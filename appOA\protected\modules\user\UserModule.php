<?php

class UserModule extends CWebModule
{
    /**
     * @var string
     * @desc hash method (md5,sha1 or algo hash function http://www.php.net/manual/en/function.hash.php)
     */
    public $hash='md5';

    public $loginUserType='staff';

    //下面两个参数通常只设置一个
    //排除下面校园的员工登录
    public $excludeCampusIds=null;
    //只允许下面校园的员工登录
    public $limitCampusIds=null;


	public function init()
	{
		// this method is called when the module is being created
		// you may place code here to customize the module or the application

		// import the module-level models and components
		$this->setImport(array(
            'user.components.*',
            'user.models.*',
            'common.models.hr.*',
        ));
	}

	public function beforeControllerAction($controller, $action)
	{
		if(parent::beforeControllerAction($controller, $action))
		{
			// this method is called before any module controller action is performed
			// you may place customized code here
			return true;
		}
		else
			return false;
	}

    /**
     * @return hash string.
     */
    public static function encrypting($string="") {
        $hash = Yii::app()->getModule('user')->hash;
        if ($hash=="md5")
            return md5($string);
        if ($hash=="sha1")
            return sha1($string);
        else
            return hash($hash,$string);
    }
}
