<style>
    [v-cloak] {
		display: none;
	}
    .colorBlue{
        color:#4D88D2
    }
    .title{
        display: flex;
        margin-top:24px;
        align-items: center;
    }
    .lineHeight{
        line-height:15px
    }
    .blueLine{
        width: 4px;
        height: 16px;
        background: #4D88D2;
    }
    .maxWidth{
        max-width:750px
    }
    .status{
        border-radius: 4px;
        border: 1px solid #E5E6EB;
        padding:16px 18px
    }
    .colorRed{
        color:#D9534F
    }
    .colorWait{
        color:#F0AD4E
    }
    .colorGreen{
        color:#5CB85C
    }
    .statusGreen:hover{
        background:#F2F9F2;
        border: 1px solid #F2F9F2;
        box-shadow: 0px 2px 8px 0px rgba(0,0,0,0.1);
    }
    .statusBlue:hover{
        background:#F0F5FB;
        border: 1px solid #F0F5FB;
        box-shadow: 0px 2px 8px 0px rgba(0,0,0,0.1);
    }
    .statusWait:hover{
        background:#FDF8F1;
        border: 1px solid #FDF8F1;
        box-shadow: 0px 2px 8px 0px rgba(0,0,0,0.1);
    }
    .listData{
        border-radius: 4px;
        border: 1px solid #DCDEE0;
        padding:20px;
        display: flex;
        align-items:center;
        cursor: pointer;
    }
    .statusImg{
        width: 45px;
        margin-left:20px;
    }
</style>
<div class="container-fluid"  id='template' v-cloak>
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('//mcampus/default/index')) ?></li>
        <li class="active">质检系统</li>
    </ol>
    <div class="row" >
        <div class='col-md-12 col-sm-12' v-if='inintList.tasks_list'>
            <div class='flex'>
                <div class='flex1'></div>
                <div>
                    <select class="form-control" v-model='current_year' @change='initData()'>
                        <option v-for='(list,index) in inintList.year_list' :value='list.key'>{{list.title}}</option>
                    </select>
                </div>
            </div>
            <div v-if='inintList.tasks_list.length>0'>
                <div v-for='(list,index) in inintList.tasks_list' class='pb20 mt24'>
                    <div class='flex mb12 align-items'>
                        <div class='title flex1'>
                            <div class='blueLine'></div>
                            <span class='font16 fontBold color3 ml8 lineHeight'>{{list.title}}</span> 
                        </div>
                        <div class='colorBlue font14 cur-p' @click='qualityTable(list)'>
                            <span class='el-icon-document'></span><span class='ml5'>质检汇总表</span>
                        </div>
                    </div>
                    <div class='maxWidth'>
                        <div class='color6 font12' v-html='html(list.desc)'></div>
                    </div>
                    <hr>
                    <div class='row'>
                        <div v-for='(item,idx) in list.items' class=' mb24 ml15 mr8' style='display:inline-block' @click='hrefLink(item)'>
                            <div class='listData statusBlue' v-if='item.status==1 || item.status==2 || item.status==4'>
                                <div>
                                    <div class='font14 color3 fontBold'>{{item.inspect_items.title}}</div>
                                    <div class='font12 color6 mt8' v-if='inintList.rectify_count[item.id].count==0'>无整改项</div>
                                    <div class='font12 color6 mt8' v-else><span class='fontBold'> {{inintList.rectify_count[item.id].count}}</span>个整改项</div>
                                    <div class='font12 colorBlue mt8'>待验收...</div>
                                </div>
                                <img src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/quality3.png' ?>" alt="" class='statusImg'>
                            </div>
                            <div class='listData statusWait' v-if='item.status==3'>
                                <div>
                                    <div class='font14 color3 fontBold'>{{item.inspect_items.title}}</div>
                                    <div class='font12 color6 mt8' v-if='inintList.rectify_count[item.id].count==0'>无整改项</div>
                                    <div class='font12 color6 mt8' v-else><span class='fontBold'> {{inintList.rectify_count[item.id].count}}</span>个整改项</div>
                                    <div class='font12 colorWait mt8'>待整改...</div>
                                </div>
                                <img src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/quality2.png' ?>" alt="" class='statusImg'>
                            </div>
                            <div class='listData statusGreen' v-if='item.status==5'>
                                <div>
                                    <div class='font14 color3 fontBold'>{{item.inspect_items.title}}</div>
                                    <div class='font12 color6 mt8' v-if='inintList.rectify_count[item.id].count==0'>无整改项</div>
                                    <div class='font12 color6 mt8' v-else><span class='fontBold'> {{inintList.rectify_count[item.id].count}}</span>个整改项</div>
                                    <div class='font12 colorGreen mt8'>已完成 {{item.updated_time}}</div>
                                </div>
                                <img src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/quality1.png' ?>" alt="" class='statusImg'>
                            </div>
                            <div class='listData statusGreen' v-if='item.status==6'>
                                <div>
                                    <div class='font14 color3 fontBold'>{{item.inspect_items.title}}</div>
                                    <div class='font12 color6 mt8' v-if='inintList.rectify_count[item.id].count==0'>无整改项</div>
                                    <div class='font12 color6 mt8' v-else><span class='fontBold'> {{inintList.rectify_count[item.id].count}}</span>个整改项</div>
                                    <div class='font12 colorWait mt8'>待确认...</div>
                                </div>
                                <img src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/quality1.png' ?>" alt="" class='statusImg'>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div v-else>
                <el-empty description="暂无数据"></el-empty>
            </div>
        </div>
    </div>
</div>
<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>
<script>
    var  container = new Vue({
        el: "#template",
        data: {
          desc_cn:'',
          desc_en:'',
          title_en:"",
          current_year:'',
          inintList:{}
        },
        created: function() {
            this.initData()
        },
        computed: {
            
        },
        methods: {
            html(data){
                if(data!='' && data!=null){
                    return  data.replace(/\n/g, '<br/>')
                }
            },
            initData(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("getQualityItemSingle") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        start_year:this.current_year
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            console.log(data)
                            that.current_year=data.data.current_year
                            that.inintList=data.data
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })  
            },
            hrefLink(item){
                window.location.href='<?php echo $this->createUrl('detail', array('branchId' => $this->branchId)); ?>'+'&id='+item.id
            },
            qualityTable(list){
                window.open('<?php echo $this->createUrl('overview', array('branchId' => $this->branchId)); ?>'+'&id='+list.id)
            }
        },
    })
</script>
