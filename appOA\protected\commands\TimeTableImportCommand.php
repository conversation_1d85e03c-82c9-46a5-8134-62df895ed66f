<?php

class TimeTableImportCommand extends CConsoleCommand
{

    public function init()
    {
        Yii::import('common.models.timetable.*');
    }

    // 课表时间安排  TimetableTimes
    public function actionTimes()
    {
        Yii::import('common.models.timetable.*');
        $file = fopen('./timetableTimes.csv', 'r+');

        $num = 1;
        while ($arr = fgetcsv($file)) {
            if (!isset($arr[1])) {
                echo "第 $num 行：" . 'code 错误' . "\r\n";
                continue;
            }

            foreach ($arr as $k => $v) {
                $arr[$k] = iconv("gbk", "utf-8", $v);
            }

            $frist = substr($arr[0], 0, 1);
            $last = substr($arr[0], -1, 1);

            $criteria = new CDbCriteria;
            $criteria->compare('weekday', $frist);
            $criteria->compare('period', $last);
            $model = TimetableTimes::model()->find($criteria);
            if (!$model) {
                $model = new TimetableTimes();
                $model->tid = 3;
                $model->status = 1;
                $model->updated_at = time();
                $model->updated_by = 1;
            }


            $model->weekday = $frist;
            $model->period = $last;
            $model->timeslot = $arr[1];
            $model->created_at = time();
            $model->created_by = 1;
            if ($model->save()) {
                echo $num . '------OK' . "\n";
                $num++;
            }
        }
        fclose($file);
        # code...
    }

    // 课程信息及介绍
    public function actionCourseInfo()
    {

        // echo "处理课程描述 \r\n";
        // $file = fopen('./coursedesc.csv', 'r+');
        // $i = 1;

        // while ($arr = fgetcsv($file)) {
        // 	if (!isset($arr[1])) {
        // 		echo "第 $i 行：" . 'program 错误'. "\r\n";
        // 		continue;
        // 	}

        // 	foreach ($arr as $k => $v) {
        // 		$arr[$k] = iconv("gbk", "utf-8", $v);
        // 	}

        // 	$codes = explode('-', $arr[1]);
        // 	foreach ($codes as $program) {
        // 		$desc = TimetableProgramDescriptions::model()->findByAttributes(array('program' => $program));
        // 		if (!$desc) {
        // 			$desc = new TimetableProgramDescriptions();
        // 		}
        // 		$desc->program = $program;
        // 		$desc->descriptions_primary = isset($arr[2]) ? $arr[2] : '';
        // 		$desc->descriptions_translated = isset($arr[3]) ? $arr[3] : '';
        // 		if (!$desc->save()) {
        // 			$errors = current($desc->getErrors());
        // 			echo "第 $i 行：" . 'TimetableProgramDescriptions 保存失败' . $errors[0]. "\r\n";
        // 			continue;
        // 		}
        // 	}

        // 	echo "第 $i 行处理结束". "\r\n";
        // 	$i++;
        // }
        // fclose($file);

        echo "处理课程信息 \r\n";
        $file = fopen('./course.csv', 'r+');
        $i = 1;
        $time = time();
        while ($arr = fgetcsv($file)) {
            $courseCode = intval($arr[0]);
            if (strlen($courseCode) < 9) {
                $courseCode = '0' . $courseCode;
            }
            //$code = iconv("gbk", "utf-8", sprintf("%09d", $courseCode));
            $code = iconv("gbk", "utf-8", str_pad($courseCode, 9, "0", STR_PAD_RIGHT));
            $title = iconv("gbk", "utf-8", $arr[1]);
            $programs = '0' . $arr[0];
            $program = substr($programs, 0, 6);

            $course = TimetableCourses::model()->findByAttributes(array('program' => $program, 'course_code' => $code));
            if (!$course) {
                $course = new TimetableCourses();
            }
            $course->program = $program;
            $course->course_code = $code;
            $course->title_cn = trim($title);
            $course->title_en = trim($title);
            $course->status = 1;
            $course->weight = 0;
            $course->created_at = $time;
            $course->updated_at = $time;
            $course->created_by = 1;
            $course->updated_by = 1;

            if (!$course->save()) {
                $errors = current($course->getErrors());
                echo "第 $i 行：" . 'TimetableCourses 保存失败' . $errors[0] . "\r\n";
                continue;
            }

            echo "第 $i 行处理结束" . "\r\n";
            $i++;
        }
        fclose($file);

    }

    // 课程具体安排，班级，时间 TimetableCourseData
    public function actionCourseData()
    {
        Yii::import('common.models.timetable.*');

        $csvPath = fopen('./timetableCourse.csv', 'r+');
        $i = 1;

        $num = 1;
        while ($arr = fgetcsv($csvPath)) {
            if (!isset($arr[1])) {
                echo "第 $i 行：" . 'code 错误' . "\r\n";
                continue;
            }

            foreach ($arr as $k => $v) {
                $arr[$k] = iconv("gbk", "utf-8", $v);
            }

            if ($arr) {
                $itemDataTwo = explode(',', $arr[1]);
                $frist = substr($arr[0], 0, 1);
                $last = substr($arr[0], -1, 1);

                foreach ($itemDataTwo as $val) {
                    $course_code = sprintf("%08d", intval($val));
                    $criteria = new CDbCriteria;
                    $criteria->compare('course_code', $course_code);
                    $courseModel = TimetableCourses::model()->find($criteria);

                    if ($courseModel) {
                        $criteria = new CDbCriteria;
                        $criteria->compare('course_code', $course_code);
                        $criteria->compare('weekday', intval($frist));
                        $criteria->compare('period', intval($last));
                        $criteria->compare('tid', 3);
                        $model = TimetableCourseData::model()->find($criteria); // 2250

                        if (!$model) {
                            $model = new TimetableCourseData();
                            $model->tid = 3;
                            $model->class_room = 0;
                            $model->status = 1;
                            $model->updated_at = time();
                            $model->updated_by = 1;
                        }

                        $model->course_id = $courseModel->id;
                        $model->course_code = $course_code;
                        $model->weekday = intval($frist);
                        $model->period = intval($last);
                        $model->created_at = time();
                        $model->created_by = 1;

                        if ($model->save()) {
                            echo $num . '------OK' . "\n";
                            $num++;
                        }
                    }
                }
            }
        }
        fclose($csvPath);
        # code...
    }

    // 课程对应教师安排 TimetableCourseTeacher
    public function actionCourseTeacher()
    {
        Yii::import('common.models.timetable.*');
        Yii::import('common.models.invoice.*');
        $csvPath = fopen('./courseTeacher.csv', 'r+');
        $i = 1;
        $num = 1;
        $a = array();
        while ($arr = fgetcsv($csvPath)) {
            if (!isset($arr[0])) {
                echo "第 $i 行：" . 'code 错误' . "\r\n";
                continue;
            }
            foreach ($arr as $k => $v) {
                $arr[$k] = iconv("gbk", "utf-8", $v);
            }
            //$course_code = sprintf("%09d", intval($arr[0]));
            $courseCode = intval($arr[0]);
            if (strlen($courseCode) < 9) {
                $courseCode = '0' . $courseCode;
            }
            $course_code = iconv("gbk", "utf-8", str_pad($courseCode, 9, "0", STR_PAD_RIGHT));

            /*$criteria = new CDbCriteria;
            $criteria->compare('course_code', $course_code);
            $criteria->compare('tid', 3);
            $courseData = TimetableCourseData::model()->findAll($criteria);*/
            $criteria = new CDbCriteria;
            $criteria->compare('course_code', $course_code);
            $course = TimetableCourses::model()->find($criteria);
            /*if(isset($arr[2])){
                if($courseData){
                    foreach ($courseData as $item) {
                        $item->class_room = 0;
                        $item->class_room_name = $arr[2];
                        $item->save();
                    }
                }
            }*/

            if ($arr[4]) {
                $childArr = explode(',', $arr[4]);
                foreach ($childArr as $child) {
                    $child = trim($child);
                    $childid = intval($child);
                    if (!$childid) {
                        continue;
                    }
                    $criteria = new CDbCriteria;
                    $criteria->compare('childid', $childid);
                    $childModel = ChildProfileBasic::model()->find($criteria);
                    /*$criteria = new CDbCriteria;
                    $criteria->compare('childid', $childid);
                    $criteria->compare('stat', 20);
                    $reserveModel = ChildReserve::model()->find($criteria);*/
                    if ($childModel && $course) {
                        $criteria = new CDbCriteria;
                        $criteria->compare('child_id', $childModel->childid);
                        $criteria->compare('course_id', $arr[5]);
                        $criteria->compare('tid', 3);
                        $studentData = TimetableStudentData::model()->find($criteria);
                        if (!$studentData) {
                            $studentData = new TimetableStudentData();
                            $studentData->tid = 3;
                            $studentData->term = $arr[1];
                            $studentData->child_id = $childModel->childid;
                            $studentData->class_room = 0;
                            $studentData->class_room_name = $arr[2];
                            $studentData->start_at = time();
                            $studentData->status = 1;
                            $studentData->created_at = time();
                            $studentData->created_by = 1;
                        }
                        $studentData->class_id = $childModel->classid;
                        $studentData->course_id = $course->id;
                        $studentData->course_code = $course_code;
                        $studentData->updated_at = time();
                        $studentData->updated_by = 1;
                        $studentData->save();
                    }
                }
            }
//            if($arr[3]){
//                if($course){
//                    $criteria = new CDbCriteria;
//                    $criteria->compare('course_id', $course->id);
//                    $criteria->compare('tid', 3);
//                    $teacher = TimetableCourseTeacher::model()->find($criteria);
//                    if(!$teacher){
//                        $teacher = new TimetableCourseTeacher();
//                    }
//
//                    $teacher->tid = 3;
//                    $teacher->schoolid = 'BJ_DS';
//                    $teacher->term = $arr[1];
//                    $teacher->course_id = $course->id;
//                    $teacher->teacher_id = $arr[3];
//                    $teacher->start_at = time();
//                    $teacher->type = 1;
//                    $teacher->save();
//                }
//            }
            echo $num . '------OK' .  "\n";
            $num++;
        }

        fclose($csvPath);
        # code...
    }

    // 学生对应上课数据
    public function actionStudentData()
    {
        # code...
    }

    // 增加孩子的缓存
    public function actionWeeklySchduleCacheStudent()
    {
        $model = TimetableStudentData::model()->findAll();
        $arr = array();
        foreach ($model as $val) {
            $arr[$val->child_id][] = $val->course_id;
        }

        if ($arr) {
            foreach ($arr as $child_id => $val) {
                $criteria = new CDbCriteria;
                $criteria->compare('course_id', $val);
                $courseModel = TimetableCourseData::model()->findAll($criteria);
                $courseData1 = array();
                foreach ($courseModel as $item) {
                    $courseData1[$item->weekday . '-' . $item->period][] = $item->course_code;
                }
                $courseData2 = array();

                if ($courseData1) {
                    foreach ($courseData1 as $k => $value) {
                        $courseData2[$k] = implode(',', $value);
                    }
                }

                if ($courseData2) {
                    $cackeModel = new TimetableWeeklyScheduleCache();
                    $cackeModel->tid = 1;
                    $cackeModel->type = 10;
                    $cackeModel->target_id = $child_id;
                    $cackeModel->data = json_encode($courseData2);
                    $cackeModel->updated_at = time();
                    $cackeModel->save();
                }

            }
        }
    }

    public function actionWeeklySchduleCacheTeacher()
    {
        $model = TimetableCourseTeacher::model()->findAll();
        $arr = array();
        foreach ($model as $val) {
            $arr[$val->teacher_id][] = $val->course_id;
        }

        if ($arr) {
            $num = 1;
            foreach ($arr as $teacher_id => $val) {
                $criteria = new CDbCriteria;
                $criteria->compare('course_id', $val);
                $courseModel = TimetableCourseData::model()->findAll($criteria);
                $courseData1 = array();
                foreach ($courseModel as $item) {
                    $courseData1[$item->weekday . '-' . $item->period][] = $item->course_code;
                }

                $courseData2 = array();
                if ($courseData1) {
                    foreach ($courseData1 as $k => $value) {
                        $courseData2[$k] = implode(',', $value);
                    }
                }

                if ($courseData2) {
                    $cackeModel = new TimetableWeeklyScheduleCache();
                    $cackeModel->tid = 1;
                    $cackeModel->type = 20;
                    $cackeModel->target_id = $teacher_id;
                    $cackeModel->data = json_encode($courseData2);
                    $cackeModel->updated_at = time();
                    $cackeModel->save();
                    echo $num;
                    $num++;
                }
            }
        }
    }

    public function actionSaveRecords($target_date = '')
    {
        if ($target_date) {
            $target_date = strtotime($target_date);
            $time = strtotime(date('Y-m-d', time()));

            for ($target_date; $target_date <= $time;) {
                $criteria = new CDbCriteria;
                $criteria->compare('target_date', $target_date);
                $criteria->compare('status', 10);
                $recordsModel = TimetableRecords::model()->findAll($criteria);

                if ($recordsModel) {
                    $courseCode = array();
                    foreach ($recordsModel as $course) {
                        $courseCode[] = $course->course_code;
                    }

                    $criteria = new CDbCriteria;
                    $criteria->compare('course_code', $courseCode);
                    $criteria->compare('status', 1);
                    $criteria->index = 'id';
                    $courseModel = TimetableCourses::model()->findAll($criteria);

                    $courseArr = array();
                    if ($courseModel) {
                        $criteria = new CDbCriteria;
                        $criteria->compare('course_id', array_keys($courseModel));
                        $criteria->compare('status', 1);
                        $courseTeacherModel = TimetableCourseTeacher::model()->findAll($criteria);
                        $teacherArr = array();
                        foreach ($courseTeacherModel as $val) {
                            $teacherArr[$val->course_id] = $val->teacher_id;
                        }

                        foreach ($courseModel as $item) {
                            if (isset($teacherArr[$item->id])) {
                                $courseArr[$item->course_code] = $teacherArr[$item->id];
                            }
                        }
                    }
                    $i = 1;
                    foreach ($recordsModel as $data) {
                        if (isset($courseArr[$data->course_code]) && $data->teacher_id != $courseArr[$data->course_code]) {
                            $data->teacher_id = $courseArr[$data->course_code];
                            $data->save();
                            echo 'OK -- ' . $i . "\n";
                            $i++;
                        }
                    }
                }
                $target_date = $target_date + 86400;
            }
        }
    }

    public function actionEmail($targetDates = '', $schoolid = 'BJ_DS')
    {
        $targetDates = ($targetDates) ? $targetDates : date('Y-m-d');
        $targetDate = strtotime($targetDates);
        $week = date('w', $targetDate);

        Yii::import('common.models.calendar.*');

        $criteria = new CDbCriteria();
        $criteria->compare('is_selected', 1);
        $criteria->compare('branchid', $schoolid);
        $calendarModel = CalendarSchool::model()->find($criteria);

        $tameTableModel = array();
        if ($calendarModel) {
            $criteria = new CDbCriteria();
            $criteria->compare('yid', $calendarModel->yid);
            $criteria->compare('month', date("Ym", $targetDate));
            $schoolDay = CalendarSchoolDays::model()->find($criteria);

            if ($schoolDay && in_array(date("d", $targetDate), explode(',', $schoolDay->schoolday_array))) {
                $criteria = new CDbCriteria();
                $criteria->compare('yid', $calendarModel->yid);
                $criteria->compare('schoolid', $schoolid);
                $tameTableModel = Timetable::model()->find($criteria);

                if ($tameTableModel) {
                    $criteria = new CDbCriteria();
                    $criteria->compare('type', 20);
                    $criteria->compare('tid', $tameTableModel->id);
                    $schedules = TimetableWeeklyScheduleCache::model()->findAll($criteria);

                    $reports = array();
                    $data = array();
                    $data['targetDate'] = $targetDate;
                    for ($i = 1; $i <= 8; $i++) {
                        $data['total'][$i] = 0;
                        $data['taken'][$i] = 0;
                    }

                    foreach ($schedules as $schedule) {
                        $item = json_decode($schedule->data);
                        for ($i = 1; $i <= 8; $i++) {
                            $reports[$schedule->target_id]['total'][$i] = 0;
                            $reports[$schedule->target_id]['taken'][$i] = 0;
                        }

                        foreach ($item as $weekdata => $coursecode) {
                            $weekdata = explode('-', $weekdata);
                            if ($week != $weekdata[0]) {
                                continue;
                            }
                            $period = $weekdata[1];
                            if ($reports[$schedule->target_id]['total'][$period] == 0) {
                                $data['total'][$period]++;
                            }
                            $reports[$schedule->target_id]['total'][$period]++;
                        }
                    }
                    $teacherIds = array_keys($reports);

                    // 教师信息
                    $criteria = new CDbCriteria();
                    $criteria->compare('t.uid', $teacherIds);
                    $criteria->index = 'uid';
                    $criteria->with = 'profile';
                    $teacherInfo = User::model()->findAll($criteria);
                    $data['teacherInfo'] = $teacherInfo;

                    // 签到记录
                    $criteria = new CDbCriteria();
                    $criteria->compare('teacher_id', $teacherIds);
                    $criteria->compare('target_date', $targetDate);
                    $criteria->compare('tid', $tameTableModel->id);
                    $criteria->compare('is_admin', 0);
                    $criteria->compare('status', TimetableRecords::ATTENDANCE_STATUS);
                    $records = TimetableRecords::model()->findAll($criteria);
                    foreach ($records as $record) {
                        if ($reports[$record->teacher_id]['taken'][$record->period] == 0) {
                            $data['taken'][$record->period]++;
                        }
                        $reports[$record->teacher_id]['taken'][$record->period]++;
                    }
                    $data['reports'] = $reports;
                    $num = array();
                    foreach ($data['reports'] as $userId => $val) {
                        foreach ($val['total'] as $key => $item) {
                            if ($item > 0) {
                                if ($val['taken'][$key] < 1) {
                                    $num[$userId][] = 'Period ' . $key;
                                }
                            }
                        }
                    }
                    if ($num) {
                        foreach ($num as $userId => $val) {
                            $mailer = Yii::createComponent('common.extensions.mailer.EMailer');
                            //$mailer->AddAddress('<EMAIL>');
                            $mailer->AddAddress($data['teacherInfo'][$userId]['email']);
                            $mailer->AddBCC('<EMAIL>');
                            $mailer->Subject = '[DS Online] Attendance Notification ' . $targetDates;
                            $mailer->getCommandView('timetable', array('teacherName' => $data['teacherInfo'][$userId], 'teacherid' => $userId, 'tid' => $tameTableModel->id, 'data' => $val, 'times' => $targetDates), 'todsparent');
                            $isProduction = defined("IS_PRODUCTION") ? IS_PRODUCTION : false;
                            $mailer->iniMail($isProduction);
                            if ($mailer->Send()) {
                                echo $userId . ' - Success' . "\n";
                            } else {
                                echo $userId . ' - Fail' . "\n";
                            }
                        }
                    }
                }
            }
        }
    }

    public function actionUpdateClass($tid = 1)
    {
        $criteria = new CDbCriteria();
        $criteria->compare('tid', $tid);
        $criteria->compare('class_id', 1);
        $model = TimetableStudentData::model()->findAll($criteria);
        $i = 1;
        if ($model) {
            foreach ($model as $val) {
                $childModel = ChildProfileBasic::model()->findByPk($val->child_id);
                if ($childModel) {
                    $val->class_id = $childModel->classid;
                    $val->save();
                    echo $i;
                    echo '\n';
                    $i++;
                }
            }
        }
    }

    public function actionCourse2021($csv='')
    {
        if(!$csv || !file_exists($csv)){
            die('csv path error');
        }
        echo "处理课程信息 \r\n";
        $file = fopen($csv, 'r+');
        $i = 1;
        $time = time();
        while ($arr = fgetcsv($file)) {
            $start_year = trim($arr[0]);
            $program = trim($arr[1]);
            $code = trim($arr[2]);
            $title_en = trim($arr[3]);
            $title_cn = trim($arr[4]);

            $course = TimetableCourses::model()->findByAttributes(
                array('program' => $program, 'course_code' => $code, 'start_year' => $start_year)
            );
            if (!$course) {
                $course = new TimetableCourses();
            }
            $course->start_year = $start_year;
            $course->program = $program;
            $course->course_code = $code;
            $course->title_cn = $title_cn;
            $course->title_en = $title_en;
            $course->status = 1;
            $course->weight = 0;
            $course->created_at = $time;
            $course->updated_at = $time;
            $course->created_by = 1;
            $course->updated_by = 1;

            if (!$course->save()) {
                $errors = current($course->getErrors());
                echo "第 $i 行：" . 'TimetableCourses 保存失败' . $errors[0] . "\r\n";
                continue;
            }

            echo "第 $i 行处理结束" . "\r\n";
            $i++;
        }
        fclose($file);
    }

    public function actionCourseTeacherStudent($csv='', $tid='')
    {
        if(!$csv || !file_exists($csv)){
            die('csv path error');
        }
        if(!$tid){
            die('csv path error');
        }

        echo "处理老师学生信息 \n";
        $file = fopen($csv, 'r+');
        $i = 1;
        $time = time();
        while ($arr = fgetcsv($file)) {
            $code = trim($arr[2]);
            $term = trim($arr[5]);
            $room = trim($arr[6]);
            $teacher_id = trim($arr[7]);
            $children = trim($arr[8]);

            $criteria = new CDbCriteria();
            $criteria->compare('course_code', $code);
            $course = TimetableCourses::model()->find($criteria);

            if ($course) {
                foreach (explode(',', $children) as $child_id) {
                    $child_id = intval(trim($child_id));
                    if (!$child_id) {
                        continue;
                    }

                    $criteria = new CDbCriteria;
                    $criteria->compare('childid', $child_id);
                    $childModel = ChildProfileBasic::model()->find($criteria);
                    if ($childModel) {
                        $criteria = new CDbCriteria;
                        $criteria->compare('child_id', $childModel->childid);
                        $criteria->compare('course_id', $course->id);
                        $criteria->compare('tid', $tid);
                        $studentData = TimetableStudentData::model()->find($criteria);
                        if (!$studentData) {
                            $studentData = new TimetableStudentData();
                        }
                        $studentData->tid = $tid;
                        $studentData->term = $term;
                        $studentData->child_id = $childModel->childid;
                        $studentData->class_room = 0;
                        $studentData->class_room_name = $room;
                        $studentData->start_at = $time;
                        $studentData->status = 1;
                        $studentData->created_at = $time;
                        $studentData->created_by = 1;
                        $studentData->class_id = $childModel->classid;
                        $studentData->course_id = $course->id;
                        $studentData->course_code = $code;
                        $studentData->updated_at = $time;
                        $studentData->updated_by = 1;
                        if (!$studentData->save()) {
                            $errors = current($course->getErrors());
                            echo "第 $i 行：" . $childModel->childid . 'ivy_timetable_student_data 保存失败' . $errors[0] . "\n";
                            continue;
                        }
                        else {
                            echo $childModel->childid."\n";
                        }
                    }
                }

                $criteria = new CDbCriteria;
                $criteria->compare('course_id', $course->id);
                $criteria->compare('tid', $tid);
                $teacher = TimetableCourseTeacher::model()->find($criteria);
                if(!$teacher){
                    $teacher = new TimetableCourseTeacher();
                }

                $teacher->tid = $tid;
                $teacher->schoolid = 'BJ_DS';
                $teacher->term = $term;
                $teacher->course_id = $course->id;
                $teacher->course_code = $code;
                $teacher->teacher_id = $teacher_id;
                $teacher->start_at = $time;
                $teacher->type = 1;
                if (!$teacher->save()) {
                    $errors = current($course->getErrors());
                    echo $code . ": " . $errors[0];
                    continue;
                }

                echo "第 $i 行处理结束" . "\n";
                $i++;
            }
            else {
                echo $i."Error \n";
                continue;
            }
        }
        fclose($file);
    }

    public function actionExportData($yid=''){
        if (!$yid) {
            die('yid not empty');
        }
        Yii::import('common.models.secondary.*');
        Yii::import('common.models.timetable.*');
        error_reporting(E_ALL);
        $crit = new CDbCriteria();
        $crit->compare('calendar', $yid);
        $crit->compare('schoolid', "BJ_DS");
        $crit->order = "cycle";
        $crit->index = "id";
        $report = AchievementReport::model()->findAll($crit);

        $timeTable = Timetable::model()->findByAttributes(array('yid' => $yid, 'schoolid' => "BJ_DS"));
        if (!$timeTable) {
            return false;
        }

        $criteria = new CDbCriteria();
        $criteria->compare('yid', $yid);
        $criteria->compare('classtype', array("e6", "e7",'e8','e9', 'e10', 'e11', 'e12'));
        //$criteria->compare('classtype', array("e7"));
//        $criteria->compare('stat', 10);
        $criteria->order='child_age ASC, title ASC';
        $classModel = IvyClass::model()->findAll($criteria);
        //  所有中学班级
        $classIds = array();
        foreach ($classModel as $val) {
            $classIds[$val->classid] = $val->title;
        }
        $csv = $this->_t('编号') . ",";
        $csv .= $this->_t('中文姓名') . ",";
        $csv .= $this->_t('英文名') . ",";
        $csv .= $this->_t('英文中间名') . ",";
        $csv .= $this->_t('英文姓') . ",";
        $csv .= $this->_t('昵称') . ",";
        $csv .= $this->_t('班级') . ",";
        $csv .= $this->_t('课程编码') . ",";
        $csv .= $this->_t('Subject Name cn') . ",";
        $csv .= $this->_t('Subject Name en') . ",";
        $csv .= $this->_t('Subject Teacher') . ",";
        $csv .= $this->_t('Reporting Period (Q1/Q2 etc)') . ",";
        $csv .= $this->_t('Criteria') . ",";
        $csv .= $this->_t('Score') . ",";
        $csv .= $this->_t('Final Criteria Score') . ",";
        $csv .= $this->_t('Final Grade') . ",";
        $csv .= "\r\n";

        foreach ($classIds as $classid => $classTitle){
            echo $classid."\n";
            $childListInfo = array();

            $criteria = new CDbCriteria();
            $criteria->compare('class_id', $classid);
            $criteria->compare('tid', $timeTable->id);
            $criteria->index = "child_id";
            $_data = TimetableStudentData::model()->findAll($criteria);
            if (!$_data) {
                continue;
            }

            $criteria = new CDbCriteria();
//            $criteria->compare('status', array(10,20));
//            $criteria->compare('classid', $classid);
            $criteria->compare('childid', array_keys($_data));
            $criteria->index = "childid";
            $childInfo = ChildProfileBasic::model()->findAll($criteria);
            if($childInfo) {
                // 孩子分配得课程信息
                $criteria = new CDbCriteria();
                $criteria->compare('class_id', $classid);
                $criteria->compare('tid', $timeTable->id);
                $criteria->compare('child_id', array_keys($childInfo));
                $studentDataInfo = TimetableStudentData::model()->findAll($criteria);
                if($studentDataInfo) {
                    foreach ($studentDataInfo as $data) {
                        $newCourseIds[$data->course_id] = $data->course_id;
                        $arrayOption = array();
                        if ($data->tableCourse->reportCourse) {
                            $criteria = new CDbCriteria();
                            $criteria->compare('courseid', $data->tableCourse->report_course_id);
                            $criteria->order = "title_cn";
                            $reportCourseStandard = AchievementReportCourseStandard::model()->findAll($criteria);

                            foreach ($reportCourseStandard as $value) {
                                $arrayOption[$value->id] = $value->title_cn;
                            }
                        } else {
                            $arrayOption[100000] = "A";
                            $arrayOption[100001] = "B";
                            $arrayOption[100002] = "C";
                            $arrayOption[100003] = "D";
                        }

                        $criteria = new CDbCriteria();
                        $criteria->compare('tid', $timeTable->id);
                        $criteria->compare('course_id', $data->course_id);
                        $criteria->compare('status', 1);
                        $teacherInfo = TimetableCourseTeacher::model()->find($criteria);

                        if ($data->tableCourse->reportCourse) {
                            $childListInfo[$data->child_id]["childid"] = $data->child_id;
                            $childListInfo[$data->child_id]["name"] = $childInfo[$data->child_id]->name_cn;
                            $childListInfo[$data->child_id]["first_name_en"] = $childInfo[$data->child_id]->first_name_en;
                            $childListInfo[$data->child_id]["middle_name_en"] = $childInfo[$data->child_id]->middle_name_en;
                            $childListInfo[$data->child_id]["last_name_en"] = $childInfo[$data->child_id]->last_name_en;
                            $childListInfo[$data->child_id]["nick"] = $childInfo[$data->child_id]->nick;
                            $childListInfo[$data->child_id]["course_id"][$data->course_id] = array(
                                "id" => $data->tableCourse->reportCourse->id,
                                "title_cn" => $data->tableCourse->reportCourse->title_cn,
                                "title_en" => $data->tableCourse->reportCourse->title_en,
                                "teacher" => $teacherInfo->user->getName(),
                                "option" => $arrayOption,
                                "course_code" => $data->course_code,
                            );
                        }
                        echo 'A';
                    }

                    // 填写完成得数据
                    $criteria = new CDbCriteria();
                    $criteria->compare('report_id', array_keys($report));
                    $criteria->compare('childid', array_keys($childInfo));
                    $childFractionInfo = AchievementReportChildFraction::model()->findAll($criteria);
                    $fractionInfo = array();
                    foreach ($childFractionInfo as $fractionData) {
                        if (isset($fractionData->courseScoresid) && $fractionData->courseScoresid) {
                            $fractionInfo[$fractionData->report_id][$fractionData->childid][$fractionData->timetable_records_id][$fractionData->optionid] = $fractionData->courseScoresid->fraction;
                            echo 'B';
                        }
                    }

                    // z总分
                    $criteria = new CDbCriteria();
                    $criteria->compare('calender_id', $yid);
                    $criteria->compare('childid', array_keys($childInfo));
                    $childFractionTotalInfo = AchievementReportChildTotal::model()->findAll($criteria);
                    $totalInfo = array();
                    $FinalCriteriaScoreList = array();
                    $achievementCourseScoresModel = array();
                    $frecyionTotalIds = array();
                    foreach ($childFractionTotalInfo as $total) {
                        $totalInfo[$total->childid][$total->timetable_records_id] = $total->oprion_value;
                        if($total->frecyion_total){
                            foreach (json_decode($total->frecyion_total) as $frecyionTotalkey => $frecyionTotal){
                                $FinalCriteriaScoreList[$total->childid][$total->timetable_records_id][$frecyionTotalkey] = $frecyionTotal;
                                $frecyionTotalIds[] = $frecyionTotal;
                                echo 'C';
                            }
                        }
                    }

                    if($frecyionTotalIds){
                        $criteria = new CDbCriteria();
                        $criteria->compare('id', $frecyionTotalIds);
                        $criteria->index = "id";
                        $achievementCourseScoresModel = AchievementCourseScores::model()->findAll($criteria);
                    }

                    foreach ($childListInfo as $childid => $item) {
                        foreach ($item["course_id"] as $courseId => $courseInfo) {
                            foreach ($report as $reportInfo) {
                                foreach ($courseInfo["option"] as $optionId => $optionValue) {
                                    $option = "-";
                                    if(isset($fractionInfo[$reportInfo->id]) && isset($fractionInfo[$reportInfo->id][$childid]) && isset($fractionInfo[$reportInfo->id][$childid][$courseId]) &&
                                    isset($fractionInfo[$reportInfo->id][$childid][$courseId][$optionId])){
                                        $option = $fractionInfo[$reportInfo->id][$childid][$courseId][$optionId];
                                    }
                                    $finalGrade = "-";
                                    $CriteriaScoreInfo = "";
                                    if ($reportInfo->cycle == 4) {
                                        if(isset($totalInfo[$childid]) && isset($totalInfo[$childid][$courseId])){
                                            $finalGrade = $totalInfo[$childid][$courseId];
                                        }
                                        if(isset($FinalCriteriaScoreList[$childid]) && isset($FinalCriteriaScoreList[$childid][$courseId]) && isset($FinalCriteriaScoreList[$childid][$courseId][$optionId])){
                                            $CriteriaScoreInfo = $achievementCourseScoresModel[$FinalCriteriaScoreList[$childid][$courseId][$optionId]]->fraction;
                                        }
                                    }
                                    $csv .= $this->_t($childid) . ",";
                                    $csv .= $this->_t($item["name"]) . ",";
                                    $csv .= $this->_t($item["first_name_en"]) . ",";
                                    $csv .= $this->_t($item["middle_name_en"]) . ",";
                                    $csv .= $this->_t($item["last_name_en"]) . ",";
                                    $csv .= $this->_t($item["nick"]) . ",";
                                    $csv .= $this->_t($classTitle) . ",";
                                    $csv .= $this->_t($courseInfo["course_code"]) . ",";
                                    $csv .= $this->_t($courseInfo["title_cn"]) . ",";
                                    $csv .= $this->_t($courseInfo["title_en"]) . ",";
                                    $csv .= $this->_t($courseInfo["teacher"]) . ",";
                                    $csv .= $this->_t("Q" . $reportInfo->cycle) . ",";
                                    $csv .= $this->_t($optionValue) . ",";
                                    $csv .= $this->_t($option) . ",";
                                    $csv .= $this->_t($CriteriaScoreInfo) . ",";
                                    $csv .= $this->_t($finalGrade) . ",";
                                    $csv .= "\r\n";
                                    echo 'D';
                                }
                            }
                        }
                    }
                }
            }
        }
        file_put_contents($this->_t("SS_Report_Card") . time() . '.csv', $csv);
        echo "success";
    }

    public function _t($str)
    {
        $str = str_replace(',', '，', $str);
        return iconv('UTF-8', 'GBK//IGNORE', $str);
    }
}