<script>
	var mediaList; 	//从服务器返回的媒体数据
	var nameList; 	//从服务器返回的孩子信息
	var checkBoxList;
	var childIds=new Array;
	var assignedMedias;
	var assignedChildren;
	var showQuickLink = 1; //是否可开启分配弹出窗口
	var pendingRefreshStats = true;
	var lastStated = 0;
	var currentClassId = <?php echo $this->currentClassId;?>;
	var currentWeeknum = <?php echo $this->currentWeeknum;?>;
	var currentStartYear = <?php echo $this->currentStartYear;?>;
	var currentCalendarId = <?php echo $this->currentCalendarId;?>;
	var url_MediaModify = "<?php echo $this->createUrl('/IvyMediaResponder/modify');?>";
	var url_SaveTagging = "<?php echo $this->createUrl('//ivyMediaResponder/weeklyAssign');?>";
	var url_RefreshStats = "<?php echo $this->createUrl('//ivyMediaResponder/weeklyAssignStats');?>";
	var refreshStatsSecurityCode = "<?php echo md5(sprintf('%s&%s&%s&%s&%s',$this->currentStartYear, $this->currentWeeknum, $this->currentClassId, Yii::app()->user->getId(),$this->securityKey));?>";

	var canSwitch = 0;
	
</script>
<div class="h_a"><?php echo Yii::t('teaching', 'Media Management');?></div>
<div class="prompt_text">
	<ul>
		<li><?php echo Yii::t('teaching', 'Instructions');?></li>
	</ul>
</div>
	
<div class="table_full">
	<table width="100%">
		<colgroup>
			<col width=160></col>
		</colgroup>
		<tbody>
			<tr>
				<th>
					<div id="list-box">
						<?php echo CHtml::link(
							Yii::t("teaching","Refresh Statistics"),
							'javascript:void(0);',
							array(
								'id' => "refresh-stat",
								'onclick' => 'refreshStat()',
								'style' => 'display:none;',
								'class' => "btn mb10",
							)
						);?>
						
						<ul id="child-list" class="child-list"> <!--place holder-->
						</ul>
					</div>
				</th>
				<td>
					<div id="media-zone"> <!--place holder-->
						<?php if(count($mediaList)):?>
							<?php $this->renderPartial("_media_zone", array("pages"=>$pages, 'mediaList'=>$mediaList, 'nameList'=>$nameList));?>
						<?php else:?>
							<div class="not_content_mini">
							<i></i><?php echo Yii::t('teaching','No media found, click :here to upload.',
								array(':here'=>CHtml::link(Yii::t('teaching', 'here'), 'javascript:void(0);', array('onclick'=>"js: self.location=$('#lnk-mediaupload').attr('href')")) )
							);?>
							</div>
						<?php endif;?>
					</div>
				</td>
			</tr>
		</tbody>
	</table>
</div>

<!--=======================媒体分配弹窗===========================-->
<div class="core_pop_wrap" id="J_quickMediaAssign_pop" style="display:none;">
	<div class="core_pop">
		<div style="width:800px;">
			<?php echo CHtml::beginForm($this->createUrl('//ivyMediaResponder/weeklyMediaDetail'),'POST',array('class'=>'J_ajaxForm','id'=>'tag-assign-form'));?>
			<div class="pop_top">
				<a href="" id="J_quickMediaAssign_pop_x" class="pop_close"><?php echo Yii::t('global','Close');?></a>
				<strong id='d_tit'><?php echo Yii::t('teaching', '快速分配周报告图片'); ?></strong>
			</div>
			<div class="pop_cont">
				<div class="pop_table" style="height:auto;">
					
					<table width="100%">
						<colgroup>
							<col width="180">
							<col width="20">
						</colgroup>
						<tr>
							<td valign="top">
								<img id="assign-medis-preview" height=120 />
								<input type="hidden" name="mediaid" id="MediaAssignForm_mediaId" value="0" />
								
								<h2 class="tag-title"><?php echo Yii::t("teaching", "Learning Domain Tag"); ?></h2>
								<div class="tag-wrapper">
									<?php echo CHtml::checkBoxList('TagLD', null, $learningDomains, array('class'=>'LDList'));?>								
								</div>
							</td>
							<td></td>
							<td valign="top">
								<h2 class="tag-title"><?php echo Yii::t("teaching", "Free Word Tag"); ?></h2>
								<div class="tag-wrapper">
									<?php echo CHtml::textField('TagFree', '', array('class'=>'mb20 input length_5','placeholder'=>'Free Tag'));?>
								</div>
							
								
								<h2 class="tag-title"><?php echo Yii::t("teaching", "Child Tag & Quick Assign"); ?></h2>
								<div class="tag-wrapper" style="padding:0 10px;">
									<table width="100%">
										<colgroup>
											<col width="240">
											<col width="80">
											<col width="60">
										</colgroup>
										<tr>
											<td><?php echo Yii::t('teaching','Child Name');?></td>
											<td><?php echo Yii::t('teaching','Child Tag');?></td>
											<td></td>
											<td><?php echo Yii::t('teaching','Assign to Weekly Media');?></td>
										</tr>
									</table>
								</div>
								<div class="height300 tag-wrapper J_check_wrap" style="padding-top: 0">
									<table width="100%">
										<colgroup>
											<col width="240">
											<col width="80">
											<col width="60">
										</colgroup>
										<tr>
											<td></td>
											<td><label><?php echo CHtml::checkBox('TagChild_all', false, array('class'=>'J_check_all','data-checklist'=>"J_check_c1",'data-direction'=>"y"));?><?php echo Yii::t('global','All');?></label></td>
											<td></td>
											<td><label><?php echo CHtml::checkBox('LinkChild_all', false, array('class'=>'J_check_all','data-checklist'=>"J_check_c2",'data-direction'=>"y"));?><?php echo Yii::t('global','All');?></label></td>
										</tr>
										<?php foreach($nameList as $child):?>
											<tr>
												<td><?php echo $child['name'];?></td>
												<td><?php echo CHtml::checkBox('TagChild[]', false, array('id'=>'TagChild_'.$child['id'],'value'=>$child['id'],'class'=>'TagChild J_check','data-yid'=>'J_check_c1'));?></td>
												<td><?php echo CHtml::link('=','javascript:void(0);',array('childid'=>$child['id'],'class'=>'syncCheck'));?></td>
												<td><?php echo CHtml::checkBox('LinkChild[]', false, array('id'=>'LinkChild_'.$child['id'],'value'=>$child['id'],'class'=>'LinkChild J_check','data-yid'=>'J_check_c2'));?></td>
											</tr>
										<?php endforeach;?>
									</table>
								</div>
							</td>							
						</tr>
					</table>
				</div>
			</div>
			<div class="pop_bottom tac mediaByChild">
                <button id="J_quickMediaAssign_pop_close" class="btn fr"><?php echo Yii::t('global','Cancel');?></button>
				<button type="button" onclick="saveTagAssigns();"  class="btn btn_submit mr10 fr" id="J_quickMediaAssign_pop_sub"><?php echo Yii::t('global','OK');?></button>
			</div>
			<?php echo CHtml::hiddenField('classid', $this->currentClassId);?>
			<?php echo CHtml::hiddenField('weeknum', $this->currentWeeknum);?>
			<?php echo CHtml::hiddenField('startyear', $this->currentStartYear);?>
			<?php echo CHtml::hiddenField('yid', $this->currentCalendarId);?>
			<?php echo CHtml::hiddenField('securityCode', md5(sprintf('%s&%s&%s&%s&%s', $this->currentClassId,$this->currentWeeknum,$this->currentStartYear,Yii::app()->user->getId(),$this->securityKey)));?>
			<?php echo CHtml::endForm();?>
		</div>
	</div>
</div>
<!--===========================结束==========================-->

<!--=======================编辑孩子头像弹窗===========================-->
<div class="core_pop_wrap" id="J_childAvatar_pop" style="display:none;">
	<div class="core_pop">
		<div style="width:680px;">
			<?php echo CHtml::beginForm($this->createUrl('//ivyMediaResponder/setAvatar'),'POST',array('class'=>'J_ajaxForm'));?>
			<div class="pop_top">
				<a href="" id="J_childAvatar_pop_x" class="pop_close"><?php echo Yii::t('global','Close');?></a>
				<strong id='d_tit'><?php echo Yii::t('teaching', '设置孩子头像'); ?></strong>
			</div>
			<div class="pop_cont">
				<div class="select_4 fr">
				<?php
					echo CHtml::dropDownList('avatarChildId', null, null, array('empty'=>Yii::t("global", "Select a Child"),'class'=>'fr mb10 select_3',
						'onchange'=>'previewCurrentAvatar(this)'
					));
				?>
				<img class="fr" src="" id="current-avatar-preview" style="display: none;" />
				</div>
				<div class="pop_table crop_window">
					<img src="<?php echo Yii::app()->theme->baseUrl . '/images/logo.png';?>" id="img-avatar-source" class="fl" />
				</div>
			</div>
			<div class="pop_bottom tac">
				<input name="avatar[coords]" id="avatar_coords" type="hidden" value="" />
				<input name="avatar[scales]" id="avatar_scales" type="hidden" value="" />
				<input name="avatar[source]" id="avatar_source" type="hidden" value="" />
				<input name="avatar[sid]" id="avatar_sid" type="hidden" value="" />
				
                <button id="J_childAvatar_pop_close" class="btn fr"><?php echo Yii::t('global','Cancel');?></button>
				<button class="J_ajax_submit_btn btn btn_submit mr10 fr" id="J_childAvatar_pop_sub"><?php echo Yii::t('global','OK');?></button>
			</div>
			<?php echo CHtml::hiddenField('classid', $this->currentClassId);?>
			<?php echo CHtml::hiddenField('weeknum', $this->currentWeeknum);?>
			<?php echo CHtml::hiddenField('startyear', $this->currentStartYear);?>
			<?php echo CHtml::hiddenField('yid', $this->currentCalendarId);?>
			<?php echo CHtml::hiddenField('securityCode', md5(sprintf('%s&%s&%s&%s&%s', $this->currentClassId,$this->currentWeeknum,$this->currentStartYear,Yii::app()->user->getId(),$this->securityKey)));?>
			<?php echo CHtml::endForm();?>
		</div>
	</div>
</div>
<!--===========================结束==========================-->

<!--=======================孩子每周媒体弹窗===========================-->
<div class="core_pop_wrap" id="J_childWeeklyMedia_pop" style="display:none;">
	<div class="core_pop">
		<div style="width:600px;">
			<?php echo CHtml::beginForm($this->createUrl('//ivyMediaResponder/weeklyMediaDetail'),'POST',array('class'=>'J_ajaxForm'));?>
			<div class="pop_top">
				<a href="" id="J_childWeeklyMedia_pop_x" class="pop_close"><?php echo Yii::t('global','Close');?></a>
				<strong id='d_tit'><?php echo Yii::t('teaching', '每周分配照片'); ?></strong>
				<ul id="current-child" class="child-list"> <!--place holder-->
				</ul>
			</div>
			<div class="pop_cont">
				<div class="pop_table weeklyMediaByWeek" style="height:400px;">
					<table width="100%" id="child-assigned-media">
						<colgroup>
							<col width="180">
						</colgroup>
					</table>
				</div>
			</div>
			<div class="pop_bottom tac">
                <button id="J_childWeeklyMedia_pop_close" class="btn fr"><?php echo Yii::t('global','Cancel');?></button>
				<button class="J_ajax_submit_btn btn btn_submit mr10 fr" id="J_childWeeklyMedia_pop_sub"><?php echo Yii::t('global','OK');?></button>
			</div>
			<?php echo CHtml::hiddenField('classid', $this->currentClassId);?>
			<?php echo CHtml::hiddenField('weeknum', $this->currentWeeknum);?>
			<?php echo CHtml::hiddenField('startyear', $this->currentStartYear);?>
			<?php echo CHtml::hiddenField('yid', $this->currentCalendarId);?>
			<?php echo CHtml::hiddenField('securityCode', md5(sprintf('%s&%s&%s&%s&%s', $this->currentClassId,$this->currentWeeknum,$this->currentStartYear,Yii::app()->user->getId(),$this->securityKey)));?>
			<?php echo CHtml::endForm();?>
		</div>
	</div>
</div>
<!--===========================结束==========================-->

<script>
var OAUploadBaseUrl = '<?php echo Yii::app()->params['OAUploadBaseUrl'];?>';
nameList = <?php echo CJSON::encode($nameList);?> ;
head.js('<?php echo Yii::app()->themeManager->baseUrl . '/base/js/phpfunc.js?t='?>',
		'<?php echo Yii::app()->themeManager->baseUrl . '/base/js/teaching/mediamgt_main.js?t='.time(); ?>');
</script>