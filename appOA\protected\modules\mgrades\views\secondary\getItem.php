<?php $form=$this->beginWidget('CActiveForm', array(
    'id'=>'visits-form',
    'enableAjaxValidation'=>false,
    'action' => $this->createUrl('getItem',array("id"=>$model->id)),
    'htmlOptions'=>array('class'=>'J_ajaxForm form-horizontal', 'role'=>'form'),
));

?>
<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">×</span></button>
    <h4 class="modal-title"><?php echo Yii::t('principal','修改');?></h4>
</div>
<div class="modal-body">

    <!-- 孩子生日 -->
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'fraction'); ?> *</label>
        <div class="col-xs-9">
            <?php echo $form->textField($model,'fraction',array('maxlength'=>255,'class'=>'form-control datepicker')); ?>
        </div>
    </div>
    <!-- 地址 -->
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'introduction_cn'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->textArea($model,'introduction_cn',array('rows' => 6, 'class'=>'form-control')); ?>
        </div>
    </div>

    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'introduction_en'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->textArea($model,'introduction_en',array('rows' => 6,'class'=>'form-control')); ?>
        </div>
    </div>
</div>
<div class="modal-footer">
    <button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global','Submit');?></button>
    <button type="button" class="btn btn-default"  data-dismiss="modal"><?php echo Yii::t('global','Cancel');?></button>
</div>

<?php $this->endWidget(); ?>
<script>
    // 回调：添加成功
    function cbSuccess() {
        setTimeout(function () {
            location.reload();
        }, 1000)
    }
</script>
