<?php

class LeaveController extends BranchBasedController
{
    public $actionAccessAuths = array(

    );

    public function init()
    {
        parent::init();

        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'campusOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site', 'Campus Workspace');

        //初始化选择校园页面
        $this->multipleBranch = $this->accessMultipleBranches();
        $this->branchSelectParams['hideOffice'] = false;
        $category = $_GET['category'] ? $_GET['category'] : 'leaveOvertime';
        $this->branchSelectParams['urlArray'] = array('//mcampus/leave/index', 'category' => $category);

        $cs = Yii::app()->clientScript;
        $cs->registerCoreScript('jquery.ui');
        $cs->registerCssFile($cs->getCoreScriptUrl() . '/jui/css/base/jquery-ui.css');
        $cs->registerCssFile(Yii::app()->themeManager->baseUrl . '/base/js/viewer/viewer.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/xlsx.full.min.js');

        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/element/vue.global.js');
        $cs->registerCssFile(Yii::app()->themeManager->baseUrl . '/base/js/element/index.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/element/index.js');
        // 七牛上传所需文件
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/plupload/plupload.full.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/qiniu.min.js');

        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/viewer/viewer.js');
        
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/v-calendar/v-calendar.umd.min.js');
        $cs->registerCssFile(Yii::app()->theme->baseUrl . '/css/calendar.css');
    }

    public function createUrl($route, $params = array(), $ampersand = '&', $parentOnly = false)
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        if (empty($params['category'])) {
            $params['category'] = $_GET['category'];
        }
        if (empty($params['month_data'])) {
            $params['month_data'] = $_GET['month_data'];
        }
        return parent::createUrl($route, $params, $ampersand);
    }

    public function actionApi()
    {
        $url = Yii::app()->request->getParam('url');
        $apiList = $this->getApiList();
        if (!isset($apiList[$url])) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '请求地址错误');
            $this->showMessage();
        }
        $remoteUrl = $apiList[$url];
        if ($_GET) {
            $getPath = '';
            foreach ($_GET as $key => $value) {
                if ($getPath == '') {
                    $getPath .= "$key=$value";
                } else {
                    $getPath .= "&$key=$value";
                }
            }
            $remoteUrl = $remoteUrl . '?' . $getPath;
        }
        $requestData = $_POST;
        $this->remote($remoteUrl, $requestData);
    }

    public function getApiList()
    {
        return array(
            'config' => 'leave/flow/config',
            'processList' => 'leave/flow/processList',
            'finishedList' => 'leave/flow/finishedList',
            'staffList' => 'leave/flow/staffList',
            'ccList' => 'leave/flow/ccList',
            'balance' => 'leave/flow/balance',
            'apply' => 'leave/flow/apply',
            'detail' => 'leave/flow/detail',
            'pass' => 'leave/flow/pass',
            'refuse' => 'leave/flow/refuse',
            'cancel' => 'leave/flow/cancel',
            'addFile' => 'leave/flow/addFile',
            'hrChange' => 'leave/flow/hrChange',
            'fileDetail' => 'leave/flow/fileDetail',
        );
    }

    public function checkRole() {
        $roleList = array('ivystaff_it', 'ivystaff_hr');
        foreach ($roleList as $role) {
            if (Yii::app()->user->checkAccess($role)) {
                return true;
            }
        }
        return false;
    }

    public function isHr() {
        $roleList = array('ivystaff_it', 'ivystaff_hr');
        foreach ($roleList as $role) {
            if (Yii::app()->user->checkAccess($role)) {
                return true;
            }
        }
        return false;
    }

    public function checkOpRole() {
        if ($this->isHr()) {
            return true;
        }
        $officeBranch = (array)CommonUtils::LoadConfig('CfgOffice');

        $officeBranch = array(
            'BJ_TYG',
            'BJ_BU',
            'BJ_SS',
            'BJ_DS',
            'BJ_SLT',
            'BJ_QFF',
        );
        
        if (in_array($this->staff->profile->branch, $officeBranch)) {
            return false;
        }

        if (in_array($this->branchId, $officeBranch)) {
            return false;
        }
        if ($this->branchId == 'XA_LB') {
            if (in_array($this->staff->uid, array(8005628, 8004124))) {
                return true;
            } else {
                return false;
            }
        }
        $roleList = array('ivystaff_cd', 'ivystaff_opschool');
        foreach ($roleList as $role) {
            if (Yii::app()->user->checkAccess($role)) {
                return true;
            }
        }
        return false;
    }


    public function actionIndex()
    {
        $res = CommonUtils::requestDsOnline('leave/getYearList', array('school_id' => $this->branchId));
        $this->render('index', array(
            'yearList' => $res['data'],
        ));
    }

    /****************
     ****考勤组管理****
     ****************/
    //增加更新审批组信息
    public function actionSaveApprovalGroup()
    {
        $group_name = Yii::app()->request->getParam('group_name', 1);
        $approver_first = Yii::app()->request->getParam('approver_first', '');
        $approver_second = Yii::app()->request->getParam('approver_second', '');
        $approver_cc = Yii::app()->request->getParam('approver_cc', array());
        $staff_ids = Yii::app()->request->getParam('staff_ids', array());
        $id = Yii::app()->request->getParam('id', 0);
        $requestUrl = 'leave/saveApprovalGroup';
        $this->remote($requestUrl, array(
            'group_name' => $group_name,
            'approver_first' => $approver_first,
            'approver_second' => $approver_second,
            'approver_cc' => $approver_cc,
            'staff_ids' => $staff_ids,
            'id' => $id,
        ));
    }

    //删除审批组
    public function actionDelApprovalGroup()
    {
        $id = Yii::app()->request->getParam('id', 0);
        $requestUrl = 'leave/delApprovalGroup';
        $this->remote($requestUrl, array(
            'id' => $id
        ));
    }

    //审批组列表-按分组
    public function actionGetApprovalGroupList()
    {
        $requestUrl = 'leave/getApprovalGroupList';
        $this->remote($requestUrl);
    }

    //审批组列表-按用户
    public function actionGetApprovalUserList()
    {
        $requestUrl = 'leave/getApprovalUserList';
        $this->remote($requestUrl);
    }

    //审批组详情
    public function actionGetApprovalGroupDetail()
    {
        $id = Yii::app()->request->getParam('id', 0);
        $requestUrl = 'leave/getApprovalGroupDetail';
        $this->remote($requestUrl, array(
            'id' => $id
        ));
    }

    //未分组员工
    public function actionGetUngroupedDetail()
    {
        $requestUrl = 'leave/getUngroupedDetail';
        $this->remote($requestUrl);
    }

    //校园所有部门和部门下的员工
    public function actionGetAllDepartment()
    {
        $id = Yii::app()->request->getParam('school_id', 0);
        $requestUrl = 'leave/getAllDepartment';
        $this->remote($requestUrl, array(
            'school_id' => $id,
        ));
    }

    //添加审核成员
    public function actionSaveMember()
    {
        $user_id = Yii::app()->request->getParam('user_id', 0);
        $group_id = Yii::app()->request->getParam('group_id', 0);
        $requestUrl = 'leave/saveMember';
        $this->remote($requestUrl, array(
            'user_id' => $user_id,
            'group_id' => $group_id,
        ));
    }

    //校园列表
    public function actionSchoolList()
    {
        $requestUrl = 'leave/schoolList';
        $this->remote($requestUrl);
    }

    public function actionArchiveList()
    {
        $requestUrl = 'leave/archive/list';
        $res = CommonUtils::requestDsOnline($requestUrl, array('school_id'=>$this->branchId));
        if ($res['code'] == 0) {
            foreach ( $res['data'] as $k=>$item){
                foreach ($item['list'] as $k1=>$item1){
                    $res['data'][$k]['list'][$k1]['url'] = $this->createUrl('/mcampus/leave/index', array('category' => 'settlement','month_data'=>$item1['Ym']));
                }
            }
            $this->addMessage('state', 'success');
            $this->addMessage('message', $res['msg']);
            $this->addMessage('data', $res['data']);
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->addMessage('data', $res['data']);
        }
        $this->showMessage();
    }

    public function actionArchiveDetail()
    {
        $archive_month = Yii::app()->request->getParam('archive_month', 0);
        $school_id = Yii::app()->request->getParam('school_id', '');
        $requestUrl = 'leave/archive/detail';
        $this->remote($requestUrl, array(
            'archive_month' => $archive_month,
            'school_id' => $school_id,
        ));
    }

    public function actionSaveArchiveStatus()
    {
        $id = Yii::app()->request->getParam('id', 0);
        $archive_month = Yii::app()->request->getParam('archive_month', 0);
        $requestUrl = 'leave/archive/saveStatus';
        $this->remote($requestUrl, array(
            'id' => $id,
            'archive_month' => $archive_month
        ));
    }

    public function actionArchiveFinish()
    {
        $archive_month = Yii::app()->request->getParam('archive_month', 0);
        $school_id = Yii::app()->request->getParam('school_id', '');
        $requestUrl = 'leave/archive/finish';
        $this->remote($requestUrl, array(
            'archive_month' => $archive_month,
            'school_id' => $school_id,
        ));
    }

    public function actionArchiveExport()
    {
        $archive_month = Yii::app()->request->getParam('archive_month', 0);
        $school_id = Yii::app()->request->getParam('school_id', '');
        $requestUrl = 'leave/archive/export';
        $this->remote($requestUrl, array(
            'archive_month' => $archive_month,
            'school_id' => $school_id,
        ));
    }

    public function actionExportOutOfOfficeData()
    {
        $archive_month = Yii::app()->request->getParam('archive_month', 0);
        $school_id = Yii::app()->request->getParam('school_id', '');
        $requestUrl = 'leave/archive/exportOutOfOfficeData';
        $this->remote($requestUrl, array(
            'archive_month' => $archive_month,
            'school_id' => $school_id,
        ));
    }

    public function actionArchiveCancel()
    {
        $requestUrl = 'leave/archive/cancel';
        $archive_month = Yii::app()->request->getParam('archive_month', 0);
        $this->remote($requestUrl, array(
            'archive_month' => $archive_month,
        ));
    }

    public function actionGetLeaveList()
    {
        if ($this->checkOpRole()) {
            $startyear = Yii::app()->request->getParam('startyear', 0);
            $month = Yii::app()->request->getParam('month', 0);
            $user_ids = Yii::app()->request->getParam('user_ids', 0);
            $school_id = Yii::app()->request->getParam('school_id', '');
            $requestUrl = 'leave/getLeaveList';
            $this->remote($requestUrl, array(
                'school_id' => $school_id,
                'startyear' => $startyear,
                'month' => $month,
                'user_ids' => $user_ids,
            ));
        }
    }

    public function actionSetLeaveQuotaDays()
    {
        $startyear = Yii::app()->request->getParam('startyear', 0);
        $user_data = Yii::app()->request->getParam('user_data', 0);
        $requestUrl = 'leave/setLeaveQuotaDays';
        $this->remote($requestUrl, array(
            'startyear' => $startyear,
            'user_data' => $user_data,
        ));
    }

    public function actionGetLeaveQuotaDays()
    {
        $startyear = Yii::app()->request->getParam('startyear', 0);
        $user_ids = Yii::app()->request->getParam('user_ids', 0);
        $requestUrl = 'leave/getLeaveQuotaDays';
        $this->remote($requestUrl, array(
            'startyear' => $startyear,
            'user_ids' => $user_ids,
        ));
    }

    public function actionGetYearList()
    {
        $requestUrl = 'leave/getYearList';
        $this->remote($requestUrl, array());
    }

    public function actionGetLeaveDays()
    {
        $requestUrl = 'leave/getLeaveDays';
        $startyear = Yii::app()->request->getParam('startyear', 0);
        $month = Yii::app()->request->getParam('month', 0);
        $user_id = Yii::app()->request->getParam('user_id', 0);
        $type = Yii::app()->request->getParam('type', 0);
        $this->remote($requestUrl, array(
            'startyear' => $startyear,
            'month' => $month,
            'user_id' => $user_id,
            'type' => $type,
        ));
    }

    public function actionGetCalendar()
    {
        $requestUrl = 'leave/getCalendar';
        $year = Yii::app()->request->getParam('year', 0);
        $month = Yii::app()->request->getParam('month', 0);
        $school_id = Yii::app()->request->getParam('school_id', 0);
        $this->remote($requestUrl, array(
            'year' => $year,
            'school_id' => $school_id,
            'month' => $month,
        ));
    }

    public function actionGetTodayLeave()
    {
        $requestUrl = 'leave/getTodayLeave';
        $date = Yii::app()->request->getParam('date', 0);
        $school_id = Yii::app()->request->getParam('school_id', 0);
        $this->remote($requestUrl, array(
            'date' => $date,
            'school_id' => $school_id,
        ));
    }


    public function remote($requestUrl, $requestData = array())
    {
        if (empty($requestData['school_id'])) {
            $requestData['school_id'] = $this->branchId;
        }
        $res = CommonUtils::requestDsOnline($requestUrl, $requestData);
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('message', $res['msg']);
            $this->addMessage('data', $res['data']);
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->addMessage('data', $res['data']);
        }
        $this->showMessage();
    }

}