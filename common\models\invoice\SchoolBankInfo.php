<?php

/**
 * This is the model class for table "ivy_school_bank_info".
 *
 * The followings are the available columns in table 'ivy_school_bank_info':
 * @property integer $id
 * @property string $schoolid
 * @property integer $yid
 * @property string $sign
 * @property string $en_title
 * @property string $cn_title
 * @property string $en_content
 * @property string $cn_content
 * @property integer $info_type
 * @property integer $weight
 * @property integer $updated_timestamp
 * @property integer $userid
 */
class SchoolBankInfo extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return WSchoolBankInfo the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_school_bank_info';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('schoolid, en_title, cn_title, en_content, cn_content, updated_timestamp, userid', 'required'),
			array('yid, info_type, weight, updated_timestamp, userid', 'numerical', 'integerOnly'=>true),
			array('schoolid, sign, en_title, cn_title', 'length', 'max'=>255),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, schoolid, yid, sign, en_title, cn_title, en_content, cn_content, info_type, weight, updated_timestamp, userid', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'schoolid' => 'Schoolid',
			'yid' => 'Yid',
			'sign' => 'Sign',
			'en_title' => 'En Title',
			'cn_title' => 'Cn Title',
			'en_content' => 'En Content',
			'cn_content' => 'Cn Content',
			'info_type' => 'Info Type',
			'weight' => 'Weight',
			'updated_timestamp' => 'Updated Timestamp',
			'userid' => 'Userid',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('yid',$this->yid);
		$criteria->compare('sign',$this->sign,true);
		$criteria->compare('en_title',$this->en_title,true);
		$criteria->compare('cn_title',$this->cn_title,true);
		$criteria->compare('en_content',$this->en_content,true);
		$criteria->compare('cn_content',$this->cn_content,true);
		$criteria->compare('info_type',$this->info_type);
		$criteria->compare('weight',$this->weight);
		$criteria->compare('updated_timestamp',$this->updated_timestamp);
		$criteria->compare('userid',$this->userid);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	public function combineDisplay($type="title", $sep='<br>'){
		if(in_array($type,array("title","content"))){
			$cnCol = "cn_".$type;
			$enCol = "en_".$type;
			$ret = Yii::app()->format->ntext($this->getAttribute($enCol));
			$ret .= ($ret == "") ? "" : $sep;
			$ret .= Yii::app()->format->ntext($this->getAttribute($cnCol));
			return $ret;
		}

	}
}