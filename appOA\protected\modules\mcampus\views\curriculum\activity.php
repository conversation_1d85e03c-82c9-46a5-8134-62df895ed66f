<div class="container-fluid">
    <!-- 面包屑导航 -->
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site','Campus Operations'), array('//mcampus/default/index'))?></li>
        <li><?php echo CHtml::link(Yii::t('site','Routines'), array('//mcampus/default/index'))?></li>
        <li><?php echo CHtml::link(Yii::t('curriculum','Curriculum'), array('//mcampus/curriculum/project'))?></li>
        <li class="active"><?php echo Yii::t('curriculum','Activity List');?></li>
    </ol>
    <div class="row">
        <!-- 左侧菜单栏 -->
        <div class="col-md-2">
            <div class="list-group" id="classroom-status-list">
                <?php foreach ($this->leftMenu as $key => $value) { ?>
                <a href="<?php echo $this->createUrl($value[0]);?>" class="list-group-item status-filter <?php echo $this->getAction()->getId()==$value[0]?'active':''; ?>"><?php echo $value[1]; ?></a></li>
                <?php } ?>
            </div>
        </div>
        <div class="col-md-10">
            <?php if (Yii::app()->user->checkAccess('o_E_Edu_Common')) { ?>
            <div class="mb10">
                <a class="btn btn-primary"  href="<?php echo $this->createUrl('editactivity'); ?>"><span class="glyphicon glyphicon-plus"></span> <?php echo Yii::t('curriculum', 'Add a New Activity');?></a>
            </div>
            <?php } ?>
            <!-- 搜索框 -->
            <div class="btn-group mb10">
                <form action="<?php echo $this->createUrl('activity'); ?>" method="get" class="row">
                <!-- 年龄 -->
                <div class="col-sm-2">
                <select name="age" class="form-control">
                    <option value=""><?php echo Yii::t('curriculum','Age');?></option>
                    <?php foreach ($diglossia['age'] as $key => $value) {
                        echo "<option value='$key'";
                        echo Yii::app()->request->getParam('age','')==$key?'selected':'';
                        echo ">$value</option>";
                    } ?>
                </select> 
                </div>
                <!-- 季节 -->
                <div class="col-sm-2">
                <select name="season" class="form-control">
                    <option value=""><?php echo Yii::t('curriculum','All Season');?></option>
                    <?php foreach ($diglossia['season'] as $key => $value) {
                        echo "<option value='$key'";
                        echo Yii::app()->request->getParam('season','')==$key?'selected':'';
                        echo ">$value</option>";
                    } ?>
                </select>
                </div>
                <!-- 多元智能 -->
                <div class="col-sm-2">
                <select name="intelligence" class="form-control">
                    <option value=""><?php echo Yii::t('curriculum','Intelligence');?></option>
                    <?php foreach ($diglossia['intelligence'] as $key => $value) {
                        echo "<option value='$key'";
                        echo Yii::app()->request->getParam('intelligence','')==$key?'selected':'';
                        echo ">$value</option>";
                    } ?>
                </select> 
                </div>
                <!-- 学习领域 -->
                <div class="col-sm-2">
                <select name="learning" class="form-control">
                    <option value=""><?php echo Yii::t('curriculum','Learning Domains');?></option>
                    <?php foreach ($diglossia['learning'] as $key => $value) {
                        echo "<option value='$key'";
                        echo Yii::app()->request->getParam('learning','')==$key?'selected':'';
                        echo ">$value</option>";
                    } ?>
                </select> 
                </div> 
                <!-- 中西文化 -->
                <div class="col-sm-2">
                <select name="cultural" class="form-control">
                    <option value=""><?php echo Yii::t('curriculum','Cultural');?></option>
                    <?php foreach ($diglossia['cultural'] as $key => $value) {
                        echo "<option value='$key'";
                        echo Yii::app()->request->getParam('cultural','')==$key?'selected':'';
                        echo ">$value</option>";
                    } ?>
                </select> 
                </div>
                <!-- 活动地点 -->
                <div class="col-sm-2">
                <select name="room" class="form-control">
                    <?php foreach ($diglossia['room'] as $key => $value) {
                        echo "<option value='$key'";
                        echo Yii::app()->request->getParam('room',40)==$key?'selected':'';
                        echo ">$value</option>";
                    } ?>
                </select> 
                </div><br><br><br>
                <!-- 是否为教育部门 -->
                <div class="col-sm-2">
                <select name="official" class="form-control">
                    <?php foreach ($diglossia['official'] as $key => $value) {
                        echo "<option value='$key'";
                        echo Yii::app()->request->getParam('official',3)==$key?'selected':'';
                        echo ">$value</option>";
                    } ?>
                </select> 
                </div>
                <!-- 搜索内容 -->
                <div class="col-sm-4">
                    <div class="input-group">
                        <input type="text" class="form-control" name="search" value="<?php echo Yii::app()->request->getParam('search','')?Yii::app()->request->getParam('search',''):''; ?>">
                        <span class="input-group-btn">
                        <button class="btn btn-default" type="submit"><span class="glyphicon glyphicon-search"> </span> </button>
                        </span>
                    </div>
                </div>
                </form>
            </div>
            <div class="panel panel-default">
                <div class="panel-body">
                    <?php $this->widget('ext.ivyCGridView.BsCGridView', array(
                        'id'=>'points-product-grid',
                        'dataProvider'=>$activity,
                        'colgroups'=>array(
                            array(
                                "colwidth"=>array(50,50,50,80),
                            )
                        ),
                        'columns'=>array(
                            array(
                            'name'=>'cn_title',
                            'value'=>array($this,'getActivityName'),
                            ),
                            array(
                            'name'=>'en_title',
                            'value'=>array($this,'getEnActivityName'),
                            ),
                            array(
                            'name'=>Yii::t('curriculum','Public'),
                            'value'=>array($this,'getUserName'),
                            ),
                            array(
                            'name'=>Yii::t('curriculum','Operation'),
                            'value'=>array($this,'getActButton'),
                           ),
                        ),
                        )); 
                    ?>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- 复制项目模态框 -->
<div class="modal fade" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
        <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            <h4 class="modal-title" id="myModalLabel"><?php echo Yii::t('curriculum','Duplicate to my projects'); ?></h4>
        </div>
        <div class="modal-body">
            <select class="form-control select_4" id="copypid">
            <?php foreach ($projects as $key => $value) {
                $content = $value->cn_title.' '.$value->en_title;
                echo "<option value='$value->pid'>$content</option>";
            } ?>
            </select>                    
        </div>
        <div class="modal-footer">
        <input type="hidden" id="aid" name="aid">
        <button onclick="clearInfo()" type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('curriculum','Close'); ?></button>
        <a class="btn btn-primary" onclick="copy(this,aid)"><?php echo Yii::t('curriculum','Copy'); ?></a>
        <h3 id="copyinfo" class="text-center"></h3>
      </div>
    </div>
  </div>
</div>


<script>
    //删除活动
    function deleteAct (deletebtn,aid) {
        if (confirm('确定要删除吗？')) {
            $.ajax( {    
                url:'<?php echo $this->createUrl('deleteActivity'); ?>',    
                data:{aid : aid},    
                type:'post',    
                dataType:'json',    
                success:function(data) {    
                    if(data.msg == 'success'){    
                        $(deletebtn).parent().parent().remove();
                        // window.location.reload(); 
                    }else{    
                        alert(data.msg);
                        console.log(data);    
                    }    
                 },    
                error : function() {    
                    console.log(data.error);    
                }    
            });  
        }
    }
    //活动状态控制
    function toggle (togglebtn,aid) {
        $.ajax( {
            url:'<?php echo $this->createUrl('state'); ?>',
            data:{aid : aid},
            type:'post',
            dataType:'json',
            success:function(data){
                console.log(data);
                if (data.msg == "success") {
                    $(togglebtn).text(data.btnvalue);
                    $(togglebtn).removeClass();
                    $(togglebtn).addClass(data.btnclass);
                }else{
                    console.log(data.msg);
                }
            },
            error:function(data){
                console.log(data.error);
            }
        });
    }
    //复制活动aid到模态框
    function copyaid (btncopy,aid) {
        $('#aid').val(aid);
    }
    //复制活动
    function copy (btncopy,aid) {
        $("#copyinfo").text();
        aid = $('#aid').val();
        pid = $('#copypid').val();
        $.ajax({
            url:'<?php echo $this->createUrl('copyFavorites'); ?>',
            data:{aid : aid,pid : pid},
            type:'post',
            dataType:'json',
            success:function(data){
                if(data.msg == 'success'){
                    console.log(data.info);
                    $("#copyinfo").text(data.info);
                    $(btncopy).addClass('disabled');
                    window.location.reload();
                }else{
                    console.log(data.info);
                    $("#copyinfo").text(data.info);
                }
            },
            error:function(data){
                console.log('请求服务器失败');
            }
        });
    }
    //清空提示信息
    function clearInfo () {
        $("#copyinfo").text('');
    }
</script>