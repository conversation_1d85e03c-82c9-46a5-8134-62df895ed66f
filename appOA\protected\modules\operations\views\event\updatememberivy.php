<?php $form=$this->beginWidget('CActiveForm', array(
	'id'=>'event-memberivy-form',
	'enableAjaxValidation'=>false,
    'htmlOptions'=>array('class'=>'J_ajaxForm'),
));
?>
<div class="pop_cont pop_table" style="height:auto;">
    <table width="100%">
        <colgroup>
            <col class="th" />
            <col />
        </colgroup>
        <tbody>
            <tr>
                <th><?php echo $form->labelEx($model,'parent_name'); ?></th>
                <td>
                    <?php echo $form->textField($model,'parent_name',array('maxlength'=>255,'class'=>'input length_6')); ?>
                </td>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($model,'telphone'); ?></th>
                <td>
                    <?php echo $form->textField($model,'telphone',array('maxlength'=>255,'class'=>'input length_6')); ?>
                </td>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($model,'schoolid'); ?></th>
                <td>
                    <?php echo $form->dropDownList($model,'schoolid',$this->branchArr,array('empty'=>Yii::t('global','Please Select'))); ?>
                </td>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($model,'ivyclass'); ?></th>
                <td>
                    <?php echo $form->textField($model,'ivyclass',array('maxlength'=>255,'class'=>'input length_6')); ?>
                </td>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($model,'child_name'); ?></th>
                <td>
                    <?php echo $form->textField($model,'child_name',array('maxlength'=>255,'class'=>'input length_6')); ?>
                </td>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($model,'adult_number'); ?></th>
                <td>
                    <?php echo $form->numberField($model,'adult_number',array('class'=>'input length_1')); ?>
                </td>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($model,'kid_number'); ?></th>
                <td>
                    <?php echo $form->numberField($model,'kid_number',array('class'=>'input length_1')); ?>
                </td>
            </tr>
        </tbody>
    </table>
</div>
<div class="pop_bottom">
    <button type="button" class="btn fr" id="J_dialog_close"><?php echo Yii::t('global','Cancel');?></button>
    <button class="btn fr btn_submit mr10 J_ajax_submit_btn"><?php echo Yii::t('global','Submit');?></button>
</div>
<?php $this->endWidget(); ?>