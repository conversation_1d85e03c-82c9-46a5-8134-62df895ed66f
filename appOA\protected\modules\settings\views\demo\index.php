<?php
/* @var $this DemoController */

$this->breadcrumbs=array(
	'Demo',
);
?>
<h1><?php echo $this->id . '/' . $this->action->id; ?></h1>


<a class="btn J_dialog" title="测试对话框" href="/settings/demo/index" role="button"><span class="add"></span>测试对话框</a>


	<table width="100%">
		<tbody><tr class="ct"><td colspan="5" style="padding-left:38px;"><a data-type="nav_1" data-html="tbody" href="" id="J_add_root" class="link_add">添加导航</a></td></tr>
	</tbody></table>

<p>
	You may change the content of this page by modifying
	the file <tt><?php echo __FILE__; ?></tt>.
</p>



<div class="h_a">账单管理</div>
<div class="table_full">
<table width=100%>
	<colgroup>
		<col class="th" width=100>
		<col width=null>
		<col width=200>
	</colgroup>
	<tbody>
		<tr>
			<th>
				孩子照片
			</th>
			<td colspan=2>
				孩子信息
			</td>
		</tr>
		<tr>
			<th>
				<?php
					$test = OA::getPolicy(OA::POLICY_PAY, 2013, 'BJ_TT');
					$paymentMenu = $test->getMenus();
					$this->widget('zii.widgets.CMenu', array(
						'items' => $paymentMenu,
						)
					);
				?>
			</th>
			<td>
			</td>
			<td>
				近期账单
			</td>
		</tr>
	</tbody>
</table>
</div>