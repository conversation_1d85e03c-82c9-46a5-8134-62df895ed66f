<div class="row hide" id="relationView">
    <div class="col-md-6 col-sm-12">
        <div class="background-gray p8">
            <button class="btn btn-primary" type="button" title="添加校园" onclick="addSchool(0)"><span class="glyphicon glyphicon-plus"></span> 添加校园</button>
        </div>
        <div id="school"></div>
    </div>
    <div class="col-md-6 col-sm-12">
        <div class="background-gray p8">
            <button class="btn btn-primary" type="button" title="添加支付公司" onclick="addChannel(0)"><span class="glyphicon glyphicon-plus"></span> 添加支付公司</button>
        </div>
        <div id="channel"></div>
    </div>
</div>

<div class="modal fade" id="addschool">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title">添加校园</h4>
            </div>
            <?php
            $form=$this->beginWidget('CActiveForm', array(
                'id'=>'school-form',
                'action'=>$this->createUrl('saveSchool'),
                'htmlOptions'=>array('class'=>'J_ajaxForm form-horizontal', 'role'=>'form'),
            ));
            ?>
            <div class="modal-body">
                <div class="form-group">
                    <label class="col-sm-2 control-label"></label>
                    <div class="col-sm-10" id="account-info">

                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-2 control-label"></label>
                    <div class="col-sm-10" id="school-info">
                        <select class="form-control" name="schoolid"></select>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary J_ajax_submit_btn">Save changes</button>
            </div>
            <?php
            echo CHtml::hiddenField('accountid');
            $this->endWidget();
            ?>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->

<div class="modal fade" id="addchannel">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title">添加支付公司</h4>
            </div>
            <?php
            $form=$this->beginWidget('CActiveForm', array(
                'id'=>'channel-form',
                'action'=>$this->createUrl('saveChannel'),
                'htmlOptions'=>array('class'=>'J_ajaxForm form-horizontal', 'role'=>'form'),
            ));
            ?>
            <div class="modal-body">
                <div class="form-group">
                    <label class="col-sm-2 control-label"></label>
                    <div class="col-sm-10" id="account-info">

                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-2 control-label"><?php echo BankChannel::model()->getAttributeLabel('type')?></label>
                    <div class="col-sm-10">
                        <?php echo CHtml::dropDownList('BankChannel[type]', '', array(
                            'alipay'=>'AliPay',
                            'yeepay'=>'YeePay',
                            'allinpay'=>'AllinPay'), array(
                            'class'=>'form-control',
                            'empty'=>'请选择'
                        ));?>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-2 control-label"><?php echo BankChannel::model()->getAttributeLabel('cid')?></label>
                    <div class="col-sm-10">
                        <?php echo CHtml::textField('BankChannel[cid]', '', array('class'=>'form-control')); ?>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-2 control-label"><?php echo BankChannel::model()->getAttributeLabel('cname')?></label>
                    <div class="col-sm-10">
                        <?php echo CHtml::textField('BankChannel[cname]', '', array('class'=>'form-control')); ?>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-2 control-label"><?php echo BankChannel::model()->getAttributeLabel('feerate')?></label>
                    <div class="col-sm-10">
                        <?php echo CHtml::textField('BankChannel[feerate]', '', array('class'=>'form-control')); ?>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-2 control-label"><?php echo BankChannel::model()->getAttributeLabel('memo')?></label>
                    <div class="col-sm-10">
                        <?php echo CHtml::textArea('BankChannel[memo]', '', array('class'=>'form-control')); ?>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary J_ajax_submit_btn">Save changes</button>
            </div>
            <?php
            echo CHtml::hiddenField('BankChannel[id]');
            echo CHtml::hiddenField('BankChannel[accountid]');
            $this->endWidget();
            ?>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->

<script>
    $('#account-list').on('click', 'a', function(){
        $('#account-list a').each(function(){
            $(this).removeClass('background-gray');
        });
        $(this).addClass('background-gray');
        showAccount(this);
    });

    var accountid = 0;
    var accountname, accountnum;
    function showAccount(_this)
    {
        var id = $(_this).data('id');
        accountid = id;
        $.get('<?php echo $this->createUrl('accountRelation')?>', {id: id}, function(data){
            accountname = data.model.name;
            accountnum = data.model.account;
            $('#relationView #school').html(data.sHtml);
            $('#relationView #channel').html(data.cHtml);
            head.Util.ajaxDel($('#relationView'));
        }, 'json');
        $('#relationView').removeClass('hide');
    }

    function addSchool(id)
    {
        if(accountid>0){
            $('#addschool #account-info').text(accountname+' <'+accountnum+'>');
            $('#addschool #accountid').val(accountid);
            $('#school-info>select').html('');
            $('<option></option>').text('请选择').val('').appendTo( $('#school-info>select') );
            $.get('<?php echo $this->createUrl('getSchool')?>', {}, function(data){
                for(var datum in data){
                    $('<option></option>').text(data[datum]).val(datum).appendTo( $('#school-info>select') );
                }
            }, 'json');
            $('#addschool').modal();
        }
    }

    function addChannel(id)
    {
        var data = {
            id: 0,
            accountid: accountid,
            type: 0,
            cid: '',
            cname: '',
            feerate: '',
            memo: ''
        };
        if(accountid>0){
            if(id>0){
                $.ajax({
                    type: 'get',
                    url: '<?php echo $this->createUrl('getChannel')?>?id='+id,
                    dataType: 'json',
                    async: false
                }).done(function(ret){
                    data = ret;
                });
            }
            $('#BankChannel_id').val(data.id);
            $('#BankChannel_accountid').val(accountid);
            $('#BankChannel_type').val(data.type);
            $('#BankChannel_cid').val(data.cid);
            $('#BankChannel_cname').val(data.cname);
            $('#BankChannel_feerate').val(data.feerate);
            $('#BankChannel_memo').val(data.memo);
            $('#addchannel').modal();
        }
    }

    // 添加学校回调
    function sSchool()
    {
        $('#account-list a[data-id="'+accountid+'"]').click();
        $('#addschool').modal('hide');
    }

    // 删除学校回调
    function dSchool(data)
    {
        $('#account-list a[data-id="'+accountid+'"]').click();
        if(data.state == 'success'){
            var ret = 0;
        }
        else{
            var ret = 1;
        }
        resultTip({msg: data.message, error: ret});
    }

    function sChannel()
    {
        $('#account-list a[data-id="'+accountid+'"]').click();
        $('#addchannel').modal('hide');
    }
</script>