.main-menu-toggle{}
.main-menu-toggle dl{margin-bottom: 0.4em;}
.main-menu-toggle dt{color: #999}
.main-menu-toggle dt b.caret{display:none}
.main-menu-toggle dd a{margin-right: 0.8em}

/*
===================
全局弹窗
*pop_top 头
	**strong 标题
	**pop_close 关闭
*pop_cont 中
	**pop_table 表格
*pop_bottom 尾
*pop_tips 提示
*pop_loading 加载状态
===================
*/
.core_pop_wrap{
    background-color: #fff;
    border: 1px solid #ccc;
    box-shadow:0 0 5px rgba(0,0,0,0.2);
    border-radius: 3px;
    position:fixed;
    _position:absolute;/*ie6*/
    z-index:10;
    color:#333;
    outline:none;
}
.core_pop_wrap a{
    color:#336699;
}
.pop_top{
    line-height:18px;
    padding:9px 0 8px;
    border-top:1px solid #fff;
    border-bottom:1px solid #e7e7e7;
    background:#f6f6f6;
    zoom:1;
    /*margin-bottom:5px;*/
}
.pop_top:after{
    content:'\20';
    display:block;
    height:0;
    clear:both;
    visibility: hidden;
    width:1px;
}
.pop_top strong{
    text-indent:15px;
    font-size:14px;
    color:#333;
    font-weight:700;
    white-space:nowrap;
    margin-right:10px;
    float:left;
}
.pop_top select{
    float:left;
    padding:1px;
    line-height:22px;
    height:22px;
}
.pop_top ul{
    border-bottom:1px solid #ccc;
    height:25px;
}
.pop_top li{
    float:left;
    display:block;
    line-height:25px;
    height:25px;
    padding:0 15px;
    cursor:pointer;
}
.pop_top li.current{
    float:left;
    border:1px solid #ccc;
    background:#fff;
    border-bottom:0 none;
}
.pop_top .pop_close{
    margin-right:15px;
}
.pop_bottom{
    padding:10px 15px;
    border-top:1px solid #e9e9e9;
    height:50px;
    line-height: 30px;
    /*white-space:nowrap;*/
}
.pop_bottom label{
    display:inline-block;
    padding-top:3px;
}
.pop_bottom .btn{
    padding-left:20px;
    padding-right:20px;
}
.pop_bottom .tips_error,
.pop_bottom .tips_success{
    max-width:200px;
    float:left;
}
.founder_pop .pop_bottom .tips_error{
    width:150px;
}
.pop_cont{
    background:#fff;
    color:#333;
    padding:10px 15px 10px;
}
.pop_table{
    height:365px;
    overflow-y:auto;
    position:relative;
}
.pore{
    position:relative;
}
.pop_table th,
.pop_table td{
    padding:6px 0 6px;
}
.pop_table th{
    height:26px;
    line-height:26px;
    vertical-align:top;
    white-space:nowrap;
    padding-right:20px;
}
.pop_table tr:hover th{
    color:#000;
}
.pop_tips{
    background:#f7f7f7;
    line-height:24px;
    padding:0 10px;
    margin:0 10px 0;
    color:#666;
}
/*关闭*/
.pop_close{
    margin-top:5px;
    float:right;
    width:9px;
    height:8px;
    overflow:hidden;
    text-indent:-2000em;
    background:url(../images/close.png) no-repeat;
    -webkit-transition: all 0.2s ease-out;
    margin-left:10px;
}
.pop_close:hover{
    background-position:0 -8px;
}
/*读取中*/
.pop_loading{
    background:url(../images/loading.gif) center center no-repeat;
    height:80px;
    width:80px;
    margin:auto;
}

/*
===================
非操作型提示
>>	作用与操作成功等，自动关闭效果的弹窗
===================
*/
.pop_showmsg_wrap:focus{
    outline:0 none;
}
.pop_showmsg_wrap,
.pop_showmsg{
    background:#f6fbfe url(../images/pop_showmsg.png) no-repeat;
}
.pop_showmsg_wrap{
    position:fixed;
    _position:absolute;
    z-index:11;
    height:55px;
    padding-right:9px;
    background-position:right 0;
    border-radius: 8px;
    box-shadow:0 0 10px #e1e1e1;
}
.pop_showmsg{
    height:55px;
    font-size:14px;
    background-position:left 0;
    border-top-left-radius:8px;
    border-bottom-left-radius:8px;
    display:inline-block;
}
.pop_showmsg span{
    padding:10px 10px 10px 68px;
    display:inline-block;
    line-height:36px;
    /*height:35px;*/
    text-shadow: 0 1px 1px #eee;
    color:#333;
}
.pop_showmsg span.tip-success{
    background:url(../images/success.gif) 20px center no-repeat;
}
.pop_showmsg span.tip-warning{
    background:url(../images/warning.gif) 20px center no-repeat;
}

/*
===================
间距
===================
*/
.mb5{
    margin-bottom:5px !important;
}
.mb10{
    margin-bottom:10px !important;
}
.mb15{
    margin-bottom:15px !important;
}
.mb20{
    margin-bottom:20px !important;
}
.mr5{
    margin-right:5px !important;
}
.mr10{
    margin-right:10px !important;
}
.mr15{
    margin-right:15px !important;
}
.mr20{
    margin-right:20px !important;
}
.ml5{
    margin-left:5px !important;
}
.ml10{
    margin-left:10px !important;
}
.ml15{
    margin-left:15px !important;
}
.ml20{
    margin-left:20px !important;
}
.mt5{
    margin-top:5px !important;
}
.mt10{
    margin-top:10px !important;
}
.mt15{
    margin-top:15px !important;
}
.mt20{
    margin-top:20px !important;
}
.p2{
    padding:2px !important;
}
.p5{
    padding:5px !important;
}
.p8{
    padding: 8px !important;
}
.p10{
    padding:10px !important;
}
.p15{
    padding:15px !important;
}
.p20{
    padding:20px !important;
}
.pt10{
    padding-top: 10px !important;
}
.pb10{
    padding-bottom: 10px !important;
}
.pb20{
    padding-bottom:20px !important;
}
.pt7{
    padding-top:7px !important;
}
.background-gray{
    background: #f7f7f9;
}
/*
===================
输入框文本框长度
===================
*/
.length_0{
    width:20px;
}
.length_1{
    width:50px;
}
.length_2{
    width:110px;
}
.length_3{
    width:170px;
}
.length_4{
    width:230px;
}
.length_5{
    width:290px;
}
.length_6{
    width:350px;
}

/*
===================
选择select专属长度
===================
*/
.select_1{
    width:60px;
}
.select_2{
    width:120px;
}
.select_3{
    width:180px;
}
.select_4{
    width:240px;
}
.select_5{
    width:300px;
}
.select_6{
    width:360px;
}
.program30{
    color: dodgerblue;
}
.program20{
    color: orangered;
}
.program10{
    color: #008000;
}
.program0{
    color: #000;
}
.w400{
    width: 400px !important;
}
.w80{
    width: 80px !important;
}
.nav-pills>li.warning>a, .nav-pills>li.warning>a:hover, .nav-pills>li.warning>a:focus {
    color: #fff;
    background-color: #ed9c28;
}
.date-active a {
    background: url("../images/circle.png") no-repeat right -2px !important;
}
.grid-view-loading{
    background: url("../../base/images/loading.gif") no-repeat center 38.2%;
}

.ui-datepicker-calendar tbody td a, .ui-datepicker-calendar tbody td span{text-align: center;font-family:arial !important;}
.ui-datepicker-week-end a.ui-state-default{color:#c1c1c1 !important;}
.ui-widget{font-weight: inherit !important;font-family: inherit !important}
.ui-widget input,.ui-widget select,.ui-widget textarea,.ui-widget button{font-family:inherit !important}
.ui-datepicker select.ui-datepicker-month, .ui-datepicker select.ui-datepicker-year {width:39% !important;}

.modal-footer #J_success_info, .modal-footer #J_fail_info{float: left;}
.mt {
    margin-top: 6px;
}
.J_ajax_del_in {
    overflow: hidden;
    margin-left: 6px;
    transition: .6s;
}
#audit {
    width: 80%;
    margin-top: 6px;
}
.new_Employee_Input {
    margin-top: 5px;
    margin-bottom: 15px;
}
/*鼠标样式*/
.cur-p {
    cursor: pointer;
}
/* 字体大小 */
.font14{
    font-size:14px
}
.font12{
    font-size:12px
}
.fontWeightNormal{
    font-weight: normal;
}
.fontBold{
    font-weight: bold;
}
.relative{
    position:relative
}
.color6{
    color:#666666 !important
}
.color3{
    color:#333333 !important
}
.color9{
    color:#999999 !important
}
/* 滚动条样式 */
.scroll-box {
    font-size: 100%;
}
.scroll-box::-webkit-scrollbar {
    /*滚动条整体样式*/
    width : 6px;  /*高宽分别对应横竖滚动条的尺寸*/
    height: 1px;
}
.scroll-box::-webkit-scrollbar-thumb {
    /*滚动条里面小方块*/
    border-radius   : 10px;
    background-color: skyblue;
    background-image: -webkit-linear-gradient(
    45deg,
    rgba(255, 255, 255, 0.2) 25%,
    transparent 25%,
    transparent 50%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0.2) 75%,
    transparent 75%,
    transparent
    );
}
.scroll-box::-webkit-scrollbar-track {
    /*滚动条里面轨道*/
    box-shadow   : inset 0 0 5px rgba(0, 0, 0, 0.2);
    background   : #ededed;
    border-radius: 10px;
}

/* flex */
.flex {
    display: flex
}

.flex1 {
    flex: 1
}
.inline-block{
    display: inline-block;
}
/* 二维码高度 */
.qrcodeImg{
    width:120px;
    height:120px
}
.font16{
    font-size: 16px;
}
.font18{
    font-size:18px
}
.p24{
    padding:24px
}
.mb4{
    margin-bottom: 4px !important;
}
.mb8{
    margin-bottom: 8px !important;
}
.mb12{
    margin-bottom: 12px !important;
}
.mb16{
    margin-bottom: 16px !important;
}
.mb24{
    margin-bottom: 24px !important;
}
.mt4{
    margin-top: 4px !important;
}
.mt8{
    margin-top: 8px !important;
}
.mt12{
    margin-top: 12px !important;
}
.mt16{
    margin-top: 16px !important;
}
.mt24{
    margin-top: 24px !important;
}

.ml4{
    margin-left: 4px !important;
}
.ml8{
    margin-left: 8px !important;
}
.ml12{
    margin-left: 12px !important;
}
.ml16{
    margin-left: 16px !important;
}
.ml24{
    margin-left: 24px !important;
}
.mt2{
    margin-top: 2px !important;
}
.mr4{
    margin-right: 4px !important;
}
.mr8{
    margin-right: 8px !important;
}
.mr12{
    margin-right: 12px !important;
}
.mr16{
    margin-right: 16px !important;
}
.mr24{
    margin-right: 24px !important;
}

.pb4{
    padding-bottom: 4px !important;
}
.pb8{
    padding-bottom: 8px !important;
}
.pb12{
    padding-bottom: 12px !important;
}
.pb16{
    padding-bottom: 16px !important;
}
.pb24{
    padding-bottom: 24px !important;
}

.pt4{
    padding-top: 4px !important;
}
.pt8{
    padding-top: 8px !important;
}
.pt12{
    padding-top: 12px !important;
}
.pt16{
    padding-top: 16px !important;
}
.pt24{
    padding-top: 24px !important;
}

.pl4{
    padding-left: 4px !important;
}
.pl8{
    padding-left: 8px !important;
}
.pl12{
    padding-left: 12px !important;
}
.pl16{
    padding-left: 16px !important;
}
.pl24{
    padding-left: 24px !important;
}

.pr4{
    padding-right: 4px !important;
}
.pr8{
    padding-right: 8px !important;
}
.pr12{
    padding-right: 12px !important;
}
.pr16{
    padding-right: 16px !important;
}
.pr20{
    padding-right: 20px !important;
}
.pr24{
    padding-right: 24px !important;
}
.p16{
    padding:16px
}
.m24{
    margin:24px !important
}
.overflow-y{
    overflow-y: auto;
}
.font20{
    font-size: 20px;
}
.loading{
    width:100%;
    height:100%;
    background:#fff;
    position: absolute; 
    opacity: 0.5;
    z-index: 99
}
.loading span{
    width:100%;
    height:100%;
    display:block;
    background: url("../../base/images/loading.gif")no-repeat center center ;
}
.align-items{
    align-items: center;
}
.nowrap{
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.overflowHidden{
    overflow: hidden;
}

.viewer-zoom-in,.viewer-zoom-out,.viewer-one-to-one,.viewer-reset,.viewer-play,.viewer-flip-horizontal,.viewer-flip-vertical{
    display: none;
}
.viewer-next{
    position: fixed;
    right: 5%;
    top: 45%;
    background-image: url('https://m2.files.ivykids.cn/cloud01-file-8025768Fgap2v19zXYFavJf6Wt7dxLPryqm.png');
    width: 50px !important;
    height: 50px !important;
    line-height: 150px;
    background-color: initial !important;
    background-size: cover;
}
.viewer-prev{
    position: fixed;
    left:5%;
    top: 45%;
    background-image: url('https://m2.files.ivykids.cn/cloud01-file-8025768FuZykfKy-7LNOGqfZzo5zgv6ZiXy.png');
    width: 50px !important;
    height: 50px !important;
    line-height: 150px;
    background-color: initial !important;
    background-size: cover;
}

.viewer-rotate-left,.viewer-rotate-right{
    width: 32px !important;
    height: 32px !important;
    margin-left:7px !important;
    margin-right: 7px !important;
}

.viewer-rotate-left::before{
    background-image: url('https://m2.files.ivykids.cn/cloud01-file-8025768FhqQ5HafUTJ3RBO79a_FnxHs_FBP.png');
    background-size: cover;
    width: 20px;
    height: 20px;
    background-position:0;
    margin: 6px !important;

}

.viewer-rotate-right::before{
    background-image: url('https://m2.files.ivykids.cn/cloud01-file-8025768FjuQq9Q8LlFQ6Cc_Ju8jHS8i_GVY.png');
    background-size: cover;
    width: 20px;
    height: 20px;
    background-position:0;
    margin: 6px !important;
}

.shortcut-link{
    position: fixed;
    top: 51px;
    left: 0;
    z-index: 1100;
    width: 100%;
    height: 100vh;
}

.shortcut-link::before{
    content: "";
    display: block;
    width: 100%;
    height: 100vh;
    background-color: #000;
    opacity: .4;
    position: absolute;
    top: 0;
    left: 0;
}

.shortcut-link .shortcut-content{
    background-color: #fff;
    width: 720px;
    height: 100vh;
    position: absolute;
}
.shortcut-link .shortcut-content .close{
    margin-top: 18px;
    margin-right: 28px;
    font-size: 22px;
}
.shortcut-link .shortcut-content .shortcut-sys{
    padding: 24px;
}
.shortcut-link .shortcut-content .category-title{
    height: 39px;
    line-height: 39px;
    margin-bottom: 16px;
    font-size: 16px;
    font-weight: 500;
    color: #333333;
    border-bottom: #EBEDF0 solid 1px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.shortcut-link .shortcut-content .category-item{
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
}
.shortcut-link .shortcut-content .shortcut-header {
    margin-top: 22px;
    display: flex;
    justify-content: space-between;
}
.shortcut-link .shortcut-content .shortcut-header .label-01{
    font-size: 14px;
    font-weight: 400;
    color: #333333;
    line-height: 22px;
}

.shortcut-link .shortcut-content #link_category li{
    margin-right: 12px;
    margin-bottom: 16px;
}
.shortcut-link .shortcut-content #link_category li a{
   padding: 8px 16px;
}
.shortcut-link .shortcut-content .link-item{
    display: flex;
    width: 324px;
    height: 44px;
    float: left;
    text-decoration: none;
    padding: 0 12px;
    margin-bottom: 16px;
    border-radius: 8px;
    background-color: #F7F7F8;
    line-height: 44px;
    justify-content: space-between;
    align-items: center;
}
.shortcut-link .shortcut-content .link-item:hover, .shortcut-link .shortcut-content .link-item-active{
    background-color: #F7F7F8;
}
.shortcut-link .shortcut-content .link-item-active span{
    float: right;
}
.shortcut-link .shortcut-content .link-item:hover .link-item-title,.shortcut-link .shortcut-content .category-title .category-title-set{
    color: #4D88D2;
}
.shortcut-link .shortcut-content .link-item .link-item-title{
    /*height: 24px;*/
    /*line-height: 24px;*/
    font-size: 14px;
    font-weight: 400;
    color: #333333;
    width: 270px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.shortcut-link .shortcut-content .link-item .link-item-intro{
    color: #CCCCCC;
}
.shortcut-link .shortcut-content .shortcut-user{
    width: 720px;
    height: 336px;
    bottom: 0;
    position: absolute;
}
.shortcut-link .shortcut-content .shortcut-user .shortcut-user-header{
    height: 52px;
    line-height: 52px;
    background-color: #F7F7F8;
    box-shadow: 0 -2px 6px 0 rgba(0,0,0,0.12);
    display: flex;
    justify-content: space-between;
    padding: 0 24px 0 32px;
}
.shortcut-link .shortcut-content .shortcut-user .shortcut-user-header .shortcut-user-header-01{
    font-size: 16px;
    font-weight: 500;
    color: #333333;
}
.shortcut-link .shortcut-content .shortcut-user-header-02{
    font-size: 14px;
    font-weight: 400;
    color: #4D88D2;
}
.shortcut-link .shortcut-content .shortcut-user-header-02 a{
    text-decoration: none;
}
.shortcut-content .shortcut-user-body{
    height: 230px;
    overflow-y: auto;
}
.shortcut-content .shortcut-user-body .shortcut-user-body-01{
    padding: 24px;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
}
.shortcut-content #category_99 .link-item span{
    /* padding-top: 15px; */
}
.shortcut-content .category-icon {
    background-repeat: no-repeat;
    background-size: cover;
    display: inline-block;
    width: 18px;
    height: 18px;
    margin-right: 8px;
    vertical-align: text-top;
}
.shortcut-content .category-link .empty {
    content: "No Data";
}
.shortcut-content .category-links_category {
    background-image: url("../../base/images/links_category.png");
}
.shortcut-content .category-links_customized {
    background-image: url("../../base/images/links_customized.png");
}

@media (max-width: 768px){
    .shortcut-link .shortcut-content{
        width: 100%;
    }
}
/* tips */
.tipRelative{
    position: relative;
    display: inline-block;
    text-align: left;
}
.tipRelative .popoverCom{
    width: 220px;
    background: #5BC0DE;
    display: inline-block;
    position: absolute;
    z-index: 999;
    border-radius: 4px;
    color: #fff;
    padding: 16px;
    font-size: 12px;
    line-height: 16px;
    box-shadow: 0 6px 12px 0 rgba(0, 0, 0, .2);
}
.tipRelative .glyphicon {
    cursor: pointer;
}
.tipRelative .popoverTop:before {
    content: "";
    position: absolute;
    top: -6px;
    left:6px;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-bottom: 6px solid #5BC0DE;
}
.tipRelative .popoverRight:before {
    content: "";
    position: absolute;
    top: 6px;
    right:-6px;
    border-top:6px solid transparent;
    border-left:6px solid #5BC0DE;
    border-bottom:6px solid transparent;
}
.tipRelative .popoverLeft:before {
    content: "";
    position: absolute;
    top: 6px;
    left: -6px;
    border-right: 6px solid #5BC0DE;
    border-bottom: 6px solid transparent;
    border-top: 6px solid transparent;
}
.tipRelative.popoverBottom:before {
    content: "";
    position: absolute;
    bottom: -6px;
    left: 6px;
    border-top: 6px solid #5BC0DE;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
}
.tipRelative .popoverTitle{
    font-weight:bold;
    display:flex;
    align-items: center;
    text-align: left;
    font-size: 12px;
}
.tipRelative .popoverDesc{
    margin-top: 12px;
    word-break: break-word;
    text-align: left;
    font-weight:400;
    font-size: 12px;
    line-height:20px
}
.tipRelative .popoverBtn{
    padding: 6px 12px;
    background: #fff;
    border-radius: 4px;
    color: #333;
    float: right;
    margin-top: 12px;
    cursor: pointer;
    line-height: 1;
}
.tipRelative .popoverBtn:hover{
    color:#428bca
}