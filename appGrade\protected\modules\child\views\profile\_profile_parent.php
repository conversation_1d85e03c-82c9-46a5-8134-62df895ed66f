<?php
foreach (Yii::app()->user->getFlashes() as $key => $message) {
    echo '<div class="flash-' . $key . '">' . $message . "</div>\n";
}
?>
<style>
    .warningContent{
        color: #8a6d3b;
        background-color: #fcf8e3;
        border-color: #faebcc;
        padding: 15px;
        font-size: 12px;
        margin-bottom: 20px;
        border: 1px solid transparent;
        border-radius: 4px;
    }
</style>
<div>

    <?php
    $this->widget('zii.widgets.CMenu', array(
        'id' => 'parent-filter',
        'htmlOptions' => array('class' => 'parent-filter'),
        'items' => array(
            array('label' => Yii::t("userinfo", 'Father\'s Profile'), 'url' => array('//child/profile/profile', "t" => "parent", "p" => "f"), 'itemOptions' => array("class" => $extra['class']["f"])),
            array('label' => Yii::t("userinfo", 'Mother\'s Profile'), 'url' => array('//child/profile/profile', "t" => "parent", "p" => "m"), 'itemOptions' => array("class" => $extra['class']["m"])),
        ),
    ));

    $canEdit = Yii::app()->user->checkAccess('editProfile', array("childid" => $this->getChildId()));
    ?>


    <div class="form profile_bg" id="user-<?php echo $model->uid; ?>">
        <?php
        $form = $this->beginWidget('CActiveForm', array(
            'id' => 'child-form',
            'enableClientValidation' => true,
            'clientOptions' => array(
                'validateOnSubmit' => true,
            ),
            'htmlOptions' => array('enctype' => 'multipart/form-data'),
                ));
        ?>

        <?php echo CHtml::errorSummary($model); ?>

        <div class="warningContent">
            <?php
            if ($this->getChildInfo()['schoolid'] == 'BJ_SLT') {
                echo Yii::t("labels", 'If any of the information above is inaccurate, <NAME_EMAIL> to update them.');
            }
            else {
                echo Yii::t("labels", 'If any of the information above is inaccurate, <NAME_EMAIL> to update them.');
            }
            ?>
        </div>

        <!--	家长已经有帐号; 修改EMAIL或密码		-->
        <?php if (!$model->isNewRecord): ?>

            <script>
                var toChangeEmail = false;
                function clearEmailChange(){
                    $('#User_changeEmail').val('');
                }
            </script>

            <!-- <dl class="row">
                <dt>
                <?php echo CHtml::activeLabelEx($model, 'email'); ?>
                </dt>
                <dd>
                    <?php //echo CHtml::encode($model->email); ?>
                    <?php echo CHtml::activeTextField($model, 'email', array("class" => "readonly", "size" => 32)) ?>
                    <?php
                    // echo CHtml::link(Yii::t("userinfo", "Change Email?"), "javascript:;", array(
                    //     "onClick" => "clearEmailChange();$('#change-email').toggle();toChangeEmail=!toChangeEmail;"
                    // ));
                    ?>
                </dd>
            </dl> -->

            <!-- <dl class="row" id="change-email" class="row prepend-1" style='display:<?php echo($model->changeEmail == "") ? "none" : "block"; ?>;'>
                <dt>
                <?php echo CHtml::activeLabelEx($model, 'changeEmail'); ?>
                </dt>
                <dd>
                    <?php echo CHtml::activeTextField($model, 'changeEmail', array("size" => 32)) ?>
                    <?php echo CHtml::error($model, 'changeEmail'); ?>
                    <p class="hint">
                        <?php echo Yii::t("userinfo", "Leave blank if no change."); ?>
                    </p>
                </dd>
            </dl> -->


            <!--	隐藏保存的密码，保证初始密码框都是空白		-->
            <!-- <div style="display:none">
                <input name="fakepassword" id="fakepassword" type="password" />
            </div>

            <dl class="row">
                <dt>
                <?php echo CHtml::activeLabelEx($model, 'changePassword'); ?>
                </dt>
                <dd>
                    <?php echo CHtml::activePasswordField($model, 'changePassword'); ?>
                    <?php echo CHtml::error($model, 'changePassword'); ?>
                    <p class="hint" id="hint_pass1">
                        <?php echo Yii::t("userinfo", "Leave blank if no change."); ?>
                        <span>
                            <?php echo Yii::t("user", "Combination of letters and numbers, min 8-length."); ?>
                        </span>
                    </p>

                </dd>
            </dl> -->


            <!--	有账号的用户给无账号的另一家长建立帐号		-->
        <?php else: ?>
            <!-- <div id="no-account-warning" class="flash-warning">
                <?php echo addColon(Yii::t("userinfo", "An account has not been created yet.  To create an account, please enter the following information")); ?>
            </div>

            <dl id="change-email" class="row">
                <dt>
                <?php echo CHtml::activeLabelEx($model, 'email'); ?>
                </dt>
                <dd>
                    <?php echo CHtml::activeTextField($model, 'email', array("size" => 32)) ?>
                    <?php echo CHtml::error($model, 'email'); ?>
                </dd>
            </dl> -->

            <!--	隐藏保存的密码，保证初始密码框都是空白		-->
            <!-- <div style="display:none">
                <input name="fakepassword" id="fakepassword" type="password" />
            </div>

            <dl id="change-email" class="row">
                <dt>
                <?php echo CHtml::activeLabelEx($model, 'pass'); ?>
                </dt>
                <dd>
                    <?php echo CHtml::activePasswordField($model, 'changePassword'); ?>
                    <?php echo CHtml::error($model, 'changePassword'); ?>
                    <p class="hint" id="hint_pass1">
                        <span>
                            <?php echo Yii::t("user", "Combination of letters and numbers, min 8-length."); ?>
                        </span>
                    </p>
                </dd>
            </dl> -->


        <?php endif; ?>


        <!-- <dl class="row">
            <dt>
            <?php echo CHtml::activeLabelEx($model, 'verifyPassword'); ?>
            </dt>
            <dd>
                <?php echo CHtml::activePasswordField($model, 'verifyPassword'); ?>
                <p class="hint hint-error" id="hint_pass2">
                    <span class="hide">
                        <?php echo Yii::t("user", "Password do not match."); ?>
                    </span>
                </p>
                <?php echo CHtml::error($model, 'verifyPassword'); ?>
            </dd>
        </dl> -->


        <dl class="row">
            <dt>
            <?php
            echo CHtml::image($model->getPhotoSubUrl(), "", array("class" => "thumb-small"));
            ?>
            </dt>
            <dd>&nbsp;
            </dd>
        </dl>


        <dl class="row">
            <dt>
            <?php echo CHtml::activeLabelEx($model, 'user_avatar'); ?>
            </dt>
            <dd>
                <?php echo CHtml::activeFileField($model, 'uploadPhoto', array("disabled" => "disabled")) ?>
                <?php echo CHtml::error($model, 'uploadPhoto'); ?>
            </dd>
        </dl>


        <dl class="row">
            <dt>
            <?php echo CHtml::activeLabelEx($model->parent, 'cn_name'); ?>
            </dt>
            <dd>
                <?php echo CHtml::activeTextField($model->parent, 'cn_name', array("disabled" => "disabled")) ?>
                <?php echo CHtml::error($model->parent, 'cn_name'); ?>
            </dd>
        </dl>


        <dl class="row">
            <dt>
            <?php echo CHtml::activeLabelEx($model->parent, 'en_firstname'); ?>
            </dt>
            <dd>
                <?php echo CHtml::activeTextField($model->parent, 'en_firstname', array("disabled" => "disabled")) ?>
                <?php echo CHtml::error($model->parent, 'en_firstname'); ?>
            </dd>
        </dl>


        <dl class="row">
            <dt>
            <?php echo CHtml::activeLabelEx($model->parent, 'en_lastname'); ?>
            </dt>
            <dd>
                <?php echo CHtml::activeTextField($model->parent, 'en_lastname', array("disabled" => "disabled")) ?>
                <?php echo CHtml::error($model->parent, 'en_lastname'); ?>
            </dd>
        </dl>


        <dl class="row">
            <dt>
            <?php echo CHtml::activeLabelEx($model->parent, 'gender'); ?>
            </dt>
            <dd>
                <?php echo CHtml::activeDropDownList($model->parent, 'gender', $cfgs["gender"], array("disabled" => "disabled")); ?>
                <?php echo CHtml::error($model->parent, 'gender'); ?>
            </dd>
        </dl>


        <dl class="row">
            <dt>
            <?php echo CHtml::activeLabelEx($model->parent, 'country'); ?>
            </dt>
            <dd>
                <?php echo CHtml::activeDropDownList($model->parent, 'country', Country::model()->getData(), array('empty' => Yii::t("global", 'Please Select'), "disabled" => "disabled")) ?>
                <?php echo CHtml::error($model->parent, 'country'); ?>
            </dd>
        </dl>



        <dl class="row">
            <dt>
            <?php echo CHtml::activeLabelEx($model->parent, 'company'); ?>
            </dt>
            <dd>
                <?php
                if ($canEdit)
                    echo CHtml::activeTextField($model->parent, 'company', array("size" => 64, "disabled" => "disabled"));
                else
                    echo CHtml::textField('company', '***', array("class" => "readonly"));
                ?>
                <?php echo CHtml::error($model->parent, 'company'); ?>
            </dd>
        </dl>


        <dl class="row">
            <dt>
            <?php echo CHtml::activeLabelEx($model->parent, 'job'); ?>
            </dt>
            <dd>
                <?php
                if ($canEdit)
                    echo CHtml::activeTextField($model->parent, 'job', array("size" => 64, "disabled" => "disabled"));
                else
                    echo CHtml::textField('job', '***', array("class" => "readonly"));
                ?>
                <?php echo CHtml::error($model->parent, 'job'); ?>
            </dd>
        </dl>


        <dl class="row">
            <dt>
            <?php echo CHtml::activeLabelEx($model->parent, 'tel'); ?>
            </dt>
            <dd>
                <?php echo CHtml::activeTextField($model->parent, 'tel', array("disabled" => "disabled")) ?>
                <?php echo CHtml::error($model->parent, 'tel'); ?>
            </dd>
        </dl>



        <dl class="row">
            <dt>
            <?php echo CHtml::activeLabelEx($model->parent, 'mphone'); ?>
            </dt>
            <dd>
                <?php
                if ($canEdit)
                    echo CHtml::activeTextField($model->parent, 'mphone', array("disabled" => "disabled"));
                else
                    echo CHtml::textField('mphone', '***', array("class" => "readonly"));
                ?>
                <?php echo CHtml::error($model->parent, 'mphone'); ?>
            </dd>
        </dl>


        <dl class="row">
            <dt>
            <?php echo CHtml::activeLabelEx($model->parent, 'fax'); ?>
            </dt>
            <dd>
                <?php echo CHtml::activeTextField($model->parent, 'fax', array("disabled" => "disabled")) ?>
                <?php echo CHtml::error($model->parent, 'fax'); ?>
            </dd>
        </dl>


        <dl class="row submit">
            <dt>
            </dt>
            <dd>
                <?php
            //    if ($canEdit) {
            //        echo CHtml::submitButton(Yii::t("global", "Save"), array('id' => 'submit_profile', "class" => "w100 bigger", "onclick" => "if(!toChangeEmail){clearEmailChange();};return true;"));
            //    } else {
            //        echo CHtml::submitButton(Yii::t("global", "Save"), array("class" => "w100 bigger", "disabled" => "disabled"));
            //    }
                ?>
            </dd>
        </dl>


        <?php $this->endWidget(); ?>
    </div><!-- form -->
</div>


<div class="clear"></div>


<script lang="text/javascript">

    $(document).ready(function() {

        var pass1 = $('#User_changePassword'),
        pass2 = $('#User_verifyPassword'),
        submit = $('#submit_profile');

        $('.form input:password').val('');
        pass1.keyup(function(){

            var showError = false;
            var pv = pass1.val();
            if(pv.length >7){
                var num = new RegExp(/.*[0-9]+/).test(pv) ? true : false;
                var let = new RegExp(/.*[a-zA-Z]+/).test(pv) ? true : false;
                if(num == true && let == true){
                    pass2.removeAttr('disabled');
                    $('#hint_pass1').removeClass('hint-error');
                }else{
                    showError = true;
                }
            }else{
                showError = true;
                if(pv.length == 0){
                    pass2.val('');
                    submit.removeAttr('disabled');
                    showError = false;
                }
            }
            if(showError == true){
                pass2.attr('disabled','true');
                $('#hint_pass1').addClass('hint-error');
                submit.attr('disabled','true');
            }

        });

        pass2.keyup(function(){
            if(pass2.val() == pass1.val()){
                submit.removeAttr('disabled');
                $('#hint_pass2 span').addClass('hide');
            }
            else{
                submit.attr('disabled','true');
                $('#hint_pass2 span').removeClass('hide');
            }
        });


    });


</script>


