<div style="font-family: 'Microsoft Yahei'">
    <p>孩子名单：<?php echo date("Y-m-d H:i:s", $beginLastweek) . ' 至 ' . date("Y-m-d H:i:s", $endLastweek) ?></p>
		<table border='1' cellpadding='2' cellspacing='0' width="100%">
			<tr>
				<th width="100" style="font-family: 'Microsoft Yahei';font-size:12px;">学生姓名</th>
				<th width="100" style="font-family: 'Microsoft Yahei';font-size:12px;">转出学校</th>
				<th width="100" style="font-family: 'Microsoft Yahei';font-size:12px;">转入学校</th>
				<th width="100" style="font-family: 'Microsoft Yahei';font-size:12px;">个人账户余额</th>
				<th width="100" style="font-family: 'Microsoft Yahei';font-size:12px;">转校时间</th>
			</tr>
			<?php
			foreach($data as $info):
			?>
			<tr>
				<td style="font-family: 'Microsoft Yahei';font-size:12px;" align="center">
                    <?php echo $info['childName']?>
				</td>
				<td style="font-family: 'Microsoft Yahei';font-size:12px;" align="center">
                    <?php echo $sModel[$info['from_schoolid']]->title;?>
				</td>
				<td style="font-family: 'Microsoft Yahei';font-size:12px;" align="center" >
                    <?php echo $sModel[$info['to_schoolid']]->title; ?>
				</td>
				<td style="font-family: 'Microsoft Yahei';font-size:12px;" align="right" ;>
                    <?php echo $info['balance']?>
				</td>
				<td style="font-family: 'Microsoft Yahei';font-size:12px;" align="right" >
                    <?php echo $info['transfer_time']?>
				</td>
			</tr>
			<?php endforeach;?>
		</table>
		<br>
	<p>Auto Email,please do not reply.<br>
	系统自动邮件，请不要回复。</p>
</div>
