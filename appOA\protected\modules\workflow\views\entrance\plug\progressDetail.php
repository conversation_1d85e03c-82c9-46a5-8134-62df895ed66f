<style>
    .approval-item {
        padding:5px 0px 5px 20px;
        position: relative;
        /* border-bottom: 1px solid #eee; */
    }

    .approval-status {
        display: inline-block;
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 12px;
    }

    .status-submit {
        background: #28a745;
        color: white;
    }

    .status-reject {
        background: #dc3545;
        color: white;
    }

    .status-pass {
        background: #28a745;
        color: white;
    }

    .status-pending {
        background: #17a2b8;
        color: white;
    }
    .approval-content {
        margin: 10px 0;
        padding:8px 10px;
        background: #f8f9fa;
        border-radius: 4px;
    }

    /* 进度条样式 */
    .timeline {
        position: absolute;
        left: 4px;
        top: 0;
        bottom: -10px;
        width:1px;
        background: #D9D9D9;
    }

    .timeline-dot {
        position: absolute;
        left: 0px;
        top: 10px;
        width: 9px;
        height: 9px;
        border-radius: 50%;
        background:#4D88D2;
        z-index: 1;
    }

    .timeline-dot.active {
        background: #28a745;
    }

    .timeline-dot.reject {
        background: #dc3545;
    }

    .timeline-dot.pending {
        background: #17a2b8;
    }

    /* 第一个时间线节点上方不需要显示线条 */
    .approval-item:first-child .timeline {
        top: 10px;
    }

    /* 最后一个时间线节点下方不需要显示线条 */
    .approval-item:last-child .timeline {
        bottom: auto;
        height: 0;
    }
    .greenColor{
        color: #5CB85C;
    }
    .redColor{
        color:#D9534F
    }
    .blueColor{
        color:#4D88D2
    }
    .me{
        background: #F2F3F5;
        border-radius: 2px;
        padding:4px;
        font-size:12px
    }
    .textareas::-webkit-input-placeholder { /* Chrome/Opera/Safari */
        color: #ccc;
    }
    .labelTag{
        margin-left: 8px;
        display: inline-block;
        padding: 2px 6px;
        background: rgba(77, 136, 210, .1);
        color:#4D88D2 !important;
        border-radius: 2px;
        cursor: pointer;
    }
    .labelTag:hover{
        background:#4D88D2;
        color:#fff !important;
    }
    .tooltipCss {
        position: relative;
        display: inline-block;
    }
    
    .tooltipCss .tooltiptext {
        visibility: hidden;
        width: 70px;
        background-color: black;
        color: #fff;
        text-align: center;
        border-radius: 4px;
        padding: 5px 0;
        position: absolute;
        z-index: 1;
        bottom: 150%;
        left: 50%;
        margin-left: -35px;
    }
    
    .tooltipCss .tooltiptext::after {
        content: "";
        position: absolute;
        top: 100%;
        left: 50%;
        margin-left: -5px;
        border-width: 5px;
        border-style: solid;
        border-color: black transparent transparent transparent;
    }
    
    .tooltipCss:hover .tooltiptext {
        visibility: visible;
    }
    @media print {
    .timeline {
        background-color: #D9D9D9 !important;
        -webkit-print-color-adjust: exact; /* 确保打印背景颜色 */
    }
    .timeline-dot {
        background-color: #4D88D2 !important;
        -webkit-print-color-adjust: exact; /* 确保打印背景颜色 */
        border-color: #fff !important; /* 确保打印边框颜色 */
    }
    .greenColor{
        color: #5CB85C !important;
    }
    .redColor{
        color:#D9534F !important;
    }
    .blueColor{
        color:#4D88D2 !important;
    }
    .approval-content {
        -webkit-print-color-adjust: exact; /* 确保打印背景颜色 */
        background-color: #f8f9fa !important;
    }
}

</style>

<h3 class='font14 color3 fontBold mt24'><?php echo Yii::t('workflow', 'Comments:'); ?></h3>
<div class="card-body p0">
    <?php
    $currentUserName = Yii::app()->user->getName();
    $processList = $data['workflow']->getWorkflowProcess();
    $processLabelList = array(
        WorkflowOperation::WORKFLOW_STATS_STEP => '退回修改',
        WorkflowOperation::WORKFLOW_STATS_RESET => '退回修改',
        WorkflowOperation::WORKFLOW_STATS_UNOP =>  '通过',
        WorkflowOperation::WORKFLOW_STATS_OPDENY =>  '拒绝并作废',
        WorkflowOperation::WORKFLOW_STATS_OPED =>  '通过',
    );
    foreach ($processList as $process) :
        $processIcon = "<span class='glyphicon glyphicon-ok-sign greenColor font12' aria-hidden='true'></span>";
        $processLabelText = $processLabelList[$process->state];
        
        if ($data['workflow']->nodeIds[0] == $process->current_node_index) {
            $processLabelText = '提交';
        }
        $processLabelTag = "<span class='greenColor'>$processLabelText</span>";
        
        if (in_array($process->state, array(WorkflowOperation::WORKFLOW_STATS_STEP,  WorkflowOperation::WORKFLOW_STATS_RESET, WorkflowOperation::WORKFLOW_STATS_OPDENY))) {
            $processIcon = "<span class='glyphicon glyphicon-exclamation-sign redColor font12' aria-hidden='true'></span>";
            $processLabelTag = "<span class='redColor'>$processLabelText</span>";
        }
        $username = $process->user->getName();
        $progressDate = date("Y-m-d H:i:s", $process->start_time);
        $progressContent = nl2br(htmlspecialchars($process->process_desc));
    ?>
        <div class="approval-item">
            <div class="timeline"></div>
            <div class="timeline-dot"></div>
            <div class="d-flex justify-content-between">
                <div class='font14'>
                    <span class='color3 mr16'><?= $username; ?></span>
                    <?= $processIcon; ?>
                    <?= $processLabelTag; ?>
                    <span class="color6 pull-right font12"><?= $progressDate; ?></span>
                </div>
            </div>
            <div class="approval-content">
                <?= $progressContent; ?>
            </div>
        </div>
    <?php endforeach; ?>
    <!-- 显示审批框 -->
    <?php if ($data['workflow']->useRole == 1 && $data['workflow']->getOperateStatus() == WorkflowOperation::WORKFLOW_STATS_UNOP && !$data['workflow']->isFristNode()) : ?>
        <div class="approval-item">
            <div class="timeline"></div>
            <div class="timeline-dot"></div>
            <div class="d-flex justify-content-between">
                <div class='mb10 font14'>
                    <span class='color3 mr4'><?= $currentUserName; ?></span>
                    <span class='me mr16'>我</span>
                    <span class="glyphicon glyphicon-question-sign blueColor font12" aria-hidden="true"></span>
                    <span class="blueColor">待审批</span>
                </div>
                <div>
                    <span class="font12 color6"><?= Yii::t('workflow', 'Option:'); ?></span>
                    
                    <span class="tooltipCss labelTag font12" onclick='inputText("同意")'>同意
                        <span class="tooltiptext">快速输入</span>
                    </span>
                    <span class="tooltipCss labelTag font12" onclick='inputText("请修改")'>请修改
                        <span class="tooltiptext">快速输入</span>
                    </span>
                    <span class="tooltipCss labelTag font12" onclick='inputText("Approved")'>Approved
                        <span class="tooltiptext">快速输入</span>
                    </span>
                </div>
            </div>
            <div class="mt8">
                <textarea name="memo" id='myTextArea' class="form-control textareas" rows="3" placeholder="请输入"></textarea>
            </div>
        </div>
    <?php endif; ?>
</div>
<script>
$(function () {
    $('[data-toggle="tooltip"]').tooltip()
})
function inputText(text) {
    var textarea = document.getElementById('myTextArea');
    textarea.value = text;
}
</script>