<?php

/**
 * This is the model class for table "ivy_reg_extra_info".
 *
 * The followings are the available columns in table 'ivy_reg_extra_info':
 * @property integer $id
 * @property integer $childid
 * @property integer $schoolid
 * @property string $openid
 * @property integer $parent_id
 * @property string $parent_phone
 * @property integer $step
 * @property string $data
 * @property integer $status
 * @property integer $updated
 * @property integer $type
 * @property integer $complete
 */
class RegExtraInfo extends CActiveRecord
{
	const PROCESS_BUS = 1;
	const PROCESS_CARD = 2;
	const PROCESS_UNIFORM = 3;
	const PROCESS_MEDICAL= 4;
	const PROCESS_LUNCH= 5;
	const PROCESS_LANG= 6;
	const PROCESS_FEE= 7;
	const PROCESS_SAFE= 8;
	const PROCESS_FAMILYID= 9;
	const PROCESS_INFORMATION= 10;
	const PROCESS_MATERIAL= 11;

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_reg_extra_info';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('childid, schoolid, parent_id, step, data, status, updated, complete', 'required'),
			array('childid, parent_id, step, status, updated', 'numerical', 'integerOnly'=>true),
			array('openid, schoolid, type', 'length', 'max'=>255),
			array('parent_phone', 'length', 'max'=>11),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, childid, schoolid, type, openid, parent_id, parent_phone, step, data, status, updated, reject_memo', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
			'student' => array(self::HAS_ONE, 'RegStudents', array('childid'=>'childid', 'reg_id'=>'reg_id')),
            'childProfile'=>array(self::HAS_ONE, 'ChildProfileBasic', array('childid'=>'childid')),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'childid' => 'Childid',
			'schoolid' => 'Schoolid',
			'openid' => 'Openid',
			'parent_id' => 'Parent',
			'parent_phone' => 'Parent Phone',
			'step' => 'Step',
			'data' => 'Data',
			'status' => 'Status',
			'updated' => 'Updated',
			'type' => 'Type',
			'reject_memo' => 'Reject Memo',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('childid',$this->childid);
		$criteria->compare('schoolid',$this->schoolid);
		$criteria->compare('openid',$this->openid,true);
		$criteria->compare('parent_id',$this->parent_id);
		$criteria->compare('parent_phone',$this->parent_phone,true);
		$criteria->compare('step',$this->step);
		$criteria->compare('data',$this->data,true);
		$criteria->compare('status',$this->status);
		$criteria->compare('updated',$this->updated);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return RegExtraInfo the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	public function checkStatus()
	{
		if ($this->complete == 1) {
			$this->addError($this->complete, Yii::t('reg', 'The step has been reviewed'));
			return false;
		}

		return true;
	}

	// 查找孩子正在进行的步骤
	public static function getChildReginfo($regid, $childid)
	{
		$criteria = new CDbCriteria();
		$criteria->compare('reg_id', $regid);
		$criteria->compare('childid', $childid);
		$criteria->compare('status', 1);
		$regInfo = RegExtraInfo::model()->findAll($criteria);
		return $regInfo;
	}

	public static function getStepConfig()
	{
		$all = array(
			self::PROCESS_BUS => Yii::t('reg', 'School Bus Registration'),
			self::PROCESS_CARD => Yii::t('reg', 'Student Pick-up Cards'),
			self::PROCESS_UNIFORM => Yii::t('reg', 'School Uniform Ordering'),
			self::PROCESS_MEDICAL => Yii::t('reg', 'Medical Information'),
			self::PROCESS_LUNCH => Yii::t('reg', 'Lunch Program'),
			self::PROCESS_LANG => Yii::t('reg', 'Home Language Survey'),
			self::PROCESS_FEE => Yii::t('reg', 'Student Enrollment Agreement'),
			self::PROCESS_SAFE => Yii::t('reg', 'Safety protocol'),
			self::PROCESS_FAMILYID => Yii::t('reg', 'Student Family Document'),
			self::PROCESS_INFORMATION => Yii::t('reg', 'New Student Information Collecting'),
			self::PROCESS_MATERIAL => Yii::t('reg', 'Material Fee Payment'),
		);
		return $all;
	}

	public static function getStepsBySchool($schoolid)
	{
		$config = array(
			'BJ_DS' => array(self::PROCESS_BUS, self::PROCESS_CARD, self::PROCESS_UNIFORM, self::PROCESS_MEDICAL ,self::PROCESS_LUNCH ,self::PROCESS_LANG, self::PROCESS_FEE),
			'BJ_SLT' => array(self::PROCESS_BUS, self::PROCESS_CARD, self::PROCESS_UNIFORM, self::PROCESS_MEDICAL ,self::PROCESS_LUNCH ,self::PROCESS_LANG, self::PROCESS_FEE),
			'BJ_IASLT' => array(self::PROCESS_BUS, self::PROCESS_CARD, self::PROCESS_FAMILYID, self::PROCESS_INFORMATION, self::PROCESS_SAFE, self::PROCESS_MATERIAL),
			'BJ_QF' => array(self::PROCESS_BUS, self::PROCESS_CARD, self::PROCESS_UNIFORM, self::PROCESS_MEDICAL ,self::PROCESS_LUNCH ,self::PROCESS_LANG, self::PROCESS_FEE),
			'BJ_QFF' => array(self::PROCESS_BUS, self::PROCESS_CARD, self::PROCESS_UNIFORM, self::PROCESS_MEDICAL ,self::PROCESS_LUNCH ,self::PROCESS_LANG, self::PROCESS_FEE),
		);
		return isset($config[$schoolid]) ? $config[$schoolid] : array() ;
	}

	public static function completeConfig()
	{
		return array(
			'-1' => Yii::t('reg', 'Rejected'),
			'0' => Yii::t('reg', 'Waiting to Confirm'),
			'1' => Yii::t('reg', 'Confirmed'),
			'2' => Yii::t('reg', 'Re-submitted'),
		);
	}

    public static function getBusStyle()
    {
        $all = array(
            1 => Yii::t('reg', 'Two-way journey'),
            2 => Yii::t('reg', 'One-way journey TO SCHOOL'),
            3 => Yii::t('reg', 'One-way journey BACK HOME'),
        );
        return $all;
    }

    public function getOssImageUrl($fileName)
    {
        $fileName = 'regextrainfo/' . $fileName;
        // 获取文件临时地址
        $osscs = CommonUtils::initOSSCS('private');

        $object = $fileName;
        //$style = 'style/w200';
        //$imageUrl = $osscs->getImageUrl($object, $style);
        $imageUrl = $osscs->getImageUrl($object);
        return $imageUrl;
    }

    public function getThumbOssImageUrl($fileName)
    {
        $fileName = 'regextrainfo/' . $fileName;
        // 获取文件临时地址
        $osscs = CommonUtils::initOSSCS('private');

        $object = $fileName;
        $style = 'style/w200' ;
        $imageUrl = $osscs->getImageUrl($object, $style);
        return $imageUrl;
    }

    public static function getPrint()
    {
        $all = array(
            self::PROCESS_BUS => 'printBus',
            self::PROCESS_CARD => 'printCard',
            self::PROCESS_UNIFORM => 'printUniform',
            self::PROCESS_MEDICAL => 'printMedical',
            self::PROCESS_LUNCH => 'printLunch',
            self::PROCESS_LANG => 'printLang',
            self::PROCESS_FEE => 'printFee',
            self::PROCESS_SAFE => 'printSafe',
            self::PROCESS_INFORMATION => 'printinformation',
        );
        return $all;
    }

    public static function getPrintData()
    {
        $all = array(
            self::PROCESS_BUS => 'printBusData',
            self::PROCESS_CARD => 'printCardData',
            self::PROCESS_UNIFORM => 'printUniformData',
            self::PROCESS_MEDICAL => 'printMedicalData',
            self::PROCESS_LUNCH => 'printLunchData',
            self::PROCESS_LANG => 'printLangData',
            self::PROCESS_FEE => 'printFeeData',
            self::PROCESS_SAFE => 'printSafeData',
        );
        return $all;
    }

    // 查看审核详情（历史记录）
    public function showDetail($model)
    {
        $data = array();
        $criteria = new CDbCriteria();
        $criteria->compare('step', $model->step);
        $criteria->compare('schoolid', $model->schoolid);
        $criteria->compare('reg_id', $model->reg_id);
        $criteria->compare('status', 0);
        $criteria->compare('complete', '-1');
        $criteria->compare('childid', $model->childid);
        $criteria->order = 'updated DESC';
        $infoModel = RegExtraInfo::model()->findAll($criteria);

        if($infoModel) {
            foreach ($infoModel as $infoKey => $val) {
                if($model->step == RegExtraInfo::PROCESS_BUS){
                    $busData = json_decode($val->data);
                    $data[] = array(
                        'needBus' => $busData->needBus,
                        'journey' => $busData->journey,
                        'parking' => $busData->parking,
                        'customize' => $busData->customize,
                        'filedata' => RegExtraInfo::getOssImageUrl($busData->filedata),
                    );
                }
                if($model->step == RegExtraInfo::PROCESS_CARD){
                    $cardData = json_decode($val->data);
                    foreach ($cardData as $k=>$item){
                        if($item->photo) {
                            $data[$infoKey][$k] = array(
                                'name' => $item->name,
                                'relation' => $item->relation,
                                'tel' => $item->tel,
                                'photo' => RegExtraInfo::getThumbOssImageUrl($item->photo),
                            );
                        }
                    }
                }
                if($model->step == RegExtraInfo::PROCESS_UNIFORM){
                    $uniformData = json_decode($val->data);
                    $data[$infoKey] = array(
                        'gender' => $uniformData->gender,
                        'size' => $uniformData->size,
                    );
                }
                if($model->step == RegExtraInfo::PROCESS_MEDICAL){
                    $medicalData = json_decode($val->data);
                    $medicalReport = array();
                    if(!empty($medicalData->medicalReport)){
                        foreach ($medicalData->medicalReport as $val2){
                            $medicalReport[] = RegExtraInfo::getOssImageUrl($val2);
                        }
                    }

                    $vaccineReport = array();
                    if(!empty($medicalData->vaccineReport)){
                        foreach ($medicalData->vaccineReport as $val2){
                            $vaccineReport[] = RegExtraInfo::getOssImageUrl($val2);
                        }
                    }

                    $healthReport = array();
                    if(!empty($medicalData->healthReport)){
                        foreach ($medicalData->healthReport as $val2){
                            $healthReport[] = RegExtraInfo::getOssImageUrl($val2);
                        }
                    }

                    $filedata = '';
                    if(!empty($medicalData->healthReport)){
                        $filedata = RegExtraInfo::getOssImageUrl($medicalData->filedata);
                    }


                    $data[$infoKey] = array(
                        'preferredHospitalOne' => $medicalData->preferredHospitalOne,
                        'preferredHospitalTwo' => $medicalData->preferredHospitalTwo,
                        'insuranceCompany' => $medicalData->insuranceCompany,
                        'oneEmergencyName' => $medicalData->oneEmergencyName,
                        'oneEmergencyPhone' => $medicalData->oneEmergencyPhone,
                        'twoEmergencyName' => $medicalData->twoEmergencyName,
                        'twoEmergencyPhone' => $medicalData->twoEmergencyPhone,
                        'ADHD' => $medicalData->ADHD,
                        'heartDisorder' => $medicalData->heartDisorder,
                        'allergies' => $medicalData->allergies,
                        'frequent' => $medicalData->frequent,
                        'asthma' => $medicalData->asthma,
                        'hepatitis' => $medicalData->hepatitis,
                        'problems' => $medicalData->problems,
                        'gastrointertianl' => $medicalData->gastrointertianl,
                        'fractures' => $medicalData->fractures,
                        'skinProblems' => $medicalData->skinProblems,
                        'diabetes' => $medicalData->diabetes,
                        'visionProblems' => $medicalData->visionProblems,
                        'seizureDisorde' => $medicalData->seizureDisorde,
                        'tuberculosis' => $medicalData->tuberculosis,
                        'specialFood' => $medicalData->specialFood,
                        'other' => $medicalData->other,
                        'filedata' => $filedata,
                        'medicalReport' => $medicalReport,
                        'vaccineReport' => $vaccineReport,
                        'healthReport' => $healthReport,
                    );
                }
                if($model->step == RegExtraInfo::PROCESS_LUNCH){
                    $lunchData = json_decode($val->data);
                    $data[$infoKey]['lunch'] = $lunchData->lunch;
                    $data[$infoKey]['lunchSort'] = $lunchData->lunchSort;
                    $data[$infoKey]['allergen'] = $lunchData->allergen;
                }
                if($model->step == RegExtraInfo::PROCESS_LANG){
                    $langData = json_decode($val->data);
                    $data[$infoKey] = array(
                        'firstLanguage' => $langData->firstLanguage,
                        'familyLanguage' => $langData->familyLanguage,
                        'commonlyLanguage' => $langData->commonlyLanguage,
                    );
                }
                if($model->step == RegExtraInfo::PROCESS_FEE){
                    $feeData = json_decode($val->data);
                    $data[$infoKey]['imageUrl'] =  RegExtraInfo::getOssImageUrl($feeData->filedata);
                }
                if($model->step == RegExtraInfo::PROCESS_SAFE){
                    $safeData = json_decode($val->data);
                    $data[$infoKey]['imageUrl'] =  RegExtraInfo::getOssImageUrl($safeData->filedata);
                }
                if($model->step == RegExtraInfo::PROCESS_FAMILYID){
                    $safeData = json_decode($val->data);
                    foreach ($safeData as $key=>$parent){
                        if($parent){
                            foreach ($parent as $item){
                                $data[$infoKey][$key][] = RegExtraInfo::getOssImageUrl($item);
                            }
                        }
                    }
                    //$data[$infoKey]['imageUrl'] =  RegExtraInfo::getOssImageUrl($safeData->filedata);
                }
                if($model->step == RegExtraInfo::PROCESS_INFORMATION){
                    $safeData = json_decode($val->data);
                    $data[$infoKey] = array(
                        'allergy' => $safeData->allergy,
                        'medicalHistory' => $safeData->medicalHistory,
                        'personName1' => $safeData->personName1,
                        'relationship1' => $safeData->relationship1,
                        'contactNumber1' => $safeData->contactNumber1,
                        'personName2' => $safeData->personName2,
                        'relationship2' => $safeData->relationship2,
                        'contactNumber2' => $safeData->contactNumber2,
                        'hospital' => $safeData->hospital,
                        'filedata' => RegExtraInfo::getOssImageUrl($safeData->filedata),
                    );
                }

                if($model->step == RegExtraInfo::PROCESS_MATERIAL){
                    $safeData = json_decode($val->data);
                    $data[$infoKey]['is_payment'] = $safeData->is_payment;
                }
                $data[$infoKey]['reject_memo'] = $val->reject_memo;
            }
        }

        return $data;
    }

    // 发送驳回通知
    public function showMessageOverrule($childid,$schoolid,$openid,$step)
    {
        $url = 'https://api.weixin.qq.com/cgi-bin/message/template/send?access_token=' . $this->accessToken;
        // 微信模板消息内容

        if ($schoolid == 50) {
            $appid = 'wxb1a42b81111e29f3';
            $state = 'ds';
            $template_id = 'cIxgkgrInnyY5hU_2a4on0EujsOST7c4Gymxov5Xe9A';
            $ceshiOpenid = 'oHBKPwQUsreFHcZkzsq0CdW7VqEQ';
        } else {
            $appid = 'wx903fba9d4709cf10';
            $state = 'ivy';
            $template_id = 'DDaV7nfT7FxBzk8TwGrWx1urvKymTqm2MYWzxMSiCZg';
            $ceshiOpenid = 'ouwmTjiT6aIh_TLd6Kldr2Cx4tCs';
        }

        $redirectUrl = "http://www.ivyonline.cn/" . $childid . "/wechat/regExtraInfo/complete";
        $touser = (OA::isProduction()) ? $openid : $ceshiOpenid;

        $data = array(
            'touser' => $touser,
            'template_id' => $template_id,
            'url' => 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' . $appid . '&redirect_uri=' . $redirectUrl . '&response_type=code&scope=snsapi_base&state=' . $state . '#wechat_redirect',
            'data' => array(
                'first' => array('value' => '第' . $step  .'步注册信息未达到审核要求，需重新提交。step ' . $step  .' registration is incomplete or incorrect', 'color' => '#ff6726'),
                'keyword1' => array('value' => '需家长重新提交 Need Resubmit'),
                'keyword2' => array('value' => date("Y-m-d H:i", time())),
                'remark' => array('value' => '请点击“详情”进行修改并重新提交 Please click on "Details" to modify and resubmit again.'),
            ),
        );
        $jsonData = json_encode($data);
        $res = CommonUtils::httpGet($url, $jsonData);
        $jsonRes = CJSON::decode($res);

        Yii::log($state . '=>' . $childid . '=>' . $openid . '=>' . $jsonRes['errcode'], CLogger::LEVEL_INFO, 'wechatReg.Overrule');
        return $jsonRes['errcode'];
    }
}
