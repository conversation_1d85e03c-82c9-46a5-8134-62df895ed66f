<div class="container" id="container">
    <div class="cell">
        <div class="hd">
            <h1 class="page_title"><?php echo Yii::t('wechat', 'ID QRcode'); ?></h1>
        </div>
        <div class="bd">
            <p class="twodimensioncode_box" style="padding: 50px 0">
                <?php
                if ($model->code) {
                    $code = $model->code;
                    $filename = 'checkinqrcode_' . $model->pid . '_' . uniqid();
                    $this->widget('ext.qrcode.QRCodeGenerator', array(
                        'data' => $code,
                        'subfolderVar' => true,
                        'matrixPointSize' => 8,
                        'filename' => $filename,
                        'filePath' => Yii::app()->params['uploadPath'],
                        'fileUrl' => Yii::app()->params['uploadBaseUrl'],
                    ));
                } else {
                    echo Yii::t('wechat', 'Error in payment process. Please try again.');
                }
                ?>
            </p>

            <div style="display: none"><?php echo Yii::t('wechat', 'Expire in: ') ?> <span
                    class="_expirationDate"><?php echo date('Y-m-d H:i:s', $model->expired); ?></span></div>

            <div style="text-align: center"><?php echo Yii::t('wechat', 'Expire in'); ?>  <span id="minute_show">00</span>: <span id="second_show">00</span>
            </div>
        </div>
        <div style="padding: 10px 0;width: 60%;margin: 0 auto;text-align: justify;"><a href="javascript:reload();" class="weui_btn weui_btn_plain_primary reload"><?php echo Yii::t('wechat', 'Refresh'); ?></a>
        </div>
    </div>
</div>
<script>
    var exDate = $('._expirationDate').text();
    var difference = (new Date(exDate) - new Date() ) / 1000;
    //    计时器
    var intDiff = parseInt(difference);//倒计时总秒数量
    function timer(intDiff) {
        var count = setInterval(function () {
            var minute = 0, second = 0, hour = 0;//时间默认值
            if (intDiff > 0) {
                hour = Math.floor(intDiff / (60 * 60));
                minute = Math.floor(intDiff / 60) - (hour * 60);
                second = Math.floor(intDiff) - (hour * 60 * 60) - (minute * 60);
            } else {
                clearInterval(count);
                reload();
            }
            if (minute <= 9) minute = '0' + minute;
            if (second <= 9) second = '0' + second;
//            $('#day_show').html(day + "天");
//            $('#hour_show').html(hour);
            $('#minute_show').html(minute);
            $('#second_show').html(second);
            intDiff--;
        }, 1000);
    }
    var reload = function () {
        $.get('<?php echo $this->createurl('checkinQrcode', array('reload'=>1)); ?>');
        location.reload();
    }
    $(function () {
        if (intDiff > 0) {
            timer(intDiff);
        }
    });
</script>