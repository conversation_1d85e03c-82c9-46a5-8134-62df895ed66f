<?php $form=$this->beginWidget('CActiveForm', array(
	'id'=>'staff-form',
	'enableAjaxValidation'=>false,
    'htmlOptions'=>array('class'=>'J_ajaxForm', 'enctype' => 'multipart/form-data'),
));
?>
<div class="h_a">新入职人员资料</div>
<div class="table_full">
    <table width="100%">
        <col width="150" />
        <col width="400" />
        <col />
        <tbody>
            <tr>
                <th><?php echo $form->labelEx($model->profile,'first_name'); ?></th>
                <td>
                    <?php echo $form->textField($model->profile,'first_name',array('maxlength'=>255,'class'=>'input length_5')); ?>
                </td>
                <td><div class="fun_tips">可用拼音代替</div></td>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($model->profile,'last_name'); ?></th>
                <td>
                    <?php echo $form->textField($model->profile,'last_name',array('maxlength'=>255,'class'=>'input length_5')); ?>
                </td>
                <td><div class="fun_tips">可用拼音代替</div></td>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($model,'name'); ?></th>
                <td>
                    <?php echo $form->textField($model,'name',array('maxlength'=>255,'class'=>'input length_5')); ?>
                </td>
                <td><div class="fun_tips"></div></td>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($model->profile,'user_gender'); ?></th>
                <td>
                    <?php echo $form->dropDownList($model->profile,'user_gender',OA::getChildGenderList(), array('class'=>'select_5', 'empty'=>Yii::t('global', 'Please Select'))); ?>
                </td>
                <td><div class="fun_tips"><?php echo $form->error($model->profile,'user_gender'); ?></div></td>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($model->profile,'nationality'); ?></th>
                <td>
                    <?php echo $form->dropDownList($model->profile,'nationality',Country::model()->getData(), array('class'=>'select_5', 'empty'=>Yii::t('global', 'Please Select'))); ?>
                </td>
                <td><div class="fun_tips"><?php echo $form->error($model->profile,'nationality'); ?></div></td>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($model->profile,'branch'); ?></th>
                <td>
                    <?php echo $form->dropDownList($model->profile,'branch', $this->cfs['branch'], array('class'=>'select_5', 'empty'=>Yii::t('global', 'Please Select'))); ?>
                </td>
                <td><div class="fun_tips"><?php echo $form->error($model->profile,'branch'); ?></div></td>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($model->profile,'occupation_en'); ?></th>
                <td>
                    <?php echo $form->dropDownList($model->profile,'occupation_en', DepPosLink::model()->getDepPos(), array('class'=>'select_5', 'empty'=>Yii::t('global', 'Please Select'))); ?>
                </td>
                <td><div class="fun_tips"><?php echo $form->error($model->profile,'occupation_en'); ?></div></td>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($model,'email'); ?></th>
                <td>
                    <?php echo $form->textField($model,'email',array('maxlength'=>255,'class'=>'input length_5')); ?>
                </td>
                <td><div class="fun_tips"><?php echo $form->error($model,'email'); ?></div></td>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($model->staff,'pemail'); ?></th>
                <td>
                    <?php echo $form->textField($model->staff,'pemail',array('maxlength'=>255,'class'=>'input length_5')); ?>
                </td>
                <td><div class="fun_tips"><?php echo $form->error($model->staff,'pemail'); ?></div></td>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($model->staff,'startdate'); ?></th>
                <td>
                    <?php
                    $this->widget('zii.widgets.jui.CJuiDatePicker', array(
                        "model"=>$model->staff,
                        "attribute"=>"startdate",
                        "options"=>array(
                            'changeMonth'=>true,
                            'changeYear'=>true,
                            'dateFormat'=>'yy-mm-dd',
                        ),
                        "htmlOptions"=>array(
                            'class'=>'input length_5'
                        )
                    ));
                    ?>
                </td>
                <td><div class="fun_tips"><?php echo $form->error($model->staff,'startdate'); ?></div></td>
            </tr>
        </tbody>
    </table>
</div>
<div class="btn_wrap">
    <div class="btn_wrap_pd">
        <button class="btn btn_submit mr10 J_ajax_submit_btn"><?php echo Yii::t('global','Submit');?></button>
    </div>
</div>
<?php $this->endWidget(); ?>