<script src="<?php echo Yii::app()->theme->baseUrl; ?>/js/vue.js"></script>
<style>
    .flex-item {
        
        position:relative;
    }
     .flex-item:first-child{
     	margin-right:10px
     }
     .four{
     	width:35%
     }
     .six{
     	width:65%
     }
	.flex-item select{ 
		border: none;
	    outline: none;
	    width: 100%;
	    appearance: none;
	    -webkit-appearance: none;
	    -moz-appearance: none;
	    padding:8px;
	    background: #fff;
	    border: 1px solid #ddd;
	    border-radius: 4px;
	    font-size:13px;
	    height: 36px
	} 
	.flex-item:after{ content: "";
		position: absolute;
		right:10px;
		top: 45%;
		pointer-events: none;border-width:5px 5px 0;
		border-style:solid;
		border-color:#333 transparent transparent;/*灰 透明 透明 */
	}
    #left-div{
        width:60px;
        float: left;
        text-align: center;
       
    }
    #right-div{
        display: -webkit-box;
	    overflow-x: scroll;
	    -webkit-overflow-scrolling:touch;
    } 
	.time0{
	    background: #E1D4C0;
	    border-right: 1px solid #fff;
	    font-size:14px;
	    color: #333
	}
	.time1{
		background: #CDBFAC;
		border-right: 1px solid #fff;
		font-size:14px;
		color: #333
	}
	.weekLy{
		width:180px;
		
	}
	.tabhead{
		height:35px;
		border-bottom: 1px solid #fff;
		border-right: 1px solid #fff;
		text-align: center;
    	line-height:35px;
    	background: #FF7D0D;
    	color: #fff;
    	font-size: 14px
	}
	.cont{
	    word-wrap:break-word;
	    word-break: normal;
	    white-space: nowrap;
	    overflow: hidden;
	    border-right: 1px solid #fff;
	    font-size:14px;
	    padding:0 10px
	}
	.cont pre{
		font-size:12px;
		color: #333
	}
	.cont p{
		white-space:normal; 
		word-break:break-all; 
	    font-size:14px
	}
	.weekcont{
		margin-bottom:60px;
	}
	.fixedbot{
		position: fixed;
		left: 0;
		bottom: 0;
		height:50px;
		background:#fff;
		width:100%
	}
	.fixedbot button:first-child{
     	margin-right:10px
     }
     .lastbtn{
     	margin-top: 0 !important;
     }
     button{
     	height:35px;
     	line-height: 35px !important;
     	font-size:16px !important;
     }
     pre{
		white-space: pre-wrap;           /* css-3 */
		white-space: -moz-pre-wrap;      /* Mozilla, since 1999 */
		white-space: -pre-wrap;          /* Opera 4-6 */
		white-space: -o-pre-wrap;        /* Opera 7 */
		word-wrap: break-word;           /* Internet Explorer 5.5+ */
	    font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;

	}
	.nodata{
		padding:10px;
     
	}
	[v-cloak]{display: none;}
	 #login {   
	    width:85%;
	    position: absolute;
	    top: 50%;
	    left: 50%;
	    z-index: 2;
	    background: white;
	    -webkit-transform: translate(-50%, -50%);
    	transform: translate(-50%, -50%); 
    	text-align: center;
    }  
    .weui_dialog_bd {
	    text-align: left;
	}
	.weui_dialog_bd pre{
		word-break:keep-all; 
	}
    #over{  
        width: 100%;
	    height: 100%;
	    position: absolute;
	    top: 0;
	    left: 0;
	    z-index: 1;
	    background: rgba(0, 0, 0, 0.6); 
    }  
</style>
<div class="weui_cells_title"><?php echo Yii::t("portfolio", "Weekly Schedule"); ?></div>
<div class="weui_panel" style="margin: 0">
	<?php
		$this->widget('ext.wechat.ChildSelector',array(
		    'childObj'=>$this->childObj,
		    'myChildObjs'=>$this->myChildObjs,
		    'jumpUrl'=>'schedule',
		));
	?>
</div>
<div id='week' v-cloak>
	<div class="weui_panel flex-container">
		 <div class="weui_cell">
		    <div class="selectYear flex-item four"> 
			    <select name="" v-model="couponSelected" @change="getCouponSelected">
			        <option :value="year.id" v-for='(year,id) in calendarArr'>{{year.title}}</option>
			    </select>
		    </div>
			<div class="selectWeek flex-item six">
			    <select name=""  v-model="weekselect" @change="getweekselect">
			        <option :value="week.id" v-for='(week,id) in weeklist'>{{week.title}}</option>
			    </select> 
			</div>
		</div>
	</div>
	<div class="weui_panel weekcont"  v-if='scheduleWeekData!=""'> 
		<div id="left-div">
			<p class="tabhead"><?php echo Yii::t("portfolio", "Time"); ?></p>
			<template v-for='(date,index) in timeNodes'>
				<p :class="'time'+date.odd+''" :style="'height:'+ 30 * DisplayLengthRatio +'px;'">{{date.text}}</p>
			</template>
		</div> 
		<div id="right-div" >
			<div class="weekLy">
				<p class="mon tabhead"><?php echo Yii::t("portfolio", "Monday"); ?></p>
				<template>
					<div class='cont' :style="'height:'+ data.period * DisplayLengthRatio +'px;background:'+ bgColorMapping[data.activityId] +''"  @click='detailed(0,idx)'  v-for='(data,idx) in scheduleWeekData[0]'><p class="title">{{data.title}}</p><pre>{{data.content}}</pre></div>	
				</template>			
			</div>
			<div class="weekLy">
				<p class="tus tabhead"><?php echo Yii::t("portfolio", "Tuesday"); ?></p>
				<div class='cont' :style="'height:'+ data1.period * DisplayLengthRatio +'px;background:'+ bgColorMapping[data1.activityId] +''" v-for='(data1,idx) in scheduleWeekData[1]'  @click='detailed(1,idx)'><p class="title">{{data1.title}}</p><pre>{{data1.content}}</pre></div>
			</div>
			<div class="weekLy">
				<p class="wed tabhead"><?php echo Yii::t("portfolio", "Wednesday"); ?></p>
				<div class='cont' :style="'height:'+ data2.period * DisplayLengthRatio +'px;background:'+ bgColorMapping[data2.activityId] +''" v-for='(data2,idx) in scheduleWeekData[2]'  @click='detailed(2,idx)'><p class="title">{{data2.title}}</p><pre>{{data2.content}}</pre></div>
			</div>
			<div class="weekLy">
				<p class="thu tabhead"><?php echo Yii::t("portfolio", "Thursday"); ?></p>
				<div class='cont' :style="'height:'+ data3.period * DisplayLengthRatio +'px;background:'+ bgColorMapping[data3.activityId] +''" v-for='(data3,idx) in scheduleWeekData[3]'  @click='detailed(3,idx)'><p class="title">{{data3.title}}</p><pre>{{data3.content}}</pre></div>
			</div>
			<div class="weekLy">
				<p class="fir tabhead"><?php echo Yii::t("portfolio", "Friday"); ?></p>
				<div class='cont' :style="'height:'+ data4.period * DisplayLengthRatio +'px;background:'+ bgColorMapping[data4.activityId] +''" v-for='(data4,idx) in scheduleWeekData[4]'  @click='detailed(4,idx)'><p class="title">{{data4.title}}</p><pre>{{data4.content}}</pre></div>
			</div>
		</div>
	</div>
	<div v-if="apiLoaded && scheduleWeekData == ''">
		<?php echo $this->renderPartial('../bind/_null', array('text' => Yii::t("lunch", "Weekly schedule information for this week is not yet available and are usually uploaded on Fridays at 6pm (except during school holidays)."), 'title' => Yii::t("survey", "No Data") )); ?>
	</div>
	<div class="fixedbot">
		<div class="weui_cell">
		    <template v-if='weekselect==1'>
		    	<button  class="weui_btn weui_btn_disabled weui_btn_default "><?php echo Yii::t("portfolio", "Prev Week"); ?></button>
		    </template>
			<template v-else>
				<button  class="weui_btn weui_btn_default prev " @click='prev()'><?php echo Yii::t("portfolio", "Prev Week"); ?></button>
			</template>
			<template v-if='weekselect==lastweek'>
				<button  class="weui_btn weui_btn_disabled weui_btn_default lastbtn"><?php echo Yii::t("portfolio", "Next Week"); ?></button>
			</template>
			<template v-else>
				<button  class="weui_btn weui_btn_default lastbtn next" @click='next()'><?php echo Yii::t("portfolio", "Next Week"); ?></button>
			</template>
		</div>
	</div>
	<div class="" id="dialog2" style="display: none">
	    <div id="login">  
	        <div class="weui_dialog_hd"><strong class="weui_dialog_title">{{title}}</strong></div> 
	        <div class="weui_dialog_bd"><pre>{{text}}</pre></div>
	        <div class="weui_dialog_ft">
	            <a href="javascript:;" class="weui_btn_dialog " onclick='$("#dialog2").hide()'><?php echo Yii::t("survey", "Close"); ?></a>
	        </div>
	   </div>  
	   <div id="over" onclick='$("#dialog2").hide()'></div>
	</div>
</div>
<script>
function getTimeSlots(classStart, classEnd){
    timeNodes = new Array();
    var i=0;
    var _t = classStart;

    var timeNodes = new Array;
    if(_t.getMinutes() != 0 && _t.getMinutes() != 30){
        timeNodes[i] = {
            period: 60 - _t.getMinutes(),
            text: ( ( _t.getHours() < 10) ? '0' + _t.getHours() : _t.getHours() ) + ':' + _t.getMinutes(),
            flag: 'first',
            odd: ( i % 2 ) ? 1 : 0
        }
        _t.setMinutes( 60 );
        i++;
    }
    while(_t < classEnd){
        timeNodes[i] = {
            flag: (i==0)?'first':'',
            period: 30,
            text: ( ( _t.getHours() < 10) ? '0' + _t.getHours() : _t.getHours() ) + ':' + ( ( _t.getMinutes() < 10) ? '0' + _t.getMinutes() : _t.getMinutes() ),
            odd: ( i % 2 ) ? 1 : 0
        }
        _t.setMinutes(_t.getMinutes() + 30, 0, 0);
        i++;
    }
    if(_t.getTime() == classEnd.getTime()){
        timeNodes[--i].flag = 'end';
        timeNodes[i].period = 30;
    }else{
        timeNodes[--i].flag = 'end';
        timeNodes[i].period = (classEnd.getMinutes() > 30 ) ? classEnd.getMinutes() - 30 : classEnd.getMinutes();
    }

    return timeNodes;
}
var calendarArr=<?php echo json_encode($calendarArr); ?>;
var bgColorMapping = {
        188:'#FFDBDB',	//自选活动
        189:'#D5FFC6',	//英文圆圈
        190:'#D2D6FF',	//间点
        191:'#DBF0FF',	//户外活动
        192:'#AFCC6A',	//午餐
        193:'#CCCCCC',	//午睡
        194:'#FFFFDB',	//中文圆圈
        195:'#C55186',	//园外活动
        196:'#DBF0FF'	//特色课
    };
    function sortKey(array,key){
		return array.sort(function(a,b){
	         var x = parseInt(a[key])
	         var y =parseInt(b[key]);
	         return ((x>y)?-1:(x<y)?1:0)
	     })
	}
var week = new Vue({
	        el: "#week",
	        data: {
	            apiLoaded: false,
	           timeNodes:'',
	           calendarArr:'',
	           couponSelected:'',//当前选中的学年
	           scheduleWeekData:'',
	           DisplayLengthRatio:'2.3',
	           text:'',
	           title:'',
	           weeklist:'',
	           weekselect:'',
	           lastweek:'',
	           bgColorMapping:bgColorMapping,
	        }, 
	        created: function () {
	        	var lastkey="";
				for (key in calendarArr){
				    lastkey=key;
				}
	        	this.couponSelected = lastkey;
	        	var newcalendarArr=[]
	        	for(key in calendarArr){
		 			var calendarArrkey={}
		 			calendarArrkey.id=key
		 			calendarArrkey.title=calendarArr[key]
		 			newcalendarArr.push(calendarArrkey)
		 		}
		 		var calendarArrs=sortKey(newcalendarArr,'id')
		 		this.calendarArr=calendarArrs
	        	this.getCouponSelected()
	        },
	        methods: {
	        	getCouponSelected(){
	        		 $.ajax({
	        		 	url:"<?php echo $this->createUrl('scheduleYear')?>",
	        		 	type: 'post',
	        		 	dataType: 'json',
	        		 	data: {yid:this.couponSelected},
	        		 	success:(data) => {
	        		 	    this.$nextTick(() => {
                                this.apiLoaded = true
                            })
	        		 		 newweekdatas=[]
	        		 		for(key in data.weekDays){
	        		 			var weekkey={}
	        		 			weekkey.id=key
	        		 			weekkey.title=data.weekDays[key]
	        		 			newweekdatas.push(weekkey)
	        		 		}
	        		 		var newweeks=sortKey(newweekdatas,'id')
	        		 		week.weeklist=newweeks
	        		 		var scheduleWeekData=[]
	        		 		if(data.scheduleData!=''){
		        		 		var weekdata= eval('(' + data.scheduleData + ')');
		        		 		for(var i=0;i<5;i++){
								    var dayData = 'scheduleWeekData[i] = ' + weekdata[i] + ';';
								    eval( dayData );
								}
								week.scheduleWeekData=scheduleWeekData
	        		 		}else{
	        		 			week.scheduleWeekData=''
	        		 		}
	        		 		for (key in data.weekDays){
							    lastweek=key;
							}
							week.lastweek=lastweek
				        	week.weekselect = lastweek;
				        	if(data.start!=false || data.start!=''){
				        		var classStart = new Date(parseInt(data.start * 1000 ));
							    var classEnd = new Date(parseInt(data.end * 1000 ));
								timeNodes = getTimeSlots(classStart, classEnd);
								week.timeNodes=timeNodes
				        	}
				        	
	        		 	}
	        		 })
	        	},
	        	getweekselect(){
					$.ajax({
	        		 	url:"<?php echo $this->createUrl('scheduleData')?>",
	        		 	type: 'post',
	        		 	dataType: 'json',
	        		 	data: {week:this.weekselect,'yid':this.couponSelected},
	        		 	success:function(data){
	        		 		
	        		 		var scheduleWeekData=[]
	        		 		if(data.scheduleData!=''){
		        		 		var weekdata= eval('(' + data.scheduleData + ')');
		        		 		for(var i=0;i<5;i++){
								    var dayData = 'scheduleWeekData[i] = ' + weekdata[i] + ';';
								    eval( dayData );
								}
								week.scheduleWeekData=scheduleWeekData
	        		 		}else{
	        		 			week.scheduleWeekData=''
	        		 		}
        		 			$('.prev').removeClass('weui_btn_disabled')
        		 			$('.next').removeClass('weui_btn_disabled')
        		 			if(data.start!=false || data.start!=''){
		        		 		var classStart = new Date(parseInt(data.start * 1000 ));
							    var classEnd = new Date(parseInt(data.end * 1000 ));
								timeNodes = getTimeSlots(classStart, classEnd);
								week.timeNodes=timeNodes
							}
	        		 	}
	        		 })
	        	},
	        	prev(){
	        		$('.prev').addClass('weui_btn_disabled')
	        		var prevweek=''
	        		for(var i=0;i<week.weeklist.length;i++){
	                    if(week.weekselect==week.weeklist[i].id){
	                        prevweek=week.weeklist[i+1].id
	                    }
	                }
	                this.weekselect=prevweek
	        		this.getweekselect()
	        	},
	        	next(){
	        		$('.next').addClass('weui_btn_disabled')
	        		var nextweek=''
	        		for(var i=0;i<week.weeklist.length;i++){
	                    if(week.weekselect==week.weeklist[i].id){
	                        nextweek=week.weeklist[i-1].id
	                    }
	                }
	                this.weekselect=nextweek
	        		this.getweekselect()
	        	},
	        	detailed(mon,index){
	        		this.text=this.scheduleWeekData[mon][index]['content']
	        		this.title=this.scheduleWeekData[mon][index]['title']
	        		$('#dialog2').show()
	        	}
	        },
	    })
</script>