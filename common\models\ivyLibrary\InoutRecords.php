<?php

/**
 * This is the model class for table "ivy_lib_inout_records".
 *
 * The followings are the available columns in table 'ivy_lib_inout_records':
 * @property integer $id
 * @property integer $userid
 * @property string $cardid
 * @property string $bookid
 * @property string $borrowtime
 * @property string $backtime
 * @property integer $borroworback
 * @property string $notes
 * @property integer $returnstatus
 * @property integer $operator
 * @property integer $status
 * @property integer $updatetime
 */
class InoutRecords extends CActiveRecord {

    /**
     * Returns the static model of the specified AR class.
     * @param string $className active record class name.
     * @return InoutRecords the static model class
     */
    public static function model($className = __CLASS__) {
        return parent::model($className);
    }

    /**
     * @return string the associated database table name
     */
    public function tableName() {
        return 'ivy_lib_inout_records';
    }

    /**
     * @return array validation rules for model attributes.
     */
    public function rules() {
        // NOTE: you should only define rules for those attributes that
        // will receive user inputs.
        return array(
            array('userid, borroworback, returnstatus, operator, status, updatetime', 'numerical', 'integerOnly' => true),
            array('cardid, bookid, borrowtime, backtime', 'length', 'max' => 25),
            array('notes', 'length', 'max' => 500),
            // The following rule is used by search().
            // Please remove those attributes that should not be searched.
            array('id, userid, cardid, bookid, borrowtime, backtime, borroworback, notes, returnstatus, operator, status, updatetime', 'safe', 'on' => 'search'),
        );
    }

    /**
     * @return array relational rules.
     */
    public function relations() {
        // NOTE: you may need to adjust the relation name and the related
        // class name for the relations automatically generated below.
        return array(
//            'barcode' => array(self::BELONGS_TO, 'Books', 'bookid'),
//            'book' => array(self::HAS_ONE, 'Books', array('barcode'=>'id'),'through'=>'barcode'),
        );
    }

    public function scopes() {
        return array(
            'borrowing' => array(
                'returnstatus' => 0
            )
        );
    }

    public function renderBookTitle($data, $row) {
        $book_info = Books::model()->findByAttributes(array('barcode' => $data->bookid));
        return $book_info->title;
    }

    /**
     * @return array customized attribute labels (name=>label)
     */
    public function attributeLabels() {
        return array(
            'bookid' => Yii::t("library", "Barcode"),
            'borrowtime' => Yii::t("library", "Date Borrowed"),
            'backtime' => Yii::t("library", "Due Date"),
        );
    }

    /**
     * Retrieves a list of models based on the current search/filter conditions.
     * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
     */
    public function search() {
        // Warning: Please modify the following code to remove attributes that
        // should not be searched.

        $criteria = new CDbCriteria;

        $criteria->compare('id', $this->id);
        $criteria->compare('userid', $this->userid);
        $criteria->compare('cardid', $this->cardid, true);
        $criteria->compare('bookid', $this->bookid, true);
        $criteria->compare('borrowtime', $this->borrowtime, true);
        $criteria->compare('backtime', $this->backtime, true);
        $criteria->compare('borroworback', $this->borroworback);
        $criteria->compare('notes', $this->notes, true);
        $criteria->compare('returnstatus', $this->returnstatus);
        $criteria->compare('operator', $this->operator);
        $criteria->compare('status', $this->status);
        $criteria->compare('updatetime', $this->updatetime);

        return new CActiveDataProvider($this, array(
                    'criteria' => $criteria,
                ));
    }

}