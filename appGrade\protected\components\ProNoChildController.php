<?php

class ProNoChildController extends ProtectedController {

    public function initChildInfo() {
        $_childid = Yii::app()->user->getAdminCIds() ? Yii::app()->user->getAdminCIds()[0] : 0;
        if ($_childid){
            $this->setChildid($_childid);
            $this->role = "parent";
            $this->getAllChild($this->role);
            $this->nav = Mims::Nav();
            $this->setChildInfo();
        }
        return $_childid;
    }
}
