<?php

/**
 * This is the model class for table "ivy_child_misc".
 *
 * The followings are the available columns in table 'ivy_child_misc':
 * @property integer $childid
 * @property string $barcode
 * @property integer $photo_auth
 * @property string $ucontact
 * @property string $allergy
 * @property integer $sign_allergy
 * @property string $upickup
 * @property string $hospital
 * @property integer $agree_photo_open
 * @property integer $vaccine
 * @property integer $physical
 * @property string $privacy
 * @property string $remark
 * @property integer $updated_timestamp
 * @property integer $create_uid
 */
class ChildMisc extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @return ChildMisc the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_child_misc';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('childid', 'required'),
			array('allergy, upickup, hospital, ucontact, privacy','safe'),

			//由于 sign_allergy agree_photo_open vaccine physical 需要校园知晓后才能由行政修改，家长不能自行更改，因此在rule中不定义这些项，保存的时候会自动过滤掉
			//array('childid, photo_auth, sign_allergy, agree_photo_open, vaccine, physical, updated_timestamp, create_uid', 'numerical', 'integerOnly'=>true),
			array('childid, photo_auth, updated_timestamp, create_uid', 'numerical', 'integerOnly'=>true),
			array('barcode', 'length', 'max'=>255),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('childid, barcode, photo_auth, ucontact, allergy, sign_allergy, upickup, hospital, agree_photo_open, vaccine, physical, remark, updated_timestamp, create_uid', 'safe', 'on'=>'search'),
			array('childid, barcode, photo_auth, ucontact, allergy, sign_allergy, upickup, hospital, agree_photo_open, vaccine, physical, residence, remark, updated_timestamp, create_uid', 'safe', 'on'=>'appOA'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'childid' => 'Childid',
			'barcode' => 'Barcode',
			'photo_auth' => Yii::t("labels",'Photo Authorization'),
			'ucontact' => Yii::t("labels",'Emergency Contact'),
			'allergy' => Yii::t("labels",'Special Needs'),
			'sign_allergy' => Yii::t("labels",'Alternative Lunch Menu'),
			'upickup' => Yii::t("labels",'Pick Up Contact'),
			'hospital' => Yii::t("labels",'Hospital'),
			'agree_photo_open' => Yii::t("labels",'Photo Authorization'),
			'vaccine' => Yii::t("labels",'Vaccination'),
			'physical' => Yii::t("labels",'Physical Examination'),
            'privacy' => Yii::t("labels", 'Privacy'),
            'residence' => Yii::t("labels", 'Residence Location'),
			'remark' => 'Remark',
			'updated_timestamp' => 'Updated Timestamp',
			'create_uid' => 'Create Uid',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('childid',$this->childid);
		$criteria->compare('barcode',$this->barcode,true);
		$criteria->compare('photo_auth',$this->photo_auth);
		$criteria->compare('ucontact',$this->ucontact,true);
		$criteria->compare('allergy',$this->allergy,true);
		$criteria->compare('sign_allergy',$this->sign_allergy);
		$criteria->compare('upickup',$this->upickup,true);
		$criteria->compare('hospital',$this->hospital,true);
		$criteria->compare('agree_photo_open',$this->agree_photo_open);
		$criteria->compare('vaccine',$this->vaccine);
		$criteria->compare('physical',$this->physical);
        $criteria->compare('privacy', $this->privacy, true);
		$criteria->compare('remark',$this->remark,true);
		$criteria->compare('updated_timestamp',$this->updated_timestamp);
		$criteria->compare('create_uid',$this->create_uid);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}