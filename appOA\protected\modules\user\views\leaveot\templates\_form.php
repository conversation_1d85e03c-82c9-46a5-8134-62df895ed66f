<?php
    $labels = LeaveItem::attributeLabels();
?>
<script type="text/template" id="form-template">
<style>
	.date-active a {
		background: url("/themes/modern/images/icons/circle.png") no-repeat right -2px !important;
	}
</style>
<div class="panel panel-default">
	<!-- Default panel contents -->
	<div class="panel-heading"><h3 class="panel-title"><%= appLabel.label %></h3></div>
	<div class="panel-body">
        <div class="well">各校园的假期规则，该功能待加入</div>
		<% if (!_.isUndefined(leaveList[type])){ %>
			<div class="row mb15">
                <div class="col-md-12">
                    <!--年假剩余天数-->
                    <div class="form-group">
                        <div class="col-sm-2">&nbsp;</div>
                        <div class="col-sm-9">
                        <%
                            var radioCheck = '';
                            if (_.keys(leaveList[type].list).length == 1){
                                radioCheck = 'checked="checked"';
                            }
                            $.each(leaveList[type].list, function(m, item){
                                print('<div class="radio mr15"><label for="LeaveItem_leave_id_'+m+'"><input id="LeaveItem_leave_id_'+m+'" value="'+m+'" type="radio" name="LeaveItem[leave_id]" '+radioCheck+'>'+item.formatHours+'有效期至'+item.formatEnddate+'</label></div>')
                            })
                        %>
                        </div>
                    </div>
			    </div>
			</div>
        <%}%>

        <% if (!_.isUndefined(appLabel.subtitle)){ %>
            <div class="row mb15">
                <div class="col-md-12">
                    <!--其它假期及加班类型-->
                    <div class="form-group">
                        <label class="col-sm-2 control-label">Type</label>
                        <div class="col-sm-9">
                        <%
                            $.each(appLabel.subtitle, function(m, item){
                                print('<div class="radio mr15"><label for="LeaveItem_type_'+m+'"><input id="LeaveItem_type_'+m+'" value="'+m+'" type="radio" name="LeaveItem[type]">'+item+'</label></div>')
                            })
                        %>
                        </div>
                    </div>
                </div>
            </div>

        <% } %>
        <div class="row mb15">
        <% if (methods == 'hour'){ %>
            <div class="col-md-12">
                <!--按小时请开始日期-->
                <div class="form-group">
                    <?php echo CHtml::label($labels['startdate'], CHtml::getIdByName('LeaveItem[startdate]'), array('class'=>'control-label col-sm-2')); ?>
                    <div class="col-sm-9">
                        <?php echo CHtml::textField('LeaveItem[startdate]', $value,array('class'=>'form-control length_3','placeholder'=>'选择开始日期'));?>
                    </div>
                </div>
            </div>
            <div class="col-md-12">
                <!--按小时请的开始（时分）与结束（时分）-->
                <div class="form-group">
                <?php echo CHtml::label($labels['hours'], CHtml::getIdByName('LeaveItem[hours]'), array('class'=>'control-label col-sm-2')); ?>
                <?php
                $day = array(''=>  Yii::t('user','Please Select'));
                for ($i = 8; $i < 18; $i++) {
                    for($j=0;$j<=30;$j+=30){
                        $day[$i.":".sprintf('%02d',$j)] = $i.":".sprintf('%02d',$j);
                    }
                }
                ?>

                    <div class="col-sm-9 form-inline">
                        <?php echo CHtml::dropDownList('LeaveItem[startTime]', '', $day, array('class' => 'form-control'));?>
                        <span class="glyphicon glyphicon-minus"></span>
                        <?php echo CHtml::dropDownList('LeaveItem[endTime]', '', $day, array('class' => 'form-control'));?>
                    </div>
                </div>

            </div>
            <% } %>
            <% if (methods == 'day'){ %>
            <!--按天请日历框-->
            <div class="col-md-12">
                <div class="form-group">
                    <label class="col-sm-2 control-label">
                        Select Days
                        <div class="pt10 text-info hide" id="selected-day">
                            选中 <span class="selected-day-number">1</span> 天 <a href="javascript:void(0)" onclick="emptySelectedDay()" title="清空选中"><span class="glyphicon glyphicon-remove"></span></a>
                        </div>
                    </label>

                    <div class="col-sm-9">
                        <div id="LeaveItem_enddate_container"></div>
                        <?php echo CHtml::hiddenField('LeaveItem[startdate]');?>
                    </div>
                </div>
            </div>
            <% } %>
            <% if (methods == 'more'){ %>
            <div class="col-md-12">
                <!--长期请假开始日期-->
                <div class="form-group">
                    <?php echo CHtml::label($labels['startdate'], CHtml::getIdByName('LeaveItem[startdate]'), array('class'=>'col-sm-2 control-label')); ?>
                    <div class="col-sm-9">
                        <?php echo CHtml::textField('LeaveItem[startdate]', '',array('class'=>'form-control length_3','placeholder'=>'选择开始日期'));?>
                    </div>
                </div>
                 <!--长期请假结束日期-->
                <div class="form-group">
                    <?php echo CHtml::label($labels['enddate'], CHtml::getIdByName('LeaveItem[enddate]'), array('class'=>'col-sm-2 control-label')); ?>
                    <div class="col-sm-9">
                        <?php echo CHtml::textField('LeaveItem[enddate]', '',array('class'=>'form-control length_3','placeholder'=>'选择结束日期'));?>
                    </div>
                </div>
            </div>
        <% } %>
        </div>
        <!--  申请备注 -->
        <div class="row mb15">
            <!--请假描述文本框-->
            <div class="col-md-12">
                <div class="form-group">
                    <?php echo CHtml::label($labels['apply_desc'], CHtml::getIdByName('LeaveItem[apply_desc]'), array('class' => 'col-sm-2 control-label')); ?>
                    <div class="col-sm-9">
                        <?php echo CHtml::textArea('LeaveItem[apply_desc]', '', array('class' => 'form-control', 'placeholder' => '描述','rows'=>'4','width'=>'200px')); ?>
                    </div>
                </div>
            </div>
        </div>
	</div>
	<div class="panel-footer">
        <?php echo CHtml::hiddenField('type', '<%= type %>',array('encode'=>false));?>
        <?php echo CHtml::hiddenField('methods', '<%= methods %>',array('encode'=>false));?>
		<button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global','Submit');?></button>
		<button type="button" class="btn btn-default" onclick="resetForm(this);"> 重置 </button>
	</div>
</div>
</script>

<script type="text/template" id="staff-item-template">
       <div class="col-md-4 hover p2 item" staffid=<%= uid %>>
       <span class="wrap">
           <span class="glyphicon glyphicon-user flag"></span>
           <a basehref="<?php echo Yii::app()->createUrl('/user/leaveot');?>" userid=<%= uid %> target="_blank"><%= name %></a>
       </span>
       </div>
</script>

<script type="text/template" id="leave-item-template">
    <tr id="<%= id %>">
        <th><%= typeTxt %></th>
        <td><%= dateTxt %></td>
        <td class="text-success text-center"><%= hoursTxt %></td>
        <td class="text-success text-center"><%= created %></td>
    </tr>
</script>