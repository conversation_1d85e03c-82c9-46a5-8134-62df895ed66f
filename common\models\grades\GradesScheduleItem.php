<?php

/**
 * This is the model class for table "ivy_grades_schedule_item".
 *
 * The followings are the available columns in table 'ivy_grades_schedule_item':
 * @property integer $id
 * @property integer $schedule_id
 * @property integer $weekday
 * @property string $timeslot
 * @property string $subject_flag
 * @property integer $teacher_uid
 * @property string $grade_flag
 * @property integer $classid
 * @property string $room_code
 * @property integer $specific_childid
 * @property integer $updated
 */
class GradesScheduleItem extends SubDBActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_grades_schedule_item';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('timeslot, subject_flag, teacher_uid, grade_flag, room_code', 'required'),
			array('schedule_id, weekday,  classid, specific_childid, updated', 'numerical', 'integerOnly'=>true),
			array('timeslot, subject_flag, room_code', 'length', 'max'=>32),
			array('grade_flag', 'length', 'max'=>16),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, schedule_id, weekday, timeslot, subject_flag, teacher_uid, grade_flag, classid, room_code, specific_childid, updated', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'schedule_id' => 'Schedule',
			'weekday' => 'Weekday',
			'timeslot' => 'Timeslot',
			'subject_flag' => 'Subject Flag',
			'teacher_uid' => 'Teacher Uid',
			'grade_flag' => 'Grade Flag',
			'classid' => 'Classid',
			'room_code' => 'Room Code',
			'specific_childid' => 'Specific Childid',
			'updated' => 'Updated',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('schedule_id',$this->schedule_id);
		$criteria->compare('weekday',$this->weekday);
		$criteria->compare('timeslot',$this->timeslot,true);
		$criteria->compare('subject_flag',$this->subject_flag,true);
		$criteria->compare('teacher_uid',$this->teacher_uid);
		$criteria->compare('grade_flag',$this->grade_flag,true);
		$criteria->compare('classid',$this->classid);
		$criteria->compare('room_code',$this->room_code,true);
		$criteria->compare('specific_childid',$this->specific_childid);
		$criteria->compare('updated',$this->updated);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return GradesScheduleItem the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}
