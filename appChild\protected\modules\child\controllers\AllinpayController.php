<?php

class allinpayController extends ProtectedController {

    public function init() {
        parent::init();
        Yii::import('common.models.invoice.*');
        Yii::import('common.models.alipay.*');
    }

    public function actionOnlineBankInfo() {

        $allinpayAvailable = false;
        $allinpayBankInfo = Mims::LoadConfig('CfgAllinpayBankInfo');
        $allinpayPartnerInfo = Mims::LoadConfig('CfgAllinpayPartnerInfo');
        if (isset(Yii::app()->params['onlineBanking']) && 'production' == Yii::app()->params['onlineBanking']) {
            $schoolid = $this->getSchoolId();
        } else {
            $schoolid = 'test';
        }
        if (!empty($allinpayPartnerInfo[$schoolid]['partner'])) {
            $allinpayAvailable = true;
        }
        $allinpayBankList = array_keys($allinpayBankInfo);
        $usedBank = array();
        $otherBankList = array();
        if (true == $allinpayAvailable) {
            $ph_cri = new CDbCriteria();
            $ph_cri->compare('childid', $this->getChildId());
            $ph_cri->order = 'hot_value DESC';
            $ph_cri->limit = 3;
            $ph_cri->index = 'bankid';
            $habits = AlipayPayHabit::model()->findAll($ph_cri);

            if (empty($habits)) {
                $habitBank = array();
            } else {
                $habitBank = array_keys($habits);
            }

            foreach ($allinpayBankList as $bankid) {
                if (in_array($bankid, $habitBank)) {
                    $usedBank[$bankid] = '';
                } else {
                    $otherBankList[$bankid] = '';
                }
            }
        }
        $this->renderPartial('/allinpay/onlineBankInfo', array(
            'allinpayAvailable' => $allinpayAvailable,
            'usedBank' => $usedBank,
            'otherBankList' => $otherBankList
                )
        );

    }


    public function actionOnlineNotice() {

        $allinpayBankInfo = Mims::LoadConfig('CfgAllinpayBankInfo');

        // Yii::app()->clientScript->registerCssFile(Yii::app()->theme->baseUrl . '/css/notice.css?t=' . Yii::app()->params['refreshAssets']);
        // 输出 变量到 视图文件
        $view_data = array(
            'childId' => $this->getChildId(),
            'langKey' => Yii::app()->language == 'en_us' ? 'en' : 'cn',
            'allinpayBankInfo' => $allinpayBankInfo,
        );
        $this->renderPartial('/allinpay/onlineNotice', $view_data);
    }

}
