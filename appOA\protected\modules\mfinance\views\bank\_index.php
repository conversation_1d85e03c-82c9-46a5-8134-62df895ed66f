<?php
$model = new Banktransfer();
?>
<style>
</style>
<div class="panel panel-default">
    <div class="panel-heading">
        <h3 class="panel-title"><?php echo $this->menuItem[$this->selectMenu]; ?></h3>
    </div>
    <div class="panel-body">
        <div class="well">
            <div class="row">
                <?php echo CHtml::form($this->createUrl('searchData'), 'post', array('class' => 'form-inline J_ajaxForm')) ?>
                <div class="col-sm-12">
                    <div class="input-group col-sm-3">
                        <?php echo CHtml::textField('id', '', array('class' => 'form-control', 'placeholder' => Yii::t('invoice', '交易流水号'))); ?>
                        <span class="input-group-btn">
                            <button type="submit" class="btn btn-default J_ajax_submit_btn">查询</button>
                        </span>
                    </div>
                    <span class="alert alert-warning hide" role="alert" style="padding:5px;"
                          id="show-bank-paded-info"></span>
                </div>
                <?php echo CHtml::endForm(); ?>
            </div>
        </div>
        <div class="mb10"></div>
        <div id="childandbankinfo" class="hide">
            <?php echo CHtml::form($this->createUrl('searchInvoiceData'), 'post', array('class' => 'form-inline J_ajaxForm')) ?>
            <div class="well">
                <div class="row">
                    <div class="col-sm-6">
                        <div class="input-group-btn">
                            <div class="form-group">
                                <?php echo CHtml::activeTextField($model, 'receiptid', array('class' => 'form-control', 'placeholder' => Yii::t('invoice', '银行回单号'))); ?>
                            </div>
                            <div class="form-group">
                                <?php echo CHtml::activeTextField($model, 'bamount', array('class' => 'form-control form-group', 'placeholder' => Yii::t('invoice', '到帐金额'))); ?>
                            </div>
                            <div class="form-group">
                                <?php echo CHtml::activeHiddenField($model, 'id'); ?>
                                <?php $this->widget('zii.widgets.jui.CJuiDatePicker', array(
                                    'model' => $model,
                                    'attribute' => 'btimestamp',
                                    'options' => array(
                                        'dateFormat' => 'yy-mm-dd',
                                    ),
                                    'htmlOptions' => array(
                                        'class' => 'form-control',
                                        'placeholder' => Yii::t('invoice', '银行到帐日期'),
                                    ),
                                )); ?>
                            </div>
                            <div class="checkbox">
                                <label style='font-size:12px'>
                                    <?php echo CHtml::activeCheckBox($model, 'company_payment'); ?> 公司付款
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="mb10"></div>
                <div class="row">
                    <div class="col-sm-3">
                        <?php $this->widget('ext.search.ChildSearchBox', array(
                            'acInputCSS' => 'form-control',
                            'allowMultiple' => true,
                            'allowMultipleSchool' => true,
                            'withAlumni' => true,
                            'useModel' => true,
                            'model' => $model,
                            'attribute' => 'childid',
                            'htmlOptions' => array('style' => 'width:100%', 'class' => 'form-control')
                        )) ?>
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-3">
                        <button type="button" class="btn btn-default active btn-block J_ajax_submit_btn">获取账单</button>
                    </div>
                </div>
            </div>
            <?php echo CHtml::endForm(); ?>
        </div>
        <div class="mb10"></div>
        <div class="hide" id="well-container">
            <?php echo CHtml::form($this->createUrl('saveInvoiceData'), 'post', array('class' => 'J_ajaxForm')) ?>
            <table class="table table-hover">
                <thead>
                <tr>
                    <th></th>
                    <th>孩子名字</th>
                    <th>标题</th>
                    <th>校园</th>
                    <th>总额</th>
                    <th>已付款</th>
                    <th>本次支付</th>
                </tr>
                </thead>
                <tbody style="background-color: #ffffff" id="child-invoice-list">

                </tbody>
            </table>
            <div id="fee-count">
            </div>
            <div class="row">
                <div class="col-sm-2">
                    <button type="button" class="btn btn-default active btn-block J_ajax_submit_btn length_4">确认付款
                    </button>
                </div>
                <div class="col-sm-10">
                    <div class="checkbox">
                        <label><input type="checkbox" name="sendMail"> 发送通知邮件给家长</label>
                    </div>
                </div>
            </div>
            <?php echo CHtml::hiddenField('transferid'); ?>
            <?php echo CHtml::endForm(); ?>
        </div>
    </div>
</div>

<!--模板-->
<script type="text/template" id="invoice-info">
    <tr style="cursor:pointer;">
        <td style="width:25px;"><?php echo CHtml::checkBox('invioceId[]', false, array('encode' => false, 'value' => '<%=invoiceId%>', 'onclick' => 'selectRow(this,0)')); ?></td>
        <td onclick="selectRow(this,1)"><%=childName%></td>
        <td onclick="selectRow(this,1)"><%=title%></td>
        <td onclick="selectRow(this,1)"><%=schoolid%></td>
        <td onclick="selectRow(this,1)"><%=amount%></td>
        <td onclick="selectRow(this,1)"><%=payed%></td>
        <td onclick="selectRow(this,1)"><%=unpay%></td>
    </tr>
</script>

<script type="text/template" id="fee-count-template">
    <div class="alert alert-success" role="alert">
        银行到帐共计：<b><%=$.number(bankInfo.bamount,2)%></b> 本次支付：<b><%=$.number(unpayAmount,2)%></b>
        <%if (balanceAmount>=0.001){%>
        ，剩余 <b><%=$.number(balanceAmount,2)%></b>
        <%if (_.size(childListIds)>=1){%>
        将转入
        <%if (_.size(childListIds)>=2){%>
        <select id="childCredit" name="childCredit" class="form-control" style="display:inline;width:150px;">
            <option value="">请选择孩子</option>
            <%_.each(childListIds,function(val,key){%>
            <option value="<%=val%>"><%=childList[val].childName%></option>
            <%})%>
        </select>
        <%}else{%>
        <%=childList[childListIds[0]].childName%>
        <%}%>
        个人账户中
        <%}%>
        <%}%>
    </div>
</script>

<script type="text/javascript">
    backBankInfo = function (data) {
        switch (data.new) {
            case 0:
                $("#show-bank-paded-info").addClass('hide');
                $("#childandbankinfo").removeClass('hide');
                $("#Banktransfer_receiptid").val(data.data.receiptid);
                $("#Banktransfer_bamount").val(data.data.bamount);
                $("#Banktransfer_btimestamp").val(data.data.btimestamp);
                $("#Banktransfer_id").val(data.data.id);
                if (data.data.option) {
                    $("#Banktransfer_childid").empty();
                    $("#Banktransfer_childid").append(data.data.option);
                }
                $("#Banktransfer_bamount").attr('disabled', false);
                $("#child-invoice-list").empty();
                break;
            case 1:
                $("#show-bank-paded-info").addClass('hide');
                $("#childandbankinfo").removeClass('hide');
                $("#Banktransfer_receiptid").val(data.data.receiptid);
                $("#Banktransfer_bamount").val(data.data.bamount);
                $("#Banktransfer_btimestamp").val(data.data.btimestamp);
                $("#Banktransfer_id").val(data.data.id);
                $("#Banktransfer_childid").empty();
                $("#Banktransfer_bamount").attr('disabled', false);
                $("#child-invoice-list").empty();
                break;
            case 2:
                var url = '<?php echo $this->createUrl('//mfinance/bank/index/',array('branchId'=>$this->branchId,'selectMenu'=>'list'));?>' + '&id=' + data.data.id;
                $("#show-bank-paded-info").removeClass('hide');
                $("#childandbankinfo").addClass('hide');
                $("#show-bank-paded-info").html('交易流水号: <a href="' + url + '">' + data.data.id + '</a> 已在系统中存中，点击查看明细！');
                break;
        }
        $("#well-container").addClass('hide');
    }

    var invoiceInfoTpl = _.template($("#invoice-info").html());
    var feeCountTemplate = _.template($("#fee-count-template").html());
    var invoiceList, bankList, childList;
    backInvoiceInfo = function (data) {
        console.log(data);
        $("#well-container").removeClass('hide');
        $("#child-invoice-list").empty();
        invoiceList = data.invoiceList;
        bankList = data.bankInfo;
        childList = data.childList;
        _.each(data.invoiceList, function (val) {
            $('#child-invoice-list').append(invoiceInfoTpl(val));
        })
        //费用统计
        $("#Banktransfer_bamount").attr('disabled', 'disabled');
        $('#fee-count').empty();
    }

    var balanceAmount = 0;
    selectRow = function (_this, control) {
        var childListIds = [];
        var unpayAmount = 0;
        if (control == 1) {
            var input = $(_this).parent();
            var inputObj = $(input).find(":checkbox");
            if (inputObj.attr("checked")) {
                inputObj.attr("checked", false);
            } else {
                inputObj.attr("checked", true);
            }
        } else {
            inputObj = $(_this);
        }
        var invoice;
        var ret = true;
        var i = 0;
        $("input[name='invioceId[]']:checkbox").each(function () {
            if ($(this).attr("checked")) {
                i++;
                invoice = _.where(invoiceList, {invoiceId: $(this).val()});
                unpayAmount += invoice[0].unpay * 100 / 100;
                if (i > 1) {
                    if ((bankList.bamount - unpayAmount) < 0) {
                        inputObj.attr("checked", false);
                        ret = false;
                    }
                }
                var index = _.indexOf(childListIds, invoice[0].childid);
                if (index == -1 && $(this).attr("checked")) {
                    childListIds.push(invoice[0].childid);
                }
            }
        })
        balanceAmount = bankList.bamount - unpayAmount;
        $("#transferid").val(bankList.id);
        if (ret == true) {
            $("#fee-count").html(feeCountTemplate({
                bankInfo: bankList,
                unpayAmount: unpayAmount,
                balanceAmount: balanceAmount,
                childListIds: childListIds
            }));
        }
    }

</script>