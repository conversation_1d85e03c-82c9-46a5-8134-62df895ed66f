<?php
Yii::import('common.models.visit.*');

$_teacherstype = array();
foreach ($typeInfo as $k => $_typeInfo) {
    $_teacherstype[$k]['time'] = date("Y-m-d", $_typeInfo['time']);
    $_teacherstype[$k]['hours'] = date("H", $_typeInfo['time']);
    $_teacherstype[$k]['minutes'] = date("i", $_typeInfo['time']);
    $_teacherstype[$k]['teacher'] = $_typeInfo['teacher_name'];
}


$criteria = new CDbCriteria();
$criteria->compare('child_id', $child->id);
$childInterview = ChildInterview::model()->find($criteria);
if ($childInterview) {
    $time_hours = date("H", $childInterview->interview_time);
    $time_minutes = date("i", $childInterview->interview_time);
    $childInterview->interview_time = date("Y-m-d", $childInterview->interview_time);

} else {
    $childInterview = new ChildInterview;
}

$form = $this->beginWidget('CActiveForm', array(
    'id' => 'invoice-form',
    'enableAjaxValidation' => false,
    'action' => $this->createUrl('interview', array('childid' => $child->id)),
    'htmlOptions' => array('class' => 'J_ajaxForm', 'role' => 'form'),
)); ?>

    <form role="form">
        <div class="modal-body">
            <!--<div class="form-group text-center">
                <a href=" /*echo $this->createUrl('interviewlist') */?>" class="btn btn-info" target="_blank">查看本月所有面试情况</a>
            </div>-->

            <div class="panel panel-default">
                <div class="panel-heading"><?php echo $_teacher ?><?php echo Yii::t('interview', 'Evaluation Date') ?></div>
                <div class="panel-body">
                    <div class="col-md-4">
                        <?php $this->widget('zii.widgets.jui.CJuiDatePicker', array(
                            'model' => $childInterview,
                            'attribute' => 'interview_time',
                            'options' => array(
                                'dateFormat' => 'yy-mm-dd',
                            ),
                            'htmlOptions' => array(
                                'class' => 'form-control',
                                'placeholder' => Yii::t('management', 'Date of received payment'),
                            ),
                        )); ?>
                    </div>
                    <div class="col-md-4">
                        <?php for ($i = 8; $i < 21; $i++) {
                            $t = sprintf('%02d', $i);
                            $hours[$t] = $t;
                        };
                        echo CHtml::dropDownList("interview[hours]", $time_hours, $hours, array('class' => "form-control", 'style' => 'width:100%', 'empty' => Yii::t('teaching', 'Hour'))) ?>
                    </div>
                    <div class="col-md-4">
                        <?php for ($i = 0; $i < 60; $i += 5) {
                            $t = sprintf('%02d', $i);
                            $minutes[$t] = $t;
                        };
                        echo CHtml::dropDownList("interview[minutes]", $time_minutes, $minutes, array('class' => "form-control", 'style' => 'width:100%', 'empty' => Yii::t('teaching', 'Minute'))) ?>
                    </div>
                </div>
            </div>

        </div>
        <div class="modal-footer">
            <button type="submit"
                    class="btn btn-primary  J_ajax_submit_btn"><?php echo Yii::t('global', 'Submit') ?></button>
            <button type="button" class="btn btn-default"
                    data-dismiss="modal"><?php echo Yii::t('global', 'Cancel') ?></button>
        </div>
    </form>
<?php $this->endWidget(); ?>