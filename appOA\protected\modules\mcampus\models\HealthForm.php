<?php

/* 
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
class HealthForm extends CFormModel
{
    // text
    public $shengao = '';
    public $shengao_nianling = '';
    public $shengao_zengzhang = '';
    public $tizhong = '';
    public $tizhong_nianling = '';
    public $tizhong_zengzhang = '';
    public $xuesesu = '';
    public $kouqiang = '';
    public $tingli = '';
    public $neike = '';
    public $zuoyan = '';
    public $youyan = '';
    public $qita = '';
    public $pingjia = '';

    // radio
    public $shengzhang = '';
    public $xuechanggui = '';
    public $shili = '';
    public $tingli2 = '';
    public $quchi = '';
    public $neike2 = '';

    // text
    public $zuoweiti = '';
    public $lidingtiao = '';
    public $shimipao = '';
    public $wangqiu = '';
    public $pinghengmu = '';
    public $lianxutiao = '';

    // radio
    public $rouren = '';
    public $baofa = '';
    public $lingmin = '';
    public $shangzhi = '';
    public $pingheng = '';
    public $xietiao = '';

    public function rules()
    {
        return array(
            array('shengao,shengao_nianling,shengao_zengzhang,tizhong,tizhong_nianling,tizhong_zengzhang,xuesesu,kouqiang,tingli,neike,zuoyan,youyan,qita,pingjia,shengzhang,xuechanggui,shili,tingli2,quchi,neike2,zuoweiti,lidingtiao,shimipao,wangqiu,pinghengmu,lianxutiao,rouren,baofa,lingmin,shangzhi,pingheng,xietiao', 'safe'),
        );
    }

    /**
     * @return array customized attribute labels (name=>label)
     */
    public function attributeLabels()
    {
        return array(
            "shengao" => "身高",
            "shengao_nianling" => "身高/年龄评价",
            "shengao_zengzhang" => "增长是否合格",
            "tizhong" => "体重",
            "tizhong_nianling" => "体重/年龄评价",
            "tizhong_zengzhang" => "增长是否合格",
            "xuesesu" => "血色素",
            "kouqiang" => "口腔",
            "tingli" => "听力",
            "neike" => "内科",
            "zuoyan" => "左眼",
            "youyan" => "右眼",
            "qita" => "其他 (血压)",
            "pingjia" => "保健评价",
            "shengzhang" => "您的孩子生长",
            "xuechanggui" => "血常规",
            "shili" => "视力",
            "tingli2" => "听力",
            "quchi" => "龋齿",
            "neike2" => "内科",
            "zuoweiti" => "坐位体前屈",
            "lidingtiao" => "立定跳远",
            "shimipao" => "十米折反跑",
            "wangqiu" => "网球投掷",
            "pinghengmu" => "走平衡木",
            "lianxutiao" => "双脚连续跳",
            "rouren" => "身体柔韧性",
            "baofa" => "身体爆发力",
            "lingmin" => "身体灵敏性",
            "shangzhi" => "身体上肢及腰腹力量",
            "pingheng" => "身体平衡能力",
            "xietiao" => "身体协调性和下肢肌肉力量",
        );
    }
}
