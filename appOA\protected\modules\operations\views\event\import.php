<?php
$this->widget('ext.ivyCGridView.IvyCGridView', array(
	'id'=>'import-member-grid',
	'dataProvider'=>$dataProvider,
    'hideHeader'=>true,
    'colgroups'=>array(
        array(
            "colwidth"=>array(),
        )
    ),
	'columns'=>array(
        array(
            'name'=>'timestamp',
            'value'=>'OA::formatDateTime($data->timestamp, "medium", "short")',
        ),
        'total',
        'success',
        'fail',
        array(
            'class'=>'CButtonColumn',
            'template' => '{delete}',
            'deleteButtonUrl'=>'Yii::app()->createUrl("/operations/event/deleteImport", array("id" => $data->id))',
        ),
	),
)); ?>

<?php $form=$this->beginWidget('CActiveForm', array(
	'id'=>'log-form',
	'enableAjaxValidation'=>false,
	'htmlOptions'=>array('class'=>'J_ajaxForm'),
));
?>
<div class="pop_cont pop_table" style="height:auto;">
    <table width="100%">
        <colgroup>
            <col class="th" />
            <col />
        </colgroup>
        <tbody>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($model,'csv'); ?></th>
                <td><?php echo $form->fileField($model,'csv'); ?></td>
            </tr>
            <tr>
                <th></th>
                <td>Excel文件另存为CSV文件</td>
            </tr>
        </tbody>
    </table>
</div>
<div class="pop_bottom">
    <button type="button" class="btn fr" id="J_dialog_close"><?php echo Yii::t('global','Cancel');?></button>
    <button class="btn fr btn_submit mr10 J_ajax_submit_btn"><?php echo Yii::t('global','Submit');?></button>
</div>
<?php $this->endWidget(); ?>