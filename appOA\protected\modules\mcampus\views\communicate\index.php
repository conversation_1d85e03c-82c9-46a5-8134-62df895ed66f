<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('//mcampus/default/index')) ?></li>
        <li><?php echo CHtml::link(Yii::t('site', 'Routines'), array('//mcampus/default/index')) ?></li>
        <li class="active"><?php echo Yii::t('site', 'Daily Bulletin') ?></li>
    </ol>
    <div class="row">
        <div class="col-md-2">
            <div class="btn-group mb10">
                <button class="btn btn-primary" onclick="openEdit(0)"><?php echo Yii::t('campus', 'Create Daily Bulletin'); ?></button>
            </div>

            <div class="list-group" id="classroom-status-list">
                <a href="<?php echo $this->createUrl('//mcampus/communicate/internal'); ?>" class="list-group-item status-filter <?php if ($filter != 'history') : ?>active<?php endif; ?>"><?php echo Yii::t('global', 'Active'); ?> </a></li>
                <a href="<?php echo $this->createUrl('//mcampus/communicate/internal', array('filter' => 'history')); ?>" class="list-group-item status-filter <?php if ($filter == 'history') : ?>active<?php endif; ?>"><?php echo Yii::t('global', 'History'); ?> </a></li>
            </div>
        </div>
        <div class="col-md-10">
            <div class="panel panel-default">
                <div class="panel-body">
                    <?php
                    $baseurl = Yii::app()->params['OABaseUrl'] . "/modules/myspace/task/getthumb2.php?img=";
                    $data_js = array();
                    $c = new Dailymail();
                    $data_js[0] = $c->attributes;
                    $data_js[0]['dueDay'] = 3;
                    $data_js[0]['startdate'] = date('Y-m-d');
                    $data_js[0]['files'] = array();
                    foreach ($items as $item) :
                        $data_js[$item->id] = $item->attributes;
                        $diffDay = ($item->duedate - $item->startdate) / 86400;
                        $data_js[$item->id]['dueDay'] = !in_array($diffDay, array_keys(Dailymail::getDueDay())) ? 99 : $diffDay;
                        $data_js[$item->id]['startdate'] = CommonUtils::formatDateTime($item->startdate);
                        $data_js[$item->id]['duedate'] = CommonUtils::formatDateTime($item->duedate);
                        $data_js[$item->id]['files'] = '';
                        $bg = $item->priority == 0 ? 'bg-info' : (($item->priority == 1) ? 'bg-warning' : 'bg-danger');
                        foreach ($item->files as $file) {
                            $name = $file->notes;
                            $link = $baseurl . $file->file_name;
                            $ext = end(explode('.', $file->file_name));
                            $data_js[$item->id]['files'] .= '<tr>';
                            $data_js[$item->id]['files'] .= '<td><a target="_blank" href="' . $link . '">' . $name . '.' . $ext . '</a></td>';
                            $data_js[$item->id]['files'] .= '<td><input type="text" onchange="changFileName(this,' . $file->id . ')" class="form-control" value="' . $name . '"></td>';
                            $data_js[$item->id]['files'] .= '<td><a class="btn btn-danger btn-xs" onclick="deleteFile(this,' . $file->id . ')"><span class="glyphicon glyphicon-trash"> </span></a></td>';
                            $data_js[$item->id]['files'] .= '</tr>';
                        }
                    ?>
                        <dl class="dl-horizontal <?php echo $bg; ?>">
                            <dt>
                                <h4><?php echo date('Y/m/d', $item->startdate) ?></h4>~<h4><?php echo date('Y/m/d', $item->duedate) ?></h4>
                            </dt>
                            <dd class="p10">
                                <h4 class="pull-right">
                                    <a href="javascript:openEdit(<?php echo $item->id ?>);"><span class="glyphicon glyphicon-pencil"></span></a>
                                    <?php if ($item->stat == 20) : ?>
                                        <a href="<?php echo $this->createUrl('bulletinMgtline', array('id' => $item->id, 'stat' => 10)); ?>" title="<?php echo Yii::t('workflow', 'Online') ?>" class="J_ajax_del" data-msg="<?php echo Yii::t('workflow', 'Confirm Online?') ?>"><span class="glyphicon glyphicon-ok-circle"></span></a>
                                    <?php else : ?>
                                        <a href="<?php echo $this->createUrl('bulletinMgtline', array('id' => $item->id, 'stat' => 20)); ?>" title="<?php echo Yii::t('workflow', 'Offline') ?>" class="J_ajax_del" data-msg="<?php echo Yii::t('workflow', 'Confirm Offline?') ?>"><span class="glyphicon glyphicon-ban-circle"></span></a>
                                    <?php endif; ?>
                                    <a href="<?php echo $this->createUrl('bulletinDel', array('id' => $item->id)); ?>" title="<?php echo Yii::t('zii', 'Delete') ?>" class="J_ajax_del"><span class="glyphicon glyphicon-trash"></span></a>
                                </h4>
                                <?php if ($item->title) : ?>
                                    <p><strong><?php echo $item->title; ?></strong></p>
                                <?php endif; ?>
                                <p><?php echo $item->en_content; ?></p>
                            </dd>
                        </dl>

                    <?php endforeach; ?>
                    <div>
                        <?php
                        $this->widget('BsCLinkPager', array(
                            'pages' => $pages,
                        ));
                        ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
$this->branchSelectParams['extraUrlArray'] = array('//mcampus/communicate/internal', 'filter' => $filter);
$this->renderPartial('//layouts/common/branchSelectBottom');
?>

<!-- Modal -->
<div class="modal" id="bulletinEditModal" tabindex="1000" role="dialog">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title"><?php echo Yii::t('site', 'Daily Bulletin') ?> <small></small></h4>
            </div>
            <form class="J_ajaxForm" action="<?php echo $this->createUrl('bulletinEdit'); ?>" method="POST">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-12">
                            <div id="form-data">
                                <!--place holder-->
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer pop_bottom">
                    <button onclick="$('#bulletinEditModal').modal('hide')" type="button" class="btn btn-default pull-right"><?php echo Yii::t('global', 'Cancel'); ?></button>
                    <button type="submit" class="btn btn-primary J_ajax_submit_btn pull-right mr10"><?php echo Yii::t('global', 'Submit'); ?></button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    $(document).on('focusin', function(e) {
        if ($(e.target).closest(".tox").length) {
            e.stopImmediatePropagation();
        }
    })

    var data = <?php echo CJSON::encode($data_js); ?>;

    function openEdit(id) {
        $('#form-data').html(_.template($('#bulletin-edit-template').html(), data[id]));
        $('#Dailymail_startdate').datepicker({
            'changeMonth': true,
            'changeYear': true,
            'dateFormat': 'yy-mm-dd'
        });
        $('#Dailymail_duedate').datepicker({
            'changeMonth': true,
            'changeYear': true,
            'dateFormat': 'yy-mm-dd'
        });
        $('#Dailymail_priority input[value="' + data[id].priority + '"]').attr('checked', true);
        $('#Dailymail_cate input[value="' + data[id].cate + '"]').attr('checked', true);
        $('#Dailymail_dueDay').val(data[id].dueDay);
        if (data[id].stat == 10) {
            $('#Dailymail_stat').attr('checked', true);
        }
        $('#bulletinEditModal').modal({
            backdrop: 'static'
        });
    }

    function cDue(_this) {
        if ($(_this).val() == 99) {
            $('#duedatewarp').show();
        } else {
            $('#duedatewarp').hide();
        }
    }
    var Watermark;
    var WatermarkImg='!v1000';
    $('#bulletinEditModal').on('shown.bs.modal', function(e) {
        tinymce.init({
            selector: '#Dailymail_en_content',
            content_style: "img{max-width:100%}",
            language: '<?php echo CommonUtils::autoLang("zh_CN", "en"); ?>',
            height: 600,
            plugins: 'fullscreen image link media imagetools preview table paste code wordcount watermark-plugin',
            imagetools_cors_hosts: ['picsum.photos'],
            menubar: 'file edit view insert format help',
            contextmenu: 'link image imagetools table spellchecker lists editimage',
            toolbar: 'undo redo bold italic underline strikethrough fontselect fontsizeselect formatselect | link fullscreen preview ',
            menu: {
                insert: { title: 'Insert', items: 'image link media template codesample inserttable | charmap emoticons hr | pagebreak nonbreaking anchor toc | insertdatetime | editimage' },
            },
            toolbar_sticky: true,
            image_uploadtab: false,
            image_dimensions: false,
            automatic_uploads: true,
            paste_data_images: true,
            setup: function(editor) {
                // 实时同步编辑器内容到 selector
                editor.on('change', function() {
                    tinymce.triggerSave();
                });

            },
            // 上传文件，成功回调，失败回调，进度
            images_upload_handler: function (blobInfo, success, failure, progress) {
                handleUploadImage(blobInfo, success, failure, progress);
            },
        });
        tinymce.PluginManager.add('watermark-plugin', function (editor) {
            editor.ui.registry.addMenuItem('editimage', {
                icon: 'image',
                text: '<?php echo Yii::t("newDS", "Add/Remove Watermark");?>',
                onAction: function () {
                    if(Watermark.indexOf(WatermarkImg) == -1){
                        Watermark=Watermark+WatermarkImg
                    }else{
                        Watermark=Watermark.replace(WatermarkImg,"")
                    }
                    editor.insertContent('<div><img  style="max-width:100%"  src='+Watermark+'></div>')
                }
            });
            editor.ui.registry.addContextMenu('image', {
                update: function (element) {
                    Watermark=element.src
                    return !Watermark ? '' : 'image';
                }
            });
        });
    });
    $('#bulletinEditModal').on('hidden.bs.modal', function(e) {
        tinymce.remove("#Dailymail_en_content");
    });

    //上传文件
    function fileUpload(_this) {
        var url = '<?php echo $this->createUrl('saveUploadFile'); ?>';
        var formData = new FormData();
        formData.append('file', _this.files[0]);
        formData.append('id', $('#Dailymail_id').val());
        $.ajax({
            url: url,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(data) {
                var msg = eval('(' + data + ')');
                if (msg.state == 'success') {
                    var html = '<tr><td><a target="_blank" href="' + msg.data.url + '">' + msg.data.fileName + '</a></td>';
                    html += '<td><input type="text" onchange=changFileName(this,' + msg.data.fileId + ') class="form-control" value="' + msg.data.fileName + '"></td>';
                    html += '<td><a class="btn btn-danger btn-xs" onclick="deleteFile(this,' + msg.data.fileId + ')"><span class="glyphicon glyphicon-trash"> </span></a></td></tr>';
                    html += '<input type="hidden" name="files[]" value=' + msg.data.fileId + ' />';
                    $('#fileList > table').append(html);
                }
                $('#spaninfo').text(msg.message);
            }
        });
    }
    //删除文件
    function deleteFile(btn, id) {
        if (confirm('<?php echo Yii::t('workflow', 'Confirm delete this file?'); ?>')) {
            var url = '<?php echo $this->createUrl('deleteUploadFile'); ?>';

            $.ajax({
                url: url,
                type: 'POST',
                data: {
                    id: id
                },
                success: function(data) {
                    var msg = eval('(' + data + ')');
                    if (msg.state == 'success') {
                        $(btn).parent().parent().remove();
                    }
                    $('#spaninfo').text(msg.message);
                }
            });
        }
    }
    //修改文件名
    function changFileName(btn, id) {
        var notes = $(btn).val();
        if (notes == "") {
            return false
        }
        var url = '<?php echo $this->createUrl('changeFileName'); ?>';

        $.ajax({
            url: url,
            type: 'POST',
            data: {
                id: id,
                notes: notes
            },
            success: function(data) {
                var msg = eval('(' + data + ')');
                if (msg.state == 'success') {
                    var con = $(btn).parent().prev().children().text(msg.data);
                }
                $('#spaninfo').text(msg.message);
            }
        });
    }

    function handleUploadImage (blobInfo, success, failure, progress) {
        $.ajax({
            url: '<?php echo $this->createUrl("//mteaching/journals/getQiniuTokenSimple") ?>',
            type: "post",
            dataType: 'json',
            data: {
                isPrivate: 0,
                prefix: 'communicate',
            },
            success: function(data) {
                if (data.state == 'success') {
                    var token = data.data.token;
                    var domain = data.data.domain;
                    // 上传文件

                    var xhr = new XMLHttpRequest();
                    xhr.withCredentials = false;
                    xhr.open("post", "https://up-z0.qiniup.com");
                    xhr.upload.onprogress = function (e) {
                        progress(Math.round(e.loaded / e.total * 100) | 0);
                    };
                    xhr.onerror = function () {
                        failure('Image upload failed due to a XHR Transport error. Code: ' + xhr.status);
                    };
                    xhr.onload = function() {
                        var json;

                        if (xhr.status === 403) {
                            failure('HTTP Error: ' + xhr.status, { remove: true });
                            return;
                        }

                        if (xhr.status < 200 || xhr.status >= 300) {
                            failure('HTTP Error: ' + xhr.status);
                            return;
                        }

                        json = JSON.parse(xhr.responseText);

                        if (!json || typeof json.name != 'string') {
                            failure('Invalid JSON: ' + xhr.responseText);
                            return;
                        }
                        success( domain + "/" + json.name+WatermarkImg);
                    };
                    var formData = new FormData();
                    var file = blobInfo.blob();
                    formData.append('file', file, file.name);
                    formData.append('token', token);
                    xhr.send(formData);
                } else {
                    resultTip({
                        error: 'warning',
                        msg: data.message
                    });
                }
            },
            error: function(data) {
                resultTip({
                    error: 'warning',
                    msg: "上传失败"
                });
            },
        })
    }
</script>

<?php
$labels = Dailymail::attributeLabels();
?>
<script type="text/template" id="bulletin-edit-template">
    <input type="hidden" name="Dailymail[id]" value="<%= id%>" id="Dailymail_id">
    <?php
    $cates = Dailymail::getCate($this->branchId);
    if (in_array('ivystaff_teacher', Yii::app()->user->roles)) {
        unset($cates[0]);
        unset($cates[1]);
        unset($cates[2]);
    }
    if ($cates) : ?>
    <div class="col-md-12 form-group" model-attribute="cate">
        <?php echo CHtml::label($labels['cate'], CHtml::getIdByName('Dailymail[cate]'), array('class' => 'control-label')); ?>
        <div class="row">
            <?php echo CHtml::radioButtonList('Dailymail[cate]', '', $cates, array('separator' => '', 'template' => '<div class="col-md-4">{input} {label}</div>')); ?>
        </div>
    </div>
    <?php endif; ?>
    <div class="col-md-12 form-group" model-attribute="priority">
        <?php echo CHtml::label($labels['priority'], CHtml::getIdByName('Dailymail[priority]'), array('class' => 'control-label')); ?>
        <div class="row">
            <?php echo CHtml::radioButtonList('Dailymail[priority]', '', Dailymail::getPrioritys(), array('separator' => '', 'template' => '<div class="col-md-3">{input} {label}</div>')); ?>
        </div>
    </div>
    <div class="col-md-4 form-group" model-attribute="startdate">
        <?php echo CHtml::label($labels['startdate'], CHtml::getIdByName('Dailymail[startdate]'), array('class' => 'control-label')); ?>
        <div class="" style="z-index: 9999;">
            <?php echo CHtml::textField('Dailymail[startdate]', '<%= startdate%>', array('class' => 'form-control', 'encode' => false)); ?>
        </div>
    </div>
    <div class="col-md-4 form-group" model-attribute="dueDay">
        <?php echo CHtml::label($labels['dueDay'], CHtml::getIdByName('Dailymail[dueDay]'), array('class' => 'control-label')); ?>
        <div class="" style="margin-top: 5px">
            <?php echo CHtml::dropDownList('Dailymail[dueDay]', '', Dailymail::getDueDay(), array('class' => 'form-control', 'onchange' => 'cDue(this)')); ?>
        </div>
    </div>
    <div class="col-md-4 form-group" model-attribute="duedate" id="duedatewarp" style="<% if(dueDay != 99){%>display: none;<%}%>">
        <?php echo CHtml::label($labels['duedate'], CHtml::getIdByName('Dailymail[duedate]'), array('class' => 'control-label')); ?>
        <div class="" style="z-index: 9999;">
            <?php echo CHtml::textField('Dailymail[duedate]', '<%= duedate%>', array('class' => 'form-control', 'encode' => false)); ?>
        </div>
    </div>
    <div class="col-md-12 form-group" model-attribute="stat">

        <div class="" style="z-index: 9999;">
            <label><?php echo CHtml::checkBox('Dailymail[stat]'); ?> <?php echo Yii::t('dailymail', 'Make Online'); ?></label>
        </div>
    </div>
    <div class="col-md-12 form-group" model-attribute="title">
        <?php echo CHtml::label(Yii::t('dailymail', 'Title (optional)'), CHtml::getIdByName('Dailymail[title]'), array('class' => 'control-label')); ?>
        <div>
            <input name="Dailymail[title]" value="<%= title%>" class="form-control">
        </div>
    </div>
    <div class="col-md-12 form-group" model-attribute="en_content">
        <?php echo CHtml::label($labels['en_content'], CHtml::getIdByName('Dailymail[en_content]'), array('class' => 'control-label')); ?>
        <div class="">
            <textarea name="Dailymail[en_content]" id="Dailymail_en_content"><%= en_content%></textarea>
        </div>
    </div>
    <div class="col-xs-12">
        <!-- 显示附件 -->
        <div id="fileList">
            <table class="table">
                <thead>
                    <tr>
                        <th><?php echo Yii::t('workflow', 'Uploaded Files'); ?></th>
                        <th><?php echo Yii::t('workflow', 'Display Name'); ?></th>
                        <th>#</th>
                    </tr>
                </thead>
                <tbody>
                    <%= files%>
                </tbody>
            </table>
        </div>
        <!-- 上传 -->
        <div class="btn-group btn-group-justified" role="group">
            <input id="file" type="file" name="file" onchange="fileUpload(this)" style="display:none">
            <div class="btn-group" role="group">
                <a id="filebtn" class="btn btn-default" onclick="$('#file').click();"><?php echo Yii::t('workflow', 'Upload New File'); ?></a>
            </div>
        </div>
        <br>
        <span class="text-warning" id="spaninfo"></span>
    </div>
</script>