<?php $form=$this->beginWidget('CActiveForm', array(
    'id'=>'changedate-form',
    'enableAjaxValidation'=>false,
    'htmlOptions'=>array('class'=>'J_ajaxForm form-horizontal', 'role'=>'form'),
));
?>
<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">×</span></button>
    <h4 class="modal-title"><?php echo $modalTitle;?></h4>
</div>
<div class="modal-body">
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'appointment_date'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->textField($model,'appointment_date',array('maxlength'=>255,'class'=>'datepicker form-control', 'readonly'=>'readonly')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'appointment_time'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->textField($model,'appointment_time',array('maxlength'=>255,'class'=>'form-control')); ?>
        </div>
    </div>
</div>
<div class="modal-footer">
    <button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global','Submit');?></button>
    <button type="button" class="btn btn-default"  data-dismiss="modal"><?php echo Yii::t('global','Cancel');?></button>
</div>

<?php $this->endWidget(); ?>
<script>
    $('.datepicker').datepicker({'dateFormat':'yy-mm-dd'});
    // 回调：添加成功
    function cbVisit() {
        $('#modal').modal('hide');
        $.fn.yiiGridView.update('confirmed-visit-grid');
    }
</script>