<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site','HQ Operations'), array('/moperation'))?></li>
        <li><?php echo CHtml::link(Yii::t('site','问卷管理'), array('index'))?></li>
        <li class="active"><?php echo Yii::t('site','模版管理') ?></li>
    </ol>

    <div class="row">
        <div class="col-md-2 col-sm-2">
            <?php

            $this->widget('zii.widgets.CMenu',array(
                'items'=> $this->getSubMenu(),
                'id'=>'pageCategory',
                'htmlOptions'=>array('class'=>'nav nav-pills nav-stacked text-left background-gray'),
                'activeCssClass'=>'active',
                'itemCssClass'=>''
            ));
            ?>
        </div>
        <div class="col-md-10 col-sm-10" id='see'>
            <div class="panel panel-default">
              <div class="panel-heading">{{data.surveyTitle}}</div>
              <div class="panel-body">
                 <p>所在学校：{{titledata.schoolName}}</p>
                 <p>所在班级：{{titledata.childClass}}</p>
                 <p>孩子姓名：{{titledata.childName}}</p>
                 <p>反馈日期：{{titledata.fb_time}}</p>
                 <p>调查问卷起止时间：{{titledata.time}}</p>
                 
              </div>
              <div v-for='(item,key,index) in data.data' class="p15">
                    <h4>
                        {{index+1}}.<span v-if='item.binary_flag=="0" || item.binary_flag=="1"'>（可选填）</span>
                        <span v-if='item.binary_flag=="2" || item.binary_flag=="3" '>（必填）</span>{{item.title}}
                    </h4>
                    <ul class="list-unstyled ml15">
                        <li v-for='(items,idx) in item.data' class="p2">
                            <div class="checkbox"  v-if='item.status==2'>
                              <label>
                                <template v-for='(val,idx) in feedBackData[key]'>
                                    <input type="checkbox" :value="items.option_value" v-if="val==items.option_value" checked="checked">
                                    <template v-else>
                                        <input type="checkbox" name='radio' :value="items.option_value" v-if='status==2' disabled>
                                        <input type="checkbox" name='radio' :value="items.option_value" v-else>
                                    </template>
                                </template>
                                
                                {{items.title}}
                              </label>
                            </div>
                            <div class="radio"  v-if='item.status==1'>
                              <label>
                                <input type="radio" name='radio' :value="items.option_value" v-if="feedBackData[key]==items.option_value" checked="checked">
                                <template  v-else>
                                     <input type="radio" name='radio' :value="items.option_value" v-if='status==2' disabled>
                                     <input type="radio" name='radio' :value="items.option_value" v-else>
                                </template>
                               {{items.title}}
                              </label>
                            </div>
                        </li>
                    </ul>
                    <div class="form-group ml15" v-if='item.binary_flag==3 || item.binary_flag==1'>
                        <label for="exampleInputEmail1">备注</label>
                        <textarea class="form-control" rows="3"></textarea>
                    </div>
                 </div>
            </div>
            <p class=" pull-right">
                <button class="btn btn-primary" type="submit" v-if='status==1'>提交问卷</button>
                <button class="btn btn-primary" type="button" onclick='window.close()'>关闭</button>
            </p>
        </div>
</div>
<script>
        var datas = <?php echo json_encode($data) ?>;
        var feedBackData   = <?php echo json_encode($feedBackData) ?>;
        var titledata = <?php echo json_encode($titledata) ?>;
        var status = <?php echo json_encode($status) ?>;
        console.log(datas)
        console.log(feedBackData)
        console.log(titledata)
        console.log(status)
        var view = new Vue({
            el: "#see",
            data: {
                data: datas,
                titledata:titledata,
                feedBackData:feedBackData,
                status:status
            },
            created: function() {
            },
            methods: {
            }
        })
</script>