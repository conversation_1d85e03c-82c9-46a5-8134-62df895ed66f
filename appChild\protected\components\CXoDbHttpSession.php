<?php
/**
 * CXoDbHttpSession class
 *
 * <AUTHOR> Ran
 */
class CXoDbHttpSession extends CDbHttpSession
{
	/**
	 * @var CDbConnection the DB connection instance
	 */
	private $_db;


	/**
	 * Returns a value indicating whether to use custom session storage.
	 * This method overrides the parent implementation and always returns true.
	 * @return boolean whether to use custom storage.
	 */
	public function getUseCustomStorage()
	{
		return true;
	}


	/**
	 * Session open handler.
	 * Do not call this method directly.
	 * @param string session save path
	 * @param string session name
	 * @return boolean whether session is opened successfully
	 */
	public function openSession($savePath,$sessionName)
	{
		$db=$this->getDbConnection();
		$db->setActive(true);

		if($this->autoCreateSessionTable)
		{
			$sql="DELETE FROM {$this->sessionTableName} WHERE sess_updated <".time() - $this->getTimeout();
			try
			{
				$db->createCommand($sql)->execute();
			}
			catch(Exception $e)
			{
				$this->createSessionTable($db,$this->sessionTableName);
			}
		}
		return true;
	}

	/**
	 * Session read handler.
	 * Do not call this method directly.
	 * @param string session ID
	 * @return string the session data
	 */
	public function readSession($sess_id)
	{
		$validtime=time()-$this->getTimeout();
		$sql="
SELECT sess_data FROM {$this->sessionTableName}
WHERE sess_updated>$validtime AND sess_id=:sess_id
";
		$data=$this->getDbConnection()->createCommand($sql)->bindValue(':sess_id',$sess_id)->queryScalar();
		return $data===false?'':$data;
	}

	/**
	 * Session write handler.
	 * Do not call this method directly.
	 * @param string session ID
	 * @param string session data
	 * @return boolean whether session write is successful
	 */
	public function writeSession($id,$data)
	{
		// exception must be caught in session write handler
		// http://us.php.net/manual/en/function.session-set-save-handler.php
		try
		{
			$expire=time()+$this->getTimeout();
			$updated=time();
			$db=$this->getDbConnection();
			$sql="SELECT sess_id FROM {$this->sessionTableName} WHERE sess_id=:sess_id";
			if($db->createCommand($sql)->bindValue(':sess_id',$id)->queryScalar()===false)
				$sql="INSERT INTO {$this->sessionTableName} (sess_id, sess_data, sess_updated) VALUES (:sess_id, :sess_data, $updated)";
			else
				$sql="UPDATE {$this->sessionTableName} SET sess_updated=$updated, sess_data=:sess_data WHERE sess_id=:sess_id";
			$db->createCommand($sql)->bindValue(':sess_id',$id)->bindValue(':sess_data',$data)->execute();
		}
		catch(Exception $e)
		{
			if(YII_DEBUG)
				echo $e->getMessage();
			// it is too late to log an error message here
			return false;
		}
		return true;
	}

	/**
	 * Session destroy handler.
	 * Do not call this method directly.
	 * @param string session ID
	 * @return boolean whether session is destroyed successfully
	 */
	public function destroySession($id)
	{
		$sql="DELETE FROM {$this->sessionTableName} WHERE sess_id=:sess_id";
		$this->getDbConnection()->createCommand($sql)->bindValue(':sess_id',$id)->execute();
		return true;
	}

	/**
	 * Session GC (garbage collection) handler.
	 * Do not call this method directly.
	 * @param integer the number of seconds after which data will be seen as 'garbage' and cleaned up.
	 * @return boolean whether session is GCed successfully
	 */
	public function gcSession($maxLifetime)
	{
		$db=$this->getDbConnection();
		$db->setActive(true);
		$outdated = time() - $this->getTimeout();
		$sql="DELETE FROM {$this->sessionTableName} WHERE sess_updated < {$outdated}" ;
		$db->createCommand($sql)->execute();
		return true;
	}

	public function nativeRegenerateID($deleteOldSession=false)
	{
	    session_regenerate_id($deleteOldSession);
	}
	
	public function regenerateID($deleteOldSession=false)
	{
	    $oldID=session_id();;
	    $this->nativeRegenerateID(true);
	    $newID=session_id();
	    session_id($newID);
	    $db=$this->getDbConnection();
	
	    $sql="SELECT * FROM {$this->sessionTableName} WHERE sess_id=:id";
	    $row=$db->createCommand($sql)->bindValue(':id',$oldID)->queryRow();
	    if($row!==false)
	    {
		if($deleteOldSession)
		{
		    $sql="UPDATE {$this->sessionTableName} SET sess_id=:newID WHERE sess_id=:oldID";
		    $db->createCommand($sql)->bindValue(':newID',$newID)->bindValue(':oldID',$oldID)->execute();
		}
		else
		{
		    $row['sess_id']=$newID;
		    $db->createCommand()->insert($this->sessionTableName, $row);
		}
	    }
	    else
	    {
		// shouldn't reach here normally
		$db->createCommand()->insert($this->sessionTableName, array(
		    'sess_id'=>$newID,
		    'sess_updated'=>time(),
		));
	    }
	}	
}
