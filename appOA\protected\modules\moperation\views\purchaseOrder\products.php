<?php 
$billing_type = <<<EOD
1.预付款(30%/50%/100%)
甲方支付货款总金额的30%作为预付款￥xxx，全部货品经甲乙双方验收合格之后、付款之前，乙方提供给甲方订单货款的全额（即总金额的100%）正式发票，人民币：￥xxx，大写：壹万肆仟壹佰柒拾。经甲方查验无误后，支付乙方当笔订单65%货款，人民币：￥xxx，大写：玖仟贰佰壹拾元零伍角。乙方将合同总金额的5%作为质量保证金，当笔订单收货验收合格之日起一个自然年内无质量问题，经双方签字确认无误后，甲方于十日内将质保金无息返还乙方，金额为￥xxx元，人民币大写：x佰x捌元x角。
2.货到付款
全部货品经甲乙双方验收合格之后、付款之前，乙方提供给甲方订单货款的全额（即总金额的100%）正式发票，人民币：￥xxx，大写：x万x仟x佰x拾。经甲方查验无误后，支付乙方当笔订单100%货款，人民币：￥9210.50，大写：玖仟贰佰壹拾元零伍角。
EOD;
$quality_aftersale = <<<EOD
1.本订单所订货品质量标准受xxxx年xx月签订的采购框架合同中约定的相关条款约束和保护。
2.本订单所订货品的维护、维修及售后服务受xxxx年xx月签订的采购框架合同中约定的相关条款约束和保护。
1.本订单所订货品质量标准需符合国家质量安全标准。
2.本订单所订货品的维护、维修及售后服务供方48小时内相应需方要求。
EOD;
$content = "联络人：$application->content ；校园电话：$schoolInfo->tel"
 ?>
 <style>
.cursor{
	cursor:pointer;
 
}
.radio-inline + .radio-inline, .checkbox-inline + .checkbox-inline { 
    margin-left:0; 
}
.form-horizontal .radio, .form-horizontal .checkbox, .form-horizontal .radio-inline, .form-horizontal .checkbox-inline {
     margin-right: 10px
}
</style>
<div class="modal-header">
	<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
	<h4 class="modal-title">选择商品：</h4>
</div>
	<?php $form = $this->beginWidget('CActiveForm',array(
		'id'=>'purchase-order-form',
		// 'enableAjaxValidation'=>false,
		'htmlOptions'=>array('class'=>'J_ajaxForm form-horizontal', 'role'=>'form'),
	)) ?>
<div class="modal-body">
	<h4 class="text-center">订单信息</h4>
		<!-- 订单标题 -->
		<div class="form-group">
			<label class="col-xs-2 control-label"><?php echo $form->labelEx($orderModel,'title'); ?></label>
			<div class="col-xs-8">
				<?php echo $form->textField($orderModel,'title',array('maxlength'=>255,'class'=>'form-control')); ?>
			</div>
		</div>
		<!-- 订单标题 -->
		<div class="form-group">
			<label class="col-xs-2 control-label"><?php echo $form->labelEx($orderModel,'pid'); ?></label>
			<div class="col-xs-8">
				<?php echo $form->textField($orderModel,'pid',array('maxlength'=>255,'class'=>'form-control')); ?>
			</div>
		</div>
		<!-- 供应商 -->
		<div class="form-group">
			<label class="col-xs-2 control-label"><?php echo $form->labelEx($orderModel,'vendorid'); ?></label>
			<div class="col-xs-4">
				<?php echo CHtml::dropDownList('vendorType','',$vendorTypeArr,array('maxlength'=>255,'class'=>'form-control','onChange'=>"choseVendor(this)")); ?>
			</div>
			<div class="col-xs-4">
				<?php echo $form->dropDownList($orderModel,'vendorid',array(),array('maxlength'=>255,'class'=>'form-control','id'=>'vendor')); ?>
			</div>
		</div>
		<!-- 订购校园 -->
		<div class="form-group">
			<label class="col-xs-2 control-label"><?php echo $form->labelEx($orderModel,'school_name'); ?></label>
			<div class="col-xs-8">
				<?php echo $form->textField($orderModel,'school_name',array('maxlength'=>255,'class'=>'form-control','value'=>$schoolInfo->title_cn)); ?>
			</div>
		</div>
		<?php echo CHtml::hiddenField('PurchaseOrder[schoolid]',$schoolInfo->branchid); ?>
		<!-- 交货日期 -->
		<div class="form-group">
			<label class="col-xs-2 control-label"><?php echo $form->labelEx($orderModel,'delivery_timestamp'); ?></label>
			<div class="col-xs-8">
				<?php echo $form->textField($orderModel,'delivery_timestamp',array('maxlength'=>255,'class'=>'form-control','value'=>date('Y-m-d',$application->delivery_timestamp))); ?>
			</div>
		</div>
		<!-- 交货地点 -->
		<div class="form-group">
			<label class="col-xs-2 control-label"><?php echo $form->labelEx($orderModel,'delivery_place'); ?></label>
			<div class="col-xs-8">
				<?php echo $form->textField($orderModel,'delivery_place',array('maxlength'=>255,'class'=>'form-control','value'=>$schoolInfo->address_cn)); ?>
			</div>
		</div>
		<!-- 付款方式 -->
		<div class="form-group">
			<label class="col-xs-2 control-label"><?php echo $form->labelEx($orderModel,'pay_type'); ?></label>
			<div class="col-xs-8">
				<?php echo $form->dropDownList($orderModel,'pay_type',array('网银'=>'网银','现金'=>'现金','支票'=>'支票'),array('maxlength'=>255,'class'=>'form-control')); ?>
			</div>
		</div>
		<!-- 发票抬头 -->
		<div class="form-group">
			<label class="col-xs-2 control-label"><?php echo $form->labelEx($orderModel,'invoice_title'); ?></label>
			<div class="col-xs-8">
				<?php echo $form->textField($orderModel,'invoice_title',array('maxlength'=>255,'class'=>'form-control','value'=>$schoolInfo->official_name)); ?>
			</div>
		</div>
		<!-- 结算方式 -->
		<div class="form-group">
			<label class="col-xs-2 control-label"><?php echo $form->labelEx($orderModel,'billing_type'); ?></label>
			<div class="col-xs-8" height="100px">
				<?php echo $form->textArea($orderModel,'billing_type',array('class'=>'form-control','rows'=>12,'value'=>$billing_type)); ?>
			</div>
		</div>
		<!-- 联络信息 -->
		<div class="form-group">
			<label class="col-xs-2 control-label"><?php echo $form->labelEx($orderModel,'content'); ?></label>
			<div class="col-xs-8">
				<?php echo $form->textArea($orderModel,'content',array('maxlength'=>255,'class'=>'form-control',
					'value'=>$content
				)); ?>
			</div>
		</div>
		<!-- 质量及售后 -->
		<div class="form-group">
			<label class="col-xs-2 control-label"><?php echo $form->labelEx($orderModel,'quality_aftersale'); ?></label>
			<div class="col-xs-8">
				<?php echo $form->textArea($orderModel,'quality_aftersale',array('class'=>'form-control','rows'=>4,'value'=>$quality_aftersale)); ?>
			</div>
		</div>
        <?php echo CHtml::hiddenField('PurchaseOrder[aid]',$application->id); ?>	
	<div id='tablelist' v-if='tabledata.length!=0'>
	<h4 class="text-center">选择商品</h4>
		<div class="list-group">
		<p class="col-xs-2 list-group">
		<label class="checkbox-inline list-group">总价:<span id="total_price">¥ {{total_price}}</span></label>
		</p>
		<p class="col-xs-10">
		<template v-for='(data,idx) in clist'>
			<label class="checkbox-inline">
		  <input type="checkbox" id="inlineCheckbox1" @click='checked()' :value="data.id" v-model='clistcheck'>{{data.title}}
		</label>
		</template>
		<label class="checkbox-inline">
		  <input type="checkbox" id="inlineCheckbox1" @click='nochecked()' value="1" v-model='nolistcheck'>全选无供应商信息
		</label>
		</p>
		</div>
		<div class="clearfix"></div>
		<div class="panel panel-default" v-for='(lists,indexs,id) in tabledata'>
		<div class="panel-heading">
		{{title[indexs]}}
		</div>
       <table class="table"  >
       <colgroup><col width="5"><col width="200"><col width="200"><col width="50"><col width="60"><col width="140"></colgroup>
         <thead>
	       	 <tr>
	       	    <th><input type="checkbox" :value="indexs"  @click="check_all(indexs)" v-model='allcheck'></th>
	       	 	<th>校园入园材料</th>
	       	 	<th>供货商</th>
	       	 	<th>单价</th>
	       	 	<th>订单总数</th>
	       	 	<th>剩余数量</th>
	       	 </tr>
       	 </thead>
       	 <tbody>
       	 	<tr v-for='(list,index,id) in lists'> 	 	
       	 	    <td>
       	 	       <p v-if='list.orderNum<=0'>
       	 	      	 <input type="checkbox" id="inlineCheckbox1" name="products[]" :value="list.id" disabled="disabled" >
       	 	       </p>
       	 	        <p v-else>
       	 	      	  <input type="checkbox" id="inlineCheckbox1" name="products[]" :value="list.id" v-model='tablecheck' @click='listchecked(indexs,$event)'>
       	 	       </p>      	 	    	
       	 	    </td>
       	 		<td normal="normal">
	       	 		<p>{{list.name}}</p>
	                <div v-if='list.order.length>0'>
	                <p class="pull-left"> 已生成订单:</p>
	                <template  v-for='(ord,id) in list.order'>
                       <p class="pull-left">

	                	<a :href="ord.url"  target="_blank" class="mr15"> <span class="label label-info">{{ord.numbering}}-{{ord.num}}</span></a>
	                	</p>
	                </template>	                
	                </div>
       	 		</td>
       	 		<td>
       	 		<template v-for='(datas,id) in clist'>
       	 		<template v-for='(data,idx) in list.supplier'>
       	 			<p v-if='data==datas.id'>{{datas.title}}</p>
       	 		</template>
       	 		</template>
       	 		</td>
       	 		<td><input type="text" :name=" ''+ list.id + '_price'" :value='list.price' class="hidden"> {{list.price}}</td>
       	 		<td><input type="text" class="hidden"> {{list.num}}</td>
       	 		<td>      	 		
                 <div class="input-group">
	                  <div class="input-group-addon cursor" @click="reduce(list)">-</div>
				      <input type="number" class="form-control" id="exampleInputAmount" :name=" ''+ list.id + '_num'" :value="list.orderNum" :max='list.orderNum' min='1' @input="numberChange($event,list.orderNum,list)" @blur='num($event,list.orderNum)'>
				      <div class="input-group-addon cursor" @click="add_num(list)">+</div>
			      </div>                
       	 		</td>
       	 	</tr>
       	 </tbody>
       </table>
       </div>
	</div>
</div>
<div class="modal-footer">
	<label class="col-xs-2 control-label"></label>
	<button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global','Submit');?></button>
	<button type="button" class="btn btn-default"  data-dismiss="modal"><?php echo Yii::t('global','Cancel');?></button>
</div>
	<?php $this->endWidget(); ?>
<script>
	$('#PurchaseOrder_delivery_timestamp').datepicker({'dateFormat':'yy-mm-dd'});
	$(document).ready(function(){
		vendor = eval(<?php echo json_encode($vendorArr);?>);
	});
	//批量选择商品
	function chosePro(inp,cid) {
		if($(inp).attr("checked")=='checked'){
			$('.cate_'+cid).attr("checked","checked");
		} else{
			$('.cate_'+cid).attr("checked",false);
		}
		countPrice();
	}
	//筛选外包公司
	function choseVendor(inp) {
		var vendorTypeId = $(inp).val();
		var vendorChosed = vendor[vendorTypeId];
		var str = '';
		$.each(vendorChosed,function(i,value) {
			str += '<option value='+i+'>'+value+'</option>';
		})
		$("#vendor").html(str);
	}
	// 计算商品总额
	function countPrice() {
		var totalPrice = 0;
		$('input').each(function() {
			if ($(this).attr("checked")=='checked') {
				var pid = $(this).val();
				if (pid != 'on') {
					totalPrice += parseFloat($('#'+pid+'_price').val()) * parseInt($('#'+pid+'_num').val());
				}
			}
		})
		$('#total_price').text(totalPrice.formatMoney());
		$('#PurchaseOrder_total_price').val(totalPrice);
	}
	//转换货币格式
	Number.prototype.formatMoney = function (places, symbol, thousand, decimal) {
	    places = !isNaN(places = Math.abs(places)) ? places : 2;
	    symbol = symbol !== undefined ? symbol : "¥ ";
	    thousand = thousand || ",";
	    decimal = decimal || ".";
	    var number = this,
	        negative = number < 0 ? "-" : "",
	        i = parseInt(number = Math.abs(+number || 0).toFixed(places), 10) + "",
	        j = (j = i.length) > 3 ? j % 3 : 0;
	    return symbol + negative + (j ? i.substr(0, j) + thousand : "") + i.substr(j).replace(/(\d{3})(?=\d)/g, "$1" + thousand) + (places ? decimal + Math.abs(number - i).toFixed(places).slice(2) : "");
	};
	var companyData = <?php echo json_encode($companyData);?>;
	var productsData = <?php echo json_encode($productsData);?>;
	var numadd = <?php echo json_encode($productsData);?>;
	var cateArr = <?php echo json_encode($cateArr);?>;
	var lists = new Vue({
		el: "#tablelist",
		data: {
			clist: companyData,
			tabledata: productsData,
			numdata: numadd,
			title: cateArr,
			clistcheck: [],//公司
			tablecheck: [],//表格
			nolistcheck: [],//无供应商
			allcheck: []//全选
		},
		computed: {
			//总价
			total_price() {
				var price = 0.00　　　　　　　　　　　　　　　　　　　　　　　
				var checklist = []
				for(key in this.tabledata) {
					for(var j = 0; j < this.tabledata[key].length; j++) {
						for(var i = 0; i < this.tablecheck.length; i++) {
							if(this.tablecheck[i] == this.tabledata[key][j].id) {
								checklist.push(this.tabledata[key][j])
							}
						}
					}
				}
				for(var i = 0; i < checklist.length; i++) {
					price += Number(checklist[i].price) * Number(checklist[i].orderNum)
				}
				return price.toFixed(2)
			},
		},
		methods: {
			//减少
			reduce(cart) {
				if(cart.orderNum <0){
                    return
				}else if(cart.orderNum == 0) {
					cart.orderNum = 0
					return
				}else if(cart.orderNum <= 1){
                   cart.orderNum = 1
				}else {
					cart.orderNum--
				}
			},
			// 商品累加
			add_num(cart) {
				if(cart.orderNum <0){
                    return
				}
				var list = ''
				for(key in this.numdata) {
					for(var j = 0; j < this.numdata[key].length; j++) {
						if(cart.id == this.numdata[key][j].id) {
							list = this.numdata[key][j]
						}
					}
				}
				if(cart.orderNum >= list.orderNum) {
					cart.orderNum = list.orderNum
				} else {
					cart.orderNum++
				}
			},
			//订单总和
			parseint(obj) {
				num = 0
				for(var i = 0; i < obj.length; i++) {
					num += parseInt(obj[i].num)
				}
				return num
			},
			//全选是否选中
			listchecked(id, e) {
				var check = e.target.checked
				for(var i = 0; i < this.allcheck.length; i++) {
					if(id == this.allcheck[i]) {
						if(!check) {
							Vue.delete(this.allcheck, i);
						}
					}
				}
			},
			//输入框填写
			numberChange(e, id, list) {
				if(parseInt(e.target.value) > parseInt(id)) {
					e.target.value = id;
				}
				 var reg = /^[0]+[0-9]*$/gi;
				if(reg.test(e.target.value)){
				 	e.target.value=''
				 }
			},
			num(e, id){
				if(e.target.value==''){
					e.target.value=id
				}
			},
			//公司
			checked() {
				var check = []
				for(key in this.tabledata) {
					for(var j = 0; j < this.tabledata[key].length; j++) {
						for(k = 0; k < this.tabledata[key][j].supplier.length; k++) {
							for(var i = 0; i < this.clistcheck.length; i++) {
								if(this.clistcheck[i] == this.tabledata[key][j].supplier[k]) {
									if(this.tabledata[key][j].num > this.parseint(this.tabledata[key][j].order)) {
										check.push(this.tabledata[key][j].id)
									}
								}
							}
						}
					}
				}
				this.tablecheck = Array.from(new Set(check))
				this.allcheck = []
				this.nolistcheck = []
			},
			//无公司
			nochecked() {
				var check = []
				for(key in this.tabledata) {
					for(var j = 0; j < this.tabledata[key].length; j++) {
						if(this.tabledata[key][j].supplier.length == 0) {
							if(this.tabledata[key][j].num > this.parseint(this.tabledata[key][j].order)) {
								check.push(this.tabledata[key][j].id)
							}
						}
					}
				}
				if(this.nolistcheck.length > 0) {
					this.tablecheck = check
					this.allcheck = []
					this.clistcheck = []
				} else {
					for(var i = 0; i < this.tablecheck.length; i++) {
						for(var j = 0; j < check.length; j++) {
							if(this.tablecheck[i] == check[j]) {
								Vue.delete(this.tablecheck, i);
							}
						}
					}
				}
			},
			//全选
			check_all(id) {
				var checked = []
				for(key in this.tabledata) {
					for(var i = 0; i < this.allcheck.length; i++) {
						if(key == this.allcheck[i]) {
							for(var j = 0; j < this.tabledata[key].length; j++) {
								if(this.tabledata[key][j].num > this.parseint(this.tabledata[key][j].order)) {
									checked.push(this.tabledata[key][j].id)
								}
							}
						}
					}
				}
				this.tablecheck = Array.from(new Set(checked))
				this.nolistcheck = []
				this.clistcheck = []
			}
		}
	})
function cbOrder() {
	    $('#modal').modal('hide');
	    $.fn.yiiGridView.update('purchase-order');
	}
</script>