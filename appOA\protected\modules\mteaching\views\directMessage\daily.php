<style>
    [v-cloak] {
        display: none;
    }
    .p8{
        padding:24px
    }
    .input{
        margin:8px 0
    }
    .dept{
        font-size: 14px;
        color: #333;
        padding: 8px;
    }
    .dept:hover{
        background: #F2F3F5;
        border-radius: 4px;
        cursor: pointer;
    }
    .overFlow{
        max-height:240px;
        margin-top:8px
    }
    .scroll-box::-webkit-scrollbar-thumb {
        /*滚动条里面小方块*/
        border-radius   : 10px;
        background-color: #ccc !important;
        background-image: none !important
    }
    .scroll-box::-webkit-scrollbar-track {
        /*滚动条里面轨道*/
        box-shadow   : inset 0 0 5px rgba(0, 0, 0, 0);
        background   : #fff;
        border-radius: 10px;
        border:none
    }
    .footer{
        border-top: 1px solid #E5E6EB;
        padding: 12px 15px;
        position: absolute;
        width: 100%;
        bottom: 0;
    }
    .list_day{
        padding:24px
    }
    .el-date-table th{
        text-align:center
    }
    .goods{
        background: #F7F7F8;
        border-radius: 4px;
        padding:16px;
    }
    .goods{
        background: #F7F7F8;
        border-radius: 4px;
        padding:16px;
    }
    .font500{
        font-weight:500
    }
</style>
<link href="<?php echo Yii::app()->theme->baseUrl; ?>/css/bootstrap.css" rel="stylesheet">
<link href="<?php echo Yii::app()->theme->baseUrl; ?>/css/css.css" rel="stylesheet">
<link href="<?php echo Yii::app()->themeManager->baseUrl; ?>/base/js/element/index.css" rel="stylesheet">
<div id='container_day'>
    <div class='list_day' >
        <!-- <el-alert
            title="<?php echo Yii::t("newDS", "Nursery& prek only");?>"
            type="info"
            :closable="false"
            class='color6'
            show-icon>
        </el-alert> -->
        <div class='flex align-items'>
            <!-- <span class='font14 color4'><?php echo Yii::t("labels", "Date");?>：</span>  -->
            <el-date-picker
                size='small'
                class='flex1'
                v-model="date"
                type="date"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                placement="bottom-start"
                placeholder="<?php echo Yii::t('ptc','Select a date') ?>">
            </el-date-picker>
        </div>
        <div class='mb12 mt24 flex align-items'>
            <el-switch
                v-model="record"
                active-color="#4D88D2"
                inactive-color="#CCCCCC">
            </el-switch>
            <span class='font14 color3 ml8 font500'><?php echo Yii::t("newDS", "Daily Note");?></span>
        </div>
        <div class='goods' v-if='record'>
            <div class='flex'>
                <span><img  style='width:24px;height:24px' src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/lunch.png' ?>"></span>
                <div class='flex1 ml10'>
                    <div class='font14 color3'><?php echo Yii::t("newDS", "Today at lunch I…");?></div>
                    <div class='mt16'>
                        <label class="radio-inline">
                            <input type="radio"  value="1" v-model='lunch' > <?php echo Yii::t("newDS", "Ate a lot");?>
                        </label>
                        <label class="radio-inline">
                            <input type="radio" value="2" v-model='lunch'  > <?php echo Yii::t("newDS", "Ate enough");?>
                        </label>
                        <label class="radio-inline">
                            <input type="radio"  value="3" v-model='lunch'  > <?php echo Yii::t("newDS", "Ate a little");?>
                        </label>
                    </div>
                </div>
            </div>
            <div class='flex mt20'>
                <span><img  style='width:24px;height:24px' src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/cup.png' ?>"></span>
                <div class='flex1 ml10'>
                    <div class='font14 color3'><?php echo Yii::t("newDS", "Today I drink water…");?></div>
                    <div class='mt16'>
                        <label class="radio-inline">
                            <input type="radio"  value="1" v-model='cup'  > <?php echo Yii::t("newDS", "Drink a lot");?>
                        </label>
                        <label class="radio-inline">
                            <input type="radio" value="2" v-model='cup'  > <?php echo Yii::t("newDS", "Drink enough");?>
                        </label>
                        <label class="radio-inline">
                            <input type="radio"  value="3" v-model='cup'  > <?php echo Yii::t("newDS", "Drink a little");?>
                        </label>
                    </div>
                </div>
            </div>
            <div class='flex mt20'>
                <span><img  style='width:24px;height:24px' src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/bed.png' ?>"></span>
                <div class='flex1 ml10'>
                    <div class='font14 color3'><?php echo Yii::t("newDS", "At nap time I…");?></div>
                    <div class='mt16'>
                        <label class="radio-inline">
                            <input type="radio"  value="1" v-model='bed'  > <?php echo Yii::t("newDS", "Slept 1-2hrs");?>
                        </label>
                        <label class="radio-inline">
                            <input type="radio" value="2" v-model='bed'  > <?php echo Yii::t("newDS", "Slept less than 1 hour");?>
                        </label>
                        <label class="radio-inline">
                            <input type="radio"  value="3" v-model='bed'  > <?php echo Yii::t("newDS", "Didn't sleep");?>
                        </label>
                    </div>
                </div>
            </div>
            <div class='flex mt20'>
                <span><img  style='width:24px;height:24px' src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/toilet.png' ?>"></span>
                <div class='flex1 ml10'>
                    <div class='font14 color3'><?php echo Yii::t("newDS", "Did I go poo-poo ?");?></div>
                    <div class='mt16'>
                        <label class="radio-inline">
                            <input type="radio"  value="1" v-model='toilet' > <?php echo Yii::t("newDS", "Yes, it's normal");?>
                        </label>
                        <label class="radio-inline">
                            <input type="radio" value="2" v-model='toilet'  > <?php echo Yii::t("newDS", "Yes, it's hard");?>
                        </label>
                        <label class="radio-inline">
                            <input type="radio"  value="3" v-model='toilet'  > <?php echo Yii::t("newDS", "No");?>
                        </label>
                    </div>
                </div>
            </div>
        </div>
        <div class='mb12 mt24 flex align-items'>
            <el-switch
                v-model="goods"
                active-color="#4D88D2"
                inactive-color="#CCCCCC">
            </el-switch>
            <span class='font14 color3 ml8 font500'><?php echo Yii::t("newDS", "Reminder: Supplies Needed");?></span>
        </div>
        <div class='goods'  v-if='goods'>
            <div>
                <label class="checkbox-inline">
                    <input type="checkbox" v-model='goodsList' value="0"> <?php echo Yii::t("newDS", "Diapers");?>
                </label><label class="checkbox-inline ml24">
                    <input type="checkbox" v-model='goodsList' value="1"> <?php echo Yii::t("newDS", "Wipes");?>
                </label><label class="checkbox-inline ml24">
                    <input type="checkbox" v-model='goodsList' value="2"> <?php echo Yii::t("newDS", "Sunscreen");?>
                </label><label class="checkbox-inline ml24">
                    <input type="checkbox" v-model='goodsList' value="3"> <?php echo Yii::t("newDS", "Clothes");?>
                </label>
            </div>
            <div class='flex align-items mt12' style='height:30px'>
                <label class="checkbox-inline">
                    <input type="checkbox" v-model='otherCheck' value="1"> <?php echo Yii::t("newDS", "Other");?>
                </label>
                <input type="text" v-if='otherCheck' class="form-control flex1 ml16" placeholder="<?php echo Yii::t("leave", "Input");?>" v-model='other'>
            </div>
        </div>
    </div>
    <div class='flex footer  align-items'>
        <div class='flex1'>
            <span class='msg' style='color:#F76D65'></span>
        </div>
        <div>
            <button type="button" class="btn btn-default" @click='cancelData()'><?php echo Yii::t("global", "Cancel");?></button>
            <button type="button" class="btn btn-primary ml10" @click='saveData()'><?php echo Yii::t("global", "OK");?></button>
        </div>
    </div>
</div>
<script src="<?php echo Yii::app()->themeManager->baseUrl; ?>/base/js/element/vue.global.js"></script>
<script src="<?php echo Yii::app()->themeManager->baseUrl; ?>/base/js/element/index.js"></script>
<script src="<?php echo Yii::app()->themeManager->baseUrl; ?>/base/js/element/en.js"></script>
<script>
    window.onload = function() {
        var data = window.location.search.substring(1);
        var params = new URLSearchParams(data);
        if(params.get('date')){
            container_day.date = params.get('date');
        }
        container_day.lunch = params.get('lunch');
        container_day.cup = params.get('cup');
        container_day.bed = params.get('bed');
        container_day.toilet = params.get('toilet');
        if(params.get('goodsList')){
            container_day.goodsList = params.get('goodsList').split(",").map(Number)
        }
        if(params.get('other')){
            container_day.other = params.get('other');
        }
        if(container_day.goodsList.length>0 || container_day.other!=''){
            container_day.goods=true
        }
        if(container_day.other!=''){
            container_day.otherCheck=true
        }
        if(container_day.lunch!='' || container_day.cup!=''){
            container_day.record=true
        }else{
            container_day.record=false
        }
    };
     var container_day = new Vue({
        el: "#container_day",
        data: {
            date:'',
            lunch:'',
            cup:'',
            bed:'',
            toilet:'',
            record:true,
            goods:false,
            goodsList:[],
            other:'',
            otherCheck:false
        },
        watch: {
        },
        computed: {
            
        },
        created: function() {
            if(this.date==''){
                const now = new Date();
                const year = now.getFullYear();
                let month = now.getMonth() + 1;
                let day = now.getDate();
                month = month < 10 ? '0' + month : month;
                day = day < 10 ? '0' + day : day;
                this.date=year + "-" + month + "-" + day
            }
            if('<?php echo Yii::app()->language;?>'!='zh_cn'){
                ELEMENT.locale(ELEMENT.lang.en)
            }
            window.parent.postMessage({
                mceAction: 'loading',
                content: {}
            }, '*');
        },
        methods: {
            cancelData(){
                window.parent.postMessage({
                    mceAction: 'cancel',
                    content: {}
                }, '*');
            },
            saveData(){
                if(this.date==null){
                    $('.msg').html('<?php echo Yii::t("newDS", "Select a date");?>')
                    return
                }
                if(!this.record && !this.goods){
                    $('.msg').html('<?php echo Yii::t("newDS", "Click to choose edit daily note or reminder");?>')
                    return
                }
                if(this.record){
                    if(this.lunch==null || this.lunch==''){
                        $('.msg').html('<?php echo Yii::t("newDS", "Please select");?> <?php echo Yii::t("newDS", "Today I drink water…");?>')
                        return
                    }
                    if(this.cup==null || this.cup==''){
                        $('.msg').html('<?php echo Yii::t("newDS", "Please select");?> <?php echo Yii::t("newDS", "At nap time I…");?>')
                        return
                    }
                    if(this.bed==null || this.bed==''){
                        $('.msg').html('<?php echo Yii::t("newDS", "Please select");?> <?php echo Yii::t("newDS", "At nap time I…");?>')
                        return
                    }
                    if(this.toilet==null || this.toilet==''){
                        $('.msg').html('<?php echo Yii::t("newDS", "Please select");?> <?php echo Yii::t("newDS", "Did I go poo-poo ?");?>')
                        return
                    }
                }
                if(this.goods){
                    if(this.goodsList.length==0 && !this.otherCheck ){
                        $('.msg').html('<?php echo Yii::t("newDS", "Please select");?> <?php echo Yii::t("newDS", "Reminder: Supplies Needed");?>')
                        return
                    }
                    if(this.otherCheck && this.other.trim()==''){
                        $('.msg').html('<?php echo Yii::t("leave", "Input");?> <?php echo Yii::t("newDS", "Other");?>')
                        return
                    }
                }
                this.postMessage()
            },
            postMessage(){
                if(this.other.trim()=='' && this.goodsList.length==0){
                    var goods=false
                }else{
                    var goods=this.goods
                }
                this.checkData={
                    record:this.record,
                    date:this.date,
                    lunch:this.lunch,
                    cup:this.cup,
                    bed:this.bed,
                    toilet:this.toilet,
                    goods:goods,
                    goodsList:this.goodsList.sort((a, b) => a - b),
                    other:this.otherCheck?this.other.trim():''
                }
                let that=this
                if((/Safari/.test(navigator.userAgent) && !/Chrome/.test(navigator.userAgent))){
                    window.parent.postMessage({
                        mceAction: 'saveData',
                        content:that.checkData
                    }, '*'); 
                }else{
                    window.parent.postMessage({
                        mceAction: 'saveData',
                        content: that.checkData
                    }, '*');
                }
            },
        }
    })
</script>
