<?php

/*
 * 退费审核控制器
 */
class RefundController extends BranchBasedController
{
    public function createUrl($route, $params = array(), $ampersand = '&')
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        return parent::createUrl($route, $params, $ampersand);
    }

    public function init(){
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'campusOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site', 'Campus Operations');

        //初始化选择校园页面
        $this->branchSelectParams['hideOffice'] = true;
        $this->branchSelectParams['urlArray'] = array('//mcampus/refund/lunchConfirm');
    }

    /*
     * 午餐退费列表
     */
    public function actionLunchConfirm(){
        Yii::import('common.models.invoice.RefundLunch');
        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/underscore-min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/backbone-min.js');
        $this->render('lunchconfirm');
    }

    /*
     * 午餐退费审核保存
     */
    public function actionSaveLunchConfirm(){
        Yii::import('common.models.invoice.*');
        $childId = Yii::app()->request->getPost('childid', 0);
        $skey = Yii::app()->request->getPost('skey', null);
        $ids = Yii::app()->request->getPost('ids', null);
        if (Yii::app()->request->isAjaxRequest && isset($ids)){
            //安全验证
            if ($skey != md5(OA::SECURITY_KEY.$childId.implode(',', $ids))){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '非法操作');
                $this->showMessage();
            }

            $unioionId = 'lunch_' . $childId;
            if (Yii::app()->cache->get($unioionId) !== false) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '操作过于频繁');
                $this->showMessage();
            } else {
                Yii::app()->cache->set($unioionId, 1, 60);
            }

            //验证是否有重复目标日期
            $sumAmount = 0;
            $repeat = true;
            $command = Yii::app()->db->createCommand();
            $command->from(RefundLunch::model()->tableName());
            $command->where('childid=:childid',array(':childid'=>$childId));
            $repeatArr = $command->andWhere(array('in','id',$ids))->queryAll();
            if (count($repeatArr)){
                foreach ($repeatArr as $val){
                    if ($val['child_credit_id'] != 0) {
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', '审核的目标天中有重复日期，请联系IT人员');
                        $this->showMessage();
                    }
                    $repeatList[$val['target_timestamp']] = $val['target_timestamp'];
                    $sumAmount += $val['amount'];
                    $calendarId = $val['yid'];
                    $datestr[] = OA::formatDateTime($val['target_timestamp']);
                    $amountList[$val['id']] = $val['amount'];
                }
                if (count($ids) == count($repeatList)){
                    $repeat = false;
                }
                $datestr = "退".count($ids)."天的餐费";
            }
            if ($repeat === true){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '审核的目标天中有重复日期，请联系IT人员');
                $this->showMessage();
            }
            //计算个人帐户流水余额
            $childModel = ChildProfileBasic::model()->findByPk($childId);
            $balanceAmount = ChildCredit::model()->getChildCredit($childId);
            if (abs($balanceAmount - $childModel->credit) > 0.001){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '个人账号的余额与明细不符，请联系IT人员');
                $this->showMessage();
            }
            //事务操作数据库（ivy_refund_lunch，ivy_invoice_invoice，ivy_invoice_transaction，ivy_child_credit，ivy_child_profile_basic）
            $transaction = Yii::app()->db->beginTransaction();
            try{
                //insert ivy_invoice_invoice
                $invoiceModel = new Invoice();
                $invoiceModel->amount = $sumAmount;
                $invoiceModel->original_amount = $sumAmount;
                $invoiceModel->schoolid = $childModel->schoolid;
                $invoiceModel->classid = $childModel->classid;
                $invoiceModel->childid = $childId;
                $invoiceModel->calendar_id = $calendarId;
                $invoiceModel->payment_type = 'lunch';
                $invoiceModel->inout = 'out';
                $invoiceModel->fee_type = 0;
                $invoiceModel->title = 'Lunch Refund (午餐退费)';
                $invoiceModel->userid = Yii::app()->user->getId();
                $invoiceModel->timestamp = time();
                $invoiceModel->status = Invoice::STATS_PAID;
                $invoiceModel->memo = $datestr;
                if (!$invoiceModel->save()){
                    $transaction->rollback();
                    return false;
                }
                //insert ivy_invoice_transaction
                $tranModel = new InvoiceTransaction();
                $tranModel->invoice_id = $invoiceModel->invoice_id;
                $tranModel->amount = $sumAmount;
                $tranModel->inout = 'out';
                $tranModel->timestampe = time();
                $tranModel->childid = $childId;
                $tranModel->calendar_id = $calendarId;
                $tranModel->classid = $childModel->classid;
                $tranModel->schoolid = $childModel->schoolid;
                $tranModel->payment_type = 'lunch';
                $tranModel->transactiontype = InvoiceTransaction::TYPE_TOCREDIT;
                $tranModel->title = 'Lunch Refund (午餐退费)';
                $tranModel->memo = $datestr;
                $tranModel->operator_uid = Yii::app()->user->getId();
                if (!$tranModel->save()){
                    $transaction->rollback();
                    return false;
                }

                //insert ivy_child_credit
                foreach ($ids as $val){
                    $balanceAmount = $balanceAmount + $amountList[$val];
                    $creditModel = new ChildCredit();
                    $creditModel->classid = $childModel->classid;
                    $creditModel->schoolid = $childModel->schoolid;
                    $creditModel->yid = $calendarId;
                    $creditModel->childid = $childId;
                    $creditModel->amount = $amountList[$val];
                    $creditModel->inout = 'in';
                    $creditModel->itemname = 'lunch';
                    $creditModel->transaction_id = $tranModel->id;
                    $creditModel->userid = Yii::app()->user->getId();
                    $creditModel->updated_timestamp = time();
                    $creditModel->balance = $balanceAmount;
                    if (!$creditModel->save()){
                        $transaction->rollback();
                        return false;
                    }
                    //update ivy_refund_lunch
                    $refundModel = RefundLunch::model()->findByPk($val);
                    $refundModel->child_credit_id = $creditModel->cid;
                    $refundModel->operator_uid = Yii::app()->user->getId();
                    $refundModel->operate_timestamp = time();
                    if (!$refundModel->save()){
                        $transaction->rollback();
                        return false;
                    }
                }
                //update ivy_child_profile_basic
                $childModel->credit = $balanceAmount;
                if (!$childModel->save()){
                    $transaction->rollback();
                    return false;
                }
                $transaction->commit();
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message','success'));
                $this->addMessage('data', $childId);
                $this->addMessage('callback', 'updateCallback');
            }catch(Exception $e){
                $transaction->rollBack();
                return false;
            }
        }
        $this->showMessage();
    }

}
?>
