<script>
    var childDataRaw = <?php echo CJSON::encode($this->getNameList($this->selectedClassId)); ?>;
    var childData = _.sortBy(childDataRaw, 'name');
    var onlines = <?php echo CJSON::encode($taskData['onlines'])?>;

    $(function(){
        var childItemTemplate = _.template( $('#child-li-item-template').html() );
        renderChildList = function(){
            $('#weekly-media-child-list').html('');
            _.each(childData, function(_data,_id){
                var _item = childItemTemplate(_data);
                var _child = _.first($(_item));
                if(_.indexOf(onlines, _data.id) != -1) $(_child).addClass('online');

                $('#weekly-media-child-list').append( $(_child) );
            });
        }

        renderChildList();
    });

    function iniRating(){
        $('.rating-custom').rating({showCaption: false});
    };

    function scheckList(id)
    {
        if(id){
            $('#info-guide-box').html('<?php echo Yii::t("global", "Loading Data...");?>').show();
            $('#schecklist').hide();
            $('#childid').val(id);
            $.getJSON('<?php echo $this->createUrl('getChildData');?>', {id: id, classid: classid, semester: semester}, function(data){
                $('#photo-child').html('<img src="<?php echo Yii::app()->params['OAUploadBaseUrl']?>/childmgt/'+childDataRaw[id].photo+'" class="img-thumbnail">');
                $('#name-child').html( childDataRaw[id].name );
                $('#info-guide-box').hide();
                $('#schecklist').show();
                $('#fall_display').attr('checked', data.model.fall_display==2?true:false);
                $('#exportGrowingUrl').attr('href', data.url);
                $('#spring_display').attr('checked', data.model.spring_display==2?true:false);
                if(data.model.development){
                    for(var season in data.model.development){
                        for(var _data in data.model.development[season]){
                            $('#rating-'+season+'-'+_data).html( '<input name="'+season+'['+_data+']" value="'+
                                data.model.development[season][_data]+'" type="number" class="rating-custom" min=0 max=3 step=1 data-size="xs" data-stars="3">' );
                        }
                    }
                    iniRating();
                }

            });
        }
    }

    function cb(data)
    {
        var obj = $('#weekly-media-child-list li[childid="'+data.childid+'"]');
        if(data.flag == 2)
            obj.addClass('online');
        else
            obj.removeClass('online');
    }
</script>

<div class="col-md-9" id="child-note">
    <div class="panel panel-default">
        <div class="panel-heading"><?php echo Yii::t('teaching','Development Checklist');?></div>
        <div class="panel-body">
			<div id="info-guide-box"><?php echo Yii::t('teaching','Click child list right side to start');?></div>
            <div id="schecklist" style="display: none;">
                <?php
                echo CHtml::form('', 'post', array('class'=>'form-horizontal J_ajaxForm', 'role'=>'form'));
                echo CHtml::hiddenField('childid');
                ?>
                <div class="form-group">
                    <label class="col-sm-2 control-label" id="name-child"></label>
                    <div class="col-sm-10">
                        <div  class="col-sm-5" id="photo-child" style="max-width: 200px;">13</div>
                        <div class = "col-sm-9">
                            <div class="pull-right">
                                <div class="rating-container rating-gly-star" data-content="">
                                    <div class="rating-stars" data-content="" style="width: 100%;">
                                    </div>
                                </div>
                                .   <?php echo Yii::t('menu','Not Yet - child has not yet demonstrated indicator') ?>
                                <br>
                                <div class="rating-container rating-gly-star" data-content="">
                                    <div class="rating-stars" data-content="" style="width: 100%;">
                                    </div>
                                </div>
                                . <?php echo Yii::t('menu','In Process - child demonstrates indicator intermittently') ?>
                                <br>
                                <div class="rating-container rating-gly-star" data-content="">
                                    <div class="rating-stars" data-content="" style="width: 100%;">
                                    </div>
                                </div>
                                . <?php echo Yii::t('menu','Proficient - child can reliably demonstrate indicator') ?>
                            </div>
                            <div>
                                <a href="javascript:;" type="button" target="_blank" id="exportGrowingUrl" class="btn btn-primary pull-right" data-toggle="tooltip">
                                    打印</a>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-2 control-label"></label>
                    <div class="col-sm-10">
                        <?php
                        function renderCheckListItem($dataArray){
                            foreach($dataArray as $id=>$item){
                                echo CHtml::openTag('li', array('class'=>'row', 'style'=>'line-height:33px;'));
                                echo CHtml::openTag('div', array('class'=>'col-sm-6'));
                                echo sprintf('%s) %s', $item['weight'],
                                    CommonUtils::autoLang($item['cn_content'],$item['en_content']) );
                                echo CHtml::closeTag('div');
                                echo sprintf('<div class="col-sm-3" id="rating-fall-%d"></div>', $id);
                                echo sprintf('<div class="col-sm-3" id="rating-spring-%d"></div>', $id);
                                echo CHtml::closeTag('li');
                                echo '<hr />';
                            }
                        }
                        ?>
                        <?php foreach($taskData['checkListTemplates']['category'] as $cat_0=>$category):?>

                            <div class="panel panel-default">
                                <div class="panel-heading">
                                    <h3 class="panel-title">
                                        <?php echo CommonUtils::autoLang(
                                            $taskData['checkListTemplates']['catList'][$cat_0]['cn_title'],
                                            $taskData['checkListTemplates']['catList'][$cat_0]['en_title']
                                        );?>
                                    </h3>
                                </div>
                                <div class="panel-body">
                                    <?php foreach( $category as $cat_1 => $data):?>
                                        <div class="row">
                                            <div class="col-sm-6">
                                                <h4>
                                                    <?php echo CommonUtils::autoLang(
                                                        $taskData['checkListTemplates']['catList'][$cat_1]['cn_title'],
                                                        $taskData['checkListTemplates']['catList'][$cat_1]['en_title']
                                                    );?>
                                                </h4>
                                            </div>
                                            <div class="col-sm-3 p15"><?php echo Yii::t("labels", '1st Semester');?></div>
                                            <div class="col-sm-3 p15"><?php echo Yii::t("labels", '2nd Semester');?></div>
                                        </div>
                                        <ul class="list-unstyled">
                                            <?php
                                            if(count($taskData['checkListTemplates']['checkList'][$cat_1])){
                                                renderCheckListItem( $taskData['checkListTemplates']['checkList'][$cat_1] );
                                            }
                                            ?>
                                            <?php foreach( $data as $cat_2):?>
                                            <li class="mb10">
                                                <strong>
                                                    <?php echo CommonUtils::autoLang(
                                                        $taskData['checkListTemplates']['catList'][$cat_2]['cn_title'],
                                                        $taskData['checkListTemplates']['catList'][$cat_2]['en_title']
                                                    );?>
                                                </strong>
                                            </li>
                                            <?php
                                            if(count( $taskData['checkListTemplates']['checkList'][$cat_2] )){
                                                renderCheckListItem( $taskData['checkListTemplates']['checkList'][$cat_2] );
                                            }
                                            ?>
                                        <?php endforeach;?>
                                        </ul>
                                    <?php endforeach;?>
                                </div>
                            </div>
                        <?php endforeach;?>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-2"></label>
                    <div class="col-sm-5">
                        <div class="checkbox">
                            <label><input type="checkbox" name="fall_display" value="2" id="fall_display">
                                <?php echo Yii::t("teaching", 'Make semester 1 online');?></label>
                        </div>
                    </div>
                    <div class="col-sm-5">
                        <div class="checkbox">
                            <label><input type="checkbox" name="spring_display" value="2" id="spring_display">
                                <?php echo Yii::t("teaching", 'Make semester 2 online');?></label>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-2"></label>
                    <div class="col-sm-10">
                        <button class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t("global", 'Save');?></button>
                    </div>
                </div>
                <?php echo CHtml::endForm();?>
            </div>
        </div>
    </div>
</div>

<div class="col-md-3">
    <div class="panel panel-default">
        <div class="panel-heading">
            <?php echo Yii::t('navs','Child List');?>
        </div>
        <div class="panel-body">
            <ul class="media-list" id="weekly-media-child-list"></ul>
        </div>
    </div>
</div>

<style>
    .img-item{background: #f2f2f2;border: 1px solid #d5d5d5}
    .child-face{max-width: 40px;}
    #weekly-media-child-list .media{padding-left: 22px; position: relative}
    #weekly-media-child-list h5.flag{position: absolute;top: 2px; left: 0px; font-size:16px;color: #f2f2f2}
    #weekly-media-child-list .online h5.flag{color: #008000}
</style>

<script type="text/template" id="child-li-item-template">
    <li class="media class-child" childid=<%- id %>>
        <a class="pull-left" href="javascript:scheckList(<%= id%>);">
            <img class="media-object child-face img-thumbnail" src="<?php echo Yii::app()->params['OAUploadBaseUrl']?>/childmgt/<%= photo %>" title="<%- name %>">
        </a>
        <div class="media-body">
            <a href="javascript:scheckList(<%= id%>);"><h5 class="media-heading"><%- name %></h5></a>
        </div>
        <h5 class="flag"><span class="glyphicon glyphicon-ok-sign"></span></h5>
    </li>
</script>