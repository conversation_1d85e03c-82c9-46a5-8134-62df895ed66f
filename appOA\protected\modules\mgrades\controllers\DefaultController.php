<?php

class DefaultController extends BranchBasedController
{
    // 访问action的初级权限
    public $actionAccessAuths = array(
        'saveAssignTeacher' => 'o_A_teacherSubjectAssign',
    );

    public function createUrl($route, $params = array(), $ampersand = '&')
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        return parent::createUrl($route, $params, $ampersand);
    }
    public function init()
    {
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'campusOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site', 'Campus Workspace');

        //初始化选择校园页面
        $this->branchSelectParams['hideOffice'] = true;
        $this->branchSelectParams['urlArray'] = array('//mgrades/default/teacherGroup');

        Yii::import('common.models.grades.*');
    }
    public function actionTeacherGroup()
    {

        $cs = Yii::app()->clientScript;
        $cs->registerCoreScript('jquery.ui');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/underscore-min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/backbone-min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/element/vue.global.js');
        $cs->registerCssFile(Yii::app()->themeManager->baseUrl . '/base/js/element/index.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/element/index.js');
		$this->render('teachergroup');
	}
    //取校园老师数据
    public function getTeacherList()
    {
        $teacherList = array();
        $teacherids = OA::getCampusTeachers($this->branchId);
        if ($teacherids) {
            $criteria = new CDbCriteria();
            $criteria->compare('t.uid', $teacherids);
            $criteria->order = 'uname';
            $data = User::model()->with('profile')->findAll($criteria);
            if ($data !== null) {
                foreach ($data as $val) {
                    $teacherList[$val->uid]['level'] = $val->level;
                    $teacherList[$val->uid]['uid'] = $val->uid;
                    $teacherList[$val->uid]['name'] = $val->getName();
                }
            }
        }
        return $teacherList;
    }

    public function actionAssignTeacher()
    {
        $subject = Yii::app()->request->getParam('subject');
        $teacherId = Yii::app()->request->getParam('teacherId');
        if (!$subject) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '请选择一项！');
            $this->showMessage();
        }
        if (!$teacherId) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '请选择老师！');
            $this->showMessage();
        }
        $model = TeacherSubjectLink::model()->findByAttributes(array(
            'schoolid' => $this->branchId,
            'teacher_uid' => $teacherId,
            'subject_flag' => $subject
        ));
        if (!$model) {
            $model = new TeacherSubjectLink();
        }
        $model->schoolid = $this->branchId;
        $model->teacher_uid = $teacherId;
        $model->subject_flag = $subject;
        $model->status = 1;
        $model->updated = time();
        $model->updated_uid = Yii::app()->user->getId();
        if ($model->save()) {
            $this->addMessage('state', 'success');
            $this->addMessage('message', '分配成功！');
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '分配失败！');
            $this->showMessage();
        }
    }

    public function actionDelTeacher()
    {
        $subject = Yii::app()->request->getParam('subject');
        $teacherId = Yii::app()->request->getParam('teacherId');
        if (!$subject) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '请选择一项！');
            $this->showMessage();
        }
        if (!$teacherId) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '请选择老师！');
            $this->showMessage();
        }
        $delResult = TeacherSubjectLink::model()->deleteAllByAttributes(
            array(
                'schoolid' => $this->branchId,
                'subject_flag' => $subject,
                'teacher_uid' => $teacherId,
            )
        );
        if ($delResult) {
            $this->addMessage('state', 'success');
            $this->addMessage('message', '分配删除成功！');
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '分配删除失败！');
            $this->showMessage();
        }
    }

    public function actionDelTeacherAll()
    {
        $subject = Yii::app()->request->getParam('subject');
        if (!$subject) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '请选择一项！');
            $this->showMessage();
        }
        $delResult = TeacherSubjectLink::model()->deleteAllByAttributes(
            array(
                'schoolid' => $this->branchId,
                'subject_flag' => $subject,
            )
        );
        if ($delResult) {
            $this->addMessage('state', 'success');
            $this->addMessage('message', '分配删除成功！');
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '分配删除失败！');
            $this->showMessage();
        }
    }

    public function actionSaveAssignTeacher()
    {
        if (Yii::app()->request->isAjaxRequest && Yii::app()->request->isPostRequest) {
            $subjectFlag = isset($_POST['TeacherSubjectLink']['subject_flag']) ? $_POST['TeacherSubjectLink']['subject_flag'] : null;
            if ($subjectFlag !== null) {
                //验证老师是否为本校
                $teachers = null;
                if (isset($_POST['TeacherSubjectLink']['teacher_uid'])) {
                    $criter = new CDbCriteria;
                    $criter->compare('t.uid', $_POST['TeacherSubjectLink']['teacher_uid']);
                    $criter->compare('profile.branch', $this->branchId);
                    $criter->index = 'uid';
                    $teachers = User::model()->with('profile')->findAll($criter);
                }
                $criter = new CDbCriteria;
                $criter->compare('schoolid', $this->branchId);
                $criter->compare('subject_flag', $subjectFlag);
                $delResult = TeacherSubjectLink::model()->deleteAllByAttributes(
                    array(
                        'schoolid' => $this->branchId,
                        'subject_flag' => $subjectFlag
                    )
                );
                if ($delResult && $teachers === null) {
                    $this->addMessage('state', 'success');
                    $this->addMessage('message', Yii::t('message', 'success'));
                    $this->addMessage('data', $subjectFlag);
                    $this->addMessage('callback', 'cbdeleteTeacher');
                    $this->showMessage();
                } elseif (!$delResult && $teachers === null) {
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', '请选择老师！');
                    $this->showMessage();
                }
                if (!empty($teachers)) {
                    $data = array();
                    foreach ($teachers as $val) {
                        $model = new TeacherSubjectLink();
                        $model->schoolid = $this->branchId;
                        $model->teacher_uid = $val->uid;
                        $model->subject_flag =  $subjectFlag;
                        $model->status = 1;
                        $model->updated = time();
                        $model->updated_uid = Yii::app()->user->getId();
                        if ($model->save()) {
                            $data['flag'] = $subjectFlag;
                            $data['info'][$val->uid]['tUid'] = $val->uid;
                            $data['info'][$val->uid]['name'] = $val->getName();
                        }
                    }
                    if (count($data)) {
                        $this->addMessage('state', 'success');
                        $this->addMessage('message', '分配成功！');
                        $this->addMessage('callback', 'cbSaveTeacher');
                        $this->addMessage('data', $data);
                    } else {
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', '分配失败！');
                    }
                }
            } else {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '请选择一项！');
            }
            $this->showMessage();
        }
    }

    //获取已经分配的教师
    public function getAssignedTeacher()
    {
        $teacherList = array();
        $criter = new CDbCriteria;
        $criter->compare('schoolid', $this->branchId);
        $criter->compare('status', 1);
        $teachers = TeacherSubjectLink::model()->findAll($criter);
        if (!empty($teachers)) {
            foreach ($teachers as $val) {
                $teacherList[$val->subject_flag][] = $val->teacher_uid;
            }
        }
        return $teacherList;
    }

    public function actionGetClassTeachers()
    {
        if (Yii::app()->request->isAjaxRequest && Yii::app()->request->isPostRequest) {
            Yii::import('common.models.classTeacher.*');
            $data = ClassTeacher::model()->getCampusAllClassTeacher($this->branchId, $this->branchObj->schcalendar, true, true);
            echo CJSON::encode(array(
                'data' => $data,
                'state' => 'success'
            ));
            Yii::app()->end();
        }
    }

    public function getClassTeachers()
    {
        Yii::import('common.models.classTeacher.*');
        $branchId = $this->branchId;
        $yid = $this->branchObj->schcalendar;

        $crit = new CDbCriteria();
        $crit->compare('schoolid', $branchId);
        $crit->compare('yid', $yid);
        $crit->compare('child_age', '<12');
        $crit->order = 'child_age ASC, title ASC';
        $classModels = IvyClass::model()->findAll($crit);

        $data = array('classArr' => array(), 'teachers' => array());
        $classIdList = array();
        foreach ($classModels as $cm) {
            $classIdList[] = $cm->classid;
            $data['classArr'][] = array(
                'id' => $cm->classid,
                'title' => $cm->title
            );
        }

        $models = ClassTeacher::model()->findAllByAttributes(
            array(
                'classid' => $classIdList,
            )
        );

        foreach ($models as $model) {
            $data['teachers'][$model->teacherid][] = $model->classid;
        }

        return $data;
    }

    public function getTeacherIdList()
    {
        Yii::import('common.models.hr.*');
        $teacherIdList = array();
        // 获取小学的全部老师
        $positionIdList = array();
        $deptId = 134;
        $positionIdModelList = DepPosLink::model()->findAllByAttributes(array('department_id' => $deptId));
        foreach ($positionIdModelList as $item) {
            $positionIdList[] = $item->position_id;
        }
        $positionIdStr = implode(',', $positionIdList);
        $branchId = $this->branchId;
        $sql = "select u.uid from ivy_users u join ivy_user_profile p on u.uid=p.uid join auth_oa_assignments a on p.uid=a.userid where u.level = 1 and p.occupation_en in ($positionIdStr) and p.branch=:schoolid and a.itemname in ('ivystaff_teacher','ivystaff_caregiver')";
        $command = Yii::app()->db->createCommand($sql);
        $command->bindParam(':schoolid', $branchId, PDO::PARAM_STR);
        $rows = $command->queryAll();
        foreach ($rows as $row) {
            $teacherIdList[] = $row['uid'];
        }

        return $teacherIdList;
    }

    public function getTeacherInfo($teacherids)
    {
        $teacherList = array();
        if ($teacherids) {
            $criteria = new CDbCriteria();
            $criteria->compare('t.uid', $teacherids);
            $criteria->order = 'uname';
            $data = User::model()->with(array('profile', 'staffInfo'))->findAll($criteria);
            if ($data !== null) {
                foreach ($data as $val) {
                    $photo = $val->staffInfo->staff_photo ? $val->staffInfo->staff_photo : "blank.jpg";
                    $teacherList[$val->uid]['photoUrl'] =  Yii::app()->params['OAUploadBaseUrl'] . '/infopub/staff/' . $photo;
                    $teacherList[$val->uid]['level'] = $val->level;
                    $teacherList[$val->uid]['uid'] = $val->uid;
                    $teacherList[$val->uid]['name'] = $val->getName();
                }
            }
        }
        return $teacherList;
    }
}
