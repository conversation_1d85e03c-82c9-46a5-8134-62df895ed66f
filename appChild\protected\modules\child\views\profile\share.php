<?php
$this->breadcrumbs=array(
		Yii::t("navigations","Profile")=>array('/child/profile'),
		Yii::t("navigations","Share Portfolio"),
);?>


<div id="left-col" class="no-bg span-6">
	&nbsp;
</div>

<div id="main-col" class="span-18 last">
	
	<h1><label><?php echo Yii::t("navigations","Share Portfolio") ?></label></h1>
	<p class="desc"><?php echo Yii::t("global","You can share your child's portfolio with family and friends by following the instructions below. Sensitive information such as Payment and Profile will not be visible to your family and friends.");?></p>
	
	<?php
		foreach(Yii::app()->user->getFlashes() as $key => $message) {
			echo '<div class="flash-' . $key . '">' . $message . "</div>\n";
		}
	?>
	
	<div class="form share_bg">
	<?php $form=$this->beginWidget('CActiveForm', array(
		'id'=>'share-form',
		'enableClientValidation'=>true,
		'clientOptions'=>array(
			'validateOnSubmit'=>true,
		),
	)); ?>
		<p class="note">
			<?php
				if($model->enabled){
					echo sprintf(Yii::t("userinfo", 'Your sharing function has been enabled.'));
				}else{
					echo sprintf(Yii::t("userinfo", 'Your sharing function is disabled. Please input access code and click “Enable”.'));
				}
			?>
		</p>
		
		<?php echo CHtml::errorSummary($model); ?>
			
		<div class="row" id="op-holder">
			
			
		</div>
	
		<dl class="row" id="op-holder">
			<dt><?php echo CHtml::activeLabelEx($model,'accessCode'); ?></dt>
			<dd>
			<?php echo CHtml::activeTextField($model,'accessCode') ?>
			<?php echo CHtml::error($model,'accessCode'); ?>
			<p class="info">
				<?php echo Yii::t("message",'Access code length 4-20 characters.');?>
			</p>
			</dd>
		</dl>	
	
	
		<dl class="row submit">
			<dt></dt>
			<dd>
				<?php
					$buttonOptions = array(
						"class"=>"w100 bigger"
					);
					if ( ! Yii::app()->user->checkAccess('editProfile',array("childid"=>$this->getChildId())) ){
						$buttonOptions["disabled"]="disabled";
					}
					
					if ($model->enabled)
					echo CHtml::submitButton(Yii::t("global", "Save Code"), $buttonOptions);
					else
					echo CHtml::submitButton(Yii::t("global", "Enable"), $buttonOptions);
				?>
				
				<?php
					if ($model->enabled){
						$buttonOptions["name"] = "disableBtn";
						echo CHtml::submitButton(Yii::t("global", "Disable"), $buttonOptions);
					}
					
				?>
			</dd>
		</dl>
	
		
		<?php if($model->enabled):?>
		<dl class="row">
			<dt>
				&nbsp;
			</dt>
			<dd>
				<p class="desc">
					<?php echo Yii::t("portfolio","Share your child’s portfolio by copying the text in the below and sending to your family and friends directly."); ?>
				</p>
				<?php echo CHtml::activeTextArea($model,'previewText',array("rows"=>10,"cols"=>70)); ?>
			</dd>
		</dl>	
		
		<dl class="row">
			<dt>&nbsp;
			</dt>
			<dd>
				<p class="desc">
					<?php echo Yii::t("message","QR Code: For easy login using a tablet or smartphone, simply scan the QR code below. You can also share this login QR code by copying and pasting it into your email to them.");?>
				</p>
				<?php
				$_tmpM = new ProfileShareForm;
				
				$childid = $this->getChildId();
				$url = $_tmpM->genAccessUrl($childid);
				
				$md5Str = sprintf("%s-%u-%s", $url , $childid, "IvyQr");
				$filename = md5($md5Str).'.png';
				echo CHtml::openTag("a", array("target"=>"_blank","href"=>Mims::CreateUploadUrl('qrcodes/'.$filename),"class"=>"qr-code"));
				if (file_exists(Mims::CreateUploadUrl('qrcodes/'.$filename))){
					echo CHtml::image(Mims::CreateUploadUrl('qrcodes/'.$filename));
				}
				else {
					$this->widget('ext.qrcode.QRCodeGenerator',array(
						'data' => $url,
						'subfolderVar' => true,
						'matrixPointSize' => 5,
						'filename' => $filename,
						'filePath' => Yii::app()->params['uploadPath'],
						'fileUrl' => Yii::app()->params['uploadBaseUrl'],
					));
				}
				echo CHtml::closeTag("a");
				?>				
			</dd>
		</dl>
        <?php endif;?>
		
	<?php $this->endWidget(); ?>
	</div><!-- form -->
    

	
</div>