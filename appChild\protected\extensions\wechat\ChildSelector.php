<?php 
/**
* 选择孩子
*/
class ChildSelector extends CWidget
{
	//所有孩子对像
	public $myChildObjs;
	// 当前孩子对象
	public $childObj;
	// 跳转地址
	public $jumpUrl;
	// 参数
	public $params = array();

	public function init()
	{
		parent::init();

	}

	public function run()
	{
		$this->render('childSelector', array(
			'childObj'=>$this->childObj,
			'myChildObjs'=>$this->myChildObjs,
			'jumpUrl'=>$this->jumpUrl,
			'params'=>$this->params,
		));
	}
}
