<?php
class Lunch extends CWidget{
	public $schoolId;
    public $yId;
    public $weekNum;
    public $allergy;
    public $cssFile;
    public $assetsUrl;

	public function init(){
		Yii::import('common.models.lunchmenu.*');

//        if($this->assetsUrl===null)
//			$this->assetsUrl=Yii::app()->getAssetManager()->publish(dirname(__FILE__) . '/assets', false, -1, YII_DEBUG);

        $cs = Yii::app()->clientScript;
        if (false !== $this->cssFile)
        {
            if (null === $this->cssFile)
//                $this->cssFile = $this->assetsUrl . '/lunch.css';
                $this->cssFile = Yii::app()->theme->baseUrl.'/widgets/lunch/lunch.css?t='.Yii::app()->params['refreshAssets'];
            $cs->registerCssFile($this->cssFile);
//			$cs->registerCssFile(Yii::app()->theme->baseUrl.'/css/widgetbase.css?t=2');
			$cs->registerCssFile(Yii::app()->theme->baseUrl.'/css/widgetbase.css?t='.Yii::app()->params['refreshAssets']);
        }
	}

	public function run(){
        Yii::import('common.models.calendar.CalendarWeek');
        Yii::import('common.models.child.ChildMisc');

        $childmisc = ChildMisc::model()->findByPk($this->controller->getChildId());

        Mims::LoadHelper('HtoolKits');

        $menu = array();
        $mdArr = array();
        $wdarrs = array();
        if ($this->yId){
            //$weeks = CalendarWeek::weeks($this->yId,true);
            $criteria = new CDbCriteria();
            $criteria->compare('schoolid', $this->schoolId);
            $criteria->compare('yid', $this->yId);
            $criteria->order = 'week_num ASC';
            $criteria->index = 'week_num';
            $weeklink = CateringMenuWeeklink::model()->findAll($criteria);

            $weekList = array();
            $morenWeek = array();
            if($weeklink){
                $startData = reset($weeklink);
                $endData = end($weeklink);
                $time = $startData->monday_timestamp;
                for ($i = $startData->week_num; $i <= $endData->week_num; $i++)
                {
                    $weekList[$i] = sprintf(Yii::t('global', 'Week %s (%s - %s)'), $i, CommonUtils::formatDateTime($time), CommonUtils::formatDateTime($time + 7 * 24 * 3600 - 1));
                    $morenWeek[$time] = $i;
                    $time += 86400 * 7;
                }
            }

            $mondayTime = mktime(0,0,0,date('m'),date('d')-date('w')+1,date('Y'));

            if (!$this->weekNum){
                $this->weekNum = isset($morenWeek) ? $morenWeek[$mondayTime] : array();
            }

            $criteria = new CDbCriteria();
            $criteria->compare('schoolid', $this->schoolId);
            $criteria->compare('yid', $this->yId);
            $criteria->compare('week_num', $this->weekNum);
            $criteria->compare('status', 1);
            $menuLink = CateringMenuWeeklink::model()->find($criteria);

            $menuId = 0;
            if ($menuLink){
                $menuId = (isset($childmisc->sign_allergy) && $childmisc->sign_allergy) ? $menuLink->allergy_id : $menuLink->menu_id;
            }

            if ($menuId){
                $menu = CateringMenu::model()->findByPk($menuId);
                $wdarrs = ($menu->week_cate) ? unserialize($menu->week_cate) : array('mon', 'tue', 'wed', 'thu', 'fri');
                $criteria = new CDbCriteria();
                $criteria->compare('menu_id', $menuId);
                $criteria->compare('category', unserialize($menu->menu_cate));
                $criteria->order='weight asc';
                $menuDetail = CateringMenuDetail::model()->with('termInfo')->findAll($criteria);
                foreach ($menuDetail as $mD){
                    $mdArr[$mD->category][$mD->weekday]=$mD;
                    $mdArr[$mD->category]['title']=  HtoolKits::autoLang($mD->termInfo->cntitle, $mD->termInfo->entitle);
                }
            }
        }

		$this->render('lunch',array('wdarrs' => $wdarrs, 'getWeekDayName' => CateringMenu::getWeekDayName(), 'weeks'=>$weekList, 'weekNum'=>$this->weekNum, 'schoolId'=>$this->schoolId, 'yId'=>$this->yId, 'menu'=>$menu, 'mdArr'=>$mdArr));
	}

}
