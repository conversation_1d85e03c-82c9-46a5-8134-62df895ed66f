<?php
$securityType = CommonUtils::LoadConfig('CfgSecurity');
$month = array();
for ($m = 1; $m < 13; $m++) {
    $month[sprintf("%02d", $m)] = sprintf("%02d", $m);
}

$year = array();
for ($y = 2016; $y <= date('Y', time()); $y++) {
    $year[$y] = $y;
}

$s = array();
if($safetyData){
    foreach ($safetyData as $k=>$item) {
        $s[$item['type']][$k] = array(
                'title' => $item['name'],
                'status' => $item['status'],
            );
    }
}
$yearData = ($yearData) ? $yearData : date("Y", time()) ;
$monthData = ($monthData) ? $monthData : date("m", time());
?>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site','HQ Operations'), array('/moperation'))?></li>
        <li><?php echo CHtml::link(Yii::t('site','Basic Configurations'), array('/moperation'))?></li>
        <li class="active"><?php echo Yii::t('site','安全报告模板') ?></li>
    </ol>

    <div class="row">
        <div class="col-md-2 col-sm-2 mb10">
            <?php
            $this->widget('zii.widgets.CMenu', array(
                'items' => $this->module->getHomeMenu(),
                'id' => 'pageCategory',
                'htmlOptions' => array('class' => 'nav nav-pills nav-stacked background-gray'),
                'activeCssClass' => 'active',
            ));
            ?>
        </div>
        <div class="col-md-10">
            <div class="row">
                <!-- 搜索框 -->
                <form  class="" style="float: left;width: 100%" action="<?php echo $this->createUrl('showSafety'); ?>" method="get">
                    <div class="col-sm-2 form-group">
                        <?php echo CHtml::dropDownList('year', $yearData, $year, array('class'=> 'form-control', 'empty' => Yii::t('teaching', '年份'))) ?>
                    </div>
                    <div class="col-sm-2 form-group">
                        <?php echo CHtml::dropDownList('month', $monthData, $month, array('class'=> 'form-control', 'empty' => Yii::t('teaching', '月份'))) ?>
                    </div>
                    <div class="col-sm-2 form-group">
                        <?php echo CHtml::dropDownList('schoolid', $schoolid, $school, array('class'=> 'form-control', 'empty' => Yii::t('global', 'Please Select'))) ?>
                    </div>
                    <div class="">
                        <div class="">
                            <button class="btn btn-default ml5" type="submit"><span class="glyphicon glyphicon-search"> </span> </button>
                        </div>
                    </div>
                </form>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <?php if($schoolid && $monthData && $yearData): ?>
                    <div class="mb10">
                        <div class="well well-sm">
                            <div><?php echo Yii::t('safety','Campus safety self-inspection table') ?></div>
                            <div><?php echo Yii::t('safety','school') ?>:<?php echo $school[$schoolid] ?></div>
                            <div><?php echo Yii::t('safety','date') ?>:<?php echo date("Y-m-d", time()) ?></div>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead>
                                <tr>
                                    <th colspan="4" class="text-center"><?php echo Yii::t('safety','Summary of monthly self-inspection results') ?></th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr>
                                    <td><?php echo Yii::t('safety','Inspection category') ?></td>
                                    <td><?php echo Yii::t('safety','Inspection item') ?></td>
                                    <td><?php echo Yii::t('safety','Inspection department/person') ?></td>
                                    <td><?php echo Yii::t('safety','Score') ?></td>
                                </tr>
                                <?php
                                foreach ($securityType['type'] as $k=>$item):
                                    ?>
                                    <tr>
                                        <td rowspan="<?php echo isset($s[$k]) ? count($s[$k]) + 1 : 2 ; ?>"><?php echo Yii::app()->language == 'zh_cn' ? $item['cn'] : $item['en'] ; ?></td>
                                    </tr>
                                    <?php if($s[$k]){?>
                                    <?php foreach($s[$k] as $key => $val): ?>
                                        <tr>
                                            <td><?php echo ($val['status'] == 1) ?  "<a href = #anchor_point_" . $key . ">" . $val['title'] ."</a>" : $val['title'] ?></td>
                                            <td> <?php
                                                if(isset($user) && isset($user[$key])){
                                                    foreach ($user[$key] as $userName) {
                                                        echo $userName . ' ';
                                                    }
                                                }
                                                ?></td>
                                            <td><?php echo (isset($scoreArr) && isset($scoreArr[$key])) ? $scoreArr[$key] : 0 ; ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php }else{ ?>
                                    <tr>
                                        <td>无</td>
                                        <td></td>
                                        <td></td>
                                    </tr>
                                <?php } ?>
                                <?php  endforeach; ?>
                                <tr>
                                    <td colspan="2"><?php echo Yii::t('safety','Overall score of self-inspection for safety in the month') ?></td>
                                    <td></td>
                                    <td></td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <tbody>
                                <tr>
                                    <td rowspan="7" class="text-center" width="100"><?php echo Yii::t('safety','Monthly self-inspection table') ?></td>
                                </tr>
                                <tr>
                                    <td rowspan="7" class="text-center" width="100"><?php echo Yii::t('safety','Description of scoring:') ?></td>
                                </tr>
                                <tr>
                                    <td><?php echo Yii::t('safety','5 points  good state') ?></td>
                                </tr>
                                <tr>
                                    <td><?php echo Yii::t('safety','4 points  rectify within 2 weeks') ?></td>
                                </tr>
                                <tr>
                                    <td><?php echo Yii::t('safety','3 points   rectify within 1 week') ?></td>
                                </tr>
                                <tr>
                                    <td><?php echo Yii::t('safety','Below 3 points  rectify immediately') ?></td>
                                </tr>
                                <tr>
                                    <td><?php echo Yii::t('safety','N/A  the item is not applicable for scoring') ?></td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <?php  endif; ?>
                    <?php if($data){ ?>
                        <?php foreach($data as $sid => $item){ ?>
                            <div id="anchor_point_<?php echo $sid ?>" class="mb10">
                                <div class="bs-example" data-example-id="simple-responsive-table">
                                    <div class="table-responsive">
                                        <table class="table table-bordered">
                                            <tr>
                                                <th class="col-md-12" colspan="8">
                                                    <?php echo $safetyData[$sid]['name']; ?>
                                                    <input  type="button" class=" btn btn-primary btn-xs" value="撤销"  onclick="undo(<?php echo $sid . ",'" . $schoolid . "'," . $timeDate ?>)">
                                                </th>
                                            </tr>
                                            <tr>
                                                <th width="100" rowspan="2"><?php echo Yii::t('safety','Category') ?></th>
                                                <th colspan="2" rowspan="2"><?php echo Yii::t('safety','Detailed rules') ?></th>
                                                <th colspan="2"><?php echo Yii::t('safety','Work arrangement') ?></th>
                                                <th rowspan="2"><?php echo Yii::t('safety','Inspection record') ?></th>
                                                <th rowspan="2"><?php echo Yii::t('safety','Rectification measures (measure+deadline)') ?></th>
                                                <th width="200"><?php echo Yii::t('safety','Self-inspection score') ?></th>
                                            </tr>
                                            <tr>
                                                <td><?php echo Yii::t('safety','Responsible person (name)') ?></td>
                                                <td><?php echo Yii::t('safety','Record/document') ?></td>
                                                <td><?php echo Yii::t('safety','0~5 points or N/A (not applicable)') ?></td>
                                            </tr>
                                            <?php foreach ($item as $cid => $val): ?>
                                                <tr>
                                                    <td rowspan="<?php echo count($val) + 1 ?>"><?php echo $cName[$cid];  ?></td>
                                                </tr>
                                                <?php
                                                $i = 1;
                                                foreach ($val as $rid=>$text): ?>
                                                    <tr>
                                                        <td><?php echo $i ?></td>
                                                        <td><?php echo $rName[$rid] ?></td>
                                                        <td><?php echo (isset($userModel) && isset($userModel[$text['principal']])) ? $userModel[$text['principal']]->getName() : ""?></td>
                                                        <td><?php echo $text['document']?></td>
                                                        <td><?php echo $text['record']?></td>
                                                        <td><?php echo $text['impore']?></td>
                                                        <td><?php echo ($text['score'] >= 0) ? $text['score'] : 'N/A' ; ?></td>
                                                    </tr>
                                                    <?php
                                                    $i++;
                                                endforeach; ?>
                                            <?php endforeach; ?>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        <?php } ?>
                    <?php } ?>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    function undo(sid,schoolid,dateTime) {
        if(confirm("确定撤销吗?")){
            $.ajax({
                type: "POST",
                url: "<?php echo $this->createUrl('undo')?>",
                data: {sid:sid, schoolid:schoolid, dateTime:dateTime},
                dataType: "json",
                success: function(data){
                    if(data.state == 'success'){
                        resultTip({msg: data.message, callback: function(){
                            reloadPage(window);
                        }});
                    }else{
                        resultTip({error: 'warning', msg: data.message});
                    }
                }
            })
        }
    }
</script>