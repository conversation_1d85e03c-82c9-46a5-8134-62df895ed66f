<?php

class ProfileController extends ChildBasedController
{
	public $childMainMenu;
	public $validPost=false;
	   
    public function createUrl($route, $params = array(), $ampersand = '&')
    {
        if (empty($params['childid'])) {
            $params['childid'] = isset($_GET['childid']) ? $_GET['childid'] : '';
        }
        return parent::createUrl($route, $params, $ampersand);
    }
	
	/**
	 * 整体权限控制
	 * @param	CAction	$action	Description
	 * @return	Object			Description
	 */
    public function beforeAction(CAction $action) {
        parent::beforeAction($action);
		if(Yii::app()->request->isPostRequest){
			if (Yii::app()->user->checkAccess('oChildGeneralAdmin')) {
				$this->validPost = true;
			}else{
				if(Yii::app()->request->isAjaxRequest){
					$ret['state'] = 'fail';
					$ret['message'] = Yii::t("message", 'No permission');
					echo CJSON::encode($ret);
					Yii::app()->end();				
				}else{
					Yii::app()->user->setFlash('error', Yii::t("message", "No permission"));
				}
			}
		}else{
			//由于模块加入全局权限判断，所以下面代码注释
			//if (!Yii::app()->user->checkAccess('oChildGeneralView')) {
			//	if (Yii::app()->request->isAjaxRequest) {
			//		$ret['state'] = 'fail';
			//		$ret['message'] = Yii::t("message", 'Unauthorized operation, please contact IT Dept.');
			//		echo CJSON::encode($ret);
			//		Yii::app()->end();
			//	} else {
			//		$this->forward('//child/message/error');
			//	}
			//}		
		}
        return true;
    }   	
	
	/**
	 * 查看和编辑孩子基本信息
	 * @return	Object		Description
	 */
	public function actionIndex()
	{
		$t = strtolower(Yii::app()->request->getParam("t", "child"));
        Yii::import('application.components.policy.PolicyApi');
        Yii::import('common.models.invoice.*');
        
        $siblings = PolicyApi::getSiblings($this->childId);
		switch($t){
			case 'child':
				$model = $this->childObj;
				$model->est_enter_date = $model->est_enter_date ? date('Y-m-d', $model->est_enter_date) : "";
				$model->enter_date = $model->enter_date ? date('Y-m-d', $model->enter_date) : "";
				$model->is_legel_cn_name = $model->is_legel_cn_name ? $model->is_legel_cn_name : 1;
				$model->setScenario('update');
				
                // if it is ajax validation request
                if (isset($_POST['ajax']) && $_POST['ajax'] === 'child-form') {
                    echo CActiveForm::validate($model);
                    Yii::app()->end();
                }

                $model->setScenario('update');

                if (isset($_POST['ChildProfileBasic']) && $this->validPost ) {
                    if (in_array($model->schoolid, array('BJ_DS', 'BJ_SLT'))) {
                        if (!in_array(Yii::app()->user->id, array('8011391', '8016254', '8029463', '8034973', '5', '8034932', '8023431', '8033477', '8028205', '8037397'))) {
                            Yii::app()->user->setFlash('error', "修改学生信息请联系招生部！");
                            $this->refresh();
                            return false;
                        }
                    }

					$oldPhoto = $model->photo;
					$oschoolid = $model->schoolid;
					$model->attributes = $_POST['ChildProfileBasic'];
					$model->is_legel_cn_name = $_POST['is_legel_cn_name'];
					$model->first_name_en = $_POST['ChildProfileBasic']['first_name_en'];
					$model->last_name_en = $_POST['ChildProfileBasic']['last_name_en'];
					$model->uploadPhoto = CUploadedFile::getInstance($model, 'uploadPhoto');
					$model->birthday = strtotime($model->birthday_search);
					$model->est_enter_date = strtotime($model->est_enter_date) ? strtotime($model->est_enter_date) : 0;
					$model->enter_date = strtotime($model->enter_date) ? strtotime($model->enter_date) : 0;
					$model->birthday = ($model->birthday) ? $model->birthday : 0; //如果输入 2012-19-90 之类的错误日期格式，此行代码可以避免一个多于的错误提示
					if ($model->validate()) {
						
						if ($model->uploadPhoto) {
							$upResult = OA::processPic($model->uploadPhoto, 'childPhoto');
                            if ($upResult) {
                                $model->processAvatar($upResult, $oldPhoto);
                            }
                        }

						if ($model->save()) {
							if ($model->schoolid != $oschoolid){
								Yii::import('common.models.child.*');
								PolicyApi::transferSchool($oschoolid, $model->schoolid, $this->childId);
							}
//							ChildProfileBasic::setChildSync($this->childId, 1);
							Yii::app()->user->setFlash('success', Yii::t("message", "Data Saved!"));
							$this->refresh();
						} else {
							print_r($model->getErrors());
						}
					}
                }				
				
			break;
			case 'parent':
				$model = $this->childObj->getParents();
                if ( isset($_POST['User']) && isset($_POST['IvyParent']) && $this->validPost ){
                    foreach ($model as $pKey=>$pModel){

                        if (is_object($pModel)){
                            $pModel->uploadPhoto = CUploadedFile::getInstance($pModel, '['.$pKey.']uploadPhoto');
                            if ( $pModel->uploadPhoto ){
                                $oldPhoto = $pModel->user_avatar;
                                $upResult = OA::processPicUpload($pModel->uploadPhoto, 'userPhoto', $oldPhoto);
                                $pModel->user_avatar = count($upResult)==3 ? $upResult['filename'] : 'blank.gif';
                            }
                            $reqUser = $_POST['User'][$pKey];
                            $reqParent = $_POST['IvyParent'][$pKey];
                            $pModel->name = $reqParent['cn_name'];
                            if ( $reqParent['en_firstname'] && $reqParent['en_lastname'] ){
                                $pModel->uname = ucfirst(strtolower($reqParent['en_firstname'])).' '.substr(ucfirst(strtolower($reqParent['en_lastname'])), 0, 1);
                            }
                            $pModel->profile->first_name = $reqParent['en_firstname'];
                            $pModel->profile->last_name = $reqParent['en_lastname'];
                            $pModel->profile->nationality = $reqParent['country'];
                            $pModel->parent->attributes = $reqParent;
                            $pModel->parent->ID_card = $reqParent['ID_card'];
                            if (!$pModel->save()){
                                print_r($pModel->getErrors());die;
                            }
                            if (!$pModel->profile->save()){
                                print_r($pModel->profile->getErrors());die;
                            }
                            $pModel->parent->setScenario('staffCreate');
                            if (!$pModel->parent->save()){
                                print_r($pModel->parent->getErrors());die;
                            }
                        }
                    }
                    ChildProfileBasic::setChildSync($this->childId, 1);
                    Yii::app()->user->setFlash('success', Yii::t("message", "Data Saved!"));
                    $this->refresh();
                }
			break;
			case 'address':
				Yii::import("common.models.child.*");
				$model = HomeAddress::model()->findByPk($this->childId);
                if (empty($model)){
                    $model = new HomeAddress();
                    $model->childid = $this->childId;
                    $model->fid = $this->childObj->fid;
                    $model->mid = $this->childObj->mid;
                    $model->family_id = $this->childObj->family_id;
                }
                // if it is ajax validation request
                if (isset($_POST['ajax']) && $_POST['ajax'] === 'address-form') {
                    echo CActiveForm::validate($model);
                    Yii::app()->end();
                }
				
                if (isset($_POST['HomeAddress']) && $this->validPost ) {
					$model->attributes = $_POST['HomeAddress'];
					if ($model->validate()) {
						if ($model->save()) {
							Yii::app()->user->setFlash('success', Yii::t("message", "Data Saved!"));
							$this->refresh();
						} else {
							print_r($model->getErrors());
						}
					}
                }
				
				
			break;
			case 'privacy':
                Yii::import("common.models.child.ChildMisc");
				$model = new ProfilePrivacyForm;
				$model->iniData($this->childObj);
				
				if (isset($_POST['ProfilePrivacyForm']) && $this->validPost) {
                    $this->childObj->misc = ChildMisc::model()->findByPk($this->childId);
                    if($this->childObj->misc == null){
                        $this->childObj->misc = new ChildMisc();
                        $this->childObj->misc->childid = $this->childId;
                    }
					$model->attributes = $_POST['ProfilePrivacyForm'];
					$this->childObj->misc->privacy = $model->genData();
					if (!$this->childObj->misc->save()) {
						print_r($this->childObj->misc->getErrors());
					} else {
						Yii::app()->user->setFlash('success', Yii::t("message", "Data Saved!"));
						$this->refresh();
					}
				}				
				
			break;
			case 'misc':
				Yii::import("common.models.child.*");
				$model = ChildMisc::model()->findByPk($this->childId);
				if ($model == null) {
					$model = new ChildMisc;
					$model->childid = $this->childId;
				}
				
				if (isset($_POST['ChildMisc'])&& $this->validPost) {
					$model->setScenario('appOA');
					$model->attributes = $_POST['ChildMisc'];
					$model->updated_timestamp = time();
					$model->create_uid = Yii::app()->user->id;
					if ($model->save()) {
						Yii::app()->user->setFlash('success', Yii::t("message", "Data Saved!"));
                        $this->refresh();
					}
				}

			break;
		}

		$this->render('index', array("model"=>$model, "t"=>$t, 'siblings'=>$siblings));
	}
	
	public function actionAccounts($childid=0){
		$model = $this->childObj->getParents(null, true);
		if (isset($_POST['User']) && $this->validPost ) {
            #生成家长帐号
            Yii::import('application.components.user.UserApi');
            $fail = 0;
			foreach(array('father','mother') as $role){
				
				$model[$role]->attributes = $_POST['User'][$role];
				if($model[$role]->isNewRecord){
					if($model[$role]->level){
                        $model[$role]->iniPassword = $model[$role]->pass = $_POST['User'][$role]['iniPassword'];
						$model[$role]->setScenario('staffCreate');
                        if ($model[$role]->validate()){
                            $model[$role]=UserApi::createParent($this->childId, $model[$role], $role=='father'?1:2);
                        }
                        else {
                            $fail = 1;
                        }
					}
				}else{
					$model[$role]->setScenario('staffUpdate');
					$model[$role]->parent->setScenario('staffUpdate');
                    if ( $_POST['User'][$role]['changePassword'] ){
                        $model[$role]->pass=md5($_POST['User'][$role]['changePassword']);
                        $model[$role]->parent->password_text=$_POST['User'][$role]['changePassword'];
                    }
					if(!$model[$role]->save()){
                        $fail = 1;
					}else{
						$model[$role]->changePassword = '';
					}
					if(!$model[$role]->parent->save()){
                        $fail = 1;
					}
				}
			}
            ChildProfileBasic::setChildSync($this->childId, 1);
            if ($fail === 0){
                Yii::app()->user->setFlash('success', Yii::t("message", "Data Saved!"));
                $this->refresh();
            }
		}
		$this->render('accounts', array('model'=>$model));
	}
	
    public function actionProcessAccountToParent($uid)
    {
        if ($uid && Yii::app()->request->isPostRequest && Yii::app()->request->isAjaxRequest){
            
            $model = User::model()->with('parent')->findByPk($uid);

            if (trim($model->parent->password_text) && $model->pass === md5($model->parent->password_text)){
                $model->setScenario('sendAccount');
                if ($model->validate()){
                    $mailer = Yii::createComponent('common.extensions.mailer.EMailer');
                    $mailer->AddAddress($model->email);
                    $mailer->AddCC($this->childObj->school->info->support_email);
                    $mailer->iniMail( OA::isProduction() ); // 此行代码要放到AddAddress, AddCC方法下面
                	//判断是否为御之桥的邮件
                    if (OA::isYZQ($this->childObj->schoolid)) {
                    	$mailer->Subject = '幼儿园管理系统登录信息 The System Login Account.';
                    	$mailer->getView('yzqparentinfo', array('model'=>$model), 'toyzqparent');
                    }
                    elseif($this->childObj->school->type == 50){
                        $mailer->Subject = 'Daystar Online Login 启明星学校在线系统登录信息';
                        if (CommonUtils::isGradeSchool($this->childObj->classid)){
                            $systemUrl = 'http://dse.ivyonline.cn/';
                        }else{
                            $systemUrl = 'http://dsk.ivyonline.cn/';
                        }
                        $mailer->getView('dsparentinfo', array('model'=>$model,'systemUrl'=>$systemUrl), 'todsparent');
                    }
                    else{
                    	$mailer->Subject = '[IvyOnline] 艾毅在线登录信息 IvyOnline Login Account.';
                    	$mailer->getView('parentinfo', array('model'=>$model), 'toparent');
                    }
                    if ($mailer->Send()) {
                        $this->addMessage('state', 'success');
                        $this->addMessage('message', '发送成功');
                    }
                    else {
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', '发送失败');
                    }
                }
                else {
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', '家长帐号不是合法的电子邮件，帐号信息只能通过电子邮件发送');
                }
            }
            else {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '用户已经修改过密码，请重置密码，保存后再发送');
            }
            
            $this->showMessage();
        }
    }

    // 把中文姓名拆分 变成拼音
    public function actionChildNamePinyin(){
        $fengeName = Yii::createComponent('common.extensions.pinyin.Utf8ToPinyin');
        $pinyin = Yii::createComponent('common.components.pinyin.PinyinTool');

        $childNamePinyin = Yii::app()->request->getParam("childNamePinyin", "");
        $childName = $fengeName->splitName($childNamePinyin);

        $lastName = $pinyin->tool->sentence($childName[1]);
        $lastName2 = str_replace(' ', '', $lastName);

        $this->addMessage('state', 'success');
        $this->addMessage('data', array(ucfirst($pinyin->tool->sentence($childName[0])), ucfirst($lastName2)));
        $this->showMessage();
    }

    /**
     * 设置不同的标签
     *
    */
    public function actionSetFlag()
    {
        $parent = Yii::app()->request->getPost('parent', '');
        $flag1 = Yii::app()->request->getPost('setFlagData1', '');//设置为1的
        $flag0 = Yii::app()->request->getPost('setFlagData0', '');//设置为0的
        if(!$parent){
            $this->addMessage('state', 'fail');
            $this->addMessage('message', 'parent null');
            $this->showMessage();
        }
        $model = $this->childObj->getParents();
        $parent_info = $model[$parent];
        //设置为1
        if(!empty($flag1)){
            foreach ($flag1 as $item1){
                $data = $parent_info->parent->setFlag($item1,1);
                if($data === false){
                    return '数据有误';
                }
                $parent_info->parent->flag = $data;
            }
        }
        //设置为0
        if(!empty($flag0)){
            foreach ($flag0 as $item0){
                $data = $parent_info->parent->setFlag($item0,0);
                if($data === false){
                    return '数据有误';
                }
                $parent_info->parent->flag = $data;
            }
        }
        $res = $parent_info->parent->save();
        if($res){
            $this->addMessage('state', 'success');
            $this->addMessage('message', '成功');
        }else{
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '失败');
        }
        $this->showMessage();
    }

    /**
     * 获取家长的标签
     */
    public function actionGetFlag(){
        $model = $this->childObj->getParents();
        $parentFlag = array();
        $parentMainInfo = array();
        foreach ($model as $parent=>$item){
            if($item){
                $BinaryFlag = $item->parent->getBinaryFlag();//二进制标签
                //获取 从右到左 1的位置
                $BinaryFlagArr = str_split($BinaryFlag);
                krsort($BinaryFlagArr);
                $BinaryFlagArr = array_values($BinaryFlagArr);
                //0的位置
//            $flag0 = array_keys(array_filter($BinaryFlagArr,function ($bit){
//                return !$bit;
//            }));
                //1的位置
                $flag1 = array_keys(array_filter($BinaryFlagArr));
                foreach ($flag1 as $k=>$value){
                    $flag1[$k] = $value+1;
                }
                $parentFlag[$parent] = $flag1;
                $parentMainInfo[$parent] = array(
                    'name'=>$item->parent->getName()
                );
            }
        }
        $data = array(
            'parentMainInfo'=>$parentMainInfo,
            'parentFlagInfo'=>$parentFlag,
            'flagConfig'=>IvyParent::model()->flagConfig(),
        );
        $this->addMessage('state', 'success');
        $this->addMessage('message', '成功');
        $this->addMessage('data', $data);
        $this->showMessage();
    }


}