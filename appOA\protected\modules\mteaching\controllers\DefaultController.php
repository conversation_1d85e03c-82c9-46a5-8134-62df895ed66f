<?php

class DefaultController extends ProtectedController
{
    public function init(){
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'teaching';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site','Teaching Tasks');
    }

    public function actionIndex()
    {
        $notice_dm_school = array('BJ_DS', 'BJ_SLT', 'BJ_QFF', 'BJ_IASLT', 'BJ_OE', 'BJ_TYG', 'BJ_BU', 'BJ_SS');
        if (!in_array($this->staff->profile->branch, $notice_dm_school) && Yii::app()->params['siteFlag'] == 'ivygroup') {
            unset($this->modernMenu['teaching']['Student_Reports']);
        }
        $this->render('application.modules.mcampus.views.default.index', array('key'=>'teaching'));
    }

}