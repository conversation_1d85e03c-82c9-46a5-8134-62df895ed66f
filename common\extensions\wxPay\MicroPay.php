<?php
class MicroPay extends CWidget {
    public $cfg;

    public $auth_code;

    public $body;

    public $detail;

    public $amount;

    public $orderid;

    public $device_info;

    private $input;

    public function init()
    {
        Yii::import('common.extensions.wxPay.lib.*');

        $this->input = new WxPayMicroPay();
        $this->input->cfg = $this->cfg;
    }

    public function run()
    {
        return $this;
    }

    public function pay()
    {
        $this->input->SetAuth_code($this->auth_code);
        $this->input->SetBody($this->body);
        $this->input->SetDetail($this->detail);
        $this->input->SetTotal_fee($this->amount);
        $this->input->SetOut_trade_no($this->orderid);
        $this->input->SetDevice_info($this->device_info);
        $result = WxPayApi::micropay($this->input);
        return $result;
    }

    /**
     *
     * 查询订单情况
     * @param string $out_trade_no  商户订单号
     * @param int $succCode         查询订单结果
     * @return 0 订单不成功，1表示订单成功，2表示继续等待
     */
    public function query()
    {
        $this->input->SetOut_trade_no($this->orderid);
        $result = WxPayApi::orderQuery($this->input);
        return $result;
    }

    /**
     *
     * 撤销订单
     * @param string $out_trade_no
     * @param 调用深度 $depth
     */
    public function cancel($out_trade_no, $depth = 0)
    {
        if($depth > 10){
            return false;
        }

        $clostOrder = new WxPayReverse();
        $clostOrder->SetOut_trade_no($out_trade_no);
        $result = WxPayApi::reverse($clostOrder);

        //接口调用失败
        if($result["return_code"] != "SUCCESS"){
            return false;
        }

        //如果结果为success且不需要重新调用撤销，则表示撤销成功
        if($result["result_code"] != "SUCCESS"
            && $result["recall"] == "N"){
            return true;
        } else if($result["recall"] == "Y") {
            return $this->cancel($out_trade_no, ++$depth);
        }
        return false;
    }

    public function hideAppid($data)
    {
        unset($data['appid']);
        unset($data['mch_id']);
        unset($data['sign']);

        return $data;
    }
}