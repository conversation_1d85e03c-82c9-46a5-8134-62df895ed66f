<?php

/**
 * This is the model class for table "ivy_achievement_report_child_total".
 *
 * The followings are the available columns in table 'ivy_achievement_report_child_total':
 * @property integer $id
 * @property integer $calender_id
 * @property integer $timetable_records_id
 * @property integer $timetable_records_code
 * @property integer $courseid
 * @property integer $childid
 * @property string $oprion_value
 * @property string $frecyion_total
 * @property integer $uid
 * @property integer $create_time
 * @property integer $update_time
 */
class AchievementReportChildTotal extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_achievement_report_child_total';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('courseid, timetable_records_id, timetable_records_code, childid, uid, create_time, update_time', 'required'),
			array('calender_id, timetable_records_id, courseid, childid, uid, create_time, update_time', 'numerical', 'integerOnly'=>true),
			array('oprion_value, frecyion_total, timetable_records_code', 'length', 'max'=>255),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, calender_id, timetable_records_id, timetable_records_code, courseid, childid, oprion_value, frecyion_total, uid, create_time, update_time', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            "courseScoresid" => array(self::BELONGS_TO, 'AchievementCourseScores', array('oprion_value'=>'id')),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'timetable_records_id' => 'Timetable Records Id',
			'timetable_records_code' => 'timetable_records_code',
			'calender_id' => 'Calender',
			'courseid' => 'Courseid',
			'childid' => 'Childid',
			'oprion_value' => 'Oprion Value',
			'frecyion_total' => 'Frecyion Total',
			'uid' => 'Uid',
			'create_time' => 'Create Time',
			'update_time' => 'Update Time',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('timetable_records_id',$this->timetable_records_id);
		$criteria->compare('timetable_records_code',$this->timetable_records_code);
		$criteria->compare('calender_id',$this->calender_id);
		$criteria->compare('courseid',$this->courseid);
		$criteria->compare('childid',$this->childid);
		$criteria->compare('oprion_value',$this->oprion_value,true);
		$criteria->compare('frecyion_total',$this->frecyion_total,true);
		$criteria->compare('uid',$this->uid);
		$criteria->compare('create_time',$this->create_time);
		$criteria->compare('update_time',$this->update_time);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return AchievementReportChildTotal the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}
