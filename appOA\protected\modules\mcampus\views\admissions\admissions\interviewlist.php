<?php
$taskDatas = array(
    'selectMonth' => '2016-12',
);

$array = array(
    '0' => Array
(
    'title'=> 'wangchao',
    'start' => '2016-11-29T13:25',
),
)

?>
<style>
    .fc-button {
        outline: none;
    }
</style>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('//mcampus/default/index')) ?></li>
        <li><?php echo CHtml::link(Yii::t('site', 'Basic'), array('//mcampus/default/index')) ?></li>
        <li><?php echo CHtml::link(Yii::t('site', '招生管理'), array('//mcampus/admissions/index')) ?></li>
        <li><?php echo CHtml::link(Yii::t('site', '入学申请'), array('//mcampus/admissions/admissions')) ?></li>
    </ol>
    <div class="row">
        <!-- 左侧菜单 -->
        <div class="col-md-2 col-sm-2 mb10">
            <?php
            $this->widget('zii.widgets.CMenu', array(
                'items' => $this->getMenu(),
                'id' => 'pageCategory',
                'htmlOptions' => array('class' => 'nav nav-pills nav-stacked background-gray'),
                'activeCssClass' => 'active',
                'itemCssClass' => ''
            ));
            ?>
        </div>

        <div class="col-md-10 col-sm-20">
            <?php
            $this->widget('common.extensions.EFullCalendar.EFullCalendar', array(
                'id' => 'ptcFull-calendar',
//                        'themeCssFile'=>'cupertino/jquery-ui.min.css',

                // raw html tags
                'htmlOptions'=>array(
                    // you can scale it down as well, try 80%
                    'style'=>'width:100%'
                ),
                // FullCalendar's options.
                'options'=>array(
                    'header'=>array(
                        'left' => 'prev,next today',
                        'center'=> 'title',
                        'right' => 'month,agendaWeek,agendaDay,listWeek',
                    ),
                    'firstDay' => 0,
                    'timezone' => 'local',
                    'defaultDate' => $teacher_list['selectMonth'],
//                            'editable' => true,

                    'selectable' => true,
//			                'selectHelper' => true,
                    'select' => "",
                    'eventClick' => "",

                    //示例数据

                    'events' => array(
                        'url' => $this->createUrl('interviewTeacherlist'),
                    ),//$teacher_list['timeslots'],//$taskData['events']
                    'timeFormat' => 'HH:mm',
                    'displayEventEnd' => true,
                )
            ));
            ?>
        </div>
    </div>
</div>
<script>
    $.getJSON('<?php echo $this->createUrl('interviewlist') ?>', function(timezones) {
        $.each(timezones, function(i, timezone) {
            $('#timezone-selector').append(
                $("<option/>").text(timezone).attr('value', timezone)
            );
        });
    });

</script>
<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>

