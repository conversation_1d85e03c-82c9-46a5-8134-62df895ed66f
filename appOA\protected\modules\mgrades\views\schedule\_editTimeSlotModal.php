<script type="text/template" id="timeslot-form-template">
    <?php echo CHtml::hiddenField('postData[id]','<%- id %>', array('encode'=>false));?>
    <?php echo CHtml::hiddenField('postData[weekday]','<%- weekday %>', array('encode'=>false));?>
    <?php echo CHtml::hiddenField('postData[timeslot]','<%- timeslot %>', array('encode'=>false));?>
    <?php echo CHtml::hiddenField('postData[grade_flag]','<%- grade_flag %>', array('encode'=>false));?>
    <?php echo CHtml::hiddenField('postData[classid]','<%- classid %>', array('encode'=>false));?>
    <?php echo CHtml::hiddenField('postData[room_code]','<%- room_code %>', array('encode'=>false));?>
</script>

<!-- Modal -->
<div class="modal" id="editTimeSlotModal" tabindex="-1" role="dialog" aria-labelledby="editTimeSlotModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title" id="myModalLabel">安排课表内容 <small></small></h4>
            </div>
            <form class="form J_ajaxForm" action="<?php echo $this->createUrl('schedule');?>" method="POST">
                <?php echo CHtml::hiddenField('extraData[schedule_id]', $scheduleid);?>
                <div class="modal-body">
                    <div class="row">
                        <div id="form-timeslot-inner-wrapper"></div>
                        <div class="col-md-12">
                            <h4 id="form-hint-title"><small></small></h4>
                            <?php
                            echo CHtml::dropDownList('postData[subject_flag]', null,
                                $programCfg['programs'],
                                array('class'=>'form-control','empty'=>Yii::t('global','Please Select'), 'onchange'=>'cSubject(this)')
                            );
                            ?>
                        </div>

                        <div class="col-md-12">
                            <div id="class-teachers"></div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer pop_bottom">
                    <button onclick="$('#editTimeSlotModal').modal('hide')" type="button" class="btn btn-default pull-right"><?php echo Yii::t('global','Cancel');?></button>
                    <button type="submit" class="btn btn-primary J_ajax_submit_btn pull-right mr10"><?php echo Yii::t('global','Submit');?></button>
                </div>
            </form>
        </div>
    </div>
</div>

<div class="modal" id="openAssign">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title"><?php echo Yii::t('campus','Select Classes');?> <small></small></h4>
            </div>
            <form class="form J_ajaxForm" action="<?php echo $this->createUrl('//mgrades/schedule/assignCla');?>" method="POST">
                <input type="hidden" name="schedule" id="schedule">
                <div class="modal-body">
                    <div id="pendingCla"></div>
                </div>
                <div class="modal-footer pop_bottom">
                    <button onclick="$('#openAssign').modal('hide')" type="button" class="btn btn-default pull-right"><?php echo Yii::t('global','Cancel');?></button>
                    <button type="submit" class="btn btn-primary J_ajax_submit_btn pull-right mr10"><?php echo Yii::t('global','Submit');?></button>
                </div>
            </form>
        </div>
    </div>
</div>