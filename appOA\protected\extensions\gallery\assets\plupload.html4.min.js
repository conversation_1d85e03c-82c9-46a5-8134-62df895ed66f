(function(a){function b(c){return document.getElementById(c)}a.runtimes.Html4=a.addRuntime("html4",{getFeatures:function(){return{multipart:true}},init:function(c,d){c.bind("Init",function(m){var f=document.body,k,e="javascript",g,u,n,o=/MSIE/.test(navigator.userAgent),q=[],j=m.settings.filters,l,h,p,t;for(l=0;l<j.length;l++){h=j[l].extensions.split(/,/);for(t=0;t<h.length;t++){p=a.mimeTypes[h[t]];if(p){q.push(p)}}}q=q.join(",");function s(){var w,v,i;n=a.guid();w=document.createElement("form");w.setAttribute("id","form_"+n);w.setAttribute("method","post");w.setAttribute("enctype","multipart/form-data");w.setAttribute("encoding","multipart/form-data");w.setAttribute("target",m.id+"_iframe");w.style.position="absolute";v=document.createElement("input");v.setAttribute("id","input_"+n);v.setAttribute("type","file");v.setAttribute("accept",q);v.setAttribute("size",1);a.extend(v.style,{width:"100%",height:"100%",opacity:0});i=m.settings.shim_bgcolor;if(i){w.style.background=i}if(o){a.extend(v.style,{filter:"alpha(opacity=0)"})}a.addEvent(v,"change",function(A){var y=A.target,x,z=[];if(y.value){b("form_"+n).style.top=-1048575+"px";x=y.value.replace(/\\/g,"/");x=x.substring(x.length,x.lastIndexOf("/")+1);z.push(new a.File(n,x));s();if(z.length){c.trigger("FilesAdded",z)}}});w.appendChild(v);f.appendChild(w);m.refresh()}function r(){k=document.createElement("iframe");k.setAttribute("src",e+':""');k.setAttribute("id",m.id+"_iframe");k.setAttribute("name",m.id+"_iframe");k.style.display="none";f.appendChild(k);a.addEvent(k,"load",function(x){var y=x.target,w,i;if(!g){return}try{w=y.contentWindow.document||y.contentDocument||window.frames[y.id].document}catch(v){m.trigger("Error",{code:a.SECURITY_ERROR,message:"Security error.",file:g});return}i=w.documentElement.innerText||w.documentElement.textContent;if(i){g.status=a.DONE;g.loaded=1025;g.percent=100;m.trigger("UploadProgress",g);m.trigger("FileUploaded",g,{response:i})}});m.bind("UploadFile",function(i,w){var x,v;if(w.status==a.DONE||w.status==a.FAILED||i.state==a.STOPPED){return}x=b("form_"+w.id);v=b("input_"+w.id);v.setAttribute("name",i.settings.file_data_name);x.setAttribute("action",i.settings.url);a.each(a.extend({name:w.target_name||w.name},i.settings.multipart_params),function(A,z){var y=document.createElement("input");a.extend(y,{type:"hidden",name:z,value:A});x.insertBefore(y,x.firstChild)});g=w;b("form_"+n).style.top=-1048575+"px";x.submit();x.parentNode.removeChild(x)})}if(m.settings.container){f=b(m.settings.container);f.style.position="relative"}m.bind("StateChanged",function(i){if(i.state==a.STARTED){r()}if(i.state==a.STOPPED){window.setTimeout(function(){k.parentNode.removeChild(k)},0)}});m.bind("Refresh",function(i){var v,w,x;v=b(i.settings.browse_button);w=a.getPos(v,b(i.settings.container));x=a.getSize(v);a.extend(b("form_"+n).style,{top:w.y+"px",left:w.x+"px",width:x.w+"px",height:x.h+"px"})});c.bind("FilesRemoved",function(v,x){var w,y;for(w=0;w<x.length;w++){y=b("form_"+x[w].id);y.parentNode.removeChild(y)}});s()});d({success:true})}})})(plupload);