<?php
/**
 * 微信公众号支付
 */
class JsPay extends CWidget {

    public $cfg = array();

    public $values = array();

    private $input;

    public function init()
    {
        Yii::import('common.extensions.wxPay.lib.*');

        $this->input = new WxPayMicroPay();
        $this->values['trade_type'] = 'JSAPI';

        $this->input->cfg = $this->cfg;
        $this->input->values = $this->values;
    }

    public function run()
    {
        return $this;
    }

    /**
     * 统一下单返回结果
     * @param string $value [description]
     */
    public function resultInfo()
    {
        $res = WxPayApi::unifiedOrder($this->input);
        Yii::log(json_encode($res), 'info', 'unifiedOrder.wechat');
        $appid = $this->input->values['appid'];
        $prepay_id = $this->input->values['prepay_id'];
        $jsapi = new WxPayJsApiPay();
        $jsapi->SetAppid($appid);
        $timeStamp = time();
        $jsapi->cfg = $this->cfg;
        $jsapi->SetTimeStamp("$timeStamp");
        $jsapi->SetNonceStr(WxPayApi::getNonceStr());
        $jsapi->SetPackage("prepay_id=" . $prepay_id);
        $jsapi->SetSignType("MD5");
        $jsapi->SetPaySign($jsapi->MakeSign());
        $jsPayInfo = json_encode($jsapi->GetValues());
        return $jsPayInfo;
    }

    /**
     * 货币转换
     * @param  integer $money [description]
     * @return [type]         [description]
     */
    public function transformMoney($money = 0)
    {
        $money = intval($money*100);
        if ($money != 0) {
            return $money;
        }
        return false;
    }

    /**
     * XML转换
     * @param [type] $xml [description]
     */
    public function fromXml($xml)
    {
        $this->values = $this->input->FromXml($xml);
        return $this->values;
    }

    /**
     * 验证签名
     */
    public function checkSign()
    {
        return $this->input->CheckSign();
    }
    
    /**
     * 数组转换为xml
     * @param  [type] $arr [array]
     * @return [type] $xml [string]
     */
    public function toXml($arr)
    {
        $xml = "<xml>";
        foreach ($arr as $key=>$val)
        {
            if (is_numeric($val)){
                $xml.="<".$key.">".$val."</".$key.">";
            }else{
                $xml.="<".$key."><![CDATA[".$val."]]></".$key.">";
            }
        }
        $xml.="</xml>";
        echo $xml; 
    }
}