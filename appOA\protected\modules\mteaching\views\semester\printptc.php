<style type="text/css">
    th{
    	text-align:center;
    }
</style>
<div class="">
    <h3><?php echo $classTitle; ?></h3>
    <?php
    if($dataArr){
    foreach ($dataArr as $mon=>$item){ ?>
        <h4><?php echo $mon ?></h4>
        <table class='table table-bordered'>
            <tr>
                <th><?php echo Yii::t('attends','Sun'); ?></th>
                <th><?php echo Yii::t('attends','Mon'); ?></th>
                <th><?php echo Yii::t('attends','Tue'); ?></th>
                <th><?php echo Yii::t('attends','Wed'); ?></th>
                <th><?php echo Yii::t('attends','Thu'); ?></th>
                <th><?php echo Yii::t('attends','Fri'); ?></th>
                <th><?php echo Yii::t('attends','Sat'); ?></th>
            </tr>
                <?php
                    foreach ($item as $val) {
                        ?>
                    <tr>
                        <?php foreach ($val as $v) { ?>
                            <td style="width:100px">
                                <?php
                                $dayTime = ($v > 0) ? date("m-d", strtotime($v)) : "";
                                echo '<p class="text-right">'.$dayTime.'</p>';

                                if($events && $events[$v]){ ?>
                                    <?php foreach ($events[$v] as $times){
                                        $title = ($times['title']) ? ' ' . $times['title']: "" ;
                                        echo '<div class="borderBto"><hr>'.$times['start'] . '-' . $times['end'] . $title .'</div>';
                                    } ?>
                                <?php }?>
                            </td>
                        <?php } ?>
                    </tr>
                <?php } ?>
        </table>
    <?php }
    } ?>
</div>