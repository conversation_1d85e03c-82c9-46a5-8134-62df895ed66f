<?php

/**
 * This is the model class for table "ivy_pta_memo".
 *
 * The followings are the available columns in table 'ivy_pta_memo':
 * @property integer $id
 * @property string $title
 * @property string $schoolid
 * @property integer $memo_date
 * @property string $pta_members
 * @property string $pta_admins
 * @property integer $timestamp
 * @property integer $uid
 */
class PtaMemo extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return PtaMemo the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_pta_memo';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('schoolid, memo_date', 'required'),
			array('timestamp, uid', 'numerical', 'integerOnly'=>true),
			array('title', 'length', 'max'=>255),
			array('schoolid', 'length', 'max'=>30),
            array('pta_members, pta_admins', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, title, schoolid, memo_date, pta_members, pta_admins, timestamp, uid', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'content' => array(self::HAS_ONE, 'PtaMemoContent', 'id'),
            'school' => array(self::BELONGS_TO, 'Branch', 'schoolid'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'title' => '会议主题',
			'schoolid' => '校园',
			'memo_date' => '会议日期',
			'pta_members' => '家长参会人员',
			'pta_admins' => '老师参会人员',
			'timestamp' => 'Timestamp',
			'uid' => 'Uid',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('title',$this->title,true);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('memo_date',$this->memo_date);
		$criteria->compare('pta_members',$this->pta_members,true);
		$criteria->compare('pta_admins',$this->pta_admins,true);
		$criteria->compare('timestamp',$this->timestamp);
		$criteria->compare('uid',$this->uid);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

    public function renderMembers()
    {
        $users = null;
        if($this->pta_members){
            $uids = array();
            $uids = explode(',', substr($this->pta_members, 0, -1));

            $criteria = new CDbCriteria();
            $criteria->compare('uid', $uids);
            $users = User::model()->findAll($criteria);
        }
        return $users;
    }

    public function renderAdmins()
    {
        $users = null;
        if($this->pta_admins){
            $uids = array();
            $uids = explode(',', substr($this->pta_admins, 0, -1));

            $criteria = new CDbCriteria();
            $criteria->compare('uid', $uids);
            $users = User::model()->findAll($criteria);
        }
        return $users;
    }

    public function renderTitle()
    {
        $langTag = '{{lang-tag}}';

        if(strpos($this->title, $langTag) !== false){
            $tmp = explode($langTag, $this->title);
            $title = HtoolKits::autoLang($tmp[0], $tmp[1]);
        }
        else{
            $title = $this->title;
        }
        return $title;
    }
}