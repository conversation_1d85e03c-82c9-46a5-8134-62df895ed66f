<?php

$data = array();
$data['allNum'] = 0;
$data['step'] = 0;
$data['stepNum'] = 0;
$data['data'] = array();
if(!empty($res['data'])){
    foreach ($res['data'] as $val){
        $medicalReport = array();
        if($val['info']->medicalReport){
            foreach ($val['info']->medicalReport as $item){
                $medicalReport[] = RegExtraInfo::getOssImageUrl($item);;
            }
        }
        $vaccineReport = array();
        if($val['info']->vaccineReport){
            foreach ($val['info']->vaccineReport as $item){
                $vaccineReport[] = RegExtraInfo::getOssImageUrl($item);;
            }
        }
        $healthReport = array();
        if($val['info']->healthReport){
            foreach ($val['info']->healthReport as $item){
                $healthReport[] = RegExtraInfo::getOssImageUrl($item);;
            }
        }
        $info = array(
            'preferredHospitalOne' => $val['info']->preferredHospitalOne,
            'preferredHospitalTwo' => $val['info']->preferredHospitalTwo,
            'insuranceCompany' => $val['info']->insuranceCompany,
            'oneEmergencyName' => $val['info']->oneEmergencyName,
            'oneEmergencyPhone' => $val['info']->oneEmergencyPhone,
            'twoEmergencyName' => $val['info']->twoEmergencyName,
            'twoEmergencyPhone' => $val['info']->twoEmergencyPhone,
            'ADHD' => $val['info']->ADHD,
            'heartDisorder' => $val['info']->heartDisorder,
            'allergies' => $val['info']->allergies,
            'frequent' => $val['info']->frequent,
            'asthma' => $val['info']->asthma,
            'hepatitis' => $val['info']->hepatitis,
            'problems' => $val['info']->problems,
            'gastrointertianl' => $val['info']->gastrointertianl,
            'fractures' => $val['info']->fractures,
            'skinProblems' => $val['info']->skinProblems,
            'diabetes' => $val['info']->diabetes,
            'visionProblems' => $val['info']->visionProblems,
            'seizureDisorde' => $val['info']->seizureDisorde,
            'tuberculosis' => $val['info']->tuberculosis,
            'tuberculosisOne' => $val['info']->tuberculosisOne,
            'tuberculosisTwo' => $val['info']->tuberculosisTwo,
            'tuberculosisThree' => $val['info']->tuberculosisThree,
            'tuberculosisFour' => $val['info']->tuberculosisFour,
            'specialFood' => $val['info']->specialFood,
            'other' => $val['info']->other,
            'filedata' => $val['info']->filedata,
            'medicalReport' => $medicalReport,
            'vaccineReport' => $vaccineReport,
            'healthReport' => $healthReport,
        );
        $data['allNum'] = $res['allNum'];
        $data['step'] = $res['step'];
        $data['stepNum'] = $res['stepNum'];
        $data['unStart'] = $res['unStart'];
        $data['unStartNum'] = $res['unStartNum'];
        $data['data'][] = array(
            'id' => $val['id'],
            'childid' => $val['childid'],
            'complete' => $val['complete'],
            'reject_memo' => $val['reject_memo'],
            'link_id' => $val['link_id'],
            'classid' => $val['classid'],
            'childid_name' => $val['childid_name'],
            'child_birthday' => $val['child_birthday'],
            'class_title' => $val['class_title'],
            'parent_id' => $val['parent_id'],
            'parent_phone' => $val['parent_phone'],
            'updated' => $val['updated'],
            'info' => $info,
        );
    }
}
?>
<div id='shuttle' v-cloak>
    <div class="progress">
        <div class="progress-bar progress-bar-success" :style="'width:'+ parsefloat(adopt.length/total) +'%;'">
            <span v-if='adopt.length!=0' :title="adopt.length+'/'+total">{{parsefloat(adopt.length/total)}}% ({{adopt.length}}/{{total}})</span>
            <span v-else>0% ({{adopt.length}}/{{total}})</span>
        </div>
        <div class="progress-bar progress-bar-warning progress-bar-striped" :style="'width:'+ parsefloat(defaults.length/total) +'%;'">
            <span v-if='defaults.length!=0' :title="defaults.length+'/'+total">{{parsefloat(defaults.length/total)}}% ({{defaults.length}}/{{total}})</span>
            <span v-else>0% ({{defaults.length}}/{{total}})</span>
        </div>
        <div class="progress-bar progress-bar-danger" :style="'width:'+ parsefloat(notPass.length/total) +'%;'">
            <span v-if='notPass.length!=0' :title="notPass.length+'/'+total">{{parsefloat(notPass.length/total)}}% ({{notPass.length}}/{{total}})</span>
            <span v-else>0% ({{notPass.length}}/{{total}})</span>
        </div>
    </div>
    <ul class="nav nav-tabs" role="tablist">
        <li role="presentation" class="active"><a href="#home" aria-controls="home" role="tab" data-toggle="tab">已填写 <span class="badge">{{res.stepNum}}</span></a></li>
        <li role="presentation"><a href="#unstart" aria-controls="unstart" role="tab" data-toggle="tab">未填写 <span class="badge badge-error">{{res.unStartNum}}</span></a></li>
    </ul>
    <div class="tab-content">
        <div role="tabpanel" class="tab-pane active" id="home">
            <table class="table table-bordered mb0" v-if='tableData.length!=0'>
                <thead id='table'>
                    <tr>
                        <th class="nosort">
                            <label class="checkbox-inline">
                                <input type="checkbox" id="inlineCheckbox1"  v-model="checked" @click="changeAllChecked()">全选
                            </label>
                        </th>
                        <th>名字</th>
                        <th>班级</th>
                        <th>状态</th>
                        <th>生日</th>
                        <th class="nosort">签名</th>
                        <th>提交时间</th>
                        <th class="nosort">操作</th>
                    </tr>
                </thead>
                <tbody>
                    <template v-for='(list,index) in tableData'>
                        <tr>
                            <td width="6%">
                                <input type="checkbox" id="inlineCheckbox1" :value="list.id" v-model="checkedNames">
                            </td>
                            <td><a href="javascript:;" onclick="overalls(this)" :data-id='list.childid' :data-name='list.childid_name' :data-class='list.class_title'>{{list.childid_name}}</a></td>
                            <td>{{list.class_title}}</td>
                            <td width="10%">{{completeConfig[list.complete]}}</td>
                            <td width="10%">{{list.child_birthday}}</td>
                            <td width="10%">
                                <img :src="list.info.filedata" v-if='list.info.filedata' class="img-thumbnail sign" @click='bigImg(list.info)'>
                            </td>
                            <td width="12%">{{list.updated}}</td>
                            <td width="12%">
                                <button class="btn btn-primary btn-xs" @click='Edit(index,0)'>查看</button>
                                <a  class="btn btn-primary btn-xs" :href="'<?php echo $this->createUrlReg('print', array('branchId' => $this->branchId)); ?>&infoId='+list.id+'&stepId=4&status=4'" target="_blank">打印</a>
                            </td>
                        </tr>
                    </template>
                </tbody>
            </table>
            <div v-else class="mt15">
                <div class="alert alert-warning" role="alert">暂无数据</div>
            </div>
            <p><button class="btn btn-primary" @click='checkAll()' v-if='tableData.length!=0'>批量审核</button></p>
        </div>
        <!-- 未填写的数据 -->
        <div role="tabpanel" class="tab-pane " id="unstart">
            <table class="table table-bordered mb0s" id='unTable' v-if='unStart.length!=0'>
                <thead>
                    <tr>
                        <th>名字</th>
                        <th>班级</th>
                        <th>状态</th>
                        <th>生日</th>
                    </tr>
                </thead>
                <tbody>
                <template v-for='(list,index) in unStart'>
                    <tr>
                        <td width="25%"><a href="javascript:;" onclick="overalls(this)" :data-id='list.childid' :data-name='list.childid_name' :data-class='list.class_title'>{{list.childid_name}}</a></td>
                        <td width="25%">{{list.class_title}}</td>
                        <td width="25%">未填写</td>
                        <td width="25%">{{list.child_birthday}}</td>
                    </tr>
                </template>
                </tbody>
             </table>
            <div v-else class="mt15">
                <div class="alert alert-warning" role="alert">暂无数据</div>
            </div>
        </div>
    </div>
    <div class="modal fade bs-example-modal-lg" id="Edit" data-backdrop="static">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">
                        &times;
                    </button>
                    <h4 class="modal-title" id="myModalLabel">查看
                    </h4>
                </div>
                <div class="modal-body">
                    <div>
                        <p v-if='historyData.length > 0'>当前数据</p>
                        <table class="table table-bordered">
                            <tbody>
                            <tr>
                                <th width="20%">体检报告</th>
                                <td colspan="3">
                                    <template  v-for='(medical,inx) in info.medicalReport' >
                                        <a :href="medical" target="_blank">
                                            <img :src="medical"  class="img-thumbnail card mr5">
                                        </a>
                                    </template>                                            
                                </td>
                            </tr>
                            <tr>
                                <th>疫苗打针记录需复印件</th>
                                <td colspan="3">
                                    <template  v-for='(vaccin,inx) in info.vaccineReport' >
                                        <a :href="vaccin" target="_blank">
                                            <img :src="vaccin"  class="img-thumbnail card mr5">
                                        </a>
                                    </template>    
                                </td>
                            </tr>
                            <tr>
                                <th>意向医院1</th>
                                <td colspan="3">{{info.preferredHospitalOne}}</td>
                            </tr>
                            <tr>
                                <th>意向医院2</th>
                                <td colspan="3">{{info.preferredHospitalTwo}}</td>
                            </tr>
                            <tr>
                                <th>保险公司</th>
                                <td colspan="3">{{info.insuranceCompany}}</td>
                            </tr>
                            <tr>
                                <th>紧急联系人1</th>
                                <td colspan="3">{{info.oneEmergencyName}}</td>
                            </tr>
                            <tr>
                                <th>紧急联系人电话1</th>
                                <td colspan="3">{{info.oneEmergencyPhone}}</td>
                            </tr>
                            <tr>
                                <th>紧急联系人2</th>
                                <td colspan="3">{{info.twoEmergencyName}}</td>
                                
                            </tr>
                            <tr>
                                <th>紧急联系人电话2</th>
                                <td colspan="3">{{info.twoEmergencyPhone}}</td>
                            </tr>
                            <tr>
                                <th>多动症</th>
                                <td>{{info.ADHD== '1' ? '是' : '否'}}</span></td>
                                <th width="20%">心脏病</th>
                                <td>{{info.heartDisorder== '1' ? '是' : '否'}}</td>
                            </tr>
                            <tr>
                                <th>过敏（食品、药品、其他）</th>
                                <td>{{info.allergies== '1' ? '是' : '否'}}</td>
                                <th>耳部疾病（听力）</th>
                                <td>{{info.frequent== '1' ? '是' : '否'}}</td>
                            </tr>
                            <tr>
                                <th>哮喘</th>
                                <td>{{info.asthma== '1' ? '是' :'否'}}</td>
                                <th>肝炎</th>
                                <td>{{info.hepatitis== '1' ? '是' :'否'}}</td>
                            </tr>
                            <tr>
                                <th>背部或脊柱问题</th>
                                <td>{{info.problems== '1' ? '是' : '否'}}</td>
                                <th>消化系统疾病</th>
                                <td>{{info.gastrointertianl== '1' ? '是' : '否'}}</td>
                            </tr>
                            <tr>
                                <th>骨折</th>
                                <td>{{info.fractures== '1' ? '是' :'否'}}</td>
                                <th>皮肤病</th>
                                <td>{{info.skinProblems== '1' ? '是' : '否'}}</td>
                            </tr>
                            <tr>
                                <th>糖尿病</th>
                                <td>{{info.diabetes== '1' ? '是' : '否'}}</td>
                                <th>视力/色觉问题</th>
                                <td>{{info.visionProblems== '1' ? '是' : '否'}}</td>
                            </tr>
                            <tr>
                                <th>癫病</th>
                                <td>{{info.seizureDisorde== '1' ? '是' : '否'}}</td>
                                <th>肺结核</th>
                                <td>{{info.tuberculosis== '1' ? '是' : '否'}}</td>
                            </tr>
                            <tr>
                                <th>咳嗽、咳痰持续2周以上</th>
                                <td>{{info.tuberculosisOne== '1' ? '是' : '否'}}</td>
                                <th>反复咳出的痰中带血</th>
                                <td>{{info.tuberculosisTwo== '1' ? '是' : '否'}}</td>
                            </tr>
                            <tr>
                                <th>反复发热持续2周以上</th>
                                <td>{{info.tuberculosisThree== '1' ? '是' : '否'}}</td>
                                <th>经常见面的家人、亲戚、朋友中2年内是否有肺结核病人</th>
                                <td>{{info.tuberculosisFour== '1' ? '是' : '否'}}</td>
                            </tr>
                            <tr>
                                <th>过敏信息（请详述）（包括但不限于食物及药品） </th>
                                <td colspan="3">{{info.specialFood}}</td>
                            </tr>
                            <tr>
                                <th>其他疾病</th>
                                <td colspan="3">{{info.other}}</td>
                            </tr>
                            <tr>
                                <th>该学生有定期使用（口服或者注射）任何药物吗？如有，请详细说明。 </th>
                                <td colspan="3">{{info.otherDiseases}}</td>
                            </tr>
                            <tr>
                                <th>签名 </th>
                                <td colspan="3">
                                    <img :src="info.filedata" v-if='info.filedata' class="img-thumbnail sign">
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <div v-if='historyData.length > 0'>
                        <p class="mt15">驳回数据</p>
                       <table class="table table-bordered" v-for='(history,idx) in historyData'>
                            <tbody>
                            <tr>
                                <th width="20%">体检报告</th>
                                <td colspan="3">
                                    <template  v-for='(medical,inx) in history.medicalReport' >
                                        <a :href="medical" target="_blank">
                                            <img :src="medical"  class="img-thumbnail card mr5">
                                        </a>
                                    </template>  
                                </td>
                            </tr>
                            <tr>
                                <th>疫苗打针记录需复印件</th>
                                <td colspan="3">
                                    <template  v-for='(vaccin,inx) in history.vaccineReport' >
                                        <a :href="vaccin" target="_blank">
                                            <img :src="vaccin"  class="img-thumbnail card mr5">
                                        </a>
                                    </template>    
                                </td>
                            </tr>
                            <tr>
                                <th>意向医院1</th>
                                <td colspan="3">{{history.preferredHospitalOne}}</td>
                            </tr>
                            <tr>
                                <th>意向医院2</th>
                                <td colspan="3">{{history.preferredHospitalTwo}}</td>
                            </tr>
                            <tr>
                                <th>保险公司</th>
                                <td colspan="3">{{history.insuranceCompany}}</td>
                            </tr>
                            <tr>
                                <th>紧急联系人1</th>
                                <td colspan="3">{{history.oneEmergencyName}}</td>
                                
                            </tr>
                            <tr>
                                <th>紧急联系人电话1</th>
                                <td colspan="3">{{history.oneEmergencyPhone}}</td>
                            </tr>
                            <tr>
                                <th>紧急联系人2</th>
                                <td colspan="3">{{history.twoEmergencyName}}</td>
                            </tr>
                            <tr>
                                <th>紧急联系人电话2</th>
                                <td colspan="3">{{history.twoEmergencyPhone}}</td>
                            </tr>
                            <tr>
                                <th>多动症</th>
                                <td>{{history.ADHD== '1' ? '是' : '否'}}</span></td>
                                <th width="20%">心脏病</th>
                                <td>{{history.heartDisorder== '1' ? '是' :'否'}}</td>
                            </tr>
                            <tr>
                                <th>过敏（食品、药品、其他）</th>
                                <td>{{history.allergies== '1' ? '是' :  '否'}}</td>
                                <th>耳部疾病（听力）</th>
                                <td>{{history.frequent== '1' ? '是' : '否'}}</td>
                            </tr>
                            <tr>
                                <th>哮喘</th>
                                <td>{{history.asthma== '1' ? '是' : '否'}}</td>
                                <th>肝炎</th>
                                <td>{{history.hepatitis== '1' ? '是' : '否'}}</td>
                            </tr>
                            <tr>
                                <th>背部或脊柱问题</th>
                                <td>{{history.problems== '1' ? '是' : '否'}}</td>
                                <th>消化系统疾病</th>
                                <td>{{history.gastrointertianl== '1' ? '是' : '否'}}</td>
                            </tr>
                            <tr>
                                <th>骨折</th>
                                <td>{{history.fractures== '1' ? '是' : '否'}}</td>
                                <th>皮肤病</th>
                                <td>{{history.skinProblems== '1' ? '是' :  '否'}}</td>
                            </tr>
                            <tr>
                                <th>糖尿病</th>
                                <td>{{history.diabetes== '1' ? '是' : '否'}}</td>
                                <th>视力问题</th>
                                <td>{{history.visionProblems== '1' ? '是' : '否'}}</td>
                            </tr>
                            <tr>
                                <th>癫病</th>
                                <td>{{history.seizureDisorde== '1' ? '是' :  '否'}}</td>
                                <th>结核</th>
                                <td>{{history.tuberculosis== '1' ? '是' : '否'}}</td>
                            </tr>
                            <tr>
                                <th>过敏信息（请详述）（包括但不限于食物及药品） </th>
                                <td colspan="3">{{history.specialFood}}</td>
                            </tr>
                            <tr>
                                <th>其他疾病</th>
                                <td colspan="3">{{history.other}}</td>
                            </tr>
                            <tr>
                                <th>该学生有定期使用（口服或者注射）任何药物吗？如有，请详细说明。 </th>
                                <td colspan="3">{{history.otherDiseases}}</td>
                            </tr>
                            <tr>
                                <th>签名 </th>
                                <td colspan="3">
                                     <img :src="info.filedata" v-if='info.filedata' class="img-thumbnail sign">
                                </td>
                            </tr>
                            <tr>
                                <th>驳回原因</th>
                                <td colspan="3">{{history.reject_memo}}</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <p class="mt15">
                        <label class="radio-inline">
                            <input type="radio" name="inlineRadioOptions" id="inlineRadio1" value="1"  v-model="complete"> 审核通过
                        </label>
                        <label class="radio-inline">
                            <input type="radio" name="inlineRadioOptions" id="inlineRadio2" value="-1"  v-model="complete"> 驳回
                        </label>
                    </p>
                    <p v-if='complete==-1'>
                        <input type="text" class="form-control" id="reject_memo" placeholder="请写出驳回原因" :value='Thisdata.reject_memo'>
                    </p>
                </div>
                <div class="modal-footer">
                    <!--<a class="btn btn-primary" :href="'<?php /*echo $this->createUrlReg('print', array('branchId' => $this->branchId)); */?>&infoId='+Thisdata.id+'&stepId=4'" target="_blank">打印表格</a>
                    <a class="btn btn-primary" :href="'<?php /*echo $this->createUrlReg('printAnnex', array('branchId' => $this->branchId)); */?>&infoId='+Thisdata.id+'&stepId=4'" target="_blank">打印附件</a>-->
                    <button type="button" class="btn btn-primary" @click='checkAll()'>提交</button>
                    <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade bs-example-modal-lg" id="bigImg">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">
                        &times;
                    </button>
                    <h4 class="modal-title" id="myModalLabel">查看大图
                    </h4>
                </div>
                <div class="modal-body">
                    <img :src="imgBig" style="width:500px">
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    var res = <?php echo json_encode($data); ?>;
    var completeConfig = <?php echo json_encode($completeConfig) ?>;//状态
    var shuttle = new Vue({
        el: "#shuttle",
        data: {
            checked: false,
            checkedNames: [],
            tableArr: [],
            res:res,
            total: res.allNum,
            number:res.number,
            tableData:res.data,
            info: '',
            Thisdata: '',
            historyData: '',
            complete: '',
            adopt: [], //通过
            defaults: [], //默认
            notPass: [], //驳回
            YearList: '',
            completeConfig:completeConfig,
            'imgBig':'',
            unStart:res.unStart,
            overAll:'',
        },
        created: function() {
            for(var i = 0; i < res.data.length; i++) {
                this.tableArr.push(res.data[i].id)
                if(res.data[i].complete == 1) {
                    this.adopt.push(res.data[i].complete)
                } else if(res.data[i].complete == 0 || res.data[i].complete == 2 ) {
                    this.defaults.push(res.data[i].complete)
                } else {
                    this.notPass.push(res.data[i].complete)
                }
            }
        },
        watch: {
            "checkedNames": function() {
                if(this.checkedNames.length != this.tableArr.length) {
                    this.checked = false
                } else {
                    this.checked = true
                }
            }
        },
        mounted: function() {},
        methods: {
            parsefloat(num) {
                return Number(num * 100).toFixed(2);
            },
            bigImg(img){
                this.imgBig=img.filedata
                $('#bigImg').modal('show')
            },
            changeAllChecked: function() {
                var self = this;
                if(!self.checked) {
                    self.checkedNames = []
                } else {
                    self.checkedNames = self.tableArr;
                }
            },
            checkAll() {
                var datas = ''
                if(this.Thisdata != '') {
                    datas = {
                        step_ids: this.Thisdata.id,
                        complete: this.complete,
                        reject_memo: $('#reject_memo').val()
                    }
                } else {
                    datas = {
                        step_ids: this.checkedNames,
                        complete: '1'
                    }
                }
                $.ajax({
                    url: "<?php echo $this->createUrlReg('check')?>",
                    type: 'post',
                    dataType: 'json',
                    data: datas,
                    success: function(data) {
                        if(data.state == "success") {
                            resultTip({
                                msg: data.message
                            });
                            if(shuttle.Thisdata != '') {
                                shuttle.Thisdata.complete = shuttle.complete
                                shuttle.Thisdata.reject_memo = $('#reject_memo').val()
                                $('#Edit').modal('hide')
                                shuttle.Thisdata = []

                            } else {
                                for(key in shuttle.tableData) {
                                    for(var i = 0; i < shuttle.checkedNames.length; i++) {
                                        if(shuttle.tableData[key].id == shuttle.checkedNames[i]) {
                                            Vue.set(shuttle.tableData[key], 'complete', '1')
                                        }
                                    }
                                }
                                shuttle.checkedNames = []
                            }
                            shuttle.adopt = []
                            shuttle.notPass = []
                            shuttle.defaults = []
                            for(var i = 0; i < shuttle.tableData.length; i++) {
                                if(shuttle.tableData[i].complete == 1) {
                                    shuttle.adopt.push(shuttle.tableData[i].complete)
                                } else if(shuttle.tableData[i].complete == 0) {
                                    shuttle.defaults.push(shuttle.tableData[i].complete)
                                } else {
                                    shuttle.notPass.push(shuttle.tableData[i].complete)
                                }
                            }
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        alert('请求错误')
                    }
                })
            },
            Edit(index) {
                this.Thisdata = this.tableData[index]
                this.info = this.tableData[index].info
                this.complete = this.tableData[index].complete
                if(this.tableData[index].link_id != '0') {
                    $.ajax({
                        url: "<?php echo $this->createUrlReg('showDetail')?>",
                        type: 'post',
                        dataType: 'json',
                        data: {
                            step_id: this.tableData[index].link_id, //-1驳回 0未操作 1通过
                        },
                        success: function(data) {
                            if(data.state == "success") {
                                shuttle.historyData = data.data
                                $('#Edit').modal('show')
                            } else {
                                resultTip({
                                    error: 'warning',
                                    msg: data.message
                                });
                            }
                        },
                        error: function(data) {
                            alert('请求错误')
                        }
                    })
                } else {
                    $('#Edit').modal('show')
                }
            },
        },
    })
</script>
<script>
    $(document).ready( function () {
        var table = $('#table').parent().DataTable({
                order: [[ 3, "desc" ], [ 6, "desc" ]],
                paging: false,
                info: false,
                searching: false,
                columnDefs: [ {
                    "targets": 'nosort',
                    "orderable": false
                } ],
        });
        var table = $('#unTable').DataTable({
                aaSorting: [0, 'desc'], // 默认排序
                paging: false,
                info: false,
                searching: false,
        });
    });
</script>