<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo Yii::t('site','Finance Management');?></li>
        <li class="active">课后课财务</li>
    </ol>

    <div class="row">
       <!-- 左侧菜单 -->
        <div class="col-md-2 col-sm-2 mb10">
            <?php
            $this->widget('zii.widgets.CMenu', array(
                'items' => $this->getMenu(),
                'id' => 'pageCategory',
                'htmlOptions' => array('class' => 'nav nav-pills nav-stacked background-gray'),
                'activeCssClass' => 'active',
                'itemCssClass' => ''
            ));
            ?>
        </div>
            <div class="col-md-10 col-sm-12">
            <?php
                $this->widget('zii.widgets.CMenu', array(
                    'items' => $this->RefundMenu(),
                    'id' => 'AttendMenu',
                    'htmlOptions' => array('class' => 'nav nav-tabs'),
                    'activeCssClass' => 'active',
                    'itemCssClass' => ''
                ));
            ?>
            <div class="page-header">
                    <h3><?php echo Yii::t('asa', 'refund');?>
                        <small><?php echo Yii::t("asa","refund information");?></small>
                    </h3>
            </div>
            <div class="mb10 row">
                    <!-- 搜索框 -->
                    <form class="" style="float: left;width: 100%" action="<?php echo $this->createUrl('refunds'); ?>"
                          method="get">
                        <?php //echo Chtml::hiddenField('branchId', $this->branchId); ?>
                        <div class="col-sm-2 form-group">
                            <?php echo Chtml::textField('childName', $_GET['childName'], array('class' => 'form-control', 'placeholder' => '孩子姓名')) ?>
                        </div>
                        <div class="col-sm-2 form-group">
                            <?php echo Chtml::dropDownList('courseGroup', $_GET['courseGroup'], $courseGroups, array('class' => 'form-control', 'onChange' => 'redirec()', 'empty' => Yii::t('teaching', '请选择课程组'))); ?>
                        </div>
                        <div class="col-sm-2 form-group">
                            <?php echo Chtml::dropDownList('course', $_GET['course'], array(), array('class' => 'form-control', 'empty' => Yii::t('teaching', '请选择课程'))); ?>
                        </div>
                        <div class="">
                            <div class="">
                                <button class="btn btn-default ml5" type="submit"><span
                                        class="glyphicon glyphicon-search"> </span></button>
                            </div>
                        </div>
                    </form>
                </div>
            <!-- <div class="col-md-12"></div>
            <div class="panel panel-default">
                <div class="panel-body"> -->

                <?php
                $this->widget('ext.ivyCGridView.BsCGridView', array(
                    'id' => 'course-grid',
                    'afterAjaxUpdate' => 'js:head.Util.modal',
                    'dataProvider' => $refund,
                    'template' => "{items}{pager}",
                    //状态为无效时标红
                    'rowCssClassExpression' => '( $data->status == 0 ? "active" : "" )',
                    'colgroups' => array(
                        array(
                            "colwidth" => array(50, 100, 100, 100, 100, 100, 100, 100, 100, 100),
                        )
                    ),
                    'columns' => array(
                        array(
                            'name' => '#',
                            'value'=>'$data->id',
                        ),
                        array(
                            'name' => 'site_id',
                            'value'=>array($this, "getSchoolName"),
                        ),
                        array(
                            'name' => "childid",
                            'value'=> array($this, "getChildName"),
                            'type' => "raw",
                        ),
                        array(
                            'name' => Yii::t('child', '课程名称 (时间表名称)'),
                            'value' => array($this, "getCourseName"),
                            'type' => "raw"
                        ),
                        array(
                            'name' => "价格",
                            'value'=> array($this, "getAmount"),
                        ),
                        array(
                            'name' => 'refund_method',
                            'value'=>array($this, "getRefundMethod"),
                        ),
                        array(
                            'name'=>'updated',
                            'value'=>'date("Y-m-d", $data->updated)',
                        ),
                        array(
                            'name'=>'updated_done',
                            'value'=>'date("Y-m-d", $data->updated_done)',
                        ),
                        array(
                            'name'=> Yii::t('asa','操作'),
                            'value'=>array($this, "getCourseButton"),
                        ),
                    ),
                ));
                ?>
               <!--  </div>-->
                           </div> 
        </div>
    </div>
</div>

<script>
    var  courseVal;
    var modal = '<div class="modal fade" id="modal" class="1123" tabindex="-1" role="dialog" aria-labelledby="modal"><div class="modal-dialog modal-lg" role="document"><div class="modal-content"></div></div></div>';
    $('body').append(modal);
    var courses = <?php echo CJSON::encode($courses); ?>;
    var courseId = <?php echo ($_GET['course']) ? $_GET['course'] : 0 ?>;
    //console.log(courseId);
    //
    function cbDelRefind(){
        $.fn.yiiGridView.update('refund');
    }
    function cbUpdateRefind(){
        $('#refund-forms').modal('hide');
        $.fn.yiiGridView.update('refund');
    }

    function redirec() {
        courseVal = $('#courseGroup option:selected').val();
        if(courseVal){
            var courseList;
            $.each(courses[courseVal], function (i,l) {
                courseList += '<option value="' +i + '">'+l+'</option>';
            })
            $("#course").html(courseList);
        }else{
            $("#course").html('<option value="">请选择课程</option>');
        }
    }

    if(courseId){
        redirec();
        $("#course").val(courseId);
    }

</script>

<?php
    //$this->renderPartial('//layouts/common/branchSelectBottom');
?> 