<?php
	foreach(Yii::app()->user->getFlashes() as $key => $message) {
        echo '<div class="flash-' . $key . '">' . $message . "</div>\n";
    }
?>
<style>
    .warningContent{
        color: #8a6d3b;
        background-color: #fcf8e3;
        border-color: #faebcc;
        padding: 15px;
        font-size: 12px;
        margin-bottom: 20px;
        border: 1px solid transparent;
        border-radius: 4px;
    }
</style>
<div class="form home_bg">
<?php $form=$this->beginWidget('CActiveForm', array(
	'id'=>'address-form',
	'enableClientValidation'=>true,
	'clientOptions'=>array(
		'validateOnSubmit'=>true,
	),
)); ?>
	
	<?php //echo CHtml::errorSummary($model); ?>

    <div class="warningContent">
        <?php
        if ($this->getChildInfo()['schoolid'] == 'BJ_SLT') {
            echo Yii::t("labels", 'If any of the information above is inaccurate, <NAME_EMAIL> to update them.');
        }
        else {
            echo Yii::t("labels", 'If any of the information above is inaccurate, <NAME_EMAIL> to update them.');
        }
        ?>
    </div>

	<dl class="row">
		<dt>
		<?php echo CHtml::activeLabelEx($model,'en_address'); ?>
		</dt>
		<dd>
		<?php echo CHtml::activeTextField($model,'en_address',array("size"=>64, "disabled" => "disabled")) ?>
		<?php echo CHtml::error($model,'en_address'); ?>
		</dd>
	</dl>
	
	
	<dl class="row">
		<dt>
		<?php echo CHtml::activeLabelEx($model,'en_district'); ?>
		</dt>
		<dd>
		<?php echo CHtml::activeTextField($model,'en_district',array("size"=>40, "disabled" => "disabled")) ?>
		<?php echo CHtml::error($model,'en_district'); ?>
		</dd>
	</dl>
	
	
	<dl class="row">
		<dt>
		<?php echo CHtml::activeLabelEx($model,'en_city'); ?>
		</dt>
		<dd>
		<?php echo CHtml::activeTextField($model,'en_city',array("size"=>40, "disabled" => "disabled")) ?>
		<?php echo CHtml::error($model,'en_city'); ?>
		</dd>
	</dl>
	
	
	<dl class="row">
		<dt>
		<?php echo CHtml::activeLabelEx($model,'en_postcode'); ?>
		</dt>
		<dd>
		<?php echo CHtml::activeTextField($model,'en_postcode',array("size"=>40, "disabled" => "disabled")) ?>
		<?php echo CHtml::error($model,'en_postcode'); ?>
		</dd>
	</dl>
	
	
	<dl class="row">
		<dt>
		<?php echo CHtml::activeLabelEx($model,'en_telephone'); ?>
		</dt>
		<dd>
		<?php echo CHtml::activeTextField($model,'en_telephone',array("size"=>40, "disabled" => "disabled")) ?>
		<?php echo CHtml::error($model,'en_telephone'); ?>
		</dd>
	</dl>
	
	
	<!-- <dl class="row submit">
		<dd>
		<?php if(Yii::app()->user->checkAccess('editProfile',array("childid"=>$this->getChildId()))){
			echo CHtml::submitButton(Yii::t("global", "Save"),array("class"=>"w100 bigger"));
		}else{
			echo CHtml::submitButton(Yii::t("global", "Save"),array("class"=>"w100 bigger","disabled"=>"disabled"));
		}
		
		?>
		</dd>
	</dl> -->
	
	
	
<?php $this->endWidget(); ?>
</div><!-- form -->