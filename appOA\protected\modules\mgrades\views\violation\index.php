<style>
	[v-cloak] {
		display: none;
	}
    .warn{
        padding:8px 12px;
        background: #FDF8F1;
        border-radius: 4px;
        align-items:start;
        font-size: 12px;
        color: #F0AD4E;
        line-height: 16px;
    }
    .mt2{
        margin-top:2px
    }
    .el-checkbox__input.is-checked+.el-checkbox__label{
        color:#333
    }
    .el-checkbox__input.is-checked .el-checkbox__inner, .el-checkbox__input.is-indeterminate .el-checkbox__inner{
        background-color: #428bca;
        border-color: #428bca;
    }
    .checkNumBlue{
        padding: 0 6px;
        background: #E2ECF8;
        border-radius: 2px;
        font-size: 12px;
        color: #4D88D2;
        line-height: 20px;
        display: inline-block;
        height: 20px;
        cursor: pointer;
    }
    .checkNumBlue:hover{
        background: #4D88D2;
        color: #fff;
    }
    .checkNumRed{
        padding: 0 6px;
        background: #F9E2E1;
        border-radius: 2px;
        font-size: 12px;
        color: #D9534F;
        line-height: 20px;
        display: inline-block;
        height: 20px;
        cursor: pointer;
    }
    .checkNumRed:hover{
        background: #D9534F;
        color: #fff;
    }
    .checkNumYellow{
        padding: 0 6px;
        background: #FCF0DE;
        border-radius: 2px;
        font-size: 12px;
        color: #F0AD4E;
        line-height: 20px;
        display: inline-block;
        height: 20px;
        cursor: pointer;
    }
    .checkNumYellow:hover{
        background: #F0AD4E;
        color: #fff;
    }
    .childInfo{
        padding:8px 16px;
        background: #FAFAFA;
        border-radius: 4px;
        align-items:center
    }
    .recordSelected{
        padding:20px;
        background: #F7F7F8;
        border-radius: 4px;
    }
    .colorBlue{
        color:#4D88D2
    }
    .checkChild{
        display: inline-block;
        margin:12px 24px 0 0
    }
    .width100{
        width: 100px;
    }
    .date{
        padding:12px 16px;
        background: #F7F7F8;
        border-radius: 4px;
        display: flex;
        align-items: center;
    }
    .el-input__inner:focus{
        border-color: #428bca;
        box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.075) inset, 0px 0px 8px rgba(64, 158, 255, 0.5)
    }
    .el-pagination.is-background .el-pager li:not(.disabled).active{
        background-color: #428bca;
    }
    .el-tabs__item:hover, .el-tabs__item.is-active,.filterId{
        color:#428bca !important;
    }
    .el-tabs__active-bar{
        background-color:#428bca;
    }
    .badgeDefault{
        background: #E5E6EB;
        color: #666666;
        margin-left:3px
    }
    .badgeActive{
        background: #F0F5FB;
        margin-left:3px;
        color: #428bca;
    }
    .arr{
        vertical-align: top !important;
    }
    .text_overflow {
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
    }
    .consequence_time{
        background: #E5E6EB;
        border-radius: 2px;
        font-size: 12px;
        color: #333333;
        line-height: 12px;
        padding:4px 6px;
        height: 20px;
        margin-left:4px;
        white-space: nowrap;
    }
    .classPopper{
        padding:12px 0
    }
    .filterClass{
        max-height:150px;
        overflow-y:auto;
    }
    .filterClass div{
        font-size:14px;
        color:#333;
        padding:7px 15px;
    }
    .filterClass div:hover{
        cursor: pointer;
        background: #F7F7F8;
    }
    .scroll-box::-webkit-scrollbar{
      background-color: #ccc;
      height:6px;
      width: 5px;
    } 
    .scroll-box::-webkit-scrollbar-thumb {
        /*滚动条里面小方块*/
        border-radius   : 10px;
        background-color: skyblue;
        background-color: #D8D8D8;
        background-image: none
    }
    .scroll-box::-webkit-scrollbar-track {
        /*滚动条里面轨道*/
        box-shadow   : inset 0 0 5px rgba(0, 0, 0, 0);
        background   : #fff;
        border-radius: 10px;
    }
    .department_name{
      padding:6px 12px;
      background: #F7F7F8;
      border-radius: 4px;
      margin-bottom:8px
    }
    .line{
        text-overflow: ellipsis;
        -webkit-line-clamp: 1;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
    .navCss > li > a{
        padding:7px 15px;
    }
    .batch-1 {
        background-color: #f0f9eb;
    }
    .batch-2 {
        background-color: #EBFAFF;
    }
    .head-1{
        background-color:#5CB85C ;
        color:#fff
    }
    .head-2 {
        background-color:#5BC0DE;
        color:#fff
    }
    .el-table--enable-row-hover .el-table__body tr:hover > td {
        background-color:inherit !important
    }
    .detailTable tr th{
        background:#fafafa;
        color:#333;
        border-bottom: 1px solid #dddddd !important;
        position: sticky; 
        top: 0;
        z-index: 10;
    }
    .detailTable tr td, .detailTable tr th{
        padding: 12px 10px !important;
        vertical-align: middle !important;
        font-size: 14px;
        line-height: 23px !important;
    }
    .detailTable > tbody + tbody {
        border-top: 1px solid #dddddd;
    }
    .comment{
        max-width:500px
    }
    .refresh:hover{
        color:#3D6CA8
    }
</style>
<div class="container-fluid"  id='container' v-cloak >
	<ol class="breadcrumb">
		<li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('//mcampus/default/index')) ?></li>
		<li class="active"><?php echo Yii::t('site','SS Violation Management');?></li>
	</ol>
	<div class="row">
        <div class="col-md-2 col-sm-2">
            <ul class="nav nav-pills nav-stacked text-left background-gray" id="pageCategory">
                <li :class='tabType=="20"?"active":""' ><a href="#tardys" @click='tabNav(20)'><?php echo Yii::t('violation','Student Tardys');?></a></li>
                <li :class='tabType=="10"?"active":""' ><a href="#uniform" @click='tabNav(10)'><?php echo Yii::t('violation','Uniform Violation');?></a></li>
            </ul>
        </div>
		<div class="col-md-10 col-sm-10" v-if='configData.year_list'>
            <div>
                <div v-if='configData.year_list' class='flex'>
                    <div class='flex1'>
                        <el-select v-model="year" size='small' @change='getList' placeholder="<?php echo Yii::t('violation','Please select');?>">
                            <el-option
                            v-for="item in configData.year_list"
                            :key="item.key"
                            :label="item.name"
                            :value="item.key">
                            </el-option>
                        </el-select>
                        <el-select v-model="semester" class='ml16'  @change='getList' size='small' placeholder="<?php echo Yii::t('violation','Please select');?>">
                            <el-option
                            v-for="item in configData.semester_list"
                            :key="item.value"
                            :label="item.name"
                            :value="item.value">
                            </el-option>
                        </el-select>
                    </div>
                    <el-input
                        v-if='activeName=="All"'
                        size='small'
                        class='length_3'
                        clearable
                        placeholder="<?php echo Yii::t('global','Search');?>"
                        prefix-icon="el-icon-search"
                        v-model="childName">
                    </el-input>
                </div>
                <div class='mt16'>
                    <el-tabs v-model="activeName" @tab-click="getList">
                        <el-tab-pane label="Consequence Pending" name="Pending">
                            <span slot="label"><?php echo Yii::t('violation','Consequence Pending');?> <span class="badge" :class='activeName=="Pending"?"badgeActive":"badgeDefault"'>{{pending_total}}</span></span>
                        </el-tab-pane>
                        <el-tab-pane label="All" name="All">
                        <span slot="label"><?php echo Yii::t('violation','All');?> <span class="badge" :class='activeName=="All"?"badgeActive":"badgeDefault"'>{{all_total}}</span></span>
                        </el-tab-pane>
                    </el-tabs>
                    <div>
                        <div class='flex warn'>
                            <span class='el-icon-info mt2'></span>
                            <div class='flex1 ml8'>
                               <div v-if='tabType=="20"'>
                                    <?php echo Yii::t('violation','Tardy times exclude Homeroom period.');?>
                                    <div>
                                    <?php echo Yii::t('violation','When the unprocessed tardy numbers accumulate to a multiple of 5, they need to be handled.');?>
                                    </div>
                               </div> 
                               <div v-if='tabType=="10"'>
                                    <?php echo Yii::t('violation','When the unprocessed violation numbers accumulate to a multiple of 5, they need to be handled.');?>
                               </div>
                            </div>
                        </div>
                        <div class='mt16'>
                            <span class="colorBlue cur-p font14 refresh" :disabled='refresh' @click='refreshCache()'><span class='el-icon-refresh font16'></span> <?php echo Yii::t('violation','Refresh');?></span>
                        </div>
                        <div v-if='activeName=="Pending"' :style="'height:'+(height-392)+'px'" v-loading="Listloading">
                            <div class='mt16 mb24 flex align-items'>
                                <div class='flex1'>
                                <ul class="nav nav-pills navCss">
                                    <li role="presentation" class='mr16' :class="totalType==1?'active':'' "><a href="javascript:;" @click='tabTotal(1)'>≥ 5 <span class="badge" :class='totalType=="1"?"badgeActive":"badgeDefault"'>{{totalGte5Num.length}}</span></a></li>
                                    <li role="presentation" :class="totalType==2?'active':'' "><a href="javascript:;" @click='tabTotal(2)'> < 5 <span class="badge" :class='totalType=="2"?"badgeActive":"badgeDefault"'>{{totalLt5Num.length}}</span></a></a></li>
                                </ul>
                                </div>
                                <span  v-if='totalType=="1"' class='mr24 color6 font14'>{{translate("<?php echo Yii::t('violation', '%s Selected'); ?>", selectchildNum.length)}}</span>
                                <button  v-if='totalType=="1"' type="button" class="btn btn-primary" @click='record()'><?php echo Yii::t('violation','Record Consequence');?></button>
                            </div>
                            <div class='scroll-box' v-if='totalList.length'  :style="'max-height:'+(tabType=='10'?height-412:height-430)+'px;overflow-x: hidden;overflow-x:hidden'" >
                                <div :class='index!=0?"mt24":"" ' v-for='(list,index) in totalList'>
                                    <el-checkbox v-if='totalType=="1"' class='color3' :indeterminate="list.isIndeterminate" v-model="list.checkAll" @change="handleCheckAllChange(index)">{{list.class_name}}</el-checkbox>
                                    <div v-if='totalType=="2"' class='font14 color3'>{{list.class_name}}</div>
                                    <div class='row mt16 ml12'>
                                        <el-checkbox-group v-model="list.checkchild" @change="handleCheckedChange(index)">
                                            <div class='col-lg-3 col-md-4 col-sm-4'  v-for="(child,idx) in list.child">
                                            <div class='flex childInfo mb16'>
                                                <div class='flex1'>
                                                    <el-checkbox  :label="child.child_id" :key="child.child_id" v-if='totalType=="1"'>
                                                    <div class="flex align-items">
                                                        <div>
                                                            <img :src="childInfo[child.child_id].avatar" width="32" class="img-circle">
                                                        </div> 
                                                        <div class="flex1 ml8 mr8">
                                                            <div class="font14 color3">{{childInfo[child.child_id].name}}</div> 
                                                            <div class="font12 color6">{{childInfo[child.child_id].className}}</div>
                                                        </div>
                                                    </div>
                                                    </el-checkbox>
                                                    <div class="flex align-items" v-if='totalType=="2"'>
                                                        <div>
                                                            <img :src="childInfo[child.child_id].avatar" width="32" class="img-circle">
                                                        </div> 
                                                        <div class="flex1 ml8 mr8">
                                                            <div class="font14 color3">{{childInfo[child.child_id].name}}</div> 
                                                            <div class="font12 color6">{{childInfo[child.child_id].className}}</div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div :class='child.number>10?"checkNumRed":child.number<=5?"checkNumBlue":"checkNumYellow"' @click='showTotal(child,"pending")'>{{child.number}}</div>
                                            </div>
                                            </div>
                                        </el-checkbox-group>
                                    </div>
                                </div>
                            </div>
                            <el-empty description="<?php echo Yii::t('ptc', 'No Data') ?>" v-else-if='!Listloading'></el-empty>
                        </div>
                        <div v-if='activeName=="All"'>
                            <el-table
                                v-loading="tableloading"
                                class='mt16'
                                :header-cell-style="{background:'#fafafa',color:'#333'}"
                                :data="allData.child_list"
                                :max-height="tabType=='10'?height-423:height-440"
                                style="width: 100%">
                                <el-table-column
                                    prop="date"
                                    min-width="15%"
                                    label="<?php echo Yii::t('labels','Student');?>"
                                    >
                                    <template slot-scope="scope">
                                        <img :src="scope.row.child_avatar" width="32" class="img-circle">
                                        <span class='color3 font14 ml5'>{{scope.row.child_name}}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column
                                    prop="class_name"
                                    min-width="15%"
                                    label="<?php echo Yii::t('labels','Class');?>"
                                    >
                                    <template slot="header" slot-scope="scope">
                                       <el-popover
                                            placement="bottom"
                                            v-model="visible"
                                            width="200"
                                            popper-class='classPopper'
                                            trigger="click">
                                            <div class='filterClass scroll-box'>
                                                <div @click='filterClass("0")' :class='class_id==0?"filterId":""'><?php echo Yii::t('violation','All');?></div>
                                                <div v-for='(list,index) in allData.class_list' @click='filterClass(list.classid)' :class='class_id==list.classid?"filterId":""'>{{list.title}}</div>
                                            </div>
                                            <span slot="reference" :class='class_id==0?"":"colorBlue"'><?php echo Yii::t('labels','Class');?> <span class='el-icon-arrow-down'></span></span>
                                        </el-popover>
                                    </template>
                                </el-table-column>
                                <el-table-column
                                    prop="total"
                                    min-width="10%"
                                    label="<?php echo Yii::t('violation','Record');?>">
                                    <template slot-scope="scope">
                                        <div v-if='scope.row.consequence.length'>
                                            <div class='flex  align-items' :class='index!=0?"mt12":""' v-for='(item,index) in scope.row.consequence'>
                                            <span :class='item.record>10?"checkNumRed":item.record<=5?"checkNumBlue":"checkNumYellow"' @click='showTotal(scope.row,"all",index)' >{{item.record}}</span>
                                            </div>
                                        </div>
                                        <div v-else>
                                            <span :class='scope.row.record>10?"checkNumRed":scope.row.record<=5?"checkNumBlue":"checkNumYellow"' @click='showTotal(scope.row,"pending")' >{{scope.row.total}}</span>
                                        </div>
                                    </template>
                                </el-table-column>
                                <el-table-column
                                    min-width="30%"
                                    label="<?php echo Yii::t('violation','Consequence');?>">
                                    <template slot-scope="scope">
                                        <div class='flex  align-items' :class='index!=0?"mt12":""' v-for='(item,index) in scope.row.consequence'>
                                            <span class='text_overflow'>{{showConsequence(configData.consequence[tabType],item.consequence)}}</span>
                                            <span class="consequence_time" v-if='item.consequence_time!=""'>{{item.consequence_time}}</span>
                                            <el-tooltip class="item" effect="dark" v-if='item.consequence_comment!=null && item.consequence_comment!=""' :content="item.consequence_comment" placement="top" popper-class='comment'	>
                                                <span class='el-icon-chat-line-square font16 ml10 color3'></span>
                                            </el-tooltip>
                                        </div>
                                    </template>
                                </el-table-column>
                                <el-table-column
                                    min-width="15%"
                                    label="<?php echo Yii::t('violation','By');?>">
                                    <template slot-scope="scope">
                                        <div :class='index!=0?"mt12":""' v-for='(item,index) in scope.row.consequence'>{{allData.staff_info[item.created_by].name}} </div>
                                    </template>
                                </el-table-column>
                                <el-table-column
                                    min-width="15%"
                                    label="<?php echo Yii::t('violation','Time');?>">
                                    <template slot-scope="scope">
                                        <div :class='index!=0?"mt12":""' v-for='(item,index) in scope.row.consequence'>{{item.created_at}} </div>
                                    </template>
                                </el-table-column>
                            </el-table>
                            <div class="block flex  align-items" style="margin-top:15px;" v-if='allData.child_list'>
                                <el-pagination  :hide-on-single-page="true" align='right' background class='flex1'  @prev-click="handlePrevChange" @next-click="handleNextChange" @current-change="handleCurrentChange" 
                                :current-page="currentPage" 
                                :page-sizes="[20]" 
                                :page-size="pageSize" 
                                layout="prev, pager, next" 
                                :total="allData.total">
                                </el-pagination>
                                <!-- <span>共{{allData.total}}页</span> -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
		</div>
	</div>
    <div class="modal fade" id="recordModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel" >
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content" >
                <div class="modal-header"  >
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel">{{tabType=='10'?'<?php echo Yii::t('violation','Uniform Violation');?>':'<?php echo Yii::t('violation','Student Tardys');?>'}}</h4>
                </div>
                <div class="modal-body p24">
                    <div class='recordSelected'>
                        <div class='flex mb8'>
                            <div class='font14 color3 flex1 fontBold'>{{translate("<?php echo Yii::t('violation', '%s Selected'); ?>", selectchildNum.length)}}</div>
                            <div class='colorBlue font14 cur-p' v-if='selectchildNum.length>8 && !showAll' @click='showAll=true'><span><?php echo Yii::t('newDS','Expand');?></span><span class='el-icon-arrow-down'></span></div>
                            <div class='colorBlue font14 cur-p' v-if='selectchildNum.length>8 && showAll' @click='showAll=false'><span><?php echo Yii::t('newDS','Collapse');?></span><span class='el-icon-arrow-up'></span></div>
                        </div>
                        <div :class='selectchildNum.length>8?"line":""'  :style="{'-webkit-line-clamp': !showAll ? '1' : 'none'}">
                            <span v-for='(list,index) in selectchildNum' class='checkChild'>
                                <span class='font14 color3 mr10'>{{childInfo[list.child_id].name}}</span> <span :class='list.number>10?"checkNumRed":list.number<=5?"checkNumBlue":"checkNumYellow"'>{{list.number}}</span>
                            </span>
                        </div>
                    </div>
                    <div class='flex mt24'>
                        <div class='font14 color6 width100'><?php echo Yii::t('violation','Consequence');?></div>
                        <div class='flex1'>
                            <div v-if='configData.consequence'>
                                <label class="radio-inline mr16" v-for='(list,i) in configData.consequence[tabType]'>
                                    <input type="radio" :value='list.value' v-model='consequence'>{{list.name}}
                                </label>
                            </div>
                            <div class='date mt12' v-if='consequence!=""'>
                                <span class='font14 color6'><?php echo Yii::t('violation','Date');?>：</span>
                                <v-date-picker name="calendar"   :masks="masks"  v-model='consequence_time' ref='calendar'  >
                                    <template v-slot="{ inputValue, togglePopover,hidePopover  }">
                                        <input
                                            class="form-control length_3"
                                            :value="inputValue"
                                            @click="togglePopover"
                                            placeholder="<?php echo Yii::t('violation','Please select');?>"
                                        />
                                    </template>
                                </v-date-picker>
                                <span class='colorBlue font14 mr16 ml20 cur-p' @click='consequenceTime(lastMonday)'><?php echo Yii::t('violation','Last Monday');?></span>
                                <span class='colorBlue font14 mr16  cur-p' @click='consequenceTime(thisMonday)'><?php echo Yii::t('violation','This Monday');?></span>
                                <span class='colorBlue font14  cur-p' @click='consequenceTime(nextMonday)'><?php echo Yii::t('violation','Next Monday');?></span>
                            </div>
                        </div>
                    </div>
                    <div class='flex mt24'>
                        <div class='font14 color6 width100'><?php echo Yii::t('violation','Comment');?></div>
                        <div class='flex1'>
                            <textarea class="form-control" rows="3" v-model="consequence_comment"></textarea> 
                        </div>
                    </div>
                    <div class='flex mt24 align-items'>
                        <div class='font14 color6 width100'><?php echo Yii::t('violation','By');?></div>
                        <div class='flex1 flex align-items' v-if='configData.staff'>
                            <img :src="configData.staff.photoUrl" width="28" class="img-circle">
                            <span class='color3 font14 ml5'>{{configData.staff.name}}</span>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                    <button type="button" class="btn btn-primary" :disabled='btnDisanled' @click='saveChild()'><?php echo Yii::t("message", "OK");?></button>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="detailModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel" >
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header"  >
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("newDS", "Detail"); ?></h4>
                </div>
                <div class="modal-body p24" v-if='detailData.childData'>
                    <div class="flex align-items">
                        <div>
                            <img :src="detailData.childData.avatar" width="42" class="img-circle">
                        </div> 
                        <div class="flex1 ml8">
                            <div class="font14 color3">{{detailData.childData.name}}</div> 
                            <div class="font12 color6">{{detailData.childData.className}}</div>
                        </div>
                    </div>
                    <div class='mt24'>
                        <ul class="nav nav-pills">
                            <li role="presentation" :class='detailType=="uniform"?"active":""'><a href="javascript:;" @click='detailTypeTab("uniform")'><?php echo Yii::t('violation','Uniform Violation');?> <span class="badge" :class='detailType=="tardiness"?"badgeDefault":""'>{{detailData.uniformTotal}}</span></a></li>
                            <li role="presentation" :class='detailType=="tardiness"?"active":""'><a href="javascript:;" @click='detailTypeTab("tardiness")'><?php echo Yii::t('violation','Student Tardys');?> <span class="badge" :class='detailType=="uniform"?"badgeDefault":""'>{{detailData.tardinessTotal}}</span></a></li>
                        </ul>
                    </div>
                    <table class='table detailTable mt16' ref="table" :style="'max-height:'+(height-370)+'px;overflow-y: auto;display:block'">
                        <thead >
                            <tr>
                                <th width='80'><?php echo Yii::t('violation','Number');?></th>
                                <th :width="detailType=='tardiness'?150:180"><?php echo Yii::t('violation','Date');?></th>
                                <th width="80" v-if='detailType=="tardiness"'><?php echo Yii::t('violation','Period');?></th>
                                <th width="290"><?php echo Yii::t('violation','Course');?></th>
                                <th width="250"><?php echo Yii::t('violation','By');?></th>
                                <th width="250"><?php echo Yii::t('violation','Time');?></th>
                            </tr>
                        </thead>
                        <template v-if='detailDataTable.length'>
                            <tbody v-for='(list,index) in detailDataTable' :class='tableRowClassName(list,index)'>
                                <tr v-if='detailData.violationConsequence[list[0].consequence_id]'  :class='tableRowClassName(list,index,"head")'>
                                    <td><span class='glyphicon glyphicon-arrow-right'></span></td>
                                    <td>{{detailData.violationConsequence[list[0].consequence_id].consequence_date}}</td>
                                    <td v-if='detailType=="tardiness"'></td>
                                    <td>{{showConsequence(configData.consequence[detailType=="uniform"?10:20],detailData.violationConsequence[list[0].consequence_id].consequence)}}</td>
                                    <td>{{detailData.teacherData[detailData.violationConsequence[list[0].consequence_id].created_by].name}}</td>
                                    <td>{{detailData.violationConsequence[list[0].consequence_id].created_time}}</td>
                                </tr>
                                <tr v-for='(item,i) in list'>
                                    <td>{{getRowIndex(index, i)}}</td>
                                    <td>{{item.target_date}}</td>
                                    <td v-if='detailType=="tardiness"'>{{item.period}}</td>
                                    <td>{{item.course_code}}</td>
                                    <td>{{detailData.teacherData[item.teacher_id].name}}</td>
                                    <td>{{item.created_time}}</td>
                                </tr>
                            </tbody>
                        </template>
                        <tbody v-else>
                            <tr>
                                <td colspan='5' class='text-center font14 color6'>暂无数据</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                </div>
            </div>
        </div>
    </div>
</div>
<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>
<script>
var height=document.documentElement.clientHeight;
var container = new Vue({
        el: "#container",
        data:{
            height:height,
            activeName:'Pending',
            isIndeterminate: true,
            checkAll: false,
            date:'',
            listData:{},
            pending_total:'',
            all_total:'',
            tabType:'20',
            configData:{},
            semester:'',
            year:'',
            totalGte5:[],
            totalGte5Num:[],
            totalLt5:[],
            totalLt5Num:[],
            totalType:'1',
            totalList:[],
            detailData:{},
            detailType:'uniform',
            detailDataTable:[],
            allData:{},
            class_id:0,
            visible: false,
            currentPage: 1,
            total:0, 
            pageSize:20,
            tableloading:false,
            selectchildNum:[],
            consequence:'',
            consequence_time:'',
            consequence_comment:'',
            masks: {
                input: 'YYYY-MM-DD',
            },
            thisMonday:'',
            lastMonday:'',
            nextMonday:'',
            showAll:false,
            childName:'',
            debounceTimeout: null,
            btnDisanled:false,
            classData:{},
            Listloading:false,
            refresh:false
        },
        watch: {
            "childName": function() {
                if (this.debounceTimeout) clearTimeout(this.debounceTimeout);
                this.debounceTimeout = setTimeout(() => {
                    this.allList('init')   
                },1000);
            }
        },
        created: function() {
            let that=this
            var hash = window.location.hash;
            if (hash) {
                this.tabType=hash.substr(1,hash.length)=='uniform'?'10':'20'
            }
            $.ajax({
                url: '<?php echo $this->createUrl("config") ?>',
                type: "post",
                dataType: 'json',
                data: {
                },
                success: function(data) {
                    if (data.state == 'success') {
                        that.configData=data.data
                        that.semester=data.data.semester
                        that.year=data.data.year
                        that.getList()
                    }else{
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    }
                },
                error: function(data) {
                    resultTip({
                        error: 'warning',
                        msg: data.message
                    });
                },
            })
        },
        methods: {
            refreshCache(){
                let that=this
                this.refresh=true
                $.ajax({
                    url: '<?php echo $this->createUrl("setCache") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        type:this.tabType,//10 着装 20 迟到
                        year:this.year,//学年
                        semester:this.semester, 
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            // resultTip({
                            //     msg: data.message
                            // });
                            that.getList()
                            that.refresh=false
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            that.refresh=false
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                        that.refresh=false
                    },
                })
            },
            getRowIndex(groupIndex, itemIndex) {
                let totalCount = 0;
                for (let i = 0; i < this.detailDataTable.length; i++) {
                    if (i < groupIndex) {
                    totalCount += this.detailDataTable[i].length;
                    } else if (i === groupIndex) {
                    totalCount += itemIndex + 1;
                    }
                }
                let totalItems = this.getTotalItems();
                return totalItems - totalCount + 1;
            },
            getTotalItems() {
                return this.detailDataTable.reduce((total, list) => total + list.length, 0);
            },
            translate(text, ...args) {
                return text.replace(/%s/g, () => args.shift());
            },
            tabNav(type){
                this.tabType=type
                this.getList()
            },
            showConsequence(list,id){
                let items=list.find(item => item.value == id)
                return items?items.name:''
            },
            getList(){
                this.selectchildNum=[]
                let that=this
                if(this.activeName=='Pending'){
                    this.Listloading=true
                    $.ajax({
                        url: '<?php echo $this->createUrl("list") ?>',
                        type: "post",
                        dataType: 'json',
                        data: {
                            type:this.tabType,//10 着装 20 迟到
                            year:this.year,//学年
                            semester:this.semester, //10上学期 20下学期
                        },
                        success: function(data) {
                            if (data.state == 'success') {
                                that.listData=data.data
                                that.childInfo=data.data.child_info
                                that.childrenData =data.data.child_list
                                that.classData=data.data.class_info
                                that.pending_total=data.data.pending_total
                                that.all_total=data.data.all_total
                                const classMap = {};
                                that.classData.forEach(classItem => {
                                    classMap[classItem.class_id] = classItem;
                                });
                                that.totalGte5 = [];
                                that.totalGte5Num = [];
                                that.totalLt5 = [];
                                that.totalLt5Num = [];
                                that.childrenData.forEach(child => {
                                    const classInfo = classMap[child.class_id];
                                    if (classInfo) {
                                        const item = { ...classInfo,checkAll:false, isIndeterminate:false,checkchild:[], child: [{ ...child }] };
                                        if (child.number >= 5) {
                                            that.totalGte5Num.push(child);
                                            const existingEntry = that.totalGte5.find(entry => entry.class_id === child.class_id);
                                            if (existingEntry) {
                                                existingEntry.child.push(child);
                                            } else {
                                                that.totalGte5.push(item);
                                            }
                                        } else {
                                            that.totalLt5Num.push(child);
                                            const existingEntry = that.totalLt5.find(entry => entry.class_id === child.class_id);
                                            if (existingEntry) {
                                                existingEntry.child.push(child);
                                            } else {
                                                that.totalLt5.push(item);
                                            }
                                        }
                                    }
                                });
                                if(that.totalType==1){
                                    that.totalList=that.sortClasses(that.totalGte5,'class_name')
                                }else{
                                    that.totalList=that.sortClasses(that.totalLt5,'class_name')
                                }
                                that.Listloading=false
                            }else{
                                resultTip({
                                    error: 'warning',
                                    msg: data.message
                                });
                                that.Listloading=false
                            }
                        },
                        error: function(data) {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            that.Listloading=false
                        },
                    })
                }else{
                    this.allList('init')   
                }
            },
            sortClasses(data,type) {
                return data.sort((a, b) => {
                    const gradeA = parseInt(a[type].match(/Grade (\d+)/)[1]);
                    const gradeB = parseInt(b[type].match(/Grade (\d+)/)[1]);
                    const letterA = a[type].split(' ').pop().charAt(0);
                    const letterB = b[type].split(' ').pop().charAt(0);
                    if (gradeA !== gradeB) {
                        return gradeA - gradeB;
                    }
                    return letterA.localeCompare(letterB);
                });
            },
            allList(type){
                let that=this
                this.tableloading=true
                $.ajax({
                    url: '<?php echo $this->createUrl("allList") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        type:this.tabType,//10 着装 20 迟到
                        year:this.year,//学年
                        semester:this.semester, //10上学期 20下学期
                        class_id:this.class_id,//选中班级的classid
                        page:type=='init'?1:this.currentPage,
                        search_name:this.childName
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.allData=data.data
                            that.classData=that.sortClasses(data.data.class_list,'title')
                            that.pending_total=data.data.pending_total
                            that.all_total=data.data.all_total
                            that.total=data.data.child_list.length
                            if(type=='init'){
                                that.currentPage=1
                            }
                            that.tableloading=false
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            that.tableloading=false
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                        that.tableloading=false
                    },
                })
            },
            filterClass(id){
                this.class_id=id
                this.visible = false
                this.allList('init')  
            },
            handlePrevChange(val) {
                this.currentPage = val;
            },
            handleNextChange(val){
                this.currentPage = val;
            },
            handleCurrentChange(val) {
                this.currentPage = val;
                this.allList()
            },
            
            tabTotal(type){
                this.totalType=type
                if(this.totalType==1){
                    this.totalList=this.sortClasses(this.totalGte5,'class_name')
                }else{
                    this.totalList=this.sortClasses(this.totalLt5,'class_name')
                }
                this.selectchildNum=[]
                this.totalList.forEach(item => {
                    item.checkAll=false
                    item.isIndeterminate=false
                    item.checkchild=[]
                });
            },
            handleCheckAllChange(index) {
                var child=[]
                this.totalList[index].child.forEach(item => {
                    child.push(item.child_id)
                });
                this.totalList[index].checkchild = this.totalList[index].checkAll ? child : [];
                this.totalList[index].isIndeterminate = false;
                this.selectchild()
            },
            handleCheckedChange(index) {
                let checkedCount = this.totalList[index].checkchild.length;
                this.totalList[index].checkAll = checkedCount === this.totalList[index].child.length;
                this.totalList[index].isIndeterminate = checkedCount > 0 && checkedCount < this.totalList[index].child.length;
                this.selectchild()
            },
            selectchild(){
                this.selectchildNum=[]
                this.totalList.forEach(item => {
                    item.child.forEach(child => {
                        if (item.checkchild.includes(child.child_id)) {
                            this.selectchildNum.push(child);
                        }
                    });
                });
            },
            record(){
                if(this.selectchildNum.length==0){
                    resultTip({
                        error: 'warning',
                        msg:'请选择要标记的学生'
                    });
                    return
                }
                this.updateMondays()
                this.consequence=''
                this.consequence_comment=''
                $('#recordModal').modal('show') 

            },
            getMonday(d) {
                // 获取给定日期所在周的周一的日期
                let day = d.getDay() || 7; 
                let diffToMonday = d.getDate() - day + (day === 7 ? -6 : 1); 
                return new Date(d.setDate(diffToMonday)); 
            },
            updateMondays() {
                let today = new Date();
                this.thisMonday = this.getMonday(new Date(today));
                
                let lastMondayDate = new Date(today);
                lastMondayDate.setDate(lastMondayDate.getDate() - 7);
                this.lastMonday = this.getMonday(lastMondayDate);

                let nextMondayDate = new Date(today);
                nextMondayDate.setDate(nextMondayDate.getDate() + 7);
                this.nextMonday = this.getMonday(nextMondayDate);

                this.consequence_time=this.nextMonday
            },
            formatYmd(date) {
                let y = date.getFullYear();
                let m = String(date.getMonth() + 1).padStart(2, '0'); 
                let d = String(date.getDate()).padStart(2, '0');
                return `${y}-${m}-${d}`;
            },
            consequenceTime(date){
                this.consequence_time=date
            },
            saveChild(){
                let that=this
                if(this.consequence==''){
                    resultTip({
                        error: 'warning',
                        msg: '请选择处理结果'
                    });
                    return
                }
                this.btnDisanled=true
                $.ajax({
                    url: '<?php echo $this->createUrl("save") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        violation_type:this.tabType,//10 着装 20 迟到
                        year:this.year,//学年
                        semester:this.semester, //10上学期 20下学期
                        child_list:this.selectchildNum,
                        consequence:this.consequence,
                        consequence_time:this.formatYmd(this.consequence_time),
                        consequence_comment:this.consequence_comment
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            $('#recordModal').modal('hide') 
                            resultTip({
                                msg: data.message
                            });
                            that.getList()
                            that.btnDisanled=false
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            that.btnDisanled=false
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                        that.btnDisanled=false
                    },
                })
            },
            showTotal(list,type,index){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("detail") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        year:this.year,
                        semester:this.semester,
                        child_id:list.child_id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.detailData=data.data
                            that.detailDataTable=data.data.uniform
                            that.detailType=that.tabType=='20'?"tardiness":"uniform"
                            that.detailDataTable=that.detailData[that.detailType]
                            $('#detailModal').modal('show') 
                            if(type=='all'){
                                that.scrollToRow('batchId'+list.consequence[index].consequence_id)
                            }
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            detailTypeTab(type){
                this.detailType=type
                this.detailDataTable=this.detailData[type]
            },
            scrollToRow(className) {
                this.$nextTick(() => {
                    setTimeout(() => {
                        const tableWrapper = this.$refs.table;
                        const targetRow = Array.from(tableWrapper.querySelectorAll('tbody')).find(row => row.classList.contains(className));
                        if (targetRow) {
                            const targetRect = targetRow.getBoundingClientRect();
                            const tableRect = tableWrapper.getBoundingClientRect();
                            const scrollTop = tableWrapper.scrollTop + (targetRect.top - tableRect.top) -50;
                            tableWrapper.scrollTo({
                                top: scrollTop,
                                behavior: 'smooth'
                            });
                        } 
                    }, 500);
                });
            },
            tableRowClassName(data,index,type) {
                if(data[0].consequence_id!=0){
                    let id =index % 2 === 0 ? '1' : '2';
                    return type?`head-${id}`:`batch-${id} batchId${data[0].consequence_id}`;
                }
            }
        }
    })
</script>