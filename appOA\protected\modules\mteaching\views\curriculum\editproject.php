<div class="container-fluid">
    <!-- 面包屑导航 -->
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site','Teaching Tasks'), array('//mteaching/default/index'))?></li>
        <li><?php echo CHtml::link(Yii::t('site','Curriculum'), array('//mteaching/curriculum/project'))?></li>
        <li class="active"><?php echo Yii::t('curriculum',$project->cn_title).'  '.Yii::t('curriculum',$project->en_title);?></li>
    </ol>
    <div class="row">
        <!-- 左侧菜单栏 -->
        <div class="col-md-2">
            <div class="list-group" id="classroom-status-list">
                <?php foreach ($this->leftMenu as $key => $value) { ?>
                <a href="<?php echo $this->createUrl($value[0]);?>" class="list-group-item status-filter <?php echo $this->getAction()->getId()==$value[0]?'active':''; ?>"><?php echo $value[1]; ?></a></li>
                <?php } ?>
            </div>
        </div>
        <div class="col-md-10">
            <div class="panel panel-default">
            <div class="panel-body">
                <?php
                $form=$this->beginWidget('CActiveForm', array(
                    'id'=>'catering-form',
                    'htmlOptions'=>array('class'=>'form-horizontal J_ajaxForm','enctype'=>'multipart/form-data'),
                ));
                ?>
                <!-- 项目名称 -->
                <div class="form-group">
                    <div class="col-sm-5">
                        <?php echo $form->labelEx($project, 'cn_title', array('class'=>'')); ?>
                        <?php echo $form->textField($project, 'cn_title', array('class'=>'form-control')); ?>
                    </div>
             
                    <div class="col-sm-5">
                        <?php echo $form->labelEx($project, 'en_title', array('class'=>'')); ?>
                        <?php echo $form->textField($project, 'en_title', array('class'=>'form-control')); ?>
                    </div>
                </div>
                <!-- 适合季节 -->
                <div class="form-group">
                    <?php echo $form->labelEx($project, 'season', array('class'=>'col-sm-2 ')); ?>
                    <div class="col-sm-10">
                        <?php echo $form->dropDownList($project, 'season',$season, array('class'=>'form-control select_4', )); ?>
                    </div>
                </div>
                <!-- 适合年龄 -->
                <div class="form-group">
                    <?php echo $form->labelEx($project, 'age', array('class'=>'col-sm-2 ')); ?><br>
                    <div class="col-sm-10">
                        <?php echo $form->checkBoxList($project, 'age', $age, array('class'=>'', 'empty'=>Yii::t('global', 'Please Select'), 'onchange'=>'cCity(this)')); ?>
                    </div>
                </div>
                <!-- 状态 -->
                <div class="form-group">
                    <input type="hidden" name="state" value="10">
                </div>
                <!-- 是否为教育部门 -->
                <?php 
                    if (Yii::app()->user->checkAccess('o_E_Edu_Common')) {?>
                    <div class="form-group">
                        <div class="col-sm-10">
                            <?php echo $form->labelEx($project, 'official', array('class'=>'')); ?>
                            <?php echo $form->checkBox($project, 'official', array('class'=>'','selected'=>'selected')); ?>
                        </div>
                    </div>
                <?php } ?>
                <!-- 项目介绍 -->
                <div class="form-group">
                    <div class="col-sm-5">
                        <?php echo $form->labelEx($project, 'cn_desc', array('class'=>'')); ?>
                        <?php echo $form->textArea($project, 'cn_desc', array('class'=>'form-control', 'rows'=>8)); ?>
                    </div>
                    <div class="col-sm-5">
                        <?php echo $form->labelEx($project, 'en_desc', array('class'=>'')); ?>
                        <?php echo $form->textArea($project, 'en_desc', array('class'=>'form-control', 'rows'=>8)); ?>
                    </div>
                </div>
                <!-- 项目备注 -->
                <div class="form-group">
                    <div class="col-sm-5">
                    <?php echo $form->labelEx($project, 'cn_memo', array('class'=>'')); ?>
                    <?php echo $form->textArea($project, 'cn_memo', array('class'=>'form-control', 'rows'=>8)); ?>
                    </div>
                    <div class="col-sm-5">
                    <?php echo $form->labelEx($project, 'en_memo', array('class'=>'')); ?>
                    <?php echo $form->textArea($project, 'en_memo', array('class'=>'form-control', 'rows'=>8)); ?>
                    </div>
                </div>
                <!-- 附件 -->
                <div class="form-group">
                    <div class="col-sm-10">
                        <?php echo $form->labelEx($project, 'attachments', array(' ')); ?>
                        <div>
                            <?php foreach ($project->attachments as $key => $value) {
                                $fileUrl = Yii::app()->params['OAUploadBaseUrl'].'/curriculum/'.$key;
                            ?>
                            <label class='btn btn-success btn-xs'> <a style='color:#fff' target='_blank' href='<?php echo $fileUrl; ?> ' ><?php echo $value; ?></a> <span class='glyphicon glyphicon-remove' aria-hidden='true' onclick="removeatta(this,'<?php echo $key; ?>')"></span></label>
                            <?php
                            } ?>
                        </div>
                        <hr>
                        <input name="upload_file" type="file" ><br>
                        <p><?php echo Yii::t('curriculum','Allowed extensions: rar ,pdf'); ?></p>
                        <h4 class="" id="info"></h4>
                    </div>
                </div>  
                <hr>
                <!-- 添加标签 -->
                <h4><?php echo Yii::t('curriculum','Tags'); ?>：</h4>
                <div class="form-group">
                    <div class="col-sm-3"><input type="text" class="form-control" id="tagval"></div>
                    <div class="col-sm-2"><button class="btn btn-success btn-xs" onclick="addtag(this)" type="button">添加</button></div>
                    <div class="col-sm-5" id="tag">
                        <?php foreach ($tag as $key => $value) {
                            echo "<label class='label label-success' onclick='deltag(this)' title='单击删除'>{$value->tag->tag_term}<input type='hidden' name='tag[]' value='{$value->tag->tag_term}'></label>  ";
                        } ?>
                    </div>
                </div>
                <hr>
                <!-- 排序字段 -->
                <h4><?php echo Yii::t('curriculum','排序'); ?>：</h4>
                <div class="form-group">
                    <div class="col-sm-1">
                        <input type="text" class="form-control" name="CurProjects[weight]" id="CurProjects_weight" value="<?php echo $project->weight ?>">
                    </div>
                </div>
                <hr>
                <!-- 提交按钮 -->
                <div class="form-group">
                    <div class="col-sm-offset-5 col-sm-7">
                        <button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('curriculum', 'Save')?></button>
                    </div>
                </div>
                <?php $this->endWidget(); ?>
            </div>
          </div>
        </div>
    </div>
</div>

<script>
    //添加标签
    function addtag (btntag) {
        if($("#tagval").val() != ''){
            var tag = "<label class='label label-success' onclick='deltag(this)' title='单击删除'>"+$("#tagval").val()+"<input type='hidden' name='tag[]' value='"+$("#tagval").val()+"'></label>  ";
            $("#tag").append(tag);
        }
    }
    //删除标签
    function deltag (tag) {
        $(tag).remove();
    }
    //删除附件
    function removeatta (span,id) {
        if (confirm('确定删除？')) {
            var pid = <?php echo $project->pid?$project->pid:0; ?>;
            var atta = '<?php echo serialize($project->attachments);?>';
            console.log(atta);
            $.ajax({
                url:'<?php echo $this->createUrl('deleteProAttachments'); ?>',
                data:{atta : atta,id : id,pid : pid},
                type:'post',
                dataType:'json',
                success : function (data) {
                    if (data.msg == 'success') {
                        $(span).parent().remove();
                    }
                },
                error : function (data) {
                    // body...
                }
            })
        };
    }
</script>