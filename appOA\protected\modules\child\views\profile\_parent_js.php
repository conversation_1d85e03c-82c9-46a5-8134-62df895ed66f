<style>
    [v-cloak] {
        display: none !important;
    }
    .m10{
        margin: 10px !important;
    }
    .mr10{
        margin-right:10px !important;
    }
    .mb5{
        margin-bottom: 5px !important;
    }
    .mb10{
        margin-bottom: 10px !important;
    }
    .mb12{
        margin-bottom: 12px !important;
    }
    .mt5{
        margin-top: 5px !important;
    }
    .mt10{
        margin-top: 10px !important;
    }
    .mt14{
        margin-top: 14px !important;
    }
    a{
        cursor: pointer;
        color: #266aae !important
    }
    .ml10{
        margin-left: 10px !important;
    }
    .mr16{
        margin-right: 16px !important;
    }
    .p4-12{
        padding: 4px 12px !important;
    }
    .mt30{
        margin-top: 30px !important;
    }
    .fw400 {
        font-weight: 400;
    }
    .label-no-select{
        border: 1px solid #E8EAED !important;
        color: #333333 !important;
    }
    .parent_flag_name {
        color: #666666;
        font-size: 12px;
    }
    .parent_name{
        color:#333333;
        font-size: 22px;
        margin-bottom: 4px !important;
    }
    .lh30{
        line-height: 30px;
    }
    .flex{
        display: flex;
    }
    .min-height-300{
        min-height: 300px !important;
    }
    .flag_div{
        display: flex;
        /*align-items: center*/
    }
    .choose_flag_div{
        display: flex;
        width:90%;
        flex:1;
        flex-wrap: wrap;
        align-items: center;
        align-content:center;
    }
    .flag_span{
        font-size: 11px !important;
        font-weight: 400 !important;
        padding-left: 4px !important;
        padding-right: 4px !important;
    }
</style>
<script>
    function primaryContact(role) {
        $("input[name='primary_contact']").val(role)
    }

    var container = new Vue({
        el: "#container",
        data: {
            parentFlagRaw: '',//原始数据
            parentFlag:'',
            parentMainInfo:'',
            flagList:'',
            setFlagData1:[],//设置1 增加标签时插入 需要在原始数组中不存在的标签
            setFlagData0:[],//设置0 删除标签时插入 需要在原始数据中存在的标签
            parent:'',// father/mother
            showNonSelectable:false,//是否展示不可选择标签
            label_no_select:'label-no-select',
            active_style:{},
            spanIndex:[],
            defColor:"#d9534f",//默认标签选中背景色
        },
        mounted() {
            //获取家长标签信息
            this.getFlag()
            $('#parentFlag').on('hide.bs.modal', function (e) {
                $(".mask").fadeOut()
            })
            $('#parentFlag').on('show.bs.modal', function (e) {
                $(".mask").fadeIn()
            })
        },
        methods: {
            getFlag() {
                var that = this
                $.get(
                    '<?php echo $this->createUrl('getFlag');?>',
                    {},
                    function (data) {
                        that.parentFlagRaw = data.data.parentFlagInfo
                        that.parentMainInfo = data.data.parentMainInfo
                        that.flagList = data.data.flagConfig
                        that.parentFlag = JSON.parse(JSON.stringify(that.parentFlagRaw)); // 拷贝原数据, 深拷贝
                    },
                    'json');
            },
            showFlag(parent){
                var that = this
                that.parent = parent
                that.spanIndex = [];
                that.setFlagData0 = [];
                that.setFlagData1 = [];
                that.active_style = {};
                for (const key in that.parentFlagRaw[parent]) {
                    that.chooseFlag(that.parentFlag[parent][key],that.parentFlag[parent][key])
                }
                for (const key in that.flagList) {
                    if(that.flagList[key]['editable_by_staff'] == false){
                        that.showNonSelectable = true
                    }
                }
                $("#parentFlag").modal('show');
            },
            chooseFlag(flag,index){
                var that = this
                index = Number(index)
                let arrIndex = that.spanIndex.indexOf(index);
                if(arrIndex>-1){
                    //删除标签
                    that.spanIndex.splice(arrIndex,1);
                    that.active_style[index] = '';
                    //删除现有的数据
                    if(!that.setFlagData0.includes(flag) && that.parentFlagRaw[that.parent].includes(flag)){
                        that.setFlagData0.push(flag)
                    }
                    //标签在setFlagData1需要去掉
                    if(that.setFlagData1.includes(flag)){
                        that.setFlagData1.splice(that.setFlagData1.findIndex(item => item === flag), 1)
                    }
                }else{
                    //添加标签
                    that.spanIndex.push(index);
                    //本次选的标签
                    let active_styles = JSON.parse(JSON.stringify(that.active_style))
                    var backgroundColor = that.flagList[flag] && that.flagList[flag]['color']? that.flagList[flag]['color'] : that.defColor
                    that.active_style = {
                        [index]:{
                            backgroundColor:backgroundColor,
                            color:'#ffffff !important',
                            border:backgroundColor,
                            borderRadius:'0.4em'
                        }
                    }
                    that.active_style = { ...active_styles, ...that.active_style };
                    if(!that.parentFlagRaw[that.parent].includes(flag)){
                        that.setFlagData1.push(flag)
                    }
                    //标签在setFlagData0需要去掉
                    if(that.setFlagData0.includes(flag)){
                        that.setFlagData0.splice(that.setFlagData0.findIndex(item => item === flag), 1)
                    }
                }
            },
            setFlag(){
                var that = this
                if(!that.parent){
                    resultTip({
                        error: 'warning',
                        msg: 'parent error'
                    });
                    return ;
                }
                $.post(
                    '<?php echo $this->createUrl('setFlag');?>',
                    {
                        parent:that.parent,
                        setFlagData1:that.setFlagData1,
                        setFlagData0:that.setFlagData0,
                    },
                    function (data){
                        if(data.state === 'success'){
                            resultTip({
                                msg: data.state,
                                callback:function (){
                                    location.reload();
                                }
                            });
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    'json')
            }
        }
    })
</script>