<?php

/**
 * This is the model class for table "ivy_asa_staff_attendance".
 *
 * The followings are the available columns in table 'ivy_asa_staff_attendance':
 * @property integer $id
 * @property integer $program_id
 * @property integer $course_id
 * @property integer $vendor_id
 * @property string $status
 * @property integer $substituter
 * @property string $course_schedule_id
 * @property string $service_start
 * @property string $service_end
 * @property integer $course_staff_id
 * @property string $memo
 * @property integer $report_id
 * @property string $updated
 * @property string $updated_by
 */
class AsaStaffAttendance extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_asa_staff_attendance';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('program_id, course_id, vendor_id, status, course_schedule_id, service_start, service_end, course_staff_id, updated, updated_by', 'required'),
			array('program_id, course_id, vendor_id, substituter, course_staff_id, report_id', 'numerical', 'integerOnly'=>true),
			array('course_schedule_id, service_start, service_end, updated, updated_by', 'length', 'max'=>255),
			array('memo', 'safe'),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, program_id, course_id, vendor_id, status, substituter, course_schedule_id, service_start, service_end, course_staff_id, memo, report_id, updated, updated_by', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
			'vendor' => array(self::BELONGS_TO, 'AsaVendor', 'substituter'),
			'vendorTeacher' => array(self::BELONGS_TO, 'AsaVendor', 'vendor_id'),
			'course'  => array(self::BELONGS_TO,'AsaCourseStaff','course_staff_id'),
			'courseTitle' => array(self::BELONGS_TO, 'AsaCourse', 'course_id'),
			'groupTitle' => array(self::BELONGS_TO, 'AsaCourseGroup', 'program_id'),
			'userName' => array(self::BELONGS_TO, 'User', array('updated_by' => 'uid')),
			'repatch' => array(self::BELONGS_TO, 'AsaMonthReportRepatch', array('id' => 'attendance_id')),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'program_id' => 'Program',
			'course_id' => 'Course',
			'vendor_id' => 'Vendor',
			'status' => 'Status',
			'substituter' => 'Substituter',
			'course_schedule_id' => 'Course Schedule',
			'service_start' => 'Service Start',
			'service_end' => 'Service End',
			'course_staff_id' => 'Course Staff',
			'memo' => 'Memo',
			'report_id' => 'Report',
			'updated' => 'Updated',
			'updated_by' => 'Updated By',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('program_id',$this->program_id);
		$criteria->compare('course_id',$this->course_id);
		$criteria->compare('vendor_id',$this->vendor_id);
		$criteria->compare('status',$this->status,true);
		$criteria->compare('substituter',$this->substituter);
		$criteria->compare('course_schedule_id',$this->course_schedule_id,true);
		$criteria->compare('service_start',$this->service_start,true);
		$criteria->compare('service_end',$this->service_end,true);
		$criteria->compare('course_staff_id',$this->course_staff_id);
		$criteria->compare('memo',$this->memo,true);
		$criteria->compare('report_id',$this->report_id);
		$criteria->compare('updated',$this->updated,true);
		$criteria->compare('updated_by',$this->updated_by,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return AsaStaffAttendance the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}
