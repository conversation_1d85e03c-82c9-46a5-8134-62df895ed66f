<script type="text/template" id="enroll-structure-template">
    <h3 class="title">
        <span class="glyphicon glyphicon-list-alt"></span>
        <?php echo Yii::t('enrollment', 'Enrollment'); ?>
    </h3>
    <div class="sub-title form-inline">
        <input type="text" class="form-control" id="specified-date1" value="" style="z-index: 3;position:relative">
        <a href="javascript:void(0);" id="J_<%= rdm%>" class="btn btn-default pull-right J_attned" onclick="showChildren(this);">考勤异常 <span class="badge">0</span></a>
    </div>
    <div class="enroll-data">
        <div class="row">
            <div class="col-md-12" id="e-data">
                <table id="c_data" class="table table-bordered table-hover" style="font-size: 14px;">
                    <thead>
                    <tr class="active">
                        <th class="success" colspan="9"><?php echo Yii::t('enrollment', 'Current school year campus overview'); ?></th>
                    </tr>
                    <tr class="active">
                        <th></th>
                        <th><?php echo Yii::t('enrollment', 'Capacity'); ?></th>
                        <th><?php echo Yii::t('enrollment', 'Total number of students<br>paid vs unpaid'); ?></th>
                        <th><?php echo Yii::t('enrollment', 'Incoming students<br>paid vs unpaid'); ?></th>
                        <th><?php echo Yii::t('enrollment', 'Waitlist<br>paid vs unpaid'); ?></th>
                        <th><?php echo Yii::t('enrollment', 'Refunds<br>completed vs in process'); ?></th>
                        <th><?php echo Yii::t('enrollment', 'Students on extended leave'); ?></th>
                        <th><?php echo Yii::t('enrollment', 'Students without invoices'); ?></th>
                        <th><?php echo Yii::t('enrollment', 'Fill rate (daily)'); ?></th>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>

                <table id="t_data" class="table table-bordered table-hover" style="font-size: 14px;">
                    <thead>
                    <tr class="active">
                        <th class="success" colspan="9"><?php echo Yii::t('enrollment', 'Unassigned students'); ?></th>
                    </tr>
                    <tr class="active">
                        <th><?php echo Yii::t('enrollment', 'Waitlist of current school year<br>paid vs unpaid'); ?></th>
                        <th><?php echo Yii::t('enrollment', 'Number of next School Year (Deposit & Tuition)<br>paid VS. unpaid VS. dropout without refund'); ?></th>
                        <th><?php echo Yii::t('enrollment', 'Deposit paid for previous school years'); ?></th>
                        <th><?php echo Yii::t('enrollment', 'Students without invoices'); ?></th>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>

                <table id="pay_data" class="table table-bordered" style="font-size: 14px;">
                    <thead>
                    <tr class="active">
                        <th class="success" colspan="9"><?php echo Yii::t('enrollment', '即将入园'); ?></th>
                    </tr>
                    </thead>
                    <tbody>
                       
            </tbody>
                </table>
            </div>
        </div>
    </div>
</script>

<script type="text/template" id="enroll-item-template">
    <th class="active"><%= title%><% if(id>0 && status){%><span class="text-danger" style="padding-left: 10px;"><%= status%></span><% } %></th>
    <td><%= data.capacity%></td>
    <td><%= data.enrollment%> = <%= echoA(data.num1, data.childids1)%> + <%= echoA(data.num2, data.childids2)%></td>
    <td><%= data.future%> = <%= echoA(data.num15, data.childids15)%> + <%= echoA(data.num16, data.childids16)%></td>
    <td><%= data.waiting%> = <%= echoA(data.num3, data.childids3)%> + <%= echoA(data.num4, data.childids4)%></td>
    <td><%= data.refunds%> = <%= echoA(data.num13, data.childids13)%> + <%= echoA(data.num14, data.childids14)%></td>
    <td><%= echoA(data.num9, data.childids9)%></td>
    <td><%= echoA(data.num5, data.childids5)%></td>
    <td><%= data.fill_ratio%></td>
</script>

<script type="text/template" id="enroll-noclass-template">
    <tr>
        <td><%= echoA(data.num6, data.childids6)%> + <%= echoA(data.num7, data.childids7)%></td>
        <td><%= echoA(data.num10, data.childids10)%> + <%= echoA(data.num11, data.childids11)%> - <%= echoA(data.num17, data.childids17)%></td>
        <td><%= echoA(data.num12, data.childids12)%></td>
        <td><%= echoA(data.num8, data.childids8)%></td>
    </tr>
</script>

<script type="text/template" id="enroll-pay-template">
    <tr>
        <td rowspan="2"><?php echo Yii::t('dashboard', 'This Y');?><br><%= echoA(childData.incoming, childData.incomingChildid)%></td>
        <td colspan="2"><%= echoA(childData.incoming_paid, childData.incoming_paidChildid)%> <?php echo Yii::t('dashboard', 'Paid');?></td>
    </tr>
    <tr>
        <td colspan="2"><%= echoA(childData.incoming_unpaid, childData.incoming_unpaidChildid)%> <?php echo Yii::t('dashboard', 'Unpaid');?></td>
    </tr>
    <tr>
        <td rowspan="4"><?php echo Yii::t('dashboard', 'Next Y');?><br><%= echoA(childData.nextComing, childData.nextComingChildid)%></td>
        <td rowspan="2"><?php echo Yii::t('dashboard', 'Returning');?><br><%= echoA(childData.intersectChild, childData.intersectChildId)%></td>
        <td><%= echoA(childData.neChildPaidInte, childData.neChildPaidInteChildid)%> <?php echo Yii::t('dashboard', 'Paid');?></td>
    </tr>
    <tr>
        <td><%= echoA(childData.neChildUnpaidInte, childData.neChildUnpaidInteChildid)%> <?php echo Yii::t('dashboard', 'Unpaid');?></td>
    </tr>
    <tr>
        <td rowspan="4"><?php echo Yii::t('dashboard', 'New');?><br><%= echoA(childData.newNextComing, childData.newNextComingChildid)%> </td>
        <td><%= echoA(childData.neChildPaid, childData.neChildPaidChildid)%> <?php echo Yii::t('dashboard', 'Paid');?></td>
    </tr>
    <tr>
        <td><%= echoA(childData.neChildUnpaid, childData.neChildUnpaidChildid)%> <?php echo Yii::t('dashboard', 'Unpaid');?></td>
    </tr>

   <!--  <tr class="active">
        <th class="success" colspan="9"><?php echo Yii::t('enrollment', '即将入园付费信息'); ?></th>
    </tr>
    <tr class="active">
        <th colspan="2"><?php echo Yii::t('enrollment', '本学年'); ?><br><%= echoA(childData.incoming, childData.childids6)%></th>
        <th colspan="4"><?php echo Yii::t('enrollment', '下学年'); ?><br><%= echoA(childData.nextComing, childData.nextComingChildid)%> </th>
        
    </tr>
    <tr>
        <th  rowspan="2">已付</th>
        <th  rowspan="2">未付</th>
        <th colspan="2"><?php echo Yii::t('enrollment', '返校'); ?><br><%= echoA(childData.intersectChild, childData.intersectChildId)%>  </th>
        <th colspan="2"><?php echo Yii::t('enrollment', '新生'); ?><br><%= echoA(childData.newNextComing, childData.newNextComingChildid)%>  </th>
    </tr>
    <tr>
        <th>已付</th>
        <th>未付</th>
        <th>已付</th>
        <th>未付</th>
    </tr>
    <tr>
        <td><%= echoA(childData.incoming_paid, childData.childids7)%></td>
        <td><%= echoA(childData.incoming_unpaid, childData.childids7)%></td>
        <td><%= echoA(childData.neChildPaidInte, childData.neChildPaidInteChildid)%></td>
        <td><%= echoA(childData.neChildUnpaidInte, childData.neChildUnpaidInteChildid)%></td>
        <td><%= echoA(childData.neChildPaid, childData.neChildPaidChildid)%></td>
        <td><%= echoA(childData.neChildUnpaid, childData.neChildUnpaidChildid)%></td>
    </tr> -->
</script>