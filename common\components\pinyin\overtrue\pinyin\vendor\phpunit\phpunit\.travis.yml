language: php

php:
  - 5.3
  - 5.4
  - 5.5
  - 5.6

env:
  matrix:
    - DEPENDENCIES="high"
    - DEPENDENCIES="low"

sudo: false

before_install:
  - composer self-update
  - composer clear-cache

install:
  - if [[ "$DEPENDENCIES" = 'high' ]]; then travis_retry composer update --no-interaction --no-ansi --no-progress --no-suggest --optimize-autoloader --prefer-stable; fi
  - if [[ "$DEPENDENCIES" = 'low' ]]; then travis_retry composer update --no-interaction --no-ansi --no-progress --no-suggest --optimize-autoloader --prefer-stable --prefer-lowest; fi

script:
  - ./phpunit
  - ./phpunit --configuration ./build/travis-ci-fail.xml > /dev/null; if [ $? -eq 0 ]; then echo "SHOULD FAIL"; false; else echo "fail checked"; fi;
  - xmllint --noout --schema phpunit.xsd phpunit.xml
  - xmllint --noout --schema phpunit.xsd tests/_files/configuration.xml
  - xmllint --noout --schema phpunit.xsd tests/_files/configuration_empty.xml
  - xmllint --noout --schema phpunit.xsd tests/_files/configuration_xinclude.xml -xinclude

notifications:
  email: false

