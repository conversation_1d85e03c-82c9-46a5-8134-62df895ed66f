<div id='protocol' v-cloak>
    <div class="progress">
        <div class="progress-bar progress-bar-success" :style="'width:'+ parsefloat(adopt.length/total) +'%;'">
            <span v-if='adopt.length!=0' :title="adopt.length+'/'+total">{{parsefloat(adopt.length/total)}}% ({{adopt.length}}/{{total}})</span>
            <span v-else>0% ({{adopt.length}}/{{total}})</span>
        </div>
        <div class="progress-bar progress-bar-warning progress-bar-striped" :style="'width:'+ parsefloat(defaults.length/total) +'%;'">
            <span v-if='defaults.length!=0' :title="defaults.length+'/'+total">{{parsefloat(defaults.length/total)}}% ({{defaults.length}}/{{total}})</span>
            <span v-else>0% ({{defaults.length}}/{{total}})</span>
        </div>
        <div class="progress-bar progress-bar-danger" :style="'width:'+ parsefloat(notPass.length/total) +'%;'">
            <span v-if='notPass.length!=0' :title="notPass.length+'/'+total">{{parsefloat(notPass.length/total)}}% ({{notPass.length}}/{{total}})</span>
            <span v-else>0% ({{notPass.length}}/{{total}})</span>
        </div>
    </div>
    <ul class="nav nav-tabs" role="tablist">
        <li role="presentation" class="active"><a href="#home" aria-controls="home" role="tab" data-toggle="tab">已填写 <span class="badge">{{res.stepNum}}</span></a></li>
        <li role="presentation"><a href="#unstart" aria-controls="unstart" role="tab" data-toggle="tab">未填写 <span class="badge badge-error">{{res.unStartNum}}</span></a></li>
    </ul>
    <div class="tab-content">
        <div role="tabpanel" class="tab-pane active" id="home">
            <table class="table table-bordered mb0" v-if='tableData.length!=0'>
                <thead id='table'>
                    <tr>
                        <th class="nosort">
                            <label class="checkbox-inline">
                                <input type="checkbox" id="inlineCheckbox1"  v-model="checked" @click="changeAllChecked()">全选
                            </label>
                        </th>
                        <th>名字</th>
                        <th>班级</th>
                        <th>状态</th>
                        <th>生日</th>
                        <th class="nosort">签名</th>
                        <th>提交时间</th>
                        <th class="nosort">操作</th>
                    </tr>
                </thead>
                <tbody>
                    <template v-for='(list,index) in tableData'>
                        <tr>
                            <td width="6%">
                                <input type="checkbox" id="inlineCheckbox1" :value="list.id" v-model="checkedNames">
                            </td>
                            <td><a href="javascript:;" onclick="overalls(this)" :data-id='list.childid' :data-name='list.childid_name' :data-class='list.class_title'>{{list.childid_name}}</a></td>
                            <td>{{list.class_title}}</td>
                            <td width="8%">{{completeConfig[list.complete]}}</td>
                            <td width="8%">{{list.child_birthday}}</td>
                            <td width="10%">
                                <img :src="list.info.filedata" v-if='list.info.filedata' class="img-thumbnail sign" @click='bigImg(list.info)'>
                            </td>
                            <td width="12%">{{list.updated}}</td>
                            <td width="12%">
                                <button class="btn btn-primary btn-xs" @click='Edit(index)'>查看</button>
                                <a  class="btn btn-primary btn-xs" :href="'<?php echo $this->createUrlReg('print', array('branchId' => $this->branchId)); ?>&infoId='+list.id+'&stepId=8'" target="_blank">打印</a>
                            </td>
                        </tr>
                    </template>
                </tbody>
            </table>
            <div v-else class="mt15">
                <div class="alert alert-warning" role="alert">暂无数据</div>
            </div>
            <p><button class="btn btn-primary" @click='checkAll()' v-if='tableData.length!=0'>批量审核</button></p>
        </div>
        <!-- 未填写的数据 -->
        <div role="tabpanel" class="tab-pane " id="unstart">
            <table class="table table-bordered mb0s" id='unTable' v-if='unStart.length!=0'>
                <thead>
                    <tr>
                        <th>名字</th>
                        <th>班级</th>
                        <th>状态</th>
                        <th>生日</th>
                        <!-- <th>操作</th> -->
                    </tr>
                </thead>
                <tbody>
                <template v-for='(list,index) in unStart'>
                    <tr>
                        <td width="25%"><a href="javascript:;" onclick="overalls(this)" :data-id='list.childid' :data-name='list.childid_name' :data-class='list.class_title'>{{list.childid_name}}</a></td>
                        <td width="25%">{{list.class_title}}</td>
                        <td width="25%">未填写</td>
                        <td width="25%">{{list.child_birthday}}</td>
                        <!-- <td width="8%">
                            <button class="btn btn-primary btn-xs" @click='overall(index)'>整体情况</button>
                        </td> -->
                    </tr>
                </template>
                </tbody>
             </table>
            <div v-else class="mt15">
                <div class="alert alert-warning" role="alert">暂无数据</div>
            </div>
        </div>
    </div>
    <div class="modal fade bs-example-modal-lg" id="Edit" data-backdrop="static">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">
                        &times;
                    </button>
                    <h4 class="modal-title" id="myModalLabel">查看
                    </h4>
                </div>
                <div class="modal-body">
                    <div>
                        <p v-if='historyData.length > 0'>当前数据</p>
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>名字</th>
                                    <th>班级</th>
                                    <th>状态</th>
                                    <th>生日</th>
                                    <th>签名</th>
                                </tr>
                            </thead>
                            <tbody>
                            <tr>
                                <td width="8%">{{Thisdata.childid_name}}</td>
                                <td width="12%">{{Thisdata.class_title}}</td>
                                <td width="7%">{{completeConfig[Thisdata.complete]}}</td>
                                <td width="8%">{{Thisdata.child_birthday}}</td>
                                <td width="8%">
                                    <img :src="info.filedata" v-if='info.filedata' class="img-thumbnail sign">
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <div v-if='historyData.length > 0'>
                        <p class="mt15">驳回数据</p>
                        <table class="table table-bordered">
                            <thead>
                            <tr>
                                <th>名字</th>
                                <th>班级</th>
                                <th>状态</th>
                                <th>生日</th>
                                <th>签名</th>
                            </tr>
                            </thead>
                            <tbody>
                            <template v-for='(history,idx) in historyData'>
                                <tr>
                                    <td width="8%">{{Thisdata.childid_name}}</td>
                                    <td width="12%">{{Thisdata.class_title}}</td>
                                    <td width="7%">{{completeConfig[Thisdata.complete]}}</td>
                                    <td width="8%">{{Thisdata.child_birthday}}</td>
                                    <td width="8%">
                                        <img :src="history.filedata" v-if='history.filedata' class="img-thumbnail sign">
                                    </td>
                                </tr>
                                <tr><td colspan="5">驳回原因：{{history.reject_memo}}</td></tr>
                            </template>
                            </tbody>
                        </table>
                    </div>
                    <p class="mt15">
                        <label class="radio-inline">
                            <input type="radio" name="inlineRadioOptions" id="inlineRadio1" value="1"  v-model="complete"> 审核通过
                        </label>
                        <label class="radio-inline">
                            <input type="radio" name="inlineRadioOptions" id="inlineRadio2" value="-1"  v-model="complete"> 驳回
                        </label>
                    </p>
                    <p v-if='complete==-1'>
                        <input type="text" class="form-control" id="reject_memo" placeholder="请写出驳回原因" :value='Thisdata.reject_memo'>
                    </p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" @click='checkAll()'>提交</button>
                    <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade bs-example-modal-lg" id="bigImg">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">
                        &times;
                    </button>
                    <h4 class="modal-title" id="myModalLabel">查看大图
                    </h4>
                </div>
                <div class="modal-body">
                    <img :src="imgBig" style="width:500px">
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    var res = <?php echo json_encode($res); ?>;
    var completeConfig = <?php echo json_encode($completeConfig) ?>;//状态

    var protocol = new Vue({
        el: "#protocol",
        data: {
            checked: false,
            checkedNames: [],
            tableArr: [],
            res:res,
            total: res.allNum,
            number:res.number,
            tableData:res.data,
            info: '',
            Thisdata: '',
            historyData: '',
            complete: '',
            adopt: [], //通过
            defaults: [], //默认
            notPass: [], //驳回
            YearList: '',
            completeConfig:completeConfig,
            imgBig:'',
            unStart:res.unStart
        },
        created: function() {
            for(var i = 0; i < res.data.length; i++) {
                this.tableArr.push(res.data[i].id)
                if(res.data[i].complete == 1) {
                    this.adopt.push(res.data[i].complete)
                } else if(res.data[i].complete == 0 || res.data[i].complete == 2 ) {
                    this.defaults.push(res.data[i].complete)
                } else {
                    this.notPass.push(res.data[i].complete)
                }
            }
        },
        watch: {
            "checkedNames": function() {
                if(this.checkedNames.length != this.tableArr.length) {
                    this.checked = false
                } else {
                    this.checked = true
                }
            }
        },
        mounted: function() {},
        methods: {
            parsefloat(num) {
                return Number(num * 100).toFixed(2);
            },
            changeAllChecked: function() {
                var self = this;
                if(!self.checked) {
                    self.checkedNames = []
                } else {
                    self.checkedNames = self.tableArr;
                }
            },
            checkAll() {
                var datas = ''
                if(this.Thisdata != '') {
                    datas = {
                        step_ids: this.Thisdata.id,
                        complete: this.complete,
                        reject_memo: $('#reject_memo').val()
                    }
                } else {
                    datas = {
                        step_ids: this.checkedNames,
                        complete: '1'
                    }
                }
                $.ajax({
                    url: "<?php echo $this->createUrlReg('check')?>",
                    type: 'post',
                    dataType: 'json',
                    data: datas,
                    success: function(data) {
                        if(data.state == "success") {
                            resultTip({
                                msg: data.message
                            });
                            if(protocol.Thisdata != '') {
                                protocol.Thisdata.complete = protocol.complete
                                protocol.Thisdata.reject_memo = $('#reject_memo').val()
                                $('#Edit').modal('hide')
                                protocol.Thisdata = []

                            } else {
                                for(key in protocol.tableData) {
                                    for(var i = 0; i < protocol.checkedNames.length; i++) {
                                        if(protocol.tableData[key].id == protocol.checkedNames[i]) {
                                            Vue.set(protocol.tableData[key], 'complete', '1')
                                        }
                                    }
                                }
                                protocol.checkedNames = []
                            }
                            protocol.adopt = []
                            protocol.notPass = []
                            protocol.defaults = []
                            for(var i = 0; i < protocol.tableData.length; i++) {
                                if(protocol.tableData[i].complete == 1) {
                                    protocol.adopt.push(protocol.tableData[i].complete)
                                } else if(protocol.tableData[i].complete == 0) {
                                    protocol.defaults.push(protocol.tableData[i].complete)
                                } else {
                                    protocol.notPass.push(protocol.tableData[i].complete)
                                }
                            }
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        alert('请求错误')
                    }
                })
            },
            bigImg(img){
                this.imgBig=img.filedata
                $('#bigImg').modal('show')
            },
            Edit(index) {
                this.Thisdata = this.tableData[index]
                this.info = this.tableData[index].info
                this.complete = this.tableData[index].complete
                if(this.tableData[index].link_id != '0') {
                    $.ajax({
                        url: "<?php echo $this->createUrlReg('showDetail')?>",
                        type: 'post',
                        dataType: 'json',
                        data: {
                            step_id: this.tableData[index].link_id, //-1驳回 0未操作 1通过 2重新提交
                        },
                        success: function(data) {
                            if(data.state == "success") {
                                protocol.historyData = data.data
                                $('#Edit').modal('show')
                            } else {
                                resultTip({
                                    error: 'warning',
                                    msg: data.message
                                });
                            }
                        },
                        error: function(data) {
                            alert('请求错误')
                        }
                    })
                } else {
                    $('#Edit').modal('show')
                }
            },
        },
    })
</script>
<script>
    $(document).ready( function () {
        var table = $('#table').parent().DataTable({
                order: [[ 3, "desc" ], [ 6, "desc" ]],
                paging: false,
                info: false,
                searching: false,
                columnDefs: [ {
                    "targets": 'nosort',
                    "orderable": false
                } ],
        });
        var table = $('#unTable').DataTable({
                aaSorting: [0, 'desc'], // 默认排序
                paging: false,
                info: false,
                searching: false,
        });
    });
</script>