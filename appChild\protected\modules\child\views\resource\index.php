<?php
$this->breadcrumbs=array(
		Yii::t("navigations","Resources")=>array('/child/resource/index'),
		Yii::t("navigations","Index"),
);?>

<h1>
	<label><?php echo Yii::t("navigations",'Resources'); ?> </label>
</h1>

<?php
$i=1;
foreach($items as $key=>$item):

?>
<dl class="span-9 resource-list <?php if(($i+1)%2==0) echo " last";?>">
	<dt>
		<?php
		$image = CHtml::image(Yii::app()->theme->baseUrl."/images/icons/resource/".$extra["covers"][$key], $item["label"]);
		echo CHtml::link($image, $item['url'], array("title"=>$item["label"]));
		?>
	</dt>
	<dd>
		<h3>
			<?php echo CHtml::link($item["label"], $item['url'], array("title"=>$item["label"]));?>
		</h3>
		<p>
			<?php echo $extra["desc"][$key];?>
		</p>
	</dd>
</dl>

<?php endforeach;?>


<?php
$nextUrl = $this->createUrl("//child/support/campus",array("demo"=>"guide"));

Yii::import('ext.guide.class.*');
$this->widget('ext.guide.Guide', array(
		'options'=>array(
				'guides'=>array(
						0=>GuideItem::navGuide(10, null, true,'navigations_resource', 160),
							
						10 =>GuideItem::subNavGuide($nextUrl, null, "subnav_resource"),

				)
		)
));
//endif;
?>