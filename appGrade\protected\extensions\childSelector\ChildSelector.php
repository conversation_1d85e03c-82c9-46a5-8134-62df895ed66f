<?php
/*
 * Author: RAN Xin
 */

class ChildSelector extends CWidget{
	public $currentCid;
	public $myChildObjs;
	public $id;
	public $class;
	
	public function init(){
		
	}
	
	public function run(){
		$cfgs = Mims::LoadConfig('CfgProfile');
		$countrys = Country::model()->getData(Yii::app()->language, Yii::app()->language);
		$country = null;
		if(!empty($this->myChildObjs)){
		$country = $countrys[$this->myChildObjs[$this->currentCid]->country];
		}

		$this->render('selector',array("cfgs"=>$cfgs, "country"=>$country));

	}
}

?>