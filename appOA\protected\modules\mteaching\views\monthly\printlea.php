<?php
if($data['flag'] == 'mik'):?>
<div style="margin:15px 0;"><strong>说明：</strong>以下是英文教学的月度整体规划，更详细的课程内容参见艾毅在线周课表。由于幼儿园阶段孩子的母语（中文）也还处在学习与发展过程中，所以对于他们英文学习的主要期待应该是开始领悟部分含义（2-3岁），逐渐唱一些儿歌或讲一些简单的单词（3-4岁），说出几首学过的儿歌、童谣，使用几个学过的词汇（4-5岁），开始试着用学过的词汇组成简单的句子进行交流（5-6岁）。</div>
<table width='100%' class='t'>
    <tr>
        <td colspan='2' align='center' style="font-size: 16px;font-weight:bold;"><?php echo $classModel->title;?><br><?php echo Yii::t('teaching', 'Monthly MIK English Language Learning Content');?></td>
    </tr>
    <tr>
        <td valign='top' style='width:100px;'><label><?php echo Yii::t('teaching', 'Words')?></label></td>
        <td><?php echo nl2br($data['words'])?></td>
    </tr>
    <tr>
        <td valign='top'><label><?php echo Yii::t('teaching', 'Sentences')?></label></td>
        <td><?php echo nl2br($data['sentences'])?></td>
    </tr>
    <tr>
        <td valign='top'><label><?php echo Yii::t('teaching', 'Letters')?></label></td>
        <td><?php echo nl2br($data['letters'])?></td>
    </tr>
    <tr>
        <td valign='top'><label><?php echo Yii::t('teaching', 'Songs')?></label></td>
        <td><?php echo nl2br($data['songs'])?></td>
    </tr>
    <tr>
        <td valign='top'><label><?php echo Yii::t('teaching', 'Stories')?></label></td>
        <td><?php echo nl2br($data['stories'])?></td>
    </tr>
    <tr>
        <td valign='top'><label><?php echo Yii::t('teaching', 'Functional Language')?></label></td>
        <td><?php echo nl2br($data['functional'])?></td>
    </tr>
</table>
<?php else:?>
    <div style="margin:15px 0;"><?php echo Yii::t('teaching', '<strong>Note:</strong> The information below is a summary of this unit’s classroom learning. As children learn from repetition, parents can support children’s learning by singing the songs, reciting the rhymes and reading the stories at home. Parents can also discuss the unit theme and the activities that took place with their child. Children are different in their own ways, so each child’s developmental progress is unique. Some children can grasp certain concepts quickly, while comprehension of other concepts may need more time. Differences in learning is natural and to be expected. Therefore, the information provided below should not to be used as a checklist to assess your child.')?></div>
<table width='100%' class='t'>
    <tr>
        <td colspan='2' align='center' style="font-size: 16px;font-weight:bold;"><?php echo $classModel->title.' '.Yii::t('teaching', 'Unit Learning Contents')?></td>
    </tr>
    <tr>
        <td valign='top' style='width:100px;'><label><?php echo Yii::t('teaching', 'Unit Theme')?></label></td>
        <td><?php echo nl2br($data['title'])?></td>
    </tr>
    <tr>
        <td valign='top' style='width:100px;'><label><?php echo Yii::t('teaching', 'Big Idea')?></label></td>
        <td><?php echo nl2br($data['newIdea'])?></td>
    </tr>
    <tr>
        <td valign='top' style='width:100px;'><label><?php echo Yii::t('teaching', 'Key Questions')?></label></td>
        <td><?php echo nl2br($data['questions'])?></td>
    </tr>
    <tr>
        <td valign='top'><label><?php echo Yii::t('teaching', 'Activities')?></label></td>
        <td><?php echo nl2br($data['activities'])?></td>
    </tr>
    <tr>
        <td valign='top'><label><?php echo Yii::t('teaching', 'Children\'s Song/Rhymes/Stories')?></label></td>
        <td><?php echo nl2br($data['song'])?></td>
    </tr>
    <tr>
        <td valign='top'><label><?php echo Yii::t('teaching', 'Special Festivals')?></label></td>
        <td><?php echo nl2br($data['festivals'])?></td>
    </tr>
    <tr>
        <td valign='top'><label><?php echo Yii::t('teaching', 'Field Trips')?></label></td>
        <td><?php echo nl2br($data['trips'])?></td>
    </tr>
    <tr>
        <td colspan='2' align='center' style="font-size: 16px;font-weight:bold;"><?php echo $classModel->title.' '.Yii::t('teaching', 'Unit Learning Objectives')?></td>
    </tr>
    <tr>
        <td valign='top' style='width:100px;'><label><?php echo Yii::t('teaching', 'Physical & Health')?></label></td>
        <td><?php echo nl2br($data['newPhysical'])?></td>
    </tr>
    <tr>
        <td valign='top' style='width:100px;'><label><?php echo Yii::t('teaching', 'Language')?></label></td>
        <td><?php echo nl2br($data['newLanguage'])?></td>
    </tr>
    <tr>
        <td valign='top' style='width:100px;'><label><?php echo Yii::t('teaching', 'Social')?></label></td>
        <td><?php echo nl2br($data['newSocial'])?></td>
    </tr>
    <tr>
        <td valign='top' style='width:100px;'><label><?php echo Yii::t('teaching', 'Science & Math')?></label></td>
        <td><?php echo nl2br($data['newScience'])?></td>
    </tr>
    <tr>
        <td valign='top' style='width:100px;'><label><?php echo Yii::t('teaching', 'Art')?></label></td>
        <td><?php echo nl2br($data['newArt'])?></td>
    </tr>
    <!--<tr>
        <td valign='top' style='width:100px;'><label><?php /*echo Yii::t('teaching', 'Language and Literacy')*/?></label></td>
        <td><?php /*echo nl2br($data['language'])*/?></td>
    </tr>
    <tr>
        <td valign='top'><label><?php /*echo Yii::t('teaching', 'Mathematical Thinking')*/?></label></td>
        <td><?php /*echo nl2br($data['mathematical'])*/?></td>
    </tr>
    <tr>
        <td valign='top'><label><?php /*echo Yii::t('teaching', 'Science Thinking')*/?></label></td>
        <td><?php /*echo nl2br($data['science'])*/?></td>
    </tr>
    <tr>
        <td valign='top'><label><?php /*echo Yii::t('teaching', 'Physical/Health')*/?></label></td>
        <td><?php /*echo nl2br($data['physical'])*/?></td>
    </tr>
    <tr>
        <td valign='top'><label><?php /*echo Yii::t('teaching', 'Social/Personal')*/?></label></td>
        <td><?php /*echo nl2br($data['social'])*/?></td>
    </tr>
    <tr>
        <td valign='top'><label><?php /*echo Yii::t('teaching', 'Art')*/?></label></td>
        <td><?php /*echo nl2br($data['art'])*/?></td>
    </tr>
    <tr>
        <td valign='top'><label><?php /*echo Yii::t('teaching', 'Social Studies')*/?></label></td>
        <td><?php /*echo nl2br($data['social_studies'])*/?></td>
    </tr>-->
</table>
<?php endif;?>

<style>
    table.t{border-collapse:collapse;font-size:14px;}
    table.t td{border:1px solid #000; padding: 5px;}
</style>