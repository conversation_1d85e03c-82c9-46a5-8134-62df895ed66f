<?php

/**
 * This is the model class for table "ivy_survey_staff_feedback".
 *
 * The followings are the available columns in table 'ivy_survey_staff_feedback':
 * @property integer $id
 * @property integer $survey_id
 * @property string $branchid
 * @property integer $userid
 * @property integer $ic_id
 * @property integer $is_fb
 * @property integer $status
 * @property integer $fb_time
 * @property integer $ext_info01
 * @property integer $ext_info02
 * @property integer $ext_info03
 * @property integer $ext_info04
 * @property integer $ext_info05
 * @property integer $update_user
 * @property integer $update_time
 */
class WSurveyStaffFeedback extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_survey_staff_feedback';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('survey_id, userid, ic_id, is_fb, status, fb_time, ext_info01, ext_info02, ext_info03, ext_info04, ext_info05, update_user, update_time', 'numerical', 'integerOnly'=>true),
			array('branchid', 'length', 'max'=>255),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, survey_id, branchid, userid, ic_id, is_fb, status, fb_time, ext_info01, ext_info02, ext_info03, ext_info04, ext_info05, update_user, update_time', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'survey_id' => 'Survey',
			'branchid' => 'Branchid',
			'userid' => 'Userid',
			'ic_id' => 'Ic',
			'is_fb' => 'Is Fb',
			'status' => 'Status',
			'fb_time' => 'Fb Time',
			'ext_info01' => 'Ext Info01',
			'ext_info02' => 'Ext Info02',
			'ext_info03' => 'Ext Info03',
			'ext_info04' => 'Ext Info04',
			'ext_info05' => 'Ext Info05',
			'update_user' => 'Update User',
			'update_time' => 'Update Time',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('survey_id',$this->survey_id);
		$criteria->compare('branchid',$this->branchid,true);
		$criteria->compare('userid',$this->userid);
		$criteria->compare('ic_id',$this->ic_id);
		$criteria->compare('is_fb',$this->is_fb);
		$criteria->compare('status',$this->status);
		$criteria->compare('fb_time',$this->fb_time);
		$criteria->compare('ext_info01',$this->ext_info01);
		$criteria->compare('ext_info02',$this->ext_info02);
		$criteria->compare('ext_info03',$this->ext_info03);
		$criteria->compare('ext_info04',$this->ext_info04);
		$criteria->compare('ext_info05',$this->ext_info05);
		$criteria->compare('update_user',$this->update_user);
		$criteria->compare('update_time',$this->update_time);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return WSurveyStaffFeedback the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

    public function getFeedBackCount($surveyId=0, $branchId=null)
    {
        if($surveyId && $branchId) {
            $crit = new CDbCriteria();
            $crit->compare('survey_id', $surveyId);
            $crit->compare('branchid', $branchId);
            $crit->compare('is_fb', 1);
            $count = self::model()->count($crit);
            return $count;
        }
        return 0;
    }

    public function getFeedBackTotalCount($surveyId=0, $branchId=null)
    {
        if($surveyId && $branchId) {
            $crit = new CDbCriteria();
            $crit->compare('survey_id', $surveyId);
            $crit->compare('branchid', $branchId);
            $count = self::model()->count($crit);
            return $count;
        }
        return 0;
    }
}
