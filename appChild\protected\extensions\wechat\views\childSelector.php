<?php 
    // 由于微信支付安全目录问题，特殊处理支付页面的跳转URL
    if ($jumpUrl == 'unpaidInvoices') {
        $baseUrl = Yii::app()->createUrl('wechat/user/unpaidInvoices');
        $appId = $this->controller->appId;
        $state = $_GET['state'];

        $payMentUrl = "https://open.weixin.qq.com/connect/oauth2/authorize?appid=$appId&redirect_uri=$baseUrl/&response_type=code&scope=snsapi_base&state=$state#wechat_redirect";
    } else {
        $payMentUrl = $this->controller->createUrl($jumpUrl, $params);
    }
?>
<?php if(count($this->myChildObjs) > 1): ?>
<!-- 选择孩子 -->
    <a href="javascript:void(0);" id="showChildern" class="weui_media_box weui_media_appmsg weui_cells_access" style="border-bottom: 1px solid #f2f2f2;">
        <div class="weui_media_hd">
            <?php if($childObj->photo):echo CHtml::image(CommonUtils::childPhotoUrl($childObj->photo, 'small'),'',array('class'=>'weui_media_appmsg_thumb'));endif;?>
        </div>
        <div class="weui_media_bd">
            <h4 class="weui_media_title"><?php echo $childObj->getChildName();?></h4>
        </div>
        <span class="weui_cell_ft"><?php echo Yii::t("wechat","Switch");?></span>
    </a>
<?php endif; ?>
<!--BEGIN 孩子选择框-->
<div id="Childern">
    <div class="weui_mask_transition" id="childern_mask"></div>
    <div class="weui_actionsheet" id="childern_sheet">
        <div class="weui_actionsheet_menu">
            <?php foreach ($myChildObjs as $childObj):?>
                <div class="weui_actionsheet_cell" onclick="setChildid(<?php echo $childObj->childid; ?>)"><?php echo $childObj->getChildName(); ?></div>
            <?php endforeach; ?>
        </div>
        <div class="weui_actionsheet_action">
            <div class="weui_actionsheet_cell" id="childern_cancel"><?php echo Yii::t('global', 'Cancel'); ?></div>
        </div>
    </div>
</div>
<!--END 孩子选择框-->

<script>
    // 选择孩子
    $('#showChildern').click(function () {
        var mask = $('#childern_mask');
        var weuiActionsheet = $('#childern_sheet');
        weuiActionsheet.addClass('weui_actionsheet_toggle');
        mask.show().addClass('weui_fade_toggle').one('click', function () {
            hideActionSheet(weuiActionsheet, mask);
        });
        $('#childern_cancel').one('click', function () {
            hideActionSheet(weuiActionsheet, mask);
        });
        weuiActionsheet.unbind('transitionend').unbind('webkitTransitionEnd');

        function hideActionSheet(weuiActionsheet, mask) {
            weuiActionsheet.removeClass('weui_actionsheet_toggle');
            mask.removeClass('weui_fade_toggle');
            weuiActionsheet.on('transitionend', function () {
                mask.hide();
            }).on('webkitTransitionEnd', function () {
                mask.hide();
            })
        }
    });

    // 设置孩子cookie
    function setChildid(childid) {
        deleteCookie('childid');
        setCookie('childid', childid, 30);
        location.href = '<?php echo $payMentUrl; ?>';
    }

    /**
     * 设置cookie
     * @param {string} name  键名
     * @param {string} value 键值
     * @param {integer} days cookie周期
     */
    function setCookie(name,value,days) {
        if (days) {
            var date = new Date();
            date.setTime(date.getTime()+(days*24*60*60*1000));
            var expires = "; expires="+date.toGMTString();
        }else{
            var expires = "";
        }
        document.cookie = name+"="+value+expires+"; path=/";
    }
     
    // 获取cookie
    function getCookie(name) {
        var nameEQ = name + "=";
        var ca = document.cookie.split(';');
        for(var i=0;i < ca.length;i++) {
            var c = ca[i];
            while (c.charAt(0)==' ') c = c.substring(1,c.length);
            if (c.indexOf(nameEQ) == 0) return c.substring(nameEQ.length,c.length);
        }
        return null;
    }
     
    // 删除cookie
    function deleteCookie(name) {
        setCookie(name,"",-1);
    }
</script>