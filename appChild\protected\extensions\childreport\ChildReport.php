<?php
class ChildReport extends CWidget{
	public $childId;
    public $yId;
    public $classId;
    public $weekNum;
    public $cssFile;
    public $assetsUrl;
	
	public function init(){
		Yii::import('ext.schoolnews.models.*');

//        if($this->assetsUrl===null)
//			$this->assetsUrl=Yii::app()->getAssetManager()->publish(dirname(__FILE__) . '/assets', false, -1, YII_DEBUG);

        $cs = Yii::app()->clientScript;
        if (false !== $this->cssFile)
        {
            if (null === $this->cssFile)
                //$this->cssFile = $this->assetsUrl . '/css.css';
				$this->cssFile = Yii::app()->theme->baseUrl.'/css/portfolio.css';
            $cs->registerCssFile($this->cssFile);
        }
	}
	
	public function run(){

        Mims::LoadHelper('HtoolKits');
        
        $criteria = new CDbCriteria();
        $criteria->compare('classid', $this->classId);
        $criteria->compare('yid', $this->yId);
        $criteria->compare('weeknumber', $this->weekNum);
        $criteria->compare('stat', 20);
        $criteria->compare('childid', $this->childId);
        $ChNotesModel = NotesChild::model()->find($criteria);

        $ClassMedia = array();
        if ($ChNotesModel){
            $criteria = new CDbCriteria();
            $criteria->compare('t.classid', $this->classId);
            $criteria->compare('t.yid', $this->yId);
            $criteria->compare('t.weeknum', $this->weekNum);
//            $criteria->compare('t.childid', array(0, $this->childId));
            $criteria->compare('t.category', 'week');
            $criteria1 = new CDbCriteria();
            $criteria1->compare('t.childid', 0);
            $criteria1->compare('t.childid', $this->childId, false, 'OR');
            $criteria->mergeWith($criteria1);
            $criteria->order='t.weight asc,t.id asc';
            $ClassMedia=ChildMediaLinks::model()->with('photoInfo')->findAll($criteria);
        }

        $this->render('childreport', array('ChNotesModel'=>$ChNotesModel, 'ClassMedia'=>$ClassMedia));
	}
	
}