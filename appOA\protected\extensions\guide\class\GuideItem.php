<?php

class GuideItem{
    //欢迎页
    public static function welcomeGuide($next = null, $prev = null){
        $guideItem = array(
            'selector'=>'#oldversion',
            'caption'=>'welcome',
            'style'=>array(
                'arrow'=>array(
                    'direction'=>'upright',
                    'top'=>-5,
                    'left'=>-60,
                ),
                'mainbox'=>array(
                    'pos'=>'arrow',
                    'leftOffset'=>-70,
                    'top'=>50,
                ),				
            ),
            'next'=>$next,
            'prev'=>$prev,
        );
        
        if (is_null($next))
            unset($guideItem['next']);
        if (is_null($prev))
            unset($guideItem['prev']);
        
        return $guideItem;
    
    }
    
    
}