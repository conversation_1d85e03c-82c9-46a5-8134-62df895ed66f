
<div  id='container' v-cloak>
    <div>
        <div v-for='(list,index) in listData'>
            <div class='flex align-items approvalTitle mb20'>
                <div class='line'></div>
                <div class='flex1 ml8'>{{list.group_title}}</div>
            </div>
            <div class='row '>
                <div class='col-md-2 relative' v-for='(item,i) in list.list'>
                    <div class='monList' @click='href_list(item)'>
                        <div class='font16 color3 fontBorder month'>{{item.month}}<span class='font15'>月</span></div>
                        <div class='settled' v-if='item.status==1'><span class='el-icon-success'></span> 已结算</div>
                        <div class='startSettled'  v-if='item.status==0'><span class='el-icon-question'></span> 待结算</div>
                        <div class='notStart'  v-if='item.status==-1'><span class='el-icon-warning'></span> 未开始</div>
                        <div class='font12 color6 mt3' v-if='item.archive_time!=""'>{{item.archive_time}}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div>

    </div>
</div>

<script>
const year = new Date().getFullYear();
const month = new Date().getMonth()+1;
const day = new Date().getDate();
var yearData = <?php echo CJSON::encode($yearList) ?>;
var yearList = yearData.list.map((val,index,arr)=>{
	let json = {}
	json.title= val.value
	json.key = val.key+''
	return json
})
var container=new Vue({
    el: "#container",
    data: {
        schoolList:[],
        schoolId:'',
        listData:[]
    },
    created: function() {
        this.getList()
    },
    methods: {
        getList(){
            let that=this
            $.ajax({
                url: '<?php echo $this->createUrl("archiveList") ?>',
                type: "post",
                dataType: 'json',
                data: {
                },
                success: function(data) {
                    if (data.state == 'success') {
                        that.listData=data.data
                    }else{
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    }
                },
                error: function(data) {
                    resultTip({
                        error: 'warning',
                        msg: data.message
                    });
                },
            })
        },
        href_list(item){
            if(item.status!=-1){
                window.location.href =item.url;
            }
        }
    }
})
   
</script>

<style>
   .approvalTitle .line{
        width: 6px;
        height: 16px;
        background: #4D88D2;
        border-radius: 1px;
    }
    .approvalTitle .flex1{
        font-size:16px;
        color:#333333;
        font-weight:500;
        line-height:16px
    }
    .monList{
        width: 100%;
        height: 90px;
        background: #FFFFFF;
        border-radius: 4px;
        border: 1px solid #E8EAED;
        padding:16px;
        text-align:center;
        margin-bottom:16px;
        cursor: pointer;
    }
    .settled{
        color:#5CB85C;
        font-size:14px
    }
    .startSettled{
        color:#F0AD4E;
        font-size:14px
    }
    .notStart{
        color:#ccc;
        font-size:14px;
    }
    .month{
        font-family: 'Roboto', sans-serif;
        margin-bottom:4px
    }
    .fontBorder{
        font-weight:bolder
    }
    .mt3{
        margin-top:3px
    }
    .font15{
        font-size:15px
    }
</style>