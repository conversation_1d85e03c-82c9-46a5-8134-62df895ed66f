<?php

/**
 * This is the model class for table "ivy_asa_course_schedule".
 *
 * The followings are the available columns in table 'ivy_asa_course_schedule':
 * @property string $course_schedule_id
 * @property integer $program_id
 * @property integer $course_id
 * @property integer $weekday
 * @property string $time_start
 * @property string $time_end
 * @property integer $status
 * @property string $updated
 * @property string $updated_by
 */
class AsaCourseSchedule extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_asa_course_schedule';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('course_schedule_id, program_id, course_id, weekday, time_start, time_end,updated, updated_by', 'required'),
			array('program_id, course_id, weekday, status', 'numerical', 'integerOnly'=>true),
			array('course_schedule_id, time_start, time_end, updated, updated_by', 'length', 'max'=>255),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('course_schedule_id, program_id, course_id, weekday, time_start, time_end, status, updated, updated_by', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
			'course' => array(self::BELONGS_TO, 'AsaCourse', 'course_id'),
			'attendance' => array(self::HAS_MANY, 'AsaStaffAttendance', array('course_schedule_id'=>'course_schedule_id')),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'course_schedule_id' => 'Course Schedule',
			'program_id' => 'Program',
			'course_id' => 'Course',
			'weekday' => Yii::t('Weekday', '开始日期'),
			'time_start' => 'Time Start',
			'time_end' => 'End Time',
			'status' => 'Status',
			'updated' => 'Updated',
			'updated_by' => 'Updatby',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('course_schedule_id',$this->course_schedule_id,true);
		$criteria->compare('program_id',$this->program_id);
		$criteria->compare('course_id',$this->course_id);
		$criteria->compare('weekday',$this->weekday);
		$criteria->compare('time_start',$this->time_start,true);
		$criteria->compare('time_end',$this->time_end,true);
		$criteria->compare('status',$this->status);
		$criteria->compare('updated',$this->updated,true);
		$criteria->compare('updated_by',$this->updated_by,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return AsaCourseSchedule the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}
