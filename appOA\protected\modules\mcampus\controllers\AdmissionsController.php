<?php

class AdmissionsController extends BranchBasedController
{
    public $dialogWidth = 500;
    public $cfgs = array();
    public $calenders = array();
    public $startyear = array();
    public $printFW = array();
    public $parents = array();

    // 访问action的初级权限
    public $actionAccessAuths = array(
        'index'             => 'o_A_Access',
        'targetDate'        => 'o_A_Access',
        'print'             => 'o_A_Access',
        'visitsLog'         => 'o_A_Access',
        'visitAview'        => 'o_A_Adm_Visits',
        'confirmVisit'      => 'o_A_Adm_Visits',
        'transfer'          => 'o_A_Adm_Visits',
        'visitsConfig'      => 'o_A_Adm_Visits',
        'interview'         => 'o_A_Adm_Visits',
        'traceLog'          => array('access'=>'o_A_Access', 'admin'=>'o_A_Adm_Visits'),
        'basicUpdate'       => array('access'=>'o_A_Access', 'admin'=>'o_A_Adm_Visits'),
        'saveRecord'        => array('access'=>'o_A_Access', 'admin'=>'o_A_Adm_Visits'),
        'visitUpdate'       => array('access'=>'o_A_Access', 'admin'=>'o_A_Adm_Visits'),
        'visitchangedate'   => array('access'=>'o_A_Access', 'admin'=>'o_A_Adm_Visits'),
        'visitAview'        => array('access'=>'o_A_Access', 'admin'=>'o_A_Adm_Visits'),
    );

    public function createUrl($route, $params = array(), $ampersand = '&', $parentOnly = false)
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        return parent::createUrl($route, $params, $ampersand);
    }

    public function init(){
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        Yii::import('common.models.visit.*');
        Yii::import('common.models.attendance.*');

        $this->modernMenuFlag = 'campusOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site','Campus Workspace');

        $this->branchSelectParams['urlArray'] = array('//mcampus/admissions/trans');

        // jquery ui
        $cs = Yii::app()->clientScript;
        $cs->registerCoreScript('jquery.ui');
        $cs->registerCssFile($cs->getCoreScriptUrl().'/jui/css/base/jquery-ui.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/jquery.jPrintArea.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/xlsx.full.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/vue2.js');

    }

    public function actionSelect(){
        $this->render('//layouts/common/branchSelect');
    }

    /**
     * 录取学生转入
     */
    public function actionTrans()
    {
        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/vue2.js');

        $calendarYids = $this->getCalendars();
        $startyear = $this->calendarStartYear[$calendarYids['currentYid']];
        $preyear = $startyear - 1;
        $nextyear = $startyear + 1;
        $yearList = array($startyear, $nextyear, $preyear);
        $config = $this->getConfig();
        Yii::import('common.models.invoice.DiscountSchool');
        Yii::import('common.models.invoice.DiscountCategory');

        $this->render('admissions/trans', array(
            'yearList' => $yearList,
            'config' => $config,
            'discountList' => DiscountSchool::getDiscountList($this->branchId)
        ));
    }

    public function actionApplyData()
    {
        $startyear = Yii::app()->request->getParam('startyear');

        $this->addMessage('state', 'fail');
        if (!$startyear) {
            $this->addMessage('message', '学年不能为空');
            $this->showMessage();
        }
        $config = $this->getConfig();
        $applyCampus = isset($config['campus'][$this->branchId]) ? $config['campus'][$this->branchId] : 0;
        $finalResult = array_keys($config['finalResult']); // 录取, 有条件录取
        $res = CommonUtils::requestApply("studentList/{$applyCampus}/{$startyear}", $finalResult, $this->branchId);
        if ($res['code'] != 0) {
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
        $applyIdList = array();
        foreach ($res['data'] as $item) {
            $applyIdList[] = $item['applyId'];
        }
        // 查找已转入的信息
        Yii::import('common.models.easyapply.EasyapplyTransfer');
        $crit = new CDbCriteria();
        $crit->compare('apply_id', $applyIdList);
        $crit->index = 'apply_id';

        $transData = array();
        $unTransData = array();

        $transferModel = EasyapplyTransfer::model()->findAll($crit);
        foreach ($res['data'] as $key => $item) {
            $array = $res['data'][$key];

            if (isset($transferModel[$item['applyId']])) {
                $transChildId = $transferModel[$item['applyId']]->child_id;
                $transTime = $transferModel[$item['applyId']]->updated_at;
                $transDate = date("Y-m-d H:i", $transferModel[$item['applyId']]->updated_at);
                $array['trans_childid'] = $transChildId;
                $array['trans_date'] = $transDate;
                $array['trans_time'] = $transTime;
                $transData[$transTime] = $array;
            } else {
                $array['trans_childid'] = '';
                $array['trans_date'] = '';
                $array['trans_time'] = '';
                $unTransData[] = $array;
            }
        }
        krsort($transData);
        $transData = array_values($transData);

        $this->addMessage('state', 'success');
        $this->addMessage('data', array_merge($unTransData, $transData));
        $this->showMessage();
    }

    public function actionApplyOne()
    {
        $applyId = Yii::app()->request->getParam('applyId');

        $this->addMessage('state', 'fail');
        if (!$applyId) {
            $this->addMessage('message', '申请ID不能为空');
            $this->showMessage();
        }

        $res = CommonUtils::requestApply("student/{$applyId}", array(), $this->branchId);
        if ($res['code'] != 0) {
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
        // 处理数据
        $resData = $res['data'];
        if ($resData['guardianList'][0]) {
            if ($resData['guardianList'][0]['gender'] == 1) {
                $fGuardian = $resData['guardianList'][0];
                if (isset($resData['guardianList'][1])) {
                    $mGuardian = $resData['guardianList'][1];
                }
            } else {
                $mGuardian = $resData['guardianList'][0];
                if (isset($resData['guardianList'][1])) {
                    $fGuardian = $resData['guardianList'][1];
                }
            }
        }
        $config = $this->getConfig();
        $lang = isset($config['lang'][$resData['language']]) ? $config['lang'][$resData['language']] : 0;
        $country = isset($config['country'][$resData['country']]) ? $config['country'][$resData['country']] : 0;
        // 中国籍
        if (in_array($country, array(36, 175))) {
            $legel = 1;
            $name_cn = "";
            $first_name_en = "";
            $middle_name_en = "";
            $last_name_en = "";
            if ($resData['nameCn']) {
                $name_cn = $resData['nameCn'];
                $splitTool = Yii::createComponent('common.extensions.pinyin.Utf8ToPinyin');
                $pinyinTool = Yii::createComponent('common.components.pinyin.PinyinTool');

                $nameArray = $splitTool->splitName($resData['nameCn']);
                if (isset($nameArray[0])) {
                    // 法定姓拼音
                    $last_name_en = ucfirst(implode($pinyinTool->tool->name($nameArray[0])));
                }
                if (isset($nameArray[1])) {
                    // 法定名拼音
                    $first_name_en = ucfirst(implode($pinyinTool->tool->name($nameArray[1])));
                }
            }
        } else {
            $legel = 2;
            $name_cn = $resData['nameCn'];
            $first_name_en = $resData['firstName'];
            $middle_name_en = $resData['middleName'];
            $last_name_en = $resData['lastName'];
        }
        $childData = array(
            "apply_year" => $resData['applyYear'],
            "schoolid" => $this->branchId,
            'classid' => 0,
            'discount_id' => 0,
            "is_legel_cn_name" => $legel,
            "name_cn" => $name_cn,
            "first_name_en" => $first_name_en,
            "middle_name_en" => $middle_name_en,
            "last_name_en" => $last_name_en,
            "nick" => $resData['firstName'],

            "child_avatar" => str_replace('!w200', '!w800', $resData['photo']),
            "child_avatar_thumb" => str_replace('!w200', '!w80', $resData['photo']),
            "start_year" => $resData['applyYear'],
            "start_date" => "",
            "gender" => $resData['gender'],
            "birthday" => $resData['birthday'],
            "country" => isset($config['country'][$resData['country']]) ? $config['country'][$resData['country']] : 0,
            "lang" => $lang,
            "identity" => $resData['identity'],
            "home_address" => $resData['homeAddress'],
            "photo_authorization" => isset($resData['photoAuthorization']) ? $resData['photoAuthorization'] : 0,

            "father_email" => isset($fGuardian['email']) ? $fGuardian['email'] : "",
            "father_phone" => isset($fGuardian['mobile']) ? $fGuardian['mobile'] : "",
            "father_name" => isset($fGuardian['name']) ? $fGuardian['name'] : "",
            "father_nation" => isset($config['country'][$fGuardian['nationality']]) ? $config['country'][$fGuardian['nationality']] : "",
            "father_employer" => isset($fGuardian['employer']) ? $fGuardian['employer'] : "",
            "father_position" => isset($fGuardian['position']) ? $fGuardian['position'] : "",

            "mother_email" => isset($mGuardian['email']) ? $mGuardian['email'] : "",
            "mother_phone" => isset($mGuardian['mobile']) ? $mGuardian['mobile'] : "",
            "mother_name" => isset($mGuardian['name']) ? $mGuardian['name'] : "",
            "mother_nation" => isset($config['country'][$mGuardian['nationality']]) ? $config['country'][$mGuardian['nationality']] : "",
            "mother_employer" => isset($mGuardian['employer']) ? $mGuardian['employer'] : "",
            "mother_position" => isset($mGuardian['position']) ? $mGuardian['position'] : "",
        );
        // 根据申请学年查找班级列表
        $startYear = $resData['applyYear'];
        Yii::import('common.models.calendar.CalendarSchool');
        $calendarModel = CalendarSchool::model()->findByAttributes(array('branchid' => $this->branchId, 'startyear' => $startYear));
        if (!$calendarModel) {
            $this->addMessage('message', "未找到{$startYear}年的校历");
            $this->showMessage();
        }
        $yid = $calendarModel->yid;
        $classModelList = IvyClass::getClassList($this->branchId, $yid);
        $classList = array();
        foreach ($classModelList as $classModel) {
            $classList[] = array(
                'classid' => $classModel->classid,
                'title' => $classModel->title,
            );
        }

        $guardianEmailList = array();
        $guardianMphoneList = array();
        foreach ($resData['guardianList'] as $guardian) {
            $guardianEmailList[] = $guardian['email'];
            $guardianMphoneList[] = $guardian['mobile'];
        }
        // 根据父母邮件查找已存在用户

        // 查找孩子信息
        $slibingData = $this->getSlibingData($guardianEmailList, $guardianMphoneList);
        // 语言、国籍字典项
        $langList = Term::model()->getLangList();
        $countryList = Country::model()->getData();
        
        $this->addMessage('state', 'success');
        $this->addMessage('data', array(
            'classList' => $classList, 
            'slibingData' => $slibingData, 
            'childData' => $childData, 
            // 'applyData' => $resData,
            'configList' => array(
                'lang' => $langList,
                'country' => $countryList,
            ),
        ));
        $this->showMessage();
    }

    public function actionApplyInfo()
    {
        $childId = Yii::app()->request->getParam('childId');
        $this->addMessage('state', 'fail');
        if (!$childId) {
            $this->addMessage('message', 'childId不能为空');
            $this->showMessage();
        }
        // 查找已转入的信息
        Yii::import('common.models.easyapply.EasyapplyTransfer');
        $crit = new CDbCriteria();
        $crit->compare('child_id', $childId);

        $transferModel = EasyapplyTransfer::model()->find($crit);
        if (!$transferModel) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', array());
            $this->addMessage('message', '没有申请数据');
            $this->showMessage();
        }
        $applyId = $transferModel->apply_id;
        $res = CommonUtils::requestApply("applicationInfo/{$applyId}", array(), $this->branchId);
        if ($res['code'] != 0) {
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
        $this->addMessage('state', 'success');
        $this->addMessage('data', $res['data']);
        $this->showMessage();
    }

    public function actionPrivateFileLink()
    {
        $fileName = Yii::app()->request->getParam('filename');
        $style = Yii::app()->request->getParam('style');
        $data = array(
            'style' => $style,
        );
        $res = CommonUtils::requestApply("styleLink/{$fileName}", $data, $this->branchId);
        if ($res['code'] != 0) {
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
        $this->addMessage('state', 'success');
        $this->addMessage('data', $res['data']);
        $this->showMessage();
    }

    // 转移孩子动作
    public function actionTransChild()
    {
        // 判断是否关联兄弟姐妹
        $applyId = Yii::app()->request->getParam('applyId');
        $siblingId = Yii::app()->request->getParam('siblingId', 0);
        $childData = Yii::app()->request->getParam('childData');
        // 转入类型 overwrite （覆盖）， conventional （正常）
        $transferType = Yii::app()->request->getParam('transferType', 'conventional');

        $this->addMessage('state', 'fail');
        if (!$applyId) {
            $this->addMessage('message', '申请ID不能为空');
            $this->showMessage();
        }
        if (!$classId = $childData['classid']) {
            $classId = 0;
        }

        // 查找转移记录
        Yii::import('common.models.easyapply.EasyapplyTransfer');
        $transferModel = EasyapplyTransfer::model()->findByAttributes(array('apply_id' =>$applyId));
        if ($transferModel) {
            $this->addMessage('message', '此申请已经转移');
            $this->showMessage();
        } else {
            $transferModel = new EasyapplyTransfer();
        }

        if (empty($childData['first_name_en'])) {
            $this->addMessage('message', '法定名不能为空');
            $this->showMessage();
        }

        if (!$siblingId) {
            // 检查父母邮箱
            if(!$childData['father_email'] && !$childData['mother_email']){
                $this->addMessage('message', '父母邮箱至少需要一个');
                $this->showMessage();
            }
            if($childData['father_email'] == $childData['mother_email']){
                $this->addMessage('message', '招生系统父母邮箱不能相同');
                $this->showMessage();
            }

            $emailList = array();
            if($childData['father_email']){
                $emailList[] = $childData['father_email'];
                $criteria = new CDbCriteria;
                $criteria->compare('email', $childData['father_email']);
                $fModela = User::model()->count($criteria);
            }
            if($childData['mother_email']){
                $emailList[] = $childData['mother_email'];
                $criteria = new CDbCriteria;
                $criteria->compare('email', $childData['mother_email']);
                $mModelb = User::model()->count($criteria);
            }
            if($fModela > 0){
                $slibingData = $this->getSlibingData($emailList);
                $this->addMessage('message', '父亲邮箱已在系统存在');
                $this->addMessage('data', $slibingData);
                $this->showMessage();
            }
            if($mModelb > 0){
                $slibingData = $this->getSlibingData($emailList);
                $this->addMessage('message', '母亲邮箱已在系统存在');
                $this->addMessage('data', $slibingData);
                $this->showMessage();
            }
        }

        // 处理孩子头像
        if ($childData['child_avatar']) {
            $cfgs = OA::LoadConfig('CfgPhoto');

            $avatarUrl = $childData['child_avatar'];

            $params = $cfgs['childPhoto'];
            $avatarExt = strtolower(strrchr( $childData['child_avatar'] , '.' ));
            $avatarExt = str_replace('!w800', '', $avatarExt);
            $subPath = rtrim($params['subDir'], '/') . '/';
            $fileName = $params['filePrefix'] . uniqid() . $avatarExt;
            $filePath = $subPath . $fileName;

            $res = file_put_contents($filePath, file_get_contents($avatarUrl));
            if ($res) {
                // 上传文件到阿里云
                $childData['photo'] = $fileName;
            }else{
                $childData['photo'] = 'blank.gif';
            }
        } else {
            $childData['photo'] = 'blank.gif';
        }
        $childData['birthday'] = strtotime($childData['birthday']);
        $childData['start_date'] = strtotime($childData['start_date']);
        $transaction = Yii::app()->db->beginTransaction();
        try {
            // 创建学生账号
            $startDate = $childData['start_date'];
            if(!$startDate){
                if(intval($childData['start_year'])){
                    Yii::import('common.models.calendar.*');
                    $criteria = new CDbCriteria;
                    $criteria->compare('startyear', $childData['start_year']);
                    $criteria->compare('branchid', $this->branchId);
                    $calendarObj = CalendarSchool::model()->find($criteria);
                    $data = explode(',', $calendarObj->cTemplate->timepoints);
                    $startDate = $data[0];
                }
            }
            if ($transferType == "overwrite") {
                $childModel = ChildProfileBasic::model()->findByPk($siblingId);
                if (!$childModel) {
                    throw new Exception('需要覆盖的孩子ID错误');
                }
                if (date("Y-m-d", $childModel->birthday) != date("Y-m-d", $childData['birthday'])) {
                    throw new Exception('学生信息在 OA 和 招生系统中 生日 不匹配, 覆盖失败，请修改后重试');
                }
            } else {
                $childModel = new ChildProfileBasic();
            }
            $childModel->attributes = $childData;
            $childModel->processAvatar($childData['photo'], $childModel->photo);
            $childModel->admission_id = $applyId;
            $childModel->schoolid = $this->branchId;
            $childModel->classid = 0;
            $childModel->birthday_search = date("Y-m-d H:i:s", $childData['birthday']);
            $childModel->est_enter_date = $startDate;
            $childModel->updated_timestamp = time();
            $childModel->created_timestamp = time();
            $childModel->status = ChildProfileBasic::STATS_REGISTERED;
            $childModel->create_uid = Yii::app()->user->id;
            if (!$childModel->save()) {
                $error = current($childModel->getErrors());
                throw new Exception('保存孩子表失败：' . $error['0']);
            }
            $_fid = 0;
            $_mid = 0;
            // 创建家长账号
            if($siblingId){
                // 关联兄弟姐妹的情况，处理数据
                if ($transferType == "conventional") {
                    $sibModel = ChildProfileBasic::model()->findByPk($siblingId);
                    $_fid = $sibModel->fid;
                    $_mid = $sibModel->mid;
                    if($_fid){
                        $fModel = IvyParent::model()->findByPk($_fid);
                        if($fModel != null && is_object($fModel)){
                            $childrenArr = unserialize($fModel->childs);
                            if(!in_array($childModel->childid, $childrenArr)){
                                $childrenArr[] = $childModel->childid;
                                $fModel->childs = serialize($childrenArr);
                                $fModel->save();
                            }
                        }
                    }
                    if($_mid){
                        $mModel = IvyParent::model()->findByPk($_mid);
                        if($mModel != null && is_object($mModel)){
                            $childrenArr = unserialize($mModel->childs);
                            if(!in_array($childModel->childid, $childrenArr)){
                                $childrenArr[] = $childModel->childid;
                                $mModel->childs = serialize($childrenArr);
                                $mModel->save();
                            }
                        }
                    }
    
                    $childModel->fid = $_fid;
                    $childModel->mid = $_mid;
                    $childModel->family_id = $sibModel->family_id ? $sibModel->family_id : md5($sibModel->childid);
                    $childModel->seniority = $childModel->getSeniority($childModel->family_id);
                }
            }else{
                if($childData['father_email'] || $childData['mother_email']){
                    Yii::import('application.components.user.UserApi');
                    if($childData['father_email']){
                        $default_password = CommonUtils::randPass();
                        $pModel = new User();
                        $pModel->email = $childData['father_email'];
                        $pModel->name = $childData['father_name'];
                        $pModel->iniPassword = $pModel->pass = $default_password;
                        $pModel->mphone = $childData['father_phone'];
                        $fModel = UserApi::createParent($childModel->childid, $pModel, 1);
                        if ($fModel) {
                            $fModel->parent->cn_name = $childData['father_name'];
                            $fModel->parent->country = $childData['father_nation'];
                            $fModel->parent->company = $childData['father_employer'];
                            $fModel->parent->job = $childData['father_position'];
                            $fModel->parent->save();
                            $_fid = $fModel->parent->pid;
                        }
                    }

                    if($childData['mother_email']){
                        $default_password = CommonUtils::randPass();
                        $pModel = new User();
                        $pModel->email = $childData['mother_email'];
                        $pModel->name = $childData['mother_name'];
                        $pModel->iniPassword = $pModel->pass = $default_password;
                        $pModel->mphone = $childData['mother_phone'];
                        $mModel = UserApi::createParent($childModel->childid, $pModel, 2);
                        if ($mModel) {
                            $mModel->parent->cn_name = $childData['mother_name'];
                            $mModel->parent->country = $childData['mother_nation'];
                            $mModel->parent->company = $childData['mother_employer'];
                            $mModel->parent->job = $childData['mother_position'];
                            $mModel->parent->save();
                            $_mid = $mModel->parent->pid;
                        }
                    }
                    $childModel->fid = $_fid;
                    $childModel->mid = $_mid;
                }
            }
            if (!$childModel->family_id) {
                $childModel->family_id = md5($childModel->childid);
            }
            $childModel->save();

            // 同步照片授权
            if(isset($childData['photo_authorization'])){
                Yii::import('common.models.child.ChildMisc');
                $childMiscModel = ChildMisc::model()->findByPk($childModel->childid);
                if(!$childMiscModel){
                    $childMiscModel = new ChildMisc();
                    $childMiscModel->childid = $childModel->childid;
                }
                $childMiscModel->photo_auth = $childData['photo_authorization'];
                $childMiscModel->save();
            }
            // 添加同步孩子搜索资料表
            $childSyncModel = ChildSync::model()->findByPk($childModel->childid);
            if(!$childSyncModel){
                $childSyncModel = new ChildSync();
            }
            $childSyncModel->childid = $childModel->childid;
            $childSyncModel->flag = 1;
            $childSyncModel->timestamp = time();
            if(!$childSyncModel->save()){
                throw new Exception('ChildSync Save Fail');
            }
            // 添加分班记录
            if ($classId > 0) {
                Yii::import('common.components.policy.*');
                Yii::import('common.models.portfolio.*');
                Yii::import('common.models.invoice.ChildReserve');
                if (!PolicyApi::assignClass(array($classId), $childModel->childid)) {
                    throw new Exception('分班失败');
                }
            }
            // 添加家庭住址
            if ($childData['home_address']) {
                Yii::import('common.models.child.HomeAddress');
                $homeModel = HomeAddress::model()->findByAttributes(array('childid' => $childModel->childid));
                if (!$homeModel) {
                    $homeModel = new HomeAddress();
                }
                $homeModel->attributes = array();
                $homeModel->childid = $childModel->childid;
                $homeModel->en_address = $childData['home_address'];
                $homeModel->fid = $_fid;
                $homeModel->mid = $_mid;
                $homeModel->family_id = $childModel->family_id;
                if(!$homeModel->save()){
                   throw new Exception("保存地址失败");
                }
            }
            // 绑定孩子折扣
            if(isset($childData['discount_id']) && $childData['discount_id'] > 0) {
                Yii::import('common.models.invoice.ChildDiscountLink');
                $childDiscountLink = new ChildDiscountLink();
                $childDiscountLink->childid = $childModel->childid;
                $childDiscountLink->discount_id = $childData['discount_id'];
                $childDiscountLink->operation_id = 0;
                $childDiscountLink->memo = "转入学生绑定折扣";
                $childDiscountLink->status = 1;
                $childDiscountLink->update_user = $this->staff->uid;
                $childDiscountLink->update_timestamp = time();
                $childDiscountLink->save();
            }
            // 添加转移记录
            $transferModel->apply_id = $applyId;
            $transferModel->apply_year = $childData['apply_year'];
            $transferModel->child_id = $childModel->childid;
            $transferModel->class_id = $classId;
            $transferModel->apply_data = json_encode($childData);
            $transferModel->sibling_id = $siblingId;
            $transferModel->discount_id = isset($childData['discount_id']) ? $childData['discount_id'] : 0;
            $transferModel->status = 1;
            $transferModel->updated_by = $this->staff->uid;
            $transferModel->updated_at = time();
            if (!$transferModel->save()) {
                $error = current($transferModel->getErrors());
                throw new Exception('保存转移记录失败：' . $error['0']);
            }
            $transaction->commit();
            $this->addMessage('state', 'success');
            $this->showMessage();
        } catch(Exception $e){
            $transaction->rollBack();
            $this->addMessage('message', $e->getMessage());
            $this->showMessage();
        }

    }

    public function actionInvoice()
    {
        $applyId = Yii::app()->request->getParam('applyId');

        $this->addMessage('state', 'fail');
        if (!$applyId) {
            $this->addMessage('message', '申请ID不能为空');
            $this->showMessage();
        }
        // 查找转移记录
        Yii::import('common.models.easyapply.*');
        Yii::import('common.components.policy.*');
        $transModel = EasyapplyTransfer::model()->findByAttributes(array('apply_id' => $applyId));
        if (!$transModel) {
            $this->addMessage('message', '未找到');
            $this->showMessage();
        }
        $res = CommonUtils::requestApply("payinfo/{$applyId}", array(), $this->branchId);
        if ($res['code'] != 0) {
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
        // 获取折扣列表
        $schoolid = $this->branchId;
        $schoolyear = $transModel->apply_year;
        $childid = $transModel->child_id;
        $policy = new IvyPolicy("pay", $schoolyear, $schoolid);
        $discounts = PolicyApi::getDiscountList($schoolid);
        $bind_dis = $policy->getBindDiscount($childid);
        if (is_array($bind_dis) && $bind_dis){
            $discounts = array($bind_dis['discoundId']['id']=>CommonUtils::autoLang($bind_dis['discoundId']['title_cn'], $bind_dis['discoundId']['title_en']))+$discounts;
        }

        $data = $res['data'];
        $transedInvoice = array();
        $unTransInvoice = array();
        $tradeIds = array();
        $paidItems = array();
        // 支付记录
        if (isset($data['invoice']['paidItems']) && $data['invoice']['paidItems']) {
            $paidItems = $data['invoice']['paidItems'];
        }
        // 查找转移记录
        $transInvoiceModel = EasyapplyInvoice::model()->findByAttributes(array('apply_id' => $applyId));
        if ($transInvoiceModel) {
            $oaInvoice = Invoice::model()->findByPk($transInvoiceModel->invoiceid_oa);
            $transedInvoice = array(
                'title' => $oaInvoice->title,
                'amount' => $oaInvoice->amount,
                'link' => $this->createUrl("//child/invoice/viewInvoice", array('invoiceid'=>$oaInvoice->invoice_id, 'childid'=>$oaInvoice->childid)),
            );
            $tradeIds = json_decode($transInvoiceModel->trade_ids);

        } else {
            $unTransInvoice = $data['options'];
        }
        // 支付明细是否转移
        foreach ($paidItems as $k => $paidItem) {
            $paidItems[$k]['status'] = 0;
            if (in_array($paidItem['tradeId'], $tradeIds)) {
                $paidItems[$k]['status'] = 1;
            }
        }

        $returnData = array(
            'discounts' => $discounts,
            'unTransInvoice' => $unTransInvoice,
            'transedInvoice' => $transedInvoice,
            'paidItems' => $paidItems,
        );
        $this->addMessage('state', 'success');
        $this->addMessage('data', $returnData);
        $this->showMessage();
    }

    /**
     * 生成账单
     *
     * @return void
     */
    public function actionTransInvoice()
    {
        Yii::import('common.models.invoice.*');
        Yii::import('common.models.calendar.*');
        Yii::import('common.components.policy.*');
        Yii::import('common.components.RC');
        Yii::import('common.models.easyapply.EasyapplyTransfer');
        Yii::import('common.models.easyapply.EasyapplyInvoice');

        $applyId = Yii::app()->request->getParam('applyId');
        $amount = Yii::app()->request->getParam('amount');
        $type = Yii::app()->request->getParam('type');
        $discountId = Yii::app()->request->getParam('discountId', 0);

        $this->addMessage('state', 'fail');
        if (!$applyId) {
            $this->addMessage('message', '申请ID不能为空');
            $this->showMessage();
        }
        if (!$amount || !$type) {
            $this->addMessage('message', '参数错误');
            $this->showMessage();
        }
        
        $transInvoiceModel = EasyapplyInvoice::model()->findByAttributes(array('apply_id' => $applyId));
        if ($transInvoiceModel) {
            $this->addMessage('message', '账单已生成');
            $this->showMessage();
        }
        $transModel = EasyapplyTransfer::model()->findByAttributes(array('apply_id' => $applyId));
        if (!$transModel) {
            $this->addMessage('message', '未找到');
            $this->showMessage();
        }
        // 生成账单
        $res = $transModel->generateInvoice($amount, $type, $discountId);
        if ($res['state'] == 'fail') {
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
        // 生成订单
        $res = CommonUtils::requestApply("payinfo/{$applyId}", array(), $this->branchId);
        if ($res['code'] != 0) {
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
        $data = $res['data'];
        $paidItems = $data['invoice']['paidItems'];
        if ($paidItems) {
            $eaInvoiceModel = EasyapplyInvoice::model()->findByAttributes(array('apply_id' => $applyId));
            $oaInvoiceModel = Invoice::model()->findByPk($eaInvoiceModel->invoiceid_oa);
            $res = $transModel->generateOrder($data['invoice']['paidItems'], $oaInvoiceModel, $eaInvoiceModel, $this->branchId);
            $this->addMessage('state', $res['state']);
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
        $this->addMessage('state', 'success');
        $this->addMessage('message', 'success');
        $this->showMessage();
    }

    /**
     * 同步订单
     */
    public function actionSyncOrder()
    {
        Yii::import('common.models.easyapply.EasyapplyTransfer');
        Yii::import('common.models.easyapply.EasyapplyInvoice');
        Yii::import('common.models.invoice.Invoice');
        $applyId = Yii::app()->request->getParam('applyId');
        $transModel = EasyapplyTransfer::model()->findByAttributes(array('apply_id' => $applyId));
        if (!$transModel) {
            $this->addMessage('message', '未找到');
            $this->showMessage();
        }
        $res = CommonUtils::requestApply("payinfo/{$applyId}", array(), $this->branchId);
        if ($res['code'] != 0) {
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
        $data = $res['data'];
        $paidItems = $data['invoice']['paidItems'];
        if ($paidItems) {
            $eaInvoiceModel = EasyapplyInvoice::model()->findByAttributes(array('apply_id' => $applyId));
            $oaInvoiceModel = Invoice::model()->findByPk($eaInvoiceModel->invoiceid_oa);
            if ($oaInvoiceModel->status == Invoice::STATS_PAID) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '账单已付清');
                $this->showMessage();
            }
            $res = $transModel->generateOrder($data['invoice']['paidItems'], $oaInvoiceModel, $eaInvoiceModel, $this->branchId);
            $this->addMessage('state', $res['state']);
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }

    public function getConfig()
    {
        if (CommonUtils::isProduction()) {
            return $this->getProductionConfig();
        } else {
            return array(
                'campus' => array(
                    'BJ_DS' => 2,
                    'BJ_SLT' => 1, 
                    'BJ_QFF' => 3,
                    'BJ_CP' => 67,
                    'BJ_IASLT' => 60,
                    'BJ_OE' => 62,
                    'BJ_OG-PH' => 71,
                    'TJ_EB' => 66,
                    'CD_LT' => 68,
                    'CD_LH' => 72,
                    'NB_HH' => 70,
                    'NB_FJ' => 69,
                    'XA_GJ' => 73,
                    'XA_LB' => 74,
                ),
                'finalResult' => array(200 => '已录取', 210 => '有条件录取'),
                'gender' => array(1 => '男', 2 => '女'),
                'class' => array(
                    6 => "Nursery",
                    7 => "Prek1[[@]]Prek1",
                    8 => "Prek2[[@]]Prek2",
                    10 => "Kindergarten",
                    9 => "Casa[[@]]Casa",
                    11 => "1年级[[@]]Grade 1",
                    12 => "2年级[[@]]Grade 2 ",
                    13 => "3年级[[@]]Grade 3",
                    14 => "4年级[[@]]Grade 4 ",
                    15 => "5年级[[@]]Grade 5",
                    16 => "6年级[[@]]Grade 6",
                    17 => "双语7年级[[@]]双语7年级",
                    18 => "双语8年级[[@]]双语8年级",
                    19 => "双语9年级[[@]]双语9年级",
                    20 => "IGB 10年级[[@]]IGB 10年级",
                    21 => "IGB 11年级[[@]]IGB 11年级",
                    22 => "IGB 12年级[[@]]IGB 12年级",
                    335 => "国际班 10年级[[@]]国际班 10年级",
                    336 => "国际班 11年级[[@]]国际班 11年级",
                    337 => "国际班 12年级[[@]]国际班 12年级",
                    252 => "小班7年级[[@]]小班7年级",
                    253 => "小班8年级[[@]]小班8年级",
                    251 => "小班9年级[[@]]小班9年级",
                    341 => "IGB[[@]]IGB",
                ),
                'lang' => array(
                    120 => 1, // 中文
                    121 => 2, // 英语
                    124 => 156, // 中英双语
                    156 => 65, // 法语
                    232 => 71, // 德语
                    255 => 68, // 韩语
                    266 => 67, // 日语
                    243 => 69, // 意大利语
                ),
                'country' => array(
                    // 'China' => 36,
                    // 'China(HKMacauTW)' => 175,
                    // 'Canada' => 31,
                    // 'Japan' => 86,
                    // 'UnitedKingdom' => 187,
                    // 'UnitedStates' => 188,
                    'Afghanistan' => 1,
                    'Albania' => 2,
                    'Algeria' => 3,
                    'Andorra' => 4,
                    'Angola' => 5,
                    'AntiguaandBarbuda' => 6,
                    'Argentina' => 7,
                    'Armenia' => 8,
                    'Australia' => 9,
                    'Austria' => 10,
                    'Azerbaijan' => 11,
                    'Bahamas' => 12,
                    'Bahrain' => 13,
                    'Bangladesh' => 14,
                    'Barbados' => 15,
                    'WhiteRussia' => 16, // Belarus
                    'Belgium' => 17,
                    'Belize' => 18,
                    'Benin' => 19,
                    'Bhutan' => 20,
                    'Bolivia' => 21,
                    'BosniaandHerzegovina' => 22,
                    'Botswana' => 23,
                    'Brazil' => 24,
                    'Brunei' => 25,
                    'Bulgaria' => 26,
                    'BurkinaFaso' => 27,
                    'Burundi' => 28,
                    'Cambodia' => 29,
                    'Cameroon' => 30,
                    'Canada' => 31,
                    'CapeVerde' => 32,
                    'CentralAfricanRepublic' => 33,
                    'Chad' => 34,
                    'Chile' => 35,
                    'China' => 36,
                    'Colombia' => 37,
                    'Comoros' => 38,
                    'Congo' => 39,
                    'Congo(DRC)' => 40,
                    'CookIslands' => 41,
                    'CostaRica' => 42,
                    'Coted\'Ivoire' => 43,
                    'Croatia' => 44,
                    'Cuba' => 45,
                    'Cyprus' => 46,
                    'CzechRepublic' => 47,
                    'Denmark' => 48,
                    'Djibouti' => 49,
                    'Dominica' => 50,
                    'DominicanRepublic' => 51,
                    'Ecuador' => 52,
                    'Egypt' => 53,
                    'ElSalvador' => 54,
                    'EquatorialGuinea' => 55,
                    'Eritrea' => 56,
                    'Estonia' => 57,
                    'Ethiopia' => 58,
                    'FijiIslands' => 59,
                    'Finland' => 60,
                    'France' => 61,
                    'Gabon' => 62,
                    'Gambia' => 63,
                    'Gaza' => 64,
                    'Georgia' => 65,
                    'Germany' => 66,
                    'Ghana' => 67,
                    'Greece' => 68,
                    'Grenada' => 69,
                    'Guatemala' => 70,
                    'Guinea' => 71,
                    'Guinea-Bissau' => 72,
                    'Guyana' => 73,
                    'Haiti' => 74,
                    'Honduras' => 75,
                    'Hungary' => 76,
                    'Iceland' => 77,
                    'India' => 78,
                    'Indonesia' => 79,
                    'Iran' => 80,
                    'Iraq' => 81,
                    'Ireland' => 82,
                    'Israel' => 83,
                    'Italy' => 84,
                    'Jamaica' => 85,
                    'Japan' => 86,
                    'Jordan' => 87,
                    'Kazakhstan' => 88,
                    'Kenya' => 89,
                    'Kiribati' => 90,
                    'Kosovo' => 91,
                    'Kuwait' => 92,
                    'Kyrgyzstan' => 93,
                    'Laos' => 94,
                    'Latvia' => 95,
                    'Lebanon' => 96,
                    'Lesotho' => 97,
                    'Liberia' => 98,
                    'Libya' => 99,
                    'Liechtenstein' => 100,
                    'Lithuania' => 101,
                    'Luxembourg' => 102,
                    'MacedoniaFormerYugoslavRepublicof' => 103,
                    'Madagascar' => 104,
                    'Malawi' => 105,
                    'Malaysia' => 106,
                    'Maldives' => 107,
                    'Mali' => 108,
                    'Malta' => 109,
                    'MarshallIslands' => 110,
                    'Mauritania' => 111,
                    'Mauritius' => 112,
                    'Mexico' => 113,
                    'Micronesia' => 114,
                    'Moldova' => 115,
                    'Monaco' => 116,
                    'Mongolia' => 117,
                    'Montenegro' => 118,
                    'Morocco' => 119,
                    'Mozambique' => 120,
                    'Myanmar' => 121,
                    'Namibia' => 122,
                    'Nauru' => 123,
                    'Nepal' => 124,
                    'Netherlands' => 125,
                    'NewZealand' => 126,
                    'Nicaragua' => 127,
                    'Niger' => 128,
                    'Nigeria' => 129,
                    'Niue' => 130,
                    'NorthKorea' => 131,
                    'Norway' => 132,
                    'Oman' => 133,
                    'Pakistan' => 134,
                    'Palau' => 135,
                    'Panama' => 136,
                    'PapuaNewGuinea' => 137,
                    'Paraguay' => 138,
                    'Peru' => 139,
                    'Philippines' => 140,
                    'Poland' => 141,
                    'Portugal' => 142,
                    'PuertoRico' => 143,
                    'Qatar' => 144,
                    'Reunion' => 145,
                    'Romania' => 146,
                    'Russia' => 147,
                    'Rwanda' => 148,
                    'St.KittsandNevis' => 149,
                    'St.Lucia' => 150,
                    'St.VincentandtheGrenadines' => 151,
                    'Samoa' => 152,
                    'SanMarino' => 153,
                    'SaoTomeandPrincipe' => 154,
                    'SaudiArabia' => 155,
                    'Senegal' => 156,
                    'Serbia' => 157,
                    'Seychelles' => 158,
                    'SierraLeone' => 159,
                    'Singapore' => 160,
                    'Slovakia' => 161,
                    'Slovenia' => 162,
                    'SolomonIslands' => 163,
                    'Somalia' => 164,
                    'SouthAfrica' => 165,
                    'Korea' => 166, // South Korea
                    'Spain' => 167,
                    'SriLanka' => 168,
                    'Sudan' => 169,
                    'Suriname' => 170,
                    'Swaziland' => 171,
                    'Sweden' => 172,
                    'Switzerland' => 173,
                    'Syria' => 174,
                    'China(HKMacauTW)' => 175,
                    'Tajikistan' => 176,
                    'Tanzania' => 177,
                    'Thailand' => 178,
                    'Timor-Leste' => 179,
                    'Togo' => 180,
                    'Tonga' => 181,
                    'TrinidadandTobago' => 182,
                    'Tunisia' => 183,
                    'Turkey' => 184,
                    'Turkmenistan' => 185,
                    'Tuvalu' => 186,
                    'UnitedKingdom' => 187,
                    'UnitedStates' => 188,
                    'Uganda' => 189,
                    'Ukraine' => 190,
                    'UnitedArabEmirates' => 191,
                    'Uruguay' => 192,
                    'Uzbekistan' => 193,
                    'Vanuatu' => 194,
                    'VaticanCity' => 195,
                    'Venezuela' => 196,
                    'Vietnam' => 197,
                    'WestBank' => 198,
                    'WesternSahara' => 199,
                    'Yemen' => 200,
                    'Zambia' => 201,
                    'Zimbabwe' => 202,
                    'PalestinianAuthority' => 203,
                )
            );
        }
    }

    public function getProductionConfig()
    {
        return array(
            'campus' => array(
                'BJ_DS' => 61, 
                'BJ_SLT' => 63, 
                'BJ_QFF' => 64,
                'BJ_CP' => 67,
                'BJ_IASLT' => 60,
                'BJ_OE' => 62,
                'BJ_OG-PH' => 71,
                'TJ_EB' => 66,
                'TJ_EC' => 75,
                'TJ_ES' => 76,
                'CD_LT' => 68,
                'CD_LH' => 72,
                'NB_HH' => 70,
                'NB_FJ' => 69,
                'XA_GJ' => 73,
                'XA_LB' => 74,
            ),
            'finalResult' => array(200 => '已录取', 210 => '有条件录取'),
            'gender' => array(1 => '男', 2 => '女'),
            'class' => array(
                136 => "托班[[@]]托班",
                137 => "小班[[@]]小班",
                138 => "中班[[@]]中班",
                140 => "大班[[@]]大班",
                156 => "蒙台梭利[[@]]Casa",
                153 => "托班[[@]]Nursery",
                157 => "学前班[[@]]Kinder",
                170 => "学前班[[@]]Kinder",
                158 => "1年级[[@]]Grade 1",
                159 => "2年级[[@]]Grade 2 ",
                160 => "3年级[[@]]Grade 3",
                161 => "4年级[[@]]Grade 4 ",
                162 => "5年级[[@]]Grade 5",
                163 => "6年级[[@]]Grade 6",
                164 => "7年级[[@]]Grade 7",
                165 => "8年级[[@]]Grade 8",
                166 => "9年级[[@]]Grade 9",
                167 => "10年级[[@]]Grade 10",
                168 => "11年级[[@]]Grade 11",
                169 => "12年级[[@]]Grade 12",
                190 => "托班[[@]]托班",
                188 => "中班[[@]]中班",
                189 => "大班[[@]]大班",
            ),
            'lang' => array(
                120 => 1, // 中文
                121 => 2, // 英语
                124 => 156, // 中英双语
                156 => 65, // 法语
                232 => 71, // 德语
                255 => 68, // 韩语
                266 => 67, // 日语
                243 => 69, // 意大利语
            ),
            'country' => array(
                'China' => 36,
                'China(HKMacauTW)' => 175,
                'Canada' => 31,
                'Japan' => 86,
                'UnitedKingdom' => 187,
                'UnitedStates' => 188,
            )
        );
    }

    public function getSlibingData($emailList, $mphoneList = array())
    {
        
        $userIdList = array();
        if ($emailList) {
            // 根据父母邮件查找已存在用户
            $crit = new CDbCriteria();
            $crit->compare('email', $emailList);
            $crit->compare('isstaff', 0);
            $crit->index = 'uid';
            $userModelList = User::model()->findAll($crit);
            $userIdList = array_keys($userModelList);
        }
        if ($mphoneList) {
            // 根据父母电话查找
            $crit = new CDbCriteria();
            $crit->compare('mphone', $mphoneList);
            $crit->index = 'pid';
            $parentModelList = IvyParent::model()->findAll($crit);
            $userIdList = $userIdList + array_keys($parentModelList);
        }
        if (count($userIdList) < 1) {
            return array();
        }
        
        // 查找孩子信息
        $slibingData = array();
        $userIdList = array_unique($userIdList);
        if ($userIdList) {
            $crit = new CDbCriteria();
            $crit->compare('fid', $userIdList);
            $crit->compare('mid', $userIdList, false, 'OR');
            $childModelList = ChildProfileBasic::model()->findAll($crit);
            foreach ($childModelList as $childModel) {
                $key = $childModel->fid ."_". $childModel->mid;
                if (!isset($slibingData[$key])) {
                    $fData = array(
                        'name' => '',
                        'email' => '',
                        'mphone' => '',
                    );
                    $mData = array(
                        'name' => '',
                        'email' => '',
                        'mphone' => '',
                    );
                    if (isset($parentModelList[$childModel->fid])) {
                        $fModel = $parentModelList[$childModel->fid];
                        $fData = array(
                            'name' => $fModel->getName(),
                            'email' => $fModel->ivyUser->email,
                            'mphone' => $fModel->mphone,
                        );
                    } elseif (isset($userModelList[$childModel->fid])) {
                        $fModel = $userModelList[$childModel->fid];
                        $fData = array(
                            'name' => $fModel->parent->getName(),
                            'email' => $fModel->email,
                            'mphone' => $fModel->parent->mphone,
                        );
                    }
                    if (isset($parentModelList[$childModel->mid])) {
                        $mModel = $parentModelList[$childModel->mid];
                        $mData = array(
                            'name' => $mModel->getName(),
                            'email' => $mModel->ivyUser->email,
                            'mphone' => $mModel->mphone,
                        );
                    } elseif (isset($userModelList[$childModel->mid])) {
                        $mModel = $userModelList[$childModel->mid];
                        $mData = array(
                            'name' => $mModel->parent->getName(),
                            'email' => $mModel->email,
                            'mphone' => $mModel->parent->mphone,
                        );
                    }
                    $slibingData[$key] = array(
                        'fData' => $fData,
                        'mData' => $mData,
                    );
                }
                $slibingData[$key]['childData'][] = array(
                    'childid' => $childModel->childid,
                    'name' => $childModel->getChildName(),
                    'birthday' => date('Y-m-d', $childModel->birthday),
                );
            }
        }
        return array_values($slibingData);
    }

    /**
     * 预约参观管理主页面
     * @param string $branchId
     * @param string $type
     */
    public function actionIndex($branchId='', $type='pending')
    {
        $this->branchSelectParams["urlArray"] = array("//mcampus/admissions/index", 'type'=>$type);

        $model=new IvyschoolsVisit('search');
        $model->unsetAttributes();  // clear any default values
        $visitDate = '';
        $visitTime = '';
        if(isset($_GET['IvyschoolsVisit']))
            $model->attributes=$_GET['IvyschoolsVisit'];


        $model->schoolid=$this->branchId;
        $model->status=0;
        $mdataProvider = $model->searchDate();

        $model = new IvyVisitsRecord;
        $model->unsetAttributes();
        if(isset($_GET['IvyVisitsRecord']))
            $model->attributes=$_GET['IvyVisitsRecord'];

        $model->schoolid=$this->branchId;

        $dataProvider = $model->searchDate();

        $schoolsCount = array();
        if ($type == 'pending')
            $sql = 'select schoolid, count(*) as c from '.IvyschoolsVisit::model()->tableName().' where status=0 group by schoolid';
        else
            $sql = "select schoolid, count(*) as c from ".IvyVisitsRecord::model()->tableName()." where category='appointment' and appointment_date=".strtotime("today")." group by schoolid";
        $sc=Yii::app()->db->createCommand($sql)->queryAll();
        foreach ($sc as $_sc){
            $schoolsCount[$_sc['schoolid']]=$_sc['c'];
        }

        $configs = array('0'=> '全部');
        $visit_date = (time() - 31536000);
        $criteria=new CDbCriteria;
        $criteria->compare('t.schoolid',$this->branchId);
        $criteria->compare('t.visit_date', ">{$visit_date}");
        //$criteria->group  = 'visit_time';
        $config = IvyschoolsVisit::model()->findAll($criteria);

        if($config) {
            foreach ($config as $k => $v) {
                $configs[$v->visit_time] = $v->visit_time;
            }
        }
        $assignData = array(
            'mdataProvider'=>$mdataProvider,
            'configs'=>$configs,
            'dataProvider'=>$dataProvider,
            'schoolsCount'=>CJSON::encode($schoolsCount),
        );

        $this->render('visit/index',$assignData);
    }

    // 打印来访记录
    public function actionPrint()
    {
        $this->layout = '//layouts/print';
        $this->printFW = $this->branchObj->getPrintHeader();

        $model = new IvyVisitsRecord;
        $model->unsetAttributes();
        if(isset($_GET['IvyVisitsRecord']))
            $model->attributes = $_GET['IvyVisitsRecord'];
        $model->schoolid = $this->branchId;
        $dataProvider = $model->searchDate();
        $this->render('visit/print', array('dataProvider' => $dataProvider));
    }

    public function actionPrints()
    {
        $this->layout = '//layouts/print';
        $this->printFW = $this->branchObj->getPrintHeader();
        $model = new IvyschoolsVisit;
        $model->unsetAttributes();
        if(isset($_GET['IvyschoolsVisit']))
            $model->attributes = $_GET['IvyschoolsVisit'];
        $model->schoolid = $this->branchId;
        $dataProvider = $model->searchDate();

        $criteria = new CDbCriteria;
        $criteria->compare('schoolid', $_GET['branchId']);
        $criteria->compare('visit_date', strtotime($_GET['IvyschoolsVisit']['visit_date']));
        $criteria->compare('visit_time', $_GET['IvyschoolsVisit']['visit_time']);
        $criteria->compare('status', 0);
        $count = IvyschoolsVisit::model()->count($criteria);
        $this->render('visit/prints', array(
            'dataProvider' => $dataProvider,
            'count' => $count
        ));
    }

    // 导出预约的excel表
    public function actionExportClvs()
    {
        $visit_date = Yii::app()->request->getParam('IvyschoolsVisit', '');
        $visitDate = ($visit_date['visit_date']) ? strtotime($visit_date['visit_date']) : "";
        $visitTime = ($visit_date['visit_time']) ? $visit_date['visit_time'] : "";

        $filename = date('Y-m-d', time());
        $criteria = new CDbCriteria;
        $criteria->compare('schoolid', $this->branchId);
        $criteria->compare('visit_date', $visitDate);
        $criteria->compare('visit_time', $visitTime);
        $criteria->compare('status', 0);
        $visitModel = IvyschoolsVisit::model()->findAll($criteria);
        $cfgs = $this->loadConfig();
        $languageList = array();
        if (isset($cfgs['language']))
        {
            foreach ($cfgs['language'] as $k=>$v)
            {
                $languageList[$k] = (Yii::app()->language=='zh_cn') ? $v['cn'] : $v['en'];
            }
        }

        $data = array();
        $data['title'] = $filename . ".xlsx";
        $data['items'][0] = array('预约日期', '孩子姓名', '孩子生日', '家长姓名', '目前所在学校','目前所在年级','参观人数','申请班级','家长电话', '家长邮箱','填写日期','接待语言','预计入学','了解途径','关注因素', '备注', '来源');

        foreach ($visitModel as $i => $visit) {
            $concerns = '';
            $concernsArray = explode('|', $visit->concerns);
            foreach ($concernsArray as $concern) {
                $concerns .= $cfgs['concerns'][$concern]['cn'] . '；';
            }
            $array = array();
            $array[] = OA::formatDateTime($visit->visit_date) .','. $visit->visit_time;
            $array[] = $visit->child_name;
            //$array[] = CommonUtils::getAge($visit->birthdate);
            $array[] = date("Y-m-d", $visit->birthdate);
            $array[] = $visit->parent_name;
            $array[] = $visit->current_school;
            $array[] = $visit->current_grade;
            $array[] = $visit->expected_attendance;
            $array[] = ($visit->application_grade) ? $cfgs['applicationGradeDS'][$visit->application_grade]  : "";
            $array[] = $visit->phone;
            $array[] = $visit->email;
            $array[] = OA::formatDateTime($visit->create_date);
            $array[] = $languageList[$visit->receive_language];
            $array[] = $visit->school_year;
            $array[] = $cfgs['knowus'][$visit->knowus]['cn'];
            $array[] = $concerns;
            $array[] = $visit->memo;
            $array[] = $visit->from;
            $data['items'][$i + 1] = $array;
        }

        $this->addMessage('state', 'success');
        $this->addMessage('data', $data);
        $this->showMessage();

    }

    // 导出需要接待的excel表
    public function actionExportClv()
    {
        $filename = date('Y-m-d', time());
        $criteria = new CDbCriteria;
        $criteria->compare('schoolid', $_GET['branchId']);
        if (!$_GET['IvyVisitsRecord']['appointment_date']) {
            $criteria->compare('appointment_date', '>='. strtotime('today'));
        } else{
            $criteria->compare('appointment_date', strtotime($_GET['IvyVisitsRecord']['appointment_date']));
        }
        $criteria->compare('status', array(0, IvyVisitsRecord::VISITS_STATUS));
        $criteria->compare('appointment_time', $_GET['IvyVisitsRecord']['appointment_time']);
        $visitRecord = IvyVisitsRecord::model()->findAll($criteria);

        $cfgs = $this->loadConfig();
        $languageList = array();
        if (isset($cfgs['language']))
        {
            foreach ($cfgs['language'] as $k=>$v)
            {
                $languageList[$k] = (Yii::app()->language=='zh_cn') ? $v['cn'] : $v['en'];
            }
        }

        $data = array();
        $data['title'] = $filename . ".xlsx";
        $data['items'][0] = array('预约日期', '孩子姓名', '孩子生日', '家长姓名', '家长电话','家长邮箱','接待语言','了解途径','关注因素', '备注','是否已到');
        if($visitRecord){
            foreach ($visitRecord as $i => $record) {
                $concerns = '';
                $concernsArray = explode('|', $record->basic->concerns);
                foreach ($concernsArray as $concern) {
                    $concerns .= $cfgs['concerns'][$concern]['cn'] . '；';
                }
                $array = array();
                $array[] = OA::formatDateTime($record->appointment_date) .','. $record->appointment_time;
                $array[] = $record->basic->child_name;
                //$array[] = CommonUtils::getAge($record->basic->birth_timestamp);
                $array[] = date("Y-m-d", $record->basic->birth_timestamp);
                $array[] = $record->basic->parent_name;
                $array[] = $record->basic->tel;
                $array[] = $record->basic->email;
                $array[] = $record->basic->receive_language;
                $array[] = $cfgs['knowus'][$record->basic->knowus]['cn'];
                $array[] = $concerns;
                $array[] = $record->basic->memo;
                $array[] = $record->category == 'visit' ? '1' : '0';
                $data['items'][$i + 1] = $array;
            }
        }
        $this->addMessage('state', 'success');
        $this->addMessage('data', $data);
        $this->showMessage();

    }

    /**
     * 返回预约（未确认/已确认）的日期
     * @param string $type
     */
    public function actionTargetDate($type='pending')
    {
        $tdate = array();
        if ($type == 'pending'){
            $sql = "select visit_date from ivy_ivyschools_visit where status=0 and schoolid='".$this->branchId."'";
            $rows = Yii::app()->db->createCommand($sql)->queryAll();
            foreach ($rows as $date){
                $vistimestamp = date('Ynj', $date['visit_date']);
                $tdate[$vistimestamp] = $vistimestamp;
            }
        }
        else{
            $sql = "select appointment_date from ivy_visits_record where status=10 and schoolid='".$this->branchId."' and appointment_date>=".strtotime('-90 day');
            $rows = Yii::app()->db->createCommand($sql)->queryAll();
            foreach ($rows as $date){
                $vistimestamp = date('Ynj', $date['appointment_date']);
                $tdate[$vistimestamp] = $vistimestamp;
            }
        }
        echo CJSON::encode($tdate);
    }

    /**
     * 修改预约参观信息及保存
     * @param int $id
     */
    public function actionVisitUpdate($id=0)
    {
        $model=$this->loadModel($id);
        $model->visit_date = OA::formatDateTime($model->visit_date);
        $model->birthdate = OA::formatDateTime($model->birthdate);
        $model->concerns = explode('|', $model->concerns);

        if(isset($_POST['IvyschoolsVisit']))
        {
            $model->attributes=$_POST['IvyschoolsVisit'];
            $model->visit_date = strtotime($model->visit_date);
            $model->birthdate = strtotime($model->birthdate);
            $model->concerns = implode('|', $model->concerns);
            $model->create_date = time();
            if($model->save()){
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message','success'));
                $this->addMessage('callback', 'cbVisit');
            } else {
                $this->addMessage('state', 'fail');
                $err = current($model->getErrors());
                $this->addMessage('message', $err ? $err[0] : '失败');
            }
            $this->showMessage();
        }
        $model->visit_date = OA::formatDateTime($model->visit_date);
        $model->birthdate = OA::formatDateTime($model->birthdate);
        $this->renderpartial('visit/visitupdate',array(
            'model'=>$model,
            'modalTitle'=>'编辑',
        ));
    }

    /**
     * 手动添加来访记录
     */
    public function actionSaveRecord()
    {
        $model = new IvyVisitsBasicInfo;
        $model->schoolid = $this->branchId;
        $status = Yii::app()->request->getParam('status', '');
        $times = Yii::app()->request->getParam('times', '');
        $appointment_time = Yii::app()->request->getParam('appointment_time', '');
        if($_POST['IvyVisitsBasicInfo'])
        {
            $timea = strtotime($times);
            if(empty($_POST['IvyVisitsBasicInfo']['tel']) && empty($_POST['IvyVisitsBasicInfo']['email'])){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '请填写手机号或邮箱');
                $this->showMessage();
            }
            if(empty($_POST['IvyVisitsBasicInfo']['knowus'])){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '来源不能为空');
                $this->showMessage();
            }

            if(empty($times) || !is_numeric($timea)){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '时间不能为空');
                $this->showMessage();
            }
            $pattern = "/^([0-9A-Za-z\\-_\\.]+)@([0-9a-z]+\\.[a-z]{2,3}(\\.[a-z]{2})?)$/i";
            if($_POST['IvyVisitsBasicInfo']['email'] && !preg_match($pattern, $_POST['IvyVisitsBasicInfo']['email'])){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '邮箱格式不正确');
                $this->showMessage();
            }
            if(empty($_POST['IvyVisitsBasicInfo']['parent_name'])){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '家长姓名不能为空');
                $this->showMessage();
            }
            if(empty($_POST['IvyVisitsBasicInfo']['child_name'])){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '孩子姓名不能为空');
                $this->showMessage();
            }
            if(empty($_POST['IvyVisitsBasicInfo']['birth_timestamp'])){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '孩子生日不能为空');
                $this->showMessage();
            }
            if(empty($status)){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '状态不能为空');
                $this->showMessage();
            }
            if(empty($appointment_time) && $status != 2){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '预约时间不能为空');
                $this->showMessage();
            }
            $ChildrenBasic = IvyVisitsBasicInfo::model()->getOneVisitBasicInfo($_POST['IvyVisitsBasicInfo']['tel'],$_POST['IvyVisitsBasicInfo']['email']);
            if($ChildrenBasic){
                $model = new IvyVisitsBasicInfoBak;
                $model->setAttributes($ChildrenBasic->getAttributes());
                $model->update_timestamp = time();
                $model->update_user = Yii::app()->user->id;
                $model->save();
            } else{
                $ChildrenBasic = new IvyVisitsBasicInfo;
            }

            $ChildrenBasic->attributes = $_POST['IvyVisitsBasicInfo'];
            $ChildrenBasic->birth_timestamp = strtotime($ChildrenBasic->birth_timestamp);
            $ChildrenBasic->concerns = implode('|', $ChildrenBasic->concerns);
            $ChildrenBasic->child_enroll = strtotime($ChildrenBasic->child_enroll);
            $ChildrenBasic->update_timestamp = time();
            $ChildrenBasic->register_timestamp = time();
            $ChildrenBasic->state_timestamp = time();
            $ChildrenBasic->recent_log = 0;
            $ChildrenBasic->status = 10;
            if($ChildrenBasic->receive_language == '1'){
                $ChildrenBasic->receive_language = '中文';
            }else{
                $ChildrenBasic->receive_language = '英文';
            }
            $ChildrenBasic->update_user = Yii::app()->user->id;

            if($ChildrenBasic->save()){
                $criteria =new CDbCriteria;
                $criteria->compare('basic_id',$ChildrenBasic->id);
                $criteria->compare('status',10);
                $IvyVisitsRecord = IvyVisitsRecord::model()->find($criteria);
                if($IvyVisitsRecord){
                    $IvyVisitsRecord->status = 20;
                    $IvyVisitsRecord->save();
                }
                $Record = new IvyVisitsRecord;
                $Record->schoolid = $this->branchId;
                $Record->basic_id = $ChildrenBasic->id;
                if($status == 1){
                    $Record->category = 'appointment';
                    $Record->appointment_date = $timea;
                    $Record->appointment_time = $appointment_time;
                    $Record->visit_timestamp = 0;
                }
                if($status == 2){
                    $Record->category = 'visit';
                    $Record->visit_timestamp = $timea;
                    $Record->appointment_time = 0;
                    $Record->appointment_date = 0;
                }
                $Record->status = 10;
                $Record->update_timestamp = time();
                $Record->update_user = Yii::app()->user->id;
                if($Record->save()){
                    $criteria =new CDbCriteria;
                    $criteria->compare('basic_id',$ChildrenBasic->id);
                    $criteria->compare('school_id', $this->branchId);
                    $visitsBasicLinkModel = VisitsBasicLink::model()->find($criteria);
                    if(!$visitsBasicLinkModel){
                        $visitsBasicLinkModel = new VisitsBasicLink();
                        $visitsBasicLinkModel->basic_id = $ChildrenBasic->id;
                        $visitsBasicLinkModel->school_id = $this->branchId;
                    }
                    $visitsBasicLinkModel->status = $ChildrenBasic->status;
                    $visitsBasicLinkModel->visit_id = $Record->id;
                    $visitsBasicLinkModel->updated_at = time();
                    $visitsBasicLinkModel->updated_by = Yii::app()->user->id;
                    $visitsBasicLinkModel->save();

                    $this->addMessage('state', 'success');
                    $this->addMessage('message', Yii::t('message','success'));
                    $this->addMessage('callback', 'cbVisit');
                } else {
                    $this->addMessage('state', 'fail');
                    $err = current($Record->getErrors());
                    $this->addMessage('message', $err ? $err[0] : '失败');
                }
                $this->showMessage();
            } else{
                $error = current($ChildrenBasic->getErrors());
                $this->addMessage('state', 'fail');
                $this->addMessage('message', $error[0]);
                $this->showMessage();
            }
        }

        $this->renderpartial('visit/visitsRecord', array(
            'model'=>$model,
            'modalTitle'=>'新建记录',
        ));
    }

    /**
     * 审核预约参观信息及保存
     * @param $id
     */
    public function actionVisitAview()
    {
        $this->loadConfig();
        if(Yii::app()->request->isPostRequest){
            $id = Yii::app()->request->getParam('id', '');
            $status = Yii::app()->request->getParam('status', '');
            $send = Yii::app()->request->getPost('send', false);
            $send = ($send == 'true') ? true : false;
            if (!$id || !$status) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '至少选择一个参观记录');
                $this->showMessage();
            }
            $models = IvyschoolsVisit::model()->findAllByPk($id);
            foreach ($models as $model) {
                $model->status = $status;
                if ($model->save()){
                    $tmp = $this->saveReadyInfo($model, $send);;
                    // if ($model->status == VISIT_USELESS_INFO)
                    // {
                    //     $tmp = $this->deleteInfo($model->id);
                    // }
                    // elseif ($model->status == VISIT_READY_INFO)
                    // {
                    //     $tmp = $this->saveReadyInfo($model->id, $send);
                    // }
                    // elseif ($model->status == VISIT_UNREADY_INFO)
                    // {
                    //     $tmp = $this->saveUnreadyInfo($model->id);
                    // }
                    if ($tmp != 'ok'){
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', $tmp?$tmp:'失败');
                        $this->showMessage();
                    }
                } else{
                    $this->addMessage('state', 'fail');
                    $err = current($model->getErrors());
                    $this->addMessage('message', $err ? $err[0] : '失败');
                }
            }
            $this->addMessage('state', 'success');
            $this->addMessage('message', '保存成功');
            $this->showMessage();
        }

        $this->renderpartial('visit/visitaview',array('modalTitle'=>'审核',));
    }

    // 保存信息准确并来参观的来访申请
    public function saveReadyInfo($model, $send = false)
    {
        $cfgs = $this->loadConfig();
        if (!empty($model))
        {
            $basicModel = IvyVisitsBasicInfo::model()->getOneVisitBasicInfo($model->phone,$model->email);
            if (empty($basicModel))
            {
                $basicModel = new IvyVisitsBasicInfo();
            }
            else
            {
                $bakBasicModel = new IvyVisitsBasicInfoBak();
                $bakBasicModel->setAttributes($basicModel->getAttributes());
                $bakBasicModel->save();
            }
            $basicModel->child_name = $model->child_name;
            $basicModel->parent_name = $model->parent_name;
            $basicModel->birth_timestamp = $model->birthdate;
            $basicModel->tel = $model->phone;
            $basicModel->email = $model->email;
            $basicModel->address = $model->address;
            $basicModel->concerns = $model->concerns;
            $basicModel->communication = 0;
            $basicModel->child_enroll = 0;
            $basicModel->country = 0;
            $basicModel->knowus = $model->knowus;
            $basicModel->memo = $model->memo;
            $basicModel->register_timestamp = $model->create_date;
            $basicModel->recent_log = $model->create_date;
            $basicModel->state_timestamp = time();
            $basicModel->status = 10;
            $basicModel->childid = 0;
            $basicModel->receive_language = $cfgs['language'][$model->receive_language]['cn'];
            $basicModel->update_timestamp = time();
            $basicModel->update_user = Yii::app()->user->id;
            if ($basicModel->save())
            {
                $basicModel->refresh();
                // 更新同一孩子下的来访记录状态
                IvyVisitsRecord::model()->updateAll(
                    array('status'=>20),
                    'basic_id=:basic_id AND schoolid=:schoolid',
                    array(':basic_id'=>$basicModel->id, ':schoolid'=>$this->branchId)
                );
                $visitModel = new IvyVisitsRecord();
                $visitModel->basic_id = $basicModel->id;
                $visitModel->event_id = 0;
                $visitModel->category = 'appointment';
                $visitModel->schoolid = $model->schoolid;
                // $visitModel->visit_timestamp = $model->visit_date;
                $visitModel->parent_num = 0;
                $visitModel->child_num = 0;
                $visitModel->is_ivyparent = 0;
                if ($model->status == VISIT_READY_INFO) {
                    $visitModel->appointment_date = $model->visit_date;
                    $visitModel->appointment_time = $model->visit_time;
                } else {
                    $visitModel->appointment_date = '';
                    $visitModel->appointment_time = '';
                }
                $visitModel->status = 10;
                $visitModel->update_timestamp = time();
                $visitModel->update_user = Yii::app()->user->id;
                if ($visitModel->save())
                {
                    $criteria =new CDbCriteria;
                    $criteria->compare('basic_id',$basicModel->id);
                    $criteria->compare('school_id',$model->schoolid);
                    $modelL = VisitsBasicLink::model()->find($criteria);
                    if(!$modelL){
                        $modelL = new VisitsBasicLink();
                        $modelL->basic_id = $basicModel->id;
                        $modelL->school_id = $visitModel->schoolid;
                    }
                    $modelL->status = $basicModel->status;
                    $modelL->visit_id = $visitModel->id;
                    $modelL->updated_at = time();
                    $modelL->updated_by = Yii::app()->user->id;
                    $modelL->save();

                    // 添加操作的跟踪记录
                    $statusList = array(
                        1=>'信息准确，人准备来参观',
                        2=>'信息准确，人不来',
                        3=>'过期、虚假或重复信息'
                    );
                    $visits = new VisitsTraceLog();
                    $visits->basic_id = $visitModel->basic_id;
                    $visits->visit_id = $visitModel->id;
                    $visits->type = 10;
                    $visits->content = $statusList[$model->status];
                    $visits->schoolid = $visitModel->schoolid;
                    $visits->update_timestamp = time();
                    $visits->update_user = Yii::app()->user->id;
                    $visits->save();

                    $visitModel->refresh();
                    if ($send && $model->status == VISIT_READY_INFO) {
                        $email = $model->email;
                        if (strpos($email, '@')) {
                            $info['date'] = $visitModel->appointment_date;
                            $info['time'] = $visitModel->appointment_time;
                            $info['location'] = $this->branchId;
                            $info['confirm'] = false;
                            $this->sendEmail($info, $email);
                        }
                    }
                    // 发送确认邮件
                    return "ok";
                }
            }
        }
        else
            throw new CHttpException(400,'Invalid request. Please do not repeat this request again.');
    }

    // 保存信息准确但不来参观的来访申请
    public function saveUnreadyInfo($id)
    {
        $model = $this->loadModel($id);
        $cfgs = $this->loadConfig();
        if (!empty($model))
        {
            $basicModel = IvyVisitsBasicInfo::model()->getOneVisitBasicInfo($model->phone,$model->email);
            if (empty($basicModel)) {
                $basicModel = new IvyVisitsBasicInfo();
            } else{
                $bakBasicModel = new IvyVisitsBasicInfoBak();
                $bakBasicModel->setAttributes($basicModel->getAttributes());
                $bakBasicModel->save();
            }
            $basicModel->child_name = $model->child_name;
            $basicModel->parent_name = $model->parent_name;
            $basicModel->birth_timestamp = $model->birthdate;
            $basicModel->tel = $model->phone;
            $basicModel->email = $model->email;
            $basicModel->address = $model->address;
            $basicModel->concerns = $model->concerns;
            $basicModel->communication = 0;
            $basicModel->child_enroll = 0;
            $basicModel->country = 0;
            $basicModel->knowus = $model->knowus;
            $basicModel->memo = $model->memo;
            $basicModel->register_timestamp = $model->create_date;
            $basicModel->recent_log = $model->create_date;
            $basicModel->state_timestamp = time();
            $basicModel->status = 10;
            $basicModel->childid = 0;
            $basicModel->receive_language = $cfgs['language'][$model->receive_language]['cn'];
            $basicModel->update_timestamp = time();
            $basicModel->update_user = Yii::app()->user->id;
            if ($basicModel->save())
            {
                $basicModel->refresh();
                return "ok";
            } else{
                return current($basicModel->getErrors());
            }
        }
        else
            throw new CHttpException(400,'Invalid request. Please do not repeat this request again.');
    }

    // 删除信息不准确的来访申请
    public function deleteInfo($id)
    {
        IvyschoolsVisit::model()->updateByPk($id, array('status'=>3), 'schoolid=:schoolid', array(':schoolid'=>$this->branchId));
        return 'ok';
    }

    /**
     * 确认家长是否已来参观
     * @param  string $value [description]
     * @return [type]        [description]
     */
    public function actionConfirmVisit()
    {
        if (Yii::app()->request->isPostRequest) {
            $ids = Yii::app()->request->getPost('ids', '');
            $send = Yii::app()->request->getPost('send', '');
            $send = ($send == 'true') ? true : false;
            $models = IvyVisitsRecord::model()->findAllByPk($ids);
            if ($models) {
                foreach ($models as $model) {
                    if ($model->category != 'visit') {
                        $model->category = 'visit';
                        $model->visit_timestamp = $model->appointment_date;
                        $model->update_timestamp = time();
                        $model->update_user = $this->staff->uid;
                        if ($model->save()) {
                            // 添加跟踪记录
                            $visits = new VisitsTraceLog();
                            $visits->basic_id = $model->basic_id;
                            $visits->visit_id = $model->id;
                            $visits->type = 10;
                            $visits->content = "确认到场";
                            $visits->schoolid = $model->schoolid;
                            $visits->update_timestamp = time();
                            $visits->update_user = Yii::app()->user->id;
                            $visits->save();

                            $email = $model->basic->email;
                            if ($send && strpos($email, '@')) {
                                $info = array('confirm' => true);
                                $this->sendEmail($info, $email);
                            }
                        }
                    }
                }
                $this->addMessage('state', 'success');
                $this->showMessage();
            }
            $this->addMessage('state', 'fail');
            $this->addMessage('message', 'No data save');
            $this->showMessage();
        }
    }

    /**
     * 修改预约参观时间及保存
     * @param int $id
     */
    public function actionVisitChangeDate($id=0)
    {
        if ($id){
            $model = IvyVisitsRecord::model()->findByPk($id, 'schoolid=:schoolid', array(':schoolid'=>$this->branchId));

            if(isset($_POST['IvyVisitsRecord'])){
                if ($model != null){
                    $model->setScenario('changedate');
                    $model->appointment_date = strtotime($_POST['IvyVisitsRecord']['appointment_date']);
                    $model->appointment_time = $_POST['IvyVisitsRecord']['appointment_time'];
                    if ( $model->save() ){
                        $this->addMessage('state', 'success');
                        $this->addMessage('message', Yii::t('message','success'));
                        $this->addMessage('callback', 'cbVisit');
                    }
                    else{
                        $this->addMessage('state', 'fail');
                        $err = current($model->getErrors());
                        $this->addMessage('message', $err ? $err[0] : '失败');
                    }
                }
                else{
                    $this->addMessage('state', 'success');
                    $this->addMessage('message', '对象丢失！');
                    $this->addMessage('callback', 'cbVisit');
                }
                $this->showMessage();
            }

            $model->appointment_date = OA::formatDateTime($model->appointment_date);
            $this->renderpartial('visit/visitchangedate', array('model'=>$model,'modalTitle'=>'重新安排时间'));
        }
    }

    /**
     * 预约参观配置显示
     */
    public function actionVisitsConfig()
    {
        $this->branchSelectParams['urlArray'] = array('//mcampus/admissions/visitsConfig');
        $criteria = new CDbCriteria;
        $criteria->compare('schoolid', $this->branchId);
        $model = VisitsConfig::model()->find($criteria);

        $days = VisitsConfigItem::model()->getVisitItems($this->branchId);
        if($model != null){
            $model = array(
                'id' => $model->id,
                'schoolid' => $model->schoolid,
                'week' => CJSON::decode($model->week),
                'days' => $days,
                'time' => CJSON::decode($model->time),
                'status' => $model->status,
                'status_text_cn' => $model->status_text_cn,
                'status_text_en' => $model->status_text_en,
            );
        }
        $array = array( '0'=>'星期日', '1'=>'星期一', '2'=>'星期二', '3'=>'星期三', '4'=>'星期四', '5'=>'星期五', '6'=>'星期六');

        if($_POST){
            $schoolid = $this->branchId;
            $weeks = Yii::app()->request->getParam('week', '');
            $status_text_cn = Yii::app()->request->getParam('status_text_cn', '');
            $status_text_en = Yii::app()->request->getParam('status_text_en', '');

            $days = Yii::app()->request->getParam('day', 0);
            $times = Yii::app()->request->getParam('time', 0);
            $status = Yii::app()->request->getParam('status', 0);

            $cdate = Yii::app()->request->getParam('cdate');
            $ctime = Yii::app()->request->getParam('ctime');
            $cnum = Yii::app()->request->getParam('cnum');

            foreach ($cdate as $k => $_cdate) {
                if (!isset($ctime[$k]) || empty($ctime[$k])) {
                    continue;
                }
                if (!isset($cnum[$k]) || empty($cnum[$k])) {
                    continue;
                }
                $criteria = new CDbCriteria();
                $criteria->compare('schoolid', $schoolid);
                $criteria->compare('day', $_cdate);
                $criteria->compare('visit_time', $ctime[$k]);
                $cmodel = VisitsConfigItem::model()->find($criteria);
                if (!$cmodel) {
                    $cmodel = new VisitsConfigItem();
                    $cmodel->created_at = time();
                    $cmodel->created_by = $this->staff->uid;
                }

                $cmodel->schoolid = $schoolid;
                $cmodel->day = $_cdate;
                $cmodel->day_timestamp = strtotime($_cdate);
                $cmodel->visit_time = $ctime[$k];
                $cmodel->visit_num = $cnum[$k];
                $cmodel->status = 1;
                $cmodel->updated_at = time();
                $cmodel->updated_by = $this->staff->uid;
                $cmodel->save();
            }

            $time = array();
            foreach($times as $k=>$v){
                if($v['time'] && $v['man']){
                    $time[] = $v;
                }
            }

            $week =  $weeks ? CJSON::encode($weeks) : '';
            $day =  $days ? CJSON::encode(array_unique($days)) : '';
            if(!empty($time)){
                $time =  $times ? CJSON::encode($time) : '';
                $criteria = new CDbCriteria;
                $criteria->compare('schoolid', $this->branchId);
                $model = VisitsConfig::model()->find($criteria);
                if(!$model){
                    $model = new VisitsConfig();
                }

                $model->schoolid = $schoolid;
                $model->week = $week;
                $model->day = $day;
                $model->status = $status;
                $model->status_text_cn = $status_text_cn;
                $model->status_text_en = $status_text_en;
                $model->time = $time;
                $model->update_timestamp = time();
                if($model->save()){
                    $this->addMessage('state', 'success');
                    $this->addMessage('message', Yii::t('message','success'));
                    $this->addMessage('callback', 'cbAddVisits');
                }else{
                    $this->addMessage('state', 'fail');
                    $err = current($model->getErrors());
                    $this->addMessage('message', $err[0]);
                }
            }else{
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '接待时间不能为空');
            }
            $this->showMessage();
        }
        $this->render('visit/visitsConfig', array(
            'model'=>$model,
            'week'=>$model['week'],
            'days'=>$days,
            'array'=>$array
        ));
    }

    // 删除单独的配置访问时间
    public function actionDeleteDay()
    {
        $id = Yii::app()->request->getParam('id');
        $this->addMessage('state', 'fail');
        $this->addMessage('message', 'fail');
        if ($id) {
            $model = VisitsConfigItem::model()->findByPk($id);
            if ($model) {
                $model->status = 0;
                $model->save();
                $this->addMessage('state', 'success');
                $this->addMessage('message', 'success');
                $this->addMessage('data', $id);
                $this->addMessage('callback', 'deleteSuccess');
            }
        }
        $this->showMessage();
    }

    /**
     * 来访记录
     */
    public function actionVisitsLog()
    {

        $status = Yii::app()->request->getParam('status', 10);
        $knowus = Yii::app()->request->getParam('knowus', '');
        $daytimeStar = Yii::app()->request->getParam('daytimeStar', '');
        $daytimeEnd = Yii::app()->request->getParam('daytimeEnd', '');
        $daytime = Yii::app()->request->getParam('daytime', '');
        $age = Yii::app()->request->getParam('age', '');
        $recent_log = Yii::app()->request->getParam('recent_log', '');
        $visitStatus = Yii::app()->request->getParam('visitStatus', '');
        $event = Yii::app()->request->getParam('event', '');
        $search = Yii::app()->request->getParam('search', '');
        $this->branchSelectParams['urlArray'] = array('//mcampus/admissions/visitsLog', 'status'=>$status);

        $criteria = new CDbCriteria;
        $criteria->with = array('basic', 'newrecord');
        if($knowus) $criteria->compare('basic.knowus', $knowus);
        if($status) $criteria->compare('t.status', $status);
        if($event) $criteria->compare('newrecord.event_id', $event);
        if($recent_log) {
            switch($recent_log){
                case 10 :
                    $timestamp = time() - 1296000;
                    break;
                case 20 :
                    $timestamp = strtotime('-1 month');
                    break;
                case 30 :
                    $timestamp = strtotime('-2 month');
                    break;
            }
            $criteria->compare('basic.recent_log', ">{$timestamp}");
        }

        if ($visitStatus) {
            $criteria->compare('newrecord.category', $visitStatus);
        }

        if ($daytimeStar || $daytimeEnd) {
            //Yii::msg($dayTimestamp);
            // if ($visitStatus == 'visit') {
            //     $criteria->compare('newrecord.visit_timestamp', $dayTimestamp);
            // } elseif ($visitStatus == 'appointment') {
            //     $criteria->compare('newrecord.appointment_date', $dayTimestamp);
            // } elseif ($visitStatus == 0) {
            //     $criteria->addCondition("newrecord.appointment_date={$dayTimestamp} OR newrecord.visit_timestamp={$dayTimestamp}");
            // }
            $dayTimestampStar = strtotime($daytimeStar);
            $dayTimestampEnd = strtotime($daytimeEnd);

            if ($visitStatus == 'visit') {
                if(!$dayTimestampStar && $dayTimestampEnd){
                    $criteria->compare('newrecord.visit_timestamp', "<=$dayTimestampEnd");
                }elseif($dayTimestampStar && !$dayTimestampEnd){
                    $criteria->compare('newrecord.visit_timestamp', ">=$dayTimestampStar");
                }elseif ($dayTimestampStar && $dayTimestampEnd){
                    $criteria->compare('newrecord.visit_timestamp', ">=$dayTimestampStar");
                    $criteria->compare('newrecord.visit_timestamp', "<=$dayTimestampEnd");
                }
                //$criteria2->compare('visit_timestamp', $dayTimestamp);
            } elseif ($visitStatus == 'appointment') {
                if(!$dayTimestampStar && $dayTimestampEnd){
                    $criteria->compare('newrecord.appointment_date', "<=$dayTimestampEnd");
                }elseif($dayTimestampStar && !$dayTimestampEnd){
                    $criteria->compare('newrecord.appointment_date', ">=$dayTimestampStar");
                }elseif ($dayTimestampStar && $dayTimestampEnd){
                    $criteria->compare('newrecord.appointment_date', "<=$dayTimestampEnd");
                    $criteria->compare('newrecord.appointment_date', ">=$dayTimestampStar");
                }
            } elseif ($visitStatus == 'transfer') {
                if(!$dayTimestampStar && $dayTimestampEnd){
                    $criteria->compare('newrecord.update_timestamp', "<=$dayTimestampEnd");
                }elseif($dayTimestampStar && !$dayTimestampEnd){
                    $criteria->compare('newrecord.update_timestamp', ">=$dayTimestampStar");
                }elseif ($dayTimestampStar && $dayTimestampEnd){
                    $criteria->compare('newrecord.update_timestamp', ">=$dayTimestampStar");
                    $criteria->compare('newrecord.update_timestamp', "<=$dayTimestampEnd");
                }
            } elseif ($visitStatus == 0) {
                if(!$dayTimestampStar && $dayTimestampEnd){
                    $criteria->addCondition("newrecord.appointment_date<={$dayTimestampEnd} OR newrecord.visit_timestamp<={$dayTimestampEnd} OR newrecord.update_timestamp<={$dayTimestampEnd}");
                }elseif($dayTimestampStar && !$dayTimestampEnd){
                    $criteria->addCondition("newrecord.appointment_date>={$dayTimestampStar} OR newrecord.visit_timestamp>={$dayTimestampStar} OR newrecord.update_timestamp>={$dayTimestampStar}");
                }elseif ($dayTimestampStar && $dayTimestampEnd){
                    $criteria->addCondition("(newrecord.appointment_date>={$dayTimestampStar} and newrecord.appointment_date<={$dayTimestampEnd}) OR (newrecord.visit_timestamp>={$dayTimestampStar} && newrecord.visit_timestamp<={$dayTimestampEnd})OR (newrecord.update_timestamp>={$dayTimestampStar} && newrecord.update_timestamp<={$dayTimestampEnd})");
                }
            }
        }
        if($age){
            $timeStamp = strtotime("-$age year");
            $bigTime = strtotime("-".($age-1)." year");
            $criteria->compare('basic.birth_timestamp', "<{$bigTime}");
            $criteria->compare('basic.birth_timestamp', ">{$timeStamp}");
        }

        if($search) $criteria->addCondition("concat_ws(',',basic.child_name,basic.parent_name,basic.tel,basic.email,basic.address) like '%{$search}%' ");
        $criteria->compare('newrecord.schoolid', $this->branchId);


        $dataProvider = new CActiveDataProvider(new VisitsBasicLink, array(
            'criteria'=>$criteria,
            'pagination'=>array('pageSize'=>20),
            'sort' => array(
                'defaultOrder' => 'basic.update_timestamp DESC',
            ),
        ));

        $criteria = new CDbCriteria;
        $criteria->order = 'id DESC';
        $criteria->compare('school_id',$this->branchId);
        $schoolEvents = SchoolEvents::model()->findAll($criteria);

        $events = array('全部活动');
        foreach ($schoolEvents as $schoolEvent) {
            $events[$schoolEvent->id] .= Yii::app()->language == 'zh_cn' ? $schoolEvent->cn_title : $schoolEvent->en_title;
            $events[$schoolEvent->id] .= date('Y/m/d', $schoolEvent->event_date);
        }


        if($dataProvider){
            $data = array();
            foreach ($dataProvider->getData() as $val){
                $data[] = $val->basic->tel;
            }
            if($data){
                $fatherArr = array();
                $criteria = new CDbCriteria;
                $criteria->compare('father_phone',$data);
                $fatherModel = AdmissionsDs::model()->findAll($criteria);
                if($fatherModel){
                    foreach ($fatherModel as $val) {
                        $fatherArr[$val->father_phone] = 1;
                    }
                }
                $motherrArr = array();
                $criteria = new CDbCriteria;
                $criteria->compare('mother_phone',$data);
                $motherModel = AdmissionsDs::model()->findAll($criteria);
                if($motherModel){
                    foreach ($motherModel as $val) {
                        $motherrArr[$val->mother_phone] = 1;
                    }
                }
                $this->parents = array_merge($fatherArr,$motherrArr);
            }
        }

        $this->render('visit/visitsLog', array(
            'dataProvider' => $dataProvider,
            'events' => $events,
        ));
    }

    /**
     * 更新来访信息
     */
    public function actionBasicUpdate()
    {
        $id = Yii::app()->request->getParam('id', '');
        $model = IvyVisitsBasicInfo::model()->findByPk($id);
        $model->register_timestamp = $model->register_timestamp ? $model->register_timestamp:'';
        $model->birth_timestamp = $model->birth_timestamp ? date('Y-m-d', $model->birth_timestamp):'';
        $model->child_enroll = $model->child_enroll ? date('Y-m-d', $model->child_enroll):'';
        $model->concerns = explode('|', $model->concerns);

        if(isset($_POST['IvyVisitsBasicInfo']))
        {
            $model->attributes = $_POST['IvyVisitsBasicInfo'];
            $model->birth_timestamp = strtotime($model->birth_timestamp);
            $model->child_enroll = $model->child_enroll?strtotime($model->child_enroll):0;
            $model->update_timestamp = time();
            $model->update_user = $this->staff->uid;
            $model->concerns = implode('|', $model->concerns);
            if($model->save()){
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message','success'));
                $this->addMessage('callback', 'cbVisit');
            }
            else {
                $this->addMessage('state', 'fail');
                $err = current($model->getErrors());
                $this->addMessage('message', $err ? $err[0] : '失败');
            }
            $this->showMessage();
        }
        $this->renderpartial('visit/basicUpdate',array(
            'model'=>$model,
            'modalTitle'=>'更新来访信息'
        ));
    }

    /**
     * 更新追踪记录
     */
    public function actionTraceLog()
    {
        $visitId = Yii::app()->request->getParam('vid', '');
        $basicId = Yii::app()->request->getParam('bid', '');
        $schoolid = $this->branchId;

        if(!$visitId || !$basicId){
            return false;
        }

        $basicInfo = IvyVisitsBasicInfo::model()->findByPk($basicId);

        $criteria = new CDbCriteria;
        $criteria->compare('school_id',$this->branchId);
        $criteria->compare('basic_id',$basicInfo->id);
        $visitBasixLinkModel = VisitsBasicLink::model()->find($criteria);
        if($visitBasixLinkModel){
            $basicInfo->status = $visitBasixLinkModel->status;
        }
        $criteria = new CDbCriteria;
        $criteria->compare('basic_id', $basicId);
        $criteria->order = 'update_timestamp';

        if ($_POST['VisitsTraceLog']) {
            $model = new VisitsTraceLog();
            $model->attributes = $_POST['VisitsTraceLog'];
            if ($model->content == '') {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '追踪内容不能为空！');
                $this->showMessage();
            }
            $model->schoolid = $this->branchId;
            $model->update_timestamp = time();
            $model->type = 10;
            $model->update_user = Yii::app()->user->id;
            if (!$model->save()) {
                $error = current($model->getErrors());
                $this->addMessage('state', 'fail');
                $this->addMessage('message', $error);
                $this->showMessage();
            }
            if($_POST['IvyVisitsBasicInfo']){
                $basicInfo->attributes = $_POST['IvyVisitsBasicInfo'];
                $basicInfo->update_timestamp = time();
                $basicInfo->recent_log = time();
                $basicInfo->update_user = Yii::app()->user->id;
                $basicInfo->save();

                if($visitBasixLinkModel){
                    $visitBasixLinkModel->status = $basicInfo->status;
                    $visitBasixLinkModel->save();
                }

            }
            $this->addMessage('state', 'success');
            $this->addMessage('message', Yii::t('message','success'));
            $this->addMessage('callback', 'cbVisit');
            $this->showMessage();
        }

        $traceLogs = VisitsTraceLog::model()->findAll($criteria);
        $this->renderpartial('visit/traceLog', array(
            'traceLogs' => $traceLogs,
            'basicInfo' => $basicInfo,
            'records' => $basicInfo->record,
            'visitId' => $visitId,
            'basicId' => $basicId,
            'modalTitle'=>'添加追踪记录'
        ));
    }

    /**
     * 转移孩子
     */
    public function actionTransfer()
    {
        $visitId = Yii::app()->request->getParam('id', '');
        $childids = Yii::app()->request->getParam('childid', '');
        $schoolid = Yii::app()->request->getParam('schoolid', '');
        $transferBranchId = Yii::app()->request->getParam('transferBranchId', '');
        Yii::import('common.models.hr.*');
        Yii::import('common.models.*');
        Yii::import('common.models.models.*');

        $criteria = new CDbCriteria;
        $criteria->compare('id', $schoolid['childid']);
        $childid = IvyVisitsBasicInfo::model()->find($criteria);

        $criteria = new CDbCriteria;
        $criteria->compare('branchid', $transferBranchId);
        $emailid = BranchInfo::model()->findAll($criteria);;

        if($transferBranchId){
            $criteria = new CDbCriteria;
            $criteria->compare('id', $schoolid['schoolid']);
            $transfer = IvyVisitsRecord::model()->find($criteria);
            $transfer->status = 20;
            $transfer->save();

            $model = new IvyVisitsRecord();
            $model->basic_id = $schoolid['childid'];
            $model->schoolid = $transferBranchId;
            $model->category = 'transfer';
            $model->update_timestamp = time();
            $model->status = IvyVisitsRecord::VISITS_STATUS;
            $model->update_user = Yii::app()->user->id;
            $model->save();
            $schoolidAll = Branch::model()->getBranchList();
            foreach($schoolidAll as $key=>$value){
                if($key == $transferBranchId){
                    $schools = $value;
                }
                if($key == $this->branchId){
                    $schooltow = $value;
                }
            }

            $visitRecord = new VisitsBasicLink();
            $visitRecord->school_id = $transferBranchId;
            $visitRecord->basic_id = $schoolid['childid'];
            $visitRecord->visit_id = $model->id;
            $visitRecord->status = 10;
            $visitRecord->updated_at = time();
            $visitRecord->updated_by = Yii::app()->user->id;
            $visitRecord->save();


            $visits = new VisitsTraceLog();
            $visits->basic_id = $schoolid['childid'];
            $visits->visit_id = $model->id;
            $visits->type = 40;
            $visits->content = $schooltow .'转给'. $schools .'一个潜在客户';
            $visits->schoolid = $transferBranchId;
            $visits->update_timestamp = time();
            $visits->update_user = Yii::app()->user->id;
            $visits->save();

            $VisitsBasicInfo=IvyVisitsBasicInfo::model()->findByPk($schoolid['childid']);
            $VisitsBasicInfo->recent_log = time();
            $VisitsBasicInfo->save();

            $mailer = Yii::createComponent('common.extensions.mailer.EMailer');
            $mailer->Subject = sprintf('%s转来一个潜在客户', $schooltow);

            foreach($emailid as $k=>$val){
                if($val->branchid == $transferBranchId){
                    $mailer->AddAddress($val->support_email);
                }
            }
            if($this->branchId != 'BJ_TYG'){
                $mailer->AddCC($this->branchObj->info->support_email);
            }
            $mailer->getView('checktransfer', array('branchId' => $schooltow, 'hid'=>Yii::app()->user->name, 'childid'=>$childid), 'main');
            $mailer->iniMail( OA::isProduction());
            $mailer->Send();
            if($model->save() && $transfer->save() && $visits->save()){
                $this->addMessage('state', 'success');
                $this->addMessage('message', '转移孩子成功');
                $this->addMessage('callback', 'cbTransfer');
                $this->showMessage();
            }
        }
        $this->renderpartial('visit/transfer', array(
            'schoolidAll' => $schoolidAll,
            'visitId' => $visitId,
            'childids' => $childids
        ));
    }

    /**
     * 关联孩子
     */
    public function actionLinkChild()
    {
        $basicId = Yii::app()->request->getParam('id', '');
        $model = IvyVisitsBasicInfo::model()->findByPk($basicId);
        if ($model) {
            if (Yii::app()->request->isPostRequest) {
                $linkChild = $_POST['linkChild'];
                if ($linkChild == '') {
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', '不能为空');
                    $this->showMessage();
                }
                if ($linkChild) {
                    $exists = IvyVisitsBasicInfo::model()->exists('childid=:childid', array(':childid'=>$linkChild));
                    if ($exists) {
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', '此孩子已绑定！');
                        $this->showMessage();
                    }
                }
                $model->status = 30;
                if ($linkChild == 0){
                    $model->status = 10;
                }
                $model->childid = $linkChild;
                $model->register_timestamp = time();
                $model->state_timestamp = time();
                if (!$model->save()) {
                    $error = current($model->getErrors());
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', $error ? $error[0] : '失败');
                    $this->showMessage();
                }
                $this->addMessage('state', 'success');
                $this->addMessage('callback', 'cbVisit');
                $this->addMessage('message', 'success');
                $this->showMessage();
            }
            if ($model->status == 30 && $model->childid ) {
                $ftsChild = FtsChild::model()->findByPk($model->childid);
                preg_match('/{(.*)}/', $ftsChild->tdata, $matches);
                $tdata = array($ftsChild->id =>$matches[1], 0=>'解除绑定');
                $select = $ftsChild->id;
            }
        }
        $this->renderpartial('visit/linkChild', array(
            'modalTitle'=>'关联孩子',
            'tdata'=>$tdata,
            'select'=>$select,
            'basicId'=>$basicId,
        ));
    }

    /**
     * 数据分析
     */
    public function actionDataAnalysis($value='')
    {
        $this->branchSelectParams['urlArray'] = array('//mcampus/admissions/DataAnalysis');
        $cfgs = $this->loadConfig();
        $knowusList = array();
        if (isset($cfgs['knowus']))
        {
            foreach ($cfgs['knowus'] as $k=>$v)
            {
                $knowusList[$k]['name'] = (Yii::app()->language=='zh_cn') ? $v['cn'] : $v['en'];
                $knowusList[$k]['y'] = 0;
            }
        }
        $criteria = new CDbCriteria;
        $criteria->compare('status', 30);
        $criteria->select = 'knowus';
        $model = IvyVisitsBasicInfo::model()->findAll($criteria);
        foreach ($model as $basicInfo) {
            if (in_array($basicInfo->knowus, array_keys($knowusList))) {
                $knowusList[$basicInfo->knowus]['y']++;
            }
        }
        $knowusList = array_values($knowusList);
        // foreach ($knowusList as $key => $value) {
        //     $knowusList[$key]['name'] .= $knowusList[$key]['y'];
        // }
        $this->render('dataAnalysis', array(
            'knowusList'=>json_encode($knowusList),
        ));
    }

    /**
     * 测试表单
     */
    public function actionForm()
    {
        $this->branchSelectParams['urlArray'] = array('//mcampus/admissions/form');
        $model = new IvyschoolsVisit();
        $criteria = new CDbCriteria;
        $criteria->compare('schoolid', $this->branchId);
        $visits = VisitsConfig::model()->find($criteria);
        if($visits != null){
            $visits = array(
                'week' => CJSON::decode($visits->week),
                'day' => CJSON::decode($visits->day),
                'time' => CJSON::decode($visits->time),
            );
        }

        $time = array();
        foreach($visits['time'] as $k=>$v){
            $time[$v['time']] = $v['time'];
        }

        foreach($visits['day'] as $k=>$v){
            $day[$v] = $v;
        }

        $cfgs = $this->loadConfig();
        $language = array();
        if (isset($cfgs['language']))
        {
            foreach ($cfgs['language'] as $k=>$v)
            {
                $language[$k] = (Yii::app()->language=='zh_cn') ? $v['cn'] : $v['en'];
            }
        }
        $knowusList = array();
        if (isset($cfgs['knowus']))
        {
            foreach ($cfgs['knowus'] as $k=>$v)
            {
                $knowusList[$k] = (Yii::app()->language=='zh_cn') ? $v['cn'] : $v['en'];
            }
        }

        $this->render('form', array(
            'model'=>$model,
            'week'=>$visits['week'],
            'day'=>$day,
            'time'=>$time,
            'language'=>$language,
            'knowusList'=>$knowusList,
            'branchId'=>$this->branchId,
        ));
    }

    public function actionAddForm()
    {
        $day = Yii::app()->request->getPost('day', 0);
        $receive_language = Yii::app()->request->getPost('receive_language', 0);
        $knowus = Yii::app()->request->getPost('knowus', 0);
        $model = new IvyschoolsVisit();
        $model->attributes =$_POST['IvyschoolsVisit'];
        $model->visit_date =strtotime($model->visit_date);
        $model->birthdate =strtotime($model->birthdate);

        $criteria = new CDbCriteria;
        $criteria->compare('schoolid', $this->branchId);
        $visits = VisitsConfig::model()->find($criteria);

        if($visits['status'] == 0){
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '校园未开启参观');
            $this->showMessage();
        }

        //拿到参观的时间和人数上限
        foreach(CJSON::decode($visits['time']) as $k=>$v){
            if($v['time'] == $day){
                $man = $v['man'];
                $time = $v['time'];
            }
        }

        if($day){
            if(!$time == $day){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '您选的参观时间不正确');
                $this->showMessage();
            }
        }

        //判断参观的日期
        if($model->visit_date){
            foreach(CJSON::decode($visits['week']) as $k=>$v){
                if(date('w',$model->visit_date) == $v){
                    $i = 1;
                }
            }

            foreach(CJSON::decode($visits['day']) as $k=>$v){
                if(strtotime($v) == $model->visit_date){
                    $s = 1;
                }
            }

            if($model->visit_date < time() || empty($i) && empty($s)){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '您选的日期不正确');
                $this->showMessage();
            }else{
                $criteria = new CDbCriteria;
                $criteria->compare('schoolid', $this->branchId);
                $criteria->compare('visit_date', $model->visit_date);
                $criteria->compare('visit_time', $time);
                $visitCount = IvyschoolsVisit::model()->count($criteria);
                if($visitCount >= $man){
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', '不好意思这个时段参观人数达到上限');
                    $this->showMessage();
                }
            }
        }

        $model->schoolid = $this->branchId;
        $model->visit_time = $day;
        $model->receive_language = $receive_language;
        $model->knowus     = $knowus;
        $model->create_date     = time();
        $model->ip    = Yii::app()->request->userHostAddress;
        $model->concerns = implode('|', $model->concerns);

        if($model->save()){
            $this->addMessage('state', 'success');
            $this->addMessage('message', Yii::t('message','Data Saved!'));
            $this->addMessage('callback', 'cbAddForm');
        }else{
            $this->addMessage('state', 'fail');
            $errs = current($model->getErrors());
            $this->addMessage('message', $errs?$errs[0]:Yii::t('message','Failed!'));
        }
        $this->showMessage();
    }

    // 申请入学
    public function actionAdmissions()
    {
        Yii::import('common.models.invoice.*');
        Yii::import('common.models.calendar.*');

        $this->branchSelectParams['urlArray'] = array('//mcampus/admissions/admissions');
        $username = Yii::app()->request->getParam('username', '');
        $grade = Yii::app()->request->getParam('grade', '');
        $phone = Yii::app()->request->getParam('phone', '');
        $startYear = Yii::app()->request->getParam('startYear', '');
        $status = Yii::app()->request->getParam('status', '');
        $model = new AdmissionsDs;

        $yid = "2016";
        $criteria = new CDbCriteria;
        //$criteria->compare('is_selected', 1);
        $criteria->compare('branchid', $this->branchId);
        $criteria->compare('startyear', ">{$yid}");
        $yidObj = CalendarSchool::model()->findAll($criteria);
        $data = date("Y", time());
        if ((int)date('m', time()) >= 8) {
            $nextYear = $data + 1;
            $nextYears = $data + 2;
        } else {
            $nextYear = $data;
            $nextYears = $data + 1;
        }
        $calender = array('others' => '其他');

        foreach ($yidObj as $val){
            $next =  $val->startyear + 1;
            $calender[$val->startyear] = $val->startyear . " - "  . $next;
            if($val->is_selected == 1){
                array_push($calender, $nextYear . " - "  . $nextYears);

            }
            $next = 0;
        }
        /*Yii::msg($calender);
        $next =  $yidObj->startyear + 1;
        $calender[$yidObj->startyear] = $yidObj->startyear . " - "  . $next;
        array_push($calender, $nextYear . " - "  . $nextYears);
        $this->startyear = $yidObj->startyear;*/
        $criteria = new CDbCriteria;
        $criteria->compare('school_id', $this->branchObj->abb);
        /*if($age){
            $timeStamp = strtotime("-$age year");
            $bigTime = strtotime("-".($age-1)." year");
            $criteria->compare('birthday', "<{$bigTime}");
            $criteria->compare('birthday', ">{$timeStamp}");
        }*/
        if($username){$criteria->addCondition("concat_ws('',en_name,en_name_middle,en_name_last,cn_name,cn_name_last) like '%{$username}%' ");}
        if($phone){$criteria->addCondition("concat_ws(',',father_phone,mother_phone) like '%{$phone}%' ");}
        if($grade){$criteria->compare('start_grade', $grade);}

        if($status || $status === '0'){
            $criteria->compare('status', $status);
        }else{
            $criteria->compare('status', "<>99");
        }
        //Yii::msg($criteria);
        if($startYear){$criteria->compare('start_year', $startYear);}

        $dataProvider = new CActiveDataProvider($model, array(
            'criteria'=>$criteria,
            'sort' => array(
                'defaultOrder' => 'add_timestamp DESC',
            ),
            'pagination'=>array(
                'pageSize'=>20,
            ),
        ));
        $cfg = AdmissionsDs::getConfig();
        $cfgs = $this->loadConfig();

        $this->calenders = $calender;
        $this->render('admissions/index', array(
            'dataProvider' => $dataProvider,
            'cfgs' => $cfgs,
            'cfg' => $cfg,
            'calender' => $calender,
        ));
    }

    public function actionAdmissionsDelete()
    {
        $id = Yii::app()->request->getParam('id', '');
        $Adminstatus = Yii::app()->request->getParam('status', '');
        $model = AdmissionsDs::model()->findByPk($id);
        if($model && $Adminstatus){
            $status = ($Adminstatus == AdmissionsDs::STATS_STATS_TEMPORARY) ? 89 : 99;
            $model->status = $status;
            if($model->save()){
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message','success'));
                $this->addMessage('callback', 'cbVisitsasdasd');
            }else{
                $this->addMessage('state', 'fail');
                $err = current($model->getErrors());
                $this->addMessage('message', $err ? $err[0] : '失败');
            }
            $this->showMessage();
        }else{
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '非法操作');
            $this->showMessage();
        }
    }

    // 更新基本资料
    public function actionAdmissionsUpdate()
    {
        Yii::import('common.models.calendar.*');
        $id = Yii::app()->request->getParam('id', '');
        $model = AdmissionsDs::model()->findByPk($id);
        $cfg = AdmissionsDs::getConfig();
        $criteria = new CDbCriteria;
        $criteria->compare('status', Branch::TYPE_OFFICE);
        $criteria->index = 'abb';
        $branchObj = Branch::model()->findAll();
        $schoolList = array();
        foreach($branchObj as $item){
            $schoolList[$item->abb] = $item->title;
        }

        $yid = "2016";
        $criteria = new CDbCriteria;
        $criteria->compare('branchid', $this->branchId);
        $criteria->compare('startyear', ">{$yid}");
        $yidObj = CalendarSchool::model()->findAll($criteria);
        $calender = array('others' => '其他');
        foreach ($yidObj as $val) {
            $next =  $val->startyear + 1;
            $calender[$val->startyear] = $val->startyear . ' - ' . $next;
        }

        if(isset($_POST['AdmissionsDs']))
        {
            $model->attributes = $_POST['AdmissionsDs'];
            $model->birthday = strtotime($model->birthday);
            //$model->valid_date = strtotime($model->valid_date);
            //$model->resident_date = strtotime($model->resident_date);
            $model->start_year = $_POST['AdmissionsDs']['start_year'];
            $model->start_date = ($_POST['AdmissionsDs']['start_date']) ? strtotime($_POST['AdmissionsDs']['start_date']) : "";
            if($_POST['AdmissionsDs']['status']){
                $model->status = ($_POST['AdmissionsDs']['status']);
            }
            if($_POST['AdmissionsDs']['is_recommended']){
                $model->is_recommended = $_POST['AdmissionsDs']['is_recommended'];
            }
            if($_POST['AdmissionsDs']['is_siblings']){
                $model->is_siblings = $_POST['AdmissionsDs']['is_siblings'];
            }

            $studyHistory = array();
            $siblingInfo = array();
            if (!$model->en_name && !$model->en_name_last && !$model->cn_name && !$model->cn_name_last) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('user', 'Student Name At least one entry.'));
                $this->showMessage();
            }


            $flag = false;
            if ($model->father_name && $model->father_phone && $model->father_email) {
                $flag = true;
            }
            if ($model->mother_name && $model->mother_phone && $model->mother_email) {
                $flag = true;
            }
            if (!$flag) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('user', 'Guardian information required: name, phone, email'));
                $this->showMessage();
            }

            if ($_POST['study-history']) {
                foreach ($_POST['study-history'] as $k=>$items) {
                    foreach ($items as $i=>$item) {
                        $studyHistory[$i][$k] = $item;
                    }
                }
            }
            $model->school_history = json_encode($studyHistory);

            if(in_array($model->start_grade, array(3,4)) && in_array($this->branchId, array('BJ_DS','BJ_SLT'))){
                if(!$_POST['AdmissionsDs']['student_status1']){
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', Yii::t('user', '学籍情况不能为空'));
                    $this->showMessage();
                }
                if($_POST['AdmissionsDs']['student_status1'] == 2){
                    if(empty($_POST['AdmissionsDs']['house1'])){
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', Yii::t('user', '住房情况不能为空'));
                        $this->showMessage();
                    }
                    if(empty($_POST['AdmissionsDs']['social_security'])){
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', Yii::t('user', '父母社保情况不能为空'));
                        $this->showMessage();
                    }
                }
                if($_POST['AdmissionsDs']['student_status1'] == 3){
                    if(empty($_POST['AdmissionsDs']['house2'])){
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', Yii::t('user', '住房情况不能为空'));
                        $this->showMessage();
                    }
                    if(empty($_POST['AdmissionsDs']['parentsSituation'])){
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', Yii::t('user', '父母工作居住证情况不能为空'));
                        $this->showMessage();
                    }
                }
                if($_POST['AdmissionsDs']['student_status1'] == 4){
                    if(empty($_POST['AdmissionsDs']['house3'])){
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', Yii::t('user', '住房情况不能为空'));
                        $this->showMessage();
                    }
                    if(empty($_POST['AdmissionsDs']['visa_type'])){
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', Yii::t('user', '签证类型不能为空'));
                        $this->showMessage();
                    }
                }
            }
            if($model->start_grade > 4 && in_array($this->branchId, array('BJ_DS','BJ_SLT'))){
                if(!$_POST['AdmissionsDs']['student_status2']){
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', Yii::t('user', '学籍情况不能为空'));
                    $this->showMessage();
                }
            }

            $schoolRoll = array(
                'student_status1' => $_POST['AdmissionsDs']['student_status1'],
                'house1' => $_POST['AdmissionsDs']['house1'],
                'house2' => $_POST['AdmissionsDs']['house2'],
                'house3' => $_POST['AdmissionsDs']['house3'],
                'social_security' => $_POST['AdmissionsDs']['social_security'],
                'parentsSituation' => $_POST['AdmissionsDs']['parentsSituation'],
                'visa_type' => $_POST['AdmissionsDs']['visa_type'],
                'student_status2' => $_POST['AdmissionsDs']['student_status2'],
            );

            $model->school_roll = json_encode($schoolRoll);

            if ($_POST['sibling-info']) {
                foreach ($_POST['sibling-info'] as $k=>$items) {
                    foreach ($items as $i=>$item) {
                        $siblingInfo[$i][$k] = $item;
                    }
                }
            }

            if($model->save()){
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message','success'));
                $this->addMessage('callback', 'cbVisit');
            }else{
                $this->addMessage('state', 'fail');
                $err = current($model->getErrors());
                $this->addMessage('message', $err ? $err[0] : '失败');
            }
            $this->showMessage();
        }

        $model->birthday = date('Y-m-d', $model->birthday);
        $model->valid_date = date('Y-m-d', $model->valid_date);
        $model->resident_date = date('Y-m-d', $model->resident_date);
        if($model->start_date){
            $model->start_date = date('Y-m-d', $model->start_date);
        }

        // 获取文件临时地址
        $osscs = CommonUtils::initOSSCS('private');
        if ($model->child_avatar) {
            $object = 'admissions/' . $model->child_avatar;
            $style = 'style/w200';
            $model->child_avatar = $osscs->getImageUrl($object, $style);
        }
        if ($model->persion_copy) {
            $model->persion_copy = json_decode($model->persion_copy);

        }
        if ($model->recommendation_form) {
            $model->recommendation_form = json_decode($model->recommendation_form);

        }
        if ($model->academic_report) {
            $model->academic_report = json_decode($model->academic_report);

        }
        if ($model->valid_date == "1970-01-01") {
            $model->valid_date = "";
        }
        if ($model->resident_date == "1970-01-01") {
            $model->resident_date = "";
        }

        $schoolRool = ($model->school_roll) ? json_decode($model->school_roll,true) : "";

        $model->student_status1 = $schoolRool['student_status1'];
        $model->student_status2 = $schoolRool['student_status2'];
        $model->house1 = $schoolRool['house1'];
        $model->house2 = $schoolRool['house2'];
        $model->house3 = $schoolRool['house3'];
        $model->visa_type = $schoolRool['visa_type'];
        $model->parentsSituation = $schoolRool['parentsSituation'];
        $model->social_security = $schoolRool['social_security'];

        $view = in_array($this->branchId, array('BJ_DS', 'BJ_SLT')) ? 'admissions/updateDs'  : 'admissions/update' ;

        $this->renderpartial($view,array(
            'model'=>$model,
            'schoolList'=>$schoolList,
            'cfg'=>$cfg,
            'calender'=>$calender,
            'modalTitle'=>'更新申请入学'
        ));
    }

    // 管理页面
    public function actionAdmissionsManagement($id=0)
    {
        if ($id) {
            $child = AdmissionsDs::model()->findByPk($id);

            if ($child) {
                Yii::import('common.models.invoice.*');

                $criteria = new CDbCriteria();
                $criteria->compare('child_id', $id);
                $childinterview = ChildInterview::model()->find($criteria);


                $old_child = array();
                //if($child->status == AdmissionsDs::STATS__INTERVIEW_PASS){ // 转移孩子到正式 查询之前是否有过孩子在艾毅过学
                if($child->father_email &&  $child->father_phone){
                    $criteria = new CDbCriteria;
                    $criteria->compare('email', $child->father_email);
                    $father_email = User::model()->find($criteria);

                    if($father_email){
                        $old_child =  $father_email->uid;
                        if(empty($old_child)){
                            $criteria = new CDbCriteria;
                            $criteria->compare('email', $child->father_phone);
                            $father_phone = User::model()->find($criteria);
                            if($father_phone){
                                $old_child =  $father_phone->uid;
                            }
                        }
                    }
                }

                if($child->mother_email &&  $child->mother_phone && !$old_child){
                    $criteria = new CDbCriteria;
                    $criteria->compare('email', $child->mother_email);
                    $mother_email = User::model()->find($criteria);
                    if($mother_email){
                        $old_child =  $mother_email->uid;
                        if(!$old_child){
                            $criteria = new CDbCriteria;
                            $criteria->compare('email', $child->mother_phone);
                            $mother_phone = User::model()->find($criteria);
                            if($mother_phone){
                                $old_child =  $mother_phone->uid;
                            }
                        }
                    }
                }
                if($old_child){
                    $criteria = new CDbCriteria;
                    $criteria->compare('fid', $old_child);
                    $before_child =  ChildProfileBasic::model()->findAll($criteria);
                    if(!$before_child){
                        $criteria = new CDbCriteria;
                        $criteria->compare('mid', $old_child);
                        $before_child =  ChildProfileBasic::model()->findAll($criteria);
                    }
                }
                //}


                //所有账单
                $criteria = new CDbCriteria;
                $criteria->compare('admission_id', $id);
                $child_Invoice = Invoice::model()->findAll($criteria);

                Yii::import('common.models.calendar.*');

                $yid = "2016";
                $criteria = new CDbCriteria;
                $criteria->compare('branchid', $this->branchId);
                $criteria->compare('startyear', ">{$yid}");
                $yidObj = CalendarSchool::model()->findAll($criteria);
                $calender = array();
                foreach ($yidObj as $val) {
                    $next =  $val->startyear + 1;
                    $calender[$val->startyear] = $val->startyear . ' - ' . $next;
                }

                $this->render('admissions/management', array(
                    'child' => $child,
                    'childinterview' => $childinterview,
                    'child_Invoice' => $child_Invoice,
                    'before_child' => $before_child,
                    'calender' => $calender,
                ));
            }
        }
    }

    public function actionTransferstudents()
    {
        $childid = Yii::app()->request->getParam('childid', '');
        $old_child = Yii::app()->request->getParam('old_child', '');
        $child = AdmissionsDs::model()->findByPk($childid);
        if($child){
            if($child->father_email){
                $criteria = new CDbCriteria;
                $criteria->compare('email',$child->father_email);
                $criteria->compare('isstaff',1);
                $fModela = User::model()->count($criteria);
            }
            if($child->mother_email){
                $criteria = new CDbCriteria;
                $criteria->compare('email',$child->mother_email);
                $criteria->compare('isstaff',1);
                $fModelb = User::model()->count($criteria);
            }
            if($fModela || $fModelb){
                $this -> addMessage('state', 'fail');
                $this -> addMessage('message', '邮箱不能为员工邮箱');
                $this->showMessage();
            }

            if($child->father_email == $child->mother_email){
                $this -> addMessage('state', 'fail');
                $this -> addMessage('message', '父母邮箱不能相同');
                $this->showMessage();
            }

            $model = new ChildProfileBasic();
            $model->setScenario('regChild');
            $model->name_cn = trim($child->cn_name . $child->cn_name_last);
            $model->schoolid = $this->branchId;
            $model->admission_id = $child->id;
            //$model->first_name_en = $child->cn_name;

            if($child->en_name){
                $model->first_name_en = trim($child->en_name_last ? $child->en_name_last : $child->pinyin_last);
            }

            if($child->en_name_middle){
                $model->middle_name_en = trim($child->en_name_middle ? $child->en_name_middle : "");
            }
            if($child->en_name_last){
                $model->last_name_en = trim($child->en_name ? $child->en_name : $child->pinyin_first);
            }

            if ($child->child_avatar) {
                $cfgs = OA::LoadConfig('CfgPhoto');

                // 获取 OSS 头像地址
                $osscs = CommonUtils::initOSSCS('private');
                $object = 'admissions/' . $child->child_avatar;
                $style = 'style/w200';
                $avatarUrl = $osscs->getImageUrl($object, $style);
                $avatarThumbUrl = $osscs->getImageUrl($object, 'style/w80');

                $params = $cfgs['childPhoto'];
                $avatarExt = substr($child->child_avatar, strpos($child->child_avatar, '.'));
                $subPath = rtrim($params['subDir'], '/') . '/';
                $subThumbPath = rtrim($params['subDir'], '/') . '/thumbs/';
                $fileName = $params['filePrefix'] . uniqid() . $avatarExt;
                $filePath = $subPath . $fileName;
                $fileThumbPath = $subThumbPath . $fileName;

                $res1 = file_put_contents($filePath, file_get_contents($avatarUrl));
                $res2 = file_put_contents($fileThumbPath, file_get_contents($avatarThumbUrl));
                if ($res1 && $res2) {
                    $model->photo = $fileName;
                }else{
                    $model->photo = 'blank.gif';
                }
            }else{
                $model->photo = 'blank.gif';
            }

            $startDate = $child->start_date;
            if(!$startDate){
                if(intval($child->start_year)){
                    Yii::import('common.models.calendar.*');
                    $criteria = new CDbCriteria;
                    $criteria->compare('startyear', $child->start_year);
                    $criteria->compare('branchid', $this->branchId);
                    $calendarObj = CalendarSchool::model()->find($criteria);
                    $data = explode(',', $calendarObj->cTemplate->timepoints);
                    $startDate = $data[0];
                }
            }


            $model->gender = $child->gender;
            $model->birthday = $child->birthday;
            $model->birthday_search = date("Y-m-d H:i:s", $child->birthday);
            $model->country = $child->nationality;
            $model->identity = $child->passportid;
            $model->lang = $child->native_lang;
            $model->est_enter_date = $startDate;
            $model->updated_timestamp = time();
            $model->created_timestamp = time();
            $model->create_uid = Yii::app()->user->id;
            $_fid = 0;
            $_mid = 0;
            if($model->save()){

                if($old_child){
                    $sibModel = ChildProfileBasic::model()->findByPk($old_child);
                    $_fid = $sibModel->fid;
                    $_mid = $sibModel->mid;

                    if($_fid){
                        $fModel = IvyParent::model()->findByPk($_fid);
                        if($fModel != null && is_object($fModel)){
                            $childrenArr = unserialize($fModel->childs);
                            if(!in_array($model->childid, $childrenArr)){
                                $childrenArr[] = $model->childid;
                                $fModel->childs = serialize($childrenArr);
                                $fModel->save();
                            }
                        }
                    }

                    if($_mid){
                        $mModel = IvyParent::model()->findByPk($_mid);
                        if($mModel != null && is_object($mModel)){
                            $childrenArr = unserialize($mModel->childs);
                            if(!in_array($model->childid, $childrenArr)){
                                $childrenArr[] = $model->childid;
                                $mModel->childs = serialize($childrenArr);
                                $mModel->save();
                            }
                        }
                    }

                    $model->fid = $_fid;
                    $model->mid = $_mid;
                    $model->family_id = md5($sibModel->childid);
                    $model->seniority = $model->getSeniority($model->family_id);
                    $model->save();
                }else{

                    if($child->father_email || $child->mother_email){
                        Yii::import('application.components.user.UserApi');
                        if($child->father_email){
                            $default_password = CommonUtils::randPass();
                            $pModel = new User();
                            $pModel->email = $child->father_email;
                            $pModel->name = $child->father_name;
                            $pModel->iniPassword = $pModel->pass = $default_password;
                            $pModel->mphone = $child->father_phone;
                            $fModel = UserApi::createParent($model->childid, $pModel, 1);

                            $fModel->parent->cn_name = $child->father_name;
                            $fModel->parent->country = $child->father_nation;
                            $fModel->parent->company = $child->father_employer;
                            $fModel->parent->job = $child->father_position;
                            $fModel->parent->save();

                            $_fid = $fModel->parent->pid;
                            if($child->father_email){
                                # 发送帐号信息给爸爸
                            }
                        }

                        if($child->mother_email){
                            $default_password = CommonUtils::randPass();
                            $pModel = new User();
                            $pModel->email = $child->mother_email;
                            $pModel->name = $child->mother_name;
                            $pModel->iniPassword = $pModel->pass = $default_password;
                            $pModel->mphone = $child->mother_phone;
                            $mModel = UserApi::createParent($model->childid, $pModel, 2);

                            $mModel->parent->cn_name = $child->mother_name;
                            $mModel->parent->country = $child->mother_nation;
                            $mModel->parent->company = $child->mother_employer;
                            $mModel->parent->job = $child->mother_position;
                            $mModel->parent->save();

                            $_mid = $fModel->parent->pid;
                            if($child->mother_email){
                                # 发送帐号信息给妈妈
                            }
                        }
                    }
                }

                Yii::import('common.models.child.ChildMisc');
                $mMisc = new ChildMisc;
                $mMisc->childid = $model->childid;
                $mMisc->ucontact = $child->emergency_contact_name . " ".  $child->emergency_contact_phone ." ".$child->emergency_contact_email;
                if(!$mMisc->save()){
                    $this->addMessage('state', 'fail');
                    $err = current($mMisc->getErrors());
                    $this->addMessage('message', $err ? $err[0] : '失败');
                    $this->showMessage();
                };

                Yii::import('common.models.child.HomeAddress');
                $homeModel = new HomeAddress;
                $homeModel->childid = $model->childid;
                $homeModel->en_address = $child->home_address;
                $homeModel->fid = $_fid;
                $homeModel->mid = $_mid;
                $homeModel->family_id = $model->family_id;
                if($homeModel->save(false)){
                    $child->status = 50;
                    if($child->save()){
                        $this->addMessage('state', 'success');
                        $this->addMessage('message', "保存成功");
                        $this->addMessage('callback', 'cbSuccess');
                        $this->showMessage();
                    }else{
                        $this->addMessage('state', 'fail');
                        $err = current($child->getErrors());
                        $this->addMessage('message', $err ? $err[0] : '失败');
                        $this->showMessage();
                    }
                }else{
                    echo 1;
                }
            }else{
                $this->addMessage('state', 'fail');
                $err = current($model->getErrors());
                $this->addMessage('message', $err ? $err[0] : '失败');
                $this->showMessage();
            }
        }else{
            $this -> addMessage('state', 'fail');
            $this -> addMessage('message', '没有该孩子');
            $this->showMessage();
        }
    }

    public function actionDeleteChild()
    {
        $childid = Yii::app()->request->getParam('childid', '');
        if($childid){
            $model = AdmissionsDs::model()->findByPk($childid);
            if($model){
                $model->status = AdmissionsDs::STATS__STATS_NOTCOMING;
                if($model->save()){
                    $this->addMessage('state', 'success');
                    $this->addMessage('message', "保存成功");
                    $this->addMessage('callback', 'cbSuccess');
                    $this->showMessage();
                }
            }
        }
    }

    public function actionInterviewlist()
    {
        $this->render('admissions/interviewlist');
    }

    public function actionInterviewTeacherlist(){

        $start = Yii::app()->request->getParam('start', '');
        $end = Yii::app()->request->getParam('end', '');

        $criteria = new CDbCriteria();
        $criteria->compare('school_id', $this->branchId);
        $childInterview = ChildInterviewTeacher::model()->findAll($criteria);
        $teacher_list = array();
        if($childInterview){
            $i = 0;
            foreach($childInterview as $k=>$v){
                switch ($v->childInter->type)
                {
                    case "1":
                        $type = '中文';
                        break;
                    case "2":
                        $type = '数学';
                        break;
                    default:
                        $type = '外语';
                        break;
                }
                if($v->childInter->interview_time >= strtotime($start) && $v->childInter->interview_time <= strtotime($end)){
                    $teacher_list[$i]['title'] = $type . "--" . $v->user->getName();
                    $teacher_list[$i]['start'] = date('Y-m-d', $v->childInter->interview_time) . 'T' . date('H:i', $v->childInter->interview_time);
                    $i++;
                }
            }
            echo json_encode($teacher_list);
        }
    }

    //面试表
    public function actionInterview()
    {
        $childid = Yii::app()->request->getParam('childid', '');
        $ChildInterview = Yii::app()->request->getParam('ChildInterview', '');
        $interview = Yii::app()->request->getParam('interview', '');
        if(empty($interview['hours']) && empty($interview['minutes'])){
            $this->addMessage('state', 'fail');
            $this->addMessage('message', "面试时间不能为空");
            $this->showMessage();
        }
        if($childid && $ChildInterview['interview_time']){
            $criteria = new CDbCriteria();
            $criteria->compare('child_id', $childid);
            $childInterview = ChildInterview::model()->find($criteria);
            if(is_null($childInterview)){
                $childInterview = new ChildInterview();
            }
            $interview_time = strtotime($ChildInterview['interview_time'] . " " . $interview['hours'] . ":" . $interview['minutes']);
            $childInterview->interview_time = $interview_time;
            $childInterview->school_id = $this->branchId;
            $childInterview->child_id = $childid;
            $childInterview->stat = 0;
            $childInterview->type = 0;
            $childInterview->updated_time = time();
            $childInterview->uid = Yii::app()->user->id;
            if($childInterview->save()){
                $child = AdmissionsDs::model()->findByPk($childid);
                if($child->status < 10){
                    $child->status = 10;
                    $child->save();
                }

                if($child->status == 30){
                    $child->status = 35;
                    $child->save();
                }

                $allbranches = $this->getAllBranch();
                $branchInFo = BranchInfo::model()->findByPk($this->branchId);
                $teacher_email = ($branchInFo->admissions_email) ? array($branchInFo->admissions_email) : array($branchInFo->email);
                $course = ChildInterview::getConfig();

                $to = array($child->father_email, $child->mother_email);
                $sub = 'Ivy Schools | Interview Schedule 艾毅幼儿园学校面试安排';
                $logo = 'http://ivyschools-www-uploads.oss-cn-beijing.aliyuncs.com/files/ivymailfooter.png';
                if($this->branchObj->type == 50){
                    $sub = 'Daystar Academy | Interview Schedule 启明星学校面试安排';
                    $logo = 'http://ivyschools-www-uploads.oss-cn-beijing.aliyuncs.com/files/dsmailfooter.png';
                }

                $tem = 'primary_school';        // 小学
                if(in_array($child->start_grade, array(1,2,3,21,22,23,24))){
                    $tem = 'kindergarten';  // 幼儿园
                }

                $cc = $teacher_email;
                $data = array("child" => $child,'childInterview' => $childInterview, 'type' => $this->branchObj->type, 'allbranches' => $allbranches, 'branchInFo' => $branchInFo, 'logo' => $logo);
                $this->mailTo($to, $sub, $tem, $cc, $data);

                $this->addMessage('state', 'success');
                $this->addMessage('message', "保存成功");
                $this->addMessage('callback', 'cbSuccess');
            }else{
                $this->addMessage('state','fail');
                $errs = current($childInterview->getErrors());
                $this->addMessage('message',$errs[0]);
            }
            $this->showMessage();
        }else{
            $this->addMessage('state', 'fail');
            $this->addMessage('message', "面试日期不能为空");
            $this->showMessage();
        }
        /*$course = ChildInterview::getConfig();
        $to = array($child->father_email, $child->mother_email);
        $sub = 'Daystar Academy 启明星双语学校 | Interview Schedule 面试安排';
        $tem = 'dsadmission_interview';
        $cc = $teacher_email;
        $data = array("child_name" => $child->getName(), 'typeInfo' => $typeInfo, "course" => $course['course'] , "teacher_list" => $teacher_list);
        $this->mailTo($to, $sub, $tem, $cc, $data);*/
    }

    // 取消面试
    public function actionCancelInterview()
    {
        if (Yii::app()->request->isPostRequest) {
            $interviewId = Yii::app()->request->getParam('interviewId', 0);
            if ($interviewId && $model = ChildInterview::model()->findByPk($interviewId)) {
                $model->delete();
                foreach ($model->childProfile as $v) {
                    $v->delete();
                }
                if ($model->admissionsds_child->status == AdmissionsDs::STATS__TRANSFER_CHILD) {
                    $model->admissionsds_child->status = AdmissionsDs::STATS_REGISTERED;
                }

                if ($model->admissionsds_child->status == AdmissionsDs::STATS_WAITINTERVIEW) {
                    $model->admissionsds_child->status = AdmissionsDs::STATS_HAS_BEEN_PAID;
                }
                $model->admissionsds_child->save();
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message','success'));
                $this->addMessage('refresh', true);
                $this->showMessage();
            }
        }
    }

    //面试结果
    public function actionInterviewResults()
    {
        $childid = Yii::app()->request->getParam('childid', '');
        $interview = Yii::app()->request->getParam('interview', array());
        $stat = Yii::app()->request->getParam('stat', '0');
        $statList = array(
            AdmissionsDs::STATS__INTERVIEW_PASS, //40
            AdmissionsDs::STATS__INTERVIEW_OUT, // 41
            AdmissionsDs::STATS__INTERVIEW_ALTERNATE //42
        );

        $child_status = AdmissionsDs::model()->findByPk($childid);
        $criteria = new CDbCriteria;
        $criteria->compare('child_id', $childid);
        $criteria->compare('school_id', $this->branchId);
        $criteria->order = "type asc";
        $child_list = ChildInterview::model()->find($criteria);
        if(in_array($this->branchId, array('BJ_DS','BJ_SLT')) && !in_array($child_status->start_grade, array(1,2,3,4,21,22,23,24))){
            $interview['interviewDate'] = strtotime($_POST['AdmissionsDs']['interviewDate']);
        }

        if($child_list){
            if($child_status->status != AdmissionsDs::STATS__TRANSFER_CHILD){  //当前状态不等于50 就可以修改   50转移孩子
                $child_list->stat = $stat;
                $child_list->discipline_stat = CJSON::encode($interview);
                $child_list->intreview_content = "0";
                $child_list->type = 0;

                if($child_list->save()){
                    //if(($this->branchObj->type == 50 && in_array($child_list->stat,$statList)) || ($this->branchId == 'BJ_IASLT' &&  $child_list->stat == AdmissionsDs::STATS__INTERVIEW_PASS)){
                    if($this->branchObj->type == 50 && in_array($child_list->stat,$statList)){
                        $school_name = BranchInfo::model()->findByPk($this->branchId);
                        $class = AdmissionsDs::getConfig('en_us');
                        $class_name = array();
                        if($class){
                            $class_name['cn'] = ($this->branchId == 'BJ_IASLT') ? Yii::t('user', $class['grade_ayalt'][$child_status->start_grade], array(), null, 'zh_cn') : Yii::t('user', $class['grade'][$child_status->start_grade], array(), null, 'zh_cn');
                            $class_name['en'] = ($this->branchId == 'BJ_IASLT') ? Yii::t('user', $class['grade_ayalt'][$child_status->start_grade], array(), null, 'en_us') : Yii::t('user', $class['grade'][$child_status->start_grade], array(), null, 'en_us');
                        }
                        $to = array($child_status->father_email, $child_status->mother_email);
                        $sub = 'Ivy Schools Acceptance Letter 艾毅幼儿园录取通知书';
                        $tem = 'admission_accepted';
                        $cc = ($school_name->admissions_email) ? array($school_name->admissions_email) : array($school_name->email);

                        $data = array("child" => $child_status, 'class_name' => $class_name, 'schoolname' => $school_name);
                        if($this->branchObj->type == 50){
                            if($child_list->stat ==  AdmissionsDs::STATS__INTERVIEW_PASS){
                                $sub = 'Daystar Academy Acceptance Letter 启明星学校录取通知书';
                                $tem = 'dsadmission_accepted';
                            }
                            if($child_list->stat ==  AdmissionsDs::STATS__INTERVIEW_OUT){
                                $sub = 'Interview Result Notification from Daystar Academy | 启明星学校面试结果通知';
                                $tem = 'admission_failure';
                                // $data = array("child" => $child_status);
                            }
                            if($child_list->stat ==  AdmissionsDs::STATS__INTERVIEW_ALTERNATE){
                                $sub = 'Waiting List Notice from Daystar Academy | 启明星学校学位候补通知';
                                $tem = 'admission_alternate';
                                // $data = array("child" => $child_status);
                            }
                        }

                        $this->mailTo($to, $sub, $tem, $cc, $data);
                    }

                    if(array_filter(array_values($interview))){
                        $child_status->status = AdmissionsDs::STATS_INTERVIEN_DONE;
                    }
                    if($stat){
                        $child_status->status = $stat;
                    }

                    if(!$child_status->save()){
                        $this->addMessage('state', 'fail');
                        $err = current($child_status->getErrors());
                        $this->addMessage('message', $err ? $err[0] : '失败');
                        $this->showMessage();
                    }
                    //Yii::msg($child_status->status);
                    $this->addMessage('state', 'success');
                    $this->addMessage('message', "保存成功");
                    $this->addMessage('callback', 'cbSuccess');
                    $this->showMessage();
                }else{
                    $this->addMessage('state', 'fail');
                    $err = current($child_list->getErrors());
                    $this->addMessage('message', $err ? $err[0] : '失败');
                    $this->showMessage();
                }
            }else{
                $this->addMessage('state', 'fail');
                $this->addMessage('message', "孩子已经转移到正式学生中");
                $this->showMessage();
            }
        }else{
            $this->addMessage('state', 'fail');
            $this->addMessage('message', "未安排面试");
            $this->showMessage();
        }
    }

    // 导出打印面试结果
    public function actionPrintInterviewResults()
    {
        $childid = Yii::app()->request->getParam('childid', '');
        if($childid){
            $this->layout = '//layouts/print';

            Yii::import('common.models.calendar.*');

            $yid = "2016";
            $criteria = new CDbCriteria;
            $criteria->compare('branchid', $this->branchId);
            $criteria->compare('startyear', ">{$yid}");
            $yidObj = CalendarSchool::model()->findAll($criteria);
            $calender = array();
            foreach ($yidObj as $val) {
                $next =  $val->startyear + 1;
                $calender[$val->startyear] = $val->startyear . ' - ' . $next;
            }

            $childModel = AdmissionsDs::model()->findByPk($childid);
            $criteria = new CDbCriteria();
            $criteria->compare('child_id', $childid);
            $childinterview = ChildInterview::model()->find($criteria);
            if(in_array($childModel->start_grade, array(1,2,3,4,21,22,23,24))){
                $this->printFW = $this->branchObj->getPrintHeader();
                $this->render('admissions/printGraderesults_g', array(
                    'child' => $childModel,
                    'childinterview' => $childinterview,
                    'calender' => $calender,
                ));
            }else{
                $this->render('admissions/printGraderesults', array(
                    'child' => $childModel,
                    'calender' => $calender,
                    'childinterview' => $childinterview,
                ));
            }
        }
    }


    public function actionNointerview()
    {
        Yii::import('common.models.invoice.*');
        Yii::import('common.models.calendar.*');

        $this->branchSelectParams['urlArray'] = array('//mcampus/admissions/admissions');

        $calender = Yii::app()->request->getParam('calender', '');

        $classlist = AdmissionsDs::getConfig();
        if($this->branchId == "BJ_DS"){
            $class_name = $classlist['grade'];
        }
        if($this->branchId == "BJ_SLT"){
            $class_name = $classlist['grade_slt'];
        }
        if($this->branchId == "BJ_IASLT"){
            $class_name = $classlist['grade_ayalt'];
        }

        $crit = new CDbCriteria;
        $crit->select = 'startyear,is_selected';
        $crit->compare('branchid', $this->branchId);
        $crit->order = 'startyear DESC';
        $school_time = CalendarSchool::model()->findAll($crit);

        $school_calendaro = array();
        if($school_time){
            foreach($school_time as $v){
                $school_calendaro[$v->startyear] = $v->startyear .'-' . ($v->startyear + 1);
                if($v->is_selected){
                    $calender_t = $v->startyear;
                }
            }
        }

        $calender = ($calender) ? $calender : $calender_t;

        $criteria = new CDbCriteria;
        $criteria->with = 'admissionsds_child';
        $criteria->compare('t.school_id', $this->branchId);
        $criteria->compare('admissionsds_child.start_year', $calender);
        $childinterview = ChildInterview::model()->findAll($criteria);

        $interviewTime = array();
        $interviewData = array();
        $exportData = array();
        if($childinterview){
            foreach($childinterview as $k=>$v){
                $interviewTime[$v->interview_time] = $v->interview_time;
                $interviewData[$v->interview_time][$v->id]['name'] = $v->admissionsds_child->getName();
                $interviewData[$v->interview_time][$v->id]['stat'] = $v->stat;
                $interviewData[$v->interview_time][$v->id]['grade'] = $class_name[$v->admissionsds_child->start_grade];
                // 构造导出数据
                // if (!isset($exportData[$v->interview_time]['item'][0])) {
                //     $exportData[$v->interview_time]['item'][0] = array('面试日期', '面试时间', '申请学年', '入学年级', '英文名字', '中文名字', '性别', '出生日期', '父亲电话', '母亲电话');
                // }


                $daystar = $this->yid();
                $enName = $v->admissionsds_child->en_name . ' ' . $v->admissionsds_child->en_name_middle . ' ' . $v->admissionsds_child->en_name_last;
                $cnName = $v->admissionsds_child->cn_name . ' ' . $v->admissionsds_child->cn_name_last;
                $exportData[$v->interview_time]['item'][] = array(
                    date("Y-m-d",$v->interview_time),
                    date("H:i",$v->interview_time),
                    $daystar[$v->admissionsds_child->start_year],
                    $class_name[$v->admissionsds_child->start_grade],
                    $enName,
                    $cnName,
                    ($v->admissionsds_child->gender == 1) ? "男" : "女",
                    date("Y-m-d",$v->admissionsds_child->birthday),
                    $v->admissionsds_child->father_phone,
                    $v->admissionsds_child->mother_phone,
                    $v->admissionsds_child->father_email,
                    $v->admissionsds_child->mother_email,
                );
                unset($childinterview[$k]);
            }
        }
        rsort($interviewTime);
        $cfg = AdmissionsDs::getConfig();
        $school_calendaro['others'] = '其他';

        $this->render('admissions/oninterviewlist', array(
            'interviewTime' => $interviewTime,
            'interviewData' => $interviewData,
            'exportData' => $exportData,
            'cfg' => $cfg,
            'school_calendaro' => $school_calendaro,
            'calender' => $calender,
        ));
    }

    //面试老师列表
    public function actionInterviewTeacher()
    {
        $dataProvider = new CActiveDataProvider(new InterviewTeacher, array(
            'criteria'=>"",
            'pagination'=>array('pageSize'=>20),
            'sort' => array(
                'defaultOrder' => 'updated_time DESC',
            ),
        ));

        $this->render('visit/interviewTeacher',array(
            "dataProvider" => $dataProvider,
        ));
    }
    //增加面试老师

    public function actionInterviewTeacherAdd()
    {
        $interviewTeacher = Yii::app()->request->getParam('InterviewTeacher', "");
        if($_POST){
            if($interviewTeacher){
                $teacher_name = array();
                $teachers = UserProfile::model()->findAllByPk($interviewTeacher['teacher_id']);
                foreach($teachers as $teacher_school){
                    if($teacher_school->branch != $this->branchId){
                        $teacher_name[] = $teacher_school->uid;
                    }
                }

                if($teacher_name){
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', Yii::t('management', 'The teacher you chose does not work for this campus'));
                    $this->showMessage();
                }
                foreach($interviewTeacher['teacher_id'] as $teacher){
                    $childInterview = new InterviewTeacher();
                    $childInterview->school_id = $this->branchId;
                    $childInterview->teacher_id = $teacher;
                    $childInterview->updated_time = time();
                    $childInterview->uid = Yii::app()->user->id;
                    $childInterview->save();
                }
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message','success'));
                $this->addMessage('callback', 'cbAddTeacher');
                $this->showMessage();
            }else{
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('management', 'The interview teacher is not available'));
                $this->showMessage();
            }
        }
    }


    public function actionInterviewTeacherDetele()
    {
        if($_POST['id']){
            $teacher_delete = InterviewTeacher::model()->deleteByPk($_POST['id']);
            if($teacher_delete){
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message','success'));
                $this->addMessage('callback', 'cbAddTeacher');
            }else{
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('message','失败'));
            }
            $this->showMessage();
        }
    }

    // 资料审核并开账单
    public function actionAdmissionsInvoice($id=0)
    {
        if (Yii::app()->request->isPostRequest) {
            $child = AdmissionsDs::model()->findByPk($id);
            if ($child) {
                Yii::import('common.models.invoice.*');
                Yii::import('common.models.calendar.*');
                $invoiceModel = new Invoice;
                if (isset($_POST['Invoice'])) {
                    // 判断校历ID
                    $startYear = date('Y', $child->start_date) - 1;
                    if (date('m', $child->start_date) > 8) {
                        $startYear = date('Y', $child->start_date);
                    }
                    $criteria = new CDbCriteria;
                    $criteria->compare('startyear', $startYear);
                    $criteria->compare('branchid', $this->branchId);
                    $calendarId = 1;
                    /*if ($calendar = CalendarSchool::model()->find($criteria)) {
                        $calendarId = $calendar->yid;
                    } else{
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', Yii::t('management','Date of preferred Admission not yet available'));
                        $this->showMessage();
                    }*/
                    // 判断是否存在有效账单
                    $criteria = new CDbCriteria;
                    $criteria->compare('schoolid', $this->branchId);
                    $criteria->compare('admission_id', $child->id);
                    $criteria->compare('status', Invoice::STATS_UNPAID);
                    if (Invoice::model()->exists($criteria)) {
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', Yii::t('management','Unpaid bills'));
                        $this->showMessage();
                    }

                    $invoiceModel->attributes = $_POST['Invoice'];
                    $invoiceModel->calendar_id = $calendarId;
                    $invoiceModel->original_amount = $invoiceModel->amount;
                    $invoiceModel->schoolid = $this->branchId;
                    $invoiceModel->childid = 0;
                    $invoiceModel->payment_type = 'registration';
                    $invoiceModel->fee_type = 0;
                    $invoiceModel->userid = $this->staff->uid;
                    $invoiceModel->timestamp = time();
                    $invoiceModel->duetime = strtotime($invoiceModel->duetime);
                    $invoiceModel->admission_id = $child->id;
                    $invoiceModel->scenario = 'daystar';
                    if ($invoiceModel->save()) {
                        if($child->status < 20){
                            $child->status = 20;
                        }
                        if($child->save()) {
                            $wxpayInfo = CommonUtils::LoadConfig('CfgWxPayGlobal');
                            $cfg = $wxpayInfo[$this->branchId];
                            // 生成支付链接
                            $QRcodeUrl = $invoiceModel->genWXQrcode();
                            // 发送邮件
                            /*$to = array($child->father_email, $child->mother_email);
                            $sub = 'Daystar Academy启明星双语学校 | Thank you for your application 感谢申请启明星';
                            $tem = 'dsadmission_invoice';
                            $cc = array('<EMAIL>');
                            $data = array('QRcodeUrl'=>$QRcodeUrl, 'childid'=>$child->id);
                            $this->mailTo($to, $sub, $tem, $cc, $data);*/

                            $this->addMessage('state', 'success');
                            $this->addMessage('message', Yii::t('message','success'));
                            $this->addMessage('callback', 'cbSuccess');
                            $this->showMessage();
                        } else{
                            $error = current($child->getErrors());
                            $this->addMessage('message', current($error[0]));
                        }
                    } else{
                        $error = current($invoiceModel->getErrors());
                        $this->addMessage('message', $error[0]);
                    }
                    $this->addMessage('state', 'fail');
                    $this->showMessage();
                }
            }
        }
    }

    // 现金付款
    public function actionCashPay($invoiceId, $childid)
    {
        Yii::import('common.models.invoice.*');
        Yii::import('application.components.policy.*');
        if (Yii::app()->request->isAjaxRequest) {
            $policyApi = new PolicyApi($this->branchId);
            $invoiceModel = Invoice::model()->findByPk($invoiceId);
            if (!$invoiceModel) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('management','Bill error'));
                $this->showMessage();
            }

            // 检查账单合法性
            if (!$this->checkInvoice($invoiceModel, $childid)){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('management','Bill is not legal'));
                $this->showMessage();
            }

            if ($policyApi->Pay(array($invoiceModel), InvoiceTransaction::TYPE_CASH, $invoiceModel->amount) !==0){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('management','Payment failed'));
                $this->showMessage();
            }
            $child = AdmissionsDs::model()->findByPk($childid);
            if($child && $child->status < AdmissionsDs::STATS_HAS_BEEN_PAID){

                $criteria = new CDbCriteria();
                $criteria->compare('child_id', $child->id);
                $childInterview = ChildInterview::model()->find($criteria);

                if($childInterview){
                    $child->status = AdmissionsDs::STATS_WAITINTERVIEW;
                }else{
                    $child->status = AdmissionsDs::STATS_HAS_BEEN_PAID;
                }
                $child->save();

            }
            // 设置孩子状态并发送邮件
            //$policyApi->admissionsToEmail($childid);

            $this->addMessage('state', 'success');
            $this->addMessage('message', Yii::t('message','success'));
            $this->addMessage('callback', 'cbSuccess');
            $this->showMessage();
        }
    }

    // 生成POS订单条形码
    public function actionGeneralBarcode($invoiceId, $childid)
    {
        Yii::import('common.models.invoice.*');
        Yii::import('application.components.policy.*');
        if (Yii::app()->request->isAjaxRequest) {
            $invoiceModel = Invoice::model()->findByPk($invoiceId);
            if ($invoiceModel) {
                // 检查账单合法性
                if (!$this->checkInvoice($invoiceModel, $childid)){
                    echo Yii::t('management','Bill is not legal');
                    Yii::app()->end();
                }
                $this->generalYeepayOrder($invoiceModel);
            }
        }
    }

    // 微信支付
    public function actionWxmicropay()
    {
        if(Yii::app()->request->isPostRequest){
            Yii::import('common.models.invoice.*');
            Yii::import('common.models.wxpay.*');
            Yii::import('application.components.policy.*');

            $wxPayCfg = CommonUtils::LoadConfig('CfgWxPayGlobal');

            $childid = Yii::app()->request->getPost('childid', '');
            $invoiceId = Yii::app()->request->getPost('invoiceId', '');
            $authCode = Yii::app()->request->getPost('authCode', '');

            if(!$childid || !$invoiceId || !$authCode){
                $this->addMessage('state','fail');
                $this->addMessage('message',Yii::t("payment", "Page expired, please reload."));
                $this->showMessage();
            }

            // 判断账单和孩子的合法性
            $childObj = AdmissionsDs::model()->findByPk($childid);
            $invoiceObj = Invoice::model()->findByPk($invoiceId);
            if (!$this->checkInvoice($invoiceObj, $childObj->id)) {
                $this->addMessage('state','fail');
                $this->addMessage('message',Yii::t("payment", "Page expired, please reload."));
                $this->showMessage();
            }

            // 生成微信账单
            $schoolid = $invoiceObj->schoolid;
            $totalAmount = $invoiceObj->amount;
            $orderPrefix = strval($wxPayCfg[$schoolid]['number_code']) . sprintf('%06s', $invoiceId);

            $model = new WechatPayOrder;
            $model->orderid = $model->genOrderID($orderPrefix);
            $model->payable_amount = $totalAmount;
            $model->schoolid = $schoolid;
            $model->childid = 0;
            $model->type = 'MICROPAY';
            $model->order_time = time();
            $model->update_timestamp = time();
            $model->uid = Yii::app()->user->id;
            if($model->save()){
                $item = new WechatPayOrderItem();
                $item->orderid = $model->orderid;
                $item->invoice_id = $invoiceObj->invoice_id;
                $item->amount = $totalAmount;
                $item->save();

                $pay = $this->beginWidget('common.extensions.wxPay.MicroPay', array(
                    'auth_code' => $authCode,
                    'body' => $invoiceObj->title,
                    'detail' => $invoiceObj->title,
                    'amount' => $totalAmount*100,
                    'orderid' => $model->orderid,
                    'cfg' => $wxPayCfg[$schoolid],
                ));
                $data = $pay->pay();
                $data = $pay->hideAppid($data);
                if($data['return_code'] == 'SUCCESS'){
                    if($data['result_code'] == 'SUCCESS'){
                        if($model->orderid == $data['out_trade_no']){
                            $model = WechatPayOrder::model()->findByPk($data['out_trade_no']);
                            $model->payInvoice();
                            if($childObj->status < AdmissionsDs::STATS_HAS_BEEN_PAID){

                                $criteria = new CDbCriteria();
                                $criteria->compare('child_id', $childObj->id);
                                $childInterview = ChildInterview::model()->find($criteria);

                                if($childInterview){
                                    $childObj->status = AdmissionsDs::STATS_WAITINTERVIEW;
                                }else{
                                    $childObj->status = AdmissionsDs::STATS_HAS_BEEN_PAID;
                                }
                                $childObj->save();
                            }
                            // 设置孩子状态并发送邮件
                            // $policyApi = new PolicyApi($this->branchId);
                            // $policyApi->admissionsToEmail($childid);AdmissionsDs
                        }
                    }
                    else{
                        $data['out_trade_no'] = $model->orderid;
                    }
                    echo CJSON::encode($data);
                }
            }
        }
    }

    //微信支付结果查询
    public function actionShowmicropay()
    {
        Yii::import('common.models.invoice.*');
        Yii::import('common.models.wxpay.*');

        $orderid = Yii::app()->request->getParam('orderid', '');
        $wxPayCfg = CommonUtils::LoadConfig('CfgWxPayGlobal');
        $micropay = $this->beginWidget('common.extensions.wxPay.MicroPay', array(
            'orderid' => $orderid,
            'cfg' => $wxPayCfg[$this->branchId],
        ));
        $data = $micropay->query();
        $data = $micropay->hideAppid($data);
        if($data['return_code'] == 'SUCCESS' && $data['result_code'] == 'SUCCESS' && $data['trade_state'] == 'SUCCESS'){
            if($orderid == $data['out_trade_no']){
                $model = WechatPayOrder::model()->findByPk($data['out_trade_no']);
                $model->payInvoice();
                $this->addMessage('state', 'success');
                $this->showMessage();
            }
        }
        $this->addMessage('state', 'fail');
        $this->showMessage();
    }

    // 银行转账
    public function actionBanktransfer($invoiceId, $childid)
    {
        if (Yii::app()->request->isAjaxRequest) {
            Yii::import('common.models.invoice.*');
            Yii::import('application.components.policy.*');
            if (isset($_POST['Banktransfer'])) {
                $invoiceModel = Invoice::model()->findByPk($invoiceId);
                if (!$invoiceModel) {
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', Yii::t('management','No bill'));
                    $this->showMessage();
                }
                // 检查账单合法性
                if (!$this->checkInvoice($invoiceModel, $childid)){
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', Yii::t('management','Bill is not legal'));
                    $this->showMessage();
                }
                //验证银行表单合法性
                $banktransferModel = new Banktransfer;
                $banktransferModel->attributes = $_POST['Banktransfer'];
                if (Banktransfer::model()->findByPk($banktransferModel->id)) {
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', '交易流水号已存在。');
                    $this->showMessage();
                }
                $banktransferModel->tamount = $invoiceModel->amount;
                $banktransferModel->bamount = $invoiceModel->amount;
                $banktransferModel->childid = 0;
                $banktransferModel->schoolid = $this->branchId;
                $banktransferModel->btimestamp = strtotime($banktransferModel->btimestamp);
                $banktransferModel->userid  = $this->staff->uid;
                $banktransferModel->otimestamp = time();
                if(!$banktransferModel->validate()) {
                    $error = current($banktransferModel->getErrors());
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', $error[0]);
                    $this->showMessage();
                }

                $policyApi = new PolicyApi($this->branchId);
                if($policyApi->Pay(array($invoiceModel), InvoiceTransaction::TYPE_TRANSFER, $invoiceModel->amount) !==0){
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', Yii::t('management','Payment failed'));
                    $this->showMessage();
                }

                $transaction = Yii::app()->db->beginTransaction();
                try {
                    if (!$banktransferModel->save()) {
                        $error = current($banktransferModel->getErrors());
                        throw new Exception($error[0]);
                    }

                    $banktransferDetailModel = new BanktransferDetail;
                    $banktransferDetailModel->transferid = $banktransferModel->id;
                    $banktransferDetailModel->invoiceid = $invoiceModel->invoice_id;
                    $banktransferDetailModel->tranid = current($invoiceModel->invoiceTransaction)->id;
                    $banktransferDetailModel->childid = 0;
                    if (!$banktransferDetailModel->save()) {
                        $error = current($banktransferDetailModel->getErrors());
                        throw new Exception($error[0]);
                    }
                    $transaction->commit();
                    // 改变孩子状态并发邮件
                    $child = AdmissionsDs::model()->findByPk($childid);
                    if($child->status < AdmissionsDs::STATS_HAS_BEEN_PAID ){
                        $criteria = new CDbCriteria();
                        $criteria->compare('child_id', $child->id);
                        $childInterview = ChildInterview::model()->find($criteria);

                        if($childInterview){
                            $child->status = AdmissionsDs::STATS_WAITINTERVIEW;
                        }else{
                            $child->status = AdmissionsDs::STATS_HAS_BEEN_PAID;
                        }
                        $child->save();
                    }
                    //$policyApi->admissionsToEmail($childid);

                    $this->addMessage('state', 'success');
                    $this->addMessage('message', Yii::t('message','success'));
                    $this->addMessage('callback', 'cbSuccess');
                    $this->showMessage();
                }
                catch (Exception $e) {
                    $transaction->rollBack();
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', $e->getMessage());
                    $this->showMessage();
                }
            }
        }
    }

    //账单作废
    public function actionInvalidInvoice()
    {
        if(Yii::app()->request->isPostRequest){
            Yii::import('common.models.invoice.*');
            $invoiceId = Yii::app()->request->getParam('invoiceId', '');
            $childInterview = Invoice::model()->findByPk($invoiceId);
            if($childInterview){
                $child = AdmissionsDs::model()->findByPk($childInterview->admission_id);
                if($childInterview->delete()){
                    $criteria = new CDbCriteria();
                    $criteria->compare('child_id', $child->id);
                    $childInterview = ChildInterview::model()->find($criteria);

                    if($childInterview){
                        $child->status = AdmissionsDs::STATS_BILLING;
                    }else{
                        $child->status = AdmissionsDs::STATS_REGISTERED;
                    }

                    if($child->save()){
                        $this->addMessage('state', 'success');
                        $this->showMessage();
                    }
                }
            }
        }
    }

    // 催缴账单
    public function actionInvoiceEmail()
    {
        if(Yii::app()->request->isPostRequest){
            Yii::import('common.models.invoice.*');
            $invoiceId = Yii::app()->request->getParam('invoiceId', '');
            $model = Invoice::model()->findByPk($invoiceId);
            $child = AdmissionsDs::model()->findByPk($model->admission_id);
            if($model && $child){
                $wxpayInfo = CommonUtils::LoadConfig('CfgWxPayGlobal');
                $cfg = $wxpayInfo[$this->branchId];
                // 生成支付链接
                $QRcodeUrl = $model->genWXQrcode();
                // 发送邮件
                $to = array($child->father_email, $child->mother_email);
                $sub = 'Daystar Academy启明星学校 | Thank you for your application 感谢申请启明星';
                $tem = 'dsadmission_invoice';
                $data = array('QRcodeUrl'=>$QRcodeUrl, 'childid'=>$child->id);
                $cc = array('<EMAIL>');
                $this->mailTo($to, $sub, $tem, $cc, $data);
                $this->addMessage('state', 'success');
                $this->showMessage();
            }
        }
    }

    // OSS 文件跳转链接
    public function actionOssfileRedit($filePath)
    {
        if ($filePath) {
            $oss = CommonUtils::initOSS('private');
            $filePath = 'admissions/' . $filePath;
            $url = $oss->get_sign_url($filePath);
            Yii::app()->request->redirect($url);
        }
        return false;
    }

    public function actionExportAdmissions()
    {
        Yii::import('common.models.calendar.*');
        $childName = Yii::app()->request->getParam('childName','');
        $phone = Yii::app()->request->getParam('phone','');
        $grade = Yii::app()->request->getParam('grade','') ;
        $status = Yii::app()->request->getParam('status','');
        $startYear = Yii::app()->request->getParam('startYear','');

        $criteria = new CDbCriteria;
        $criteria->compare('school_id', $this->branchObj->abb);
        if($childName){$criteria->addCondition("concat_ws('',en_name,en_name_middle,en_name_last,cn_name,cn_name_last) like '%{$childName}%' ");}
        if($phone){$criteria->addCondition("concat_ws(',',father_phone,mother_phone) like '%{$phone}%' ");}
        if($grade){$criteria->compare('start_grade', $grade);}
        if($status || $status === '0'){
            $criteria->compare('status', $status);
        }else{
            $criteria->compare('status', '<>' . AdmissionsDs::STATS_DROPOUT);
        }
        if($startYear){$criteria->compare('start_year', $startYear);}
        $items =  AdmissionsDs::model()->findAll($criteria);

        $filename = 'Application admission list-'.$this->branchObj->abb;
        $configa = AdmissionsDs::getConfig();
        if($this->branchId == "BJ_DS"){
            $class_name = $configa['grade'];
        }else if($this->branchId == "BJ_SLT"){
            $class_name = $configa['grade_slt'];
        }else{
            $class_name = $configa['grade_ayalt'];
        }

        $criteria = new CDbCriteria;
        $criteria->compare('is_selected', 1);
        $criteria->compare('branchid', $this->branchId);
        $yidObj = CalendarSchool::model()->find($criteria);
        $data = date("Y", time());
        if ((int)date('m', time()) >= 8) {
            $nextYear = $data + 1;
            $nextYears = $data + 2;
        } else {
            $nextYear = $data;
            $nextYears = $data + 1;
        }
        $calender = array('others' => '其他');
        $next =  $yidObj->startyear + 1;
        $calender[$yidObj->startyear] = $yidObj->startyear . " - "  . $next;
        array_push($calender, $nextYear . " - "  . $nextYears);

        $data = array();
        if($items){
            $data['title'] = $this->branchId . "_入学申请.xlsx";
            if(in_array($this->branchId, array('BJ_DS','BJ_SLT'))){
                $data['items'][0] = array('中文姓', '中文名', '英文姓', '英文中间名', '英文名', '拼音姓', '拼音名', '性别', '出生日期', '国籍', '护照/身份证', '母语', '其他语言'
                ,'入学日期','入学年级','家庭住址','是否为艾毅/启明星员工子女','备注','入学年份','是否已有子女在启明星就读','是否为启明星家长推荐'
                ,'学籍情况', '居住情况', '社保情况', '工作居住证', '签证类型','是否参观过校园'
                ,'学校名称','所在城市','就读时间','年级','课程','教学语言'
                , '父亲姓名','父亲国籍','父亲母语','父亲家庭使用的语言','父亲工作单位','父亲职位','父亲学历','父亲联系电话','父亲邮件'
                , '母亲姓名','母亲国籍','母亲母语','母亲家庭使用的语言','母亲工作单位','母亲职位','母亲学历','母亲联系电话','母亲邮件'
                ,'其他信息 - 有无学习困难','您的孩子有什么健康的问题','您的孩子有哪些身体上的残疾或者障碍吗','您的孩子是否有音乐或体育方面的特长','学生照片','一至两年内的学业报告 - 附件'
                ,'学生及家长户口或护照扫描件 - 附件','老师推荐信','状态'
                );
            }else{
                $data['items'][0] = array('中文姓', '中文名', '英文姓', '英文中间名', '英文名', '拼音姓', '拼音名', '性别', '出生日期', '国籍', '护照/身份证', '母语', '其他语言'
                ,'入学日期','入学年级','家庭住址','是否为艾毅/启明星员工子女','备注','入学年份'
                ,'学校名称','所在城市','就读时间','年级','课程','教学语言'
                , '父亲姓名','父亲国籍','父亲母语','父亲家庭使用的语言','父亲工作单位','父亲职位','父亲学历','父亲联系电话','父亲邮件'
                , '母亲姓名','母亲国籍','母亲母语','母亲家庭使用的语言','母亲工作单位','母亲职位','母亲学历','母亲联系电话','母亲邮件'
                ,'其他信息 - 有无学习困难','您的孩子有什么健康的问题','您的孩子有哪些身体上的残疾或者障碍吗','您的孩子是否有音乐或体育方面的特长','学生照片','一至两年内的学业报告 - 附件'
                ,'学生及家长户口或护照扫描件 - 附件','老师推荐信','状态'
                );
            }
        }

        foreach($items as $i => $item){
            $school = "";
            $location = "";
            $studytime = "";
            $grade = "";
            $Curriculum = "";
            $language = "";
            foreach(json_decode($item->school_history) as $it){
                ($it->school) ?  $school .= "\"$it->school\r\n\"" :$school .=  "\"Null\r\n\"";
                ($it->location) ?  $location .= "\"$it->location\r\n\"" :$location .=  "\"Null\r\n\"";
                ($it->studytime) ?  $studytime .= "\"$it->studytime\r\n\"" :$studytime .=  "\"Null\r\n\"";
                ($it->grade) ?  $grade .= "\"$it->grade\r\n\"" :$grade .=  "\"Null\r\n\"";
                ($it->Curriculum) ?  $Curriculum .= "\"$it->Curriculum\r\n\"" :$Curriculum .=  "\"Null\r\n\"";
                ($it->language) ?  $language .= "\"$it->language\r\n\"" :$language .=  "\"Null\r\n\"";
            }
            $name = "";
            $age = "";
            $gender = "";
            $schools = "";
            $grades = "";
            foreach(json_decode($item->sibling_info) as $it){
                ($it->name) ?  $name .= "\"$it->name\r\n\"" : $name .=  "\"Null\r\n\"";
                ($it->age) ?  $age .= "\"$it->age\r\n\"" : $age .=  "\"Null\r\n\"";
                ($it->gender) ?  $gender .= "\"$it->gender\r\n\"" : $gender .=  "\"Null\r\n\"";
                ($it->grade) ?  $grades .= "\"$it->grade\r\n\"" : $grades .=  "\"Null\r\n\"";
                ($it->school) ?  $schools .= "\"$it->school\r\n\"" : $schools .=  "\"Null\r\n\"";
            }

            $school_roll = json_decode($item->school_roll,true);

            switch ($item->status) {
                case AdmissionsDs::STATS_REGISTERED:
                    $modalTitle = '等待安排面试';
                    break;
                case AdmissionsDs::STATS_BILLING:
                    $modalTitle = '开账单';
                    break;
                case AdmissionsDs::STATS_PAYMENT://安排面试
                    $modalTitle = '等待缴费';
                    break;
                case AdmissionsDs::STATS_HAS_BEEN_PAID://面试结果
                    $modalTitle = '已支付';
                    break;
                case AdmissionsDs::STATS__INTERVIEW_PASS://面试结果
                    $modalTitle = '面试通过';
                    break;
                case AdmissionsDs::STATS__INTERVIEW_OUT://面试结果
                    $modalTitle = '面试未通过';
                    break;
                default:
                    $modalTitle = '失效状态';
                    break;
            }
            $array = array();
            $array[] = $item->cn_name;
            $array[] = $item->cn_name_last;
            $array[] = $item->en_name;
            $array[] = $item->en_name_middle;
            $array[] = $item->en_name_last;
            $array[] = $item->pinyin_first;
            $array[] = $item->pinyin_last;
            $array[] = ($item->gender == 1) ? "男" :"女";
            $array[] = $item->birthday ? date('Y-m-d', $item->birthday) : '';
            $array[] = $configa['countries'][$item->nationality];
            $array[] = $item->passportid;
            $array[] = $configa['language'][$item->native_lang];
            $array[] = $configa['language'][$item->other_lang];
            $array[] = $item->start_date ? date('Y-m-d', $item->start_date) : '';
            $array[] = $class_name[$item->start_grade];
            $array[] = $item->home_address;
            $array[] = ($item->is_staff == 1) ? "是" :"否";
            $array[] = nl2br($item->remark);
            $array[] = $calender[$item->start_year];
            if(in_array($this->branchId, array('BJ_DS','BJ_SLT'))){
                $array[] = ($item->is_siblings == 2) ? "是" :"否";
                $array[] = ($item->is_recommended == 2) ? "是" :"否";
            }
            if(in_array($this->branchId, array('BJ_DS','BJ_SLT'))){
                $student_status = '';
                $house = '';
                $social_security = '';
                $parentsSituation = '';
                $visa_type = '';
                if(in_array($item->start_grade, array(3,4))){
                    if($school_roll){
                        $student_status = $configa['student_status'][$school_roll['student_status1']];
                        if($school_roll['student_status1'] == 2){
                            $house = $school_roll['house1'];
                            $social_security = $configa['yes_no'][$school_roll['social_security']];
                        }
                        if($school_roll['student_status1'] == 3){
                            $house = $school_roll['house2'];
                            $parentsSituation = $configa['yes_no'][$school_roll['parentsSituation']];
                        }
                        if($school_roll['student_status1'] == 4){
                            $house = $school_roll['house3'];
                            $visa_type = $configa['visa_type'][$school_roll['visa_type']];
                        }
                        $house = $configa['house'][$house];
                    }
                }
                if($item->start_grade > 4){
                    $student_status = $school_roll['student_status2'];
                }
                $array[] = $student_status;
                $array[] = $house;
                $array[] = $social_security;
                $array[] = $parentsSituation;
                $array[] = $visa_type;
            }

            $isVisit = array(1 => Yii::t('user','No'), 2 => Yii::t('user','Yes'));
            $array[] = ($item->is_visit) ? $isVisit[$item->is_visit] : '';

            $array[] = $school;
            $array[] = $location;
            $array[] = $studytime;
            $array[] = $grade;
            $array[] = $Curriculum;
            $array[] = $language;

            $array[] = $item->father_name;
            $array[] = $configa['countries'][$item->father_nation];
            $array[] = $configa['language'][$item->father_lang];
            $array[] = $configa['language'][$item->father_home_lang];
            $array[] = $item->father_employer;
            $array[] = $item->father_position;
            $array[] = $configa['education'][$item->father_education];
            $array[] = $item->father_phone;
            $array[] = $item->father_email;

            $array[] = $item->mother_name;
            $array[] = $configa['countries'][$item->mother_nation];
            $array[] = $configa['language'][$item->mother_lang];
            $array[] = $configa['language'][$item->mother_home_lang];
            $array[] = $item->mother_employer;
            $array[] = $item->mother_position;
            $array[] = $configa['education'][$item->mother_education];
            $array[] = $item->mother_phone;
            $array[] = $item->mother_email;

            $array[] = $item->learn_diffculties;
            $array[] = $item->medical_conditions;
            $array[] = $item->physical_disabilities;
            $array[] = $item->talent;
            $array[] = ($item->child_avatar) ? "有" : "无";
            $array[] = ($item->academic_report) ? "有" : "无";
            $array[] = ($item->persion_copy) ? "有" : "无";
            $array[] = ($item->recommendation_form) ? "有" : "无";
            $array[] = $modalTitle;
            $data['items'][$i + 1] = $array;

        }

        $this->addMessage('state', 'success');
        $this->addMessage('data', $data);
        $this->showMessage();
        /*mysql_query('set names utf8');
        ob_end_clean();
        header("Content-type:application/vnd.ms-excel");
        header("Content-Disposition:attachment;filename=$filename.xls");
        echo $this->_t("中文姓")."\t";
        echo $this->_t("中文名")."\t";
        echo $this->_t("英文姓")."\t";
        echo $this->_t("英文中间名")."\t";
        echo $this->_t("英文名")."\t";
        echo $this->_t("拼音姓")."\t";
        echo $this->_t("拼音名")."\t";
        echo $this->_t("性别")."\t";
        echo $this->_t("出生日期")."\t";
        echo $this->_t("国籍")."\t";
        echo $this->_t("护照/身份证")."\t";
        /*echo $this->_t("签证有效期")."\t";
        echo $this->_t("居留许可证有效期")."\t";*
        echo $this->_t("母语")."\t";
        echo $this->_t("其他语言")."\t";

        echo $this->_t("入学日期")."\t";
        echo $this->_t("入学年级")."\t";
        echo $this->_t("家庭住址")."\t";
        /*echo $this->_t("是否需要校车")."\t";
        echo $this->_t("是否需要学籍")."\t";*
        echo $this->_t("是否为艾毅/启明星员工子女")."\t";
        echo $this->_t("备注")."\t";

        echo $this->_t("学校名称")."\t";
        echo $this->_t("所在城市")."\t";
        echo $this->_t("就读时间")."\t";
        echo $this->_t("年级")."\t";
        echo $this->_t("课程")."\t";
        echo $this->_t("教学语言")."\t";

        echo $this->_t("姓名")."\t";
        echo $this->_t("年龄")."\t";
        echo $this->_t("性别")."\t";
        echo $this->_t("目前就读学校")."\t";
        echo $this->_t("年级")."\t";

        echo $this->_t("父亲姓名")."\t";
        echo $this->_t("父亲国籍")."\t";

        echo $this->_t("父亲母语")."\t";
        echo $this->_t("父亲家庭使用的语言")."\t";
        echo $this->_t("父亲工作单位")."\t";
        echo $this->_t("父亲职位")."\t";
        echo $this->_t("父亲学历")."\t";
        echo $this->_t("父亲联系电话")."\t";
        echo $this->_t("父亲邮件")."\t";
        echo $this->_t("母亲姓名")."\t";
        echo $this->_t("母亲国籍")."\t";
        echo $this->_t("母亲母语")."\t";

        echo $this->_t("母亲家庭使用的语言")."\t";
        echo $this->_t("母亲工作单位")."\t";
        echo $this->_t("母亲职位")."\t";
        echo $this->_t("母亲学历")."\t";
        echo $this->_t("母亲联系电话")."\t";
        echo $this->_t("母亲邮件")."\t";
        /*echo $this->_t("紧急联系人")."\t";
        echo $this->_t("联系电话")."\t";
        echo $this->_t("邮箱")."\t";*
        echo $this->_t("其他信息 - 有无学习困难")."\t";

        /*echo $this->_t("其他信息 - 是否参加过学习辅导课")."\t";
        echo $this->_t("您的孩子上过特殊教育课程吗？  - 附件")."\t";
        echo $this->_t("您的孩子曾接受过心理咨询吗？  - 附件")."\t";
        echo $this->_t("您的孩子是否被学校劝退或者开除过？")."\t";*
        echo $this->_t("您的孩子有什么健康的问题？")."\t";
        echo $this->_t("您的孩子有哪些身体上的残疾或者障碍吗？")."\t";
        echo $this->_t("您的孩子是否有音乐或体育方面的特长？")."\t";
        echo $this->_t("学生照片")."\t";
        echo $this->_t("一至两年内的学业报告 - 附件")."\t";
        echo $this->_t("学生及家长户口或护照扫描件 - 附件")."\t";
        echo $this->_t("老师推荐信")."\t";
        echo $this->_t("状态")."\n";*/




           /* echo $this->_t($item->cn_name)."\t";
            echo $this->_t($item->cn_name_last )."\t";
            echo $this->_t($item->en_name)."\t";
            echo $this->_t($item->en_name_middle)."\t";
            echo $this->_t($item->en_name_last)."\t";
            echo $this->_t($item->pinyin_first)."\t";
            echo $this->_t($item->pinyin_last)."\t";
            echo $this->_t(($item->gender == 1) ? "男" :"女")."\t";
            echo ($item->birthday ? date('Y-m-d', $item->birthday) : '')."\t";
            echo $this->_t($configa['countries'][$item->nationality])."\t";
            echo $this->_t($item->passportid)."\t";
            /*echo ($item->valid_date ? date('Y-m-d', $item->valid_date) : '')."\t";
            echo ($item->resident_date ? date('Y-m-d', $item->resident_date) : '')."\t";*
            echo $this->_t($configa['language'][$item->native_lang])."\t";
            echo $this->_t($configa['language'][$item->other_lang])."\t";

            echo ($item->start_date ? date('Y-m-d', $item->start_date) : '')."\t";
            echo $this->_t($class_name[$item->start_grade])."\t";
            echo $this->_t($item->home_address)."\t";
            /*echo $this->_t(($item->require_bus == 1) ? "是" :"否")."\t";
            echo $this->_t(($item->has_xueji == 1) ? "是" :"否")."\t";*
            echo $this->_t(($item->is_staff == 1) ? "是" :"否")."\t";
            echo $this->_t(nl2br($item->remark))."\t";

            echo $this->_t($school)."\t";
            echo $this->_t($location)."\t";
            echo $this->_t($studytime)."\t";
            echo $this->_t($grade)."\t";
            echo $this->_t($Curriculum)."\t";
            echo $this->_t($language)."\t";

            echo $this->_t($name)."\t";
            echo $this->_t($age)."\t";
            echo $this->_t($gender)."\t";
            echo $this->_t($schools)."\t";
            echo $this->_t($grades)."\t";

            echo $this->_t($item->father_name)."\t";
            echo $this->_t($configa['countries'][$item->father_nation])."\t";

            echo $this->_t($configa['language'][$item->father_lang])."\t";
            echo $this->_t($configa['language'][$item->father_home_lang])."\t";
            echo $this->_t($item->father_employer)."\t";
            echo $this->_t($item->father_position)."\t";
            echo $this->_t($configa['education'][$item->father_education])."\t";
            echo $this->_t($item->father_phone)."\t";
            echo $this->_t($item->father_email)."\t";
            echo $this->_t($item->mother_name)."\t";
            echo $this->_t($configa['countries'][$item-visitsLog>mother_nation])."\t";
            echo $this->_t($configa['language'][$item->mother_lang])."\t";

            echo $this->_t($configa['language'][$item->mother_home_lang])."\t";
            echo $this->_t($item->mother_employer)."\t";
            echo $this->_t($item->mother_position)."\t";
            echo $this->_t($configa['education'][$item->mother_education])."\t";
            echo $this->_t($item->mother_phone)."\t";
            echo $this->_t($item->mother_email)."\t";
            /*echo $this->_t($item->emergency_contact_name)."\t";
            echo $this->_t($item->emergency_contact_phone)."\t";
            echo $this->_t($item->emergency_contact_email)."\t";*
            $learn_diffculties = '"' . str_replace(PHP_EOL, "\r\n",  $item->learn_diffculties ) . '"';
            echo $this->_t($learn_diffculties)."\t";
            /*$learn_support = '"' . str_replace(PHP_EOL, "\r\n",  $item->learn_support ) . '"';
            echo $this->_t($learn_support)."\t";
            echo $this->_t(($item->special_needs) ? "有" : "无")."\t";
            echo $this->_t(($item->psychological_evaluation) ? "有" : "无")."\t";
            $been_expelled = '"' . str_replace(PHP_EOL, "\r\n",  $item->been_expelled ) . '"';
            echo $this->_t($been_expelled)."\t";*
            $medical_conditions = '"' . str_replace(PHP_EOL, "\r\n",  $item->medical_conditions ) . '"';
            echo $this->_t($medical_conditions)."\t";
            $physical_disabilities = '"' . str_replace(PHP_EOL, "\r\n",  $item->physical_disabilities ) . '"';
            echo $this->_t($physical_disabilities)."\t";
            $talent = '"' . str_replace(PHP_EOL, "\r\n",  $item->talent ) . '"';
            echo $this->_t($talent)."\t";
            echo $this->_t(($item->child_avatar) ? "有" : "无")."\t";
            echo $this->_t(($item->academic_report) ? "有" : "无")."\t";
            echo $this->_t(($item->persion_copy) ? "有" : "无")."\t";
            echo $this->_t(($item->recommendation_form) ? "有" : "无")."\t";
            echo $this->_t($modalTitle)."\n";*/
    }


    public function actionPrintAdmissions($childid = "")
    {
        if($childid){
            $this->layout = '//layouts/print';
            $this->printFW = $this->branchObj->getPrintHeader();
            Yii::import('common.models.calendar.*');
            $yid = "2016";
            $criteria = new CDbCriteria;
            $criteria->compare('is_selected', 1);
            $criteria->compare('branchid', $this->branchId);
            $criteria->compare('startyear', ">{$yid}");
            $yidObj = CalendarSchool::model()->findAll($criteria);
            $data = date("Y", time());
            $nextYear = $data + 1;
            $nextYears = $data + 2;
            $calender = array('others' => '其他', $nextYear => $nextYear . " - "  . $nextYears);
            foreach($yidObj as $item){
                $next =  $item->startyear + 1;
                $calender[$item->startyear] = $item->startyear . " - "  . $next;
            }


            $configa = AdmissionsDs::getConfig();

            $child =  AdmissionsDs::model()->findByPk($childid);

            $this->render('visit/reportprints', array(
                'child' => $child,
                'configa' => $configa,
                'calender' => $calender,
            ));
        }
    }

    public function actionProgression()
    {
        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/echarts.min.js');

        $date = Yii::app()->request->getParam('date');

        $datetime = strtotime($date);
        if (!$datetime) {
            $datetime = time();
        }
        $schoolid = $this->branchId;
        $startyear = date('Y', $datetime);
        if (date('m', $datetime) >= 1 &&  date('m', $datetime) < 9 ) {
           $startyear -= 1;
        }
        Yii::import('common.models.calendar.*');
        $crit = new CDbCriteria();
        $crit->compare('branchid', $schoolid);
        $crit->compare('startyear', $startyear);
        $calendarSchool = CalendarSchool::model()->find($crit);


        $arraySchool = array('BJ_OE'=>'OE', 'BJ_OG'=>'OG', 'BJ_XHL'=>'LJ', 'BJ_IASLT'=>'IASLT');


        $data = array();
        $classids = array();

        Yii::import('common.models.child.StatsChildCount');
        for ($i = 0; $i < 3; $i++) {
            $_startyear = date('Y', $datetime) - $i + 1;
            $_datetime = strtotime($_startyear .'-'. date('m-d', $datetime));
            $_lasttime = strtotime(($_startyear - 1) .'-'. date('m-d', $datetime));

            $data[$i][$schoolid]['class'] = array();
            $data[$i][$schoolid]['childids'] = array();
            if ($i == 0 && $calendarSchool->is_selected) {
                Yii::import('common.models.invoice.ChildReserve');
                $nextYid = CalendarSchool::model()->getCalendarId($schoolid, $startyear + 1);
                $crit = new CDbCriteria();
                $crit->compare('schoolid', $schoolid);
                $crit->compare('stat', 20);
                $crit->compare('calendar', $nextYid);
                $crit->index = 'childid';
                $models = ChildReserve::model()->findAll($crit);

                foreach ($models as $model) {
                    $data[$i][$schoolid]['class'][$model->childid] = $model->classid;
                    $classids[] = $model->classid;
                    $data[$i][$schoolid]['childids'][$model->childid] = $model->childid;
                }
            } else {
                $time = mktime(0,0,0,date('m',$_datetime),date('d',$_datetime),date('Y',$_datetime));
                $criteria = new CDbCriteria;
                $criteria->compare('period_timestamp', $time);
                $criteria->compare('schoolid', $schoolid);

                $items = StatsChildCount::model()->findAll($criteria);
                foreach($items as $item){
                    if ($item->classid){
                        $chids = $item->childids1.','.$item->childids2;
                        foreach(explode(',',$chids) as $cid){
                            if ($cid){
                                $data[$i][$schoolid]['class'][$cid] = $item->classid;
                                $classids[] = $item->classid;
                                $data[$i][$schoolid]['childids'][$cid] = $cid;
                            }
                        }
                    }
                }
            }
            foreach ($arraySchool as $_schoolid => $name) {
                $childids = StatsChildCount::model()->getChild($_schoolid, $_lasttime);
                $intersects = array_intersect($data[$i][$schoolid]['childids'], $childids);
                foreach ($intersects as $intersect) {
                    $data[$i][$_schoolid][$intersect] = $data[$i][$schoolid]['class'][$intersect];
                }
            }
            unset($data[$i][$schoolid]);

        }
        $classesModel = IvyClass::model()->findAllByPk($classids);
        $classInfo = array();
        foreach ($classesModel as $model) {
            if ($model->classtype == 'e1') {
                $classInfo[$model->classid] = $model->classid;
            }
        }
        $res = array();
        $j = 0;
        foreach ($data as $i => $items) {
            $_startyear = $startyear - $j + 1;
            $_startyear = $_startyear .'-'. ($_startyear + 1);
            foreach ($arraySchool as $_schoolid=>$name) {
                $res[$_startyear][$_schoolid]['e'] = 0;
                $res[$_startyear][$_schoolid]['k'] = 0;
                foreach ($items[$_schoolid] as $k2 => $v2) {
                    if (isset($classInfo[$v2])) {
                        $res[$_startyear][$_schoolid]['e'] ++;
                    } else {
                        $res[$_startyear][$_schoolid]['k'] ++;
                    }
                }
            }
            $j++;
        }
        $chartData = array(
            'xAxis' => array_keys($res),
            'series1' => array(),
            'series' => array(),
        );
        foreach ($res as $_startyear => $items) {
            foreach ($arraySchool as $_schoolid=>$name) {
                $chartData['series1'][$_schoolid . 'e']['name'] = 'Elementary';
                $chartData['series1'][$_schoolid . 'e']['type'] = 'bar';
                $chartData['series1'][$_schoolid . 'e']['stack'] = $name;
                $chartData['series1'][$_schoolid . 'e']['barWidth'] = '20px';
                $chartData['series1'][$_schoolid . 'e']['data'][] = $res[$_startyear][$_schoolid]['e'];
                // $chartData['series1'][$_schoolid . 'e']['label']['normal'] = array('position'=>'top','show'=> true,'formatter'=>$name);

                $chartData['series1'][$_schoolid . 'k']['name'] = 'Kindergarten';
                $chartData['series1'][$_schoolid . 'k']['type'] = 'bar';
                $chartData['series1'][$_schoolid . 'k']['stack'] = $name;
                $chartData['series1'][$_schoolid . 'k']['barWidth'] = '20px';
                $chartData['series1'][$_schoolid . 'k']['data'][] = $res[$_startyear][$_schoolid]['k'];
                $chartData['series1'][$_schoolid . 'k']['label']['normal'] = array('position'=>'top','show'=> true,'formatter'=>$name);
            }
        }
        foreach ($chartData['series1'] as $key => $value) {
           $chartData['series'][]= $value;
        }
        $this->render('progression', array('data'=>$chartData));
    }

    // --------------------------------------方法--------------------------------------

    // 加载模型
    public function loadModel($id)
    {
        $model=IvyschoolsVisit::model()->findByPk($id);
        // $model=IvyschoolsVisit::model()->findByAttributes(array('id'=>$id, 'schoolid'=>$this->branchId));
        if($model===null)
            throw new CHttpException(404,'The requested page does not exist.');
        return $model;
    }

    // 菜单项
    public function getMenu()
    {
        Yii::import('common.models.calendar.*');
        $criteria = new CDbCriteria;
        $criteria->compare('is_selected', 1);
        $criteria->compare('branchid', $this->branchId);
        $yidObj = CalendarSchool::model()->find($criteria);
        $startyear = $yidObj->startyear;
        if($this->branchObj->type == 50){
            $startyear = $yidObj->startyear + 1;
        }
        $mainMenu = array(
            array('label'=>Yii::t('user','录取学生学生转入'), 'url'=>array("/mcampus/admissions/trans"), 'active'=>$this->getAction()->getId()=='trans'?true:false),
            array('label'=>Yii::t('admissions','Scheduled visits'), 'url'=>array("/mcampus/admissions/index"), 'active'=>$this->getAction()->getId()=='index'?true:false),
            array('label'=>Yii::t('admissions','Visitor log'), 'url'=>array("/mcampus/admissions/visitsLog/status/10"), 'active'=>$this->getAction()->getId()=='visitsLog'?true:false),
            array('label'=>Yii::t('admissions','Visit date configuration'), 'url'=>array("/mcampus/admissions/visitsConfig"), 'active'=>$this->getAction()->getId()=='visitsConfig'?true:false),
            //array('label'=>Yii::t('user',' 面试老师'), 'url'=>array("/mcampus/admissions/interviewTeacher"), 'active'=>$this->getAction()->getId()=='interviewTeacher'?true:false),
            array('label'=>Yii::t('admissions','Applications'), 'url'=>array("/mcampus/admissions/admissions", 'startYear' => $startyear), 'active'=>$this->getAction()->getId()=='admissions'?true:false),
            array('label'=>Yii::t('management','Arrange interview'), 'url'=>array("/mcampus/admissions/nointerview"), 'active'=>$this->getAction()->getId()=='nointerview'?true:false),
            array('label'=>Yii::t('admissions','Data analysis'), 'url'=>array("/mcampus/admissions/dataAnalysis"), 'active'=>$this->getAction()->getId()=='dataAnalysis'?true:false),
            array('label'=>Yii::t('admissions','升学分析'), 'url'=>array("/mcampus/admissions/progression"), 'active'=>$this->getAction()->getId()=='progression'?true:false),
        );
        return $mainMenu;
    }

    // 获取所在国家
    public function getCountry($data)
    {
        $countryList = Country::model()->getCountryList();
        return $countryList[$data->basic->country];
    }

    public function getAdmission($data)
    {
        $is_set = (isset($this->parents) && $this->parents[$data->basic->tel]) ? '是' : '否';
        return $is_set;
    }

    // 加载配置文件
    public function loadConfig()
    {
        // 根据学校取配置
        $this->cfgs = OA::LoadConfig('CfgVisit');
        if ($this->branchObj->type == 50) {
            $this->cfgs = OA::LoadConfig('CfgDsVisit');
        }
        return $this->cfgs;
    }

    /**
     * [sendEmail 发送邮件给家长]
     * @param  array  $info    [邮件内容]
     * @param  string $subject [邮件主题]
     * @param  string $email   [邮件地址]
     * @return [type]          [发送结果]
     */
    private function sendEmail($info=array(), $email='')
    {
        if (strpos($email, '@')) {
            $branch = Branch::model()->findByPk($this->branchId);
            if ($branch->info) {
                $branchInfo = $branch->info;
                if (!$branchInfo->admissions_tel) {
                    $branchInfo->admissions_tel = $branchInfo->tel;
                }
                if (!$branchInfo->admissions_email) {
                    $branchInfo->admissions_email = $branchInfo->email;
                }
            }
            $view = 'visitConfirm';
            $main = 'main';
            $sub = '[Ivyonline] ';
            $supportEmails = array($branchInfo->admissions_email);

            $subject = 'Welcome to Visit Ivy Schools 欢迎参观艾毅幼儿园';
            if ($info['confirm']) {
                $subject = 'Thank you for Visiting Ivy Schools 感谢参观艾毅幼儿园';
            }
            if ($this->branchId == 'BJ_DS' || $this->branchId == 'BJ_SLT') {
                $view = 'visitConfirm_ds';
                $main = 'todsparent';
                $sub = '[DayStar] ';
                $cfgs = $this->loadConfig();
                $subject = 'Welcome to Visit Daystar Academy 欢迎参观启明星学校';
                if ($info['confirm']) {
                    $subject = 'Thank you for Visiting Daystar Academy 感谢参观启明星学校';
                }
            }

            $mailer = Yii::createComponent('common.extensions.mailer.EMailer');
            $mailer->AddAddress($email);
            foreach ($supportEmails as $supportEmail) {
                $mailer->AddCC($supportEmail);
                $mailer->AddReplyTo($supportEmail);
            }
            $mailer->Subject = $sub . $subject ;
            $mailer->getView($view, array('info' => $info, 'branchInfo' => $branchInfo, 'branch' => $branch), $main);
            $mailer->iniMail( OA::isProduction() ); // 此行代码要放到AddAddress, AddCC，getView法下面
            if (!$mailer->Send()) {
                return false;
            }
        }
        return true;
    }




    public function actionExportInvoice()
    {
        $invoiceTime = Yii::app()->request->getParam('invoiceTime','');
        $calender = Yii::app()->request->getParam('calender','');

        $criteria = new CDbCriteria;
        $criteria->with = 'admissionsds_child';
        $criteria->compare('interview_time', $invoiceTime);
        $criteria->compare('t.school_id', $this->branchId);
        $criteria->compare('admissionsds_child.start_year', $calender);
        $items =  ChildInterview::model()->findAll($criteria) ;

        if($items){
            $filename = 'Admission Interview list-'.$this->branchObj->abb .' '. date('Y-m-d H:i',$invoiceTime);
            $configa = AdmissionsDs::getConfig();
            if($this->branchId == "BJ_DS"){
                $class_name = $configa['grade'];
            }
            if($this->branchId == "BJ_SLT"){
                $class_name = $configa['grade_slt'];
            }
            if($this->branchId == "BJ_IASLT"){
                $class_name = $configa['grade_ayalt'];
            }
            $daystar = $this->yid();

            mysql_query('set names utf8');
            ob_end_clean();
            header("Content-type:application/vnd.ms-excel");
            header("Content-Disposition:attachment;filename=$filename.xls");
            echo $this->_t("面试日期")."\t";
            echo $this->_t("面试时间")."\t";
            echo $this->_t("申请学年")."\t";
            echo $this->_t("入学年级")."\t";
            echo $this->_t("英文名字")."\t";
            echo $this->_t("中文名字")."\t";
            echo $this->_t("性别")."\t";
            echo $this->_t("出生日期")."\t";
            echo $this->_t("父亲联系电话")."\t";
            echo $this->_t("母亲联系电话")."\n";
            foreach($items as $item){
                $enName = $item->admissionsds_child->en_name . ' ' . $item->admissionsds_child->en_name_middle . ' ' . $item->admissionsds_child->en_name_last;
                $cnName = $item->admissionsds_child->cn_name . ' ' . $item->admissionsds_child->cn_name_last;
                echo $this->_t(date("Y-m-d",$item->interview_time))."\t";
                echo $this->_t(date("H:i",$item->interview_time))."\t";
                echo $this->_t($daystar[$item->admissionsds_child->start_year])."\t";
                echo $this->_t($class_name[$item->admissionsds_child->start_grade])."\t";
                echo $this->_t($enName)."\t";
                echo $this->_t($cnName)."\t";
                echo $this->_t(($item->admissionsds_child->gender == 1) ? "男" : "女")."\t";
                echo $this->_t(date("Y-m-d",$item->admissionsds_child->birthday))."\t";
                echo $this->_t($item->admissionsds_child->father_phone)."\t";
                echo $this->_t($item->admissionsds_child->mother_phone)."\n";
            }
        }
    }

    public function _t($str='')
    {
        return iconv('utf-8', 'gbk', $str);
    }

    /*
     * $child 孩子的全部信息
     * $teacher 安排的老师
     *
     */
    public  function emails($ivterview)
    {
        $mailer = Yii::createComponent('common.extensions.mailer.EMailer');
        $mailer->Subject = sprintf('%s' . $ivterview['title'], $ivterview['child_name']);
        if(count($ivterview['email']) == 1){
            $mailer->AddAddress($ivterview['email']);
        }else{
            foreach($ivterview['email'] as $v){
                $mailer->AddAddress($v);
            }
        }
        if(count($ivterview['addcc']) == 1){
            $mailer->AddCC($ivterview['addcc']);
        }else{
            foreach($ivterview['addcc'] as $v){
                $mailer->AddCC($v);
            }
        }
        $mailer->getView($ivterview['template'], array('ivterview' => $ivterview), 'todsparent');

        //艾毅在线面试邮件模板
        //$mailer->getView('Interview', array('child' => $child,'branchId' => $this->branchId), 'main');
        $mailer->iniMail( OA::isProduction()); // 此行代码要放到AddAddress, AddCC方法下面
        $mailer->Send();
    }

    /*
     * $interview_id 面试表的ID
     */
    public  function teacherlist($interview_id)
    {
        $criteria = new CDbCriteria;
        $criteria->compare('child_interview_list', $interview_id);
        $ChildInterviewTeacher = ChildInterviewTeacher::model()->findAll($criteria);
        $arr = array();
        if($ChildInterviewTeacher){
            foreach($ChildInterviewTeacher as $v){
                $arr[] = $v->teacher_id;
            }
        }
        return User::model()->findAllByPk($arr);
    }

    // 生成POS订单
    public function generalYeepayOrder($invoiceObj)
    {
        $this->layout='//layouts/dialog';
        Yii::import('common.models.yeepay.*');
        Yii::import('common.components.yeepay.*');

        $yeePayCfg = CommonUtils::LoadConfig('CfgYeePayGlobal');
        $schoolid = $invoiceObj->schoolid;
        $orderPrefix = strval($yeePayCfg[$schoolid]['number_code']) . sprintf('%06s', $invoiceObj->invoice_id);

        $yeeOrder = new YeepayOrder;
        $yeeOrder->orderId = $yeeOrder->genOrderID($orderPrefix);
        $yeeOrder->payable_amount = $invoiceObj->amount;
        $yeeOrder->rb_BankId = 'POS-NET';
        $yeeOrder->schoolId = $schoolid;
        $yeeOrder->childId = 0;
        $yeeOrder->operatorId = Yii::app()->user->id;
        $yeeOrder->payment_method = InvoiceTransaction::TYPE_POS;
        $yeeOrder->status = 0;
        $yeeOrder->orderTime = time();
        $yeeOrder->updateTime = time();
        if ($yeeOrder->save()) {
            $model = new YeepayOrderItem;
            $model->it_orderId = $yeeOrder->orderId;
            $model->status = 0;
            $model->orderTime = time();
            $model->updateTime = time();
            $model->invocieId = $invoiceObj->invoice_id;
            $model->payable_amount = $invoiceObj->amount;
            if ($model->save()) {
                $yeePay = new YeepayCommon;
                $yeePay->init($schoolid);
                $url = $yeePay->getReqUrlOnline();
                $ret = array(
                    "p0_Cmd"=>$yeePay->getP0Cmd(),
                    "p1_MerId"=>$yeePay->getP1MerId(),
                    "p2_Order"=>$yeeOrder->orderId,
                    "p3_Amt"=>$yeeOrder->payable_amount,
                    "p4_Cur"=>$yeePay->getP4Cur(),
                    "p5_Pid"=>'',
                    "p6_Pcat"=>'',
                    "p7_Pdesc"=>'',
                    "p8_Url"=>$yeePay->getP8Url(),
                    "p9_SAF"=>$yeePay->getP9Saf(),
                    "pd_FrpId"=>'POS-NET',
                    "pr_NeedResponse"=>$yeePay->getPrNeedResponse(),
                    "hmac"=>$yeePay->getReqHmacString($yeeOrder->orderId, $yeeOrder->payable_amount, '', '', '', '', 'POS-NET'),
                );

                $this->render('admissions/_postyeepay', array('url'=>$url, 'ret'=>$ret));
            }
        }
        return false;
    }

    // 检查支付合法性
    public function checkInvoice($invoiceObj, $childid)
    {
        if ($invoiceObj->status != Invoice::STATS_UNPAID) {
            return false;
        }

        if ($invoiceObj->admission_id != $childid) {
            return false;
        }

        return true;
    }

    /**
     * [mailTo 发送邮件]
     * @param  [array]  $to   [收件人]
     * @param  [string] $sub  [主题]
     * @param  [string] $tem  [模板]
     * @param  [array]  $cc   [抄送]
     * @param  [array]  $data [data]
     * @return [bool]         [发送结果]
     */
    public function mailTo($to, $sub, $tem, $cc = array(), $data = array())
    {
        $mailer = Yii::createComponent('common.extensions.mailer.EMailer');
        $mailer->Subject = $sub;

        $flag = false;
        foreach ($to as $v) {
            if (strpos($v, '@')) {
                $flag = true;
                $mailer->AddAddress($v);
            }
        }
        if (!$flag) {
            return false;
        }

        if (count($cc) > 0) {
            foreach ($cc as $v) {
                if (strpos($v, '@')) {
                    $mailer->AddCC($v);
                }
            }
        }

        $template = 'toparent';
        if($this->branchObj->type == 50){
            $template = 'todsparent';
        }

        $mailer->getView($tem, array('data' => $data), $template);

        $mailer->iniMail( OA::isProduction()); // 此行代码要放到AddAddress, AddCC方法下面
        $mailer->Send();
    }

    public function getButtons($data)
    {
        //echo '<a class="btn btn-info btn-xs" href="javascript:void(0);" data-backdrop="static" onclick="vacationEdit('.$data->id.')"><span class="glyphicon glyphicon-pencil"></span></a> ';
        echo '<a class="J_ajax_del btn btn-danger btn-xs" href="'.$this->createUrl("interviewTeacherDetele", array("id"=>$data->id, 'type'=>'detele')).'"><span class="glyphicon glyphicon-remove"></span></a>';
    }

    public function getButton($data)
    {
        echo CHtml::link(Yii::t('global', 'Print'), array('printAdmissions', 'childid' => $data->id, "branchId"=>Yii::app()->controller->branchId), array('class' => 'btn btn-success btn-xs mb5', 'target' => '_blank')) . ' ';
        echo CHtml::link(Yii::t('management', 'Management'), array('admissionsManagement', 'id' => $data->id, "branchId"=>Yii::app()->controller->branchId), array('class' => 'btn btn-primary btn-xs mb5', 'target' => '_blank')) . ' ';
        echo CHtml::link(Yii::t('global', 'Edit'), array('admissionsUpdate', 'id' => $data->id, "branchId"=>Yii::app()->controller->branchId), array('class' => 'J_modal btn btn-info btn-xs mb5')) . ' ';
        echo CHtml::link(Yii::t('payment', 'Cancel'), array('admissionsDelete', 'id' => $data->id, "status" => "99", "branchId"=>Yii::app()->controller->branchId), array('class' => 'J_ajax_del btn btn-danger btn-xs mb5', 'data-msg' => "确定要作废吗？")) . ' ';
        if(in_array($data->school_id, array('DS','SLT'))){
            echo CHtml::link(Yii::t('payment', '驳回'), array('admissionsDelete', 'id' => $data->id,  "status" => "89","branchId"=>Yii::app()->controller->branchId), array('class' => 'J_ajax_del btn btn-warning btn-xs mb5', 'data-msg' => "确定要驳回吗？"));
        }
    }

    public function getBirthday($data) {
        $birthday = AdmissionsDs::model()->computeAge($data->birthday);
        return $birthday;
    }


    public function getTeacher($data)
    {
        $teacher =  User::model()->findByPk($data->teacher_id);
        echo $teacher->getName();
    }

    public function getUid($data)
    {
        $teacher =  User::model()->findByPk($data->uid);
        echo $teacher->getName();
    }

    public function getSchool($data)
    {
        $teacher =  Branch::model()->findByPk($data->school_id);
        echo $teacher->title;
    }

    public function getStartDate($data)
    {
        return ($data->start_year) ? $this->calenders[$data->start_year] : date("Y-m-d", $data->start_date);
    }

    public function yid()
    {
        Yii::import('common.models.calendar.*');
        $criteria = new CDbCriteria;
        $criteria->compare('is_selected', 1);
        $criteria->compare('branchid', $this->branchId);
        $yidObj = CalendarSchool::model()->find($criteria);
        $data = date("Y", time());
        if ((int)date('m', time()) >= 8) {
            $nextYear = $data + 1;
            $nextYears = $data + 2;
        } else {
            $nextYear = $data;
            $nextYears = $data + 1;
        }
        $calender = array('others' => '其他');
        $next =  $yidObj->startyear + 1;
        $calender[$yidObj->startyear] = $yidObj->startyear . " - "  . $next;
        array_push($calender, $nextYear . " - "  . $nextYears);

        return $calender;
    }


    public function actionSchoolData()
    {
        $startdate = Yii::app()->request->getParam('startdate',date('Y-m-d'));
        $enddate = Yii::app()->request->getParam('enddate',date('Y-m-d'));
        $startdate_arr = explode('-',$startdate);
        $enddate_arr = explode('-',$enddate);

        if(count($startdate_arr)==1){
            #学年查询
            Yii::import('common.models.calendar.*');
            $criteria = new CDbCriteria;
            $criteria->compare('startyear', $startdate);
            $criteria->compare('branchid', $this->branchId);
            $calendarObj = CalendarSchool::model()->find($criteria);
            $yid = $calendarObj->yid;
            $criteria = new CDbCriteria;
            $criteria->compare('yid', $yid);
            $Calendar = Calendar::model()->find($criteria);
            $timepoints = $Calendar->timepoints;
            $timepoints_arr = explode(',',$timepoints);
            $startdate = date('Y-m-d',$timepoints_arr[0]);
            $enddate = date('Y-m-d',$timepoints_arr[3]);
        }
        if(count($startdate_arr)==2){
            $startdate .= '-01';
            $enddate .= '-' . date('t', strtotime($startdate));
        }
        $startdate = date('Y-m-d',strtotime($startdate));
        $enddate = date('Y-m-d',strtotime($enddate));
        $res = CommonUtils::requestDsOnline('statistical/schoolData', array(
             'school_id' => $this->branchId,
             'startdate' => $startdate,
             'enddate' => $enddate,
        ));
        if ($res['code'] == 0) {
            $data = $res['data'];
            $this->addMessage('state', 'success');
            $this->addMessage('data', $data);
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
        }
        $this->showMessage();
    }

    public function actionGetSchoolYear()
    {
        $res = CommonUtils::requestDsOnline('statistical/getSchoolYear', array(
            'school_id' => $this->branchId,
        ));
        if ($res['code'] == 0) {
            $data = $res['data'];
            $this->addMessage('state', 'success');
            $this->addMessage('data', $data);
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
        }
        $this->showMessage();
    }

    public function actionReturnSchoolData()
    {
        $yid = Yii::app()->request->getParam('yid','');
        $res = CommonUtils::requestDsOnline('statistical/returnSchoolData', array(
            'school_id' => $this->branchId,
            'yid' => $yid,
        ));
        if ($res['code'] == 0) {
            $data = $res['data'];
            $this->addMessage('state', 'success');
            $this->addMessage('data', $data);
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
        }
        $this->showMessage();
    }


}
