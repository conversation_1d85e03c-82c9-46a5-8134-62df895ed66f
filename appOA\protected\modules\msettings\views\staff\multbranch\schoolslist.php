<?php $form=$this->beginWidget('CActiveForm', array(
    'id'=>'schools-form',
    'enableAjaxValidation'=>false,
    'htmlOptions'=>array('class'=>'J_ajaxForm'),
)); ?>
<div class="pop_cont">
    <div>
        <label>
            <input type="checkbox" data-checklist="J_check_c1" class="J_check_all" data-direction="y" />
            <span>全部</span>
        </label>
    </div>
    <div class="row J_check_wrap">
        <?php foreach ($branchList as $bid=>$branch):?>
        <div class="col-xs-6">
            <label>
                <input type="checkbox" data-yid="J_check_c1" class="J_check" name="school[<?php echo $bid;?>]" value="<?php echo $branch['title']?>" <?php if ($branch['selected']):?>checked='checked'<?php endif;?>/>
                <span><?php echo $branch['title']?></span>
            </label>
        </div>
        <?php endforeach;?>
    </div>
</div>
<div class="pop_bottom">
    <button id="J_dialog_close" type="button" class="btn btn-default pull-right"><?php echo Yii::t('global','Cancel');?></button>
    <button type="submit" class="btn btn-primary J_ajax_submit_btn pull-right mr10"><?php echo Yii::t('global','Submit');?></button>
</div>
<?php $this->endWidget(); ?>
<script type="text/javascript">
function callback(data)
{
    data = eval('('+data+')');
    $(parent.document.getElementById('adm_'+data.uid)).html(data._data);
    window.parent.head.dialog.closeAll();
}
</script>