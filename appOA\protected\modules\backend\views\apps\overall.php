<?php
$branches = $this->getAllBranch();
// 查找当前用户是否有审批人
// 使用Yii::app()->db获取数据库连接
$db = Yii::app()->subdb;
$staffId = $this->staff->uid;
// 创建SQL查询
//$sql = "SELECT * FROM ivy_leave_staff WHERE staff_id = $staffId";
$sql = "SELECT * FROM ivy_reporting_line WHERE `type` = 'leave' and (`staff_id` = $staffId OR `top_uid` = $staffId) limit 1";
// 执行查询
$command = $db->createCommand($sql);
$showLeave = $command->queryAll();

$userBranch = $this->staff->profile->branch;
// 总部报销付款权限
if (in_array($userBranch, CommonUtils::LoadConfig('CfgOffice'))) {
    $workflowAccess = true;
}

?>

    <div class="container-fluid">
        <div class="col-md-2">
            <div class="panel panel-default">
                <div class="panel-body">
                    <h4>
                        <span class="glyphicon glyphicon-user"></span>
                        <strong><?php echo $this->staff->getName();?></strong>
                    </h4>
                    <p class="text-muted">
                        <?php echo $this->staff->profile->occupation->getName();?>
                    </p>
                    <?php
                    if($this->staff->user_avatar && $this->staff->user_avatar != 'blank.gif'):
                        echo CHtml::image(OA::CreateOAUploadUrl('users', $this->staff->user_avatar),'',array("class"=>"img-rounded img-responsive mb10"));
                    else:
                        $publicPhoto = ($this->staff->staffInfo && $this->staff->staffInfo->staff_photo) ? $this->staff->staffInfo->staff_photo : 'blank.jpg';
                        echo CHtml::image(OA::CreateOAUploadUrl('infopub/staff', $publicPhoto),'',array("class"=>"img-rounded img-responsive mb10"));
                    endif;
                    ?>
                    <div class="caption">
                        <p><a href="mailto:<?php echo $this->staff->email;?>"><?php echo $this->staff->email;?></a></p>
                        <p><a href="<?php echo $this->createUrl('//mpub/default/ivyers', array('branchId'=>$this->staff->profile->branch));?>">
                                <?php echo $branches[$this->staff->profile->branch]['title'];?>
                            </a></p>
                        <p><a class="btn btn-primary" href="<?php echo $this->createUrl('//user/profile/index', array('category'=>'basic'));?>">
                            <span class="glyphicon glyphicon-pencil"></span> <?php echo Yii::t('user','Edit Profile');?>
                        </a></p>
                        <?php if($showLeave): ?>
                            <p><a class="btn btn-primary" href="<?php echo $this->createUrl('//mcampus/leave/index', array('branchId'=>$this->staff->profile->branch, 'category'=>'leaveOvertime'));?>">
                                <?php if($leaveCount > 0): ?>
                                    <span class="badge"><?php echo $leaveCount;?></span> <?php echo Yii::t('leave','Leave Application');?>
                                <?php else: ?>
                                    <span class="glyphicon glyphicon-plus"></span> <?php echo Yii::t('leave','Leave Application');?>
                                <?php endif; ?>
                            </a></p>
                        <?php endif; ?>
                    </div>
                    <?php if($waits): ?>
                        <a href="<?php echo $this->createUrl('//workflow/index/index');?>" class="btn btn-primary mt15">
                            <span class="badge"><?php echo $waits?></span> <?php echo Yii::t('workflow', 'Workflow');?>
                        </a>
                    <?php elseif($workflowAccess): ?>
                        <a href="<?php echo $this->createUrl('//workflow/index/index');?>" class="btn btn-primary mt15">
                            <?php echo Yii::t('workflow', 'Workflow');?>
                        </a>
                    <?php endif; ?>

                    <?php if($absent['count'] > 0): ?>
                        <?php if(Yii::app()->params['siteFlag'] == 'daystar'){?>

                            <a href="<?php echo $this->createUrl('//mcampus/childsign/index2', array('branchId'=>$this->branchId));?>" class="mt15 btn <?php echo $absent['style'];?>">
                                <span class="badge"><?php echo $absent['count']?></span> <?php echo Yii::t('attends', 'Student Attendance');?>
                            </a>
                        <?php }else{?>
                            <a href="<?php echo $this->createUrl('//mcampus/childsign/index', array('branchId'=>$this->branchId));?>" class="mt15 btn <?php echo $absent['style'];?>">
                                <span class="badge"><?php echo $absent['count']?></span> <?php echo Yii::t('attends', 'Student Attendance');?>
                            </a>
                        <?php }?>
                    <?php endif; ?>

                    <?php if($isAsa && $this->branchObj->type == Branch::PROGRAM_DAYSTAR): ?>
                    <a href="<?php echo $this->createUrl('//user/profile/asainfo'); ?>" class="btn btn-primary mt15"><?php echo Yii::t('asa', 'ASA Staff Attendance'); ?></a>
                    <?php endif; ?>

                    <?php if($unRepiedData['total'] > 0): ?>
                        <div class="btn-group mt15">
                            <button type="button" class="btn btn-danger dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <span class="badge"><?php echo $unRepiedData['total']; ?></span> <?php echo Yii::t('newDS', 'No-Reply DM'); ?> <span class="caret"></span>
                            </button>
                            <ul class="dropdown-menu">
                                <?php foreach($unRepiedData['items'] as $item): ?>
                                <li><a href="<?php echo $item['url'];?>"><?php echo $item['title'];?> <span class="badge"><?php echo $item['num']; ?></span></a></li>
                                <?php endforeach; ?>
                            </ul>
                            </div>
                    <?php endif; ?>
                    <?php if($untreatedBehavior['total'] > 0):?>
                        <?php if(count($untreatedBehavior['school_total'])>1):?>
                        <div class="btn-group mt15">
                            <button type="button" class="btn btn-danger dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <span class="badge"><?php echo $untreatedBehavior['total']; ?></span> <?php echo Yii::t('site', 'Student Behavior'); ?> <span class="caret"></span>
                            </button>
                            <ul class="dropdown-menu">
                                <?php foreach($untreatedBehavior['school_total'] as $item): ?>
                                    <li><a href="<?php echo $item['url'];?>"><?php echo $item['title'];?> <span class="badge"><?php echo $item['total']; ?></span></a></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                        <?php else:?>
                            <?php foreach($untreatedBehavior['school_total'] as $item): ?>
                                <a href="<?php echo $item['url']; ?>" class="mt15 btn <?php echo $absent['style'];?>">
                                    <span class="badge"><?php echo $item['total']?></span> <?php echo Yii::t('site', 'Student Behavior');?>
                                </a>
                            <?php endforeach; ?>
                        <?php endif;?>
                    <?php endif;?>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="panel-group mb5" id="accordion_Parent" style="display: none">

            </div>

            <div class="panel panel-default">
                <div class="panel-heading" data-branchfilter="dailyBulletin">
                    <span class="glyphicon glyphicon-bullhorn"></span>
                    <a data-toggle="collapse" href="#collapse-1" aria-expanded="true" aria-controls="collapse-1">
                        <span id="allSchoolNotice" style="color: #333;"></span>
                    </a>
                </div>
                <div id="collapse-1" class="panel-collapse collapse <?php if($this->branchId != 'BJ_DS'){echo 'in';}?>">
                    <div class="panel-body">
                        <?php $this->renderPartial('_dailyBulletin');?>
                    </div>
                </div>
            </div>

            <div class="role-shortcuts">
                <div class="panel panel-default">
                    <div class="panel-heading"><span class="glyphicon glyphicon-link"></span> <?php echo Yii::t('user','Shortcuts')?></div>
                    <div class="panel-body">
                        <div class="row">
                            <?php $this->renderPartial('_shortcuts'); ?>
                        </div>
                    </div>
                </div>
            </div>
            <?php if(Yii::app()->language=='en_us'):?>
            <div class="role-shortcuts">
                <div class="panel panel-info">
                    <div class="panel-heading"><span class="glyphicon glyphicon-tasks"></span> 2023-26 Group Strategy</div>
                    <div class="panel-body">
                        <h5>1. <strong>Leadership Everywhere</strong> Every function in Ivy Group becomes the leader in its own field.</h5>
                        <h5>2. <strong>SI Everywhere</strong> SI embedded into everything that we do.</h5>
                        <h5>3. <strong>Sustainable Capacity Development</strong> Develop strong people, systems and processes.</h5>
                        <h5><a target="_blank"  href="<?php echo $okrUrl['en']; ?>"> Progress Tracker</a></h5>
                    </div>
                </div>
            </div>
            <?php else:?>
            <div class="role-shortcuts">
                <div class="panel panel-info">
                    <div class="panel-heading"><span class="glyphicon glyphicon-tasks"></span> 2023-26 集团策略</div>
                    <div class="panel-body">
                        <h5>1. <strong>在全方位领先</strong> 各部门在行业内都成为领导者。</h5>
                        <h5>2. <strong>社会创新（SI）无所不在</strong> SI融入到我们的环境和做的每一件事里。</h5>
                        <h5>3. <strong>可持续提升内部能力</strong> 拥有卓越的人，系统，和流程。</h5>
                        <h5><a target="_blank" href="<?php echo $okrUrl['cn']; ?> ">进展</a></h5>
                    </div>
                </div>
            </div>
            <?php endif;?>
        </div>
        <div class="col-md-4">
            <div class="panel panel-default">
                <div class="panel-heading" data-branchfilter="calendar"><span class="glyphicon glyphicon-calendar"></span> <?php echo Yii::t('site','School Calendar')?></div>
                <div class="panel-body">
                    <div class="row">
                        <div class="col-md-12 col-lg-8">
                            <div id="school-calendar">

                            </div>
                            <?php if (Yii::app()->params['siteFlag']=='daystar'): ?>
                            <div class="mt10">
                                <?php echo CHtml::link(Yii::t('referral', 'More'), '/backend/apps2');?>
                            </div>
                            <?php endif;?>
                        </div>
                        <div class="col-md-12 col-lg-4">
                            <div class="pt10" id="school-calendar-ext"></div>
                        </div>
                    </div>
                </div>
            </div>

            <?php if($staffs):?>
                <div class="panel panel-default">
                    <div class="panel-heading" data-branchfilter="newStaff"><span class="glyphicon glyphicon-user"></span> <?php echo Yii::t('user','New Staff')?></div>
                    <div class="panel-body" id="accordionNewStaff">
                        <?php foreach($staffs as $uid=>$staff):?>
                            <div class="panel panel-default">
                                <div class="panel-heading new-staff-head" data-uid="<?php echo $uid;?>">
                                    <h4 class="pull-right">
                                        <a data-toggle="collapse" data-parent="#accordionNewStaff" href="#collapseNewStaff-<?php echo $uid;?>" onclick="switchAccordion(this)">
                                            <span class="glyphicon glyphicon-chevron-down"></span>
                                        </a>
                                    </h4>
                                    <h4 class="panel-title">
                                        <img class="img-circle" src="<?php echo OA::CreateOAUploadUrl('users', $staff['photo'])?>" height="40">
                                        <?php echo $staff['name'];?>
                                    </h4>
                                </div>
                                <div id="collapseNewStaff-<?php echo $uid;?>" class="panel-collapse collapse out new-staff-body" style="" data-uid="<?php echo $uid;?>">
                                    <div class="panel-body">
                                        <?php
                                        echo CHtml::image(OA::CreateOAUploadUrl('users', $staff['photo']),'',array("class"=>"pull-left img-circle mr10 mb10", 'width'=>160));
                                        echo $staff['bio'];
                                        ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach;?>
                    </div>
                </div>
            <?php endif;?>
        </div>
    </div>

    <div id="branchFilter-template" style="display: none;">
    <span class="pull-right dropdown">
        <a href="#" class="dropdown-toggle" data-toggle="dropdown"><span class="glyphicon glyphicon-list"></span></a>
        <div class="dropdown-menu p10" style="width: 300px;">
            <ul class="nav nav-pills">
                <?php foreach($this->adminBranch as $_cid):?>
                    <?php if (isset($branches[$_cid])) : ?>
                    <li><a href="javascript:" data-yid="<?php echo $branches[$_cid]['yid'];?>" data-branchid="<?php echo $_cid;?>"><?php echo $branches[$_cid]['title'];?></a></li>
                    <?php endif; ?>
                <?php endforeach;?>
            </ul>
        </div>
    </span>
    </div>

    <script>
        var switchAccordion = null;
        var branchFilter = $('#branchFilter-template').html();
        var dailyBulletins = <?php echo CJSON::encode($dailyBulletins)?>;
        var calendarDayData;

        $(function(){
            switchAccordion = function(obj){
                var _class1 = 'glyphicon-chevron-down';
                var _class2 = 'glyphicon-chevron-right';
                $('.new-staff-head h4 span.glyphicon').removeClass(_class2).addClass(_class1);
                if($(obj).parents('.new-staff-head').siblings('.new-staff-body').hasClass('in')){
                    $(obj).find('span.'+_class2).removeClass(_class2).addClass(_class1);
                }else{
                    $(obj).find('span.'+_class1).removeClass(_class1).addClass(_class2);

                }
            };
            $('.new-staff-head').first().find('h4.pull-right a').click();


            $.datepicker.setDefaults($.datepicker.regional['<?php echo (Yii::app()->language == 'en_us')? '': 'zh-CN'?>']);

            <?php if(count($this->adminBranch) > 1):?>
            _.each( $('div.panel-heading[data-branchfilter]'), function(obj){
                $(branchFilter).appendTo($(obj));
                $(obj).find('ul.nav>li>a').click(function(){
                    var type = $(obj).data('branchfilter');
                    var branchid = $(this).data('branchid');
                    var yid = $(this).data('yid');
                    switch (type){
                        case 'dailyBulletin':
                            reDailyBulletins(branchid);
                            break;
                        case 'calendar':
                            reCalendar(branchid, yid);
                            break;
                    }
                });
            });
            <?php endif; ?>

            reCalendar = function(branchid, yid){
                $( "#school-calendar").datepicker('destroy');
                if(branchid != 'BJ_TYG' && yid){
                    $.getJSON('<?php echo $this->createUrl("/moperation/calendar/getCalendarDays")?>', {calendarId: yid, branchId: branchid}, function(data){
                        calendarDayData = data.data.cdata;
                        var sDate = new Date(parseInt(data.data.startdate)*1000);
                        var eDate = new Date(parseInt(data.data.enddate)*1000);
                        $( "#school-calendar").datepicker({
                            minDate: sDate,
                            maxDate: eDate,
                            dateFormat: 'yy-mm-dd',
                            selectOtherMonths: false,
                            showOtherMonths: false,
                            changeMonth: true,
                            changeYear: true,
                            beforeShowDay: beforeShowDay,
                            firstDay: 0
                        });
                        $('#school-calendar-ext').html(_.template($('#calendar-ext-template').html(), {startdate: dateformat(sDate),enddate:dateformat(eDate),schoolday:data.data.schoolday,branchid:branchid}));
                    }, 'json');
                }
                else{
                    $( "#school-calendar").datepicker({
                        dateFormat: 'yy-mm-dd',
                        selectOtherMonths: false,
                        showOtherMonths: false,
                        changeMonth: true,
                        changeYear: true,
                        firstDay: 0
                    });
                    $('#school-calendar-ext').html('<div class="well well-sm">'+'<?php echo Yii::t('message','No school calendar assigned to your campus or branch, select a campus by clicking the icon at right top.');?></div>');
                }
            };

            reDailyBulletins = function(branchid){
                var html = '';
                var schoolHtml = '';
                var schoolsTemplate = '';
                if(!_.isUndefined(dailyBulletins[branchid])){
                    if(branchid == "BJ_DS"){
                        var schoolInfoCount = 0;
                        var schoolInfoCountall = 0;
                        $(".bulletin-items").show();
                        $("#accordion_Parent").empty();
                        _.each(dailyBulletins[branchid], function(item,u){
                            if(u > 0){//幼儿园/小学/中学的信息
                                schoolInfoCount = 0;
                                $("#accordion_Parent").show();
                                schoolHtml = "";
                                var schoolClass = item.name;
                                _.each(item.items, function(item){
                                    schoolInfoCount += 1;
                                    var css = item.priority == 2 ? 'bg-secondary' : (item.priority == 1) ? 'bg-warning' : 'bg-info';
                                    item.css = css;
                                    schoolHtml += _.template($('#bulletin-item-template').html(), item);
                                    var schoolTemplate = '<div class="timeline-centered"> '+ schoolHtml +'<article class="timeline-entry begin"><div class="timeline-entry-inner"><div class="timeline-icon" style="-webkit-transform: rotate(-90deg); -moz-transform: rotate(-90deg);"><i></i> +</div></div></article></div>';
                                    schoolsTemplate = '<div class="panel-heading"><h4 class="panel-title" style="font-size: 12px"><span class="glyphicon glyphicon-bullhorn"></span> <a data-toggle="collapse" data-parent="#accordion_Parent" href="#collapse'+ u +'School">'+ schoolClass +' </a><span class="badge">'+ schoolInfoCount +'</span></h4></div><div id="collapse'+ u + 'School" class="panel-collapse collapse"><div class="panel-body">' + schoolTemplate +'</div></div>';
                                });
                                    $("#accordion_Parent").append('<div class="panel panel-default">' + schoolsTemplate + '</div>');
                            }
                            else if (u == 0) {
                                schoolInfoCountall = Object.keys(item.items).length
                            }
                        });

                        if(dailyBulletins[branchid][0] !== undefined){//启明星有幼儿园/小学/中学信息无全校信息的情况
                            $("#allSchoolNotice").html(dailyBulletins[branchid][0].name + ' <span class="badge">'+schoolInfoCountall+'</span>');
                            _.each(dailyBulletins[branchid][0]["items"], function(item){
                                var css = item.priority == 2 ? 'bg-secondary' : (item.priority == 1) ? 'bg-warning' : 'bg-info';
                                item.css = css;
                                html += _.template($('#bulletin-item-template').html(), item);
                                $('.bulletin-items').html(html);
                            });
                            $('.bulletin-items').html(html);
                            $('.no-bulletin').hide();
                        }else {

                            $("#allSchoolNotice").html("<?php echo Yii::t('user','Daily Bulletin')?>");
                            $('.no-bulletin').show();
                        }

                    }else{
                        $(".bulletin-items").show();
                        $("#accordion_Parent").hide();
                        $("#allSchoolNotice").html(dailyBulletins[branchid][0].name);
                        _.each(dailyBulletins[branchid][0]["items"], function(item){
                            var css = item.priority == 2 ? 'bg-secondary' : (item.priority == 1) ? 'bg-warning' : 'bg-info';
                            item.css = css;
                            html += _.template($('#bulletin-item-template').html(), item);
                        });
                        $('.no-bulletin').hide();
                        $('.bulletin-items').html(html);
                    }
                }else{
                    $('.no-bulletin').show();
                    $(".bulletin-items").hide();
                    $("#accordion_Parent").hide();
                    $("#allSchoolNotice").html("<?php echo Yii::t('user','Daily Bulletin')?>");
                }


            };
            reDailyBulletins('<?php echo $this->branchId;?>');
            reCalendar('<?php echo $this->branchId;?>', <?php echo $this->branchObj->schcalendar;?>);
        });
        dateformat = function(date){
            return $.datepicker.formatDate('yy-mm-dd', date);
        };

        beforeShowDay = function(date){
            var theDate = $.datepicker.formatDate('yymmdd',date);
            var _css = [];
            _css.push(theDate);
            if(!_.isUndefined(calendarDayData[theDate])){
                var _tip = [calendarDayData[theDate].title_cn, calendarDayData[theDate].title_en, calendarDayData[theDate].memo_cn, calendarDayData[theDate].memo_en];
                _css.push('dayflag');
                _css.push('dayflag-' + calendarDayData[theDate].day_type + calendarDayData[theDate].event);
                return [true, _css.join(' '), _tip.join("\n")];
            }else{
                return [true, _css.join(' '), ''];
            }
        };

        //workflow callback
        showCheckList = function(data){
            $('#workflow-modal-template').html(data.template);
            if (!_.isUndefined(data.callback)){
                eval(data.callback+'()');
            }
        }
    </script>

    <style>
        #school-calendar .ui-datepicker{width: 100%}
    </style>

    <script type="text/template" id="bulletin-item-template">
        <article class="timeline-entry">
            <div class="timeline-entry-inner">
                <div class="timeline-icon <%= css%>">
                    <i></i>
                </div>
                <div class="timeline-label">
                    <% if(startdate != 0){ %>
                    <h2><%= dateformat( new Date( parseInt(startdate) * 1000 ) )%> ~ <%= dateformat( new Date( parseInt(duedate) * 1000 ) )%> <span class='pull-right font12'><?php echo Yii::t('user','Updated: ')?><%= updated_timestamp %></span></h2>
                    <% }else{ %>
                    <h2><%= dateformat( new Date( parseInt(duedate) * 1000 ) )%> <span class='pull-right font12'><?php echo Yii::t('user','Updated: ')?><%= updated_timestamp %></span> </h2>
                    <% } %>
                    <p style="color: #000"><strong><%= title%></strong></p>
                    <div class="bulletin-text"><%= en_content%></div>
                    <%= files%>
                </div>
            </div>
        </article>
    </script>

    <script type="text/template" id="calendar-ext-template">
        <h4><?php echo Yii::t('site','Calendar Detail');?></h4>
        <ul class="list-unstyled">
            <li><?php echo CommonUtils::addColon( Yii::t('site','Calendar Start') );?><%= startdate%></li>
            <li><?php echo CommonUtils::addColon( Yii::t('site','Calendar End') );?><%= enddate%></li>
            <li><?php echo CommonUtils::addColon( Yii::t('site','Total Schooldays') );?><%= schoolday%> <?php echo Yii::t('site', 'Days');?></li>
        </ul>
    </script>

    <!-- Modal -->
    <div id="workflow-modal-template"></div>

<?php
$cookiename = "new-ivyonline-guide".Yii::app()->user->id;

if ( Yii::app()->params['siteFlag']=='ivygroup' && !Yii::app()->request->cookies[$cookiename] ) {
    Yii::import('ext.guide.class.*');
    $this->widget('ext.guide.Guide', array(
        'options' => array(
            'guides' => array(
                0 => GuideItem::welcomeGuide(),
            ),
            'demo' => true
        )
    ));

    $cookie = new CHttpCookie($cookiename, 1);
    $cookie->expire=time()+3600*24*360;
    Yii::app()->request->cookies[$cookiename]=$cookie;
}

if (Yii::app()->params['siteFlag']=='daystar') {
    $this->widget('ext.si.SI');
}

