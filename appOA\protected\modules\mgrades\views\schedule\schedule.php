<?php
foreach($classes as $class){
    $_cdata = array(
        'classid' => $class->classid,
        'title' => $class->title,
        'room_code' => $class->introduction,
        'grade_flag' => $class->classtype,
    );
    $pageContent['classData'][$class->classid] = $_cdata;
    foreach($class->teacherInfo as $teacher){
        $pageContent['classTeacher'][$class->classid][] = $teacher->teacherid;
    }
}

$pageContent['dayLabels'] = array(
    '1' => 'Monday/周一',
    '2' => 'Tuesday/周二',
    '3' => 'Wednesday/周三',
    '4' => 'Thursday/周四',
    '5' => 'Friday/周五',
);

$pageContent['subjects'] = $programCfg['programs'];
$pageContent['teachers'] = $teachers;
$pageContent['teachersubject'] = $teachersubject;
$pageContent['scheduleid'] = $scheduleid;

?>

<script>
    var pageContent = <?php echo CJSON::encode($pageContent);?>;
</script>

<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo Yii::t('site','Campus Workspace');?></li>
        <li class="active"><?php echo Yii::t('campus','Course Schedule');?></li>
    </ol>

    <!-- 时间段列表 -->
    <div class="row">
        <div class="col-md-12">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <?php echo sprintf("%s - %s", $calendar->startyear, $calendar->startyear + 1); ?>
                    <a title="<?php echo Yii::t('global','Back'); ?>" href="<?php echo $this->createUrl('index');?>"><span class="glyphicon glyphicon-cog"></span> </a>
                </div>
                <div class="panel-body">
                    <?php
                    $listItems = array();
                    $assignClass= array();
                    foreach($schedules as $_schedule){
                        if ($_schedule->id == $scheduleid){
                            //课程表关系的班级
                            if (!empty($_schedule->gradesScheduleLink)){
                                foreach ($_schedule->gradesScheduleLink as $val){
                                    $assignClass[$val->class_id] = $val->class_id;
                                }
                            }
                        }else{
                            unset($programCfg['schedules'][$_schedule->schedule_code]);
                        }
                        $listItems[$_schedule->id] = array(
                            'label' => sprintf('%s ( %s ~ %s )',$_schedule->label, OA::formatDateTime($_schedule->valid_from), OA::formatDateTime($_schedule->valid_to)),
                            'url' => array('//mgrades/schedule/index', 'scheduleid'=>$_schedule->id),
                        );
                    }
                    $this->widget('zii.widgets.CMenu', array(
                        'items'=>$listItems,
                        'htmlOptions'=>array('class'=>'nav nav-pills'),
                        'activeCssClass'=>'active',
                    ));
                    ?>
                </div>
            </div>
        </div>
    </div><!-- 时间段列表 -->

    <!--编辑课表表格-->
    <div class="row">
        <div class="col-md-2">
            <div id="class-timeslot-wrapper">
                <?php
                foreach($programCfg['schedules'] as $_key => $_timetpl):
                ?>
                    <div class="panel panel-default">
                        <div class="panel-heading" data-timekey="<?php echo $_key;?>">
                            <?php echo Yii::t('campus','Class List');?>

                            <a target="_blank" class="btn btn-success btn-sm"
                               href="<?php echo $this->createUrl('scheduleOverview', array('scheduleid'=>$scheduleid));?>"
                               id="viewSwitch">
                                <span class="glyphicon glyphicon-th"></span> <?php echo Yii::t('campus','View All');?></a>
                        </div>
                        <div class="list-group" id="class-list" data-attr="<?php echo $_key;?>">
                            <?php foreach ($classes as $_class) {
                                if (in_array($_class->classid, $assignClass)):
                                    echo CHtml::link(
                                        $_class->title,
                                        '#',
                                        array(
                                            'onclick'=>sprintf('loadScheduleByClass(%d, false)',$_class->classid),
                                            'class'=>'list-group-item',
                                            'data-classid'=>$_class->classid,
                                        )
                                    );
                                endif;
                            }
                            ?>
                        </div>
                    </div>

                    <div style="display: none;" class="class-weekly-schedule-tpl" schedule-code="<?php echo $_key?>">
                        <div class="panel panel-default">
                            <div class="panel-heading class-title">
                                <h4>
                                    <label></label>
                                    <a href="javascript:;" onclick="loadScheduleByClass(0, true)" class="pull-right"><span class="glyphicon glyphicon-refresh"></span></a>
                                    <a href="javascript:;" onclick="clearScheduleByClass()" class="pull-right mr10" title="清楚本班级所有课表数据"><span class="glyphicon glyphicon-trash"></span></a>
                                </h4>
                            </div>
                            <div class="panel-body">
                                <table class="table table-striped">
                                    <thead>
                                    <tr>
                                        <th></th>
                                        <?php
                                        for($i=1; $i<6; $i++):
                                            ?>
                                            <th><?php echo $pageContent['dayLabels'][$i];?></th>
                                        <?php
                                        endfor;
                                        ?>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <?php
                                    foreach($_timetpl as $_timeslot):
                                        ?>
                                        <tr>
                                            <th>
                                                <?php echo $_timeslot; ?>
                                            </th>
                                            <?php
                                            for($i=1; $i<6; $i++):
                                                ?>
                                                <td class="schedule-timeslot-item" data-timeslot="<?php echo $_timeslot; ?>" data-weekday="<?php echo $i;?>"></td>
                                            <?php
                                            endfor;
                                            ?>
                                        </tr>
                                    <?php
                                    endforeach;
                                    ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                <?php
                endforeach;
                ?>
            </div>
        </div>
        <div class="col-md-10 schedule-main-wrapper">

        </div>
    </div><!--编辑课表表格-->
</div>

<?php

$this->renderPartial('_editTimeSlotModal', array(
    'programCfg'=>$programCfg,
    'scheduleid'=>$scheduleid,
));
$defaultTimeSlotModel = array(
    'id' => 0,
    'branchid' => $this->branchId,
    'weekday' => 0,
    'timeslot' => '',
    'subject_flag' => '',
    'teacher_uid' => 0,
    'grade_flag' => '',
    'classid' => 0,
    'room_code' => '',
    'calendarid' => $this->branchObj->schcalendar
);
?>

<script type="text/template" id="class-item-content-template">
    <div class="well"  style="background-color:#fff;padding:10px;margin-bottom:10px;" data-subject_flag="<%= subject_flag %>" data-teacher_uid="<%= teacher_uid %>" data-id="<%= id %>">
        <button type="button" class="close" aria-label="Close" onclick="delSchedule(this);"><span aria-hidden="true">&times;</span></button>
        <h5><% print(pageContent.subjects[subject_flag]);%></h5>

        <% for( var jsoni in JSON.parse(teacher_uid)) { %>
            <h5 class="names"><% print(pageContent.teachers[JSON.parse(teacher_uid)[jsoni]]);%></h5>
        <% } %>
    </div>
</script>

<script>
var loadScheduleByClass = null; //加载某一个班级的课程安排
var cbSchedule = null; //保存完时间短安排后的回调
var classItemContentTemplate = _.template($('#class-item-content-template').html());
var timeslotItemTemplate = _.template($('#timeslot-form-template').html());
var drawScheduleTable = null; //把课程表数据render到页面
var editTimeSlot = null; //编辑某一时间段的安排
var editTimeSlotButton = '<a href="javascript:;" onclick="editTimeSlot(this)"><span class="glyphicon glyphicon-plus"></span></a>';
var currentSelectedClassId = null;
var DefaultTimeSlotModel = Backbone.Model.extend({});
var defaultTimeSlot = new DefaultTimeSlotModel;
var scheduleData = {};
defaultTimeSlot.set(<?php echo CJSON::encode($defaultTimeSlotModel);?>);
var toggle = true;


$(function(){
    loadScheduleByClass = function(classid, forceRefresh){
        if(classid > 0){
            currentSelectedClassId = classid;
        }

        //初次加载或者强制刷新
        if(toggle){
            toggle = false;

            $('#class-list a.list-group-item').removeClass('active');
            $('#class-list a.list-group-item[data-classid|="'+currentSelectedClassId+'"]').addClass('active');

            if(_.isUndefined(scheduleData[currentSelectedClassId]) || forceRefresh){
                $.ajax({
                    url: '<?php echo $this->createUrl('LoadScheduleDataByClass')?>',
                    type: 'post',
                    dataType: 'json',
                    data:{classid: currentSelectedClassId, scheduleid: pageContent.scheduleid},
                    async: false
                }).done(function(data){
                    if(data.state == 'success'){
                        scheduleData[currentSelectedClassId] = {};
                        scheduleData[currentSelectedClassId] = data.data.data;

                    }
                });
            }

            $('div.class-weekly-schedule-tpl').hide();
            var _table = $('div.class-weekly-schedule-tpl');
            _table.find('div.class-title h4 label').html(pageContent.classData[currentSelectedClassId].title);
            _table.appendTo('div.schedule-main-wrapper').show();
            var _tdItems = _table.find('td.schedule-timeslot-item');
            _tdItems.empty();
            _table.hide();

            drawScheduleTable(currentSelectedClassId, _tdItems);
            _table.show('blind',{},500, function(){
                toggle = true;
            });
            _tdItems.append(editTimeSlotButton);
        }
    }

    editTimeSlot = function(obj){
        var dataDuck = $(obj).parents('td.schedule-timeslot-item');
        //下面要判断，如果之前没有值，走下面的逻辑，如果有值，还没写
        var selectedTimeSlot = defaultTimeSlot.clone();
        selectedTimeSlot.set({
            timeslot: dataDuck.attr('data-timeslot'),
            weekday: dataDuck.attr('data-weekday'),
            grade_flag: pageContent.classData[currentSelectedClassId].grade_flag,
            room_code: pageContent.classData[currentSelectedClassId].room_code,
            classid: currentSelectedClassId,
            subject_flag: dataDuck.attr('data-subject_flag'),
            teacher_uid: dataDuck.attr('data-teacher_uid'),
            id: dataDuck.attr('data-id')
        });

        var _innerForm = timeslotItemTemplate(selectedTimeSlot.attributes);


        $('#form-timeslot-inner-wrapper').empty().html(_innerForm);
        $('#editTimeSlotModal #form-hint-title').html(
            pageContent.dayLabels[selectedTimeSlot.get('weekday')] + ' <small>'+selectedTimeSlot.get('timeslot')+'</small>');

        if(selectedTimeSlot.get('teacher_uid')==0){
            var teachers = '<div class="radio"><label><input type="checkbox" name="postData[teacher_uid][]" value="0" checked> <?php echo Yii::t('global','N/A');?></label></div>';
        }
        else{
            var teachers = '<div class="radio"><label><input type="checkbox" name="postData[teacher_uid][]" value="0"> <?php echo Yii::t('global','N/A');?></label></div>';
        }
        _.each(pageContent['classTeacher'][currentSelectedClassId], function(val){
            //console.log(selectedTimeSlot.get(''));
            teachers += '<div class="radio J_radio"><label>';

            if(val == selectedTimeSlot.get('teacher_uid')){
                    teachers += '<input type="checkbox" name="postData[teacher_uid][]" value="'+val+'" checked>';
            }
            else{
                teachers += '<input type="checkbox" name="postData[teacher_uid][]" value="'+val+'">';
            }
            teachers += pageContent['teachers'][val];
            teachers += '</label></div>';
        });
        $('#editTimeSlotModal #class-teachers').html(teachers);
        $('#postData_subject_flag').val( selectedTimeSlot.get('subject_flag') );
        $('#postData_id').val( selectedTimeSlot.get('id') );
        $('#J_fail_info').hide();
        $('#editTimeSlotModal').modal();
    }

    drawScheduleTable = function(classid, tdObjs){
        $('.class-weekly-schedule-tpl td.schedule-timeslot-item').removeAttr('data-subject_flag').removeAttr('data-teacher_uid').removeAttr('data-id');
        if(!_.isEmpty(scheduleData[classid])){
            _.each(scheduleData[classid], function(item,i){
                var _tdTarget = tdObjs.parents('table').find('td[data-timeslot|="'+item.timeslot+'"][data-weekday|="'+item.weekday+'"]');
                //_tdTarget.attr('data-subject_flag', item.subject_flag);
                //_tdTarget.attr('data-teacher_uid', item.teacher_uid);
                //_tdTarget.attr('data-id', item.id);
                var _content = classItemContentTemplate(item);
                _tdTarget.append(_content);
            })
        }
    }

    cbSchedule = function(data){
        if(_.isUndefined(scheduleData[data.classid])){
            scheduleData[data.classid] = {};
        }
        scheduleData[data.classid][data.id] = data;
        loadScheduleByClass(data.classid, false);
        setTimeout(function(){
            $('#editTimeSlotModal').modal('hide');
        }, 100);
    }

    cSubject = function(_this){
        var val = $(_this).val();
        $('#class-teachers input[type="checkbox"]').each(function(){
            if(_.indexOf(pageContent['teachersubject'][$(this).val()], val) == -1){
                $(this).parents('div.J_radio').hide();
                $(this).removeAttr('checked');
            }
            else{
                //console.log($(this).parent().text());
                $(this).parents('div.J_radio').show();
            }
        });
    }

    clearScheduleByClass = function(){
        if(currentSelectedClassId){
            if(confirm('清除本班级所有课表数据？')){
                $.ajax({
                    type: 'post',
                    url: '<?php echo $this->createUrl('clearScheduleByClass')?>',
                    data:{classid: currentSelectedClassId, scheduleid: pageContent.scheduleid},
                    dataType: 'json'
                }).done(function(data){
                    loadScheduleByClass(currentSelectedClassId, true);
                    resultTip({error: data.state=='success'?0:1, msg: data.message});
                });
            }
        }
    }
    
    delSchedule = function(_this){
        var obj = $(_this).parent('div');
        var id = obj.data('id');
        var subject_flag = obj.data('subject_flag');
        if (id && subject_flag){
            if(confirm('确定删除此课程吗？')){
                $.ajax({
                    type: 'post',
                    url: '<?php echo $this->createUrl('DelScheduleItem')?>',
                    data:{id: id, subject_flag: subject_flag},
                    dataType: 'json'
                }).done(function(data){
                    loadScheduleByClass(currentSelectedClassId, true);
                    resultTip({error: data.state=='success'?0:1, msg: data.message});
                });
            }
        }
    }
})
</script>

<style>
    #class-teachers div.radio.J_radio{display:inline-block;width: 48%}
</style>