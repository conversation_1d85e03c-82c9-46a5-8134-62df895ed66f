<style>
    textarea {
        resize: none;
    }
    dl {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
    }
    dl dt,
    dl dd {
        margin: 0;
    }
    .td_center{
        text-align: center;
        vertical-align: middle !important;
    }
    #fill_in{
        padding-left: 0;
    }
    #choose_class_title{
        margin-bottom:8px;
    }
    .cycle{
        width: 25px;
    }
    .content1{
        flex: 1;
        margin-right:25px;
        font-size: 14px;
    }
    .content1_1{
        flex: 1;
        margin-right:25px;
        font-size: 14px;
        color: #999;
    }
    .division_2{
        border-bottom: 1px solid #D9D9D9;
        padding-bottom:20px;
        margin-top: 16px;
        margin-bottom:20px;
    }
    .division_4{
        margin-bottom:19px;
    }
    .pl-0{
        padding-left: 0;
    }.iconfont  {
         font-size:14px
     }
</style>
<div class="col-md-2">
    <div class="list-group">
        <?php foreach ($data['classData'] as $classid => $title) {
            echo '<span class="list-group-item" onclick="switchCourse(this, ' . $classid. ')">';
            echo $title;
            echo '</span>';
        } ?>
    </div>
</div>
<div class="col-md-10"  style="display: none" id="student_style">
    <div id="choose_class_title"></div>
    <div class="col-md-8" id="fill_in" style="font-size: 14px;color: #D9534F;margin-bottom: 24px;">
        <?php if(!empty($report[$this->selectedSemester]->fill_in_end_time)){?>
            <span class="glyphicon glyphicon-info-sign" style="margin-right: 5px"></span>
            <span style="font-weight: bold;"><?php echo Yii::t("principal","Reporting Deadline")?>：<?php echo date('Y/m/d H:i',$report[$this->selectedSemester]->fill_in_end_time )?></span>
        <?php }?>
    </div>
    <div class="pull-right ml15" id="templateBtn" style="display: none">
        <button type="button" id="downloadTemplate" class="btn btn-default mr5" onclick="downloadTemplate()">
            <?php echo Yii::t('ptc','Download class template') ?>
        </button>
        <label>
            <input type="button" id="i-check" value="<?php echo Yii::t('teaching','Import');?>" class="btn btn-primary mr5" onclick="$('#i-file').click();">
            <input type="file" name="file" id='i-file'  accept=".xls, .xlsx" onchange="uploadPlan()" style="display: none">
        </label>
        <button type="button" class="btn btn-primary" onclick="daochu()">
            <?php echo Yii::t('user', 'Export');?>
        </button>
    </div>
    <table class="table table-bordered hidden" id="student-table">
        <thead>
            <tr>
                <th width="150" style="text-align: center"><?php echo Yii::t('attends', 'Student List'); ?></th>
                <th style="text-align: center"><?php
                    switch (Yii::app()->request->getParam('classid', 1)) {
                        case 1:
                            $comment_text = Yii::t('teaching', 'Comment from advisor');
                            break;
                        case 3:
                            $comment_text = Yii::t('teaching', 'Comment from Social Innovation');
                            break;
                        case 4:
                            $comment_text = Yii::t('teaching', 'Comment from College Counseling');
                            break;
                        case 5:
                            $comment_text = Yii::t('teaching', 'CAS').' '.Yii::t('teaching', 'Comment from teacher');;
                            break;
                        case 6:
                            $comment_text = Yii::t('teaching', 'EE').' '.Yii::t('teaching', 'Comment from teacher');
                            break;
                    }
                    echo $comment_text;
                    ?></th>
                <th class="td_center text-center "  width="7%"  data-switchable="false">
                    <!-- <button class="btn btn-primary btn-sm" onclick="saveAll(0)"><?php echo Yii::t('teaching','Save All');?></button> -->
                </th>
            </tr>
        </thead>
        <tbody></tbody>
    </table>
</div>


<script type="text/template" id="student-template">
    <tr>
        <td>
            <dl style="text-align: center;">
                <dt style="width:80px" class="visible-lg"><img class="img-rounded" style="width:100%" src=<%= childPhoto %> ></dt>
                <dt style="width:40px" class="hidden-lg"><img class="img-rounded" style="width:100%" src=<%= childPhoto %> ></dt>
                <dd style="padding-top:10px;text-align: center;">
                    <div><%= childname %></div>
                    <div class="font12 mt5" style='text-align:center'><span  style="height: 20px;width:50px ;line-height: 20px;background-color:#EBEDF0;border-radius: 2px;display:inline-block">#<%= childid %></span> </div>
                    <div class='mt10' style='display: flex;flex-wrap: wrap;align-items: center;align-content: center;justify-content: center;'>
                        <% _.each(childLabel, function(cchildLabel, rid, list) { %>
                        <!--                    <span  class="label flag_span hover-cursor" title="<%= cchildLabel.desc %>" style="padding:4px;background-color: rgb(<%= cchildLabel.color %>)"><%= cchildLabel.name %> <span class="iconfont"><%= cchildLabel.flag_text %></span></span>-->
                        <span  class="flag_span" title="<%= cchildLabel.desc %>" style="color: rgb(<%= cchildLabel.color %>)">
                            <span><%= cchildLabel.name %></span>
                            <span style="line-height:20px"><%= cchildLabel.flag_text %></span>
                        </span>
                        <% }); %>
                    </div>
                </dd>
            </dl>
        </td>
        <td>
            <% _.each(report, function(rep, key, list) { %>
                <% if(rep.cycle == 2 || rep.cycle ==4){%>
                <div class="flex <%= rep.cycle==2 ? 'division_2' : 'division_4' %>">
                <% if(rep.type == 1){%>
                <% if(rep.id == semester){ %>
                <div class="cycle">
                    <span class="label label-default"><%= rep.cycle %></span>
                </div>
                <% if(!evaluationTemplate){ %>
                <!--没有模板配置的课程使用输入框-->
                <div class="content1">
                    <textarea class="form-control" onfocus="limitTextarea(this,'.state');"
                              data-child-id="<%= childid %>" id="<%= childid + '_en'%>" rows="9"><%= en =  (reports[rep.id]) ? reports[rep.id].en : '' %></textarea>
                    <p class="text-right" style="display: none"><span class="state">0</span>/1000</p>
                </div>
                <% }else{%>
                <!--使用模板-->
                <div class="table-responsive content1" >
                    <div class="col-md-6 pl-0">
                        <% _.each(evaluationTemplate['options'],function(item, key, list){ %>
                        <div class="mb5 <%if(key>0){%> mt10 <% }%> pl-2 font14"><%= item['title'] %></div>
                        <% if(item['options']){%>
                        <select class='form-control height40 select_center' id="<%= childid %>_<%= item['flag'] %>" data-child-id="<%= childid %>" onchange="plan_comment('<%= childid %>')">
                            <% _.each(item['options'],function(item2, key2, list2){%>
                            <option value="<%= item2['value'] %>" <%= reports[rep.id] && reports[rep.id]['temp_data'] && reports[rep.id]['temp_data'][item['flag']] == item2['value'] ? 'selected' : '' %> >
                            <%= item2['title'] %>
                            </option>
                            <% })%>
                        </select>
                        <% }else{%>
                        <textarea class="form-control" style="min-width: unset"  placeholder="teacher enters - past tense, no spaces" rows="3" data-child-id="<%= childid %>"  id="<%= childid %>_<%= item['flag'] %>" oninput="plan_comment('<%= childid %>')"><%= reports[rep.id] && reports[rep.id]['temp_data'] && reports[rep.id]['temp_data'][item['flag']]  ? reports[rep.id]['temp_data'][item['flag']] : '' %></textarea>
                        <% }%>
                        <% })%>
                    </div>
                    <div class="col-md-6">
                        <textarea class="form-control col-md-5 mt10" rows="25" style="min-width: unset" data-child-id="<%= childid %>" onfocus="limitTextarea(this,'.state');" id="<%= childid + '_en'%>" ><%= en =  (reports[rep.id]) ? reports[rep.id].en : '' %></textarea>
                        <p class="text-right" style="display: none"><span class="state">0</span>/1000</p>
                    </div>
                </div>
                <% } %>
                <% }else{ %>
                <div class="cycle">
                    <span class="label label-default"><%= rep.cycle %></span>
                </div>
                <div class="<%= en =  (reports[rep.id])   ? (reports[rep.id].en ? 'content1': 'content1_1') : 'content1_1' %>">
                    <%= en =  (reports[rep.id])   ? (reports[rep.id].en ? reports[rep.id].en : '<?php echo Yii::t('teaching','No Comment');?>') : '<?php echo Yii::t('teaching','No Comment');?>' %>
                </div>
                <% } %>
                <% } %>
                </div>
                <% }%>
            <% }); %>
        </td>
        <td class="text-center td_center">
            <p>
                <button class="btn btn-primary btn-sm" onclick="saveData(<%= childid %>, this)"><?php echo Yii::t('global','Save');?></button>
            </p>
            <p>
                <button class="btn btn-info btn-sm" onclick="reportPreview(<%= childid %>,<%= semester %>,<%= classid %>,<%= yid %>)"><?php echo Yii::t('teaching','Preview');?></button>
            </p>
        </td>
    </tr>
</script>

<script>
    var template = _.template($('#student-template').html());
    var students = <?php echo CJSON::encode($data['childArr']); ?>;
    var report = <?php echo CJSON::encode($report); ?>;
    var classData = <?php echo CJSON::encode($data['classData']); ?>;
    var cstudents;
    var childids;
    var table = $('#student-table tbody');
    var classid_global;
    var type = <?php echo Yii::app()->request->getParam('classid', 1); ?>;
    var evaluationTemplateConfig = <?php echo CJSON::encode($data['evaluationTemplate']); ?>;
    var evaluationTemplate = '';//匹配到的模板信息
    var import_map;//导入数据字段对应关系
    var tempOptions;//模板所有的可选择项
    var tempId = 0;//模板id
    var map_type = {
        1:'counselor',
        5:'CAS',
        6:'EE',
    }
    var map_type2 = {
        1:'counselor_temp',
        5:'cas_temp',
        6:'ee_temp',
    }
    function sortTea(list){
        list.sort((x,y)=>{
            if (x['childname'] && y['childname']) {
                return x['childname'].localeCompare(y['childname'])
            }
        })
        return list
    }
    function switchCourse(_this, classid) {
        childids = [];
        $('#student-table').removeClass('hidden');
        table.empty();
        $("#student_style").css('display','block');
        $('.list-group-item').removeClass('active');
        $(_this).addClass('active');
        var data = students[classid];
        cstudents = students[classid];
        classid_global = classid;
        $('#choose_class_title').html('<strong style="font-size: 14px">' + classData[classid_global] + '</strong>');
        if (!_.isNull(data)) {
            var firstKey = Object.keys(data)[0];
            var classtype = data[firstKey]['classtype'];
            //暂时只给 11和12年纪开放 辅导员评语不限制年级
            var open_class_type = ['e11','e12'];
            if(open_class_type.indexOf(classtype) != -1 || type ==1 ){
                for (var temp_id in evaluationTemplateConfig['courseTemp']) {
                    if(evaluationTemplateConfig['courseTemp'][temp_id].includes(map_type[type]) ){
                        tempId = temp_id;
                        continue;
                    }
                }
                if(tempId){
                    evaluationTemplate = evaluationTemplateConfig['tempData'][tempId];//模板信息
                    import_map = evaluationTemplate['import_map']//导入数据字段对应关系
                    tempOptions = evaluationTemplate['options']//模板所有的可选择项
                    $("#templateBtn").show()//展示模板下载 导入导出按钮
                }else{
                    evaluationTemplate = '';
                    $("#templateBtn").hide()//隐藏模板下载 导入导出按钮
                }
            }else{
                evaluationTemplate = '';
                $("#templateBtn").hide()//隐藏模板下载 导入导出按钮
            }
            list = [];
            for(var key in data){
                list.push({
                    id:key,
                    childname:data[key]['childname'],
                    childLabel:data[key]['childLabel'],
                })
            }
            child_sort = sortTea(list);

            $.each(child_sort,function (x,y){
                if(y.id && y.childname){
                    childids.push(y.id);
                    var view = template(data[y.id]);
                    table.append(view);
                }
            })
            // $.each(data, function (_i, _v) {
            //     if (_v.childid) {
            //         childids.push(_i);
            //         var view = template(_v);
            //         table.append(view);
            //     }
            // })
        }

    }
    function limitTextarea(self, nowleng) {
        $(self).on('input propertychange', function(event) {
            textareaAssist(self)
        });
        $(self).blur(function() {
            $(self).off('input propertychange');
        });
    }

    function textareaAssist(self)
    {
        $(self).next().show();
        var _val = $(self).val();
        // _val = _val < 500 ? _val : _val.substr(0, 500);
        if (_val.length < 1000) {
            $(self).val(_val);
            $(self).next().find('.state').html(_val.length);
            $(self).next().css('color', '#b2b2b2');
        } else {
            var num = $(self).val().substr(0, 1000);
            $(self).val(num);
            $(self).next().find('.state').html(num.length);
            $(self).next().css('color', 'red');
        }
    }

    function saveData(childid, btn) {
        $(btn).attr("disabled", true);
        // var cntext = $('#' + childid + '_cn').val();
        var entext = $('#' + childid + '_en').val();
        var temp_data = {};
        _.each(evaluationTemplate['options'],function (item, b, c){
            temp_data[item['flag']] = $("#" + childid + "_" + item['flag']).val()
        })
        _.each(evaluationTemplate['textarea'],function (item, b, c){
            temp_data[item['flag']] = $("#" + childid + "_" + item['flag']).val()
        })
        $.ajax({
            type: 'post',
            url: '<?php echo $this->createUrl('saveCounselorMessage');?>',
            dataType: 'json',
            data: {
                semester,
                yid,
                childid,
                entext,
                type,
                temp_data
            }
        }).done(function(data){
            if(data.state == 'success'){
                cstudents[childid].reports[semester].en = entext;
                resultTip({msg: data.message});
            } else {
                resultTip({msg: data.message, error: 1});
            }
            $(btn).attr("disabled", false);
        });
    }
    function saveAll(i) {
        $('#process-modal').modal('show');
        $('.modal-footer button').attr('disabled', true);
        $('.modal-title').text('<?php echo Yii::t('teaching','Save All');?>');
        var childid = childids[i];
        var entext = $('#' + childid + '_en').val();
        cstudents[childid].reports[semester].en = entext;
        $.ajax({
            type: 'post',
            url: '<?php echo $this->createUrl('saveCounselorMessage');?>',
            dataType: 'json',
            data: {semester, yid, childid, entext}
        }).done(function(data){
            var percent = ((i+1) / (childids.length))*100;
            $('.progress-bar').attr('aria-valuenow', percent).css('width', percent + '%');
            $('.complete').text(i+1 +'/'+ (childids.length));
            if ((i+1) == childids.length) {
                resultTip({msg: data.message});
                $('.modal-footer button').attr('disabled', false);
                return true;
            }
            if (childids[i + 1])
                return saveAll(i + 1);
        });
    }

    function setReportStatus(childid, status) {
        $.ajax({
            type: 'post',
            url: '<?php echo $this->createUrl('saveReportMessage');?>',
            dataType: 'json',
            data: {semester, yid, childid, status:status}
        }).done(function(data){
            if(data.state == 'success') {
                if(data.data == 1){
                    var html = '<button class="btn btn-danger btn-sm" onclick="setReportStatus(' + childid + ', "offline")"><?php echo Yii::t('teaching','Make Offline');?></button>';
                }else{
                    var html = '<button class="btn btn-success btn-sm" onclick="setReportStatus(' + childid + ', "online")"><?php echo Yii::t('teaching','Make Online');?></button>';
                }
                $("#status_"+childid).html(html)
            }else{
                resultTip({msg: data.message, error: 1});
            }
        });
    }

    function reportPreview(childid, report_id, classid, yid) {
        var url = "<?php echo $this->createUrl('newPreviewReportDs', array(
            'childid'=>'-childid-',
            'report_id'=>'-report_id-',
            'classid'=>'-classid-',
            'yid'=>'-yid-',
        ));?>";
        url = url.replace('-childid-', childid);
        url = url.replace('-report_id-', report_id);
        url = url.replace('-classid-', classid);
        url = url.replace('-yid-', yid);
        window.open(url);
    }

    function allOnline() {
        $('.modal-body p').text('');
        $('#process-modal').modal('show');
        $('.modal-footer button').attr('disabled', true);
        $('.modal-title').text('<?php echo Yii::t('teaching','Make All Online');?>');
        Online(0, 'online');
    }

    function allOffline() {
        $('.modal-body p').text('');
        $('#process-modal').modal('show');
        $('.modal-footer button').attr('disabled', true);
        $('.modal-title').text('<?php echo Yii::t('teaching','Make All Offline');?>');
        Online(0, 'offline');
    }

    function Online(i, status) {
        var childid = childids[i];
        $.ajax({
            type: 'post',
            url: '<?php echo $this->createUrl('saveReportMessage');?>',
            dataType: 'json',
            data: {semester, yid, childid, status}
        }).done(function(data){
            if(data.state == 'success') {
                if(data.data == 1){
                    var html = '<button class="btn btn-danger btn-sm" onclick="setReportStatus(' + childid + ', "offline")"><?php echo Yii::t('teaching','Make Offline');?></button>';
                }else{
                    var html = '<button class="btn btn-success btn-sm" onclick="setReportStatus(' + childid + ', "online")"><?php echo Yii::t('teaching','Make Online');?></button>';
                }
                $("#status_"+childid).html(html);

                var percent = ((i+1) / (childids.length))*100;
                $('.progress-bar').attr('aria-valuenow', percent).css('width', percent + '%');
                $('.complete').text(i+1 +'/'+ (childids.length));
            }
            if ((i+1) == childids.length) {
                $('.modal-body p').text(data.message);
                $('.modal-footer button').attr('disabled', false);
                return true;
            }
            if (childids[i + 1])
                return Online(i + 1, status);
        });
    }


    function downloadTemplate(){
        let url='<?php echo $this->createUrl('report/downloadTemplate2', array('branchId' => $this->branchId,'is_template'=>0)); ?>'+'&class_id='+classid_global+'&CAS_EE='+type+'&report_id='+semester
        downloadExcel(url)
    }
    function daochu(){
        let url='<?php echo $this->createUrl('report/downloadTemplate2', array('branchId' => $this->branchId,'is_template'=>1)); ?>'+'&class_id='+classid_global+'&CAS_EE='+type+'&report_id='+semester
        downloadExcel(url)
    }
    function uploadPlan(){
        var file = document.querySelector("#i-file").files[0];
        //可重复传同一文件
        $("#i-file").val('')
        if(!file){
            return false;
        }
        let file_name = file.name
        let xlx_child_ids = [] //需要导入的所有孩子的id
        var type = file.name.split('.');
        if (type[type.length - 1] !== 'xlsx' && type[type.length - 1] !== 'xls') {
            resultTip({error: 'warning', msg: '只能选择xlsx或者xls文件导入'});
            that.loading_read = false
            return false;
        }
        const reader = new FileReader();
        reader.readAsBinaryString(file);
        reader.onload = (e) => {
            const data = e.target.result;
            const my_excel = window.XLS.read(data, {
                type: 'binary'
            });
            const sheet2JSONOpts = {
                defval: ''//给defval赋值为空的字符串
            }
            let xlx_json = window.XLS.utils.sheet_to_json(my_excel.Sheets[my_excel.SheetNames[0]],sheet2JSONOpts);
            for (const xlxJsonKey in xlx_json) {
                let column = 0;
                let child_id = 0
                for (const key in xlx_json[xlxJsonKey]) {
                    //表格数据检查
                    if(column === 0){
                        if(xlx_json[xlxJsonKey][key]){
                            child_id = $.trim(xlx_json[xlxJsonKey][key])
                            xlx_child_ids.push(child_id)
                            // resultTip({error: 'warning', msg: 'student id null'});
                            // return ;
                        }
                    }
                    //转换成 A B C 列
                    let excel_col = String.fromCharCode(65 + parseInt(column));
                    let flag = import_map[excel_col]
                    let filteredData = tempOptions.find(item => item.flag === flag);
                    if(xlx_json[xlxJsonKey][key] !== ''){
                        let input_data = xlx_json[xlxJsonKey][key]
                        if(typeof filteredData !== 'undefined'){
                            if(filteredData['options']){
                                var filteredData2 = filteredData['options'].find(item => item.title === input_data)
                                if(typeof filteredData2 !== 'undefined'){
                                    $("#"+child_id+"_"+flag).val(filteredData2['value'])
                                }
                            }
                            if(filteredData['textarea']){
                                $("#"+child_id+"_"+flag).val(input_data)
                            }
                        }
                    }
                    column++
                }
                if(child_id > 0){
                    plan_comment(child_id)
                }
            }
        }
    }
    function downloadExcel(url){
        let link = document.createElement('a');
        link.href = url;
        link.download = '';
        link.click();
        setTimeout(function() {
            // 延时释放掉obj
            URL.revokeObjectURL(link.href);
            link.remove();
        }, 500);
    }

    //评语 拼接 that.ptcPlanDefComment
    function plan_comment(child_id){
        let comment = evaluationTemplate['template']; //评语模板
        let options = tempOptions;//可填写的评语项
        comment = comment.replace(/%name%/g, students[classid_global][child_id]['nick'])
        comment = comment.replace(/%child_name%/g, students[classid_global][child_id]['child_name'])
        for (const optionKey in options) {
            let flag = options[optionKey]['flag']
            let isOption = options[optionKey]['options']
            let isTextarea = options[optionKey]['textarea']
            let flagData = $("#"+child_id+"_"+flag).val()
            let optionTemp = '';
            if(isOption){
                optionTemp = options[optionKey]['options'][flagData]['template']
            }
            if(isTextarea){
                optionTemp = flagData
            }
            comment = comment.replace("%"+flag+"%", optionTemp)
        }
        $("#"+child_id+"_en").val(comment);
        textareaAssist( $("#"+child_id+"_en")[0])
    }

</script>
