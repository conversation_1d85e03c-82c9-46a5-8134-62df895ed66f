<?php
$token = sprintf('%s&%s&%s&%s', $this->branchid, $this->startyear, $this->classid, $this->controller->securityKey);
$token = md5($token);
?>
<script>
    var _SM_qParams = {
        startYear: <?php echo $this->startyear;?>,
        classId: <?php echo $this->classid;?>,
        weekNumber: <?php echo $this->weeknum;?>,
        page: 1,
        domainId: 0,
        childId: 0,
        freeTag: '',
        childList: null
    };

    var _SM_childData = _.sortBy(<?php echo CJSON::encode($children); ?>, 'name');
    var _SM_ldomain = <?php echo CJSON::encode(CHtml::listData(Term::model()->ld()->findAll(),'diglossia_id',
        function($model){
            return ( Yii::app()->language == 'en_us' ) ? $model->entitle : $model->cntitle;
        }))?>;

    //调用远程图片服务器使用这个
    <!--    var _SM_tmpUrl = '--><?php //echo $this->createUrl("//notifyBroker/fetchWeeklyMediaJS", array('token'=>$token, 'time'=>$taskData['params']['time']));?><!--';-->
    //调用本机图片服务器使用这个
    var _SM_tmpUrl = '<?php echo $this->controller->createUrl("//notifyBroker/fetchWeekly", array('token'=>$token, 'time'=>time()));?>';
    //    var page = 0;
    var _SM_contentData;

    function _SM_loadMedia(obj){
        _SM_qParams.page = parseInt( $(obj).parents('li[pageno]').attr('pageno') );

        _SM_loadContentData();
    }

    function _SM_setChildData(params){
        _.each(params, function(_item,_index){

        })
    }

    function _SM_renderPage(){
        if( _SM_contentData.pageData.pageCount > 1){
            $('#media-pagination').empty().show();
            for(var i=1; i<=_SM_contentData.pageData.pageCount; i++){
                $('<li pageno='+i+'></li>').html('<a href="javascript:;" onclick="_SM_loadMedia(this)">'+i+'</a>').appendTo($('#media-pagination'));
            }
            $('#media-pagination li[pageno|='+ ( parseInt(_SM_contentData.pageData.currentPage) + 1 ) + ']').addClass('active');
        }else{
            $('#media-pagination').empty().hide();
        }

        $('#media-container').empty();
        _.each(_SM_contentData.mediaData, function(_data,_index){
            $('#media-container').append( _.template($('#imgitem-action-tmpl').html(), _data) )
        });
    }

    function _SM_loadContentData(){
        var url = _SM_tmpUrl;
        _.each(_SM_qParams,function(_d,_i){
            url +='&' + _i + '=' + _d;
        });
        url += '&_=' + (new Date()).valueOf();
        url += '&callbackVarName=_SM_contentData';
        head.load(
            url,
            function() {
                _SM_renderPage();
            }
        );
    }

    function _SM_applyFilter(){
        _SM_qParams.page = 1;
        _SM_qParams.childId = $('#filter_childid').val();
        _SM_qParams.weekNumber = $('#filter_wnumber').val();
        _SM_qParams.domainId = $('#filter_ldomain').val();
        _SM_qParams.freeTag = $('#filter_freetag').val();
        _SM_loadContentData();
    }

    $(function(){
        var childidObj = $('#filter_childid');
        var ldomainObj = $('#filter_ldomain');
        childidObj.empty();
        ldomainObj.empty();
        var _option = $('<option></option>').val(0).text("<?php echo Yii::t('teaching','All Students');?>");
        var _opt = $('<option></option>').val(0).text("<?php echo Yii::t('teaching','All Domains');?>");
        childidObj.append(_option);
        ldomainObj.append(_opt);

        _.each(_SM_childData, function(_data,_id){
            var _option = $('<option></option>').val(_data.id).text(_data.name);
            childidObj.append(_option);
        });

        $.each(_SM_ldomain, function(_id, _data){
            var _opt = $('<option></option>').val(_id).text(_data);
            ldomainObj.append(_opt);
        });
        childidObj.removeAttr('disabled');
        ldomainObj.removeAttr('disabled');
    });

    function _SM_selectPhotos()
    {
        _SM_loadContentData();
        $('#_SM_selectPhotos').modal();
    }

    function _SM_ActionDone()
    {
        <?php if($this->callback):?>
        var c = '<?php echo $this->callback;?>';
        var data = {};
        $.each($('.J_photo'), function(index, value){
            var obj = $(value);
            if(obj.is(':checked')){
                var v = obj.val();
                <?php if ($this->fetchBigPhoto):?>
                data[v] = _SM_contentData.mediaData[v]._url;
                <?php else:?>
                data[v] = $('#photo_'+v).attr('src');
                <?php endif;?>
            }
        });
        if(c){
            eval(c+'(data);');
        }
        <?php endif;?>
        $('#_SM_selectPhotos').modal('hide');
    }

    function _SM_loadBigPhoto(obj)
    {
        <?php if($this->enablePreview): ?>
        var _container = $('#SM-large-img-preview');
        var _oldImgUrl = _container.find('img.img-thumbnail').attr("src");
        {
            var _bigUrl = $(obj).attr('data-img-src');
            if(_.isUndefined(_bigUrl) || _bigUrl == _oldImgUrl){
                _container.empty();
                _container.hide(50);
            } else {
                var _img = $('<img class="img-thumbnail" onclick="_SM_loadBigPhoto()" />').attr('src', _bigUrl);
                _container.empty();
                _container.append(_img);
                _img.load(function(){
                    _container.show('blind',{},200);
                })
            }
        }
        <?php endif;?>
    }
</script>

<?php if($this->buttonLabel !== false):?>
<button type="button" class="btn btn-default" onclick="_SM_selectPhotos();"><?php echo $this->buttonLabel;?></button>
<?php endif;?>

<script type="text/template" id="imgitem-action-tmpl">
    <div class="pull-left mr10 mb10 img-select-box">
        <label>
        <img class="img-thumbnail" id="photo_<%= id%>" src="<%= url%>">
        <?php if($this->multiple){?>
        <input type="checkbox" name="photo[]" value="<%= id%>" class="J_photo">
        <?php }else{?>
        <input type="radio" name="photo" value="<%= id%>" class="J_photo">
        <?php }?>
        </label>
        <a class="btn-zoomin" data-img-src="<%= _url %>" href="javascript:;" onclick="_SM_loadBigPhoto(this)"><span
            class="glyphicon
        glyphicon-zoom-in"></span></a>
    </div>
</script>

<div id="SM-large-img-preview"></div>

<div class="modal" id="_SM_selectPhotos">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                <h4 class="modal-title"><?php echo Yii::t('teaching','Select Photos');?></h4>
            </div>
            <div class="modal-body">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <div class="form-inline" role="form">
                            <div class="input-group">
                                <div class="input-group-addon"><span class="glyphicon glyphicon-tag"></span></div>
                                <select class="form-control" size="1" name="filter[ldomain]" id="filter_ldomain" disabled style="width: 110px;">
                                    <!--place holder-->
                                </select>
                            </div>
                            <div class="input-group">
                                <div class="input-group-addon"><span class="glyphicon glyphicon-user"></span></div>
                                <select class="form-control" size="1" name="filter[childid]" id="filter_childid" disabled style="width: 110px;">
                                    <!--place holder-->
                                </select>
                            </div>
                            <div class="input-group">
                                <div class="input-group-addon"><span class="glyphicon glyphicon-calendar"></span></div>
                                <?php
                                Yii::import('common.models.calendar.CalendarWeek');
                                $cwCrit = new CDbCriteria();
                                $cwCrit->compare('yid', $this->yid );
                                $cwCrit->order = 'weeknumber DESC';
                                echo CHtml::dropDownList(
                                    'filter[wnumber]',
                                    $this->weeknum,
                                    CHtml::listData(CalendarWeek::model()->findAll($cwCrit),'weeknumber',
                                        function($model){
                                            return sprintf('W%02d %s ~ %s',
                                                $model->weeknumber,
                                                Yii::app()->dateFormatter->format('yyyy.MM.dd', $model->monday_timestamp),
                                                Yii::app()->dateFormatter->format('yyyy.MM.dd', $model->monday_timestamp + 7 * 24 * 3600)
                                            );
                                        }),
                                    array('class'=>'form-control', 'empty'=>Yii::t('teaching','All Weeks'))
                                );
                                ?>
                            </div>
                            <div class="input-group">
                                <div class="input-group-addon"><span class="glyphicon glyphicon-comment"></span></div>
                                <?php
                                echo CHtml::textField('filter[freetag]', '', array('class'=>'form-control length_1',
                                    'placeholder'=>Yii::t('teaching','Free Tag')));
                                ?>
                            </div>
                            <button class="btn btn-primary" onclick="_SM_applyFilter();"
                                    type="button"><?php echo Yii::t('global','Filter');?></button>
                        </div>
                    </div>
                    <div class="panel-body">
                        <div>
                            <ul class="pagination pagination-sm" id="media-pagination"></ul>
                        </div>
                        <div id="media-container">
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">
                    <?php echo Yii::t('global','Close');?></button>
                <button type="button" class="btn btn-primary" onclick="_SM_ActionDone();">
                    <?php echo Yii::t('global','OK');?></button>
            </div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->

<style>
    .img-select-box{position: relative;}
    .img-select-box input.J_photo{position: absolute; top: -2px; left: 2px;}
    .img-select-box a.btn-zoomin{position: absolute; top: 2px; left: 20px;}
    #SM-large-img-preview {
        width: auto;height: auto;position: fixed;top: 0px; left: 0px;
        z-index: 999999;}
</style>