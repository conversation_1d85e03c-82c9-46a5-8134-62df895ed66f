<?php

/**
 * This is the model class for table "ivy_survey_return_questions".
 *
 * The followings are the available columns in table 'ivy_survey_return_questions':
 * @property integer $id
 * @property integer $survey_id
 * @property integer $pid
 * @property string $type
 * @property string $cn_title
 * @property string $en_title
 * @property string $content_ext
 * @property string $content_ext_en
 * @property integer $is_required
 * @property integer $is_multiple
 * @property integer $is_memo
 * @property integer $is_signature
 * @property integer $is_invoice
 * @property integer $option_sort
 * @property integer $status
 * @property integer $created_at
 * @property integer $created_by
 * @property string $updated_at
 * @property integer $updated_by
 */
class SurveyReturnQuestions extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_survey_return_questions';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('survey_id, pid, type, cn_title, en_title, status, created_at, created_by, updated_at, updated_by', 'required'),
			array('survey_id, pid, is_required, is_multiple, is_signature, is_memo, option_sort, status, created_at, created_by, updated_by, jump_id', 'numerical', 'integerOnly'=>true),
			array('type, is_invoice', 'length', 'max'=>255),
			array('updated_at', 'length', 'max'=>11),
			array('content_ext, content_ext_en, to_survey, jump_id', 'safe'),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, survey_id, pid, type, cn_title, en_title, content_ext, content_ext_en, is_required, is_multiple, is_signature, is_memo, is_invoice, option_sort, status, created_at, created_by, updated_at, updated_by', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'survey_id' => 'Survey',
			'pid' => 'Pid',
			'type' => 'Type',
			'cn_title' => 'Cn Title',
			'en_title' => 'En Title',
			'content_ext' => 'Content Ext',
			'content_ext_en' => 'Content Ext En',
			'is_required' => 'Is Required',
			'is_multiple' => 'Is Multiple',
			'is_signature' => 'Is Signature',
			'is_memo' => 'Is Memo',
			'is_invoice' => 'Is Invoice',
			'option_sort' => 'Option Sort',
			'status' => 'Status',
			'created_at' => 'Created At',
			'created_by' => 'Created By',
			'updated_at' => 'Updated At',
			'updated_by' => 'Updated By',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('survey_id',$this->survey_id);
		$criteria->compare('pid',$this->pid);
		$criteria->compare('type',$this->type,true);
		$criteria->compare('cn_title',$this->cn_title,true);
		$criteria->compare('en_title',$this->en_title,true);
		$criteria->compare('content_ext',$this->content_ext,true);
		$criteria->compare('content_ext_en',$this->content_ext_en,true);
		$criteria->compare('is_required',$this->is_required);
		$criteria->compare('is_multiple',$this->is_multiple);
		$criteria->compare('is_signature',$this->is_signature);
		$criteria->compare('is_memo',$this->is_memo);
		$criteria->compare('is_invoice',$this->is_invoice);
		$criteria->compare('option_sort',$this->option_sort);
		$criteria->compare('status',$this->status);
		$criteria->compare('created_at',$this->created_at);
		$criteria->compare('created_by',$this->created_by);
		$criteria->compare('updated_at',$this->updated_at,true);
		$criteria->compare('updated_by',$this->updated_by);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return SurveyReturnQuestions the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

    public function getTitle()
    {
        switch (Yii::app()->language) {
            case "zh_cn":
                return  $this->cn_title ;
                break;
            case "en_us":
                return  $this->en_title;
                break;
        }
    }
}
