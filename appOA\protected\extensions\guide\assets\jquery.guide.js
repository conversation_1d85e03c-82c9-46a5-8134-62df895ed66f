(function($) {
    if($(window).width() < 768)
        return false;

    var gData = null;
    var _gindex = 0;

    $.guide = function(options, index) {
        gData=options;
        $.guide.reveal(index);
    }

    $.extend($.guide, {
        options: {
            
        },

        reveal: function(index){
            $.guide.clearguide();
            _gindex = index;

            if (typeof(index) == 'undefined'){return;}

            var selector = gData['guides'][index]['selector'];
            var next = gData['guides'][index]['next'];
            var prev = gData['guides'][index]['prev'];

            if (typeof(gData['guides'][index]['jsrun']) != 'undefined'){
                eval(gData['guides'][index]['jsrun']);
            }
            
            if (typeof(gData['guides'][index]['style']['mainbox']['leftOffset']) == 'undefined'){
                var leftOffset = 100;
            }
            else {
                var leftOffset = gData['guides'][index]['style']['mainbox']['leftOffset'];
            }
            
            if (gData['guides'][index]['fixed'] == 1){
                $('body').css('height', $(window).height());
                $('body').css('overflow', 'hidden');
            }

            var _mainContentWidth = 733; //提示窗口背景图宽度
            var _mainPageWidth = 950; //页面内容宽度 css page
            var _width = $(selector).outerWidth() //元素宽度
            var _height = $(selector).outerHeight(); //元素高度
            var dwidth = $(document.body).width();  //页面宽度
            var dheight = $(document).height(); //页面高度
            var offset = $(selector).offset()
            var div1_2w = parseFloat(offset.left+_width);
            var div3w = dwidth-div1_2w;
            var div1_2h = parseFloat(offset.top+_height);
            var div7h = dheight-div1_2h;
            var blankColWidth = parseFloat(( dwidth - _mainPageWidth ) / 2 );
            if (gData['guides'][index]['style']['mainbox']['pos'] == 'arrow'){
                var mainContentLeft = leftOffset;
            }
            else {
                var mainContentLeft = parseFloat( ( offset.left - blankColWidth -  leftOffset) * (-1) );
            }
            var mainContentBottom = typeof(gData['guides'][index]['style']['mainbox']['top']) == 'undefined' ? 50 : gData['guides'][index]['style']['mainbox']['top'];
            

            $('body').css('position', 'relative');

            var div1 = '<div class="guide" style="top:0;left:0;width:'+(offset.left)+'px;height:'+offset.top+'px"></div>';
            var div2 = '<div class="guide" style="top:0;left:'+offset.left+'px;width:'+_width+'px;height:'+offset.top+'px"></div>';
            var div3 = '<div class="guide" style="top:0;left:'+div1_2w+'px;width:'+div3w+'px;height:'+offset.top+'px"></div>';
            var div4 = '<div class="guide" style="top:'+offset.top+'px;left:0;width:'+offset.left+'px;height:'+_height+'px"></div>';
            var div5 = '<div class="highlight" onclick="$.guide.closeguide();" id="div5" style="top:'+offset.top+'px;left:'+offset.left+'px;width:'+_width+'px;height:'+_height+'px"></div>';
            div5 += '<div class="tip" style="top:'+parseInt(div1_2h+10)+'px;left:'+offset.left+'px">';//+gData['guides'][index]['caption']+' ';

            try{
                emClass = gData['guides'][index]['style']['arrow']['direction'];
            }catch(err){
                emClass = "up";
            }
            
            if(emClass=='undefined'){
                emClass = "up";
            }
            //emClass = "upright";
            try{
                emStyleTop = gData['guides'][index]['style']['arrow']['top'];
            }catch(err){
                emStyleTop = "0";
            }
            try{
                emStyleLeft = gData['guides'][index]['style']['arrow']['left'];
            }catch(err){
                emStyleLeft = "0";
            }
            <!--var emClass = ( typeof(gData['guides'][index]['style']['arrow']['class']) != 'undefined' ) ? gData['guides'][index]['style']['arrow']['class'] : 'up';-->
            div5 += '<em class="'+emClass+'" style="top:'+emStyleTop+'px;left:'+emStyleLeft+'px"></em>';
            div5 += '<div class="guide-content" style="right:'+mainContentLeft+'px; top:'+mainContentBottom+'px;">';
            div5 += '<div class="close" onclick="$.guide.closeguide();"></div>';
            div5 += '<div class="content">';
            div5 += gData['guides'][index]['caption'];
            
            div5 += '</div>';

            div5 += '</div>';
            div5 += '</div>';
            var div6 = '<div class="guide" style="top:'+offset.top+'px;left:'+div1_2w+'px;width:'+div3w+'px;height:'+_height+'px"></div>';
            var div7 = '<div class="guide" style="top:'+div1_2h+'px;left:0;width:'+offset.left+'px;height:'+div7h+'px"></div>';
            var div8 = '<div class="guide" style="top:'+div1_2h+'px;left:'+offset.left+'px;width:'+_width+'px;height:'+div7h+'px"></div>';
            var div9 = '<div class="guide" style="top:'+div1_2h+'px;left:'+div1_2w+'px;width:'+div3w+'px;height:'+div7h+'px"></div>';
            var div10= '<div class="galert" style="top:'+(offset.top-6)+'px;left:'+(offset.left-6)+'px;width:'+parseInt(_width+4)+'px;height:'+parseInt(_height+4)+'px;"></div>';

            var divhtml = div1+div2+div3+div4+div5+div6+div7+div8+div9+div10;

            $('body').append(divhtml);
        },
        
        clearguide: function(){
            $('.guide').remove();
            $('.highlight').remove();
            $('.galert').remove();
            $('.tip').remove();
            $('body').css('height', 'auto');
            $('body').css('overflow', 'auto');
        },

        getindex: function(){
            return _gindex;
        },
        
        closeguide: function(){
            if (typeof(gData['closeajax']) != 'undefined' && gData['closeajax']){
                $.get(gData['closeajax']);
            }
            this.reveal();
        }
    });

    setInterval(function(){$('.galert').fadeOut().fadeIn();}, 1000);

})(jQuery);
