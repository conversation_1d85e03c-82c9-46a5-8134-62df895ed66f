<script type="text/template" id="childlist-item-template">
    <li class="media class-child" childid=<%- id %>>
        <a class="pull-left" href="javascript:loadReportViewByChild(<%= id%>);">
            <img class="media-object child-face img-thumbnail"
                 src="<?php echo Yii::app()->params['OAUploadBaseUrl'] ?>/childmgt/<%= photo %>" title="<%- name %>">
        </a>

        <div class="media-body">
            <a href="javascript:loadReportViewByChild(<%= id%>);"><h5 class="media-heading"><%- name %></h5></a>
        </div>
        <h5 class="flag"><span class="glyphicon glyphicon-ok-sign"></span></h5>
    </li>
</script>

<script>
    var currentData = {};
    var childDataRaw = <?php echo CJSON::encode($taskData['children']); ?>;
    var childData = _.sortBy(childDataRaw, 'name');
    var childTpl = _.template($('#childlist-item-template').html()); //孩子列表模版
    currentData.onlined = [];
    currentData.onlined = <?php echo CJSON::encode( $taskData['onlined'] );?>; //已经上线的孩子ID；数组

    $(function () {

        showChildList = function () {
            $('#semester-report-child-list').empty();
            _.each(childData, function (_child) {
                var _view = childTpl(_child);
                $('#semester-report-child-list').append(_view);
            });
            showReportStats();
        }

        showReportStats = function () {
            $('#semester-report-child-list li[childid]').removeClass('online');
            _.each(currentData.onlined, function (_childId) {
                $('#semester-report-child-list li[childid|="' + _childId + '"]').addClass('online');
            })
        }

        setReportStatus = function (type, id) {
            $.post('<?php echo $this->createUrl('setDsreportStatus')?>', {id: id, type: type}, function (data) {
                if (data.state == 'success') {
                    resultTip({msg: data.message});
                    loadReportViewByChild(currentData.childId);
                    if (type == 'online') {
                        currentData.onlined.push(currentData.childId);
                    }
                    else {
                        currentData.onlined.splice(currentData.onlined.indexOf(currentData.childId), 1);
                    }
                    showReportStats();
                }
                else {
                    resultTip({msg: data.message, error: 1});
                }
            }, 'json');
        }

        reportPreview = function (childid, id, classid, yid, courseid) {
            var url = "<?php echo $this->createUrl('previewReportDs', array(
                'childid'=>'-childid-',
                'id'=>'-id-',
                'classid'=>'-classid-',
                'yid'=>'-yid-',
                'courseid'=>'-courseid-',
            ));?>";
            url = url.replace('-childid-', childid);
            url = url.replace('-id-', id);
            url = url.replace('-classid-', classid);
            url = url.replace('-yid-', yid);
            url = url.replace('-courseid-', courseid);
            window.open(url);
        }

        cbSuccess = function () {
            $('#modal1').modal("hide");
            $('#modal').modal("hide");
            $('.modal-backdrop').hide();
            loadReportViewByChild(currentData.childId);
        };

        var ajaxing = false;
        var getChildReport = '<?php
            echo $this->createUrl('getChildSemesterReport01',array(
                'classid'=>$this->selectedClassId,
                'semester'=>$this->selectedSemester,
                'yid'=>$taskData['calendarData']['yid']
                ))
                ?>';
        loadReportViewByChild = function (childId) {
            if (!ajaxing) {
                $('.report-edit-zone').hide();
                $('#info-guide-box').html('<?php echo Yii::t("global", "Loading Data...");?>').show();
                currentData.childId = childId;
                ajaxing = !ajaxing;
                $.ajax({
                    type: 'post',
                    url: getChildReport,
                    dataType: 'html',
                    data: {childid: currentData.childId}
                }).done(function (data) {
                    $('#info-guide-box').hide();
                    ajaxing = false;
                    $('.report-edit-zone').html(data);
                    head.Util.ajaxForm();
                    head.Util.ajaxDel();
                    head.Util.modal();
                    $('.report-edit-zone').show('blind', {}, 500);
                });
            }
        }

        showChildList();
    })

    function setAll(type) {
        $.post(
            '<?php echo $this->createUrl('setAllDs', array('classid'=>$classid, 'yid'=>$taskData['historyYid'], 'semester'=>$semester))?>',
            {type: type},
            function (data) {
                if (data.state == 'success') {
                    resultTip({msg: data.message});
                    currentData.onlined = data.data;
                    showChildList();
                }
                else {
                    resultTip({msg: data.message, error: 1});
                }
            },
            'json'
        );
    }
</script>

<div class="row">

    <div class="col-md-3 col-lg-2">
        <div class="panel panel-default">
            <div class="panel-heading">
                <h5 class="pull-left">
                    <?php echo Yii::t("teaching", "Child List"); ?>
                </h5>
                <div class="dropdown pull-right">
                    <button class="btn btn-default" data-toggle="dropdown"><?php echo Yii::t('teaching',
                            'Batch'); ?> <span class="caret"></span></button>
                    <ul class="dropdown-menu">
                        <li><a href="javascript:;"
                               onclick="setAll('online');"><?php echo Yii::t('teaching', 'Make All Online'); ?></a>
                        </li>
                        <li><a href="javascript:;"
                               onclick="setAll('offline');"><?php echo Yii::t('teaching', 'Make All Offline'); ?></a>
                        </li>
                    </ul>
                </div>
                <div style="clear: both"></div>
            </div>
            <div class="panel-body">
                <ul class="media-list" id="semester-report-child-list"></ul>
            </div>
        </div>
    </div>

    <div class="col-md-9 col-lg-10">

        <div class="panel panel-default">
            <div class="panel-heading">
                <?php echo Yii::t('teaching', 'Semester Report'); ?>
            </div>
            <div class="panel-body">
                <div id="info-guide-box"><?php echo Yii::t('teaching', 'Click child list left side to start'); ?></div>
                <div class="panel-body report-edit-zone" style="display: none;"></div>
            </div>
        </div>
    </div>

</div>

<script>

</script>

<style>
    .child-face {
        max-width: 40px;
    }

    #semester-report-child-list .media {
        padding-left: 22px;
        position: relative
    }

    #semester-report-child-list h5.flag {
        position: absolute;
        top: 2px;
        left: 0px;
        font-size: 16px;
        color: #f2f2f2
    }

    #semester-report-child-list .online h5.flag {
        color: #008000
    }

    .content-item .img-thumbnail {
        margin: 0 8px 8px 0;
    }

    .content-item .panel, .content-item h4 {
        margin-bottom: 0px;
    }

    .editing .panel, .preview .panel {
        margin-top: 8px;
    }

    .child-select-box {
        position: relative;
    }

    .child-select-box input.J_child {
        position: absolute;
        top: -2px;
        left: 2px;
    }
</style>