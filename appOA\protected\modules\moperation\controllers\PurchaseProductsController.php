<?php

class PurchaseProductsController extends ProtectedController
{
    public $actionAccessAuths = array(
    	// 'View'=>'ivystaff_opgeneral',
    	'Index'=>'oOperationsView',
    	'Update'=>'oOperationsAdmin',
    	'Delete'=>'oOperationsAdmin',
    	'AddStandard'=>'oOperationsAdmin',
    	'DeletePic'=>'oOperationsAdmin',
    );

    public $dialogWidth = 600;
    
    public function init() {
        parent::init();
        Yii::import('common.models.purchase.*');

        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'hqOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site','HQ Operations');
           $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/vue2.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/bootstrap-select.min.js');
        $cs->registerCssFile(Yii::app()->themeManager->baseUrl.'/base/css/bootstrap-select.min.css');
        $cs->registerCssFile($cs->getCoreScriptUrl().'/jui/css/base/jquery-ui.css');
        $cs->registerScriptFile($cs->getCoreScriptUrl().'/jui/js/jquery-ui.min.js');
    }

	/**
	 * Updates a particular model.
	 * If update is successful, the browser will be redirected to the 'view' page.
	 * @param integer $id the ID of the model to be updated
	 */
	public function actionUpdate($id=0)
	{
        Yii::import('common.models.vendor.*');

        $modelVendor = VendorType::model()->findAll();
        $data = array();
        foreach ($modelVendor as $val){
            $data['cagegorys'][$val->vendor_type_id] = $val->getTitle();
        }

        $criteria = new CDbCriteria;
        $criteria->compare('status ',"<>99");
        $vendorMode = Vendor::model()->findAll($criteria);

        if($vendorMode) {
            foreach ($vendorMode as $val) {
                $data['data'][] = array(
                    'id' => $val->vendor_id,
                    'title' => Yii::app()->language == 'zh_cn' ? $val->cn_title : $val->en_title,
                    'type_id' => $val->vendor_type_id,
                );
            }
        }

		$model=$this->loadModel($id);

		$diglossiaArray = $this->getDiglossias();
		if(isset($_POST['PurchaseProducts']))
		{
			$model->attributes = $_POST['PurchaseProducts'];
			$model->uploadedFile = CUploadedFile::getInstance($model,'uploadedFile');
			$time = time();
			if ($model->isNewRecord) {
				$model->add_timestamp = $time;
			}
			$model->update_timestamp = $time;
			$model->add_user = $this->staff->uid;
			if($model->save()){
                if(isset($_POST['company']))
                {
                    $criteria = new CDbCriteria;
                    $criteria->compare('pid ',$model->id);
                    $company = PurchaseProductsCompany::model()->findAll($criteria);
                    if($company){
                        foreach ($company as $val){
                            $val->delete();
                        }
                    }
                    foreach ($_POST['company'] as $companyId => $val){
                        $companyModel = new PurchaseProductsCompany();
                        $companyModel->pid = $model->id;
                        $companyModel->company_id = $companyId;
                        $companyModel->start_time = strtotime($val['start']);
                        $companyModel->end_time = strtotime($val['end']);
                        $companyModel->updated_at = time();
                        $companyModel->updated_by = Yii::app()->user->id;
                        $companyModel->save();
                    }
                }
				//ajax通知
                $this->addMessage('state', 'success');
                $this->addMessage('callback', 'cbProduct');
                $this->addMessage('message', Yii::t('message','Data saved!'));
            }else {
                $this->addMessage('state', 'fail');
                $errs = current($model->getErrors());
                $this->addMessage('message', $errs?$errs[0]:Yii::t('message','Saving Failed!'));
            }
            $this->showMessage();
		}
		$companyData = array();
        if(isset($model) && isset($model->id)){
            $criteria = new CDbCriteria;
            $criteria->compare('pid ',$model->id);
            $company = PurchaseProductsCompany::model()->findAll($criteria);
           
            if($company){
                foreach ($company as $val){
                    $companyData[] = array(
                        'id' => $val->company_id,
                        'start' => ($val->start_time) ? date("Y-m-d", $val->start_time) : '',
                        'end' => ($val->end_time) ? date("Y-m-d", $val->end_time) : '',
                    );
                }
            }
        }

		$this->renderPartial('update',array(
			'model'=>$model,
			'diglossiaArray'=>$diglossiaArray,
			'data'=>$data,
			'companyData'=>$companyData,
		));
	}

	/**
	 * 显示商品
	 * @param  [type] $id [商品id]
	 * @return [type]     [description]
	 */
	public function actionView($id)
	{
		$model = $this->loadModel($id);
		if (!$model->isNewRecord) {
			$diglossiaArray = $this->getDiglossias();
			$this->render('view',array(
				'model'=>$model,
				'diglossiaArray'=>$diglossiaArray,
				));
		}
		$this->redirect(array('index'));
	}

	/**
	 * Deletes a particular model.
	 * If deletion is successful, the browser will be redirected to the 'admin' page.
	 * @param integer $id the ID of the model to be deleted
	 */
	public function actionDelete()
	{
		if (isset($_POST['id'])) {
			$id = Yii::app()->request->getPost('id', 0);
			$model = $this->loadModel($id);
			if($model->delete()){
				$this->addMessage('state', 'success');
				$this->addMessage('callback', 'cbProduct');
				$this->addMessage('message', Yii::t('message','Data saved!'));
			}else{
				$this->addMessage('state', 'fail');
				$this->addMessage('message', '');
			}
			$this->showMessage();
		}
	}

	/**
	 * Manages all models.
	 */
	public function actionIndex()
	{
		$diglossiaArray = $this->getDiglossias();

		$model=new PurchaseProducts('search');
		$model->unsetAttributes();  // clear any default values
		if(isset($_GET['PurchaseProducts']))
			$model->attributes=$_GET['PurchaseProducts'];
		$this->render('index',array(
			'model'=>$model,
			'diglossiaArray'=>$diglossiaArray,
		));
	}

	/**
	 * 添加商品到标配
	 * @param  integer $id [description]
	 * @return [type]      [description]
	 */
	public function actionAddStandard($id = 0)
	{
		if ($id < 1) {
			return false;
		}
		if (isset($_POST['PurchaseProductsItem'])) {
			$ppiModel = new PurchaseProductsItem;
			$ppiModel->attributes = $_POST['PurchaseProductsItem'];
			$exists = PurchaseProductsItem::model()->exists('sid=:sid and pid=:pid',array(':sid'=>$ppiModel->sid,':pid'=>$ppiModel->pid));
			if ($exists) {
				$this->addMessage('state','fail');
	            $this->addMessage('message', Yii::t('message','标配中已存在此商品'));
	            $this->showMessage();
			}
			if ($ppiModel->save()) {
				$this->addMessage('state','success');
				$this->addMessage('callback', 'cbProduct');
	            $this->addMessage('message', Yii::t('message','Data saved!'));
			}else{
				$this->addMessage('state','fail');
				$errs = current($ppiModel->getErrors());
				$this->addMessage('message', $errs?$errs[0]:Yii::t('message','Saving Failed!'));
			}
			$this->showMessage();
		}
		$sdModels = PurchaseStandard::model()->findAll();
		$pName = PurchaseProducts::model()->findByPk($id)->cn_name;
		$this->renderPartial('addStandard',array(
				'sdModels'=>$sdModels,
				'pid'=>$id,
				'pName'=>$pName,
			));
	}

	/**
	 * Returns the data model based on the primary key given in the GET variable.
	 * If the data model is not found, an HTTP exception will be raised.
	 * @param integer $id the ID of the model to be loaded
	 * @return PurchaseProducts the loaded model
	 * @throws CHttpException
	 */
	public function loadModel($id)
	{
		$model=PurchaseProducts::model()->findByPk($id);
		if($model===null){
			$model = new PurchaseProducts;
        }

		return $model;
	}

	/**
	 * Performs the AJAX validation.
	 * @param PurchaseProducts $model the model to be validated
	 */
	protected function performAjaxValidation($model)
	{
		if(isset($_POST['ajax']) && $_POST['ajax']==='purchase-products-form')
		{
			echo CActiveForm::validate($model);
			Yii::app()->end();
		}
	}

	/**
	 * 获取分类数组
	 * @return [type] [description]
	 */
	public function getDiglossias()
	{
		Yii::import('common.models.Diglossia');
		Yii::import('common.models.DiglossiaCategory');

		$diglossiaCate = array();
		$diglossiaArray = array();

		$arr = array('productcate','productarea');
		$criteria = new CDbCriteria;
		$criteria->compare('category_sign',$arr);
		$diglossiaCateModels = DiglossiaCategory::model()->findAll($criteria);
		foreach ($diglossiaCateModels as $diglossiaCateModel) {
			if ($diglossiaCateModel->category_sign == 'productcate') {
				foreach ($diglossiaCateModel->diglossia as $diglossia) {
					$diglossiaArray['cate'][$diglossia->diglossia_id] = ('zh_cn' == Yii::app()->language)?$diglossia->cntitle:$diglossia->entitle;
				}
			} elseif ($diglossiaCateModel->category_sign == 'productarea') {
				foreach ($diglossiaCateModel->diglossia as $diglossia) {
					$diglossiaArray['area'][$diglossia->diglossia_id] = ('zh_cn' == Yii::app()->language)?$diglossia->cntitle:$diglossia->entitle;
				}
			}
		}
		return $diglossiaArray;
	}

	/**
	 * 删除采购商品图片
	 * @param  [type] $id [description]
	 * @return [type]     [description]
	 */
	public function actionDeletePic($id)
	{
		$model = $this->loadModel($id);
		$picPath = Yii::app()->params['OAUploadBasePath'] . '/purchase/';
		$picName = $model->pic;

		unlink($picPath . $picName );
		unlink($picPath . 'thumbs/' .$picName);
		
		$model->pic = '';
		$model->save();
        $this->addMessage('state','success');
        $this->addMessage('message', Yii::t('message','删除成功'));
        $this->showMessage();

	}

	/**
	 * 搜索商品
	 * @param  [type] $term [description]
	 * @return [type]       [description]
	 */
	public function actionSearch($term)
	{
	    $sep = '%';
	    $limit = 30;
		$source=array();
		$criteria = new CDbCriteria;
		$criteria->limit = $limit;	
		$criteria->compare("concat_ws(',' , id, t.cn_name)", $term, true);
		$founds = PurchaseProducts::model()->findAll($criteria);

		$i=0;
		
		if($founds){
			foreach($founds as $found){
				$source[] = array(
					'label'=> $found->cn_name,
					'value'=> $found->cn_name . $sep . $found->id . $sep . $found->price. $sep .$found->cid. $sep .'1',
				);
				$i++;
			}		
		}else{
			$source[] = array(
				'label' => Yii::t('message', 'Sorry, no match found!'),
				'value' => 0,
			);
		}
		if($limit == $i ){
			$source[] = array(
				'label'=> Yii::t('message', '查询结果只显示:limit条信息，其余已被隐藏，请精确条件后再查询。', array(':limit'=>$limit)),
				'value'=> -2,
			);		
		}
		
		echo CJSON::encode($source);
	}

	/**
	 * 显示图片
	 * @param  [type] $data [description]
	 * @return [type]       [description]
	 */
	public function showPic($data)
	{
 		if ($data->pic) {
 			echo CHtml::image(Yii::app()->params['OAUploadBaseUrl'].'/purchase/thumbs/'.$data->pic,$data->id,array("style"=>"height:50px")).'<br>';
 		}
 		echo $data->cn_name;
	}

	public function showCate($data)
	{
		$diglossiaArray = $this->getDiglossias();
		echo $diglossiaArray['cate'][$data->cid];
	}

	public function showArea($data)
	{
		$diglossiaArray = $this->getDiglossias();
		echo $diglossiaArray['area'][$data->aid];
	}

	public function showType($data)
	{
		$diglossiaArray = $this->getDiglossias();
		echo $diglossiaArray['type'][$data->tid];
	}

	public function showBtn($data)
	{
		echo '<button class="btn btn-primary btn-xs" type="button"  onclick="addStandard('.$data->id.')" title="添加到标配"><span class="glyphicon glyphicon-plus"></span></button>  ';

		echo '<button class="btn btn-info btn-xs" type="button" onclick="update('.$data->id.')" title="更新"><span class="glyphicon glyphicon-pencil"></span></button>  ';
		
		echo '<a href="'.$this->createUrl('delete', array('id'=>$data->id)).'" class="btn btn-danger btn-xs J_ajax_del" ><span class="glyphicon glyphicon-trash"></span></a>';
	}

}
