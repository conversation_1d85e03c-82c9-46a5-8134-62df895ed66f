<?php

class PtaMemoController extends ProtectedController
{
    public $dialogWidth = 500;
	/**
	 * @var string the default layout for the views. Defaults to '//layouts/column2', meaning
	 * using two-column layout. See 'protected/views/layouts/column2.php'.
	 */
	public $layout='//layouts/column2';
    
    public $defaultAction = 'admin';

	public function init(){
		parent::init();
		
		//本Controller全局的权限，根据实际情况自行设定
		if(!Yii::app()->user->checkAccess("oPtaAdmin")){
			throw new CException('Permission Denied');
		}

        Yii::import('common.models.pta.*');
	}
	
    /**
	 * @return array action filters
	 */
	public function filters()
	{
		return array(
			'accessControl', // perform access control for CRUD operations
		);
	}

	/**
	 * Specifies the access control rules.
	 * This method is used by the 'accessControl' filter.
	 * @return array access control rules
	 */
	public function accessRules()
	{
		return array(
			array('allow',  // deny all users
				'users'=>array('@'),
			),
		);
	}

	/**
	 * Creates a new model.
	 * If creation is successful, the browser will be redirected to the 'view' page.
	 */
	public function actionCreate($id=0)
	{
        $this->actionUpdate($id);
	}

	/**
	 * Updates a particular model.
	 * If update is successful, the browser will be redirected to the 'view' page.
	 * @param integer $id the ID of the model to be updated
	 */
	public function actionUpdate($id)
	{
		$model=PtaMemo::model()->findByPk($id);
        if ($model === null){
            $model = new PtaMemo;
        }
        else {
            $model->memo_date = date('Y-m-d', $model->memo_date);
            $model->renderMembers();
        }

        $model1=PtaMemoContent::model()->findByPk($id);
        if ($model1 === null){
            $model1 = new PtaMemoContent;
        }

        $schools = Branch::model()->getBranchList();

		if(isset($_POST['PtaMemo']))
		{
			$model->attributes=$_POST['PtaMemo'];
            $model->memo_date = strtotime($_POST['PtaMemo']['memo_date']);
            $model->timestamp = time();
            $model->uid = Yii::app()->user->id;
            
            $model1->attributes=$_POST['PtaMemoContent'];
                
            if ($model->validate() && $model1->validate()){
                $model->save(false);
                $model1->id = $model->id;
                $model1->save(false);
                $this->redirect(array('admin'));
            }
            else {
                $model->memo_date = date('Y-m-d', $model->memo_date);
            }
		}

		$this->render('create',array(
			'model'=>$model,
			'model1'=>$model1,
            'schools'=>$schools,
		));
	}

	/**
	 * Deletes a particular model.
	 * If deletion is successful, the browser will be redirected to the 'admin' page.
	 * @param integer $id the ID of the model to be deleted
	 */
	public function actionDelete($id)
	{
		if(Yii::app()->request->isPostRequest)
		{
			// we only allow deletion via POST request
			$this->loadModel($id)->delete();
            
            PtaMemoContent::model()->deleteByPk($id);

			// if AJAX request (triggered by deletion via admin grid view), we should not redirect the browser
			if(!isset($_GET['ajax']))
				$this->redirect(isset($_POST['returnUrl']) ? $_POST['returnUrl'] : array('admin'));
		}
		else
			throw new CHttpException(400,'Invalid request. Please do not repeat this request again.');
	}

	/**
	 * Manages all models.
	 */
	public function actionAdmin()
	{
		$model=new PtaMemo('search');
		$model->unsetAttributes();  // clear any default values
		if(isset($_GET['PtaMemo']))
			$model->attributes=$_GET['PtaMemo'];

		$this->render('admin',array(
			'model'=>$model,
		));
	}

	/**
	 * Returns the data model based on the primary key given in the GET variable.
	 * If the data model is not found, an HTTP exception will be raised.
	 * @param integer the ID of the model to be loaded
	 */
	public function loadModel($id)
	{
		$model=PtaMemo::model()->findByPk($id);
		if($model===null)
			throw new CHttpException(404,'The requested page does not exist.');
		return $model;
	}

	/**
	 * Performs the AJAX validation.
	 * @param CModel the model to be validated
	 */
	protected function performAjaxValidation($model)
	{
		if(isset($_POST['ajax']) && $_POST['ajax']==='pta-memo-form')
		{
			echo CActiveForm::validate($model);
			Yii::app()->end();
		}
	}
    
    public function actionAdminmember($id=0, $schoolid)
    {
        $this->layout='//layouts/dialog';
        Yii::import('common.models.hr.*');
        $criteria = new CDbCriteria;
        $criteria->compare('profile.branch', $schoolid);
        $criteria->compare('t.level', 1);
        $criteria->compare('t.isstaff', 1);
        $theusers = User::model()->with('profile')->findAll($criteria);
        $posids = array();
        $dps = array();
        $deptitle = array();

        $pModel = PtaMemo::model()->model()->findByPk($id);
        $usered = explode(',', substr($pModel->pta_admins, 0, -1));

        foreach ($theusers as $user){
            $posids[$user->profile->occupation_en][$user->uid] = array(
                'uid' => $user->uid,
                'name' => CommonUtils::autoLang($user->name, $user->profile->first_name.' '.$user->profile->last_name),
                'photo' => 'http://oa.ivyonline.cn/uploads/users/thumbs/'.$user->user_avatar,
            );
        }
        if($posids){
            $criteria = new CDbCriteria;
            $criteria->compare('position_id', array_keys($posids));
            $criteria->order = 'department.weight asc';
            $deppos = DepPosLink::model()->with('position', 'department')->findAll($criteria);
            foreach ($deppos as $dp){
                $deptitle[$dp->department_id] = Yii::app()->language=='en_us' ? $dp->department->en_name : $dp->department->cn_name;
                $dps[$dp->department_id][$dp->position_id] = array(
                    'title' => Yii::app()->language=='en_us' ? $dp->position->en_name : $dp->position->cn_name,
                    'lead' => $dp->is_lead,
                );
            }
        }
        
        $this->render('adminmember', array(
            'posids'    => $posids,
            'dps'       => $dps,
            'deptitle'  => $deptitle,
            'schoolid'  => $schoolid,
            'usered'   => $usered,
        ));
    }
    
    public function actionParentmember($id=0, $schoolid)
    {
        $this->layout='//layouts/dialog';
        $criteria = new CDbCriteria;
        $criteria->compare('schoolid', $schoolid);
        $criteria->compare('status', 0);
        $ptas = Pta::model()->findAll($criteria);

        $pModel = PtaMemo::model()->model()->findByPk($id);
        $usered = explode(',', substr($pModel->pta_members, 0, -1));
        
        $this->render('parentmember', array(
            'schoolid'  => $schoolid,
            'ptas' => $ptas,
            'usered'   => $usered,
        ));
    }
}
