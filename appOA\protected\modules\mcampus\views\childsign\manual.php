<style>
    .child-box {
        height: 140px;
        text-align: center;
    }

    .children_Class_SignIn .btn-default, .children_Class_SignIn .btn-success {
        margin-bottom: 10px;
    }

    .children_Class_SignIn .child_Sign_Label div {
        position: relative;
        margin-bottom: 5px;
    }

    .child_Sign_Label input[type="checkbox"] {
        position: absolute;
        left: 0;
        bottom: 0;;
    }

    .collapsed_Parents {
        position: relative;
        height: 40px;
    }

    .collapsed_Parents > p {
        position: absolute;
        width: 100%;
        font-size: 16px;
        left: 0;
        top: 0;
        margin: 0;
        line-height: 40px;
        height: 40px;
        padding: 0 10px;
    }
    .student_Leave {
        position: absolute;
        left: 0;
        bottom: 0;
        padding: 2px;
        border-radius: 3px;
        color:#c7254e;
        background: #f9f2f4;
    }
    /*灰色滤镜 加在img上*/
    .unsign_Img {
        -webkit-filter:grayscale(100%);
        -moz-filter:grayscale(100%);
        -o-filter:grayscale(100%);
        -ms-filter:grayscale(100%);
        filter:grayscale(100%);
    }
    @media screen and (max-width: 768px) {
        .have_Checked {
            padding: 10px 15px;
        }
    }
</style>
<script>var totalNumber = 0;</script>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('default/index')); ?></li>
        <li><?php echo CHtml::link(Yii::t('site','Routines'), array('//mcampus/default/index'))?></li>
        <li><?php echo CHtml::link(Yii::t('site','Child Attendance Management'), array('//mcampus/childsign/manual'))?></li>
        <li class="active"><?php echo Yii::t('campus', 'Manual'); ?></li>
    </ol>
    <div class="row">
        <div class="col-md-2">
            <div class="list-group" id="classroom-status-list">
                <a href="<?php echo $this->createUrl('manual'); ?>" class="list-group-item status-filter active"><?php echo Yii::t('campus', 'Manual'); ?></a>
                <a href="<?php echo $this->createUrl('index'); ?>" class="list-group-item status-filter"><?php echo Yii::t('campus', 'Scan Barcode'); ?></a>
                <a href="<?php echo $this->createUrl('vacation'); ?>" class="list-group-item status-filter"><?php echo Yii::t('campus', 'List of students on leave/Tardy'); ?></a>
            </div>
        </div>
        <div class="col-md-10">
            <div class="mb10 row">
                <div class="col-md-2">
                    <input id="datepicker" class="form-control" name="date"
                           value="<?php echo $_GET['date'] ? $_GET['date'] : date('Y-m-d', time()); ?>" readonly>
                </div>
                <div class="col-md-3 input-sm have_Checked">
                    <?php echo Yii::t('campus', 'Sign in: ')?><span class="label label-default"><span id='total-num'><?php echo count($signChilds); ?></span>/<?php echo count($childData); ?></span>
                    <?php echo Yii::t("campus",'Students on leave/Tardy') ?><?php echo Yii::t("global",': ') ?><span class="label label-default"><span id='total-num'><?php echo count($vacations); ?></span>
                </div>
            </div>

            <div class="row">
                <div class="col-md-12">
                    <div class="panel-group" id="accordion" role="tablist" aria-multiselectable="true">
                        <?php foreach ($classes as $class): ?>
                            <div class="panel panel-default">
                                <div class="panel-heading collapsed_Parents" role="tab"
                                     id="<?php echo $class->classid; ?>">
                                    <p class="collapsed" style="cursor: pointer" data-toggle="collapse"
                                       data-parent="#accordion" href="#col_<?php echo $class->classid ?>"
                                       aria-expanded="false"
                                       aria-controls="col_<?php echo $class->classid ?>"><?php echo $class->title; ?>
                                        <span class="label label-default"><span class="class_SignIn_Num_<?php echo $class->classid ?>"><?php echo count($signData[$class->classid]) ?></span>
                                            /<span><?php echo count($classnum[$class->classid]) ?>
                                            </span>
                                        </span>
                                    </p>
                                </div>
                                <div id="col_<?php echo $class->classid ?>"
                                     class="panel-collapse collapse children_Class_SignIn <?php if (count($classes) == 1) {
                                         echo "in";
                                     } ?>" role="tabpanel"
                                     aria-labelledby="<?php echo $class->classid; ?>">
                                    <div class="panel-body J_check_wrap">
                                        <div class="row">
                                            <div class="col-md-12">
                                                <div class="checkbox">
                                                    <label>
                                                        <input type="checkbox" class="J_check_all"
                                                               data-checklist="J_check_<?php echo $class->classid; ?>"
                                                               data-direction="y" style="margin-top:0"> <?php echo Yii::t('global', 'Select All')?>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <?php foreach ($childData as $child): ?>
                                                <?php if ($class->classid == $child['classid']): ?>
                                                    <div class="col-lg-1 col-md-2 col-sm-3 col-xs-6">
                                                        <div class="child-box">
                                                            <label class="child_Sign_Label">
                                                                <div>
                                                                    <img class="img-rounded img-responsive <?php if(isset($vacations[$child['id']])) echo 'unsign_Img';?>"
                                                                         src="<?php echo CommonUtils::childPhotoUrl($child['photo'], 'small'); ?>">
                                                                    <?php if(isset($vacations[$child['id']])):?>
                                                                        <span class="student_Leave"><?php echo $vacations[$child['id']]['title']?></span>
                                                                    <?php else:?>
                                                                        <input
                                                                            class="child-manual-<?php echo $class->classid ?> J_check"
                                                                            data-yid="J_check_<?php echo $class->classid; ?>" <?php echo (in_array($child['id'], $signData[$class->classid])) ? checked : "" ?>
                                                                            value="<?php echo $child['id'] ?>" type="checkbox">
                                                                    <?php endif;?>

                                                                </div>
                                                                <span><?php echo $child['name'] ?></span>
                                                            </label>
                                                        </div>
                                                    </div>
                                                <?php endif; ?>
                                            <?php endforeach; ?>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-12">
                                                <button style="float: none" class="btn btn-primary col-md-2 col-xs-4" onclick="childsign(<?php echo $class->classid ?>)"><?php echo Yii::t('global', 'OK')?></button>
                                                <span class="operation_uid_<?php echo $class->classid; ?>">
                                                    <?php
                                                    foreach($operation_uid as $k=>$v){
                                                        if($class->classid == $k){
                                                            echo " ".$v['name']. " " . $v['time'];
                                                        }
                                                    }
                                                    ?>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- 显示底部学校选择栏 -->
    <?php $this->renderPartial('//layouts/common/branchSelectBottom'); ?>
    <!-- 模态框 -->
    <div class="modal fade" id="modal" tabindex="-1" role="dialog" aria-labelledby="modal">
        <div class="modal-dialog" role="document">
            <div class="modal-content">

            </div>
        </div>
    </div>
    <!-- script代码 -->
    <script>

        $('#datepicker').datepicker({
            dateFormat: 'yy-mm-dd',
            maxDate: '<?php echo date('Y-m-d', time()); ?>'
        });

        $('#datepicker').bind('change', function (e) {
            window.location.href = '<?php echo $this->createUrl('manual'); ?>&date=' + $(this).val();
        });

        function childsign(classid) {
            if(confirm("<?php echo Yii::t('campus','Are you sure the children ticked have all arrived at school?') ?>")){
                var day = $("#datepicker").val();
                var childid = [];
                $('.child-manual-' + classid).each(function (i, val) {
                    if (val.checked && !val.disabled) {
                        childid[i] = $(val).val();
                    }
                });
                $.ajax({
                    type: 'post',
                    dataType: 'json',
                    url: '<?php echo $this->createUrl("manualAdd", array('branchId'=>$this->branchId));?>',
                    data: {childid: childid, classid: classid, day: day},
                    success: function (data) {
                        if (data.state == 'success') {
                            resultTip({msg: "<?php echo Yii::t('message', 'Data Saved!')?>"});
                            $("#total-num").text(data.data.signChilds);
                            $(".class_SignIn_Num_" + classid).text(data.data.childlist);
                            $(".operation_uid_" + classid).text(" " +data.data.operation_uid.name +  " "+ data.data.operation_uid.time);

                        }
                    }
                })
            }
        }

        // 回调
        function cbLasereggInfo() {
            $('#modal').modal('hide');
            $.fn.yiiGridView.update('points-product-grid');
        }
    </script>
