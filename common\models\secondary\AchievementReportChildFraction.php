<?php

/**
 * This is the model class for table "ivy_achievement_report_child_fraction".
 *
 * The followings are the available columns in table 'ivy_achievement_report_child_fraction':
 * @property integer $id
 * @property integer $timetable_records_id
 * @property integer $timetable_records_code
 * @property integer $report_id
 * @property integer $childid
 * @property integer $cid
 * @property integer $courseid
 * @property integer $optionid
 * @property string $value
 * @property integer $uid
 * @property integer $update_time
 */
class AchievementReportChildFraction extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_achievement_report_child_fraction';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('cid, timetable_records_id, timetable_records_code, courseid, optionid, value, uid, update_time', 'required'),
			array('report_id, timetable_records_id, childid, cid, courseid, optionid, uid, update_time', 'numerical', 'integerOnly'=>true),
			array('value, timetable_records_code', 'length', 'max'=>255),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, report_id, timetable_records_id, timetable_records_code, childid, cid, courseid, optionid, value, uid, update_time', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
			"courseScoresid" => array(self::BELONGS_TO, 'AchievementCourseScores', array('value'=>'id')),
			"courseCourse" => array(self::BELONGS_TO, 'AchievementReportChildCourse', array('cid'=>'id')),
			"courseStandard" => array(self::BELONGS_TO, 'AchievementReportCourseStandard', array('optionid'=>'id')),
			"report" => array(self::BELONGS_TO, 'AchievementReport', array('report_id'=>'id')),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'report_id' => 'Report',
			'timetable_records_id' => 'timetable_records_id',
			'timetable_records_code' => 'timetable_records_code',
			'childid' => 'Childid',
			'cid' => 'Cid',
			'courseid' => 'Courseid',
			'optionid' => 'Optionid',
			'value' => 'Value',
			'uid' => 'Uid',
			'update_time' => 'Update Time',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('timetable_records_id',$this->timetable_records_id);
		$criteria->compare('timetable_records_code',$this->timetable_records_code);
		$criteria->compare('report_id',$this->report_id);
		$criteria->compare('childid',$this->childid);
		$criteria->compare('cid',$this->cid);
		$criteria->compare('courseid',$this->courseid);
		$criteria->compare('optionid',$this->optionid);
		$criteria->compare('value',$this->value,true);
		$criteria->compare('uid',$this->uid);
		$criteria->compare('update_time',$this->update_time);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return AchievementReportChildFraction the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	public function getName()
	{
		switch (Yii::app()->language) {
			case "zh_cn":
				return  $this->introduction_cn ;
				break;
			case "en_us":
				return  $this->introduction_en;
				break;
		}
	}


	public function showData($type,$id,$yid,$schoolid)
    {
        $criteria = new CDbCriteria;
        $criteria->compare('type', $type);
        $criteria->compare('type_id', $id);
        $criteria->compare('yid', $yid);
        $model = AchievementReportCache::model()->find($criteria);

        $criteria = new CDbCriteria;
        $criteria->compare('calendar', $yid);
        $criteria->compare('schoolid', $schoolid);
        $criteria->index = 'id';
        $reportModel = AchievementReport::model()->findAll($criteria);
        $data = array();
        if($reportModel){
            $frequencys = array();
            $scoreNum = array();
            //类型为课程时候
            if($type == 'course'){
                // 根据课程program  查询出所有的课程
                $criteria = new CDbCriteria;
                $criteria->compare('program', $id);
                $criteria->index = 'id';
                $courseModel = TimetableCourses::model()->findAll($criteria);
            }

            //类型为老师时候
            if($type == 'teacher'){
                // 根据老师查询出课程ID
                $criteria = new CDbCriteria;
                $criteria->compare('teacher_id', $id);
                $criteria->index = 'course_id';
                $courseModel = TimetableCourseTeacher::model()->findAll($criteria);
            }

            $status = 1;
            if($model){
                // 根据不同的type 去拿数据 和缓存时间对比，缓存时间大于报告数据时间直接拿缓存
                $criteria = new CDbCriteria;
                if(in_array($type, array('course', 'teacaher'))) {
                    $criteria->compare('timetable_records_id', array_keys($courseModel));
                }
                if($type == 'class'){
                    $criteria->compare('timetable_records_code', $id);
                }
                $criteria->compare('report_id', array_keys($reportModel));
                $criteria->limit = '1';
                $criteria->group = 'update_time desc';
                $childFractionModelTime = AchievementReportChildFraction::model()->find($criteria);
                if($childFractionModelTime){
                    if($childFractionModelTime->update_time <= $model->updated_at){
                        $status = 0;
                    }
                }
            }

            // 0 缓存表时间大于报告表就不需要去重新生成
            // 1 重新生成
            if($status) {
                $crit = new CDbCriteria;
                if (in_array($type, array('course', 'teacaher'))) {
                    $crit->compare('timetable_records_id', array_keys($courseModel));
                }
                if ($type == 'class') {
                    $crit->compare('timetable_records_code', $id);
                }

                $crit->compare('report_id', array_keys($reportModel));
                $childFractionModel = AchievementReportChildFraction::model()->findAll($crit);

                $criteria = new CDbCriteria;
                if (in_array($type, array('course', 'teacaher'))) {
                    $criteria->compare('timetable_records_id', array_keys($courseModel));
                }
                if ($type == 'class') {
                    $criteria->compare('timetable_records_code', $id);
                }
                $criteria->compare('calender_id', $yid);
                $childTotal = AchievementReportChildTotal::model()->findAll($criteria);

                // 拿到整合数据的数组
                $childNum = 0;
                if ($childFractionModel) {
                    foreach ($childFractionModel as $val) {
                        if ($val->courseScoresid) {
                            $scoreNum[$val->report->cycle][$val->courseStandard->title_cn]['score'] += $val->courseScoresid->fraction;
                            $scoreNum[$val->report->cycle][$val->courseStandard->title_cn]['num'] += 1;
                            $frequencys['data'][$val->report->cycle][$val->courseStandard->title_cn][$val->courseScoresid->fraction] += 1;
                            $childNum++;
                        }
                    }
                }

                // 拿到总分的数据的数组
                $totle = array();
                if ($childTotal) {
                    foreach ($childTotal as $val) {
                        if ($val->courseScoresid) {
                            $totle['score'] += $val->oprion_value;
                            $totle['num'] += 1;
                            $totle['fractionNum'][$val->oprion_value] += 1;
                        }
                    }
                }

                $data = $this->splice($scoreNum, $frequencys, $id, $childNum, $totle);
                if(!$model){
                    $model = new AchievementReportCache();
                    $model->yid = $yid;
                    $model->type = $type;
                    $model->type_id = $id;
                    $model->status = 1;
                    $model->created_at = time();
                    $model->created_by = Yii::app()->user->id;
                }
                $model->average_data = json_encode($data['average']);
                $model->frequency_data = json_encode($data['frequency']);
                $model->updated_at = time();
                $model->updated_by = Yii::app()->user->id;
                $model->save();
            }else{
                $data['average'] = json_decode($model->average_data,true);
                $data['frequency'] = json_decode($model->frequency_data,true);
            }
        }
        return $data;
    }

    /*
     *
     * $scoreNum   计算平均分数的数据组
     * $frequencys  计算人数的数据组
     * $id     类型
     * $childNum  孩子总人数
     * $totle  计算总分的的数据在
     */
    public function splice($scoreNum,$frequencys,$id,$childNum,$totle)
    {
        $array = array(
            'score1' => array(
                'peroid1' => array('A' => 0, 'B' => 0, 'C' => 0, 'D' => 0),
                'peroid2' => array('A' => 0, 'B' => 0, 'C' => 0, 'D' => 0),
                'peroid3' => array('A' => 0, 'B' => 0, 'C' => 0, 'D' => 0),
                'peroid4' => array('A' => 0, 'B' => 0, 'C' => 0, 'D' => 0),
                'yearend' => 0,
            ),
            'score2' => array(
                'peroid1' => array('A' => 0, 'B' => 0, 'C' => 0, 'D' => 0),
                'peroid2' => array('A' => 0, 'B' => 0, 'C' => 0, 'D' => 0),
                'peroid3' => array('A' => 0, 'B' => 0, 'C' => 0, 'D' => 0),
                'peroid4' => array('A' => 0, 'B' => 0, 'C' => 0, 'D' => 0),
                'yearend' => 0,
            ),
            'score3' => array(
                'peroid1' => array('A' => 0, 'B' => 0, 'C' => 0, 'D' => 0),
                'peroid2' => array('A' => 0, 'B' => 0, 'C' => 0, 'D' => 0),
                'peroid3' => array('A' => 0, 'B' => 0, 'C' => 0, 'D' => 0),
                'peroid4' => array('A' => 0, 'B' => 0, 'C' => 0, 'D' => 0),
                'yearend' => 0,
            ),
            'score4' => array(
                'peroid1' => array('A' => 0, 'B' => 0, 'C' => 0, 'D' => 0),
                'peroid2' => array('A' => 0, 'B' => 0, 'C' => 0, 'D' => 0),
                'peroid3' => array('A' => 0, 'B' => 0, 'C' => 0, 'D' => 0),
                'peroid4' => array('A' => 0, 'B' => 0, 'C' => 0, 'D' => 0),
                'yearend' => 0,
            ),
            'score5' => array(
                'peroid1' => array('A' => 0, 'B' => 0, 'C' => 0, 'D' => 0),
                'peroid2' => array('A' => 0, 'B' => 0, 'C' => 0, 'D' => 0),
                'peroid3' => array('A' => 0, 'B' => 0, 'C' => 0, 'D' => 0),
                'peroid4' => array('A' => 0, 'B' => 0, 'C' => 0, 'D' => 0),
                'yearend' => 0,
            ),
        );


        // 按照分数计算周期ABCE的人数
        if($frequencys['data']){
            foreach ($frequencys['data'] as $report_id => $item) {
                foreach ($item as $standard_id => $val) {
                    foreach ($val as $k => $num) {
                        if($k == 0){
                            $array['score1']['peroid' . $report_id][$standard_id] += $num;
                        }
                        if(in_array($k, array(1,2))){
                            $array['score2']['peroid' . $report_id][$standard_id] += $num;
                        }
                        if(in_array($k, array(3,4))){
                            $array['score3']['peroid' . $report_id][$standard_id] += $num;
                        }
                        if(in_array($k, array(5,6))){
                            $array['score4']['peroid' . $report_id][$standard_id] += $num;
                        }
                        if(in_array($k, array(7,8))){
                            $array['score5']['peroid' . $report_id][$standard_id] += $num;
                        }
                    }
                }
            }
        }

        // 按照分数计算总人数
        if($totle['fractionNum']){
            foreach ($totle['fractionNum'] as $k=>$val){

                if($k == 0){
                    $array['score1']['yearend'] += $val;
                }
                if(in_array($k, array(1,2))){
                    $array['score2']['yearend'] += $val;
                }
                if(in_array($k, array(3,4))){
                    $array['score3']['yearend'] += $val;
                }
                if(in_array($k, array(5,6))){
                    $array['score4']['yearend'] += $val;
                }
                if(in_array($k, array(7,8))){
                    $array['score5']['yearend'] += $val;
                }
            }
        }

        $data['average'] = array('name' => $id, 'total_students' => $childNum);
        $data['frequency'] = array(
            'name' => $id,
            'total_students' => $childNum,
            'score1' => $array['score1'],
            'score2' => $array['score2'],
            'score3' => $array['score3'],
            'score4' => $array['score4'],
            'score5' => $array['score5'],
        );

        if($scoreNum){
            $peroid1 = ($scoreNum[1]) ? $scoreNum[1] : array();
            $peroid1Data1 = array('-','-','-','-');
            if($peroid1){
                $peroid1Data1[0] = ($peroid1['A']) ? sprintf('%.2f', $peroid1['A']['score'] / $peroid1['A']['num']) : '-';
                $peroid1Data1[1] = ($peroid1['B']) ? sprintf('%.2f', $peroid1['B']['score'] / $peroid1['B']['num']) : '-';
                $peroid1Data1[2] = ($peroid1['C']) ? sprintf('%.2f', $peroid1['C']['score'] / $peroid1['C']['num']) : '-';
                $peroid1Data1[3] = ($peroid1['D']) ? sprintf('%.2f', $peroid1['D']['score'] / $peroid1['D']['num']) : '-';
            }

            $peroid2 = ($scoreNum[2]) ? $scoreNum[2] : array();
            $peroid1Data2 = array('-','-','-','-');
            if($peroid2){
                $peroid1Data2[0] = ($peroid2['A']) ? sprintf('%.2f', $peroid2['A']['score'] / $peroid2['A']['num']) : '-';
                $peroid1Data2[1] = ($peroid2['B']) ? sprintf('%.2f', $peroid2['B']['score'] / $peroid2['B']['num']) : '-';
                $peroid1Data2[2] = ($peroid2['C']) ? sprintf('%.2f', $peroid2['C']['score'] / $peroid2['C']['num']) : '-';
                $peroid1Data2[3] = ($peroid2['D']) ? sprintf('%.2f', $peroid2['D']['score'] / $peroid2['D']['num']) : '-';
            }
            $peroid3 = ($scoreNum[3]) ? $scoreNum[3] : array();
            $peroid1Data3 = array('-','-','-','-');
            if($peroid3){
                $peroid1Data3[0] = ($peroid3['A']) ? sprintf('%.2f', $peroid3['A']['score'] / $peroid3['A']['num']) : '-';
                $peroid1Data3[1] = ($peroid3['B']) ? sprintf('%.2f', $peroid3['B']['score'] / $peroid3['B']['num']) : '-';
                $peroid1Data3[2] = ($peroid3['C']) ? sprintf('%.2f', $peroid3['C']['score'] / $peroid3['C']['num']) : '-';
                $peroid1Data3[3] = ($peroid3['D']) ? sprintf('%.2f', $peroid3['D']['score'] / $peroid3['D']['num']) : '-';
            }
            $peroid4 = ($scoreNum[4]) ? $scoreNum[4] : array();
            $peroid1Data4 = array('-','-','-','-');
            if($peroid4){
                $peroid1Data4[0] = ($peroid4['A']) ? sprintf('%.2f', $peroid4['A']['score'] / $peroid4['A']['num']) : '-';
                $peroid1Data4[1] = ($peroid4['B']) ? sprintf('%.2f', $peroid4['B']['score'] / $peroid4['B']['num']) : '-';
                $peroid1Data4[2] = ($peroid4['C']) ? sprintf('%.2f', $peroid4['C']['score'] / $peroid4['C']['num']) : '-';
                $peroid1Data4[3] = ($peroid4['D']) ? sprintf('%.2f', $peroid4['D']['score'] / $peroid4['D']['num']) : '-';
            }

            $yearend = 0;
            if($totle){
                $yearend = sprintf('%.2f', $totle['score'] / $totle['num']);
            }
        }
        $data['average']['peroid1'] = ($peroid1Data1) ? $peroid1Data1 : array('-','-','-','-');
        $data['average']['peroid2'] = ($peroid1Data2) ? $peroid1Data2 : array('-','-','-','-');
        $data['average']['peroid3'] = ($peroid1Data3) ? $peroid1Data3 : array('-','-','-','-');
        $data['average']['peroid4'] = ($peroid1Data4) ? $peroid1Data4 : array('-','-','-','-');
        $data['average']['yearend'] = ($yearend) ? $yearend : '-';
        return $data;
    }
}
