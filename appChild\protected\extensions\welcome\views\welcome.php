<div class="journal-box">
    <h3>
        <span><?php echo Yii::t("navigations", 'Welcome'); ?></span>
        <?php if (!$weeknum && !$yid): ?>
            <em><?php echo Mims::formatDateTime($SNotesModel->weekInfo->monday_timestamp) ?> - <?php echo Mims::formatDateTime($SNotesModel->weekInfo->monday_timestamp + (3600 * 24 * 5) - 1) ?></em>
        <?php endif; ?>
    </h3>
    <?php if(Yii::app()->params['ownerConfig']['key'] == 'BJ_DS'):?>
    <ul class="pfolio-list-b" style="text-align:left;margin-bottom:15px;">
        <?php if($informationJ){ ?>
            <?php foreach($informationJ as $infromItem){ ?>
                <li class="semester" style="margin-left: 0;width:47%">
                    <a title="<?php echo (Yii::app()->language == 'zh_cn') ? $infromItem->title_cn : $infromItem->title_en ;?>" href="<?php echo (Yii::app()->language == 'zh_cn') ? Yii::app()->params['uploadBaseUrl']  .'information/' .$infromItem->file_cn : Yii::app()->params['uploadBaseUrl']  .'/information/' . $infromItem->file_en; ?>" target="_blank">
                        <em class="pdf-icon" style="float: left;"></em><p style="margin:14px 10px 12px -1px;float: left;height: 32px;width: 219px;overflow: hidden;"><?php echo (Yii::app()->language == 'zh_cn') ? $infromItem->title_cn : $infromItem->title_en ;?></p>
                    </a>
                </li>
            <?php } ?>
        <?php } ?>
    </ul>
    <?php endif; ?>
    <div class="key-reminder">
        <h5 class="title"></h5>
        <div class="reminder">
            <?php if ($SNotesModel->en_important):?>
            <script>
                document.write( normalText(<?php echo CJSON::encode($SNotesModel->en_important); ?>) );
            </script>
            <?php
            else:
                echo Yii::t("welcome",'No key reminders this week.');
            endif; ?>
        </div>
        <em class="pinner"></em>
        <em class="left-bottom"></em>
        <em class="right-top"></em>
    </div>

    <?php if ($recentj):?>
    <div class="recent-journals">
        <h5><?php echo addColon(Yii::t("global", "Recent Journals"));?></h5>
        <ul>
            <?php foreach ($recentj as $week):?>
            <li><?php echo CHtml::link(sprintf(Yii::t('global', 'Week %s (%s - %s)'), $week->weeknumber, Mims::formatDateTime($week->weekInfo->monday_timestamp), Mims::formatDateTime($week->weekInfo->monday_timestamp+7*24*3600-1)), array('//portfolio/portfolio/journal', 'startyear'=>$week->yInfo->startyear, 'classid'=>$week->classid, 'weeknum'=>$week->weeknumber))?></span></li>
            <?php endforeach;?>
            <li class="more"><?php echo CHtml::link(Yii::t("global","More"), $this->getController()->createUrl('//portfolio/portfolio/journal'));?></a></li>
        </ul>
    </div>
    <?php endif;?>
</div>
