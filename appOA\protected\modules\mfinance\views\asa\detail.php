<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span>
    </button>
    <h4 class="modal-title" id="exampleModalLabel"><?php echo Yii::t("asa", "detail"); ?></h4>
</div>
<?php $form = $this->beginWidget('CActiveForm', array(
    'id' => 'course-form',
    'enableAjaxValidation' => false,
    'action' => ($model->status == 4) ? $this->createUrl("anain", array('id' => $model->id)) : $this->createUrl("updateRefund", array('id' => $model->id)),
    'htmlOptions' => array('class' => 'J_ajaxForm form-horizontal', 'role' => 'form'),
)); ?>
<div class="modal-body">
    <div class="form-group">
        <div class="col-xs-3 text-center">
            <h3><?php echo ($childsName) ? $childsName->getChildName() : "" ?></h3>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 text-right"><?php echo Yii::t('asa', '#'); ?></label>

        <div class="col-xs-3">
            <?php echo $model->id; ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 text-right"><?php echo $form->labelEx($model, 'site_id'); ?></label>

        <div class="col-xs-3">
            <?php echo $branchName; ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 text-right"><?php echo Yii::t('asa', '班级'); ?></label>

        <div class="col-xs-3">
            <?php echo $childClass->title; ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 text-right"><?php echo $form->labelEx($model, 'program_id'); ?></label>

        <div class="col-xs-3">
            <?php echo $courseGroupName; ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 text-right"><?php echo $form->labelEx($model, 'course_id'); ?></label>

        <div class="col-xs-3">
            <?php echo $courseName; ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 text-right"><?php echo Yii::t('asa','缴费总额'); ?></label>
        <div class="col-xs-4">
            <?php echo $model->asainvoiceitem->actual_total . " 元"; ?> / 已退 <?php echo $oldRefundMun ?> <?php echo $model->status == 5 ? "（含本次退费）" : "（不含本次退费）" ;?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 text-right"><?php echo Yii::t('asa','退费详情'); ?></label>
        <div class="col-xs-3">
            共计<?php echo $model->refund_total_amount . " 元"; ?> （<?php echo $model->refund_class_count . " 节课"; ?>）
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 text-right"><?php echo $form->labelEx($model, 'dropout_date'); ?></label>

        <div class="col-xs-3">
            <?php echo date('Y-m-d', $model->dropout_date); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 text-right"><?php echo $form->labelEx($model, 'memo'); ?></label>

        <div class="col-xs-3">
            <?php echo $model->memo; ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 text-right"><?php echo $form->labelEx($model, 'refund_reason'); ?></label>

        <div class="col-xs-3">
            <?php echo $config['refundReason'][$model->refund_reason]['cn']; ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 text-right"><?php echo $form->labelEx($model, 'refund_type'); ?></label>

        <div class="col-xs-3">
            <?php echo $config['reFundType'][$model->refund_type]['cn']; ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 text-right"><?php echo $form->labelEx($model, 'payee_info'); ?></label>

        <div class="col-xs-8">
            <table class="table table-bordered">
                <tbody>
                <tr>
                    <td><?php echo Yii::t('asa', 'bank'); ?></td>
                    <td><?php echo Yii::t('asa', 'accountName'); ?></td>
                    <td><?php echo Yii::t('asa', 'accountNumber'); ?></td>
                </tr>
                <tr>
                    <?php $info = CJSON::decode($model->payee_info);
                    foreach ($info as $k => $v) {
                        echo "<td>" . $v . "</td>";
                    }
                    ?>
                </tr>
                </tbody>
            </table>
        </div>
    </div>
    <?php if ($exchangeData): ?>
        <div class="form-group">
        <label class="col-xs-3 text-right">换课历史</label>
        <div class="col-xs-8">
            <p>初始账单信息</p>
            <table class="table table-bordered">
                <tr>
                    <th>课程</th>
                    <th>金额</th>
                    <th>支付方式</th>
                    <th>支付ID</th>
                </tr>
                <tr>
                    <td><?php echo $firstInvoiceData['course_name']; ?></td>
                    <td><?php echo $firstInvoiceData['amount']; ?></td>
                    <td><?php echo $firstInvoiceData['type']; ?></td>
                    <td><?php echo $firstInvoiceData['id']; ?></td>
                </tr>
            </table>
            <p>换课历史</p>
            <table class="table table-bordered">
                <tr>
                    <th>课程</th>
                    <th>新课</th>
                    <th>旧课可用金额</th>
                    <th>新课所需金额</th>
                    <th>类型</th>
                    <th>支付ID</th>
                </tr>
                <?php foreach ($exchangeData as $data): ?>
                <tr>
                    <td><?php echo $data['old_course_name']; ?></td>
                    <td><?php echo $data['new_course_name']; ?></td>
                    <td><?php echo $data['old_course_amount']; ?></td>
                    <td><?php echo $data['new_course_amount']; ?></td>
                    <td><?php echo $data['difference'] > 0 ? '补缴' : '退费'; ?></td>
                    <td><?php echo $data['pay_id']; ?></td>
                </tr>
                <?php endforeach; ?>
            </table>
        </div>
        </div>
    <?php endif; ?>
    <div class="form-group">
        <label class="col-xs-3 text-right"><?php echo $form->labelEx($model, 'refund_files'); ?></label>

        <div class="col-xs-8">
            <?php if ($model->refund_files) { ?>
                <?php foreach ($fileNames as $k => $v) { ?>
                    <p class="mt"><a
                            href="<?php echo $this->createUrl('downloads', array('id' => $model->id, 'fileName' => $v)); ?>"
                            target="_blank" class="btn btn-info btn-xs"><span
                                class="glyphicon glyphicon-file"></span><?php echo $v; ?></a></p>
                <?php } ?>
            <?php } ?>
            <?php //echo $form->fileField($model, 'refund_files'); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 text-right"><?php echo $form->labelEx($model, 'refund_method'); ?></label>

        <div class="col-xs-3">
            <?php echo $config['refundMethod'][$model->refund_method]['cn']; ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 text-right"><?php echo $form->labelEx($model, 'updated'); ?></label>

        <div class="col-xs-3">
            <?php echo date('Y-m-d H:i:s', $model->updated); ?>
        </div>

        <label class="col-xs-3 text-right"><?php echo $form->labelEx($model, 'updated_by'); ?></label>

        <div class="col-xs-3">
            <?php echo ($userName[$model->updated_by]) ? $userName[$model->updated_by]->getName(): "" ; ?>
        </div>
    </div>

    <div class="form-group">
        <label class="col-xs-3 text-right"><?php echo $form->labelEx($model, 'updated_confirm'); ?></label>

        <div class="col-xs-3">
            <?php echo ($model->updated_confirm) ? date('Y-m-d H:i:s', $model->updated_confirm) :  ""; ?>
        </div>

        <label class="col-xs-3 text-right"><?php echo $form->labelEx($model, 'updated_confirm_by'); ?></label>

        <div class="col-xs-3">
            <?php echo ($userName[$model->updated_confirm_by]) ? $userName[$model->updated_confirm_by]->getName(): "" ; ?>
        </div>
    </div>

    <div class="form-group">
        <label class="col-xs-3 text-right"><?php echo $form->labelEx($model, 'updated_done'); ?></label>

        <div class="col-xs-3">
            <?php echo ($model->updated_done) ? date('Y-m-d H:i:s', $model->updated_done) :  ""; ?>
        </div>

        <label class="col-xs-3 text-right"><?php echo $form->labelEx($model, 'updated_done_by'); ?></label>

        <div class="col-xs-3">
            <?php echo ($userName[$model->updated_done_by]) ? $userName[$model->updated_done_by]->getName(): "" ; ?>
        </div>
    </div>

    <?php if (in_array($model->status, array(1, 3))) { ?>
        <div class="form-group">
            <label class="col-xs-3 text-right"><?php echo $form->labelEx($model, 'status'); ?></label>

            <div class="col-xs-8">
                <?php echo $form->dropDownList($model, 'status', $status, array('maxlength' => 255, 'class' => 'form-control', 'empty' => Yii::t('teaching', '请选择'))); ?>
            </div>
        </div>

        <?php if (in_array($model->status, array(3, 4))) { ?>
            <div class="form-group">
                <label class="col-xs-3 text-right"><?php echo $form->labelEx($model, 'receipt_no'); ?></label>

                <div class="col-xs-8">
                    <?php echo $form->textField($model, 'receipt_no', array('maxlength' => 255, 'class' => 'form-control')); ?>
                </div>
            </div>
        <?php } ?>
    <?php } elseif ($model->status == 5) { ?>
        <div class="form-group">
            <label class="col-xs-3 text-right"><?php echo $form->labelEx($model, 'status'); ?></label>

            <div class="col-xs-8">
                <?php echo $config['status'][$model->status]['cn']; ?>
            </div>
        </div>
        <div class="form-group">
            <label class="col-xs-3 text-right"><?php echo $form->labelEx($model, 'receipt_no'); ?></label>

            <div class="col-xs-8">
                <?php echo $model->receipt_no; ?>
            </div>
        </div>
    <?php } ?>
</div>
<div class="modal-footer">
    <?php if ($model->status != 5) { ?>
        <button type="submit"
                class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global', 'Submit'); ?></button>
    <?php } ?>
    <button type="button" class="btn btn-default"
            data-dismiss="modal"><?php echo Yii::t('global', 'Cancel'); ?></button>
</div>

<?php $this->endWidget(); ?>
