<?php
return array(
	'registration' => array(
		"sign" => 'registration',
		"cn" => "入园材料费",
		"en" => "Registration",
		"unique" => false, // 每学年是否只有唯一值
	),
	'deposit' => array(
		"sign" => 'deposit',
		"cn" => "预缴学费",
		"en" => "Tuition Deposit",
		"unique" => false,
	),
	'tuition' => array(
		"sign" => 'tuition',
		"cn" => "学费",
		"en" => "Tuition",
	),
	'bus' => array(
		"sign" => 'bus',
		"cn" => "校车费",
		"en" => "School Bus",
		"unique" => false,		
	),
	'lunch' => array(
		"sign" => 'lunch',
		"cn" => "餐费",
		"en" => "Lunch",
		"unique" => true,
	
	),
	'afterschool' => array(
		"sign" => 'afterschool',
		"cn" => "课外活动",
		"en" => "After School",
		"unique" => false,
	
	),
	'carousel' => array(
		"sign" => 'carousel',
		"cn" => "亲子班",
		"en" => "CaRousel",
		"unique" => false,
	
	),
	'library_card' => array(
		"sign" => 'library_card',
		"cn" => "借书卡",
		"en" => "Library Card",
		"unique" => false,
	
	),
	'library_card_cost' => array(
		"sign" => 'library_card_cost',
		"cn" => "借书卡工本费",
		"en" => "Library Card Cost",
		"unique" => false,
	
	),
	'latepayment' => array(
		"sign" => 'latepayment',
		"cn" => "滞纳金",
		"en" => "Late Payment",
		"unique" => false,
	
	),
	'account_balance_disposal' => array(
		"sign" => 'account_balance_disposal',
		"cn" => "个人账户余额处置",
		"en" => "Account Balance Disposal",
		"unique" => false,
	),
	'margin' => array(
		"sign" => 'margin',
		"cn" => "保证金",
		"en" => "Margin",
		"unique" => false,
	),
	'childcare' => array(
		"sign" => 'childcare',
		"cn" => "保育费",
		"en" => "保育费",
		"unique" => false,
	),
	'transfer' => array(
		"sign" => 'transfer',
		"cn" => "转移",
		"en" => "Transfer",
		"unique" => false,
	),
	'cash' => array(
		"sign" => 'cash',
		"cn" => "提现",
		"en" => "Cash",
		"unique" => false,
	),
);