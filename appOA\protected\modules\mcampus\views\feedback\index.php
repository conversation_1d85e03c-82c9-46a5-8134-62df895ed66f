<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site','Campus Operations'), array('//mcampus/default/index'))?></li>
        <li><?php echo CHtml::link(Yii::t('site','Routines'), array('//mcampus/default/index'))?></li>
        <li class="active"><?php echo Yii::t('site','Feedback');?></li>
    </ol>
    <div class="row">
        <div class="col-md-2">
            <ul class="nav nav-pills nav-stacked text-right background-gray">
                <li <?php if($type==''):?>class="active"<?php endif;?>><?php echo CHtml::link(Yii::t('campus', 'New'), array('index'));?></li>
                <li <?php if($type=='replied'):?>class="active"<?php endif;?>><?php echo CHtml::link(Yii::t('campus', 'Replied'), array('index', 'type'=>'replied'));?></li>
            </ul>
        </div>
        <div class="col-md-10">
            <?php
            if($items):
            foreach($items as $item):?>
            <div class="panel panel-default" style="position: relative;margin-top: 50px;">
                <div style="position: absolute;top: -30px;left: 73px">
                    <?php echo CHtml::image(CommonUtils::childPhotoUrl($item->childInfo->photo, 'small'),
                    '',
                    array('class'=>'img-circle', 'title'=>$item->childInfo->getChildName(), 'style'=>'width:60px;'));?>
                </div>
                <div style="position: absolute;right: 5px; top: 5px;">
                    <button class="btn btn-xs" style="margin:6px" type="button"><?php echo $item->classInfo->title?></button>
                    <a class="btn btn-default btn-xs" style="margin:6px" role="button" target="_blank" href="<?php echo $this->createUrl('/child/index/index', array('childid'=>$item->com_child_id))?>">
                        <?php echo $item->childInfo->getChildName()?>
                    </a>
                    <a class="btn btn-default btn-xs" role="button" style="margin:6px" target="_blank" href="<?php echo CommonUtils::genCCUrlHome($item->com_child_id, $item->yInfo->startyear.'-'.$item->com_class_id.'-'.$item->com_week_num)?>">
                        Week: <?php echo $item->com_week_num?>
                    </a>
                    <button class="btn btn-primary btn-xs" type="button" style="margin:6px" onclick="beforeForward(<?php echo $item->id?>, <?php echo $item->com_class_id?>)"><?php echo Yii::t('campus', 'Forward to teachers')?></button>
                    <?php if($type != 'replied'):?>
                    <button class="btn btn-success btn-xs" type="button" style="margin:6px" onclick="asReplied(<?php echo $item->id?>)"><?php echo Yii::t('campus', 'Tag as replied')?></button>
                    <?php endif;?>
                </div>
                <div class="panel-body" style="margin-top: 60px;" id="feedback-<?php echo $item->id?>">
                    <div class="clearfix" id="com-<?php echo $item->id?>">
                        <div class="pull-left mr10">
                            <?php echo CHtml::image($item->userWithProfile->getPhotoSubUrlforOA(),
                                '',
                                array('title'=>$item->userWithProfile->getName(), 'style'=>'width:49px;'));?>
                        </div>
                        <div class="alert alert-success pull-left" style="max-width: 85%;word-wrap:break-word">
                            <?php echo CHtml::encode($item->com_content)?> <em class="text-muted" title="<?php echo date('Y-m-d H:i', $item->com_created_time)?>">- <?php echo CommonUtils::time_elapsed_string($item->com_created_time)?></em>
                        </div>
                    </div>
                    <?php
                    foreach($item->replies as $reply):
                        $className1 = ($reply->com_user_type == 0) ? 'pull-left mr10' : 'pull-right ml10';
                        $className2 = ($reply->com_user_type == 0) ? 'alert-success pull-left' : 'alert-info pull-right';
                    ?>
                        <div class="clearfix" id="com-<?php echo $reply->id?>">
                            <div class="<?php echo $className1?>">
                                <?php echo CHtml::image($reply->userWithProfile->getPhotoSubUrlforOA(),
                                    '',
                                    array('title'=>$reply->userWithProfile->getName(), 'style'=>'width:49px;'));?>
                            </div>
                            <div class="alert <?php echo $className2;?>" style="max-width: 85%;word-wrap:break-word">
                                <?php echo CHtml::encode($reply->com_content);?> <em class="text-muted" title="<?php echo date('Y-m-d H:i', $reply->com_created_time)?>">- <?php echo CommonUtils::time_elapsed_string($reply->com_created_time)?></em>
                                <?php if($reply->com_user_type == 1 && $reply->com_uid == Yii::app()->user->id):?>
                                    <a href="javascript:;" onclick="delComment(<?php echo $reply->id;?>)"><?php echo Yii::t('global', 'Delete')?></a>
                                <?php endif;?>
                            </div>
                        </div>
                    <?php endforeach;?>
                </div>
                <hr>
                <form action="<?php echo $this->createUrl('saveComment', array('id'=>$item->id));?>" method="post" class="J_ajaxForm">
                <div class="input-group mb15 ml10 mr10">
                    <textarea class="form-control" name="content" id="content-<?php echo $item->id?>" style="height: 50px;"></textarea>
                    <span class="input-group-btn">
                        <button class="btn btn-success J_ajax_submit_btn" type="button" style="height: 50px;">
                            <?php echo Yii::t('global', 'Submit')?>
                        </button>
                    </span>
                </div>
                </form>
            </div>
            <?php
            endforeach;
            else:
            ?>
            <div class="alert alert-info" role="alert"><?php echo $type == 'replied' ? Yii::t('campus', 'No replied feedback') : Yii::t('campus', 'No new feedback')?></div>
            <?php endif;?>
            <div class="text-right">
                <?php
                $this->widget('BsCLinkPager',array(
                        'header'=>'',
                        'pages' => $pager,
                    )
                );
                ?>
            </div>
        </div>
    </div>
</div>
<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>

<div class="modal fade" id="to-teachers">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title"><?php echo Yii::t('campus', 'Forward to teachers');?></h4>
            </div>
            <form action="<?php echo $this->createUrl('forward')?>" method="post" class="J_ajaxForm">
                <div class="modal-body">
                    <p id="forward-ed"></p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('global', 'Cancel');?></button>
                    <button type="button" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global', 'Send');?></button>
                </div>
            </form>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->

<script>
    function cbSaveComment(data)
    {
        $('#content-'+data.id).val('');
        var html = '<div class="clearfix" id="com-'+data.cid+'">';
        html += '<div class="pull-right ml10">';
        html += '<img src="'+data.photo+'" title="'+data.name+'" style="width:49px;">';
        html += '</div>';
        html += '<div class="alert alert-info pull-right" style="max-width: 85%;">';
        html += data.content+' <em class="text-muted" title="'+data.timestamp+'">- '+data.timestampshow+'</em> ';
        html += '<a href="javsscript:;" onclick="delComment('+data.cid+')"><?php echo Yii::t('global', 'Delete')?></a>';
        $('#feedback-'+data.id).append(html);
    }

    function delComment(id)
    {
        $.post('<?php echo $this->createUrl('delComment');?>', {id: id}, function(data){
            if(data.state == 'success'){
                $('#com-'+id).remove();
            }
            else{
                resultTip({msg: data.message, error: 1});
            }
        }, 'json');
    }

    function asReplied(id)
    {
        $.post('<?php echo $this->createUrl('asReplied');?>', {id: id}, function(data){
            if(data.state == 'success'){
                $('#feedback-'+id).parent().remove();
            }
        }, 'json');
    }

    function beforeForward(id, classid)
    {
        $.getJSON('<?php echo $this->createUrl('beforeForward');?>', {id: id, classid: classid}, function(data){
            var html = '<?php echo Yii::t('campus', 'Forward log: ');?>';
            for(var forward in data.forwards){
                html += '<span>'+data.forwards[forward]+'</span> ';
            }
            html += '<hr><div>';
            for(var teacher in data.teachers){
                html += '<p class="checkbox"><label><input value="'+teacher+'" type="checkbox" name="uid[]"> ';
                html += data.teachers[teacher]+'</label></p>';
            }
            html += '</div>';
            html += '<input type="hidden" name="id" value="'+id+'">';
            $('#to-teachers #forward-ed').html(html);
            $('#to-teachers').modal();
        });
    }

    function cbSend()
    {
        window.setTimeout(function(){
            $('#to-teachers').modal('hide');
        }, 1000);
    }
</script>