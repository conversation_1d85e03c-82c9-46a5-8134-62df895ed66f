<div class="form">
<?php $form=$this->beginWidget('CActiveForm', array(
	'id'=>'child-form',
	'enableClientValidation'=>true,
	'clientOptions'=>array(
		'validateOnSubmit'=>true,
	),
)); ?>
	
	<div class="table_full">
		<table width="100%">
			<colgroup>
				<col width="200">
				<col width="600">
			</colgroup>
			<tbody>
				<tr>
					<th>
						<?php echo CHtml::activeLabelEx($model, 'ucontact') ?>
					</th>
					<td>
						<p class="desc"><?php echo Yii::t("userinfo", 'Who should we contact for emergency situations?') ?></p>
						<?php echo CHtml::activeTextArea($model, 'ucontact', array("rows" => 4, "cols" => 60)); ?>
					</td>
					<td></td>
				</tr>

				<tr>
					<th>
						<?php echo CHtml::activeLabelEx($model, 'upickup') ?>
					</th>
					<td>
						<p class="desc"><?php echo Yii::t("userinfo", 'Who do you authorize to pick up your child?') ?></p>
						<?php echo CHtml::activeTextArea($model, 'upickup', array("rows" => 4, "cols" => 60)); ?>
					</td>
					<td></td>
				</tr>

				<tr>
					<th>
						<?php echo CHtml::activeLabelEx($model, 'allergy') ?>
					</th>
					<td>
						<p class="desc"><?php echo Yii::t("userinfo", 'Allergy and other special needs?') ?></p>
						<?php echo CHtml::activeTextArea($model, 'allergy', array("rows" => 4, "cols" => 60)); ?>
					</td>
					<td></td>
				</tr>

				<tr>
					<th>
						<?php echo CHtml::activeLabelEx($model, 'hospital') ?>
					</th>
					<td>
						<p class="desc"><?php echo Yii::t("userinfo", 'Should a situation arise when emergency medical attention is required and we are unable to contact any of the above, please indicate,on the space provided below, the hospital we will take your child to. If not filled in, the school will select a hospital on its own. The school will not be responsible for any costs or fees incurred.') ?></p>
						<?php echo CHtml::activeTextArea($model, 'hospital', array("rows" => 1, "cols" => 60)); ?>
					</td>
					<td></td>
				</tr>

				<tr>
					<th>
						<?php echo CHtml::activeLabelEx($model, 'agree_photo_open') ?>
					</th>
					<td>
						<p class="desc"><?php echo Yii::t("userinfo", 'Parents give permission to the school, to use pictures taken of the child in school, for marketing purposes.') ?></p>
						<?php echo CHtml::activeCheckBox($model, 'agree_photo_open'); ?>
						<?php echo CHtml::activeLabelEx($model, 'agree_photo_open') ?>
					</td>
					<td></td>
				</tr>

				<tr>
					<th>
						<?php echo CHtml::activeLabelEx($model, 'sign_allergy') ?>
					</th>
					<td>
						<p class="desc"><?php echo Yii::t("userinfo", 'Parents have signed the agreement to choose Alternative Lunch Menu the child.') ?></p>
						<?php echo CHtml::activeCheckBox($model, 'sign_allergy'); ?>
						<?php echo CHtml::activeLabelEx($model, 'sign_allergy') ?>
					</td>
					<td></td>
				</tr>
				<tr>
					<th>
						<?php echo CHtml::activeLabelEx($model, 'vaccine') ?>
					</th>
					<td>
                        <p class="desc"><?php echo Yii::t("userinfo", '已完成接种疫苗')?></p>
						<?php echo CHtml::activeCheckBox($model, 'vaccine'); ?>
						<?php echo CHtml::activeLabelEx($model, 'vaccine') ?>
					</td>
					<td></td>
				</tr>
				<tr>
					<th>
						<?php echo CHtml::activeLabelEx($model, 'physical') ?>
					</th>
					<td>
                        <p class="desc"><?php echo Yii::t("userinfo", '已完成体检')?></p>
						<?php echo CHtml::activeCheckBox($model, 'physical'); ?>
						<?php echo CHtml::activeLabelEx($model, 'physical') ?>
					</td>
					<td></td>
				</tr>
                <tr>
                    <th>
                        <?php echo CHtml::activeLabelEx($model, 'residence') ?>
                    </th>
                    <td>
                        <p class="desc"></p>
                        <?php echo CHtml::activeTextField($model, 'residence', array('class'=>'input length_4')); ?>
                    </td>
                    <td></td>
                </tr>

			</tbody>
		</table>
	</div>
		<div class="btn_wrap_pd">
			<?php
				echo CHtml::submitButton(Yii::t("global", "Save"), array("class"=>"btn btn_submit"));
                foreach(Yii::app()->user->getFlashes() as $key => $message) {
                    echo '<div class="tips_' . $key . '">' . $message . "</div>\n";
                }
			?>
		</div>


	
	
<?php $this->endWidget(); ?>
</div><!-- form -->