<?php 
$crit = new CDbCriteria;
$crit->compare("schoolid", $this->branchId);
$crit->addCondition('status>='.ChildProfileBasic::STATS_GRADUATED);
$crit->group ='updated_timestamp desc,childid desc';
$dataProvider = new CActiveDataProvider('ChildProfileBasic', array(
    'criteria' => $crit,
    'pagination' => array(
        'pageSize' => 20,
    ),
));
?>
<div class="col-md-10">
    <div class="row mb15">
        <div class="col-md-12">
            <h1 class="flex align-items font24 fontBold mt20"><?php echo Yii::t('site','Inactive Students');?> <span class="label label-default" id="total-number"></span></h1>
        </div>
    </div>
    <div class="row">
        <div class="col-md-12">
            <?php 
                $this->widget('ext.ivyCGridView.BsCGridView', array(
                    'id'=>'child-list',
                    'dataProvider'=>$dataProvider,
                    'template'=>"{items}{pager}",
                    'colgroups'=>array(
                        array(
                            "colwidth"=>array(80,150,90, 120, 120,120),
                        )
                    ),
                    'columns'=>array(
                        'childid'=>array(
                            'name'=>'childid',
                            'header'=>Yii::t("labels","ID"),
                        ),
                        'name'=>array(
                            'type'=>'raw',
                            'header' => Yii::t('child', 'Name'),
                            'value'=>'CHtml::link($data->getChildName(), array("//child/index/index","childid"=>$data->childid), array("target"=>"_blank"))',
                        ),
                        'gender'=>array(
                            'name'=>'gender',
                            'value'=>'OA::renderChildGender($data->gender)',
                        ),
                        'birthday'=>array(
                            'name'=>'birthday_search',
                        ),
                        'credit'=>array(
                            'name'=>'credit',
                        ),
                        'regdate'=>array(
                            'name'=>'created_timestamp',
                            'header'=>Yii::t('labels', 'Created Time'),
                            'value'=>'OA::formatDateTime($data->created_timestamp)',
                        ),
                        'savedate' => array(
                            'name' => 'updated_timestamp',
                            'header' => Yii::t('global', 'Update Time'),
                            'value' => 'OA::formatDateTime($data->updated_timestamp)',
                        ),
                    )
                ));
            ?>
        </div>
    </div>
</div>
