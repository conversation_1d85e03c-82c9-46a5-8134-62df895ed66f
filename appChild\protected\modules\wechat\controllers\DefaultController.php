<?php

class DefaultController extends WeChatBasedController
{
	private $iniEmailProduction = true;

	const BOUND_USER_ALREADY = 10001; //已经绑定过
	const BOUND_USER_REPLACED = 10002;//之前绑定过账户，现在绑定到其他账户
	const BOUND_USER_DONE = 10003; //绑定成功
	const BOUND_USER_MAXEXCEEDED = 10004; //绑定达最大数，需手动输入帐号密码绑定
	const BOUND_USER_ERROR = 10099; //绑定达最大数，需手动输入帐号密码绑定

	const MAX_NUMBER_BOUND_PER_ACCOUNT = 10;

	private $sceneId = 99995; // 大于此ID的为定制的场景二维码，小于的为绑定孩子二维码

	public function actionIndex()
	{
		//$this->wechatObj->initialValidate();

		if($this->userMsg->MsgType == 'event' && $this->userMsg->Event == 'subscribe'){
			if($this->userMsg->EventKey){
				$result = $this->processQRScene($this->userMsg->FromUserName, $this->userMsg->EventKey);
				$this->processCustomQrCodeResult($result);
			}
            if ($this->userMsg->ToUserName == 'gh_98e9efeac823') {
                $this->text("Thanks for following me. Please click on the \"Program\" button below to enter the registration platform.\r\n感谢关注萌犸象，请点击下方菜单的“课程”查看并注册课程。");
            }
            else {
                $this->text('你好，我是机器人小艾，欢迎关注我，你可以使用菜单功能进行操作哦');
            }

			if (is_null($this->wechatUser)) {
                $this->wechatUser = new WechatUser();
			    $this->wechatUser->openid = $this->userMsg->FromUserName;
			    $account = 'ivy';
			    switch ($this->userMsg->ToUserName) {
                    case 'gh_f599e1155020':
                        $account = 'ds';
                        break;
                    case 'gh_98e9efeac823':
                        $account = 'mmx';
                        break;
                }
			    $this->wechatUser->account = $account;
                $this->wechatUser->updated = time();
                $this->wechatUser->save(false);
            }
			Yii::app()->end();
		}

		if($this->userMsg->MsgType == 'event' && $this->userMsg->Event == 'SCAN'){
			if($this->userMsg->EventKey){
				$result = $this->rebind($this->userMsg->FromUserName, $this->userMsg->EventKey);
				$this->processCustomQrCodeResult($result);
			}
		}

		if(strtoupper($this->userMsg->Event) == 'CLICK'){
			switch($this->userMsg->EventKey){
				case 'MINE_CONTACT_CN':
					$text = "请直接发送以 LXXY 开头的内容\r\n";
					$text .= "例如：LXXY请问下次开放日是哪天？";
					$this->text($text);
					break;
				case 'MINE_CONTACT_EN':
					$text = "Please start your content with LXXY and send.\r\n";
					$text .= "For example, LXXYWhen is the school open day?";
					$this->text($text);
					break;
                case 'USE_ENGLISH':
                    if ($this->wechatUser->updateTag('en')) {
                        $this->text('You have successfully set the language to English. WeChat will update the local cache in 5-10 minutes before the change takes effect.');
                    }
                    else {
                        $this->text('网络错误，请稍后再试。Networking error, please try again later.');
                    }
                    break;
                case 'USE_CHINESE':
                    if ($this->wechatUser->updateTag('cn')) {
                        $this->text('您已成功设置语言为中文，微信将在5-10分钟内更新本地缓存，稍后即将生效。');
                    }
                    else {
                        $this->text('网络错误，请稍后再试。Networking error, please try again later.');
                    }
                    break;
                case 'USE_ENGLISH_2023':
                    $accessToken = CommonUtils::getAccessToken('ds');
                    $this->wechatUser->batchUnTag(array($this->wechatUser->openid), 133, $accessToken);
                    if ($this->wechatUser->batchTag(array($this->wechatUser->openid), 132, $accessToken)) {
                        $this->text('You have successfully set the language to English. WeChat will update the local cache in 5-10 minutes before the change takes effect.');
                    }
                    else {
                        $this->text('网络错误，请稍后再试。Networking error, please try again later.');
                    }
                    Yii::app()->end();
                    break;
                case 'USE_CHINESE_2023':
                    $accessToken = CommonUtils::getAccessToken('ds');
                    $this->wechatUser->batchUnTag(array($this->wechatUser->openid), 132, $accessToken);
                    if ($this->wechatUser->batchTag(array($this->wechatUser->openid), 133, $accessToken)) {
                        $this->text('您已成功设置语言为中文，微信将在5-10分钟内更新本地缓存，稍后即将生效。');
                    }
                    else {
                        $this->text('网络错误，请稍后再试。Networking error, please try again later.');
                    }
                    Yii::app()->end();
                    break;
			}
		}

		if($this->userMsg->MsgType == 'voice'){
			$content = $this->userMsg->Recognition;
		}

		if($this->userMsg->MsgType == 'text'){
			$content = trim($this->userMsg->Content);
		}

		if(!empty($content)) {
			$command = strtolower(substr($content, 0, 4));
            $content = strtolower($content);
			if ($command == 'lxxy' && strlen($content)>4) {
				$mailcontent = substr($content, 4);
				$child = array_shift($this->myChildObjs);
				$campus = Branch::model()->with('info')->findByPk($child->schoolid);

				$mailer = Yii::createComponent('common.extensions.mailer.EMailer');
				$mailer->Subject = Mims::unIvy() ? sprintf(Yii::app()->params['ownerConfig']['name_cn'].'【微信助手 - %s】孩子信息：%s（联系校园）' , $campus->abb, $child->getChildName()) : sprintf('【艾毅在线微信助手 - %s】孩子信息：%s（联系校园）' , $campus->abb, $child->getChildName());

				$pEmails = $this->getParentsEmail($child);

				$mailer->AddAddress($campus->info->support_email);
				$mailer->AddReplyTo($campus->info->support_email);
				foreach($pEmails as $email){
					$mailer->AddCC($email);
				}

				$mailer->iniMail($this->iniEmailProduction); // 此行代码要放到AddAddress, AddCC方法下面
				$mailer->getView('wechatContact', array('userMsg'=>$mailcontent), 'wechat');
				if ($mailer->Send()) {
					$this->text("您的信息已经发送至校园支持团队，内容如下：\r\n" . $mailcontent );
				}else{
					$this->text("系统错误，请稍后再试\r\n");
				}
				Yii::app()->end();
			}
			elseif ($content == 'switch to english' || $content == 'use english') {
			    if ($this->wechatUser->updateTag('en')) {
                    $this->text('Language has been set to English, please note it takes effect in 5-10 minutes.');
                }
			    else {
                    $this->text('网络错误，请稍后再试。Networking error, please try again later.');
                }
                Yii::app()->end();
            }
            elseif ($content == '使用中文') {
                if ($this->wechatUser->updateTag('cn')) {
                    $this->text('您已成功设置语言为中文，将在5-10分钟内生效。');
                }
			    else {
                    $this->text('网络错误，请稍后再试。Networking error, please try again later.');
                }
                Yii::app()->end();
            }
            elseif ($content == '使用中文2023') {
                $accessToken = CommonUtils::getAccessToken('ds');
                $this->wechatUser->batchUnTag(array($this->wechatUser->openid), 132, $accessToken);
                if ($this->wechatUser->batchTag(array($this->wechatUser->openid), 133, $accessToken)) {
                    $this->text('您已成功设置语言为中文，将在5-10分钟内生效。');
                }
                else {
                    $this->text('网络错误，请稍后再试。Networking error, please try again later.');
                }
                Yii::app()->end();
            }
            elseif ($content == 'use english 2023') {
                $accessToken = CommonUtils::getAccessToken('ds');
                $this->wechatUser->batchUnTag(array($this->wechatUser->openid), 133, $accessToken);
                if ($this->wechatUser->batchTag(array($this->wechatUser->openid), 132, $accessToken)) {
                    $this->text('Language has been set to English, please note it takes effect in 5-10 minutes.');
                }
                else {
                    $this->text('网络错误，请稍后再试。Networking error, please try again later.');
                }
                Yii::app()->end();
            }
            elseif ($content == '星爸星妈学院') {
                $this->image('N-OUc62naAJUJdMckdUn7R73tRo27I-x5-HNjzr2u6T99N7vw40LIj5RIFZJfN3G');
                Yii::app()->end();
            }
            elseif ($content == 'dpa') {
                $this->image('N-OUc62naAJUJdMckdUn7eM1x0PUqDQRO3iEyMPLrs60X0rYH2fDNu9kh2tREcVB');
                Yii::app()->end();
            }
			elseif ($content == '凤凰体育') {
			    $this->image('N-OUc62naAJUJdMckdUn7dM1-1VKLOZpSdo1jo1-3mMc113W_RTLZsJe21kNfauF');
			    Yii::app()->end();
            }
            elseif (in_array($content, array('phoenix athletic', 'athletic'))) {
                $this->image('N-OUc62naAJUJdMckdUn7dM1-1VKLOZpSdo1jo1-3mMc113W_RTLZsJe21kNfauF');
                Yii::app()->end();
            }
			else {
				$chats = CommonUtils::LoadConfig('CfgWxChat');

				foreach ($chats as $key => $value) {
					if (false !== stripos($content, $key)) {
						$this->text($value);
						Yii::app()->end();
					}
				}
			}
		}
		$this->text('抱歉我没懂你的意思！你可以使用菜单功能进行操作哦');
	}

	public function processCustomQrCodeResult($data){
		if($data['code']){
			$text = "";
			$childInfo = "";
			switch($data['code']){
				case self::BOUND_USER_MAXEXCEEDED:
					$text = "★ 此帐号绑定的微信已达上限 \r\n";
					$text.= "为保证账户安全，请选择“绑定幼儿园系统帐号”菜单按提示手动输入帐号信息进行绑定";
					break;
				case self::BOUND_USER_ALREADY:
					$text = "★ 已经绑定过此账户 \r\n";
					$text .= "孩子信息：\r\n";
					break;
				case self::BOUND_USER_REPLACED:
					$text = "★ 已经绑定新账户，先前绑定的账户失效 \r\n";
					$text .= "孩子信息：\r\n";
					break;
				case self::BOUND_USER_DONE:
					$text = "★ 账户绑定成功 \r\n";
					$text .= "孩子信息：\r\n";
					break;
				default:
					$text = "★ 账户绑定失败 \r\n";
					$text .= isset($data['message'])?$data['message']:$text;
					break;
			}
			if(isset($data['childData']) && isset($data['childData']['data'])){
				foreach($data['childData']['data'] as $_child){
					$text .= sprintf("%s %s %s", $_child['childName'], $_child['campus'], ($_child['active'])?'就读中':'非就读中') . "\r\n";
				}
				if(!$data['childData']['hasActive']){
					$text .= '★ 抱歉微信应用暂时只对在读中的孩子提供服务，请使用电脑端访问非在读孩子信息。';
				}
			}
			$this->text($text);
		}
	}

	/**
	 * 扫描私人定制二维码的处理；关注过微信帐号
	 * @param	string	$openId		Description
	 * @param	string	$sceneId	Description
	 * @return	Object				Description
	 */
	public function rebind($openId, $sceneId){
		if ($sceneId > $this->sceneId) {
			$this->scene($sceneId);
		}
		$exist = WechatUser::model()->findByPk($openId);
		$scene = WechatQRScene::model()->findByPk($sceneId);
		$return = array('code'=>'');

		// 如果是后台生成的绑定码，需要判断是否为 dev 成员
		if ($scene->flag == 1) {
			$openIds = $this->getDevOpenIds($scene->account);
			if (!in_array($openId, $openIds)) {
				return array('code' => self::BOUND_USER_ERROR);
			}
		}

		if( is_null($exist) && $scene ) {
			$exist = new WechatUser;
			$exist->openid = $openId;
			$exist->userid = $scene->userid;
			$exist->valid = 1;
			$exist->updated = time();
			if (! $exist->save() ){
				$return['code'] = self::BOUND_USER_ERROR;
				$return['message'] = print_r($exist->getErrors(), true);
			}
		}

		if($exist && $scene){

			$boundTotal = WechatUser::model()->countByAttributes(array('valid'=>1, 'userid'=>$scene->userid));
			if($boundTotal >= self::MAX_NUMBER_BOUND_PER_ACCOUNT){
				$return['code'] = self::BOUND_USER_MAXEXCEEDED;
				return $return;
			}

			$scene->scanned += 1;
			$scene->save();
			if($exist->userid && $exist->userid == $scene->userid && $exist->valid){
				$return['code'] = self::BOUND_USER_ALREADY;
				$return['childData'] = self::getChildInfo($exist->userid);
				return $return;
			}elseif($exist->userid && $exist->valid){
				$exist->userid = $scene->userid;
				$exist->updated = time();
				if( $exist->save() ){
					$return['childData'] = self::getChildInfo($exist->userid);
					$return['code'] = self::BOUND_USER_REPLACED;
					return $return;
				}else{
					$return['code'] = self::BOUND_USER_ERROR;
					$return['message'] = print_r($exist->getErrors(), true);
					return $return;
				}
			}else{
				$exist->userid = $scene->userid;
				$exist->valid = 1;
				$exist->updated = time();
				if( $exist->save() ){
					$return['code'] = self::BOUND_USER_DONE;
					$return['childData'] = self::getChildInfo($exist->userid);
					return $return;
				}else{
					$return['code'] = self::BOUND_USER_ERROR;
					$return['message'] = print_r($exist->getErrors(), true);
					return $return;
				}
			}
		}
	}

	/**
	 * 扫描私人定制二维码的处理；未关注过微信帐号
	 * @param	string	$openId		Description
	 * @param	string	$sceneId	Description
	 * @return	Object				Description
	 */
	public function processQRScene($openId, $sceneId){
		$pattern = 'qrscene_';
		$return = array('code'=>'');

		if(strpos($sceneId,$pattern)!== false){
			$sceneId = intval(str_replace($pattern, '', $sceneId));
			if($sceneId){
				if ($sceneId > $this->sceneId) {
					$this->scene($sceneId);
				}
				$scene = WechatQRScene::model()->findByPk($sceneId);
				if($scene){
					$boundTotal = WechatUser::model()->countByAttributes(array('valid'=>1, 'userid'=>$scene->userid));
					if($boundTotal >= self::MAX_NUMBER_BOUND_PER_ACCOUNT){
						$return['code'] = self::BOUND_USER_MAXEXCEEDED;
						return $return;
					}
					// 如果是后台生成的绑定码，需要判断是否为 dev 成员
					if ($scene->flag == 1) {
						$openIds = $this->getDevOpenIds($scene->account);
						if (!in_array($openId, $openIds)) {
							return array('code' => self::BOUND_USER_ERROR);
						}
					}
					$wechatUser = WechatUser::model()->findByPk($openId);
					if(is_null($wechatUser)){
						$wechatUser = new WechatUser;
						$wechatUser->openid = $openId;
					}
					$wechatUser->userid = $scene->userid;
					$wechatUser->valid = 1;
					$wechatUser->updated = time();
					if(	$wechatUser->save() ){

						$return['code'] = self::BOUND_USER_DONE;
						$return['childData'] = self::getChildInfo($wechatUser->userid);

						return $return;
					}else{
						$return['code'] = self::BOUND_USER_ERROR;
						$return['message'] = print_r($wechatUser->getErrors(), true);
						return $return;
					}
				}
			}
		}
		return false;
	}

	public function getDevOpenIds($account)
	{
		Yii::app()->user->setId($this->wechatUser->userid);
		$res = CommonUtils::requestDsOnline('devOpenIds/' . $account);
		$openIds = array();
		if (isset($res['code']) && $res['code'] == 0) {
			$openIds = is_array($res['data']) ? $res['data'] : array() ;
		}
		return $openIds;

	}

	static function getChildInfo($userId){
		$parent = IvyParent::model()->findByPk($userId);
		$myChildObjs = null;
		$activeChildIds = null;
		$branchList = null;
		$hasActive = false;

		$result = array('hasActive'=>$hasActive);

		if (!empty($parent) && count(@unserialize($parent->childs))) {
			$cids = @unserialize($parent->childs);
			$myChildObjs = ChildProfileBasic::model()->findAllByPk($cids, array("index" => "childid","order"=>"childid DESC"));

			$branchList = Branch::model()->getBranchList(null, true);
			foreach($myChildObjs as $child){
				$result['data'][] = array(
					'childName' => $child->getChildName(),
					'active' => ($child->status < 100) ? true : false,
					'campus' => empty($child->schoolid)? '': $branchList[$child->schoolid]['abb']
				);
				if($child->status < 100 && !$hasActive) $result['hasActive']=true;
			}
		}
		return $result;
	}

	private function getParentsEmail($child){
		$emails = array();
		$pids = array();
		if($child->fid) $pids[] = $child->fid;
		if($child->mid) $pids[] = $child->mid;
		$parents = User::model()->findAllByPk($pids, '`level`>:level', array(':level'=>0));
		if(!empty($parents)){
			foreach($parents as $parent){
				$emails[] = $parent->email;
			}
		}
		return $emails;
	}

	public function scene($id = 0)
	{
		switch ($id) {
			case 99999: //Ivy 家长问卷
				$url = 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=wx903fba9d4709cf10&redirect_uri=http://www.ivyonline.cn/wechat/user/survey/id/24&response_type=code&scope=snsapi_base&state=ivy&connect_redirect=1#wechat_redirect';
				$picUrl = 'http://mega.ivymik.cn/2016-2017_survey.jpg';
				$desc = '艾毅教育集团总部新一年度的家长问卷调查开始了，希望您的参与能帮助我们不断提升校园的质量，衷心感谢您的支持！';
				$this->link('2016-2017 学年家长调查问卷 Parent Survey', $desc, $picUrl, $url);
				break;
			case 99998: //DS 家长问卷
				$url = 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=wxb1a42b81111e29f3&redirect_uri=http://www.ivyonline.cn/wechat/user/survey/id/25&response_type=code&scope=snsapi_base&state=ds&connect_redirect=1#wechat_redirect';
				$picUrl = 'http://mega.ivymik.cn/2016-2017_survey.jpg';
				$desc = '艾毅教育集团总部新一年度的家长问卷调查开始了，希望您的参与能帮助我们不断提升校园的质量，衷心感谢您的支持！';
				$this->link('2016-2017 学年家长调查问卷 Parent Survey', $desc, $picUrl, $url);
				break;
			case 99997: //新生注册
				$url = 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=wxb1a42b81111e29f3&redirect_uri=http://www.ivyonline.cn/wechat/regExtraInfo/step1%3fstate=ds&response_type=code&scope=snsapi_base&state=ds&connect_redirect=1#wechat_redirect';
				$url = 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=wxb1a42b81111e29f3&redirect_uri=http://www.ivyonline.cn/wechat/regExtraInfo/step1/state/ds&response_type=code&scope=snsapi_userinfo&state=ds&connect_redirect=1#wechat_redirect';
				$picUrl = 'http://mega.ivymik.cn/2016-17_reg.jpeg';
				$this->link('2018-19 New Student Registration 新生注册', '', $picUrl, $url);
				break;
		}
		Yii::app()->end();
	}
}
