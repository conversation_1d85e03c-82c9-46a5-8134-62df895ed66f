<?php

/**
 * This is the model class for table "ivy_infopub_staff_extend".
 *
 * The followings are the available columns in table 'ivy_infopub_staff_extend':
 * @property integer $userid
 * @property string $intro_cn
 * @property string $intro_en
 * @property string $staff_photo
 * @property integer $updated_user
 * @property integer $updated_time
 */
class InfopubStaffExtend extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return InfopubStaffExtend the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_infopub_staff_extend';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('userid, updated_user, updated_time', 'numerical', 'integerOnly'=>true),
			array('staff_photo', 'length', 'max'=>255),
			array('intro_cn, intro_en', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('userid, intro_cn, intro_en, staff_photo, updated_user, updated_time', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'profile' => array(self::BELONGS_TO, 'UserProfile', 'userid'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'userid' => 'Userid',
			'intro_cn' => 'Intro Cn',
			'intro_en' => 'Intro En',
			'staff_photo' => 'Staff Photo',
			'updated_user' => 'Updated User',
			'updated_time' => 'Updated Time',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('userid',$this->userid);
		$criteria->compare('intro_cn',$this->intro_cn,true);
		$criteria->compare('intro_en',$this->intro_en,true);
		$criteria->compare('staff_photo',$this->staff_photo,true);
		$criteria->compare('updated_user',$this->updated_user);
		$criteria->compare('updated_time',$this->updated_time);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}