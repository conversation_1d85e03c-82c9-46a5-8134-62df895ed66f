<?php
/**
 * Created by PhpStorm.
 * User: XINRAN
 * Date: 14-3-20
 * Time: 下午2:03
 */
?>
<div class="modal fade" id="unameModal" tabindex="-1" role="dialog" aria-labelledby="ModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title" id="ModalLabel">首次评论请输入显示名</h4>
            </div>
            <div class="modal-body">
                <form class="form-horizontal" role="form" id="loginForm">
                    <div class="form-group" id="form-uname">
                        <label for="uname" class="col-sm-2 hidden-xs control-label">显示名</label>
                        <div class="col-sm-10">
                            <input type="text" class="form-control" id="uname"
                                   name="ClubUsers[uname]" placeholder="请输入显示名"
                                   value="<?php echo Yii::app()->user->getState('clubUsername');?>">
                        </div>
                    </div>
                    <div class="form-group sr-only" id="form-uname-status">
                        <div class="col-sm-offset-2 col-sm-10">
                            <div class="alert alert-danger"></div>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-offset-2 col-sm-10">
                            <button type="button" class="btn btn-primary" id="submitbtn">提交</button>
                        </div>
                    </div>
                </form>
            </div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->

<script>
    $('input#uname').keydown(function(event){
        if(event.keyCode==13){
            event.preventDefault();
            $('#submitbtn').click();
        }
    });
    $('#submitbtn', '#unameModal').on('click', function(){
        $.ajax({
            type: 'POST',
            url: '<?php echo $this->createUrl('//user/login/uname');?>',
            data: {uname: $('#uname').val()},
            dataType: 'json'
        }).done(function(data){
            if(data.state == 'success'){
                window.location.reload();
            }
            else{
                $('#form-uname-status').removeClass('sr-only');
                $('#form-uname-status .alert').html(data.msg);
            }
        });
    });
    function openClubUnameModel(){
        $('#unameModal').modal({
            keyboard: false,
            backdrop: 'static'
        });
    }
</script>