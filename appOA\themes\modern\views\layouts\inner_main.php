<!doctype html>
<html>
<head>

	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<meta name="language" content="en" />
	<meta http-equiv="X-UA-Compatible" content="IE=edge" />
	
	<link href="<?php echo Yii::app()->theme->baseUrl; ?>/css/admin_style.css?v<?php echo Yii::app()->params['assetsVersion'];?>" rel="stylesheet" />
	<link href="<?php echo Yii::app()->theme->baseUrl; ?>/css/main.css?v<?php echo Yii::app()->params['assetsVersion'];?>" rel="stylesheet" />
	<script>
	
	var GV = {
		JS_ROOT : "<?php echo Yii::app()->request->baseUrl; ?>/js/dev/",	//js目录
		JS_VERSION : "<?php echo Yii::app()->params['assetsVersion'];?>",	//版本号,强制刷新
		//TOKEN : '{@Wind::getComponent('windToken')->saveToken('csrf_token')}',	//token ajax全局
		TOKEN : '',	//token ajax全局
		URL : {
			LOGIN : ''//后台登录地址
		},
        'themeManagerBaseUrl': '<?php echo Yii::app()->themeManager->baseUrl;?>',
        'themeBaseUrl': '<?php echo Yii::app()->theme->baseUrl;?>',
        'lang': "<?php echo Yii::app()->language;?>"
	};
	</script>

	<title><?php echo CHtml::encode($this->pageTitle); ?></title>
</head>
<body>

	<?php Yii::app()->clientScript->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/head.min.js');?>
	<?php Yii::app()->clientScript->registerCoreScript('jquery');?>

	<div class="wrap">
		<?php
		if ( !empty($this->mainMenu) ):
			echo CHtml::openTag("div", array("class"=>"nav"));
			$this->widget('zii.widgets.CMenu',array(
				'items'=> $this->mainMenu,
				'htmlOptions'=>array('class'=>'cc'),
				'activeCssClass'=>'current',
			));
			echo CHtml::closeTag("div");
		endif;
		?>

		<?php
		if ( $this->branchBased || !empty($this->subMenu) ):
			echo CHtml::openTag("div", array("class"=>"nav_minor"));
			if ( $this->branchBased ):
				$this->widget('ext.branchSelector.branchSelector', $this->branchSelectParams);
			endif;
			
			
			if ( !empty($this->subMenu) ):
				echo CHtml::openTag("div", array("id"=>"subMenu"));
				$this->widget('zii.widgets.CMenu',array(
					'items'=> $this->subMenu,
					'activeCssClass'=>'current',
				));
				echo CHtml::closeTag("div");
			endif;
			
			echo CHtml::openTag("div",array("class"=>"fr"));?>
				<a class="btn" title="<?php echo Yii::t("child","Add Child");?>" href="<?php echo Yii::app()->params['OABaseUrl'];?>/modules/childmgt/addchild.php?action=s" target="_blank"><span class="add"></span><?php echo Yii::t("child","Add Child");?></a>
			<?php
			echo CHtml::closeTag("div");
			

			echo CHtml::closeTag("div");
			echo CHtml::openTag("div", array("class"=>"clear"));
			echo CHtml::closeTag("div");
		endif;
		?>


		<?php if( 0 && isset($this->breadcrumbs)):?>
			<?php $this->widget('zii.widgets.CBreadcrumbs', array(
				'links'=>$this->breadcrumbs,
			)); ?><!-- breadcrumbs -->
		<?php endif?>
	
		<?php echo $content; ?>

	</div>
	

</div><!-- page -->

<script type="text/javascript" src="<?php echo Yii::app()->themeManager->baseUrl; ?>/base/js/common.js"></script>


</body>
</html>