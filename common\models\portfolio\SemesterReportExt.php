<?php

/**
 * This is the model class for table "ivy_semester_report_ext".
 *
 * The followings are the available columns in table 'ivy_semester_report_ext':
 * @property integer $id
 * @property integer $reportid
 * @property string $category
 * @property integer $pid
 * @property integer $tid
 * @property string $content
 */
class SemesterReportExt extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return SemesterReportExt the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_semester_report_ext';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('content', 'required'),
			array('reportid, pid, tid', 'numerical', 'integerOnly'=>true),
			array('category', 'length', 'max'=>9),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, reportid, category, pid, tid, content', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'reportid' => 'Reportid',
			'category' => 'Category',
			'pid' => 'Pid',
			'tid' => 'Tid',
			'content' => 'Content',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('reportid',$this->reportid);
		$criteria->compare('category',$this->category,true);
		$criteria->compare('pid',$this->pid);
		$criteria->compare('tid',$this->tid);
		$criteria->compare('content',$this->content,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}