<?php

class Contact extends CWidget {

    public $classId;
    public $cssFile;
    public $assetsUrl;

    public function init() {
        Yii::import('ext.classContact.models.*');
        Yii::import('common.models.portfolio.NotesChild');

//        if ($this->assetsUrl === null)
//            $this->assetsUrl = Yii::app()->getAssetManager()->publish(dirname(__FILE__) . '/assets', false, -1, YII_DEBUG);

        $cs = Yii::app()->clientScript;
        if (false !== $this->cssFile) {
            if (null === $this->cssFile)
//                $this->cssFile = $this->assetsUrl . '/contact.css?t=' . Yii::app()->params['refreshAssets'];
                $this->cssFile = Yii::app()->theme->baseUrl.'/widgets/classContact/contact.css?t='.Yii::app()->params['refreshAssets'];
            $cs->registerCssFile($this->cssFile);
//			$cs->registerCssFile(Yii::app()->theme->baseUrl.'/css/widgetbase.css');
            $cs->registerCssFile(Yii::app()->theme->baseUrl . '/css/widgetbase.css?t=' . Yii::app()->params['refreshAssets']);
        }
    }

    public function run() {
        if (!Yii::app()->user->getIsGuest()):
            Yii::import('common.models.invoice.Invoice');
            Yii::import('common.models.child.HomeAddress');
            Yii::import('common.models.child.ChildMisc');

            Mims::LoadHelper('HtoolKits');

            /*
             * 得到孩子所上过的所有班级
             */
            if (!$this->classId) {
                return false;
            }
            $nc = new NotesChild();
            $classIds = $nc->getClassIdList($this->getController()->getChildId());
            $classIds[$this->classId] = $this->classId;
            $use_class_list = count($classIds) > 1 ? true : false;
            $class_cri = new CDbCriteria();
            $class_cri->compare('classid', $classIds);
            $class_cri->order = 'yid DESC';
            $class_cri->index='classid';
            $class_list = null;
            $class_info = IvyClass::model()->findAll($class_cri);
            foreach ($class_info as $key => $class) {
                $class_list[$class->classid] = $class->title;
            }

            if (isset($class_info[$this->classId]) && $class_info[$this->classId]->stat == IvyClass::STATS_OPEN){
                $criteria = new CDbCriteria();
                $criteria->compare('t.classid', $this->classId);
                $childs = ChildProfileBasic::model()->with('homeaddr')->findAll($criteria);
                $claOpen = true;
            }
            else{
                $criteria = new CDbCriteria();
                $criteria->compare('t.classid', $this->classId);
                $criteria->select=array('childid');
                $criteria->distinct=true;
    //            $criteria->compare('t.payment_type', 'tuition');
    //            $criteria->addNotInCondition('t.status', array(88, 99));
                $childs = NotesChild::model()->with('childInfo')->findAll($criteria);
                $claOpen = false;
            }

            $ret = array();
            $parentId['fid'] = array();
            $parentId['mid'] = array();
            $classChild = array();
            foreach ($childs as $_child) {
                $child = isset($_child->childInfo) ? $_child->childInfo : $_child;
                $classChild[$child->childid] = array(
                    'name' => $child->getChildName(true, true),
                    'photo' => $child->photo,
                    'hometel' => $child->homeaddr->en_telephone,
                    'statText'=>$child->status >= ChildProfileBasic::STATS_GRADUATED ? $child->getStatus() : '',
                );
                $childPrivacy[$child->childid] = unserialize($child->misc->privacy);
                $parentId['fid'][$child->fid] = $child->fid;
                $parentId['mid'][$child->mid] = $child->mid;
                $childP[$child->childid]['fid'] = $child->fid;
                $childP[$child->childid]['mid'] = $child->mid;
            }

            $criteria = new CDbCriteria();
            $criteria->compare('pid', $parentId['fid'] + $parentId['mid']);
            $parentModel = IvyParent::model()->with('user')->findAll($criteria);
            $parents = array();
            foreach ($parentModel as $parent) {
                $parents[$parent->pid] = $parent;
            }

            $locked = CHtml::openTag("em", array("class" => "lock", "title" => Yii::t("labels", 'Privacy')));
            $locked .= CHtml::closeTag("em");
            foreach ($classChild as $childid => $childinfo) {
                $ret[$childid]['name'] = $childinfo['name'];
                $ret[$childid]['photo'] = $childinfo['photo'];
                $pri = $childPrivacy[$childid] ? $childPrivacy[$childid] : array('father_email', 'father_mobile', 'mother_mobile', 'mother_email', 'home_phone');
                $ret[$childid]['fmail'] = isset($parents[$childP[$childid]['fid']]->user->email) && !in_array('father_email', $pri) ? $parents[$childP[$childid]['fid']]->user->email : $locked;
                $ret[$childid]['fmp'] = isset($parents[$childP[$childid]['fid']]->mphone) && !in_array('father_mobile', $pri) ? $parents[$childP[$childid]['fid']]->mphone : $locked;
                $ret[$childid]['mmail'] = isset($parents[$childP[$childid]['mid']]->user->email) && !in_array('mother_email', $pri) ? $parents[$childP[$childid]['mid']]->user->email : $locked;
                $ret[$childid]['mmp'] = isset($parents[$childP[$childid]['mid']]->mphone) && !in_array('mother_mobile', $pri) ? $parents[$childP[$childid]['mid']]->mphone : $locked;
                $ret[$childid]['tel'] = isset($childinfo['hometel']) && !in_array('home_phone', $pri) ? $childinfo['hometel'] : $locked;
                $ret[$childid]['statText'] = $childinfo['statText'];
            }

            $this->render('contact', array('use_class_list' => $use_class_list, 'class_list' => $class_list, 'currentClassid' => $this->classId, 'ret' => $ret, 'claOpen'=>$claOpen));
        else:
            $this->render('noservice');
        endif;
    }

}