<style>
.panel-body{
    height:250px;
    overflow-y:auto
}
[v-cloak] {
		display: none;
	}
.panel-footer{
    height:40px;
    font-size:16px
}
.panel-footer a{
    color:#000
}
</style>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site','Campus Operations'), array('/mcampus/default/index'))?></li>
        <li><?php echo Yii::t('site','General Survey Result');?></li>
    </ol>
    <div id='schoolTemplate' v-cloak>
        <div class='row'>
            <div class='col-md-12 mb20'>
                <ul class="nav nav-pills">
                    <li v-for='(year,key,index) in calendarArr'   :class="year.yid==yid ? 'active':''" >
                        <a :href="'<?php echo $this->createUrl('index',array('branchId'=> $branchId)) ?>&yid='+year.yid+''" >{{year.startyear}}</a>
                    </li>
                </ul>
            </div>
        </div>
        <div class='row'>
            <div class='col-md-3' v-for='(list,index) in schoolTemplate'>
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h3 class="panel-title">{{list.cn_title}}</h3>
                    </div>
                    <div class="panel-body">
                        <p class='text-danger'>开放日期：{{list.start_timestamp}} ~ {{list.end_timestamp}}</p>
                        <p>{{list.cn_intro}}</p>
                        <p>
                            <span class="label label-primary mr5 mb5 p5" style='display:inline-block' v-for='classData in list.classType'>
                            {{classType[classData]}}</span>
                        </p>

                        <!-- <div>
                            <a :href="'<?php echo $this->createUrl('result',array('branchId'=> $branchId)) ?>&template_id='+list.template_id+'&return_id='+list.id+''" class="btn btn-primary " role="button"  target="_Blank"><?php echo Yii::t('site','查看问卷结果');?></a>
                            <a :href="'<?php echo $this->createUrl('qrcode',array('branchId'=> $branchId)) ?>&id='+list.id" class="btn btn-primary J_modal" role="button"><?php echo Yii::t('site','查看二维码');?></a>
                        </div> -->
                    </div>
                    <div class="panel-footer">
                            <a :href="'<?php echo $this->createUrl('result',array('branchId'=> $branchId)) ?>&template_id='+list.template_id+'&return_id='+list.id+''" class="glyphicon glyphicon-th-list col-md-3" target="_Blank" title='查看问卷结果'></a>

                            <i class='glyphicon glyphicon-list-alt col-md-3' title='编辑班级' @click='editClass(list)'></i>

                            <i class='glyphicon glyphicon-time col-md-3' title='编辑时间状态' @click='editTemplate(list)'></i>
                            
                            <a :href="'<?php echo $this->createUrl('qrcode',array('branchId'=> $branchId)) ?>&id='+list.id" class="J_modal glyphicon glyphicon-qrcode col-md-3" title='查看二维码'></a>
                            <div class='clear:both'></div>
                        </div>
                </div>
            </div>
        </div>
        <div class="modal fade bs-example-modal-lg" id="addgroup" tabindex="-1" role="dialog" data-backdrop="static">
            <div class="modal-dialog modal-lg" >
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">
                        &times;
                    </button>
                        <h4 class="modal-title" id="myModalLabel">
                        分配模板
                    </h4>
                    </div>
                    <div class="modal-body">
                        <div class="form-horizontal">
                            <div class="form-inline row mb15">
                                <label for="inputPassword" class="col-sm-2 control-label">开放日期：</label>
                                <div class="col-sm-10">
                                    <input type="text" class="form-control" id="startTime" :value='start_timestamp'  v-model='start_timestamp' placeholder="开始时间">
                                    —
                                    <input type="text" class="form-control" id="endTime" :value='end_timestamp'  v-model='end_timestamp'  placeholder="结束时间">
                                    <span>开放到结束日期24点</span>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="cn_intro" class="col-sm-2 control-label">状态</label>
                                <div class="col-sm-10">
                                    <label class="checkbox-inline">
                                        <input type="checkbox" v-model='status' value='1' name='status'> 是否开启
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" @click='updateAssigned()' :disabled="disabled">确认</button>
                        <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal fade bs-example-modal-lg" id="editClass" tabindex="-1" role="dialog" data-backdrop="static">
            <div class="modal-dialog modal-lg" >
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">
                        &times;
                    </button>
                        <h4 class="modal-title" id="myModalLabel">
                        分配模板
                    </h4>
                    </div>
                    <div class="modal-body">
                        <div class="form-horizontal">
                            <div class="form-group">
								<label for="inputPassword" class="col-sm-2 control-label">选择班级：</label>
								<div class="col-sm-10">
                                    <template v-for='(Data,index,key) in classType'>
                                        <label class="checkbox-inline">
                                            <input type="checkbox" id="inlineCheckbox1" :value='index' v-model='classids'> {{Data}}
                                        </label>
                                    </template>
								</div>
							</div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" @click='updataCalss()' :disabled="disabled">确认</button>
                        <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    var schoolTemplate = <?php echo json_encode($templateInfo) ?>;
    var calendarArr = <?php echo json_encode($calendarArr) ?>;
    var classType = <?php echo json_encode($classType) ?>;
    var yid = '<?php echo $yid ?>';//学年
    var datalist = new Vue({
        el: "#schoolTemplate",
        data: {
            schoolTemplate:schoolTemplate,
            calendarArr:calendarArr,
            yid:yid,
            editTem:'',
            thisData:'',
            classids:[],
            start_timestamp:'',
            end_timestamp:'',
            disabled:false,
            classType:classType,
            currentData:'',
            status:""
        },
        methods: {
            editTemplate(data){
                var that=this
                $( "#startTime" ).datepicker({
                    dateFormat: "yy-mm-dd",
                    onClose : function(dateText, inst){//当日期面板关闭后触发此事件（无论是否有选择日期）
                        that.start_timestamp=dateText
                    }
                });
                $( "#endTime" ).datepicker({
                    dateFormat: "yy-mm-dd",
                    onClose : function(dateText, inst){//当日期面板关闭后触发此事件（无论是否有选择日期）
                        that.end_timestamp=dateText
                    }
                });
                that.start_timestamp=data.start_timestamp
                that.end_timestamp=data.end_timestamp
                that.classids=data.classType
                that.currentData=data
                that.status=data.status=="1"?true:false
                $('#addgroup').modal('show')
            },
            editClass(data){
                var that=this
                that.classids=data.classType
                that.currentData=data
                console.log(that.classids)
                $('#editClass').modal('show')
            },
            updateAssigned(){
                var that=this
                that.disabled=true
                $.ajax({
                    url: '<?php echo $this->createUrl("updateReturn")?>',
                    type: "post",
                    async: true,
                    dataType: 'json',
                    data: {
                        id:that.currentData.id,
                        status:that.status?'1':'0',
                        start_timestamp:that.start_timestamp,
                        end_timestamp:that.end_timestamp,
                        type:'saveTime'
                    },
                    success: function(data) {
                        that.disabled=false
                        if(data.state == 'success') {
                            $('#addgroup').modal('hide')
                            resultTip({
                                "msg": data.message
                            })
                            for(var i=0;i<that.schoolTemplate.length;i++){
                                if(that.schoolTemplate[i].id==that.currentData.id){
                                    Vue.set(that.schoolTemplate, i, data.data)
                                }
                            }
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        that.disabled=false
                        alert("请求错误")
                    }
                });
            },
            updataCalss(){
                var that=this
                that.disabled=true
                $.ajax({
                    url: '<?php echo $this->createUrl("updateReturn")?>',
                    type: "post",
                    async: true,
                    dataType: 'json',
                    data: {
                        id:that.currentData.id,
                        class_type:that.classids,
                        type:'saveClass'
                    },
                    success: function(data) {
                        that.disabled=false
                        if(data.state == 'success') {
                            $('#editClass').modal('hide')
                            resultTip({
                                "msg": data.message
                            })
                            for(var i=0;i<that.schoolTemplate.length;i++){
                                if(that.schoolTemplate[i].id==that.currentData.id){
                                    Vue.set(that.schoolTemplate[i],'classType', data.data.classType)
                                }
                            }
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        that.disabled=false
                        alert("请求错误")
                    }
                });
            }
        }
    })
    var modal = '<div class="modal fade" id="modal" tabindex="-1" role="dialog" aria-labelledby="modal"><div class="modal-dialog" role="document"><div class="modal-content"></div></div></div>';
    $('body').append(modal);
</script>
<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>
