<div class="col-md-2">
    <div class="list-group weekday-label-list">
    </div>
</div>
<div class="col-md-10 timeslot-schedule-zone">
    <table class="table table-bordered timeslot-schedule" style="display: none;">
        <thead></thead>
        <tbody>
        </tbody>
    </table>
</div>

<script>

    var groupedValidScheduleData = null;
    var selectedWeekday = 0;

    var renderDataschedules = function(viewid){
        wrapper = $('.view-zone[data-viewid|="'+viewid+'"]');

        if( wrapper.find('.weekday-label-list a').length < 1){
            var _temp = _.template(
                '<a href="javascript:;" data-weekday=<%= day %> onclick="filterTimeslotSchedule(<%= day %>, this)" class="list-group-item"><%- daytitle %></a>'
            );
            _.each(pageData.dayLabels, function(_day, _i){
                var _a = _temp({day:_i, daytitle:_day});
                $('.weekday-label-list').append(_a);
            })
            $('.weekday-label-list').prepend(_temp({day:0, daytitle: 'All'}));
        }

        loadSchedule();
    }

    var filterTimeslotSchedule = function(weekday, obj){
        selectedWeekday = weekday;
        if(!_.isUndefined(obj)){
            $(obj).siblings().removeClass('active');
            $(obj).addClass('active');
        }
        if(weekday){
            wrapper.find('table.timeslot-schedule[data-weekday]').hide();
            wrapper.find('table.timeslot-schedule[data-weekday|="'+weekday+'"]').show();
        }else{
            wrapper.find('table.timeslot-schedule[data-weekday]').show();
        }
    }

    var groupbyScode_Day_Data = {};

    var loadSchedule = function(){
        wrapper.find('table.timeslot-schedule[data-weekday]').remove();
        var tableSource = wrapper.find('table.timeslot-schedule').first();
        tableSource.hide();

        if(_.isEmpty(groupbyScode_Day_Data)){
            groupbyScode_Day_Data = _.groupBy(pageData.scheduleList, 'weekday');
        }
        for(var _weekday=1; _weekday<6; _weekday++){

            var table = tableSource.clone().attr('data-weekday', _weekday);
            var thead = table.find('thead');
            var tbody = table.find('tbody');
            thead.empty();
            tbody.empty();

            //画表头
            var _tr = $('<tr class="active"></tr>').append('' +
                '<th rowspan="2" class="warning" width=120 valign="top"><h3>'+
                '<span class="glyphicon glyphicon-calendar"></span> ' +
                '</h3></th>' +
                '');
            _tr.append(
                $('<th></th>')
                    .attr('colspan', _.size(pageData.classList))
                    .html('<h4>'+pageData.dayLabels[_weekday]+'</h4>')
            ).appendTo(thead);
            var _tr = $('<tr></tr>');
            _.each( sortedClassIdWithData , function( _classid ){
                $('<td></td>').html(pageData.classList[_classid].title).appendTo(_tr);
            });
            thead.append(_tr);

            //画表体
            _.each( programCfg.schedules[scheduleCode], function(timeslot){
                var _tr = $('<tr></tr>').append($('<th class="active"></th>').html('<h5>'+timeslot+'</h5>'));
                _.each( sortedClassIdWithData, function( _classid ){
                    _tr.append($('<td></td>')
                        .attr('data-classid',_classid)
                        .attr('data-day',_weekday)
                        .attr('data-timeslot',timeslot));
                } );
                tbody.append(_tr);
            });

            //填充表格内容
            _.each(groupbyScode_Day_Data[_weekday], function(item,index){
                var _item = classesScheduleItemTemplate(item);
                tbody.find('td[data-timeslot|="'+item.timeslot+'"][data-day|="'+item.weekday+'"][data-classid|="'+item.classid+'"]')
                    .attr("data-subject",item.subject_flag)
                    .append(_item);
            })

            table.appendTo(wrapper.find('.timeslot-schedule-zone'));
            table.show();
        }

    }

</script>