<?php

class SupportController extends ProtectedController {

    public $defaultAction = 'campus';
    public $wechatPerAccount = 10; // 与 MimsAPI::MAX_NUMBER_BOUND_PER_ACCOUNT 相同
    protected $securityKey = 'A23sW343eL8934ov2332E';

    public function actionCampus() {

        $show = Yii::app()->request->getParam("show", "");

        //显示该家长下所有的孩子所在的所有校园，理论上自己的孩子们可能会在不同的校园
        if (strtolower($show) == "all") {
            $cities = Term::model()->city()->with('campuslist')->findAll();
            $this->render('campuslist', array("cities" => $cities));
        } else {
            if (Yii::app()->user->getIsGuest()) {
                $this->redirect(array("//child/support/campus", "show" => "all"));
            }

            $cids = Yii::app()->user->getAdminCIds();
            //若是员工访问本页面：
            $cids = (empty($cids)) ? array($this->getChildId()) : $cids;
            $schoolIds = array();
            foreach ($cids as $cid) {
                $schoolIds[] = $this->myChildObjs[$cid]->schoolid;
            }

            $schoolIds = array_unique($schoolIds);
            $branches = Branch::model()->with('info')->findAllByPk($schoolIds);

            $this->render('campus', array("branches" => $branches));
        }
    }

    public function actionEmail() {
        Yii::import('common.models.classTeacher.*');
        $model = new IvyMail;

        if (isset($_POST['ajax']) && $_POST['ajax'] === 'support-form') {
            echo CActiveForm::validate($model);
            Yii::app()->end();
        }

        $child_obj = $this->myChildObjs[$this->childid];
        $classId = $child_obj->classid;
        $childInfo = "";
        $childInfo .= $child_obj->getChildName();
        if ($classId) {
            $class = IvyClass::model()->findByPk($classId);
            $childInfo .= addComma($class->title);
        }

        $child = $this->myChildObjs[$this->childid];
        $branch = Branch::model()->with('info')->findByPk($child->schoolid);
        if (isset($_POST['IvyMail'])) {
            if (Yii::app()->user->checkAccess('sendEmail', array("childid" => $this->childid))) {
                $model->attributes = $_POST['IvyMail'];
                if ($model->validate()) {
                    $model->content = nl2br(CHtml::encode($model->content));
                    $subject = sprintf(Mims::unIvy()?'['.Yii::app()->params['ownerConfig']['name_cn'].' - %s] %s':"[IvyOnline Support - %s] %s", $branch->abb, $model->subject);
                    $support_email = array($branch->info->support_email);

                    $teacherEmail = array();
                    if(isset($_POST['for_teacher'])){
                        if(in_array($child->status, array(ChildProfileBasic::STATS_ACTIVE_WAITING,ChildProfileBasic::STATS_ACTIVE)) && $child->classid > 0){
                            $criteria = new CDbCriteria();
                            $criteria->select = "teacherid";
                            $criteria->compare('t.classid', $child->classid);
                            $criteria->compare('t.yid', $this->childInfo["yid"]);
                            $criteria->compare('t.schoolid', $this->childInfo["schoolid"]);
                            $criteria->index = "teacherid";
                            $classModel = ClassTeacher::model()->findAll($criteria);
                            if(isset($classModel)){
                                $criteria = new CDbCriteria();
                                $criteria->compare('t.uid', array_keys($classModel));
                                $criteria->compare('t.level', 1);
                                $teacherModel = User::model()->findAll($criteria);
                                if(isset($teacherModel)){
                                    foreach($teacherModel as $item){
                                        $teacherEmail[] = $item->email;
                                    }
                                }
                            }
                        }
                    }
                    $support_email_arr = array_merge($support_email,$teacherEmail);

                    // 联系校园
                    $sendEmail = $this->widget('ext.sendEmail.Send', array(
                        'viewName' => 'contactCampus',
                        'lang' => false,
                        'toParty' => false, //发送至 当事人 -参数 party_email
                        'toParent' => false, //发送至 父母 -
                        'toSupport' => true, //发送至 校园支持 -参数 support_email
                        'ccSpouse' => false, //抄送至 配偶 -
                        'ccParent' => false, //抄送至 父母 -
                        'ccSupport' => false, //抄送至 校园支持 -参数 support_email
                        'bccItDev' => true, // 密抄至 ItDev -
                        'replyToSupport' => false, // 回复至 校园支持 -参数 support_email
                        'params' => array(
                            'support_email' => $support_email_arr,
                            'subject' => $subject,
                            'content' => $model->content,
                            'useStyle' => true,
                            'classTitle' => $class->title,
                            'childName' => $child_obj->getChildName(),
                            'parentEmail' => $_POST['parent_email'],
                            'dateTime' => time(),
                            'cc' => $model->cc,
                        ),
                            ));

                    if ($sendEmail->getSendFlag()) {
                        Yii::app()->user->setFlash('success', Yii::t("message", "Email sent to support team!"));
                    } else {
                        Yii::app()->user->setFlash('error', Yii::t("message", "Email sending failed!"));
                    }
                    $this->render('emailsent', array('model' => $model));
                    Yii::app()->end();
                } else {
                    $this->render('email', array('model' => $model, 'branch' => $branch));
                    Yii::app()->end();
                }
            }
        } else {

            $defaultSubject = $childInfo;
            $model->to = strtoupper($branch->info->support_email);

            $parentIds = array();
            foreach ($this->myChildObjs as $child) {
                $parentIds[$child->fid] = $child->fid;
                $parentIds[$child->mid] = $child->mid;
            }

            Yii::import('user.models.*');

            $users = User::model()->findAllByPk($parentIds);
            $pEmails = array();
            foreach ($users as $user) {
                $pEmails[] = $user->email;
            }
            $model->cc = implode(";", $pEmails);
            $model->replyto = implode(";", $pEmails);
            $model->subject = $defaultSubject;
        }

        $this->render('email', array('model' => $model, 'branch' => $branch, 'child' => $child));
    }

    public function actionPta() {

        if (Yii::app()->user->checkAccess('viewClassContact', array("childid" => $this->getChildId()))) {
            Yii::import('common.models.pta.*');
            $pta_cri = new CDbCriteria();
            $pta_cri->compare('t.status', 0);
            $pta_cri->compare('t.schoolid', $this->getSchoolId());
            $pta_cri->compare('classLink.stat', array(0, 10, 20)); // 注册 等待付款 在读
            $_pta_info = Pta::model()->with('classLink')->findAll($pta_cri);

            $class_ids = null;
            $parent_ids = null;
            if (!empty($_pta_info)) {
                foreach ($_pta_info as $pta) {
                    if (isset($pta->classLink)) {
                        $class_ids[$pta->classLink->classid] = $pta->classLink->classid;
                    }
                    $parent_ids[$pta->parent_id] = $pta->parent_id;
                    $pta_info[$pta->classLink->classid][$pta->id] = $pta;
                }
            }

            $parent_cri = new CDbCriteria();
            $parent_cri->addInCondition('t.pid', $parent_ids);
            $parent_cri->index = 'pid';
            $parent_info = IvyParent::model()->with('user', 'countryInfo')->findAll($parent_cri);

            $class_cri = new CDbCriteria();
            $class_cri->compare('classid', $class_ids);
            $class_cri->index = 'classid';
            $class_cri->order = 'child_age, title';
            $class_info = IvyClass::model()->findAll($class_cri);
            
            $criteria = new CDbCriteria;
            $criteria->compare('schoolid', $this->getSchoolId());
            $criteria->order='memo_date desc';
            $memos = PtaMemo::model()->with('content')->findAll($criteria);

            $this->render('pta', array('noservice' => false, 'pta_info' => $pta_info, 'parent_info' => $parent_info, 'class_info' => $class_info, 'memos'=>$memos));
        } else {
            $this->render('pta', array('noservice' => true));
        }
    }

    public function actionPtaGuidelines() {
        // 输出 变量到 视图文件
        $view_data = array(
            'langKey' => Yii::app()->language == 'en_us' ? 'en' : 'cn',
        );
        $this->renderPartial('ptaGuidelines', $view_data);
    }

    public function actionLunch() {

        Yii::import('common.models.invoice.*');
        Yii::import('common.models.calendar.*');
        Yii::import('common.components.policy.*');

        if (Yii::app()->user->checkAccess('viewPayment', array("childid" => $this->getChildId()))) {
            $schoolId = $this->myChildObjs[$this->getChildId()]->schoolid;
            $calendarId = Branch::model()->getBranchInfo($schoolId, 'schcalendar');

            $criteria = new CDbCriteria();
            $criteria->compare('yid', $calendarId);
            $criteria->compare('branchid', $schoolId);
            $criteria->compare('is_selected', 1);
            $calendarModel = CalendarSchool::model()->find($criteria);

            $CfgFeeType = Mims::LoadConfig('CfgFeeType');
            $lunchType = $CfgFeeType["lunch"]["sign"];
            //get school lunch config

            /*$feeObj = FeemgtSchoolConfig::model()->getSchoolFeeConfig($schoolId, $calendarId, $lunchType);
            if (empty($feeObj) || ($feeObj->lunch_payment_type == 30)) {*/
            $IvyPolicy = new IvyPolicy('refund', $calendarModel->startyear, $schoolId);
            if (empty($IvyPolicy) || empty($IvyPolicy->configs['LUNCH_PERDAY'])) {
                $this->render('lunch_nocancel');
            }
            else{
                $this->render('lunch', array("calendarId" => $calendarId, "lunchType" => $lunchType, "childId" => $this->getChildId(), "schoolId" => $schoolId));
            }
                
        } else {
            $this->render("noservice");
        }
    }
    
    public function actionPtamemoview($id)
    {
        if ($id){
            Mims::LoadHelper('HtoolKits');
            Yii::import('common.models.pta.*');
            
            $criteria = new CDbCriteria;
            $criteria->compare('t.schoolid', $this->getSchoolId());
            $criteria->compare('t.id', $id);
            $memo = PtaMemo::model()->with('content')->find($criteria);
            
            $adminPta = array();
            $parentPta = array();
            
            $ptaadmin = $memo->pta_admins;
            if ($ptaadmin){
                $ptaadmin_ext = explode(',', $ptaadmin);
                foreach ($ptaadmin_ext as $uid){
                    if ($uid){
                        $adminPta[$uid] = $uid;
                    }
                }
            }
            $ptaparent = $memo->pta_members;
            if ($ptaparent){
                $ptaparent_ext = explode(',', $ptaparent);
                foreach ($ptaparent_ext as $uid){
                    if ($uid){
                        $parentPta[$uid] = $uid;
                    }
                }
            }
            
            $alluids = $adminPta + $parentPta;
            
            $attendance = '';
            
            if ($alluids){
                $criteria = new CDbCriteria;
                $criteria->compare('t.uid', $alluids);
                $users = User::model()->with('profile')->findAll($criteria);
                foreach ($users as $user){
                    $attendance .= HtoolKits::getContentByLang($user->name, $user->profile->first_name.' '.$user->profile->last_name).', ';
                }
            }

            if($memo->content->ext_members)
                $attendance .= $memo->content->ext_members.', ';
            if($memo->content->ext_admins)
                $attendance .= $memo->content->ext_admins;
            if (substr($attendance, -2, 2) == ', '){
                $attendance = substr($attendance, 0, -2);
            }
            $this->renderPartial('ptaMemoview', array('memo'=>$memo, 'attendance'=>$attendance));
        }
    }
    
    public function actionWechat(){
        Yii::import('application.modules.weixin.models.*');
        $remoteToken = Yii::app()->cache->get(md5('weixin-token'));
        $account = Mims::unIvy() ? 'ds' : 'ivy';
        if(Yii::app()->getRequest()->isAjaxRequest){
            if(
                (Yii::app()->user->checkAccess('adminChild', array("childid" => $this->childid))) &&
                isset($_POST['command']) &&
                ($_POST['command'] == 'doRemote')
            ){
                $crit = new CDbCriteria;
                $crit->compare('userid', Yii::app()->user->id);
                $crit->compare('account', $account);
                $crit->compare('flag', 0);
                $crit->order = 'created DESC';
                $scene = WechatQRScene::model()->find($crit);
                $time = time();
                if(!empty($scene) && ($scene->updated + $scene->expire ) > $time ){
                    $result = array(
                        'state' => 'success',
                        'message' => 'from Cache..',
                        'data'=>array('ticket' => $scene->ticket)
                    );
                    echo CJSON::encode($result);
                    Yii::app()->end();
                }
                
                if(empty($scene)) {
                    $scene = new WechatQRScene;
                    $scene->account = $account;
                    $scene->requested = 0;
                    $scene->flag = 0;
                    $scene->created = time();
                    $scene->userid = Yii::app()->user->id;
                }
                $scene->expire = 0;
                $scene->updated = time();
                $scene->ticket = '';
                $scene->requested++;
                $scene->save();
            
				$ch = curl_init();
				$time = time();
				$command = 'doQrCode';
				$data = array(
					"postTime" => $time,
					"command" => $command,
                    "scene_id" => $scene->id,
					"postKey" => md5(sprintf("%s&%s&%s", $time, $command, $this->securityKey))
				);
				curl_setopt($ch, CURLOPT_URL,  $this->createUrl("/weixin/remoteCall/doQrCode")); 
				curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
                //curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json'));
				curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
				curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
				$return = curl_exec($ch);
                $return = substr($return, 0, strpos($return, '}')+1);
                $data = CJSON::decode($return);
                curl_close($ch);
                if(isset($data['ticket'])){
                    $result = array(
                        'state' => 'success',
                        'message' => $return,
                        'data'=>array('ticket' => $data['ticket'])
                    );
                    $scene->ticket = $data['ticket'];
                    $scene->expire = $data['expire_seconds'];
                    $scene->updated = time();
                    $scene->save();
                }else{
                    $result = array(
                        'state' => 'fail',
                        'message' => $return,
                        'data'=>array('ticket' => '')
                    );
                }
                echo CJSON::encode($result);
                Yii::app()->end();
            }
        }else{
            $speUid = null;
            $wechatData = $this->getWechatUsers(null, $speUid);
            
            Yii::app()->clientScript->registerCssFile(Yii::app()->theme->baseUrl . '/css/wechat.css?t=' . Yii::app()->params['refreshAssets']);
            $this->render('wechat', array(
                'boundCount' => count($wechatData['wechatUsers']),
            ));        
        }
    }
    
    public function actionWechatUserList(){
        Yii::import('application.modules.weixin.models.*');
        $openid = isset($_POST['openid'])? $_POST['openid'] : null;
        
        $speUid = null;
        $wechatData = $this->getWechatUsers($openid, $speUid);
    
        if(Yii::app()->getRequest()->isAjaxRequest){
            if($_POST['actionType']=='userList'):
                $fetchData = array();
                if (isset($wechatData['wechatUsers']) && !is_null($wechatData['wechatUsers'])){
                    $openIds = array();
                    foreach($wechatData['wechatUsers'] as $wechatUser){
                        $openIds[] = $wechatUser->openid;
                    }
                    $wechatUserInfo = $this->getWechatUsersInfo($openIds);
                    foreach($wechatData['wechatUsers'] as $wechatUser){
                        $openIds[] = $wechatUser->openid;
                        if (isset($wechatUserInfo[$wechatUser->openid])){
                            $info = json_decode($wechatUserInfo[$wechatUser->openid]['info'], true);
                        } else {
                            $info = array('nickname'=>'', 'headimgurl'=>'', 'country'=>'', 'province'=>'', 'city'=>'');
                        }
                        $fetchData[] = array(
                            'openid' => $wechatUser->openid,
                            'boundto' => $wechatData['pids'][$wechatUser->userid],
                            'lastcontact' => $wechatUser->last_contact > 0 ? CommonUtils::formatDateTime($wechatUser->last_contact) : '',
                            'nickname' => $info['nickname'],
                            'headimgurl' => $info['headimgurl'],
                            'country' => $info['country'],
                            'province' => $info['province'],
                            'city' => $info['city'],
                        );  
                    }
                }
                echo CJSON::encode($fetchData);
            
            elseif($_POST['actionType']=='userDisable'):
                if($_POST['openid'] && Yii::app()->user->id == $_POST['userid']){
                    if (Yii::app()->user->checkAccess('adminChild', array("childid" => $this->childid))){
                        if(count($wechatData['wechatUsers']) && isset($wechatData['wechatUsers'][0])){
                            if(in_array($_POST['userid'], array_keys($wechatData['pids']))){
                                $wechatData['wechatUsers'][0]->valid = 0;
                                $wechatData['wechatUsers'][0]->disabled = sprintf('%d:%d', Yii::app()->user->id, time());
                                if( $wechatData['wechatUsers'][0]->save() ){
                                    echo CJSON::encode(array(
                                        'state'=>'success',
                                        'data'=>array(
                                            'openid' => $wechatData['wechatUsers'][0]->openid
                                        )
                                    ));
                                    Yii::app()->end();
                                }
                            }
                        }
                        echo CJSON::encode(array(
                            'state'=>'fail'
                        ));                        
                    }else{
                        echo CJSON::encode(array(
                            'state'=>'fail',
                            'message'=>Yii::t("message","Unauthorized operation.")
                        ));
                    }
                }
                Yii::app()->end();
            endif;
        }
        Yii::app()->end();
    }
    
    public function getWechatUsers($openid=null, $userId=null){
        $child_obj = $this->myChildObjs[$this->childid];
        $pids = array();
        $tmpPids = array();
        
        if($child_obj->fid) $tmpPids[$child_obj->fid] = 'f';
        if($child_obj->mid) $tmpPids[$child_obj->mid] = 'm';
        
        if(is_null($userId)){
            $pids = $tmpPids;
        }else{
            if(in_array($userId, array_keys($tmpPids))){
                $pids[$userId] = $tmpPids[$userId];
            }else{
                return array(
                    'pids' => null,
                    'wechatUsers' => null
                );
            }
        }

        $crit = new CDbCriteria;
        $crit->compare('valid', 1);
        if(!empty($openid)){
            $crit->compare('openid', $openid);
        }
        $crit->addInCondition('userid', array_keys($pids));
        $boundUsers = WechatUser::model()->findAll($crit);
        
        return array(
            'pids' => $pids,
            'wechatUsers' => $boundUsers
        );
    }

    public function getWechatUsersInfo($openids)
    {
        if (count($openids) == 0) {
            return array();
        }
        $openidStr = '';
        foreach($openids as $v){
            $openidStr .= "'{$v}',";
        }
        $openidStr = substr($openidStr, 0, -1);
        $sql = 'SELECT * FROM wechat_users_info WHERE openid IN (' . $openidStr . ')';
        $data = Yii::app()->db->createCommand($sql)->queryAll();
        $returnData = array();
        foreach($data as $v){
            $returnData[$v['openid']] = $v;
        }   
        return $returnData;
    }

    public function actionPtc()
    {
        $cs = Yii::app()->getClientScript();
        $cs->registerCoreScript('jquery.ui');
        Yii::import('common.models.ptc.*');
        Yii::import('common.models.calendar.Calendar');
        $childinfo = $this->getChildInfo();
        $calendarObj = Calendar::model()->findByPk($childinfo['yid']);
        $classObj = IvyClass::model()->findByPk($childinfo['classid']);
        $timepoints = explode(',', $calendarObj->timepoints);
        $t = time();
        $t1 = mktime(0,0,0,date('m')-2, date('d'),date('Y'));
        $semester = ($t1>$timepoints[0] && $t1<$timepoints[1]) ? 1 : 2;
        $criteria = new CDbCriteria();
        $criteria->compare('classid', $childinfo['classid']);
        $criteria->compare('semester', $semester);
        $criteria->compare('t.status', ParentMeetingPlan::STATUS_ONLINE);
        $plan = ParentMeetingPlan::model()->with('items')->find($criteria);
        $events = array();
        $editable = 1;
        if($plan != null){
            foreach($plan->items as $item){
                $target_timestamp = strtotime($item->target_date);
                $target_mon = date('Y-m', $target_timestamp);
                $target_date = date('Y-m-d', $target_timestamp);
                $timeslots = $item->exTimeslot();
                $title = '';
                $color = '#68BA00';
                $textColor = '';
                if($item->childid || $target_timestamp < $t){
                    $color = '#999999';
                    $title = Yii::t('support', 'Busy');
                    $textColor = '#fefefe';
                }
                if($item->childid==$this->getChildId()){
                    $editable = ($target_timestamp-$t)<172800 ? 0 : 1;
                    $title = $this->getChildName();
                    $color = '#3a87ad';
                    $textColor = '';
                }
                $events[$target_mon][] = array(
                    'id' => $item->uniKey(),
                    'title' => $title,
                    'start' => $target_date.'T'.$timeslots[0],
                    'end' => $target_date.'T'.$timeslots[1],
                    'color' => $color,
                    'textColor' => $textColor,
                    'className' => 'event',
                );
            }

            $criteria = new CDbCriteria();
            $criteria->compare('planid', $plan->id);
            $criteria->compare('childid', $this->getChildId());
            $memo = ParentMeetingItemMemo::model()->find($criteria);
        }
		if(empty($memo))
			$memo = new ParentMeetingItemMemo();

        $this->render('ptc', array(
            'events'=>$events,
            'memo'=>$memo->memo,
            'classObj'=>$classObj,
            'editable'=>$editable));
    }

    /**
     * TODO 保存预约信息
     * 如果有预约信息 判断修改预约时间小于48小时可以更新 否则提示错误
     * 孩子ID = $this->getChildId()
     * @return {state:x, message:x, unikey: x}
     */
    public function actionProcessPtc()
    {
        if(!Yii::app()->user->checkAccess('editProfile',array("childid"=>$this->getChildId()))){
            echo CJSON::encode(array(
                'state'=>'fail',
                'message'=>Yii::t("global", "Failed!")
            ));
            Yii::app()->end();
        }
		$unikey = Yii::app()->request->getParam('unikey', '');
		$memo = Yii::app()->request->getParam('memo', '');
		$childid = $this->getChildId();
		$arr = explode("_", $unikey);
		$planid = $arr[0];
		$days = $arr[1];
		$times = $arr[2];
		$meet_index = $arr[3];
		$op_timestamp = time();
		$twoday = $op_timestamp + 172800;
		$mon = date('Y-m', strtotime($days));
		$old_unikey = '';
		$editable = 1;
		Yii::import('common.models.ptc.*');
		$criteria = new CDbCriteria();
		$criteria->compare('childid', $childid);
		$criteria->compare('planid', $planid);
		$items = ParentMeetingItem::model()->find($criteria);
		$criter = new CDbCriteria();
		$criter->compare('planid', $planid);
		$criter->compare('target_date', $days);
		$criter->compare('timeslot', $times);
		$criter->compare('meet_index', $meet_index);
		$item = ParentMeetingItem::model()->find($criter);
		$criteria = new CDbCriteria();
		$criteria->compare('planid', $planid);
		$criteria->compare('childid', $childid);
		$itemmemo = ParentMeetingItemMemo::model()->find($criteria);
		if($itemmemo == null){
			$itemmemo = new ParentMeetingItemMemo;
		}
		if($twoday > strtotime($days)){
			$editable = 0;
		}
		$itemmemo->planid = $planid;
		$itemmemo->childid = $childid;
		$itemmemo->memo = $memo;
		if($op_timestamp < strtotime($days)){
			if($items){
				$mon_old = date('Y-m', strtotime($items->target_date));
				$target_date = strtotime($items->target_date);
				$old_unikey = $items->planid.'_'.$items->target_date.'_'.$items->timeslot . '_' . $items->meet_index;
				if($twoday > $target_date){
					echo CJSON::encode(array(
						'state' => 'fail',
						'message' => Yii::t("support", "Scheduled appointment cannot be modified online within 2 days of the reserved time slot; please contact the lead teacher directly to reschedule in this situation.")
					));
				}
				else{
					if($item->childid ==0){
						$items->childid = 0;
						$items->stem = 0;
						$item->childid = $childid;
						$item->stem = 1;
						$item->op_userid = Yii::app()->user->getId();
						$items->op_userid = 0;
						$items->op_timestamp = 0; 
						$item->op_timestamp = $op_timestamp;
						if($items->save()){
							if($itemmemo->save()){
								if($item->save()){
									echo CJSON::encode(array(
										'state'=>'success',
										'message'=>Yii::t("global", "Success!"),
										'data'=>array('mon' => $mon, 'mon_old' => $mon_old, 'uniKey' => $old_unikey, 'editable' => $editable)
									));
								}
								else{
									echo CJSON::encode(array(
										'state'=>'fail',
										'message'=>Yii::t("global", "Failed!")
									));	
								}
							}
						}
					}else{
						echo CJSON::encode(array(
							'state'=>'fail',
							'message'=>Yii::t("support", "Sorry this time slot has just been booked and is no longer available. Please select another time slot.")
						));	
					}
				}	
			}
			else{
				if($item->childid ==0){
					$item->childid = $childid;
					$itemmemo->memo = $memo;
					$item->op_userid = Yii::app()->user->getId();
					$item->op_timestamp = $op_timestamp;
					$item->stem = 1;
					if($item->save()){
						if($itemmemo->save()){
							echo CJSON::encode(array(
								'state'=>'success',
								'message'=>Yii::t("global", "Success!"),
								'data'=>array('mon' => $mon, 'uniKey' => $old_unikey, 'editable' => $editable)
							));
						}
					}
					else{
						echo CJSON::encode(array(
							'state'=>'fail',
							'message'=>Yii::t("global", "Failed!")
						));	
					}
				}
				else{
					echo CJSON::encode(array(
                        'state'=>'fail',
                        'message'=>Yii::t("support", "Sorry this time slot has just been booked and is no longer available. Please select another time slot.")
                    ));
				}
			}
		}
		else{
			echo CJSON::encode(array(
					'state'=>'fail',
					'message'=>Yii::t("support", "Time slot invalid")
				));	
		}
    }
}