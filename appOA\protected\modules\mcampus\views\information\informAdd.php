<?php 
$oss = CommonUtils::initOSS();
$baseUrl = Yii::app()->params['OAUploadBaseUrl'] . '/information/';
 ?>
<?php $form=$this->beginWidget('CActiveForm', array(
    'id'=>'visits-form',
    'enableAjaxValidation'=>false,
    'htmlOptions'=>array('class'=>'J_ajaxForm form-horizontal', 'role'=>'form'),
));


?>
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">×</span></button>
        <h4 class="modal-title"><?php echo $modalTitle;?></h4>
    </div>
    <div class="modal-body">
        <!-- 类型 -->
        <div class="form-group">
            <label class="col-xs-3 control-label"><?php echo Yii::t('labels','Type') ?></label>
            <div class="col-xs-9">
                <?php echo $form->dropDownList($model, 'cagegory', $Informationlist['category'], array('class' => 'form-control', 'empty' => Yii::t('global','Please Select'))); ?>
            </div>
        </div>

        <div class="form-group">
            <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'times'); ?></label>
            <div class="col-xs-9">
                <?php
                $this->widget('zii.widgets.jui.CJuiDatePicker', array(
                    "model" => $model,
                    "name" => "time",
                    "options" => array(
                        'changeMonth' => true,
                        'changeYear' => true,
                        'dateFormat' => 'yy-mm-dd',
                        'minDate' => 'd',
                    ),
                    'htmlOptions' => array(
                        'class' => 'form-control',
                        'placeholder' => Yii::t('currculim','Choose'),
                        'style' => 'width:100%'
                    ),
                ));
                ?>
            </div>
        </div>

        <!-- 英文标题 -->
        <div class="form-group">
            <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'title_en'); ?></label>
            <div class="col-xs-9">
                <?php echo $form->textField($model,'title_en',array('maxlength'=>255,'class'=>'form-control')); ?>
            </div>
        </div>
        <!-- 中文标题 -->
        <div class="form-group">
            <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'title_cn'); ?></label>
            <div class="col-xs-9">
                <?php echo $form->textField($model,'title_cn',array('maxlength'=>255,'class'=>'form-control')); ?>
            </div>
        </div>

        <div class="form-group">
            <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'file_cn'); ?></label>
            <div class="col-xs-9">
                <?php if($model->file_cn){
                    echo CHtml::link('文件链接', $baseUrl . $model->file_cn, array('target'=>'_blank'));
                } ?>
                <input  name="InformationDs[inform_file_cn]" class="new_Employee_Input" id="Staff_bankcard" type="file" />
            </div>
        </div>

        <div class="form-group">
            <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'file_en'); ?></label>
            <div class="col-xs-9">
                <?php if($model->file_en){
                    echo CHtml::link('文件链接', $baseUrl . $model->file_en, array('target'=>'_blank'));
                } ?>
                <input  name="InformationDs[inform_file_en]" class="new_Employee_Input" id="Staff_bankcard" type="file" />
            </div>
        </div>


        <div class="form-group">
            <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'published'); ?></label>
            <div class="col-xs-9">
                <?php echo $form->CheckBox($model,'published',array()); ?>
            </div>
        </div>

    </div>
    <div class="modal-footer">
        <button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global','Submit');?></button>
        <button type="button" class="btn btn-default"  data-dismiss="modal"><?php echo Yii::t('global','Cancel');?></button>
    </div>

<?php $this->endWidget(); ?>

