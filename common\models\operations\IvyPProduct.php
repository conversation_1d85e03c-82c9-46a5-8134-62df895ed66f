<?php

/**
 * This is the model class for table "ivy_p_product".
 *
 * The followings are the available columns in table 'ivy_p_product':
 * @property integer $id
 * @property string $title
 * @property string $spec
 * @property double $price
 * @property integer $account_code
 * @property string $category_id
 * @property integer $dept_id
 * @property integer $supplier_id
 * @property integer $p_period
 * @property string $image
 * @property integer $status
 * @property string $memo
 * @property integer $user_id
 * @property integer $create_timestamp
 * @property integer $update_timestamp
 */
class IvyPProduct extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @return IvyPProduct the static model class
	 */
	public $imageUrl = null;
	public $region = null;
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_p_product';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('title, price,dept_id,supplier_id,region,status', 'required'),
			array('account_code, dept_id, supplier_id,p_period, status, user_id, create_timestamp, update_timestamp', 'numerical', 'integerOnly'=>true),
			array('price', 'numerical'),
			array('image', 'file','allowEmpty'=>true,'maxSize'=>200000,'types' => 'jpg, gif, png, jpeg'),
			array('title, spec,image', 'length', 'max'=>255),
			array('category_id', 'length', 'max'=>10),
			array('memo', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, title, spec, price, account_code, category_id, dept_id, supplier_id, p_period, image, status, memo, user_id, create_timestamp, update_timestamp', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
			"Dept" => array(self::BELONGS_TO, 'HrDepartment', 'dept_id','select'=>'cn_name,en_name'),
			"Supplier" => array(self::BELONGS_TO, 'IvyVendor', 'supplier_id','select'=>'cn_title,en_title'),
			"Category" => array(self::BELONGS_TO, 'IvyPCategory', 'category_id','select'=>'title'),
			"Region" => array(self::HAS_MANY, 'IvyPRegionLink', 'product_id'),
		);
	}

	public function behaviors() {
        return array(
            'CTimestampBehavior' => array(
                'class' => 'zii.behaviors.CTimestampBehavior',
                'createAttribute' => 'create_timestamp',
                'updateAttribute' => 'update_timestamp',
            )
        );
    }
	
    
	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => Yii::t('operations', '标识'),
			'title' => Yii::t('operations', '标题'),
			'spec' => Yii::t('operations', '规格'),
			'price' => Yii::t('operations', '单价'),
			'image' => Yii::t('operations', '图片'),
			'region' => Yii::t('operations', '区域'),
			'account_code' => Yii::t('operations', '科目'),
			'category_id' => Yii::t('operations', '分类'),
			'dept_id' => Yii::t('operations', '执行部门'),
			'supplier_id' => Yii::t('operations', '供应商'),
			'p_period' => Yii::t('operations', '周期'),
			'status' => Yii::t('operations', '状态'),
			'memo' => Yii::t('operations', '备注'),
			'user_id' => Yii::t('operations', '创建用户'),
			'create_timestamp' => Yii::t('operations', '创建日期'),
			'update_timestamp' => Yii::t('operations', '更新日期'),
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;
		$criteria->compare('t.id',$this->id);
		$criteria->compare('t.title',$this->title,true);
		$criteria->compare('t.spec',$this->spec,true);
		$criteria->compare('t.price',$this->price);
		$criteria->compare('t.account_code',$this->account_code);
		$criteria->compare('t.category_id',$this->category_id,true);
		$criteria->compare('t.dept_id',$this->dept_id);
		$criteria->compare('t.supplier_id',$this->supplier_id);
		$criteria->compare('t.p_period',$this->p_period);
		$criteria->compare('t.image',$this->image,true);
		$criteria->compare('t.status',$this->status);
		$criteria->compare('t.memo',$this->memo,true);
		$criteria->compare('t.user_id',$this->user_id);
		$criteria->compare('t.create_timestamp',$this->create_timestamp);
		$criteria->compare('t.update_timestamp',$this->update_timestamp);
		$criteria->with = array('Dept','Supplier','Category');
		return new CActiveDataProvider(get_class($this), array(
			'criteria'=>$criteria,
		));
	}
	
 	/**
	 * 显示产品的图片
	 * @param int $status
	 * @return Array
	 * <AUTHOR>
	 * @copyright IvyGroup
	*/
	public function showPic() {
        $pic = '';
        if ($this->image) {
            $cfgs = OA::LoadConfig('CfgPhoto');
            $params = $cfgs["operations"];
            $pic = $params['uploadUrl'] . '/thumbs/' . $this->image;
            return CHtml::image($pic);
        }
        return null;
    }
    
    /**
	 * 显示产品的购买的部门 
	 * @param int $status
	 * @return Array
	 * <AUTHOR>
	 * @copyright IvyGroup
	*/
    public function showDept($status=0)
    {
    	$depList = HrDepartment::model()->getNameInfo($status);
    	if ($this->dept_id)
    	{
    		return $depList[$this->dept_id];
    	}
    	else 
    		return $depList;
    	
    }
    
    /**
	 * 显示产品的供应厂商
	 * @param int $status
	 * @return Array
	 * <AUTHOR>
	 * @copyright IvyGroup
	*/
    public function showSupplier($status=0)
    {
    	$venList = IvyVendor::model()->getVendorTitleInfo($status);
    	if ($this->supplier_id)
    	{
    		return $venList[$this->supplier_id];
    	}
    	else 
    		return $venList;
    }
    
    
    /**
	 * 显示产品的用友科目
	 * @param int $status
	 * @return Array
	 * <AUTHOR>
	 * @copyright IvyGroup
	*/
    public function showAccount($status=0)
    {
    	$accountList = IvyPAccount::model()->getNameInfo($status);
    	if ($this->account_code)
    	{
    		return $accountList[$this->account_code];
    	}
    	else 
    		return $accountList;
    }
    
    /**
	 * 显示产品的分类
	 * @param int $status
	 * @return Array
	 * <AUTHOR>
	 * @copyright IvyGroup
	*/
    public function showCategory($status=0)
    {
    	$categoryList = IvyPCategory::model()->categoryList($status);
    	if ($this->category_id)
    	{
    		return $categoryList[$this->category_id];
    	}
    	else
    		return $categoryList;
    }
    
	/**
	 * 显示类别的状态
	 * @return string
	 * <AUTHOR>
	 * @copyright IvyGroup
	*/
	public function showStatus()
	{
		return ($this->status) ? Yii::t('operations', '隐藏') : Yii::t('operations', '显示');
	}
	
	/**
	 * 根据产品的名称搜索产品
	 * @param string $title
	 * @return ArrayObject
	 * <AUTHOR>
	 * @copyright IvyGroup
	*/
	public function searchProduct($title)
	{
		$criteria = new CDbCriteria;
		$criteria->select = 'id,title';
		$criteria->compare('status', 0);
		$criteria->addSearchCondition('title', $title);
		return $this->findAll($criteria);
	}
}