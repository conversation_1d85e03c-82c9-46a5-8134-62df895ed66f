<table width="100%">
    <colgroup>
        <col width=240>
    </colgroup>
<?php if (!empty($model)):?>
<?php foreach ($model as $val):?>
    <tr>
        <th id="classid-<?php echo $val->childid; ?>">
            <?php 
            echo CHtml::link($val->getChildName(), array("/child/index/index", "childid"=>$val->childid), array("target"=>"_blank"));
            ?>
        </th>
        <td class="user_group" id="childlist-<?php echo $val->childid; ?>">
            <ul class="constant-list">
                <?php foreach ($val->invoices as $v):?>
                <li>
                    <span><?php echo CHtml::link($v->title, array("//child/invoice/viewInvoice", 'invoiceid'=>$v->invoice_id,'childid'=>$v->childid), array('target'=>'_blank'));?> ( <?php echo $v->amount;?> )</span>
                    <span style="margin-left: 100px;"><button class="btn mr10" type="button" onclick="operateInvoice(<?php echo$v->invoice_id;?>,<?php echo $v->childid;?>,'change');"><?php echo Yii::t('campus', '更改金额');?></button></span>
                    <span><button class="btn mr10" type="button" onclick="operateInvoice(<?php echo $v->invoice_id;?>,<?php echo $v->childid;?>,'again');"><?php echo Yii::t('campus', '补开学费');?></button></span>
                    <div id="subsidy_<?php echo $val->childid; ?>_<?php echo $v->invoice_id; ?>"></div>
                </li>
                <?php endforeach;?>
            </ul>
        </td>
    </tr>
    <tr>
        <td colspan="2">
            <div id="invoice-box-<?php echo $val->childid;; ?>"></div>
        </td>
    </tr>
<?php endforeach;?>
<?php else: ?>
     <tr>
        <td colspan="2">
            <div id="invoice-box-<?php echo $val->childid;; ?>">无数据</div>
        </td>
    </tr>
<?php endif;?>
</table>
