<?php
$this->breadcrumbs=array(
	Yii::t("navigations","Payment")=>array('/child/payment/summary'),
	Yii::t("navigations","Ivy Rewards Club"),
);?>
<?php
$colorbox = $this->widget('application.extensions.colorbox.JColorBox');
$colorbox->addInstance('.colorbox', array(
    'iframe' => false,
    'width' => '750',
    'height' => '300'
));
?>
<!-- <h1><label><?php echo Yii::t("payment", 'Ivy Points'); ?></label></h1>
<div class="credit-balance credit-tuition" style="height:120px;">
    <div class="span-6 info">
        <span class="title"><?php echo Yii::t("payment", "Current Balance"); ?></span>
        <span class="credit"><?php echo CHtml::link($points, array("/child/payment/pointsDetail"),array('class'=>'colorbox')); ?></span>
        <div class="span-12 last desc" style="height: 110px;"><?php echo Yii::t("payment", 'A kind reminder to please use your points to exchange gifts before December 31, 2020. After December 31st, the exchange of points for gifts will be terminated and the points will be cleared. The accumulated payment points that will expire on June 30th, 2020, and the fees paid after June 30th will no longer be included in the points. We have prepared gifts. You are welcome to exchange it in time. If you have any questions, please contact us. Thank you very much!'); ?></div>
        <em></em>
    </div>
</div>
<div class="clear"></div> -->
<?php if (time() < strtotime('20210101')) : ?>
<?php
	foreach(Yii::app()->user->getFlashes() as $key => $message) {
        echo '<div class="flash-' . $key . '">' . $message . "</div>\n";
    }
?>

<div class="flash-info2">
    <ol class="number">
        <li><h5><?php echo Yii::t("payment", "Procedure for Gift Exchange");?></h5></li>
        <li><?php echo Yii::t("payment", "1. Orders can only be cancelled on the same day that it was made.  It cannot be cancelled after that.");?></li>
        <li><?php echo Yii::t("payment", "2. Exchanged gifts will be delivered to each campus from our distribution center once every month.");?></li>
        <li><?php echo Yii::t("payment", "3. Office staff will provide the gift to parents as soon as it arrives on campus.");?></li>
    </ol>
</div>

<div class="span-12">
	<div class="p-box" id="product-list-wrapper">
	<h3><?php echo Yii::t("payment", 'Gifts Available'); ?></h3>
		<?php
		$viewData['colorbox'] = $colorbox;
		$this->widget('zii.widgets.CListView', array(
			'dataProvider'=>$productList,
			'itemView'=>'_points_list',
			'id'=>'product-list',
			'itemsCssClass'=>'items',
			'viewData'=>$viewData,
			'ajaxUpdate'=>false,
			'template'=>'{items}{pager}',
			'pager' => array(
				'class' => 'CLinkPager',
				'cssFile' => Yii::app()->theme->baseUrl . '/css/pager.css',
			),
		));
		?>
	</div>
</div>

<div class="span-6 last">
	<div class="p-box orders">
		<h3><?php echo Yii::t("payment", 'Exchange History'); ?></h3>
		<?php
		if (!empty($orderList->data)):
			$this->widget('zii.widgets.CListView', array(
				'dataProvider'=>$orderList,
				'itemView'=>'_order_list',
				'id'=>'product-list',
				'itemsCssClass'=>'items',
				'viewData'=>$viewData,
				'ajaxUpdate'=>false,
				'template'=>'{items}',
				'pager' => array(
					'class' => 'CLinkPager',
					'cssFile' => Yii::app()->theme->baseUrl . '/css/pager.css',
				),
			));
		?>
		
		<?php else: ?>

			<?php echo Yii::t("payment", 'No gifts ordered yet.'); ?>

		<?php endif; ?>
	</div>
</div>
<?php endif; ?>
<script type="text/javascript">
    $('.e-exch').on('click', function(){
        var id = $(this).attr('id');
        var numberEle = $('#orderNumber_'+id);
        number = numberEle.val();
        if(parseInt(number)!=number || number < 1){
            alert("<?php echo Yii::t('payment', 'Please input correct number, thanks!');?>");
            numberEle.select();
            return false;
        }
        if ( confirm("<?php echo Yii::t("payment", "Confirm the order?")?>") ){
            $("#e_"+id).html("<img src='<?php echo Yii::app()->theme->baseUrl;?>/images/loading.gif'>");
            $.post("<?php echo $this->createUrl("/child/payment/exchange")?>", {id:id,number:number}, function(data){
                if (data.state === 'success'){
                    location.reload();
                }
                else {
                    if (data.message != ''){alert(data.message)}
                    $("#e_"+id).html('<input class="e-exch" id="'+id+'" name="orderBtn'+id+'" type="button" value="<?php echo Yii::t('payment', 'Order')?>">');
                }
            }, 'json')
        }
    });
    $('.o-order').on('click', function(){
        if ( confirm("<?php echo Yii::t("payment", "Confirm to cancel the order?")?>") ){
            var id = $(this).attr('id');
            $("#o_"+id).html("<img src='<?php echo Yii::app()->theme->baseUrl;?>/images/loading.gif'>");
            $.post("<?php echo $this->createUrl("/child/payment/cancelOrder")?>", {id:id}, function(data){
                if (data.state === 'success'){
                    location.reload();
                }
                else {
                    if (data.message != ''){alert(data.message)}
                    $("#o_"+id).html('<input class="o-order" id="'+id+'" type="button" value="<?php echo Yii::t('payment', 'Cancel the order')?>">');
                }
            }, 'json')
        }
    });
</script>