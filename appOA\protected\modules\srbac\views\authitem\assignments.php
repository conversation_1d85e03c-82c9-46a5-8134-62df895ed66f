<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site','System Management'), array('/msettings'))?></li>
        <li class="active">系统权限</li>
    </ol>
    <div class="row panel-group">
        <div class="col-md-12">
            <?php
            $this->renderPartial('_menu');
            ?>
        </div>
        <?php if($this->module->getMessage() != ""){ ?>
            <div id="srbacError">
                <?php echo $this->module->getMessage();?>
            </div>
        <?php } ?>

    </div>

    <div class="row">
        <?php echo SHtml::beginForm($this->createUrl('assign', array('assignRoles'=>1)), 'post', array('class'=>'J_ajaxForm')); ?>
        <div class="col-md-2">
            <div class="">
                <?php
                $userRows = Yii::app()->db->createCommand()->select('id,schoolid,tdata')->from('fts_staff')->where('active=1')->order('tdata asc')->queryAll();
                foreach($userRows as $_row){
                    $userList[$_row['schoolid']][] = array('id'=>$_row['id'],'tdata'=>$_row['tdata']);
                }
                $branchList = $this->getAllBranch();

                echo SHtml::activeDropDownList( User::model(), 'uid',
                    null,
                    array('size'=>$this->module->listBoxNumberOfLines,'class'=>'form-control mb5','onchange' => 'getUserDetail(this)',
                    )); ?>
                <div id="branch-filter"></div>
            </div>
        </div>
        <div class="col-md-10">
            <div id="assignments">
                <!--placeholder-->
            </div>
        </div>
        <?php echo SHtml::endForm(); ?>
    </div>

</div>

<script>
    var staffList;
    var branchTotal =0;
    var branchList = <?php echo CJSON::encode($branchList);?>;
    $(function() {
        staffList = <?php echo CJSON::encode($userList);?>;
        for(var _i in staffList){
            for(var i=0; i<staffList[_i].length; i++){
                $('#User_uid')
                    .append($('<option>', {value : staffList[_i][i].id }).text(staffList[_i][i].tdata));
            }
            if (branchList[_i]) {
                $('#branch-filter').append($('<a class="btn btn-default btn-sm mb5 mr5"></a>').attr('branch',_i).attr('href','javascript:void(0)').html(branchList[_i]['abb']+' ('+staffList[_i].length+')'));
                branchTotal++;
            }
        };
        if(branchTotal<=1){
            $('#branch-filter').hide();
        }else{
            $('#branch-filter a').click(function(){
                var _i = $(this).attr('branch');
                $('#User_uid option').remove();
                for(var i=0; i<staffList[_i].length; i++){
                    $('#User_uid')
                        .append($('<option>', {value : staffList[_i][i].id }).text(staffList[_i][i].tdata));
                };
                $('#branch-filter a').removeClass('btn-primary').addClass('btn-default');
                $(this).removeClass('btn-default').addClass('btn-primary');
            });
        }
    });

    function callback(data)
    {
        getUserDetail(document.getElementById('User_uid'));
    }

    function getUserDetail(_this)
    {
        jQuery.ajax({'type':'POST','url':'/srbac/authitem/showAssignments','beforeSend':function(){
            $("#assignments").addClass("srbacLoading");
        },'complete':function(){
            $("#assignments").removeClass("srbacLoading");
            head.Util.ajaxForm();
        },data:jQuery(_this).parents("form").serialize(),success:function(html){jQuery("#assignments").html(html)}});return false;
    }
</script>

