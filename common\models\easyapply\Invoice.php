<?php

/**
 * This is the model class for table "ea_invoice".
 *
 * The followings are the available columns in table 'ea_invoice':
 * @property integer $id
 * @property integer $family_id
 * @property integer $school_id
 * @property integer $child_id
 * @property integer $parent_id
 * @property integer $apply_id
 * @property integer $discount
 * @property double $nodiscount_amount
 * @property double $amount_original
 * @property double $amount_actual_pay
 * @property string $invoice_title
 * @property string $memo
 * @property integer $paytype
 * @property integer $pay_timestamp
 * @property integer $status
 * @property integer $created_at
 * @property integer $created_by
 * @property integer $updated_at
 * @property integer $updated_by
 */
class Invoice extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ea_invoice';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('family_id, child_id, apply_id, nodiscount_amount, amount_original, invoice_title, status, created_at, created_by, updated_at, updated_by', 'required'),
			array('family_id, school_id, child_id, parent_id, apply_id, discount, paytype, pay_timestamp,status, created_at, created_by, updated_at, updated_by', 'numerical', 'integerOnly'=>true),
			array('nodiscount_amount, amount_original, amount_actual_pay', 'numerical'),
			array('invoice_title, memo', 'length', 'max'=>255),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, family_id, school_id, child_id, parent_id, apply_id, discount, nodiscount_amount, amount_original, amount_actual_pay, invoice_title, memo, paytype, pay_timestamp,status, created_at, created_by, updated_at, updated_by', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'family_id' => 'Family',
			'school_id' => 'School',
			'child_id' => 'Child',
			'parent_id' => 'Parent',
			'apply_id' => 'Apply',
			'discount' => 'Discount',
			'nodiscount_amount' => 'Nodiscount Amount',
			'amount_original' => 'Amount Original',
			'amount_actual_pay' => 'Amount Actual Pay',
			'invoice_title' => 'Invoice Title',
			'memo' => 'Memo',
			'paytype' => 'Paytype',
			'pay_timestamp' => 'pay_timestamp',
			'status' => 'Status',
			'created_at' => 'Created At',
			'created_by' => 'Created By',
			'updated_at' => 'Updated At',
			'updated_by' => 'Updated By',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('family_id',$this->family_id);
		$criteria->compare('school_id',$this->school_id);
		$criteria->compare('child_id',$this->child_id);
		$criteria->compare('parent_id',$this->parent_id);
		$criteria->compare('apply_id',$this->apply_id);
		$criteria->compare('discount',$this->discount);
		$criteria->compare('nodiscount_amount',$this->nodiscount_amount);
		$criteria->compare('amount_original',$this->amount_original);
		$criteria->compare('amount_actual_pay',$this->amount_actual_pay);
		$criteria->compare('invoice_title',$this->invoice_title,true);
		$criteria->compare('memo',$this->memo,true);
		$criteria->compare('paytype',$this->paytype);
		$criteria->compare('status',$this->status);
		$criteria->compare('created_at',$this->created_at);
		$criteria->compare('created_by',$this->created_by);
		$criteria->compare('updated_at',$this->updated_at);
		$criteria->compare('updated_by',$this->updated_by);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->mmxxcxdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return Invoice the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}
