<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo Yii::t('site','User Center');?></li>
        <li>休假及加班</li>
        <li class="active">休假及加班审核列表</li>
    </ol>
    <div class="row">
        <div class="col-md-12">
            <?php echo CHtml::form('', 'post',array('id'=>'check-form','class'=>'J_ajaxForm form-horizontal'));?>
                <table class="table">
                    <thead>
                        <tr>
                            <th width="250">员工信息</th>
                            <th width="200"><label><?php echo CHtml::checkBox('all',null,array('id'=>'checkall'));?><span class="text">全选</span></label></th>
                            <th width="200">Type</th>
                            <th width="200">Date</th>
                            <th width="150">Period</th>
                            <th width="250">Desc</th>
                            <th width="150">Created</th>
                            <th></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (count($checkList)):?>
                        <?php foreach($checkList as $userVal):?>
                            <?php
                            $i=0;
                            foreach($userVal['list'] as $val):
                                $id = $val['id'];
                            ?>
                            <tr>
                                <?php if ($i==0):?>
                                <th rowspan="<?php echo count($userVal['list']);?>">
                                    <?php echo CHtml::image(OA::CreateOAUploadUrl('users', $userVal['img']),'',array("class"=>"img-rounded","width"=>"30%"));?>
                                    <?php echo $userVal['name'];?>
                                </th>
                                <?php endif;?>
                                <td id="check-item-input-<?php echo $id ;?>">
                                    <?php echo CHtml::checkBox("id[]",null,array('value'=>$id,'class'=>'input-check J_check col-sm-2'));?>
                                </td>
                                <td><?php echo $val['typeTxt'];?></td>
                                <td><?php echo $val['dateTxt'];?></td>
                                <td><?php echo $val['hoursTxt'];?></td>
                                <td><?php echo $val['desc'];?></td>
                                <td><?php echo $val['created'];?></td>
                                <td id="check-item-<?php echo $id ;?>">
                                    <a data-agree='yes' data-id="<?php echo $val['id'];?>" class="btn btn-success btn-xs mr5" title="同意" href="javascript:void(0);" onclick="check(this);"><span class="glyphicon glyphicon-ok"></span></a>
                                    <a data-agree='no' data-id="<?php echo $val['id'];?>" class="btn btn-danger btn-xs mr5" title="不同意" href="javascript:void(0);" onclick="check(this);"><span class="glyphicon glyphicon-trash"></span></a>
                                    <a data-msg="您确定要代申请吗？" class="J_ajax_del btn btn-success btn-xs" title="代申请" href="<?php echo $this->createUrl("//user/leaveot/delete",array('id'=>$id,'command'=>'invalid'));?>"><span class="glyphicon glyphicon-plus"></span></a>
                                </td>
                            </tr>
                            <?php
                            $i++;
                            endforeach;
                            ?>
                        <?php endforeach;?>
                            <tr>
                                <td colspan="8">
                                    <button type="button" class="btn btn-primary J_ajax_submit_btn" data-subcheck="1" data-msg="您确认同意吗？" data-action="<?php echo $this->createUrl('/user/leaveot/check',array('agree'=>'yes'));?>">同意</button>
                                    <button type="button" class="btn btn-danger J_ajax_submit_btn" data-subcheck="1" data-msg="您确认不同意吗？" data-action="<?php echo $this->createUrl('/user/leaveot/check',array('agree'=>'no'));?>">不同意</button>
                                </td>
                            </tr>
                        <?php else:?>
                            <tr>
                                <td colspan="7" class="empty">
                                    <span class="empty">没有找到数据.</span>
                                </td>
                            </tr>
                        <?php endif;?>
                    </tbody>
                </table>
            <?php echo CHtml::endForm();?>
        </div>
    </div>
</div>
<script type="text/template" id="staff-check">
    <div>
        <p><%if (type == 'yes'){%>您确认同意吗？<%}else{%>您确认不同意吗？<%}%></p>
        <p>
            <?php echo CHtml::textArea('LeaveItem[check_desc]', '', array('class' => 'form-control', 'placeholder' => '描述','rows'=>3,'cols'=>40,'maxlength'=>200));?>
        </p>
    </div>
</script>

<script type="text/template" id="add-td">
    <div class="col-sm-10">
        <textarea class="form-control" name="desc[<%=id%>]" id="desc_<%=id%>" placeholder="审核备注"></textarea>
    </div>
</script>


<script>
var checkTemplate =  _.template($('#staff-check').html());
var addTd =  _.template($('#add-td').html());
function check(obj){
    head.load('<?php echo Yii::app()->themeManager->baseUrl;?>/base/js/dialog/dialog.js',function() {
        var agree = $(obj).data('agree');
        var id = $(obj).data('id');
        var params = {
            follow:$(obj),
            zIndex:10000,
            message:checkTemplate({type:$(obj).data('agree')}),
            type:'confirm',
            onOk:function(){
                var desc = $("#LeaveItem_check_desc").val();
                $.post("<?php echo $this->createUrl('//user/leaveot/check');?>",{agree:agree,id:id,desc:desc}, function(data){
                    if(data.state === 'success') {
                        resultTip({msg: data.message, callback: function(){
                            if(data.refresh === true){
                                if(data.referer) {
                                    location.href = data.referer;
                                }else {
                                    reloadPage(window);
                                }
                            }
                        }});
                    }else if( data.state === 'fail' ) {
                        head.dialog.alert(data.message);
                    }
                    if (typeof(data.callback) !== 'undefined'){
                        eval(data.callback+'(data.data)');
                    }
                }, 'json');
            }
        };
        head.dialog(params);
    })
}

$(function(){
    var total_check_all = $("#check-form").find('input.input-check');
    $("#checkall").click(
        function(){
            if(this.checked){
                $("input[name='id[]']").each(function(){
                    this.checked = true;
                    addTdTxt(this,this.value);
                });
            }else{
                $("input[name='id[]']").each(function(){
                    this.checked = false;
                    removeTdTxt(this);
                });
            }
        }
    )

    addTdTxt = function(_this,data){
        if ($("#desc_"+_this.value).length<=0){
            $(_this).parent().append(addTd({id:data}));
        }
    }

    removeTdTxt = function(_this){
        if ($("#desc_"+_this.value).length){
            $("#desc_"+_this.value).remove();
        }
    }

    $('#check-form').on('click', 'input.input-check',function(e){
        var total_check_all = $("#check-form").find('input.input-check');
        if(this.checked){
            addTdTxt(this,this.value);
            if(total_check_all.filter(':checked').length === total_check_all.length) {
                //已选择和未选择的复选框数相等
                $("#checkall").attr('checked', true);
            }
        }else{
            removeTdTxt(this);
            $("#checkall").removeAttr('checked');
        }
    })

    checkCallback = function(data){
        _.each(data,function(v,k){
            $("#check-item-input-"+v).html('');
            $("#check-item-"+v).html('已审核');
        })
    }
})
</script>