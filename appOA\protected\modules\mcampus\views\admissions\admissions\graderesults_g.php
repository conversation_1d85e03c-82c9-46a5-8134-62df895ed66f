<?php
$form = $this->beginWidget('CActiveForm', array(
    'id' => 'child_interivew',
    'enableAjaxValidation' => false,
    'action' => $this->createUrl('interviewResults', array('childid' => $child->id)),
    'htmlOptions' => array('class' => 'J_ajaxForm', 'role' => 'form'),
));
$gender = array(1=>'Boy', 2=> 'Girl');
Yii::import('common.models.visit.*');
$grade = AdmissionsDs::getConfig();
$data = array();
$countries = Country::model()->getCountryList('','','en');
$diglossia = Diglossia::model()->getLanguage('true');

if($childinterview->discipline_stat){
    $data = json_decode($childinterview->discipline_stat,true);
    $child->interviewDate = date("Y-m-d", $data['interviewDate']);
}


$schoolHistoryArr = array();
if($child->school_history){
    $school_history = json_decode($child->school_history,true);
    foreach ($school_history as $val){
        if($val['school']){
            $schoolHistoryArr[] = $val['school'];
        }
    }
    if($schoolHistoryArr){
        $schoolHistoryArr = implode(',',$schoolHistoryArr);
    }
}

?>

<table class="table table-bordered">
    <h3 class="text-center"><?php echo (isset($calender) && isset($calender[$child->start_year])) ? $calender[$child->start_year] : '' ; ?> Admissions Assessment Check List ( ELC & G1 )</h3><br/>
</table>
<div class="col-md-12">
    <table class="table table-bordered">
        <tr>
            <td>Student Behavior: </td>
            <td><?php echo CHTml::radioButtonList('interview[studentInterview]', ($data && $data['studentInterview']) ? $data['studentInterview'] : '', array(1=>'A',2=>'B',3=>'C',4=>'D'), array('separator'=>'', 'template'=>'<div class="col-md-2">{input} {label}</div>')) ?></td>
            <td rowspan="3">
                <div>Student Interviewer: </div>
                <?php echo CHtml::textField("interview[studentAcademic]", ($data) ? $data['studentAcademic'] : "", array('class' => "form-control")) ?>
                <br>
                <br>
                <div>Parents Interviewer: </div>
                <?php echo CHtml::textField("interview[parentsInterviews]", ($data) ? $data['parentsInterviews'] : "", array('class' => "form-control")) ?>
            </td>
        </tr>
        <tr>
            <td><?php echo Yii::t('user', 'Student Academic') ?>:</td>
            <td><?php echo CHTml::radioButtonList('interview[academic]', ($data && $data['academic']) ? $data['academic'] : '', array(1=>'A',2=>'B',3=>'C',4=>'D'), array('separator'=>'', 'template'=>'<div class="col-md-2">{input} {label}</div>')) ?></td>
        </tr>
        <tr>
            <td><?php echo Yii::t('user', 'Parents Interview') ?>:</td>
            <td><?php echo CHTml::radioButtonList('interview[parentsInterview]', ($data && $data['parentsInterview']) ? $data['parentsInterview'] : '', array(1=>'A',2=>'B',3=>'C',4=>'D'), array('separator'=>'', 'template'=>'<div class="col-md-2">{input} {label}</div>')) ?></td>
        </tr>
        <tr>
            <td>Note:</td>
            <td colspan="2">
                A: <?php echo Yii::t('user','Outstanding'); ?>
                B: <?php echo Yii::t('user','Above Average'); ?>
                C: <?php echo Yii::t('user','Average'); ?>
                D: <?php echo Yii::t('user','Below Average'); ?>
            </td>
        </tr>
    </table>
</div>

<div class="col-md-12">
    <h3>Student Information</h3>
    <table class="table table-bordered">
        <tr>
            <td>English Name: <?php echo $child->en_name_last . ' ' .$child->en_name_middle . ' ' .$child->en_name?></td>
            <td>Chinese Name: <?php echo $child->cn_name . ' ' .$child->cn_name_last ?></td>
        </tr>
        <tr>
            <td>Nationality: <?php echo $countries[$child->nationality] ?></td>
            <td>Mother Language: <?php echo $diglossia[$child->native_lang] ?></td>
        </tr>
        <tr>
            <td>Gender: <?php echo $gender[$child->gender] ?></td>
            <td>DOB: <?php echo date("Y-m-d", $child->birthday) ?></td>
        </tr>
        <tr>
            <td>Starting Year & Grade: <?php echo $grade['grade_en'][$child->start_grade] ?></td>
            <td>Previous School: <?php echo ($schoolHistoryArr) ? $schoolHistoryArr : '' ?></td>
        </tr>
        <tr>
            <td>Father:
                <?php echo ($child->father_name) ? $child->father_name . '，' : '' ; ?>
                <?php echo ($child->father_education) ? $grade['education'][$child->father_education]  . '，' : '' ; ?>
                <?php echo ($child->father_position) ? $child->father_position  . '，' : '' ; ?>
                <?php echo ($child->father_employer) ? $child->father_employer : '' ; ?>，
            </td>
            <td>
                Mother:
                <?php echo ($child->mother_name) ? $child->mother_name  . '，' : '' ?>
                <?php echo ($child->mother_education) ? $grade['education'][$child->mother_education]  . '，' : '' ?>
                <?php echo ($child->mother_position) ? $child->mother_position  . '，' : '' ?>
                <?php echo ($child->mother_employer) ? $child->mother_employer : '' ?>
            </td>
        </tr>
        <tr>
            <td>
            <div class="form-inline">
                  <span>Siblings at Daystar: </span>
                   <?php echo CHTml::dropDownList('interview[siblingsDaystar]', ($data) ? $data['schoolReport'] : 2, array( 1 => 'Yes', 2 => 'No' ), array('class'=>'form-control')) ?>
                </div>

            </td>
            <td>Staff Child: <?php echo ($child->is_staff == 1) ? "Yes" : "No" ?></td>
        </tr>
    </table>
</div>
<div  class="col-md-12">
    <h3><?php echo Yii::t('user','Other Materials') ?></h3>
    <table class="table table-bordered">
        <tr>
            <td width="200"><?php echo Yii::t('user', '学籍 (Xueji)') ?>:</td>
            <td>
                <?php echo CHtml::textField("interview[xueji]", ($data) ? $data['xueji'] : "", array('class' => "form-control")) ?>
            </td>
            <td  width="200">
                <?php echo CHtml::textField("interview[xueji2]", ($data) ? $data['xueji2'] : "", array('class' => "form-control")) ?>
            </td>
        </tr>
        <tr>
            <td width="200">School Report:</td>
            <td>
                <?php echo CHtml::textField("interview[schoolReport]", ($data) ? $data['schoolReport'] : "", array('class' => "form-control")) ?>
            </td>
            <td>
                <?php echo CHtml::textField("interview[schoolReport2]", ($data) ? $data['schoolReport2'] : "", array('class' => "form-control")) ?>
            </td>
        </tr>
        <tr>
            <td width="200">Recommendation Letter:</td>
            <td><?php echo CHtml::textField("interview[letter]", ($data) ? $data['letter'] : "", array('class' => "form-control")) ?></td>
            <td><?php echo CHtml::textField("interview[letter2]", ($data) ? $data['letter2'] : "", array('class' => "form-control")) ?></td>
        </tr>
    </table>
</div>

<div  class="col-md-12">
   
    <h3>Interview Date:
        <?php echo ($childinterview) ? date("Y-m-d", $childinterview->interview_time) : "未分配面试" ?>
    </h3>
     <h3>Person in Charge:</h3>
    <table class="table table-bordered">
        <tr>
            <td width="200">Department:</td>
            <td>Accept:</td>
            <td>Reject:</td>
            <td>Signature &Date:</td>
            <td>Note:</td>
        </tr>
        <tr>
            <td width="200">Admissions Director:</td>
            <td>
                <?php echo CHtml::textField("interview[director]", ($data) ? $data['director'] : "", array('class' => "form-control")) ?>
            </td>
            <td>
                <?php echo CHtml::textField("interview[director2]", ($data) ? $data['director2'] : "", array('class' => "form-control")) ?>
            </td>
            <td>
                <?php echo CHtml::textField("interview[director3]", ($data) ? $data['director3'] : "", array('class' => "form-control")) ?>
            </td>
            <td>
                <?php echo CHtml::textField("interview[director4]", ($data) ? $data['director4'] : "", array('class' => "form-control")) ?>
            </td>
        </tr>
        <tr>
            <td width="200">Principal:</td>
            <td>
                <?php echo CHtml::textField("interview[principal]", ($data) ? $data['principal'] : "", array('class' => "form-control")) ?>
            </td>
            <td>
                <?php echo CHtml::textField("interview[principal2]", ($data) ? $data['principal2'] : "", array('class' => "form-control")) ?>
            </td>
            <td>
                <?php echo CHtml::textField("interview[principal3]", ($data) ? $data['principal3'] : "", array('class' => "form-control")) ?>
            </td>
            <td>
                <?php echo CHtml::textField("interview[principal4]", ($data) ? $data['principal4'] : "", array('class' => "form-control")) ?>
            </td>
        </tr>
    </table>
</div>
<?php
if (Yii::app()->user->checkAccess('o_A_Adm_Visits') && $child->status != AdmissionsDs::STATS__TRANSFER_CHILD && $status == 2): ?>
    <div class="form-group col-md-12"><?php echo Yii::t('interview', 'Whether to pass the interview') ?>:
        <label for="exampleInputEmail1">
            <?php echo CHtml::radioButtonList('stat', $child->status, array('41' => Yii::t('user', 'No'), '40' => Yii::t('user', 'Yes'), '42' => Yii::t('user', '候补')), array('separator' => '&nbsp;&nbsp;')); ?>
    </div>
<?php endif; ?>
<?php if($status == 2): ?>
    <div class="clearfix"> </div>
<div class="modal-footer">
    <button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global', 'Submit') ?></button>
    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('global', 'Cancel') ?></button>
</div>
<?php endif; ?>
<?php $this->endWidget(); ?>