<div>
    <div class='flex align-items mb24 mt10'>
        <span class='font14 color3'>处理人：</span>
        <div class='flex1 flex align-items lib_class_handle'>
        </div>
    </div>
    <div class="balance_items">   
        <p class='itemReturn'>
            <span class='notAllowed'>
                <label class="radio-inline font14 color6 lableHeight eventsNone">
                    <input type="radio" name="balance" value="1"> <?php echo Yii::t('withdrawal','Fully Reimbursed');?>
                </label>
            </span>
            <span class='notAllowed'>
                <label class="radio-inline font14 color6 lableHeight eventsNone ml24">
                    <input type="radio" name="balance" value="0"> <?php echo Yii::t('withdrawal','Compensation Required');?>
                </label>
            </span>
        </p>  
        <textarea class="form-control mb24" rows="3" placeholder="<?php echo Yii::t('withdrawal','Comment');?>" disabled name="remark" id='remark'  class='font14'></textarea>
        <table  class="table tableCss">
            <thead>
                <tr style='background:#FAFAFA' class='font14 color3'>
                    <td>物品名称</td>
                    <td>赔偿金额</td>
                    <td><?php echo Yii::t('withdrawal','Comment');?></td>
                </tr>
            </thead>
            <tbody class='tbody'>
            </tbody>
        </table>
        <div class='text-right font14 color3  mt20'><span class='submit'></span>  <span class='cur-p ml10 redColor canRevoke' onclick='cancelSubmit()'>撤销</span></div>
    </div>
</div>

<script>
     lib()
     $(function () {
        $('[data-toggle="tooltip"]').tooltip()
    })
     function lib(){
        $('[data-toggle="popover"]').popover()
        let forms=childDetails.forms.find(item2 =>item2.key === 'lib_class')
        $('#type').val(forms.key)
        if(forms.reject_memo!=''){
            $('.reject').show()
            $('.rejectMemo').html(forms.reject_memo)
        }
        if(forms.data.remark!=''){
            $('#remark').val(forms.data.remark);
        }
        if(forms.data.balance){
            $('input[type="radio"][name="balance"][value='+forms.data.balance+']').prop('checked', true);
            if(forms.data.balance=='0'){
                $('.table').show()
                for(let i=0;i<forms.data.goods.length;i++){
                    $('.tbody').append('<tr class="font14 color3"><td> <input type="text" class="form-control" disabled value="'+forms.data.goods[i]["name"]+'"></td><td><input type="text" class="form-control" disabled value='+forms.data.goods[i].amount+'></td><td><input type="text" class="form-control" disabled value="'+forms.data.goods[i].memo+'"></td></tr>');
                }
            }else{
                $('.table').hide()
            }
        }else{
            $('.table').hide()
        }
        if(forms.canRevoke){
            $('.canRevoke').show()
            $('.submit').html('由 '+childDetails.staffInfo[forms.implementer].name+' <?php echo Yii::t('global','Submit');?>')
        }else{
            $('.canRevoke').hide()
            $('.submit').html()
        }
        for(var key in forms.owner_info){
            if (key !== '2' && key !== '5') {
                if(forms.implementer==key && forms.data._id){
                    $('.lib_class_handle').append('<div class="flex align-items mr16 relative"><img src='+forms.owner_info[key].photoUrl+' alt="" class="avatar38"/><div class="ml8"><div class="font14 color3 ">'+forms.owner_info[key].name+'</div><div class="font12 color6">处理时间：'+forms.implementAt+'</div></div></div>')
                }else if(!forms.data._id){
                    $('.lib_class_handle').append('<div class="flex align-items mr16"><img src='+forms.owner_info[key].photoUrl+' alt="" class="img28"/><div class="font14 color3 ml8">'+forms.owner_info[key].name+'</div></div>')
                }
            }
        }
    }
    function cancelSubmit(){
        container.cancelSubmitData('lib_class')
    }
</script>
