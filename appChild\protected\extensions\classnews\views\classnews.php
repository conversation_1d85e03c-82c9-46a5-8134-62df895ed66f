<?php if ($CNotesModel->en_content){?>
<div class="journal-box">
    <h3><?php echo Yii::t("portfolio", 'Class Notes');?></h3>
    <?php if($Teachers):
        echo CHtml::openTag("ul", array("class"=>"teachers"));
        foreach($Teachers as $teacher){
            echo CHtml::openTag("li", array('title'=>$teacher->userWithProfile->getName()));
            $staff_photo = is_object($teacher->staffInfo) ? $teacher->staffInfo->staff_photo : 'blank.jpg';
            echo CHtml::image(Mims::CreateUploadUrl('infopub/staff/'.$staff_photo), '', array("class"=>"face"));
            echo CHtml::openTag("div",array("class"=>"hover"));
            echo $teacher->userWithProfile->getName();
            echo CHtml::closeTag("div");
            echo CHtml::closeTag("li");
        }
        echo CHtml::closeTag("ul");
    endif;?>
    <div class="clear"></div>
    <div class="seperator"></div>
    <script>
        document.write( normalText(<?php echo CJSON::encode($CNotesModel->en_content); ?>) );
    </script>
</div>
<?php }?>