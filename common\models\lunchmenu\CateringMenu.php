<?php

/**
 * This is the model class for table "ivy_catering_menu".
 *
 * The followings are the available columns in table 'ivy_catering_menu':
 * @property integer $id
 * @property string $title_cn
 * @property string $title_en
 * @property integer $city_id
 * @property string $schoolid
 * @property integer $yid
 * @property integer $vendor_id
 * @property string $nutrition
 * @property integer $is_allergy
 * @property integer $school_mgt
 * @property integer $weight
 * @property string $week_cate
 * @property string $menu_cate
 * @property string $memo
 * @property integer $status
 * @property integer $update_user
 * @property integer $update_timestamp
 */
class CateringMenu extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CateringMenu the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_catering_menu';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('title_cn,schoolid, yid', 'required'),
			array('city_id, yid, vendor_id, is_allergy, school_mgt, weight, status, update_user, update_timestamp', 'numerical', 'integerOnly'=>true),
			array('title_cn, title_en, week_cate, menu_cate', 'length', 'max'=>255),
			array('schoolid', 'length', 'max'=>25),
			array('nutrition, memo', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, title_cn, title_en, city_id, schoolid, yid, vendor_id, nutrition, is_allergy, school_mgt, weight, menu_cate, memo, status, update_user, update_timestamp', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
			'dailyMenus' => array(self::HAS_MANY, 'CateringMenuDetail', array('menu_id'=>'id'),'with'=>'termInfo'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
            'title_cn' => Yii::t('lunch', 'Title'),
			'title_en' => Yii::t('lunch', 'Title'),
			'city_id' => Yii::t('lunch', 'City'),
			'schoolid' => Yii::t('lunch', 'School'),
			'yid' => 'Yid',
			'vendor_id' => Yii::t('lunch', 'Supplier'),
			'nutrition' => Yii::t('lunch', 'Nutrition'),
			'is_allergy' => Yii::t('lunch', 'Special Requirements'),
			'school_mgt' => Yii::t('lunch', 'School Management'),
			'weight' => Yii::t('lunch', 'Serial Number'),
			'week_cate' => Yii::t('lunch', 'week_cate'),
			'menu_cate' => Yii::t('lunch', 'Meals Included'),
			'memo' => Yii::t('lunch', 'Memo'),
			'status' => Yii::t('lunch', 'Cease'),
			'update_user' => 'Update User',
			'update_timestamp' => 'Update Timestamp',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('title_cn',$this->title_cn,true);
		$criteria->compare('title_en',$this->title_en,true);
		$criteria->compare('city_id',$this->city_id);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('yid',$this->yid);
		$criteria->compare('vendor_id',$this->vendor_id);
		$criteria->compare('nutrition',$this->nutrition,true);
		$criteria->compare('is_allergy',$this->is_allergy);
		$criteria->compare('school_mgt',$this->school_mgt);
		$criteria->compare('weight',$this->weight);
		$criteria->compare('week_cate',$this->week_cate,true);
		$criteria->compare('menu_cate',$this->menu_cate,true);
		$criteria->compare('memo',$this->memo,true);
		$criteria->compare('status',$this->status);
		$criteria->compare('update_user',$this->update_user);
		$criteria->compare('update_timestamp',$this->update_timestamp);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

    public static function getWeekDayName()
    {
        $getWeekDayName = array(
            'mon' => Yii::t('labels','Mon'),
            'tue' => Yii::t('labels','Tue'),
            'wed' => Yii::t('labels','Wed'),
            'thu' => Yii::t('labels','Thu'),
            'fri' => Yii::t('labels','Fri'),
            'sat' => Yii::t('labels','Sat'),
            'sun' => Yii::t('labels','Sun'),
        );
        return $getWeekDayName;
    }
}