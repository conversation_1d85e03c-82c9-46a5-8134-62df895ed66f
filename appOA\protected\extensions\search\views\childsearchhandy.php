<?php
    if($this->restrictSchool){
        $sourceUrl = Yii::app()->createUrl('//backend/childSearchByCampus/childoutput', array('displayType'=>$this->simpleDisplay ? 'simpleDisplay' : '', 'withAlumni'=>'withAlumni', 'branchId'=>$this->restrictSchool));
    }
    else{
        $sourceUrl = Yii::app()->createUrl('//backend/searchOverall/childoutput', array('displayType'=>$this->simpleDisplay ? 'simpleDisplay' : '', 'withAlumni'=>'withAlumni'));
    }
    $sourceUrl2 = Yii::app()->createUrl('//backend/childSearchByCampus/childoutput', array('displayType'=>$this->simpleDisplay ? 'simpleDisplay' : '', 'withAlumni'=>'withAlumni', 'branchId'=>$this->restrictSchool, 'multiple' => 0));
?>
<script>

var checked = false;

function handleSchoolCheckbox(obj){
    if($(obj).is(':checked')){
        $('#searchChild').autocomplete('option', 'source', '<?php echo $sourceUrl2; ?>');
        checked = true;
    } else {
        $('#searchChild').autocomplete('option', 'source', '<?php echo $sourceUrl; ?>');
        checked = false;
    }
    $('#searchChild').autocomplete('search');
}
function nl2br (str, is_xhtml) {
	var breakTag = (is_xhtml || typeof is_xhtml === 'undefined') ? '<br />' : '<br>';     
	return (str + '').replace(/([^>\r\n]?)(\r\n|\n\r|\r|\n)/g, '$1'+ breakTag +'$2'); 
}
function fly(t, id)
{
    var url;
    switch(t)
    {
        case 'c':
            url = '<?php echo Yii::app()->createUrl('/child/index/index')?>?childid='+id;
        break;
        case 'f':
            url = '<?php echo Yii::app()->createUrl('/child/index/cCUrl')?>?childid='+id;
        break;
    }
    window.open(url);
}
function closePop(){
	jQuery('#'+'<?php echo $this->name;?>').val(""); 
	jQuery('#'+'<?php echo $this->name;?>').data( 'autocomplete' ).term=""; 
	jQuery('#'+'<?php echo $this->name;?>').data( 'autocomplete' ).close(); 
}
</script>
<?php if($this->displayLabel):?>
    <label for="<?php echo $this->name;?>" class="mr5">
        <?php echo $this->displayLabel;?>
    </label>
<?php endif;?>
<?php
$hint = Yii::t("message", "Search by child ID, name, DOB, parent name, email, Phone NO.");
if($this->restrictSchool){
    $sourceUrl = Yii::app()->createUrl('//backend/childSearchByCampus/childoutput', array('displayType'=>$this->simpleDisplay ? 'simpleDisplay' : '', 'withAlumni'=>'withAlumni', 'branchId'=>$this->restrictSchool));
}
else{
    $sourceUrl = Yii::app()->createUrl('//backend/searchOverall/childoutput', array('displayType'=>$this->simpleDisplay ? 'simpleDisplay' : '', 'withAlumni'=>'withAlumni'));
}
$this->widget('zii.widgets.jui.CJuiAutoComplete',array(
    'name'=>$this->name,
	'sourceUrl'=>$sourceUrl,
    'options'=>array(
        'minLength'=>'1',
		'delay'=>1000,
		'position'=> $this->popPosition,
		'select'=>'js:function(event,ui){return false;}',
    ),
    'htmlOptions'=>array(
		'class'=>$this->inputCSS,
		'placeholder' => $hint,
		'title' => $hint,
        'size' => '80'
    ),
));


$js = "jQuery('#".$this->name."')";
$js .= ".data( 'autocomplete' )._renderItem = function( ul, item ) {
			if(item.value==-1){
				return $( '<li onclick=\'closePop();\' class=\'close\'>×</li><li class=\'panel panel-default\'><div class=\'panel-heading\'>".$this->abbHtml.Yii::t('message', 'Result')."</div><div class=\'panel-body\'><div id=\'res-list\' class=\'list-group\'></div></div></li>' ).appendTo(ul);
			}else if(item.value==-2){
				return $( '<li></li>' ).addClass('full').data('item.autocomplete', item).append( '<div>' + item.label + '</div>' ).appendTo(ul);
			}else{
				var re = new RegExp( '(' + $.ui.autocomplete.escapeRegex(this.term) + ')', 'gi' );
				var highlightedResult = item.label.replace( re, '<b>$1</b>' );
				highlightedResult = nl2br(highlightedResult);
				var extra1 = (item.value==0) ? '' : '<button class=\'btn btn-default btn-xs mb5\' onclick=\"fly(\'c\','+item.value+')\"> ".Yii::t('message', 'Profile Detail')." </button>';
				var extra2 = (item.value==0) ? '' : '<button class=\'btn btn-default btn-xs\' onclick=\"fly(\'f\','+item.value+')\"> ".Yii::t('message', 'Front End')." </button>';
				return $( '<a class=\'list-group-item\'></a>' )
					.attr('cid',item.value)
					.data( 'item.autocomplete', item )
					.append( '<div class=\'col-sm-9\'>' + highlightedResult + '</div>' + '<div class=\'col-sm-3 text-right\'>' + extra1 + extra2 + '</div><div class=\'clearfix\'></div>')
					.appendTo( ul.find('#res-list') );
			}
        };";
$js .= "jQuery('#".$this->name."')";
$js .=".data('autocomplete').close = function(event){
			if($.trim(this.term)!=''){
				return false;
			}else{
				if ( this.menu.element.is( ':visible' ) ) {
					this.menu.element.hide();
					this.menu.blur();
					this.isNewMenu = true;
					this._trigger( 'close', event );
				}
			}
		};";
$js .= "jQuery('#".$this->name."')";
$js .=".data('autocomplete')._resizeMenu = function(){
            if (checked) {
                $('#schoolCheckbox').prop('checked', true);
            }

			var ul = this.menu.element;
			ul.outerWidth(600);
		};";
$js .= "jQuery('#".$this->name."')";
$js .=".data('autocomplete')._move = function( event ){
			return false;
		};";
$cs = Yii::app()->getClientScript();
$cs->registerScript(__CLASS__.'#'.$this->name, $js);
?>

<style>
    #res-list{
        max-height: 500px;
        overflow-y: auto;
    }
    #res-list .ui-menu{
        padding: 2px 0;
    }
    #res-list .ui-widget-content{
        border: none;
        font-family: "Microsoft Yahei";
    }
    #res-list .close{
        line-height: 34px;
        margin-right: 5px;
    }
    #res-list .list-group-item span{
        color: #3c763d;
        font-weight: bold;
    }
    .list-group-item.active, .list-group-item.active:hover, .list-group-item.active:focus{
        z-index: 0;
    }
</style>