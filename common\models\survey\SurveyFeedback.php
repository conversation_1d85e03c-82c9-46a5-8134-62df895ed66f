<?php

/**
 * This is the model class for table "ivy_survey_feedback".
 *
 * The followings are the available columns in table 'ivy_survey_feedback':
 * @property integer $id
 * @property integer $survey_id
 * @property integer $classid
 * @property integer $childid
 * @property string $schoolid
 * @property integer $is_fb
 * @property integer $followup
 * @property integer $open2school
 * @property integer $status
 * @property integer $fb_user
 * @property integer $fb_time
 * @property integer $update_user
 * @property integer $update_time
 */
class SurveyFeedback extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @return SurveyFeedback the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_survey_feedback';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('survey_id, classid, childid, is_fb, followup, open2school, status, fb_user, fb_time, update_user, update_time', 'numerical', 'integerOnly'=>true),
			array('schoolid', 'length', 'max'=>255),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, survey_id, classid, childid, schoolid, is_fb, followup, open2school, status, fb_user, fb_time, update_user, update_time', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'ivyclass' => array(self::BELONGS_TO, 'IvyClass', 'classid'),
            'child' => array(self::BELONGS_TO, 'ChildProfileBasic', 'childid'),
            'survey' => array(self::BELONGS_TO, 'Survey', array('survey_id' => 'id')),
		);
	}
	
	
	/**
	 * 检查 某孩子是否已经回复过某问卷了
	 * @param int $survey_id 问卷ID
	 * @param int $childid 孩子ID
	 * @return string
	 * <AUTHOR>
	 * @time 2012-6-26
	 */
	public function isFeedback($survey_id = 0,$childid = 0){
		
		if(empty($survey_id) || empty($childid)){
			return "inexistence";
		}
		
		$fb_cri = new CDbCriteria();
		$fb_cri->compare('survey_id', $survey_id);
		$fb_cri->compare('childid', $childid);
		$fb_cri->compare('status', 0);
		$fb = $this->find($fb_cri);
		
		if(empty($fb)){
			return "inexistence";
		}else{
			if($fb->is_fb == 1){
				return "has_fb";
			}
		}
		
		return "not_fb";
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'survey_id' => 'Survey',
			'classid' => 'Classid',
			'childid' => 'Childid',
			'schoolid' => 'Schoolid',
			'is_fb' => 'Is Fb',
			'followup' => 'Followup',
			'open2school' => 'Open2school',
			'status' => 'Status',
			'fb_user' => 'Fb User',
			'fb_time' => 'Fb Time',
			'update_user' => 'Update User',
			'update_time' => 'Update Time',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('survey_id',$this->survey_id);
		$criteria->compare('classid',$this->classid);
		$criteria->compare('childid',$this->childid);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('is_fb',$this->is_fb);
		$criteria->compare('followup',$this->followup);
		$criteria->compare('open2school',$this->open2school);
		$criteria->compare('status',$this->status);
		$criteria->compare('fb_user',$this->fb_user);
		$criteria->compare('fb_time',$this->fb_time);
		$criteria->compare('update_user',$this->update_user);
		$criteria->compare('update_time',$this->update_time);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}