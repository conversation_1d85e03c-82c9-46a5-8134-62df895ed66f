<?php

/**
 * VisitorAccess class.
 * VisitorAccess is the data structure for keeping
 * visitor access child info 
 */
class VisitorAccessForm extends CFormModel
{
	public $accesskey;
	public $passcode;

	/**
	 * Declares the validation rules.
	 * The rules state that username and password are required,
	 * and password needs to be authenticated.
	 */
	public function rules()
	{
		return array(
			// accesskey and passcode are required
			array('accesskey, passcode', 'required'),

			// password needs to be authenticated
			array('passcode', 'authenticate'),
		);
	}

	/**
	 * Declares attribute labels.
	 */
	public function attributeLabels()
	{
		return array(
			'accesskey'=>"accessLink",
			'passcode'=>Yii::t("user", "Access Code"),
		);
	}

	/**
	 * Authenticates the password.
	 * This is the 'authenticate' validator as declared in rules().
	 */
	public function authenticate($attribute,$params)
	{
		if(!$this->hasErrors())  // we only want to authenticate when no input errors
		{
			$identity=new VisitorIdentity($this->accesskey,$this->passcode);
			$identity->authenticate();
			switch($identity->errorCode)
			{
				case VisitorIdentity::ERROR_NONE:
					Yii::app()->user->authVisit($identity->getChildId());
					break;
				case VisitorIdentity::ERROR_ACCESSKEY:
					$this->addError("accesskey",Yii::t("user", "Invalid access link"));
					break;
				case VisitorIdentity::ERROR_WRONG_PASSCODE:
					$this->addError("passcode",Yii::t("user", "Access key is incorrect."));
					break;
			}
		}
	}
}
