<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
    <h4 class="modal-title" id="exampleModalLabel"><?php echo Yii::t('asa','跨校园分享'); ?></h4>
</div>
<?php $form=$this->beginWidget('CActiveForm', array(
    'id'=>'courseGroup-form',
    'enableAjaxValidation'=>false,
    'htmlOptions'=>array('class'=>'J_ajaxForm form-horizontal', 'role'=>'form'),
)); ?>

<div class="modal-body">
    <div class="form-group">
        <div class="col-xs-12">
            <label class="col-xs-2 control-label"><?php echo Yii::t('site','Campuses') ?></label>
            <div class="col-xs-10">
                <?php echo CHtml::HiddenField("Products[cid]", $cid); ?>
                <?php echo CHtml::checkBoxList("schoolId", $checkAll, $allBranch,array('maxlength'=>255, 'template' => "<div class='col-xs-3'>{input}{label}</div>","separator"=>"")); ?>
            </div>
        </div>
        <?php if(!empty($allBranch) && !in_array($this->branchId,array('BJ_DS','BJ_QFF','BJ_SLT'))){?>
            <div class="col-xs-12">
                <label class="col-xs-2 control-label" ><?php echo Yii::t('site','共享库存') ?></label>
                <div class="col-xs-8" style="padding-top: 7px;padding-left: 11px">
                    <?php echo $form->radioButtonList($share_stock, 'share_stock', array(
                        1 => Yii::t('reg', 'Yes'),
                        0 => Yii::t('reg', 'No'),
                    ), array(
                        'template' => '<label class="col-xs-2">{input} {label}</label>',
                        'separator' => '',
                        'class'=>'col-xs-3',
                        'labelOptions'=> array('class' => 'form-check-label' ),
                    ))?>

                </div>
            </div>
            <div class="col-xs-12">
                <label class="col-xs-2 control-label"><?php echo Yii::t('site','共享推荐') ?></label>
                <div class="col-xs-8" style="padding-top: 7px;padding-left: 11px">
                    <?php echo $form->radioButtonList($share_featured, 'share_featured', array(
                        1 => Yii::t('reg', 'Yes'),
                        0 => Yii::t('reg', 'No'),
                    ), array(
                        'id' => 'share_featured_radio',
                        'disabled'=>1 != $share_featured->share_featured && 1 != $share_featured->share_stock,
                        'template' => '<label class="col-xs-2">{input} {label}</label>',
                        'separator' => '',
                        'class'=>'col-xs-3',
                        'title'=>'设置共享库存后可设置共享推荐',
                        'labelOptions'=> array('class' => 'form-check-label' ),
                    ))?>
                </div>
            </div>
        <?php }?>
    </div>
</div>
<div class="modal-footer">
    <button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global','Submit');?></button>
    <button type="button" class="btn btn-default"  data-dismiss="modal"><?php echo Yii::t('global','Cancel');?></button>
</div>

<?php $this->endWidget(); ?>
<script>
    var optionA = document.getElementsByName("ProductsCatShare[share_stock]");
    var optionB = document.getElementsByName("ProductsCatShare[share_featured]");
    // 添加事件监听器
    for (var i = 0; i < optionA.length; i++) {
        optionA[i].addEventListener("change", function() {
            if (this.value === "1") {
                enableOptionB();
            } else {
                disableOptionB();
            }
        });
    }

    function cbSchoolshare() {
        $('#modal').modal('hide');
        window.location.reload(true);
    }
    function check_share_stock()
    {
        //设置共享库存后可设置共享推荐
        var checkboxA = document.getElementById('ProductsCatShare_share_stock');
        var checkboxB = document.getElementById('ProductsCatShare_share_featured');
        if (checkboxA.checked) {
            checkboxB.disabled = false;
        } else {
            checkboxB.disabled = true;
            checkboxB.checked = false;
        }
    }

    // 启用选项 B
    function enableOptionB() {
        for (var i = 0; i < optionB.length; i++) {
            optionB[i].disabled = false;
        }
    }

    // 禁用选项 B
    function disableOptionB() {
        for (var i = 0; i < optionB.length; i++) {
            optionB[i].disabled = true;
            optionB[i].checked = false;
        }
    }
</script>