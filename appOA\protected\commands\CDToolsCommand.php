<?php
class CDToolsCommand extends CConsoleCommand{
    public $data1;
    public $data2;
    public $data3;
    public $data4;
    public $data5;
    public $data6;
    public $data7;
    public $data8;
    public $data9;
    public $data10;
    public $data11;
    public $data12;
    public $data13;
    public $data14;
    public $data15;
    public $data16;
    public $data17;

    public $inClass;
    public $inClassChild;

    public function actionIndex(){
		echo $this->getHelp();
	}

    public function actionStats($schoolid='')
    {
        Yii::import('common.models.child.StatsChildCount');

        $today = strtotime('today');

        $sql = "select branchid, abb, schcalendar from ivy_branch where type in (20,50) and status=10";
        if($schoolid){
            $sql .= " and branchid='".$schoolid."'";
        }
        $items = Yii::app()->db->createCommand($sql)->queryAll();
        foreach($items as $item){
            $this->data1 = array();
            $this->data2 = array();
            $this->data3 = array();
            $this->data4 = array();
            $this->data5 = array();
            $this->data6 = array();
            $this->data7 = array();
            $this->data8 = array();
            $this->data9 = array();
            $this->data10 = array();
            $this->data11 = array();
            $this->data12 = array();
            $this->data13 = array();
            $this->data14 = array();
            $this->data15 = array();
            $this->data16 = array();
            $this->data17 = array();

            $sql = "select classid from ivy_class_list where schoolid='".$item['branchid']."' and yid=".$item['schcalendar'];
            $classes = Yii::app()->db->createCommand($sql)->queryAll();

            $total1 = array();
            $total2 = array();
            $total3 = array();
            $total4 = array();
            $total5 = array();
            $total9 = array();
            $total13 = array();
            $total14 = array();
            $total15 = array();
            $total16 = array();

            $this->getNum3_4_5($item);
            $this->getNum1_2_13_14($item);
            $this->getNum15_16($item);
            $this->getNum9($item);
            $this->getNum10_11($item);
            $this->getNum6_7_8($item);
            $this->getNum12($item);

            foreach($classes as $cla){
                $classid = $cla['classid'];
                $criteria = new CDbCriteria();
                $criteria->compare('schoolid', $item['branchid']);
                $criteria->compare('yid', $item['schcalendar']);
                $criteria->compare('classid', $classid);
                $criteria->compare('period_timestamp', $today);
                $model = StatsChildCount::model()->find($criteria);
                if($model == null)
                    $model = new StatsChildCount();
                $model->schoolid = $item['branchid'];
                $model->classid = $classid;
                $model->yid = $item['schcalendar'];
                $model->period_timestamp = $today;

                for($i=1; $i<17; $i++){
                    $num = 'num'.$i;
                    $data = 'data'.$i;
                    $childids = 'childids'.$i;

                    $dataData = $this->$data;

                    $model->$num = isset($dataData[$classid]) ? count($dataData[$classid]) : 0;
                    $model->$childids = (isset($dataData[$classid]) && is_array($dataData[$classid])) ? implode(',', $dataData[$classid]) : '';
                }

                $_cids1 = isset($this->data1[$classid]) ? $this->data1[$classid] : array();
                $_cids2 = isset($this->data2[$classid]) ? $this->data2[$classid] : array();
                $_cids3 = isset($this->data3[$classid]) ? $this->data3[$classid] : array();
                $_cids4 = isset($this->data4[$classid]) ? $this->data4[$classid] : array();
                $_cids5 = isset($this->data5[$classid]) ? $this->data5[$classid] : array();
                $_cids9 = isset($this->data9[$classid]) ? $this->data9[$classid] : array();

                $total1 += $_cids1;
                $total2 += $_cids2;
                $total3 += $_cids3;
                $total4 += $_cids4;
                $total5 += $_cids5;
                $total9 += $_cids9;
                $total13 += isset($this->data13[$classid]) ? $this->data13[$classid] : array();
                $total14 += isset($this->data14[$classid]) ? $this->data14[$classid] : array();
                $total15 += isset($this->data15[$classid]) ? $this->data15[$classid] : array();
                $total16 += isset($this->data16[$classid]) ? $this->data16[$classid] : array();

                if(!$model->save()){
                    print_r($model->getErrors());
                }

                $classModel = IvyClass::model()->findByPk($classid);
                $fill_ratio = $classModel->capacity ? sprintf("%0.2f",($model->num1+$model->num2)/$classModel->capacity*100) : '';
                $classModel->fill_ratio = $fill_ratio;
                $classModel->child_total = $model->num1+$model->num2+$model->num3+$model->num4+$model->num5+$model->num9;
                $classModel->child_inclass = $model->num1+$model->num2;
                $classModel->child_waiting = $model->num3+$model->num4;

                $genders = array();
                $allchildids = $_cids1+$_cids2+$_cids3+$_cids4+$_cids5+$_cids9;
                if($allchildids){
                    $sql = "SELECT gender,count(*) as count FROM ivy_child_profile_basic WHERE childid in (".implode(',', $allchildids).") group by gender";
                    $genders = Yii::app()->db->createCommand($sql)->queryAll();
                    if($genders){
                        foreach($genders as $key=>$gend){
                            if($gend['gender'] == 1){
                                $classModel->child_boys = $gend['count'];
                            }
                            else{
                                $classModel->child_girls = $gend['count'];
                            }
                        }
                    }
                }

                $classModel->save(false);
            }

            $criteria = new CDbCriteria();
            $criteria->compare('schoolid', $item['branchid']);
            $criteria->compare('yid', $item['schcalendar']);
            $criteria->compare('classid', 0);
            $criteria->compare('period_timestamp', $today);
            $model = StatsChildCount::model()->find($criteria);
            if($model == null)
                $model = new StatsChildCount();
            $model->schoolid = $item['branchid'];
            $model->classid = 0;
            $model->yid = $item['schcalendar'];
            $model->period_timestamp = $today;

            $model->num1 = count($total1);
            $model->childids1 = implode(',', $total1);

            $model->num2 = count($total2);
            $model->childids2 = implode(',', $total2);

            $model->num3 = count($total3);
            $model->childids3 = implode(',', $total3);

            $model->num4 = count($total4);
            $model->childids4 = implode(',', $total4);

            $model->num5 = count($total5);
            $model->childids5 = implode(',', $total5);

            $model->num6 = $this->data6 ? count($this->data6) : 0;
            $model->childids6 = $this->data6 ? implode(',', $this->data6) : '';

            $model->num7 = $this->data7 ? count($this->data7) : 0;
            $model->childids7 = $this->data7 ? implode(',', $this->data7) : '';

            $model->num8 = $this->data8 ? count($this->data8) : '';
            $model->childids8 = $this->data8 ? implode(',', $this->data8) : '';

            $model->num9 = count($total9);
            $model->childids9 = implode(',', $total9);

            $model->num10 = $this->data10 ? count($this->data10) : 0;
            $model->childids10 = $this->data10 ? implode(',', $this->data10) : '';

            $model->num11 = $this->data11 ? count($this->data11) : 0;
            $model->childids11 = $this->data11 ? implode(',', $this->data11) : '';

            $model->num12 = $this->data12 ? count($this->data12) : 0;
            $model->childids12 = $this->data12 ? implode(',', $this->data12) : 0;

            $model->num13 = count($total13);
            $model->childids13 = implode(',', $total13);

            $model->num14 = count($total14);
            $model->childids14 = implode(',', $total14);

            $model->num15 = count($total15);
            $model->childids15 = implode(',', $total15);

            $model->num16 = count($total16);
            $model->childids16 = implode(',', $total16);

            $model->num17 = $this->data17 ? count($this->data17) : 0;
            $model->childids17 = $this->data17 ? implode(',', $this->data17) : '';

            if(!$model->save()){
                print_r($model->getErrors());
            }
            echo $item['branchid']."\n";
        }
    }

    public function getNum1_2_13_14($school)
    {
        $num1 = array(); #已付费
        $num2 = array(); #未付费
        $num13 = array(); #已退费
        $num14 = array(); #退费中
        $t = strtotime('today');

        $day = strtolower(date('D'));

        $sql = "select classid,childid,mon,tue,wed,thu,fri from ivy_child_service_info where schoolid='".$school['branchid']."' and payment_type='tuition'
        and yid=".$school['schcalendar']." and startdate <=".$t." and enddate>=".$t."";
        $services = Yii::app()->db->createCommand($sql)->queryAll();
        foreach($services as $service){
//            if(in_array($service[$day], array(10,20))){
                $num1[$service['childid']] = $service['childid'];
//            }
        }

        $sql = "select invoice_id, classid, childid, status from ivy_invoice_invoice where payment_type = 'tuition' and schoolid='".$school['branchid']."'
        and calendar_id=".$school['schcalendar']." and status in (10, 20, 30, 40, 77) and startdate <=".$t." and
        enddate>=".$t." and `inout`='in'";
        $items = Yii::app()->db->createCommand($sql)->queryAll();
        foreach($items as $item){
            if($item['status'] == 20){
//                $num1[$item['classid']][$item['childid']] = $item['childid'];
            }
            else{
                $num2[$item['childid']] = $item['childid'];
            }
            $invoiceids[$item['invoice_id']] = $item['invoice_id'];
        }

        $sql = "select * from ivy_invoice_child_refund where payment_type='tuition' and schoolid='".$school['branchid']."'
        and startdate <=".$t." and enddate>=".$t." and status != 99";
        $items = Yii::app()->db->createCommand($sql)->queryAll();
        foreach($items as $item){
            if($item['status'] == 20){
                $num13[$item['childid']] = $item['childid'];
            }
            else{
                $num14[$item['childid']] = $item['childid'];
            }
        }

//        foreach($num1 as $classid=>$num){
//            $tmp = $num1[$classid];
//            $tmp13 = isset($num13[$classid]) ? $num13[$classid] : array();
//            $tmp14 = isset($num14[$classid]) ? $num14[$classid] : array();
//            $num1[$classid] = array_diff($tmp, $tmp13, $tmp14);
//        }

        $num1 = array_diff($num1, $num2);
        foreach ($this->inClassChild as $classid=>$children) {
            foreach ($children as $child) {
                if (in_array($child, $num1)) {
                    $this->data1[$classid][$child] = $child;
                }
                if (in_array($child, $num2)) {
                    $this->data2[$classid][$child] = $child;
                }
                if (in_array($child, $num13)) {
                    $this->data13[$classid][$child] = $child;
                }
                if (in_array($child, $num14)) {
                    $this->data14[$classid][$child] = $child;
                }
            }
        }

        return array('num1'=>$this->data1, 'num2'=>$this->data2, 'num13'=>$this->data13, 'num14'=>$this->data14);
    }

    public function getNum3_4_5($school)
    {
        $num3 = array();
        $num4 = array();
        $num5 = array();

        $sql = "select childid, classid from ivy_child_school_class where schoolid='".$school['branchid']."' and calendar=".$school['schcalendar']." and stat in (10, 20)";
        $items = Yii::app()->db->createCommand($sql)->queryAll();
        $classChild = array();
        $childids = array();
        foreach($items as $item){
            $classChild[$item['classid']][$item['childid']] = $item['childid'];
            $childids[$item['childid']] = $item['childid'];
        }
        $this->inClass = $childids;
        $this->inClassChild = $classChild;

        $invoices = array();
        if($childids){
            $sql = "select payment_type,status,childid,classid from ivy_invoice_invoice where payment_type in ('deposit', 'tuition') and childid in (".implode(',', $childids).")
            and schoolid='".$school['branchid']."' and calendar_id=".$school['schcalendar']." and status != 99";
            $invoices = Yii::app()->db->createCommand($sql)->queryAll();
        }

        $depositPay = array();
        $depositUnpay = array();
        $childtuition = array();
        $_childids = array();
        foreach($invoices as $invoice){
            if($invoice['payment_type'] == 'deposit'){
                if($invoice['status'] == 20){
                    $depositUnpay[$invoice['childid']] = $invoice['childid'];
                }
                elseif(in_array($invoice['status'], array(10,30,40,77))){
                    $depositPay[$invoice['childid']] = $invoice['childid'];
                }
            }
            elseif($invoice['payment_type'] == 'tuition'){
                $childtuition[$invoice['childid']] = $invoice['childid'];
            }
            $_childids[$invoice['childid']] = $invoice['childid'];
        }

        foreach($classChild as $classid=>$claObj){
            foreach($claObj as $childid){
                if (in_array($childid, $depositUnpay) && !in_array($childid, $childtuition)){
                    $num3[$classid][$childid] = $childid;
                }

                if (in_array($childid, $depositPay) && !in_array($childid, $childtuition)){
                    $num4[$classid][$childid] = $childid;
                }

                if (!in_array($childid, $_childids)){
                    $num5[$classid][$childid] = $childid;
                }
            }
        }

        $this->data3 = $num3;
        $this->data4 = $num4;
        $this->data5 = $num5;

        return array('num3'=>$num3, 'num4'=>$num4, 'num5'=>$num5);
    }

    public function getNum9($school)
    {
        $t = strtotime('today');
        $invoices = array();
        if ($this->inClass) {
            $sql = "select childid,classid from ivy_invoice_invoice where payment_type = 'tuition' and status!=88 and status!=99 and `inout`='in'
            and childid in (".implode(',', $this->inClass).") and schoolid='".$school['branchid']."' and calendar_id=".$school['schcalendar']." and (enddate<".$t." or startdate > ".$t.")";
            $invoices = Yii::app()->db->createCommand($sql)->queryAll();
        }
        $num9 = array();
        foreach($invoices as $invoice){
            $num9[$invoice['classid']][$invoice['childid']] = $invoice['childid'];
        }

        $d1 = array();
        $d2 = array();
        $d15 = array();
        $d16 = array();

        foreach($this->data1 as $n1){
            $d1 += $n1;
        }
        foreach($this->data2 as $n2){
            $d2 += $n2;
        }

        foreach($this->data15 as $n15){
            $d15 += $n15;
        }
        foreach($this->data16 as $n16){
            $d16 += $n16;
        }

        foreach($num9 as $classid=>$num){
            $this->data9[$classid] = array_diff($num, $d1, $d2, $d15, $d16);
        }

        return $this->data9;
    }

    public function getNum15_16($school)
    {
        $num15 = array();
        $num16 = array();

        $t = strtotime('today');
        $y4 = strtotime('-4year');

//        $invoiced = array();
//        $sql = "select distinct(childid) from ivy_invoice_invoice where payment_type = 'tuition' and status!=88 and status!=99 and `inout`='in'
//        and schoolid='".$school['branchid']."' and enddate < ".$t." and startdate>".$y4;
//        $invoices = Yii::app()->db->createCommand($sql)->queryAll();
//        foreach($invoices as $invoice){
//            $invoiced[$invoice['childid']] = $invoice['childid'];
//        }

        $invoiceids = array();
        $sql = "select invoice_id,childid,classid,status from ivy_invoice_invoice where payment_type = 'tuition' and status!=88 and status!=99 and `inout`='in'
        and schoolid='".$school['branchid']."' and calendar_id=".$school['schcalendar']." and startdate > ".$t;
        $invoices = Yii::app()->db->createCommand($sql)->queryAll();
        foreach($invoices as $invoice){
            if($invoice['status'] == 20){
                $invoiceids[$invoice['invoice_id']] = $invoice;
            }
            else{
//                if(in_array($invoice['childid'], $this->inClass) && !in_array($invoice['childid'], $invoiced)){
                if(in_array($invoice['childid'], $this->inClass)){
                    $num16[$invoice['classid']][$invoice['childid']] = $invoice['childid'];
                }
            }
        }

        $refundChild = array();
        if($invoiceids){
            $sql = "select on_invoice_id from ivy_invoice_child_refund where status!=99 and on_invoice_id in (".implode(',', array_keys($invoiceids)).") and refund_type !=41";
            $refunds = Yii::app()->db->createCommand($sql)->queryAll();
            foreach($refunds as $refund){
                $refundChild[$refund['on_invoice_id']] = $refund['on_invoice_id'];
            }

            foreach($invoiceids as $invo){
//                if(!in_array($invo['invoice_id'], $refundChild) && in_array($invo['childid'], $this->inClass) && !in_array($invo['childid'], $invoiced)){
                if(!in_array($invo['invoice_id'], $refundChild) && in_array($invo['childid'], $this->inClass)){
                    $num15[$invo['classid']][$invo['childid']] = $invo['childid'];
                }
            }
        }

        $d1 = array();
        $d2 = array();

        foreach($this->data1 as $n1){
            $d1 += $n1;
        }
        foreach($this->data2 as $n2){
            $d2 += $n2;
        }

        foreach($num15 as $classid=>$num){
            $this->data15[$classid] = array_diff($num, $d1, $d2);
        }

        foreach($num16 as $classid=>$num){
            $this->data16[$classid] = array_diff($num, $d1, $d2);
        }

//        $this->data15 = $num15;
//        $this->data16 = $num16;
        return array('num15'=>$this->data15, 'num16'=>$this->data16);
    }

    public function getNum10_11($school)
    {
        Yii::import('common.models.calendar.CalendarSchool');

        $schoolYear = CalendarSchool::model()->getSchoolCalender($school['branchid']);
        unset($schoolYear[$school["schcalendar"]]);

        $deposited = array();
        if($schoolYear){
            $sql = "select invoice_id,childid,status from ivy_invoice_invoice where payment_type in ('deposit','tuition') and schoolid='".$school['branchid']."' and
            calendar_id in (".implode(',', array_keys($schoolYear)).") and status!=99 and status!=88 and `inout`='in'";
            $wellbeclass = Yii::app()->db->createCommand($sql)->queryAll();
            foreach($wellbeclass as $invo){
                if($invo['status'] == 20){
                    $deposited[$invo['invoice_id']] = $invo['childid'];
                }
                else{
                    $this->data11[$invo['childid']] = $invo['childid'];
                }
            }
        }

        $refunds = array();
        $refundChilds = array();

        if($deposited){
            $sql = "select childid,on_invoice_id from ivy_invoice_child_refund where on_invoice_id in (".implode(',',array_keys($deposited)).") and status!=99";
            $refunds = Yii::app()->db->createCommand($sql)->queryAll();
            foreach($refunds as $refund){
//                $refundChilds[$refund['childid']] = $refund['childid'];
                unset($deposited[$refund['on_invoice_id']]);
            }

            foreach($deposited as $childid){
//                if(!in_array($childid, $refundChilds)){
                    $this->data10[$childid] = $childid;
//                }
            }

            $this->data11 = array_diff($this->data11, $this->data10);

            $m1011 = $this->data10+$this->data11;
            if ($m1011) {
                $sql = "select childid from ivy_child_profile_basic where childid in (".implode(',', $m1011).") and status>20";
                $exitChild = Yii::app()->db->createCommand($sql)->queryAll();
                foreach ($exitChild as $cid) {
                    $this->data17[$cid['childid']] = $cid['childid'];
                }
            }
        }

        return array('num10'=>$this->data10, 'num11'=>$this->data11, 'num17'=>$this->data17);
    }

    public function getNum6_7_8($school)
    {
        $num6 = array();
        $num7 = array();
        $num8 = array();

        $invoiceids = array();
        $childids = array();
//        $refundids = array();

        $sql = "select i.invoice_id,i.childid,i.status from ivy_invoice_invoice i join ivy_child_profile_basic c on i.childid=c.childid
        where i.payment_type = 'deposit' and i.status!=88 and i.status!=99 and i.`inout`='in' and c.status=0 and c.schoolid='".$school['branchid']."'
        and i.schoolid='".$school['branchid']."' and i.calendar_id=".$school['schcalendar'];
        $invoices = Yii::app()->db->createCommand($sql)->queryAll();
        foreach($invoices as $invoice){
            $invoiceids[$invoice['invoice_id']] = $invoice;
            $childids[$invoice['childid']] = $invoice['childid'];
        }

        if($invoiceids){
//            $sql = "select on_invoice_id from ivy_invoice_child_refund where status!=99 and on_invoice_id in (".implode(',', array_keys($invoiceids)).")";
//            $refunds = Yii::app()->db->createCommand($sql)->queryAll();
//            foreach($refunds as $refund){
//                $refundids[$refund['on_invoice_id']] = $refund['on_invoice_id'];
//            }

            foreach($invoiceids as $invo){
                if(!in_array($invo['childid'], $this->inClass) && !in_array($invo['childid'], $this->data10) && !in_array($invo['childid'], $this->data11)){
                    if($invo['status'] == 20){
                        $num6[$invo['childid']] = $invo['childid'];
                    }
                    else{
                        $num7[$invo['childid']] = $invo['childid'];
                    }
                }
            }
        }

        $sql = "select * from ivy_child_profile_basic where status=0 and schoolid='".$school['branchid']."'";
        $allChilds = Yii::app()->db->createCommand($sql)->queryAll();
        foreach($allChilds as $child){
            if(!in_array($child['childid'], $childids) && !in_array($child['childid'], $this->data10) && !in_array($child['childid'], $this->data11)){
                $num8[$child['childid']] = $child['childid'];
            }
        }

        $this->data6 = $num6;
        $this->data7 = $num7;
        $this->data8 = $num8;
        return array('num6'=>$num6, 'num7'=>$num7, 'num8'=>$num8);
    }

    public function getNum12($school)
    {
        Yii::import('common.models.calendar.CalendarSchool');

        $schoolYear = CalendarSchool::model()->getSchoolCalender($school['branchid'], 'desc');
        unset($schoolYear[$school["schcalendar"]]);

        if($schoolYear){
            $sql = "select h.* from ivy_child_deposit_history h join ivy_child_profile_basic t on h.childid=t.childid
            where h.yid in (".implode(',', array_keys($schoolYear)).") and t.schoolid='".$school['branchid']."'";
            $historyDeposit = Yii::app()->db->createCommand($sql)->queryAll();
            $deposits = array();
            foreach($historyDeposit as $deposit){
                $deposits[$deposit['yid']][$deposit['childid']][$deposit['inout']][$deposit['did']] = $deposit['amount'];
            }

            foreach($deposits as $yid=>$deposit){
                foreach($deposit as $childid=>$inouts){
                    if(!isset ($inouts['in']))
                        $inouts['in'] = array();
                    if(!isset ($inouts['out']))
                        $inouts['out'] = array();
                    if(array_sum($inouts['in']) > array_sum($inouts['out'])){
                        $this->data12[$childid] = $childid;
                    }
                }
            }

            return array('num12'=>$this->data12);
        }
    }
}