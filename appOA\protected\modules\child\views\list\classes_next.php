<?php
$this->renderPartial('header');

	$this->widget('zii.widgets.CMenu',array(
		'items'=> $pageSubMenu,
		'activeCssClass'=>'current',
		'id'=>'page-subMenu',
		'htmlOptions'=>array('class'=>'pageSubMenu')
	));
	echo CHtml::openTag("div", array("class"=>"c"));
	echo CHtml::closeTag("div");
?>

<?php if ($calendar):?>
	<div class="h_a"><?php echo Yii::t("campus", "School Year");?></div>
	<div class="prompt_text">
		<ul>
			<li><?php echo $calendar->startyear . ' ~ ' . intval($calendar->startyear + 1);?></li>
		</ul>
	</div>	
	
	<div class="h_a"><?php echo Yii::t("campus", "Class List");?></div>
	<div class="table_full">
		<table width="100%">
			<colgroup>
				<col width=240>
			</colgroup>
			<?php foreach ($calendar->nextClasses as $class):?>
				<tr>
					<th id="classid-<?php echo $class->classid;?>">
						<?php echo $class->title . ' ('.count($class->reserveChildren).')';?>
						</th>
						<td class="user_group">
						<?php if($class->reserveChildren):?>
							<?php foreach ($class->reserveChildren as $child):
								echo CHtml::link($child->childProfile->getChildName(), array("/child/index/index", "childid"=>$child->childid), array("class"=>"mr15", "target"=>"_blank"));
							endforeach;?>
						<?php endif;?>
						</td>
				</tr>
				<tr>
					<td colspan="2">
					<div id="invoice-box-<?php echo $class->classid;?>"></div>
					</td>
				</tr>				
			<?php endforeach; ?>
		</table>
	</div>
	
	
	
<?php endif;?>



