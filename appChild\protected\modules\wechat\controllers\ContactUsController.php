<?php

class ContactUsController extends WeChatVisualController
{

    public $signPackage;
    public $childid;
    public $schoolid;
    public $childObj;
    public $userid;
    public $langPrefix;
    public $Num = 20;
	public function beforeAction($action)
	{
		Yii::import('common.models.wechat.ContactProblem');
		Yii::import('common.models.child.ChildDailySign');
		Yii::import('common.models.attendance.ChildVacation');
		$this->userid = $this->wechatUser->userid;
		$this->childid = isset($_COOKIE['childid']) ? $_COOKIE['childid'] : '' ;

        if ($this->childid && isset($this->myChildObjs[$this->childid])) {
            $this->childObj = $this->myChildObjs[$this->childid];
        } else {            
            $this->childObj = end($this->myChildObjs);
            $this->childid = $this->childObj->childid;
        }
		$this->schoolid = $this->childObj->schoolid;
		// 语言前缀
		$this->langPrefix = 'cn';
		if (Yii::app()->language == 'en_us') {
			$this->langPrefix = 'en';
		}

		return true;
	}

	// 首页
	public function actionIndex()
	{
        $this->subPageTitle = Yii::t('navigations' ,'Contact Campus');
        Yii::import('common.models.invoice.*');
        Yii::import('common.models.calendar.*');
        $schoolStatus = 1; // 是否显示取消午餐按钮 1不显示，0 显示
        $schoolId = $this->schoolid;
        $yid = Branch::model()->getBranchInfo($schoolId, 'schcalendar');

        $dsSchoolList = CommonUtils::dsSchoolList();

        if (!in_array($schoolId, $dsSchoolList)) {
            $feeType = 'lunch';
            // 去掉查询旧版餐费设置
            // $feeObj = FeemgtSchoolConfig::model()->getSchoolFeeConfig($schoolId, $yid, $feeType);
            // if (empty($feeObj) || ($feeObj->lunch_payment_type == 30)) {
            //     $schoolStatus = 1;
            // }

            $childServiceInfo = ChildServiceInfo::model()->getChildFeeInfo($this->childid, $feeType, $yid, time());
    
            if($childServiceInfo){
                $schoolStatus = 0;
            }

        }


        $this->render('index',array(
            'schoolStatus' => $schoolStatus,
        ));
	}

	public function actionLeaveList()
    {
        $this->subPageTitle = Yii::t('navigations' ,'请假列表');
        $criteria = new CDbCriteria();
        $criteria->compare('t.child_id', $this->childid);
        $criteria->compare('t.school_id', $this->schoolid);
        $criteria->order = "vacation_time_start DESC";
        $criteria->compare('stat', ChildVacation::STATUS_CHECKED);
        $criteria->limit = $this->Num;
        $criteria->order = "vacation_time_start DESC";
        $model = ChildVacation::model()->findAll($criteria);

        $criteria = new CDbCriteria();
        $criteria->compare('t.childid', $this->childid);
        $criteria->compare('t.sign_timestamp', strtotime(date('Y-m-d')));
        $childDailModel = ChildDailySign::model()->find($criteria);


        $this->render('leavelist', array(
            'model' => $model,
            'childDailModel' => $childDailModel,
        ));
    }

    public function actionPagination()
    {
        $offset = Yii::app()->request->getParam('offset', '');
        if(Yii::app()->request->isPostRequest) {
            $config = ChildVacation::getConfig();
            if($offset){
                $limit = $this->Num;
                $criteria = new CDbCriteria();
                $criteria->compare('t.child_id', $this->childid);
                $criteria->compare('t.school_id', $this->schoolid);
                $criteria->order = "vacation_time_start DESC";
                $criteria->compare('stat', ChildVacation::STATUS_CHECKED);
                $criteria->limit = $limit;
                $criteria->offset  = $limit * $offset;
                $criteria->order = "vacation_time_start DESC";
                $model = ChildVacation::model()->findAll($criteria);
                $array = array();
                if($model){
                    foreach($model as $item){
                        $data = strtotime(date("Y-m-d",$item->vacation_time_start) . " 7:00");
                        $start = ($item->vacation_time_start) ? date("Y/m/d",$item->vacation_time_start) : "";
                        $end = ($item->vacation_time_end) ? date("Y/m/d",$item->vacation_time_end) : "" ;
                        $array[] = array(
                            'id' => $item->id,
                            'vacation_time_start' => (in_array($item->type, array(10,20))) ? $start . " - " .  $end  : $start,
                            'est_begin_time' => ($item->est_begin_time) ? $item->est_begin_time : 0 ,
                            'estData' => ($item->est_begin_time) ? $item->est_begin_time - 7200 : 0 ,
                            'data' => $data,
                            'time' => time(),
                            "typeName" => $config[$item->type],
                            'type' => $item->type,
                        );
                    }
                }
                echo CJSON::encode($array);
            }
        }
    }

	/*
	 *  增加联系我们资料
	 *  $leaveType   类型  1 一般问题  2 请假   3  迟到
	 *  $contact     内容
	 *  $startDate   开始时间
	 *  $endDate     结束时间
	 *  $type        请假类型   10 病假 20 事假
	 *  $lunchStatus 是否取消午餐
	 */
	public function actionUpdateContact()
    {
        $leaveType = Yii::app()->request->getParam('leaveType', '');
        $contact = Yii::app()->request->getParam('vacation_reason', '');
        $startDate = Yii::app()->request->getParam('startDate', '');
        $endDate = Yii::app()->request->getParam('endDate', '');
        $estBeginTime = Yii::app()->request->getParam('estBeginTime', '');
        $type = Yii::app()->request->getParam('type', '');
        $lunchStatus = Yii::app()->request->getParam('lunchStatus', '');
        $dataStuats = false;
        $date_type = "";
        $childServiceInfoModel = 0;
        if(empty($contact)){
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '内容不能为空');
            $this->showMessage();
        }

        if(in_array($leaveType, array(2,3,4))){
            if(empty($type) && $leaveType == 2){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '请假类型不能为空');
                $this->showMessage();
            }

            if(empty($startDate)){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '时间不能为空');
                $this->showMessage();
            }
            if(empty($endDate) && $leaveType == 2){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '结束时间不能为空');
                $this->showMessage();
            }

            if($leaveType == 2){
                if(strtotime(date('Y-m-d')) == strtotime($startDate)){
                    $criteria = new CDbCriteria();
                    $criteria->compare('t.childid', $this->childid);
                    $criteria->compare('t.classid', $this->childObj->classid);
                    $criteria->compare('t.schoolid', $this->schoolid);
                    $criteria->compare('t.sign_timestamp', strtotime($startDate));
                    $childDailModelCount = ChildDailySign::model()->count($criteria);
                    if($childDailModelCount){
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', '所选日期已经有签到,请假请联系校园');
                        $this->showMessage();
                    }
                }
            }

            if($leaveType == 3){  // 迟到
                $endDate = $startDate;
                $type = ChildVacation::VACATION_LATE;
            }
            if($leaveType == 4){  //早退
                $criteria = new CDbCriteria();
                $criteria->compare('t.child_id', $this->childid);
                $criteria->compare('vacation_time_start', strtotime($startDate));
                $criteria->compare('type', ChildVacation::VACATION_LEAVE);
                $criteria->compare('stat', ChildVacation::STATUS_CHECKED);
                $model = ChildVacation::model()->count($criteria);
                if($model){
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', '所选日期已经添加过早退信息');
                    $this->showMessage();
                }
                $endDate = $startDate;
                $type = ChildVacation::VACATION_LEAVE;
            }
            if(strtotime($startDate) > strtotime($endDate)){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '开始时间不可大于结束时间');
                $this->showMessage();
            }
            $vacationEndDate = strtotime($endDate);
            $vacationStartDate = strtotime($startDate);
            if($leaveType != 4){
                $ChildCacationType = ChildVacation::VACATION_LEAVE;
                $criteria = new CDbCriteria();
                $criteria->compare('t.child_id', $this->childid);
                $criteria->compare('t.type', "<>{$ChildCacationType}");
                $criteria->compare('vacation_time_start', "<={$vacationEndDate}");
                $criteria->compare('vacation_time_end', ">={$vacationStartDate}");
                $criteria->compare('stat', ChildVacation::STATUS_CHECKED);
                $modelCount = ChildVacation::model()->count($criteria);

                if($modelCount){
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', '您所选日期其中已经有过请假, 请核对后在填写');
                    $this->showMessage();
                }
            }

            $vacationStatus = $this->saveVacation(strtotime($startDate), strtotime($endDate), $type, $estBeginTime, $lunchStatus, $contact);
            if($vacationStatus){
                $dataStuats = true;
                if($lunchStatus){
                    Yii::import('common.models.invoice.*');
                    $dataStart = strtotime($startDate);
                    $dataEnd = strtotime($endDate);
                    $criteria = new CDbCriteria();
                    $criteria->compare('t.childid', $this->childid);
                    $criteria->compare('t.startdate', "<={$dataEnd}");
                    $criteria->compare('t.enddate', ">={$dataStart}");
                    $criteria->compare('t.payment_type', "lunch");
                    $childServiceInfoModel = ChildServiceInfo::model()->count($criteria);
                    if($childServiceInfoModel){
                        $dataStuats = $this->updateLunch($startDate,$endDate,'');
                    }
                }
                // 发送邮件
                $this->sendVacationEmail($vacationStatus);
                // Yii::import('common.components.AliYun.MQ.MQProducer');
                // $producer = new MQProducer(MQProducer::TAG_ASA, "Mail.vacation", $vacationStatus->id);
                // //启动消息发布者
                // $producer->process();
            }
        }else{
            $intoSchool = Yii::app()->request->getParam('intoSchool', '0');
            $leaveSchool = Yii::app()->request->getParam('leaveSchool', '0');
            $startDate = Yii::app()->request->getParam('startDate', '');

            if($leaveType == 1){
                $type = ContactProblem::TYPE_RIDE;
            }else{
                $type = ContactProblem::TYPE_NORIDE;
                if(empty($startDate)){
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', "时间不能为空");
                    $this->showMessage();
                }

                if(empty($intoSchool) && empty($leaveSchool)){
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', "必选一个");
                    $this->showMessage();
                }
                $date_type = implode(",",(array($intoSchool, $leaveSchool)));

                $criteria = new CDbCriteria();
                $criteria->compare('t.childid', $this->childid);
                $criteria->compare('t.dayTime', strtotime($startDate));
                $criteria->compare('t.status', ContactProblem::STATUS_SUBMIT);
                $contactModel = ContactProblem::model()->find($criteria);

            }

            $dataStuats = $this->saveContact($contact, $type, $date_type, $startDate);
            if($dataStuats){
                if($contactModel){
                    $contactModel->status = 0;
                    $contactModel->save();
                }
                $this->sendContactEmail($dataStuats);
                // Yii::import('common.components.AliYun.MQ.MQProducer');
                // $producer = new MQProducer(MQProducer::TAG_ASA, "Mail.contact", $dataStuats->id);
                // //启动消息发布者
                // $producer->process();
            }
        }

        if(!$dataStuats){
            $this->addMessage('state', 'fail');
            $this->addMessage('message', "失败");
            $this->showMessage();
        }

        $this->addMessage('state', 'success');
        $this->addMessage('message', Yii::t('asa','success'));
        $this->addMessage('data', $childServiceInfoModel);
        $this->showMessage();
    }

    public function actionVacation()
    {
        $id = Yii::app()->request->getParam('id', '');
        Yii::import('common.models.invoice.*');
        if (Yii::app()->request->isPostRequest) {
            $childid = $this->childid;
            $userId = $this->userid;
            $model = ChildVacation::model()->findByPk($id);

            if (!$model || $model->stat == 0) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '请假记录未找到');
                $this->showMessage();
            }

            $dayTime = strtotime(date('Y-m-d'));
            $criteria = new CDbCriteria();
            $criteria->compare('t.childid', $this->childid);
            $criteria->compare('t.sign_timestamp', $dayTime);
            $childDailModel = ChildDailySign::model()->count($criteria);

            if (isset($childDailModel) && ChildVacation::VACATION_LEAVE != $model->type && $model->vacation_time_start == $dayTime) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '已经有签到记录, 不可取消');
                $this->showMessage();
            }
            $data = strtotime(date("Y-m-d", $model->vacation_time_start) . " 7:00");
            $estData = $model->est_begin_time - 7200;
            $typeList = array(ChildVacation::VACATION_SICK_LEAVE, ChildVacation::VACATION_AFFAIR_LEAVE, ChildVacation::VACATION_LATE);

            if ($data < time() && in_array($model->type, $typeList)) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '已过操作时间不可取消迟到或者请假,请联系校园');
                $this->showMessage();
            }

            if ($estData < time() && ChildVacation::VACATION_LEAVE == $model->type) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '已过操作时间取消早退,请联系校园');
                $this->showMessage();
            }

            $model->stat = 0;
            if ($model->save()) {
                if ($model->cancel_lunch == ChildVacation::STATUS_CHECKED && in_array($model->type, array(ChildVacation::VACATION_SICK_LEAVE, ChildVacation::VACATION_AFFAIR_LEAVE))) {
                    $criteria = new CDbCriteria;
                    $criteria->compare('childid', $this->childid);
                    $criteria->compare('target_timestamp', ">={$model->vacation_time_start}");
                    $criteria->compare('target_timestamp', "<={$model->vacation_time_end}");
                    $refundModel = RefundLunch::model()->findAll($criteria);
                    if ($refundModel) {
                        foreach ($refundModel as $item) {
                            Mims::lunchRecover($childid, $item->target_date, $userId);
                        }
                    }
                }

                $this->sendVacationEmail($model);
                // Yii::import('common.components.AliYun.MQ.MQProducer');
                // $producer = new MQProducer(MQProducer::TAG_ASA, "Mail.vacation", $model->id);
                // //启动消息发布者
                // $producer->process();

                $this->addMessage('state', 'success');
                $this->addMessage('message', '取消成功');
                $this->showMessage();
            }

        }
        $this->addMessage('state', 'fail');
        $this->addMessage('message', '非法操作');
        $this->showMessage();
    }

	// 一般咨询
	public function saveContact($content, $type ,$date_type ,$dayTime)
	{
		$model = new ContactProblem();
		$model->content = $content;
		$model->type = $type;
		$model->childid = $this->childid;
        $model->schoolid = $this->schoolid;
        $model->classid = $this->childObj->classid;
		$model->date_type = $date_type;
		$model->dayTime = ($dayTime) ? strtotime($dayTime) : "";
		$model->status = ContactProblem::STATUS_SUBMIT;
		$model->uid = $this->userid;
		$model->update_time = time();
		if (!$model->save()) {
			return false;
		}
		return $model;
	}

	// 保存请假、迟到
	public function saveVacation($startDate, $endDate, $type, $estBeginTime, $cancelLunch ,$reason)
	{
		$model = new ChildVacation();
		$model->child_id = $this->childid;
		$model->school_id = $this->schoolid;
		$model->class_id = $this->childObj->classid;
		$model->vacation_time_start = $startDate;
		$model->vacation_time_end = $endDate;
		$model->est_begin_time = ($estBeginTime) ? strtotime(date("Y-m-d", $startDate) . $estBeginTime): "";
		$model->cancel_lunch = ($cancelLunch) ? 1 : 0 ;
		$model->type = $type;
		$model->vacation_reason = $reason;
		$model->uid = $this->userid;
		$model->updata_time = time();
		$model->stat = 1;
		if (!$model->save()) {
			return false;
		}
		return $model;
	}

	// 取消、恢复指定区间的午餐
	public function updateLunch($startDate, $endDate, $action)
	{
        Yii::import('common.models.invoice.*');
        Yii::import('common.models.calendar.*');
        $schoolId = $this->schoolid;
        $yid = Branch::model()->getBranchInfo($schoolId, 'schcalendar');
        $CfgFeeType = Mims::LoadConfig('CfgFeeType');
        // $feeType = $CfgFeeType["lunch"]["sign"];
        // $feeObj = FeemgtSchoolConfig::model()->getSchoolFeeConfig($schoolId, $yid, $feeType);
        // if (empty($feeObj) || ($feeObj->lunch_payment_type == 30)) {
        // }else{
            $startDate = strtotime($startDate);
            $startDate_a = date("Ymd", $startDate);
            $endDate = strtotime($endDate);
            $endDate_a = date("Ymd", $endDate);
            if (date('H', time()) >= 9) {
                $startDate += 24*3600;
            }

            // 根据开始结束时间算出 学校的上学日
            $schoolDays = CalendarSchoolDays::model()->getCalendarSchoolDay($yid, $startDate, $endDate);
            if($schoolDays){
                $schoolDaysArr =array();
                foreach ($schoolDays as $schoolDay) {
                    $schoolDaysArr[$schoolDay->month] = explode(',', $schoolDay->schoolday_array);
                }
                $schoolDayList = array();
                foreach ($schoolDaysArr as $key => $schoolItem) {
                    foreach($schoolItem as $item){
                        $sad = $key . $item;
                        if($sad >= $startDate_a && $sad <= $endDate_a){
                            $schoolDayList[$sad] = $sad;
                        }
                    }
                }

                //餐费退费信息
                $refundLunchDays = array();
                $refundLunch = RefundLunch::model()->getRefundLunch($this->childid, $startDate, $endDate);
                if($refundLunch){
                    foreach ($refundLunch as $v) {
                        $refundLunchDays[$v->target_date] = $v->target_date;
                    }
                }

                $cancelLunch = array_merge(array_diff($schoolDayList,$refundLunchDays),array_diff($refundLunchDays,$schoolDayList));

                if($cancelLunch){
                    $childid = $this->childid;
                    $userId = $this->userid;
                    foreach($cancelLunch as $timeLunch){
                        Mims::lunchCancel($childid, $timeLunch, $userId);
                    }
                }
            // }
            return true;
        }
	}


    /**
     * 发送联系我们邮件
     *
     * @param [type] $contactObj 发送内容
     * @return void
     */
    public function sendContactEmail($contactObj)
    {
        $childObj = $this->childObj;
        $campus = Branch::model()->with('info')->findByPk($childObj->schoolid);
        $mailer = Yii::createComponent('common.extensions.mailer.EMailer2');

        $childName = $childObj->getChildName();
        $classTitle = $childObj->ivyclass ? $childObj->ivyclass->title : '';
        $type = ( $contactObj->type == 10) ? '【一般咨询】' : '【临时修改接送方式】' ;

        $content['contact'] = $contactObj;
        $content['childName'] = $childName;
        $content['classTitle'] = $classTitle;
        $mailer->Subject = $type . $childName;
        $pEmails = $this->getParentsEmail($childObj);
        $mailer->AddAddress($campus->info->support_email);
        $mailer->AddReplyTo($campus->info->support_email);
        foreach($pEmails as $_pe){
            $mailer->AddCC($_pe);
        }

        $mailer->getView('contact', array('data'=> $content,), 'wechat');
        $mailer->iniMail($this->isProduction()); // 此行代码要放到AddAddress, AddCC方法下面
        return $mailer->Send();
    }

    /**
     * 发送请假邮件
     *
     * @param [type] $vacationObj 发送内容
     * @return void
     */
    public function sendVacationEmail($vacationObj)
    {
        $childObj = $this->childObj;
        $campus = Branch::model()->with('info')->findByPk($childObj->schoolid);
        $mailer = Yii::createComponent('common.extensions.mailer.EMailer2');

        $childName = $childObj->getChildName();
        $classTitle = $childObj->ivyclass ? $childObj->ivyclass->title : '';
        $type = $vacationObj->getVacationType();
        if (in_array($vacationObj->type, array(10, 20))) {
            $type = '请假';
        }
        if ($vacationObj->stat == 0) {
            $type = '取消' . $type;
        }

        $content['vacation'] = $vacationObj;
        $content['childName'] = $childName;
        $content['classTitle'] = $classTitle;
        $mailer->Subject = '【' . $type. '】' . $childName;
        $pEmails = $this->getParentsEmail($childObj);
        $mailer->AddAddress($campus->info->support_email);
        $mailer->AddReplyTo($campus->info->support_email);
        foreach($pEmails as $_pe){
            $mailer->AddCC($_pe);
        }

        $mailer->getView('vacation', array('data'=> $content,), 'wechat');
        $mailer->iniMail($this->isProduction()); // 此行代码要放到AddAddress, AddCC方法下面
        return $mailer->Send();
    }

    //获取孩子父母的邮件地址
    private function getParentsEmail($child){
        $emails = array();
        $pids = array();
        if($child->fid) $pids[] = $child->fid;
        if($child->mid) $pids[] = $child->mid;
        $parents = User::model()->findAllByPk($pids, '`level`>:level', array(':level'=>0));
        if(!empty($parents)){
            foreach($parents as $parent){
                $emails[] = $parent->email;
            }
        }
        return $emails;
    }

    //判断是否为生产环境
    private function isProduction(){
        if ( strtolower(Yii::app()->params['productionDomain']) == strtolower($_SERVER['HTTP_HOST']) ) {
            return true;
        } else{
            return false;
        }
    }


    // 取消请假、迟到  暂时不用
    /*public function cancelVacation($id)
    {
        $childid = $this->childid;
        $userId = $this->userid;
        $model = ChildVacation::model()->deleteByPk($id);
        if ($model->status != ChildVacation::STATUS_UNCHECKED) {
            $dayTime = "";
            if($model->vacation_time_start == $model->vacation_time_end){
                $dayTime = $model->vacation_time_start;
            }
            if ($model->delete()) {
                Mims::lunchRecover($childid, $dayTime, $userId);
                return true;
            }
        } else {
            $this->addMessage('message', '签到记录已审核');
        }
        return false;
    }*/

}
