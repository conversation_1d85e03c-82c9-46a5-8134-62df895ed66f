<?php
/**
 * Created by PhpStorm.
 * User: XINRAN
 * Date: 14-4-16
 * Time: 上午10:53
 */ ?>
<script type="text/template" id="monthly-finance-structure-template">
    <h3 class="title"><span class="glyphicon glyphicon-list-alt"></span> Monthly Financial Data</h3>
    <div class="month-list sub-title">

    </div>
    <div class="month-data"></div>
</script>

<script type="text/template" id="monthly-finance-data-template">
<div class="row">
    <div class="col-md-12" id="monthly-data-1">
        <table class="table table-bordered table-hover">
            <thead>
            <tr>
                <th class="success" colspan="5">月度数据 <?php echo $monthStr;?></th>
                <th class="warning" colspan="3">本学年累计数据</th>
            </tr>
            <tr class="active">
                <th></th>
                <th>实际数据</th>
                <th>预算数据</th>
                <th>完成百分比(%)</th>
                <th><?php echo Yii::t('labels','Memo');?></th>
                <th>累计实际数据</th>
                <th>累计预算数据</th>
                <th>累计完成进度</th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <th class="active">招生人数</th>
                <td><%= parseInt(c11.s1001.data) %><% print(printGap(c11.s1001.gap)); %></td>
                <td><%= parseInt(c12.s1001.data) %><% print(printGap(c12.s1001.gap)); %></td>
                <td><% print(getPercent(c11.s1001.data, c12.s1001.data)); %></td>
                <td></td>
                <td><%= parseInt(c11.s1001.cursive) %></td>
                <td><%= parseInt(c12.s1001.cursive) %></td>
                <td></td>
            </tr>
            <tr>
                <th class="active">收入金额</th>
                <td><% print(formatMoney(c11.s1002.data)); %><% print(printGap(c11.s1002.gap)); %></td>
                <td><% print(formatMoney(c12.s1002.data)); %><% print(printGap(c12.s1002.gap)); %></td>
                <td><% print(getPercent(c11.s1002.data, c12.s1002.data)); %></td>
                <td></td>
                <td><% print(formatMoney(c11.s1002.cursive)); %></td>
                <td><% print(formatMoney(c12.s1002.cursive)); %></td>
                <td><% print(getPercent(c11.s1002.cursive, c12.s1002.cursive)); %></td>
            </tr>
            <tr>
                <th class="active">总支出</th>
                <td><% print(formatMoney(c11.cost.data));%><% print(printGap(c11.cost.gap)); %></td>
                <td><% print(formatMoney(c12.cost.data));%><% print(printGap(c12.cost.gap)); %></td>
                <td><% print(getPercent(c11.cost.data, c12.cost.data)); %></td>
                <td></td>
                <td><% print(formatMoney(c11.cost.cursive)); %></td>
                <td><% print(formatMoney(c12.cost.cursive)); %></td>
                <td><% print(getPercent(c11.cost.cursive, c12.cost.cursive)); %></td>
            </tr>
            <tr>
                <td align="right">人事费用</td>
                <td><% print(formatMoney(c11.s1003.data)); %><% print(printGap(c11.s1003.gap)); %></td>
                <td><% print(formatMoney(c12.s1003.data)); %><% print(printGap(c12.s1003.gap)); %></td>
                <td><% print(getPercent(c11.s1003.data, c12.s1003.data)); %></td>
                <td></td>
                <td><% print(formatMoney(c11.s1003.cursive)); %></td>
                <td><% print(formatMoney(c12.s1003.cursive)); %></td>
                <td><% print(getPercent(c11.s1003.cursive, c12.s1003.cursive)); %></td>
            </tr>
            <tr>
                <td align="right">设备费用</td>
                <td><% print(formatMoney(c11.s1004.data)); %><% print(printGap(c11.s1004.gap)); %></td>
                <td><% print(formatMoney(c12.s1004.data)); %><% print(printGap(c12.s1004.gap)); %></td>
                <td><% print(getPercent(c11.s1004.data, c12.s1004.data)); %></td>
                <td></td>
                <td><% print(formatMoney(c11.s1004.cursive)); %></td>
                <td><% print(formatMoney(c12.s1004.cursive)); %></td>
                <td><% print(getPercent(c11.s1004.cursive, c12.s1004.cursive)); %></td>
            </tr>
            <tr>
                <td align="right">日常运作</td>
                <td><% print(formatMoney(c11.s1005.data)); %><% print(printGap(c11.s1005.gap)); %></td>
                <td><% print(formatMoney(c12.s1005.data)); %><% print(printGap(c12.s1005.gap)); %></td>
                <td><% print(getPercent(c11.s1005.data, c12.s1005.data)); %></td>
                <td></td>
                <td><% print(formatMoney(c11.s1005.cursive)); %></td>
                <td><% print(formatMoney(c12.s1005.cursive)); %></td>
                <td><% print(getPercent(c11.s1005.cursive, c12.s1005.cursive)); %></td>
            </tr>
            <tr>
                <td align="right">房租</td>
                <td><% print(formatMoney(c11.s1010.data)); %><% print(printGap(c11.s1010.gap)); %></td>
                <td><% print(formatMoney(c12.s1010.data)); %><% print(printGap(c12.s1010.gap)); %></td>
                <td><% print(getPercent(c11.s1010.data, c12.s1010.data)); %></td>
                <td></td>
                <td><% print(formatMoney(c11.s1010.cursive)); %></td>
                <td><% print(formatMoney(c12.s1010.cursive)); %></td>
                <td><% print(getPercent(c11.s1010.cursive, c12.s1010.cursive)); %></td>
            </tr>
            <tr>
                <td align="right">其他</td>
                <td><% print(formatMoney(c11.s1006.data)); %><% print(printGap(c11.s1006.gap)); %></td>
                <td><% print(formatMoney(c12.s1006.data)); %><% print(printGap(c12.s1006.gap)); %></td>
                <td><% print(getPercent(c11.s1006.data, c12.s1006.data)); %></td>
                <td></td>
                <td><% print(formatMoney(c11.s1006.cursive)); %></td>
                <td><% print(formatMoney(c12.s1006.cursive)); %></td>
                <td><% print(getPercent(c11.s1006.cursive, c12.s1006.cursive)); %></td>
            </tr>
            <tr>
                <th class="active">利润</th>
                <td><% print(formatMoney(c11.profit.data));%><% print(printGap(c11.profit.gap)); %></td>
                <td><% print(formatMoney(c12.profit.data));%><% print(printGap(c12.profit.gap)); %></td>
                <td><% print(getPercent(c11.profit.data, c12.profit.data)); %></td>
                <td></td>
                <td><% print(formatMoney(c11.profit.cursive)); %></td>
                <td><% print(formatMoney(c12.profit.cursive)); %></td>
                <td><% print(getPercent(c11.profit.cursive, c12.profit.cursive)); %></td>
            </tr>
            <tr>
                <th class="active">利润率</th>
                <td><% print(formatMoney(c11.profitMargin.data));%><% print(printGap(c11.profitMargin.gap)); %></td>
                <td><% print(formatMoney(c12.profitMargin.data));%><% print(printGap(c12.profitMargin.gap)); %></td>
                <td></td>
                <td></td>
                <td><% print(formatMoney(c11.profitMargin.cursive)); %></td>
                <td><% print(formatMoney(c12.profitMargin.cursive)); %></td>
                <td></td>
            </tr>
            <!--<tr class="active">-->
                <!--<th></th>-->
                <!--<th>实际数据</th>-->
                <!--<th>预算数据</th>-->
                <!--<th>完成百分比(%)</th>-->
                <!--<th>备注</th>-->
                <!--<th>累计实际数据</th>-->
                <!--<th>累计预算数据</th>-->
                <!--<th>累计完成进度</th>-->
            <!--</tr>-->
            <tr><td colspan="8">&nbsp;</td></tr>


            <tr class="active">
                <th></th>
                <th>实际数据</th>
                <th>预算数据</th>
                <th>完成百分比(%)</th>
                <th></th>
                <th>累计实际平均数据</th>
                <th>累计预算平均数据</th>
                <th>累计完成平均百分比(%)</th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <th class="active">月收入/招生人数<br>
                    <small>标准费用<% print(formatMoney(standardTuitionFee));%></small>
                </th>
                <td><% print(formatMoney(c11.revenuePerChild.data));%><% print(printGap(c11.revenuePerChild.gap)); %></td>
                <td><% print(formatMoney(c12.revenuePerChild.data));%><% print(printGap(c12.revenuePerChild.gap)); %></td>
                <td><% print(getPercent(c11.revenuePerChild.data, c12.revenuePerChild.data)); %></td>
                <td></td>
                <td><% print(formatMoney(c11.revenuePerChild.cursive)); %></td>
                <td><% print(formatMoney(c12.revenuePerChild.cursive)); %></td>
                <td><% print(getPercent(c11.revenuePerChild.cursive, c12.revenuePerChild.cursive)); %></td>
            </tr>
            <tr>
                <th class="active">总支出/招生人数</th>
                <td><% print(formatMoney(c11.costPerChild.data));%><% print(printGap(c11.costPerChild.gap)); %></td>
                <td><% print(formatMoney(c12.costPerChild.data));%><% print(printGap(c12.costPerChild.gap)); %></td>
                <td><% print(getPercent(c11.costPerChild.data, c12.costPerChild.data)); %></td>
                <td></td>
                <td><% print(formatMoney(c11.costPerChild.cursive)); %></td>
                <td><% print(formatMoney(c12.costPerChild.cursive)); %></td>
                <td><% print(getPercent(c11.costPerChild.cursive, c12.costPerChild.cursive)); %></td>
            </tr>
            <tr>
                <td align="right">人事费用/招生人数</td>
                <td><% print(formatMoney(c11.hrCostPerChild.data));%><% print(printGap(c11.hrCostPerChild.gap)); %></td>
                <td><% print(formatMoney(c12.hrCostPerChild.data));%><% print(printGap(c12.hrCostPerChild.gap)); %></td>
                <td><% print(getPercent(c11.hrCostPerChild.data, c12.hrCostPerChild.data)); %></td>
                <td></td>
                <td><% print(formatMoney(c11.hrCostPerChild.cursive)); %></td>
                <td><% print(formatMoney(c12.hrCostPerChild.cursive)); %></td>
                <td><% print(getPercent(c11.hrCostPerChild.cursive, c12.hrCostPerChild.cursive)); %></td>
            </tr>
            <tr>
                <td align="right">设备费用/招生人数</td>
                <td><% print(formatMoney(c11.eqCostPerChild.data));%><% print(printGap(c11.eqCostPerChild.gap)); %></td>
                <td><% print(formatMoney(c12.eqCostPerChild.data));%><% print(printGap(c12.eqCostPerChild.gap)); %></td>
                <td><% print(getPercent(c11.eqCostPerChild.data, c12.eqCostPerChild.data)); %></td>
                <td></td>
                <td><% print(formatMoney(c11.eqCostPerChild.cursive)); %></td>
                <td><% print(formatMoney(c12.eqCostPerChild.cursive)); %></td>
                <td><% print(getPercent(c11.eqCostPerChild.cursive, c12.eqCostPerChild.cursive)); %></td>
            </tr>
            <tr>
                <td align="right">日常运作/招生人数</td>
                <td><% print(formatMoney(c11.opCostPerChild.data));%><% print(printGap(c11.opCostPerChild.gap)); %></td>
                <td><% print(formatMoney(c12.opCostPerChild.data));%><% print(printGap(c12.opCostPerChild.gap)); %></td>
                <td><% print(getPercent(c11.opCostPerChild.data, c12.opCostPerChild.data)); %></td>
                <td></td>
                <td><% print(formatMoney(c11.opCostPerChild.cursive)); %></td>
                <td><% print(formatMoney(c12.opCostPerChild.cursive)); %></td>
                <td><% print(getPercent(c11.opCostPerChild.cursive, c12.opCostPerChild.cursive)); %></td>
            </tr>
            <tr>
                <td align="right">房租/招生人数</td>
                <td><% print(formatMoney(c11.rtCostPerChild.data));%><% print(printGap(c11.rtCostPerChild.gap)); %></td>
                <td><% print(formatMoney(c12.rtCostPerChild.data));%><% print(printGap(c12.rtCostPerChild.gap)); %></td>
                <td><% print(getPercent(c11.rtCostPerChild.data, c12.rtCostPerChild.data)); %></td>
                <td></td>
                <td><% print(formatMoney(c11.rtCostPerChild.cursive)); %></td>
                <td><% print(formatMoney(c12.rtCostPerChild.cursive)); %></td>
                <td><% print(getPercent(c11.opCostPerChild.cursive, c12.opCostPerChild.cursive)); %></td>
            </tr>
            <tr>
                <td align="right">其他成本/招生人数</td>
                <td><% print(formatMoney(c11.otCostPerChild.data));%><% print(printGap(c11.otCostPerChild.gap)); %></td>
                <td><% print(formatMoney(c12.otCostPerChild.data));%><% print(printGap(c12.otCostPerChild.gap)); %></td>
                <td><% print(getPercent(c11.otCostPerChild.data, c12.otCostPerChild.data)); %></td>
                <td></td>
                <td><% print(formatMoney(c11.otCostPerChild.cursive)); %></td>
                <td><% print(formatMoney(c12.otCostPerChild.cursive)); %></td>
                <td><% print(getPercent(c11.otCostPerChild.cursive, c12.otCostPerChild.cursive)); %></td>
            </tr>
            <tr>
                <th class="active">利润/招生人数
                </th>
                <td><% print(formatMoney(c11.profitPerChild.data));%><% print(printGap(c11.profitPerChild.gap)); %></td>
                <td><% print(formatMoney(c12.profitPerChild.data));%><% print(printGap(c12.profitPerChild.gap)); %></td>
                <td><% print(getPercent(c11.profitPerChild.data, c12.profitPerChild.data)); %></td>
                <td></td>
                <td><% print(formatMoney(c11.profitPerChild.cursive)); %></td>
                <td><% print(formatMoney(c12.profitPerChild.cursive)); %></td>
                <td><% print(getPercent(c11.profitPerChild.cursive, c12.profitPerChild.cursive)); %></td>
            </tr>
            <tr>
                <th class="active">现金流</th>
                <th colspan="7"><% print(formatMoney(c11.s1007.data)); %></th>
            </tr>
            <tr>
                <th class="active">学生教师比例</th>
                <td colspan="7"><% print(formatMoney(parseFloat( parseInt(c11.s1001.data) / parseInt(c11.s1008.data) ))); %> (<%= parseInt(c11.s1001.data) %>:<%= parseInt(c11.s1008.data) %>)</td>
            </tr>
            <tr>
                <th class="active">学生管理人员比例</th>
                <td colspan="7"><% print(formatMoney(parseFloat( parseInt(c11.s1001.data) / parseInt(c11.s1009.data) ))); %> (<%= parseInt(c11.s1001.data) %>:<%= parseInt(c11.s1009.data) %>)</td>
            </tr>
            </tbody>
        </table>
    </div>
</div>
<div class="row">

    <div class="monthly-data-stats">
        <div class="col-sm-12 col-md-12 col-lg-12" id="container-cost">
            <div class="panel panel-default">
                <div class="panel-heading">
                    统计图表
                </div>
                <div class="panel-body">
                    <div class="chart-container" style="min-width: 310px; height: 400px; margin: 0 auto"></div>
                    <ul class="nav nav-pills">
                        <li><a dataType="enroll" href="javascript:void(0);" onclick="drawChart(this);">招生数据</a></li>
                        <li><a dataType="finance" href="javascript:void(0);" onclick="drawChart(this);">财务数据</a></li>
                        <li><a dataType="cost" href="javascript:void(0);" onclick="drawChart(this);">成本分析（比例）</a></li>
                        <li><a dataType="costval" href="javascript:void(0);" onclick="drawChart(this);">成本分析（金额）</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
</script>