<style>
    .list-group-item {
        display: table-row;
    }
</style>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('//mcampus/default/index')) ?></li>
        <li><?php echo CHtml::link(Yii::t('site', 'Routines'), array('//mcampus/default/index')) ?></li>
        <li class="active"><?php echo Yii::t('site', '课后课管理') ?></li>
    </ol>

    <div class="row">
        <!-- 左侧菜单 -->
        <div class="col-md-2 col-sm-2 mb10">
            <?php
            $this->widget('zii.widgets.CMenu', array(
                'items' => $this->module->getMenu(),
                'id' => 'pageCategory',
                'htmlOptions' => array('class' => 'nav nav-pills nav-stacked background-gray'),
                'activeCssClass' => 'active',
            ));
            ?>
        </div>

        <div class="col-md-2">
            <select autocomplete="off" class="form-control" id="selectstartyear">
                <option value="">请选择学年</option>
                <?php
                if(empty($_GET['year'])){
                    //默认最新的学年
                    $getUrlYear = $startyearArr[0];
                }else{
                    $getUrlYear = $_GET['year'];
                }
                foreach ($startyearArr as $value){
                    if(Yii::app()->language == 'zh_cn'){
                        $msg = ' 学年';
                    }else{
                        $msg =' School Year';
                    }
                    $schoolYear = $value.'-'.($value+1).$msg;
                    if($value == $getUrlYear){
                        echo "<option value='$value' selected>$schoolYear</option>";
                    }else{
                        echo "<option value='$value'>$schoolYear</option>";
                    }
                }
                ?>
            </select>
            <br>
            <br>
            <?php
            echo "<span class=span_year$year>";
            $selected_title = "";
            $program_type = "";
            $groupStartTime = "";
            $groupEndTime = "";

            foreach ($newCourseGroup as $year => $courseGroup){
                if($getUrlYear == $year){
                    echo "<ul class='nav nav-pills nav-stacked background-gray year$year'>";
                }else{
                    echo "<ul class='nav nav-pills nav-stacked background-gray year$year' style='display: none'>";
                }
                foreach ($courseGroup as $v) {
                    $href = $this->createUrl('index', array('groupId' => $v['id'],'year'=>$year));
                    $active = '';
                    $title = Yii::app()->language == "zh_cn" ?
                        empty($v['title_cn']) ? $v['title_en'] : $v['title_cn']
                        :
                        empty($v['title_en']) ? $v['title_cn'] : $v['title_en'];
                    if ($v['id'] == $groupId) {
                        $active = 'active';
                        $selected_title = $title;
                        $program_type = $v['program_type'];
                        $groupStartTime = $v['buy_from'];
                        $groupEndTime = $v['buy_end'];
                    }
//                    echo "<li class='{$active}'><a href='{$href}'>";
                    echo "<li class='{$active}' id='group_{$v['id']}' onClick='query({$v['id']})'><a href='#'>";
                    echo $title;
                    echo '</a></li>';
                }
                echo '</ul>';
            }
            echo '</span>';
            ?>
        </div>
        <div class="col-md-8">
            <div class="row J_check_wrap" id="viewData"></div>
        </div>
    </div>
<div>

<script>
    function query(groupId) {
        var _this = $('#group_' + groupId);
        console.log(_this);
        _this.parent().children().removeClass('active');
        _this.addClass('active');
        $.ajax({
            url: '<?php echo $this->createUrl('/mfinance/asa/queryAmount'); ?>',
            data: {groupId:groupId},
            type: 'POST',
            dataType: 'json',
            success: function(data) {
                if (data.state === 'fail') {
                    head.dialog.alert(data.message);
                    return false;
                }
                if (typeof(data.callback) !== 'undefined') {
                    eval(data.callback + '(data.data)');
                }
            },
            error: function() {
                alert("访问异常！");
            }
        });
    }
    $("#selectstartyear").change(function (){
        var id = $(this).val();
        $(".year"+id).show().siblings().hide();
    })
    function callback(data) {
        var html = '';
        html+='<p class="col-md-12"><button onclick="daochu()" class="btn btn-primary btn-sm pull-right"><?php echo Yii::t('user','Excel Export'); ?></button></p>'
        html += '<div class="col-md-12" id="block">';
        html += '<div class="table-responsive">';
        html += '<table class="table table-bordered table-hover">';
        html += '<thead>';
        html += '<tr>';
        html += '<th class="text-center">ID</th>';
        html += '<th class="text-center"><?php echo Yii::t("principal","Course Title"); ?></th>';
        html += '<th class="text-center"><?php echo Yii::t("asa","Purchase"); ?></th>';
        html += '<th class="text-center"><?php echo Yii::t("asa","Refund"); ?></th>'; //退款金额
        html += '<th class="text-center"><?php echo Yii::t("asa","Revenue"); ?></th>';//有效收入
        html += '<th class="text-center"><?php echo Yii::t("asa","VAT (6%)"); ?></th>';//收入税款
        html += '<th class="text-center"><?php echo Yii::t("asa","Wechat Fee (0.6%)"); ?></th>';//微信税款
        html += '<th class="text-center"><?php echo Yii::t("asa","Staff Salary"); ?></th>';//考勤支出
        html += '<th class="text-center"><?php echo Yii::t("asa","Adjustiment"); ?></th>';//调整金额
        html += '<th class="text-center"><?php echo Yii::t("asa","Vendor Payment"); ?></th>';//付款金额
        html += '<th class="text-center"><?php echo Yii::t("labels","Reimbursement"); ?></th>';//报销金额
        html += '<th class="text-center"><?php echo Yii::t("asa","Profit"); ?></th>';//实际金额
        html += '<th class="text-center"><?php echo Yii::t("asa","Profit Margin"); ?></th>';//利润率
        html += '</tr>';
        html += '</thead>';
        html += '<tbody>';

        for (var key in data['data']) {
            html += '<tr>';
            html += '<td>' + key + '</td>';
            html += '<td>' + data['data'][key].title_cn + '<br>' + data['data'][key].title_en + '</td>';
            html += '<td class="text-right">' + data['data'][key].amount + '</td>';
            html += '<td class="text-right">' + data['data'][key].refund + '</td>';
            html += '<td class="text-right">' + data['data'][key].income + '</td>';
            html += '<td class="text-right">' + data['data'][key].wage + '</td>';
            html += '<td class="text-right">' + data['data'][key].wechat + '</td>';
            html += '<td class="text-right">' + data['data'][key].teacher + '</td>';
            html += '<td class="text-right">' + data['data'][key].variation + '</td>';
            html += '<td class="text-right">' + data['data'][key].vendor + '</td>';
            html += '<td class="text-right">' + data['data'][key].reimbursement + '</td>';
            html += '<td class="text-right">' + data['data'][key].actual + '</td>';
            html += '<td class="text-right">' + data['data'][key].margin + '</td>';
            html += '</tr>';
        }

        html += '</tbody>';
        html += '<tfoot>';
        html += '<tr>';
        html += '<th colspan="2"><?php echo Yii::t("asa","Total"); ?></th>';
        html += '<th class="text-right">' + data['total'][0] + '</th>';
        html += '<th class="text-right">' + data['total'][1] + '</th>';
        html += '<th class="text-right">' + data['total'][2] + '</th>';
        html += '<th class="text-right">' + data['total'][8] + '</th>';
        html += '<th class="text-right">' + data['total'][9] + '</th>';
        html += '<th class="text-right">' + data['total'][3] + '</th>';
        html += '<th class="text-right">' + data['total'][4] + '</th>';
        html += '<th class="text-right">' + data['total'][5] + '</th>';
        html += '<th class="text-right">' + data['total'][6] + '</th>';
        html += '<th class="text-right">' + data['total'][7] + '</th>';
        html += '<th class="text-right">' + data['total'][10] + '</th>';
        html += '</tr>';
        html += '</tfoot>';
        html += '</table>';
        html += '</div>';
        
        $('#viewData').html(html);
    }
    
    
    function daochu(){
        var elt = document.getElementById('block');
        var wb = XLSX.utils.table_to_book(elt, {sheet:"<?php echo Yii::t('asa','Program Revenue'); ?>"});
        return XLSX.writeFile(wb, '<?php echo Yii::t('asa','Program Revenue'); ?>.xlsx');
    }
</script>

<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>