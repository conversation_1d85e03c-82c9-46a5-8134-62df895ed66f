<style>
	[v-cloak] {
		display: none;
	}
</style>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'HQ Operations'), array('default/index')); ?></li>
        <li><?php echo Yii::t('site','模板列表'); ?></li>
    </ol>
    <div id='vueData' v-cloak>
        <div class="panel panel-default">
            <!-- Default panel contents -->
            <div class="panel-heading">
             <h4 class='pull-left'>问卷模板列表</h4>
            <button type="button" class="btn btn-primary pull-right" @click='editTemplate(1)'>添加模板</button>
            <div style='clear:both'></div>
            </div>
            <table class='table'>
                <thead>
                    <tr>
                        <th>标题中文</th>
                        <th>标题英文</th>
                        <th>介绍中文</th>
                        <th>介绍英文</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                <tr v-for='(item,index) in templateData'>
                    <td width='100'>{{item.cn_title}}</td>
                    <td width='100'>{{item.en_title}}</td>
                    <td width='200'>{{item.cn_intro}}</td>
                    <td width='200'>{{item.en_intro}}</td>
                    <td width='150'>
                        <a :href="'<?php echo $this->createUrl('questionsList') ?>?survey_id='+item.id+''" class="btn btn-primary btn-sm" role="button"  target="_Blank">问题列表</a>
                        <button type="button" class="btn btn-primary btn-sm" @click='assigntemplate(item)'>分配模板</button>
                        <button type="button" class="btn btn-primary btn-sm " @click='editTemplate(item)'>编辑模板</button>
                        <button type="button" class="btn btn-primary btn-sm" @click='delTemplate(item,index)'>删除模板</button>
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
        <!-- 编辑 -->
        <div class="modal fade bs-example-modal-lg" id="company" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true"  data-backdrop="static">
            <div class="modal-dialog modal-lg">
                <?php $form = $this->beginWidget('CActiveForm', array(
                        'id' => 'expense-forms',
                        'enableAjaxValidation' => false,
                        'action' => ($model->id) ? $this->createUrl('expenesAdd', array('expenesId' => $model->id)) : $this->createUrl('updateTemplate'),
                        'htmlOptions' => array('class' => 'J_ajaxForm form-horizontal', 'role' => 'form', 'enctype' => "multipart/form-data"),
                    )); ?>
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                        <h4 class="modal-title" id="myModalLabel">编辑信息</h4>
                    </div>
                    <div class="modal-body">
                        <form class="form-horizontal">
                            <input type="text" value='0' name='SurveyReturnTemplate[status]' class="hidden">
                            <input type="text" :value='editTem.id' name='id'  class="hidden">
                            <div class="form-group">
                                <label for="cn_title" class="col-sm-2 control-label">中文标题：</label>
                                <div class="col-sm-10">
                                    <input type="text" class="form-control"  placeholder="请输入中文标题"  :value='editTem.cn_title' id="cn_title"  name='SurveyReturnTemplate[cn_title]'>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="en_title" class="col-sm-2 control-label">英文标题：</label>
                                <div class="col-sm-10">
                                    <input type="text" class="form-control" placeholder="请输入英文标题"  :value='editTem.en_title' id="en_title" name='SurveyReturnTemplate[en_title]'>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="en_intro" class="col-sm-2 control-label">中文提示文字</label>
                                <div class="col-sm-10">
                                    <textarea class="form-control" placeholder="请输入中文提示文字" rows='4' :value='editTem.cn_intro' id="cn_intro" name='SurveyReturnTemplate[cn_intro]'></textarea>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="cn_intro" class="col-sm-2 control-label">英文提示文字</label>
                                <div class="col-sm-10">
                                    <textarea class="form-control" placeholder="请输入英文提示文字"  rows='4'  :value='editTem.en_intro' id="en_intro" name='SurveyReturnTemplate[en_intro]'></textarea>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="cn_intro" class="col-sm-2 control-label">中文备注文字</label>
                                <div class="col-sm-10">
                                    <textarea class="form-control" placeholder="请输入中文备注文字"  rows='4'  :value='editTem.cn_intro2' id="cn_intro2" name='SurveyReturnTemplate[cn_intro2]'></textarea>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="cn_intro" class="col-sm-2 control-label">英文备注文字</label>
                                <div class="col-sm-10">
                                    <textarea class="form-control" placeholder="请输入英文备注文字"  rows='4'  :value='editTem.en_intro2' id="en_intro2" name='SurveyReturnTemplate[en_intro2]'></textarea>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="cn_intro" class="col-sm-2 control-label">状态</label>
                                <div class="col-sm-10">
                                    <label class="checkbox-inline">
                                        <template v-if='editTem.status!="0"'>
                                            <input type="checkbox" checked="checked" v-model='editTem.status' value='1' name='SurveyReturnTemplate[status]'> 是否开启
                                        </template>
                                        <template v-else>
                                            <input type="checkbox" value='1' name='SurveyReturnTemplate[status]'> 是否开启
                                        </template>
                                    </label>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary J_ajax_submit_btn">确定</button>
                        <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                    </div>
                </div>
                <!-- /.modal-content -->
                <?php $this->endWidget(); ?>
            </div>
            <!-- /.modal -->
        </div>
         <!-- 分配校园 -->
         <div class="modal fade bs-example-modal-lg" id="addgroup" tabindex="-1" role="dialog" data-backdrop="static">
            <div class="modal-dialog modal-lg" >
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">
                        &times;
                    </button>
                        <h4 class="modal-title" id="myModalLabel">
                        分配模板
                    </h4>
                    </div>
                    <div class="modal-body">
                        <div class="form-horizontal">
                        <!--  v-if='!assignedTemplate.class_type' -->
                            <div class="form-inline row mb15">
                                <label for="inputPassword" class="col-sm-2 control-label">开放日期：</label>
                                <div class="col-sm-10">
                                    <input type="text" class="form-control" id="startTime" v-model='start_timestamp' placeholder="开始时间">
                                    —
                                    <input type="text" class="form-control" id="endTime"  v-model='end_timestamp'  placeholder="结束时间">
                                    <span>开放到结束日期24点</span>
                                </div>
                            </div>
                            <div class="form-group">
								<label for="inputPassword" class="col-sm-2 control-label">选择校园：</label>
								<div class="col-sm-10">
									<template v-for='Data in assignedTemplate.branchInfo'>
										<label class="checkbox-inline">
											<input type="checkbox" id="inlineCheckbox1" v-model='selectSchool' :value="Data"  @input="input(Data.type)" name='Vendor[vendor_city][]'> {{Data.title}}
										</label>
									</template>
								</div>
							</div>
                            <div class="form-group"  v-if='selectSchool.length!=0'>
								<label for="inputPassword" class="col-sm-2 control-label">选择班级：</label>
								<div class="col-sm-10">
                                    <template v-if='selectSchool[0].type==20'>
                                        <template v-for='(Data,index,key) in assignedTemplate.classInfo[0]'>
                                            <label class="checkbox-inline">
                                                <input type="checkbox" id="inlineCheckbox1" :value='index' v-model='classids' > {{Data}}
                                            </label>
                                        </template>
                                    </template>
                                    <template v-else>
                                        <template v-for='(Data,index,key) in assignedTemplate.classInfo[1]'>
                                            <label class="checkbox-inline">
                                                <input type="checkbox" id="inlineCheckbox1" :value='index' v-model='classids'> {{Data}}
                                            </label>
                                        </template>
                                    </template>
								</div>
							</div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" @click='updateAssigned()' :disabled="disabled">确认</button>
                        <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
	var templateData = <?php echo json_encode($templateData) ?>;//增加得返校模板
    console.log(templateData)
    var datalist = new Vue({
        el: "#vueData",
        data: {
            templateData:templateData,
            editTem:'',
            thisData:'',
            branchData:'',
            classData:'',
            assignedTemplate:'',
            selectSchool:[],
            schoolType:[],
            classids:[],
            start_timestamp:'',
            end_timestamp:'',
            disabled:false
        },
        methods: {
            input(type){
                if(this.selectSchool.length!=0){
                    if(this.selectSchool[0].type!=type){
                        resultTip({
                            error: 'warning',
                            msg: '启明星和艾毅不可同时选择'
                        });
                        this.selectSchool.splice(this.selectSchool.length-1,1);
                    }
                }
            },
            assigntemplate(data){
                var that=this
                $( "#startTime" ).datepicker({
                    dateFormat: "yy-mm-dd",
                    onClose : function(dateText, inst){//当日期面板关闭后触发此事件（无论是否有选择日期）
                        that.start_timestamp=dateText
                    }
                });
                $( "#endTime" ).datepicker({
                    dateFormat: "yy-mm-dd",
                    onClose : function(dateText, inst){//当日期面板关闭后触发此事件（无论是否有选择日期）
                        that.end_timestamp=dateText
                    }
                });
                console.log(data)
                var that=this
                that.thisData=data
                $.ajax({
                    url: '<?php echo $this->createUrl("assigntemplate")?>',
                    type: "post",
                    async: true,
                    dataType: 'json',
                    data: {
                        survey_id:data.id
                    },
                    success: function(data) {
                        that.classData=data.data.classInfo
                        that.branchData=data.data.branchInfo
                        that.assignedTemplate=data.data
                        if(data.data.class_type){
                            that.classids=data.data.class_type
                        }else{
                            that.classids=[]
                        }
                        that.selectSchool=[]
                        if(data.data.branchid){
                            for(var i=0;i<data.data.branchInfo.length;i++){
                                for(j=0;j<data.data.branchid.length;j++){
                                    if(data.data.branchInfo[i].branchid==data.data.branchid[j]){
                                        that.selectSchool.push(data.data.branchInfo[i]);
                                    }
                                }
                            }
                        }else{
                            that.selectSchool=[]
                        }
                        $('#addgroup').modal('show')
                    },
                    error: function(data) {
                        console.log(data)
                        alert("请求错误")
                    }
                });
            },
            updateAssigned(){
                var that=this
                that.disabled=true
                var branchids=[]
                for(var i=0;i<this.selectSchool.length;i++){
                    branchids.push(this.selectSchool[i].branchid)
                }
                console.log(branchids)
                console.log(that.start_timestamp)
                console.log(that.end_timestamp)
                // return
                $.ajax({
                    url: '<?php echo $this->createUrl("addreturn")?>',
                    type: "post",
                    async: true,
                    dataType: 'json',
                    data: {
                        tid:that.thisData.id,
                        branchids:branchids,
                        classids:that.classids,
                        start_timestamp:that.start_timestamp,
                        end_timestamp:that.end_timestamp
                    },
                    success: function(data) {
                        that.disabled=false
                        if(data.state == 'success') {
                            console.log(data)
                            $('#addgroup').modal('hide')
                            resultTip({
                                "msg": data.message
                            })
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        that.disabled=false
                        console.log(data)
                        alert("请求错误")
                    }
                });
            },
            editTemplate(data){
                console.log(data)
                if(data==1){
                    this.editTem=''
                }else{
                    this.editTem=data
                }
                $('#company').modal('show')
            },
            delTemplate(data,index){
                $.ajax({
                    url: '<?php echo $this->createUrl("deleteTemplate")?>',
                    type: "post",
                    async: true,
                    dataType: 'json',
                    data: {
                        survey_id:data.id
                    },
                    success: function(data) {
                        console.log(data)
                        if(data.state == 'success') {
                            datalist.templateData.splice(index,1);
                            resultTip({"msg": data.message})
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        console.log(data)
                        alert("请求错误")
                    }
                });
            },
            questionsList(data){

                console.log(data)
            },

        }
    })
    function  cbSuccess(data){
        if(datalist.editTem==''){
            datalist.templateData.push(data)
        }else{
            for(var i=0;i<datalist.templateData.length;i++){
                if(datalist.templateData[i].id==data.id){
                    datalist.templateData.splice(i,1,data);
                }
            }
        }
        $('#company').modal('hide')
        resultTip({
            "msg": '添加成功'
        })
    }
</script>

