<style>
	[v-cloak] {
		display: none;
	}
	#mapExample{
		height: 500px
	}
	#log-form{
		display: inline-block;
	}
	.text-left{
		text-align:left !important;
	}
	.maxHeight{
		max-height:500px;
		overflow-y: scroll;
	}
	.file {
		position: relative;
		display: inline-block;
		border: 1px solid #cccccc;
		border-radius: 4px;
		padding: 4px 12px;
		overflow: hidden;
		color: #333333;
		text-decoration: none;
		text-indent: 0;
		line-height: 20px;
		float:right
	}
	.file input {
		position: absolute;
		font-size: 100px;
		right: 0;
		top: 0;
		opacity: 0;
	}
	.file:hover {
		background: #e6e6e6;
		border-color: #adadad;
		color: #333333;
		text-decoration: none;
	}
</style>
<div class="container-fluid">
	<ol class="breadcrumb">
		<li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('//mcampus/default/index')) ?></li>
        <li><?php echo CHtml::link(Yii::t('site','Campus Support'), array('//mcampus/default/index'))?></li>
		<li class="active">班车站点</li>
	</ol>
	<div class="row">
        <div class="col-md-2 col-sm-2">
            <ul class="nav nav-pills nav-stacked text-left background-gray" id="pageCategory">
                <li class="" ><a href="<?php echo $this->createUrl('index', array('branchId' => $this->branchId)); ?>">学生信息收集</a></li>
                <li class="active" ><a href="<?php echo $this->createUrl('busStation', array('branchId' => $this->branchId)); ?>">班车站点</a></li>
            </ul>
        </div>
		<div class="col-md-10 col-sm-10" id='map' v-cloak>
			<div>
				<div class="btn-group wid pull-left">
					<button type="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" class="btn btn-default dropdown-toggle  wid">{{startYearList[yid]}} <span class="caret"></span></button>
					<ul class="dropdown-menu wid">
						<li v-for='(data,index) in YearList'>
							<a href="javascript:;" @click='getList(data.yid)'>{{data.year}} </a>
						</li>
					</ul>
				</div>
                <?php if($this->branchId != 'BJ_IASLT'){?>
				<button type="button" class="btn btn-default ml10" @click='copyCartSite()'><?php echo Yii::t('global','复制站点');?></button>
				<?php }?>
                <div class='pull-right'>
                    <?php if($this->branchId != 'BJ_IASLT'){?>
					<a class="btn btn-default ml10" href="http://mega.ivymik.cn/bus_stop_win.csv"> <?php echo Yii::t('user','下载Windows模板'); ?></a>
					<a class="btn btn-default ml10" href="http://mega.ivymik.cn/bus_stop_mac.csv"> <?php echo Yii::t('user','下载Mac模板'); ?></a>
                    <button type="button" class="btn btn-default ml10" @click='modify(0)'><?php echo Yii::t('global','手动添加');?></button>
					<!-- <button type="file" class="btn btn-default  mr10"><?php echo Yii::t('global','批量添加');?></button>
					<input type="file" id="load_xls" name="file" @change ="uploadFile()"> -->
					<a href="javascript:;" class="file ml10">批量添加
						<input type="file" id="load_xls" name="file" @change ="uploadFile()">
					</a>
                    <?php }?>
				</div>
				
			</div>
			<div class="clearfix"></div>
			<div class="row mt15">
				<div id='site' class="col-md-6">
					<div class="panel panel-default">
						<div class="panel-heading">
						<div class="form-horizontal">
						    <label for="inputEmail3" class="col-sm-2 text-left control-label">地图</label>
						    <div class="col-sm-10">
						    	<input name="lon" class="hidden">
								<input name="lat" class="hidden">
						        <input type="text" name="resourceAddress" class="form-control" id="address" placeholder="站点名称" disabled>
						    </div>
						</div>
						<div class="clearfix"></div>
						</div>
						<div class="panel-body" id='mapExample'>

						</div>
						
					</div>
				</div>
				<div class="col-md-6">
					<div class="panel panel-default maxHeight" v-if='city!=""'>
						<div class="panel-heading">
							<span>新增站点 <span class="badge">{{city.length}}</span></span>
						</div>
						<table class="table">
							<thead>
								<tr>
									<th>小区中文</th>
									<th>小区英文</th>
									<th>停车点中文</th>
									<th>停车点英文</th>
								</tr>
							</thead>
							<tbody>
								<template v-for='(list,index) in city'>
									<template  v-if='list.update==1'>
										<tr class="info">
											<td width="25%">{{list[0]}}</td>
											<td width="25%">{{list[1]}}</td>
											<td width="25%">{{list[2]}}</td>
											<td width="25%">{{list[3]}}</td>
										</tr>
									</template>
									<template  v-else>
										<tr @click="markLocation(list,index)" :class="{warning:index==importIndex}">
											<td width="25%">{{list[0]}}</td>
											<td width="25%">{{list[1]}}</td>
											<td width="25%">{{list[2]}}</td>
											<td width="25%">{{list[3]}}</td>
										</tr>
									</template>
								</template>
							</tbody>
						</table>
					</div>
					<p  v-if='city!=""'>
						<a href="javascript:;" class="btn btn-primary" id='update' @click='subcarlist()'>确认添加</a>
						<a href="javascript:;" class="btn btn-default" id='update' @click='delcarlist()'>删除</a>
					</p>
				</div>
			</div>
			<div class="panel panel-default">
					<div class="panel-heading align-items flex">
						<span class='flex1'>
							<span>已有站点</span><span>（{{carSiteList.length}}个）</span>
						</span>
						<button type="button" v-if='carSiteList.length!=0' class="btn btn-primary" @click='exportSite'>导出站点</button>
					</div>
					<table class="table" v-if='carSiteList.length!=0'>
						<thead>
							<tr>
								<th>小区中文</th>
								<th>小区英文</th>
								<th>停车点中文</th>
								<th>停车点英文</th>
                                <?php if($this->branchId != 'BJ_IASLT'){?>
                                    <th>操作</th>
                                <?php }?>
							</tr>
						</thead>
						<tbody>
							<template v-for='(list,idx) in carSiteList'>
								<tr class=""  @click="showlocation(list,idx)" :class="{warning:idx==current}">
									<td width="22%">{{list.name_cn}}</td>
									<td width="22%">{{list.name_en}}</td>
									<td width="22%">{{list.park_address_cn}}</td>
									<td width="22%">{{list.park_address_en}}</td>
                                    <?php if($this->branchId != 'BJ_IASLT'){?>
									<td width="22%"  @click.stop>
										<div>
											<a href="javascript:;" class="btn btn-xs btn-primary" @click='modify(list)'><span class="glyphicon glyphicon-pencil"></span></a>
											<a href="javascript:;" class="btn btn-xs btn-danger" @click='remove(list.id,idx)'><span class="glyphicon glyphicon-trash"></span></a>
										</div>
									</td>
                                    <?php }?>
								</tr>
							</template>
						</tbody>
					</table>
					<p v-else class="panel-body">暂无数据</p>
				</div>
			<div class="modal fade bs-example-modal-lg" id="modify" tabindex="-1" role="dialog" data-backdrop="static">
                <div class="modal-dialog" >
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-hidden="true">
                            &times;
                        </button>
                            <h4 class="modal-title" id="myModalLabel">
                            更新
                        </h4>
                        </div>
                        <div class="modal-body">
                            <div class="form-horizontal">
                                <div class="form-group">
                                    <label for="inputEmail3" class="col-sm-2 control-label">小区中文</label>
                                    <div class="col-sm-10">
                                      <input type="text" class="form-control" id='name_cn' :value='modifyAddress.name_cn'>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="inputEmail3" class="col-sm-2 control-label">小区英文</label>
                                    <div class="col-sm-10">
                                      <input type="text" class="form-control" id='name_en' :value='modifyAddress.name_en'>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="inputEmail3" class="col-sm-2 control-label">停车点中文</label>
                                    <div class="col-sm-10">
                                      <input type="text" class="form-control" id='park_address_cn' :value='modifyAddress.park_address_cn'>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="inputEmail3" class="col-sm-2 control-label">停车点英文</label>
                                    <div class="col-sm-10">
                                      <input type="text" class="form-control" id='park_address_en' :value='modifyAddress.park_address_en'>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
                            <button type="button" class="btn btn-primary" @click='updateAddress()'>更新</button>
                        </div>
                    </div>
                </div>
            </div>
			<div class="modal fade bs-example-modal-lg" id="copySite" tabindex="-1" role="dialog" data-backdrop="static">
                <div class="modal-dialog" >
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-hidden="true">
                            &times;
                        </button>
						<h4 class="modal-title" id="myModalLabel">
                        复制站点
                        </h4>
                        </div>
                        <div class="modal-body">
							<div class="form-horizontal">
								<div class="form-group">
									<label for="inputEmail3" class="col-sm-2 control-label">当前学年：</label>
									<div class="col-sm-10">
									 <p class='mt10'>{{startYearList[yid]}}</p> 
									</div>
								</div>
								<div class="form-group">
									<label for="inputPassword3" class="col-sm-2 control-label">复制到：</label>
									<div class="col-sm-10">
										<div class="checkbox" v-for='(list,index) in YearList'>
											<label>
												<input type="checkbox" :value="list.yid" v-model='to_yid'  :disabled="list.yid==yid">
												{{list.year}}
											</label>
										</div> 
									</div>
								</div>
							</div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
                            <button type="button" class="btn btn-primary" @click='saveCopyData()'>更新</button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal fade" id="delModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
                <div class="modal-dialog modal-sm" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                            <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("newDS", "Delete");?></h4>
                        </div>
                        <div class="modal-body">
                            <div><?php echo Yii::t("newDS", "Confirm to delete this item?");?></div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                            <button type="button" class="btn btn-primary" @click='remove(0,0,"hide")'><?php echo Yii::t("newDS", "Delete");?></button>
                        </div>
                    </div>
                </div>
            </div>
		</div>

	</div>
</div>
<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>
<script type="text/javascript">
  window._AMapSecurityConfig = {
    serviceHost: "https://apps.ivyonline.cn/_AMapService",
  };
</script>
<script type="text/javascript" src="https://webapi.amap.com/maps?v=1.4.12&key=166c9d81f042ef0ae55c7a2c85e19a7e"></script>
<script>
	var mapdata = new Vue({
		el: "#map",
		data: {
			carSiteList: {},
			startYearList: {},
			yid:'',
			YearList:[],
			city: [],
			current: '-1',
			carSiteId: '',
			addLocation:'',
			modifyAddress:'',
			role:'',
			markers:[],
			addMarkers:[],
			importIndex:'-1',
			to_yid:[],
            remove_id:'',
            remove_idx:'',
		},
		created: function() {
			let that=this
			that.YearList=[]
			$.ajax({
                url: '<?php echo $this->createUrlReg('getYearList'); ?>',
                type: 'post',
                dataType: 'json',
                data: {
                },
                success: function(data) {
                    if(data.state == 'success') {
                        that.YearList=data.data.yearList
						for(var i=0;i<data.data.yearList.length;i++) {
							that.startYearList[data.data.yearList[i].yid]=data.data.yearList[i].year
						}
                    } else {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    }
                },
                error: function(data) {
                    resultTip({
                        error: 'warning',
                        msg: '请求错误'
                    });
                }
            })
			this.getList('')
		},
		computed: {},
		mounted: function() {
			
		},
		methods: {
			init() {
				map = new AMap.Map('mapExample', {
					resizeEnable: true,
					zoom: 12
					/*center: [116.397428, 39.90923] */
				});
				AMap.plugin(['AMap.ToolBar','AMap.Scale'],
			    function(){
			            map.addControl(new AMap.Scale());
			    });
				AMap.service('AMap.Geocoder', function() { //回调函数
					geocoder = new AMap.Geocoder({
						city: "北京", //城市，默认：“全国”
					});
				});
				
			},
			getList(yid){
				let that=this
				$.ajax({
					url: '<?php echo $this->createUrlReg('CarSiteList'); ?>',
					type: 'post',
					dataType: 'json',
					data: {
						"yid": yid,
					},
					success: function(data) {
						if(data.state == 'success') {
							that.carSiteList=data.data.list
							that.yid=data.data.yid
							that.init()
							that.defaultPot()
						} else {
							resultTip({
								error: 'warning',
								msg: data.message
							});
						}
					},
					error: function(data) {
						resultTip({
							error: 'warning',
							msg: '请求错误'
						});
					}
				})
			},
			//显示已添加的站点
			defaultPot(){
				map.remove(this.markers);
				map.remove(this.addMarkers);
				for(var i = 0; i < this.carSiteList.length; i++) {
					var marker = new AMap.Marker({
						icon: 'https://webapi.amap.com/theme/v1.3/markers/n/mark_b.png',
						offset: new AMap.Pixel(-13, -30),
					});
					this.markers.push(marker)
					marker.setPosition(new AMap.LngLat(this.carSiteList[i].longitude, this.carSiteList[i].latitude));
					marker.setMap(map);
					if(GV.lang=="zh_cn"){
				        marker.content ="<p class='mt15'><b>小区名：</b><span>"+this.carSiteList[i].name_cn+"</span></p>"+
            				"<p><b>停车地点：</b><span>"+this.carSiteList[i].park_address_cn+"</span></p>";
					}else{
						marker.content ="<p class='mt15'><b>小区名：</b><span>"+this.carSiteList[i].name_en+"</span></p>"+
            				"<p><b>停车地点：</b><span>"+this.carSiteList[i].park_address_en+"</span></p>";
					}
                    marker.on('click', markerClick);
				}
				map.setFitView()
			},
			//点击新增定位
			markLocation(list,index) {
				infoWindow.close();//关闭点击提示信息
				for(var i=0;i<this.carSiteList.length;i++){
					if(list[0]+list[2]==this.carSiteList[i].name_cn+this.carSiteList[i].park_address_cn){
						resultTip({
							error: 'warning',
							msg: '定位已标注'
						});
						this.addLocation=''
						this.importIndex=-1
						this.defaultPot()
						this.showlocation(this.carSiteList[i],-2)
						map.off('click', initMapClick);
						return
					}
				}
				this.importIndex=index
				this.current = -1
				this.addLocation=list
				locationname(list[0],list[2])
				
			},
			//查看已有定位
			showlocation(list,index){
				map.remove(this.addMarkers);
				if(index!=-2){
				this.current = index
				this.importIndex=-1
				}
				$("input[name=resourceAddress]").val(list.site_name);
				var marker = new AMap.Marker({
						icon: "http://webapi.amap.com/theme/v1.3/markers/n/mark_b.png",
						position: [list.longitude,list.latitude],
					});
				infoWindow.close();
				map.setFitView(marker); // 执行定位
			},
			//修改新增
			modify(list){
				if(list!=0){
					this.role=1
					this.modifyAddress=list

				}else{
					this.role=0
					this.modifyAddress=''
				}
				 $('#modify').modal('show')
			},
			//导出模板
			updateAddress(){
				if(this.role==0){
					this.importIndex=-1
					this.city.unshift([$('#name_cn').val(),$('#name_en').val(),$('#park_address_cn').val(),$('#park_address_en').val()])
					$('#modify').modal('hide')
					return
				}
				$.ajax({
					url: '<?php echo $this->createUrlReg('editCarsite'); ?>',
					type: 'post',
					dataType: 'json',
					data: {
						'data[name_cn]' : $("#name_cn").val(),
				        'data[name_en]' : $("#name_en").val(),
				        'data[park_address_cn]' : $("#park_address_cn").val(),
				        'data[park_address_en]': $("#park_address_en").val(),
						'id':this.modifyAddress.id,
						'data[siteName]':this.modifyAddress.site_name,
						'data[longitude]':this.modifyAddress.longitude,
						'data[latitude]': this.modifyAddress.latitude,
					},
					success: function(data) {
						if(data.state == 'success') {
								Vue.set(mapdata.modifyAddress,'name_cn', $('#name_cn').val())
                                Vue.set(mapdata.modifyAddress,'name_en', $('#name_en').val())
                                Vue.set(mapdata.modifyAddress,'park_address_cn',$("#park_address_cn").val())
                                Vue.set(mapdata.modifyAddress,'park_address_en',$("#park_address_en").val())
							resultTip({
								msg: data.state
							});
							infoWindow.close();
							mapdata.defaultPot()
							$('#modify').modal('hide')
						} else {
							resultTip({
								error: 'warning',
								msg: data.message
							});
						}
					},
					error: function(data) {
						resultTip({
							error: 'warning',
							msg: '请求错误'
						});
					}
				})
			},
			//删除
			remove(id,index,rule){
                if(rule != 'hide'){
                    this.remove_id=id
                    this.remove_idx=index
                    $('#delModal').modal('show')
                    return
                }
				$.ajax({
					url: '<?php echo $this->createUrlReg('delCarsite'); ?>',
					type: 'post',
					dataType: 'json',
					data: {
						'id':this.remove_id,
					},
					success: function(data) {
						if(data.state == 'success') {
                            $('#delModal').modal('hide');
							mapdata.carSiteList.splice(this.remove_idx,1);
							mapdata.defaultPot()
							resultTip({
								msg: data.state,
							});
						} else {
							resultTip({
								error: 'warning',
								msg: data.message
							});
						}
					},
					error: function(data) {
						resultTip({
							error: 'warning',
							msg: '请求错误'
						});
					}
				})
			},
			delcarlist(){
				this.city.splice(this.importIndex,1);
				this.defaultPot()
			},
			//提交
			subcarlist(){
				$("#update").attr('disabled', true);
				if(this.importIndex==-1){
					resultTip({
							error: 'warning',
							msg:'请先选择新增站点'
						});
					$("#update").attr('disabled', false);
					return
				}
				lon=$("input[name=lon]").val().toString()
                lat=$("input[name=lat]").val().toString()
                for(var i=0;i<this.carSiteList.length;i++){
                	if(lon.substring(0,lon.length-2)+lat.substring(0,lat.length-2)==this.carSiteList[i].longitude.substring(0,this.carSiteList[i].longitude.length-2)+this.carSiteList[i].latitude.substring(0,this.carSiteList[i].latitude.length-2)){
                		resultTip({
							error: 'warning',
							msg: '定位已标注'
						});
						this.addLocation=''
						this.importIndex=-1
						this.defaultPot()
						this.showlocation(this.carSiteList[i],-2)
						map.off('click', initMapClick);
                		return
                	}
				}
				$.ajax({
					url: '<?php echo $this->createUrlReg('addCarsite'); ?>',
					type: 'post',
					dataType: 'json',
					data: {
						'data[site_name]': $("input[name=resourceAddress]").val(),
						'data[longitude]': $("input[name=lon]").val(),
						'data[latitude]': $("input[name=lat]").val(),
						'data[park_address_cn]' :this.addLocation[2],
				        'data[park_address_en]': this.addLocation[3],
						'data[name_cn]' :this.addLocation[0],
				        'data[name_en]' : this.addLocation[1],
				        'data[yid]' : this.yid,
					},
					success: function(data) {
						$("#update").attr('disabled', false);
						if(data.state == 'success') {
							mapdata.city.splice(mapdata.importIndex,1);
							Vue.set(mapdata.addLocation,'update','1')
							mapdata.carSiteList.push(data.data)
							mapdata.defaultPot()
							lnglatXY=''
							map.off('click', initMapClick);
							resultTip({
								msg: data.state
							});
						} else {
							resultTip({
								error: 'warning',
								msg: data.message
							});
						}
					},
					error: function(data) {
						$("#update").attr('disabled', false);
						resultTip({
							error: 'warning',
							msg: '请求错误'
						});
					}
				})
			},
			copyCartSite(){
				$('#copySite').modal('show')

			},
			saveCopyData(){
				$.ajax({
					url: '<?php echo $this->createUrlReg('putCopyCartSite'); ?>',
					type: 'post',
					dataType: 'json',
					data: {
						"from_yid": this.yid,
						"to_yid": this.to_yid
					},
					success: function(data) {
						if(data.state == 'success') {
							$('#copySite').modal('hide')
							resultTip({
								msg: data.state
							});
						} else {
							resultTip({
								error: 'warning',
								msg: data.message
							});
						}
					},
					error: function(data) {
						resultTip({
							error: 'warning',
							msg: '请求错误'
						});
					}
				})
			},
			uploadFile() {
				var myform = new FormData();
				myform.append('EventImportForm[csv]', '');
				myform.append('EventImportForm[csv]',$('#load_xls')[0].files[0]);
				let that=this
				$.ajax({
					url: '<?php echo $this->createUrlReg('import'); ?>',
					type: "POST",
					data:myform,
					dataType: 'json',
					contentType: false,
					processData: false,
					success: function (data) {
						if(data.state == 'success') {
							for(var i=0;i<data.data.length;i++){
								that.city.push(data.data[i])
							}
							resultTip({
								msg: data.state
							});
						} else {
							resultTip({
								error: 'warning',
								msg: data.message
							});
						}
					},
					error:function(data){
						console.log(data)
					}
				});
			},
			exportSite(){
				let year=this.startYearList[this.yid]
				let branchId = '<?php echo $_GET['branchId'] ?>';
				var result =branchId.split('_')
				var title=year+' '+ result[1] +' 校车站点.xlsx'
				const ws_name = "SheetJS";
				var exportDatas = [];
				for(var i=0;i<this.carSiteList.length;i++){
					exportDatas.push(
						{
						'小区中文':this.carSiteList[i].name_cn,
						'小区英文':this.carSiteList[i].name_en,
						'停车点中文':this.carSiteList[i].park_address_cn,
						'停车点英文':this.carSiteList[i].park_address_en,
						'经度':this.carSiteList[i].longitude,
						'纬度':this.carSiteList[i].latitude,
						'地址':this.carSiteList[i].site_name,
						});
				}
				var wb=XLSX.utils.json_to_sheet(exportDatas,{
					origin:'A1',// 从A1开始增加内容
					header: ['小区中文','小区英文','停车点中文','停车点英文','经度','纬度','地址'
					],
				});
				const workbook = XLSX.utils.book_new();
				XLSX.utils.book_append_sheet(workbook, wb, ws_name);
				const wbout = XLSX.write(workbook, {bookType: 'xlsx', type: 'array'});
				const blob = new Blob([wbout], {type: 'application/octet-stream'});
				let link = document.createElement('a');
				link.href = URL.createObjectURL(blob);
				link.download = title;
				link.click();
				setTimeout(function() {
					// 延时释放掉obj
					URL.revokeObjectURL(link.href);
					link.remove();
				}, 500);
			}
		},
	})
	//回显
	myMapViewLocation()
	function myMapViewLocation(mlon, mlat){
	    if(mlon&&mlat){
	        lnglatXY = [mlon,mlat];
	        $("input[name=lon]").val(mlon);
	       	$("input[name=lat]").val(mlat);
	        addMarker(lnglatXY);
	    }
	}
	// 填写地址
	function writeAddress(lnglatXY){
		var geocoder = new AMap.Geocoder({
	        city : "北京", //城市，默认：“全国”
	    });
	    geocoder.getAddress(lnglatXY, function(status, result) {
	        if (status === 'complete' && result.info === 'OK') {
	        	geocoder_CallBack(result);
	        }
	    });
	}
	//地址回调
	function geocoder_CallBack(data) {
	    var address = data.regeocode.formattedAddress; //返回地址描述

	    $("input[name=resourceAddress]").val(address);
	}
	//打点
	var t=1;
	function addMarker(lnglatXY) {
		map.remove(mapdata.addMarkers);
		mapdata.addMarkers=[]
	    if(t == 1) {
	        marker = new AMap.Marker({
	        	icon: "http://webapi.amap.com/theme/v1.3/markers/n/mark_r.png",
	            position: lnglatXY,
	            offset: new AMap.Pixel(-13, -30),
	            // 设置是否可以拖拽
	            draggable: true,
	            cursor: 'move',
	            // 设置拖拽效果
	            raiseOnDrag: true
	        });
	        marker.setMap(map);
	        map.setFitView();// 执行定位
	        t++;
	    }
	    //修改标点位置
	    if(t != 1){
	        marker.on('dragend', showInfoM);
	        marker.setPosition(lnglatXY);
	        mapdata.addMarkers.push(marker)
	        map.setCenter(lnglatXY);
	        marker.setMap(map);
	        map.on('click',initMapClick)
	        map.setFitView(marker);// 执行定位
	    }
	}
	var infoWindow = new AMap.InfoWindow({offset: new AMap.Pixel(0, -30)});//信息窗口
	//点击点出现详情
	function markerClick(e){
		infoWindow.setContent(e.target.content);
        infoWindow.open(map, e.target.getPosition());
	}
	//拖拽重新定位获取地址
    function showInfoM(e){
        writeAddress([e.lnglat.lng,e.lnglat.lat]);
        $("input[name=lon]").val(e.lnglat.lng);
        $("input[name=lat]").val(e.lnglat.lat);
    }
    //地图注册click事件获取鼠标点击出的经纬度坐标
	function initMapClick(e){
		writeAddress([e.lnglat.lng, e.lnglat.lat]);
		addMarker([e.lnglat.lng, e.lnglat.lat]);
	}
	//根据地址获取经纬度
    function locationname(address,redirectAdd) {
    	var geocoder = new AMap.Geocoder({
	        city : "北京", //城市，默认：“全国”
	    });
   		AMap.plugin('AMap.Geocoder', function() {       
	        geocoder.getLocation(address+redirectAdd, function(status, result) {
	            if (status === 'complete' && result.info === 'OK') {
	     
	                var lon = result.geocodes[0].location.lng;
	                var lat = result.geocodes[0].location.lat;
	                $("input[name=lon]").val(lon);
	                $("input[name=lat]").val(lat);
	                $("input[name=resourceAddress]").val(address+redirectAdd);
	                lnglatXY = [lon, lat];
	                lon=lon.toString()
	                lat=lat.toString()
	                for(var i=0;i<mapdata.carSiteList.length;i++){
	                	if(lon.substring(0,lon.length-2)+lat.substring(0,lat.length-2)==mapdata.carSiteList[i].longitude.substring(0,mapdata.carSiteList[i].longitude.length-2)+mapdata.carSiteList[i].latitude.substring(0,mapdata.carSiteList[i].latitude.length-2)){
	                		resultTip({
								error: 'warning',
								msg: '定位已标注'
							});
							mapdata.addLocation=''
							mapdata.importIndex=-1
							mapdata.defaultPot()
							mapdata.showlocation(mapdata.carSiteList[i],-2)
							map.off('click', initMapClick);
	                		return
	                	}
					}
	                addMarker(lnglatXY);
	            } else {
	            	redirect(address)
	            }
	        });
	    });
	}
	//重定向
	function redirect(redirectAdd){
		AMap.plugin('AMap.Geocoder', function() {
	        var geocoder = new AMap.Geocoder({
		        city : "北京", //城市，默认：“全国”
		    });           
	        geocoder.getLocation(redirectAdd, function(status, result) {
	            if (status === 'complete' && result.info === 'OK') {
	                var lon = result.geocodes[0].location.lng;
	                var lat = result.geocodes[0].location.lat;
	                $("input[name=lon]").val(lon);
	                $("input[name=lat]").val(lat);
	                $("input[name=resourceAddress]").val(redirectAdd);
	                lnglatXY = [lon, lat];
	                addMarker(lnglatXY);
	            } else {
	            	resultTip({
						error: 'warning',
						msg: '定位失败！'
					});
					mapdata.defaultPot()
	            }
	        });
	    });
	}
</script>