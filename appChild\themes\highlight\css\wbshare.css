.weibo{
    position: relative;
    float: left;
}
.weibo .wbbtn{
    position: absolute;
    top: 5px;
    right: 5px;
    display: none;
    background-color: #fff;
    padding: 3px 5px;
    filter:alpha(opacity=80);
    -moz-opacity:0.8;
    opacity:0.8;
}
.weibo a.wa{
    cursor: default;
    display: block;
}
.weibo a.wa:hover .wbbtn{
    display: block;
}
.weibo .wbbtn div.qqbtn, .weibo .wbbtn div.sinabtn{
    width: 24px;
    height: 24px;
    overflow: hidden;
    float: left;
    cursor: pointer;
    background-image: url("../images/icons/wb.png");
    background-repeat: no-repeat;
}
.weibo .wbbtn div.qqbtn{
    background-position: 0 -24px;
}
.weibo .wbbtn div.sinabtn{
    margin-right: 5px;
    background-position: 0 0;
}
.weibo .wbbtn .qqbtn span{
    height:16px;
    font-size:12px;
    line-height:16px;
    cursor: pointer;
}