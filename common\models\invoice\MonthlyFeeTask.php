<?php

/**
 * This is the model class for table "ivy_child_monthly_fee_task".
 *
 * The followings are the available columns in table 'ivy_child_monthly_fee_task':
 * @property integer $id
 * @property integer $planid
 * @property integer $childid
 * @property string $schoolid
 * @property integer $calendarid
 * @property integer $startdate
 * @property integer $month
 * @property double $amount
 * @property string $fee_program
 * @property integer $classid
 * @property double $nodiscount_amount
 * @property integer $discountid
 * @property string $payment_type
 * @property string $title
 * @property string $child_service_info
 * @property integer $userid
 * @property integer $create_timestamp
 * @property integer $plantime
 * @property integer $runtime
 * @property integer $invoiceid
 */
class MonthlyFeeTask extends CActiveRecord
{
    public $planDay = 20;
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return MonthlyFeeTask the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_child_monthly_fee_task';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('planid, schoolid, startdate, month, payment_type, title, userid, create_timestamp, plantime', 'required'),
			array('planid, childid, calendarid, startdate, month, classid, discountid, userid, create_timestamp, plantime, runtime', 'numerical', 'integerOnly'=>true),
			array('amount, nodiscount_amount', 'numerical'),
			array('schoolid', 'length', 'max'=>10),
			array('payment_type', 'length', 'max'=>7),
			array('title', 'length', 'max'=>255),
			array('invoiceid', 'length', 'max'=>100),
			array('child_service_info, linked_invoice_params, fee_program', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, planid, childid, schoolid, calendarid, startdate, month, amount, fee_program, classid, nodiscount_amount, discountid, payment_type, title, child_service_info, userid, create_timestamp, plantime, runtime, invoiceid', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'plan'=>array(self::BELONGS_TO, 'MonthlyFeePlanner', 'planid'),
            'child'=>array(self::BELONGS_TO, 'ChildProfileBasic', 'childid'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'planid' => 'Planid',
			'childid' => 'Childid',
			'schoolid' => 'Schoolid',
			'calendarid' => 'Calendarid',
			'startdate' => 'Startdate',
			'month' => 'Month',
			'amount' => 'Amount',
			'fee_program' => 'Fee Program',
			'classid' => 'Classid',
			'nodiscount_amount' => 'Nodiscount Amount',
			'discountid' => 'Discountid',
			'payment_type' => 'Payment Type',
			'title' => 'Title',
			'child_service_info' => 'Child Service Info',
			'userid' => 'Userid',
			'create_timestamp' => 'Create Timestamp',
			'plantime' => 'Plantime',
			'runtime' => 'Runtime',
			'invoiceid' => 'Invoiceid',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('planid',$this->planid);
		$criteria->compare('childid',$this->childid);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('calendarid',$this->calendarid);
		$criteria->compare('startdate',$this->startdate);
		$criteria->compare('month',$this->month);
		$criteria->compare('amount',$this->amount);
		$criteria->compare('fee_program',$this->fee_program);
		$criteria->compare('classid',$this->classid);
		$criteria->compare('nodiscount_amount',$this->nodiscount_amount);
		$criteria->compare('discountid',$this->discountid);
		$criteria->compare('payment_type',$this->payment_type,true);
		$criteria->compare('title',$this->title,true);
		$criteria->compare('child_service_info',$this->child_service_info,true);
		$criteria->compare('userid',$this->userid);
		$criteria->compare('create_timestamp',$this->create_timestamp);
		$criteria->compare('plantime',$this->plantime);
		$criteria->compare('runtime',$this->runtime);
		$criteria->compare('invoiceid',$this->invoiceid);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}