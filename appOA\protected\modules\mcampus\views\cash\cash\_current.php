<?php

Yii::import('common.models.visit.*');
Yii::import('common.models.invoice.*');
//取默认交接截止日
$model = BranchVar::model()->find('branchid=:branchid and category=:category and subcategory=:subcategory',array(':branchid'=>$this->branchId,':category'=>$this->dcategory,':subcategory'=>$this->dsubcategory));
$defaultDate = 'N/A';
if (!empty($model)):
    $defaultDateTime = empty($model->data) ? 0 : $model->data;
    $defaultDate = empty($model->data) ? $defaultDate : OA::formatDateTime($model->data,'medium','medium');
endif;

//获取某学校未交接现金数据
$crite = new CDbCriteria;
$crite->compare('t.schoolid', $this->branchId);
$crite->compare('t.transactiontype', InvoiceTransaction::TYPE_CASH);
$crite->compare('t.inout', 'in');
$crite->compare('t.transfer_to_uid', 0);
$crite->compare('t.transfer_timestamp', 0);
$crite->addCondition('t.amount>0');
//$crite->compare('invoiceInfo.status', array(Invoice::STATS_PAID, Invoice::STATS_PARTIALLY_PAID));
$dataArr = InvoiceTransaction::model()->with('childInfo','userInfo','invoiceInfo')->findAll($crite);
$unHandoverAmount = 0; //未交接现金总和
$unHandoverDeadlineamount = 0;   //截止日之前总和
$unHandoverList = array(); //未交接现金列表
$unHandoverDeadlineList = array();
$userName = array();
$childName = array();

if (!empty($dataArr)):
    foreach ($dataArr as $val):
        //计算未交接总和

        $unHandoverAmount += $val->amount;
        $unHandoverList[$val->operator_uid]['info'][$val->id] = $val->getAttributes();
        if(empty($val->childInfo)){
            $model_name = AdmissionsDs::model()->findByPk($val->invoiceInfo->admission_id);

        }
        $unHandoverList[$val->operator_uid]['info'][$val->id]['name'] =  $val->childInfo ? $val->childInfo->getChildName()  : $model_name->getName();
        $unHandoverList[$val->operator_uid]['info'][$val->id]['timestampe'] =  OA::formatDateTime($val->timestampe,'medium','short');
        $unHandoverList[$val->operator_uid]['info'][$val->id]['amount'] = number_format($val->amount,2);
        $unHandoverList[$val->operator_uid]['info'][$val->id]['date'] = ($val->startdate) ? OA::formatDateTime($val->startdate).' ~ '.OA::formatDateTime($val->enddate) : null;
        $unHandoverList[$val->operator_uid]['amount'] = isset($unHandoverList[$val->operator_uid]['amount']) ? $unHandoverList[$val->operator_uid]['amount']+$val->amount : $val->amount;
        $unHandoverList[$val->operator_uid]['fAmount'] = number_format($unHandoverList[$val->operator_uid]['amount'],2);
        //计算截止日之前总和
        if ($defaultDate != 'N/A' && $val->timestampe<=$defaultDateTime):
            $unHandoverDeadlineamount += $val->amount;
            $unHandoverDeadlineList[$val->operator_uid]['info'][$val->id] = $val->getAttributes();
            $unHandoverDeadlineList[$val->operator_uid]['info'][$val->id]['name'] =  $val->childInfo->getChildName();
            $unHandoverDeadlineList[$val->operator_uid]['info'][$val->id]['timestampe'] =  OA::formatDateTime($val->timestampe,'medium','short');
            $unHandoverDeadlineList[$val->operator_uid]['info'][$val->id]['amount'] = number_format($val->amount,2);
            $unHandoverDeadlineList[$val->operator_uid]['info'][$val->id]['date'] = ($val->startdate) ? OA::formatDateTime($val->startdate).' ~ '.OA::formatDateTime($val->enddate) : null;
            $unHandoverDeadlineList[$val->operator_uid]['amount'] = isset($unHandoverDeadlineList[$val->operator_uid]['amount']) ? $unHandoverDeadlineList[$val->operator_uid]['amount']+$val->amount : $val->amount;
            $unHandoverDeadlineList[$val->operator_uid]['fAmount'] = number_format($unHandoverDeadlineList[$val->operator_uid]['amount'],2);
        endif;
        $userName[$val->operator_uid] = $val->userInfo->getName();
        if($val->childInfo){
            $childName[$val->childid] = $val->childInfo->getChildName();
        }
    endforeach;

    unset($dataArr);

    //查询个人帐户
    $command = Yii::app()->db->createCommand();
    $command->from(ChildCredit::model()->tableName());
    $dataArr = $command->where('schoolid=:schoolid and transactiontype=:transactiontype and `inout`=:inout and transfer_to_uid=:transfer_to_uid and transfer_timestamp=:transfer_timestamp', array(
        ':schoolid'=>$this->branchId,'transactiontype'=>InvoiceTransaction::TYPE_CASH,':inout'=>'in',':transfer_to_uid'=>0,':transfer_timestamp'=>0
    ))->queryAll();

    if (count($dataArr)):
        $creditAmount = 0; //个人帐户未交接现金总和
        $creditDeadlineAmount = 0; //个人帐户截止日之前未交接现金总和
        $creditAmountList = array(); //个人帐户未交接现金列表
        $creditAmountDeadlineList = array(); //个人帐户截止日之未交接现金列表
        foreach ($dataArr as $val):
            //计算未交接总和
            $creditAmount += $val['amount'];
            $creditAmountList['info'][$val['cid']] = $val;
            $creditAmountList['info'][$val['cid']]['updated_timestamp'] = OA::formatDateTime($val['updated_timestamp'],'medium','short');
            //计算截止日之前总和
            if ($defaultDate != 'N/A' && $val['updated_timestamp']<=$defaultDateTime):
                $creditDeadlineAmount += $val['amount'];
                $creditAmountDeadlineList['info'][$val['cid']] = $val;
                $creditAmountDeadlineList['info'][$val['cid']]['updated_timestamp'] = OA::formatDateTime($val['updated_timestamp'],'medium','short');
            endif;
        endforeach;
        $unHandoverAmount += $creditAmount;
        $unHandoverDeadlineamount += $creditDeadlineAmount;
        $creditAmountList['fAmount'] = number_format($creditAmount,2);
        $creditAmountDeadlineList['fAmount'] = number_format($creditDeadlineAmount,2);
    endif;
endif;
?>
<div class="panel panel-default">
    <div class="panel-heading panel-title"><?php echo Yii::t('campus', 'Outstanding deposit overview'); ?></div>
    <div class="panel-body">
        <span><?php echo Yii::t('campus', 'Cut off date: '); ?></span>
        <span class="text-success"><?php echo $defaultDate;?></span>
        <a href="javascript:void(0)" title="设置交接截止日" onclick="openHandoverEditModal()"><span class="glyphicon glyphicon-cog ml10"></span></a>
    </div>
    <!-- List group -->
    <ul class="list-group">
        <?php if (count($unHandoverDeadlineList)):?>
        <li class="list-group-item">
            <span class="dropdown">
                <a class="class-edit-actions" href="javascript:void(0)" id="dropdownMenu1" data-toggle="dropdown"><span class="glyphicon glyphicon-th-list"></span></a>
                <ul class="dropdown-menu" role="menu" aria-labelledby="dropdownMenu1">
                    <li><a href="javascript:void(0);" onclick="viewDetails('part');"><span class="glyphicon glyphicon-search"></span> 查看明细</a></li>
                </ul>
            </span>
            截止交接日期之前金额 :   <span class="badge" style="float:none;">¥<?php echo number_format($unHandoverDeadlineamount,2);?></span>
        </li>
        <li class="list-group-item hidden" id="part-cash-list">

        </li>
        <?php endif;?>
        <li class="list-group-item">
            <span class="dropdown">
                <a class="class-edit-actions" href="javascript:void(0)" id="dropdownMenu2" data-toggle="dropdown"><span class="glyphicon glyphicon-th-list"></span></a>
                <ul class="dropdown-menu" role="menu" aria-labelledby="dropdownMenu2">
                    <li><a href="javascript:void(0);" onclick="viewDetails('all');"><span class="glyphicon glyphicon-search"></span> 查看明细</a></li>
                </ul>
            </span>
            <?php echo Yii::t('campus', 'Deposit amount: '); ?> <span class="badge" style="float:none;">¥<?php echo number_format($unHandoverAmount,2);?></span>
        </li>
        <li class="list-group-item hidden" id="all-cash-list">

        </li>
    </ul>
</div>

<!-- Modal -->
<div class="modal" id="handoverEditModal" tabindex="-1" role="dialog" aria-labelledby="handoverEditModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title" id="myModalLabel"><?php echo Yii::t('campus', '设置交接截止日'); ?><small></small></h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-12">
                        <div class="well">
                            每次现金交接之前请设置交接截止日，交接时，只能交接截止日之前的现金。
                        </div>
                        <form class="form-horizontal J_ajaxForm" action="<?php echo $this->createUrl('//mcampus/cash/saveHandoverDate');?>" method="POST">
                            <div class="form-group">
                                <label for="inputEmail3" class="col-sm-2 control-label"><?php echo Yii::t('campus', 'Cut off date: '); ?></label>
                                <div class="col-sm-10">
                                    <?php //echo CHtml::textField('data',  date('Y-m-d H:i:s',  time()), array('placeholder'=>'交接截止日','id'=>'cashHandoverDate','class'=>'form-control'));?>
                                    <?php
                                        $this->widget('common.extensions.datePicker.JMy97DatePicker',array(
                                            'name'=> 'data',
                                            'value'=>date('Y-m-d H:i:s',  time()),
                                            'options'=>array(
                                                'dateFmt'=>'yyyy-MM-dd HH:mm:ss',
                                            ),
                                            'htmlOptions'=>array(
                                                'class'=>'form-control',
                                                'placeholder'=>Yii::t('campus', 'Cut off date: '),
                                            )
                                        ));
                                    ?>
                                </div>
                            </div>
                            <div class="pop_bottom">
                                <button onclick="$('#handoverEditModal').modal('hide')" type="button" class="btn btn-default pull-right"><?php echo Yii::t('global','Cancel');?></button>
                                <button type="button" class="btn btn-primary J_ajax_submit_btn pull-right mr10"><?php echo Yii::t('global','Submit');?></button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!--未交接现金列表模板-->
<script type="text/template" id="handover-list">
    <div class="panel panel-default table-responsive">
        <div class="panel-heading"><%=userName[key]%> <span class="badge" style="float:right;"><%=values['fAmount']%></span></div>
        <table class="table table-hover">
                <tr>
                    <th>#</th>
                    <th><?php echo Yii::t('campus', 'Fapiao number'); ?></th>
                    <th><?php echo Yii::t('labels','Name');?></th>
                    <th><?php echo Yii::t('campus', 'Category'); ?></th>
                    <th><?php echo Yii::t('campus', 'Date received'); ?></th>
                    <th><?php echo Yii::t('campus', 'Period'); ?></th>
                    <th><?php echo Yii::t('campus', 'Total'); ?></th>
                </tr>
                <%for(var vs in values['info']){%>
                <tr>
                    <td><%=values['info'][vs].id%></td>
                    <td><%=values['info'][vs].invoice_number%></td>
                    <td><%=values['info'][vs].name%></td>
                    <td><%=values['info'][vs].payment_type%></td>
                    <td><%=values['info'][vs].timestampe%></td>
                    <td><%=values['info'][vs].date%></td>
                    <td><%=values['info'][vs].amount%></td>
                </tr>
                <%}%>
        </table>
    </div>
</script>

<!--现金转入个人帐户列表模板-->
<script type="text/template" id="cash-credit-list">
    <div class="panel panel-default">
        <div class="panel-heading">个人帐户转入现金<span class="badge" style="float:right;"><%=creditAmountList.fAmount%></span></div>
        <table class="table table-hover">
            <tr>
                <th>#</th>
                <th><?php echo Yii::t('labels','Name');?></th>
                <th><?php Yii::t('campus', 'Category'); ?></th>
                <th><?php Yii::t('campus', 'Total'); ?></th>
                <th><?php Yii::t('campus', '收款人'); ?></th>
                <th><?php Yii::t('campus', 'Date received'); ?></th>
            </tr>
            <%for(var vs in creditAmountList['info']){%>
            <tr>
                <td><%=creditAmountList['info'][vs].transaction_id%></td>
                <td><%=childName[creditAmountList['info'][vs].childid]%></td>
                <td><%=creditAmountList['info'][vs].itemname%></td>
                <td><%=creditAmountList['info'][vs].amount%></td>
                <td><%=userName[creditAmountList['info'][vs].userid]%></td>
                <td><%=creditAmountList['info'][vs].updated_timestamp%></td>
            </tr>
            <%}%>
        </table>
    </div>
</script>

<script>
var unHandoverList = <?php echo CJSON::encode($unHandoverList);?>;
var unHandoverDeadlineList = <?php echo CJSON::encode($unHandoverDeadlineList);?>;
var creditAmountList = <?php echo CJSON::encode($creditAmountList);?>;
var creditAmountDeadlineList = <?php echo CJSON::encode($creditAmountDeadlineList);?>;
var userName = <?php echo CJSON::encode($userName);?>;
var childName = <?php echo CJSON::encode($childName);?>;
var handoverList = _.template($('#handover-list').html());
var creditList = _.template($('#cash-credit-list').html());
var allCashList = $('#all-cash-list');
var partCashList = $('#part-cash-list');
$(function(){
    openHandoverEditModal = function(){
        $("#handoverEditModal").modal();
    };

    viewDetails = function(op){
        if (op == 'all'){
            if (!_.isEmpty(unHandoverList)){
                allCashList.empty();
                allCashList.removeClass('hidden');
                _.each(unHandoverList, function(values,key){
                    allCashList.append(handoverList({key:key,values:values,userName:userName}));
                });
            }
            if (!_.isEmpty(creditAmountList)){
                allCashList.append(creditList({creditAmountList:creditAmountList,userName:userName}));
            }
        }else if(op == 'part'){
            if (!_.isEmpty(unHandoverDeadlineList)){
                partCashList.empty();
                partCashList.removeClass('hidden');
                _.each(unHandoverDeadlineList, function(values,key){
                    partCashList.append(handoverList({key:key,values:values}));
                });
            }
            if (!_.isEmpty(creditAmountDeadlineList)){
                partCashList.append(creditList({creditAmountList:creditAmountDeadlineList}));
            }
        }

    }

})
</script>