<style>
    [v-cloak] {
        display: none;
    }
    .panel-body .badge {
        position: absolute;
        right: 15px;
    }
    .loading{
        width:98%;
        height:100%;
        background:#fff;
        position: absolute;
        opacity: 0.5;
        z-index: 99;
        overflow: hidden;
    }
    .loading span{
        width:100%;
        height:100%;
        display:block;
        background: url("<?php echo Yii::app()->theme->baseUrl?>/images/loading.gif")no-repeat center center ;
    }
    .badge {
        background-color: #F56B6C;
        color: #fff;
    }
    .comment-box {
        background-color: #fff;
        padding: 15px;
        border: 1px solid #dddddd;
        margin-bottom: 17px;
        margin-top: -17px;
    }
    .bg-info {
        background-color: #F7F7F8;
    }
    .active-1 {
        background-color: #FCF8E4 !important;
        border: 1px solid #ddd;
    }
    .name-2 {
        font-size: 14px;
        font-weight: 700;
        color: #333333;
        margin-bottom: 8px;
        line-height:50px
    }
    .content-2 {
        font-size: 14px;
        font-weight: 400;
        color: #333333;
        margin-bottom: 10px;
    }
    .bg-hover:hover {
        background-color: #F7F7F8;
    }
    .badge.dot {
        width: 8px;
        height: 8px;
        border-radius: 4px;
        min-width: unset;
        padding: 0;
    }
    .allImage{
        width: 48px;
        height: 48px;
        object-fit: cover;
    }
    .cover{
        object-fit: cover;
    }
    .displayFlex{
        display:flex
    }
    .flexWidth{
        width:162px
    }
    .ellipsis{
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    .badgeNum{
        display: inline-block;
        width: 6px;
        height: 6px;
        background: #d9534f;
        border-radius: 50%;
        margin-top: 6px;
    }
    .nav-tabs > li > a{
        border:none
    }
    .image{
        width: 48px; 
        height: 48px;
        object-fit:cover;
    }
    .content img{
        max-width:100%;
        margin-bottom: 1em; 
    }
    .gray{
        background:#777
    }
    .Unreplied{
        right:74px !important;
    }
    .borderTop{
        border-top:1px solid #ddd;
    }
    .borderBto{
        border-bottom:1px solid #ddd;
    }
    .p0{
        padding:0
    }
    .pr15{
        padding-right:15px
    }
    .commentImage{
        width:40px;
        height:40px;
        object-fit:cover;
    }
    .point{
        border: 3px solid #EC971F;
    }
    .border{
        border: 2px solid #428BCA;
    }
    .pl10{
        padding-left:10px
    }
    .maxHeight{
        max-height:500px;
        overflow-y:auto
    }
    .forwarded{
        background:#F7F7F8;
        border: 1px solid #EBEDF0;
        padding:12px 16px;
        border-radius: 4px;
    }
    .replyColor{
        color:#F0AD4E
    }
    .select{
        display: flex;
        flex-direction: column;
        width: 100%;
        display: inline-block;
        position: relative;
    }
    .icon{
        position: absolute;
        right: 10px;
        top: 14px;
        color: #999;
    }
    .input {
        -webkit-appearance: none;
        background-color: #FFF;
        border-radius: 4px;
        border: 1px solid #DCDFE6;
        box-sizing: border-box;
        color: #606266;
        display: inline-block;
        font-size: inherit;
        height: 40px;
        line-height: 40px;
        outline: 0;
        padding: 0 15px;
        transition: border-color .2s cubic-bezier(.645,.045,.355,1);
        width: 100%;
        cursor: pointer;
    }
    .select ul {
        width: 100%;
        cursor: pointer;
        border-radius: 4px;
        z-index: 9;
        list-style: none;
        padding: 6px 0;
        box-sizing: border-box;
        border: 1px solid #E4E7ED;
        border-radius: 4px;
        background-color: #FFF;
        -webkit-box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
        box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
        margin: 8px 0;
        max-height: 274px;
        overflow-y:auto;
        position:absolute;
    }
    .select ul:hover{
      border: 1 solid #409eff;
    }
    .select ul li{
        font-size: 14px;
        padding: 4px 20px;
        position: relative;
        white-space: nowrap;
        overflow: hidden;
        -o-text-overflow: ellipsis;
        text-overflow: ellipsis;
        color: #606266;
        box-sizing: border-box;
        cursor: pointer;
    }
    .select ul li:hover{
        background-color: #F5F7FA;
    }
    .contentAvatar {
        width: 44px;
        height: 44px;
        border-radius: 50%;
        object-fit: cover;
    }
    .tagLabel {
        background: #EBEDF0;
    }
    .contentPostObject{
        width: 36px;
        height: 36px;
        border-radius: 50%;
        object-fit:cover;
    }
    .postMore{
        font-size: 14px;
        display: inline-block;
        height: 36px;
        line-height: 36px;
        background: #4D88D2;
        color: #fff;
        padding: 0 10px;
        border-radius: 18px;
        vertical-align: middle;
    }
    .forwardText{
        background: #FAFAFA;
        border-radius:4px;
        padding:16px
    }
    .borderRight{
        border-right:1px solid #ddd
    }
    .textareaCss{
        border: none;
        border-radius: 0;
        box-shadow: none;
    }
    .uploadIcon{
        background: #fff;
        font-size: 16px;
        padding: 10px 12px;
        color: #555555;
        margin-top:10px
    }
    .uploadImg{
        overflow-x: auto;
        white-space: nowrap;
    }
    .fileImgs{
        width:72px;
        height:72px;
        object-fit: cover;
        display: inline-block;
    }
    .fileImg{
        width:72px;
        height:72px;
        object-fit: cover;
        display: inline-block;
        margin-bottom:10px;
        cursor: pointer;
    }
    .imgData{
        position: relative;
        display: inline-block;
    }
    .imgData span{    
        position: absolute;
        right: 3px;
        top: 3px;
        background: #333333;
        opacity: 1;
        color: #fff;
        width: 17px;
        height: 17px;
        border-radius: 50%;
        font-weight: 200;
        text-align: center;
        line-height: 15px;
        font-size: 19px;
    }
    .uploadFile {
        font-size:14px;
        padding:6px 12px;
        align-items: center;
    }
    .uploadFile .FileIcon{
        display:none
    }
    .uploadFile:hover{
        background: #F7F7F8;

    }
    .uploadFile:hover .FileIcon{
        display:block
    }
    .uploadFile a{
        color:#4D88D2
    }
    .fileLink{
        background: #F7F7F8;
        display: inline-block;
        border-radius: 4px;
        border: 1px solid #EBEDF0;
        padding: 6px 12px;
        font-size: 14px;
        margin-right: 16px;
        margin-bottom:8px;
        line-height:20px
    }
    .fileLink img{
        width:20px
    }
    .uploadLoading{
        width: 72px;
        height: 72px;
        background: #D9D9D9;
        border-radius: 4px;
        line-height: 72px;
        text-align: center;
    }
    .inputStyle{
        border: none;
        border-bottom: 1px solid #ccc;
        padding: 0;
        height: 25px;
        line-height: 25px;
        width: 100%;
    }
    .imgLi{
        list-style: none;
        padding-left: 0;
    }
    .imgLi li{
        display:inline-block
    }
    .deptHover:hover{
        cursor: pointer;
        color:#428bca !important
    }
    .viewer-prev::before, .viewer-next::before{
        background-image: none !important;
    }
    .overflowH{
        overflow-x:hidden;
        overflow-y:auto;
    }
    .text_overflow {
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        line-height: 14px;
    }
    video{
        max-height: 300px;
        max-width: 100%;
        width: auto !important;
        margin-bottom:8px
    }
    .panel-body{
        position: relative;
    }
    .scrollhidden{
        overflow: hidden;
    }
</style>

<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Teaching Tasks'), array('//mteaching/default/index')) ?></li>
        <li><?php echo Yii::t('newDS', 'Journal') ?></li>
        <li><?php echo Yii::t("newDS", "Parent Feedback");?></li>
    </ol>
    <div class="row scrollhidden" id='container' v-cloak>
        <div class="col-md-2 col-sm-12">
            <div class="list-group">
                <a href="<?php echo $this->createUrl('index'); ?>" class="list-group-item status-filter"><?php echo Yii::t("newDS", "All Journals");?></a></li>
                <a href="<?php echo $this->createUrl('edit'); ?>" class="list-group-item status-filter"><?php echo Yii::t("newDS", "Mine");?></a></li>
                <a href="<?php echo $this->createUrl('feedback'); ?>" class="list-group-item status-filter active"><span  v-if='toReplyNum>0' class='badgeNum pull-right'></span><?php echo Yii::t("newDS", "Parent Feedback");?></a></li>
            </div>
        </div>
        <div class="col-md-10 col-sm-12">
            <div >
                <!-- Nav tabs -->
                <ul class="nav nav-tabs" role="tablist">
                    <li role="presentation" class="active"><a href="#unreply" aria-controls="unreply" role="tab" data-toggle="tab" @click="change('unreply')"><?php echo Yii::t("newDS", "My unreplied");?> <span class="badge" v-if='tabType=="unreply" &&  feedback.count &&  feedback.count!=0'>{{ replied }}</span></a></li>
                    <li role="presentation"><a href="#replied" aria-controls="replied" role="tab" data-toggle="tab" @click="change('replied')"><?php echo Yii::t("newDS", "My replied");?>  </a></li>
                    <li role="presentation"><a href="#viewStu" aria-controls="replied" role="tab" data-toggle="tab" @click="viewStudent()"><?php echo Yii::t("newDS", "View By Student");?>  </a></li>
                    <?php if(in_array('leader', $this->managetypeList)): ?>
                    <li role="presentation"><a href="#all" aria-controls="all" role="tab" data-toggle="tab" @click="change('all')"><?php echo Yii::t("newDS", "All (Admin Only)");?></a></li>
                    <li role="presentation"><a href="#allUnreplied" aria-controls="allUnreplied" role="tab" data-toggle="tab" @click="change('allUnreplied')"><?php echo Yii::t("newDS", "Unreplied (Admin Only)");?></a></li>
                    <?php endif;?>
                </ul>
                <!-- Tab panes -->
                <div class="tab-content mt20 overflowH" :style="'max-height:'+(height-250)+'px'">
                    <div class='loading' v-if='loading'>
                        <span></span>
                    </div>
                    <div role="tabpanel" class="tab-pane active" id="unreply">
                        <?php $this->renderPartial("_reply", array('type' => 'unreply'));?>
                    </div>
                    <div role="tabpanel" class="tab-pane" id="replied">
                        <?php $this->renderPartial("_reply", array('type' => 'replied'));?>
                        <nav aria-label="Page navigation" v-if='CopyPages.count>1'  class="text-left ml10">
                            <ul class="pagination">
                                <li v-if='pageNum >1'>
                                    <a href="javascript:void(0)" @click="plus('replied',1)" aria-label="Previous">
                                        <span aria-hidden="true">&laquo;</span>
                                    </a>
                                </li>
                                <li v-else class="disabled">
                                    <a aria-label="Previous">
                                        <span aria-hidden="true">&laquo;</span>
                                    </a>
                                </li>
                                <li class="previous" v-if='pageNum >1'>
                                    <a href="javascript:void(0)" @click="prev('replied',pageNum)">‹</a>
                                </li>
                                <li class="disabled" v-else>
                                    <a href="javascript:void(0)">‹</a>
                                </li>
                                <li v-for='(data,index) in pages.count' :class="{ active:data==pageNum }">
                                    <a href="javascript:void(0)" @click="plus('replied',data)">{{data}}</a>
                                </li>
                                <li class="previous" v-if='pageNum <CopyPages.count'>
                                    <a href="javascript:void(0)" @click="next('replied',pageNum)">›</a>
                                </li>
                                <li class="previous disabled" v-else>
                                    <a href="javascript:void(0)">›</a>
                                </li>
                                <li v-if='pageNum <CopyPages.count'>
                                    <a href="javascript:void(0)" @click="plus('replied',CopyPages.count)" aria-label="Next">
                                        <span aria-hidden="true">&raquo;</span>
                                    </a>
                                </li>
                                <li v-else class="disabled">
                                    <a aria-label="Next">
                                        <span aria-hidden="true">&raquo;</span>
                                    </a>
                                </li>
                            </ul>
                            <div class='summary mb10'>第 <span v-if='pageNum*20-20==0'>1</span><span v-else>{{pageNum*20-20}}</span>-{{pageNum*20}} 条, 共 {{CopyPages.total}} 条.</div>
                        </nav>
                    </div>
                    <div role="tabpanel" class="tab-pane" id="allUnreplied">
                        <?php $this->renderPartial("_reply", array('type' => 'allUnreplied'));?>
                    </div>
                    <div role="tabpanel" class="tab-pane" id="all">
                        <div class='flex mb16'>
                            <div class='flex1'></div>
                            <el-autocomplete
                                popper-class="my-autocomplete"
                                v-model="searchChildName"
                                clearable
                                size='small'
                                @clear='confirmAdmin("clear")'
                                :fetch-suggestions="querySearchAsync"
                                placeholder="<?php echo Yii::t('withdrawal','Search by student name');?>"
                                 class='inline-input'
                                @select="handleSelect">
                                <template slot-scope="{ item }">
                                    <div v-if='item.not' class='text-center'>{{item.name}}</div>
                                    <div v-else>
                                        <div class="media mb8" v-if='item.avatar'>
                                            <div class="media-left pull-left media-middle">
                                                <a href="javascript:void(0)">
                                                    <img :src="item.avatar" data-holder-rendered="true" class="media-object img-circle contentAvatar">
                                                </a>
                                            </div>
                                            <div class="media-body mt5 media-middle">
                                                <h4 class="media-heading font14 color3 text_overflow">{{item.name}}</h4>
                                                <div class="text-muted text_overflow font12">{{ item.class_name }}</div>
                                            </div>
                                        </div>
                                    </div>
                                </template>
                            </el-autocomplete>
                            <button type="button" class="btn btn-primary btn-sm ml16"  :disabled='searchChildId==""?true:false' @click='confirmAdmin("search")'><?php echo Yii::t("withdrawal", "Search"); ?></button>
                        </div>
                        <div class='pr8' :style="'max-height:'+(height-300)+'px;overflow-y:auto'">
                            <div class="panel panel-default" v-for='(item,index) in allItems.items'>
                                <div class="panel-body">
                                    <div class="media " v-if='item.creator_type == "parent"' >
                                        <div class="media-left pull-left media-middle">
                                            <a href="javascript:void(0)">
                                                <img class="media-object img-circle allImage" :src='allItems.childs[item.child_id].avatar' data-holder-rendered="true" >
                                            </a>
                                        </div>
                                        <div class="media-right pull-right">
                                            <a href='javascript:void(0)'style='line-height:48px' @click='linked(item)'><?php echo Yii::t("newDS", "Read all conversations");?></a>
                                        </div>
                                        <div class="media-body pt10 media-middle">
                                            <h4  class="media-heading font14">{{allItems.childs[item.child_id].name}}</h4>
                                            <div class='text-muted font12'>{{allItems.childs[item.child_id].class_name}}</div>
                                        </div>
                                    </div>
                                    <div class="media " v-if='item.creator_type == "staff"'>
                                        <div class="media-left pull-left media-middle">
                                            <a href="javascript:void(0)">
                                                <img class="media-object img-circle allImage" :src='allItems.staff[item.created_by].avatar' data-holder-rendered="true" >
                                            </a>
                                        </div>
                                        <div class="media-right pull-right">
                                            <a href='javascript:void(0)'style='line-height:48px' @click='linked(item)'><?php echo Yii::t("newDS", "Read all conversations");?></a>
                                        </div>
                                        <div class="media-body pt10 media-middle">
                                            <h4  class="media-heading font14 pt10">{{allItems.staff[item.created_by].name}}</h4>
                                        </div>
                                    </div>
                                    <div class='pt10' style='padding-left:58px'>
                                        <div class='font14 color3' v-html='html(item.content)'> </div>
                                        <div class='mb10 font14 mt10' v-if='item.attachments>0'><a href="javascript:void(0)" @click='linked(item)'><span class='glyphicon glyphicon-paperclip'></span> {{item.attachments}} <?php echo Yii::t("curriculum", "Attachments"); ?></a> </div>
                                        <p class='font12 color6 mt10'>{{item.updated_at}}</p>
                                    </div>
                                </div>    
                                <div class="panel-footer displayFlex">
                                    <div class="flex1 ellipsis">
                                        <?php echo Yii::t("newDS", "Source:");?>  <a href='javascript:void(0)' @click='viewContent(item.journal_id)'>{{item.journal_title}}</a>
                                    </div>
                                    <div class="flexWidth color6 text-right"> {{newDate(item.journal_updated_at)}} </div>
                                </div>
                            </div>
                            <nav aria-label="Page navigation" v-if='CopyPages.count>1'  class="text-left ml10">
                                <ul class="pagination">
                                    <li v-if='pageNum >1'>
                                        <a href="javascript:void(0)" @click="plus('all',1)" aria-label="Previous">
                                            <span aria-hidden="true">&laquo;</span>
                                        </a>
                                    </li>
                                    <li v-else class="disabled">
                                        <a aria-label="Previous">
                                            <span aria-hidden="true">&laquo;</span>
                                        </a>
                                    </li>
                                    <li class="previous" v-if='pageNum >1'>
                                        <a href="javascript:void(0)" @click="prev('all',pageNum)">‹</a>
                                    </li>
                                    <li class="disabled" v-else>
                                        <a href="javascript:void(0)">‹</a>
                                    </li>
                                    <li v-for='(data,index) in pages.count' :class="{ active:data==pageNum }">
                                        <a href="javascript:void(0)" @click="plus('all',data)">{{data}}</a>
                                    </li>
                                    <li class="previous" v-if='pageNum <CopyPages.count'>
                                        <a href="javascript:void(0)" @click="next('all',pageNum)">›</a>
                                    </li>
                                    <li class="previous disabled" v-else>
                                        <a href="javascript:void(0)">›</a>
                                    </li>
                                    <li v-if='pageNum <CopyPages.count'>
                                        <a href="javascript:void(0)" @click="plus('all',CopyPages.count)" aria-label="Next">
                                            <span aria-hidden="true">&raquo;</span>
                                        </a>
                                    </li>
                                    <li v-else class="disabled">
                                        <a aria-label="Next">
                                            <span aria-hidden="true">&raquo;</span>
                                        </a>
                                    </li>
                                </ul>
                                <div class='summary mb10'>第 <span v-if='pageNum*20-20==0'>1</span><span v-else>{{pageNum*20-20}}</span>-{{pageNum*20}} 条, 共 {{CopyPages.total}} 条.</div>
                            </nav>
                        </div>
                    </div>
                    <div role="tabpanel" class="tab-pane" id="viewStu"> 
                        <div v-if='!loading'>
                            <div class="col-md-2 col-sm-12 p0 pr15" v-if='classList.list && classList.list.length!=0'>
                                <div class="list-group mt5">
                                    <a href="javascript:;" class="list-group-item status-filter" v-for='(list,index) in classList.list' @click='messageStu(list)' :class='list.id==selectedId?"active":""'>{{list.title}}</a></li>
                                </div>
                            </div>
                            <div class="alert alert-warning" role="alert" v-else><?php echo Yii::t("newDS", "No comments replied to you.");?></div>
                        </div>
                        <div  class="col-md-10 col-sm-12 p0">
                            <div class='loading' v-if='classLoading'>
                                <span></span>
                            </div>
                            <div v-if='selectedId!="" && showChildName'>
                                <div class="alert alert-warning" role="alert" v-if='commentedIds.length==0 && unCommentedIds.length==0'><?php echo Yii::t("newDS", "No comment found.");?></div>
                                <div v-else>
                                    <p class='mb20 font14 color6 mt5'><?php echo Yii::t("newDS", "Students with comments");?></p>
                                    <div class='pb20' :class='unCommentedIds.length==0?"borderBto":""' v-if='commentedIds.length!=0'>
                                        <img  class='img-circle commentImage cur-p' v-for='(list,index) in commentedIds' onMouseOver="$(this).tooltip('show')"  data-toggle="tooltip" data-html='true' :title="`<div>${studentInfo[list].name}</div>${classTitle}`"  data-placement="top" :class='list==commentChildId?"point":""' @click='commentList(list)' style='margin: 0 10px 10px 0' :src="studentInfo[list].avatar" alt="">
                                    </div>
                                    <div class="alert alert-warning" role="alert" v-else><?php echo Yii::t("newDS", "No comment found.");?></div>
                                </div> 
                                <div v-if='unCommentedIds.length!=0'>
                                    <p class='mb20 font14 color6'><?php echo Yii::t("newDS", "Students without comments");?></p>
                                    <div class=' borderBto pb20'>
                                        <img v-for='(list,index) in unCommentedIds' class='img-circle commentImage cur-p'  onMouseOver="$(this).tooltip('show')"  data-toggle="tooltip" data-html='true' :title="`<div>${studentInfo[list].name}</div>${classTitle}`"  data-placement="top"  style='margin: 0 10px 10px 0;opacity:0.4' :src="studentInfo[list].avatar" alt="">
                                    </div>
                                </div>
                            </div>
                            <div>
                                <div class='loading' v-if='commentLoading'>
                                    <span></span>
                                </div>
                                <div class="alert alert-warning mt20" role="alert" v-if='selectedId!="" && commentChildId=="" && commentedIds.length!=0'><?php echo Yii::t("newDS", "Select a student to view comments.");?></div>
                                <div class='mt20 pt7' v-if='selectedId!="" && commentChildId!=""'>                               
                                    <div class="panel panel-default mb20" v-for='(list,i) in commentedList'>
                                        <div class="panel-heading">
                                            <p>
                                                <a href='javascript:;' class='font14' @click='viewContent(list._id)'>{{list.title}}</a>
                                            </p>
                                            <div>
                                                <span class="label label-default defaultBg mr5 mb5 fontWeightNormal pull-left">{{journalCategory[list.category]}}</span>
                                                <span class='text-muted'>{{list.created}}</span>
                                            </div>
                                        </div>
                                        <div class="panel-body">
                                            <div class="media" v-for='(fItem,i) in list.items'>
                                                <div v-if='fItem.mark_as_staff==0'>
                                                    <div v-if="fItem.creator_type == 'parent'">
                                                        <div class="media-left pull-left media-middle">
                                                            <a href="javascript:void(0)">
                                                                <img :src="commentChildData.avatar" data-holder-rendered="true" class="media-object img-circle commentImage">
                                                            </a>
                                                        </div> 
                                                        <div class="media-body media-middle pl10">
                                                            <h4 class="media-heading color3 font14" style='line-height:37px'><strong>{{commentChildData.name}}</strong> </h4> 
                                                            <div class='color3 font14' v-html='html(fItem.content)'></div>
                                                            <div>
                                                                <ul class='mb12 imgLi':id='tabType+"_"+fItem.id' v-if='fItem.imgUrl.length!=0'>
                                                                    <li v-for='(list,j) in fItem.imgUrl'>
                                                                        <img :src="list.url" class='fileImg mr8' @click='showImg(fItem)' alt=""  >
                                                                    </li>
                                                                </ul>
                                                                <div  v-if='fItem.videoUrl.length!=0'>
                                                                    <div v-for="(list, j) in fItem.videoUrl" :key="list.id">
                                                                        <div v-if="list.video_convert_status == 0" class="flex fileLink">
                                                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/link.png' ?>" alt="" class="pdfImg" /> 
                                                                            <a class='flex1 ml5' target= "_blank" :href="list.url"><?php echo Yii::t('message', "Convert") ?></a>
                                                                        </div>
                                                                        <video v-else :src="list.url" :poster="list.video_cover"  controls muted preload="auto" style="width: 100%; height: 100%; fit: fill"></video>
                                                                    </div>
                                                                </div>
                                                                <div >
                                                                    <div class='flex fileLink' v-for='(list,j) in fItem.pdfUrl'>
                                                                        <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/PDF1.png' ?>" alt="" class="pdfImg" v-if='list.mimetype=="application/pdf"' />
                                                                        <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/excel.png' ?>" alt="" class="pdfImg" v-else-if='list.mimetype=="application/vnd.ms-excel" || list.mimetype=="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"' />
                                                                        <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/word1.png' ?>" alt="" class="pdfImg" v-else-if='list.mimetype=="application/msword" || list.mimetype=="application/vnd.openxmlformats-officedocument.wordprocessingml.document"' />
                                                                        <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/zip.png' ?>" alt="" class="pdfImg" v-else-if='list.mimetype=="application/x-zip-compressed"' />
                                                                        <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/link.png' ?>" alt="" class="pdfImg" v-else /> 
                                                                        <a class='flex1 ml5' target= "_blank" :href="list.url">{{list.title}}
                                                                        </a>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class='mt15 text-muted'>
                                                                <span class='mr20' v-if="commentChildData[`p_${fItem.created_by}`] == 'f'"><?php echo Yii::t("newDS", "From student's Dad");?></span>
                                                                <span class='mr20' v-if="commentChildData[`p_${fItem.created_by}`] == 'm'"><?php echo Yii::t("newDS", "From student's Mom");?></span>
                                                                <span class='ml20'> {{ fItem.created_at }}</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div v-else>
                                                        <div class="media-left pull-left media-middle">
                                                            <a href="javascript:void(0)">
                                                                <img :src="teacherInfo.photo" data-holder-rendered="true" class="media-object img-circle commentImage">
                                                            </a>
                                                        </div> 
                                                        <div class="media-body media-middle pl10">
                                                            <h4 class="media-heading color3 font14 "  style='line-height:37px'><strong>{{teacherInfo.name}}</strong> </h4> 
                                                            <div class='color3 font14' v-html='html(fItem.content)'></div>
                                                            <div>
                                                                <ul class='mb12 imgLi':id='tabType+"_"+fItem.id' v-if='fItem.imgUrl.length!=0'>
                                                                    <li v-for='(list,j) in fItem.imgUrl'>
                                                                        <img :src="list.url" class='fileImg mr8' @click='showImg(fItem)' alt=""  >
                                                                    </li>
                                                                </ul>
                                                                <div  v-if='fItem.videoUrl.length!=0'>
                                                                    <div v-for="(list, j) in fItem.videoUrl" :key="list.id">
                                                                        <div v-if="list.video_convert_status == 0" class="flex fileLink">
                                                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/link.png' ?>" alt="" class="pdfImg" /> 
                                                                            <a class='flex1 ml5' target= "_blank" :href="list.url"><?php echo Yii::t('message', "Convert") ?></a>
                                                                        </div>
                                                                        <video v-else :src="list.url" :poster="list.video_cover"  controls muted preload="auto" style="width: 100%; height: 100%; fit: fill"></video>
                                                                    </div>
                                                                </div>
                                                                <div >
                                                                    <div class='flex fileLink' v-for='(list,j) in fItem.pdfUrl'>
                                                                        <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/PDF1.png' ?>" alt="" class="pdfImg" v-if='list.mimetype=="application/pdf"' />
                                                                        <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/excel.png' ?>" alt="" class="pdfImg" v-else-if='list.mimetype=="application/vnd.ms-excel" || list.mimetype=="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"' />
                                                                        <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/word1.png' ?>" alt="" class="pdfImg" v-else-if='list.mimetype=="application/msword" || list.mimetype=="application/vnd.openxmlformats-officedocument.wordprocessingml.document"' />
                                                                        <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/zip.png' ?>" alt="" class="pdfImg" v-else-if='list.mimetype=="application/x-zip-compressed"' />
                                                                        <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/link.png' ?>" alt="" class="pdfImg" v-else /> 
                                                                        <a class='flex1 ml5' target= "_blank" :href="list.url">{{list.title}}
                                                                        </a>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class='text-muted mt15'>{{ fItem.created_at }}</div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div v-else class='p20 mt20 text-center borderTop' :class='i!=list.items.length-1?"borderBto":""'>
                                                    <p class='font14 color3'>
                                                        <span class='glyphicon glyphicon-pushpin mr10' style='color:#EC971FFF'></span>{{teacherInfo.name}} <?php echo Yii::t("newDS", "marked No Reply Needed");?> <span class='font14 color6 ml10'>{{fItem.created_at}}</span>
                                                    </p>
                                                    <div  class='font12 color9'>
                                                        <?php echo Yii::t("newDS", "(This message is invisible to parents)");?>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <nav aria-label="Page navigation" v-if='CopyPages.count>1'  class="text-left ml10">
                                <ul class="pagination">
                                    <li v-if='pageNum >1'>
                                        <a href="javascript:void(0)" @click="plus('viewStu',1)" aria-label="Previous">
                                            <span aria-hidden="true">&laquo;</span>
                                        </a>
                                    </li>
                                    <li v-else class="disabled">
                                        <a aria-label="Previous">
                                            <span aria-hidden="true">&laquo;</span>
                                        </a>
                                    </li>
                                    <li class="previous" v-if='pageNum >1'>
                                        <a href="javascript:void(0)" @click="prev('viewStu',pageNum)">‹</a>
                                    </li>
                                    <li class="disabled" v-else>
                                        <a href="javascript:void(0)">‹</a>
                                    </li>
                                    <li v-for='(data,index) in pages.count' :class="{ active:data==pageNum }">
                                        <a href="javascript:void(0)" @click="plus('viewStu',data)">{{data}}</a>
                                    </li>
                                    <li class="previous" v-if='pageNum <CopyPages.count'>
                                        <a href="javascript:void(0)" @click="next('viewStu',pageNum)">›</a>
                                    </li>
                                    <li class="previous disabled" v-else>
                                        <a href="javascript:void(0)">›</a>
                                    </li>
                                    <li v-if='pageNum <CopyPages.count'>
                                        <a href="javascript:void(0)" @click="plus('viewStu',CopyPages.count)" aria-label="Next">
                                            <span aria-hidden="true">&raquo;</span>
                                        </a>
                                    </li>
                                    <li v-else class="disabled">
                                        <a aria-label="Next">
                                            <span aria-hidden="true">&raquo;</span>
                                        </a>
                                    </li>
                                </ul>
                                <div class='summary mb10'>第 <span v-if='pageNum*CopyPages.limit-CopyPages.limit==0'>1</span><span v-else>{{pageNum*CopyPages.limit-CopyPages.limit}}</span>-{{pageNum*CopyPages.limit}} 条, 共 {{CopyPages.total}} 条.</div>
                            </nav>
                        </div>
                    </div>
                </div>
                <div class="modal fade" id="linkedModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
                    <div class="modal-dialog modal-lg" role="document">
                        <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                            <h4 class="modal-title" id="parentReplyLabel">{{journal_title}}
                                <div class='font12 color6 pt10'>{{newDate(journal_updated_at)}}</div></h4>
                        </div>
                        <div class="modal-body">
                            
                            <div  class="comment-box mt20" v-if='Object.keys(linkData).length!=0'>
                                <div class="row" style="margin-right: 0;">
                                <div class="col-md-4">
                                    <div class="list-group scroll-box"  style="max-height: 500px;overflow-y: auto; overflow-x: hidden; margin-bottom: 15px;">
                                        <a href="javascript:;" class="list-group-item" :class="{'active-1': allChildid==_childid}" v-for="(list,_childid,index) in linkData.childs" :key="_childid" :id='"id"+_childid' @click.stop="itemAllChild(_childid)">
                                            <div class="row">
                                                <div class="col-md-3">
                                                    <img :src="linkData.childs[_childid].avatar" class="img-circle allImage" width="50">
                                                </div>
                                                <div class="col-md-9" style="margin-top: 5px;">
                                                    <h4 class="list-group-item-heading">{{linkData.childs[_childid].name}}</h4>
                                                    <p class="list-group-item-text">{{linkData.childs[_childid].class_name}}</p>
                                                </div>
                                            </div>
                                        </a>
                                    </div>
                                </div>
                                    <div  class="col-md-8 active-1 scroll-box" style="padding: 15px; border-radius: 4px;max-height: 500px;overflow-y: auto; overflow-x: hidden; margin-bottom: 15px;"  id='scrollItem'>
                                        <div class='scroll-box' v-if='Object.keys(allFeedbackItem).length!=0'>
                                            <div style="margin-bottom: 15px;" v-for="(fItem, i) in allFeedbackItem.items" :key="i"  :id='fItem.id'>
                                                <div v-if='fItem.mark_as_staff==0'>
                                                    <div class="row" v-if="fItem.creator_type == 'parent'">
                                                        <div class="col-md-2 text-center">
                                                            <img :src="linkData.childs[allChildid].avatar" class="img-circle allImage" width="50">
                                                        </div>
                                                        <div class="col-md-10">
                                                            <div class="name-2">{{linkData.childs[allChildid].name}}</div>
                                                            <div class="content-2"  v-html='html(fItem.content)'></div>
                                                            <div>
                                                                <ul class='mb12 imgLi':id='tabType+"_"+fItem.id' v-if='fItem.imgUrl.length!=0'>
                                                                    <li v-for='(list,j) in fItem.imgUrl'>
                                                                        <img :src="list.url" class='fileImg mr8' @click='showImg(fItem)' alt=""  >
                                                                    </li>
                                                                </ul>
                                                                <div  v-if='fItem.videoUrl.length!=0'>
                                                                    <div v-for="(list, j) in fItem.videoUrl" :key="list.id">
                                                                        <div v-if="list.video_convert_status == 0" class="flex fileLink">
                                                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/link.png' ?>" alt="" class="pdfImg" /> 
                                                                            <a class='flex1 ml5' target= "_blank" :href="list.url"><?php echo Yii::t('message', "Convert") ?></a>
                                                                        </div>
                                                                        <video v-else :src="list.url" :poster="list.video_cover"  controls muted preload="auto" style="width: 100%; height: 100%; fit: fill"></video>
                                                                    </div>
                                                                </div>
                                                                <div >
                                                                    <div class='flex fileLink' v-for='(list,j) in fItem.pdfUrl'>
                                                                        <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/PDF1.png' ?>" alt="" class="pdfImg" v-if='list.mimetype=="application/pdf"' />
                                                                        <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/excel.png' ?>" alt="" class="pdfImg" v-else-if='list.mimetype=="application/vnd.ms-excel" || list.mimetype=="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"' />
                                                                        <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/word1.png' ?>" alt="" class="pdfImg" v-else-if='list.mimetype=="application/msword" || list.mimetype=="application/vnd.openxmlformats-officedocument.wordprocessingml.document"' />
                                                                        <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/zip.png' ?>" alt="" class="pdfImg" v-else-if='list.mimetype=="application/x-zip-compressed"' />
                                                                        <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/link.png' ?>" alt="" class="pdfImg" v-else /> 
                                                                        <a class='flex1 ml5' target= "_blank" :href="list.url">{{list.title}}
                                                                        </a>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="text-muted" v-if="linkData.childs[allChildid][`p_${fItem.created_by}`] == 'f'"><?php echo Yii::t("newDS", "From student's Dad");?> {{ fItem.updated_at }}</div>
                                                            <div class="text-muted" v-if="linkData.childs[allChildid][`p_${fItem.created_by}`] == 'm'"><?php echo Yii::t("newDS", "From student's Mom");?> {{ fItem.updated_at }}</div>
                                                        </div>
                                                    </div>
                                                    <div class="row" v-if="fItem.creator_type == 'staff'">
                                                        <div class="col-md-2 text-center">
                                                            <img :src="allFeedbackItem.staff[fItem.created_by].avatar" class="img-circle allImage" width="50">
                                                        </div>
                                                        <div class="col-md-10">
                                                            <div class="name-2">{{allFeedbackItem.staff[fItem.created_by].name}}</div>
                                                            <div class="content-2" v-html='html(fItem.content)'></div>
                                                            <div>
                                                                <ul class='mb12 imgLi':id='tabType+"_"+fItem.id' v-if='fItem.imgUrl.length!=0'>
                                                                    <li v-for='(list,j) in fItem.imgUrl'>
                                                                        <img :src="list.url" class='fileImg mr8' @click='showImg(fItem)' alt=""  >
                                                                    </li>
                                                                </ul>
                                                                <div  v-if='fItem.videoUrl.length!=0'>
                                                                    <div v-for="(list, j) in fItem.videoUrl" :key="list.id">
                                                                        <div v-if="list.video_convert_status == 0" class="flex fileLink">
                                                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/link.png' ?>" alt="" class="pdfImg" /> 
                                                                            <a class='flex1 ml5' target= "_blank" :href="list.url"><?php echo Yii::t('message', "Convert") ?></a>
                                                                        </div>
                                                                        <video v-else :src="list.url" :poster="list.video_cover"  controls muted preload="auto" style="width: 100%; height: 100%; fit: fill"></video>
                                                                    </div>
                                                                </div>
                                                                <div >
                                                                    <div class='flex fileLink' v-for='(list,j) in fItem.pdfUrl'>
                                                                        <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/PDF1.png' ?>" alt="" class="pdfImg" v-if='list.mimetype=="application/pdf"' />
                                                                        <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/excel.png' ?>" alt="" class="pdfImg" v-else-if='list.mimetype=="application/vnd.ms-excel" || list.mimetype=="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"' />
                                                                        <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/word1.png' ?>" alt="" class="pdfImg" v-else-if='list.mimetype=="application/msword" || list.mimetype=="application/vnd.openxmlformats-officedocument.wordprocessingml.document"' />
                                                                        <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/zip.png' ?>" alt="" class="pdfImg" v-else-if='list.mimetype=="application/x-zip-compressed"' />
                                                                        <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/link.png' ?>" alt="" class="pdfImg" v-else /> 
                                                                        <a class='flex1 ml5' target= "_blank" :href="list.url">{{list.title}}
                                                                        </a>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="text-muted">{{ fItem.updated_at }}</div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div v-else class='p20 mt20 text-center borderTop ' :class='i!=allFeedbackItem.items.length-1?"borderBto":""'>
                                                    <p class='font14 color3'>
                                                        <span class='glyphicon glyphicon-pushpin mr10' style='color:#EC971FFF'></span>{{allFeedbackItem.staff[fItem.created_by].name}} <?php echo Yii::t("newDS", "marked No Reply Needed");?> <span class='font14 color6 ml10'>{{fItem.updated_at}}</span>
                                                    </p>
                                                    <div  class='font12 color9'>
                                                        <?php echo Yii::t("newDS", "(This message is invisible to parents)");?>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                 
            </div>
        </div>
        <div style='position: fixed; z-index: 10000;display:none' class='nextImg'>
            <div id='prev' class='viewer-prev'></div>
            <div id='next' class='viewer-next'></div>
        </div>
        <!-- 查看内容 -->
        <div class="modal fade" id="contentModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("newDS", "Content");?></h4>
                    </div>
                    <div class="modal-body" v-if='contentData.journalData' >
                        <div class="col-md-12 col-sm-12"  style='min-height:400px' ref='contentShow'  >
                            <div><span class="label label-default tagLabel  color6"><?php echo Yii::t("newDS", "School Year"); ?>：{{contentData.journalData.start_year}}-{{contentData.journalData.start_year+1}}</span></div>
                            <div v-if='contentData.journalData.category==3 || contentData.journalData.category==4'>
                                <div class='font20 color3 mt24'><label>{{contentData.journalData.title}}</label> </div>
                                <div v-if='contentData.journalData.category==3'>
                                    <div class="media mt24">
                                        <div class="media-left pull-left media-middle">
                                            <a href="javascript:void(0)">
                                            <img :src="contentData.userInfo[contentData.journalData.sign_as_uid].photoUrl" data-holder-rendered="true" class="contentAvatar">
                                            </a>
                                        </div>
                                        <div class="media-body pt8 media-middle">
                                            <h4  class="media-heading font12">{{contentData.userInfo[contentData.journalData.sign_as_uid].name}}</h4>
                                            <div class='text-muted'>{{contentData.userInfo[contentData.journalData.sign_as_uid].hrPosition}}</div>
                                        </div>
                                    </div>
                                </div>
                                <div class='mt24' v-if='contentData.journalData.category==4'>
                                    <div class='mt24 mb24 breakAll' v-html='contentData.deptData.deptDesc'></div>
                                    <div v-if='contentData.deptData.staffs.length!=0'>                                
                                        <p class='color3 font14'><strong><?php echo Yii::t("directMessage", "Team members"); ?></strong> <span class="badge ml5">{{contentData.deptData.staffs.length}}</span></p>
                                        <div v-for='(list,index) in contentData.deptData.staffs' class='mt8 mb8' >
                                            <div class="media">
                                                <div class="media-left pull-left media-middle">
                                                    <a href="javascript:void(0)">
                                                        <img class="contentAvatar" :src="contentData.userInfo[list].photoUrl" data-holder-rendered="true" >
                                                    </a>
                                                </div>
                                                <div class="media-body pt8 media-middle">
                                                    <h4  class="media-heading font12">{{contentData.userInfo[list].name}}</h4>
                                                    <div class='text-muted'>{{contentData.userInfo[list].hrPosition}}</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div v-else>
                                <form class="form-horizontal">
                                    <div class="form-group" v-if='contentData.journalData.type=="teacher"'>
                                        <div class="col-sm-12">
                                            <h3><strong class='font18 titleCn'> {{contentData.journalData.title}}</strong></h3>
                                        </div>
                                    </div>
                                    <div  v-if='contentData.journalData.type=="leader"'>
                                        <div class="form-group">
                                            <div class="col-sm-12">
                                                <h3><strong class='font18 titleCn'> {{contentData.journalData.title_cn}}</strong></h3>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <div class="col-sm-12">
                                            <h3><strong class='font18 titleEn'>{{contentData.journalData.title_en}}</strong></h3>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group" >
                                        <div class="col-sm-12 pt7">
                                            <span class="label label-default defaultBg defaultBg mr5 mb5" > {{journalCategory[contentData.journalData.category]}}</span>
                                        </div>
                                    </div>
                                    <div class="form-group"  v-if='contentData.journalData.type=="leader"'>
                                        <div class="col-sm-12 pt7">
                                            <span class="label label-default defaultBg defaultBg mr5" v-for='(item,id) in contentData.journalData.grade_group_id'>{{gradeGroupList[item]}}</span>
                                        </div>
                                    </div>
                                    <div class="form-group" v-if='contentData.journalData.type=="teacher"'>
                                        <div class="col-sm-12 pt7">
                                        <span class="label label-info mr5 mb5 fontWeightNormal pull-left" v-for='(item,id) in contentData.journalData.subjects'>{{subjectList[item]}}</span>
                                        </div>
                                    </div>       
                                    <div class="media mt24 mb24">
                                        <div class="media-left pull-left media-middle">
                                            <a href="javascript:void(0)">
                                                <img class="contentAvatar" :src="contentData.userInfo[contentData.journalData.sign_as_uid].photoUrl" data-holder-rendered="true" >
                                            </a>
                                        </div>
                                        <div class="media-body pt8 media-middle">
                                            <h4  class="media-heading font12">{{contentData.userInfo[contentData.journalData.sign_as_uid].name}}<span class="label label-default color6 tagLabel  ml5"><?php echo Yii::t("directMessage", "Signature"); ?></span></h4>
                                            <div class='text-muted'>{{contentData.journalData.sign_as_title}}</div>
                                        </div>
                                    </div>
                                    <div class="form-group" v-if='contentData.journalData.type=="teacher"'>
                                        <div class="col-sm-12 pt7" style="padding: 0 30px;">
                                            <div class='content contentCn' id='contentCn' v-html='contentData.journalData.content'></div>
                                        </div>
                                    </div>
                                    <div v-if='contentData.journalData.type=="leader"'>
                                        <div class="form-group">
                                            <div class="col-sm-12 pt7" style="padding: 0 30px;">
                                                <div class='content contentCn' id='contentCn' v-html='contentData.journalData.content'></div>
                                            </div>
                                        </div>
                                        <div class="form-group" >
                                            <div class="col-sm-12 pt7" style="padding: 0 30px;">
                                                <div class='content contentEn'  id='contentEn'  v-html='contentData.journalData.content_en'></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class='mt24'  v-if='contentData.journalData.attachments.length!=0'>
                                        <p class='color3 font14'><strong><?php echo Yii::t("curriculum", "Attachments"); ?></strong> <span class="badge ml5">{{contentData.journalData.attachments.length}}</span></p>
                                        <div v-for='(list,index) in contentData.journalData.attachments' class='mt16' style='word-break:break-all;'>
                                            <a :href="list.file_key" target='_blank'><span class='glyphicon glyphicon-paperclip mr4'></span>{{list.title}}</a>    
                                        </div>
                                    </div>
                                    <div class='mt24'>
                                        <p class='color3 font14'><strong><?php echo Yii::t("newDS", "Subscribers"); ?></strong> <span class="badge ml5">{{contentData.journalData.targets_num}}</span></p>
                                        <div class='mt16'>
                                            <img class="contentPostObject mr16 mb8"  v-for='(list,index) in contentData.journalData.targets'  :src="contentData.childData[list].avatar" data-holder-rendered="true" >    
                                            <span class='postMore cur-p' v-if='contentData.journalData.targets_num>10' @click='showMore(contentData.journalData)'>+ {{contentData.journalData.targets_num-10}} ></span>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                        <div class='clearfix'></div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 查看全部发布对象 -->
        <div class="modal fade" id="postObjectModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("newDS", "查看全部发布对象"); ?></h4>
                    </div>
                    <div class="modal-body" >
                        <div class="form-group">
                            <div class="input-group">
                            <div class="input-group-addon"><span class='glyphicon glyphicon-search'></span></div>
                            <input type="text" class="form-control" v-model='search' placeholder="请输入姓名">
                            </div>
                        </div>
                        <div class='font14'>
                            <span class='glyphicon glyphicon-user color6'> </span><span class='color3 pl4'>共{{Object.keys(searchData).length}}人</span>
                        </div>
                        <div>
                            <div class="media mt10 mb10 col-md-6 col-sm-6"  v-for='(list,key,index) in searchData' >
                                <div class="media-left pull-left media-middle">
                                    <a href="javascript:void(0)">
                                        <img class="contentAvatar" :src="list.avatar" data-holder-rendered="true" >
                                    </a>
                                </div>
                                <div class="media-body pt8 media-middle">
                                    <h4  class="media-heading font12">{{list.name}}</h4>
                                    <div class='text-muted'>{{list.class_name}}</div>
                                </div>
                            </div>  
                            <div class='clearfix'></div>  
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 标记为未回复 -->
        <div class="modal fade" id="noReplyModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
            <div class="modal-dialog modal-sm" role="document">
                <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("newDS", "Warning");?></h4>
                </div>
                <div class="modal-body">
                    <div v-if='joint_admins_count!=0'>
                        <div><?php echo Yii::t("directMessage", 'This DM involves multi collaborators. This operation will cause everyone to no longer receive unreplied reminders about this feedback. Are you sure to continue?');?> </div>
                        <div class="checkbox">
                            <label>
                                <input type="checkbox" v-model='noReplySure'><?php echo Yii::t("directMessage", 'Yes, pretty sure');?>
                            </label>
                        </div>
                    </div>
                    <div v-else><?php echo Yii::t("newDS", 'Proceed to mark it as "No Reply Needed"?');?></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Cancel");?></button>
                    <button type="button" class="btn btn-primary" @click='reply("noReply")'><?php echo Yii::t("message", "OK");?></button>
                </div>
                </div>
            </div>
        </div>
         <!-- 移交 -->
        <div class="modal fade" id="forwardModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel" data-backdrop="static">
            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("directMessage", "Handover");?></h4>
                </div>
                <div class="modal-body p24" >
                    <div class='row'>
                        <div class='col-md-6 borderRight' style='min-height:480px' v-if='inputForward.length!=0 && childid!=0'>
                            <div><label class='font14'><?php echo Yii::t("directMessage", "Handover Content");?></label></div>
                            <div class='color6'><?php echo Yii::t("directMessage", "Multiple messages will be merged.");?></div>
                            <div class='flex mt15'>
                                <div>
                                    <img :src="feedback.childs[childid].avatar" height="50" width="50" class="img-circle" style="object-fit: cover;">
                                </div>
                                <div class='flex1 ml15'>
                                    <div class='color3 font14 mt5'>{{feedback.childs[childid].name}}</div>
                                    <div class='color6 mt5'>{{feedback.childs[childid].class_name}}</div>
                                    <div class='forwardText mt20'>
                                        <div v-for='(list,index) in inputForward' class=''>
                                            <div class='color3 font14 mb12' v-html='html(feedbackItem.items[list].content)'></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class='col-md-6'>
                            <div class='mt16 mb16'>
                                <label class="radio-inline">
                                    <input type="radio" v-model="forwardType" value="3"> <?php echo Yii::t("directMessage", "Handover to staff");?> 
                                </label>
                                <label class="radio-inline">
                                    <input type="radio"  v-model="forwardType" value="4"> <?php echo Yii::t("directMessage", "Handover to a team");?>
                                </label>
                            </div>
                            <div class='mb20' v-if='forwardType==3'>
                                <div class="select" >
                                    <input type="text" readonly="readonly" autocomplete="off" placeholder="<?php echo Yii::t("directMessage", "Handover to staff");?> "  @blur="optionShow=false"
                                         @focus='optionShow=true' class='input'
                                        v-model="teacherName">
                                        <span class='glyphicon glyphicon-chevron-down icon'></span>
                                    <ul v-show='optionShow'>
                                        <li v-for="(item,key) in teacherList" :key="key+'opt'" @mousedown="mousedown(item,'teacherName')" class='flexbox justify_bet'>
                                            <div class="media">
                                                <div class="media-left pull-left media-middle">
                                                    <a href="javascript:void(0)">
                                                        <img :src="item.photoUrl" data-holder-rendered="true" class="media-object img-circle contentAvatar">
                                                    </a>
                                                </div>
                                                <div class="media-body mt5 media-middle">
                                                    <h4 class="media-heading font14 color3 text_overflow">{{item.name}}</h4>
                                                    <div class="color6  text_overflow font12">{{ item.hrPosition }}</div>
                                                </div>
                                            </div>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                            <div class='mb20' v-if='forwardType==4'>
                                <div class="select" >
                                    <input type="text" readonly="readonly" autocomplete="off" placeholder="<?php echo Yii::t("directMessage", "Handover to a team");?> "  @blur="optionShow=false"
                                         @focus='optionShow=true' class='input'
                                        v-model="deptName">
                                        <span class='glyphicon glyphicon-chevron-down icon'></span>
                                    <ul v-show='optionShow'>
                                        <li v-for="(item,key) in deptList" :key="key+'opt'" @mousedown="mousedown(item,'deptName')" class='flexbox justify_bet'>
                                        <h4 class="media-heading font14 color3 text_overflow">{{item.name}}</h4>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                            <div>
                                <div class='mb16'><span class='font14 color3'><?php echo Yii::t("labels", "Memo");?></span> <span class='color6 font12 ml10'><?php echo Yii::t("directMessage", "Only handover target can see the memo");?></span></div>
                                <textarea class="form-control" rows="3" v-model='forwardMark'></textarea>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                    <button type="button" class="btn btn-primary" @click='repost'><?php echo Yii::t("directMessage", "Confirm to handover");?></button>
                </div>
                </div>
            </div>
        </div>
        <div class="modal fade" id="originalInfoModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("directMessage", "Original Information");?></h4>
                </div>
                <div class="modal-body p24" >
                    <div class='flex mt15' v-if='childid!=0'>
                        <div>
                            <img :src="feedback.childs[childid].avatar" height="50" width="50" class="img-circle" style="object-fit: cover;">
                        </div>
                        <div class='flex1 ml15'>
                            <div class='color3 font14 mt5'>{{feedback.childs[childid].name}}</div>
                            <div class='color6 mt5'>{{feedback.childs[childid].class_name}}</div>
                            <div class='forwardText mt20'>
                                <div v-for='(list,index) in repostDetailList.commentList' class=' pb16 pt16' :class='repostDetailList.commentList.length!=(index+1)?"borderBto":""'>
                                    <div class='color3 font14 mb12' v-html='html(list.content)'></div>
                                    <div class='color6 font12'>
                                        <div class='mb8'><?php echo Yii::t("directMessage", "Originally sent to: ");?>{{repostDetailList.contactInfo[list.link_id]}}</div>
                                        <div class='mb8'><?php echo Yii::t("directMessage", "Submitted at: ");?>{{list.updated_at}}</div>
                                        <div>
                                            <span v-if="feedback.childs[childid][`p_${list.created_by}`] == 'f'"><?php echo Yii::t("newDS", "From student's Dad");?></span>
                                            <span v-else><?php echo Yii::t("newDS", "From student's Mom");?> </span> 
                                        </div>
                                       
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>
<script>
    var toReplyNum = '<?php echo $this->toReplyNum; ?>';
    var data = <?php echo json_encode($data); ?>;
    var staff = <?php echo CHtml::encode(Yii::app()->request->getParam('staff', 0)); ?>;
    var height=document.documentElement.clientHeight;
    var container= new Vue({
        el: "#container",
        data: {
            height:height,
            loading: false,
            toReplyNum:toReplyNum,
            opened: '',
            childid: 0,
            content: '',
            feedback: {},
            feedbackNonAlert: [],
            feedbackItem: {},
            allItems:{},
            linkData:{},
            replied:null,
            pageNum:'1',
            pages:{},
            CopyPages:{},
            journal_title:'',
            journal_updated_at:'',
            contentData:{},
            attachments:[],
            journalCategory:data.journalCategory,
            subjectList:data.subjectList,
            leaderuserList: data.leaderList,
            teacheruserList: {},
            gradeGroupList:data.gradeGroupList,
            tabType:'',
            commentChildId:'',
            selectedId:'',
            classTitle:'',
            classList:{},
            showChildName:false,
            commentedIds:[],
            unCommentedIds:[],
            studentInfo:{},
            commentedList:[],
            commentChildData:{},
            teacherInfo:{},
            classLoading:false,
            commentLoading:false,
            staff: staff,
            isForward:false,
            inputForward:[],
            forwardTo:'',
            options:[],
            forwardType:'',
            forwardMark:'',
            teacherList:[],
            deptList:[],
            repostDetailList:{},
            optionShow:false,
            teacherName:'',
            deptName:'',
            allFeedbackItem:{},
            allChildid:0,
            forwardCategory:'',
            postObjectList:{},
            search:'',
            token:'',
            uploadImgList:[],
            uploadLinkList:[],
            attachmentName:'',
            uploadShow:false,
            loadingList:[],
            loadingType:0,
            searchChildId:'',
            searchChildName:'',
            searchChildNameCopy:'',
            joint_admins_count:null,
            noReplySure:false,
            videoToken:''
        },
        created: function() {
            this.change('unreply')
        },
        watch:{
            forwardType(old,newValue){
                if(old!=newValue){
                    this.forwardTo=''
                    this.teacherName=''
                    this.deptName=''
                }
            }
        },
        computed: {
            searchData: function() {
                var search = this.search;
                var searchVal = ''; //搜索后的数据
                if(search) {
                    searchVal =Object.values(this.postObjectList).filter(function(product) {
                        return Object.keys(product).some(function(key) {
                            return String(product['name']).indexOf(search) !== -1;
                        })
                    })
                    return searchVal;
                }
                return this.postObjectList;
            }
        },
        methods: {
            html(data){
              return  data.replace(/\n/g, '<br/>')
            },
            newDate(dateTime){
                var dateString=''
                if(dateTime!=''){
                    let time = new Date(dateTime).getTime();
                    const date = new Date(time);
                    const Y = date.getFullYear() + '-';
                    const M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
                    const D = (date.getDate() < 10 ? '0'+date.getDate() : date.getDate()) + '  ';
                    const h = (date.getHours() < 10 ? '0'+date.getHours() : date.getHours()) + ':';
                    const m = (date.getMinutes() <10 ? '0'+date.getMinutes() : date.getMinutes()) + ':';
                    const s = date.getSeconds(); // 秒
                    dateString = Y + M + D + h + m + s;
                }
                return dateString;
            },
            open(item) {
                this.forwardCategory=item.category
                if (this.opened == item.id) {
                    this.opened = ''
                }
                else {
                    if (this.content != '') {
                        if (!confirm('您填写的内容还未提交，确定切换到其他学生吗？')) {
                            return;
                        }
                    }
                    if (this.opened != item.id) {
                        this.opened = item.id;
                        this.childid = 0;
                        this.content = '';
                    }
                }
            },
            itemChild(childid,type) {
                if (this.content != '') {
                    if (!confirm('您填写的内容还未提交，确定切换到其他学生吗？')) {
                        return;
                    }
                }
                var _this = this;
                this.childid = childid;
                this.feedbackItem={}
                this.content = '';
                _this.isForward=false
                _this.inputForward=[]
                this.uploadImgList=[]
                this.uploadLinkList=[]
                this.loadingList=[]
                if(!type){
                    this.loading = true
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("item") ?>',
                    type: 'get',
                    dataType: 'json',
                    data: {
                        id: _this.opened,
                        child_id: childid
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            if(!type){
                                _this.loading = false
                            }
                            _this.getQiniu()
                            data.data.items.forEach((list) => {
                                list.imgUrl=[]
                                list.pdfUrl=[]
                                list.videoUrl=[]
                                if(list.attachments.length!=0){
                                    list.attachments.forEach((item) => {
                                        if(data.data.attachmentList[item]){
                                            let type=data.data.attachmentList[item].mimetype.split('/')[0]
                                            if(type=="image"){
                                            list.imgUrl.push(data.data.attachmentList[item])
                                            }else if(type=="video"){
                                            list.videoUrl.push(data.data.attachmentList[item])
                                            }else{
                                            list.pdfUrl.push(data.data.attachmentList[item])
                                            }
                                        }
                                    })
                                }
                            })
                            _this.feedbackItem = data.data 
                            _this.$nextTick(function () {
                                setTimeout(() => {
                                    if(_this.tabType=='unreply'){
                                        _this.$refs.scrollHeight[2].scrollTop = _this.$refs.scrollHeight[2].scrollHeight
                                    }
                                    if(_this.tabType=='replied'){
                                        _this.$refs.scrollHeight[1].scrollTop = _this.$refs.scrollHeight[1].scrollHeight
                                    }
                                    if(_this.tabType=='allUnreplied'){
                                        _this.$refs.scrollHeight[0].scrollTop = _this.$refs.scrollHeight[0].scrollHeight
                                    }
                                }, 500);
                            });
                        }
                        else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            _this.loading = false
                        }
                    }
                });
            },
            itemAllChild(childid) {
                var _this = this;
                this.allChildid = childid; 
                this.allFeedbackItem={}
                this.uploadImgList=[]
                this.uploadLinkList=[]
                this.loadingList=[]
                $.ajax({
                    url: '<?php echo $this->createUrl("journals/item") ?>',
                    type: 'get',
                    dataType: 'json',
                    data: {
                        id: _this.opened,
                        child_id: childid
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            data.data.items.forEach((list) => {
                                list.imgUrl=[]
                                list.pdfUrl=[]
                                list.videoUrl=[]
                                if(list.attachments.length!=0){
                                    list.attachments.forEach((item) => {
                                        if(data.data.attachmentList[item]){
                                            let type=data.data.attachmentList[item].mimetype.split('/')[0]
                                            if(type=="image"){
                                            list.imgUrl.push(data.data.attachmentList[item])
                                            }else if(type=="video"){
                                            list.videoUrl.push(data.data.attachmentList[item])
                                            }else{
                                            list.pdfUrl.push(data.data.attachmentList[item])
                                            }
                                        }
                                    })
                                }
                            })
                            _this.allFeedbackItem = data.data 
                        }
                        else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    }
                });
            },
            reply(typeData) {
                var _this = this;
                if(this.loadingList.length!=0){
                    resultTip({
                        error: 'warning',
                        msg: '<?php echo Yii::t("directMessage", "Uploading");?>'
                    });
                    return
                }
                var imgIds=[]
                var pdfIds=[]
                var videoIds=[]
                var ids=[]
                this.uploadImgList.forEach((item) => {
                    ids.push(item._id)
                    imgIds.push({
                        url:item.file_key,
                        id:item._id,
                        mimetype:item.mimetype,
                        title:item.title
                    })
                })
                this.uploadLinkList.forEach((item) => {
                    ids.push(item._id)
                    let type=item.mimetype.split('/')[0]
                    if(type=='video'){
                        videoIds.push({
                            url:item.file_key,
                            id:item._id,
                            mimetype:item.mimetype,
                            title:item.title,
                            video_convert_status: item.video_convert_status,
                            video_cover: item.video_cover,
                        })
                    }else{
                        pdfIds.push({
                            url:item.file_key,
                            id:item._id,
                            mimetype:item.mimetype,
                            title:item.title
                        })
                    }
                })
                if(typeData=='noReply' && this.joint_admins_count!=0){
                    if(!this.noReplySure){
                        resultTip({
                            error: 'warning',
                            msg: '<?php echo Yii::t("newDS", "Please confirm");?>'
                        });
                        return
                    }
                }
                if((_this.tabType=='unreply' || _this.tabType=='replied') && typeData!='noReply'){
                    if(ids.length==0 && _this.content==''){
                        resultTip({
                            error: 'warning',
                            msg: '<?php echo Yii::t("directMessage", "Content cannot be empty.");?>'
                        });
                        return
                    }
                }
                this.loading = true
                $.ajax({
                    url: '<?php echo $this->createUrl("saveComment") ?>',
                    type: 'post',
                    dataType: 'json',
                    data: {
                        id: _this.opened,
                        child_id: _this.childid,
                        content: _this.content,
                        mark_as_staff:typeData=='noReply'?1:0,
                        attachments:ids
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            _this.feedbackItem.items.push({
                                id: data.data._id,
                                content: data.data.content,
                                created_by: data.data.created_by,
                                creator_type: data.data.creator_type,
                                updated_at: data.data.created_at_format,
                                mark_as_staff:data.data.mark_as_staff,
                                imgUrl:imgIds,
                                pdfUrl:pdfIds,
                                videoUrl:videoIds
                            })
                            _this.loading = false
                            _this.content = '';
                            _this.feedbackNonAlert.push(_this.childid)
                            if(typeData=='noReply'){
                                $('#noReplyModal').modal('hide')
                            }
                             _this.count()
                            _this.uploadImgList=[]
                             _this.uploadLinkList=[]
                        }
                        else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            _this.loading = false
                        }
                    }
                });
            },
            count(){
                if(Number(this.replied)!=0){
                    this.replied=Number(this.replied) - 1
                }
                if(this.replied==0){
                    this.toReplyNum=0
                }
                for(var i=0;i<this.feedback.items.length;i++){
                    if(this.feedback.items[i].id==this.opened){
                        if(Number(this.feedback.items[i].count)!=0){
                            this.feedback.items[i].count=Number(this.feedback.items[i].count) - 1
                        }
                    }
                }
            },
            change(type,page) {
                this.feedback={}
                this.tabType=type
                this.loading = true
                this.opened = '';
                this.childid = 0;
                this.content = '';
                var _this = this;
                this.linkData={}
                if(!page){
                    _this.pageNum='1'
                }
                if (type == 'unreply') {
                    $.ajax({
                        url: '<?php echo $this->createUrl("unreply") ?>',
                        type: 'get',
                        dataType: 'json',
                        data:{
                            staff:staff
                        },
                        success: function(data) {
                            if (data.state == 'success') {
                                _this.replied=data.data.count
                                _this.feedback = data.data;
                                _this.loading = false;
                            }
                            else {
                                resultTip({
                                    error: 'warning',
                                    msg: data.message
                                });
                                _this.loading = false

                            }
                        }
                    });
                }
                else if (type == 'allUnreplied') {
                    $.ajax({
                        url: '<?php echo $this->createUrl("AllUnreply") ?>',
                        type: 'get',
                        dataType: 'json',
                        success: function(data) {
                            if (data.state == 'success') {
                                _this.feedback = data.data
                                _this.loading = false
                            }
                            else {
                                resultTip({
                                    error: 'warning',
                                    msg: data.message
                                });
                                _this.loading = false
                            }
                        }
                    });
                }
                else if (type == 'replied') {
                    $.ajax({
                        url: '<?php echo $this->createUrl("replied") ?>',
                        type: 'get',
                        dataType: 'json',
                        data:{
                            page:_this.pageNum,
                            staff:staff
                        },
                        success: function(data) {
                            if (data.state == 'success') {
                                _this.pageNum=data.data.pages.page
                                _this.pages=data.data.pages
                                _this.feedback = data.data
                                _this.loading = false
                                if(!page){
                                    _this.pageNum='1'
                                    _this.initPage()
                                }
                            }
                            else {
                                resultTip({
                                    error: 'warning',
                                    msg: data.message
                                });
                               _this.loading = false

                            }
                        }
                    });
                }
                else if (type == 'all') {                    
                    $.ajax({
                        url: '<?php echo $this->createUrl("all") ?>',
                        type: 'get',
                        dataType: 'json',
                        data:{
                            page:_this.pageNum,
                            child_id:this.searchChildId
                        },
                        success: function(data) {
                            if (data.state == 'success') {
                            //    _this.pageNum=data.data.pages.page
                               _this.CopyPages=data.data.pages
                               _this.allItems=data.data
                               _this.loading = false
                               if(!page){
                                    _this.pages=data.data.pages
                                    _this.pageNum='1'
                                    _this.initPage()
                                }
                            }
                            else {
                                resultTip({
                                    error: 'warning',
                                    msg: data.message
                                });
                                _this.loading = false
                            }
                        }
                    });
                }else if(type=='viewStu'){
                    this.commentList(this.commentChildId,_this.pageNum)
                }
            },
            handleSelect(item) {
                if(item.id){
                    this.searchChildName=item.name
                    this.searchChildNameCopy=JSON.parse(JSON.stringify(item.name))
                    this.searchChildId=item.id
                }
            },
            confirmAdmin(type){
                if(type!='search'){
                    this.searchChildId=''
                }
                this.change('all') 
            },
            querySearchAsync(query,cb) {
                let that=this
                if(this.options.length && this.searchChildNameCopy==query){
                    cb(this.options);
                    return
                }
                if (query && query != '') {
                    this.loading = true;
                    this.searchChildNameCopy=JSON.parse(JSON.stringify(query))

                    $.ajax({
                        url: '<?php echo $this->createUrl("searchStudent") ?>',
                        type: "post",
                        dataType: 'json',
                        data: {
                            child_name:query
                        },
                        success: function(data) {
                            if (data.state == 'success') {
                                that.options = Object.values(data.data) ;
                                if(that.options.length==0){
                                    that.options=[{not:true,name:'<?php echo Yii::t('ptc', 'No Data') ?>'}]
                                }
                                cb(that.options);
                            }else{
                                resultTip({
                                    error: 'warning',
                                    msg: data.message
                                });
                            }
                            that.loading = false;
                        },
                        error: function(data) {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            that.loading = false;
                        },
                    })
                } else {
                    cb([]);
                    if(this.options.length){
                        this.searchChildId=''
                        this.options = [];
                        this.change('all') 
                    }
                }
            },
            initPage(){
                var _this = this;
                _this.CopyPages=JSON.parse(JSON.stringify(_this.pages))
                if(_this.CopyPages.count>=10){
                    _this.pages.count=[1,2,3,4,5,6,7,8,9,10]
               }else{
                    var numPage=[]
                    for(var i=1;i<=_this.CopyPages.count;i++){
                        numPage.push(i)
                    }
                    _this.pages.count=numPage
               }
            },
            plus(type, index) { 
                var _this = this;
                _this.pageNum = Number(index)
                this.pagesSize(type)
                
                if(type=='viewStu'){
                    this.commentList(this.commentChildId,'page')
                }else{
                    this.change(type, 'page') 
                }
            },
            pagesSize(type){
                var _this = this;
                if(_this.pageNum<10){
                    if(_this.CopyPages.count>=10){
                        _this.pages.count=[1,2,3,4,5,6,7,8,9,10]
                    }else{
                        var numPage=[]
                        for(var i=1;i<=_this.CopyPages.count;i++){
                            numPage.push(i)
                        }
                        _this.pages.count=numPage
                    }
                }else if(_this.pageNum<=_this.CopyPages.count){
                    if(_this.CopyPages.count-_this.pageNum>=4){
                        var minPage=_this.pageNum-5
                        var maxPage=_this.pageNum+4
                    }else{
                        var minPage=_this.pageNum-(9-((_this.CopyPages.count-_this.pageNum)))
                        var maxPage=_this.pageNum+(_this.CopyPages.count-_this.pageNum)
                    }
                    var numPage=[]
                    for(var i=minPage;i<=maxPage;i++){
                        numPage.push(i)
                    }
                    _this.pages.count=numPage
                }
            },
            next(type, index){
                var _this = this;
                _this.pageNum = Number(index) + 1
                this.pagesSize(type)
                if(type=='viewStu'){
                    this.commentList(this.commentChildId,'page')
                }else{
                    this.change(type, 'page') 
                }
            },
            prev(type, index) {
                var _this = this;
                _this.pageNum = Number(index) - 1
                this.pagesSize(type)
                if(type=='viewStu'){
                    this.commentList(this.commentChildId,'page')
                }else{
                    this.change(type, 'page') 
                }
            },
            linked(list){
                var _this = this;
                this.opened=list.journal_id
                $.ajax({
                    url: '<?php echo $this->createUrl("viewComment") ?>',
                    type: 'get',
                    dataType: 'json',
                    data: {
                        id: list.id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            _this.linkData=data.data
                            data.data.items.forEach((list) => {
                                list.imgUrl=[]
                                list.pdfUrl=[]
                                list.videoUrl=[]
                                if(list.attachments.length!=0){
                                    list.attachments.forEach((item) => {
                                        if(data.data.attachmentList[item]){
                                            let type=data.data.attachmentList[item].mimetype.split('/')[0]
                                            if(type=="image"){
                                            list.imgUrl.push(data.data.attachmentList[item])
                                            }else if(type=="video"){
                                            list.videoUrl.push(data.data.attachmentList[item])
                                            }else{
                                            list.pdfUrl.push(data.data.attachmentList[item])
                                            }
                                        }
                                    })
                                }
                            })
                            _this.allFeedbackItem.items = data.data.items
                            _this.allFeedbackItem.staff = data.data.staff
                            _this.allChildid = data.data.current_child_id;
                            _this.journal_title=list.journal_title
                            _this.journal_updated_at=list.journal_updated_at
                            $('#linkedModal').modal('show')
                        }
                        else {
                            alert(data.message)
                        }
                    }
                });
            },
            showlink(list){
                let that=this
                this.$nextTick(()=>{
                    setTimeout(() => {
                        let btn = document.getElementById(list.id)
                        let scrollItem = document.getElementById('scrollItem')
                        scrollItem.scrollTop = btn.offsetTop
                        var targetElement = document.querySelector('#id'+list.child_id);
                        if (targetElement) {
                            targetElement.scrollIntoView({behavior: 'smooth'});
                        }
                    }, 500);
                });
                
            },
            viewContent(journal_id){
                var that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("directMessage/view") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        journal_id: journal_id,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                           that.contentData=data.data
                           $('#contentModal').modal('show')
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            showMore(list){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("directMessage/subscribers") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        journal_id:list.journal_id,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.postObjectList=data.data
                            $('#postObjectModal').modal('show')
                        }
                    },
                    error: function(data) {
                        
                    },
                })
            },
            markNoReply(item){
                this.noReplySure=false
                this.joint_admins_count=item.joint_admins_count
                $('#noReplyModal').modal()
            },
            viewStudent(){
               let that=this
               this.CopyPages={}
                this.pages={}
                this.pageNum='1'
                this.loading=true
                $.ajax({
                    url: '<?php echo $this->createUrl("getCategoryList") ?>',
                    type: 'get',
                    dataType: 'json',
                    data: {
                        staff:this.staff
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.classList=data.data
                            that.teacherInfo=data.data.teacherInfo
                            that.loading=false

                        }
                        else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            that.loading=false
                        }
                    }
                });
            },
            messageStu(list){
                this.selectedId=list.id
                this.classTitle=list.title
                this.commentedList=[]
                this.commentChildId=''
                this.CopyPages={}
                this.pages={}
                this.pageNum='1'
                let that=this
                this.classLoading=true
                var dataList={}
                if(that.classList.type=='course'){
                    dataList={
                        'id': list.course_code,
	                    'type': that.classList.type,
                        'staff':this.staff
                    } 
                }else{
                    dataList={
                        'id': list.id,
	                    'type': that.classList.type,
                        'staff':this.staff
                    } 
                }
                that.showChildName=false
                $.ajax({
                    url: '<?php echo $this->createUrl("getChildList") ?>',
                    type: 'get',
                    dataType: 'json',
                    data:dataList,
                    success: function(data) {
                        if (data.state == 'success') {
                           if(data.data!=null){
                                that.commentedIds=data.data.commentedIds
                                that.studentInfo=data.data.studentInfo
                                that.unCommentedIds=data.data['non-commentedIds']
                           }else{
                                that.commentedIds=[]
                                that.studentInfo={}
                                that.unCommentedIds=[]
                           }
                           that.showChildName=true
                           that.classLoading=false
                        }
                        else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            that.showChildName=true
                            that.classLoading=false
                        }
                    },
                    error: function(data) {
                        that.showChildName=true
                        that.classLoading=false
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                });
            },
            commentList(list,page){
                this.commentChildId=list
                this.commentChildData=this.studentInfo[list]
                this.commentLoading=true
                let that=this
                if(!page){
                    that.pageNum='1'
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("getFeedbackByChild") ?>',
                    type: 'get',
                    dataType: 'json',
                    data: {
                        childId: list,
	                    pageNum:that.pageNum,
                        staff:this.staff
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            if(data.data!=null && data.data.length!=0){
                                that.CopyPages={
                                    count:data.data.pagination.pages,
                                    page: data.data.pagination.pageNum,
                                    total:data.data.pagination.total,
                                    limit:data.data.pagination.limit,
                                }
                                if(!page){
                                    that.pages={
                                        count:data.data.pagination.pages,
                                        page: data.data.pagination.pageNum,
                                        total:data.data.pagination.total,
                                        limit:data.data.pagination.limit,
                                    }
                                    that.pageNum='1'
                                    that.initPage()
                                }
                                data.data.commentList.forEach((_item) => {
                                    _item.items.forEach((list) => {
                                        list.imgUrl=[]
                                        list.pdfUrl=[]
                                        list.videoUrl=[]
                                        if(list.attachments.length!=0){
                                            list.attachments.forEach((item) => {
                                                if(data.data.attachmentList[item]){
                                                    let type=data.data.attachmentList[item].mimetype.split('/')[0]
                                                    if(type=="image"){
                                                    list.imgUrl.push(data.data.attachmentList[item])
                                                    }else if(type=="video"){
                                                    list.videoUrl.push(data.data.attachmentList[item])
                                                    }else{
                                                    list.pdfUrl.push(data.data.attachmentList[item])
                                                    }
                                                }
                                            })
                                        }
                                    })
                                })
                                that.commentedList=data.data.commentList
                            }
                            that.commentLoading=false
                        }
                        else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            that.commentLoading=false
                        }
                    },
                    error: function(data) {
                        that.commentLoading=false
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                });
            },
            forwardEvent(){
                this.isForward=true
            },
            forwardCancel(){
                this.isForward=false
                this.inputForward=[]
            },
            forwardTeacher(){
                if(this.inputForward.length==0){
                    resultTip({
                        error: 'warning',
                        msg:'<?php echo Yii::t("global", "Please Select");?><?php echo Yii::t("directMessage", "Handover Content");?>'
                    });
                    return
                }
                this.inputForward=this.inputForward.sort()
                this.forwardType=''
                this.forwardTo=''
                this.forwardToName=''
                this.forwardMark=''
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("directMessage/childTeacherList") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        childId:this.childid
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.teacherList = Object.values(data.data) ;
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                        that.loading = false;
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                        that.loading = false;
                    },
                })
                $.ajax({
                    url: '<?php echo $this->createUrl("directMessage/childDeptList") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {},
                    success: function(data) {
                        if (data.state == 'success') {
                            that.deptList = Object.values(data.data) ;
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                        that.loading = false;
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                        that.loading = false;
                    },
                })
                $('#forwardModal').modal('show')
            },
            repost(){
                var comment_id_list=[]
                this.inputForward.forEach(item => {
                    comment_id_list.push(this.feedbackItem.items[item].id)
                });
                let that=this
                if(this.forwardType==''){
                    resultTip({
                        error: 'warning',
                        msg:'<?php echo Yii::t("global", "Please Select");?>移交类型'
                    });
                    return
                }
                if(this.forwardTo==''){
                    resultTip({
                        error: 'warning',
                        msg:'<?php echo Yii::t("global", "Please Select");?>移交人或者部门'
                    });
                    return
                }
                var forwardTo=''
                if(this.forwardType==3){
                    forwardTo=this.forwardTo.uid
                }else{
                    forwardTo=this.forwardTo.id
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("directMessage/repost") ?>',
                    type: 'post',
                    dataType: 'json',
                    data:{
                        comment_id_list:comment_id_list ,
                        repost_type: this.forwardType, 
                        repost_to: forwardTo,
                        repost_mark:this.forwardMark
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            $('#forwardModal').modal('hide')
                            that.initLoading = false;
                            that.change(that.tabType)
                            resultTip({
                                msg: data.message
                            });
                        }
                        else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        that.classLoading=false
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                }); 
            },
            showOriginalInfo(id){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("directMessage/repostDetail") ?>',
                    type: 'post',
                    dataType: 'json',
                    data:{
                        commentId:id,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            $('#originalInfoModal').modal('show')
                          that.repostDetailList=data.data
                        }
                        else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        that.classLoading=false
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                }); 
            },
            mousedown(item,type) {
                this[type]=item.name
                this.forwardTo=item
                this.show=false
            },
            getQiniu(){
                this.initUploader(1);
                this.initUploader(0);
            },
            initUploader(isVideo) {
                let that = this;
                let url = '<?php echo $this->createUrl("journals/getCommentQiniuToken") ?>';
                let data = {
                    linkId: that.opened,
                    linkType: 'journal'
                };

                if (isVideo) data.isVideo = 1;

                $.ajax({
                    url: url,
                    type: "POST",
                    dataType: 'json',
                    data: data,
                    success: function(res) {
                        if (res.state === 'success') {
                            if (isVideo) {
                                that.videoToken = res.data.data;
                            } else {
                                that.token = res.data.data;
                            }
                            that.uploadShow = true;
                            that.$nextTick(() => {
                                let btns = [];
                                if (that.tabType === 'unreply') {
                                    btns = isVideo ? [{id: 'pickfilesVideo', type: 'video'}] :
                                        [{id: 'pickfilesPhoto', type: 'photo'}, {id: 'pickfilesPDF', type: 'pdf'}];
                                } else if (that.tabType === 'replied') {
                                    btns = isVideo ? [{id: 'pickfilesVideo1', type: 'video'}] :
                                        [{id: 'pickfilesPhoto1', type: 'photo'}, {id: 'pickfilesPDF1', type: 'pdf'}];
                                } else if (that.tabType === 'allUnreplied') {
                                    btns = isVideo ? [{id: 'pickfilesVideo2', type: 'video'}] :
                                        [{id: 'pickfilesPhoto2', type: 'photo'}, {id: 'pickfilesPDF2', type: 'pdf'}];
                                }
                                btns.forEach(btn => {
                                    const mimeTypes = that.getMimeTypes(btn.type);
                                    let uploaderConfig = Object.assign({}, config, {
                                        token: isVideo ? that.videoToken : that.token,
                                        browse_button: btn.id,
                                        filters: mimeTypes
                                    });
                                    let uploader = new plupload.Uploader(uploaderConfig);
                                    uploader.init();
                                });
                            });
                        } else {
                            resultTip({ error: 'warning', msg: res.message });
                        }
                    },
                    error: function() {
                        resultTip({ error: 'warning', msg: '请求 Token 失败' });
                    }
                });
            },
            getMimeTypes(type) {
                let mimeTypes;
                if (type === 'photo') {
                    mimeTypes = [{
                        title: "<?php echo Yii::t('teaching', 'Image files'); ?>",
                        extensions: "jpg,gif,png,jpeg"
                    }];
                } else if (type === 'video') {
                    mimeTypes = [{
                        title: "<?php echo Yii::t('teaching', 'Video files'); ?>",
                        extensions: "mp4,avi,mov,wmv"
                    }];
                } else {
                    mimeTypes = [{
                        title: "<?php echo Yii::t('teaching', 'PDF and document files'); ?>",
                        extensions: "pdf,doc,xls,zip,xlsx,docx"
                    }];
                }
                return mimeTypes;
            },
            delImg(type,list,index){
                var that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("journals/deleteAttachment") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        attachmentId:list._id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            if(type=='img'){
                                that.uploadImgList.forEach((item,index) => {
                                    if (item._id==list._id) {
                                        that.uploadImgList.splice(index, 1)
                                    } 
                                })
                            }else{
                                that.uploadLinkList.forEach((item,index) => {
                                    if (item._id==list._id) {
                                        that.uploadLinkList.splice(index, 1)
                                    } 
                                })
                            }
                            resultTip({
                                msg:data.state
                            });
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            saveFile(list,index) {
                var that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("journals/updateAttachmentName") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        attachmentId:list._id,
                        attachmentName:this.attachmentName

                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.uploadLinkList.forEach((item,index) => {
                                if (item._id==list._id) {
                                    Vue.set(that.uploadLinkList[index], 'title',data.data.title);
                                    Vue.set(that.uploadLinkList[index], 'file_key', data.data.url);
                                    Vue.set(that.uploadLinkList[index], 'isEdit', false);
                                } 
                            })
                            resultTip({
                                msg:'<?php echo Yii::t("newDS", "Data Saved!");?>'
                            });
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            showImg(list){
                var id=this.tabType+"_"+list.id 
                var viewer = new Viewer(document.getElementById(id),{
                    fullscreen: false,
                    title:false,
                    show:function(){ 
                        $('.nextImg').show()
                        if(list.imgUrl.length==1){
                            $('.viewer-prev').hide()
                            $('.viewer-next').hide()
                        }
                    },
                    hide:function(){
                        $('.nextImg').hide() 
                        viewer.destroy()
                    }
                });
                document.getElementById('next').onclick = function() {
                    viewer.next();
                }
                document.getElementById('prev').onclick = function() {
                    viewer.prev();
                }
                $("#"+id).click();
            },
        }
    })
    var config = {
        runtimes: 'html5,flash,html4', //上传模式,依次退化
        container: 'container', //上传区域DOM ID，默认是browser_button的父元素，
        browse_button: 'pickfilesPhoto', //上传选择的点选按钮，**必需**
        token: '',
        flash_swf_url: '/js/plupload/Moxie.swf', // flash的相对地址
        auto_start: true, //选择文件后自动上传，若关闭需要自己绑定事件触发上传
        // multi_selection: false, //不允许多选
        url:'https://upload-z1.qiniup.com',
        init: {
            'FilesAdded': function(up, files) {
                container.disabledUpload=true
                let fileType=files[0].type.split('/')
                if(fileType[0]=="image"){
                  container.loadingType=1
                }else{
                  container.loadingType=2
                }
                container.loadingList=files
                up.start();
            },
            BeforeUpload: function(up, file) {
                let fileType=file.type.split('/')
                let token=fileType[0]=="video"?container.videoToken:container.token
                up.setOption({
                    multipart_params:{token:token}
                })
                if(fileType[0]=="image"){
                    container.uploadImgList.push({types:'1'})
                }else{
                    container.uploadLinkList.push({title:' <?php echo Yii::t("directMessage", "Uploading");?>'})  
                }
                container.loadingList.splice(0,1)
            },
            UploadProgress: function(up, file) {
                $('#progress').css('width', file.percent+'%').html(file.percent+'%');
            },
            'FileUploaded': function(up, file, info) {
                var response = eval('('+info.response+')');
                if(info.status == 200){
                    let fileType=response.data.mimetype.split('/')
                    if(fileType[0]=="image"){
                        container.uploadImgList.splice(container.uploadImgList.length-1,1)
                        container.uploadImgList.push(response.data)
                    }else{
                        response.data.isEdit=false
                        container.uploadLinkList.splice(container.uploadLinkList.length-1,1)
                        container.uploadLinkList.push(response.data)
                    }
                }
            },
            'Error': function(up, err, errTip) {
                if(err.response){
                    var response = eval('('+err.response+')');
                }
                else{
                    var response = {message: err.message};
                }
                if(response.error == 'token not specified'){
                    up.stop();
                    resultTip({
                        error: 'warning',
                        msg: err.message
                    });
                }
            },
            'UploadComplete': function(up, file) {
                //队列文件处理完毕后,处理相关的事情
            }
        }
    };
</script>
