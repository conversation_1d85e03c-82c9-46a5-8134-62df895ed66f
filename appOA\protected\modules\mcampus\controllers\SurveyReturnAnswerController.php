<?php

class SurveyReturnAnswerController extends BranchBasedController
{
    public $actionAccessAuths = array(
        'Index'             => 'o_A_Access',
    );

    public function createUrl($route, $params = array(), $ampersand = '&', $parentOnly = false)
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        return parent::createUrl($route, $params, $ampersand);
    }

    public function init(){
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";
        Yii::import('common.models.surveyReturn.*');
        Yii::import('common.models.calendar.*');
        $this->modernMenuFlag = 'campusOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site','Campus Workspace');
        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/vue2.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/xlsx.full.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/echarts.min.js');
        $cs->registerCssFile($cs->getCoreScriptUrl().'/jui/css/base/jquery-ui.css');
        $cs->registerScriptFile($cs->getCoreScriptUrl().'/jui/js/jquery-ui.min.js');
        $this->branchSelectParams['urlArray'] = array('//mcampus/surveyReturnAnswer/index');
    }


    /**
     * 模板主页面
     */
    public function actionIndex()
    {
        $yid = Yii::app()->request->getParam('yid', '');

        $criteria = new CDbCriteria();
        $criteria->compare('branchid', $this->branchId);
        $criteria->compare('startyear', ">=2019");
        $calendar = CalendarSchool::model()->findall($criteria);
        $calendarArr = array();
        foreach ($calendar as $val){
            $endyaer = $val->startyear + 1;
            $calendarArr[] = array(
                'startyear' => $val->startyear . ' - ' . $endyaer,
                'yid' => $val->yid
            );
            if(empty($yid) && $val->is_selected){
                $yid = $val->yid;
            }
        }

        $criteria = new CDbCriteria();
        $criteria->compare('school_id', $this->branchId);
        $criteria->compare('yid', $yid);
        $criteria->compare('status', array(0,1));
        $criteria->index = 'tid';
        $surveyModel = SurveyReturn::model()->findAll($criteria);
        $templateInfo = array();
        $templatedata= array();
        if($surveyModel){
            $templateModel = SurveyReturnTemplate::model()->findAllByPk(array_keys($surveyModel));
            foreach ($templateModel as $item){
                $templatedata[$item->id] = array(
                    'survey_id' => $item->id,
                    'cn_title' => $item->getTitle(),
                    'cn_intro' => $item->getIntro(),
                );
            }

            foreach ($surveyModel as $val){
                $templateInfo[] = array(
                    'id' => $val->id,
                    'template_id' => $val->tid,
                    'status' => $val->status,
                    'cn_title' => $templatedata[$val->tid]['cn_title'],
                    'cn_intro' => $templatedata[$val->tid]['cn_intro'],
                    'start_timestamp' => date("Y/m/d", $val->start_timestamp),
                    'end_timestamp' => date("Y/m/d", $val->end_timestamp),
                    'classType' => explode(',', $val->class_type),
                );
            }
        }

        $type = ($this->branchObj->type == 50) ? 50 : 20;
        $classType = IvyClass::getClassTypes(true, $type);

        $this->render('index', array(
            'templateInfo' => $templateInfo,
            'branchId' => $this->branchId,
            'yid' => $yid,
            'calendarArr' => $calendarArr,
            'classType' => $classType,
        ));
    }

    public function actionQrcode()
    {
        $id = Yii::app()->request->getParam('id', '');

        if(!$id){
            $this->addMessage('state', 'fail');
            $this->addMessage('message', Yii::t('message', '参数错误'));
            $this->showMessage();
        }
        $model = SurveyReturn::model()->findByPk($id);
        if(!$model){
            $this->addMessage('state', 'fail');
            $this->addMessage('message', Yii::t('message', '参数错误'));
            $this->showMessage();
        }
        $state = $this->branchObj->type == 50 ? 'ds' : 'ivy';
        $url = 'http://www.ivyonline.cn/wechat/regWelcome/reEnrollment/sid/'.$model->id.'/state/' . $state;
        $filename = 'survey_return_qrcode_'.$model->id .'.png';
        $templateModel = SurveyReturnTemplate::model()->findByPk($model->tid);
        $this->renderPartial('qrcode', array('url' => $url, 'filename' => $filename, 'templateModel' => $templateModel));
    }

    /**
     * 结果
     */
    public function actionResult()
    {
        Yii::import('common.models.invoice.*');

        $template_id = Yii::app()->request->getParam('template_id', '');
        $return_id = Yii::app()->request->getParam('return_id', '');

        $surveyModel = SurveyReturn::model()->findByPk($return_id);
        $templateModel = SurveyReturnTemplate::model()->findByPk($template_id);

        $schoolList = array();
        // 查找所有问题
        $criteria = new CDbCriteria();
        $criteria->compare('survey_id', $template_id);
        $criteria->compare('status', 1);
        $criteria->order = 'id ASC';
        $qmodel = SurveyReturnQuestions::model()->findAll($criteria);
        $qList = array();
        $question = array();
        $is_positioning = 0;
        foreach ($qmodel as $val){
            $question[] = array(
                'id' => $val->id,
                'pid' => $val->pid,
                'survey_id' => $val->survey_id,
                'option_sort' => $val->option_sort,
                'type' => $val->type,
                'titleEn' => $val->en_title,
                'titleCn' => $val->cn_title,
                'is_required' => $val->is_required,
                'is_multiple' => $val->is_multiple,
                'is_memo' => $val->is_memo,
                'is_invoice' => $val->is_invoice,
            );
            if($val->is_invoice){
                $is_positioning = 1;
            }
            $qList[$val->id] = array(
                'id' => $val->id,
                'pid' => $val->pid,
                'type' => $val->type,
            );
            if($val->is_invoice){
                if ($val->is_invoice == 'all') {
                    $schoolList[] = $surveyModel->school_id;
                } else {
                    $schoolList[] = $val->is_invoice;
                }
            }
        }
        $level0Question = $this->getChildQuestion(0, $qList, array());
        foreach ($level0Question as $key => $val){
            unset($qList[$val['id']]);
        }
        $qIds = array();
        foreach ($level0Question as $key => $val){
            $qIds[] = $val['id'];
            $list = $this->getChildArray($qList, $val['id']); // 递归
            $tempList = $this->getAllChildQuestion($list);
            $qIds = array_merge($qIds, $tempList);
        }

        // 查找答案
        $criteria = new CDbCriteria();
        $criteria->compare('school_id', $this->branchId);
        $criteria->compare('template_id', $template_id);
        $criteria->compare('survey_id', $return_id);
        $criteria->index = 'childid';
        $criteria->order = 'is_answer DESC, t.updated_at DESC';
        $criteria->with = array('childInfo', 'classInfo');
        $surveyRetunrmChildModel = SurveyReturnChild::model()->findAll($criteria);

        $criteria = new CDbCriteria();
        $criteria->compare('school_id', $this->branchId);
        $criteria->compare('survey_id', $return_id);
        $criteria->compare('del_flag', 0);
        $criteria->compare('template_id', $template_id);
        
        $count = SurveyReturnAnswer::model()->count($criteria);
        $batchNum = 200;
        $page = ceil($count / $batchNum);

        $childAnswerInfo = array();
        $qval = array();
        for ($i = 0; $i < $page; $i++) {
            $criteria->limit = $batchNum;
            $criteria->offset = $i * $batchNum;
            $answerModel = SurveyReturnAnswer::model()->findAll($criteria);
            foreach ($answerModel as $val) {
                $childAnswerInfo[$val->cid][] = array(
                    'q_id' => $val->q_id,
                    'q_val' => json_decode($val->q_val),
                    'q_memo' => $val->q_memo,
                    'q_signature_raw' => $val->q_signature,
                    'q_signature' => $val->q_signature ? SurveyReturn::getThumbOssImageUrl($val->q_signature, 'style/w200') : '',
                );
    
                $qval[] = json_decode($val->q_val);
            }
        }

        $invoiceInfo = array();
        $answer = 0;
        $classIds = array();
        $classList = array();
        $classChild = array();
        if($surveyRetunrmChildModel) {
            $calendarModel = Calendar::model()->findByPk($surveyModel->yid);
            $startYear = $calendarModel->startyear + 1;
            $schoolLists = $schoolList ? $schoolList : $surveyModel->school_id;
            $criteria = new CDbCriteria();
            $criteria->compare('branchid', $schoolLists);
            $criteria->compare('startyear', $startYear);
            $criteria->index = 'yid';
            $nextCalendarModel = CalendarSchool::model()->findAll($criteria);

            if($nextCalendarModel) {
                $criteria = new CDbCriteria();
                $criteria->compare('schoolid', $schoolList);
                $criteria->compare('childid', array_keys($surveyRetunrmChildModel));
                $criteria->compare('calendar_id', array_keys($nextCalendarModel));
                $criteria->compare('payment_type', 'deposit');
                $criteria->compare('status', 20);
                $invoiceModel = Invoice::model()->findAll($criteria);
                if ($invoiceModel) {
                    foreach ($invoiceModel as $val) {
                        $invoiceInfo[$val->childid] = $val->childid;
                    }
                }
            }

            $childInfo = array();
            $parentIds = array();
            foreach ($surveyRetunrmChildModel as $val){
                if($val->is_answer){
                    $answer++;
                }
                $childName_cn = $val->childid;
                $childName_en = $val->childid;
                $gender = '';
                $birthday = '';
                if(isset($val->childInfo)){
                    $gender = $val->childInfo->gender == 1 ? '男' : '女';
                    $birthday = date("Y-m-d", $val->childInfo->birthday);
                    $childName_cn = trim($val->childInfo->name_cn);
                    if (trim($val->childInfo->last_name_en) != "")
                        $childName_en = trim(sprintf("%s %s", trim($val->childInfo->first_name_en), trim($val->childInfo->last_name_en)));
                    else
                        $childName_en = trim($val->childInfo->first_name_en);
                }
                $answerInfo = ($val->is_answer == 1) ? isset($childAnswerInfo) && isset($childAnswerInfo[$val->id]) ? $childAnswerInfo[$val->id] : array() : array();
                $newAnswerInfo = array();
                if ($answerInfo) {
                    foreach ($qIds as $qId) {
                        $flag = false;
                        foreach($answerInfo as $item) {
                            if (isset($item['q_id']) && $item['q_id'] == $qId) {
                                $newAnswerInfo[] = $item;
                                $flag = true;
                                break;
                            }
                        }
                        if (!$flag) {
                            $newAnswerInfo[] = array(
                                'q_id' => $qId,
                                'q_val' => '',
                                'q_memo' => '',
                                'q_signature' => '',
                            );
                        }
                    }
                }
                $fid = $val->childInfo->fid;
                $mid = $val->childInfo->mid;
                $parentIds[] = $fid;
                $parentIds[] = $mid;
                $childInfo[] = array(
                    'id' => $val->id,
                    'class_id' => $val->classInfo->classid,
                    'class_child_age' => $val->classInfo->child_age,
                    'classid' => $val->classInfo->title,
                    'classType' => $val->classType,
                    'gender' => $gender,
                    'birthday' =>$birthday,
                    'childName' => $childName_cn, //isset($val->childInfo) ? $val->childInfo->getChildName(false,false,true) : $val->childid,
                    'childName_en' => $childName_en, //isset($val->childInfo) ? $val->childInfo->getChildName(false,false,true) : $val->childid,
                    'childid' => $val->childid,
                    'fid' => $fid,
                    'mid' => $mid,
                    'motherTel' => '',
                    'motherEmail' => '',
                    'fatherTel' => '',
                    'fatherEmail' => '',
                    'is_answer' => $val->is_answer,
                    'is_invoice' => (isset($invoiceInfo) && isset($invoiceInfo[$val->childid])) ? 1 : 0,
                    'url' => $this->createUrl("/child/index/index", array('branchId' => $this->branchId, 'childid' => $val->childid)),
                    'answerInfo' => $newAnswerInfo,
                    'updated_at' => ($val->is_answer == 1) ? date("Y-m-d H:i:s", $val->updated_at) : '',
                );
            }
            $parentIds = array_unique($parentIds);
            $parentModels = User::model()->with(array('parent'))->findAllByPk($parentIds, array('index' => 'uid'));
            foreach ($childInfo as $key => $value) {
                $fInfo = isset($parentModels[$value['fid']]) ? $parentModels[$value['fid']] : NULL;
                $mInfo = isset($parentModels[$value['mid']]) ? $parentModels[$value['mid']] : NULL;
                if ($fInfo) {
                    $childInfo[$key]['fatherTel'] = $fInfo->parent->mphone;
                    $childInfo[$key]['fatherEmail'] = $fInfo->email;
                }
                if ($mInfo) {
                    $childInfo[$key]['motherTel'] = $mInfo->parent->mphone;
                    $childInfo[$key]['motherEmail'] = $mInfo->email;
                }
                $classIds[$value['class_id']] = $value['class_id'];
                $classChild[$value['class_id']][] = $childInfo[$key];
                // $classList[$value['class_id']] = array(
                //     'class_id' =>  $childInfo[$key]['class_id'] ,
                //     'class_title' =>  $childInfo[$key]['classid'] ,
                //     'class_child_age' =>  $childInfo[$key]['class_child_age'],
                // );
            }
        }

        // $classList = CommonUtils::sortByField($classList, 'class_child_age');
        // $classList = CommonUtils::sortByField($classList, 'class_title');

        $criteria = new CDbCriteria();
        $criteria->compare('classid', $classIds);
        $criteria->order = 'child_age ASC, title ASC';;
        $classModels = IvyClass::model()->findAll($criteria);
        foreach ($classModels as $key => $value) {
            $classList[] = array(
                'class_id' =>  $value->classid ,
                'class_title' =>  $value->title ,
                'class_child_age' =>  $value->child_age,
            );
        }
        $newChildInfo = array();
        foreach ($classList as $class) {
            $class_id = $class['class_id'];
            $newChildInfo = array_merge($newChildInfo, $classChild[$class_id]);
        }

        $answeredData = array();
        $unAnswerData = array();
        foreach ($newChildInfo as $k => $v) {
            if ($v['is_answer'] == 0) {
                $unAnswerData[] = $v;
            } else {
                $answeredData[] = $v;
            }
        }
        $newChildInfo = array_merge($answeredData, $unAnswerData);

        $this->render('result', array(
            'qIds' => $qIds,
            'childInfo' => array_values($newChildInfo),
            'question' => $question,
            'template_id' => $template_id,
            'return_id' => $return_id,
            'answer' => $answer,
            'is_positioning' => $is_positioning,  // 1 显示是否有定位金 0 为不需要显示定位金
            'invoiceNum' => count($invoiceInfo),
            'templateTitle' => $templateModel->getTitle(),
            'classChild' => $classChild,
            'classList' => $classList,
        ));
    }

    public function actionGetBigImage() {
        $q_signature_raw = Yii::app()->request->getParam('q_signature_raw', '');;
        $url = '';
        if ($q_signature_raw) {
            $url =  SurveyReturn::getThumbOssImageUrl($q_signature_raw);
        }
        $this->addMessage('state', 'message');
        $this->addMessage('message', 'message');
        $this->addMessage('data', $url);
        $this->showMessage();
    }

    function getChildQuestion($firstQid, $qidList, $result) {
        $q = array();
        foreach ($qidList as $qid => $val) {
            if ($val['type'] == 'question' && $val['pid'] == $firstQid) {
                $q = $val;
                break;
            }
        }
        if ($q) {
            $result[$q['id']] = $q;
            return $this->getChildQuestion($q['id'], $qidList, $result); // 递归
        } else {
            return $result; // 递归结束
        }
    }

    function getChildArray($array, $parent_id = 0) {
        $temp_array = array();
        foreach ($array as $element) {
            if ($element['pid'] == $parent_id) {
                $element['subs'] = $this->getChildArray($array, $element['id']);
                $temp_array[] = $element;
            }
        }
        return $temp_array;
    }

    function getAllChildQuestion($array) {
        $temp_array = array();
        foreach ($array as $element) {
            if ($element['subs']) {
                $temp_array[] = $this->getAllChildQuestion($element['subs']);
            }
            if ($element['type'] == 'question') {
                return $element['id'];
            }
        }
        return $temp_array;
    }

    public function actionBlockedChildList() 
    {
        $survey_id = Yii::app()->request->getParam('survey_id', '');
        $school_id = $this->branchId;
        $models = SurveyReturnChildBlocked::model()->findAllByAttributes(array('school_id' => $school_id, 'survey_id' => $survey_id));
        $childIds = array();
        foreach($models as $model) {
            $childIds[] = $model->childid;
        }
        $childModels = ChildProfileBasic::model()->with(array('ivyclass'))->findAllByPk($childIds, array('order' => 'ivyclass.child_age'));
        $returnData = array();
        foreach($childModels as $childModel) {
            $returnData[] = array(
                'childid' => $childModel->childid,
                'url' => $this->createUrl("/child/index/index", array('branchId' => $this->branchId, 'childid' => $childModel->childid)),
                'childName' => $childModel->getChildName(),
                'className' => $childModel->ivyclass->title,
            );
        }

        $this->addMessage('state', 'success');
        $this->addMessage('message', 'success');
        $this->addMessage('data', $returnData);
        $this->showMessage();
    }
    
    public function actionAddBlockedChild() 
    {
        $id = Yii::app()->request->getParam('id', '');
        if(!$id){
            $this->addMessage('state', 'fail');
            $this->addMessage('message', Yii::t('message', '参数错误'));
            $this->showMessage();
        }

        $childSurveyModel = SurveyReturnChild::model()->findByPk($id);
        if(!$childSurveyModel){
            $this->addMessage('state', 'fail');
            $this->addMessage('message', Yii::t('message', '数据错误'));
            $this->showMessage();
        }
        if ($childSurveyModel->is_answer) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', Yii::t('message', '已填写，请先删除问卷结果'));
            $this->showMessage();
        }

        $school_id = $this->branchId;
        $survey_id = $childSurveyModel->survey_id;
        $childid = $childSurveyModel->childid;
        $model = SurveyReturnChildBlocked::model()->findByAttributes(array('school_id' => $school_id, 'survey_id' => $survey_id, 'childid' => $childid));
        if ($model) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '已存在');
            $this->showMessage();
        }
        $model = new SurveyReturnChildBlocked();
        $model->school_id = $school_id;
        $model->survey_id = $survey_id;
        $model->childid = $childid;
        $model->created_by = $this->staff->uid;
        $model->updated_by = $this->staff->uid;
        $model->created_at = time();
        $model->updated_at = time();
        if (!$model->save()) {
            $err = current($model->getErrors());
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '保存失败：' . $err[0]);
            $this->showMessage();
        }
        $childSurveyModel->delete();
        $this->addMessage('state', 'success');
        $this->addMessage('message', 'success');
        $this->addMessage('data', $id);
        $this->addMessage('callback', 'cbBlockedCallback');
        $this->showMessage();
    }


    public function actionDelBlockedChild() 
    {
        $child_id = Yii::app()->request->getParam('child_id', '');
        $survey_id = Yii::app()->request->getParam('survey_id', '');
        if (!$child_id || !$survey_id) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '参数错误');
            $this->showMessage();
        }
        $school_id = $this->branchId;
        $model = SurveyReturnChildBlocked::model()->findByAttributes(array('school_id' => $school_id, 'survey_id' => $survey_id, 'childid' => $child_id));
        if ($model) {
            $model->delete();
        }

        $this->addMessage('state', 'success');
        $this->addMessage('message', 'success');
        $this->addMessage('data', $child_id);
        $this->addMessage('callback', 'cbBlockedDelCallback');
        $this->showMessage();
    }


    // 同步需填写人数
    public function actionSynchronizeChild()
    {
        $template_id = Yii::app()->request->getParam('template_id', '');
        $return_id = Yii::app()->request->getParam('return_id', '');

        if(!$template_id || !$return_id){
            $this->addMessage('state', 'fail');
            $this->addMessage('message', Yii::t('message', '参数错误'));
            $this->showMessage();
        }

        $surveyReturnModel = SurveyReturn::model()->findByPk($return_id);
        if($surveyReturnModel->school_id != $this->branchId){
            $this->addMessage('state', 'fail');
            $this->addMessage('message', Yii::t('message', '非法操作'));
            $this->showMessage();
        }

        // 根据学校id和班级类型，拿到当前所有有效的班级id
        $criteria = new CDbCriteria();
        $criteria->compare('schoolid', $surveyReturnModel->school_id);
        $criteria->compare('yid', $surveyReturnModel->yid);
        $criteria->compare('stat', 10);
        $criteria->compare('classtype', explode(',', $surveyReturnModel->class_type));
        $criteria->index = 'classid';
        $classModel = IvyClass::model()->findAll($criteria);

        // 拿到现在已经增加的学生
        $criteria = new CDbCriteria();
        $criteria->compare('school_id', $surveyReturnModel->school_id);
        $criteria->compare('template_id', $template_id);
        $criteria->compare('survey_id', $return_id);
        $criteria->index = 'childid';
        $surveyRetunrmChildModel = SurveyReturnChild::model()->findAll($criteria);

        // 查找排除的学生
        $criteria = new CDbCriteria();
        $criteria->compare('school_id', $surveyReturnModel->school_id);
        $criteria->compare('survey_id', $return_id);
        $criteria->index = 'childid';
        $blockedModels = SurveyReturnChildBlocked::model()->findAll($criteria);
        $blockedChildIds = array_keys($blockedModels);

        // 拿到所有有效班级里的有效学生数据
        $criteria = new CDbCriteria();
        $criteria->compare('classid', array_keys($classModel));
        $criteria->addNotInCondition('childid', array_keys($surveyRetunrmChildModel));
        $criteria->compare('status', array(ChildProfileBasic::STATS_REGISTERED, ChildProfileBasic::STATS_ACTIVE_WAITING,ChildProfileBasic::STATS_ACTIVE));
        $childModel = ChildProfileBasic::model()->findAll($criteria);

        $childInfo = array();
        if($childModel) {
            foreach ($childModel as $child) {
                if (in_array($child->childid, $blockedChildIds)) {
                    continue;
                }
                $returnChildModel = new SurveyReturnChild();
                $returnChildModel->school_id = $surveyReturnModel->school_id;
                $returnChildModel->yid = $surveyReturnModel->yid;
                $returnChildModel->classid = $child->classid;
                $returnChildModel->classType = $child->ivyclass->classtype;
                $returnChildModel->childid = $child->childid;
                $returnChildModel->survey_id = $surveyReturnModel->id;
                $returnChildModel->template_id = $surveyReturnModel->tid;
                $returnChildModel->is_answer = 0;
                $returnChildModel->status = 1;
                $returnChildModel->updated_by = $this->staff->uid;
                $returnChildModel->updated_at = time();
                $returnChildModel->created_by = $this->staff->uid;
                $returnChildModel->created_at = time();
                if ($returnChildModel->save()) {
                    $childInfo[] = array(
                        'id' => $returnChildModel->id,
                        'classid' => $returnChildModel->classInfo->title,
                        'classType' => $returnChildModel->classType,
                        'childid' => $returnChildModel->childInfo->getChildName(),
                        'is_answer' => $returnChildModel->is_answer,
                        'is_invoice' => 0,
                        'url' => $this->createUrl("/child/index/index", array('branchId' => $this->branchId, 'childid' => $returnChildModel->childid)),
                        'answerInfo' => array(),
                        'updated_at' => '',
                    );
                }
            }
        }

        $this->addMessage('state', 'success');
        $this->addMessage('data', $childInfo);
        $this->addMessage('message', Yii::t('message', 'success'));
        $this->showMessage();
    }

    public function actionDeleteAnswer()
    {
        $id = Yii::app()->request->getParam('id', '');
        if(!$id){
            $this->addMessage('state', 'fail');
            $this->addMessage('message', Yii::t('message', '参数错误'));
            $this->showMessage();
        }

        $res = SurveyReturnAnswer::model()->deleteAll('cid=:cid',array(':cid'=> $id));
        if($res){
            SurveyReturnChild::model()->updateByPk($id,array('is_answer' => 0, 'updated_at' => time(), 'updated_by' => $this->staff->uid));
        }

        $this->addMessage('state', 'success');
        $this->addMessage('callback', 'cbResultCallback');
        $this->addMessage('data', $id);
        $this->addMessage('message', Yii::t('message', 'success'));
        $this->showMessage();
    }


    // 单独修改学校开启状态和时间，班级
    public function actionUpdateReturn()
    {
        $id = Yii::app()->request->getParam('id', '');
        $start_timestamp = Yii::app()->request->getParam('start_timestamp', '');
        $end_timestamp = Yii::app()->request->getParam('end_timestamp', '');
        $status = Yii::app()->request->getParam('status', 1);
        $type = Yii::app()->request->getParam('type','');
        $class_type = Yii::app()->request->getParam('class_type', array());


        if(!$type){
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '类型不能为空');
            $this->showMessage();
        }

        $model = SurveyReturn::model()->findByPk($id);
        if(!$model){
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '数据错误');
            $this->showMessage();
        }

        if($type == 'saveTime'){
            $model->start_timestamp = ($start_timestamp) ? strtotime($start_timestamp) : '';
            $model->end_timestamp = ($end_timestamp) ? strtotime($end_timestamp) : '';
            $model->status = $status;
            $model->updated_at = time();
            $model->updated_by = $this->staff->uid;
            if(!$model->save()){
                $this->addMessage('state', 'fail');
                $err = current($model->getErrors());
                $this->addMessage('message', $err[0]);
                $this->showMessage();
            }
        }

        if($type == 'saveClass'){
            $connection = Yii::app()->db->beginTransaction();
            try {
                $class = explode(',', $model->class_type);

                $delResultChildClass = array_diff($class, $class_type); // 要删除的班级数组ID

                if($delResultChildClass) {
                    $delReg = SurveyReturnChild::getDelClass($model->id, $delResultChildClass);
                    if ($delReg['state'] == 'fail') {
                        $connection->rollBack();
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', $delReg['message']);
                        $this->showMessage();
                    }
                }

                $saveResultChildClass = array_diff($class_type, $class); // 要增加的班级数组ID
                if($saveResultChildClass) {
                    $saveReg = SurveyReturnChild::getSaveClass($model, $saveResultChildClass, $this->staff->uid);
                    if ($saveReg['state'] == 'fail') {
                        $connection->rollBack();
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', $saveReg['message']);
                        $this->showMessage();
                    }
                }
                $classType = ($class_type) ? implode(',', $class_type) : '';
                $model->class_type = $classType;
                if (!$model->save()) {
                    $connection->rollBack();
                    $this->addMessage('state', 'fail');
                    $err = current($model->getErrors());
                    $this->addMessage('message', $err[0]);
                    $this->showMessage();
                }
                $connection->commit();

            } catch (\Exception $e) {
                $connection->rollBack();
                $this->addMessage('state', 'fail');
                $this->addMessage('message', $e->getMessage());
                $this->showMessage();
            }
        }

        $data = array(
            'id' => $model->id,
            'template_id' => $model->tid,
            'start_timestamp' => date("Y-m-d", $model->start_timestamp),
            'end_timestamp' => date("Y-m-d", $model->end_timestamp),
            'classType' => explode(',', $model->class_type),
        );
        $this->addMessage('state', 'success');
        $this->addMessage('data', $data);
        $this->addMessage('message', Yii::t('message', 'success'));
        $this->showMessage();
    }

    // 调查结果统计
    public function actionAnswerCount()
    {
        $template_id = Yii::app()->request->getParam('template_id', 0);
        $return_id = Yii::app()->request->getParam('return_id', 0);

        $surveyModel = SurveyReturn::model()->findByPk($return_id);
        $templateModel = SurveyReturnTemplate::model()->findByPk($template_id);
        if (!$surveyModel || !$templateModel) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '参数错误');
            $this->showMessage();
        }

        $criteria = new CDbCriteria();
        $criteria->compare('t.school_id', $this->branchId);
        $criteria->compare('t.survey_id', $return_id);
        $criteria->compare('t.del_flag', 0);
        $criteria->compare('t.template_id', $template_id);
        $criteria->compare('question.type', array('question', 'option'));
        $criteria->compare('returnchild.is_answer', 1);
        $criteria->compare('returnchild.status', 1);
        $criteria->with = array('question', 'returnchild');
        $answerModels = SurveyReturnAnswer::model()->findAll($criteria);
        $answers = array();
        foreach($answerModels as $answerModel) {
            $qid = $answerModel->q_id;
            $qval = trim($answerModel->q_val, '"');
            if ($qid && $qval) {
                if (!isset($answers[$qid][$qval])) {
                    $answers[$qid][$qval] = 1;
                } else {
                    $answers[$qid][$qval]++;
                }
            }
        }


        // 查找题目
        $criteria = new CDbCriteria();
        $criteria->compare('survey_id', $template_id);
        $criteria->compare('type', array('question', 'option'));
        $criteria->compare('status', 1);
        $criteria->index = 'id';
        $questionModels = SurveyReturnQuestions::model()->findAll($criteria);

        $result = array();
        foreach($answers as $qid => $answer) {
            $qModel = $questionModels[$qid];
            $data = array();
            foreach ($answer as $oid => $count) {
                $oModel = $questionModels[$oid];
                $data[$oModel->option_sort] = array('name' => CommonUtils::autoLang($oModel->cn_title, $oModel->en_title), 'value' => $count);
            }
            ksort($data);
            $data = array_values($data);
            $result[] = array(
                'name' => CommonUtils::autoLang($qModel->cn_title, $qModel->en_title),
                'data' => $data,
            );
        }
        $this->addMessage('state', 'success');
        $this->addMessage('data', $result);
        $this->showMessage();
    }
}

