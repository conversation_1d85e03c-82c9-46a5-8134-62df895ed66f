<div class="mb10">
    <a href="<?php echo $this->createUrl("/operations/event/update");?>" class="btn"><span class="add"></span>添加活动</a>
</div>

<?php
$this->widget('ext.ivyCGridView.IvyCGridView', array(
	'id'=>'points-product-grid',
	'dataProvider'=>$model->search(),
//	'filter'=>$model,
    'colgroups'=>array(
        array(
            "colwidth"=>array(null,150,150,150),
//            "htmlOptions"=>array("align"=>"left", "span"=>2)
        )
    ),
	'columns'=>array(
		array(
            'name'=>'title',
            'value'=>'CommonUtils::autoLang($data->cn_title, $data->en_title)',
        ),
        array(
            'name'=>'event_date',
            'value'=>'OA::formatDateTime($data->event_date)',
        ),
        array(
            'name'=>'学校',
            'value'=>'Yii::app()->controller->branchArr[$data->school_id]',
        ),
        array(
			'class'=>'CButtonColumn',
            'template' => '{member} {update} {delete}',
            'updateButtonImageUrl'=>false,
            'deleteButtonImageUrl'=>false,
            'afterDelete'=>'function(link,success,data){ if(success && data) alert(data);}',
            'buttons'=>array(
                'member'=>array(
                    'label'=>'查看报名名单',
                    'url'=>'Yii::app()->createUrl("/operations/event/viewMember", array("id" => $data->id))',
                    'options'=>array('class'=>''),
                ),
            ),
		),
	),
)); ?>