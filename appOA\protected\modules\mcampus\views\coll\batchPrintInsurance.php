<?php
$medicalCn = '';
$medicalEn = '';
$filedata = '';
$data = '';
if($model){
    $data = json_decode($model->data);
    $filedata = ($data->filedata) ? $data->filedata : '' ;
}
?>
<?php $studentNum = count($child_infos);$i=0;?>
<style>
    .container-fluid{
        width:860px;
        margin:0 auto
    }
    img{
        margin-top:10px;
        max-width:100%;
        height:auto
    }
    .sign{
        float: right;
        margin-right:20px;
        margin-bottom:20px;
        max-height: 15mm;
    }
    .sign img{
        width:200px;
        height: 110px;
    }
    @media print {
        .print_img{
            max-height: 290mm;
            display: block;
            margin: 0 auto;
        }
        .alwaysAttr{
            page-break-after:always;
        }
        .print_protocol_img
        {
            max-height: 290mm;
            display: block;
            margin: 0 auto;
        }
    }
</style>
<?php foreach ($child_infos as $child_id=>$child_info){?>
    <?php
    $i++;
    $allImg[$child_id] = array();
    if(!in_array($step_data[$child_id]['insurance_type'],array(1,2))){
        if($step_data[$child_id]['is_continued'] == 1 && $step_data[$child_id]['is_insured']==1 ){
            //学生医保卡
            foreach ($step_data[$child_id]['insurance_card_url'] as $item){
                $allImg[$child_id][] = $item;
            }
        }
        if($step_data[$child_id]['is_insured'] == 2){
            //学生户口簿首页和本人页原比例复印件或扫描件
            if(in_array($step_data[$child_id]['insurance_type'],array(3,4,5,6))){
                foreach ($step_data[$child_id]['insurance_file_url']['student_account'] as $item){
                    $allImg[$child_id][] = $item;
                }
            }
            //请上传学生近期1寸免冠正面彩色白底照片电子版，照片须符合二代身份证要求
            if(in_array($step_data[$child_id]['insurance_type'],array(3,4,5,6,7))){
                foreach ($step_data[$child_id]['insurance_file_url']['student_photo'] as $item){
                    $allImg[$child_id][] = $item;
                }
            }
            //父（母）的北京市户口簿首页和本人页原比例复印件或扫描件。
            if($step_data[$child_id]['insurance_type'] == 4){
                foreach ($step_data[$child_id]['insurance_file_url']['parent_account'] as $item){
                    $allImg[$child_id][] = $item;
                }
            }
            //学生的出生证明原比例复印件或扫描件。如有外文的文件需先翻译成中文，翻译件必须有翻译公司或公证处公章。
            if(in_array($step_data[$child_id]['insurance_type'],array(4,5,6))){
                foreach ($step_data[$child_id]['insurance_file_url']['student_birth'] as $item){
                    $allImg[$child_id][] = $item;
                }
            }
            //父母的结婚证原比例复印件或扫描件。继子女关系的需加收原生父母的离婚协议或离婚判决书复印件、原生父（母）与继母（父）结婚证复印件。养子女关系的需加收民政部门或其他政府部门的抚养证明材料复印件。
            if(in_array($step_data[$child_id]['insurance_type'],array(4,5,6))){
                foreach ($step_data[$child_id]['insurance_file_url']['parent_marry'] as $item){
                    $allImg[$child_id][] = $item;
                }
            }
            //学生二代身份证原比例复印件或扫描件，还未申领身份证的不用交
            if($step_data[$child_id]['insurance_type']==3){
                foreach ($step_data[$child_id]['insurance_file_url']['student_id_card'] as $item){
                    $allImg[$child_id][] = $item;
                }
            }
            //学生二代身份证原比例复印件或扫描件，还未申领身份证的不用交。（香港和澳门学生需提交中华人民共和国港澳居民居住证；台湾学生需提交中华人民共和国台湾居民居住证）
            if(in_array($step_data[$child_id]['insurance_type'],array(4,5,6))){
                foreach ($step_data[$child_id]['insurance_file_url']['student_id_card1'] as $item){
                    $allImg[$child_id][] = $item;
                }
            }
            //登记有该子女信息的父(母)的有效《北京市工作居住证》原比例复印件或扫描件。（注：工作居住证上必须有孩子的名字，否则无法参保。）
            if($step_data[$child_id]['insurance_type']==5){
                foreach ($step_data[$child_id]['insurance_file_url']['work_permit'] as $item){
                    $allImg[$child_id][] = $item;
                }
            }
            //部队团级以上单位政治机关开具的证明（原件）
            if($step_data[$child_id]['insurance_type']==6){
                foreach ($step_data[$child_id]['insurance_file_url']['political_organ'] as $item){
                    $allImg[$child_id][] = $item;
                }
            }
            //学生本人护照原比例复印件或扫描件
            if($step_data[$child_id]['insurance_type']==7){
                foreach ($step_data[$child_id]['insurance_file_url']['student_passport'] as $item){
                    $allImg[$child_id][] = $item;
                }
            }
            //学生本人《外国人永久居留身份证》原比例复印件或扫描件。
            if($step_data[$child_id]['insurance_type']==7){
                foreach ($step_data[$child_id]['insurance_file_url']['foreigner_id_card'] as $item){
                    $allImg[$child_id][] = $item;
                }
                foreach ($step_data[$child_id]['insurance_file_url']['commitment'] as $item){
                    $allImg[$child_id][] = $item;
                }
            }
        }
    }
    ?>

    <div class="container-fluid">
        <div class ="row">
            <table class="table">
                <tbody>
                <tr>
                    <th  width="25%"><?php echo Yii::t('reg', 'Name') ?></th>
                    <td colspan="3"><?php echo $child_info->getChildName() ?></td>
                </tr>
                <tr>
                    <th><?php echo Yii::t('labels', 'Date of Birth') ?></th>
                    <td colspan="3"><?php echo $child_info->birthday_search ?></td>
                </tr>
                <tr>
                    <th><?php echo Yii::t('labels', 'Class') ?></th>
                    <td colspan="3"><?php echo $child_info->ivyclass->title?></td>
                </tr>
                <tr>
                    <th><?php echo Yii::t('reg', 'Has your child ever signed up for Beijing Social Medical Insurance') ?></th>
                    <td colspan="3"><?php echo $step_data[$child_id]['is_insured']==1 ?  "是":  "否" ?></td>
                </tr>
                <?php if($step_data[$child_id]['is_insured']==1){?>
                    <tr>
                        <th><?php echo Yii::t('reg', 'Do you agree to continue Beijing Social Medical Insurance through Daystar Academy?') ?></th>
                        <td colspan="3"><?php echo $step_data[$child_id]['is_continued']==1 ?  "是":  "否" ?></td>
                    </tr>
                <?php }?>
                <?php if($step_data[$child_id]['is_continued']==1 && $step_data[$child_id]['is_insured']==1){?>
                    <tr>
                        <th><?php echo Yii::t('reg', "Student's ID Number") ?></th>
                        <td colspan="3"><?php echo $step_data[$child_id]['student_id'] ?></td>
                    </tr>
                    <tr>
                        <th><?php echo Yii::t('reg', "Phone number registered while applying for Social Medical insurance") ?></th>
                        <td colspan="3"><?php echo $step_data[$child_id]['insured_phone'] ?></td>
                    </tr>
                <?php }?>
                <?php if($step_data[$child_id]['is_insured'] == 2){?>
                    <tr>
                        <th>参保类型:</th>
                        <td colspan="3">
                            <?php
                            switch ($step_data[$child_id]['insurance_type']){
                                case 1:
                                    echo   Yii::t('reg', 'Students DO NOT meet any of the criteria listed above.');
                                    break;
                                case 2:
                                    echo   Yii::t('reg', 'Qualified but willingly forgo the opportunity to participate the Social Medical Insurance with the school.');
                                    break;
                                case 3:
                                    echo   Yii::t('reg', 'Student with Beijing Household Resident Status');
                                    break;
                                case 4:
                                    echo   Yii::t('reg', 'Student without Beijing Household Resident Status  – one of the parents is BJ Household Resident');
                                    break;
                                case 5:
                                    echo   Yii::t('reg', 'Student without Beijing Household Resident Status – one of the parents has official Beijing Working and Residing Permit.');
                                    break;
                                case 6:
                                    echo   Yii::t('reg', 'Minor children of Military Personnel');
                                    break;
                                case 7:
                                    echo   Yii::t('reg', 'Students of foreign nationals have Alien Permanent Residence ID Card and do not have any medical insurance.');
                                    break;
                            }
                            ?>
                        </td>
                    </tr>
                    <!--                在全市定点医疗机构范围内就近选择4所医院作为学生的定点医疗机构-->
                    <?php if(in_array($step_data[$child_id]['insurance_type'],array(3,4,5,6,7))){?>
                        <tr>
                            <th><?php echo Yii::t('reg', 'Choose 4 from National Medical Insurance Designated Hospitals within BJ city as student’s preferred hospital choices') ?></th>
                            <td colspan="3">
                                <?php foreach ($step_data[$child_id]['insurance_hospitals'] as $item){?>
                                    <?php echo $item.PHP_EOL?>
                                <?php }?>
                            </td>
                        </tr>
                    <?php }?>
                <?php }?>
                </tbody>
            </table>
        </div>
        <div style="page-break-after:always;"></div>
        <!--    协议-->
        <?php if(isset($step_data[$child_id]['extraInfo']['protocol']) && !empty($step_data[$child_id]['extraInfo']['protocol'])){?>
            <div class="row">
                <?php $protocol_num = count($step_data[$child_id]['extraInfo']['protocol'])?>
                <?php if(isset($step_data[$child_id]['extraInfo']['protocol']) && !empty($step_data[$child_id]['extraInfo']['protocol'])){?>
                    <?php foreach ($step_data[$child_id]['extraInfo']['protocol'] as $k=>$v){?>
                        <div>
                            <?php echo $child_info->getChildName() ?>
                            <?php echo $child_info->ivyclass->title?>
                        </div>
                        <p>
                            <img class="img-responsive print_protocol_img" src="<?php echo $v; ?>">
                        </p>
                        <?php if(($k+1)<$protocol_num){?>
                            <div style="page-break-after:always;"></div>
                        <?php }?>
                        <?php if(($k+1)==$protocol_num){?>
                                <?php if(!empty($step_data[$child_id]['childStatus']['sign_url'])){?>
                                <!--        签名-->
                                <div class="sign">
                                    <img src="<?php echo $step_data[$child_id]['childStatus']['sign_url']?>" alt="" >
                                </div>
                                <?php }?>
                            <div style="page-break-after:always;"></div>
                        <?php }?>
                    <?php }?>
                <?php }?>
            </div>
        <?php }?>
        <!--    不参加社保和放弃社保的没有图片-->
        <?php if(!in_array($step_data[$child_id]['insurance_type'],array(1,2))){?>
            <div class="row">
                <?php if(!empty($allImg[$child_id])){?>
                    <?php foreach ($allImg[$child_id] as $k=>$val){?>
                        <div>
                            <?php echo $child_info->getChildName() ?>
                            <?php echo $child_info->ivyclass->title?>
                        </div>
                        <p class="pageBreak">
                            <?php if(count($allImg[$child_id]) == $k+1){?>
                                <img  class="img-responsive print_img" src="<?php echo $val; ?>">
                            <?php }else{?>
                                <img  class="img-responsive print_img alwaysAttr" src="<?php echo $val; ?>">
                            <?php }?>
                        </p>
                    <?php }?>
                <?php }?>
            </div>
        <?php }?>
    </div>
    <?php if($studentNum != $i){?>
        <div style="page-break-after:always;"></div>
    <?php }?>
<?php }?>
