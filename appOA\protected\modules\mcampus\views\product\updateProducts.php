<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
    <h4 class="modal-title" id="exampleModalLabel"><?php echo $model->id? '更新商品：':'添加商品：'; ?></h4>
</div>
<?php
$config = Products::config();
$attrType = array();
foreach ($config as $ckey=>$cfg) {
    $attrType[$ckey] = $cfg['title'];
}

$type = "";
if($model->attr){
    $type = $model->attr[0]->type;
}

$form=$this->beginWidget('CActiveForm', array(
    'id'=>'courseGroup-form',
    'enableAjaxValidation'=>false,
    'htmlOptions'=>array('class'=>'J_ajaxForm form-horizontal', 'role'=>'form'),
)); ?>

<div class="modal-body">
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model,'title_cn'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->textField($model,'title_cn',array('maxlength'=>255,'class'=>'form-control')); ?>
            <?php echo CHtml::HiddenField("Products[cid]", $pcid); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model,'title_en'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->textField($model,'title_en',array('maxlength'=>255,'class'=>'form-control')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model,'unit_price'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->textField($model,'unit_price',array('maxlength'=>255,'class'=>'form-control')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model,'scribing_price'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->textField($model,'scribing_price',array('maxlength'=>255,'class'=>'form-control')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model,'intro_cn'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->textArea($model,'intro_cn',array('class'=>'form-control')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model,'intro_en'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->textArea($model,'intro_en',array('class'=>'form-control')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model,'status'); ?></label>
        <div class="col-xs-9 checkbox" style="padding-top: 0.5%">
            <label>
                <?php echo $form->checkbox($model,'status', array('1'=>"开启"),array('template'=>'<label class="radio-inline">{input}{label}</label>','separator'=>'')); ?>有效
            </label>
        </div>
    </div>
<!--    推荐商品-->
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model,'featured'); ?></label>
        <div class="col-xs-9 checkbox" style="padding-top: 0.5%">
            <label>
                <?php echo $form->checkbox($model,'Featured', array('1'=>"开启"),array('template'=>'<label class="radio-inline">{input}{label}</label>','separator'=>'')); ?>首页推荐
            </label>
        </div>
    </div>

    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model,'sort'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->textField($model,'sort',array('class'=>'form-control')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model,'类型'); ?></label>
        <div class="col-xs-9">
            <?php
            if(!$model->id){
                echo $form->dropDownList($model, 'type', $attrType, array('empty'=> Yii::t('global','Please Select'), 'class'=>'form-control'));
            }else{
                echo CHtml::textField("", $attrType[$model->type], array('maxlength'=>255,'class'=>'form-control', 'disabled' => "disabled"));
            }?>
        </div>
    </div>
</div>
<div class="modal-footer">
    <button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global','Submit');?></button>
    <button type="button" class="btn btn-default"  data-dismiss="modal"><?php echo Yii::t('global','Cancel');?></button>
</div>
<?php $this->endWidget(); ?>
