<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span>
    </button>
    <h4 class="modal-title"
        id="exampleModalLabel"><?php echo $model->status == AsaExpense::STATS_REFER ? Yii::t('asa', '报表审核') : Yii::t('asa', '审核通过') ?></h4>
</div>
<?php $form = $this->beginWidget('CActiveForm', array(
    'id' => 'course-forms',
    'enableAjaxValidation' => false,
    'htmlOptions' => array('class' => 'J_ajaxForm form-horizontal', 'role' => 'form', 'enctype' => "multipart/form-data"),
));

?>
<div class="modal-body">
    <div class="form-group">
        <label class="col-xs-2 text-right"><?php echo $form->labelEx($model, 'voucherid'); ?></label>

        <div class="col-xs-8">
            <div><?php echo sprintf("%08d",$model->id) ?></div>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 text-right"><?php echo $form->labelEx($model, 'title'); ?></label>

        <div class="col-xs-8">
            <div><?php echo $model->title ?></div>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 text-right"><?php echo $form->labelEx($model, 'branch'); ?></label>

        <div class="col-xs-8">
            <div><?php echo $branchObj->title ?></div>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 text-right"><?php echo $form->labelEx($model, 'amount'); ?></label>

        <div class="col-xs-8">
            <?php echo $model->amount ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 text-right"><?php echo $form->labelEx($model, 'payee_user'); ?></label>

        <div class="col-xs-8">
            <?php echo $model->payee_user ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 text-right"><?php echo $form->labelEx($model, 'payee_account'); ?></label>

        <div class="col-xs-8">
            <?php echo $model->payee_account ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 text-right"><?php echo $form->labelEx($model, 'payee_bank'); ?></label>

        <div class="col-xs-8">
            <?php echo ($model->payee_bank) ? $bank[$model->payee_bank] : '' ; ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 text-right"><?php echo $form->labelEx($model, 'created'); ?></label>

        <div class="col-xs-8">
            <div><?php echo date("Y-m-d", $model->created) ?></div>
        </div>
    </div>
    <?php if ($model->asaExpenseItem) { ?>
        <div class="form-group">
            <label class="col-xs-2 text-right"><?php echo Yii::t('asa', '详细明细'); ?></label>

            <div class="col-xs-8">
                <table class="table table-bordered" width="400" border="0" cellspacing="0" cellpadding="0">
                    <thead>
                    <tr>
                        <td width="100"><?php echo Yii::t('asa', '内容') ?></td>
                        <td width="100"><?php echo Yii::t('asa', '金额') ?></td>
                        <td width="100"><?php echo Yii::t('asa', '课程组') ?></td>
                        <td width="100"><?php echo Yii::t('asa', '课程') ?></td>
                        <td width="100"><?php echo Yii::t('asa', '文件') ?></td>
                    </tr>
                    </thead>
                    <?php foreach ($model->asaExpenseItem as $item) { ?>
                        <tr>
                            <td><?php echo $item->content ?></td>
                            <td><?php echo $item->amount ?></td>
                            <td><?php echo ($item->groupid) ? $item->asaCourseGroup->getName() : Yii::t('asa','所有课程组') ?></td>
                            <td><?php echo ($item->courseid) ? $item->asaCourse->getTitle() : Yii::t('asa','所有课程') ?></td>
                            <td><?php
                                if ($item->files) {
                                    foreach (CJSON::decode($item->files) as $k=>$filesItem) {
                                        echo '<a class="btn btn-info btn-xs" target = "_blank" href="' . $this->createUrl("downloadsExpense", array("fileName" => $filesItem)) . '"><span>文件    </span></a> ';
                                    }
                                }
                                ?>
                            </td>
                        </tr>
                    <?php } ?>
                </table>
            </div>
        </div>
    <?php } ?>
    <div class="form-group">
        <label class="col-xs-2 text-right"><?php echo $form->labelEx($model, 'status'); ?></label>

        <div class="col-xs-8">
            <?php
            if ($model->status == AsaExpense::STATS_REFER) {
                echo $form->radioButtonList($model, 'status', array('20' => Yii::t('asa', '通过'), '30' => Yii::t('asa', '驳回')), array('template' => '<span class="">{input}{label}</span> ', 'separator' => '&nbsp;&nbsp;'));
            } else {
                $statusList = AsaExpense::getConfig();
                $statusText = $statusList[$model->status];
                echo $statusText;
            }
            ?>
        </div>
    </div>
    <?php if ($model->status == AsaExpense::STATS_REFER): ?>
    <div class="form-group">
        <label class="col-xs-2 text-right"><?php echo $form->labelEx($model, 'memo'); ?></label>

        <div class="col-xs-8">
            <div><?php echo $form->textArea($model, 'memo', array('class' => 'form-control')); ?></div>
        </div>
    </div>
    <?php endif; ?>
</div>
<div class="modal-footer">
    <button type="button" class="btn btn-default"
            data-dismiss="modal"><?php echo Yii::t('global', 'Cancel'); ?></button>
    <?php if ($model->status == AsaExpense::STATS_REFER): ?>
        <button type="submit"
                class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global', 'Submit'); ?></button>
    <?php endif; ?>
</div>
<?php $this->endWidget(); ?>
