<?php

/**
 * This is the model class for table "ivy_notes_sch_cla".
 *
 * The followings are the available columns in table 'ivy_notes_sch_cla':
 * @property integer $id
 * @property string $schoolid
 * @property integer $classid
 * @property integer $yid
 * @property integer $type
 * @property integer $weekid
 * @property integer $weeknumber
 * @property integer $stat
 * @property string $en_title
 * @property string $cn_title
 * @property string $en_content
 * @property string $cn_content
 * @property string $en_important
 * @property string $cn_important
 * @property integer $updated_timestamp
 * @property integer $userid
 * @property string $cd_uids
 * @property integer $operate_uid
 */
class NotesSchCla extends CActiveRecord
{
    const NOTESSCHCLA_TYPE_SCHOOLNEW = 10; //学校新闻
    const NOTESSCHCLA_TYPE_CLASSNEW = 20;  //班级新闻
    const NOTESSCHCLA_TYPE_GRADE_SCHOOLNEW = 30; //小学新闻
    const NOTESSCHCLA_TYPE_MIDDLE_SCHOOLNEW = 40; //中学新闻
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return IvyNotesSchCla the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_notes_sch_cla';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('schoolid, classid, type, weeknumber, stat, updated_timestamp, userid', 'required'),
			array('classid, yid, type, weekid, weeknumber, stat, updated_timestamp, userid', 'numerical', 'integerOnly'=>true),
			array('schoolid, en_title, cn_title', 'length', 'max'=>255),
			array('en_important, en_content', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, schoolid, classid, yid, type, weekid, weeknumber, stat, en_title, cn_title, en_content, cn_content, en_important, cn_important, updated_timestamp, userid', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'yInfo'=>array(self::BELONGS_TO, 'Calendar', 'yid'),
            'weekInfo'=>array(self::HAS_ONE, 'CalendarWeek', '', 'on'=>'t.yid = weekInfo.yid and t.weeknumber=weekInfo.weeknumber'),
            'classTitle'=>array(self::BELONGS_TO, 'IvyClass', 'classid'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'schoolid' => 'Schoolid',
			'classid' => 'Classid',
			'yid' => 'Yid',
			'type' => 'Type',
			'weekid' => 'Weekid',
			'weeknumber' => 'Weeknumber',
			'stat' => 'Stat',
			'en_title' => 'En Title',
			'cn_title' => 'Cn Title',
			'en_content' => 'En Content',
			'cn_content' => 'Cn Content',
			'en_important' => 'En Important',
			'cn_important' => 'Cn Important',
			'updated_timestamp' => 'Updated Timestamp',
			'userid' => 'Userid',
			'cd_uids' => 'CD Uid',
			'operate_uid' => 'Operate Uid',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('classid',$this->classid);
		$criteria->compare('yid',$this->yid);
		$criteria->compare('type',$this->type);
		$criteria->compare('weekid',$this->weekid);
		$criteria->compare('weeknumber',$this->weeknumber);
		$criteria->compare('stat',$this->stat);
		$criteria->compare('en_title',$this->en_title,true);
		$criteria->compare('cn_title',$this->cn_title,true);
		$criteria->compare('en_content',$this->en_content,true);
		$criteria->compare('cn_content',$this->cn_content,true);
		$criteria->compare('en_important',$this->en_important,true);
		$criteria->compare('cn_important',$this->cn_important,true);
		$criteria->compare('updated_timestamp',$this->updated_timestamp);
		$criteria->compare('userid',$this->userid);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}