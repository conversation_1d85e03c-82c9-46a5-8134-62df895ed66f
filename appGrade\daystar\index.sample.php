<?php

// change the following paths if necessary
$yii=dirname(__FILE__).'/../../../../framework/yii.php';
$config=dirname(__FILE__).'/../protected/config/main.child.php';

// remove the following lines when in production mode
defined('YII_DEBUG') or define('YII_DEBUG',true);
// specify how many levels of call stack should be shown in each log message
defined('YII_TRACE_LEVEL') or define('YII_TRACE_LEVEL',3);


//以下内容不需要修改
require_once($yii);

Yii::setPathOfAlias('common', dirname(__FILE__) . '/../../common');
Yii::setPathOfAlias('webroot', dirname(__FILE__));
Yii::createWebApplication($config)->run();
