<?php

/**
 * 采购流程工作流
 */
class PaymentHq implements WorkflowTemplate
{
    private $template;
    private $isJumpNode = 0;
    public $controller;
    public $workflow;

    public function __construct()
    {
        Yii::import('common.models.Uploads');
        Yii::import('common.models.workflow.payment.*');
        Yii::import('common.models.workflow.contract.ContractApply');
    }

    public function initi()
    {
        $data['jsFunction'] = 'WorkflowModalShow';
        $data['modalNameId'] = 'workflow-payment-modal';
        $payemntId = $this->workflow->getOperateObjId();
        $contractId = Yii::app()->request->getParam('contractId');
        if ($payemntId) {
            $init = false;
            $paymentModel = PaymentApply::model()->findByPk($payemntId);
            $paymentItemModel = PaymentItem::model()->findAllByAttributes(array('pid' => $payemntId));
        } else {
            $init = true;
            $paymentModel = new PaymentApply();
            $paymentItemModel = array();
        }
        if ($contractId) {
            $contractModel = ContractApply::model()->findByPk($contractId);
            $data['contractModel'] =$contractModel;
            $data['contractLink'] = $contractModel->getContractInfoLink();
            if (!$payemntId) {
                $paymentModel->vendor_id = $contractModel->vendor_id;
                $paymentModel->bank_account = $contractModel->bank_account;
                $paymentModel->bank_name = $contractModel->bank_name;
                $paymentModel->bank_user = $contractModel->bank_user;
            }
        }
        if (Yii::app()->request->isPostRequest) {
            $payinfo = Yii::app()->request->getPost('payinfo');
            $memo = Yii::app()->request->getPost('memo');
            $title = Yii::app()->request->getPost('title');
            $bank_user = Yii::app()->request->getPost('bank_user');
            $bank_account = Yii::app()->request->getPost('bank_account');
            $bank_name = Yii::app()->request->getPost('bank_name');
            $vendor_id = Yii::app()->request->getPost('vendor_id');
            $budget = Yii::app()->request->getPost('budget');
            $itemArr = Yii::app()->request->getPost('itemArray');
            if (!in_array($budget, array(0 ,1))) {
                $this->controller->addMessage('state', 'fail');
                $this->controller->addMessage('message', '保存失败, 原因：请选择预算。');
                $this->controller->showMessage();
            }
            if (!$payinfo) {
                $this->controller->addMessage('state', 'fail');
                $this->controller->addMessage('message', '保存失败, 原因：请选择付款方。');
                $this->controller->showMessage();
            }
            if (!$memo) {
                $this->controller->addMessage('state', 'fail');
                $this->controller->addMessage('message', '保存失败, 原因：申请备注不能为空。');
                $this->controller->showMessage();
            }
            if (!$title) {
                $this->controller->addMessage('state', 'fail');
                $this->controller->addMessage('message', '保存失败, 原因：申请标题不能为空。');
                $this->controller->showMessage();
            }
            if (!$bank_user) {
                $this->controller->addMessage('state', 'fail');
                $this->controller->addMessage('message', '保存失败, 原因：收款方不能为空。');
                $this->controller->showMessage();
            }
            if (!$bank_account) {
                $this->controller->addMessage('state', 'fail');
                $this->controller->addMessage('message', '保存失败, 原因：银行账号不能为空。');
                $this->controller->showMessage();
            }
            if (!$bank_name) {
                $this->controller->addMessage('state', 'fail');
                $this->controller->addMessage('message', '保存失败, 原因：开户银行不能为空。');
                $this->controller->showMessage();
            }
            if (empty($itemArr)) {
                $this->controller->addMessage('state', 'fail');
                $this->controller->addMessage('message', '保存失败, 原因：至少添加一项。');
                $this->controller->showMessage();
            }
            $totalPrice = 0;
            foreach ($itemArr as $item) {
                if (!$item['desc']) {
                    $this->controller->addMessage('state', 'fail');
                    $this->controller->addMessage('message', '保存失败, 原因：付款明细描述不能为空。');
                    $this->controller->showMessage();
                }
                if (!$item['category1']) {
                    $this->controller->addMessage('state', 'fail');
                    $this->controller->addMessage('message', '保存失败, 原因：至少选择一个分类。');
                    $this->controller->showMessage();
                }
                if (!floatval($item['price']) || $item['price'] <= 0) {
                    $this->controller->addMessage('state', 'fail');
                    $this->controller->addMessage('message', '保存失败, 原因：' . $item['desc'] . '付款明细价格错误。');
                    $this->controller->showMessage();
                }
                $totalPrice += bcmul($item['price'] , 100);
            }
            if (isset($data['contractModel'])) {
                $data['paymentData'] = $data['contractModel']->getPaymentList($contractId);
                $paidAmount = bcmul($data['paymentData']['total'], 100);
                $paidAmount = round($paidAmount + $totalPrice);
                // 框架合同不检查金额
                if ($data['contractModel']->category != 2) {
                    if ($data['contractModel']->price_total < $paidAmount) {
                        $this->controller->addMessage('state', 'fail');
                        $this->controller->addMessage('message', '保存失败, 原因：超过合同金额。');
                        $this->controller->showMessage();
                    }
                }
            }
            $transaction = Yii::app()->db->beginTransaction();
            try {
                if ($paymentModel->id) {
                    PaymentItem::model()->deleteAllByAttributes(array('pid' => $paymentModel->id));
                }
                // 查找供应商
                // 去除 $bank_account 除数字外的特殊符号
                $bank_account = preg_replace('/[^\d]/', '', $bank_account);
                $bank_name = trim($bank_name);
                $bank_user = trim($bank_user);
                $vendorModel = PaymentVendor::model()->findByAttributes(array('bank_account' => $bank_account));
                if (!$vendorModel) {
                    $vendorModel = new PaymentVendor();
                    $vendorModel->type = 1;
                    $vendorModel->state = 1;
                    $vendorModel->name = $bank_user;
                    $vendorModel->bank_name = $bank_name;
                    $vendorModel->bank_user = $bank_user;
                    $vendorModel->bank_account = $bank_account;
                    $vendorModel->created_by = Yii::app()->user->id;
                    $vendorModel->created_at = time();
                    $vendorModel->updated_by = Yii::app()->user->id;
                    $vendorModel->updated_at = time();
                    $vendorModel->save();
                }
                // 保存付款申请表
                $paymentModel->startyear = date('Y') > 8 ? date('Y') : date('Y') - 1;
                $paymentModel->school_id = $this->workflow->schoolId;
                $paymentModel->budget = $budget;
                $paymentModel->title = $title;
                $paymentModel->vendor_id = $vendorModel->id;
                $paymentModel->bank_user = $bank_user;
                $paymentModel->bank_account = $bank_account;
                $paymentModel->bank_name = $bank_name;
                $paymentModel->price_total = $totalPrice;
                $paymentModel->contract_id = $contractId;
                $paymentModel->state = PaymentApply::INPROGRESS;
                $paymentModel->created_by = $this->controller->staff->uid;
                $paymentModel->created_at = time();
                $paymentModel->updated_by = $this->controller->staff->uid;
                $paymentModel->updated_at = time();
                // 保存付款明细
                if (!$paymentModel->save()) {
                    $errors = current($paymentModel->getErrors());
                    throw new Exception('保存失败, 原因：付款申请表保存失败，' . current($errors));
                }
                //保存账单申请表
                foreach ($itemArr as $item) {
                    $newPaymentItem = new PaymentItem();
                    $newPaymentItem->pid = $paymentModel->id;
                    $newPaymentItem->state = PaymentApply::INPROGRESS;
                    $newPaymentItem->category1 = $item['category1'];
                    $newPaymentItem->category2 = $item['category2'];
                    $newPaymentItem->desc = $item['desc'];
                    $newPaymentItem->price = bcmul($item['price'] , 100);
                    $newPaymentItem->created_by = $this->controller->staff->uid;
                    $newPaymentItem->created_at = time();
                    $newPaymentItem->updated_by = $this->controller->staff->uid;
                    $newPaymentItem->updated_at = time();
                    if (!$newPaymentItem->save()) {
                        $errors = current($newPaymentItem->getErrors());
                        throw new Exception('保存失败, 原因：付款申请明细保存失败，' . current($errors));
                    }
                }
                $uid = $this->controller->staff->uid;
                $email = $this->controller->staff->email;
                $operationData = array('operation_type' => WorkflowConfig::WORKFLOW_TYPE_PAYMENT, 'operation_object_id' => $paymentModel->id, 'defination_id' => $this->workflow->definationObj->defination_id, 'branchid' => $this->workflow->schoolId, 'state' => WorkflowOperation::WORKFLOW_STATS_UNOP, 'current_node_index' => $this->workflow->getNextNodeId(), 'exte1' => '');
                //保存工作流业务流程
                if ($this->workflow->opertationObj->id) {
                    $operationData['id'] = $this->workflow->getOperateId();
                    $operationData['exte4'] = $this->workflow->opertationObj->exte4;
                }
                $operationRet = $this->workflow->saveFirstNodeForWorkflow($operationData, array($paymentModel->id), $memo, $transaction, array($uid => $email));
                if ($operationRet === false) {
                    throw new Exception('保存失败, 原因：工作流底层更新失败。');
                }
                // 更新相关附件的 link_id
                if ($files = $_POST['files']) {
                    foreach ($files as $file) {
                        $uploadModel = Uploads::model()->findByPk($file);
                        if (!$uploadModel) {
                            continue;
                        }
                        $uploadModel->link_id = $this->workflow->opertationObj->id;
                        $uploadModel->save();
                    }
                }
                $transaction->commit();
                // 发送邮件
                $message = 'success';
                if ($this->jumpMail() === false) {
                    $message = 'save success, 邮件发送失败';
                }
                $this->controller->addMessage('state', 'success');
                $this->controller->addMessage('message', $message);
                $this->controller->addMessage('refresh', true);
                $this->controller->showMessage();
            } catch (Exception $e) {
                $this->controller->addMessage('state', 'fail');
                $this->controller->addMessage('message', $e->getMessage());
                $this->controller->showMessage();
            }
        }
        $this->setTemplate('initi');
        $branchObj = Branch::model()->getBranchInfo($this->workflow->schoolId);
        $data['branchObj'] = $branchObj;
        $data['workflow'] = $this->workflow;
        $data['categoryList'] = $this->categoryList($this->workflow->definationObj->defination_memo);
        $data['paymentModel'] = $paymentModel;
        $data['paymentItemModel'] = $paymentItemModel;
        $data['checked'] = ($this->controller->staff->profile->branch == $this->workflow->schoolId) || $paymentItemModel->id;

        return $data;
    }

    public function show()
    {
        if (($this->workflow->isFristNode() === true) && $this->workflow->getOperateStatus() == WorkflowOperation::WORKFLOW_STATS_STEP){
            return $this->initi();
        }
        if (Yii::app()->request->isPostRequest) {
            $operationId = $this->workflow->opertationObj->id;
            $buttonCategory = Yii::app()->request->getPost('buttonCategory', 0);
            $memo = Yii::app()->request->getPost('memo', '');
            $budget = Yii::app()->request->getPost('budget', '');
            $flowId = Yii::app()->request->getPost('flowId', 0);
            if (!in_array($budget, array(0 ,1))) {
                $this->controller->addMessage('state', 'fail');
                $this->controller->addMessage('message', '保存失败, 原因：请选择预算。');
                $this->controller->showMessage();
            }
            if (!$buttonCategory) {
                $this->controller->addMessage('state', 'fail');
                $this->controller->addMessage('message', '保存失败, 原因：请选择操作。');
                $this->controller->showMessage();
            }
            if (!$memo) {
                $this->controller->addMessage('state', 'fail');
                $this->controller->addMessage('message', '保存失败, 原因：申请备注不能为空。');
                $this->controller->showMessage();
            }
            if ($this->workflow->nodeObj->field2 == 'finance' && $this->workflow->definationObj->display == 1) {
                if (!$flowId) {
                    $this->controller->addMessage('state', 'fail');
                    $this->controller->addMessage('message', '保存失败，原因：请选择适用工作流！');
                    $this->controller->showMessage();
                }
            }

            switch ($buttonCategory) {
                case WorkflowOperation::WORKFLOW_STATS_RESET:
                    $process = 'reset';
                    break;
                case WorkflowOperation::WORKFLOW_STATS_OPED:
                    $process = 'pass';
                    break;
                case WorkflowOperation::WORKFLOW_STATS_OPDENY:
                    $process = 'reject';
                    break;
                default:
                    $process = '';
                    break;
            }
            if ($process == '') {
                $this->controller->addMessage('state', 'fail');
                $this->controller->addMessage('message', '保存失败，原因：请选择！');
                $this->controller->showMessage();
            }

            $requestUrl = "workflow/process/$process";
            $requestData = array(
                'operationId' => $operationId,
                'budget' => $budget,
                'flowId' => $flowId,
                'desc' => $memo,
            );
            $res = CommonUtils::requestDsOnline($requestUrl, $requestData);
            if ($res['code'] == 0) {
                $this->controller->addMessage('state', 'success');
                $this->controller->addMessage('message', $res['msg']);
                $this->controller->addMessage('refresh', true);
            } else {
                $this->controller->addMessage('state', 'fail');
                $this->controller->addMessage('message', '工作流保存异常：' . $res['msg']);
                $this->controller->addMessage('data', $res['data']);
            }
            $this->controller->showMessage();
        }
        
        $this->setTemplate('show');
        $data['jsFunction'] = 'WorkflowModalShow';
        $data['modalNameId'] = 'workflow-payment-modal';
        //查找相关的采购商品、标配
        $oDetail = WorkflowOperationDetail::model()->find('operation_id=:operation_id', array(':operation_id' => $this->workflow->opertationObj->id));
        $paymentApply = PaymentApply::model()->findByPk($oDetail->operation_object_id);
        $isFinance = $this->workflow->nodeObj->field2 == 'finance';
        if (empty($paymentApply)) {
            throw new Exception('工作流程业务参数错误！', 500);
        }
        $paymentItem = PaymentItem::model()->findAllByAttributes(array('pid' => $paymentApply->id));

        $allCategoy = PaymentApply::allCategoryList();

        foreach ($paymentItem as $key => $itemModel) {
            if (isset($allCategoy[$itemModel->category1])) {
                $category1 = $allCategoy[$itemModel->category1];
                $paymentItem[$key]->category1 = $category1['title'];
                if ($itemModel->category2) {
                    foreach ($category1['items'] as $v2) {
                        if ($v2['code'] == $itemModel->category2) {
                            $paymentItem[$key]->category2 = $v2['title'];
                            break;
                        }
                    }
                }
            }
        }
        $contractId = $paymentApply->contract_id;
        if ($contractId) {
            $contractModel = ContractApply::model()->findByPk($contractId);
            $data['contractModel'] =$contractModel;
            $data['contractLink'] = $contractModel->getContractInfoLink();
        }
        $branchObj = Branch::model()->getBranchInfo($this->workflow->schoolId);
        $data['branchObj'] = $branchObj;
        $data['isFinance'] = $isFinance;
        $data['paymentApply'] = $paymentApply;
        $data['paymentItem'] = $paymentItem;
        $data['workflow'] = $this->workflow;
        if ($this->workflow->definationObj->display == 1 && $isFinance) {
            $flowList = $this->getFlowList();
            $flowIdList = array();
            foreach($flowList as $flow) {
                $flowIdList[] = $flow['defination_id'];
            }
            $lastFlowId = NULL;
            if ($this->workflow->definationObj->display == 1 && $flowIdList) {
                // 获取用户最后一次选择的付款流程id
                $crit = new CDbCriteria();
                $crit->compare('defination_id', $flowIdList);
                $crit->compare('start_user', $this->workflow->opertationObj->start_user);
                $crit->compare('state', WorkflowOperation::WORKFLOW_STATS_OPED);
                $crit->order = 'end_time DESC';
                $lastFlowOperationModel = WorkflowOperation::model()->find($crit);
                if ($lastFlowOperationModel) {
                    $lastFlowId = $lastFlowOperationModel->defination_id;
                }
            }
            $data['flowList'] = $flowList;
            $data['lastFlowId'] = $lastFlowId;
        }
        return $data;
    }

    public function changeFlow($operationModel, $newDefinationId, $branchId) {
        $operationId = $operationModel->id;
        $oldDefinationId = $operationModel->defination_id;

        $oldDefination = WorkflowDefination::model()->findByPk($oldDefinationId);
        if (!$oldDefination) {
            throw new Exception('WorkflowDefination 未找到');
        }
        $newDefination = WorkflowDefination::model()->findByPk($newDefinationId);
        if (!$newDefination) {
            throw new Exception('new WorkflowDefination 未找到');
        }
        $oldNodeIds = explode(',', $oldDefination->node_order);
        $oldFirstNode =$oldNodeIds[0];
        $oldFinanceNode =$oldNodeIds[1];
        $newNodeOrder = explode(',', $newDefination->node_order);
        $newFirstNode = $newNodeOrder[0];
        $newFinanceNode = $newNodeOrder[1];

        // 修改 workflw_role
        $roleModels = WorkflowRole::model()->findAllByAttributes(array('operation_id' => $operationId, 'defination_id' => $oldDefinationId));
        foreach ($roleModels as $roleModel) {
            $roleModel->defination_id = $newDefinationId;
            if ($roleModel->user_node_id == $oldFirstNode) {
                $roleModel->user_node_id = $newFirstNode;
            }
            if ($roleModel->user_node_id == $oldFinanceNode) {
                $roleModel->user_node_id = $newFinanceNode;
            }
            $roleModel->save();
        } 
        // 修改 workflow_process
        $processModels = WorkflowProcess::model()->findAllByAttributes(array('operation_id' => $operationId, 'defination_id' => $oldDefinationId));
        foreach ($processModels as $processModel) {
            $processModel->defination_id = $newDefinationId;
            if ($processModel->current_node_index == $oldFirstNode) {
                $processModel->current_node_index = $newFirstNode;
            }
            if ($processModel->current_node_index == $oldFinanceNode) {
                $processModel->current_node_index = $newFinanceNode;
            }
            $processModel->save();
        }
        // 修改 workflow_opertaion
        $operationModel->defination_id = $newDefinationId;
        $operationModel->current_node_index = $newFinanceNode;
        $operationModel->save();
        return new WorkflowApi($newDefinationId, $newFinanceNode, $operationId, $branchId);
    }

    public function jumpMail()
    {

        $this->workflow->wechatPush();
        
        $branchList = Branch::model()->getBranchList();
        $branchTitle = $branchList[$this->workflow->getOperateValue('branchid')];
        $applyName = $this->workflow->opertationObj->userInfo->getName();
        $workflowTitle = CommonUtils::autoLang($this->workflow->definationObj->defination_name_cn, $this->workflow->definationObj->defination_name_en);
        $nodesTitle = CommonUtils::autoLang($this->workflow->nodesList[$this->workflow->getNextNodeId()]->node_name_cn, $this->workflow->nodesList[$this->workflow->getNextNodeId()]->node_name_en);
        // 判断是否为跳节点
        if ($this->isJumpNode) {
            $nodesTitle = CommonUtils::autoLang(end($this->workflow->nodesList)->node_name_cn, end($this->workflow->nodesList)->node_name_en);
        }
        if ($this->workflow->getOperateStatus() == WorkflowOperation::WORKFLOW_STATS_OPDENY) {
            $subject = sprintf('%s 校园 %s 提出 %s申请被拒绝并作废', $branchTitle, $applyName, $workflowTitle);
        } elseif ($this->workflow->getOperateStatus() == WorkflowOperation::WORKFLOW_STATS_RESET) {
            $subject = sprintf('%s 校园 %s 提出 %s申请被退回修改', $branchTitle, $applyName, $workflowTitle);
        }  else {
            if ($this->workflow->isEndNode()) {
                $subject = sprintf('%s 校园 %s 提出 %s申请已完成', $branchTitle, $applyName, $workflowTitle);
            } else {
                $subject = sprintf('%s 校园 %s 提出 %s申请需要您处理', $branchTitle, $applyName, $workflowTitle);
            }
        }

        $link = $this->controller->createUrl('/workflow/index', array('branchId' => $this->workflow->schoolId));
        $body = $this->controller->renderPartial('/mail/templater', array('branchTitle' => $branchTitle, 'childName' => $applyName, 'workflowTitle' => $workflowTitle, 'nodes' => $nodesTitle, 'link' => $link, 'workflow' => $this->workflow), true);

        if ($this->isJumpNode) {
            $jumpNode = end($this->workflow->nodeIds);
            return $this->workflow->sendEmail($subject, $body, false, $jumpNode);
        }

        if($this->workflow->sendEmail($subject,$body)){
            return true;
        }

        return false;
    }

    public function categoryList($flag)
    {
        $categoryIds = explode(',', $flag);
        $category = array();
        if (!$categoryIds) {
            return $category;
        }
        $allCategoy = PaymentApply::allCategoryList();
        foreach ($categoryIds as $id) {
            $item = $allCategoy[$id];
            $item['code'] = $id;
            $category[] = $item;
        }
        return $category;
    }

    public function allCategoryList($flag)
    {
        $categoryIds = PaymentApply::categoryList($flag);
        $category = array();
        if (!$categoryIds) {
            return $category;
        }
        $allCategoy = PaymentApply::allCategoryList();
        foreach ($categoryIds as $id) {
            $item = $allCategoy[$id];
            $item['code'] = $id;
            $category[] = $item;
        }
        return $category;
    }

    //实现接口方法
    public function setTemplate($name)
    {
        $this->template = $name;
    }

    //实现接口方法
    public function getTemplate()
    {
        return $this->template;
    }

    //删除工作流
    public function delete()
    {
        $request = Yii::app()->request;
        if ($this->workflow->getOperateStatus() == WorkflowOperation::WORKFLOW_STATS_STEP){
            $transaction = Yii::app()->db->beginTransaction();
            try{
                $payemntId = $this->workflow->getOperateObjId();
                if ($payemntId) {
                    PaymentApply::model()->updateByPk($payemntId, array('state' => PaymentApply::CANCEL));
                }
                if (!$this->workflow->deleteOperationNode($transaction)){
                    $this->controller->addMessage('state', 'fail');
                    $this->controller->addMessage('message', "工作流底层删除失败!");
                    $this->controller->showMessage();
                }
                $transaction->commit();
                $this->controller->addMessage('state', 'success');
                $this->controller->addMessage('message', '工作流删除成功');
                $this->controller->addMessage('data',$this->workflow->getOperateId());
                $this->controller->addMessage('callback', 'deleteWorkflowOperator');
                $this->controller->showMessage();
            } catch (Exception $ex) {
                $transaction->rollBack();
                $this->controller->addMessage('state', 'fail');
                $this->controller->addMessage('message', $ex->getMessage());
                $this->controller->showMessage();
            }
        }
    }

    /**
     * 保存上传的附件
     * @return [type] [description]
     */
    public function saveUploadFile()
    {
        if ($file = CUploadedFile::getInstanceByName('file')) {
            if ($file->size > 10 * 1024 * 1024) {
                $this->controller->addMessage('state', 'fail');
                $this->controller->addMessage('message', $file->name . '上传失败，最大10m。');
                $this->controller->showMessage();
            }
            $ext = strtolower($file->getExtensionName());
            $needType = array('jpg', 'png', 'pdf', 'gif', 'jpeg', 'doc', 'docx', 'xls', 'xlsx', 'tif', 'tiff');
            if (!in_array($ext, $needType)) {
                $this->controller->addMessage('state', 'fail');
                $this->controller->addMessage('message', $file->name . '上传失败，资料类型出错。');
                $this->controller->showMessage();
            }
            $datePath = date('Y') . '/' . date('m') . '/';
            $filePath = Yii::app()->params['xoopsVarPath'] . '/uploads/' . $datePath;
            if (!is_dir($filePath)) {
                mkdir($filePath, 0777, true);
            }
            $baseUrl = Yii::app()->params['OABaseUrl'] . "/modules/myspace/task/getthumb.php?img=";
            $fileName = uniqid() . '.' . $ext;
            if (!$file->saveAs($filePath . $fileName)) {
                $this->controller->addMessage('state', 'fail');
                $this->controller->addMessage('message',  $file->name .'资料保存失败。');
                $this->controller->showMessage();
            }
            //保存表ivy_uploads
            $uploadModel = new Uploads();
            $uploadModel->link_id = $this->workflow->opertationObj->id;
            $uploadModel->mod_name = 'workflow';
            $uploadModel->func_name = 'payment';
            $uploadModel->file_name = $datePath . $fileName;

            $uploadModel->notes = pathinfo($file->name, PATHINFO_FILENAME);
            $uploadModel->update_time = time();
            $uploadModel->update_user = Yii::app()->user->id;
            if (!$uploadModel->save()) {
                unlink($filePath . $fileName);
                $error = current($uploadModel->getErrors());
                $this->controller->addMessage('message', $file->name . '保存失败: ' . $error[0]);
                $this->controller->showMessage();
            }
            $this->controller->addMessage('data', array('fileId' => $uploadModel->id, 'fileName' => $uploadModel->notes, 'url' => $baseUrl . $uploadModel->file_name));
            $this->controller->addMessage('state', 'success');
            $this->controller->addMessage('message', '资料上传成功。');
            $this->controller->showMessage();
        }
        Yii::app()->end();
    }

    /**
     * 删除附件
     * @return [type] [description]
     */
    public function deleteUploadFile()
    {
        if (Yii::app()->request->isPostRequest) {
            $id = Yii::app()->request->getPost('id', 0);
            if ($uploadModel = Uploads::model()->findByPk($id)) {
                //删除资料
                $fileName = $uploadModel->file_name;
                $filePath = Yii::app()->params['xoopsVarPath'] . '/uploads/';
                if ($uploadModel->delete()) {
                    unlink($filePath . $fileName);
                    $this->controller->addMessage('state', 'success');
                    $this->controller->addMessage('message', '资料删除成功。');
                    $this->controller->showMessage();
                }
            }
        }
        $this->controller->addMessage('state', 'fail');
        $this->controller->addMessage('message', '资料删除失败。');
        $this->controller->showMessage();
    }

    /**
     * 修改附件显示名
     * @param string $value [description]
     */
    public function changeFileName()
    {
        if (Yii::app()->request->isPostRequest) {
            $id = Yii::app()->request->getPost('id', 0);
            $notes = Yii::app()->request->getPost('notes', 0);
            if ($notes && $uploadModel = Uploads::model()->findByPk($id)) {
                $uploadModel->notes = $notes;
                if ($uploadModel->save()) {
                    $this->controller->addMessage('state', 'success');
                    $this->controller->addMessage('message', '资料修改成功。');
                    $this->controller->showMessage();
                }
            }
        }
        $this->controller->addMessage('state', 'fail');
        $this->controller->addMessage('message', '资料修改失败。');
        $this->controller->showMessage();
    }

    public function getFlowList() {
        return WorkflowDefination::model()->findAllByAttributes(array('defination_handle' => 'PaymentHq', 'display' => 0, 'status' => 1));
    }
}
