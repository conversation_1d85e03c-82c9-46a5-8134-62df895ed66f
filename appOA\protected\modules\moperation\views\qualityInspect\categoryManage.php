<style>
    [v-cloak] {
		display: none;
	}
    .top{
        padding:16px 24px;
        background: #FFFFFF;
        box-shadow:0px 8px 8px 0px rgba(0, 0, 0, 0.12);
        display:flex;
        align-items:center
    }
    .colorBlue{
        color:#4D88D2
    }
    .line{
        background: #ccc;
        height: 100%;
        width: 1px;
        position: absolute;
        left: 6px;
        top: 25px;
    }
    .mt2{
        margin-top:2px
    }
    .tableCode tr th,.tableCode tr td{
        border-top:none !important;
        padding: 6px 12px !important;
    }
    .subBox{
        background: #F7F7F8;
        padding:24px
    }
    .hiddens{
        overflow:hidden
    }
    .scroll-box::-webkit-scrollbar-thumb {
        /*滚动条里面小方块*/
        border-radius   : 10px;
        background-color: #ccc;
        background-image: none
    }
    .scroll-box::-webkit-scrollbar-track {
        /*滚动条里面轨道*/
        box-shadow   : inset 0 0 5px rgba(0, 0, 0, 0);
        background   : #fff;
        border-radius: 10px;
        border:none
    }
    .sortable-ghost{
        background: #F0F5FB;
        border: 1px dashed #4D88D2;
    }
    .sortable-drag{
        background: #F7F7F8;
        box-shadow: 0px 0px 12px 0px rgba(0,0,0,0.16);
        border-radius: 4px 0px 0px 4px;
    }
    .hoverBlue:hover{
        color:#4D88D2
    }
</style>
<div class="container-fluid"  id='template' v-cloak>
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('//moperation/default/index')) ?></li>
        <li><?php echo CHtml::link(Yii::t('site','模板管理'), array('template'))?></li>
        <li class="active">管理分类</li>
    </ol>
    <div class="row top" v-if='templateInfo.info'>
       <div class='flex1'><span class='font16 color3 fontBold'>{{templateInfo.info.title}}</span> </div>
       <button type="button" class="btn btn-default mr16"  @click='closePage("modal")'>取消</button>
       <button type="button" class="btn btn-primary" :disabled='btnDis' @click='saveCategory'>保存</button>
    </div>
    <div class='row mt5 p24 scroll-box' v-if='templateInfo.info' :style="'height:'+(height-190)+'px;overflow-y: auto;'">
        <div class='flex mb20  relative hiddens' >
            <div class='line'></div>
            <div class=''>
                <!-- <span class='el-icon-folder-opened colorBlue font16 mt2'></span> -->
                <img style='width:18px' src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/folder.png' ?>" alt="">
            </div>
            <div class='ml8 flex1'>
                <div class='colorBlue font14 fontBold'>{{templateInfo.info.title}}</div>
                <div class='flex mt15 relative hiddens' v-for='(list,index) in categoryList'>
                    <div class='line'></div>
                    <div class=''>
                        <span class='el-icon-coin colorBlue font16 mt2'></span>
                    </div>
                    <div class='ml8 flex1'>
                        <div class='colorBlue font14 fontBold'>分类</div>
                        <div class='mt16'>
                            <table class='table tableCode color6 '>
                                <tbody>
                                    <tr>
                                        <th width='80'>序号</th>
                                        <th width='500'>分类名称（中文）</th>
                                        <th width='500'>分类名称（英文）</th>
                                        <th width='80'>操作</th>
                                    </tr>
                                    <tr>
                                    <td><el-input v-model='list.weight'  size='small'  placeholder="请输入内容"></el-input></td>
                                    <td><el-input v-model="list.title_cn"  size='small'  placeholder="请输入内容"></el-input></td>
                                    <td><el-input v-model="list.title_en"  size='small'  placeholder="请输入内容"></el-input></td>
                                    <td><div class='colorBlue pt8 cur-p' @click='delCategory(index)'>删除分类</div></td>
                                    </tr>
                                </tbody>
                            </table>
                            
                        </div>
                        <div class='subBox'>
                            <div class='flex relative hiddens '>
                                <div class='line'></div>
                                <div class=''>
                                    <span class='el-icon-coin colorBlue font16 mt2'></span>
                                </div>
                                <div class='ml8 flex1'>
                                    <div>
                                        <span class='cur-p' @click='showSub(list)'>
                                            <span  class='colorBlue font14 fontBold'>子分类</span>     
                                            <span class='el-icon-arrow-down'  v-if='!list.show'></span>
                                            <span class='el-icon-arrow-up'  v-if='list.show'></span>
                                        </span>
                                        <el-tag 
                                            class='ml10'
                                            type="success"
                                            size="mini"
                                            effect="dark">
                                            {{list.sub.length}}项
                                        </el-tag>
                                    </div>
                                    <table class='table tableCode color6 mt15 ' ref='tableList' v-if='list.show'>
                                        <tbody class='tableList'>
                                            <tr>
                                                <!-- <th width='80'>序号</th> -->
                                                <th width='300'>子类别名称（中文）</th>
                                                <th width='300'>子类别名称（英文）</th>
                                                <th width='250'>检查形式</th>
                                                <th width='200'>检查频率</th>
                                                <th width='80'>操作</th>
                                            </tr>
                                            <tr v-for='(item,i) in list.sub' class='timeList' :data-id="index">
                                                <!-- <td><el-input :value="i+1"  size='small'  placeholder="请输入内容"></el-input></td> -->
                                                <td><el-input v-model="item.title_cn"  size='small'  placeholder="请输入内容"></el-input></td>
                                                <td><el-input v-model="item.title_en"  size='small'  placeholder="请输入内容"></el-input></td>
                                                <td>
                                                    <el-select v-model="item.check_form"     collapse-tags multiple placeholder="请选择" size='small' style='width:100%'>
                                                        <el-option
                                                            v-for="(item,key,index) in templateInfo.check_form"
                                                            :key="key"
                                                            :label="item"
                                                            :value="key">
                                                        </el-option>
                                                    </el-select>
                                                </td>
                                                <td>
                                                    <el-select v-model="item.frequency" placeholder="请选择" size='small' style='width:100%'>
                                                        <el-option
                                                        v-for="(item,key,index) in templateInfo.check_frequency"
                                                        :key="key"
                                                        :label="item"
                                                        :value="key">
                                                        </el-option>
                                                    </el-select>
                                                </td>
                                                <td>
                                                    <span class='el-icon-delete hoverBlue font14 pt8 cur-p' @click='delSub(index,i)'></span>
                                                    <span class='el-icon-rank hoverBlue font14 ml16 pt8 cur-p handle'></span>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                    <div class='mt24'><span class='font14 colorBlue cur-p' @click='addSub(list,index)'><span class='el-icon-plus'></span><span> 新增子类别</span></span></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class='mt24'><button type="button" class="btn btn-primary" @click='addCategory()'><span class='el-icon-coin'></span> 新增分类</button></div>
            </div>
        </div>
    </div>
     <!-- 删除 -->
     <div class="modal fade" id="tipModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel" data-backdrop="static" data-keyboard="false"> 
        <div class="modal-dialog modal-sm" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t('leave', '提示') ?></h4>
                </div>
                <div class="modal-body p24" >
                    <div>您做的修改将被忽略，确定关闭么？</div>
                </div>
                <div class="modal-footer" >
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Cancel"); ?></button>
                    <button type="button" class="btn btn-primary" @click='closePage()'><?php echo Yii::t("global", "OK"); ?></button>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    var height=document.documentElement.clientHeight;
    var id = '<?php echo $_GET['id']?>';

    var  container = new Vue({
        el: "#template",
        data: {
        height:height,
        input:'',
        value:'',
        templateInfo:{},
        categoryList:{},
        id:id,
        sortable:[],
        btnDis:false
        },
        created: function() {
           this.getTepInfo()
        },
        methods: {
            getTepInfo(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("templateInfo") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        id:this.id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                           console.log(data)
                           that.templateInfo=data.data
                           data.data.info.categories.forEach(element => {
                                element.show=true
                           });
                           that.categoryList=data.data.info.categories
                            that.moveSort()
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            delCategory(index){
                this.categoryList.splice(index,1)
            },
            addCategory(){
                this.categoryList.push({
                    "id": '',
                    "items_id":this.id,
                    "title_cn": "",
                    'show':true,
                    'weight':this.categoryList.length+1,
                    "title_en": "",
                    "category_id": '',
                    "sub": [
                        {
                            "id": '',
                            "items_id":this.id,
                            "title_cn": "",
                            "title_en": "",
                            "check_form": [],
                            "frequency":'',
                            "weight": 1,
                            "attach": [],
                            "title": ""
                        },
                    ],
                })
            },
            showSub(list){
                list.show=!list.show
                this.$forceUpdate();
            },
            delSub(index,i){
                this.categoryList[index].sub.splice(i,1)
            },
            addSub(list,index){
                this.categoryList[index].sub.push({
                    "id": '',
                    "items_id":this.id,
                    "title_cn": "",
                    "title_en": "",
                    "check_form": [],
                    "frequency":'',
                    "weight":  this.categoryList[index].sub.length+1,
                    "attach": [],
                })                
                this.moveSort()
            },
            saveCategory(){
                let that=this
                for(let i=0;i<this.categoryList.length;i++){
                    if(this.categoryList[i].sub.length==0){
                        resultTip({
                            error: 'warning',
                            msg:'请添加子类别'
                        });
                        return
                    }
                    for(let j=0;j<this.categoryList[i].sub.length;j++){
                        console.log(this.categoryList[i].sub[j])
                        this.categoryList[i].sub[j].weight=j+1
                    }
                }
                that.btnDis=true
                $.ajax({
                    url: '<?php echo $this->createUrl("saveCategorySubMany") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        items_id:this.id,
                        list:this.categoryList
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                           that.getTepInfo()
                           resultTip({
                                msg: data.message
                            });
                            that.btnDis=false
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            that.btnDis=false
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                        that.btnDis=false
                    },
                })
            },
            moveSort(){
                console.log(this.sortable)
                if(this.sortable.length){
                    this.sortable.forEach(element => {
                        element.destroy();
                    });
                    this.sortable=[]
                }
                let that=this      
                that.$nextTick(function () {
                    var ops2 = {
                        animation: 150,
                        handle: '.handle', // 拖动的手柄
                        forceFallback: true,
                        ghostClass: "sortable-ghost",  // 放置占位符的类名
                        chosenClass: "sortable-chosen",  // 所选项目的类名
                        dragClass: "sortable-drag",  // 拖动项的类名
                        onStart: function (evt) {
                        },
                        onEnd: function (evt) { //拖拽完毕之后发生该事件
                            const draggedElement = evt.item; // 被拖拽的DOM元素
                                const index = draggedElement.getAttribute('data-id');
                                if(evt.newIndex!=evt.oldIndex){
                                    var list=JSON.parse( JSON.stringify (that.categoryList[index].sub))
                                    console.log(list)
                                    list.splice(evt.newIndex-1, 0, list.splice(evt.oldIndex-1, 1)[0])
                                    var newArray = list.slice(0)
                                    console.log(newArray)
                                    that.categoryList[index].sub = []
                                    that.$nextTick(function () {
                                        that.categoryList[index].sub = newArray
                                    })
                                }
                        }
                    };
                    const parents = document.querySelectorAll('.tableList')
                    parents.forEach(parent => {
                        this.sortable.push(new Sortable(parent,ops2 ))
                    })
                })
            },
            closePage(type){
                if(type){
                    $("#tipModal").modal('show')
                    return
                }

                if (window.close) {
                 window.close();
                } else {
                    window.open('', '_self', '');
                    window.close();
                }
            }
        },
    })
</script>
