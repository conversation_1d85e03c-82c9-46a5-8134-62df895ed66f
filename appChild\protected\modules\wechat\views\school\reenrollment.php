<script src="<?php echo Yii::app()->theme->baseUrl; ?>/js/vue.js"></script>
<style>
    * {
        margin: 0;
        padding: 0;
    }

    body {
        font-family: -apple-system-font, Helvetica Neue, Helvetica, sans-serif;
        background-color: #fbf9fe;
    }
 
    a {
        text-decoration: none;
        -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
    }

    .title,
    .weui_cells_radio {
        margin-top: 0
    }

    .weui_panel {
        margin-top: 0
    }

    .weui_panel:first-child {
        margin-top: 10px;
        margin-bottom: 10px
    }

    .weui_cell {
        clear: both;
    }

    .weui_cell_primary {
        flex: none
    }

    .weui_cell:before {
        border-top:0
    }

    .weui_cell {
        margin: 10px 15px;
        border-radius: 3px;
        background: #F5F5F5;
        margin: 10px 15px;
        border-radius: 3px;
        color: #414141;
        font-size: 14px;
        overflow: hidden;
        display: block
    }

    .weui_icon_checked {
        position: absolute;
        right: -9px;
        top: -9px;
        width: 30px;
        background: #E7E7E7;
        height: 30px;
        border-radius: 50%;
    }

    .weui_icon_checked:before {
        background: #E7E7E7;
        height: 30px;
        border-radius: 50%;
        display: block;
        content: '\EA08';
        color: #fff;
        font-size: 16px;
        width: 30px;
        line-height: 35px;
        text-align: left;
        margin-left: 4px;
    }

    .bd.spacing {
        margin-top: 10px;
        background: #fff;
        margin: 0 15px;
        border-bottom-right-radius: 10px;
        border-bottom-left-radius: 10px;
        /* padding: 20px 40px 30px 0; */
        padding:20px;
        margin-top:-4px
    }

    .weui_cells_radio .weui_check:checked+.weui_icon_checked:before {
        background: #09BB07;
        color: #fff;
        margin: 0;
        padding-left: 4px;
    }

    .weui_media_box .weui_media_title {
        text-overflow: normal;
        white-space: normal;
        word-wrap: normal;
        word-wrap: normal;
        word-break: normal;
    }

    .flex {
        display: flex
    }

    .weui_btn+.weui_btn {
        margin-top: 0
    }

    .weui_media_box:before {
        border-top: none;
        left:0.5px !important
    }

    .prevQuestion {
        width: 130px;
        text-align: center;
        line-height: 41px;
    }

    .prevcolor {
        color: #999
    }

    .nextQuestion,
    .submit {
        border-radius: 25px;
        flex: 1
    }

    .weui_panel:after {
        border-bottom: 0
    }

    .weui_panel {
        margin: 0 15px;
        border-radius: 10px;
        border-bottom:none;
        padding-bottom:4px;
        
    }

    .weui_cellss {
        margin-top: 20px;
        /* max-height: 215px; */
        overflow-y: auto;
        margin-bottom: 20px;
        line-height: 1.41176471; 
        font-size: 17px;
        position: relative;
    }

    .bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image: url('<?php echo $bgimg;?>');
        background-repeat:no-repeat;
        background-size: cover;
        filter: blur(5px);
        overflow-y:auto;
    }

    .bg-mask {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        overflow-y:auto;
    }

    .weui_media_box div {
        color: #fff;
        font-size: 14px
    }

    .weui_media_box .titlesize {
        color: #fff;
        font-size: 20px;
        font-weight: 500;
    }
    .weui_media_box  .defaultSize{
        color: #fff;
    }
    .questionnaire2-tit {
        padding: 15px;
    }

    .questionnaire2-tit span {
        padding: 5px;
        border: 1px solid #fff;
        border-radius: 5px;
        color: #fff
    }

    .title {
        padding-bottom: 10px;
        padding-top:20px
    }

    .weui_cells_title {
        text-align: center;
        font-size: 20px
    }
    #questionnaire {
        width: 100%;
        min-height:100%
    }

    .textarea {
        margin: 0 15px;
        background: #fff;
        border-radius: 0px;
        padding: 0 0;
        padding-right: 25px;
    }

    .weui_textarea {
        border: 1px solid #ccc;
        border-radius: 3px;
        padding:5px 10px;
        outline: none;
        -webkit-appearance: none;
        line-height:20px;
        font-family: -apple-system-font, Helvetica Neue, Helvetica, sans-serif;
    }
    .images{
        width:auto;
        
        margin:0 15px
    }
    .images img{
        width:100%;
    }
    [v-cloak] {
		display: none;
	}
    dl{
        display:flex;
        float:right;
        margin-top:-13px
    }
    dl dd{
        color:#fff;
        line-height:50px;
        padding-right:15px
    }
    .circle{
        border-radius:50%;
        border:2px solid #fff;
        width:50px
    }
    ..weui_mask{
        z-index:999
    }
    .weui_dialog{
        z-index:1000;
        position: absolute;
        z-index: 13;
        width:80%;
        top: 50%;
        left: 50%;
        -webkit-transform: translate(-50%, -50%);
        /* transform: translate(-50%, -50%); */
        background-color: #FAFAFC;
        text-align: center;
        border-radius: 3px;
        /* overflow: hidden;*/
    }
    .bgFixed{
        position: absolute;
        width: 100%;
        height: 100%;
        background: #000;
        opacity: 0.5;
        left:0;
        top:0
    }
    .bgFixedText{
        position: absolute;
        left:0;
        /* bottom:300px; */
        width:100%
    }
    /* 签名 */
    #signature-div {
        width: 100%;
        height: 100%;
        display: none;
        position: fixed;
        left: 0;
        top: 0;
        z-index: 999;
        background-color: white;
    }

    #signature-div p {
        position: absolute;
        bottom:15px;
        left: 50%;
        margin-left: -78px
    }

    .signature-pad {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: white;
    }

    .signCan {
        position: relative;
        -webkit-box-flex: 1;
        -ms-flex: 1;
        flex: 1;
        border: 1px solid #f4f4f4;
    }

    .signBody {
        position: relative;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -ms-flex-direction: column;
        flex-direction: column;
        font-size: 10px;
        /* width: 100%; */
        height: 100%;
        max-height: 300px;
        background-color: #fff;
        border-radius: 4px;
        padding: 16px;
    }

    #signature-data img {
        width: 100px
    }

    .signText {
        padding: 16px;
    }
    .textCen,#signature-data{
        text-align:center
    }
    .questionbox{
        padding-bottom:20px;
        position: relative;
        overflow:auto
        /* overflow-x:hidden */
    }
    .text{
        color:#fff;
        font-size:20px;
        padding:16px
    }
    html,body{
        overflow:auto

    }
    .padding-50{
        margin-top:50px
    }
</style>

<div id='questionnaire' style='min-height:100%;position: relative;overflow:auto' v-cloak>
    <div class="bg"></div>
    <div class="bg-mask"></div>
    <div class="questionbox">
        <div class="title">
            <div class="questionnaire2-tit">
                <span  v-if='options.length>0'>{{question.show_type}}</span>
                <dl>
                    <dd><?php echo $childInfo->getChildName(); ?></dd>
                    <dt>
                        <img class="circle" src="<?php echo CommonUtils::childPhotoUrl($childInfo->photo); ?>">    
                    </dt>
                </dl>
            </div>
            <div style='clear:both'></div>
            <div class="text">
                <p :class='question.type=="content"?"defaultSize":"titlesize"'>{{question.title}}
                <a href="javascript:;" @click="tourl" v-if='question.order == "last" && question.to_survey!=""' class="weui_btn weui_btn_primary nextQuestion padding-50"><?php echo Yii::t("survey", "Start"); ?></a>
               </p>
            </div>
        </div>
        <div class='images' v-if="question.content_ext">
            <img :src="question.content_ext" @click='previewMoreImage(question.content_ext)'>
        </div>
        <div class="weui_panel weui_panel_access" v-if="question.order != 'last'">
            <div class="weui_cellss weui_cells_radio listHeight" >
                <label class="weui_cell weui_check_label" v-for="(option, index) in options" v-if='isuploads'>
                    <div class="weui_cell_bd weui_cell_primary">
                        <p>{{option.title}}</p>
                    </div>
                    <div class="weui_cell_ft">
                        <input v-model="oid" :value="option.oid" :type="(question.is_multiple == 1) ? 'checkbox' : 'radio'" class="weui_check">
                        <span class="weui_icon_checked"></span>
                    </div>
                </label>
            </div>
            <div v-if="question.is_memo==1 || question.is_memo==2" class="weui_cell textarea">
                <div class="weui_cell_bd weui_cell_primary">
                    <textarea class="weui_textarea" v-model="memo" placeholder="<?php echo Yii::t("reg", "Please take a note if any"); ?>" rows="3"></textarea>
                </div>
            </div>
            <p id="signature-data" v-if="question.is_signature==1">
                <img :src="signImg" alt="">
            </p>
            <div class='weui_article textCen' v-if="question.is_signature==1">
                <button class="weui_btn weui_btn_mini weui_btn_primary" type="button" id="signature-save" @click='signature()'> <?php echo Yii::t('reg', "Agree & sign") ?></button>
            </div>
            <div class='bd spacing flex' v-if="question.order != 'last'">
                <a href="javascript:;" class='prevQuestion prevcolor' v-if="question.order == 'middle'" @click="oPre"><?php echo Yii::t("reg", "Back"); ?></a>
                <a href="javascript:;" @click="oNext" class="weui_btn weui_btn_primary nextQuestion"><?php echo Yii::t("reg", "Next Step"); ?></a>
            </div>
        </div>
    </div>
    <div style='clear:both'></div> 
    <div class='bgFixed'v-if='dialog2'></div>
        <div class='bgFixedText'  :style="'bottom:' + bottom"  v-if='dialog2' >
            <div class="weui_dialog">
                <div class="weui_dialog_hd"><strong class="weui_dialog_title"><?php echo Yii::t("survey", "Notice"); ?></strong></div>
                <div class="weui_dialog_bd">{{toastText}}</div>
                <div class="weui_dialog_ft">
                    <a href="javascript:;" class="weui_btn_dialog primary" @click='dialog2=false'><?php echo Yii::t("bind", "Confirm"); ?></a>
                </div>
            </div>
        </div>
</div>
<div class="wrapper" id="signature-div">
    <div class="signBody">
        <div class="signCan">
            <canvas id="signature-pad" class="signature-pad"></canvas>
        </div>
    </div>
    <div class="signText"><?php echo Yii::t('reg', "Rotate your phone horizontal for better view to sign") ?></div>
    <canvas id = "watermark" width = "100%"  height = "150px" style="display:none;"></canvas>
    <p>
        <button type="button" class="weui_btn weui_btn_mini weui_btn_primary exit"><?php echo Yii::t('bind', "Confirm") ?></button>
        <button type="button" class="weui_btn weui_btn_mini weui_btn_warn clear"><?php echo Yii::t('reg', "Rewrite") ?></button>
        <button type="button" class="weui_btn weui_btn_mini weui_btn_default dropOut"><?php echo Yii::t('bind', "Cancel") ?></button>
    </p>
</div>
<script>
    var childid = "<?php echo $childInfo->childid; ?>";
    deleteCookie('childid');
    setCookie('childid', childid, 30);
    /**
     * 设置cookie
     * @param {string} name  键名
     * @param {string} value 键值
     * @param {integer} days cookie周期
     */
    function setCookie(name,value,days) {
        if (days) {
            var date = new Date();
            date.setTime(date.getTime()+(days*24*60*60*1000));
            var expires = "; expires="+date.toGMTString();
        }else{
            var expires = "";
        }
        document.cookie = name+"="+value+expires+"; path=/";
    }
          
    // 删除cookie
    function deleteCookie(name) {
        setCookie(name,"",-1);
    }
    console.log(<?php echo json_encode($data['question']); ?>)
    var vum = new Vue({
        el: '#questionnaire',
        data: {
            dialog2: false,
            question: <?php echo json_encode($data['question']); ?>,
            options: <?php echo json_encode($data['options']); ?>,
            answer: <?php echo json_encode($data['answer']); ?>,
            returnid: '<?php echo $returnid; ?>',
            oid: <?php echo json_encode($data['answer']['q_val']); ?>,
            memo: '<?php echo $data['answer']['q_memo']; ?>',
            showNext: true,
            showtip: false,
            toastText: '<?php echo Yii::t("reg", 'Please "Click" to continue'); ?>',
            bottom:$(window).height()/2+'px',
            signImg:'',
            isuploads:true
        },
        mounted: function() {
            document.body.addEventListener('focusout', () => { 
                //软键盘收起的事件处理 
                let ua = navigator.userAgent.toLowerCase();
                if (ua.indexOf('iphone') > 0 || ua.indexOf('ipad') > 0) { 
                    //键盘收齐页面空白问题 
                    document.body.scrollTop = document.body.scrollHeight; 
                }
            })
            let height=window.screen.availHeight-450+'px'
            // $('.listHeight').css('max-height',height)
        },
        creted: function() {
        },
        methods: {
            review() {
                linkToPage('questionNaire');
            },
            previewMoreImage(img) {
                let urlarr = [];
                urlarr.push(img)
                wx.previewImage({
                current: img,
                urls: urlarr
                })
            },
            oPre: function() {
                // 发起保存请求
                $.ajax({
                    url: "<?php echo $this->createUrl('returnPre') ?>",
                    type: 'post',
                    dataType: 'json',
                    data: {
                        returnid: this.returnid,
                        pid: this.question.preid,
                        preid: this.question.preid
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            vum.question = data.data.question
                            vum.options = data.data.options
                            vum.memo = data.data.answer.q_memo
                            vum.oid = data.data.answer.q_val
                            vum.signImg= data.data.answer.q_signature
                            vum.isuploads = false
                            vum.$nextTick(() => (vum.isuploads = true))
                        }
                    }
                })
            },
            oNext: function() {
                // 题目是否必选验证
                if (this.question.is_required == 1 && !this.oid) {
                    this.toastText = '<?php echo Yii::t("reg", 'Please "Click" to continue'); ?>'
                    this.dialog2 = true
                    return false;
                }
                if (this.question.is_signature == 1 && this.signImg=='') {
                    this.toastText = '<?php echo Yii::t("reg", 'Signature required'); ?>'
                    this.dialog2 = true
                    return false;
                }
                if (this.question.is_memo == 2 && this.memo == '') {
                    this.toastText = '<?php echo Yii::t("reg", 'Memo required'); ?>'
                    this.dialog2 = true
                    return false;
                }
                // 发起保存请求
                $.ajax({
                    url: "<?php echo $this->createUrl('returnSave') ?>",
                    type: 'post',
                    dataType: 'json',
                    data: {
                        returnid: this.returnid,
                        qid: this.question.qid,
                        oid: this.oid,
                        memo: this.memo,
                        signature:this.signImg,
                        preid: this.question.preid
                    },
                    success: function(data) {
                        console.log(data)
                        
                        if (data.state == 'success') {
                            
                            // 判断是否要跳转
                            console.log(data.data.redirect)
                            if (data.data.redirect) {
                                location.href = data.data.redirect;
                            } else {
                                vum.question = data.data.question
                                vum.options = data.data.options
                                vum.memo = data.data.answer.q_memo
                                vum.oid = data.data.answer.q_val
                                vum.signImg= data.data.answer.q_signature
                                vum.isuploads = false
                                vum.$nextTick(() => (vum.isuploads = true))
                            }
                        } else {
                            vum.toastText = data.message;
                            vum.dialog2 = true
                        }
                    },
                })
            },
            signature(){
                $('#signature-div').show();
            signatureInit();
            },
            tourl(){
                window.location.href=this.question.to_survey
            }
        }
    });

    $('.exit').click(function() {
        $('html').css({
            'overflow': 'initial',
            'position': 'initial'
        });
        $('html,body').scrollTop(scrollTops);
        var canvas = document.getElementById('signature-pad');
        var data = canvas.toDataURL("image/png");
        vum.signImg=data
        // $("#signature-data").attr('src', data);
        $("#signature-data img").attr('src',data)
        $("#filedata").val(data);
        $("#signature-data").show();
        $('#signature-div').hide();
        var width = canvas.width / 8
        var height = canvas.height / 8
        $('#signature-data img').height(height)
        $('#signature-data img').width(width)
    })
    // 重写
    $('.clear').click(function() {
        signatureInit();
    })
    var canvas = document.getElementById('signature-pad');
    $('.dropOut').click(function(){
        $('html').css({
            'overflow': 'initial',
            'position': 'initial'
        });
        $('html,body').scrollTop(scrollTops);
        $('#signature-div').hide();
        clea = canvas.getContext("2d");
        clea.clearRect(0,0,500,500);
    })
    // 点击全名书写
    // $('#signature-save').click(function() {
    //     //canvas全屏
    //     alert(2)
    //     $('#signature-div').show();
    //     signatureInit();
    // });
    // 初始化画板
    var scrollTops;
    function signatureInit() {
        scrollTops = $(window).scrollTop();
        $('html').css({
            'overflow': 'hidden',
            'position': 'fixed'
        })
        ratio = Math.max(window.devicePixelRatio || 1, 1);
        canvas.width = canvas.offsetWidth * ratio;
        canvas.height = canvas.offsetHeight * ratio;
        canvas.getContext("2d").scale(ratio, ratio);
        var signaturePad = new SignaturePad(canvas);
        watermark(canvas);
    }

    function hengshuping() {
        if(window.orientation == 180 || window.orientation == 0) {
            if($('.wrapper').css("display") == "block"){
                clea = canvas.getContext("2d");
                clea.clearRect(0,0,500,500);
                setTimeout("signatureInit()",200);
            }
        }
        if(window.orientation == 90 || window.orientation == -90) {
            if($('.wrapper').css("display") == "block"){
                clea = canvas.getContext("2d");
                clea.clearRect(0,0,500,500);
                setTimeout("signatureInit()",200);
            }
        }
    }
    window.addEventListener("onorientationchange" in window ? "orientationchange" : "resize", hengshuping, false);
    // 绘制水印
    function watermark(canvas) {
        var date = new Date();
        var year = date.getFullYear();
        var month = date.getMonth() + 1;
        var strDate = date.getDate();
        if(month >= 1 && month <= 9) {
            month = "0" + month;
        }
        if(strDate >= 0 && strDate <= 9) {
            strDate = "0" + strDate;
        }
        var currentdate = year + month + strDate
        var cw = $('#watermark')[0];   
        cw.width = $(document).width();
        var ctx = cw.getContext("2d");   //返回一个用于在画布上绘图的环境
        
       
        ctx.fillStyle = "rgba(100,100,100,0.1)";
        if(window.orientation == 90 || window.orientation == -90) {
            ctx.font="30px 黑体";  
            ctx.rotate(-10*Math.PI/180);
            $('.signText').hide()
            ctx.fillText(currentdate + "",100, 130);
        } else if(window.orientation == 0 || window.orientation == 180) {
            ctx.font="20px 黑体";  
            ctx.rotate(-18*Math.PI/180);
             $('.signText').show()
           ctx.fillText(currentdate + "", 0, 130);
        }
        
        ctx.rotate('20*Math.PI/180');  //坐标系还原
        var crw =canvas;  
        ctxr = canvas.getContext("2d");
        ctxr.width = $(window).height();
        ctxr.clearRect(0,0,500,500);  //清除整个画布 
        var pat = ctxr.createPattern(cw, "repeat");    //在指定的方向上重复指定的元素  
        ctxr.fillStyle = pat;  
        ctxr.fillRect(0, 0, crw.width, crw.height);
    }
</script>