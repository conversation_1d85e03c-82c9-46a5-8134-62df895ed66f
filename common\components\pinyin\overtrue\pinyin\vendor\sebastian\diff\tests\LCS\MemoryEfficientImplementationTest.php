<?php
/*
 * This file is part of sebastian/diff.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace <PERSON>\Diff\LCS;

/**
 * @covers <PERSON><PERSON><PERSON><PERSON><PERSON>\Diff\LCS\MemoryEfficientImplementation
 */
class MemoryEfficientImplementationTest extends LongestCommonSubsequenceTest
{
    protected function createImplementation()
    {
        return new MemoryEfficientImplementation;
    }
}
