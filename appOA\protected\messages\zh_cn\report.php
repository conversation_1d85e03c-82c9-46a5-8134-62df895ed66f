<?php
return array (
    'Create Template' => '创建模版',
    'Categories Edit' => '分类管理',
    'Options Edit' => '选项管理',
    'Items Edit' => '内容条目',
    'Add Root Category' => '添加根类',
    'Add Option' => '添加选项',
    'Options' => '选项',

    'Category (English)' => '大分类（英文）',
    'Category (Chinese)' => '大分类（中文）',
    'Sub category (English)' => '小分类（英文）',
    'Sub category (Chinese)' => '小分类（中文）',

    'Option Title (English)' => '选项标题（英文）',
    'Option Title (Chinese)' => '选项标题（中文）',
    'Option Description (English)' => '选项说明（英文）',
    'Option Description (Chinese)' => '选项说明（中文）',
    'Active' => '正常',
    'Disabled' => '已禁用',

    'Please clear sub categories' => '请先清空子类',
    'Please selecte options for this category' => '请选择本分类下适用的选项',

    'Definition Groups' => '评定标准组管理',
    'Add Group' => '添加组',
    'Group is not empty, cannot be deleted' => '不能删除有内容的分类',
    'Please add report categories first.' => '请先添加分类',
    'Progress Definitions' => '评定标准管理',

    'Teacher\'s Comment / Next Step' => '老师评语／计划',
    'Grade-Level Proficiency' => '评定标准管理',
    'Student Achievement Summary' => '学生成绩',
    'Term 1' => '第一学期',
    'Term 2' => '第二学期',
    'Days Absent' => '缺勤天数',
    'Days Tardy' => '迟到天数',
    'Other' => '其他',
    'Additional Support and Services' => '额外辅导',
    'Name' => '姓名',
    'DOB' => '出生日期',
    'Campus' => '校园',
    'Class' => '班级',
    'Class Teachers' => '班级老师',
    'Select at least 1 grade as required' => '所选年级不能为空',
    'Only 1 valid report template for each grade level at a time' => '一个年级同时只能有一个有效的报告模板',
    'Learning Support' => '学习支持',
    'Add' => '增加',
    'Evidence Score' => '评估成绩',
    'Teacher List' => '老师列表',
    'Set up the teacher list in the report' => '设置报告中显示的老师',
    'Search Teacher' => '搜索老师',
    'Save (Grade-Level Only)'=>'仅保存评定标准',
    'Save (Comment Only)'=>'仅保存评语',
    'Auto-populate data from GradeBook' => '一键评分（用 GradeBook 数据填充）',
    'Confirm to fill in the report levels based on GradeBook evidences (existing report levels) will be overwritten)?' => '确认使用 GradeBook证据填充评估报告的水平（已填写的评估报告水平会被覆盖)',
);