<?php
class MimsAPI{
	const BOUND_USER_ALREADY = 10001; //已经绑定过
	const BOUND_USER_REPLACED = 10002;//之前绑定过账户，现在绑定到其他账户
	const BOUND_USER_DONE = 10003; //绑定成功
	const BOUND_USER_MAXEXCEEDED = 10004; //绑定达最大数，需手动输入帐号密码绑定
	const BOUND_USER_ERROR = 10099; //绑定达最大数，需手动输入帐号密码绑定
	
	const MAX_NUMBER_BOUND_PER_ACCOUNT = 10;
	
	static function unBindUser($openID){
		$exist = WechatUser::model()->findByPk($openID);
		if( is_null($exist) ){
			$result = "您没有绑定过帐号";
		}else{
			if($exist->valid){
				$exist->valid = 0;
				if(!$exist->save()){
					$result = "系统错误，请重试";
				}else{
					$result = "成功取消绑定！";
				}
			}else{
				$result = "您已经取消过绑定了。";
			}
		}
		return $result;
	}
	
	static function getBalance($childIds){
		Yii::import('common.components.policy.PolicyApi');
		Yii::import('common.models.invoice.*');
		$result = array();
		if(!empty($childIds)){
			foreach($childIds as $childid){
				$result['credit'][$childid] = PolicyApi::getCreditAmount($childid);
				$deposit = PolicyApi::getDepositAmount($childid, 0, true);
				$deposit = is_null($deposit) ? array() : $deposit;
				$result['deposit'][$childid] = $deposit;
			}
		}
		return $result;
	}
	
	static function getInvoices($childIds, $type, $limit=10){
		Yii::import('common.models.invoice.*');
		$crit = new CDbCriteria;
		$crit->compare('childid', $childIds);
		$crit->compare('`inout`', 'in');
		$crit->compare('payment_type', '<>preschool_subsidy');
		$crit->limit = $limit;
		switch($type){
			case 'SPT_UNPAID':
				$crit->order = 'timestamp ASC';
				$crit->compare('status', array(Invoice::STATS_UNPAID, Invoice::STATS_PARTIALLY_PAID));
			break;
			case 'SPT_RECENT_PAID':
				$crit->order = 'last_paid_timestamp DESC';
				$crit->compare('status',Invoice::STATS_PAID);
			break;
		}
		$invoices = Invoice::model()->findAll($crit);
		return $invoices;
	}
	
	static function lunchCancel($childId, $targetDate, $userid){
		Yii::import('common.components.policy.*');
		Yii::import('common.models.invoice.*');
		Yii::import('common.models.calendar.*');
		return PolicyApi::cancelChildLunchByDate($childId, $targetDate, $userid);
	}
	
	static function lunchRecover($childId, $targetDate, $userid){
		Yii::import('common.components.policy.*');
		Yii::import('common.models.invoice.*');
		Yii::import('common.models.calendar.*');
		return PolicyApi::recoverChildLunchByDate($childId, $targetDate, $userid);
	}
	
	static function getRecentJournals($childIds, $limit=6){
		if(!is_array($childIds)){
			$childIds = array(intval($childIds));
		}
		Yii::import('common.models.portfolio.*');
		$crit = new CDbCriteria;
		$crit->compare('childid', $childIds);
		$crit->compare('t.stat', 20);
		$crit->order = 't.updated_timestamp DESC';
		$crit->limit = ($limit)?$limit:6;
		$notes = NotesChild::model()->with('cInfo')->findAll($crit);
		return $notes;
	}
	
	/**
	 * Summary
	 * @param	Object	$note		Description
	 * @param	Object	$time		Description
	 * @param	Object	$secrityKey	Description
	 * @param	Object	$o			openid
	 * @return	Object				Description
	 */
	static function createJournalUrl($note, $time, $o, $secrityKey){
		$data = $note->id;
		$url = Yii::app()->createUrl('/weixin/user/journal', array(
			'data'=>$data,
			't'=>$time,
			'o'=>$o,
			'code'=>md5($secrityKey . $time . $data . $o)
		));
		return $url;
	}
	
	static function createLunchMenuUrl($menuLink, $time, $o, $secrityKey){
		$data = $menuLink->id;
		$url = Yii::app()->createUrl('/weixin/user/lunchMenu', array(
			'data'=>$data,
			't'=>$time,
			'o'=>$o,
			'code'=>md5($secrityKey . $time . $data . $o)
		));
		return $url;
	}
	
	static function getRecentMenus($branchId, $limit){
		Yii::import('common.models.lunchmenu.*');
		Yii::import('common.models.calendar.Calendar');
		$theTime = time() + 7 * 24 * 60 * 60;
		$crit = new CDbCriteria;
		$crit->compare('schoolid', $branchId);
		$crit->compare('menu_id', '>0');
		$crit->compare('t.monday_timestamp', '<' . strval($theTime));
		$crit->limit = $limit;
		$crit->order = 'monday_timestamp DESC';
		$menuLinks = CateringMenuWeeklink::model()->with(array('branch','calendar'))->findAll($crit);
		return $menuLinks;
	}
	
	static function getPhotos($idsByYear){
		$data = array();
		foreach($idsByYear as $startYear => $pids){
			$tableName = 'ivy_child_media_' . $startYear;
			$data[$startYear] = Yii::app()->db->createCommand()
				->select('*')
				->from($tableName)
				->where(array('in', 'id', $pids))
				->queryAll();
		}
		
		$result = array();
		foreach($data as $y=>$d){
			foreach($d as $i){
				$result[$y][$i['id']] = $i;
			}
		}
		
		return $result;
	}

    static function getMediaUrl($thumb=false, $mediaData)
    {
        $mediaUrl = CommonUtils::getMediaUrl($thumb, $mediaData['server'], $mediaData['type'], $mediaData['filename'],$mediaData['schoolid'], $mediaData['classid'], $mediaData['weeknum']);
//        $conf = Mims::LoadConfig('CfgPhotoServer');
//
//        $mediaUrl = '';
//        $mediaUrl=$conf["servers"][$mediaData['server']]["web"] . "/s_" . $mediaData['schoolid'] . "/c_" . $mediaData['classid'] . "/w_" . $mediaData['weeknum'] ."/";
//        if ($thumb){
//            $mediaData['filename'] = $mediaData['type'] == 'video' ? str_replace('.flv', '.jpg', $mediaData['filename']) : $mediaData['filename'];
//            $mediaUrl .= "thumbs/".$mediaData['filename'];
//        }
//        else {
//            $mediaUrl .= $mediaData['filename'];
//        }
        return $mediaUrl;
    }
	
	static function getLastUploadByOpenId($openId){
		$ret = null;
		if(!empty($openId)){
			$crit = new CDbCriteria();
			$crit->compare('openid', $openId);
			$crit->order = 'created DESC';
			$crit->limit = 1;
			$ret = WechatUploads::model()->find($crit);
		}
		return $ret;
	}
	
	static function saveImageUploads($openId='', $imgUrl=''){
		if(!empty($openId) && !empty($imgUrl)){
			$upload = new WechatUploads;
			$upload->openid = $openId;
			$upload->imgurl = $imgUrl;
			$upload->status = 0;
			$upload->created = time();
			if($upload->save()){
				return true;
			}
		}
		return false;
	}
	
	static function copyImage($src,$targetDir,$filename){
		$destFile = (substr($targetDir, -1) != '/') ? $targetDir . '/' . $filename : $targetDir . $filename;
		$thumbDir = (substr($targetDir, -1) != '/') ? $targetDir . '/thumbs/'  : $targetDir . 'thumbs/';
		
        //$fsrc = fopen($src,'r');
        //$fdest = fopen($destFile,'w+'); 
        //$len = stream_copy_to_stream($fsrc,$fdest);
        //fclose($fsrc); 
        //fclose($fdest);
		copy($src, $destFile);

		$extensions = array(
			'image/png' => '.png',
			'image/jpeg' => '.jpg',
			'image/gif' => '.gif',
		);
		
		$mimetype = mime_content_type($destFile);
		if(isset($extensions[$mimetype])){
			$newDestFile = $destFile . $extensions[$mimetype];
			$newThumbFile = $thumbDir . $filename . $extensions[$mimetype];
			rename($destFile, $newDestFile);
			$cfgs = Mims::LoadConfig('CfgProfile');
			$params = $cfgs["childPhoto"];
			
			Yii::import('application.extensions.image.Image');
			$image = new Image($newDestFile);
			
			if (isset($params['forceSquare']) && $params['forceSquare']) {
				$min = min($image->__get('width'), $image->__get('height'));
				$image->crop($min, $min);
			}
			
			$image->resize($params['sizeNormal'], $params['sizeNormal'])->save();
			$image->resize($params['sizeThumb'], $params['sizeThumb'])->save($newThumbFile);
			
			return $filename . $extensions[$mimetype];
		}else{
			@unlink($destFile);
		}
		
		return false;
	}
	
	static function genClassMatesLink($data, $time, $o, $secrityKey, $isNew=false){
		$action = ($isNew)? 'classmates2' : 'classmates';
		$url = Yii::app()->createUrl('/weixin/user/'. $action, array(
			'data'=>$data,
			't'=>$time,
			'o'=>$o,
			'code'=>md5($secrityKey . $time . $data . $o)
		));
		return $url;	
	}
	
	static function customSendText($data, $securityKey){
		$ch = curl_init();
		$time = time();
		$command = 'doCustomSendText';
		$postData = array(
			"postTime" => $time,
			"command" => $command,
			"data" => base64_encode(serialize($data)),
			"postKey" => md5(sprintf("%s&%s&%s", $time, $command, $securityKey))
		);
		curl_setopt($ch, CURLOPT_URL,  Yii::app()->createUrl("/weixin/remoteCall/doCustomSendText")); 
		curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
		//curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json'));
		curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
		$return = curl_exec($ch);
		print_r($return);
		curl_close($ch);
	}
	
	/**
	 * 扫描私人定制二维码的处理；关注过微信帐号
	 * @param	Object	$openId		Description
	 * @param	Object	$sceneId	Description
	 * @return	Object				Description
	 */
	static function rebind($openId, $sceneId){		
		$exist = WechatUser::model()->findByPk($openId);
		$scene = WechatQRScene::model()->findByPk($sceneId);
		$return = array('code'=>'');

        if( is_null($exist) && $scene ) {
            $exist = new WechatUser;
            $exist->openid = $openId;
            $exist->userid = $scene->userid;
            $exist->valid = 1;
            $exist->updated = time();
            if (! $exist->save() ){
                $return['code'] = MimsAPI::BOUND_USER_ERROR;
                $return['message'] = print_r($exist->getErrors(), true);
            }
        }

		if($exist && $scene){
		
			$boundTotal = WechatUser::model()->countByAttributes(array('valid'=>1, 'userid'=>$scene->userid));
			if($boundTotal >= MimsAPI::MAX_NUMBER_BOUND_PER_ACCOUNT){
				$return['code'] = MimsAPI::BOUND_USER_MAXEXCEEDED;
				return $return;
			}
		
			$scene->scanned += 1;
			$scene->save();
			if($exist->userid && $exist->userid == $scene->userid && $exist->valid){
				$return['code'] = MimsAPI::BOUND_USER_ALREADY;
				$return['childData'] = MimsAPI::getChildInfo($exist->userid);
				return $return;
			}elseif($exist->userid && $exist->valid){
				$exist->userid = $scene->userid;
				$exist->updated = time();
				if( $exist->save() ){
					$return['childData'] = MimsAPI::getChildInfo($exist->userid);
					$return['code'] = MimsAPI::BOUND_USER_REPLACED;
					return $return;
				}else{
					$return['code'] = MimsAPI::BOUND_USER_ERROR;
					$return['message'] = print_r($exist->getErrors(), true);
					return $return;
				}
			}else{
				$exist->userid = $scene->userid;
				$exist->valid = 1;
				$exist->updated = time();
				if( $exist->save() ){
					$return['code'] = MimsAPI::BOUND_USER_DONE;
					$return['childData'] = MimsAPI::getChildInfo($exist->userid);
					return $return;
				}else{
					$return['code'] = MimsAPI::BOUND_USER_ERROR;
					$return['message'] = print_r($exist->getErrors(), true);
					return $return;
				}
			}
		}
	}
	
	/**
	 * 扫描私人定制二维码的处理；未关注过微信帐号
	 * @param	Object	$openId		Description
	 * @param	Object	$sceneId	Description
	 * @return	Object				Description
	 */
	static function processQRScene($openId, $sceneId){
		$pattern = 'qrscene_';
		$return = array('code'=>'');
		
		if(strpos($sceneId,$pattern)!== false){
			$sceneId = intval(str_replace($pattern, '', $sceneId));
			if($sceneId){
				$scene = WechatQRScene::model()->findByPk($sceneId);
				if($scene){
					$boundTotal = WechatUser::model()->countByAttributes(array('valid'=>1, 'userid'=>$scene->userid));
					if($boundTotal >= MimsAPI::MAX_NUMBER_BOUND_PER_ACCOUNT){
						$return['code'] = MimsAPI::BOUND_USER_MAXEXCEEDED;
						return $return;
					}
					
					$wechatUser = WechatUser::model()->findByPk($openId);
					if(is_null($wechatUser)){
						$wechatUser = new WechatUser;
						$wechatUser->openid = $openId;
					}
					$wechatUser->userid = $scene->userid;
					$wechatUser->valid = 1;
					$wechatUser->updated = time();
					if(	$wechatUser->save() ){
					
						$return['code'] = MimsAPI::BOUND_USER_DONE;
						$return['childData'] = MimsAPI::getChildInfo($wechatUser->userid);
						
						return $return;
					}else{
						$return['code'] = MimsAPI::BOUND_USER_ERROR;
						$return['message'] = print_r($wechatUser->getErrors(), true);
						return $return;
					}
				}
			}
		}
		return false;
	}
	
	static function getChildInfo($userId){
		$parent = IvyParent::model()->findByPk($userId);
		$myChildObjs = null;
		$activeChildIds = null;
		$branchList = null;
		$hasActive = false;
		
		$result = array('hasActive'=>$hasActive);
		
		if (!empty($parent) && count(@unserialize($parent->childs))) {
			$cids = @unserialize($parent->childs);
			$myChildObjs = ChildProfileBasic::model()->findAllByPk($cids, array("index" => "childid","order"=>"childid DESC"));
			
			$branchList = Branch::model()->getBranchList(null, true);
			foreach($myChildObjs as $child){
				$result['data'][] = array(
					'childName' => $child->getChildName(),
					'active' => ($child->status < 100) ? true : false,
					'campus' => empty($child->schoolid)? '': $branchList[$child->schoolid]['abb']
				);
				if($child->status < 100 && !$hasActive) $result['hasActive']=true;
			}
		}
		return $result;
	}

    static function clearHtmlContent($content){
        $content = str_ireplace('font-family', '', $content);
        $content = str_ireplace('font-size', '', $content);
        return $content;
    }
}