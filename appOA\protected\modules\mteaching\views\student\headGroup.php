<style>
    .allCheck {
        position: absolute;
        right: 10px;
        top: 4px;
    }
    .addChild {
        font-size: 17px;
        color: #409EFF;
        width: 16px;
        height: 16px;
        display: inline-block;
        border: 1px solid #409EFF;
        text-align: center;
        line-height: 15px;
        border-radius: 50%;
    }
    .childHover{
        padding:8px;
        border: 1px solid #fff;
    }
    .childHover:hover{
        background: rgba(77,136,210,0.1);
        border-radius: 4px;
        border: 1px solid #4D88D2;
        cursor: pointer;
    }
    .border {
        border: 1px solid #E8EAED;
        border-radius: 4px;
    }
    .childList {
        max-height: 250px;
        min-height: 40px;
        padding: 14px;
        overflow-y: auto;
        border: 1px solid #E8EAED;
        border-radius: 4px;
    }
    .lineHeight {
        line-height: 32px;
    }
    .max500{
        max-height:500px;
        padding-bottom:24px
    }
    .list{
        list-style: none;
        padding: 0;
    }
    .groupList{
        padding:10px;
        align-items:center;
        font-size:14px
    }
    .groupList a{
        color:#333;
        display:block
    }
    .groupList:hover,.currentGroup{
        background: rgba(77,136,210,0.1);
        border-radius: 4px;
        cursor: pointer;
        color: #4D88D2;
    }
    .groupList:hover a,.currentGroup a{
        color: #4D88D2;
    }
</style>
<div class="" id='container' v-cloak >
    <div class='row mb20' v-if='department.length>0'>
        <div class="col-md-12">
            <ul class="nav nav-tabs" id="tablist">
                <li role="presentation" v-for='(list,index) in department' :class="groupType==list?'active':''" @click='tabgroupType(list)'><a >{{groupName[list]}}</a></li>
            </ul>
        </div>
    </div>
    <div class='row'>
        <div class='col-md-2'>
        <?php
            $mainMenu = array(
                array('label'=>Yii::t('referral','Support Groups'), 'url'=>array("//mteaching/student/index","category"=>"adminGroups","category2"=>"supportList")),
                array('label'=>Yii::t('referral','Head of Grade'), 'url'=>array("//mteaching/student/index","category"=>"adminGroups","category2"=>"headGroup")),
                array('label'=>Yii::t('referral','Principal Office'), 'url'=>array("//mteaching/student/index","category"=>"adminGroups","category2"=>"office")),
                array('label'=>Yii::t('referral','Head of Department'), 'url'=>array("//mteaching/student/index","category"=>"adminGroups","category2"=>"subject")),
            );
            $this->widget('zii.widgets.CMenu',array(
                'id' => 'user-profile-item',
                'items'=> $mainMenu,
                'htmlOptions'=>array('class'=>'list'),
                'activeCssClass'=>'currentGroup',
                'itemCssClass'=>'groupList'
            ));
        ?>
        </div>
        <div class='col-md-10'>
            <div  v-if='gradeList.enable_head_grade && gradeList.enable_head_grade==1'>    
                <div class="panel panel-default"   v-for='(item,key,index) in gradeList.list'>
                    <div class="panel-heading">
                        <div class='font16 color3 flex align-items'>
                            <div class='flex1'>{{gradeList.grade_name[key]}}</div>
                            <div><div class='font14 text-primary cur-p' @click='addTeacher(key)'><span class='el-icon-circle-plus'></span> <?php echo Yii::t("global", "Add"); ?></div></div>
                        </div>
                    </div>
                    <div class="panel-body " >
                        <div v-if='item.length>0'  class='overflow-y scroll-box'>
                            <div class='col-xs-4 '  v-for='(list,idx) in item'>
                                <div class="media listMedia" >
                                    <div class="media-left pull-left media-middle">
                                        <a href="javascript:void(0)">
                                            <img :src="gradeList.staff_info[list].photoUrl" data-holder-rendered="true" class="avatar">
                                        </a>
                                    </div>
                                    <div class="media-right pull-right pt12 text-right">
                                        <span class='el-icon-circle-close font16 closeIcon' @click='delTeacher(key,list,idx)'></span>
                                    </div>
                                    <div class="media-body pt4 media-middle">
                                        <div class="lineHeight20  text-primary">
                                            <span class='font14 color3 nowrap'>{{gradeList.staff_info[list].name}}</span>
                                        </div>
                                        <div class="font12 color6 nowrap">{{gradeList.staff_info[list].hrPosition}}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div v-else class='color9 font14 text-center p16'><?php echo Yii::t("ptc", "No Data"); ?></div>
                    </div>
                </div>
            </div>
            <div v-else-if='gradeList.enable_head_grade==0'>
                <div class="alert alert-warning" role="alert"><span class='glyphicon glyphicon-exclamation-sign mr5'></span><?php echo Yii::t("referral", "Head of Grade group is not enabled in this division"); ?></div>
            </div>
        </div>
    </div>
   
    <div class="modal fade" id="addTeacherModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel"  aria-labelledby="wantSayModalLabel"  data-backdrop="static">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("global", "Add"); ?></h4>
                </div>
                <div class="modal-body">
                  
                        <div class='color6 font14 '><?php echo Yii::t("global", "Search"); ?></div>
                        <div  class='flex mt16 mb16'>
                            <el-select
                                    v-model="teacherUid"
                                    filterable
                                    remote
                                    @visible-change="loseFocus"
                                    clearable
                                    class='inline-input flex1 formControl'
                                    reserve-keyword
                                    placeholder="<?php echo Yii::t("principal", "Teacher Name"); ?>"
                                    :remote-method="remoteMethod"
                                    prefix-icon="el-icon-search"
                                    :loading="loading">
                                <el-option
                                        v-for="item in options"
                                        :key="item.uid"
                                        :label="item.name"
                                        class='optionSearch mb8'
                                        :value="item.uid">
                                    <div class="media">
                                        <div class="media-left pull-left media-middle">
                                            <a href="javascript:void(0)">
                                                <img :src="item.photoUrl" data-holder-rendered="true" class="media-object img-circle avatar">
                                            </a>
                                        </div>
                                        <div class="media-body mt5 media-middle">
                                            <h4 class="media-heading font14 color3 text_overflow">{{item.name}}</h4>
                                            <div class="text-muted text_overflow font12">{{ item.hrPosition }}</div>
                                        </div>
                                    </div>
                                </el-option>
                            </el-select>
                            <button type="button" class="btn btn-primary ml16"  :disabled='teacherUid==""?true:false' @click='confirmTeacher'><?php echo Yii::t("global", "Add"); ?></button>
                        </div>
                        <div class="row row-no-gutters" v-if='gradeType!=""'>
                            <div class='col-xs-6 pb16'   v-for='(list,index) in  gradeList.list[gradeType]'>
                                <div class="media listMedia" >
                                    <div class="media-left pull-left media-middle">
                                        <a href="javascript:void(0)">
                                            <img :src="gradeList.staff_info[list].photoUrl" data-holder-rendered="true" class="avatar">
                                        </a>
                                    </div>
                                    <div class="media-right pull-right pt12 text-right">
                                        <span class='el-icon-circle-close font16 closeIcon' @click='delTeacher(gradeType,list,index)'></span>
                                    </div>
                                    <div class="media-body pt4 media-middle">
                                        <div class="lineHeight20  text-primary">
                                            <span class='font14 color3 nowrap'>{{gradeList.staff_info[list].name}}</span>
                                        </div>
                                        <div class="font12 color6 nowrap">{{gradeList.staff_info[list].hrPosition}}</div>
                                    </div>
                                </div>
                            </div>
                        </div>    
                </div>
            </div>
        </div>
    </div>
    <!-- 删除确认框 -->
    <div class="modal fade" id="delModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
        <div class="modal-dialog modal-sm" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel">
                       <span><?php echo Yii::t("global", "Delete"); ?></span>
                    </h4>
                </div>
                <div class="modal-body" >
                    <div ><?php echo Yii::t("directMessage", "Proceed to remove?"); ?></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                    <button type="button" class="btn btn-primary" @click='delTeacher("del")'><?php echo Yii::t("message", "OK");?></button>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>
<script>
    var department= <?php echo CJSON::encode($department)?>;
    var height=document.documentElement.clientHeight;
    var container = new Vue({
        el: "#container",
        data: {
            groupName:{
                ES:'<?php echo Yii::t("referral", "Elementary School"); ?>',
                MS:'<?php echo Yii::t("referral", "Secondary School"); ?>',
            },
            department:department,
            search:'',
            height:height,
            gradeList:{},
            addChildType:'',
            searchText:'',
            teacherUid:{},
            options:[],
            loading:false,
            gradeType:'',
            teacher_id:'',
            index:'',
            key:'',
            groupType:''
        },
        watch:{
            searchText(old,newValue){
                if(old!=''){
                    this.searchChild()
                }
            }
        },
        created: function() {
            let currentType=sessionStorage.getItem('groupType') || ''
            if(department.indexOf(currentType)!=-1){
                this.groupType=currentType
            }else{
                this.groupType=department[0]
                sessionStorage.removeItem("groupType");
            }
            this.getGradeList()
        },
        computed: {},
        methods: {
            loseFocus(val) {
                // 下拉框隐藏时
                if (!val) {
                this.options=[]
                }
            },
            tabgroupType(type){
                sessionStorage.setItem('groupType',type);
                this.groupType=type
                this.getGradeList()
            },
            getGradeList(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("getGradelead") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        group:this.groupType
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.gradeList=data.data
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            addTeacher(gradeType){
                this.gradeType=gradeType
                this.options=[]
                $('#addTeacherModal').modal('show')
            },
            confirmTeacher(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("saveGradelead") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        teacher_id:this.teacherUid,
                        grade:this.gradeType,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.teacherUid=''
                            that.options = []
                            resultTip({
                                msg: data.message
                            });
                            that.getGradeList()
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            delTeacher(key,teacher_id,index){
                if(key!='del'){
                    this.key=key
                    this.teacher_id=teacher_id
                    this.index=index
                    $('#delModal').modal('show')
                    return
                }
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("delGradelead") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        teacher_id:this.teacher_id,
                        grade:this.key,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.gradeList.list[that.key].splice(that.index,1)
                            resultTip({
                                msg: data.message
                            });
                            $('#delModal').modal('hide')
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            remoteMethod(query) {
                let that=this
                if (query !== '') {
                    this.loading = true;
                    $.ajax({
                        url: '<?php echo $this->createUrl("teacherSearch") ?>',
                        type: "post",
                        dataType: 'json',
                        data: {
                            teacher_name:query,
                            group:this.groupType
                        },
                        success: function(data) {
                            if (data.state == 'success') {
                                that.options = Object.values(data.data) ;
                            }else{
                                resultTip({
                                    error: 'warning',
                                    msg: data.message
                                });
                            }
                            that.loading = false;
                        },
                        error: function(data) {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            that.loading = false;
                        },
                    })
                } else {
                    this.options = [];
                }
            },
        }
    })
</script>
