<style>
    .addicon{
        width: 40px;
        height: 40px;
        background: #4D88D2;
        border-radius: 50%;
        text-align: center;
        line-height: 40px;
        color: #fff;
        font-size: 14px;
    }
    .list{
        list-style: none;
        padding: 0;
    }
    .groupList{
        padding:10px;
        align-items:center;
        font-size:14px
    }
    .groupList a{
        color:#333;
        display:block
    }
    .groupList:hover,.currentGroup{
        background: rgba(77,136,210,0.1);
        border-radius: 4px;
        cursor: pointer;
        color: #4D88D2;
    }
    .groupList:hover a,.currentGroup a{
        color: #4D88D2;
    }
</style>
<div id='container' v-cloak >
    <div class='row mb20' v-if='department.length>0'>
        <div class="col-md-12">
            <ul class="nav nav-tabs" id="tablist">
                <li role="presentation" v-for='(list,index) in department' :class="groupType==list?'active':''" @click='tabgroupType(list)'><a >{{groupName[list]}}</a></li>
            </ul>
        </div>
    </div>
    <div class='row'>
        <div class="col-md-2">
            <?php
            $mainMenu = array(
                array('label'=>Yii::t('referral','Support Groups'), 'url'=>array("//mteaching/student/index","category"=>"adminGroups","category2"=>"supportList")),
                array('label'=>Yii::t('referral','Head of Grade'), 'url'=>array("//mteaching/student/index","category"=>"adminGroups","category2"=>"headGroup")),
                array('label'=>Yii::t('referral','Principal Office'), 'url'=>array("//mteaching/student/index","category"=>"adminGroups","category2"=>"office")),
                array('label'=>Yii::t('referral','Head of Department'), 'url'=>array("//mteaching/student/index","category"=>"adminGroups","category2"=>"subject")),

            );
            $this->widget('zii.widgets.CMenu',array(
                'id' => 'user-profile-item',
                'items'=> $mainMenu,
                'htmlOptions'=>array('class'=>'list'),
                'activeCssClass'=>'currentGroup',
                'itemCssClass'=>'groupList'
            ));
        ?>
        </div>
        <div class='col-md-10'>
            <div class='color3 font16 fontBold'>
                <div class=''>
                    <button type="button" class="btn btn-primary"  @click='addTeacher'><span class='el-icon-circle-plus-outline'></span> <?php echo Yii::t("global", "Add"); ?></button>
                </div>
            </div>
            <div class='row mt20' v-if='teacherList.list'>
                <div class='col-md-3'  v-for='(list,index) in teacherList.list'>
                    <div class="media listMedia" >
                        <div class="media-left pull-left media-middle">
                            <a href="javascript:void(0)">
                                <img :src="teacherList.staff_info[list].photoUrl" data-holder-rendered="true" class="avatar">
                            </a>
                        </div>
                        <div class="media-right pull-right pt12 text-right">
                            <span class='el-icon-circle-close font16 closeIcon' @click='delTeacher(list,index)'></span>
                        </div>
                        <div class="media-body pt4 media-middle">
                            <div class="lineHeight20  text-primary">
                                <span class='font14 color3 nowrap'>{{teacherList.staff_info[list].name}}</span>
                            </div>
                            <div class="font12 color6 nowrap">{{teacherList.staff_info[list].hrPosition}}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="addTeacherModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel"  aria-labelledby="wantSayModalLabel"  data-backdrop="static">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("global", "Add"); ?></h4>
                </div>
                <div class="modal-body">
                  
                        <div class='color6 font14 '><?php echo Yii::t("global", "Search"); ?></div>
                        <div  class='flex mt16 mb16'>
                            <el-select
                                    v-model="teacherUid"
                                    filterable
                                    remote
                                    clearable
                                    class='inline-input flex1 formControl'
                                    reserve-keyword
                                    placeholder="<?php echo Yii::t("directMessage", "Staff Name"); ?>"
                                    :remote-method="remoteMethod"
                                    prefix-icon="el-icon-search"
                                    :loading="loading">
                                <el-option
                                        v-for="item in options"
                                        :key="item.uid"
                                        :label="item.name"
                                        class='optionSearch mb8'
                                        :value="item.uid">
                                    <div class="media">
                                        <div class="media-left pull-left media-middle">
                                            <a href="javascript:void(0)">
                                                <img :src="item.photoUrl" data-holder-rendered="true" class="media-object img-circle avatar">
                                            </a>
                                        </div>
                                        <div class="media-body mt5 media-middle">
                                            <h4 class="media-heading font14 color3 text_overflow">{{item.name}}</h4>
                                            <div class="text-muted text_overflow font12">{{ item.hrPosition }}</div>
                                        </div>
                                    </div>
                                </el-option>
                            </el-select>
                            <button type="button" class="btn btn-primary ml16"  :disabled='teacherUid==""?true:false' @click='confirmTeacher'><?php echo Yii::t("global", "Add"); ?></button>
                        </div>
                        <div class="row row-no-gutters">
                            
                            <div class='col-xs-6 pb16'   v-for='(list,index) in teacherList.list'>
                                <div class="media listMedia" >
                                    <div class="media-left pull-left media-middle">
                                        <a href="javascript:void(0)">
                                            <img :src="teacherList.staff_info[list].photoUrl" data-holder-rendered="true" class="avatar">
                                        </a>
                                    </div>
                                    <div class="media-right pull-right pt12 text-right">
                                        <span class='el-icon-circle-close font16 closeIcon' @click='delTeacher(list,index)'></span>
                                    </div>
                                    <div class="media-body pt4 media-middle">
                                        <div class="lineHeight20  text-primary">
                                            <span class='font14 color3 nowrap'>{{teacherList.staff_info[list].name}}</span>
                                        </div>
                                        <div class="font12 color6 nowrap">{{teacherList.staff_info[list].hrPosition}}</div>
                                    </div>
                                </div>
                            </div>
                        </div>    
                </div>
            </div>
        </div>
    </div>
    <!-- 删除确认框 -->
    <div class="modal fade" id="delModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
        <div class="modal-dialog modal-sm" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel">
                       <span><?php echo Yii::t("global", "Delete"); ?></span>
                    </h4>
                </div>
                <div class="modal-body" >
                    <div ><?php echo Yii::t("directMessage", "Proceed to remove?"); ?></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                    <button type="button" class="btn btn-primary" @click='delTeacher("del")'><?php echo Yii::t("message", "OK");?></button>
                </div>
            </div>
        </div>
    </div>
</div>
<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>
<script>
    $(document).ready(function () {
    // 通过该方法来为每次弹出的模态框设置最新的zIndex值，从而使最新的modal显示在最前面
        $(document).on('show.bs.modal', '.modal', function (event) {
            var zIndex = 1040 + (10 * $('.modal:visible').length);
            $(this).css('z-index', zIndex);
            setTimeout(function() {
                $('.modal-backdrop').not('.modal-stack').css('z-index', zIndex - 1).addClass('modal-stack');
            }, 0);
        });
    });
    var groupType='MS'
    var department= <?php echo CJSON::encode($department)?>;

    var height=document.documentElement.clientHeight;
    var config = <?php echo CJSON::encode($config)?>;
    var container = new Vue({
        el: "#container",
        data: {
            teacherUid:'',
            teacherList:[],
            options:[],
            loading:false,
            groupType:'',
            groupName:{
                ES:'<?php echo Yii::t("referral", "Elementary School"); ?>',
                MS:'<?php echo Yii::t("referral", "Secondary School"); ?>',
            },
            department:department,
            teacher_id:'',
            index:''
        },
        watch:{
        },
        created: function() {
            let currentType=sessionStorage.getItem('groupType') || ''
            if(department.indexOf(currentType)!=-1){
                this.groupType=currentType
            }else{
                this.groupType=department[0]
                sessionStorage.removeItem("groupType");
            }
            this.getData()
        },
        computed: {},
        methods: {
            tabgroupType(type){
                sessionStorage.setItem('groupType',type);
                this.groupType=type
                this.getData()
            },
            getData(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("officeUserList") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        group:this.groupType
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.teacherList=data.data
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            addTeacher(){
                $('#addTeacherModal').modal('show')
            },
            remoteMethod(query) {
                let that=this
                if (query !== '') {
                    this.loading = true;
                    $.ajax({
                        url: '<?php echo $this->createUrl("teacherSearch") ?>',
                        type: "post",
                        dataType: 'json',
                        data: {
                            teacher_name:query,
                            group:this.groupType
                        },
                        success: function(data) {
                            if (data.state == 'success') {
                                that.options = Object.values(data.data) ;
                            }else{
                                resultTip({
                                    error: 'warning',
                                    msg: data.message
                                });
                            }
                            that.loading = false;
                        },
                        error: function(data) {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            that.loading = false;
                        },
                    })
                } else {
                    this.options = [];
                }
            },
            confirmTeacher(){
                // if(this.teacherList.staff_info[this.teacherUid]){
                //     resultTip({
                //         error: 'warning',
                //         msg: '<?php echo Yii::t("directMessage", "Already added"); ?>'
                //     });
                //     return
                // }
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("officeUser") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        teacher_id:this.teacherUid,
                        type:'1',
                        group:this.groupType
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                           that.teacherUid=''
                           that.options = []
                           that.getData()
                           resultTip({
                                msg:data.state
                            });
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            delTeacher(list,index){
                if(list!='del'){
                    this.teacher_id=list
                    this.index=index
                    $('#delModal').modal('show')
                    return
                }
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("officeUser") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        teacher_id:this.teacher_id,
                        type: "2",//1-设置 2取消
                        group:this.groupType
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.teacherList.list.splice(that.index,1)
                            resultTip({
                                msg: data.message
                            });
                            $('#delModal').modal('hide')
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
        }
    })
</script>
