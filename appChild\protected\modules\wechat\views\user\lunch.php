<div class="weui_cells_title"><?php echo Yii::t('navigations' ,'Cancel Lunch'); ?></div>
<div class="weui_cells weui_cells_form">
	<?php 
		$this->widget('ext.wechat.ChildSelector',array(
		    'childObj'=>$childObj,
		    'myChildObjs'=>$this->myChildObjs,
		    'jumpUrl'=>'lunch',
		));
	 ?>
	<?php if(empty($invoiceDays)): ?>
	<div class="msg">
		<div class="weui_msg">
		    <div class="weui_icon_area"><i class="weui_icon_msg weui_icon_info"></i></div>
		    <div class="weui_text_area">
		        <p class="weui_msg_desc"><?php echo Yii::t('lunch', 'Your child currently does not subscribe to our lunch service.'); ?></p>
		    </div>
		</div>
	</div>
	<?php endif; ?>
    <?php foreach ($invoiceDays as $invoiceDay):?>
	<div class="weui_cell weui_cell_switch">
	    <div class="weui_cell_hd weui_cell_primary"><?php echo date('Y/m/d', strtotime($invoiceDay)); ?></div>
	    <div class="weui_cell_ft">
	        <input type="checkbox" class="weui_switch" value="<?php echo $invoiceDay;?>"<?php if(!in_array($invoiceDay, $refundLunchDays)){echo 'checked';} ?>/>
	    </div>
	</div>
    <?php endforeach; ?>
</div>

<script>
	$('.weui_cells_form .weui_cell_ft input').on('change', function(e) {
		var operate = 'DO';
		var inputObj = $(this);
		if(inputObj.is(':checked')){
			operate = 'UNDO';
		}
		var day = inputObj.val();
		$('#loadingToast').show();

		$.ajax({
			type: 'POST',
			url: '<?php echo $this->createUrl('cancelLunch', array('childid'=>$this->getChildId())); ?>',
			data: {day: day, operate: operate},
			dataType: 'JSON',
			timeout: 10000,
			success: function (data) {
				data = eval('(' + data + ')');
				if (data.state == 'success') {
					showMessage('<?php echo Yii::t('global', 'Success!'); ?>');
				} else{
					inputToggle(inputObj);
					showTips('<?php echo Yii::t('global', 'Failed!'); ?>', data.message);
				}
			},
			error: function () {
				inputToggle(inputObj);
				showTips('<?php echo Yii::t('global', 'Failed!'); ?>', '<?php echo Yii::t('global', '服务器错误'); ?>');
			},
			complete: function() {
				$('#loadingToast').hide();
			}
		});
	});

	//切换input的选中状态
	function inputToggle(inputObj) {
		if(inputObj.is(':checked')){
			inputObj.prop('checked', false);
		} else{
			inputObj.prop('checked', true);
		}
	}
</script>