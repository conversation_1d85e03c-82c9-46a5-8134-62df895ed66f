<?php echo $pointStartDate ?>
<?php $nf = new CNumberFormatter('zh-cn'); ?>

<div class="cell">
	<div class="bd">
	<div class="weui_cells_title"><?php echo Yii::t('navigations', 'General Credit'); ?></div>
		<!-- 个人余额 -->
		<div class="weui_cells">
		<?php
						//选择孩子
						$this->widget('ext.wechat.ChildSelector',array(
							'childObj'=>$childObj,
							'myChildObjs'=>$this->myChildObjs,
							'jumpUrl'=>'credit',
						));
					?>
			 <!-- 账户余额 -->
				<div class="weui_cell weui_cells_access">
				    <div class="weui_cell_bd weui_cell_primary">
				        <?php echo Yii::t('payment', 'Account Credits'); ?>
				    </div>  
				    <div class="weui_cell_bd weui_cell_primary weui_cell_ft">
				        <?php echo $nf->format('#,##0.00', $balance); ?>
				    </div>
				</div>
				<!-- 账户明细 -->
    			<?php foreach($creditModel as $credit): ?>
    				<div class="weui_cell detail" style="display:none">
			            <div class="weui_cell_bd weui_cell_primary">
			                <?php echo Invoice::feeType($credit->itemname); ?>
			            </div>
	    	            <div class="weui_cell_bd weui_cell_primary weui_cell_ft">
			             	<?php echo ($credit->inout == 'in' ? '+ ' : '- ') . $nf->format('#,##0.00', $credit->amount); ?>
	    	            </div>
	            	</div>
    		    <?php endforeach; ?>
	    </div>
	    <!-- 预缴学费 -->
	    <?php if($deposit || $depositDetail): ?>
    	<div class="weui_cells">
	        <div class="weui_cell weui_cells_access">
	            <div class="weui_cell_bd weui_cell_primary">
	                <?php echo Yii::t('payment', 'Deposit');?>
	            </div>
	            <div class="weui_cell_bd weui_cell_primary weui_cell_ft">
    	            <?php echo $nf->format('#,##0.00', $deposit);  ?>
	            </div>
	        </div>
	        <!-- 预缴学费明细 -->
			
			<?php foreach ($depositDetail as $v) : ?>
				<div class="weui_cell deposit" style="display:none">
		            <div class="weui_cell_bd weui_cell_primary">
		                <?php echo ($v->calendarInfo->startyear) .'~'. ($v->calendarInfo->startyear+1); ?>
		            </div>
		            <div class="weui_cell_bd weui_cell_primary weui_cell_ft">
		             	<?php echo ($v->inout == 'in' ? '+ ' : '- ') . $nf->format('#,##0.00', $v->amount); ?>
		            </div>
	            </div>
	            <?php endforeach; ?>
	        <?php endif; ?>
	    </div>
		<!-- 剩余积分 -->
	    <?php if($status): ?>
	    <div class="weui_cells">
	        <div class="weui_cell">
	        	<div class="weui_cell_bd weui_cell_primary">
	        	    <p><?php echo Yii::t('payment', 'Current Balance'); ?></p>
	        	</div>	
	            <div class="weui_cell_ft"><?php echo $points; ?></div>
	        </div>
        </div>
    	<?php endif;?>
	</div>
</div>
<script>
	$('.detail').prev().on('click', function () {
		$('.detail').toggle();
	});	
	$('.deposit').prev().on('click', function () {
		$('.deposit').toggle();
	});
</script>