<div style="font-family: 'Microsoft Yahei'">
    <p>以下是<?php echo $school ?>过期帐单列表 过期账单应付总金额：<?php echo $sum[$schoolid]?>
	</p>
	<?php foreach(array_keys($invoices) as $classid): ?> 
		<?php echo isset($classInfo[$classid]) ? $classInfo[$classid] : ''; ?>
		<table border='1' cellpadding='2' cellspacing='0' width="100%">
			<tr>
				<th width="100" style="font-family: 'Microsoft Yahei';font-size:12px;">孩子姓名</th>
				<th width="100" style="font-family: 'Microsoft Yahei';font-size:12px;">账单标题</th>
				<th width="100" style="font-family: 'Microsoft Yahei';font-size:12px;">到期日期</th>
				<th width="100" style="font-family: 'Microsoft Yahei';font-size:12px;">账单金额</th>
				<th width="100" style="font-family: 'Microsoft Yahei';font-size:12px;">应付金额</th>
			</tr>
			<?php
			foreach(array_keys($invoices[$classid]) as $childid):
				$countbyChild =  count($invoices[$classid][$childid]);
			?>
			<tr>
				<td style="font-family: 'Microsoft Yahei';font-size:12px;" 
                <?php if($countbyChild>1) echo sprintf(" rowspan=%s", $countbyChild);?>
                >
                    <?php echo isset($childInfo[$childid])?$childInfo[$childid]:'' . ' ' . $childid;?>
				</td>
				<td style="font-family: 'Microsoft Yahei';font-size:12px;">
					<?php echo CHtml::encode( $invoices[$classid][$childid][0]['title'] );?>
				</td>
				<td style="font-family: 'Microsoft Yahei';font-size:12px;text-align: center;">
					<?php echo date('Y-m-d', $invoices[$classid][$childid][0]['duetime']);?>
				</td>
				<td style="font-family: 'Microsoft Yahei';font-size:12px;text-align: right;">
					<?php echo $invoices[$classid][$childid][0]['z_amount'];?>
				</td>
				<td style="font-family: 'Microsoft Yahei';font-size:12px;text-align: right;">
					<?php echo $invoices[$classid][$childid][0]['y_amount'];?>
				</td>
			</tr>
			<?php
            if($countbyChild > 1):
            
            for($i=1; $i<$countbyChild; $i++){
                if (isset($invoices[$classid][$childid][$i])):
            ?>
			<tr>
				<td style="font-family: 'Microsoft Yahei';font-size:12px;">
					<?php echo CHtml::encode( $invoices[$classid][$childid][$i]['title'] );?>
				</td>
				<td style="font-family: 'Microsoft Yahei';font-size:12px;text-align: center;">
					<?php echo date('Y-m-d', $invoices[$classid][$childid][$i]['duetime']);?>
				</td>
				<td style="font-family: 'Microsoft Yahei';font-size:12px;text-align: right;">
					<?php echo $invoices[$classid][$childid][$i]['z_amount'];?>	
				</td>
				<td style="font-family: 'Microsoft Yahei';font-size:12px;text-align: right;">
					<?php echo $invoices[$classid][$childid][$i]['y_amount'];?>
				</td>	
			</tr>
			<?php
            endif;
            }
            endif;
            ?>
            
			<?php endforeach;?>
		</table>
		<br>
	<?php endforeach;?>
	
	<p>Auto Email,please do not reply.<br>
	系统自动邮件，请不要回复。</p>
	
</div>