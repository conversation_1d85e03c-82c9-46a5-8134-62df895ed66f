<?php

/**
 * This is the model class for table "ivy_dep_pos_link".
 *
 * The followings are the available columns in table 'ivy_dep_pos_link':
 * @property integer $position_id
 * @property integer $department_id
 * @property integer $is_lead
 * @property string $authority
 */
class DepPosLink extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return DepPosLink the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_dep_pos_link';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
            array('department_id', 'required'),
			array('position_id, department_id, is_lead', 'numerical', 'integerOnly'=>true),
			array('authority', 'length', 'max'=>255),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('position_id, department_id, is_lead, authority', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'position' => array(self::BELONGS_TO, 'HrPosition', 'position_id'),
            'department' => array(self::BELONGS_TO, 'HrDepartment', 'department_id'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'position_id' => 'Position',
			'department_id' => 'Department',
			'is_lead' => 'Is Lead',
			'authority' => 'Authority',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('position_id',$this->position_id);
		$criteria->compare('department_id',$this->department_id);
		$criteria->compare('is_lead',$this->is_lead);
		$criteria->compare('authority',$this->authority,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
    
    public function getDepPos($schoolid='')
    {
        $dps = array();
        $criteria = new CDbCriteria;
        if($schoolid){
            $model = Branch::model()->findByPk($schoolid);
            $criteria->compare('department.category', $model->type);
        }
        $criteria->order = 'department.weight asc';
        $deppos = $this->with('position', 'department')->findAll($criteria);
        foreach ($deppos as $dp){
            if (is_object($dp->department) && is_object($dp->position))
                $dps[$dp->department->getName()][$dp->position_id] = $dp->position->getName();
        }
        return $dps;
    }
}