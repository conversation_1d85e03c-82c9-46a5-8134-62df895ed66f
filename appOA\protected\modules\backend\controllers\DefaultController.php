<?php

class DefaultController extends ProtectedController
{
	public function actionIndex()
	{
		$this->layout = "//layouts/blank";
		Yii::app()->clientScript->registerCoreScript('jquery');
		//echo urlencode("http://apps.ivynew.cn");
		$this->render('index');
	}
	//public function actionIndex2()
	//{
	//	Yii::app()->clientScript->registerCoreScript('jquery');
	//	//echo urlencode("http://apps.ivynew.cn");
	//	$this->render('index2');
	//}

    public function actionAttachment($file = '', $type = 'big')
    {
        $file = strtolower($type) == 'big' ? '/uploads/' . $file : '/uploads/thumbs/' . $file;
        echo '<img src="'. $this->printFileStreams( $file ).'">';
    }

    public function printFileStreams($subPath, $fileRootPath = null)
    {
        $fileRootPath = is_null($fileRootPath) ? Yii::app()->params['xoopsVarPath']  : $fileRootPath;
        $fileRootPath = rtrim($fileRootPath, '/') . '/';
        $subPath = ltrim($subPath, '/');

        $file = $fileRootPath . $subPath;

        //First, see if the file exists, if not, go to aliyun OSS.
        if (!is_file($file)) {
            $fileInfo = pathinfo($file);

            require_once rtrim(Yii::app()->params['OAUploadBasePath'], '/') .'/../class/Aliyun/OSS.php';
            $ossSDK = new OSS('ivy-private-uploads');
            $ossObj = $ossSDK->get_object($subPath);
            if ( $ossObj->status == 200) {
                header(sprintf("content-type: %s", $ossObj->header['content-type']));
                header(sprintf("content-length: %s", $ossObj->header['content-length']));
                header(sprintf("accept-ranges: %s", $ossObj->header['accept-ranges']));
                if (!in_array($fileInfo['extension'], array('jpg', 'jpeg', 'png', 'gif', 'pdf'))) {
                    header('Content-Disposition: attachment; filename=' . $fileInfo['basename']);
                }

                $bytes = $ossObj->body;
                print($bytes);
            } else {
                die('cannot find file on OSS');
            }
        } else {
            $fileInfo = pathinfo($file);
            //Gather relevent info about file
            $filename = $fileInfo["basename"];
            $file_extension = strtolower($fileInfo["extension"]);
            //This will set the Content-Type to the appropriate setting for the file
            switch( $file_extension ) {
                case "zip": $ctype="application/zip"; break;
                case "mp3": $ctype="audio/mpeg"; break;
                case "mpg":$ctype="video/mpeg"; break;
                case "avi": $ctype="video/x-msvideo"; break;
                case "gif": $ctype="image/gif"; break;
                case "jpg": $ctype="image/jpeg"; break;
                case "jpeg": $ctype="image/jpeg"; break;
                case "png": $ctype="image/png"; break;
                case "pdf": $ctype="application/pdf"; break;
                default: $ctype="application/force-download";
            }
            //Use the switch-generated Content-Type
            header("Content-Type: $ctype");
            if ($ctype == 'application/force-download') {
                header('Content-Disposition: attachment; filename='.$filename);
            }

            header("Accept-Ranges: bytes");
            print(file_get_contents($file));
        }

        flush();
        ob_flush();
    }
}
