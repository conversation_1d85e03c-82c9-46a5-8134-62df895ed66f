<?php $form=$this->beginWidget('CActiveForm', array(
    'id'=>'visits-form',
    'enableAjaxValidation'=>false,
    'htmlOptions'=>array('class'=>'J_ajaxForm form-horizontal', 'role'=>'form'),
));
$branchList = Branch::model()->getBranchList();
$cfgs = $this->loadConfig();
$knowusList = array();
if (isset($cfgs['knowus']))
{
    foreach ($cfgs['knowus'] as $k=>$v)
    {
        $knowusList[$k] = (Yii::app()->language=='zh_cn') ? $v['cn'] : $v['en'];
    }
}
$languageList = array();
if (isset($cfgs['language']))
{
    foreach ($cfgs['language'] as $k=>$v)
    {
        $languageList[$k] = (Yii::app()->language=='zh_cn') ? $v['cn'] : $v['en'];
    }
}
$concerns = array();
if (isset($cfgs['concerns']))
{
    foreach ($cfgs['concerns'] as $k=>$v)
    {
        $concerns[$k] = (Yii::app()->language=='zh_cn') ? $v['cn'] : $v['en'];
    }
}
?>
<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">×</span></button>
    <h4 class="modal-title"><?php echo $modalTitle;?></h4>
</div>
<div class="modal-body">

    <!-- 邮箱 -->
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'email'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->textField($model,'email',array('maxlength'=>255,'class'=>'form-control')); ?>
        </div>
    </div>
    <!-- 电话 -->
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'tel'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->textField($model,'tel',array('maxlength'=>255,'class'=>'form-control')); ?>
        </div>
    </div>
    <!-- 家长姓名 -->
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'parent_name'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->textField($model,'parent_name',array('maxlength'=>255,'class'=>'form-control')); ?>
        </div>
    </div>
    <!-- 孩子姓名 -->
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'child_name'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->textField($model,'child_name',array('maxlength'=>255,'class'=>'form-control')); ?>
        </div>
    </div>
    <!-- 孩子生日 -->
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'birth_timestamp'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->textField($model,'birth_timestamp',array('maxlength'=>255,'class'=>'datepicker form-control','readonly'=>'readonly')); ?>
        </div>
    </div>
    <!-- 国家 -->
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'country'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->dropDownList($model,'country',Country::model()->getCountryList(),array('class'=>'form-control')); ?>
        </div>
    </div>
    <!-- 地址 -->
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'address'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->textField($model,'address',array('maxlength'=>255,'class'=>'form-control')); ?>
        </div>
    </div>
    <!-- 沟通方式 -->
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'communication'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->radioButtonList($model,'communication',IvyVisitsBasicInfo::communications(),array('template'=>'<label class="radio-inline">{input}{label}</label>','separator'=>'')); ?>
        </div>
    </div>
    <!-- 预计上学时间 -->
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'child_enroll'); ?></label>
        <div class="col-xs-9">
           <?php echo $form->textField($model,'child_enroll',array('maxlength'=>255,'class'=>'datepicker form-control','readonly'=>'readonly')); ?>
        </div>
    </div>
    <!-- 哪里知道我们 -->
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'knowus'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->dropDownList($model,'knowus',$knowusList,array('class'=>'form-control')); ?>
        </div>
    </div>
    <!-- 家长注重 -->
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'concerns'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->checkBoxList($model,'concerns',$concerns); ?>
        </div>
    </div>
    <!-- 备注 -->
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'memo'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->textArea($model,'memo', array('class'=>'form-control')); ?>
        </div>
    </div>
</div>
<div class="modal-footer">
    <button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global','Submit');?></button>
    <button type="button" class="btn btn-default"  data-dismiss="modal"><?php echo Yii::t('global','Cancel');?></button>
</div>

<?php $this->endWidget(); ?>
<script>
    $('.datepicker').datepicker({
        changeMonth: true,
        changeYear: true,
        dateFormat:'yy-mm-dd',
    });
    // 回调：添加成功
    function cbVisit() {
        $('#modal').modal('hide');
        $.fn.yiiGridView.update('confirmed-visit-grid');
    }
</script>