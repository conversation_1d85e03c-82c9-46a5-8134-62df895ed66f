<?php

class WithdrawalController extends BranchBasedController
{
    public $printFW = array();

    public function createUrl($route, $params = array(), $ampersand = '&', $parentOnly = false)
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        return parent::createUrl($route, $params, $ampersand);
    }

    public function init()
    {
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'campusOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site', 'Campus Workspace');
        $this->accessBranch = array('BJ_DS', 'BJ_SLT'); //DS SLT 可用
        //初始化选择校园页面
        $schoolList = array('BJ_DS', 'BJ_SLT');
        foreach ($this->accessBranch as $key => $item) {
            if (!in_array($item, $schoolList)) {
                unset($this->accessBranch[$key]);
            }
        }
        $this->branchSelectParams['hideOffice'] = true;
        $this->branchSelectParams['urlArray'] = array('//mcampus/withdrawal/index');
        $cs = Yii::app()->clientScript;
        $cs->registerCoreScript('jquery.ui');
        $cs->registerCssFile($cs->getCoreScriptUrl() . '/jui/css/base/jquery-ui.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/element/vue.global.js');
        $cs->registerCssFile(Yii::app()->themeManager->baseUrl . '/base/js/element/index.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/element/index.js');
        $cs->registerCssFile(Yii::app()->themeManager->baseUrl . '/modern/css/wizard/bootstrap-nav-wizard.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/xlsx.full.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/plupload/plupload.full.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/qiniu.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/jquery.qrcode.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/jquery.printThis.js');
    }

    public function beforeAction($action)
    {
        if ($this->checkUser() || $this->checkRole()) {
            return parent::beforeAction($action);
        }

        $this->render('//denied/index');
        Yii::app()->end();
    }

    public function checkRole()
    {
        $roleList = array('ivystaff_opschool', 'ivystaff_finance', 'ivystaff_cd', 'ivystaff_pd', 'ivystaff_counselor');
        foreach ($roleList as $role) {
            if (Yii::app()->user->checkAccess($role)) {
                return true;
            }
        }
        return false;
    }

    public function isFinance()
    {
        $roleList = array('ivystaff_finance');
        foreach ($roleList as $role) {
            if (Yii::app()->user->checkAccess($role)) {
                return true;
            }
        }
        return false;
    }

    public function checkUser()
    {

        $res = CommonUtils::requestDsOnline2('withdrawal/taskOwners', array('schoolId' => $_GET['branchId']));
        if (isset($res['code']) && $res['code'] == 0) {
            $data = $res['data'];
            if (is_array($data) && in_array(Yii::app()->user->getId(), $data)) {
                return true;
            }
        }
        return false;
    }

    public function actionIndex()
    {
        $this->getCalendars();

        $this->render('index', array('startYear' => current($this->calendarStartYear)));
    }

    public function actionSearch()
    {
        $childName = Yii::app()->request->getParam('childName', '');

        $data = array();
        $res = CommonUtils::requestDsOnline2('withdrawal/search', array(
            'schoolId' => $this->branchId,
            'childName' => $childName,
        ));
        if (isset($res['code']) && $res['code'] == 0) {
            $data = $res['data'];
        }
        if (isset($res['code']) && $res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $data);
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
        }

        $this->showMessage();
    }

    public function actionIndexData()
    {
        $startYear = Yii::app()->request->getParam('startYear', '');

        $data = array();
        $res = CommonUtils::requestDsOnline2('withdrawal/index', array(
            'schoolId' => $this->branchId,
            'startYear' => $startYear,
        ));
        if (isset($res['code']) && $res['code'] == 0) {
            $data = $res['data'];
        }
        if (isset($res['code']) && $res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $data);
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
        }

        $this->showMessage();
    }

    public function actionList()
    {
        $startYear = Yii::app()->request->getParam('startYear', '');
        $node = Yii::app()->request->getParam('node', '');
        $task = Yii::app()->request->getParam('task', '');
        $showAll = Yii::app()->request->getParam('showAll', 0);

        $data = array();
        $res = CommonUtils::requestDsOnline2('withdrawal/list', array(
            'schoolId' => $this->branchId,
            'startYear' => $startYear,
            'node' => $node,
            'task' => $task,
            'showAll' => $showAll,
        ));
        if (isset($res['code']) && $res['code'] == 0) {
            $data = $res['data'];
        }
        if (isset($res['code']) && $res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $data);
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
        }

        $this->showMessage();
    }

    public function actionAddApplication()
    {
        $childIds = Yii::app()->request->getParam('childIds', array());
        $skip = Yii::app()->request->getParam('skip', array());

        $res = CommonUtils::requestDsOnline2('withdrawal/addApplication', array(
            'schoolId' => $this->branchId,
            'childIds' => $childIds,
            'skip' => $skip,
        ));

        $this->extracted($res);
    }
    public function actionDelApplication()
    {
        $applicationId = Yii::app()->request->getParam('applicationId', '');
        if (!$applicationId) {
            $this->addMessage('state', 'fail');
            $this->addMessage('msg', 'applicationId error');
            $this->showMessage();
        }

        $res = CommonUtils::requestDsOnline2('withdrawal/del/' . $applicationId, array(
            'schoolId' => $this->branchId,
        ));

        $this->extracted($res);
    }
    public function actionCancelApplication()
    {
        $applicationId = Yii::app()->request->getParam('applicationId', '');
        $confirm = Yii::app()->request->getParam('confirm', false);
        if (!$applicationId) {
            $this->addMessage('state', 'fail');
            $this->addMessage('msg', 'applicationId error');
            $this->showMessage();
        }

        $res = CommonUtils::requestDsOnline2('withdrawal/cancel/' . $applicationId, array(
            'schoolId' => $this->branchId,
            'confirm' => $confirm,
        ));

        $this->extracted($res);
    }

    public function actionRevokeTask()
    {
        $applicationId = Yii::app()->request->getParam('applicationId', '');
        if (!$applicationId) {
            $this->addMessage('state', 'fail');
            $this->addMessage('msg', 'applicationId error');
            $this->showMessage();
        }

        $res = CommonUtils::requestDsOnline2('withdrawal/revokeTask', array(
            'schoolId' => $this->branchId,
            'applicationId' => Yii::app()->request->getParam('applicationId'),
            'node' => Yii::app()->request->getParam('node'),
            'task' => Yii::app()->request->getParam('task'),
        ));

        $this->extracted($res);
    }

    public function actionNode()
    {
        $applicationId = Yii::app()->request->getParam('applicationId', '');
        $node = Yii::app()->request->getParam('node', '');
        $data = array();
        $res = CommonUtils::requestDsOnline2('withdrawal/showNode', array(
            'schoolId' => $this->branchId,
            'applicationId' => $applicationId,
            'node' => $node,
        ));
        if (isset($res['code']) && $res['code'] == 0) {
            $data = $res['data'];

            foreach ($data['forms'] as $key => $form) {
                $viewOrEdit = 'view';
                $isOwner = in_array(Yii::app()->user->id, $form['owner']);
                if ($isOwner && $form['status'] == 0) {
                    $viewOrEdit = 'edit';
                }
                if ($isOwner && $form['confirm_status'] == 2) {
                    $viewOrEdit = 'edit';
                }
                $page = sprintf('steps/%s/%s/%s', $form['key'], $form['template'], $viewOrEdit);
                $data['forms'][$key]['template_state'] = $viewOrEdit;
                $data['forms'][$key]['template'] = $this->renderPartial($page, array(), true);
            }
        }
        $this->addMessage('state', 'success');
        $this->addMessage('data', $data);
        $this->showMessage();
    }

    public function actionSaveNode()
    {
        $data = $_POST;
        $data['schoolId'] = $this->branchId;

        $res = CommonUtils::requestDsOnline2('withdrawal/saveNode', $data);
        $this->extracted($res);
    }

    public function actionBatchSave()
    {
        $data = $_POST;
        $data['schoolId'] = $this->branchId;

        $res = CommonUtils::requestDsOnline2('withdrawal/batchSave', $data);
        $this->extracted($res);
    }

    public function actionReject()
    {
        $data = $_POST;
        $data['schoolId'] = $this->branchId;

        $res = CommonUtils::requestDsOnline2('withdrawal/reject', $data);
        $this->extracted($res);
    }

    public function actionSendAppForm()
    {
        $data = $_POST;
        $data['schoolId'] = $this->branchId;

        $res = CommonUtils::requestDsOnline2('withdrawal/sendAppForm', $data);
        $this->extracted($res);
    }
    public function actionFinancePush()
    {
        $data = $_POST;
        $data['schoolId'] = $this->branchId;

        $res = CommonUtils::requestDsOnline2('withdrawal/financePush', $data);
        $this->extracted($res);
    }

    public function actionCancelReject()
    {
        $data = $_POST;
        $data['schoolId'] = $this->branchId;

        $res = CommonUtils::requestDsOnline2('withdrawal/cancelReject', $data);
        $this->extracted($res);
    }

    public function actionRejectLog()
    {
        $data = $_POST;
        $data['schoolId'] = $this->branchId;

        $res = CommonUtils::requestDsOnline2('withdrawal/rejectLog', $data);
        if (isset($res['code']) && $res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        }
        $this->addMessage('state', 'fail');
        $this->addMessage('message', $res['msg']);
        $this->showMessage();
    }

    public function actionFinanceConfirmAll()
    {
        $data = $_POST;
        $data['schoolId'] = $this->branchId;

        $res = CommonUtils::requestDsOnline2('withdrawal/financeConfirmAll', $data);
        if (isset($res['code']) && $res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('callback', 'cbSuccess2');
            $this->addMessage('message', Yii::t('message', 'Data Saved!'));
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
        }

        $this->showMessage();
    }

    public function actionFinanceCancelConfirmAll()
    {
        $data = $_POST;
        $data['schoolId'] = $this->branchId;

        $res = CommonUtils::requestDsOnline2('withdrawal/financeCancelConfirmAll', $data);
        if (isset($res['code']) && $res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('callback', 'cbSuccess2');
            $this->addMessage('message', Yii::t('message', 'Data Saved!'));
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
        }

        $this->showMessage();
    }

    // 获取七牛上传的token
    public function actionGetQiniuToken()
    {
        $applicationId = Yii::app()->request->getParam('applicationId', '');
        $linkType = Yii::app()->request->getParam('linkType', 'withdrawal_enrollment');
        $isPrivate = Yii::app()->request->getParam('isPrivate', 1);
        if (!$applicationId || !$linkType) {
            $this->addMessage("state", "fail");
            $this->addMessage('message', "参数错误");
            $this->showMessage();
        }
        $data = array(
            'linkType' => $linkType,
            'linkId' => $applicationId,
            'isPrivate' => $isPrivate,
        );
        $res = CommonUtils::requestDsOnline('getQiniuToken', $data);
        $this->addMessage("state", "success");
        $this->addMessage('data', $res);
        $this->showMessage();
    }

    // 删除附件
    public function actionDeleteAttachment()
    {
        $attachmentId = Yii::app()->request->getParam('attachmentId');
        $res = CommonUtils::requestDsOnline('deleteFile/' . $attachmentId);
        $this->addMessage("state", "success");
        $this->addMessage('data', $res);
        $this->showMessage();
    }

    /**
     * 可选老师搜索
     *
     * @return void
     */
    public function actionTeacherSearch()
    {
        $requestData = array(
            'schoolId' => $this->branchId,
            'searchString' => Yii::app()->request->getParam('searchString'),
        );
        $res = CommonUtils::requestDsOnline('directMessage/teacherSearch', $requestData);
        $this->addMessage("state", "success");
        $this->addMessage('message', $res['msg']);
        $this->addMessage('data', $res['data']);
        $this->showMessage();
    }

    public function actionRecover()
    {
        $applicationId = Yii::app()->request->getParam('applicationId', '');
        if (!$applicationId) {
            $this->addMessage('state', 'fail');
            $this->addMessage('msg', 'applicationId error');
            $this->showMessage();
        }

        $res = CommonUtils::requestDsOnline2('withdrawal/recover/' . $applicationId, array(
            'schoolId' => $this->branchId,
        ));

        $this->extracted($res);
    }

    public function actionRefundRecordList()
    {
        $appId = Yii::app()->request->getParam('app_id', '');
        if (!$appId) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', 'applicationId error');
            $this->showMessage();
        }

        $res = CommonUtils::requestDsOnline2('withdrawal/refundRecord/list', array(
            'schoolId' => $this->branchId,
            'app_id' => $appId,
        ));

        $this->extracted($res);
    }

    public function actionRefundRecordEdit()
    {
        if (!$this->isFinance()) {
            $this->addMessage("state", "fail");
            $this->addMessage('message', '权限错误');
            $this->showMessage();
        }
        $appId = Yii::app()->request->getParam('app_id');
        $recordId = Yii::app()->request->getParam('record_id');
        $amount = Yii::app()->request->getParam('amount');
        $memo = Yii::app()->request->getParam('memo');
        if (!$appId) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', 'applicationId error');
            $this->showMessage();
        }
        if (!$amount) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', 'amount error');
            $this->showMessage();
        }

        $res = CommonUtils::requestDsOnline2('withdrawal/refundRecord/edit', array(
            'schoolId' => $this->branchId,
            'app_id' =>$appId,
            'record_id' =>$recordId,
            'amount' => $amount,
            'memo' => $memo,
        ));

        $this->extracted($res);
    }

    public function actionRefundRecordDel()
    {
        $id = Yii::app()->request->getParam('record_id');
        
        if (!$id) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', 'record id error');
            $this->showMessage();
        }

        $res = CommonUtils::requestDsOnline2('withdrawal/refundRecord/del', array(
            'schoolId' => $this->branchId,
            'record_id' => $id,
        ));

        $this->extracted($res);
    }

    public function actionGetQrcode()
    {
        $applicationId = Yii::app()->request->getParam('applicationId', '');
        $type = Yii::app()->request->getParam('type', 'form');
        if (!$applicationId) {
            $this->addMessage("state", "fail");
            $this->addMessage('message', 'applicationId not null');
            $this->showMessage();
        }
        $res = CommonUtils::requestDsOnline2('withdrawal/qrcode/' . $applicationId, array(
            'schoolId' => $this->branchId,
            'type' => $type,
        ));
        $this->addMessage("state", "success");
        $this->addMessage('data', $res['data']);
        $this->showMessage();
    }

    public function actionGetFinanceStateInfo()
    {
        $app_id = Yii::app()->request->getParam('app_id');
        if (!$app_id) {
            $this->addMessage("state", "fail");
            $this->addMessage('message', 'applicationId not null');
            $this->showMessage();
        }
        $res = CommonUtils::requestDsOnline2('withdrawal/financeState/info', array(
            'schoolId' => $this->branchId,
            'app_id' => $app_id,
        ));
        $this->addMessage("state", "success");
        $this->addMessage('data', $res['data']);
        $this->addMessage('message', $res['msg']);
        $this->showMessage();
    }

    public function actionDelFinanceStateFile()
    {
        if (!$this->isFinance()) {
            $this->addMessage("state", "fail");
            $this->addMessage('message', '权限错误');
            $this->showMessage();
        }
        $id = Yii::app()->request->getParam('id');
        if (!$id) {
            $this->addMessage("state", "fail");
            $this->addMessage('message', 'fileid not null');
            $this->showMessage();
        }
        $res = CommonUtils::requestDsOnline2('withdrawal/financeState/fileDel', array(
            'schoolId' => $this->branchId,
            'id' => $id,
        ));
        $this->addMessage("state", "success");
        $this->addMessage('data', $res['data']);
        $this->addMessage('message', $res['msg']);
        $this->showMessage();
    }

    public function actionSetFinanceState()
    {
        if (!$this->isFinance()) {
            $this->addMessage("state", "fail");
            $this->addMessage('message', '权限错误');
            $this->showMessage();
        }
        $app_id = Yii::app()->request->getParam('app_id');
        $state = Yii::app()->request->getParam('state');
        $state_memo = Yii::app()->request->getParam('state_memo');
        if (!$app_id) {
            $this->addMessage("state", "fail");
            $this->addMessage('message', 'applicationId not null');
            $this->showMessage();
        }
        $res = CommonUtils::requestDsOnline2('withdrawal/setFinanceState', array(
            'schoolId' => $this->branchId,
            'app_id' => $app_id,
            'state' => $state,
            'state_memo' => $state_memo,
        ));
        $this->addMessage("state", $res['code'] == 0 ? "success" : "fail");
        $this->addMessage('data', $res['data']);
        $this->addMessage('message', $res['msg']);
        $this->showMessage();
    }


    public function actionGetParentsFreq()
    {
        $childId = Yii::app()->request->getParam('childId');
        $openIds = Yii::app()->request->getParam('openIds');
        if (!$childId || !$openIds) {
            $this->addMessage("state", "fail");
            $this->addMessage('message', '参数错误');
            $this->showMessage();
        }
        $res = CommonUtils::requestDsOnline2('withdrawal/parentsFreq', array(
            'schoolId' => $this->branchId,
            'childId' => $childId,
            'openIds' => $openIds,
        ));
        $this->addMessage("state", "success");
        $this->addMessage('data', $res['data']);
        $this->addMessage('message', $res['msg']);
        $this->showMessage();
    }

    public function actionPrint($appId) {
        $res = CommonUtils::requestDsOnline2('withdrawal/printData/' . $appId, array(
            'schoolId' => $this->branchId,
        ));
        $printData = array();
        if ($res['code'] == 0) {
            $printData = $res['data'];
        }

        $this->layout = '//layouts/print';
        $this->render('print', array(
            'appId' => $appId,
            'printData' => $printData
        ));
    }

    /**
     * @param $res
     */
    public function extracted($res)
    {
        $callback = Yii::app()->request->getParam('callback', 'cbSuccess');
        if (isset($res['code']) && $res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res['data']);
            $this->addMessage('callback', $callback);
            $this->addMessage('message', Yii::t('message', 'Data Saved!'));
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('data', $res['data']);
            $this->addMessage('message', $res['msg']);
        }

        $this->showMessage();
    }

    /*****************负责人管理 start***********************/
    public function actionTasksOwner()
    {
        $this->branchSelectParams['urlArray'] = array('//mcampus/withdrawal/tasksOwner');
        $this->render('tasksOwner', array());
    }

    public function actionOwnersList()
    {
        $res = CommonUtils::requestDsOnline2('withdrawal/owners/list', array(
            'schoolId' => $this->branchId,
        ));
        $this->addMessage("state", "success");
        $this->addMessage('data', $res['data']);
        $this->addMessage('message', $res['msg']);
        $this->showMessage();
    }

    public function actionOwnersAdd()
    {
        $node = Yii::app()->request->getParam('node');
        $task = Yii::app()->request->getParam('task');
        $user_id = Yii::app()->request->getParam('user_id');
        if (!$node || !$task || !$user_id) {
            $this->addMessage("state", "fail");
            $this->addMessage('message', '参数错误');
            $this->showMessage();
        }
        $res = CommonUtils::requestDsOnline2('withdrawal/owners/add', array(
            'schoolId' => $this->branchId,
            'node'     => $node,
            'task'     => $task,
            'user_id'  => $user_id,
        ));
        $this->addMessage("state", "success");
        $this->addMessage('data', $res['data']);
        $this->addMessage('message', $res['msg']);
        $this->showMessage();
    }

    public function actionOwnersDel()
    {
        $node = Yii::app()->request->getParam('node');
        $task = Yii::app()->request->getParam('task');
        $user_id = Yii::app()->request->getParam('user_id');
        if (!$node || !$task || !$user_id) {
            $this->addMessage("state", "fail");
            $this->addMessage('message', '参数错误');
            $this->showMessage();
        }
        $res = CommonUtils::requestDsOnline2('withdrawal/owners/del', array(
            'schoolId' => $this->branchId,
            'node'     => $node,
            'task'     => $task,
            'user_id'  => $user_id,
        ));
        $this->addMessage("state", "success");
        $this->addMessage('data', $res['data']);
        $this->addMessage('message', $res['msg']);
        $this->showMessage();
    }
    /*****************负责人管理 end***********************/

}
