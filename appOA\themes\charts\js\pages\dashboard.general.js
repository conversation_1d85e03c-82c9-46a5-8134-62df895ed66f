/**
 * Created by XINRAN on 14-4-16.
 */

var DataEnrollment;
var DataEnrollmentColl = new Backbone.Collection;
var DataTarget;
var currentGroupBy = 'program';


function getData(){

    var progress = 0;
    var step = ( 100 *( 1 / getDataRequest.length) ).toFixed(2);
    var complete = 0;
    var container;
    $('#overall-progress .progress-bar').css('width','0').html('0%');
    $('#overall-progress').modal({keyboard: false,backdrop:'static'});

    for(var i=0; i<getDataRequest.length;i++){
        var varName = getDataRequest[i].varName;
        $.ajax({
            type: "POST",
            url: getDataRequest[i].url,
            data: getDataRequest[i].data,
            dataType: 'json',
            async: true,
            success: function(data) {
                progress = parseInt(progress) + parseInt(step);
                complete +=1;
                if(progress > 100) progress = 100;
                if(complete == getDataRequest.length) progress = 100;

                if(data.type == 'replace'){
                    container = $('#'+data.replaceholder);
                    container.html(data.data);
                    container.hide();
                }else{
                    eval(data.cmd);
                }

                $('#overall-progress .progress-bar').css('width',progress+'%').html(progress+'%');
                if(progress == 100){
                    $('#overall-progress .hint').html('Preparing Data...');
                    showData();
                    container.show();
                }
            }
        });
    }
}

var singleBranchView;
var allBranchView;

function showData(){
    var total = 0;
    singleBranchView = Backbone.View.extend({
        template: _.template($('#item-branch-template').html()),
        tagName: 'div',
        className: 'col-md-2 branch-item',
        initialize: function(){

        },
        render: function(){
            var _g = branchTypeMapping[branchObjs[this.model.get('id')].group];
            this.$el.html( this.template(this.model.attributes) );
            this.$el.attr('group', _g );
            this.$el.attr('currentCount', this.model.get('data')[0] );
            this.$el.attr('branchId', this.model.get('id') );
            this.$el.attr('opentime', branchObjs[this.model.get('id')].opentime );
            this.$el.attr('city', branchObjs[this.model.get('id')].city );
            this.$el.attr('progress', getPercent(this.model.get('data')[0], DataTarget[this.model.get('id')]).replace('%',''));

            var _child = this.$el.children('div.panel:first-child');
            _child.removeClass('panel-default');
            if(_g == 'IA'){
                _child.addClass('panel-success');
            }else if(_g == 'IBS'){
                _child.addClass('panel-danger');
            }else if(_g == 'MIK'){
                _child.addClass('panel-info');
            }
            return this;
        }
    });

    allBranchView = Backbone.View.extend({
        el: $('#branch-list'),
        initialize: function(){
            this.listenTo(DataEnrollmentColl, 'reset', this.addAll);
            DataEnrollmentColl.reset(DataEnrollment);
        },
        addAll: function(){
            DataEnrollmentColl.each(this.addOne, this);
        },
        addOne: function(branchModel){
            var item = new singleBranchView({model:branchModel});
            this.$el.append(item.render().el);
            total += branchModel.get('data')[0];
        }
    });

    var pageBranchList = new allBranchView;

    groupBy(currentGroupBy);
    $('label.action-group-by').removeClass('active');
    $('label.action-group-by[data|="'+currentGroupBy+'"]').addClass('active');

    $('.action-group-by').click(function(){groupBy($(this).attr('data'))});
    $('#reload-general-page').click(function(){getData();});

    $('#totleNumber').html(total);

    $( "#specified-date" ).datepicker({
        dateFormat: 'yy-mm-dd',
        maxDate: todayDate,
        onSelect: function(date){
            specifiedDate = date;
            loadGenealInfo(specifiedDate);
        }
    });
    $('#specified-date').val(specifiedDate);

    setTimeout("js:$('#overall-progress').modal('hide')", 500);
}

/**
 * 将校园分组显示
 * @param type
 */
function groupBy(type){
    $('#branch-list-by-group').html('');

    var program = [];
    var programText = [];
    var subTotal = {};

    switch (type){
        case 'program':
            programText = ['IA','IBS','MIK'];
            break;
        case 'opentime':
            programText = ['New Campus (0 - 2Y)','New Campus (3 - 4Y)','Mature Campus (4Y+)'];
            break;
        case 'city':
            break;
        case 'progress':
            programText = ['<80%','80%~100%','≥100%'];
            break;
    }
    $('#branch-list .branch-item').each(function(){
        switch (type){
            case 'program':
                var _g = $(this).attr('group');
                var _p = (_g =='IA' )? 0 : ((_g =='IBS' )? 1: 2);
                var _text = programText[_p];
                break;
            case 'opentime':
                var _g = $(this).attr('opentime');
                var _p = (parseInt(_g) < 2 )? 0 : ((parseInt(_g) < 4 )? 1: 2);
                var _text = programText[_p];
                break;
            case 'city':
                var _g = $(this).attr('city');
                var _p = _g;
                var _text = cityObjs[_p].en+ ' ('+cityObjs[_p].cn+')';
                break;
            case 'progress':
                var _g = parseFloat($(this).attr('progress'));
                var _p = (_g < 80) ? 0 : ( (_g<100)? 1 : 2 );
                var _text = programText[_p];

                break;
        }
        if(!in_array(_p, program)){
            subTotal[_p] = 0;
            program.push(_p);
            var ele = $('<div class="col-sm-12 col-md-4 col-lg-4 groupwrap"></div>').attr('group',_p).hide();
            ele.html('<h4>'+_text+'<span class="pull-right"></span></h4>');
            $('#branch-list-by-group').append(ele);
        }
        subTotal[_p] += parseInt($(this).attr('currentcount'));

        var _ele = $(this).clone();
        _ele.removeClass('col-md-2').addClass('col-sm-4 col-md-12 col-lg-6');
        $('#branch-list-by-group .groupwrap[group|='+_p+']').append(_ele);
        $('#branch-list-by-group .groupwrap[group|='+_p+'] h4 span').html( subTotal[_p] );
    });

    currentGroupBy = type;
    $('.groupwrap h4').prepend('<span class="glyphicon glyphicon-stats"></span>');

    $('#branch-list-by-group div.groupwrap').tsort({order:'asc',attr:'group'});
    $('.branch-title').css('cursor','pointer').click(function(){showCampus($(this).attr('branchid'))});
    $('#branch-list-by-group .groupwrap').each(function(){
        $(this).fadeIn(800);
    });
}

function showCampus(campusId){
    var _container = $('#container-campus-'+campusId);
    currentBranchId = campusId;
    if(_container.length == 0){
        var _content = _.template($('#data-campus-template').html());
        $('body').append(_content({branchId:campusId}));
        $('#container-campus-'+campusId+' .list-group-item').click(function(){loadCampusInfo({schoolid:campusId},$(this))});;
    }
    $('#container-general').fadeOut(100, function(){$('#container-campus-'+campusId).fadeIn(500);});
}

function showGeneral(){
    history.back();return;
    $('.dashboard-page').hide();
    $('#container-general').fadeIn(500);
}

function loadCampusData(dataType,branchId,startYear){

}