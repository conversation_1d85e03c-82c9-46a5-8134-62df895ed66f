<?php

/**
 * This is the model class for table "ivy_reports_data".
 *
 * The followings are the available columns in table 'ivy_reports_data':
 * @property integer $id
 * @property integer $class_id
 * @property integer $child_id
 * @property integer $startyear
 * @property integer $semester
 * @property string $status
 * @property integer $updated_user
 * @property integer $updated
 * @property string $custom_teacher
 * @property integer $uptime
 */
class ReportsData extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_reports_data';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('class_id, child_id, startyear, semester, updated_user, updated', 'required'),
			array('class_id, child_id, startyear, semester, pdf_file, updated_user, updated, custom, uptime', 'numerical', 'integerOnly'=>true),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, class_id, child_id, startyear, semester, pdf_file, custom_pdf, status, updated_user, updated', 'safe', 'on'=>'search'),
            array('status, pdf_file, custom_pdf, attendance_ext, lang, custom_teacher', 'safe'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'ext' => array(self::HAS_MANY, 'ReportsDataExtra', 'data_id'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'class_id' => 'Class',
			'child_id' => 'Child',
			'startyear' => 'Startyear',
			'semester' => 'Semester',
			'status' => 'Status',
			'updated_user' => 'Updated User',
			'updated' => 'Updated',
			'custom_teacher' => 'Custom Teacher',
			'uptime' => 'Uptime',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('class_id',$this->class_id);
		$criteria->compare('child_id',$this->child_id);
		$criteria->compare('startyear',$this->startyear);
		$criteria->compare('semester',$this->semester);
		$criteria->compare('status',$this->status);
		$criteria->compare('pdf_file',$this->pdf_file);
		$criteria->compare('updated_user',$this->updated_user);
		$criteria->compare('updated',$this->updated);
		$criteria->compare('uptime',$this->uptime);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return ReportsData the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}
