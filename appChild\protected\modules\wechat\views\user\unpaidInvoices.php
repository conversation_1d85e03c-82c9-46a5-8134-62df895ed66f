<style>
	#total_bus {
    text-align: right;
	}
	.align-items{
		align-items: flex-start;
	}
	.font16{
		font-size:16px
	}
	.mt8{
		margin-top:8px
	}
	.payBtn{
		margin-bottom:20px;
    	padding-top: 20px;
		margin-top:0
	}
	.mt1em{
		margin-top:1em
	}
    .the-page-btn {
        display: flex;
        margin-bottom: 15px;
        justify-content: space-between;
    }
    .the-page-btn a {
        margin-top: 0 !important;
        font-size: 16px;
        border-radius: 4px;
        border: 1px solid #EBEDF0;
        background-color: #fff;
        height: 44px;
        line-height: 44px;
        color: #4D88D2;
        margin-left: 0;
        margin-right: 0;
        padding-left: 18px;
        padding-right: 18px;
        width: 48%;
    }
    .the-page-btn a:after {
        content: " ";
        display: inline-block;
        -webkit-transform: rotate(45deg);
        transform: rotate(45deg);
        height: 8px;
        width: 8px;
        border-width: 2px 2px 0 0;
        border-color: #4D88D2;
        border-style: solid;
        position: relative;
        top: -2px;
        margin-left: .8em;
        border-radius: 0;
        box-sizing: content-box;
    }
</style>
<div class="cell">
	<div class="bd">
		<div class="weui_cells_title"><?php echo Yii::t('navigations', 'Make Payment'); ?></div>
		<div class="weui_cells weui_cells_checkbox">
			<?php
			//选择孩子
			$this->widget('ext.wechat.ChildSelector', array(
				'childObj' => $childObj,
				'myChildObjs' => $this->myChildObjs,
				'jumpUrl' => 'unpaidInvoices',
			));
			?>
		</div>
		<?php if (!empty($unPaidList) || !empty($busUnPaidList)) : ?>
			<?php if (!empty($busUnPaidList)) : ?>
				<div class="weui_cells weui_cells_checkbox bus_list mt1em">
					<form action="<?php echo $this->createUrl('paymentConfirm'); ?>" method="post">
						<?php foreach ($busUnPaidList as $invoiceId => $unPaid) : ?>
							<label class="weui_cell align-items weui_check_label weui_media_box" for="invoice_<?php echo $invoiceId ?>">
								<div class="weui_cell_hd">
									<input type="checkbox" class="weui_check" data-amount=<?php echo $unPaid['dueAmount']; ?> data-school=<?php echo $unPaid['schoolid']; ?> name="invoice[<?php echo $invoiceId; ?>]" id="invoice_<?php echo $invoiceId; ?>" checked="checked">
									<i class="weui_icon_checked"></i>
								</div>
								<div class="weui_cell_bd weui_cell_primary">
									<p class='font16'><?php echo $unPaid['title'] . '(' . $unPaid['amount'] . ')'; ?></p>
									<p class="weui_media_desc mt8"><?php echo Yii::t('payment', 'Amount Due') . ': ' . $unPaid['dueAmount'] ?></p>
									<p class="weui_media_desc mt8"><?php echo Yii::t('payment', 'Amount Paid') . ': ' . $unPaid['paidAmount'] ?></p> 
								</div>
							</label>
						<?php endforeach; ?>

						<div class="weui_cells_title"> <?php echo Yii::t('payment', 'Total'); ?></div>
						<div class=" weui_cells_form">
							<div class="weui_cell">
								<div class="weui_cell_hd"><label class="weui_label" style="width: 9em;"><?php echo Yii::t('payment', 'Payment Amount'); ?></label></div>
								<div class="weui_cell_bd weui_cell_primary">
									<input id="total_bus" name="total_bus" class="weui_input" type="number">
									<input id="totalFee_bus" name="totalFee_bus" class="weui_input" hidden="hidden">
								</div>
							</div>
						</div>
						<div class="weui_btn_area weui_cells payBtn">
							<?php if ($wechatPay) : ?>
								<a class="weui_btn weui_btn_primary" href="javascript:void(0)" id="wechatPay_bus"><?php echo Yii::t('payment', 'Wechat Pay'); ?></a>
							<?php else : ?>
								<a class="weui_btn weui_btn_primary" href="javascript:void(0)" id="wechatPay_bus"><?php echo Yii::t('payment', 'Wechat Pay'); ?></a>
								<!-- <a class="weui_btn weui_btn_primary weui_btn_disabled" href="javascript:void(0)"><?php //echo Yii::t('wechat', 'We do not support wechat payment yet.'); 
																														?></a> -->
							<?php endif; ?>
						</div>
					</form>
				</div>
			<?php endif; ?>
			<?php if (!empty($unPaidList)) : ?>
				<div class="weui_cells weui_cells_checkbox invoice_list mt1em">
					<form action="<?php echo $this->createUrl('paymentConfirm'); ?>" method="post">
						<?php foreach ($unPaidList as $invoiceId => $unPaid) : ?>
							<label class="weui_cell align-items  weui_check_label weui_media_box" for="invoice_<?php echo $invoiceId ?>">
								<div class="weui_cell_hd">
									<input type="checkbox" class="weui_check" data-amount=<?php echo $unPaid['dueAmount']; ?> data-school=<?php echo $unPaid['schoolid']; ?> name="invoice[<?php echo $invoiceId; ?>]" id="invoice_<?php echo $invoiceId; ?>" checked="checked">
									<i class="weui_icon_checked"></i>
								</div>
								<div class="weui_cell_bd weui_cell_primary">
									<p class='font16'><?php echo $unPaid['title'] . '(' . $unPaid['amount'] . ')'; ?></p>
									<p class="weui_media_desc mt8"><?php echo Yii::t('payment', 'Amount Due') . ': ' . $unPaid['dueAmount'] ?></p>
									<p class="weui_media_desc mt8"><?php echo Yii::t('payment', 'Amount Paid') . ': ' . $unPaid['paidAmount'] ?></p> 
								</div>
							</label>
						<?php endforeach; ?>

						<div class="weui_cells_title"> <?php echo Yii::t('payment', 'Total'); ?></div>
						<div class=" weui_cells_form">
							<div class="weui_cell">
								<div class="weui_cell_hd"><label class="weui_label" style="width: 9em;"><?php echo Yii::t('payment', 'Payment Amount'); ?></label></div>
								<div class="weui_cell_bd weui_cell_primary">
									<input id="total" name="total" class="weui_input" type="number">
									<input id="totalFee" name="totalFee" class="weui_input" hidden="hidden">
								</div>
							</div>
						</div>
						<div class="weui_btn_area  weui_cells payBtn">
							<?php if ($wechatPay) : ?>
								<a class="weui_btn weui_btn_primary" href="javascript:void(0)" id="wechatPay"><?php echo Yii::t('payment', 'Wechat Pay'); ?></a>
							<?php else : ?>
								<a class="weui_btn weui_btn_primary" href="javascript:void(0)" id="wechatPay"><?php echo Yii::t('payment', 'Wechat Pay'); ?></a>
								<!-- <a class="weui_btn weui_btn_primary weui_btn_disabled" href="javascript:void(0)"><?php //echo Yii::t('wechat', 'We do not support wechat payment yet.'); 
																														?></a> -->
							<?php endif; ?>
						</div>
					</form>
				</div>
			<?php endif; ?>
			<div class="mt1em weui_cells" >
				<div class='weui_cells_title' style='padding:16px'>
					<p style="padding-bottom: 8px;font-size:17px;color:#000"><?php echo Yii::t('payment', 'Exceeding the payment limit?'); ?></p>
					<ol style="margin-left: 15px;">
						<li style="padding-bottom: 8px;"><?php echo Yii::t('payment', 'If there are multiple invoices, please try paying each invoice separately.'); ?></li>
						<li style="padding-bottom: 8px;"><?php echo Yii::t('payment', 'If an invoice exceeds the payment limit, please try splitting the invoice and make the payments.'); ?></li>
						<li style="padding-bottom: 8px;"><?php echo Yii::t('payment', 'Please contact us if you are unable to pay via Wechat.'); ?></li>
					</ol>
				</div>
			</div>
		<?php else : ?>
			<div class="weui_msg">
				<div class="weui_icon_area"><i class="weui_icon_msg weui_icon_info"></i></div>
				<div class="weui_text_area">
					<p class="weui_msg_desc"><?php echo Yii::t('payment', 'All invoices have been paid.  Thank you very much!') ?></p>
				</div>
			</div>
		<?php endif; ?>
        <?php if ($this->wechatUser->account == 'ds'):?>
        <div class="weui_btn_area the-page-btn">
            <a href="https://open.weixin.qq.com/connect/oauth2/authorize?appid=wxb9c6c538ebf7efa2&redirect_uri=http://m.ivykids.cn/wechat/me/unpaid-invoice%3flang%3d&response_type=code&scope=snsapi_userinfo&state=mmx#wechat_redirect" class="weui_btn weui_btn_plain_default"><?php echo Yii::t('payment', 'ASA Invoices') ?></a>
            <a href="weixin://dl/business/?appid=wx3690b00fdbbb3d53&path=pages/index/index" class="weui_btn weui_btn_plain_default"><?php echo Yii::t('payment', 'Camp Invoices') ?></a>
        </div>
        <?php endif;?>
	</div>
</div>
<script>
	getTotal();
	$('.invoice_list .weui_cell_hd input').bind('change', function() {
		getTotal();
	});
	//计算商品总额
	function getTotal() {
		var total = 0;
		var checkedNum = 0;
		var inputEle = $('#total');
		var inputHidden = $('#totalFee');
		$('.invoice_list input').each(function(i, val) {
			if (val.checked) {
				total += Number($(val).data('amount').toString().replace(',', ''));
				checkedNum++;
			}
		});
		if (checkedNum == 1) {
			inputEle.removeAttr('disabled');
		} else {
			inputEle.attr('disabled', 'disabled');
		}
		total = total.toFixed(2);
		inputEle.val(total);
		inputHidden.val(total);
	}
	
	$('#total').bind('change', function() {
		$('#totalFee').val($(this).val());
	});
	//发起支付
	$('#wechatPay').bind('click', function() {
		var childid = <?php echo $childObj->childid; ?>;
		if (checkMoney()) {
			$(this).addClass('weui_btn_disabled');
			var payType = '<?php echo $payType; ?>';
			if (payType == 'JSAPI') {

				var invoiceInfo = new FormData(document.querySelector(".invoice_list form"));

				$('.invoice_list form input:checked').each(function(i, elem) {
					invoiceInfo.append($(elem).attr('name'), $(elem).val());
				});
				invoiceInfo.append('totalFee', $('#totalFee').val());
				$.ajax({
					url: '<?php echo $this->createUrl('paymentConfirm'); ?>',
					type: 'POST',
					data: invoiceInfo,
					dataType: 'json',
					processData: false,
					contentType: false,
					success: function(data) {
						jsPay(data);
					}
				});
			} else {
				$('.invoice_list form').submit();
			}
		}
	});

	// *************************************** 泉发校车 ******************************
	//计算商品总额
	getTotalBus();
	$('.bus_list .weui_cell_hd input').bind('change', function() {
		getTotalBus();
	});
	function getTotalBus() {
		var total = 0;
		var checkedNum = 0;
		var inputEle = $('#total_bus');
		var inputHidden = $('#totalFee_bus');
		$('.bus_list input').each(function(i, val) {
			if (val.checked) {
				total += Number($(val).data('amount').toString().replace(',', ''));
				checkedNum++;
			}
		});
		if (checkedNum == 1) {
			inputEle.removeAttr('disabled');
		} else {
			inputEle.attr('disabled', 'disabled');
		}
		total = total.toFixed(2);
		inputEle.val(total);
		inputHidden.val(total);
	}
	
	$('#total_bus').bind('change', function() {
		$('#totalFee_bus').val($(this).val());
	});
	//发起支付
	$('#wechatPay_bus').bind('click', function() {
		var childid = <?php echo $childObj->childid; ?>;
		if (checkMoneyBus()) {
			$(this).addClass('weui_btn_disabled');
			var payType = '<?php echo $payType; ?>';
			if (payType == 'JSAPI') {

				var invoiceInfo = new FormData(document.querySelector(".bus_list form"));

				$('.bus_list form input:checked').each(function(i, elem) {
					invoiceInfo.append($(elem).attr('name'), $(elem).val());
				});
				invoiceInfo.append('totalFee', $('#totalFee_bus').val());
				$.ajax({
					url: '<?php echo $this->createUrl('paymentConfirm'); ?>',
					type: 'POST',
					data: invoiceInfo,
					dataType: 'json',
					processData: false,
					contentType: false,
					success: function(data) {
						jsPay(data);
					}
				});
			} else {
				$('.bus_list form').submit();
			}
		}
	});

	function jsPay(jsPayInfo) {
		WeixinJSBridge.invoke(
			'getBrandWCPayRequest',
			jsPayInfo,
			function(res) {
				if (res.err_msg == "get_brand_wcpay_request:ok") {
					location.href = '<?php echo $this->createUrl('paymentResult', array('result' => 'success')); ?>';
				} else {
					// 错误提示
					// WeixinJSBridge.log(res.err_msg);
					// var childid = <?php echo $childObj->childid; ?>;
					// if (childid == 2815) {
					// 	alert(res.err_code+res.err_desc+res.err_msg);
					// }
					location.href = '<?php echo $this->createUrl('paymentResult', array('result' => 'fail')); ?>';
				}
			}
		);
	}
	// 验证支付金额合法性
	function checkMoney() {
		var pay = $('#total').val();
		if ($('.invoice_list input:checked').length < 1) {
			showTips('<?php echo Yii::t('wechat', 'Payment Record'); ?>', '<?php echo Yii::t("payment", "Please select at least one invoice."); ?>');
			return false;
		} else {
			var schools = [];
			var total = 0;
			$('.invoice_list input').each(function(i, val) {
				if (val.checked) {
					schools.push($(val).data('school'));
					total += Number($(val).data('amount').toString().replace(',', ''));
				}
			});
			for (var i = schools.length - 1; i >= 1; i--) {
				if (schools[i] != schools[i - 1]) {
					showTips('<?php echo Yii::t('wechat', 'Payment Record'); ?>', '<?php echo Yii::t("payment", "不能同时支付两个学校的账单"); ?>');
					return false;
				}
			}
			if (pay > total) {
				showTips('<?php echo Yii::t('wechat', 'Payment Record'); ?>', '<?php echo Yii::t("payment", "Your payment amount cannot exceed the total amount of the invoice(s)!"); ?>');
				return false;
			}
		}
		if (pay < 0.01) {
			showTips('<?php echo Yii::t('wechat', 'Payment Record'); ?>', '<?php echo Yii::t("payment", "Please enter a valid payment amount!"); ?>');
			return false;
		}
		return true;
	}

	// 验证支付金额合法性
	function checkMoneyBus() {
		var pay = $('#total_bus').val();
		if ($('.bus_list input:checked').length < 1) {
			showTips('<?php echo Yii::t('wechat', 'Payment Record'); ?>', '<?php echo Yii::t("payment", "Please select at least one invoice."); ?>');
			return false;
		} else {
			var schools = [];
			var total = 0;
			$('.bus_list input').each(function(i, val) {
				if (val.checked) {
					schools.push($(val).data('school'));
					total += Number($(val).data('amount').toString().replace(',', ''));
				}
			});
			for (var i = schools.length - 1; i >= 1; i--) {
				if (schools[i] != schools[i - 1]) {
					showTips('<?php echo Yii::t('wechat', 'Payment Record'); ?>', '<?php echo Yii::t("payment", "不能同时支付两个学校的账单"); ?>');
					return false;
				}
			}
			if (pay > total) {
				showTips('<?php echo Yii::t('wechat', 'Payment Record'); ?>', '<?php echo Yii::t("payment", "Your payment amount cannot exceed the total amount of the invoice(s)!"); ?>');
				return false;
			}
		}
		if (pay < 0.01) {
			showTips('<?php echo Yii::t('wechat', 'Payment Record'); ?>', '<?php echo Yii::t("payment", "Please enter a valid payment amount!"); ?>');
			return false;
		}
		return true;
	}
</script>