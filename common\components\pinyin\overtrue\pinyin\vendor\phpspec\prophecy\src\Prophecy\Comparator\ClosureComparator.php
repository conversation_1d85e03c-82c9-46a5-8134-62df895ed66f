<?php

/*
 * This file is part of the Prophecy.
 * (c) <PERSON> <<EMAIL>>
 *     <PERSON><PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Prophecy\Comparator;

use <PERSON><PERSON><PERSON><PERSON><PERSON>\Comparator\Comparator;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\Comparator\ComparisonFailure;

/**
 * Closure comparator.
 *
 * <AUTHOR> <<EMAIL>>
 */
final class ClosureComparator extends Comparator
{
    public function accepts($expected, $actual)
    {
        return is_object($expected) && $expected instanceof \Closure
            && is_object($actual) && $actual instanceof \Closure;
    }

    public function assertEquals($expected, $actual, $delta = 0.0, $canonicalize = false, $ignoreCase = false, array &$processed = array())
    {
        if ($expected !== $actual) {
            throw new ComparisonFailure(
                $expected,
                $actual,
                // we don't need a diff
                '',
                '',
                false,
                'all closures are different if not identical'
            );
        }
    }
}
