<?php
/**
 * IBS, MIK 第一个学期报告的版本
 */

return array(
	'pages'=>array(
		'1' => array(
			'blocks' => array(
				'childinfo' => array(
					'desc' => '孩子基本信息、班级、老师、头像',
					'title' => '基本信息',
					'readonly'=>'readonly',
					'caption'=>''
				),
				'works' => array(
					'desc' => '孩子作品',
					'title' => '孩子作品',
					'caption' => '孩子作品',
					'content' => array('photo'),
					'photoToOther' => true, //照片分配给其他孩子相同项目
				)
			)
		),
		'2' => array(
			'blocks' => array(
				'strength1' => array(
					'desc' => '列举两个孩子的优势智能，选取合适的图片并做文字归纳',
					'title' => '优势 Strengths',
					'content' => array('photo', 'learningDomain', 'textArea'),
				),
				'strength2' => array(
					'desc' => '列举两个孩子的优势智能，选取合适的图片并做文字归纳',
					'title' => '优势 Strengths',
					'content' => array('photo', 'learningDomain', 'textArea'),
				),
			)
		),
		'3' => array(
			'blocks' => array(
				'interest' => array(
					'desc' => '列举一个孩子的兴趣爱好，选取合适的图片并做文字归纳',
					'title' => '兴趣 Interest',
					'content' => array('photo','textArea')
				),
				'comments' => array(
					'desc' => '概述 General Comments',
					'title' => '概述 General Comments',
					'content' => array('textArea')
				)
			)
		),
		'4' => array(
			'blocks' => array(
				'backcover' => array(
					'desc' => '封底图片，班级合影为佳',
					'title' => '',
					'photoToOther' => true, //照片分配给其他孩子相同项目
					'content' => array('photo')
				)
			)
		),
	)
);
 
?>
