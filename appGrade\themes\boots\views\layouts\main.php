<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1.0, user-scalable=no">

    <title><?php echo CHtml::encode($this->pageTitle); ?></title>

    <!-- Bootstrap core CSS -->
    <link href="<?php echo Yii::app()->theme->baseUrl; ?>/css/bootstrap.min.css" rel="stylesheet">
    <link href="<?php echo Yii::app()->theme->baseUrl; ?>/css/main.css?t=1" rel="stylesheet">

    <?php Yii::app()->clientScript->registerCoreScript('jquery'); ?>
    <script src="<?php echo Yii::app()->theme->baseUrl; ?>/js/bootstrap.min.js"></script>
    <script type="text/javascript" src="<?php echo Yii::app()->themeManager->baseUrl; ?>/base/js/callback.js"></script>

    <?php
    if(Yii::app()->params['baiduStats'])
        $this->renderPartial('//statsScripts/baiduStats_head');
    ?>

</head>
<body>
<div id="wrap">
    <div class="navbar navbar-fixed-top navbar-inverse" role="navigation">
        <div class="container">
            <div class="navbar-header">
                
                <?php if(Yii::app()->params['enableSideBar']):?>
                <div class="pull-left visible-xs">
                  <button type="button" class="navbar-toggle" data-toggle="offcanvas" data-target=".sidebar-offcanvas">
                    <span class="sr-only">Toggle navigation</span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>                  
                  </button>
                </div>
                <?php endif;?>
            
                <?php if(1):?>
                <button type="button" class="navbar-toggle" data-toggle="collapse" data-target=".navbar-collapse">
                    <span class="sr-only">Toggle navigation</span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
                <?php endif;?>
                
                <?php echo CHtml::link(Yii::t('global','Club@IvySchools'), array('//club/default/home'), array('class'=>'navbar-brand'));?>
            </div>
            
            <?php if(1):?>
            <div class="collapse navbar-collapse">
                <?php
                $this->widget('zii.widgets.CMenu', array(
                    'items'=>$this->menu,
                    'htmlOptions'=>array('class' => 'nav navbar-nav'),
                    'encodeLabel'=>false,
                ));
                ?>
                <ul class="nav navbar-nav navbar-right">
                    <?php if(Yii::app()->user->isGuest):?>
                        <li>
                            <?php echo CHtml::link(
                                Yii::t('user','用户'),
                                'javascript:void(0);',
                                array('onClick'=>'openLoginModel();'));?></li>
                    <?php else:?>
                        <?php $userText = (!Yii::app()->user->isGuest && (Yii::app()->user->getState('clubUsername'))) ?
                            Yii::app()->user->getState('clubUsername') :
                            Yii::t('club','用户');
                        ?>
                        <li>
                            <a href="#" class="dropdown-toggle" data-toggle="dropdown"><?php echo CHtml::encode($userText); ?> <b class="caret"></b></a>
                            <ul class="dropdown-menu">
                                <li>
                                    <?php echo CHtml::link(
                                        Yii::t('user','修改用户名'),
                                        'javascript:void(0);',
                                        array('onClick'=>'openClubUnameModel();'));?></li>
                                <li>
                                    <?php echo CHtml::ajaxLink(Yii::t("global", '退出登录' ),
                                        $this->createUrl("//site/logout",
                                            array("lang"=>"en_us")
                                        ),
                                        array(
                                            "dataType"=>"json",
                                            "success"=>"function(data, textStatus, jqXHR){callback(data,'reload')}",
                                        ))
                                    ?>
                                </li>
                            </ul>
                        </li>
                    <?php endif;?>
                    <?php // 双语切换；暂时注释 $this->renderPartial('//layouts/_langSwitch');?>
                </ul>
            </div><!-- /.nav-collapse -->
            <?php endif;?>
        </div><!-- /.container -->
    </div><!-- /.navbar -->
    <div class="container">
    
        <?php echo $content; ?>
        
        <footer>
          <p>© IvySchools Club <?php echo date('Y');?></p>
        </footer>
    
    </div>    
</div>

<?php if(Yii::app()->params['enableSideBar']):?>
<script type="text/javascript">
    $(document).ready(function() {
      $('[data-toggle=offcanvas]').click(function() {
        $('.row-offcanvas').toggleClass('active');
      });
    });
</script>
<?php endif;?>


<?php
    if(Yii::app()->user->isGuest): //未登录
        $this->renderPartial('//layouts/_userLoginModal');
    else://(!Yii::app()->user->getState('clubUsername')):
        $this->renderPartial('//layouts/_userClubNameModal');
    endif;
?>

<?php
if(Yii::app()->params['baiduStats'])
    $this->renderPartial('//statsScripts/baiduStats_common');
?>

</body>
</html>