<?php

class DefaultController extends TeachingBasedController
{
	public $defaultAction = 'index';
	
	public $currentSchoolId; 	//当前选中校园ID
	public $currentClassId;		//当前选中班级ID
	public $currentWeeknum;		//当前选中周号（第N周）
	public $currentCalendarId;	//当前所选班级的YID
	public $currentStartYear;	//当前所选班级的开始年
	public $task=null;			//当前要操作的教学任务

	public function actionIndex()
	{
		$this->historyStartYear = Yii::app()->request->getParam('historyStartYear',0);
		parent::actionSchoolClasses($this->historyStartYear);
		
        $this->currentSchoolId = Yii::app()->request->getParam('schoolid', '');
        $this->currentClassId = Yii::app()->request->getParam('classid', 0);
        $this->currentWeeknum = Yii::app()->request->getParam('weeknum', $this->currentWeek);

		$this->showHistory = Yii::app()->request->getParam('showHistory',0);
        $this->task = Yii::app()->request->getParam('task', null);


		//查看历史数据
		if($this->showHistory && $this->historyStartYear){
		
		}
		
		//默认查看当前数据
		if(!empty($this->currentSchoolId)){
			$this->currentCalendarId = $this->calendarIds[$this->currentSchoolId];
			$this->currentStartYear = date('Y', $this->schoolYears[$this->currentCalendarId]['starttime']);
		}
		
		
		if(!empty($this->currentSchoolId) && !empty($this->currentClassId)){
			if(
				!in_array($this->currentSchoolId, array_keys($this->calendarIds)) ||
				!in_array($this->currentClassId, array_keys($this->schoolClasses[$this->currentSchoolId]['items']))
			){
			
				print_r($this->currentSchoolId);
				print_r($this->currentClassId);
				print_r($this->calendarIds);
				print_r($this->schoolClasses);
				//$this->redirect('/teaching/default/schoolClasses');
			}
		}
		
		if(in_array($this->task, array_keys($this->teachingTasks))){
			switch($this->task){
				//case 'mediaupload':
				default:
					$task = 'task'  . ucfirst($this->task);
					//echo $this->task;
					$this->$task();
					Yii::app()->end();
				break;
			}
		
		}
		$this->render('test2');
	}
	
	public function taskMediaupload(){
		$serverConfig = OA::getMediaServerConfig();
		
		$mediaServer = OA::getMediaServer($this->currentClassId, $this->currentSchoolId, $serverConfig);
		$options = array();
				
		//主键 全小写
		$options['ext-param'] = array(
			'branchid' => $this->currentSchoolId,
			'classid' => $this->currentClassId,
			'weeknumber' => $this->currentWeeknum,
			'yid' => $this->currentCalendarId,
			'startyear' => $this->currentStartYear,
			'uid' => Yii::app()->user->getId(),
			'serverid' => $mediaServer['id'],
			//'mediaServer' => $mediaServer
		);
		
		$options['notify-url'] = Yii::app()->params['mediaNotifyUrl'];
		
		Yii::app()->clientScript->registerScriptFile( Yii::app()->theme->baseUrl.'/swfupload/swfupload.js');
		Yii::app()->clientScript->registerScriptFile( Yii::app()->theme->baseUrl.'/swfupload/swfupload.queue.js');
		Yii::app()->clientScript->registerScriptFile( Yii::app()->theme->baseUrl.'/swfupload/js/wu_fileprogress.js');
		Yii::app()->clientScript->registerScriptFile( Yii::app()->theme->baseUrl.'/swfupload/js/wu_handlers.js?t='.time());
		Yii::app()->clientScript->registerCssFile( Yii::app()->theme->baseUrl.'/css/swfupload.css');
		Yii::app()->clientScript->registerCssFile( Yii::app()->theme->baseUrl.'/css/teaching.css');	
		$this->render('upload', array('options'=>$options, 'mediaServer'=>$mediaServer));
	}
	
	public function taskMediamgt(){
		$learningDomains = Term::model()->getLdList($useLang=true);
		$nameList = $this->getNameList($this->currentClassId);

		Yii::import('common.models.portfolio.*');
		ChildMedia::setStartYear($this->currentStartYear);
		ChildMediaLinks::setStartYear($this->currentStartYear);
		
		$criteria = new CDbCriteria;
		$criteria->compare('classid', $this->currentClassId);
		$criteria->compare('weeknum', $this->currentWeeknum);
		
		$count = ChildMedia::model()->count($criteria);
		$pages = new CPagination($count);
		
		$pages->pageSize = 20;
		$pages->applyLimit($criteria);
		
		$medias = ChildMedia::model()->with(array('weeklyLinks'=>array(
			'condition' => 'category="week" AND weeklyLinks.classid=:classid AND weeklyLinks.weeknum=:weeknum',
			'params' => array(
				':classid' => $this->currentClassId,
				':weeknum' => $this->currentWeeknum
			)
		)))->findAll($criteria);
		
		$mediaList = $this->getFormatedData($medias);
		Yii::app()->clientScript->registerCssFile( Yii::app()->theme->baseUrl.'/css/teaching.css');
		Yii::app()->clientScript->registerCssFile( Yii::app()->theme->baseUrl.'/css/jcrop.css');
		
		if(Yii::app()->request->isAjaxRequest){
			echo $this->renderPartial('_media_zone', array("pages"=>$pages, 'mediaList'=>$mediaList,  'nameList'=>$nameList));
		}else{
			$this->render('media', array('pages'=>$pages, 'mediaList'=>$mediaList, 'nameList'=>$nameList, 'learningDomains'=>$learningDomains ));
		}
	}
	
	private function getFormatedData($medias){
		$serverConfigs = OA::getMediaServerConfig();
		$mediaList = array();
		foreach($medias as $media){
			$links = array();
			if(count($media->weeklyLinks)){
				foreach($media->weeklyLinks as $linkObj){
					$links[] = $linkObj->childid;
				}
			}
			
			$tags = array();
			if($media->tag != ""){
				preg_match_all('/sc'.$this->currentClassId.'_(\d+)_e/', $media->tag, $out1);
				preg_match_all('/sl_(\d+)_e/', $media->tag, $out2);
				preg_match_all('/sf_([^_]*)_e/', $media->tag, $out3);
				
				$tags['childid'] = ($out1 && isset($out1[1])) ? $out1[1]: array();
				$tags['learndomain'] = ($out2 && isset($out2[1])) ? $out2[1]: array();
				$freeword = ($out3 && isset($out3[1])) ? $out3[1][0]: '';
				$tags['freeword'] = CHtml::encode($freeword);
			}
			
			$subPath = $media->getSubPath();
			
			$mediaList[$media->id] = array(
			//为配合backbone，必须要从0开始，不能以孩子ID为主键
			//$mediaList[] = array(
				'pid' => $media->id,
				'sid' => $media->server,
				'year' => $this->currentStartYear,
				'securityCode' => md5(sprintf('%s&%s&%s', Yii::app()->user->getId(), $media->id, $serverConfigs['servers'][$media->server]['securityKey'])),
				'thumb' => $serverConfigs['servers'][$media->server]['url'] . $media->getSubPath() . 'thumbs/' . $media->filename,
				'links' => $links,
				'tags' => $tags
			);
		}
		return $mediaList;
	}

    public function TaskSchedule()
    {
        Yii::import('common.models.portfolio.*');
        
        $schoolid = Yii::app()->request->getParam('schoolid', '');
        $classId = Yii::app()->request->getParam('classid', 0);
        $class_model = IvyClass::model()->findByPk($classId);
        
        $sArr = explode('|', $class_model->periodtime);
		
		$classTime['start'] = strtotime($sArr[0].':'.$sArr[1]);
		$classTime['end'] = strtotime($sArr[2].':'.$sArr[3]);
				
        $pids = array(8=>8); //日常活动
        $criteria = new CDbCriteria();
        $criteria->compare('userid', Yii::app()->user->id);
        $items=CurFavorites::model()->findAll($criteria);
        foreach($items as $item){
            $pids[$item->pid]=$item->pid;
        }
        $items=CurActiveProject::model()->find('branch_id=:schoolid', array(':schoolid'=>$schoolid));
        foreach(unserialize($items->projects) as $item){
            $pids[$item]=$item;
        }
        
        $criteria = new CDbCriteria();
        $criteria->compare('t.pid', $pids);
        $criteria->compare('t.userid', Yii::app()->user->id, false, 'or');
        $items=CurProjects::model()->with('activity')->findAll($criteria);
        $projects = array();
		$activityTitle = array();
        foreach($items as $item){
            foreach($item->activity as $activity){
                $projects[CommonUtils::autoLang($item->cn_title, $item->en_title)][$activity->aid] = CommonUtils::autoLang($activity->cn_title, $activity->en_title);
				$activityTitle[$activity->aid] = array(
                    'title'=>CommonUtils::autoLang($activity->cn_title, $activity->en_title),
                    'memo'=>CommonUtils::autoLang($activity->cn_memo, $activity->en_memo),
                );
            }
        }
		Yii::app()->clientScript->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/underscore-min.js');
		Yii::app()->clientScript->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/backbone-min.js');
		Yii::app()->clientScript->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/phpfunc.js');
        
        Yii::app()->clientScript->registerCssFile(Yii::app()->theme->baseUrl.'/css/schedule/schedule.css');
		
		Yii::import('common.models.schedule.*');
		$crit = new CDbCriteria;
		$crit->compare('classid', $this->currentClassId);
		$crit->compare('weeknumber', $this->currentWeeknum);
		$schedule = ClassScheduleV2::model()->with('data')->find($crit);
		
		$crit = new CDbCriteria;
		$crit->compare('userid', Yii::app()->user->getId());
		$crit->index = 'scheduleid';
		$userTemplates = ScheduleTemplate::model()->with('data')->findAll( $crit );
		
        $this->render('schedule', array('userTemplates'=>$userTemplates, 'activityTitle'=>CJSON::encode($activityTitle), 'projects'=>$projects, 'classTime'=>$classTime,'schedule'=>$schedule));
    }


	public function TaskDevCheckList(){
		Yii::import('common.models.childdev.*');
		$nameListArr = $this->getNameList($this->currentClassId, true);
		
		$devDataArr = Yii::app()->db->createCommand()
			->select('id, childid, display, fall_display, spring_display')
			->from('ivy_dev_data')
			->where('classid=:classid',array(':classid'=>$this->currentClassId))
			->queryAll();
		
		foreach($devDataArr as $_data){
			if(isset($nameListArr[$_data['childid']])){
				$nameListArr[$_data['childid']]['fall'] = $_data['fall_display'];
				$nameListArr[$_data['childid']]['spring'] = $_data['spring_display'];			
			}
		}
		$nameList = array();
		
		foreach($nameListArr as $key => $_data){
			$nameListArr[$key]['fall'] = isset( $nameListArr[$key]['fall'] )? $nameListArr[$key]['fall'] : 0;
			$nameListArr[$key]['spring'] = isset( $nameListArr[$key]['spring'] )? $nameListArr[$key]['spring'] : 0;
			
			$nameList[] = CJSON::encode($nameListArr[$key]);
		}
		
		$classObj = IvyClass::model()->findByPk($this->currentClassId);
		$checkListTemplates = DevTemplate::model()->getTemplatebyClassType($classObj->classtype);
		
		//print_r($checkListTemplates['checkIds']);
		//print_r($devDataArr);
		
		$crit = new CDbCriteria;
		$crit->compare('classid', $this->currentClassId);
		$devData = DevData::model()->findAll();
		
		$securityCode = md5(sprintf('%s&%s&%s', Yii::app()->user->getId(), $this->currentClassId, $this->securityKey));
		
		Yii::app()->clientScript->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/underscore-min.js');
		Yii::app()->clientScript->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/backbone-min.js');
		Yii::app()->clientScript->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/phpfunc.js');
		Yii::app()->clientScript->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/jRating.jquery.min.js');
		Yii::app()->clientScript->registerCssFile( Yii::app()->theme->baseUrl.'/css/jRating.jquery.css');
		Yii::app()->clientScript->registerCssFile( Yii::app()->theme->baseUrl.'/css/teaching.css');
		$this->render('devcheck', array('nameList'=>$nameList, 'checkListTemplates'=>$checkListTemplates, 'securityCode'=>$securityCode));
	}
	
	public function actionChildDevCheckList($childid=0){
		extract($_POST);
		$myCode = md5(sprintf('%s&%s&%s', Yii::app()->user->getId(), $classid, $this->securityKey));
		if($myCode == $securityCode){
			Yii::import('common.models.childdev.*');
			$devData = DevData::model()->findByAttributes(array('classid'=>$classid,'childid'=>$childid));
			if(is_null($devData)){
				echo CJSON::encode(
					array(
						'state'=>'success',
						'data' => array(
							'hasdata'=>false,
						)
					)
				);
			}else{
				echo CJSON::encode(
					array(
						'state' => 'success',
						'data' => array(
							'hasdata'=>true,
							'devdata'=>unserialize($devData->development),
							'fall_display'=>$devData->fall_display,
							'spring_display'=>$devData->spring_display
						)
					)
				);
			}
		}
	}
	
	public function actionChildDevCheckListSave($childid=0){
		extract($_POST);
		$myCode = md5(sprintf('%s&%s&%s', Yii::app()->user->getId(), $classid, $this->securityKey));
		if($myCode == $securityCode){
			Yii::import('common.models.childdev.*');
			$devData = DevData::model()->findByAttributes(array('classid'=>$classid,'childid'=>$childid));
			if(is_null($devData)){
				$classObj = IvyClass::model()->findByPk($classid);
				$devData = new DevData;
				$devData->childid = $childid;
				$devData->branchid = $classObj->schoolid;
				$devData->classid = $classid;
				$devData->yid = $classObj->yid;
				$devData->class_type = $classObj->classtype;
				$devData->display = 1;
				$devData->timestamp = time();
				$devData->userid = Yii::app()->user->getId();
			}
			$dev = CJSON::decode($development);
			$devData->development = serialize($dev);
			$devData->fall_display = $fall_display;
			$devData->spring_display = $spring_display;
			if(!$devData->save()){
				echo CJSON::encode( array(
					'state'=>'failed',
					'message'=>Yii::t('global','System Error!'),
					//'message'=> $devData->getErrors()
				));
			}else{
				echo CJSON::encode(array(
					'state'=>'success',
					'message'=>Yii::t('global','Data Saved!'),
					'data' => array(
						'fall_display' => $devData->fall_display,
						'spring_display' => $devData->spring_display,
						'devdata'=>unserialize($devData->development),
					)
				));
			}
		}
	}
	
	public function TaskSemesterReport(){
		$nameListArr = $this->getNameList($this->currentClassId, true);
		
		$sRptDataArr = Yii::app()->db->createCommand()
			->select('*')
			->from('ivy_sreport')
			->where('classid=:classid',array(':classid'=>$this->currentClassId))
			->queryAll();
		
		foreach($sRptDataArr as $_data){
			if(isset( $nameListArr[$_data['childid']] )){
				$nameListArr[$_data['childid']][$_data['semester ']] = $_data['stat'];
			}
		}
		
		$cfgs = CommonUtils::LoadConfig('CfgSemesterReport');
		$defaultTemplate = (isset($cfgs[$this->currentSchoolId])) ? $cfgs[$this->currentSchoolId] : $cfgs['default'];
		
		$tempConfigFile = Yii::getPathOfAlias("common.components.semesterReport.templates.{$defaultTemplate}") . ".php";
		$rptTemplate[$defaultTemplate] = require($tempConfigFile);

		$ldlist = require(Yii::getPathOfAlias("common.components.semesterReport.learningDomain") . ".php");
		
		$nameList = array();
		
		foreach($nameListArr as $key => $_data){
			$nameListArr[$key]['10'] = isset( $nameListArr[$key]['0'] )? $nameListArr[$key]['0'] : 0;
			$nameListArr[$key]['20'] = isset( $nameListArr[$key]['20'] )? $nameListArr[$key]['20'] : 0;
			
			$nameList[] = CJSON::encode($nameListArr[$key]);
		}
		
		$learningDomains = Term::model()->getLdList($useLang=true);
				
		$maxWeekNumber = Yii::app()->db->createCommand()
			->select('max(weeknum) as maxweek')
			->from( sprintf('ivy_child_media_%s', $this->currentStartYear))
			->where('classid=:classid', array(':classid'=>$this->currentClassId))
			->queryRow();

		$securityCode = md5(sprintf('%s&%s&%s', Yii::app()->user->getId(), $this->currentClassId, $this->securityKey));

		Yii::app()->clientScript->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/underscore-min.js');
		Yii::app()->clientScript->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/backbone-min.js');
		Yii::app()->clientScript->registerCssFile( Yii::app()->theme->baseUrl.'/css/teaching.css');
		Yii::app()->clientScript->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/phpfunc.js');
		$this->render('sreport', array('nameList'=>$nameList,
			'srptTemplate'=>$rptTemplate,
			'ldlist' => CJSON::encode($ldlist),
			'maxweek' => intval($maxWeekNumber['maxweek']),
			'ldTerms'=>$learningDomains,
			'securityCode'=>$securityCode));
	}
	
	public function actionFecthSemesterReport(){
		extract($_POST);
		if(Yii::app()->request->isAjaxRequest){
			Yii::import('common.models.portfolio.*');
			$sRpt = SReport::model()->with('items')->findByAttributes(array(
				'classid' => $classid,
				'childid' => $childid,
				'semester' => $semester
			));
			
			$tempConfigFile = Yii::getPathOfAlias("common.components.semesterReport.templates.{$templateid}") . ".php";
			$templateCfg = require($tempConfigFile);
			
			$reportData = array();
			foreach($templateCfg['pages'] as $page){
				foreach($page['blocks'] as $_bkey => $_bitem){
					foreach($_bitem['content'] as $_content){
						$reportData[$_bkey][$_content] = ($_content == 'textArea') ? '' : 0;
					}
				}
			}
			
			$photoIds = array();
			
			if(!is_null($sRpt)){
				foreach($sRpt->items as $cateory=>$rptItem){
					$reportData[$cateory]['photo'] = $rptItem->media_id;
					$reportData[$cateory]['learningDomain'] = $rptItem->ld_id;
					$reportData[$cateory]['textArea'] = $rptItem->content;
					
					if($rptItem->media_id) $photoIds[] = $rptItem->media_id;
				}
			}
			
			$photos = array();
			
			if(count($photoIds)){
				ChildMedia::setStartYear($sRpt->startyear);
				$photoObjs = ChildMedia::model()->findAllByPk($photoIds);
				
				$serverConfigs = OA::getMediaServerConfig();
				foreach($photoObjs as $photoObj){
					$photos[$photoObj->id] = $serverConfigs['servers'][$photoObj->server]['url'] . $photoObj->getSubPath() . $photoObj->filename;
				}
			}
			
			echo CJSON::encode(array(
				'state' => 'success',
				'data' => array(
					'reportData' => $reportData,
					'photos'=>$photos
				)
			));
		}
	}
	
	public function TaskSemesterReport222(){
		$learningDomains = Term::model()->getLdList($useLang=true);
		$nameList = $this->getNameList($this->currentClassId);

		Yii::import('common.models.portfolio.*');
		ChildMedia::setStartYear($this->currentStartYear);
		
		$dataProvider=new CActiveDataProvider(ChildMedia::model());
		
		$maxWeekNumber = Yii::app()->db->createCommand()
			->select('max(weeknum) as maxweek')
			->from( sprintf('ivy_child_media_%s', $this->currentStartYear))
			->where('classid=:classid', array(':classid'=>$this->currentClassId))
			->queryRow();
		
		Yii::app()->clientScript->registerCssFile( Yii::app()->theme->baseUrl.'/css/teaching.css');
		$this->render('semesterrpt',
			array(
				'dataProvider' => $dataProvider,
				'learningDomains'=>$learningDomains,
				'nameList'=>$nameList,
				'maxweek' => intval($maxWeekNumber['maxweek'])
			)
		);
	}
	
	public function actionSaveSemesterReport(){
		//todo 数字签名判断
		Yii::import('common.models.portfolio.*');
		extract($_POST);
		$error = '';
		$sRpt = SReport::model()->with('items')->findByAttributes(array(
				'classid' => $classid,
				'childid' => $selectedChildid,
				'semester' => $selectedSemester
			));
		
		$isNew = false;
		if(is_null($sRpt)){
			$isNew = true;
			$classObj = IvyClass::model()->findByPk($classid);
			$sRpt = new SReport;
			$sRpt->schoolid = $classObj->schoolid;
			$sRpt->classid = $classid;
			$sRpt->childid = $selectedChildid;
			$sRpt->yid = $yid;
			$sRpt->startyear = $startyear;
			$sRpt->semester = $selectedSemester;
			$sRpt->template_id = $rptTempId;
		}
		$sRpt->timestamp = time();
		$sRpt->uid = Yii::app()->user->getId();
		
		if( count($rptData['assignMany'])) {
			foreach($rptData['assignMany'] as $category => $childIds){
				$crit = new CDbCriteria;
				$crit->compare('classid',$classid);
				$crit->compare('semester',$selectedSemester);
				$crit->index = 'childid';
				//$otherRpts = SReport::model()->with('items')->findAll($crit);
				$otherRpts = SReport::model()->with(array(
					'items'=>array(
						'condition'=>'category=:category',
						'params'=>array(':category'=>$category),
						'together'=>false //必须要，否则挂
					)))->findAll($crit);
				
				foreach($childIds as $_cid){
					if($_cid){
						if(!isset($otherRpts[$_cid])){
							$new_sRpt = new SReport;
							$new_sRpt->attributes = $sRpt->attributes;
							$new_sRpt->childid = $_cid;
							$new_sRpt->save();
							
							$new_sRptItem = new SReportItem;
							$new_sRptItem->report_id = $new_sRpt->id;
							$new_sRptItem->category = $category;							
							$new_sRptItem->media_id = $rptData[$category]['photo'];
							$new_sRptItem->save();
						}else{
							if(!isset($otherRpts[$_cid]->items[$category])){
								$new_sRptItem = new SReportItem;
								$new_sRptItem->report_id = $otherRpts[$_cid]->id;
								$new_sRptItem->category = $category;					
							}else{
								$new_sRptItem = $otherRpts[$_cid]->items[$category];
							}
							$new_sRptItem->media_id = $rptData[$category]['photo'];
							$new_sRptItem->save();
						}					
					}
				}
				foreach($otherRpts as $_ocid => $rpt){
					if(!in_array($_ocid, $childIds)){
						if ( $otherRpts[$_ocid]->items[$category]->media_id == $rptData[$category]['photo']){
							$otherRpts[$_ocid]->items[$category]->media_id = 0;
							$otherRpts[$_ocid]->items[$category]->save();
						}
					}
				}
			}
		}
		
		if($sRpt->save()){
			foreach( array_keys($rptData) as $category){
				if($category!='assignMany'){
					if($isNew || !isset($sRpt->items[$category])){
						$sRptItem = new SReportItem;
						$sRptItem->report_id = $sRpt->id;
						$sRptItem->category = $category;				
					}else{
						$sRptItem = $sRpt->items[$category];
					}
					$sRptItem->media_id = isset($rptData[$category]['photo']) ? $rptData[$category]['photo']:0;
					$sRptItem->ld_id = intval(isset($rptData[$category]['learningDomain']) ? $rptData[$category]['learningDomain']:0);
					$sRptItem->content = isset($rptData[$category]['textArea']) ? $rptData[$category]['textArea']:"";
					if(!$sRptItem->save()){
						$error .= print_r($sRptItem->getErrors(), true);
					}				
				}
			}
		}else{
			$error .= print_r($sRpt->getErrors(), true);
			print_r($sRpt->getErrors());
		}
		
		if(empty($error)){
			echo CJSON::encode(array(
				'state' => 'success'
			));		
		}else{
			echo CJSON::encode(array(
				'state' => 'fail',
				'message' => $error
			));				
		}
	}
	
	public function actionFetchAssignMany(){
		Yii::import('common.models.portfolio.*');
		extract($_POST);
		$existed = SReport::model()->with(array(
			'items'=>array(
				'condition'=>'category=:category',
				'params'=>array(':category'=>$context),
				'together'=>false
			)))->findAllByAttributes(array(
				'classid' => $classid,
				'semester' => $semester,
		));
		$result = array();
		if(count($existed)){
			foreach($existed as $rpt){
				$result[$rpt->items[$context]->media_id][] = $rpt->childid;
			}
		}
		echo CJSON::encode(array(
			'state' => 'success',
			'data' => $result
		));
	}
}