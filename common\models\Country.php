<?php

/**
 * This is the model class for table "ivy_country".
 *
 * The followings are the available columns in table 'ivy_country':
 * @property integer $id
 * @property string $country
 * @property string $country_cn
 */
class Country extends CActiveRecord {

    /**
     * Returns the static model of the specified AR class.
     * @param string $className active record class name.
     * @return Country the static model class
     */
    public static function model($className = __CLASS__) {
        return parent::model($className);
    }

    /**
     * @return string the associated database table name
     */
    public function tableName() {
        return 'ivy_country';
    }

    /**
     * @return array validation rules for model attributes.
     */
    public function rules() {
        // NOTE: you should only define rules for those attributes that
        // will receive user inputs.
        return array(
            array('country, country_cn', 'required'),
            array('country', 'length', 'max' => 20),
            array('country_cn', 'length', 'max' => 150),
            // The following rule is used by search().
            // Please remove those attributes that should not be searched.
            array('id, country, country_cn', 'safe', 'on' => 'search'),
        );
    }

    /**
     * @return array relational rules.
     */
    public function relations() {
        // NOTE: you may need to adjust the relation name and the related
        // class name for the relations automatically generated below.
        return array(
        );
    }

    /**
     * @return array customized attribute labels (name=>label)
     */
    public function attributeLabels() {
        return array(
            'id' => 'ID',
            'country' => 'Country',
            'country_cn' => 'Country Cn',
        );
    }

    public function getCountryName() {
        //Mims::LoadHelper('HtoolKits');
        return CommonUtils::autoLang($this->country_cn, $this->country);
    }

    /**
     * Retrieves a list of models based on the current search/filter conditions.
     * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
     */
    public function search() {
        // Warning: Please modify the following code to remove attributes that
        // should not be searched.

        $criteria = new CDbCriteria;

        $criteria->compare('id', $this->id);
        $criteria->compare('country', $this->country, true);
        $criteria->compare('country_cn', $this->country_cn, true);

        return new CActiveDataProvider($this, array(
                    'criteria' => $criteria,
                ));
    }

    public function getData($sortlang = 'en_us', $lang = 'ALL') {
        //$cacheIds = Mims::LoadConfig('CfgCacheId');
        $cacheIds = CommonUtils::LoadConfig('CfgCacheId');
        $cacheId = $cacheIds['countryList']['id'];
        if ($lang != 'ALL') {
            $cacheId .= '_' . $lang;
        }
        $countryList = Yii::app()->cache->get($cacheId);
        if (false === $countryList) {
            $crit = new CDbCriteria;
            $crit->order = ($sortlang == 'en_us') ? 'country ASC' : 'country_cn ASC';
            $countries = Country::model()->findAll($crit);
            foreach ($countries as $country) {
                if ($lang == 'zh_cn')
                    $countryList[$country->id] = $country->country_cn;
                elseif ($lang == 'en_us')
                    $countryList[$country->id] = $country->country;
                else
                    $countryList[$country->id] = $country->country . ' ' . $country->country_cn;
            }
            Yii::app()->cache->set($cacheId, $countryList);
        }
        return $countryList;
    }

    public function getCountryList($firstLabel = null, $riseCountry = null, $lang = false) {

        $criteria = new CDbCriteria();
        if (false === $lang) {
            $lang = Yii::app()->language;
        }
        if (true === $lang) {
            $cacheId = __CLASS__ . '_all_' . Yii::app()->language;
            $criteria->order = 'country ASC';
        } else {
            $cacheId = __CLASS__ . '_' . $lang;
            $criteria->order = $lang == 'en_us' ? 'country ASC' : 'country_cn ASC';
        }

        $countryList = Yii::app()->cache->get($cacheId);

        if (false === $countryList) {
            $criteria->index = 'id';
            $country = $this->findAll($criteria);
            if (!empty($country)) {
                foreach ($country as $c) {
                    $countryList[$c->id] = CommonUtils::autoLang($c->country_cn, $c->country, $lang);
                }
            }
            Yii::app()->cache->set($cacheId, $countryList, 3600 * 24 * 7);
        }
        if (null == $riseCountry) {
            $riseCountry = array(36, 175, 188, 187, 86, 66, 31, 9, 61, 166, 84);
        }

        $ckey = array_keys($countryList);
        $cckey = array_merge($riseCountry, $ckey);
        $cckey = array_unique($cckey);
        $cl = null;
        if ($firstLabel) {
            $cl[0] = $firstLabel;
        }
        foreach ($cckey as $cid) {
            $cl[$cid] = $countryList[$cid];
        }
        return $cl;
    }

    public function getName()
    {
        return (Yii::app()->language == "zh_cn") ?  $this->country_cn : $this->country;
    }

}