<?php

class ExportCommand extends CConsoleCommand {
    public $batchNum = 200;

	public function init(){
		Yii::setPathOfAlias('common', dirname(__FILE__) . '/../../../common');
	}

    public function actionToCatherine1($targetDate = 0)
    {
        if($targetDate){
            Yii::import('common.models.child.*');
            Yii::import('common.models.invoice.*');
            $country = Country::model()->getCountryList();
            $branch = Branch::model()->getBranchList(null, true);
            $classList = IvyClass::model()->findAll();
            $classes = array();
            foreach($classList as $classObj){
                $classes[$classObj->classid] = $classObj->title;
            }
            $criteria = new CDbCriteria();
            $criteria->compare('t.startdate', '<='.$targetDate);
            $criteria->compare('t.enddate', '>='.$targetDate);
            $criteria->compare('t.payment_type', 'tuition');
            $criteria->compare('t.status', 20);
            $criteria->compare('`inout`', 'in');
            //$criteria->compare('t.schoolid', 'BJ_DS');
            $total = Invoice::model()->count($criteria);
            $cycle = ceil($total/$this->batchNum);
            $csv = '';
            for($i=0; $i<$cycle; $i++){
                $criteria->limit=$this->batchNum;
                $criteria->offset=$i*$this->batchNum;
                $items = Invoice::model()->with('childprofile')->findAll($criteria);
                foreach($items as $item){
                    $fname = $fcompany = $fposition = $femail = $ftel = '';
                    $mname = $mcompany = $mposition = $memail = $mtel = '';
                    if($item->childprofile->fid){
                        $fUser = User::model()->with('profile', 'parent')->findByPk($item->childprofile->fid);
                        $fname = $this->_t($fUser->getName());
                        $fcompany = $this->_t($fUser->parent->company);
                        $fposition = $this->_t($fUser->parent->job);
                        $femail = $this->_t($fUser->email);
                        $ftel = $this->_t($fUser->parent->mphone);

                    }
                    if($item->childprofile->mid){
                        $mUser = User::model()->with('profile', 'parent')->findByPk($item->childprofile->mid);
                        $mname = $this->_t($mUser->getName());
                        $mcompany = $this->_t($mUser->parent->company);
                        $mposition = $this->_t($mUser->parent->job);
                        $memail = $this->_t($mUser->email);
                        $mtel = $this->_t($mUser->parent->mphone);

                    }
                    $classTit = isset($classes[$item->classid]) ? $this->_t($classes[$item->classid]) : '';
                    $csv .= $this->_t($item->childprofile->getChildName()).','.$country[$item->childprofile->country].','.$branch[$item->schoolid]['abb'].','.$classTit.','.$item->childprofile->birthday_search.','.date('Y-m-d', $item->childprofile->created_timestamp).',';
                    $csv .= $fname.','.$fcompany.','.$fposition.','.$femail.','.$ftel.',';
                    $csv .= $mname.','.$mcompany.','.$mposition.','.$memail.','.$mtel;
                    $csv .= "\n";
                    echo ".";
                }
            }
            file_put_contents(date('Y-m-d', $targetDate).'.csv', $csv);
        }
    }

    public function _t($str)
    {
        $str = str_replace(',', '，', $str);
        return iconv('UTF-8', 'GBK//IGNORE', $str);
    }

    public function _t1($str)
    {
        $str = str_replace(',', '，', $str);
        return mb_convert_encoding($str, 'GBK', 'UTF-8');
    }

    public function actionTest()
    {
        $sql1 = "SELECT count(*) as c FROM ivy_invoice_transaction where `inout`='in' and ufida_income=1 and amount>0 and transactiontype<90 and (timestampe between 1427817600 and 1430409599) and schoolid not in ('BJ_CP','BJ_WJ','BJ_DS','XA_JD')";
        $sql2 = "select count(*) as c from ivy_ufida_income where transaction_id != 0 and transactiontype != 0 and timestmp = 1430409599";
        $rs1 = Yii::app()->db->createCommand($sql1)->queryRow();
        $rs2 = Yii::app()->db->createCommand($sql2)->queryRow();
        if(($rs1['c']-$rs2['c']) == 155){
            $sql3 = "SELECT id FROM ivy_invoice_transaction where `inout`='in' and ufida_income=1 and amount>0 and transactiontype<90 and (timestampe between 1427817600 and 1430409599) and schoolid not in ('BJ_CP','BJ_WJ','BJ_DS','XA_JD')";
            $sql4 = "select transaction_id from ivy_ufida_income where transaction_id != 0 and transactiontype != 0 and timestmp = 1430409599";
            $rs3 = Yii::app()->db->createCommand($sql3)->queryAll();
            $rs4 = Yii::app()->db->createCommand($sql4)->queryAll();
            $ids1 = array();
            $ids2 = array();
            foreach($rs3 as $idarr){
                $ids1[$idarr['id']] = $idarr['id'];
            }
            foreach($rs4 as $idarr){
                $ids2[$idarr['transaction_id']] = $idarr['transaction_id'];
            }
            $diff = array_diff($ids1, $ids2);
            if($diff){
                foreach($diff as $id){
                    $sql = "update ivy_invoice_transaction set ufida_income=0 where id=".$id;
                    echo $id.",";
                    Yii::app()->db->createCommand($sql)->execute();
                }
            }
        }
    }

    public function actionToCatherine2($year='')
    {
        if($year){
            Yii::import('common.models.invoice.*');
            Yii::import('common.models.calendar.*');
            $branch = Branch::model()->getBranchList(null, true);
            $classList = IvyClass::model()->findAll();
            $classes = array();
            foreach($classList as $classObj){
                $classes[$classObj->classid] = $classObj->title;
            }

            $yids = array();
            $cri = new CDbCriteria();
            $cri->compare('startyear', array(2009,2010,2011,2012,2013,2014));
            $calendars = Calendar::model()->findAll($cri);
            foreach($calendars as $calendar){
                $yids[$calendar->yid] = $calendar->yid;
            }

            $criteria = new CDbCriteria();
            $criteria->compare('payment_type', 'tuition');
            $criteria->compare('yid', $yids);
            $total = ChildServiceInfo::model()->count($criteria);
            $cycle = ceil($total/$this->batchNum);
            $csv = '';
            $csv .= 'Child,';
            $csv .= 'School,';
            $csv .= 'Class,';
            $months = array();
            for($j=9;$j<81;$j++){
                $month = mktime(0, 0, 0, $j, 1, 2009);
                $months[] = $month;
                $csv .= date('Y-m', $month).',';
            }
            $csv .= "\n";
            for($i=0; $i<$cycle; $i++){
                $criteria->limit=$this->batchNum;
                $criteria->offset=$i*$this->batchNum;
                $items = ChildServiceInfo::model()->with('childInfo')->findAll($criteria);
                foreach($items as $item){
                    $data[$item->childid]['child'] = $item->childInfo->getChildName();
                    $data[$item->childid]['school'] = $branch[$item->schoolid]['abb'];
                    $data[$item->childid]['class'] = isset($classes[$item->classid]) ? $classes[$item->classid] : '';
                    $data[$item->childid]['date'][$item->id]['startdate'] = $item->startdate;
                    $data[$item->childid]['date'][$item->id]['enddate'] = $item->enddate;
                    $data[$item->childid]['mon'][$item->id] = $item->mon;
                }
            }

            foreach($data as $datum){
                $csv .= $this->_t($datum['child']).',';
                $csv .= $this->_t($datum['school']).',';
                $csv .= $this->_t($datum['class']).',';

                foreach($months as $monfirstday){
                    $inClass = false;
                    $dat = 0;
                    foreach($datum['date'] as $key=>$date){
                        if($monfirstday>=$date['startdate'] && $monfirstday<=$date['enddate']){
                            $inClass = true;
                            $dat = $datum['mon'][$key];
                        }
                    }
                    if($inClass){
                        $csv .= $dat.',';
                    }
                    else{
                        $csv .= '0,';
                    }
                }
                $csv .= "\n";
                echo '.';
            }

            file_put_contents($year.'.csv', $csv);
        }
    }

    public function actionToCatherine3()
    {
        Yii::import('common.models.invoice.*');
        Yii::import('common.models.calendar.*');
        $branch = Branch::model()->getBranchList(null, true);
        $classList = IvyClass::model()->findAll();
        $classes = array();
        foreach($classList as $classObj){
            $classes[$classObj->classid] = $classObj->title;
        }
        $cri = new CDbCriteria();
        $cri->compare('startyear', array(2011,2012,2013,2014));
        $calendars = Calendar::model()->findAll($cri);
        $yids = array();
        foreach($calendars as $calendar){
            $yids[$calendar->yid] = $calendar->startyear.' - '.($calendar->startyear+1);
        }

        $criteria = new CDbCriteria();
        $criteria->compare('calendar_id', array_keys($yids));
        $criteria->compare('payment_type', 'tuition');
        $criteria->compare('`inout`', 'in');
        $total = InvoiceTransaction::model()->count($criteria);
        $cycle = ceil($total/$this->batchNum);
        $csv = '';
        $cid = array();
        for($i=0; $i<$cycle; $i++){
            $criteria->limit=$this->batchNum;
            $criteria->offset=$i*$this->batchNum;
            $items = InvoiceTransaction::model()->findAll($criteria);
            foreach($items as $item){
                $count = ChildProfileBasic::model()->countByAttributes(array('childid'=>$item->childid, 'status'=>999));
                if($count){
                    $cid[$item->childid] = $item->childid;
                    echo '-';
                }
            }
        }
        foreach($cid as $id){
            $model = ChildProfileBasic::model()->with('stat')->findByPk($id);
            $csv .= $this->_t($model->getChildName()).',';
            $csv .= $branch[$model->schoolid]['abb'].',';
            $csv .= (isset($classes[$model->classid]) ? $this->_t($classes[$model->classid]) : '').',';
            $csv .= $model->birthday_search.',';
            $csv .= ($model->stat?$yids[$model->stat->calendar]:'').',';
            $csv .= "\n";
            echo '.';
        }
        file_put_contents('refund.csv', $csv);
    }

    # DS 所有收费
    public function actionToLucy1()
    {
        $dbFeeType = array(
            'registration'=>'注册费',
            'entrance'=>'入学费',
            'uniform'=>'校服',
            'softdyned'=>'软件费用',
            'bus'=>'校车费',
            'tuition'=>'学费',
            'lunch'=>'餐费',
        );
        Yii::import('common.models.invoice.*');
        $classList = IvyClass::model()->findAll();
        $classes = array();
        foreach($classList as $classObj){
            $classes[$classObj->classid] = $classObj->title;
        }

        $criteria = new CDbCriteria();
        $criteria->compare('schoolid', 'BJ_DS');
        $criteria->compare('status', 20);
        $criteria->compare('`inout`', 'in');
        $total = Invoice::model()->count($criteria);
        $cycle = ceil($total/$this->batchNum);
        $csv = '';
        for($i=0; $i<$cycle; $i++){
            $criteria->limit=$this->batchNum;
            $criteria->offset=$i*$this->batchNum;
            $items = Invoice::model()->findAll($criteria);
            foreach($items as $item){
                if($item->amount > 0){
                    $model = ChildProfileBasic::model()->findByPk($item->childid);
                    $csv .= $item->childid.',';
                    $csv .= $this->_t($model->getChildName()).',';
                    $csv .= (isset($classes[$model->classid]) ? $this->_t($classes[$model->classid]) : '').',';
                    $csv .= (isset($dbFeeType[$item->payment_type]) ? $this->_t($dbFeeType[$item->payment_type]) : $item->payment_type).',';
                    $csv .= $item->amount.',';
                    $csv .= $this->_t($item->title).',';
                    $csv .= ($item->startdate ? date('Y-m-d', $item->startdate) : '').',';
                    $csv .= ($item->enddate ? date('Y-m-d', $item->enddate) : '').',';
                    $csv .= "\n";
                    echo '.';
                }
            }
        }
        file_put_contents('DS.csv', $csv);

        $csv = '';
        $criteria = new CDbCriteria();
        $criteria->compare('schoolid', 'BJ_DS');
        $criteria->compare('status', 20);
        $items = InvoiceChildRefund::model()->findAll($criteria);
        foreach($items as $item){
            if($item->amount > 0){
                $model = ChildProfileBasic::model()->findByPk($item->childid);
                $csv .= $item->childid.',';
                $csv .= $this->_t($model->getChildName()).',';
                $csv .= (isset($classes[$model->classid]) ? $this->_t($classes[$model->classid]) : '').',';
                $csv .= (isset($dbFeeType[$item->payment_type]) ? $this->_t($dbFeeType[$item->payment_type]) : $item->payment_type).',';
                $csv .= $item->amount.',';
                $csv .= $this->_t($item->title).',';
                $csv .= ($item->startdate ? date('Y-m-d', $item->startdate) : '').',';
                $csv .= ($item->enddate ? date('Y-m-d', $item->enddate) : '').',';
                $csv .= "\n";
                echo '.';
            }
        }
        file_put_contents('DS_refund.csv', $csv);
    }

    public function actionToshenji()
    {
        $dbFeeType = array(
            'registration'=>'注册费',
            'entrance'=>'入学费',
            'uniform'=>'校服',
            'softdyned'=>'软件费用',
            'bus'=>'校车费',
            'tuition'=>'学费',
            'lunch'=>'餐费',
            'deposit'=>'预交学费',
        );

        Yii::import('common.models.invoice.*');

        $branchs = Branch::model()->getBranchList(null, true);
        $months = array('2014-09', '2014-10', '2014-11', '2014-12', '2015-01', '2015-02', '2015-03', '2015-04');

        $csv = '';
        $csv1 = '';
        foreach($branchs as $branch){
            foreach($months as $mon){
                $start = strtotime($mon.'-1');
                $end = mktime(0, 0, 0, (date('m', $start)+1), 1, date('Y', $start));
                $sql = "select title,amount,childid from ivy_invoice_transaction where `inout`='in' and timestampe >=".$start." and timestampe <=".$end." and schoolid='".$branch['id']."' and payment_type = 'deposit'";
                $items = Yii::app()->db->createCommand($sql)->queryAll();
                foreach($items as $item){
                    $csv .= $branch['abb'].",";
                    $csv .= $mon.",";
                    $csv .= $item['childid'].",";
                    $csv .= $item['amount'].",";
                    $csv .= $this->_t($item['title'])."";
                    $csv .= "\n";
                }

                $sql = "select * from ivy_invoice_child_refund where status=20 and timestamp >=".$start." and timestamp <=".$end." and schoolid='".$branch['id']."' and payment_type = 'deposit'";
                $items = Yii::app()->db->createCommand($sql)->queryAll();
                foreach($items as $item){
                    $csv1 .= $branch['abb'].",";
                    $csv1 .= $mon.",";
                    $csv1 .= $item['childid'].",";
                    $csv1 .= $item['amount'].",";
                    $csv1 .= $this->_t($item['title'])."";
                    $csv1 .= "\n";
                }
                echo '.';
            }
        }
        if($csv){
            file_put_contents('D:/fee/csv.csv', $csv);
        }

        if($csv1){
            file_put_contents('D:/fee/refund/csv.csv', $csv1);
        }
    }

    public function actionWechatMenu()
    {
        $access_token = '55_OhZnnuRagtXqRXJNO9SSxbd6Qu9t_5wiM14tigleLs07VtxsLWJBELIm-olMjMn7WG8PgAfwK7xc-5Cw9RW0qUr09GoTIzyjfNPETEulEqMgt75nIuVeXp19bxlbY2LhDWDNPuwKG0Xile8NJKCaAJAQRJ';

        $url = 'https://api.weixin.qq.com/cgi-bin/menu/addconditional?access_token='.$access_token;
        $appid = 'wxb1a42b81111e29f3';      // appid
//        $appid = 'wx903fba9d4709cf10';      // appid
        $state = 'ds';

        $menuCN = array(
            'button' => array(
                array(
                    'name' => '基本信息',
                    'sub_button' => array(
                        array(
                            'type' => 'view',
                            'name' => '绑定账号',
                            'url' => 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' .$appid. '&redirect_uri='.urlencode('http://www.ivyonline.cn/wechat/user/bind/lang/zh_cn').'&response_type=code&scope=snsapi_userinfo&state=' .$state. '#wechat_redirect',
                            'sub_button' => array(),
                        ),
                        array(
                            'type' => 'view',
                            'name' => '孩子信息',
                            'url' => 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' .$appid. '&redirect_uri='.urlencode('http://www.ivyonline.cn/wechat/user/student/lang/zh_cn').'&response_type=code&scope=snsapi_userinfo&state=' .$state. '#wechat_redirect',
                            'sub_button' => array(),
                        ),
                        array(
                            'type' => 'view',
                            'name' => '我的同学',
                            'url' => 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' .$appid. '&redirect_uri='.urlencode('http://www.ivyonline.cn/wechat/user/mates/lang/zh_cn').'&response_type=code&scope=snsapi_userinfo&state=' .$state. '#wechat_redirect',
                            'sub_button' => array(),
                        ),
                    ),
                ),
                array(
                    'name' => '校园生活',
                    'sub_button' => array(
                        array(
                            'type' => 'view',
                            'name' => '通知公告',
                            'url' => 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' .$appid. '&redirect_uri='.urlencode('https://wx.daystaracademy.cn/notice?lang=zh').'&response_type=code&scope=snsapi_userinfo&state=' .$state. '#wechat_redirect',
                            'sub_button' => array(),
                        ),
                        array(
                            'type' => 'view',
                            'name' => '每周报告',
                            'url' => 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' .$appid. '&redirect_uri='.urlencode('http://www.ivyonline.cn/wechat/user/journal/lang/zh_cn').'&response_type=code&scope=snsapi_userinfo&state=' .$state. '#wechat_redirect',
                            'sub_button' => array(),
                        ),
                        array(
                            'type' => 'view',
                            'name' => '每周餐谱',
                            'url' => 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' .$appid. '&redirect_uri='.urlencode('http://www.ivyonline.cn/wechat/user/cateringMenuDetail/lang/zh_cn').'&response_type=code&scope=snsapi_userinfo&state=' .$state. '#wechat_redirect',
                            'sub_button' => array(),
                        ),
                        array(
                            'type' => 'view',
                            'name' => 'Dragon项目',
                            'url' => 'http://www.ivyonline.cn/wechat/asa',
                            'sub_button' => array(),
                        ),
//                        array(
//                            'type' => 'view',
//                            'name' => '课后课程',
//                            'url' => 'http://www.ivyonline.cn/wechat/asa/asaindex',
//                            'sub_button' => array(),
//                        ),
                        array(
                            'type' => 'view',
                            'name' => '校服选购',
                            'url' => 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' .$appid. '&redirect_uri='.urlencode('http://www.ivyonline.cn/wechat/products/index/lang/zh_cn').'&response_type=code&scope=snsapi_userinfo&state=' .$state. '#wechat_redirect',
                            'sub_button' => array(),
                        ),
//                        array(
//                            'type' => 'click',
//                            'key' => 'MINE_CONTACT_CN',
//                            'name' => '联系校园',
//                            'sub_button' => array(),
//                        ),
//                        array(
//                            'type' => 'view',
//                            'url' => 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' .$appid. '&redirect_uri=http://www.ivyonline.cn/wechat/user/songs/lang/zh_cn&response_type=code&scope=snsapi_userinfo&state=' .$state. '#wechat_redirect',
//                            'name' => '幼儿音乐',
//                            'sub_button' => array(),
//                        ),
                    ),
                ),
                array(
                    'name' => '财务信息',
                    'sub_button' => array(
                        array(
                            'type' => 'view',
                            'name' => '未付账单',
                            'url' => 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' .$appid. '&redirect_uri='.urlencode('http://www.ivyonline.cn/wechat/user/unpaidInvoices/lang/zh_cn').'&response_type=code&scope=snsapi_userinfo&state=' .$state. '#wechat_redirect',
                            'sub_button' => array(),
                        ),
                        array(
                            'type' => 'view',
                            'name' => '付款记录',
                            'url' => 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' .$appid. '&redirect_uri='.urlencode('http://www.ivyonline.cn/wechat/user/paidInvoices/lang/zh_cn').'&response_type=code&scope=snsapi_userinfo&state=' .$state. '#wechat_redirect',
                            'sub_button' => array(),
                        ),
                        array(
                            'type' => 'view',
                            'name' => '余额信息',
                            'url' => 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' .$appid. '&redirect_uri='.urlencode('http://www.ivyonline.cn/wechat/user/credit/lang/zh_cn').'&response_type=code&scope=snsapi_userinfo&state=' .$state. '#wechat_redirect',
                            'sub_button' => array(),
                        ),
                    ),
                ),
            ),
            'matchrule' => array(
                "tag_id" => "129",
//                'language' => 'zh_CN'
            ),
        );

        $post = json_encode($menuCN, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);
//        $post = json_encode($menuCN);
//        echo $post;die;

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $post);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER , 1);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_POST, 1);
//        $result = curl_exec($ch);
//        print_r($result);
//        die;


        $menuEN = array(
            'button' => array(
                array(
                    'name' => '中/En',
                    'sub_button' => array(
                        array(
                            'type' => 'view',
                            'name' => 'Bind Account',
                            'url' => 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' .$appid. '&redirect_uri=http://www.ivyonline.cn/wechat/user/bind/lang/en_us&response_type=code&scope=snsapi_userinfo&state=' .$state. '#wechat_redirect',
                            'sub_button' => array(),
                        ),
//                        array(
//                            'type' => 'view',
//                            'name' => 'My Children',
//                            'url' => 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' .$appid. '&redirect_uri=http://www.ivyonline.cn/wechat/user/student/lang/en_us&response_type=code&scope=snsapi_userinfo&state=' .$state. '#wechat_redirect',
//                            'sub_button' => array(),
//                        ),
                        array(
                            'type' => 'view',
                            'name' => 'My Class',
//                            'url' => 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' .$appid. '&redirect_uri=http://www.ivyonline.cn/wechat/user/mates/lang/en_us&response_type=code&scope=snsapi_userinfo&state=' .$state. '#wechat_redirect',
                            'url' => 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' .$appid. '&redirect_uri=https://wx.daystaracademy.cn/student?lang=en&response_type=code&scope=snsapi_userinfo&state=' .$state. '#wechat_redirect',
                            'sub_button' => array(),
                        ),
                        array(
                            'type' => 'view',
                            'name' => 'School Calendar',
                            'url' => 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' .$appid. '&redirect_uri=https://wx.daystaracademy.cn/calendar?lang=en&response_type=code&scope=snsapi_userinfo&state=' .$state. '#wechat_redirect',
                            'sub_button' => array(),
                        ),
                        array(
                            'type' => 'view',
                            'name' => 'PTC',
                            'url' => 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' .$appid. '&redirect_uri=https://wx.daystaracademy.cn/ptc?lang=en&response_type=code&scope=snsapi_userinfo&state=' .$state. '#wechat_redirect',
                            'sub_button' => array(),
                        ),
                        array(
                            'type' => 'click',
                            'name' => '使用中文',
                            'key' => 'USE_CHINESE',
                            'sub_button' => array(),
                        )
                    )
                ),
                array(
                    'name' => 'School Life',
                    'sub_button' => array(
                        array(
                            'type' => 'view',
                            'name' => 'Notice',
                            'url' => 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' .$appid. '&redirect_uri=https://wx.daystaracademy.cn/notice?lang=en&response_type=code&scope=snsapi_userinfo&state=' .$state. '#wechat_redirect',
                            'sub_button' => array(),
                        ),
                        array(
                            'type' => 'view',
                            'name' => 'Weekly Journal',
                            'url' => 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' .$appid. '&redirect_uri=http://www.ivyonline.cn/wechat/user/journal/lang/en_us&response_type=code&scope=snsapi_userinfo&state=' .$state. '#wechat_redirect',
                            'sub_button' => array(),
                        ),
                        array(
                            'type' => 'view',
                            'name' => 'Assessments',
                            'url' => 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' .$appid. '&redirect_uri=https://wx.daystaracademy.cn/assessments?lang=en&response_type=code&scope=snsapi_userinfo&state=' .$state. '#wechat_redirect',
                            'sub_button' => array(),
                        ),
                        array(
                            'type' => 'view',
                            'name' => 'Lunch Menu',
                            'url' => 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' .$appid. '&redirect_uri=http://www.ivyonline.cn/wechat/user/cateringMenuDetail/lang/en_us&response_type=code&scope=snsapi_userinfo&state=' .$state. '#wechat_redirect',
                            'sub_button' => array(),
                        ),
                        array(
                            'type' => 'view',
                            'name' => 'Dragon Program',
                            'url' => 'http://www.ivyonline.cn/wechat/asa',
                            'sub_button' => array(),
                        ),
//                        array(
//                            'type' => 'view',
//                            'name' => 'ASA',
//                            'url' => 'http://www.ivyonline.cn/wechat/asa/asaindex',
//                            'sub_button' => array(),
//                        ),
//                        array(
//                            'type' => 'view',
//                            'name' => 'Uniform Purchase',
//                            'url' => 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' .$appid. '&redirect_uri=http://www.ivyonline.cn/wechat/products/index/lang/en_us&response_type=code&scope=snsapi_userinfo&state=' .$state. '#wechat_redirect',
//                            'sub_button' => array(),
//                        ),
//                        array(
//                            'type' => 'click',
//                            'key' => 'MINE_CONTACT_EN',
//                            'name' => 'Contact Us',
//                            'sub_button' => array(),
//                        ),
//                        array(
//                            'type' => 'view',
//                            'url' => 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' .$appid. '&redirect_uri=http://www.ivyonline.cn/wechat/user/songs/lang/en_us&response_type=code&scope=snsapi_userinfo&state=' .$state. '#wechat_redirect',
//                            'name' => 'Children Songs',
//                            'sub_button' => array(),
//                        ),
                    ),
                ),
                array(
                    'name' => 'Payment',
                    'sub_button' => array(
                        array(
                            'type' => 'view',
                            'name' => 'Uniform Purchase',
                            'url' => 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' .$appid. '&redirect_uri=https://wx.daystaracademy.cn/uniform?lang=en&response_type=code&scope=snsapi_userinfo&state=' .$state. '#wechat_redirect',
                            'sub_button' => array(),
                        ),
                        array(
                            'type' => 'view',
                            'name' => 'Unpaid Bills',
                            'url' => 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' .$appid. '&redirect_uri=http://www.ivyonline.cn/wechat/user/unpaidInvoices/lang/en_us/&response_type=code&scope=snsapi_userinfo&state=' .$state. '#wechat_redirect',
                            'sub_button' => array(),
                        ),
                        array(
                            'type' => 'view',
                            'name' => 'Payment History',
                            'url' => 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' .$appid. '&redirect_uri=http://www.ivyonline.cn/wechat/user/paidInvoices/lang/en_us&response_type=code&scope=snsapi_userinfo&state=' .$state. '#wechat_redirect',
                            'sub_button' => array(),
                        ),
                        array(
                            'type' => 'view',
                            'name' => 'Balance',
                            'url' => 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' .$appid. '&redirect_uri=http://www.ivyonline.cn/wechat/user/credit/lang/en_us&response_type=code&scope=snsapi_userinfo&state=' .$state. '#wechat_redirect',
                            'sub_button' => array(),
                        ),
                    ),
                ),
            ),
            'matchrule' => array(
                "tag_id" => "130",
//                'language' => 'en'
            ),
        );

        $post = json_encode($menuEN, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);
//        echo $post;die;

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $post);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER , 1);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_POST, 1);
        $result = curl_exec($ch);
        print_r($result);

        /**
        {
        "button": [
        {
        "name": "中/En",
        "sub_button": [
        {
        "type": "view",
        "name": "绑定账号",
        "url": "https://open.weixin.qq.com/connect/oauth2/authorize?appid=wxb1a42b81111e29f3&redirect_uri=http://www.ivyonline.cn/wechat/user/bind/lang/zh_cn&response_type=code&scope=snsapi_userinfo&state=ds#wechat_redirect",
        "sub_button": [ ]
        },
        {
        "type": "view",
        "name": "我和同学",
        "url": "https://open.weixin.qq.com/connect/oauth2/authorize?appid=wxb1a42b81111e29f3&redirect_uri=https://wx.daystaracademy.cn/student?lang=zh&response_type=code&scope=snsapi_userinfo&state=ds#wechat_redirect",
        "sub_button": [ ]
        },
        {
        "type": "view",
        "name": "校历",
        "url": "https://open.weixin.qq.com/connect/oauth2/authorize?appid=wxb1a42b81111e29f3&redirect_uri=https://wx.daystaracademy.cn/calendar?lang=zh&response_type=code&scope=snsapi_userinfo&state=ds#wechat_redirect",
        "sub_button": [ ]
        },
        {
        "type": "view",
        "name": "PTC",
        "url": "https://open.weixin.qq.com/connect/oauth2/authorize?appid=wxb1a42b81111e29f3&redirect_uri=https://wx.daystaracademy.cn/ptc?lang=zh&response_type=code&scope=snsapi_userinfo&state=ds#wechat_redirect",
        "sub_button": [ ]
        },
        {
        "type": "click",
        "name": "Use English",
        "key": "USE_ENGLISH",
        "sub_button": [ ]
        }
        ]
        },
        {
        "name": "校园生活",
        "sub_button": [
        {
        "type": "view",
        "name": "通知公告",
        "url": "https://open.weixin.qq.com/connect/oauth2/authorize?appid=wxb1a42b81111e29f3&redirect_uri=https://wx.daystaracademy.cn/notice?lang=zh&response_type=code&scope=snsapi_userinfo&state=ds#wechat_redirect",
        "sub_button": [ ]
        },
        {
        "type": "view",
        "name": "每周报告",
        "url": "https://open.weixin.qq.com/connect/oauth2/authorize?appid=wxb1a42b81111e29f3&redirect_uri=http://www.ivyonline.cn/wechat/user/journal/lang/zh_cn&response_type=code&scope=snsapi_userinfo&state=ds#wechat_redirect",
        "sub_button": [ ]
        },
        {
        "type": "view",
        "name": "学业报告",
        "url": "https://open.weixin.qq.com/connect/oauth2/authorize?appid=wxb1a42b81111e29f3&redirect_uri=https://wx.daystaracademy.cn/assessments?lang=zh&response_type=code&scope=snsapi_userinfo&state=ds#wechat_redirect",
        "sub_button": [ ]
        },
        {
        "type": "view",
        "name": "每周餐谱",
        "url": "https://open.weixin.qq.com/connect/oauth2/authorize?appid=wxb1a42b81111e29f3&redirect_uri=http://www.ivyonline.cn/wechat/user/cateringMenuDetail/lang/zh_cn&response_type=code&scope=snsapi_userinfo&state=ds#wechat_redirect",
        "sub_button": [ ]
        },
        {
        "type": "view",
        "name": "Dragon项目",
        "url": "http://www.ivyonline.cn/wechat/asa",
        "sub_button": [ ]
        }
        ]
        },
        {
        "name": "财务信息",
        "sub_button": [
        {
        "type": "view",
        "name": "校服选购",
        "url": "https://open.weixin.qq.com/connect/oauth2/authorize?appid=wxb1a42b81111e29f3&redirect_uri=https://wx.daystaracademy.cn/uniform?lang=zh&response_type=code&scope=snsapi_userinfo&state=ds#wechat_redirect",
        "sub_button": [ ]
        },
        {
        "type": "view",
        "name": "未付账单",
        "url": "https://open.weixin.qq.com/connect/oauth2/authorize?appid=wxb1a42b81111e29f3&redirect_uri=http://www.ivyonline.cn/wechat/user/unpaidInvoices/lang/zh_cn/&response_type=code&scope=snsapi_userinfo&state=ds#wechat_redirect",
        "sub_button": [ ]
        },
        {
        "type": "view",
        "name": "付款记录",
        "url": "https://open.weixin.qq.com/connect/oauth2/authorize?appid=wxb1a42b81111e29f3&redirect_uri=http://www.ivyonline.cn/wechat/user/paidInvoices/lang/zh_cn&response_type=code&scope=snsapi_userinfo&state=ds#wechat_redirect",
        "sub_button": [ ]
        },
        {
        "type": "view",
        "name": "余额信息",
        "url": "https://open.weixin.qq.com/connect/oauth2/authorize?appid=wxb1a42b81111e29f3&redirect_uri=http://www.ivyonline.cn/wechat/user/credit/lang/zh_cn&response_type=code&scope=snsapi_userinfo&state=ds#wechat_redirect",
        "sub_button": [ ]
        }
        ]
        }
        ]
        }
         */
    }

    public function actionWechatMenu2022()
    {
        $access_token = '85_nR9iREc7W07L6PbXhFfOqjUYPEyt_1urDiXCYUIimhupvSMfsRCL7qV3RgF82hcQ6WYS_Df1USi-G5ZPpkeBbo0dA3klPAdK34QqS91J7uOD4i6Ngsc6cgq1diYSSGhAEADDR';

        $url = 'https://api.weixin.qq.com/cgi-bin/menu/addconditional?access_token='.$access_token;
        $appid = 'wxb1a42b81111e29f3';
        $state = 'ds';

        $menuEN = array(
            'button' => array(
                array(
                    'name' => 'Basic',
                    'sub_button' => array(
                        array(
                            'type' => 'click',
                            'name' => 'EN/中文',
                            'key' => 'USE_CHINESE',
                            'sub_button' => array(),
                        ),
                        array(
                            'type' => 'view',
                            'name' => 'Bind Account',
                            'url' => 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' .$appid. '&redirect_uri=https://wx.daystaracademy.cn/login?lang=en&response_type=code&scope=snsapi_userinfo&state=' .$state. '#wechat_redirect',
                            'sub_button' => array(),
                        ),
                        array(
                            'type' => 'view',
                            'name' => 'Calendar',
                            'url' => 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' .$appid. '&redirect_uri=https://wx.daystaracademy.cn/calendar?lang=en&response_type=code&scope=snsapi_userinfo&state=' .$state. '#wechat_redirect',
                            'sub_button' => array(),
                        ),
                        array(
                            'type' => 'view',
                            'name' => 'More...',
                            'url' => 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' .$appid. '&redirect_uri=https://wx.daystaracademy.cn/more?lang=en&response_type=code&scope=snsapi_userinfo&state=' .$state. '#wechat_redirect',
                            'sub_button' => array(),
                        ),
                    )
                ),
                array(
                    'name' => 'Communicate',
                    'sub_button' => array(
                        array(
                            'type' => 'view',
                            'name' => 'Message Staff',
                            'url' => 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' .$appid. '&redirect_uri=https://wx.daystaracademy.cn/message?lang=en&response_type=code&scope=snsapi_userinfo&state=' .$state. '#wechat_redirect',
                            'sub_button' => array(),
                        ),
                        array(
                            'type' => 'view',
                            'name' => 'Notices',
                            'url' => 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' .$appid. '&redirect_uri=https://wx.daystaracademy.cn/notice?lang=en&response_type=code&scope=snsapi_userinfo&state=' .$state. '#wechat_redirect',
                            'sub_button' => array(),
                        ),
                        array(
                            'type' => 'view',
                            'name' => 'Journals',
                            'url' => 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' .$appid. '&redirect_uri=http://www.ivyonline.cn/wechat/user/journal/lang/en_us&response_type=code&scope=snsapi_userinfo&state=' .$state. '#wechat_redirect',
                            'sub_button' => array(),
                        ),
                        array(
                            'type' => 'view',
                            'name' => 'Report Card',
                            'url' => 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' .$appid. '&redirect_uri=https://wx.daystaracademy.cn/assessments?lang=en&response_type=code&scope=snsapi_userinfo&state=' .$state. '#wechat_redirect',
                            'sub_button' => array(),
                        ),
//                        array(
//                            'type' => 'view',
//                            'name' => 'Competitions',
//                            'url' => 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' .$appid. '&redirect_uri=https://wx.daystaracademy.cn/competitions?lang=en&response_type=code&scope=snsapi_userinfo&state=' .$state. '#wechat_redirect',
//                            'sub_button' => array(),
//                        ),
                        array(
                            'type' => 'view',
                            'name' => 'PTC',
                            'url' => 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' .$appid. '&redirect_uri=https://wx.daystaracademy.cn/ptc?lang=en&response_type=code&scope=snsapi_userinfo&state=' .$state. '#wechat_redirect',
                            'sub_button' => array(),
                        )
                    ),
                ),
                array(
                    'name' => 'School Life',
                    'sub_button' => array(
                        array(
                            'type' => 'view',
                            'name' => 'Athletics',
                            'url' => 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' .$appid. '&redirect_uri=https://wx.daystaracademy.cn/athletics?lang=en&response_type=code&scope=snsapi_userinfo&state=' .$state. '#wechat_redirect',
                            'sub_button' => array(),
                        ),
                        array(
                            'type' => 'view',
                            'name' => 'ASA and Camps',
                            'url' => 'http://www.ivyonline.cn/wechat/asa',
                            'sub_button' => array(),
                        ),
                        array(
                            'type' => 'view',
                            'name' => 'Lunch Menu',
                            'url' => 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' .$appid. '&redirect_uri=http://www.ivyonline.cn/wechat/user/cateringMenuDetail/lang/en_us&response_type=code&scope=snsapi_userinfo&state=' .$state. '#wechat_redirect',
                            'sub_button' => array(),
                        ),
                        array(
                            'type' => 'view',
                            'name' => 'Pay Bills',
                            'url' => 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' .$appid. '&redirect_uri=http://www.ivyonline.cn/wechat/user/unpaidInvoices/lang/en_us/&response_type=code&scope=snsapi_userinfo&state=' .$state. '#wechat_redirect',
                            'sub_button' => array(),
                        ),
                        array(
                            'type' => 'view',
                            'name' => 'Buy Uniform',
                            'url' => 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' .$appid. '&redirect_uri=https://wx.daystaracademy.cn/uniform?lang=en&response_type=code&scope=snsapi_userinfo&state=' .$state. '#wechat_redirect',
                            'sub_button' => array(),
                        ),
                    ),
                ),
            ),
            'matchrule' => array(
                "tag_id" => "130",
            ),
        );

        $post = json_encode($menuEN, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);
//        echo $post;die;

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $post);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER , 1);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_POST, 1);
        $result = curl_exec($ch);
        print_r($result);

        $menuCN = array(
            'button' => array(
                array(
                    'name' => '基本信息',
                    'sub_button' => array(
                        array(
                            'type' => 'click',
                            'name' => 'EN/中文',
                            'key' => 'USE_ENGLISH_2023',
                            'sub_button' => array(),
                        ),
                        array(
                            'type' => 'view',
                            'name' => '绑定账号',
                            'url' => 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' .$appid. '&redirect_uri=https://wx.daystaracademy.cn/login?lang=zh&response_type=code&scope=snsapi_userinfo&state=' .$state. '#wechat_redirect',
                            'sub_button' => array(),
                        ),
                        array(
                            'type' => 'view',
                            'name' => '校园校历',
                            'url' => 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' .$appid. '&redirect_uri=https://wx.daystaracademy.cn/calendar?lang=zh&response_type=code&scope=snsapi_userinfo&state=' .$state. '#wechat_redirect',
                            'sub_button' => array(),
                        ),
                        array(
                            'type' => 'view',
                            'name' => '更多...',
                            'url' => 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' .$appid. '&redirect_uri=https://wx.daystaracademy.cn/more?lang=zh&response_type=code&scope=snsapi_userinfo&state=' .$state. '#wechat_redirect',
                            'sub_button' => array(),
                        ),
                    )
                ),
                array(
                    'name' => '校园沟通',
                    'sub_button' => array(
                        array(
                            'type' => 'view',
                            'name' => '联系校园',
                            'url' => 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' .$appid. '&redirect_uri=https://wx.daystaracademy.cn/message?lang=zh&response_type=code&scope=snsapi_userinfo&state=' .$state. '#wechat_redirect',
                            'sub_button' => array(),
                        ),
                        array(
                            'type' => 'view',
                            'name' => '校园公告',
                            'url' => 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' .$appid. '&redirect_uri=https://wx.daystaracademy.cn/notice?lang=zh&response_type=code&scope=snsapi_userinfo&state=' .$state. '#wechat_redirect',
                            'sub_button' => array(),
                        ),
                        array(
                            'type' => 'view',
                            'name' => '学生日志',
                            'url' => 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' .$appid. '&redirect_uri=http://www.ivyonline.cn/wechat/user/journal/lang/zh_cn&response_type=code&scope=snsapi_userinfo&state=' .$state. '#wechat_redirect',
                            'sub_button' => array(),
                        ),
                        array(
                            'type' => 'view',
                            'name' => '学业报告',
                            'url' => 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' .$appid. '&redirect_uri=https://wx.daystaracademy.cn/assessments?lang=zh&response_type=code&scope=snsapi_userinfo&state=' .$state. '#wechat_redirect',
                            'sub_button' => array(),
                        ),
//                        array(
//                            'type' => 'view',
//                            'name' => 'Competitions',
//                            'url' => 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' .$appid. '&redirect_uri=https://wx.daystaracademy.cn/competitions?lang=zh&response_type=code&scope=snsapi_userinfo&state=' .$state. '#wechat_redirect',
//                            'sub_button' => array(),
//                        ),
                        array(
                            'type' => 'view',
                            'name' => 'PTC家长会',
                            'url' => 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' .$appid. '&redirect_uri=https://wx.daystaracademy.cn/ptc?lang=zh&response_type=code&scope=snsapi_userinfo&state=' .$state. '#wechat_redirect',
                            'sub_button' => array(),
                        )
                    ),
                ),
                array(
                    'name' => '校园生活',
                    'sub_button' => array(
                        array(
                            'type' => 'view',
                            'name' => '凤凰体育',
                            'url' => 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' .$appid. '&redirect_uri=https://wx.daystaracademy.cn/athletics?lang=zh&response_type=code&scope=snsapi_userinfo&state=' .$state. '#wechat_redirect',
                            'sub_button' => array(),
                        ),
                        array(
                            'type' => 'view',
                            'name' => 'Dragon项目',
                            'url' => 'http://www.ivyonline.cn/wechat/asa',
                            'sub_button' => array(),
                        ),
                        array(
                            'type' => 'view',
                            'name' => '每周餐谱',
                            'url' => 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' .$appid. '&redirect_uri=http://www.ivyonline.cn/wechat/user/cateringMenuDetail/lang/zh_cn&response_type=code&scope=snsapi_userinfo&state=' .$state. '#wechat_redirect',
                            'sub_button' => array(),
                        ),
                        array(
                            'type' => 'view',
                            'name' => '未付账单',
                            'url' => 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' .$appid. '&redirect_uri=http://www.ivyonline.cn/wechat/user/unpaidInvoices/lang/zh_cn/&response_type=code&scope=snsapi_userinfo&state=' .$state. '#wechat_redirect',
                            'sub_button' => array(),
                        ),
                        array(
                            'type' => 'view',
                            'name' => '校服选购',
                            'url' => 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' .$appid. '&redirect_uri=http://www.ivyonline.cn/wechat/products/index/lang/zh_cn&response_type=code&scope=snsapi_userinfo&state=' .$state. '#wechat_redirect',
                            'sub_button' => array(),
                        ),
                    ),
                ),
            ),
            'matchrule' => array(
                "tag_id" => "133",
            ),
        );

        $post = json_encode($menuCN, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);
//        echo $post;die;

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $post);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER , 1);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_POST, 1);
//        $result = curl_exec($ch);
//        print_r($result);

        /**
        {
        "button": [
        {
        "name": "基本信息",
        "sub_button": [
        {
        "type": "click",
        "name": "EN/中文",
        "key": "USE_ENGLISH",
        "sub_button": [ ]
        },
        {
        "type": "view",
        "name": "绑定账号",
        "url": "https://open.weixin.qq.com/connect/oauth2/authorize?appid=wxb1a42b81111e29f3&redirect_uri=https://wx.daystaracademy.cn?lang=zh&response_type=code&scope=snsapi_userinfo&state=ds#wechat_redirect",
        "sub_button": [ ]
        },
        {
        "type": "view",
        "name": "校园校历",
        "url": "https://open.weixin.qq.com/connect/oauth2/authorize?appid=wxb1a42b81111e29f3&redirect_uri=https://wx.daystaracademy.cn/calendar?lang=zh&response_type=code&scope=snsapi_userinfo&state=ds#wechat_redirect",
        "sub_button": [ ]
        },
        {
        "type": "view",
        "name": "更多...",
        "url": "https://open.weixin.qq.com/connect/oauth2/authorize?appid=wxb1a42b81111e29f3&redirect_uri=https://wx.daystaracademy.cn/more?lang=zh&response_type=code&scope=snsapi_userinfo&state=ds#wechat_redirect",
        "sub_button": [ ]
        }
        ]
        },
        {
        "name": "校园沟通",
        "sub_button": [
        {
        "type": "view",
        "name": "联系校园",
        "url": "https://open.weixin.qq.com/connect/oauth2/authorize?appid=wxb1a42b81111e29f3&redirect_uri=https://wx.daystaracademy.cn/message?lang=zh&response_type=code&scope=snsapi_userinfo&state=ds#wechat_redirect",
        "sub_button": [ ]
        },
        {
        "type": "view",
        "name": "校园公告",
        "url": "https://open.weixin.qq.com/connect/oauth2/authorize?appid=wxb1a42b81111e29f3&redirect_uri=https://wx.daystaracademy.cn/notice?lang=zh&response_type=code&scope=snsapi_userinfo&state=ds#wechat_redirect",
        "sub_button": [ ]
        },
        {
        "type": "view",
        "name": "学生日志",
        "url": "https://open.weixin.qq.com/connect/oauth2/authorize?appid=wxb1a42b81111e29f3&redirect_uri=http://www.ivyonline.cn/wechat/user/journal/lang/zh_cn&response_type=code&scope=snsapi_userinfo&state=ds#wechat_redirect",
        "sub_button": [ ]
        },
        {
        "type": "view",
        "name": "学业报告",
        "url": "https://open.weixin.qq.com/connect/oauth2/authorize?appid=wxb1a42b81111e29f3&redirect_uri=https://wx.daystaracademy.cn/assessments?lang=zh&response_type=code&scope=snsapi_userinfo&state=ds#wechat_redirect",
        "sub_button": [ ]
        },
        {
        "type": "view",
        "name": "PTC家长会",
        "url": "https://open.weixin.qq.com/connect/oauth2/authorize?appid=wxb1a42b81111e29f3&redirect_uri=https://wx.daystaracademy.cn/ptc?lang=zh&response_type=code&scope=snsapi_userinfo&state=ds#wechat_redirect",
        "sub_button": [ ]
        }
        ]
        },
        {
        "name": "校园生活",
        "sub_button": [
        {
        "type": "view",
        "name": "凤凰体育",
        "url": "https://open.weixin.qq.com/connect/oauth2/authorize?appid=wxb1a42b81111e29f3&redirect_uri=https://wx.daystaracademy.cn/athletics?lang=zh&response_type=code&scope=snsapi_userinfo&state=ds#wechat_redirect",
        "sub_button": [ ]
        },
        {
        "type": "view",
        "name": "ASA课后课",
        "url": "http://www.ivyonline.cn/wechat/asa",
        "sub_button": [ ]
        },
        {
        "type": "view",
        "name": "每周餐谱",
        "url": "https://open.weixin.qq.com/connect/oauth2/authorize?appid=wxb1a42b81111e29f3&redirect_uri=http://www.ivyonline.cn/wechat/user/cateringMenuDetail/lang/zh_cn&response_type=code&scope=snsapi_userinfo&state=ds#wechat_redirect",
        "sub_button": [ ]
        },
        {
        "type": "view",
        "name": "未付账单",
        "url": "https://open.weixin.qq.com/connect/oauth2/authorize?appid=wxb1a42b81111e29f3&redirect_uri=http://www.ivyonline.cn/wechat/user/unpaidInvoices/lang/zh_cn/&response_type=code&scope=snsapi_userinfo&state=ds#wechat_redirect",
        "sub_button": [ ]
        },
        {
        "type": "view",
        "name": "校服选购",
        "url": "https://open.weixin.qq.com/connect/oauth2/authorize?appid=wxb1a42b81111e29f3&redirect_uri=https://wx.daystaracademy.cn/uniform?lang=zh&response_type=code&scope=snsapi_userinfo&state=ds#wechat_redirect",
        "sub_button": [ ]
        }
        ]
        }
        ]
        }
         */
    }

    public function actionWechatMenuNew()
    {
        $access_token = '76_K0dTTJNTcPyrWnEyLxIgmZ2kfZkFaSYD9juHO9_FwCAMOwnyHhDDQc7FCyp6MtDhx3CMHm0zQv9yEuiVD4wGiDJSwBlS6a8slvP-_-UjcJfXZ3Zauu0E-Fa2PjYUPZbABAJEO';

        $url = 'https://api.weixin.qq.com/cgi-bin/menu/addconditional?access_token='.$access_token;
        $appid = 'wx903fba9d4709cf10';      // appid
        $state = 'ivy';

        $menuCN = array(
            'button' => array(
                array(
                    'name' => '我的',
                    'sub_button' => array(
                        array(
                            'type' => 'view',
                            'name' => '绑定账号',
                            'url' => 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' .$appid. '&redirect_uri=http://www.ivyonline.cn/wechat/user/bind/lang/zh_cn&response_type=code&scope=snsapi_userinfo&state=' .$state. '#wechat_redirect',
                            'sub_button' => array(),
                        ),
                        array(
                            'type' => 'view',
                            'name' => '孩子信息',
                            'url' => 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' .$appid. '&redirect_uri=http://www.ivyonline.cn/wechat/user/student/lang/zh_cn&response_type=code&scope=snsapi_userinfo&state=' .$state. '#wechat_redirect',
                            'sub_button' => array(),
                        ),
                        array(
                            'type' => 'view',
                            'name' => '未付账单',
                            'url' => 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' .$appid. '&redirect_uri=http://www.ivyonline.cn/wechat/user/unpaidInvoices/lang/zh_cn/&response_type=code&scope=snsapi_userinfo&state=' .$state. '#wechat_redirect',
                            'sub_button' => array(),
                        ),
                        array(
                            'type' => 'view',
                            'name' => '付款记录',
                            'url' => 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' .$appid. '&redirect_uri=https://wx.daystaracademy.cn/bill/history?lang=zh&response_type=code&scope=snsapi_userinfo&state=' .$state. '#wechat_redirect',
                            'sub_button' => array(),
                        ),
                        array(
                            'type' => 'view',
                            'name' => '余额信息',
                            'url' => 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' .$appid. '&redirect_uri=https://wx.daystaracademy.cn/accountBalance?lang=zh&response_type=code&scope=snsapi_userinfo&state=' .$state. '#wechat_redirect',
                            'sub_button' => array(),
                        ),
                    ),
                ),
                array(
                    'name' => '校园生活',
                    'sub_button' => array(
                        array(
                            'type' => 'view',
                            'name' => '每周报告',
                            'url' => 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' .$appid. '&redirect_uri=http://www.ivyonline.cn/wechat/user/journal/lang/zh_cn&response_type=code&scope=snsapi_userinfo&state=' .$state. '#wechat_redirect',
                            'sub_button' => array(),
                        ),
                        array(
                            'type' => 'view',
                            'name' => '每周餐谱',
                            'url' => 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' .$appid. '&redirect_uri=http://www.ivyonline.cn/wechat/user/cateringMenuDetail/lang/zh_cn&response_type=code&scope=snsapi_userinfo&state=' .$state. '#wechat_redirect',
                            'sub_button' => array(),
                        ),
                        array(
                            'type' => 'view',
                            'url' => 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' .$appid. '&redirect_uri=http://www.ivyonline.cn/wechat/school/schedule/lang/zh_cn&response_type=code&scope=snsapi_userinfo&state=' .$state. '#wechat_redirect',
                            'name' => '每周安排',
                            'sub_button' => array(),
                        ),
                        array(
                            'type' => 'view',
                            'name' => '课后课程',
                            'url' => 'http://www.ivyonline.cn/wechat/asa/asaindex',
                            'sub_button' => array(),
                        ),
                        array(
                            'type' => 'view',
                            'name' => '我的伙伴',
                            'url' => 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' .$appid. '&redirect_uri=http://www.ivyonline.cn/wechat/user/mates/lang/zh_cn&response_type=code&scope=snsapi_userinfo&state=' .$state. '#wechat_redirect',
                            'sub_button' => array(),
                        ),
                    ),
                ),
                array(
                    'type' => 'view',
                    'name' => '更多功能',
                    'url' => 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' .$appid. '&redirect_uri=http://www.ivyonline.cn/wechat/school/index/lang/zh_cn&response_type=code&scope=snsapi_userinfo&state=' .$state. '#wechat_redirect',
                    'sub_button' => array(),
                ),
            ),
            'matchrule' => array(
                'language' => 'zh_CN'
            ),
        );

        $post = json_encode($menuCN, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);
//        echo $post;die;

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $post);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER , 1);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_POST, 1);
//        $result = curl_exec($ch);
//        print_r(CJSON::decode($result));


        $menuEN = array(
            'button' => array(
                array(
                    'name' => 'Me',
                    'sub_button' => array(
                        array(
                            'type' => 'view',
                            'name' => 'Connect Account',
                            'url' => 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' .$appid. '&redirect_uri=http://www.ivyonline.cn/wechat/user/bind/lang/en_us&response_type=code&scope=snsapi_userinfo&state=' .$state. '#wechat_redirect',
                            'sub_button' => array(),
                        ),
//                        array(
//                            'type' => 'view',
//                            'name' => 'My Children',
//                            'url' => 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' .$appid. '&redirect_uri=http://www.ivyonline.cn/wechat/user/student/lang/en_us&response_type=code&scope=snsapi_userinfo&state=' .$state. '#wechat_redirect',
//                            'sub_button' => array(),
//                        ),
                        array(
                            'type' => 'view',
                            'name' => 'My Friends',
                            'url' => 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' .$appid. '&redirect_uri=https://wx.daystaracademy.cn/student?lang=en&response_type=code&scope=snsapi_userinfo&state=' .$state. '#wechat_redirect',
                            'sub_button' => array(),
                        ),
                        array(
                            'type' => 'view',
                            'name' => 'Unpaid Bills',
                            'url' => 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' .$appid. '&redirect_uri=http://www.ivyonline.cn/wechat/user/unpaidInvoices/lang/en_us/&response_type=code&scope=snsapi_userinfo&state=' .$state. '#wechat_redirect',
                            'sub_button' => array(),
                        ),
                        array(
                            'type' => 'view',
                            'name' => 'Payment History',
                            'url' => 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' .$appid. '&redirect_uri=https://wx.daystaracademy.cn/bill/history?lang=en&response_type=code&scope=snsapi_userinfo&state=' .$state. '#wechat_redirect',
                            'sub_button' => array(),
                        ),
                        array(
                            'type' => 'view',
                            'name' => 'Balance',
                            'url' => 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' .$appid. '&redirect_uri=https://wx.daystaracademy.cn/accountBalance?lang=en&response_type=code&scope=snsapi_userinfo&state=' .$state. '#wechat_redirect',
                            'sub_button' => array(),
                        ),
                    ),
                ),
                array(
                    'name' => 'School Life',
                    'sub_button' => array(
                        array(
                            'type' => 'view',
                            'name' => 'Weekly Journal',
                            'url' => 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' .$appid. '&redirect_uri=http://www.ivyonline.cn/wechat/user/journal/lang/en_us&response_type=code&scope=snsapi_userinfo&state=' .$state. '#wechat_redirect',
                            'sub_button' => array(),
                        ),
                        array(
                            'type' => 'view',
                            'name' => 'Lunch Menu',
                            'url' => 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' .$appid. '&redirect_uri=http://www.ivyonline.cn/wechat/user/cateringMenuDetail/lang/en_us&response_type=code&scope=snsapi_userinfo&state=' .$state. '#wechat_redirect',
                            'sub_button' => array(),
                        ),
                        array(
                            'type' => 'view',
                            'url' => 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' .$appid. '&redirect_uri=http://www.ivyonline.cn/wechat/school/schedule/lang/en_us&response_type=code&scope=snsapi_userinfo&state=' .$state. '#wechat_redirect',
                            'name' => 'Weekly Schedule',
                            'sub_button' => array(),
                        ),
                        array(
                            'type' => 'view',
                            'name' => 'ASA',
                            'url' => 'http://www.ivyonline.cn/wechat/asa/asaindex',
                            'sub_button' => array(),
                        ),
                        array(
                            'type' => 'view',
                            'name' => 'Buy Uniform',
                            'url' => 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' .$appid. '&redirect_uri=https://wx.daystaracademy.cn/uniform?lang=en&response_type=code&scope=snsapi_userinfo&state=' .$state. '#wechat_redirect',
                            'sub_button' => array(),
                        ),
                    ),
                ),
                array(
                    'type' => 'view',
                    'name' => 'More',
                    'url' => 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' .$appid. '&redirect_uri=http://www.ivyonline.cn/wechat/school/index/lang/en_us&response_type=code&scope=snsapi_userinfo&state=' .$state. '#wechat_redirect',
                    'sub_button' => array(),
                ),
            ),
            'matchrule' => array(
                "tag_id" => "133",
//                'language' => 'en'
            ),
        );

        $post = json_encode($menuEN, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);
//        echo $post;die;

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $post);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER , 1);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_POST, 1);
        $result = curl_exec($ch);
        print_r($result);

        /**
        {
        "button":[
        {
        "name":"我的",
        "sub_button":[
        {
        "type":"view",
        "name":"绑定账号",
        "url":"https://open.weixin.qq.com/connect/oauth2/authorize?appid=wx903fba9d4709cf10&redirect_uri=http://www.ivyonline.cn/wechat/user/bind/lang/zh_cn&response_type=code&scope=snsapi_userinfo&state=ivy#wechat_redirect",
        "sub_button":[

        ]
        },
        {
        "type":"view",
        "name":"我的伙伴",
        "url":"https://open.weixin.qq.com/connect/oauth2/authorize?appid=wx903fba9d4709cf10&redirect_uri=https://wx.daystaracademy.cn/student?lang=zh&response_type=code&scope=snsapi_userinfo&state=ivy#wechat_redirect",
        "sub_button":[

        ]
        },
        {
        "type":"view",
        "name":"未付账单",
        "url":"https://open.weixin.qq.com/connect/oauth2/authorize?appid=wx903fba9d4709cf10&redirect_uri=http://www.ivyonline.cn/wechat/user/unpaidInvoices/lang/zh_cn/&response_type=code&scope=snsapi_userinfo&state=ivy#wechat_redirect",
        "sub_button":[

        ]
        },
        {
        "type":"view",
        "name":"付款记录",
        "url":"https://open.weixin.qq.com/connect/oauth2/authorize?appid=wx903fba9d4709cf10&redirect_uri=https://wx.daystaracademy.cn/bill/history?lang=zh&response_type=code&scope=snsapi_userinfo&state=ivy#wechat_redirect",
        "sub_button":[

        ]
        },
        {
        "type":"view",
        "name":"余额信息",
        "url":"https://open.weixin.qq.com/connect/oauth2/authorize?appid=wx903fba9d4709cf10&redirect_uri=https://wx.daystaracademy.cn/accountBalance?lang=zh&response_type=code&scope=snsapi_userinfo&state=ivy#wechat_redirect",
        "sub_button":[

        ]
        }
        ]
        },
        {
        "name":"校园生活",
        "sub_button":[
        {
        "type":"view",
        "name":"每周报告",
        "url":"https://open.weixin.qq.com/connect/oauth2/authorize?appid=wx903fba9d4709cf10&redirect_uri=http://www.ivyonline.cn/wechat/user/journal/lang/zh_cn&response_type=code&scope=snsapi_userinfo&state=ivy#wechat_redirect",
        "sub_button":[

        ]
        },
        {
        "type":"view",
        "name":"每周餐谱",
        "url":"https://open.weixin.qq.com/connect/oauth2/authorize?appid=wx903fba9d4709cf10&redirect_uri=http://www.ivyonline.cn/wechat/user/cateringMenuDetail/lang/zh_cn&response_type=code&scope=snsapi_userinfo&state=ivy#wechat_redirect",
        "sub_button":[

        ]
        },
        {
        "type":"view",
        "url":"https://open.weixin.qq.com/connect/oauth2/authorize?appid=wx903fba9d4709cf10&redirect_uri=http://www.ivyonline.cn/wechat/school/schedule/lang/zh_cn&response_type=code&scope=snsapi_userinfo&state=ivy#wechat_redirect",
        "name":"每周安排",
        "sub_button":[

        ]
        },
        {
        "type":"view",
        "name":"课后课程",
        "url":"http://www.ivyonline.cn/wechat/asa/asaindex",
        "sub_button":[

        ]
        },
        {
        "type":"view",
        "name":"校服选购",
        "url":"https://open.weixin.qq.com/connect/oauth2/authorize?appid=wx903fba9d4709cf10&redirect_uri=https://wx.daystaracademy.cn/uniform?lang=zh&response_type=code&scope=snsapi_userinfo&state=ivy#wechat_redirect",
        "sub_button":[

        ]
        }
        ]
        },
        {
        "type":"view",
        "name":"更多功能",
        "url":"https://open.weixin.qq.com/connect/oauth2/authorize?appid=wx903fba9d4709cf10&redirect_uri=http://www.ivyonline.cn/wechat/school/index/lang/zh_cn&response_type=code&scope=snsapi_userinfo&state=ivy#wechat_redirect",
        "sub_button":[

        ]
        }
        ]
        }
         */
    }

    public function actionWechatMenuMessenger()
    {
        $access_token = '73_BpsPTRvfbYKTvaPgfF5vGpJLP6d6vZl1dwknnqCN4olYeprFny85nk2OzOVrjWo5r-lAUrZC6yq68zvRW5cnMXklGa4SHuyMgWjQKcA2II1k_PMtKcTyHHL6jzwXUYgAFATQU';

        $url = 'https://api.weixin.qq.com/cgi-bin/menu/addconditional?access_token='.$access_token;
        $appid = 'wxe929eea87aaf012f';
        $state = 'ds';

        $menuEN = array(
            'button' => array(
                array(
                    'name' => 'Me',
                    'sub_button' => array(
                        array(
                            'type' => 'click',
                            'name' => 'EN/中文',
                            'key' => 'USE_CHINESE',
                            'sub_button' => array(),
                        ),
                        array(
                            'type' => 'view',
                            'name' => 'Bind Account',
                            'url' => 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' .$appid. '&redirect_uri=https://staff.ivyonline.cn?lang=en&response_type=code&scope=snsapi_userinfo&state=' .$state. '#wechat_redirect',
                            'sub_button' => array(),
                        ),
                    )
                ),
                array(
                    'name' => 'Workspace',
                    'type' => 'view',
                    'url' => 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' .$appid. '&redirect_uri=https://staff.ivyonline.cn/workspace?lang=en&response_type=code&scope=snsapi_userinfo&state=' .$state. '#wechat_redirect',
                    'sub_button' => array()
                )
            ),
            'matchrule' => array(
                "tag_id" => "101",
            )
        );

        $post = json_encode($menuEN, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $post);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER , 1);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_POST, 1);
        $result = curl_exec($ch);
        print_r($result);
    }

    /**
    {
    "button": [
    {
    "name": "我的",
    "sub_button": [
    {
    "type": "click",
    "name": "EN/中文",
    "key": "USE_ENGLISH"
    },
    {
    "type": "view",
    "name": "绑定账号",
    "url": "https://open.weixin.qq.com/connect/oauth2/authorize?appid=wxe929eea87aaf012f&redirect_uri=https://staff.ivyonline.cn?lang=zh&response_type=code&scope=snsapi_userinfo&state=dswechat_redirect"
    }
    ]
    },
    {
    "name": "工作台",
    "type": "view",
    "url": "https://open.weixin.qq.com/connect/oauth2/authorize?appid=wxe929eea87aaf012f&redirect_uri=https://staff.ivyonline.cn/workspace?lang=zh&response_type=code&scope=snsapi_userinfo&state=dswechat_redirect",
    "sub_button": []
    }
    ]
    }
     */

    public function actionSend()
    {
        $url = 'https://api.weixin.qq.com/cgi-bin/message/template/send?access_token=WIJIr6ewFWxYYBSoD3AK6xXVMfRmV4SXHaxFF_4C7-g_dMPgqwa6_3kQLkdiTDi00pS6FElYBjh06Hh5qQlPPFXiKSHz8TZqqDy0t36KuVRB_Vp4cx21ZRn43nMaxoFPEXGcAAAMFO';
        $data = array(
            'touser' => 'oHBKPwV23NHyswXhsw91S9BIQ_Hw',
            'template_id' => 'iwYT6iZbSeSoE1Z8IK-ZxOir0hh2iGKOySsvEZeLHnw',
            'url' => 'http://www.ivyonline.cn',
            'data' => array(
                'first' => array('value'=>'尊敬的家长，您的孩子的学习报告已经上线'),
                'keyword1' => array('value'=>'测试'),
                'keyword2' => array('value'=>'2016/6/1 - 2016/6/5'),
                'remark' => array('value'=>'请点击“详情”查看报告完整信息'),
            ),
        );
        $post = json_encode($data, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $post);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER , 1);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_POST, 1);
        $result = curl_exec($ch);
        print_r(CJSON::decode($result));
    }

    public function actionMmxWechatMenu()
    {
        $access_token = '62_IWwpdtV9MjNzFPm4dNi6Cj2xKGeRJhC_Li89LEmWD7RsJsje5oElpk4w1pc_p383-anO8WpGin6y_WIy_qun-nMg_cCmIqFa1F7JQf8t6wx4XKaRCxBKF8P_4Rab4d3a3t2_1TYQldfPeHF7BYIbAAAROY';

        $url = 'https://api.weixin.qq.com/cgi-bin/menu/addconditional?access_token='.$access_token;
        $appid = 'wxb9c6c538ebf7efa2';      // appid
        $state = 'mmx';
        $baseUrl = 'http://m.ivykids.cn/wechat/';
        // 默认菜单
        $lang = 'en-US';
        $default = array(
            'button' => array(
                array(
                    'name' => 'ASA & Camp',
                    'sub_button' => array(
                        array(
                            'type' => 'view',
                            'name' => 'FAQ',
                            'url' => "https://open.weixin.qq.com/connect/oauth2/authorize?appid={$appid}&redirect_uri={$baseUrl}default/index%3flang%3d{$lang}&response_type=code&scope=snsapi_userinfo&state={$state}#wechat_redirect",
                        ),
                        array(
                            'type' => 'view',
                            'name' => 'Program',
                            'url' => "https://open.weixin.qq.com/connect/oauth2/authorize?appid={$appid}&redirect_uri={$baseUrl}course/index%3flang%3d{$lang}&response_type=code&scope=snsapi_userinfo&state={$state}#wechat_redirect",
                        ),
                        array(
                            'type' => 'view',
                            'name' => 'Me',
                            'url' => "https://open.weixin.qq.com/connect/oauth2/authorize?appid={$appid}&redirect_uri={$baseUrl}me/index%3flang%3d{$lang}&response_type=code&scope=snsapi_userinfo&state={$state}#wechat_redirect",
                        ),
                    ),
                ),
                array(
                    'name' => 'Weekend Program',
                    'sub_button' => array(
                        array(
                            'type' => 'miniprogram',
                            'name' => 'Beijing Sanlitun Campus',
                            'appid' => 'wx3690b00fdbbb3d53',
                            'pagepath' => 'pages/index/index?site_id=87104b5a2ca2d0e0',
                            'url' => 'http://m.ivykids.cn/miniprogram/no-miniprogram',
                        ),
                        array(
                            'type' => 'miniprogram',
                            'name' => 'Beijing Beigao Campus',
                            'appid' => 'wx3690b00fdbbb3d53',
                            'pagepath' => 'pages/index/index?site_id=f8fb7b74742011d0',
                            'url' => 'http://m.ivykids.cn/miniprogram/no-miniprogram',
                        ),
                    ),
                ),
            ),
        );
        $post = json_encode($default, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);
//         var_dump($post);die();
//         $ch = curl_init();
//         curl_setopt($ch, CURLOPT_URL, $url);
//         curl_setopt($ch, CURLOPT_POSTFIELDS, $post);
//         curl_setopt($ch, CURLOPT_RETURNTRANSFER , 1);
//         curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
//         curl_setopt($ch, CURLOPT_POST, 1);
//         $result = curl_exec($ch);
//         print_r($result);
//         die;


        // 中文菜单
        $lang = 'zh_CN';

        $menuCN = array(
            'button' => array(
                array(
                    'name' => '中/En',
                    'sub_button' => array(
                        array(
                            'type' => 'click',
                            'name' => 'Use English',
                            'key' => 'USE_ENGLISH',
                            'sub_button' => array(),
                        )
                    )
                ),
                array(
                    'name' => '课后课&营地',
                    'sub_button' => array(
                        array(
                            'type' => 'view',
                            'name' => '常见问题',
                            'url' => "https://open.weixin.qq.com/connect/oauth2/authorize?appid={$appid}&redirect_uri={$baseUrl}default/index%3flang%3d{$lang}&response_type=code&scope=snsapi_userinfo&state={$state}#wechat_redirect",
                        ),
                        array(
                            'type' => 'view',
                            'name' => '课程',
                            'url' => "https://open.weixin.qq.com/connect/oauth2/authorize?appid={$appid}&redirect_uri={$baseUrl}course/index%3flang%3d{$lang}&response_type=code&scope=snsapi_userinfo&state={$state}#wechat_redirect",
                        ),
                        array(
                            'type' => 'view',
                            'name' => '我的',
                            'url' => "https://open.weixin.qq.com/connect/oauth2/authorize?appid={$appid}&redirect_uri={$baseUrl}me/index%3flang%3d{$lang}&response_type=code&scope=snsapi_userinfo&state={$state}#wechat_redirect",
                        ),
                    ),
                ),
                array(
                    'name' => '周末课程',
                    'sub_button' => array(
                        array(
                            'type' => 'miniprogram',
                            'name' => '北京泉发校区（幼儿园）',
                            'appid' => 'wx3690b00fdbbb3d53',
                            'pagepath' => 'pages/index/index?site_id=7aa6baf5dcbc6da5',
                            'url' => 'http://m.ivykids.cn/miniprogram/no-miniprogram',
                        ),
                        array(
                            'type' => 'miniprogram',
                            'name' => '北京三里屯校区',
                            'appid' => 'wx3690b00fdbbb3d53',
                            'pagepath' => 'pages/index/index?site_id=87104b5a2ca2d0e0',
                            'url' => 'http://m.ivykids.cn/miniprogram/no-miniprogram',
                        ),
                        array(
                            'type' => 'miniprogram',
                            'name' => '北京北皋校区',
                            'appid' => 'wx3690b00fdbbb3d53',
                            'pagepath' => 'pages/index/index?site_id=f8fb7b74742011d0',
                            'url' => 'http://m.ivykids.cn/miniprogram/no-miniprogram',
                        ),
                    ),
                ),
            ),
            'matchrule' => array(
//                'language' => 'zh_CN'
                "tag_id" => "127"
            ),
        );

//         print_r(json_encode($menuCN, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE));die();

        $post = json_encode($menuCN, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $post);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER , 1);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_POST, 1);
        $result = curl_exec($ch);
        print_r($result);

        # 繁体菜单
        $menuCN = array(
            'button' => array(
                array(
                    'name' => '课后课&营地',
                    'sub_button' => array(
                        array(
                            'type' => 'view',
                            'name' => '常见问题',
                            'url' => "https://open.weixin.qq.com/connect/oauth2/authorize?appid={$appid}&redirect_uri={$baseUrl}default/index%3flang%3d{$lang}&response_type=code&scope=snsapi_userinfo&state={$state}#wechat_redirect",
                        ),
                        array(
                            'type' => 'view',
                            'name' => '课程',
                            'url' => "https://open.weixin.qq.com/connect/oauth2/authorize?appid={$appid}&redirect_uri={$baseUrl}course/index%3flang%3d{$lang}&response_type=code&scope=snsapi_userinfo&state={$state}#wechat_redirect",
                        ),
                        array(
                            'type' => 'view',
                            'name' => '我的',
                            'url' => "https://open.weixin.qq.com/connect/oauth2/authorize?appid={$appid}&redirect_uri={$baseUrl}me/index%3flang%3d{$lang}&response_type=code&scope=snsapi_userinfo&state={$state}#wechat_redirect",
                        ),
                    ),
                ),
                array(
                    'name' => '周末课程',
                    'sub_button' => array(
                        array(
                            'type' => 'miniprogram',
                            'name' => '北京三里屯校区',
                            'appid' => 'wx3690b00fdbbb3d53',
                            'pagepath' => 'pages/index/index?site_id=87104b5a2ca2d0e0',
                            'url' => 'http://m.ivykids.cn/miniprogram/no-miniprogram',
                        ),
                        array(
                            'type' => 'miniprogram',
                            'name' => '北京北皋校区',
                            'appid' => 'wx3690b00fdbbb3d53',
                            'pagepath' => 'pages/index/index?site_id=f8fb7b74742011d0',
                            'url' => 'http://m.ivykids.cn/miniprogram/no-miniprogram',
                        ),
                    ),
                ),
            ),
            'matchrule' => array(
                'language' => 'zh_TW'
            ),
        );

//         print_r(json_encode($menuCN, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE));die();

        $post = json_encode($menuCN, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);

//        $ch = curl_init();
//        curl_setopt($ch, CURLOPT_URL, $url);
//        curl_setopt($ch, CURLOPT_POSTFIELDS, $post);
//        curl_setopt($ch, CURLOPT_RETURNTRANSFER , 1);
//        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
//        curl_setopt($ch, CURLOPT_POST, 1);
//        $result = curl_exec($ch);
//        print_r($result);

        // 英文菜单
        $lang = 'en-US';
        $menuEN = array(
            'button' => array(
                array(
                    'name' => '中/En',
                    'sub_button' => array(
                        array(
                            'type' => 'click',
                            'name' => '使用中文',
                            'key' => 'USE_CHINESE',
                            'sub_button' => array(),
                        )
                    )
                ),
                array(
                    'name' => "ASA & Camp",
                    'sub_button' => array(
                        array(
                            'type' => 'view',
                            'name' => 'FAQ',
                            'url' => "https://open.weixin.qq.com/connect/oauth2/authorize?appid={$appid}&redirect_uri={$baseUrl}default/index%3flang%3d{$lang}&response_type=code&scope=snsapi_userinfo&state={$state}#wechat_redirect",
                        ),
                        array(
                            'type' => 'view',
                            'name' => 'Program',
                            'url' => "https://open.weixin.qq.com/connect/oauth2/authorize?appid={$appid}&redirect_uri={$baseUrl}course/index%3flang%3d{$lang}&response_type=code&scope=snsapi_userinfo&state={$state}#wechat_redirect",
                        ),
                        array(
                            'type' => 'view',
                            'name' => 'Me',
                            'url' => "https://open.weixin.qq.com/connect/oauth2/authorize?appid={$appid}&redirect_uri={$baseUrl}me/index%3flang%3d{$lang}&response_type=code&scope=snsapi_userinfo&state={$state}#wechat_redirect",
                        ),
                    ),
                ),
                array(
                    'name' => 'Weekend Program',
                    'sub_button' => array(
                        array(
                            'type' => 'miniprogram',
                            'name' => 'Beijing Quanfa Campus',
                            'appid' => 'wx3690b00fdbbb3d53',
                            'pagepath' => 'pages/index/index?site_id=7aa6baf5dcbc6da5',
                            'url' => 'http://m.ivykids.cn/miniprogram/no-miniprogram',
                        ),
                        array(
                            'type' => 'miniprogram',
                            'name' => 'Beijing Sanlitun Campus',
                            'appid' => 'wx3690b00fdbbb3d53',
                            'pagepath' => 'pages/index/index?site_id=87104b5a2ca2d0e0',
                            'url' => 'http://m.ivykids.cn/miniprogram/no-miniprogram',
                        ),
                        array(
                            'type' => 'miniprogram',
                            'name' => 'Beijing Beigao Campus',
                            'appid' => 'wx3690b00fdbbb3d53',
                            'pagepath' => 'pages/index/index?site_id=f8fb7b74742011d0',
                            'url' => 'http://m.ivykids.cn/miniprogram/no-miniprogram',
                        ),
                    ),
                ),
            ),
            'matchrule' => array(
//                'language' => 'en'
                "tag_id" => "128",
            ),
        );

        $post = json_encode($menuEN, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $post);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER , 1);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_POST, 1);
        $result = curl_exec($ch);
        print_r($result);

        /*
         {
    "button":[
        {
            "name":"中/En",
            "sub_button":[
                {
                    "type": "click",
                    "name": "Use English",
                    "key": "USE_ENGLISH",
                    "sub_button": [ ]
                }
            ]
        },
        {
            "name":"课后课&营地",
            "sub_button":[
                {
                    "type":"view",
                    "name":"常见问题",
                    "url":"https://open.weixin.qq.com/connect/oauth2/authorize?appid=wxb9c6c538ebf7efa2&redirect_uri=http://m.ivykids.cn/wechat/default/index%3flang%3dzh-CN&response_type=code&scope=snsapi_userinfo&state=mmx#wechat_redirect"
                },
                {
                    "type":"view",
                    "name":"课程",
                    "url":"https://open.weixin.qq.com/connect/oauth2/authorize?appid=wxb9c6c538ebf7efa2&redirect_uri=http://m.ivykids.cn/wechat/course/index%3flang%3dzh-CN&response_type=code&scope=snsapi_userinfo&state=mmx#wechat_redirect"
                },
                {
                    "type":"view",
                    "name":"我的",
                    "url":"https://open.weixin.qq.com/connect/oauth2/authorize?appid=wxb9c6c538ebf7efa2&redirect_uri=http://m.ivykids.cn/wechat/me/index%3flang%3dzh-CN&response_type=code&scope=snsapi_userinfo&state=mmx#wechat_redirect"
                }
            ]
        },
        {
            "name":"周末课程",
            "sub_button":[
                {
                    "type":"miniprogram",
                    "name":"北京泉发校区（幼儿园）",
                    "appid":"wx3690b00fdbbb3d53",
                    "pagepath":"pages/index/index?site_id=7aa6baf5dcbc6da5",
                    "url":"http://m.ivykids.cn/miniprogram/no-miniprogram"
                },
                {
                    "type":"miniprogram",
                    "name":"北京三里屯校区",
                    "appid":"wx3690b00fdbbb3d53",
                    "pagepath":"pages/index/index?site_id=87104b5a2ca2d0e0",
                    "url":"http://m.ivykids.cn/miniprogram/no-miniprogram"
                },
                {
                    "type":"miniprogram",
                    "name":"北京北皋校区",
                    "appid":"wx3690b00fdbbb3d53",
                    "pagepath":"pages/index/index?site_id=f8fb7b74742011d0",
                    "url":"http://m.ivykids.cn/miniprogram/no-miniprogram"
                }
            ]
        }
    ]
}
         */

    }



    public function actionReport($step = 7, $schoolid = 'BJ_SLT', $type = 2018)
    {
        Yii::import('common.models.regextrainfo.*');
        Yii::import('common.models.invoice.*');
        $criteira = new CDbCriteria();
        $criteira->compare('step', $step);
        $criteira->compare('status', 1);
        $criteira->compare('schoolid', $schoolid);
        $criteira->compare('type', $type);
        $criteira->order = "childid DESC";
        $criteira->index = "childid";
        $regexyraInfoObj = RegExtraInfo::model()->findAll($criteira);


        if($regexyraInfoObj){
            $criteira = new CDbCriteria();
            $criteira->compare('childid', array_keys($regexyraInfoObj));
            $criteira->index = 'childid';
            $childObj = ChildProfileBasic::model()->findAll($criteira);
            $childParents = array();
            $nextSchool = array();
            $childGender = array();
            foreach($childObj as $child){
                $childParents[$child->childid] = $child->getParents();
                $nextSchool[$child->childid] = $child->nextYear->schoolid   ;
                $childGender[$child->childid] = $child->gender;
            }

            $branch = Branch::model()->getBranchList(null, true);
            $childGenderArr = array('1' => "男", 2 => "女");
            $status = array(1 => "是", 2 => "否");

            $csv = $this->_t('孩子ID').",";
            $csv .= $this->_t('孩子英文姓名').",";
            $csv .= $this->_t('孩子中文姓名').",";
            $csv .= $this->_t('父亲名字').",";
            $csv .= $this->_t('父亲电话').",";
            $csv .= $this->_t('母亲名字').",";
            $csv .= $this->_t('母亲电话').",";
            $csv .= $this->_t('学校').",";
            $csv .= $this->_t('第几步').",";
            $csv .= $this->_t('班级 ').",";
            switch ($step)
            {
                case 1:
                    $needBus = array(1 => "双程乘坐", 2 => "早上乘坐", 3 => "下午乘坐");
                    $csv .= $this->_t('家庭住址 ').",";
                    $csv .= $this->_t('是否需要校车').",";
                    $csv .= $this->_t('乘坐日期').",";
                    $csv .= $this->_t('乘坐方式').",";
                    $csv .= $this->_t('下车后是否自己回家').",";
                    $csv .= $this->_t('时间')."\r\n";
                    foreach($regexyraInfoObj as $info){
                        $dateContent = json_decode($info->data);
                        $criteria = new CDbCriteria();
                        $criteria->compare('childid', $info->childid);
                        $criteria->compare('stat', 20);
                        $childClass = ChildReserve::model()->find($criteria);

                        $csv .= $this->_t($info->childid).",";
                        $csv .= $this->_t((isset($childObj[$info->childid])) ? $childObj[$info->childid]->getChildName() : "").",";
                        $csv .= $this->_t((isset($childObj[$info->childid]->name_cn)) ? $childObj[$info->childid]->name_cn : "").",";
                        $csv .= $this->_t(isset($childParents[$info->childid]['father']) ? $childParents[$info->childid]['father']->getName() : "").",";
                        $csv .= $this->_t(isset($childParents[$info->childid]['father']) ? $childParents[$info->childid]['father']['parent']->mphone : "").",";
                        $csv .= $this->_t(isset($childParents[$info->childid]['mother']) ? $childParents[$info->childid]['mother']->getName() : "").",";
                        $csv .= $this->_t(isset($childParents[$info->childid]['mother']) ? $childParents[$info->childid]['mother']['parent']->mphone : "").",";
                        $csv .= $this->_t($branch[$info->schoolid]['title']).",";
                        $csv .= $this->_t($info->step).",";
                        $csv .= $this->_t($childClass->ivyclass->title).",";
                        $csv .= $this->_t($dateContent->address).",";
                        $csv .= $this->_t(($dateContent->needBus) ? "是" : "否").",";
                        $csv .= $this->_t(($dateContent->needBus == 1) ? $dateContent->startingFrom : "").",";
                        $csv .= $this->_t(($dateContent->needBus == 1) ? $needBus[$dateContent->journey] : "").",";
                        $themselves = ($dateContent->themselves == 1) ? "是" : "否";
                        $csv .= $this->_t(($dateContent->needBus == 1) ? $themselves : "").",";
                        $csv .= $this->_t(date("Y-m-d", $info->updated)).",";
                        $csv .= "\r\n";
                    }
                    file_put_contents($step . " - " . time().'.csv', $csv);
                    break;
                case 2:
                    $csv .= $this->_t('接送人1 - 姓名 ').",";
                    $csv .= $this->_t('接送人1 - 关系').",";
                    $csv .= $this->_t('接送人1 - 电话').",";
                    $csv .= $this->_t('接送人2 - 姓名 ').",";
                    $csv .= $this->_t('接送人2 - 关系').",";
                    $csv .= $this->_t('接送人2 - 电话').",";
                    $csv .= $this->_t('接送人3 - 姓名 ').",";
                    $csv .= $this->_t('接送人3 - 关系').",";
                    $csv .= $this->_t('接送人3 - 电话').",";
                    $csv .= $this->_t('接送人4 - 姓名 ').",";
                    $csv .= $this->_t('接送人4 - 关系').",";
                    $csv .= $this->_t('接送人4 - 电话').",";
                    $csv .= $this->_t('时间')."\r\n";
                    foreach($regexyraInfoObj as $info){
                        $dateContent = json_decode($info->data);
                        $criteria = new CDbCriteria();
                        $criteria->compare('childid', $info->childid);
                        $criteria->compare('stat', 20);
                        $childClass = ChildReserve::model()->find($criteria);
                        $csv .= $this->_t($info->childid).",";
                        $csv .= $this->_t((isset($childObj[$info->childid])) ? $childObj[$info->childid]->getChildName() : "").",";
                        $csv .= $this->_t((isset($childObj[$info->childid]->name_cn)) ? $childObj[$info->childid]->name_cn : "").",";
                        $csv .= $this->_t(isset($childParents[$info->childid]['father']) ? $childParents[$info->childid]['father']->getName() : "").",";
                        $csv .= $this->_t(isset($childParents[$info->childid]['father']) ? $childParents[$info->childid]['father']['parent']->mphone : "").",";
                        $csv .= $this->_t(isset($childParents[$info->childid]['mother']) ? $childParents[$info->childid]['mother']->getName() : "").",";
                        $csv .= $this->_t(isset($childParents[$info->childid]['mother']) ? $childParents[$info->childid]['mother']['parent']->mphone : "").",";
                        $csv .= $this->_t($branch[$nextSchool[$info->childid]]['title']).",";
                        $csv .= $this->_t($info->step).",";
                        $csv .= $this->_t($childClass->ivyclass->title).",";
                        foreach($dateContent as $content){
                            $csv .= $this->_t($content->name).",";
                            $csv .= $this->_t($content->relation).",";
                            $csv .= $this->_t($content->tel).",";
                        }
                        $csv .= $this->_t(date("Y-m-d", $info->updated)).",";
                        $csv .= "\r\n";
                    }
                    file_put_contents($step . " - " . time().'.csv', $csv);
                    break;
                case 3:
                    $csv .= $this->_t('性别').",";
                    $csv .= $this->_t('校服尺码').",";
                    $csv .= $this->_t('时间')."\r\n";
                    foreach($regexyraInfoObj as $info){
                        $dateContent = json_decode($info->data);
                        $criteria = new CDbCriteria();
                        $criteria->compare('childid', $info->childid);
                        $criteria->compare('stat', 20);
                        $childClass = ChildReserve::model()->find($criteria);
                        $csv .= $this->_t($info->childid).",";
                        $csv .= $this->_t((isset($childObj[$info->childid])) ? $childObj[$info->childid]->getChildName() : "").",";
                        $csv .= $this->_t((isset($childObj[$info->childid]->name_cn)) ? $childObj[$info->childid]->name_cn : "").",";
                        $csv .= $this->_t(isset($childParents[$info->childid]['father']) ? $childParents[$info->childid]['father']->getName() : "").",";
                        $csv .= $this->_t(isset($childParents[$info->childid]['father']) ? $childParents[$info->childid]['father']['parent']->mphone : "").",";
                        $csv .= $this->_t(isset($childParents[$info->childid]['mother']) ? $childParents[$info->childid]['mother']->getName() : "").",";
                        $csv .= $this->_t(isset($childParents[$info->childid]['mother']) ? $childParents[$info->childid]['mother']['parent']->mphone : "").",";
                        $csv .= $this->_t($branch[$info->schoolid]['title']).",";
                        $csv .= $this->_t($info->step).",";
                        $csv .= $this->_t($childClass->ivyclass->title).",";
                        $csv .= $this->_t($childGenderArr[$childGender[$info->childid]['gender']]).",";
                        $csv .= $this->_t($dateContent->size).",";
                        $csv .= $this->_t(date("Y-m-d", $info->updated)).",";
                        $csv .= "\r\n";
                    }
                    file_put_contents($step . " - " . time().'.csv', $csv);
                    break;
                case 4:

                    $csv .= $this->_t('意向医院1').",";
                    $csv .= $this->_t('意向医院2').",";
                    $csv .= $this->_t('保险公司').",";
                    $csv .= $this->_t('紧急联系人1').",";
                    $csv .= $this->_t('紧急联系人电话1').",";
                    $csv .= $this->_t('紧急联系人2').",";
                    $csv .= $this->_t('紧急联系人电话2').",";
                    $csv .= $this->_t('多动症').",";
                    $csv .= $this->_t('心脏病').",";
                    $csv .= $this->_t('过敏').",";
                    $csv .= $this->_t('耳部疾病').",";
                    $csv .= $this->_t('哮喘').",";
                    $csv .= $this->_t('肝炎').",";
                    $csv .= $this->_t('背部或脊柱问题').",";
                    $csv .= $this->_t('消化系统疾病').",";
                    $csv .= $this->_t('骨折').",";
                    $csv .= $this->_t('皮肤病').",";
                    $csv .= $this->_t('糖尿病').",";
                    $csv .= $this->_t('视力问题').",";
                    $csv .= $this->_t('癫病').",";
                    $csv .= $this->_t('结核').",";
                    $csv .= $this->_t('食物过敏原').",";
                    $csv .= $this->_t('其他疾病').",";
                    $csv .= $this->_t('时间')."\r\n";
                    foreach($regexyraInfoObj as $info){
                        $dateContent = json_decode($info->data);
                        $criteria = new CDbCriteria();
                        $criteria->compare('childid', $info->childid);
                        $criteria->compare('stat', 20);
                        $childClass = ChildReserve::model()->find($criteria);
                        $csv .= $this->_t($info->childid).",";
                        $csv .= $this->_t((isset($childObj[$info->childid])) ? $childObj[$info->childid]->getChildName() : "").",";
                        $csv .= $this->_t((isset($childObj[$info->childid]->name_cn)) ? $childObj[$info->childid]->name_cn : "").",";
                        $csv .= $this->_t(isset($childParents[$info->childid]['father']) ? $childParents[$info->childid]['father']->getName() : "").",";
                        $csv .= $this->_t(isset($childParents[$info->childid]['father']) ? $childParents[$info->childid]['father']['parent']->mphone : "").",";
                        $csv .= $this->_t(isset($childParents[$info->childid]['mother']) ? $childParents[$info->childid]['mother']->getName() : "").",";
                        $csv .= $this->_t(isset($childParents[$info->childid]['mother']) ? $childParents[$info->childid]['mother']['parent']->mphone : "").",";
                        $csv .= $this->_t($branch[$nextSchool[$info->childid]]['title']).",";
                        $csv .= $this->_t($info->step).",";
                        $csv .= $this->_t($childClass->ivyclass->title).",";
                        $csv .= $this->_t($dateContent->preferredHospitalOne).",";
                        $csv .= $this->_t($dateContent->preferredHospitalTwo).",";
                        $csv .= $this->_t($dateContent->insuranceCompany).",";
                        $csv .= $this->_t($dateContent->oneEmergencyName).",";
                        $csv .= $this->_t($dateContent->oneEmergencyPhone).",";
                        $csv .= $this->_t($dateContent->twoEmergencyName).",";
                        $csv .= $this->_t($dateContent->twoEmergencyPhone).",";
                        $csv .= $this->_t($status[$dateContent->ADHD]).",";
                        $csv .= $this->_t($status[$dateContent->heartDisorder]).",";
                        $csv .= $this->_t($status[$dateContent->allergies]).",";
                        $csv .= $this->_t($status[$dateContent->frequent]).",";
                        $csv .= $this->_t($status[$dateContent->asthma]).",";
                        $csv .= $this->_t($status[$dateContent->hepatitis]).",";
                        $csv .= $this->_t($status[$dateContent->problems]).",";
                        $csv .= $this->_t($status[$dateContent->gastrointertianl]).",";
                        $csv .= $this->_t($status[$dateContent->fractures]).",";
                        $csv .= $this->_t($status[$dateContent->skinProblems]).",";
                        $csv .= $this->_t($status[$dateContent->diabetes]).",";
                        $csv .= $this->_t($status[$dateContent->visionProblems]).",";
                        $csv .= $this->_t($status[$dateContent->seizureDisorde]).",";
                        $csv .= $this->_t($status[$dateContent->tuberculosis]).",";
                        $csv .= $this->_t('"' . $dateContent->specialFood . '"').",";
                        $csv .= $this->_t('"' . $dateContent->other. '"').",";
                        $csv .= $this->_t(date("Y-m-d", $info->updated)).",";
                        $csv .= "\r\n";
                    }
                    file_put_contents($step . " - " . time().'.csv', $csv);
                    break;
                case 5:
                    $lunchs = array("否","是");
                    $csv .= $this->_t('是否用午餐').",";
                    $csv .= $this->_t('时间')."\r\n";
                    foreach($regexyraInfoObj as $info){
                        $dateContent = json_decode($info->data);
                        $criteria = new CDbCriteria();
                        $criteria->compare('childid', $info->childid);
                        $criteria->compare('stat', 20);
                        $childClass = ChildReserve::model()->find($criteria);
                        $csv .= $this->_t($info->childid).",";
                        $csv .= $this->_t((isset($childObj[$info->childid])) ? $childObj[$info->childid]->getChildName() : "").",";
                        $csv .= $this->_t((isset($childObj[$info->childid]->name_cn)) ? $childObj[$info->childid]->name_cn : "").",";
                        $csv .= $this->_t(isset($childParents[$info->childid]['father']) ? $childParents[$info->childid]['father']->getName() : "").",";
                        $csv .= $this->_t(isset($childParents[$info->childid]['father']) ? $childParents[$info->childid]['father']['parent']->mphone : "").",";
                        $csv .= $this->_t(isset($childParents[$info->childid]['mother']) ? $childParents[$info->childid]['mother']->getName() : "").",";
                        $csv .= $this->_t(isset($childParents[$info->childid]['mother']) ? $childParents[$info->childid]['mother']['parent']->mphone : "").",";
                        $csv .= $this->_t($branch[$nextSchool[$info->childid]]['title']).",";
                        $csv .= $this->_t($info->step).",";
                        $csv .= $this->_t($childClass->ivyclass->title).",";
                        $csv .= $this->_t($lunchs[$dateContent->lunch]).",";
                        $csv .= $this->_t(date("Y-m-d", $info->updated)).",";
                        $csv .= "\r\n";
                    }
                    file_put_contents($step . " - " . time().'.csv', $csv);
                    break;
                case 6:
                    $langModels = Term::model()->lang()->findAll();
                    //$titleLang = (Yii::app()->language == "zh_cn") ? "entitle" : "cntitle";
                    $langcn = CHtml::listData($langModels, 'diglossia_id', "cntitle");
                    $csv .= $this->_t('孩子第一种语言').",";
                    $csv .= $this->_t('家庭常用语言').",";
                    $csv .= $this->_t('常用语言').",";
                    $csv .= $this->_t('时间')."\r\n";
                    foreach($regexyraInfoObj as $info){
                        $dateContent = json_decode($info->data);
                        $criteria = new CDbCriteria();
                        $criteria->compare('childid', $info->childid);
                        $criteria->compare('stat', 20);
                        $childClass = ChildReserve::model()->find($criteria);
                        $csv .= $this->_t($info->childid).",";
                        $csv .= $this->_t((isset($childObj[$info->childid])) ? $childObj[$info->childid]->getChildName() : "").",";
                        $csv .= $this->_t((isset($childObj[$info->childid]->name_cn)) ? $childObj[$info->childid]->name_cn : "").",";
                        $csv .= $this->_t(isset($childParents[$info->childid]['father']) ? $childParents[$info->childid]['father']->getName() : "").",";
                        $csv .= $this->_t(isset($childParents[$info->childid]['father']) ? $childParents[$info->childid]['father']['parent']->mphone : "").",";
                        $csv .= $this->_t(isset($childParents[$info->childid]['father']) ? $childParents[$info->childid]['father']->email : "").",";
                        $csv .= $this->_t(isset($childParents[$info->childid]['mother']) ? $childParents[$info->childid]['mother']->getName() : "").",";
                        $csv .= $this->_t(isset($childParents[$info->childid]['mother']) ? $childParents[$info->childid]['mother']['parent']->mphone : "").",";
                        $csv .= $this->_t(isset($childParents[$info->childid]['mother']) ? $childParents[$info->childid]['mother']->email : "").",";
                        $csv .= $this->_t($branch[$nextSchool[$info->childid]]['title']).",";
                        $csv .= $this->_t($info->step).",";
                        $csv .= $this->_t($childClass->ivyclass->title).",";
                        $csv .= $this->_t($langcn[$dateContent->firstLanguage]).",";
                        $csv .= $this->_t($langcn[$dateContent->familyLanguage]).",";
                        $csv .= $this->_t($langcn[$dateContent->commonlyLanguage]).",";
                        $csv .= $this->_t(date("Y-m-d", $info->updated)).",";
                        $csv .= "\r\n";
                    }
                    file_put_contents($step . " - " . time().'.csv', $csv);
                    break;
                default:
                    $sta = array(1 => "是");
                    $csv .= $this->_t('协议是否同意').",";
                    $csv .= $this->_t('时间')."\r\n";
                    foreach($regexyraInfoObj as $info){
                        $dateContent = json_decode($info->data);
                        $criteria = new CDbCriteria();
                        $criteria->compare('childid', $info->childid);
                        $criteria->compare('stat', 20);
                        $childClass = ChildReserve::model()->find($criteria);
                        $csv .= $this->_t($info->childid).",";
                        $csv .= $this->_t((isset($childObj[$info->childid])) ? $childObj[$info->childid]->getChildName() : "").",";
                        $csv .= $this->_t((isset($childObj[$info->childid]->name_cn)) ? $childObj[$info->childid]->name_cn : "").",";
                        $csv .= $this->_t(isset($childParents[$info->childid]['father']) ? $childParents[$info->childid]['father']->getName() : "").",";
                        $csv .= $this->_t(isset($childParents[$info->childid]['father']) ? $childParents[$info->childid]['father']['parent']->mphone : "").",";
                        $csv .= $this->_t(isset($childParents[$info->childid]['mother']) ? $childParents[$info->childid]['mother']->getName() : "").",";
                        $csv .= $this->_t(isset($childParents[$info->childid]['mother']) ? $childParents[$info->childid]['mother']['parent']->mphone : "").",";
                        $csv .= $this->_t($branch[$info->schoolid]['title']).",";
                        $csv .= $this->_t($info->step).",";
                        $csv .= $this->_t($childClass->ivyclass->title).",";
                        $csv .= $this->_t($sta[$dateContent->agree]).",";
                        $csv .= $this->_t(date("Y-m-d", $info->updated)).",";
                        $csv .= "\r\n";
                    }
                    file_put_contents($step . " - " . time().'.csv', $csv);
            }

        }
    }

    public function actionExportParents($schoolid = 'BJ_SLT', $type = 2018)
    {
        Yii::import('common.models.regextrainfo.*');
        Yii::import('common.models.invoice.*');
        $criteira = new CDbCriteria();
        $criteira->compare('step', 2);
        $criteira->compare('status', 1);
        $criteira->compare('type', $type);
        $criteira->compare('schoolid', $schoolid);
        $criteira->order = "childid DESC";
        $criteira->index = "childid";
        $regexyraInfoObj = RegExtraInfo::model()->findAll($criteira);

        $criteira = new CDbCriteria();
        $criteira->compare('childid', array_keys($regexyraInfoObj));
        $criteira->index = 'childid';
        $childObj = ChildProfileBasic::model()->findAll($criteira);

        //$childParents = array();
        $nextSchool = array();
        $childGender = array();
        foreach($childObj as $child){
            //$childParents[$child->childid] = $child->getParents();
            $nextSchool[$child->childid] = $child->nextYear->schoolid;
            $childGender[$child->childid] = $child->gender;
        }

        $path = 'F:/WWW/mimsa/trunk/oasrc/XOOPS-data/asa/childParents';

        $osscs = CommonUtils::initOSSCS('private');

        if (!file_exists($path)) {
            mkdir($path, 0777);
        }

        foreach($regexyraInfoObj as $info){
            $a = 0;
            $childNameEn = $this->_t(isset($childObj[$info->childid]) ? $childObj[$info->childid]->getChildName() : "");
            $childNameCn = $this->_t((isset($childObj[$info->childid]->name_cn)) ? $childObj[$info->childid]->name_cn : "");
            $paths = 'F:/WWW/mimsa/trunk/oasrc/XOOPS-data/asa/childParents/' . $info->childid . '_'. $childNameEn . "_" . $childNameCn;
            if (!file_exists($paths)) {
                mkdir($paths, 0777);
            }

            $dateContent = json_decode($info->data);

            foreach($dateContent as $k=>$content){
                if($content->photo){
                    $url = $osscs->getImageUrl('regextrainfo/' . $content->photo);

                    $qrcodeurl = file_get_contents($url);
                    $red = file_put_contents($paths . '/' . $this->_t($content->name) . '_' . $this->_t($content->relation) . "." . end(explode(".",$content->photo)), $qrcodeurl);
                    if($red){
                       $a++;
                    }
                }
            }
            echo $info->childid . "____" . $a . "\n";
        }

    }

    //导出预约参观  孩子姓名  生日 如何知道我们 学校 关注点（以逗号分隔）  参数（开始时间,结束时间）
    public function actionExportVisit($startTime = 0, $endTime = 0)
    {
        Yii::import('common.models.visit.IvyschoolsVisit');

        $branch = Branch::model()->getBranchList(null, true, "<>10","");

        $criteira = new CDbCriteria();
        if($startTime && $endTime){
            $criteira->compare('visit_date', ">{$startTime}");
            $criteira->compare('visit_date', "<{$endTime}");
        }
        $criteira->order = "schoolid DESC, visit_date DESC";
        $schoolsVisit = IvyschoolsVisit::model()->findAll($criteira);
        if($schoolsVisit){
            $csv = $this->_t('孩子姓名').",";
            $csv .= $this->_t('孩子生日').",";
            $csv .= $this->_t('学校').",";
            $csv .= $this->_t('如何知道我们').",";
            $csv .= $this->_t('关注点').",";
            $csv .= "\r\n";
            foreach($schoolsVisit as $item){
                $config = OA::LoadConfig('CfgVisit');
                if (in_array($item->schoolid,array('BJ_DS','BJ_SLT','BJ_IASLT'))) {
                    $config = OA::LoadConfig('CfgDsVisit');
                }
                $knowusArr = array();
                $concer = explode('|',$item->concerns);
                foreach($concer as $items){
                    $knowusArr[] = $config['concerns'][$items]['cn'];
                }
                $csv .= $this->_t($item->child_name).",";
                $csv .= $this->_t(date("Y-m-d", $item->birthdate)).",";
                $csv .= $this->_t($branch[$item->schoolid]['title']).",";
                $csv .= $this->_t($config['knowus'][$item->knowus]['cn']).",";
                $csv .= $this->_t(implode(',',$knowusArr)).",";
                $csv .= "\r\n";
            }
            file_put_contents(time().'.csv', $csv);
        }

    }

    public function actionExportDome()
    {
        Yii::import('common.models.asainvoice.AsaDomeday');
        $criteira = new CDbCriteria();
        $criteira->compare('tag', "20180820demo");
        $criteira->compare('channel', "wx");
        $domeObj = AsaDomeday::model()->findAll($criteira);
        $config = AsaDomeday::getConfig();
        $gender= array(1=>"男",2=>"女");
        if($domeObj){
            $csv = $this->_t('编号').",";
            $csv .= $this->_t('家长姓名').",";
            $csv .= $this->_t('家长电话').",";
            $csv .= $this->_t('来源').",";
            $csv .= $this->_t('孩子信息').",";
            foreach ($config as $items ) {
                $csv .= $this->_t($items['title'] . " " . $items['time']).",";
            }
            $csv .= $this->_t('其他').",";
            $csv .= "\r\n";
            $num = 1;
            foreach($domeObj as $item){
                $csv .= $this->_t($num).",";
                $csv .= $this->_t($item->parent_name).",";
                $csv .= $this->_t($item->parents_tel).",";
                $csv .= $this->_t($item->channel).",";
                $courseTime = CJSON::decode($item->chilids);
                $a = "";
                foreach($courseTime as $ite){
                    $a .= $ite['name'] . ' ' . $ite['year'] . '年' . $ite['month'] . '月 ' . $gender[$ite['gender']] . PHP_EOL;
                }
                $physical_disabilities = '"' . str_replace(PHP_EOL, "\r\n",  $a ) . '"';
                $csv .= $this->_t($physical_disabilities).",";
                $courseTime = CJSON::decode($item->course_time);
                foreach ($config as $k => $items ) {
                    $csv .= $this->_t((in_array($k, $courseTime)) ? 1 : "" ).",";
                }
                $csv .= $this->_t($item->other).",";
                $csv .= "\r\n";
                $num++;
            }
            file_put_contents(time().'.csv', $csv);
        }
    }

    public function actionExportChildDome()
    {
        Yii::import('common.models.asainvoice.AsaDomeday');

        $criteira = new CDbCriteria();
        $criteira->compare('tag', "20180820demo");
        $criteira->compare('channel', "wx");
        $domeObj = AsaDomeday::model()->findAll($criteira);
        $config = AsaDomeday::getConfig();
        $gender= array(1=>"男",2=>"女");
        if($domeObj){
            $csv = $this->_t('编号').",";
            $csv .= $this->_t('家长姓名').",";
            $csv .= $this->_t('家长电话').",";
            $csv .= $this->_t('来源').",";
            $csv .= $this->_t('孩子名字').",";
            $csv .= $this->_t('孩子生日').",";
            $csv .= $this->_t('孩子性别').",";
            foreach ($config as $items ) {
                $csv .= $this->_t($items['title'] . " " . $items['time']).",";
            }
            $csv .= "\r\n";
            $num = 1;
            foreach($domeObj as $item){
                $courseTime = CJSON::decode($item->chilids);
                foreach($courseTime as $ite){
                    $csv .= $this->_t($num).",";
                    $csv .= $this->_t($item->parent_name).",";
                    $csv .= $this->_t($item->parents_tel).",";
                    $csv .= $this->_t($item->channel).",";
                    $csv .= $this->_t($ite['name']).",";
                    $csv .= $this->_t($ite['year'] . '年' . $ite['month'] . '月 ').",";
                    $csv .= $this->_t($gender[$ite['gender']]).",";
                    $courseTime = CJSON::decode($item->course_time);
                    foreach ($config as $k => $items ) {
                        $csv .= $this->_t((in_array($k, $courseTime)) ? 1 : "" ).",";
                    }
                    $csv .= "\r\n";
                    $num++;
                }
            }
            file_put_contents(time().'.csv', $csv);
        }
    }

    public function actionResetChildUfida($schoolid = '')
    {
        Yii::import('common.models.child.ChildinfoForUfidaTask');

        if ($schoolid) {
            $items = ChildProfileBasic::model()->findAllByAttributes(array('schoolid'=>$schoolid));
            foreach ($items as $item) {
                $criteria = new CDbCriteria();
                $criteria->compare('schoolid', $schoolid);
                $criteria->compare('childid', $item->childid);
                $uModel = ChildinfoForUfidaTask::model()->find($criteria);
                if (!$uModel) {
                    $uModel = new ChildinfoForUfidaTask();
                }
                $uModel->childid = $item->childid;
                $uModel->schoolid = $schoolid;
                $uModel->status = 0;
                if(!$uModel->save()){
                    echo $item->childid.": ivy_childinfo_for_ufida_task fail \n";
                }
                else {
                    echo $item->childid.": ok\n";
                }
            }
        }
    }

    // 导出每个学校的来访记录和入学人数
    public function actionVisitConversion($start = '', $end = '')
    {
        Yii::import('common.models.visit.*');

        // 获取当前所有有效的校园
        $criteria = new CDbCriteria();
        $criteria->compare('status', 10);
        $criteria->compare('type', "<>10");
        $branch = Branch::model()->findAll($criteria);

        $crit = new CDbCriteria();
        $crit->compare('visit_timestamp', ">={$start}");
        $crit->compare('visit_timestamp', "<={$end}");
        $total = IvyVisitsRecord::model()->count($crit);
        $cycle = ceil($total/$this->batchNum);

        $visit = array();
        $childArr = array();
        for($i=0; $i<$cycle; $i++){
            $crit->limit=$this->batchNum;
            $crit->offset=$i*$this->batchNum;
            $recordModel = IvyVisitsRecord::model()->findAll($crit);

            $basicInfo = array();
            if($recordModel){
                foreach ($recordModel as $val) {
                    $visit[$val->schoolid][$val->id] = $val->id;
                    $basicInfo[$val->basic_id] = $val->basic_id;
                }
            }

            // 根据电话，获取是否在校园上学，在哪个校园上的数据
            $basicInfoModel = array();
            if($basicInfo){
                $criteria = new CDbCriteria();
                $criteria->compare('id',$basicInfo);
                $basicInfoModel = IvyVisitsBasicInfo::model()->findAll($criteria);
            }
            $mphone = array();
            if($basicInfoModel){
                foreach ($basicInfoModel as $val) {
                    if($val->tel){
                        $mphone[] = $val->tel;
                    }
                }
            }

            $parentModel = array();
            if($mphone){
                $criteria = new CDbCriteria();
                $criteria->compare('mphone', $mphone);
                $parentModel = IvyParent::model()->findAll($criteria);
            }

            if($parentModel){
                foreach ($parentModel as $tel) {
                    $criteria = new CDbCriteria();
                    $criteria->compare('fid', $tel->pid);
                    $fChild = ChildProfileBasic::model()->find($criteria);

                    $criteria = new CDbCriteria();
                    $criteria->compare('mid', $tel->pid);
                    $mChild = ChildProfileBasic::model()->find($criteria);

                    if($fChild){
                        $childArr[$fChild->schoolid][$fChild->childid] = $tel->mphone;
                    }

                    if($mChild){
                        $childArr[$mChild->schoolid][$mChild->childid] = $tel->mphone;
                    }
                }
            }
        }



        $csv = $this->_t('学校').",";
        $csv .= $this->_t('参观人数').",";
        $csv .= $this->_t('入园人数').",";
        $csv .= "\r\n";
        foreach($branch as $item){
            $url = "runtime/cache/". $item->branchid . "_text.txt";
            $fp  = fopen($url,'w');
            $a = '';
            if($childArr && $childArr[$item->branchid]){
                foreach ($childArr[$item->branchid] as $key=> $val) {
                    $a .= $key . " - " . $val . "\r\n ";
                }
            }
            fwrite($fp,$a);
            fclose($fp);
            $csv .= $this->_t($item->title).",";
            $csv .= $this->_t((isset($visit) && isset($visit[$item->branchid])) ? count($visit[$item->branchid]) : 0 ).",";
            $csv .= $this->_t((isset($childArr) && isset($childArr[$item->branchid])) ? count($childArr[$item->branchid]) : 0 ).",";
            $csv .= "\r\n";
        }
        file_put_contents(time().'.csv', $csv);
    }

    public function actionToBoard($date1='', $date2='', $schoolid='')
    {
        if($date1 && $date2 && $schoolid){
            $d1 = strtotime($date1);
            $d2 = strtotime($date2);

            $yid = 0;
            $ayid = 0;
            $sql = "select * from ivy_calendar_school s join ivy_calendar_yearly y on s.yid=y.yid where s.branchid='".$schoolid."'";
            $rs = Yii::app()->db->createCommand($sql)->queryAll();
            foreach ($rs as $r){
                $timepoints = explode(',', $r['timepoints']);
                if($timepoints[0]<$d2 && $timepoints[3]>$d2) {
                    $yid = $r['yid'];
                }
                if($timepoints[0]<$d1 && $timepoints[3]>$d1) {
                    $ayid = $r['yid'];
                }
            }

            $sql = "select childids1,childids2,period_timestamp from ivy_stats_child_count where schoolid='".$schoolid."' and classid=0 and period_timestamp in (".$d1.",".$d2.")";
            $rs = Yii::app()->db->createCommand($sql)->queryAll();
            $children1 = array();
            $children2 = array();
            foreach($rs as $r){
                $childStr = $r['childids1'];
                if($r['childids2']){
                    $childStr .= ','.$r['childids2'];
                }

                if($r['period_timestamp'] == $d1){
                    $children1 = explode(',', $childStr);
                }

                if($r['period_timestamp'] == $d2){
                    $children2 = explode(',', $childStr);
                }
            }
            if($children1 && $children2) {
                $retained = array_intersect($children2, $children1);
                $newstudent = array_diff($children2, $children1);
                $leavestudent = array_diff($children1, $children2);

                $data = array();
                if($retained) {
                    foreach ($retained as $childid) {
                        $cChild = $this->cChild($childid, $yid, $d2,$schoolid, $ayid);
                        if($cChild){
                            if ($cChild['discount'] == 100){
                                $data['retained'][$cChild['classType']]['nodiscount'][$childid]=array(
                                    'original_amount' => $cChild['original_amount'],
                                    'amount' => $cChild['amount'],
                                );
                            }
                            else{
                                $data['retained'][$cChild['classType']]['discount'][$childid]=array(
                                    'original_amount' => $cChild['original_amount'],
                                    'amount' => $cChild['amount'],
                                );
                            }
                        }
                    }
                }
                if($newstudent) {
                    foreach ($newstudent as $childid) {
                        $cChild = $this->cChild($childid, $yid, $d2,$schoolid, $ayid);
                        if($cChild){
                            if ($cChild['discount'] == 100){
                                $data['newstudent'][$cChild['classType']]['nodiscount'][$childid]=array(
                                    'original_amount' => $cChild['original_amount'],
                                    'amount' => $cChild['amount'],
                                );
                            }
                            else{
                                $data['newstudent'][$cChild['classType']]['discount'][$childid]=array(
                                    'original_amount' => $cChild['original_amount'],
                                    'amount' => $cChild['amount'],
                                );
                            }
                        }
                    }
                }
                if($leavestudent) {
                    foreach ($leavestudent as $childid) {
                        $cChild = $this->cChild($childid, $yid, $d2,$schoolid, $ayid);
                        if($cChild){
                            if ($cChild['discount'] == 100){
                                $data['leavestudent'][$cChild['classType']]['nodiscount'][$childid]=array(
                                    'original_amount' => $cChild['original_amount'],
                                    'amount' => $cChild['amount'],
                                );
                            }
                            else{
                                $data['leavestudent'][$cChild['classType']]['discount'][$childid]=array(
                                    'original_amount' => $cChild['original_amount'],
                                    'amount' => $cChild['amount'],
                                );
                            }
                        }
                    }
                }
//                print_r($data);
                $ratio = array();
                foreach ($data as $key=>$datum) {
                    echo $key."\n";
                    foreach ($datum as $classType=>$_datum){
                        $count = isset($_datum['nodiscount']) && $_datum['nodiscount'] ? count($_datum['nodiscount']) : 0;
                        $count1 = isset($_datum['discount']) && $_datum['discount'] ? count($_datum['discount']) : 0;
                        echo $classType.'[nodiscount]: '.$count."\n";
                        echo $classType.'[discount]: '.$count1;
                        echo "\n";

                        if ($count){
                            foreach ($_datum['nodiscount'] as $_ratio){
                                $ratio[$key][$classType]['original_amount'] += $_ratio['original_amount'];
                                $ratio[$key][$classType]['amount'] += $_ratio['amount'];
                            }
                        }

                        if ($count1){
                            foreach ($_datum['discount'] as $_ratio){
                                $ratio[$key][$classType]['original_amount'] += $_ratio['original_amount'];
                                $ratio[$key][$classType]['amount'] += $_ratio['amount'];
                            }
                        }
                    }
                    echo "\n";
                }
                print_r($ratio);
            }
        }
    }

    public function cChild($childid=0, $yid=0, $date = 0, $schoolid, $ayid)
    {
        if($childid && $yid && $date) {
            Yii::import('common.models.invoice.*');
            $sql = "select classid from ivy_child_study_history where schoolid='".$schoolid."' and childid='" . $childid . "' and calendar='" . $yid ."' order by timestamp DESC limit 1";
            $rs = Yii::app()->db->createCommand($sql)->queryRow();

            if (isset($rs) && isset($rs['classid'])){
                $classModel = IvyClass::model()->findByPk($rs['classid']);
            }
            else {
                $sql = "select classid from ivy_child_study_history where schoolid='".$schoolid."' and childid='" . $childid . "' and calendar='" . $ayid ."' order by timestamp DESC limit 1";
                $rs = Yii::app()->db->createCommand($sql)->queryRow();
                $classModel = IvyClass::model()->findByPk($rs['classid']);
            }

            $criteria = new CDbCriteria();
            $criteria->compare('calendar_id', $yid);
            $criteria->compare('childid', $childid);
            $criteria->compare('`inout`', 'in');
            $criteria->compare('startdate', "<={$date}");
            $criteria->compare('enddate', ">={$date}");
            $criteria->compare('status', array(10,20,30));
            $criteria->compare('payment_type', 'tuition');
            $invoiceModel = Invoice::model()->find($criteria);

            $discount = isset($invoiceModel->discount) ? $invoiceModel->discount->discount : 100;
            $amount = isset($invoiceModel) ? $invoiceModel->original_amount : 0;

            return array(
                'classType' => isset($classModel) ?$classModel->classtype : '',
                'original_amount' => $amount/($discount/100),
                'amount' => $amount,
                'discount' => $discount,

            );
        }
    }

    // 把新网站的数据导入微信小程序    转移时候账单问题。转之前一定要问清楚
    public function actionImport($aid)
    {
        Yii::import('common.models.visit.*');
        Yii::import('common.models.easyapply.*');
        $criteria = new CDbCriteria();
        $criteria->compare('id', $aid);
        $admissionDs = AdmissionsDs::model()->findAll($criteria);

        $time = time();
        $country = array(36 => 112, 175 => 251, 187=>263 ,188 => 264, 31=>107, 152=>228);  // 国籍
        $lang = array(1 => 311, 2 => 312, 67 => 317, 71=>321, 66=>316);  // 语言
        $grade = array(2 => 340, 3 => 330, 4 => 331, 5 => 332, 6 => 333,
            7 => 334, 8 => 335, 9 => 336, 10 => 337, 11 => 338, 12 => 339,);  // 年级

        $new_grade_DS = array(2 => 1, 3=> 2, 4 => 3, 6=> 55, 7 => 6, 9 => 8,10 => 9);
        $edu_background = array(1 => 0, 2=> 1, 3 => 2, 4 => 3, 5 => 4);

        $schoolList = array('DS' => 1, 'SLT' => 6);
        $complete_step = array(1 => 1, 2 => 1, 3 => 1, 4 => 1, 5 => 1);
        $num = 1;
        if($admissionDs) {
            foreach ($admissionDs as $value) {
                //  创建 Family表
                $family = new Family();
                $family->status = 10;
                $family->created_by = 0;
                $family->created_at = $time;
                $family->updated_by = 0;
                $family->updated_at = $time;
                $family->save();
                $familyData = array();
                //创建父母信息
                if ($value->father_name && $value->father_phone && $value->father_email) {
                    $fatherParent = new Parents();
                    $fatherParent->family_id = $family->id;
                    $fatherParent->name = $value->father_name;
                    $fatherParent->phone = $value->father_phone;
                    $fatherParent->email = $value->father_email;
                    $fatherParent->employer = $value->father_employer;
                    $fatherParent->position = $value->father_position;
                    $fatherParent->language = ($value->father_lang) ? $lang[$value->father_lang] : '';
                    $fatherParent->country = ($value->father_nation) ? $country[$value->father_nation] : '';
                    $fatherParent->edu_background = ($value->father_education) ? $edu_background[$value->father_education] : '';
                    $fatherParent->gender = 1;
                    $fatherParent->status = 10;
                    $fatherParent->created_by = 0;
                    $fatherParent->created_at = $time;
                    $fatherParent->updated_by = 0;
                    $fatherParent->updated_at = $time;
                    $fatherParent->save();
                    $familyData['father'] = $fatherParent->id;
                }
                if ($value->mother_name && $value->mother_phone && $value->mother_email) {
                    $motherParent = new Parents();
                    $motherParent->family_id = $family->id;
                    $motherParent->name = $value->mother_name;
                    $motherParent->phone = $value->mother_phone;
                    $motherParent->email = $value->mother_email;
                    $motherParent->employer = $value->mother_employer;
                    $motherParent->position = $value->mother_position;
                    $motherParent->language = ($value->mother_lang) ? $lang[$value->mother_lang] : '';
                    $motherParent->country = ($value->mother_nation) ? $country[$value->mother_nation] : '';
                    $motherParent->edu_background = ($value->mother_education) ? $edu_background[$value->mother_education] : '';
                    $motherParent->gender = 2;
                    $motherParent->status = 10;
                    $motherParent->created_by = 0;
                    $motherParent->created_at = $time;
                    $motherParent->updated_by = 0;
                    $motherParent->updated_at = $time;
                    $motherParent->is_creator = 1;
                    $motherParent->save();
                    $familyData['mother'] = $motherParent->id;
                }
                $this->saveParentChild($family, $familyData, 'parent');
                // 创建孩子信息
                $childModel = new Child();
                $childModel->family_id = $family->id;
                $childModel->first_name_cn = $value->cn_name_last;
                $childModel->last_name_cn = $value->cn_name;
                $childModel->first_name_en = $value->en_name_last;
                $childModel->middle_name_en = $value->en_name_middle;
                $childModel->last_name_en = $value->en_name;
                $childModel->birthday = $value->birthday;
                $childModel->child_photo = '';
                $childModel->gender = $value->gender;
                $childModel->country = $country[$value->nationality];
                $childModel->identity = $value->passportid;
                $childModel->mother_tongue = $lang[$value->native_lang];
                $childModel->now_grade = ''; // 班级
                $childModel->status = 10;
                $childModel->created_by = 0;
                $childModel->created_at = $time;
                $childModel->updated_by = 0;
                $childModel->updated_at = $time;
                $childModel->save();
                $this->saveParentChild($family, array($childModel->id), 'child');

                // 增加申请数据
                $apply = new Apply();
                $apply->child_id = $childModel->id;
                $apply->school_id = $schoolList[$value->school_id];
                $apply->family_id = $family->id;
                $apply->schoolyear_id = $schoolList[$value->school_id];
                $apply->grade_id = $value->school_id == 'SLT' ? 12 : $new_grade_DS[$value->start_grade];
                $apply->is_staff = $value->is_staff;
                $apply->many_children = 0;
                $apply->xueji = '';
                $apply->is_paid = 1;
                $apply->step = 5;
                $apply->complete_step = json_encode($complete_step);
                $apply->status = 20;
                $apply->created_by = 0;
                $apply->created_at = $time;
                $apply->updated_by = 0;
                $apply->updated_at = $time;
                $apply->save();
                $apply->serial_number = date("Ymd", time()) . $apply->id;
                $apply->save();

                for ($i = 1; $i < 5; $i++) {
                    $applyStep = new ApplyStep();
                    $step = 'Step' . $i;
                    $step = new $step();
                    $data = $step->processData($apply, $value);
                    $applyStep->apply_id = $apply->id;
                    $applyStep->step = $i;
                    $applyStep->data = $data;
                    $applyStep->status = 10;
                    $applyStep->created_by = 0;
                    $applyStep->created_at = $time;
                    $applyStep->updated_by = 0;
                    $applyStep->updated_at = $time;
                    $applyStep->updated_type = 2;
                    $applyStep->save();
                }

                // 增加一个已付账单表
                $invoiceModel = new Invoice();
                $invoiceModel->family_id = $family->id;
                $invoiceModel->child_id = $childModel->id;
                $invoiceModel->school_id = $schoolList[$value->school_id];
                $invoiceModel->parent_id = isset($fatherParent) ? $fatherParent->id : $motherParent->id;
                $invoiceModel->apply_id = $apply->id;
                $invoiceModel->nodiscount_amount = 0;
                $invoiceModel->amount_original = 0;
                $invoiceModel->amount_actual_pay = 0;
                $invoiceModel->invoice_title = '注册费/Application Fee';
                $invoiceModel->paytype = 1;
                $invoiceModel->pay_timestamp = $time;
                $invoiceModel->status = 20;
                $invoiceModel->created_by = 0;
                $invoiceModel->created_at = $time;
                $invoiceModel->updated_by = 0;
                $invoiceModel->updated_at = $time;
                $invoiceModel->save();
                echo $num . "\n";
                $num++;
            }
        }
    }

    public static function saveParentChild($family, $data = array(), $type = '')
    {
        Yii::import('common.models.easyapply.*');
        if($data){
            $time = time();
            foreach ($data as $item){
                $ParentChild = new ParentChild();
                $ParentChild->family_id = $family->id;
                $ParentChild->item_id = $item;
                $ParentChild->item_type = $type;
                $ParentChild->status = 10;
                $ParentChild->created_by = 0;
                $ParentChild->created_at = $time;
                $ParentChild->updated_by = 0;
                $ParentChild->updated_at = $time;
                $ParentChild->save();
                $parentChild_data[] = intval($item);
            }
            if($type == 'parent'){
                $family->parent_data = json_encode($parentChild_data);
            }
            if($type == 'child'){
                $family->child_data = json_encode($parentChild_data);
            }
            $family->save();
        }
    }


    public function actionEatest()
    {

        Yii::import('common.models.easyapply.*');
        $count = Child::model()->count();

        echo $count;
    }

    // 修复小程序面试数据

    public function actionRepairData()
    {
        Yii::import('common.models.easyapply.*');
        $criteria = new CDbCriteria();
        $criteria->compare('status', 20);
        $criteria->compare('is_paid', 1);
        $applyModel = Apply::model()->findAll($criteria);

        if($applyModel){
            $interview = array();
            foreach ($applyModel as $val){
                if($val->interview_id) {
                    $interview[$val->interview_id] = $val->interview_id;
                }
            }
            if($interview) {
                $cri = new CDbCriteria();
                $cri->addNotInCondition('id', $interview);
                $interviewModel = Interview::model()->findAll($cri);
                if ($interviewModel) {
                    $num = 1;
                    foreach ($interviewModel as $item) {
                        $item->status = 0;
                        $item->save();
                        echo $num . "\n";
                        $num++;
                    }
                }
            }
        }
    }

    public function actionExportPtc($schoolid, $startyear, $semester)
    {
        Yii::import('common.models.ptc.*');
        Yii::import('common.models.calendar.*');
        Yii::import('common.models.schoolbus.*');

        if(!$schoolid || !$startyear || !$semester){
            return false;
        }

        $criteria = new CDbCriteria();
        $criteria->compare('branchid', $schoolid);
        $criteria->compare('startyear', $startyear);
        $calendar = CalendarSchool::model()->find($criteria);
        $yid = $calendar->yid;

        $criteria = new CDbCriteria();
        $criteria->compare('school_id', $schoolid);
        $criteria->compare('startyear', $startyear);
        $criteria->compare('semester', $semester);
        $criteria->compare('calendar_id', $yid);
        $criteria->index = 'id';
        $planModel = ParentMeetingPlan::model()->findAll($criteria);

        if($planModel) {
            $childData = array();
            $classids = array();
            foreach ($planModel as $val) {
                $classids[$val->classid] = $val->classid;
                if (isset($val->items)) {
                    foreach ($val->items as $child) {
                        if ($child->childid) {
                            $childData[$child->childid] = array(
                                'playid' => $child->planid,
                                'target_date' => $child->target_date,
                                'timeslot' => $child->timeslot,
                                'classid' => $val->classid,
                            );
                        }
                    }
                }
            }
            if($classids){
                $criteria = new CDbCriteria();
                $criteria->compare('classid', $classids);
                $criteria->index = 'classid';
                $classModel = IvyClass::model()->findAll($criteria);
            }

            $csv = $this->_t('孩子ID').",";
            $csv .= $this->_t('孩子姓名') . ",";
            $csv .= $this->_t('班级') . ",";
            $csv .= $this->_t('校车') . ",";
            $csv .= $this->_t('家长会日期') . ",";
            $csv .= $this->_t('家长会时间') . ",";
            $csv .= "\r\n";
            if($childData) {
                $childModel = ChildProfileBasic::model()->findAllByPk(array_keys($childData));
                $criteria = new CDbCriteria();
                $criteria->compare('childid', array_keys($childData));
                $criteria->compare('startyear', $startyear);
                $criteria->compare('schoolid', $schoolid);
                $criteria->index = 'childid';
                $busChild = SchoolBusChild::model()->findAll($criteria);
                if($childModel) {
                    foreach ($childModel as $child) {
                        $csv .= $this->_t($child->childid) . ",";
                        $csv .= $this->_t($child->getChildName(false,false,true)) . ",";
                        $csv .= $this->_t($classModel[$childData[$child->childid]['classid']]->title) . ",";
                        $csv .= $this->_t(isset($busChild[$child->childid]) ? $busChild[$child->childid]->businfo->bus_title : '') . ",";
                        $csv .= $this->_t(date("Y-m-d", strtotime($childData[$child->childid]['target_date']))) . ",";
                        $csv .= $this->_t(str_replace(",", "-", $childData[$child->childid]['timeslot'])) . ",";
                        $csv .= "\r\n";
                    }
                }
            }
            file_put_contents(time().'.csv', $csv);
        }
    }

    /**
     * 修复批量退费导致的班级错误
     */
    public function actionFixRefund($exec = 0, $type = 'slt')
    {
        Yii::import('common.models.invoice.*');

        $yid = 117;
        if ($type=='slt'){
            $sql = "SELECT * FROM `ivy_ufida_income` WHERE `invoice_id` in (679657,679656,679655,679654,679639,679619,679618,679596,679591,679576,679573,679561,679552,679551,679539,679537,679533,679515,679508,679495,679487, 679488,679489,679490,679491,679492,679493,679494,679496,679497,679498,679499,679500,679501,679502,679503,679504,679505,679506,679507,679509,679510,679511,679512,679513,679514,679516,679517,679518,679519,679520,679521,679522,679523,679524,679525,679526,679527,679528,679529,679530,679531,679532,679534,679535,679536,679538,679540,679541,679542,679543,679544,679545,679546,679547,679548,679549,679550,679553,679554,679555,679556,679557,679558,679559,679560,679562,679563,679564,679565,679566,679567,679568,679569,679570,679571,679572,679574,679575,679577,679578,679579,679580,679581,679582,679583,679584,679585,679586,679587,679588,679589,679590,679592,679593,679594,679595,679597,679598,679599,679600,679601,679602,679603,679604,679605,679606,679607,679608,679609,679610,679611,679612,679613,679614,679615,679616,679617,679620,679621,679622,679623,679624,679625,679626,679627,679628,679629,679630,679631,679632,679633,679634,679635,679636,679637,679638,679640,679641,679642,679643,679644,679645,679646,679647,679648,679649,679650,679651,679652,679653,679658,679659,679660,679661) and timestmp=1601481599 and type='refund_fee_ufida' ORDER BY `id` DESC";
        }
        else if ($type=='ds'){
            $sql = "SELECT * FROM `ivy_ufida_income` WHERE `invoice_id` in (679200, 656027,678967,678968,678969,678970,678971,678972,678973,678974,678975,678976,678977,678978,678979,678980,678981,678982,678983,678984,678985,678986,678987,678988,678989,678990,678991,678992,678993,678994,678995,678996,678997,678998,678999,679000,679001,679002,679003,679004,679005,679006,679007,679008,679009,679010,679011,679012,679013,679014,679015,679016,679017,679018,679019,679020,679021,679022,679023,679024,679025,679026,679027,679028,679029,679030,679031,679032,679033,679034,679035,679036,679037,679038,679039,679040,679041,679042,679043,679044,679045,679046,679047,679048,679049,679050,679051,679052,679053,679054,679055,679056,679057,679058,679059,679060,679061,679062,679063,679064,679065,679066,679067,679068,679069,679070,679071,679072,679073,679074,679075,679076,679077,679078,679079,679080,679081,679082,679083,679084,679085,679086,679087,679088,679089,679090,679091,679092,679093,679094,679095,679096,679097,679098,679099,679100,679101,679102,679103,679104,679105,679106,679107,679108,679109,679110,679111,679112,679113,679114,679115,679116,679117,679118,679119,679120,679121,679122,679123,679124,679125,679126,679127,679128,679129,679130,679131,679132,679133,679134,679135,679136,679137,679138,679139,679140,679141,679142,679144,679145,679147,679148,679149,679150,679151,679152,679153,679154,679155,679156,679157,679158,679159,679160,679161,679162,679163,679164,679165,679166,679167,679168,679169,679170,679171,679172,679173,679174,679175,679176,679177,679178,679179,679180,679181,679182,679183,679184,679185,679186,679187,679188,679189,679190,679191,679192,679193,679194,679195,679196,679197,679198,679199,679201,679202,679203,679204,679205,679206,679207,679208,679209,679210,679211,679212,679213,679214,679215,679216,679217,679218,679219,679220,679221,679222,679223,679224,679226,679227,679228,679229,679230,679231,679232,679233,679234,679235,679237,679238,679239,679240,679241,679242,679243,679244,679245,679247,679248,679249,679250,679251,679252,679253,679256,679259,679261,679262,679263,679264,679265,679266,679267,679269,679270,679271,679272,679273,679274,679275,679277,679278,679279,679280,679281,679282,679283,679284,679285,679286,679287,679288,679289,679290,679291,679292,679293,679294,679295,679296,679297,679298,679300,679301,679302,679303,679304,679305,679306,679307,679308,679309,679310,679311,679312,679313,679314,679315,679316,679317,679318,679319,679320,679321,679322,679323,679324,679326,679327,679328,679332,679333,679334,679341,679342,679343,679344,679345,679346,679347,679348,679349,679350,679351,679352,679353,679354,679355,679356,679357,679358,679359,679360,679361,679362,679363,679364,679365,679366,679367,679368,679369,679370,679372,679373,679374,679375,679376,679377,679378,679379,679380,679381,679382,679383,679384,679385,679386,679387,679388,679389,679390,679391,679392,679393,679394,679395,679396,679397,679400,679401,679402,679403,679405,679406,679407,679408,679409,679410,679411,679412,679414,679415,679416,679418,679419,679420,679421,679423,679424,679425,679426,679427,679428,679429,679430,679431,679432,679433,679434,679435,679436,679437,679438,679439,679440,679441,679442,679443,679444,679446,679447,679449,679450,679452,679453,679454,679455,679456,679457,679458,679459,679460,679461,679462,679464,679466,679468,679469,679470,679471,679472,679475,679476,679477,679478,679479,679480,679481,679482,679485,679486,679487,679495,679508,679515,679533,679537,679539,679551,679552,679561,679573,679576,679591,679596,679618,679619,679639,679654,679655,679656,679657 )  and timestmp=1601481599 and type='refund_fee_ufida' ORDER BY `id` DESC";
        }
        else if ($type == 'qf') {
            $sql = "SELECT * FROM `ivy_ufida_income` WHERE `invoice_id` in ( 679143,679146,679225,679236,679246,679254,679255,679257,679258,679260,679268,679276,679299,679325,679329,679330,679331,679335,679336,679337,679338,679339,679340,679371,679398,679399,679404,679413,679417,679422,679445,679448,679451,679463,679465,679467,679473,679474,679483,679484)  and timestmp=1601481599 and type='refund_fee_ufida' ORDER BY `id` DESC";
        }
        $items = Yii::app()->db->createCommand($sql)->queryAll();
        $i=0;
        foreach($items as $item){
            $criteria = new CDbCriteria();
            $criteria->compare('childid', $item['childid']);
            $criteria->compare('schoolid', $item['schoolid']);
            $criteria->compare('calendar_id', $yid);
            $criteria->compare('payment_type', 'tuition');
            $criteria->compare('`inout`', 'in');
            $criteria->order = 'timestamp desc';
            $child = Invoice::model()->find($criteria);
            echo $item['id'];
            if($child) {
//                echo ' '.$child->classid;
                if ($exec) {
                    $model = UfidaIncome::model()->findByPk($item['id']);
                    $model->classid = $child->classid;
                    $model->status = 0;
                    if (!$model->save(false)) {
                        print_r($model->getErrors());die;
                    }
//                    echo ' ok';
                }
            }
            else {
                die('end');
            }
            echo "\n";
        }
    }

    public function actionToJack()
    {
        $criteria = new CDbCriteria();
        $criteria->index = 'branchid';
        $branch = Branch::model()->findAll($criteria);

        $criteria = new CDbCriteria();
        $total = ChildProfileBasic::model()->count($criteria);
        $cycle = ceil($total/$this->batchNum);
        $csv = '';
        for($i=0; $i<$cycle; $i++){
            $criteria->limit=$this->batchNum;
            $criteria->offset=$i*$this->batchNum;
            $items = ChildProfileBasic::model()->findAll($criteria);
            foreach($items as $item) {
                $csv .= $item->childid.',';
                $csv .= $this->_t1($item->name_cn).',';
                $csv .= $this->_t1($item->first_name_en).',';
                $csv .= $this->_t1($item->last_name_en).',';
                $csv .= $item->birthday_search.',';
                $csv .= $branch[$item->schoolid]->abb.',';
                $csv .= ($item->ivyclass ? $this->_t1($item->ivyclass->title) : '').',';

                $sql = "SELECT count(DISTINCT(yid)) as c FROM `ivy_notes_child` where childid = " . $item->childid;
                $rs = Yii::app()->db->createCommand($sql)->queryRow();
                $csv .= $rs['c'].",";

                if (in_array($item->schoolid, array('BJ_DS', 'BJ_SLT'))) {
                    $sql = "SELECT n.yid,c.schoolid FROM ivy_notes_child n join ivy_class_list c on n.classid=c.classid where childid = " . $item->childid . " and c.classtype in ('n', 'b', 'p', 'k', 'c', 'mt', 'mc', 'mk') order by n.startyear desc";
                    $rs = Yii::app()->db->createCommand($sql)->queryAll();
                    if ($rs) {
                        $ret = array();
                        foreach ($rs as $r) {
                            $ret[$r['yid']] = $r['schoolid'];
                        }
                        $csv .= count($ret).",";
                        $csv .= $branch[array_shift($ret)]->abb."\n";
                    }
                    else {
                        $csv .= ",\n";
                    }
                }
                else {
                    $csv .= ",\n";
                }

                echo ".";
            }
            echo $i."--\n";
        }
        file_put_contents('jack.csv', $csv);
    }

    public function actionEachSql()
    {
        $childStr = '17128,17802,17803,17804,17805,17806,17810,18169,18325,18617,18734,18736,18738,18744,15980,15981,16067,16171,16459,16514,17657,17659,17662,17663,17665,17667,17668,17670,17776,18674,15982,15983,15993,16031,16196,16669,17653,17654,17661,17664,17666,17712,17713,18007,18039,18250,12027,13467,13468,13507,14361,14967,15881,15883,15884,15888,15892,16298,16375,16399,17618,17619,18613,13471,13856,13857,14430,15093,15879,15885,15889,15890,15891,16440,16486,16627,17029,17427,17702,17976,11939,13647,13654,13660,13858,14759,16190,16191,18041,18207,18324,18707,18733,11327,11391,11649,11723,13203,13861,14947,17586,17589,17650,18040,18321,18322,18323,11720,12364,13338,13655,13658,13708,14428,16403,17588,17591,17592,17593,17642,18649,16093,15718,11694';
        $arr = explode(',', $childStr);
        foreach ($arr as $childid) {
            $sql = "insert into ivy_childinfo_for_ufida_task (childid, schoolid) values (".$childid.", 'BJ_IASLT');";
            echo $sql."\n";
        }
    }
}
