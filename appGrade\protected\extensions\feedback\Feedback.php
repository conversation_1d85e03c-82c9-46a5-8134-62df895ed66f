<?php
class Feedback extends CWidget{
    public $classId;
    public $weekNum;
    public $cssFile;
    public $assetsUrl;
	
	public function init(){
		Yii::import('common.models.feedback.Comments');

//        if($this->assetsUrl===null)
//			$this->assetsUrl=Yii::app()->getAssetManager()->publish(dirname(__FILE__) . '/assets', false, -1, YII_DEBUG);

        $cs = Yii::app()->clientScript;
        if (false !== $this->cssFile)
        {
            if (null === $this->cssFile)
//                $this->cssFile = $this->assetsUrl . '/css.css';
                $this->cssFile = Yii::app()->theme->baseUrl.'/widgets/feedback/css.css?t='.Yii::app()->params['refreshAssets'];
            $cs->registerCssFile($this->cssFile);
        }
        $cs->registerScriptFile(Yii::app()->theme->baseUrl.'/js/jscroll.js');
	}
	
	public function run(){
        
        Mims::LoadHelper('HtoolKits');

        $model=new Comments();
        
        $criteria = new CDbCriteria();
        $criteria->compare('com_child_id', $this->getController()->getChildId());
        $criteria->compare('com_class_id', $this->classId);
        $criteria->compare('com_week_num', $this->weekNum);
        $co = Comments::model()->count($criteria);

		$this->render('feedback', array('model'=>$model, 'classId'=>$this->classId, 'weekNum'=>$this->weekNum, 'co'=>$co));
	}
	
}