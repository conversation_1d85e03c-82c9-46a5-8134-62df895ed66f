<style>
    .switch {
        position: relative;
        display: inline-block;
    }

    .switch input {
        display: none;
    }

    .slider {
        margin: 0;
        display: inline-block;
        position: relative;
        width: 40px;
        height: 20px;
        border: 1px solid #dcdfe6;
        outline: none;
        border-radius: 10px;
        box-sizing: border-box;
        background: #dcdfe6;
        cursor: pointer;
        transition: border-color .31s, background-color .3s;
        vertical-align: middle;
    }

    .slider:before {
        content: "";
        position: absolute;
        top: 1px;
        left: 1px;
        border-radius: 50%;
        transition: all .3s;
        width: 16px;
        height: 16px;
        background-color: #fff;
    }

    input:checked+.slider {
        background-color: #2196F3;
    }

    input:checked+.slider:before {
        left: 100%;
        margin-left: -17px;
    }
</style>

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <h1 class="page-header">
                <span class="glyphicon glyphicon-cog"></span> 是否展示班级同学
            </h1>
        </div>
    </div>
    <div class="row">
        <div class="col-md-10">
            <?php foreach ($models as $model) : ?>
                <div class="row">
                    <div class="col-md-1">
                        <span><?php echo $model['schoolid']; ?>: </span>
                    </div>
                    <div class="col-md-1">                        
                        <label class="switch">
                            <input <?php echo $model['status'] == 1 ? 'checked="checked"' : ''; ?> onchange="change(this, '<?php echo $model['schoolid']; ?>')" type="checkbox"> <span class="slider"></span>
                        </label>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</div>

<script>
    function change(btn, schoolid) {
        var status = $(btn).is(':checked') ? 1 : 0;
        $.ajax({
            url: '<?php echo $this->createUrl('changeClassShow'); ?>',
            data: {schoolid, status},
            type: 'POST',
            dataType: 'json',
            success: function(data) {
                if (data.state === 'fail') {
                    head.dialog.alert(data.message);
                    return false;
                }
                resultTip({msg: data.message});
                if (typeof(data.callback) !== 'undefined') {
                    eval(data.callback + '(data.data)');
                }
            },
            error: function() {
                alert("访问异常！");
            }
        });
    }
</script>