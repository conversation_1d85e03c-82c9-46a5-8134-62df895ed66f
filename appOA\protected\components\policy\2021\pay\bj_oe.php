<?php
if(!defined("IVY_POLICY"))
throw new CException('DO NOT USE THIS FILE DIRECTLY');

return array( 'policy'=>array(

	//是否老生老办法
	ENABLE_CONSTANT_TUITION => true,
    // 按天收费
    FENTAN_FACTOR => DAILY,

	//年保育费
	//ANNUAL_CHILDCARE_AMOUNT => 8000,

	//是否开放课外活动账单
	ENABLE_AFTERSCHOOLS => true,

	//图书馆工本费
	FEETYPE_LIBCARD => 10,

	//学期四个时间点；取前后两端
	TIME_POINTS   => array(202108,202201,202202,202207),

	//年付开通哪些缴费类型
//	PAYGROUP_ANNUAL => array(
//		ITEMS => array(
//			FEETYPE_TUITION,
//			FEETYPE_LUNCH,
//			FEETYPE_SCHOOLBUS,
//		),
//		TUITION_CALC_BASE => BY_MONTH,
//	),
//
	//学期付开通哪些缴费类型
	PAYGROUP_SEMESTER => array(
		ITEMS => array(
			FEETYPE_TUITION,
			FEETYPE_LUNCH,
           FEETYPE_SCHOOLBUS,
		),
		TUITION_CALC_BASE => BY_SEMESTER,
	),
//
//    PAYGROUP_MONTH => array(
//        ITEMS => array(
//            FEETYPE_TUITION,
//            FEETYPE_LUNCH,
//            FEETYPE_SCHOOLBUS,
//        ),
//        TUITION_CALC_BASE => BY_MONTH,
//    ),

	//计算基准：BY_MONTH, 或者 BY_SETTING, 前者表示所有的学费都基于月费用计算，后者表示所有的学费都基于学期或学年
	TUITION_CALCULATION_BASE => array(


        BY_MONTH => array(

            //月收费金额
            AMOUNT => array(
               FULL5D => 10500,
               HALF5D => 7560,
                // FULL3D => 6840,
                // HALF3D => 5050,
                // FULL2D => 4750,
                // HALF2D => 4321,
            ),

            //收费月份，及其权重
            MONTH_FACTOR => array(
                202109=>1,
                202110=>1,
                202111=>1,
                202112=>.55,
                202201=>1,
                202202=>.55,
                202203=>1,
                202204=>1,
                202205=>1,
                202206=>1,
                202207=>1
            ),

            //折扣
            GLOBAL_DISCOUNT => array(
                DISCOUNT_ANNUAL => '1:1:2022-06-01:round',
                DISCOUNT_SEMESTER1 => '1:1:2022-02-01:round',
                DISCOUNT_SEMESTER2 => '1:1:2022-07-01:round'
            )
        ),

        BY_SEMESTER1 => array(
            AMOUNT => array(
                FULL5D => 99076,
//                FULL5D2 => 63662,
//                FULL5D3 => 70657,
                // HALF2D => 28500,
                // HALF3D => 41500,
                // HALF5D => 69664,
            ),
        ),

        BY_SEMESTER2 => array(
            AMOUNT => array(
                FULL5D => 85934,
//                FULL5D2 => 87628,
//                FULL5D3 => 80633,
                // FULL5D4 => 78668,
                // FULL5D5 => 72345,
                // HALF2D => 28500,
                // HALF3D => 41500,
                // HALF5D => 55322,
            ),
            ENDDATE => array(
                FULL5D => 1656000000,
                RS_FULL5D => 1658419200,
                LUNCHA => 1656000000,
                FULL5D2 => 1658419200,
                RS_FULL5D2 => 1658419200,
                LUNCHB => 1658419200,
                // 双语班校车费
                41637 => 1658419200,
                41638 => 1658419200,
                41639 => 1658419200,
                // 国际班校车费
                41648 => 1656000000,
                41649 => 1656000000,
                41650 => 1656000000,
                FULL5D3 => 1656000000,
                FULL5D4 => 1658419200,
                FULL5D5 => 1658419200,
            )
        ),
	),

	//每餐费用
    LUNCH_PERDAY => array(
        LUNCHA => 45,
        LUNCHB => 45,
    ),

	//账单到期日和账单开始日之差
	DUEDAYS_OFFSET => 1,

));