<?php if(isset($_GET['allinpay'])):?>
    <div class="pop_cont">
        <div class="tips_light tips_block">请使用通联POS机扫描或输入下面的条码获取订单金额</div>
        <?php
        $optionsArray = array(
            'elementId' => 'barcode',
            'value' => $ret['p2_Order'],
            'type' => 'code128',
            'settings' => array(
                'barWidth' => 2
            )
        );
        $this->widget("common.extensions.barcode.Barcode", $optionsArray);

        echo CHtml::form($url, 'post', array('name'=>'yeepay', 'target'=>'_blank', 'class'=>'tac'));
        foreach($ret as $key=>$v){
            echo CHtml::hiddenField($key, $v);
        }
        echo CHtml::button('已完成支付', array('class'=>'btn ml10', 'onclick'=>'reloadPage(window.parent);'));
        echo CHtml::endForm();
        ?>
    </div>

    <style>
        #barcode{
            margin: 0 auto;
        }
    </style>
<?php else:?>
<?php
echo CHtml::form($url, 'post', array('name'=>'yeepay'));
foreach($ret as $key=>$v){
    echo CHtml::hiddenField($key, $v);
}
echo CHtml::endForm();
?>
<script type="text/javascript">
document.yeepay.submit();
</script>
<?php endif;?>