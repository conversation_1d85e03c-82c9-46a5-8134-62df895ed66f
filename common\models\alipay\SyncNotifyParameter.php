<?php

/**
 * This is the model class for table "ivy_alipay_sync_notify_parameter".
 *
 * The followings are the available columns in table 'ivy_alipay_sync_notify_parameter':
 * @property integer $id
 * @property string $is_success
 * @property string $sign_type
 * @property string $sign
 * @property string $out_trade_no
 * @property string $subject
 * @property string $payment_type
 * @property string $exterface
 * @property string $trade_no
 * @property string $trade_status
 * @property string $notify_id
 * @property string $notify_time
 * @property string $notify_type
 * @property string $seller_email
 * @property string $buyer_email
 * @property string $seller_id
 * @property string $buyer_id
 * @property double $total_fee
 * @property string $body
 * @property string $bank_seq_no
 * @property string $extra_common_param
 * @property integer $update_timestamp
 */
class SyncNotifyParameter extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return SyncNotifyParameter the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_alipay_sync_notify_parameter';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('is_success, sign_type, sign', 'required'),
			array('update_timestamp', 'numerical', 'integerOnly'=>true),
			array('total_fee', 'numerical'),
			array('is_success, sign_type, payment_type, trade_status, notify_time, notify_type, seller_id, buyer_id', 'length', 'max'=>25),
			array('sign, out_trade_no, trade_no, seller_email, buyer_email, bank_seq_no', 'length', 'max'=>125),
			array('subject, notify_id, extra_common_param', 'length', 'max'=>255),
			array('exterface', 'length', 'max'=>50),
			array('body', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, is_success, sign_type, sign, out_trade_no, subject, payment_type, exterface, trade_no, trade_status, notify_id, notify_time, notify_type, seller_email, buyer_email, seller_id, buyer_id, total_fee, body, bank_seq_no, extra_common_param, update_timestamp', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'is_success' => 'Is Success',
			'sign_type' => 'Sign Type',
			'sign' => 'Sign',
			'out_trade_no' => 'Out Trade No',
			'subject' => 'Subject',
			'payment_type' => 'Payment Type',
			'exterface' => 'Exterface',
			'trade_no' => 'Trade No',
			'trade_status' => 'Trade Status',
			'notify_id' => 'Notify',
			'notify_time' => 'Notify Time',
			'notify_type' => 'Notify Type',
			'seller_email' => 'Seller Email',
			'buyer_email' => 'Buyer Email',
			'seller_id' => 'Seller',
			'buyer_id' => 'Buyer',
			'total_fee' => 'Total Fee',
			'body' => 'Body',
			'bank_seq_no' => 'Bank Seq No',
			'extra_common_param' => 'Extra Common Param',
			'update_timestamp' => 'Update Timestamp',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('is_success',$this->is_success,true);
		$criteria->compare('sign_type',$this->sign_type,true);
		$criteria->compare('sign',$this->sign,true);
		$criteria->compare('out_trade_no',$this->out_trade_no,true);
		$criteria->compare('subject',$this->subject,true);
		$criteria->compare('payment_type',$this->payment_type,true);
		$criteria->compare('exterface',$this->exterface,true);
		$criteria->compare('trade_no',$this->trade_no,true);
		$criteria->compare('trade_status',$this->trade_status,true);
		$criteria->compare('notify_id',$this->notify_id,true);
		$criteria->compare('notify_time',$this->notify_time,true);
		$criteria->compare('notify_type',$this->notify_type,true);
		$criteria->compare('seller_email',$this->seller_email,true);
		$criteria->compare('buyer_email',$this->buyer_email,true);
		$criteria->compare('seller_id',$this->seller_id,true);
		$criteria->compare('buyer_id',$this->buyer_id,true);
		$criteria->compare('total_fee',$this->total_fee);
		$criteria->compare('body',$this->body,true);
		$criteria->compare('bank_seq_no',$this->bank_seq_no,true);
		$criteria->compare('extra_common_param',$this->extra_common_param,true);
		$criteria->compare('update_timestamp',$this->update_timestamp);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}