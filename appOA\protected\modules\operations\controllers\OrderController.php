<?php

class OrderController extends BranchBasedController
{
	/**
	 * @var string the default layout for the views. Defaults to '//layouts/column2', meaning
	 * using two-column layout. See 'protected/views/layouts/column2.php'.
	 */
//	public $layout='//layouts/column2';
    
    public $cAdmin = false;	//是否有校园管理权限
    public $oAdmin = false; //是否总部后勤部权限
    public $defaultAction = 'admin';
    public $dialogWidth=500;
    
    public function init() {
        parent::init();
        Yii::import('common.models.points.*');
        
        $this->cAdmin = Yii::app()->user->checkAccess('tCampusAdmin');
        $this->oAdmin = Yii::app()->user->checkAccess('tOperationsAdmin');
    }
    
    public function actionSelect($type='opgcount')
	{
		if($this->multipleBranch):			
			$this->mainMenu = array(
				array(
					'label' => '订单管理',
					'url' => array('/operations/order/admin')
				),
				array(
					'label' => '商品管理',
					'url' => array('/operations/product/admin')
				),
			);
			
			$schools = $this->getAllBranch();

            if($type == 'opgcount')
			    $sql = 'select schoolid, count(*) as c from '.PointsOrder::model()->tableName().' where status in ('.PointsStatus::STATS_CREATED.','.PointsStatus::STATS_CONFIRMED.','.PointsStatus::STATS_READYTOSHIP.') and category="order" group by schoolid';
            else
                $sql = 'select schoolid, count(*) as c from '.PointsOrder::model()->tableName().' where status in ('.PointsStatus::STATS_SHIPPING.','.PointsStatus::STATS_RECEIVED.') and category="order" group by schoolid';
			$sc=Yii::app()->db->createCommand($sql)->queryAll();
			foreach ($sc as $_sc){
				if (in_array($_sc['schoolid'], array_keys($schools))){
					$schools[$_sc['schoolid']]['c']=$_sc['c'];
				}
			}
			
			$this->branchSelectParams["urlArray"] = array("//operations/order/admin", 'type'=>$type);
			$this->render('select', array('schools'=>$schools, 'type'=>$type));
		endif;
	}

	/**
	 * Displays a particular model.
	 * @param integer $id the ID of the model to be displayed
	 */
	public function actionView($id)
	{
		$this->render('view',array(
			'model'=>$this->loadModel($id),
		));
	}

	/**
	 * Updates a particular model.
	 * If update is successful, the browser will be redirected to the 'view' page.
	 * @param integer $id the ID of the model to be updated
	 */
	public function actionUpdate($id)
	{
		$model=$this->loadModel($id);

		// Uncomment the following line if AJAX validation is needed
		// $this->performAjaxValidation($model);

		if(isset($_POST['PointsOrder']))
		{
			$model->attributes=$_POST['PointsOrder'];
			if($model->save())
				$this->redirect(array('view','id'=>$model->id));
		}

		$this->render('update',array(
			'model'=>$model,
		));
	}

	/**
	 * Deletes a particular model.
	 * If deletion is successful, the browser will be redirected to the 'index' page.
	 * @param integer $id the ID of the model to be deleted
	 */
	public function actionDelete($id)
	{
		if(Yii::app()->request->isPostRequest)
		{
			// we only allow deletion via POST request
			$exist = PointsStatus::model()->exists('orderid=:orderid',array(':orderid'=>$id));
			if ($exist)
			{
				throw new CHttpException(400,Yii::t('points', '订单状态已存在，不能删除！'));
			}
			else
			{
				$this->loadModel($id)->delete();
				// if AJAX request (triggered by deletion via admin grid view), we should not redirect the browser
				if(!isset($_GET['ajax']))
				$this->redirect(isset($_POST['returnUrl']) ? $_POST['returnUrl'] : array('admin'));
			}
			
		}
		else
			throw new CHttpException(400,'Invalid request. Please do not repeat this request again.');
	}


	/**
	 * Manages all models.
	 */
	public function actionAdmin($branchId='', $type='opgcount')
	{
		$branchId = $this->branchId;
        $this->mainMenu = array(
			array(
				'label' => '订单管理',
				'url' => array('/operations/order/admin')
			),
			array(
				'label' => '商品管理',
				'url' => array('/operations/product/admin')
			),
		);
//        $this->branchSelectParams["showList"] = true;
        $this->branchSelectParams["urlArray"] = array("//operations/order/admin", 'type'=>$type);
        
        $criteria=new CDbCriteria;
        $criteria->compare('status', array(PointsStatus::STATS_CREATED, PointsStatus::STATS_CONFIRMED, PointsStatus::STATS_READYTOSHIP, PointsStatus::STATS_SHIPPING, PointsStatus::STATS_RECEIVED));
        if ($branchId){
            $criteria->compare('schoolid', $branchId);
        }
        $allOrders = PointsOrder::model()->findAll($criteria);
        
        $sData10 = array();
        $sData20 = array();
        $sData110 = array();
        $sData120 = array();
        $sData130 = array();
        $sData210 = array();
        
        foreach ($allOrders as $order){
            if ($order->status == PointsStatus::STATS_CREATED && $order->category == 'order'){
                $sData10[$order->id] = $order;
            }
            elseif ($order->status == PointsStatus::STATS_CONFIRMED && $order->category == 'order'){
                $sData20[$order->id] = $order;
            }
            elseif ($order->status == PointsStatus::STATS_READYTOSHIP && $order->category == 'pack'){
                $sData110[$order->id] = $order;
            }
            elseif ($order->status == PointsStatus::STATS_SHIPPING && $order->category == 'pack'){
                $sData120[$order->id] = $order;
            }
            elseif ($order->status == PointsStatus::STATS_RECEIVED && $order->category == 'order'){
                $sData130[$order->id] = $order;
            }
        }
        
        $criteria = new CDbCriteria;
        $criteria->compare('status', PointsStatus::STATS_COMPLETED);
        $criteria->compare('schoolid', $branchId);
        $criteria->order='update_timestamp desc';
        $sData210 = new CActiveDataProvider('PointsOrder', array(
            'criteria'=>$criteria,
        ));

        $schoolsCount = array();
        if($type == 'opgcount')
            $sql = 'select schoolid, count(*) as c from '.PointsOrder::model()->tableName().' where status in ('.PointsStatus::STATS_CREATED.','.PointsStatus::STATS_CONFIRMED.','.PointsStatus::STATS_READYTOSHIP.') and category="order" group by schoolid';
        else
            $sql = 'select schoolid, count(*) as c from '.PointsOrder::model()->tableName().' where status in ('.PointsStatus::STATS_SHIPPING.','.PointsStatus::STATS_RECEIVED.') and category="order" group by schoolid';
        $sc=Yii::app()->db->createCommand($sql)->queryAll();
        foreach ($sc as $_sc){
            $schoolsCount[$_sc['schoolid']]=$_sc['c'];
        }

		$this->render('admin',array(
            'branchId'=>$branchId,
            'sData10'=>$sData10,
            'sData20'=>$sData20,
            'sData110'=>$sData110,
            'sData120'=>$sData120,
            'sData130'=>$sData130,
            'sData210'=>$sData210,
            'schoolsCount'=>CJSON::encode($schoolsCount),
		));
	}

	/**
	 * Returns the data model based on the primary key given in the GET variable.
	 * If the data model is not found, an HTTP exception will be raised.
	 * @param integer the ID of the model to be loaded
	 */
	public function loadModel($id)
	{
		$model=PointsOrder::model()->findByPk((int)$id);
		if($model===null)
			throw new CHttpException(404,'The requested page does not exist.');
		return $model;
	}

	/**
	 * Performs the AJAX validation.
	 * @param CModel the model to be validated
	 */
	protected function performAjaxValidation($model)
	{
		if(isset($_POST['ajax']) && $_POST['ajax']==='points-order-form')
		{
			echo CActiveForm::validate($model);
			Yii::app()->end();
		}
	}
    
    public function actionConfirm($branchId='')
    {
        $oIds = Yii::app()->request->getPost('orderid', array());
        
        $this->addMessage('state', 'fail');
        $this->addMessage('message', Yii::t('message','Failed!'));
        if ($this->oAdmin && $oIds){
            
            foreach ($oIds as $id){
                $model = PointsOrder::model()->findByPk($id);
                if ($model->status == PointsStatus::STATS_CREATED){
                    $model->status = PointsStatus::STATS_CONFIRMED;
                    $model->update_timestamp = time();
                    $model->update_userid = Yii::app()->user->id;
                    $model->save();
                    
                    $status = new PointsStatus;
                    $status->itemid = $id;
                    $status->status = PointsStatus::STATS_CONFIRMED;
                    $status->update_timestamp = time();
                    $status->update_userid = Yii::app()->user->id;
                    $status->save();
                }
            }
            $this->addMessage('state', 'success');
            $this->addMessage('message', Yii::t('message','success'));
            $this->addMessage('refresh', true);
        }
        $this->showMessage();
    }
    
    public function actionPack($branchId='')
    {
        $this->addMessage('state', 'fail');
        $this->addMessage('message', Yii::t('message','Failed!'));
        $oIds = Yii::app()->request->getPost('orderid', array());
        
        if ($this->oAdmin && $oIds){
            $model = new PointsOrder;
            $model->category = 'pack';
            $model->schoolid = $branchId;
            $model->created_timestamp = time();
            $model->created_userid = Yii::app()->user->id;
            $model->status = PointsStatus::STATS_READYTOSHIP;
            $model->quantity = count($oIds);
            $model->update_timestamp = time();
            $model->update_userid = Yii::app()->user->id;
            $model->save();

            $status = new PointsStatus;
            $status->itemid = $model->id;
            $status->status = PointsStatus::STATS_READYTOSHIP;
            $status->update_timestamp = time();
            $status->update_userid = Yii::app()->user->id;
            $status->save();

            $criteria = new CDbCriteria;
            $criteria->compare('id', $oIds);
            PointsOrder::model()->updateAll(array( 'status'=>PointsStatus::STATS_READYTOSHIP, 'pack_id'=>$model->id), $criteria);

            $this->addMessage('state', 'success');
            $this->addMessage('message', Yii::t('message','success'));
            $this->addMessage('refresh', true);
        }
        $this->showMessage();
        
    }
    
    public function actionOrderlist($branchId='', $id=0)
    {
        if ($id){
            $this->dialogWidth = 800;
            $this->layout='//layouts/dialog';
            $model = PointsOrder::model()->findByPk($id);
            
            $orders = PointsOrder::model()->findAllByAttributes(array('pack_id'=>$id));
            
            Yii::app()->clientScript->registerScriptFile( Yii::app()->request->baseUrl.'/themes/base/js/jquery.jPrintArea.js' );
            
            $this->render('orderlist', array( 'model'=>$model, 'orders'=>$orders ));
        }
    }
    
    public function actionSend($branchId='', $id=0)
    {
        if ($this->oAdmin && $id){
            $op = Yii::app()->request->getPost('op', null);
            if ($op == 'send'){
                $this->addMessage('state', 'fail');
                $criteria = new CDbCriteria;
                $criteria->condition = "(id = :id or pack_id = :id) and status=:s";
                $criteria->params = array(':id'=>$id, ':s'=>PointsStatus::STATS_READYTOSHIP);

                $items = PointsOrder::model()->findAll($criteria);
                foreach ($items as $item){
                    $item->status = PointsStatus::STATS_SHIPPING;
                    if ($item->category == 'pack'){
                        $model->shipinfo = isset($_POST['PointsOrder']['shipinfo']) ? $_POST['PointsOrder']['shipinfo'] : '';
                    }
                    $item->update_timestamp = time();
                    $item->update_userid = Yii::app()->user->id;
                    $item->save();
                }
                $mpoint = new PointsStatus;
                $mpoint->itemid = $item->id;
                $mpoint->status = PointsStatus::STATS_SHIPPING;
                $status->memo = isset($_POST['PointsStatus']['memo']) ? $_POST['PointsStatus']['memo'] : '';
                $mpoint->update_timestamp = time();
                $mpoint->update_userid = Yii::app()->user->id;
                $mpoint->save();

                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message','success'));
                $this->addMessage('refresh', true);
                $this->showMessage();
            }
            
            $this->layout='//layouts/dialog';
            $criteria = new CDbCriteria;
            $criteria->compare('pack_id', $id);
            $models = PointsOrder::model()->findAll($criteria);
            
            $omodel = new PointsOrder;
            $smodel = new PointsStatus;
            
            $this->render('pack', array( 'models'=>$models, 'omodel'=>$omodel, 'smodel'=>$smodel ));
        }
    }
    
    public function actionSplit($branchId='', $id=0)
    {
        $ret = array('status'=>'fail');
        if ($this->oAdmin && $id){
            
            PointsOrder::model()->deleteByPk($id);
            
            PointsStatus::model()->deleteAllByAttributes(array('itemid'=>$id), 'status=:s', array(':s'=>PointsStatus::STATS_READYTOSHIP));
            
            PointsOrder::model()->updateAll(
                    array(
                        'status'=>PointsStatus::STATS_CONFIRMED,
                        'pack_id'=>'',
                        'update_timestamp'=>time(),
                        'update_userid'=>Yii::app()->user->id,
                    ),
                    'pack_id=:id and status=:s',
                    array(':id'=>$id, ':s'=>PointsStatus::STATS_READYTOSHIP)
            );
            
            $ret['status'] = 'success';
        }
        echo CJSON::encode($ret);
    }
    
    public function actionReceive($branchId='', $id=0)
    {
        $ret = array('status'=>'fail');
        if ($this->cAdmin && $id){
            $criteria = new CDbCriteria;
            $criteria->condition = "(id = :id or pack_id = :id) and status=:s";
            $criteria->params = array(':id'=>$id, ':s'=>PointsStatus::STATS_SHIPPING);
            
            $items = PointsOrder::model()->findAll($criteria);
            foreach ($items as $item){
                $item->status = PointsStatus::STATS_RECEIVED;
                $item->update_timestamp = time();
                $item->update_userid = Yii::app()->user->id;
                $item->save();
            }
            $mpoint = new PointsStatus;
            $mpoint->itemid = $item->id;
            $mpoint->status = PointsStatus::STATS_RECEIVED;
            $mpoint->update_timestamp = time();
            $mpoint->update_userid = Yii::app()->user->id;
            $mpoint->save();
            
            $ret['status'] = 'success';
        }
        echo CJSON::encode($ret);
    }
    
    public function actionDone($branchId='')
    {
        $oIds = Yii::app()->request->getPost('orderid', array());
        
        $this->addMessage('state', 'fail');
        $this->addMessage('message', Yii::t('message','Failed!'));
        if ($this->cAdmin && $oIds){
            
            foreach ($oIds as $id){
                $model = PointsOrder::model()->findByPk($id);
                if ($model->status == PointsStatus::STATS_RECEIVED){
                    $model->status = PointsStatus::STATS_COMPLETED;
                    $model->update_timestamp = time();
                    $model->update_userid = Yii::app()->user->id;
                    $model->save();
                    
                    $status = new PointsStatus;
                    $status->itemid = $id;
                    $status->status = PointsStatus::STATS_COMPLETED;
                    $status->update_timestamp = time();
                    $status->update_userid = Yii::app()->user->id;
                    $status->save();
                }
            }
            $this->addMessage('state', 'success');
            $this->addMessage('message', Yii::t('message','success'));
            $this->addMessage('refresh', true);
        }
        $this->showMessage();
    }
    
    public function actionGodone($branchId='', $id=0)
    {
        $this->addMessage('state', 'fail');
        $this->addMessage('message', Yii::t('message','Failed!'));
        if ($this->oAdmin && $id){
            if ( PointsOrder::model()->updateByPk($id, array('status'=>210)) ) {
                $status = new PointsStatus;
                $status->itemid = $id;
                $status->status = PointsStatus::STATS_COMPLETED;
                $status->update_timestamp = time();
                $status->update_userid = Yii::app()->user->id;
                $status->save();
                $this->addMessage('state', 'success');
            }
            else {
                $this->addMessage('state', 'fail');
            }
        }
        $this->showMessage();
    }
}
