<?php
$this->breadcrumbs = array(
    Yii::t("navigations", "Profile") => array('/child/profile'),
    Yii::t("navigations", "Misc Information"),
);
?>
<h1><label><?php echo Yii::t("navigations", "Misc Information") ?></label></h1>
<p class="desc"><?php echo Yii::t("userinfo", "Please review the miscellenous information below and correct if necessary. Click Save when completed."); ?></p>

<?php
foreach (Yii::app()->user->getFlashes() as $key => $message) {
    echo '<div class="flash-' . $key . '">' . $message . "</div>\n";
}

$colorbox = $this->widget('application.extensions.colorbox.JColorBox');
$colorbox->addInstance('.colorbox', array('iframe' => false, 'width' => '750'));
?>

<div class="form misc_bg">
    <?php
    $form = $this->beginWidget('CActiveForm', array(
            //'id'=>'misc-form',
            ));
    ?>

    <?php echo CHtml::errorSummary($model); ?>


    <dl class="row">
        <dt><?php echo CHtml::activeLabelEx($model, 'ucontact') ?></dt>
        <dd>
            <p class="desc"><?php echo Yii::t("userinfo", 'Who should we contact for emergency situations?') ?></p>
            <?php echo CHtml::activeTextArea($model, 'ucontact', array("rows" => 4, "cols" => 60)); ?>
        </dd>
    </dl>


    <dl class="row">
        <dt><?php echo CHtml::activeLabelEx($model, 'upickup') ?></dt>
        <dd>
            <p class="desc"><?php echo Yii::t("userinfo", 'Who do you authorize to pick up your child?') ?></p>
            <?php echo CHtml::activeTextArea($model, 'upickup', array("rows" => 4, "cols" => 60)); ?>
        </dd>
    </dl>


    <dl class="row">
        <dt><?php echo CHtml::activeLabelEx($model, 'allergy') ?></dt>
        <dd>
            <p class="desc"><?php echo Yii::t("userinfo", 'Please edit the following information correctly. An automatic email will be sent to Campus Support Team if you make changes to “Special Needs”, so they will be well aware of this and inform teachers as well.') ?></p>
            <?php echo CHtml::activeTextArea($model, 'allergy', array("rows" => 4, "cols" => 60)); ?>
        </dd>
    </dl>


    <dl class="row">
        <dt><?php echo CHtml::activeLabelEx($model, 'hospital') ?></dt>
        <dd>
            <p class="desc"><?php echo Yii::t("userinfo", 'Should a situation arise when emergency medical attention is required and we are unable to contact any of the above, please indicate,on the space provided below, the hospital we will take your child to. If not filled in, the school will select a hospital on its own. The school will not be responsible for any costs or fees incurred.') ?></p>
            <?php echo CHtml::activeTextArea($model, 'hospital', array("rows" => 1, "cols" => 60)); ?>
        </dd>
    </dl>

    <dl class="row">
        <dt><?php echo CHtml::activeLabelEx($model, 'agree_photo_open') ?></dt>
        <dd>
            <p class="desc"><?php echo Yii::t("userinfo", 'I give permission to the school, to use pictures taken of my child in school, for marketing purposes.') ?></p>
            <?php echo CHtml::activeCheckBox($model, 'agree_photo_open', array('disabled' => 'disabled')); ?>
			<?php echo CHtml::activeLabelEx($model, 'agree_photo_open') ?>
			<span><?php
            echo addBrackets(Yii::t("userinfo", 'Please :contact office staff to change.', array(
                ':contact' => CHtml::link(Yii::t("userinfo", 'contact'), array("//child/support/email"), array("target" => "_blank"))
                    )
            ));
            ?></span>
        </dd>
    </dl>

    <dl class="row submit">
        <dt></dt>
        <dd>
            <?php
            if (Yii::app()->user->checkAccess('editProfile', array("childid" => $this->getChildId()))){
				if ($child->status >= ChildProfileBasic::STATS_GRADUATED) {
					echo CHtml::submitButton(Yii::t("global", "Save"), array("class" => "w100 bigger","disabled" => "disabled"));
				}else{
					echo CHtml::submitButton(Yii::t("global", "Save"), array("class" => "w100 bigger"));
				}
			}else{
                echo CHtml::submitButton(Yii::t("global", "Save"), array("class" => "w100 bigger", "disabled" => "disabled"));
			}
            ?></dd>
    </dl>

    <?php $this->endWidget(); ?>
</div><!-- form -->