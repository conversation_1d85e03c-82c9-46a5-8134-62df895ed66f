<div class="form">
<?php
	foreach (Yii::app()->user->getFlashes() as $key => $message) {
		echo '<div class="flash-' . $key . '">' . $message . "</div>\n";
	}
?>
<?php $form=$this->beginWidget('CActiveForm', array(
	'id'=>'ivy-pregion-form',
	'enableAjaxValidation'=>false,
)); ?>

	<p class="note">Fields with <span class="required">*</span> are required.</p>

	<div class="row">
		<?php echo $form->labelEx($model,'en_title'); ?>
		<?php echo $form->textField($model,'en_title',array('size'=>60,'maxlength'=>255)); ?>
		<?php echo $form->error($model,'en_title'); ?>
	</div>

	<div class="row">
		<?php echo $form->labelEx($model,'cn_title'); ?>
		<?php echo $form->textField($model,'cn_title',array('size'=>60,'maxlength'=>255)); ?>
		<?php echo $form->error($model,'cn_title'); ?>
	</div>

	<div class="row">
		<?php echo $form->labelEx($model,'en_memo'); ?>
		<?php echo $form->textArea($model,'en_memo',array('rows'=>6, 'cols'=>50)); ?>
		<?php echo $form->error($model,'en_memo'); ?>
	</div>

	<div class="row">
		<?php echo $form->labelEx($model,'cn_memo'); ?>
		<?php echo $form->textArea($model,'cn_memo',array('rows'=>6, 'cols'=>50)); ?>
		<?php echo $form->error($model,'cn_memo'); ?>
	</div>

	<div class="row">
		<?php echo $form->labelEx($model,'status'); ?>
		<?php echo $form->dropDownList($model, 'status', array('' => Yii::t('operations', Yii::t('global','Please Select'))) + array(Yii::t('operations', '显示'),Yii::t('operations', '隐藏'))); ?>
		<?php echo $form->error($model,'status'); ?>
	</div>

	<div class="row buttons">
		<?php echo CHtml::submitButton($model->isNewRecord ? 'Create' : 'Save'); ?>
	</div>

<?php $this->endWidget(); ?>

</div><!-- form -->