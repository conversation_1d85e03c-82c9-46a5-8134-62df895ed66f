<style>
    .weui-loadmore_line {
        border-top: 1px solid #E5E5E5;
        margin-top: 2.4em;
    }
    .weui-loadmore {
        width: 65%;
        margin: 1.5em auto;
        line-height: 1.6em;
        font-size: 14px;
        text-align: center;
    }
    .weui-loadmore_line .weui-loadmore__tips {
        position: relative;
        top: -0.9em;
        padding: 0 .55em;
        color: #999999;
    }
    .weui-loadmore__tips {
        display: inline-block;
        vertical-align: middle;
        background: #fbf9fe;
    }
</style>
<div class="weui_cells_title"><?php echo Yii::t("wechat", "My orders"); ?></div>
<div class="weui_panel weui_panel_access" style="margin-top: 0">
    <div class="weui_panel_bd" id="products">
    </div>
</div>
<script>
    var products = <?php echo json_encode($products); ?>;

    var productsContent = '';
    if(products.length != 0){

        $.each(products, function (i, p) {
            var attrArr = '';
            $.each(p.attr, function (i, attr) {
                if(i < p.attr.length - 1 ){
                    attrArr += attr + ', ';
                }else {
                    attrArr += attr;
                }
            });
            if (typeof p.imgthumb[p.attr[0]] == "undefined" && typeof p.imgthumb[p.attr[1]] == "undefined") {
                if(typeof p.imgthumb.all != 'undefined'){
                    productsContent += '<a href="javascript:void(0);" class="weui_media_box weui_media_appmsg"><div class="weui_media_hd"><img class="weui_media_appmsg_thumb" src="' + p.imgthumb.all[0] + '" alt="image"></div><div class="weui_media_bd"><h4 class="weui_media_title">' + p.status + '</h4><p class="weui_media_desc">' + p.title + '<br>' + attrArr + '</p><div class="weui_cell_ft"></div></div></a>'
                }
            } else if(typeof p.imgthumb[p.attr[0]] == "undefined") {
                if(typeof p.imgthumb[p.attr[1]] != "undefined"){
                    productsContent += '<a href="javascript:void(0);" class="weui_media_box weui_media_appmsg"><div class="weui_media_hd"><img class="weui_media_appmsg_thumb" src="' + p.imgthumb[p.attr[1]][0] + '" alt="image"></div><div class="weui_media_bd"><h4 class="weui_media_title">' + p.status + '</h4><p class="weui_media_desc">' + p.title + '<br>' +attrArr + '</p><div class="weui_cell_ft"></div></div></a>'
                }
            }else if( typeof p.imgthumb[p.attr[1]] == "undefined") {
                if(typeof p.imgthumb[p.attr[0]] != "undefined"){
                    productsContent += '<a href="javascript:void(0);" class="weui_media_box weui_media_appmsg"><div class="weui_media_hd"><img class="weui_media_appmsg_thumb" src="' +  p.imgthumb[p.attr[0]][0] + '" alt="image"></div><div class="weui_media_bd"><h4 class="weui_media_title">' + p.status + '</h4><p class="weui_media_desc">' + p.title + '<br>' +attrArr + '</p><div class="weui_cell_ft"></div></div></a>'
                }
            }else if(typeof arrayIntersection(p.imgthumb[p.attr[0]], p.imgthumb[p.attr[1]]) == false) {
                productsContent += '<a href="javascript:void(0);" class="weui_media_box weui_media_appmsg"><div class="weui_media_hd"><img class="weui_media_appmsg_thumb" src="' + p.imgthumb['all'][0]+'" alt="image"></div><div class="weui_media_bd"><h4 class="weui_media_title">' + p.status + '</h4><p class="weui_media_desc">' + p.title + '<br>' +attrArr + '</p><div class="weui_cell_ft"></div></div></a>'
            }else {
                if (arrayIntersection(p.imgthumb[p.attr[0]], p.imgthumb[p.attr[1]])[0] == undefined) {
                    productsContent += '<a href="javascript:void(0);" class="weui_media_box weui_media_appmsg"><div class="weui_media_hd"><img class="weui_media_appmsg_thumb" src="' + p.imgthumb[p.attr[0]][0] + '" alt="image"></div><div class="weui_media_bd"><h4 class="weui_media_title">' + p.status + '</h4><p class="weui_media_desc">' + p.title + '<br>' +attrArr + '</p><div class="weui_cell_ft"></div></div></a>'
                } else {
                    productsContent += '<a href="javascript:void(0);" class="weui_media_box weui_media_appmsg"><div class="weui_media_hd"><img class="weui_media_appmsg_thumb" src="' + arrayIntersection(p.imgthumb[p.attr[0]], p.imgthumb[p.attr[1]])[0] + '" alt="image"></div><div class="weui_media_bd"><h4 class="weui_media_title">' + p.status + '</h4><p class="weui_media_desc">' + p.title + '<br>' +attrArr + '</p><div class="weui_cell_ft"></div></div></a>'
                }
            }

        });
        $('#products').html(productsContent);
    }else {
        $('.container').append('<div class="weui-loadmore weui-loadmore_line"><span class="weui-loadmore__tips"><?php echo Yii::t("wechat", "No data found!"); ?></span></div>');
    }

    function arrayIntersection(a, b) {  //两个数组取交集
        var ai = 0, bi = 0;
        var result = new Array();
        while (ai < a.length && bi < b.length) {
            if (a[ai] < b[bi]) {
                ai++;
            }
            else if (a[ai] > b[bi]) {
                bi++;
            }
            else /* they're equal */
            {
                result.push(a[ai]);
                ai++;
                bi++;
            }
        }
        return result;
    }
    var thumbnailUrl = function () {

    }
</script>