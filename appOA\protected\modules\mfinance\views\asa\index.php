<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo Yii::t('site','Finance Management');?></li>
        <li class="active">课后课财务</li>
    </ol>

    <div class="row">
        <!-- 左侧菜单 -->
        <div class="col-md-2 col-sm-2 mb10">
            <?php
            $this->widget('zii.widgets.CMenu', array(
                'items' => $this->getMenu(),
                'id' => 'pageCategory',
                'htmlOptions' => array('class' => 'nav nav-pills nav-stacked background-gray'),
                'activeCssClass' => 'active',
                'itemCssClass' => ''
            ));
            ?>
        </div>

        <div class="col-md-10 col-sm-12">
            <!--分类菜单-->
            <?php
            $this->widget('zii.widgets.CMenu', array(
                'items' => $this->AttendMenu(),
                'id' => 'AttendMenu',
                'htmlOptions' => array('class' => 'nav nav-tabs'),
                'activeCssClass' => 'active',
                'itemCssClass' => ''
            ));
            ?>

            <?php

            $this->widget('ext.ivyCGridView.BsCGridView', array(
                'id' => 'cashHandober',
                'afterAjaxUpdate' => 'js:head.Util.modal',
                'dataProvider' => $cashHandoverl,
                'template' => "{items}{pager}",
                //状态为无效时标红
                //'rowCssClassExpression' => '( $data->status == 0 ? "active" : "" )',
                'colgroups' => array(
                    array(
                        "colwidth" => array(100, 100, 100, 100, 100, 200, 100),
                    )
                ),
                'columns' => array(
                    array(
                        'name' => 'site_id',
                        'value'=>array($this, "getSchoolName"),
                    ),
                    array(
                        'name' => 'amount',
                        'value' => '$data->amount',
                    ),
                    array(
                        'name' => 'bank_ref',
                        'value' => '$data->bank_ref',
                    ),
                    array(
                        'name' => 'creater',
                        'value'=>array($this, "getCreate"),
                    ),
                    array(
                        'name' => 'created',
                        'value' => 'date("Y-m-d",$data->created)',
                    ),
                    array(
                        'name' => 'creater_memo',
                        'value' => '$data->creater_memo',
                    ),
                   /* array(
                        'name' => 'status',
                        'value' => '($data->status == 2) ? "已提交" : "已确定"',
                    ),*/
                    array(
                        'name'=> Yii::t('asa','action'),
                        'value'=>array($this, "getButton"),
                    ),
                ),
            ));
            ?>
        </div>
    </div>
</div>
<script>
    var modal = '<div class="modal fade" id="modal" class="1123" tabindex="-1" role="dialog" aria-labelledby="modal"><div class="modal-dialog modal-lg" role="document"><div class="modal-content"></div></div></div>';
    $('body').append(modal);

    function cbUpdateHandover(){
        $('#modal').modal('hide');
        $.fn.yiiGridView.update('cashHandober');
    }
</script>