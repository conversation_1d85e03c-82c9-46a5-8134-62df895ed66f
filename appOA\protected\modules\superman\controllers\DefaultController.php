<?php

class DefaultController extends Controller
{
    public function init()
    {
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";
    }

	public function actionIndex()
	{
		$this->render('index');
	}

    public function actionClass()
    {
        $classShow = Yii::app()->subdb->createCommand()
            ->select('schoolid, status')
            ->from('ivy_school_class_show')
            ->queryAll();

        $this->render('class', array('models' => $classShow));
    }

    public function actionChangeClassShow()
    {
        $schoolid = Yii::app()->request->getParam('schoolid');
        $status = Yii::app()->request->getParam('status');
        if (!$schoolid || !in_array($status, array(0, 1))) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '参数错误');
            $this->showMessage();
        }
        $update = Yii::app()->subdb->createCommand()->update(
            'ivy_school_class_show', 
            array('status' => $status),
            'schoolid=:schoolid',
            array(':schoolid' => $schoolid)
        );
        if ($update) {
            $this->addMessage('state', 'success');
            $this->addMessage('message', '保存成功');
            $this->showMessage();
        }
        $this->addMessage('state', 'fail');
        $this->addMessage('message', '保存失败');
        $this->showMessage();
    }

    public function actionWechatToken()
    {
        Yii::import('common.models.wechat.WechatToken');
        $critr = new CDbCriteria;
		$dataProvider = new CActiveDataProvider('WechatToken', array(
			'criteria' => $critr,
			'sort' => array(
				'defaultOrder'=>array(
					'updated_at'=>CSort::SORT_DESC,
				)
			),
			'pagination'=>array(
				'pageSize'=>20,
			),
		));

		$this->render('wechatToken', array(
			'dataProvider' => $dataProvider,
		));
    }

    public function actionDelToken()
    {
        $key = Yii::app()->request->getParam('key');
        $this->addMessage('state', 'fail');
        if (!$key) {
            $this->addMessage('message', '参数错误');
            $this->showMessage();
        }
        if (!CommonUtils::delAccessToken($key)) {
            $this->addMessage('message', '删除失败');
            $this->showMessage();
        }
        $this->addMessage('state', 'success');
        $this->addMessage('message', '删除成功');
        $this->showMessage();
    }
}