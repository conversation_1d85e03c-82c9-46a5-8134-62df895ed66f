<?php

/**
 * This is the model class for table "ivy_timetable_student_number_cache".
 *
 * The followings are the available columns in table 'ivy_timetable_student_number_cache':
 * @property integer $id
 * @property integer $tid
 * @property integer $course_id
 * @property integer $target_date
 * @property integer $weekday
 * @property integer $period
 * @property integer $class_room
 * @property string $data
 * @property integer $updated_at
 */
class TimetableStudentNumberCache extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_timetable_student_number_cache';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('tid, course_id, target_date, weekday, period, class_room, data, updated_at', 'required'),
			array('tid, course_id, target_date, weekday, period, class_room, updated_at', 'numerical', 'integerOnly'=>true),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, tid, course_id, target_date, weekday, period, class_room, data, updated_at', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'courses' => array(self::HAS_ONE, 'TimetableCourses', array('id'=>'course_id')),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'tid' => 'Tid',
			'course_id' => 'Course',
			'target_date' => 'Target Date',
			'weekday' => 'Weekday',
			'period' => 'Period',
			'class_room' => 'Class Room',
			'data' => 'Data',
			'updated_at' => 'Updated At',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('tid',$this->tid);
		$criteria->compare('course_id',$this->course_id);
		$criteria->compare('target_date',$this->target_date);
		$criteria->compare('weekday',$this->weekday);
		$criteria->compare('period',$this->period);
		$criteria->compare('class_room',$this->class_room);
		$criteria->compare('data',$this->data,true);
		$criteria->compare('updated_at',$this->updated_at);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return TimetableStudentNumberCache the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}
