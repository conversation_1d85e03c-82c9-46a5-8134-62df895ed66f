<?php
$form = $this->beginWidget('CActiveForm', array(
    'id' => 'child_interivew',
    'enableAjaxValidation' => false,
    'action' => $this->createUrl('interviewResults', array('childid' => $child->id)),
    'htmlOptions' => array('class' => 'J_ajaxForm', 'role' => 'form'),
));
Yii::import('common.models.visit.*');
$gender = array(1=>'Boy', 2=> 'Girl');
$grade = AdmissionsDs::getConfig();

$countries = Country::model()->getCountryList('','','en');
$Diglossia = Diglossia::model()->getLanguage('true');

$data = array();
if($childinterview->discipline_stat){
    $data = json_decode($childinterview->discipline_stat,true);
    $child->interviewDate = date("Y-m-d", $data['interviewDate']);
    $child->interviewDate_t = date("Y-m-d", $data['interviewDate_t']);
}

$schoolHistoryArr = array();
if($child->school_history){
    $school_history = json_decode($child->school_history,true);
    foreach ($school_history as $val){
        $schoolHistoryArr[] = $val['school'];
    }
    $schoolHistoryArr = implode(',',$schoolHistoryArr);
}

$array = array(1=>'A',2=>'B',3=>'C',4=>'D');
?>
<div>
   
        <h3 class="text-center"><?php echo (isset($calender) && isset($calender[$child->start_year])) ? $calender[$child->start_year] : '' ; ?> Admissions Assessment Check List (Grade 2+)</h3>
    <div class="table-responsive">
        <h4>Student Information</h4>
        <table class="table table-bordered">
        <thead>
             <colgroup>
              <col style="width:50%">
              <col style="width:50%">
           </colgroup>
        </thead>
        <tbody>
        
            <tr>
                <td>English Name: <?php echo $child->en_name_last . ' ' .$child->en_name_middle . ' ' .$child->en_name?></td>
                <td>Chinese Name: <?php echo $child->cn_name . ' ' .$child->cn_name_last ?></td>
            </tr>
            <tr>
                <td>Nationality: <?php echo $countries[$child->nationality] ?></td>
                <td>Mother Language: <?php echo $Diglossia[$child->native_lang] ?></td>
            </tr>
            <tr>
                <td>Gender: <?php echo $gender[$child->gender] ?></td>
                <td>DOB: <?php echo date("Y-m-d", $child->birthday) ?></td>
            </tr>
            <tr>
                <td>Starting Year & Grade: <?php echo $grade['grade_en'][$child->start_grade] ?></td>
                <td>Previous School: <?php echo ($schoolHistoryArr) ? $schoolHistoryArr : '' ?></td>
            </tr>
            <tr>
                <td>Father:
                    <?php echo ($child->father_name) ? $child->father_name . '，' : '' ; ?>
                    <?php echo ($child->father_education) ? $grade['education'][$child->father_education]  . '，' : '' ; ?>
                    <?php echo ($child->father_position) ? $child->father_position  . '，' : '' ; ?>
                    <?php echo ($child->father_employer) ? $child->father_employer : '' ; ?>，
                </td>
                <td>
                    Mother:
                    <?php echo ($child->mother_name) ? $child->mother_name  . '，' : '' ?>
                    <?php echo ($child->mother_education) ? $grade['education'][$child->mother_education]  . '，' : '' ?>
                    <?php echo ($child->mother_position) ? $child->mother_position  . '，' : '' ?>
                    <?php echo ($child->mother_employer) ? $child->mother_employer : '' ?>
                </td>
            </tr>
            <tr>
                <td>
                    Siblings at Daystar: <?php
                    $siblingsDaystar = array( 1 => 'Yes', 2 => 'No' );
                    echo ($data && $data['siblingsDaystar']) ? $siblingsDaystar[$data['siblingsDaystar']] : '未选择' ;
                    ?>
                </td>
                <td>Staff Child: <?php echo ($child->is_staff == 1) ? "Yes" : "No" ?></td>
            </tr>

        </tbody>
        </table>
    </div>
    <div class="table-responsive">
        <h4><?php echo Yii::t('user','Other Materials') ?></h4>
        <table class="table table-bordered">
        <thead>
             <colgroup>
              <col style="width:24%">
              <col style="width:36%">
              <col style="width:40%">
           </colgroup>
        </thead>
        <tbody>
            <tr>
                <td width="280">学籍Xueji:</td>
                <td>
                    <?php echo ($data) ? $data['xueji'] : ""; ?>
                </td>
                <td width="200">
                    <?php echo ($data) ? $data['xueji2'] : ""; ?>
                </td>
            </tr>
            <tr>
                <td width="280">School Report:</td>
                <td>
                    <?php echo ($data) ? $data['schoolReport'] : ""; ?>
                </td>
                <td>
                    <?php echo ($data) ? $data['schoolReport2'] : ""; ?>
                </td>
            </tr>
            <tr>
                <td width="280">Recommendation Letter:</td>
                <td>
                    <?php echo ($data) ? $data['letter'] : ""; ?>
                </td>
                <td>
                    <?php echo ($data) ? $data['letter2'] : ""; ?>
                </td>
            </tr>
            </tbody>
        </table>
    </div>
    <div class="table-responsive">
        <div>
        <h4>
         <span>Written Test Information</span>
            <span class="pull-right" style="font-size:13px"><?php echo Yii::t('user','Date:') ?>
                <?php echo ($childinterview) ? date("Y-m-d", $childinterview->interview_time) : "未分配面试时间" ?>
            </span>
            </h4>
           
        </div>
        <table class="table table-bordered">
         <thead>
             <colgroup>
              <col style="width:20%">
              <col style="width:20%">
              <col style="width:20%">
              <col style="width:20%">
              <col style="width:20%">
           </colgroup>
        </thead>
        <tbody>
            <tr>
                <td><?php echo Yii::t('user', 'Subject & Score') ?>:</td>
                <td><?php echo Yii::t('user', 'Benchmark') ?>:</td>
                <td><?php echo Yii::t('user', 'Teacher') ?>:</td>
                <td><?php echo Yii::t('user', 'Subject & Score') ?>:</td>
                <td><?php echo Yii::t('user', 'Teacher') ?>:</td>
            </tr>
            <tr>
                <td>
                    <?php echo Yii::t('user', 'Map Reading') ?>:
                    <?php echo ($data) ? $data['mapReadingFraction'] : ""; ?>
                </td>
                <td>
                    <?php echo ($data) ? $data['mapReading'] : ""; ?>
                </td>
                <td>
                    <?php echo ($data) ? $data['mapReadingTeacher'] : ""; ?>
                </td>
                <td>
                    <?php echo Yii::t('user', 'English writing') ?>:
                    <?php echo ($data) ? $data['englishWritingFraction'] : ""; ?>
                </td>
                <td>
                    <?php echo ($data) ? $data['englishWriting'] : ""; ?>
                </td>
            </tr>
            <tr>
                <td>
                    <?php echo Yii::t('user', 'Map Language') ?>:
                    <?php echo ($data) ? $data['mapLanguageFraction'] : ""; ?>
                </td>
                <td>
                    <?php echo ($data) ? $data['mapLanguage'] : ""; ?>
                </td>
                <td>
                    <?php echo ($data) ? $data['mapLanguageTeacher'] : ""; ?>
                </td>
                <td>
                    <?php echo Yii::t('user', 'Chinese Literature') ?>:
                    <?php echo ($data) ? $data['chineseLiteratureFraction'] : ""; ?>
                </td>
                <td>
                    <?php echo ($data) ? $data['chineseLiterature'] : ""; ?>
                </td>
            </tr>
            <tr>
                <td>
                    <?php echo Yii::t('user', 'Map Math') ?>:
                    <?php echo ($data) ? $data['mapMathFraction'] : ""; ?>
                </td>
                <td>
                    <?php echo ($data) ? $data['mapMath'] : ""; ?>
                </td>
                <td>
                    <?php echo ($data) ? $data['mapMathTeacher'] : ""; ?>
                </td>
                <td>
                    <?php echo Yii::t('user', 'Chinese Math') ?>:
                    <?php echo ($data) ? $data['chineseMathFraction'] : ""; ?>
                </td>
                <td>
                    <?php echo ($data) ? $data['chineseMath'] : ""; ?>
                </td>
            </tr>
            <tr>
                <td colspan="5"><?php echo Yii::t('user', 'Learning Support') ?>:
                    <?php echo ($data) ? $data['recommendationLetter'] : ""; ?>
                </td>
            </tr>
            </tbody>
        </table>
    </div>

    <div class="table-responsive">
        <div>
            <h4><?php echo Yii::t('user','Interview Information') ?>
            <span class="pull-right" style="font-size:12px"><?php echo Yii::t('user','Date:') ?>
                <?php echo ($data && $data['interviewDate']) ? date("Y-m-d", $data['interviewDate']) : "未分配时间"?>
            </span>
            </h4>
        </div>
        <table class="table table-bordered">
            <tr>
                <td></td>
                <td><?php echo Yii::t('user', 'Student Interview') ?>:</td>
                <td><?php echo Yii::t('user', 'Parents Interview') ?>:</td>
            </tr>
            <tr>
                <td><?php echo Yii::t('user', 'Interviewer:') ?>:</td>
                <td>
                    <?php echo ($data) ? $data['interviewer1'] : ""; ?>
                </td>
                <td>
                    <?php echo ($data) ? $data['interviewer2'] : ""; ?>
                </td>
            </tr>
            <tr>
                <td><?php echo Yii::t('user', 'Interview Result') ?>:</td>
                <td>
                    <?php echo CHTml::radioButtonList('interview[studentInterview]', ($data) ? $data['studentInterview'] : '', array(1=>'A',2=>'B',3=>'C',4=>'D'), array('separator'=>'', 'template'=>'<div class="col-md-2 radio-inline">{input} {label}</div>')) ?>
                </td>
                <td>
                    <?php echo CHTml::radioButtonList('interview[parentsInterview]', ($data) ? $data['parentsInterview'] : '', array(1=>'A',2=>'B',3=>'C',4=>'D'), array('separator'=>'', 'template'=>'<div class="col-md-2 radio-inline">{input} {label}</div>')) ?>
                </td>
            </tr>
            <tr>
                <td colspan="3"><span class="pull-right">A: Outstanding   B: Above Average   C: Average   D: Below Average</span></td>
            </tr>
        </table>
    </div>

    <div class="table-responsive">
        <h4><?php echo Yii::t('user','Person in Charge') ?></h4>
        <table class="table table-bordered">
            <tr>
                <td width="280"><?php echo Yii::t('user', 'Department') ?>:</td>
                <td><?php echo Yii::t('user', 'Accept') ?>:</td>
                <td><?php echo Yii::t('user', 'Reject') ?>:</td>
                <td><?php echo Yii::t('user', 'Note') ?>:</td>
                <td><?php echo Yii::t('user', 'Signature &Date') ?>:</td>
            </tr>
            <tr>
                <td width="280"><?php echo Yii::t('user', 'Admissions Director') ?>:</td>
                <td>
                    <?php echo ($data) ? $data['admissionsDirector'] : ""; ?>
                </td>
                <td>
                    <?php echo ($data) ? $data['admissionsDirector2'] : ""; ?>
                </td>
                <td>
                    <?php echo ($data) ? $data['admissionsDirector3'] : ""; ?>
                </td>
                <td>
                    <?php echo ($data) ? $data['admissionsDirector4'] : ""; ?>
                </td>
            </tr>
            <tr>
                <td width="280"><?php echo Yii::t('user', 'Principal') ?>:</td>
                <td>
                    <?php echo ($data) ? $data['principal'] : ""; ?>
                </td>
                <td>
                    <?php echo ($data) ? $data['principal2'] : ""; ?>
                </td>
                <td>
                    <?php echo ($data) ? $data['principal3'] : ""; ?>
                </td>
                <td>
                    <?php echo ($data) ? $data['principal4'] : ""; ?>
                </td>
            </tr>
        </table>
    </div>
<?php $this->endWidget(); ?>
