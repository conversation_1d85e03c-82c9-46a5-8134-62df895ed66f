<?php
class PlanInvoiceCommand extends CConsoleCommand
{
    public $batchNum = 200;
    
	public function actionIndex()
	{
		echo $this->getHelp();
	}
	
	public function actionGenDepositBalance($branchId='', $toFinanceOnly=0){
		if($branchId ==''){
			echo '--branchId=XXX or all' . "\r\n";
			echo '--toFinanceOnly=1 or 0' . "\r\n";
			exit;
		}
        
		Yii::app()->language = 'zh_cn';
		
        Yii::import('common.models.calendar.Calendar');
		Yii::import('common.models.invoice.*');
        Yii::import('application.components.policy.*');
        $criteria = new CDbCriteria();
		if(strtolower($branchId)!='all'){
			$criteria->compare('t.branchid', $branchId);
		}
        $criteria->compare('t.status', 10);
        $criteria->compare('t.type', 20);
        $criteria->index = 'branchid';
		$branches = Branch::model()->with('info')->findAll($criteria);
        
        $criteira = new CDbCriteria();
        $criteira->compare('startyear', '<=2012');
        $items = Calendar::model()->findAll($criteira);
        foreach ($items as $item){
            $yids[$item->yid]=$item->startyear;
        }
        
        $ret = array();
        $criteira = new CDbCriteria();
        $criteira->compare('yid', array_keys($yids));
        $total = DepositHistory::model()->count($criteira);
        $cycle = ceil($total/$this->batchNum);
        for($i=0; $i<$cycle; $i++){
            $criteira->limit=$this->batchNum;
            $criteira->offset=$i*$this->batchNum;
            $items = DepositHistory::model()->findAll($criteira);
            foreach ($items as $item){
				$data = isset($ret[$item->childid][$yids[$item->yid]]) ? $ret[$item->childid][$yids[$item->yid]] : 0;
				if( $item->inout == 'in' )
					$ret[$item->childid][$yids[$item->yid]] = $data + $item->amount;
				else
					$ret[$item->childid][$yids[$item->yid]] = $data - $item->amount;
					
				if($ret[$item->childid][$yids[$item->yid]] == 0){
					unset($ret[$item->childid][$yids[$item->yid]]);
				}
            }
        }
		
		foreach(array_keys($ret) as $childid){
			if(count($ret[$childid]) == 0){
				unset($ret[$childid]);
			}
		}
		
        //print_r(count($ret));die;
        //print_r($ret);die;
		$criteria = new CDbCriteria;
		$criteria->compare('childid', array_keys($ret));
		$criteria->index = 'childid';
		$children = ChildProfileBasic::model()->findAll($criteria);
		
		foreach($ret as $_c => $_data){
			$result[$children[$_c]->schoolid][$_c] = $_data;
		}
				
		foreach($branches as $branchId=>$branch){
			if(isset($result[$branchId]) && count($result[$branchId])){
				echo $branchId . "\r\n";
                $mailer = Yii::createComponent('common.extensions.mailer.EMailer');
				
				if($toFinanceOnly){
					$mailer->AddAddress('<EMAIL>');
					$mailer->AddAddress('<EMAIL>');
					$mailer->AddAddress('<EMAIL>');
				}else{
					$mailer->AddAddress($branch->info->support_email);
					$mailer->AddReplyTo('<EMAIL>');
					//$mailer->AddCC('<EMAIL>');				
				}
				
				$isProduction = defined("IS_PRODUCTION")? IS_PRODUCTION : false;
				
                $mailer->Subject = sprintf('【 %s 艾毅在线往年预缴学费余额 - %s】%s' , $isProduction ? "正式":"测试", $branch->title, date('Y-m-d'));
				
				$mailP = ($isProduction) ? 'sendmail' : 'smtp';
                $mailer->iniMail($isProduction, $mailP); // 此行代码要放到AddAddress, AddCC方法下面
				
				$mailDesc = '<p>请查看往年剩余预缴学费的孩子清单。
				<br />如果需要转移到当年学年的，请自行操作，<span style="color:red">请先转移余额到今年再开具今年的学费账单，否则将无法在该账单中使用此部分余额。</span>';
				$mailDesc .= '<br />如果预缴学费余额无需退还，请联系财务部分进行处理。';
				$mailDesc .= '自动邮件，请勿回复，有问题请联系IT部门。</p>';
				
				
				//print_r(array_keys($children));
				
                $mailer->getCommandView('depositBalance', array(
					'mailDesc'=>$mailDesc,
					'result'=>$result[$branchId],
					'children'=>$children,
					'branch'=>$branch,
					), 'main');
                if (!$mailer->Send()) {
					print_r($mailer->getError());
				}
			}
		}
	}

	public function actionGenInvoice($branchId='', $targetDate='')
	{
        error_reporting(E_ERROR);
		if($branchId ==''){
			echo '--branchId=XXX' . "\r\n";
			exit;
		}
		Yii::import('common.models.invoice.*');
        Yii::import('application.components.policy.*');
        $criteria = new CDbCriteria();
		if(strtolower($branchId)!='all'){
			$criteria->compare('t.branchid', $branchId);
		}
        $criteria->compare('t.status', 10);
        $criteria->compare('t.type', 20);
        $criteria->index = 'branchid';
		$branches = Branch::model()->with('info')->findAll($criteria);
        if ($targetDate){
            $plantime = $targetDate;
        }
        else {
            $plantime = date('Ymd',time());
        }
		
		foreach($branches as $branchId=>$branch){
            $branchTitle = $branch->title;
			echo $branchId . "\r\n";
			
			$criteria = new CDbCriteria();
			$criteria->compare('plantime', '='.$plantime);
			$criteria->compare('plantime', '>0');
			$criteria->compare('t.schoolid', $branchId);
			$criteria->compare('child.schoolid', $branchId);
			$criteria->compare('child.status', array(ChildProfileBasic::STATS_ACTIVE_WAITING, ChildProfileBasic::STATS_ACTIVE));
			$criteria->compare('runtime', 0);
			$criteria->compare('invoiceid', 0);
			//$criteria->compare('amount', '>0');
			$criteria->compare('startdate', '>='.strtotime('2013/09/01'));
			$criteria->order = 't.classid ASC, t.childid ASC';
			$bySchoolTask = MonthlyFeeTask::model()->with(array('plan', 'child'))->findAll($criteria);
			
			$result = array();
			$childIds = array();
			if(count($bySchoolTask)){
				foreach($bySchoolTask as $task){
					echo $task->id.'.';
					
					$resultModel = null;
					
					if($task->amount > 0){
						$policyKey = $branchId.$task->plan->startyear;
						if(!isset($policy[$policyKey])){
							$policy[$policyKey] = new IvyPolicy('pay', $task->plan->startyear, $branchId);
						}
						global $paymentMappings;
						$dbFeeType = $paymentMappings['dbFeeType'];
						
						$status = true;
						$retult = false;
						
						if($task->payment_type == $dbFeeType[FEETYPE_TUITION]){
							$status = $policy[$policyKey]->checkTuitionCreation($task->childid);
						}
						
						$message = RC::isErrCode($status) ? RC::getTip($status) : '';
						if ($status === true){
							$invoiceModel = $policy[$policyKey]->genPlanModelSetting($task);
							$dbFeeType = array_flip($dbFeeType);
							
							$error = array();
							
							if(!$invoiceModel->exist['list']){
								$resultModel = $policy[$policyKey]->saveInvoice($invoiceModel,$dbFeeType[$task->payment_type]);
								if ($resultModel){
									$retult = true;
								}
								else {
									$error = $resultModel->getErrors();
								}
	
								if ($error && !$message)
									$message = $error[0];
								$task->invoiceid = $task->invoiceid != 0 ? $task->invoiceid.','.$resultModel->invoice_id : $resultModel->invoice_id;
								$task->runtime = $plantime;
								$task->save();
							}
							else {
								$message = '本区间已存在账单';
							}
						}
					}else{
						$retult = false;
						$message = "账单计划金额不能为0";
					}
					
					$result[$task->classid][$task->childid][] = array(
						'payment_type'=>$task->payment_type,
						'result'=>$retult,
						'finalAmount' => !empty($resultModel) ? $resultModel->amount : 0,
						'amount' => $task->amount,
						'message'=> $message,
						'title'=>$task->title,
					);
					
					$childIds[$task->childid] = $task->childid;
				}
                
                if(!is_dir(Yii::getPathOfAlias('xoopsVarPath').'/monthly_task'))
                    mkdir(Yii::getPathOfAlias('xoopsVarPath').'/monthly_task', 0777);
                if(!is_dir(Yii::getPathOfAlias('xoopsVarPath').'/monthly_task/'.$plantime))
                    mkdir(Yii::getPathOfAlias('xoopsVarPath').'/monthly_task/'.$plantime, 0777);
                file_put_contents(Yii::getPathOfAlias('xoopsVarPath').'/monthly_task/'.$plantime.'/'.date('YmdHis').$branchId.'_json.log', CJSON::encode($result));
				
//				$crit = new CDbCriteria;
//				$crit->compare('classid', array_keys($result));
//				$crit->index = 'classid';
//				$classInfo = IvyClass::model()->findAll($crit);
                
                $classes = Yii::app()->db->createCommand()
                        ->from(IvyClass::model()->tableName())
//                        ->where('classid in (:classid)', array(':classid'=>implode(',', array_keys($result))))
                        ->where(array('in','classid',array_keys($result)))
                        ->queryAll();
                foreach ($classes as $_cla){
                    $classInfo[$_cla['classid']]=$_cla['title'];
                }
				
//				$crit = new CDbCriteria;
//				$crit->compare('childid', array_keys($childIds));
//				$crit->index = 'childid';
//				$children = ChildProfileBasic::model()->findAll($crit);
                
                $childs = Yii::app()->db->createCommand()
                        ->from(ChildProfileBasic::model()->tableName())
//                        ->where('childid in (:childid)', array(':childid'=>implode(',', array_keys($childIds))))
                        ->where(array('in','childid',array_keys($childIds)))
                        ->queryAll();
                foreach ($childs as $_chi){
                    $children[$_chi['childid']]=CommonUtils::autoLang($_chi['name_cn'],$_chi['first_name_en'].' '.$_chi['last_name_en']);
                }
				
                $mailer = Yii::createComponent('common.extensions.mailer.EMailer');
                $mailer->Subject = sprintf('【艾毅在线月付费计划 - %s】运行状况汇总 %s' , $branchTitle, date('Y-m-d'));
				
                $mailer->AddAddress($branch->info->support_email);
				$mailer->AddReplyTo($branch->info->support_email);
				$mailer->AddCC('<EMAIL>');
				
				$isProduction = defined("IS_PRODUCTION")? IS_PRODUCTION : false;
				$mailP = ($isProduction) ? 'sendmail' : 'smtp';
                $mailer->iniMail($isProduction, $mailP); // 此行代码要放到AddAddress, AddCC方法下面
				
				$mailDesc = '<p>请核对系统自动生成的账单信息（红色部分表示账单生成失败的原因，请务必根据具体情况认真核实。';
				$mailDesc .= '<br />自动邮件，请勿回复，有问题请联系IT部门。</p>';
                
                $params = array(
                        'mailDesc'=>$mailDesc,
                        'result'=>$result,
                        'classInfo'=>$classInfo,
                        'children'=>$children,
                    );

				$mailer->getCommandView('invoiceTasks', $params, 'main');
                echo "log file:";
				$logfile = Yii::getPathOfAlias('xoopsVarPath').'/monthly_task/'.$plantime.'/'.date('YmdHis').$branchId.'.log';
				echo $logfile;
				echo "\r\n";
                file_put_contents($logfile, $mailer->Body);
				echo "\r\n";
                $mailer->Send();
			}
			echo "\r\n";
		}
	}
}
