<script type="text/javascript">
jQuery(function($) {
    jQuery(document).on('click','#checking a.agree',function() {
        if(!confirm('确定要执行此操作吗?')) return false;
        var th = this;
        afterDelete = function(){};
        jQuery('#checking').yiiGridView('update', {
            type: 'POST',
            url: jQuery(this).attr('href'),
            success: function(data) {
                jQuery('#checking').yiiGridView('update');
                afterDelete(th, true, data);
            },
            error: function(XHR) {
                return afterDelete(th, false, XHR);
            }
        });
        return false;
    });
});
</script>
<div id="childinfo">
    <?php $this->widget('ext.userInfo.UserInfo', array('staff'=>$this->staff,'branches'=>$this->getAllBranch()));?>
</div>
<div class="h_a"><?php echo Yii::t("hr", "人事考勤");?></div>
<div class="table_c1">
<table width=100%>
	<colgroup>
		<col class="th" width=180>
		<col width=null>
		<col width=200>
	</colgroup>
	<tbody>
		<tr>
			<td class="level0">
				<?php
                    $profileMenu = array(
                        'leaveApplication'=>array(
                            'label' => Yii::t("hr", "休假"),
                            'url' => array('//user/hr/index', 't'=>'leavelist')
                        ),
                        'toApplication'=>array(
                            'label' => Yii::t("hr", "加班"),
                            'url' => array('//user/hr/index','t'=>'otlist')
                        ),
                        'toCount'=>array(
                            'label' => Yii::t("hr", "统计"),
                            'url' => array('//user/hr/count')
                        ),
                    );                    
					$this->widget('zii.widgets.CMenu', array(
						'id' => 'invoice-type',
						'items' => $profileMenu,
                        'htmlOptions' => array('class'=>'subm'),
                        'activeCssClass' => 'current'                        
                        )
					);
				?>
			</td>
			<td>
                <div id="leave-edit">
                    <?php
                        $this->widget('ext.ivyCGridView.IvyCGridView', array(
                            'id'=>'checking',
                            'dataProvider'=>$dataProvider,
                            'template'=>"<div class='table_list'>{items}</div><div class='table_list'>{summary}</div><div class='table_list'>{pager}</div>",
                            'colgroups'=>array(
                                array(
                                    "colwidth"=>array(80,150,90, 120, 120),
                                )
                            ),
                            'columns'=>array(
                                'approver'=>array(
                                    'name'=>'leave_id',
                                    'value'=>'UserLeave::getStartYear($data->userLeave->startyear)',
                                ),
                                'staff_uid'=>array(
                                    'name'=>'staff_uid',
                                    'value'=>'$data->appUser->getName()',
                                ),
                                'startdate'=>array(
                                    'name'=>'startdate',
                                    'value'=>'OA::formatDateTime($data->startdate)',
                                ),
                                'enddate'=>array(
                                    'name'=>'startdate',
                                    'value'=>'OA::formatDateTime($data->enddate)',
                                ),
                                'hours'=>array(
                                    'name'=>'hours',
                                    'value'=>'UserLeave::getLeaveFormat($data->hours)',
                                ),
                                'applydate'=>array(
                                    'name'=>'applydate',
                                    'value'=>'OA::formatDateTime($data->applydate)',
                                ),
                                array(
                                    'class'=>'CButtonColumn',
                                    'template' => '{agree}{disagree}',
                                    'buttons'=>array(
                                        'agree'=>array(
                                            'label'=>Yii::t('hr', '有资料'),
                                            'url'=>'Yii::app()->createUrl("/user/hr/docchecking", array("id"=>$data->id,"agree"=>"ok"))',
                                            'options'=>array('class'=>'agree','style'=>'padding-right:10px;'),
                                        ),
                                        'disagree'=>array(
                                            'label'=>Yii::t('hr', '无资料'),
                                            'url'=>'Yii::app()->createUrl("/user/hr/docchecking", array("id"=>$data->id))',
                                            'options'=>array('class'=>'agree'),
                                        ),
                                    ),
                                ),
                            )
                        )); ?>
				</div>
			</td>
		</tr>
	</tbody>
</table>
</div>