<?php

/**
 * This is the model class for table "ivy_asa_cash_handover".
 *
 * The followings are the available columns in table 'ivy_asa_cash_handover':
 * @property integer $id
 * @property string $site_id
 * @property string $program_id
 * @property double $amount
 * @property integer $item_number
 * @property string $invoice_ids
 * @property integer $status
 * @property integer $creater
 * @property integer $created
 * @property integer $handover_type
 * @property integer $receiver
 * @property integer $received
 * @property string $bank_ref
 * @property string $bank_ref_url
 * @property integer $bank_deposit_time
 * @property string $creater_memo
 * @property string $received_memo
 */
class AsaCashHandover extends CActiveRecord
{
	const STATUS_DRAFT = 0;
	const STATUS_SUBMITTED = 1;
	const STATUS_CONFIRMED = 2;
	const STATUS_REJECTED = 99;
	
	public $bank_ref_file;
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_asa_cash_handover';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('item_number, status, creater, created, handover_type, receiver, received, bank_deposit_time', 'numerical', 'integerOnly'=>true),
			array('amount', 'numerical'),
			array('site_id, program_id, invoice_ids, bank_ref, bank_ref_url', 'length', 'max'=>255),
			array('creater_memo, bank_ref_file, received_memo', 'safe'),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, site_id, program_id, amount, item_number, invoice_ids, status, creater, created, handover_type, receiver, received, bank_ref, bank_ref_url, bank_deposit_time, creater_memo, received_memo', 'safe', 'on'=>'search'),
			array("bank_ref_file", "file", "types" => "jpg, gif, png, jpeg, pdf", "maxSize" => 1024*1024*2, "allowEmpty" => true),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'site_id' => Yii::t('asa','学校'),
			'program_id' => Yii::t('asa','课程组'),
			'amount' => Yii::t('asa','金额'),
			'item_number' => Yii::t('asa','账单明细数量'),
			'invoice_ids' => Yii::t('asa','账单ID'),
			'status' => Yii::t('asa','状态'),
			'creater' => Yii::t('asa','创建人'),
			'created' => Yii::t('asa','创建时间'),
			'handover_type' => Yii::t('asa','支付方式'),
			'receiver' => Yii::t('asa','确定人'),
			'received' => Yii::t('asa','确定时间'),
			'bank_ref' => Yii::t('asa','银行回单号'),
			'bank_ref_url' => Yii::t('asa','汇单照片'),
			'bank_deposit_time' => Yii::t('asa','存入银行时间'),
			'creater_memo' => Yii::t('asa','备注(创建人)'),
			'received_memo' => Yii::t('asa','备注(确定人)'),
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('site_id',$this->site_id,true);
		$criteria->compare('program_id',$this->program_id,true);
		$criteria->compare('amount',$this->amount);
		$criteria->compare('item_number',$this->item_number);
		$criteria->compare('invoice_ids',$this->invoice_ids,true);
		$criteria->compare('status',$this->status);
		$criteria->compare('creater',$this->creater);
		$criteria->compare('created',$this->created);
		$criteria->compare('handover_type',$this->handover_type);
		$criteria->compare('receiver',$this->receiver);
		$criteria->compare('received',$this->received);
		$criteria->compare('bank_ref',$this->bank_ref,true);
		$criteria->compare('bank_ref_url',$this->bank_ref_url,true);
		$criteria->compare('bank_deposit_time',$this->bank_deposit_time);
		$criteria->compare('creater_memo',$this->creater_memo,true);
		$criteria->compare('received_memo',$this->received_memo,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return AsaCashHandover the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	protected function beforeSave()
	{
		if (parent::beforeSave()) {
			if($this->bank_ref_file)
			{
			   	$file_ext = $this->bank_ref_file->getExtensionName();
	         	$picPath = Yii::app()->params['xoopsVarPath'] . '/asa/handover/';
	         	$picName = '5_' . uniqid() . '.' . $file_ext;
	         	if ($this->bank_ref_file->saveAs($picPath . $picName)){
		   	      	$this->bank_ref_url = $picName;
			   	}
			}
   	      	return true;
		}
		return false;
	}

	public static function getConfig($lang=null)
	{
		return  array(
			'0' => '草稿',
			'1' => '已提交',
			'2' => '已完成',
			'99'=> '驳回',
		);
	}
}
