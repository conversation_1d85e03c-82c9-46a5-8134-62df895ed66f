<?php
class IncomeController extends BranchBasedController
{
    public $batchNum = 200;
    public function createUrl($route, $params = array(), $ampersand = '&')
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        return parent::createUrl($route, $params, $ampersand);
    }

    public function init()
    {
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'campusOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site', 'Campus Workspace');

        $this->branchSelectParams['urlArray'] = array('//masa/course/index');

        // jquery ui
        $cs = Yii::app()->clientScript;
        $cs->registerCoreScript('jquery.ui');
        $cs->registerCssFile($cs->getCoreScriptUrl() . '/jui/css/base/jquery-ui.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/vue2.js');

        $baseDir = Yii::getPathOfAlias('common.extensions.ueditor.ueditor_full');
        $assets = Yii::app()->getAssetManager()->publish($baseDir, true, -1);
        $cs->registerScriptFile($assets . '/ueditor.teacher.config.js');
        $cs->registerScriptFile($assets . '/ueditor.all.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/clipboard.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/xlsx.full.min.js');
    }

    public function actionIndex($groupId = 0)
    {
        $courseGroup = AsaCourseGroup::getGroup($this->branchId, false, false, false);
        $newCourseGroup = array();
        //        echo "<pre>";
        $courseGroupArr = json_decode(CJSON::encode($courseGroup), TRUE);
        foreach ($courseGroupArr as $v) {
            $newCourseGroup[$v['startyear']][] = $v;
        }
        $startyearArr = array_keys($newCourseGroup);
        $this->render('index', array(
            'startyearArr' => $startyearArr,
//            'courseGroup' => $courseGroup,
            'newCourseGroup' => $newCourseGroup,
            'groupId' => $groupId,
            'schoolid' => $this->branchId,
        ));
    }
}
