<?php
$msType = IvyClass::getClassTypes(true,40);

echo $this->renderPartial('steps_header', array('currentStep'=>9)); ?>
<style>
    #signature-div {
        width:100%;
        height:100%;
        display: none;
        position: fixed;
        left: 0;
        top:0;
        z-index:999;
        background-color: white;
    }
    #signature-div p{
        position: absolute;
        bottom:0;
        left:50%;
        margin-left: -91px
    }
    .signature-pad {
        position: absolute;
        left: 0;
        top: 0;
        width:100%;
        height:100%;
        background-color: white;
    }
    .signCan{
        position: relative;
        -webkit-box-flex: 1;
        -ms-flex: 1;
        flex: 1;
        border: 1px solid #f4f4f4;
    }
    .signBody{
        position: relative;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -ms-flex-direction: column;
        flex-direction: column;
        font-size: 10px;
        width: 100%;
        height: 100%;
        max-height:300px;
        background-color: #fff;
        border-radius: 4px;
        padding: 16px;
    }
    #signature-data img{
        width:100px
    }
    .signText{
        padding:16px;
    }
    .del{
        position: absolute;
        right: 4px;
        top: -10px;
        font-size: 25px;
        width: 20px;
        height: 20px;
        text-align: center;
        line-height: 20px;
        border: 1px solid #ddd;
        border-radius: 50%;
    }
    .main-site{
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background: #000;
        opacity: 0.4;
    }
    #mainContent{
        position: absolute;
        width: 120px;
        height: 100px;
        top: 50%;
        left: 50%;
        margin-top: -50px;
        margin-left: -60px;
    }
    #mainContent>p{
        margin-left: 6px;
        color: #EF7F00;
        font-size: 1.4rem;
        font-weight: bold;
    }
    .loading{
        display: none;
    }
    .mark{
        position: fixed;
        left: 0;
        top:0;
        width: 100%;
        height:100%;
        background: #000;
        opacity: 0.3;
        z-index: 999;
    }
    .spinner {
        width: 150px;
        text-align: center;
        z-index:9999;
        position: fixed;
        left: 50%;
        top:50%;
        margin-left:-75px;
    }

    .spinner > div {
        width: 30px;
        height: 30px;
        background-color: #1eaaf1;
        z-index:9999;
        opacity: 1;
        border-radius: 100%;
        display: inline-block;
        -webkit-animation: bouncedelay 1.4s infinite ease-in-out;
        animation: bouncedelay 1.4s infinite ease-in-out;
        -webkit-animation-fill-mode: both;
        animation-fill-mode: both;
    }

    .spinner .bounce1 {
        -webkit-animation-delay: -0.32s;
        animation-delay: -0.32s;
    }

    .spinner .bounce2 {
        -webkit-animation-delay: -0.16s;
        animation-delay: -0.16s;
    }

    @-webkit-keyframes bouncedelay {
        0%, 80%, 100% { -webkit-transform: scale(0.0) }
        40% { -webkit-transform: scale(1.0) }
    }

    @keyframes bouncedelay {
        0%, 80%, 100% {
            transform: scale(0.0);
            -webkit-transform: scale(0.0);
        } 40% {
              transform: scale(1.0);
              -webkit-transform: scale(1.0);
          }
    }
</style>
<div class="container">
    <h5 class="htitle"><span class="fas fa-user-md"></span> <?php echo $this->getStepTitle($this->step); ?></h5>
    <?php echo $this->renderPartial('_nouser_header'); ?>
    <div class="edit-content">
        <?php
        $content = RegConfig::getContent($this->regStudent->reg_id, $this->step);
        if ($content) {
            echo CommonUtils::autoLang(base64_decode($content['cn']['student']), base64_decode($content['en']['student']));
        }
        ?>
    </div>
    <div class="">
        <!-- 英文名 -->
        <?php $form = $this->beginWidget('CActiveForm', array(
            'id' => 'sep1-form',
            'enableAjaxValidation' => false,
            'htmlOptions' => array(
                // 'class'=>'J_ajaxForm form-horizontal',
                'role' => 'form',
                'enctype' => 'multipart/form-data'
            ),
        )); ?>


        <div class="form-group">
            <?php echo Yii::t('reg','Student Passport Photo (only for foreign student)'); ?>
            <input class="form-control-file" style="display:none;" onchange="fileChange(this,'studentdataFile','studentdata')" id="RegStep9Form_studentdataFile1" type="file" accept="image/*">
            <p><button class="btn btn-info btn-sm" type="button" onclick="choseImg(1)"><?php echo Yii::t('reg', "Click to Upload") ?></button></p>
            <div class="row studentdataFileImgList">
                <?php foreach ($formModel9->studentdata as $item) :?>
                    <div class="col-4 mb-3">
                        <img class="shadow img-fluid mb-2 rounded" src="<?php echo $formModel9->getOssImageUrl($item); ?>">
                        <span aria-hidden="true" class="del" onclick="deletes(this)">&times;</span>
                        <input type="text" hidden name='RegStep9Form[studentdata][]' value="<?php echo $item; ?>">
                    </div>
                <?php endforeach; ?>
            </div>
        </div>

        <div class="form-group">
            <?php echo Yii::t('reg','Mother Passport Photo'); ?>
            <input class="form-control-file" style="display:none;" onchange="fileChange(this,'parentsdataFile','parentsdata')" id="RegStep9Form_studentdataFile2" type="file" accept="image/*">
            <p><button class="btn btn-info btn-sm" type="button" onclick="choseImg(2)"><?php echo Yii::t('reg', "Click to Upload") ?></button></p>
            <div class="row parentsdataFileImgList">
                <?php foreach ($formModel9->parentsdata as $item) :?>
                    <div class="col-4 mb-3">
                        <img class="shadow img-fluid mb-2 rounded" src="<?php echo $formModel9->getOssImageUrl($item); ?>">
                        <span aria-hidden="true" class="del" onclick="deletes(this)">&times;</span>
                        <input type="text" hidden name='RegStep9Form[parentsdata][]' value="<?php echo $item; ?>">
                    </div>
                <?php endforeach; ?>
            </div>
        </div>

        <div class="form-group">
            <?php echo Yii::t('reg','Photo Copy of Vaccine Record(Please upload all the vaccine record)'); ?>
            <input class="form-control-file" style="display:none;" onchange="fileChange(this,'vaccinedataFile','vaccinedata')" id="RegStep9Form_studentdataFile3" type="file" accept="image/*">
            <p><button class="btn btn-info btn-sm" type="button" onclick="choseImg(3)"><?php echo Yii::t('reg', "Click to Upload") ?></button></p>
            <div class="row vaccinedataFileImgList">
                <?php foreach ($formModel9->vaccinedata as $item) :?>
                    <div class="col-4 mb-3">
                        <img class="shadow img-fluid mb-2 rounded" src="<?php echo $formModel9->getOssImageUrl($item); ?>">
                        <span aria-hidden="true" class="del" onclick="deletes(this)">&times;</span>
                        <input type="text" hidden name='RegStep9Form[vaccinedata][]' value="<?php echo $item; ?>">
                    </div>
                <?php endforeach; ?>
            </div>
        </div>

        <div class="form-group">
            <?php echo Yii::t('reg','Commercial Medical Insurance Card Photo (optional)'); ?>
            <input class="form-control-file" style="display:none;" onchange="fileChange(this,'insurancedataFile','insurancedata')" id="RegStep9Form_studentdataFile4" type="file" accept="image/*">
            <p><button class="btn btn-info btn-sm" type="button" onclick="choseImg(4)"><?php echo Yii::t('reg', "Click to Upload") ?></button></p>
            <div class="row insurancedataFileImgList">
                <?php foreach ($formModel9->insurancedata as $item) :?>
                    <div class="col-4 mb-3">
                        <img class="shadow img-fluid mb-2 rounded" src="<?php echo $formModel9->getOssImageUrl($item); ?>">
                        <span aria-hidden="true" class="del" onclick="deletes(this)">&times;</span>
                        <input type="text" hidden name='RegStep9Form[insurancedata][]' value="<?php echo $item; ?>">
                    </div>
                <?php endforeach; ?>
            </div>
        </div>

        <div class="mt-3">
            <button type="submit" class="btn btn-primary btn-lg btn-block examine"><?php echo Yii::t('reg', 'Submit') ?></button>
            <p class="text-black-50 mt-3 text-center examine_status">

            </p>
        </div>
        <?php $this->endWidget(); ?>
    </div>
</div>


<div class="loading">
    <div class="mark"></div>
    <div class="spinner">
        <div class="bounce1"></div>
        <div class="bounce2"></div>
        <div class="bounce3"></div>
    </div>
</div>

<script>
    function choseImg(k) {
        var fileObj = $('#RegStep9Form_studentdataFile'+k);
        $(fileObj).click();
    }
    function fileChange(obj,html,input){
        var file=obj.files[0]
        if(file==undefined){
            return
        }
        $('.loading').show()
        EXIF.getData(file, function() {
            EXIF.getAllTags(this);
            Orientation = EXIF.getTag(this, 'Orientation');
        });
        var base64='';
        var oReader = new FileReader();
        oReader.onload = function(e) {
            var image = new Image();
            image.src = e.target.result;
            image.onload = function() {
                var expectWidth = this.naturalWidth;
                var expectHeight = this.naturalHeight;

                if (this.naturalWidth > this.naturalHeight && this.naturalWidth > 800) {
                    expectWidth = 800;
                    expectHeight = expectWidth * this.naturalHeight / this.naturalWidth;
                } else if (this.naturalHeight > this.naturalWidth && this.naturalHeight > 1200) {
                    expectHeight = 1200;
                    expectWidth = expectHeight * this.naturalWidth / this.naturalHeight;
                }
                var canvas = document.createElement("canvas");
                var ctx = canvas.getContext("2d");
                canvas.width = expectWidth;
                canvas.height = expectHeight;
                ctx.drawImage(this, 0, 0, expectWidth, expectHeight);
                //修复ios
                if (navigator.userAgent.match(/iphone/i)) {
                    if(Orientation != "" && Orientation != 1){
                        // alert('旋转处理');
                        switch(Orientation){
                            case 6://需要顺时针（向左）90度旋转
                                rotateImg(this,'left',canvas);
                                break;
                            case 8://需要逆时针（向右）90度旋转
                                rotateImg(this,'right',canvas);
                                break;
                            case 3://需要180度旋转
                                rotateImg(this,'right',canvas);//转两次
                                rotateImg(this,'right',canvas);
                                break;
                        }
                    }
                    base64 = canvas.toDataURL("image/jpeg", 0.8);
                }else {
                    base64 = canvas.toDataURL("image/jpeg", 0.8);
                }
                $(".base").attr("src", base64);
                formData = new FormData();
                formData.append('fileName',html);
                formData.append('RegStep9Form['+html+']',dataURLtoFile(base64, file.name));
                var xhr = new XMLHttpRequest();
                xhr.open("post","<?php echo $this->createUrl('uploadFileIvy', array('childid'=>$this->childObj->childid)); ?>");
                xhr.send(formData);
                xhr.onreadystatechange = function() {
                    //编写回调函数
                    if (xhr.readyState == 4 && xhr.status == 200) {
                        var responseText = JSON.parse(xhr.responseText);
                        if(responseText.state=="success"){
                            imgurl=responseText.data.fileUrl
                            var str='<div class="col-4 mb-3">'+
                                '<img class="shadow img-fluid mb-2 rounded" src='+imgurl+' id="5_photoShow">'+
                                '<span aria-hidden="true" class="del" onclick="deletes(this)">&times;</span>'+
                                '<input type="text" hidden name="RegStep9Form['+input+'][]" value='+responseText.data.fileName+'>'+
                                '</div>'
                            $('.loading').hide()
                            $('.'+html+'ImgList').append(str)
                        }else{
                            $('.loading').hide()
                            alert(responseText.message)
                        }
                    }
                }
            };
        };
        oReader.readAsDataURL(file);
    }
    function dataURLtoFile(dataurl, filename) {//将base64转换为文件
        var arr = dataurl.split(','), mime = arr[0].match(/:(.*?);/)[1],
            bstr = atob(arr[1]), n = bstr.length, u8arr = new Uint8Array(n);
        while(n--){
            u8arr[n] = bstr.charCodeAt(n);
        }
        return new File([u8arr], filename, {type:mime});
    }
    function deletes(obj){
        $(obj).parent().remove()
    }

    // 点击退出全屏
    $('.exit').click(function() {
        $('html').css({
            'overflow': 'initial',
            'position': 'initial'
        });
        $('html,body').scrollTop(scrollTops);
        var canvas = document.getElementById('signature-pad');
        var data = canvas.toDataURL("image/png");
        // $("#signature-data").attr('src', data);
        $("#signature-data").html('<img src=' + data + ' >')
        $("#filedata").val(data);
        $("#signature-data").show();
        $('#signature-div').hide();
        var width = canvas.width / 8
        var height = canvas.height / 8
        $('#signature-data img').height(height)
        $('#signature-data img').width(width)
    })
    // 重写
    $('.clear').click(function() {
        signatureInit();
    })
    var canvas = document.getElementById('signature-pad');
    $('.dropOut').click(function(){
        $('#signature-div').hide();
        clea = canvas.getContext("2d");
        clea.clearRect(0,0,500,500);
        $('html').css({
            'overflow': 'initial',
            'position': 'initial'
        });
        $('html,body').scrollTop(scrollTops);
    })
    // 点击全名书写
    $('#signature-save').click(function() {
        //canvas全屏
        $('#signature-div').show();
        signatureInit();
    });
    // 初始化画板
    var scrollTops;
    function signatureInit() {
        scrollTops = $(window).scrollTop();
        $('html').css({
            'overflow': 'hidden',
            'position': 'fixed'
        })
        ratio = Math.max(window.devicePixelRatio || 1, 1);
        canvas.width = canvas.offsetWidth * ratio;
        canvas.height = canvas.offsetHeight * ratio;
        canvas.getContext("2d").scale(ratio, ratio);
        var signaturePad = new SignaturePad(canvas);
        watermark(canvas);
    }

    function hengshuping() {
        if(window.orientation == 180 || window.orientation == 0) {
            if($('.wrapper').is(':visible')){
                clea = canvas.getContext("2d");
                clea.clearRect(0,0,500,500);
                setTimeout("signatureInit()",200);
            }
        }
        if(window.orientation == 90 || window.orientation == -90) {
            if($('.wrapper').is(':visible')){
                clea = canvas.getContext("2d");
                clea.clearRect(0,0,500,500);
                setTimeout("signatureInit()",200);
            }
        }
    }
    window.addEventListener("onorientationchange" in window ? "orientationchange" : "resize", hengshuping, false);
    // 绘制水印
    function watermark(canvas) {
        var date = new Date();
        var year = date.getFullYear();
        var month = date.getMonth() + 1;
        var strDate = date.getDate();
        if(month >= 1 && month <= 9) {
            month = "0" + month;
        }
        if(strDate >= 0 && strDate <= 9) {
            strDate = "0" + strDate;
        }
        var currentdate = year + month + strDate
        var cw = $('#watermark')[0];
        cw.width = $(document).width();
        var ctx = cw.getContext("2d");   //返回一个用于在画布上绘图的环境


        ctx.fillStyle = "rgba(100,100,100,0.1)";
        if(window.orientation == 90 || window.orientation == -90) {
            ctx.font="30px 黑体";
            ctx.rotate(-10*Math.PI/180);
            $('.signText').hide()
            ctx.fillText("<?php echo Yii::t('reg', "School nurse agreement") ?>" + currentdate + "",100, 130);
        } else if(window.orientation == 0 || window.orientation == 180) {
            ctx.font="20px 黑体";
            ctx.rotate(-18*Math.PI/180);
            $('.signText').show()
            ctx.fillText("<?php echo Yii::t('reg', "School nurse agreement") ?>" + currentdate + "", 0, 130);
        }

        ctx.rotate('20*Math.PI/180');  //坐标系还原
        var crw =canvas;
        ctxr = canvas.getContext("2d");
        ctxr.width = $(window).height();
        ctxr.clearRect(0,0,500,500);  //清除整个画布
        var pat = ctxr.createPattern(cw, "repeat");    //在指定的方向上重复指定的元素
        ctxr.fillStyle = pat;
        ctxr.fillRect(0, 0, crw.width, crw.height);
    }


    //对图片旋转处理 added by lzk
    function rotateImg(img, direction,canvas) {
        var min_step = 0;
        var max_step = 3;
        if (img == null)return;
        var height = img.height;
        var width = img.width;
        var step = 2;
        if (step == null) {
            step = min_step;
        }
        if (direction == 'right') {
            step++;
            //旋转到原位置，即超过最大值
            step > max_step && (step = min_step);
        } else {
            step--;
            step < min_step && (step = max_step);
        }
        //旋转角度以弧度值为参数
        var degree = step * 90 * Math.PI / 180;
        var ctx = canvas.getContext('2d');
        switch (step) {
            case 0:
                canvas.width = width;
                canvas.height = height;
                ctx.drawImage(img, 0, 0);
                break;
            case 1:
                canvas.width = height;
                canvas.height = width;
                ctx.rotate(degree);
                ctx.drawImage(img, 0, -height);
                break;
            case 2:
                canvas.width = width;
                canvas.height = height;
                ctx.rotate(degree);
                ctx.drawImage(img, -width, -height);
                break;
            case 3:
                canvas.width = height;
                canvas.height = width;
                ctx.rotate(degree);
                ctx.drawImage(img, -width, 0);
                break;
        }
    }
</script>
