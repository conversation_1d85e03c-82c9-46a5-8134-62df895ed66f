<!-- 加载底部导航栏 -->
<?php
//$this->branchSelectParams['extraUrlArray'] = array('//mgrades/attendance/index');
//$this->renderPartial('//layouts/common/branchSelectBottom');

?>
<style>
    [v-cloak] {
        display: none;
    }
    .btn-default.active{
        color: #fff;
        background-color: #337ab7;
        border-color: #2e6da4;
    }
    .table{
        color:#606266
    }
    .table tr th {
        vertical-align:middle !important;
        text-align:center;
        color:#606266;
    }
    .blue{
        background:#DFF0D8 !important;
    }
    .blue .icon{
        color:#68AB3B
    }
    .course{
        padding: 8px;
        border-radius: 4px;
        color: #666666;
        background: #FAFAFA;
        border-radius: 4px;
        margin-bottom: 8px;
    }
    .cursor{
        cursor:pointer
    }
    .mb2{
        margin-bottom:2px
    }
    .middle{
        vertical-align: middle !important;
    }
    .switch {
        position: relative;
        display: inline-block;
    }
    .switch input {display:none;}
    .slider {
        margin: 0;
        display: inline-block;
        position: relative;
        width: 40px;
        height: 20px;
        border: 1px solid #dcdfe6;
        outline: none;
        border-radius: 10px;
        box-sizing: border-box;
        background: #dcdfe6;
        cursor: pointer;
        transition: border-color .31s,background-color .3s;
        vertical-align: middle;
    }
    .slider:before {
        content: "";
        position: absolute;
        top: 1px;
        left: 1px;
        border-radius: 50%;
        transition: all .3s;
        width: 16px;
        height: 16px;
        background-color: #fff; 
    }
    input:checked + .slider {
        background-color: #2196F3;
    }
    input:checked + .slider:before {
        left: 100%;
        margin-left: -17px;
    }
    .detailed{
        display:none
    }
    .showDetailed .detailed{
        display:block
    }
    .green{
        background:#fbf17d;
        padding:4px;
        color:#5e3e04;
        margin: 2px 0;
        display: inline-block;
        height: 22px;
    }
    .showConflict .linkshow{
        display:none
    }
    .omit{
        overflow:hidden;
        text-overflow: ellipsis;
        white-space:nowrap;
    }
</style>
<div class="container-fluid">
    <!-- 面包屑导航 -->
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('//mcampus/default/index')) ?></li>
        <li class="active"><?php echo Yii::t('site', 'G6-12 Schedule Management'); ?></li>
    </ol>
    <div class="row">
        <div class="col-md-2">
            <?php
                $this->widget('zii.widgets.CMenu',array(
                    'items'=> $this->siderMenu,
                    'htmlOptions'=>array('class'=>'nav nav-pills nav-stacked text-right background-gray mb10'),
                    'activeCssClass'=>'active',
                    'itemCssClass'=>''
                ));
            ?>
        </div>
        <div class="col-md-10"  id='container'  v-cloak >
            <div class="row">
                <div class="col-md-12" >
                    <p class='color3 pb20 borderBto' ><strong style='font-size:18px'><?php echo Yii::t('attends','Schedules Overview');?></strong> <span class='ml20 font14'>{{startYear}}</span> </p>
                    <p>
                    <button type="button" class="btn btn-default mr20" v-for='(list,index) in category' :class='current==list?"active":""' @click='showTab(list)'>{{list}} <?php echo Yii::t('global','Weekly schedule') ?></button>
                    </p>
                    <div class='mt20' v-if='current!=""'>
                        <span >
                            <label class="switch">
                                <input type="checkbox" :checked='conflictCourse?true:false' @change='conflictClick()'>
                                <span class="slider"></span>
                            </label>
                            <span class='ml5' style='padding-top:3px'><?php echo Yii::t('attends','Only show conflicted courses (conflics of classroom and teacher)');?></span>
                        </span>
                    </div>
                    <div class='mt10' v-if='current!="" && !conflictCourse'>
                        <span >
                            
                            <label class="switch">
                                <input type="checkbox" :checked='isTab?true:false' @change='tabSimple()'>
                                <span class="slider"></span>
                            </label>
                            <span class='ml5' style='padding-top:3px'><?php echo Yii::t('attends','Display course details');?></span>
                        </span>
                    </div>
                    <p v-if='current!="" && !conflictCourse' class='mt10'>
                        <label  class='mr20'>
                            <input type="checkbox"  v-model='classAll' @change='selectAll($event)' ref='selectAll' value="key"> <?php echo Yii::t('global','Select All') ?> 
                        </label>
                        <label  v-for='(list,key,index) in classData' class='mr20'>
                            <input type="checkbox"  v-model='classList' @click='select(key)' :value="key"> {{list}}
                        </label>
                    </p>
                    <table class='table table-bordered mt20' style='table-layout: fixed' ref='table' v-if='current!="" && classList.length!=0'>
                        <thead >
                            <tr class='text-center'>
                                <th width='100' >
                                <?php echo Yii::t('labels','Time') ?>
                                </th>
                                <th v-for='(list,id) in weekLang' class='relative font14'>
                                    <div>{{list}}</div>
                                </th>
                            </tr>
                        </thead>
                        <tbody  :class='conflictCourse?"showConflict showDetailed":isTab?"showDetailed":""'>
                            <tr v-for='(data,index) in tableData'>
                                <th class='text-center'>
                                    <div class='font14'>{{timetableTimes[index].label}}</div>
                                    <div style='font-weight:200'>{{timetableTimes[index].timeslot}}</div>
                                </th>
                                <template v-for='(item,idx) in data'>
                                    <td  :class='item==null?"middle":""'>
                                        <template v-for='(_item,i) in item' >
                                            <div v-if='classList.indexOf(rep(_item[1]))!=-1'>
                                                <div class='flex course' :class='courseId==_item[1]?"blue":showSame(index,idx,_item[3],"teacher") || showSame(index,idx,_item[2],"room")?"":"linkshow"'>
                                                    <div style='width:20px'>
                                                        <span class='glyphicon glyphicon-asterisk icon cursor'  aria-hidden="true" onMouseOver="$(this).tooltip('show')"  data-toggle="tooltip" data-html='true' title='<?php echo Yii::t('global','Highlight same course') ?>'  data-placement="top" @click='showCourse(_item[1])'></span>
                                                    </div>
                                                    <div v-if='_item!=null'  class='color6 flex1' >
                                                        <div class='color3 flex'>
                                                            <strong class='flex1'><span v-if='!isTab' class='color6' style='font-weight:100'>{{_item[1]}}</span>

                                                                <a v-if="_item[1].includes('MEET.')" href='javascript:;' @click='showMeetTeacher(_item[1])'> {{courses[_item[1]]}}</a>
                                                                <a v-else href='javascript:;' @click='showChild(idx,index,_item[1])'> {{courses[_item[1]]}}</a>

                                                            </strong>

                                                            <div class="btn-group  text-right" style='width:30px'>
                                                                <button type="button" class="btn btn-link pull-right  dropdown-toggle"  data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" style='padding:0;margin-top:-3px'>
                                                                    ...
                                                                </button>
                                                                <ul class="dropdown-menu">


                                                                    <li><a href="javascript:;" @click='addCourse(idx,index,_item)'><?php echo Yii::t('attends','Edit classroom') ?></a></li>
                                                                    <li><a href="javascript:;" @click='delCourse(_item)'><?php echo Yii::t('global','Delete') ?></a></li>
                                                                </ul>
                                                            </div>
                                                        </div>
                                                        <div class='mt10 detailed'>
                                                            <div class='mb2'>{{_item[1]}}</div>
                                                            <div class='mb2' ><span :class='showSame(index,idx,_item[3],"teacher")?"green":""'> {{_item[3]}}</span>     </div>
                                                            <div class='mb2'  ><span :class='showSame(index,idx,_item[2],"room")?"green":""'>{{_item[2]}}</span> </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </template>
                                        <div class='text-center mt20'>
                                            <span class='glyphicon glyphicon-plus-sign cur-p ' style='font-size:18px;color:#428BCA' @click='addCourse(idx,index)'></span>
                                        </div>
                                    </td>
                                </template>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal fade" id="childModel" tabindex="-1" role="dialog" aria-labelledby="modal" aria-hidden="false">
                <div class="modal-dialog modal-lg" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                            <h4 class="modal-title" ><?php echo Yii::t('attends','Student List') ?> <span id="remarks_t"></span></h4>
                        </div>
                        <div class="form-horizontal">
                            <div class="modal-body" v-if='childData.courseInfo'>
                                <div class='p10'>
                                    <p class='color3 font14'><strong>{{code}} {{childData.courseInfo.title}}</strong></p>
                                    <p class='color3 font14 mt10'><span><span class='color6'><?php echo Yii::t('newDS','Teacher') ?><?php echo Yii::t('global',': ') ?></span>{{childData.courseInfo.teacherName}}</span> <span class='ml20'><span class='color6'><?php echo Yii::t('labels','Room NO.') ?><?php echo Yii::t('global',': ') ?></span>{{childData.courseInfo.room}}</span></p>
                                    <div class='color6 mt20 font14'><?php echo Yii::t('attends','Student List') ?>  <span class="badge">{{childData.studentList.length}}</span></div>
                                    <div class='col-md-4' v-for='(list,index) in childData.studentList' >
                                        <div class="media mt15">
                                            <div class="media-left pull-left media-middle">
                                            <a href="javascript:void(0)">
                                                <img :src="list.avatar" data-holder-rendered="true" style='width:48px;height:48px' class="media-object img-circle image"></a>
                                            </div>
                                            <div class="media-body pt10 media-middle">
                                                <h4 class="media-heading font12">({{list.id}}) {{list.name}}</h4>
                                                <div class="text-muted">{{list.class_name}}</div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class='clearfix'></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="modal fade" id="meetTeacherModel" tabindex="-1" role="dialog" aria-labelledby="modal" aria-hidden="false">
                <div class="modal-dialog modal-lg" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                            <h4 class="modal-title" ><?php echo Yii::t('attends','Meet') ?> <span id="remarks_t"></span></h4>
                        </div>
                        <div class="form-horizontal">
                            <div class="modal-body" v-if='meetTeacher.meet_base_data'>
                                <div class='p10'>
                                <p class='color3 font14'><strong>{{meetTeacher.meet_base_data.course_code}} {{meetTeacher.meet_base_data.title}}</strong></p>
<!--                                    <div>-->
                                        <div class='color6 mt20 font14'><?php echo Yii::t('attends','Meet Owner') ?></div>
                                        <div class='col-md-4'>
                                            <div class="media mt15">
                                                <div class="media-left pull-left media-middle">
                                                    <a href="javascript:void(0)">
                                                        <img :src="meetTeacher.meet_teachers[meetTeacher.meet_leader_id]['photoUrl']" data-holder-rendered="true" style='object-fit:cover;width:48px;height:48px' class="media-object img-circle image"></a>
                                                </div>
                                                <div class="media-body pt10 media-middle">
                                                    <h4 class="media-heading font12">
                                                        {{meetTeacher.meet_teachers[meetTeacher.meet_leader_id]['name']}}&nbsp;&nbsp;<span class="label label-success">发起人</span></h4>
                                                    <div class="text-muted">
                                                        {{meetTeacher.meet_teachers[meetTeacher.meet_leader_id]['hrPosition']}}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class='clearfix'></div>
<!--                                    </div>-->
<!--                                    <div>-->
                                        <div class='color6 mt20 font14'><?php echo Yii::t('attends','Members') ?>  <span class="badge">{{meetTeacher.acceding_teacher_id.length}}</span></div>
                                        <div class='col-md-4' v-for='(list,index) in meetTeacher.acceding_teacher_id' >
                                            <div class="media mt15">
                                                <div class="media-left pull-left media-middle">
                                                    <img :src="meetTeacher.meet_teachers[list]['photoUrl']" data-holder-rendered="true" style='object-fit:cover;width:48px;height:48px' class="media-object img-circle image">
                                                </div>
                                                <div class="media-body pt10 media-middle">
                                                    <h4 class="media-heading font12">{{meetTeacher.meet_teachers[list]['name']}}</h4>
                                                    <div class="text-muted omit" >{{meetTeacher.meet_teachers[list]['hrPosition']}}</div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class='clearfix'></div>
<!--                                    </div>-->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="modal fade" id="editModel" tabindex="-1" role="dialog" aria-labelledby="modal" aria-hidden="false" data-backdrop="static" data-keyboard="false">
                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                            <h4 class="modal-title" >{{editCourseId=='-1'?"<?php echo Yii::t('attends','Add a Course') ?> ":"<?php echo Yii::t('attends','Edit classroom') ?> "}}<span id="remarks_t"></span></h4>
                        </div>
                        <div class="modal-body">
                            <div class="form-horizontal">
                                <div class="form-group">
                                    <label  class="col-sm-2 control-label"><?php echo Yii::t('labels','Time') ?><?php echo Yii::t('global',': ') ?></label>
                                    <div class="col-sm-10">
                                        <select class="form-control select_3 pull-left" v-model='weekId' :disabled='isEditAdd?true:false'>
                                            <option v-for='(list,index) in weekLang' :value='index'>{{list}}</option>
                                        </select>
                                        <select class="form-control select_3 pull-left ml10"  v-model='times' :disabled='isEditAdd?true:false'>
                                            <option v-for='(list,index) in timetableTimes' :value='index'>{{list.label}}</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label  class="col-sm-2 control-label"><?php echo Yii::t('user','Grade') ?><?php echo Yii::t('global',': ') ?></label>
                                    <div class="col-sm-10">
                                        <select class="form-control" v-model='ediGrade' @change='filterGrade()' :disabled='isEditAdd?true:false'>
                                            <option  disabled value='-1'><?php echo Yii::t('global','Please Select') ?></option>
                                            <option v-for='(list,key,index) in classData' :value='key'>{{list}}</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label  class="col-sm-2 control-label"><?php echo Yii::t('attends','Course') ?><?php echo Yii::t('global',': ') ?></label>
                                    <div class="col-sm-10">
                                        <select class="form-control" v-model='editCourseId' :disabled='isEditAdd?true:false'>
                                            <option  disabled value='-1'><?php echo Yii::t('global','Please Select') ?></option>
<!--                                            <option v-for='(list,key,index) in selectCourses' :value='key'>{{list}}</option>-->
                                            <option v-for='(list,key,index) in sortSelectCourses' :value='list.id'>{{list.value}}</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label  class="col-sm-2 control-label"><?php echo Yii::t('labels','Room NO.') ?><?php echo Yii::t('global',': ') ?></label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" v-model='classRoom' placeholder='<?php echo Yii::t('attends','Input classroom NO.') ?>'>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-primary" :disabled='disabled' @click='saveCourse()'><?php echo Yii::t("global", 'Save');?></button>
                            <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", 'Close');?></button>
                            <span id="J_fail_info" class="text-warning" v-if='errorText!=""'><i class="glyphicon glyphicon-remove text-warning"></i> {{errorText}}</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal fade" id="delModel" tabindex="-1" role="dialog" aria-labelledby="modal" aria-hidden="false">
                <div class="modal-dialog modal-sm" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                            <h4 class="modal-title" ><?php echo Yii::t('newDS','Delete') ?> <span id="remarks_t"></span></h4>
                        </div>
                        <div class="modal-body">
                            <?php echo Yii::t("newDS", 'Confirm to delete this item?');?>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-primary" :disabled='disabled' @click='romoveCourse()'><?php echo Yii::t("global", 'OK');?></button>
                            <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", 'Cancel');?></button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    var container = new Vue({
        el: "#container",
        data: {
            weekLang:['<?php echo Yii::t('labels','Mon') ?>','<?php echo Yii::t('labels','Tue') ?>','<?php echo Yii::t('labels','Wed') ?>','<?php echo Yii::t('labels','Thu') ?>','<?php echo Yii::t('labels','Fri') ?>'],
            showLoading:false,
            courseData:[],
            category:[],
            timetableTimes:[],
            startYear:'',
            current:'',
            tableData:[],
            courseId:'',
            courses:{},
            childData:{},
            code:'',
            isTab:true,
            editCourseId:'',
            classRoom:'',
            weekId:'',
            times:'',
            disabled:false,
            selectCourses:{},
            sortSelectCourses:{},
            copySelectCourses:{},
            isShow:false,
            delId:'',
            editId:'',
            classData:{
                '.6':'Gr. 6',
                '.7':'Gr. 7',
                '.8':'Gr. 8',
                '.9':'Gr. 9',
                '.10':'Gr. 10',
                '.11':'Gr. 11',
                '.12':'Gr. 12',
                'CLUB.':'CLUB',
                'MEET.':'MEET',
            },
            classList:['.6','.7','.8','.9','.10','.11','.12','CLUB.','MEET.'],
            classAll:true,
            ediGrade:'-1',
            errorText:'',
            teacherA:{},
            roomA:{},
            teacherB:{},
            roomB:{},
            teacherC:{},
            roomC:{},
            teacherD:{},
            roomD:{},
            conflictCourse:false,
            isEditAdd:false,
            meetTeacher:{}
        },
        created(){
          this.getData()
        },
        methods: {
            getData(){
                let that=this
                this.showLoading=true
                $.ajax({
                    url: '<?php echo $this->createUrl("schedulesOverview") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                    },
                    success: function(data) {
                        if(data.state=='success'){  
                            that.courseData=data.data.courseData
                            that.courses=data.data.courses
                            that.startYear=data.data.startyear
                            that.timetableTimes=data.data.timetableTimes
                            that.category=data.data.category
                            that.selectCourses=data.data.selectCourses
                            that.showLoading=false
                            //排序选择的课程
                            that.sortSelectCourses = that.sortSelectCoursesFun(data.data.selectCourses);
                            that.copySelectCourses = that.sortSelectCourses;
                            if(that.isShow){
                                that.showTab(that.current)
                            }
                        }else{
                            that.showLoading=false 
                        }
                    }
                })
            },
            sortSelectCoursesFun(list) {
                var sortSelectCoursesList = [];
                for(let i in list){
                    let codeArr = list[i].split("-");
                    sortSelectCoursesList.push({
                        id:i,
                        code:codeArr[0],
                        value:list[i]
                    })
                }
                sortSelectCoursesList.sort((x,y)=>{
                    return x.code.localeCompare(y.code)
                })
                return sortSelectCoursesList
            },
            rep(text){
                for(var key in this.classData){
                    if(text!== null && text.indexOf(key)!==-1){
                        return key+''
                    }
                }
            },
            conflictClick(){
                this.classList=['.6','.7','.8','.9','.10','.11','.12','CLUB.','MEET.']
                this.conflictCourse=!this.conflictCourse               
            },
            showTab(id){
                this.current=id
                let _this=this
                this.tableData=this.courseData[id]
                var teacher={}
                var room={}
                let list=this.courseData[id]
                if(Object.keys(_this['teacher' + id]).length!=0){
                    return
                }
                console.log(this.tableData)
                for(var i=0;i<list.length;i++){
                    for(var j=0;j<list[i].length;j++){
                        if(list[i][j]!=null){
                            teacher[i+'-'+j]=[]
                            room[i+'-'+j]=[]
                            for(var k=0;k<list[i][j].length;k++){
                                if(list[i][j][k][3]!=null){
                                    teacher[i+'-'+j].push(list[i][j][k][3])
                                }
                                if(list[i][j][k][2]!=null){
                                    room[i+'-'+j].push(list[i][j][k][2])
                                }
                            }
                        }
                    }
                }
                console.log(room)
               const filterUnique = arr => arr.filter(i => arr.indexOf(i) !== arr.lastIndexOf(i))
                for(var key in teacher){
                   let t=filterUnique(teacher[key])
                   teacher[key]=Array.from(new Set(t))
                }
                console.log(teacher)
                for(var key in room){
                    let t=filterUnique(room[key])
                    room[key]=Array.from(new Set(t))
                }
                console.log(room)
                _this['teacher' + id] = teacher
                _this['room' + id] = room
                console.log(1)
            },
            showSame(index,i,item,type){
                if(type=='room'){
                    if(this['room' + this.current][index+'-'+i].indexOf(item)!=-1){
                        return true
                    }
                }else{
                    if(this['teacher' + this.current][index+'-'+i].indexOf(item)!=-1){
                        return true
                    }
                }
            },
            showCourse(id){
                if(id==this.courseId){
                    this.courseId=''
                }else{
                    this.courseId=id
                } 
            },
            showChild(week,time,id){
                this.code=id
                let that=this
                $.ajax({
                    type: "POST",
                    url: "<?php echo $this->createUrl('courseChild')?>",
                    data: {
                        courseCode:id,
                        weekday:week+1,
                        period:time+1,
                    },
                    dataType: "json",
                    success: function(data){
                       if(data.state=='success'){  
                           if(data.data!=null){
                                that.childData=data.data
                                $('#childModel').modal('show');
                           }else{
                                resultTip({
                                    error: 'warning',
                                    msg:'<?php echo Yii::t('attends','No students') ?>'
                                }); 
                           }
                        
                       }
                    }
                });
            },
            showMeetTeacher(id){
                this.code=id
                let that=this
                $.ajax({
                    type: "POST",
                    url: "<?php echo $this->createUrl('meetTeacher')?>",
                    data: {courseCode:id},
                    dataType: "json",
                    success: function(data){
                        if(data.state=='success'){
                            if(data.data!=null){
                                that.meetTeacher=data.data
                                $('#meetTeacherModel').modal('show');
                            }else{
                                resultTip({
                                    error: 'warning',
                                    msg:'<?php echo Yii::t('attends','老师为空') ?>'
                                });
                            }

                        }
                    }
                });
            },
            tabSimple(){
                this.isTab=!this.isTab
            },
            delCourse(data){
                this.delId=data[0]
                $('#delModel').modal('show'); 
            },
            romoveCourse(){
                let that=this
                that.disabled=true
                $.ajax({
                    type: "POST",
                    url: "<?php echo $this->createUrl('removeCourseData')?>",
                    data: {id:this.delId},
                    dataType: "json",
                    success: function(data){
                       if(data.state=='success'){  
                           if(data.data!=null){
                            that.getData()
                                that.isShow=true
                                setTimeout(function () {
                                    $('#delModel').modal('hide');
                                }, 1000)
                                
                                resultTip({
                                    msg:data.state
                                });
                           }else{
                                resultTip({
                                    error: 'warning',
                                    msg:data.message
                                }); 
                           }
                       }
                       that.disabled=false 
                    },
                    error:function(){
                        that.disabled=false
                    }
                });
            },
            addCourse(week,time,data){
                this.weekId=week;
                this.times=time
                if(data){
                    this.editCourseId=data[5]
                    this.classRoom=data[2]
                    this.editId=data[0]
                    this.isEditAdd=true
                }else{
                    this.editCourseId='-1'
                    this.classRoom=''
                    this.ediGrade='-1'
                    this.isEditAdd=false
                }
                this.errorText=''
                $('#editModel').modal('show'); 
            },
            saveCourse(){
                let that=this
                if(this.editCourseId=='-1' ){
                    this.errorText='<?php echo Yii::t('global','Please Select') ?><?php echo Yii::t('attends','Course') ?>'
                    return
                }
                if(this.classRoom==''){
                    this.errorText='<?php echo Yii::t('attends','Input classroom NO.') ?>'
                    return
                }
                if(this.editId!=''){
                    this.editCourseData()
                    return
                }
                this.disabled=true
                $.ajax({
                    type: "POST",
                    url: "<?php echo $this->createUrl('addCourseData')?>",
                    data:{
                        "week":this.weekId+1,
                        "period":this.timetableTimes[this.times].period,
                        "course_id": this.editCourseId,
                        "class_room_name": this.classRoom,
                        "category": this.current
                    },
                    dataType: "json",
                    success: function(data){
                       if(data.state=='success'){  
                           if(data.data!=null){
                                that.getData()
                                that.isShow=true
                                setTimeout(function () {
                                    $('#editModel').modal('hide');
                                }, 1000)
                                
                                resultTip({
                                    msg:data.state
                                });
                           }
                       }else{
                            that.errorText=data.message
                        }
                       that.disabled=false
                    },
                    error:function(){
                        that.disabled=false
                    }
                });
            },
            editCourseData(){
                let that=this
                this.disabled=true
                $.ajax({
                    type: "POST",
                    url: "<?php echo $this->createUrl('saveCourseData')?>",
                    data:{
                        "id": this.editId,
                        "week":this.weekId+1,
                        "period":this.timetableTimes[this.times].period,
                        "course_id": this.editCourseId,
                        "class_room_name": this.classRoom,
                        "category": this.current
                    },
                    dataType: "json",
                    success: function(data){
                       if(data.state=='success'){  
                           if(data.data!=null){
                                that.getData()
                                that.isShow=true
                                setTimeout(function () {
                                    $('#editModel').modal('hide');
                                }, 1000)
                                
                                resultTip({
                                    msg:data.state
                                });
                           }
                       }else{
                            resultTip({
                                error: 'warning',
                                msg:data.message
                            });
                        }
                       that.disabled=false
                    },
                    error:function(){
                        that.disabled=false
                    }
                });
            },
            selectAll(event){
                if(event.target.checked){
                    this.classList=['.6','.7','.8','.9','.10','.11','.12','CLUB.','MEET.']
                }else{
                    this.classList=[]
                }
            },
            select(key){
                if( this.classList.length==Object.keys(this.classData).length){
                    this.classAll=true
                }else{
                    this.classAll=false
                }
            },
            filterGrade(){
                this.sortSelectCourses={}
                this.editCourseId='-1'
                for(var key in this.copySelectCourses){
                    if(this.copySelectCourses[key]['value'].indexOf(this.ediGrade)!=-1){
                        this.sortSelectCourses[key]=this.copySelectCourses[key]
                    }
                }
            }   
        }
    })
</script>



