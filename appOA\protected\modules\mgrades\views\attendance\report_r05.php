
<style type="text/css">
 [v-cloak] {
	display: none;
}
.att-report th {
	background-color: #f5f5f5;
}
.att-report tbody td {
	text-align: center;
}
th div.progress {
	margin-bottom: 0px;
}
.table{
	color:#606266
}
.table tr th,.table tr td {
	vertical-align:middle !important;
	text-align:center;
	color:#606266
}
.pink{
	background:#FFECEE
}
.progress{
	background:#fff;
}
.loading{
	width:98%;
	height:90%;
	background:#fff;
	position: absolute; 
	opacity: 0.5;
	z-index: 99
}
.loading span{
	width:100%;
	height:60%;
	display:block;
	background: url("<?php echo Yii::app()->theme->baseUrl?>/images/loading.gif")no-repeat center center ;
}
.piont{
	width: 6px;
	height: 6px;
	background: #666666;
	border-radius: 50%;
	display:inline-block
}
.pl0{
	padding-left:0px
}
.pr0{
	padding-right:0px
}
</style>
<div id='container'  v-cloak>
	<h3 class='color3 font14'><strong style='font-size:18px' class='mr20'><?php echo Yii::t('attends', 'Student Attendance Profile'); ?></strong></h3>
	<div class="form-inline mt15 mb20"> 
		<select class="form-control input-sm mb5"  v-model="classId">
			<option value="-1" selected  disabled><?php echo Yii::t('global','Please Select') ?></option>
            <option v-for="(list,key,index) in classArr2" :value="list.classid">{{list.name}}</option>
		</select>
	</div>
	<div class="col-md-2 pl0">
		<div class="list-group">
		<a class="list-group-item" href="javascript:void(0);"  v-for='(list,idx) in childArr[classId]' :class='list.childId==studentId?"active":""' @click='showData(list.childId)'>{{list.childName}}</a>
		</div>
	</div>
	<div class="col-md-10 relative pr0" v-if='studentId!=""'>
		<div class='loading'  v-if='showLoading'>
			<span></span>
		</div>
		<table class="table table-bordered att-report " style='table-layout: fixed' >
			<thead>
				<th width='120'><?php echo Yii::t("attends", "Students");?></th>
				<template v-for='(_item,key,i) in item[1]'>
					<th>
						{{numberTypes[key]}}
					</th>
				</template>
			</thead>
			<tbody>
				<tr v-for='(list,index) in timetableTimes'>
					<th>
						<p>{{list.label}}</p>
						<div>{{list.timeslot}}</div>
					</th>
					<template v-for='(_item,keys,i) in item[list.period]'>
						<td >
							<a href="javascript:;" v-if='_item[0]!=0' @click='showStu(keys,list.period)'>{{_item[0]}}</a>
							<span class="label ml5" v-if='_item[0]!=0' :class='_item[1]<80?"label-danger":"label-success"'>{{ _item[1]}}%</span>
						</td>
					</template>
				</tr>
			</tbody>
		</table>
	</div>
	<div class="modal fade" id="childModel" tabindex="-1" role="dialog" aria-labelledby="modal" aria-hidden="false">
		<div class="modal-dialog modal-lg" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
					<h4 class="modal-title" ><?php echo Yii::t('attends','Student List') ?> <span id="remarks_t"></span></h4>
				</div>
				<div class="form-horizontal">
					<div class="modal-body">
						<div class='p10'>
							<ul class="nav nav-pills mb20" role="tablist">
								<li role="presentation"  :class='key==type?"active":""'  v-for='(list,key,index) in childData.countList' @click='showStu(key,period)'><a href="javascript:;">{{numberTypes[key]}} <span class="badge">{{list}}</span></a></li>
							</ul>
							<div class="alert alert-danger mt20"  v-if='childData.studentInfo && childData.studentInfo.length==0' role="alert"><?php echo Yii::t('asa','No data found.') ?></div>
							<div style='max-height:400px;overflow-y:auto' v-else class='scroll-box'>
								<div class='col-md-3' v-for='(list,key,index) in childData.studentInfo' >
									<div class='flex'>
										<div style='width:16px'>
											<span class='piont'></span>
										</div>
										<div class='flex1'>
											<div class='color3 font14 mb5'>{{list.target_date}}</div>
											<div class='color6 font14'>#{{list.period}} <span class='ml5'>({{childData.timetableTimes[list.period].timeslot}})</span> </div>
										</div>
									</div>
								</div>
							</div>
							<div class='clearfix'></div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<script>
	
	
    var classArr  = <?php echo CJSON::encode($data['classArr'] ); ?>;
	var childArr = <?php echo CJSON::encode($data['childArr']); ?>;
    var classArr2  = <?php echo CJSON::encode($data['classArr2'] ); ?>;
    classArr2.sort((a, b) => {
        if (a.child_age < b.child_age) {
            return -1;
        }
        if (a.child_age > b.child_age) {
            return 1;
        }
        // field1 相等时，按 field2 排序
        if (a.name < b.name) {
            return -1;
        }
        if (a.name > b.name) {
            return 1;
        }
        return 0;
    });
	var container = new Vue({
        el: "#container",
        data: {
			classArr:classArr,
			childArr:childArr,
            classArr2:classArr2,
			classId:'-1',
			childInfoList:{},
			item:{},
			showLoading:false,
			childData:{},
			numberTypes:{},
			stuId:'',
			type:'',
			studentId:'',
			timetableTimes:{}
        },
		created(){
		},
        methods: {
			progress(num,total){
				var len=0
				for(var key in total){
					len+=total[key].total
				}
				var progress=len <= 0? "0" : Math.round((num / len) * 10000) / 100.0;
				return progress
			},
			showData(id){
				this.showLoading=true
				this.studentId=id
				let that=this
				$.ajax({
                    url: '<?php echo $this->createUrl("studentAttendCount") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
						childid:this.studentId,
						classId:this.classId
                    },
                    success: function(data) {
                        if(data.state=='success'){  
							that.timetableTimes=data.data.timetableTimes
							that.item=data.data.item
							that.numberTypes=data.data.numberTypes
                            that.showLoading=false
                        }else{
                            that.showLoading=false 
                        }
                    }
                })
			},
			showStu(type,period){
				let that=this
				this.period=period
				this.type=type
				$.ajax({
                    url: '<?php echo $this->createUrl("showStudentAttend") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
						targetDate:this.targetDate,
						childid:this.studentId,
						classId:this.classId,
						type:type,
						period:period
                    },
                    success: function(data) {
                        if(data.state=='success'){  
							that.childData=data.data
                        	$('#childModel').modal('show');
                        }else{
                        }
                    }
                })
			}
		}
    })
</script>
