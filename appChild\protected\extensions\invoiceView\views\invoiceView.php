<?php 
//学费状态
$cfgFee = Mims::LoadConfig('CfgFee');
$CfgFeeType = Mims::LoadConfig('CfgFeeType');
$HtoolKits = Mims::LoadHelper('HtoolKits');

?>
<style>
.invoice-view{
	padding: 20px;
}
.gtab td{
	border-bottom: 1px solid #f2f2f2;	
}
.basic th{
	text-align: right;
	padding-left: 20px;
}
.pop-box h2{
	border-bottom: 2px solid #666;
}
</style>
<div class="invoice-view pop-box">
	<h2><?php echo Yii::t("payment","Invoice Information");?></h2>
	<table class="gtab basic">
		<tr>
			<th width="30%"><?php echo Yii::t("labels","Child Name");?></th>
			<td><?php echo CHtml::encode($childName);?></td>
		</tr>
		<tr>
			<th><?php echo Yii::t("labels","Class");?></th>
			<td><?php echo CHtml::encode($className)?></td>
		</tr>
		<tr>
			<th><?php echo Yii::t("labels","Campus");?></th>
			<td><?php echo CHtml::encode($schoolName)?></td>
		</tr>
		<tr>
			<th><?php echo Yii::t("payment","Title");?></th>
			<td><?php echo CHtml::encode($invoModel->title);?></td>
		</tr>
		<tr>
			<th><?php echo Yii::t("payment","Amount");?></th>
			<td><?php echo number_format($invoModel->original_amount,2);?></td>
		</tr>
		<?php if ($depositAmount):?>
		<tr>
			<th><?php echo Yii::t("payment","Deposit Amount");?></th>
			<td><?php echo number_format($depositAmount,2);?></td>
		</tr>
		<?php endif; ?>
		<tr>
			<th><?php echo Yii::t("payment","Amount Due");?></th>
			<td><?php echo number_format($invoModel->amount,2);?></td>
		</tr>
		<?php if ($discountName):?>
		<tr>
			<th><?php echo Yii::t("payment","Discount");?></th>
			<td><?php echo CHtml::encode($discountName);?></td>
		</tr>
		<?php endif; ?>
		<tr>
			<th><?php echo Yii::t("payment","Invoice Status");?></th>
			<td>
			<?php echo $HtoolKits->getContentByLang($cfgFee['invoice_state'][$invoModel->status]['cn'],$cfgFee['invoice_state'][$invoModel->status]['en'])?>
			</td>
		</tr>
		<tr>
			<th><?php echo Yii::t("payment","Invoice Type");?></th>
			<td><?php echo $HtoolKits->getContentByLang($CfgFeeType[$invoModel->payment_type]['cn'],$CfgFeeType[$invoModel->payment_type]['en']);?></td>
		</tr>
		<tr>
			<th><?php echo Yii::t("payment","Duration");?></th>
			<td>
			<?php 
				if ($lunchStr)
				{
					echo $lunchStr;
				}
				elseif ($schoolYear)
				{
					echo $schoolYear;
				}
				else
				{
					if ($invoModel->startdate && $invoModel->enddate)
					{
						echo $invoModel->getDSInterval();
					}
				}
			?></td>
		</tr>
		<?php if ($invoModel->duetime):?>
		<tr>
			<th><?php echo Yii::t("payment","Due Date");?></th>
			<td><?php echo date('Y-m-d',$invoModel->duetime);?>
			</td>
		</tr>
		<?php endif; ?>
		<?php if ($invoModel->gen_latefees_date):?>
		<tr>
			<th><?php echo Yii::t("payment","Late fee Date");?></th>
			<td><?php echo date('Y-m-d',$invoModel->gen_latefees_date);?></td>
		</tr>
		<?php endif; ?>
		<tr>
			<th><?php echo Yii::t("labels","Operator");?></th>
			<td><?php echo CHtml::encode($userName);?></td>
		</tr>
		<tr>
			<th><?php echo Yii::t("payment","Date of Invoice");?></th>
			<td><?php echo date('Y-m-d H:s',$invoModel->timestamp);?></td>
		</tr>
		<?php if($childServiceInfo):?>
		<tr>
			<th><?php echo Yii::t("payment","Service Information");?></th>
			<td>
				<?php
					$cycle = array("mon","tue","wed","thu","fri");
					foreach($cycle as $_k=>$_d){
						$src = Yii::app()->theme->baseUrl . "/images/icons/" . $invoModel->payment_type . "_" . $childServiceInfo['mon'] .".gif";
						$title = Yii::app()->locale->getWeekDayName($_k+1);
						echo CHtml::image($src, $title, array("title"=>$title) );
					}
				?>
			</td>
		</tr>
		<?php endif;?>
	</table>
	
	
	
	<?php if($tranModel):?>
	<!--	显示付款记录-->
	<h2><?php echo Yii::t("payment","Payment Information");?></h2>
	<table class="gtab">
		<tr>
			<th><?php echo Yii::t("payment","Fapiao No.");?></th>
			<th><?php echo Yii::t("payment","Amount");?></th>
			<th><?php echo Yii::t("payment","Payment Type");?></th>
			<th><?php echo Yii::t("labels","Paid Date");?></th>
		</tr>
		<?php foreach($tranModel as $trans):?>
		<tr>
			<td><?php echo $trans->invoice_number;?></td>
			<td><?php echo $trans->amount;?></td>
			<td>
				<?php echo $HtoolKits->getContentByLang($cfgFee['transaction_type'][$trans->transactiontype]['cn'],$cfgFee['transaction_type'][$trans->transactiontype]['en'])?>
			</td>
			<td><?php echo Mims::formatDateTime($trans->timestampe, "medium", "short");?></td>
		</tr>
		<?php endforeach;?>
	</table>
	<?php endif;?>	

	<?php if($latepayRate):?>
	<!-- 如果是滞纳金账单，则显示原账单信息	-->
	<h2><?php echo Yii::t("payment","Original Invoice Information");?></h2>
	<table class="gtab">
		 <tr>
			<th width="20%"><?php echo Yii::t("payment","Invoice");?></th>
			<th width="20%"><?php echo Yii::t("payment","Invoice Type");?></th>
			<th width="20%"><?php echo Yii::t("payment","Invoice Amount");?></th>
			<th width="20%"><?php echo Yii::t("payment","Date of Invoice");?></th>
			<th width="20%"><?php echo Yii::t("payment","Late Payment Rate");?></th>
		 </tr>
		<tr>
		   <td><?php echo $previousInvoice->title;?></td>
		   <td><?php echo $HtoolKits->getContentByLang($CfgFeeType[$previousInvoice->payment_type]['cn'],$CfgFeeType[$previousInvoice->payment_type]['en']);?></td>
		   <td><?php echo $previousInvoice->amount;?></td>
		   <td><?php echo Mims::formatDateTime($previousInvoice->timestamp);?></td>
		   <td><?php echo $latepayRate;?>%</td>
		</tr>
	</table>
	<?php endif;?>	
</div>



