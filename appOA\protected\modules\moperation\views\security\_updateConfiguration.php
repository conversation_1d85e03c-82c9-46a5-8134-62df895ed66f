<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site','HQ Operations'), array('/moperation'))?></li>
        <li><?php echo CHtml::link(Yii::t('site','Basic Configurations'), array('/moperation'))?></li>
        <li class="active"><?php echo Yii::t('site','安全报告模板') ?></li>
    </ol>

    <div class="row">
        <div class="col-md-2 col-sm-2 mb10">
            <?php
            $this->widget('zii.widgets.CMenu', array(
                'items' => $this->module->getHomeMenu(),
                'id' => 'pageCategory',
                'htmlOptions' => array('class' => 'nav nav-pills nav-stacked background-gray'),
                'activeCssClass' => 'active',
            ));
            ?>
        </div>
        <div class="col-md-10">
            <div class="row">
                <div class="col-md-12 mb10">
                    <a href="<?php echo $this->createUrl('addSafetySelf');?>" class="J_modal btn btn-primary"><?php echo Yii::t('report', '增加检查项目');?></a>
                </div>
            </div>
            <div class="row">
                <?php
                $this->widget('ext.ivyCGridView.BsCGridView', array(
                    'id'=>'security_type',
                    'dataProvider'=>$dataProvider,
                    'colgroups'=>array(
                        array(
                            "colwidth" => array(null, 150, 100, 150),
                        )
                    ),
                    'columns'=>array(
                        array(
                            'header'=>Yii::t('labels', '检查项目'),
                            'value'=>'$data->getName()'
                        ),
                        array(
                            'name' => Yii::t('global', '检查类别'),
                            'type' => 'raw',
                            'value' => array($this, 'getType'),
                        ),
                        array(
                            'name' => Yii::t('global', '状态'),
                            'type' => 'raw',
                            'value' => array($this, 'getStatus'),
                        ),
                        array(
                            'name' => Yii::t('global', 'Action'),
                            'type' => 'raw',
                            'value' => array($this, 'getButton'),
                        ),
                    ),
                )); ?>
            </div>
        </div>
    </div>
</div>
<div class="modal fade" id="categoryEditModal" tabindex="-1" role="dialog" aria-labelledby="categoryEditModalLabel">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title"><?php echo Yii::t('report', 'Categories Edit'); ?>
                    <small></small>
                </h4>
            </div>
            <form class="form-horizontal J_ajaxForm"
                  action="<?php echo $this->createUrl('saveCategory'); ?>"
                  method="POST">
                <div class="modal-body">
                    <div id="body-data">

                        <div id="_myApp">

                        </div>

                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <button type="button" class="btn btn-primary btn-xs" onclick="addRoot()"><?php echo Yii::t
                                ('report', 'Add Root Category');
                                ?></button>
                        </div>
                    </div>
                    <input type="hidden" name="tid" value="" id="tid">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default pull-right"
                            data-dismiss="modal"><?php echo Yii::t('global', 'Cancel'); ?></button>
                    <button type="submit"
                            class="btn btn-primary J_ajax_submit_btn pull-right mr10"><?php echo Yii::t('global', 'Submit'); ?></button>
                </div>
            </form>
        </div>
    </div>
</div>
<script type="text/template" id="tpl">
    <div class="item" v-for="item in items">
        <div class="row mb10">
            <input v-if="!item.isnew" type="hidden" name="old[root][]" value="{{item.id}}">
            <input  v-else type="hidden" name="new[root][]" value="{{item.id}}">
            <input type="hidden" name="sub_weight[{{item.id}}]" value="{{$index + 1}}">

            <div class="col-md-1"></div>
            <div class="col-md-4">
                <input type="text" class="form-control" name="program[{{item.id}}][cn]" value="{{item.ctitle}}" placeholder="中文标题">
            </div>
            <div class="col-md-1"></div>
            <div class="col-md-4">
                <input type="text" class="form-control" name="program[{{item.id}}][en]" value="{{item.etitle}}" placeholder="EnglishTitle">
            </div>

            <div class="col-md-2">
                <a role="button" class="btn btn-default btn-sm sort-header"
                   style="cursor: move;"><span class="glyphicon glyphicon-move"></span></a>
                <button type="button" class="btn btn-danger btn-sm V_remove" index="{{item.index}}" lid="{{item.id}}">
                    <span class="glyphicon glyphicon-minus"></span></button>
            </div>
        </div>
        <hr>
    </div>
</script>
<script>
    // 模态框
    var modal = '<div class="modal fade" id="modal" tabindex="-1" role="dialog" aria-labelledby="modal"><div class="modal-dialog" role="document"><div class="modal-content"></div></div></div>';
    $('body').append(modal);

    var MyComponent = Vue.extend({
        template: '#tpl'
    });

    var VItems = new MyComponent({  // 创建一个 user 实例
        el: '#_myApp',  // 挂载到元素上
        data: {
            items: []
        }, methods: {
            getData: function (tid) {
                $.ajax({
                    type: 'post',
                    url: '<?php echo $this->createUrl('getCateGory')?>',
                    data: {id: tid},
                    dataType: 'json',
                    success:function(data){
                        VItems.items = data;
                    }
                })
            }
        }
    });

    var vLen = VItems.items.length;
    var c = 10000;
    addRoot = function () { //添加新课程
        var vLength = VItems.items.length;
        VItems.items.$set(vLength, {
            cplaceholder: '标题中文',
            eplaceholder: '标题英文',
            isnew:true,
            id: c++
        });
    };

    $(document).on('click', '.V_remove', function () {  //删除方法
        var delid = $(this).attr("lid");
        for (var i = VItems.items.length - 1; i >= 0; i--) {
            if (VItems.items[i].id !== undefined) {
                if (VItems.items[i].id == delid){
                    if(!VItems.items[i].isnew){
                        delOption(delid);
                        VItems.items.splice(i, 1);
                    }else {
                        VItems.items.splice(i, 1);
                    }
                }
            }
        }
    });



    editCategory = function (tid) {
        VItems.getData(tid);
        $('#tid').val(tid);
        $('#categoryEditModal').modal({backdrop: 'static', keyboard: false});
        $("#body-data > #_myApp").empty();
    };
    $("#categoryEditModal #body-data").sortable({
        handle: ".sort-header",
        placeholder: "ui-state-highlight",
        axis: 'y',
        opacity: 0.8,
        start: function (event, ui) {
            $('.ui-state-highlight').height($(ui['item'][0]).height());
        },
        update: function (event, ui) {
            var cc = 1;
            $("#categoryEditModal #body-data .item").each(function () {
                $(this).find('input[name^="sub_weight"]').val(cc++);
            });
        }
    });

    delOption = function(id){
        var ret=false;
        $.ajax({
            type: 'post',
            url: '<?php echo $this->createUrl('delCateGory')?>',
            data: {id: id},
            async: false,
            dataType: 'json'
        }).done(function(data){
            var error = data.state == 'success' ? 0 : 1;
            resultTip({msg: data.message, error: error});
            if(data.state == 'success'){
                ret = true;
            }
        });
        return ret;
    }

</script>