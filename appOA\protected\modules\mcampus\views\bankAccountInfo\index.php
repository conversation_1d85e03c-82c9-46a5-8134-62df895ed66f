<style>
    [v-cloak] {
		display: none;
	}
    .imgCover{
        width:100px;
        height:100px;
        object-fit:cover;
        border-radius:50%;
        border:1px solid #428bca
    }
    .color4D88D2{
        color:#4D88D2
    }
    .border {
		border: 1px solid #ccc;
		border-radius: 5px
	}
	.ptintImg {
		height: 140px;
        border-radius: 4px;
	}
	.datalist{
		float: left;
		width: 50%;
		margin-top:10px;
	}
	.datalist div{
		font-size:12px
	}
	.list{
		width:140px;
		margin:0 auto;
	}
	.borderNone {
		border: 1px solid #ccc;
        height: 440px
	}
	.borderRight {
		border-right: 1px solid #ccc;
	}
	.padd0 {
		padding: 0 !important
	}
	.h100 {
		height: 100%
	}
	.chilidPto {
		max-height: 40%;
        border-radius: 4px;
	}
	.pt10 {
		padding: 10px 0 20px 10px;
	}
    .pl10{
        padding-left:10px;
    }
	.col-md-6 {
		float: left;
		width: 50%;
	}
	.relative{
		position: relative;
	}
	.absolute{
		position: absolute;
        bottom: 15px;
        left: 15px;
        font-size: 16px;
        color: #fff;
	}
	.pt30{
		padding-top:30px
	}

	.fontSize{
		font-size:18px
	}
	.keepAll{
		word-wrap:break-word;
		word-break:keep-all;
	}
	.flexW{
		width:39px
	}
	.imgCss{
		width:150px
	}
    .green{
        color:#5DB85B
    }
    .red{
        color:#D5514E
    }
    .yellow{
        color:#F1AD4E
    }
    .selectWidth{
        width:70px
    }
    .classActive{
        border-left:6px solid #428BCA;
        color:#4D88D2;
        padding-left:10px
    }
    .floatNone{
        /* float:none !important; */
        background:#F1AD4E;
        margin-left:5px
    }
    .loading{
        width:98%;
        height:100%;
        background:#fff;
        position: absolute; 
        opacity: 0.5;
        z-index: 99
    }
    .loading span{
        width:100%;
        height:100%;
        display:block;
        background: url("<?php echo Yii::app()->theme->baseUrl?>/images/loading.gif")no-repeat center center ;
    }
</style>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('//mcampus/default/index')) ?></li>
        <li><?php echo CHtml::link(Yii::t('site', '银行账户信息'), array('//mcampus/bankAccountInfo/index')) ?></li>
    </ol>

    <div class="row">
        <!-- 左侧菜单 -->
        <div class="col-md-2 col-sm-12">
            <div class="list-group" id="classroom-status-list">
                <a href="<?php echo $this->createUrl('pickupCard/index'); ?>" class="list-group-item status-filter"><?php echo Yii::t("newDS", "接送卡");?></a>
                <a href="<?php echo $this->createUrl('homeAddress/index'); ?>" class="list-group-item status-filter"><?php echo Yii::t("newDS", "家庭居住地址");?></a>
                <a href="<?php echo $this->createUrl('bankAccountInfo/index'); ?>" class="list-group-item status-filter active"><?php echo Yii::t("newDS", "银行账户信息");?></a>
            </div>
            <div class="text-center" v-if='qrcodeImg!=""'>
                <h5><?php echo Yii::t("newDS", "家长二维码入口");?></h5>
                <p><img class='qrcodeImg' src="http://m2.files.ivykids.cn/cloud01-file-8025768Fi9Deo9iwS9YJMAknp4uFyc8MbAr.png" alt=""></p>
            </div>
        </div>
        <div class="col-md-10 col-sm-10" id='container' v-cloak>
            <div class="panel panel-default">
                <div class="panel-heading">当前状态</div>
                <div class="panel-body" v-if='Object.keys(initData).length != 0'>
                    <div v-if='initData.start!=""'>
                        <p class='font14 mb20'>
                            <strong>{{initData.start_year}}-{{initData.start_year+1}}学年</strong>
                        </p>
                        <p>
                            <span class='mr20'>开放时间：{{initData.start}}-{{initData.end}}</span>
                        </p>
                    </div>
                    <div v-else   class='font14 mb20'>
                        <strong>未设置</strong>
                    </div>
                    <p>
                        <button type="button" class="btn btn-primary mt10"  @click='editInfo()'>
                            <span class="glyphicon glyphicon-pencil" aria-hidden="true"></span> 设置
                        </button>
                    </p>
                </div>
            </div>
            <div class="panel panel-default" v-if='Object.keys(initData).length != 0 && initData.classList.length!=0'>
                <div class="panel-heading">银行信息</div>
                    <div class="panel-body">
                    <div class='col-sm-2 scroll-box' style='max-height:500px;overflow-y:auto'>
                        <ul class="list-group " >
                            <li class="list-group-item" :class='list.class_id==activeId?"classActive":""' v-for='(list,index) in initData.classList'  @click='tabList(list)'>
                                {{list.title}} <span class="badge floatNone" v-if='list.newSubmitNum>0' onMouseOver="$(this).tooltip('show')"  data-toggle="tooltip" data-html='true' title='<?php echo Yii::t("newDS", "待审核");?>'  data-placement="left">{{list.newSubmitNum}}</span>
                                <div class="progress pull-left mt10 mb5" style='height:8px;width:80%' v-if='!classTotal(list)'>
                                    <div class="progress-bar progress-bar-success" :style="progress(list,'approvedNum')">
                                    </div>
                                    <div class="progress-bar progress-bar-warning progress-bar-striped" :style="progress(list,'newSubmitNum')">
                                    </div>
                                    <div class="progress-bar progress-bar-danger" :style="progress(list,'rejectedNum')">
                                    </div>
                                </div>
                                <div class="progress pull-left mt5 mb5" style='width:80%' v-else>
                                    <span class="label label-primary"  style='width:100%;display:inline-block' >
                                        <span class="glyphicon glyphicon-ok" aria-hidden="true"></span>
                                            完成
                                    </span>
                                    </div>
                                <div class="pull-right mt5">{{list.submitNum}}/{{list.total}}</div>
                                <div class='clearfix'></div>
                            </li>
                        </ul>
                    </div>
                    <div class='col-sm-10'  v-if='tableData.length != 0'>
                        <div>
                            <div class="btn-group">
                                <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                    批量操作 <span class="caret"></span>
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a href="javascript:;" @click='bathAudit' >批量标记已审核</a></li>
                                </ul>
                            </div>
                            <span class='text-success ml10'>已选择{{printIds.length}}名学生</span>
                            <button  class="btn btn-primary pull-right" @click='wechatModel'> 发送微信提醒</button>
                            <button  class="btn btn-default pull-right mr10" @click='exportInfo'> 导出</button>
                        </div>
                        <table class="table mt10">
                            <thead>
                                <tr>
                                    <th width='50'><input type="checkbox" id="inlineCheckbox1" v-model="printAll" @change='printAllData()'></th>
                                    <th width='150'>学生</th>
                                    <th width='100'>审核状态</th>
                                    <th>帐户名</th>
                                    <th>银行名称含支行</th>
                                    <th>银行所在城市</th>
                                    <th>帐户号码</th>
                                    <th width='100'>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for='(list,index) in tableData.items'>
                                    <th scope="row"><input type="checkbox" id="inlineCheckbox1" v-model='printIds' :disabled='list.audit_status==1?true:false'  @change='printData()' :value="list.id"></th>
                                    <td> <a href='javascript:void(0);' @click='stuInfo(list.child_id)'>{{childInfo[list.child_id]}}</a></td>
                                    <td>
                                        <i class='glyphicon glyphicon-remove-sign' v-if='list.audit_status==2' style='color:#D5514E'></i>
                                        <i class='glyphicon glyphicon-ok-sign'  v-if='list.audit_status==1' style='color:#5cb85c'></i>
                                        <i class='glyphicon glyphicon-registration-mark'  v-if='list.audit_status==3' style='color:#f0ad4e'></i>
                                        <i class='glyphicon glyphicon-question-sign'  v-if='list.audit_status==0' style='color:#f0ad4e'></i>
                                        <span>{{statusList[list.audit_status]}}</span> 
                                    </td>
                                    <td>
                                        <div>{{list.bank_user}}</div>
                                    </td>
                                    <td>
                                        <div>{{list.bank_name}}</div>
                                    </td>
                                    <td>
                                        <div>{{list.bank_city}}</div>
                                    </td>
                                    <td>
                                        <div>{{list.bank_account}}</div>
                                    </td>
                                    <td>
                                        <a href="javascript:;" @click='editData(list)'>查看详情</a>
                                    </td>
                                </tr>
                                <tr v-for='(list,index) in tableData.unSubmitChild'>
                                    <th scope="row"><input type="checkbox" id="inlineCheckbox1" disabled></th>
                                    <td> <a href='javascript:void(0);' @click='stuInfo(list)'>{{childInfo[list]}}</a></td>
                                    <td>-</td>
                                    <td>-</td>
                                    <td>-</td>
                                    <td>-</td>
                                    <td>-</td>
                                    <td>-</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="modal fade" tabindex="-1" role="dialog" id='infoModal'>
                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                            <h4 class="modal-title">银行信息管理</h4>
                        </div>
                        <div class="modal-body">
                            <p class='font14 mb20'>
                                <strong>银行信息文字说明（显示在家长端）</strong>
                            </p>
                            <div class="form-group mb5">
                                <label for="inputEmail3" class="col-sm-2 control-label">中文</label>
                                <div class="col-sm-10 mb15">
                                <textarea class="form-control" v-model='tips_text_cn' rows="10"></textarea>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="inputPassword3" class="col-sm-2 control-label">英文</label>
                                <div class="col-sm-10 mb15">
                                <textarea class="form-control" v-model='tips_text_en' rows="10"></textarea>
                                </div>
                            </div>
                            <p class='font14 mb20'>
                                <strong>开放填写时间段</strong>
                            </p>
                            <div class="form-group">
                                <label for="inputEmail3" class="col-sm-2 control-label"><?php echo Yii::t("newDS", "开始时间");?></label>
                                <div class="col-sm-10 mb15">
                                    <input type="text" class="form-control  select_3 pull-left" id='startDate'  v-model='startDate'  placeholder="<?php echo Yii::t("newDS", "Select a date");?>"   :value='startDate'>
                                    <select class="form-control selectWidth ml20 pull-left" v-model='startHours'>
                                        <option v-for='(list,index) in 25'>{{index < 10?'0'+index:index}}</option>
                                    </select>
                                    <select class="form-control ml5 selectWidth pull-left"   v-model='startMinutes'>
                                        <option v-for='(list,index) in 61' >{{index < 10?'0'+index:index}}</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="inputEmail3" class="col-sm-2 control-label"><?php echo Yii::t("newDS", "结束时间");?></label>
                                <div class="col-sm-10 mb15">
                                    <input type="text" class="form-control  select_3 pull-left"  id='endDate'  v-model='endDate' placeholder="<?php echo Yii::t("newDS", "Select a date");?>"   :value='endDate'>
                                    <select class="form-control selectWidth ml20 pull-left" v-model='endHours'>
                                        <option v-for='(list,index) in 25'>{{index < 10?'0'+index:index}}</option>
                                    </select>
                                    <select class="form-control ml5 selectWidth pull-left"   v-model='endMinutes'>
                                        <option v-for='(list,index) in 61' >{{index < 10?'0'+index:index}}</option>
                                    </select>
                                </div>
                            </div>
                            <div class='clearfix'></div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
                            <button type="button" class="btn btn-primary" @click='saveInfo()'>保存</button>
                        </div>
                    </div><!-- /.modal-content -->
                </div><!-- /.modal-dialog -->
            </div><!-- /.modal -->
            <div class="modal fade" tabindex="-1" role="dialog" id="editModal">
                <div class="modal-dialog modal-lg" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            详细信息
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                        </div>
                        <div class="modal-body">
                        <p>姓名：{{childDetail.child_name_cn}}</p>
                        <p>帐户名：{{childDetail.bank_user}}</p>
                        <p>银行名称含支行：{{childDetail.bank_name}}</p>
                        <p>银行所在城市：{{childDetail.bank_city}}</p>
                        <p>帐户号码：{{childDetail.bank_account}}</p>
                        <hr>
                            <div class='col-md-12 col-sm-12 font14'>
                                <div class='col-md-6 col-sm-6 borderRight scroll-box' style='max-height:250px;overflow-y:auto'>
                                    <p><strong>当前审核状态</strong></p>
                                    
                                    <div v-if='Object.keys(childDetail).length != 0'>
                                       {{childDetail.logStatusList[childDetail.audit_status]}}
                                        <button type="button" class="btn btn-primary  btn-xs pull-right" @click='resetStatus("show")'  v-if='childDetail.audit_status==1 || childDetail.audit_status==2'>重置</button>
                                    </div>
                                    <p class='mt10'><strong>审核记录</strong></p>
                                    <div>
                                        <div v-for='(list,index) in childDetail.audit_comments' class='ml10' style='border-left:1px dashed #ccc'>
                                        <p style='margin-left:-7px;background: #fff;' >
                                            <span v-if='list.status==1'>
                                            <span class='glyphicon glyphicon-ok-sign green'></span><span> 已通过</span> 
                                            </span>
                                            <span v-if='list.status==2'>
                                            <span class='glyphicon glyphicon-info-sign red'></span> <span> 驳回</span>
                                            </span>
                                            <span v-if='list.status==0'>
                                            <span class='glyphicon glyphicon-question-sign yellow'></span> <span> 重置</span>
                                            </span>
                                            </p> 
                                        <p class='ml10' style='background:#F7F7F8;line-height:22px' v-if='list.status==2'>原因：{{list.comment}}</p>
                                        <p class='pl10 font12'>{{list.date}} {{list.user}}</p>
                                        </div>
                                    </div>
                                </div>
                                <div class='col-md-6 col-sm-6' v-if='childDetail.audit_status==0 || childDetail.audit_status==3'>
                                    <p><strong>审核</strong></p>
                                    <p>
                                        <label class="radio-inline">
                                            <input type="radio" id="inlineradio1" v-model='auditStatus' value="1"> 通过
                                        </label>
                                        <label class="radio-inline">
                                            <input type="radio" id="inlineradio2" v-model='auditStatus' value="2"> 未通过
                                        </label>
                                    </p>
                                    <div v-if='auditStatus==2'>
                                        <textarea class="form-control" rows="3" placeholder='请输入未通过原因' v-model='comment'></textarea>
                                        <p class='mt10'>修改建议将直接显示在家长端，请准确措辞。</p>   
                                        <div class="checkbox">
                                            <label>
                                                <input type="checkbox"  v-model='send_wechat'>将审核结果发送至家长微信
                                            </label>
                                        </div>
                                    </div>
                                    <div>
                                        <button type="button"  class="btn btn-primary pull-right" :disabled='btnCon' @click='saveAudit()'><?php echo Yii::t("newDS", "确认");?></button>
                                    </div>
                                </div>
                            </div>
                            <div class='clearfix'></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal fade" tabindex="-1" role="dialog" id="wechatModal">
                <div class="modal-dialog modal-lg" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                            发送微信提醒 <span class='ml20'>24小时最多提醒一次</span>
                        </div>
                        <div class="modal-body">
                            <div class='col-md-12 col-sm-12 font14'>
                                <div class='col-md-6 col-sm-6 borderRight scroll-box' style='max-height:600px;min-height:400px;overflow-y:auto'>
                                    <div class='loading'  v-if='loading'>
                                        <span></span>
                                    </div>
                                    <p><strong>选择学生</strong></p>
                                    <div class='color6 mb15'>已过滤 【通过/待审核/待再次审核】状态的学生</div>
                                    <div class="checkbox">
                                        <label>
                                            <input type="checkbox"  v-model="wechatAll" @change='wechatAllData()' >全选
                                        </label>
                                    </div>
                                    <div class='ml20'>
                                        <div class="checkbox"  v-for='(key,index) in dataListSend'>
                                            <label v-if='sendData[key]' >
                                                <span  v-if='sendData[key].can_send==0'>
                                                    <input type="checkbox" disabled='true'>{{key}} - {{childInfo[key]}}<span class='ml20 font12 red'>上次提醒：{{sendData[key].latest_date}}</span>
                                                </span>
                                               <span v-else>
                                                    <input type="checkbox"  :value="key" v-model='wechatList'  @change='wechatData()'>{{key}} - {{childInfo[key]}}<span class='ml20 font12 color6'>上次提醒：{{sendData[key].latest_date}}</span>
                                               </span>
                                            </label>
                                            <label v-else >
                                                <input type="checkbox"  :value="key" v-model='wechatList'  @change='wechatData()'>{{key}} - {{childInfo[key]}}
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div class='col-md-6 col-sm-6'>
                                    <p><strong>微信消息示例（截图）</strong></p>
                                    <div>
                                        <img src="http://m2.files.ivykids.cn/cloud01-file-8025768Fr2FY_rWhOrkEqkN3izglDfpk843.png" style='width:100%' alt="">
                                    </div>
                                </div>
                            </div>
                            <div class='clearfix'></div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("newDS", "取消");?></button>
                            <button type="button" class="btn btn-primary" v-if='!wechatBtn'  @click='sendWechat()'><?php echo Yii::t("newDS", "发送微信通知");?></button>
                            <button type="button" class="btn btn-primary" v-else disabled @click='sendWechat()'><?php echo Yii::t("newDS", "发送中，请勿关闭页面");?></button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal fade" tabindex="-1" role="dialog" id='childInfo'>
                <div class="modal-dialog modal-lg" role="document">
                    <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title"><?php echo Yii::t("site", "Student Information");?></h4>
                    </div>
                    <div class="modal-body">
                        <div class="col-md-6 borderRight text-center marginAuto mb20">
                            <img :src='stuData.photo' class="imgCover" alt="">
                            <p class='font14 fontBold mt10'>{{stuData.name}} | {{childId}}</p>
                        </div>
                        <div class="col-md-6 mb20"  > 
                            <p><strong><?php echo Yii::t("newDS", "Wechat message receivers");?></strong>  </p>
                            <div class=' scroll-box' style='max-height:130px;overflow-y:auto' v-if='stuData.wxReceivers && stuData.wxReceivers.length!=0'>
                                <div class='flex mt10'  v-for='(list,index) in stuData.wxReceivers'>
                                    <div style='width:50px'>
                                        <img :src="list.headimgurl" style='width:40px;height:40px' class='media-object img-circle image' alt="">
                                    </div>
                                    <div class='flex1'>
                                        <p><span class="label label-info" v-if='list.isDev'>IT DEV</span> <strong> {{list.nickname}}</strong></p>
                                        <div><?php echo Yii::t("newDS", "Recent received:");?> {{list.recent}}</div>
                                    </div>
                                </div>
                            </div>
                            <div v-else class="alert alert-warning" role="alert"><?php echo Yii::t("newDS", "No messsage sent successfully in recent 60 days.");?></div>
                        </div>
                        <div  class="col-md-12 mb20 mt20">
                            <div  class="col-md-3">
                                <p class="font12 color6"> <?php echo Yii::t("labels", "Legal First Name");?></p>
                                <p class="font14 color3">{{stuData.first_name_en}}</p>
                            </div>
                            <div  class="col-md-3">
                                <p class="font12 color6"><?php echo Yii::t("labels", "Legal Middle Name");?></p>
                                <p class="font14 color3">{{stuData.middle_name_en}}</p>
                            </div>
                            <div  class="col-md-3">
                                <p class="font12 color6"><?php echo Yii::t("labels", "Legal Last Name");?></p>
                                <p class="font14 color3">{{stuData.last_name_en}}</p>
                            </div>
                            <div  class="col-md-3">
                                <p class='font12 color6'><?php echo Yii::t("labels", "Preferred Name");?></p>
                                <p class='font14 color3'>{{stuData.nick}}</p>
                            </div>
                        </div>
                        <div  class="col-md-12 ">
                            <div  class="col-md-3">
                                <p class="font12 color6"><?php echo Yii::t("labels", "Nationality");?></p>
                                <p class="font14 color">{{stuData.country}}</p>
                            </div>
                            <div  class="col-md-3">
                                <p class="font12 color6"><?php echo Yii::t("labels", "Date of Birth");?></p> 
                                <p class="font14 color">{{stuData.birthday_search}}</p>
                            </div>
                            <div  class="col-md-3">
                                <p class="font12 color6"><?php echo Yii::t("labels", "Gender");?></p> 
                                <p class="font14 color">{{stuData.gender==1?"男":"女"}}</p>
                            </div>
                        </div>
                        <div class='clearfix'></div>
                        <hr>
                        <div  class="col-md-12">
                        <div  class="col-md-3">
                                <p class="font12 color6"><?php echo Yii::t("labels", "Bus NO.");?></p>
                                <p class="font14 color">{{stuData.busId}}</p>
                            </div>
                            <div  class="col-md-9">
                                <p class="font12 color6"><?php echo Yii::t("labels", "Bus Notes");?></p>
                                <p class="font14 color">{{stuData.BusContent}}</p>
                            </div>
                        </div>
                        <div class='clearfix'></div>
                        <hr>
                        <div  class="col-md-12 mb20">
                            <div  class="col-md-3">
                                <p class="font12 color6"><?php echo Yii::t("labels", "Father");?></p>                           
                                <p class="font14 color">{{stuData.fName}}</p>
                            </div>
                            <div  class="col-md-3">
                                <p class="font12 color6"><?php echo Yii::t("labels", "Mobile Phone");?></p>
                            <p class="font14 color">{{stuData.fTel}}</p>
                        </div>
                            <div  class="col-md-6">
                                <p class="font12 color6"><?php echo Yii::t("child", "Email");?></p>
                                <p class="font14 color">{{stuData.fEmail}}</p>
                            </div>
                        </div>
                        <div  class="col-md-12 ">
                            <div  class="col-md-3">
                                <p class="font12 color6"><?php echo Yii::t("labels", "Mother");?></p>
                            <p class="font14 color">{{stuData.mName}}</p>
                            </div>
                            <div  class="col-md-3">
                                <p class="font12 color6"><?php echo Yii::t("labels", "Mobile Phone");?></p>
                                <p class="font14 color">{{stuData.mTel}}</p>
                            </div>
                            <div  class="col-md-6">
                                <p class="font12 color6"><?php echo Yii::t("child", "Email");?></p>
                                <p class="font14 color">{{stuData.mEmail}}</p>
                            </div>
                        </div>
                         <hr>
                        <div class="clearfix"></div>
                    </div>
                    </div>
                </div>
            </div>     
            <div class="modal fade" id="deleteModal" tabindex="-1" role="dialog">
                <div class="modal-dialog modal-sm" role="document">
                    <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title" id="parentCommentsModalLabel"><?php echo Yii::t("newDS", "批量标记已审核");?></h4>
                    </div>
                    <div class="modal-body">          
                       
                        <div><?php echo Yii::t("newDS", "确认批量标记为已审核吗？");?></div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                        <button type="button" class="btn btn-primary" :disabled='batchBtn' @click='batchpass()'><?php echo Yii::t("newDS", "确认");?></button>

                    </div>
                    </div>
                </div>
            </div>       
        </div>
    </div>
</div>

<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>
<script>
    var schoolTitle = '<?php echo $schoolTitle ?>';
    $(function() {
		$("#startDate").datepicker({
			dateFormat: "yy-mm-dd ",
		});
        $( "#startDate").on('change', function () {
            container.startDate = $('#startDate').val();
        });
        $("#endDate").datepicker({
			dateFormat: "yy-mm-dd ",
		});
        $( "#endDate").on('change', function () {
            container.endDate = $('#endDate').val();
        });
	});
    var container = new Vue({
        el: "#container",
        data: {
            tips_text_cn:'',
            tips_text_en:'',
            startDate:'',
            startHours:'00',
            startMinutes:"00",
            endDate:'',
            endHours:'00',
            endMinutes:"00",
            class_ids:[],
            dataList:[
                {
                    "id": "6151a3a595ee2f5e1858b94a",
                    "child_id": 12168,
                    "class_id": 1860,
                    "audit_status": 0,
                    "address": 1632968184,
                }
            ],
            schoolTitle:schoolTitle,
            auditStatus:0,
            tabDetailData:{},
            printIds:[],
            initData:{},
            childDetail:{},
            activeId:'',
            stuData:{},
            childId:'',
            tableData:[],
            childInfo:{},
            wechatAll:false,
            wechatList:[],
            send_wechat:true,
            sendData:{},
            wechatBtn:false,
            comment:'',
            btnCon:false,
            classTitle:'',
            printIds:[],
            printAll:false,
            checkData:[],
            batchBtn:false,
            loading:false,
            statusList:[],
            classData:{},
            dataListSend:[],
            unSendData:[]
        },
        watch:{},
        created:function(){
            let that=this
            this.getData()
        },
        methods: {
            classTitle(id){
              for(var i=0;i<this.initData.classList.length;i++){
                if(id==this.initData.classList[i].class_id){
                    return this.initData.classList[i].title
                }
              }  
            },
            getData(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("getData") ?>',
                    type: "get",
                    dataType: 'json',
                    success: function(data) {
                        if (data.state == 'success') {
                            that.initData=data.data
                            that.tips_text_en=data.data.tips_text_en
                            that.tips_text_cn=data.data.tips_text_cn
                            that.statusList=data.data.statusList
                            if(data.data.start!=''){
                                that.startDate=that.newDate(data.data.start,'date')
                                that.startHours=that.newDate(data.data.start,'hours')
                                that.startMinutes=that.newDate(data.data.start,'minutes')
                            }
                            if(data.data.end!=''){
                                that.endDate=that.newDate(data.data.end,'date')
                                that.endHours=that.newDate(data.data.end,'hours')
                                that.endMinutes=that.newDate(data.data.end,'minutes')
                            }
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                    },
                })
            },
            newDate(dateTime,type){
                const date = new Date(dateTime.replace(/\-/g, '/'));
                const Y = date.getFullYear() + '-';
                const M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
                const D = (date.getDate() < 10 ? '0'+date.getDate() : date.getDate()) + ' ';
                const h = (date.getHours() < 10 ? '0'+date.getHours() : date.getHours()) ;
                const m = (date.getMinutes() <10 ? '0'+date.getMinutes() : date.getMinutes());
                const s = date.getSeconds(); // 秒
                const dateString = Y + M + D + h+':' + m+':' + s;
                if(type=='hours'){
                    return h
                } if(type=='minutes'){
                    return m
                } if(type=='date'){
                    return Y + M + D 
                }
                if(!type){
                    return dateString;
                }
            },
            editInfo(){
                $('#infoModal').modal('show') 
            },
            saveInfo(){
                let that=this
                let start=$('#startDate').val().trim()+' '+this.startHours+':'+this.startMinutes
                let end=$('#endDate').val().trim()+' '+this.endHours+':'+this.endMinutes
                $.ajax({
                    url: '<?php echo $this->createUrl("edit") ?>',
                    type: "post",
                    dataType: 'json',
                    data:{
                        "start":start,
                        "end": end,
                        "tips_text_cn": that.tips_text_cn,
                        "tips_text_en": that.tips_text_en,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                          resultTip({
                                msg: data.state
                            });
                           $('#infoModal').modal('hide') 
                           that.getData()
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                    },
                })
            },
            progress(data,type){
                let num=data[type] <= 0? "0%" : Math.round((data[type] / data.total) * 10000) / 100.0 + "%";
                return 'width:'+num
            },
            tabList(data){
                let that=this
                this.printIds=[]
                this.classData=data
                this.activeId=data.class_id
                this.classTitle=data.title
                this.printAll=false
                $.ajax({
                    url: '<?php echo $this->createUrl("list") ?>',
                    type: "post",
                    dataType: 'json',
                    data:{
                        "class_id":data.class_id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.dataList=data.data.items
                            that.tableData=data.data
                            that.childInfo=data.data.childInfo
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                    },
                })
            },
            editData(list){
                this.tabDetailData=list
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("detail") ?>',
                    type: "get",
                    dataType: 'json',
                    data:{
                        "id": list.id,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.childDetail=data.data  
                            that.auditStatus=data.data.audit_status
                            $('#editModal').modal('show')                       
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                    },
                })
            },
            saveAudit(){
                let that=this
                if(this.auditStatus==2 && this.comment.trim()==''){
                    resultTip({
                        error: 'warning',
                        msg: '请输入未通过原因'
                    });
                    return
                }
                this.btnCon=true
                $.ajax({
                    url: '<?php echo $this->createUrl("audit") ?>',
                    type: "get",
                    dataType: 'json',
                    data:{
                        "audit_status": this.auditStatus,
                        "comment": this.comment,
                        id:this.childDetail.id,
                        send_wechat:that.send_wechat?1:0
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.editData(that.tabDetailData)       
                            that.tabList(that.classData)       
                            for(var i=0;i<that.initData.classList.length;i++){
                                if(that.initData.classList[i].class_id==that.activeId){
                                    if(that.initData.classList[i].newSubmitNum!=0){
                                        that.initData.classList[i].newSubmitNum=that.initData.classList[i].newSubmitNum-1
                                    }
                                }
                            }    
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                        that.btnCon=false
                    },
                    error: function(data) {
                        that.btnCon=false
                    },
                })
            },
            resetStatus(){
                let that=this
                that.btnCon=false
                $.ajax({
                    url: '<?php echo $this->createUrl("reset") ?>',
                    type: "get",
                    dataType: 'json',
                    data:{
                        id:this.childDetail.id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.editData(that.tabDetailData)    
                            that.tabList(that.classData)  
                            $('#resetModal').modal('hide')  
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                    },
                })                
            },
            stuInfo(childId){
                this.childId=childId
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("student/childInfo") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        childid:childId
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                        that.stuData=data.data
                        $('#childInfo').modal()
                        } else {
                            resultTip({  
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            wechatModel(){
                let that=this
                this.wechatList=[]
                this.wechatAll=false
                that.checkData=[]
                let dataList=JSON.parse(JSON.stringify(that.tableData.unSubmitChild))
                for(var i=0;i<that.dataList.length;i++){
                    if(that.dataList[i].audit_status==2){
                        dataList.push(that.dataList[i].child_id)
                    }
                }
                that.dataListSend=dataList
                if(this.dataListSend.length==0){
                    resultTip({
                        error: 'warning',
                        msg: '暂无可发送人员'
                    });
                    return
                }
                var list=[]
                for(var key in this.childInfo){
                    list.push(key)
                }
                // console.log(that.dataListSend)
                $.ajax({
                    url: '<?php echo $this->createUrl("sendInfo") ?>',
                    type: "post",
                    dataType: 'json',
                    data:{
                        "child_ids":list
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.sendData=data.data
                            that.unSendData=[]
                            for(var i=0;i<dataList.length;i++){
                                if(that.sendData[dataList[i]] && that.sendData[dataList[i]].can_send!=1){
                                    that.unSendData.push(that.sendData[dataList[i]].child_id)  
                                }
                            }
                            $('#wechatModal').modal('show')               
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                    },
                })
            },
            wechatAllData(){
                if(this.wechatAll){
                    let dataList=JSON.parse(JSON.stringify(this.dataListSend))
                    this.unSendData.forEach(item => { 
                        if (dataList.indexOf(item)!=-1){
                            dataList.splice(dataList.indexOf(item),1)
                        } 
                    })
                    this.checkData=dataList
                    // console.log(dataList)
                    this.wechatList=this.checkData
                }else{
                    this.wechatList=[]
                }
            },
            wechatData(){
                if(this.wechatList.length!=0){
                    if(this.wechatList.length==this.checkData.length){
                        this.wechatAll=true
                    }else{
                        this.wechatAll=false
                    }
                }
                
            },
            sendWechat(){
                let that=this
                if(this.wechatList.length==0){
                    resultTip({error: 'warning', msg:'请选择学生'});
                    return
                }
                that.wechatBtn=true
                that.loading=true
                $.ajax({
                    url: '<?php echo $this->createUrl("send") ?>',
                    type: "post",
                    dataType: 'json',
                    data:{
                        "child_ids":this.wechatList
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.wechatBtn=false
                            that.loading=false
                            resultTip({
                                msg:data.state
                            });   
                            that.wechatModel()            
                        } else {
                            that.loading=false
                            that.wechatBtn=false
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        that.loading=false
                        that.wechatBtn=false
                    },
                })
            },
            exportInfo(obj) {        
                const filename =this.classTitle+'.xlsx';
                const ws_name = "SheetJS";
                var exportData = [];
                     for(var i=0;i<this.tableData.items.length;i++){
                        let status=this.statusList[this.tableData.items[i].audit_status]
                        exportData.push({'学生ID':this.tableData.items[i].child_id,'学生姓名':this.childInfo[this.tableData.items[i].child_id],"状态":status,"帐户名":this.tableData.items[i].bank_user,"银行名称含支行":this.tableData.items[i].bank_name,"银行所在城市":this.tableData.items[i].bank_city,"帐户号码":this.tableData.items[i].bank_account,});
                    }
                    for(var i=0;i<this.tableData.unSubmitChild.length;i++){
                        exportData.push({'学生ID':this.tableData.unSubmitChild[i],'学生姓名':this.childInfo[this.tableData.unSubmitChild[i]],"状态":"","帐户名":"","银行名称含支行":"","银行所在城市":"","帐户号码":"",});
                    }
                    var da=XLSX.utils.json_to_sheet(exportData,{
                        origin:'A1',// 从A1开始增加内容
                        header: ['学生ID','学生姓名', '状态', '帐户名', '银行名称含支行',"银行所在城市",'帐户号码', ],
                        // skipHeader: true// 跳过上面的标题行
                    });
                    // return
                // const worksheet = XLSX.utils.aoa_to_sheet(workSheet);
                const workbook = XLSX.utils.book_new();
                // console.log(worksheet)
                XLSX.utils.book_append_sheet(workbook, da, ws_name);
                // generate Blob
                const wbout = XLSX.write(workbook, {bookType: 'xlsx', type: 'array'});
                const blob = new Blob([wbout], {type: 'application/octet-stream'});
                // save file
                // return
                let link = document.createElement('a');
                link.href = URL.createObjectURL(blob);
                link.download = filename;
                link.click();
                setTimeout(function() {
                    // 延时释放掉obj
                    URL.revokeObjectURL(link.href);
                    link.remove();
                }, 500);
            },
            printAllData(){
                if(this.printAll){
                    this.printIds=[]
                    for(var i=0;i<this.dataList.length;i++){
                        if(this.dataList[i].audit_status!=1){
                            this.printIds.push(this.dataList[i].id)
                        }
                    }
                }else{
                    this.printIds=[]
                }
            },
            printData(){
                var itemList=[]
                for(var i=0;i<this.dataList.length;i++){
                    if(this.dataList[i].audit_status!=1){
                        itemList.push(this.dataList[i].id)
                    }
                }
                if(this.printIds.length==itemList.length){
                    this.printAll=true
                }else{
                    this.printAll=false
                }
            },
            bathAudit(){
                if(this.printIds.length==0){
                    resultTip({error: 'warning', msg:'请选择学生'});
                    return
                }
                $('#deleteModal').modal('show')
            },
            batchpass(){
                let that=this
                that.batchBtn=true
                $.ajax({
                    url: '<?php echo $this->createUrl("batchpass") ?>',
                    type: "post",
                    dataType: 'json',
                    data:{
                        "ids":this.printIds
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.batchBtn=false
                            resultTip({
                                msg:data.state
                            });   
                            that.tabList(that.classData)  
                            $('#deleteModal').modal('hide')
                        } else {
                            that.batchBtn=false
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                    },
                })
            },
            classTotal(list){
                if(list.approvedNum==list.total){
                    return true
                }
            }
            
        }
    })
</script>
