<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span>
    </button>
    <h4 class="modal-title" id="exampleModalLabel">图片编辑</h4>
</div>
<?php
$form = $this->beginWidget('CActiveForm', array(
    'id' => 'editphoto',
    'enableAjaxValidation' => false,
    'htmlOptions' => array('class' => 'J_ajaxForm form-horizontal', 'role' => 'form', 'enctype'=>"multipart/form-data"),
)); ?>
<div class="modal-body">
    <div class="form-group">
        <label class="col-xs-2 control-label"></label>
        <div class="col-xs-8">
            <?php 
            $data = json_decode($model->data);
            $src = Yii::app()->params['OAUploadBaseUrl'] . '/crads/' . $data->photo . "?" . time();;
             ?>
            <img id="image" class="img-responsive" src="<?php echo $src; ?>">
            <input type="hidden" name="base64" id="base64">
            <div>
                <span class="btn btn-default glyphicon glyphicon-edit" aria-hidden="true" onclick="edit(this)"></span>
            </div>
            <div id = 'edit' hidden="hidden">
                <span class="btn btn-default glyphicon glyphicon-arrow-left" aria-hidden="true" onclick="rotate(this, 90)"></span>
                <span class="btn btn-default glyphicon glyphicon-arrow-right" aria-hidden="true" onclick="rotate(this, -90)"></span>
                <span class="btn btn-default glyphicon glyphicon-ok" aria-hidden="true" onclick="sure(this)"></span>
            </div>
        </div>
    </div>
</div>
<div class="modal-footer">
    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('global', 'Cancel'); ?></button>
    <button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global', 'Submit'); ?></button>
</div>
<?php $this->endWidget(); ?>
<script>
    function edit(btn) {
        $('#edit').show();
        $('#image').cropper('rotate', 0);
    }

    function rotate(btn, num) {
        $('#image').cropper('rotate', num);
    }

    function sure(btn) {
        $('#edit').hide();
        var newImage = $('#image').cropper("getCroppedCanvas").toDataURL();
        $('#image').attr('src', newImage);
        $('#image').cropper('destroy');
        $('#base64').val(newImage);
    }
</script>

