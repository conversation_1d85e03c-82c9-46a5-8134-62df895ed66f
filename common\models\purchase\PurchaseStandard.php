<?php

/**
 * This is the model class for table "ivy_purchase_standard".
 *
 * The followings are the available columns in table 'ivy_purchase_standard':
 * @property integer $id
 * @property string $cn_title
 * @property string $tip
 * @property integer $add_timestamp
 * @property integer $add_user
 * @property integer $update_timestamp
 */
class PurchaseStandard extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_purchase_standard';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('cn_title, status, school_type, pro_type', 'required'),
			array('add_timestamp, add_user, update_timestamp', 'numerical', 'integerOnly'=>true),
			array('cn_title, school_type, pro_type', 'length', 'max'=>255),
			array('tip', 'safe'),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, cn_title, tip, add_timestamp, add_user, update_timestamp, status, school_type, pro_type', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
			'productItem'=>array(self::HAS_MANY, 'PurchaseProductsItem', 'sid'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'cn_title' => '标配标题',
			'tip' => '标配备注',
			'add_timestamp' => '添加时间',
			'add_user' => '操作用户',
			'update_timestamp' => '最后更新',
			'status' => '状态',
			'school_type' => '学校类型',
			'pro_type' => '商品类型',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('cn_title',$this->cn_title,true);
		$criteria->compare('tip',$this->tip,true);
		$criteria->compare('add_timestamp',$this->add_timestamp);
		$criteria->compare('add_user',$this->add_user);
		$criteria->compare('update_timestamp',$this->update_timestamp);
		$criteria->compare('status',$this->update_timestamp);
		$criteria->compare('school_type',$this->school_type);
		$criteria->compare('pro_type',$this->pro_type);
		$criteria->order = 'status DESC';

		return new CActiveDataProvider($this, array(
			'criteria' => $criteria,
			'sort'=>array(
			     'defaultOrder' => 't.school_type',
			 ),
		));
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return PurchaseStandard the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * 获取当前标配的商品总价
	 * @return [type] [description]
	 */
	public function getTotalMoney()
	{
		Yii::import('common.models.purchase.*');
		$price = 0;
		$criteria=new CDbCriteria;
		$criteria->compare('sid',$this->id);
		$ppiModels = PurchaseProductsItem::model()->findAll($criteria);
		foreach ($ppiModels as $ppiModel) {
			$price += floatval($ppiModel->product->price) * $ppiModel->num;
		}
		return number_format($price, 2, '.', '');
	}

	protected function afterSave()
	{
		if (!$this->isNewRecord) {
			$this->changeApplicationStatus();
		}
		parent::afterSave();
	}

	protected function afterDelete()
	{
		$this->changeApplicationStatus();
		parent::afterDelete();
	}

	/**
	 * 修改相关的申请表状态
	 * @return [type] [description]
	 */
	public function changeApplicationStatus()
	{
		$criteria = new CDbCriteria;
		$criteria->compare('type',PurchaseApplicationItem::PURCHASE_STAND);
		$criteria->compare('pid',$this->id);
		$paiModels = PurchaseApplicationItem::model()->findAll($criteria);
		foreach ($paiModels as $paiModel) {
			//如果是正在进行中的申请
			if ($paiModel->application->status == PurchaseApplication::INPROGRESS) {
				$paiModel->application->status = PurchaseApplication::CHANGED;
				$paiModel->application->save();
			}
		}
	}

	public function getType($type)
	{
		$criteria=new CDbCriteria;
		$criteria->select = $type;
		$criteria->group = $type;

		return $this->findAll($criteria);
	}
}
