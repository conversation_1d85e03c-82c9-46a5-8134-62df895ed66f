<div class="container-fluid">
	<ol class="breadcrumb">
		<li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('//mcampus/default/index')) ?></li>
		<li><?php echo CHtml::link(Yii::t('site', 'Routines'), array('//mcampus/default/index')) ?></li>
		<li class="active"><?php echo Yii::t('site', 'Device list')?></li>
	</ol>

	<div class="row">
		<div class="col-md-2 col-sm-2 mb10">
			<?php
			$this->widget('zii.widgets.CMenu', array(
				'items' => $this->module->getMenu(),
				'id' => 'pageCategory',
				'htmlOptions' => array('class' => 'nav nav-pills nav-stacked background-gray'),
				'activeCssClass' => 'active',
			));
			?>
		</div>
		<div class="col-md-10 col-sm-10">
			<a class="btn btn-primary J_modal" href="<?php echo $this->createUrl('updateDevice'); ?>">
                    <span class="glyphicon glyphicon-plus"
						  aria-hidden="true"></span> <?php echo Yii::t("site", "Add device"); ?>
			</a>
			<br>
			<br>
			<?php
			$this->widget('ext.ivyCGridView.BsCGridView', array(
				'id' => 'cardsList',
				'afterAjaxUpdate' => 'js:head.Util.modal',
				'dataProvider' => $cardsObj,
				'template' => "{items}{pager}",
				//状态为无效时标红
				//'rowCssClassExpression' => '( $data->active == 0 ? "active" : "" )',
				'colgroups' => array(
					array(
						"colwidth" => array(200, 200, 200, 200),
					)
				),
				'columns' => array(
					'device_id',
					array(
						'name'=>'status',
						'value'=> '($data->status == 1) ? "有效" : "无效" ',
					),
					array(
						'name'=>'updated',
						'value'=> 'date("Y-m-d", $data->updated)',
					),
					array(
						'name' => Yii::t('global', 'Action'),
						'value' => array($this, 'getButton'),
					),
				),

			));
			?>
		</div>
	</div>
</div>

<script>
	var modal = '<div class="modal fade" id="modal" class="1123" tabindex="-1" role="dialog" aria-labelledby="modal"><div class="modal-dialog modal-lg" role="document"><div class="modal-content"></div></div></div>';
	$('body').append(modal);

	function cbUpdateDevices()
	{
		location.reload();
	}
</script>

<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>