<?php
class UserRepass extends CFormModel
{
	public $pass;
	public $repass;
	
	public function rules()
	{
		return array(
			array('pass, repass', 'required'),
            array('pass', 'length', 'min'=>8),
            array('pass', 'match', 'pattern'=>'/^.*(?=.*\d)(?=.*[A-Za-z]).*$/', 'message'=>Yii::t('message', 'Combination of letters and numbers, min 8-length.')),
			array('pass', 'compare', 'compareAttribute'=>'repass'),
		);
	}

	/**
	 * Declares attribute labels.
	 */
	public function attributeLabels()
	{
		return array(
			'pass'=>Yii::t("user", "New password"),
			'repass'=>Yii::t("user", "Repeat password"),
		);
	}
}