<div class="product">
    <?php
    if (!empty($data->Product->cover)):
        $imgUrl = Yii::app()->params['uploadBaseUrl'] . 'points/thumb/' . $data->Product->Image->image;
    else:
        $imgUrl = Yii::app()->theme->baseUrl.'\images\pic_loading_s.gif';
    endif;
    $img = CHtml::image($imgUrl, '', array('class' => 'fl cover', 'width' => 60));
    echo $img;
    ?>
    <h5>
        <?php
        echo CHtml::link(CHtml::encode($data->Product->getContent("title")), '#');
        ?>
    </h5>			
    <p>
    	<?php echo Yii::t('payment', ':points points', array(':points'=> '<span class="pt">'.$data->credits.'</span>' )) ?> ×
        <?php echo $data->quantity?>
        <?php if ($data->status == PointsStatus::STATS_CREATED && time()<=$data->created_timestamp+PointsOrder::model()->cancleOrderParam()): ?>
            <span id="o_<?php echo $data->id?>">
                <?php echo CHtml::Button(Yii::t('payment', 'Cancel the order'), array('class'=>'o-order', 'id'=>$data->id)) ?>
            </span>
        <?php endif; ?>
    </p>
	<div class="clear"></div>
    <ul>
		<li><label><?php echo addColon(Yii::t('payment', 'Order Date')) ?></label><?php echo Mims::formatDateTime($data->created_timestamp); ?></li>
        <li><label><?php echo addColon(Yii::t('payment', 'Order Status')) ?></label><?php echo $data->getStatus(); ?></li>
    </ul>

    <div class="sep"></div>
</div>