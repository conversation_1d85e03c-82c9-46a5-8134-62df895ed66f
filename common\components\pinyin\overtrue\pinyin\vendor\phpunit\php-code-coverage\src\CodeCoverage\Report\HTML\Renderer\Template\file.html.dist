<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Code Coverage for {{full_path}}</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="{{path_to_root}}css/bootstrap.min.css" rel="stylesheet">
  <link href="{{path_to_root}}css/style.css" rel="stylesheet">
  <!--[if lt IE 9]>
  <script src="{{path_to_root}}js/html5shiv.min.js"></script>
  <script src="{{path_to_root}}js/respond.min.js"></script>
  <![endif]-->
 </head>
 <body>
  <header>
   <div class="container">
    <div class="row">
     <div class="col-md-12">
      <ol class="breadcrumb">
{{breadcrumbs}}
      </ol>
     </div>
    </div>
   </div>
  </header>
  <div class="container">
   <table class="table table-bordered">
    <thead>
     <tr>
      <td>&nbsp;</td>
      <td colspan="10"><div align="center"><strong>Code Coverage</strong></div></td>
     </tr>
     <tr>
      <td>&nbsp;</td>
      <td colspan="3"><div align="center"><strong>Classes and Traits</strong></div></td>
      <td colspan="4"><div align="center"><strong>Functions and Methods</strong></div></td>
      <td colspan="3"><div align="center"><strong>Lines</strong></div></td>
     </tr>
    </thead>
    <tbody>
{{items}}
    </tbody>
   </table>
   <table id="code" class="table table-borderless table-condensed">
    <tbody>
{{lines}}
    </tbody>
   </table>
   <footer>
    <hr/>
    <h4>Legend</h4>
    <p>
     <span class="success"><strong>Executed</strong></span>
     <span class="danger"><strong>Not Executed</strong></span>
     <span class="warning"><strong>Dead Code</strong></span>
    </p>
    <p>
     <small>Generated by <a href="http://github.com/sebastianbergmann/php-code-coverage" target="_top">PHP_CodeCoverage {{version}}</a> using <a href="{{runtime_link}}" target="_top">{{runtime_name}} {{runtime_version}}</a>{{generator}} at {{date}}.</small>
    </p>
    <a title="Back to the top" id="toplink" href="#"><span class="glyphicon glyphicon-arrow-up"></span></a>
   </footer>
  </div>
  <script src="{{path_to_root}}js/jquery.min.js" type="text/javascript"></script>
  <script src="{{path_to_root}}js/bootstrap.min.js" type="text/javascript"></script>
  <script src="{{path_to_root}}js/holder.min.js" type="text/javascript"></script>
  <script type="text/javascript">
  $(function() {
   var $window   = $(window)
     , $top_link = $('#toplink')
     , $body     = $('body, html')
     , offset    = $('#code').offset().top;

   $top_link.hide().click(function(event) {
    event.preventDefault();
    $body.animate({scrollTop:0}, 800);
   });

   $window.scroll(function() {
    if($window.scrollTop() > offset) {
     $top_link.fadeIn();
    } else {
     $top_link.fadeOut();
    }
   }).scroll();

   $('.popin').popover({trigger: 'hover'});
  });
  </script>
 </body>
</html>
