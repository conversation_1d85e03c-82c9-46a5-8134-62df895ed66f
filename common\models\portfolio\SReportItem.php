<?php

/**
 * This is the model class for table "ivy_sreport_item".
 *
 * The followings are the available columns in table 'ivy_sreport_item':
 * @property integer $id
 * @property integer $report_id
 * @property string $category
 * @property integer $media_id
 * @property integer $ld_id
 * @property string $content
 * @property string $img_cropper
 */
class SReportItem extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_sreport_item';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('content, category, subcategory', 'safe'),
			array('report_id, media_id, ld_id', 'numerical', 'integerOnly'=>true),
			array('category', 'length', 'max'=>64),
			array('img_cropper', 'length', 'max'=>255),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, report_id, category, media_id, ld_id, content, img_cropper', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'report' => array(self::BELONGS_TO, 'SReport', array('report_id'=>'id'))
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'report_id' => 'Report',
			'category' => 'Category',
			'media_id' => Yii::t('teaching', 'Photo'),
			'ld_id' => Yii::t('teaching', 'Learning Domain'),
			'content' => Yii::t('teaching', 'Content'),
			'img_cropper' => Yii::t('teaching', 'Img Cropper'),
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('report_id',$this->report_id);
		$criteria->compare('category',$this->category,true);
		$criteria->compare('media_id',$this->media_id);
		$criteria->compare('ld_id',$this->ld_id);
		$criteria->compare('content',$this->content,true);
		$criteria->compare('img_cropper',$this->img_cropper,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

    public function getDbConnection()
    {
        return Yii::app()->subdb;
    }

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return SReportItem the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}
