<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site','HQ Operations'), array('/moperation'))?></li>
        <li><?php echo CHtml::link(Yii::t('site','Basic Configurations'), array('/moperation'))?></li>
        <li class="active"><?php echo Yii::t('site','查看校园事故上报') ?></li>
    </ol>

    <div class="row">
        <div class="col-md-2 col-sm-2 mb10">
            <?php
            $this->widget('zii.widgets.CMenu', array(
                'items' => $this->module->getHomeMenu(),
                'id' => 'pageCategory',
                'htmlOptions' => array('class' => 'nav nav-pills nav-stacked background-gray'),
                'activeCssClass' => 'active',
            ));
            ?>
        </div>
        <div class="col-md-10 col-sm-10">
            <?php
            $this->widget('ext.ivyCGridView.BsCGridView', array(
                'id' => 'safetyIncidentReport',
                'afterAjaxUpdate' => 'js:head.Util.modal',
                'dataProvider' => $dataProvider,
                'template' => "{items}{pager}",
                //状态为无效时标红
                //'rowCssClassExpression' => '( $data->active == 0 ? "active" : "" )',
                'colgroups' => array(
                    array(
                        "colwidth" => array(200, 200, 200, 200, 200, 200),
                    )
                ),
                'columns' => array(
                    array(
                        'name'=> 'school_id',
                        'value'=> array($this, 'getSchool'),
                    ),
                    'incident_name',
                    array(
                        'name'=>'incident_date',
                        'value'=> 'date("Y-m-d", $data->incident_date)',
                    ),
                    'incident_place',
                    array(
                        'name'=>'status',
                        'value'=> array($this, 'getReportStatus'),
                    ),
                    array(
                        'name' => Yii::t('laseregg', 'Edit'),
                        'value' => array($this, 'getButtonReport'),
                    ),
                ),

            ));
            ?>
        </div>
    </div>
</div>