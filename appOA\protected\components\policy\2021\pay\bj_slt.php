<?php
if(!defined("IVY_POLICY"))
	throw new CException('DO NOT USE THIS FILE DIRECTLY');

return array( 'policy'=>array(

	//是否老生老办法
	ENABLE_CONSTANT_TUITION => false,
	//FENTAN_FACTOR => DAILY,
	//校园多收费摸式
	MORE_FEE_BY_SCHOOL => true,

	//是否开放课外活动账单
	ENABLE_AFTERSCHOOLS => false,

	NEWFUNCTION => true,

	//图书馆工本费
	FEETYPE_LIBCARD => 10,

	//学期四个时间点；取前后两端
	TIME_POINTS   => array(202109,202201,202202,202206),

	//年付开通哪些缴费类型
	PAYGROUP_ANNUAL => array(
		ITEMS => array(
			FEETYPE_TUITION,
			FEETYPE_LUNCH,
			FEETYPE_SCHOOLBUS,
		),
		TUITION_CALC_BASE => BY_MONTH,
	),
//
//	//学期付开通哪些缴费类型
	PAYGROUP_SEMESTER => array(
		ITEMS => array(
			FEETYPE_TUITION,
//			FEETYPE_LUNCH,
			FEETYPE_SCHOOLBUS,
		),
		TUITION_CALC_BASE => BY_MONTH,
	),

	//月份开通哪些缴费类型
	/*
    PAYGROUP_MONTH => array(
        ITEMS => array(
            FEETYPE_TUITION,
            FEETYPE_LUNCH,
            FEETYPE_SCHOOLBUS,
        ),
        TUITION_CALC_BASE => BY_MONTH,
    ),
     *
     */

	//计算基准：BY_MONTH, 或者 BY_SETTING, 前者表示所有的学费都基于月费用计算，后者表示所有的学费都基于学期或学年
	TUITION_CALCULATION_BASE => array(
		'BY_GRADE' =>array(
			BY_MONTH => array(
				//月收费金额
				AMOUNT => array(
					FULL5D => 21000,
//					FULL5D1 => 19500,
				),
				//收费月份，及其权重
				MONTH_FACTOR => array(
                    202108=>0,
                    202109=>1,
                    202110=>1,
                    202111=>1,
                    202112=>1,
                    202201=>1,
                    202202=>1,
                    202203=>1,
                    202204=>1,
                    202205=>1,
                    202206=>1,
				),
				//折扣
				GLOBAL_DISCOUNT => array(
					DISCOUNT_ANNUAL => '1:1:2022-06-01',
					DISCOUNT_SEMESTER1 => '1:1:2022-02-01',
					DISCOUNT_SEMESTER2 => '1:1:2022-06-01'
				),
//				LUNCH_PERDAY=> 885.7
				LUNCH_PERDAY=> 891.8
			),
		),
	),

	//每餐费用
	//LUNCH_PERDAY => 30,

	//账单到期日和账单开始日之差
	DUEDAYS_OFFSET => 0,

));