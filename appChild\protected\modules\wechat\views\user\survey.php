<?php $lang = (Yii::app()->language == 'zh_cn') ? 'cn' : 'en';
    $title = 'title_' . $lang;
    // 由于微信支付安全目录问题，特殊处理支付页面的跳转URL
    $payMentUrl = Yii::app()->createUrl('wechat/user/unpaidInvoices')
        . '/?openid=' . $_GET['openid']
        . '&access_token=' . $_GET['access_token']
        . '&state=' . $_GET['state'];
    $required = array();
    $requiredRadio = array();
    $requiredCheck = array();
?>
<div class="panel">
    <div class="bd">
        <div class="weui_panel weui_panel_access">
            <?php
                 //选择孩子
                $this->widget('ext.wechat.ChildSelector',array(
                    'childObj'=>$childObj,
                    'myChildObjs'=>$this->myChildObjs,
                    'jumpUrl'=>'survey',
                    'params'=>array('id'=>$fid, 'lang'=>Yii::app()->request->getParam('lang', 'zh_cn')),
                ));
             ?>
            <div id="billList" class="weui_panel_bd"></div>
        </div>
    </div>
</div>
<?php if($surveyObj->isNewRecord): ?>
    <div class="weui_msg">
        <div class="weui_msg_icon_area"><i class="weui_icon_info weui_icon_msg"></i></div>
        <div class="weui_msg_text_area">
            <h2 class="weui_msg_title"><?php echo Yii::t('survey', 'There is no survey available'); ?></h2>
            <p class="weui_msg_desc"><?php echo Yii::t('survey', "No survey found. Parents' survey is only open to current students' parents. If you have any query, please contact the school"); ?></p>
        </div>
        <div class="weui_msg_opr_area">
            <p class="weui_btn_area">
                <a href="javascript:wx.closeWindow();" class="weui_btn weui_btn_default"><?php echo Yii::t('survey', 'Close'); ?></a>
            </p>
        </div>
    </div>
<?php elseif($surveyObj->surveyDetail->start_time > time()): ?>
    <div class="weui_msg">
        <div class="weui_msg_icon_area"><i class="weui_icon_waiting weui_icon_msg"></i></div>
        <div class="weui_msg_text_area">
            <h2 class="weui_msg_title"><?php echo Yii::t('survey', 'The survey is not open yet'); ?></h2>
            <p class="weui_msg_desc"><?php echo Yii::t('survey', 'Please start the survey on %s', array('%s'=>date('Y-m-d', $surveyObj->surveyDetail->start_time))); ?></p>
        </div>
        <div class="weui_msg_opr_area">
            <p class="weui_btn_area">
                <a href="javascript:wx.closeWindow();" class="weui_btn weui_btn_default"><?php echo Yii::t('survey', 'Close'); ?></a>
            </p>
        </div>
    </div>
<?php elseif($surveyObj->surveyDetail->end_time < time() - 86400 ): ?>
    <div class="weui_msg">
        <div class="weui_msg_icon_area"><i class="weui_icon_info weui_icon_msg"></i></div>
        <div class="weui_msg_text_area">
            <h2 class="weui_msg_title"><?php echo Yii::t('survey', 'This survey has closed'); ?></h2>
            <p class="weui_msg_desc"><?php echo Yii::t('survey', 'Time closed: %s', array('%s'=>date('Y-m-d', $surveyObj->surveyDetail->end_time))); ?></p>
        </div>
        <div class="weui_msg_opr_area">
            <p class="weui_btn_area">
                <a href="javascript:wx.closeWindow();" class="weui_btn weui_btn_default"><?php echo Yii::t('survey', 'Close'); ?></a>
            </p>
        </div>
    </div>
<?php elseif($feedbackObj->isNewRecord): ?>
    <div class="weui_msg">
        <div class="weui_msg_icon_area"><i class="weui_icon_warn weui_icon_msg"></i></div>
        <div class="weui_msg_text_area">
            <h2 class="weui_msg_title"><?php echo Yii::t('survey', 'Your survey is unavailable'); ?></h2>
            <p class="weui_msg_desc"><?php echo Yii::t('survey', 'Please contact our admin'); ?></p>
        </div>
        <div class="weui_msg_opr_area">
            <p class="weui_btn_area">
                <a href="javascript:wx.closeWindow();" class="weui_btn weui_btn_default"><?php echo Yii::t('survey', 'Close'); ?></a>
            </p>
        </div>
    </div>
<?php else: ?>
<?php if(!$isFeedback): ?>
    <div class="weui_msg">
        <div class="weui_icon_area"><i class="weui_icon_success weui_icon_msg"></i></div>
        <div class="weui_text_area">
            <h2 class="weui_msg_title"><?php echo $surveyObj->getTitle(); ?></h2>
            <div class="weui_msg_desc" style="text-align: left">
            <?php
            if($surveyObj->survey_type == Survey::SURVEY_TYPE_THIRD){
                echo Yii::t('survey','<p>That concludes the survey. Thank you very much for taking your time to complete the survey. If you have any further questions, please email us at <a href="mailto:<EMAIL>"><EMAIL></a><p>');
            } elseif($surveyObj->survey_type == Survey::SURVEY_TYPE_ONE) {
                if (in_array($schoolid, array('BJ_DS', 'BJ_SLT'))) {
                    if (Yii::app()->language == 'en_us') {
                        echo '<p>Thank you for taking the time to fill this survey out. Every response is important to us, and we will be reading through them carefully.</p>';
                        echo '<p><br>Daystar Academy</p>';
                    }
                    else {
                        echo '<p>感谢您参与这次的调查问卷。每一份反馈对我们都很重要，我们将认真仔细阅读。</p>';
                        echo '<p><br>启明星学校</p>';
                    }
                }
                else {
                    echo '<p>' . Yii::t('survey', 'We have successfully received your survey submission, thank you! As it will take our staff some time to read through every survey carefully and to compile the results, please be patient in that we may not be able to respond to you immediately or individually. ') . '</p>';
                    echo '<p>' . Yii::t('survey', 'If you have further comments, you can always email our support team at Ivy headquarters: <a href="mailto:<EMAIL>"><EMAIL></a>.') . '</p>';
                    echo '<p>' . Yii::t('survey', 'Ivy Education Group') . '</p>';
                }
            }else{
                $url = 'http://mega.ivymik.cn/Ways_of_payment_'.Yii::app()->language.'.jpg';
                // echo Yii::t('survey', '<p>Thank you for submitting your 2018-2019 school year re-enrolment confirmation！</p>');
                if (in_array($schoolid, array('BJ_DS', 'BJ_SLT'))) {
                    echo Yii::t('survey', '<p>Thank you for submitting your re-enrolment confirmation. The next step for you to complete the re-enrollment process is to pay the placement deposit by January 31, 2019  through online "<a href=":url">Payment-Tuition Deposit</a>". If we don\'t receive your payment by January 31, 2019, your child will be put into waitlist.<p>', array(':url' => $payMentUrl));
                    echo '<br>';
                    echo Yii::t('survey', '<p>If you have any questions regarding re-enrollment please feel free to contact admissions:</p><p>Beigao Campus: <br>5603-9446 <br> <a href="mailto:<EMAIL>"><EMAIL></a></p><p>Sanlitun Campus: <br>8532-2500 <br> <a href="mailto:<EMAIL>"><EMAIL></a></p>');
                    echo '<br>';
                } else {
                    echo Yii::t('survey', '<p>Thank you for submitting your re-enrollment confirmation. The next step for you to complete the re-enrollment process is to pay the placement deposit by Tuesday, May 21st  through online "<a href=":url">Payment-Tuition Deposit</a>". If we don\'t receive your payment by Tuesday, May 21st, your child will be put into waitlist.<p>', array(':url' => $payMentUrl));
                    echo '<br>';
                }

                // echo Yii::t('survey', '<p>If you have any questions regarding re-enrollment please feel free to contact admissions:</p><p>Beigao Campus: <br>5603-9446 <br> <a href="mailto:<EMAIL>"><EMAIL></a></p><p>Sanlitun Campus: <br>8532-2500 <br> <a href="mailto:<EMAIL>"><EMAIL></a></p>');
            }
             ?>
            </div>
        </div>
        <div class="weui_opr_area">
            <p class="weui_btn_area">
                <?php echo CHtml::link(Yii::t('wechat', 'Close'), 'javascript:wx.closeWindow();', array('class'=>'weui_btn weui_btn_default'))?>
            </p>
        </div>
    </div>
<?php else: ?>
    <div class="page slideIn cell">
        <form action="<?php echo $this->createUrl('saveSurvey'); ?>" method="post" id="form">
        <div class="bd">
            <div class="weui_cells_title"><strong><?php echo $surveyObj->getTitle(); ?></strong></div>
            <div class="weui_cells_title">
                <?php
                if ($surveyObj->survey_type == Survey::SURVEY_TYPE_ONE){
                    if($surveyObj->id == 60){ //艾毅
                        $this->renderPartial('application.modules.child.views.survey.' . Yii::t("survey", '_en_us_message_entrepreneurship'), array('endTime'=>$endTime));
                    }else if($surveyObj->id == 58){//   启明星
                        $this->renderPartial('application.modules.child.views.survey.' . Yii::t("survey", '_en_us_message_ds_entrepreneurship'), array('endTime'=>$endTime));
                    }
                    elseif(in_array($surveyObj->id, array(62, 63, 64))){
                        $this->renderPartial('application.modules.child.views.survey.' . Yii::t("survey", '_en_us_message_mid_year'), array('endTime'=>$endTime));
                    }
                    elseif(in_array($surveyObj->id, array(74,75))){
                        $this->renderPartial('application.modules.child.views.survey.' . Yii::t("survey", '_en_us_message_ds'), array('endTime'=>$endTime));
                    }
                    else{
                        $this->renderPartial('application.modules.child.views.survey.' . Yii::t("survey", '_en_us_message'), array('endTime'=>$endTime));
                    }
                }else if($surveyObj->survey_type == Survey::SURVEY_TYPE_THIRD){
                    $this->renderPartial('application.modules.child.views.survey.' . Yii::t("survey", '_en_us_third_message'), array('endTime' => $surveyObj->surveyDetail->end_time, 'schoolid'=>$schoolid));
                }else{
                    $this->renderPartial('application.modules.child.views.survey.' . Yii::t("survey", '_en_us_return_message'), array('schoolid'=>$schoolid, 'surveyid'=>$surveyObj->id));
                } ?>
            </div>
            <div class="weui_cells">
                <div class="weui_cell">
                    <div class="weui_cells_title  weui_cell_bd weui_cell_primary">
                        <?php echo Yii::t('survey', 'Deadline'); ?>
                    </div>
                    <div class="weui_cell_ft"><?php echo $endTime; ?></div>
                </div>
            </div>
            <?php foreach ($topics as $key => $topic): ?>
                <div class="weui_cells_title" style="color: #333;" id="title_<?php echo $topic['id']; ?>">
                    <?php if(in_array($topic['binary_flag'], array(2, 3))){ echo '*'; $required[$topic['id']] = $key;} ?>
                    <?php echo isset($numberConfig[$key]) ? $numberConfig[$key] : $key+1; ?>.
                    <?php echo nl2br($topic[$title]); ?>
                </div>
                <?php if ($topic['cat'] == 1):if(in_array($topic['binary_flag'], array(2, 3))) $requiredRadio[] = $topic['id']; ?>
                <!-- 单选题 -->
                <div class="weui_cells weui_cells_checkbox">
                    <?php foreach ($options[$topic['option_group']] as $k=>$option):?>
                        <label class="weui_cell weui_check_label " for="<?php echo $topic['id'] . $k; ?>">
                            <div class="weui_cell_ft">
                                <input type="radio" class="weui_check" name="group[<?php echo $topic['id'];?>][value]" value="<?php echo $option['option_value'];?>" id="<?php echo $topic['id'] . $k; ?>">
                                <span class="icon-radio"></span>
                            </div>
                            <div class="weui_cells_title weui_cell_hd">
                                <?php echo $option[$title]; ?>
                            </div>
                        </label>
                    <?php endforeach; ?>
                    <?php if (in_array($topic['binary_flag'], array(1, 3))): ?>
                    <div class="weui_cell">
                    <div class="weui_cell_bd weui_cell_primary">
                        <textarea class="weui_textarea" name="group[<?php echo $topic['id'];?>][memo]" placeholder="<?php echo Yii::t('survey', 'Additional Comments: '); ?>" rows="3"></textarea>
                    </div>
                    </div>
                    <?php endif; ?>
                </div>
                <?php else: if(in_array($topic['binary_flag'], array(2, 3))) $requiredCheck[] = $topic['id']; ?>
                <!-- 多选题 -->
                <div class="weui_cells weui_cells_checkbox">
                <?php foreach ($options[$topic['option_group']] as $k=>$option):?>
                    <label class="weui_cell weui_check_label" for="<?php echo $topic['id'] . $k; ?>">
                        <div class="weui_cell_ft">
                            <input type="checkbox" class="weui_check" name="group[<?php echo $topic['id'];?>][value][]" value="<?php echo $option['option_value'];?>" id="<?php echo $topic['id'] . $k; ?>" >
                            <span class="icon-checked"></span>
                        </div>
                        <div class="weui_cells_title weui_cell_hd">
                            <?php echo $option[$title]; ?>
                        </div>
                    </label>
                <?php endforeach; ?>
                    <?php if (in_array($topic['binary_flag'], array(1, 3))): ?>
                    <div class="weui_cell">
                    <div class="weui_cell_bd weui_cell_primary">
                        <textarea class="weui_textarea" name="group[<?php echo $topic['id'];?>][memo]" placeholder="<?php echo Yii::t('survey', 'Additional Comments: '); ?>" rows="3"></textarea>
                    </div>
                    </div>
                    <?php endif; ?>
                </div>
                <?php endif; ?>
            <?php endforeach; ?>
            <div class="weui_btn_area">
                <input type="hidden" name="surveyid" value="<?php echo $surveyObj->id; ?>">
                <input type="hidden" name="childid" value="<?php echo $childObj->childid; ?>">
                <a class="weui_btn weui_btn_primary" href="javascript:" id="surveybutton"><?php echo Yii::t('survey', 'Submit') ?></a>
            </div>
        </div>
        </form>
    </div>
<?php endif; ?>
<?php endif; ?>
<div class="weui_dialog_confirm" id="dialog" hidden="hidden">
    <div class="weui_mask"></div>
    <div class="weui_dialog">
        <div class="weui_dialog_hd"><strong class="weui_dialog_title">Select language 选择语言</strong></div>
        <!-- <div class="weui_dialog_bd">请选择您常用的语言</div> -->
        <div class="weui_dialog_ft">
            <a href="<?php echo $this->createUrl('survey', array('id'=>$fid, 'lang'=>'en_us')); ?>" class="weui_btn_dialog primary">English</a>
            <a href="<?php echo $this->createUrl('survey', array('id'=>$fid, 'lang'=>'zh_cn')); ?>" class="weui_btn_dialog primary">中文</a>
        </div>
    </div>
</div>
<script>
    var requiredRadio = <?php echo json_encode($requiredRadio); ?>;
    var requiredCheck = <?php echo json_encode($requiredCheck); ?>;
    var required = <?php echo json_encode($required); ?>;

    var lang = '<?php echo Yii::app()->request->getParam('lang', ''); ?>';
    var dialog = $('#dialog');
    if (lang=='') {
        dialog.show();
    }
    $('#surveybutton').bind('click', function () {
        var btn = $(this);
        btn.addClass('weui_btn_disabled');

        var flag = true;
        var flagid = 0;
        var flagid1 = 0;
        var flagid2 = 0;
        // 查看单选必选是否全部选择
        $.each(requiredRadio, function (k, v) {
            $('#title_'+v).css('color', '');
            var value = $("input[name='group["+v+"][value]']:checked").val();
            if (value == undefined) {
                if (flagid1 == 0) {
                    flagid1 = v;
                }
                $('#title_'+v).css('color', '#E15119');
                flag = false;
            }
        });
        // 查看多选必选是否全部选择        
        $.each(requiredCheck, function (k, v) {
            $('#title_'+v).css('color', '');
            var length = $("input[name='group["+v+"][value][]']:checked").length;
            if (length == 0) {
                if (flagid2 == 0) {
                    flagid2 = v;
                }
                $('#title_'+v).css('color', '#E15119');
                flag = false;
            }
        });

        if (!flag) {
            if (flagid1==0) {
                flagid = flagid2;
            } else if (flagid2==0) {
                flagid = flagid1;
            } else {
                flagid = flagid1;
                if (required[flagid1] > required[flagid2]) {
                    flagid = flagid2;
                }
            }
            var target =document.querySelector('#title_'+flagid);
            showTips('<?php echo Yii::t('global', 'Failed!'); ?>', '<?php echo Yii::t('survey', 'Please complete all the questions with * before submission.');?>');
            target.scrollIntoView(true);
            btn.removeClass('weui_btn_disabled');
            return true;
        }
        // 检查必填字段
        $.ajax({
            url: '<?php echo $this->createUrl('saveSurvey'); ?>',
            type: 'POST',
            data: $('#form').serialize(),
            dataType: 'json',
            success:function (data) {
                if (data.state == 'success') {
                    showMessage('<?php echo Yii::t('global', 'Success!'); ?>');
                    location.reload();
                } else{
                    console.log($('#btn'));
                    window.location.hash = "#btn";
                    showTips('<?php echo Yii::t('global', 'Failed!'); ?>', data.message);
                }
                btn.removeClass('weui_btn_disabled');
            }
        });
    });
</script>
<style>
    .weui_cells_title p{
        margin-bottom: 10px;
    }
    textarea::-webkit-input-placeholder {
        font-size: 12px;
    }
    textarea::-moz-placeholder {
        font-size: 12px;
    }
</style>
