<?php

class BankTransferController extends ProtectedController {

    public function init() {
        parent::init();
        Yii::import('common.models.invoice.*');
    }

    public function actionOnlineBankInfo() {
        $schoolId = $this->getSchoolId();

        $bank_info = BranchVar::model()->bankInfo($schoolId);

        $this->renderPartial('/bankTransfer/onlineBankInfo', array(
                'bank_info' => $bank_info
            )
        );
    }

}
