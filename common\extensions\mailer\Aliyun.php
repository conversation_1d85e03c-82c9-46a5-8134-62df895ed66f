<?php
/**
 * EMailer class file.
 *
 * <AUTHOR>
 * @version 2.2
 * @link http://www.yiiframework.com/
 * @copyright Copyright &copy; 2009 MetaYii
 *
 * Copyright (C) 2009 MetaYii.
 *
 * 	This program is free software: you can redistribute it and/or modify
 * 	it under the terms of the GNU Lesser General Public License as published by
 * 	the Free Software Foundation, either version 2.1 of the License, or
 * 	(at your option) any later version.
 *
 * 	This program is distributed in the hope that it will be useful,
 * 	but WITHOUT ANY WARRANTY; without even the implied warranty of
 * 	MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * 	GNU Lesser General Public License for more details.
 *
 * 	You should have received a copy of the GNU Lesser General Public License
 * 	along with this program.  If not, see <http://www.gnu.org/licenses/>.
 *
 * For third party licenses and copyrights, please see phpmailer/LICENSE
 *
 */

/**
 * Include the the PHPMailer class.
 */
require_once(dirname(__FILE__).DIRECTORY_SEPARATOR.'PHPMailer2'.DIRECTORY_SEPARATOR.'PHPMailerAutoload.php');

/**
 * EMailer is a simple wrapper for the PHPMailer library.
 * @see http://phpmailer.codeworxtech.com/index.php?pg=phpmailer
 *
 * <AUTHOR>
 * @package application.extensions.emailer 
 * @since 1.0
 */
class Aliyun
{
   //***************************************************************************
   // Configuration
   //***************************************************************************

   /**
    * The path to the directory where the view for getView is stored. Must not
    * have ending dot.
    *
    * @var string
    */
   protected $pathViews = 'application.views.email';

   /**
    * The path to the directory where the layout for getView is stored. Must
    * not have ending dot.
    *
    * @var string
    */
   protected $pathLayouts = 'application.views.email.layouts';

   //***************************************************************************
   // Private properties
   //***************************************************************************

   /**
    * The internal PHPMailer object.
    *
    * @var object PHPMailer
    */
   private $_myMailer;

   //***************************************************************************
   // Initialization
   //***************************************************************************

   /**
    * Init method for the application component mode.
    */
   public function init() {}

   /**
    * Constructor. Here the instance of PHPMailer is created.
    */
	public function __construct()
	{
		$this->_myMailer = new PHPMailer();
	}

   //***************************************************************************
   // Setters and getters
   //***************************************************************************

   /**
    * Setter
    *
    * @param string $value pathLayouts
    */
   public function setPathLayouts($value)
   {
      if (!is_string($value) && !preg_match("/[a-z0-9\.]/i", $value))
         throw new CException(Yii::t('EMailer', 'pathLayouts must be a Yii alias path'));
      $this->pathLayouts = $value;
   }

   /**
    * Getter
    *
    * @return string pathLayouts
    */
   public function getPathLayouts()
   {
      return $this->pathLayouts;
   }

   /**
    * Setter
    *
    * @param string $value pathViews
    */
   public function setPathViews($value)
   {
      if (!is_string($value) && !preg_match("/[a-z0-9\.]/i", $value))
         throw new CException(Yii::t('EMailer', 'pathViews must be a Yii alias path'));
      $this->pathViews = $value;
   }

   /**
    * Getter
    *
    * @return string pathViews
    */
   public function getPathViews()
   {
      return $this->pathViews;
   }

   //***************************************************************************
   // Magic
   //***************************************************************************

   /**
    * Call a PHPMailer function
    *
    * @param string $method the method to call
    * @param array $params the parameters
    * @return mixed
    */
	public function __call($method, $params)
	{
		if (is_object($this->_myMailer) && get_class($this->_myMailer)==='PHPMailer') return call_user_func_array(array($this->_myMailer, $method), $params);
		else throw new CException(Yii::t('EMailer', 'Can not call a method of a non existent object'));
	}

   /**
    * Setter
    *
    * @param string $name the property name
    * @param string $value the property value
    */
	public function __set($name, $value)
	{
	   if (is_object($this->_myMailer) && get_class($this->_myMailer)==='PHPMailer') $this->_myMailer->$name = $value;
	   else throw new CException(Yii::t('EMailer', 'Can not set a property of a non existent object'));
	}

   /**
    * Getter
    *
    * @param string $name
    * @return mixed
    */
	public function __get($name)
	{
	   if (is_object($this->_myMailer) && get_class($this->_myMailer)==='PHPMailer') return $this->_myMailer->$name;
	   else throw new CException(Yii::t('EMailer', 'Can not access a property of a non existent object'));
	}

	/**
	 * Cleanup work before serializing.
	 * This is a PHP defined magic method.
	 * @return array the names of instance-variables to serialize.
	 */
	public function __sleep()
	{
	}

	/**
	 * This method will be automatically called when unserialization happens.
	 * This is a PHP defined magic method.
	 */
	public function __wakeup()
	{
	}

   //***************************************************************************
   // Utilities
   //***************************************************************************

   /**
    * Displays an e-mail in preview mode. 
    *
    * @param string $view the class
    * @param array $vars
    * @param string $layout
    */
   public function getView($view, $vars = array(), $layout = null)
   {
      $body = Yii::app()->controller->renderPartial($this->pathViews.'.'.$view, array_merge($vars, array('content'=>$this->_myMailer)), true);
      if ($layout === null) {
         $this->_myMailer->Body = $body;
      }
      else {
         $this->_myMailer->Body = Yii::app()->controller->renderPartial($this->pathLayouts.'.'.$layout, array('content'=>$body), true);
      }
   }
   
    /**
    * 在命令行下发信使用此方法,注意模板的路径
    * @param string $view the class
    * @param array $vars
    * @param string $layout
    */
   public function getCommandView($view, $vars = array(), $layout = null)
   {
      
	  $path = Yii::app()->basePath . '/views/email/';
	  $body = CConsoleCommand::renderFile($path.$view.'.php', array_merge($vars, array('content'=>$this->_myMailer)), true);
      if ($layout === null) {
         $this->_myMailer->Body = $body;
      }
      else {
         $this->_myMailer->Body = CConsoleCommand::renderFile($path . 'layouts/'.$layout.'.php', array('content'=>$body), true);
      }
   }
   
   public function loginInfo($mail)
   {
       if( !isset(Yii::app()->params['senderConfig']) ){
           $senderConfig = array(
               'host' => 'smtp.office365.com',
               'port' => 25,
               'username' => '<EMAIL>',
               'password' => 'Ivy2021!',
               'sender' => '<EMAIL>',
               'from' => '<EMAIL>',
               'fromname' => 'IvySchools',
           );
       }
       else{
           $senderConfig = Yii::app()->params['senderConfig'];
       }
		switch($mail){
			case 'smtp':
				$this->IsSMTP();
				$this->SMTPAuth=true;
				$this->Host = $senderConfig['host'];
                $this->Port = $senderConfig['port'];
				$this->Username = $senderConfig['username'];
				$this->Password = $senderConfig['password'];
		
				$this->Sender = $senderConfig['sender'];
				$this->From = $senderConfig['from'];
				$this->FromName = $senderConfig['fromname'];
				
				break;
			case 'sendmail':
				$this->IsSendmail();
				$this->Sender = $senderConfig['sender'];
				$this->From = $senderConfig['from'];
				$this->FromName = $senderConfig['fromname'];
				break;
		}

        $this->CharSet = 'UTF-8';
        $this->IsHTML(true);
   }

    public function iniAsTest()
    {
        $to = '<p>TO: ';
        foreach($this->getToAddresses() as $addr){
            $to .= $addr[0].'; ';
        }
        $to .= '</p>';
        $cc = '<p>CC: ';
        foreach($this->getCCAddresses() as $addr){
            $cc .= $addr[0].'; ';
        }
        $cc .= '</p>';
        $bcc = '<p>BCC: ';
        foreach($this->getBCCAddresses() as $addr){
            $bcc .= $addr[0].'; ';
        }
        $bcc .= '</p>';
        $reply = '<p>ReplyTo: ';
        foreach($this->getReplyToAddresses() as $addr){
            $reply .= $addr[0].'; ';
        }
        $reply .= '</p>';
        $this->Body .= $to.$cc.$bcc.$reply;
        $this->ClearAddresses();
        $this->ClearCCs();
        $this->ClearBCCs();

        $this->AddAddress("<EMAIL>","IVY IT");
    }
   
    public function iniAsProduction()
    {
        $this->AddBCC("<EMAIL>","IVY IT");
    }

    public function iniMail($isProduction=false, $mail='smtp')
    {
        $this->loginInfo($mail);
        if ( $isProduction){
            $this->iniAsProduction();
        }
        else {
            $this->iniAsTest();
        }
    }

    public function pushTo($flag='', $schoolid='')
    {
       if($flag && $schoolid){
           Yii::import('common.models.mailer.*');
           $branchData = Branch::model()->getTitleEmail();
           $supportEmail = $branchData[$schoolid]['support'];
           $crit = new CDbCriteria();
           $crit->compare('flag', $flag);
           $crit->index = 'branch_id';
           $modals = MailRecipient::model()->findAll($crit);
           if(isset($modals[$schoolid])){
               $modal = $modals[$schoolid];
           }
           else{
               $modal = $modals['all'] ? $modals['all'] : null;
           }
           if($modal){
               if($modal->send_to){
                   foreach(explode(',', $modal->send_to) as  $email){
                       $this->AddAddress($email);
                   }
               }
               if($modal->cc_to){
                   foreach(explode(',', $modal->cc_to) as  $email){
                       $this->AddCC($email);
                   }
               }
               if($modal->bcc_to){
                   foreach(explode(',', $modal->bcc_to) as  $email){
                       $this->AddBCC($email);
                   }
               }
               if($modal->mail_reply_to){
                   foreach(explode(',', $modal->mail_reply_to) as  $email){
                       $this->AddReplyTo($email);
                   }
               }
               $support = CJSON::decode($modal->support_email);
               if(isset($support['to'])){
                   $this->AddAddress($supportEmail);
               }
               if(isset($support['cc'])){
                   $this->AddCC($supportEmail);
               }
               if(isset($support['bcc'])){
                   $this->AddBCC($supportEmail);
               }
               if(isset($support['reply_to'])){
                   $this->AddReplyTo($supportEmail);
               }
           }
           else{
               $this->AddAddress($supportEmail);
           }
       }
    }

    public function Send()
    {
        $toAddress = array();
        foreach ($this->getToAddresses() as $v) {
            $toAddress[] = $v[0];
        }
        foreach ($this->getCCAddresses() as $v) {
            $toAddress[] = $v[0];
        }
        foreach ($this->getBccAddresses() as $v) {
            $toAddress[] = $v[0];
        }
        $subject = $this->Subject;
        $htmlBody = $this->Body;
        $fromAlias =  $this->FromName;

        try {
            return CommonUtils::sendEmail($toAddress, $subject, $htmlBody, '', $fromAlias);
        } catch (Exception $e) {
            throw new phpmailerException($e->getMessage());
        }

        return false;
    }
}