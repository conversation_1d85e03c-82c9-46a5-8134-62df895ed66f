<div class="weui_cells_title"><?php echo Yii::t('wechat', 'Pick-up Card'); ?></div>
<div class="panel">
    <div class="bd">
        <?php if($parents && $open){ ?>
            <?php foreach($parents as $childid => $childParent){?>
                <div class="weui_panel weui_panel_access">
                    <?php foreach($childParent as $k=>$item){?>
                        <?php if($k < 1){ ?>
                            <div class="weui_panel_hd"><?php echo $item['childName'] ?></div>
                        <?php } ?>
                        <div class="weui_panel_bd <?php if(in_array($item['sign'], array(1,2))){ echo "weui_btn_default";} ?>">
                            <a href="<?php echo $this->createUrl('cardsInfo', array('cardId' => $item['id'])); ?>"
                               class="weui_media_box weui_media_appmsg">
                                <div class="weui_media_hd" style="background: #999">
                                    <img class="weui_media_appmsg_thumb" src="<?php echo $item['photo']; ?>" alt="">
                                </div>
                                <div class="weui_media_bd">
                                    <h4 class="weui_media_title"><?php echo $item['relation']; ?></h4>

                                    <p class="weui_media_desc"><?php echo $item['parentName']; ?>; <?php echo $item['tel']; ?></p>
                                </div>
                            </a>
                        </div>
                    <?php } ?>
                    <a class="weui_panel_ft" href="<?php echo $this->createUrl('cardsInfo', array('childid' => $childid)); ?>"><?php echo Yii::t('wechat', 'Add a New Card'); ?></a>
                </div>
            <?php } ?>
        <?php }else{ ?>
            <div class="weui_cells">
                <div class="weui_cell">
                    <div class="weui_cell_bd weui_cell_primary">
                        <p style="color:#E64340">
                            <?php 
                                if ($open) {
                                    $message = Yii::t('wechat', 'Pick-up card is not available. Please contact the campus to get the card. ');
                                } else {
                                    $message = Yii::t('wechat', 'The function is not open');
                                }
                            ?>
                            <?php echo $message; ?>
                        </p>
                    </div>
                </div>
            </div>
        <?php } ?>
    </div>
</div>