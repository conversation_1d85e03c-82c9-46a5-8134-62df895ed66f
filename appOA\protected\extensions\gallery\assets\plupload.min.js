(function(){var d=0,i=[],k={},g={},a={"<":"lt",">":"gt","&":"amp",'"':"quot","'":"#39"},j=/[<>&\"\']/g,b,c=window.setTimeout;function f(){this.returnValue=false}function h(){this.cancelBubble=true}(function(l){var m=l.split(/,/),n,p,o;for(n=0;n<m.length;n+=2){o=m[n+1].split(/ /);for(p=0;p<o.length;p++){g[o[p]]=m[n]}}})("application/msword,doc dot,application/pdf,pdf,application/pgp-signature,pgp,application/postscript,ps ai eps,application/rtf,rtf,application/vnd.ms-excel,xls xlb,application/vnd.ms-powerpoint,ppt pps pot,application/zip,zip,application/x-shockwave-flash,swf swfl,application/vnd.openxmlformats,docx pptx xlsx,audio/mpeg,mpga mpega mp2 mp3,audio/x-wav,wav,image/bmp,bmp,image/gif,gif,image/jpeg,jpeg jpg jpe,image/png,png,image/svg+xml,svg svgz,image/tiff,tiff tif,text/html,htm html xhtml,text/rtf,rtf,video/mpeg,mpeg mpg mpe,video/quicktime,qt mov,video/x-flv,flv,video/vnd.rn-realvideo,rv,text/plain,asc txt text diff log,application/octet-stream,exe");var e={STOPPED:1,STARTED:2,QUEUED:1,UPLOADING:2,FAILED:4,DONE:5,GENERIC_ERROR:-100,HTTP_ERROR:-200,IO_ERROR:-300,SECURITY_ERROR:-400,INIT_ERROR:-500,FILE_SIZE_ERROR:-600,FILE_EXTENSION_ERROR:-700,mimeTypes:g,extend:function(l){e.each(arguments,function(m,n){if(n>0){e.each(m,function(p,o){l[o]=p})}});return l},cleanName:function(l){var m,n;n=[/[\300-\306]/g,"A",/[\340-\346]/g,"a",/\307/g,"C",/\347/g,"c",/[\310-\313]/g,"E",/[\350-\353]/g,"e",/[\314-\317]/g,"I",/[\354-\357]/g,"i",/\321/g,"N",/\361/g,"n",/[\322-\330]/g,"O",/[\362-\370]/g,"o",/[\331-\334]/g,"U",/[\371-\374]/g,"u"];for(m=0;m<n.length;m+=2){l=l.replace(n[m],n[m+1])}l=l.replace(/\s+/g,"_");l=l.replace(/[^a-z0-9_\-\.]+/gi,"");return l},addRuntime:function(l,m){m.name=l;i[l]=m;i.push(m);return m},guid:function(){var l=new Date().getTime().toString(32),m;for(m=0;m<5;m++){l+=Math.floor(Math.random()*65535).toString(32)}return(e.guidPrefix||"p")+l+(d++).toString(32)},buildUrl:function(m,l){var n="";e.each(l,function(p,o){n+=(n?"&":"")+encodeURIComponent(o)+"="+encodeURIComponent(p)});if(n){m+=(m.indexOf("?")>0?"&":"?")+n}return m},each:function(o,p){var n,m,l;if(o){n=o.length;if(n===b){for(m in o){if(o.hasOwnProperty(m)){if(p(o[m],m)===false){return}}}}else{for(l=0;l<n;l++){if(p(o[l],l)===false){return}}}}},formatSize:function(l){if(l===b){return e.translate("N/A")}if(l>1048576){return Math.round(l/1048576,1)+" MB"}if(l>1024){return Math.round(l/1024,1)+" KB"}return l+" b"},getPos:function(m,q){var r=0,p=0,t,s=document,n,o;m=m;q=q||s.body;function l(z){var v,w,u=0,A=0;if(z){w=z.getBoundingClientRect();v=s.compatMode==="CSS1Compat"?s.documentElement:s.body;u=w.left+v.scrollLeft;A=w.top+v.scrollTop}return{x:u,y:A}}if(m.getBoundingClientRect&&(navigator.userAgent.indexOf("MSIE")>0&&s.documentMode!==8)){n=l(m);o=l(q);return{x:n.x-o.x,y:n.y-o.y}}t=m;while(t&&t!=q&&t.nodeType){r+=t.offsetLeft||0;p+=t.offsetTop||0;t=t.offsetParent}t=m.parentNode;while(t&&t!=q&&t.nodeType){r-=t.scrollLeft||0;p-=t.scrollTop||0;t=t.parentNode}return{x:r,y:p}},getSize:function(l){return{w:l.clientWidth||l.offsetWidth,h:l.clientHeight||l.offsetHeight}},parseSize:function(l){var m;if(typeof(l)=="string"){l=/^([0-9]+)([mgk]+)$/.exec(l.toLowerCase().replace(/[^0-9mkg]/g,""));m=l[2];l=+l[1];if(m=="g"){l*=1073741824}if(m=="m"){l*=1048576}if(m=="k"){l*=1024}}return l},xmlEncode:function(l){return l?(""+l).replace(j,function(m){return a[m]?"&"+a[m]+";":m}):l},toArray:function(n){var m,l=[];for(m=0;m<n.length;m++){l[m]=n[m]}return l},addI18n:function(l){return e.extend(k,l)},translate:function(l){return k[l]||l},addEvent:function(m,l,n){if(m.attachEvent){m.attachEvent("on"+l,function(){var o=window.event;if(!o.target){o.target=o.srcElement}o.preventDefault=f;o.stopPropagation=h;n(o)})}else{if(m.addEventListener){m.addEventListener(l,n,false)}}}};e.Uploader=function(o){var m={},r,q=[],s,n;r=new e.QueueProgress();o=e.extend({chunk_size:0,multipart:true,multi_selection:true,file_data_name:"file",filters:[]},o);function p(){var t;if(this.state==e.STARTED&&s<q.length){t=q[s++];if(t.status==e.QUEUED){t.status=e.UPLOADING;this.trigger("BeforeUpload",t);this.trigger("UploadFile",t)}else{p.call(this)}}else{this.stop()}}function l(){var u,t;r.reset();for(u=0;u<q.length;u++){t=q[u];if(t.size!==b){r.size+=t.size;r.loaded+=t.loaded}else{r.size=b}if(t.status==e.DONE){r.uploaded++}else{if(t.status==e.FAILED){r.failed++}else{r.queued++}}}if(r.size===b){r.percent=q.length>0?Math.ceil(r.uploaded/q.length*100):0}else{r.bytesPerSec=Math.ceil(r.loaded/((+new Date()-n||1)/1000));r.percent=r.size>0?Math.ceil(r.loaded/r.size*100):0}}e.extend(this,{state:e.STOPPED,features:{},files:q,settings:o,total:r,id:e.guid(),init:function(){var y=this,z,v,u,x=0,w;if(typeof(o.preinit)=="function"){o.preinit(y)}else{e.each(o.preinit,function(B,A){y.bind(A,B)})}o.page_url=o.page_url||document.location.pathname.replace(/\/[^\/]+$/g,"/");if(!/^(\w+:\/\/|\/)/.test(o.url)){o.url=o.page_url+o.url}o.chunk_size=e.parseSize(o.chunk_size);o.max_file_size=e.parseSize(o.max_file_size);y.bind("FilesAdded",function(A,D){var C,B,F=0,G,E=o.filters;if(E&&E.length){G=[];e.each(E,function(H){e.each(H.extensions.split(/,/),function(I){G.push("\\."+I.replace(new RegExp("["+("/^$.*+?|()[]{}\\".replace(/./g,"\\$&"))+"]","g"),"\\$&"))})});G=new RegExp(G.join("|")+"$","i")}for(C=0;C<D.length;C++){B=D[C];B.loaded=0;B.percent=0;B.status=e.QUEUED;if(G&&!G.test(B.name)){A.trigger("Error",{code:e.FILE_EXTENSION_ERROR,message:"File extension error.",file:B});continue}if(B.size!==b&&B.size>o.max_file_size){A.trigger("Error",{code:e.FILE_SIZE_ERROR,message:"File size error.",file:B});continue}q.push(B);F++}if(F){c(function(){y.trigger("QueueChanged");y.refresh()})}else{return false}});if(o.unique_names){y.bind("UploadFile",function(A,B){var D=B.name.match(/\.([^.]+)$/),C="tmp";if(D){C=D[1]}B.target_name=B.id+"."+C})}y.bind("UploadProgress",function(A,B){B.percent=B.size>0?Math.ceil(B.loaded/B.size*100):100;l()});y.bind("StateChanged",function(A){if(A.state==e.STARTED){n=(+new Date())}});y.bind("QueueChanged",l);y.bind("Error",function(A,B){if(B.file){B.file.status=e.FAILED;l();c(function(){p.call(y)})}});y.bind("FileUploaded",function(A,B){B.status=e.DONE;B.loaded=B.size;A.trigger("UploadProgress",B);c(function(){p.call(y)})});if(o.runtimes){v=[];w=o.runtimes.split(/\s?,\s?/);for(z=0;z<w.length;z++){if(i[w[z]]){v.push(i[w[z]])}}}else{v=i}function t(){var D=v[x++],C,A,B;if(D){C=D.getFeatures();A=y.settings.required_features;if(A){A=A.split(",");for(B=0;B<A.length;B++){if(!C[A[B]]){t();return}}}D.init(y,function(E){if(E&&E.success){y.features=C;y.trigger("Init",{runtime:D.name});y.trigger("PostInit");y.refresh()}else{t()}})}else{y.trigger("Error",{code:e.INIT_ERROR,message:"Init error."})}}t();if(typeof(o.init)=="function"){o.init(y)}else{e.each(o.init,function(B,A){y.bind(A,B)})}},refresh:function(){this.trigger("Refresh")},start:function(){if(this.state!=e.STARTED){s=0;this.state=e.STARTED;this.trigger("StateChanged");p.call(this)}},stop:function(){if(this.state!=e.STOPPED){this.state=e.STOPPED;this.trigger("StateChanged")}},getFile:function(u){var t;for(t=q.length-1;t>=0;t--){if(q[t].id===u){return q[t]}}},removeFile:function(u){var t;for(t=q.length-1;t>=0;t--){if(q[t].id===u.id){return this.splice(t,1)[0]}}},splice:function(v,t){var u;u=q.splice(v===b?0:v,t===b?q.length:t);this.trigger("FilesRemoved",u);this.trigger("QueueChanged");return u},trigger:function(u){var w=m[u.toLowerCase()],v,t;if(w){t=Array.prototype.slice.call(arguments);t[0]=this;for(v=0;v<w.length;v++){if(w[v].func.apply(w[v].scope,t)===false){return false}}}return true},bind:function(t,v,u){var w;t=t.toLowerCase();w=m[t]||[];w.push({func:v,scope:u||this});m[t]=w},unbind:function(t,v){var w=m[t.toLowerCase()],u;if(w){for(u=w.length-1;u>=0;u--){if(w[u].func===v){w.splice(u,1)}}}}})};e.File=function(o,m,n){var l=this;l.id=o;l.name=m;l.size=n;l.loaded=0;l.percent=0;l.status=0};e.Runtime=function(){this.getFeatures=function(){};this.init=function(l,m){}};e.QueueProgress=function(){var l=this;l.size=0;l.loaded=0;l.uploaded=0;l.failed=0;l.queued=0;l.percent=0;l.bytesPerSec=0;l.reset=function(){l.size=l.loaded=l.uploaded=l.failed=l.queued=l.percent=l.bytesPerSec=0}};e.runtimes={};window.plupload=e})();