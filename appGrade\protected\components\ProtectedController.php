<?php

/**
 * Controller is the customized base controller class.
 * All controller classes for this application should extend from this base class.
 */
class ProtectedController extends Controller {

    protected $childid = "";
    protected $childInfo = array('schoolid' => '', 'classid' => 0, 'yid' => 0, 'stat' => 0);
    //protected $childs;
    protected $nav;
    protected $role;
    public $myChildObjs = null; //当前用户所有的孩子;
    public $layout = '//layouts/column2_with_child';

    public function init() {
        parent::init();
        if (!($this->initChildInfo())) {
            Yii::app()->user->loginRequired();
        }
    }

    /*
      public function beforeAction($action)
      {
      $this->setPageTitle(Yii::app()->name.' - '.$this->nav[$this->id]['items'][$action->id]['label']);
      return true;
      }
     */

    /**
     * 根据不同角色初始化孩子信息
     * @return Ambiguous|boolean
     * <AUTHOR>
     */
    public function initChildInfo() {
        $_childid = Yii::app()->getRequest()->getParam("childid", "");
        // 如果是员工，直接使用 _GET['chilid']
        if (Yii::app()->user->getId() && (Yii::app()->user->getState("__isstaff") == Yii::app()->user->getId())) {
			$authKey = Yii::app()->user->getState('_previewCCToken'); //读取apps验证完权限之后写入SESSION的标识
			if(is_null($authKey) || Yii::app()->user->genAppsToken() != $authKey){
				throw new CException("Permission denied", 500);
			}
			if(!Yii::app()->user->checkStaffRole(0, false)){
				throw new CException("Permission denied", 500);
			}
            $this->setChildid($_childid);
            $this->role = "staff";
            $this->getAllChild($this->role);
            $this->nav = Mims::Nav();
            $this->setChildInfo();
            return $_childid;
        }


        // 如果是家长或访客, 判断 _GET['chilid'] 是否在可管理孩子id中及授权孩子id中
        elseif (in_array($_childid, Yii::app()->user->getAdminCIds())) {
            $this->setChildid($_childid);
            $this->role = "parent";
            $this->getAllChild($this->role);
            $this->nav = Mims::Nav();
            $this->setChildInfo();
            return $_childid;
        } elseif (in_array($_childid, Yii::app()->user->getVisitCIds())) {
            $this->setChildid($_childid);
            $this->role = "visitor";
            $this->getAllChild($this->role);
            $this->nav = Mims::Nav();
            $this->setChildInfo();
            return $_childid;
        }
        return false;
    }

    public function setChildid($childId) {
        $this->childid = $childId;
    }

    public function createChildUrl($route, $params = array(), $ampersand = '&') {
        if (empty($params['childid'])) {
            $params['childid'] = $this->childid;
        }
        return parent::createUrl($route, $params, $ampersand);
    }

    public function createUrl($route, $params = array(), $ampersand = '&', $parentOnly = false) {
        if (!$parentOnly) {
            if (empty($params['childid'])) {
                $params['childid'] = $this->childid;
            }
        }
        return parent::createUrl($route, $params, $ampersand);
    }

    public function getRole() {
        return $this->role;
    }

    public function getAllChild($role) {
        switch ($role) {
            case "parent":
                if (Yii::app()->user->id) {
                    $parent = IvyParent::model()->findByPk(Yii::app()->user->id);
                    if (!empty($parent) && count(@unserialize($parent->childs))) {
                        $cids = @unserialize($parent->childs);
                        $this->myChildObjs = ChildProfileBasic::model()->with('stat')->findAllByPk($cids, array("index" => "childid"));
                    }
                }
                break;
            case "staff":
                $this->myChildObjs = ChildProfileBasic::model()->with('stat')->findAllByPk($this->childid, array("index" => "childid"));
                break;
            case "visitor":
                $this->myChildObjs = ChildProfileBasic::model()->with('stat')->findAllByPk(Yii::app()->user->getVisitCIds(), array("index" => "childid"));
                break;
        }
    }

    public function getChildName($childId = 0) {
        $childId = ( $childId ) ? $childId : $this->getChildId();
        if (!empty($this->myChildObjs[$childId]))
            return $this->myChildObjs[$childId]->getChildName();
    }

    /**
     * 得到当前激活的孩子的ID
     * 只是为了不直接访问 childid 属性childid的getter方法而已
     * @return string
     * <AUTHOR>
     * @time 2012-7-23
     */
    public function getChildId() {
        return $this->childid;
    }

    /**
     * 得到当前激活孩子现在所在的学校
     * 以免一直写 $this->myChildObjs[$this->childid]->schoolid 这么长串啊
     * @return NULL
     * <AUTHOR>
     * @time 2012-7-23
     */
    public function getSchoolId() {
        if (isset($this->myChildObjs[$this->childid]))
            return $this->myChildObjs[$this->childid]->schoolid;
        else
            return null;
    }

    /**
     * 设置孩子基本信息数组
     * <AUTHOR> Chen <<EMAIL>>
     * @date 2012/9/28
     */
    public function setChildInfo() {
        $currentchild = $this->myChildObjs[$this->getChildId()];
        $this->childInfo = array(
            'schoolid' => $currentchild->schoolid,
            'classid' => $currentchild->classid?$currentchild->classid:null,
            'yid' => is_object($currentchild->stat)?$currentchild->stat->calendar:null,
            'stat' => is_object($currentchild->stat)?$currentchild->stat->stat:null,
        );
    }

    /**
     * 得到孩子基本信息数组
     * <AUTHOR> Chen <<EMAIL>>
     * @date 2012/9/28
     * @return Array
     */
    public function getChildInfo() {
        return $this->childInfo;
    }
    
    /**
     * 获取班级的主键
     * <AUTHOR> Yu <<EMAIL>>
     * @date   2015/10/20
     * @return int
     */
    public function getClassId(){
        return $this->myChildObjs[$this->getChildId()]->classid;
    }
}
