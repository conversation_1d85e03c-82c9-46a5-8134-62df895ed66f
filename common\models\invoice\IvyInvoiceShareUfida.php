<?php

/**
 * This is the model class for table "ivy_invoice_share_ufida".
 *
 * The followings are the available columns in table 'ivy_invoice_share_ufida':
 * @property integer $id
 * @property string $month
 * @property integer $month_stamp
 * @property integer $calendar_id
 * @property integer $invoice_id
 * @property string $payment_type
 * @property string $borrow
 * @property string $loan
 * @property string $borrow_accounts_code
 * @property string $loan_accounts_code
 * @property double $amount
 * @property integer $childid
 * @property integer $classid
 * @property string $schoolid
 * @property string $child_name
 * @property string $class_name
 * @property string $school_name
 * @property integer $fee_type
 * @property string $memo
 * @property integer $status
 * @property integer $timestmp
 */
class IvyInvoiceShareUfida extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @return IvyInvoiceShareUfida the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_invoice_share_ufida';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('borrow, loan, borrow_accounts_code, loan_accounts_code', 'required'),
			array('month_stamp, calendar_id, invoice_id, childid, classid, fee_type, status, timestmp', 'numerical', 'integerOnly'=>true),
			array('amount', 'numerical'),
			array('month', 'length', 'max'=>7),
			array('payment_type, child_name, class_name, school_name', 'length', 'max'=>255),
			array('borrow, loan', 'length', 'max'=>500),
			array('borrow_accounts_code, loan_accounts_code, schoolid', 'length', 'max'=>100),
			array('memo', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, month, month_stamp, calendar_id, invoice_id, payment_type, borrow, loan, borrow_accounts_code, loan_accounts_code, amount, childid, classid, schoolid, child_name, class_name, school_name, fee_type, memo, status, timestmp', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'month' => 'Month',
			'month_stamp' => 'Month Stamp',
			'calendar_id' => 'Calendar',
			'invoice_id' => 'Invoice',
			'payment_type' => 'Payment Type',
			'borrow' => 'Borrow',
			'loan' => 'Loan',
			'borrow_accounts_code' => 'Borrow Accounts Code',
			'loan_accounts_code' => 'Loan Accounts Code',
			'amount' => 'Amount',
			'childid' => 'Childid',
			'classid' => 'Classid',
			'schoolid' => 'Schoolid',
			'child_name' => 'Child Name',
			'class_name' => 'Class Name',
			'school_name' => 'School Name',
			'fee_type' => 'Fee Type',
			'memo' => 'Memo',
			'status' => 'Status',
			'timestmp' => 'Timestmp',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('month',$this->month,true);
		$criteria->compare('month_stamp',$this->month_stamp);
		$criteria->compare('calendar_id',$this->calendar_id);
		$criteria->compare('invoice_id',$this->invoice_id);
		$criteria->compare('payment_type',$this->payment_type,true);
		$criteria->compare('borrow',$this->borrow,true);
		$criteria->compare('loan',$this->loan,true);
		$criteria->compare('borrow_accounts_code',$this->borrow_accounts_code,true);
		$criteria->compare('loan_accounts_code',$this->loan_accounts_code,true);
		$criteria->compare('amount',$this->amount);
		$criteria->compare('childid',$this->childid);
		$criteria->compare('classid',$this->classid);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('child_name',$this->child_name,true);
		$criteria->compare('class_name',$this->class_name,true);
		$criteria->compare('school_name',$this->school_name,true);
		$criteria->compare('fee_type',$this->fee_type);
		$criteria->compare('memo',$this->memo,true);
		$criteria->compare('status',$this->status);
		$criteria->compare('timestmp',$this->timestmp);

		return new CActiveDataProvider(get_class($this), array(
			'criteria'=>$criteria,
		));
	}
	
	/**
	 * 判断某张账单某月是否已经在数据库存在
	 * @param int $invoiceId
	 * @param string $month
	 * @return int
	 * <AUTHOR>
	 * @copyright Ivy
	 */
	public function existsApportionMonth($invoiceId,$month)
	{
		$criteria=new CDbCriteria;
		$criteria->compare('invoice_id', $invoiceId);
		$criteria->compare('month', $month);
		return $this->model()->count($criteria);
	}
	
	/**
	 * 获取某张账单已经分摊的数据
	 * @param int $invoiceId
	 * @return ArrayIterator
	 * <AUTHOR>
	 * @copyright Ivy
	 */
	public function getApportionedList($invoiceId)
	{
		$apportionList = array();
		$criteria=new CDbCriteria;
		$criteria->select = 'month';
		$criteria->compare('invoice_id', $invoiceId);
		$result = $this->model()->findAll($criteria);
		if (!empty($result))
		{
			foreach ($result as $v)
			{
				$apportionList[] = $v->month;
			}
		}
		return $apportionList;
	}
	
	
	/**
	 * 合计某张账单已分摊的总金额
	 * @param int $invoiceId
	 * @return number
	 * <AUTHOR>
	 * @copyright Ivy
	 */
	public function sumApportioned($invoiceId)
	{
		$sumApportion = 0;
		$criteria=new CDbCriteria;
		$criteria->select = 'amount';
		$criteria->compare('invoice_id', $invoiceId);
		$result = $this->model()->findAll($criteria);
		if (!empty($result))
		{
			foreach ($result as $value) {
				$sumApportion += $value->amount;
			}
		}
		return abs($sumApportion);
	}
	
	
	
	/**
	 * 查询某个学校某段日期内的孩子
	 * @param string $schoolId
	 * @param string $startMon
	 * @param string $endMon
	 * <AUTHOR>
	 * @copyright Ivy
	 * @return ArrayIterator
	 */
	public function countActualIncome($schoolId,$startMon,$endMon)
	{
		$resultList = array();
		$criteria = new CDbCriteria;
		$criteria->select = 'childid,classid';
		$criteria->distinct = true;
		$criteria->compare('schoolid', $schoolId);
		$criteria->addBetweenCondition('month', $startMon, $endMon);
		$result = $this->model()->findAll($criteria);
		if (!empty($result))
		{
			foreach ($result as $v)
			{
				$resultList[$v->classid][$v->childid] = $v->childid;
			}
		}
		unset($result);
		return $resultList;
	}
	
	/**
	 * 获取某一个孩子所有分摊数据
	 * @param int $childId
	 * @param double $flag
	 * <AUTHOR>
	 * @copyright Ivy
	 * @return ArrayObject;
	 */
	public function getChildApportedData($schoolId,$classId,$childId,$startMon,$endMon,$flag)
	{
		$criteria = new CDbCriteria;
		$criteria->select = 'month,month_stamp,payment_type,amount,calendar_id';
		$criteria->compare('schoolid', $schoolId);
		$criteria->compare('classid', $classId);
		$criteria->compare('childid', $childId);
		$criteria->addBetweenCondition('month', $startMon, $endMon);
		if ($flag == false)
		{
			$criteria->compare('status',0);
		}
		return $this->model()->findAll($criteria);
	}
    
    /*
     * 汇总某学校所有分摊总收入（学费）
     * @param string $schoolid 学校ID
     * @return double $amount  总收入（学费）
     */
    static public function sumAmountForSchool($schoolId,$month){
        $amountData = Yii::app()->db->createCommand()
                    ->select('sum(amount) as amount')
                    ->from('ivy_invoice_share_ufida')
                    ->where('schoolid=:schoolid and month=:month and payment_type=:payment_type',array(':schoolid'=>$schoolId,':month'=>$month,':payment_type'=>'tuition'))
                    ->queryAll();
        if (is_array($amountData) && count($amountData)){
            $amountData = current($amountData);
            return $amountData['amount'];
        }
        return 0;
    }
}