<?php
class PosPayCommand extends CConsoleCommand{
    public function actionIndex(){
		echo $this->getHelp();
	}
    
    public function run($args){
        $isProduction = isset($args[0]) ? trim($args[0]) : 0;
        Yii::import('common.models.yeepay.YeepayOrder');
        $criteria = new CDbCriteria();
        $criteria->compare('status', 1);
        $criteria->compare('flag', 1);
        $criteria->index = 'orderId';
        $count = YeepayOrder::model()->count($criteria);
        if ($count){
           $orderModel = YeepayOrder::model()->findAll($criteria);
           PolicyApi::posToEmail(array_keys($orderModel),null,false,$isProduction);
           YeepayOrder::model()->updateAll(array('flag'=>0),$criteria);
        }else{
            echo 'No Data!';
        }
    }
}
?>
