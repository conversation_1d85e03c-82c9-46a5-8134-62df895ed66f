<?php

class PortfolioController extends ProtectedController {

    public $layout = "//layouts/column2_child_portfolio";

    //调用远程学期报告需要使用
    protected $securityKey = 'I3v4y7Onl8ine6Mi433ms';


    public function init() {
        Yii::import('common.models.calendar.*');
        parent::init();
    }

    public function actionIndex() {
        $this->render('index');
    }

    public function actionClasses() {
        $classList = null;
        $classids = $this->getChildClassid();
        $classShow = Yii::app()->subdb->createCommand()
            ->select('schoolid, status')
            ->from('ivy_school_class_show')
            ->where('schoolid = :schoolid', [':schoolid' => $this->getSchoolId()])
            ->queryRow();

        $classList = new CActiveDataProvider('IvyClass', array(
                    'criteria' => array(
                        'with' => array(
                            'classInfo' => array(
                                'condition' => 'h.childid=' . $this->childid . ' and h.classid in (' . implode(',', $classids) . ')',
                                'order' => 'h.timestamp DESC',
                                'together' => true,
                                'alias' => 'h',
                            ),
                            'schoolInfo' => array(
                                'alias' => 'b',
                            ),
                            'calendarInfo' => array(
                                'alias' => 'c',
                            ),
                        ),
                    ),
                    'pagination' => array("pagesize" => 30),
                ));
        
        $this->render('classes', array('classList' => $classList, 'classShow' => $classShow));
    }

    public function actionZerorizeReminder($startYear = 0) {
        if (Yii::app()->user->checkAccess('sendFeedback', array("childid" => $this->getChildId()))) {
            $this->updateRead($startYear);
        }
    }

    public function actionJournal() {
        $type = Yii::app()->getRequest()->getParam('type', "");
        if ($type == "feedback") {
            $this->showAllFeedback();
            Yii::app()->end();
        }
        $classid = Yii::app()->getRequest()->getParam('classid');
        $weeknum = Yii::app()->getRequest()->getParam('weeknum');
        $feedback = Yii::app()->getRequest()->getParam('feedback');
        $startyear = Yii::app()->getRequest()->getParam('startyear');
        Mims::LoadHelper('HtoolKits');

        Yii::import('common.models.classTeacher.ClassTeacher');
        Yii::import('common.models.classTeacher.InfopubStaffExtend');

        $classids = $this->getChildClassid();

        $csoh = array();
        $classOfYear = array();
        $criteria = new CDbCriteria();
        $criteria->compare('classid', $classids);
        $criteria->order = 'calendarInfo.startyear DESC';
        $classes = IvyClass::model()->with('calendarInfo')->findAll($criteria);
        $classe = array();
        foreach ($classes as $cla) {
            $classe[$cla->calendarInfo->startyear][$cla->calendarInfo->yid][$cla->classid] = $cla->classid;
            $classOfYear[$cla->calendarInfo->startyear] = $cla->classid;
            $csoh[$cla->classid] = $cla->schoolid;
        }
        $curryear = array();
        if (!$startyear) {
            if ($classe) {
                $startyear = max(array_keys($classe));
                $curryear = max($classe);
            }
        } else {
            $curryear = $classe[$startyear];
        }
        ChildMedia::setStartYear($startyear);
        ChildMediaLinks::setStartYear($startyear);
        $criteria = new CDbCriteria();
        foreach ($curryear as $ykey => $cy) {
            $criteria1 = new CDbCriteria();
            $criteria1->compare('yid', $ykey);
            $criteria1->compare('classid', $cy);
            $criteria->mergeWith($criteria1, false);
        }
        $criteria->compare('stat', 20);
        $criteria->compare('childid', $this->getChildId());
        $criteria->select = 'weeknumber,yid,classid';
        $criteria->order = 'weeknumber asc';
        $weekmodel = NotesChild::model()->findAll($criteria);

        $weekf = array();
        foreach ($weekmodel as $week) {
            $weekf[$week->weeknumber]['yid'] = $week->yid;
            $weekf[$week->weeknumber]['classid'] = $week->classid;
            $weekf[$week->weeknumber]['schoolid'] = $csoh[$week->classid];
        }
        if (!$weeknum) {
            if ($weekf) {
                $weeknum = max(array_keys($weekf));
            } else {
                $weeknum = 0;
            }
        }
        $tweek = $weekf;
//        ksort($tweek);

        if (!$classid)
            $classid = isset($tweek[$weeknum]) ? $tweek[$weeknum]['classid'] : 0;

        $class_model = null;
        $weeks = array();
        $yid = 0;
        $schoolid = '';
        $sModel = null;
        if ($classid) {
            $class_model = IvyClass::model()->findByPk($classid);

            $schoolid = $class_model->schoolid;
            $yid = $class_model->yid;

            $weeks = CalendarWeek::weeks($yid);

            $sModel = Branch::model()->findByPk($schoolid);
        }
        //取当前周
//        $nochildreport = array();
        $diffweek = array();
        $sctp = array();

        if ($weeks) {
            $currentWeek = max(array_keys($weeks));
            if (!isset($tweek[$currentWeek])) {
                if (date('w', time()) < 6 && date('w', time()) != 0) {
                    unset($weeks[$currentWeek]);
                }
            }

            $diffweek = array_diff_key($weeks, $weekf);
        }

        if (count($curryear) > 1) {
            $journal = new Journal();
            $weekinfo = $journal->perfectWeekInfo($weekf, count($weeks));
            $retweek = $journal->removeRepetitive($weekinfo);

            $stp = array();
            $ctp = array();

            $criteria = new CDbCriteria;
            foreach ($retweek as $thekey => $cinfo) {
                $criteria1 = new CDbCriteria();
                $criteria1->compare('yid', $cinfo['yid']);
                $criteria1->compare('schoolid', $cinfo['schoolid']);
                $criteria->mergeWith($criteria1, false);
            }
            $criteria->compare('stat', 20);
            $criteria->compare('type', 10);
            $criteria->compare('classid', 0);
            $criteria->compare('weeknumber', array_keys($diffweek));
            $schreport = NotesSchCla::model()->findAll($criteria);
            foreach ($schreport as $rep) {
                $stp[$rep['weeknumber']] = $weekinfo[$rep['weeknumber']]['classid'];
            }

            $criteria = new CDbCriteria;
            foreach ($retweek as $thekey => $cinfo) {
                $criteria1 = new CDbCriteria();
                $criteria1->compare('yid', $cinfo['yid']);
                $criteria1->compare('classid', $cinfo['classid']);
                $criteria->mergeWith($criteria1, false);
            }
            $criteria->compare('stat', 20);
            $criteria->compare('type', 20);
            $criteria->compare('weeknumber', array_keys($diffweek));
            $clareport = NotesSchCla::model()->findAll($criteria);
            foreach ($clareport as $rep) {
                $ctp[$rep['weeknumber']] = $weekinfo[$rep['weeknumber']]['classid'];
            }

            $sctp = $stp + $ctp;
        } else {
            if ($weekf) {
                $tmpa = current($weekf);

                $stp = array();
                $ctp = array();

                $criteria = new CDbCriteria;
                $criteria->compare('schoolid', $tmpa['schoolid']);
                $criteria->compare('yid', $tmpa['yid']);
                $criteria->compare('stat', 20);
                $criteria->compare('type', 10);
                $criteria->compare('classid', 0);
                $criteria->compare('weeknumber', array_keys($diffweek));
                $schreport = NotesSchCla::model()->findAll($criteria);
                foreach ($schreport as $rep) {
                    $stp[$rep['weeknumber']] = $classid;
                }

                $criteria = new CDbCriteria;
                $criteria->compare('yid', $tmpa['yid']);
                $criteria->compare('stat', 20);
                $criteria->compare('type', 20);
                $criteria->compare('classid', $tmpa['classid']);
                $criteria->compare('weeknumber', array_keys($diffweek));
                $clareport = NotesSchCla::model()->findAll($criteria);
                foreach ($clareport as $rep) {
                    $ctp[$rep['weeknumber']] = $classid;
                }

                $sctp = $stp + $ctp;
            }
        }
        $this->render('journal', array(
            'class_model' => $class_model,
            'weeks' => $weeks,
            'weekNum' => $weeknum,
            'yId' => $yid,
            'schoolId' => $schoolid,
            'classId' => $classid,
            'classe' => $classe,
            'tweek' => $tweek,
            'feedback' => $feedback,
            'startyear' => $startyear,
            'classOfYear' => $classOfYear,
            'sctp' => $sctp,
            'sModel' => $sModel,
                )
        );
    }

    public function showAllFeedback() {

        Yii::import('common.models.feedback.Comments');
        Yii::import('common.models.calendar.*');
        Yii::import('common.models.classTeacher.InfopubStaffExtend');
        Yii::import('common.models.feedback.Comments');
        $childId = $this->getChildId();
        $currentClassId = $this->myChildObjs[$childId]->classid;
        $startYear = Yii::app()->getRequest()->getParam('startyear', 0);

        $comment_cri = new CDbCriteria();
        $comment_cri->compare('com_child_id', $childId);
        $comment_cri->compare('com_root_id', 0);
        $comment_cri->select = 'com_yid';
        $comment_cri->index = 'com_yid';
        $comment_cri->order = 'com_yid desc';

        $calendarInfo = Comments::model()->findAll($comment_cri);
        $calendarIds = null;
        if (!empty($calendarInfo)) {
            $calendarIds = array_keys($calendarInfo);
        }
        $calendarStartYear = array();
        $currentYids = null;
        if (!empty($calendarIds)) {

            $cs_cri = new CDbCriteria();
            $cs_cri->addInCondition('yid', $calendarIds);
            $cs_cri->order = 'yid DESC';
            $calendarSchoolInfo = CalendarSchool::model()->findAll($cs_cri);
            $yearYids = null;
            if (!empty($calendarSchoolInfo)) {
                foreach ($calendarSchoolInfo as $csi) {
                    $yearYids[$csi->startyear][$csi->yid] = $csi->yid;
                }
            }
            $calendarStartYear = array_keys($yearYids);
            if (empty($startYear)) {
                $startYear = max($calendarStartYear);
            }
            $currentYids = $yearYids[$startYear];
        }

        $comment_cri = new CDbCriteria();
        $comment_cri->compare('t.com_yid', $currentYids);
        $comment_cri->compare('t.com_child_id', $childId);
        $comment_cri->compare('t.com_root_id', 0);
        $comment_cri->order = 't.com_yid desc,t.com_week_num desc,t.id asc';

        $comment_list = Comments::model()->with('replies', 'userWithProfile', 'staffInfo')->findAll($comment_cri);
        $class_ids = array($currentClassId => $currentClassId);
        $week_nums = array();

        if (!empty($comment_list)) {
            foreach ($comment_list as $cc => $comment) {

                $class_ids[$comment->com_class_id] = $comment->com_class_id;
                if (isset($week_nums[$comment->com_week_num])) {
                    $comment_list[$cc]->showWeekInfo = false;
                } else {
                    $week_nums[$comment->com_week_num] = $comment->com_week_num;
                }
                // 用作 格式化后的 反馈时间
                $comment_list[$cc]->userName = $comment->userWithProfile->getName();
                $comment_list[$cc]->userPhoto = $comment->com_user_type == 1 ? Mims::CreateUploadUrl('infopub/staff/' . $comment->staffInfo->staff_photo) : $comment->userWithProfile->getPhotoSubUrl();
            }
        }
        $week_time_slot = CalendarWeek::model()->getWeekTimeSlot($currentYids, $week_nums);

        $classes = IvyClass::model()->with("calendarInfo")->findAllByPk(array_keys($class_ids), array("index" => "classid"));
        $this->render('journal_feedback', array(
            'comment_list' => $comment_list,
            'classes' => $classes,
            'week_time_slot' => $week_time_slot,
            'calendarStartYear' => $calendarStartYear,
            'startYear' => $startYear,
            'currentClassId' => $currentClassId,
        ));
    }

    public function updateRead($startYear = 0) {
        Yii::import('common.models.feedback.Comments');
        Yii::import('common.models.calendar.CalendarSchool');

        $yids = null;
        $cs_cri = new CDbCriteria();
        $cs_cri->addInCondition('startyear', $startYear);
        $cs_cri->order = 'yid DESC';
        $calendarSchoolInfo = CalendarSchool::model()->findAll($cs_cri);
        if (!empty($calendarSchoolInfo)) {
            foreach ($calendarSchoolInfo as $csi) {
                $yids[$csi->yid] = $csi->yid;
            }
        }
        $childid = $this->getChildid();

        $criteria = new CDbCriteria();
        $criteria->compare('com_ifread', array(0, 1, 2));
        $criteria->compare('com_child_id', $childid);
        if (!empty($yids)) {
            $criteria->compare('com_yid', $yids);
        }
        $comms = Comments::model()->findAll($criteria);

        $fid = $this->myChildObjs[$childid]->fid;
        $mid = $this->myChildObjs[$childid]->mid;

        if (Yii::app()->user->id == $fid) {
            $gender = 1;
        } else {
            $gender = 2;
        }

        foreach ($comms as $c) {
            $model = Comments::model()->findByPk($c->id);
            if ($model->com_ifread == 0) {
                $model->com_ifread = $gender;
            } elseif ($model->com_ifread == 1 && $gender == 2) {
                $model->com_ifread = 3;
            } elseif ($model->com_ifread == 2 && $gender == 1) {
                $model->com_ifread = 3;
            }
            $model->save();
        }
    }

    public function actionSemester() {
        $startyear = Yii::app()->getRequest()->getParam('startyear', null);
        $semester = Yii::app()->getRequest()->getParam('semester', null);

        $criteria = new CDbCriteria();
        $criteria->compare('t.childid', $this->getChildId());
        $criteria->compare('t.stat', 20);
        $criteria->compare('t.pdf_file', 1);
        $criteria->order = 't.semester ASC';
        $reports = SReport::model()->findAll($criteria);
        $reportList = array();
        if (!empty($reports)){
            foreach($reports as $_report){
                $classIds[$_report->classid] = $_report->classid;
                if(!isset($reportList[$_report->startyear])){
                    $reportList[$_report->startyear] = array();
                }
                $reportList[$_report->startyear][$_report->semester] = $_report->id;
            }
            //最近的学年排在前面
            krsort($reportList);
        }
        if($reportList):

            if(empty($startyear)) $startyear = max(array_keys($reportList));
            if(empty($semester)) $semester = max(array_keys($reportList[$startyear]));
            $reportId = $reportList[$startyear][$semester];

            $report = SReport::model()->with('items')->findByPk($reportId);
            Yii::import('common.models.classTeacher.*');
            Yii::import('common.components.teaching.*');
            $data = ContentFetcher::getSemesterReportData($report);
        endif;

        $terms = require (Yii::getPathOfAlias('common.components.semesterReport').'/learningDomain.php');
        $this->render('sreport', array(
            'reportList'    => $reportList,
            'reportid'      => $reportId,
            'startyear'     => $startyear,
            'semester'      => $semester,
            'terms'         => $terms,
            'data'          => $data,
            'report'        => $report
        ));
    }

    public function actionProgress() {
        Yii::import('common.models.reportCards.*');
        Yii::import('common.components.teaching.*');
        $startyear = Yii::app()->getRequest()->getParam('startyear', null);
        $criteria = new CDbCriteria();
        $criteria->compare('t.child_id', $this->getChildId());
        $criteria->compare('t.status', 1);
        $criteria->compare('t.pdf_file', 1);
        $reports = ReportsData::model()->findAll($criteria);
        $reportHtml = '';
        if (!empty($reports)){
            $reportsList = array();
            $reportClass = array();
            foreach ($reports as $report){
                $reportsList[$report->startyear][$report->id] = $report->id;
                $reportClass[$report->startyear] = $report->class_id;
            }
            if ($startyear===null){
                $startYearList = array_keys($reportsList);
                $startyear = max($startYearList);
                $classid = $reportClass[$startyear];
                $reportIds = $reportsList[$startyear];
            }else{
                $classid = $reportClass[$startyear];
                $reportIds = isset($reportsList[$startyear]) ? $reportsList[$startyear] : array();
            }
            if (count($reportIds)){
                $params = array(
                    'schoolid' =>$this->getSchoolId(),
                    'childid' => $this->getChildId(),
                    'classid' => $classid,
                    'id' => $reportIds,
                    'flag' => '',
                );
                $withToolBar = false;
                $downloadActionUrl = Yii::app()->createAbsoluteUrl(
                    '//mteaching/semester/downloadReportDs',
                    array('branchId'=>$this->getSchoolId()));
                $reportHtml =  ContentFetcher::getSemesterReportOfDs($params,
                    $withToolBar,
                    $downloadActionUrl
                );
            }
        }
        $this->render('progress', array(
            'reportHtml'    => $reportHtml,
            'reportsList'    => $reportsList,
            'startyear'    => $startyear,
        ));
    }


    //中学报告显示

    public function actionSecondaryProgress()
    {
        Yii::import('common.models.reportCards.*');
        Yii::import('common.components.teaching.*');
        Yii::import('common.models.secondary.*');
        $params['yid'] = $startyear = Yii::app()->getRequest()->getParam('startyear', null);
        $params['courseid'] = $semester = Yii::app()->getRequest()->getParam('semester', null);
        $params['id'] = $ckid = Yii::app()->getRequest()->getParam('ckid', null);
        $params['childid'] = $this->getChildId();
        $params['classid'] = $this->childInfo['classid'];
        $criteria = new CDbCriteria();
        $criteria->compare('t.childid', $this->getChildId());
        $criteria->compare('ivyclass.classtype', array('e6','e7','e8'));
        $ischild = ChildProfileBasic::model()->with('ivyclass')->find($criteria);
        $reportHtml = '';
        $reportsList = array();
        $calen = array();

        $crit = new CDbCriteria();
        $crit->compare('t.childid', $this->getChildId());
        $crit->compare('t.is_stat', 1);
        $crit->compare('t.is_cache', 1);
        $crit->order = "report.start_time desc";
        $child_report = AchievementReportChild::model()->with('report')->findAll($crit);

        $yid = array();
        $menu = array();
        if($child_report){
            foreach($child_report as $_yid){
                $menu[$_yid->report->calendar][$_yid->report_id]['title'] = $_yid->report->getTitle();
                $menu[$_yid->report->calendar][$_yid->report_id]['id'] = $_yid->id;
                $yid[] = $_yid->report->calendar;

            }
            $crit = new CDbCriteria();
            $crit->compare('yid', $yid);
            $crit->order = "startyear";
            $calenders = CalendarSchool::model()->findAll($crit);
            foreach($calenders as $_calender){
                $calen[$_calender->yid] = $_calender->startyear;
            }
        }

        if(empty($startyear)) $params['yid'] = max(array_keys($calen));
        if(empty($ckid)) {
            $params['id'] = max(array_keys($menu[$params['yid']]));
            $params['courseid'] = $menu[$params['yid']][$params['id']]['id'];
        }
        if($calen){
            $params['runJs'] = true;
            $reportHtml =  ContentFetcher::getReportOfDs($params,
                $this->childInfo['schoolid']
            );
        }

        Yii::app()->clientScript->registerScriptFile(Yii::app()->theme->baseUrl . '/js/tooltip_popover.js', CClientScript::POS_END);
        Yii::app()->clientScript->registerCssFile(Yii::app()->theme->baseUrl . '/css/popover.css');

        $this->render('progressSecondary', array(
            'reportHtml'    => $reportHtml,
            'reportsList'    => $reportsList,
            'calen'    => $calen,
            'menu'    => $menu,
            'params'    => $params,
        ));
    }

    public function actionDownloadReportDs()
    {
        Yii::import('common.models.secondary.*');
        $params = Yii::app()->request->getParam('params', null);
        $report = AchievementReportChild::model()->findByAttributes(array('id' => $params['courseid'], 'childid' => $this->getChildId()));
        Yii::import('common.models.reportCards.*');
        if($report){
            if ($report->is_cache == 1 && $report->cache_pdf) {
                $pdfUrl = CommonUtils::getPdfUrl($report->cache_pdf, 0 , true);
                echo '<script>location.href="'.$pdfUrl.'";setTimeout(function(){history.back()}, 10000);</script>';
            }
        }
    }


    public function actionSemester_bak() {
        $startyear = Yii::app()->getRequest()->getParam('startyear');
        $semester = Yii::app()->getRequest()->getParam('semester');
        $classid = Yii::app()->getRequest()->getParam('classid');

        Mims::LoadHelper('HtoolKits');

        Yii::import('common.models.classTeacher.ClassTeacher');

        if ($classid) {
            $classm = IvyClass::model()->with('calendarInfo')->findByPk($classid);
            $startyear = $classm->calendarInfo->startyear;
        }

        $criteria = new CDbCriteria();
        $criteria->compare('t.childid', $this->getChildId());
        $criteria->compare('t.stat', 20);
        $criteria->order = 't.semester ASC';
        $model = SemesterReport::model()->with('classInfo', 'schoolInfo', 'yInfo', 'extInfo')->findAll($criteria);

        $mod = array();
        $pids = array();
        foreach ($model as $m) {
            $mod[$m->yInfo->startyear][$m->semester] = $m;
            $pids[$m->pre_cover] = $m->pre_cover;
            $pids[$m->suf_cover] = $m->suf_cover;
            foreach ($m->extInfo as $ext) {
                $pids[$ext->pid] = $ext->pid;
            }
        }

        $classteacher = array();
        $pitems = array();

        if ($mod):
            if (!$startyear)
                $startyear = max(array_keys($mod));

            if (isset($mod[$startyear])):
                if (!$semester)
                    $semester = max(array_keys($mod[$startyear]));

                $criteria = new CDbCriteria();
                $criteria->compare('classid', $mod[$startyear][$semester]->classid);
                $criteria->order = 't.weight asc';
                $classteacher = ClassTeacher::model()->with('userWithProfile')->findAll($criteria);

                $CalendarModel = Calendar::model()->findByPk($mod[$startyear][$semester]->yid);
                ChildMedia::setStartYear($CalendarModel->startyear);

                $criteria = new CDbCriteria();
                $criteria->compare('id', $pids);
                $pmodel = ChildMedia::model()->findAll($criteria);
                foreach ($pmodel as $p) {
                    $pitems[$p->id] = $p->getMediaUrl();
                }
            endif;
        endif;

        $terms = require (Yii::getPathOfAlias('common.components.semesterReport').'/learningDomain.php');

        $this->render('semester', array('mod' => $mod, 'startyear' => $startyear, 'semester' => $semester, 'classteacher' => $classteacher, 'pitems' => $pitems, 'terms'=>$terms));
    }

    public function actionProject() {
        Mims::LoadHelper('HtoolKits');

        $classid = Yii::app()->getRequest()->getParam('classid');

        $class_model = IvyClass::model()->findByPk($classid);

        $schoolid = $class_model->schoolid;
        $yid = $class_model->yid;

        $criteria = new CDbCriteria();
        $criteria->compare('classid', $classid);
        $criteria->compare('stat', 20);
        $criteria->compare('yid', $yid);
        $criteria->compare('childid', $this->childid);
        $NoteMiiModel = NotesMii::model()->with('projectInfo')->findAll($criteria);

        $this->render('project', array('NoteMiiModel' => $NoteMiiModel, 'classid' => $classid));
    }

    public function actionProjectView() {
        Mims::LoadHelper('HtoolKits');

        $classid = Yii::app()->getRequest()->getParam('classid');
        $pid = Yii::app()->getRequest()->getParam('pid');

        Yii::import('common.models.schedule.ClassSchedule');

        $class_model = IvyClass::model()->findByPk($classid);

        $schoolid = $class_model->schoolid;
        $yid = $class_model->yid;

        $ProjectModel = CurProjects::model()->findByPk($pid);

        $criteria = new CDbCriteria();
        $criteria->compare('schoolid', $schoolid);
        $criteria->compare('classid', $classid);
        $criteria->compare('stat', 20);
        $criteria->compare('yid', $yid);
        $criteria->compare('pid', $pid);
        $criteria->compare('childid', 0);
        $ClassMii = NotesMii::model()->find($criteria);

        $criteria = new CDbCriteria();
        $criteria->compare('childid', $this->childid);
        $criteria->compare('classid', $classid);
        $criteria->compare('stat', 20);
        $criteria->compare('yid', $yid);
        $criteria->compare('pid', $pid);
        $ChildMii = NotesMii::model()->find($criteria);

        $criteria = new CDbCriteria();
        $criteria->distinct = true;
        $criteria->select = 'classid, weeknumber, activity_id';
        $criteria->compare('classid', $classid);
        $criteria->compare('activity_id', '<>0');
        $ScheduleModel = ClassSchedule::model()->findAll($criteria);

        $aids = array();
        $learning = "";
        $intelligence = "";

        $activitytit = array();
        $intelligence_arr = array();
        $learning_arr = array();
        $learnings = array();
        $intelligences = array();
        $_learnings = array();
        $_intelligences = array();
        $lid_aid_arr = array();
        $iid_aid_arr = array();

        if ($ClassMii) {
            $ud = $ClassMii->ext1 ? unserialize($ClassMii->ext1) : array();
            foreach ($ud["learning"] as $lg) {
                if ($lg) {
                    $learning .= $lg . ",";
                    $_learnings[$lg] = array();
                }
            }
            foreach ($ud["intelligence"] as $in) {
                if ($in) {
                    $intelligence .= $in . ",";
                    $_intelligences[$in] = array();
                }
            }
        }

        foreach ($ScheduleModel as $schedule) {
            $aids[$schedule->activity_id] = $schedule->activity_id;
        }

        $criteria = new CDbCriteria();
        $criteria->compare('aid', $aids);
        $criteria->compare('pid', $pid);
        $activitys = CurActivityInfo::model()->findAll($criteria);
        foreach ($activitys as $act) {
            $learning .= $act->learning ? $act->learning . "," : "";
            $intelligence .= $act->intelligence ? $act->intelligence . "," : "";

            $activitytit[$act->aid] = HtoolKits::autoLang($act->cn_title, $act->en_title);

            $learnings[$act->aid] = explode(",", $act->learning);
            $intelligences[$act->aid] = explode(",", $act->intelligence);
        }

        $learning = substr($learning, 0, (strlen($learning) - 1));
        $intelligence = substr($intelligence, 0, (strlen($intelligence) - 1));

        if ($learning) {
            $criteria = new CDbCriteria();
            $criteria->compare('diglossia_id', array_unique(explode(",", $learning)));
            $diglossia_lea = Term::model()->findAll($criteria);
            foreach ($diglossia_lea as $dig) {
                $learning_arr[$dig->diglossia_id] = HtoolKits::autoLang($dig->cntitle, $dig->entitle);

                foreach (array_keys($learnings) as $val) {
                    if (in_array($dig->diglossia_id, $learnings[$val])) {
                        $lid_aid_arr[$dig->diglossia_id][] = $val;
                    }
                }
            }
        }
        $lid_aid_arr = $lid_aid_arr + $_learnings;

        if ($intelligence) {
            $criteria = new CDbCriteria();
            $criteria->compare('diglossia_id', array_unique(explode(",", $intelligence)));
            $diglossia_lea = Term::model()->findAll($criteria);
            foreach ($diglossia_lea as $dig) {
                $intelligence_arr[$dig->diglossia_id] = HtoolKits::autoLang($dig->cntitle, $dig->entitle);

                foreach (array_keys($intelligences) as $val) {
                    if (in_array($dig->diglossia_id, $intelligences[$val])) {
                        $iid_aid_arr[$dig->diglossia_id][] = $val;
                    }
                }
            }
        }
        $iid_aid_arr = $iid_aid_arr + $_intelligences;

        $CalendarModel = Calendar::model()->findByPk($yid);
        ChildMedia::setStartYear($CalendarModel->startyear);
        ChildMediaLinks::setStartYear($CalendarModel->startyear);

        $criteria = new CDbCriteria();
        $criteria->compare('t.classid', $classid);
        $criteria->compare('t.yid', $yid);
        $criteria->compare('t.itemid', $pid);
        $criteria->compare('t.childid', array(0, $this->childid));
        $criteria->compare('t.category', 'activity');
        $criteria->order = 't.id asc';
        $CCMedia = ChildMediaLinks::model()->with('photoInfo')->findAll($criteria);

        $_savedImgs_class = array();
        $_savedImgs_child = array();

        foreach ($CCMedia as $m) {
            if (!$m->childid) {
                $_savedImgs_class[$m->id] = array(
                    'url' => $m->getMediaUrl(),
                    'thumb' => $m->getMediaUrl(true),
                    'txt' => $m->content,
                );
            } else {
                $_savedImgs_child[$m->id] = array(
                    'url' => $m->getMediaUrl(),
                    'thumb' => $m->getMediaUrl(true),
                    'txt' => $m->content,
                );
            }
        }

        $this->render('projectView', array(
            'ProjectModel' => $ProjectModel,
            'ClassMii' => $ClassMii,
            'ChildMii' => $ChildMii,
            'learning_arr' => $learning_arr,
            'lid_aid_arr' => $lid_aid_arr,
            'intelligence_arr' => $intelligence_arr,
            'iid_aid_arr' => $iid_aid_arr,
            'activitytit' => $activitytit,
            '_savedImgs_class' => $_savedImgs_class,
            '_savedImgs_child' => $_savedImgs_child,
                )
        );
    }

    public function actionCalendarView() {
        $schoolid = Yii::app()->getRequest()->getParam('schoolid');
        $yid = Yii::app()->getRequest()->getParam('yid');

        $abb = Branch::model()->findByPk($schoolid)->abb;
        $startyear = Calendar::model()->findByPk($yid)->startyear;

        $this->renderPartial("calendarView", array('abb' => $abb, 'startyear' => $startyear));
    }

    public function actionSchedule() {
        $classid = Yii::app()->getRequest()->getParam('classid');
        $weeknum = Yii::app()->getRequest()->getParam('weeknum');
        $this->renderPartial("schedule", array('classid' => $classid, 'weeknum' => $weeknum), false, true);
    }

    public function actionLunch() {
        $schoolId = Yii::app()->getRequest()->getParam('schoolid');
        $yId = Yii::app()->getRequest()->getParam('yid');
        $weekNum = Yii::app()->getRequest()->getParam('weeknum');

        $this->renderPartial("lunch", array('schoolId' => $schoolId, 'yId' => $yId, 'weekNum' => $weekNum), false, true);
    }

    public function actionTeachers() {
        $classid = Yii::app()->getRequest()->getParam('classid');

        $this->renderPartial("teachers", array('classId' => $classid), false, true);
    }

    public function actionContact() {
        $classid = Yii::app()->getRequest()->getParam('classid');

        $this->renderPartial("contact", array('classId' => $classid), false, true);
    }

    public function actionReport() {
        $classid = Yii::app()->getRequest()->getParam('classid');

        $model = array();
        if ($classid) {
            $class_model = IvyClass::model()->findByPk($classid);

            $schoolid = $class_model->schoolid;
            $yid = $class_model->yid;

            $criteria = new CDbCriteria();
            $criteria->compare('classid', $classid);
            $criteria->compare('yid', $yid);
            $criteria->compare('childid', $this->childid);
            $criteria->compare('stat', 20);
            $criteria->order = 'semester ASC';
            $model = SemesterReport::model()->findAll($criteria);
        }
        $this->renderPartial("report", array('model' => $model), false, true);
    }

    public function actionActivityView() {
        $id = Yii::app()->getRequest()->getParam('id');

        Mims::LoadHelper('HtoolKits');

        $ages = Term::model()->getAgeList();
        $mis = Term::model()->getMiList();
        $lds = Term::model()->getLdList();
        $culturals = Term::model()->getCulturalList();

        $cfgs = Mims::LoadConfig('CfgSeason');

        $model = CurActivityInfo::model()->findByPk($id);

        $activity_arr = array();
        $activity_arr['title'] = HtoolKits::autoLang($model->cn_title, $model->en_title);
        $activity_arr['memo'] = HtoolKits::autoLang($model->cn_memo, $model->en_memo);
        $activity_arr['season'] = $cfgs['season'][$model->season];
        $activity_arr['type'] = $cfgs['type'][$model->activity_type];
        $activity_arr['intelligence'] = explode(',', $model->intelligence);
        $activity_arr['age'] = explode(',', $model->age);
        $activity_arr['learning'] = explode(',', $model->learning);
        $activity_arr['cultural'] = explode(',', $model->cultural);
        $activity_arr['learning_obj'] = HtoolKits::autoLang($model->cn_learning_obj, $model->en_learning_obj);
        $activity_arr['materials_content'] = HtoolKits::autoLang($model->materials_cn_content, $model->materials_en_content);
        $activity_arr['preparation_content'] = HtoolKits::autoLang($model->preparation_cn_content, $model->preparation_en_content);
        $activity_arr['tips_content'] = HtoolKits::autoLang($model->tips_cn_content, $model->tips_en_content);
        $activity_arr['presentation_content'] = HtoolKits::autoLang($model->presentation_cn_content, $model->presentation_en_content);
        $activity_arr['extension_content'] = HtoolKits::autoLang($model->extension_cn_content, $model->extension_en_content);

        $this->renderPartial("activityview", array('activity_arr' => $activity_arr, 'ages' => $ages, 'mis' => $mis, 'lds' => $lds, 'culturals' => $culturals, 'cfgs' => $cfgs), false, true);
    }

    public function actionSaveFeedback() {
        $this->widget('application.extensions.feedback.FeedbackSave', array(
            'returnFeedback' => true
                )
        );
    }

    public function actionGetComments() {
        $classid = Yii::app()->getRequest()->getParam('classid');
        $weeknum = Yii::app()->getRequest()->getParam('weeknum');

        $this->renderPartial('comments', array('classId' => $classid, 'weekNum' => $weeknum));
    }

    public function actionSemesterPDF() {
        Yii::import('common.models.reportCards.*');
        Yii::import('common.components.teaching.*');

        $id = Yii::app()->request->getParam('id', 0);
        $report = ReportsData::model()->findByAttributes(array('id'=>$id, 'child_id'=>$this->getChildId()));
        if ($report && $report->custom_pdf && $report->pdf_file) {
            $pdfUrl = CommonUtils::getPdfUrl($report->custom_pdf, $report->custom, true);
            echo '<script>location.href="'.$pdfUrl.'";setTimeout(function(){history.back()}, 10000);</script>';
        }
    }

    public function actionSemesterPDF2() {
        Yii::import('common.components.teaching.*');
        $id = Yii::app()->request->getParam('id', 0);

        $report = SReport::model()->findByAttributes(array('id'=>$id, 'childid'=>$this->getChildId()));
        if($report && $report->stat == 20){
            $pdfUrl = CommonUtils::getPdfUrl($report->custom_pdf, $report->custom);
            echo '<script>location.href="'.$pdfUrl.'";setTimeout(function(){history.back()}, 10000);</script>';
        }
    }

    /*
    public function actionSemesterPDF_bak() {
        $test = Yii::app()->request->getParam("test", 0);
        if ($test) {
            $file = Yii::app()->params['xoopsVarPath'] . '/semester_report/custom_pdf/el_2011_2012_spring/Nursery/Evan.pdf';
            header('Content-Type: application/pdf');
            readfile($file);
            Yii::app()->end();
        }
        $id = Yii::app()->getRequest()->getParam('id', '0');
        $f = Yii::app()->getRequest()->getParam('f', 0);
        $semester = SemesterReport::model()->findByPk($id);

        $xoops_var_path = Yii::app()->params['xoopsVarPath'];

        if ($this->getChildId() == $semester->childid):
            if ($semester->stat == 20) {
                if ($semester->custom) {
                    header("Content-type: application/pdf");
                    if (!$f)
                        header("Content-Disposition: attachment; filename=" . $semester->custom_pdf);
                    readfile($xoops_var_path . '/semester_report/custom_pdf/' . $semester->custom_pdf);
                }
                else {
                    if (!file_exists($xoops_var_path . '/semester_report/c_' . $semester->classid . '/' . $semester->childid . "_" . $semester->semester . ".pdf")) {
                        echo Yii::t("portfolio", 'Report is being generated, please come back later.');
                        Yii::app()->end();
                    }
                    header("Content-type: application/pdf");
                    header("Content-Disposition: attachment; filename=" . $semester->childid . "_" . $semester->semester . ".pdf");
                    readfile($xoops_var_path . '/semester_report/c_' . $semester->classid . '/' . $semester->childid . "_" . $semester->semester . ".pdf");
                }
            }
        endif;
    }*/

    public function actionGallery() {
        $this->layout = "//layouts/column2_child_portfolio_gallery";
        Yii::app()->params['hideShortCuts'] = true;
        $startyear = Yii::app()->getRequest()->getParam('startyear', 0);
        $albumid = Yii::app()->getRequest()->getParam('albumid', 0);
        $type = Yii::app()->getRequest()->getParam('type', '');
        $index = Yii::app()->getRequest()->getParam('index', 0);
        $childid = $this->getChildId();

        $sems = array();
        $classes = array();
        $albums = array();
        $pics = array();
        $nurl = '';
        $allcalendar = array();

        $criteria = new CDbCriteria;
        $criteria->compare('childid', $childid);
        $criteria->compare('stat', 20);
        $criteria->select = 'yid';
        $criteria->distinct = true;
        $allyears = NotesChild::model()->findAll($criteria);
        $allyids = array();
        foreach ($allyears as $year) {
            $allyids[$year->yid] = $year->yid;
        }
        if ($allyids) {
            $criteria = new CDbCriteria;
            $criteria->compare('yid', $allyids);
            $calendars = Calendar::model()->findAll($criteria);
            foreach ($calendars as $cal) {
                $allcalendar[$cal->startyear][$cal->yid] = $cal->yid;
            }
            krsort($allcalendar);
            if (!$startyear) {
                $startyear = max(array_keys($allcalendar));
            }
            if (!$type) {
                $type = 'journal';
            }
            if (!$albumid) {
                $albumid = 0;
            }

            $yids = $allcalendar[$startyear];

            $criteria = new CDbCriteria;
            $criteria->compare('childid', $childid);
            $criteria->compare('stat', 20);
            $criteria->compare('yid', $yids);
            $criteria->select = 'weeknumber,classid';
            $weeklist = NotesChild::model()->findAll($criteria);
            $criteria->order = 'weeknumber asc';
            foreach ($weeklist as $week) {
                $weeks[$week->weeknumber] = $week->weeknumber;
                $classes[$week->classid][$week->weeknumber] = $week->weeknumber;
            }

            $albums['weelyjournal'] = array_chunk($weeks, 10, true);

            foreach ($albums['weelyjournal'] as $ak => $album) {
                $kalbum = array_keys($album);
                $albums['weelyjournal'][$ak] = current($kalbum) . '-' . end($kalbum);
            }

            $criteria = new CDbCriteria();
            $criteria->compare('t.childid', $childid);
            $criteria->compare('t.stat', 20);
            $criteria->compare('t.custom', 0);
            $criteria->order = 't.semester ASC';
            $semsters = SemesterReport::model()->with('extInfo', 'yInfo')->findAll($criteria);

            foreach ($semsters as $semester) {
                $sems[$semester->yInfo->startyear][$semester->pre_cover] = '';
                $sems[$semester->yInfo->startyear][$semester->suf_cover] = '';
                foreach ($semester->extInfo as $ext) {
                    $sems[$semester->yInfo->startyear][$ext->pid] = $ext->content;
                }
            }
            if (isset($sems[$startyear])) {
                $albums['semsterreport'] = $startyear;
            }

            $criteria = new CDbCriteria();
            foreach ($classes as $claid => $weid) {
                $criteria1 = new CDbCriteria();
                $criteria1->compare('classid', $claid);
                $criteria1->compare('weeknum', $weid);
                $criteria->mergeWith($criteria1, false);
            }
            $weekun = explode('-', $albums['weelyjournal'][$albumid]);
            ChildMediaLinks::setStartYear($startyear);

            $criteria->compare('childid', array(0, $childid));
            $criteria->compare('category', 'week');
            $criteria->compare('weeknum', '>=' . $weekun[0]);
            $criteria->compare('weeknum', '<=' . $weekun[1]);
            $jpics = ChildMediaLinks::model()->findAll($criteria);

            if ($type == 'journal') {
                foreach ($jpics as $pic) {
                    $pics[$pic['pid']] = $pic['content'];
                }
            } else {
                $pics = isset($sems[$startyear]) ? $sems[$startyear] : array();
            }

            if (isset($albums['weelyjournal'][$albumid + 1])) {
                $nurl = Yii::app()->getController()->createUrl('//portfolio/portfolio/gallery', array('startyear' => $startyear, 'albumid' => $albumid + 1));
            } elseif (isset($sems[$startyear])) {
                $nurl = Yii::app()->getController()->createUrl('//portfolio/portfolio/gallery', array('startyear' => $startyear, 'type' => 'semester'));
            }
            if ($type == 'semester')
                $nurl = '';
        }
        Yii::app()->clientScript->registerCoreScript('jquery');
        $this->render('gallery', array(
            'classes' => $classes,
            'sems' => $sems,
            'type' => $type,
            'pics' => $pics,
            '_startyear' => $startyear,
            'albums' => $albums,
            'albumid' => $albumid,
            'nurl' => $nurl,
            'index' => $index,
        ));
    }

    public function actionGalleryList() {
        $startyear = Yii::app()->getRequest()->getParam('startyear', 0);
        $albumid = Yii::app()->getRequest()->getParam('albumid', 0);
        $type = Yii::app()->getRequest()->getParam('type', '');
        $childid = $this->getChildId();

        $sems = array();
        $classes = array();
        $albums = array();
        $pics = array();
        $yids = array();
        $nurl = '';
        $allcalendar = array();

        $criteria = new CDbCriteria;
        $criteria->compare('childid', $childid);
        $criteria->compare('stat', 20);
        $criteria->select = 'yid';
        $criteria->distinct = true;
        $allyears = NotesChild::model()->findAll($criteria); //得到孩子所有上线周报告学年的ID
        $allyids = array();
        foreach ($allyears as $year) {
            $allyids[$year->yid] = $year->yid;
        }
        if ($allyids) {
            $criteria = new CDbCriteria;
            $criteria->compare('yid', $allyids);
            $calendars = Calendar::model()->findAll($criteria); //根据所有学年ID得到开始年
            foreach ($calendars as $cal) {
                $allcalendar[$cal->startyear][$cal->yid] = $cal->yid; //开始年为主键创建数组
            }
            krsort($allcalendar); //根据开始年倒序
            if (!$startyear) {
                $startyear = max(array_keys($allcalendar)); //默认最大开始年
            }
            if (!$type) {
                $type = 'journal'; //默认周报告照片
            }
            if (!$albumid) {
                $albumid = 0; //默认第一个相册开始
            }

            $yids = $allcalendar[$startyear]; //当前浏览学年内的所有yid，一般情况只有一个yid，转学后可能会出现多个yid

            $criteria = new CDbCriteria;
            $criteria->compare('childid', $childid);
            $criteria->compare('stat', 20);
            $criteria->compare('yid', $yids);
            $criteria->select = 'weeknumber,classid,pids';
            $criteria->order = 'weeknumber asc';
            $weeklist = NotesChild::model()->findAll($criteria); //得到一年内所有上线的周报告，包括转学后的
            foreach ($weeklist as $week) {
                if(!empty($week->pids))
                    $weeks[$week->weeknumber] = $week->weeknumber; //所有上线的周号数组
                $classes[$week->classid][$week->weeknumber] = $week->weeknumber; //以班级ID为主键的周号数组
            }

            $albums['weelyjournal'] = array_chunk($weeks, 10, true); //所有上线的周报告10个一组分成多个相册

            foreach ($albums['weelyjournal'] as $ak => $album) {
                $kalbum = array_keys($album);
                $albums['weelyjournal'][$ak] = current($kalbum) . '-' . end($kalbum);
            }

            $criteria = new CDbCriteria();
            $criteria->compare('t.childid', $childid);
            $criteria->compare('t.stat', 20);
            $criteria->compare('t.custom', 0);
            $criteria->order = 't.semester ASC';
            $semsters = SemesterReport::model()->with('extInfo', 'yInfo')->findAll($criteria); //查询学期报告照片

            foreach ($semsters as $semester) {
                $sems[$semester->yInfo->startyear][$semester->pre_cover] = '';
                $sems[$semester->yInfo->startyear][$semester->suf_cover] = '';
                foreach ($semester->extInfo as $ext) {
                    $sems[$semester->yInfo->startyear][$ext->pid] = $ext->content;
                }
            }
            if (isset($sems[$startyear])) {
                $albums['semsterreport'] = $startyear; //学期报告照片为一个相册
            }

            $criteria = new CDbCriteria();
            foreach ($classes as $claid => $weid) {
                $criteria1 = new CDbCriteria();
                $criteria1->compare('classid', $claid);
                $criteria1->compare('weeknum', $weid);
                $criteria->mergeWith($criteria1, false); // 通过次条件查出不同学校、不同班级内的所有照片
            }
            $weekun = explode('-', $albums['weelyjournal'][$albumid]);
            ChildMediaLinks::setStartYear($startyear);

            $criteria->compare('childid', array(0, $childid));
            $criteria->compare('category', 'week');
            $criteria->compare('weeknum', '>=' . $weekun[0]);
            $criteria->compare('weeknum', '<=' . $weekun[1]);
            $jpics = ChildMediaLinks::model()->findAll($criteria); //查询当前周报告相册中所有照片

            if ($type == 'journal') {
                foreach ($jpics as $pic) {
                    $pics[$pic['pid']] = $pic['content']; //周报告图片配注释
                }
            } else {
                $pics = isset($sems[$startyear]) ? $sems[$startyear] : array(); //学期报告图片
            }

            if (isset($albums['weelyjournal'][$albumid + 1])) { //下一个周报告相册是否存在
                $nurl = Yii::app()->getController()->createUrl('//portfolio/portfolio/gallerylist', array('startyear' => $startyear, 'albumid' => $albumid + 1));
            } elseif (isset($sems[$startyear])) { //下一个相册是否为学期报告相册
                $nurl = Yii::app()->getController()->createUrl('//portfolio/portfolio/gallerylist', array('startyear' => $startyear, 'type' => 'semester'));
            }
            if ($type == 'semester') //如果当前是学期报告相册，下面一定没有相册
                $nurl = '';
        }
        $this->render('gallerylist', array(
            'sems' => $sems,
            'type' => $type,
            'pics' => $pics,
            '_startyear' => $startyear,
            'albums' => $albums,
            'albumid' => $albumid,
            'nurl' => $nurl,
            'allcalendar' => $allcalendar,
        ));
    }


    /**
     * 批量下载周报告照片
     */
    public function actionDownload() {
        $childid = $this->getChildId();
        if(0 && !in_array($childid, array(1392,3954))) {
            $this->render('downloadClosed');
            Yii::app()->end();
        }
        Yii::import('common.components.general.*');
        $ranges = CommonContentFetcher::getChildNoteWeekRange($childid);
        $criteria = new CDbCriteria();
        $criteria->compare('childid', $childid);
        $criteria->compare('sweek', '<>0');
        $criteria->order ='sweek ASC';
        $tps = TaskPackpic::model()->findAll($criteria);
        $packages = array();
        $tmpMaxWeeks = array();
        foreach ($tps as $tp) {
            $tmpMaxWeeks[$tp->startyear] = $tp->eweek;
            $packages[$tp->startyear][$tp->sweek] = array(
                's' => $tp->sweek,
                'e' => $tp->eweek,
                'label' => ($tp->sweek == $tp->eweek) ?
                        Yii::t("portfolio","Week :weeknumber", array(":weeknumber"=>$tp->sweek)) :
                        Yii::t("portfolio","Week :weeknumber1 - Week :weeknumber2",
                            array(":weeknumber1"=>$tp->sweek, ":weeknumber2"=>$tp->eweek)),
                'schoolid' => $tp->schoolid,
                'dlpath' => $tp->dlpath ? $tp->dlpath : '',
                'pcount' => $tp->pcount,
                'pack_time' => $tp->pack_time,
                'zipurl' => $tp->zipurl,
                'uploaded_at' => $tp->uploaded_at,
            );
        }

        foreach($ranges as $startyear=>$range){
            $newStart = $tmpMaxWeeks[$startyear] + 1;
            if($range['max'] >= $newStart) {
                $packages[$startyear][$newStart] = array(
                    's' => $newStart,
                    'e' => $range['max']
                );
                $packages[$startyear][$newStart]['label'] = ( $range['max'] == $newStart ) ?
                    Yii::t("portfolio","Week :weeknumber", array(":weeknumber"=>$newStart)) :
                    Yii::t("portfolio","Week :weeknumber1 - Week :weeknumber2",
                        array(":weeknumber1"=>$newStart, ":weeknumber2"=>$range['max']));
            }
            krsort($packages[$startyear]);
        }

        krsort($ranges);
        $this->render('download/index', array('ranges'=>$ranges, 'packages'=>$packages));
    }


    /**
     * 处理照片打包请求，进入任务队列，任务靠定时运行触发
     */
    public function actionPackpic() {
        $batchNum = 100;
        if (Yii::app()->user->checkAccess('adminChild', array("childid" => $this->childid))) :
            $startyear = Yii::app()->getRequest()->getParam('startyear', 0);
            $sweek = Yii::app()->getRequest()->getParam('sweek', 0);
            $eweek = Yii::app()->getRequest()->getParam('eweek', 0);
            $childid = $this->getChildId();

            $taskModel = TaskPackpic::model()->findByAttributes(array(
                'childid' => $childid,
                'startyear' => $startyear,
                'sweek' => $sweek,
                'eweek' => $eweek
            ));

            if(empty($taskModel))
                $taskModel = new TaskPackpic();

            Mims::LoadHelper('HtoolKits');

            $userid = Yii::app()->user->getId();

            $usermodel = User::model()->findByPk($userid);

            $taskModel->childid = $childid;
            $taskModel->startyear = $startyear;
            $taskModel->sweek = $sweek;
            $taskModel->eweek = $eweek;
            $taskModel->pid = $userid;
            $taskModel->pemail = $usermodel->email;
            $taskModel->create_time = time();
            $taskModel->run_next = 1;
            if ($taskModel->save()) {
                echo CJSON::encode(array('state'=>'success'));
            } else {
                echo CJSON::encode(array(
                    'state' => 'fail',
                    'message' => print_r($taskModel->getErrors(),true)
                ));
            }
        else:
            echo CJSON::encode(array('state'=>'fail'));
        endif;

        Yii::app()->end();
    }

    /**
     * 获取下载地址
     * @param $dlpath
     */
    public function actionGetfile($dlpath) {
        if (Yii::app()->user->checkAccess('adminChild', array("childid" => $this->childid))) {

            $taskModel = TaskPackpic::model()->findByAttributes(array(
                'dlpath' => $dlpath,
                'childid' => $this->childid,
            ));

            if($taskModel) {
                $oss = CommonUtils::initOSS('private');
                $ossObj = $oss->get_object($dlpath);
                if ( $ossObj->status == 200) {
                    header("Content-Type: application/force-download");
                    header("Content-Transfer-Encoding: binary");
                    header('Content-Type: application/zip');
                    header(sprintf("content-length: %s", $ossObj->header['content-length']));
                    header('Content-Disposition: attachment; filename=' . end(explode('/', $dlpath)));

                    $bytes = $ossObj->body;
                    echo $bytes;
                } else {
                    die('cannot find file on OSS');
                }

            } else {
                echo "NO PERMISSION";
            }


        } else {
            echo "NO PERMISSION";
        }
    }

    public function getChildClassid() {
        $criteria = new CDbCriteria();
        $criteria->compare('childid', $this->getChildId());
        $criteria->compare('stat', 20);
        $childclass = NotesChild::model()->findAll($criteria);
        $classids = array();
        foreach ($childclass as $cc) {
            $classids[$cc->classid] = $cc->classid;
        }
        $currentcid = $this->myChildObjs[$this->getChildId()]->classid;
        $classids[$currentcid] = $currentcid;
        return $classids;
    }

    public function actionTeachingContent($classid=0, $yid=0, $weeknum=0, $month='')
    {
        $this->renderPartial("teachingcontent", array('classid' => $classid, 'yid' => $yid, 'weeknum' => $weeknum, 'month'=>$month), false, true);
    }
    
    public function actionGetSchedule(){
        $classId = Yii::app()->request->getParam('classid',0);
        $yId = Yii::app()->request->getParam('yid',0);
        if ($classId && $yId){
            $this->renderPartial("schedule_view", array('yId' => $yId, 'classId' => $classId), false, true);
        }
    }
}
