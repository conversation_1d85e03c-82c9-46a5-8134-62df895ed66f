<?php

/**
 * This is the model class for table "ivy_cash_handover_link".
 *
 * The followings are the available columns in table 'ivy_cash_handover_link':
 * @property integer $id
 * @property integer $handover_id
 * @property integer $invoice_id
 * @property integer $transaction_id
 */
class CashHandoverLink extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CashHandoverLink the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_cash_handover_link';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('handover_id, invoice_id, transaction_id', 'required'),
			array('handover_id, invoice_id, transaction_id', 'numerical', 'integerOnly'=>true),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, handover_id, invoice_id, transaction_id', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'transaction'=>array(self::BELONGS_TO,'InvoiceTransaction','transaction_id'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'handover_id' => 'Handover',
			'invoice_id' => 'Invoice',
			'transaction_id' => 'Transaction',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('handover_id',$this->handover_id);
		$criteria->compare('invoice_id',$this->invoice_id);
		$criteria->compare('transaction_id',$this->transaction_id);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}