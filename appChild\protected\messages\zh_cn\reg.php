<?php
return array(
    'School Bus Introduction & Policies' => '班车介绍及政策',
    "Introduction of Daystar's School Bus Service" => "总体介绍",
    'Bus Agreement' => '班车协议',
    'Name' => '姓名',
    'Relation' => '关系',
    'Tel' => '电话',
    'Photo' => '照片',
    'Submit' => '提交',
    'Daystar Academy Pick-up Information' => '启明星接送信息',
    'Two-way journey' => '双程乘坐',
    'One-way journey TO SCHOOL' => '早上乘坐',
    'One-way journey BACK HOME' => '下午乘坐',
    'Walk home from bus stop by themselves' => '下班车后自己回家',
    'To pick up students from school or from the bus stop, parents must have their parent passes with their child’s photo. By showing this card to the guards, parents can enter the campus, and if the card is forgotten, parents may ask for a temporary Visitor Pass. Please upload a 1 inch sized photo of all people in your family who may drop off/pickup your child (e.g. Mum, Dad, <PERSON><PERSON>, Driver, Grandma etc). You must upload a minimum of 1 photo. A maximum of 4 photos may be uploaded.' => '家长必须佩戴有学生照片的接送卡从学校或者班车站接送孩子，此接送卡也是您进入校园的通行证。来学校如忘记带接送卡，门卫将给家长一个临时访客卡。请您上传所有接送人的照片（如爸爸、妈妈、阿姨、司机、祖父母等），请使用1寸照片。至少一人，最多四人。',
    'Only students who are in Grade 3 or above can go home by themselves. Kindergarten to Grade 2 students must be met at the bus stop by a parent or caregiver. They may go home with a sibling who is in Grade 3 or above. If parents allow their child to walk home from the bus by themselves, the school is not responsible for the safety of the child once they have got off the bus.' => '只有3年级及以上的学生才可以选择下车后自己回家。幼儿园至小学2年级的学生必须由家长或其他监护人接；或者可以跟3年级及以上的哥哥姐姐一起回家。如果家长选择班车到站后学生自己步行回家，学生离开班车以后，学校对学生的安全不负任何责任。',
    'Daystar students are expected to wear a school uniform. We have a variety of different uniforms to suit boys and girls of all ages for different seasons. We have long & short sleeve collared cotton polo-shirts, girls dresses which all come in red, green and blue. We also have skorts (skirts that are shorts inside), shorts, summer & winter trousers, fleece zip-up jackets and trousers.' => '启明星要求所有在校学生穿着校服。我们有多种适合学生的应季校服。包括红绿蓝三种颜色的长/短袖T恤、连衣裙。此外还有女生裤裙、男生短裤、夏/冬长裤、拉链式夹克和绒裤可供选择。在您购买了新生成套校服后，可在开学后根据实际情况另外购买单件校服。所有校服在不过水清洗的情况下可以调换尺寸。',
    'Boy' => '男生',
    'Girl' => '女生',
    'Size' => '尺寸',
    "New Students' Uniform Order" => '新生校服订购',
    'Daystar students are expected to wear a school uniform. In Middle School, we provide a variety of different uniforms to suit boys and girls of teenagers’ age for different seasons. We have girls/boys blazers, short sleeve polo T-Shirts in red and blue, girls pleated skirts/boys pants in khaki and blue. We also have skorts (skirts that are shorts inside), tracksuit pants, PE summer shirts/shorts and full zip-up hoodies. You may order separate pieces as you need after you buy a whole set of uniforms for new students.' => '启明星要求所有在校学生穿着校服。中学有多种适合学生的应季校服。包括女士/男士西服，红色、藏蓝色两种颜色的短袖T恤，卡其色、藏蓝色两种颜色的女生褶裙/男生长裤。此外还有女生裤裙、男生短裤、运动长裤、PE短袖/短裤和拉链帽衫可供选择。在您购买了新生成套校服后，可在开学后根据实际情况另外购买单件校服。',
    'New student uniform sets include the following items: 9 pieces for girls and 13 pieces for boys.' => '新生校服套装分别包含以下款式和件数，其中女生共计9件，男生共计13件：',
    'Please choose the suitable size according the following form:' => '请根据以下建议尺寸选购合适的校服：',
    'New student uniform sets include the following items: 12 pieces for girls and 12 pieces for boys.' => '新生校服套装分别包含以下款式和件数，其中女生共计12件，男生共计12件：',
    'Preferred Hospital 2' => '意向医院2',
    'Insurance Company' => '保险公司',
    'Emergency Contact Person 1' => '紧急联系人 1',
    'Emergency Contact Person Phone 1' => '紧急联系人电话 1',
    'Emergency Contact Person 2' => '紧急联系人 2',
    'Emergency Contact Person Phone 2' => '紧急联系人电话 2',
    'ADD/ADHD' => '多动症',
    'Heart Disorder' => '心脏病',
    'Allergies(food,medications,etc.)' => '过敏（食品、药品、其他）',
    'Frequent Ear Infections /Hearing Problems' => '耳部疾病（听力）',
    'Asthma' => '哮喘',
    'Hepatitis' => '肝炎',
    'Back problems or scoliosis' => '背部或脊柱问题',
    'Gastrointestinal Disorder' => '消化系统疾病',
    'Bone Fractures' => '骨折',
    'Skin Problems' => '皮肤病',
    'Diabetes' => '糖尿病',
    'Vision Problems' => '视力问题',
    'Epilepsy/Seizure Disorder' => '癫病',
    'Tuberculosis' => '肺结核',
    'Cough and expectoration lasting more than 2 weeks' => '咳嗽、咳痰持续2周以上',
    'There is blood in the mucus repeatedly coughed up' => '反复咳出的痰中带血',
    'Recurrent fever lasting more than 2 weeks' => '反复发热持续2周以上',
    'Are there tuberculosis patients among the family members, relatives and friends who are often in contact with the immediate family over the last 2 years' => '经常见面的家人、亲戚、朋友中2年内是否有肺结核病人',
    'Vision/Color Vision Problems' => '视力/色觉问题',
    'Special Food considerations' => '特殊饮食需要',
    'Other (please specify)' => '其他（请写明）',
    'School Lunch' => '学校午餐',
    'Assessment needs for LEAP' => '教学问卷',
    'Which language did your child learn first?' => '您孩子学会的第一种语言是什么？',
    'Which language is most often spoken at home?' => '在家里常用的语言是什么？',
    'Which language does your child usually speak?' => '您孩子常用的语言是什么？',
    'Questionnaire:' => 'HLQ中的3个问题如下：',
    'If the answer to any of these questions is a language other than English, the student will be assessed for English Language Proficiency using WIDA W-APT.' => '如果这三个问题中的任何一个答案不是英文，这些学生都要使用WIDA WAPT 进行英文测评。',
    'School Bus Application' => '班车申请',
    'Address (compound name, street and district in Beijing)' => '家庭地址信息（小区名、街道名和区名）',
    'Would you like to apply for the school bus service for your child?' => '您想为您的孩子申请班车吗？',
    'No, we will not need the school bus service and will arrange our own drop off and pick up.' => '不，我们不需要班车服务，我们自己接送孩子上下学。',
    'Yes. I would like to apply the school bus service for my child.' => '是的，我想为我的孩子申请班车。',
    'Starting from' => '乘坐开始日期',
    'Gender' => '性别',
    'Medical Information Form' => '医疗信息表',
    'Yes' => '是',
    'No' => '否',
    'School Bus Registration' => '班车服务登记',
    'School Uniform Ordering' => '校服套装信息',
    'Student Pick-up Cards' => '完善接送卡信息',
    'Medical Information' => '完善医疗健康信息',
    'Lunch Program' => '午餐服务登记',
    'Home Language Survey' => '家庭语言调查',
    'Student Enrollment Agreement' => '入学协议',
    'Choose' => '请选择',

    'Item' => '种类',
    'Quantity' => '数量',
    'Price(RMB)' => '价格(人民币)',
    'Girls set' => '女孩套装',
    'Dress(red,green,blue)' => '连衣裙(红、绿、蓝)',
    'Long Sleeve T-Shirt(red,green,blue)' => '长袖T恤(红、绿、蓝)',
    'Fleece Trousers' => '冬天绒裤',
    'Fleece Zip up jacket' => '冬天夹克',
    'Winter Trousers' => '冬天长裤',
    'Boys set' => '男孩套装',
    'Short Sleeve T-Shirt(ref,green,blue)' => '短袖T恤(红、绿、蓝)',
    'Summer Trousers' => '夏天长裤',
    'Shorts' => '男生短裤',
    'Uniform Size Reference' => '校服尺寸参考',
    'Uniform Size' => '校服尺寸',
    'Age' => '年龄',
    'Child’s Height' => '孩子身高',


    'Uniform Order Form (Middle School)' => '校服订购单(中学)',
    'Girls Blazer' => '女式西服',
    'Girls Long Sleeve Shirt' => '女式长袖衬衫',
    'Pleated Skirt(Khaki, blue)' => '女式褶裙(卡其、藏蓝)',
    'Skort' => '女式裙裤',
    'Unisex Short Sleeve Polo T-Shirt(red, blue)' => '短袖T恤衫(红色、藏蓝)',
    'Unisex Full Zip Hoodies' => '拉链帽衫',
    'PE Tracksuit Pants' => '运动长裤',
    'PE Summer Shirt' => 'PE 短袖',
    'PE Summer Short' => 'PE 短裤',
    'Girls Tie' => '女式领带',

    'Boys Blazer' => '男式西服',
    'Boys Long Sleeve Shirt' => '男式长袖衬衫',
    'Boys Pant(Khaki, blue)' => '男式长裤(卡其、藏蓝)',
    'Boys Tie' => '男式领带',
    'Boys Short' => '男式短裤',

    'People who may drop off/collect your child ' => '接送人 ',
    'People who may drop off/collect your child 1' => '接送人1',
    'People who may drop off/collect your child 2' => '接送人2',
    'People who may drop off/collect your child 3' => '接送人3',
    'People who may drop off/collect your child 4' => '接送人4',

    'Will your child have school lunch?' => '您的孩子是否在学校吃午餐?',
    'Yes, my child will eat school lunch.' => '是，我的孩子在学校享用午餐',
    'No, my child will bring his/her own lunch from home.' => '否，我的孩子将自带午餐',

    'File should not exceed 5M' => '文件最大为5M',
    'File can not be empty' => '上传头像不能为空',
    'Incomplete information' => '信息不完整',
    'Minimum of 1 person is needed' => '至少填写一位',
    'Congratulation! You have successfully submitted your information.' => '提交成功，感谢您完善信息!',
    'Error! Please fill in the information as needed.' => '系统错误，您需要重新填写部分内容',
    'Go back to the first Step.' => '返回重新填写',
    'Please indicate if your child need bus service or not.' => '请选择您的孩子是否需要班车服务',
    'Complete' => '完成',

    'Health Policies & Insurance' => '健康政策&保险',

    'As part of the registration process for Kindergarten, Casa and Grade 1 students, you are required to submit either a health check form or a letter from your current Kindergarten stating that you have previously turned the form into them. Please turn these items into the school by August 20th. You may turn these documents into the school at the New Parent Orientation meeting.' => '作为幼儿园、Casa和一年级学生注册流程的一部分，需要您提交一份孩子的体检证明或者孩子之前幼儿园开具的转园证明，说明之前曾经向该幼儿园提交过体检证明。请在8月20日之前把这些文件交到学校。你也可以在新生说明会时提交这些文件。',

    'Food Allergies' => '食物过敏源',
    'Agree' => '同意',
    'Mammoth' => '萌犸象科技',
    'Please scan the QRcode with Wechat to continue.' => '请使用微信扫描二维码访问',

    'Every Daystar student is expected to wear a school uniform' => '我们希望每一个启明星的学生在校园里都穿着校服。',
    'We have a variety of different uniforms for boys and girls of all ages for different seasons. We have long & short sleeve collared cotton polo-shirts, girls dresses which come in red, green, or blue. We also have Boy’s shorts, summer trousers, fleece zip-up jackets and trousers.' => '我们为学生提供不同季节的校服，其中包括：长短袖T恤，连衣裙（分为红色、绿色、蓝色），男生短裤，夏长裤，绒夹克和绒裤。',
    'New elementary student uniform sets include the following items: 9 pieces for girls and 13 pieces for boys.' => '新款小学校服套装分为：女生9件套和男生13件套。',
    'New middle school student uniform sets include the following items: 12 pieces for girls and 12 pieces for boys.' => '新款中学校服套装分为：女生12件套和男生12件套。',

    'Sign up for school bus'=>'班车服务登记',
    'Method (Round Trip/Morning Only/Afternoon Only)'=>'乘坐方式',
    'Pick-up/Drop-off Point'=>'设置站点',
    'My Pick-up/Drop-off Point:'=>'我的站点：',
    'Existing Pick-up/Drop-off Point'=>'选择已有站点',
    'Apply New Pick-up/Drop-off Point'=>'申请新站点',
    'Agree & sign'=>'同意并签字',
    'Signature required'=>'签名不能空',
    'Memo required'=>'请填写备注',
    'Rotate your phone horizontal for better view to sign'=>'若要获得更大宽度的签字面积，可开启手机的自动旋转并旋转屏幕。',
    'Rewrite'=>'重写',
    'Please enter'=>'请输入站点信息',
    'Format Example: Chaoyang District Rongke Olive City Phase III South Exit'=>'格式示例：朝阳区 融科橄榄城三期 南口',
    'New pick-up/drop-off point must meet a minimum three students requirement.  Please refer to Bus Handbook for more details.'
=>'新站开设需满足3人以上同时申请，新站点开设原则详见以下班车手册',
    'Compound Name'=>'小区名：',
    'Pick-up/Drop-off Point'=>'停靠点：',
    'Select'=>'选为站点',
    'School Bus Agreement'=>'校车协议',
    'Location Pin Failed'=>'定位失败',
    'School Bus Agreement Signature'=>'校车协议签名专用',
    'Student Profile'=>'学生头像',
    'Change profile'=>'更换头像',
    'Left Rotation'=>'左旋转',
    'Right Rotation'=>'右旋转',
    'Reset'=>'重置',
    'Out'=>'退出',
    'Selecting Size'=>'选择尺寸',
    'I fully understand'=>'我已了解',
    'Recommended Height:'=>'推荐身高：',
    'Click to Upload'=>'点击上传',
    'Allergy information (Please specify) (Including but not limited to food and medicine)'=>'过敏信息（请详述）（包括但不限于食物及药品）',
    'School nurse agreement'=>'医疗健康签名专用',
    'Does the student take any medication (orally or by injection) on a regular basis? If yes, please describe in details. '=>'该学生有定期使用（口服或者注射）任何药物吗？如有，请详细说明。',
    'Preferred Hospital 1'=>'意向医院1（家庭首选医院）',
    'Physical Examination Report'=>'体检报告',
    'More than one picture can be uploaded'=>'可上传多张图片',
    'Copy of the Vaccination Record'=>'疫苗打针记录复印件',
    'Normal Lunch at School'=>'我的孩子在学校享用正常午餐',
    'Special Pork-free Lunch at School'=>'我的孩子在学校享用特殊不含猪肉午餐',
    'Special Vegan Lunch at School'=>'我的孩子在学校享用特殊纯素食午餐',
    'Food Allergies (Please List in Detail)'=>'我的孩子对食物过敏（请详细列出）',
    'Meal variety cannot be empty'=>'餐品种类不能空',
    'School fee agreement'=>'收费协议签名专用',
    'School safety agreement'=>'安全协议签名专用',
    'The class chosen is wrong for next school year,  please contact the school.'=>'下学年班级错误，请联系校园。',
    'Click on the "Child" to start the registration process'=>'点击孩子开始注册流程',
    'View'=>'查看',
    'Registration steps'=>'注册步骤',
    'Period for registration: '=>'开放时间：',
    'Status: '=>'状态：',
    'School: '=>'校园：',
    'You do not have a student who needs to register'=>'您没有需要注册的学生',
    'If you have any questions, please contact the school'=>'如有疑问，请联系校园',
    'Return to start page'=>'返回开始页',
    'Start'=>'开始注册',
    'Please complete the registration before :date'=>'请于:date日之前完成注册',
    'File upload failed'=>'文件上传失败',
    'File cannot be empty'=>'文件不能为空',
    'File name error'=>'文件名错误',
    'Signature save failed'=>'签名保存失败',
    'Upload photo'=>'请上传照片',
    'Remarks'=>'特殊说明',
    'Allergy'=>'过敏说明',
    'Reject reason: '=>'驳回原因：',
    'Registration closed'=>'入学注册已关闭',
    'Please select'=>'未选择',
    'Physical examination report must be uploaded'=>'体检报告不能为空',
    'Vaccine report must be uploaded'=>'疫苗打针记录需复印件不能为空',
    'New Student Online Registration'=>'新生在线注册',
    'Log In <small> to enter registration procedures</small>'=>'登录 <small> 进入登记流程</small>',
    'Login in <small> to enter registration process</small>'=>'登录 <small> 进入登记流程</small>',
    'Address+compound+pick up and drop off point'=>'地址 + 小区名 + 停靠点',
    'pending for register' =>'待注册',
    'In progress'=>'进行中',
    'It has been submitted'=>'家长已提交',
    'It has been processed'=>'校园已审核',
    'Information to be revised'=>'需修改资料',
    'Safety protocol'=>'安全协议',
    'The step has been reviewed'=>'已通过审核，不允许修改。',
    'The step has been reviewed. Please contact school to revise information.'=>'校园已完成本步骤审核，不能自行修改，如需变动请联系校园。',
    'No content can be submitted while registration is closed'=>'未在注册开放期间，暂不能提交内容。',
    'Please complete highlited steps'=>'请完成以下红色步骤！',
    'Click a number in the top bar to do other step.'=>'您可以点击顶部的数字进入其他步骤。',
    'Please clarify allergy information, fill NO if it is none.'=>'过敏信息不能为空，没有请填无。',
    'Start'=>'开始填写',
    'Please take a note if any'=>'如有备注请输入...',
    'Back'=>'上一步',
    'Next Step'=>'下一步',
    'Please "Click" to continue'=>'请勾选后继续',
    'The Re-enrollment Confirmation has been completed'=>'返校确认已完成',
    'You have filled the Re-enrollment Confirmation. If you haven’t paid the deposit yet, please click "To Pay" to ensure your child’s spot.'=>'您已填写完成返校确认函。如果您尚未支付定位金，请点击下方“去支付”按钮。',
    'You have filled the Re-enrollment Confirmation. If you haven’t paid the tuition deposit yet, please click "To Pay" to ensure your child’s spot.'=>'您已填写完成返校确认函。如果您尚未支付预交学费，请点击下方“去支付”按钮。',
    'To Pay'=>'去支付',
    'Please click the photo or name to start'=>'点击头像或姓名进行填写',
    'Siblings need to fill the form respectively.'=>"多子女需要分别填写",
    'Tuition Policy and Agreement'=>'查看学费协议',
    'Student Name:'=>'学生姓名：',
    'Campus:'=>'所属校园：',
    'Class:'=>'班级：',
    'Name of Parent/Guardian:'=>'家长/监护人姓名：',
    'Mobile Number:'=>'联系电话：',
    'For student and members living at the same address (“members”), between January 24, 2020 and now:'=>'疫情期间（自2020年1月24日起），学生及其同住人出行情况：',
    'Prior to returning to school, student and other people living at the same address have all completed the mandatory 14-day quarantine requirement.  No one has exhibited COVID-19 symptoms.  No one is a confirmed or suspected COVID-19 patient.'=>'开学前学生及其同住人已按要求隔离14天，观察期间学生及其同住人无新冠肺炎疑似症状，未被确诊或疑似新冠肺炎。',
    'Upon returning to school, I will adhere to the procedures required by the school.  These include but are not limited to the following:'=>'开学后严格执行学校对家庭的要求，包括并不限于：',
    'Wear masks during pick-up and drop-off of students'=>'接送学生的家长全程佩戴口罩',
    'Pick-up and drop-off students at the designated time and place according to school’s instructions.'=>'按照学校要求在指定时间、地点接送学生',
    'Comply with school zone designations and not enter the restricted zone designated for students and teaching staff only.'=>'遵守学校对于家长及外来人员进入学校不同区域的要求，不进入学校为学生和教职工特别划定的控制区域',
    'Complete a daily temperature check for the family. Immediately report to the school with any abnormal temperature readings or suspicious symptoms, and voluntarily submit to home quarantine if needed.  Once symptoms disappear, the student must obtain approval from the school before returning.'=>'每天主动对学生及同住人进行体温测试，如学生本人或同主人有体温异常或其他疑似症状，第一时间上报校园管理者，主动进行居家隔离，身体恢复正常后经校园管理者批准后再返校',
    'Notify the school if:'=>'以下情况我会主动汇报学校：',
    'The student, members living at the same address, or family members have been in contact with people from a high-risk region (as defined by Chinese government or WHO), confirmed carriers, or suspected carriers'=>'学生、同住人或家人近期接触过来自重点疫区的人群、新冠肺炎确诊患者、及疑似患者',
    'The student, members living at the same address, or family members exhibit COVID-19 symptoms such as fever, dry cough, fatigue, nausea, or shortness of breath'=>'学生、同住人或家人近期有发热/干咳/乏力/恶心呕吐/心慌胸闷等疑似症状',
    'I have any reason to suspect that the student may be infected'=>'任何原因怀疑学生有被感染',
    'I hereby acknowledge the truthfulness and accuracy of the information provided herein.  I commit myself to adhering to the school’s guidelines above. I am solely responsible for all legal matters pertaining to the consequences of my actions.'=>'我承诺以上信息真实可靠，并会配合学校以上的要求。否则由此所产生的一切法律责任及其后果，均由本人自行承担。',
    'I hereby acknowledge the personal information provided herein is true and accurate to the best of my knowledge.'=>'我承诺以下提供的家庭信息真实可靠。',
    'I am the:'=>'本人为学生的：',
    'No one has not left the city during this period.'=>'一直在本地，无出城史。',
    "The student or other members left the city on for <strong class='stInt'><input type='text' class='country' name='destination'></strong> (Country/Province/City) and returned on <span class='goDate'></span> <input type='date' name='return_date' class='year' id='date' placeholder='请选择日期' > (date). (If multiple members have travelled, list the one who returned last)"=>"学生或同住人去往 <strong class='stInt'><input type='text' class='country' name='destination'></strong>  （国家/省/城市），于 <span class='goDate'></span> <input type='date' name='return_date' class='year' id='date' placeholder='请选择日期' >
    返回本市。（如有多人出城，请列出最后返回人的信息）",
    'Affiant Signature:'=>'承诺人（签字）：',
    'COVID-19 Legal Guardian Affidavit'=>'家长防疫行为承诺书',
    'Vehicle Plate Number'=>'接送车辆号牌',
    'Using comma if registering more than one vehicle.'=>'请输入车牌号码，多个请使用逗号分隔。',
    'Student Health History'=>'学生基本健康情况',
    'Please answer the following questions regarding the health of your child. If you check YES to any conditions, please provide further details in "Other" column.'=>'请勾出以下与您孩子疾病史相关问题的答案。如果您勾划“是”，请在最下方“其他”一栏注明具体情况。',

    'Allergies History (please give details of severity and treatment)' => '过敏历史（请详细列出过敏程度及治疗方法）',
    'Do you give permission to the school to use pictures taken of your child for marketing purpose?' => '您是否同意学校将您孩子的照片用于市场推广？',
    'Hospital' => '医院',
    'Should a situation arise, when emergency medical attention is required and we are unable to contact any of the above, please indicate, on the space provided below, the hospital or clinic you want your child taken to for treatment. If no hospital is designed by you, the school will take your child to an established public hospital. If the hospital or clinic parent provided is not a public one, parents will take responsibility of the payment. ' => '如遇紧急情况，学生需要医疗服务，而学校无法联系到上述联系人，请在下列横线注明家长希望把学生送至的医院或诊所名。若家长不指定任何医院，学生将被送往三甲公立医院就诊。如果家长指定医院不在三甲公立医院，家长将要承担相关费用。',
    'Contact Number' => '联系电话',
    'Relationship' => '关系',
    'Medical History' => '病史',
    'Emergency Contact (Beside Parents)' => '紧急联系人 (除父母外）',
    'Please specify any previous illness historiy （ex. dislocate, catagma, hyperpyretic convulsion，asthma ...)' => '请在此说明任何既往病史 （包括脱臼，骨折，高热惊厥，哮喘等）',

    'Student Passport Photo (only for foreign student)'=>'学生户口本照片',
    'Mother Passport Photo'=>'母亲身份证照片',
    'Photo Copy of Vaccine Record(Please upload all the vaccine record)'=>'预防接种记录照片(请从第一页开始上传全部接种记录)',
    'Commercial Medical Insurance Card Photo (optional)'=>'商业医疗保险卡照片（选填）',
    'New Student Information Collecting'=>'学生学籍信息采集',
    'Material Fee Payment'=>'材料费付款',
    'Student Family Document'=>'学生家庭证件',

    'Children Passport Number' => '儿童身份证号码或护照号码',
    'Blood Type' => '血型',
    'Birth Place' => '出生所在地',
    'Guardian Name' => '监护人姓名',
    'Guardian Passport Number or Chinese ID Number' => '监护人身份证号码',
    'Guardian’s relationship with child' => '监护人与儿童的关系',

    'Father Name' => '⽗亲姓名',
    'Father’s contact number' => '⽗亲联系方式',
    'Father’s Email' => '⽗亲邮箱',
    'Mother Name' => '⺟亲姓名',
    'Mother’s contact number' => '母亲联系方式',
    'Mother’s Email' => '⺟亲邮箱',
    'Only Child' => '独生子女',
    'Current Address' => '现住址',
    'Is your child the only kid in your family?' => '是否为独生子女？',
    '(ID photo taken within 3 months)' => '（近3个月免冠证件照）',
);
