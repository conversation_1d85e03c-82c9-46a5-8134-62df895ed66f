<?php
Yii::import('common.models.hr.*');

$crit = new CDbCriteria;
$crit->index = 'branchid';
$crit->compare('status', Branch::STATUS_ACTIVE);
$crit->order = '`group` ASC, `weight` ASC';
$branches = Branch::model()->findAll($crit);

$schools = array();
$group = array();
foreach ($branches as $branch) {
    if ($branch->type == 10) {
        $group[] = $branch;
    } else {
        $schools[] = $branch;
    }
}

$crit = new CDbCriteria();
$crit->order = 'weight ASC';
$crit->index = 'id';

//所有部门
$depModels = HrDepartment::model()->findAll($crit);

//职位与部门对应关系
$depPosLinkModels = DepPosLink::model()->findAll();

$userData[$initBranchId] = $this->getStaffByBranch($initBranchId);


$depIndex = array();
foreach($depPosLinkModels as $linkModel){
    $depIndex[] = $linkModel->position_id;
    $depPosData[$linkModel->position_id] = array(
        'isLead' => $linkModel->is_lead,
        'depId' => $linkModel->department_id,
    );
}
?>

<script>
    var selectedBranchId = '<?php echo $initBranchId; ?>';
    var userData = <?php echo CJSON::encode( $userData );?>;
    var userArray = [];
    _.each(userData[selectedBranchId], function (v) {
        v.weight = parseInt(v.weight)
        userArray.push(v);
    });
    userArray = _.sortBy(userArray, 'weight');
    var depPosLinks = <?php echo CJSON::encode( $depPosData );?>;
    var countryData = <?php echo CJSON::encode( Country::model()->getCountryList() ); ?>
</script>

<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo Yii::t('site', 'Ivy Family')?></li>
        <li class="active"><?php echo Yii::t('site', 'Ivyers')?></li>
    </ol>
    <div class="row">
        <div class="col-md-2">
            <?php $this->widget('ext.search.StaffSearchHandy', array(
                'inputCSS' => 'form-control mb10 search-input',
                'displayLabel'=>'',
                'popPosition' => array('collision'=>"none"),
                'restrictSchool' => 'BJ_OE',
            )) ?>
    
            <div class="list-group" id="campus-list">
                <?php
                foreach($group as $branch):
                    $_title = '<span class="glyphicon glyphicon-tags program'.$branch->group.'"></span> ';
                    $_title .= $branch->title;
                    echo CHtml::link($_title,'#'.$branch->branchid, array('branchid'=>$branch->branchid,'class'=>'list-group-item'));
                endforeach;
                ?>
            </div>

            <div class="list-group" id="campus-list">
                <?php
                foreach($schools as $branch):
                    $_title = '<span class="glyphicon glyphicon-tags program'.$branch->group.'"></span> ';
                    $_title .= $branch->title;
                    echo CHtml::link($_title,'#'.$branch->branchid, array('branchid'=>$branch->branchid,'class'=>'list-group-item'));
                endforeach;
                ?>
            </div>
        </div>
        <div class="col-md-2">
            <div class="panel panel-default">
                <div class="list-group" id="staff-stats">
                    <a onclick="stafffilter(this)" href="javascript:void(0)" class="list-group-item active" id="stats-total-count"><?php echo Yii::t('ivyer','Total Staff');?> <span class="badge"></span></a>
                    <a onclick="stafffilter(this)" href="javascript:void(0)" class="list-group-item stats" uattr="uattr-gender" uattr-value="=2"><?php echo Yii::t('ivyer','Female Staff');?> <span class="badge"></span></a>
                    <a onclick="stafffilter(this)" href="javascript:void(0)" class="list-group-item stats" uattr="uattr-gender" uattr-value="=1"><?php echo Yii::t('ivyer','Male Staff');?> <span class="badge"></span></a>
                    <a onclick="stafffilter(this)" href="javascript:void(0)" class="list-group-item stats" uattr="uattr-country" uattr-value="=36"><?php echo Yii::t('ivyer','China Mainland');?> <span class="badge"></span></a>
                    <a onclick="stafffilter(this)" href="javascript:void(0)" class="list-group-item stats" uattr="uattr-country" uattr-value="=175"><?php echo Yii::t('ivyer','China Other');?> <span class="badge"></span></a>
                    <a onclick="stafffilter(this)" href="javascript:void(0)" class="list-group-item stats" uattr="uattr-country" uattr-value="=999"><?php echo Yii::t('ivyer','Foreigners');?> <span class="badge"></span></a>
                </div>
            </div>
        </div>
        <div class="col-md-8">
            <div class="panel panel-default p20">
                <?php
                foreach($depModels as $dModel):
                    echo CHtml::openTag('div',array('dep-id'=>$dModel->id, 'class'=>'dep-container'));
                    echo '<h3 class="dep-item"><span class="glyphicon glyphicon-chevron-right"></span> ';
                    echo ( Yii::app()->language == 'zh_cn' ? $dModel->cn_name : $dModel->en_name );
                    echo ' <span class="badge"></span></h3>';
                    echo CHtml::openTag('ul', array('class'=>'media-list'));
                    echo CHtml::closeTag('ul');
                    echo CHtml::closeTag('div');
                endforeach;
                ?>
            </div>
        </div>
    </div>
</div>

<script type="text/template" id="staff-li-item-template">
    <li class="media" uattr-gender=<%- gender%> uattr-country=<%- nationality %>>
        <img class="pull-left img-thumbnail img-face" src="<%- uploadBaseUrl %><%- photo_thumb %>">
        <div class="media-body">
             <span class="user-head"><button onclick="resetPopups()" class="user-name popover-dismiss" data-staffid=<%- uid %> data-placement="bottom" data-toggle="popover"><%= name %></button> <small><%= positionTitle %></small> </span><%= wechatBind?'<img style="height:16px" src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/wechatDM.png' ?>" alt="">':'' %>
        </div>
    </li>
</script>

<script type="text/template" id="staff-info-item-template">
    <div class="media">
        <a class="pull-left" href="javascript:;">
            <img class="img-thumbnail" src="<%- uploadBaseUrl %><%- photo %>" style="height: 120px">
        </a>
        <div class="media-body">
            <h5 class="pull-right">
                <a href="javascript:;" onclick="resetPopups();"> <span class="glyphicon glyphicon-remove"></span></a>
            </h5>
            <h4>
                <a href="<?php echo $this->createUrl('//mpub/default/staff')?>?id=<%- uid %>" target="_blank"><span class="glyphicon glyphicon-user"></span> <strong><%- name %></strong></a>
            </h4>
            <div class="ml20hi  text-muted">
                <ul class="list-unstyled">
                    <li class='color3 fontBold'>ID: <%- uid %></li>
                    <li> <%- positionTitle %></li>
                    <li> <a href="mailto:<%- email %>"><%- email %></a></li>
                    <li> <%= countryData[countryId] %></li>
                    <li> <%- intrest %></li>
                    <% if(readtask > 0){%>
                    <li> <?php echo Yii::t('ivyer', 'Reading Tasks: ');?><%- readtask %></li>
                    <% }%>
                </ul>
            </div>
        </div>
    </div>
</script>

<script type="text/template" id="staff-pubinfo-item-template">
    <div class="media">
        <% if(staff_photo !=''){%>
            <a class="pull-left" href="javascript:;">
                <img class="img-thumbnail" src="<%- uploadBaseUrl %>/infopub/staff/<%- staff_photo %>" style="height: 110px">
            </a>
        <% }%>
        <div class="media-body">
            <p><%- intro_cn %></p>
            <p><%- intro_en %></p>
            <!-- <div class="well well-sm">
                <?php echo Yii::t('global','Error Report: '); ?><a href="mailto:<EMAIL>"><EMAIL></a>
            </div> -->
        </div>
    </div>
</script>

<script>
    var drawStaff = null; //显示员工列表`
    var uploadBaseUrl = '<?php echo Yii::app()->params['OAUploadBaseUrl'];?>'; //员工照片地址
    var staffTemp = _.template( $('#staff-li-item-template').html() ); //员工模版
    var staffPopTemp = _.template( $('#staff-info-item-template').html() ); //员工详细信息模版
    var staffPopPubInfoTemp = _.template( $('#staff-pubinfo-item-template').html() ); //员工详细信息模版
    var refreshStats = null; //统计信息
    var stafffilter = null; //过滤员工
    var resetPopups = null; //关闭所有弹窗

    $(function(){

        resetPopups = function(){
            $('div.popover[role|="tooltip"]').popover('hide');
        }

        refreshStats = function(){
            $('#stats-total-count span').html($('.media-list li').length);
            _.each( $('div#staff-stats a.stats'), function(dom, i){
                var _span = $(dom).find('span');
                var _total = $('.media-list li['+$(dom).attr('uattr')+'|'+$(dom).attr('uattr-value')+']').length;
                if(_total > 0){
                    _span.html( _total);
                    $(dom).show();
                }else{
                    $(dom).hide();
                }
            } )
        }

        stafffilter = function(obj){

            $('ul.media-list li.media').removeClass('active');
            if(_.isNull(obj)){
                obj = $('#stats-total-count');
            }else{
                if($(obj).hasClass('stats')){
                    $('.media-list li['+$(obj).attr('uattr')+'|'+$(obj).attr('uattr-value')+']').removeClass('muted').addClass('active');
                    $('.media-list li['+$(obj).attr('uattr')+'!'+$(obj).attr('uattr-value')+']').removeClass('active').addClass('muted');
                }else{
                    $('.media-list li').removeClass('muted').addClass('active');
                }
            }

            $(obj).siblings().removeClass("active");
            $(obj).addClass('active');
        }

        drawStaff = function(){
            $('div.dep-container').hide();
            $('div.dep-container ul').empty();
            $('div.dep-container h3 span.badge').empty();
            _.each(userArray,function(u,i){
                if (typeof depPosLinks[u.positionId] != 'undefined') {
                    var depContainer = $('div.dep-container[dep-id|="' + depPosLinks[u.positionId]['depId'] + '"]');
                    // console.log(depContainer.find('h3'))
                    var _staffView = staffTemp(u);
                    if(depPosLinks[u.positionId]['isLead'] > 0)
                        depContainer.find('ul').prepend(_staffView);
                    else
                        depContainer.find('ul').append(_staffView);
                    depContainer.show();
                }
            });
            var bigTitle=[150,154,112,116]
            var title=[153,155,156,157,158,159]
            _.each( $('div.dep-container[dep-id]'), function(obj, i){
                var depId=$(obj).attr("dep-id")
                for(var j=0;j<bigTitle.length;j++){
                    if(bigTitle[j]==depId){
                         $(obj).find('h3').addClass('dep-item-background');
                    }
                }
                for(var j=0;j<title.length;j++){
                    if(title[j]==depId){
                        $(obj).addClass('dep-container-left');
                        $(obj).find('h3').addClass('dep-item-fontSize');
                    }
                }
                if($(obj).is(":visible")){
                    var children = $(obj).find('ul.media-list li');
                    $(obj).find('h3 span.badge').html( children.length );
                }
            });

            refreshStats();

            $('#campus-list a').removeClass('active');
            $('#campus-list a[branchid|="'+selectedBranchId+'"]').addClass('active');

            $('.popover-dismiss').bind('click',function() {
                var e=$(this);
                var staffid = e.data('staffid');
                var key = staffid;
                $.ajax({
                    type: "POST",
                    url: '<?php echo $this->createUrl('//mpub/default/staffinfo') ?>',
                    data: "staffid=" + staffid,
                    dataType: 'json',
                    success: function(data) {
                        if(data.state=='success'){
                            userData[selectedBranchId][key]['readtask'] = data['data']['info']['readtask'];
                            var _infoView = staffPopTemp(userData[selectedBranchId][key]);
                            if(!_.isEmpty(data.data.info.userid)){
                                _infoView += staffPopPubInfoTemp(data.data.info)
                            }
                            e.unbind('click');
                            e.popover({content: _infoView, trigger: 'click', html: true}).popover('show');
                        }
                    }
                });
            });
        };

        $('#campus-list a').click(function(){
            var _bid = $(this).attr('branchid');
            if(_.isUndefined(userData[_bid])){
                $.ajax({
                    type: 'post',
                    url: '<?php echo $this->createUrl('getStaff')?>',
                    dataType: 'json',
                    data: {branchid:_bid}
                }).done(function(data){
                    userData[_bid] = data;
                    selectedBranchId = _bid;
                    userArray = [];
                    _.each(userData[selectedBranchId], function (v) {
                        v.weight = parseInt(v.weight)
                        userArray.push(v);
                    });
                    userArray = _.sortBy(userArray, 'weight');
                    drawStaff();
                });
            }
            selectedBranchId = _bid;
            userArray = [];
            _.each(userData[selectedBranchId], function (v) {
                v.weight = parseInt(v.weight)
                userArray.push(v);
            });
            userArray = _.sortBy(userArray, 'weight');
            drawStaff();
            stafffilter(null);
        });

        drawStaff();

        $('.dep-container h3.dep-item').on('click', function (e) {
            var children = $(this).parent('.dep-container').find(' > ul > li');
            if (children.is(":visible")) {
                children.hide();
                $(this).find('span.glyphicon').addClass('glyphicon-chevron-down').removeClass('glyphicon-chevron-right');
            } else {
                children.show();
                $(this).find('span.glyphicon').addClass('glyphicon-chevron-right').removeClass('glyphicon-chevron-down');
            }
            e.stopPropagation();
        });
    })
</script>

<style>
    .dep-container h3.dep-item {
        -moz-border-radius:5px;
        -webkit-border-radius:5px;
        border:1px solid #999;
        border-radius:5px;
        display:inline-block;
        padding:3px 10px;
        text-decoration:none;
        line-height: 1.5;
        cursor: pointer;
    }
    .dep-container h3.dep-item span.badge {
        font-size: 19px;
    }
    .img-face{max-width: 40px;}
    li.muted {display: none}
    li.media span.user-head .user-name{color: #428bca;border: 0;background-color: #fff;line-height: 40px; font-size:115%; font-weight: bold;}
    li.media:hover img.img-face, li.media.active img.img-face{padding: 0;}
    .media-list li.media{padding-left: 2.5em;}
    .search-input{z-index: 10}
    .dep-container .popover{max-width: 460px;}
    .dep-item-background{background:#f2f2f2}
    .dep-container-left{margin-left:30px}
    .dep-item-fontSize{font-size:14px}
</style>