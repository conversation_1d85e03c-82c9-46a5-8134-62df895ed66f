/*!
 * WeUI v0.4.2 (https://github.com/weui/weui)
 * Copyright 2016 Tencent, Inc.
 * Licensed under the MIT license
 */
html {
    -ms-text-size-adjust: 100%;
    -webkit-text-size-adjust: 100%;
}
body {
    line-height: 1.6;
    font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
}
* {
    margin: 0;
    padding: 0;
}
a img {
    border: 0;
}
a {
    text-decoration: none;
}
@font-face {
    font-weight: normal;
    font-style: normal;
    font-family: "weui";
    src: url('data:application/octet-stream;base64,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') format('truetype');
}
[class^="weui_icon_"]:before,
[class*=" weui_icon_"]:before {
    font-family: "weui";
    font-style: normal;
    font-weight: normal;
    speak: none;
    display: inline-block;
    vertical-align: middle;
    text-decoration: inherit;
    width: 1em;
    margin-right: .2em;
    text-align: center;
    /* opacity: .8; */
    /* For safety - reset parent styles, that can break glyph codes*/
    font-variant: normal;
    text-transform: none;
    /* fix buttons height, for twitter bootstrap */
    line-height: 1em;
    /* Animation center compensation - margins should be symmetric */
    /* remove if not needed */
    margin-left: .2em;
    /* you can be more comfortable with increased icons size */
    /* font-size: 120%; */
    /* Uncomment for 3D effect */
    /* text-shadow: 1px 1px 1px rgba(127, 127, 127, 0.3); */
}
.weui_icon_circle:before {
    content: "\EA01";
}
/* '' */
.weui_icon_download:before {
    content: "\EA02";
}
/* '' */
.weui_icon_info:before {
    content: "\EA03";
}
/* '' */
.weui_icon_safe_success:before {
    content: "\EA04";
}
/* '' */
.weui_icon_safe_warn:before {
    content: "\EA05";
}
/* '' */
.weui_icon_success:before {
    content: "\EA06";
}
/* '' */
.weui_icon_success_circle:before {
    content: "\EA07";
}
/* '' */
.weui_icon_success_no_circle:before {
    content: "\EA08";
}
/* '' */
.weui_icon_waiting:before {
    content: "\EA09";
}
/* '' */
.weui_icon_waiting_circle:before {
    content: "\EA0A";
}
/* '' */
.weui_icon_warn:before {
    content: "\EA0B";
}
/* '' */
.weui_icon_info_circle:before {
    content: "\EA0C";
}
/* '' */
.weui_icon_cancel:before {
    content: "\EA0D";
}
/* '' */
.weui_icon_search:before {
    content: "\EA0E";
}
/* '' */
.weui_icon_clear:before {
    content: "\EA0F";
}
/* '' */
[class^="weui_icon_"]:before,
[class*=" weui_icon_"]:before {
    margin: 0;
}
.weui_icon_success:before {
    font-size: 23px;
    color: #09BB07;
}
.weui_icon_waiting:before {
    font-size: 23px;
    color: #10AEFF;
}
.weui_icon_warn:before {
    font-size: 23px;
    color: #F43530;
}
.weui_icon_info:before {
    font-size: 23px;
    color: #10AEFF;
}
.weui_icon_success_circle:before {
    font-size: 23px;
    color: #09BB07;
}
.weui_icon_success_no_circle:before {
    font-size: 23px;
    color: #09BB07;
}
.weui_icon_waiting_circle:before {
    font-size: 23px;
    color: #10AEFF;
}
.weui_icon_circle:before {
    font-size: 23px;
    color: #C9C9C9;
}
.weui_icon_download:before {
    font-size: 23px;
    color: #09BB07;
}
.weui_icon_info_circle:before {
    font-size: 23px;
    color: #09BB07;
}
.weui_icon_safe_success:before {
    color: #09BB07;
}
.weui_icon_safe_warn:before {
    color: #FFBE00;
}
.weui_icon_cancel:before {
    color: #F43530;
    font-size: 22px;
}
.weui_icon_search:before {
    color: #B2B2B2;
    font-size: 14px;
}
.weui_icon_clear:before {
    color: #B2B2B2;
    font-size: 14px;
}
.weui_icon_msg:before {
    font-size: 104px;
}
.weui_icon_warn.weui_icon_msg:before {
    color: #F76260;
}
.weui_icon_safe:before {
    font-size: 104px;
}
.weui_btn.weui_btn_mini {
    line-height: 1.9;
    font-size: 14px;
    padding: 0 .75em;
    display: inline-block;
}
button.weui_btn,
input.weui_btn {
    width: 100%;
    border-width: 0;
    outline: 0;
    -webkit-appearance: none;
}
button.weui_btn:focus,
input.weui_btn:focus {
    outline: 0;
}
button.weui_btn_inline,
input.weui_btn_inline,
button.weui_btn_mini,
input.weui_btn_mini {
    width: auto;
}
/*gap between btn*/
.weui_btn + .weui_btn {
    margin-top: 15px;
}
.weui_btn.weui_btn_inline + .weui_btn.weui_btn_inline {
    margin-top: auto;
    margin-left: 15px;
}
.weui_btn_area {
    margin: 1.17647059em 15px 0.3em;
}
.weui_btn_area.weui_btn_area_inline {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
}
.weui_btn_area.weui_btn_area_inline .weui_btn {
    margin-top: auto;
    margin-right: 15px;
    width: 100%;
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    -ms-flex: 1;
    flex: 1;
}
.weui_btn_area.weui_btn_area_inline .weui_btn:last-child {
    margin-right: 0;
}
.weui_btn {
    position: relative;
    display: block;
    margin-left: auto;
    margin-right: auto;
    padding-left: 14px;
    padding-right: 14px;
    box-sizing: border-box;
    font-size: 18px;
    text-align: center;
    text-decoration: none;
    color: #FFFFFF;
    line-height: 2.33333333;
    border-radius: 5px;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
    overflow: hidden;
}
.weui_btn:after {
    content: " ";
    width: 200%;
    height: 200%;
    position: absolute;
    top: 0;
    left: 0;
    border: 1px solid rgba(0, 0, 0, 0.2);
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
    -webkit-transform-origin: 0 0;
    transform-origin: 0 0;
    box-sizing: border-box;
    border-radius: 10px;
}
.weui_btn.weui_btn_inline {
    display: inline-block;
}
.weui_btn_default {
    background-color: #F7F7F7;
    color: #454545;
}
.weui_btn_default:not(.weui_btn_disabled):visited {
    color: #454545;
}
.weui_btn_default:not(.weui_btn_disabled):active {
    color: #A1A1A1;
    background-color: #DEDEDE;
}
.weui_btn_primary {
    background-color: #04BE02;
}
.weui_btn_primary:not(.weui_btn_disabled):visited {
    color: #FFFFFF;
}
.weui_btn_primary:not(.weui_btn_disabled):active {
    color: rgba(255, 255, 255, 0.4);
    background-color: #039702;
}
.weui_btn_warn {
    background-color: #EF4F4F;
}
.weui_btn_warn:not(.weui_btn_disabled):visited {
    color: #FFFFFF;
}
.weui_btn_warn:not(.weui_btn_disabled):active {
    color: rgba(255, 255, 255, 0.4);
    background-color: #C13E3E;
}
.weui_btn_disabled {
    color: rgba(255, 255, 255, 0.6);
}
.weui_btn_disabled.weui_btn_default {
    color: #C9C9C9;
}
.weui_btn_plain_primary {
    color: #04BE02;
    border: 1px solid #04BE02;
}
button.weui_btn_plain_primary,
input.weui_btn_plain_primary {
    border-width: 1px;
    background-color: transparent;
}
.weui_btn_plain_primary:active {
    border-color: #039702;
}
.weui_btn_plain_primary:after {
    border-width: 0;
}
.weui_btn_plain_default {
    color: #5A5A5A;
    border: 1px solid #5A5A5A;
}
button.weui_btn_plain_default,
input.weui_btn_plain_default {
    border-width: 1px;
    background-color: transparent;
}
.weui_btn_plain_default:after {
    border-width: 0;
}
.weui_cell {
    position: relative;
}
.weui_cell:before {
    content: " ";
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 1px;
    border-top: 1px solid #D9D9D9;
    color: #D9D9D9;
    -webkit-transform-origin: 0 0;
    transform-origin: 0 0;
    -webkit-transform: scaleY(0.5);
    transform: scaleY(0.5);
    left: 15px;
}
.weui_cell:first-child:before {
    display: none;
}
.weui_cells {
    margin-top: 1.17647059em;
    background-color: #FFFFFF;
    line-height: 1.41176471;
    font-size: 17px;
    overflow: hidden;
    position: relative;
}
.weui_cells:before {
    content: " ";
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 1px;
    border-top: 1px solid #D9D9D9;
    color: #D9D9D9;
    -webkit-transform-origin: 0 0;
    transform-origin: 0 0;
    -webkit-transform: scaleY(0.5);
    transform: scaleY(0.5);
}
.weui_cells:after {
    content: " ";
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 1px;
    border-bottom: 1px solid #D9D9D9;
    color: #D9D9D9;
    -webkit-transform-origin: 0 100%;
    transform-origin: 0 100%;
    -webkit-transform: scaleY(0.5);
    transform: scaleY(0.5);
}
.weui_cells_title {
    margin-top: .77em;
    margin-bottom: .3em;
    padding-left: 15px;
    padding-right: 15px;
    color: #888;
    font-size: 14px;
}
.weui_cells_title + .weui_cells {
    margin-top: 0;
}
.weui_cells_tips {
    margin-top: .3em;
    color: #888;
    padding-left: 15px;
    padding-right: 15px;
    font-size: 14px;
}
.weui_cell {
    padding: 10px 15px;
    position: relative;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
}
.weui_cell_ft {
    text-align: right;
    color: #888;
}
.weui_cell_primary {
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    -ms-flex: 1;
    flex: 1;
}
.weui_cells_access .weui_cell:not(.no_access) {
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}
.weui_cells_access .weui_cell:not(.no_access):active {
    background-color: #ECECEC;
}
.weui_cells_access a.weui_cell {
    color: inherit;
}
.weui_cells_access .weui_cell_ft:after {
    content: " ";
    display: inline-block;
    -webkit-transform: rotate(45deg);
    transform: rotate(45deg);
    height: 6px;
    width: 6px;
    border-width: 2px 2px 0 0;
    border-color: #C8C8CD;
    border-style: solid;
    position: relative;
    top: -2px;
    top: -1px;
    margin-left: .3em;
}
.weui_check_label {
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}
.weui_check {
    position: absolute;
    left: -9999em;
}
.weui_cells_radio .weui_cell_ft {
    padding-left: 0.35em;
}
.weui_cells_radio .weui_cell:active {
    background-color: #ECECEC;
}
.weui_cells_radio .weui_check:checked + .weui_icon_checked:before {
    display: block;
    content: '\EA08';
    color: #09BB07;
    font-size: 16px;
}
.weui_cells_checkbox .weui_cell_hd {
    padding-right: 0.35em;
}
.weui_cells_checkbox .weui_cell:active {
    background-color: #ECECEC;
}
.weui_cells_checkbox .weui_icon_checked:before {
    content: '\EA01';
    color: #C9C9C9;
    font-size: 23px;
    display: block;
}
.weui_cells_checkbox .weui_check:checked + .weui_icon_checked:before {
    content: '\EA06';
    color: #09BB07;
}
.weui_label {
    display: block;
    width: 105px;
    word-wrap: break-word;
    word-break: break-all;
}
.weui_input {
    width: 100%;
    border: 0;
    outline: 0;
    -webkit-appearance: none;
    background-color: transparent;
    font-size: inherit;
    color: inherit;
    height: 1.41176471em;
    line-height: 1.41176471;
}
.weui_input::-webkit-outer-spin-button,
.weui_input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}
.weui_textarea {
    display: block;
    border: 0;
    resize: none;
    width: 100%;
    color: inherit;
    font-size: 1em;
    line-height: inherit;
    outline: 0;
}
.weui_textarea_counter {
    color: #B2B2B2;
    text-align: right;
}
.weui_cell_warn .weui_textarea_counter {
    color: #E64340;
}
.weui_toptips {
    display: none;
    position: fixed;
    -webkit-transform: translateZ(0);
    width: 100%;
    top: 0;
    line-height: 2.3;
    font-size: 14px;
    text-align: center;
    color: #FFF;
    z-index: 2;
}
.weui_toptips.weui_warn {
    background-color: #E64340;
}
.weui_cells_form .weui_cell_warn {
    color: #E64340;
}
.weui_cells_form .weui_cell_warn .weui_icon_warn {
    display: inline-block;
}
.weui_cells_form .weui_cell_ft {
    font-size: 0;
}
.weui_cells_form .weui_icon_warn {
    display: none;
}
.weui_cells_form input,
.weui_cells_form textarea,
.weui_cells_form label[for] {
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}
.weui_cell_select {
    padding: 0;
}
.weui_cell_select .weui_select {
    padding-right: 30px;
}
.weui_cell_select .weui_cell_bd:after {
    content: " ";
    display: inline-block;
    -webkit-transform: rotate(45deg);
    transform: rotate(45deg);
    height: 6px;
    width: 6px;
    border-width: 2px 2px 0 0;
    border-color: #C8C8CD;
    border-style: solid;
    position: relative;
    top: -2px;
    position: absolute;
    top: 50%;
    right: 15px;
    margin-top: -3px;
}
.weui_select {
    -webkit-appearance: none;
    border: 0;
    outline: 0;
    background-color: transparent;
    width: 100%;
    font-size: inherit;
    height: 44px;
    line-height: 44px;
    position: relative;
    z-index: 1;
    padding-left: 15px;
}
.weui_select_before {
    padding-right: 15px;
}
.weui_select_before .weui_select {
    width: 105px;
    box-sizing: border-box;
}
.weui_select_before .weui_cell_hd {
    position: relative;
}
.weui_select_before .weui_cell_hd:after {
    content: " ";
    position: absolute;
    right: 0;
    top: 0;
    width: 1px;
    height: 100%;
    border-right: 1px solid #D9D9D9;
    color: #D9D9D9;
    -webkit-transform-origin: 100% 0;
    transform-origin: 100% 0;
    -webkit-transform: scaleX(0.5);
    transform: scaleX(0.5);
}
.weui_select_before .weui_cell_hd:before {
    content: " ";
    display: inline-block;
    -webkit-transform: rotate(45deg);
    transform: rotate(45deg);
    height: 6px;
    width: 6px;
    border-width: 2px 2px 0 0;
    border-color: #C8C8CD;
    border-style: solid;
    position: relative;
    top: -2px;
    position: absolute;
    top: 50%;
    right: 15px;
    margin-top: -3px;
}
.weui_select_before .weui_cell_bd {
    padding-left: 15px;
}
.weui_select_before .weui_cell_bd:after {
    display: none;
}
.weui_select_after {
    padding-left: 15px;
}
.weui_select_after .weui_select {
    padding-left: 0;
}
.weui_vcode {
    padding-top: 0;
    padding-right: 0;
    padding-bottom: 0;
}
.weui_vcode .weui_cell_ft img {
    margin-left: 5px;
    height: 44px;
    vertical-align: middle;
}
.weui_cell_switch {
    padding-top: 6px;
    padding-bottom: 6px;
}
.weui_switch {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    position: relative;
    width: 52px;
    height: 32px;
    border: 1px solid #DFDFDF;
    outline: 0;
    border-radius: 16px;
    box-sizing: border-box;
    background: #DFDFDF;
}
.weui_switch:before {
    content: " ";
    position: absolute;
    top: 0;
    left: 0;
    width: 50px;
    height: 30px;
    border-radius: 15px;
    background-color: #FDFDFD;
    -webkit-transition: -webkit-transform .3s;
    transition: -webkit-transform .3s;
    transition: transform .3s;
    transition: transform .3s, -webkit-transform .3s;
}
.weui_switch:after {
    content: " ";
    position: absolute;
    top: 0;
    left: 0;
    width: 30px;
    height: 30px;
    border-radius: 15px;
    background-color: #FFFFFF;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);
    -webkit-transition: -webkit-transform .3s;
    transition: -webkit-transform .3s;
    transition: transform .3s;
    transition: transform .3s, -webkit-transform .3s;
}
.weui_switch:checked {
    border-color: #04BE02;
    background-color: #04BE02;
}
.weui_switch:checked:before {
    -webkit-transform: scale(0);
    transform: scale(0);
}
.weui_switch:checked:after {
    -webkit-transform: translateX(20px);
    transform: translateX(20px);
}
.weui_uploader_hd {
    padding-top: 0;
    padding-right: 0;
    padding-left: 0;
}
.weui_uploader_hd .weui_cell_ft {
    font-size: 1em;
}
.weui_uploader_bd {
    margin-bottom: -4px;
    margin-right: -9px;
    overflow: hidden;
}
.weui_uploader_files {
    list-style: none;
}
.weui_uploader_file {
    float: left;
    margin-right: 9px;
    margin-bottom: 9px;
    width: 79px;
    height: 79px;
    background: no-repeat center center;
    background-size: cover;
}
.weui_uploader_status {
    position: relative;
}
.weui_uploader_status:before {
    content: " ";
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background-color: rgba(0, 0, 0, 0.5);
}
.weui_uploader_status .weui_uploader_status_content {
    position: absolute;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    color: #FFFFFF;
}
.weui_uploader_status .weui_icon_warn {
    display: block;
}
.weui_uploader_input_wrp {
    float: left;
    position: relative;
    margin-right: 9px;
    margin-bottom: 9px;
    width: 77px;
    height: 77px;
    border: 1px solid #D9D9D9;
}
.weui_uploader_input_wrp:before,
.weui_uploader_input_wrp:after {
    content: " ";
    position: absolute;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    background-color: #D9D9D9;
}
.weui_uploader_input_wrp:before {
    width: 2px;
    height: 39.5px;
}
.weui_uploader_input_wrp:after {
    width: 39.5px;
    height: 2px;
}
.weui_uploader_input_wrp:active {
    border-color: #999999;
}
.weui_uploader_input_wrp:active:before,
.weui_uploader_input_wrp:active:after {
    background-color: #999999;
}
.weui_uploader_input {
    position: absolute;
    z-index: 1;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}
.weui_msg {
    padding-top: 36px;
    text-align: center;
}
.weui_msg .weui_icon_area {
    margin-bottom: 30px;
}
.weui_msg .weui_text_area {
    margin-bottom: 25px;
    padding: 0 20px;
}
.weui_msg .weui_msg_title {
    margin-bottom: 5px;
    font-weight: 400;
    font-size: 20px;
}
.weui_msg .weui_msg_desc {
    font-size: 14px;
    color: #888;
}
.weui_msg .weui_opr_area {
    margin-bottom: 25px;
}
.weui_msg .weui_extra_area {
    margin-bottom: 15px;
    font-size: 14px;
    color: #888;
}
.weui_msg .weui_extra_area a {
    color: #61749B;
}
@media screen and (min-height: 438px) {
    .weui_extra_area {
        position: fixed;
        left: 0;
        bottom: 0;
        width: 100%;
        text-align: center;
    }
}
.weui_article {
    padding: 20px 15px;
    font-size: 15px;
}
.weui_article section {
    margin-bottom: 1.5em;
}
.weui_article h1 {
    font-size: 17px;
    font-weight: 400;
    margin-bottom: .75em;
}
.weui_article h2 {
    font-size: 16px;
    font-weight: 400;
    margin-bottom: .3em;
}
.weui_article h3 {
    font-weight: 400;
    font-size: 15px;
}
.weui_article * {
    max-width: 100%;
    box-sizing: border-box;
    word-wrap: break-word;
}
.weui_article p {
    margin: 10px 0;
}
.weui_tabbar {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    position: absolute;
    bottom: 0;
    width: 100%;
    background-color: #f7f7fa;
}
.weui_tabbar:before {
    content: " ";
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 1px;
    border-top: 1px solid #979797;
    color: #979797;
    -webkit-transform-origin: 0 0;
    transform-origin: 0 0;
    -webkit-transform: scaleY(0.5);
    transform: scaleY(0.5);
}
.weui_tabbar_item {
    display: block;
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    -ms-flex: 1;
    flex: 1;
    padding: 7px 0 0;
    -webkit-tap-highlight-color: transparent;
}
.weui_tabbar_item.weui_bar_item_on .weui_tabbar_label {
    color: #09BB07;
}
.weui_tabbar_icon {
    margin: 0 auto;
    width: 24px;
    height: 24px;
}
.weui_tabbar_icon img {
    display: block;
    width: 100%;
    height: 100%;
}
.weui_tabbar_icon + .weui_tabbar_label {
    margin-top: 5px;
}
.weui_tabbar_label {
    text-align: center;
    color: #888;
    font-size: 12px;
}
.weui_navbar {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    position: absolute;
    z-index: 1;
    top: 0;
    width: 100%;
    background-color: #fafafa;
}
.weui_navbar:after {
    content: " ";
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 1px;
    border-bottom: 1px solid #BCBAB6;
    color: #BCBAB6;
    -webkit-transform-origin: 0 100%;
    transform-origin: 0 100%;
    -webkit-transform: scaleY(0.5);
    transform: scaleY(0.5);
}
.weui_navbar + .weui_tab_bd {
    padding-top: 50px;
    padding-bottom: 0;
}
.weui_navbar_item {
    position: relative;
    display: block;
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    -ms-flex: 1;
    flex: 1;
    padding: 13px 0;
    text-align: center;
    font-size: 15px;
    -webkit-tap-highlight-color: transparent;
}
.weui_navbar_item:active {
    background-color: #ededed;
}
.weui_navbar_item.weui_bar_item_on {
    background-color: #eaeaea;
}
.weui_navbar_item:after {
    content: " ";
    position: absolute;
    right: 0;
    top: 0;
    width: 1px;
    height: 100%;
    border-right: 1px solid #cccccc;
    color: #cccccc;
    -webkit-transform-origin: 100% 0;
    transform-origin: 100% 0;
    -webkit-transform: scaleX(0.5);
    transform: scaleX(0.5);
}
.weui_navbar_item:last-child:after {
    display: none;
}
.weui_tab {
    position: relative;
    height: 100%;
}
.weui_tab_bd {
    box-sizing: border-box;
    height: 100%;
    padding-bottom: 55px;
    overflow: auto;
    -webkit-overflow-scrolling: touch;
}
.weui_tab_bd_item {
    display: none;
}
.weui_progress {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
}
.weui_progress_bar {
    background-color: #EBEBEB;
    height: 3px;
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    -ms-flex: 1;
    flex: 1;
}
.weui_progress_inner_bar {
    width: 0;
    height: 100%;
    background-color: #09BB07;
}
.weui_progress_opr {
    display: block;
    margin-left: 15px;
    font-size: 0;
}
.weui_panel {
    background-color: #FFFFFF;
    margin-top: 10px;
    position: relative;
    overflow: hidden;
}
.weui_panel:first-child {
    margin-top: 0;
}
.weui_panel:before {
    content: " ";
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 1px;
    border-top: 1px solid #E5E5E5;
    color: #E5E5E5;
    -webkit-transform-origin: 0 0;
    transform-origin: 0 0;
    -webkit-transform: scaleY(0.5);
    transform: scaleY(0.5);
}
.weui_panel:after {
    content: " ";
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 1px;
    border-bottom: 1px solid #E5E5E5;
    color: #E5E5E5;
    -webkit-transform-origin: 0 100%;
    transform-origin: 0 100%;
    -webkit-transform: scaleY(0.5);
    transform: scaleY(0.5);
}
.weui_panel_hd {
    padding: 14px 15px 10px;
    color: #999999;
    font-size: 13px;
    position: relative;
}
.weui_panel_hd:after {
    content: " ";
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 1px;
    border-bottom: 1px solid #E5E5E5;
    color: #E5E5E5;
    -webkit-transform-origin: 0 100%;
    transform-origin: 0 100%;
    -webkit-transform: scaleY(0.5);
    transform: scaleY(0.5);
    left: 15px;
}
.weui_panel_ft {
    padding: 10px 15px 12px;
    color: #999999;
    font-size: 14px;
    position: relative;
}
.weui_panel_ft:before {
    content: " ";
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 1px;
    border-top: 1px solid #E5E5E5;
    color: #E5E5E5;
    -webkit-transform-origin: 0 0;
    transform-origin: 0 0;
    -webkit-transform: scaleY(0.5);
    transform: scaleY(0.5);
    left: 15px;
}
.weui_panel_access .weui_panel_ft {
    display: block;
    color: #586C94;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}
.weui_panel_access .weui_panel_ft:active {
    background-color: #ECECEC;
}
.weui_panel_access .weui_panel_ft:after {
    content: " ";
    display: inline-block;
    -webkit-transform: rotate(45deg);
    transform: rotate(45deg);
    height: 6px;
    width: 6px;
    border-width: 2px 2px 0 0;
    border-color: #C7C7CC;
    border-style: solid;
    position: relative;
    top: -2px;
    position: absolute;
    right: 15px;
    top: 50%;
    margin-top: -4px;
}
.weui_media_box {
    padding: 15px;
    position: relative;
}
.weui_media_box:before {
    content: " ";
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 1px;
    border-top: 1px solid #E5E5E5;
    color: #E5E5E5;
    -webkit-transform-origin: 0 0;
    transform-origin: 0 0;
    -webkit-transform: scaleY(0.5);
    transform: scaleY(0.5);
    left: 15px;
}
.weui_media_box:first-child:before {
    display: none;
}
a.weui_media_box {
    color: #000000;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}
a.weui_media_box:active {
    background-color: #ECECEC;
}
.weui_media_box .weui_media_title {
    font-weight: 400;
    font-size: 17px;
    width: auto;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    word-wrap: normal;
    word-wrap: break-word;
    word-break: break-all;
}
.weui_media_box .weui_media_desc {
    color: #999999;
    font-size: 13px;
    line-height: 1.2;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
}
.weui_media_box.weui_media_text .weui_media_title {
    margin-bottom: 8px;
}
.weui_media_box.weui_media_text .weui_media_info {
    margin-top: 15px;
    padding-bottom: 5px;
    font-size: 13px;
    color: #CECECE;
    line-height: 1em;
    list-style: none;
    overflow: hidden;
}
.weui_media_box.weui_media_text .weui_media_info_meta {
    float: left;
    padding-right: 1em;
}
.weui_media_box.weui_media_text .weui_media_info_meta.weui_media_info_meta_extra {
    padding-left: 1em;
    border-left: 1px solid #CECECE;
}
.weui_media_box.weui_media_appmsg {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
}
.weui_media_box.weui_media_appmsg .weui_media_hd {
    margin-right: .8em;
    width: 60px;
    height: 60px;
    line-height: 60px;
    text-align: center;
}
.weui_media_box.weui_media_appmsg .weui_media_appmsg_thumb {
    width: 100%;
    max-height: 100%;
    vertical-align: middle;
}
.weui_media_box.weui_media_appmsg .weui_media_bd {
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    -ms-flex: 1;
    flex: 1;
    min-width: 0;
}
.weui_media_box.weui_media_small_appmsg {
    padding: 0;
}
.weui_media_box.weui_media_small_appmsg .weui_cells {
    margin-top: 0;
}
.weui_media_box.weui_media_small_appmsg .weui_cells:before {
    display: none;
}
.weui_grids {
    position: relative;
    overflow: hidden;
}
.weui_grids:before {
    content: " ";
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 1px;
    border-top: 1px solid #D9D9D9;
    color: #D9D9D9;
    -webkit-transform-origin: 0 0;
    transform-origin: 0 0;
    -webkit-transform: scaleY(0.5);
    transform: scaleY(0.5);
}
.weui_grids:after {
    content: " ";
    position: absolute;
    left: 0;
    top: 0;
    width: 1px;
    height: 100%;
    border-left: 1px solid #D9D9D9;
    color: #D9D9D9;
    -webkit-transform-origin: 0 0;
    transform-origin: 0 0;
    -webkit-transform: scaleX(0.5);
    transform: scaleX(0.5);
}
.weui_grid {
    position: relative;
    float: left;
    padding: 20px 10px;
    width: 33.33333333%;
    box-sizing: border-box;
}
.weui_grid:before {
    content: " ";
    position: absolute;
    right: 0;
    top: 0;
    width: 1px;
    height: 100%;
    border-right: 1px solid #D9D9D9;
    color: #D9D9D9;
    -webkit-transform-origin: 100% 0;
    transform-origin: 100% 0;
    -webkit-transform: scaleX(0.5);
    transform: scaleX(0.5);
}
.weui_grid:after {
    content: " ";
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 1px;
    border-bottom: 1px solid #D9D9D9;
    color: #D9D9D9;
    -webkit-transform-origin: 0 100%;
    transform-origin: 0 100%;
    -webkit-transform: scaleY(0.5);
    transform: scaleY(0.5);
}
.weui_grid:active {
    background-color: #E4E4E4;
}
.weui_grid_icon {
    width: 28px;
    height: 28px;
    margin: 0 auto;
}
.weui_grid_icon img {
    display: block;
    width: 100%;
    height: 100%;
}
.weui_grid_icon + .weui_grid_label {
    margin-top: 5px;
}
.weui_grid_label {
    display: block;
    text-align: center;
    color: #000;
    font-size: 14px;
}
.weui_dialog {
    position: fixed;
    z-index: 13;
    width: 85%;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    background-color: #FAFAFC;
    text-align: center;
    border-radius: 3px;
    overflow: hidden;
}
.weui_dialog_confirm .weui_dialog .weui_dialog_hd {
    padding: 1.2em 20px .5em;
}
.weui_dialog_confirm .weui_dialog .weui_dialog_bd {
    text-align: left;
}
.weui_dialog_hd {
    padding: 1.2em 0 .5em;
}
.weui_dialog_title {
    font-weight: 400;
    font-size: 17px;
}
.weui_dialog_bd {
    padding: 0 20px;
    font-size: 15px;
    color: #888;
    word-wrap: break-word;
    word-break: break-all;
}
.weui_dialog_ft {
    position: relative;
    line-height: 42px;
    margin-top: 20px;
    font-size: 17px;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
}
.weui_dialog_ft a {
    display: block;
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    -ms-flex: 1;
    flex: 1;
    color: #3CC51F;
    text-decoration: none;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}
.weui_dialog_ft a:active {
    background-color: #EEEEEE;
}
.weui_dialog_ft:after {
    content: " ";
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 1px;
    border-top: 1px solid #D5D5D6;
    color: #D5D5D6;
    -webkit-transform-origin: 0 0;
    transform-origin: 0 0;
    -webkit-transform: scaleY(0.5);
    transform: scaleY(0.5);
}
.weui_dialog_confirm .weui_dialog_ft a {
    position: relative;
}
.weui_dialog_confirm .weui_dialog_ft a:after {
    content: " ";
    position: absolute;
    left: 0;
    top: 0;
    width: 1px;
    height: 100%;
    border-left: 1px solid #D5D5D6;
    color: #D5D5D6;
    -webkit-transform-origin: 0 0;
    transform-origin: 0 0;
    -webkit-transform: scaleX(0.5);
    transform: scaleX(0.5);
}
.weui_dialog_confirm .weui_dialog_ft a:first-child:after {
    display: none;
}
.weui_btn_dialog.default {
    color: #353535;
}
.weui_btn_dialog.primary {
    color: #0BB20C;
}
@media screen and (min-width: 1024px) {
    .weui_dialog {
        width: 35%;
    }
}
.weui_toast {
    position: fixed;
    z-index: 3;
    width: 7.6em;
    min-height: 7.6em;
    top: 180px;
    left: 50%;
    margin-left: -3.8em;
    background: rgba(40, 40, 40, 0.75);
    text-align: center;
    border-radius: 5px;
    color: #FFFFFF;
}
.weui_icon_toast {
    margin: 22px 0 0;
    display: block;
}
.weui_icon_toast:before {
    content: '\EA08';
    color: #FFFFFF;
    font-size: 55px;
}
.weui_toast_content {
    margin: 0 0 15px;
}
.weui_loading_toast .weui_toast_content {
    margin-top: 64%;
    font-size: 14px;
}
.weui_loading {
    position: absolute;
    width: 0px;
    z-index: 2000000000;
    left: 50%;
    top: 38%;
}
.weui_loading_leaf {
    position: absolute;
    top: -1px;
    opacity: 0.25;
}
.weui_loading_leaf:before {
    content: " ";
    position: absolute;
    width: 8.14px;
    height: 3.08px;
    background: #d1d1d5;
    box-shadow: rgba(0, 0, 0, 0.0980392) 0px 0px 1px;
    border-radius: 1px;
    -webkit-transform-origin: left 50% 0px;
    transform-origin: left 50% 0px;
}
.weui_loading_leaf_0 {
    -webkit-animation: opacity-60-25-0-12 1.25s linear infinite;
    animation: opacity-60-25-0-12 1.25s linear infinite;
}
.weui_loading_leaf_0:before {
    -webkit-transform: rotate(0deg) translate(7.92px, 0px);
    transform: rotate(0deg) translate(7.92px, 0px);
}
.weui_loading_leaf_1 {
    -webkit-animation: opacity-60-25-1-12 1.25s linear infinite;
    animation: opacity-60-25-1-12 1.25s linear infinite;
}
.weui_loading_leaf_1:before {
    -webkit-transform: rotate(30deg) translate(7.92px, 0px);
    transform: rotate(30deg) translate(7.92px, 0px);
}
.weui_loading_leaf_2 {
    -webkit-animation: opacity-60-25-2-12 1.25s linear infinite;
    animation: opacity-60-25-2-12 1.25s linear infinite;
}
.weui_loading_leaf_2:before {
    -webkit-transform: rotate(60deg) translate(7.92px, 0px);
    transform: rotate(60deg) translate(7.92px, 0px);
}
.weui_loading_leaf_3 {
    -webkit-animation: opacity-60-25-3-12 1.25s linear infinite;
    animation: opacity-60-25-3-12 1.25s linear infinite;
}
.weui_loading_leaf_3:before {
    -webkit-transform: rotate(90deg) translate(7.92px, 0px);
    transform: rotate(90deg) translate(7.92px, 0px);
}
.weui_loading_leaf_4 {
    -webkit-animation: opacity-60-25-4-12 1.25s linear infinite;
    animation: opacity-60-25-4-12 1.25s linear infinite;
}
.weui_loading_leaf_4:before {
    -webkit-transform: rotate(120deg) translate(7.92px, 0px);
    transform: rotate(120deg) translate(7.92px, 0px);
}
.weui_loading_leaf_5 {
    -webkit-animation: opacity-60-25-5-12 1.25s linear infinite;
    animation: opacity-60-25-5-12 1.25s linear infinite;
}
.weui_loading_leaf_5:before {
    -webkit-transform: rotate(150deg) translate(7.92px, 0px);
    transform: rotate(150deg) translate(7.92px, 0px);
}
.weui_loading_leaf_6 {
    -webkit-animation: opacity-60-25-6-12 1.25s linear infinite;
    animation: opacity-60-25-6-12 1.25s linear infinite;
}
.weui_loading_leaf_6:before {
    -webkit-transform: rotate(180deg) translate(7.92px, 0px);
    transform: rotate(180deg) translate(7.92px, 0px);
}
.weui_loading_leaf_7 {
    -webkit-animation: opacity-60-25-7-12 1.25s linear infinite;
    animation: opacity-60-25-7-12 1.25s linear infinite;
}
.weui_loading_leaf_7:before {
    -webkit-transform: rotate(210deg) translate(7.92px, 0px);
    transform: rotate(210deg) translate(7.92px, 0px);
}
.weui_loading_leaf_8 {
    -webkit-animation: opacity-60-25-8-12 1.25s linear infinite;
    animation: opacity-60-25-8-12 1.25s linear infinite;
}
.weui_loading_leaf_8:before {
    -webkit-transform: rotate(240deg) translate(7.92px, 0px);
    transform: rotate(240deg) translate(7.92px, 0px);
}
.weui_loading_leaf_9 {
    -webkit-animation: opacity-60-25-9-12 1.25s linear infinite;
    animation: opacity-60-25-9-12 1.25s linear infinite;
}
.weui_loading_leaf_9:before {
    -webkit-transform: rotate(270deg) translate(7.92px, 0px);
    transform: rotate(270deg) translate(7.92px, 0px);
}
.weui_loading_leaf_10 {
    -webkit-animation: opacity-60-25-10-12 1.25s linear infinite;
    animation: opacity-60-25-10-12 1.25s linear infinite;
}
.weui_loading_leaf_10:before {
    -webkit-transform: rotate(300deg) translate(7.92px, 0px);
    transform: rotate(300deg) translate(7.92px, 0px);
}
.weui_loading_leaf_11 {
    -webkit-animation: opacity-60-25-11-12 1.25s linear infinite;
    animation: opacity-60-25-11-12 1.25s linear infinite;
}
.weui_loading_leaf_11:before {
    -webkit-transform: rotate(330deg) translate(7.92px, 0px);
    transform: rotate(330deg) translate(7.92px, 0px);
}
@-webkit-keyframes opacity-60-25-0-12 {
    0% {
        opacity: 0.25;
    }
    0.01% {
        opacity: 0.25;
    }
    0.02% {
        opacity: 1;
    }
    60.01% {
        opacity: 0.25;
    }
    100% {
        opacity: 0.25;
    }
}
@-webkit-keyframes opacity-60-25-1-12 {
    0% {
        opacity: 0.25;
    }
    8.34333% {
        opacity: 0.25;
    }
    8.35333% {
        opacity: 1;
    }
    68.3433% {
        opacity: 0.25;
    }
    100% {
        opacity: 0.25;
    }
}
@-webkit-keyframes opacity-60-25-2-12 {
    0% {
        opacity: 0.25;
    }
    16.6767% {
        opacity: 0.25;
    }
    16.6867% {
        opacity: 1;
    }
    76.6767% {
        opacity: 0.25;
    }
    100% {
        opacity: 0.25;
    }
}
@-webkit-keyframes opacity-60-25-3-12 {
    0% {
        opacity: 0.25;
    }
    25.01% {
        opacity: 0.25;
    }
    25.02% {
        opacity: 1;
    }
    85.01% {
        opacity: 0.25;
    }
    100% {
        opacity: 0.25;
    }
}
@-webkit-keyframes opacity-60-25-4-12 {
    0% {
        opacity: 0.25;
    }
    33.3433% {
        opacity: 0.25;
    }
    33.3533% {
        opacity: 1;
    }
    93.3433% {
        opacity: 0.25;
    }
    100% {
        opacity: 0.25;
    }
}
@-webkit-keyframes opacity-60-25-5-12 {
    0% {
        opacity: 0.270958333333333;
    }
    41.6767% {
        opacity: 0.25;
    }
    41.6867% {
        opacity: 1;
    }
    1.67667% {
        opacity: 0.25;
    }
    100% {
        opacity: 0.270958333333333;
    }
}
@-webkit-keyframes opacity-60-25-6-12 {
    0% {
        opacity: 0.375125;
    }
    50.01% {
        opacity: 0.25;
    }
    50.02% {
        opacity: 1;
    }
    10.01% {
        opacity: 0.25;
    }
    100% {
        opacity: 0.375125;
    }
}
@-webkit-keyframes opacity-60-25-7-12 {
    0% {
        opacity: 0.479291666666667;
    }
    58.3433% {
        opacity: 0.25;
    }
    58.3533% {
        opacity: 1;
    }
    18.3433% {
        opacity: 0.25;
    }
    100% {
        opacity: 0.479291666666667;
    }
}
@-webkit-keyframes opacity-60-25-8-12 {
    0% {
        opacity: 0.583458333333333;
    }
    66.6767% {
        opacity: 0.25;
    }
    66.6867% {
        opacity: 1;
    }
    26.6767% {
        opacity: 0.25;
    }
    100% {
        opacity: 0.583458333333333;
    }
}
@-webkit-keyframes opacity-60-25-9-12 {
    0% {
        opacity: 0.687625;
    }
    75.01% {
        opacity: 0.25;
    }
    75.02% {
        opacity: 1;
    }
    35.01% {
        opacity: 0.25;
    }
    100% {
        opacity: 0.687625;
    }
}
@-webkit-keyframes opacity-60-25-10-12 {
    0% {
        opacity: 0.791791666666667;
    }
    83.3433% {
        opacity: 0.25;
    }
    83.3533% {
        opacity: 1;
    }
    43.3433% {
        opacity: 0.25;
    }
    100% {
        opacity: 0.791791666666667;
    }
}
@-webkit-keyframes opacity-60-25-11-12 {
    0% {
        opacity: 0.895958333333333;
    }
    91.6767% {
        opacity: 0.25;
    }
    91.6867% {
        opacity: 1;
    }
    51.6767% {
        opacity: 0.25;
    }
    100% {
        opacity: 0.895958333333333;
    }
}
.weui_mask {
    position: fixed;
    z-index: 1;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background: rgba(0, 0, 0, 0.6);
}
.weui_mask_transparent {
    position: fixed;
    z-index: 1;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
}
.weui_mask_transition {
    display: none;
    position: fixed;
    z-index: 1;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background: rgba(0, 0, 0, 0);
    -webkit-transition: background .3s;
    transition: background .3s;
}
.weui_fade_toggle {
    background: rgba(0, 0, 0, 0.6);
}
.weui_actionsheet {
    position: fixed;
    left: 0;
    bottom: 0;
    -webkit-transform: translate(0, 100%);
    transform: translate(0, 100%);
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    z-index: 2;
    width: 100%;
    background-color: #EFEFF4;
    -webkit-transition: -webkit-transform .3s;
    transition: -webkit-transform .3s;
    transition: transform .3s;
    transition: transform .3s, -webkit-transform .3s;
}
.weui_actionsheet_menu {
    background-color: #FFFFFF;
}
.weui_actionsheet_action {
    margin-top: 6px;
    background-color: #FFFFFF;
}
.weui_actionsheet_cell {
    position: relative;
    padding: 10px 0;
    text-align: center;
    font-size: 18px;
}
.weui_actionsheet_cell:before {
    content: " ";
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 1px;
    border-top: 1px solid #D9D9D9;
    color: #D9D9D9;
    -webkit-transform-origin: 0 0;
    transform-origin: 0 0;
    -webkit-transform: scaleY(0.5);
    transform: scaleY(0.5);
}
.weui_actionsheet_cell:active {
    background-color: #ECECEC;
}
.weui_actionsheet_cell:first-child:before {
    display: none;
}
.weui_actionsheet_toggle {
    -webkit-transform: translate(0, 0);
    transform: translate(0, 0);
}
.weui_search_bar {
    position: relative;
    padding: 8px 10px;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    box-sizing: border-box;
    background-color: #EFEFF4;
    z-index: 2;
}
.weui_search_bar:before {
    content: " ";
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 1px;
    border-top: 1px solid #C7C7C7;
    color: #C7C7C7;
    -webkit-transform-origin: 0 0;
    transform-origin: 0 0;
    -webkit-transform: scaleY(0.5);
    transform: scaleY(0.5);
}
.weui_search_bar:after {
    content: " ";
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 1px;
    border-bottom: 1px solid #C7C7C7;
    color: #C7C7C7;
    -webkit-transform-origin: 0 100%;
    transform-origin: 0 100%;
    -webkit-transform: scaleY(0.5);
    transform: scaleY(0.5);
}
.weui_search_bar.weui_search_focusing .weui_search_cancel {
    display: block;
}
.weui_search_bar.weui_search_focusing .weui_search_text {
    display: none;
}
.weui_search_outer {
    position: relative;
    -webkit-box-flex: 1;
    -webkit-flex: auto;
    -ms-flex: auto;
    flex: auto;
    background-color: #EFEFF4;
}
.weui_search_outer:after {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 200%;
    height: 200%;
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
    -webkit-transform-origin: 0 0;
    transform-origin: 0 0;
    border-radius: 10px;
    border: 1px solid #E6E6EA;
    box-sizing: border-box;
    background: #FFFFFF;
}
.weui_search_inner {
    position: relative;
    padding-left: 30px;
    padding-right: 30px;
    height: 100%;
    width: 100%;
    box-sizing: border-box;
    z-index: 1;
}
.weui_search_inner .weui_search_input {
    padding: 4px 0;
    width: 100%;
    height: 1.42857143em;
    border: 0;
    font-size: 14px;
    line-height: 1.42857143em;
    box-sizing: content-box;
    background: transparent;
}
.weui_search_inner .weui_search_input:focus {
    outline: none;
}
.weui_search_inner .weui_icon_search {
    position: absolute;
    left: 10px;
    top: -2px;
    line-height: 28px;
}
.weui_search_inner .weui_icon_clear {
    position: absolute;
    top: -2px;
    right: 0;
    padding: 0 10px;
    line-height: 28px;
}
.weui_search_text {
    position: absolute;
    top: 1px;
    right: 1px;
    bottom: 1px;
    left: 1px;
    z-index: 2;
    border-radius: 3px;
    text-align: center;
    color: #9B9B9B;
    background: #FFFFFF;
}
.weui_search_text span {
    display: inline-block;
    font-size: 14px;
    vertical-align: middle;
}
.weui_search_text .weui_icon_search {
    margin-right: 5px;
}
.weui_search_cancel {
    display: none;
    margin-left: 10px;
    line-height: 28px;
    color: #09BB07;
}
.weui_search_input:not(:valid) ~ .weui_icon_clear {
    display: none;
}
input[type="search"]::-webkit-search-decoration,
input[type="search"]::-webkit-search-cancel-button,
input[type="search"]::-webkit-search-results-button,
input[type="search"]::-webkit-search-results-decoration {
    display: none;
}

/*# sourceMappingURL=data:application/json;base64,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 */