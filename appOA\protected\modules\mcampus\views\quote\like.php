<div class="modal fade" id="quote_like" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" v-if="likeObject">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel"><?php echo Yii::t('quote', 'Like Records')?></h4>
            </div>
            <div class="modal-body">
                <div class="text-03 mb10">{{ likeObject.quote_data.content }}</div>
                <div class="text-02 mb16">— {{ likeObject.quote_data.author }}</div>
                <div>
                    <span class="label label-info"><i class="glyphicon glyphicon-ok-sign"></i> <?php echo Yii::t('quote', 'Used :count time(s)', array(':count' => '{{ likeObject.quote_data.useTotal }}'))?></span>
                    <span class="label label-info"><i class="glyphicon glyphicon-thumbs-up"></i> {{ likeObject.quote_data.like_count }} <?php echo Yii::t('quote', 'Like(s)')?></span>
                </div>
                <hr>
                <div v-for="(_, index) in likeObject.list">
                    <div class="mb16">
                        <span class="title-02">{{ _.use_title }}</span>
                        <span class="label label-info"><i class="glyphicon glyphicon-thumbs-up"></i> {{ _.like_count }}</span>
                    </div>
                    <div class="mb20 row">
                        <div class="col-md-3" v-for="(__, _index) in _.like_list">
                            <div class="list-02 mb16">
                                <div><img class="img-circle" :src="likeObject.userInfo[__.user_id].photoUrl"></div>
                                <div class="ml8 linkName"><div>{{ likeObject.userInfo[__.user_id].name }}</div></div>
                            </div>
                            
                        </div>
                        <div class="clearfix"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
