
<div class="container-fluid">
	 <ol class="breadcrumb">
	     <li><?php echo CHtml::link(Yii::t('site','HQ Operations'), array('default/index'));?></li>
	     <li><?php echo CHtml::link(Yii::t('site','校园管理'), array('default/index'));?></li>
	     <li class="active"><?php echo Yii::t('site','预算人数') ;?></li>
	 </ol>
	 <div class="row">
	 	<div class="col-md-2">
	 	    <div class="list-group">
	 	        <?php foreach ($schools as $id => $name) : ?>
	 	            <span class="list-group-item" onClick="switchSchool(this, '<?php echo $id ?>')"><?php echo $name; ?></span>
	 	        <?php endforeach; ?>
	 	    </div>
	 	</div>
	 	<div class="col-md-10">
	 		<form class="J_ajaxForm" method="post" action="<?php echo $this->createUrl('saveBudget'); ?>"  style="display: none">
	 			<div id="calendar" class="col-md-2 mb5">
	 				
	 			</div>
	 			<div class="col-md-12">			
		 		<table id="table" class="table table-bordered">
		 			
		 		</table>
		 		<input type="hidden" name="schoolid" id="schoolid">
		 		<div>
		 		<button class="btn btn-primary J_ajax_submit_btn pull-right" type="button">保存</button>
		 		</div>
			 	</div>
	 		</form>
	 	</div>
	</div>
</div>

<script>
	var _yid = 0;
	var _schoolid = '';
	function switchSchool(_this, schoolid) {
		_yid = 0;
		_schoolid = schoolid;
		$('#schoolid').val(schoolid);
		$('.list-group-item').removeClass('active');
		$(_this).addClass('active');
		refreshData();
	}

	function switchCalendar() {
		_yid = $('#yid').val();
		refreshData();
	}

	function refreshData() {
		$.ajax({
		    type: 'post',
		    url: '<?php echo $this->createUrl('getBudget')?>',
		    data: {schoolid: _schoolid, yid: _yid},
		    dataType: 'json',
		    async: false
		}).done(function(ret){
		    data = ret;
		    if (data.state == 'success') {
		    	// 构建校历选择框
		    	var input = '<select onchange="switchCalendar()" name="yid" id="yid" class="form-control" >';
		    	$.each(data.data.schoolyear, function (index, value) {
		    		var selected = index == data.data.yid ? 'selected' : '';
		    		input += '<option '+selected+' value="'+index+'">'+value+'</option>';
		    	})
		    	input += '</select>';
		    	$('#calendar').empty();
		    	$('#calendar').append(input);
		    	// 构建 table 表格
		    	$('#table').parent().parent().show();
		    	$('#table').empty();
		    	var html = '<tr><td style="width:100px"></td>';
		    	var total = '';
		    	$.each(data.data.month, function (monthindex, value) {
		    		var span = ' <span onClick="copyData(this)" class="glyphicon glyphicon-transfer" aria-hidden="true" title="复制前一个月"></span>';
		    		if (monthindex == 0) {
		    			span = '';
		    		}
		    		html += '<td>' +value+span+ '</td>';
		    		total += '<td>' +(data.data.monthTotal[value] ? data.data.monthTotal[value] : 0 )+ '</td>';
		    	});
		    	html += '</tr>';

		    	$.each(data.data.classes, function (index, classid) {
		    		html += '<tr><td>' +data.data.items[classid].title+ '</td>';
		    		$.each(data.data.items[classid].month, function (index2, num) {
		    			html += '<td style="width:40px"> <input onChange="numChange(this)" tabindex="'+index2+'" style="width:40px" name="items['+classid+']['+index2+']"type="number" value="' +num+ '" /></td>';
		    		})
		    		html += '</tr>';
		    	})
		    	html += '<tr><td>Total</td>' +total+ '</tr>'
	    		$('#table').append(html);
		    } else {
		        resultTip({"msg": "<?php echo Yii::t("attends", "failed"); ?>"})
		    }
		});
	}

	function copyData(_this) {
		var index2 = $(_this).parent()[0].cellIndex;
		var inputs = $('#table tr').find('td:eq(\''+index2+'\') input');
		$.each(inputs, function (index, value) {
			var obj = $('#table tr').find('td:eq('+(index2-1)+') input')[index];
			$(inputs[index]).val($(obj).val());
		});
		numChange(_this);
		
	}

	function numChange(_this) {
		var index = $(_this).parent()[0].cellIndex;
		var tds = $('#table tr').find('td:eq('+(index)+') input');
		var total = 0;
		$.each(tds, function (i, obj) {
			var num = $(obj).val();
			total += Number(num);
		});
		var td = $('#table tr:eq(-1)').find('td:eq('+(index)+')');
		$(td).text(total);
	}
</script>