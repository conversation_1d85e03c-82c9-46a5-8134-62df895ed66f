<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site','System Management'), array('/msettings'))?></li>
        <li class="active">部门职位管理</li>
    </ol>
    <div class="row">
        <div class="col-md-2">
            <?php

            foreach(Branch::branchTypes() as $bkey => $bval){
                $mainMenu[] = array(
                    'label' => $bval,
                    'url' => array("//msettings/dept/index","category"=>$bkey),
                );
            }

            $this->widget('zii.widgets.CMenu',array(
                'items'=> $mainMenu,
                'id'=>'pageCategory',
                'htmlOptions'=>array('class'=>'nav nav-pills nav-stacked text-right background-gray mb10'),
                'activeCssClass'=>'active',
                'itemCssClass'=>''
            ));
            ?>
        </div>
        <div class="col-md-10">
            <div class="mb10">
                <a href="<?php echo $this->createUrl('deptEdit', array('category'=>$category));?>" class="btn btn-primary J_dialog" title="添加部门">
                    <span class="glyphicon glyphicon-plus"></span>
                    添加部门
                </a>
                <a href="<?php echo $this->createUrl('titleEdit', array('category'=>$category));?>" class="btn btn-primary J_dialog" title="添加职位">
                    <span class="glyphicon glyphicon-plus"></span>
                    添加职位
                </a>
            </div>
            <?php
            if($items):
                foreach($items as $item):
            ?>
            <div class="row">
                <div class="col-md-12">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <div class="pull-left"><?php echo CommonUtils::autoLang($item->cn_name, $item->en_name)?></div>
                            <div class="pull-right">
                                <?php echo CHtml::link('<i class="glyphicon glyphicon-pencil"></i>',
                                    array('deptEdit', 'id'=>$item->id),
                                    array('class'=>'J_dialog btn btn-info btn-xs', 'title'=>Yii::t('global', 'Edit')));?>
                                <?php echo CHtml::link('<i class="glyphicon glyphicon-trash"></i>',
                                    array('deptDel', 'id'=>$item->id),
                                    array('class'=>'J_ajax_del btn btn-danger btn-xs', 'title'=>Yii::t('global', 'Delete')));?>
                            </div>
                            <div class="clearfix"></div>
                        </div>
                        <div class="panel-body">
                            <?php foreach($item->title as $title):?>
                                <div class="mb10">
                                    <?php echo CHtml::link('<i class="glyphicon glyphicon-pencil"></i>',
                                        array('titleEdit', 'id'=>$title->position->id),
                                        array('class'=>'J_dialog btn btn-info btn-xs', 'title'=>Yii::t('global', 'Edit')));?>
                                    <?php echo CHtml::link('<i class="glyphicon glyphicon-trash"></i>',
                                        array('titleDel', 'id'=>$title->position->id),
                                        array('class'=>'J_ajax_del btn btn-danger btn-xs', 'title'=>Yii::t('global', 'Delete')));?>
                                    <?php echo CommonUtils::autoLang($title->position->cn_name, $title->position->en_name)?>
                                </div>
                            <?php endforeach;?>
                        </div>
                    </div>
                </div>
            </div>
            <?php
            endforeach;
            endif;
            ?>
        </div>
    </div>
</div>