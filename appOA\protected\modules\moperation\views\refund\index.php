<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site','HQ Operations'), array('default/index'));?></li>
        <li><?php echo CHtml::link(Yii::t('site','校园管理'), array('default/index'));?></li>
        <li class="active"><?php echo Yii::t('site','批量退费') ;?></li>
    </ol>
    <div class="row">
        <div class="col-md-12">
            <form action="<?php echo $this->createUrl('previewData'); ?>" method="post" class="form-inline J_ajaxForm">
                <input name="yid" type="text" class="form-control form-group" placeholder="yid">
                <select name="type" class="form-control">
                    <option value="tuition">学费</option>
                    <option value="lunch">餐费</option>
                    <option value="bus">校车费</option>
                </select>
                <input name="title" type="text" class="form-control form-group" placeholder="Title">
                <input name="start" type="text" class="form-control form-group" id="datepicker_start" placeholder="请选择开始日期">
                <input name="end" type="text" class="form-control form-group" id="datepicker_end" placeholder="请选择结束日期">
                <input name="csv" type="file" class="form-group" accept=".csv">
                <button class="form-control btn btn-primary J_ajax_submit_btn">Load</button>
            </form>
        </div>
        <div class="col-md-12">
            <div id="main_data" style="display: none;">
                <table class="table table-striped mt20">
                    <thead>
                    <tr>
                        <th style="width: 100px;">Child ID</th>
                        <th style="width: 100px;">Amount</th>
                        <th style="width: 100px;">Yid</th>
                        <th style="width: 100px;">Type</th>
                        <th>Title</th>
                        <th style="width: 100px;">Start Date</th>
                        <th style="width: 100px;">End Date</th>
                        <th style="width: 200px;">Result</th>
                    </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
                <div class="text-right">
                    <button class="btn btn-primary" onclick="process(0)" id="exec">执行退费</button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    $("#datepicker_start").datepicker({
        dateFormat: "yy-mm-dd",
    });
    $("#datepicker_end").datepicker({
        dateFormat: "yy-mm-dd",
    });

    let pageData;

    function callback(data) {
        $('#main_data').show()
        $('#main_data table tbody').html('')
        data.map((val, index) => {
            $('#main_data table tbody').append('<tr id="item_'+index+'">' +
                '<td>'+val.childid+'</td>' +
                '<td>'+val.amount+'</td>' +
                '<td>'+val.yid+'</td>' +
                '<td>'+val.type+'</td>' +
                '<td>'+val.title+'</td>' +
                '<td>'+val.start+'</td>' +
                '<td>'+val.end+'</td>' +
                '<td class="result"></td>' +
                '</tr>');
        })
        pageData = data
        $('#exec').removeAttr('disabled')
    }

    function process(i) {
        if (typeof pageData[i] != "undefined") {
            if (i === 0) {
                $('#exec').attr('disabled', 'disabled')
            }
            const val = pageData[i]
            $.ajax({
                type: 'post',
                url: '<?php echo $this->createUrl("process"); ?>',
                dataType: 'json',
                // async: false,
                data: {
                    yid: val.yid,
                    type: val.type,
                    title: val.title,
                    start: val.start,
                    end: val.end,
                    childid: val.childid,
                    amount: val.amount
                }
            }).done((data) => {
                if (data.message) {
                    $('#item_' + i + ' td.result').html( data.message )
                    $('#item_' + i).addClass('danger')
                } else {
                    $('#item_' + i + ' td.result').html( 'OK.' )
                }
                process(++i)
            })
        }
    }
</script>
