<div>
    <div>
        <div class='ml8 pb24 flex1 width100'>
            <div class='font14 color3 fontBold'>财务</div>
            <div class='mt16 form-horizontal font14'>
                <div class='flex flexStart mb20'>
                    <div class='color6 flexWidth'><?php echo Yii::t('withdrawal','Refund');?></div>
                    <div class='flex1 color3'> 朱丹 (<PERSON>) </div>
                </div>
                <div class='flex mb20 flexStart'>
                    <div class='color6 flexWidth'><?php echo Yii::t('withdrawal','Comment');?></div>
                    <div class='flex1 color3'>2024.04.19</div>
                </div>
            </div>
        </div>
        <div class='ml8 pb24 flex1 width100'>
            <div class='font14 color3 fontBold'><?php echo Yii::t('withdrawal','Admissions confirmation');?></div>
            <div class='mt16 form-horizontal font14'>
                <div class='flex flexStart mb20'>
                    <div class='color6 flexWidth'><?php echo Yii::t('withdrawal','Final student status');?></div>
                    <div class='flex1 color3'> 朱丹 (Daniel Williams) </div>
                </div>
                <div class='flex mb20 flexStart'>
                    <div class='color6 flexWidth'><?php echo Yii::t('withdrawal','Final withdrawal date');?></div>
                    <div class='flex1 color3'>2024.04.19</div>
                </div>
                <div class='flex mb20 flexStart'>
                    <div class='color6 flexWidth'><?php echo Yii::t('withdrawal','Ultimate withdrawal reason');?></div>
                    <div class='flex1'>
                        <div class='color3'>学校</div>
                        <div class='color6 mt4'>备注：这是家长填的备注</div>
                    </div>
                </div>
                <div class='flex mb20 flexStart'>
                    <div class='color6 flexWidth'>学籍转移状态</div>
                    <div class='flex1 color3'>2024.04.19</div>
                </div>
                <div class='flex mb20 flexStart'>
                    <div class='color6 flexWidth'><?php echo Yii::t('withdrawal','Comment');?></div>
                    <div class='flex1 color3'>2024.04.19</div>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
     lib()
     function lib(){
        let forms=childDetails.forms[0]
        var totalEnd=0
        let amountTable = forms.return.filter((i) => i.key != 'enrollment' && i.key != 'insurance')
        for(let k=0;k<amountTable.length;k++){
            totalEnd+=  Number(amountTable[k].data.totalAmount)
        }
    }
</script>
<style>
    .rejectInput{
        display:none
    }
    .pushWechat{
        background: #F0F5FB;
        border-radius: 4px;
        padding:24px;
    }
    .financeStep{
        width: 28px;
        height: 28px;
        background: #E9F0F9;
        display: inline-block;
        font-size: 18px;
        border-radius: 50%;
        text-align: center;
        line-height: 28px;
        color: #4D88D2;
    }
    .financeStep1{
        width: 28px;
        height: 28px;
        background: #4D88D2;
        display: inline-block;
        font-size: 14px;
        border-radius: 50%;
        text-align: center;
        line-height: 28px;
        color: #fff;
    }
    .financeLine{
        position: absolute;
        width: 1px;
        height: 100%;
        left: 13px;
        top: 28px;
        background: #4D88D2;
    }
    .resubmit{
        background: #F0F5FB;
        border-radius: 2px;
        color: #4D88D2;
        padding:4px 6px;
        font-size:12px;
        margin-left:16px;
    }
</style>