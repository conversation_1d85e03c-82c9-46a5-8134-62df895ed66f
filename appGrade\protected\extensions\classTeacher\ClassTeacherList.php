<?php
class ClassTeacherList extends CWidget{
	public $classId;
    public $cssFile;
    public $assetsUrl;
	
	public function init(){
		Yii::import('common.models.classTeacher.*');

//        if($this->assetsUrl===null)
//			$this->assetsUrl=Yii::app()->getAssetManager()->publish(dirname(__FILE__) . '/assets', false, -1, YII_DEBUG);

        $cs = Yii::app()->clientScript;
        if (false !== $this->cssFile)
        {
            if (null === $this->cssFile)
//                $this->cssFile = $this->assetsUrl . '/teacher.css';
                $this->cssFile = Yii::app()->theme->baseUrl.'/widgets/classTeacher/teacher.css?t='.Yii::app()->params['refreshAssets'];
            $cs->registerCssFile($this->cssFile);
//			$cs->registerCssFile(Yii::app()->theme->baseUrl.'/css/widgetbase.css');
			$cs->registerCssFile(Yii::app()->theme->baseUrl.'/css/widgetbase.css?t='.Yii::app()->params['refreshAssets']);
        }
	}
	
	public function run(){
        Mims::LoadHelper('HtoolKits');

        $class_model = IvyClass::model()->findByPk($this->classId);

        $criteria = new CDbCriteria();
        $criteria->compare('classid', $this->classId);
        $criteria->order = 'isheadteacher DESC,weight ASC';
        $Teachers = ClassTeacher::model()->with('userWithProfile', 'staffInfo')->findAll($criteria);
        $country = Country::model()->getData();

        $ts = array();
        $posIds = array();
        $pos = array();
        foreach ($Teachers as $teacher){
            $ts[$teacher->teacherid]=$teacher;
            $posIds[$teacher->userWithProfile->profile->occupation_en] = $teacher->userWithProfile->profile->occupation_en;
        }
        
        if ($posIds) {
            Yii::import('common.models.hr.HrPosition');
            $criteria = new CDbCriteria();
            $criteria->compare('id', $posIds);
            $items = HrPosition::model()->findAll($criteria);
            foreach ($items as $item) {
                $pos[$item->id] = $item->getName();
            }
        }

        $this->render('teacher',array('Teachers'=>$ts, 'class_model'=>$class_model, 'country'=>$country, 'pos'=>$pos));
	}
	
}
