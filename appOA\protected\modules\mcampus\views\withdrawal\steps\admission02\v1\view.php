<div>
    <div class='admissionData'>
        <div class="panel panel-default">
            <div class="panel-heading"><?php echo Yii::t('withdrawal','Admissions confirmation');?></div>
            <div class="panel-body">
                <div class='ml8'>
                    <div class=' form-horizontal font14'>
                        <div class='notTalkData'>
                            <div class='flex flexStart mb20'>
                                <div class='color6 flexWidth'><?php echo Yii::t('withdrawal','Final student status');?></div>
                                <div class='flex1 color3'>
                                    <label class="checkbox-inline pt0 lableHeight">
                                        <input type="checkbox" class='notTalk' name="student_status"  value="1" disabled> <?php echo Yii::t('withdrawal','Completed');?>
                                    </label>
                                </div>
                            </div>
                            <div class='flex mb20 flexStart'>
                                <div class='color6 flexWidth'><?php echo Yii::t('withdrawal','Final withdrawal date');?></div>
                                <div class='flex1'>
                                    <input type="text" class="form-control length_3 pull-left mr20" disabled name='withdrawal_date'   id="dropout_date"   placeholder="<?php echo Yii::t("newDS", "Select a date");?>"  >
                                </div>
                            </div>
                            <div class='flex mb20 flexStart'>
                                <div class='color6 flexWidth'><?php echo Yii::t('withdrawal','Ultimate withdrawal reason');?></div>
                                <div class='flex1'>
                                    <select class="form-control length_3 mb12" disabled name='withdrawal_reason' id='withdrawal_reason'>
                                        <option value=""><?php echo Yii::t('newDS','Please select');?></option>
                                    </select>
                                    <input type="text" class="form-control" disabled  name='withdrawal_reason_memo' placeholder="<?php echo Yii::t('withdrawal','Comment');?>">
                                </div>
                            </div>
                            <div class='flex  flexStart'>
                                <div class='color6 flexWidth'><?php echo Yii::t('withdrawal','Comment');?></div>
                                <div class='flex1'>
                                    <textarea class="form-control" name='memo' rows="3" disabled></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    // 第一步
    initDate()
    function initDate() {
        $('input[name="applicationId"]').val(container.applicationId)
        $('input[name="node"]').val(container.stepName)
        let indexData=container.indexDataList
        for(let key in indexData.reasonList){
            let option='<option value='+key+'>'+indexData.reasonList[key]+'</option>'
            $('#withdrawal_reason').append(option)
        }
        $('#dropout_date').datepicker({
            dateFormat: 'yy-mm-dd',
            selectOtherMonths: false,
            showOtherMonths: false,
            changeMonth: true,
            changeYear: true,
            firstDay: 0,
            onSelect: function (date,str) {
                $('#dropout_date').val(date)
            }
        });
        let data=childDetails.forms[0].data
        if(data){
            $('#withdrawal_reason').val(data.withdrawal_reason);
            $('input[name="student_status"][value='+data.student_status+']').prop('checked', true);
            $('input[name="withdrawal_date"]').val(data.withdrawal_date)
            if(data.withdrawal_reason_memo!=null){
                $('input[name="withdrawal_reason_memo"]').val(data.withdrawal_reason_memo)
            }
            if(data.memo!=null){
                $('textarea[name="memo"]').val(data.memo)
            }
        }
    }
    
</script>
<style>
    .avatar20{
        width: 20px;
        height: 20px;
        border-radius: 50%;
        object-fit: cover;
    }
    .addTalk{
        padding:4px;
        display:inline-flex;
        background: #F2F3F5;
        border-radius: 2px;
        margin-left:16px;
        margin-bottom:6px
    }
    .pt0{
        padding-top:0 !important
    }
</style>