<?php

class SchoolNews extends CWidget {

    public $schoolId;
    public $yId;
    public $weekNum;
    public $cssFile;
    public $assetsUrl;

    public function init() {
        Yii::import('ext.schoolnews.models.*');

//        if($this->assetsUrl===null)
//			$this->assetsUrl=Yii::app()->getAssetManager()->publish(dirname(__FILE__) . '/assets', false, -1, YII_DEBUG);

        $cs = Yii::app()->clientScript;
        if (false !== $this->cssFile) {
            if (null === $this->cssFile)
            //$this->cssFile = $this->assetsUrl . '/css.css';
                $this->cssFile = Yii::app()->theme->baseUrl . '/css/portfolio.css';
            $cs->registerCssFile($this->cssFile);
        }
    }

    public function run() {
        $SNotesModel = array();
        $CDProfile = array();
        if ($this->schoolId) {
            Mims::LoadHelper('HtoolKits');

            $criteria = new CDbCriteria();
            $criteria->condition = "t.en_content <> ''";
            $criteria1 = new CDbCriteria();
            $criteria1->condition = "t.en_important <> ''";
            $criteria->mergeWith($criteria1, false);
            $criteria->compare('schoolid', $this->schoolId);
            if ($this->weekNum && $this->yId) {
                $criteria->compare('t.yid', $this->yId);
                $criteria->compare('t.weeknumber', $this->weekNum);
            } else {
                $criteria->order = 't.updated_timestamp desc';
                $criteria->limit = 1;
            }
            $criteria->compare('t.stat', 20);
            $criteria->compare('t.type', 10);
            if ($this->weekNum && $this->yId) {
                $SNotesModel = NotesSchCla::model()->find($criteria);
            } else {
                $SNotesModel = NotesSchCla::model()->find($criteria);
                if ($SNotesModel) {
                    $weekInfoModel = CalendarWeek::model()->findByAttributes(array('weeknumber' => $SNotesModel->weeknumber, 'yid' => $SNotesModel->yid));
                    $SNotesModel->weekInfo = $weekInfoModel;
                }
            }
            $CDProfile = null;
            if ($SNotesModel) {
                $cd_uids = $SNotesModel->cd_uids;
                $cd_uid_str = null;
                if (!empty($cd_uids)) {
                    $cd_uid_str = explode(',', $cd_uids);
                    $CDProfile = User::model()->with('profile', 'staffInfo')->findAllByPk($cd_uid_str);
                }
            }
        }
        if ($SNotesModel)
            $this->render('schoolnews', array('SNotesModel' => $SNotesModel, 'CDProfile' => $CDProfile, 'weeknum' => $this->weekNum, 'yid' => $this->yId));
    }

}