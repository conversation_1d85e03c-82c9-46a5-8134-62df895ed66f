<style>
	[v-cloak] {
		display: none;
	}
</style>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site','HQ Operations'), array('/moperation'))?></li>
        <li><?php echo CHtml::link(Yii::t('site','问卷管理'), array('option'))?></li>
        <li class="active"><?php echo Yii::t('site','选项管理') ?></li>
    </ol>

    <div class="row">
        <div class="col-md-2 col-sm-2">
            <?php

            $this->widget('zii.widgets.CMenu',array(
                'items'=> $this->getSubMenu(),
                'id'=>'pageCategory',
                'htmlOptions'=>array('class'=>'nav nav-pills nav-stacked text-left background-gray'),
                'activeCssClass'=>'active',
                'itemCssClass'=>''
            ));
            ?>
        </div>
        <div class="col-md-10 col-sm-10" id='view' v-cloak>
			<div class="panel panel-default">
			  <div class="panel-heading">{{data.surveyTitle}}</div>
			  <div class="panel-body">
			  	 <div v-for='(item,key,index) in data.data'>
			  	 	<h4>
				  	 	{{index+1}}.<span v-if='item.binary_flag=="0" || item.binary_flag=="1"'>（可选填）</span>
						<span v-if='item.binary_flag=="2" || item.binary_flag=="3" '>（必填）</span>{{item.title}}
			  	 	</h4>
			  	 	<ul class="list-unstyled ml15">
			  	 		<li v-for='(items,idx) in item.data' class="p2">
			  	 			<div class="checkbox"  v-if='item.status==2'>
							  <label>
							    <input type="checkbox" :value="items.option_value">{{items.title}}
							  </label>
							</div>
							<div class="radio"  v-if='item.status==1'>
							  <label>
							    <input type="radio" :name='index' :value="items.option_value">{{items.title}}
							  </label>
							</div>
			  	 		</li>
			  	 	</ul>
			  	 	<div class="form-group ml15" v-if='item.binary_flag==3 || item.binary_flag==1'>
					    <label for="exampleInputEmail1">备注</label>
					    <textarea class="form-control" rows="3"></textarea>
					</div>
			  	 </div>
			  </div>
			</div>
			<p class=" pull-right">
				<button class="btn btn-primary" type="submit" onclick='window.close()'>关闭</button>
			</p>
        </div>
</div>
<script>
// status: 1单选 2多选
// binary_flag:  0 1 可选填  2 3 必填  3带备注  2不带备注  

//不带备注 必选 2  不带备注 可选 0 
//带备注   必选 3  带备注 可选1

	 var datas = <?php echo json_encode($data) ?>;
	 console.log(datas)
	  var view = new Vue({
            el: "#view",
            data: {
                data: datas,
            },
            created: function() {
            },
            methods: {
            }
        })
</script>