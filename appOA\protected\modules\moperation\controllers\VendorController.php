<?php

class VendorController extends ProtectedController
{
    public $actionAccessAuths = array(
    	// 'View'=>'ivystaff_opgeneral',
    	'Index'=>'oOperationsView',
    	'Update'=>'oOperationsAdmin',
    	'Delete'=>'oOperationsAdmin',
    	'AddStandard'=>'oOperationsAdmin',
    	'DeletePic'=>'oOperationsAdmin',
    );

    public $dialogWidth = 600;
    
    public function init() {
        parent::init();
        Yii::import('common.models.vendor.*');
        Yii::import('common.models.hr.*');

        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'hqOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site','HQ Operations');
        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/vue2.js');
    }

	/**
	 * Manages all models.
     * cagegorys   类型
     * city   城市
     * data   公司数据
	 */
	public function actionIndex()
	{
	    $model = VendorType::model()->findAll();
        $data = array();
	    foreach ($model as $val){
            $data['cagegorys'][] = array(
                'id' => $val->vendor_type_id,
                'sign' => $val->sign,
                'title' => $val->getTitle(),
                'cn_title' => $val->cn_title,
                'en_title' => $val->en_title,
            );
        }

        $criteria = new CDbCriteria;
        $criteria->compare('category_id ',7);
        $diglossiaMode = Diglossia::model()->findAll($criteria);
        $city = array();
        foreach ($diglossiaMode as $val){
            $data['city'][] = array(
                'diglossia_id' => $val->diglossia_id,
                'title' => Yii::app()->language == 'zh_cn' ? $val->cntitle : $val->entitle,
            );
            $city[$val->diglossia_id] = Yii::app()->language == 'zh_cn' ? $val->cntitle : $val->entitle;
        }

        $criteria = new CDbCriteria;
        $criteria->compare('status ',"<>99");
        $vendorMode = Vendor::model()->findAll($criteria);

        if($vendorMode) {
            foreach ($vendorMode as $val) {
                $data['data'][] = array(
                    'id' => $val->vendor_id,
                    'title' => Yii::app()->language == 'zh_cn' ? $val->cn_title : $val->en_title,
                    'linkman' => $val->linkman,
                    'type_id' => $val->vendor_type_id,
                    'city' => $val->vendor_city ? unserialize($val->vendor_city) : array() ,
                    'tel' => $val->contact_method,
                    'address' => $val->address,
                    'is_signed' => $val->is_signed,
                    'bank_account' => ($val->bankAccount) ? $val->bankAccount->account : " ",
                    'account_title' => ($val->bankAccount) ? $val->bankAccount->account_title : " ",
                    'account_bank' => ($val->bankAccount) ? $val->bankAccount->account_bank : " ",
                );
            }
        }

		$this->render('index',array(
		    'data' => $data,
        ));
	}

	//  增加和修改分类
	public function actionCategorySave()
    {
        $type_id = Yii::app()->request->getParam('type_id', '');
        $sign = Yii::app()->request->getParam('sign', '');
        $cn_title = Yii::app()->request->getParam('cn_title', '');
        $en_title = Yii::app()->request->getParam('en_title', '');

        $model = VendorType::model()->findByPk($type_id);
        if(!$model){
            $model = new VendorType();
        }
        $model->sign = $sign;
        $model->cn_title = $cn_title;
        $model->en_title = $en_title;
        $model->typefield_arr = serialize(array(0 => 'title'));
        if(!$model->save()){
            $error = current($model->getErrors());
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $error);
            $this->showMessage();
        }
        $data = array(
            'id' => $model->vendor_type_id,
            'sign' => $model->sign,
            'title' => $model->getTitle(),
            'cn_title' => $model->cn_title,
            'en_title' => $model->en_title,
        );
        $this->addMessage('state', 'success');
        $this->addMessage('message', '添加成功');
        $this->addMessage('data', $data);
        $this->showMessage();
    }


    //  删除分类
    public function actionCategoryDel()
    {
        $type_id = Yii::app()->request->getParam('type_id', '');
        $model = VendorType::model()->findByPk($type_id);
        if($model){
            $criteria = new CDbCriteria;
            $criteria->compare('vendor_type_id ',$model->vendor_type_id);
            $count = Vendor::model()->count($criteria);
            if(!$count){
                if(!$model->delete()){
                    $error = current($model->getErrors());
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', $error);
                    $this->showMessage();
                }
                $this->addMessage('state', 'success');
                $this->addMessage('message', '删除成功');
                $this->showMessage();
            }else{
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '分类下有公司,不可删除');
                $this->showMessage();
            }

        }
        $this->addMessage('state', 'fail');
        $this->addMessage('message', '未找到数据');
        $this->showMessage();
    }

    // 修改时候获取数据方法
    public function actionShowVendor()
    {
        $vendor_id = Yii::app()->request->getParam('vendor_id', '');
        $model = Vendor::model()->findByPk($vendor_id);

        $criteria = new CDbCriteria;
        $criteria->compare('category',10);
        $criteria->compare('status',1);
        $hrModel = HrDepartment::model()->findAll($criteria);
        $deptArr = array();
        foreach ($hrModel as $val){
            $deptArr[] = array(
                'id' => $val->id,
                'name' => $val->getName(),
            );
        }
        $data = array('deptArr' => $deptArr, 'data' => array());
        if($model){
            $data['data'] = $model->attributes;
            $data['data']['vendor_city'] = unserialize($data['vendor_city']);
            $data['data']['account_bank'] = ($model->bankAccount) ? $model->bankAccount->account_bank : "";
            $data['data']['account'] = ($model->bankAccount) ? $model->bankAccount->account : "";
            $data['data']['account_title'] = ($model->bankAccount) ? $model->bankAccount->account_title : "";

        }

        $this->addMessage('state', 'success');
        $this->addMessage('message', $data);
        $this->showMessage();
    }

    // 增加公司和修改公司
    public function actionVendorSave()
    {

        $vendor_id = Yii::app()->request->getParam('vendor_id', '');
        $model = Vendor::model()->findByPk($vendor_id);
        if(!$model){
            $model = new Vendor();
        }

        if(isset($_POST['Vendor'])){
            $model->attributes = $_POST['Vendor'];
            $model->vendor_city = serialize($_POST['Vendor']['vendor_city']);
            $model->status = 0;
            $model->update_timestamp = time();
            $model->update_user = Yii::app()->user->id;
            if($model->save())
            {
                $criteria = new CDbCriteria;
                $criteria->compare('vendor_id ',$model->vendor_id);
                $accountModel = VendorBankAccount::model()->find($criteria);
                if(!$accountModel){
                    $accountModel = new VendorBankAccount();
                }
                $accountModel->attributes = $_POST['VendorBankAccount'];
                $accountModel->vendor_id = $model->vendor_id;
                $accountModel->update_timestamp = time();
                $accountModel->update_user = Yii::app()->user->id;
                if(!$accountModel->save()){
                    $error = current($accountModel->getErrors());
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', $error);
                    $this->showMessage();
                }
                $data = array(
                    'id' => $model->vendor_id,
                    'title' => Yii::app()->language == 'zh_cn' ? $model->cn_title : $model->en_title,
                    'linkman' => $model->linkman,
                    'type_id' => $model->vendor_type_id,
                    'city' => $model->vendor_city ? unserialize($model->vendor_city) : array() ,
                    'tel' => $model->contact_method,
                    'address' => $model->address,
                    'is_signed' => $model->is_signed,
                    'bank_account' => $accountModel->account,
                    'account_title' => $accountModel->account_title,
                    'account_bank' => $accountModel->account_bank,
                );
                $this->addMessage('state', 'success');
                $this->addMessage('message', '成功');
                $this->addMessage('data', $data);
                $this->addMessage('callback', 'cbSuccess');
                $this->showMessage();
            }else{
                $error = current($model->getErrors());
                $this->addMessage('state', 'fail');
                $this->addMessage('message', $error);
                $this->showMessage();
            }
        }
    }

    // 删除公司
    public function actionVendorDel()
    {
        $vendor_id = Yii::app()->request->getParam('vendor_id', '');
        $model = Vendor::model()->findByPk($vendor_id);
        if($model){
            $model->status = 99;
            $model->update_timestamp = time();
            $model->update_user = Yii::app()->user->id;
            if(!$model->save()){
                $error = current($model->getErrors());
                $this->addMessage('state', 'fail');
                $this->addMessage('message', $error);
                $this->showMessage();
            }
            $this->addMessage('state', 'success');
            $this->addMessage('message', '删除成功');
            $this->addMessage('data', $model->vendor_id);
            $this->showMessage();
        }
        $this->addMessage('state', 'fail');
        $this->addMessage('message', '非法操作');
        $this->showMessage();
    }
}
