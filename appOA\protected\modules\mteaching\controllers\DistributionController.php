<?php

class DistributionController extends TeachBasedController
{
    public $selectedClassId = 0;
    public $selectedChildId = 0;

    public $printFW = array();

    // 访问action的初级权限
    public $actionAccessAuths = array(
        'updatereport'              => array('access' => 'o_MS_Rrport', 'admin' => 'o_MS_RrportMgt'),
        'delereport'                => 'o_MS_RrportMgt',
    );

    public function init()
    {
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'teaching';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site', 'Teaching Tasks');

        //初始化选择校园页面
        $this->branchSelectParams['hideOffice'] = true;
        $this->branchSelectParams['urlArray'] = array('//mteaching/distribution/index');
        $cs = Yii::app()->clientScript;
        $cs->registerCoreScript('jquery.ui');
        $cs->registerCssFile($cs->getCoreScriptUrl() . '/jui/css/base/jquery-ui.css');
        $cs->registerScriptFile($cs->getCoreScriptUrl() . '/jui/js/jquery-ui-i18n.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/element/vue.global.js');
        // $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/underscore-min.js');


        Yii::import('common.models.portfolio.*');
        Yii::import('common.models.secondary.*');
        Yii::import('common.models.calendar.*');
        Yii::import('common.models.timetable.*');
    }

    public function beforeAction($action)
    {
        if (!parent::beforeAction($action)) {
            return false;
        }

        $this->selectedClassId = Yii::app()->request->getParam('classid', 0);
        $this->selectedChildId = Yii::app()->request->getParam('childid', 0);

        return true;
    }

    public function actionIndex()
    {
        $yid = $this->branchObj->schcalendar;
        $criteria = new CDbCriteria();
        $criteria->compare('yid', $yid);
        $criteria->compare('classtype', array("e6", "e7", 'e8', 'e9', 'e10', 'e11', 'e12'));
        $criteria->compare('stat', 10);
        $criteria->index = 'classtype';
        $criteria->order = 'child_age ASC, title ASC';
        $classModel = IvyClass::model()->findAll($criteria);
        //  所有中学班级
        $classArr = array();
        foreach ($classModel as $val) {
            $class  = substr($val->classtype, 1);
            $classArr[$class] = $val->title;
        }

        $criteria = new CDbCriteria();
        $criteria->compare('t.school', $this->branchId);
        $criteria->compare('t.stat', 1);
        $criteria->compare('grade.grade', array("e6", "e7", 'e8', 'e9', 'e10', 'e11', 'e12'));
        $criteria->with = 'grade';
        $model = SecondaryReport::model()->findAll($criteria);

        $templateIds = array();
        $gradeTemplate = array();
        $gradeTemplateData = array();

        $flagClass = array();
        foreach ($model as $val) {
            foreach ($val->grade as $item) {
                $grade = substr($item->grade, 1);
                if ($item->period > 0) {
                    if ($grade < 10) {
                        $gradeStr = '0' . $grade;
                    } else {
                        $gradeStr = $grade;
                    }
                    $flagClass[$gradeStr][$item->period] = 1;
                }
                $templateIds[] = $item->template_id;
                $gradeTemplate[$grade][$item->period] = $item->template_id;
                $gradeTemplateData[$item->template_id] = array(
                    'id' => $item->template_id,
                    'title' => CommonUtils::autoLang($val->report_title_cn, $val->report_title_en),
                );
            }
        }

        // 拿到所有的课程，根据班级分类
        $timeTable = Timetable::model()->findByAttributes(array('yid' => $yid, 'status' => 1));
        $tid = $timeTable->id;
        $criteria = new CDbCriteria();
        $criteria->compare('tid', $tid);
        $criteria->compare('status', array(1,99));
        $criteria->group = "course_id";
        $courseStudents = TimetableStudentData::model()->findAll($criteria);
        $courseIdList = array();
        foreach ($courseStudents as $key => $value) {
            $courseIdList[$value->course_id] = $value->course_id;
        }

        $criteria = new CDbCriteria();
        $criteria->compare('status', 1);
        $criteria->compare('id', array_values($courseIdList));
        $criteria->order = 'id ASC';
        $courseModel = TimetableCourses::model()->findAll($criteria);
        $courseList = array();
        $code = array();
        foreach ($courseModel as $val) {
            $class = substr($val->program, 2, 2);

            $code[$val->program][] = $val->course_code;
            $period1 = $val->period1 != 0 || isset($flagClass[$class][1]) ? $val->period1 : $val->report_course_id;
            $period2 = $val->period2 != 0 || isset($flagClass[$class][2]) ? $val->period2 : $val->report_course_id;
            $period3 = $val->period3 != 0 || isset($flagClass[$class][3]) ? $val->period3 : $val->report_course_id;
            $period4 = $val->period4 != 0 || isset($flagClass[$class][4]) ? $val->period4 : $val->report_course_id;

            $courseList[$class][$val->program] = array(
                'program' => $val->program,
                'codeArr' => $code[$val->program],
                'report_course_id' => $val->report_course_id,
                'period1' => $period1,
                'period2' => $period2,
                'period3' => $period3,
                'period4' => $period4,
                'title' => $val->getTitle(),
            );
        }

        foreach ($gradeTemplate as $grade => $v) {
            $commonTid = $v[0];
            if ($commonTid) {
                for ($i = 1; $i < 5; $i++) {
                    if (!isset($v[$i])) {
                        $gradeTemplate[$grade][$i] = $commonTid;
                    }
                }
            }
        }


        $criteria = new CDbCriteria();
        $criteria->compare('td', array_unique($templateIds));
        $reportModel = AchievementReportCourse::model()->findAll($criteria);

        $secodarArr = array();
        foreach ($reportModel as $val) {
            $secodarArr[$val->td][] = array(
                'id' => $val->id,
                'title' => $val->getName(),
            );
        }

        $this->render('index', array(
            'classArr' => $classArr,
            'courseList' => $courseList,
            'code' => $code,
            'secodarArr' => $secodarArr,
            'gradeTemplate' => $gradeTemplate,
            'gradeTemplateData' => $gradeTemplateData,
        ));
    }

    // 关联课程标准
    public function actionSaveAchievement()
    {
        $savaData = Yii::app()->request->getParam('savaData', array());

        $code = array();
        $returnData = array();
        if ($savaData) {
            $criteria = new CDbCriteria();
            $criteria->compare('t.program', array_keys($savaData));
            $models = TimetableCourses::model()->findAll($criteria);
            foreach ($models as $model) {
                if (isset($savaData[$model->program])) {
                    $periodData = $savaData[$model->program];
                    $model->period1 = $periodData['period1'];
                    $model->period2 = $periodData['period2'];
                    $model->period3 = $periodData['period3'];
                    $model->period4 = $periodData['period4'];
                    $model->updated_at = time();
                    $model->updated_by = Yii::app()->user->getId();
                    $model->save();
                }
                $code[$model->program][] = $model->course_code;
                $returnData[$model->program] = array(
                    'program' => $model->program,
                    'codeArr' => $code[$model->program],
                    'report_course_id' => $model->report_course_id,
                    'period1' => $model->period1,
                    'period2' => $model->period2,
                    'period3' => $model->period3,
                    'period4' => $model->period4,
                    'title' => $model->getTitle(),
                );
            }
        }
        $this->addMessage('state', 'success');
        $this->addMessage('message', '保存成功');
        $this->addMessage('data', $returnData);
        $this->showMessage();
    }

    // 关联课程标准
    public function actionUpdateType()
    {
        $type = Yii::app()->request->getParam('type', array());
        $courseList = array();
        if ($type) {
            $criteria = new CDbCriteria();
            $criteria->compare('t.program', array_keys($type));
            $model = TimetableCourses::model()->findAll($criteria);

            if ($model) {
                foreach ($model as $val) {
                    $val->report_course_id = $type[$val->program];
                    $val->updated_at = time();
                    $val->save();
                    $class  = substr($val->program, 3, 1);
                    $courseList[$class][$val->program] = array(
                        'program' => $val->program,
                        'report_course_id' => $val->report_course_id,
                        'title' => $val->getTitle(),
                    );
                }
            }
        }
        $this->addMessage('state', 'success');
        $this->addMessage('message', '保存成功');
        $this->addMessage('data', $courseList);
        $this->addMessage('callback', 'cbSuccess');
        $this->showMessage();
    }
}
