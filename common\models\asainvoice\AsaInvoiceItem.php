<?php

/**
 * This is the model class for table "ivy_asa_invoice_item".
 *
 * The followings are the available columns in table 'ivy_asa_invoice_item':
 * @property integer $id
 * @property integer $course_group_id
 * @property integer $course_id
 * @property integer $order_id
 * @property integer $class_count
 * @property double $unit_price
 * @property double $actual_total
 * @property integer $status
 * @property integer $handover_status
 * @property integer $refund_status
 * @property string $schoolid
 * @property integer $updated
 * @property integer $updated_userid
 * @property integer $spot_hold
 */
class AsaInvoiceItem extends CActiveRecord
{
	const SPOT_HOLD_FREE = 0;
	const SPOT_HOLD_ACTIVE = 1;
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_asa_invoice_item';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('course_group_id, course_id, order_id, class_count, unit_price, actual_total, status, schoolid, updated, updated_userid', 'required'),
			array('course_group_id, course_id, order_id, class_count, status, handover_status, refund_status, updated, updated_userid, spot_hold', 'numerical', 'integerOnly'=>true),
			array('unit_price, actual_total', 'numerical'),
			array('schoolid', 'length', 'max'=>255),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, course_group_id, course_id, order_id, class_count, unit_price, actual_total, status, handover_status, refund_status, schoolid, updated, updated_userid, spot_hold', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
			"asacourse" => array(self::BELONGS_TO, 'AsaCourse', array('course_id'=>'id')),
			'asainvoice' => array(self::BELONGS_TO, 'AsaInvoice', array('order_id'=>'id')),
			'asaCourseGroup' => array(self::BELONGS_TO, 'AsaCourseGroup', array('course_group_id'=>'id')),
			'asaInvoiceItemExtra' => array(self::HAS_ONE, 'AsaInvoiceItemExtra', array('invoice_item_id'=>'id')),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'course_group_id' => 'Course Group',
			'course_id' => 'Course',
			'order_id' => 'Order',
			'class_count' => 'Class Count',
			'unit_price' => 'Unit Price',
			'actual_total' => 'Actual Total',
			'status' => 'Status',
			'handover_status' => 'Handover Status',
			'refund_status' => 'Refund Status',
			'schoolid' => 'Schoolid',
			'updated' => 'Updated',
			'updated_userid' => 'Updated Userid',
			'spot_hold' => 'Spot Hold',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('course_group_id',$this->course_group_id);
		$criteria->compare('course_id',$this->course_id);
		$criteria->compare('order_id',$this->order_id);
		$criteria->compare('class_count',$this->class_count);
		$criteria->compare('unit_price',$this->unit_price);
		$criteria->compare('actual_total',$this->actual_total);
		$criteria->compare('status',$this->status);
		$criteria->compare('handover_status',$this->handover_status);
		$criteria->compare('refund_status',$this->refund_status);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('updated',$this->updated);
		$criteria->compare('updated_userid',$this->updated_userid);
		$criteria->compare('spot_hold',$this->spot_hold);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return AsaInvoiceItem the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}
