<?php

/**
 * Clear up
 */

class ClearUpCommand extends CConsoleCommand {
    public $batchNum = 200;

	public function init(){
		Yii::setPathOfAlias('common', dirname(__FILE__) . '/../../../common');
	}
    public function actionTestMonthList($startDate='2014-09-01', $endDate='2015-06-30'){
        $startTime = strtotime($startDate);
        $endTime = strtotime($endDate);
        $cycleTime = mktime(0,0,0,date('m',$startTime), 1, date('Y',$startTime));
        $endMonthTime = mktime(0,0,0,date('m',$endTime), 1, date('Y',$endTime));
        while($cycleTime <= $endMonthTime){
            echo date('Y-m', $cycleTime) . "\r\n";
            $cycleTime = strtotime( date('Y-m-d', $cycleTime) . ' + 1 month');
        }
    }

    public function actionYeePayOrder($day = 60)
    {
        Yii::import('common.models.yeepay.*');

        $t = strtotime('-'.$day.'day');
        $criteria = new CDbCriteria;
        $criteria->compare('status', 0);
        $criteria->compare('payed_status', 0);
        $criteria->compare('fact_amount', 0);
        $criteria->compare('updateTime', '<'.$t);

        $total = YeepayOrder::model()->count($criteria);
        if (!$total)
            return false;
		$cycle = ceil($total/$this->batchNum);
        for($i=0; $i<$cycle; $i++){
			$criteria->limit=$this->batchNum;
			$criteria->offset=$i*$this->batchNum;
            $orders = YeepayOrder::model()->findAll($criteria);
            foreach ($orders as $order){
                $sql = "delete from ivy_yp_transaction_detail where it_orderId='".$order->orderId."'";
                if ($order->delete()){
                    Yii::app()->db->createCommand($sql)->query();
                    echo '.';
                }
            }
        }
    }

    # 填充数据 ivy_notes_child `startyear` `pids`
    public function actionChildNotes($timePoint='2013/06/01', $childid=0)
    {
        if (!$timePoint)
            echo $this->getHelp();
        Yii::import('common.models.calendar.Calendar');

        $years = CHtml::listData(Calendar::model()->findAll(), 'yid', 'startyear');
        $ys = array_unique($years);

        $sql = ($childid == 0) ?
			"select count(*) as count from ivy_notes_child where updated_timestamp > ".strtotime($timePoint) :
			"select count(*) as count from ivy_notes_child where childid = " . $childid . " and updated_timestamp > ".strtotime($timePoint) ;
        $count = Yii::app()->db->createCommand($sql)->queryRow();
        $total = $count['count'];
        $cycle = ceil($total/$this->batchNum);
        echo $total."\n";
        $j = 0;
        for($i=0; $i<$cycle; $i++){
			echo 'round '.($i+1)."\r\n";
            $sql = ($childid == 0 )?
				"select * from ivy_notes_child where updated_timestamp > ".strtotime($timePoint). ' order by updated_timestamp DESC'." limit ".$i*$this->batchNum.",".$this->batchNum :
				"select * from ivy_notes_child where childid = " . $childid . " and updated_timestamp > ".strtotime($timePoint). ' order by updated_timestamp DESC'." limit ".$i*$this->batchNum.",".$this->batchNum ;
			echo $sql . "\r\n";
            $rows = Yii::app()->db->createCommand($sql)->queryAll();
            foreach($rows as $row){
                if (intval($row['startyear']) == 0){
                    $_tmp = array();
                    $sYear = isset($years[$row['yid']]) ? $years[$row['yid']] : null;

                    $tabname = "ivy_child_media_links_".$sYear;
                    $sql1 = "select id,pid from ".$tabname." where category='week' and classid=".$row['classid']." and childid=".$row['childid']." and yid=".$row['yid']." and weeknum=".$row['weeknumber'];
                    $pArrs = Yii::app()->db->createCommand($sql1)->queryAll();
                    foreach($pArrs as $pa){
                        $_tmp[$pa['pid']] = $pa['pid'];
                    }

                    $randarr = (count($_tmp)>3) ? array_rand($_tmp, 4) : $_tmp;
                    $randarr = ($randarr) ? implode(',', $randarr) : null;
                    $sqluu = "update ivy_notes_child set startyear=".$sYear.", pids='".$randarr."' where id=".$row['id'];
                    Yii::app()->db->createCommand($sqluu)->query();
                    echo '.';$j++;
                }
            }
        }
        echo "\n".$j;
    }

    public function actionTest()
    {
        $dir = "d:/TS/";
        $dri1 = "d:/c2/";
        $handle = dir($dir);
        while (false !== ($file = $handle->read())){
            $dfile = $dir.$file;
            if (is_file($dfile)){
                list($width, $height) = getimagesize($dfile);
                if ($width < 90 || $height < 120){
                    copy($dfile, $dri1.$file);
                    echo '.';
                }
                elseif ($width > 90 || $height > 120){
                    Yii::import('application.extensions.image.Image');
                    $image = new Image($dfile);
                    $min = min($width, $height);
                    $_h = $min/0.75;
                    if ($min == $width){
                        $min = $_h < $height ? $min : $min*($height/$_h);
                    }
                    $image->crop($min, $_h);
                    $image->resize(90, 120)->save();
                    echo '.';
                }
            }
        }
    }

    public function actionVisit1()
    {
        $sql = "select basic_id, max(update_timestamp) as recent_timestamp from ivy_visits_trace_log group by basic_id";
        $rs = Yii::app()->db->createCommand($sql)->queryAll();
        foreach ($rs as $_rs){
            if ($_rs['basic_id']){
                Yii::app()->db->createCommand("update ivy_visits_basic_info set recent_log = ".$_rs['recent_timestamp']." where id=".$_rs['basic_id'])->query();
                echo $_rs['basic_id']."\n";
            }
        }
    }

    public function actionSyncTestingMediaData($startYear, $dataRange='1h')
    {
        Yii::import('common.models.media.*');

        $securityKey = 'I3v4y7Onl8ine6Mi433ms';
        $dataRangePattern = '/^([0-9]*)([d|h|m])$/i';
        $found = preg_match_all($dataRangePattern, $dataRange, $matches);
        if($found && count($matches)==3){

            $time = time();
            $postParams = array(
                'startYear' => $startYear,
                'timestamp' => $time,
                'dataRange' => $dataRange,
                'token' => md5(sprintf('%s&%s&%s&%s', $startYear, $time, $dataRange, $securityKey))
            );
            $ch = curl_init();

//            $url = 'http://www.ivynotify.cn/media/fetchData';
            $url = 'http://182.92.101.169/media/fetchData';

            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $postParams);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER , 1);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

            $result = CJSON::decode(curl_exec($ch));

            foreach($result as $_data){
                $data = CJSON::decode($_data['data']);

                if($data){
                    $mediaModel = ChildMedia::model();
                    $mediaModel->setOptions( $sandbox=false, $data['t'] );

                    $media = new $mediaModel;
                    $attrs = array(
                        'schoolid' => $data['s'],
                        'classid' => $data['c'],
                        'yid' => $data['y'],
                        'weeknum' => $data['w'],
                        'type' => $data['filetype'],
                        'filename' => $data['key'],
                        'tag' => '',
                        'server' => $data['svr'],
                        'timestamp' => time(),
                        'uid' => $data['u']
                    );
                    $media->setAttributes( $attrs );
                    if($media->save()){
                        echo $media->id.'..';
                    }else{
                        echo "\r\n";
                        print_r($media->getErrors());
                        echo "\r\n";
                    }
                }
            }

        }else{
            echo "\r\n";
            echo "\r\n";
            echo "Wrong dataRange parameter";
            echo "\r\n";
            echo "\r\n";
        }


    }

    public function actionImportDsUsers()
    {
        $nationaliy = array(
            'Australia' => 9,
            'CHN' => 36,
            'France' => 61,
            'Guatemala' => 70,
            'HKG' => 175,
            'India' => 78,
            'Phillipines' => 140,
            'South Africa' => 165,
            'Thailand' => 178,
            'Swizerland' => 173,
            'UK' => 187,
            'USA' => 188,
        );
        Yii::import('common.models.staff.*');
        $sql = "select * from ds_users where success=0";
        $items = Yii::app()->db->createCommand($sql)->queryAll();
        foreach($items as $item){
            $model = new User;
            $model->profile = new UserProfile;
            $model->staff = new Staff;

//            $model->setScenario('addStaff');
//            $model->profile->setScenario('addStaff');
//            $model->staff->setScenario('addStaff');

            $nameArr = explode(' ', $item['name_en']);
            if(count($nameArr) > 2){
                $firstName = ucfirst(strtolower($nameArr[0]));
                $lastName = ucfirst(strtolower($nameArr[2]));
            }
            else{
                $firstName = ucfirst(strtolower($nameArr[0]));
                $lastName = ucfirst(strtolower($nameArr[1]));
            }

            $model->attributes = array(
                'name' => $item['name_cn'],
                'uname' => $firstName.' '.substr($lastName, 0, 1),
                'email' => $item['email'],
                'user_regdate' => time(),
                'pass' => md5(uniqid()),
                'level' => 1,
                'isstaff' => 1,
            );

            $model->profile->attributes = array(
                'first_name' => $firstName,
                'last_name' => $lastName,
                'user_gender' => $item['gender'] == 'M' ? 1 :2,
                'nationality' => $nationaliy[$item['nationality']],
                'branch' => 'BJ_DS',
            );

            $model->staff->attributes = array(
                'startdate' => strtotime($item['enter_date']),
                'mobile_telephone' => $item['tel'],
            );

            $valid=$model->validate();
            $valid=$model->profile->validate() && $valid;
            $valid=$model->staff->validate() && $valid;
            if ($valid){
                $model->save(false);
                $model->profile->uid=$model->uid;
                $model->profile->save(false);
                $model->staff->sid=$model->uid;
                $model->staff->save(false);

                StaffSync::model()->sync($model->uid);

                $sql = "update ds_users set success=".$model->uid." where id=".$item['id'];
                Yii::app()->db->createCommand($sql)->query();
                echo $model->uid."\n";
            }
            else{
                print_r($model->getErrors());
                print_r($model->profile->getErrors());
                print_r($model->staff->getErrors());
                die;
            }
        }
    }

    public function actionDelDsUsers()
    {
        Yii::import('common.models.staff.*');
        $sql = "select * from ds_users where success>0";
        $items = Yii::app()->db->createCommand($sql)->queryAll();
        foreach($items as $item){
            $model = User::model()->findByPk($item['success']);
            if($model != null && $model->delete()){
                $modelP = UserProfile::model()->findByPk($item['success']);
                if($modelP != null && $modelP->delete()){
                    $modelS = Staff::model()->findByPk($item['success']);
                    if($modelS != null && $modelS->delete()){
                        $modelSS = StaffSync::model()->findByPk($item['success']);
                        if($modelSS != null){
                            $modelSS->delete();
                        }
                        $sql = "update ds_users set success=0 where id=".$item['id'];
                        Yii::app()->db->createCommand($sql)->query();
                        echo $item['success']."\n";
                    }
                    else{
                        echo "!staff".$item['success'];
                    }
                }
                else{
                    echo "!profile".$item['success'];
                }
            }
            else{
                echo "!user".$item['success'];
            }
        }
    }

    /**
     * 政府补贴钱多了，得出孩子ID
     */
    public function actionDiffChild()
    {
        $diff1 = array();
        $diff2 = array();
        $sql = "SELECT childid  FROM `ivy_batch_invoices_item` WHERE `batch_id` = 17";
        $items = Yii::app()->db->createCommand($sql)->queryAll();
        foreach($items as $item){
            $diff1[$item['childid']] = $item['childid'];
        }
        $sql1 = "SELECT childid  FROM `ivy_ufida_income` WHERE `schoolid` LIKE 'TJ_EC' AND `timestmp` = 1414771199 and `payment_type`='preschool_subsidy' and `memo` like '%2014-05-01%'";
        $items = Yii::app()->db->createCommand($sql1)->queryAll();
        foreach($items as $item){
            $diff2[$item['childid']] = $item['childid'];
        }
        print_r(array_diff($diff1, $diff2));
    }

	// EC ES 呕吐事故退费
	public function actionRefund($yid=0, $csv='', $type='', $st='', $et='', $memo='特殊退费')
	{
        if(!$yid){
            die('yid error');
        }
	    if(!$csv || !file_exists($csv)){
	        die('csv path error');
        }
        if(!$type){
            die('type error');
        }
        if(!$st || !$et){
            die('time error');
        }

        Yii::import('common.models.invoice.*');
        $errorids = array();
//		$data = file('C:/g2.csv');
//		$data = file('/Users/<USER>/Downloads/OG.csv');
		$data = file($csv);
//		print_r($data);die;
        date_default_timezone_set('Asia/Shanghai');
		$time = time();
		foreach($data as $k=>$v){
			$csv = explode(",", trim($v));
			if($csv[0] && $csv[1]){
				$childid[] = trim($csv[0]);
				$_amount = (isset($csv[2]) && $csv[2]) ? $csv[1].$csv[2] : $csv[1];
				$amount[$csv[0]] = trim(trim($_amount), '"');
			}
		}
//		print_r($childid);die;
		$item = ChildProfileBasic::model()->findAllByPk($childid);
//		print_r($item);die;
		foreach($item as $k=>$v1){
			$classid = $v1->classid;
			$schoolid = $v1->schoolid;
			$inout = 'in';
			$payment_type = $type;
//			$payment_type = 'tuition';
//			$payment_type = 'bus';
//            $payment_type = 'lunch';
//			$payment_type = 'registration';
//			$payment_type = 'deposit';
//            1625068800 7.1
//            1627660800 7.31
//            1630339200 8.31
			$startdate = $st;
			$enddate = $et;
			$criter = new CDbCriteria();
//            $criter->compare('classid', $classid);
            $criter->compare('t.childid', $v1->childid);
			$criter->compare('t.inout', $inout);
			$criter->compare('t.calendar_id', $yid);
			$criter->compare('t.payment_type', $payment_type);
			$criter->compare('invoiceInfo.startdate', '<='.$startdate);
			$criter->compare('invoiceInfo.enddate', '>='.$enddate);
			$criter->compare('invoiceInfo.status', 20);
			$criter->order='timestampe DESC';
			$Transaction = InvoiceTransaction::model()->with('invoiceInfo')->find($criter);
			if($Transaction){
				$model = new Invoice;//print_r($item->childid);
				$model->calendar_id = $yid;
				$model->amount = floatval($amount[$v1->childid]);
				$model->original_amount = $model->amount;
				$model->fee_type = $Transaction->fee_type;
				$model->userid = 1;
				$model->childid = $v1->childid;
				$model->schoolid = $Transaction->schoolid;
				$model->classid = $Transaction->classid;
				$model->payment_type = $payment_type;
				$model->startdate = $startdate;
				$model->enddate = $enddate;
				$model->inout = 'out';
				$model->title = $memo;
				$model->memo = $memo;
				$model->timestamp = $time;
				$model->last_paid_timestamp = $time;
				$model->status = 20;
				if(!$model->save()){
					print_r($model->getErrors());die('1');
				}
				$item = new InvoiceTransaction;
				$item->calendar_id = $model->calendar_id;
				$item->invoice_id = $model->invoice_id;
				$item->operator_uid = $model->userid;
				$item->amount = $model->amount;
				$item->fee_type = $model->fee_type;
				$item->childid = $v1->childid;
				$item->schoolid = $model->schoolid;
				$item->classid = $model->classid;
				$item->payment_type = $model->payment_type;
				$item->inout = $model->inout;
				$item->title = $model->title;
				$item->memo = $model->memo;
				$item->timestampe = $time;
				$item->transfer_timestamp = $time;
				$item->startdate = $model->startdate;
				$item->enddate = $model->enddate;
				$item->transactiontype = 50;
				if(!$item->save()){
					print_r($item->getErrors());die('2');
				}

				$item_refund = new InvoiceChildRefund;
				$item_refund->childid = $v1->childid;
				$item_refund->schoolid = $model->schoolid;
				$item_refund->classid = $model->classid;
				$item_refund->yid = $model->calendar_id;
				$item_refund->invoice_id = $model->invoice_id;
				$item_refund->on_invoice_id = $Transaction->invoice_id;
				$item_refund->transaction_id = $item->id;
				$item_refund->on_transaction_id = $Transaction->id;
				$item_refund->title = $model->title;
				$item_refund->amount = $model->amount;
				$item_refund->payment_type = $model->payment_type;
				$item_refund->memo = $model->memo;
				$item_refund->startdate = $startdate;
				$item_refund->enddate = $enddate;
				$item_refund->timestamp = $time;
				$item_refund->userid = $model->userid;
				$item_refund->status = 20;
				$item_refund->refund_type = 41;
				if(!$item_refund->save()){
					print_r($item_refund->getErrors());die('3');
				}

				$ChildCredit = new ChildCredit;
				$ChildCredit->childid = $v1->childid;
				$ChildCredit->schoolid = $model->schoolid;
				$ChildCredit->classid = $model->classid;
				$ChildCredit->yid = $model->calendar_id;
				$ChildCredit->amount = $model->amount;
				$ChildCredit->inout = $inout;
				$ChildCredit->itemname = $payment_type;
				$ChildCredit->invoice_id = $model->invoice_id;
				$ChildCredit->transaction_id = $item->id;
				$ChildCredit->userid = $model->userid;
				$ChildCredit->updated_timestamp = $time;
				$ChildCredit->balance = $ChildCredit->getChildCredit($v1->childid) + $model->amount;
				if(!$ChildCredit->save()){
					print_r($ChildCredit->getErrors());die('4');
				}

				$v1->credit = $ChildCredit->balance;
				if(!$v1->save()){
					print_r($v1->getErrors());die('5');
				}
                echo $v1->childid."\n";
			}
			else{
                $errorids[] = $v1->childid;
			}
		}
        print_r($errorids);
	}

	public function actionCancelLunch(){
		//yiic clearup cancelLunch
		Yii::import('common.models.invoice.*');
		$time = date('Ymd', time()); //程序执行时间
		//$time = 20150212;
		$criter = new CDbCriteria();
		$criter->compare('target_date', $time); //$time
		$RefundLunch = RefundLunch::model()->with(array('childInfo'))->findAll($criter);
		if($RefundLunch){
			$item = array();
			$schoolids = array();
			$classids = array();
			$uid = array();
			foreach($RefundLunch as $v){
				$schoolids[$v->schoolid] = $v->schoolid;
				$classids[$v->classid] = $v->classid;
				$uid[$v->userid] = $v->userid;
				$timestamp = date('Y-m-d H:i', $v->updated_timestamp);//操作日期
				$item[$v->schoolid][$v->classid][] = array(
					'user' => $v->userid,
					'child' => $v->childInfo->getChildName(false,false,true),
					'timestamp'=>$timestamp
				);
			}
			$criter = new CDbCriteria();
			$criter->compare('uid', $uid);
			$uModel = User::model()->findAll($criter);
			$userInfo = array();
			foreach($uModel as $user){
				$userInfo[$user->uid] = $user->getName();
			}
			$criter = new CDbCriteria();
			$criter->compare('branchid', $schoolids);
			$sModel = BranchInfo::model()->findAll($criter);
			$bInfo = array();
			foreach($sModel as $model){
				$bInfo[$model->branchid] = array(
					'title' => $model->title_cn,
					'email' => $model->support_email,
				);
			}
			$criter = new CDbCriteria();
			$criter->compare('classid', $classids);
            $criter->order = 'child_age ASC, title ASC';
			$cModel = IvyClass::model()->findAll($criter);
			$classInfo = array();
			foreach($cModel as $Model){
				$classInfo[$Model->classid] = $Model->title;
			}
            //print_r($item);die;
			foreach($item as $k =>$lunchs){
				$subject_time = date('Y-m-d', strtotime($time));
				$mailer = Yii::createComponent('common.extensions.mailer.EMailer');
				$mailer->Subject = '['.$subject_time.']'.$bInfo[$k]['title']."午餐取消名单";
				$mailer->AddAddress($bInfo[$k]['email']);
				$mailer->getCommandView('cancelLunch', array(
					'lunchs' => $lunchs,
					'classInfo'=> $classInfo,
					'userInfo'=>$userInfo,
					'k'      => $bInfo[$k]['title'],
					'time'   => $subject_time
				), 'main');
                $isProduction = defined("IS_PRODUCTION")? IS_PRODUCTION : false;
				$mailer->iniMail($isProduction);
				$mailer->Send();
			}
		}
	}

	public function actionReply(){
		//yiic clearup reply
		Yii::import('common.models.feedback.*');
		$criter = new CDbCriteria();
		$criter->compare('com_root_id', 0);
		$criter->compare('com_ifreply', 0);
		$Comments = Comments::model()->findAll($criter);
		if($Comments){
			$subject_time = date('Y/m/d H:i', time());
			$schoolids = array();
			$childids = array();
			$classids = array();
			foreach($Comments as $v){
				$schoolids[$v->com_school_id] = $v->com_school_id;
				$childids[$v->com_child_id] = $v->com_child_id;
				$classids[$v->com_class_id] = $v->com_class_id;
				$item[$v->com_school_id][] = array(
					'child'   => $v->com_child_id,
					'class'   => $v->com_class_id,
					'content' => $v->com_content,
					'time'    => $v->com_created_time
				);
			}//print_r($item);die;
			$criter = new CDbCriteria();
			$criter->compare('classid', $classids);
			$cModel = IvyClass::model()->findAll($criter);
			$classInfo = array();
			foreach($cModel as $Model){
				$classInfo[$Model->classid] = $Model->title;
			}
			$criter = new CDbCriteria();
			$criter->compare('childid', $childids);
			$uModel = ChildProfileBasic::model()->findAll($criter);
			$childInfo = array();
			foreach($uModel as $child){
				$childInfo[$child->childid] = $child->getChildName();
			}//print_r($item);die;
			$criter = new CDbCriteria();
			$criter->compare('branchid', $schoolids);
			$sModel = BranchInfo::model()->findAll($criter);
			$bInfo = array();
			foreach($sModel as $model){
				$bInfo[$model->branchid] = array(
					'title'    => $model->title_cn,
					'title_en' => $model->title_en,
					'email'    => $model->support_email,
				);
			}
			foreach($item as $k =>$numbers){
				$mailer = Yii::createComponent('common.extensions.mailer.EMailer');
				$mailer->Subject = "[IvyOnline] ".$bInfo[$k]['title']." New Journal Feedback 周报告有新反馈内容";
				$mailer->AddAddress($bInfo[$k]['email']);
				$mailer->getCommandView('reply', array(
					'numbers'      => $numbers,
					'classInfo'    => $classInfo,
					'childInfo'     =>$childInfo,
					'title'        => $bInfo[$k]['title'],
					'title_en'     => $bInfo[$k]['title_en'],
					'subject_time' => $subject_time,
				), 'main');
				$isProduction = defined("IS_PRODUCTION")? IS_PRODUCTION : false;
				$mailer->iniMail($isProduction);
				$mailer->Send();
			}
		}
	}

	public function actionOverdueInvoices($schoolid = '')
	{
        Yii::app()->language = 'zh_cn';

        //yiic clearup overdueInvoices
		$month = date("d");
		$weeks = date("D");
		if($weeks == 'Mon' or $month == 01) {
			Yii::import('common.models.invoice.*');
			$criter = new CDbCriteria();
			$time = time();
			if($schoolid){
				$criter->compare('t.schoolid', $schoolid);
			}
			$criter->compare('t.status', array(10, 30));
			$criter->compare('t.duetime', '<'.$time);
			$criter->compare('t.duetime', '>'.'0');
			$criter->compare('t.`inout`', 'in');
            $criter->compare('t.payment_type', '<>preschool_subsidy');
            $criter->compare('school.status', 10);
			$Invoice = Invoice::model()->with('paidSum', 'school')->findAll($criter);
			if($Invoice){
				$schoolids = array();
				$classids = array();
				$childids = array();
				$amount = array();
				foreach($Invoice as $v){
					$childids[$v->childid] = $v->childid;
					$schoolids[$v->schoolid] = $v->schoolid;
					$classids[$v->classid] = $v->classid;
					$amount[$v->schoolid][] = sprintf("%.2f", $v->amount -$v->paidSum);
					$item[$v->schoolid][$v->classid][$v->childid][] = array(
						'childid'  => $v->childid,
						'title'    => $v->title,
						'duetime'  => $v->duetime,
						'y_amount' => sprintf("%.2f", $v->amount -$v->paidSum),
						'z_amount' => $v->amount
					);
				}
				foreach($amount as $k => $sums){
					$sum[$k] = sprintf("%.2f", array_sum($sums));//应付金额（总）
				}
				$criter = new CDbCriteria();
				$criter->compare('childid', $childids);
				$uModel = ChildProfileBasic::model()->findAll($criter);
				$childInfo = array();
				foreach($uModel as $child){
					$childInfo[$child->childid] = $child->getChildName();
				}
				$criter = new CDbCriteria();
				$criter->compare('branchid', $schoolids);
				$sModel = BranchInfo::model()->findAll($criter);
				$bInfo = array();
				foreach($sModel as $model){
					$bInfo[$model->branchid] = array(
						'title' => $model->title_cn,
						'email' => $model->support_email,
					);
				}
				$criter = new CDbCriteria();
				$criter->compare('classid', $classids);
				$cModel = IvyClass::model()->findAll($criter);
				$classInfo = array();
				foreach($cModel as $Model){
					$classInfo[$Model->classid] = $Model->title;
				}
				foreach($item as $k =>$invoices){
					$mailer = Yii::createComponent('common.extensions.mailer.EMailer');
					$mailer->Subject = $bInfo[$k]['title'].": overdue invoices list";
					$mailer->AddAddress($bInfo[$k]['email']);
					$mailer->AddCC('<EMAIL>'); //抄送
					$mailer->getCommandView('overdueInvoices', array(
						'sum'       => $sum,
						'schoolid'  => $k,
						'invoices'  => $invoices,
						'school'    => $bInfo[$k]['title'],
						'classInfo' => $classInfo,
						'childInfo' => $childInfo
					), 'main');
					$isProduction = defined("IS_PRODUCTION")? IS_PRODUCTION : false;
					$mailer->iniMail($isProduction);
					$mailer->Send();
				}
			}
		}
	}

	public function actionCreditMonth()
	{
		//yiic clearup creditMonth
		$all_school = Branch::model()->getBranchList(false ,true, 20, 10);
		//print_r($all_school);die;
		$criter = new CDbCriteria();
		$criter->compare('credit', '>0');
		$cModels = ChildProfileBasic::model()->findAll($criter);
		$classids = array();
		$childs =array();
		foreach($cModels as $v){
			if(!empty($v->classid)){
			$classids[$v->classid] = $v->classid;
			}
			$childs[$v->childid] = $v->childid;
			$item[$v->schoolid][$v->childid] = array(
				'childid' => $v->childid,
				'schoolid' => $v->schoolid,
				'classid' => $v->classid,
				'credit' => $v->credit
			);
		}
		$criter = new CDbCriteria();
		$criter->compare('classid', $classids);
		$cModel = IvyClass::model()->findAll($criter);
		$classInfo = array();
		foreach($cModel as $Model){
			$classInfo[$Model->classid] = $Model->title;
		}
		$criter = new CDbCriteria();
		$criter->compare('childid', $childs);
		$uModel = ChildProfileBasic::model()->findAll($criter);
		$childInfo = array();
		foreach($uModel as $child){
			$childInfo[$child->childid] = $child->getChildName();
		}
		$current_month = mktime(0,0,0,  date('m',  time())-1,1,date('Y', time()));
		$file_path = Yii::getPathOfAlias('xoopsVarPath').'/childcurrentbalance/'.date('Y-m',$current_month).'/';
		if(!is_dir($file_path)){
			mkdir($file_path, 0777, true);
		}
		foreach ($item as $s => $sc) {
			$filename = $file_path.$all_school[$s]['abb']."-Child_Current_Balance-".date('Y-m',$current_month).".csv";
			$word = "Id,class,name,credit\r\n";
			$grand_total = 0.00;
			if (file_exists($filename)){
				unlink($filename);
			}
			$fh = fopen($filename, "w");
			fwrite($fh, $word);
			$str = '';
			foreach ($sc as $k => $v1) {
				$str .= $v1['childid'].','.(empty($v1['classid']) ? '' : $classInfo[$v1['classid']]).','.$childInfo[$v1['childid']].','.$v1['credit'];
				$str .= "\r\n";
				$grand_total += $v1['credit'];
				echo $v1['childid']."....\r\n";
			}
			fwrite($fh, $str);
			$str1 = ",,";
			$str1 .= ',' . $grand_total;
			$str1 .= "\r\n";
			fwrite($fh, $str1);
			fclose($fh);
		}
	}

    /**
     * 把历史的离职信息都写入新表  ivy_staff_resign
     */
    public function actionIniResignStaff(){
        Yii::import('common.models.staff.*');
        $crit = new CDbCriteria();
        $crit->compare('isstaff', 1);
        $crit->compare('level', 0);
        $total = User::model()->count($crit);
        $cycle = ceil( $total / $this->batchNum );
        for( $i = 0; $i <= $cycle; $i++) {
            echo $i. ":\n";
            $crit->limit = $this->batchNum;
            $crit->offset = $i * $this->batchNum;
            $users = User::model()->with(array('staff','profile'))->findAll($crit);
            foreach ($users as $user){
                $leavedate = empty($user->staff->leavedate) ? 0 : $user->staff->leavedate;
                $resign = StaffResign::model()->findByAttributes(array('staff_uid'=>$user->uid, 'resign_timestamp'=>$leavedate));
                if($resign)
                    continue;
                $resign = new StaffResign;
                $resign->setAttributes(array(
                    'staff_uid' => $user->uid,
                    'branch_id' => $user->profile->branch,
                    'entry_timestamp' => empty($user->staff->startdate) ? 0: $user->staff->startdate,
                    'resign_timestamp' => $leavedate,
                    'account_close_timestamp' => empty($user->staff->leavedate) ? 0 :$user->staff->leavedate,
                    'processed' => 3,
                    'processed_time' => time(),
                    'updated' => time(),
                    'updated_user' => 0
                ));
                if (!$resign->save()) {
                    print_r($resign->getErrors());
                }else{
                    echo '.';
                }
            }
        }
    }

    public function actionProcessResign(){
        Yii::import('common.models.staff.*');
        Yii::import('common.models.hr.*');
        Yii::app()->language = 'zh_cn';

        /**
         * 处理离职通知邮件 0 & 2
         * 考虑到离职信息不会很多，因此一次全部查出
         * processed 脚本是否处理；0未处理；1已发送离职邮件；2：已处理帐号禁用；3：都已经处理完成
         */

        $endTime = mktime(24,0,0,date('m'), date('d'), date('Y'));

        //先处理帐号禁用: processed = 0, 1;
        $accountCrit = new CDbCriteria();
        $accountCrit->compare('processed', array(0, 1));
        $accountCrit->compare('account_close_timestamp', '<'.$endTime);
        $accountResigns = StaffResign::model()->findAll($accountCrit);
        foreach($accountResigns as $resign){
            echo '.';
            User::model()->updateByPk($resign->staff_uid, array('level'=>0));
            $resign->processed += 2;
            $resign->processed_time = time();
            $resign->save();

            // 更新微信绑定表
            $sql = "select * from ivy_wechat_teacher_bind where teacher_id=".$resign->staff_uid;
            $items = Yii::app()->db->createCommand($sql)->queryAll();
            if ($items) {
                $sql = "update ivy_wechat_teacher_bind set state=0 where teacher_id=".$resign->staff_uid;
                Yii::app()->db->createCommand($sql)->execute();
            }
        }

        //处理邮件通知
        $crit = new CDbCriteria();
        $crit->compare('processed', array(0, 2));
        $crit->compare('resign_timestamp', '<'.$endTime);
        $crit->order = 'branch_id ASC';
        $crit->index = 'staff_uid';

        $todoList = StaffResign::model()->findAll($crit);
        if(empty($todoList)){
            echo "no staff to process...";
            Yii::app()->end();
        }

        $userIds = array();

        foreach($todoList as $resign){
            $resigns[$resign->branch_id][$resign->staff_uid] = $resign;
            $userIds[$resign->staff_uid] = $resign->staff_uid;
            $userIds[$resign->updated_user] = $resign->updated_user;

            //发邮件之前先标记已处理；
            $resign->processed += 1;
            $resign->processed_time = time();
            $resign->save();

            //写入Staff表离职时间
            Staff::model()->updateByPk($resign->staff_uid, array('leavedate' => $resign->resign_timestamp));
            $model = new Staff();
            $model->updateWechat($resign->staff_uid, 'delete');

            // 同步搜索表
            StaffSync::model()->sync($resign->staff_uid);
        }

        if(empty($userIds)){
            echo "no staff to process...";
            Yii::app()->end();
        }

//        $branchObjs = Branch::model()->with('info')->findAll();
//        foreach($branchObjs as $branch){
//            $branchData[$branch->branchid] = array(
//                'title' => $branch->title,
//                'support' => $branch->info->support_email
//            );
//        }
        $branchData = Branch::model()->getTitleEmail();

        $crit = new CDbCriteria();
        $crit->compare('t.uid', $userIds);
        $crit->index = 'uid';
        $users = User::model()->with('profile')->findAll($crit);
        $data['users'] = $users;

        //按校园发送离职清单信息
        foreach($resigns as $_campus => $_resigns){
            $data['resigns'] = $_resigns;
            $data['campus'] = $branchData[$_campus]['title'];
            $isProduction = defined("IS_PRODUCTION")? IS_PRODUCTION : false;
            $subject = $isProduction ? '' : '测试';
            $subject .= sprintf('【%s】 员工离职通知 Staff Resign List', $data['campus']);

            $mailer = Yii::createComponent('common.extensions.mailer.Aliyun');
            $mailer->Subject = $subject;

//            $mailer->AddAddress( $branchData[$_campus]['support'] );
//            if($_campus != 'BJ_DS') {
//                $mailer->AddCC( '<EMAIL>');
//                $mailer->AddCC( '<EMAIL>');
//                $mailer->AddCC( '<EMAIL>');
//                $mailer->AddCC( '<EMAIL>');
//                $mailer->AddCC( '<EMAIL>');
//                $mailer->AddCC( '<EMAIL>');
//            }
            $mailer->pushTo('alumni_notice', $_campus);
            $mailer->getCommandView('resign', array(
                'data' => $data,
            ), 'main');
            $mailer->iniMail($isProduction);
            if (!$mailer->Send()) {
                Yii::log($_campus.','.json_encode($mailer->ErrorInfo), CLogger::LEVEL_INFO, 'alumni.notice');
            }
        }
    }

    /**
     * 新员工入职通知邮件
     */
    public function actionEntryWelcome()
    {
        Yii::import('common.models.*');
        Yii::import('common.models.staff.*');
        Yii::import('common.models.hr.*');
        Yii::app()->language = 'zh_cn';

        $criteria = new CDbCriteria;
        $criteria->compare('user_regdate','>'.strtotime('today'));
        $criteria->compare('isstaff','1');
        $criteria->compare('level','1');
        $newStaffs = User::model()->findAll($criteria);

        if (empty($newStaffs)) {
            echo 'No New Staff...';
            Yii::app()->end();
        }
        $todoList = array();
        foreach ($newStaffs as $newStaff) {
            $todoList[$newStaff->profile->branch][$newStaff->uid] = $newStaff;
        }

        if (empty($todoList)) {
            echo "No New Staff...";
            Yii::app()->end();
        }
        //按照学校发送新员工入职通知邮件
        foreach ($todoList as $key => $value) {
            $campus = Branch::model()->findByPk($key)->title;
            $data = $value;
            $isProduction = defined("IS_PRODUCTION")? IS_PRODUCTION : false;
            $subject = $isProduction ? '' : '测试';
            $subject .= sprintf('【%s】 新员工入职通知 New Staff Entry Message', $campus);

            $mailer = Yii::createComponent('common.extensions.mailer.Aliyun');
            $mailer->Subject = $subject;
            $mailer->pushTo('alumni_notice', $key);
            $maillayout = $key != 'BJ_DS' ? 'main' : 'todsparent';
            $mailer->getCommandView('entrywelcome', array(
                'data' => $data,
                'campus' => $campus,
            ), $maillayout);
            $mailer->iniMail($isProduction);
            if ($mailer->Send()) {
                echo $key.','.count($value)."success \n";
            }else{
                echo $key.','.count($value)."error \n";
                Yii::log($key.','.json_encode($mailer->ErrorInfo), CLogger::LEVEL_INFO, 'entry.welcome');
            }
        }
    }

    public function actionGovTJ($month='')
    {
        Yii::import('common.models.subsidy.*');
        $month = $month ? $month : date('Ym');
        foreach(array('TJ_EC', 'TJ_ES') as $schoolid){
            $yid = Branch::model()->findByPk($schoolid)->schcalendar;
            foreach(array('preschool_subsidy' => '补贴', 'sponsorship_fee' => '代收') as $type=>$value){
                $criteria = new CDbCriteria();
                $criteria->compare('schoolid', $schoolid);
                $criteria->compare('payment_type', $type);
                $criteria->compare('startmonth', $month);
                $modal = BatchInvoices::model()->find($criteria);
                if($modal == null){
                    $modal = new BatchInvoices();
                    $modal->schoolid = $schoolid;
                    $modal->yid = $yid;
                    $modal->startyear = 0;
                    $modal->amount = 0;
                    $modal->status = 0;
                    $modal->create_userid = 0;
                    $modal->payment_type = $type;
                    $modal->startmonth = $month;
                    $modal->endmonth = $month;
                    $modal->title = $month.$value;
                    $modal->create_timestamp = time();
                    if($modal->save()){
                        echo "ok\n";
                    }
                    else{
                        print_r($modal->getErrors());
                    }
                }
            }
        }
    }

    public function actionClearUploadOrphans($subfolder='users', $uploadRootPath=null) {
        if(!empty($subfolder) && !empty($uploadRootPath)) {
            $destFolder = rtrim($uploadRootPath, '/') . '/' . trim($subfolder);
            $destThumbFolder = $destFolder . '/' . 'thumbs';
            $iterator = new DirectoryIterator($destFolder);
            $i=1;
            while($iterator->valid()) {
                if($iterator->isFile()) {
                    $filename = $iterator->getFilename();
                    $filePath = $destFolder . '/' . $filename;
                    $thumbFilePath = $destThumbFolder . '/' . $filename;
                    if(!file_exists($thumbFilePath)) {
                        //echo $thumbFilePath;
                        echo filesize($filePath) . " ". $filename . "              ". $i."\n";
                        @unlink($filePath);
                        //echo $filename . "\n";
                        $i++;
                    }
                }
                $iterator->next();
            }
        }
    }

    /**
     * 补齐个人报告表 开始年字段
     */
    public function actionFixStartyear()
    {
        Yii::import('common.models.calendar.*');
        $items = Calendar::model()->findAll();

        $yidstartyear = CHtml::listData($items, 'yid', 'startyear');

        foreach($yidstartyear as $yid=>$startyear){
            $sql = "update ivy_notes_child set startyear=".$startyear." where yid=".$yid." and isnull(startyear)";
            Yii::app()->db->createCommand($sql)->execute();
            echo ".";
        }
    }

    /**
     * 每日运行查询孩子账单状况并存储
     */
    public function actionCredit()
    {
        Yii::import('common.models.ChildProfileBasic');
        Yii::import('common.models.*');
        Yii::import('common.models.child.ChildCreditNow');
        $total = ChildProfileBasic::model()->count();
        $cycle = ceil($total/$this->batchNum);
        $k = 0;
        $j = 0;
        for ($i=0; $i < $cycle; $i++) {
            $criteria = new CDbCriteria();
            $criteria->limit=$this->batchNum;
            $criteria->offset=$i*$this->batchNum;
            $childModel = ChildProfileBasic::model()->findAll($criteria);
            foreach($childModel as $child){
                $sql = "select childid,classid,schoolid,surplus from ivy_child_credit_now where childid = $child->childid order by updated_timestamp desc limit 1";
                $creditChild = ChildCreditNow::model()->findBySql($sql);
                if(!empty($creditChild)){
                    //判断孩子账户余额是否改变、是否转班或转校
                    if($creditChild->surplus != $child->credit || $creditChild->schoolid != $child->schoolid || $creditChild->classid != $child->classid){
                        $creditChildNow = new ChildCreditNow;
                        $creditChildNow->childid = $child->childid;
                        $creditChildNow->childname = $child->getChildName();
                        $creditChildNow->classid = $child->classid;
                        $creditChildNow->classname = isset($child->ivyclass)?$child->ivyclass->title:'';
                        $creditChildNow->schoolid = $child->schoolid;
                        $creditChildNow->schoolname = isset($child->school)?$child->school->title:'';
                        $creditChildNow->updated_timestamp = time();
                        $creditChildNow->surplus = $child->credit;
                        if($creditChildNow->save()){
                            echo $child->childid."->update,success \n";
                            $j++;
                        }else{
                            echo $child->childid."->update,fail \n";
                        }
                    }
                }else{
                    //储存新加入孩子的记录
                    if($child->credit > 0){
                        $creditChildNow = new ChildCreditNow;
                        $creditChildNow->childid = $child->childid;
                        $creditChildNow->childname = $child->getChildName();
                        $creditChildNow->classid = $child->classid;
                        $creditChildNow->classname = isset($child->ivyclass)?$child->ivyclass->title:'';
                        $creditChildNow->schoolid = $child->schoolid;
                        $creditChildNow->schoolname = isset($child->school)?$child->school->title:'';
                        $creditChildNow->updated_timestamp = time();
                        $creditChildNow->surplus = $child->credit;
                        if($creditChildNow->save()){
                            echo $child->childid."->create,success \n";
                            $k++;
                        }else{
                            echo $child->childid."->create,fail \n";
                        }
                    }
                }
            }
        }
        echo "update $j ;create $k .";
    }
    /**
     * 老生改450添加1000元代收代缴
     */
    public function actionCorrectSepCredit()
    {
        Yii::import('common.models.invoice.*');

        $clv = '/home/<USER>/file.csv';
        $childid = array();
        $i = 0;
        $j = 0;
        $file = fopen($clv, 'r+');
        while ($arr = fgetcsv($file)) {
            $childid = $arr[1];
            $criteria = new CDbCriteria();
            $criteria->compare('schoolid','TJ_EC');
            $criteria->compare('childid',$childid);
            $criteria->compare('payment_type','preschool_subsidy');
            $criteria->compare('installment','2015-09');
            $criteria->compare('fee_type',10);
            $exits = Invoice::model()->exists($criteria);

            if (!$exits) {
                $criteria = new CDbCriteria();
                $criteria->compare('schoolid','TJ_EC');
                $criteria->compare('childid',$childid);
                $criteria->compare('payment_type','tuition');
                $criteria->compare('installment','2015-09');
                $criteria->compare('fee_type',10);
                $invoice = Invoice::model()->find($criteria);
                if($invoice){
                    $invoice->isNewRecord = true;
                    $old_invoice_id = $invoice->invoice_id;
                    unset($invoice->invoice_id);
                    if ($invoice->original_amount == 950) {
                        $invoice->discount_id = 150;
                        $invoice->amount = 500;
                        $invoice->original_amount = 500;
                    }else{
                        $invoice->discount_id = 162;
                        $invoice->amount = 1000;
                        $invoice->original_amount = 1000;
                    }
                    $invoice->payment_type = 'preschool_subsidy';
                    $invoice->title = '2015/09  政府补贴';
                    $invoice->last_paid_timestamp = 0;
                    $invoice->status = 10;
                    $invoice->flag = 0;
                    if ($invoice->save()) {
                        $old_invoice = Invoice::model()->findByPk($old_invoice_id);
                        $old_invoice->flag = $invoice->invoice_id;
                        $old_invoice->save();
                        echo $childid."success \n";
                        $i++;
                    }else{
                        echo $childid.'error';
                        $j++;
                    }
                }
            }
        }
        echo "{$i} success,{$j} fail";
        fclose($file);
    }

    /**
     * 每日运行，修改转学转班的学生历史账单的班级
     * @return [type]        [description]
     */
    public function actionUpdateInvoice($all = false)
    {
        Yii::import('common.models.calendar.*');
        // 取学校当前使用的校历ID
        $calendar = array();
        $calendarSchool = CalendarSchool::model()->findAllByAttributes( array('is_selected'=>1));
        $calendar = array_unique(CHtml::listData($calendarSchool, 'schid', 'yid'));

        Yii::import('common.models.portfolio.ChildStudyHistory');
        Yii::import('common.models.ChildProfileBasic');
        Yii::import('common.models.Branch');
        Yii::import('common.models.invoice.*');
        //查找指定时间内转过班的学生
        $criteria = new CDbCriteria;
        $criteria->compare('calendar' , $calendar);
        $criteria->select = 'hid,childid';
        if (!$all) {
            $criteria->compare('timestamp' , '>'.strtotime('-1 days'));
        }
        $criteria->order = 'timestamp';

        $total = ChildStudyHistory::model()->findAll($criteria);
        $historyIds = CHtml::listData($total, 'childid', 'hid');

        $updateInvoice = 0;
        $data = array();
        $cc = 0;

        foreach($historyIds as $v){
            $transferChild = ChildStudyHistory::model()->findByPk($v);
            if ( in_array($transferChild->child->status, array(10, 20))) {
                $k = 0;
                $j = 0;
                if ($transferChild->classid != $transferChild->child->classid) {
                    continue;
                }
                $classid = $transferChild->classid;
                $criteria = new CDbCriteria;
                $criteria->compare('childid',$transferChild->childid);
                $criteria->compare('calendar_id',$transferChild->calendar);
                $criteria->compare('payment_type',array('lunch','bus','tuition'));
                $criteria->compare('enddate','>'.time());
                $criteria->addNotInCondition('status',array(88,99));
                $invoices = Invoice::model()->findAll($criteria);
                if (empty($invoices)) {
                    continue;
                }
                echo "************** ChildId $transferChild->childid ***************** \r\n";
                foreach ($invoices as $invoice) {
                    if ($invoice->classid != $classid) {
                        echo $transferChild->childid . "\r\n";
                        // 修改相关孩子当前学年账单的班级 id
                        $invoice->classid = $classid;
                        if(!$invoice->save()){
                            echo 'UpdateInvoice: '.$invoice->invoice_id."fail \r\n";
                            $j++;
                            continue;
                        }
                        $updateInvoice++;
                        // 判断数据是否导入了用友
                        if ($invoice->apportion_status == 1) {
                            $cc++;
                            $data1[$invoice->childid] = 1;
                            $criteria = new CDbCriteria;
                            $criteria->compare('childid',$invoice->childid);
                            $criteria->order = 'timestamp desc';
                            $childModel = ChildStudyHistory::model()->find($criteria);
                            $data[$invoice->childid] = array($transferChild->child->school->abb,$transferChild->child->childName,$classid,date('Y-m-d',$childModel->timestamp));
                        }
                        echo 'UpdateInvoice: '.$invoice->invoice_id."success \r\n";
                        $k++;
                        // 修改相关孩子当前学年 invoiceTransaction 的班级 id
                       foreach ($invoice->invoiceTransaction as $invoiceTransaction) {
                           $invoiceTransaction->classid = $classid;
                           $invoiceTransaction->save();
                       }
                       // 修改相关孩子当前学年 childServiceInfo 的班级 id
                       foreach ($invoice->childServiceInfo as $childServiceInfo) {
                           $childServiceInfo->classid = $classid;
                           $childServiceInfo->save();
                       }
                    }
                }
                echo $transferChild->childid.'update:'.$k.',fail:'.$j."\r\n";
            }
        }
        echo 'transferChildern: '.count($historyIds).';updateInvoice: '.$updateInvoice;
        // 发送邮件，childId,childName,classId,date,
        if (empty($data)) {
            return;
        }
        $mailer = Yii::createComponent('common.extensions.mailer.EMailer');
        $mailer->Subject = "【艾毅】转班学生账单已导用友列表";
        $mailer->AddAddress('<EMAIL>');
        $mailer->getCommandView('updateInvoice', array(
        'data' => $data,
        ), 'main');
        $isProduction = defined("IS_PRODUCTION")? IS_PRODUCTION : false;
        $mailer->iniMail($isProduction);
        $mailer->Send();
    }

    /**
     * 已经开放的班级连带下面的孩子转移到其他的校园
     */
    public function actionClassGoto()
    {
        Yii::import('common.models.portfolio.*');
        Yii::import('common.models.invoice.*');
        Yii::import('common.models.classTeacher.*');
        Yii::import('common.models.feedback.*');

        $from = 'TJ_ES';
        $to = 'TJ_EB';
        $classids = array(607,609,613,614); #ES双语的班级ID

        try{
            $c = IvyClass::model()->updateAll(
                array('schoolid'=>$to, 'yid'=>50),
                'classid in ('.implode(',', $classids).')'
            );
            echo $c."\n";

            $c = ChildClassLink::model()->updateAll(
                array('schoolid'=>$to, 'calendar'=>50),
                'classid in ('.implode(',', $classids).')'
            );
            echo $c."\n";

            $c = ChildStudyHistory::model()->updateAll(
                array('schoolid'=>$to, 'calendar'=>50),
                'classid in ('.implode(',', $classids).')'
            );
            echo $c."\n";

            $c = ChildProfileBasic::model()->updateAll(
                array('schoolid'=>$to),
                'classid in ('.implode(',', $classids).')'
            );
            echo $c."\n";

            $criteira = new CDbCriteria();
            $criteira->compare('classid', $classids);
            $criteira->index = 'teacherid';
            $teachers = ClassTeacher::model()->findAll($criteira);

            $c = ClassTeacher::model()->updateAll(
                array('schoolid'=>$to, 'yid'=>50),
                'classid in ('.implode(',', $classids).')'
            );
            echo $c."\n";

            if($teachers){
                $c = UserProfile::model()->updateAll(
                    array('branch'=>$to),
                    'uid in ('.implode(',', array_keys($teachers)).')'
                );
                echo $c."\n";
            }

            $c = Comments::model()->updateAll(
                array('com_school_id'=>$to, 'com_yid'=>50),
                'com_class_id in ('.implode(',', $classids).')'
            );
            echo $c."\n";

            $c = NotesChild::model()->updateAll(
                array('yid'=>50),
                'classid in ('.implode(',', $classids).')'
            );
            echo $c."\n";

            $c = NotesSchCla::model()->updateAll(
                array('schoolid'=>$to, 'yid'=>50),
                'classid in ('.implode(',', $classids).')'
            );
            echo $c."\n";

            $c = SReport::model()->updateAll(
                array('schoolid'=>$to, 'yid'=>50),
                'classid in ('.implode(',', $classids).')'
            );
            echo $c."\n";

            $c = Invoice::model()->updateAll(
                array('schoolid'=>$to, 'calendar_id'=>50),
                'classid in ('.implode(',', $classids).')'
            );
            echo $c."\n";

            $c = InvoiceTransaction::model()->updateAll(
                array('schoolid'=>$to, 'calendar_id'=>50),
                'classid in ('.implode(',', $classids).')'
            );
            echo $c."\n";

            $c = ChildServiceInfo::model()->updateAll(
                array('schoolid'=>$to, 'yid'=>50),
                'classid in ('.implode(',', $classids).')'
            );
            echo $c."\n";

            $c = RefundLunch::model()->updateAll(
                array('schoolid'=>$to, 'yid'=>50),
                'classid in ('.implode(',', $classids).')'
            );
            echo $c."\n";

            $c = InvoiceChildRefund::model()->updateAll(
                array('schoolid'=>$to, 'yid'=>50),
                'classid in ('.implode(',', $classids).')'
            );
            echo $c."\n";

            $c = IvyInvoiceShareUfida::model()->updateAll(
                array('schoolid'=>$to),
                'classid in ('.implode(',', $classids).')'
            );
            echo $c."\n";
        }
        catch(Exception $e){
            print_r($e);
        }
    }

    /**
     * 自动抓取镭豆数据脚本
     * @return [type] [description]
     */
    public function actionGetLasereggData()
    {
        // 设置时区
        date_default_timezone_set('PRC');
        Yii::import('common.models.laseregg.*');
        $total = LasereggInfo::model()->count();
        if (!$total)
            return false;
        $cycle = ceil($total/$this->batchNum);
        for($i=0; $i<$cycle; $i++){
            $criteria = new CDbCriteria();
            $criteria->limit=$this->batchNum;
            $criteria->offset=$i*$this->batchNum;
            $criteria->addCondition('t.laseregg_id != ""');
            $lasereggs = LasereggInfo::model()->findAll($criteria);
            foreach ($lasereggs as $laseregg) {
                $baseUrl = 'http://www.origins-china.cn:8080/topdata/getDataList';
                $timestamp = time();
                // $date = date('Y-m-d H:i:s', $timestamp-60*10);
                $device_id = $laseregg->laseregg_id;
                // $start_time = urlencode($date);
                // $requestUrl = $baseUrl . '?device_id=' . $device_id . '&start_time=' .$start_time;
                $requestUrl = $baseUrl . '?device_id=' . $device_id;
                $opts = array(
                    'http'=>array(
                        'METHOD'=>'GET',
                        'timeout'=>10,
                    )
                );
                $responseCon = file_get_contents($requestUrl, false, stream_context_create($opts));
                $data = json_decode($responseCon);
                if (!($data->code==200&&$data->msg=='Succeed'&&isset($data->data))) {
                    echo $laseregg->laseregg_name . ' No Data' . "\r\n";
                    continue;
                }
                $newData = end($data->data);

                $recieveTime = strtotime($newData->recieveTime);
                if ($timestamp-$recieveTime > 60*10) {
                    echo $laseregg->laseregg_name . ' Data Expired' . "\r\n";
                    continue;
                }
                Yii::import('common.models.CommonUtils');
                // $aqi = CommonUtils::AQI($newData->pm2_5);
                $aqi = $newData->pm2_5;
                if (!$aqi) {
                    echo $laseregg->laseregg_name . ' Data Error' . "\r\n";
                    continue;
                }
                $lasereggLog = new LasereggLog();
                $lasereggLog->laseregg_id = $laseregg->laseregg_id;
                $lasereggLog->laseregg_num = $aqi;
                $lasereggLog->laseregg_mac = $laseregg->laseregg_mac;
                $lasereggLog->time_hour = date('H', $recieveTime);
                $lasereggLog->time_minute = date('m', $recieveTime);
                $lasereggLog->log_timestamp = $recieveTime;
                $lasereggLog->add_timestamp = $timestamp;
                $lasereggLog->add_user = 0;
                $lasereggLog->add_type = LasereggLog::AUTO;
                if (!$lasereggLog->save()) {
                    $errors = current($lasereggLog->getErrors());
                    echo $laseregg->laseregg_name . ' Save Error. ' . $errors[0] . "\r\n";
                }
                echo $laseregg->laseregg_name . ' Save Success'. "\r\n";
                sleep(5);
            }
        }
    }

    /**
     * [actionFixDeposit 修复工作流预缴学费未写入表ivy_child_deposit_history的问题]
     * @param  integer $date [起始时间]
     * @param  integer $exec [是否执行]
     * @return [type]        [transationID]
     */
    public function actionFixDeposit($date = 0, $exec = 0)
    {
        Yii::import('common.models.invoice.*');
        $criteria = new CDbCriteria();
        $criteria->compare('payment_type', 'deposit');
        $criteria->addCondition('t.inout = "out"');
        if (strtotime($date)) {
            $criteria->compare('timestampe', '>'.strtotime($date));
        }
        $transactions = InvoiceTransaction::model()->findAll($criteria);
        foreach ($transactions as $tran) {
            if(!DepositHistory::model()->exists('tran_id=:tran_id', array(':tran_id'=>$tran->id))){
                $model = new DepositHistory();
                //如果孩子当前预缴学费为0则跳过不处理
                if (!$model->getChildDepositBalance($tran->childid))
                    continue;
                if ($exec) {
                    // 特殊处理(183224,254190,262182)

                    // 处理工作流bug导致的deposit问题
                    $model->childid = $tran->childid;
                    $model->yid = $tran->calendar_id;
                    $model->amount = $tran->amount;
                    $model->balance = $model->getChildDepositBalance($tran->childid) - $tran->amount;
                    $model->inout = $tran->inout;
                    $model->tran_id = $tran->id;
                    $model->timestamp = $tran->timestampe;
                    $model->create_uid = $tran->operator_uid;
                    $model->ufida_status = 0;
                    if ($model->save()) {
                        echo $model->tran_id . "success \r\n";
                    }else {
                        echo $model->tran_id . "fail \r\n";
                    }
                } else{
                    echo $tran->id . "\r\n";
                }
            }
        }
    }

    /**
     * [actionUpdateChildSchool 学生学校类型]
     * @param  boolean $all [是否选择所有学生]
     * @return [type]       [description]
     */
    public function actionUpdateChildSchoolType($all = false)
    {
        Yii::import('common.models.ChildProfileBasic');
        Yii::import('common.models.IvyClass');
        Yii::import('common.models.child.ChildSchoolType');
        Yii::import('common.models.child.ChildinfoForUfidaTask');
        Yii::import('common.models.invoice.ChildReserve');

        // $gradeArr = array('mk', 'e1', 'e2', 'e3', 'e4', 'e5');
        $gradeArr = array('e1', 'e2', 'e3', 'e4', 'e5');
        $middleArr = array('e6', 'e7', 'e8');
        $highArr = array('e9', 'e10', 'e11', 'e12');
        $gAge = 6;
        $mAge = 12;
        $hAge = 15;
        $timestamp = 0;
        if (!$all) {
            $timestamp = time() - 3600*24*10;
        }
        //学校为艾毅
            //type_current = k
            //type_next = 根据班级判断,未分班的话根据年龄判断
        //学校为启明星
            //type_current = 根据班级判断,未分班的话根据年龄判断
            //type_next = 根据班级判断,未分班的话根据年龄判断

        $total = ChildProfileBasic::model()->count('updated_timestamp>=:updated_timestamp', array(':updated_timestamp'=>$timestamp));
        $cycle = ceil($total/$this->batchNum);
        for ($i=0; $i < $cycle; $i++) {
            $criteria = new CDbCriteria();
            $criteria->limit = $this->batchNum;
            $criteria->offset = $i*$this->batchNum;
            $criteria->compare('updated_timestamp', '>=' . $timestamp);
            $childModels = ChildProfileBasic::model()->findAll($criteria);
            foreach ($childModels as $childModel) {
                $model = ChildSchoolType::model()->findByPk($childModel->childid);
                if (!$model) {
                    $model = new ChildSchoolType();
                }
                $model->childid = $childModel->childid;
                $model->schoolid = $childModel->schoolid;
                $childAge = date('y', time()) - date('y', $childModel->birthday);
                if (intval(date('m', $childModel->birthday)>9)) {
                    $childAge--;
                }
                $classModel = IvyClass::model()->findByPk($childModel->classid);
                $childReserve = ChildReserve::model()->find('childid=:childid', array(':childid'=>$childModel->childid));
                $netxSchoolId = '';
                if($childReserve){
                    $netxSchoolId = $childReserve->schoolid;
                }
                $model->next_schoolid = $netxSchoolId?$netxSchoolId:$childModel->schoolid;
                if (in_array($childModel->schoolid, array('BJ_DS', 'BJ_SLT'))) {
                    //当前学年是否有班级
                    if ($classModel) {
                        //当前学年学校类型
                        if (in_array($classModel->classtype, $gradeArr)) {
                            $model->type_current = 'g';
                        } elseif(in_array($classModel->classtype, $middleArr)){
                            $model->type_current = 'm';
                        } elseif(in_array($classModel->classtype, $highArr)){
                            $model->type_current = 'h';
                        } else{
                            $model->type_current = 'k';
                        }
                        // 下一学年学校类型
                        if ($childReserve) {
                            $nextClassModel = IvyClass::model()->findByPk($childReserve->classid);
                            if (in_array($nextClassModel->classtype, $gradeArr)) {
                              $model->type_next = 'g';
                            } elseif(in_array($nextClassModel->classtype, $middleArr)){
                              $model->type_next = 'm';
                            } elseif(in_array($classModel->classtype, $highArr)){
                              $model->type_next = 'h';
                            } else{
                              $model->type_next = 'k';
                            }
                        } else{
                            // 判断孩子是否为在读状态
                            if ($childModel->status == ChildProfileBasic::STATS_ACTIVE_WAITING ||
                                $childModel->status == ChildProfileBasic::STATS_ACTIVE) {
                                if ($classModel->child_age+1 < $gAge) {
                                    $model->type_next = 'k';
                                } elseif($classModel->child_age+1 < $mAge){
                                    $model->type_next = 'g';
                                } elseif(in_array($classModel->classtype, $highArr)){
                                    $model->type_next = 'h';
                                } else{
                                    $model->type_next = 'm';
                                }
                            } else{
                                if ($childAge+1 < $gAge) {
                                    $model->type_next = 'k';
                                } elseif($childAge+1 < $mAge){
                                    $model->type_next = 'g';
                                } elseif(in_array($classModel->classtype, $highArr)){
                                    $model->type_next = 'h';
                                } else{
                                    $model->type_next = 'm';
                                }
                            }
                        }
                    } else{
                        //当前学年学校类型
                        if ($childAge < $gAge) {
                            $model->type_current = 'k';
                        } elseif($childAge < $mAge){
                            $model->type_current = 'g';
                        } elseif($childAge < $hAge){
                            $model->type_current = 'm';
                        } else{
                            $model->type_current = 'h';
                        }
                        //下一学年学校类型
                        if ($childReserve) {
                            $nextClassModel = IvyClass::model()->findByPk($childReserve->classid);
                            if (in_array($nextClassModel->classtype, $gradeArr)) {
                                $model->type_next = 'g';
                            } elseif(in_array($nextClassModel->classtype, $middleArr)){
                                $model->type_next = 'm';
                            } elseif(in_array($nextClassModel->classtype, $highArr)){
                                $model->type_next = 'h';
                            } else{
                                $model->type_next = 'k';
                            }
                        } else{
                            if ($childAge+1 < $gAge) {
                                $model->type_next = 'k';
                            } elseif($childAge+1 < $mAge){
                                $model->type_next = 'g';
                            } elseif($childAge+1 < $hAge){
                                $model->type_next = 'm';
                            } else{
                                $model->type_next = 'h';
                            }
                        }
                    }
                } else{
                    $model->type_current = 'k';
                    // 计算学生下学年学校类型
                    if ($childReserve && $childReserve->classid) {
                        $nextClassModel = IvyClass::model()->findByPk($childReserve->classid);
                        if ($nextClassModel) {                            
                            if (in_array($nextClassModel->classtype, $gradeArr)) {
                                $model->type_next = 'g';
                            } elseif(in_array($nextClassModel->classtype, $middleArr)){
                                $model->type_next = 'm';
                            } else{
                                $model->type_next = 'k';
                            }
                        }
                    } else{
                        if ($childAge+1 < $gAge) {
                            $model->type_next = 'k';
                        } elseif($childAge+1 < $mAge){
                            $model->type_next = 'g';
                        } else{
                            $model->type_next = 'm';
                        }
                    }
                }
                if($model->save()){
                    if ($childModel->ufida_update == 0 || ($netxSchoolId && $childModel->schoolid != $netxSchoolId)) {
                        // $uModel = new ChildinfoForUfidaTask();
                        // $uModel->childid = $childModel->childid;
                        // if ($netxSchoolId) {
                        //     $uModel->schoolid = $netxSchoolId;
                        //     $schoolid = $netxSchoolId;
                        // } else{
                        //     $uModel->schoolid = $childModel->schoolid;
                        //     $schoolid = $childModel->schoolid;
                        // }

                        if ($netxSchoolId) {
                            $schoolid = $netxSchoolId;
                        } else{
                            $schoolid = $childModel->schoolid;
                        }

                        $criteria = new CDbCriteria();
//                        $criteria->compare('status', '0');
                        $criteria->compare('schoolid', $schoolid);
                        $criteria->compare('childid', $childModel->childid);
                        $uModel = ChildinfoForUfidaTask::model()->find($criteria);
                        if (!$uModel) {
                            $uModel = new ChildinfoForUfidaTask();
                        }
                        $uModel->childid = $childModel->childid;
                        $uModel->schoolid = $schoolid;
                        $uModel->status = 0;
                        if(!$uModel->save()){
                            echo $childModel->childid.": ivy_childinfo_for_ufida_task fail \n";
                        }
                    }
                    echo $childModel->childid . ' success' . "\r\n";
                } else{
                    echo $childModel->childid . ' fail' . "\r\n";
                    print_r($model->getErrors());
                    Yii::app()->end();
                }
            }

        }
    }

    public function actionFixServiceInfo($exec = 0)
    {
        Yii::import('common.models.invoice.ChildServiceInfo');
        Yii::import('common.models.invoice.InvoiceChildRefund');

        $crit = new CDbCriteria();
        $crit->compare('status', InvoiceChildRefund::STATS_COMPLETED);
        $crit->compare('schoolid', 'BJ_DS');
        $total = InvoiceChildRefund::model()->count($crit);
        $cycle = ceil($total/$this->batchNum);
        $kk = 0;
        for ($i=0; $i < $cycle; $i++) {
            $crit->limit = $this->batchNum;
            $crit->offset = $i * $this->batchNum;
            $crit->order = 'rid';
            $childRefunds = InvoiceChildRefund::model()->findAll($crit);
            foreach ($childRefunds as $childRefund) {
                $crit2 = new CDbCriteria();
                $crit2->compare('invoice_id', $childRefund->on_invoice_id);
                $crit2->addCondition("startdate<={$childRefund->startdate} and enddate>={$childRefund->enddate}");
                $serviceInfo = ChildServiceInfo::model()->find($crit2);
                if($serviceInfo) {
                    if ($exec) {
                        // $transaction=$serviceInfo->dbConnection->beginTransaction();
                        //退前半部分
                        if ($childRefund->startdate == $serviceInfo->startdate && $childRefund->enddate < $serviceInfo->enddate) {
                            $serviceInfo->startdate = strtotime("+1 day", $childRefund->enddate);
                            $info = ',fail';
                            if($serviceInfo->save()) {
                                $info = ',updated';
                            }
                            echo $serviceInfo->id . $info . "\r\n";

                        //退中间部分
                        } elseif ($childRefund->startdate > $serviceInfo->startdate && $childRefund->enddate < $serviceInfo->enddate) {
                            $newServiceInfo = new ChildServiceInfo();
                            $newServiceInfo->setAttributes($serviceInfo->getAttributes());

                            $newServiceInfo->enddate = strtotime('-1 day',$childRefund->startdate);
                            if($newServiceInfo->save()){
                                $serviceInfo->startdate = strtotime("+1 day", $childRefund->enddate);
                                $info = ',fail';
                                if($serviceInfo->save()) {
                                    $info = ',updated';
                                }
                                echo $serviceInfo->id . $info . "\r\n";
                                echo $newServiceInfo->id . ',add' . "\r\n";
                            }

                        // 退后半部分
                        } elseif ($childRefund->startdate > $serviceInfo->startdate && $childRefund->enddate == $serviceInfo->enddate) {
                            $serviceInfo->enddate = strtotime("-1 day", $childRefund->startdate);
                            $info = ',fail';
                            if($serviceInfo->save()) {
                                $info = ',updated';
                            }
                            echo $serviceInfo->id . $info . "\r\n";

                        // 全退
                        } else{
                            $info = ',fail';
                            if($serviceInfo->delete()) {
                                $info = ',deleted';
                            }
                            echo $serviceInfo->id . $info . "\r\n";
                        }
                        // $transaction->rollBack();
                    } else{
                        echo $childRefund->rid . ',' .$serviceInfo->invoice_id. "\r\n";
                    }
                    $kk ++;
                }
            }
        }
        echo 'total:' . $kk;
    }

    /**
     * [将账单更改历史中关联的invoice表 send_timestamp 字段补成正确的值]
     * @param  integer $exec [是否执行]
     * @return [type]        [执行结果]
     */
    public function actionFixChangedInvoice($exec = 0)
    {
        Yii::import('common.models.invoice.*');

        $timestamp = strtotime(date('Y-m-1', time()));
        $criteria = new CDbCriteria();
        $criteria->compare('t.operate_timestamp', '>' . $timestamp);
        $criteria->compare('invoice.status', Invoice::STATS_CHANGE_ARCHIVED);
        $criteria->with = 'invoice';
        $changedInvoices = InvoiceChangeHistory::model()->findAll($criteria);
        $total = 0;
        $success = 0;
        $fail = 0;
        foreach ($changedInvoices as $changedInvoice) {
            if (!$changedInvoice->invoice->send_timestamp) {
                $total ++;
                if (!$exec) {
                    echo $changedInvoice->invoice_id . "\r\n";
                } else {
                        $changedInvoice->invoice->send_timestamp = $changedInvoice->operate_timestamp;
                        if($changedInvoice->invoice->save()){
                            $success ++;
                            echo $changedInvoice->invoice_id . " success\r\n";
                        } else{
                            $fail ++;
                            echo $changedInvoice->invoice_id . " fail:" . current($changedInvoice->invoice->getErrors()) . "\r\n";
                        }
                    }
                }
            }
        echo '***************************************' . "\r\n";
        echo 'total:' . $total . "\r\nsuccess:$success\r\nfail:$fail";
    }

    /**
     * [actionFixInvoiceClass 解决开账单时，班级ID和学年不符的情况]
     * @param string $value [description]
     */
    public function actionFixInvoiceClass($exec = 0)
    {
        Yii::import('common.models.calendar.*');
        Yii::import('common.models.invoice.*');

        $branchList = Branch::model()->getBranchList();
        $file = fopen('./data2.csv', 'w+') or die('open file fail');
        foreach ($branchList as $key => $branch) {
            $yid = CalendarSchool::model()->getCalendarId($key, 2016);
            $currentYid = CalendarSchool::model()->getCalendarId($key, 2015);
            $classModel = IvyClass::getClassList($key, $currentYid);
            if($classModel && $yid){
                $classList = array();
                foreach ($classModel as $class) {
                    $classList[] = $class->classid;
                }
                $criteria = new CDbCriteria;
                $criteria->compare('schoolid', $key);
                $criteria->compare('calendar_id', $yid);
                $criteria->compare('classid', $classList);
                $criteria->compare('payment_type', array('tuition','bus','lunch','entrance'));
                // $criteria->addNotInCondition('classid', $classList);

                $invoices = Invoice::model()->findAll($criteria);

                $i = 0;
                $text = '';
                foreach ($invoices as $invoice) {
                    if (!$exec) {
                        $text .= $invoice->schoolid .','. $invoice->childid .','. $invoice->classid .','. $invoice->invoice_id;
                        $text .= "\n";
                        foreach ($invoice->invoiceTransaction as $invoiceTransaction) {
                            if($invoiceTransaction->ufida_income){
                                echo $invoice->childid . ',transaction,' . $invoiceTransaction->id .','. $invoiceTransaction->ufida_income;
                                echo "\n";
                                $i++;
                            }
                        }
                    } else{
                        $transaction = Yii::app()->db->beginTransaction();
                        $model = ChildReserve::model()->findByAttributes(array('schoolid'=>$key, 'calendar'=>$yid, 'childid'=>$invoice->childid));
                        if($model){
                            $classid = $model->classid;
                            // 修改相关孩子 invoice 的班级 id
                            $invoice->classid = $classid;
                            if($invoice->save()){
                                echo 'Invoice' . $invoice->invoice_id . 'success';
                            } else{
                                echo '!! Invoice' . $invoice->invoice_id . 'fail';
                                $transaction->rollBack();
                            }
                            echo "\r\n";
                            // 修改相关孩子 invoiceTransaction 的班级 id
                            foreach ($invoice->invoiceTransaction as $invoiceTransaction) {
                                $invoiceTransaction->classid = $classid;
                                if($invoiceTransaction->save()){
                                    echo ' Transaction' . $invoiceTransaction->id . 'success';
                                } else{
                                    echo ' !! Transaction' . $invoiceTransaction->id . 'fail';
                                    $transaction->rollBack();
                                }
                                echo "\r\n";
                            }
                            // 修改相关孩子 childServiceInfo 的班级 id
                            foreach ($invoice->childServiceInfo as $childServiceInfo) {
                                $childServiceInfo->classid = $classid;
                                if($childServiceInfo->save()){
                                    echo '  Service' . $childServiceInfo->id . 'success';
                                } else{
                                    echo '  !! Service' . $childServiceInfo->id . 'fail';
                                    $transaction->rollBack();
                                }
                                echo "\r\n";
                            }
                        } else{
                            echo $invoice->invoice_id . ",no childid \r\n";
                            die();
                        }
                        $transaction->commit();
                    }
                }
                if (!$exec) {
                    echo '-------------------------------';
                    echo "{$key},ufidaIncome " . $i;
                    echo '-------------------------------';
                    echo "\r\n";
                    fwrite($file, $text);
                }
                continue;
            }
        }
        fclose($file);
    }

	public function actionFixnotes()
	{
		Yii::import('common.models.portfolio.NotesSchCla');

		$school = 'BJ_DS';
		$type = '20';

		$criteria = new CDbCriteria();
		$criteria->select = 'id, type, schoolid, classid, weeknumber, yid, updated_timestamp';
		$criteria->compare('schoolid',$school);
		$criteria->compare('type',$type);
		$Fixnotes = NotesSchCla::model()->findAll($criteria);
		$result=array();
		foreach($Fixnotes as $k=> $model){
			$result[$k]['id']=$model->id;
			$result[$k]['schoolid']=$model->schoolid;
			$result[$k]['classid']=$model->classid;
			$result[$k]['yid']=$model->yid;
			$result[$k]['type']=$model->type;
			$result[$k]['weeknumber']=$model->weeknumber;
			$result[$k]['updated_timestamp']=date("Y-m-d H:i:s", $model->updated_timestamp);
		}
		$results = array();
		$repeat = array();
		if($type == 20){
			foreach($result as $k=>$v) {
				$results[$v["weeknumber"]][$v["classid"]][$v["yid"]][] = $v;
			}
			foreach($results as $k=>$v) {
				foreach($v as $vs) {
					foreach($vs as $vsw) {
						$repeat[] = $vsw;
					}
				}
			}
		}else{
			foreach($result as $k=>$v) {
				$results[$v["schoolid"]][$v["weeknumber"]][$v["classid"]][$v["yid"]][] = $v;
			}
			foreach($results as $k=>$v) {
				foreach($v as $vs) {
					foreach($vs as $vsw) {
						foreach($vsw as $vsd) {
							$repeat[] = $vsd;
						}
					}
				}
			}
		}
		$array = array();
		foreach($repeat as $v){
			if(count($v) != 1){
				$array[] = $v;
			}
		}
		print_r($array);
	}

    /**
     * [修复错误的用友数据，冲掉一条，添加一条]
     * @param  string $schoolid [学校 all 为所有]
     * @param  string $exec [是否执行]
     * @return [type]           [description]
     */
    public function actionFixUfidaData($schoolid,$exec=0)
    {
        date_default_timezone_set('PRC');
        Yii::import('common.models.calendar.*');
        Yii::import('common.models.invoice.*');

        $branchList = array_keys(Branch::model()->getBranchList());
        array_push($branchList, 'all');
        if (!$schoolid || !in_array($schoolid, $branchList)) {
            echo 'school not exits';
            return;
        }
        $file = file('./data.csv');
        $i = 0;
        foreach ($file as $string) {
            $arr = explode(',', $string);
            $childId = isset($arr[0]) ? $arr[0] : '';
            $wrongClass = isset($arr[1]) ? $arr[1] : '';
            $invoiceId = isset($arr[2]) ? $arr[2] : '';
            $transactionId = isset($arr[3]) ? $arr[3] : '';
            if ($childId && $wrongClass && $transactionId) {
                $criteria = new CDbCriteria;
                if ($schoolid != 'all') {
                    $criteria->compare('schoolid', $schoolid);
                }
                $criteria->compare('childid', $childId);
                $criteria->compare('classid', $wrongClass);
                $criteria->compare('invoice_id', $invoiceId);
                $criteria->compare('transaction_id', $transactionId);
                $ufidaIncome = UfidaIncome::model()->findAll($criteria);
                if ($ufidaIncome && count($ufidaIncome)==1) {
                    $ufidaIncome = current($ufidaIncome);
                    if ($ufidaIncome->status != 1) {
                        continue;
                    }
                    if (!$exec) {
                        echo $ufidaIncome->id;
                        echo "\r\n";
                        $i++;
                        continue;
                    }
                    $transaction = InvoiceTransaction::model()->findByPk($transactionId);
                    $year = date('Y', $transaction->timestampe);
                    $month = date('m', $transaction->timestampe);
                    $status = $month * 10;
                    $month ++;
                    $timestmp = strtotime($year .'-'. $month) - 2;
                    // 处理银行转账的情况
                    if ($ufidaIncome->type == 'banktransfer' && $ufidaIncome->transferid) {
                        $bankUfida = ufidaIncome::model()->findAllByAttributes(array('transferid'=>$ufidaIncome->transferid));
                        foreach ($bankUfida as $v) {
                            $newUfidaIncome = new UfidaIncome;
                            $newUfidaIncome->setAttributes($v->attributes);
                            $newUfidaIncome->amount = 0-$v->amount;
                            $newUfidaIncome->status = $status;
                            $newUfidaIncome->timestmp = $timestmp;
                            $newUfidaIncome->transferid = $ufidaIncome->transferid . '_1';
                            if ($newUfidaIncome->save()) {
                                echo "new bankUfida success," . $newUfidaIncome->id;
                                echo "\r\n";
                                $yid = CalendarSchool::model()->getCalendarId($v->schoolid, 2016);
                                $childReserve = ChildReserve::model()->findByAttributes(array('schoolid'=>$v->schoolid, 'calendar'=>$yid, 'childid'=>$ufidaIncome->childid));
                                if ($childReserve) {
                                    $newClassid = $childReserve->classid;
                                } else{
                                    $newClassid = 0;
                                }
                                $rightUfidaIncome = new UfidaIncome;
                                $rightUfidaIncome->setAttributes($v->attributes);
                                $rightUfidaIncome->status = $status;
                                $rightUfidaIncome->classid = $newClassid;
                                $rightUfidaIncome->timestmp = $timestmp;
                                $rightUfidaIncome->transferid = $ufidaIncome->transferid . '_2';
                                if ($rightUfidaIncome->save()) {
                                    echo "right bankUfida success," . $rightUfidaIncome->id;
                                    echo "\r\n";
                                    $i++;
                                } else{
                                    $error = current($rightUfidaIncome->getErrors());
                                    echo "right bankUfida fail," . $v->id . 'error:' . $error[0];
                                    echo "\r\n";
                                }
                            } else{
                                $error = current($newUfidaIncome->getErrors());
                                echo "new bankUfida fail," . $v->id . 'error:' . $error[0];
                                echo "\r\n";
                            }
                        }
                        continue;
                    }
                    $newUfidaIncome = new UfidaIncome;
                    $newUfidaIncome->setAttributes($ufidaIncome->attributes);
                    $newUfidaIncome->amount = 0-$ufidaIncome->amount;
                    $newUfidaIncome->status = $status;
                    $newUfidaIncome->timestmp = $timestmp;
                    if ($newUfidaIncome->save()) {
                        echo "new ufida success," . $newUfidaIncome->id;
                        echo "\r\n";
                        $yid = CalendarSchool::model()->getCalendarId($ufidaIncome->schoolid, 2016);
                        $childReserve = ChildReserve::model()->findByAttributes(array('schoolid'=>$ufidaIncome->schoolid, 'calendar'=>$yid, 'childid'=>$ufidaIncome->childid));
                        $newClassid = $childReserve->classid;
                        $rightUfidaIncome = new UfidaIncome;
                        $rightUfidaIncome->setAttributes($ufidaIncome->attributes);
                        $rightUfidaIncome->status = $status;
                        $rightUfidaIncome->classid = $newClassid;
                        $rightUfidaIncome->timestmp = $timestmp;
                        if ($rightUfidaIncome->save()) {
                            echo "right ufida success," . $rightUfidaIncome->id;
                            echo "\r\n";
                            $i++;
                        } else{
                            $error = current($rightUfidaIncome->getErrors());
                            echo "right ufida fail," . $ufidaIncome->id . 'error:' . $error[0];
                            echo "\r\n";
                        }
                    } else{
                        $error = current($newUfidaIncome->getErrors());
                        echo "new ufida fail," . $ufidaIncome->id . 'error:' . $error[0];
                        echo "\r\n";
                    }
                }
            }
        }
        echo $i;
    }
	/**
     * [把现在的来访记录有效的状态变10   无效变20]
     */
    public function actionVisitsRecord($school = '')
    {
		if (!$school){
			$branchList = Branch::model()->getBranchList();
			//echo $this->getHelp(); exit;
			foreach($branchList as $key=>$value){
				Yii::import('common.models.visit.IvyVisitsRecord');
				$criteria = new CDbCriteria();
				$criteria->select = 'id, basic_id,visit_timestamp,update_timestamp';
				$criteria->compare('schoolid',$key);
				$criteria->order = 'visit_timestamp desc';
				$VisitsRecord = IvyVisitsRecord::model()->count($criteria);

				$count = ceil($VisitsRecord/$this->batchNum);
				for($i=0; $i<$count; $i++){
					$criteria->limit=$this->batchNum;
					$criteria->offset=$i*$this->batchNum;
					$VisitsRecord = IvyVisitsRecord::model()->findAll($criteria);

					$result=array();
					$results=array();
					foreach($VisitsRecord as $k=> $model){
						$result[$k]['id']=$model->id;
						$result[$k]['basic_id']=$model->basic_id;
						$result[$k]['visit_timestamp']=$model->visit_timestamp;
						$result[$k]['update_timestamp']=$model->update_timestamp;
					}

					foreach($result as $k=>$v) {
							$results[$v["basic_id"]][] = $v;
					}
					$array = array();
					foreach($results as $v){
						$model = new IvyVisitsRecord();
						if(count($v) == 1){
							foreach($v as $value){
								$basicInfo = IvyVisitsRecord::model()->findByPk($value['id']);
								$basicInfo->status = 10;
								if($basicInfo->save()){
									print_r($basicInfo->id);
								}
							}
						}
						if(count($v) != 1){
							$time = array();
							foreach($v as  $k =>$val){
								$time[$k] = $val['visit_timestamp'];
							}
							foreach($v as $value){
								if($time[0] == $value['visit_timestamp']){
									$basicInfo = IvyVisitsRecord::model()->findByPk($value['id']);
									$basicInfo->status = 10;
									if($basicInfo->save()){
										print_r($basicInfo->id);
									}
								}
								if($time[0] != $value['visit_timestamp']){
									$basicInfo = IvyVisitsRecord::model()->findByPk($value['id']);
									$basicInfo->status = 20;
									if($basicInfo->save()){
										print_r($basicInfo->id);
									}
								}
							}
						}
					}
				}
			}
		}else{
			Yii::import('common.models.visit.IvyVisitsRecord');
			$criteria = new CDbCriteria();
			$criteria->select = 'id, basic_id,visit_timestamp,update_timestamp';
			$criteria->compare('schoolid',$school);
			$count = IvyVisitsRecord::model()->count($criteria);

			$VisitsRecordCount = ceil($count/$this->batchNum);
			for($i=0; $i<$VisitsRecordCount; $i++){
				$criteria->limit=$this->batchNum;
				$criteria->offset=$i*$this->batchNum;
				$criteria->order = 'visit_timestamp desc';
				$VisitsRecord = IvyVisitsRecord::model()->findAll($criteria);

				$result=array();
				$Record=array();
				foreach($VisitsRecord as $k=> $model){
					$result[$k]['id']=$model->id;
					$result[$k]['basic_id']=$model->basic_id;
					$result[$k]['visit_timestamp']=$model->visit_timestamp;
					$result[$k]['update_timestamp']=$model->update_timestamp;
				}

				foreach($result as $k=>$v) {
						$Record[$v["basic_id"]][] = $v;
				}
				$array = array();
				foreach($Record as $v){
					$model = new IvyVisitsRecord();
					if(count($v) == 1){
						foreach($v as $value){
							$basicInfo = IvyVisitsRecord::model()->findByPk($value['id']);
							$basicInfo->status =10;
							if($basicInfo->save()){
								print_r($basicInfo->id);
								echo '-----';
							}
						}
					}
					if(count($v) != 1){
						$time = array();
						foreach($v as  $k =>$val){
							$time[$k] = $val['visit_timestamp'];
						}
						foreach($v as $value){
							if($time[0] == $value['visit_timestamp']){
								$basicInfo = IvyVisitsRecord::model()->findByPk($value['id']);
								$basicInfo->status = 10;
								if($basicInfo->save()){
									print_r($basicInfo->id);
									echo '-----';
								}
							}
							if($time[0] != $value['visit_timestamp']){
								$basicInfo = IvyVisitsRecord::model()->findByPk($value['id']);
								$basicInfo->status = 20;
								if($basicInfo->save()){
									print_r($basicInfo->id);
									echo '-----';
								}
							}
						}
					}
				}
				echo '//////////////////////';
			}
		}
	}

	public function actionVisitsRepeat($school = '')
    {
		if (!$school){
			$branchList = Branch::model()->getBranchList();

			foreach($branchList as $key=>$value){
				Yii::import('common.models.visit.*');
				$criteria = new CDbCriteria();
				$criteria->select = 'id, basic_id,visit_timestamp,update_timestamp,appointment_date';
				$criteria->compare('schoolid',$key);
				$criteria->order = 'update_timestamp desc';
				$count = IvyVisitsRecord::model()->count($criteria);

				$VisitsRepeatCount = ceil($count/$this->batchNum);
				for($i=0; $i<$VisitsRepeatCount; $i++){
					$criteria->limit=$this->batchNum;
					$criteria->offset=$i*$this->batchNum;
					$VisitsRepeat = IvyVisitsRecord::model()->findAll($criteria);

					$result=array();
					$results=array();
					$repeat=array();
					foreach($VisitsRepeat as $k=> $model){
						$result[$k]['id']=$model->id;
						$result[$k]['basic_id']=$model->basic_id;
						$result[$k]['visit_timestamp']=$model->visit_timestamp;
						$result[$k]['update_timestamp']=$model->update_timestamp;
						$result[$k]['visit_id']=$model->newlog->visit_id;
						$result[$k]['appointment_date']=$model->appointment_date;
					}

					foreach($result as $k=>$v) {
							$results[$v["appointment_date"]][$v["basic_id"]][] = $v;
					}
					foreach($results as $k=>$v) {
						foreach($v as $vs){
							$repeat[] = $vs;
						}
					}

					foreach($repeat as $k=>$v){
						if(count($v) > 1){
							foreach($v as $ve){
								$visitsRecord = IvyVisitsRecord::model()->findByPk($ve['id']);
								if($ve['update_timestamp'] == $v[0]['update_timestamp']){
									$visitsRecord->status = 10;
								}else{
									$visitsRecord->status = 30;
								}
								if($visitsRecord->save()){
									print_r($visitsRecord->id);
									echo '-----';
								}
							}
						}
					}
				}
			}
		}else{
			Yii::import('common.models.visit.*');
			$criteria = new CDbCriteria();
			$criteria->select = 'id, basic_id,visit_timestamp,update_timestamp,appointment_date';
			$criteria->compare('schoolid',$school);
			$criteria->order = 'update_timestamp desc';
			$count = IvyVisitsRecord::model()->count($criteria);

			$VisitsRepeatCount = ceil($count/$this->batchNum);
			for($i=0; $i<$VisitsRepeatCount; $i++){
				$criteria->limit=$this->batchNum;
				$criteria->offset=$i*$this->batchNum;
				$VisitsRepeat = IvyVisitsRecord::model()->findAll($criteria);

				$result=array();
				$repeat=array();
				$results=array();
				foreach($VisitsRepeat as $k=> $model){
					$result[$k]['id']=$model->id;
					$result[$k]['basic_id']=$model->basic_id;
					$result[$k]['visit_timestamp']=$model->visit_timestamp;
					$result[$k]['update_timestamp']=$model->update_timestamp;
					$result[$k]['visit_id']=$model->newlog->visit_id;
					$result[$k]['appointment_date']=$model->appointment_date;
				}

				foreach($result as $k=>$v) {
						$results[$v["appointment_date"]][$v["basic_id"]][] = $v;
				}
				foreach($results as $k=>$v) {
					foreach($v as $vs){
						$repeat[] = $vs;
					}
				}

				foreach($repeat as $k=>$v){
					if(count($v) > 1){
						foreach($v as $ve){
							$visitsRecord = IvyVisitsRecord::model()->findByPk($ve['id']);
							if($ve['update_timestamp'] == $v[0]['update_timestamp']){
								$visitsRecord->status = 10;
							}else{
								$visitsRecord->status = 30;
							}
							if($visitsRecord->save()){
									print_r($visitsRecord->id);
									echo '-----';
								}
						}
					}
				}
			}
		}
	}

    public function actionCalendarSemester()
    {
        Yii::import('common.models.calendar.*');
        $criteria = new CDbCriteria();
        $criteria->select = 'yid,timepoints';
        $count = Calendar::model()->findAll($criteria);//所有的校历
        $Calendar = array();
        foreach($count as $k=>$v){
            $Calendar[] = $v->yid;
        }
        $criteria = new CDbCriteria();
        $criteria->select = 'yid';
        $criteria->compare("semester_flag",10);
        $counts = CalendarSemester::model()->findAll($criteria);// 时间分开
        $Semester = array();
        foreach($counts as $k=>$val){
            $Semester[] = $val->yid;
        }
        $diffA = array_diff($Calendar, $Semester);
        if($diffA){
        $criteria = new CDbCriteria();
        $criteria->select = 'yid,timepoints';
        $criteria->compare("yid",$diffA);
        $countssad = Calendar::model()->findAll($criteria);
        $i = 10;
        foreach($countssad as $key=>$value){
            $arr = explode(",",$value->timepoints);
            if($i == 10){
                $Calendar = new CalendarSemester();
                $Calendar->yid = $value->yid;
                $Calendar->semester_flag = $i;
                $Calendar->school_start_timestamp = $arr[0];
                $Calendar->school_end_timestamp = $arr[1];
                $Calendar->save();
                $i += 10;
            }
            if($i == 20){
                $Calendar = new CalendarSemester();
                $Calendar->yid = $value->yid;
                $Calendar->semester_flag = $i;
                $Calendar->school_start_timestamp = $arr[2];
                $Calendar->school_end_timestamp = $arr[3];
                $Calendar->save();
                $i -= 10;
            }
        }}
    }

    // 将校历表内的学期开始结束时间写入到 CalendarSemester 中
    public function actionCalendarSemester2()
    {
        Yii::import('common.models.calendar.*');
        $calendarYearlys = Calendar::model()->findAll();
        $array = array();
        $up = 10;
        $down = 20;
        foreach ($calendarYearlys as $calendarYearly) {
            $yid = $calendarYearly->yid;
            $timepoints = explode(',', $calendarYearly->timepoints);
            if (count($timepoints) != 4) {
                echo 'timepoints error';
                Yii::app()->end();
            }

            $array[$yid][$up] = array($timepoints[0], $timepoints[1]);
            $array[$yid][$down] = array($timepoints[2], $timepoints[3]);
        }

        foreach ($array as $k => $v) {
            // 上半年
            $criteria = new CDbCriteria;
            $criteria->compare('yid', $k);
            $criteria->compare('semester_flag', $up);
            $upSemester = CalendarSemester::model()->find($criteria);
            if (!$upSemester) {
                $upSemester = new CalendarSemester;
                $upSemester->yid = $k;
                $upSemester->semester_flag = $up;
                $upSemester->school_start_timestamp = $v[$up][0];
                $upSemester->school_end_timestamp = $v[$up][1];
                if (!$upSemester->save()) {
                    print_r(current($upSemester->getErrors()));
                    Yii::app()->end();
                }
                echo $k ."-up success \r\n";
            }

            // 下半年
            $criteria = new CDbCriteria;
            $criteria->compare('yid', $k);
            $criteria->compare('semester_flag', $down);
            $downSemester = CalendarSemester::model()->find($criteria);
            if (!$downSemester) {
                $downSemester = new CalendarSemester;
                $downSemester->yid = $k;
                $downSemester->semester_flag = $down;
                $downSemester->school_start_timestamp = $v[$down][0];
                $downSemester->school_end_timestamp = $v[$down][1];
                if (!$downSemester->save()) {
                    print_r(current($downSemester->getErrors()));
                    Yii::app()->end();
                }
                echo $k ."-down success \r\n";
            }
        }

    }

    public function actionSearchChildID()
    {
        $csvPath = 'C:/GJ.csv';
        $newcsvPath = 'C:/g.csv';

        $data = file($csvPath);

        if($data){
            $handle = fopen($newcsvPath, "w+");
            foreach($data as $datum){
                if($datum){
                    $itemData = explode(',', $datum);

                    if($itemData[0]){
                        $criteria = new CDbCriteria();
                        $childName = iconv("gbk", "utf-8", $itemData[0]);
                        if (!$childName) {
                            $childName = iconv("gbk2312", "utf-8", $itemData[0]);
                        }
                        $criteria->compare('name_cn', $childName);
                        $criteria->compare('schoolid', 'XA_GJ');
                        $criteria->compare('childid', '<>6847');
                        $childs = ChildProfileBasic::model()->findAll($criteria);
                        if(count($childs) == 1){
                            fwrite($handle, $childs[0]->childid.','.$itemData[0].','.$itemData[1]);
                        }
                        else{
                            echo $itemData[0];die;
                        }

                        echo ".\n";
                    }
                }
            }
            fclose($handle);
        }
    }

    // 修改应当设为毕业的孩子状态
    public function actionUpdateChildStatus()
    {
        Yii::import('common.models.calendar.*');

        $startYear = 2016;
        $branchList = Branch::model()->getBranchList(false, true);
        foreach ($branchList as $branchid => $branch) {

            $criteria = new CDbCriteria;
            $criteria->compare('status', ChildProfileBasic::STATS_ACTIVE);
            $criteria->compare('schoolid', $branchid);
            $criteria->compare('classid', '>0');

            $count = ChildProfileBasic::model()->count($criteria);

            $cycle = ceil($count / $this->batchNum);
            for($i = 0; $i < $cycle; $i++){
                $criteria->limit=$this->batchNum;
                //$criteria->offset=$i*$this->batchNum;
                $childList = ChildProfileBasic::model()->findAll($criteria);
                foreach ($childList as $child) {
                    $class = IvyClass::model()->findByPk($child->classid);
                    $yid = $class->yid;
                    $criteria1 = new CDbCriteria;
                    $criteria1->compare('yid', $yid);
                    $criteria1->compare('branchid', $branchid);
                    $calendar = CalendarSchool::model()->find($criteria1);
                    $year = $calendar->startyear;

                    if ($year < $startYear) {
                        $child->status = ChildProfileBasic::STATS_GRADUATED;
                        if(!$child->save()){
                            echo 'childid:' . $child->childid . ',error:' . current(current($child->getErrors()));
                            Yii::app()->end();
                        }
                        echo 'childid:' . $child->childid . ',success' . "\r\n";
                    }

                }
            }
        }
    }

    // 启明星用友数据修复（班级ID）
    public function actionFixUfidaDataClass($exec=0)
    {
        Yii::import('common.models.invoice.*');

        $schoolid = 'BJ_DS';
        // $timestmp = '1478225089';   //20161031
        $month = '2016-10';
        $mkClass = array(815,813,666,814,696,676,698,675,674,699,673,672,695,683,670,669,668,667,682,681,680,679,678,694);
        $replace = array(
            220320=>220304,
            220321=>220305,
            220322=>220306,
            224119=>224104,
            600120=>600101,
            600121=>22411301,
            600102=>22411301,
            600122=>22411401,
            600103=>22411401,
        );

        //income 表
        echo "************ update ufida income ************ \r\n";
        $criteria = new CDbCriteria;
        $criteria->compare('schoolid', $schoolid);
        // $criteria->compare('timestmp', $timestmp);
        $criteria->compare('status', array(0, 50));

        $count = UfidaIncome::model()->count($criteria);

        $cycle = ceil($count / $this->batchNum);
        $num = 0;
        for ($i = 0; $i < $cycle ; $i++) {
            $criteria->limit = $this->batchNum;
            $criteria->offset = $i*$this->batchNum;
            $incomeData = UfidaIncome::model()->findAll($criteria);
            foreach ($incomeData as $income) {
                $childid = $income->childid;
                $child = ChildProfileBasic::model()->findByPk($childid);
                // 处理转班的孩子
                // if ($income->classid != $child->classid) {
                //     $income->classid = $child->classid;
                //     echo $income->id;
                //     if (!$exec) {
                //         echo " \r\n";
                //         continue;
                //     }
                //     if ($income->save()) {
                //         echo " success! \r\n";
                //     }else{
                //         echo " fail:" . current(current($income->getErrors())) . "\r\n";
                //     }
                // }
                // 处理mk的孩子
                if (in_array($income->classid, $mkClass)) {
                    $needChange = false;
                    if (in_array($income->borrow_accounts_code, array_keys($replace))) {
                        $income->borrow_accounts_code = $replace[$income->borrow_accounts_code];
                        $needChange = true;
                    }
                    if (in_array($income->loan_accounts_code, array_keys($replace))) {
                        $needChange = true;
                        $income->loan_accounts_code = $replace[$income->loan_accounts_code];
                    }
                    if ($needChange && $exec) {
                        if ($income->save()) {
                            $num++;
                            echo $income->id . " success! \r\n";
                        }else{
                            echo $income->id . " fail:" . current(current($income->getErrors())) . "\r\n";
                            Yii::app()->end();
                        }
                    } else{
                        echo $income->id . "\r\n";
                    }
                }
            }
        }
        echo "************ update ufida income $num ************ \r\n";
        $num = 0;
        //share 表
        echo "************ update share ufida ************ \r\n";
        $criteria = new CDbCriteria;
        $criteria->compare('schoolid', $schoolid);
        $criteria->compare('month', '>' . $month);
        $criteria->compare('status', array(0, 1, 50));

        $count = IvyInvoiceShareUfida::model()->count($criteria);
        $cycle = ceil($count / $this->batchNum);
        for ($i = 0; $i < $cycle ; $i++) {
            $criteria->limit = $this->batchNum;
            $criteria->offset = $i*$this->batchNum;
            $shareData = IvyInvoiceShareUfida::model()->findAll($criteria);
            foreach ($shareData as $share) {
                $childid = $share->childid;
                $child = ChildProfileBasic::model()->findByPk($childid);
                // 处理转班孩子
                // if ($share->classid != $child->classid) {
                //     $share->classid = $child->classid;
                //     echo $share->id;
                //     if (!$exec) {
                //         echo " \r\n";
                //         continue;
                //     }
                //     if ($share->save()) {
                //         echo " success! \r\n";
                //     }else{
                //         echo " fail:" . current(current($share->getErrors())) . "\r\n";
                //     }
                // }
                // 处理mk孩子的科目代码
                if (in_array($share->classid, $mkClass)) {
                    $needChange = false;
                    if (in_array($share->borrow_accounts_code, array_keys($replace))) {
                        $needChange = true;
                        $share->borrow_accounts_code = $replace[$share->borrow_accounts_code];
                    }
                    if (in_array($share->loan_accounts_code, array_keys($replace))) {
                        $needChange = true;
                        $share->loan_accounts_code = $replace[$share->loan_accounts_code];
                    }
                    if ($needChange) {
                        if ($exec) {
                            if ($share->save()) {
                                $num++;
                                echo $share->id . " success! \r\n";
                            }else{
                               echo $share->id . " fail:" . current(current($share->getErrors())) . "\r\n";
                               Yii::app()->end();
                            }
                        } else {
                            echo $share->id . "\r\n";
                        }
                    }
                }
            }
        }
        echo "************ update ufida income $num ************ \r\n";
    }

    /**
     * 显示月份未打折和打折后的金额
     * @param string $month
     * @param string $schoolid
     * @throws CException
     */
    public function actionDiscountData($month='', $schoolid='', $classType='')
    {
        Yii::import('common.models.invoice.*');

        if($schoolid && $month){
            $total = 0;
            $timestamp = strtotime($month);
            $criteira = new CDbCriteria();
            $criteira->compare('`inout`', 'in');
            if($schoolid == "BJ_DS" && $classType){

                $crit = new CDbCriteria();
                switch ($classType){
                    case 'mc':
                        $crit->compare('classtype', array('mt', 'mc'));
                        break;
                    case 'dse':
                        $crit->compare('classtype', array('mk', 'e1', 'e2', 'e3', 'e4', 'e5'));
                        break;
                    case 'dsm':
                        $crit->compare('classtype', array('e6', 'e7'));
                        break;
                }

//                $crit->compare('classtype', array('mt', 'mc', 'mk', 'e1', 'e2', 'e3', 'e4', 'e5', 'e6', 'e7'));
                $classes = IvyClass::model()->findAll($crit);
                foreach($classes as $citem){
                    $classid[$citem->classid] = $citem->classid;
                }

                $criteira->compare('classid', $classid);
//                $criteira->compare('schoolid', $schoolid);
            }
            else{
                $criteira->compare('schoolid', $schoolid);
            }
            $criteira->compare('payment_type', 'tuition');
            $criteira->compare('status', array(10,20,30));
            $criteira->compare('startdate', '<='.$timestamp);
            $criteira->compare('enddate', '>'.$timestamp);
            $criteira->compare('discount_id', '<>0');
            $items = Invoice::model()->findAll($criteira);

            foreach($items as $item){
                list($startdate['y'], $startdate['m']) = array(date('Y', $item->startdate), date('m', $item->startdate));
                list($enddate['y'], $enddate['m']) = array(date('Y', $item->enddate), date('m', $item->enddate));

                $diffmonth = abs( ($startdate['y']-$enddate['y'])*12 + abs($enddate['m']-$startdate['m']) ) + 1;

                if($schoolid == 'BJ_DS'){
                    if($item->fee_type == 1){
                        $diffmonth = 12;
                    }
                    elseif(in_array($item->fee_type, array(2,3))){
                        $diffmonth = 6;
                    }
                }

                $total += (($item->nodiscount_amount-$item->original_amount)/$diffmonth);
            }
            echo count($items)."\n";

            echo number_format($total, 2)."\n";
        }
    }

    /**
     * @param int $fromId
     * @param int $toId
     * @throws CException
     */
    public function actionCopyDSReport($fromId=0, $toId=0)
    {
        if($fromId && $toId){
            Yii::import('common.models.reportCards.*');

            $items = ReportsCategory::model()->findAllByAttributes(array('template_id'=>$fromId, 'parent_id'=>0));
            foreach($items as $item){
                $model = new ReportsCategory();
                $model->setAttributes($item->attributes);
                $model->template_id = $toId;
                $model->update_user = 1;
                $model->updated = time();
                if(!$model->save()){
                    print_r($model->getErrors());
                    die;
                }

                $subItems = ReportsCategory::model()->findAllByAttributes(array('template_id'=>$fromId, 'parent_id'=>$item->id));
                foreach($subItems as $subItem){
                    $subModel = new ReportsCategory();
                    $subModel->setAttributes($subItem->attributes);
                    $subModel->template_id = $toId;
                    $subModel->parent_id = $model->id;
                    $subModel->update_user = 1;
                    $subModel->updated = time();
                    if(!$subModel->save()){
                        print_r($subModel->getErrors());
                        die;
                    }
                    echo "--\n";

                    $itemItems = ReportsItem::model()->findAllByAttributes(array('template_id'=>$fromId, 'category_id'=>$subItem->id));
                    foreach($itemItems as $itemItem){
                        $itemModel = new ReportsItem();
                        $itemModel->setAttributes($itemItem->attributes);
                        $itemModel->template_id = $toId;
                        $itemModel->category_id = $subModel->id;
                        $itemModel->update_user = 1;
                        $itemModel->updated = time();
                        if(!$itemModel->save()){
                            print_r($itemModel->getErrors());
                            die;
                        }
                        echo "-\n";
                    }
                }

                echo "---\n";
            }
        }
    }

    // 启明星导出在读孩子列表
    public function actionExportChildInfo($schoolid = 'BJ_DS')
    {
        Yii::import('common.models.invoice.*');

        $countryList = Country::model()->getCountryList();
        $crit = new CDbCriteria;
        $crit->compare('t.schoolid', $schoolid);
//        $crit->compare('ivyclass.stat', '10');
//        $crit->compare('t.status', ChildProfileBasic::STATS_ACTIVE);
        $basicInfo = ChildProfileBasic::model()->with('ivyclass')->findAll($crit);
        $csv = $this->_t('ID').",";
        $csv .= $this->_t('中文姓名').",";
        $csv .= $this->_t('英文姓名').",";
        $csv .= $this->_t('性别').",";
        $csv .= $this->_t('生日').",";
        $csv .= $this->_t('国家').",";
        $csv .= $this->_t('班级').",";
//        $csv .= $this->_t('父亲姓名').",";
//        $csv .= $this->_t('父亲邮箱').",";
//        $csv .= $this->_t('父亲电话').",";
//        $csv .= $this->_t('母亲姓名').",";
//        $csv .= $this->_t('母亲邮箱').",";
//        $csv .= $this->_t('母亲电话').",";
        $csv .= $this->_t('启明星就读开始').",";
        $csv .= $this->_t('启明星就读结束').",";
        $csv .= $this->_t('艾毅就读历史')."\r\n";
        foreach ($basicInfo as $child) {
            $gender = $child->gender == 1 ? '男' : '女';
            $csv .= $this->_t1(trim($child->childid)).",";
            $csv .= $this->_t1(trim($child->name_cn)).",";
            $csv .= $this->_t1(trim($child->last_name_en).' '.trim($child->first_name_en)).",";
            $csv .= $this->_t1($gender).",";
            $csv .= $child->birthday_search.",";
            $csv .= $this->_t1($countryList[$child->country]).",";
            $csv .= ($child->ivyclass ? $this->_t1($child->ivyclass->title) : '') .",";
//            //父亲信息
//            $fInfo = IvyParent::model()->with('ivyUser')->findByPk($child->fid);
//            //母亲信息
//            $mInfo = IvyParent::model()->with('ivyUser')->findByPk($child->mid);
//            if (isset($fInfo) && $child->fid != 0){
//                $csv .= $this->_t1($fInfo->getName()).",";
//                $csv .= " ".$fInfo->ivyUser->email.",";
//                $csv .= $this->_t1($fInfo->mphone).",";
//            }else{
//                $csv .= ",";
//                $csv .= ",";
//                $csv .= ",";
//            }
//            if (isset($mInfo) && $child->mid != 0){
//                $csv .= $this->_t1($mInfo->getName()).",";
//                $csv .= " ".$mInfo->ivyUser->email.",";
//                $csv .= $this->_t1($mInfo->mphone).",";
//            }else{
//                $csv .= ",";
//                $csv .= ",";
//                $csv .= ",";
//            }
            // 查找serviceInfo
            $crit = new CDbCriteria;
            $crit->compare('childid', $child->childid);
            $crit->compare('payment_type', 'tuition');
            $serviceInfo = ChildServiceInfo::model()->findAll($crit);
            $learnArray = array();
            foreach ($serviceInfo as $v) {
                if (!isset($learnArray[$v->schoolid]['mindate']) || $v->startdate < $learnArray[$v->schoolid]['mindate']) {
                    $learnArray[$v->schoolid]['mindate'] = $v->startdate;
                }
                if (!isset($learnArray[$v->schoolid]['maxdate']) || $v->enddate > $learnArray[$v->schoolid]['maxdate']) {
                    $learnArray[$v->schoolid]['maxdate'] = $v->enddate;
                }
            }
            $ds = ',,';
            $ivy = ',';

            $minAtDs = '1893427200';
            $maxAtDs = '1009814400';
            foreach ($learnArray as $k => $v) {
                if (in_array($k, array('BJ_DS', 'BJ_SLT'))) {
                    $minAtDs = ($v['mindate'] < $minAtDs) ? $v['mindate'] : $minAtDs;
                    $maxAtDs = ($v['maxdate'] > $maxAtDs) ? $v['maxdate'] : $maxAtDs;
                    $ds = date('Y/m/d', $minAtDs) .','. date('Y/m/d', $maxAtDs) . ',';
                } else{
                    $ivy .= $k .':'. date('Y/m/d', $v['mindate']) .'-'. date('Y/m/d', $v['maxdate']) . ';';
                }
            }
            $csv .= $ds . $ivy ."\r\n";
            echo ".";
        }
        file_put_contents(time().'.csv', $csv);
    }

    // 更新已有openid用户的unionid
    public function actionUpdateUnionid($flag = false)
    {
        Yii::import('common.models.wechat.WechatUser');

        $ivyToken = $this->getAccessToken('ivy');
        $dsToken = $this->getAccessToken('ds');
        $mmxToken = $this->getAccessToken('mmx');
        $baseUrl = 'https://api.weixin.qq.com/cgi-bin/user/info';

        $criteria = new CDbCriteria;
        if ($flag) {
            $criteria->addCondition('unionid IS NULL');
        }
        $count = WechatUser::model()->count($criteria);

        $cycle = ceil($count / $this->batchNum);
        for ($i = 0; $i < $cycle; $i++) {
            $criteria = new CDbCriteria;
            if ($flag) {
                $criteria->addCondition('unionid IS NULL');
            }
            $criteria->limit = $this->batchNum;
            $criteria->offset = $i * $this->batchNum;
            $wechatUserObj = WechatUser::model()->findAll($criteria);
            foreach ($wechatUserObj as $wechatUser) {
                $accessToken = $ivyToken;
                $account = 'ivy';
                if (!$wechatUser->account) {
                    continue;
                }
                if ($wechatUser->account == 'ds') {
                    $account = 'ds';
                    $accessToken = $dsToken;
                } elseif ($wechatUser->account == 'mmx') {
                    $account = 'mmx';
                    $accessToken = $mmxToken;
                }
                if ($wechatUser->unionid) {
                    echo $wechatUser->campus .' '. $wechatUser->openid . " unionid isset.\r\n";
                    continue;
                }
                $url = $baseUrl . '?access_token=' . $accessToken .'&openid='. $wechatUser->openid;
                $res = json_decode(file_get_contents($url));
                if (!isset($res->unionid)) {
                    echo $wechatUser->campus .' '. $wechatUser->openid . " errcode." . $res->errcode .' '. $res->errmsg . "\r\n";
                    continue;
                }
                $wechatUser->unionid = $res->unionid;
                $wechatUser->account = $account;
                if($wechatUser->save()){
                    echo $wechatUser->campus .' '. $wechatUser->openid . " success.\r\n";
                    continue;
                }else{
                    echo $wechatUser->campus .' '. $wechatUser->openid . " save failed.\r\n";
                }
            }
        }
    }

    // 更新已有微信用户的个人信息
    public function actionWechatUserInfo()
    {
        Yii::import('common.models.wechat.WechatUser');
        Yii::import('common.models.wechat.WechatUsersInfo');

        $ivyToken = $this->getAccessToken('ivy');
        $dsToken = $this->getAccessToken('ds');
        $mmxToken = $this->getAccessToken('mmx');
        $baseUrl = 'https://api.weixin.qq.com/cgi-bin/user/info';

        $criteria = new CDbCriteria;
        $count = WechatUser::model()->count($criteria);

        $cycle = ceil($count / $this->batchNum);
        for ($i = 0; $i < $cycle; $i++) {
            $criteria = new CDbCriteria;
            $criteria->limit = $this->batchNum;
            $criteria->offset = $i * $this->batchNum;
            $wechatUserObj = WechatUser::model()->findAll($criteria);
            foreach ($wechatUserObj as $wechatUser) {
                $accessToken = $ivyToken;
                $account = 'ivy';
                if (!$wechatUser->account) {
                    continue;
                }
                if ($wechatUser->account == 'ds') {
                    $account = 'ds';
                    $accessToken = $dsToken;
                } elseif ($wechatUser->account == 'mmx') {
                    $account = 'mmx';
                    $accessToken = $mmxToken;
                }
                $url = $baseUrl . '?access_token=' . $accessToken .'&openid='. $wechatUser->openid;
                $responseData = file_get_contents($url);
                $res = json_decode($responseData);
                if (isset($res->errcode) && $res->errcode == 42001) {
                    // 判断是否为 accessToken 过期
                   $ivyToken = $this->getAccessToken('ivy');
                   $dsToken = $this->getAccessToken('ds');
                   $mmxToken = $this->getAccessToken('mmx');

                   $accessToken = $ivyToken;
                   $account = 'ivy';
                   if (!$wechatUser->account) {
                       continue;
                   }
                   if ($wechatUser->account == 'ds') {
                       $account = 'ds';
                       $accessToken = $dsToken;
                   } elseif ($wechatUser->account == 'mmx') {
                       $account = 'mmx';
                       $accessToken = $mmxToken;
                   }
                   $url = $baseUrl . '?access_token=' . $accessToken .'&openid='. $wechatUser->openid;
                   $responseData = file_get_contents($url);
                   $res = json_decode($responseData);
                }
                if (isset($res->errcode) && $res->errcode != 42001) {
                    echo $wechatUser->campus .' '. $wechatUser->openid . " errcode." . $res->errcode .' '. $res->errmsg . "\r\n";
                    continue;
                }
                if (isset($res->subscribe) && $res->subscribe == 0) {
                    echo $wechatUser->campus .' '. $wechatUser->openid . " 已退订" . "\r\n";
                    continue;
                }
                if (!isset($res->openid)) {
                    echo $wechatUser->campus .' '. $wechatUser->openid . " 返回数据错误" . "\r\n";
                    continue;
                }
                $wechatUser->unionid = $res->unionid;
                $wechatUser->account = $account;
                if($wechatUser->save()){
                    $infoModel = WechatUsersInfo::model()->findByPk($res->openid);
                    if (!$infoModel) {
                        $infoModel = new WechatUsersInfo();
                    }
                    $infoModel->openid = $res->openid;
                    $infoModel->info = json_encode($res);
                    $infoModel->updated = time();
                    $infoModel->save();
                    echo $wechatUser->campus .' '. $wechatUser->openid . " success.\r\n";
                    continue;
                }else{
                    echo $wechatUser->campus .' '. $wechatUser->openid . " save failed.\r\n";
                }
            }
        }
    }

    public function _t($str='')
    {
        $str = "\"".$str."\"";
        $str = str_replace(',', '，', $str);
        return iconv("UTF-8", "GBK", $str);
    }

    public function _t1($str)
    {
        $str = str_replace(',', '，', $str);
        return mb_convert_encoding($str, 'GBK', 'UTF-8');
    }

    // 获取微信 token
    private function getAccessToken($wechatKey)
    {
        $token = md5(CommonUtils::SECURITY_KEY);
        $data = array(
            "token" => $token,
            "wechatKey" => $wechatKey,
            "authKey" => md5(sprintf("%s%s", $wechatKey, CommonUtils::SECURITY_KEY))
        );
        $url = "http://apps.ivyonline.cn/webapi/wechat/getAccessToken";
        $res = CJSON::decode($this->httpGet($url, $data));

        return $res['accessToken'];
    }

    // 发起 curl 请求
    private function httpGet($url, $data = '') {
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_TIMEOUT, 500);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
        if ($data) {
            curl_setopt($curl, CURLOPT_POST, 1);
            curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
        }
        curl_setopt($curl, CURLOPT_URL, $url);

        $res = curl_exec($curl);
        curl_close($curl);

        return $res;
    }

    public function actionWxAT($key = 'ivy')
    {
        echo $this->getAccessToken($key);
    }

    // 修正 asa_invoice_item 的actual_total 字段
    public function actionAsaInvoice()
    {
        Yii::import('common.models.asainvoice.*');

        $discountlinkModel = AsaInvoiceDiscountLink::model()->findAll();
        foreach ($discountlinkModel as $discountlink) {
            $invoiceModel = AsaInvoice::model()->findByPk($discountlink->invoice_id);
            if ($invoiceModel) {
                $occupied = 0;
                $discount = $discountlink->amount_final / $discountlink->amount_pre_discount;
                foreach ($invoiceModel->item as $item) {
                    $total = $item->unit_price * $item->class_count;
                    $item->actual_total = round($total * $discount);
                    $a = $invoiceModel->item;
                    $last = end($a);
                    if ($item->id == $last->id) {
                        $item->actual_total = $discountlink->amount_final - $occupied;
                    }
                    $occupied += $item->actual_total;
                    if ($item->save()) {
                        echo $item->id . ":success\r\n";
                    } else {
                        $error = current($item->getErrors());
                        echo 'error:' . $error[0];
                    }
                }
            }
        }
    }

    // 导出在读孩子艾毅在线使用情况
    public function actionExportChildActive()
    {
        Yii::import('common.models.portfolio.*');
        Yii::import('common.models.wechat.*');

        $timestamp = time()-3600*24*90;

        $crite = new CDbCriteria();
        $crite->compare('schoolid', array('BJ_IASLT', 'BJ_OE', 'BJ_OG-PH', 'BJ_CP', 'TJ_EB', 'TJ_EC', 'TJ_ES', 'CD_LH', 'CD_LT', 'XA_LB', 'XA_GJ', 'NB_HH', 'NB_FJ', 'BJ_QFF'));
        $crite->index = 'classid';
        $classItems = IvyClass::model()->findAll($crite);

        $crit = new CDbCriteria;
        $crit->compare('status', array(ChildProfileBasic::STATS_ACTIVE, ChildProfileBasic::STATS_ACTIVE_WAITING));
        $crit->compare('schoolid', array('BJ_IASLT', 'BJ_OE', 'BJ_OG-PH', 'BJ_CP', 'TJ_EB', 'TJ_EC', 'TJ_ES', 'CD_LH', 'CD_LT', 'XA_LB', 'XA_GJ', 'NB_HH', 'NB_FJ', 'BJ_QFF'));
        $total = ChildProfileBasic::model()->count($crit);
        if (!$total)
            return false;

        $csv = $this->_t('ID').",";
        $csv .= $this->_t('学校').",";
        $csv .= $this->_t('中文姓名').",";
        $csv .= $this->_t('英文姓名').",";
        $csv .= $this->_t('班级').",";
//        $csv .= $this->_t('使用过系统').",";
        $csv .= $this->_t('父亲最近使用').",";
        $csv .= $this->_t('母亲最近使用').",";
//        $csv .= $this->_t('绑定过微信')."," . "\r\n";
        // $csv .= $this->_t('存在周报告')."," . "\r\n";

        $cycle = ceil($total/$this->batchNum);
        for($i = 0; $i < $cycle; $i++){
            $crit->limit = $this->batchNum;
            $crit->offset = $i*$this->batchNum;
            $crit->order = 'schoolid';
            $basicInfo = ChildProfileBasic::model()->findAll($crit);
            foreach ($basicInfo as $child) {
                $csv .= $this->_t(trim($child->childid)).",";
                $csv .= $this->_t(trim($child->schoolid)).",";
                $csv .= $this->_t(trim($child->name_cn)).",";
                $csv .= $this->_t(trim($child->last_name_en).' '.trim($child->first_name_en)).",";

                if ($child->classid && isset($classItems[$child->classid])) {
                    $csv .= $classItems[$child->classid]->title.",";
                }
                else {
                    $csv .= ",";
                }

                //父亲信息
                $fInfo = User::model()->findByPk($child->fid);
                if ($fInfo && $fInfo->last_login > 0) {
                    $csv .= date('Y-m-d H:i', $fInfo->last_login).",";
                } else {
                    $csv .= ",";
                }
                //母亲信息
                $mInfo = User::model()->findByPk($child->mid);
                if ($mInfo && $mInfo->last_login > 0) {
                    $csv .= date('Y-m-d H:i', $mInfo->last_login).",";
                } else {
                    $csv .= ",";
                }
//                if (($fInfo->last_login > 0)||($mInfo->last_login > 0)) {
//                    $csv .= 1 .",";
//                } else {
//                    $csv .= 0 .",";
//                }
//                if ($fInfo->last_login>$timestamp||$mInfo->last_login>$timestamp) {
//                    $csv .= 1 .",";
//                } else {
//                    $csv .= 0 .",";
//                }


//                if (WechatUser::model()->countByAttributes(array('userid'=>array($child->fid, $child->mid)))) {
//                    $csv .= 1 .",";
//                } else {
//                    $csv .= 0 .",";
//                }

                // if (NotesChild::model()->countByAttributes(array('childid'=>$child->childid))) {
                //     $csv .= 1 .",";
                // } else {
                //     $csv .= 0 .",";
                // }

                $csv .= "\r\n";
                echo '.';
            }
        }


        file_put_contents(time().'.csv', $csv);
    }

    // 导出十月董事会需要数据
    public function actionExportChildData()
    {

        Yii::import('common.models.invoice.*');
        Yii::import('common.models.calendar.*');

        $branchList = Branch::model()->getBranchList();
        // $branchList = array('BJ_XHL'=>'LJ');

        $crit = new CDbCriteria;
        $crit->compare('schoolid', array_keys($branchList));
        $crit->order = 'schoolid';
        $total = ChildProfileBasic::model()->count($crit);
        if (!$total)
            return false;

        $csv = $this->_t('学校').",";
        $csv .= $this->_t('ID').",";
        $csv .= $this->_t('出生年月').",";
        $csv .= $this->_t('2015-2016学费账单').",";
        $csv .= $this->_t('2016-2017学费账单').",";
        $csv .= $this->_t('2017-2018学费账单').",";
        $csv .= $this->_t('2015-2016退费').",";
        $csv .= $this->_t('2016-2017退费').",";
        $csv .= $this->_t('2017-2018退费').",";
        $csv .= $this->_t('入学学年').",";
        $csv .= $this->_t('退学学年').",";
        $csv .= $this->_t('毕业学年')."," . "\r\n";

        $cycle = ceil($total/$this->batchNum);
        $j = 1;
        for($i = 0; $i < $cycle; $i++){
            $crit->limit = $this->batchNum;
            $crit->offset = $i*$this->batchNum;
            $basicInfo = ChildProfileBasic::model()->findAll($crit);
            foreach ($basicInfo as $child) {
                // 是否存在2015-2016学费账单
                $criteria = new CDbCriteria();
                $criteria->with = 'calendar';
                $criteria->compare('t.childid', $child->childid);
                $criteria->compare('t.payment_type', 'tuition');
                $criteria->compare('t.`inout`', 'in');
                $criteria->compare('t.status', array(Invoice::STATS_PAID, Invoice::STATS_UNPAID, Invoice::STATS_PARTIALLY_PAID));
                $criteria->compare('calendar.startyear', '2015');
                $res1 = Invoice::model()->exists($criteria)? 1 : '' ;
                // 是否存在2016-2017学费账单
                $criteria = new CDbCriteria();
                $criteria->with = 'calendar';
                $criteria->compare('t.childid', $child->childid);
                $criteria->compare('t.payment_type', 'tuition');
                $criteria->compare('t.`inout`', 'in');
                $criteria->compare('t.status', array(Invoice::STATS_PAID, Invoice::STATS_UNPAID, Invoice::STATS_PARTIALLY_PAID));
                $criteria->compare('calendar.startyear', '2016');
                $res2 = Invoice::model()->exists($criteria)? 1 : '' ;
                // 是否存在2017-2018学费账单
                $criteria = new CDbCriteria();
                $criteria->with = 'calendar';
                $criteria->compare('t.childid', $child->childid);
                $criteria->compare('t.payment_type', 'tuition');
                $criteria->compare('t.`inout`', 'in');
                $criteria->compare('t.status', array(Invoice::STATS_PAID, Invoice::STATS_UNPAID, Invoice::STATS_PARTIALLY_PAID));
                $criteria->compare('calendar.startyear', '2017');
                $res3 = Invoice::model()->exists($criteria)? 1 : '' ;
                // 是否存在2015-2016退费
                $criteria = new CDbCriteria();
                $criteria->with = 'calendar';
                $criteria->compare('t.childid', $child->childid);
                $criteria->compare('t.payment_type', 'tuition');
                $criteria->compare('t.`inout`', 'out');
                $criteria->compare('t.status', Invoice::STATS_PAID);
                $criteria->compare('calendar.startyear', '2015');
                $res4 = Invoice::model()->exists($criteria)? 1 : '' ;
                // 是否存在2016-2017退费
                $criteria = new CDbCriteria();
                $criteria->with = 'calendar';
                $criteria->compare('t.childid', $child->childid);
                $criteria->compare('t.payment_type', 'tuition');
                $criteria->compare('t.`inout`', 'out');
                $criteria->compare('t.status', Invoice::STATS_PAID);
                $criteria->compare('calendar.startyear', '2016');
                $res5 = Invoice::model()->exists($criteria)? 1 : '' ;
                // 是否存在2017-2018退费
                $criteria = new CDbCriteria();
                $criteria->with = 'calendar';
                $criteria->compare('t.childid', $child->childid);
                $criteria->compare('t.payment_type', 'tuition');
                $criteria->compare('t.`inout`', 'out');
                $criteria->compare('t.status', Invoice::STATS_PAID);
                $criteria->compare('calendar.startyear', '2017');
                $res6 = Invoice::model()->exists($criteria)? 1 : '' ;
                if (!$res1&&!$res2&&!$res3) {
                    continue;
                }
                $csv .= $this->_t($branchList[$child->schoolid]).",";
                $csv .= $this->_t($child->childid).",";
                $csv .= $this->_t($child->birthday_search).",";
                $csv .= $res1 . ',' . $res2 . ',' . $res3 . ',' . $res4 . ',' . $res5 . ',' . $res6 . ',' ;
                // 计算入学学年
                $criteria = new CDbCriteria();
                $criteria->compare('childid', $child->childid);
                $criteria->compare('payment_type', 'tuition');
                $criteria->compare('`inout`', 'in');
                $criteria->compare('status', array(Invoice::STATS_PAID, Invoice::STATS_UNPAID, Invoice::STATS_PARTIALLY_PAID));
                $criteria->order = 'startdate';
                $firstInvoice = Invoice::model()->find($criteria);
                $firstYear = $firstInvoice->calendar->startyear;
                $csv .= $firstYear .'-'. ($firstYear+1) . ',';
                // 计算毕业学年
                $criteria = new CDbCriteria();
                $criteria->compare('childid', $child->childid);
                $criteria->compare('payment_type', 'tuition');
                $criteria->compare('`inout`', 'in');
                $criteria->compare('status', array(Invoice::STATS_PAID, Invoice::STATS_UNPAID, Invoice::STATS_PARTIALLY_PAID));
                $criteria->order = 'enddate DESC';
                $latestInvoice = Invoice::model()->find($criteria);
                $latestYear = $latestInvoice->calendar->startyear;
                $latestClass = $latestInvoice->classid ? $latestInvoice->classInfo : 0;

                if ($child->status == ChildProfileBasic::STATS_DROPOUT) {
                    $csv .= $latestYear .'-'. ($latestYear+1) . ',';
                } elseif($child->status == ChildProfileBasic::STATS_GRADUATED) {
                    $csv .= ',';
                    $csv .= $latestYear .'-'. ($latestYear+1) . ',';
                } else {
                    // 判断学生最后一条学费账单是否大于2017年
                    if ($latestYear < '2017' ) {
                        if ($latestClass->classtype == 'k') {
                            $csv .= ',';
                            $csv .= $latestYear .'-'. ($latestYear+1) . ',';
                        }
                    }
                }

                echo $j .'  '. $child->schoolid . '-' . $child->childid . "\r\n";
                $j++;
                $csv .= "\r\n";
            }
        }


        file_put_contents('childdata.csv', $csv);
    }

    // 自动作废24小时以外家长开的未付款校服账单
    public function actionCancelUniformInvoices()
    {
        Yii::import('common.models.invoice.Invoice');
        $timestamp = time() - 86400;
        $schoolids = array('BJ_DS', 'BJ_SLT');
        $criteria = new CDbCriteria();
        $criteria->compare('payment_type', 'uniform');
        $criteria->compare('status', Invoice::STATS_UNPAID);
        $criteria->compare("`inout`", Invoice::INVOICE_INOUT_IN);
        $criteria->compare('schoolid', $schoolids);
        $criteria->compare('timestamp', '<' . $timestamp);
        $count = Invoice::model()->count($criteria);
        $cycle = ceil($this->batchNum / $count);
        for ($i=0; $i < $cycle; $i++) {
            $criteria->limit = $this->batchNum;
            $criteria->offset = $i * $this->batchNum;
            $invoices = Invoice::model()->findAll($criteria);
            foreach ($invoices as $invoice) {
                // 排除行政开的账单
                $user = User::model()->findByPk($invoice->userid);
                if ($user && $user->isstaff) {
                    // echo $invoice->invoice_id . "\r\n";
                    continue;
                }
                $invoice->status = Invoice::STATS_CANCELLED;
                if (!$invoice->save()) {
                    $error = current($invoice->getErrors());
                    echo $error[0] . "\r\n";
                }
                echo $invoice->invoice_id . ' success' . "\r\n";
                Yii::log($invoice->invoice_id.': 1', CLogger::LEVEL_INFO, 'invoice.cancel');
            }
        }
    }

    public function actionReportData()
    {
        Yii::import('common.models.asainvoice.*');
        $crit = new CDbCriteria();
        $crit->compare('status', array(AsaMonthReport::SUBMIT, AsaMonthReport::CONFIRM));
        $reports = AsaMonthReport::model()->findAll($crit);
        foreach ($reports as $report) {
            echo $report->schoolid .': '. $report->report_month . "\r\n";
            $items = $report->item;
            $crit = new CDbCriteria();
            $crit->compare('report_id', $report->id);
            $crit->index = 'vendor_id';
            $variations = AsaMonthReportVariation::model()->findAll($crit);
            $vendorIds = array();
            foreach ($items as $item) {
                if (!in_array($item->vendor_id, $vendorIds) && !isset($variations[$item->vendor_id])) {
                    $vendorIds[] = $item->vendor_id;
                    $model = new AsaMonthReportVariation();
                    $model->report_id = $item->report_id;
                    $model->vendor_id = $item->vendor_id;
                    $model->variation_amout = 0;
                    $model->update_time = time();
                    $model->uid = 1;
                    $model->expense_id = 0;
                    $model->settle_report_id = 0;
                    $model->settlement = 1;
                    if (!$model->save()) {
                        var_dump($model->getErrors());die();
                    }
                }
            }
        }
    }

    // 修改小学报告报告数据
    public function actionReportUpdateData($childid = '')
    {
        Yii::import('common.models.reportCards.*');
        $chilidArr = array(
            11988, 11747, 13482, 11670, 11615, 13224, 8693, 13779, 12385, 13483,
            8490, 13299, 13611, 13248, 13221, 13219, 13335, 12128, 13566, 13609,
            13674, 13682, 13229, 8234, 6323, 12155, 12979, 9757, 13937, 6644,
            11838, 13227, 8624, 11462, 13800, 11475, 6248, 12362, 11858
        );
        $childData = ($childid) ? $childid : $chilidArr;

        $classArr = array(1059,1060,1082);
        $criteria = new CDbCriteria();
        $criteria->compare('class_id', $classArr);
        $criteria->compare('child_id', $childData);
        $criteria->compare('semester', 1);
        $model = ReportsData::model()->with('ext')->findAll($criteria);
        $num = 1;
        if($model){
            foreach ($model as $val){
                if($val->ext){
                    foreach ($val->ext as $item){
                        $item->data_id = '-' . $item->data_id;
                        $item->save();
                        echo $num . "\r\n";
                    }
                }
            }
        }
    }

    public function actionDeleteRepeatHoliday()
    {
        Yii::import('common.models.calendar.*');
        $criteria = new CDbCriteria();
        $criteria->compare('is_selected', 1);
        $schoolYid = CalendarSchool::model()->findAll($criteria);

        if($schoolYid) {
            foreach ($schoolYid as $yid) {
                $criteria = new CDbCriteria();
                $criteria->compare('yid', $yid->yid);
                $criteria->compare('event', 10);
                $criteria->compare('schoolid', 0);
                $criteria->order = 'date_timestamp ASC';
                $model = CalendarDay::model()->findAll($criteria);

                $array = array();
                if($model) {
                    foreach ($model as $val) {
                        $array[$val->date][$val->did] = $val->date;
                    }
                }

                $data = array();
                if ($array) {
                    foreach ($array as $val) {
                        if (count($val) > 1) {
                            foreach ($val as $key => $item) {
                                $data[$item] = $key;
                            }
                        }
                    }
                }

                if ($data) {
                    $criteria = new CDbCriteria();
                    $criteria->compare('did', $data);
                    CalendarDay::model()->deleteAll($criteria);
                    echo $yid->branchid . ' - ' . count($data);
                }
            }
        }
    }

    /*
     *  根据ivy_child_credit 关联查询 ivy_refund_lunch ->child_credit_id; 未有关联数据则是问题数据
     *
     *
     *
     */
    public function actionRepairLunch()
    {
        Yii::import('common.models.invoice.*');
        Yii::import('common.models.calendar.*');
        $criteria = new CDbCriteria();
        $criteria->compare('startyear', "2018");
        $criteria->compare('yid', "<>99");
        $calendarModel = Calendar::model()->findAll($criteria);
        $childArr = array();
        $i = 0;
        foreach ($calendarModel as $calendar){
            $criteria = new CDbCriteria();
            $criteria->compare('yid', $calendar->yid);
            $criteria->compare('itemname', 'lunch');
            $criteria->compare('`inout`', 'in');
            $criteria->compare('updated_timestamp', '>1548950400');
            $Obj = ChildCredit::model()->findAll($criteria);

            foreach ($Obj as $item){
                $criteria = new CDbCriteria();
                $criteria->compare('child_credit_id', $item->cid);
                $refundcount = RefundLunch::model()->count($criteria);
                if(!$refundcount){
                    echo '-----------------' . $item->transaction_id . "\n";
                    $childArr[$item->transaction_id] = $item->transaction_id;
                }
                echo $i++ . ' - ' .$item->cid . "\n";
            }
        }
        print_r($childArr);
        die;
        $tranModel = InvoiceTransaction::model()->findAllByPk($childArr);
        foreach ($tranModel as $item){
            Invoice::model()->deleteByPk($item->invoice_id);
            ChildCredit::model()->deleteAllByAttributes(array('transaction_id'=>$item->id));
            $item->delete();
        }
    }

    public function actionFixRefundLunch()
    {
        Yii::import('common.models.invoice.*');

        $data = array();

        $sql = "SELECT count(*) as count FROM `ivy_invoice_transaction` WHERE `inout` = 'out' and `timestampe`>=1548950400 and `payment_type`='lunch' and `memo` like '%天的餐费'";
        $count = Yii::app()->db->createCommand($sql)->queryRow();
        $total = $count['count'];

        $sql = str_replace('count(*) as count', '*', $sql);
        $cycle = ceil($total/$this->batchNum);
        for($i=0; $i<$cycle; $i++){
            $sql1 = $sql . " limit ".$i*$this->batchNum.", ".$this->batchNum;
            $rows = Yii::app()->db->createCommand($sql1)->queryAll();
            foreach($rows as $row){
                echo $row['id']."\n";

                $criteria = new CDbCriteria();
                $criteria->compare('transaction_id', $row['id']);
                $items = ChildCredit::model()->findAll($criteria);
                foreach($items as $item) {
                    echo $item->cid."\n";
                    $criteria = new CDbCriteria();
                    $criteria->compare('child_credit_id', $item->cid);
                    $refundcount = RefundLunch::model()->count($criteria);
                    if(!$refundcount){
                        $data[$row['id']] = $row['id'];
                    }
                }
                echo "---------\n";
            }
        }
        print_r($data);
    }

    public function actionFixRefundLunch1($filename='')
    {
        Yii::import('common.models.invoice.*');

        $data = file_get_contents($filename);
        $data = explode(',', $data);
        $data = array_unique($data);
        $i=0;
        foreach($data as $tid) {
            if($tid){
                $tranModel = InvoiceTransaction::model()->findByPk($tid);
                if($tranModel && !in_array($tranModel->schoolid, array('CD_LT', 'BJ_DS', 'BJ_SLT'))){
                    Invoice::model()->deleteByPk($tranModel->invoice_id);
                    ChildCredit::model()->deleteAllByAttributes(array('transaction_id'=>$tid));
                    $tranModel->delete();
                    echo $i++.': '.$tid."\n";
                }
            }
        }
    }


    public function actionChildBillDetail()
    {
        Yii::import('common.models.invoice.*');
        $data = array();

        $sql = "SELECT count(*) as count FROM `ivy_child_profile_basic`";
        $count = Yii::app()->db->createCommand($sql)->queryRow();
        $total = $count['count'];
        $sql = str_replace('count(*) as count', '*', $sql);
        $cycle = ceil($total/$this->batchNum);

        for($i=0; $i<$cycle; $i++){
            $sql1 = $sql . " limit ".$i*$this->batchNum.", ".$this->batchNum;
            $childs = Yii::app()->db->createCommand($sql1)->queryAll();
            foreach($childs as $child){
                $model = new ChildCredit();
                $childCredit = $model->getChildCredit($child['childid']);
                if(abs($childCredit - $child['credit']) > 0.01){
                    $data[$child['childid']] = $child['status'];
                    echo $child['childid'] . "---------\n";
                }
            }
        }
        print_r($data);
    }

    // 修复注册第一步，customize字段
    public function actionRegStepBus()
    {
        Yii::import('common.models.regextrainfo.RegCarSite');
        Yii::import('common.models.regextrainfo.RegExtraInfo');

        $regs = array(1 => 'BJ_DS', 3 => 'BJ_SLT' );

        $carsites = array();

        $models = RegCarSite::getCarsite(1, 'BJ_DS');
        foreach ($models as $model) {
            $carsites[1][] = $model->name_cn . $model->park_address_cn;
        }
        $models = RegCarSite::getCarsite(3, 'BJ_SLT');
        foreach ($models as $model) {
            $carsites[3][] = $model->name_cn . $model->park_address_cn;
        }

        $crit = new CDbCriteria();
        $crit->compare('reg_id', array_keys($regs));
        $crit->compare('step', 1);
        $crit->compare('status', 1);
        $steps = RegExtraInfo::model()->findAll($crit);

        foreach ($steps as $step) {
            $data = json_decode($step->data, true);
            if (isset($data['needBus']) && $data['needBus'] == 1) {
                if (!isset($data['customize'])) {
                    $data['customize'] = 2;
                    if (in_array($data['parking'], $carsites[$step->reg_id])) {
                        $data['customize'] = 1;
                    }
                    $step->data = json_encode($data);
                    if ($step->save()) {
                        echo $step->id . " success \n";
                    }
                }
            }
        }
    }

    /*
     * 作废家长网站入学申请面试费账单
     * 大于3天
     *
     */
    public function actionInvalidInvoice()
    {
        Yii::import('common.models.invoice.*');
        $time = time() - 86400 * 3;

        $crit = new CDbCriteria();
        $crit->compare('schoolid', array('BJ_DS','BJ_SLT'));
        $crit->compare('admission_id', ">0");
        $crit->compare('status', Invoice::STATS_UNPAID);
        $crit->compare('payment_type', 'registration');
        $crit->compare('duetime', "<$time");
        $invoice = Invoice::model()->findAll($crit);

        if($invoice){
            $num = 1;
            foreach ($invoice as $val){
                $val->status = Invoice::STATS_CANCELLED;
                if(!$val->save()){
                    echo $num . ' - ' . $val->invoice_id . '失败' . "\n";
                }
                echo $num . ' - ' . $val->invoice_id . '成功' . "\n";
                $num++;
                Yii::log($val->invoice_id.': 1', CLogger::LEVEL_INFO, 'invoice.cancel');
            }
        }else{
            echo '暂无数据';
        }
    }

    public function actionEdcation()
    {
        $file = file('./data.csv');
        $i = 1;
        foreach ($file as $string) {
            echo $i . ': ';
            $data = explode(",", $string);
            $childid = isset($data[0]) ? $data[0] : '';
            $eid = isset($data[1]) ? intval($data[1]) : '';
            $barcode = isset($data[2]) ? $data[2] : '';
            $i++;
            if (!$childid) {
                echo 'child ID 为空' . "\n";
                continue;
            }
            if (!$eid) {
                echo $childid . ', eid 为空' . "\n";
                continue;
            }

            $child = ChildProfileBasic::model()->findByPk($childid);
            if (!$child) {
                echo $childid . ', model 未找到' . "\n";
                continue;
            }
            $child->educational_id = $eid;
            $child->barcode = $barcode;
            if (!$child->save()) {
                $error = current($child->getErrors());
                echo $childid . ', 保存失败。' . $error[0] . "\n";
                continue;
            }
            echo $childid . ', success' . "\n";
        }
    }

    /*
     *  总部服务报销数据 各校园的统计数据在月底自动发送给校园当月得数据，支持邮箱
     */

    public function actionServiceMonth($month = '', $schoolid = '')
    {
        if(!$month) {
            $month = date('Y-m-d', strtotime(date('Y-m-01') . " - 1 month"));
        }

        Yii::import('common.models.service.*');

        $strat_time = strtotime($month);
        $end_time = strtotime("$month +1 month -1 day");

        $criteria = new CDbCriteria;
        $criteria->compare('status', 2);
        $criteria->compare('service_id', 1);
        $criteria->compare('start_time', ">=$strat_time");
        $criteria->compare('start_time', "<=$end_time");
        if ($schoolid) $criteria->compare('school_id', $schoolid);
        $criteria->order = 'start_time ASC';
        $model = Service::model()->findAll($criteria);

        $data = array();
        if ($model) {
            $serviceInfo = Service::getServiceInfo();
            foreach ($model as $val) {
                $data[$val->school_id][$val->id]['service_id'] = $val->id;
                $data[$val->school_id][$val->id]['service_title'] = $val->service_title;
                $data[$val->school_id][$val->id]['time'] = date("Y-m-d", $val->start_time);
                foreach ($val->serviceExpense as $item) {
                    if ($item->fileld_content && in_array($item->fileld, array(1, 2, 3))) {
                        $data[$val->school_id][$item->sid]['data'][] = array(
                            'title' => $serviceInfo[$val->service_id]['expenseFields'][$item->fileld]['title'],
                            'money' => $item->fileld_content,
                            'fileld' => $item->fileld,
                        );
                    }
                }
            }
        }

        if ($data) {
            $criteria = new CDbCriteria;
            $criteria->compare('branchid', array_keys($data));
            $criteria->index = 'branchid';
            $branch = Branch::model()->findAll($criteria);

            foreach ($data as $school => $val) {
                $mailer = Yii::createComponent('common.extensions.mailer.EMailer');
                $mailer->AddAddress($branch[$school]->info->support_email);
                //$mailer->AddAddress('<EMAIL>');
                /*$mailer->AddCC('<EMAIL>');*/
                $mailer->Subject = date("Y/m", $strat_time) . ' - 每月IT支持报销详情 - ' . $branch[$school]->title;
                $mailer->getCommandView('serviceMonth', array(
                    'data' => $val,
                    'month' => date("Y/m", $strat_time),
                    'school' => $branch[$school]->title,
                ), 'main');
                $isProduction = defined("IS_PRODUCTION") ? IS_PRODUCTION : false;
                $mailer->iniMail($isProduction);
                $mailer->ClearBCCs();
                if ($mailer->Send()) {
                    echo $branch[$school]->title . ' - Success' . "\n";
                } else {
                    echo $branch[$school]->title . ' - Fail' . "\n";
                }
            }
        }
    }

    // 把新生注册得过敏信息更新到孩子基本表
    public function actionUpdateAllergy($yid = '', $schoolid = '')
    {
        if($yid) {
            Yii::import('common.models.regextrainfo.*');
            Yii::import('common.models.child.*');
            $criteria = new CDbCriteria;
            if ($schoolid) {
                $criteria->compare('schoolid', $schoolid);
            }
            $criteria->compare('yid', $yid);
            $reg = Reg::model()->findAll($criteria);
            $num = 1;
            foreach ($reg as $item) {
                $criteria = new CDbCriteria;
                $criteria->compare('reg_id', $item->id);
                $criteria->compare('step', 4);
                $criteria->compare('status', 1);
                $criteria->compare('complete', 1);
                $regExtraInfo = RegExtraInfo::model()->findAll($criteria);
                foreach ($regExtraInfo as $val) {
                    $data = json_decode($val->data);
                    $childMedia = ChildMisc::model()->findByPk($val->childid);
                    if(!$childMedia){
                        $childMedia = new ChildMisc();
                        $childMedia->childid = $val->childid;
                    }
                    $allergy = ($childMedia->allergy) ? $childMedia->allergy . ',' . $data->specialFood : $data->specialFood;
                    $childMedia->allergy = trim($allergy);
                    if(!$childMedia->save()){
                        $err = current($childMedia->getErrors());
                        echo $val->childid . ' - fail - ' . $err[0]  . $num . "\n";
                    }else {
                        echo $val->childid . ' - success - ' . $num . "\n";
                    }
                    $num++;
                }
            }
        }
    }
}
