<?php
$this->name = ($this->useModel)? CHtml::resolveName($this->model, $this->attribute) : $this->name;
?>
<script>
function mySelect_<?php echo $this->acName?>(elem){
	var v=$(elem).attr('uid');
	jQuery('#<?php echo $this->acName?>').data('autocomplete').close();
	$('#<?php echo $this->acName?>').val("");
	if(v>0)
	$('#<?php echo CHtml::getIdByName($this->name)?>').append('<option value="'+v+'" selected="selected">'+$(elem).html()+'</option>');
}
</script>
<?php
$hint = Yii::t("message", "Search by child ID, name, DOB, parent name, email, Phone NO.");
$flag = ($this->with<PERSON>lumni) ? 'withAlumni' : '';
echo CHtml::openTag("div", array("class"=>"mb5"));
$this->widget('zii.widgets.jui.CJuiAutoComplete',array(
    'name'=>$this->acName,
	'sourceUrl'=>Yii::app()->createUrl('//backend/search/childoutput', array(
		'displayType' => $this->simpleDisplay ? 'simpleDisplay' : '',
		'withAlumni' => $this->withAlumni ? 'withAlumni' : '',
		'allowMultipleSchool' => $this->allowMultipleSchool,
		)),
    'options'=>array(
        'minLength'=>'1',
		'delay'=>1000,
		'position'=> $this->acPopPosition,
    ),
    'htmlOptions'=>array(
		'class'=>'input ' . $this->acInputCSS,
		'placeholder' => $hint,
		'title' => $hint
    ),
));
echo CHtml::closeTag("div");
echo CHtml::openTag("div");
$this->htmlOptions['class'] = isset($this->htmlOptions['class'])? $this->htmlOptions['class'] : "";
if ($this->extendCss){
    $this->htmlOptions['class'] .= " acInputCss length_4";
}
echo CHtml::closeTag("div");

//$this->htmlOptions['empty'] = '请先搜索用户';

if($this->allowMultiple){
	$this->htmlOptions['multiple'] = 'multiple';
}else{
	$this->htmlOptions['size'] = 1;
}

if(!$this->useModel)
	echo CHtml::listBox($this->name, $this->select, $this->data, $this->htmlOptions);
else{
	echo CHtml::activeListBox($this->model, $this->attribute, $this->data, $this->htmlOptions);
}

if($this->allowMultiple){
	echo CHtml::openTag("p");
	echo Yii::t('message','用鼠标拖拉进行多选，或按住Ctrl进行点选');
	echo CHtml::closeTag("p");
}

$js = "jQuery('#".$this->acName."')";
$js .= ".data( 'autocomplete' )._renderItem = function( ul, item ) {
				return $( '<li onclick=\'mySelect_".$this->acName."(this);return false;\'></li>' )
					.attr('uid',item.value)
					.data( 'item.autocomplete', item )
					.append( item.label)
					.appendTo( ul );			
        };";
$cs = Yii::app()->getClientScript();
$cs->registerScript(__CLASS__.'#'.$this->name, $js);		
?>