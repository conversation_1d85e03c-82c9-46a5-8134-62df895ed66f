<?php
/**
 * Locale data for 'zh_CN'.
 *
 * This file is automatically generated by yiic cldr command.
 *
 * Copyright © 1991-2007 Unicode, Inc. All rights reserved.
 * Distributed under the Terms of Use in http://www.unicode.org/copyright.html.
 *
 * Copyright © 2008-2011 Yii Software LLC (http://www.yiiframework.com/license/)
 */
return array (
  'version' => '4123',
  'numberSymbols' => 
  array (
    'decimal' => '.',
    'group' => ',',
    'list' => ';',
    'percentSign' => '%',
    'nativeZeroDigit' => '0',
    'patternDigit' => '#',
    'plusSign' => '+',
    'minusSign' => '-',
    'exponential' => 'E',
    'perMille' => '‰',
    'infinity' => '∞',
    'nan' => 'NaN',
  ),
  'decimalFormat' => '#,##0.###',
  'scientificFormat' => '#E0',
  'percentFormat' => '#,##0%',
  'currencyFormat' => '¤#,##0.00',
  'currencySymbols' => 
  array (
    'AFN' => 'Af',
    'ANG' => 'NAf.',
    'AOA' => 'Kz',
    'ARA' => '₳',
    'ARL' => '$L',
    'ARM' => 'm$n',
    'ARS' => 'AR$',
    'AUD' => 'AU$',
    'AWG' => 'Afl.',
    'AZN' => 'man.',
    'BAM' => 'KM',
    'BBD' => 'Bds$',
    'BDT' => 'Tk',
    'BEF' => 'BF',
    'BHD' => 'BD',
    'BIF' => 'FBu',
    'BMD' => 'BD$',
    'BND' => 'BN$',
    'BOB' => 'Bs',
    'BOP' => '$b.',
    'BRL' => 'R$',
    'BSD' => 'BS$',
    'BTN' => 'Nu.',
    'BWP' => 'BWP',
    'BZD' => 'BZ$',
    'CAD' => 'CA$',
    'CDF' => 'CDF',
    'CLE' => 'Eº',
    'CLP' => 'CL$',
    'CNY' => '￥',
    'COP' => 'CO$',
    'CRC' => '₡',
    'CUC' => 'CUC$',
    'CUP' => 'CU$',
    'CVE' => 'CV$',
    'CYP' => 'CY£',
    'CZK' => 'Kč',
    'DEM' => 'DM',
    'DJF' => 'Fdj',
    'DKK' => 'Dkr',
    'DOP' => 'RD$',
    'DZD' => 'DA',
    'EEK' => 'Ekr',
    'ERN' => 'Nfk',
    'ESP' => 'Pts',
    'ETB' => 'Br',
    'EUR' => '€',
    'FIM' => 'mk',
    'FJD' => 'FJ$',
    'FKP' => 'FK£',
    'FRF' => '₣',
    'GBP' => '£',
    'GHC' => '₵',
    'GHS' => 'GH₵',
    'GIP' => 'GI£',
    'GMD' => 'GMD',
    'GNF' => 'FG',
    'GRD' => '₯',
    'GTQ' => 'GTQ',
    'GYD' => 'GY$',
    'HKD' => 'HK$',
    'HNL' => 'HNL',
    'HRK' => 'kn',
    'HTG' => 'HTG',
    'HUF' => 'Ft',
    'IDR' => 'Rp',
    'IEP' => 'IR£',
    'ILP' => 'I£',
    'ILS' => '₪',
    'INR' => 'Rs',
    'ISK' => 'Ikr',
    'ITL' => 'IT₤',
    'JMD' => 'J$',
    'JOD' => 'JD',
    'JPY' => 'JP¥',
    'KES' => 'Ksh',
    'KMF' => 'CF',
    'KRW' => '￦',
    'KWD' => 'KD',
    'KYD' => 'KY$',
    'LAK' => '₭',
    'LBP' => 'LB£',
    'LKR' => 'SLRs',
    'LRD' => 'L$',
    'LSL' => 'LSL',
    'LTL' => 'Lt',
    'LVL' => 'Ls',
    'LYD' => 'LD',
    'MMK' => 'MMK',
    'MNT' => '₮',
    'MOP' => 'MOP$',
    'MRO' => 'UM',
    'MTL' => 'Lm',
    'MTP' => 'MT£',
    'MUR' => 'MURs',
    'MXN' => 'MX$',
    'MYR' => 'RM',
    'MZM' => 'Mt',
    'MZN' => 'MTn',
    'NAD' => 'N$',
    'NGN' => '₦',
    'NIO' => 'C$',
    'NLG' => 'fl',
    'NOK' => 'Nkr',
    'NPR' => 'NPRs',
    'NZD' => 'NZ$',
    'PAB' => 'B/.',
    'PEI' => 'I/.',
    'PEN' => 'S/.',
    'PGK' => 'PGK',
    'PHP' => '₱',
    'PKR' => 'PKRs',
    'PLN' => 'zł',
    'PTE' => 'Esc',
    'PYG' => '₲',
    'QAR' => 'QR',
    'RHD' => 'RH$',
    'RON' => 'RON',
    'RSD' => 'din.',
    'SAR' => 'SR',
    'SBD' => 'SI$',
    'SCR' => 'SRe',
    'SDD' => 'LSd',
    'SEK' => 'Skr',
    'SGD' => 'S$',
    'SHP' => 'SH£',
    'SKK' => 'Sk',
    'SLL' => 'Le',
    'SOS' => 'Ssh',
    'SRD' => 'SR$',
    'SRG' => 'Sf',
    'STD' => 'Db',
    'SVC' => 'SV₡',
    'SYP' => 'SY£',
    'SZL' => 'SZL',
    'THB' => '฿',
    'TMM' => 'TMM',
    'TND' => 'DT',
    'TOP' => 'T$',
    'TRL' => 'TRL',
    'TRY' => 'TL',
    'TTD' => 'TT$',
    'TWD' => 'NT$',
    'TZS' => 'TSh',
    'UAH' => '₴',
    'UGX' => 'USh',
    'USD' => 'US$',
    'UYU' => '$U',
    'VEF' => 'Bs.F.',
    'VND' => '₫',
    'VUV' => 'VT',
    'WST' => 'WS$',
    'XAF' => 'FCFA',
    'XCD' => 'EC$',
    'XOF' => 'CFA',
    'XPF' => 'CFPF',
    'YER' => 'YR',
    'ZAR' => 'R',
    'ZMK' => 'ZK',
    'ZRN' => 'NZ',
    'ZRZ' => 'ZRZ',
    'ZWD' => 'Z$',
  ),
  'monthNames' => 
  array (
    'wide' => 
    array (
      1 => '1月',
      2 => '2月',
      3 => '3月',
      4 => '4月',
      5 => '5月',
      6 => '6月',
      7 => '7月',
      8 => '8月',
      9 => '9月',
      10 => '10月',
      11 => '11月',
      12 => '12月',
    ),
    'abbreviated' => 
    array (
      1 => '1月',
      2 => '2月',
      3 => '3月',
      4 => '4月',
      5 => '5月',
      6 => '6月',
      7 => '7月',
      8 => '8月',
      9 => '9月',
      10 => '10月',
      11 => '11月',
      12 => '12月',
    ),
  ),
  'monthNamesSA' => 
  array (
    'narrow' => 
    array (
      1 => '1月',
      2 => '2月',
      3 => '3月',
      4 => '4月',
      5 => '5月',
      6 => '6月',
      7 => '7月',
      8 => '8月',
      9 => '9月',
      10 => '10月',
      11 => '11月',
      12 => '12月',
    ),
    'abbreviated' => 
    array (
      1 => '一月',
      2 => '二月',
      3 => '三月',
      4 => '四月',
      5 => '五月',
      6 => '六月',
      7 => '七月',
      8 => '八月',
      9 => '九月',
      10 => '十月',
      11 => '十一月',
      12 => '十二月',
    ),
    'wide' => 
    array (
      1 => '一月',
      2 => '二月',
      3 => '三月',
      4 => '四月',
      5 => '五月',
      6 => '六月',
      7 => '七月',
      8 => '八月',
      9 => '九月',
      10 => '十月',
      11 => '十一月',
      12 => '十二月',
    ),
  ),
  'weekDayNames' => 
  array (
    'wide' => 
    array (
      0 => '星期日',
      1 => '星期一',
      2 => '星期二',
      3 => '星期三',
      4 => '星期四',
      5 => '星期五',
      6 => '星期六',
    ),
    'abbreviated' => 
    array (
      0 => '周日',
      1 => '周一',
      2 => '周二',
      3 => '周三',
      4 => '周四',
      5 => '周五',
      6 => '周六',
    ),
  ),
  'weekDayNamesSA' => 
  array (
    'narrow' => 
    array (
      0 => '日',
      1 => '一',
      2 => '二',
      3 => '三',
      4 => '四',
      5 => '五',
      6 => '六',
    ),
  ),
  'eraNames' => 
  array (
    'abbreviated' => 
    array (
      0 => '公元前',
      1 => '公元',
    ),
    'wide' => 
    array (
      0 => '公元前',
      1 => '公元',
    ),
    'narrow' => 
    array (
      0 => '公元前',
      1 => '公元',
    ),
  ),
  'dateFormats' => 
  array (
    'full' => 'y年M月d日EEEE',
    'long' => 'y年M月d日',
    'medium' => 'yyyy/MM/dd',
    'short' => 'yy-M-d',
  ),
  'timeFormats' => 
  array (
    'full' => 'zzzzah时mm分ss秒',
    'long' => 'zah时mm分ss秒',
    'medium' => 'ah:mm:ss',
    'short' => 'HH:mm',
  ),
  'dateTimeFormat' => '{1} {0}',
  'amName' => '上午',
  'pmName' => '下午',
  'orientation' => 'ltr',
);
