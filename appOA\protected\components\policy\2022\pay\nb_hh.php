<?php
if(!defined("IVY_POLICY"))
throw new CException('DO NOT USE THIS FILE DIRECTLY');

return array( 'policy'=>array(

    //是否老生老办法
    ENABLE_CONSTANT_TUITION => true,

    //年保育费
    //ANNUAL_CHILDCARE_AMOUNT => 8000,

    //是否开放课外活动账单
    ENABLE_AFTERSCHOOLS => true,

    //图书馆工本费
    FEETYPE_LIBCARD => 10,

    //学期四个时间点；取前后两端
    TIME_POINTS   => array(202209,202301,202302,202307),

    //年付开通哪些缴费类型
    PAYGROUP_ANNUAL => array(
        ITEMS => array(
            FEETYPE_TUITION,
            FEETYPE_LUNCH,
            FEETYPE_SCHOOLBUS,
        ),
        EXCEPTS => array(
            OTHER_PROGRAM_CHN,
        ),
        TUITION_CALC_BASE => BY_MONTH,
    ),

   //学期付开通哪些缴费类型
    PAYGROUP_SEMESTER => array(
        ITEMS => array(
            FEETYPE_TUITION,
            FEETYPE_LUNCH,
            FEETYPE_SCHOOLBUS,
        ),
        EXCEPTS => array(
            OTHER_PROGRAM_CHN,
        ),
        TUITION_CALC_BASE => BY_MONTH,
    ),

   //月份开通哪些缴费类型
    PAYGROUP_MONTH => array(
        ITEMS => array(
            FEETYPE_TUITION,
            FEETYPE_LUNCH,
            FEETYPE_SCHOOLBUS,
        ),
        TUITION_CALC_BASE => BY_MONTH,
    ),

    //计算基准：BY_MONTH, 或者 BY_SETTING, 前者表示所有的学费都基于月费用计算，后者表示所有的学费都基于学期或学年
    TUITION_CALCULATION_BASE => array(

        BY_MONTH => array(

            //月收费金额
            AMOUNT => array(
                FULL5D => 8500,
//                HALF5D => 5800,
//                FULL5D1 => 6500,
//                OTHER_PROGRAM_CHILDCARE => 3800,
                OTHER_PROGRAM_CHN => 1050,
//                OTHER_PROGRAM_CHN_1 => 800,
//                FIX => 24,
            ),

            //收费月份，及其权重
            MONTH_FACTOR => array(
                202209=>1,
                202210=>1,
                202211=>1,
                202212=>1,
                202301=>.6,
                202302=>1,
                202303=>1,
                202304=>1,
                202305=>1,
                202306=>1,
                202307=>0.1
            ),

            //折扣
            GLOBAL_DISCOUNT => array(
                DISCOUNT_ANNUAL => '0.95:10:2022-12-01:round',
                DISCOUNT_SEMESTER1 => '0.97:10:2023-01-01:round',
                DISCOUNT_SEMESTER2 => '1:1:2023-07-31:round'
            )

        ),

    ),

    //每餐费用
    LUNCH_PERDAY => array(
        LUNCHA => 25,
        LUNCHB => 17,
    ),

    //账单到期日和账单开始日之差
    DUEDAYS_OFFSET => 1,

));