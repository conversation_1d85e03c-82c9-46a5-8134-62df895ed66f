<?php
    if($withToolBar){
        $widthHeight=array(
            'pageWidth' => 820,
            'pageHeight' => 1148,
            'bodyfontSize' => 16,
            'tableTheadSzie'=>14,
            'tableTBodySzie'=>10,
            'coverTextSize'=>60,
            'coverLogoSize'=>300,
            'coverFrontendImg' => 343,
            'avatarWdith' => 350,
            'avatarHeight' => 350,
            'avatarLeft' => 94,
            'avatarTop' => 178,
            'creationTop' => 580,
            'creationHeight' => 490,
            'teacherList'=>81,
            'chineseteacherList'=>81,
            'publicImgHeight'=>502,
            'publicTablePadding'=>4,
            'teacherMesImgTop'=>170,
            'teacherMesImgWidth'=>440,
            'stuInfoSize'=>24,
            'toSumUpPostionLeft'=>54,
            'toSumUpPostionTop'=>250,
            'toSumUpPostionWidth'=>350,
            'toSumUpTitleTop'=>112,
            'toSumUpTitleSize'=>40,
            'lineHeight'=>26,
            'teacherMess'=>24,
            'teacherListBto'=>90,
            'teacherListLeft'=>70,
            'teacherNameEnSize'=>24,
            'enTeacherNameEnSize'=>18,
            'chinesedirectorName'=>32,
            'important-infoOneTop'=>240,
            'chineseimportant-infoOneTop'=>290,
            'importantOnepWidth'=>545,
            'importantTwoWidth'=>505,
            'importantTwoWTop'=>33,
            'chineseimportantTwoWTop'=>50,
            'importantTwoWLeft'=>25,
            'importantThreeWidth'=>479,
            'importantThreeTop'=>75,
            'chineseimportantThreeTop'=>115,
            'importantThreeLeft'=>197,
            'importantNoteWidth'=>372,
            'chineseimportantNoteWidth'=>325,
            'text6cDiv'=>670,
            'cLeftWidth'=>311,
            'sixImgDivWidth'=>470,
            'sixImgDivMargin'=>-235,
            'sixImgDivTop'=>180,
            'sixImgDivLeft'=>155,
            'profileBoxTop'=>55,
            'profileBoxRight'=>50,

        );
    }else{
        $widthHeight=array(
            'pageWidth' => 620,
            'pageHeight' => 877,
            'bodyfontSize' =>12,
            'tableTheadSzie'=>12,
            'tableTBodySzie'=>8,
            'coverTextSize'=>50,
            'coverLogoSize'=>230,
            'coverFrontendImg' => 259,
            'avatarWdith' => 250,
            'avatarHeight' => 250,
            'avatarLeft' => 77,
            'avatarTop' => 139,
            'creationTop' => 440,
            'creationHeight' => 350,
            'teacherList'=>61,
            'chineseteacherList'=>61,
            'publicImgHeight'=>371,
            'publicTablePadding'=>2,
            'teacherMesImgTop'=>130,
            'teacherMesImgWidth'=>350,
            'stuInfoSize'=>18,
            'toSumUpPostionLeft'=>40,
            'toSumUpPostionTop'=>215,
            'toSumUpPostionWidth'=>300,
            'toSumUpTitleTop'=>85,
            'toSumUpTitleSize'=>30,
            'lineHeight'=>20,
            'teacherMess'=>18,
            'teacherListBto'=>80,
            'teacherListLeft'=>50,
            'teacherNameEnSize'=>18,
            'enTeacherNameEnSize'=>15,
            'chinesedirectorName'=>26,
            'important-infoOneTop'=>181,
            'chineseimportant-infoOneTop'=>218,
            'importantOnepWidth'=>395,
            'importantTwoWidth'=>390,
            'importantTwoWTop'=>8,
            'chineseimportantTwoWTop'=>35,
            'importantTwoWLeft'=>1,
            'importantThreeWidth'=>330,
            'importantThreeTop'=>54,
            'chineseimportantThreeTop'=>82,
            'importantThreeLeft'=>133,
            'importantNoteWidth'=>315,
            'chineseimportantNoteWidth'=>250,
            'text6cDiv'=>510,
            'cLeftWidth'=>230,
            'sixImgDivWidth'=>350,
            'sixImgDivMargin'=>-175,
            'sixImgDivTop'=>145,
            'sixImgDivLeft'=>125,
            'profileBoxTop'=>40,
            'profileBoxRight'=>30,

        );
    }
    $tdNum = 5;
?>
<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="language" content="en" />
    <title><?php echo CHtml::encode($this->pageTitle); ?></title>
</head>
<body>

    <?php if($withToolBar):?>
        <div class="navbar navbar-fixed-top navbar-inverse" role="navigation">
            <?php
            echo CHtml::form( $downloadActionUrl );
            echo CHtml::hiddenField('schoolid',     $params['schoolid']);
            echo CHtml::hiddenField('childid',      $params['childid']);
            echo CHtml::hiddenField('id',           $params['id']);
            echo CHtml::submitButton('Download PDF / 下载 PDF', array('style'=>'font-family: Microsoft Yahei','id'=>'submit'));
            echo CHtml::endForm();
            ?>
        </div>
    <?php endif;?>
    <!-- chinese english-->
    <div class='bgBox' style='position: relative;'>
        <?php if($withToolBar):?>
            <div class='bgck' id='bgck'></div>
            <div id="loading">
                <i></i>
            </div>
        <?php endif;?>
        <div  class="report-wrapper web-view <?php echo Yii::app()->language == 'zh_cn' ? 'chinese' : 'english' ?>">

            <div class="report-page-blank page-break-after cover-frontend">
                <div class='logo'><img src="https://m2.files.ivykids.cn/cloud01-file-5Fu3aYsVEedYXJmGsNoLYG__zUm4y.png" alt=""></div>
                <div class="bg"></div>
                <div class='img'></div>
                <div class='report'>
                    <div><?php echo Yii::t('principal', 'Semester<br>Report') ?></div>
                </div>
                <div class='coverName'>
                    <div><?php echo Yii::t('principal', 'Name:') ?>
                    <?php
                            $childName = $data['child']->getChildName();
                            $hasCNChars = CommonUtils::hasCNChars( $childName );
                            if( $hasCNChars > 0){
                                echo '<span>';
                            }else{
                                echo '<span >';
                            }
                            echo $childName . '</span>';
                        ?>
                    </div>
                    <div><?php echo Yii::t('principal', 'Semester:') ?>
                    <?php echo IvyClass::formatSchoolYear($data['report']['startyear'])?>
                    <?php echo ($data['report']['semester'] == 1) ? Yii::t("principal", "Autumn"): Yii::t("principal", "Spring")
                        ?>
                    </div>
                    <div><?php echo Yii::t('principal', 'Campus:') ?>
                        <?php echo Yii::t('principal', 'Daystar Academy Early Learning Center') ?>
                    </div>
                </div>
            </div>
            <div class="report-page-blank page-break-after prologue"></div>
            <div class="report-page-blank page-break-after important-info"></div>
            <div class="report-page-blank page-break-after studentInfo">
                <div class="profile-box">
                    <div>
                        <label><?php echo Yii::t('principal', 'Name') ?></label>
                        <?php
                            $childName = $data['child']->getChildName();
                            $hasCNChars = CommonUtils::hasCNChars( $childName );
                            if( $hasCNChars > 0){
                                echo '<span class="cn">';
                            }else{
                                echo '<span class="en">';
                            }
                            echo $childName . '</span>';
                        ?>
                    </div>
                    <div>
                    <label><?php echo Yii::t('principal', 'DOB') ?></label>
                    <?php echo $data['child']->birthday_search?>
                    </div>
                    <div>
                        <label><?php echo Yii::t('principal', 'Campus') ?></label>
                        <?php
                            $title = $data['campusT'];
                            $title = str_replace('(', '(<span class="cn">', $title);
                            $title = str_replace(')', '</span>)', $title);
                            echo $title;
                        ?>
                    </div>
                    <div>
                    <label><?php echo Yii::t('principal', 'Semester') ?></label>
                        <?php echo IvyClass::formatSchoolYear($data['report']['startyear'])?>
                        <?php echo ($data['report']['semester'] == 1) ? Yii::t("principal", "Autumn"): Yii::t("principal", "Spring")
                        ?>
                    </div>
                    <div>
                    <label><?php echo Yii::t('principal', 'Class') ?></label>
                        <?php echo $data['classTitle'];?>
                    </div>
                    <div>
                    <label class='classTeacher'><?php echo Yii::t('principal', 'Teachers') ?></label>
                        <?php
                            $listClass = (count($data['teachers']) <= 9 ) ? 'a' :((count($data['teachers']) < 16 ) ? 'b' : 'c');
                        ?>
                        <div class="teacher-list teacher-list-<?php echo $listClass;?>">
                            <?php
                            if ($data['teachers']):?>
                                <?php foreach ($data['teachers'] as $tName):?>
                                    <span class="item"><?php echo $tName;?></span>
                                <?php endforeach;?>
                            <?php endif;?>
                        </div>
                        <div style="clear: both"></div>
                    </div>
                </div>
                <div class='profile-photo shadow-img'>
                <?php
                if(isset($data['photos'][$data['items']['Avatar']['media_id']]) && $data['photos'][$data['items']['Avatar']['media_id']]){
                    if ($data['items']['Avatar']['img_cropper']) {
                        echo CHtml::image($data['photos'][$data['items']['Avatar']['media_id']].$data['items']['Avatar']['img_cropper'].'/thumbnail/1000x1000');
                    } else {
                        echo CHtml::image($data['photos'][$data['items']['Avatar']['media_id']].'?imageView2/1/w/1000/h/1000');
                    }
                }
                elseif($data['child']->photo){
                    echo CHtml::image(CommonUtils::childPhotoUrl($data['child']->photo));
                }
                ?>
                </div>
                <div class="cover-bottom">
                    <?php
                    if($data['items']['FrontCoverWork']['media_id']){
                        echo CHtml::image($data['photos'][
                            $data['items']['FrontCoverWork']['media_id']
                            ] . "?imageMogr/auto-orient", "", array('class'=>'middle-size shadow-img')
                        );
                    }
                    ?>
                    <p class="tac">
                        <?php
                        if($data['items']['FrontCoverWork']['content']){
                            echo $data['items']['FrontCoverWork']['content'];
                        }
                        ?>
                    </p>
                </div>
            </div>
            <?php foreach ($data['learningDomains'] as $learnItem) : ?>
                <div class="report-page-blank page-break-after tableCom">
                    <?php
                    $childReport = isset($data['childLearningDomains'][$learnItem->id])?$data['childLearningDomains'][$learnItem->id]:null;
                    $learnText = array_values(array_filter(explode("\n", $learnItem->getContent('intro'))));
    
                    ?>
                    <div class='img'>
                        <?php
                        if($childReport->items_img){
                            echo CHtml::image($data['photos'][
                            $childReport->items_img
                            ] . $childReport->items_img_cropper . '/thumbnail/2000x2000>', "", array()
                            );
                        }
                        ?>
                    </div>
                    <table class="arttable">
                        <thead>
                        <tr>
                            <th class='key'><?php echo  Yii::t("principal", $learnItem->getContent() )?></th>
                            <?php if( $tdNum == 5):?>
                                <th class='observed'><?php echo Yii::t('teaching', 'Experiencing Challenges');?></th>
                            <?php endif;?>
                            <th class='marking'><?php echo Yii::t('teaching', 'Making Progress');?></th>
                            <th class='meething'><?php echo Yii::t('teaching', 'Meeting Expectations');?></th>
                            <th class='exceeding'><?php echo Yii::t('teaching', 'Achieving Excellence');?></th>
                        </tr>
                        </thead>
                        <tbody>
                        <?php foreach ($learnItem->items as $_item):?>
                            <tr>
                                <td><?php echo $_item->getContent();?></td>
                                <?php if( $tdNum == 5):?>
                                    <td>
                                    <?php if( isset($data['childLDItems'][$_item->id]) && $data['childLDItems'][$_item->id]->option == 1):?>
                                        <img src="http://m1.files.ivykids.cn//images/sreport2/img/not_observed.png" alt="">
                                    <?php endif;?>
                                    </td>
                                <?php endif;?>
                                <td>
                                    <?php if( isset($data['childLDItems'][$_item->id]) && $data['childLDItems'][$_item->id]->option == 2):?>
                                        <img src="http://m1.files.ivykids.cn/images/sreport2/img/marking.png" alt="">
                                    <?php endif;?>
                                </td>
                                <td>
                                    <?php if( isset($data['childLDItems'][$_item->id]) && $data['childLDItems'][$_item->id]->option == 3):?>
                                        <img src="http://m1.files.ivykids.cn/images/sreport2/img/meeting.png" alt="">
                                    <?php endif;?>
                                </td>
                                <td>
                                    <?php if( isset($data['childLDItems'][$_item->id]) && $data['childLDItems'][$_item->id]->option == 4):?>
                                        <img src="http://m1.files.ivykids.cn/images/sreport2/img/exceeding.png" alt="">
                                    <?php endif;?>
                                </td>
                            </tr>
                        <?php endforeach;?>
                        <?php if( $childReport->proof_text):?>
                        <tr>
                            <td colspan='<?php echo $tdNum; ?>' class='lastTable'>
                                <div class='problem'>
                                    <strong><?php echo Yii::t('teaching', 'Comments:') ?></strong>
                                </div>
                                <div class='answer'>
                                    <i><?php echo $childReport->proof_text;?></i>
                                </div>
                            </td>
                        </tr>
                        <?php endif;?>
                        </tbody>
                    </table>
                </div>
            <?php endforeach; ?>
            <?php
            $childReportZongJie = isset($data['childLearningDomains'][0])?$data['childLearningDomains'][0]:null;
            if($data['campusGroup'] == 30 && $childReportZongJie->proof_text){
                ?>
                <div class="report-page-blank page-break-after toSumUp">
                    <div class='toSumUpTitle'><strong><?php echo Yii::t('principal', 'Summary') ?></strong></div>
                    <div class='toSumUpPostion'>
                        <?php
                        $learnTexts = array_values(array_filter(explode("\n", $childReportZongJie->proof_text)));
                        foreach ($learnTexts as $val){
                        ?>
                            <p>
                                <?php echo $val;?>
                            </p>
                        <?php }?>
                    </div>
                </div>
            <?php } ?>
            <div class="report-page-blank page-break-after teacherMes">
                <p class="teacherMesImg">
                    <?php
                    if($data['items']['TeacherMessage']['media_id']){
                        echo CHtml::image($data['photos'] [
                        $data['items']['TeacherMessage']['media_id']
                        ] . "?imageMogr/auto-orient", "", array('class'=>'')
                        );
                    }
                    ?>
                </p>
                <div class='teacherMesText'>
                    <?php
                    if($data['items']['TeacherMessage']['content']){
                        echo nl2br( $data['items']['TeacherMessage']['content']);
                    }
                    ?>
                </div>
                <div style='clear:both'></div>
                <?php
                    $listClass = (count($data['teachers']) < 9 ) ? 'a' :(
                    (count($data['teachers']) < 16 ) ? 'b' : 'c'
                    );
                ?>
                <div class="teacher-list teacher-list-<?php echo $listClass;?> teaBtoList">
                    <?php
                    if ($data['teachersName']):?>
                        <?php foreach ($data['teachersName'] as $tName):?>
                            <span class="teacherName"><?php echo $tName;?></span>
                        <?php endforeach;?>
                    <?php endif;?>
                </div>
                <div class="teacherMesBto">
                <?php if(!$data['teachersName']): ?>
                        <div>
                        ___________________
                        </div>
                    <?php endif;?>
                    <div><strong class="en"><i><?php echo Yii::t('principal', 'Your Teachers') ?></i></strong></div> 
                </div>
            </div>
            <div class="report-page-blank page-break-after groupPhoto">
                <div class="groupClass"><strong><i><?php echo $data['classTitle'];?></i></strong></div>
                <p class="groupPhotoImg">
                <?php
                if($data['items']['BackCoverPhoto']['media_id']){
                    echo CHtml::image($data['photos'] [
                    $data['items']['BackCoverPhoto']['media_id']
                    ] . "?imageMogr/auto-orient", "", array('class'=>'')
                    );
                }
                ?>
                </p>
                <!-- <div class='groupBtoLeft'>
                    <span class="teacherName"><?php echo Yii::app()->language == 'zh_cn' ? '李姝' : 'Suly Li' ?></span>
                </div>
                <div class="teacherMesBtoLeft">
                    <strong class="en"><i><?php echo Yii::t('principal', 'Academic Principal') ?></i></strong>
                </div> -->
                <div class='groupBto'>
                    <span class="teacherName"><?php echo $data['cdName']; ?></span>
                </div>
                <div class="teacherMesBto">
                    <strong class="en"><i><?php echo Yii::t('principal', 'Campus Director') ?></i></strong>
                </div>
            </div>
            <div class="report-page-blank page-break-after end">
            </div>
        </div>
    </div>
    <script>
        var submit = document.querySelector('#submit');
        submit.onclick = ()=>{
            document.getElementById("loading").style.display = "block"
            document.getElementById("bgck").style.display = "block"
        }
    </script>
      <style>
        @font-face {
            font-family: 'teacherFont';
            src: url('https://m1.files.ivykids.cn/images/sreport2/img/FZHJJW.TTF')
        }
        @font-face {
            font-family: 'cnFontName';
            src: url('https://m1.files.ivykids.cn/images/sreport2/img/FZHCJW.TTF')
        }
        @font-face {
            font-family: 'enFontName';
            src: url('https://m1.files.ivykids.cn/images/sreport2/img/SEGOEPR.TTF')
        }
        @font-face {
            font-family: 'simhei';
            src: url('https://m1.files.ivykids.cn/images/sreport2/img/simhei.ttf')
        }
        @font-face {
            font-family: 'Trebuchet MS';
            src: url('https://m1.files.ivykids.cn/images/sreport2/img/trebuc.ttf')
        }
        body {
            font-family:'Trebuchet MS','simhei',Arial,Helvetica, sans-serif;
            font-size: 16px;
            line-height: 1.42857143;
            color: #333333;
            margin: 0;
            padding: 0;
            background-color: #efefef;
            <?php if($withToolBar):?>
                padding-top: 70px;
                padding-bottom: 20px;
            <?php endif;?>
        }
        .enFont{
            font-family: 'enFontName';
        }
        .cnFont{
            font-family: 'cnFontName';
        }
        .bgck{
            width:100%;
            height:100%;
            position: absolute;
            left:0;
            top:0;
            background:#000;
            opacity:0.3;
            z-index:9;
            display:none
        }
        #loading {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 999;
        }
        #loading i {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            width: 50px;
            height: 50px;
            border: 4px solid #fff;
            border-right-color: transparent;
            border-radius: 50%;
            animation: loadingAnimation 0.8s infinite Linear;
            margin: auto;
        }
        @keyframes loadingAnimation {
            from {
                transform: rotate(0deg);
            }
            to {
                transform: rotate(360deg);
            }
        }
        .page-opposite{
            background: url('http://m1.files.ivykids.cn/images/sreport2/header-02.jpg') no-repeat center top, url('http://m1.files.ivykids.cn/images/sreport2/footer-02.jpg') no-repeat center bottom;
            background-size: 100%;
            background-color: #ffffff;
        }
        .report-page-blank{
            padding:0;
            height: <?php echo $widthHeight['pageHeight']?>px;
            position: relative;
            background-size: 100%;
            background-color: #ffffff;
            margin-top:10px
        }
        .shadow-img{
            box-shadow: 0 .5rem 1rem rgba(0,0,0,.15)!important;
        }
        .cover-frontend{
            background-image: url('<?php echo $data['photos'][$data['items']['CoverWork']['media_id']] . "?imageMogr/auto-orient"?>');
            background-repeat:no-repeat;
            background-position: center;
            background-size: cover;
        }
        .cover-frontend .logo{
            width:<?php echo $widthHeight['coverLogoSize']?>px;
            position:absolute;
            right:20px;
            top:30px
        }
        .cover-frontend .logo img{
            width:100%
        }
        .cover-frontend .report{
            position:absolute;
            right:50px;
            bottom:30%;
            color:#fff;
            font-weight:700;
            font-size:<?php echo $widthHeight['coverTextSize']?>px;
            text-shadow:1px 1px 0px #999;
        }
        .cover-frontend .img{
            position: absolute;
            left: 0;
            bottom: 0;
            height: <?php echo $widthHeight['coverFrontendImg']?>px;
            width: 100%;
            background-image: url('http://m1.files.ivykids.cn/images/sreport2/img/cover.png');
            background-size: 100%;
        }
        .cover-frontend .coverName{
            position:absolute;
            left:30px;
            bottom:55px;
            font-size:<?php echo $widthHeight['stuInfoSize']?>px;
            color:#fff;
            width:80%
        }
        .cover-backend{
            background-image: url('http://m1.files.ivykids.cn/images/sreport2/cover-backend.jpg');
        }
        .prologue{
            background-image: url('http://m2.files.ivykids.cn/cloud01-file-8025768Fv2G60tEOCYHHUNSI3IuRM-Xassk.jpg');
        }
        .important-info{
            background-image: url('http://m2.files.ivykids.cn/cloud01-file-8025768FqnlEYOXP7R2zbDpmiZsBs9jLi-m.jpg');
        }
        .chinese .important-info{
            background-image: url('http://m2.files.ivykids.cn/cloud01-file-8025768FhR9abYiKl6oFGyXjehGKi6GUK_p.jpg');
        }
        .important-info .one {
            width: 100%;
            padding-top: <?php echo $widthHeight['important-infoOneTop']?>px

        }
        .chinese .important-info .one {
            width: 100%;
            /* chineseimportant-infoOneTop */
            padding-top:<?php echo $widthHeight['chineseimportant-infoOneTop']?>px
        }
        .important-info .one p{
            width:<?php echo $widthHeight['importantOnepWidth']?>px;
            float:right
        }
        .chinese .important-info .one p{
            width: <?php echo $widthHeight['importantOnepWidth']?>px;
            float:right
        }
        .important-info .two{
            width:<?php echo $widthHeight['importantTwoWidth']?>px;
            padding-top:<?php echo $widthHeight['importantTwoWTop']?>px;
            padding-left:<?php echo $widthHeight['importantTwoWLeft']?>px;
        }
        .chinese .important-info .two{
            width:<?php echo $widthHeight['importantTwoWidth']?>px;
            padding-top:<?php echo $widthHeight['chineseimportantTwoWTop']?>px;
            padding-left:<?php echo $widthHeight['importantTwoWLeft']?>px;
        }
        .important-info .three{
            width:<?php echo $widthHeight['importantThreeWidth']?>px;
            padding-top:<?php echo $widthHeight['importantThreeTop']?>px;
            padding-left: <?php echo $widthHeight['importantThreeLeft']?>px;
        }
        .chinese .important-info .three{
            width:<?php echo $widthHeight['importantThreeWidth']?>px;
            padding-top:<?php echo $widthHeight['chineseimportantThreeTop']?>px;
            padding-left: <?php echo $widthHeight['importantThreeLeft']?>px;
        }
        .important-info .note{
            width:<?php echo $widthHeight['importantNoteWidth']?>px;
            padding-top:40px;
        }
        .chinese .important-info .note{
            width:<?php echo $widthHeight['chineseimportantNoteWidth']?>px;
            padding-top: 75px;
        }
        .text-6c{
            background-image: url('http://m1.files.ivykids.cn/images/sreport2/img/6c1.jpg');
        }
        .chinese .text-6c{
            background-image: url('http://m1.files.ivykids.cn/images/sreport2/img/6c2.jpg');
        }
        .sixImgDiv{
            width:<?php echo $widthHeight['sixImgDivWidth']?>px;
            height:<?php echo $widthHeight['sixImgDivWidth']?>px;
            position: absolute;
            left:50%;
            top: <?php echo $widthHeight['sixImgDivTop']?>px;
            /* background:#fff;
            border-radius:50%; */
            margin-left:<?php echo $widthHeight['sixImgDivMargin']?>px
        }
        .sixImgDiv img{
            position: absolute;
            left:0;
            top:0;
            width:100%;
            height:100%;
        }
        .sixImg{
            width:100%;
            height:100%;
        }
        
        .text-6c .sixText{
            padding:<?php echo $widthHeight['text6cDiv']?>px 50px 0 50px;
        }
        .text-6c .cLeft{
            float: left;
            width:<?php echo $widthHeight['cLeftWidth']?>px;
            padding-right: 50px;
            line-height: <?php echo $widthHeight['lineHeight']?>px;
            color:#14283D
        }
        .text-6c .cRight{
            float: right;
            width:<?php echo $widthHeight['cLeftWidth']?>px;
            color:#fff;
            line-height: <?php echo $widthHeight['lineHeight']?>px

        }
        .studentInfo{
            background-image: url('http://m1.files.ivykids.cn/images/sreport2/img/stuInfo.jpg');
            background-repeat:no-repeat;
            background-position:top left;
        }
        .studentInfo .profile-photo{
            position: absolute;
            left:<?php echo $widthHeight['avatarLeft']?>px;
            top: <?php echo $widthHeight['avatarTop']?>px;
            width: <?php echo $widthHeight['avatarWdith']?>px;
            height:<?php echo $widthHeight['avatarHeight']?>px;
            border-radius: 50%;
            -moz-border-radius: 50%;
            -webkit-border-radius:50%;
            overflow: hidden;
            background:#fff
        }
        .studentInfo .profile-photo img{
            width:100%;
            border-radius: 50%;
            -moz-border-radius: 50%;
            -webkit-border-radius:50%;
        }
        .studentInfo .profile-box{
            position: absolute;
            right:<?php echo $widthHeight['profileBoxRight']?>px;
            top:<?php echo $widthHeight['profileBoxTop']?>px;
            color:#fff;
            /* max-width:250px */
        }

        .studentInfo .profile-box div{line-height:<?php echo $widthHeight['lineHeight']?>px}
        .studentInfo .profile-box label:after{content: ' : '}
        .studentInfo .profile-box .classTeacher{
            float: left;
        }
        .studentInfo .profile-box .teacher-list{
            margin-left:<?php echo $widthHeight['teacherList']?>px;
        }
        .chinese .studentInfo .profile-box .teacher-list{
            margin-left:<?php echo $widthHeight['chineseteacherList']?>px;
        }
        .teacher-list span.item{display: inline-block; text-align: left;color: #fff;}
        .teacher-list-a span.item{display: block; }
        .teacher-list-b span.item{width: 110px;float: left; height: 25px;}

        .studentInfo .cover-bottom{
            position: absolute;
            width: 80%;
            top: <?php echo $widthHeight['creationTop']?>px;
            left: 10%;
        }
        .studentInfo .cover-bottom img{
           width:100%;
           max-height:<?php echo $widthHeight['creationHeight']?>px
        }
        .studentInfo .cover-bottom .tac{
            text-align: center;
            font-size:<?php echo $widthHeight['bodyfontSize'] ?>px;
            /* font-family:'EnFontName' */
        }
        table.arttable {
            border-collapse: collapse;
            width:100%
        }
         table.arttable th {
            border-width:2px;
            border-style: solid;
            border-color: #fff;
            text-align:center;
            color:#fff;
            font-size:<?php echo $widthHeight['tableTBodySzie'] ?>px;
            font-weight:400;
        }
         table.arttable tr .key{
            background:#13ae67;
            font-size:<?php echo $widthHeight['tableTheadSzie'] ?>px;
            text-align:left;
            padding:10px 8px;
            font-weight:700
        }
        table.arttable tr .marking{
            background:#00a29a;
            width:65px
        }
        table.arttable  tr .meething{
            background:#2ea7e0;
            width:65px
        }
        table.arttable  tr .exceeding{
            background:#036eb8;
            width:65px
        }
        table.arttable  tr .observed{
            background:#449867;
            width:65px
        }
        table.arttable tbody tr td:first-child{
            background:#d9f1ef;
            padding:2px 8px;
            text-align:left;

        }
        table.arttable tbody  tr td:first-child+td{
            background:#E3F0E9;
        }
        table.arttable tbody tr td:first-child+td+td{
            background:#d9f1ef;
        }
        table.arttable tbody tr td:first-child+td+td+td{
            background:#dff1fa;
        }
        table.arttable tbody tr td:last-child{
            background:#d9e9f4;
        }
        table.arttable tbody tr td img{
            width:20px;
        }
        table.arttable tbody tr td {
            border-width:2px;
            font-size:<?php echo $widthHeight['tableTBodySzie'] ?>px;
            border-style: solid;
            border-color: #fff;
            text-align:center;
            padding:<?php echo $widthHeight['publicTablePadding'] ?>px
        }
        table.arttable tbody tr .lastTable{
            background:#c0e8e6 !important;
            padding:8px !important
        }
        table.arttable tbody tr .lastTable .problem{
           color:#20b378;
           font-size:11px
        }
        table.arttable tbody tr .lastTable .answer{
            padding:2px 0px
        }
        .tableCom{
            padding:0 20px
        }
        .tableCom .img{
            width:100%;
            padding-bottom:20px;
            padding-top:20px;
            height:<?php echo $widthHeight['publicImgHeight'] ?>px
        }
        .tableCom .img img{
            width:100%;
            height:100%
        }
        .teacherMes{
            background-image: url('http://m1.files.ivykids.cn/images/sreport2/img/teaMessageEn.jpg');
            background-repeat:no-repeat;
            background-size: cover;
        }
        .chinese .teacherMes{
            background-image: url('http://m1.files.ivykids.cn/images/sreport2/img/teaMessageCn.jpg');
        }
        .teacherMes .teacherMesImg{
            width:100%;
            margin:auto;
            text-align:center;
            padding-top:<?php echo $widthHeight['teacherMesImgTop'] ?>px;
        }
        .teacherMes .teacherMesImg img{
            border-radius:10%;
            margin:auto;
            height:<?php echo $widthHeight['teacherMesImgWidth'] ?>px;
        }
        .teacherMes .teacherMesText{
            width:80%;
            margin-left:10%;
            margin-top:20px;
            font-size:<?php echo $widthHeight['teacherMess'] ?>px;
            font-family: 'teacherFont';
            display: inline-block;
            text-align: justify;
        }
        .teacherSign{
            clear: both;
            position: absolute;
            bottom: 55px;
            left: 0;
        }
        .teacherSign img{
            /* float: left;
            margin-right: 30px; */
            width: 120px;
        }
        .DirectorSign{
            width: 150px;
            position: absolute;
            right: 50px;
            bottom: 50px;
        }
        .DirectorSign img{
            width:100%;
        }
        .teacherMesBtoLeft{
            position: absolute;
            left:50px;
            bottom:50px;
            text-align:left;
            font-size:<?php echo $widthHeight['teacherMess'] ?>px;
        }
        .teacherMesBto{
            position: absolute;
            right:50px;
            bottom:50px;
            text-align:right;
            font-size:<?php echo $widthHeight['teacherMess'] ?>px;
        }
        .teaBtoList{
            position: absolute;
            right:50px;
            bottom:<?php echo $widthHeight['teacherListBto'] ?>px;
            margin-left:50px;
            text-align:right;
            font-size:<?php echo $widthHeight['teacherMess'] ?>px;
            border-bottom:2px solid #000
        }
        .teacherName{
            font-size:<?php echo $widthHeight['enTeacherNameEnSize'] ?>px;
            font-family: 'enFontName'
        }
        .teaBtoList .teacherName{
            padding-left:10px;
            color:#000 !important
        }
        .groupBtoLeft{
            position: absolute;
            left:50px;
            bottom:<?php echo $widthHeight['teacherListBto'] ?>px;
            padding-right:50px;
            text-align:left;
            font-size:<?php echo $widthHeight['teacherMess'] ?>px;
            border-bottom:2px solid #000
        }
        .groupBto{
            position: absolute;
            right:50px;
            bottom:<?php echo $widthHeight['teacherListBto'] ?>px;
            padding-left:50px;
            text-align:right;
            font-size:<?php echo $widthHeight['teacherMess'] ?>px;
            border-bottom:2px solid #000
        }
        .DirectorName{
            position: absolute;
            right:50px;
            bottom:<?php echo $widthHeight['teacherListBto'] ?>px;
            width:84%;
            text-align:right;
            font-size:<?php echo $widthHeight['teacherMess'] ?>px;
            color:#000
        }
        .director{
            font-size:<?php echo $widthHeight['directorName'] ?>px;
            font-family: 'enFontName'
        }
        .chinese  .director{
            font-size:<?php echo $widthHeight['chinesedirectorName'] ?>px;
            font-family: 'cnFontName'
        }
        .chinese .teacherName{
            font-size:<?php echo $widthHeight['teacherNameEnSize'] ?>px;
            font-family:'cnFontName'
        }
        .groupPhoto{
            background-image: url('http://m2.files.ivykids.cn/cloud01-file-8025768FhPxxM5kFRgwiwrJRvaOK5MpHTjB.jpg');
            background-repeat:no-repeat;
            background-size: cover;
        }
        .groupPhoto .groupClass{
            font-size:26px;
            padding:50px 50px 20px;
            color:#142A43
        }
        .groupPhoto .groupPhotoImg{
            width:100%;
        }
        .groupPhoto .groupPhotoImg img{
            width:100%;
        }
        .end{
            background-image: url('http://m1.files.ivykids.cn/images/sreport2/img/end.jpg');
            background-repeat:no-repeat;
            background-size: cover;
        }
        .toSumUp{
            background-image: url('http://m1.files.ivykids.cn/images/sreport2/img/mik.jpg');
            background-repeat:no-repeat;
            background-size: cover;
        }
        .toSumUpTitle{
            position: absolute;
            left:<?php echo $widthHeight['toSumUpPostionLeft'] ?>px;
            top:<?php echo $widthHeight['toSumUpTitleTop'] ?>px;
            color:#fff;
            font-size:<?php echo $widthHeight['toSumUpTitleSize'] ?>px
        }
        .toSumUpPostion{
            color:#fff;
            position: absolute;
            left:<?php echo $widthHeight['toSumUpPostionLeft'] ?>px;
            top:<?php echo $widthHeight['toSumUpPostionTop'] ?>px;
            width:<?php echo $widthHeight['toSumUpPostionWidth'] ?>px;
            line-height:<?php echo $widthHeight['lineHeight']?>px
        }



        .physical-growth-dev{
            background: url('http://m1.files.ivykids.cn/images/sreport2/physical-growth-dev.jpg') no-repeat center 20px, url('http://m1.files.ivykids.cn/images/sreport2/footer-02.jpg') no-repeat center bottom;
            background-size: 100%;
            background-color: #ffffff;
        }
        .fitness-test{
            background: url('http://m1.files.ivykids.cn/images/sreport2/header-01.jpg') no-repeat center top, url('http://m1.files.ivykids.cn/images/sreport2/footer-fitness.jpg') no-repeat center bottom;
            background-size: 100%;
            background-color: #ffffff;
        }
        .p2-title{
            font-size: 18px;
            color: #fff;
            padding-top: 65px;
            padding-left: 20px;
            padding-right: 100px;
            font-weight: bold;
        }
        .p2-text-en{
            padding: 0 144px 40px 60px;
        }
        .p2-text-en p{
            color: #fff;
        }
        .p2-text-cn{
            padding: 0 25px;
        }
        .p3-title{
            font-size: 22px;
            padding: 30px 0;
            text-align: center;
        }
        .p3-text{
            font-size: 15px;
            padding: 20px 25px 0 25px;
        }

        .ld-box1{
            padding: 60px 25px 0 25px;
        }
        .ld-box1 .box1-title{
            font-size: 15px;
            text-align: center;
            font-weight: bold;
        }
        .ld-box1 .box1-image{
            padding-bottom: 10px;
        }

        .ld-box2{
            padding: 60px 25px 0 25px;
        }
        .ld-box2 .box2-image{
            padding-bottom: 2px;
        }
        .ld-box2 table.box2-table{
            border: 1px;
            background-color: #000;
        }
        .ld-box2 table.box2-table td{
            background-color: #fff;
            padding: 5px;
        }

        .cd-signature{
            position: absolute;
            right: 60px;
            bottom: 120px;
            text-align: right;
            font-size: 12px;
        }

        .report-wrapper{
            width:<?php echo $widthHeight['pageWidth'] ?>px; 
            margin: 0px auto;
            padding: 0; 
            font-size:<?php echo $widthHeight['bodyfontSize'] ?>px;
            
        }
        .logo-contact-left .phone{font-size:18px;display:block;}
        .logo-contact-left .url{font-size:12px;}


        span.ld{
            color: #000;
            font-size: 110%;
            padding: 1px 3px;
            margin-right: 4px;
            background: #efefef;
        }

        span.ld:after{
        }

        hr.sep{
            margin: 5em 0 3em;
            border: 0;
            height: 1px;
            background: #333;
            background-image: -webkit-linear-gradient(left, #efefef, #ccc, #efefef);
            background-image:    -moz-linear-gradient(left, #efefef, #999, #ccc);
            background-image:     -ms-linear-gradient(left, #efefef, #999, #ccc);
            background-image:      -o-linear-gradient(left, #efefef, #999, #ccc);
        }

        ul.mission li{line-height: 1.5em;}
        /*.web-view .report-page{margin: 1em 0;}*/

        .page-break-after{page-break-after: always;}
        /* img.middle-size{
            max-width: 470px;
            max-height: 360px;
            margin: 10px auto;
            display: block;
            border: 4px solid #efefef;
        } */
        @media print {
            /**.web-view .report-page {background: none;}**/
            /*.web-view .report-page{margin: 0;}*/
        }
        .navbar-inverse {
            background-color: #333333;
            border-color: #efefef;
        }
        .navbar-inverse form {
            line-height: 50px;
            height: 50px;
            text-align: center;
        }
        .navbar-inverse form input {
            margin-top: 13px;
        }
        .navbar-fixed-top {
            top: 0;
            position: fixed;
            right: 0;
            left: 0;
            z-index: 1030;
            -webkit-transform: translate3d(0, 0, 0);
            transform: translate3d(0, 0, 0);
        }
        .navbar-inverse a{
            color: #f2f2f2;
        }
    </style>
</body>
</html>
