<?php

/**
 * This is the model class for table "ivy_event_member_import".
 *
 * The followings are the available columns in table 'ivy_event_member_import':
 * @property integer $id
 * @property integer $event_id
 * @property string $flag
 * @property integer $total
 * @property integer $success
 * @property integer $fail
 * @property integer $timestamp
 * @property integer $uid
 */
class EventMemberImport extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return EventMemberImport the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_event_member_import';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('event_id, flag, total, success, fail, timestamp, uid', 'required'),
			array('event_id, total, success, fail, timestamp, uid', 'numerical', 'integerOnly'=>true),
			array('flag', 'length', 'max'=>255),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, event_id, flag, total, success, fail, timestamp, uid', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'event_id' => 'Event',
			'flag' => 'Flag',
			'total' => 'Total',
			'success' => 'Success',
			'fail' => 'Fail',
			'timestamp' => 'Timestamp',
			'uid' => 'Uid',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('event_id',$this->event_id);
		$criteria->compare('flag',$this->flag,true);
		$criteria->compare('total',$this->total);
		$criteria->compare('success',$this->success);
		$criteria->compare('fail',$this->fail);
		$criteria->compare('timestamp',$this->timestamp);
		$criteria->compare('uid',$this->uid);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}