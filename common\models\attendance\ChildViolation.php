<?php

/**
 * This is the model class for table "ivy_child_violation".
 *
 * The followings are the available columns in table 'ivy_child_interview':
 * @property integer $id
 * @property integer $child_id
 * @property string $school_id
 * @property integer $class_id
 * @property string $type
 * @property string $vacation_reason
 * @property integer $uid
 * @property integer $updata_time
 */
class ChildViolation extends CActiveRecord
{
    const DRESS = 10;     // 着装违规


    /**
     * @return string the associated database table name
     */
    public function tableName()
    {
        return 'ivy_child_violation';
    }

    /**
     * @return array validation rules for model attributes.
     */
    public function rules()
    {
        // NOTE: you should only define rules for those attributes that
        // will receive user inputs.
        return array(
            array('child_id, school_id, type, uid, updata_time', 'required'),
            array('child_id, class_id, uid, updata_time', 'numerical', 'integerOnly'=>true),
            array('school_id', 'length', 'max'=>255),
            // The following rule is used by search().
            // @todo Please remove those attributes that should not be searched.
            array('id, child_id, school_id, class_id, type, , uid, updata_time', 'safe', 'on'=>'search'),
        );
    }

    /**
     * @return array relational rules.
     */
    public function relations()
    {
        // NOTE: you may need to adjust the relation name and the related
        // class name for the relations automatically generated below.
        return array(
            'childProfile' => array(self::BELONGS_TO, 'ChildProfileBasic', 'child_id'),
            'user' => array(self::BELONGS_TO, 'User', 'uid'),
            'ivyclass' => array(self::BELONGS_TO, 'IvyClass', array('class_id' => 'classid')),
            'tracks' => array(self::HAS_MANY, 'ChildVacationTrack', 'vid'),
        );
    }

    /**
     * @return array customized attribute labels (name=>label)
     */
    public function attributeLabels()
    {
        return array(
            'id' => 'ID',
            'child_id' =>  Yii::t("campus",'Students on leave/tardy'),
            'school_id' =>  Yii::t("campus",'school_id'),
            'class_id' =>  Yii::t("campusreport",'classid'),
            'type' =>  Yii::t("campus",'Type of Leave/tardy'),
            'uid' =>  Yii::t("labels",'Operator'),
            'updata_time' => Yii::t('global', 'Update Time'),
        );
    }

    /**
     * Retrieves a list of models based on the current search/filter conditions.
     *
     * Typical usecase:
     * - Initialize the model fields with values from filter form.
     * - Execute this method to get CActiveDataProvider instance which will filter
     * models according to data in model fields.
     * - Pass data provider to CGridView, CListView or any similar widget.
     *
     * @return CActiveDataProvider the data provider that can return the models
     * based on the search/filter conditions.
     */
    public function search()
    {
        // @todo Please modify the following code to remove attributes that should not be searched.

        $criteria=new CDbCriteria;

        $criteria->compare('id',$this->id);
        $criteria->compare('child_id',$this->child_id);
        $criteria->compare('school_id',$this->school_id,true);
        $criteria->compare('class_id',$this->class_id);
        $criteria->compare('type',$this->type,true);
        $criteria->compare('uid',$this->uid);
        $criteria->compare('updata_time',$this->updata_time);

        return new CActiveDataProvider($this, array(
            'criteria'=>$criteria,
        ));
    }



    /**
     * Returns the static model of the specified AR class.
     * Please note that you should have this exact method in all your CActiveRecord descendants!
     * @param string $className active record class name.
     * @return ChildInterview the static model class
     */
    public static function model($className=__CLASS__)
    {
        return parent::model($className);
    }

    public static function typeConf($type=0)
    {
        $conf = array(
            self::DRESS => Yii::t('attends', 'Uniform Infraction'),
        );
        if($type){
            return $conf[$type];
        }
        return $conf;
    }
    //获取违规数据
    public function getViolations($timestamp = '', $schoolid = '')
    {
        if (!$timestamp) {
            $timestamp = strtotime('today');
        }

        $ret = array();

        if($timestamp && $schoolid){
            $criteria = new CDbCriteria();
            $criteria->compare('school_id', $schoolid);
            $criteria->compare('updata_time', '<='.$timestamp);
            $criteria->compare('updata_time', '>='.$timestamp);
            $criteria->order = 'type DESC';
            $items = $this->findAll($criteria);
            foreach($items as $item){
                $ret[$item->child_id] = array(
                    'type' => $item->type,
                    'title' => self::typeConf($item->type),
                    'classid' => $item->class_id,
                    'child_id' => $item->child_id,
                    'name' => $item->childProfile->getChildName(),
                    'photo' => $item->childProfile->photo,
                    'uid'=>$item->uid,
                );
            }
        }

        return $ret;
    }
}
