<?php

class ScholarshipController extends BranchBasedController
{
    public $batchNum = 20;
    public $printFW = array();
    public $actionAccessAuths = array(
        'Index'             => 'o_A_Access',
    );

    public function createUrl($route, $params = array(), $ampersand = '&')
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        return parent::createUrl($route, $params, $ampersand);
    }

    public function init(){
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";
        Yii::import('common.models.support.*');
        Yii::import('common.models.calendar.*');
        $this->modernMenuFlag = 'campusOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site','Campus Workspace');
        $cs = Yii::app()->clientScript;
        $cs->registerCoreScript('jquery.ui');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/vue2.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/xlsx.full.min.js');
        $cs->registerCssFile($cs->getCoreScriptUrl().'/jui/css/base/jquery-ui.css');
        $cs->registerScriptFile($cs->getCoreScriptUrl().'/jui/js/jquery-ui.min.js');
        $baseDir = Yii::getPathOfAlias('common.extensions.ueditor.ueditor_full');
        $assets = Yii::app()->getAssetManager()->publish($baseDir, true, -1);
        $cs->registerScriptFile($assets.'/ueditor.teacher.config.js');
        $cs->registerScriptFile($assets.'/ueditor.all.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/clipboard.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/tinymce/tinymce.min.js');

        $this->branchSelectParams['urlArray'] = array('//mcampus/scholarship/scholarshipApplyList');

    }

    public function actionIndex()
    {
        $criteria = new CDbCriteria;
        $criteria->compare('branchId', $this->branchId);
        $criteria->compare('startyear', '>2019');
        $criteria->order = 'startyear desc';
        $found = CalendarSchool::model()->findAll($criteria);//校历ID

        $calendarInfo = array();
        $year = date('Y');
        if($found){
            foreach ($found as $val){
                $is_current = 0;
                $endYear = $val->startyear + 1;
                if ($val->startyear == $year) {
                    $is_current = 1;
                }
                $calendarInfo[] = array(
                    'yid' => $val->yid,
                    'startyear' => $val->startyear,
                    'title' => $val->startyear . ' - ' . $endYear,
                    'is_current' => $is_current,
                );
            }
        }

        $this->render('index', array(
            'calendarInfo' => $calendarInfo,
        ));
    }

    public function actionFindScholarShip()
    {
        $data = array('currentPage' => 0, 'total' => 0, 'info' => array());
        if(Yii::app()->request->isPostRequest){
            $schoolYear = Yii::app()->request->getParam('schoolYear', '');
            $yid = Yii::app()->request->getParam('yid', '');
            $page = Yii::app()->request->getParam('page', 1);
            $pageNum = Yii::app()->request->getParam('pageNum', '');
            if(!$schoolYear || !$yid){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', "学年不可为空！");
                $this->showMessage();
            }
            $criteria = new CDbCriteria;
            $criteria->compare('schoolid', $this->branchId);
            $criteria->compare('startyear', $schoolYear);
            $criteria->compare('status', Scholarship::STATS_ACTIVE);
            $criteria->compare('yid', $yid);
            $total = Scholarship::model()->count($criteria);

            $batchNum = $pageNum ? $pageNum: $this->batchNum;
            $data['total'] = ($total) ? ceil($total / $batchNum) : 0;
            $data['currentPage'] = $page;
            $data['pageNum'] = $batchNum;
            $offset = ($page - 1) * $batchNum;
            $criteria->limit = $batchNum; //取1条数据，如果小于0，则不作处理
            $criteria->offset = $offset; //两条合并起来，则表示 limit 10 offset 1,或者代表了。limit 1,10
            $criteria->order = 'created_at DESC';
            $scholarshiModel = Scholarship::model()->findAll($criteria);

            if($scholarshiModel){
                foreach ($scholarshiModel as $val){
                    $parents[$val->fid] = $val->fid;
                    $parents[$val->mid] = $val->mid;
                    $student = json_decode($val->student,true);
                    if($student){
                        foreach ($student as $item){
                            $childsClassids[$item['chilid']] = $item['classid'];
                        }
                    }
                }

                // 父母信息
                $criteria = new CDbCriteria;
                $criteria->compare('uid', $parents);
                $criteria->index = 'uid';
                $userModel = User::model()->findAll($criteria);

                // 孩子信息
                $criteria = new CDbCriteria;
                $criteria->compare('childid', array_keys($childsClassids));
                $criteria->index = 'childid';
                $childModel = ChildProfileBasic::model()->findAll($criteria);

                // 班级信息
                $criteria = new CDbCriteria;
                $criteria->compare('classid', $childsClassids);
                $criteria->index = 'classid';
                $classModel = IvyClass::model()->findAll($criteria);
                $scholarshiStatusArr = Scholarship::getStatus();
                foreach ($scholarshiModel as $item){
                    $childInfo = array();
                    if($item->student){
                        $student = json_decode($item->student);
                        foreach ($student as $child){
                            $childInfo[] = array(
                                'childid' => $child->chilid,
                                'name' => isset($childModel) && isset($childModel[$child->chilid]) ? $childModel[$child->chilid]->getChildName() : $child->chilid,
                                'birthdate' => isset($childModel) && isset($childModel[$child->chilid]) ? $childModel[$child->chilid]->birthday_search : $child->chilid,
                                'grade' => isset($classModel) && isset($classModel[$child->classid]) ? $classModel[$child->classid]->title : $child->classid,
                            );
                        }
                    }

                    $avatarUrl = array();
                    if($item->attach){
                        $avatarUrl = json_decode($item->attach);
                    }
                    $data['info'][] = array(
                        'id' => intval($item->id),
                        'childInfo' => $childInfo,
                        'fName' => (isset($userModel) && isset($userModel[$item->fid])) ? $userModel[$item->fid]->getName() : '',
                        'fPhone' => (isset($userModel) && isset($userModel[$item->fid])) ? $userModel[$item->fid]->parent->mphone : '',
                        'fEmail' => (isset($userModel) && isset($userModel[$item->fid])) ? $userModel[$item->fid]->email : '',
                        'mName' => (isset($userModel) && isset($userModel[$item->mid])) ? $userModel[$item->mid]->getName() : '',
                        'mPhone' => (isset($userModel) && isset($userModel[$item->mid])) ? $userModel[$item->mid]->parent->mphone : '',
                        'mEmail' => (isset($userModel) && isset($userModel[$item->mid])) ? $userModel[$item->mid]->email : '',
                        'Reason' => isset($item->reason) ? nl2br($item->reason) : '',
                        'status' => isset($scholarshiStatusArr) && isset($scholarshiStatusArr[$item->status]) ? $scholarshiStatusArr[$item->status] :intval($item->status),
                        'attach' => $avatarUrl
                    );
                }
            }
        }

        $this -> addMessage('data', $data);
        $this -> addMessage('state', 'success');
        $this -> addMessage('callback', 'cbsuccess');
        $this -> addMessage('message', Yii::t('message','Data saved!'));
        $this -> showMessage();
    }


   /* public function actionFindScholarShipT()
    {
        $data = array('currentPage' => 0, 'total' => 0, 'info' => array());
        if(Yii::app()->request->isPostRequest){
            $schoolYear = Yii::app()->request->getParam('schoolYear', '');
            $page = Yii::app()->request->getParam('page', 1);
            $yid = Yii::app()->request->getParam('yid', '');
            $pageNum = Yii::app()->request->getParam('pageNum', '');
            if(!$schoolYear || !$yid){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', "学年不可为空！");
                $this->showMessage();
            }
            $criteria = new CDbCriteria;
            $criteria->compare('schoolid', $this->branchId);
            $criteria->compare('startyear', $schoolYear);
            $criteria->compare('yid', $yid);
            $criteria->compare('status', Scholarship::STATS_ACTIVE);
            $total = Scholarship::model()->count($criteria);

            $batchNum = $pageNum ? $pageNum: $this->batchNum;
            $data['total'] = ($total) ? ceil($total / $batchNum) : 0;
            $data['pageNum'] = $batchNum;
            $data['currentPage'] = $page;
            $offset = ($page - 1) * $batchNum;
            $criteria->limit = $batchNum; //取1条数据，如果小于0，则不作处理
            $criteria->offset = $offset; //两条合并起来，则表示 limit 10 offset 1,或者代表了。limit 1,10
            $criteria->order = 'created_at DESC';
            $scholarshiModel = Scholarship::model()->findAll($criteria);

            if($scholarshiModel){
                foreach ($scholarshiModel as $val){
                    $parents[$val->fid] = $val->fid;
                    $parents[$val->mid] = $val->mid;
                    $student = json_decode($val->student,true);
                    if($student){
                        foreach ($student as $item){
                            $avatarUrl = array();
                            if($val->attach){
                                $avatarUrl = json_decode($val->attach);
                            }
                            $childsClassids[$item['chilid']] = $item['classid'];
                            $scholarsData[] = array(
                                'childid' => $item['chilid'],
                                'classid' => $item['classid'],
                                'fid' => $val->fid,
                                'mid' => $val->mid,
                                'reason' => $val->reason,
                                'attach' => $avatarUrl,
                            );
                        }
                    }
                }

                // 父母信息
                $criteria = new CDbCriteria;
                $criteria->compare('uid', $parents);
                $criteria->index = 'uid';
                $userModel = User::model()->findAll($criteria);

                // 孩子信息
                $criteria = new CDbCriteria;
                $criteria->compare('childid', array_keys($childsClassids));
                $criteria->index = 'childid';
                $childModel = ChildProfileBasic::model()->findAll($criteria);

                // 班级信息
                $criteria = new CDbCriteria;
                $criteria->compare('classid', $childsClassids);
                $criteria->index = 'classid';
                $classModel = IvyClass::model()->findAll($criteria);

                if($scholarsData){
                    foreach ($scholarsData as $item){
                        $data['info'][] = array(
                            'childid' => $item['childid'],
                            'name' => isset($childModel) && isset($childModel[$item['childid']]) ? $childModel[$item['childid']]->getChildName() : $item['childid'],
                            'birthdate' => isset($childModel) && isset($childModel[$item['childid']]) ? $childModel[$item['childid']]->birthday_search : $item['childid'],
                            'grade' => isset($classModel) && isset($classModel[$item['classid']]) ? $classModel[$item['classid']]->title : $item['classid'],
                            'fName' => (isset($userModel) && isset($userModel[$item['fid']])) ? $userModel[$item['fid']]->getName() : $item['fid'],
                            'fPhone' => (isset($userModel) && isset($userModel[$item['fid']])) ? $userModel[$item['fid']]->parent->mphone : $item['fid'],
                            'fEmail' => (isset($userModel) && isset($userModel[$item['fid']])) ? $userModel[$item['fid']]->email : $item['fid'],
                            'mName' => (isset($userModel) && isset($userModel[$item['mid']])) ? $userModel[$item['mid']]->getName() : $item['mid'],
                            'mPhone' => (isset($userModel) && isset($userModel[$item['mid']])) ? $userModel[$item['mid']]->parent->mphone : $item['mid'],
                            'mEmail' => (isset($userModel) && isset($userModel[$item['mid']])) ? $userModel[$item['mid']]->email : $item['mid'],
                            'Reason' => isset($item['reason']) ? nl2br($item['reason']) : '',
                            'attach' => $item['attach']
                        );
                    }
                }
            }
        }

        $this -> addMessage('data', $data);
        $this -> addMessage('state', 'success');
        $this -> addMessage('callback', 'cbsuccess');
        $this -> addMessage('message', Yii::t('message','Data saved!'));
        $this -> showMessage();
    }*/

    // OSS 文件跳转链接
    public function actionOssfileRedit($filePath, $space = '')
    {
        if ($filePath) {
            $oss = CommonUtils::initOSS('private');
            if($space){
                $filePath = $space . $filePath;
            }
            $url = $oss->get_sign_url($filePath);
            Yii::app()->request->redirect($url);
        }
        return false;
    }

    // 修改申请
    public function actionUpdate()
    {
        $id = Yii::app()->request->getParam('id');
        $reason = Yii::app()->request->getParam('reason');
        $attach = Yii::app()->request->getParam('attach', array());

        $this->addMessage('state', 'fail');
        if (!$id) {
            $this->addMessage('message', 'id not null');
            $this->showMessage();
        }
        if (!$reason) {
            $this->addMessage('message', 'reason not null');
            $this->showMessage();
        }
        $model = Scholarship::model()->findByPk($id);
        if (!$model) {
            $this->addMessage('message', 'not found');
            $this->showMessage();
        }
        $files = CUploadedFile::getInstancesByName('file');
        $model->reason = $reason;
        if (!$attach && !$files) {
            $this->addMessage('message', 'attach not null');
            $this->showMessage();
        }
        // 上传新文件
        if ($files) {
            // 上传文件到oss
            $oss = CommonUtils::initOSS('private');
            $objectPath = 'scholarship/' . date('Ym') . '/';
            foreach ($files as $file) {
                $id = uniqid();
                $newFileName = $id .'.'. $file->getExtensionName();
                if ($oss->uploadFile($objectPath . $newFileName, $file->getTempName())) {
                    $attach[] = $objectPath . $newFileName;
                }
            }
        }
        $model->attach = json_encode($attach);
        if (!$model->save()) {
            $error = $model->getErrors();
            $this->addMessage('message', $error[0]);
            $this->showMessage();
        }
        $this->addMessage('state', 'success');
        $this->addMessage('callback', 'cbSuccess');
        $this->addMessage('message', 'success');
        $this->showMessage();
    }

    // 修改当前奖学金状态
    public function actionDelScholarship()
    {
        if(Yii::app()->request->isPostRequest){
            $id = Yii::app()->request->getParam('id', '');
            $status = Yii::app()->request->getParam('status', 99);
            if(!$id){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', "参数不可为空！");
                $this->showMessage();
            }
            $scholarshiModel = Scholarship::model()->findByPk($id);
            if(!$scholarshiModel) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', "数据错误！");
                $this->showMessage();
            }
            if ($scholarshiModel->schoolid != $this->branchId) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', "非法操作！");
                $this->showMessage();
            }

            $scholarshiModel->status = $status;
            if(!$scholarshiModel->save()){
                $this->addMessage('state', 'fail');
                $err = current($scholarshiModel->getErrors());
                $this->addMessage('message', $err[0]);
                $this->showMessage();
            }
            $this -> addMessage('state', 'success');
            $this -> addMessage('callback', 'cbsuccess');
            $this -> addMessage('message', Yii::t('message','Success'));
            $this -> showMessage();
        }
        $this -> addMessage('state', 'fail');
        $this -> addMessage('message', Yii::t('message','参数未找到!'));
        $this -> showMessage();
    }
    public function actionPrint($id)
    {
        $this->layout = '//layouts/print';
        $this->printFW = $this->branchObj->getPrintHeader();
        $scholarshiModel = Scholarship::model()->findByPk($id);

        if($scholarshiModel){
            $parents[$scholarshiModel->fid] = $scholarshiModel->fid;
            $parents[$scholarshiModel->mid] = $scholarshiModel->mid;
            $student = json_decode($scholarshiModel->student,true);
            if($student){
                foreach ($student as $item){
                    $childsClassids[$item['chilid']] = $item['classid'];
                }
            }

            // 父母信息
            $criteria = new CDbCriteria;
            $criteria->compare('uid', $parents);
            $criteria->index = 'uid';
            $userModel = User::model()->findAll($criteria);

            // 孩子信息
            $criteria = new CDbCriteria;
            $criteria->compare('childid', array_keys($childsClassids));
            $criteria->index = 'childid';
            $childModel = ChildProfileBasic::model()->findAll($criteria);

            // 班级信息
            $criteria = new CDbCriteria;
            $criteria->compare('classid', $childsClassids);
            $criteria->index = 'classid';
            $classModel = IvyClass::model()->findAll($criteria);
            $childInfo = array();
            if($scholarshiModel->student){
                $student = json_decode($scholarshiModel->student);
                foreach ($student as $child){
                    $childInfo[] = array(
                        'childid' => $child->chilid,
                        'name' => isset($childModel) && isset($childModel[$child->chilid]) ? $childModel[$child->chilid]->getChildName() : $child->chilid,
                        'birthdate' => isset($childModel) && isset($childModel[$child->chilid]) ? $childModel[$child->chilid]->birthday_search : $child->chilid,
                        'grade' => isset($classModel) && isset($classModel[$child->classid]) ? $classModel[$child->classid]->title : $child->classid,
                    );
                }
            }

            $data = array(
                'id' => intval($scholarshiModel->id),
                'childInfo' => $childInfo,
                'fName' => (isset($userModel) && isset($userModel[$scholarshiModel->fid])) ? $userModel[$scholarshiModel->fid]->getName() : '',
                'fPhone' => (isset($userModel) && isset($userModel[$scholarshiModel->fid])) ? $userModel[$scholarshiModel->fid]->parent->mphone : '',
                'fEmail' => (isset($userModel) && isset($userModel[$scholarshiModel->fid])) ? $userModel[$scholarshiModel->fid]->email : '',
                'mName' => (isset($userModel) && isset($userModel[$scholarshiModel->mid])) ? $userModel[$scholarshiModel->mid]->getName() : '',
                'mPhone' => (isset($userModel) && isset($userModel[$scholarshiModel->mid])) ? $userModel[$scholarshiModel->mid]->parent->mphone : '',
                'mEmail' => (isset($userModel) && isset($userModel[$scholarshiModel->mid])) ? $userModel[$scholarshiModel->mid]->email : '',
                'reason' => isset($scholarshiModel->reason) ? nl2br($scholarshiModel->reason) : '',
            );
            $this->render('print', array(
                'data' => $data,
            ));
        }
    }


    // 奖学金前台文字配置
    public function actionIntroduceList()
    {
        $this->branchSelectParams['urlArray'] = array('//mcampus/scholarship/introduceList');
        $criteria = new CDbCriteria;
        $criteria->compare('schoolid', $this->branchId);
        $criteria->compare('status', "<>99");
        $dataProvider = new CActiveDataProvider('ScholarshipIntro', array(
            'criteria'=>$criteria,
        ));

        $this->render('scholarshipList',array(
            'dataProvider'=>$dataProvider,
        ));
    }

    // 增加某学年得奖学金介绍
    public function actionUpdateYear()
    {
        $id = Yii::app()->request->getParam('id', '');
        $startyear = Yii::app()->request->getParam('startyear', '');

        $model = ScholarshipIntro::model()->findByPk($id);
        if(!$model){
            $model = new ScholarshipIntro();
        }

        if($startyear){
            $criteria = new CDbCriteria;
            $criteria->compare('schoolid', $this->branchId);
            $criteria->compare('startyear', $startyear);
            $criteria->compare('status', 1);
            $count = ScholarshipIntro::model()->count($criteria);
            if($count){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '所选学年已增加，请先删除或者作废在操作');
                $this->showMessage();
            }

            $model->startyear = $startyear;
            $model->schoolid = $this->branchId;
            $model->status = 1;
            $model->created_at = time();
            $model->created_by = $this->staff->uid;
            $model->updated_at = time();
            $model->updated_by = $this->staff->uid;
            if(!$model->save()){
                $this->addMessage('state', 'fail');
                $err = current($model->getErrors());
                $this->addMessage('message', $err ? $err[0] : '失败');
                $this->showMessage();
            }
            $this->addMessage('state', 'success');
            $this->addMessage('message', Yii::t('message','success'));
            $this->addMessage('callback', 'cbSuccess');
            $this->showMessage();
        }
        $yearList = $this->getYear($this->branchId);
        $this->renderPartial('_updateYear', array(
            'model' => $model,
            'yearList' => $yearList,
        ));
    }

    // 作废奖学金介绍
    public function actionDelIntro()
    {
        $id = Yii::app()->request->getParam('id', '');
        $model = ScholarshipIntro::model()->findByPk($id);

        if($model){
            $model->status = 99;
            $model->updated_at = time();
            $model->updated_by = $this->staff->uid;
            if(!$model->save()){
                $this->addMessage('state', 'fail');
                $err = current($model->getErrors());
                $this->addMessage('message', $err ? $err[0] : '失败');
                $this->showMessage();
            }
            $this->addMessage('state', 'success');
            $this->addMessage('message', Yii::t('message','success'));
            $this->addMessage('callback', 'cbSuccess');
            $this->showMessage();
        }
    }

    // 显示编辑介绍列表
    public function actionUpdateScholarship()
    {
        $this->branchSelectParams['urlArray'] = array('//mcampus/scholarship/updateScholarship');
        $id = Yii::app()->request->getParam('id', '');
        $type = Yii::app()->request->getParam('type', '');

        $model = ScholarshipIntro::model()->findByPk($id);
        $view = '_update';
        $config = ScholarshipIntro::getScholarshipConfig();
        if($type != 100){
            $view = '_detailed';
            $scholarshipForm = new ScholarshipForm();
            $itemInfo = 'item' . $type . '_info';
            if($model->$itemInfo){
                $info = json_decode($model->$itemInfo);
                $scholarshipForm->academic_cn = $info->academic_cn;
                $scholarshipForm->academic_en = $info->academic_en;
                $scholarshipForm->academic_form_cn = $info->academic_form_cn;
                $scholarshipForm->academic_form_en = $info->academic_form_en;
                $scholarshipForm->academic_student_cn = $info->academic_student_cn;
                $scholarshipForm->academic_student_en = $info->academic_student_en;
                /*$scholarshipForm->academic_Referrer_cn = $info->academic_Referrer_cn;
                $scholarshipForm->academic_Referrer_en = $info->academic_Referrer_en;*/
                $scholarshipForm->academic_report_cn = $info->academic_report_cn;
                $scholarshipForm->academic_report_en = $info->academic_report_en;
            }
        }

        $this->render($view, array(
            'scholarshipForm' => $scholarshipForm,
            'model' => $model,
            'type' => $type,
            'config' => $config[$this->branchId],
        ));
    }

    // 增加和修改介绍
    public function actionSave()
    {
        $id = Yii::app()->request->getParam('id', '');
        $type = Yii::app()->request->getParam('type', '');

        if(!$id && !$type){
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '参数错误');
            $this->showMessage();
        }
        if(Yii::app()->request->isPostRequest){
            $model = ScholarshipIntro::model()->findByPk($id);
            if($type == 100){
                $model->attributes = $_POST['ScholarshipIntro'];
                $model->updated_at = time();
                $model->updated_by = $this->staff->uid;
                if(!$model->intro_cn && !$model->intro_en){
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', '中英介绍都为必填，不可为空');
                    $this->showMessage();
                }
                if(!$model->save()){
                    $this->addMessage('state', 'fail');
                    $err = current($model->getErrors());
                    $this->addMessage('message', $err ? $err[0] : '失败');
                    $this->showMessage();
                }
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message','success'));
                $this->showMessage();
            }else{
                $Scholarship1Form = new ScholarshipForm();
                $Scholarship1Form->attributes = $_POST['ScholarshipForm'];
                if(!$Scholarship1Form->validate()){
                    $this->addMessage('state', 'fail');
                    $err = current($Scholarship1Form->getErrors());
                    $this->addMessage('message', $err ? $err[0] : '失败');
                    $this->showMessage();
                }
                $type = 'item' . $type . '_info';
                $model->$type = json_encode($Scholarship1Form->attributes);
                if(!$model->save()){
                    $this->addMessage('state', 'fail');
                    $err = current($model->getErrors());
                    $this->addMessage('message', $err ? $err[0] : '失败');
                    $this->showMessage();
                }
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message','success'));
                $this->showMessage();
            }
        }
    }

    // 家长申请数据列表
    public function actionScholarshipApplyList()
    {
        $username = Yii::app()->request->getParam('username', '');
        $type = Yii::app()->request->getParam('type', '');
        $year = Yii::app()->request->getParam('year', '');

        $criteria = new CDbCriteria;
        $criteria->compare('schoolid', $this->branchId);
        if($type)$criteria->compare('apply_type', $type);
        if(isset($year))$criteria->compare('startyear', $year);
        if($username){
            $criteria->addCondition("concat_ws('',first_name,last_name) like '%{$username}%' ");
        }
        $criteria->compare('status', 1);
        $dataProvider = new CActiveDataProvider('ScholarshipApply', array(
            'criteria'=>$criteria,
            'sort'=>array(
                'defaultOrder'=>'id DESC',
            ),
        ));

        $config = ScholarshipIntro::getScholarshipConfig();
        $applyType = array();
        foreach ($config[$this->branchId] as $key=>$val){
            if($key != 100){
                $applyType[$key] = $val['title'];
            }
        }

        $yearList = $this->getYear($this->branchId,1);
        $yearList[0] = Yii::t('attends', 'Not Assigned Year');
        $this->render('scholarshipApplylist', array(
            'dataProvider' => $dataProvider,
            'type' => $type,
            'yearList' => $yearList,
            'applyType' => $applyType,
            'year' => $year,
        ));
    }

    public function actionTransfer()
    {
        $id = Yii::app()->request->getParam('id', '');
        $model = ScholarshipApply::model()->findByPk($id);

        if($model){
            $schoolID = $model->schoolid == 'BJ_DS' ? 'BJ_SLT' : 'BJ_DS';
            $model->schoolid = $schoolID;
            $model->updated_at = time();
            $model->updated_by = $this->staff->uid;
            if(!$model->save()){
                $this->addMessage('state', 'fail');
                $err = current($model->getErrors());
                $this->addMessage('message', $err ? $err[0] : '失败');
                $this->showMessage();
            }
            $this->addMessage('state', 'success');
            $this->addMessage('message', Yii::t('message','success'));
            $this->addMessage('callback', 'cbSuccess');
            $this->showMessage();
        }
    }

    public function actionShowInfo()
    {
        $id = Yii::app()->request->getParam('id', '');
        $model = ScholarshipApply::model()->findByPk($id);
        if(Yii::app()->request->isPostRequest){
            $model->attributes = $_POST['ScholarshipApply'];
            $model->birthday = ($model->birthday) ? strtotime($model->birthday) : '';
            $model->attach = json_encode($_POST['files']);
            $model->updated_at = time();
            $model->updated_by = $this->staff->uid;
            if(!$model->save()){
                $err = current($model->getErrors());
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('message', $err[0]));
                $this->showMessage();
            }
            $this->addMessage('state', 'success');
            $this->addMessage('message', 'success');
            $this->addMessage('callback', 'cbSuccess');
            $this->showMessage();
        }
        $config = ScholarshipIntro::getScholarshipConfig();
        $applyType = $config[$this->branchId];

        $startyear = $this->getYear($this->branchId,1);

        $type = array();
        foreach ($applyType as $key=>$val){
            if($key != 100){
                $type[$key] = $val['title'];
            }
        }
        $model->birthday = date('Y-m-d', $model->birthday);
        $this->renderPartial('info', array(
            'model' => $model,
            'applyType' => $type,
            'startyear' => $startyear,
        ));
    }

    // 作废奖学金申请
    public function actionDelApply()
    {
        $id = Yii::app()->request->getParam('id', '');
        $model = ScholarshipApply::model()->findByPk($id);
        if($model){
            $model->status = 99;
            $model->updated_at = time();
            $model->updated_by = $this->staff->uid;
            if(!$model->save()){
                $this->addMessage('state', 'fail');
                $err = current($model->getErrors());
                $this->addMessage('message', $err ? $err[0] : '失败');
                $this->showMessage();
            }
            $this->addMessage('state', 'success');
            $this->addMessage('message', Yii::t('message','success'));
            $this->addMessage('callback', 'cbSuccess');
            $this->showMessage();
        }
    }

    public function getButtons($data)
    {
        echo CHtml::link(Yii::t('global', '编辑'), array('updateYear', 'id' => $data->id, "type"=>'introduction'), array('class' => 'J_modal btn btn-xs btn-info')) . ' ';
        echo CHtml::link(Yii::t('global', '操作'), array('updateScholarship', 'id' => $data->id, "type"=>100), array('class' => 'btn btn-xs btn-info', 'target' => "_blank")) . ' ';
        //echo CHtml::link(Yii::t('global', '无效'), array('save', 'id' => $data->id), array('class' => 'J_ajax_del btn btn-xs btn-danger', 'data-msg' => "确认作废吗?")) . ' ';
        echo CHtml::link(Yii::t('global', '删除'), array('delIntro', 'id' => $data->id), array('class' => 'J_ajax_del btn btn-xs btn-danger')) . ' ';
    }

    public function getApplyType($data)
    {
        $config = ScholarshipIntro::getScholarshipConfig();
        return $config[$data->schoolid][$data->apply_type]['title'];
    }

    public function getChildName($data)
    {
        return $data->last_name . ' ' . $data->first_name;
    }

    public function getButtun($data)
    {
        $title = '您确定转移到启明星（北皋）校区吗？';
        if($data->schoolid == 'BJ_DS'){
            $title = '您确定转移到启明星（三里屯）校区吗？';
        }

        echo CHtml::link(Yii::t('global', '转移'), array('transfer', 'id' => $data->id), array('class' => 'J_ajax_del btn btn-xs btn-success', 'data-msg' => $title)) . ' ';
        echo CHtml::link(Yii::t('global', '编辑'), array('showInfo', 'id' => $data->id), array('class' => 'J_modal btn btn-xs btn-info')) . ' ';
        echo CHtml::link(Yii::t('global', '打印'), array('printInfo', 'id' => $data->id), array('target' => "_blank", 'class' => 'btn btn-xs btn-primary')) . ' ';
        echo CHtml::link(Yii::t('global', '删除'), array('delApply', 'id' => $data->id), array('class' => 'J_ajax_del btn btn-xs btn-danger')) . ' ';
    }

    public function actionPrintInfo()
    {
        $this->layout = '//layouts/print';
        $this->printFW = $this->branchObj->getPrintHeader();

        $id = Yii::app()->request->getParam('id', '');
        $model = ScholarshipApply::model()->findByPk($id);
        $config = ScholarshipIntro::getScholarshipConfig();
        $typeConfig = $config[$this->branchId] ? $config[$this->branchId] : array();
        $info = array();
        if($model && $model->attach){
            $attach = json_decode($model->attach);
            $exts = array('.jpg','.png','.jpeg');
            $oss = CommonUtils::initOSS('private');
            foreach ($attach as $val){
                $ext = strrchr($val, '.');
                if(in_array($ext,$exts)){
                    $filePath = 'admissions/';
                    $url = $oss->get_sign_url($filePath. $val);
                    $info[] = $url;
                }
            }
        }

        $this->render('shipApplyPrint', array(
            'info' => $info,
            'config' => $typeConfig,
            'model' => $model,
        ));

    }

    public static function getYear($schoolid,$show_not_Assigned_Year=0)
    {
        $criteria = new CDbCriteria;
        $criteria->compare('branchid', $schoolid);
        $criteria->compare('startyear', ">=2019");
        $calendarModel = CalendarSchool::model()->findAll($criteria);

        $yearList = array();

        foreach ($calendarModel as $val){
            $year = $val->startyear + 1;
            $yearList[$val->startyear] = $val->startyear . ' - ' . $year;
        }
        if($show_not_Assigned_Year){
            $yearList[0] = Yii::t('attends', 'Not Assigned Year');
        }
        return $yearList;
    }

    public function actionRefundFiles()
    {
        $file = CUploadedFile::getInstanceByName('upload_file');
        $msg = '没有附件选中';
        $saveName = "";
        if ($file) {
            if ($file->size > 10*1024*1024) {
                $msg = '文件过大';
            } else{
                $needType = array('jpg','jpeg','png','pdf');
                if (!in_array($file->getExtensionName(), $needType)) {
                    $msg = '此文件类型不允许上传';
                }else{
                    $filePath = Yii::app()->params['xoopsVarPath'] . '/asa/refund/';

                    $ext = $file->getExtensionName();
                    $saveName = uniqid() . '_scholarship_'. '.' . $ext;
                    if ($file->saveAs($filePath . $saveName)){
                        $normalDir = rtrim($filePath . $saveName);
                        $oss = CommonUtils::initOSSCS('private');
                        $objectPath = 'admissions/' . date('Ym') . '/';
                        if ($oss->uploadFile($objectPath . $saveName, $normalDir)) {
                            unlink($normalDir);
                        }
                        $msg = 'success';
                        $baseUrl = $this->createUrl('ossfileRedit', array('filePath' => date('Ym', time()) . '/' . $saveName, 'space' => 'admissions/')) ;
                    }else{
                        $msg = '文件上传失败';
                    }
                }
            }
        }
        echo CJSON::encode(array(
            'url' => $baseUrl,
            'saveName' => date('Ym', time()) . '/' . $saveName,
            'msg' => $msg,
        ));
    }

    public static function getButtunFile($data)
    {
        if($data->attach){
            $attach = json_decode($data->attach);
            foreach ($attach as $val){
                echo CHtml::link(Yii::t('global', '文件'), array('ossfileRedit', 'filePath' => $val, 'space' => 'admissions/'), array('target' => "_blank", 'class' => 'btn btn-default btn-xs mb5')) . ' ';
            }
        }
    }

    // 导出家长信息
    public function actionExportInfo()
    {
        $type = Yii::app()->request->getParam('type', '');
        $year = Yii::app()->request->getParam('year', '');
        $criteria = new CDbCriteria;
        $criteria->compare('schoolid', $this->branchId);
        if($type)$criteria->compare('apply_type', $type);
        if($year)$criteria->compare('startyear', $year);
        $criteria->compare('status', 1);
        $modelApply = ScholarshipApply::model()->findAll($criteria);
        $data = array();
        Yii::import('common.models.calendar.*');
        Yii::import('common.models.schoolbus.*');
        $data['title'] = $this->branchId . "_奖学金数据.xlsx";
        $data['items'][0] = array('学年', '奖学金类型', '学生姓名', '出生日期', '当前学校',
            '就读时间', '申请年级1', '申请年级2', '申请理由', '家长邮箱', '家长电话',
            '班主任姓名', '班主任邮箱', '科任老师姓名', '科任老师邮箱','文件');
        if ($modelApply) {
            foreach ($modelApply as $i => $model) {
                $array = array();
                $array[] = trim($model->startyear);
                $array[] = trim($model->apply_type);
                $array[] = trim($model->first_name) . ' ' . trim($model->last_name);
                $array[] = date("Y-m-d", $model->birthday);
                $array[] = trim($model->current_school);
                $array[] = trim($model->study_time);
                $array[] = trim($model->apply_grade);
                $array[] = trim($model->apply_grade2);
                $array[] = trim($model->apply_reason);
                $array[] = trim($model->parent_email);
                $array[] = trim($model->parent_phone);
                $array[] = trim($model->head_teacher_name);
                $array[] = trim($model->head_teacher_email);
                $array[] = trim($model->class_teacher_name);
                $array[] = trim($model->class_teacher_email);
                $array[] = ($model->attach) ? '有' : '无' ;
                $data['items'][$i + 1] = $array;
            }
        }
        $this->addMessage('state', 'success');
        $this->addMessage('data', $data);
        $this->showMessage();
    }

    public function getMenu()
    {
        $mainMenu = array(
            array('label'=>Yii::t('','官网奖学金申请列表'), 'url'=>array("/mcampus/scholarship/scholarshipApplyList")),
            array('label'=>Yii::t('','官网奖学金页面配置'), 'url'=>array("/mcampus/scholarship/introduceList")),
        );
        return $mainMenu;
    }
}

