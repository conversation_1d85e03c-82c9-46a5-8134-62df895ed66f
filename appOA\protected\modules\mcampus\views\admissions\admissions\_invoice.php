<?php
    Yii::import('application.components.policy.*');
    Yii::import('common.models.wxpay.*');
    $policyApi = new PolicyApi($this->branchId);
    $bankModel = new Banktransfer;
    // 查找账单
    $criteria = new CDbCriteria;
    $criteria->compare('schoolid', $this->branchId);
    $criteria->compare('admission_id', $child->id);
    $criteria->compare('status', Invoice::STATS_UNPAID);
    $invoiceModel = Invoice::model()->find($criteria);
    if (!$invoiceModel) {
        $invoiceModel = new Invoice;
    } else{
        $user = User::model()->findByPk($invoiceModel->userid);
    }
    // 查找所有相关账单
    $criteria = new CDbCriteria;
    $criteria->compare('schoolid', $this->branchId);
    $criteria->compare('admission_id', $child->id);
    $criteria->compare('status', '>' . Invoice::STATS_UNPAID);
    $invoiceModels = Invoice::model()->findAll($criteria);

 ?>
<div class="modal-body">
    <!-- 账单详情 -->
    <table class="table">
        <thead>
            <th><?php echo  Yii::t('invoice','Invoice Title') ?></th>
            <th><?php echo  Yii::t('payment','Invoice Amount') ?></th>
            <th><?php echo  Yii::t('campus','生成日期') ?></th>
            <th><?php echo  Yii::t('campus','Invoice Status') ?></th>
            <th><?php echo  Yii::t('invoice','Created By') ?></th>
            <th><?php echo  Yii::t('curriculum','Operation') ?></th>
        </thead>
        <?php foreach ($invoiceModels as $invoice):?>
        <tr>
            <td><?php echo $invoice->title; ?></td>
            <td><?php echo $invoice->amount; ?></td>
            <td><?php echo date('Y-m-d', $invoice->timestamp); ?></td>
            <td><?php echo $policyApi->renderInvoiceStatus($invoice->status); ?></td>
            <td><?php echo User::model()->findByPk($invoice->userid)->getName(); ?></td>
            <td>
            </td>
        </tr>
        <?php endforeach; ?>
        <?php if(!$invoiceModel->isNewRecord): ?>
        <tr>
            <td><?php echo $invoiceModel->title; ?></td>
            <td><?php echo $invoiceModel->amount; ?></td>
            <td><?php echo date('Y-m-d', $invoiceModel->timestamp); ?></td>
            <td><?php echo $policyApi->renderInvoiceStatus($invoiceModel->status); ?></td>
            <td><?php echo $user->getName(); ?></td>
            <td>
                <?php 
                    if($invoiceModel->status == Invoice::STATS_UNPAID):
                        // 微信支付的异常记录
                        $criteria = new CDbCriteria;
                        $criteria->compare('invoice_id', $invoiceModel->invoice_id);
                        $items = WechatPayOrderItem::model()->findAll($criteria);
                        $orderid = array();
                        foreach ($items as $item) {
                            $orderid[] = $item->orderid;
                        }
                ?>
                <button type="button" class="btn btn-xs btn-primary" onclick="sendemail(this, <?php echo $invoiceModel->invoice_id ?>)">邮件</button>
                <button type="button" class="btn btn-xs btn-danger" onclick="invalid(this, <?php echo $invoiceModel->invoice_id ?>)">作废</button>
                <?php endif; ?>
            </td>
        </tr>
        <?php endif; ?>
    </table>

    <?php if(!$invoiceModel->isNewRecord): ?>
    <!-- 账单付款 -->
    <ul class="nav nav-tabs" role="tablist" style="margin-bottom: 15px">
      <li role="presentation" class="active"><a href="#cash" style="padding: 10px" role="tab" data-toggle="tab"><?php echo  Yii::t('payment','Cash Payment') ?></a></li>
      <li role="presentation"><a href="#pos" role="tab" style="padding: 10px" class="p10" data-toggle="tab"><?php echo Yii::t('management','POS payment') ?></a></li>
      <li role="presentation"><a href="#bank" role="tab" style="padding: 10px" class="p10" data-toggle="tab"><?php echo Yii::t('management','Bank transfer') ?></a></li>
      <li role="presentation"><a href="#wechat" role="tab" style="padding: 10px" class="p10" data-toggle="tab"><?php echo Yii::t('management','WeChat credit card payment') ?></a></li>
    </ul>

    <div class="tab-content">
        <!-- 现金支付 -->
        <div role="tabpanel" class="tab-pane active" id="cash">
            <?php $form=$this->beginWidget('CActiveForm', array(
                    'id'=>'cash-form',
                    'enableAjaxValidation'=>false,
                    'action'=>$this->createUrl('cashPay',array('invoiceId'=>$invoiceModel->invoice_id, 'childid'=>$child->id)),
                    'htmlOptions'=>array('class'=>'J_ajaxForm form-inline', 'role'=>'form'),
            ));?>
            <button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo  Yii::t('payment','Cash Payment') ?></button>
            <?php $this->endWidget(); ?>
        </div>

        <!-- 刷卡支付 -->
        <div role="tabpanel" class="tab-pane" id="pos">
            <button id="showcode" class="btn btn-primary"><?php echo Yii::t('management','Generate POS order ') ?></button>
            <div id="code"></div>
        </div>

        <!-- 银行转账 -->
        <div role="tabpanel" class="tab-pane" id="bank">
            <?php $form=$this->beginWidget('CActiveForm', array(
                    'id'=>'invoice-form',
                    'enableAjaxValidation'=>false,
                    'action'=>$this->createUrl('banktransfer', array('invoiceId'=>$invoiceModel->invoice_id, 'childid'=>$child->id)),
                    'htmlOptions'=>array('class'=>'J_ajaxForm form-inline', 'role'=>'form'),
            ));?>
            <div class="form-group">
                <label class="sr-only" for="exampleInputEmail2"><?php echo Yii::t('management','Transaction sequence number') ?></label>
                <?php echo CHtml::activeTextField($bankModel, 'id', array('class'=>'form-control','placeholder'=>Yii::t('management','Transaction sequence number')));?>
            </div>
            <div class="form-group">
                <label class="sr-only" for="exampleInputEmail2"><?php echo Yii::t('management','Bank receipt number') ?></label>
                <?php echo CHtml::activeTextField($bankModel, 'receiptid', array('class'=>'form-control','placeholder'=>Yii::t('management','Bank receipt number')));?>
            </div>
                <div class="form-group">
                <?php $this->widget('zii.widgets.jui.CJuiDatePicker',array(
                            'model'=>$bankModel,
                            'attribute'=>'btimestamp',
                            'options'=>array(
                                'dateFormat'=>'yy-mm-dd',
                            ),
                            'htmlOptions'=>array(
                                'class'=>'form-control',
                                'placeholder'=> Yii::t("management", 'Date of received payment'),
                            ),
                ));?>
                </div>
                    <div class="form-group">
                        <label class="sr-only" for="exampleInputEmail2">面试费</label>
                <?php echo CHtml::activeHiddenField($bankModel, 'title', array('class'=>'form-control','value'=>$child->cn_name . '面试费'));?>
                        </div>
                <button type="submit" class="btn btn-primary J_ajax_submit_btn">确认</button>
            <?php $this->endWidget(); ?>
        </div>

        <!-- 微信支付 -->
        <div role="tabpanel" class="tab-pane" id="wechat">
            <div class="row">
                <div class="col-lg-6">
                    <div class="input-group">
                        <span class="input-group-addon"><?php echo Yii::t("management", 'Please scan the authorization code')?></span>
                        <input type="text" id="auth_code" name="auth_code" class="form-control">
                    </div>
                </div>
                <div class="col-lg-6 confirm">
                    
                </div>
                <!-- 异常记录 -->
                <div class="col-lg-12">
                    <h5 ><?php echo Yii::t("management", 'Abnormal recording')?>：</h5>
                    <?php 
                        $wechatPayOrder = WechatPayOrder::model()->findAllByPk($orderid);
                        foreach ($wechatPayOrder as $order):
                            if ($order->type != 'MICROPAY') {
                                continue;
                            }
                     ?>
                    <button class="btn btn-default mb5" onclick="wechatConfirm('<?php echo $order->orderid; ?>')"><?php echo date('Y-m-d H:i:s', $order->order_time); ?></button>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>
<?php if(!$invoiceModel->isNewRecord): ?>
<div class="modal-footer">
    <button type="button" class="btn btn-default"  data-dismiss="modal"><?php echo Yii::t('global','Cancel');?></button>
</div>
<?php endif; ?>
<script>
    $("#showcode").click(function (e) {
        $('#code').html('<p><?php echo Yii::t('management','Creating order'); ?></p>');
        $('#code').load('<?php echo $this->createUrl('generalBarcode',array('invoiceId'=>$invoiceModel->invoice_id, 'childid'=>$child->id)); ?>');
    });

    $('#auth_code').bind('keypress',function(event){
        if(event.keyCode == "13")
        {
            if($(this).val() == ''){
                resultTip({msg: '<?php echo Yii::t('management','Please scan the authorization code first '); ?>', error: 1});
                $(this).focus();
            }
            else{
                var authCode = $('#auth_code').val();
                var childid = <?php echo $child->id; ?>;
                var invoiceId = '<?php echo $invoiceModel->invoice_id; ?>';
                $.ajax({
                    type: 'post',
                    dataType: 'json',
                    url: '<?php echo $this->createUrl('wxmicropay');?>',
                    data: {authCode:authCode, invoiceId:invoiceId, childid:childid},
                    success: function(data){
                        if(data.return_code == 'SUCCESS'){
                            if(data.result_code == 'SUCCESS'){
                                resultTip({msg: '付款成功', callback: function(){
                                    reloadPage(window);
                                }});
                            }
                            else{
                                if(data.err_code == 'AUTHCODEEXPIRE'){
                                    resultTip({msg: data.err_code_des, error: 1});
                                }
                                else if(data.err_code == 'USERPAYING'){
                                    showMicropay(data.out_trade_no);
                                }
                            }
                        }
                    },
                    complete: function(){
                        o=true;
                    }
                });
            }
            return false;
        }
    });

    // 查询账单状态
    function showMicropay(orderid) {
        var html = '<button onclick="wechatConfirm(' +orderid+ ')" class="btn btn-default"><?php echo Yii::t('management','Confirm payment'); ?></button>';
        $('.confirm').html(html);
    }

    function wechatConfirm(orderid) {
        $.ajax({
            type: 'post',
            dataType: 'json',
            url: '<?php echo $this->createUrl('showmicropay');?>',
            data: {orderid:orderid},
            success: function (data) {
                if (data.state == 'success') {
                    resultTip({msg: '<?php echo Yii::t('management','Payment success'); ?>', callback: function(){
                        reloadPage(window);
                    }});
                } else{
                    resultTip({msg: '<?php echo Yii::t('management','Payment failure'); ?>', error: 'warning'});
                }
            }

        });
    }

    // 作废账单
    function invalid(btn, invoiceId) {
        var btn = $(btn);
        btn.attr('disabled', 'disabled');
        $.ajax({
            type: 'post',
            dataType: 'json',
            url: '<?php echo $this->createUrl("invalidInvoice"); ?>',
            data: {invoiceId:invoiceId},
            success: function (data) {
                if (data.state == 'success') {
                    btn.parent().parent().remove();
                    resultTip({msg: '<?php echo Yii::t('management','The bill has been voided'); ?>', error: 'warning'});
                    cbSuccess();
                }
                btn.removeAttr('disabled');
            }

        });
    }

    // 发送邮件
    function sendemail(btn, invoiceId) {
        var btn = $(btn);
        btn.attr('disabled', 'disabled');
        $.ajax({
            type: 'post',
            dataType: 'json',
            url: '<?php echo $this->createUrl("invoiceEmail"); ?>',
            data: {invoiceId:invoiceId},
            success: function (data) {
                if (data.state == 'success') {
                    resultTip({msg: '<?php echo Yii::t('management','Email has been sent'); ?>', error: 'warning'});
                }else{
                    resultTip({msg: '<?php echo Yii::t('management',"The email can't be sent"); ?>', error: 'warning'});
                }
                btn.removeAttr('disabled');
            }

        });
    }
</script>
