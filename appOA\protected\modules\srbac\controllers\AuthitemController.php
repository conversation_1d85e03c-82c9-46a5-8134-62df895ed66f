<?php

/**
 * AuthitemController class file.
 *
 * <AUTHOR> <<EMAIL>>
 * @link http://code.google.com/p/srbac/
 */

/**
 * AuthitemController is the main controller for all of the srbac actions
 *
 * <AUTHOR> <<EMAIL>>
 * @package srbac.controllers
 * @since 1.0.0
 */
class AuthitemController extends SBaseController {

    public $dialogWidth = 600;
    /**
     * @var string specifies the default action to be 'list'.
     */
    public $defaultAction = 'frontpage';
    /**
     *  @var $breadcrumbs
     */
    public $breadcrumbs;
    /**
     * @var CActiveRecord the currently loaded data model instance.
     */
    private $_model;

    public function init() {
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'sysConfig';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site','System Management');
    }

    /**
     * Checks if the user has the authority role
     * @param String $action The current action
     * @return Boolean true if user has the authority role
     */
    public function beforeAction($action) {

        if (!$this->module->isInstalled() && $action->id != "install" && $this->module->debug) {
            $this->redirect(array("install"));
            return false;
        }

        if ($this->module->debug) {
            return true;
        }

        //if (Yii::app()->user->checkAccess(Helper::findModule('srbac')->superUser) ||
        //  !Helper::isAuthorizer()) {
        if (Yii::app()->user->checkAccess(Helper::findModule('srbac')->superUser) ) {
            return true;
        } else {
            parent::beforeAction($action);
        }
    }


    /**
     * Assigns roles to a user
     *
     * @param int $userid The user's id
     * @param String $roles The roles to assign
     * @param String $bizRules Not used yet
     * @param String $data Not used yet
     */
    private function _assignUser($userid, $roles, $bizRules, $data) {
        if ($userid) {
            $auth = Yii::app()->authManager;
            /* @var $auth CDbAuthManager */
            foreach ($roles as $role) {
                $auth->assign($role, $userid, $bizRules, $data);
            }
        }
    }

    /**
     * Displays srbac frontpage
     */
    public function actionFrontPage() {
        $this->render('frontpage', array());
    }

    /**
     * Revokes roles from a user
     * @param int $userid The user's id
     * @param String $roles The roles to revoke
     */
    private function _revokeUser($userid, $roles) {
        if ($userid) {
            $auth = Yii::app()->authManager;
            /* @var $auth CDbAuthManager */
            foreach ($roles as $role) {
                if ($role == $this->module->superUser) {
                    $count = Assignments::model()->count("itemname='" . $role . "'");
                    if ($count == 1) {
                        return false;
                    }
                }
                $auth->revoke($role, $userid);
                return true;
            }
        }
    }

    /**
     * Assigns child items to a parent item
     * @param String $parent The parent item
     * @param String $children The child items
     */
    private function _assignChild($parent, $children) {
        if ($parent) {
            $auth = Yii::app()->authManager;
            /* @var $auth CDbAuthManager */
            foreach ($children as $child) {
                $auth->addItemChild($parent, $child);
            }
        }
    }

    /**
     * Revokes child items from a parent item
     * @param String $parent The parent item
     * @param String $children The child items
     */
    private function _revokeChild($parent, $children) {
        if ($parent) {
            $auth = Yii::app()->authManager;
            /* @var $auth CDbAuthManager */
            foreach ($children as $child) {
                $auth->removeItemChild($parent, $child);
            }
        }
    }

    /**
     * 分配角色默认权限
     */
    public function actionAssignRole(){
        $this->render('assignRole');
    }

    public function actionAssignTask(){
        $authData = array();
        $itemTable = Yii::app()->authManager->itemTable;
        $rows = Yii::app()->db->createCommand()->select('*')->from($itemTable)->where('type<2')->queryAll();
        foreach($rows as $row){
            $authData['items'][$row['type']][$row['name']] = $row['description'];
        }

        $itemChildTable = Yii::app()->authManager->itemChildTable;
        $rows = Yii::app()->db->createCommand()->select('*')->where('parent != "'.$this->module->superUser.'" AND parent NOT LIKE "ivystaff%"')->from($itemChildTable)->queryAll();
        foreach($rows as $row){
            $authData['parent'][$row['child']][] = $row['parent'];
        }
        $this->render('assignTask', array('authData'=>$authData));
    }

    /**
     * The assignment action
     * First checks if the user is authorized to perform this action
     * Then initializes the needed variables for the assign view.
     * If there's a post back it performs the assign action
     */
    public function actionAssign() {
        //CVarDumper::dump($_POST, 5, true);
        $userid = isset($_POST[Helper::findModule('srbac')->userclass][$this->module->userid]) ?
            $_POST[Helper::findModule('srbac')->userclass][$this->module->userid] :
            "";

        //Init values
        $model = AuthItem::model();
        $data['userAssignedRoles'] = array();
        $data['userNotAssignedRoles'] = array();
        $data['roleAssignedTasks'] = array();
        $data['roleNotAssignedTasks'] = array();
        $data['taskAssignedOpers'] = array();
        $data['taskNotAssignedOpers'] = array();
        $data["assign"] = array("disabled" => true);
        $data["revoke"] = array("disabled" => true);
        $this->_setMessage("");

        /* @var $auth CDbAuthManager */
        $authItemAssignName = isset($_POST['AuthItem']['name']['assign']) ?
            $_POST['AuthItem']['name']['assign'] : "";


        $assBizRule = isset($_POST['Assignments']['bizrule']) ?
            $_POST['Assignments']['bizrule'] : "";
        $assData = isset($_POST['Assignments']['data']) ?
            $_POST['Assignments']['data'] : "";


        $authItemRevokeName = isset($_POST['AuthItem']['name']['revoke']) ?
            $_POST['AuthItem']['name']['revoke'] : "";

        if (isset($_POST['AuthItem']['name'])) {
            if (isset($_POST['AuthItem']['name'][0])) {
                $authItemName = $_POST['AuthItem']['name'][0];
            } else {
                $authItemName = $_POST['AuthItem']['name'];
            }
        }

        $assItemName = isset($_POST['Assignments']['itemname']) ? $_POST['Assignments']['itemname'] : "";

        $assignRoles = Yii::app()->request->getParam('assignRoles', 0);
        $revokeRoles = Yii::app()->request->getParam('revokeRoles', 0);
        $assignTasks = isset($_GET['assignTasks']) ? $_GET['assignTasks'] : 0;
        $revokeTasks = isset($_GET['revokeTasks']) ? $_GET['revokeTasks'] : 0;
        $assignOpers = isset($_GET['assignOpers']) ? $_GET['assignOpers'] : 0;
        $revokeOpers = isset($_GET['revokeOpers']) ? $_GET['revokeOpers'] : 0;

        $auth = Yii::app()->authManager;
        if(isset($_POST['tauth']) || isset($_POST['oauth'])){
            if(!$userid){
                Yii::app()->db->createCommand()
                    ->delete($auth->itemChildTable, 'parent=:parent', array(
                        ':parent'=>$authItemName
                    )) > 0;
            }
            $tasks = $_POST['tauth'] ? $_POST['tauth'] : array();
            $operations = $_POST['oauth'] ? $_POST['oauth'] : array();
            foreach($tasks+$operations as $to){
                if($userid){
                    if(!$auth->checkAccess($to, $userid)){
                        $auth->assign($to, $userid);
                    }
                }
                else{
                    if(!$auth->hasItemChild($authItemName, $to)){
                        $auth->addItemChild($authItemName, $to);
                    }
                }
            }
            $this->addMessage('state', 'success');
            $this->addMessage('message', 'OK!');
            $this->addMessage('callback', 'callback');
            $this->addMessage('data', array(''));
            $this->showMessage();
        }

        if ($assignRoles && is_array($authItemAssignName)) {

            $this->_assignUser($userid, $authItemAssignName, $assBizRule, $assData);
            $this->_setMessage(Helper::translate('srbac', 'Role(s) Assigned'));
        } else if ($revokeRoles && is_array($authItemRevokeName)) {
            $revoke = $this->_revokeUser($userid, $authItemRevokeName);
            if ($revoke) {
                $this->_setMessage(Helper::translate('srbac', 'Role(s) Revoked'));
            } else {
                $this->_setMessage(Helper::translate('srbac', 'Can\'t revoke this role'));
            }
        } else if ($assignTasks && is_array($authItemAssignName)) {
            $this->_assignChild($authItemName, $authItemAssignName);
            $this->_setMessage(Helper::translate('srbac', 'Task(s) Assigned'));
        } else if ($revokeTasks && is_array($authItemRevokeName)) {
            $this->_revokeChild($authItemName, $authItemRevokeName);
            $this->_setMessage(Helper::translate('srbac', 'Task(s) Revoked'));
        } else if ($assignOpers && is_array($authItemAssignName)) {
            $this->_assignChild($assItemName, $authItemAssignName);
            $this->_setMessage(Helper::translate('srbac', 'Operation(s) Assigned'));
        } else if ($revokeOpers && is_array($authItemRevokeName)) {
            $this->_revokeChild($assItemName, $authItemRevokeName);
            $this->_setMessage(Helper::translate('srbac', 'Operation(s) Revoked'));
        }
        //If not ajax show the assign page
        if (!Yii::app()->request->isAjaxRequest) {
            $this->render('assign', array(
                'model' => $model,
                'message' => $this->_getMessage(),
                'userid' => $userid,
                'data' => $data
            ));
        } else {
            $this->addMessage('state', 'success');
            $this->addMessage('message', 'OK!');
            $this->addMessage('callback', 'callback');
            $this->addMessage('data', array(''));
            $this->showMessage();
            // assign to user show the user tab
            if ($userid != "") {
                $this->_getTheRoles();
            } else if ($assignTasks != 0 || $revokeTasks != 0) {
                $this->_getTheTasks();
            } else if ($assignOpers != 0 || $revokeOpers != 0) {
                $this->_getTheOpers();
            }
        }
    }

    /**
     * Used by Ajax to get the roles of a user when he is selected in the Assign
     * roles to user tab
     */
    public function actionGetRoles() {
        $this->_setMessage("");
        $this->_getTheRoles();
    }

    /**
     * Gets the assigned and not assigned roles of the selected user
     */
    private function _getTheRoles() {
        $model = new AuthItem();
        $userid = $_POST[Helper::findModule('srbac')->userclass][$this->module->userid];
        $data['userAssignedRoles'] = Helper::getUserAssignedRoles($userid);
        $data['userNotAssignedRoles'] = Helper::getUserNotAssignedRoles($userid);
        if ($data['userAssignedRoles'] == array()) {
            $data['revoke'] = array("name" => "revokeUser", "disabled" => true);
        } else {
            $data['revoke'] = array("name" => "revokeUser");
        }
        if ($data['userNotAssignedRoles'] == array()) {
            $data['assign'] = array("name" => "assignUser", "disabled" => true);
        } else {
            $data['assign'] = array("name" => "assignUser");
        }
        $this->renderPartial('tabViews/userAjax',
            array('model' => $model, 'userid' => $userid, 'data' => $data, 'message' => $this->_getMessage()),
            false, true);
    }

    /**
     * Used by Ajax to get the tasks of a role when it is selected in the Assign
     * tasks to roles tab
     */
    public function actionGetTasks() {
        $this->_setMessage("");
        $this->_getTheTasks();
    }

    /**
     * Gets the assigned and not assigned tasks of the selected user
     */
    private function _getTheTasks() {
        $model = new AuthItem();
        $name = isset($_POST["AuthItem"]["name"][0]) ? $_POST["AuthItem"]["name"][0] : "";
        $data['roleAssignedTasks'] = Helper::getRoleAssignedTasks($name);
        $data['roleNotAssignedTasks'] = Helper::getRoleNotAssignedTasks($name);
        if ($data['roleAssignedTasks'] == array()) {
            $data['revoke'] = array("name" => "revokeTask", "disabled" => true);
        } else {
            $data['revoke'] = array("name" => "revokeTask");
        }
        if ($data['roleNotAssignedTasks'] == array()) {
            $data['assign'] = array("name" => "assignTasks", "disabled" => true);
        } else {
            $data['assign'] = array("name" => "assignTasks");
        }
        $this->renderPartial('tabViews/roleAjax',
            array('model' => $model, 'name' => $name, 'data' => $data, 'message' => $this->_getMessage()), false, true);
    }

    /**
     * Used by Ajax to get the operations of a task when he is selected in the Assign
     * operations to tasks tab
     */
    public function actionGetOpers() {
        $this->_setMessage("");
        $this->_getTheOpers();
    }

    /**
     * Gets the assigned and not assigned operations of the selected user
     */
    private function _getTheOpers() {
        $model = new AuthItem();
        $data['taskAssignedOpers'] = array();
        $data['taskNotAssignedOpers'] = array();
        $name = isset($_POST["Assignments"]["itemname"]) ?
            $_POST["Assignments"]["itemname"] :
            Yii::app()->getGlobalState("cleverName");
        if (Yii::app()->getGlobalState("cleverAssigning") && $name) {
            $data['taskAssignedOpers'] = Helper::getTaskAssignedOpers($name, true);
            $data['taskNotAssignedOpers'] = Helper::getTaskNotAssignedOpers($name, true);
        } else if ($name) {
            $data['taskAssignedOpers'] = Helper::getTaskAssignedOpers($name, false);
            $data['taskNotAssignedOpers'] = Helper::getTaskNotAssignedOpers($name, false);
        }
        if ($data['taskAssignedOpers'] == array()) {
            $data['revoke'] = array("name" => "revokeOpers", "disabled" => true);
        } else {
            $data['revoke'] = array("name" => "revokeOpers");
        }
        if ($data['taskNotAssignedOpers'] == array()) {
            $data['assign'] = array("name" => "assignOpers", "disabled" => true);
        } else {
            $data['assign'] = array("name" => "assignOpers");
        }
        $this->renderPartial('tabViews/taskAjax',
            array('model' => $model, 'name' => $name, 'data' => $data, 'message' => $this->_getMessage()), false, true);
    }

    /**
     * Creates a new model.
     * If creation is successful, the browser will be redirected to the 'show' page.
     */
    public function actionCreate() {
        $this->layout='//layouts/dialog';
        $model = new AuthItem;
        if (isset($_POST['AuthItem'])) {
            $model->attributes = $_POST['AuthItem'];
            try {
                if ($model->save()) {
                    $this->addMessage('state', 'success');
                    $this->addMessage('refresh', true);
                    $this->addMessage('referer', $this->createUrl('manage', array('selectedType'=>$model->type)));
                    $this->addMessage('message', Helper::translate('srbac', 'created successfully'));
                } else {
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', Helper::translate('srbac', 'Error while creating'));
                }
            } catch (CDbException $exc) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Helper::translate('srbac', 'Error while creating'));
            }
            $this->showMessage();
        } else {
            $this->render('manage/create', array('model' => $model));
        }
    }

    /**
     * Updates a particular model.
     * If update is successful, the browser will be redirected to the 'show' page.
     */
    public function actionUpdate() {
        $this->layout='//layouts/dialog';
        $model = $this->loadAuthItem();
        if (isset($_POST['AuthItem'])) {
            $model->oldName = isset($_POST["oldName"]) ? $_POST["oldName"] : $_POST["name"];
            $model->attributes = $_POST['AuthItem'];

            if ($model->save()) {
                $this->addMessage('state', 'success');
                $this->addMessage('refresh', true);
                $this->addMessage('message', Helper::translate('srbac', 'updated successfully'));
            } else {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Helper::translate('srbac', 'Error while updating'));
            }
            $this->showMessage();
        }
        $this->render('manage/update', array('model' => $model));
    }

    /**
     * Deletes a particular model.
     * If deletion is successful, the browser will be redirected to the 'list' page.
     */
    public function actionDelete() {
        if (Yii::app()->request->isAjaxRequest) {

            if ($this->loadAuthItem()->delete()){
            //$this->processAdminCommand();
            //$criteria = new CDbCriteria;
            //$pages = new CPagination(AuthItem::model()->count($criteria));
            //$pages->pageSize = $this->module->pageSize;
            //$pages->applyLimit($criteria);
            //$sort = new CSort('AuthItem');
            //$sort->applyOrder($criteria);
            //$models = AuthItem::model()->findAll($criteria);

                $this->addMessage('state', 'success');
                $this->addMessage('refresh', true);
                $this->addMessage('message', Helper::translate('srbac', 'Deleted successfully'));
            }
            else{
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Helper::translate('srbac', 'Error while deleting'));
            }
            $this->showMessage();
        } else {
            throw new CHttpException(400, 'Invalid request. Please do not repeat this request again.');
        }
    }

    /**
     * Installs srbac (only in debug mode)
     */
    public function actionInstall() {
        if ($this->module->debug) {
            $action = Yii::app()->getRequest()->getParam("action", "");
            $demo = Yii::app()->getRequest()->getParam("demo", 0);
            if ($action) {
                $error = Helper::install($action, $demo);
                if ($error == 1) {
                    $this->render('install/overwrite', array("demo" => $demo));
                } else if ($error == 0) {
                    $this->render('install/success', array("demo" => $demo));
                } else if ($error == 2) {
                    $error = Helper::translate("srbac", "Error while installing srbac.<br />Please check your database and try again");
                    $this->render('install/error', array("demo" => $demo, "error" => $error));
                }
            } else {
                $this->render('install/install');
            }
        } else {
            $error = Helper::translate("srbac", "srbac must be in debug mode");
            $this->render("install/error", array("error" => $error));
        }
    }

    /**
     * Displayes the authitem manage page
     */
    public function actionManage() {
        $selectedType = Yii::app()->request->getParam('selectedType', null);
        $page = Yii::app()->getRequest()->getParam("page", "");

        $models = null;
        $pages = null;
        if ($selectedType != "") {
            $criteria = new CDbCriteria;
            $criteria->condition = "type = " . $selectedType;
            $pages = new CPagination(AuthItem::model()->count($criteria));
            $pages->route = "manage";
            $pages->pageSize = $this->module->pageSize;
            $pages->applyLimit($criteria);
            $pages->setCurrentPage($page-1);

            $sort = new CSort('AuthItem');
            $sort->applyOrder($criteria);

            $models = AuthItem::model()->findAll($criteria);
        }

        $this->render('manage/manage', array(
            'models' => $models,
            'pages' => $pages,
            'sort' => $sort,
            'selectedType'=>$selectedType,
        ));
    }

    /**
     * Returns the data model based on the primary key given in the GET variable.
     * If the data model is not found, an HTTP exception will be raised.
     * @param integer the primary key value. Defaults to null, meaning using the 'id' GET variable
     */
    public function loadAuthItem($id=null) {
        if ($this->_model === null) {
            $r_id = urldecode(Yii::app()->getRequest()->getParam("id", ""));
            if ($id !== null || $r_id != "")
                $this->_model = AuthItem::model()->findbyPk($id !== null ? $id : $r_id);
            if ($this->_model === null)
                throw new CHttpException(404, 'The requested page does not exist.');
        }
        return $this->_model;
    }

    //TODO These messages should be replaced by flash messages

    /**
     * Sets the message that is displayed to the user
     * @param String $mess  The message to show
     */
    private function _setMessage($mess) {
        Yii::app()->user->setState("message", $mess);
    }

    /**
     *
     * @return String Gets the message that will be displayed to the user
     */
    private function _getMessage() {
        return Yii::app()->user->getState("message");
    }

    /**
     * Displayes the assignments page with no user selected
     */
    public function actionAssignments() {
        $this->render('assignments', array("id" => 0));
    }

    /**
     * Displayes the members of the role selected
     */
    public function actionRolemember() {
        $this->render('rolemember');
    }

    public function actionShowRolemembers() {
        $role = $_POST['role'];
        if(!empty($role)){
            $role = addslashes($role);
            $assignmentTable = Yii::app()->authManager->assignmentTable;
            $rows = Yii::app()->db->createCommand()->select('userid')->from($assignmentTable)->where('itemname="'.$role.'"')->queryAll();
            $userIds = array();
            foreach($rows as $row){
                $userIds[] = $row['userid'];
            }
            echo CJSON::encode(array('userids'=>$userIds));
        }
        Yii::app()->end();
    }

    /**
     * Show a user's assignments.The user is passed by $_GET
     */
    public function actionShowAssignments() {
        $userid = isset($_GET["id"]) ? $_GET["id"] : $_POST[Helper::findModule('srbac')->userclass][$this->module->userid];
        $user = $this->module->getUserModel()->with('profile')->findByPk($userid);
        $username = $user->{$this->module->username};
        $r = array(0 => array(0 => array()));
        if ($userid > 0) {
            $auth = Yii::app()->authManager;
            $descriptions = array();
            /* @var $auth CDbAuthManager */
            $tasks = $auth->getAuthItems(1, null);
            foreach($tasks as $x=>$task){
                $descriptions[$task->name] = $task->description;
                $curTask = $task->name;
                $t[$x] = $curTask;
                $grandchildren = $auth->getItemChildren($curTask);
                $t[$x] = array();
                foreach ($grandchildren as $y => $oper) {
                    $curOper = $oper->name;
                    $descriptions[$oper->name] = $oper->description;
                    $t[$x][$y] = $curOper;
                }
            }

            $ass = $auth->getAuthItems(null, $userid);

            $r = array();
            foreach ($ass as $i => $role) {
                $curRole = $role->name;
                $descriptions[$role->name] = $role->description;
                $r[$i] = $curRole;
                $children = $auth->getItemChildren($curRole);
                $r[$i] = array();
                foreach ($children as $j => $task) {
                    $descriptions[$task->name] = $task->description;
                    $curTask = $task->name;
                    $r[$i][$j] = $curTask;
                    $grandchildren = $auth->getItemChildren($curTask);
                    $r[$i][$j] = array();
                    foreach ($grandchildren as $k => $oper) {
                        $curOper = $oper->name;
                        $descriptions[$oper->name] = $oper->description;
                        $r[$i][$j][$k] = $curOper;
                    }
                }
            }
            $userNotAssignedRoles = Helper::getUserNotAssignedRoles($userid);
            $model = new AuthItem();
            // Add always allowed opers
//            $r["AlwaysAllowed"][""] = $this->module->getAlwaysAllowed();
            $this->renderPartial('userAssignments', array('data' => $r, 'datat'=>$t, 'descriptions'=>$descriptions, 'user' => $user, 'userNotAssignedRoles'=>$userNotAssignedRoles, 'model'=>$model));
        }
    }

    public function actionShowTasks()
    {
        $descriptions = array();
        $auth = Yii::app()->authManager;
        $tasks = $auth->getAuthItems(1, null);
        foreach($tasks as $x=>$task){
            $descriptions[$task->name] = $task->description;
            $curTask = $task->name;
            $t[$x] = $curTask;
            $grandchildren = $auth->getItemChildren($curTask);
            $t[$x] = array();
            foreach ($grandchildren as $y => $oper) {
                $curOper = $oper->name;
                $descriptions[$oper->name] = $oper->description;
                $t[$x][$y] = $curOper;
            }
        }

        $name = isset($_POST["AuthItem"]["name"][0]) ? $_POST["AuthItem"]["name"][0] : "";
//        $roleAssignedTasks = Helper::getRoleAssignedTasks($name);
        $roleAssignedTasks = $auth->getItemChildren($name);
        $tarr = array();
        foreach($roleAssignedTasks as $datum){
            $tarr[] = $datum->name;
        }

        $this->renderPartial('tabViews/taskToRole', array('taskData'=>$t, 'descriptions'=>$descriptions, 'roleAssignedTasks'=>CJSON::encode($tarr)));
    }

    public function actionMoveOper()
    {
        $op         = Yii::app()->request->getParam('op');
        $targett    = Yii::app()->request->getParam('targett');
        $currentt   = Yii::app()->request->getParam('currentt');

        if($currentt != $targett){
            $auth = Yii::app()->authManager;
            $auth->removeItemChild($currentt, $op);
            if(!$auth->hasItemChild($targett, $op)){
                $auth->addItemChild($targett, $op);
                Yii::log(sprintf('%s把%s从%s移动到%s',Yii::app()->user->getName(), $op, $currentt, $targett), CLogger::LEVEL_INFO, 'application.auth');
            }
        }
    }
}

