<?php

/**
 * This is the model class for table "ivy_survey_return_template".
 *
 * The followings are the available columns in table 'ivy_survey_return_template':
 * @property integer $id
 * @property string $cn_title
 * @property string $en_title
 * @property string $cn_intro
 * @property string $en_intro
 * @property string $cn_intro2
 * @property string $en_intro2
 * @property integer $status
 * @property integer $created_at
 * @property integer $created_by
 * @property string $updated_at
 * @property integer $updated_by
 */
class SurveyReturnTemplate extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_survey_return_template';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('cn_title, en_title, cn_intro, en_intro, created_at, created_by, updated_at, updated_by', 'required'),
			array('status, created_at, created_by, updated_by', 'numerical', 'integerOnly'=>true),
			array('cn_title, en_title', 'length', 'max'=>255),
			array('updated_at', 'length', 'max'=>11),
			array('cn_intro2, en_intro2', 'safe'),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, cn_title, en_title, cn_intro, en_intro, cn_intro2, en_intro2, status, created_at, created_by, updated_at, updated_by', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'cn_title' => 'Cn Title',
			'en_title' => 'En Title',
			'cn_intro' => 'Cn Intro',
			'en_intro' => 'En Intro',
			'cn_intro2' => 'Cn Intro2',
			'en_intro2' => 'En Intro2',
			'status' => 'Status',
			'created_at' => 'Created At',
			'created_by' => 'Created By',
			'updated_at' => 'Updated At',
			'updated_by' => 'Updated By',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('cn_title',$this->cn_title,true);
		$criteria->compare('en_title',$this->en_title,true);
		$criteria->compare('cn_intro',$this->cn_intro,true);
		$criteria->compare('en_intro',$this->en_intro,true);
		$criteria->compare('cn_intro2',$this->cn_intro2,true);
		$criteria->compare('en_intro2',$this->en_intro2,true);
		$criteria->compare('status',$this->status);
		$criteria->compare('created_at',$this->created_at);
		$criteria->compare('created_by',$this->created_by);
		$criteria->compare('updated_at',$this->updated_at,true);
		$criteria->compare('updated_by',$this->updated_by);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return SurveyReturnTemplate the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

    public function getTitle()
    {
        switch (Yii::app()->language) {
            case "zh_cn":
                return  $this->cn_title ;
                break;
            case "en_us":
                return  $this->en_title;
                break;
        }
    }

    public function getIntro()
    {
        switch (Yii::app()->language) {
            case "zh_cn":
                return  $this->cn_intro ;
                break;
            case "en_us":
                return  $this->en_intro;
                break;
        }
    }
}
