<?php
/**
 * Message translations.
 *
 * This file is automatically generated by 'yiic message' command.
 * It contains the localizable messages extracted from source code.
 * You may modify this file by translating the extracted messages.
 *
 * Each array element represents the translation (value) of a message (key).
 * If the value is empty, the message is considered as not translated.
 * Messages that no longer need translation will have their translations
 * enclosed between a pair of '@@' marks.
 *
 * Message string can be used with plural forms format. Check i18n section
 * of the guide for details.
 *
 * NOTE, this file must be saved in UTF-8 encoding.
 *
 * @version $Id: $
 */
return array (
  'Air' => 'PM2.5',
  'Air Quality Index (PM2.5)' => '空气质量指数 (PM2.5)',
  'Beijing' => '北京',
  'Chengdu' => '成都',
  'Ningbo' => '宁波',
  'Tianjin' => '天津',
  'Xi\'an' => '西安',
);
