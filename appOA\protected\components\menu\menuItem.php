<?php
$menus = array(
    'previewCC'=>array(
        'title'=>Yii::t("menu", "孩子前台预览"),
        'link' => array("/index/index"),
        'params'=>null,
    ),
    'pta'=>array(
        'title'=>Yii::t("menu", "家长会议记录"),
        'link' => array("/operations/ptaMemo/admin"),
        'params'=>null,
    ),
    'visit'=>array(
        'title'=>Yii::t("menu", "预约参观管理"),
        'link' => array("/operations/visit"),
        'params'=>null,
    ),
    'crm'=>array(
        'title'=>Yii::t("menu", "来访记录"),
        'link' => array("/operations/crm", 'state'=>10),
        'params'=>null,
    ),
    'event'=>array(
        'title'=>Yii::t("menu", "市场活动"),
        'link' => array("/operations/event"),
        'params'=>null,
    ),
    'pointsConfig'=>array(
        'title'=>Yii::t("menu", "积分兑换配置"),
        'link' => array("/settings/default/index", 'target'=>'PointsConfig'),
        'params'=>null,
    ),
    'multBranch'=>array(
        'title'=>Yii::t("menu", "管理多校园"),
        'link' => array("/settings/default/index", 'target'=>'multBranch'),
        'params'=>null,
    ),
    'pointsExchage'=>array(
        'title'=>Yii::t("menu", "积分兑换"),
        'link' => array("/operations/order/admin"),
        'params'=>null,
    ),
    'childMgt'=>array(
        'title'=>Yii::t("menu", "孩子管理"),
        'link' => array("/child/list"),
        'params'=>null,
    ),
    'diglossia'=>array(
        'title'=>Yii::t("menu", "双语选项管理"),
        'link' => array("/settings/default/index", 'target'=>'diglossia'),
        'params'=>null,
    ),
    'hr'=>array(
        'title'=>Yii::t("menu", "HR"),
        'link' => array('//operations/hr/index', 'type'=>1),
        'params'=>null,
    ),
    'charts'=>array(
        'title'=>Yii::t("menu", "财务预算及执行"),
        'link' => array('//operations/charts/index'),
        'params'=>null,
    ),
    'voucher'=>array(
        'title'=>Yii::t('menu', '现金抵用卷统计'),
        'link'=>array('//operations/voucher/index'),
        'params'=>null
    )
);

return array(
	"tPreviewCC" => array(
		'previewCC'=>$menus['previewCC'],
	),
	"tPtaAdmin" => array(
        'pta'=>$menus['pta'],
        'crm'=>$menus['crm'],
        'event'=>$menus['event'],
	),
	"tVisitAdmin" => array(
        'visit'=>$menus['visit'],
        'voucher'=>$menus['voucher'],
	),
    'tGeneralConfigAccess' => array(
        'pointsConfig'=>$menus['pointsConfig'],
        'multBranch'=>$menus['multBranch'],
        'diglossia'=>$menus['diglossia'],
    ),
    'tCampusAdmin' => array(
        'pointsExchage'=>$menus['pointsExchage'],
		'childMgt'=>$menus['childMgt'],
    ),
    'tOperationsAdmin' => array(
        'pointsExchage'=>$menus['pointsExchage'],
    ),
    'tChildAdmin' => array(
        'childMgt'=>$menus['childMgt'],
    ),
    'tHr' => array(
        'hr'=>$menus['hr'],
    ),
    'tChartsAdmin'=>array(
        'charts'=>$menus['charts'],
    )
);