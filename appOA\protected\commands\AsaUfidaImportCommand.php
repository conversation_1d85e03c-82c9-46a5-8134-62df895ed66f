<?php

/**
 * 导入 NC 数据
 * yiic asaufidaimport schools 导入学校
 * yiic asaufidaimport courses 导入课程
 * yiic asaufidaimport income --month=202101 导入收入
 * yiic asaufidaimport share --month=202101 导入分摊
 */
class AsaUfidaImportCommand extends CConsoleCommand
{
    private $batchNum = 50;
    private $xmlPath = "./commands/ufidaxml/";
    private $depConfig = array(
        "code" => "100620",
        "user" => "ivyonline"
    );
    private $url = "http://123.57.253.39:80/service/XChangeServlet?account=0001";

    public function init()
    {
        error_reporting(E_ALL);
        Yii::import("common.models.asainvoice.*");
    }

    // 导入部门（学校）
    public function actionSchools()
    {
        $xmlUrl = $this->xmlPath . "Department.xml";
        $xmlTemplate = file_get_contents($xmlUrl);
        $templateParams = array("{[RECEIVER]}", "{[SCHOOLID]}", "{[SCHOOLNAME]}", "{[DATE]}");

        // 查找所有校园
        $branchList = Branch::model()->getBranchList(null, true);
        foreach ($branchList as $branch) {
            // 判断是否导入过
            $ufidaSchool = AsaUfidaSchool::model()->findByPk($branch["id"]);
            if ($ufidaSchool) {
                echo $branch["id"] . " 已导入\r\n";
                continue;
            }
            $parseParams = array($this->depConfig["code"], $branch["id"], $this->_t($branch["title"]), date('Y-m-d'));
            if ($this->postData($templateParams, $parseParams, $xmlTemplate)) {
                echo $branch["id"] . " 导入成功\r\n";
                // 更新学校为已导入
                $ufidaSchool = new AsaUfidaSchool();
                $ufidaSchool->school_id = $branch["id"];
                $ufidaSchool->status = 1;
                $ufidaSchool->updated_time = time();
                $ufidaSchool->save();
            }
        }
    }

    // 导入客商（课程）
    public function actionCourses()
    {
        $xmlUrl = $this->xmlPath . "CustomerSupplierFile.xml";
        $xmlTemplate = file_get_contents($xmlUrl);
        $templateParams = array("{[RECEIVER]}", "{[COURSEID]}", "{[CORP]}", "{[COURSENAME]}");
        // 查找所有课程
        $crit = new CDbCriteria();
        $crit->compare("startyear", ">= 2021");
        $courseGroupList = AsaCourseGroup::model()->findAll($crit);

        foreach ($courseGroupList as $courseGroup) {
            foreach ($courseGroup->course as $course) {
                // 判断是否导入过
                if ($course->ufida_update == 1) {
                    echo "课程: " . $course->id . " 已导入\r\n";
                    continue;
                }
                $parseParams = array($this->depConfig["code"], $course->id, $this->depConfig["code"], $this->_t($course->title_cn));
                if ($this->postData($templateParams, $parseParams, $xmlTemplate)) {
                    echo "课程: " . $course->id . " 导入成功\r\n";
                    $course->ufida_update = 1;
                    $course->save();
                } else {
                    echo "课程: " . $course->id . " 导入失败\r\n";
                    Yii::app()->end();
                }
            }
        }
    }

    // 导入收入
    public function actionIncome($month)
    {
        $monthStamp = strtotime($month . "01");
        if (!$monthStamp) {
            echo "error month";
            Yii::app()->end();
        }
        $timestamp = mktime(0, 0, 0, date("n", $monthStamp) + 1, 1, date("Y", $monthStamp)) - 1;
        // 查找指定月份的数据
        $crit = new CDbCriteria();
        $crit->compare("timestmp", $timestamp);
        $count = AsaUfidaIncome::model()->count($crit);
        $cycle = ceil($count / $this->batchNum);
        $incomeXmlUrl = $this->xmlPath . "ufida_income.xml";
        $incomeXmlTemplate = file_get_contents($incomeXmlUrl);
        $refundXmlUrl = $this->xmlPath . "ufida_refund.xml";
        $refundXmlTemplate = file_get_contents($refundXmlUrl);
        for ($i = 0; $i < $cycle; $i++) {
            $crit->limit = $this->batchNum;
            $crit->offset = $i * $this->batchNum;
            $models = AsaUfidaIncome::model()->findAll($crit);
            foreach ($models as $model) {
                if ($model->status != 0) {
                    continue;
                }
                $voucherid = UfidaVoucher::getVoucherid(date("Ym", $monthStamp));
                $parseParams = array(
                    "{[COMPANY_CODE]}" => $this->depConfig["code"],
                    "{[YEAR]}" => date("Y", $timestamp),
                    "{[MONTH]}" => date("m", $timestamp),
                    "{[VOUCHER]}" => $voucherid,
                    "{[VOUCHERID]}" =>  date("Ym", $monthStamp) . $voucherid,
                    "{[DATE]}" => date("Y-m", $monthStamp) . "-" . date("t", $monthStamp),
                    "{[ENTERUSER]}" => $this->depConfig["user"],
                    "{[BORROW_ACCOUNT]}" => $model->borrow_accounts_code,
                    "{[BORROW_MEMO]}" => $this->formatMemo($model->memo),
                    "{[BORROW_AMOUNT]}" => $model->amount,
                    "{[LOAN_ACCOUNT]}" => $model->loan_accounts_code,
                    "{[COURSEID]}" => $model->course_id,
                    "{[SCHOOLID]}" => $model->school_id,
                    "{[SEASON]}" => $model->fee_type,
                );
                if ($model->type == AsaUfidaIncome::UFIDA_TYPE_PAY) {
                    $xmlTemplate = $incomeXmlTemplate;
                }
                if ($model->type == AsaUfidaIncome::UFIDA_TYPE_REFUND) {
                    $xmlTemplate = $refundXmlTemplate;
                }
                if ($this->postData(array_keys($parseParams), $parseParams, $xmlTemplate)) {
                    echo $model->id . " 导入收入成功\r\n";
                    $model->status = 1;
                    $model->save();
                    // 存储凭证号
                    $this->updateVoucher($voucherid, $model->id, date("Ym", $monthStamp));
                } else {
                    echo $model->id . " 导入收入失败\r\n";
                    Yii::app()->end();
                }
            }
        }
    }

    // 导入分摊
    public function actionShare($month)
    {
        $monthStamp = strtotime($month . "01");
        if (!$monthStamp) {
            echo "error month";
            Yii::app()->end();
        }
        $shareMonth = date("Y-m", $monthStamp);
        $shareDate = date("t", strtotime($shareMonth . "-01"));
        // 查找指定月份的数据
        $crit = new CDbCriteria();
        $crit->compare("month", date("Ym", $monthStamp));
        $count = AsaUfidaShare::model()->count($crit);
        $cycle = ceil($count / $this->batchNum);
        for ($i = 0; $i < $cycle; $i++) {
            $crit->limit = $this->batchNum;
            $crit->offset = $i * $this->batchNum;
            $models = AsaUfidaShare::model()->findAll($crit);
            foreach ($models as $model) {
                if ($model->status != 0) {
                    continue;
                }

                $xmlUrl = $this->xmlPath . "ShareVoucher.xml";
                $xmlTemplate = file_get_contents($xmlUrl);
                $voucherid = UfidaVoucher::getVoucherid(date("Ym", $monthStamp));
                $memo = $this->formatMemo($model->memo);

                $parseParams = array(
                    "{[COMPANY_CODE]}" => $this->depConfig["code"],
                    "{[YEAR]}" => date("Y", $model->month_stamp),
                    "{[MONTH]}" => date("m", $model->month_stamp),
                    "{[VOUCHER]}" => $voucherid,
                    "{[VOUCHERID]}" => date("Ym", $monthStamp) . $voucherid,
                    "{[DATE]}" => $shareMonth . "-" . $shareDate,
                    "{[ENTERUSER]}" => $this->depConfig["user"],
                    "{[BORROW_ACCOUNT]}" => $model->borrow_accounts_code,
                    "{[BORROW_MEMO]}" => $memo,
                    "{[BORROW_AMOUNT]}" => $model->amount,
                    "{[LOAN_ACCOUNT]}" => $model->loan_accounts_code,
                    "{[COURSEID]}" => $model->course_id,
                    "{[SCHOOLID]}" => $model->school_id,
                    "{[SEASON]}" => $model->fee_type,
                );
                if ($this->postData(array_keys($parseParams), $parseParams, $xmlTemplate)) {
                    echo $model->id . " 导入分摊成功\r\n";
                    $model->status = 1;
                    $model->save();
                    // 存储凭证号
                    $this->updateVoucher($voucherid, $model->id, date("Ym", $monthStamp));
                } else {
                    echo $model->id . " 导入分摊失败\r\n";
                    Yii::app()->end();
                }
            }
        }
    }

    /**
     * 发送数据给NC服务器
     *
     * @param array $templateParams 模板变量
     * @param array $parseParams 解析变量
     * @param string $xmlTemplate xml 模板内容
     * @return bool
     */
    private function postData($templateParams, $parseParams, $xmlTemplate)
    {
        $dataContent = str_ireplace($templateParams, $parseParams, $xmlTemplate);
        echo $dataContent;
        $res = $this->postCurl($this->url, $dataContent);
        if ($res == "Y") {
           return true;
        }
        return false;
    }

    private function postCurl($url, $dataContent)
    {
        echo $dataContent;
        //CURL
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $dataContent);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: text/xml'));
        $result = curl_exec($ch);
        $resultStatus = $this->str_cut($result, 'successful="', '">');
        if ($resultStatus == "Y") {
            $info = curl_getinfo($ch);
            echo $result;
            echo 'Took ' . $info['total_time'] . ' seconds to send a request to ' . $info['url'];
        } else {
            echo $result;
            echo 'Curl error: ' . curl_error($ch);
        }
        curl_close($ch);
        return $resultStatus;
    }

    public function updateVoucher($voucherid, $relevancyid, $month)
    {
        $model = new UfidaVoucher();
        $model->month = $month;
        $model->voucherid = $voucherid;
        $model->relevancyid = $relevancyid;
        $model->timestamp = time();
        $model->save();
    }

    public function str_cut($str, $start, $end)
    {
        //取出第一个匹配,效率最高，先分割再替换
        $content = strstr($str, $start);
        $content = substr($content, strlen($start), strpos($content, $end) - strlen($start));
        return $content;
    }

    public function formatMemo($memo)
    {
        if (strlen($memo) == 0) {
            $memo = "摘要不能为空";
        }
        $memo = $this->_t($memo);
        // 长度限制为 60
        return substr($memo, 0, 60);
    }

    public function _t($str = '')
    {
        trim($str);
        $str = str_replace("&", "", $str);
        return iconv("UTF-8", "GBK//TRANSLIT", $str);
    }
}
