<?php
    $labels = EClassroom::attributeLabels();
    $typeList = array('is_multifunc'=>$labels['is_multifunc'],'is_hall'=>$labels['is_hall'],'is_lib'=>$labels['is_lib']);
?>
<script type="text/template" id="classroom-list-template">
    <tr id="<%= id %>" data-status=<%= status %>>
        <th class="active"><h4><span class="glyphicon glyphicon-map-marker"></span> <%= code %></h4></th>
        <td><%= capacity %></td>
        <td class="text-success text-center"><% if (is_multifunc == 1) {%> <span class="glyphicon glyphicon-ok"></span> <%}%></td>
        <td class="text-success text-center"><% if (is_hall == 1) {%> <span class="glyphicon glyphicon-ok"></span> <%}%></td>
        <td class="text-success text-center"><% if (is_lib == 1) {%> <span class="glyphicon glyphicon-ok"></span> <%}%></td>
        <td><span> <%= statsList[status] %></span></td>
        <td><%= desc %></td>
        <td>
            <a class="btn btn-primary btn-xs" title="<?php echo Yii::t('global','Edit');?>" href="javascript:void(0)" onclick="createClassroom(<%= id %>);"><i class="glyphicon glyphicon-pencil"></i></a>
            <a class="J_ajax_del btn btn-danger btn-xs" title="<?php echo Yii::t('global','Delete');?>" href="<?php echo $this->createUrl('/mcampus/classroom/deteteClassroom');?>&id=<%=id%>"><i class="glyphicon glyphicon-trash"></i></a>
        </td>
    </tr>
</script>

<script type="text/template" id="classroom-add-template">
<div class="form-group"  model-attribute="code">
    <?php echo CHtml::label($labels['code'], CHtml::getIdByName('EClassroom[code]'), array('class'=>'col-sm-3 control-label'));?>
    <div class="col-sm-9">
        <?php echo CHtml::textField('EClassroom[code]', '<%= code %>', array('class'=>'form-control','encode'=>false));?>
    </div>
</div>
<div class="form-group" model-attribute="capacity">
    <?php echo CHtml::label($labels['capacity'], CHtml::getIdByName('EClassroom[capacity]'), array('class'=>'col-sm-3 control-label'));?>
    <div class="col-sm-9">
        <?php echo CHtml::textField('EClassroom[capacity]', '<%= capacity %>', array('class'=>'form-control','encode'=>false));?>
    </div>
</div>
<div class="form-group" model-attribute="type">
    <?php echo CHtml::label($labels['type'], CHtml::getIdByName('EClassroom[type]'), array('class'=>'col-sm-3 control-label'));?>
    <div class="col-sm-9">
        <p class="text-info">
            <?php echo Yii::t('message', 'Please ignore classroom type for normal classroom.');?>
        </p>
        <?php echo CHtml::checkBoxList('EClassroom[type]', '<%= type %>',$typeList,array('class'=>'checkbox-inline','encode'=>false,'separator'=>'&nbsp;&nbsp;'));?>
    </div>
</div>
<div class="form-group" model-attribute="status">
    <?php echo CHtml::label($labels['status'], CHtml::getIdByName('EClassroom[status]'), array('class'=>'col-sm-3 control-label'));?>
    <div class="col-sm-9">
        <?php echo CHtml::radioButtonList('EClassroom[status]', '<%= status %>',$statsList,array('class'=>'checkbox-inline','encode'=>false,'separator'=>'&nbsp;&nbsp;'));?>
    </div>
</div>
<div class="form-group">
    <?php echo CHtml::label($labels['desc'], CHtml::getIdByName('EClassroom[desc]'), array('class'=>'col-sm-3 control-label'));?>
    <div class="col-sm-9">
        <?php echo CHtml::hiddenField('id','<%= id %>',array('encode'=>false));?>
        <?php echo CHtml::textArea('EClassroom[desc]', '<%= desc %>',array('class'=>'form-control','encode'=>false));?>
    </div>
</div>
</script>