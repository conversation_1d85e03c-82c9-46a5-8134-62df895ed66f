<?php
if(!defined("IVY_POLICY"))
throw new CException('DO NOT USE THIS FILE DIRECTLY');

return array( 'policy'=>array(

	//是否老生老办法
	ENABLE_CONSTANT_TUITION => true,
	
	//年保育费
	//ANNUAL_CHILDCARE_AMOUNT => 8000,
	
	//是否开放课外活动账单
	ENABLE_AFTERSCHOOLS => false,
	
	//图书馆工本费
	FEETYPE_LIBCARD => 10,
		
	//学期四个时间点；取前后两端
	TIME_POINTS   => array(201509,201512,201601,201608),

	//寒暑假月份，及其每月计费天数
	/*HOLIDAY_MONTH => array(
            MONTH=>array(201502=>20,201508=>21),
            ITEMS => array(
                FEETYPE_TUITION,
                FEETYPE_LUNCH,
                FEETYPE_SCHOOLBUS,
            ),
            TUITION_CALC_BASE => BY_MONTH,
        ),*/
	
	//年付开通哪些缴费类型
	/*
	PAYGROUP_ANNUAL => array(
		FEETYPE_TUITION,
		FEETYPE_LUNCH,
		FEETYPE_SCHOOLBUS,
	),
	*/
		
	//学期付开通哪些缴费类型
	PAYGROUP_SEMESTER => array(
		ITEMS => array(
			FEETYPE_TUITION,
			FEETYPE_LUNCH,
			FEETYPE_SCHOOLBUS,		
		),
		TUITION_CALC_BASE => BY_MONTH,
	),
	
	//月份开通哪些缴费类型
	PAYGROUP_MONTH => array(
            ITEMS => array(
                FEETYPE_TUITION,
                FEETYPE_LUNCH,
                FEETYPE_SCHOOLBUS,
            ),
            TUITION_CALC_BASE => BY_MONTH,
	),

	
	//计算基准：BY_MONTH, 或者 BY_SETTING, 前者表示所有的学费都基于月费用计算，后者表示所有的学费都基于学期或学年
	TUITION_CALCULATION_BASE => array(
            BY_MONTH => array(
                //月收费金额
                AMOUNT => array(
                    FULL5D => 4500,
		),
			
		//收费月份，及其权重
		MONTH_FACTOR => array(
                    201509=>1,
                    201510=>1,
                    201511=>1,
                    201512=>1,
                    201601=>1,
                    201602=>.5,
                    201603=>1,
                    201604=>1,
                    201605=>1,
                    201606=>1,
                    201607=>1,
                    201608=>.5,
		),
            ),
	),
	
	//每餐费用
	LUNCH_PERDAY => 32,
	
	//账单到期日和账单开始日之差
	DUEDAYS_OFFSET => 0,
    
        //代金卷金额
//        VOUCHER_AMOUNT => 500,

));

?>