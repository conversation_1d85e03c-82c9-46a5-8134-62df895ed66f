<style>
    [v-cloak] {
		display: none;
	}
    .colorBlue{
        color:#4D88D2
    }
    .project{
        display: inline-flex;
        align-items:center;
        padding:16px;
        border-radius: 4px;
        border: 1px solid #DCDEE0;
        margin-right:24px;
        color:#333
    }
    .project:hover,.projected{
        background:#F0F5FB;
        cursor: pointer;
        border: 1px solid #4D88D2;
        color:#4D88D2
    }
    .addProject:hover{
        background:#F0F5FB;
        cursor: pointer;
    }
    .addProject{
        cursor: pointer;
        display: inline-flex;
        align-items:center;
        padding:16px;
        border-radius: 4px;
        border: 1px dashed #4D88D2;
        float: left;
    }
    .projectStaff{
        display: inline-flex;
        align-items:center;
        margin-right:24px
    }
    .projectStaff img{
        width: 28px;
        height: 28px;
        border-radius: 50%;
        object-fit: cover;
    }
    .standard{
        border-radius: 4px;
        margin-bottom: 24px;
        border: 1px solid transparent;
        overflow: hidden
    }
    .tableData{
        border:1px solid #E5E6EB !important;
        margin:0
    }
    .table  th{
        background:#F7F7F8;
    }
    .tableData  td, .tableData  th{
        border: 1px solid #E5E6EB !important;
        vertical-align: middle !important;
        text-align:center;
        padding:12px 16px !important;
    }
    .text-left{
        text-align:left !important
    }
    .tableData > tbody + tbody{
        border-top: 1px solid #E5E6EB;
    }
    .title{
        display: flex;
        padding:16px 24px;
        align-items: center;
        border: 1px solid #E5E6EB;
        border-bottom:none;
        font-weight:bold
    }
    .blueLine{
        width: 4px;
        height: 14px;
        background: #4D88D2;
    }
    .listMedia{
        padding:8px;
        border: 1px solid #fff;
    }
    .listMedia:hover{
        background: rgba(77,136,210,0.1);
        border-radius: 4px;
        border: 1px solid #4D88D2;
        cursor: pointer;
    }
    .borderLeftpos {
        display: inline-block;
        border-left: 1px solid #E4E7ED;
        height: 100%;
        width: 1px;
        position: absolute;
        left: 50%;
    }
    .optionSearch{
        height:auto;
        padding:5px 20px
    }
    .text_overflow {
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        line-height: 14px;
    }
    .img42{
      width: 42px;
      height: 42px;
      object-fit: cover;
      border-radius:50%;
    }
    .refresh{
        padding:2px 8px;
        background: #FCF1F0;
        border-radius: 12px;
        color:#D9534F;
        font-size:12px
    }
    .tableCode tr th,.tableCode tr td{
        border-top:none !important
    }
    .allCheck{
        position: absolute;
        right: 10px;
        top: 0;
    }
    .border{
        border: 1px solid #DCDFE6;
        padding: 16px;
        border-radius: 4px;
    }
    .listMedia{
        padding:8px;
        border: 1px solid #fff;
    }
    .listMedia:hover{
        background: rgba(77,136,210,0.1);
        border-radius: 4px;
        border: 1px solid #4D88D2;
        cursor: pointer;
    }
    .space{
        white-space: nowrap;
    }
    .el-tabs__item:hover{
        color:#4D88D2
    }
    .el-tabs__item.is-active{
        color:#4D88D2
    }
    .el-tabs__active-bar{
        background-color: #4D88D2;
    }
</style>
<div class="container-fluid"  id='template' v-cloak>
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('//moperation/default/index')) ?></li>
        <li class="active">质检系统</li>
    </ol>
    <div class="row" >
        <div class="col-md-12 col-sm-12">
            <!-- <ul class="nav nav-pills nav-stacked text-left background-gray" id="pageCategory">
                <li class="" ><a href="<?php echo $this->createUrl('index', array('branchId' => $this->branchId)); ?>">年度质检</a></li>
                <li class="active" ><a href="<?php echo $this->createUrl('template', array('branchId' => $this->branchId)); ?>">质检标准</a></li>
            </ul> -->
            <el-tabs v-model="activeName" @tab-click="handleClick">
                <el-tab-pane name="first"> <span slot="label">年度质检</el-tab-pane>
                <el-tab-pane name="second"> <span slot="label">质检标准</el-tab-pane>
            </el-tabs>
        </div>
        <div class='col-md-12 col-sm-12' v-if='initLoading'>
            <div class=''>
                <div class='project pull-left mb20' :class='currentTep.id==list.id?"projected":""' v-for='(list,index) in tepList' @click='getTepInfo(list)'>
                    <!-- <span class='el-icon-folder-opened colorBlue font20'></span> -->
                    <img style='width:20px' src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/folder.png' ?>" alt="">
                    <span class='font14 ml8'>{{list.title}}</span>
                </div>
                <div class='addProject mb20' @click='addProject'><span class='el-icon-plus colorBlue font20'></span><span class='colorBlue font14 ml8'>新建项目</span></div>
            </div>
            <div class='clearfix'></div>
            <div v-if='templateInfo.info'>
                <div class='flex'>
                    <div class='flex1 mr24'>
                        <div class='font16 color3 fontBold pt8'>{{templateInfo.info.title}}</div>
                        <div class='mt12 color6 font12'>{{templateInfo.info.desc}}</div>
                        <div class='mt16'>
                            <div class='projectStaff' v-for='(list,index) in templateInfo.info.head_user'>
                                <img :src="templateInfo.head_user_info[list].photoUrl" alt="">
                                <span class='font14 color3 ml8'>{{templateInfo.head_user_info[list].name}}</span>
                            </div>
                        </div>
                    </div>
                    <div>
                        <button type="button" class="btn btn-default" @click='editProject(templateInfo)'><span class='el-icon-edit'></span> 编辑项目</button>
                        <button type="button" class="btn btn-default ml16" @click='hrefLink(templateInfo)'><span class='el-icon-setting'></span> 管理分类</button>
                    </div>
                </div>
                <hr>
                <div class='mb24'>
                    <button type="button" class="btn btn-primary" @click='addStandard'><span class='el-icon-circle-plus-outline'></span> 添加标准</button>
                </div>
                <div class='standard' v-for='(item,index) in templateInfo.info.categories'>
                    <div class='title'>
                        <div class='blueLine'></div>
                        <span class='font14 color3 ml8'>{{item.title}}</span> 
                    </div>
                    <table class="table table-bordered tableData">
                        <tbody>
                            <tr>
                                <th  width='150'>子类别</th>
                                <th  width='160'>检查形式</th>
                                <th  width='130'>检查频率</th>
                                <th  width='80'>编号</th>
                                <th  width='550' class='text-left'>合格标准</th>
                                <th style='width:180px'>操作</th>
                            </tr>
                        </tbody>
                        <tbody v-for='(list,idx) in item.sub'>
                            <tr>
                                <td :rowspan='list.attach.length+1'>{{list.title}}</td>
                                <td :rowspan='list.attach.length+1'><span v-for='(_item,i) in list.check_form'>{{templateInfo.check_form[_item]}}<span v-if='i+1<list.check_form.length'>、</span></span></td>
                                <td :rowspan='list.attach.length+1'>{{templateInfo.check_frequency[list.frequency]}}</td>
                            </tr>
                            <tr v-for='(items,j) in list.attach'>
                                <td>{{items.number}}</td>
                                <td  class='text-left'><div v-html='html(items.standards)'></div></td>
                                <td>
                                    <span class='flex align-items'>
                                        <el-switch
                                            v-model="items.status"
                                            active-color="#4D88D2"
                                            active-value=1
                                            inactive-value=0
                                            @change='attachStatus(items)'
                                            inactive-color="#CCCCCC">
                                        </el-switch>
                                        <span class='mr20 ml4 font14 color6 text-left space'>启用</span>
                                        <el-dropdown>
                                            <span class="el-dropdown-link colorBlue font14 space">
                                                更多<i class="el-icon-arrow-down el-icon--right"></i>
                                            </span>
                                            <el-dropdown-menu slot="dropdown">
                                                <el-dropdown-item @click.native='editStandard(item,items)'>编辑</el-dropdown-item>
                                                <el-dropdown-item @click.native='delStandard(items)'>删除</el-dropdown-item>
                                            </el-dropdown-menu>
                                        </el-dropdown>
                                    </span>
                                </td>
                            </tr>
                           
                        </tbody>                    
                    </table>
                </div>
            </div>
            <div v-else-if='tepList.length==0' class='mt24'>
                <el-empty description="暂无数据"></el-empty>
            </div>
        </div>
    </div>
    <!-- 新建项目 -->
    <div class="modal fade" id="addProjectModal" tabindex="-1" role="dialog" data-backdrop="static" data-keyboard="false" data-backdrop="static">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentCommentsModalLabel"><?php echo Yii::t('leave', '新建项目') ?></h4>
                </div>
                <div class="modal-body relative" style='padding:24px'>
                    <div class='font14 color6 mb12'>项目标题（中文）</div>
                    <div class='mb24'> <input type="text" class="form-control" v-model='temAdd.title_cn' placeholder="请输入"></div>
                    <div class='font14 color6 mb12'>项目标题（英文）</div>
                    <div class='mb24'> <input type="text" class="form-control" v-model='temAdd.title_en' placeholder="请输入"></div>
                    <div class='font14 color6 mb12'>项目简介（中文）</div>
                    <div class='mb24'><textarea class="form-control" rows="3" v-model='temAdd.desc_cn' placeholder="请输入"></textarea></div>
                    <div class='font14 color6 mb12'>项目简介（英文）</div>
                    <div class='mb24'><textarea class="form-control" rows="3" v-model='temAdd.desc_en' placeholder="请输入"></textarea></div>
                    <div class='font14 color6 mb12'>项目负责人</div>
                    <div class='mb16'><span class='colorBlue font14 cur-p' @click='getSchoolList'><span class='el-icon-plus'></span>添加</span></div>
                    <div class='row'>
                        <div class='col-md-6' v-for='(item,index) in temAdd.head_user'>
                            <div class='flex align-items listMedia'>
                                <img :src="allDept[item].photoUrl" alt="" class="img42">
                                <div class="flex1 ml10 flex1Text">
                                    <div class=" font14 color3 text_overflow">{{allDept[item].name}}</div>
                                    <div class="font12 mt5 color6 text_overflow">{{allDept[item].hrPosition}}</div>
                                </div>
                                <span class='el-icon-circle-close font14' @click='removeUser(index)'></span>
                            </div>
                        </div>
                    </div>
                </div> 
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Cancel");?></button>
                    <el-button type="button" class="btn btn-primary"  @click='confirmTemplateAdd()' :loading="confirmLoading"><?php echo Yii::t("global", "OK");?></el-button>
                </div>
            </div>
        </div>
    </div>
    <!-- 选择成员 -->
    <div class="modal fade" id="addStaffModal" tabindex="-1" role="dialog" data-backdrop="static" data-keyboard="false" data-backdrop="static">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentCommentsModalLabel"><?php echo Yii::t('leave', '添加成员') ?></h4>
                </div>
                <div class="modal-body relative" style='padding:0'>
                    <span class='borderLeftpos'></span>
                    <div style='max-height:600px;' class='p24 row'>
                        <div class='col-md-6 col-sm-6'>
                          <div>
                            <el-select v-model="schoolId" style='width:100%' size='small' placeholder="<?php echo Yii::t('teaching', 'Please select') ?>" @change='getSchoolData()'>
                                <el-option
                                    v-for="(item,key,index) in schoolList"
                                    :key="key"
                                    :label="item.title"
                                    :value="key">
                                </el-option>
                            </el-select>
                          </div>
                          <div class='mt10'>
                              <el-input
                              size='small' 
                              placeholder="<?php echo Yii::t('global', 'Search') ?>"
                              v-model='searchText' 
                              clearable>
                              </el-input>
                          </div>
                          <div  class="tab-pane active mt15 scroll-box" id="class" v-if='searchText==""'  style='max-height:460px;overflow-y:auto'>
                            <div v-if='currentDept.list && currentDept.list.length>0'>
                              <div v-for='(list,index) in currentDept.list' class='relative mb16'>
                                  <p  @click='showDepName(list)'>
                                      <span  class='font14 color606 cur-p'>{{list.dep_name}} </span>
                                      <span class='el-icon-arrow-down ml5' v-if='dep_name!=list.dep_name'></span>
                                      <span class='el-icon-arrow-up ml5' v-else></span>
                                  </p>
                                  <!-- <p  class='allCheck' v-if='dep_name==list.dep_name'>
                                    <button class="btn btn-default pull-right btn-xs" type="button" @click='selectAll(list,index)' ><?php echo Yii::t("global", "Select All");?></button>
                                    </p> -->
                                  <div  class='border scroll-box mr10 childList' v-if='dep_name==list.dep_name'>
                                      <div class="flex align-items listMedia" v-for='(item,key,idx) in list.user'>
                                          <div class='flex flex1' v-if='currentDept.user_info[item.uid]'>
                                              <img :src="currentDept.user_info[item.uid].photoUrl" data-holder-rendered="true" class="media-object img-circle img42">
                                              <div class="flex1 ml10 flex1Text">
                                                  <div class=" font14 mt8 color3 text_overflow">{{currentDept.user_info[item.uid].name}}</div>
                                                  <div class="font12 mt5 color6 text_overflow">{{currentDept.user_info[item.uid].hrPosition}}</div>
                                              </div>
                                          </div>
                                          <div >
                                              <span class='cur-p font16 text-primary  el-icon-circle-plus-outline' v-if='!currentDept.user_info[item.uid].disabled' @click='assignStaff(item,index,idx)'></span>
                                              <span v-else><?php echo Yii::t('directMessage', 'selected') ?></span>
                                          </div>
                                      </div>
                                  </div>
                              </div>
                            </div>
                            <div v-else>
                              <el-empty description="<?php echo Yii::t('ptc', '无可选择成员') ?>"></el-empty>
                            </div>
                          </div>
                          <div v-else>
                              <div v-if='searchStaffList.length!=0' class='mt24 scroll-box'  style='max-height:460px;overflow-y:auto'>  
                                  <div class="flex align-items listMedia" v-for='(item,idx) in searchStaffList'>
                                      <div class='flex flex1' >
                                          <img :src="item.photoUrl" data-holder-rendered="true" class="media-object img-circle img42">
                                          <div class="flex1 ml10 flex1Text">
                                              <div class=" font14 mt8 color3 text_overflow">{{item.name}}</div>
                                              <div class="font12 mt5 color6 text_overflow">{{item.hrPosition}}</div>
                                          </div>
                                      </div>
                                      <div >
                                          <span class='cur-p font16 text-primary  el-icon-circle-plus-outline' v-if='!currentDept.user_info[item.uid].disabled' @click='assignStaff(item,idx,"search")'></span>
                                          <span v-else><?php echo Yii::t('directMessage', 'selected') ?></span>
                                      </div>
                                  </div> 
                              </div>
                              <div v-else-if='searchText!=""'>
                                      <div class='font14 color6 text-center mt20'><?php echo Yii::t('ptc', 'No Data') ?></div>    
                              </div>
                          </div>
                        </div>
                        <div class='col-md-6 col-sm-6'>
                            <p class='mt10 font14 color6'>
                            <?php echo Yii::t("newDS", " ");?>{{staffSelected.length}} <?php echo Yii::t('leave', '成员') ?>
                            </p>
                            <div class='scroll-box p10 overflow-y' style='height:500px'>
                                <div class="flex align-items listMedia" v-for='(item,idx) in staffSelected'>
                                    <div class='flex flex1' v-if='allDept[item]'>
                                        <img :src="allDept[item].photoUrl" data-holder-rendered="true" class="media-object img-circle img42">
                                        <div class="flex1 ml10 flex1Text">
                                            <div class=" font14 mt8 color3 text_overflow">{{allDept[item].name}}</div>
                                            <div class="font12  mt5 color6 text_overflow">{{allDept[item].hrPosition}}</div>
                                        </div>
                                    </div>
                                    <div @click='Unassign(item,idx)'>
                                        <span class='closeChild cur-p mt10 font16 el-icon-circle-close'></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class='clearfix'></div>
                    </div>
                </div> 
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Cancel");?></button>
                    <el-button type="button" class="btn btn-primary"  @click='confirmSatff()' ><?php echo Yii::t("global", "OK");?></el-button>
                </div>
            </div>
        </div>
    </div>
    <!-- 添加标准 -->
    <div class="modal fade" id="addStandardModal" tabindex="-1" role="dialog" data-backdrop="static" >
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentCommentsModalLabel"><?php echo Yii::t('leave', '添加标准') ?></h4>
                </div>
                <div class="modal-body relative" style='padding:24px'>
                    <div class='font14 color6 mb12'>所属分类 <span class='refresh ml16 cur-p' @click='updateOption()'><span class='el-icon-refresh'></span> 同步更新</span></div>
                    <div class='mb24'>
                        <el-cascader
                            style='width:400px'
                            size='small'
                            v-model='addCategory'
                            :options="options"
                            @change="handleChange">
                        </el-cascader>
                        <button type="button" class="btn btn-default ml16"  @click='hrefLink(templateInfo)'><span class='el-icon-setting'></span> 管理分类</button>
                    </div>
                    <div v-if='addCategory.length!=0'>
                        <div class='font14 color6 mb12'>合格标准</div>
                        <div>
                            <table class='table tableCode'>
                                <tbody>
                                    <tr>
                                        <th width='100'>编号</th>
                                        <th width='200'>合格标准（中文）</th>
                                        <th width='200'>合格标准（英文）</th>
                                        <th width='50'>操作</th>
                                    </tr>
                                    <tr v-for='(list,index) in attachList'>
                                    <td><textarea class="form-control" v-model='list.number'  placeholder="<?php echo Yii::t("leave", "Input");?>" rows="3"></textarea></td>
                                    <td><textarea class="form-control" v-model='list.standards_cn'  placeholder="<?php echo Yii::t("leave", "Input");?>" rows="3"></textarea></td>
                                    <td><textarea class="form-control" v-model='list.standards_en'  placeholder="<?php echo Yii::t("leave", "Input");?>" rows="3"></textarea></td>
                                    <td><span class='el-icon-delete mt24 cur-p' @click='delAttachList(index)'></span></td>
                                    </tr>
                                </tbody>
                            </table>
                            <div><span class='colorBlue font14 cur-p' @click='addAttachList'><span class='el-icon-plus'></span> 新增</span></div>
                        </div>
                    </div>
                </div> 
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Cancel");?></button>
                    <el-button type="button" class="btn btn-primary"  @click='saveAddAttach()' :loading="confirmLoading"><?php echo Yii::t("global", "OK");?></el-button>
                </div>
            </div>
        </div>
    </div>
    <!-- 编辑标准 -->
    <div class="modal fade" id="editStandardModal" tabindex="-1" role="dialog" data-backdrop="static">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentCommentsModalLabel"><?php echo Yii::t('leave', '编辑标准') ?></h4>
                </div>
                <div class="modal-body relative" style='padding:24px'>
                    <div class='font14 color6 mb12'>所属分类</div>
                    <div class='mb24'>
                        <el-cascader
                            style='width:400px'
                            size='small'
                            disabled
                            v-model='editCategory'
                            :options="options">
                        </el-cascader>
                        <button type="button" class="btn btn-default ml16"  @click='hrefLink(templateInfo)'><span class='el-icon-setting'></span> 管理分类</button>
                    </div>
                    <div class='font14 color6 mb12'>编号</div>
                    <div class='mb24'> <input type="text" class="form-control" v-model='editStandardData.number'  placeholder="<?php echo Yii::t("leave", "Input");?>"></div>
                    <div class='font14 color6 mb12'>合格标准（中文）</div>
                    <div class='mb24'><textarea class="form-control" v-model='editStandardData.standards_cn'  placeholder="<?php echo Yii::t("leave", "Input");?>" rows="3"></textarea></div>
                    <div class='font14 color6 mb12'>合格标准（英文）</div>
                    <div class='mb24'><textarea class="form-control" v-model='editStandardData.standards_en'  placeholder="<?php echo Yii::t("leave", "Input");?>" rows="3"></textarea></div>
                </div> 
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Cancel");?></button>
                    <el-button type="button" class="btn btn-primary"  @click='saveEditStandard()' :loading="confirmLoading"><?php echo Yii::t("global", "OK");?></el-button>
                </div>
            </div>
        </div>
    </div>
    <!-- 删除 -->
    <div class="modal fade" id="delModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog modal-sm" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><span v-if='delType=="edit"'>提示</span><span v-if='delType=="del"'>删除</span></h4>
                </div>
                <div class="modal-body p24" >
                  <div v-if='delType=="del"'>
                    <?php echo Yii::t('directMessage', 'Proceed to remove?') ?>
                  </div>
                  <div v-if='delType=="edit"'>
                    <span v-if='attachStatusList.status=="1"'>
                        <?php echo Yii::t('directMessage', '确认启用吗？') ?>
                    </span>
                    <span v-else>
                        <?php echo Yii::t('directMessage', '确认关闭吗？') ?>
                    </span>
                  </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default"  data-dismiss="modal"><?php echo Yii::t("global", "Cancel"); ?></button>
                    <button type="button" class="btn btn-primary" v-if='delType=="del"' @click='delStandard()'><?php echo Yii::t("global", "OK"); ?></button>
                    <button type="button" class="btn btn-primary" v-if='delType=="edit"' @click='attachStatus()'><?php echo Yii::t("global", "OK"); ?></button>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    var  container = new Vue({
        el: "#template",
        data: {
            confirmLoading:false,
            schoolId:'',
            schoolList:[],
            reportData:{},
            searchText:'',
            staffSelected:[],
            dep_name:'',
            allDept:{},
            currentDept:{},
            options:[],
            tepList:[],
            initLoading:false,
            temAdd:{
                head_user: [],
                title_cn: "",
                title_en: "",
                desc_cn: "",
                desc_en: "",
                id: ""
            },
            currentTep:{},
            templateInfo:{},
            editCategory:[],
            editStandardData:{},
            delType:'',
            delId:'',
            attachList:[],
            addCategory:[],
            attachStatusList:{},
            activeName:'second'
        },
        created: function() {
            
           this.getInitList()
        },
        computed: {
            searchStaffList: function() {
                var search = this.searchText;
                var searchVal = ''; //搜索后的数据
                if(search) {
                    searchVal =Object.values(this.currentDept.user_info).filter(function(product) {
                        return Object.keys(product).some(function(key) {
                            return String(product['name'].toLowerCase()).indexOf(search.toLowerCase()) !== -1;
                        })
                    })
                    return searchVal;
                }
                return this.searchStaffList;
            },
        },
        mounted() {
            let that=this
            $('#delModal').on('hide.bs.modal', function (e) {
                if(that.delType=='edit'){
                    that.attachStatusList.status=that.attachStatusList.status=='1'?'0':'1'
                }
            })
        },
        methods: {
            html(data){
                if(data!='' && data!=null){
                    return  data.replace(/\n/g, '<br/>')
                }
            },
            getInitList(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("getTemplateList") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                           that.tepList=data.data
                           if(data.data.length!=0){
                            if(that.currentTep.id){
                                that.getTepInfo(that.currentTep)
                            }else{
                                that.getTepInfo(data.data[0])

                            }
                           }
                           that.initLoading=true
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            getSchoolList(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("schoolList") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {},
                    success: function(data) {
                        if (data.state == 'success') {
                            that.schoolList=data.data.list
                            that.schoolId=data.data.current_school
                            that.getSchoolData()
                            that.dep_name=''
                            that.staffSelected=JSON.parse(JSON.stringify(that.temAdd.head_user))
                            that.searchText=''
                            $("#addStaffModal").modal('show')
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            getSchoolData(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("getAllDepartment") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        school_id:this.schoolId
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                           that.currentDept=data.data
                           that.allDept=Object.assign(that.allDept, data.data.user_info)
                           that.dep_name=''
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
                
            },
            showDepName(list){
                if(this.dep_name==list.dep_name){
                    this.dep_name=''
                    return
                }
                this.dep_name=list.dep_name
                for(let i=0;i<list.user.length;i++){
                    if(this.staffSelected.indexOf(list.user[i].uid)!=-1){
                        console.log(list.user[i].uid)
                        Vue.set(this.currentDept.user_info[list.user[i].uid], 'disabled', true);
                    }
                }
            },
            assignStaff(list,index,idx){
                if(idx=='search'){
                    this.searchStaffList[index].disabled=true
                }else{
                    Vue.set(this.currentDept.user_info[list.uid], 'disabled', true);
                }
                this.staffSelected.push(list.uid)
            },
            Unassign(list,index){
                if(this.currentDept.user_info[list]){
                    Vue.set(this.currentDept.user_info[list], 'disabled', false);
                }
                this.staffSelected.splice(index,1)
            },
            removeUser(index){
                this.temAdd.head_user.splice(index,1)
            },
            confirmSatff(){
                this.temAdd.head_user=JSON.parse(JSON.stringify(this.staffSelected))
                $("#addStaffModal").modal('hide')
            },
            confirmTemplateAdd(){
                let that=this
                that.confirmLoading=true
                $.ajax({
                    url: '<?php echo $this->createUrl("saveTemplate") ?>',
                    type: "get",
                    dataType: 'json',
                    data:{data:this.temAdd},
                    success: function(data) {
                        if (data.state == 'success') {
                           console.log(data)
                            resultTip({
                                msg: data.message
                            });
                            
                            that.getInitList()
                            $("#addProjectModal").modal('hide')
                            that.confirmLoading=false
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            that.confirmLoading=false
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                        that.confirmLoading=false
                    },
                })
            },
            getTepInfo(list,type){
                this.currentTep=list
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("templateInfo") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        id:list.id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                           that.templateInfo=data.data
                           if(type){
                                that.getOptions()
                                resultTip({
                                    msg: data.message
                                });
                           }
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            editProject(info){
                let infoData=JSON.parse(JSON.stringify(info))
                this.temAdd={
                    head_user:infoData.info.head_user,
                    title_cn: infoData.info.title_cn,
                    title_en: infoData.info.title_en,
                    desc_cn: infoData.info.desc_cn,
                    desc_en: infoData.info.desc_en,
                    id: infoData.info.id
                },
                this.allDept=infoData.head_user_info
                $("#addProjectModal").modal('show')
            },
            addProject(){
                this.temAdd={
                    head_user: [],
                    title_cn: "",
                    title_en: "",
                    desc_cn: "",
                    desc_en: "",
                    id: ""
                },
                $("#addProjectModal").modal('show')
            },
            addStandard(){
                this.getOptions()
                this.addCategory=[]
                this.attachList=[]
                $("#addStandardModal").modal('show')
            },
            updateOption(){
                this.getTepInfo(this.currentTep,'update')
            },
            getOptions(){
                this.options=[]
                for(let i=0;i<this.templateInfo.info.categories.length;i++){
                    this.options.push({
                        value: this.templateInfo.info.categories[i].id,
                        label:this.templateInfo.info.categories[i].title,
                        children: []
                    })
                    for(let j=0;j<this.templateInfo.info.categories[i].sub.length;j++){
                        this.options[i].children.push({
                            value: this.templateInfo.info.categories[i].sub[j].id,
                            label:this.templateInfo.info.categories[i].sub[j].title,
                        })
                    }
                }
            },
            handleChange(value) {
                console.log(value);
                console.log(this.addCategory);
                let list=this.templateInfo.info.categories.filter((i) => i.id === value[0])
                let item=list[0].sub.filter((i) => i.id === value[1])
                this.attachList=JSON.parse(JSON.stringify(item[0].attach))
                if(this.attachList.length==0){
                    this.attachList.push({
                        "number": "",
                        "standards_cn": "",
                        "standards_en": ""
                    })
                }
            },
            delAttachList(index){
                this.attachList.splice(index,1)
            },
            addAttachList(){
                this.attachList.push({
                    "number": "",
                    "standards_cn": "",
                    "standards_en": ""
                })
            },
            saveAddAttach(){
                let that=this
                if(this.addCategory.length==0){
                    resultTip({
                        error: 'warning',
                        msg: '请选择分类'
                    });
                    return
                }
                if(this.attachList.length==0){
                    resultTip({
                        error: 'warning',
                        msg: '请添加标准'
                    });
                    return
                }
                let list=this.templateInfo.info.categories.filter((i) => i.id === this.addCategory[0])
                let item=list[0].sub.filter((i) => i.id === this.addCategory[1])
                that.confirmLoading=true
                $.ajax({
                    url: '<?php echo $this->createUrl("saveCategorySubAttachMany") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        "items_id":item[0].items_id,
                        "category_id":item[0].category_id,
                        "sub_id":item[0].id,
                        "list":this.attachList
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.getTepInfo(that.currentTep)
                            resultTip({
                                msg: data.message
                            });
                            $("#addStandardModal").modal('hide')
                            that.confirmLoading=false
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            that.confirmLoading=false
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                        that.confirmLoading=false
                    },
                })
            },
            editStandard(list,item){
                this.getOptions()
                this.editCategory=[list.id,item.sub_id]
                this.editStandardData=item
                $("#editStandardModal").modal('show')
            },
            saveEditStandard(){
                let that=this
                that.confirmLoading=true
                $.ajax({
                    url: '<?php echo $this->createUrl("saveCategorySubAttach") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        "items_id":this.editStandardData.items_id,
                        "category_id":this.editStandardData.category_id,
                        "sub_id":this.editStandardData.sub_id,
                        "sub_attach_id":this.editStandardData.id,
                        "number": this.editStandardData.number,
                        "standards_cn":this.editStandardData.standards_cn,
                        "standards_en":this.editStandardData.standards_en
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.getTepInfo(that.currentTep)
                            resultTip({
                                msg: data.message
                            });
                            $("#editStandardModal").modal('hide')
                            that.confirmLoading=false
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            that.confirmLoading=false
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                        that.confirmLoading=false
                    },
                })
            },
            delStandard(item){
                if(item){
                    this.delType='del'
                    this.delId=item.id
                    $("#delModal").modal('show')
                    return
                }
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("delCategorySubAttach") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        id:this.delId
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            $("#delModal").modal('hide')
                            that.getTepInfo(that.currentTep)
                            resultTip({
                                msg: data.message
                            });
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            attachStatus(list){
                if(list){
                    this.delType='edit'
                    this.attachStatusList=list
                    $("#delModal").modal('show')
                    return
                }
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("saveAttachStatus") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        id:this.attachStatusList.id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.getTepInfo(that.currentTep)
                            $("#delModal").modal('hide')
                            resultTip({
                                msg: data.message
                            });
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            hrefLink(item){
                console.log(this.currentTep)
                window.open('<?php echo $this->createUrl('categoryManage', array('branchId' => $this->branchId)); ?>'+'&id='+this.currentTep.id)
            },
            handleClick(){
                if(this.activeName=='first'){
                    window.location.href="<?php echo $this->createUrl('index', array('branchId' => $this->branchId)); ?>"
                }
            }
        },
        
    })
</script>
