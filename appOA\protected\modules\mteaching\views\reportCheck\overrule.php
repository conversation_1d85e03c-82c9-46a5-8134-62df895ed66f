<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span>
    </button>
    <h4 class="modal-title" id="exampleModalLabel"><?php echo Yii::t('reg','Reject reason') ?>：</h4>
</div>
<?php $form = $this->beginWidget('CActiveForm', array(
    'id' => 'learning-form',
    'enableAjaxValidation' => false,
    'htmlOptions' => array('class' => 'J_ajaxForm form-horizontal', 'role' => 'form'),
)); ?>
<div class="modal-body">
    <div class="form-group">
        <?php echo $form->labelEx($model, 'overrule', array('class' => 'col-xs-2 control-label')); ?>
        <div class="col-xs-8">
            <?php echo $form->textArea($model, 'overrule', array('class' => 'form-control')); ?>
        </div>
    </div>
</div>
<div class="modal-footer">
    <button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global', 'Submit'); ?></button>
    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('global', 'Cancel'); ?></button>
</div>
<?php $this->endWidget(); ?>
