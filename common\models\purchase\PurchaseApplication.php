<?php

/**
 * This is the model class for table "ivy_purchase_application".
 *
 * The followings are the available columns in table 'ivy_purchase_application':
 * @property integer $id
 * @property double $price
 * @property string $title
 * @property integer $add_user
 * @property integer $add_timestamp
 * @property integer $update_timestamp
 * @property integer $status
 */
class PurchaseApplication extends CActiveRecord
{
	
	const REFUSE = -1;
	const INPROGRESS = 0;
	const AGREE = 1;
	const CHANGED = 2;

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_purchase_application';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('price, title, add_user, add_timestamp, update_timestamp, budget, delivery_timestamp', 'required'),
			array('add_user, add_timestamp, update_timestamp, status, delivery_timestamp', 'numerical', 'integerOnly'=>true),
			array('price, budget', 'numerical'),
			array('content', 'length', 'max'=>255),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, price, title, add_user, add_timestamp, update_timestamp, status, budget, content', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
			'applicationItem'=>array(self::HAS_MANY ,'PurchaseApplicationItem' ,'aid' ,'order'=>'FIELD(type,1,3,2)'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'price' => 'Price',
			'title' => 'Title',
			'add_user' => 'Add User',
			'add_timestamp' => 'Add Timestamp',
			'update_timestamp' => 'Update Timestamp',
			'status' => 'Status',
			'budget' => '预算标准',
			'content' => '联络信息',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('price',$this->price);
		$criteria->compare('title',$this->title,true);
		$criteria->compare('add_user',$this->add_user);
		$criteria->compare('add_timestamp',$this->add_timestamp);
		$criteria->compare('update_timestamp',$this->update_timestamp);
		$criteria->compare('status',$this->status);
		$criteria->compare('content',$this->content);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return PurchaseApplication the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
	
	/**
	 * 获取采购申请表的商品总价
	 * @return [type] [description]
	 */
	public function getTotalMoney()
	{
		$totalMoney = 0;
		foreach ($this->applicationItem as $v) {
			if ($v->type == PurchaseApplicationItem::PURCHASE_PRODUCT) {
				$totalMoney += PurchaseProducts::model()->findByPk($v->pid)->price * $v->num;
			} elseif ($v->type == PurchaseApplicationItem::PURCHASE_STAND) {
				$totalMoney += PurchaseStandard::model()->findByPk($v->pid)->getTotalMoney() * $v->num;
			} elseif ($v->type == PurchaseApplicationItem::PURCHASE_CUSTOM) {
				$totalMoney += $v->price * $v->num;
			}
		}
		return number_format($totalMoney, 2, '.', '');
	}
}
