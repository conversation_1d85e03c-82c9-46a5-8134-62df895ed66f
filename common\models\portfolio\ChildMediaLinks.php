<?php

/**
 * This is the model class for table "ivy_child_media_links_2011".
 *
 * The followings are the available columns in table 'ivy_child_media_links_2011':
 * @property integer $id
 * @property string $category
 * @property string $schoolid
 * @property integer $classid
 * @property integer $childid
 * @property integer $yid
 * @property integer $weeknum
 * @property integer $itemid
 * @property integer $pid
 * @property string $title
 * @property string $content
 * @property integer $updated_timestamp
 * @property integer $userid
 */
class ChildMediaLinks extends CActiveRecord
{
    public static $StartYear = 2009;

	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return ChildMediaLinks2011 the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_child_media_links_'.self::$StartYear;
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array(' classid, childid, yid, weeknum, pid,  updated_timestamp, userid', 'required'),
			array('classid, childid, yid, weeknum, itemid, pid, updated_timestamp, userid', 'numerical', 'integerOnly'=>true),
			array('category', 'length', 'max'=>11),
			array('schoolid, title', 'length', 'max'=>255),
			array('schoolid,content,title,', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, category, schoolid, classid, childid, yid, weeknum, itemid, pid, title, content, updated_timestamp, userid', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'photoInfo'=>array(self::BELONGS_TO, 'ChildMedia', 'pid')
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'category' => 'Category',
			'schoolid' => 'Schoolid',
			'classid' => 'Classid',
			'childid' => 'Childid',
			'yid' => 'Yid',
			'weeknum' => 'Weeknum',
			'itemid' => 'Itemid',
			'pid' => 'Pid',
			'title' => 'Title',
			'content' => 'Content',
			'updated_timestamp' => 'Updated Timestamp',
			'userid' => 'Userid',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('category',$this->category,true);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('classid',$this->classid);
		$criteria->compare('childid',$this->childid);
		$criteria->compare('yid',$this->yid);
		$criteria->compare('weeknum',$this->weeknum);
		$criteria->compare('itemid',$this->itemid);
		$criteria->compare('pid',$this->pid);
		$criteria->compare('title',$this->title,true);
		$criteria->compare('content',$this->content,true);
		$criteria->compare('updated_timestamp',$this->updated_timestamp);
		$criteria->compare('userid',$this->userid);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

    public static function setStartYear($startYear)
    {
        self::$StartYear = $startYear;
    }
}