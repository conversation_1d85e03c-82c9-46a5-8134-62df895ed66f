<style>
  
    [v-cloak] {
        display: none;
    }
    .el-input__inner, .form-control {
        height: 34px;
        box-shadow: none;
        line-height:34px
    }
    .el-input__icon {
        line-height: 100%;
    }
    .borderCom{
        border-radius: 8px;
        border: 1px solid #E5E6EB;
    }
    .p24{
        padding:24px
    }
    .title{
        font-size: 16px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 600;
        color: #333333;
        line-height: 22px;
    }
    .imgIcon{
        width:30px;
        height:30px
    }
    .hrefLink{
        background:#FAFAFA;
        padding:12px;
        border-radius: 6px;
        align-items:center;
        margin-top:12px;
        cursor: pointer;
       
    }
    .leave{
        position: absolute;
        right:0;
        top:0;
        background: #F2DEDE;
        border-radius: 2px;
        font-size: 12px;
        font-weight: 400;
        color: #D9534F;
        padding:2px 6px;
    }
    .linkNum{
        font-size: 16px;
        font-weight: bold;
        color: #333333;
        margin-right:5px
    }
    .photoUrl{
        height: 118px;
        width: 118px;
        border-radius: 8px;
        object-fit: cover;
    }
    .wechat{
        border-radius: 4px;
        border: 1px solid #5CB85C;
        padding:8px 12px;
        color:#5CB85C;
        cursor: pointer;
        display:inline-block;
    }
    .flag{
        width:16px;
    }
    .notice{
        background:#EDF3FF;
        padding:24px;
        border-radius: 8px;
    }
    .Progressbtn{
        font-size: 14px;
        color: #FFFFFF !important;
        background: #4D88D2 !important;
        border-radius: 4px;
        padding:6px 12px;
        display: inline-block;
        cursor: pointer;
    }
    .calendarDays{
        background: #FAFAFA;
        border-radius: 8px;
        padding:12px;
        display:flex
    }
    .font500{
        font-weight:500
    }
    .selectSchool{
        background: #FAFAFA;
        border-radius: 4px;
        padding:10px;
        font-size: 14px;
        font-weight: 400;
        color: #333333;
        text-align:center;
        display:block;
        margin-bottom:8px
    }
    .redBg{
        background: #D9534F;
        color:#FFFFFF;
    }
    .selectSchool:hover{
        background: rgba(77,136,210,0.1);
        border-radius: 4px;
        cursor: pointer;
    }
    .tabEvent{
        padding: 6px 12px
    }
    .tabEventClick{
        background: #4D88D2 !important;
        color:#fff !important;
        border-radius: 4px;
    }
    .tabEvent:hover{
        cursor: pointer;
        background:#F7F7F8 ;
        color:#666 ;
        border-radius: 4px;
    }
    .full-calendar .fc-toolbar .fc-toolbar-chunk{
        color:#333
    }
    .fc-theme-standard td, .fc-theme-standard th{
        border:none;
        color:#333333 !important;
        border-top: 1px solid #F2F3F5;
    }
    .fc-theme-standard .fc-scrollgrid{
        border:none
    }
    .fc-scrollgrid thead{
        border-bottom: 1px solid #F2F3F5;
        border-top: 1px solid #F2F3F5;
    }
    .fc .fc-daygrid-day-top {
        display:block;
        text-align: left;
        color: #333333 !important;
        /* padding: 5px 0; */
        font-size:14px;
        height:24px
    }
    
    .fc .fc-button-primary{
        background:#fff;
        color:#333;
        border:none;
        font-size:14px
    }
    .fc-button-primary:hover{
        background:#F7F7F8 !important;
        color:#333 !important;
    }
    a:hover{
        text-decoration: none;
    }
    .fc .fc-col-header-cell-cushion { /* needs to be same precedence */
        padding-top: 5px; /* an override! */
        padding-bottom: 5px; /* an override! */
        font-size:14px
    }
    .fc .fc-toolbar-title{
        font-size:18px
    }
    .fc .fc-toolbar.fc-header-toolbar{
        border-bottom: 1px solid #F2F3F5;
        padding-bottom: 13px;
        margin-bottom:0px;
    }
    .fc-scrollgrid-sync-inner{
        padding:5px 0;
        text-align:left
    }
    .fc a{
        color:#333
    }
    .fc-day-today{
        background:#FAFAFA !important
    }
    .myClass .fc-daygrid-day-number,.fc-day-today .fc-daygrid-day-number{
        width: 24px;
        height: 24px;
        background: #4D88D2;
        color: #fff;
        display: inline-block;
        line-height: 24px;
        text-align: center;
        color: #fff !important;
        border-radius: 50%;
        padding: 0 !important;
    }
    .fc-daygrid-event-harness{
        margin-top:4px !important
    }
    .fc .fc-daygrid-day-number{
        padding:8px
    }
    .holidayDetails {
        background: #FAFAFA;
        border-radius: 4px;
        position: relative;
    }
    .alignCenter{
        align-items: center;
    }
    .holidayColor{
        background:rgba(240, 173, 78, 0.05);
        border-left:2px solid #F0AD4E;
    }
    .eventColor{
        background:rgba(85, 193, 238, 0.05);
        border-left:2px solid #55C1EE;
    }
    .weekColor{
        background:rgba(217, 83, 79, 0.03);
        border-left:2px solid #D9534F;
    }
    .privacy{
        position: absolute;
        right:0;
        top:0;
        background:#F2F3F5;
        padding:2px 4px;
        border-radius:2px;
        font-size:12px;
        color: #333333;
    }
    .dayType{
        padding: 2px 6px;
        background: rgba(77, 136, 210, 0.10);
        color: #4D88D2;
        border-radius: 2px;
        margin-right:6px
    }
    .point{
        border:none;
        color:#333;
        padding-left:15px;
        line-height:22px
    }
    .point:before{
        content: "";
        display: block;
        width:8px;
        height:8px;
        position: absolute;
        left: 4px;
        top:8px;
        border-radius: 50%;
    }
    .holidayList{
        background:rgba(240, 173, 78, 0.1);
    }
    .holidayList:before{
        background-color: #F0AD4E ;
    }
    .holidayListHalf:before{
        background-color: #fff !important;
        border:1px solid #F0AD4E;
    }
    .eventList{
        background:rgba(85, 193, 238, 0.10)
    }
    .eventListHalf:before{
        background-color: #fff !important;
        border:1px solid #55C1EE;
    }
    .eventList:before{
        background-color: #55C1EE;
    }
    .weekList{
        background:rgba(217, 83, 79, 0.10)
    }
    .weekListHalf:before{
        background-color: #fff !important;
        border:1px solid #D9534F;
    }
    .weekList:before{
        background-color: #D9534F;
    }
    .fc-h-event .fc-event-title{
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size:12px
    }
    .wechatQrcode{
        display: inline-block;
        padding: 6px 10px;
        color: #50B674;
        background: #FFFFFF;
        border-radius: 4px;
        border: 1px solid #50B674;
        cursor: pointer;
    }
    .wechatQrcode img{
        width:16px;
        float:left;
        border-radius:50%
    }
    .qrcodeBox{
        position: absolute;
        left: 0;
        top: 45px;
        width: 290px;
        /* height: 274px; */
        border: 1px solid #ddd;
        background: #fff;
        border-radius: 4px;
        text-align:center;
        font-size:14px;
        color:#333;
        box-shadow: 0px 2px 8px 0px rgb(0 0 0 / 20%);
        z-index:9;
        padding:20px 10px
    }
    .qrcodeBox .qrcodeWechat{
        width:124px;
        height:124px;
        margin:0 auto
    }
    .wechatIcon{
        height:16px;
        position: absolute;
        bottom: 0;
        right: 0;
        width: 16px;
    }
    .avatarWechat{
        width:60px;
        height:60px;
        border-radius:50%
    }
    .container-fluid{
        margin:0 9px
    }
    .el-tabs__item.is-active{
        color:#4D88D2
    }
    .el-tabs__active-bar{
        background-color:#4D88D2
    }
    #container a{
        color:#333333
    }
    .email:hover,.el-tabs__item:hover{
        color:#4D88D2 !important
    }
    .max500{
        overflow-y:auto
    }
    .scroll-box::-webkit-scrollbar-thumb {
        /*滚动条里面小方块*/
        border-radius   : 10px;
        background-color: #ccc;
        background-image: none
    }
    .scroll-box::-webkit-scrollbar-track {
        /*滚动条里面轨道*/
        box-shadow   : inset 0 0 5px rgba(0, 0, 0, 0);
        background   : #fff;
        border-radius: 10px;
        border:none
    }
    .colorC {
        color:#ccc
    }
    .break-all{
        word-break: break-all;
    }
    .imgBg{
        background-image:url('<?php echo Yii::app()->themeManager->baseUrl.'/base/images/index_calendar.png'?>');
        display: inline-block;
        width: 40px;
        height: 40px;
        background-size: cover;
        text-align: center;
        line-height: 43px;
        color: #fff;
        font-size: 16px;
    }
</style>
<div class="container-fluid"  id='container' v-cloak>
    <div class='flex' v-loading="fullscreenLoading">
        <div style='width:300px'>
            <div class='borderCom p24' v-if='userData.userInfo'>
                <div class='text-center'>
                    <div><img :src="userData.userInfo.photoUrl" alt="" class='photoUrl' ></div>
                    <div class='title mt12'>{{userData.userInfo.name}} <a class='el-icon-edit cur-p'  href="<?php echo $this->createUrl('//user/profile/index', array('category'=>'basic'));?>"></a></div>
                    <div class='font14 color6 mt5'><a href="<?php echo $this->createUrl('//mpub/default/ivyers')?>"></a>{{userData.userInfo.hrPositionCn}}</div>
                </div>
                <div class='font14 color3 mt10 flex'><span class='el-icon-message pt4'></span><a class='ml10 email flex1 break-all' :href="'mailto:'+userData.userInfo.email" :href="userData.userInfo.email">{{userData.userInfo.email}}</a> </div>
                <div class='font14 color3 mt5 flex'><span class='el-icon-postcard pt4'></span><span class='ml10 flex1 break-all'><a class='email' :href ="'<?php echo $this->createUrl('//mpub/default/ivyers'); ?>&branchId='+school_id+''" >{{userData.userInfo.school_name}}</a></span> </div>
                <div class='relative mt24'>
                        <div class='text-center '>
                        <div class='wechatQrcode ' @click.stop='showUnWechatQrcode' v-if='wechatData.state==0'>
                            <div>
                                <img src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/wechatDM.png' ?>" alt="">
                                <span class='ml4'><?php echo Yii::t("directMessage", "Wechat Notification"); ?></span>
                            </div>
                        </div>
                        <div class='wechatQrcode ' @click.stop='qrcodeBox=true'  v-if='wechatData.state==1'>
                            <div>
                                <img src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/wechatDM.png' ?>" alt="" v-if='wechatData.headimgurl=="" || wechatData.headimgurl==null'>
                                <img :src="wechatData.headimgurl" alt="" v-if='wechatData.headimgurl!=""'>
                                <span class='ml4'><?php echo Yii::t("directMessage", "Wechat Enabled"); ?></span>
                            </div>
                        </div>
                    </div>
                    <div class="qrcodeBox"  v-if='scanQrcodeBox'>
                        <div class='font14 color3'><strong><?php echo Yii::t("directMessage", "Wechat Notification"); ?></strong></div>
                        <div class='mt8 font12 color6'><?php echo Yii::t("directMessage", "Response parent feedback on wechat!"); ?></div>
                        <div>
                            <div id='wechatQrcodeBind' alt="" class='qrcodeWechat mt24'></div>
                            <div class='font12 mt16 color6 '><?php echo Yii::t("directMessage", "Wechat scan to bind your account."); ?></div>
                        </div>
                    </div>
                    <div  class="qrcodeBox"  v-if='qrcodeBox'>
                        <div class='font14 color3'><strong><?php echo Yii::t("directMessage", "Wechat Enabled"); ?></strong></div>
                        <div class='mt8 font12 color6'><?php echo Yii::t("directMessage", "Response parent feedback on wechat!"); ?></div>
                        <div class='relative inline-block' v-if='wechatData.headimgurl!=""'>
                            <img :src="wechatData.headimgurl" alt="" class='avatarWechat mt16'>
                            <img src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/wechatDM.png' ?>" alt="" class='wechatIcon'>
                        </div>
                        <div class='font12 mt16 color6 '  v-if='wechatData.nickname!=""'>{{wechatData.nickname}}</div>
                        <div class='font12 mt24 mt16 color6 '><button class="btn btn-default" type="button" @click.stop='unbind()'><?php echo Yii::t("directMessage", "Unbind your account"); ?></button></div>
                    </div>
                </div>
            </div>
            <div class='borderCom p24 mt24' v-if='userData.userInfo'>
                <div class='title mb16'><?php echo Yii::t("referral", "In Process"); ?></div>
                <div class='flex hrefLink relative' v-if='userData.workDone.waitsLeave'>
                    <span class='leave' v-if='userData.workDone.waitsLeave.showLeave'>有新审批</span>
                    <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/leave.png' ?>" alt="" class='imgIcon'>
                    <a class='font14 color3 flex1 ml10' :href='userData.workDone.waitsLeave.url'><?php echo Yii::t("leave", "Leave Application"); ?></a>
                    <span class='el-icon-arrow-right color6' size='16'></span>
                </div>
                <div class='flex hrefLink' v-if='userData.workDone.waitsWorkflow' @click='showSchoolList(userData.workDone.waitsWorkflow)'>
                    <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/workflow.png' ?>" alt="" class='imgIcon'>
                    <div class='font14 color3 flex1 ml10' ><?php echo Yii::t("workflow", "Workflow"); ?></div>
                    <span class='linkNum'>{{userData.workDone.waitsWorkflow.all_total}}</span>
                    <span class='el-icon-arrow-right color6' size='16'></span>
                </div>
                <div class='flex hrefLink'  v-if='userData.workDone.waitsBehaviors'  @click='showSchoolList(userData.workDone.waitsBehaviors)'>
                    <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/behavior.png' ?>" alt="" class='imgIcon'>
                    <div class='font14 color3 flex1 ml10'><?php echo Yii::t("site", "Student Behavior"); ?></div>
                    <span class='linkNum'>{{userData.workDone.waitsBehaviors.all_total}}</span>
                    <span class='el-icon-arrow-right color6' size='16'></span>
                </div>
                <div class='flex hrefLink' v-if='userData.workDone.waitsDM'  @click='showSchoolList(userData.workDone.waitsDM)'>
                    <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/DM.png' ?>" alt="" class='imgIcon'>
                    <div class='font14 color3 flex1 ml10'><?php echo Yii::t('newDS', 'No-Reply DM'); ?></div>
                    <span class='linkNum'>{{userData.workDone.waitsDM.all_total}}</span>
                    <span class='el-icon-arrow-right color6' size='16'></span>
                </div>
                <div class='flex hrefLink' v-if='userData.workDone.waitsAbsent'>
                    <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/absent.png' ?>" alt="" class='imgIcon'>
                    <a class='font14 color3 flex1 ml10' :href='userData.workDone.waitsAbsent.url'><?php echo Yii::t('campus', 'Absent')?></a>
                    <span class='el-icon-arrow-right color6' size='16'></span>
                </div>
                <div class='flex hrefLink'  v-if='userData.workDone.asa'>
                    <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/attendance.png' ?>" alt="" class='imgIcon'>
                    <a class='font14 color3 flex1 ml10' :href='userData.workDone.asa.url'><?php echo Yii::t('asa', 'ASA Staff Attendance'); ?></a>
                    <span class='el-icon-arrow-right color6' size='16'></span>
                </div>
                
            </div>
        </div>
        <div  class='flex1 ml24'>
            <div class='notice'>
                <div class='flex'>
                    <div class='flex1'>
                        <?php if(Yii::app()->language=='en_us'):?>
                            <div class='title mb16'>2023-24 Group Strategy</div>
                            <div class='mt8'>
                                <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/flag.png' ?>" alt="" class='flag'> 
                                <span class='font14 color3 ml10'>Leadership Everywhere Every function in Ivy Group becomes the leader in its own field.</span>
                            </div>
                            <div class='mt8'>
                                <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/flag.png' ?>" alt="" class='flag'> 
                                <span class='font14 color3 ml10'>SI Everywhere SI embedded into everything that we do.</span>
                            </div>
                            <div class='mt8'>
                                <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/flag.png' ?>" alt="" class='flag'> 
                                <span class='font14 color3 ml10'>Internal Capabilities Transformation Develop strong people, systems and processes.</span>
                            </div>
                            <div class='ml24 mt16'>
                                <a class='Progressbtn' target="_blank" :href="userData.okrUrl">Progress Tracker<span class='el-icon-arrow-right ml5'></span></a>
                            </div>
                        <?php else:?>
                            <div class='title mb16'>2023-24 集团策略</div>
                            <div class='mt8'>
                                <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/flag.png' ?>" alt="" class='flag'> 
                                <span class='font14 color3 ml10'>在全方位领先各部门在行业内都成为领导者。</span>
                            </div>
                            <div class='mt8'>
                                <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/flag.png' ?>" alt="" class='flag'> 
                                <span class='font14 color3 ml10'>社会创新（SI）无所不在SI融入到我们的环境和做的每一件事里。</span>
                            </div>
                            <div class='mt8'>
                                <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/flag.png' ?>" alt="" class='flag'> 
                                <span class='font14 color3 ml10'>内部能力提升拥有卓越的人，系统，和流程。</span>
                            </div>
                            <div class='ml24 mt16'>
                                <a class='Progressbtn' target="_blank" :href="userData.okrUrl">进展<span class='el-icon-arrow-right ml5'></span></a>
                            </div>
                        <?php endif;?>
                    </div>
                    <div>

                        <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/indexImg.png'?> " alt="" style='width:200px' class='mr24'>
                    </div>
                </div>
            </div>
            <div class='flex mt24 align-items' v-if='userData.accessSchoolList'>
                <div class='flex1 title'><?php echo Yii::t("site", "School Calendar"); ?></div>
                <div class='Progressbtn' @click='tabSchool'><span>{{userData.allBranch[school_id].title}}</span><span class='el-icon-arrow-down ml5' v-if='Object.keys(userData.accessSchoolList).length>1'></span></div>
            </div>
            <div class='mt16 calendarDays'>
                <div class='flex1 text-center'>
                    <div  class='font14 color3 font500'  v-if='calendarData.calendar_yearly_data'>{{calendarData.calendar_yearly_data.first_semester1}}</div>
                    <div  class='font14 color3 font500' v-else>-</div>

                    <div class='font12 color6 mt4'><?php echo Yii::t("site", "Calendar Start"); ?></div>
                </div>
                <div class='flex1 text-center'>
                    <div  class='font14 color3 font500' v-if='calendarData.calendar_yearly_data'>{{calendarData.calendar_yearly_data.second_semester2}}</div>
                    <div  class='font14 color3 font500' v-else>-</div>
                    <div class='font12 color6 mt4'><?php echo Yii::t("site", "Calendar End"); ?></div>
                </div>
                <div class='flex1 text-center'>
                    <div  class='font14 color3 font500' v-if='calendarData.calendar_yearly_data'>{{calendarData.calendar_yearly_data.all_total}}</div>
                    <div  class='font14 color3 font500' v-else>-</div>

                    <div class='font12 color6 mt4'><?php echo Yii::t("site", "Total Schooldays"); ?></div>
                </div>
            </div>
            <div class='flex mt24 align-items' style='height:34px'>
                <div class='flex1'>
                    <span class=' font14 color6 tabEvent' :class='calendarType=="all"?"tabEventClick":""'  @click='tabCalendar("all")'><?php echo Yii::t("site", "All"); ?></span>
                    <span class=' font14 color6 tabEvent' :class='calendarType==10?"tabEventClick":""' @click='tabCalendar(10)'><?php echo Yii::t("site", "Holiday"); ?></span>
                    <span class=' font14 color6 tabEvent' :class='calendarType==20?"tabEventClick":""' @click='tabCalendar(20)'><?php echo Yii::t("site", "Event"); ?></span>
                    <?php if(Yii::app()->params['siteFlag'] != 'daystar'){?>
                    <span class=' font14 color6 tabEvent' :class='calendarType==30?"tabEventClick":""' @click='tabCalendar(30)'><?php echo Yii::t("site", "Schooldays Make-up"); ?></span>
                    <?php }?>
                </div>
                <div>
                    <el-select v-model="filter_event_type" multiple collapse-tags  placeholder="<?php echo Yii::t("site", "Please Select"); ?>" @change='filterType()' v-if='calendarType=="all" || calendarType==20'>
                        <el-option
                        v-for="(list,key,index) in calendarData.day_event_type"
                        :key="index"
                        :label="list"
                        :value="key">
                        </el-option>
                    </el-select>
                </div>
            </div>
            <el-row :gutter="24">
                <el-col :xs="24" :sm="16" :md="16" :lg="16" :xl="16">
                    <div class='borderCom p16 mt24 mb20'>
                    <div id='calendar' ref='calendar' ></div>
                    </div>
                </el-col>
                <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
                <div class='borderCom p16 mt24'>
                    <el-tabs v-model="activeName">
                        <el-tab-pane :label="currentDay" name="day">
                            <div v-if='currentDayList.length'  > 
                                <div class='font14 color3 font500'>
                                    <span class='imgBg'>{{imgDay}}</span>
                                   <span class='font14 font500'>{{currentDay}}</span> 
                                </div>
                                <div  class='scroll-box'  :style="'max-height:'+(elementHeight)+'px;overflow-x: hidden;'">
                                    <div class='flex mt16 alignCenter'  v-for='(item,index) in currentDayList'>
                                        <div class='text-center mr10 time font14'>
                                            <div v-if='item.days.length!=1'>
                                            <span class='color3'>{{item.start_day}}</span><br>
                                            <span class='colorC mt5'>|</span><br>
                                            <span class='color3'>{{item.end_day}}</span>
                                            </div>
                                            <span class='color3' v-else>{{item.start_day}}</span>
                                        </div>
                                        <div class='flex1 flex align-items holidayDetails' :class='item.event==10?"holidayColor":item.event==20?"eventColor":"weekColor"' :style='item.memo=="" && (item.time[0]=="" || item.time[0]==null)?"padding:22px 16px;":"padding:8px 16px"'>
                                            <div class='flex1'>
                                                <div class='color3 font14'>{{item.title}}</div>
                                                <div class='color6 font12 mt5' v-if='item.memo!=""'><?php echo Yii::t("labels", "Description"); ?>：{{item.memo}}</div>
                                                <div class='flex align-items '>
                                                    <span class='dayType mt5' v-if='item.day_type==20'> <?php echo Yii::t('site','Half Day');?></span>
                                                    <span class='color6 font12 mt5  flex1' v-if="item.time[0]!='' && item.time[0]!=null">{{item.time[0]}} — {{item.time[1]}}</span>
                                                </div>
                                            </div>
                                            <span v-if='item.privacy==1' class='privacy'><?php echo Yii::t("newDS", "Visible to Staff Only"); ?></span>
                                        </div>
                                    </div>
                                </div>  
                            </div>
                            <div v-else>
                                <el-empty description="无日程"></el-empty>
                            </div>
                        </el-tab-pane>
                        <el-tab-pane :label="currentMon" name="mon">
                            <div v-if='currentMonList.length'  >  
                                <div class='font14 color3 font500'>{{currentMon}}</div>
                                <div class='scroll-box'  :style="'max-height:'+(elementHeight)+'px;overflow-x: hidden;'">
                                    <div class='flex mt16 alignCenter'  v-for='(item,index) in currentMonList'>
                                        <div class='text-center mr10 time font14'>
                                            <div v-if='item.days.length!=1'>
                                            <span class='color3'>{{item.start_day}}</span><br>
                                            <span class='colorC mt5'>|</span><br>
                                            <span class='color3'>{{item.end_day}}</span>
                                            </div>
                                            <span class='color3' v-else>{{item.start_day}}</span>
                                        </div>
                                        <div class='flex1 flex align-items holidayDetails' :class='item.event==10?"holidayColor":item.event==20?"eventColor":"weekColor"' :style='item.memo=="" && (item.time[0]=="" || item.time[0]==null)?"padding:22px 16px;":"padding:8px 16px"'>
                                            <div class='flex1'>
                                                <div class='color3 font14'>{{item.title}}</div>
                                                <div class='color6 font12 mt5' v-if='item.memo!=""'><?php echo Yii::t("labels", "Description"); ?>：{{item.memo}}</div>
                                                <div class='flex align-items '>
                                                    <span class='dayType mt5' v-if='item.day_type==20'> <?php echo Yii::t('site','Half Day');?></span>
                                                    <span class='color6 font12 mt5  flex1' v-if="item.time[0]!='' && item.time[0]!=null">{{item.time[0]}} — {{item.time[1]}}</span>
                                                </div>
                                            </div>
                                            <span v-if='item.privacy==1' class='privacy'><?php echo Yii::t("newDS", "Visible to Staff Only"); ?></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div v-else>
                                <el-empty description="无日程"></el-empty>
                            </div>
                        </el-tab-pane>
                        <el-tab-pane label="本学年日程" name="year">
                            <div v-if='calendar_annual.length' >
                                <div class='font14 color3 font500'>{{calendarData.calendar_yearly_data.school_year_title}}</div>
                                <div  class='scroll-box'  :style="'max-height:'+(elementHeight)+'px;overflow-x: hidden;'">
                                <div class='flex mt16 alignCenter'  v-for='(item,index) in calendar_annual'>
                                    <div class='text-center mr10 time font14'>
                                        <div v-if='item.days.length!=1'>
                                        <span class='color3'>{{item.start_day}}</span><br>
                                        <span class='colorC mt5'>|</span><br>
                                        <span class='color3'>{{item.end_day}}</span>
                                        </div>
                                        <span class='color3' v-else>{{item.start_day}}</span>
                                    </div>
                                    <div class='flex1 flex align-items holidayDetails' :class='item.event==10?"holidayColor":item.event==20?"eventColor":"weekColor"' :style='item.memo=="" && (item.time[0]=="" || item.time[0]==null)?"padding:22px 16px;":"padding:8px 16px"'>
                                        <div class='flex1'>
                                            <div class='color3 font14'>{{item.title}}</div>
                                            <div class='color6 font12 mt5' v-if='item.memo!=""'><?php echo Yii::t("labels", "Description"); ?>：{{item.memo}}</div>
                                            <div class='flex align-items '>
                                                <span class='dayType mt5' v-if='item.day_type==20'> <?php echo Yii::t('site','Half Day');?></span>
                                                <span class='color6 font12 mt5  flex1' v-if="item.time[0]!='' && item.time[0]!=null">{{item.time[0]}} — {{item.time[1]}}</span>
                                            </div>
                                        </div>
                                        <span v-if='item.privacy==1' class='privacy'><?php echo Yii::t("newDS", "Visible to Staff Only"); ?></span>
                                    </div>
                                </div>
                                </div>
                            </div>
                            <div v-else>
                                <el-empty description="无日程"></el-empty>
                            </div>
                        </el-tab-pane>
                    </el-tabs>
                </div>
                </el-col>
            </el-row>
        </div>
    </div>
    <div class="modal fade" id="schoolModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
        <div class="modal-dialog " role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("global", "选择校园"); ?></h4>
                </div>
                <div class="modal-body p24">
                  <div class='row'>
                    <div class=' col-md-6'  v-for='(list,key,index) in schoolListShow'>
                        <div v-if='SchoolListNum'>
                            <a :href='list.url' class='selectSchool pl15 pr15'> 
                                <span v-if='userData.allBranch' >{{userData.allBranch[key].title}}</span> 
                                <span class="badge redBg pull-right" v-if='SchoolListNum'>{{list.total}}</span>
                            </a>
                        </div>
                        <div v-else>
                            <div class='selectSchool pl15 pr15' @click='school_id=key;initCalendar()'> 
                                <span v-if='userData.allBranch' >{{userData.allBranch[key].title}}</span> 
                                <span class="badge redBg pull-right" v-if='SchoolListNum'>{{list.total}}</span>
                            </div>
                        </div>
                    </div>
                   
                  </div>
                  
                </div>
              
            </div>
        </div>
    </div>
</div>
<?php  $this->widget('ext.si.SI'); ?>
<script>
var lang='<?php echo Yii::app()->language;?>'
var container = new Vue({
        el: "#container",
       data:{
            lang:lang,
            school_id:'',
            userData:{},
            schoolListShow:{},
            calendarData :{},
            activeName: 'day',
            currentDay:'',
            imgDay:'',
            currentMon:'',
            currentDayList:[],
            currentMonList:[],
            calendar_annual:[],
            calendarDataList: [],
            calendarType:'all',
            wechatData:{},
            scanQrcodeBox:false,
            qrcodeBox:false,
            timer:'',
            filter_event_type:[],
            CopycurrentMonList:[],
            CopycurrentDayList:[],
            Copycalendar_annual:[],
            SchoolListNum:false,
            fullscreenLoading: true,
            elementHeight:''
        },
        watch: {
            calendarDataList(val){
                console.log(val)
                this.calendarDataList=val
            }
        },
        created: function() {
            this.getDate()
            this.initData()
            this.init();
        },
        computed: {
            
        },
        mounted: function () {
           
        },
        methods: {
            initData(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("homePage") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            console.log(data)
                            that.userData=data.data
                            if(getCookie('school_id_index')==null){
                                if(data.data.userInfo.school_id=='BJ_TYG' || data.data.userInfo.school_id=='BJ_BU' || data.data.userInfo.school_id=='BJ_SS'){
                                    that.school_id='BJ_DS'
                                }else{
                                    that.school_id=data.data.userInfo.school_id
                                }
                            }else{
                                that.school_id=getCookie('school_id_index')
                            }
                            that.initCalendar()
                            that.wechatData=data.data.userInfo.bindInfo
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            showSchoolList(data){
                this.SchoolListNum=true
                if(data.all_total==0){
                    window.location.href=data.url
                }else{
                    if(Object.values(data.school_total).length==1){
                        window.location.href=Object.values(data.school_total)[0].url
                    }else{
                        this.schoolListShow=data.school_total
                        $('#schoolModal').modal('show')
                    }
                }
            },
            tabSchool(){
                this.SchoolListNum=false
                this.schoolListShow=this.userData.accessSchoolList
                $('#schoolModal').modal('show')
            },
            showUnWechatQrcode(){
                let that=this
                if(this.scanQrcodeBox){
                    clearInterval(this.timer);
                    that.scanQrcodeBox=!that.scanQrcodeBox
                    return
                }else{
                    this.timer =setInterval(this.showWechatQrcode, 5000);   
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("/user/profile/teacherBindQrcode") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        staff_id:this.otherTeacherId==0?this.indexList.uid:this.otherTeacherId
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.scanQrcodeBox=!that.scanQrcodeBox
                            that.$nextTick(() => {
                                $('#wechatQrcodeBind').qrcode({width: 124,height: 124,text:data.data});
                            })
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            showWechatQrcode(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("/user/profile/teacherBindInfo") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {},
                    success: function(data) {
                        if (data.state == 'success') {
                            that.wechatData=data.data
                            if(data.data.state==1){
                                if(that.scanQrcodeBox){
                                    that.scanQrcodeBox=false
                                    that.qrcodeBox=true
                                }
                                clearInterval(that.timer);
                            }
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            unbind(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("/user/profile/teacherUnbind") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {},
                    success: function(data) {
                        if (data.state == 'success') {
                            resultTip({
                                msg:data.message
                            })
                            that.qrcodeBox=false
                            that.showWechatQrcode()
                        }else{
                            resultTip({
                                error: 'warning',
                                msg:data.message
                            })
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            initCalendar(){
                let that=this
                setCookie('school_id_index',this.school_id)
                $.ajax({
                    url: '<?php echo $this->createUrl("calendar") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        school_id:this.school_id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            $('#schoolModal').modal('hide')
                            that.calendarData=data.data
                            if(data.data.calendar_annual){
                                that.calendar_annual=JSON.parse( JSON.stringify ( data.data.calendar_annual ) )
                                that.Copycalendar_annual=JSON.parse( JSON.stringify ( data.data.calendar_annual ) )
                            }else{
                                that.calendar_annual=[]
                                that.Copycalendar_annual=[]
                            }
                            that.elementHeight = that.$refs.calendar.clientHeight-70; // 获取元素高度
 
                            let events=that.calendarOption.getEvents()
                            events.forEach(item => {
                                item.remove();
                            });
                            const currentDate = new Date();
                            that.getDayList()
                            that.getMonList(currentDate)
                            that.addEvent(that.calendar_annual)
                            that.fullscreenLoading=false
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            that.fullscreenLoading=false
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                        that.fullscreenLoading=false
                    },
                })
            },
            getDate(){
                const currentDate = new Date();
                const year = currentDate.getFullYear();
                const month = currentDate.getMonth() + 1; // 月份是从 0 开始计数的，因此要加1
                const day = currentDate.getDate();
                this.currentMon=year+'年'+month+'月'
                this.currentDay=year+'-'+('0' + month).slice(-2)+'-'+('0' + day).slice(-2)
                this.imgDay=('0' + day).slice(-2)
            },
            getDayList(){
                let day=this.currentDay.split('-')
                this.imgDay=('0' + day[2]).slice(-2)
                if(this.calendarData.calendar_by_day && this.calendarData.calendar_by_day[day[0]+day[1]+day[2]]){
                    this.currentDayList=this.calendarData.calendar_by_day[day[0]+day[1]+day[2]]
                    this.CopycurrentDayList=JSON.parse( JSON.stringify ( this.currentDayList ) )
                }else{
                    this.currentDayList=[]
                    this.CopycurrentDayList=[]
                }
            },
            getMonList(date1){
                console.log(date1)
                let y = date1.getFullYear()
                let m = date1.getMonth() + 1
                m = m < 10 ? ('0' + m) : m
                if(this.calendarData.calendar_by_month && this.calendarData.calendar_by_month[y+''+m]){
                    this.currentMonList=this.calendarData.calendar_by_month[y+''+m]
                    this.CopycurrentMonList=JSON.parse( JSON.stringify ( this.currentMonList ) )
                }else{
                    this.currentMonList=[]
                    this.CopycurrentMonList=[]
                }
            },
            init(){
                let that=this
                document.addEventListener('DOMContentLoaded', function() {
                    var calendarEl = document.getElementById('calendar');
                    that.calendarOption = new FullCalendar.Calendar(calendarEl, {
                        headerToolbar: {
                        left: 'prevButton',
                        center: 'title',
                        right: 'nextButton'
                        }, 
                        customButtons: {
                            nextButton: {
                                text: '下月',
                                click: function(info) {
                                    that.calendarOption.next()
                                    that.getMonList(that.calendarOption.currentData.currentDate)
                                    that.currentMon=that.calendarOption.currentData.viewTitle
                                }
                            },
                            prevButton: {
                                text: '上月',
                                click: function(info) {
                                    that.calendarOption.prev()
                                    that.getMonList(that.calendarOption.currentData.currentDate)
                                    that.currentMon=that.calendarOption.currentData.viewTitle
                                }
                            }
                        },
                        views:{
                            //对应月视图
                            dayGridMonth:{
                                displayEventTime:false,//是否显示时间
                                dayCellContent(item){
                                let _date=item.date.getDate();
                                return {html:`<span>${_date}</span>`}
                                }
                            },
                        },
                        firstDay:1,
                        locale: that.lang=="en_us"?"en":"zh-cn",
                        fixedWeekCount: false,
                        dateClick: function(info) {
                            that.currentDay=info.dateStr
                            that.getDayList()
                            console.log(info)
                            let clickTime = info.date
                            let currTime =new Date()
                            $('.fc-day').css('background-color', '#fff');
                            let nodeList = document.querySelectorAll('.myClass');
                            Array.prototype.forEach.call(nodeList,(el)=>{
                                el.classList.remove('myClass')
                            })
                            let today = document.querySelectorAll('.fc-day-today');
                            console.log(today)
                            if(today.length){
                                today[0].classList.remove('fc-day-today')
                            }
                            document.getElementById("calendar").classList.remove("fc-day-today")
                            info.dayEl.classList.add('myClass')
                            info.dayEl.style.backgroundColor = '#FAFAFA'
                        },
                        events:[]
                    });

                    that.calendarOption.render();
                });
            },
            addEvent(eventData){
                var eventList=[]
                for(let i=0;i<eventData.length;i++){
                    let start_date=eventData[i].start_date.split('.')
                    let end_date=eventData[i].end_date.split('.')
                    let classId= eventData[i].event==10?"holidayList":eventData[i].event==20?"eventList":"weekList" 
                    let classNames= eventData[i].day_type==20?[classId,classId+'Half','point']:[classId,'point']
                  
                    if(eventData[i].days.length>1){
                        let endDay=('0' + (parseInt(end_date[2])+1)).slice(-2) 
                        eventList.push({
                            title  : eventData[i].title,
                            start  :start_date[0]+'-'+start_date[1]+'-'+start_date[2],
                            end    :end_date[0]+'-'+end_date[1]+'-'+endDay,
                            classNames:classNames,
                            textColor:'#333'

                        })
                    }else{
                        eventList.push({
                            title  : eventData[i].title,
                            start  :start_date[0]+'-'+start_date[1]+'-'+start_date[2],
                            classNames:classNames,
                            textColor:'#333'
                        })
                    }
                    
                }
                console.log(eventList)
                eventList.forEach(item => {
                    this.calendarOption.addEvent(item);
                });
            },
            tabCalendar(type,filter){
                this.calendarType=type
                let events=this.calendarOption.getEvents()
                events.forEach(item => {
                    item.remove();
                });
                if(type=='all'){
                    this.addEvent(this.calendar_annual)
                }else{
                    if(type==20 && this.filter_event_type.length!=0){
                        let eventData=JSON.parse( JSON.stringify (this.calendarData.calendar_day_list_by_event ) )
                        let eventList=eventData[type].filter((a,i)=>{ 
                            return  this.filter_event_type.some(f=>(parseInt(f) === a.event_type)) 
                        }) 
                        this.addEvent(eventList)
                    }else{

                        this.addEvent(this.calendarData.calendar_day_list_by_event[type])
                    }
                }
                
            },
            filterType(){
                if(this.filter_event_type.length==0){
                    this.currentMonList=this.CopycurrentMonList
                    this.currentDayList=this.CopycurrentDayList
                    this.calendar_annual=this.Copycalendar_annual
                    this.tabCalendar(this.calendarType,'filter')
                }else{
                    this.calendar_annual=this.Copycalendar_annual.filter((a,i)=>{ 
                        return  this.filter_event_type.some(f=>(parseInt(f) === a.event_type)) 
                    })     
                    this.currentMonList=this.CopycurrentMonList.filter((a,i)=>{ 
                        return  this.filter_event_type.some(f=>(parseInt(f) === a.event_type)) 
                    })     
                    this.currentDayList=this.CopycurrentDayList.filter((a,i)=>{ 
                        return  this.filter_event_type.some(f=>(parseInt(f) === a.event_type)) 
                    })  
                    if(this.calendarType=='all' || this.calendarType==20){
                        this.tabCalendar(this.calendarType)                
                    }
                }
            }
        }
    })
    $(document).click(function(event) {
        container.qrcodeBox=false;
        container.scanQrcodeBox=false;
        container.itdevScanQrcodeBox=false;
        clearInterval(container.timer);
    });
        //  设置cookies
    function setCookie(name, value) {
        var exp = new Date();
        exp.setTime(exp.getTime() + 60 * 60 * 1000);
        document.cookie = name + "=" + escape(value) + ";expires=" + exp.toGMTString() + ";path=/";
    }
    //读取cookies
    function getCookie(name) {
        var arr, reg = new RegExp("(^| )" + name + "=([^;]*)(;|$)");
        if (arr = document.cookie.match(reg))
            return unescape(arr[2]);
        else
            return null;
    }
</script>