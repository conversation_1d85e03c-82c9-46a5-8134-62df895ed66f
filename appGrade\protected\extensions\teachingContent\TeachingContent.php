<?php
class TeachingContent extends CWidget{
	public $classId;
    public $yId;
    public $weekNum;
    public $month;

    public function init(){
        $cs = Yii::app()->clientScript;
        $cs->registerCssFile(Yii::app()->theme->baseUrl.'/widgets/lunch/lunch.css?t='.Yii::app()->params['refreshAssets']);
        $cs->registerCssFile(Yii::app()->theme->baseUrl.'/css/widgetbase.css?t='.Yii::app()->params['refreshAssets']);
    }

	public function run(){
        Yii::import('common.models.calendar.CalendarWeek');
        Yii::import('common.models.portfolio.TeachingMonthly');

        $lKey = Yii::app()->language == 'zh_cn' ? 'cn' : 'en';

        $weeks = array();
        $criteria = new CDbCriteria();
        $criteria->compare('yid', $this->yId);
        $criteria->order='weeknumber asc';
        $items = CalendarWeek::model()->findAll($criteria);
        foreach($items as $item){
            $weeks[$item->weeknumber] = $item->monday_timestamp;
        }

//        $months = array();
//        foreach($weeks as $week){
//            $_month = date('Y-m', $week);
//            $months[$_month] = $_month;
//        }
        $months = TeachingMonthly::configType();
        $this->month = $this->weekNum ? date('Y-m', $weeks[$this->weekNum]) : $this->month;

        $criteria = new CDbCriteria();
        $criteria->compare('classid', $this->classId);
        $criteria->compare('yid', $this->yId);
        $criteria->compare('unit_type', $this->month);
        //$criteria->compare('month', '2020-11');
//        $criteria->compare('stat_'.$lKey, 20);
        $model = TeachingMonthly::model()->with('content')->find($criteria);

        if(is_object($model) && is_object($model->content))
            $model->content->expVista();

        $indexMonths = array_values($months);
        $_indexMonths = array_flip($indexMonths);
        $pmonth = isset($indexMonths[$_indexMonths[$this->month]-1]) ? $indexMonths[$_indexMonths[$this->month]-1] : reset($indexMonths);
        $nmonth = isset($indexMonths[$_indexMonths[$this->month]+1]) ? $indexMonths[$_indexMonths[$this->month]+1] : end($indexMonths);
        $this->render('teachingcontent', array('model'=>$model, 'months'=>$months, 'pmonth'=>$pmonth, 'nmonth'=>$nmonth));
    }
	
}