<?php

class OrderController extends BranchBasedController
{
    public $actionAccessAuths = array(
        'admin'     => 'oHQOperationsView',
        'Confirm'   => 'oHQOperationsAdmin',
        'Pack'      => 'oHQOperationsAdmin',
        'Orderlist' => 'oHQOperationsView',
        'Send'      => 'oHQOperationsAdmin',
        'Split'     => 'oHQOperationsAdmin',
        'Godone'    => 'oHQOperationsAdmin',
    );
    
    public $cAdmin = false;	//是否有校园管理权限
    public $oAdmin = false; //是否总部后勤部权限
    public $defaultAction = 'admin';
    public $dialogWidth=500;
    
    public function init() {
        parent::init();

        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";
        Yii::import('common.models.points.*');

        $this->modernMenuFlag = 'hqOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site','HQ Operations');

        $this->branchSelectParams['hideOffice'] = true;
        $this->branchSelectParams['urlArray'] = array('//moperation/order/admin');

        $this->oAdmin = Yii::app()->user->checkAccess('oHQOperationsAdmin');
    }
    
    public function actionSelect()
	{
        $this->render('//layouts/common/branchSelect');
	}

    /**
     * 积分兑换订单管理主页面
     * @param string $branchId
     * @param string $type
     */
    public function actionAdmin($branchId='', $type='opgcount')
	{
		$branchId = $this->branchId;
        $this->mainMenu = array(
			array(
				'label' => '订单管理',
				'url' => array('/operations/order/admin')
			),
			array(
				'label' => '商品管理',
				'url' => array('/operations/product/admin')
			),
		);
//        $this->branchSelectParams["showList"] = true;
        $this->branchSelectParams["urlArray"] = array("//moperation/order/admin", 'type'=>$type);
        
        $criteria=new CDbCriteria;
        $criteria->compare('t.status', array(PointsStatus::STATS_CREATED, PointsStatus::STATS_CONFIRMED, PointsStatus::STATS_READYTOSHIP, PointsStatus::STATS_SHIPPING, PointsStatus::STATS_RECEIVED));
        if ($branchId){
            $criteria->compare('t.schoolid', $branchId);
        }
        $allOrders = PointsOrder::model()->with(array('Product', 'ChildProfile', 'school', 'orderUser', 'updateUser'))->findAll($criteria);
        
        $sData10 = array();
        $sData20 = array();
        $sData110 = array();
        $sData120 = array();
        $sData130 = array();
        $sData210 = array();
        
        foreach ($allOrders as $order){
            if ($order->status == PointsStatus::STATS_CREATED && $order->category == 'order'){
                $sData10[$order->id] = $order;
            }
            elseif ($order->status == PointsStatus::STATS_CONFIRMED && $order->category == 'order'){
                $sData20[$order->id] = $order;
            }
            elseif ($order->status == PointsStatus::STATS_READYTOSHIP && $order->category == 'pack'){
                $sData110[$order->id] = $order;
            }
            elseif ($order->status == PointsStatus::STATS_SHIPPING && $order->category == 'pack'){
                $sData120[$order->id] = $order;
            }
            elseif ($order->status == PointsStatus::STATS_RECEIVED && $order->category == 'order'){
                $sData130[$order->id] = $order;
            }
        }
        
        $criteria = new CDbCriteria;
        $criteria->compare('t.status', PointsStatus::STATS_COMPLETED);
        $criteria->compare('t.schoolid', $branchId);
        $criteria->order='t.update_timestamp desc';
        $criteria->with=array('Product', 'ChildProfile', 'school', 'orderUser', 'updateUser');
        $sData210 = new CActiveDataProvider('PointsOrder', array(
            'criteria'=>$criteria,
        ));

        $schoolsCount = array();
        if($type == 'opgcount')
            $sql = 'select schoolid, count(*) as c from '.PointsOrder::model()->tableName().' where status in ('.PointsStatus::STATS_CREATED.','.PointsStatus::STATS_CONFIRMED.','.PointsStatus::STATS_READYTOSHIP.') and category="order" group by schoolid';
        else
            $sql = 'select schoolid, count(*) as c from '.PointsOrder::model()->tableName().' where status in ('.PointsStatus::STATS_SHIPPING.','.PointsStatus::STATS_RECEIVED.') and category="order" group by schoolid';
        $sc=Yii::app()->db->createCommand($sql)->queryAll();
        foreach ($sc as $_sc){
            $schoolsCount[$_sc['schoolid']]=$_sc['c'];
        }

		$this->render('admin',array(
            'branchId'=>$branchId,
            'sData10'=>$sData10,
            'sData20'=>$sData20,
            'sData110'=>$sData110,
            'sData120'=>$sData120,
            'sData130'=>$sData130,
            'sData210'=>$sData210,
            'schoolsCount'=>CJSON::encode($schoolsCount),
		));
	}

    /**
     * 确认订单
     */
    public function actionConfirm()
    {
        $oIds = Yii::app()->request->getPost('orderid', array());
        
        $this->addMessage('state', 'fail');
        $this->addMessage('message', Yii::t('message','Failed!'));
        if ($this->oAdmin && $oIds){
            
            foreach ($oIds as $id){
                $model = PointsOrder::model()->findByPk($id);
                if ($model->status == PointsStatus::STATS_CREATED){
                    $model->status = PointsStatus::STATS_CONFIRMED;
                    $model->update_timestamp = time();
                    $model->update_userid = Yii::app()->user->id;
                    $model->save();
                    
                    $status = new PointsStatus;
                    $status->itemid = $id;
                    $status->status = PointsStatus::STATS_CONFIRMED;
                    $status->update_timestamp = time();
                    $status->update_userid = Yii::app()->user->id;
                    $status->save();
                }
            }
            $this->addMessage('state', 'success');
            $this->addMessage('message', Yii::t('message','success'));
            $this->addMessage('refresh', true);
        }
        $this->showMessage();
    }

    /**
     * 打包订单
     */
    public function actionPack()
    {
        $branchId = $this->branchId;
        $this->addMessage('state', 'fail');
        $this->addMessage('message', Yii::t('message','Failed!'));
        $oIds = Yii::app()->request->getPost('orderid', array());
        
        if ($this->oAdmin && $oIds){
            $model = new PointsOrder;
            $model->category = 'pack';
            $model->schoolid = $branchId;
            $model->created_timestamp = time();
            $model->created_userid = Yii::app()->user->id;
            $model->status = PointsStatus::STATS_READYTOSHIP;
            $model->quantity = count($oIds);
            $model->update_timestamp = time();
            $model->update_userid = Yii::app()->user->id;
            $model->save();

            $status = new PointsStatus;
            $status->itemid = $model->id;
            $status->status = PointsStatus::STATS_READYTOSHIP;
            $status->update_timestamp = time();
            $status->update_userid = Yii::app()->user->id;
            $status->save();

            $criteria = new CDbCriteria;
            $criteria->compare('id', $oIds);
            PointsOrder::model()->updateAll(array( 'status'=>PointsStatus::STATS_READYTOSHIP, 'pack_id'=>$model->id), $criteria);

            $this->addMessage('state', 'success');
            $this->addMessage('message', Yii::t('message','success'));
            $this->addMessage('refresh', true);
        }
        $this->showMessage();
        
    }

    /**
     * 包裹里的订单列表
     * @param int $id
     */
    public function actionOrderlist($id=0)
    {
        if ($id){
            $this->dialogWidth = 800;
            $this->layout='//layouts/dialog';
            $model = PointsOrder::model()->findByPk($id);
            
            $orders = PointsOrder::model()->findAllByAttributes(array('pack_id'=>$id));
            
            Yii::app()->clientScript->registerScriptFile( Yii::app()->request->baseUrl.'/themes/base/js/jquery.jPrintArea.js' );
            
            $this->render('orderlist', array( 'model'=>$model, 'orders'=>$orders ));
        }
    }

    /**
     * 发送包裹到校园
     * @param int $id
     */
    public function actionSend($id=0)
    {
        if ($this->oAdmin && $id){
            if (isset($_POST['PointsOrder']) && isset($_POST['PointsStatus'])){
                $this->addMessage('state', 'fail');
                $criteria = new CDbCriteria;
                $criteria->condition = "(id = :id or pack_id = :id) and status=:s";
                $criteria->params = array(':id'=>$id, ':s'=>PointsStatus::STATS_READYTOSHIP);

                $items = PointsOrder::model()->findAll($criteria);
                foreach ($items as $item){
                    $item->status = PointsStatus::STATS_SHIPPING;
                    if ($item->category == 'pack'){
                        $item->shipinfo = isset($_POST['PointsOrder']['shipinfo']) ? $_POST['PointsOrder']['shipinfo'] : '';
                    }
                    $item->update_timestamp = time();
                    $item->update_userid = Yii::app()->user->id;
                    $item->save();
                }
                $mpoint = new PointsStatus;
                $mpoint->itemid = $item->id;
                $mpoint->status = PointsStatus::STATS_SHIPPING;
                $mpoint->memo = isset($_POST['PointsStatus']['memo']) ? $_POST['PointsStatus']['memo'] : '';
                $mpoint->update_timestamp = time();
                $mpoint->update_userid = Yii::app()->user->id;
                $mpoint->save();

                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message','success'));
                $this->addMessage('refresh', true);
                $this->showMessage();
            }
            
            $this->layout='//layouts/dialog';
            $criteria = new CDbCriteria;
            $criteria->compare('pack_id', $id);
            $models = new CActiveDataProvider('PointsOrder', array(
                'criteria'=>$criteria,
            ));
            
            $omodel = new PointsOrder;
            $smodel = new PointsStatus;
            
            $this->render('pack', array( 'models'=>$models, 'omodel'=>$omodel, 'smodel'=>$smodel ));
        }
    }

    /**
     * 把包裹拆分（还原）成订单
     * @param int $id
     */
    public function actionSplit($id=0)
    {
        $ret = array('status'=>'fail');
        if ($this->oAdmin && $id){
            
            PointsOrder::model()->deleteByPk($id);
            
            PointsStatus::model()->deleteAllByAttributes(array('itemid'=>$id), 'status=:s', array(':s'=>PointsStatus::STATS_READYTOSHIP));
            
            PointsOrder::model()->updateAll(
                    array(
                        'status'=>PointsStatus::STATS_CONFIRMED,
                        'pack_id'=>'',
                        'update_timestamp'=>time(),
                        'update_userid'=>Yii::app()->user->id,
                    ),
                    'pack_id=:id and status=:s',
                    array(':id'=>$id, ':s'=>PointsStatus::STATS_READYTOSHIP)
            );
            
            $ret['status'] = 'success';
        }
        echo CJSON::encode($ret);
    }

    /**
     * 订单状态直接变成已发放
     * @param int $id
     */
    public function actionGodone($id=0)
    {
        $this->addMessage('state', 'fail');
        $this->addMessage('message', Yii::t('message','Failed!'));
        if ($this->oAdmin && $id){
            if ( PointsOrder::model()->updateByPk($id, array('status'=>210)) ) {
                $status = new PointsStatus;
                $status->itemid = $id;
                $status->status = PointsStatus::STATS_COMPLETED;
                $status->update_timestamp = time();
                $status->update_userid = Yii::app()->user->id;
                $status->save();
                $this->addMessage('state', 'success');
            }
            else {
                $this->addMessage('state', 'fail');
            }
        }
        $this->showMessage();
    }
}
