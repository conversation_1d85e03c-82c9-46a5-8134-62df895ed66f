<div class="h_a"><?php echo Yii::t('teaching', 'Development Checklist');?></div>
<div class="prompt_text">
	<ul>
		<li><?php echo Yii::t('teaching', 'Instructions');?></li>
	</ul>
</div>

<div class="table_full-">
	<table width="100%">
		<colgroup>
			<col width=180></col>
		</colgroup>
		<tbody>
			<tr>
				<th valign=top>
					<div id="list-box">
						<?php
						?>
						
						<ul id="child-list" class="child-list child-checklist"> <!--place holder-->
						</ul>
					</div>
				</th>
				<td valign="top">
					<div style="display:none;" id="sreport-zone" class="table_list"> <!--place holder-->
					<form class="J_ajaxForm" action="<?php echo $this->createUrl('//teaching/default/saveSemesterReport');?>" method="post">
						<?php foreach($srptTemplate as $_tid => $_template):?>
						<!--template <?php echo $_tid;?> -->
						<div id="template-<?php echo $_tid;?>" style="min-height:1000px">
							<?php
							$inputIndex = 0;
							foreach($_template['pages'] as $_pid=>$_page):
							?>
							<div class="h_a"><?php echo sprintf("page %d", $_pid);?></div>
								<div class="table_full">
									<table width="100%">
										<colgroup>
											<col width="180">
										</colgroup>
										<tbody>
										<?php foreach($_page['blocks'] as $_bid=>$_block):?>
											<tr>
												<th><?php echo $_block['desc']?></th>
												<td>
													<?php if(isset($_block['readonly'])):?>
														 READONLY
													<?php elseif($_block['content']):
														$_contextid = $_bid;
													?>
														<ul id="context-<?php echo $_contextid;?>" class="rpt-block">
															<?php foreach($_block['content'] as $_content):
																$_name = 'rptData[' . $_contextid . '][' . $_content . ']';
															?>
																	<?php
																	$htmlOptions = array(
																		'pageId'=>$_pid,
																		'contextId'=>$_contextid,
																		'class'=>'inputItem',
																		'inputIndex'=>$inputIndex,
																		'inputType'=>$_content,
																		'itemName' => $_name,
																		'itemId' => CHtml::getIdByName($_name),
																		'photoToOther' => 0
																	);
																	
																	if( isset($_block['photoToOther']) && $_block['photoToOther'] ){
																		$htmlOptions['photoToOther'] = 1;
																	}
																	
																	echo CHtml::openTag('li', $htmlOptions);
																	echo CHtml::closeTag('li');			
																	$inputIndex++;
																	?>
															<?php endforeach; ?>
														</ul>
													<?php endif;?>													
												</td>
											</tr>
										<?php endforeach;?>
									</tbody>
								</table>
							</div>
							
							<?php endforeach;?>
						</div>
						<!--template <?php echo $_tid;?> end-->
						
						<input type="hidden" id="rptTempId" name="rptTempId" value="<?php echo $_tid;?>" />
						<?php endforeach;?>
						
						<div class="btn_wrap">						
							<div class="btn_wrap_pd">
								<label class="mr15">
								<?php echo CHtml::checkBox("finish[fall]", false, array('semester'=>'fall', 'class'=>'markFinish')); ?>
								第一学期完成标记
								</label>
								<button class="btn btn_submit J_ajax_submit_btn" type="submit">保存内容</button>
								<div id="J_message_send_tip"></div>
							</div>
						</div>
						<?php echo CHtml::hiddenField('selectedChildid', 0);?>
						<?php echo CHtml::hiddenField('selectedSemester', 0);?>
						<?php echo CHtml::hiddenField('classid', $this->currentClassId);?>
						<?php echo CHtml::hiddenField('weeknum', $this->currentWeeknum);?>
						<?php echo CHtml::hiddenField('startyear', $this->currentStartYear);?>
						<?php echo CHtml::hiddenField('yid', $this->currentCalendarId);?>
						<?php echo CHtml::hiddenField('securityCode', md5(sprintf('%s&%s&%s&%s&%s', $this->currentClassId,$this->currentWeeknum,$this->currentStartYear,Yii::app()->user->getId(),$this->securityKey)));?>						
					</form>

					<?php
					$nameListStr = implode(',', $nameList);
					?>

					</div>
					<div id="loading" style="display:none;padding-left:250px;line-height:30px;"><?php echo Yii::t('global','Loading Data...');?></div>
					&nbsp;
				</td>
			</tr>
		</tbody>
	</table>
</div>

<!--=======================孩子媒体搜索===========================-->
<div class="core_pop_wrap" id="J_mediaSearch_pop" style="display:none;">
	<div class="core_pop">
		<div style="width:800px;">
			<?php echo CHtml::beginForm($this->createUrl('//ivyMediaResponder/mediaSearch'),'GET',array('class'=>'J_ajaxForm'));?>
			<div class="pop_top">
				<a href="" id="J_mediaSearch_pop_x" class="pop_close"><?php echo Yii::t('global','Close');?></a>
				<strong id='d_tit'><?php echo Yii::t('teaching', '每周分配照片'); ?></strong>
			</div>
			<div class="pop_cont">
				<div class="pop_table weeklyMediaByWeek" style="height:500px;">
					<table width="100%">
						<tbody>
							<tr>
								<td width=190>
									<?php echo CHtml::dropDownList('MediaSearch[ldid]',null, $ldTerms,
										array('class'=>'select_3','empty'=>Yii::t('learning', 'All Learning Domain')));?>
								</td>
								<td width=130>
									<?php echo CHtml::dropDownList('MediaSearch[childid]',null, null,
										array('class'=>'select_2','empty'=>Yii::t('learning', 'All Child')));?>
								</td>
								<td width=130>
									<?php echo CHtml::dropDownList('MediaSearch[weeknum]',null, null,
										array('class'=>'select_2','empty'=>Yii::t('learning', 'All Week')));?>
								</td>
								<td width=130>
									<?php echo CHtml::textField('MediaSearch[freetag]','',
										array('class'=>'length_2 input','placeholder'=>Yii::t('learning', 'Free tag')));?>
								</td>
								<td>
									<button class="J_ajax_submit_btn btn" id="J_mediaSearch_pop_sub"><?php echo Yii::t('global','Filter');?></button>
								</td></tr>
						</tbody>
					</table>
					<div id="media-pager" class="page-container"></div>
					<div class="clear"></div>
					<ul id="media-list">
					</ul>					
				</div>
			</div>
			<div class="pop_bottom tac">
                <button id="J_mediaSearch_pop_close" class="btn fr"><?php echo Yii::t('global','Cancel');?></button>
				<button type="button" onclick=""  class="btn btn_submit mr10 fr"><?php echo Yii::t('global','OK');?></button>
			</div>
			<?php echo CHtml::hiddenField('classid', $this->currentClassId);?>
			<?php echo CHtml::hiddenField('weeknum', $this->currentWeeknum);?>
			<?php echo CHtml::hiddenField('startyear', $this->currentStartYear);?>
			<?php echo CHtml::hiddenField('yid', $this->currentCalendarId);?>
			<?php echo CHtml::hiddenField('securityCode', md5(sprintf('%s&%s&%s&%s&%s', $this->currentClassId,$this->currentWeeknum,$this->currentStartYear,Yii::app()->user->getId(),$this->securityKey)));?>
			<?php echo CHtml::endForm();?>
		</div>
	</div>
</div>
<!--===========================结束==========================-->


<!-- Templates -->

<script type="text/template" id="item-namelist-template">
<em title="1st Semester" class="devflag flag-1"></em>
<em title="2nd Semester" class="devflag flag-2"></em>
<img class="face" src="<% print(OAUploadBaseUrl) %>/childmgt/<%= photo %>"><%= name %>
<div class="s-rpt">
	<a href="javascript:void(0)" semester="10">第一学期报告</a>
	<a href="javascript:void(0)" semester="20">第二学期报告</a>
</div>
</script>

<style>
.btn_wrap{left: 215px !important;border-left: #dedede;}
.child-list li {border-top: 2px solid #f3f3f3;border-bottom: 2px solid #f3f3f3;}
.child-list li:hover {border-color: #FE8402;}
.child-list li div.s-rpt{border-top: 2px solid #f3f3f3;border-bottom: 2px solid #f3f3f3; border-right: 4px solid #f3f3f3;}
.child-list li:hover div.s-rpt{border-color: #FE8402;}
.sep{margin-bottom: 10px; border-bottom: 1px dotted #dedede; padding-bottom: 10px;}
img.rpt-photo-preview{border: 3px solid #f3f3f3;max-width:360px;}
img.rpt-photo-preview:hover{border: 3px dotted #666;cursor:pointer}
.assignBox {border: 1px dotted #c6c6c6; padding: 10px;}
.assignBox label{margin-right:20px; display: inline-block;width: 200px;}
</style>

<script>
var OAUploadBaseUrl = '<?php echo Yii::app()->params['OAUploadBaseUrl'];?>';
var nameList;
var learningDomains;
var currentInputIndex;
var emptySemesterData=null;
var currentSemesterData=null;
var currentPhotoData=null;
var currentChildId=null;
var loadMultipleAssign=false;

function enableMultipleAssign(obj){
	var checked = $(obj).is(":checked");
	var parent = $(obj).parents(".multiple-assign");
	
	if(!checked){
		parent.children('div.assignBox').remove();
		return;
	}
	var li = $(obj).parents('li');
	var mediaid = $('#'+li.attr('itemid')).val();
	if( mediaid == 0){
		alert('Please chose photo first');
		$(obj).removeAttr('checked');
		return false;
	}
	var contextid = li.attr('contextid');
	var name = 'rptData['+contextid+'][others]';
	var id = 'rptData_'+contextid+'_others';
	
	if(!loadMultipleAssign){
		loadMultipleAssign=true;
		$(obj).siblings('div.assignBox').remove();
		var div =$('<div class="assignBox"></div>').attr('id', 'assign-'+contextid);
		div.append($('<span class="simpleloading"></span>'));
		
		$(obj).parents('.multiple-assign').append(div);

		$.ajax({
			url: '<?php echo $this->createUrl('//teaching/default/fetchAssignMany');?>',
			dataType: 'json',
			type: 'POST',
			data:{
				mediaid: mediaid,
				context: contextid,
				classid: <?php echo $this->currentClassId;?>,
				startyear: <?php echo $this->currentStartYear;?>,
				semester: $('#selectedSemester').val()
			}
		}).done(function(data){
			if(data.state='success'){
				div.html("");
				for(i in nameList){
					var label = $('<label></label');
					var _cbname = "rptData[assignMany]["+contextid+"][]";
					var checkbox = $('<input type="checkbox" class="assignMany-'+contextid+'" name='+_cbname+' />').val(nameList[i].id).attr('childid', nameList[i].id);
					if(in_array(nameList[i].id, data.data[mediaid])){
						checkbox.attr('checked','checked');
					}
					if(currentChildId == nameList[i].id){
						checkbox.attr('checked','checked');
						checkbox.attr('disabled','disabled');
						div.append($('<input type="hidden" name="'+_cbname+'" value="0" />'));
					}
					label.append(checkbox);
					label.append($('<span></span>').html(nameList[i].name));
					div.append(label);					
				}
			}
		});
		loadMultipleAssign=false;
	}
}

$(function(){
	nameList = [<?php echo $nameListStr;?>];
	nameListColl = new Backbone.Collection;
	
	var singleChildView = Backbone.View.extend({
		tagName: 'li',
		template: _.template($('#item-namelist-template').html()),
		initialize: function(){
		
		},
		render: function(){
			this.$el.attr('childid', this.model.get('id'));
			this.$el.html( this.template(this.model.attributes) );
			if(this.model.get("10")==20){this.$el.children("em.flag-1").addClass('finished');}
			if(this.model.get("20")==20){this.$el.children("em.flag-2").addClass('finished');}
			return this;
		}
	});
	
	var ChildListView = Backbone.View.extend({
		el: $('#child-list'),
		initialize: function(){
			this.listenTo(nameListColl, 'reset', this.addAll);
			nameListColl.reset(nameList);
		},
		addAll: function(){
			this.$el.html("");
			nameListColl.each(this.addOne, this);
			iniChildClick();
		},
		addOne: function(childModel){
			var item = new singleChildView({model:childModel});
			this.$el.append(item.render().el);
		}
	});

	var pageChildList = new ChildListView;
	
	learningDomains = <?php echo $ldlist?>;
	
	//******显示孩子结束
	
	
	function iniChildClick(){
		$('div.s-rpt a').click(function(){
			currentSemesterData = null;
			currentPhotoData = null;
			$('#sreport-zone').hide();
			$('#loading').show();
			var li=$(this).parents('li');
			childid = li.attr('childid');
			
			$('#selectedChildid').val(childid);
			$('#selectedSemester').val($(this).attr('semester'));
			$.ajax({
				url: '<?php echo $this->createUrl('//teaching/default/fecthSemesterReport');?>',
				dataType: 'json',
				type: 'POST',
				data:{
					childid:childid,
					semester:$(this).attr('semester'),
					startyear: <?php echo $this->currentStartYear;?>,
					classid: <?php echo $this->currentClassId;?>,
					templateid: $('#rptTempId').val()
				}
			}).done(function(data){
				li.siblings().removeClass('current');
				li.addClass('current');
				currentSemesterData = data.data.reportData;
				currentPhotoData = data.data.photos;
				currentChildId = childid;
				renderInputItem();
				$('#loading').hide();
				$('#sreport-zone').show(200);
			});
		});
	}

	function renderInputItem(){
		$( 'li.inputItem' ).each(function( index ) {
			$(this).html("");
			var _name = $(this).attr('itemname');
			var _id = $(this).attr('itemid');
			var _contextid = $(this).attr('contextid');
			var _inputtype = $(this).attr('inputType');
			var _photoToOther = $(this).attr('photoToOther');
			
			if(_inputtype == 'textArea'){
				var textArea = $('<textarea class="mb5 length_6"></textarea>').attr('id',_id).attr('name',_name);
				textArea.html(currentSemesterData[_contextid][_inputtype]);
				$(this).append(textArea);
			}else if(_inputtype == 'learningDomain'){
				var ldSelect = $('<select class="mb5"></select>').attr('id',_id).attr('name',_name);
				ldSelect.append('<option>Please Select</option>');
				for(var i in learningDomains){
					ldSelect.append('<option value="'+i+'">'+learningDomains[i]+'</option>');
				}
				ldSelect.val(currentSemesterData[_contextid][_inputtype]);
				$(this).append(ldSelect);
			}else if(_inputtype == 'photo'){
				var src = '';
				if( currentPhotoData[currentSemesterData[_contextid][_inputtype]]){
					src = currentPhotoData[currentSemesterData[_contextid][_inputtype]];
				}else{
					src = '<?php echo Yii::app()->theme->baseUrl . "/images/plus2.png";?>';
				}
				
				var img = $('<img  class="rpt-photo-preview" />')
					.attr("src", src)
					.attr("onclick", 'openD(this)');
				var hiddenInput = $('<input type="hidden"/>').attr('id',_id).attr('name',_name).val(currentSemesterData[_contextid][_inputtype]);
				
				$(this).append(img);
				$(this).append(hiddenInput);
				
				if(_photoToOther > 0){
					var div = $('<div class="multiple-assign"></div>');
					var lb = $('<label></label>');
					var cb = $('<input onclick="enableMultipleAssign(this)" type="checkbox" value="0" name="rptData[assignMany]['+_contextid+'][]" />');
					var sp = $('<span></span>').html('将同一照片分配给其他孩子');
					lb.append(cb).append(sp);
					div.append(lb);
					$(this).append(div);
				}
			}
		});
	}
});

</script>

<script>
head.use('dialog', 'draggable', function() {
    region_pop = $('#J_mediaSearch_pop');
    region_pop.draggable( { handle : '.pop_top'} );
    
    $('#J_mediaSearch_pop_x, #J_mediaSearch_pop_close').on('click', function(e){
		e.preventDefault();
		region_pop.hide();
	});
    
});

$(function() {
    $( ".drEle" ).resizable({
      handles: "se"
    }).draggable();
	
	$('img.assets-image').load ( function() {
		$(this).attr('owidth', $(this).width());
		$(this).attr('oheight', $(this).height());
	});
	
	$('img.assets-image').click(function(e){
		var img = new Image();
	
		var w = $(this).width();
		if(e.ctrlKey){
			$(this).css('width', w-5);
		}
		else{
			$(this).css('width', w+5);
		}
	});
});

function openD(obj){
	currentInputIndex = $(obj).parent("li").attr('inputindex');
		$('#J_mediaSearch_pop').show().css({
			left : ($(window).width() - region_pop.outerWidth())/2,
			top : ($(window).height() - region_pop.outerHeight())/2 //+ $(document).scrollTop()
		});
}
function getMediaList(obj){
	$.ajax({
		url: '<?php echo Yii::app()->request->hostInfo;?>'+ $(obj).attr('href'),
		dataType: 'json',
		type: 'GET',
	}).done(function(data){
		postMediaSearch(data.data);
	});
}
function renderMediaList(mediaList){
	var ul = $('#media-list');
	ul.html('');
	if(mediaList.length==0){
		
	}else{
		$('#media-list').show();
		for(var i in mediaList){
			var item = $('<li></li>')
				.attr('id', 'mc'+i)
				.attr('year', mediaList[i].year)
				.attr('sid', mediaList[i].sid)
				.attr('mediaid', mediaList[i].mediaid)
				.html('<img mediaid="'+mediaList[i].mediaid+'" class="preview" onclick="selectPhoto(this)" title="单击选中" src="'+mediaList[i].thumb+'">');
			ul.append(item);
		}
	}
}

function selectPhoto(obj){
	$('#J_mediaSearch_pop_x').click();
	src = $(obj).attr("src");
	src = src.replace("/thumbs/", '/');
	
	var li = $("li[inputindex|='"+currentInputIndex+"']");
	li.children('img').hide().attr('src', src).show(300);
	li.children('input[type|="hidden"]').val($(obj).attr("mediaid"));
}

function postMediaSearch(data){
	$('#media-pager').hide();
	$('#media-list').hide();
	$('#media-pager').html(data.pages);
	$('#media-pager a').unbind('click');
	
	renderMediaList(data.mediaData);
	
	$('#media-pager a').click(function(){
		getMediaList(this);
		return false;
	});
	$('#media-pager').show();
}
</script>

<script>
var OAUploadBaseUrl = '<?php echo Yii::app()->params['OAUploadBaseUrl'];?>';
var nameList = <?php echo CJSON::encode($nameList);?> ;
var maxweek = <?php echo $maxweek;?>;
head.js('<?php echo Yii::app()->themeManager->baseUrl . '/base/js/teaching/semesterrpt.js?t='.time(); ?>');
</script>