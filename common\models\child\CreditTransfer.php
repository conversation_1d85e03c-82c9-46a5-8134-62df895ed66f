<?php

/**
 * This is the model class for table "ivy_child_credit_transfer".
 *
 * The followings are the available columns in table 'ivy_child_credit_transfer':
 * @property integer $id
 * @property integer $fromchildid
 * @property integer $tochildid
 * @property integer $fromcalendarid
 * @property integer $tocalendarid
 * @property string $fromschoolid
 * @property string $toschoolsid
 * @property string $type
 * @property double $amount
 * @property integer $fromcid
 * @property integer $tocid
 * @property integer $updated_timestamp
 * @property integer $userid
 */
class CreditTransfer extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @return CreditTransfer the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_child_credit_transfer';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('fromschoolid, toschoolsid, updated_timestamp, userid', 'required'),
			array('fromchildid, tochildid, fromcalendarid, tocalendarid, fromcid, tocid, updated_timestamp, userid', 'numerical', 'integerOnly'=>true),
			array('amount', 'numerical'),
			array('fromschoolid, toschoolsid, type', 'length', 'max'=>255),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, fromchildid, tochildid, fromcalendarid, tocalendarid, fromschoolid, toschoolsid, type, amount, fromcid, tocid, updated_timestamp, userid', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'fromchildid' => 'Fromchildid',
			'tochildid' => 'Tochildid',
			'fromcalendarid' => 'Fromcalendarid',
			'tocalendarid' => 'Tocalendarid',
			'fromschoolid' => 'Fromschoolid',
			'toschoolsid' => 'Toschoolsid',
			'type' => 'Type',
			'amount' => 'Amount',
			'fromcid' => 'Fromcid',
			'tocid' => 'Tocid',
			'updated_timestamp' => 'Updated Timestamp',
			'userid' => 'Userid',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('fromchildid',$this->fromchildid);
		$criteria->compare('tochildid',$this->tochildid);
		$criteria->compare('fromcalendarid',$this->fromcalendarid);
		$criteria->compare('tocalendarid',$this->tocalendarid);
		$criteria->compare('fromschoolid',$this->fromschoolid,true);
		$criteria->compare('toschoolsid',$this->toschoolsid,true);
		$criteria->compare('type',$this->type,true);
		$criteria->compare('amount',$this->amount);
		$criteria->compare('fromcid',$this->fromcid);
		$criteria->compare('tocid',$this->tocid);
		$criteria->compare('updated_timestamp',$this->updated_timestamp);
		$criteria->compare('userid',$this->userid);

		return new CActiveDataProvider(get_class($this), array(
			'criteria'=>$criteria,
		));
	}
}