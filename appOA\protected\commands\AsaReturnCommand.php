<?php

/*
 * To change this template, choose Tools | Templates
 */
class AsaReturnCommand extends CConsoleCommand
{
    public function init() {
        parent::init();
        Yii::import('common.models.asainvoice.*');
    }

    public function actionExportItem()
    {
        $csv = $this->_t('账单ItemId').",";
        $csv .= $this->_t('课程组ID').",";
        $csv .= $this->_t('课程ID').",";
        $csv .= $this->_t('孩子ID').",";
        $csv .= $this->_t('支付方式').",";
        $csv .= $this->_t('退费状态').",";
        $csv .= $this->_t('总节数').",";
        $csv .= $this->_t('退费节数').",";
        $csv .= $this->_t('课程单价').",";
        $csv .= $this->_t('退费钱数').",";
        $csv .= $this->_t('余额').",";
        $csv .= $this->_t('总钱数')."\r\n";

        $csvNo = $this->_t('孩子ID').",";
        $csvNo .= $this->_t('课程ID')."\r\n";

        $SLTRetired = $this->_t('item_id').",";
        $SLTRetired .= $this->_t('孩子ID').",";
        $SLTRetired .= $this->_t('课程ID')."\r\n";
        $file = fopen('./asaReturnSLT.csv', 'r+');
        while ($arr = fgetcsv($file)) {
            $crit = new CDbCriteria();
            $crit->compare('t.course_id', $arr[3]);
            $crit->compare('asainvoice.childid', $arr[0]);
            $crit->compare('asainvoice.status', 20);
            $crit->with = 'asainvoice';
            $itemModel = AsaInvoiceItem::model()->findAll($crit);
            if($itemModel){
                foreach ($itemModel as $item) {
                    if(!$item->refund_status) {
                        if ($item->actual_total >= $arr[12]) {
                            $csv .= $this->_t($item->id) . ",";
                            $csv .= $this->_t($item->course_group_id) . ",";
                            $csv .= $this->_t($item->course_id) . ",";
                            $csv .= $this->_t($arr[0]) . ",";
                            $csv .= $this->_t($item->asainvoice->pay_type) . ",";
                            $csv .= $this->_t($item->refund_status) . ",";
                            $csv .= $this->_t($item->class_count) . ",";
                            $csv .= $this->_t($arr[10]) . ",";
                            $csv .= $this->_t($item->unit_price) . ",";
                            $csv .= $this->_t($arr[12]) . ",";
                            $csv .= $this->_t($item->actual_total) . ",";
                            $csv .= $this->_t($item->actual_total) . "\r\n";
                        }
                    } else {
                        $SLTRetired .= $this->_t($item->id) . ",";
                        $SLTRetired .= $this->_t($arr[0]) . ",";
                        $SLTRetired .= $this->_t($arr[3]) . "\r\n";
                    }
                }
            }else{
                $csvNo .= $this->_t($arr[0]) . ",";
                $csvNo .= $this->_t($arr[3]) . "\r\n";
            }
        }
        file_put_contents('asaReturnWechatSLT'.'.csv', $csv);
        file_put_contents('asaReturnWechatWrongSLT' . '.csv', $csvNo);
        file_put_contents('asaReturnDataSLT'.'.csv', $SLTRetired);
    }


    /*
     *
     */
    public function actionSaveReturn(){

        Yii::import('common.models.*');
        Yii::import('common.components.AliYun.MQ.MQProducer');
        $i = 1;
        $file = fopen('./asaReturnWechatSLT.csv', 'r+');
        $csv = $this->_t('孩子ID').",";
        $csv .= $this->_t('课程ID').",";
        $csv .= $this->_t('ItemId').",";
        $csv .= $this->_t('支付方式').",";
        $csv .= $this->_t('退费金额').",";
        $csv .= $this->_t('余额').",";
        $csv .= $this->_t('总额')."\r\n";

        $csvNo = $this->_t('孩子ID').",";
        $csvNo .= $this->_t('课程ID').",";
        $csvNo .= $this->_t('ItemId').",";
        $csvNo .= $this->_t('支付方式').",";
        $csvNo .= $this->_t('退费金额').",";
        $csvNo .= $this->_t('余额').",";
        $csvNo .= $this->_t('总额').",";
        $csvNo .= $this->_t('失败原因')."\r\n";
        while ($arr = fgetcsv($file)) {
            $courseModel = AsaCourse::model()->findByPk($arr[2]);
            $crit = new CDbCriteria();
            $crit->compare('t.schedule_id', $courseModel->schedule_id);
            $crit->order = 'course_index DESC';
            $crit->limit = $arr[7];
            $scheduleModel = AsaScheduleItem::model()->findAll($crit);
            $dropoutDate = end($scheduleModel);
            if($arr[4] == 2 && $arr[9] <= $arr[10]) {
                $model = new AsaRefund;
                $model->setAttributes(array(
                    'site_id' => 'BJ_SLT',
                    'childid' => $arr[3],
                    'invoice_item_id' => $arr[0],
                    'program_id' => $arr[1],
                    'course_id' => $arr[2],
                    'refund_class_count' => $arr[7],
                    'refund_total_amount' => $arr[9],
                    'dropout_date' => strtotime($dropoutDate->schedule_date),
                    'memo' => '疫情退费',
                    'refund_reason' => 3,
                    'refund_type' => 3,
                    'refund_method' => 1,
                    'payee_info' => "",
                    'status' => 4,
                    'refund_files' => '',
                    'updated' => time(),
                    'updated_by' => 1,
                    'updated_confirm' => time(),
                    'updated_confirm_by' => 1,
                    'updated_done' => time(),
                    'updated_done_by' => 1,
                ));

                if ($model->save()) {
                    $asaInvocieModel = AsaInvoiceItem::model()->findByPk($arr[0]);
                    $asaInvocieModel->refund_status = $model->id;
                    $asaInvocieModel->save();

                    CommonUtils::addProducer(MQProducer::TAG_ASA, "WechatPay.wechatRefund", $model->id);
                    foreach ($scheduleModel as $refundItems) {
                        $refundItem = new AsaRefundItem();
                        $refundItem->refund_id = $model->id;
                        $refundItem->site_id = $model->site_id;
                        $refundItem->course_group_id = $model->program_id;
                        $refundItem->course_id = $model->course_id;
                        $refundItem->child_id = $model->childid;
                        $refundItem->schedule_id = $refundItems->schedule_id;
                        $refundItem->schedule_item_id = $refundItems->id;
                        $refundItem->save();
                    }
                    echo $i . "\n";
                    $i++;
                }else{
                    $error = current($model->getErrors());
                    $csvNo .= $this->_t($arr[3]).",";
                    $csvNo .= $this->_t($arr[2]).",";
                    $csvNo .= $this->_t($arr[0]).",";
                    $csvNo .= $this->_t($arr[4]).",";
                    $csvNo .= $this->_t($arr[9]).",";
                    $csvNo .= $this->_t($arr[10]).",";
                    $csvNo .= $this->_t($error[0])."\r\n";
                }
            }else{
                $csv .= $this->_t($arr[3]).",";
                $csv .= $this->_t($arr[2]).",";
                $csv .= $this->_t($arr[0]).",";
                $csv .= $this->_t($arr[4]).",";
                $csv .= $this->_t($arr[9]).",";
                $csv .= $this->_t($arr[10]).",";
                $csv .= $this->_t($arr[11])."\r\n";
            }
        }
        file_put_contents('SLT-NoWechat ' . time().'.csv', $csv);
        file_put_contents('SLT-SaveFail ' . time().'.csv', $csvNo);
    }

    public function _t($str='')
    {
        $str = "\"".$str."\"";
        $str = str_replace(',', '，', $str);
        return iconv("UTF-8", "GBK", $str);
    }
}
