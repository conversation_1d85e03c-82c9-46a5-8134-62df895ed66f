<?php

/**
 * This is the model class for table "ivy_child_discount_link".
 *
 * The followings are the available columns in table 'ivy_child_discount_link':
 * @property integer $id
 * @property integer $childid
 * @property integer $discount_id
 * @property integer $operation_id
 * @property string $memo
 * @property integer $status
 * @property integer $update_user
 * @property integer $update_timestamp
 */
class ChildDiscountLink extends CActiveRecord
{
    const DISCOUNT_STATUS_VALID = 1;
    const DISCOUNT_STATUS_INVALID = 0;

    /**
	 * Returns the static model of the specified AR class.
	 * @return ChildDiscountLink the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_child_discount_link';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
            array('discount_id, childid, memo','required','on'=>'workflow'),
			array('childid, discount_id, operation_id, status, update_user, update_timestamp', 'numerical', 'integerOnly'=>true),
			array('memo', 'length', 'max'=>500),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, childid, discount_id, operation_id, memo, status, update_user, update_timestamp', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
			'DiscountSchool'=> array(self::BELONGS_TO, 'DiscountSchool', 'discount_id','with'=>'discountTitle'),
			'DiscountSchoolNoTitle'=> array(self::BELONGS_TO, 'DiscountSchool', 'discount_id'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'childid' => 'Childid',
			'discount_id' => 'Discount',
			'operation_id' => 'Operation',
			'memo' => 'Memo',
			'status' => 'Status',
			'update_user' => 'Update User',
			'update_timestamp' => 'Update Timestamp',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('childid',$this->childid);
		$criteria->compare('discount_id',$this->discount_id);
		$criteria->compare('operation_id',$this->operation_id);
		$criteria->compare('memo',$this->memo,true);
		$criteria->compare('status',$this->status);
		$criteria->compare('update_user',$this->update_user);
		$criteria->compare('update_timestamp',$this->update_timestamp);

		return new CActiveDataProvider(get_class($this), array(
			'criteria'=>$criteria,
		));
	}
}