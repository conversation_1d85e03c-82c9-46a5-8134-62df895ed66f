<script>
    var currentBranchId = '<?php echo $this->branchId;?>';
</script>

<?php
    if ( count($this->accessBranch) > 1 ):
?>
    <nav class="navbar navbar-inverse navbar-fixed-bottom" role="navigation">
        <div class="container-fluid">
            <ul class="nav navbar-nav">
                <li class="active dropdown">
                    <a href="#" id="branch-list" class="dropdown-toggle" data-toggle="dropdown">Loading <b class="caret"></b></a>
                    <div class="dropdown-menu w400" aria-labelledby="branch-list" id="branch-select-list-bottom">
                        <ul class="nav nav-pills">
                            <?php
                            $allbranches = $this->getAllBranch();
                            foreach($this->accessBranch as $_branch):
                                if(isset($allbranches[$_branch])):
                                    if ($this->branchSelectParams['hideKG']):
                                        if ($allbranches[$_branch]['type'] == 50 || $_branch == 'BJ_QFF'):
                                            $text = '<span class="glyphicon glyphicon-tags program'.$allbranches[$_branch]['group'].'"></span> '.$allbranches[$_branch]['title'];
                                            $routeUrl = is_null($this->branchSelectParams['extraUrlArray']) ? $this->branchSelectParams['urlArray'] : $this->branchSelectParams['extraUrlArray'];
                                        ?>
                                            <li branchId="<?php echo $_branch?>" class="branchItem">
                                                <?php
                                                if(isset($this->branchSelectParams['branchCount']) && $this->branchSelectParams['branchCount'][$_branch] && in_array($_branch, array_keys($this->branchSelectParams['branchCount']))){
                                                    $text .= ' <span class="badge">'.$this->branchSelectParams['branchCount'][$_branch].'</span>';
                                                }
                                                ?>
                                                <?php echo CHtml::link($text, array_merge($routeUrl, array($this->branchSelectParams['keyId']=>$_branch)));?>
                                            </li>
                                        <?php endif;
                                        else:
                                        if(!$this->branchSelectParams['hideOffice'] || $allbranches[$_branch]['type'] != 10):
                                            $text = '<span class="glyphicon glyphicon-tags program'.$allbranches[$_branch]['group'].'"></span> '.$allbranches[$_branch]['title'];
                                            ?>
                                            <?php
                                            $routeUrl = is_null($this->branchSelectParams['extraUrlArray']) ? $this->branchSelectParams['urlArray'] : $this->branchSelectParams['extraUrlArray'];
                                            ?>
                                            <li branchId="<?php echo $_branch?>" class="branchItem">
                                                <?php
                                                if(isset($this->branchSelectParams['branchCount']) && $this->branchSelectParams['branchCount'][$_branch] && in_array($_branch, array_keys($this->branchSelectParams['branchCount']))){
                                                    $text .= ' <span class="badge">'.$this->branchSelectParams['branchCount'][$_branch].'</span>';
                                                }
                                                ?>
                                                <?php echo CHtml::link($text, array_merge($routeUrl, array($this->branchSelectParams['keyId']=>$_branch)));?>
                                            </li>
                                        <?php
                                        endif;
                                    endif;
                                endif;
                            endforeach;
                            ?>
                        </ul>
                    </div>
                </li>
            </ul>
        </div>
    </nav>

    <script>
        var allBranches = '<?php echo CJSON::encode($allbranches);?>';
        $(function() {
            var _selected = $('#branch-select-list-bottom li[branchId|='+currentBranchId+']');
            _selected.addClass('active');
            $('#branch-list').html(_selected.html()+' <b class="caret"></b>');

        });
    </script>

    <style>
        body{padding-bottom: 70px;}
        .branchItem{height: 37px;width: 170px;}
    </style>

<?php
    endif;
?>