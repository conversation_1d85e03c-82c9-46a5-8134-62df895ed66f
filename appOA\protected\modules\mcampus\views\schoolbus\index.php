<?php
//取当前校历开始年
$calendarInfo = range( (OA::isShowNextYear() === TRUE) ? $currentSchoolYear+1 : $currentSchoolYear,$currentSchoolYear-1);
?>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site','Campus Operations'), array('//mcampus/default/index'))?></li>
        <li><?php echo CHtml::link(Yii::t('site','Campus Support'), array('//mcampus/default/index'))?></li>
        <li><?php echo Yii::t('site','Schoolbus Management')?></li>
    </ol>
    <div class="row">
        <div class="col-md-1 col-sm-2">
            <div class="btn-group mb10">
                <button type="button" class="btn btn-primary" onclick="createSchoolbus(0);">
                    <?php echo Yii::t('site','New Schoolbus');?>
                </button>
            </div>
            <?php
                foreach($calendarInfo as $_v):
                    $mainMenu[$_v]['label'] = sprintf('%s - %s', $_v,  $_v + 1);
                    $mainMenu[$_v]['url'] = array("//mcampus/schoolbus/index","startYear"=>$_v);
                endforeach;
                $this->widget('zii.widgets.CMenu',array(
                    'items'=> $mainMenu,
                    'htmlOptions'=>array('class'=>'nav nav-pills nav-stacked text-right background-gray mb10'),
                    'activeCssClass'=>'active',
                    'itemCssClass'=>''
                ));
            ?>
        </div>
        <?php
        if ($startYear):
            $temp = ($startYear >= $currentSchoolYear) ? '_list_edit' : '_list_view';
            $this->renderPartial($temp,array('startYear'=>$startYear,'schoolbusList'=>$schoolbusList,'childBySchoolbusList'=>$childBySchoolbusList,'childInfo'=>$childInfo));
        endif;
        ?>
    </div>
</div>
<!-- Modal -->
<div class="modal" id="schoolbusEditModal" tabindex="-1" role="dialog" aria-labelledby="schoolbusEditModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title" id="myModalLabel">校车信息 <small></small></h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-12">
                        <div class="well">
                            说明
                        </div>
                        <form class="form-horizontal J_ajaxForm" action="<?php echo $this->createUrl('//mcampus/schoolbus/saveSchoolbus');?>" method="POST">
                            <div id="form-data">
                                <!--place holder-->
                            </div>

                            <div class="pop_bottom">
                                <button onclick="$('#schoolbusEditModal').modal('hide')" type="button" class="btn btn-default pull-right"><?php echo Yii::t('global','Cancel');?></button>
                                <button type="submit" class="btn btn-primary J_ajax_submit_btn pull-right mr10"><?php echo Yii::t('global','Submit');?></button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php
    $this->branchSelectParams['extraUrlArray'] = array('//mcampus/schoolbus/index');
    $this->renderPartial('//layouts/common/branchSelectBottom');
    $this->renderPartial('mcampus.views.schoolbus.templates.schoolbusTemplate');
?>
<script>
var schoolbusEditTemplate = _.template($('#schoolbus-item-template').html());
$(function(){
    //初始化校车添加页面
    createSchoolbus = function(bid){
        var schoolbusInfo = {};
        if (bid == 0){
            schoolbusInfo={bid:0,bus_title:'',driver_name:'',bus_code:'',bus_type:'',acctual_children:0,driver_mobile:'',aunt_name:'',aunt_mobile:'',branchid:'',state:'',city_id:'',vendor_id:''};
        }else{
            $.each(schoolbusList, function(i,item){
                if (item.bid == bid){
                    schoolbusInfo = item;
                    return false;
                }
            })
        }
        openSchoolbusEditModal(schoolbusInfo);
    }
    //显示添加校车页面
    openSchoolbusEditModal = function(schoolbusInfo){
        $('#J_fail_info').remove();
        $('#schoolbusEditModal').modal();
        var _formData = schoolbusEditTemplate(schoolbusInfo);
        $('#form-data').html(_formData);
        $('#form-data #SchoolBus_state option[value="'+schoolbusInfo.state+'"]').attr('selected', true);
        $('#form-data #SchoolBus_city_id option[value="'+schoolbusInfo.city_id+'"]').attr('selected', true);
        selectVendor(null,schoolbusInfo.city_id);
        $('#form-data #SchoolBus_vendor_id option[value="'+schoolbusInfo.vendor_id+'"]').attr('selected', true);
    };

    //外包公司联动
    selectVendor = function(_this,cityId){
        var cityId = (cityId) ? cityId : $(_this).val();
        if (!_.isEmpty(cityId)){
            $("#SchoolBus_vendor_id").empty();
            $("<option />").attr("value", '').text('<?php echo Yii::t('global','Please Select')?>').appendTo("#SchoolBus_vendor_id");
            $.each(vendorList[cityId], function(m, item){
                $("<option />").attr("value", m).text(item).appendTo("#SchoolBus_vendor_id");
            })
        }else{
            $("#SchoolBus_vendor_id").empty();
            $("<option />").attr("value", '').text('<?php echo Yii::t('global','Please Select')?>').appendTo("#SchoolBus_vendor_id");
        }
    }

})
</script>