<?php
/**
 * Controller is the customized base controller class.
 * All controller classes for this application should extend from this base class.
 */
class ProtectedController extends Controller
{
	public $staff = null;
	/**
	 * @var string the default layout for the controller view. Defaults to '//layouts/column1',
	 * meaning using a single column layout. See 'protected/views/layouts/column1.php'.
	 */
	public $layout='//layouts/column1';
	/**
	 * @var array context menu items. This property will be assigned to {@link CMenu::items}.
	 */
	
	//主菜单
	public $mainMenu = array();
	
	//子菜单
	public $subMenu = array();
	
	//必须按校园过滤
	public $branchBased = false;
	
	public $multipleBranch = false;
    public $allBranch = array(); //所有校园
    public $accessBranch = array(); //可以查看的校园
    public $adminBranch  = array(); //可以管理的校园

    public $modernMenu = array(); //新框架中的主导航
    public $modernMenuFlag = ''; //新框架中的主导航
    public $modernMenuCategoryTitle = ''; //新框架中的主分类

    public $actionAccessAuths = array(); // 访问action的初级权限

    public $nonJquery; // 不使用框架默认jquery

	/**
	 * @var array the breadcrumbs of the current page. The value of this property will
	 * be assigned to {@link CBreadcrumbs::links}. Please refer to {@link CBreadcrumbs::links}
	 * for more details on how to specify this property.
	 */
	public $breadcrumbs=array();

	public function init(){
		parent::init();

        //标准独立登录
        $standLogin = ( isset(Yii::app()->params['standardLogin']) && Yii::app()->params['standardLogin'] ) ? true : false;

        if($standLogin){
            if(is_null($this->staff)){
                //Yii已经登录
                if(Yii::app()->user->getId()){
                    $staff = User::model()->with('profile')->findByPk(Yii::app()->user->getId());
                    if( $staff->isstaff > 0 && $staff->level > 0 ){
                        $this->staff = $staff;
                        Yii::app()->user->setState("__isstaff", $staff->uid);
                    }else{
                        Yii::app()->user->logout();
                    }

                }else{
                    Yii::app()->user->returnUrl = Yii::app()->getRequest()->getRequestUri();
                    Yii::app()->user->loginRequired();
                }
            }
        }else{
            if(is_null($this->staff)){
                //Yii已经登录
                if(Yii::app()->user->getId()){
                    $staff = User::model()->with('profile')->findByPk(Yii::app()->user->getId());
                    if( $staff->isstaff > 0 && $staff->level > 0 ){
                        $this->staff = $staff;
                        return OA::checkSecurity();
                    }
                }
                //XOOPS已经登录, Yii未登录
                elseif(isset($_SESSION['xos_kernel_Xoops2']['currentUser']) && !empty($_SESSION['xos_kernel_Xoops2']['currentUser']) ){
                    $email = $_SESSION['xos_kernel_Xoops2']['currentUser'];
                    $staff = User::model()->with('profile')->findByAttributes(array("email"=>$email));
                }else{
                    $staff = null;
                }

                if(!is_null($staff)){
                    $_identity = new UserIdentity($staff);
                    $_identity->errorCode = UserIdentity::ERROR_NONE;
                    if($_identity->authenticate()){
                        $_identity->errorCode = ( $_identity->errorCode == UserIdentity::ERROR_UNKNOWN_IDENTITY ) ? UserIdentity::ERROR_NONE : $_identity->errorCode;

                        $login = Yii::app()->user->login($_identity);
                        if($login){
                            Yii::app()->user->setState("__isstaff", $staff->uid);
                            $this->staff = User::model()->with('profile')->findByPk(Yii::app()->user->getId());
                            return OA::checkSecurity();
                        }else{
                            $_identity->errorCode = UserIdentity::ERROR_UNKNOWN;
                            $this->render('//site/error',array('identity'=>$_identity));
                            Yii::app()->end();
                        }
                    }else{
                        $this->render('//site/error',array('identity'=>$_identity));
                        Yii::app()->end();
                    }

                }else{

                    $absUrl = Yii::app()->createAbsoluteUrl(Yii::app()->getRequest()->getUrl());
                    Yii::app()->user->loginUrl = Yii::app()->user->loginUrl . "?xoops_requesturi=" . urlencode($absUrl);

                    Yii::app()->user->loginRequired();
                }
            }
        }


	}
	
	public function getAllBranch($status=10){
		if(empty($this->allBranch)){
			$this->allBranch = Branch::model()->getBranchList(null,true,null,$status);
		}
		return $this->allBranch;
	}

    public function checkActionAccess($operation, $params=array())
    {
        if(!Yii::app()->user->checkAccess($operation, $params)){
            if(Yii::app()->request->isAjaxRequest){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', 'Unauthorized operation, please contact IT Dept.');
                $this->showMessage();
                return false;
            }
            else{
                $this->render('//denied/index');
                return false;
            }
        }
        return true;
    }

    public function beforeAction($action){
        if($this->actionAccessAuths){
            $actionAccessAuths = array();
            foreach($this->actionAccessAuths as $actionID=>$auth){
                $actionAccessAuths[strtolower($actionID)] = $auth;
            }
            $thisactid = strtolower($action->id);
            if(isset($actionAccessAuths[$thisactid])){
                $auths = $actionAccessAuths[$thisactid];
                if(!is_array($auths)){
                    if (!$this->checkActionAccess($auths)){
                        return false;
                    }
                }
                else{
                    if(Yii::app()->request->isPostRequest){
                        if (isset($auths['admin']) && !$this->checkActionAccess($auths['admin'])){
                            return false;
                        }
                    }
                    else{
                        if (isset($auths['access']) && !$this->checkActionAccess($auths['access'])){
                            return false;
                        }
                    }
                }
            }
        }
        return true;
    }
}