<div>
    <div class='flex align-items  mb24 mt10'>
        <span class='font14 color3'>处理人：</span>
        <div class='flex1 flex align-items lib_class_handle'>
        </div>
    </div>
    <p class='mt5'>
        <span class='notAllowed'>
            <label class="radio-inline font14 color6 lableHeight eventsNone">
                <input type="radio" name="balance" value="1"> <?php echo Yii::t('withdrawal', 'Insured through Daystar, transferred out.'); ?>
            </label>
        </span>
        <span class='notAllowed'>
            <label class="radio-inline font14 color6 lableHeight ml24 eventsNone">
                <input type="radio" name="balance" value="2"> <?php echo Yii::t('withdrawal', 'Insured through Daystar, not transferred out yet.'); ?>
            </label>
        </span>
        <span class='notAllowed'>
            <label class="radio-inline font14 color6 lableHeight ml24 eventsNone">
                <input type="radio" name="balance" value="0"> <?php echo Yii::t('withdrawal', 'Not insured through Daystar.'); ?>
            </label>
        </span>
    </p>
    <p class='explain hide'>
    </p>  
    <textarea class="form-control" rows="3" placeholder="<?php echo Yii::t('withdrawal','Comment');?>" name="remark" id='remark' disabled  class='font14'></textarea>
    <div class='text-right font14 color3  mt20'><span class='submit'></span>  <span class='cur-p ml10 redColor canRevoke' onclick='cancelSubmit()'>撤销</span></div>
</div>

<script>
     lib()
     $(function () {
        $('[data-toggle="tooltip"]').tooltip()
    })
    
     function lib(){
        var explainList={
        1:'亲爱的家长您好，根据北京市社会医疗保险（一老一小）的要求，在校学生的社会医疗保险需要跟随学生的就读学校进行迁移。由于学生需要从启明星学校转出，我校将不再为其办理下一年度社会医疗保险的续保事宜。您孩子的社会医疗保险关系即日起将从本校转出，建议您尽快联系孩子的新学校或户口所在社区进行续保，以确保不会断保。',
        2:'亲爱的家长您好，根据北京市社会医疗保险（一老一小）的要求，学生的保险需要跟随学生的所在学校/学籍进行迁移。对于已经离校的学生，启明星学校原则上不再为学生办理一老一小保险的参保及续保手续。对于未到转出学籍窗口期，学生虽已离校，但学籍仍需暂时由我校代为保管的情况，需要家长知晓：1. 请家长在转出学籍窗口期尽快完成学籍转移，并同时联系校医室办理一老一小保险的转出。2. 由于学籍暂未转出原因，需要代管一老一小保险的最长代管期为一年，逾期将默认进行减员。3. 请家长主动联系校医室申请一老一小保险代管。未在学生转走一个月内联系校医室，学生的一老一小保险将默认进行减员。'
    }
        let forms=childDetails.forms.find(item2 =>item2.key === 'insurance')
        for(var key in forms.owner_info){
            if (key !== '2' && key !== '5') {
                if(forms.implementer==key && forms.data._id){
                    $('.lib_class_handle').append('<div class="flex align-items mr16 relative"><img src='+forms.owner_info[key].photoUrl+' alt="" class="avatar38"/><div class="ml8"><div class="font14 color3 ">'+forms.owner_info[key].name+'</div><div class="font12 color6">处理时间：'+forms.implementAt+'</div></div></div>')
                }else if(!forms.data._id){
                    $('.lib_class_handle').append('<div class="flex align-items mr16"><img src='+forms.owner_info[key].photoUrl+' alt="" class="img28"/><div class="font14 color3 ml8">'+forms.owner_info[key].name+'</div></div>')
                }
            }
        }
        if(forms.data.balance){
            $('input[type="radio"][name="balance"][value='+forms.data.balance+']').prop('checked', true);
            if(forms.data.balance!='0'){
                $('.explain').html(explainList[forms.data.balance])
                $('.explain').removeClass('hide')
            }else{
                $('.explain').addClass('hide')
            }
        }
        if(forms.data.remark!=''){
            $('#remark').val(forms.data.remark);
        }
        if(forms.canRevoke){
            $('.canRevoke').show()
            $('.submit').html('由 '+childDetails.staffInfo[forms.implementer].name+' <?php echo Yii::t('global','Submit');?>')
        }else{
            $('.canRevoke').hide()
            $('.submit').html()
        }
    }
    function cancelSubmit(){
        container.cancelSubmitData('insurance')
    }
</script>
<style>
    .explain{
        padding:8px 10px;
        background:#F7F7F8;
        border-radius:4px;
        margin-bottom:16px;
        color:#666
    }
</style>
