<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo Yii::t('site','Finance Management');?></li>
        <li class="active">对账管理</li>
    </ol>
    <form action="<?php echo $this->createUrl('query')?>" class="form-inline mb15 J_ajaxForm" method="post">
        <?php echo CHtml::dropDownList('account', '', $accountSel, array('class'=>'form-control form-group', 'empty'=>Yii::t('global', 'Please Select')));?>
        <?php
        $this->widget('zii.widgets.jui.CJuiDatePicker',array(
            'name'=>'startdate',
            'options'=>array(
                'dateFormat'=>'yy-mm-dd',
            ),
            'htmlOptions'=>array(
                'class'=>'form-control form-group',
                'placeholder'=>'开始日期',
            ),
        ));
        ?>
        <?php
        $this->widget('zii.widgets.jui.CJuiDatePicker',array(
            'name'=>'enddate',
            'value'=>date('Y-m-d'),
            'options'=>array(
                'dateFormat'=>'yy-mm-dd',
            ),
            'htmlOptions'=>array(
                'class'=>'form-control form-group',
                'placeholder'=>'结束日期',
            ),
        ));
        ?>
        <?php echo CHtml::dropDownList('type', '', array('1'=>'未到账', '2'=>'已到帐'), array('class'=>'form-control form-group'))?>
        <button class="btn btn-primary J_ajax_submit_btn">查询</button>
    </form>
    <div class="row J_check_wrap" id="viewData"></div>
</div>
<div class="modal fade" id="myModal">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title">确认到账信息</h4>
            </div>
            <div class="modal-body">
                <div class="alert alert-danger" role="alert">请输入银行实际到账的日期，而非当前操作时间！</div>
                <p id="tip"></p>
                <div class="form-group">
                    <label for="thetime">到账时间</label>
                    <input type="text" id="thetime" name="thetime" class="form-control" placeholder="到账时间">
                    <input type="hidden" id="typekey" name="typekey">
                    <input type="hidden" id="bankid" name="bankid">
                </div>
                <div class="form-group hide">
                    <label for="actual_amount">银行到账金额</label>
                    <input type="number" step="0.01" id="actual_amount" name="actual_amount" disabled="disabled" class="form-control" placeholder="银行到账金额">
                </div>
                <div class="form-group hide">
                    <label for="handling_amount">手续费金额</label>
                    <input type="number" step="0.01" id="handling_amount" name="handling_amount" disabled="disabled" class="form-control" placeholder="手续费金额">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="done()">确认</button>
            </div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->

<div class="modal fade" id="viewOrder">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title">订单详情</h4>
            </div>
            <div class="modal-body"></div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->

<script>
    var labels = {
        wechat: '微信支付',
        allinpayOnline: '在线支付（通联支付）',
        alipay: '在线支付（支付宝）',
        allinpay: 'POS刷卡（通联）',
        yeepay: 'POS刷卡（易宝）',
        wechatBus: '泉发校车微信支付',
        wechatUniform: '泉发校服微信支付',
        allinpayBus: '泉发校车POS刷卡（通联）',
        yeepayBus: '泉发校车POS刷卡（易宝）',
    };
    var branch = <?php echo CJSON::encode($branch);?>;
    var amount = {};
    var handling_amount = {};
    var selected = [];
    var canSubmit = true;
    function callback(ret)
    {
        var html = '';
        var data = ret.data;
        for(var key in data){
            if(!data[key].items) continue;
            if(typeof amount[key] == 'undefined'){
                amount[key] = {};
                handling_amount[key] = {};
            }
            html += '<div class="col-lg-4 col-md-12" id="block-'+key+'">';
            html += '<h4>'+labels[key]+': '+data[key]['total']+'</h4>';
            html += '<div class="table-responsive">';
            html += '<table class="table table-bordered">';
            html += '<thead>';
            html += '<tr>';
            html += '<th class="text-center"></th>';
            html += '<th class="text-center">订单号</th>';
            html += '<th class="text-center">校园</th>';
            html += '<th class="text-center">订单金额</th>';
            html += '<th class="text-center">支付时间</th>';
            html += '</tr>';
            html += '</thead>';
            html += '<tbody>';
            for(var datum in data[key]['items']){
                html += '<tr>';
                html += '<td class="text-center"><input onclick="calcAmount(\''+key+'\')" type="checkbox" class="J_check" data-yid="J_check_'+key+'" value="'+data[key]['items'][datum].orderId+'"></td>';
                html += '<td class="text-center"><a href="javascript:;" onclick="viewOrder(\''+data[key]['items'][datum].orderId+'\', \''+key+'\')">'+data[key]['items'][datum].orderId+'</a></td>';
                html += '<td class="text-center">'+branch[data[key]['items'][datum].schoolid].abb+'</td>';
                html += '<td class="text-right">'+data[key]['items'][datum].amount+'</td>';
                html += '<td class="text-center">'+data[key]['items'][datum].payDate+'</td>';
                html += '</tr>';

                amount[key][data[key]['items'][datum].orderId] = data[key]['items'][datum].amount;
                handling_amount[key][data[key]['items'][datum].orderId] = data[key]['items'][datum].handling_amount;
            }
            html += '</tbody>';
            html += '<tfoot>';
            html += '<tr>';
            html += '<th class="text-center"><input onclick="checkAll(\''+key+'\', this)" type="checkbox" class="J_check_all" data-direction="y" data-checklist="J_check_'+key+'"></th>';
            html += '<th colspan="3" class="text-right"><span id="total_'+key+'">0</span>';
            html += '</th>';
            html += '<th class="text-center"><button class="btn btn-primary btn-xs" onclick="confirmData(\''+key+'\')">确认到账</button></th>';
            html += '</tr>';
            html += '</tfoot>';
            html += '</table>';
            html += '</div>';
            html += '</div>';
        }
        $('#viewData').html(html);
        $('#bankid').val(ret.bankid);
        head.Util.checkAll();
    }

    function checkAll(key, obj) {
        if ($(obj).attr('checked')) {
            $('#block-'+key+' input.J_check').attr('checked', true);
        } else {
            $('#block-'+key+' input.J_check').attr('checked', false);
        }
        this.calcAmount(key);
    }

    function calcAmount(key)
    {
        selected = [];
        var objs = $('#block-'+key+' input.J_check:checked');
        var _amount = 0;
        var _handling_amount = 0;
        $.each(objs, function(index, value){
            var orderid = $(value).val();
            _amount += parseFloat(amount[key][orderid]);
            selected[index] = orderid;
            if (key == 'wechat' || key == 'wechatBus' || key == 'wechatUniform') {
                _handling_amount += handling_amount[key][orderid];
            }
        });
        $('#total_'+key).text(_amount.toFixed(2));
        $('#handling_amount').val(_handling_amount.toFixed(2));
    }

    function confirmData(key)
    {
        var tot = $('#total_'+key).text();
        if(tot>0){
            $('#myModal #tip').html('确定 '+labels[key]+' 到账 '+tot+' 元！');
            $("#thetime").val('');
            $("#thetime").datepicker({'dateFormat':'yy-mm-dd'});
            $('#typekey').val(key);
            $('#myModal').modal();
        }
        // 判断是否为微信到账
        if (key == 'wechat' || key == 'wechatBus' || key == 'wechatUniform') {
            var handling = $('#handling_amount').val();
            var actual_amount = (tot - handling).toFixed(2);
            $('#actual_amount').val(actual_amount);
            $('#handling_amount').val(handling);
            $('#actual_amount').parent().removeClass('hide');
            $('#handling_amount').parent().removeClass('hide');
        } else {
            $('#actual_amount').parent().addClass('hide');
            $('#handling_amount').parent().addClass('hide');
        }
    }

    function done()
    {
        if(canSubmit){
            canSubmit = false;
            $.post('<?php echo $this->createUrl('done')?>', {
                type: $('#typekey').val(), 
                selected:selected, 
                thetime: $('#thetime').val(), 
                bankid: $('#bankid').val(),
                handling_amount: $('#handling_amount').val(),
                actual_amount: $('#actual_amount').val()
            }, function(data){
                canSubmit = true;
                if(data.state == 'success'){
                    resultTip({msg: data.message});
                    $('#myModal').modal('hide');
                    $('.J_ajaxForm .J_ajax_submit_btn').click();
                }
                else{
                    resultTip({msg: data.message, error: 1});
                }
            }, 'json');
        }
    }

    function history(data)
    {
        $('#viewData').html(data);
    }

    function viewOrder(orderid, type)
    {
        $.get('<?php echo $this->createUrl('viewOrder')?>', {orderid: orderid, type: type}, function(data){
            $('#viewOrder .modal-body').html(data);
            $('#viewOrder').modal();
        });
    }
</script>