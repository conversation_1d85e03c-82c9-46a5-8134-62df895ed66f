<style>
    .weui_products_ft_a {
        display: block;
        position: relative;
        margin-bottom: 25px;
        padding-bottom: 15px;
        border-bottom: 1px solid #E5E5E5;
    }

    .weui_products_ft_a:last-child {
        border: none;
        margin-bottom: 0;
        padding-bottom: 0;
    }

    .weui_products_group_name {
        color: #111;
        opacity: .7;
        position: relative;
    }

    .weui_products_group_name:after {
        content: " ";
        display: inline-block;
        -webkit-transform: rotate(45deg) translateY(-50%);
        transform: rotate(45deg) translateY(-50%);
        height: 6px;
        width: 6px;
        border-width: 2px 2px 0 0;
        border-color: #C8C8CD;
        border-style: solid;
        position: absolute;
        right: 0;
        top: 50%;
        margin-left: .3em;
    }

    .weui_tabbar_item {
        float: left;
        width: 50%;
        padding: 0;
        text-align: center;
    }

    .table {
        border-spacing: 0;
        border-collapse: collapse;
        width: 100%;
        text-align: center;
        border-color: #d8d8d8;
        border: 1px solid #d8d8d8
    }

    .table th {
        padding: 5px;
        background: #eceaed;
        color:#464447;
    }

    .table tr, .table td {
        padding: 5px;
        color:#7f7d80;
    }
    #showChildern {
        border-bottom:none !important;
    }
/*    #childern_sheet {
        bottom: 50px;
    }*/
</style>

<div class="tabbar" style="height: 100%;">
    <div class="bd" style="height: 100%;">
        <div class="weui_tab">
            <div class="weui_tab_bd">
                <div class="weui_cells_title"><?php echo Yii::t("wechat", "Departments"); ?></div>

                <div class="weui_panel" style="margin: 0">
                        <?php
                        //选择孩子
                        $this->widget('ext.wechat.ChildSelector', array(
                            'childObj' => $this->childObj,
                            'myChildObjs' => $this->myChildObjs,
                            'jumpUrl' => 'index',
                        ));
                        ?>
                    <div class="weui_cells" style="margin-top: 0">
                        <div class="weui_cell">
                            <div class="weui_cell_bd weui_cell_primary" style="font-size: 14px;color: #888;">
                                <p style="list-style: none;padding-bottom: 5px"><?php echo Yii::t("wechat", "Purchasing Notice: "); ?></p>
                                <ol style="margin:0;padding: 0; list-style-position:inside;">
                                    <li style="padding-bottom: 5px"><?php echo Yii::t("wechat", "The uniform bill must be paid to complete the purchasing procedure."); ?></li>
                                    <li style="padding-bottom: 5px"><?php echo Yii::t("wechat", "The school is in charge of uniform distribution when the bill is paid."); ?></li>
                                    <li style="padding-bottom: 5px"><?php echo Yii::t("wechat", "Please contact the school when it comes to the uniform return or change."); ?></li>
                                </ol>
                            </div>
                        </div>
                    </div>
                    <div class="weui_panel_bd" id="typeContent" style="padding: 15px;">

                    </div>
                </div>
            </div>

            <div class="weui_tabbar" style="height: 50px;line-height: 50px;background: #fbf9fe;z-index: 1">

                <a href="<?php echo $this->createUrl('history'); ?>" class="weui_tabbar_item"
                   style="color:#000;border-left:1px solid #e5e5e5 ">
                    <?php echo Yii::t("wechat", "My orders"); ?>
                </a>
            </div>

        </div>

    </div>


<script>
    var cats = <?php echo json_encode($cats); ?>;
    var catsContent = '';
    $.each(cats, function (i, c) {
        catsContent += '<a class="weui_products_ft_a" href="' + c.url + '"><div><div style="display: inline-block;position: relative"><img src="' + c.img + '" " class="weui_media_appmsg_thumb img-responsive"></div><div class="weui_products_group_name""><span>' + c.title + '</span></div></div></a>'
    });
    $('#typeContent').html(catsContent);
    $('#showActionSheet').on('click', function () {
        if ($('#actionsheet').hasClass('weui_actionsheet_toggle')) {
            $('#actionsheet').removeClass('weui_actionsheet_toggle');
            $('#mask').removeClass('weui_fade_toggle').hide()
        } else {
            $('#actionsheet').addClass('weui_actionsheet_toggle');
            $('#mask').addClass('weui_fade_toggle').show()
        }
    });
    $('#mask').on('click', function () {
        $('#actionsheet').removeClass('weui_actionsheet_toggle');
        $('#mask').removeClass('weui_fade_toggle').hide()
    });
</script>
