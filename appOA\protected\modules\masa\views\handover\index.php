<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('//mcampus/default/index')) ?></li>
        <li><?php echo CHtml::link(Yii::t('site', 'Routines'), array('//mcampus/default/index')) ?></li>
        <li class="active"><?php echo Yii::t('site', '课后课管理') ?></li>
        <li class="active"><?php echo Yii::t('site', '现金交接') ?></li>
    </ol>

    <div class="row">
        <div class="col-md-2 col-sm-2 mb10">
            <?php
            $this->widget('zii.widgets.CMenu', array(
                'items' => $this->module->getMenu(),
                'id' => 'pageCategory',
                'htmlOptions' => array('class' => 'nav nav-pills nav-stacked background-gray'),
                'activeCssClass' => 'active',
            ));
            ?>
        </div>

        <div class="col-md-2">
            <select autocomplete="off" class="form-control" id="selectstartyear">
                <option value="">请选择学年</option>
                <?php
                if(empty($_GET['year'])){
                    //默认最新的学年
                    $getUrlYear = $startyearArr[0];
                }else{
                    $getUrlYear = $_GET['year'];
                }
                foreach ($startyearArr as $value){
                    if(Yii::app()->language == 'zh_cn'){
                        $msg = ' 学年';
                    }else{
                        $msg =' School Year';
                    }
                    $schoolYear = $value.'-'.($value+1).$msg;
                    if($value == $getUrlYear){
                        echo "<option value='$value' selected>$schoolYear</option>";
                    }else{
                        echo "<option value='$value'>$schoolYear</option>";
                    }
                }
                ?>
            </select>
            <br>
            <br>
            <?php
            echo "<span class=span_year$year>";
            $selected_title = "";
            $program_type = "";
            $groupStartTime = "";
            $groupEndTime = "";

            foreach ($newCourseGroup as $year => $courseGroup){
                if($getUrlYear == $year){
                    echo "<ul class='nav nav-pills nav-stacked background-gray year$year'>";
                }else{
                    echo "<ul class='nav nav-pills nav-stacked background-gray year$year' style='display: none'>";
                }
                foreach ($courseGroup as $v) {
                    $href = $this->createUrl('index', array('groupId' => $v['id'],'year'=>$year));
                    $active = '';
                    $title = Yii::app()->language == "zh_cn" ?
                        empty($v['title_cn']) ? $v['title_en'] : $v['title_cn']
                        :
                        empty($v['title_en']) ? $v['title_cn'] : $v['title_en'];
                    if ($v['id'] == $groupId) {
                        $active = 'active';
                        $selected_title = $title;
                        $program_type = $v['program_type'];
                        $groupStartTime = $v['buy_from'];
                        $groupEndTime = $v['buy_end'];
                    }
                    echo "<li class='{$active}'><a href='{$href}'>";
                    echo $title;
                    echo '</a></li>';
                }
                echo '</ul>';
            }
            echo '</span>';
            ?>
        </div>
        <div class="col-md-8">
            <?php if ($groupId): ?>
                <div class="page-header">
                    <h2>
                        <?php echo $selected_title; ?>
                    </h2>
                </div>
                <div class="page-header">
                    <h3><?php echo Yii::t('asa', '现金交接'); ?>
                        <!--<small><php /*echo Yii::t("asa","Mgt team and operation team");*/?></small>-->
                        <a class="pull-right btn btn-primary" href="javascript:invoiceModal(<?php echo $groupId ?>)">
                            <span class="glyphicon glyphicon-plus"
                                  aria-hidden="true"></span> <?php echo Yii::t("asa", "增加现金交接"); ?>
                        </a>
                    </h3>
                </div>

                <?php
                $this->widget('ext.ivyCGridView.BsCGridView', array(
                    'id' => 'cashHandober',
                    'afterAjaxUpdate' => 'js:head.Util.modal',
                    'dataProvider' => $cashHandover,
                    'template' => "{items}{pager}",
                    //状态为无效时标红
                    //'rowCssClassExpression' => '( $data->status == 0 ? "active" : "" )',
                    'colgroups' => array(
                        array(
                            "colwidth" => array(100, 100, 100, 100, 100, 100, 200),
                        )
                    ),
                    'columns' => array(
                        array(
                            'name' => 'item_number',
                            'value' => '$data->item_number',
                        ),
                        array(
                            'name' => 'amount',
                            'value' => '$data->amount',
                        ),
                        array(
                            'name' => 'status',
                            'value' => array($this, "getStatus"),
                        ),
                        /*array(
                            'name' => 'creater',
                            'value' => '$data->creater',
                        ),*/
                        array(
                            'name' => 'created',
                            'value' => 'date("Y-m-d", $data->created)',
                        ),
                        array(
                            'name' => 'updated',
                            'value' => array($this, "getButton"),
                        ),
                    ),
                ));
                ?>
            <?php endif; ?>
        </div>
    </div>
</div>
<div class="modal fade" id="invoiceListVueModel" tabindex="-1" data-backdrop="static">
    <div class="modal-dialog modal-lg" role="document">
        <form action="<?php echo $this->createUrl('updateHandover', array('groupId' => $groupId)) ?>" method="post"
              class="J_ajaxForm">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="">现金交接</h4>
                </div>
                <div class="modal-body">
                    <table class="table table-hover" v-if="isEmpty(invoiceListData)">
                        <thead>
                        <tr>
                            <th><label><input type="checkbox" class="J_check_all" v-model="checkAll"
                                              @click="check_all()" data-direction="y" data-checklist="J_check_i1"/>
                                    全选</label></th>
                            <th><label>账单标题</label></th>
                            <th><label>账单金额</label></th>
                            <!--<th><label>孩子名字</label></th>-->
                            <th><label>收款时间</label></th>
                            <th><label>收款人</label></th>
                            <th><label>该账单下的所有课程</label></th>
                        </tr>
                        </thead>
                        <tbody class="J_check_wrap">
                        <tr v-for="(invoiceList,iindex) in invoiceListData" :key="invoiceList.updated">
                            <th>
                                <input type="checkbox" class="J_check" data-yid="J_check_i1"
                                       @click="isChecked(iindex , invoiceList.isCheck , invoiceList.amount)"
                                       :name="'invoices[' + invoiceList.incoiceid + ']'"/>
                            </th>
                            <td>{{invoiceList.title}}</td>
                            <td>{{invoiceList.amount}}</td>
                            <!--<td>{{invoiceList.childname}}</td>-->
                            <td>{{invoiceList.updated}}</td>
                            <td>{{invoiceList.updated_by}}</td>
                            <td>
                                <div v-for="cItems in invoiceList.courseItems">
                                    {{cItems.title}}（{{cItems.classCount}}×{{cItems.amount}}）
                                </div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                    <div><label v-if="isEmpty(invoiceListData)">总金额：{{allAmount}} 元</label></div>
                    <label v-if="isEmpty(invoiceListData)">备注：</label>
                    <textarea class="form-control" name="creater_memo" rows="3" v-if="isEmpty(invoiceListData)"
                              placeholder="填写备注"></textarea>

                    <div v-else-if="loading">正在加载 请稍后...</div>
                    <div v-else-if="errorText">{{errorText}}</div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary J_ajax_submit_btn">保存</button>
                    <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                    <input type="reset" class="_reset" style="display: none"/>
                </div>
            </div>
        </form>
    </div>
</div>
<script>
    $("#selectstartyear").change(function (){
        var id = $(this).val();
        $(".year"+id).show().siblings().hide();
    })
    var modal = '<div class="modal fade" id="modal" class="1123" tabindex="-1" role="dialog" aria-labelledby="modal"><div class="modal-dialog modal-lg" role="document"><div class="modal-content"></div></div></div>';
    $('body').append(modal);
    var invoiceListData, errorText;
    function invoiceModal(group) {
        $('._reset').click();
        invoicelistVue.loading = true;
        invoicelistVue.allAmount = 0;
        invoicelistVue.checkAll = false;
        invoicelistVue.loading = false;
        $.ajax({
            url: '<?php echo $this->createUrl('handoverList');?>',
            type: 'get',
            timeout: 5000,
            data: {groupId: group},
            dataType: 'json',
            global: false
        }).done(function (data) {
            if (data.state == 'success') {
                invoicelistVue.invoiceListData = data.data;
                $('.J_check_wrap').find('input').removeProp('checked')
                $.each(invoicelistVue.invoiceListData, function (i, l) {
                    invoicelistVue.setCheck(i);
                });
            } else {
                invoicelistVue.errorText = data.message;
            }
        }).fail(function (data) {
        });
        $('#invoiceListVueModel').modal();
    }
    var invoicelistVue = new Vue({  //老师列表模型
        el: "#invoiceListVueModel",
        data: {
            invoiceListData:invoiceListData,
            errorText:errorText,
            loading: true,
            allAmount: 0,
            checkAll: false
        }, created: function () {
        },
        updated: function () {
            head.Util.checkAll();
        },
        methods: {
            isEmpty: function (data) {
                if ($.isEmptyObject(data)) {
                    return false;
                } else {
                    return true;
                }
            },
            isChecked: function (index, check, amount) {
                if (check) {
                    this.allAmount = this.allAmount - amount;
                } else {
                    this.allAmount = this.allAmount + (amount - 0);
                }
                this.$set(this.invoiceListData[index], 'isCheck', !check);
            },
            setCheck: function (index) {
                Vue.set(this.invoiceListData[index], 'isCheck', false)
            },
            check_all: function () {
                var c = this.checkAll;
                var allA = 0;
                $.each(this.invoiceListData, function (i, ListData) {
                    ListData.isCheck = c;
                    allA += (ListData.amount - 0);
                });
                if (c) {
                    this.allAmount = allA;
                } else {
                    this.allAmount = 0;
                }
            }
        }
    });

    function cbUpdateCash() {
        location.reload();
    }
</script>
<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>