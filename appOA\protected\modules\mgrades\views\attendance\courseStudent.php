<!-- 加载底部导航栏 -->
<?php
$this->branchSelectParams['extraUrlArray'] = array('//mgrades/attendance/index');
$this->renderPartial('//layouts/common/branchSelectBottom');

$timeData = array(5=>5, 10=>10, 15=>15, 20=>20);
?>
<div class="container-fluid">
    <!-- 面包屑导航 -->
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('//mcampus/default/index')) ?></li>
        <li><?php echo CHtml::link(Yii::t('site', 'G6-12 Student Attendance'), array('//mgrades/attendance/index')) ?></li>
        <li class="active"><?php echo isset($courseModel) ? $courseModel->getTitle() : "" ; ?></li>
    </ol>
    <div class="row">
        <div class="col-md-2">
            <?php
                $this->widget('zii.widgets.CMenu',array(
                    'items'=> $this->siderMenu,
                    'htmlOptions'=>array('class'=>'nav nav-pills nav-stacked text-right background-gray mb10'),
                    'activeCssClass'=>'active',
                    'itemCssClass'=>''
                ));
            ?>
        </div>
        <div class="col-md-10">
            <div class="panel panel-default">
                <div class="panel-body">

                    <?php
                    $form=$this->beginWidget('CActiveForm', array(
                        'id'=>'visits-form',
                        'enableAjaxValidation'=>false,
                        'action' => $this->createUrl('updateRecords',array('tid' => $tid, 'datatime'=>$datatime,'weekday'=>$weekday,'course_code'=>$course_code, 'teacherId' => $uid,)),
                        'htmlOptions'=>array('class'=>'J_ajaxForm form-horizontal', 'role'=>'form'),
                    ));
                    ?>
                    <div class="row">
                        <div class="col-md-8">
                            <h2>
                                <?php echo CHtml::link('<span class="glyphicon glyphicon-chevron-left"></span>', array('index', 'teacherId' => $uid));?>
                                <span><?php echo isset($courseModel) ? $courseModel->getTitle() : "" ; ?></span>
                            </h2>
                            <p>
                                <span class="glyphicon glyphicon-time"></span>
                                <?php
                                    $week = substr( $weekday, 0, 1 );
                                    $period = substr($weekday,-1,1);
                                    $weekArr = array(
                                        1=> Yii::t('attends','Mon'),
                                        2=> Yii::t('attends','Tue'),
                                        3=> Yii::t('attends','Wed'),
                                        4=> Yii::t('attends','Thu'),
                                        5=> Yii::t('attends','Fri'),
                                        );
                                    $getTime = TimetableCourses::getTime();
                                    echo sprintf(Yii::t('attends', '%s period #%s'), $weekArr[$week], $period);
                                ?>
                                <span class="label label-info"><?php echo $getTime[$period]; ?></span>
                            </p>
                        </div>
                        <div class="col-md-4 text-right">
                            <a class="btn btn-primary" href="<?php echo $this->createUrl('courseStudentPrint', array('uid'=>$uid, 'tid'=>$tid, 'weekday'=>$weekday, 'course_code'=>$course_code)); ?>" target="_blank">
                            <span class="glyphicon glyphicon-print" aria-hidden="true"> </span> <?php echo Yii::t('global', 'Print') ?></a>
                        </div>
                    </div>
                    <?php if($status < 1):?>
                        <div class="alert alert-danger"><?php echo Yii::t('attends','View Only! You have no attendance permission of this course.');?></div>
                    <?php endif;?>
                    <hr>

                    <table class="table">
                        <thead>
                        <tr>
                            <th><?php echo Yii::t('attends','Student List') ?></th>
                            <th><?php echo Yii::t('attends','Attendance') ?></th>
                            <th><?php echo Yii::t('attends','Dress Code Violation') ?></th>
                            <th><?php echo Yii::t('attends','Memo') ?></th>
                        </tr>
                        <tr>
                            <th width="250"></th>
                            <th width="380">
                                <div class="btn-group buttons attendance">
                                    <label class="btn btn-primary" onclick="allSet(10, 0)"><?php echo Yii::t('attends','All Present') ?></label>
                                    <label class="btn btn-primary" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                        <?php echo Yii::t('attends','All Tardy') ?>
                                        <span class="caret"></span>
                                    </label>
                                    <ul class="dropdown-menu">
                                        <?php foreach ($timeData as $tda):?>
                                            <li><a href="javascript:" onclick="allSet(20, <?php echo $tda;?>)"><?php echo sprintf(Yii::t('attends','%s Minutes'), $tda);?></a></li>
                                        <?php endforeach;?>
                                    </ul>
                                    <label class="btn btn-primary" onclick="allSet(30, 0)" style="margin-left: -1px;"><?php echo Yii::t('attends','All Leave') ?></label>
                                    <label class="btn btn-primary" onclick="allSet(40, 0)"><?php echo Yii::t('attends','All Absent') ?></label>
                                </div>
                            </th>
                            <th width="200">
                                <div class="btn-group buttons">
                                    <label class="btn btn-primary" onclick="allSets(1, 0)"><?php echo Yii::t('attends','All Yes') ?></label>
                                    <label class="btn btn-primary" onclick="allSets(0, 0)"><?php echo Yii::t('attends','All No') ?></label>
                                </div>
                            </th>
                            <th></th>
                        </tr>
                        </thead>
                        <tbody>
                        <?php foreach ($studentLiat as $key => $val){ ?>
                            <tr>
                                <td>
                                    <div class="pull-left">
                                        <img class="img-thumbnail" src="<?php echo CommonUtils::childPhotoUrl($val['photo'], 'small'); ?>">
                                    </div>
                                    <div class="pull-left ml15" style="width: 120px;">
                                        <p><?php echo $val['childName'] ?></p>
                                        <p><span class="badge"><?php echo ($val['gender'] == 1) ? Yii::t('attends','Boy') : Yii::t('attends','Girl') ; ?></span></p>
                                    </div>
                                </td>
                                <?php $is_admin = (isset($recordsArr) && isset($recordsArr[$key])) ? $recordsArr[$key]['is_admin'] : 0; ?>
                                <td style="line-height: 90px;">
                                    <div id="action_<?php echo $key?>" class="attendance btn-group buttons <?php echo ($is_admin) ? '' : 'act'; ?>" data-toggle="buttons" <?php echo ($is_admin) ? '' : 'act'; ?>>
                                        <?php $active = (isset($recordsArr) && isset($recordsArr[$key])) ? $recordsArr[$key]['type'] : 0; ?>
                                        <label class="btn btn-default btn_label_10 <?php echo $active == 10 ? 'active' : (($is_admin) ? 'disabled' : '')?>">
                                            <input type="radio" autocomplete="off" <?php echo $active == 10 ? 'checked' : '';?>  name="type[<?php echo $key; ?>]" id="hospital_1_<?php echo $key; ?>" value="10">
                                            <span><?php echo Yii::t('attends','Present') ?></span>
                                        </label>
                                        <label class="btn btn-default btn_label_20 <?php echo $active == 20 ? 'active' : (($is_admin) ? 'disabled' : '')?>" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                            <input type="radio" autocomplete="off" <?php echo $active == 20 ? 'checked' : '';?> name="type[<?php echo $key; ?>]" id="hospital_2_<?php echo $key; ?>" value="20">
                                            <span class="tflag"><?php echo $active == 20 ? $recordsArr[$key]['late_time']. Yii::t('attends','Minutes') : Yii::t('attends','Tardy') ?></span>
                                            <span class="caret"></span>
                                            <input type="hidden" name="select[<?php echo $key?>]" id="late_<?php echo $key?>" value="<?php echo $recordsArr[$key]['late_time'];?>">
                                        </label>
                                        <ul class="dropdown-menu">
                                            <?php foreach ($timeData as $tda):?>
                                                <li><a href="javascript:" onclick="types(this, <?php echo $key.', '.$tda;?>)"><?php echo sprintf(Yii::t('attends','%s Minutes'), $tda);?></a></li>
                                            <?php endforeach;?>
                                        </ul>
                                        <label class="btn btn-default btn_label_30 <?php echo $active == 30 ? 'active' : (($is_admin) ? 'disabled' : '')?>" style="margin-left: -1px;">
                                            <input type="radio" autocomplete="off" <?php echo $active == 30 ? 'checked' : '';?> name="type[<?php echo $key; ?>]" id="hospital_3_<?php echo $key; ?>" value="30">
                                            <span><?php echo Yii::t('attends','Leave') ?></span>
                                        </label>
                                        <label class="btn btn-default btn_label_40 <?php echo $active == 40 ? 'active' : (($is_admin) ? 'disabled' : '')?>">
                                            <input type="radio" autocomplete="off" <?php echo $active == 40 ? 'checked' : '';?> name="type[<?php echo $key; ?>]" id="hospital_4_<?php echo $key; ?>" value="40">
                                            <span><?php echo Yii::t('attends','Absent') ?></span>
                                        </label>
                                    </div>
                                </td>
                                <td style="line-height: 90px;">
                                    <div id="action_<?php echo $key?>" class="btn-group buttons" data-toggle="buttons">
                                        <?php $active = (isset($recordsArr) && isset($recordsArr[$key])) ? $recordsArr[$key]['dress'] : 0; ?>
                                        <label class="btn btn-default btn_label_1 <?php echo $active == 1 ? 'active' : ''?>">
                                            <input type="radio" autocomplete="off" <?php echo $active == 1 ? 'checked' : "";?>  name="dress[<?php echo $key; ?>]" id="hospital_1_<?php echo $key; ?>" value="1">
                                            <span><?php echo Yii::t('attends','Yes') ?></span>
                                        </label>
                                        <label class="btn btn-default btn_label_0 <?php echo $active == 0 ? 'active' : ''?>" style="margin-left: -1px;">
                                            <input type="radio" autocomplete="off" <?php echo $active == 0 ? 'checked' : "";?> name="dress[<?php echo $key; ?>]" id="hospital_3_<?php echo $key; ?>" value="0">
                                            <span><?php echo Yii::t('attends','No') ?></span>
                                        </label>
                                    </div>
                                </td>
                                <td>
                                    <textarea class="form-control" name="content[<?php echo $key; ?>]" style="height: 90px;"><?php if(isset($recordsArr) && isset($recordsArr[$key])){echo $recordsArr[$key]['memo'];} ?></textarea>
                                </td>
                            </tr>
                        <?php } ?>
                        </tbody>
                        <tfoot>
                            <tr>
                                <th></th>
                                <th>
                                    <div class="btn-group buttons">
                                        <label class="btn btn-primary"><span id="current_10"><?php echo $numberTypes[10];?></span> / <?php echo count($studentLiat) ?></label>
                                        <label class="btn btn-primary"><span id="current_20"><?php echo $numberTypes[20];?></span> / <?php echo count($studentLiat) ?></label>
                                        <label class="btn btn-primary"><span id="current_30"><?php echo $numberTypes[30];?></span> / <?php echo count($studentLiat) ?></label>
                                        <label class="btn btn-primary"><span id="current_40"><?php echo $numberTypes[40];?></span> / <?php echo count($studentLiat) ?></label>
                                    </div>
                                </th>
                                <th>
                                    <div class="btn-group buttons">
                                        <label class="btn btn-primary"><span id="current_1"><?php echo $numberTypes[1];?></span> / <?php echo count($studentLiat) ?></label>
                                        <label class="btn btn-primary"><span id="current_0"><?php echo $numberTypes[0];?></span> / <?php echo count($studentLiat) ?></label>
                                    </div>
                                </th>
                                <th></th>
                            </tr>
                        </tfoot>
                    </table>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t("global", 'Save');?></button>
                    </div>
                    <?php $this->endWidget(); ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    function cbSuccess() {
        window.location.href = '<?php echo $this->createUrl('index', array('teacherId' => $uid, 'weekTime' => $datatime)) ?>';
    }

    function calcNum() {
        var num = {
            10: 0,
            20: 0,
            30: 0,
            40: 0,
            0: 0,
            1: 0
        };
        $('.table input[type="radio"]:checked').each(function (key, val) {
            var v = $(val).val();
            num[v] += 1;
        });
        for (var key in num){
            var val = num[key];
            $('#current_'+key).text(val);
        }
    }

    function types(e, childid, min)
    {
        $('#action_'+childid+'.attendance label').removeClass('active').eq(1).addClass('active').find('span.tflag').text( $(e).text() );
        $('#hospital_2_'+childid).attr('checked', true);
        $('#late_'+childid).val(min);
        calcNum();
    }

    function allSet(type, min) {
        if (type == 20) {
            $('.act label').removeClass('active');
            $('.table tbody tr .buttons[act] .btn_label_'+type).addClass('active').find('span.tflag').text( min+' <?php echo Yii::t('attends','Minutes');?>' );
            $('.table tbody tr .buttons[act] .btn_label_'+type+' input[type="radio"]').attr('checked', true);
            $('.table tbody tr .buttons[act] .btn_label_'+type+' input[type="hidden"]').val(min);
        }
        else {
            $('.table tbody tr .buttons[act] .btn_label_'+type).click();
        }
        calcNum();
    }

    function allSets(type, min) {
        $('.table tbody tr .buttons .btn_label_'+type).click();
        calcNum();
    }

    $('tbody .buttons label').on('click', function () {
        setTimeout(function () {
            calcNum();
        }, 300)
    });

    calcNum();

</script>

<style>
    .buttons label{
        width: 90px;
    }
</style>
