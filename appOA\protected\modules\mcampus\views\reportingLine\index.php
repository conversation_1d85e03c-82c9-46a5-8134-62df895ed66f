
  <div id="container" v-cloak>
    <div  class="container-fluid">
      <ol class="breadcrumb">
          <li><?php echo CHtml::link(Yii::t('site', 'Teaching Tasks'), array('//mteaching/default/index')) ?></li>
          <li><?php echo Yii::t('directMessage', 'Approval Line') ?></li>
      </ol>
    </div>
      <div class='col-md-12'>
        <div class="tabList" v-if='!isEdit && dataAll.tree'>
          <span  class="el-tabs__item"  @click="handleClick(list.type)" v-for='(list,index) in reportingLine' :class='index==0?"pl0":""'>
            <span :class='activeName==list.type?"borderBto":""' class='tabHeight'>{{list.name}}</span>
          </span>
        </div>
      </div>
    <div :class='!isEdit?"container-fluid":""'>
      <div class='row' v-if='!isEdit && dataAll.tree'>

        <div class='col-md-12'>
          <div class='align-items flex pt10 pb10'>
            <div class='flex1 font14 color3'>
              <div class='font14 color6 ' >
                <?php echo Yii::t('directMessage', 'All Members') ?>：{{dataAll.staff_total}}
                <span class='ml24 delTree' v-if='levelList.length>0'>离职：{{levelList.length}}人（待处理）</span>
              </div>
            </div>
              <?php if ($this->checkRole()){?>
            <div class=''>
              <span class='colorBlue font14 mr24 cur-p' @click='linkHref()'><span class='el-icon-setting'></span> 共同审批和抄送</span>
              <button type="button" class="btn btn-primary" @click='editTree'><?php echo Yii::t('directMessage', 'Enter to Edit Mode') ?></button>
            </div>
              <?php }?>
          </div>
          <div class='flex1 relative'>
            <div class="back_box " :class='isEdit?"bgBox":""' ref="back_box"  :style="'height:'+(height-editHeight)+'px;overflow-y: auto;'">
              <div
              
                class="drag_box"
                :style="`left:${elLeft}px;top:${elTop}px;height:${elHeight}px;`"
                
              >
              <div ref="text" class='pb50' :style="`left:${(0 * elWidth) / 100}px;top:${(25 * elHeight) / 100}px;-webkit-transform: scale(${meter_zoom} )`">
                <div  v-if='viewData.length!=0' >
                  <div class="text"
                    v-for='(item,index) in viewData'
                  >
                    <vue2-org-tree
                      :data="item"
                      @on-expand="onExpand"
                      :horizontal="horizontal"
                      collapsable
                      :render-content="renderContentView" 
                    >
                    </vue2-org-tree>

                  </div>
                </div>
                <div v-else>
                  <div><el-empty description="<?php echo Yii::t('ptc', 'No Data') ?>"></el-empty></div>
                </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div  v-if='isEdit'>
      <div class='editTop'>
        <div class='flex'>
          <div class='flex1 font14 color3'>
            <span class='cur-p' @click='cancelEdit'>
              <span class='el-icon-back'></span> {{backTitle}}
            </span>
          </div>
          <div>
          <button type="button" class="btn btn-default" @click='cancelEdit'><?php echo Yii::t('directMessage', 'Back to View Mode') ?></button>
          </div>
          <div>
          </div>
        </div>
      </div>
      <div class="drag flex relative" >
        <div class='flex1 relative'>
          <div class="back_box" :class='isEdit?"bgBox":""' ref="back_box"  :style="'height:'+(height-editHeight)+'px;overflow-y: auto;'">
            <div
              class="drag_box"
              :style="`left:${elLeft}px;top:${elTop}px;height:${elHeight}px;`"
            
            >
            <div ref="text" :style="`left:${(0 * elWidth) / 100}px;top:${(25 * elHeight) / 100}px;-webkit-transform: scale(${meter_zoom} )`" v-loading="loading">
              <div v-if='data.length!=0'>
                <div
                  v-for='(item,index) in data'
                  class="text"
                  @drop="drop($event)" 
                  @dragend="dragend()"
                  @dragover="allowDrop($event)"
                  @dragstart="dragTree($event)" 
                  @dragleave="dragleave2($event)"
              
                >
                  <vue2-org-tree
                    class='editTreeCss'
                    v-if='item.length!=0'
                    :data="item"
                    :horizontal="horizontal"
                    @on-expand="onExpand"
                    :label-class-name="labelClassName"
                    collapsable
                    @on-node-click="NodeClick"
                    :render-content="renderContent" 
                    selected-class-name="bg-tomato"
                    selected-key="selectedKey" 
                  />
                
                </div>
              </div>
              <div v-else>
                  <div v-if='isEdit' class='fristStaff' @dblclick='topStaff'>双击编辑</div>
                </div>
            </div>
            </div>
          </div>
        </div>
        <div  style='width:300px'>
          <div class='scroll-box' :style="'height:'+(height-editHeight-25)+'px;overflow-y: auto;padding:12px'">
            <div v-if='levelList.length>0' class='mb20'>
              <div class='color3 font12 mb16 fontBold'>已离职人员</div>
              <div class='classlist'>
                <div  class='mb12'>
                    <div v-for='(item,idx) in levelList' class='itemHover' style='padding:0 8px' >
                    <div  class='flex align-items dragItem' style='padding:4px'> 
                      <img :src="item.photoUrl" class='img42 img-circle ' alt="">
                        <div class='flex1 flexText ml10'>
                            <div class='font12 color3 word-break'>{{item.name}}</div>
                            <div class='font12 color6 word-break'>{{item.hrPosition}}</div>
                        </div>
                        <div class='ml5'>
                          <span class='el-icon-circle-close font14 colorBlue cur-p ' @click='delTreeModal([item.uid],"one")'></span>
                        </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class='color3 font12 mb16 fontBold'>未分配人员</div>
            <div class='classlist' v-if='Object.values(unStaffList).length>0'>
              <div v-for='(list,key,index) in unStaffList' class='mb12'>
                <div class='department_name flex'><span class='flex1 font12 color3'> {{list[0].department_name}}</span> <span class="badge">{{list.length}}</span></div>
                  <div v-for='(item,idx) in list' class='itemHover' style='padding:0 8px'  @dragstart="dragstart($event,item)"  @dragend="dragend()" >
                  <div draggable="true" class='flex align-items dragItem' style='padding:4px'> 
                    <img :src="item.photoUrl" class='img42 img-circle ' alt="">
                      <div class='flex1 flexText ml10'>
                          <div class='font12 color3 word-break'>{{item.name}}</div>
                          <div class='font12 color6 word-break'>{{item.hrPosition}}</div>
                      </div>
                      <div class='ml5'>
                        <span class='el-icon-circle-plus-outline font14 colorBlue cur-p ' @click='getTreeList(item,"nuStaff")'></span>
                      </div>
                  </div>
                </div>
              </div>
            </div>
            <div v-else>
              <el-empty description="<?php echo Yii::t('ptc', 'No Data') ?>"></el-empty>
            </div>
          </div>
        </div>
    </div>
    </div>
    <div class='flex'  :style="'right:'+(isEdit?324:24)+'px;  position: fixed;bottom: 74px;z-index:999'">
      <div class='scale mr12' v-if='delTreeData.staff_id'>
        <span class='el-icon-delete delTree cur-p fixedText' @click='delTreeModal([delTreeData.staff_id],"one")'></span>
      </div>
      <div class='scale mr12'  v-if='isEdit && data.length!=0' >
        <span class='delTree cur-p fixedText'  style='line-height:28px' @click='clearTree("clear")'>全部清空</span>
      </div>
      <div  class='scale'>
      <span class='fixedText cur-p' v-if='!isEdit' @click='onExpandAll'><span class='mr8' :class='collapseText=="1"?"el-icon-remove-outline":"el-icon-circle-plus-outline"'></span>{{collapseText=='1'?'<?php echo Yii::t('directMessage', 'Collapse All') ?>':'<?php echo Yii::t('directMessage', 'Expand All') ?>'}}</span>
      <el-divider direction="vertical" v-if='!isEdit'></el-divider>
        <span class='el-icon-zoom-out  cur-p mr5 fixedText' @click="pageZoomOut"></span>
        {{ Math.round(100 * meter_zoom)}}%
        <span class='el-icon-zoom-in cur-p  ml5 fixedText' @click="pageZoomIn"></span>
      </div>
    </div>
    <!-- 添加成员 -->
    <div class="modal fade" id="editTreeModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel" data-backdrop="static">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="parentReplyLabel">{{isEdit?'编辑汇报关系':'查看'}}</h4>
            </div>
            <div class="p24 overflow-y modalscroll-box scroll-box font14" :style="'max-height:'+(height-220)+'px;overflow-x: hidden;'"  >
              <div v-if='reportData.current'>
                <div class='flex relative overFlow'>
                  <div class='borderLeft'></div>
                  <span class='point'></span>
                  <div class='flex1 ml16'>
                    <div  class='flex align-items font14'>
                      <span class='color3 font600'>上级</span>
                      <span class='color6 ml12'><span class='el-icon-warning-outline'></span> 只能有一个上级</span>
                    </div>
                    <div class='flex mt16 align-items' v-if='reportData.staffInfo[reportData.top]'>
                      <img :src="reportData.staffInfo[reportData.top].photoUrl" class='img24 img-circle' alt="">
                      <div class='font14 color3 ml10 mr16 word-break'>{{reportData.staffInfo[reportData.top].name}} <span v-if='reportData.staffInfo[reportData.top].level==0' class='levelTag'>已离职</span></div>
                      <div v-if='isEdit'>
                        <span class='colorBlue cur-p font14' v-if='editBox' @click='transferStaff("tab")'> 更换</span>
                        <span class='el-icon-circle-close cur-p font14'  @click='delSelect(reportData.top,"top")' v-if='!editBox'></span>
                      </div>
                    </div>
                    <div v-else-if='reportData.top==0' class='mt16'>
                      <span v-if='isEdit'  class='flex1 colorBlue font14 cur-p' @click='addStaff(1)'><span class='el-icon-circle-plus-outline mr5'></span>添加上级成员</span>
                      <span v-else class='color6'>无上级</span>
                    </div>
                  </div>
                </div>
                <div class='flex mt24 relative overFlow'>
                  <div class='borderLeft'></div>
                  <span class='point'></span>
                  <div class='flex1 ml16'>
                    <div class='font14 color3 font600'>本人</div>
                    <div class='mt12'>
                      <div class='flex p24 bgchild' v-if='reportData.staffInfo[reportData.current.staff_id]'>
                        <img :src="reportData.staffInfo[reportData.current.staff_id].photoUrl" class='img42 img-circle' alt="">
                        <div class='flex1 flex flexText ml10'>
                          <div class='mt4'>
                            <div class='font14 color3 word-break'>{{reportData.staffInfo[reportData.current.staff_id].name}} <span v-if='reportData.staffInfo[reportData.current.staff_id].level==0' class='levelTag'>已离职</span></div>
                            <div class='font12 color6 word-break'>{{reportData.staffInfo[reportData.current.staff_id].hrPosition}}</div>
                          </div>
                          <div class='flex1 ml20'>
                            <div class='ccupList'>
                              <div class='align-items flex'>
                                <span class='flex1 font14 color3 fontBold ml8'>被审批后抄送至</span>
                                <el-dropdown v-if='reportData.staffInfo[reportData.top] && isEdit'>
                                  <el-button type="primary"  size="small" :class='reportData.useSpecialCCConfig?"yellowConfig":"greenConfig"'>
                                    {{reportData.useSpecialCCConfig?"特殊抄送配置":"默认抄送配置"}}<i class="el-icon-arrow-down el-icon--right"></i>
                                  </el-button>
                                  <el-dropdown-menu slot="dropdown">
                                    <el-dropdown-item v-if='reportData.useSpecialCCConfig' @click.native='addSpecialCC("default")'>使用默认抄送配置</el-dropdown-item>
                                    <el-dropdown-item v-if='!reportData.useSpecialCCConfig' @click.native='addStaff(1,"special")'>使用特殊抄送配置</el-dropdown-item>
                                  </el-dropdown-menu>
                                </el-dropdown>
                              </div>
                              <div v-if='reportData.staffInfo[reportData.top]' class='mt8 approveLoading' v-loading='approvedLoading' element-loading-background="#e7f2f6">
                                <span v-if='reportData.userCCBackupConfig.cc.length'>
                                  <span v-for='(item,index) in reportData.userCCBackupConfig.cc' :class='isEdit && reportData.useSpecialCCConfig?"specialCC":"mt8 ml8 mr12 inline-block"'>
                                    <img :src="reportData.staffInfo[item].photoUrl" alt="" class='img24'>
                                    <span class='color6'>{{reportData.staffInfo[item].name}}</span> 
                                    <span class='el-icon-close ml16 cur-p' @click='delSpecialCC(item)' v-if='isEdit && reportData.useSpecialCCConfig'></span>
                                  </span>
                                </span>
                                  <span class='colorBlue font14 cur-p ml8' v-if='isEdit && reportData.useSpecialCCConfig' style='white-space: nowrap;' @click='addStaff(1,"add")' >
                                    <span class='el-icon-circle-plus-outline mr5' ></span>选择人员</span>
                                
                                <div v-if='reportData.userCCBackupConfig.cc.length==0' class='mt16 color9 font12 text-center'>
                                  未配置抄送人员<span v-if='reportData.school_other_config_CC.length'>（默认抄送给<span v-for='(item,index) in reportData.school_other_config_CC'>{{reportData.staffInfo[item].name}} <span v-if='index+1<reportData.school_other_config_CC.length'>、</span></span>）</span>
                                </div>
                              </div>
                              <div v-else-if='reportData.top==0' class='mt16 color9 font12 text-center'>
                                <span class='el-icon-warning-outline mr4'></span>未设置上级
                              </div>
                            </div>
                            <div class='ccList'>
                              <div class='font14 color3 mb8 fontBold ml8'>审批下级时抄送至</div>
                              <div v-if='reportData.last.length'>
                                <div class='flex align-items mb8 mt16' v-if='isEdit'>
                                  <el-select v-model="cc_id" clearable placeholder="<?php echo Yii::t('teaching', 'Please select') ?>" size='small' @change='saveApproval'>
                                    <el-option
                                      v-for="item in reportData.mountGroupList.list"
                                      :key="item._id"
                                      :label="item.name"
                                      :value="item._id">
                                    </el-option>
                                  </el-select>
                                  <div class='flex1'>
                                    <span class='refresh' @click='refreshList'><span class='el-icon-refresh'></span> {{refreshLoading?"刷新":"刷新中"}}</span>
                                  </div>
                                  <span class='colorBlue font14 cur-p'  @click='linkHref()'>创建新的抄送组 <span class='el-icon-arrow-right'></span></span>
                                </div>
                                <div v-loading='approveLoading' element-loading-background="#f6f0e7" class='approveLoading'>
                                  <div v-if='reportData.mountGroup._id' >
                                    <div v-if='reportData.mountGroup.config.cc.length>0'>
                                      <span v-for='(item,index) in reportData.mountGroup.config.cc'  class='mt8 mr12 ml8 inline-block'>
                                        <img :src="reportData.mountGroupList.staffInfo[item].photoUrl" alt="" class='img24'>
                                        <span class='color6'>{{reportData.mountGroupList.staffInfo[item].name}}</span> 
                                      </span>
                                    </div>
                                    <div v-else class='color9 font12 text-center mt16'>未配置抄送人员<span v-if='reportData.school_other_config_CC.length'>（默认抄送给<span v-for='(item,index) in reportData.school_other_config_CC'>{{reportData.staffInfo[item].name}} <span v-if='index+1<reportData.school_other_config_CC.length'>、</span>）</span></div>
                                  </div>
                                  <div v-else class='color9 font12 text-center mt16'>未配置抄送人员<span v-if='reportData.school_other_config_CC.length'>（默认抄送给<span v-for='(item,index) in reportData.school_other_config_CC'>{{reportData.staffInfo[item].name}} <span v-if='index+1<reportData.school_other_config_CC.length'>、</span>）</span></div>
                                </div>
                              </div>
                              <div v-else class='color9 font12 text-center'>
                                <span class='el-icon-warning-outline mr4'></span>未设置下级
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class='flex mt24 relative overFlow'>
                  <div class='borderLeft'></div>
                  <span class='point'></span>
                  <div class='flex1 ml16'>
                    <div class='font14 color3 font600 mb16'>下级</div>
                    <div class='flex  align-items' v-if='isEdit'>
                      <span  class='flex1 colorBlue font14'>
                        <span class=' cur-p' @click='addStaff(2)' ><span class='el-icon-circle-plus-outline mr5'></span>添加下级成员</span>
                      </span>
                      <label class="checkbox-inline"  v-if='reportData.last.length && editBox'>
                        <input type="checkbox" id="inlineCheckbox1" @change='transferAll($event)' v-model='transferCheckAll'> <?php echo Yii::t('global', 'Select All') ?>
                      </label>
                      <button type="button" v-if='reportData.last.length && editBox' :disabled='transferId.length>0?false:true' class="btn btn-default mr16 ml24" @click='transferStaff("transfer")'>转移成员</button>
                      <button type="button" v-if='reportData.last.length && editBox' :disabled='transferId.length>0?false:true'  class="btn btn-default" @click='delTreeModal(transferId,"all")'>批量删除</button>
                    </div>
                    <div class='row mt4' v-if='reportData.last.length'>
                      <div class='col-md-6' v-for='(list,index) in reportData.last'>
                        <div class='flex mt8 pt4 pb4 pl8 pr8 align-items bgchild' v-if='reportData.staffInfo[list]'>
                          <input type="checkbox" class='mr12' :value='list' v-model='transferId' v-if='editBox && isEdit'  @change='transferCheck($event)'>
                          <img :src="reportData.staffInfo[list].photoUrl" class='img24 img-circle' alt="">
                          <div class='flex1 flexText ml10'>
                              <div class='font12 color6 word-break'>{{reportData.staffInfo[list].name}} 
                                <span v-if='reportData.staffInfo[list].level==0' class='levelTag'>已离职</span>
                                <el-popover
                                  placement="bottom"
                                  width="250"
                                  class='ml10'
                                  v-if='reportData.firstConfigInfo[list]'
                                  trigger="click">
                                  <div>
                                      <div class='mb16 color3 font14 fontBold'>Ta被审批后抄送至：</div>
                                      <div v-for='(item,index) in reportData.firstConfigInfo[list].cc' class='mt8'>
                                        <img :src="reportData.staffInfo[item].photoUrl" alt="" class='img24'>
                                        {{reportData.staffInfo[item].name}}
                                      </div>
                                  </div>
                                  <img slot="reference" src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/userbg.png' ?>" alt="" style='width:17px'>
                                </el-popover>
                                <img class='ml10' src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/user.png' ?>" alt=""  style='width:14px' v-if='!reportData.firstConfigInfo[list]'>
                              </div>
                              <!-- <div class='font12 color6 word-break'>{{reportData.staffInfo[list].hrPosition}}</div> -->
                          </div>
                          <div v-if='isEdit'>
                            <!-- <span class='el-icon-circle-close font14'  @click='delSelect(index,"last")'></span> -->
                            <span class='el-icon-circle-close font14 cur-p'  @click='delTreeModal([list],"last")'></span>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div v-else-if='!isEdit' class='color9 font12'>
                      <span class='el-icon-warning-outline mr4'></span>未设置下级
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close"); ?></button>
                <!-- <button type="button" class="btn btn-primary" @click="saveStaff()"><?php echo Yii::t("global", "Save"); ?></button> -->
            </div>
            </div>
        </div>
    </div>
    <!-- 转移 -->
    <div class="modal fade" id="transferModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel">{{transferType=='tab'?'更换上级':'选择上级'}}</h4>
                </div>
                <div class="modal-body p24" >
                  <el-select
                    v-model="top_uid"
                    filterable
                    remote
                    style='width:100%'
                    reserve-keyword
                    placeholder="搜索"
                    :remote-method="remoteMethod"
                    @change='addStu'
                    @visible-change="clearOptions($event)"
                    :loading="loading">
                    <el-option
                      v-for="item in options"
                      :key="item.uid"
                      :label="item.name"
                      class='optionSearch'
                      :value="item.uid">
                      <div class="media">
                          <div class="media-left pull-left media-middle">
                              <a href="javascript:void(0)">
                                  <img :src="item.photoUrl" data-holder-rendered="true" class="media-object img-circle img42">
                              </a>
                          </div> 
                          <div class="media-body mt5 media-middle">
                              <h4 class="media-heading font14 color3 text_overflow">{{item.name}}</h4> 
                              <div class="text-muted text_overflow font12">{{ item.hrPosition }}</div>
                          </div>
                      </div>
                    </el-option>
                  </el-select>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Cancel"); ?></button>
                    <button type="button" class="btn btn-primary" @click='editLineData("tab")'><?php echo Yii::t("global", "OK"); ?></button>
                </div>
            </div>
        </div>
    </div>
    <!-- 删除 -->
    <div class="modal fade" id="delModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
        <div class="modal-dialog modal-sm" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t('leave', '提示') ?></h4>
                </div>
                <div class="modal-body p24" >
                  <div v-if='delType=="clear" || delType=="clearCC"'>
                    <?php echo Yii::t('leave', '确认清空吗？') ?>
                  </div>
                  <div v-else-if='delType=="all"'>
                    <?php echo Yii::t('leave', '确认全部删除吗？') ?>
                  </div>
                  <div v-else-if='delType=="special"'>
                    <?php echo Yii::t('leave', '确认删除吗？') ?>
                  </div>
                  <div v-else>
                    <?php echo Yii::t('directMessage', 'Proceed to remove?') ?>
                  </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Cancel"); ?></button>
                    <button type="button" class="btn btn-primary" v-if='delType=="clear"' @click='clearTree()'><?php echo Yii::t("global", "OK"); ?></button>
                    <button type="button" class="btn btn-primary" v-else-if='delType=="clearCC"' @click='clearCC()'><?php echo Yii::t("global", "OK"); ?></button>
                    <button type="button" class="btn btn-primary" v-else-if='delType=="special"' @click='delSpecialCC()'><?php echo Yii::t("global", "OK"); ?></button>
                    <button type="button" class="btn btn-primary" v-else @click='delTreeModal(delId,delType,"del")'><?php echo Yii::t("global", "OK"); ?></button>
                </div>
            </div>
        </div>
    </div>
    <!-- 选择成员 -->
    <div class="modal fade" id="addStaffModal" tabindex="-1" role="dialog" data-backdrop="static" data-keyboard="false" data-backdrop="static">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentCommentsModalLabel"><?php echo Yii::t('leave', '添加成员') ?></h4>
                </div>
                <div class="modal-body relative" style='padding:0'>
                    <span class='borderLeftpos'></span>
                    <div style='max-height:600px;' class='p24 row'>
                        <div class='col-md-6 col-sm-6'>
                          <div>
                            <el-select v-model="schoolId" style='width:100%' placeholder="<?php echo Yii::t('teaching', 'Please select') ?>" @change='addStaff(addType,"tab")'>
                                <el-option
                                    v-for="(item,key,index) in schoolList"
                                    :key="key"
                                    :label="item.title"
                                    :value="key">
                                </el-option>
                            </el-select>
                          </div>
                          <div class='mt10'>
                              <el-input
                              placeholder="<?php echo Yii::t('global', 'Search') ?>"
                              v-model='searchText' 
                              clearable>
                              </el-input>
                          </div>
                          <div  class="tab-pane active mt15 scroll-box" id="class" v-if='searchText==""'  style='max-height:460px;overflow-y:auto'>
                            <div v-if='currentDept.list && currentDept.list.length>0'>
                              <div v-for='(list,index) in currentDept.list' class='relative mb16'>
                                  <p  @click='showDepName(list)'>
                                      <span  class='font14 color606 cur-p'>{{list.dep_name}} </span>
                                      <span class='el-icon-arrow-down ml5' v-if='dep_name!=list.dep_name'></span>
                                      <span class='el-icon-arrow-up ml5' v-else></span>
                                  </p>
                                  <p  class='allCheck' v-if='dep_name==list.dep_name && addType==2'><button class="btn btn-default pull-right btn-xs" type="button" @click='selectAll(list,index)' ><?php echo Yii::t("global", "Select All");?></button></p>
                                  <div  class='border scroll-box mr10 childList' v-if='dep_name==list.dep_name'>
                                      <div class="flex align-items listMedia" v-for='(item,key,idx) in list.user'>
                                          <div class='flex flex1' v-if='currentDept.user_info[item.uid]'>
                                              <img :src="currentDept.user_info[item.uid].photoUrl" data-holder-rendered="true" class="media-object img-circle img42">
                                              <div class="flex1 ml10 flex1Text">
                                                  <div class=" font14 mt8 color3 text_overflow">{{currentDept.user_info[item.uid].name}}</div>
                                                  <div class="font12 mt5 color6 text_overflow">{{currentDept.user_info[item.uid].hrPosition}}</div>
                                              </div>
                                          </div>
                                          <div >
                                              <span class='cur-p font16 text-primary  el-icon-circle-plus-outline' v-if='!currentDept.user_info[item.uid].disabled' @click='assignStaff(item,index,idx)'></span>
                                              <span v-else><?php echo Yii::t('directMessage', 'selected') ?></span>
                                          </div>
                                      </div>
                                  </div>
                              </div>
                            </div>
                            <div v-else>
                              <el-empty description="<?php echo Yii::t('ptc', '无可选择成员') ?>"></el-empty>
                            </div>
                          </div>
                          <div v-else>
                              <div v-if='searchStaffList.length!=0' class='mt24 scroll-box'  style='max-height:460px;overflow-y:auto'>  
                                  <div class="flex align-items listMedia" v-for='(item,idx) in searchStaffList'>
                                      <div class='flex flex1' >
                                          <img :src="item.photoUrl" data-holder-rendered="true" class="media-object img-circle img42">
                                          <div class="flex1 ml10 flex1Text">
                                              <div class=" font14 mt8 color3 text_overflow">{{item.name}}</div>
                                              <div class="font12 mt5 color6 text_overflow">{{item.hrPosition}}</div>
                                          </div>
                                      </div>
                                      <div >
                                          <span class='cur-p font16 text-primary  el-icon-circle-plus-outline' v-if='!currentDept.user_info[item.uid].disabled' @click='assignStaff(item,idx,"search")'></span>
                                          <span v-else><?php echo Yii::t('directMessage', 'selected') ?></span>
                                      </div>
                                  </div> 
                              </div>
                              <div v-else-if='searchText!=""'>
                                <div class='font14 color6 text-center mt20'><?php echo Yii::t('ptc', 'No Data') ?></div>    
                              </div>
                          </div>
                        </div>
                        <div class='col-md-6 col-sm-6'>
                            <p class='mt10 font14 color6'>
                            <?php echo Yii::t("newDS", " ");?>{{staffSelected.length}} <?php echo Yii::t('leave', '成员') ?>
                            </p>
                            <div class='scroll-box p10 overflow-y' style='height:500px'>
                                <div class="flex align-items listMedia" v-for='(item,idx) in staffSelected'>
                                    <div class='flex flex1' v-if='allDept[item]'>
                                        <img :src="allDept[item].photoUrl" data-holder-rendered="true" class="media-object img-circle img42">
                                        <div class="flex1 ml10 flex1Text">
                                            <div class=" font14 mt8 color3 text_overflow">{{allDept[item].name}}</div>
                                            <div class="font12  mt5 color6 text_overflow">{{allDept[item].hrPosition}}</div>
                                        </div>
                                    </div>
                                    <div @click='Unassign(item,idx)'>
                                        <span class='closeChild cur-p mt10 font16 el-icon-circle-close'></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class='clearfix'></div>
                    </div>
                </div> 
                <div class="modal-footer">
                    <span class='text-left colorRed pr16' v-if='addType==1 || addType==3'><span class='glyphicon glyphicon-exclamation-sign'></span> <?php echo Yii::t('leave', '最多选择一名成员') ?></span>
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Cancel");?></button>
                    <el-button type="button" class="btn btn-primary"  @click='confirmSatff()' :loading="confirmLoading"><?php echo Yii::t("global", "OK");?></el-button>
                </div>
            </div>
        </div>
    </div>
  </div>
  <?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>
  <script>
     var reportingLine =  <?php echo json_encode($reportingLine);?>;
     var type = '<?php echo $_GET['type']?>';
     var branchId = '<?php echo $_GET['branchId']?>';
    $(document).ready(function () {
    // 通过该方法来为每次弹出的模态框设置最新的zIndex值，从而使最新的modal显示在最前面
        $(document).on('show.bs.modal', '.modal', function (event) {
            var zIndex = 1040 + (10 * $('.modal:visible').length);
            $(this).css('z-index', zIndex);
            setTimeout(function() {
                $('.modal-backdrop').not('.modal-stack').css('z-index', zIndex - 1).addClass('modal-stack');
            }, 0);
        });
    });
    function jsClick(data){
      container.getTreeList({staff_id:data})
    }
    var height=document.documentElement.clientHeight;
    var container = new Vue({
    el: "#container",
    data: {
        activeName:type,
        type:type,
        reportingLine:reportingLine,
        backTitle:'',
        data: {},
        labelClassName: "bg-color-orange",
        BasicSwich: false,
        BasicInfo: { id: null, label: null },
        checked: true,
        horizontal: true,
        checked2: true,
        startclientX: 0, // 元素拖拽前距离浏览器的X轴位置
        startclientY: 0, //元素拖拽前距离浏览器的Y轴位置
        elLeft:0, // 元素的左偏移量
        elTop: 0, // 元素的右偏移量
        textWidth:0,
        zoom: 1, // 缩放比例
        elWidth: 0, // 元素宽
        elHeight: 0, // 元素高
        meter_zoom: 1, // 子元素缩放比例
        clickTimes:0,
        height:height,
        dataAll:{},
        isEdit:false,
        editHeight:270,
        unStaffList:{},
        delTreeData:{},
        reportData:{},
        searchText:'',
        transferId:[],
        schoolList:[],
        schoolId:'',
        currentDept:{},
        allDept:{},
        staffSelected:[],
        dep_name:'',
        addType:'',
        editBox:false,
        loading:false,
        top_uid:'',
        staff_name:'',
        options:[],
        transferType:'',
        delType:'',
        delId:'',
        dragIds:'',
        dragTop:'',
        transferCheckAll:false,
        loading:false,
        confirmLoading:false,
        result:[],
        viewData:{},
        showCC:false,
        cc_id:'',
        cc_id_copy:'',
        collapseText:'1',
        levelList:[],
        refreshLoading:true,
        approvedLoading:false,
        approveLoading:false,
      },
      created() {
        let title=this.reportingLine.filter((i) => i.type === this.type)
        this.backTitle=title[0].name
        this.getIndex()
      },
    mounted() {
    },
    computed: {
        searchStaffList: function() {
            var search = this.searchText;
            var searchVal = ''; //搜索后的数据
            if(search) {
                searchVal =Object.values(this.currentDept.user_info).filter(function(product) {
                    return Object.keys(product).some(function(key) {
                        return String(product['name'].toLowerCase()).indexOf(search.toLowerCase()) !== -1;
                    })
                })
                return searchVal;
            }
            return this.searchStaffList;
        },
    },
    methods: {
        sortTree(nodes) {
          return nodes.sort((a, b) => a.staff_name.localeCompare(b.staff_name)).map(node => ({
            ...node,
            children: node.children ? this.sortTree(node.children) : [],
          }));
        },
        transformTree(tree) {
            tree.forEach(node => {
                if (node.children && node.children.length > 3) {
                  let arrayChild=node.children.filter(node => !node.children || node.children.length === 0)
                  if(arrayChild.length>3){
                    const joinChild = arrayChild[0]
                    joinChild.childrenList = arrayChild.slice(1);
                    if(node.children.every(child => !child.children.length)){
                      const firstChild = node.children[0]
                      firstChild.childrenList = node.children.slice(1);
                      firstChild.children = [];
                      node.children = [node.children[0]];
                    }
                    node.children = node.children.filter(node => node.childrenList || node.children.length !== 0);
                  }
                }
                if (node.children) {
                    this.transformTree(node.children);
                }
            });

            return tree;
        },
        getIndex(){
          let that=this
          $.ajax({
              url: '<?php echo $this->createUrl("GroupIndex") ?>',
              type: "get",
              dataType: 'json',
              data: {},
              success: function(data) {
                  if (data.state == 'success') {
                    that.schoolList=data.data.school_list.list  
                    that.schoolId=data.data.school_list.current_school
                    for(var i=0;i<data.data.tree.length;i++){
                      if(data.data.tree[i].children){
                        that.sortTree(data.data.tree[i].children)
                      }
                    }
                    that.data=JSON.parse(JSON.stringify(data.data.tree))
                    that.viewData=JSON.parse(JSON.stringify(data.data.tree))
                    for(var i=0;i<that.viewData.length;i++){
                      that.transformTree([that.viewData[i]]);
                    }
                    that.levelList=[]
                    for(let key in data.data.staffInfo){
                      if(data.data.staffInfo[key].level==0){
                        that.levelList.push(data.data.staffInfo[key])
                      }
                    }
                    that.dataAll=data.data
                    that.unStaffList=data.data.ungrouped
                    that.toggleExpand(that.data, true);
                    that.toggleExpand(that.viewData, true);
                    that.loading=false
                    that.$nextTick(()=>{
                      if(that.isEdit){
                        $('.org-tree-node-label').attr('draggable', 'false');
                        // $('.org-tree-node-label-inner').attr('draggable', 'true');
                      }else{
                        $('.org-tree-node-label').attr('draggable', 'false');
                        // $('.org-tree-node-label-inner').attr('draggable', 'false');
                      }
                      that.getWidth()
                    })
                  }else{
                      resultTip({
                          error: 'warning',
                          msg: data.message
                      });
                  }
                  that.showTable=true
              },
              error: function(data) {
                  that.showTable=true
                  resultTip({
                      error: 'warning',
                      msg: data.message
                  });
              },
          })
        },
        getWidth(){
          let boxWidth=this.$refs.back_box.offsetWidth
          let boxHeight=this.height-this.editHeight
          let treeWidth=this.$refs.text.offsetWidth
          let treeHeight=this.$refs.text.offsetHeight
          if(treeHeight<boxHeight){
            this.elTop=(boxHeight-treeHeight)/2<0?0:(boxHeight-treeHeight)/2
          }
          this.elLeft=(boxWidth-treeWidth)/2 <10?0:(boxWidth-treeWidth)/2 
        },
        collapse(list) {
          var _this = this;
            list.forEach(function(child) {
                if (child.expand) {
                  child.expand = false;
                }
                child.children && _this.collapse(child.children);
          });
        },
        startInit(){
          return false
        },
        onExpand(e,data) {
            if ("expand" in data) {
              data.expand = !data.expand;
              if (!data.expand && data.children) {
                  this.collapse(data.children);
              }
            } else {
                this.$set(data, "expand", true);
            }
            this.$nextTick(()=>{
              if(this.viewData[0].expand){
                this.collapseText='1'
              }else{
                this.collapseText='2'
              }
              this.getWidth()
            })
        },
        onExpandAll(){
          for(var i=0;i<this.viewData.length;i++){
            if(this.viewData[i].children){
              if(this.viewData[0].expand){
                this.onExpand('',this.viewData[0]) 
              }else{
                this.toggleExpand(this.viewData, true);
                this.elLeft=0
                this.$nextTick(()=>{
                  this.collapseText='1'
                  let boxWidth=this.$refs.back_box.offsetWidth
                  let treeWidth=this.$refs.text.offsetWidth
                  this.elTop=0
                  this.elLeft=(boxWidth-treeWidth)/2 <0?0:(boxWidth-treeWidth)/2 
                })
              }
            }
          }
        },
        toggleExpand(data, val) {
          var _this = this;
          if (Array.isArray(data)) {
              data.forEach(function (item) {
              _this.$set(item, "expand", val);
              if (item.children) {
                  _this.toggleExpand(item.children, val);
              }
              });
          } else {
              this.$set(data, "expand", val);
              if (data.children) {
              _this.toggleExpand(data.children, val);
              }
          }
        },
        pageZoomOut() {
          if (this.zoom <= 0.1) {
            this.zoom = 0.1;
            return;
          }
          this.zoom -= 0.1;
          const height = this.$refs.text.offsetHeight
          const width = this.$refs.text.offsetWidth
          this.meter_zoom = this.zoom ; 
          if(this.zoom<1){
            this.getWidth()
          }else if(this.zoom==1){
            this.elTop=0
            this.elLeft=0
          }else{
            this.elTop=((this.elHeight/100) *(this.zoom+1))*10
            this.elLeft=((this.elWidth/100) *(this.zoom+1))*10
          }
        },
        pageZoomIn() {
          if (this.zoom >= 1.5) {
            this.zoom = 1.5;
            return;
          }
          this.zoom += 0.1;
          this.elHeight = this.$refs.text.offsetHeight
          this.elWidth = this.$refs.text.offsetWidth
          this.meter_zoom = this.zoom ; 
          if(this.zoom<1){
            this.getWidth()
          }else if(this.zoom==1){
            this.elTop=0
            this.elLeft=0
          }else{
            this.elTop=((this.elHeight/100) *(this.zoom+1))*10
            this.elLeft=((this.elWidth/100) *(this.zoom+1))*10
          }
        },
        renderContentView(h, data) {
          if(!this.dataAll.staffInfo){
            return
          }
          if(data.childrenList){
            let one= h('div', {
              attrs: {
                onDblclick: 'jsClick('+data.staff_id+')',
                class: this.dataAll.staffInfo[data.staff_id].level==0?'optionView flex  col-md-3 col-xs-3 dragClass':'optionView flex  col-md-3 col-xs-3',
                'data-id': data.staff_id,
                'title':this.dataAll.staffInfo[data.staff_id].level==0?'已离职':'',

              },
            }, [
              h('img', {
                attrs: {
                  src: this.dataAll.staffInfo[data.staff_id]
                    ? this.dataAll.staffInfo[data.staff_id].photoUrl
                    : '',
                  class: 'img28',
                },
              }),data.mount?h('span', {class: 'el-icon-s-check auditing'},):"",
              h('span', {
                class: 'flex1 treeFlexText',
                domProps: {
                  textContent: this.dataAll.staffInfo[data.staff_id]
                    ? this.dataAll.staffInfo[data.staff_id].name
                    : '',
                },
              }),
            ]);
            const listItems = data.childrenList.map((item) => {
              return h('div', {
                attrs: {
                  onDblclick: 'jsClick('+item.staff_id+')', 
                  class: this.dataAll.staffInfo[item.staff_id].level==0?'optionView flex col-md-3 col-xs-3 dragClass':'optionView flex col-md-3 col-xs-3',
                  'data-id': item.staff_id,
                  'title':this.dataAll.staffInfo[item.staff_id].level==0?'已离职':'',

                },
              }, [
                h('img', {
                  attrs: {
                    src: this.dataAll.staffInfo[item.staff_id]
                      ? this.dataAll.staffInfo[item.staff_id].photoUrl
                      : '',
                    class: 'img28',
                  },
                }),item.mount?h('span', {class: 'el-icon-s-check auditing'},):"",
                h('span', {
                  class: 'flex1 treeFlexText',
                  domProps: {
                    textContent: this.dataAll.staffInfo[item.staff_id]
                      ? this.dataAll.staffInfo[item.staff_id].name
                      : '',
                  },
                }),
              ]);
            });
            return   h('div',{class: 'viewMoreP'},[ h('div',{class:this.dataAll.unfold?'viewMore scroll-box':'viewMore viewMoreHeight scroll-box'},[one, ...listItems]) ]);
          }else{         
            return  h('div', {
              attrs: {
                'onDblclick': 'jsClick('+data.staff_id+')',
                'class':this.dataAll.staffInfo[data.staff_id].level==0?'option flex dragClass':'option flex',
                'data-id':data.staff_id,
                'title':this.dataAll.staffInfo[data.staff_id].level==0?'已离职':'',
                },
            },[h('img', {
              attrs: {
                src:this.dataAll.staffInfo[data.staff_id]?this.dataAll.staffInfo[data.staff_id].photoUrl:'',
                class: 'img28',
              },
            }),data.mount?h('span', {class: 'el-icon-s-check auditing'},):"",
            h('span', {class: 'flex1 treeFlexText'}, this.dataAll.staffInfo[data.staff_id]?this.dataAll.staffInfo[data.staff_id].name:''),
            data.children.length!=0?h('span', {class: 'treeNum'}, data.childrenTotal):""])
          }
        },
        renderContent(h, data) {
          if(!this.dataAll.staffInfo){
            return
          }      
          let element= h('div', {
            attrs: {
              'class':this.dataAll.staffInfo[data.staff_id].level==0?'option flex listsd dragClass':'option flex listsd',
              'data-id':data.staff_id,
              'title':this.dataAll.staffInfo[data.staff_id].level==0?'已离职':'',
              'data-down':true,
              'draggable':true
              },
          },[
            data.mount?h('span', {class: 'el-icon-s-check auditing'},):"",
            h('img', {
            attrs: {
              src:this.dataAll.staffInfo[data.staff_id]?this.dataAll.staffInfo[data.staff_id].photoUrl:'',
              // 'data-id': data.staff_id,
              class: 'img28',
            },
          }),
          h('span', {attrs: {'class': 'flex1 treeFlexText','data-down':true,}}, this.dataAll.staffInfo[data.staff_id]?this.dataAll.staffInfo[data.staff_id].name:''),
          data.children.length!=0?h('span', {attrs: {'class': 'treeNum','data-down':true,}}, data.childrenTotal):""])
          return element
        },
        
        // 拖拽
        dragstart(ev, item) {
          if(!this.isEdit){
              return false
          }
          this.dragIds = item.uid;
          this.dom = ev.currentTarget.cloneNode(true);
          ev.dataTransfer.setData('my-info', 'hello')
          ev.dataTransfer.setData('my-extra-info', 'world')
          this.dragged = ev.target;
          ev.target.classList.add("hover-background");
        },
        dragleave2( e) {
          e.target.classList.remove("tree-background");
        },
        dragTree(ev){
          if(!this.isEdit){
              return false
          }
          this.dragged = ev.target;
          let treeNode = ev.target;
          // ev.target.classList.add("tree-background");
          this.dragIds=treeNode.getAttribute("data-id")
        },
        // 允许放下拖拽
        allowDrop(ev) {
          let treeNode = ev.target;
          // console.log(treeNode)
          let id=treeNode.getAttribute("data-id")
          let draggable=treeNode.getAttribute("data-down")
          if(id==null){
            if(ev.target.parentElement.getAttribute("data-id")!=null){
              id=ev.target.parentElement.getAttribute("data-id")
              treeNode=ev.target.parentElement
            }
          }
          if(!this.isEdit || id==null || !draggable){
            event.preventDefault()
            event.dataTransfer.dropEffect = 'none'
            return false
          }
          treeNode.classList.remove("tree-background");
          treeNode.classList.add("tree-background");
          ev.preventDefault();
        },
        // 放下事件
        drop(ev, item) {
          let treeNode = ev.target;
          this.dragged.classList.remove("tree-background");
          this.dragged.classList.remove("hover-background");
          ev.target.classList.remove("tree-background");
          this.dragTop=treeNode.getAttribute("data-id")
          if(this.dragTop==null){
            if(ev.target.parentElement.getAttribute("data-down")!=null){
              this.dragTop=ev.target.parentElement.getAttribute("data-id")
              ev.target.parentElement.classList.remove("tree-background");
            }
          }
          if(!this.isEdit || !this.dragIds || this.dragTop==null){
            return false
          }
         
          if(this.dragTop==this.dragIds){
            return false
          }
          let that=this
          this.editLineData('drag')
        },
        dragend() {
          if(!this.isEdit){
              return false
          }
          this.dragIds=''
          this.delTreeData={}
          this.dragged.classList.remove("tree-background");
          this.dragged.classList.remove("hover-background");
        },
        delTreeModal(id,delType,type){
          if(!type){
            this.delType=delType
            this.delId=id
            $("#delModal").modal('show')
            return
          }
          let that=this
          that.loading=true
          $.ajax({
              url: '<?php echo $this->createUrl("delLine") ?>',
              type: "get",
              dataType: 'json',
              data: {
                staff_ids:this.delId
              },
              success: function(data) {
                  if (data.state == 'success') {
                    $("#delModal").modal('hide')
                     that.getIndex()
                     if(delType=='all' || delType=='last'){
                      that.transferId=[]
                      that.reportData.last=that.reportData.last.filter((i) => that.delId.indexOf(i) == -1)
                     }
                     that.delTreeData={}
                     resultTip({
                          msg: data.message
                      });
                  }else{
                      resultTip({
                          error: 'warning',
                          msg: data.message
                      });
                      that.loading=false
                  }
                  that.showTable=true
              },
              error: function(data) {
                  that.showTable=true
                  that.loading=false
                  resultTip({
                      error: 'warning',
                      msg: data.message
                  });
              },
          })
        },
        cancelEdit(){
          this.delTreeData={}
          this.isEdit=false
          this.editHeight=270
          this.$nextTick(()=>{
            this.getWidth()
            
            $('.org-tree-node-label').attr('draggable', 'false');
            // $('.org-tree-node-label-inner').attr('draggable', 'false');

          })
        },
        editTree(){
          let _this = this
          this.isEdit=true
          this.editHeight=225
          this.$nextTick(()=>{
            this.getWidth()
            // $('.org-tree-node-label-inner').attr('draggable', 'true');
            $('.org-tree-node-label').attr('draggable', 'false');

          })
        },
        NodeClick(e, data) {
          let _this = this
          if(!this.isEdit){
            return
          }
          if(data.off){
            return
          }
          _this.clickTimes++;
          if (_this.clickTimes === 2) {  
              _this.clickTimes = 0;  
              _this.getTreeList(data)
          } 
          setTimeout(function () {
              if (_this.clickTimes === 1) {
                _this.clickTimes = 0; 
                if(!data.selectedKey){
                  _this.selectedKey(_this.data);
                  // if(data.pid==0){
                  //   _this.delTreeData={}
                  // }else{
                    _this.delTreeData=data
                  // }
                }else{
                  _this.delTreeData={}
                }
                _this.$set(data, 'selectedKey', !data.selectedKey)                
              }
          }, 250) 
          
        },
        selectedKey(data) {
          data.forEach((item, index) => {
            if (item.children&& item.children.length > 0) {
              this.$set(item, 'selectedKey', false)
              this.selectedKey(item.children)
            } else {
              this.$set(item, 'selectedKey', false)
            }
          })
          return data
        },
        getTreeList(staff,type){
          if(type){
            staff.staff_id=staff.uid
            this.editBox=false
          }else{
            this.editBox=true
          }
          this.transferId=[]
          let that=this
          $.ajax({
              url: '<?php echo $this->createUrl("tierLine") ?>',
              type: "get",
              dataType: 'json',
              data: {
                staff_id:staff.staff_id
              },
              success: function(data) {
                  if (data.state == 'success') {
                     that.reportData=data.data
                     if(data.data.last.length>0){
                      that.editBox=true
                     }
                     that.transferCheckAll=false
                     that.reportData.current=staff
                     if(data.data.mountGroup._id){
                        that.showCC=true
                        that.cc_id=data.data.mountGroup._id
                        that.cc_id_copy=data.data.mountGroup._id
                     }else{
                        that.showCC=false
                        that.cc_id=''
                        that.cc_id_copy=''
                     }
                     that.approvedLoading=false
                     that.approveLoading=false
                     $("#editTreeModal").modal('show')
                  }else{
                      resultTip({
                          error: 'warning',
                          msg: data.message
                      });
                  }
                  that.showTable=true
              },
              error: function(data) {
                  that.showTable=true
                  resultTip({
                      error: 'warning',
                      msg: data.message
                  });
              },
          })
        },
        transferCheck(){
          if(this.transferId.length==this.reportData.last.length){
            this.transferCheckAll=true
          }else{
            this.transferCheckAll=false
          }
        },
        transferAll(e){
          this.transferId=[]
          if(e.target.checked){
            this.transferId=this.reportData.last
          }
        },
        remoteMethod(query) {
          let that=this
          if (query !== '') {
              this.loading = true;
              $.ajax({
                  url: '<?php echo $this->createUrl("searchFirst") ?>',
                  type: "post",
                  dataType: 'json',
                  data: {
                    staff_name:query
                  },
                  success: function(data) {
                      if (data.state == 'success') {
                          that.options = Object.values(data.data) ;
                      }else{
                          resultTip({
                              error: 'warning',
                              msg: data.message
                          });
                      }
                      that.loading = false;
                  },
                  error: function(data) {
                      resultTip({
                          error: 'warning',
                          msg: data.message
                      });
                      that.loading = false;
                  },
              })
          } else {
              this.options = [];
          }
        },
        transferStaff(type){
          if(type=='tab'){
            this.transferId=[this.reportData.current.staff_id]
          }
          this.top_uid=''
          this.transferType=type
          $("#transferModal").modal('show')
        },
        clearOptions(val) {
          // 下拉框隐藏时
          if (!val) {
          this.options=[]
          }
        },
        addStu(id){
          this.top_uid=id
          this.options=[]
          this.staff_name=''
        },
        editLineData(type){
          var data={}
          if(type=='tab'){
            data= {
              top_uid:this.top_uid,
              staff_ids:this.transferId
            }
          }else if(type=='add'){
            data= {
              top_uid:this.staffSelected[0],
              staff_ids:[this.reportData.current.staff_id],
            }
          }else{
            data={
              top_uid:this.dragTop,
              staff_ids:[this.dragIds]
            }
          }
          let that=this
          if(data.top_uid=='' || data.top_uid==undefined){
            resultTip({
                error: 'warning',
                msg: '<?php echo Yii::t('leave', '请选择人员') ?>'
            });
            return
          }
          that.loading=true
          $.ajax({
              url: '<?php echo $this->createUrl("editLine") ?>',
              type: "post",
              dataType: 'json',
              data:data,
              success: function(data) {
                  if (data.state == 'success') {
                    
                    that.getIndex()
                    if(type=='tab'){
                      that.getTreeList(that.reportData.current)
                      $("#editTreeModal").modal('hide')
                      $("#transferModal").modal('hide')
                    }
                    if(type=='add'){
                      that.getTreeList(that.reportData.current)
                      that.reportData.top=that.staffSelected[0]
                      $("#addStaffModal").modal('hide')
                      that.editBox=true
                      that.confirmLoading=false
                    }
                  }else{
                      resultTip({
                          error: 'warning',
                          msg: data.message
                      });
                      that.confirmLoading=false
                      that.loading=false
                  }
              },
              error: function(data) {
                  resultTip({
                      error: 'warning',
                      msg: data.message
                  });
                  that.confirmLoading=false
                  that.loading=false
              },
          })
        },
        addStaff(type,tab){
          let that=this
          this.dep_name=''
          let dataList={}
          if(type==3){
            dataList= {
                "school_id":this.schoolId,
                "status":2,//1上级  2下级
                "current_uid": 0
              }
          }else{
            dataList= {
                "school_id":this.schoolId,
                "status":type,//1上级  2下级
                "current_uid": this.reportData.current.staff_id
              }
          }
          $.ajax({
              url: '<?php echo $this->createUrl("optionalStaff") ?>',
              type: "post",
              dataType: 'json',
              data:dataList,
              success: function(data) {
                  if (data.state == 'success') {
                      that.currentDept=data.data
                      that.reportData.staffInfo=Object.assign( that.reportData.staffInfo, data.data.user_info)
                      that.allDept=Object.assign(that.allDept, that.reportData.staffInfo)
                      if(!tab){
                        that.addType=type
                        if(type==1){
                          that.staffSelected=that.reportData.top!=0?[that.reportData.top]:[]
                        }else{
                          that.staffSelected= JSON.parse( JSON.stringify (that.reportData.last))
                        }
                        that.confirmLoading=false
                        $("#addStaffModal").modal('show')
                      }else if(tab=='special'){
                        that.addType=4
                        that.staffSelected=[]
                        $("#addStaffModal").modal('show')
                      }else if(tab=='add'){
                        that.addType=4
                        that.staffSelected= JSON.parse( JSON.stringify (that.reportData.userCCBackupConfig.cc))
                        $("#addStaffModal").modal('show')
                      }
                  }else{
                      resultTip({
                          error: 'warning',
                          msg: data.message
                      });
                  }
              },
              error: function(data) {
                  resultTip({
                      error: 'warning',
                      msg: data.message
                  });
              },
          })
        },
        showDepName(list){
            if(this.dep_name==list.dep_name){
                this.dep_name=''
                return
            }
            this.dep_name=list.dep_name
            for(let i=0;i<list.user.length;i++){
                if(this.staffSelected.indexOf(list.user[i].uid)!=-1){
                    Vue.set(this.currentDept.user_info[list.user[i].uid], 'disabled', true);
                }
            }
        },
        selectAll(list,index){
          list.user.forEach(item => {
              if(!this.currentDept.user_info[item.uid].disabled){
                  this.staffSelected.push(item.uid)
                  Vue.set(this.currentDept.user_info[item.uid], 'disabled', true);
              }
          });
        },
        assignStaff(list,index,idx){
          if(this.addType==1 || this.addType==3){
            if(this.staffSelected.length==1){
                resultTip({
                    error: 'warning',
                    msg: '<?php echo Yii::t('leave', '最多选择一名成员') ?>'
                });
                return
            }
          }
            if(idx=='search'){
                this.searchStaffList[index].disabled=true
            }else{
                Vue.set(this.currentDept.user_info[list.uid], 'disabled', true);
            }
            this.staffSelected.push(list.uid)
        },
        Unassign(list,index){
            if(this.currentDept.user_info[list]){
                Vue.set(this.currentDept.user_info[list], 'disabled', false);
            }
            this.staffSelected.splice(index,1)
        },
        delSelect(id,type){
          if(type=='top'){
            this.reportData.top=0
          }else{
            this.reportData.last.splice(id,1)
          }
        },
        confirmSatff(){
          let that=this
          if(this.addType==4){
            this.addSpecialCC('add')
          }else if(this.addType==1){
            this.editLineData('add')
          }else{
            if(this.staffSelected.length==0){
              resultTip({
                  error: 'warning',
                  msg: '<?php echo Yii::t('leave', '请选择人员') ?>'
              });
              return
            }
            let postData={}
            if(this.addType==3){
              postData={
                current_uid:this.staffSelected[0]
              }
            }else{
              postData={
                top_uid:this.reportData.top,
                staff_ids:this.staffSelected,
                current_uid: this.reportData.current.staff_id
              }
            }
            
            this.confirmLoading=true
            $.ajax({
              url: '<?php echo $this->createUrl("addLine") ?>',
              type: "post",
              dataType: 'json',
              data:postData,
              success: function(data) {
                  if (data.state == 'success') {
                      resultTip({
                        msg: data.message
                      });
                      if(this.addType==3){

                      }else{
                        that.editBox=true
                        that.reportData.last=that.staffSelected
                      }
                      $("#addStaffModal").modal('hide')
                      that.getIndex()
                      that.confirmLoading=false
                  }else{
                      resultTip({
                          error: 'warning',
                          msg: data.message
                      });
                      that.confirmLoading=false
                  }
              },
              error: function(data) {
                  resultTip({
                      error: 'warning',
                      msg: data.message
                  });
                  that.confirmLoading=false
              },
          })
          }
          
        },
        saveStaff(){
          let that=this
          $.ajax({
              url: '<?php echo $this->createUrl("addLine") ?>',
              type: "post",
              dataType: 'json',
              data: {
                top_uid:this.reportData.top,
                staff_ids:this.reportData.last,
                current_uid: this.reportData.current.staff_id
              },
              success: function(data) {
                  if (data.state == 'success') {
                      resultTip({
                        msg: data.message
                      });
                      that.getIndex()
                  }else{
                      resultTip({
                          error: 'warning',
                          msg: data.message
                      });
                  }
              },
              error: function(data) {
                  resultTip({
                      error: 'warning',
                      msg: data.message
                  });
              },
          })
        },
        topStaff(){
          this.reportData={
            staffInfo:{},
            last:[],
            top:0
          }
          this.addStaff(3)
        },
        clearTree(delType){
          if(delType){
            this.delType=delType
            $("#delModal").modal('show')
            return
          }
          let that=this
          that.confirmLoading=true
          $.ajax({
              url: '<?php echo $this->createUrl("delAllLine") ?>',
              type: "get",
              dataType: 'json',
              data: {
                type:this.type
              },
              success: function(data) {
                  if (data.state == 'success') {
                      that.getIndex()
                      that.delTreeData={}
                      that.confirmLoading=false 
                     $("#delModal").modal('hide')
                  }else{
                      resultTip({
                          error: 'warning',
                          msg: data.message
                      });
                  }
                  that.confirmLoading=false
              },
              error: function(data) {
                  that.confirmLoading=false
                  resultTip({
                      error: 'warning',
                      msg: data.message
                  });
              },
          })
        },
        handleClick(type){
          this.activeName=type
        //   this.getIndex()
        // this.getSchool()
          window.location.href='index?type='+type+'&branchId='+branchId
        },
        saveApproval(type){
          let that=this
          if(this.cc_id==''){
            this.cc_id=this.cc_id_copy
            this.clearCC("clear")
            return
          }
          this.approveLoading=true
          $.ajax({
              url: '<?php echo $this->createUrl("saveApprovalGroupMount") ?>',
              type: "get",
              dataType: 'json',
              data: {
                user_id:this.reportData.current.staff_id,
                id:this.cc_id,
                old_id:this.reportData.mountGroup._id?this.reportData.mountGroup._id:0
              },
              success: function(data) {
                  if (data.state == 'success') {
                    that.getIndex()
                    that.getTreeList(that.reportData.current)
                  }else{
                    resultTip({
                        error: 'warning',
                        msg: data.message
                    });
                    that.approveLoading=false
                  }
              },
              error: function(data) {
                  resultTip({
                    error: 'warning',
                    msg: data.message
                  });
                  that.approveLoading=false
              },
          })
        },
        checkCC(){
          if(!this.showCC && this.reportData.mountGroup._id){
            this.showCC=true
            $('#myCheckbox').prop('checked', true);
            this.clearCC("clear")
          }
        },
        clearCC(delType){
          if(delType){
            this.delType='clearCC'
            $("#delModal").modal('show')
            return
          }
          let that=this
          $.ajax({
              url: '<?php echo $this->createUrl("deleteApprovalGroupMount") ?>',
              type: "get",
              dataType: 'json',
              data: {
                user_id:this.reportData.current.staff_id,
                id:this.cc_id
              },
              success: function(data) {
                  if (data.state == 'success') {
                    resultTip({
                        msg: data.message
                    });
                    that.getIndex()
                    $("#delModal").modal('hide')
                    that.getTreeList(that.reportData.current)
                  }else{
                      resultTip({
                          error: 'warning',
                          msg: data.message
                      });
                  }
              },
              error: function(data) {
                  resultTip({
                      error: 'warning',
                      msg: data.message
                  });
              },
          })
        },
        linkHref(){
          window.open('approvalGroup?type='+type+'&branchId='+branchId)
        },
        refreshList(){
          let that=this
          this.refreshLoading=false
          $.ajax({
              url: '<?php echo $this->createUrl("approvalGroupList") ?>',
              type: "get",
              dataType: 'json',
              data: {
                school_id:this.schoolId,
                type:this.type
              },
              success: function(data) {
                  if (data.state == 'success') {
                    that.reportData.mountGroupList=data.data
                    resultTip({
                        msg: data.message
                    });
                    that.refreshLoading=true
                  }else{
                    resultTip({
                        error: 'warning',
                        msg: data.message
                    });
                    that.refreshLoading=true

                  }
              },
              error: function(data) {
                that.refreshLoading=true
                  resultTip({
                      error: 'warning',
                      msg: data.message
                  });
              },
          })
        },
        addSpecialCC(type){
          if(type=='add' && this.staffSelected.length==0){
            resultTip({
                error: 'warning',
                msg: '<?php echo Yii::t('leave', '请选择人员') ?>'
            });
            return
          }
          let that=this
          this.approvedLoading=true
          $.ajax({
              url: '<?php echo $this->createUrl("saveApprovalFirstConfig") ?>',
              type: "get",
              dataType: 'json',
              data: {
                cc_user_list:type=='add'?this.staffSelected:[],
                setType:type=='add'?1:999,
                current_uid: this.reportData.current.staff_id
              },
              success: function(data) {
                  if (data.state == 'success') {
                    $("#addStaffModal").modal('hide')
                    that.getTreeList(that.reportData.current)
                  }else{
                    resultTip({
                      error: 'warning',
                      msg: data.message
                    });
                    that.approvedLoading=false
                  }
              },
              error: function(data) {
                  resultTip({
                      error: 'warning',
                      msg: data.message
                  });
                  that.approvedLoading=false
              },
          })
        },
        delSpecialCC(id){
          if(id){
            this.delType='special'
            this.delId=id
            $("#delModal").modal('show')
            return
          }
          let that=this
          let cc_user = that.reportData.userCCBackupConfig.cc.filter(item => item !== this.delId);
          $.ajax({
              url: '<?php echo $this->createUrl("saveApprovalFirstConfig") ?>',
              type: "get",
              dataType: 'json',
              data: {
                cc_user_list:cc_user,
                setType:1,
                current_uid: this.reportData.current.staff_id
              },
              success: function(data) {
                  if (data.state == 'success') {
                    $("#delModal").modal('hide')
                    resultTip({
                        msg: data.message
                    });
                    that.getTreeList(that.reportData.current)
                  }else{
                      resultTip({
                          error: 'warning',
                          msg: data.message
                      });
                  }
              },
              error: function(data) {
                  resultTip({
                      error: 'warning',
                      msg: data.message
                  });
              },
          })
        }
      }
    })
  </script>
<style >
  [v-cloak]{
        display:none;
    }
  .font600{
    font-weight:600
  }
  #container{
    width:100%;
    height:100%;
    position: relative;
  }
    .bg-color-orange{
     /* border:1px solid #fff */

    }
   .bg-tomato{
     border:1px solid #4D88D2
   }
    /* 盒子css */
    .floating {
    background: rgba(0, 0, 0, 0.7);
    width: 160px;
    height: 100px;
    position: absolute;
    color: #fff;
    padding-top: 15px;
    border-radius: 15px;
    padding-left: 15px;
    box-sizing: border-box;
    left: 0;
    top: 0;
    transition: all 0.3s;
    z-index: 999;
    text-align: left;
    font-size: 12px;
    }
    .back_box {
      position: absolute;
      width: 100%;
      height: 100%;
    }
    .modalscroll-box::-webkit-scrollbar{
      background-color: #ccc;
      height:6px;
      width: 5px;
    } 
    .modalscroll-box::-webkit-scrollbar-thumb {
        border-radius   : 10px;
        background-color: skyblue;
        background-color: #D8D8D8;
        background-image: none
    }
    .modalscroll-box::-webkit-scrollbar-track {
        box-shadow   : inset 0 0 5px rgba(0, 0, 0, 0);
        background   : #fff;
        border-radius: 10px;
    }
    .bgBox{
      background: #f7f7f7
    }
    .drag_box {
      position: absolute;
      z-index: 10;
      user-select: none; /* 不可选中,为了拖拽时不让文字高亮 */
    }

    .text {
      transform-origin: 0 0; 
    }
    .org-tree-container{
      width:100%
    }
    .editTreeCss .org-tree-node-label .org-tree-node-label-inner{
      cursor: pointer;
    }
    .org-tree-node-label .org-tree-node-label-inner{
      padding:0 !important;
    }
    .option{
      padding:5px 8px;
      border:1px solid #fff;
      display: flex;
      align-items: center;
      background:#fff;
      width:180px;
      position: relative;
    }
    .optionView{
      padding: 5px 8px;
      border: 1px solid #fff;
      display: flex;
      align-items: center;
      background: #fff;
      width: 150px;
      margin-bottom:3px;
      margin-right:3px
    }
    .scale{
      background: #FFFFFF;
      box-shadow: 0px 2px 6px 0px rgba(0,0,0,0.16);
      border-radius: 4px;
      font-size: 14px;
      padding: 6px;
      z-index: 999
    }
    .fixedText{
      padding:6px
    }
    .fixedText:hover{
      background: #F7F7F8;
    }
    .delTree{
      color: #D9534F;
    }
    .editBtn{
      position: absolute;
      right:15px;
      top:0;
      z-index: 999
    }
    .editTop{
      width: 100%;
      padding:18px 15px;
      background: #FFFFFF;
      box-shadow: -1px 3px 5px 0px rgba(0,0,0,0.12);
      margin-bottom: 5px;
    }
    .department_name{
      padding:6px 12px;
      background: #F7F7F8;
      border-radius: 4px;
      margin-bottom:8px
    }
    
    .point{
      width: 9px;
      height: 9px;
      background: #4D88D2;
      border-radius:50%;
      margin-top:5px;
    }
    .bgchild{
      background: #F7F7F8;
      border-radius: 4px;
    }
    .borderLeft{
      border-left: 1px solid #4D88D2;
      margin-left: 4px;
      position: absolute;
      height: 100%;
      left: 0px;
      top: 5px;
    }
    .ml0{
      margin:0
    }
    .colorBlue{
      color:#4D88D2
    }
    .listMedia{
        padding:8px;
        border: 1px solid #fff;
    }
    .listMedia:hover{
        background: rgba(77,136,210,0.1);
        border-radius: 4px;
        border: 1px solid #4D88D2;
        cursor: pointer;
    }
    .borderLeftpos {
        display: inline-block;
        border-left: 1px solid #E4E7ED;
        height: 100%;
        width: 1px;
        position: absolute;
        left: 50%;
    }
    .optionSearch{
        height:auto;
        padding:5px 20px
    }
    .text_overflow {
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        line-height: 14px;
    }
    .dragItem.hover-background {
      border: 1px dashed #4D88D2 !important;
      background:#F7F7F8 !important
    }
    .dragItem:hover{
      background:#F7F7F8 !important

    }
    .tree-background{
      border: 1px solid #4D88D2 !important;
      border-radius: 3px;
    }
    .dragItem{
      border:1px solid #fff;
      border-radius: 3px;
      opacity: 1;
    }
    .org-tree-container{
      background: transparent;
    }
    .dragClass{
      background: #D9534F !important;
      border-radius: 3px;
      /* opacity: .5; */
      color:#fff;
      border: 1px solid #D9534F !important;
    }
    .list-item {
        width: 100%;
        height: 50px;
        margin-top: 10px;
        border: 1px solid gray;
        margin-right: 10px;
        background-color: gray;
        color: #fff;
        display:none
    }
    .btn-default.disabled, .btn-default[disabled], fieldset[disabled] .btn-default, .btn-default.disabled:hover, .btn-default[disabled]:hover, fieldset[disabled] .btn-default:hover, .btn-default.disabled:focus, .btn-default[disabled]:focus, fieldset[disabled] .btn-default:focus, .btn-default.disabled:active, .btn-default[disabled]:active, fieldset[disabled] .btn-default:active, .btn-default.disabled.active, .btn-default[disabled].active, fieldset[disabled] .btn-default.active{
      background-color: #F2F3F5;
    }
    .badge{
      width: 20px;
      height: 20px;
      line-height: 20px;
      padding:0
    }
    .fristStaff{
      background: #FFFFFF;
      box-shadow: 0px 2px 8px 0px rgba(0,0,0,0.12);
      border-radius: 4px;
      font-weight: 400;
      font-size: 14px;
      color: #CCCCCC;
      text-align: center;
      font-style: normal;
      padding:10px 24px;
      cursor: pointer;
    }
    .flexText{
      width:0
    }
    .word-break{
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .colorRed{
        color:rgb(217, 83, 79)
    }
    .allCheck{
        position: absolute;
        right: 10px;
        top: 0;
    }
    .border{
        border: 1px solid #DCDFE6;
        padding: 16px;
        border-radius: 4px;
    }
    .flex1Text{
      width:0
    }
    .treeNum{
      background: #666666;
      border-radius: 20px;
      border: 1px solid #FFFFFF;
      padding:2px 6px;
      color:#fff;
      height: 20px;
      line-height: 19px;
      display: inline-block;
      margin-left: 10px;
      padding: 0 6px;
    }
    .treeFlexText{
      width:0;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      text-align:left
    }
    .img28{
      height: 28px;
      width: 28px;
      margin-right:6px;
      border-radius: 50%;
      object-fit: cover;
    }
    .viewMoreP{
      padding: 4px;
    }
    .viewMore {
      max-width: 630px;
      margin-left: 0;
      padding: 6px;
      overflow-y: auto;
      direction: ltr;
    }
    .viewMoreHeight{
      max-height:272px
    }
    .editTreeCss .org-tree-node-label-inner div{
      padding:5px 4px;
      border:1px solid #fff;
      display: flex;
      align-items: center;
      background:#fff;
      width:180px
    }
    .img42{
      width: 42px;
      height: 42px;
      object-fit: cover;
      border-radius:50%;
    }
    .img24{
      width: 24px;
      height: 24px;
      object-fit: cover;
      border-radius:50%;
    }
    .el-tabs__item:hover,.el-tabs__item.is-active{
      color:#4D88D2
    }
    .el-tabs__active-bar{
      background-color:#4D88D2
    }
    .pb50{
      padding-bottom:50px
    }
    .no-animated-tabs ..el-tabs__active-bar {
      -webkit-transition: none !important;
      transition: none !important
    }
    .tabList{
      border-bottom:1px solid #D9D9D9;
    }
    .pl0{
      padding-left:0 !important
    }
    .borderBto{
      border-bottom:2px solid #4D88D2;
      color:#4D88D2
    }
    .tabHeight{
      height: 40px;
      box-sizing: border-box;
      line-height: 40px;
      display:inline-block;
    }
    .ccupList{
      background:rgba(91, 192, 222, 0.10);
      border-radius: 4px;
      padding: 16px 8px;
    }
    .ccList{
      background:rgba(240, 173, 78, 0.10);
      border-radius: 4px;
      padding: 16px 8px;
      margin-top: 16px;
    }
    .img24{
      width: 24px;
      height: 24px;
      object-fit: cover;
      border-radius:50%;
    }
    .org-tree-node-btn{
      background: #F0AD4E;
      border: 1px solid #F0AD4E;
    }
    .org-tree-node-btn:hover{
      background: #F0AD4E;
    }
    .org-tree-node-btn:before{
      border-top: 1px solid #fff;
    }
    .org-tree-node-btn:after{
      border-left: 1px solid #fff;
    }
    .org-tree-node-btn.expanded:before{
      border-top: 1px solid #ccc;
    }
    .org-tree-node-label-inner .expanded{
      background: #fff !important;
      border: 1px solid #ccc !important;
    }
    .org-tree-node-label-inner .expanded:hover{
      background: #fff !important;
      border: 1px solid #ccc !important;
    }
    .auditing{
      font-size: 12px;
      position: absolute;
      display: inline-block;
      width: 16px;
      height: 16px;
      background: #5CB85C;
      color: #fff;
      border-radius: 50%;
      text-align: center;
      line-height: 16px;
      border: 1px solid #fff;
      left: 23px;
      top: 20px;
      display:none
    }
    .levelTag{
      color:#D9534F;
      background:#F2DEDE;
      border-radius:4px;
      padding:2px 6px;
      font-size:12px
    }
    .refresh{
      color: #D9534F;
      background: #FCF1F0;
      padding: 2px 6px;
      border-radius: 2px;
      cursor: pointer;
      margin-left: 4px;
    }
    .yellowConfig,.yellowConfig:hover{
      background: #F0AD4E !important;
      border: 1px solid #F0AD4E !important;
    }
    .greenConfig,.greenConfig:hover{
      background: #5CB85C !important;
      border: 1px solid #5CB85C !important;
    }
    .specialCC{
      display: inline-block;
      background: #FFFFFF;
      border-radius: 2px;
      padding:4px 8px;
      margin: 4px 8px;
    }
    .overFlow{
      overflow:hidden
    }
    .approveLoading .el-loading-mask .circular{
      height: 30px;
      width: 30px;
    }
</style>
 