<style>
    .counters {
        color: #b2b2b2;
        text-align: right;
    }
    .input_1{
        width: 61px;
    }
</style>
<div class="container-fluid">
    <ol class="breadcrumb">
            <li><?php echo CHtml::link(Yii::t('site','Teaching Tasks'), array('//mteaching/default/index'))?></li>
            <li><?php echo CHtml::link(Yii::t('principal','Secondary School Achievement Report'), array('//mteaching/default/index'))?></li>
            <li class="active"><?php echo Yii::t('principal','Start an Achievement Report');?></li>
    </ol>
    <div class="row">
        <div class="col-md-12">
            <?php foreach($schoolYear as $yid=> $_schoolYear): ?>
            <div class="panel panel-default">
                <div class="panel-heading">
                    <div class="row">
                        <div class="col-md-10"><?php echo $_schoolYear . ' - ' . ($_schoolYear+1); ?></div>
                        <div class="col-md-2">
                            <button type="button" class="btn btn-xs btn-primary pull-right mr5" onclick="addItem(<?php echo $yid ?>, 0);">
                                <span class="glyphicon glyphicon-plus"></span></button>
                        </div>
                    </div>
                </div>
                <ul class="list-group">
                    <?php foreach($items as $_items): ?>
                        <?php if($yid == $_items->calendar): ?>
                        <li class="list-group-item" data-id=<?php echo $_items->id ?> data-sid=<?php echo $_items->id ?>>
                            <div class="row">
                                <div class="col-md-10">
                                    <code class="pull-left" style="font-size: 20px"><?php echo $_items->cycle ?></code>
                                    <p class="col-md-11 text-info"><?php echo $_items->getTitle() ?></p>
                                    <p class="col-md-11 text-info"><?php echo date("Y-m-d",$_items->start_time) . " - " . date("Y-m-d",$_items->end_time) ?></p>
                                </div>
                                <div class="col-md-2">
                                    <a role="button" class="btn btn-xs btn-danger J_ajax_del pull-right mr5" href="<?php echo $this->createUrl
                                    ('delereport', array('id' => $_items->id)) ?>"><span class="glyphicon glyphicon-minus"></span></a>
                                    <a href="javascript:;" class="btn btn-xs btn-primary pull-right mr5" onclick="addItem(<?php echo $yid ?>, <?php echo $_items->id;?>);"><span class="glyphicon glyphicon-pencil"></span></a>
                                </div>
                            </div>
                        </li>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </ul>
            </div>
            <?php endforeach;?>
        </div>
    </div>
</div>

<div class="modal fade" id="addModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg">
        <form action="<?php echo $this->createUrl('updatereport')?>" method="post" class="J_ajaxForm form-horizontal">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">×</span></button>
                <h4 class="modal-title"><?php echo Yii::t('principal','Achievement Report') ?></h4>
            </div>
            <div class="modal-body">
            </div>
            <div class="modal-footer">
                <button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global','Submit');?></button>
                <button type="button" class="btn btn-default"  data-dismiss="modal"><?php echo Yii::t('global','Cancel');?></button>
            </div>
        </div>
        </form>
    </div>
</div>
<script>

    function addItem(yid, id) {

        $.get('<?php echo $this->createUrl('updatereport')?>', {id: id}, function(data){
            $('#addModal .modal-body').html( data );
            $('#addModal #AchievementReport_calendar').val(yid);
        });

        $('#addModal').modal({backdrop: 'static', keyboard: false});
    }

    function cbSuccess() {
        setTimeout(function () {
            location.reload();
        }, 1000)
    }
</script>