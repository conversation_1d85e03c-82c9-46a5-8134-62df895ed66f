<?php
$statusList = IvyVisitsBasicInfo::status();
$statusList = array('全部状态') + $statusList;
$cfgs = $this->loadConfig();
$knowusList = array();
if (isset($cfgs['knowus']))
{
    foreach ($cfgs['knowus'] as $k=>$v)
    {
        $knowusList[$k] = (Yii::app()->language=='zh_cn') ? $v['cn'] : $v['en'];
    }
}
$knowusList = array('全部来源') + $knowusList;
$ageList = array();
if (isset($cfgs['ages']))
{
    foreach ($cfgs['ages'] as $k=>$v)
    {
        $ageList[$k] = $v;
    }
}
$ageList = array('全部年龄') + $ageList;
$visitStatusList = array('参观记录','visit' =>'来访参观','appointment'=>'预约参观','transfer'=>'转移校园');
$timestamp = array('最近追踪','10' =>'半个月','20'=>'一个月','30'=>'两个月');
?>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site','Campus Operations'), array('//mcampus/default/index'))?></li>
        <li><?php echo CHtml::link(Yii::t('site','Basic'), array('//mcampus/default/index'))?></li>
        <li class="active">招生管理</li>
    </ol>

    <div class="row">
        <!-- 左侧菜单 -->
        <div class="col-md-2 col-sm-2">
            <?php
            $this->widget('zii.widgets.CMenu',array(
                'items'=> $this->getMenu(),
                'id'=>'pageCategory',
                'htmlOptions'=>array('class'=>'nav nav-pills nav-stacked background-gray'),
                'activeCssClass'=>'active',
                'itemCssClass'=>''
            ));
            ?>
        </div>

        <div class="col-md-10 col-sm-12">
            <div class="mb10">
                <a class="J_modal btn btn-primary" href="<?php echo $this->createUrl('saveRecord'); ?>"><span class="glyphicon glyphicon-plus"></span> 新建记录</a>
            </div>
            <div class="btn-group mb10 row">
                <!-- 搜索框 -->
                <form action="<?php echo $this->createUrl('visitsLog'); ?>" method="get">
                <?php echo Chtml::hiddenField('branchId',$this->branchId); ?>
                <div class="col-lg-12 form-inline mb10">
                    <!-- 状态 -->
                    <div class="form-group">
                    <?php echo Chtml::dropDownList('status',$_GET['status'],$statusList,array('class'=>'form-control')); ?>
                    </div>
    				<!-- 模式 -->
    				<div class="form-group">
                    <?php echo Chtml::dropDownList('visitStatus',$_GET['visitStatus'],$visitStatusList,array('class'=>'form-control')); ?>
                    </div>
                    <!-- 时间 -->
                    <div class="form-group">
                        <?php echo Chtml::textField('daytimeStar',$_GET['daytimeStar'],array('class'=>'form-control datepicker', 'placeholder'=>'选择开始时间')); ?>
                    </div>
                    <div class="form-group">
                        <?php echo Chtml::textField('daytimeEnd',$_GET['daytimeEnd'],array('class'=>'form-control datepicker', 'placeholder'=>'选择结束时间')); ?>
                    </div>
                </div>
                <div class="col-lg-12 form-inline">
                    <!-- 来源 -->
                    <div class="form-group">
                        <?php echo Chtml::dropDownList('knowus',$_GET['knowus'],$knowusList,array('class'=>'form-control')); ?>
                    </div>
                    <!-- 年龄 -->
                    <div class="form-group">
                    <?php echo Chtml::dropDownList('age',$_GET['age'],$ageList,array('class'=>'form-control')); ?>
                    </div>                
    				<!-- 最近追踪 -->
                    <div class="form-group">
                   	<?php echo Chtml::dropDownList('recent_log',$_GET['recent_log'],$timestamp,array('class'=>'form-control')); ?>
                    </div>
                    <!-- 活动 -->
                    <div class="form-group">
                    <?php echo Chtml::dropDownList('event',$_GET['event'],$events,array('class'=>'form-control')); ?>
                    </div>
                    <!-- 内容匹配 -->
                    <div class="form-group">
                        <div class="input-group">
                            <input placeholder="电话、姓名、邮箱等" type="text" class="form-control" name="search" value="<?php echo Yii::app()->request->getParam('search','')?Yii::app()->request->getParam('search',''):''; ?>">
                            <span class="input-group-btn">
                            <button class="btn btn-default" type="submit"><span class="glyphicon glyphicon-search"> </span> </button>
                            </span>
                        </div>
                    </div>
                </div>
                </form>
            </div>
            <div class="panel panel-default">
                <div class="panel-body">
                <?php 
                $this->widget('ext.ivyCGridView.BsCGridView', array(
                    'id'=>'confirmed-visit-grid',
                    'afterAjaxUpdate'=>'js:head.Util.modal',
                    'dataProvider'=>$dataProvider,
                    'enableSorting'=>true,
                    'template'=>"{items}{pager}{summary}",
                    'colgroups'=>array(
                        array(
                            "colwidth"=>array(50,50,50,50,50,50,50,50,50,100),
                        )
                    ),
                    'columns'=>array(
                        array(
                            'name'=>'家长信息',
                            'type'=>'raw',
                            'value'=>'$data->basic->parent_name',
                        ),                    
                        array(
                            'name'=>'孩子姓名',
                            'value'=>'$data->basic->child_name',
                        ),
                        array(
                            'name'=>'孩子年龄',
                            'type'=>'raw',
                            'value'=>'CommonUtils::getAge($data->basic->birth_timestamp)',
                        ),
                        array(
                            'name'=>'家长电话',
                            'value'=>'$data->basic->tel',
                        ),
                        array(
                            'name'=>'有过入学申请',
                            'value'=>array($this, 'getAdmission'),
                        ),
                        /*array(
                            'name'=>'国家',
                            'type'=>'raw',
                            'value'=>array($this, 'getCountry'),
                        ),  */
                        array(
                            'name'=>'来访状态',
                            'type'=>'raw',
                            'value'=>function ($data)
                            {
                                switch ($data->newrecord->category) {
                                    case 'visit':
                                        echo '来访';
                                        break;
                                    case 'appointment':
                                        echo '预约';
                                        break;
                                    case 'transfer':
                                        echo '转移';
                                        break;
                                    case 'event':
                                        echo '活动';
                                        break;
                                    default:
                                        echo '未知';
                                        break;
                                }
                            }
                        ),                     
                        array(
                            'name'=>'预约日期',
                            'type'=>'raw',
                            'value'=>'$data->newrecord->appointment_date ? date("Y-m-d", $data->newrecord->appointment_date) : "无"',
                        ),                       
                        array(
                            'name'=>'来访日期',
                            'type'=>'raw',
                            'value'=>'$data->newrecord->visit_timestamp ? date("Y-m-d", $data->newrecord->visit_timestamp) : "无"',
                        ),
                        array(
                            'name'=>'跟踪记录',
                            'type'=>'raw',
                            'value'=>'$data->newlog->content',
                        ),
                       array(
                           'class' => 'CButtonColumn',
                           'template' => '{edit}  {tracelog}  {transfer}',
                           'buttons' => array(
                               'edit' => array(
                                   'label' => Yii::t('visit', '管理'),
                                   'url' => 'Yii::app()->controller->createUrl("basicUpdate",array("id"=>$data->basic->id))',
                                   'options' => array('class'=>'J_modal btn btn-info btn-xs'),
                               ),
                               'connect' => array(
                                   'label' => Yii::t('visit', '关联孩子'),
                                   'url' => 'Yii::app()->controller->createUrl("linkChild",array("id"=>$data->basic->id))',
                                   'options' => array('class'=>'J_modal btn btn-info btn-xs'),
                               ),                           
                               'tracelog' => array(
                                   'label' => Yii::t('visit', '跟踪记录'),
                                   'url' => 'Yii::app()->controller->createUrl("tracelog",array("vid"=>$data->visit_id,"bid"=>$data->basic->id))',
                                   'options' => array('class'=>'J_modal btn btn-info btn-xs'),
                               ),                           
                               'qrcode' => array(
                                   'label' => Yii::t('visit', '二维码'),
                                   'url' => 'Yii::app()->controller->createUrl("visitChangeDate",array("id"=>$data->visit_id))',
                                   'options' => array('class'=>'J_modal btn btn-info btn-xs'),
                               ),
							   'transfer' => array(
                                   'label' => Yii::t('visit', '转移孩子'),
                                   'url' => 'Yii::app()->controller->createUrl("Transfer",array("id"=>$data->visit_id, "childid"=>$data->basic->id))',
                                   'options' => array('class'=>'J_modal btn btn-info btn-xs'),
                               ),
                           ),
                       ),
                    ),
                ));
             ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>
<script>
    // 模态框
    var modal = '<div class="modal fade" id="modal" tabindex="-1" role="dialog" aria-labelledby="modal"><div class="modal-dialog" role="document"><div class="modal-content"></div></div></div>';
    $('body').append(modal);
	
    $('.datepicker').datepicker({
        changeMonth: true,
        changeYear: true,
        dateFormat:'yy-mm-dd',
    });
</script>