<style>
    .radio label, .checkbox label {
        padding-left: 0;
    }
</style>
<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">×</span>
    </button>
        <h4 class="modal-title" id="exampleModalLabel"><?php echo ($model->title) ? $model->title: "添加时间";  ?></h4>
</div>

<?php $form = $this->beginWidget('CActiveForm', array(
    'id' => 'schedule-form    ',
    'enableAjaxValidation' => false,
    'htmlOptions' => array('class' => 'J_ajaxForm form-horizontal', 'role' => 'form')
//    ,'action' => '111'
)); ?>
<div class="modal-body" id="scheduleVue">
    <div class="form-group">
        <label class="col-xs-2 control-label"><label for="AsaCourseGroup_title_cn" class="">标题</label>
        </label>

        <div class="col-xs-10">
            <div class="col-xs-7">
                <input class="form-control" name="AsaSchedule[title]" type="text" v-model="scheduleTitle"/>
            </div>
            <div class="col-xs-5">
                <?php if ($courseList): ?>
                    <div class="pull-right">
                        <h5 class="col-xs-12 text-danger"><label><?php echo Yii::t('asa', '修改会影响以下课程：') ?></label></h5>

                        <div class="col-xs-12">
                            <?php foreach ($courseList as $item) { ?>
                                <div><?php echo $item; ?></div>
                            <?php } ?>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 control-label"><label for="AsaCourseGroup_title_cn"
                                                     class="">类型</label>
        </label>

        <div class="col-xs-10 checkbox">
            <div class="col-xs-8" v-if="programType == 1">
                <label>
                    <input type="radio" value="1" v-model="regularFlag"> 常规时间表（同一门课程的一个班每周只有一节课）</label>
            </div>
            <div class="col-xs-8" v-if="programType == 1">
                <label>
                    <input type="radio" name="AsaSchedule[week_day]" value="0" v-model="regularFlag"> 非常规时间表（同一门课程的一个班每周有多节课）</label>
            </div>
            <div class="col-xs-5" v-if="programType == 10">
                <label>
                    <input type="radio" name="AsaSchedule[week_day]" value="10" checked> 延时服务时间表
                </label>
            </div>
            <div class="col-xs-5" v-if="programType == 20">
                <label>
                    <input type="radio" name="AsaSchedule[week_day]" value="20" checked> 冬夏令营（适用于日期连续的课程）</label>
            </div>
        </div>
    </div>
    <div class="form-group" v-if="regularFlag == 1 && programType == 1">
        <label class="col-xs-2 control-label"><label for="AsaCourseGroup_title_cn"
                                                     class="required">请选择时间</label></label>

        <div class="col-xs-10">
            <div class="col-xs-7">
                <select maxlength="255" class="form-control" name="AsaSchedule[week_day]" :value="scheduleWeekday"
                        v-once>
                    <option value="1">周一</option>
                    <option value="2">周二</option>
                    <option value="3">周三</option>
                    <option value="4">周四</option>
                    <option value="5">周五</option>
                    <option value="6">周六</option>
                    <option value="7">周日</option>
                </select>
            </div>
        </div>
    </div>
    <div class="form-group" v-if="regularFlag == 0 && programType == 1">
        <label class="col-xs-2 control-label"><label for="AsaCourseGroup_title_cn"
                                                     class="required">请选择时间</label></label>

        <div class="col-xs-10">
            <div class="col-xs-7">
                <p>提示：按住 ctrl 可多选</p>
                <select multiple maxlength="255" class="form-control" name="AsaSchedule[week_multiple][]" v-model="weekMultiple"
                        v-once>
                    <option value="1">周一</option>
                    <option value="2">周二</option>
                    <option value="3">周三</option>
                    <option value="4">周四</option>
                    <option value="5">周五</option>
                    <option value="6">周六</option>
                    <option value="7">周日</option>
                </select>
            </div>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 control-label"><label for="AsaCourseGroup_title_cn"
                                                     class="required">默认时间</label></label>

        <div class="col-xs-10">
            <div class="col-xs-12 form-inline" id="">
                <select class="form-control mr10" v-model="defaultReady_s" name="defaultTime[startH]">
                    <option value="">时</option>
                    <option :value="timeCompletion(num - 1)" v-for="num in 24">{{timeCompletion(num - 1)}}</option>
                </select>
                <select class="form-control" v-model="defaultReady_e" name="defaultTime[startM]">
                    <option value="">分</option>
                    <option :value="timeCompletion((num - 1) * 5)" v-for="num in 12">{{timeCompletion((num - 1) * 5)}}
                    </option>
                </select>
                -
                <select class="form-control mr10" v-model="defaultEnd_s" name="defaultTime[endH]">
                    <option value="">时</option>
                    <option :value="timeCompletion(num - 1)" v-for="num in 24">{{timeCompletion(num - 1)}}</option>
                </select>
                <select class="form-control mr10" v-model="defaultEnd_e" name="defaultTime[endM]">
                    <option value="">分</option>
                    <option :value="timeCompletion((num - 1) * 5)" v-for="num in 12">{{timeCompletion((num - 1) * 5)}}
                    </option>
                </select>
            </div>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 control-label"><label for="AsaCourseGroup_title_cn">状态</label></label>

        <div class="col-xs-10 checkbox">
            <div class="col-xs-3">
                <label>
                    <input type="radio" name="AsaSchedule[state]" id="" value="1" v-model="scheduleState"> 有效</label>
            </div>
            <div class="col-xs-3">
                <label>
                    <input type="radio" name="AsaSchedule[state]" id="" value="0" v-model="scheduleState"> 无效</label>
            </div>
        </div>
    </div>

    <div class="form-group" v-if="programType != 10">
        <label class="col-xs-2 control-label"><label for="AsaCourseGroup_title_cn">是否共享</label></label>
        <div class="col-xs-10">
            <h5 class="col-xs-12">
                <label>
                    <input name="AsaSchedule[shared]" type="checkbox" value="1" v-bind:checked="shared == 1"/> 共享
                </label>
                <small>如果有多个课程组,开启此功能可以彼此共享时间表</small>
            </h5>
        </div>
    </div>

    <div class="form-group" v-if="programType != 10">
        <div class="col-xs-2 control-label">

            <label>已选时间
            </label>
        </div>
        <div class="col-xs-10">
            <div class="col-md-4 col-xs-12">

                <div class="datepicker mb10" name="" id="attendance_date_start"></div>
            </div>

            <div class="col-md-8 col-xs-12">
                <div v-if="isEmptyObj(scheduleDate)">
                    <p class="">已选时间：{{shoppingNumber}} 个</p>

                    <div class="form-inline mb5" :class="sdate" _value="1" v-for="(sdate,index) in scheduleDate">
                        <span class="badge" style="background: #5bc0de;">{{week[sdate.week]}}</span> <span>{{sdate.dateText}}</span>
                        <input type="hidden" :name="'date[' + index + '][schedule_date]'" :value="sdate.dateText"/>
                        <select class="form-control mr10 defaultReady_s" :value="sdate.ready_s"
                                :name="'date[' + index + '][timeStartH]'"
                                :disabled="(sdate.isReadOnly == 1)?true:false">
                            <option :value="timeCompletion(num - 1)" v-for="num in 24">{{timeCompletion(num - 1)}}
                            </option>
                        </select>
                        <select class="form-control defaultReady_e" :value="sdate.ready_e"
                                :name="'date[' + index + '][timeStartM]'"
                                :disabled="(sdate.isReadOnly == 1)?true:false">
                            <option :value="timeCompletion((num - 1) * 5)" v-for="num in 12">{{timeCompletion((num - 1)
                                * 5)}}
                            </option>
                        </select>
                        -
                        <select class="form-control mr10 defaultEnd_s" :value="sdate.end_s"
                                :name="'date[' + index + '][timeEndH]'" :disabled="(sdate.isReadOnly == 1)?true:false">
                            <option :value="timeCompletion(num - 1)" v-for="num in 24">{{timeCompletion(num - 1)}}
                            </option>
                        </select>
                        <select class="form-control defaultEnd_e" :value="sdate.end_e"
                                :name="'date[' + index + '][timeEndM]'" :disabled="(sdate.isReadOnly == 1)?true:false">
                            <option :value="timeCompletion((num - 1) * 5)" v-for="num in 12">{{timeCompletion((num - 1)
                                * 5)}}
                            </option>
                        </select>
                        <a href="javascript:void(0)" @click="removeTimes(index)" class="btn btn-sm"
                           :disabled="(sdate.isReadOnly == 1)?true:false"><i
                                class="glyphicon glyphicon-trash"></i></a>
                    </div>
                </div>
                <div v-else>
                    <p>请点击左侧选择时间
                    </p>

                    <p class="text-danger fail_info"></p>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal-footer">
    <button type="submit" class="btn btn-primary J_ajax_submit_btn">提交</button>
    <button type="button" class="btn btn-default" @click="clearDate" data-dismiss="modal">取消</button>
    <input type="reset" class="_reset" style="display: none"/>
</div>
<?php $this->endWidget(); ?>
<script>
    var datecounter = 0, week = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
    var scheduleDate, regularFlag, shared, defaultReady_s, defaultReady_e, defaultEnd_e, defaultEnd_s, scheduleTitle, scheduleWeekday, scheduleState, weekMultiple;
    var scheduleIdDateObj = <?php echo CJSON::encode($scheduleIdDateObj); ?>;
    var programType = <?php echo ($groupObj) ? $groupObj->program_type : 2  ;?>;
    //    var programType = 10;
    if (!$.isArray(scheduleIdDateObj)) {
        scheduleDate = scheduleIdDateObj.scheduleDate;
        regularFlag = scheduleIdDateObj.regularFlag;
        defaultReady_s = scheduleIdDateObj.defaultReady_s;
        defaultReady_e = scheduleIdDateObj.defaultReady_e;
        defaultEnd_e = scheduleIdDateObj.defaultEnd_e;
        defaultEnd_s = scheduleIdDateObj.defaultEnd_s;
        scheduleTitle = scheduleIdDateObj.scheduleTitle;
        scheduleWeekday = scheduleIdDateObj.scheduleWeekday;
        scheduleState = scheduleIdDateObj.status;
        weekMultiple = scheduleIdDateObj.weekMultiple;
        if (scheduleIdDateObj.scheduleWeekday != 0) {
            regularFlag = 1;
        } else {
            regularFlag = 0
        }

        if (scheduleIdDateObj.shared == 1) {
            shared = 1;
        } else {
            shared = 0
        }

    } else {
        scheduleDate = {}, regularFlag = 1, defaultReady_s = '', defaultReady_e = '', defaultEnd_e = '', defaultEnd_s = '', scheduleTitle = ''
            , scheduleWeekday = 1 , scheduleState = 1, weekMultiple = [];
    }

    var scheduleVue = new Vue({
        el: "#scheduleVue",
        data: {
            scheduleTitle:scheduleTitle,  //标题
            scheduleDate:scheduleDate,   //增加的时间
            defaultReady_s:defaultReady_s, //默认时间开始时
            defaultReady_e:defaultReady_e, //默认时间开始分
            defaultEnd_s:defaultEnd_s,   //默认时间结束时
            defaultEnd_e:defaultEnd_e,   //默认时间结束分
            regularFlag:regularFlag,    //常规/非常规
            scheduleWeekday:scheduleWeekday,//周几
            scheduleState:scheduleState,  //状态
            week:week,
            shared:shared,
            programType:programType,
            weekMultiple:weekMultiple,  //多选周几
        }, beforeUpdate: function () {

        },
        updated: function () {
        },
        methods: {
            timeCompletion: function (num) {
                var _num;
                if (num < 10) {
                    _num = '0' + num
                } else {
                    _num = num
                }
                return _num;
            },
            removeTimes: function (num) {
                Vue.delete(this.scheduleDate, (num));
                $('.datepicker').datepicker('refresh');
            },
            scheduleObjNumber: function (obj) {
                return Object.keys(obj).length;
            },
            isEmptyObj: function (obj) {
                if (Object.keys(obj).length > 0) {
                    return true;
                }
                return false;
            },
            clearDate: function () {
                scheduleDate = {}, regularFlag = 1, defaultReady_s = '', defaultReady_e = '', defaultEnd_e = '', defaultEnd_s = '', scheduleTitle = ''
                    , scheduleWeekday = 1 , scheduleState = 1;
                $('._reset').click();
            }
        }, computed: {
            shoppingNumber: function () {
                return Object.keys(this.scheduleDate).length;
            }
        }
    });

    $('.datepicker').datepicker({
        dateFormat: 'yy-mm-dd',
        changeMonth: true,
        changeYear: true,
        onSelect: function (dateText, inst) {    //选中事件 第一个参数是选中时间 第二个参数是选中的对象
            var _week = new Date(dateText).getDay();
            if (scheduleVue.defaultReady_s == '' || scheduleVue.defaultReady_e == '' || scheduleVue.defaultEnd_s == '' || scheduleVue.defaultEnd_e == '') {
                $('.fail_info').text('请选择默认时间');
            } else {
                $('.fail_info').text('');
                var _text = dateText.replace(/-/g, '');
                var _counter = scheduleVue.timeCompletion(datecounter++);
                _text += _counter;
                Vue.set(scheduleVue.scheduleDate, _text, {
                    dateText: dateText,
                    ready_s: scheduleVue.defaultReady_s,
                    week: _week,
                    ready_e: scheduleVue.defaultReady_e,
                    end_s: scheduleVue.defaultEnd_s,
                    end_e: scheduleVue.defaultEnd_e
                });
                $('.defaultReady_s:last').val(scheduleVue.defaultReady_s);
                $('.defaultReady_e:last').val(scheduleVue.defaultReady_e);
                $('.defaultEnd_s:last').val(scheduleVue.defaultEnd_s);
                $('.defaultEnd_e:last').val(scheduleVue.defaultEnd_e)
            }

        },
        beforeShowDay: function (date) {//日期控件显示面板之前
            var syear = date.getFullYear();
            var smonth = scheduleVue.timeCompletion(date.getMonth() + 1);
            var sday = scheduleVue.timeCompletion(date.getDate());
            var selectDay = syear + '-' + smonth + '-' + sday;
            var cssClass = '';
            if (scheduleIdDateObj.length != 0) {
                $.each(scheduleIdDateObj.scheduleDate, function (index, list) {
                    if (selectDay == list.dateText) {
                        cssClass = 'date-active';
                    }
                });
            } else {
                $.each(scheduleVue.scheduleDate, function (index, list) {
                    if (selectDay == list.dateText) {
                        cssClass = 'date-active';
                    }
                });
            }
            return [true, cssClass];
        }
    });
</script>