<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
    <h4 class="modal-title" id="exampleModalLabel"><?php echo $model->id?'更新：':'添加：'; ?></h4>
</div>
<?php $form=$this->beginWidget('CActiveForm', array(
    'id'=>'courseGroup-form',
    'enableAjaxValidation'=>false,
    'htmlOptions'=>array('class'=>'J_ajaxForm form-horizontal', 'role'=>'form'),
)); ?>



<div class="modal-body">

    <div class="alert alert-info" role="alert">
        <p>课程组标题规范，注明明确的学年信息和时间信息，该标题将为默认的账单标题，如：</p>
        <br/>
        <ul>
            <li>2016-2017学年秋季课后课</li>
            <li>2016-2017学年第二季课后课</li>
        </ul>
    </div>

    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model,'title_cn'); ?></label>
        <div class="col-xs-8">
            <?php echo $form->textField($model,'title_cn',array('maxlength'=>255,'class'=>'form-control')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model,'title_en'); ?></label>
        <div class="col-xs-8">
            <?php echo $form->textField($model,'title_en',array('maxlength'=>255,'class'=>'form-control')); ?>
        </div>
    </div>    
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model,'open_date_start'); ?></label>
        <div class="col-xs-8">
            <?php echo $form->textField($model,'open_date_start',array('maxlength'=>255,'class'=>'form-control datepicker')); ?>
        </div>
    </div>    
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model,'open_date_end'); ?></label>
        <div class="col-xs-8">
            <?php echo $form->textField($model,'open_date_end',array('maxlength'=>255,'class'=>'form-control datepicker')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model,'status'); ?></label>
        <div class="col-xs-8">
            <?php echo $form->dropDownList($model,'status',array(1=>'有效', 0=>'无效'),array('class'=>'form-control')); ?>
        </div>
    </div>
</div>
<div class="modal-footer">
    <button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global','Submit');?></button>
    <button type="button" class="btn btn-default"  data-dismiss="modal"><?php echo Yii::t('global','Cancel');?></button>
</div>

<?php $this->endWidget(); ?>

<script>
    $('.datepicker').datepicker({
        changeMonth: true,
        changeYear: true,
        dateFormat:'yy-mm-dd'
    });
    function cbGroup() {
        var groupId = '<?php echo $model->id; ?>';
        $('#modal').modal('hide');
        window.location.reload(true);
    }
</script>
