<?php

/**
 * This is the model class for table "ivy_workflow_role".
 *
 * The followings are the available columns in table 'ivy_workflow_role':
 * @property integer $id
 * @property integer $operation_id
 * @property integer $defination_id
 * @property integer $assign_user
 * @property integer $user_node_id
 * @property integer $current_operator
 * @property integer $state
 * @property integer $timestamp
 */
class WorkflowRole extends CActiveRecord
{
    const WORKFLOW_ROLE_CURRENT_CHECKED_USER = 0;
    const WORKFLOW_ROLE_CURRENT_CHECK_USER = 1;

    /**
	 * Returns the static model of the specified AR class.
	 * @return WorkflowRole the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_workflow_role';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('operation_id, defination_id, assign_user, user_node_id, current_operator, state, timestamp', 'numerical', 'integerOnly'=>true),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, operation_id, defination_id, assign_user, user_node_id, current_operator, state, timestamp', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'user'=>array(self::BELONGS_TO,'User','assign_user'),
            'operation'=>array(self::BELONGS_TO,'WorkflowOperation','operation_id')
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'operation_id' => 'Operation',
			'defination_id' => 'Defination',
			'assign_user' => 'Assign User',
			'user_node_id' => 'User Node',
			'current_operator' => 'Current Operator',
			'state' => 'State',
			'timestamp' => 'Timestamp',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('operation_id',$this->operation_id);
		$criteria->compare('defination_id',$this->defination_id);
		$criteria->compare('assign_user',$this->assign_user);
		$criteria->compare('user_node_id',$this->user_node_id);
		$criteria->compare('current_operator',$this->current_operator);
		$criteria->compare('state',$this->state);
		$criteria->compare('timestamp',$this->timestamp);

		return new CActiveDataProvider(get_class($this), array(
			'criteria'=>$criteria,
		));
	}
}