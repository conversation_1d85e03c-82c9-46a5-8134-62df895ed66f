
<?php $form=$this->beginWidget('CActiveForm', array(
	'id'=>'preinvoice-form',
));

?>
<div class="table_full form">
    <?php $_i=1; foreach ($imodel as $ikey=>$invoice):?>
	<div class="h_a">预览账单 <?php echo $_i;?></div>
    <table class="table_full">
        <colgroup>
            <col class="th" width=180>
            <col width=null>
        </colgroup>
        <tbody>
            <?php if ($invoice->payment_type == 'FEETYPE_TUITION' && RC::isErrCode($tuitioncheck) ):?>
            <div><?php echo RC::getTip($tuitioncheck)?></div>
            <?php elseif($annualdue !== true):?>
            <div><?php echo $payTypeTxt;?>截止日已过（<?php echo $annualdue?>）</div>
            <?php elseif(!$invoice->exist['create'] && $invoice->exist['list']):?>
            <?php foreach ($invoice->exist['list'] as $invoiceid=>$iTitle):?>
            <tr>
                <th>此区间已经存在账单</th>
                <td>
                    <?php echo CHtml::link('[打印]', array('/child/invoice/printInvoice', 'invoice_id'=>$invoiceid), array('target'=>'_blank', 'title'=>'打印账单'));?> 
                    <?php echo CHtml::link($iTitle, array('/child/invoice/viewInvoice', 'invoiceid'=>$invoiceid), array('target'=>'_blank', 'title'=>'查看账单'));?>
                </td>
            </tr>
            <?php endforeach;?>
            <?php else:?>
            <?php if($invoice->exist['create'] && $invoice->exist['list']):?>
            <?php foreach ($invoice->exist['list'] as $invoiceid=>$iTitle):?>
            <tr>
                <th>此区间已经存在账单</th>
                <td>
                    <?php echo CHtml::link('[打印]', array('/child/invoice/printInvoice', 'invoice_id'=>$invoiceid), array('target'=>'_blank', 'title'=>'打印账单'));?> 
                    <?php echo CHtml::link($iTitle, array('/child/invoice/viewInvoice', 'invoiceid'=>$invoiceid), array('title'=>'查看账单', 'target'=>'_blank'));?>
                </td>
            </tr>
            <?php endforeach;?>
            <?php endif;?>
            <tr>
                <th>账单标题</th>
                <td><?php echo $form->textField($invoice, '['.$ikey.']title', array('class'=>'input length_6'));?></td>
            </tr>
            <?php if ( !in_array($invoice->payment_type, array('FEETYPE_MATERIAL', 'FEETYPE_DEPOSIT', 'FEETYPE_LIBCARD')) ):?>
            <tr>
                <th>付款区间</th>
                <td><?php echo OA::formatDateTime($invoice->startdate);?> / <?php echo OA::formatDateTime($invoice->enddate);?></td>
            </tr>
            <?php endif;?>
            <tr>
                <th>账单金额</th>
                <td><?php echo $invoice->original_amount;?></td>
            </tr>
            <?php if ($invoice->payment_type == 'FEETYPE_TUITION'):?>
            <tr>
                <th>预交学费余额</th>
                <td><?php echo $invoice->depositAmount;?></td>
            </tr>
            <tr>
                <th>实际付款</th>
                <td><?php $realAmount = $invoice->original_amount-$invoice->depositAmount; echo $realAmount > 0 ? $realAmount : 0;?></td>
            </tr>
            <?php endif;?>
            <tr>
                <th>到期日期</th>
                <td><?php Yii::app()->getController()->widget('zii.widgets.jui.CJuiDatePicker', array(
                    "model"=>$invoice,
                    "attribute"=>"[".$ikey."]duetime",
                    //"options"=>array(
                    //    'changeMonth'=>true,
                    //    'changeYear'=>true,
                    //    'dateFormat'=>'yy-mm-dd',
                    //),
                    'htmlOptions'=>array('class'=>'input length_2 specialDatePicker'),
                ))?></td>
            </tr>
            <tr>
                <th>账单备注</th>
                <td>
                    <?php
                    echo $form->textArea($invoice, '['.$ikey.']memo', array('class'=>'length_6'));
                    echo $form->hiddenField($invoice, '['.$ikey.']child_service_info');
                    echo $form->hiddenField($invoice, '['.$ikey.']fee_type');
                    echo $form->hiddenField($invoice, '['.$ikey.']payment_type');
                    echo $form->hiddenField($invoice, '['.$ikey.']startdate');
                    echo $form->hiddenField($invoice, '['.$ikey.']enddate');
                    echo $form->hiddenField($invoice, '['.$ikey.']amount');
                    echo $form->hiddenField($invoice, '['.$ikey.']amountid');
                    echo $form->hiddenField($invoice, '['.$ikey.']discount_id');
                    echo $form->hiddenField($invoice, '['.$ikey.']project_num');
                    ?>
                </td>
            </tr>
            <?php endif;?>
            
        </tbody>
    </table>
	<?php $_i++; endforeach;?>
</div>
<div class="mb10 tac">
    <button class="btn btn_submit mr10" type="button" onclick="invoiceGenerate(this)">生成账单</button>
</div>
<?php $this->endWidget(); ?>
