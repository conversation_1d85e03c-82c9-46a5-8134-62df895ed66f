<?php 
$statusName = array(
    '20' => Yii::t('user','等待确定'),
    '30' => Yii::t('user','等待发放'),
    '40' => Yii::t('user','已经发放'),
    '45' => Yii::t('user','等待退款'),
    '50' => Yii::t('user','退款完成'),
);
 ?>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('//mcampus/default/index')) ?></li>
        <li><?php echo CHtml::link(Yii::t('site', '商品列表'), array('//mcampus/product/index')) ?></li>
        <li><?php echo CHtml::link(Yii::t('site', $productsCatModel->getName()), array('//mcampus/product/index', 'pcid' => $productsCatModel->id)) ?></li>
        <li><?php echo $statusName[$status]; ?></li>
    </ol>

    <div class="row">
        <div class="col-md-2 col-sm-2 mb10">
            <?php
            $username = Yii::app()->request->getParam('username', '') ? Yii::app()->request->getParam('username', '') : '';
            $classId = Yii::app()->request->getParam('classId', '') ? Yii::app()->request->getParam('classId', '') : '';
            $startdate = Yii::app()->request->getParam('startdate', '') ? Yii::app()->request->getParam('startdate', '') : '';
            $enddate = Yii::app()->request->getParam('enddate', '') ? Yii::app()->request->getParam('enddate', '') : '';
            $this->widget('zii.widgets.CMenu', array(
                'items' => $this->getMenu(),
                'id' => 'pageCategory',
                'htmlOptions' => array('class' => 'nav nav-pills nav-stacked background-gray'),
                'activeCssClass' => 'active',
                'itemCssClass' => ''
            ));
            ?>
        </div>
        <div class="col-md-10 col-sm-10">
            <div class="panel panel-default">
                <div class="panel-heading"><?php echo $statusName[$status]; ?></div>
                <div class="panel-body">
                    <!-- 搜索框 -->
                    <form class="row" style="float: left;width: 100%"
                          action="<?php echo $this->createUrl('showConfirmOrder'); ?>" method="get">
                        <?php echo Chtml::hiddenField('branchId', $this->branchId); ?>
                        <?php echo Chtml::hiddenField('status', $status); ?>
                        <?php echo Chtml::hiddenField('pcid', $pcid); ?>
                        <div class="col-sm-2 form-group">
                            <input type="text" class="form-control" name="username"
                                   placeholder="<?php echo Yii::t('user', 'Child Name') ?>"
                                   value="<?php echo $username ?>">
                        </div>
                        <div class="col-sm-2 form-group">
                            <?php echo Chtml::dropDownList('classId', $classId, $classList, array('class' => 'form-control', 'empty' => Yii::t('labels', 'Class Title'))); ?>
                        </div>
                        <div class="col-sm-2 form-group">
                            <input type="text" class="form-control datepicker" name="startdate"
                                   placeholder="<?php echo Yii::t('user', '开始日期') ?>"
                                   value="<?php echo $startdate ?>" readonly = "readonly">
                        </div>
                        <div class="col-sm-2 form-group">
                            <input type="text" class="form-control datepicker" name="enddate"
                                   placeholder="<?php echo Yii::t('user', '结束日期') ?>"
                                   value="<?php echo $enddate ?>" readonly = "readonly">
                        </div>
                        <div class="">
                            <div class="">
                                <button class="btn btn-default ml5" type="submit"><span class="glyphicon glyphicon-search"> </span></button>
                                <a title="导出" href="javascript:;" onclick="exportRefund('<?php echo $this->createUrl('printOrder',array('pcid' => $pcid, 'status' => $status, 'classid' => $classId, 'userName' => $username)) ?>')"
                                   class="btn btn-default ml5" type="submit"><span class="glyphicon glyphicon-export"> </span>
                                </a>
                            </div>
                        </div>
                    </form>


                    <?php
                    $data = array(
                        array(
                            'name' => 'childid',
                            'value' => array($this, 'getChildName'),
                        ),
                        array(
                            'name' => 'classid',
                            'value' => array($this, 'getChildClass'),
                        ),
                        array(
                            'name' => 'childsNextClass',
                            'value' => '$data->childsNextClass',
                        ),
                        array(
                            'name' => 'pid',
                            'value' => '$data->product->getTitle()',
                        ),
                        array(
                            'name' => Yii::t("products", "规格"),
                            'value' => array($this, 'getProducts'),
                            //'value' => '$data->paid',
                        ),
                        array(
                            'name' => 'unit_price',
                            'value' => '$data->unit_price',
                        ),
                        array(
                            'name' => 'num',
                            'value' => '$data->num',
                        ),
                        array(
                            'name' => Yii::t('laseregg', 'Add Timestamp'),
                            'value' => 'date("Y-m-d H:i:s", $data->updated_time)',
                        ),
                    );
                    if ($status == ProductsInvoice::STATS_REFUNDING) {
                        $data[] = array(
                            'name' => 'refund_info',
                            'value' => array($this, 'getRefundInfo'),
                        );
                        $data[] = 'totalAmount';
                    }

                    if ($status < ProductsInvoice::STATS_SEND || in_array($status, array(ProductsInvoice::STATS_REFUNDING,ProductsInvoice::STATS_REFUNDED))) {
                        $first = array(
                            'class' => 'CCheckBoxColumn',
                            'selectableRows' => 2,
                            'checkBoxHtmlOptions' => array(
                                'class' => 'chose',
                                'value' => '$data->id',
                            ),
                        );
                        array_unshift($data, $first);
                    }

                    $this->widget('ext.ivyCGridView.BsCGridView', array(
                        'id' => 'cradList',
                        'afterAjaxUpdate' => 'js:head.Util.modal',
                        'dataProvider' => $dataProvider,
                        'template' => "{items}{pager}{summary}",
                        'colgroups' => array(/*array(
                        "colwidth" => array(200, 200, 200, 200, 200),
                    )*/
                        ),
                        'columns' => $data

                    ));
                    ?>
                </div>

                <div class="panel-footer">
                <?php if ($status < ProductsInvoice::STATS_SEND) { ?>
                        <button class="btn btn-primary "
                                onclick="check(this)"><?php echo ($status == ProductsInvoice::STATS_PAID) ? Yii::t('labels', '确认订单') : Yii::t('labels', '确认发放'); ?></button>
                        <span id="J_fail_info" class="text-warning" style="line-height:2.6em;padding-left:1em;display: inline-block"></span>
                <?php } ?>
                <!--<ph if (in_array($status, array(ProductsInvoice::STATS_PAID, ProductsInvoice::STATS_REFUNDING))): */?>
                    <button class="btn btn-primary" onclick="refund(this)">确认退款</button>
                <php /*endif; ?>-->
                <?php if ($totalAmount): ?>
                    <?php echo '总金额：' . $totalAmount; ?>
                <?php endif; ?>
                </div>
            </div>
                    <?php
                    $config = Products::config();
                    if ($invoiceItemModel && in_array($status, array(ProductsInvoice::STATS_PAID, ProductsInvoice::STATS_CONFIRM))): ?>
                        <div class="panel panel-default">
                            <div class="panel-heading"><?php echo Yii::t('product', '备货统计') ?></div>
                            <div class="panel-body">
                        <div class="">
                            <table class="table table-hover">
                                <thead>
                                <tr>
                                    <th><?php echo Yii::t('product', '商品') ?></th>
                                    <?php foreach ($config[$invoiceItemModel[0]->product->type]['item'] as $key => $types) { ?>
                                        <th><?php echo Yii::t('products', $types['title']); ?></th>
                                    <?php } ?>
                                    <th><?php echo Yii::t('product', '数量') ?></th>
                                </tr>
                                </thead>
                                <tbody>
                                <?php
                                $config = $config[$invoiceItemModel[0]->product->type]['item'];
                                foreach ($invoiceItemModel as $item):
                                    ?>
                                    <tr>
                                        <td class="col-md-2"><?php echo $item->product->getTitle() ?></td>
                                        <?php
                                        $i = 1;
                                        foreach ($config as $types) {
                                            $_attr = "attr" . $i; ?>
                                            <th><?php echo Yii::t('products', $types['option'][$item->attr->$_attr]); ?></th>
                                            <?php $i++;
                                        } ?>
                                        <td class="col-md-2"><?php echo $item->countnum ?></td>
                                    </tr>
                                <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                </div>
            </div>
                    <?php endif; ?>


        </div>
    </div>
</div>
<script>
    $('.datepicker').datepicker({
        changeMonth: true,
        changeYear: true,
        dateFormat: 'yy-mm-dd'
    });
    var modal = '<div class="modal fade" id="modal" class="1123" tabindex="-1" role="dialog" aria-labelledby="modal"><div class="modal-dialog modal-lg" role="document"><div class="modal-content"></div></div></div>';
    $('body').append(modal);

    function check(btn) {
        var id = [];
        $('.chose').each(function (i, val) {
            if (val.checked) {
                id[i] = $(val).val();
            }
        });

        if (id.length < 1) {
            $('#J_fail_info').text('至少选择一个记录');
            return;
        }

        $(btn).prop("disabled", "disabled");
        $.ajax({
            url: '<?php echo $this->createUrl("confirmOrder", array('branchId'=>$this->branchId));?>',
            data: {status: <?php echo $status ?>, ids: id},
            type: 'post',
            timeout: 5000,
            dataType: 'json',
            global: false
        }).done(function (data, status, xhr) {
//            $(btn).removeProp("disabled");
            if (data.state == 'success') {
                location.reload();
            } else {
                alert(data.message)
            $(btn).removeProp("disabled");
            }

        }).fail(function (xml, status, text) {
            alert(status)
            $(btn).removeProp("disabled");
        });
    }

    function refund(btn) {
        var id = [];
        $('.chose').each(function (i, val) {
            if (val.checked) {
                id[i] = $(val).val();
            }
        });

        if (id.length < 1) {
            $('#J_fail_info').text('至少选择一个记录');
            return;
        }

        $(btn).prop("disabled", "disabled");
        $.ajax({
            url: '<?php echo $this->createUrl("refund", array('branchId'=>$this->branchId));?>',
            data: {status: <?php echo $status ?>, ids: id},
            type: 'post',
            timeout: 5000,
            dataType: 'json',
            global: false
        }).done(function (data, status, xhr) {
            if (data.state == 'success') {
                location.reload();
            } else {
                alert(data.message)
            $(btn).removeProp("disabled");
            }

        }).fail(function (xml, status, text) {
            alert(status)
            $(btn).removeProp("disabled");
        });
    }
    function exportRefund(url) {
        $.ajax({
            url: url,
            type: 'POST',
            success: function (res) {
                if (res.state == 'success') {
                    var data = res.data.items;
                    const filename = res.data.title;
                    const ws_name = "SheetJS";
                    if(data.length<=1){
                        alert('<?php echo Yii::t('asa','No data found.'); ?>');
                        return false;
                    }
                    const worksheet = XLSX.utils.aoa_to_sheet(data);
                    const workbook = XLSX.utils.book_new();
                    XLSX.utils.book_append_sheet(workbook, worksheet, ws_name);
                    // generate Blob
                    const wbout = XLSX.write(workbook, {bookType: 'xlsx', type: 'array'});
                    const blob = new Blob([wbout], {type: 'application/octet-stream'});
                    // save file
                    let link = document.createElement('a');
                    link.href = URL.createObjectURL(blob);
                    link.download = filename;
                    link.click();
                    setTimeout(function() {
                        // 延时释放掉obj
                        URL.revokeObjectURL(link.href);
                        link.remove();
                    }, 500);
                }
            },
            dataType: 'json'
        });
    }
</script>
<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>
