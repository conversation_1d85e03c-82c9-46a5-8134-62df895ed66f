<?php
/**
 * 提现工作流
 */
class RefundCashIvy implements WorkflowTemplate{
    private $template;
    public $controller;
    public $workflow;
    private $step = 'one';
    public function initi(){
        $childId = Yii::app()->request->getParam('childid',0);
        $branchId = Yii::app()->request->getParam('branchId',null);
        if($childId && $branchId){
            $this->setTemplate('initi');
            return $this->single($childId,$branchId);
        }else{
            $data['jsFunction'] = 'WorkflowModalShow';
            $data['modalNameId'] = 'workflow-refund-cash-modal';
            $data['workflow'] = $this->workflow;
            $this->setTemplate('newiniti');
            return $data;
        }
    }
    
     /**
     * 公共保存跳转函数
     */
    public function publicSave(){
        $this->step = Yii::app()->request->getParam('step');
        $buttonCategory = Yii::app()->request->getParam('buttonCategory');
        switch ($this->step){
            case 'one':
                return $this->firstSave();
                break;
            case 'two':
                if ($buttonCategory === 'next'){
                    return $this->twoSave();
                }else{
                    $this->step = 'one';
                    return $this->show();
                }
                break;
        }
    }
    
    /**
    * 第一节点（第一步保存）函数
    */
    public function firstSave(){
        if (isset($_POST['RefundCash']) && Yii::app()->request->isPostRequest){
            $childid = isset($_POST['RefundCash']['childid']) ? $_POST['RefundCash']['childid'] : 0;
            $memo = isset($_POST['RefundCash']['memo']) ? $_POST['RefundCash']['memo'] : null;
            if ($childid === 0){
                $this->controller->addMessage('state', 'fail');
                $this->controller->addMessage('message', '请先选择孩子！');
                $this->controller->showMessage();
            }
            if (empty($memo)){
                $this->controller->addMessage('state', 'fail');
                $this->controller->addMessage('message', '意见不能为空！');
                $this->controller->showMessage();
            }
            $transaction = Yii::app()->db->beginTransaction();
            try{
                //保存工作流业务流程
                $operationData = array('id'=>$this->workflow->getOperateId(),'operation_type'=>  WorkflowConfig::WORKFLOW_TYPE_CASH,'operation_object_id'=>$childid,'defination_id'=>$this->workflow->getWorkflowId(),
                    'branchid'=>$this->workflow->schoolId,'state'=>WorkflowOperation::WORKFLOW_STATS_STEP,'current_node_index'=>$this->workflow->getCurrentNodeId(),
                );
                $operationRet = $this->workflow->saveFirstNodeForWorkflow($operationData,null,$memo,$transaction);
                if ($operationRet === false){
                    $this->controller->addMessage('state', 'fail');
                    $this->controller->addMessage('message', '保存失败');
                    $this->controller->showMessage();
                }
                $transaction->commit();
                $this->setTemplate('initi_two');
                $data['jsFunction'] = 'WorkflowModalShow';
                $data['modalNameId'] = 'workflow-refund-cash-modal';
                $data['workflow'] = $this->workflow;
                //查询账单
                $data['profileBasic'] = ChildProfileBasic::model()->findByPk($childid);
                return $data;
            } catch (Exception $ex) {
                $transaction->rollBack();
                $this->controller->addMessage('state', 'fail');
                $this->controller->addMessage('message', $ex->getMessage());
                $this->controller->showMessage();
            }
            $this->controller->showMessage();
        }
    }
    
    /**
     * 第一节点（最终保存）函数
     */
    public function twoSave(){
        Yii::import('application.components.policy.PolicyApi');
        $model = new WorkflowChildCredit();
        $model->unsetAttributes();
        if (isset($_POST['WorkflowChildCredit']) && Yii::app()->request->isPostRequest){
            $processModel = $this->workflow->getWorkflowProcess(true);
            $model->attributes = $_POST['WorkflowChildCredit'];
            $model->childid = $this->workflow->getOperateObjId();
            $model->schoolid = $this->workflow->getOperateValue('branchid');
            $model->status = WorkflowOperation::WORKFLOW_STATS_UNOP;
            $model->memo = $processModel->process_desc;
            $model->uid = Yii::app()->user->getId();
            $model->updatetime = time();
            if ($model->amount - (PolicyApi::getCreditAmount($model->childid)) > 0.001){
                $this->controller->addMessage('state', 'fail');
                $this->controller->addMessage('message', '提现金额不能大于剩余金额！');
                $this->controller->showMessage();
            }
            if ($model->validate()){
                $transaction = Yii::app()->db->beginTransaction();
                try{
                    if (!$model->save()){
                        $transaction->rollback();
                        $this->controller->addMessage('state', 'fail');
                        $this->controller->addMessage('message', '保存失败');
                        $this->controller->showMessage();
                    }
                    //保存工作流业务流程
                    $operationData = array('id'=>$this->workflow->getOperateId(),'operation_type'=> WorkflowConfig::WORKFLOW_TYPE_CASH,'operation_object_id'=>$model->childid,'defination_id'=>$this->workflow->getWorkflowId(),'branchid'=>$model->schoolid,'state'=>WorkflowOperation::WORKFLOW_STATS_UNOP,'current_node_index'=>$this->workflow->getNextNodeId(),'exte1'=>'');
                    $operationRet = $this->workflow->saveFirstNodeForWorkflow($operationData,array($model->id),$model->memo,$transaction);
                    if ($operationRet === false){
                        $this->controller->addMessage('state', 'fail');
                        $this->controller->addMessage('message', '保存失败');
                        $this->controller->showMessage();
                    }
                    $transaction->commit();
                    $this->jumpMail();
                    $this->controller->addMessage('state', 'success');
                    $this->controller->addMessage('message', '成功');
                    $this->controller->addMessage('data',array('modalNameId'=>'workflow-refund-cash-modal','operatorId'=>$this->workflow->getOperateId(),'html'=>$this->workflow->getNodeListUseHtml()));
                    $this->controller->addMessage('callback', 'saveWorkflowOperator');
                }catch(Exception $e){
                    $transaction->rollBack();
                    $this->controller->addMessage('state', 'fail');
                    $this->controller->addMessage('message', $e->getMessage());
                    $this->controller->showMessage();
                }
            }
            else {
                $this->controller->addMessage('state', 'fail');
                $errs = current($model->getErrors());
                if ($errs)
                    $this->controller->addMessage('message', $errs[0]);
            }
            $this->controller->showMessage();
        }
    }


    /**
     * 提现单入口函数
     * @param int $childId
     * @param string $branchId
     * @return array
     */
    public function single($childId,$branchId){
        if ($childId === 0){
            throw new Exception('孩子在系统中不存在！',500);
        }
        $childProfile = ChildProfileBasic::model()->findByPk($childId);
        if (empty($childProfile) || $branchId !== $childProfile->schoolid){
            throw new Exception('请操作本校孩子！',500);
        }
        Yii::import('application.components.policy.PolicyApi');
        $model = new WorkflowChildCredit();
        $model->unsetAttributes();
        if (isset($_POST['WorkflowChildCredit']) && Yii::app()->request->isPostRequest){
            $model->attributes = $_POST['WorkflowChildCredit'];
            $model->childid = $childId;
            $model->schoolid = $branchId;
            $model->status = WorkflowOperation::WORKFLOW_STATS_UNOP;
            $model->uid = Yii::app()->user->getId();
            $model->updatetime = time();
            if ($model->amount - (PolicyApi::getCreditAmount($childId)) > 0.001){
                $this->controller->addMessage('state', 'fail');
                $this->controller->addMessage('message', '提现金额不能大于剩余金额！');
                $this->controller->showMessage();
            }
            if ($model->validate()){
                $transaction = Yii::app()->db->beginTransaction();
                try{
                    if (!$model->save()){
                        $transaction->rollback();
                        $this->controller->addMessage('state', 'fail');
                        $this->controller->addMessage('message', '保存失败');
                        $this->controller->showMessage();
                    }
                    //保存工作流业务流程
                    $operationData = array('operation_type'=> WorkflowConfig::WORKFLOW_TYPE_CASH,'operation_object_id'=>$childId,'defination_id'=>$this->workflow->getWorkflowId(),'branchid'=>$branchId,'state'=>WorkflowOperation::WORKFLOW_STATS_UNOP,'current_node_index'=>$this->workflow->getNextNodeId(),'exte1'=>'');
                    $operationRet = $this->workflow->saveFirstNodeForWorkflow($operationData,array($model->id),$model->memo,$transaction);
                    if ($operationRet === false){
                        $this->controller->addMessage('state', 'fail');
                        $this->controller->addMessage('message', '保存失败');
                        $this->controller->showMessage();
                    }
                    $transaction->commit();
                    $this->jumpMail();
                    $this->controller->addMessage('state', 'success');
                    $this->controller->addMessage('message', '成功');
                    $this->controller->addMessage('refresh', true);
                }catch(Exception $e){
                    $transaction->rollBack();
                    $this->controller->addMessage('state', 'fail');
                    $this->controller->addMessage('message', $e->getMessage());
                    $this->controller->showMessage();
                }
            }
            else {
                $this->controller->addMessage('state', 'fail');
                $errs = current($model->getErrors());
                if ($errs)
                    $this->controller->addMessage('message', $errs[0]);
            }
            $this->controller->showMessage();
        }
        $data['model'] = $model;
        $data['childProfile'] = $childProfile;
        return $data;
    }


    public function show(){
        //Definition template
        $this->setTemplate('show');
        //Definition modal's id and create js function
        $data['jsFunction'] = 'WorkflowModalShow';
        $data['modalNameId'] = 'workflow-refund-cash-modal';
        if (($this->workflow->isFristNode() === true) && $this->workflow->getOperateStatus() == WorkflowOperation::WORKFLOW_STATS_STEP){
            $this->setTemplate('newiniti');
            //业务
            $process = $this->workflow->getWorkflowProcess(TRUE);
            //查询孩子
            $ftsChild = FtsChild::model()->findByPk($this->workflow->getOperateObjId());
            $data['processDesc'] = $process->process_desc;
            preg_match('/{(.*)}/', $ftsChild->tdata, $matches);
            $data['ftsChild'] = array($ftsChild->id =>$matches[1]);
            $data['select'] = $ftsChild->id;
            $data['workflow'] = $this->workflow;
            return $data;
        }else{
            //业务
            $oDetail = WorkflowOperationDetail::model()->find('operation_id=:operation_id',array(':operation_id'=>$this->workflow->getOperateId()));
            $childCreit = WorkflowChildCredit::model()->with('child')->findByPk($oDetail->operation_object_id);
            if (empty($childCreit)){
                throw new Exception('工作流程业务参数错误！',500);
            }
            $data['childCreit'] = $childCreit;
            $data['workflow'] = $this->workflow;
            //处理POST请求
            if (Yii::app()->request->isPostRequest){
                $buttonCategory = Yii::app()->request->getPost('buttonCategory',0);
                $memo = Yii::app()->request->getPost('memo','');
                $workflowStatus = WorkflowOperation::WORKFLOW_STATS_UNOP;
                if (empty($memo)){
                    $this->controller->addMessage('state', 'fail');
                    $this->controller->addMessage('message', '保存失败，原因：意见必填！');
                    $this->controller->showMessage();
                }
                $transaction = Yii::app()->db->beginTransaction();
                try{
                    //被拒绝
                    if ($buttonCategory == WorkflowOperation::WORKFLOW_STATS_OPDENY){
                        $childCreit->status = WorkflowOperation::WORKFLOW_STATS_OPDENY;
                        $childCreit->cid = 0;
                        if (!$childCreit->save()){
                            $transaction->rollBack();
                            $this->controller->addMessage('state', 'fail');
                            $this->controller->addMessage('message', '提现失败');
                            $this->controller->showMessage();
                        }
                        $workflowStatus = WorkflowOperation::WORKFLOW_STATS_OPDENY;
                    }else{
                        if ($this->workflow->getOperateObjId() !== $childCreit->childid){
                            $this->controller->addMessage('state', 'fail');
                            $this->controller->addMessage('message', "孩子（{$childCreit->childid}）错误！");
                            $this->controller->showMessage();
                        }
                        Yii::import('application.components.policy.PolicyApi');
                        if ($childCreit->amount - (PolicyApi::getCreditAmount($childCreit->childid)) > 0.001){
                            $this->controller->addMessage('state', 'fail');
                            $this->controller->addMessage('message', '提现金额不能大于剩余金额！');
                            $this->controller->showMessage();
                        }
                        $ret = PolicyApi::newChildOutCash($childCreit->childid,$childCreit->amount,$transaction);
                        if ( $ret === false){
                            $this->controller->addMessage('state', 'fail');
                            $this->controller->addMessage('message', '提现失败');
                            $this->controller->showMessage();
                        }
                        $childCreit->status = WorkflowOperation::WORKFLOW_STATS_OPED;
                        $childCreit->cid = $ret;
                        if (!$childCreit->save()){
                            $transaction->rollBack();
                            $this->controller->addMessage('state', 'fail');
                            $this->controller->addMessage('message', '提现失败');
                            $this->controller->showMessage();
                        }
                        $workflowStatus = WorkflowOperation::WORKFLOW_STATS_OPED;
                    }
                    if ($this->workflow->saveWorkflow($memo,$workflowStatus,$transaction) === false){
                        $this->controller->addMessage('state', 'fail');
                        $this->controller->addMessage('message', '提现失败');
                        $this->controller->showMessage();
                    }
                    $transaction->commit();
                    $this->jumpMail();
                    $this->controller->addMessage('state', 'success');
                    $this->controller->addMessage('message', '成功');
                    $this->controller->addMessage('data',array('modalNameId'=>'workflow-refund-cash-modal','operatorId'=>$this->workflow->getOperateId(),'html'=>$this->workflow->getNodeListUseHtml()));
                    $this->controller->addMessage('callback', 'saveWorkflowOperator');
                    $this->controller->showMessage();
                } catch (Exception $ex) {
                    $transaction->rollBack();
                    $this->controller->addMessage('state', 'fail');
                    $this->controller->addMessage('message', $ex->getMessage());
                    $this->controller->showMessage();
                }
            }
            return $data;
        }
    }
   
    public function jumpMail() {
        $branchList = Branch::model()->getBranchList();
        $branchTitle = $branchList[$this->workflow->getOperateValue('branchid')];
        $childName = WorkflowConfig::getChildName($this->workflow->getOperateValue('operation_object_id'));
        $workflowTitle = CommonUtils::autoLang($this->workflow->definationObj->defination_name_cn,$this->workflow->definationObj->defination_name_en);
        $nodesTitle = CommonUtils::autoLang($this->workflow->nodesList[$this->workflow->getNextNodeId()]->node_name_cn,$this->workflow->nodesList[$this->workflow->getNextNodeId()]->node_name_en);
        if ($this->workflow->getOperateStatus() == WorkflowOperation::WORKFLOW_STATS_OPDENY){
            $subject = sprintf('%s 校园 %s 提出 %s申请被拒绝',$branchTitle,$childName,$workflowTitle);
        }else{
            if ($this->workflow->isEndNode()){
                $subject = sprintf('%s 校园 %s 提出 %s申请已完成',$branchTitle,$childName,$workflowTitle);
            }else{
                $subject = sprintf('%s 校园 %s 提出 %s申请需要您处理',$branchTitle,$childName,$workflowTitle);
            }
        }
        // 微信推送
        $this->workflow->wechatPush();
        $link = $this->controller->createUrl('/workflow/index', array('branchId'=>$this->workflow->schoolId));
        $body = $this->controller->renderPartial('/mail/templater',array('branchTitle'=>$branchTitle,'childName'=>$childName,'workflowTitle'=>$workflowTitle,'nodes'=>$nodesTitle,'link'=>$link,'workflow'=>$this->workflow),true);
        return $this->workflow->sendEmail($subject,$body);
    }
    
    public function delete() {
        $request = Yii::app()->request;
        if ($this->workflow->getOperateStatus() == WorkflowOperation::WORKFLOW_STATS_STEP){
            $transaction = Yii::app()->db->beginTransaction();
            try{
                if (!$this->workflow->deleteOperationNode($transaction)){
                    $this->controller->addMessage('state', 'fail');
                    $this->controller->addMessage('message', "工作流底层删除失败!");
                    $this->controller->showMessage();
                }
                $transaction->commit();
                $this->controller->addMessage('state', 'success');
                $this->controller->addMessage('message', '工作流删除成功');
                $this->controller->addMessage('data',$this->workflow->getOperateId());
                $this->controller->addMessage('callback', 'deleteWorkflowOperator');
                $this->controller->showMessage();
            } catch (Exception $ex) {
                $transaction->rollBack();
                $this->controller->addMessage('state', 'fail');
                $this->controller->addMessage('message', $ex->getMessage());
                $this->controller->showMessage();
            }
        }
    }

    //实现接口方法
    public function setTemplate($name){
        $this->template = $name;
    }
    
    //实现接口方法
    public function getTemplate(){
        return $this->template;
    }
}
