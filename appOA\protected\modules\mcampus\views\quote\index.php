<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('quote','Campus Operations'), array('//mcampus/default/index'))?></li>
        <li><?php echo Yii::t('quote','Quotes')?></li>
    </ol>
    <div class="row">
        <div class="col-md-2">
            <?php
            $mainMenu = array(
                array('label'=>Yii::t('quote','Weekly Quote'), 'url'=>array("//mcampus/quote/index")),
                array('label'=>Yii::t('quote','Quotes Tank'), 'url'=>array("//mcampus/quote/lib")),
            );
            $this->widget('zii.widgets.CMenu',array(
                'id' => 'user-profile-item',
                'items'=> $mainMenu,
                'htmlOptions'=>array('class'=>'nav nav-pills nav-stacked text-right background-gray'),
                'activeCssClass'=>'active',
                'itemCssClass'=>''
            ));
            ?>
        </div>
        <div class="col-md-10" id="app" v-if="pageData">
            <div class="row">
                <div class="col-md-12 mb5 top-bar">
                    <div class="title-01">{{ pageData.year }}</div>
                    <div>
                        <span class="glyphicon glyphicon-chevron-left" @click="flip(-1)"></span>
                        <span class="glyphicon glyphicon-chevron-right" @click="flip(1)"></span>
                    </div>
                </div>
            </div>
            <div class="row middle-bar mb16">
                <div class="col-md-3 title-02 text-center" v-for="(month, index) in pageData.month" :key="index">
                    {{ month.title }}
                </div>
            </div>
            <div class="row">
                <div class="col-md-3" v-for="(month, index) in pageData.month" :key="index">
                    <div class="week-item" :class="{ 'week-item-non': !week.quote, 'week-item-current': pageData.current_week == week.number }" v-for="(week, _index) in month.weeks" :key="_index">
                        <div class="week-item-title">
                            <div>{{ week.title }}</div>
                            <div><span class="glyphicon glyphicon-cog" @click="setting(index, _index)"></span></div>
                        </div>
                        <div class="week-item-date">{{ week.date }}</div>
                        <div class="week-item-content" v-if="week.quote">
                            <p class="title">{{ week.quote.content }}</p>
                            <p class="author">- {{ week.quote.author }}</p>
                            <p class="like" @click="likeList(week.number, week.quote.id)"><span class="glyphicon glyphicon-thumbs-up"></span> {{ week.quote.like }}</p>
                        </div>
                        <div class="text-center" v-else @click="setting(index, _index)" style="cursor: pointer;"><?php echo Yii::t('quote','Not set')?></div>
                    </div>
                </div>
            </div>

            <div class="modal fade" id="quote_setting" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" v-if="quote">
                <div class="modal-dialog modal-lg" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                            <h4 class="modal-title" id="myModalLabel"><?php echo Yii::t('quote','Quote Settings')?></h4>
                        </div>
                        <div class="modal-body">
                            <div class="form-group top-bar">
                                <div class="title-02">{{ quote.week }}</div>
                                <div><button type="button" class="btn btn-default btn-sm" @click="clear()"><?php echo Yii::t('quote', 'Reset')?></button></div>
                            </div>
                            <div class="form-group">
                                <label class="radio-inline">
                                    <input type="radio" value="1" v-model="quote.type"> <?php echo Yii::t('quote', 'Creating a new quote')?>
                                </label>
                                <label class="radio-inline">
                                    <input type="radio" value="2" v-model="quote.type"> <?php echo Yii::t('quote', 'Choose one from quotes tank')?>
                                </label>
                            </div>
                            <div v-if="quote.type == '1'">
                                <div class="form-group">
                                    <label for="content"><?php echo Yii::t('quote', 'Quote Content')?></label>
                                    <textarea class="form-control" rows="3" id="content" v-model="quote.content" placeholder="<?php echo Yii::t('quote', 'Please input quote content')?>"></textarea>
                                </div>
                                <div class="form-group">
                                    <label for="author"><?php echo Yii::t('quote', 'Quote Author')?></label>
                                    <input type="text" class="form-control" id="author" v-model="quote.author" placeholder="<?php echo Yii::t('quote', 'Please input quote author')?>">
                                </div>
                            </div>
                            <div v-if="quote.type == '2'">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="input-group">
                                            <span class="input-group-addon">
                                                <i class="glyphicon glyphicon-search"></i>
                                            </span>
                                            <input type="text" class="form-control" placeholder="Search for..." v-model="keyword" @keydown.enter="search()">
                                            <span class="input-group-btn">
                                                <button class="btn btn-primary" type="button" @click="searchWarp()"><?php echo Yii::t('withdrawal', 'Search')?></button>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="box-01 mt16" id="the_content">
                                            <div class="item-01 mb16" v-for="(_, index) in recentQuote" :key="index" @click="selected(_.id)">
                                                <div class="item-01-left">
                                                    <input type="radio" name="lib_id" :value="_.id" v-model="quote.value">
                                                </div>
                                                <div class="item-01-right">
                                                    <div class="text-01 mb5">{{ _.content }}</div>
                                                    <div class="item-01-sub text-02">
                                                        <div>— {{ _.author }}</div>
                                                        <div v-if="_.latest_use.length > 0" class="text-right">
                                                            <?php echo Yii::t('quote', 'Records: ')?><span class="text-03 text-right" v-html="formatUse(_.latest_use)"></span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="box-02 text-right">
                                            <nav aria-label="Page navigation">
                                                <ul class="pagination pagination-sm">
                                                    <li :class="{'disabled': page == 1}">
                                                        <a href="javascript:;" @click="toPage(page-1)" aria-label="Previous">
                                                            <span aria-hidden="true">&laquo;</span>
                                                        </a>
                                                    </li>
                                                    <li v-for="n in Math.ceil(pagination.total/20)" :key="n" :class="{'active': n == pagination.page}">
                                                        <a href="javascript:;" @click="toPage(n)">{{ n }}</a>
                                                    </li>
                                                    <li :class="{'disabled': page == Math.ceil(pagination.total/20)}">
                                                        <a href="javascript:;" @click="toPage(page+1)" aria-label="Next">
                                                            <span aria-hidden="true">&raquo;</span>
                                                        </a>
                                                    </li>
                                                </ul>
                                            </nav>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('global', 'Cancel')?></button>
                            <button type="button" class="btn btn-primary" @click="submit()" v-if="!submitDisabled"><?php echo Yii::t('global', 'Save')?></button>
                            <button type="button" class="btn btn-primary" v-else disabled="disabled"><?php echo Yii::t('global', 'Save')?>...</button>
                        </div>
                    </div>
                </div>
            </div>
            <?php echo $this->renderPartial('like');?>
        </div>
    </div>
</div>

<script>
    new Vue({
        el: "#app",
        data: {
            year: 0,
            index: 0,
            pageData: null,
            quote: null,
            recentQuote: [],
            pagination: null,
            page: 1,
            keyword: '',
            submitDisabled: false,
            likeObject: null
        },
        created: function() {
            this.init()
        },
        watch: {
            'quote.type': {
                handler(newVal) {
                    if (newVal == 2) {
                        this.$nextTick(() => {
                            this.autoHeight()
                        })
                    }
                },
                // immediate: true
            }
        },
        methods: {
            init() {
                $.getJSON('<?php echo $this->createUrl('quote/getQuoteList') ?>', {
                    year: this.year,
                    index: this.index
                }, (data) => {
                    this.pageData = data.data
                    this.year = this.pageData.year
                    this.index = this.pageData.index
                })
            },
            flip(i) {
                let year = this.pageData.year
                let index = this.pageData.index
                index += i
                if (index > 3) {
                    year = parseInt(year) + 1
                    index = 1
                }
                if (index < 1) {
                    year -= 1
                    index = 3
                }
                this.year = year
                this.index = index
                this.init()
            },
            setting(index, index2) {
                const month = this.pageData.month[index];
                const week = this.pageData.month[index].weeks[index2];

                $.getJSON('<?php echo $this->createUrl('quote/getQuoteDetail') ?>', {
                    year: this.year,
                    weekNumber: week.number
                }, (data) => {
                    this.recentQuote = data.data.limitQuote
                    this.pagination = {
                        total: data.data.totalQuote,
                        page: data.data.page
                    }
                    this.quote = {
                        week: this.pageData.year + ' ' + month.title + ' Week ' + parseInt(index2+1),
                        number: week.number,
                        value: week.quote ? week.quote.id : 0,
                        type: data.data.type,
                        content: data.data.quoteData ? data.data.quoteData.content : '',
                        author: data.data.quoteData ? data.data.quoteData.author : ''
                    }
                    this.$nextTick(() => {
                        $('#quote_setting').modal({backdrop: 'static'})
                    })
                })
            },

            autoHeight() {
                $('#the_content').css('max-height', $(window).height()-450)
            },
            submit() {
                this.submitDisabled = true
                $.post('<?php echo $this->createUrl('quote/setWeeklyQuote') ?>', {
                    year: this.year,
                    weekNumber: this.quote.number,
                    type: this.quote.type,
                    id: this.quote.value,
                    content: this.quote.content,
                    author: this.quote.author
                }, (data) => {
                    this.submitDisabled = false
                    if (data.state === 'success') {
                        $('#quote_setting').modal('hide')
                        this.init()
                    }
                }, 'json')
            },
            selected(n) {
                this.quote.value = n
            },
            toPage(n) {
                if (n != this.page && n > 0 && n <= Math.ceil(this.pagination.total/20)) {
                    this.page = n
                    this.search()
                }
            },
            search() {
                $.getJSON('<?php echo $this->createUrl('quote/searchQuote') ?>', {
                    year: this.year,
                    weekNumber: this.quote.number,
                    keyword: this.keyword,
                    page: this.page
                }, (data) => {
                    this.recentQuote = data.data.list
                    this.pagination = {
                        total: data.data.total,
                        page: data.data.page
                    }
                })
            },
            searchWarp() {
                this.page = 1
                this.search()
            },
            clear() {
                this.quote.type = 0
                this.quote.content = ''
                this.quote.author = ''
            },
            formatUse(_) {
                return _.join('<br>')
            },
            likeList(number, id) {
                console.log(this.year, number, id)
                $.getJSON('<?php echo $this->createUrl('quote/likeUserList') ?>', {
                    id: id
                }, (data) => {
                    this.likeObject = data.data

                    this.$nextTick(() => {
                        $('#quote_like').modal()
                    })
                })
            }
        }
    })
</script>

<style>
    .top-bar {
        display: flex;
        justify-content: space-between;
        line-height: 22px;
    }
    .top-bar div span {
        cursor: pointer;
    }
    .title-01 {
        font-weight: 500;
        font-size: 16px;
        color: #333333;
        line-height: 22px;
        text-align: left;
        font-style: normal;
    }
    .title-02 {
        font-weight: 500;
        font-size: 14px;
        color: #333333;
        font-style: normal;
    }
    .middle-bar {
        line-height: 44px;
        height: 44px;
        background: #F7F7F8;
        border-radius: 4px;
        margin-left: 0;
        margin-right: 0;
    }
    .week-item {
        background: #F2F9F2;
        border-radius: 4px;
        padding: 20px 20px 16px 20px;
        margin-bottom: 16px;
    }
    .week-item-non {
        background-color: #FAFAFA !important;
    }
    .week-item-current {
        background-color: #F0F5FB !important;
        border:1px solid #428bca
    }
    .week-item-title {
        line-height: 20px;
        height: 20px;
        font-weight: 500;
        font-size: 14px;
        color: #333333;
        text-align: left;
        font-style: normal;
        margin-bottom: 4px;
        display: flex;
        justify-content: space-between;
    }
    .week-item-title span {
        color: #333333;
        cursor: pointer;
    }
    .week-item-date {
        line-height: 18px;
        height: 18px;
        font-weight: 400;
        font-size: 12px;
        color: #666666;
        text-align: left;
        font-style: normal;
        margin-bottom: 12px;
    }
    .week-item-content .title {
        font-weight: 400;
        font-size: 14px;
        color: #333333;
        line-height: 20px;
        text-align: left;
        font-style: normal;
        margin-bottom: 6px;
    }
    .week-item-content .author {
        font-weight: 400;
        font-size: 12px;
        color: #999999;
        line-height: 16px;
        height: 16px;
        text-align: left;
        font-style: normal;
    }
    .week-item-content .like {
        font-weight: 400;
        font-size: 14px;
        color: #4D88D2;
        line-height: 20px;
        height: 20px;
        text-align: left;
        font-style: normal;
        cursor: pointer;
    }
    .item-01 {
        display: flex;
        border-radius: 4px;
        border: 1px solid #E5E6EB;
        padding: 16px;
        cursor: pointer;
        background-color: #fff;
    }
    .item-01-sub {
        display: flex;
        justify-content: space-between;
    }
    .item-01-left {
        width: 46px;
    }
    .item-01-right {
        width: 100%;
    }
    .text-01 {
        font-weight: 400;
        font-size: 14px;
        color: #333333;
        line-height: 20px;
        text-align: left;
        font-style: normal;
    }
    .text-02 {
        font-weight: 400;
        font-size: 12px;
        color: #999999;
        line-height: 16px;
        text-align: left;
        font-style: normal;
    }
    .text-03 {
        font-weight: 400;
        font-size: 12px;
        color: #666666;
        line-height: 16px;
        font-style: normal;
    }
    .list-02 {
        display: flex;
        align-items: center;
        overflow: hidden;
    }
    .list-02 img{
        width: 32px;
        height: 32px;
        object-fit: cover;
    }
    .list-02 .linkName{
        overflow: hidden;
        flex: 1;
    }
    .list-02 .linkName div{
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
    }
    .box-01 {
        background: #FAFAFA;
        border-radius: 4px;
        padding: 20px 20px 0 20px;
        overflow-y: auto;
    }
    .box-02 {
        background: #FAFAFA;
        padding: 0 20px 0 20px;
    }

</style>
