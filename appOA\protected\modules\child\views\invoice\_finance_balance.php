<div class="table_full">
	<table width="100%">
		<colgroup>
			<col width="200">
			<col width="200">
		</colgroup>
		<tbody>
			<tr>
				<th><?php echo Yii::t("payment", "Personal Account");?></th>
				<td><?php
					echo is_null($this->childObj->credit)? OA::formatMoney(0) : OA::formatMoney($this->childObj->credit);
					?></td>
				<td>
					<?php
					//if(Yii::app()->user->checkAccess('oSeniorFinanceOp', array('schoolid'=>$this->childObj->schoolid)) && $this->childObj->credit > 0.01){
					if( $this->childObj->credit > 0 ){
                        if(isset(Yii::app()->params['siteFlag']) &&  Yii::app()->params['siteFlag'] === 'daystar'):
                            Yii::import('common.models.workflow.WorkflowDefination');
                            $branch = Branch::model()->findByPk($this->childObj->schoolid);
                            $defination = WorkflowDefination::model()->find('branch_type=:branch_type and defination_handle=:defination_handle',array(':branch_type'=>$branch->type,':defination_handle'=>'refundCash'));
                            echo CHtml::link(Yii::t("payment","提现申请"), array('/workflow/entrance/index','definationId'=>$defination->defination_id,'branchId'=>$this->childObj->schoolid,'action'=>'initi','oldWindow'=>true), array('class'=>'btn mr15 J_dialog', 'title'=>Yii::t("payment","提现工作流")));
                        else:
                            echo CHtml::link(Yii::t("payment","余额提现"), array('/child/index/processWithDraw'), array('class'=>'btn mr15 J_dialog', 'title'=>Yii::t("payment","余额提现")));
                        endif;
                        
						echo CHtml::link(Yii::t("payment","家长放弃余额"), array('/child/index/processCleanBalance'), array('class'=>'btn mr15 J_dialog', 'title'=>Yii::t("payment","家长放弃余额")));
                        if (isset($siblings['isRelation']) && $siblings['isRelation'] === true){
                            echo CHtml::link(Yii::t("payment","兄弟姐妹余额转移"), array('/child/index/processCreditTransfer'), array('class'=>'btn J_dialog', 'title'=>Yii::t("payment","兄弟姐妹余额转移")));
                        }
					}
					?>
				
				</td>
			</tr>
			<?php
				if(!empty($depositList)):
				foreach($depositList as $_sy=>$_amount):
				if($_amount['amount'] > 0.01):
				?>
				
				<tr>
					<th><?php echo Yii::t('payment','Deposit Balance');?><br/><?php echo strval($_sy) . ' ~ ' . strval( $_sy + 1 );?></th>
					<td><?php echo OA::formatMoney($_amount['amount']);?></td>
					<td>
					<?php 
						if ($_amount['frozen']>0.01):
							echo Yii::t("payment","Part of :amount has been frozen.",array(":amount"=>$_amount['frozen']));
                        else:
                            if ( $this->childObj->status >= ChildProfileBasic::STATS_GRADUATED )
                                echo CHtml::link(Yii::t("payment","转入收入（余额清零）"), array('/child/index/cleanDeposit', 'startyear'=>$_sy), array('class'=>'btn mr15 J_dialog', 'title'=>Yii::t("payment","转入收入（余额清零）")));
//                            if (($cMod->startyear-1 == $_sy) && ( $this->childObj->status < ChildProfileBasic::STATS_GRADUATED ))
                            if ( $this->childObj->status < ChildProfileBasic::STATS_GRADUATED )
                                echo CHtml::link(Yii::t("payment","余额转移"), CHtml::normalizeUrl(Yii::app()->params['OABaseUrl'].'/modules/invoice/invoice_transfer_deposit.php?childid='.$this->childId), array('class'=>'mr15', 'target'=>'_blank', 'title'=>Yii::t("payment","余额转移")));
						endif;
					?>
					</td>
				</tr>
				
			<?php
				endif;
				endforeach;
				endif;
			?>
		</tbody>
	</table>
</div>

