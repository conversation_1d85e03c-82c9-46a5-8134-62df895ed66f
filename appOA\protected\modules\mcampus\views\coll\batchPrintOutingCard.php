<style>
    .container-fluid{
        width:860px;
        margin:0 auto
    }
    .page{
        margin-top:20px
    }
    img{
        margin-top:10px;
        max-width:100%;
        height:auto
    }
    .sign{
        float: right;
        margin-right:20px;
        margin-bottom:20px;
        max-height: 15mm;
    }
    .sign img{
        width:200px;
        height: 110px;
    }
    @media print {
        .print_img{
            max-height: 290mm;
            display: block;
            margin: 0 auto;
        }
        .alwaysAttr{
            page-break-after:always;
        }

        .print_protocol_img
        {
            max-height: 290mm;
            display: block;
            margin: 0 auto;
        }
    }
</style>
<?php $studentNum = count($child_infos);$i=0;?>
<?php foreach ($child_infos as $child_id=>$child_info){?>
    <?php $i++;?>
    <div class="container-fluid">
        <div class ="row">
            <table class="table">
                <tbody>
                <tr >
                    <th width="25%"><?php echo Yii::t('reg', 'Name') ?></th>
                    <td colspan="3"><?php echo $child_info->getChildName() ?></td>
                </tr>
                <tr >
                    <th width="25%"><?php echo Yii::t('labels', 'Date of Birth') ?></th>
                    <td colspan="3"><?php echo $child_info->birthday_search ?></td>
                </tr>
                <tr >
                    <th width="25%"><?php echo Yii::t('labels', 'Class') ?></th>
                    <td colspan="3"><?php echo $child_info->ivyclass->title?></td>
                </tr>
                <tr >
                    <th width="25%"><?php echo Yii::t('coll', 'What identification documents are being used by the student for this trip') ?></th>
                    <td>
                        <?php if($step_data[$child_id]['identity_type']=='ID_card'){
                            echo Yii::t('coll', 'Chinese ID card');
                        }elseif ($step_data[$child_id]['identity_type']=='passport'){
                            echo Yii::t('coll', 'Passport');
                        }else{
                            echo Yii::t('coll', 'Other documents (Hong Kong, Macau, Taiwan)');
                        }?>
                    </td>
                </tr>
                <?php if($step_data[$child_id]['identity_type'] == 'ID_card'){?>
                    <tr>
                        <th><?php echo Yii::t('coll', 'ID NO.')?></th>
                        <td><?php echo $step_data[$child_id]['identity_number']?></td>
                    </tr>
                    <tr>
                        <th><?php echo Yii::t('coll', 'ID card expiry date')?></th>
                        <td colspan="3"><?php echo $step_data[$child_id]['identity_validity']?></td>
                    </tr>
                    <tr>
                        <th><?php echo Yii::t('coll','All pages of the documents')?></th>
                        <td><?php echo Yii::t('coll', 'See annex')?></td>
                    </tr>
                <?php }elseif ($step_data[$child_id]['identity_type'] == 'passport'){?>
                    <tr>
                        <th><?php echo Yii::t('coll', 'Passport NO.')?></th>
                        <td><?php echo $step_data[$child_id]['identity_number']?></td>
                    </tr>
                    <tr>
                        <th><?php echo Yii::t('coll', 'Passport Expired Date')?></th>
                        <td colspan="3"><?php echo $step_data[$child_id]['identity_validity']?></td>
                    </tr>
                    <tr >
                        <th width="25%"><?php echo Yii::t('coll','Chinese visa number on your passport/or foreign permanent resident ID card number')?> </th>
                        <td colspan="3"><?php echo empty($step_data[$child_id]['visa_number']) ? '' : $step_data[$child_id]['visa_number'] ?></td>
                    </tr>
                    <tr >
                        <th width="25%"><?php echo Yii::t('coll','Students passport\'s personal information page and valid visa page/ or foreign permanent resident ID card page')?></th>
                        <td colspan="3"><?php echo Yii::t('coll', 'See annex')?></td>
                    </tr>
                <?php }else{?>
                    <tr>
                        <th><?php echo  Yii::t('coll', 'ID number')?></th>
                        <td><?php echo $step_data[$child_id]['identity_number']?></td>
                    </tr>
                    <tr >
                        <th width="25%"><?php echo Yii::t("coll",'ID card expiry date')?></th>
                        <td colspan="3"><?php echo $step_data[$child_id]['identity_validity']?></td>
                    </tr>
                    <tr >
                        <th width="25%"><?php echo Yii::t('coll','All pages of the documents')?></th>
                        <td colspan="3"><?php echo Yii::t('coll', 'See annex')?></td>
                    </tr>
                <?php }?>
                <tr >
                    <th width="25%"><?php echo Yii::t('coll', 'Full name as on ID card')?></th>
                    <td colspan="3"><?php echo $step_data[$child_id]['identity_name']?></td>
                </tr>
                <tr >
                    <th width="25%"><?php echo Yii::t("coll",'Emergency contact person for this trip')?></th>
                    <td colspan="3"><?php echo $step_data[$child_id]['emergency_contact']?></td>
                </tr>
                <tr >
                    <th width="25%"><?php echo Yii::t("coll",'Relationship to student')?></th>
                    <td  colspan="3"><?php echo $step_data[$child_id]['emergency_relation']?></td>
                </tr>
                <tr >
                    <th width="25%"><?php echo Yii::t("coll",'Emergency contact phone number')?></th>
                    <td colspan="3"><?php echo $step_data[$child_id]['emergency_tel']?></td>
                </tr>
                </tbody>
            </table>
            <div style="page-break-after:always;"></div>
        </div>
        <?php if($step_data[$child_id]['identity_photo']){?>
            <div>
                <?php foreach ($step_data[$child_id]['identity_photo'] as $k=>$val){?>
                    <div>
                        附件页
                        <?php echo $child_info->getChildName() ?>
                        <?php echo $child_info->ivyclass->title?>
                    </div>
                    <p class="pageBreak">
                        <?php if(count($step_data[$child_id]['identity_photo']) == $k+1){?>
                            <img  class="img-responsive print_img" src="<?php echo $val; ?>">
                        <?php }else{?>
                            <img  class="img-responsive print_img alwaysAttr" src="<?php echo $val; ?>">
                        <?php }?>
                    </p>
                <?php }?>
            </div>
        <?php }?>
    </div>
    <?php if($studentNum != $i){?>
    <div style="page-break-after:always;"></div>
    <?php }?>
<?php }?>
