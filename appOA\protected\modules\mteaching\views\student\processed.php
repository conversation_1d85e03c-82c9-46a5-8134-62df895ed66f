<style>
    .listCom{
        border-radius: 8px;
        border: 1px solid #E8EAED;
        padding:24px 0 24px 24px
    }
    .labelbg{
        padding:2px;
        background: #F7F7F8;
        border-radius: 2px;
        border: 1px solid #D9D9D9;
    }
    .supportPlan{
        background: #FFFFFF;
        border: 1px solid #E8EAED;
        border-radius:4px;
    }
    .handle{
        background: #F0AD4E;
        border-radius: 100px 0 0 100px;
        font-size: 12px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #FFFFFF;
        padding:2px 8px
    }
    .handlingResults{
        background: rgba(77,136,210,0.06);
        border-radius: 4px;
        border: 1px solid #E8EAED;
    }
    .handlingTitle{
        background: linear-gradient(90deg, #84BDEB 0%, #4D88D2 100%);
        border-radius: 4px 4px 0px 0px;
        border: 1px solid #E8EAED;
        padding:16px 0;
        color: #FFFFFF;
        text-align:center
    }
    .labelTag{
        width: 18px;
        height: 18px;
        background: #666666;
        border-radius: 9px;
        border: 1px solid #FFFFFF;
        display:inline-block;
        text-align: center;
        line-height: 17px;
        color: #fff;
        font-size: 12px;
    }
    .labelTagRed{
        background:#D9534F
    }
    .font24{
        font-size:24px
    }
    
</style>
<div class="" id='container' v-cloak >
    <div class='flex'>
        <div class='flex1'>
            <div v-if='start_year!=""'>
                <select class="form-control length_3 pull-left" v-model='start_year'>
                    <option v-for='(list,index) in yearData.school_years_list'  :value='list.value'>{{list.title}}</option>
                </select>
                <select class="form-control length_3 pull-left ml20" v-model='classId'  @change='getData()'>
                    <option  value='0'>全部班级</option>
                    <option v-for='(list,index) in yearData.class_list[start_year]' :value='list.value'>{{list.title}}</option>
                </select>
            </div>
        </div>
        <div class="input-group" style="width: 200px;float: right;">
            <span class="input-group-addon" id="basic-addon1"><i class="glyphicon glyphicon-search"></i></span>
            <input type="text" class="form-control" placeholder="请输入姓名" v-model='search' aria-describedby="basic-addon1" name="search">
            <span class="glyphicon glyphicon-remove delSearchText cur-p" v-if='search!=""' @click='search=""'></span>
        </div>
    </div>
    <div class='flex mt24'>
        <div class='flex1 bgGrey  presenter mr24'>
            <div class='color3 font14 pb16'>全部提请单</div>
            <div><span class='font24 color3'>{{numberData.all_processed}}</span><span class='color6 font14 ml4'>个</span></div>
        </div>
        <div class='flex1 bgGrey mr24'>
            <div class='color3 font14 pb16'>只做记录，无需后续处理</div>
            <div><span class='font24 color3'>{{numberData.record}}</span><span class='color6 font14 ml4'>个</span></div>
        </div>
        <div class='flex1 bgGrey mr24'>
            <div class='color3 font14 pb16'>希望校长办公室后续处理</div>
            <div><span class='font24 color3'>{{numberData.office}}</span><span class='color6 font14 ml4'>个</span></div>
        </div>
        <div class='flex1 bgGrey mr24 pb16'>
            <div class='color3 font14 pb16'>认定结果：行为问题</div>
            <div><span class='font24 color3'>{{numberData.behavior}}</span><span class='color6 font14 ml4'>个</span></div>
        </div>
        <div class='flex1 bgGrey'>
            <div class='color3 font14 pb16'>认定结果：非行为问题</div>
            <div><span class='font24 color3'>{{numberData.not_behavior}}</span><span class='color6 font14 ml4'>个</span></div>
        </div>
    </div>
    <div class='mt24 mb24 font14'>
        <span>筛选：</span>
        <label class="radio-inline">
            <input type="radio"  value="1" v-model='tag'> 无支持计划的学生
        </label>
        <label class="radio-inline">
            <input type="radio"  value="2" v-model='tag'> 属于学习支持计划的学生
        </label>
        <label class="radio-inline">
            <input type="radio"  value="3" v-model='tag'> 属于行为支持计划的学生
        </label>
    </div>
    <el-table
        :data="dataList.list"
        style="width: 100%"
        :header-cell-style="{background:'#fafafa',color:'#333'}"
        :default-sort = "{prop: 'created_at', order: 'descending'}"
        @sort-change="sort_change" 
        >
        <el-table-column
        prop="child"
        label="<?php echo Yii::t("ptc", "Student"); ?>"
        min-width="250">
            <template slot-scope="scope">
                <div v-if='scope.row.child.length==1'>
                    <div class="media" v-for='(list,index) in scope.row.child'>
                        <div class="media-left pull-left media-middle relative">
                            <img :src="dataList.child_list[list.id].avatar" data-holder-rendered="true" class="avatar">
                        </div> 
                        <div class="pull-left media-middle">
                            <div class="font14 color3"> {{dataList.child_list[list.id].name}}
                                <span  :class='list.tag==2?"tagLabelYellow":list.tag==3?"tagLabel":""'>{{showTitle(list.tag,'ISP_type')}}</span>
                            </div> 
                            <div class="color6 font12">{{dataList.child_list[list.id].className}}</div>
                        </div>
                    </div>
                </div>
                <div v-else>
                    <img :src="dataList.child_list[list.id].avatar" v-for='(list,index) in scope.row.child' data-holder-rendered="true" class="avatar32 mb8 mr4">
                    <el-popover
                        placement="bottom"
                        trigger="click">
                        <div class="media" v-for='(list,index) in scope.row.child'>
                            <div class="media-left pull-left media-middle relative">
                                <img :src="dataList.child_list[list.id].avatar" data-holder-rendered="true" class="avatar">
                            </div> 
                            <div class="pull-left media-middle">
                                <div class="font12 color6"> {{dataList.child_list[list.id].name}}
                                    <span  :class='list.tag==2?"tagLabelYellow":list.tag==3?"tagLabel":""'>{{showTitle(list.tag,'ISP_type')}}</span>
                                </div> 
                                <div class="color6 font12">{{dataList.child_list[list.id].className}}</div>
                            </div>
                        </div>
                        <span class='bluebg font14 cur-p' slot="reference">共{{scope.row.child.length}}人</span>
                    </el-popover>
                </div>
            </template>
        </el-table-column>
        <el-table-column
        prop="created_by"
        min-width="150"
        label="提请人">
        <template slot-scope="scope">
        <span class='color3'>{{dataList.staff_info[scope.row.created_by].name}}</span>

        </template>
        </el-table-column>
        <el-table-column
        prop="created_at"
        sortable='custom'
        min-width="150"
        label="提交时间">
            <template slot-scope="scope">
                <el-tooltip class="item" effect="dark" :content="scope.row.created_date" placement="top">
                    <span class='color3 font14'>{{scope.row.diff_date}}</span>
                </el-tooltip>
            </template>
        </el-table-column>
        <el-table-column
        prop="office_user"
        label="处理状态"
        min-width="300">
            <template slot-scope="scope">
                <div class='flex' v-if='scope.row.just_record==0'>
                    <div class='text-center minWidth mr50' v-for='(list,index) in scope.row.nodes_order'>
                        <div v-if='scope.row.nodes_by_node[list].status==1'>
                            <div class='relative'>
                                <div class='firstBorder' v-if='scope.row.nodes_order.length>1 && index+1<scope.row.nodes_order.length'></div>
                                <img :src="dataList.staff_info[scope.row.nodes_by_node[list].updated_by].photoUrl" alt="" class='avatar32'>
                            </div>
                            <div class='color3 font14'>{{scope.row.nodes_order_title[index]}}</div>
                            <div class='color6 font12'>
                            <el-tooltip class="item" effect="dark"  placement="top">
                            <div slot="content">由{{dataList.staff_info[scope.row.nodes_by_node[list].updated_by].name}}处理<br/>{{scope.row.nodes_by_node[list].updated_at}}</div>
                            <div class='color6'>{{scope.row.nodes_by_node[list].diff_date}}</div>
                            </el-tooltip>
                            </div>
                        </div>
                        <div v-else>
                            <div>
                                <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/pending.png' ?>" alt="" class='avatar32'>
                                <!-- <span class='waiting'>?</span> -->
                            </div>
                            <div class='color3 font14'>{{scope.row.nodes_order_title[index]}}</div>
                            <div class='font12 waitingColor'>等待处理</div>
                        </div>
                    </div>
                </div>
                <div v-if='scope.row.just_record==1'>
                    只是记录，无需后续处理
                </div>
            </template>     
        </el-table-column>
        <el-table-column
        prop="office_user"
        label="<?php echo Yii::t("newDS", "Actions"); ?>"
        width="200">
            <template slot-scope="scope">
                <el-button type="text"@click='showDetails(scope.row._id)'class='font14 bluebg'><?php echo Yii::t("global", "View Detail"); ?></el-button>
                <!-- <button class="btn btn-link" type="button" @click='showDetails(scope.row._id)'><?php echo Yii::t("global", "View Detail"); ?></button> -->
            </template>
        </el-table-column>
    </el-table>
    <nav aria-label="Page navigation" v-if='itemList.count_page>1'  class="text-left ml10">
        <ul class="pagination">
            <li v-if='itemList.current_page >1'>
                <a href="javascript:void(0)" @click="prev(itemList.current_page)" aria-label="Previous">
                    <span aria-hidden="true">&laquo;</span>
                </a>
            </li>
            <li v-else class="disabled">
                <a aria-label="Previous">
                    <span aria-hidden="true">&laquo;</span>
                </a>
            </li>
            <li v-for='(data,index) in itemList.count_page' :class="{ active:index+1==itemList.current_page }">
                <a href="javascript:void(0)" @click="plus(index)">{{index+1}}</a>
            </li>
            <li v-if='itemList.current_page <itemList.count_page'>
                <a href="javascript:void(0)" @click="plus(itemList.current_page)" aria-label="Next">
                    <span aria-hidden="true">&raquo;</span>
                </a>
            </li>
            <li v-else class="disabled">
                <a aria-label="Next">
                    <span aria-hidden="true">&raquo;</span>
                </a>
            </li>
        </ul>
    </nav>
     <!-- 详情 -->
     <div class="modal fade" id="contentModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("newDS", "Detail"); ?></h4>
                </div>
                <div class="modal-body p0 relative" v-if='contentList.behavior'>
                    <div><?php $this->renderPartial("detail", array('type' => 'completed'));?></div>
                    <div class='clearfix'></div>
                </div>
            </div>
        </div>
    </div>
     <!-- 最终处理 -->
     <div class="modal fade"  id="finalResultModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel"  aria-labelledby="wantSayModalLabel"  data-backdrop="static">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title">{{editStatus==2?"认定其他学生":"重新认定"}}</h4>
            </div>
            <div class="modal-body p24">
                <div class='font14'>
                    <div v-if='editStatus==2'>
                        <div class='flex align-items'>
                            <span class='fontBold color3 flex1'>认定学生</span> 
                            <template>
                                <el-select
                                        v-model="stuVal"
                                        filterable
                                        remote
                                        clearable
                                        class='inline-input flex1 formControl'
                                        reserve-keyword
                                        placeholder="<?php echo Yii::t("directMessage", "学生姓名"); ?>"
                                        :remote-method="stuRemoteMethod"
                                        prefix-icon="el-icon-search"
                                        @change='addStu'
                                        :loading="loading">
                                    <el-option
                                            v-for="item in stuOption"
                                            :key="item.id"
                                            :label="item.name"
                                            class='optionSearch mb8'
                                            :value="item.id">
                                        <div class="media">
                                            <div class="media-left pull-left media-middle">
                                                <a href="javascript:void(0)">
                                                    <img :src="item.avatar" data-holder-rendered="true" class="media-object img-circle avatar">
                                                </a>
                                            </div>
                                            <div class="media-body mt5 media-middle">
                                                <h4 class="media-heading font14 color3 text_overflow">{{item.name}}</h4>
                                                <div class="text-muted text_overflow font12">{{ item.class_name }}</div>
                                            </div>
                                        </div>
                                    </el-option>
                                </el-select>
                            </template>
                        </div>
                        <div  class='mt8'>
                            <div class='row' v-if='contentList.behavior'>
                                <div class='col-md-6 mt10' v-for='(list,index) in contentList.behavior.child'>
                                    <div class='bgGrey p8 height65'>
                                        <label class="checkbox-inline flex">
                                            <input type="checkbox"   class='mt16' v-if='showDisabled(list.id)' disabled='true' > 
                                            <input type="checkbox"  :value="list.id" class='mt16' v-if='!showDisabled(list.id)' v-model='officeData.student_ids'> 
                                            <div class="media flex1 m0 authorizedMedia" >
                                                <div class="media-left pull-left media-middle">
                                                    <a href="javascript:void(0)">
                                                        <img :src="contentList.student_info_list[list.id].avatar" data-holder-rendered="true" class="avatar">
                                                    </a>
                                                </div>
                                                <div class="media-right pull-right mt15 text-right">
                                                    <span class='color6' v-if='showDisabled(list.id)'>已认定</span>
                                                </div>
                                                <div class="media-body media-middle">
                                                    <div class="lineHeight20  text-primary">
                                                        <span class='font14 color3 nowrap'>{{contentList.student_info_list[list.id].name}} </span>
                                                        <span :class='list.tag==2?"tagLabelYellow":list.tag==3?"tagLabel":""'>{{showTitle(list.tag,'ISP_type')}}</span>
                                                    </div>
                                                    <div class="font12 color6 nowrap">{{contentList.student_info_list[list.id].className}}</div>
                                                </div>
                                            </div>
                                        </label>
                                    </div>
                                </div>
                                <div class='col-md-6 mt10' v-for='(list,index) in addChildList' :key='list.id'>
                                    <div class='bgGrey p8 height65'>
                                        <label class="checkbox-inline flex">
                                            <input type="checkbox"  :value="list.id" class='mt16' v-model='officeData.student_ids'> 
                                            <div class="media flex1 m0 authorizedMedia" >
                                                <div class="media-left pull-left media-middle">
                                                    <a href="javascript:void(0)">
                                                        <img :src="list.avatar" data-holder-rendered="true" class="avatar">
                                                    </a>
                                                </div>
                                                <div class="media-right pull-right mt15 text-right">
                                                    <span class='el-icon-circle-close font16' @click='delChild(index,list.id)'></span>
                                                </div>
                                                <div class="media-body media-middle">
                                                    <div class="lineHeight20  text-primary">
                                                        <span class='font14 color3 nowrap'>{{list.name}} </span>
                                                        <span v-for='(item,ind) in list.label' >
                                                            <span v-if='item.flag==2 || item.flag==3' :class='item.flag==2?"tagLabelYellow":item.flag==3?"tagLabel":""'>{{item.name}}</span>
                                                        </span>
                                                    </div>
                                                    <div class="font12 color6 nowrap">{{list.className}}</div>
                                                </div>
                                                
                                            </div>
                                        </label>
                                    </div>
                                </div>
                                
                            </div>
                        </div>
                    </div>
                    <div  v-if='editStatus==1'>
                        <div class='row' v-if='reaffData.child'>
                            <div class='col-md-6 mt10'>
                                <div class="media flex1 m0 listMedia bgGrey height65" >
                                    <div class="media-left pull-left media-middle">
                                        <a href="javascript:void(0)">
                                            <img :src="contentList.student_info_list[reaffData.child.id].avatar" data-holder-rendered="true" class="avatar">
                                        </a>
                                    </div>
                                    <div class="media-body pt4 media-middle">
                                        <div class="lineHeight20  text-primary">
                                            <span class='font14 color3 nowrap'>{{contentList.student_info_list[reaffData.child.id].name}} </span>
                                        </div>
                                        <div class="font12 color6 nowrap">{{contentList.student_info_list[reaffData.child.id].className}}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class='fontBold color3 mt24'>认定结果</div>
                    <div  class='mt8'>
                        <div  v-if='reaffirmationType=="admin"'>
                        <label class="radio-inline" v-for='(list,index) in config.fact'>
                            <input type="radio" name="inlineRadioOptions" v-model='officeData.identified' :value="list.value"> {{list.title}}
                        </label>
                        </div>
                        <div v-else>
                            <div class='flex1 color3'><span class="label label-success" >不属于行为问题</span></div>
                        </div>
                    </div>
                    <div v-if='reaffirmationType=="admin" && officeData.identified==1'>                    
                        <div class='fontBold color3 mt24'>最终决定</div>
                        <div class='row'>
                            <div class='col-md-3 col-sm-12'  v-for='(list,index) in config.actions'>
                                <div class="checkbox m0 mt5" >
                                    <label>
                                        <input type="checkbox" v-model='officeData.actions'  :value="list.value" >{{list.title}}
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class='fontBold color3 mt24'>联系家长</div>
                    <div class='row'>
                        <div class='col-md-3 col-sm-12'  v-for='(list,index) in config.contact_parents'>
                            <div class="checkbox m0 mt5" >
                                <label>
                                    <input type="checkbox" v-model='officeData.contact_parents'  :value="list.value" >{{list.title}}
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class='otherBg mt10' v-if='showIndexOf("actions")'>
                        <input type="text" class="form-control" placeholder="其他"  v-model='officeData.actions_other' >
                    </div>
                    <div class='fontBold color3 mt24'>面谈时长</div>
                    <div class='mt8'>
                        <el-time-picker
                            v-model="officeData.duration"
                            value-format="HH:mm"
                            format="H 小时 m 分"
                            placement="bottom-start"
                            placeholder="面谈时长">
                        </el-time-picker>
                    </div>
                    <div class='fontBold color3 mt24'>处理意见</div>
                    <div  class='mt8'><textarea class="form-control" placeholder="<?php echo Yii::t("teaching", "Input"); ?>" v-model='officeData.remark' rows="3"></textarea></div>
                    <div class='flex mb10 align-items mt24'>
                        <div class='fontBold color3'><?php echo Yii::t("curriculum", "Attachments"); ?></div>
                        <div class='flex1'><span id='finalResultAddFile' class='btn btn-link'><i class="el-icon-circle-plus-outline bluebg  font14 mr5"></i>上传</span></div>
                    </div>
                    <div>
                        <div class='' v-if='attachments.img && attachments.img.length>0'>
                            <div class='imgData mr8'  v-for='(list,i) in attachments.img'>
                                <div v-if="list.types=='1'">
                                    <div class='uploadLoading'> <?php echo Yii::t("directMessage", "Uploading");?></div>
                                </div>
                                <div v-else>
                                    <img class='imgList' @click='uploadShowImg(list.file_key)'  :src="list.file_key" alt="">
                                    <span aria-hidden="true" class='closeImg' @click.stop='delImg("img",list,i)'>×</span>
                                </div>
                            </div>
                            <template v-if='loadingType==1'>
                                <div class='imgData mr8'  v-for='(list,i) in loadingList'>
                                    <div class='uploadLoading'> <?php echo Yii::t("directMessage", "Upload queue");?></div>
                                </div>
                            </template>
                        </div>
                        <div>
                            <div class='mt16' v-if='attachments.other && attachments.other.length>0'>
                                <div class='flex uploadFile' v-for='(list,index) in attachments.other'>
                                    <span class='glyphicon glyphicon-paperclip mr8'></span>
                                    <a target="_blank" class='flex1'  style='line-height:26px' :href='list.file_key' v-if='!list.isEdit'>{{list.title}}</a>
                                    <span class='flex1' v-else><input type="input" class='inputStyle'  @keyup.enter="saveFile(list,index)"  v-model='attachmentName' ></span>
                                    <span v-if="list.types=='1'"></span>
                                    <template v-else>
                                        <template v-if='!list.isEdit'>
                                            <span class='glyphicon glyphicon-edit icon mr16' v-if='list.file_key!=""' @click.stop='list.isEdit=true,attachmentName=list.title'></span>
                                            <span class='glyphicon glyphicon-trash icon' v-if='list.file_key!=""' @click.stop='delImg("link",list,index)'></span>
                                        </template>
                                        <span style='width:90px'  class='text-right inline-block' v-else>
                                            <button type="button" class="btn btn-primary btn-xs" @click='saveFile(list,index)'><?php echo Yii::t("global", "Save");?></button>
                                            <button type="button" class="btn btn-default btn-xs" @click='list.isEdit=false'><?php echo Yii::t("global", "Cancel");?></button>
                                        </span>
                                    </template>
                                </div>
                            </div>
                            <template v-if='loadingType==2'>
                                <div class='flex uploadFile'  v-for='(list,i) in loadingList'>
                                    <span class='glyphicon glyphicon-paperclip mr8'></span>
                                    <a  class='flex1'  style='line-height:26px' > <?php echo Yii::t("directMessage", "Upload queue");?></a>
                                </div>
                            </template>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Cancel"); ?></button>
                <button type="button" class="btn btn-primary" @click='saveOffice()' v-if='editStatus==2'><?php echo Yii::t("global", "OK"); ?></button>
                <button type="button" class="btn btn-primary" @click='saveReaff()' v-if='editStatus==1'><?php echo Yii::t("global", "OK"); ?></button>
            </div>
            </div><!-- /.modal-content -->
        </div><!-- /.modal-dialog -->
    </div><!-- /.modal --> 
    <div><?php $this->renderPartial("sendModal");?></div>  
</div>

<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>
<script>
    var height=document.documentElement.clientHeight;
    var config = <?php echo CJSON::encode($config)?>;
    console.log(<?php echo CJSON::encode($config)?>)
    var container = new Vue({
        el: "#container",
        data: {
            tag:'',
            tableData: [],
            search:'',
            height:height-220,
            config:config,
            teacherUid:{},
            options:[],
            loading:false,
            dataList:{},
            contentList:{},
            time:'',
            principal_processing:'',
            remark:'',
            duration:'00:00',
            help:false,
            officeData:{},
            planData:{},
            viewType:'',
            sendType:'',
            appendComment:'',
            appendText:false,
            refill:false,
            detail:{},
            order: "descending",
            orderProp:"created_at",
            pageNum:1,
            itemList:{},
            yearData:{},
            numberData:{},
            uploader:[],
            handId:'',
            loadingType:0,
            attachments:{
                img:[],
                other:[]
            },
            stuVal:'',
            stuOption:[],
            addChildList:[],
            editStatus:0,
            reaffData:{},
            followItem:{},
            followData:{
                remark:'',
                date:'',
                time:''
            },
            followType:'',
            sendItem:{},
            classTeacher:{},
            teacher_ids:[],
            sendRemark:'',
            checkall:false,
            sendLogList:[],
            affirmRecordList:{},
            affirmData:{},
            reaffirmationType:'',
            start_year:'',
            classId:''
        },
        watch:{
        },
        created: function() {
            this.getData()
        },
        computed: {},
        methods: {
            htmlBr(content){
                if(content!=null){
                    var reg=new RegExp("\n","g");
                    content= content.replace(reg,"<br/>");
                }
                return content
            },
            showImg(id,list){
                var viewer = new Viewer(document.getElementById(id),{
                    fullscreen: false,
                    title:false,
                    scalable:false,
                    show:function(){ 
                        if(list.length==1){
                            $('.viewer-prev').hide()
                            $('.viewer-next').hide()
                        }
                    },
                    hide:function(){ 
                        viewer.destroy()
                    }
                });
                $("#"+id).click();
            },
            sort_change(column){
                this.order=column.order
                this.orderProp=column.prop
                this.getData()
            },
            plus(index) {
                this.pageNum = Number(index) + 1
                this.getData()
            },
            prev(index) {
                this.pageNum = Number(index) - 1
                this.getData()
            },
            showTitle(data,type){
                for(var i=0;i<this.config[type].length;i++){
                    if(this.config[type][i].value==data){
                        return this.config[type][i].title
                    }
                }
            },
            getData(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("getProcessedList") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        "tags":[],
                        page:this.pageNum,
                        order: this.order,
                        orderProp:this.orderProp,
                        start_year:this.start_year,
                        class_id: this.classId,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.dataList=data.data.list
                            that.itemList=data.data.list.meta
                            that.yearData=data.data.search_data
                            that.numberData=data.data.stat
                            that.start_year=data.data.search_data.search.start_year
                            that.classId=data.data.search_data.search.class_id
                            
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            getQiniu(id){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("getQiniuToken") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        linkId:id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.qiniuToken=data.data
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            showDetails(id){
                let that=this
                this.sendLogList=[]
                this.affirmData={}
                this.stuOption=[]
                this.addChildList=[]
                this.handId=id
                that.getQiniu(id)
                this.appendText=false
                this.refill=false
                if(that.uploader.length!=0) {
                    for(var i=0;i<that.uploader.length;i++){
                    that.uploader[i].destroy();
                    }
                }
                this.officeData={
                    identified:'',
                    actions_other:'',
                    duration:"00:00",
                    remark:'',
                    actions:[],
                    attachments:{
                        img:[],
                        other:[]
                    },
                    student_ids:[],
                    contact_parents:[]
                },
                $.ajax({
                    url: '<?php echo $this->createUrl("getBehaviorDetail") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        id:id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.contentList=data.data
                            that.detail=data.data.behavior.detail

                            $('#contentModal').modal('show')
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                        that.loading = false;
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                        that.loading = false;
                    },
                })
            },
            showIndexOf(type){
                if(this.officeData[type] && this.officeData[type].indexOf("OTHER")!=-1){
                    return true
                }else{
                    return false
                }
            },
            stuRemoteMethod(query) {
                let that=this
                if (query !== '') {
                    this.loading = true;
                    $.ajax({
                        url: '<?php echo $this->createUrl("studentSearch") ?>',
                        type: "post",
                        dataType: 'json',
                        data: {
                            child_name:query
                        },
                        success: function(data) {
                            if (data.state == 'success') {
                                that.stuOption = Object.values(data.data) ;
                            }else{
                                resultTip({
                                    error: 'warning',
                                    msg: data.message
                                });
                            }
                            that.loading = false;
                        },
                        error: function(data) {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            that.loading = false;
                        },
                    })
                } else {
                    this.options = [];
                }
            },
            addStu(id){
                for(var i=0;i<this.addChildList.length;i++){
                    if(this.addChildList[i].id==this.stuVal){
                        resultTip({
                            error: 'warning',
                            msg: '不能重复添加'
                        });
                        return
                    }
                }
                this.stuOption.forEach(item => {
                    if(item.id==this.stuVal){
                        this.addChildList.push(item)
                    }
                })
                this.officeData.student_ids.push(this.stuVal)
                this.stuOption=[]
                this.stuVal=''
                console.log(this.officeData.student_ids)
            },
            delChild(index,id){
                this.addChildList.splice(index, 1)
                this.officeData.student_ids=this.officeData.student_ids.filter((item)=>{return item!=id});
            },
            saveOffice(type){
                let that=this
                if(this.officeData.student_ids.length==0){
                    resultTip({
                        error: 'warning',
                        msg: '请选择学生'
                    });
                    return
                }
                for(var i=0;i<this.attachments.img.length;i++){
                    this.officeData.attachments.img.push(this.attachments.img[i]._id)
                }
                for(var i=0;i<this.attachments.other.length;i++){
                    this.officeData.attachments.other.push(this.attachments.other[i]._id)
                }
                // for(var i=0;i<this.addChildList.length;i++){
                //     this.officeData.student_ids.push(this.addChildList[i].id)
                // }
                var data={id:this.contentList.behavior._id,data:this.officeData,help:0}
                $.ajax({
                    url: '<?php echo $this->createUrl("officeProcess") ?>',
                    type: "post",
                    dataType: 'json',
                    data:data,
                    success: function(data) {
                        if (data.state == 'success') {
                            $('#finalResultModal').modal('hide')
                            resultTip({
                                msg: data.message
                            });
                            that.showDetails(that.contentList.behavior._id)
                            
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            saveReaff(){
                let that=this
                for(var i=0;i<this.attachments.img.length;i++){
                    this.officeData.attachments.img.push(this.attachments.img[i]._id)
                }
                for(var i=0;i<this.attachments.other.length;i++){
                    this.officeData.attachments.other.push(this.attachments.other[i]._id)
                }
                var data={id:this.reaffData._id,data:this.officeData}
                $.ajax({
                    url: '<?php echo $this->createUrl("afreshAffirm") ?>',
                    type: "post",
                    dataType: 'json',
                    data:data,
                    success: function(data) {
                        if (data.state == 'success') {
                            $('#finalResultModal').modal('hide')
                            resultTip({
                                msg: data.message
                            });
                            that.showDetails(that.contentList.behavior._id)
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            saveComment(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("officeProcessAddComment") ?>',
                    type: "post",
                    dataType: 'json',
                    data:{
                        id:this.contentList.behavior._id,
                        comment:this.appendComment
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.appendText=false
                            resultTip({
                                msg: data.message
                            });
                            that.appendComment=''
                            that.showDetails(that.contentList.behavior._id)
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            showDisabled(id){
                var finallyId=[]
                this.contentList.finally_data.forEach(item => {
                    finallyId.push(item.child.id)
                });
                if(finallyId.indexOf(id)!=-1){
                    return true
                }else{
                    return false
                }
            },
            addReaff(){
                this.officeData={
                    identified:'',
                    actions_other:'',
                    duration:"00:00",
                    remark:'',
                    actions:[],
                    attachments:{
                        img:[],
                        other:[]
                    },
                    student_ids:[],
                    contact_parents:[]
                }
                this.addChildList=[]
                this.attachments={
                    img:[],
                    other:[]
                }
                this.editStatus=2
                this.reaffirmationType='admin'
                $('#finalResultModal').modal('show')
                if(this.uploader.length!=0) {
                    for(var i=0;i<this.uploader.length;i++){
                    this.uploader[i].destroy();
                    }
                }
                this.$nextTick(()=>{
                    config['token'] = this.qiniuToken;
                    config['browse_button'] ='finalResultAddFile';
                    var uploader=new plupload.Uploader(config);
                    this.uploader.push(uploader);
                    uploader.init();
                })
            },
            reaffirmation(data,type){
                this.reaffirmationType=this.contentList.is_office?'admin':'isp'
                this.officeData={
                    identified:data.result.identified,
                    actions_other:data.result.actions_other,
                    duration:data.result.duration,
                    remark:data.result.remark,
                    actions:data.result.actions,
                    attachments:{ 
                        img:[],
                        other:[]
                    },
                    contact_parents:data.result.contact_parents
                }
                var imgs=[]
                var others=[]
                if(data.result.attachments.img){
                    data.result.attachments.img.forEach(item => {
                        imgs.push(this.contentList.attachmentList[item]) 
                    });
                }
                if(data.result.attachments.other){
                    data.result.attachments.other.forEach(item => {
                        others.push(this.contentList.attachmentList[item]) 
                    });
                }
                this.attachments={
                    img:imgs,
                    other:others
                }
                this.editStatus=1
                this.reaffData=data
                $('#finalResultModal').modal('show')
                if(this.uploader.length!=0) {
                    for(var i=0;i<this.uploader.length;i++){
                    this.uploader[i].destroy();
                    }
                }
                this.$nextTick(()=>{
                    config['token'] = this.qiniuToken;
                    config['browse_button'] ='finalResultAddFile';
                    var uploader=new plupload.Uploader(config);
                    this.uploader.push(uploader);
                    uploader.init();
                    console.log(this.uploader)
                })

            },
            delImg(type,list,index){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("deleteAttachment") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        attachmentId:list._id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            if(type=='img'){
                                that.attachments.img.forEach((item,index) => {
                                    if (item._id==list._id) {
                                        that.attachments.img.splice(index, 1)
                                    } 
                                })
                            }else{
                                that.attachments.other.forEach((item,index) => {
                                    if (item._id==list._id) {
                                        that.attachments.other.splice(index, 1)
                                    } 
                                })
                            }
                            resultTip({
                                msg:data.state
                            });
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            saveFile(list,index) {
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("updateAttachmentName") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        attachmentId:list._id,
                        attachmentName:this.attachmentName

                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.attachments.other.forEach((item,index) => {
                                if (item._id==list._id) {
                                    Vue.set(that.attachments.other[index], 'title',data.data.title);
                                    Vue.set(that.attachments.other[index], 'file_key', data.data.url);
                                    Vue.set(that.attachments.other[index], 'isEdit', false);
                                } 
                            })
                            resultTip({
                                msg:'<?php echo Yii::t("newDS", "Data Saved!");?>'
                            });
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            showFollow(list){
                if(list.indexOf("OUTSCHOOLSUSPENSION")!=-1 || list.indexOf("INSCHOOLSUSPENSION")!=-1 ){
                    return true
                }else{
                    return false
                }
            },
            follow(data,type){
                this.followItem=data
                this.followType=type
                this.followData={
                    remark:this.followItem.followup.remark?this.followItem.followup.remark:'',
                    date:this.followItem.followup.date?this.followItem.followup.date:'',
                    time:this.followItem.followup.time?this.followItem.followup.time:''
                }
                $('#followModal').modal('show')
            },
            saveFollow(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("saveFollowup") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        id:this.followItem._id,
                        data:this.followData

                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.showDetails(that.contentList.behavior._id)
                            resultTip({
                                msg:data.message
                            });
                             $('#followModal').modal('hide')

                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            sendMessage(item){
                this.sendItem=item
                let that=this
                this.teacher_ids=[]
                this.sendRemark=''
                $.ajax({
                    url: '<?php echo $this->createUrl("getClassTeacherList") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        class_id:[item.child.class_id]
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.classTeacher=data.data[item.child.class_id]
                            $('#sendTeacherModal').modal('show')
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
                
            },
            allTeacher(){
                this.teacher_ids=[]
                if(this.checkall){
                    for(var keys in this.classTeacher){
                        this.teacher_ids.push(parseInt(keys))                         
                    }
                }
            },
            selectSendTeacher(){
                if(this.teacher_ids.length==Object.values(this.classTeacher).length){
                    this.checkall=true
                }else{
                    this.checkall=false
                }
            },
            saveSend(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("sendBehaviorMsgToTeacher") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        class_id:[this.sendItem.child.class_id],
                        id: this.sendItem._id,
                        remark: this.sendRemark,
                        "assist[class_id]":this.sendItem.child.class_id,
                        "assist[teacher_ids]":this.teacher_ids,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            resultTip({
                                msg: data.message
                            });
                            that.showDetails(that.contentList.behavior._id)
                            $('#sendTeacherModal').modal('hide')
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            viewSendMessage(item){
                this.sendLogList=item
                $('#viewSendLogModal').modal('show')
            },
            viewAffirm(list){
                let that=this
                if(list.affirm_num==0){
                    return
                }
                this.affirmData=list
                $.ajax({
                    url: '<?php echo $this->createUrl("affirmRecordList") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        child_id:list.id,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.affirmRecordList=data.data
                            $('#affirmRecordListModal').modal('show')
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            }
        }
    })
   
    var config = {
        runtimes: 'html5,flash,html4', //上传模式,依次退化
        container: 'container', //上传区域DOM ID，默认是browser_button的父元素，
        browse_button: '', //上传选择的点选按钮，**必需**
        token: '',
        flash_swf_url: '/js/plupload/Moxie.swf', // flash的相对地址
        auto_start: true, //选择文件后自动上传，若关闭需要自己绑定事件触发上传
        // multi_selection: false, //不允许多选
        url:'https://upload-z1.qiniup.com',
        init: {
            'FilesAdded': function(up, files) {
                container.disabledUpload=true
                let fileType=files[0].type.split('/')
                if(fileType[0]=="image"){
                  container.loadingType=1
                }else{
                  container.loadingType=2
                }
                container.loadingList=files
                up.start();
            },
            BeforeUpload: function(up, file) {
                //设置参数
                up.setOption({
                    multipart_params:{token:container.qiniuToken}
                })
                let fileType=file.type.split('/')
                if(fileType[0]=="image"){
                    container.attachments.img.push({types:'1'})
                }else{
                    container.attachments.other.push({types:'1',title:' <?php echo Yii::t("directMessage", "Uploading");?>'})  
                }
                container.loadingList.splice(0,1)
            },
            UploadProgress: function(up, file) {
                $('#progress').css('width', file.percent+'%').html(file.percent+'%');
            },
            'FileUploaded': function(up, file, info) {
                var response = eval('('+info.response+')');
                if(info.status == 200){
                    let fileType=response.data.mimetype.split('/')
                    if(fileType[0]=="image"){
                        container.attachments.img.splice(container.attachments.img.length-1,1)
                        container.attachments.img.push(response.data)
                    }else{
                        response.data.isEdit=false
                        container.attachments.other.splice(container.attachments.other.length-1,1)
                        container.attachments.other.push(response.data)
                    }
                }
            },
            'Error': function(up, err, errTip) {
                if(err.response){
                    var response = eval('('+err.response+')');
                }
                else{
                    var response = {message: err.message};
                }
                if(response.error == 'token not specified'){
                    up.stop();
                    $.ajax({
                        url: '<?php echo $this->createUrl("getQiniuToken") ?>',
                        type: "post",
                        dataType: 'json',
                        data: {
                            linkId: container.handId
                        },
                        success: function(data) {
                            if (data.state == 'success') {
                                container.qiniuToken=data.data
                                config['uptoken'] = data.data;
                                up.setOption("multipart_params", {"token":data.data,})
                                up.start();
                            } else {
                                resultTip({
                                    error: 'warning',
                                    msg: data.msg
                                });
                            }
                        },
                        error: function(data) {

                        },
                    })
                }
            },
            'UploadComplete': function(up, file) {
                //队列文件处理完毕后,处理相关的事情
            }
        }
    };
</script>
