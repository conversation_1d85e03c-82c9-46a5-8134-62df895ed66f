<?php

class IndexController extends ProtectedController
{
	public $layout="//layouts/main";
	public function actionIndex()
	{
		$this->render('index');
	}
	
	public function actionIndex1()
	{
		Mims::Nav();
		$criteria=new CDbCriteria;
		
		$criteria->compare('childid', $this->childid);
		$criteria->compare('status', '<>99');
		//$criteria->order = 'timestamp DESC';
		$provider=new CActiveDataProvider('invoice',array(
				'criteria'   => $criteria,
				'pagination' => array('pageSize'=>5)
		));
		
		$this->render('index',array('provider'=>$provider));
	}	
	
	public function actionIndex2()
	{
		
		$criteria=new CDbCriteria;
		
		$criteria->compare('childid', $this->childid);
		$criteria->compare('status', '<>99');
		//$criteria->order = 'timestamp DESC';
		$provider=new CActiveDataProvider('invoice',array(
				'criteria'   => $criteria,
				'pagination' => array('pageSize'=>5)
		));
		
		$this->render('index',array('provider'=>$provider));
	}	

	public function actionIndex3()
	{
		
		$criteria=new CDbCriteria;
		
		$criteria->compare('childid', $this->childid);
		$criteria->compare('status', '<>99');
		//$criteria->order = 'timestamp DESC';
		$provider=new CActiveDataProvider('invoice',array(
				'criteria'   => $criteria,
				'pagination' => array('pageSize'=>5)
		));
		
		$this->render('index',array('provider'=>$provider));
	}	
	
	public function actionRoletest($role)
	{
		$params["childid"] = $this->childid;
		if(Yii::app()->user->checkAccess($role,$params))
		{
			echo CJSON::encode(array("result"=>"success","ret"=>"You have the privilege"));
			Yii::app()->end();		
		}else{
			echo CJSON::encode(array("result"=>"success","ret"=>"Illegal request!"));
			Yii::app()->end();					
		}
	}

	
	public function actionChangetheme($theme)
	{
		$theme = strtolower(trim($theme));
		$cookiename = "child_mims_theme";
		if(in_array($theme, array("green","classic"))){
			unset(Yii::app()->request->cookies[$cookiename]);
			$cookie = new CHttpCookie($cookiename, $theme);
			Yii::app()->request->cookies[$cookiename]=$cookie;			
			
			echo CJSON::encode(array("result"=>"success","ret"=>$theme));
			Yii::app()->end();		
		}
	}

	// 家长手册操作接口
	public function actionHandbook()
	{
		if (Yii::app()->request->isPostRequest) {
			if ($this->role !='parent') {
				return false;
			}
			Yii::import('common.models.feedback.HandbookReport');
			$version = Yii::app()->request->getParam('version', '');
			$childid = Yii::app()->request->getParam('childid', '');
			if (!$version || !$childid) {
				echo json_encode(array('state'=>'fail', 'message'=>'参数错误'));
				return;
			}
			$uid = Yii::app()->user->getId();
			if (!in_array($childid, array_keys($this->myChildObjs))) {
				echo json_encode(array('state'=>'fail', 'message'=>'孩子ID不匹配'));
				return;
			}
			$criteria = new CDbCriteria;
			$criteria->compare('version', $version);
			$criteria->compare('childid', $childid);
			if (HandbookReport::model()->count($criteria)) {
				echo json_encode(array('state'=>'fail', 'message'=>'此版本已同意'));
				return;
			}
			$model = new HandbookReport;
			$model->version = $version;
			$model->childid = $childid;
			$model->status = HandbookReport::STATS_AGREE;
			$model->uid = $uid;
			$model->timestamp = time();
			if (!$model->save()) {
				$error = current(current($model->getErrors()));
				echo json_encode(array('state'=>'fail', 'message'=>$error));
				return;
			}
			echo json_encode(array('state'=>'success', 'message'=>Yii::t('global', 'Success!')));
			return ;
		}
		return false;
	}
	
}