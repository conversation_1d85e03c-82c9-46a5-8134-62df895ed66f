<?php

/**
 * This is the model class for table "ivy_pta_memo_content".
 *
 * The followings are the available columns in table 'ivy_pta_memo_content':
 * @property integer $id
 * @property string $content
 * @property string $ext_members
 * @property string $ext_admins
 */
class PtaMemoContent extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return PtaMemoContent the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_pta_memo_content';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('content', 'required'),
			array('id', 'numerical', 'integerOnly'=>true),
            array('ext_members, ext_admins', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, content, ext_members, ext_admins', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'content' => '会议内容',
			'ext_members' => '额外出席家长',
			'ext_admins' => '额外出席老师',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('content',$this->content,true);
		$criteria->compare('ext_members',$this->ext_members,true);
		$criteria->compare('ext_admins',$this->ext_admins,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

    public function renderContent()
    {
        $langTag = '{{lang-tag}}';

        if(strpos($this->content, $langTag) !== false){
            $tmp = explode($langTag, $this->content);
            $content = HtoolKits::autoLang($tmp[0], $tmp[1]);
        }
        else{
            $content = $this->content;
        }
        return $content;
    }
}