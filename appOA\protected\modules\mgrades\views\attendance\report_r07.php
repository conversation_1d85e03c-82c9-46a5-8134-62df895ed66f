
<style type="text/css">
 [v-cloak] {
	display: none;
}
.select_2{
    min-width:120px !important
}
.loading{
	width:98%;
	height:90%;
	background:#fff;
	position: absolute;
	opacity: 0.5;
	z-index: 99
}
.loading span{
	width:100%;
	height:60%;
	display:block;
	background: url("<?php echo Yii::app()->theme->baseUrl ?>/images/loading.gif")no-repeat center center ;
}
.table_wrap {
    width: 100%;
    overflow: auto;
}

table {
    table-layout: fixed;
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    border-left: 1px solid #DDDDDD;
    border-top: 1px solid #DDDDDD;
}
td,th {
    width: 200px;
    text-align:center;
    border-right: 1px solid #DDDDDD;
    border-bottom: 1px solid #DDDDDD;
    border-top:none !important
}

/* 表头固定 */
thead tr th {
    position: sticky;
    top: 0;
    background: #F4F4F5;
}

/* 首列固定/最后一列固定*/
th:first-child,
th:last-child,
td:first-child,
td:last-child {
    position: sticky;
    left: 0;
    background: #F4F4F5;
    text-align: center;
    right: 0px;
    width: 100px;
}

/* 表头首列和最后一列强制最顶层 */
th:last-child,
th:first-child {
    z-index: 3;
    /*左上角单元格z-index，切记要设置，不然表格纵向横向滚动时会被该单元格右方或者下方的单元格遮挡*/
    background: #F4F4F5;
}
  .scrollbar::-webkit-scrollbar {
  /*滚动条整体样式*/
  width : 10px;  /*高宽分别对应横竖滚动条的尺寸*/
  height:10px;
  }
  .scrollbar::-webkit-scrollbar-thumb {
    border-radius   : 10px;
    background-color: skyblue;
    background-image: -webkit-linear-gradient(
    45deg,
    rgba(255, 255, 255, 0.2) 25%,
    transparent 25%,
    transparent 50%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0.2) 75%,
    transparent 75%,
    transparent
    );
  }
  .scrollbar::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  box-shadow   : inset 0 0 5px rgba(0, 0, 0, 0.2);
  border-radius: 10px;
  background   : #ededed;
  }
</style>
<div id='container'  v-cloak>
	<h3 class='color3 font14'><strong style='font-size:18px' class='mr20'><?php echo Yii::t('attends', 'Attendance Report by Subjects'); ?></strong></h3>
    <div class='form-inline mt20'>
        <p class='pull-left mr20 pr20'>
            <span class='color3 mr5 font14'><?php echo Yii::t("labels", 'School Year') ?></span>
            <select class="form-control select_2" v-model='yid' @change='class_id=""'>
            <option  value=''><?php echo Yii::t("global", 'Please Select') ?></option>  
            <option v-for='(list,index) in yearList.school_year_list' :value='list.yid'>{{list.schoolyear}}</option>
            </select>
        </p>
        <p class='pull-left mr20 pr20 '>
            <span class='color3 mr5 font14'><?php echo Yii::t("teaching", 'semester') ?></span>
            <select class="form-control select_2" v-model='semester'>
            <option  value=''><?php echo Yii::t("global", 'Please Select') ?></option>  
                
            <option v-for='(list,key,index) in yearList.semester' :value='key'>{{list}}</option>
            </select>
        </p>
        <p class='pull-left mr20 pr20'>
            <span class='color3 mr5 font14'><?php echo Yii::t("user", 'Grade') ?></span>
            <select class="form-control select_2"  v-model='class_id' @change='searchData'>
                <option  value=''><?php echo Yii::t("global", 'Please Select') ?></option>  
                <template v-if='yid!=""'>
                    <option v-for='(list,index) in yearList.class_list[yid]' :value='list.classtype'>{{list.classTypeName}}</option>  
                </template>
            </select>
        </p>
    </div>
    <div class='clearfix'></div>
    <div class='loading'  v-if='showLoading'>
		<span></span>
	</div>
    <div v-if='showData'>
        <div v-if='Object.keys(tableList).length!=0'>
            <p class='mt20 mb20 ' v-if='tableList.child_list.length!=0'>
                <button type="button" class="btn btn-primary pull-right" @click='exportTab()'><?php echo Yii::t("user", 'Export') ?></button>
            </p>
            <div class='clearfix'></div>
            <div class='table_wrap scrollbar mb20'>
                <table class="table table-hover  mt20" id='table' v-if='tableList.child_list.length!=0'>
                    <tr>
                        <th><?php echo Yii::t("report", 'Name') ?></th>
                        <th v-for='(list,key,index) in tableList.courses_list'>{{list.title}}</th>
                        <th><?php echo Yii::t("payment", 'Total') ?></th>
                    </tr>
                    <tr v-for='(list,key,index) in tableList.child_list'>
                        <th>{{list.name}}</th>
                        <td v-for='(item,suject,idx) in tableList.list[key]'>{{item.absence}}</td>
                        <td >{{tableList.child_absence_list[key]}}</td>
                    </tr>
                    <tr>
                        <th><?php echo Yii::t("payment", 'Total') ?></th>
                        <td v-for='(list,key,index) in tableList.courses_list'>{{tableList.course_absence_list[key]}}</td>
                        <th></th>
                    </tr>
                </table>
                <div class="alert alert-danger mt20" role="alert" v-else><?php echo Yii::t('asa', 'No data found.') ?></div>
            </div>
        </div>
        <div class="alert alert-danger mt20" role="alert" v-else><?php echo Yii::t('asa', 'No data found.') ?></div>
    </div>
</div>

<script>
	var container = new Vue({
        el: "#container",
        data: {
            yid:'',
            semester:'',
            class_id:'',
            tableList:{},
            pageList:'',
			showLoading:false,
            showData:false,
            yearList:{}
        },
		created(){
            this.initData()
		},
        methods: {
            initData(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("SubjectAbsenceClass") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                    },
                    success: function(data) {
                        if(data.state=='success'){
							that.yearList=data.data
                            that.yid=data.data.default_schoolyear
                            that.semester=data.data.default_term
                        }else{
                        }
                    }
                })
            },
            searchData(page){
                if(page){
                    this.current_page=page
                }
                let that=this
				this.showLoading=true
				this.showData=false
				$.ajax({
                    url: '<?php echo $this->createUrl("SubjectAbsence") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
						"class_type": this.class_id,
                        "term":this.semester,
                        "yid": this.yid
                    },
                    success: function(data) {
                        if(data.state=='success'){
							console.log(data)
                            that.tableList=data.data
				            that.showLoading=false
                            that.showData=true
                        }else{
                            that.showLoading=false
                            that.showData=true
                        }
                    },error:function(){
                        that.showLoading=false
                        that.showData=false
                    }
                })
            },
            exportTab(){
                var title='学科考勤统计'
                var elt = document.getElementById('table');
                var wb = XLSX.utils.table_to_book(elt, {sheet:title});
                return XLSX.writeFile(wb, title+'.xlsx');
            }
		}
    })
</script>
