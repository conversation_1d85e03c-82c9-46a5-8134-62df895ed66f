<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span>
    </button>
    <h4 class="modal-title" id="exampleModalLabel"><?php echo $model->course_id ? '更新：' : '添加：'; ?></h4>
</div>
<?php $form = $this->beginWidget('CActiveForm', array(
    'id' => 'course-desc',
    'enableAjaxValidation' => false,
    'htmlOptions' => array('class' => 'J_ajaxForm form-horizontal', 'role' => 'form'),
)); ?>
<div class="modal-body">
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'brief_cn'); ?></label>

        <div class="col-xs-9">
            <?php echo $form->textArea($model, 'brief_cn', array('maxlength' => 255, 'class' => 'form-control')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'brief_en'); ?></label>

        <div class="col-xs-9">
            <?php echo $form->textArea($model, 'brief_en', array('maxlength' => 255, 'class' => 'form-control')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'desc_cn'); ?></label>
        <div class="col-xs-9">
            <?php
            $toolbars = array('fullscreen', 'source', '|', 'undo', 'redo', '|','bold', 'italic', 'underline', 'strikethrough', 'superscript', 'subscript', '|','insertorderedlist', 'insertunorderedlist', '|','link', 'unlink', 'simpleupload');
            $this->widget('common.extensions.ueditor.ueditor_1_5_0',array(
                'model' => $model,
                'attribute' => 'desc_cn',
                'configFile' => 'ueditor.asa.config',
                'language' =>Yii::app()->language == 'en_us' ? 'en' : 'zh-cn',
                'editorOptions' => array('initialFrameWidth'=>'100%'),
                'toolbars'=>$toolbars,
                'classId' => "asa",
            ));
            ?>

        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'desc_en'); ?></label>
        <div class="col-xs-9">
            <?php
            $this->widget('common.extensions.ueditor.ueditor_1_5_0',array(
                'model' => $model,
                'attribute' => 'desc_en',
                'configFile' => 'ueditor.asa.config',
                'language' =>Yii::app()->language == 'en_us' ? 'en' : 'zh-cn',
                'editorOptions' => array('initialFrameWidth'=>'100%'),
                'toolbars'=>$toolbars,
                'classId' => "asa",
            ));
            ?>
        </div>
    </div>
</div>
<div class="modal-footer">
    <button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global', 'Submit'); ?></button>
    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('global', 'Cancel'); ?></button>
</div>
<?php $this->endWidget(); ?>

<script>
    var ueAsaCourseDesc_desc_cn;
    var ueAsaCourseDesc_desc_en;
    function cbCourseDesc() {
        var courseId = '<?php echo $model->course_id; ?>';
        $('#modal1').modal('hide');
        window.location.reload(true);
    }

    $('#modal1').on('hidden.bs.modal', function (e) {
        if(ueAsaCourseDesc_desc_cn){
            ueAsaCourseDesc_desc_cn.destroy();
        }
        if(ueAsaCourseDesc_desc_en){
            ueAsaCourseDesc_desc_en.destroy();
        }
    })

</script>
