<?php
$data = json_decode($model->data);

$medicalReport = array();
if($data->medicalReport){
    foreach ($data->medicalReport as $item){
        $medicalReport[] = RegExtraInfo::getOssImageUrl($item);;
    }
}
$vaccineReport = array();
if($data->vaccineReport){
    foreach ($data->vaccineReport as $item){
        $vaccineReport[] = RegExtraInfo::getOssImageUrl($item);;
    }
}
$healthReport = array();
if($data->healthReport){
    foreach ($data->healthReport as $item){
        $healthReport[] = RegExtraInfo::getOssImageUrl($item);;
    }
}
?>
<?php
    if($medicalReport){
        foreach ($medicalReport as $val){
        ?>
        <img class="img-responsive" src="<?php echo $val; ?>">
<?php   }
    }
?>
<?php
if($vaccineReport){
    foreach ($vaccineReport as $val){
        ?>
        <img class="img-responsive" src="<?php echo $val; ?>">
    <?php   }
}
?>
<?php
if($healthReport){
    foreach ($healthReport as $val){
        ?>
        <img class="img-responsive" src="<?php echo $val; ?>">
    <?php   }
}
?>

