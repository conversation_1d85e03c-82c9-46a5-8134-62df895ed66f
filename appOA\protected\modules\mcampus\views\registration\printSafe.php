<?php
$feeCn = '';
$feeEn = '';
if($configModel){
    $baseContentCn = ($configModel->content_cn) ? json_decode($configModel->content_cn) : '';
    $feeCn = ($baseContentCn) ? nl2br(base64_decode($baseContentCn->safe)) : '';

    $baseContentCn = ($configModel->content_en) ? json_decode($configModel->content_en) : '';
    $feeEn = ($baseContentCn) ? nl2br(base64_decode($baseContentCn->safe)) : '';
}

?>
<style>
.bus{
	width:950px;
	margin:0 auto;
}
.sign{
    float: right;
    margin-right:20px;
    margin-bottom:20px
}
.sign img{
    width:100px;
}
</style>
<div class="container-fluid">
	<div class="row">
        <?php if($model){
            $data = json_decode($model->data);
            ?>
       		<div class="bus">
	            <div><?php echo $feeCn; ?></div>
	            <div><?php echo $feeEn; ?></div>
	            <div class="sign"><img src="<?php echo RegExtraInfo::getOssImageUrl($data->filedata) ?>" alt=""></div>
            </div>
        <?php } ?>
    </div>
</div>