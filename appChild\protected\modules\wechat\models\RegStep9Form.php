<?php
class RegStep9Form extends CFormModel
{
	public $studentdata = array();
    public $studentdataFile;
	public $parentsdata = array();
    public $parentsdataFile;
	public $vaccinedata = array();
    public $vaccinedataFile;
	public $insurancedata = array();
    public $insurancedataFile;


	public function rules()
	{
		return array(
            array('studentdata, parentsdata, vaccinedata', 'required', 'on'=>'submit'),
            array('studentdata, parentsdata, vaccinedata, insurancedata, studentdataFile, parentsdataFile, vaccinedataFile, insurancedataFile', 'safe'),
            //array("studentdataFile, parentsdataFile, vaccinedataFile, insurancedataFile","file", "types" => "jpg, gif, png, jpeg", "maxSize" => 1024*1024*5, "tooLarge"=>Yii::t('reg', 'File should not exceed 5M'), 'allowEmpty' => true, 'on'=>'uploadFile'),
		);
	}

	public function attributeLabels()
	{
        return array(
            'studentdata' => Yii::t('reg', 'Student Passport Photo (only for foreign student)'),
            'parentsdata' => Yii::t('reg', 'Mother Passport Photo'),
            'vaccinedata' => Yii::t('reg', 'Photo Copy of Vaccine Record'),
        );
	}

    public function uploadFile()
    {
        $oss = CommonUtils::initOSS('private');

        $this->studentdataFile ? $this->studentdata = array() : '';
        foreach ($this->studentdataFile as $item) {
            $fileName = 'studentdata_' . time() . '_' .uniqid() . '.' . $item->getExtensionName();
            if ($res = $this->upload($oss, $item, $fileName)) {
                $this->studentdata[] = $res;
            } else {
                return false;
            }
        }

        $this->parentsdataFile ? $this->parentsdata = array() : '';
        foreach ($this->parentsdataFile as $item) {
            $fileName = 'parentsdata_' . time() . '_' .uniqid() . '.' . $item->getExtensionName();
            if ($res = $this->upload($oss, $item, $fileName)) {
                $this->parentsdata[] = $res;
            } else {
                return false;
            }
        }

        $this->vaccinedataFile ? $this->vaccinedata = array() : '';
        foreach ($this->vaccinedataFile as $item) {
            $fileName = 'vaccinedata_' . time() . '_' .uniqid() . '.' . $item->getExtensionName();
            if ($res = $this->upload($oss, $item, $fileName)) {
                $this->vaccinedata[] = $res;
            } else {
                return false;
            }
        }

        $this->insurancedataFile ? $this->insurancedata = array() : '';
        foreach ($this->insurancedataFile as $item) {
            $fileName = 'insurancedata_' . time() . '_' .uniqid() . '.' . $item->getExtensionName();
            if ($res = $this->upload($oss, $item, $fileName)) {
                $this->insurancedata[] = $res;
            } else {
                return false;
            }
        }

        return true;
    }

    public function upload($oss, $file, $fileName)
    {
        if ($file) {
            // 上传到 OSS
            $objectPath = 'regextrainfo/';

            if ($oss->uploadFile($objectPath . $fileName, $file->getTempName())) {
                return $fileName;
            }
        }
        return false;
    }

    public function getOssImageUrl($fileName)
    {
        $fileName = 'regextrainfo/' . $fileName;
        // 获取文件临时地址
        $osscs = CommonUtils::initOSSCS('private');

        $object = $fileName;
        $style = 'style/w200';
        $imageUrl = $osscs->getImageUrl($object, $style);
        return $imageUrl;
    }

    public function updataSignature()
    {
        // 判断是否为 base64 图片
        if (strpos($this->filedata, 'base64') === false) {
            return true;
        }
        $data = base64_decode(preg_replace('#^data:image/\w+;base64,#i', '', $this->filedata));

        $normalDir = rtrim(Yii::app()->params['xoopsVarPath'], '/') .'/';

        $fileName = uniqid() . '.png';

        $filePath = $normalDir . $fileName;

        if (file_put_contents($filePath, $data)) {
            $this->filedata = $fileName;

            if ($this->filedata) {
                // 上传到 OSS
                $objectPath = 'regextrainfo/';
                $oss = CommonUtils::initOSS('private');
                if ($oss->uploadFile($objectPath . $this->filedata, $filePath)) {
                    unlink($filePath);
                    return true;
                }
            }
        }
        return false;
    }

}
