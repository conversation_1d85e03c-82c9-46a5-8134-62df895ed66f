<?php $form=$this->beginWidget('CActiveForm', array(
	'id'=>'visits-form',
	'enableAjaxValidation'=>false,
    'htmlOptions'=>array('class'=>'J_ajaxForm'),
));
$branchList = Branch::model()->getBranchList();
$cfgs = OA::LoadConfig('CfgVisit');
$knowusList = array();
if (isset($cfgs['knowus']))
{
	foreach ($cfgs['knowus'] as $k=>$v)
	{
		$knowusList[$k] = (Yii::app()->language=='zh_cn') ? $v['cn'] : $v['en'];
	}
}
$languageList = array();
if (isset($cfgs['language']))
{
	foreach ($cfgs['language'] as $k=>$v)
	{
		$languageList[$k] = (Yii::app()->language=='zh_cn') ? $v['cn'] : $v['en'];
	}
}
?>

<div class="pop_cont pop_table">
    <table width="100%" class="" style="height:auto;">
        <col width="150" />
        <col width="400" />
        <col />
        <tbody>
            <tr>
                <th><?php echo $form->labelEx($model,'visit_date'); ?></th>
                <td>
                    <?php
                    $this->widget('common.extensions.datePicker.JMy97DatePicker', array(
                          'name'=>CHtml::activeName($model,'visit_date'),
                          'value'=>$model->visit_date,
                          'options'=>array('dateFmt'=>'yyyy-MM-dd','opposite'=>true),
                    	  'htmlOptions'=>array('readonly'=>'readonly', 'class'=>'input'),
                    ));
                    ?>
                </td>
                <td><div class="fun_tips"><?php echo $form->error($model,'visit_date'); ?></div></td>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($model,'visit_time'); ?></th>
                <td>
                    <?php echo $form->textField($model,'visit_time',array('maxlength'=>255,'class'=>'input length_6')); ?>
                </td>
                <td><div class="fun_tips"><?php echo $form->error($model,'visit_time'); ?></div></td>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($model,'parent_name'); ?></th>
                <td>
                    <?php echo $form->textField($model,'parent_name',array('maxlength'=>255,'class'=>'input length_6')); ?>
                </td>
                <td><div class="fun_tips"><?php echo $form->error($model,'parent_name'); ?></div></td>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($model,'phone'); ?></th>
                <td>
                    <?php echo $form->textField($model,'phone',array('maxlength'=>255,'class'=>'input length_6')); ?>
                </td>
                <td><div class="fun_tips"><?php echo $form->error($model,'phone'); ?></div></td>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($model,'email'); ?></th>
                <td>
                    <?php echo $form->textField($model,'email',array('maxlength'=>255,'class'=>'input length_6')); ?>
                </td>
                <td><div class="fun_tips"><?php echo $form->error($model,'email'); ?></div></td>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($model,'child_name'); ?></th>
                <td>
                    <?php echo $form->textField($model,'child_name',array('maxlength'=>255,'class'=>'input length_6')); ?>
                </td>
                <td><div class="fun_tips"><?php echo $form->error($model,'child_name'); ?></div></td>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($model,'schoolid'); ?></th>
                <td>
                    <?php echo $form->dropDownList($model,'schoolid',$branchList); ?>
                </td>
                <td><div class="fun_tips"><?php echo $form->error($model,'schoolid'); ?></div></td>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($model,'birthdate'); ?></th>
                <td>
                    <?php
                    $this->widget('common.extensions.datePicker.JMy97DatePicker', array(
                          'name'=>CHtml::activeName($model,'birthdate'),
                          'value'=>$model->birthdate,
                          'options'=>array('dateFmt'=>'yyyy-MM-dd','opposite'=>true),
                    	  'htmlOptions'=>array('readonly'=>'readonly', 'class'=>'input'),
                    ));
                    ?>
                </td>
                <td><div class="fun_tips"><?php echo $form->error($model,'birthdate'); ?></div></td>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($model,'address'); ?></th>
                <td>
                    <?php echo $form->textField($model,'address',array('maxlength'=>255,'class'=>'input length_6')); ?>
                </td>
                <td><div class="fun_tips"><?php echo $form->error($model,'address'); ?></div></td>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($model,'knowus'); ?></th>
                <td>
                    <?php echo $form->dropDownList($model,'knowus',$knowusList); ?>
                </td>
                <td><div class="fun_tips"><?php echo $form->error($model,'knowus'); ?></div></td>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($model,'receive_language'); ?></th>
                <td>
                    <?php echo $form->dropDownList($model,'receive_language',$languageList); ?>
                </td>
                <td><div class="fun_tips"><?php echo $form->error($model,'receive_language'); ?></div></td>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($model,'memo'); ?></th>
                <td>
                    <?php echo $form->textArea($model,'memo', array('class'=>'length_6')); ?>
                </td>
                <td><div class="fun_tips"><?php echo $form->error($model,'memo'); ?></div></td>
            </tr>
        </tbody>
    </table>
</div>
<div class="pop_bottom">
    <button type="button" class="btn fr" id="J_dialog_close"><?php echo Yii::t('global','Cancel');?></button>
    <button class="btn fr btn_submit mr10 J_ajax_submit_btn"><?php echo Yii::t('global','Submit');?></button>
</div>

<?php $this->endWidget(); ?>