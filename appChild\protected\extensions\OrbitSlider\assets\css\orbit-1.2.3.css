/* CSS for jQuery Orbit Plugin 1.2.3
 * www.ZURB.com/playground
 * Copyright 2010, ZURB
 * Free to use under the MIT license.
 * http://www.opensource.org/licenses/mit-license.php
 
 
 
/* PUT IN YOUR SLIDER ID AND SIZE TO MAKE LOAD BEAUTIFULLY
   ================================================== */
#featured { 
	width: 940px;
	height: 450px;
	background: #000 url('orbit/loading.gif') no-repeat center center;
	overflow: hidden; }
#featured>img,  
#featured>div,
#featured>a { display: none; }




/* CONTAINER
   ================================================== */

div.orbit-wrapper {
    width: 1px;
    height: 1px;
    position: relative; }

div.orbit {
    width: 1px;
    height: 1px;
    position: relative;
    overflow: hidden }

div.orbit>img {
    position: absolute;
    top: 0;
    left: 0;
    display: none; }

div.orbit>a {
    border: none;
    position: absolute;
    top: 0;
    left: 0;
    line-height: 0; 
    display: none; }

.orbit>div {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%; }

/* Note: If your slider only uses content or anchors, you're going to want to put the width and height declarations on the ".orbit>div" and "div.orbit>a" tags in addition to just the .orbit-wrapper */


/* TIMER
   ================================================== */

div.timer {
    width: 40px;
    height: 40px;
    overflow: hidden;
    position: absolute;
    top: 10px;
    right: 10px;
    opacity: .6;
    cursor: pointer;
    z-index: 1001; }

span.rotator {
    display: block;
    width: 40px;
    height: 40px;
    position: absolute;
    top: 0;
    left: -20px;
    background: url(orbit/rotator-black.png) no-repeat;
    z-index: 3; }

span.mask {
    display: block;
    width: 20px;
    height: 40px;
    position: absolute;
    top: 0;
    right: 0;
    z-index: 2;
    overflow: hidden; }

span.rotator.move {
    left: 0 }

span.mask.move {
    width: 40px;
    left: 0;
    background: url(orbit/timer-black.png) repeat 0 0; }

span.pause {
    display: block;
    width: 40px;
    height: 40px;
    position: absolute;
    top: 0;
    left: 0;
    background: url(orbit/pause-black.png) no-repeat;
    z-index: 4;
    opacity: 0; }

span.pause.active {
    background: url(orbit/pause-black.png) no-repeat 0 -40px }

div.timer:hover span.pause,
span.pause.active {
    opacity: 1 }


/* CAPTIONS
   ================================================== */

.orbit-caption {
    display: none;
    font-family: "HelveticaNeue", "Helvetica-Neue", Helvetica, Arial, sans-serif; }

.orbit-wrapper .orbit-caption {
    background: #000;
    background: rgba(0,0,0,.6);
    z-index: 99;
    color: #fff;
	text-align: center;
	padding: 7px 0;
    font-size: 13px;
    position: absolute;
    right: 0;
    bottom: 0;
    width: 100%; }


/* DIRECTIONAL NAV
   ================================================== */

div.slider-nav {
    display: block }

div.slider-nav span {
    width: 50px;
    height: 75px;
    text-indent: -9999px;
    position: absolute;
    z-index: 1000;
    top: 50%;
    margin-top: -37px;
    cursor: pointer; }

div.slider-nav span.right {
    background: url(orbit/right-arrow.png);
    right: 0; }

div.slider-nav span.left {
    background: url(orbit/left-arrow.png);
    left: 0; }

/* BULLET NAV
   ================================================== */

.orbit-bullets {
    position: absolute;
    z-index: 1000;
    list-style: none;
    bottom: -40px;
    left: 50%;
	margin-left: -50px;
    padding: 0; }

.orbit-bullets li {
    float: left;
    margin-left: 5px;
    cursor: pointer;
    color: #999;
    text-indent: -9999px;
    background: url(orbit/bullets.jpg) no-repeat 4px 0;
    width: 13px;
    height: 12px;
    overflow: hidden; }

.orbit-bullets li.active {
    color: #222;
    background-position: -8px 0; }
    
.orbit-bullets li.has-thumb {
    background: none;
    width: 100px;
    height: 75px; }

.orbit-bullets li.active.has-thumb {
    background-position: 0 0;
    border-top: 2px solid #000; }