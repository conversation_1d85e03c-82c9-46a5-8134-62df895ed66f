<?php

/**
 * 学校类型
 */
return array(
    "programs" => array(
        "10" => array(
            "sign" => "IA",
            "title" => Yii::t("global", "Ivy Academy"),
            "url" => "#", //应该l连接到ivyschools IA的介绍
        ),
        "20" => array(
            "sign" => "IBS",
            "title" => Yii::t("global", "Ivy Bilingual School"),
            "url" => "#", //应该l连接到ivyschools IA的介绍
        ),
        "30" => array(
            "sign" => "MIK",
            "title" => Yii::t("global", "Ivy MI Kindergarten"),
            "url" => "#", //应该l连接到ivyschools IA的介绍
        ),
    ),
    'library_auth' => array(
        'BJ_OE' => true,
        'BJ_OG' => true,
        'BJ_IA' => true,
        'BJ_XHL' => true,
        'BJ_TS' => true,
        'BJ_TT' => true,
        'CD_LH' => true,
        'XA_LB' => true,
        'TJ_SA' => true,
        'TJ_EC' => true,
        'NB_HH' => true,
        'BJ_CP' => true,
        'TJ_ES' => true,
    ),
    'download_auth' => array(
        'BJ_IA' => true,
        'BJ_CP' => true,
        'BJ_OE' => true,
        'BJ_OG' => true,
        'TJ_SA' => true,
        'NB_HH' => true,
    ),
	'hidePTA' => array(
//		'BJ_IA' => true,
//		'BJ_CP' => true,
	),
//    'showPTC' => array(
//        'BJ_OE',
//        'BJ_OG',
//    )
);
?>