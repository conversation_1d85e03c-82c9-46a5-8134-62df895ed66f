<?php

class PtcScheduleController extends TeachBasedController
{

    protected $yid;
    protected $startYear;
    protected $semester;
    protected $startYearList = array();
    protected $semesterList = array();
    protected $manageAccess = false;

    public function createUrlPtc($route, $params = array(), $ampersand = '&')
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        if (empty($params['startyear'])) {
            $params['startyear'] = $this->startYear;
        }
        if (empty($params['semester'])) {
            $params['semester'] = $this->semester;
        }
        return parent::createUrl($route, $params, $ampersand);
    }

    public function init()
    {
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'campusOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site', 'Campus Workspace');

        //初始化选择校园页面
        $this->branchSelectParams['hideKG'] = true;
        $this->branchSelectParams['urlArray'] = array('//mteaching/ptcSchedule/index');
        $cs = Yii::app()->clientScript;
        $cs->registerCoreScript('jquery.ui');
        $cs->registerCssFile($cs->getCoreScriptUrl() . '/jui/css/base/jquery-ui.css');
        $cs->registerCssFile(Yii::app()->theme->baseUrl . '/css/calendar.css');
        // $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/vue2.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/sortable/Sortable.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/sortable/jquery-sortable.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/xlsx.full.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/v-calendar/vue.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/v-calendar/v-calendar.umd.min.js');
    }

    public function beforeAction($action)
    {
        parent::beforeAction($action);
        $this->getCalendars();
        $this->yid = $this->calendarYids['currentYid'];
        if ($startYear = Yii::app()->request->getParam('startyear')) {
            $this->startYear = $startYear;
        } else {
            // 获取当前校历的开始年
            $this->startYear = current($this->calendarStartYear);
        }
        if ($semester = Yii::app()->request->getParam('semester')) {
            $this->semester = $semester;
        } else {
            // 指定学期
            $res = CalendarSemester::model()->getSemesterTimeStamp($this->yid);
            $this->semester = 10;
            if (time() > $res['spring_start']) {
                $this->semester = 20;
            }
        }

        $this->semesterList = array(
            10 => Yii::t('labels', '1st Semester'),
            20 => Yii::t('labels', '2nd Semester'),
        );
        $start = 2021;
        while ($start <= current($this->calendarStartYear)) {
            $next = $start + 1;
            $this->startYearList[$start] = "$start - $next";
            $start++;
        }

        // 是否具有管理权限
        if (Yii::app()->user->checkAccess('ivystaff_it') || Yii::app()->user->checkAccess('ivystaff_opschool')) {
            $this->manageAccess = true;
        }

        return true;
    }

    // 首页数据
    public function actionIndex()
    {
        $this->render('/ptc/schedule', array('data' => array()));
    }

    // 时间设置页面数据
    public function actionClassList()
    {
        $this->addMessage("state", "fail");

        $requestData = array(
            'startyear' => $this->startYear,
            'semester' => $this->semester,
            'school_id' => $this->branchId,
        );

        $res = CommonUtils::requestDsOnline('ptc/schedule/classList', $requestData);
        if (!isset($res['code'])) {
            $this->addMessage('message', 'api error');
            $this->showMessage();
        }
        if ($res['code'] != 0) {
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
        $this->addMessage('state', "success");
        $this->addMessage('data', $res['data']);
        $this->showMessage();
    }

    // 时间设置页面数据
    public function actionScheduleData()
    {
        $this->addMessage("state", "fail");

        $requestData = array(
            'startyear' => $this->startYear,
            'semester' => $this->semester,
            'school_id' => $this->branchId,
        );

        $res = CommonUtils::requestDsOnline('ptc/schedule/index', $requestData);
        if (!isset($res['code'])) {
            $this->addMessage('message', 'api error');
            $this->showMessage();
        }
        if ($res['code'] != 0) {
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
        $this->addMessage('state', "success");
        $this->addMessage('data', $res['data']);
        $this->showMessage();
    }


    // 我的 PTC
    public function actionMyPtc()
    {
        $teacherId = $this->getTeacherId();
        $date = Yii::app()->request->getParam('date', date('Y-m-d'));
        $yid = Yii::app()->request->getParam('yid');

        $requestData = array(
            'startyear' => $this->startYear,
            'semester' => $this->semester,
            'school_id' => $this->branchId,
            'teacher_id' => $teacherId,
            'date' => $date,
        );
        $res = CommonUtils::requestDsOnline('ptc/schedule/myPtc', $requestData);
        if (!isset($res['code'])) {
            $this->addMessage('message', 'api error');
            $this->showMessage();
        }
        if ($res['code'] != 0) {
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
        if ($teacherId == Yii::app()->user->getId()) {
            $staff = $this->staff;
        } else {
            $staff = User::model()->with(array('staffInfo'))->findByPk($teacherId);
        }
        $staffPhoto = empty($staff->staffInfo->staff_photo) ? "blank.jpg" : $staff->staffInfo->staff_photo;
        $teacherInfo = array(
            "uid" => $teacherId,
            "name" => $staff->getName(),
            "photo" => OA::CreateOAUploadUrl('infopub/staff', $staffPhoto),
        );
        $crit = new CDbCriteria();
        $crit->compare('schoolid', $this->branchId);
        $crit->compare('yid', $yid);
        $crit->compare('teacherid', Yii::app()->user->getId());
        $crit->compare('ishelpteacher', 1);
        $showPtcSet = ClassTeacher::model()->exists($crit);
        if(Yii::app()->user->checkAccess('ivystaff_it')){
            $showPtcSet = true;
        }

        $data = array(
            'startYearList' => $this->startYearList,
            'semesterList' => $this->semesterList,
            'startYear' => $this->startYear,
            'semester' => $this->semester,
            'ptcData' => $res['data'],
            'teacherInfo' => $teacherInfo,
            'showPtcSet'=>$showPtcSet
        );
        $this->addMessage('state', "success");
        $this->addMessage('data', $data);
        $this->showMessage();
    }

    // 老师 PTC 数量
    public function actionTeacherPtcNum()
    {
        $startyear = Yii::app()->request->getParam('start_year',$this->startYear);
        $semester = Yii::app()->request->getParam('semester',$this->semester);
        $requestData = array(
            'startyear' => $startyear,
            'semester' => $semester,
            'school_id' => $this->branchId,
        );

        $res = CommonUtils::requestDsOnline('ptc/schedule/teacherPtcNum', $requestData);
        if (!isset($res['code'])) {
            $this->addMessage('message', 'api error');
            $this->showMessage();
        }
        if ($res['code'] != 0) {
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
        $data = array(
            'startYearList' => $this->startYearList,
            'semesterList' => $this->semesterList,
            'startYear' => $this->startYear,
            'semester' => $this->semester,
            'teacherPtcNum' => $res['data'],
        );
        $this->addMessage('state', "success");
        $this->addMessage('data', $data);
        $this->showMessage();
    }

    // 我的 PTC 孩子列表
    public function actionMyPtcChild()
    {
        $teacherId = $this->getTeacherId();

        $requestData = array(
            'startyear' => $this->startYear,
            'semester' => $this->semester,
            'school_id' => $this->branchId,
            'teacher_id' => $teacherId,
        );
        $res = CommonUtils::requestDsOnline('ptc/schedule/myPtcChild', $requestData);
        if (!isset($res['code'])) {
            $this->addMessage('message', 'api error');
            $this->showMessage();
        }
        if ($res['code'] != 0) {
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
        $data = array(
            'startYearList' => $this->startYearList,
            'semesterList' => $this->semesterList,
            'startYear' => $this->startYear,
            'semester' => $this->semester,
            'childList' => $res['data'],
        );
        $this->addMessage('state', "success");
        $this->addMessage('data', $data);
        $this->showMessage();
    }

    // 班级预约总览
    public function actionGetPtcData()
    {
        $classId = Yii::app()->request->getParam('class_id');
        $teacherId = Yii::app()->request->getParam('teacher_id');
        $this->addMessage("state", "fail");
        if (!$classId) {
            $this->addMessage('message', 'class id error');
            $this->showMessage();
        }
        $requestData = array(
            'startyear' => $this->startYear,
            'semester' => $this->semester,
            'school_id' => $this->branchId,
        );
        // 是否指定老师
        if ($teacherId) {
            $requestData['teacher_id'] = $teacherId;
        }
        $res = CommonUtils::requestDsOnline('ptc/schedule/overview/' . $classId, $requestData);
        if (!isset($res['code'])) {
            $this->addMessage('message', 'api error');
            $this->showMessage();
        }
        if ($res['code'] != 0) {
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
        $this->addMessage('state', "success");
        $this->addMessage('data', $res['data']);
        $this->showMessage();
    }

    //小学ptc安排总览 v2
    public function actionPrimarySchoolScheduleOverview()
    {
        $classId = Yii::app()->request->getParam('class_id');
        $this->addMessage("state", "fail");
        if (!$classId) {
            $this->addMessage('message', 'class id error');
            $this->showMessage();
        }
        $requestData = array(
            'startyear' => $this->startYear,
            'semester' => $this->semester,
            'school_id' => $this->branchId,
        );
        $res = CommonUtils::requestDsOnline('ptc/schedule/overview2/' . $classId, $requestData);
        if (!isset($res['code'])) {
            $this->addMessage('message', 'api error');
            $this->showMessage();
        }
        if ($res['code'] != 0) {
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
        $this->addMessage('state', "success");
        $this->addMessage('data', $res['data']);
        $this->showMessage();
    }

    //删除日期及其下属的时间段
    public function actionDelScheduleClass()
    {
        $classId = Yii::app()->request->getParam('class_id');
        $date = Yii::app()->request->getParam('date');
        $this->addMessage("state", "fail");
        if (!$classId) {
            $this->addMessage('message', 'class id error');
            $this->showMessage();
        }
        if(!$date){
            $this->addMessage('message', 'date  error');
            $this->showMessage();
        }
        $requestData = array(
            'startyear' => $this->startYear,
            'semester' => $this->semester,
            'school_id' => $this->branchId,
            'date' => $date,
        );
        $res = CommonUtils::requestDsOnline('ptc/schedule/delScheduleClass/' . $classId, $requestData);
        if (!isset($res['code'])) {
            $this->addMessage('message', 'api error');
            $this->showMessage();
        }
        if ($res['code'] != 0) {
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
        $this->addMessage('state', "success");
        $this->addMessage('data', $res['data']);
        $this->showMessage();
    }

    //Ptc分配学生
    public function actionAddChildToItem()
    {
        $schedule_id = Yii::app()->request->getParam('schedule_id');
        $child_id = Yii::app()->request->getParam('child_id');
        $this->addMessage("state", "fail");
        if (!$schedule_id) {
            $this->addMessage('message', 'schedule id error');
            $this->showMessage();
        }
        if(!$child_id){
            $this->addMessage('message', 'child id error');
            $this->showMessage();
        }
        $requestData = array(
            'startyear' => $this->startYear,
            'semester' => $this->semester,
            'school_id' => $this->branchId,
            'child_id' => $child_id,
        );
        $res = CommonUtils::requestDsOnline('ptc/schedule/addChildToItem/' . $schedule_id, $requestData);
        if (!isset($res['code'])) {
            $this->addMessage('message', 'api error');
            $this->showMessage();
        }
        if ($res['code'] != 0) {
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
        $this->addMessage('state', "success");
        $this->addMessage('data', $res['data']);
        $this->showMessage();
    }

    //设置线上家长会标记
    public function actionSetOnlineChild(){
        $classId = Yii::app()->request->getParam('class_id');
        $status= Yii::app()->request->getParam('status');
        $child_id = Yii::app()->request->getParam('child_id');
        $requestData = array(
            'startyear' => $this->startYear,
            'semester' => $this->semester,
            'school_id' => $this->branchId,
            'child_id' => $child_id,
            'status' => $status,
        );
        $res = CommonUtils::requestDsOnline('ptc/schedule/setOnlineChild/' . $classId, $requestData);
        if (!isset($res['code'])) {
            $this->addMessage('message', 'api error');
            $this->showMessage();
        }
        if ($res['code'] != 0) {
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
        $this->addMessage('state', "success");
        $this->addMessage('data', $res['data']);
        $this->showMessage();
    }

    //保存时间段
    public function actionSaveScheduleClass()
    {
        $classId = Yii::app()->request->getParam('class_id');
        $day = Yii::app()->request->getParam('day');
        $item = Yii::app()->request->getParam('item');
        $this->addMessage("state", "fail");
        if (!$classId) {
            $this->addMessage('message', 'class id error');
            $this->showMessage();
        }
        if(!$day){
            $this->addMessage('message', 'date  error');
            $this->showMessage();
        }
        $requestData = array(
            'startyear' => $this->startYear,
            'semester' => $this->semester,
            'school_id' => $this->branchId,
            'day' => $day,
            'item' => $item,
        );

        $res = CommonUtils::requestDsOnline('ptc/schedule/saveScheduleClass/' . $classId, $requestData);
        if (!isset($res['code'])) {
            $this->addMessage('message', 'api error');
            $this->showMessage();
        }
        if ($res['code'] != 0) {
            $this->addMessage('message', $res['msg']);
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        }
        $this->addMessage('state', "success");
        $this->addMessage('data', $res['data']);
        $this->showMessage();
    }

    //更新时间段
    public function actionUpdateScheduleClass(){
        $classId = Yii::app()->request->getParam('class_id');
        $day = Yii::app()->request->getParam('day');
        $item = Yii::app()->request->getParam('item');
        $del_ids = Yii::app()->request->getParam('del_ids');
        $this->addMessage("state", "fail");
        if (!$classId) {
            $this->addMessage('message', 'class id error');
            $this->showMessage();
        }
        if(!$day){
            $this->addMessage('message', 'date  error');
            $this->showMessage();
        }
        $requestData = array(
            'startyear' => $this->startYear,
            'semester' => $this->semester,
            'school_id' => $this->branchId,
            'day' => $day,
            'item' => $item,
            'del_ids'=>$del_ids,
        );

        $res = CommonUtils::requestDsOnline('ptc/schedule/updateScheduleClass/' . $classId, $requestData);
        if (!isset($res['code'])) {
            $this->addMessage('message', 'api error');
            $this->showMessage();
        }
        if ($res['code'] != 0) {
            $this->addMessage('message', $res['msg']);
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        }
        $this->addMessage('state', "success");
        $this->addMessage('data', $res['data']);
        $this->showMessage();
    }

    //展示一天的时间段数据 展示修改前的数据
    public function actionViewScheduleClass()
    {
        $classId = Yii::app()->request->getParam('class_id');
        $day = Yii::app()->request->getParam('day');
        $this->addMessage("state", "fail");
        if (!$classId) {
            $this->addMessage('message', 'class id error');
            $this->showMessage();
        }
        if(!$day){
            $this->addMessage('message', 'date  error');
            $this->showMessage();
        }
        $requestData = array(
            'startyear' => $this->startYear,
            'semester' => $this->semester,
            'school_id' => $this->branchId,
            'day' => $day,
        );

        $res = CommonUtils::requestDsOnline('ptc/schedule/viewScheduleClass/' . $classId, $requestData);
        if (!isset($res['code'])) {
            $this->addMessage('message', 'api error');
            $this->showMessage();
        }
        if ($res['code'] != 0) {
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
        $this->addMessage('state', "success");
        $this->addMessage('data', $res['data']);
        $this->showMessage();
    }

    //复制时间段展示数据
    public function actionViewCopyScheduleClass()
    {
        $classId = Yii::app()->request->getParam('class_id');
        $day = Yii::app()->request->getParam('day');
        $this->addMessage("state", "fail");
        if (!$classId) {
            $this->addMessage('message', 'class id error');
            $this->showMessage();
        }
        if(!$day){
            $this->addMessage('message', 'date  error');
            $this->showMessage();
        }
        $requestData = array(
            'startyear' => $this->startYear,
            'semester' => $this->semester,
            'school_id' => $this->branchId,
            'day' => $day,
        );

        $res = CommonUtils::requestDsOnline('ptc/schedule/viewCopyScheduleClass/' . $classId, $requestData);
        if (!isset($res['code'])) {
            $this->addMessage('message', 'api error');
            $this->showMessage();
        }
        if ($res['code'] != 0) {
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
        $this->addMessage('state', "success");
        $this->addMessage('data', $res['data']);
        $this->showMessage();
    }

    public function actionSaveCopyScheduleClass()
    {
        $classId = Yii::app()->request->getParam('class_id');
        $day = Yii::app()->request->getParam('day');
        $days = Yii::app()->request->getParam('days');
        $this->addMessage("state", "fail");
        if (!$classId) {
            $this->addMessage('message', 'class id error');
            $this->showMessage();
        }
        if(!$day){
            $this->addMessage('message', 'day  error');
            $this->showMessage();
        }
        if(empty($days)){
            $this->addMessage('message', 'days  error');
            $this->showMessage();
        }
        $requestData = array(
            'startyear' => $this->startYear,
            'semester' => $this->semester,
            'school_id' => $this->branchId,
            'day' => $day,
            'days' => $days,
        );

        $res = CommonUtils::requestDsOnline('ptc/schedule/saveCopyScheduleClass/' . $classId, $requestData);
        if (!isset($res['code'])) {
            $this->addMessage('message', 'api error');
            $this->showMessage();
        }
        if ($res['code'] != 0) {
            $this->addMessage('message', $res['msg']);
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        }
        $this->addMessage('state', "success");
        $this->addMessage('data', $res['data']);
        $this->showMessage();
    }

    //设置ptc地点页面的数据
    public function actionGetLocation()
    {
        $classId = Yii::app()->request->getParam('class_id');
        $day = Yii::app()->request->getParam('day');
        $start = Yii::app()->request->getParam('start');
        $end = Yii::app()->request->getParam('end');
        $this->addMessage("state", "fail");
        if (!$classId) {
            $this->addMessage('message', 'class id error');
            $this->showMessage();
        }
        if(!$day){
            $this->addMessage('message', 'day  error');
            $this->showMessage();
        }

        $requestData = array(
            'startyear' => $this->startYear,
            'semester' => $this->semester,
            'school_id' => $this->branchId,
            'day' => $day,
            'start' => $start,
            'end' => $end,
        );

        $res = CommonUtils::requestDsOnline('ptc/schedule/getLocation/' . $classId, $requestData);
        if (!isset($res['code'])) {
            $this->addMessage('message', 'api error');
            $this->showMessage();
        }
        if ($res['code'] != 0) {
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
        $this->addMessage('state', "success");
        $this->addMessage('data', $res['data']);
        $this->showMessage();
    }

    // 编辑班级对应的学科老师
    public function actionAssignTeacher()
    {
        $classId = Yii::app()->request->getParam('class_id');
        $subjectToteacher = Yii::app()->request->getParam('subject_teacher');

        $this->addMessage("state", "fail");
        if (!$classId) {
            $this->addMessage('message', 'class id error');
            $this->showMessage();
        }
        if (!$subjectToteacher) {
            $this->addMessage('message', 'subjectToteacher error');
            $this->showMessage();
        }
        $requestData = array(
            'startyear' => $this->startYear,
            'semester' => $this->semester,
            'school_id' => $this->branchId,
            'class_id' => $classId,
            'subject_teacher' => $subjectToteacher,
        );
        $res = CommonUtils::requestDsOnline('ptc/schedule/assignTeacher', $requestData);
        if (!isset($res['code'])) {
            $this->addMessage('message', 'api error');
            $this->showMessage();
        }
        if ($res['code'] != 0) {
            $this->addMessage('message', $res['msg']);
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        }
        $this->addMessage('state', "success");
        $this->addMessage('data', $res['data']);
        $this->showMessage();
    }

    // 分配班级可预约的时间
    public function actionAssignSchedule()
    {
        $classId = Yii::app()->request->getParam('class_id');
        $day = Yii::app()->request->getParam('day');
        $start = Yii::app()->request->getParam('start');
        $end = Yii::app()->request->getParam('end');

        $this->addMessage("state", "fail");
        if (!$classId || !$day || !$start || !$end) {
            $this->addMessage('message', 'params error');
            $this->showMessage();
        }

        $requestData = array(
            'startyear' => $this->startYear,
            'semester' => $this->semester,
            'school_id' => $this->branchId,
            'class_id' => $classId,
            'day' => $day,
            'start' => $start,
            'end' => $end,
        );
        $res = CommonUtils::requestDsOnline('ptc/schedule/assignSchedule', $requestData);
        if (!isset($res['code'])) {
            $this->addMessage('message', 'api error');
            $this->showMessage();
        }
        if ($res['code'] != 0) {
            $this->addMessage('message', $res['msg']);
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        }
        $this->addMessage('state', "success");
        $this->addMessage('data', $res['data']);
        $this->showMessage();
    }

    // 删除班级分配的时间
    public function actionDeleteSchedule()
    {
        $id = Yii::app()->request->getParam('id');

        $this->addMessage("state", "fail");
        if (!$id) {
            $this->addMessage('message', 'params error');
            $this->showMessage();
        }

        $requestData = array(
            'startyear' => $this->startYear,
            'semester' => $this->semester,
            'school_id' => $this->branchId
        );
        $res = CommonUtils::requestDsOnline('ptc/schedule/deleteSchedule/' . $id, $requestData);
        if (!isset($res['code'])) {
            $this->addMessage('message', 'api error');
            $this->showMessage();
        }
        if ($res['code'] != 0) {
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
        $this->addMessage('state', "success");
        $this->addMessage('data', $res['data']);
        $this->showMessage();
    }

    // 批量修改，班级学科老师对应预约时间的地点
    public function actionAssignLocation()
    {
        $classId = Yii::app()->request->getParam('class_id');
        $subjectLocation = Yii::app()->request->getParam('subject_location');
        $subjectOnlineLink = Yii::app()->request->getParam('subject_online_link');
        $scheduleIds = Yii::app()->request->getParam('schedule_ids');

        $this->addMessage("state", "fail");
        if (!$classId) {
            $this->addMessage('message', 'class id error');
            $this->showMessage();
        }
        if (!$subjectLocation) {
            $this->addMessage('message', 'subject location error');
            $this->showMessage();
        }
        if (!$scheduleIds) {
            $this->addMessage('message', 'schedule ids error');
            $this->showMessage();
        }
        $requestData = array(
            'startyear' => $this->startYear,
            'semester' => $this->semester,
            'school_id' => $this->branchId,
            'class_id' => $classId,
            'subject_location' => $subjectLocation,
            'subject_online_link' => $subjectOnlineLink,
            'schedule_ids' => $scheduleIds,
        );
        $res = CommonUtils::requestDsOnline('ptc/schedule/assignLocation', $requestData);
        if (!isset($res['code'])) {
            $this->addMessage('message', 'api error');
            $this->showMessage();
        }
        if ($res['code'] != 0) {
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
        $this->addMessage('state', "success");
        $this->addMessage('data', $res['data']);
        $this->showMessage();
    }

    // 获取班级科目设置的地点
    public function actionLocationData()
    {
        $classId = Yii::app()->request->getParam('class_id');
        $day = Yii::app()->request->getParam('day');
        $start = Yii::app()->request->getParam('start');
        $end = Yii::app()->request->getParam('end');

        $this->addMessage("state", "fail");
        if (!$classId || !$day || !$start || !$end) {
            $this->addMessage('message', 'params error');
            $this->showMessage();
        }

        $requestData = array(
            'startyear' => $this->startYear,
            'semester' => $this->semester,
            'school_id' => $this->branchId,
            'class_id' => $classId,
            'day' => $day,
            'start' => $start,
            'end' => $end,
        );
        $res = CommonUtils::requestDsOnline('ptc/schedule/locationData', $requestData);
        if (!isset($res['code'])) {
            $this->addMessage('message', 'api error');
            $this->showMessage();
        }
        if ($res['code'] != 0) {
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
        $this->addMessage('state', "success");
        $this->addMessage('data', $res['data']);
        $this->showMessage();
    }

    // 获取班级分配的老师
    public function actionTeacherList()
    {
        $classId = Yii::app()->request->getParam('class_id');

        $this->addMessage("state", "fail");
        if (!$classId) {
            $this->addMessage('message', 'class id not null');
            $this->showMessage();
        }

        $requestData = array(
            'startyear' => $this->startYear,
            'semester' => $this->semester,
            'school_id' => $this->branchId,
            'class_id' => $classId
        );
        $res = CommonUtils::requestDsOnline('ptc/schedule/teacherList/' . $classId, $requestData);
        if (!isset($res['code'])) {
            $this->addMessage('message', 'api error');
            $this->showMessage();
        }
        if ($res['code'] != 0) {
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
        $this->addMessage('state', "success");
        $this->addMessage('data', $res['data']);
        $this->showMessage();
    }

    // 中学已分配数据
    public function actionAssign()
    {
        $this->branchSelectParams['urlArray'] = array('//mteaching/ptcSchedule/assign');
        $requestData = array(
            'startyear' => $this->startYear,
            'semester' => $this->semester,
            'school_id' => $this->branchId,
            'day' => Yii::app()->request->getParam('day'),
        );
        $res = CommonUtils::requestDsOnline('ptc/schedule/assignChildData', $requestData);
        $data = array();
        if ($res['code'] == 0) {
            $data = $res['data'];
        }
        $opAuth = Yii::app()->user->checkAccess('ivystaff_opschool');
        $this->render('/ptc/assign', array('data' => $data, 'opAuth' => $opAuth));
    }

    // 分配学生给老师
    public function actionAssignChild()
    {
        $teacherId = Yii::app()->request->getParam('teacher_id');
        $childId = Yii::app()->request->getParam('child_id');
        $day = Yii::app()->request->getParam('day');
        $start = Yii::app()->request->getParam('start');
        $end = Yii::app()->request->getParam('end');

        $this->addMessage("state", "fail");
        if (!$teacherId || !$childId || !$day || !$start || !$end) {
            $this->addMessage('message', 'params error');
            $this->showMessage();
        }
        $this->addMessage("state", "fail");


        $requestData = array(
            'startyear' => $this->startYear,
            'semester' => $this->semester,
            'school_id' => $this->branchId,
            'teacher_id' => $teacherId,
            'child_id' => $childId,
            'day' => $day,
            'start' => $start,
            'end' => $end,
        );
        $res = CommonUtils::requestDsOnline('ptc/schedule/assignChild', $requestData);
        if (!isset($res['code'])) {
            $this->addMessage('message', 'api error');
            $this->showMessage();
        }
        if ($res['code'] != 0) {
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
        $this->addMessage('state', "success");
        $this->addMessage('data', $res['data']);
        $this->showMessage();
    }

    // 批量修改，中学老师的会谈地点
    public function actionAssignTeacherLocation()
    {
        $classId = Yii::app()->request->getParam('class_id');
        $location = Yii::app()->request->getParam('location');
        $onlineLink = Yii::app()->request->getParam('online_link');
        $scheduleIds = Yii::app()->request->getParam('schedule_ids');
        $teacherId = Yii::app()->request->getParam('teacher_id');

        $this->addMessage("state", "fail");
        if (!$teacherId) {
            $this->addMessage('message', 'teacher id error');
            $this->showMessage();
        }
        if (!$location) {
            $this->addMessage('message', 'subject location error');
            $this->showMessage();
        }
        if (!$scheduleIds) {
            $this->addMessage('message', 'schedule ids error');
            $this->showMessage();
        }
        $requestData = array(
            'startyear' => $this->startYear,
            'semester' => $this->semester,
            'school_id' => $this->branchId,
            'location' => $location,
            'online_link' => $onlineLink,
            'schedule_ids' => $scheduleIds,
            'teacher_id' => $teacherId,
        );
        $res = CommonUtils::requestDsOnline('ptc/schedule/assignTeacherLocation', $requestData);
        if (!isset($res['code'])) {
            $this->addMessage('message', 'api error');
            $this->showMessage();
        }
        if ($res['code'] != 0) {
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
        $this->addMessage('state', "success");
        $this->addMessage('data', $res['data']);
        $this->showMessage();
    }

    // 保存开放时间
    public function actionPtcInit()
    {
        $dept = Yii::app()->request->getParam('dept');
        $start = Yii::app()->request->getParam('p_schedule_start'); // 家长预约时间起始（ES用）
        $end = Yii::app()->request->getParam('p_schedule_end'); // 家长预约时间截止（ES用）
        $viewFrom = Yii::app()->request->getParam('p_view_from'); // 从何时起开放给家长浏览 （SS用）
        $studentFrom = Yii::app()->request->getParam('froze_student_from'); // 从何时起冻结学生名单，禁止增减学生 （SS用）

        $this->addMessage("state", "fail");
        if ($dept == 1) {
            if (!$start || !$end) {
                $this->addMessage('message', 'params error');
                $this->showMessage();
            }
        } elseif ($dept == 2) {
            if (!$viewFrom) {
                $this->addMessage('message', 'params error');
                $this->showMessage();
            }
        } else {
            $this->addMessage('message', 'params error');
            $this->showMessage();
        }


        $requestData = array(
            'startyear' => $this->startYear,
            'semester' => $this->semester,
            'school_id' => $this->branchId,
            'dept' => $dept,
            'p_schedule_start' => $start,
            'p_schedule_end' => $end,
            'p_view_from' => $viewFrom,
            'froze_student_from' => $studentFrom,
        );
        $res = CommonUtils::requestDsOnline('ptc/schedule/ptcInit', $requestData);
        if (!isset($res['code'])) {
            $this->addMessage('message', 'api error');
            $this->showMessage();
        }
        if ($res['code'] != 0) {
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
        $this->addMessage('state', "success");
        $this->addMessage('data', $res['data']);
        $this->showMessage();
    }

    // 数学成绩详情
    public function actionMathDetail()
    {
        $scoreExt = Yii::app()->request->getParam('score_ext');
        $this->addMessage("state", "fail");
        if (!$scoreExt) {
            $this->addMessage('message', 'params error');
            $this->showMessage();
        }
        $this->addMessage("state", "fail");

        $requestData = array(
            'startyear' => $this->startYear,
            'semester' => $this->semester,
            'school_id' => $this->branchId,
            'score_ext' => $scoreExt,
        );
        $res = CommonUtils::requestDsOnline('ptc/schedule/mathDetail', $requestData);
        if (!isset($res['code'])) {
            $this->addMessage('message', 'api error');
            $this->showMessage();
        }
        if ($res['code'] != 0) {
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
        $this->addMessage('state', "success");
        $this->addMessage('data', $res['data']);
        $this->showMessage();
    }

    // 中学家长会计划保存
    public function actionSchedulePlanSave()
    {
        $planId = Yii::app()->request->getParam('plan_id');
        $this->addMessage("state", "fail");
        if (!$planId) {
            $this->addMessage('message', 'params error');
            $this->showMessage();
        }

        $requestData = array(
            'startyear' => $this->startYear,
            'semester' => $this->semester,
            'school_id' => $this->branchId,
            'plan_id' => $planId,
        );
        $res = CommonUtils::requestDsOnline('ptc/schedule/schedulePlanSave', $requestData);
        if (!isset($res['code'])) {
            $this->addMessage('message', 'api error');
            $this->showMessage();
        }
        if ($res['code'] != 0) {
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
        $this->addMessage('state', "success");
        $this->addMessage('data', $res['data']);
        $this->showMessage();
    }

    // 中学家长会计划清除
    public function actionSchedulePlanClean()
    {
        $this->addMessage("state", "fail");

        $requestData = array(
            'startyear' => $this->startYear,
            'semester' => $this->semester,
            'school_id' => $this->branchId,
            'day' => Yii::app()->request->getParam('day'),
        );
        $res = CommonUtils::requestDsOnline('ptc/schedule/schedulePlanClean', $requestData);
        if (!isset($res['code'])) {
            $this->addMessage('message', 'api error');
            $this->showMessage();
        }
        if ($res['code'] != 0) {
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
        $this->addMessage('state', "success");
        $this->addMessage('data', $res['data']);
        $this->showMessage();
    }

    // 重置学生的预约
    public function actionResetStudentPtc()
    {
        $this->addMessage("state", "fail");
        $dept = Yii::app()->request->getParam('dept');
        $childId = Yii::app()->request->getParam('child_id');
        $class_id = Yii::app()->request->getParam('class_id');
        if (!$dept || !$childId) {
            $this->addMessage('message', 'params error');
            $this->showMessage();
        }
        $requestData = array(
            'startyear' => $this->startYear,
            'semester' => $this->semester,
            'school_id' => $this->branchId,
            'dept' => $dept,
            'child_id' => $childId,
            'class_id' => $class_id,
        );
        $res = CommonUtils::requestDsOnline('ptc/schedule/resetStudentPtc', $requestData);
        if (!isset($res['code'])) {
            $this->addMessage('message', 'api error');
            $this->showMessage();
        }
        if ($res['code'] != 0) {
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
        $this->addMessage('state', "success");
        $this->addMessage('data', $res['data']);
        $this->showMessage();
    }

    
    // 重置学生的预约
    public function actionTeacherClassSchedule()
    {
        $this->addMessage("state", "fail");
        $teacherId = Yii::app()->request->getParam('teacher_id', Yii::app()->user->getId());
        if (!$teacherId) {
            $this->addMessage('message', 'teacher id error');
            $this->showMessage();
        }
        $requestData = array(
            'startyear' => $this->startYear,
            'semester' => $this->semester,
            'school_id' => $this->branchId,
            'teacher_id' => $teacherId,
        );
        $res = CommonUtils::requestDsOnline('ptc/schedule/teacherClassSchedule', $requestData);
        if (!isset($res['code'])) {
            $this->addMessage('message', 'api error');
            $this->showMessage();
        }
        if ($res['code'] != 0) {
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
        $this->addMessage('state', "success");
        $this->addMessage('data', $res['data']);
        $this->showMessage();
    }

    public function actionSetAttendStatus()
    {
        $this->addMessage("state", "fail");
        $child_id = Yii::app()->request->getParam('child_id');
        $day = Yii::app()->request->getParam('day');
        $start = Yii::app()->request->getParam('start');
        $attended = Yii::app()->request->getParam('attended');
        $classId = Yii::app()->request->getParam('class_id');
        if (!$child_id) {
            $this->addMessage('message', 'teacher id error');
            $this->showMessage();
        }
        $requestData = array(
            'startyear' => $this->startYear,
            'semester' => $this->semester,
            'school_id' => $this->branchId,
            'child_id' => $child_id,
            'day'=>$day,
            'start'=>$start,
            'attended'=>$attended,
        );
        $res = CommonUtils::requestDsOnline('ptc/schedule/setAttendStatus/'.$classId, $requestData);
        if (!isset($res['code'])) {
            $this->addMessage('message', 'api error');
            $this->showMessage();
        }
        if ($res['code'] != 0) {
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
        $this->addMessage('state', "success");
        $this->addMessage('data', $res['data']);
        $this->showMessage();
    }

    // 无面谈学生
    public function actionStudentsWithoutPtc()
    {
        $this->addMessage("state", "fail");
        $dept = Yii::app()->request->getParam('dept', 2);
        if (!$dept) {
            $this->addMessage('message', 'teacher id error');
            $this->showMessage();
        }
        $requestData = array(
            'startyear' => $this->startYear,
            'semester' => $this->semester,
            'school_id' => $this->branchId,
            'dept' => $dept,
        );
        $res = CommonUtils::requestDsOnline('ptc/schedule/studentsWithoutPtc', $requestData);
        if (!isset($res['code'])) {
            $this->addMessage('message', 'api error');
            $this->showMessage();
        }
        if ($res['code'] != 0) {
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
        $this->addMessage('state', "success");
        $this->addMessage('data', $res['data']);
        $this->showMessage();
    }

    // 重置学生的预约
    public function actionGetPtcScheduleDay()
    {
        $this->addMessage("state", "fail");
        $dept = Yii::app()->request->getParam('dept');
        if (!$dept) {
            $this->addMessage('message', 'params error');
            $this->showMessage();
        }
        $requestData = array(
            'startyear' => $this->startYear,
            'semester' => $this->semester,
            'school_id' => $this->branchId,
            'dept' => $dept,
        );
        $res = CommonUtils::requestDsOnline('ptc/schedule/getPtcScheduleDay', $requestData);
        if (!isset($res['code'])) {
            $this->addMessage('message', 'api error');
            $this->showMessage();
        }
        if ($res['code'] != 0) {
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
        $this->addMessage('state', "success");
        $this->addMessage('data', $res['data']);
        $this->showMessage();
    }

    //ptc2.0 小学导出访客提示日期
    public function actionGetPtcScheduleDay2()
    {
        $this->addMessage("state", "fail");
        $class_id = Yii::app()->request->getParam('class_id');
        if (!$class_id) {
            $this->addMessage('message', 'class_id error');
            $this->showMessage();
        }
        $requestData = array(
            'startyear' => $this->startYear,
            'semester' => $this->semester,
            'school_id' => $this->branchId,
            'dept' => 1,
            'class_id' => $class_id,
        );
        $res = CommonUtils::requestDsOnline('ptc/schedule/getPtcScheduleDay2', $requestData);
        if (!isset($res['code'])) {
            $this->addMessage('message', 'api error');
            $this->showMessage();
        }
        if ($res['code'] != 0) {
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
        $this->addMessage('state', "success");
        $this->addMessage('data', $res['data']);
        $this->showMessage();
    }




    public function actionGetStudentsByDate()
    {
        $this->addMessage("state", "fail");
        $dept = Yii::app()->request->getParam('dept');
        $date = Yii::app()->request->getParam('date');
        if (!$dept || !$date) {
            $this->addMessage('message', 'params error');
            $this->showMessage();
        }
        $requestData = array(
            'startyear' => $this->startYear,
            'semester' => $this->semester,
            'school_id' => $this->branchId,
            'dept' => $dept,
            'date' => $date,
        );
        $res = CommonUtils::requestDsOnline('ptc/schedule/getStudentsByDate', $requestData);
        if (!isset($res['code'])) {
            $this->addMessage('message', 'api error');
            $this->showMessage();
        }
        if ($res['code'] != 0) {
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
        $this->addMessage('state', "success");
        $this->addMessage('data', $res['data']);
        $this->showMessage();
    }

    //ptc2.0导出学生到访数据
    public function actionGetStudentsByDate2()
    {
        $this->addMessage("state", "fail");
        $class_id = Yii::app()->request->getParam('class_id');
        if(empty($class_id)){
            $this->addMessage('message', 'class_id error');
            $this->showMessage();
        }
        $requestData = array(
            'startyear' => $this->startYear,
            'semester' => $this->semester,
            'school_id' => $this->branchId,
            'dept' => 1,
            'class_id' => $class_id,
        );
        $res = CommonUtils::requestDsOnline('ptc/schedule/getStudentsByDate2', $requestData);
        if (!isset($res['code'])) {
            $this->addMessage('message', 'api error');
            $this->showMessage();
        }
        if ($res['code'] != 0) {
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
        $this->addMessage('state', "success");
        $this->addMessage('data', $res['data']);
        $this->showMessage();
    }

    public function getTeacherId()
    {
        // 所有人可看其他老师数据
        return Yii::app()->request->getParam('teacher_id', Yii::app()->user->getId());

        // if ($this->manageAccess) {
        //     return Yii::app()->request->getParam('teacher_id', Yii::app()->user->getId());
        // }
        // return Yii::app()->user->getId();
    }
}
