<?php
$category = $_GET['category'];
if(empty($category)){
    $category = 'leaveOvertime';
}
?>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li class="active"><?php echo Yii::t('leave','Leave Application');?></li>
    </ol>
    <div class="row">
        <?php
        $col = 'col-md-12';
        $col = 'col-md-10';
        echo '<div class="col-md-2">';
        $mainMenu = array(
            array('label'=>Yii::t('leave','Leave Application'), 'url'=>array("//mcampus/leave/index","category"=>"leaveOvertime")),
            array('label'=>Yii::t('leave','Attendance Calendar'), 'url'=>array("//mcampus/leave/index","category"=>"calendar")),
        );

        if ($this->checkOpRole()) {
            $mainMenu = array(
                array('label'=>Yii::t('leave','Leave Application/Out of Office'), 'url'=>array("//mcampus/leave/index","category"=>"leaveOvertime")),
                array('label'=>Yii::t('leave','Attendance Calendar'), 'url'=>array("//mcampus/leave/index","category"=>"calendar")),
                array('label'=>Yii::t('user','假期配额及余额'), 'url'=>array("//mcampus/leave/index","category"=>"balance")),
            );
        }
        if ($this->checkRole()) {
            $mainMenu = array(
                array('label'=>Yii::t('leave','Leave Application/Out of Office'), 'url'=>array("//mcampus/leave/index","category"=>"leaveOvertime")),
                array('label'=>Yii::t('leave','Attendance Calendar'), 'url'=>array("//mcampus/leave/index","category"=>"calendar")),
                array('label'=>Yii::t('user','考勤结算'), 'url'=>array("//mcampus/leave/index","category"=>"settle"),'active' => in_array($category,array('settle','settlement'))),
                array('label'=>Yii::t('user','考勤组管理'), 'url'=>array("//mcampus/reportingLine/index","type"=>"leave"),'linkOptions'=>array('target'=>'_blank')),
                array('label'=>Yii::t('user','假期配额及余额'), 'url'=>array("//mcampus/leave/index","category"=>"balance")),
            );
        }
        $this->widget('zii.widgets.CMenu',array(
            'id' => 'user-profile-item',
            'items'=> $mainMenu,
            'htmlOptions'=>array('class'=>'nav nav-pills nav-stacked text-right background-gray'),
            'activeCssClass'=>'active',
            'itemCssClass'=>''
        ));
        echo '</div>';
        ?>
        <div class="<?php echo $col; ?>" id="main-edit-zone">
            <?php
            $roleCategory = array();
            foreach ($mainMenu as $key => $value) {
                $roleCategory[] = $value['url']['category'];
            }
            if (in_array("settle", $roleCategory)) {
                $roleCategory[] = 'settlement';
            }
            if (!in_array($category, $roleCategory)) {
                $category = 'leaveOvertime';
            }
            $this->renderPartial($category,array('yearList'=>$yearList));
            ?>
        </div>
    </div>
</div>
<?php
    $this->renderPartial('//layouts/common/branchSelectBottom');
?>
<style>
    .img42{
        width: 42px;
        height: 42px;
        object-fit: cover;
        border-radius:50%
    }
    
    [v-cloak]{
        display:none;
    }
    .scroll-box::-webkit-scrollbar-thumb {
        /*滚动条里面小方块*/
        border-radius   : 10px;
        background-color: #ccc;
        background-image: none
    }
    .scroll-box::-webkit-scrollbar-track {
        /*滚动条里面轨道*/
        box-shadow   : inset 0 0 5px rgba(0, 0, 0, 0);
        background   : #fff;
        border-radius: 10px;
        border:none
    }
    .fileLink{
        background: #F7F7F8;
        border-radius: 4px;
        border: 1px solid #EBEDF0;
        padding: 6px 12px;
        font-size: 14px;
        margin-right: 16px;
        margin-bottom:8px;
        line-height:20px
    }
    .fileLink img{
        width:20px;
        height:20px
    }
    .imgLi{
        list-style: none;
        padding-left: 0;
    }
    .imgLi li{
        display:inline-block
    }
    .inputStyle{
        width:100%;
        border:none;
        border-bottom:1px solid #ccc
    }
</style>
<script>
     $(document).ready(function () {
    // 通过该方法来为每次弹出的模态框设置最新的zIndex值，从而使最新的modal显示在最前面
        $(document).on('shown.bs.modal', '.modal.in', function(event) {
            var zIndex = 1040 + (10 * $('.modal:visible').length);
                $(this).css('z-index', zIndex);
                setTimeout(function() {
                    $('.modal-backdrop').not('.modal-stack').css('z-index', zIndex - 1).addClass('modal-stack');
                }, 0);
        }).on('hidden.bs.modal', '.modal', function(event) {
            if ($('.modal.in').size() >= 1) {
                $('body').addClass('modal-open')
            }
        })
    });
    $(function(){
        $('#main-edit-zone').find('.edit-title').html($('#user-profile-item li.active a').html());
    })
</script>