(function(b){var c={};function a(j,f,m,l,d){var n,h,g,i;h=google.gears.factory.create("beta.canvas");try{h.decode(j);i=Math.min(f/h.width,m/h.height);if(i<1){h.resize(Math.round(h.width*i),Math.round(h.height*i));return h.encode(d,{quality:l/100})}}catch(k){}return j}b.runtimes.Gears=b.addRuntime("gears",{getFeatures:function(){return{dragdrop:true,jpgresize:true,pngresize:true,chunks:true,progress:true,multipart:true}},init:function(g,i){var h;if(!window.google||!google.gears){return i({success:false})}try{h=google.gears.factory.create("beta.desktop")}catch(f){return i({success:false})}function d(k){var j,e,l=[],m;for(e=0;e<k.length;e++){j=k[e];m=b.guid();c[m]=j.blob;l.push(new b.File(m,j.name,j.blob.length))}g.trigger("FilesAdded",l)}g.bind("PostInit",function(){var j=g.settings,e=document.getElementById(j.drop_element);if(e){b.addEvent(e,"dragover",function(k){h.setDropEffect(k,"copy");k.preventDefault()});b.addEvent(e,"drop",function(l){var k=h.getDragData(l,"application/x-gears-files");if(k){d(k.files)}l.preventDefault()});e=0}b.addEvent(document.getElementById(j.browse_button),"click",function(o){var n=[],l,k,m;o.preventDefault();for(l=0;l<j.filters.length;l++){m=j.filters[l].extensions.split(",");for(k=0;k<m.length;k++){n.push("."+m[k])}}h.openFiles(d,{singleFile:!j.multi_selection,filter:n})})});g.bind("UploadFile",function(o,l){var q=0,p,m,n=0,k=o.settings.resize,e;if(k&&/\.(png|jpg|jpeg)$/i.test(l.name)){c[l.id]=a(c[l.id],k.width,k.height,k.quality||90,/\.png$/i.test(l.name)?"image/png":"image/jpeg")}l.size=c[l.id].length;m=o.settings.chunk_size;e=m>0;p=Math.ceil(l.size/m);if(!e){m=l.size;p=1}function j(){var v,x,s=o.settings.multipart,r=0,w={name:l.target_name||l.name},t=o.settings.url;function u(z){var y,E="----pluploadboundary"+b.guid(),B="--",D="\r\n",A,C;if(s){v.setRequestHeader("Content-Type","multipart/form-data; boundary="+E);y=google.gears.factory.create("beta.blobbuilder");b.each(b.extend(w,o.settings.multipart_params),function(G,F){y.append(B+E+D+'Content-Disposition: form-data; name="'+F+'"'+D+D);y.append(G+D)});C=b.mimeTypes[l.name.replace(/^.+\.([^.]+)/,"$1")]||"application/octet-stream";y.append(B+E+D+'Content-Disposition: form-data; name="'+o.settings.file_data_name+'"; filename="'+l.name+'"'+D+"Content-Type: "+C+D+D);y.append(z);y.append(D+B+E+B+D);A=y.getAsBlob();r=A.length-z.length;z=A}v.send(z)}if(l.status==b.DONE||l.status==b.FAILED||o.state==b.STOPPED){return}if(e){w.chunk=q;w.chunks=p}x=Math.min(m,l.size-(q*m));if(!s){t=b.buildUrl(o.settings.url,w)}v=google.gears.factory.create("beta.httprequest");v.open("POST",t);if(!s){v.setRequestHeader("Content-Disposition",'attachment; filename="'+l.name+'"');v.setRequestHeader("Content-Type","application/octet-stream")}b.each(o.settings.headers,function(z,y){v.setRequestHeader(y,z)});v.upload.onprogress=function(y){l.loaded=n+y.loaded-r;o.trigger("UploadProgress",l)};v.onreadystatechange=function(){var y;if(v.readyState==4){if(v.status==200){y={chunk:q,chunks:p,response:v.responseText,status:v.status};o.trigger("ChunkUploaded",l,y);if(y.cancelled){l.status=b.FAILED;return}n+=x;if(++q>=p){l.status=b.DONE;o.trigger("FileUploaded",l,{response:v.responseText,status:v.status})}else{j()}}else{o.trigger("Error",{code:b.HTTP_ERROR,message:"HTTP Error.",file:l,chunk:q,chunks:p,status:v.status})}}};if(q<p){u(c[l.id].slice(q*m,x))}}j()});i({success:true})}})})(plupload);