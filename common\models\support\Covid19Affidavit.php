<?php

/**
 * This is the model class for table "ivy_covid19_affidavit".
 *
 * The followings are the available columns in table 'ivy_covid19_affidavit':
 * @property integer $id
 * @property string $parent_name
 * @property string $parent_relationship
 * @property integer $parent_tel
 * @property string $type
 * @property string $destination
 * @property integer $return_date
 * @property string $signature
 * @property string $status
 * @property integer $updated_at
 * @property integer $updated_by
 */
class Covid19Affidavit extends CActiveRecord
{
	public $filedata;
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_covid19_affidavit';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array(' parent_name, status, parent_relationship, parent_tel, type, filedata, updated_at, updated_by', 'required'),
			array('return_date, status, updated_at, updated_by', 'numerical', 'integerOnly'=>true),
			array('parent_name, parent_tel, destination, signature', 'length', 'max'=>255),
			array('parent_relationship, type', 'length', 'max'=>1),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, parent_name, parent_relationship, parent_tel, type, destination, return_date, signature, status, updated_at, updated_by', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'parent_name' => Yii::t('event','Parent Name'),
			'parent_relationship' => Yii::t('asa','家长关系'),
			'parent_tel' => Yii::t('asa','家长电话'),
			'type' => Yii::t('event','Stat'),
			'destination' => Yii::t('asa','目的地'),
			'return_date' => Yii::t('asa','返回日期'),
			'signature' => Yii::t('event','Signature'),
			'status' => 'status',
			'updated_at' => 'Updated At',
			'updated_by' => 'Updated By',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('parent_name',$this->parent_name,true);
		$criteria->compare('parent_relationship',$this->parent_relationship,true);
		$criteria->compare('parent_tel',$this->parent_tel);
		$criteria->compare('type',$this->type,true);
		$criteria->compare('destination',$this->destination,true);
		$criteria->compare('return_date',$this->return_date);
		$criteria->compare('signature',$this->signature,true);
		$criteria->compare('status',$this->status);
		$criteria->compare('updated_at',$this->updated_at);
		$criteria->compare('updated_by',$this->updated_by);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return Covid19Affidavit the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	public function uploadFile()
	{
		// 判断是否为 base64 图片
		if (strpos($this->filedata, 'base64') === false) {
			return true;
		}
		$data = base64_decode(preg_replace('#^data:image/\w+;base64,#i', '', $this->filedata));

		$normalDir = rtrim(Yii::app()->params['xoopsVarPath'], '/') .'/';

		$fileName = uniqid() . '.png';

		$filePath = $normalDir . $fileName;
		if (file_put_contents($filePath, $data)) {
			$this->signature = $fileName;
            if ($this->signature) {
                // 上传到 OSS
                $objectPath = 'covid19/';
                $oss = CommonUtils::initOSS('private');
                if ($oss->uploadFile($objectPath . $this->signature, $filePath)) {
                    unlink($filePath);
                    return true;
                }
            }
		}
		return false;
	}


    public static function getRelationship()
    {
        $relationship = array(
            1 => Yii::t('site','Father'),
            2 => Yii::t('site','Mother'),
            3 => Yii::t('site','Legal Guardian'),
        );
        return $relationship;
    }
}
