<script>
<?php if(Yii::app()->user->checkAccess('tSuperAdmin')):?>
$('ul.constant-list li span.constant').bind('dblclick',function(){
	childid=$(this).parent().attr('childid');
	id='constant-'+childid;
	oldData =$(this).html();
	if (oldData.indexOf("input")>=0){
        return;
    }
	$(this).html("<input class='input' id='"+id+"' type='text' size='6' childid=" + $(this).parent().attr('childid')+" value="+$(this).html()+">");
	$("#"+id).focus().blur(function(){
		if(parseInt(oldData) == parseInt($("#"+id).val())){
			$("#"+id).parent().html(oldData);
		}else{
			$.ajax({
				type: "POST",   
				url: "<?php echo $this->createUrl('//child/list/processConstant');?>",
				data: "childid="+childid+"&value="+$("#"+id).val(),
				dataType: "json",
				success: function(data){
					if(data.state == 'success'){
						$("#"+id).parent().html(data.data);
					}else{
						alert(data.message);
						$("#"+id).parent().html(oldData);
					}
				}   
			});		
		}
	});
	$("#"+id).keydown(function(e){
		if(e.keyCode==13){
		   $(this).blur();
		}
	});
})
<?php endif;?>

$('.J_cancelbinding').on('click', function(){
    if ( confirm('确定取消') ){
        var url = $(this).attr('href');
        $.ajax({
            type: 'POST',
            url: url,
            dataType: 'json',
            success: function(data){
                if (data.state === 'success'){
                    reloadPage(window);
                }
                else {
                    alert(data.message);
                }
            }
        });
    }
    return false;
});

function removeElement(data){
	var obj = $('ul.constant-list li[childid|="'+data.childId+'"]').find('span');
	for (var i=2; i<obj.length; i++){
		obj[i].remove();
	}
}
</script>