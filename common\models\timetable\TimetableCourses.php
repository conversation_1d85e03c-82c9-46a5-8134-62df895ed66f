<?php

/**
 * This is the model class for table "ivy_timetable_courses".
 *
 * The followings are the available columns in table 'ivy_timetable_courses':
 * @property integer $id
 * @property string $report_course_id
 * @property string $program
 * @property string $course_code
 * @property string $title_cn
 * @property string $title_en
 * @property integer $status
 * @property integer $weight
 * @property integer $created_at
 * @property integer $created_by
 * @property integer $updated_at
 * @property integer $updated_by
 */
class   TimetableCourses extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_timetable_courses';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('program, course_code, title_cn, status, weight, created_at, created_by, updated_at, updated_by', 'required'),
			array('status, weight, created_at, created_by, updated_at, updated_by, report_course_id', 'numerical', 'integerOnly'=>true),
			array('program, course_code, title_cn, title_en', 'length', 'max'=>255),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, program, course_code, title_cn, title_en, status, weight, created_at, created_by, updated_at, updated_by, report_course_id', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
			'desc' => array(self::HAS_ONE, 'TimetableProgramDescriptions', 'program'),
			'alias' => array(self::HAS_ONE, 'TimetableCoursesAlias', array('course_code' => 'course_code')),
			'courseData' => array(self::HAS_MANY, 'TimetableCourseData', array('course_id' => 'id')),
			'teacher' => array(self::HAS_ONE, 'TimetableCourseTeacher', 'course_id', 'on' => 'teacher.status=1'),
			'reportCourse' => array(self::HAS_ONE, 'AchievementReportCourse', array('id' => 'report_course_id')),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'program' => 'Program',
			'course_code' => 'Course Code',
			'title_cn' => 'Title Cn',
			'title_en' => 'Title En',
			'status' => 'Status',
			'weight' => 'Weight',
			'created_at' => 'Created At',
			'created_by' => 'Created By',
			'updated_at' => 'Updated At',
			'updated_by' => 'Updated By',
			'report_course_id' => 'report_course_id',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following course_code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('program',$this->program,true);
		$criteria->compare('course_code',$this->course_code,true);
		$criteria->compare('title_cn',$this->title_cn,true);
		$criteria->compare('title_en',$this->title_en,true);
		$criteria->compare('status',$this->status);
		$criteria->compare('weight',$this->weight);
		$criteria->compare('created_at',$this->created_at);
		$criteria->compare('created_by',$this->created_by);
		$criteria->compare('updated_at',$this->updated_at);
		$criteria->compare('updated_by',$this->updated_by);
		$criteria->compare('report_course_id',$this->report_course_id);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return TimetableCourses the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

    public function getTitle()
    {
        switch (Yii::app()->language) {
            case "zh_cn":
                return  $this->title_cn ;
                break;
            case "en_us":
                return  $this->title_en;
                break;
        }
    }

    public function getAliasTitle($tid = 1)
    {
        // 获取课程别名
        $criteria = new CDbCriteria();
        $criteria->compare('tid', $tid);
        $criteria->compare('course_code', $this->course_code);
        $alias = TimetableCoursesAlias::model()->find($criteria);
        $titleCn = $this->title_cn;
        $titleEn = $this->title_en;
        if ($alias) {
        	if ($alias->alias_cn) {
        		$titleCn .= "（" .$alias->alias_cn. "）";
        	}
        	if ($alias->alias_en) {
        		$titleEn .= " (" .$alias->alias_en. ")	";
        	}
        }

        switch (Yii::app()->language) {
            case "zh_cn":
                return  $titleCn;
                break;
            case "en_us":
                return  $titleEn;
                break;
        }
    }

//    static public function getCourseType()
//    {
//    	$type = array(
//    		'01' => 'Language & Literature',
//    		'02' => 'Mathematics',
//    		'03' => 'Sciences',
//    		'04' => 'Individuals & Societies',
//    		'05' => 'Arts',
//    		'06' => 'Language Acquisition',
//    		'08' => 'Physical & Health Education',
//    		'21' => 'Design',
//    		'22' => 'Homeroom Misc',
//    		'100' => 'Other',
//    	);
//
//    	return $type;
//    }

    static public function getCourseType()
    {
        $type = array(
            '01' => '01',
            '02' => '02',
            '03' => '03',
            '04' => '04',
            '05' => '05',
            '06' => '06',
            '07' => '07',
            '08' => '08',
            '09' => '09',
            'CL' => 'CLUB',
            'ME' => 'MEET',
        );

        return $type;
    }

    static public function getTime()
    {
        $type = array(
            '1'=>'8:00-8:20',
            '2'=>'8:25-9:10',
            '3'=>'9:15-10:00',
            '4'=>'10:25-11:10',
            '5'=>'11:15-12:00',
            '6'=>'13:00-13:45',
            '7'=>'13:50-14:35',
            '8'=>'14:40-15:25',
            '9'=>'15:45-16:45',
        );

        return $type;
    }
}
