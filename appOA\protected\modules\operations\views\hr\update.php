<?php $form = $this->beginWidget('CActiveForm', array(
    'id' => 'staff-form',
    'enableAjaxValidation' => false,
    'htmlOptions' => array('class' => 'J_ajaxForm', 'enctype' => 'multipart/form-data'),
));
?>
<div class="h_a">常规信息</div>
<div class="table_full">
    <table width="100%">
        <col width="150"/>
        <col width="400"/>
        <col/>
        <tbody>
        <tr>
            <th><?php echo $form->labelEx($model->profile, 'first_name'); ?></th>
            <td>
                <?php echo $form->textField($model->profile, 'first_name', array('maxlength' => 255, 'class' => 'input length_5')); ?>
            </td>
            <td>
                <div class="fun_tips"><?php echo $form->error($model->profile, 'first_name'); ?></div>
            </td>
        </tr>
        <tr>
            <th><?php echo $form->labelEx($model->profile, 'last_name'); ?></th>
            <td>
                <?php echo $form->textField($model->profile, 'last_name', array('maxlength' => 255, 'class' => 'input length_5')); ?>
            </td>
            <td>
                <div class="fun_tips"><?php echo $form->error($model->profile, 'last_name'); ?></div>
            </td>
        </tr>
        <tr>
            <th><?php echo $form->labelEx($model, 'name'); ?></th>
            <td>
                <?php echo $form->textField($model, 'name', array('maxlength' => 255, 'class' => 'input length_5')); ?>
            </td>
            <td>
                <div class="fun_tips"><?php echo $form->error($model, 'name'); ?></div>
            </td>
        </tr>
        <tr>
            <th><?php echo $form->labelEx($model, 'email'); ?></th>
            <td>
                <?php echo $form->textField($model, 'email', array('maxlength' => 255, 'class' => 'input length_5')); ?>
            </td>
            <td>
                <div class="fun_tips"><?php echo $form->error($model, 'email'); ?></div>
            </td>
        </tr>
        <tr>
            <th><?php echo $form->labelEx($model->staff, 'pemail'); ?></th>
            <td>
                <?php echo $form->textField($model->staff, 'pemail', array('maxlength' => 255, 'class' => 'input length_5')); ?>
            </td>
            <td>
                <div class="fun_tips"><?php echo $form->error($model->staff, 'pemail'); ?></div>
            </td>
        </tr>
        <tr>
            <th><?php echo $form->labelEx($model->profile, 'user_gender'); ?></th>
            <td>
                <?php echo $form->dropDownList($model->profile, 'user_gender', OA::getChildGenderList(), array('class' => 'select_5', 'empty' => Yii::t('global', 'Please Select'))); ?>
            </td>
            <td>
                <div class="fun_tips"><?php echo $form->error($model->profile, 'user_gender'); ?></div>
            </td>
        </tr>
        <tr>
            <th><?php echo $form->labelEx($model->profile, 'nationality'); ?></th>
            <td>
                <?php echo $form->dropDownList($model->profile, 'nationality', Country::model()->getData(), array('class' => 'select_5', 'empty' => Yii::t('global', 'Please Select'))); ?>
            </td>
            <td>
                <div class="fun_tips"><?php echo $form->error($model->profile, 'nationality'); ?></div>
            </td>
        </tr>
        <tr>
            <th><?php echo $form->labelEx($model, 'level'); ?></th>
            <td>
                <?php echo $form->radioButtonList($model, 'level', $this->cfg['level'], array('separator' => '&nbsp;&nbsp;')); ?>
            </td>
            <td>
                <div class="fun_tips"><?php echo $form->error($model, 'level'); ?></div>
            </td>
        </tr>
        <?php if ($model->user_avatar && $model->user_avatar != 'blank.gif'): ?>
            <tr>
                <th><?php echo $form->labelEx($model, 'user_avatar'); ?></th>
                <td>
                    <?php echo CHtml::image(Yii::app()->params["OAUploadBaseUrl"] . "/users/thumbs/" . $model->user_avatar); ?>
                </td>
                <td></td>
            </tr>
        <?php endif; ?>
        <tr>
            <th><?php echo $form->labelEx($model, 'uploadPhoto'); ?></th>
            <td>
                <?php echo $form->fileField($model, 'uploadPhoto'); ?>
            </td>
            <td>
                <div class="fun_tips"><?php echo $form->error($model, 'uploadPhoto'); ?></div>
            </td>
        </tr>
        <tr>
            <th><?php echo $form->labelEx($model->profile, 'branch'); ?></th>
            <td>
                <?php echo $form->dropDownList($model->profile, 'branch', $this->cfs['branch'], array('class' => 'select_5', 'empty' => Yii::t('global', 'Please Select'))); ?>
            </td>
            <td>
                <div class="fun_tips"><?php echo $form->error($model->profile, 'branch'); ?></div>
            </td>
        </tr>
        <tr>
            <th><?php echo $form->labelEx($model->profile, 'occupation_en'); ?></th>
            <td>
                <?php echo $form->dropDownList($model->profile, 'occupation_en', DepPosLink::model()->getDepPos(), array('class' => 'select_5', 'empty' => Yii::t('global', 'Please Select'))); ?>
            </td>
            <td>
                <div class="fun_tips"><?php echo $form->error($model->profile, 'occupation_en'); ?></div>
            </td>
        </tr>
        <tr>
            <th><?php echo $form->labelEx($model, 'iniPassword'); ?></th>
            <td>
                <?php echo $form->textField($model, 'iniPassword', array('class' => 'input length_3')); ?>
                <?php echo CHtml::link(Yii::t('child', 'Generate'), 'javascript:void(0)', array('class' => 'setPass btn', 'rel' => 'User_iniPassword')); ?>
            </td>
            <td>
                <div class="fun_tips"><?php echo $form->error($model, 'iniPassword'); ?></div>
            </td>
        </tr>
        <tr>
            <th><?php echo $form->labelEx($model->staffApprover, 'approver'); ?></th>
            <td>
                <?php echo $form->dropDownList($model->staffApprover, 'approver', $model->staffApprover->approvers, array('class' => 'select_5', 'empty' => Yii::t('global', 'Please Select'))); ?>
            </td>
            <td>
                <div class="fun_tips"><?php echo $form->error($model->staffApprover, 'approver'); ?></div>
            </td>
        </tr>
        <?php if ($model->getScenario() == 'addStaff'): ?>
            <tr>
                <th>是否发送邮件</th>
                <td>
                    <?php echo CHtml::checkBox('isSend', true) ?>
                </td>
                <td></td>
            </tr>
        <?php endif; ?>
        </tbody>
    </table>
</div>
<div class="h_a">扩展信息</div>
<div class="table_full">
    <table width="100%">
        <col width="150"/>
        <col width="400"/>
        <col/>
        <tbody>
        <tr>
            <th><?php echo $form->labelEx($model->staff, 'mobile_telephone'); ?></th>
            <td>
                <?php echo $form->textField($model->staff, 'mobile_telephone', array('maxlength' => 255, 'class' => 'input length_5')); ?>
            </td>
            <td>
                <div class="fun_tips"><?php echo $form->error($model->staff, 'mobile_telephone'); ?></div>
            </td>
        </tr>
        <tr>
            <th><?php echo $form->labelEx($model->staff, 'card_id'); ?></th>
            <td>
                <?php echo $form->textField($model->staff, 'card_id', array('maxlength' => 255, 'class' => 'input length_5')); ?>
            </td>
            <td>
                <div class="fun_tips"><?php echo $form->error($model->staff, 'card_id'); ?></div>
            </td>
        </tr>
        <tr>
            <th><?php echo $form->labelEx($model->staff, 'card_id_due'); ?></th>
            <td>
                <?php
                $this->widget('zii.widgets.jui.CJuiDatePicker', array(
                    "model" => $model->staff,
                    "attribute" => "card_id_due",
                    "options" => array(
                        'changeMonth' => true,
                        'changeYear' => true,
                        'dateFormat' => 'yy-mm-dd',
                    ),
                    "htmlOptions" => array(
                        'class' => 'input length_5'
                    )
                ));
                ?>
            </td>
            <td>
                <div class="fun_tips"><?php echo $form->error($model->staff, 'card_id_due'); ?></div>
            </td>
        </tr>
        <tr>
            <th><?php echo $form->labelEx($model->staff, 'startdate'); ?></th>
            <td>
                <?php
                $this->widget('zii.widgets.jui.CJuiDatePicker', array(
                    "model" => $model->staff,
                    "attribute" => "startdate",
                    "options" => array(
                        'changeMonth' => true,
                        'changeYear' => true,
                        'dateFormat' => 'yy-mm-dd',
                    ),
                    "htmlOptions" => array(
                        'class' => 'input length_5'
                    )
                ));
                ?>
            </td>
            <td>
                <div class="fun_tips"><?php echo $form->error($model->staff, 'startdate'); ?></div>
            </td>
        </tr>
        <tr>
            <th><?php echo $form->labelEx($model->staff, 'leavedate'); ?></th>
            <td>
                <?php
                $this->widget('zii.widgets.jui.CJuiDatePicker', array(
                    "model" => $model->staff,
                    "attribute" => "leavedate",
                    "options" => array(
                        'changeMonth' => true,
                        'changeYear' => true,
                        'dateFormat' => 'yy-mm-dd',
                    ),
                    "htmlOptions" => array(
                        'class' => 'input length_5'
                    )
                ));
                ?>
            </td>
            <td>
                <div class="fun_tips"><?php echo $form->error($model->staff, 'leavedate'); ?></div>
            </td>
        </tr>
        <tr>
            <th><?php echo $form->labelEx($model->staff, 'contract_type'); ?></th>
            <td>
                <?php echo $form->textField($model->staff, 'contract_type', array('maxlength' => 255, 'class' => 'input length_5')); ?>
            </td>
            <td>
                <div class="fun_tips"><?php echo $form->error($model->staff, 'contract_type'); ?></div>
            </td>
        </tr>
        <tr>
            <th><?php echo $form->labelEx($model->staff, 'contract_period'); ?></th>
            <td>
                <?php
                $this->widget('zii.widgets.jui.CJuiDatePicker', array(
                    "model" => $model->staff,
                    "attribute" => "contract_period",
                    "options" => array(
                        'changeMonth' => true,
                        'changeYear' => true,
                        'dateFormat' => 'yy-mm-dd',
                    ),
                    "htmlOptions" => array(
                        'class' => 'input length_5'
                    )
                ));
                ?>
            </td>
            <td>
                <div class="fun_tips"><?php echo $form->error($model->staff, 'contract_period'); ?></div>
            </td>
        </tr>
        <tr>
            <th><?php echo $form->labelEx($model->staff, 'social_insurance'); ?></th>
            <td>
                <?php echo $form->textField($model->staff, 'social_insurance', array('maxlength' => 255, 'class' => 'input length_5')); ?>
            </td>
            <td>
                <div class="fun_tips"><?php echo $form->error($model->staff, 'social_insurance'); ?></div>
            </td>
        </tr>
        <tr>
            <th><?php echo $form->labelEx($model->staff, 'emergency_person'); ?></th>
            <td>
                <?php echo $form->textField($model->staff, 'emergency_person', array('maxlength' => 255, 'class' => 'input length_5')); ?>
            </td>
            <td>
                <div class="fun_tips"><?php echo $form->error($model->staff, 'emergency_person'); ?></div>
            </td>
        </tr>
        <tr>
            <th><?php echo $form->labelEx($model->staff, 'emergency_contact'); ?></th>
            <td>
                <?php echo $form->textField($model->staff, 'emergency_contact', array('maxlength' => 255, 'class' => 'input length_5')); ?>
            </td>
            <td>
                <div class="fun_tips"><?php echo $form->error($model->staff, 'emergency_contact'); ?></div>
            </td>
        </tr>
        <tr>
            <th><?php echo $form->labelEx($model->staff, 'dwelling_place'); ?></th>
            <td>
                <?php echo $form->textField($model->staff, 'dwelling_place', array('maxlength' => 255, 'class' => 'input length_5')); ?>
            </td>
            <td>
                <div class="fun_tips"><?php echo $form->error($model->staff, 'dwelling_place'); ?></div>
            </td>
        </tr>
        <tr>
            <th><?php echo $form->labelEx($model->staff, 'native_place'); ?></th>
            <td>
                <?php echo $form->textField($model->staff, 'native_place', array('maxlength' => 255, 'class' => 'input length_5')); ?>
            </td>
            <td>
                <div class="fun_tips"><?php echo $form->error($model->staff, 'native_place'); ?></div>
            </td>
        </tr>
        <tr>
            <th><?php echo $form->labelEx($model->staff, 'education_degree'); ?></th>
            <td>
                <?php echo $form->textField($model->staff, 'education_degree', array('maxlength' => 255, 'class' => 'input length_5')); ?>
            </td>
            <td>
                <div class="fun_tips"><?php echo $form->error($model->staff, 'education_degree'); ?></div>
            </td>
        </tr>
        <tr>
            <th><?php echo $form->labelEx($model->staff, 'archive_site'); ?></th>
            <td>
                <?php echo $form->textField($model->staff, 'archive_site', array('maxlength' => 255, 'class' => 'input length_5')); ?>
            </td>
            <td>
                <div class="fun_tips"><?php echo $form->error($model->staff, 'archive_site'); ?></div>
            </td>
        </tr>
        </tbody>
    </table>
</div>
<div class="btn_wrap">
    <div class="btn_wrap_pd">
        <button class="btn btn_submit mr10 J_ajax_submit_btn"><?php echo Yii::t('global', 'Submit'); ?></button>
    </div>
</div>
<?php $this->endWidget(); ?>

<script type="text/javascript">
    var flag = true;
    function getRandomNum(num) {
        var numArr = [ 2, 3, 4, 5, 6, 7, 8, 9];
        var codeUpArr = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'J', 'K', 'M', 'N', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'];
        var codeArr = [ 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'j', 'k', 'm', 'n', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z'];
        var section = (Math.round(Math.random() * 10)) > 5;
        if(flag === true && num === 3){
            var rndNum = randArr(numArr);
            var ranCode = randArr(codeUpArr);
            var rNum = ranCode + rndNum;
        }else if(flag === true && section){
            var rndNum = randArr(numArr);
            var ranCode = randArr(codeUpArr);
            var rNum = ranCode + rndNum;
            flag = false;
        }else {
            var rndNum = randArr(numArr);
            var ranCode = randArr(codeArr);
            var rNum = ranCode + rndNum;
        }
        return rNum;
    }
    function randArr(arr){
        return arr[Math.floor(Math.random() * (arr.length-1))];
    }
    function randPassword()
    {
        var sPassword = "";
        for (var i=0; i < 4; i++) {
            var grn = getRandomNum(i);
            sPassword = sPassword + grn;
        }
        flag = true;
        return sPassword;
    }
    $('a.setPass').bind('click',function(){
        var t='#'+$(this).attr('rel');
        $(t).val(randPassword());
    });
    $('#UserProfile_branch').bind('change',function(){
        var branchid = $(this).val();
        $("#StaffApprover_approver").empty();
        $("#StaffApprover_approver").append($('<option>', {
            value: '',
            text : '请选择'
        }));
        if (branchid){
            $.ajax({
                type: "POST",
                url: "http://apps.mims.cn/operations/hr/getUser",
                data: "branchid="+branchid,
                dataType: "json",
                success: function(data){
                    var select = (Object.keys(data).length == 1) ? 'selected' : "";
                    $.each(data, function (i, item) {
                        $("#StaffApprover_approver").append($("<option>", {
                            selected: select,
                            value: i,
                            text : item
                        }));
                    });
                }
            });
        }
    })

</script>