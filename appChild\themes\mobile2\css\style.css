@font-face {
    font-family: 'icomoon';
    src:  url('fonts/icomoon.eot?r5ub0n');
    src:  url('fonts/icomoon.eot?r5ub0n#iefix') format('embedded-opentype'),
    url('fonts/icomoon.ttf?r5ub0n') format('truetype'),
    url('fonts/icomoon.woff?r5ub0n') format('woff'),
    url('fonts/icomoon.svg?r5ub0n#icomoon') format('svg');
    font-weight: normal;
    font-style: normal;
    font-display: block;
}

[class^="icon-"], [class*=" icon-"] {
    /* use !important to prevent issues with browser extensions that change fonts */
    font-family: 'icomoon' !important;
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;

    /* Better Font Rendering =========== */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

body, html {
    height: 100%;
    -webkit-tap-highlight-color: transparent
}
body{
    overflow-x: hidden;
    background-color: #fbf9fe;
}

.hide {
    display: none;
}
.container {
    height: 100%;
    -webkit-overflow-scrolling:touch;
    background-color: #fbf9fe;
    overflow-y:auto;
}

.hd {
    padding: 2em 0
}

.bd.spacing {
    padding: 0 15px
}

.page_title {
    text-align: center;
    font-size: 34px;
    color: #3cc51f;
    font-weight: 400;
    margin: 0 15%
}

.page.cell .bd {
    padding-bottom: 30px
}

.page.toast {
    background-color: #fff
}

.page.toast .bd {
    padding: 120px 15px 0
}

.page.dialog {
    background-color: #fff
}

.page.dialog .bd {
    padding: 120px 15px 0
}

.page.msg {
    background-color: #fff
}

.page.panel .bd {
    padding-bottom: 20px
}

.page.article {
    background-color: #fff
}

.page.article .page_title {
    color: #de7c23
}

.page.icons {
    background-color: #fff;
    text-align: center
}

.page.icons .page_title {
    color: #3e24bd
}

.page.icons .bd {
    padding: 30px 0;
    text-align: center
}

.page.icons .icon_sp_area {
    padding: 10px 20px;
    text-align: left
}

.page.icons i {
    margin: 0 5px 10px
}

.search_show {
    display: none;
    margin-top: 0;
    font-size: 14px
}

.search_show .weui_cell_bd {
    padding: 2px 0 2px 20px;
    color: #666
}
@-webkit-keyframes a {
    0% {
        -webkit-transform: translate3d(100%, 0, 0);
        transform: translate3d(100%, 0, 0);
        opacity: 0
    }
    to {
        -webkit-transform: translateZ(0);
        transform: translateZ(0);
        opacity: 1
    }
}

@keyframes a {
    0% {
        -webkit-transform: translate3d(100%, 0, 0);
        transform: translate3d(100%, 0, 0);
        opacity: 0
    }
    to {
        -webkit-transform: translateZ(0);
        transform: translateZ(0);
        opacity: 1
    }
}

@-webkit-keyframes b {
    0% {
        -webkit-transform: translateZ(0);
        transform: translateZ(0);
        opacity: 1
    }
    to {
        -webkit-transform: translate3d(100%, 0, 0);
        transform: translate3d(100%, 0, 0);
        opacity: 0
    }
}

@keyframes b {
    0% {
        -webkit-transform: translateZ(0);
        transform: translateZ(0);
        opacity: 1
    }
    to {
        -webkit-transform: translate3d(100%, 0, 0);
        transform: translate3d(100%, 0, 0);
        opacity: 0
    }
}

.page.slideIn {
    -webkit-animation: a .2s forwards;
    animation: a .2s forwards
}

.page.slideOut {
    -webkit-animation: b .2s forwards;
    animation: b .2s forwards
}

.img-responsive{
    max-width: 100%;
    height: auto;
    display: block;
}

.feedback{
    position: fixed;
    bottom: 0;
    overflow: hidden;
    width: 100%;
    background-color: #FFF;
}

.feedback textarea{
    border: 0;
    border-bottom: 1px solid #f2f2f2;
    line-height: 30px;
    height: 30px;
    width: 100%;
    overflow: hidden;
    outline: none;
}

.feedback .weui_btn{
    margin-top: 0;
}

.feedback .weui_cell_switch{
    padding-bottom: 0px;
}
.feedback a{
    /*width: 88%;*/
/*    -weblit-transform: translateX();
    transform: translateX();*/
}

#showTooltips {
    margin-top: -2px;
    width: 64px;
}
.reminders{
    border-width: 8px 2px 2px;
    border-style: solid;
    border-color: rgb(18, 149, 39);
    padding: 16px;
}

.content-view{
    border-left-width: 3px;
    border-left-style: solid;
    border-color: rgb(18, 149, 39);
    padding: 5px 5px 5px 10px;
}

.weui_panel_hd:after {
    width: 96%;
}
.recipes_week {
    text-indent: 20px;
    padding: 10px 0;
    text-align: left;
    font-weight: 600;
    background-color: #fff;
}
.recipes_table {
    text-align: center;
    border-collapse: collapse;
    width: 90%;
    margin: 0 auto;
}
.recipes_table th {
    background-color: #f2f2f2;
}
.recipes_td {
    width: 12%;
    background-color: #f2f2f2;
}
.recipes_content img {
    border-radius: 6px;
    width: 100%;
}
.recipes_content:nth-of-type(2) {
    padding:10px;
    text-align: left;
}
.recipes_table .recipes_content:nth-of-type(4) {
    background-color: red;
}
.recipes_table th, .recipes_table td{
    border: 1px solid gray;
}

.outher_recipes {
    padding:20px 0 ;
    text-align: center;
}

.article_h {
    padding: 2px 13px;
    background: #dfdfdf;
    color: #6c6c6c;
    border: 1px solid #eee;
    text-align: center;
}
.article_content {
    padding:10px 0;
}
.twodimensioncode_box {
    position: relative;
    text-align: center;
}
.twodimensioncode_box + div {
    text-align: center;
}
.twodimensioncode_box i {
    height: 50px;
    position: absolute;
    left:0;
    top: 0;
    bottom: 0;
    right: 0;
    margin: auto;
    z-index: 2;
}
#replynumber {
    margin-top: -2px;
    margin-left: 8px;
}
#replynumber_parent {
    position: relative;
}
#replynumber_parent span {
    border-radius: 50%;
    position: absolute;
    right: -4px;
    top: -4px;
    width: 8px;
    height: 8px;
    line-height: 16px;
    text-align: center;
    background-color: red;
    z-index: 9;
}
.mychat_box_left , .mychat_box_right {
    padding: 15px;
    padding-bottom: 0;
    position: relative;
    overflow: visible;
}
.mychat_box_left .mychat_img_box , .mychat_box_right .mychat_img_box  {
    width: 36px;
    height: 36px;
    position: absolute;
    top: 40px;
    -webkit-transform: translateY(-50%);
    -moz-transform: translateY(-50%);
    transform: translateY(-50%);
}
.mychat_box_left .mychat_img_box {
    left: 15px;
}
.mychat_box_right .mychat_img_box {
    right: 15px;
}
.mychat_box_left img ,.mychat_box_right img {
    width: 100%;
} 
.mychat_box_talk div {
    font-weight: 400;
    font-size: 15px;
    line-height: 1.2;
    word-wrap:break-word
}
.mychat_box_talk p {
    color: #999;
    font-size: 12px;
    line-height: 1.2;
}
.mychat_box_left .mychat_box_talk {
    border: #bce8f1 solid 1px;
    background-color: #d9edf7;
    border-radius: 10px;
    padding: 10px;
    margin-left: 18%;
    position: relative;
}
.mychat_box_talk:after ,.mychat_box_talk:after  {
    content: "";
    -webkit-transform: rotate(45deg);
    -moz-transform: rotate(45deg);
    transform: rotate(45deg);
    width: 10px;
    height: 10px;
    position: absolute;
    top: 18px;
}
.mychat_box_left .mychat_box_talk:after {
    border-left:1px solid #bce8f1;
    border-bottom:1px solid #bce8f1;
    background: #d9edf7;
    left: -6px;
}

.mychat_box_right .mychat_box_talk {
    border: #d6e9c6 solid 1px;
    background-color: #dff0d8;
    border-radius: 10px;
    padding: 10px;
    margin-right: 18%;
    position: relative;
}
.mychat_box_right .mychat_box_talk:after {
    border-top:1px solid #d6e9c6;
    border-right:1px solid #d6e9c6;
    background: #dff0d8;
    right: -6px;
}

#total {
    text-align: right;
}



.swiper-container {
    width: 100%;
    height: 300px;
}
.swiper-pagination-bullet-active {
    background: #0ca249;
}

.product-info {
    position: relative;
    display: block;
    width: 100%;
    box-sizing: border-box;
    border-top: #e2e2e2 1px solid;
    border-bottom: #e2e2e2 1px solid;
    background-color: #ffffff;
    padding: 14px;
}
.product-intro {
    margin-top: 15px;
    position: relative;
    display: block;
    width: 100%;
    box-sizing: border-box;
    border-top: #e2e2e2 1px solid;
    border-bottom: #e2e2e2 1px solid;
    background-color: #ffffff;
}
.product-info .info-price .price-market {
    margin-left: 4px;
    line-height: 16px;
    color: #999999;
    font-size: 14px;
    font-weight: normal;
    overflow: hidden;
    text-decoration: line-through;
}
.product-intro .intro-header {
    position: relative;
    display: block;
    width: 100%;
    height: 44px;
    box-sizing: border-box;
    border-bottom: #e2e2e2 1px solid;
    text-align: center;
    color: #404245;
    font-size: 14px;
    font-weight: normal;
    line-height: 44px;
}
.product-intro .intro-content {
    position: relative;
    display: block;
    box-sizing: border-box;
    color: #404245;
    font-size: 14px;
    font-weight: normal;
    line-height: 18px;
    overflow: hidden;
    padding: 14px;
    text-align: left;
}
.shopping-cart .shopping-btn {
    width: 38.2%;
    background-color: #E64340;
    float: right;
    text-align: center;
    border-radius: 0;
    border: 2px solid #E64340;
}
.shopping-cart .shopping-count {
    width: 61.8%;
    overflow: hidden;
    white-space: nowrap;
    float: left;
    font-size: 15px;
}
.caret {
    display: inline-block;
    width: 0;
    height: 0;
    margin: 0 0 2px 2px;
    vertical-align: middle;
    border-top: 4px dashed;
    border-right: 4px solid transparent;
    border-left: 4px solid transparent;
    transition: .6s ease;
}
.weui-btn {
    position: relative;
    display: block;
    margin-left: auto;
    margin-right: auto;
    padding-left: 14px;
    padding-right: 14px;
    box-sizing: border-box;
    font-size: 18px;
    text-align: center;
    text-decoration: none;
    color: #fff;
    line-height: 2.55555556;
    border-radius: 5px;
    -webkit-tap-highlight-color: rgba(0,0,0,0);
    overflow: hidden;
}
.weui-actionsheet__title {
    position: relative;
    height: 35px;
    padding: 0 20px;
    line-height: 1.4;
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    justify-content: center;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-flex-direction: column;
    flex-direction: column;
    text-align: center;
    font-size: 14px;
    color: #888;
    background: #fcfcfd;
}
.weui-actionsheet__title .weui-actionsheet__title-text {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
}
.tb-promo-price {
    position: static;
    display: inline;
    padding: 0;
    margin-right: 5px;
    vertical-align: baseline;
    font-size: 26px;
    font-weight: 700;
    font-family: Tahoma,Arial,Helvetica,sans-serif;
    color: #333;
}

.weui_cells_checkbox .icon-radio:before {
    content: '\e903';
    color: #C9C9C9;
    font-size: 23px;
    display: block;
}
.weui_cells_checkbox .weui_check:checked + .icon-radio:before {
    content: '\e902';
    color: #09BB07;
}

.weui_cells_checkbox .icon-checked:before {
    content: '\e901';
    color: #C9C9C9;
    font-size: 23px;
    display: block;
}
.weui_cells_checkbox .weui_check:checked + .icon-checked:before {
    content: '\e900';
    color: #09BB07;
}

.weui_cells_checkbox .weui_cells_title {
    margin-top: 0.6em;
    margin-bottom: 0.6em;
}