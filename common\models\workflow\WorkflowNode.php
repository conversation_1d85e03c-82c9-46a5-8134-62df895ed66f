<?php

/**
 * This is the model class for table "ivy_workflow_node".
 *
 * The followings are the available columns in table 'ivy_workflow_node':
 * @property integer $node_id
 * @property integer $defination_id
 * @property string $node_name_cn
 * @property string $node_name_en
 * @property integer $node_type
 * @property string $init_function
 * @property string $run_function
 * @property string $save_function
 * @property string $transit_function
 * @property string $executor
 * @property integer $execute_type
 * @property integer $filter_adm
 * @property integer $remind
 * @property string $field
 * @property integer $max_day
 * @property integer $allow_repeat
 */
class WorkflowNode extends CActiveRecord
{
    public $executeId;  //权限执行ID
    public $branchType; //学校类型
    const WORKFLOW_NODES_CACHE_ID = 'workflow-node-list';
    /**
	 * Returns the static model of the specified AR class.
	 * @return WorkflowNode the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_workflow_node';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('defination_id', 'required','on'=>'add'),
			array('defination_id, node_name_cn, node_name_en, node_type,executor,executeId,', 'required','on'=>'update'),
			array('defination_id, node_type, execute_type, filter_adm, remind, max_day, allow_repeat', 'numerical', 'integerOnly'=>true),
			array('node_name_cn, node_name_en, init_function, run_function, save_function, transit_function, executor, field, field2', 'length', 'max'=>255),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('node_id, defination_id, node_name_cn, node_name_en, node_type, init_function, run_function, save_function, transit_function, executor, execute_type, filter_adm, remind, field, field2, max_day, allow_repeat', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'defination'=>array(self::BELONGS_TO,'WorkflowDefination','defination_id'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'node_id' => 'Node',
			'defination_id' => 'Defination',
			'node_name_cn' => Yii::t('workflow', '节点中文名称'),
			'node_name_en' => Yii::t('workflow', '节点英文名称'),
			'node_type' => Yii::t('workflow', '结点类型'),
			'init_function' => 'Init Function',
			'run_function' => 'Run Function',
			'save_function' => 'Save Function',
			'transit_function' => 'Transit Function',
			'executor' =>Yii::t('workflow', '执行角色'),
			'executeId' =>Yii::t('workflow', '权限范围'),
			'execute_type' => 'Execute Type',
			'filter_adm' => 'Filter Adm',
			'remind' => 'Remind',
			'field' => Yii::t('workflow', '扩展字段'),
			'field2' => Yii::t('workflow', '扩展字段2'),
			'max_day' => 'Max Day',
			'allow_repeat' => 'Allow Repeat',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('node_id',$this->node_id);
		$criteria->compare('defination_id',$this->defination_id);
		$criteria->compare('node_name_cn',$this->node_name_cn,true);
		$criteria->compare('node_name_en',$this->node_name_en,true);
		$criteria->compare('node_type',$this->node_type);
		$criteria->compare('init_function',$this->init_function,true);
		$criteria->compare('run_function',$this->run_function,true);
		$criteria->compare('save_function',$this->save_function,true);
		$criteria->compare('transit_function',$this->transit_function,true);
		$criteria->compare('executor',$this->executor,true);
		$criteria->compare('execute_type',$this->execute_type);
		$criteria->compare('filter_adm',$this->filter_adm);
		$criteria->compare('remind',$this->remind);
		$criteria->compare('field',$this->field,true);
		$criteria->compare('max_day',$this->max_day);
		$criteria->compare('allow_repeat',$this->allow_repeat);

		return new CActiveDataProvider(get_class($this), array(
			'criteria'=>$criteria,
		));
	}
    
    /**
     * 节点类型
     */
    public static function getNodeType($type=null){
        $ret = array(
            1=>'人为决策',
            2=>'自动处理',
            3=>'等待外部响应',
            4=>'分支',
            5=>'汇总',
            6=>'结束结点',
        );
        return empty($type) ? $ret : $ret[$type];
    }
    
    /**
     * 获取所有节点列表
     */
    public function getAllNodesList(){
        $nodeList = Yii::app()->cache->get(WorkflowNode::WORKFLOW_NODES_CACHE_ID);
        if ($nodeList === false){
            $definationObj = WorkflowDefination::model()->with('workflowNode')->findAll();
            $lang = (Yii::app()->language == 'zh_cn' ) ? 'cn' : 'en';
            foreach ($definationObj as $defination){
                $dName = 'defination_name_'.$lang;
                $nodeList[$defination->defination_id]['name'] = $defination->$dName;
                $nodeList[$defination->defination_id]['obj'] = $defination;
                foreach ($defination->workflowNode as $node){
                    $nName = 'node_name_'.$lang;
                    $nodeList[$node->defination_id]['nodes'][$node->node_id]['name'] = $node->$nName;
                }
            }
        }
        // Yii::app()->cache->set(WorkflowNode::WORKFLOW_NODES_CACHE_ID, $nodeList);
        return $nodeList;
    }
    
    public function save($runValidation = true, $attributes = null) {
        $ret = parent::save($runValidation, $attributes);
        Yii::app()->cache->delete(WorkflowNode::WORKFLOW_NODES_CACHE_ID);
        return $ret;
    }
    
    public function delete() {
        $ret = parent::delete();
        Yii::app()->cache->delete(WorkflowNode::WORKFLOW_NODES_CACHE_ID);
        return $ret;
    }
    
}