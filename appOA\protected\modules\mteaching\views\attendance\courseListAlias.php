<script>
    var teachers = <?php echo CJSON::encode($teachers); ?>;
    var projects = [];
</script>
<style>
    [v-cloak] {
        display: none;
    }
    .loading{
        width:98%;
        height:95%;
        background:#fff;
        position: absolute; 
        opacity: 0.5;
        z-index: 99
    }
    .loading span{
        width:100%;
        height:60%;
        display:block;
        background: url("<?php echo Yii::app()->theme->baseUrl?>/images/loading.gif")no-repeat center center ;
    }
    .btn-default.active{
        color: #fff;
        background-color: #337ab7;
        border-color: #2e6da4;
    }
</style>
<div class="container-fluid">
    <!-- 面包屑导航 -->
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Teaching Tasks'), array('//mteaching/default/index')) ?></li>
        <li class="active"><?php echo Yii::t('site', 'G6-12 Student Attendance'); ?></li>
        <li class="active"><?php echo Yii::t('attends', 'My Courses') ?></li>
    </ol>
    <div class="row">
        <div class="col-md-2">
            <?php
            $this->widget('zii.widgets.CMenu',array(
                'items'=> $this->siderMenu,
                'htmlOptions'=>array('class'=>'nav nav-pills nav-stacked text-right background-gray mb10'),
                'activeCssClass'=>'active',
                'itemCssClass'=>''
            ));
            ?>
        </div>
        <div class="col-md-10" id="list" v-cloak>
            <p class='color3 mb20' ><strong style='font-size:18px'><?php echo Yii::t('attends','My Courses') ?></strong> <span class='ml20 font14'>{{startYear}}</span> </p>
            <h4 class='mb20'>
                <span class="glyphicon glyphicon-user color6"></span>
                <span style='color:#4D88D2' class='font16'>{{teacherName}}</span>
                <span style='color:#4D88D2' title="<?php echo Yii::t('attends', 'View Schedules of Other Teachers')?>" @click="chooseTeacher(<?php echo $tid; ?>)" class="glyphicon glyphicon-chevron-down"></span>
            </h4>
            <div class='loading'  v-if='showLoading'>
                <span></span>
            </div>
            <div class="panel panel-default" v-if='courseList.length!=0  && !showLoading'>
                <div class="panel-heading"><?php echo Yii::t('attends', 'My Courses') ?></div>
                <table class="table reportTbody">
                    <tbody class="reportTbody">
                        <tr>
                            <td>#</td>
                            <td><?php echo Yii::t('attends', 'Course Code') ?></td>
                            <td><?php echo Yii::t('attends', 'Course Name') ?></td>
                        </tr>
                        <tr  v-for='(data,list) in courseList'>
                            <td width="50">{{list+1}}</td>
                            <td width="150">{{data.course_code}}</td>
                            <td width="200"> {{data.title }}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="alert alert-warning" v-else-if='!showLoading' role="alert"><?php echo Yii::t('attends', 'No course has been assigned to you') ?></div>
            <div class="modal fade" id="remarks" tabindex="-1" role="dialog" aria-labelledby="modal" aria-hidden="false">
                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                            <h4 class="modal-title" ><?php echo Yii::t('attends','Select a Teacher') ?> <span id="remarks_t"></span></h4>
                        </div>
                        <div class="form-horizontal">
                            <div class="modal-body">
                                <div name='teacherId' id="mySelect">
                                <a class='mb5 btn btn-default mr10' href='javascript:;' :class='teacherId==list.id?"active":""' v-for='(list,index) in teacherList' @click='getList(list)'>{{list.value}}</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
var name='<?php echo $userModel ? $userModel->getName() : $uid ?>';
var tid='<?php echo $tid; ?>';
var teacher_id='<?php echo $teacher_id ?>'
var teacherList;
     var lists = new Vue({
        el: "#list",
        data: {
            courseList:[],
            teacherList:[],
            teacherName:name,
            teacherId:'',
            startYear:'',
            showLoading:false
            },
            created(){
                let that=this
                $.ajax({
                    type: "POST",
                    url: "<?php echo $this->createUrl('teacher')?>",
                    data: {tid:tid},
                    dataType: "json",
                    success: function(data){
                        if(data){
                            let list=[]
                            for(var key in data){
                                list.push({
                                    id:key,
                                    value:data[key].name
                                })
                            }
                            that.teacherList=that.sortTea(list)
                        }
                    }
                });
                this.getList('')
                
            },
            methods: {
                sortTea(list){
                    list.sort((x,y)=>{
                        console.log(x.value,typeof x.value, y.value,typeof y.value,)
                        return x.value.localeCompare(y.value)
                    })
                    return list
                },
                getList(list){
                    let that=this
                    if(list!=''){
                        this.teacherName=list.value
                    }
                    this.teacherId=list==''?teacher_id:list.id
                    this.showLoading=true
                    $.ajax({
                        type: "POST",
                        url: "<?php echo $this->createUrl('myCourse')?>",
                        data: {
                            teacherId:this.teacherId
                        },
                        dataType: "json",
                        success: function(data){
                            if(data.state=='success'){
                                that.courseList = data.data.course;
                                that.startYear = data.data.startyear;
                                if(list!=''){
                                    $("#remarks").modal('hide');
                                }
                            }
                            that.showLoading=false
                        }
                    });
                },
                chooseTeacher(){
                    $("#remarks").modal('show');
                },
           }
        })

function chooseTeacher(tid) {
    var child = '';
    if(teacherList){
        $("#remarks").modal('show');
        $.each(teacherList,function(key,val){
            child += "<a class='mb5 btn btn-default' href='<?php echo $this->createUrl('courseAlias') ?>&teacherId="+ key +"'>" + val +  "</a> ";
        });
        $("#mySelect").html(child);
    }else{
        $.ajax({
            type: "POST",
            url: "<?php echo $this->createUrl('teacher')?>",
            data: {tid:tid},
            dataType: "json",
            success: function(data){
                $("#remarks").modal('show');
                var list = '';
                if(data){

                    $.each(data,function(key,val){
                        child += "<a class='mb5 btn btn-default' href='<?php echo $this->createUrl('courseAlias') ?>&teacherId="+ key +"'>" + val.name +  "</a> ";
                    });
                    // child += "<p class='clearfix'></p>";
                    $("#mySelect").html(child);
                    teacherList = data;
                }
            }
        });
    }
}
</script>

