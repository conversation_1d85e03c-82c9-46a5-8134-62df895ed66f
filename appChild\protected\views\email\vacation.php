<?php
if (in_array($data['vacation']->type, array(40, 50))) {
    $vacationDate = date('Y/m/d H:i', $data['vacation']->est_begin_time);
} else {
    if ($data['vacation']->vacation_time_start == $data['vacation']->vacation_time_end) {
        $vacationDate = date('Y/m/d', $data['vacation']->vacation_time_start);
    }
    else {
        $vacationDate = date('Y/m/d', $data['vacation']->vacation_time_start) .' - '. date('Y/m/d', $data['vacation']->vacation_time_end);
    }
}
?>
<div>
    <div style="color:#274257;">
        <div style="font-family: 'Microsoft Yahei'">
            <p>致校园管理团队:<br />
            <p><?php echo $data['classTitle']; ?></p>
            <p>孩子姓名：<?php echo $data['childName']; ?></p>
            <p>类型：<?php echo $data['vacation']->getVacationType(); ?></p>
            <p>目标时间：<?php echo $vacationDate;?></p>
            <p>请假原因：<?php echo $data['vacation']->vacation_reason; ?></p>
            <?php if($data['vacation']->cancel_lunch): ?>
            <p>是否<?php echo $data['vacation']->stat == 1 ? '取消' : '恢复' ;?>午餐：<?php echo $data['vacation']->cancel_lunch ? '是' : '否' ;?></p>
            <?php endif; ?>
            <?php echo isset($data['test']) ? $data['test'] : ''; ?>
        </div>
        <div style="height:0; border-bottom: 1px solid #f2f2f2; margin: 0.2em 0;">&nbsp;</div>
        <div style="font-family: 'Microsoft Yahei'">
            <p>To School Administrative Team:<br />
            <p><?php echo $data['classTitle']; ?></p>
            <p>Student name：<?php echo $data['childName']; ?></p>
            <p>Type：<?php echo $data['vacation']->getVacationTypeEn(); ?></p>
            <p>Target Time：<?php echo $vacationDate;?></p>
            <p>Reason: for Application：<?php echo $data['vacation']->vacation_reason; ?></p>
            <?php if($data['vacation']->cancel_lunch): ?>
            <p><?php echo $data['vacation']->stat == 1 ? 'Cancel Lunch?' : 'Resume Lunch?' ;?>：<?php echo $data['vacation']->cancel_lunch ? 'Yes' : 'No' ;?></p>
            <?php endif; ?>
            <?php echo isset($data['test']) ? $data['test'] : ''; ?>
        </div>
    </div>
    <div style="height:0; border-bottom: 1px solid #f2f2f2; margin: 0.2em 0;">&nbsp;</div>
</div>