<?php

/**
 * This is the model class for table "ivy_dev_category".
 *
 * The followings are the available columns in table 'ivy_dev_category':
 * @property integer $id
 * @property integer $pid
 * @property integer $parent_id
 * @property string $class_type
 * @property string $en_title
 * @property string $cn_title
 * @property integer $weight
 */
class DevCategory extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return DevCategory the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_dev_category';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('pid, class_type', 'required'),
			array('pid, parent_id, weight', 'numerical', 'integerOnly'=>true),
			array('class_type', 'length', 'max'=>5),
			array('en_title, cn_title', 'length', 'max'=>500),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, pid, parent_id, class_type, en_title, cn_title, weight', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'pid' => 'Pid',
			'parent_id' => 'Parent',
			'class_type' => 'Class Type',
			'en_title' => 'En Title',
			'cn_title' => 'Cn Title',
			'weight' => 'Weight',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('pid',$this->pid);
		$criteria->compare('parent_id',$this->parent_id);
		$criteria->compare('class_type',$this->class_type,true);
		$criteria->compare('en_title',$this->en_title,true);
		$criteria->compare('cn_title',$this->cn_title,true);
		$criteria->compare('weight',$this->weight);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	public static function getConfig()
    {
        return array(
            'typeOf' => array(
                1 => array(9,81,126),
                2 => array(12,82,127),
                3 => array(13,83,128),
                4 => array(14,84,129),
                5 => array(15,85,130),
                6 => array(16,86,131),
                7 => array(17,87,132),
            ),
            'titleList' => array(
                Yii::t('labels','Personal/Social'),
                Yii::t('labels','Scientific Thinking'),
                Yii::t('labels','Mathematical Thinking'),
                Yii::t('labels','The Arts'),
                Yii::t('labels','Language and Literacy'),
                Yii::t('labels','Social Studies'),
                Yii::t('labels','Physical Development/Health'),
            ),
        );
    }
}