<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
class ScholarshipForm extends CFormModel
{
    // text
    public $academic_cn = '';
    public $academic_en = '';
    public $academic_form_cn = '';
    public $academic_form_en = '';
    public $academic_student_cn = '';
    public $academic_student_en = '';
    /*public $academic_Referrer_cn = '';
    public $academic_Referrer_en = '';*/
    public $academic_report_cn = '';
    public $academic_report_en = '';


    public function rules()
    {
        return array(
            //array('academic_cn, academic_en, academic_form_cn, academic_form_en, academic_student_cn, academic_student_en, academic_report_cn, academic_report_en', 'required'),
            array('academic_cn, academic_en, academic_form_cn, academic_form_en, academic_student_cn, academic_student_en, academic_report_cn, academic_report_en', 'safe'),
        );
    }

    /**
     * @return array customized attribute labels (name=>label)
     */
    public function attributeLabels()
    {
        return array(
            'academic_cn' => '介绍中文',
            'academic_en' => '介绍英文',
            'academic_form_cn' => '表单头中文',
            'academic_form_en' => '表单头英文',
            'academic_student_cn' => '学生中文',
            'academic_student_en' => '学生英文',
            /*'academic_Referrer_cn' => '推荐人中文',
            'academic_Referrer_en' => '推荐人英文',*/
            'academic_report_cn' => '资料中文',
            'academic_report_en' => '资料英文',
        );
    }
}
