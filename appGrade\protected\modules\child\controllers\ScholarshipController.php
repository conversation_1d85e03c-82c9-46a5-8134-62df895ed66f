<?php

class ScholarshipController extends ProNoChildController
{

    public function init()
    {
        parent::init();
        Mims::LoadHelper('HtoolKits');
        Yii::import('common.models.support.Scholarship');
    }

    public function actionIndex($startyear = 0)
    {
        $schoolid = 'BJ_DS';
        // 查找校历
        Yii::import('common.models.calendar.CalendarSchool');
        $calendarModel = CalendarSchool::model()->findByAttributes(array('startyear' => $startyear, 'branchid' => $schoolid));
        if (!$calendarModel) {
            return false;
        }
        $yid = $calendarModel->yid;
        $fid = current($this->myChildObjs)->fid;
        $mid = current($this->myChildObjs)->mid;
        // 查找是否已存在填写记录
        $model = $this->getModel($schoolid, $yid);

        // 保存数据
        if (Yii::app()->request->isPostRequest) {
            $reason = $_POST['Scholarship']['reason'];
            $childids = Yii::app()->request->getPost('childid', array());
            $childData = array();
            foreach ($childids as $childid) {
                if (isset($this->myChildObjs[$childid])) {
                    $childData[] = array('chilid' => $childid, 'classid' => $this->myChildObjs[$childid]->classid);
                }
            }
            $files = CUploadedFile::getInstances($model, 'file');

            $model->startyear = $startyear;
            $model->schoolid = $schoolid;
            $model->yid = $yid;
            $model->fid = $fid;
            $model->mid = $mid;
            $model->reason = $reason;
            $model->student = $childData ? json_encode($childData) : '';
            $model->status = 10;
            $model->attach = $files;
            $model->updated_by = Yii::app()->user->id;
            $model->updated_at = time();
            if (!$model->validate()) {
                print_r($model->getErrors());
            } else {
                $fileData = array();
                // 上传文件到oss
                $oss = CommonUtils::initOSS('private');
                $objectPath = 'scholarship/' . date('Ym') . '/';
                foreach ($files as $file) {
                    $id = uniqid();
                    $newFileName = $id .'.'. $file->getExtensionName();
                    if ($oss->uploadFile($objectPath . $newFileName, $file->getTempName())) {
                        $fileData[] = $objectPath . $newFileName;
                    }
                }
                $model->attach = $fileData ? json_encode($fileData) : '';
                if (!$model->save()) {
                    print_r($model->getErrors());
                } else {
//                    Yii::app()->user->setFlash('success', Yii::t("message", "Data Saved!"));
                    $this->redirect('/scholarship/'.$startyear);
                }
            }
        }
        // 查找父母的信息
        $parentData = array(
            'f' => array('name' => '', 'email' => '', 'phone' => ''),
            'm' => array('name' => '', 'email' => '', 'phone' => '')
        );
        if ($fid) {
            $userModel = User::model()->findByPk($fid);
            $parentData['f']['name'] = $userModel->getName();
            $parentData['f']['email'] = $userModel->email;
            $parentData['f']['phone'] = $userModel->parent->mphone;
        }
        if ($mid) {
            $userModel = User::model()->findByPk($mid);
            $parentData['m']['name'] = $userModel->getName();
            $parentData['m']['email'] = $userModel->email;
            $parentData['m']['phone'] = $userModel->parent->mphone;
        }

        $this->render('index', array(
            'parentData' => $parentData,
            'model' => $model,
            'myChildObjs' => $this->myChildObjs,
        ));
    }

    // 保存
    public function actionSave()
    {
        $model = new Scholarship();
        $file = CUploadedFile::getInstances($model, 'file');
        var_dump($file);
        die();
        $oss = CommonUtils::initOSS('private');
        $upload = $oss->upload_by_file('');

        //上传阿里云
        if ($upload->status == 200) {
            echo "success uploaded...";
        }

        // $oss = CommonUtils::initOSS('private');
        // $objectPath = 'admissions/' . date('Ym') . '/';
        // $id = uniqid();

        // if ($avatarFile) {
        //     $newFileName = $id . '_avatar' .'.'. $avatarFile->getExtensionName();

        //     if ($oss->uploadFile($objectPath . $newFileName, $avatarFile->getTempName())) {
        //         // 删除旧附件
        //         if ($this->child_avatar) {
        //             $oss->delete_object('admissions/' . $this->child_avatar);
        //         }
        //         $this->child_avatar = date('Ym', time()) . '/' . $newFileName;
        //     }
        // }
    }

    public function getModel($schoolid, $yid)
    {
        $crit = new CDbCriteria();
        $crit->compare('schoolid', $schoolid);
        $crit->compare('status', Scholarship::STATS_ACTIVE);
        $crit->compare('yid', $yid);
        $crit->compare('created_by', Yii::app()->user->id);
        $model = Scholarship::model()->find($crit);
        if (!$model) {
            $model = new Scholarship();
            $model->created_by = Yii::app()->user->id;
            $model->created_at = time();
        }
        return $model;
    }
}
