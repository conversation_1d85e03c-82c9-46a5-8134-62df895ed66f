<?php

/**
 * This is the model class for table "ivy_survey_option_group".
 *
 * The followings are the available columns in table 'ivy_survey_option_group':
 * @property integer $id
 * @property string $title_cn
 * @property string $title_en
 * @property integer $option_cat
 * @property string $schoolid
 * @property string $option_order
 * @property integer $status
 * @property integer $update_user
 * @property integer $update_time
 */
class WSurveyOptionGroup extends CActiveRecord
{
	const STATUS_NORMAL = 0;
	const STATUS_DELETED = 1;
	/**
	 * Returns the static model of the specified AR class.
	 * @return SurveyOptionGroup the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_survey_option_group';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
				array('option_cat, status, update_user, update_time', 'numerical', 'integerOnly'=>true),
				array('title_cn, title_en, schoolid, option_order', 'length', 'max'=>255),
				// The following rule is used by search().
				// Please remove those attributes that should not be searched.
				array('id, title_cn, title_en, option_cat, schoolid, option_order, status, update_user, update_time', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
				'options'=>array(self::HAS_MANY,'WSurveyOptions','group_id','order'=>'options.weight DESC,options.id asc'),
				'optionsWithKey'=>array(self::HAS_MANY,'WSurveyOptions','group_id','index'=>'id'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
				'id' => 'ID',
				'title_cn' => 'Title Cn',
				'title_en' => 'Title En',
				'option_cat' => 'Option Cat',
				'schoolid' => 'Schoolid',
				'option_order' => 'Option Order',
				'status' => 'Status',
				'update_user' => 'Update User',
				'update_time' => 'Update Time',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('title_cn',$this->title_cn,true);
		$criteria->compare('title_en',$this->title_en,true);
		$criteria->compare('option_cat',$this->option_cat);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('option_order',$this->option_order,true);
		$criteria->compare('status',$this->status);
		$criteria->compare('update_user',$this->update_user);
		$criteria->compare('update_time',$this->update_time);

		return new CActiveDataProvider($this, array(
				'criteria'=>$criteria,
		));
	}

    public function getTitle()
    {
        return Yii::app()->language == "zh_cn"  ? $this->title_cn : $this->title_en;
    }
}