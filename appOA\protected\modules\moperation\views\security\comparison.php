<?php

$monthAll = array( '09' => 9, 10 => 10, 11 => 11, 12 => 12, '01' => 1, '02' => 2, '03' => 3, '04' => 4, '05' => 5, '06' => 6, '07' => 7, '08' => 8);

?>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site','HQ Operations'), array('/moperation'))?></li>
        <li><?php echo CHtml::link(Yii::t('site','Basic Configurations'), array('/moperation'))?></li>
        <li class="active"><?php echo Yii::t('site','月累计事故及同期比较') ?></li>
    </ol>

    <div class="row">
        <div class="col-md-2 mb10">
            <?php
            $this->widget('zii.widgets.CMenu', array(
                'items' => $this->module->getHomeMenu(),
                'id' => 'pageCategory',
                'htmlOptions' => array('class' => 'nav nav-pills nav-stacked background-gray'),
                'activeCssClass' => 'active',
            ));
            ?>
        </div>
        <div class="col-md-10">
            <div class="row">
                <!-- 搜索框 -->
                <form  class="" action="<?php echo $this->createUrl('comparisonAccident'); ?>" method="get">
                    <div class="col-sm-2 form-group">
                        <?php echo CHtml::dropDownList('schoolid', $schoolid, $school, array('class'=> 'form-control', 'empty' => Yii::t('teaching', '校园'))) ?>
                    </div>
                    <div class="">
                        <div class="">
                            <button class="btn btn-default ml5" type="submit"><span class="glyphicon glyphicon-search"> </span> </button>
                        </div>
                    </div>
                </form>
            </div>

            <div class="row">
                <?php if($schoolid): ?>
                <div class="col-md-12">
                    <table class="table table-bordered" >
                        <tr>
                            <td  colspan="14" class="text-center bg-success"><?php echo Yii::t('safety','月累计事故及同期比较') ?></td>
                        </tr>
                        <tr>
                            <td class="text-center" rowspan="3"><?php echo Yii::t('safety','Month') ?></td>
                            <td class="text-center" colspan="6"><?php echo (date("m", time()) > 9) ? $time . '-' . date("Y", strtotime("+1 year")) : date("Y", strtotime("-1 year")) . '-' .  $time ?><?php echo Yii::t('safety','Months summary') ?></td>
                            <td class="text-center" rowspan="15" style="width: 1%"></td>
                            <td class="text-center" colspan="6"><?php echo (date("m", time()) > 9) ? date("Y", strtotime("-1 year")) . '-' . $time : date("Y", strtotime("-2 year")) . '-' .  date("Y", strtotime("-1 year")) ?><?php echo Yii::t('safety','Months summary') ?></td>
                        </tr>
                        <tr>
                            <td class="text-center" rowspan="2"><?php echo Yii::t('safety','Total') ?></td>
                            <td class="text-center" colspan="2"><?php echo Yii::t('safety','Accident Type') ?>  </td>
                            <td class="text-center" colspan="3"><?php echo Yii::t('safety','Seriousnes') ?></td>
                            <td class="text-center" rowspan="2"><?php echo Yii::t('safety','Total') ?></td>
                            <td class="text-center" colspan="2"><?php echo Yii::t('safety','Accident Type') ?></td>
                            <td class="text-center" colspan="3"><?php echo Yii::t('safety','Seriousnes') ?></td>
                        </tr>
                        <tr>
                            <td class="text-center" ><?php echo Yii::t('safety','Unpreventable') ?> </td>
                            <td class="text-center"><?php echo Yii::t('safety','Preventable') ?></td>
                            <td class="text-center"><?php echo Yii::t('safety','Level 1') ?> </td>
                            <td class="text-center"><?php echo Yii::t('safety','Level 2') ?> </td>
                            <td class="text-center"><?php echo Yii::t('safety','Level 3') ?> </td>
                            <td class="text-center"><?php echo Yii::t('safety','Unpreventable') ?></td>
                            <td class="text-center"><?php echo Yii::t('safety','Preventable') ?> </td>
                            <td class="text-center"><?php echo Yii::t('safety','Level 1') ?></td>
                            <td class="text-center"><?php echo Yii::t('safety','Level 2') ?></td>
                            <td class="text-center"><?php echo Yii::t('safety','Level 3') ?></td>
                        </tr>
                        <?php foreach ($monthAll as $key=>$item){ ?>
                            <tr>
                                <td class="text-center" ><?php echo $item . "月" ?></td>

                                <td class="text-center" ><?php echo isset($comparisonAccidentsNew[$key]) ? $comparisonAccidentsNew[$key] : "" ;?></td>
                                <td class="text-center" ><?php echo isset($typeNew[$key]['accident']) ? count($typeNew[$key]['accident']) : "" ;?></td>
                                <td class="text-center" ><?php echo isset($typeNew[$key]['responsibility']) ? count($typeNew[$key]['responsibility']) : "" ;?></td>

                                <td class="text-center" ><?php echo isset($reportNew[$key][1]) ? count($reportNew[$key][1]) : "" ;?></td>
                                <td class="text-center" ><?php echo isset($reportNew[$key][2]) ? count($reportNew[$key][2]) : "" ;?></td>
                                <td class="text-center" ><?php echo isset($reportNew[$key][3]) ? count($reportNew[$key][3]) : "" ;?></td>

                                <td class="text-center" ><?php echo isset($comparisonAccidentsOld[$key]) ? $comparisonAccidentsOld[$key] : "" ;?></td>
                                <td class="text-center" ><?php echo isset($typeOld[$key]['accident']) ? count($typeOld[$key]['accident']) : "" ;?></td>
                                <td class="text-center" ><?php echo isset($typeOld[$key]['responsibility']) ? count($typeOld[$key]['responsibility']) : "" ;?></td>
                                <td class="text-center" ><?php echo isset($reportOld[$key][1]) ? count($reportOld[$key][1]) : "" ;?></td>
                                <td class="text-center" ><?php echo isset($reportOld[$key][2]) ? count($reportOld[$key][2]) : "" ;?></td>
                                <td class="text-center" ><?php echo isset($reportOld[$key][3]) ? count($reportOld[$key][3]) : "" ;?></td>
                            </tr>
                        <?php } ?>
                    </table>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>