<?php

class TrainingController extends BranchBasedController
{
    public function init()
    {
        parent::init();

        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'campusOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site', 'Campus Workspace');

        //初始化选择校园页面
        $schoolList = CommonUtils::dsSchoolList();
        foreach ($this->accessBranch as $key => $item) {
            if (!in_array($item, $schoolList)) {
                unset($this->accessBranch[$key]);
            }
        }
        $this->branchSelectParams['urlArray'] = array('//mcampus/training/index');

        $cs = Yii::app()->clientScript;
        $cs->registerCoreScript('jquery.ui');
        $cs->registerCssFile($cs->getCoreScriptUrl() . '/jui/css/base/jquery-ui.css');
        $cs->registerCssFile(Yii::app()->themeManager->baseUrl . '/base/js/viewer/viewer.css');

        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/element/vue.global.js');
        $cs->registerCssFile(Yii::app()->themeManager->baseUrl . '/base/js/element/index.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/element/index.js');
        // 七牛上传所需文件
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/plupload/plupload.full.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/sortable/Sortable.min.js');

        
    }
    public function createUrl($route, $params = array(), $ampersand = '&', $parentOnly = false) {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        return parent::createUrl($route, $params, $ampersand);
    }
    public function actionIndex() 
    {
        $this->render('index');    
    }

    public function actionInfo() 
    {
        $this->render('info');    
    }

    public function actionApi()
    {
        $url = Yii::app()->request->getParam('url');
        if (!$url) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', 'url error');
            $this->showMessage();
        }
        $remoteUrl = 'training/' . $url;
        if ($_GET) {
            $getPath = '';
            foreach ($_GET as $key => $value) {
                if ($getPath == '') {
                    $getPath .= "$key=$value";
                } else {
                    $getPath .= "&$key=$value";
                }
            }
            $remoteUrl = $remoteUrl . '?' . $getPath;
        }
        $requestData = $_POST;
        $this->remote($remoteUrl, $requestData);
    }

    public function remote($requestUrl, $requestData = array())
    {
        if (empty($requestData['schoolId'])) {
            $requestData['school_id'] = $this->branchId;
        }
        $res = CommonUtils::requestDsOnline($requestUrl, $requestData);
        if (isset($res['code']) && $res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('message', $res['msg']);
            $this->addMessage('data', $res['data']);
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->addMessage('data', $res['data']);
        }
        $this->showMessage();
    }

}