<?php

class EFullCalendar extends CWidget
{

    public $mainPath = '/fullcalendar-2.2.0';
    /**
     * @var string Google's calendar URL.
     */
    public $googleCalendarUrl;

    /**
     * @var string Theme's CSS file.
     */
    public $themeCssFile;

    public $fullCalendarCss=null;

    /**
     * @var array FullCalendar's options.
     */
    public $options=array();

    /**
     * @var array HTML options.
     */
    public $htmlOptions=array();

    /**
     * @var bool
     */
    public $loadPrintCss=false;

    /**
     * @var string Language code as ./locale/<code>.php file
     */
    public $lang;

    public function run()
    {
        $this->registerFiles();
        $this->showOutput();
    }

    protected function registerFiles()
    {
        $assetsDir=(defined(__DIR__) ? __DIR__ : dirname(__FILE__));
        $assets=Yii::app()->assetManager->publish($assetsDir);

        $ext=defined('YII_DEBUG') ? 'js' : 'min.js';
        $cs=Yii::app()->clientScript;
        $cs->registerCoreScript('jquery');
        $cs->registerScriptFile($assets.$this->mainPath.'/lib/moment.min.js');
        $cs->registerScriptFile($assets.$this->mainPath.'/lib/jquery-ui.custom.min.js');
        if(empty($this->fullCalendarCss))
            $cs->registerCssFile($assets.$this->mainPath.'/fullcalendar.css');
        else
            $cs->registerCssFile($this->fullCalendarCss);
        $cs->registerScriptFile($assets.$this->mainPath.'/fullcalendar.'.$ext);
        $langFile = (strpos(Yii::app()->language,'zh') === false ) ? 'en-us' : 'zh-cn';
        $cs->registerScriptFile($assets.$this->mainPath.'/lang/'.$langFile.'.js');

        if ($this->loadPrintCss) {
            $cs->registerCssFile($assets.$this->mainPath.'/fullcalendar.print.css');
        }
        if ($this->googleCalendarUrl) {
            $cs->registerScriptFile($assets.$this->mainPath.'/gcal.js');
            $this->options['events']=$this->googleCalendarUrl;
        }
        if ($this->themeCssFile) {
            $this->options['theme']=true;
            $cs->registerCssFile($assets.$this->mainPath.'/lib/'.$this->themeCssFile);
        }

        $js='$("#'.$this->id.'").fullCalendar('.CJavaScript::encode($this->options).');';
        $cs->registerScript(__CLASS__.'#'.$this->id, $js, CClientScript::POS_READY);
    }

    protected function showOutput()
    {
        if (! isset($this->htmlOptions['id']))
            $this->htmlOptions['id']=$this->id;

        echo CHtml::tag('div', $this->htmlOptions,'');
    }
}