<?php

/**
 * This is the model class for table "ivy_calendar_outsite".
 *
 * The followings are the available columns in table 'ivy_calendar_outsite':
 * @property integer $id
 * @property integer $calendarid
 * @property string $month
 * @property integer $month_stamp
 * @property string $schoolid
 * @property string $memo_cn
 * @property string $memo_en
 * @property integer $userid
 * @property integer $updated_timestamp
 */
class CalendarOutsite extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CalendarOutsite the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_calendar_outsite';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('month, schoolid', 'required'),
			array('calendarid, month_stamp, userid, updated_timestamp', 'numerical', 'integerOnly'=>true),
			array('month', 'length', 'max'=>7),
			array('schoolid', 'length', 'max'=>100),
			array('memo_cn, memo_en', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, calendarid, month, month_stamp, schoolid, memo_cn, memo_en, userid, updated_timestamp', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'calendarid' => 'Calendarid',
			'month' => 'Month',
			'month_stamp' => 'Month Stamp',
			'schoolid' => 'Schoolid',
			'memo_cn' => 'Memo Cn',
			'memo_en' => 'Memo En',
			'userid' => 'Userid',
			'updated_timestamp' => 'Updated Timestamp',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('calendarid',$this->calendarid);
		$criteria->compare('month',$this->month,true);
		$criteria->compare('month_stamp',$this->month_stamp);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('memo_cn',$this->memo_cn,true);
		$criteria->compare('memo_en',$this->memo_en,true);
		$criteria->compare('userid',$this->userid);
		$criteria->compare('updated_timestamp',$this->updated_timestamp);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}