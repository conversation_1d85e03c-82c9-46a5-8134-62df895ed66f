<?php 
$purchaseProducts = Yii::app()->request->getParam('PurchaseProducts','');
 ?>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site','HQ Operations'), array('default/index'));?></li>
        <li><?php echo CHtml::link(Yii::t('site','Support'), array('default/index'));?></li>
        <li class="active"><?php echo Yii::t('site','标配管理') ;?></li>
    </ol>
    <div class="row">
        <div class="col-md-2">
            <div class="list-group" id="classroom-status-list">
                <a href="<?php echo $this->createUrl('purchaseProducts/index');?>" class="list-group-item status-filter ">商品管理</a>
                <a href="<?php echo $this->createUrl('purchaseStandard/index');?>" class="list-group-item status-filter active">标配管理</a>
                <a href="<?php echo $this->createUrl('purchaseOrder/index');?>" class="list-group-item status-filter">生成订单</a>
                <a href="<?php echo $this->createUrl('purchaseOrder/orders');?>" class="list-group-item status-filter">订单管理</a>
            </div>
        </div>
        <div class="col-md-10">
            <div class="mb10">
                <button class="btn btn-primary" onclick="update(0)"><span class="glyphicon glyphicon-plus"></span> 新建标配</button>
            </div>
            <div class="panel panel-default">
                <div class="panel-body">
                <?php
                $this->widget('ext.ivyCGridView.BsCGridView', array(
                    'id'=>'purchase-product-grid',
                    'afterAjaxUpdate'=>'js:function(){head.Util.ajaxDel()}',
                    'dataProvider'=>$model->search(),
                    'colgroups'=>array(
                        array(
                            "colwidth"=>array(120,40,20,20,20,80),
                        )
                    ),
                    'columns'=>array(
                        'cn_title',
                        'school_type',
                        'pro_type',
                        array(
                            'name'=>'update_timestamp',
                            'value'=>'CommonUtils::formatDateTime($data->update_timestamp, "medium", "short")',
                            ),
                        array(
                            'name'=>'status',
                            'value'=>'$data->status?"可用":"不可用"',
                            ),
                        array(
                           'name'=>'操作',
                           'value'=>array($this,'showBtn'),
                            ),
                        ),
                )); ?>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- 模态框 -->
<div class="modal fade" id="modal" tabindex="-1" role="dialog" aria-labelledby="modal">
    <div class="modal-dialog" role="document">
        <div class="modal-content">

        </div>
    </div>
</div>

<!-- script代码 -->
<script>
//显示编辑界面
function update (id) {
    $('#modal  .modal-content').load('<?php echo $this->createUrl('update') ?>'+'?id='+id,function () {
        $('#modal').modal({
          show: true,
          backdrop: 'static'
        });
        head.Util.ajaxForm( $('#modal') );
    })
}
//标配列表
function standardList (pid) {
    $('#modal  .modal-content').load('<?php echo $this->createUrl('standardList') ?>'+'?id='+pid,function () {
        $('#modal').modal({
          show: true,
          backdrop: 'static'
        });
        head.Util.ajaxForm( $('#modal') );
    })
}
//复制标配
function copyStandard (btn,sid) {
    $('#modal  .modal-content').load('<?php echo $this->createUrl('copyStandard') ?>'+'?id='+sid,function () {
        $('#modal').modal({
          show: true,
          backdrop: 'static'
        });
        head.Util.ajaxForm( $('#modal') );
    });
}
// 回调：标配添加成功
function cbStandard() {
    $('#modal').modal('hide');
    $.fn.yiiGridView.update('purchase-product-grid');
}
</script>
