<?php
$shortCuts = array(
    'public' => array(
        'label' => Yii::t('site','Public Information'),
        'items' => array(
            'grouping' => array('label'=>Yii::t('site','Teachers Grouping'), 'url'=>array('//mgrades/default/teacherGroup')),
            'schedule' => array('label'=>Yii::t('site','Schedule Overview'), 'url'=>array('//mgrades/schedule/index')),
            'classes' => array('label'=>Yii::t('site','Class Management'), 'url'=>array('//mcampus/classes/index')),
        )
    ),
    'campusAdmin' => array(
        'label' => Yii::t('site','Campus Admin'),
        'items' => array(
            'students' => array('label'=>Yii::t('site','Student Management'), 'url'=>array('//mcampus/student/index','category'=>'current'),'roles'=>array('ivystaff'),'itemOptions'=>array('title'=>'注册学生、管理学生')),
            'calendar' => array('label'=>Yii::t('site','School Calendar Management'), 'url'=>array('//mcampus/calendarNew/index'),'roles'=>array('ivystaff')),
            'schedule' => array('label'=>Yii::t('site','Schedule Management'), 'url'=>array('//mgrades/schedule/index'),'roles'=>array('ivystaff_it')),
            'staff' => array('label'=>Yii::t('site','Staff Management'), 'url'=>array('//mcampus/staffProfile/index'),'roles'=>array('ivystaff_it')),
        )
    ),
    'billings' => array(
        'label' => Yii::t('site','Invoice & Billing'),
        'items' => array(
            'invoice' => array('label'=>Yii::t('payment','Invoice Management'), 'url'=>array('//mfinance/invoice/index'),'roles'=>array('ivystaff_opschool', 'ivystaff_it')),
            'overdue' => array('label'=>Yii::t('site','Overdue Reminder'), 'url'=>array('//mcampus/billings/overdues'),'roles'=>array('ivystaff_it')),
        )
    )
);

foreach($shortCuts as $sc):?>
    <div class="col-md-4">
        <h4><?php echo $sc['label'];?></h4>
        <ul class="list-unstyled">
            <?php foreach($sc['items'] as $_item): ?>
            <li class="mb5">
                <?php echo CHtml::link($_item['label'], $_item['url']); ?>
            </li>
            <?php endforeach; ?>
        </ul>
    </div>
<?php endforeach;?>