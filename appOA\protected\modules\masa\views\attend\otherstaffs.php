<style>
    .bggray {
        background: #eee;
        border-radius: 4px;
    }

    [v-cloak] {
        display: none;
    }
</style>
<?php if ($groupId): ?>
    <div class="modal fade" id="operatorVueModel" tabindex="-1" data-backdrop="static">
        <div class="modal-dialog" role="document">
            <form action="<?php echo $this->createUrl('addOtherStaffs') ?>" method="post" class="J_ajaxForm">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                                aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title" id="myModalLabel">运营人员考勤</h4>
                    </div>
                    <div class="modal-body">
                        <input type="hidden" name="courseGroupId" id="courseGroupId"/>
                        <input type="hidden" name="id" id="id"/>
                        <input type="hidden" name="courseStaffId" id="courseStaffId"/>

                        <div class="form-group">
                            <label for="">老师姓名</label>
                            <input class="form-control" id="operatorname" readonly>
                        </div>
                        <div class="form-group">
                            <label for="">教师职位</label>
                            <input class="form-control" id="operatorTypeName" readonly>
                        </div>
                        <div class="form-group">
                            <label>考勤周期</label>

                            <div id="attendanceType">
                            </div>
                        </div>

                        <label for="">签到时间</label>

                        <div class="form-inline mb15">
                            <label class="datestartName" for="">开始</label>
                            <input type="text" class="form-control ml10 mr10 datestart datepicker" name="dateStart"
                                   id="" placeholder="请点击选择时间">
                            <label class="dateendName" for="">结束</label>
                            <input type="text" class="form-control ml10 mr10 dateend datepicker" name="dateEnd" id=""
                                   placeholder="">
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary J_ajax_submit_btn">保存</button>
                        <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                        <input type="reset" class="_reset" style="display: none"/>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <div class="modal fade" id="detailModel" tabindex="-1" data-backdrop="static">
        <div class="modal-dialog" role="document">
            <div class="modal-content" v-if="detailModelData">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="myModalLabel">{{detailModelData.daytime}} 详细信息</h4>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="form-group">
                            <label class="col-xs-2 text-right"><label for="AsaCourseGroup_title_cn">课程列表</label></label>

                            <div class="col-xs-10"><p v-for="course in detailModelData.course">{{course}}</p></div>
                        </div>

                    </div>

                    <div class="row">
                        <div class="form-group">
                            <label class="col-xs-2 text-right"><label for="AsaCourseGroup_title_cn">工作人员</label></label>

                            <div class="col-xs-10"><p v-for="courseStaff in detailModelData.courseStaff"
                                                      v-html="courseStaff"></p></div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">确定</button>
                </div>
            </div>
        </div>
    </div>
<?php endif; ?>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('//mcampus/default/index')) ?></li>
        <li><?php echo CHtml::link(Yii::t('site', 'Routines'), array('//mcampus/default/index')) ?></li>
        <li class="active"><?php echo Yii::t('site', '课后课管理') ?></li>
    </ol>

    <div class="row">
        <div class="col-md-2 mb10">
            <?php
            $this->widget('zii.widgets.CMenu', array(
                'items' => $this->module->getMenu(),
                'id' => 'pageCategory',
                'htmlOptions' => array('class' => 'nav nav-pills nav-stacked background-gray'),
                'activeCssClass' => 'active',
            ));
            ?>
        </div>
        <div class="col-md-2">
            <?php
            $this->widget('zii.widgets.CMenu', array(
                'items' => $this->module->getAttendSubMenu(),
                'id' => 'pageCategory',
                'htmlOptions' => array('class' => 'nav nav-pills nav-stacked background-gray'),
                'activeCssClass' => 'active',
            ));
            ?>
        </div>
        <div class="col-md-8">
            <!-- Nav tabs -->
            <?php
            $this->widget('zii.widgets.CMenu', array(
                'items' => $this->module->getAttendMenu(),
                'id' => 'AttendMenu',
                'htmlOptions' => array('class' => 'nav nav-tabs'),
                'activeCssClass' => 'active',
            ));
            ?>
            <div class="tab-content pt10">
                <div class="col-md-12 mb10">
                    <select autocomplete="off" class="form-control" style="width: auto" id="selectstartyear">
                        <option value="">请选择学年</option>
                        <?php
                        if(empty($_GET['year'])){
                            //默认最新的学年
                            $getUrlYear = $startyearArr[0];
                        }else{
                            $getUrlYear = $_GET['year'];
                        }
                        foreach ($startyearArr as $value){
                            if(Yii::app()->language == 'zh_cn'){
                                $msg = ' 学年';
                            }else{
                                $msg =' School Year';
                            }
                            $schoolYear = $value.'-'.($value+1).$msg;
                            if($value == $getUrlYear){
                                echo "<option value='$value' selected>$schoolYear</option>";
                            }else{
                                echo "<option value='$value'>$schoolYear</option>";
                            }
                        }
                        ?>
                    </select>
                    <br>
                    <label class="control-label" for="inputSuccess1">请选择课程组</label>
<!--                    <ul class="nav nav-pills">-->
<!--                        --><?php
//
//                        //显示课程组
//                        foreach ($course_group as $gid => $group) {
//                            //print_r($group['courseGroupName']);
//                            $href = $this->createUrl('otherStaffs', array('groupId' => $gid));
//                            $active = '';
//
//                            if ($gid == $groupId) {
//                                $active = 'active';
//                            }
//
//                            echo "<li class='{$active} background-gray mr5 mb5'><a href='{$href}'>";
//                            echo $group['courseGroupName'];
//                            echo '</a></li>';
//                        }
//                        ?>
<!--                    </ul>-->

                    <?php
                    echo "<span class=span_year$year>";
                    foreach ($newCourseGroup as $year => $courseGroup){
                        if($getUrlYear == $year){
                            echo "<ul class='nav nav-pills mb20 year$year'>";
                        }else{
                            echo "<ul class='nav nav-pills mb20 year$year' style='display: none'>";
                        }
                        foreach ($courseGroup as $v) {
                            $href = $this->createUrl('otherStaffs', array('groupId' => $v['id'],'year'=>$year));
                            $active = '';
                            $title = $v['courseGroupName'];
                            if (!empty($groupId) && $v['id'] == $groupId) {
                                $active = 'active';
                            }
                            echo "<li class='background-gray mr5 mb5 {$active}'><a href='{$href}'>";
                            echo $title;
                            echo '</a></li>';
                        }
                        echo '</ul>';
                    }
                    echo '</span>';
                    ?>

                </div>
                <?php if ($groupId && $groupObj->program_type != AsaCourseGroup::PROGRAM_DELAYCARE) { ?>
                    <div class="col-md-12" id="operatorlistVueModel" v-cloak>
                        <hr/>
                        <div v-if="all_operatorlist[groupId]">
                            <div class="panel panel-default" v-cloak>
                                <div class="panel-heading">添加运营人员考勤</div>
                                <div class="panel-body">
                                    <a class="btn btn-default mr10 mb10" v-for="alllist in all_operatorlist[groupId]"
                                       :_value="alllist.operatorId"
                                       :href="'javascript:operatorModal('+ alllist.operatorId + ',' + alllist.courseStaffId +')'">{{alllist.operatorName}}
                                        {{alllist.operatorTypeName}}</a>
                                </div>
                            </div>
                            <div class="table-responsive" v-if="operatorlist[groupId]">
                                <table class="table table-hover">
                                    <colgroup>
                                        <col width="100">
                                        <col width="100">
                                        <col width="100">
                                        <col width="100">
                                        <col width="100">
                                        <col width="100">
                                        <col width="100">
                                        <col width="100">
                                        <col width="200">
                                    </colgroup>
                                    <thead>
                                    <tr>
                                        <th>名字</th>
                                        <th>课时费</th>
                                        <th>开始日期</th>
                                        <th>结束日期</th>
                                        <th>考勤周期</th>
                                        <th>教师职位</th>
                                        <th></th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <tr v-for="list in operatorlist[groupId]" v-cloak>
                                        <td>{{list.operatorName}}</td>
                                        <td>{{list.operatorPrice}}</td>
                                        <td class="text-info" style="cursor: pointer" v-if="list.checkInType == 2"
                                            @click="detailModel(groupId,list.dateStart)"><u>{{list.dateStart}}</u></td>
                                        <td v-else>{{list.dateStart}}</td>
                                        <td class="text-info" style="cursor: pointer" v-if="list.checkInType == 2"
                                            @click="detailModel(groupId,list.dateEnd)"><u>{{list.dateEnd}}</u></td>
                                        <td v-else>{{list.dateEnd}}</td>
                                        <td>{{list.checkInTypeName}}</td>
                                        <td>{{list.operatorTypeName}}</td>
                                        <td class="button-column">
                                            <a class="btn btn-info btn-xs mb5" title="编辑"
                                               :href="'javascript:operatorModal('+ list.operatorId + ',' + list.courseStaffId + ',' + list.id + ')'">编辑</a>
                                            <a class="btn btn-danger btn-xs mb5 J_ajax_del" title="删除"
                                               :href="'<?php echo $this->createUrl('deleteOtherStaffs') ?>&id='+list.id">删除</a>

                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="col-md-12" v-else>暂无数据</div>
                    </div>
                    <script>
                        var all_operatorlist = <?php echo CJSON::encode($all_operatorlist); ?>;
                        var operatorlist = <?php echo CJSON::encode($operatorlist); ?>;
                        var groupId = <?php echo CJSON::encode($groupId); ?>;
                        $('#courseGroupId').val(groupId);

                        var operatorlistVue = new Vue({  //运营人员列表模型
                            el: "#operatorlistVueModel",
                            data: {
                                all_operatorlist:all_operatorlist,
                                operatorlist:operatorlist,
                                groupId:groupId
                            },
                            updated: function () {
                                $('.editmodal').on('click', function (id) {
                                    $('#operatorVueModel').modal();
                                });
                                head.Util.ajaxDel();

                            },
                            created: function () {
                            },
                            methods: {
                                addOperator: function (data) {
                                    if (this.operatorlist[groupId]) {
                                        this.$set(this.operatorlist[groupId], data.id, data);
                                    } else {
                                        this.$set(this.operatorlist, groupId, {});
                                        this.$set(this.operatorlist[groupId], data.id, data);
                                    }
                                },
                                delOperator: function (id) {
                                    this.$delete(this.operatorlist[groupId], id);
                                },
                                detailModel: function (id, time) {
                                    $.ajax({
                                        url: '<?php echo $this->createUrl('infoByDate') ?>',
                                        data: {groupId: id, dayTime: time},
                                        type: 'POST',
                                        timeout: 5000,
                                        dataType: 'json',
                                        global: false
                                    }).done(function (data) {
                                        detailModelVue.detailModelData = data;
                                        $('#detailModel').modal();
                                    }).fail(function (xml, status, text) {

                                    });
                                }

                            }
                        });
                        var all_operatorlistVue = new Vue({  //运营人员模态框模型
                            el: "#operatorVueModel",
                            data: {
                                all_operatorlist:all_operatorlist
                            },
                            updated: function () {

                            },
                            created: function () {
                            },
                            methods: {}
                        });
                        var detailModelData;
                        var detailModelVue = new Vue({  //日期课程详细内容
                            el: "#detailModel",
                            data: {
                                detailModelData:detailModelData
                            }
                        });
                        function operatorModal(operatorId, courseStaffId, id) {      //呼出模态框
                            var edit = true;
                            $('._reset').click();
                            $('#id').val(id);
                            $('#operatorId').val(operatorId);
                            $('#courseStaffId').val(courseStaffId);
                            $(".datepicker").datepicker({
                                dateFormat: 'yy-mm-dd'
                            });
                            $('#operatorVueModel').modal();
                            $('#attendanceType').empty();
                            if (id != undefined) { //编辑
                                $.each(all_operatorlist[groupId], function (i, l) {
                                    if (l.courseStaffId == courseStaffId) {
                                        $('#attendanceType').append('<div class="radio">' + l.unitType.title + '</div>');
                                        if (l.unitType.unitType == 2) {
                                            edit = false;
                                            $('.dateend').hide();
                                            $('.dateendName').hide();
                                            $('.datestartName').text('签到日期');
                                        } else {
                                            $('.datestartName').text('开始');
                                            $('.dateend').show();
                                            $('.dateendName').show();
                                        }
                                        $("#operatorTypeName").val(l.operatorTypeName);
                                        $("#operatorname").val(l.operatorName);
                                    }
                                });
                                $.each(operatorlist[groupId], function (i, l) {
                                    if (l.id == id) {
                                        if (edit) {
                                            $('.dateend').val(l.dateEnd);
                                        }
                                        $('.datestart').val(l.dateStart);
                                    }
                                });
                            } else {
                                $.each(all_operatorlist[groupId], function (i, l) {
                                    if (l.courseStaffId == courseStaffId) {
                                        $('#attendanceType').append('<div class="radio">' + l.unitType.title + '</div>');
                                        if (l.unitType.unitType == 2) {
                                            $('.dateend').hide()
                                            $('.dateendName').hide()
                                            $('.datestartName').text('签到日期');
                                            $('.datestart').val(l.unitType.dateStart)
                                        } else {
                                            $('.datestartName').text('开始');
                                            $('.datestart').val(l.unitType.dateStart);
                                            $('.dateend').val(l.unitType.dateEnd).show();
                                            $('.dateendName').show();
                                        }
                                        $("#operatorTypeName").val(l.operatorTypeName);
                                        $("#operatorname").val(l.operatorName);
                                    }
                                });
                            }

                        }
                        function addoperator(data) {      //增加成功后回调
                            operatorlistVue.addOperator(data);
                            $('#operatorVueModel').modal('hide');
                            location.reload();
                        }
                        function deloperator(id) {     //删除成功后回调
                            operatorlistVue.delOperator(id.id);

                        }

                        function cbTeacher() {
                            $('#modal').modal('hide');
                            window.location.reload(true);
                        }
                    </script>
                <?php }else if ($groupId && $groupObj->program_type == AsaCourseGroup::PROGRAM_DELAYCARE){ ?>
                    <div class="col-md-12" id="delaylistVue" v-cloak>
                        <div class="panel panel-default">
                            <div class="panel-heading">延时课人员考勤</div>
                            <div class="panel-body">
                                <span class="mr15 mb10"
                                      v-for="(teacher,tid) in teacherList">{{teacher}}</span>
                            </div>
                        </div>
                        <div class="panel panel-default" v-for="(data,time) in childExtra">
                            <div class="panel-heading">
                                <h4>
                                    <span>{{data.time}}</span>
                                    <small>延时服务名单</small>
                                </h4>
                            </div>
                            <ul class="list-group">

                                <li class="list-group-item bg-warning" v-for="d in isObject(data)"
                                    v-if="d.teacher == '' || isObject(d.teacher)">
                                    <div class="page-header">
                                        <h4>
                                            {{d.courseName}}
                                        </h4>
                                    </div>
                                    <div class="form-group" v-for="classes in d.classes">
                                        <dl class="dl-horizontal">
                                            <dt style="text-align: left">{{classes.className}}</dt>
                                            <dd>
                                                <span v-for="c in isObject(classes)" v-if="isObject(c)" class="">
                                                    <span style="color:#999;display: inline-block;min-width:6%">{{c.id}}
                                                    <span class="text-danger">{{c.status}}</span>
                                                    </span>
                                                </span>

                                            </dd>
                                        </dl>
                                    </div>
                                    <span class="text-success pull-right" v-if="d.teacher != ''">
                                        <span class="glyphicon glyphicon-ok"></span> <span>{{teacherList[d.teacher.teacher]}}</span>
                                    </span>

                                    <div class="clearfix"></div>
                                    <a class="btn btn-info btn-xs pull-right"
                                       @click="delayModel(data.time,d.courseId,d.courseName)"
                                       :key="1"
                                       v-if="d.teacher == ''" title="签到">签到</a>
                                    <a class="btn btn-danger btn-xs pull-right J_ajax_del" title="取消签到" :key="2" v-else
                                       :href="'<?php echo $this->createUrl('deleteExtra') ?>&id='+d.teacher.id">取消签到</a>

                                    <div class="col-md-12">
                                        <ul class="list-inline"></ul>
                                    </div>
                                    <div class="clearfix"></div>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div class="modal fade" id="delayModel" tabindex="-1" data-backdrop="static">
                        <div class="modal-dialog" role="document">
                            <form action="<?php echo $this->createUrl('addDelay') ?>" method="post" class="J_ajaxForm">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <button type="button" class="close" data-dismiss="modal"
                                                aria-label="Close"><span
                                                aria-hidden="true">&times;</span></button>
                                        <h4 class="modal-title modalTitle"></h4>
                                    </div>
                                    <div class="modal-body">
                                        <div class="">
                                            <div class="form-group">
                                                <label for="">老师签到</label>
                                                <select class="form-control" name="teacherId">
                                                    <option value="">请选择</option>
                                                    <option :value="tid" v-for="(teacher,tid) in teacherList">
                                                        {{teacher}}
                                                    </option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-info J_ajax_submit_btn"
                                                data-dismiss="modal">签到
                                        </button>
                                    </div>
                                </div>
                                <input type="hidden" name="groupId" id="groupId"/>
                                <input type="hidden" name="courseId" :value="courseId"/>
                                <input type="hidden" name="date" :value="courseDate"/>
                                <input type="reset" class="_reset" style="display: none"/>
                            </form>
                        </div>
                    </div>
                    <script>
                        var _childExtra = <?php echo CJSON::encode($childExtra); ?>; //日期数组
                        var childExtra;
                        var groupId = <?php echo CJSON::encode($groupId); ?>;
                        $('#groupId').val(groupId);
                        var teacherList = <?php echo CJSON::encode($teacherList); ?>;
                        var delaylistVue = new Vue({  //延时课运营人员列表模型
                            el: "#delaylistVue",
                            data: {
                                childExtra: [],
                                teacherList:teacherList,
                                modalTitle: ""
                            },
                            updated: function () {
                                head.Util.ajaxDel();
                            },
                            created: function () {
                                var _this = this;
                                $.each(_childExtra, function (i, l) {
                                    _this.childExtra.unshift(l)
                                });
                            },
                            methods: {
                                delayModel: function (time, courseId, title) {
                                    $('.modalTitle').text(title)
                                    $('._reset').click();
                                    delayModel.courseDate = time;
                                    delayModel.courseId = courseId;
                                    $('#delayModel').modal();
                                },
                                compare: function (property) {
                                    return function (a, b) {
                                        var value1 = a[property];
                                        var value2 = b[property];
                                        return value1 - value2;
                                    }
                                },
                                isObject: function (data) {
                                    if (typeof data == "object") {
                                        return data;
                                    } else {
                                        return false;
                                    }
                                }, isEmpty: function (data) {
                                    return Object.keys(data).length;
                                }, addDelay: function (data) {
                                    $.each(this.childExtra, function (i, list) {
                                        if (data.data == list.time) {
                                            $.each(list, function (i, l) {
                                                if (typeof l == 'object') {
                                                    if (l.courseId == data.courseId) {
                                                        l.teacher = {"teacher": data.teacherId, "id": data.id}
                                                    }
                                                }
                                            });
                                        }
                                    });
                                },
                                delDelay: function (data) {
                                    console.log(data)
                                    $.each(this.childExtra, function (i, list) {
                                        if (data.data == list.time) {
                                            $.each(list, function (i, l) {
                                                if (typeof l == 'object') {
                                                    if (l.courseId == data.courseId) {
                                                        l.teacher = "";
                                                    }
                                                }
                                            });
                                        }
                                    });
                                }
                            }, computed: {}
                        });
                        var delayModel = new Vue({  //延时课签到模态框
                            el: "#delayModel",
                            data: {
                                teacherList:teacherList,
                                courseId: "",
                                courseDate: ""
                            },
                            updated: function () {

                            },
                            created: function () {
                            },
                            methods: {}
                        });
                        function addDelay(data) {      //增加成功后回调
                            delaylistVue.addDelay(data);
                            $('#delayModel').modal('hide');
                        }
                        function delDelay(data) {     //删除成功后回调
                            delaylistVue.delDelay(data);
                        }
                    </script>
                <?php } ?>
            </div>
        </div>
        <?php if ($groupId): ?>
        <?php endif; ?>
<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>
        <script>
            $("#selectstartyear").change(function (){
                console.log(1)
                var id = $(this).val();
                $(".year"+id).show().siblings().hide();
                $('#courseDate').html('')
                $("#vendorAttendVueModel").html('')
                $('.background-gray.mr5.mb5.active').removeClass('active');
            })
        </script>
