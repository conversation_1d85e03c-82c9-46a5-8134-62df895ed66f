<?php
function addColon($str){
	return $str . Yii::t("global",": ");
}
function addComma($str, $beforeStr=true){
	if($beforeStr){
		return Yii::t("global", ", ") . $str;
	}else{
		return $str . Yii::t("global", ", ");
	}
}
function addBrackets($str, $withSpace=true, $spacePos='pre'){
	if($withSpace){
		if($spacePos == "pre")
			return(Yii::t("global", " (:str)", array(':str'=>$str)) );
		else
			return(Yii::t("global", "(:str) ", array(':str'=>$str)) );
	}else{
		return(Yii::t("global", "(:str)", array(':str'=>$str)) );
	}
}
?>