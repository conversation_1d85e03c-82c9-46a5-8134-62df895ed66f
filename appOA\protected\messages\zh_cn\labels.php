<?php
/**
 * Message translations.
 *
 * This file is automatically generated by 'yiic message' command.
 * It contains the localizable messages extracted from source code.
 * You may modify this file by translating the extracted messages.
 *
 * Each array element represents the translation (value) of a message (key).
 * If the value is empty, the message is considered as not translated.
 * Messages that no longer need translation will have their translations
 * enclosed between a pair of '' marks.
 *
 * Message string can be used with plural forms format. Check i18n section
 * of the guide for details.
 *
 * NOTE, this file must be saved in UTF-8 encoding.
 *
 * @version $Id: $
 */
return array (
    'Access Code' => '访客口令',
    'Address' => '地址',
    'Yr.' => '学年',
    'Alternative Lunch Menu' => '特殊餐',
    'Bcc' => '密送',
    'Campus' => '校园',
    'Cc' => '抄送至',
    'Cellphone' => '手机',
    'Child Name' => '姓名',
    'Chinese Name' => '中文名字',
    'City' => '城市',
    'Class' => '班级',
    'Company' => '公司',
    'Content' => '内容',
    'Credit' => '个人账户',
    'Created Time' => '创建时间',
    'Date of Birth' => '生日',
    'District' => '区',
    'Email' => '邮件',
    'Emergency Contact' => '紧急联系人',
    'Fax' => '传真',
    'ID_card' => '身份证号',
    'First Name' => '英文名',
    'Gender' => '性别',
    'Hospital' => '医院或诊所',
    'Invoice Title' => '账单标题',
    'ID' => '编号',
    'Job' => '职位',
    'Language' => '母语',
    'Last Name' => '英文姓',
    'Mobile Phone' => '手机',
    'Bus Notes' => '校车备注',
    'Name' => '姓名',
    'Barcode' => '学籍号',
    'Nationality' => '国籍',
    'Operator' => '操作人',
    'Paid Date' => '付款时间',
    'Photo' => '照片',
    'Photo Authorization' => '照片授权',
    'Pick Up Contact' => '接送授权',
    'Postcode' => '邮政编码',
    'Privacy' => '隐私设置',
    'Province' => '省份',
    'Replyto' => '回复至',
    'Special Needs' => '过敏源',
    'Subject' => '主题',
    'Tel' => '电话',
    'Telephone' => '家庭电话',
    'To' => '发送至',
    'Father\'s Email Address' => '父亲电邮地址',
    'Father\'s Mobile Number' => '父亲手机号',
    'Home Telephone Number' => '家庭电话',
    'Mother\'s Email Address' => '母亲电邮地址',
    'Mother\'s Mobile Number' => '母亲手机号',
    'Fapiao Title' => '发票抬头',
    'Vaccination' => '接种疫苗',
    'Physical Examination' => '体检',
    'Bus NO.' => '校车编号',
    'Driver Name' => '司机姓名',
    'License Plate NO.' => '车牌号',
    'Bus Type' => '车型',
    'Driver Mobile' => '司机手机',
    'Ayi Name' => '阿姨姓名',
    'Ayi Mobile' => '阿姨手机',
    'Pick-up Time' => '接站时间',
    'Drop-off Time' => '送达时间',
    '分配孩子' => '分配孩子',
    'Personal Email' => '私人邮件',
    'Start Date' => '入职日期',
    'Leave Date' => '离职日期',
    'Upload Image' => '上传头像',
    'Password' => '密码',
    'ID NO./Passport NO.' => '身份证号/护照号',
    'ID/Passport Expired Date' => '身份证/护照有效期至',
    'Contract Type' => '合同类型',
    'Contract Period' => '合同到期日',
    'Social Insurance' => '社保单位',
    'Telephone (Confidential)' => '紧急联系电话',
    'Dwelling Place' => '住址',
    'Native Place' => '籍贯',
    'Highest Degree' => '最高学历',
    'Personal Files in' => '档案所在',
    'Identity' => '身份证或护照',
    'English Name' => '英文名字',
    'Father Name' => '父亲姓名',
    'Father Email' => '父亲邮件',
    'Father Mobile' => '父亲手机',
    'Father Company' => '父亲公司',
    'Father Position' => '父亲职位',
    'Mother Name' => '母亲姓名',
    'Mother Email' => '母亲邮件',
    'Mother Mobile' => '母亲手机',
    'Mother Company' => '母亲公司',
    'Mother Position' => '母亲职位',
    'Middle Name' => '中间名',
    'Student ID' => '学生ID',
    'Primary Contact' => '主要联系人',
    'Do not contact for routine communication'=>'日常沟通请勿联系',
    'Room Code' => '房间号',
    'Capacity' => '学生容量',
    'Multifunctional Room' => '多功能厅',
    'Hall' => '大会议厅',
    'Library' => '图书馆',
    'Type' => '类型',
    'Description' => '描述',
    'Status' => '状态',

    'Interests' => '兴趣爱好',
    'Position' => '职位职责',
    'Biography (Internal)' => '个人简介（内部）',
    'English Title'=>'英文称谓',
    'Payment Status' => '付款状态',
    'School Year' => '学年',
    '1st Semester' => '第一学期',
    '2nd Semester' => '第二学期',
    'Calendar Title' => '校历标题',

    'Title' => '标题',
    'Title (Chinese)' => '标题（中文）',
    'Title (English)' => '标题（英文）',
    'Desc (Chinese)' => '描述（中文）',
    'Desc (English)' => '描述（英文）',
    'Description (Chinese)' => '描述（中文）',
    'Description (English)' => '描述（英文）',
    'Staff Only' => '仅员工可见',
    'Calendar Time' => '时间类型',
    'Calendar Day Type' => '校历日类型',
    'Student Capacity' => '可容纳学生数',
    'Vendor' => '外包公司',
    'Class Type' => '班级类型',
    'Class Title' => '班级名称',
    'Room NO.' => '教室',

    'Default Duration' => '默认时长',
    'Timeslot Starts' => '开始时间',
    'Memo' => '备注',
    'period slot' => '教学时间',

    'Resign Date' => '离职日期',
    'Account Deactive Date' => '帐号停用日期',


    'For Class' => '适用班级',
    'Active' => '有效',


    'Item (English)' => '内容条目（英文）',
    'Item (Chinese)' => '内容条目（中文）',

    'Progress Code' => '评定标准代码',

    'Residence Location' => '户口所在地',

	'Reason' => '离职原因',

	'Application Form' => '录取审批表',
	'Idcard' => '身份证复印件',
	'Bankcard' => '银行卡复印件',
	'Registration Form' => '人事登记表',
	'Dagree Certificate' => '学历和学位证明',
	'Release Letter' => '离职证明',
	'Retirement Certificate' => '退休证',
	'Other Certificates' => '其他证件',
	'Review the status' => '审核状态',
	'Yes' => '通过',
	'No' => '不通过',
	'Date' => '日期',
	'Student' => '学生',

    'Personal/Social' => '个性/社会性',
    'Scientific Thinking' => '科学思维',
    'Mathematical Thinking' => '数学思维',
    'The Arts' => '艺术',
    'Language and Literacy' => '语言文字',
    'Social Studies' => '社会学习',
    'Physical Development/Health' => '身体发展与健康',
    'Educational Student ID' => '教育ID号',
    'Mon' => '周一',
    'Tue' => '周二',
    'Wed' => '周三',
    'Thu' => '周四',
    'Fri' => '周五',
    'Sat' => '周六',
    'Sun' => '周日',
    'Reimbursement' => '报销',

    'Color' => '颜色',
    'Red' => '红色',
    'Green' => '绿色',
    'Blue' => '蓝色',
    'Size' => '尺寸',
    'White' => '白色',
    'Complete set' => '套装',
    'Khaki' => '卡其',
    'Grey' => '灰色',
    'Navy blue' => '藏蓝色',
    'Orange' => '橙色',

    'Unit 1 All About Me' => '第一单元 这就是我',
    'Unit 2 Community Helpers' => '第二单元 社区工作人员',
    'Unit 3 So Many Buildings' => '第三单元 这么多建筑',
    'Unit 4 Stories Around the World' => '第四单元 世界各地的故事',
    'Unit 5 The World We Share' => '第五单元 我们生活的世界',
    'Unit 6 Frontiers' => '第六单元 外太空',

    'Legal Last Name' => '法定姓（拼音）',
    'Legal Chinese Name' => '法定中文名字',
    'Legal First Name' => '法定名（拼音）',
    'Preferred Name' => '昵称/英文名',
    'Relationship' => '关系',
    'Legal Middle Name' => '法定中间名',

    "Father" => "父亲",
    "Mother" => "母亲",
    "Grandfather" => "爷爷",
    "Grandmother" => "奶奶",
    "Other" => "其他",
    "Default" => "默认",
    "Chinese Student" => "法定名为中文",
    "Non Chinese Student" => "法定名为英文",
    "Auto complete Pinyin" => "自动填充拼音",
    'Time' => '时间',
    'Members' => '成员',
    'Owner'=>'负责人',
    'Tagged students' => '已标记学生',
    'On the roster' => '在册',
    'Students with this tag' => '标记此标签的学生',
    'Student Tags'=>'学生标签管理',
    'Tag Icon' => '标签图形',
    'Tag Title'=>'标签标题',
    'Tag Color'=>'标签颜色',
    // 'Description'=>'说明',
    'Who can add or remove this tag to students' => '负责人可以给学生添加或去除此标签',
    'Select a Owner' => '选择负责人',
    '%s owner(s) selected' => '已选择%s名负责人',
    '(Dropped Out)' => '（已退学）',
    'Clear All (ES)' => '清空（小学）',
    'Clear All (SS)' => '清空（中学）',
    'Are you sure to claer all?' => '确认清空吗？',
    'DS-Zhongkao Score Conversion'=>'DS-中考成绩转化',
    'Score Mapping (IB to Zhongkao)' => '分数转换（IB to 中考）',
    'OK'=>'确定',
    'Convert All'=>'全部生成',
    'Export' => '导出',
    'Please Select Campus' => '请选择校园',
    'Please Select School Year' => '请选择学年',
    'Please Select Grade' => '请选择年级',
    'Please Select Semester' => '请选择学期',
    'Following subjects will be skipped for not setting conversion rule, proceed?'=>'以下课程未设置转换规则，本操作将转换其他科目成绩，确定继续么？',
    'All Zhongkao grades will be regenerated, proceed?'=>'本操作将重新转换并覆盖现有中考成绩，确定继续么？',
    'Convert to' => '转换到',
    'Source' => 'Source',
    'Average of' => '平均成绩',
    'If rule #1 is not matched, the rule #2 will be proceeded.'=>'若未匹配到第一规则，则进行第二规则匹配检查',
    'Conversion Rules' =>'转换规则',
    'Current'=>'当前',
    'Average score'=>'平均分',
    'No Match'=>'未匹配',
    'Preview matched number' => '预览匹配学生数',
    'p'  =>'人',
    'Save & Convert' => '保存并转换分数',
    'Settings'=>'基本信息',
    'Dynamic' => '动态标签',
    'Dynamic tags can be set based on scheduling'=>'只有动态标签可以设置时间',
    "Enable Privacy (Description won't be displayed when hover)" => '开启隐私（鼠标悬停时将不显示描述）',
    "Enable Privacy (Title won't be displayed when hover)" => '开启隐私（鼠标悬停时将不显示标题）',
    'Description is only visible in administrative page'=>'描述仅在管理界面可见',
    'EDU ID' => '学籍号',
    'R1: Rule 1 matched' => 'R1: 已匹配第一规则',
    'R2: Rule 2 matched' =>'R2: 已匹配第二规则',

    'Expected Date of Attendance' => '预计上学日期',
    'Actual Date of Attendance' => '实际上学日期',
    'Protected Tag Info' => '受保护的标签信息'
);
