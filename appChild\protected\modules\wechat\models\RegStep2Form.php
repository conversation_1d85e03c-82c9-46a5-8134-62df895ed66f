<?php
class RegStep2Form extends CFormModel
{
	public $name;
	public $relation;
	public $tel;
	public $photo;
	public $photoFile;

	public function rules()
	{
		return array(
			array('name, relation, tel, photo', 'sometimes'),
			// array("photoFile","file", "types" => "jpg, gif, png, jpeg", "maxSize" => 1024*1024*5, "tooLarge"=>Yii::t('reg', 'File should not exceed 5M'), "allowEmpty" => true),
			array('name, relation, tel, photoFile, photo', 'safe')
		);
	}
	
	public function attributeLabels()
	{
		return array(
			'name' => Yii::t("wechat", "Phone"),
			'relation' => Yii::t("wechat", "Code"),
			'tel' => Yii::t('reg', 'Address (compound name, street and district in Beijing)'),
			'photoFile' => Yii::t('reg', 'photoFile'),
		);
	}

	public function sometimes($attribute, $params)
	{
		if ($this->name || $this->relation || $this->tel || $this->photo) {
			if (!$this->name || !$this->relation || !$this->tel) {
				$this->addError($attribute, Yii::t('reg', 'Incomplete information'));
			}

			if( !$this->photo){
				$this->addError($attribute, Yii::t('reg', 'File can not be empty'));
			}
		}
	}

	public function upload($oss)
	{
		// 保存到本地
		if(!$this->saveLocal()) {
			return false;
		}
		$file = $this->photoFile;
		if ($file) {
			// 上传到 OSS
			$objectPath = 'regextrainfo/';

		    if ($oss->uploadFile($objectPath . $this->photo, $file)) {
                $normalDir = rtrim(Yii::app()->params['xoopsVarPath'], '/') .'/' . $this->photo;
                unlink($normalDir);
		    	// 删除旧附件
		    	// if ($this->photo) {
		    	// 	$oss->delete_object('regextrainfo/' . $this->photo);
		    	// }
		    	return true;
		    }
		}
	    return false;
	}

	public function getOssImageUrl($fileName)
	{
		$fileName = 'regextrainfo/' . $fileName;
		// 获取文件临时地址
		$osscs = CommonUtils::initOSSCS('private');

	    $object = $fileName;
	    $style = 'style/w200';
	    $imageUrl = $osscs->getImageUrl($object, $style);
	    return $imageUrl;
	}

	public function saveLocal()
	{
		// 判断是否为 base64 图片
		if (strpos($this->photoFile, 'base64') === false) {
			return true;
		}
		$data = base64_decode(preg_replace('#^data:image/\w+;base64,#i', '', $this->photoFile));
		//$subDir = 'attachment/temps/';
        $normalDir = rtrim(Yii::app()->params['xoopsVarPath'], '/') .'/';
		//$normalDir = rtrim(Yii::app()->params['uploadPath'], '/') .'/'. $subDir;

		$fileName = $this->photo;

		$filePath = $normalDir . $fileName;
		if (file_put_contents($filePath, $data)) {
			$this->photoFile = $filePath;
			return true;
		}
		return false;
	}
}