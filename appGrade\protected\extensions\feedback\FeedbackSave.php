<?php

class FeedbackSave extends CWidget {

    // 保存之后 是否返回反馈 以便及时显示在页面上
    public $returnFeedback = false;
    // 显示单个反馈的 视图文件名称
    public $singleFbView = null;

    public function init() {
        Yii::import('common.models.feedback.Comments');
    }

    public function run() {

        if (Yii::app()->user->checkAccess('adminChild', array("childid" => $this->getController()->getChildId()))) {
            $model = new Comments();

            $ret = array();

            $validate = CJSON::decode(CActiveForm::validate($model));
            if ($validate) {
                $validate1 = current($validate);
                $ret['stat'] = 20;
                $ret['tip'] = $validate1[0];
                echo CJSON::encode($ret);
                Yii::app()->end();
            }

            if (isset($_POST['Comments'])) {
                $model->attributes = $_POST['Comments'];

                $class_model = IvyClass::model()->findByPk($model->com_class_id);

                $model->com_child_id = $this->getController()->childid;
                $model->com_uid = Yii::app()->user->id;
                $model->com_yid = $class_model->yid;
                $model->com_school_id = $class_model->schoolid;
                $model->com_created_time = time();

                if ($model->save()) {
                    $feedback = Comments::model()->findByPk($model->com_root_id);
                    if ($feedback) {
                        $feedback->com_ifreply=0;
                        $feedback->save(false);
                    }
                    $ret['stat'] = 10;
                    if (true === $this->returnFeedback) {
                        Yii::import('common.models.classTeacher.InfopubStaffExtend');
                        if (empty($this->singleFbView)) {
                            $this->singleFbView = 'singleFbView';
                        }
                        $fb_info = Comments::model()->with('userWithProfile', 'staffInfo')->findByPk($model->getPrimaryKey());
                        $ret['com_id'] = $model->com_root_id;
                        $ret['fb_info'] = $this->render($this->singleFbView, array('fb' => $fb_info), true);
                    }

                    echo CJSON::encode($ret);
                    Yii::app()->end();
                }
            }
        } else {
            throw new CHttpException(500, 'No permission');
        }
    }

}