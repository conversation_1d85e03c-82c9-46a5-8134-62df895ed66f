<?php

/**
 * Class IvySemesterReport01
 * IVY MIK/IBS的学期报告结构模版
 */
class IvySemesterReport {
    public $configs;
    public $tplId;

    /**
     * @param $tplId 模版ID, 构造函数
     */
    public function __construct($tplId){
        $this->tplId = $tplId;
        $treeFile = __DIR__ . DIRECTORY_SEPARATOR
            . 'SemesterTemplates' . DIRECTORY_SEPARATOR
            . $this->tplId . '.php';
        if(!file_exists( $treeFile )){
            throw new CException('Invalid Semester Report Template ID');
        }else{
            //加载模版树形结构内容
            $this->configs = require($treeFile);
        }
    }

    /**
     * @return array 返回学期报告树形结构
     */
    public function getTree(){
        return $this->configs['tree'];
    }

    /**
     * @return array 返回学习领域
     */
    public function getLearningDomains(){
        return $this->configs['learningDomains'];
    }
}