<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
    <h4 class="modal-title" id="exampleModalLabel"><?php echo $model->vendor_id?'更新：':'添加：'; ?></h4>
</div>
<?php $form=$this->beginWidget('CActiveForm', array(
    'id'=>'course-form',
    'enableAjaxValidation'=>false,
    'htmlOptions'=>array('class'=>'J_ajaxForm form-horizontal', 'role'=>'form'),
)); ?>
<div class="modal-body">
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model,'bank'); ?></label>
        <div class="col-xs-8">
            <select class="form-control" name="bank">
            <?php foreach($bank as $key=>$val){;?>
            <option value="<?php echo $val;?>" ><?php echo $val;?></option>
            <?php };?>
            </select>
        </div>
    </div>    
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model,'bank_account'); ?></label>
        <div class="col-xs-8">
            <?php echo $form->textField($model,'bank_account',array('maxlength'=>255,'class'=>'form-control')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model,'bank_name'); ?></label>
        <div class="col-xs-8">
            <?php echo $form->textField($model,'bank_name',array('maxlength'=>255,'class'=>'form-control')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model,'identify'); ?></label>
        <div class="col-xs-8">
            <?php echo $form->textField($model,'identify',array('maxlength'=>30,'class'=>'form-control')); ?>
        </div>
    </div> 
</div>
<div class="modal-footer">
    <button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global','Submit');?></button>
    <button type="button" class="btn btn-default"  data-dismiss="modal"><?php echo Yii::t('global','Cancel');?></button>
</div>

<?php $this->endWidget(); ?>
