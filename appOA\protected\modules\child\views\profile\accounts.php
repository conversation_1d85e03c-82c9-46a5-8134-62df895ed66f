<div id="childinfo">
    <?php $this->widget('ext.childInfo.ChildInfo', array('childObj'=>$this->childObj))?>
</div>

<script>
function toggleUserInput(flag, ele){
	var divClass = 'div.parent-' + flag;
	var checked = $(ele).is(':checked');
	if(checked){
		$(divClass+' input:text').removeAttr('disabled');
		$(divClass+' input:text').removeClass('disabled');
	}
	else{
		$(divClass+' input:text').attr('disabled', true);
		$(divClass+' input:text').addClass('disabled');
	}
}

var flag = true;
function getRandomNum(num) {
    var numArr = [ 2, 3, 4, 5, 6, 7, 8, 9];
    var codeUpArr = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'J', 'K', 'M', 'N', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'];
    var codeArr = [ 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'j', 'k', 'm', 'n', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z'];
    var section = (Math.round(Math.random() * 10)) > 5;
    if(flag === true && num === 3){
        var rndNum = randArr(numArr);
        var ranCode = randArr(codeUpArr);
        var rNum = ranCode + rndNum;
    }else if(flag === true && section){
        var rndNum = randArr(numArr);
        var ranCode = randArr(codeUpArr);
        var rNum = ranCode + rndNum;
        flag = false;
    }else {
        var rndNum = randArr(numArr);
        var ranCode = randArr(codeArr);
        var rNum = ranCode + rndNum;
    }
    return rNum;
}
function randArr(arr){
    return arr[Math.floor(Math.random() * (arr.length-1))];
}
function randPassword()
{
    var sPassword = "";
    for (var i=0; i < 4; i++) {
        var grn = getRandomNum(i);
        sPassword = sPassword + grn;
    }
    flag = true;
    return sPassword;
}
</script>
<div class="form">
<?php $form=$this->beginWidget('CActiveForm', array(
	'id'=>'account-form',
	'enableClientValidation'=>true,
	'clientOptions'=>array(
		'validateOnSubmit'=>true,
	),
)); ?>

	<div class="table_full">
		<table width="100%">
			<colgroup>
				<col width="460">
				<col width="460">
				<col>
			</colgroup>
			<tbody>
				<tr>
					<th><?php echo Yii::t("child", "Father's Account");?></th>
					<th><?php echo Yii::t("child", "Mother's Account");?></th>
				</tr>
				<tr>
					<td>
						<?php $this->renderPartial("_account_parent", array("role"=>'father', "model"=>$model['father']));?>
					</td>
					<td>
						<?php $this->renderPartial("_account_parent", array("role"=>'mother', "model"=>$model['mother']));?>
					</td>
				</tr>
			</tbody>
		</table>
	</div>

	<div class="btn_wrap_pd">
		<?php
			echo CHtml::submitButton(Yii::t("global", "Save"), array("class"=>"btn btn_submit"));
		?>
        <?php
        foreach(Yii::app()->user->getFlashes() as $key => $message) {
            echo '<div class="tips_' . $key . '">' . $message . "</div>\n";
        }
        ?>
	</div>

<?php $this->endWidget(); ?>
</div><!-- form -->

<script>
$('a.setPass').bind('click',function(){
	var t='#'+$(this).attr('textTarget');
	$(t).val(randPassword());
})
</script>