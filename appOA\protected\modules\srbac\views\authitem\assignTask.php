<?php
$criteria = new CDbCriteria();
$criteria->condition = "type < 2";
$criteria->order = "type, name";

?>
<div class="container-fluid" style="margin-bottom: 80px;">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site','System Management'), array('/msettings'))?></li>
        <li class="active">系统权限</li>
    </ol>
    <div class="row panel-group">
        <div class="col-md-12">
            <?php
            $this->renderPartial('_menu');
            ?>
        </div>
        <?php if($this->module->getMessage() != ""){ ?>
            <div id="srbacError">
                <?php echo $this->module->getMessage();?>
            </div>
        <?php } ?>

    </div>

    <div class="row">
        <div class="col-md-6" id="unassigned-wrapper">
            <div class="panel panel-danger">
                <div class="panel-heading"><?php echo '未分配的操作';?></div>
                <div class="panel-body unassigned-operations auth-operation-list">
                    <ul></ul>
                </div>
            </div>
        </div>
        <?php
        foreach($authData['items']['1'] as $_key=>$_desc):
            ?>
            <div class="col-md-6">
                <div class="panel panel-success">
                    <div class="panel-heading"><?php echo $_desc;?> <?php echo $_key;?></div>
                    <div class="panel-body auth-operation-list" task="<?php print_r($_key);?>">
                        <ul></ul>
                    </div>
                </div>
            </div>
        <?php
        endforeach;
        ?>
    </div>
</div>
<div id="select-template" style="display: none;">
    <ul class="dropdown-menu" role="menu">
        <?php
        foreach($authData['items']['1'] as $_key=>$_desc):
        ?>
            <li><a role="menuitem" tabindex="-1" href="javascript:void(0)" onclick="changeTask(this)" opTarget="<?php echo $_key;?>"><?php echo $_desc . ' ' . $_key;?></a></li>
        <?php
        endforeach;
        ?>
    </ul>
</div>

<script>
    var authData = <?php echo CJSON::encode($authData);?>;
    var displayItem;
    var changeTask;
    $(function() {
        $('#assign-dropdown').addClass('active');
        displayItem = function(){
            $('.auth-operation-list ul').html('');
            $.each(authData['items']['0'], function(key) {
                var _a = '<a data-role="oper" data-toggle="dropdown" href="javascript:void(0)">'+authData['items']['0'][key]+' '+key + '</a>';
                var _li = $('<li></li>').attr('oper', key).append(_a);
                if(authData['parent'][key] != undefined){
                    for(var i=0; i< authData['parent'][key].length; i++){
                        var _task = authData['parent'][key][i];
                        $('.auth-operation-list[task|="'+_task+'"] ul').append(_li);
                    }
                }else{
                    $('.unassigned-operations ul').append(_li);
                }
            });
            if($('.unassigned-operations ul').html()==""){
                $('#unassigned-wrapper').hide();
            }else{
                $('#unassigned-wrapper').show();
            }

            $('.auth-operation-list li').click(function(){
                if(!$(this).attr('ini')){
                    $(this).addClass('dropdown');
                    $(this).append($('#select-template').html());
                    $(this).attr('ini',1);
                }
                $("a[data-role|='oper']").removeClass('text-danger');
                $(this).find("a[data-role|='oper']").addClass('text-danger');
            });
        };

        changeTask = function(obj){
            var _currentOp = $(obj).parents('li[oper]').attr('oper');
            var _currentTask = $(obj).parents('div.auth-operation-list').attr('task');
            var _targetTask = $(obj).attr('opTarget');

            if(_currentTask != _targetTask){
                head.load('<?php echo Yii::app()->themeManager->baseUrl;?>/base/js/dialog/dialog.js',function() {
                    var params = {
                        message	: '确定要移动<br>'+_currentOp+'<br>从 '+_currentTask+'<br>到 '+_targetTask+' 吗？',
                        type	: 'confirm',
                        isMask	: false,
                        follow  : $(obj).parents('li[oper]').find('a'),
                        onOk	: function() {
                            $.post('<?php echo $this->createUrl('moveOper')?>', {op: _currentOp, targett: _targetTask, currentt: _currentTask}, function(){
                                if(typeof authData['parent'][_currentOp] == 'undefined'){
                                    $('.unassigned-operations ul li[oper="'+_currentOp+'"]').remove();
                                    authData['parent'][_currentOp] = [];
                                }
                                else{
                                    for(var i in authData['parent'][_currentOp]){
                                        if(authData['parent'][_currentOp][i] == _currentTask)
                                            delete authData['parent'][_currentOp][i];
                                    }
                                }
                                authData['parent'][_currentOp].push(_targetTask);
                                resultTip({msg:'移动成功！', callback: function(){
                                    displayItem();
                                }});
                            });
                        }
                    };
                    head.dialog(params);
                });
            }
        };
        displayItem();
    });
</script>
