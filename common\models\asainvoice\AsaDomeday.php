<?php

/**
 * This is the model class for table "ivy_asa_domeday".
 *
 * The followings are the available columns in table 'ivy_asa_domeday':
 * @property integer $id
 * @property string $parent_name
 * @property string $parents_tel
 * @property string $chilids
 * @property string $course_time
 * @property integer $updated
 * @property string $channel
 */
class AsaDomeday extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_asa_domeday';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('updated', 'numerical', 'integerOnly'=>true),
			array('parent_name, parents_tel, chilids, course_time, channel', 'length', 'max'=>255),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, parent_name, parents_tel, chilids, course_time, updated, channel', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'parent_name' => 'Parent Name',
			'parents_tel' => 'Parents Tel',
			'chilids' => 'Chilids',
			'course_time' => 'Course Time',
			'updated' => 'Updated',
			'channel' => 'Channel',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('parent_name',$this->parent_name,true);
		$criteria->compare('parents_tel',$this->parents_tel,true);
		$criteria->compare('chilids',$this->chilids,true);
		$criteria->compare('course_time',$this->course_time,true);
		$criteria->compare('updated',$this->updated);
		$criteria->compare('channel',$this->channel,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return AsaDomeday the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	public static function getConfig()
    {
	    return  array(
                '1' => array(
                    'title' => Yii::t('demo','美国STEAM课程'),
                    'time' => "",
                    'supplier' => Yii::t('demo','闪钥双语艺术团'),
                    'introduction' => "",
                ),            
                '2' => array(
                    'title' => Yii::t('demo','X MAKER'),
                    'time' => "",
                    'supplier' => Yii::t('demo','闪钥双语艺术团'),
                    'introduction' => "",
                ),  
                '3' => array(
                    'title' => Yii::t('demo','闪钥双语合唱课'),
                    'time' => "",
                    'supplier' => Yii::t('demo','闪钥双语艺术团'),
                    'introduction' => "",
                ),            
                '4' => array(
                    'title' => Yii::t('demo','闪钥双语街舞课'),
                    'time' => "",
                    'supplier' => Yii::t('demo','闪钥双语艺术团'),
                    'introduction' => "",
                ),  
                '5' => array(
                    'title' => Yii::t('demo','爱立方艺术中心'),
                    'time' => "",
                    'supplier' => Yii::t('demo','爱立方艺术中心'),
                    'introduction' => "",
                ),            
                '6' => array(
                    'title' => Yii::t('demo','绘本戏剧'),
                    'time' => "",
                    'supplier' => Yii::t('demo','乐活猫国际教育'),
                    'introduction' => "",
                ),  
                '7' => array(
                    'title' => Yii::t('demo','趣味建筑'),
                    'time' => "",
                    'supplier' => Yii::t('demo','筑趣工坊'),
                    'introduction' => "",
                ),            
                '8' => array(
                    'title' => Yii::t('demo','创新情景数学思维课'),
                    'time' => "",
                    'supplier' => Yii::t('demo','北京美赛教育'),
                    'introduction' => "",
                ),  
                '9' => array(
                    'title' => Yii::t('demo','棒垒球'),
                    'time' => "",
                    'supplier' => Yii::t('demo','小虎棒球'),
                    'introduction' => "",
                ),            
                '10' => array(
                    'title' => Yii::t('demo','网球启蒙'),
                    'time' => "",
                    'supplier' => Yii::t('demo','好动国际网球学院'),
                    'introduction' => "",
                ),  
                '11' => array(
                    'title' => Yii::t('demo','美式橄榄球'),
                    'time' => "",
                    'supplier' => Yii::t('demo','凯瑞石文化'),
                    'introduction' => "",
                ),            
                '12' => array(
                    'title' => Yii::t('demo','幼儿足球—球小星'),
                    'time' => "",
                    'supplier' => Yii::t('demo','北京爱思乐踢教育'),
                    'introduction' => "",
                ),

            /*
                '2' => array(
                    'title' => Yii::t('demo','闪钥双语合唱课'),
                    'time' => "14:00 - 14:30 ".Yii::t('demo','英文合唱').Yii::t('demo','（6-9岁）'),
                    'supplier' => Yii::t('demo','闪钥双语艺术团'),
                    'introduction' => "",
                ),
                '3' => array(
                    'title' => Yii::t('demo','闪钥双语合唱课'),
                    'time' => "15:00 - 15:30 ".Yii::t('demo','英文表演唱').Yii::t('demo','（3-5岁）'),
                    'supplier' => Yii::t('demo','闪钥双语艺术团'),
                    'introduction' => "",
                ),
                '4' => array(
                    'title' => Yii::t('demo','街舞'),
                    'time' => "14:30 - 15:00 ".Yii::t('demo','街舞').Yii::t('demo','（3-5岁）'),
                    'supplier' => Yii::t('demo','闪钥双语艺术团'),
                    'introduction' => "",
                ),
                '5' => array(
                    'title' => Yii::t('demo','街舞'),
                    'time' => "15:30 - 16:00 ".Yii::t('demo','街舞').Yii::t('demo','（6-9岁）'),
                    'supplier' => Yii::t('demo','闪钥双语艺术团'),
                    'introduction' => "",
                ),
                '6' => array(
                    'title' => Yii::t('demo','爵士舞'),
                    'time' => "15:00 - 16:30".Yii::t('demo','（5-8岁）'),
                    'supplier' => Yii::t('demo','海右舞蹈'),
                    'introduction' => "",
                ),
                '7' => array(
                    'title' => Yii::t('demo','拉丁舞'),
                    'time' => "10:00 - 11:30".Yii::t('demo','（4-8岁）'),
                    'supplier' => Yii::t('demo','海右舞蹈'),
                    'introduction' => "",
                ),
                '8' => array(
                    'title' => Yii::t('demo','拉丁舞'),
                    'time' => "13:30 - 15:00".Yii::t('demo','（4-8岁）'),
                    'supplier' => Yii::t('demo','海右舞蹈'),
                    'introduction' => "",
                ),
                '9' => array(
                    'title' => Yii::t('demo','油画'),
                    'time' => "09:30 - 10:30 ".Yii::t('demo','小班').Yii::t('demo','（3-5岁）'),
                    'supplier' => Yii::t('demo','爱立方艺术中心'),
                    'introduction' => "",
                ),
                '10' => array(
                    'title' => Yii::t('demo','油画'),
                    'time' => "11:00 - 12:00 ".Yii::t('demo','小班').Yii::t('demo','（3-5岁）'),
                    'supplier' => Yii::t('demo','爱立方艺术中心'),
                    'introduction' => "",
                ),
                '11' => array(
                    'title' => Yii::t('demo','油画'),
                    'time' => "13:30 – 15:00 ".Yii::t('demo','中班').Yii::t('demo','（5-8岁）'),
                    'supplier' => Yii::t('demo','爱立方艺术中心'),
                    'introduction' => "",
                ),
                '12' => array(
                    'title' => Yii::t('demo','油画'),
                    'time' => "16:00 – 17:30 ".Yii::t('demo','中班').Yii::t('demo','（5-8岁）'),
                    'supplier' => Yii::t('demo','爱立方艺术中心'),
                    'introduction' => "",
                ),
                '17' => array(
                    'title' => Yii::t('demo','网球启蒙课程'),
                    'time' => "14:00 – 15:00".Yii::t('demo','（5-9岁）'),
                    'supplier' => Yii::t('demo','好动国际网球'),
                    'introduction' => "",
                ),
                '18' => array(
                    'title' => Yii::t('demo','网球启蒙课程'),
                    'time' => "15:00 – 16:00".Yii::t('demo','（5-9岁）'),
                    'supplier' => Yii::t('demo','好动国际网球'),
                    'introduction' => "",
                ),
                '19' => array(
                    'title' => Yii::t('demo','网球启蒙课程'),
                    'time' => "16:00 – 17:00".Yii::t('demo','（5-9岁）'),
                    'supplier' => Yii::t('demo','好动国际网球'),
                    'introduction' => "",
                ),
                '20' => array(
                    'title' => Yii::t('demo','儿童棒垒球课程'),
                    'time' => "09:30 – 11:00".Yii::t('demo','（4-12岁）'),
                    'supplier' => Yii::t('demo','小虎棒球'),
                    'introduction' => "",
                ),
                '21' => array(
                    'title' => Yii::t('demo','儿童棒垒球课程'),
                    'time' => "15:00 – 16:30".Yii::t('demo','（4-12岁）'),
                    'supplier' => Yii::t('demo','小虎棒球'),
                    'introduction' => "",
                ),
                '22' => array(
                    'title' => Yii::t('demo','SNAG高尔夫'),
                    'time' => "09:30 – 11:30".Yii::t('demo','（4-10岁）'),
                    'supplier' => Yii::t('demo','DaVinci高尔夫学院'),
                    'introduction' => "",
                ),
                '23' => array(
                    'title' => Yii::t('demo','SNAG高尔夫'),
                    'time' => "14:00 – 16:00".Yii::t('demo','（4-10岁）'),
                    'supplier' => Yii::t('demo','DaVinci高尔夫学院'),
                    'introduction' => "",
                ),
                '24' => array(
                    'title' => Yii::t('demo','常青藤国际象棋PC班'),
                    'time' => "10:15 – 11:00 ".Yii::t('demo','PC班（5-12 岁）'),
                    'supplier' => Yii::t('demo','常青藤国际象棋'),
                    'introduction' => "",
                ),
                '13' => array(
                    'title' => Yii::t('demo','常青藤国际象棋PC班'),
                    'time' => "13:30 – 14:15 ".Yii::t('demo','PC班（5-12 岁）'),
                    'supplier' => Yii::t('demo','常青藤国际象棋'),
                    'introduction' => "",
                ),
                '14' => array(
                    'title' => Yii::t('demo','常青藤国际象棋PC班'),
                    'time' => "15:00 – 15:45 ".Yii::t('demo','PC班（5-12 岁）'),
                    'supplier' => Yii::t('demo','常青藤国际象棋'),
                    'introduction' => "",
                ),
                '15' => array(
                    'title' => Yii::t('demo','逻辑思维课'),
                    'time' => "14:00 – 17:00 ".Yii::t('demo','（7-12岁）'),
                    'supplier' => Yii::t('demo','慧玩部落'),
                    'introduction' => "",
                ),
                '16' => array(
                    'title' => Yii::t('demo','小小木工课'),
                    'time' => "14:00 – 17:00 ".Yii::t('demo','（7-12岁）'),
                    'supplier' => Yii::t('demo','慧玩部落'),
                    'introduction' => "",
                ),
                '1' => array(
                    'title' => Yii::t('demo','Vex 竞赛课程'),
                    'time' => "13:00 – 18:00 ".Yii::t('demo','四年级及以上（10岁以上）'),
                    'supplier' => Yii::t('demo','爱萝卜少儿机器人'),
                    'introduction' => "",
                ),
                */
        );
    }
}
