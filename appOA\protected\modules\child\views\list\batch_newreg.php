<div class="h_a"><?php echo Yii::t("site", "Newly Registered");?></div>
<div class="user_group J_check_wrap">
		<dl id="classid-0">
			<dt>
				<?php
				echo CHtml::openTag("label");
				echo CHtml::checkBox("test", false, array('data-checklist'=>'J_check_c1', 'class'=>'J_check_all', 'data-direction'=>'y'));
				echo Yii::t("site", "Newly Registered");
				echo ' ('.count($children).')';
				echo CHtml::closeTag("label");
				?></dt>
				<dd>
				<?php if(!empty($children)):?>
					<?php foreach ($children as $child):
						$_htmlopt = array();
						$sts = $child->getSwitchStatus();
						$childName = $child->getChildName();
						if(isset($sts['txt'])){
							$_htmlopt['disabled']='disabled';
						}
                         else {
                            $_htmlopt['data-yid'] = 'J_check_c1';
                        }
                        $_htmlopt['class'] = 'J_check';
                        $_htmlopt['value'] = $child->childid;
						
						echo CHtml::openTag("label");
							echo CHtml::checkBox("child[0][".$child->childid.']', false, $_htmlopt);
							echo CHtml::openTag("span", array("title"=>$childName, "id"=>'c_'.$child->childid));
							echo strlen($childName)>16? substr($child->getChildName(), 0, 16).'...':$childName;
							echo CHtml::openTag("i", array("class"=>"stats"));
							echo $child->birthday_search;
							echo CHtml::closeTag("i");
							if(isset($sts['classid'])){
								echo CHtml::openTag("i", array("class"=>"class"));
								echo $nextClassList[$sts['classid']];
								echo CHtml::closeTag("i");				
							}							
							echo CHtml::closeTag("span");
							echo CHtml::link('', array("//child/index/index", "childid"=>$child->childid), array('class'=>'icon', 'target'=>'_blank'));
						echo CHtml::closeTag("label");
						
					endforeach;?>
					
					<?php
						echo CHtml::openTag("label", array("class"=>"short-action cl"));
						echo CHtml::link(Yii::t("campus", "Set Droppout"),'javascript:;', array("class"=>"btn mr15", 'onclick'=>'batHandle(this, "f");', 'classid'=>0));
						echo CHtml::link(Yii::t("campus", "Set Graduated"),'javascript:;', array("class"=>"btn", 'onclick'=>'batHandle(this, "o");', 'classid'=>0));
						echo CHtml::closeTag("label");
						
						
						echo CHtml::openTag("label", array("class"=>"long-action"));
						echo CHtml::openTag("span");
						echo Yii::t("campus", "Assign To: ");
                        $nextClassHtmlOpt['classid'] = 0;
						echo CHtml::dropDownList("toClass",0, $nextClassList, $nextClassHtmlOpt);
						echo CHtml::closeTag("span");
						echo CHtml::closeTag("label");
					?>
				<?php endif;?>
				</dd>
		</dl>
</div>