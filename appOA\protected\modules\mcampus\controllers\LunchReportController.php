<?php
/*
 * 孩子午餐统计报告
 */
class LunchReportController extends BranchBasedController{

    public $actionAccessAuths = array(
        'index'             => 'o_A_Access',
        'SearchData'        => 'o_A_Access',
    );
    
    public function createUrl($route, $params = array(), $ampersand = '&')
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        return parent::createUrl($route, $params, $ampersand);
    }
    
    public function init(){
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'campusOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site','Campus Workspace');

        //初始化选择校园页面
        $this->branchSelectParams['hideOffice'] = true;
        $this->branchSelectParams['urlArray'] = array('//mcampus/lunchReport/index');
    }
    
    public function actionIndex(){
        $t = Yii::app()->request->getParam('t','day');
        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/underscore-min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/backbone-min.js');
        $this->render('index',array(
            't'=>$t,
        ));
    }
    
    /*
     * 数据查询
     */
    public function actionSearchData(){
        $startDate = ($_POST['startdate']) ? strtotime($_POST['startdate']) : 0;
        $endDate = ($_POST['enddate']) ? strtotime($_POST['enddate']) : 0;
        $byType = Yii::app()->request->getPost('byType');
        $t = Yii::app()->request->getParam('t','');
        $ret = array('state'=>'fail');
        if ($t === 'day'){
            if ($startDate ===0){
                $ret['message'] = Yii::t('lunch','Please select date.');
                $this->addMessage('callback', 'showData');
                $this->addMessage('data', $ret);
                $this->addMessage('state','success');
                $this->addMessage('message', $ret['message']);
                $this->showMessage();
            }
        }else{
            if ($startDate ===0 || $endDate===0){
                $ret['message'] = Yii::t('lunch','Please select the starting and ending dates.');
                $this->addMessage('callback', 'showData');
                $this->addMessage('data', $ret);
                $this->addMessage('state','success');
                $this->addMessage('message', $ret['message']);
                $this->showMessage();
            }
            if ($startDate>$endDate){
                $ret['message'] = Yii::t('lunch','The ending date cannot be earlier than the starting date.');
                $this->addMessage('callback', 'showData');
                $this->addMessage('data', $ret);
                $this->addMessage('state','success');
                $this->addMessage('message', $ret['message']);
                $this->showMessage();
            }
            if (date('Ym',$startDate) != date('Ym',$endDate)){
                $ret['message'] = Yii::t('lunch','Please set the starting and ending dates in the same month.');
                $this->addMessage('callback', 'showData');
                $this->addMessage('state','success');
                $this->addMessage('message', $ret['message']);
                $this->addMessage('data', $ret);
                $this->showMessage();
            }
        }
        if ($t === 'day'){
            $ret = $this->getLunchDataByDay($startDate,$byType,$t);
        }else{
            $ret = $this->getLunchDataByMonth($startDate,$endDate,$byType,$t);
        }
        $message = 'success';
        if ($ret['sum'] == 0) {
            $message = '暂无数据';
        }
        $this->addMessage('state','success');
        $this->addMessage('message', $message);
        $this->addMessage('callback', 'showData');
        $this->addMessage('data', $ret);
        $this->showMessage();
    }
    
    /*
     * 按天查
     */
    public function getLunchDataByDay($startDate,$byType,$t){
        $ret= array('state'=>'success','sum'=>0,'classList'=>array(),'childInfo'=>array(),'t'=>$t);
        //have lunch
        if ($byType === 'HAVELUNCH'){
            $refundDataList = array(0);
            $refundData = $this->getRefundLunchData($startDate,$startDate);
            if (!empty($refundData)){
                foreach ($refundData as $val){
                    $refundDataList[$val->childid] = $val->childid;
                }
            }
            $yid = 0;
            $schoolDay = $this->getSchoolday($startDate,$startDate,$yid);
            $data = $this->getHaveLunchData($startDate,$startDate,'day',$yid);
            if (count($schoolDay)){
                $schooldaysList = array();
                foreach ($schoolDay as $val){
                    $schooldaysList = $val['schoolday_array'];
                }
                $d = date('d',$startDate);
                foreach ($data as $val){
                    $val->classid = $val->childInfo->classid;
                    if (!in_array($val->childid, $refundDataList)){
                        if (in_array($d,$schooldaysList)){
                            $ret['sum']++;
                            $ret['classList'][$val->classid]['sum']++;
                            $ret['classList'][$val->classid]['classid'] = $val->classid;
                            $ret['classList'][$val->classid]['title'] = $val->childInfo->ivyclass->title;
                            $ret['childInfo'][] = array(
                                'childid'=>$val->childid,
                                'classid'=>$val->classid,
                                'childName'=>$val->childInfo->getChildName(),
                            );
                        }
                    }
                }
            }
        }else{
            //cancel lunch
            $refundData = $this->getRefundLunchData($startDate,$startDate,true);
            if (!empty($refundData)){
                foreach ($refundData as $val){
                    $val->classid = $val->childInfo->classid;
                    $ret['sum']++;
                    $ret['classList'][$val->classid]['sum']++;
                    $ret['classList'][$val->classid]['classid'] = $val->classid;
                    $ret['classList'][$val->classid]['title'] = $val->childInfo->ivyclass->title;
                    $ret['childInfo'][] = array(
                        'childid'=>$val->childid,
                        'classid'=>$val->classid,
                        'childName'=>$val->childInfo->getChildName(),
                    );
                }
            }
        }
        return $ret;
    }
    
    /*
     * 按月查
     */
    public function getLunchDataByMonth($startDate,$endDate,$byType,$t){
        $ret= array('state'=>'success','sum'=>0,'classList'=>array(),'childInfo'=>array(),'lunchInfo'=>array(),'t'=>$t);
        if ($byType === 'HAVELUNCH'){
            //查询孩子已取消列表
            $refundData = $this->getRefundLunchData($startDate,$endDate);
            $refundDataList = array();
            if (!empty($refundData)){
                foreach ($refundData as $val){
                    $refundDataList[$val->childid][] = $val->target_timestamp;
                }
            }
            $yid = 0;
            $schoolDay = $this->getSchoolday($startDate,$endDate,$yid);
            $haveLunchData = $this->getHaveLunchData($startDate,$endDate,'month',$yid);
            if (count($schoolDay) && !empty($haveLunchData)){
                $schooldaysList = array();
                foreach ($schoolDay as $val){
                    $schooldaysList = $val['schoolday_array'];
                }
                //have lunch info
                foreach ($haveLunchData as $val){
                    $weekList = array("Mon"=>$val->mon, "Tue"=>$val->tue, "Wed"=>$val->wed, "Thu"=>$val->thu, "Fri"=>$val->fri,"Sat"=>10,"Sun"=>10);
                    for ($i=$startDate;$i<=$endDate;$i+=86400){
                        $d = date('d',$i);
                        $w = date('D',$i);
                        if (isset($refundDataList[$val->childid])){
                            if (in_array($i, $refundDataList[$val->childid])) {
                                continue;
                            }
                        }
                        if ((in_array($d, $schooldaysList)) && ($i>=$val->startdate && $i<=$val->enddate)){
                            if (intval($weekList[$w]) === 10){
                                $val->classid = $val->childInfo->classid;
                                $ret['sum']++;
                                $ret['classList'][$val->classid]['sum']++;
                                $ret['classList'][$val->classid]['classid'] = $val->classid;
                                $ret['classList'][$val->classid]['title'] = $val->childInfo->ivyclass->title;
                                $ret['childInfo'][$val->childid] = array(
                                    'childid'=>$val->childid,
                                    'classid'=>$val->classid,
                                    'childName'=>$val->childInfo->getChildName(),
                                );
                                $ret['lunchInfo'][] = array(
                                    'childid'=>$val->childid,
                                    'lunch'=>  date('Y-m-d',$i),
                                );
                            }
                        }
                    }
                }
                unset($haveLunchData);
                unset($schoolDay);
                unset($refundData);
            }
        }else{
            //cancel lunch
            $refundData = $this->getRefundLunchData($startDate,$endDate,true);
            if (!empty($refundData)){
                foreach ($refundData as $val){
                    $val->classid = $val->childInfo->classid;
                    $ret['sum']++;
                    $ret['classList'][$val->classid]['sum']++;
                    $ret['classList'][$val->classid]['classid'] = $val->classid;
                    $ret['classList'][$val->classid]['title'] = $val->childInfo->ivyclass->title;
                    $ret['childInfo'][$val->childid] = array(
                        'childid'=>$val->childid,
                        'classid'=>$val->classid,
                        'childName'=>$val->childInfo->getChildName(),
                    );
                    $ret['lunchInfo'][] = array(
                        'childid'=>$val->childid,
                        'lunch'=>  date('Y-m-d',$val->target_timestamp),
                    );
                }
            }
        }
        return $ret;
    }
    
    /*
     * 取得某段区间取消孩子的数据
     */
    public function getRefundLunchData($startDate,$endDate,$relations = false){
        Yii::import('common.models.invoice.RefundLunch');
        $criteria = new CDbCriteria();
        $criteria->compare('t.schoolid', $this->branchId);
        $criteria->addBetweenCondition('t.target_timestamp', $startDate, $endDate);
        if ($relations === true){
            return RefundLunch::model()->with('childInfo','classInfo')->findAll($criteria);
        }else{
            return RefundLunch::model()->with()->findAll($criteria);
        }
    }
    
    /*
     * 查询孩子吃午餐用户
     */
    public function getHaveLunchData($startDate,$endDate,$type='day',$yid){
        Yii::import('common.models.invoice.ChildServiceInfo');
        $criteria = new CDbCriteria();
        $criteria->compare('t.schoolid', $this->branchId);
        $criteria->compare('t.yid', $yid);
        $criteria->compare('t.payment_type','lunch');
        if ($type === 'day'){
            $criteria->addCondition('t.startdate<='.$startDate.' and t.enddate>='.$startDate);
            $arr = array(1=>'mon',2=>'tue',3=>'wed',4=>'thu',5=>'fri');
            $w = isset($arr[date('w',$startDate)]) ? $arr[date('w',$startDate)] : '';
            if (!empty($w)){
                $criteria->compare('t.'.$w, 10);
            }
        }else{
            $criteria->addCondition('(t.startdate<='.$startDate.' and t.enddate>='.$endDate.') or (t.startdate>='.$startDate.' and t.startdate<='.$endDate.') or (t.startdate >='.$startDate.' and t.enddate <='.$endDate.') or (t.startdate <='.$startDate.' and t.enddate >='.$startDate.')');
        }
        return ChildServiceInfo::model()->with('childInfo','classInfo')->findAll($criteria);
        
    }
    
    /*
     * 查询教学天数
     */
    public function getSchoolday($startDate,$endDate,&$yid=0){
        $schoolDay = array();
        //查询$startDate所属校历
        Yii::import('common.models.calendar.CalendarSchool');
        Yii::import('common.models.calendar.CalendarSemester');
        Yii::import('common.models.calendar.CalendarSchoolDays');
        $criteria = new CDbCriteria();
        $criteria->compare('assignSchool.branchid', $this->branchId);
        $criteria->addCondition('t.school_start_timestamp<='.$startDate.' and school_end_timestamp>='.$startDate);
        $semesterObj = CalendarSemester::model()->with('assignSchool')->find($criteria);
        //查教学天数
        if (!empty($semesterObj)){
            $schoolDay = CalendarSchoolDays::model()->getCalendarSchooldays($semesterObj->yid,$startDate,$endDate);
            $schoolDay = $schoolDay[$semesterObj->yid]['month'];
            $yid = $semesterObj->yid;
        }
        return $schoolDay;
    }

    public function getType($type = ''){
        $ret = array(
            'day'=>Yii::t('lunch', 'Search by date'),
            'month'=>Yii::t('lunch', 'Search by month'),
        );
        return (empty($type)) ? $ret : $ret[$type];
    }
}

