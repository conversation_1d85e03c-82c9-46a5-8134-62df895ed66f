<style>
    .childLableCss{
        line-height: normal;
        font-size: 11px;
        font-weight: 400;
        padding-left: 4px;
        padding-right: 4px;
        margin-bottom:4px;
    }
    .parentLableCss{
        line-height: normal;
        font-size: 11px;
        font-weight: 400;
        padding-left: 4px;
        padding-right: 4px;
    }
    .labelTag{
        display: flex;
        align-items: center;
        background: #FFE4E6;
        border: 1px solid #FF9FA6;
        height: 18px;
        cursor: pointer;
        border-radius:3px
    }
    .plusAdd{
        font-size: 10px;
        background: #E50012;
        color: #fff;
        padding: 0 3px;
        margin-right: 3px;
        font-weight: bold;
        height: 16px;
        line-height: 14px;
        margin-top: -1px;
    }
    .labelTag .iconfont{
        font-size: 13px;
        margin:0 3px
    }
    .medicalDate{
        background: #F0F5FB;
        border-radius: 4px;
        padding:2px 8px;
        color: #4D88D2;
        font-size:12px;
        margin-left:12px
    }
</style>
<div id="content"  @click='medical=false'>
    <div class="text-center marginAuto ">
        <img :src="childInfo.photo" class="imgCover" alt="">
        <p class="font14 fontBold mt10">{{childInfo.name}} | {{childInfo.id}}</p>
        <!--孩子标签-->
        <div class="font14 fontBold" style=" display: flex;flex-wrap: wrap;align-items: center;align-content: baseline;justify-content: center;">
            <span v-for="(item,index) in childInfo.label" class="mr4 ml4 childLableCss"  :style="'color:rgb('+item['color']+')'" >
                <el-tooltip  class="item" effect="dark" :content="item.desc" placement="top">
                    <span v-html="item.name"></span>
                </el-tooltip>
            </span>
            <span v-for="(item,index) in childInfo.labelGroup"  class="mr4 ml4 childLableCss">
                <el-popover
                    placement="bottom"
                    width="400"
                    v-model='medical'
                    trigger="click">
                    <div style='padding:10px'>
                        <div class='font14 color3 fontBold'><?php echo Yii::t('coll', 'Medical Information') ?></div>
                        <div class='flex mt20' v-for='(list,key,index) in medicalData'>
                            <div class='mr5'><span :style="'color:rgb('+list['flag_color']+')'" v-html="list.flag_name"></span></div>
                            <div v-if='key==12'>
                                <div style='margin-top:2px'>
                                    <span class='font14 color3 fontBold'>{{list.title}}</span>
                                    <span class='medicalDate'><span class='el-icon-date mr5'></span>{{list.use_days[0]}}—{{list.use_days[1]}}</span>
                                </div>
                                <div class='mt12 font14 flex'><span class='color6'><?php echo Yii::t('coll', 'Dosing time / symptoms') ?>：</span><span class='color3 flex1'>{{list.use_time}}</span></div>
                                <div class='mt8 font14 flex'><span class='color6'><?php echo Yii::t('coll', 'Other Information') ?>：</span><span class='color3 flex1'>{{list.medical_special}}</span></div>
                            </div>
                            <div v-if='key==13'>
                                <div style='margin-top:2px'>
                                    <span class='font14 color3 fontBold'>{{list.title}}</span>
                                    <span class='medicalDate'><span class='el-icon-date mr5'></span>{{list.medicalHistoryDate[0]}}—{{list.medicalHistoryDate[1]}}</span>
                                </div>
                                <div class='mt12 font14 flex'><span class='color6'><?php echo Yii::t('reg', 'Medical History') ?>：</span><span class='color3 flex1'>{{list.medicalHistoryDetail}}</span></div>
                                <div class='mt8 font14 flex'><span class='color6'><?php echo Yii::t('coll', 'Other Information') ?>：</span><span class='color3 flex1'>{{list.otherDetail}}</span></div>
                            </div>
                            <div v-if='key==14'>
                                <div style='margin-top:2px'>
                                    <span class='font14 color3 fontBold'>{{list.title}}</span>
                                    <span class='medicalDate'><span class='el-icon-date mr5'></span>{{list.exemptionDate[0]}}—{{list.exemptionDate[1]}}</span>
                                </div>
                                <div class='mt12 font14 flex'><span class='color6'><?php echo Yii::t('coll', 'Reasons') ?>：</span><span class='color3 flex1'>{{list.reasons}}</span></div>
                                <div class='mt8 font14 flex'><span class='color6'><?php echo Yii::t('coll', 'Other Information') ?>：</span><span class='color3 flex1'>{{list.other}}</span></div>
                            </div>
                            <div v-if='key==15'>
                                <div style='margin-top:2px'>
                                    <span class='font14 color3 fontBold'>{{list.title}}</span>
                                    <span class='medicalDate'><span class='el-icon-date mr5'></span>{{list.allergyDate[0]}}—{{list.allergyDate[1]}}</span>
                                </div>
                                <div class='mt12 font14 flex'><span class='color6'><?php echo Yii::t('reg', 'Food Allergy') ?>：</span><span class='color3 flex1'>{{list.foodAllergyDetail}}</span></div>
                                <div class='mt8 font14 flex'><span class='color6'><?php echo Yii::t('reg', 'Medical Allergy') ?>：</span><span class='color3 flex1'>{{list.medAllergyDetail}}</span></div>
                                <div class='mt8 font14 flex'><span class='color6'><?php echo Yii::t('reg', 'Other Allergy') ?>：</span><span class='color3 flex1'>{{list.otherAllergyDetail}}</span></div>
                                <div class='mt8 font14 flex'><span class='color6'><?php echo Yii::t('coll', 'Other Information') ?>：</span><span class='color3 flex1'>{{list.otherDetail}}</span></div>
                            </div>
                        </div>
                    </div>
                    <!--分组标签-->
                    <span @click="getLabelExtraInfo(childInfo.id,item)" class='labelTag' slot="reference">
                        <!-- {{index}} -->
                        <span class='glyphicon glyphicon-plus plusAdd'></span>
                        <span v-html="item2.name"  v-for="(item2,index2) in item" :style="'color:rgb('+item2['color']+')'" :title="item2.hide_desc==1 ?  '' :  item2.desc"></span>
                    </span>
                </el-popover>
            </span>
        </div>
    </div>
    <div class="text-center marginAuto mb10 font14">
        <img :src="imgBaseUrl+'/base/images/student_website.png'" class="stuImg" alt="">
        <a target="_blank" :href="childInfo.homeUrl"><?php echo Yii::t("teaching", "Frontend Preview");?></a>

        <?php if($this->branchId == 'BJ_DS' || $this->branchId=='BJ_SLT'){?>
        <span class="ml20"><img :src="imgBaseUrl+'/base/images/student_information.png'" class="stuImg ml10" alt="">
            <a :href="childInfo.academicCenterUrl+'#Journals'"><?php echo Yii::t("newDS", "Academic Center");?></a>
        </span>
        <?php }?>

        <?php if($data['isMiddleSchool']){?>
        <span class="ml20 ">
            <form role="form" target="_blank" id="courseGroup-form" :action="exportChildUrl" method="post" class="inline-block">
                <input type="checkbox" hidden="" checked="checked" :value="childInfo.id" name="childList[]">
                <input type="checkbox" hidden="" checked="checked" value="A" name="scheduleTypeList[]">
                <input type="checkbox" hidden="" checked="checked" value="B" name="scheduleTypeList[]">
                <input type="checkbox" hidden="" checked="checked" value="C" name="scheduleTypeList[]">
                <input type="checkbox" hidden="" checked="checked" value="D" name="scheduleTypeList[]">
                <button type="submit" class="btn btn-link font14 mt-2">
                    <span class="glyphicon glyphicon-print mr5 font12"></span><?php echo Yii::t("newDS", "Student Schedule");?>
                </button>
            </form>
        </span>
        <?php }?>
    </div>
    <div class="col-md-12 mb20 mt20">
        <div class="col-md-3"><p class="font12 color6"><?php echo Yii::t("labels", "Legal First Name");?></p>
            <p class="font14 color3">{{childInfo.first_name_en}}</p>
        </div>
        <div class="col-md-3"><p class="font12 color6"><?php echo Yii::t("labels", "Legal Middle Name");?></p>
            <p class="font14 color3">{{childInfo.middle_name_en}}</p>
        </div>
        <div class="col-md-3"><p class="font12 color6"><?php echo Yii::t("labels", "Legal Last Name");?></p>
            <p class="font14 color3">{{childInfo.last_name_en}}</p>
        </div>
        <div class="col-md-3"><p class="font12 color6"><?php echo Yii::t("labels", "Preferred Name");?></p>
            <p class="font14 color3">{{childInfo.nick}}</p>
        </div>
    </div>
    <div class="col-md-12 ">
        <div class="col-md-3"><p class="font12 color6"><?php echo Yii::t("labels", "Nationality");?></p>
            <p class="font14 color">{{childInfo.country}}</p>
        </div>
        <div class="col-md-3"><p class="font12 color6"><?php echo Yii::t("labels", "Date of Birth");?></p>
            <p class="font14 color">{{childInfo.birthday_search}}</p>
        </div>
        <div class="col-md-3"><p class="font12 color6"><?php echo Yii::t("labels", "Gender");?></p>
            <p class="font14 color">
                {{childInfo.gender==1 ? '<?php echo Yii::t("child", "Male")?>':'<?php echo Yii::t("child", "Female")?>'}}
            </p>
        </div>
    </div>
    <div class="clearfix"></div>
    <hr>
    <div class="col-md-12">
        <div class="col-md-3"><p class="font12 color6"><?php echo Yii::t("labels", "Bus NO.");?></p>
            <p class="font14 color">{{childInfo.busId}}</p>
        </div>
        <div class="col-md-9"><p class="font12 color6"><?php echo Yii::t("labels", "Bus Notes");?></p>
            <p class="font14 color">{{childInfo.BusContent}}</p>
        </div>
    </div>
    <div class="clearfix"></div>
    <hr>
    <div class="col-md-12 mb20">
        <div class="col-md-3" style="padding-right: 0px;">
            <p class="font12 color6"><?php echo Yii::t("labels", "Father");?></p>
            <div class="font14 color" style="display:flex;flex-wrap: wrap;align-items: center;">
                <div class="mr5">{{childInfo.fName}}</div>
                <div style="display:flex;flex-wrap: wrap;flex: 1;align-content: flex-start;">
                    <span v-for="(item,index) in childInfo.fFlag" class='label label-info mr5 parentLableCss' :style="'color:rgb('+item['color']+')'">
                        <span v-html="item.name"></span>
                    </span>
                </div>
            </div>
        </div>
        <div class="col-md-3"><p class="font12 color6"><?php echo Yii::t("labels", "Mobile Phone");?></p>
            <p class="font14 color">{{childInfo.fTel}}</p>
        </div>
        <div class="col-md-6"><p class="font12 color6"><?php echo Yii::t("child", "Email");?></p>
            <p class="font14 color">{{childInfo.fEmail}}</p>
        </div>
    </div>

    <div class="col-md-12 mb20">
        <div class="col-md-3" style="padding-right: 0px;">
            <p class="font12 color6"><?php echo Yii::t("labels", "Mother");?></p>
            <div class="font14 color" style="display:flex;flex-wrap: wrap;align-items: center;">
                <div class="mr5">{{childInfo.mName}}</div>
                <div style="display:flex;flex-wrap: wrap;flex: 1;align-content: flex-start;">
                    <span v-for="(item,index) in childInfo.mFlag" class='label label-info mr5 parentLableCss' :style="'color:rgb('+item['color']+')'">
                        <span v-html="item.name"></span>
                    </span>
                </div>
            </div>
        </div>
        <div class="col-md-3"><p class="font12 color6"><?php echo Yii::t("labels", "Mobile Phone");?></p>
            <p class="font14 color">{{childInfo.mTel}}</p>
        </div>
        <div class="col-md-6"><p class="font12 color6"><?php echo Yii::t("child", "Email");?></p>
            <p class="font14 color">{{childInfo.mEmail}}</p>
        </div>
    </div>
    <div class="clearfix"></div>
</div>
<script>
    var vue = new Vue({
        el: '#content',
        data: {
            imgBaseUrl:'<?php echo Yii::app()->themeManager->baseUrl?>',
            exportChildUrl:'<?php echo $this->createUrl("//mgrades/attendance/exportChild") ?>&tid=<?php echo $data['tid'];?>',
            childInfo:<?php echo CJSON::encode($data);?>,
            medical:false,
            medicalData:{}
        },
        created: function () {
        },
        mounted: function () {
        },
        methods: {
            getLabelExtraInfo(childid, item) {
                var labelArr = [];
                for (const labelKey in item) {
                     let label = item[labelKey]['flag']
                    labelArr.push(label)
                }
                if(labelArr.length <= 0){
                    return false;
                }
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("getLabelExtraInfo") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        child_id:childid,
                        label:labelArr,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.medical=true
                            that.medicalData=data.data
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            }
        }
    })
</script>
