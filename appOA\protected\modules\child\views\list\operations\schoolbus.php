<?php
$this->renderPartial('header');

$this->widget('zii.widgets.CMenu',array(
    'items'=> $pageSubMenu,
    'activeCssClass'=>'current',
    'id'=>'page-subMenu',
    'htmlOptions'=>array('class'=>'pageSubMenu')
));
echo CHtml::openTag("div", array("class"=>"c"));
echo CHtml::closeTag("div");
?>

<div class="h_a"><?php echo Yii::t("campus", "School Year");?></div>
<div class="prompt_text">
	<ul>
		<li>
            <?php
            $this->beginWidget('CActiveForm', array(
                'id'=>'start-year-form',
                'method'=>'get',
                'action'=>$this->createUrl('/child/list/operations', array('category'=>'bus')),
            ));
            echo CHtml::dropDownList('startyear', $startyear, Calendar::model()->getUpdownYear($branch->schcalendar));
            $this->endWidget();
            ?>
        </li>
	</ul>
</div>

<div class="mb10">
    <a href="<?php echo $this->createUrl('/child/list/operations', array('category'=>'bus', 'act'=>'editbus', 'startyear'=>$startyear));?>" title="<?php echo Yii::t("labels", "分配孩子")?>" class="btn J_dialog">
        <span class="add"></span>
        <?php echo Yii::t("labels", "分配孩子")?>
    </a>
</div>

<?php
$busAttrs = array(
	"bus_code","bus_type","driver_name","driver_mobile","aunt_name","aunt_mobile"
);

?>

<?php foreach($schoolBuses as $schoolBus){ ?>
	<?php if(count($schoolBus->child)):?>
	<div class="h_a"><?php echo $schoolBus->bus_title;?> <?php echo CHtml::link('导出Excel', array('/child/list/operations', 'category'=>'bus','act'=>'export','id'=>$schoolBus->bid, 'startyear'=>$startyear));?></div>
	<div class="table_full">
	<table width="100%">
		<colgroup>
			<col width="180">
		</colgroup>
		<tbody>
			<th>
				<ul class="businfo">
					<?php foreach($busAttrs as $attr):?>
						<li>
							<?php
							echo CHtml::activeLabel($schoolBus, $attr);
							echo CHtml::openTag('span');
							echo $schoolBus->getAttribute($attr);
							echo CHtml::closeTag('span');
							?>
						</li>
					<?php endforeach;?>
				</ul>
			</th>
			<td class="user_group">
				<div class="table_list">
					<table width="100%">
						<colgroup>
							<col width="50">
							<col width="100">
							<col width="100">
							<col width="100">
                            <col width="120">
							<col width="100">
							<col width="110">
							<col width="200">
                            <col>
						</colgroup>
						<tbody>
							<tr>
								<th><?php echo Yii::t("invoice", "序号")?></th>
								<th><?php echo Yii::t("invoice", "Child Name")?></th>
                                <th><?php echo Yii::t("global", "Action")?></th>
                                <th><?php echo Yii::t("labels", "Pick-up Time")?></th>
                                <th><?php echo Yii::t("labels", "接站地址")?></th>
                                <th><?php echo Yii::t("labels", "Drop-off Time")?></th>
                                <th><?php echo Yii::t("labels", "送达地址")?></th>
                                <th><?php echo Yii::t("labels", "孩子联系人")?></th>
                                <th><?php echo Yii::t("labels", "父母信息")?></th>
                                <th><?php echo Yii::t("invoice", "Memo")?></th>
							</tr>
							<?php $i=1;foreach($schoolBus->child as $child):?>
							<tr>
                                <th><?php echo $i++?></th>
								<td>
									<?php
									echo CHtml::link($child->childinfo->getChildName(), array("/child/index/index", "childid"=>$child->childid), array("class"=>"mr15", "target"=>"_blank"));									?>
								</td>
								<td>
									<?php
										echo CHtml::link(Yii::t("global", "Edit"),array('/child/list/operations', 'category'=>'bus', 'act'=>'editbus', 'id'=>$child->id), array('class'=>'mr10 J_dialog', 'title'=>addColon(Yii::t("invoice", "Child Name")).$child->childinfo->getChildName()));
										echo CHtml::link(Yii::t("global", "Delete"),array('/child/list/operations', 'category'=>'bus', 'act'=>'delbus', 'id'=>$child->id), array('class'=>'mr10 J_region_del'));
									?>
								</td>
								<td>
									<?php echo $child->time1;?>
								</td>
								<td>
									<?php echo $child->addr1;?>
								</td>
								<td>
									<?php echo $child->time2;?>
								</td>
                                <td>
									<?php echo $child->addr2;?>
								</td>
                                <td>
									<?php
                                    if (!empty($child->childmisc->upickup)){
                                        $txt = $child->childmisc->upickup;
                                        echo nl2br($txt);
                                    }
                                    //else {
                                       // $txt = $pphones[$child->childid]['f'] ? '父亲：'.$pphones[$child->childid]['f'] : '';
                                        //$txt .= $pphones[$child->childid]['m'] ? '<br>母亲：'.$pphones[$child->childid]['m'] : '';
                                   // }
                                    ?>
								</td>
                                <td>
                                    <?php 
                                        $txt = $pphones[$child->childid]['f'] ? '父亲：'.$pphones[$child->childid]['f'] : '';
                                        $txt .= $pphones[$child->childid]['m'] ? '<br>母亲：'.$pphones[$child->childid]['m'] : '';
                                        echo nl2br($txt);
                                    ?>
                                </td>
								<td>
									<?php echo $child->remark;?>
								</td>
							</tr>
							<?php endforeach;?>
						</tbody>
					</table>
				</div>
			</td>
		</tbody>
	</table>
	</div>
	<?php endif;?>
<?php }?>

<style>
ul.businfo li label{padding-right: 4px;color:#999;}
</style>

<script type="text/javascript">
$('.user_group').on('click', 'a.J_region_del', function(e){
    e.preventDefault();
    var $this = $(this),
            role = $this.data('role'),
            id = $this.data('id');

    //dialog
    head.dialog({
        message	: '确定该孩子不坐这辆校车？', 
        type	: 'confirm', 
        isMask	: false,
        follow	: $this,//跟随触发事件的元素显示
        onOk	: function() {
            $.getJSON($this.attr('href'), function(data){
                if(data.state == 'success') {
                    reloadPage(window);
                }else if(data.state == 'fail'){
                    head.dialog.alert(data.message);
                }
            });
        }
    });

});
$('#startyear').on('change', function(){
this.form.submit();
});
</script>