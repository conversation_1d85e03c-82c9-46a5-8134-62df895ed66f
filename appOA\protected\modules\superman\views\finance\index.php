<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <h1 class="page-header">
                <span class="glyphicon glyphicon-cog"></span> 个人账户余额与明细不等
            </h1>
        </div>
    </div>
    <div class="row">
        <div class="col-md-2">
            <?php
            echo CHtml::form($this->createUrl('/superman/finance'), 'get');
            $this->widget('ext.search.ChildSearchBox', array(
                'name' => 'childid',
                'acInputCSS' => 'form-control length_4',
                'htmlOptions' => array('class'=>'form-control')
            ));
            echo CHtml::tag('hr');
            echo CHtml::submitButton('提交', array('class'=>'btn btn-primary'));
            echo CHtml::endForm();
            ?>
        </div>
        <div class="col-md-10">
            <?php if($data):?>
                <h2>
                    <?php echo $data['name']?> 个人账户余额：<?php echo $data['credit']?> <?php echo CHtml::ajaxLink('更新', array('updateCredit'), array(
                        'type' => 'post',
                        'data' => array('childid'=>$childid),
                        'dataType' => 'json',
                        'success' => 'js:update',
                    ));?>
                </h2>
                <div class="table-responsive">
                    <table class="table table-hover table-bordered">
                        <thead>
                            <tr>
                                <th width="20">#</th>
                                <th width="100">项目</th>
                                <th width="100">Transaction</th>
                                <th width="100">金额</th>
                                <th width="100">进出</th>
                                <th width="100">当前余额</th>
                                <th width="100">更新时间</th>
                                <th width="100">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $total = 0;
                            foreach($data['creditList'] as $key=>$item):
                            ?>
                            <tr <?php if(!$item->transaction && $item->transaction_id):?>class="warning"<?php endif;?>>
                                <td><?php echo $key;?></td>
                                <td><?php echo $item->itemname?></td>
                                <td><?php echo $item->transaction_id?></td>
                                <td class="text-right"><?php echo $item->amount?></td>
                                <td class="text-center"><?php echo $item->inout?></td>
                                <td class="text-right"><?php echo $item->balance?></td>
                                <td class="text-center"><?php echo date('Y-m-d H:i:s', $item->updated_timestamp)?></td>
                                <td>
                                    <?php
                                    if(!$item->transaction && $item->transaction_id):
                                        echo CHtml::link('删除', 'javascript:;', array('onclick'=>'del('.$item->cid.')'));
                                    endif;
                                    ?>
                                </td>
                            </tr>
                            <?php
                            $total = $item->inout == 'in' ? $total + $item->amount :  $total - $item->amount;
                            endforeach;
                            ?>
                        </tbody>
                        <tfoot>
                            <tr>
                                <th>#</th>
                                <th>项目</th>
                                <th>Transaction</th>
                                <th class="text-right"><?php echo number_format($total, 2);?></th>
                                <th>进出</th>
                                <th class="text-right"><?php echo $item->balance?></th>
                                <th>更新时间</th>
                                <th>操作</th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            <?php endif;?>
        </div>
    </div>
</div>

<script>
    function update(data)
    {
        if(data.state == 'success'){
            resultTip({msg: data.message, callback: function(){
                reloadPage(window);
            }});
        } else {
            resultTip({msg: data.message, error: 1});
        }
    }

    function del(id)
    {
        if(confirm('确定删除？')){
            $.post('<?php echo $this->createUrl('del');?>', {id:id}, function(data){
                if(data.state == 'success'){
                    resultTip({msg: data.message, callback: function(){
                        reloadPage(window);
                    }});
                }
                else{
                    resultTip({msg: data.message, error: 1});
                }
            }, 'json');
        }
    }
</script>