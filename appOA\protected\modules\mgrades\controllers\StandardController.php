<?php
class StandardController extends BranchBasedController
{
    // 访问action的初级权限
    public $actionAccessAuths = array();

    public function createUrl($route, $params = array(), $ampersand = '&', $parentOnly = false)
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        return parent::createUrl($route, $params, $ampersand);
    }

    public function init()
    {
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'campusOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site', 'Campus Workspace');

        //初始化选择校园页面
        $this->branchSelectParams['hideOffice'] = true;
        $this->branchSelectParams['urlArray'] = array('//mgrades/standard/index');

        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/element/vue.global.js');
        $cs->registerCssFile(Yii::app()->themeManager->baseUrl . '/base/js/element/index.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/element/index.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/sortable/Sortable.min.js');
    }


    // *************************** 页面 *************************


    public function actionIndex()
    {
        $this->render('index');
    }

    public function actionOption()
    {
        $this->render('option');
    }

    public function actionTemplate()
    {
        $this->branchSelectParams['urlArray'] = array('//mgrades/standard/template');
        $this->render('template');
    }

    // *************************** 接口 *************************

    /**
     * 配置列表
     *
     * @return void
     */
    public function actionConfig()
    {
        $requestData = array();
        $requestUrl = 'standard/category/config';
        $this->remote($requestUrl, $requestData);
    }

    /**
     * 分类列表
     *
     * @return void
     */
    public function actionCategoryList()
    {
        $requestData = array(
            'program' => Yii::app()->request->getParam('program'),
            'grade' => Yii::app()->request->getParam('grade'),
        );
        $requestUrl = 'standard/category/list';
        $this->remote($requestUrl, $requestData);
    }


    /**
     * 分类更新
     *
     * @return void
     */
    public function actionCategoryUpdate()
    {
        $requestData = array(
            'id' => Yii::app()->request->getParam('id'),
            'program' => Yii::app()->request->getParam('program'),
            'grade' => Yii::app()->request->getParam('grade'),
            'title_cn' => Yii::app()->request->getParam('title_cn'),
            'title_en' => Yii::app()->request->getParam('title_en'),
            'option_groupid' => Yii::app()->request->getParam('option_groupid'),
            'items' => Yii::app()->request->getParam('items'),
        );
        $requestUrl = 'standard/category/update';
        $this->remote($requestUrl, $requestData);
    }

    /**
     * 分类排序
     *
     * @return void
     */
    public function actionCategorySort()
    {
        $requestData = array(
            'categoryIdList' => Yii::app()->request->getParam('categoryIdList'),
        );
        $requestUrl = 'standard/category/sort';
        $this->remote($requestUrl, $requestData);
    }

    /**
     * 分类删除
     *
     * @return void
     */
    public function actionCategoryDel()
    {
        $requestData = array();
        $id =  Yii::app()->request->getParam('id');
        $requestUrl = 'standard/category/del/' . $id;
        $this->remote($requestUrl, $requestData);
    }

    /**
     * 分类子项删除
     *
     * @return void
     */
    public function actionCategoryItemDel()
    {
        $requestData = array();
        $id =  Yii::app()->request->getParam('id');
        $requestUrl = 'standard/category/itemDel/' . $id;
        $this->remote($requestUrl, $requestData);
    }

    /**
     * 模板列表
     *
     * @return void
     */
    public function actionTemplateList()
    {
        $requestData = array();
        $requestUrl = 'standard/template/list';
        $this->remote($requestUrl, $requestData);
    }

    /**
     * 模板更新
     *
     * @return void
     */
    public function actionTemplateUpdate()
    {
        $requestData = array(
            'startyear' => Yii::app()->request->getParam('startyear'),
            'semester' => Yii::app()->request->getParam('semester'),
            'grade' => Yii::app()->request->getParam('grade'),
            'program' => Yii::app()->request->getParam('program'),
            'updateList' => Yii::app()->request->getParam('updateList') ? Yii::app()->request->getParam('updateList') : array(),
        );
        $requestUrl = 'standard/template/update';
        $this->remote($requestUrl, $requestData);
    }

    /**
     * 模板复制
     *
     * @return void
     */
    public function actionTemplateCopy()
    {
        $requestData = array(
            'startyear' => Yii::app()->request->getParam('startyear'),
            'copySchoolId' => Yii::app()->request->getParam('copySchoolId'),
            'copyyear' => Yii::app()->request->getParam('copyyear'),
            'programs' => Yii::app()->request->getParam('programs'),
            'grade' => Yii::app()->request->getParam('grade'),
        );
        $requestUrl = 'standard/template/copy';
        $this->remote($requestUrl, $requestData);
    }

    /**
     * 模板详情
     *
     * @return void
     */
    public function actionTemplateDetail()
    {
        $requestData = array(
            'startyear' => Yii::app()->request->getParam('startyear'),
            'grade' => Yii::app()->request->getParam('grade'),
            'program' => Yii::app()->request->getParam('program'),
        );
        $id =  Yii::app()->request->getParam('id');
        $requestUrl = 'standard/template/detail/' . $id;
        $this->remote($requestUrl, $requestData);
    }

    /**
     * 模板删除
     *
     * @return void
     */
    public function actionTemplateDel()
    {
        $requestData = array();
        $id =  Yii::app()->request->getParam('id');
        $requestUrl = 'standard/template/del/' . $id;
        $this->remote($requestUrl, $requestData);
    }

    /**
     * 选项列表
     *
     * @return void
     */
    public function actionOptionList()
    {
        $requestData = array();
        $requestUrl = 'standard/option/list';
        $this->remote($requestUrl, $requestData);
    }

    /**
     * 选项更新
     *
     * @return void
     */
    public function actionOptionUpdate()
    {
        $requestData = array(
            'id' => Yii::app()->request->getParam('id'),
            'group_id' => Yii::app()->request->getParam('group_id'),
            'label' => Yii::app()->request->getParam('label'),
            'title_cn' => Yii::app()->request->getParam('title_cn'),
            'title_en' => Yii::app()->request->getParam('title_en'),
            'desc_cn' => Yii::app()->request->getParam('desc_cn'),
            'desc_en' => Yii::app()->request->getParam('desc_en'),
        );
        $requestUrl = 'standard/option/update';
        $this->remote($requestUrl, $requestData);
    }

    /**
     * 选项排序
     *
     * @return void
     */
    public function actionOptionSort()
    {
        $requestData = array(
            'optionIdList' => Yii::app()->request->getParam('optionIdList')
        );
        $requestUrl = 'standard/option/sort';
        $this->remote($requestUrl, $requestData);
    }

    /**
     * 选项删除
     *
     * @return void
     */
    public function actionOptionDel()
    {
        $requestData = array();
        $id =  Yii::app()->request->getParam('id');
        $requestUrl = 'standard/option/del/' . $id;
        $this->remote($requestUrl, $requestData);
    }

    /**
     * 选项组更新
     *
     * @return void
     */
    public function actionOptionGroupUpdate()
    {
        $requestData = array(
            'list' => Yii::app()->request->getParam('list'),
        );
        $requestUrl = 'standard/optionGroup/update';
        $this->remote($requestUrl, $requestData);
    }

    /**
     * 选项组删除
     *
     * @return void
     */
    public function actionOptionGroupDel()
    {
        $requestData = array();
        $id =  Yii::app()->request->getParam('id');
        $requestUrl = 'standard/optionGroup/del/' . $id;
        $this->remote($requestUrl, $requestData);
    }




    // *************************** 方法 *************************

    public function remote($requestUrl, $requestData = array())
    {
        $requestData['schoolId'] = $this->branchId;
        $res = CommonUtils::requestDsOnline($requestUrl, $requestData);
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('message', $res['msg']);
            $this->addMessage('data', $res['data']);
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->addMessage('data', $res['data']);
        }
        $this->showMessage();
    }
}
