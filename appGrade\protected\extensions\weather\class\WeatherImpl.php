<?php

/*
 * <PERSON>.<PERSON>
 * 2012-8-1
 */

class WeatherImpl {

    private $place = null;
    private $cacheKey = null;

    public function __construct($place) {
        $this->setPlace($place);
    }

    public function setPlace($place) {
        $this->place = $place;
    }

    /**
     * 得到 气象信息
     * @param string $weatherApiForCn 中文api的名字
     * @param string $weatherApiForEn 英文api的名字
     * method_exists
     * @return NULL|Ambigous
     * <AUTHOR>
     * @time 2012-8-1
     */
    public function getWeather($weatherApiForCn = 'nmc', $weatherApiForEn = 'yahoo') {

        if (empty($this->place)) {
            return null;
        }

        $weatherApiForCn = ucfirst(strtolower($weatherApiForCn));
        $weatherApiForEn = ucfirst(strtolower($weatherApiForEn));

        if (Yii::app()->language == 'en_us') {
            $this->cacheKey = $this->place . "WeatherInfoFrom" . $weatherApiForEn . 'En';
        } else {
            $this->cacheKey = $this->place . "WeatherInfoFrom" . $weatherApiForCn . 'Cn';
        }
        $wi = Yii::app()->weatherCache->get($this->cacheKey);

        return CJSON::decode($wi);
    }

    /**
     * 得到 空气质量信息
     * http://iphone.bjair.info/m/beijing/mobile
     * http://www.airnow.gov/
     * http://pm25.moji001.com/aqi/index-33.html
     * @return NULL|Ambigous
     * <AUTHOR>
     * @time 2012-8-3
     */
    public function getAirQualityInfo($AQIApiForCn = 'Moji', $AQIApiForEn = 'Moji') {

        if (empty($this->place)) {
            return null;
        }

        $AQIApiForCn = ucfirst(strtolower($AQIApiForCn));
        $AQIApiForEn = ucfirst(strtolower($AQIApiForEn));

        if (Yii::app()->language == 'en_us') {
            $this->cacheKey = $this->place . "AQIFrom" . $AQIApiForEn . 'En';
        } else {
            $this->cacheKey = $this->place . "AQIFrom" . $AQIApiForCn . 'Cn';
        }
        $aqInfo = Yii::app()->weatherCache->get($this->cacheKey);

        return CJSON::decode($aqInfo);
    }

}

?>