<?php

/**
 * This is the model class for table "ivy_vote_basic_info".
 *
 * The followings are the available columns in table 'ivy_vote_basic_info':
 * @property integer $id
 * @property string $title_cn
 * @property string $title_en
 * @property integer $start_time
 * @property integer $end_time
 * @property integer $option_number
 * @property integer $vote_model
 * @property string $school_id
 * @property integer $operate_id
 * @property string $detail_description_cn
 * @property string $detail_description_en
 * @property integer $authorize_class_type
 * @property integer $whether_show
 * @property integer $update_time
 */
class WVoteBasicInfo extends CActiveRecord
{
	/*
	 * 总票数
	*/
	public $votesCasted = 0;
	/*
	 * 投票总人数
	*/
	public $totalVoters = 0;

	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return VoteBasicInfo the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_vote_basic_info';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
				array('start_time, end_time, option_number, vote_model, operate_id, authorize_class_type, whether_show, update_time', 'numerical', 'integerOnly'=>true),
				array('title_cn, title_en', 'length', 'max'=>1024),
				array('school_id', 'length', 'max'=>20),
				array('detail_description_cn, detail_description_en', 'safe'),
				// The following rule is used by search().
				// Please remove those attributes that should not be searched.
				array('id, title_cn, title_en, start_time, end_time, option_number, vote_model, school_id, operate_id, detail_description_cn, detail_description_en, authorize_class_type, whether_show, update_time', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
				'permission'=>array(self::HAS_ONE,'WVotePermission',"vote_id"),
				'options'=>array(self::HAS_MANY,'WVoteOptions',"vote_id"),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
				'id' => 'ID',
				'title_cn' => 'Title Cn',
				'title_en' => 'Title En',
				'start_time' => 'Start Time',
				'end_time' => 'End Time',
				'option_number' => 'Option Number',
				'vote_model' => 'Vote Model',
				'school_id' => 'School',
				'operate_id' => 'Operate',
				'detail_description_cn' => 'Detail Description Cn',
				'detail_description_en' => 'Detail Description En',
				'authorize_class_type' => 'Authorize Class Type',
				'whether_show' => 'Whether Show',
				'update_time' => 'Update Time',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('title_cn',$this->title_cn,true);
		$criteria->compare('title_en',$this->title_en,true);
		$criteria->compare('start_time',$this->start_time);
		$criteria->compare('end_time',$this->end_time);
		$criteria->compare('option_number',$this->option_number);
		$criteria->compare('vote_model',$this->vote_model);
		$criteria->compare('school_id',$this->school_id,true);
		$criteria->compare('operate_id',$this->operate_id);
		$criteria->compare('detail_description_cn',$this->detail_description_cn,true);
		$criteria->compare('detail_description_en',$this->detail_description_en,true);
		$criteria->compare('authorize_class_type',$this->authorize_class_type);
		$criteria->compare('whether_show',$this->whether_show);
		$criteria->compare('update_time',$this->update_time);

		return new CActiveDataProvider($this, array(
				'criteria'=>$criteria,
		));
	}
}