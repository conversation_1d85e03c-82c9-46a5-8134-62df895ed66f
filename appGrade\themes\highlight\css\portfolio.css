.pf-sub-nav{
}
.pf-sub-nav li{
	display: inline;
	border: 1px solid #dedede;
	border-radius: 4px;
	background:#fff;
	padding: 4px 8px;
}
.pf-sub-nav li.active{
	background:#FF0091;
	border-color: #FF0091;
}
.pf-sub-nav li.active a{
	color:#fff;
}
.pf-sub-nav li a{
	line-height: 24px;
	text-decoration: none;
	line-height: 32px;
	color:#666;
}
.pf-sub-nav li:hover a{
	color: #FF0091;
}
.pf-sub-nav li.active:hover a{
	color: #FFf;
}
#pfolio-nav-box{
	background:url(../images/pfolio_nav_bg.png) no-repeat left bottom transparent;
	padding-bottom: 4px;
	margin-top: 10px;
}
#pfolio-nav-box h2{
	margin-bottom: 0;
	text-align: right;
	color:#333;
	line-height: 32px;
	height:32px;
	background: url(../images/bt-right-icon.png) no-repeat right center transparent;
	margin-right: 10px;
	padding-right: 38px;
	color:#533419;
}
.no-bg{
	background: none !important;
}
ul.pfolio-list-b{
	text-align:right;
}
ul.pfolio-list-b li{
	height: 60px;
	display: inline-block;
	margin-left: 5px;
	border: 1px solid #fff;
	border-radius: 8px;
	background:#fff;
	*width: 188px;
}
ul.pfolio-list-b li{
	*display: inline;
}
ul.pfolio-list-b li a{
	text-decoration: none;
}
ul.pfolio-list-b li em{
	display:inline-block;
	width:63px;
	height:50px;
	margin: 5px 2px;
	*float: left;
}
ul.pfolio-list-b li em{
	*display:inline;
}
ul.pfolio-list-b li p{
	float: right;
	font-size: 14px;
	line-height: 30px;
	margin: 14px 10px 14px 4px;
	border-bottom: 2px solid #aeaeae;
}
ul.pfolio-list-b li:hover p{
	border-bottom: 2px solid #333;
}
ul.pfolio-list-b li.journal em{
	background: url(../images/pf_journal.png) no-repeat transparent;
}
ul.pfolio-list-b li.project em{
	background: url(../images/pf_project.png) no-repeat transparent;
}
ul.pfolio-list-b li.semester em{
	background: url(../images/pf_semester.png) no-repeat transparent;
}
.class-box{
	margin-bottom: 3em;
	padding-bottom: 1em;
	background: url(../images/pfolio_nav_bg.png) no-repeat -230px bottom transparent;
}
.class-box:hover {
	border-color:#fff;
}
#mainbox .class-box h1{
	height:38px;
	border-bottom: 1px solid #f7f7f7;
	z-index: 1;
}
#mainbox .class-box:hover h1 label{
}
#mainbox .class-box h1 label{
	width: 230px;
	text-align:right;
	left: -240px;
	padding: 0;
	background: none;
}
ul.class-attr{
	margin-bottom: 0.5em;
}
ul.class-attr li label{
	width: 200px;
	text-align:right;
	font-weight: bold;
	font-size: 14px;
	line-height: 28px;
	padding-right: 10px;
	display: inline-block;
	color:#999;
}
ul.class-attr li span{
	font-style: italic;
	font-family: verdana;
	border-bottom: 1px dotted #999;
}

.sub-nav li p.week-list{
	text-align:left;
	padding: 6px 0 6px 10px;
	margin-bottom: 0;
	margin-right: 10px;
	border-radius:4px;
	border-top-right-radius: 0;
	position: relative;
}
.sub-nav li p.wtitle{
    margin: 10px 10px -2px;
    text-align: right;
}
.sub-nav li p.wtitle span{
    color: #fff;
    font-size: 14px;
    line-height: 1.5em;
    padding: 4px 8px 0;
}
.sub-nav li span{
}
.sub-nav li span a:link,.sub-nav li span a:visited, .sub-nav li span.no-journal span{
	margin-bottom: 2px;
	border: 1px solid #FFB082;;
	border-radius: 2px;
	display: inline-block;
	color: #ededed;
	font-size: 12px;
	padding: 1px 2px;
	min-height: 20px;
	line-height: 20px;
	min-width:16px;
	text-align:center;
	
}
.sub-nav li span.withphoto a:link,.sub-nav li span.withphoto a:visited{
	background:url("../images/withphoto.gif") repeat top center !important;
	color:#fff !important;
}
.sub-nav li p.week-list span{
	position:relative;
}

.sub-nav li p.week-list #week-number-hint{
	white-space: nowrap;
	position: absolute;
	z-index:9999999;
}

#left-col{
	overflow-y:visible !important;
}

.sub-nav li p.week-list #week-number-hint {
    background: url("../images/unpay_reminder_bg.png") no-repeat scroll right -30px transparent;
    color: #333;
    cursor: pointer;
    display: inline-block;
    height: 30px;
    line-height: 24px;
	z-index: 9999999;
	visibility: hidden;
}
.sub-nav li p.week-list #week-number-hint span{
    background: url("../images/unpay_reminder_bg.png") no-repeat scroll left 0 transparent;
    display: inline-block;
    margin-right: 1em;
    padding-left: 1em;
	height:30px;
}
.sub-nav li span.no-journal span{
	color:#FF9C63;
	border-color: #FF8944;
}
.sub-nav li.active span a:hover{
	background:#90B82F;
	color:#333;
}
.sub-nav li.inside span a:hover,
.sub-nav li.active span a.active:link,.sub-nav li.active span a.active:visited{
	background:#fff !important;
	color:#01C2F4 !important;
}
.sub-nav li span:hover{
}

/*****semester report ********/
.sub-nav li.active li a:link,.sub-nav li.active li a:visited,.sub-nav li li a:link,.sub-nav li li a:visited{
	font-size:12px;
	background: none;
}


/*Journal XIN*/
p.journal-date{
	font-size: 16px;
	font-weight:bold;
	color:#333
}
p.journal-date span{
	font-size: 12px;
	font-weight: normal;
	color:#999;
}
.journal-box{
    background: #f3f3ec;
    border: 1px solid #f3f3ec;
    border-radius: 8px;
    padding: 0 10px 10px;
	margin-bottom: 20px;
}
.journal-box img.face {
    border: 5px solid #FFFFFF;
    box-shadow: 2px 2px 3px #666666;
    margin: 0 20px 10px 0;
}
.journal-box h3{
	line-height:32px;
	border-bottom: 2px solid #549033;
	color: #549033;
	font-weight: bold;
	font-size: 16px;
}
.journal-box h3 em{
    font-size: 14px;
    color: #999;
    margin-left: 15px;
	font-style: normal;
	font-weight: normal;
}
.journal-box dt{
	float: left;
	width: 120px;
}
.journal-box dd p.cd{
	font-size:16px;
	color:#999;
	margin-bottom: 10px;
}
.journal-box ul.teachers{
	/*width:120px;*/
}
.journal-box ul.teachers li{
	float: left;
	width:128px;
	color:#333;
}
.journal-box .seperator{
	height:0;
	border-top: 2px dotted #fff;
	margin: 0.5em 0;
}
.key-reminder{
	position: relative;
	width: 653px;
	min-height: 207px;
	background:url("../images/keyreminder/footer.png") no-repeat bottom left transparent;
	padding-bottom: 37px;
	margin-bottom: 10px;
}
.key-reminder .reminder{
	min-height: 130px;
	background:url("../images/keyreminder/all_bg.png") repeat-y transparent;
	padding: 64px 40px 40px 50px;
}
.key-reminder em.pinner{
	position: absolute;
	left: -18px;
	top: 37px;
	background:url("../images/keyreminder/pinner.png") no-repeat transparent;
	display: block;
	width: 54px;
	height: 32px;	
}
.key-reminder em.left-bottom{
	position: absolute;
	left: 0;
	bottom: 37px;
	background:url("../images/keyreminder/lefter.png") no-repeat transparent;
	display: block;
	width: 34px;
	height: 170px;
}
.key-reminder em.right-top{
	position: absolute;
	right: 0;
	top: 0;
	background:url("../images/keyreminder/righter.png") no-repeat transparent;
	display: block;
	width: 20px;
	height: 170px;
}
.semester-report{
	background: url(../images/semester_footer.png) no-repeat bottom left;
	padding-bottom:75px;
}
.semester-report .main-content{
	background: url(../images/semester_body.png) repeat-y;
	min-height: 25px;
	padding: 0 20px 0 10px;
}
.main-content .basic{
	line-height: 1.8em;
	font-size: 16px;
	margin-bottom: 2em;
}
.main-content .basic li{
	height: 29px;
	line-height: 29px;
	position: relative;
	border-bottom: 1px dotted #666;
	margin-bottom: 0.5em;
}
.main-content .basic label{
	display: inline-block;
	width:140px;
	text-align:right;
	padding-right: 10px;
	font-weight:bold;
	color:#666;
	height:30px;
	line-height:30px;
	background:#fff;
}
.main-content h3{
	font-weight:bold;
	color:#666;
	height:30px;
	line-height:30px;
}
.semester-report .head{
	background: url(../images/semester_header.png) no-repeat top left;
	height: 100px;
}

.semester-report .header{
	background: url(../images/progress_header.png) no-repeat top left;
	height: 100px;
}

.semester-report p{
	font-size:14px;
	line-height:1.5em;
}
.semester-report p img{
    border: 5px solid #FFFFFF;
    box-shadow: 2px 2px 3px #666666;
    margin: 0 20px 0 0;
	max-height:370px;
}
.semester-report ul.mission{
    list-style: disc outside none;
    padding-left: 40px;	
}
.semester-report ul.mission li{
	font-size: 14px;
}
.orbit-wrapper .orbit-caption {
	background: white !important;
	color: #999 !important;
	text-align: right !important;
}
.wa img{
	border: 5px solid white;
	box-shadow: 2px 2px 3px #666;
}
p.caption{
	margin: 0.5em 0 1em 0;
}
.download-list{
	margin-left: 40px;
	border-radius: 4px;
	background: #fff;
	padding: 10px;
}
.download-list li{
	list-style: disc;
	line-height: 22px;
	height: 22px;
	margin: 0;
	margin-left: 20px;
	background: url("../images/downlist_bg.png") repeat-x left top transparent;
	position: relative;
}
.download-list li span{
	background: #fff;
	color:#999;
	padding: 0 10px 0 0;
}
.download-list li em{
	position:absolute;
	right: 0;
	top: 0;
	height: 22px;
	line-height: 22px;
	background:#fff;
	padding: 0 10px;
}
.download-list li em span{
	padding-right: 0;
}
.download-list li:hover{
	background: url("../images/downlist_bg.png") repeat-x left -22px transparent;
}
.download-list li:hover span{
	color:#333;
}
.download-list li:hover a{
	color:#FF0091;
}
.download-list li a.package{
	background: url("../images/icons/package.gif") no-repeat left 2px transparent;
	padding-left: 22px;
}
.download-list li a.download{
	background: url("../images/icons/download.gif") no-repeat left 2px transparent;
	padding-left: 22px;
}
.weekly-misc{
	margin-bottom: 20px;
	text-align: right;
}
.weekly-misc ul li{
	float: right;
	margin-left: 20px;
}
.weekly-misc ul li a{
	background:url("../images/weekly_misc.gif") no-repeat 4px top #f2f2f2;
	display: block;
	height: 32px;
	line-height: 32px;
	padding-left: 44px;
	padding-right: 10px;
	/*width: 100px;*/
	text-align: center;
	color: #FF0091;
	text-decoration: none;
	border-radius: 4px;
}
.weekly-misc ul li a:hover{
	color:#F36601;
}
.weekly-misc ul li.lunchMenu a{
	background-position: 4px top;
}
.weekly-misc ul li.schedule a{
	background-position: 4px -32px;
}
.weekly-misc ul li.teaching a{
    background-position: 4px -64px;
}

.recent-journals{
	padding-left: 140px;
	margin-left: 20px;
	margin-top: 10px;
	background: url("../images/journal.png") no-repeat left 40px transparent;
}
.recent-journals h5{
	font-size: 16px;
	margin-bottom: 10px;
	color:#549033;
}
.recent-journals li{
	line-height: 25px;
	height: 25px;
	background: url(../images/list_bg.gif) no-repeat left -14px transparent;
	padding-left: 12px;
	margin-left: 8px;
}
.recent-journals li:hover{
	background-position: left -46px;
}
.recent-journals li a{
	text-decoration: none;
	border-bottom: 1px dotted #dedede;
}
.recent-journals li a:hover{
	color: #549033;
}
.recent-journals li.more{
	background: none;
}
ul.pfolio-list-i{
	text-align:left;
	padding-top:2px;
}
ul.pfolio-list-i li{
	height: 40px;
	width: 155px;
    float: left;
    position: relative;
	margin-bottom: 4px;
}
ul.pfolio-list-i li div{
    position: absolute;
    left: 35px;
    top: 0;
	padding-top: 7px;
	padding-left: 4px;
    cursor: pointer;
}
ul.pfolio-list-i li a:hover{
	text-decoration: underline;
}
ul.pfolio-list-i li.calendar em{
	background: url(../images/icons/calendar.png) no-repeat transparent;
}
ul.pfolio-list-i li.schedule em{
	background: url(../images/icons/schedule.png) no-repeat transparent;
}
ul.pfolio-list-i li.lunch em{
	background: url(../images/icons/lunch.png) no-repeat transparent;
}
ul.pfolio-list-i li.teacher em{
	background: url(../images/icons/teacher.png) no-repeat transparent;
}
ul.pfolio-list-i li.contact em{
	background: url(../images/icons/contact.png) no-repeat transparent;
}
ul.pfolio-list-i li em{
    width: 32px;
    height: 32px;
    display: block;
}
ul.pfolio-list-i li a{
    display: block;
}
.journal-box pre { 
	whitewhite-space: pre-wrap; /* css-3 */ 
	whitewhite-space: -moz-pre-wrap; /* Mozilla, since 1999 */ 
	whitewhite-space: -pre-wrap; /* Opera 4-6 */ 
	whitewhite-space: -o-pre-wrap; /* Opera 7 */ 
	word-wrap: break-word; /* Internet Explorer 5.5+ */ 
	whitewhite-space : normal ; /* Internet Explorer 5.5+ */
	font-style: italic;
}
.journal-box div.hover{
    height: 22px;
    line-height: 22px;
    overflow: hidden;
    margin-bottom: 5px;
}

.pdf-icon{
	background: url(../images/pdf-icon.png) no-repeat transparent;
}