<?php $form=$this->beginWidget('CActiveForm', array(
	'id'=>'diglossia-form',
	'enableAjaxValidation'=>false,
	'htmlOptions'=>array('class'=>'J_ajaxForm'),
));
?>
<div class="pop_cont pop_table" style="height:auto;">
    <table width="100%">
        <colgroup>
            <col class="th" />
            <col />
        </colgroup>
        <tbody>
            <?php if ($type == 1):?>
            <tr>
                <th><?php echo $form->labelEx($model,'category_sign'); ?></th>
                <td><?php
                if ($model->isNewRecord):
                    echo $form->textField($model,'category_sign',array('class'=>'input length_3'));
                else:
                    echo $model->category_sign;
                endif;
                ?></td>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($model,'category_entitle'); ?></th>
                <td><?php echo $form->textField($model,'category_entitle', array('class'=>'input length_3')); ?></td>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($model,'category_cntitle'); ?></th>
                <td><?php echo $form->textField($model,'category_cntitle', array('class'=>'input length_3')); ?></td>
            </tr>
            <?php elseif($type == 2):?>
            <tr>
                <th><?php echo $form->labelEx($model,'entitle'); ?></th>
                <td><?php echo $form->textField($model,'entitle', array('class'=>'input length_3')); ?></td>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($model,'cntitle'); ?></th>
                <td><?php echo $form->textField($model,'cntitle', array('class'=>'input length_3')); ?></td>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($model,'weight'); ?></th>
                <td><?php echo $form->numberField($model,'weight', array('class'=>'input length_1')); ?></td>
            </tr>
            <?php echo $form->hiddenField($model, 'category_id');?>
            <?php endif;?>
        </tbody>
    </table>
</div>
<div class="pop_bottom">
    <button type="button" class="btn fr" id="J_dialog_close"><?php echo Yii::t('global','Cancel');?></button>
    <button class="btn fr btn_submit mr10 J_ajax_submit_btn"><?php echo Yii::t('global','Submit');?></button>
</div>
<?php $this->endWidget(); ?>