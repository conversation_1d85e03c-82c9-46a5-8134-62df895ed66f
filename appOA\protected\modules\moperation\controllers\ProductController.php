<?php

class ProductController extends ProtectedController
{
    public $actionAccessAuths = array(
        'admin'         => 'o_B_Access',
        'update'        => array('access'=>'o_B_Access', 'admin'=>'o_B_Adm_PointExchange'),
        'delete'        => 'o_B_Adm_PointExchange',
        'Gallery'       => 'o_B_Access',
        'Upload'        => 'o_B_Adm_PointExchange',
        'Delpic'        => 'o_B_Adm_PointExchange',
        'Cover'         => 'o_B_Adm_PointExchange',
        'Pic'           => array('access'=>'o_B_Access', 'admin'=>'o_B_Adm_PointExchange'),
        'StockCreate'   => array('access'=>'o_B_Access', 'admin'=>'o_B_Adm_PointExchange'),
        'StockDelete'   => 'o_B_Adm_PointExchange',
    );

    public $dialogWidth = 600;
    
    public function init() {
        parent::init();
        Yii::import('common.models.points.*');

        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'hqOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site','HQ Operations');
        
        $this->mainMenu = array(
			array(
				'label' => '订单管理',
				'url' => array('/operations/order/admin')
			),
			array(
				'label' => '商品管理',
				'url' => array('/operations/product/admin')
			),
		);
		
    }

    /**
     * 编辑商品页面及保存处理
     * @param int $id
     */
    public function actionUpdate($id=0)
	{
        // $this->layout='//layouts/dialog';
		$model=$this->loadModel($id);
        //照片管理
        $cfgs = OA::LoadConfig('CfgPhoto');
        $cfgsModules = $cfgs['points'];
        $imageModel = PointsImages::model()->getProductImages($id);
        $thumbdir = $cfgsModules['sizes']['thumbW'] ? 'thumb/' : '';
        $imageUrl = $cfgsModules['uploadUrl'] . $thumbdir;
        $productid = $id;

        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/plupload/plupload.full.min.js');
		
        if(isset($_POST['PointsProduct']))
		{
			$model->attributes=$_POST['PointsProduct'];
            $model->userid =  Yii::app()->user->id;
            $model->updated =  time();

			if($model->save()){
                $this->addMessage('state', 'success');
                $this->addMessage('refresh', true);
                $this->addMessage('referer', $this->createUrl('admin'));   
                $this->addMessage('message', Yii::t('message','Data saved!'));
            }else {
                $this->addMessage('state', 'fail');
                $errs = current($model->getErrors());
                $this->addMessage('message', $errs?$errs[0]:Yii::t('message','Saving Failed!'));
            }
            $this->showMessage();
		}

		$this->render('update',array(
            'productid'=>$productid,
            'imageUrl'=>$imageUrl,
            'imageModel'=>$imageModel,
			'model'=>$model,
		));
	}

    /**
     * 删除商品
     * @throws CHttpException
     */
    public function actionDelete()
	{
		if(Yii::app()->request->isPostRequest)
		{
            $id = Yii::app()->request->getParam('id', 0);
			$order = PointsOrder::model()->exists('productid=:productid',array(':productid'=>$id));
			$stock = PointsStock::model()->exists('product_id=:product_id',array(':product_id'=>$id));
			if ($stock || $order)
			{
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('points', '已经增加库存或已生成订单，不能删除！'));
			}
			else
			{
				if($this->loadModel($id)->delete()){
                    $this->addMessage('state', 'success');
                    $this->addMessage('refresh', true);
                    $this->addMessage('message', '删除成功！');
                }
				else{
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', '删除失败！');
                }
			}
            $this->showMessage();
		}
		else
			throw new CHttpException(400,'Invalid request. Please do not repeat this request again.');
	}

    /**
     * 积分兑换商品管理主页面
     */
    public function actionAdmin()
	{
		$model=new PointsProduct('search');
		$model->unsetAttributes();  // clear any default values
		if(isset($_GET['PointsProduct']))
			$model->attributes=$_GET['PointsProduct'];

		$this->render('admin',array(
			'model'=>$model,
		));
	}

	/**
	 * Returns the data model based on the primary key given in the GET variable.
	 * If the data model is not found, an HTTP exception will be raised.
	 * @param integer the ID of the model to be loaded
	 */
	public function loadModel($id)
	{
		$model=PointsProduct::model()->findByPk((int)$id);
		if($model===null)
			$model = new PointsProduct;
		return $model;
	}

    /**
     * 上传商品照片页面
     * @param int $id
     */
    public function actionGallery($id = 0)
	{
        $this->layout='//layouts/dialog';
		$cfgs = OA::LoadConfig('CfgPhoto');
        $cfgsModules = $cfgs['points'];
		$imageModel = PointsImages::model()->getProductImages($id);
		$model = PointsProduct::model()->findByPk($id);
//		$subdir = $cfgsModules['filePrefix'] . $id;
		$thumbdir = $cfgsModules['sizes']['thumbW'] ? 'thumb/' : '';
		$imageUrl = $cfgsModules['uploadUrl'] . $thumbdir;
		$this->render('gallery',array('productid'=>$id,'imageUrl'=>$imageUrl,'imageModel'=>$imageModel,'model'=>$model));
	}

    /**
     * 处理商品上传照片
     * @param int $productid
     * @throws CHttpException
     */
    public function actionUpload($productid = 0)
	{
		if ($productid)
		{
			// HTTP headers for no cache etc
        	header('Content-type: text/plain; charset=UTF-8');
            header("Expires: Mon, 26 Jul 1997 05:00:00 GMT");
            header("Last-Modified: " . gmdate("D, d M Y H:i:s") . " GMT");
            header("Cache-Control: no-store, no-cache, must-revalidate");
            header("Cache-Control: post-check=0, pre-check=0", false);
            header("Pragma: no-cache");

            // Settings
            $cfgs = OA::LoadConfig('CfgPhoto');
            $cfgsModules = $cfgs['points'];
//            $subdir = $cfgsModules['filePrefix'] . $productid;
//            $targetDir = $cfgsModules['subDir'] . $subdir;
            $targetDir = $cfgsModules['subDir'];
            $cleanupTargetDir = false; // Remove old files
            $maxFileAge = 60 * 60; // Temp file age in seconds
            // 5 minutes execution time
            @set_time_limit(5 * 60);
            // usleep(5000);
            // Get parameters
            $chunk = isset($_REQUEST["chunk"]) ? $_REQUEST["chunk"] : 0;
            $chunks = isset($_REQUEST["chunks"]) ? $_REQUEST["chunks"] : 0;
            $fileName = isset($_REQUEST["name"]) ? $_REQUEST["name"] : '';
            $suffix = strtolower(substr($fileName, strrpos($fileName, '.')));
            // Clean the fileName for security reasons
            $fileName = preg_replace('/[^\w\._\s]+/', '', $fileName);
            // Create target dir
            if (!file_exists($targetDir))
            {
            	 OA::autoMkDirs($targetDir);
            }
            // Remove old temp files
            if (is_dir($targetDir) && ($dir = opendir($targetDir))) {
                while (($file = readdir($dir)) !== false) {
                    $filePath = $targetDir . DIRECTORY_SEPARATOR . $file;

                    // Remove temp files if they are older than the max age
                    if (preg_match('/\\.tmp$/', $file) && (filemtime($filePath) < time() - $maxFileAge))
                        @unlink($filePath);
                }

                closedir($dir);
            } else
                throw new CHttpException(500, Yii::t('points', "Can't open temporary directory."));

            // Look for the content type header
            if (isset($_SERVER["HTTP_CONTENT_TYPE"]))
                $contentType = $_SERVER["HTTP_CONTENT_TYPE"];

            if (isset($_SERVER["CONTENT_TYPE"]))
                $contentType = $_SERVER["CONTENT_TYPE"];

            if (strpos($contentType, "multipart") !== false) {
                if (isset($_FILES['file']['tmp_name']) && is_uploaded_file($_FILES['file']['tmp_name'])) {
                    // Open temp file
                    $out = fopen($targetDir . DIRECTORY_SEPARATOR . $fileName, $chunk == 0 ? "wb" : "ab");
                    if ($out) {
                        // Read binary input stream and append it to temp file
                        $in = fopen($_FILES['file']['tmp_name'], "rb");

                        if ($in) {
                            while ($buff = fread($in, 4096))
                                fwrite($out, $buff);
                        } else
                            throw new CHttpException(500, Yii::t('points', "Can't open input stream."));

                        fclose($out);
                        unlink($_FILES['file']['tmp_name']);
                    } else
                        throw new CHttpException(500, Yii::t('points', "Can't open output stream."));
                } else
                    throw new CHttpException(500, Yii::t('points', "Can't move uploaded file."));
            } else {
                // Open temp file
                $out = fopen($targetDir . DIRECTORY_SEPARATOR . $fileName, $chunk == 0 ? "wb" : "ab");
                if ($out) {
                    // Read binary input stream and append it to temp file
                    $in = fopen("php://input", "rb");

                    if ($in) {
                        while ($buff = fread($in, 4096))
                            fwrite($out, $buff);
                    } else
                        throw new CHttpException(500, Yii::t('points', "Can't open input stream."));

                    fclose($out);
                } else
                    throw new CHttpException(500, Yii::t('points', "Can't open output stream."));
            }

            // After last chunk is received, process the file
            $ret = array('result' => '1');
            if (intval($chunk) + 1 >= intval($chunks)) {

                $originalname = $fileName;
                if (isset($_SERVER['HTTP_CONTENT_DISPOSITION'])) {
                    $arr = array();
                    preg_match('@^attachment; filename="([^"]+)"@', $_SERVER['HTTP_CONTENT_DISPOSITION'], $arr);
                    if (isset($arr[1]))
                        $originalname = $arr[1];
                }

                // **********************************************************************************************
                // Do whatever you need with the uploaded file, which has $originalname as the original file name
                // and is located at $targetDir . DIRECTORY_SEPARATOR . $fileName
                // **********************************************************************************************



                $oldname = $targetDir . DIRECTORY_SEPARATOR . $fileName;
                $fileName = uniqid() . $suffix;
                @rename($oldname, $targetDir . DIRECTORY_SEPARATOR . $fileName);

                $image = Yii::app()->image->load($targetDir . DIRECTORY_SEPARATOR . $fileName);
                $image->quality(100);
                $image->resize($cfgsModules['sizes']['normalW'], $cfgsModules['sizes']['normalH']);
                $image->save();
                
                if ($cfgsModules['sizes']['thumbW']) {
                    $image = Yii::app()->image->load($targetDir . DIRECTORY_SEPARATOR . $fileName);
                    $subtargetDir = $targetDir . DIRECTORY_SEPARATOR . 'thumb';
                    if (!file_exists($subtargetDir))
                    {
                    	OA::autoMkDirs($subtargetDir);
                    }
                    $image->resize($cfgsModules['sizes']['thumbW']);
                    $image->save($subtargetDir . DIRECTORY_SEPARATOR . $fileName);
                }
				
                $picmodel = new PointsImages;
                $picmodel->productid = $productid;
                $picmodel->image = $fileName;
                if ($picmodel->save()) {
                    $thumbdir = $cfgsModules['sizes']['thumbW'] ? 'thumb/' : '';
                    $photourl = $cfgsModules['uploadUrl'] . $thumbdir;
                    $ret['pic'] = $photourl . $picmodel->image;
                    $ret['id'] = $picmodel->id;
                } else {
                    $ret['result'] = 0;
                }
            }

            // Return response
            die(json_encode($ret));
		}
	}

    /**
     * 删除商品照片
     */
    public function actionDelpic()
	{
		$id = Yii::app()->request->getPost('id',0);
		$model = PointsImages::model()->findByPk($id);
		if ($model->delete())
		{
            $criteria = new CDbCriteria;
            $criteria->compare('cover', $id);
            PointsProduct::model()->updateAll(array('cover'=>0), $criteria);
			$cfgs = OA::LoadConfig('CfgPhoto');
            $cfgsModules = $cfgs['points'];
            @unlink($cfgsModules['subDir'] . '/thumb/' . $model->image);
            @unlink($cfgsModules['subDir'] . $model->image);
            echo CJSON::encode(
                array(
                    'status' => 'success',
                )
            );
		}
		else
		{
			echo CJSON::encode(
                array(
                    'status' => 'failure',
                    'message' => Yii::t("points", '删除失败'),
                )
            );
		}
	}

    /**
     * 设置商品封面图片
     */
    public function actionCover()
	{
		$id = Yii::app()->request->getPost('id', 0);
		$model = PointsImages::model()->findByPk($id);

		if (PointsProduct::model()->updateByPk($model->productid, array('cover' => $id))) {
			echo CJSON::encode(
			array(
                'status' => 'success',
                'message' => Yii::t("bview", '设置成功'),
			)
			);
		} else {
			echo CJSON::encode(
			array(
                'status' => 'failure',
                'message' => Yii::t("bview", '设置失败'),
			)
			);
		}
	}

    /**
     * 商品图片页面及保存处理
     * @param int $id
     */
    public function actionPic($id=0)
	{
		if ($id)
		{
			$model = PointsImages::model()->findByPk($id);
			$cfgs = OA::LoadConfig('CfgPhoto');
            $cfgsModules = $cfgs['points'];
//            $subdir = $cfgsModules['filePrefix'] . $model->productid;
            $thumbdir = $cfgsModules['sizes']['thumbW'] ? 'thumb/' : '';
			$imageUrl = $cfgsModules['uploadUrl'] . $thumbdir;
			if (isset($_POST['PointsImages']))
			{
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('message','Saving Failed!'));
				$model->attributes = $_POST['PointsImages'];
				if ($model->save()) {
				 	$this->addMessage('state', 'success');                 
                    $this->addMessage('message', Yii::t('message','Data saved!'));
				}
                $this->showMessage();
			}
            $this->layout = '//layouts/dialog';
			$this->render('pic', array('model' => $model, 'imageUrl' => $imageUrl));
		}
	}

    /**
     * 商品库存管理页面及保存处理
     */
    public function actionStockCreate()
    {
        $productId = Yii::app()->request->getParam('productid',0);
        $model=new PointsStock;
        $model->product_id = $productId;
        if(isset($_POST['PointsStock']))
        {
            $this->addMessage('fail', 'success');
            $this->addMessage('message', Yii::t('message','Saving Failed!'));
            $model->attributes=$_POST['PointsStock'];
            $model->userid = Yii::app()->user->id;
            $model->update_timestamp = time();
            if($model->save())
            {
                $model->refresh();
                //count current stock
                $stock = PointsStock::model()->countStock($model->product_id);
                //update current stock
                if (PointsProduct::model()->updateStock($model->product_id, $stock))
                {
                    $this->addMessage('state', 'success');
                    $this->addMessage('callback', 'callback');
                    $this->addMessage('message', Yii::t('message','Data saved!'));
                }
            }
            $this->showMessage();
        }
        $this->layout='//layouts/dialog';
        $stockModel = new PointsStock('search');
        $stockModel->product_id = $stockModel->product_id;
        $this->render('stockcreate', array('model' => $model,'stockModel'=>$stockModel), false, true);
    }

    /**
     * 删除添加的库存
     * @throws CHttpException
     */
    public function actionStockDelete()
    {
        if(Yii::app()->request->isPostRequest)
        {
            $id = Yii::app()->request->getParam('id', 0);
            $model = PointsStock::model()->findByPk($id);
            $stock = PointsStock::model()->countStock($model->product_id);
            if ($stock - $model->num >=0)
            {
                if ($model->delete())
                {
                    $stock = PointsStock::model()->countStock($model->product_id);
                    PointsProduct::model()->updateStock($model->product_id, $stock);
                    $this->addMessage('state', 'success');
                    $this->addMessage('refresh', true);
                    $this->addMessage('message', '删除成功');
                }
            }
            else
            {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('operations', '不能删除，添加的库存已有订单生成！'));
            }
            $this->showMessage();
        }
        else
        {
            throw new CHttpException(400,'Invalid request. Please do not repeat this request again.');
        }
    }
}
