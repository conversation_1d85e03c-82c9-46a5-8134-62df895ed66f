<?php

/**
 * This is the model class for table "ivy_reports_item".
 *
 * The followings are the available columns in table 'ivy_reports_item':
 * @property integer $id
 * @property integer $template_id
 * @property integer $category_id
 * @property integer $weight
 * @property string $title_en
 * @property string $title_cn
 * @property integer $update_user
 * @property integer $updated
 */
class ReportsItem extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_reports_item';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('template_id, category_id, title_en, title_cn, update_user, updated, standard_code', 'required'),
			array('template_id, category_id, weight, update_user, updated', 'numerical', 'integerOnly'=>true),
			array('title_en', 'length', 'max'=>512),
			array('title_cn, standard_code', 'length', 'max'=>255),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, template_id, category_id, weight, title_en, title_cn, update_user, updated, standard_code', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'template_id' => 'Template',
			'category_id' => 'Category',
			'weight' => 'Weight',
			'title_en' => 'Item (English)',
			'title_cn' => 'Item (Chinese)',
			'update_user' => 'Update User',
			'updated' => 'Updated',
			'standard_code' => 'Standard Code',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('template_id',$this->template_id);
		$criteria->compare('category_id',$this->category_id);
		$criteria->compare('weight',$this->weight);
		$criteria->compare('title_en',$this->title_en,true);
		$criteria->compare('title_cn',$this->title_cn,true);
		$criteria->compare('update_user',$this->update_user);
		$criteria->compare('updated',$this->updated);
		$criteria->compare('standard_code',$this->standard_code);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return ReportsItem the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

    public function getTitle()
    {
        return CommonUtils::autoLang($this->title_cn, $this->title_en);
    }
}
