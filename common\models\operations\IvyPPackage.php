<?php

/**
 * This is the model class for table "ivy_p_package".
 *
 * The followings are the available columns in table 'ivy_p_package':
 * @property string $id
 * @property string $cn_title
 * @property string $en_title
 * @property integer $status
 * @property integer $weight
 * @property integer $create_userid
 * @property integer $update_timestamp
 */
class IvyPPackage extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @return IvyPPackage the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_p_package';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('id, cn_title, en_title,status', 'required'),
			array('status, weight, create_userid, update_timestamp', 'numerical', 'integerOnly'=>true),
			array('id', 'length', 'max'=>10),
			array('id', 'unique'),
			array('cn_title, en_title', 'length', 'max'=>255),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, cn_title, en_title, status, weight, create_userid, update_timestamp', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => Yii::t('operations', '标识'),
			'cn_title' => Yii::t('operations', '中文标题'),
			'en_title' => Yii::t('operations', '英文标题'),
			'status' => Yii::t('operations', '状态'),
			'weight' => Yii::t('operations', '排序'),
			'create_userid' => Yii::t('operations', '创建用户'),
			'update_timestamp' => Yii::t('operations', '更新日期'),
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id,true);
		$criteria->compare('cn_title',$this->cn_title,true);
		$criteria->compare('en_title',$this->en_title,true);
		$criteria->compare('status',$this->status);
		$criteria->compare('weight',$this->weight);
		$criteria->compare('create_userid',$this->create_userid);
		$criteria->compare('update_timestamp',$this->update_timestamp);

		return new CActiveDataProvider(get_class($this), array(
			'criteria'=>$criteria,
		));
	}
	
	/**
	 * 显示类别的状态
	 * @return string
	 * <AUTHOR>
	 * @copyright IvyGroup
	 */
	public function showStatus()
	{
		return ($this->status) ? Yii::t('operations', '隐藏') : Yii::t('operations', '显示');
	}
	
	public function getTitle()
	{
		return ( Yii::app()->language == "zh_cn" ) ? $this->cn_title : $this->en_title;
	}
}