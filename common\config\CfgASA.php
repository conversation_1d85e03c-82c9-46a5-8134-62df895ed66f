<?php
return array(

	/**
	考勤周期起始日
	  a. 若设置为1，则表示考勤区间从上月1号（含）到本月1号（不含）
      b. 若定为21日，则考勤周期为上一个月21日（含）止本月21日（不含）
      c. 配置时不要使用28以上的数字，避免大小月、闰月可能带来的问题
      **/
	'attendance_cycle_start_day' => 21,

	'attendance_result' => array(
		'1' => array(
			'en' => 'On duty',
			'cn' => '已出勤'
		),
		'2' => array(
			'en' => 'Absent',
			'cn' => '缺勤无替补'
		),
		'3' => array(
			'en' => 'Absent with substitution',
			'cn' => '缺勤有替补'
		),

	),
	'type' => array(
		'1' => array(
			'en' => 'In-house Staff',
			'cn' => '内部个人',
		),
		'2' => array(
			'en' => '3rd Party Company',
			'cn' => '外部公司',
		),
		'3' => array(
			'en' => 'Freelancer',
			'cn' => '外部个人',
		),
	),
	'job_type' => array(
		'1' => array(
			'en' => 'Lead Teacher',
			'cn' => '主班老师',
		),
		'2' => array(
			'en' => 'Teaching Assistant',
			'cn' => '助教',
		),
		'10' => array(
			'en' => 'Cordinator',
			'cn' => '协调员',
		),
		'11' => array(
			'en' => 'Assistant',
			'cn' => '行政人员',
		),
		'12' => array(
			'en' => 'Administrator',
			'cn' => '管理员',
		),
        '13' => array(
            'en' => 'School bus Ayi',
            'cn' => '校车老师',
        ),
		'20' => array(
			'en' => 'Security',
			'cn' => '保安',
		),
		'21' => array(
			'en' => 'Cleaner',
			'cn' => '保洁',
		),
		'30' => array(
			'en' => 'Temporary (not routine)',
			'cn' => '机动（非常规）',
		),
		'40' => array(
			'en' => '延时服务',
			'cn' => '延时服务',
		),
	),

	'unit_type' => array(
		'1' => array(
			'en' => 'per class',
			'cn' => '每节课',
		),
		'2' => array(
			'en' => 'per day',
			'cn' => '每日',
		),
		'3' => array(
			'en' => 'per month',
			'cn' => '每月',
		),
		'4' => array(
			'en' => 'per season',
			'cn' => '每期',
		),
		'99' => array(
			'en' => 'per student per class',
			'cn' => '每学生每节'
		)
	),
	'bank'  => array(
		'1' => array(
			'en' => 'ICBC',
			'cn' => '中国工商银行',
		),
		'2' => array(
			'en' => 'CCB',
			'cn' => '中国建设银行',
		),
		'3' => array(
			'en' => 'ABC',
			'cn' => '中国农业银行',
		),
        '4' => array(
            'en' => 'BOC',
            'cn' => '中国银行',
        ),
        '5' => array(
            'en' => 'CMBC',
            'cn' => '中国民生银行',
        ),
        '6' => array(
            'en' => 'CMB',
            'cn' => '中国招商银行',
        ),
        '7' => array(
            'en' => 'BOCOM',
            'cn' => '中国交通银行',
        ),
        '8' => array(
            'en' => 'ABC',
            'cn' => '中国邮政',
        ),
        '9' => array(
            'en' => 'CNCB',
            'cn' => '中信银行',
        ),
        '10' => array(
            'en' => 'Yin Zhou YingHang',
            'cn' => '鄞州银行',
        ),
        '11' => array(
            'en' => 'NongCun XinYongShe',
            'cn' => '农村信用社',
        ),
        '12' => array(
            'en' => 'NCBC',
            'cn' => '宁波银行',
        ),
        '13' => array(
            'en' => 'LINSHANG BANK',
            'cn' => '临沂商业银行',
        ),
		'14' => array(
            'en' => 'JIANGYINNONGCUNSHANGYING BANK',
            'cn' => '江阴农村商业银行',
        ),
		'15' => array(
			'en' => '双流诚民村镇银行',
			'cn' => '双流诚民村镇银行',
		),
		'16' => array(
			'en' => '兴业银行',
			'cn' => '兴业银行',
		),
        '17' => array(
            'en' => 'Bank of Shanghai',
            'cn' => '上海银行',
        ),
        '18' => array(
            'en' => 'BRCB',
            'cn' => '北京农村商业银行',
        ),
        '19' => array(
            'en' => 'PAB',
            'cn' => '平安银行',
        ),
	    '20' => array(
            'en' => '渤海银行',
            'cn' => '渤海银行',
        ),
        '21' => array(
            'en' => '上海浦发银行',
            'cn' => '上海浦发银行',
        ),
		'22' => array(
            'en' => '大连银行',
            'cn' => '大连银行',
        ),
		'23' => array(
            'en' => '广发银行',
            'cn' => '广发银行',
        ),
		'24' => array(
            'en' => '中国光大银行股份有限公司',
            'cn' => '中国光大银行股份有限公司',
        ),
		'25' => array(
            'en' => '浙商银行股份有限公司',
            'cn' => '浙商银行股份有限公司',
        ),
		'26' => array(
            'en' => '华夏银行股份有限公司',
            'cn' => '华夏银行股份有限公司',
        ),
		'27' => array(
            'en' => '西安银行',
            'cn' => '西安银行',
        ),
		'28' => array(
            'en' => '北京银行望京支行',
            'cn' => '北京银行望京支行',
        ),
		'29' => array(
            'en' => '北京银行',
            'cn' => '北京银行',
        ),
	),

	'reFundType' => array(
		'1' => array(
			'en' => '开课前全额退款',
			'cn' => '开课前全额退款',
		),
		'2' => array(
			'en' => '开课后部分退款（退班）',
			'cn' => '开课后部分退款（退班）',
		),
		'3' => array(
			'en' => '特殊：开课后部分退款（不退班）',
			'cn' => '特殊：开课后部分退款（不退班）',
		),		
		'4' => array(
			'en' => '换课账单自动退款进个人账户',
			'cn' => '换课账单自动退款进个人账户',
		),
		'5' => array(
			'en' => '换课账单差价退还家长',
			'cn' => '换课账单差价退还家长',
		),
		'10' => array(
			'en' => '延时课程退费',
			'cn' => '延时课程退费',
		),
	),
	'status' => array(
		'1' => array(
			'en' => '已提交',
			'cn' => '已提交',
		),
		'2' => array(
			'en' => '退回需补充材料',
			'cn' => '退回需补充材料',
		),
		'3' => array(
			'en' => '已确认',
			'cn' => '已确认',
		),
		'4' => array(
			'en' => '提交退款处理',
			'cn' => '提交退款处理',
		),
		'5' => array(
			'en' => '已退费',
			'cn' => '已退费',
		),
		'6' => array(
			'en' => '退费申请作废',
			'cn' => '退费申请作废',
		),
	),

	'refundReason' => array(
		'1' => array(
			'en' => '课程质量不佳',
			'cn' => '课程质量不佳',
		),
		'2' => array(
			'en' => '老师频繁更换',
			'cn' => '老师频繁更换',
		),
		'3' => array(
			'en' => '其它特殊原因',
			'cn' => '其它特殊原因',
		),
		'4' => array(
			'en' => '换课账单自动退款进个人账户',
			'cn' => '换课账单自动退款进个人账户',
		),
		'5' => array(
			'en' => '换课账单差价退还家长',
			'cn' => '换课账单差价退还家长',
		),
		'10' => array(
			'en' => '延时课程退费',
			'cn' => '延时课程退费',
		),
	),

	'refundMethod' => array(
		'1' => array(
			'en' => '微信',
			'cn' => '微信',
		),
		'2' => array(
			'en' => '银行卡',
			'cn' => '银行卡',
		),
		'3' => array(
			'en' => '个人账户',
			'cn' => '个人账户',
		),
	),
);
