<?php

class WeeklyController extends TeachBasedController
{

    public $selectedClassId=0;
    public $selectedWeeknum=0;
    public $selectedTask='';
    public $selectedGradeTask='';
    public $calendarModel=null;
    public $showHelp=false;
    public $printFW = "";

    public $demoFlag = '-demo-'; //必须与通知服务器一致

    // 访问action的初级权限
    public $actionAccessAuths = array(
        'index'             => 'o_T_Access',
        'GetLinks'          => 'o_T_Access',
        'ChildPhotos'       => 'o_T_Access',
        'ChildNote'         => 'o_T_Access',
        'SpecialNotes'      => 'o_T_Access',
        'DelSpecialPhoto'   => 'o_T_Adm_Common',
        'SetAll'            => 'o_T_Adm_Common',
        'SetAllDs'            => 'o_T_Adm_Common',
    );

    public function init(){
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'teaching';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site','Teaching Tasks');

        //初始化选择校园页面
        $this->branchSelectParams['hideOffice'] = true;
        $this->branchSelectParams['urlArray'] = array('//mteaching/weekly/index');


        Yii::import('common.models.portfolio.*');
    }

	public function actionIndex($classid=0, $weeknum=0, $task='')
	{
        parent::initExt();
        list( $this->selectedClassId, $this->selectedWeeknum, $this->selectedTask )
            = array( $classid, $weeknum, $task);
        Yii::import('common.models.calendar.Calendar');
        $this->calendarModel = Calendar::model()->findByPk($this->calendarId);

        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile($cs->getCoreScriptUrl().'/jui/js/jquery-ui-i18n.min.js');

        //该校园或班级不支持此功能
        $stopped = array(
            Branch::PROGRAM_DAYSTAR => array(
                //2-6岁，幼儿园
                'A' => array(
//                    'specialnotes','schedule','videoupload',
                ),
                //7-12岁，小学
                'B'=> array(
                    'specialnotes','schedule',
                )
            )
        );

        //校园支持本功能
        $stopping = false;
        $_ageType = ( $this->schoolClasses['ages'][$this->selectedClassId] < 7 ) ? 'A' : 'B';
        if(isset($stopped[$this->branchObj->group])){
            if( isset( $stopped[$this->branchObj->group][$_ageType] ) ){
                if(in_array($task, $stopped[$this->branchObj->type][$_ageType] )){
                    //校园不支持本功能
                    $stopping = true;
                }
            }
        }
        $taskData = array();
        $taskData['stopping'] = $stopping;
        if(!$stopping){
            if($classid && $weeknum && $task){
                $taskfun = 'task'.ucfirst($task);
                if ($taskfun === 'taskClassnews' && $this->schoolClasses['ages'][$this->selectedClassId] > 4 && Yii::app()->params['siteFlag'] == 'daystar'){
                    $this->selectedGradeTask = 'grade'.$task;
                    $taskfun = 'taskGrade'.ucfirst($task);
                }
                $taskData = $this->$taskfun();
            }
        }
        $this->render('index', array(
            'taskData'=>$taskData,
            'classid'=> $this->selectedClassId,
            'weeknum'=> $this->selectedWeeknum,
        ));

	}

    public function taskMediaupload()
    {
        return $this->processUpload('photo');
    }

    public function taskVideoupload()
    {
        return $this->processUpload('video');
    }

    public function processUpload($uploadType){
        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/plupload/plupload.full.min.js');

        $upOpts = $this->getQiniuOpt($uploadType);
        $upOpts['uploadType'] = $uploadType;

        return $upOpts;
    }

    public function taskMediamgt()
    {
        $this->showHelp = true;
        if(Yii::app()->request->isPostRequest && Yii::app()->request->isAjaxRequest){

            if(!$this->checkTeachers() || !Yii::app()->user->checkAccess('o_T_Adm_Common')){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', 'Unauthorized operation, please contact IT Dept.');
                $this->showMessage();
            }

            Yii::import('common.models.portfolio.*');
            ChildMedia::setStartYear($this->calendarModel->startyear);
            ChildMediaLinks::setStartYear($this->calendarModel->startyear);

            $childid = Yii::app()->request->getParam('childid', 0);
            if($childid){

                $mediaid = Yii::app()->request->getParam('mediaid', array());
                $delphoto = Yii::app()->request->getParam('delphoto', array());
                $caption = Yii::app()->request->getParam('caption', array());
                $forceCaption = Yii::app()->request->getParam('forceCaption', array());
                $weight = Yii::app()->request->getParam('weight', array());

                $imgCount = 0;
                $aqlCount = 0;
                $totCount = 0;

                if($delphoto){
                    $criteria = new CDbCriteria();
                    $criteria->compare('category', 'week');
                    $criteria->compare('classid', $this->selectedClassId);
                    $criteria->compare('childid', $childid);
                    $criteria->compare('yid', $this->calendarId);
                    $criteria->compare('weeknum', $this->selectedWeeknum);
                    $criteria->compare('pid', $delphoto);
                    ChildMediaLinks::model()->deleteAll($criteria);
                }

                if($mediaid){
                    $criteria = new CDbCriteria();
                    $criteria->compare('category', 'week');
                    $criteria->compare('classid', $this->selectedClassId);
                    $criteria->compare('childid', array(0, $childid));
                    $criteria->compare('yid', $this->calendarId);
                    $criteria->compare('weeknum', $this->selectedWeeknum);
                    $criteria->compare('pid', $mediaid);
                    $items = ChildMediaLinks::model()->findAll($criteria);
                    foreach($items as $item){
                        $item->content = isset($caption[$item->pid]) ? $caption[$item->pid] : '';
                        $item->weight = isset($weight[$item->pid]) ? $weight[$item->pid] : 0;
                        $item->updated_timestamp = time();
//                        if($item->childid==0 && empty($item->content)){
//                            $this->addMessage('state', 'fail');
//                            $this->addMessage(Yii::t('teaching', 'Please input comment for class photo'));
//                            $this->showMessage();
//                        }
                        $item->save();

                        if($item->content){
                            if($item->childid){
                                $aqlCount++;
                            }
                            else{
                                $totCount++;
                            }
                        }
                        $imgCount++;
                    }
                }

                if($forceCaption){
                    $criteria = new CDbCriteria();
                    $criteria->compare('category', 'week');
                    $criteria->compare('classid', $this->selectedClassId);
                    $criteria->compare('yid', $this->calendarId);
                    $criteria->compare('weeknum', $this->selectedWeeknum);
                    $criteria->compare('pid', $forceCaption);
                    $items = ChildMediaLinks::model()->findAll($criteria);
                    foreach($items as $item){
                        $item->content = isset($caption[$item->pid]) ? $caption[$item->pid] : '';
                        $item->updated_timestamp = time();
                        $item->save();
                    }
                }



                $criteria = new CDbCriteria();
                $criteria->compare('classid', $this->selectedClassId);
                $criteria->compare('childid', $childid);
                $criteria->compare('yid', $this->calendarId);
                $criteria->compare('weeknumber', $this->selectedWeeknum);
                $model = NotesChild::model()->find($criteria);
                if($model === null)
                    $model = new NotesChild();
                if(isset($_POST['stat'])){
                    $model->stat = 20;
                }
                else{
                    $model->stat = 10;
                }
                $model->classid = $this->selectedClassId;
                $model->childid = $childid;
                $model->yid = $this->calendarId;
                $model->weeknumber = $this->selectedWeeknum;
                $model->updated_timestamp = time();
                $model->userid = Yii::app()->user->id;
                $model->invalid = $imgCount > 2 && ($aqlCount+$totCount) > 2 ? 0 : 1;
                $model->startyear = $this->calendarModel->startyear;
                if(!$model->pids){
                    $randMed = count($mediaid)>3 ? array_rand($mediaid, 4) : $mediaid;
                    $model->pids = implode(',', $randMed);
                }
                $model->save();

                Yii::log(sprintf('%s,c%d,w%d,ch%d,u%d',
                        $this->branchId,
                        $this->selectedClassId,
                        $this->selectedWeeknum,
                        $childid,
                        Yii::app()->user->id
                    ),
                    CLogger::LEVEL_INFO, 'teaching.mediamgt');

                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message','Data saved!'));
                $this->addMessage('data', array('childid'=>$childid, 'delphoto'=>count($delphoto), 'aqlCount'=>$aqlCount, 'totCount'=>$totCount, 'stat'=>$model->stat));
                $this->addMessage('callback', 'callback1');
                $this->showMessage();
            }
            $mediaid = Yii::app()->request->getParam('mediaid', 0);
            if($mediaid){
                $linkChildren   = Yii::app()->request->getParam('LinkChild', array());
                $tagChildren    = Yii::app()->request->getParam('TagChild', array());
                $tagLd          = Yii::app()->request->getParam('TagLD', array());
                $tagMonth       = Yii::app()->request->getParam('TagMonth', array());
                $tagFree        = Yii::app()->request->getParam('TagFree', '');
                $allchild       = Yii::app()->request->getParam('allchild', 0);

                $model = ChildMedia::model()->findByPk($mediaid);
                if($model->schoolid == $this->branchId){
                    $tag = '';
                    if($tagChildren){
                        foreach($tagChildren as $tagChild){
                            $tag .= 'sc'.$this->selectedClassId.'_'.$tagChild.'_e ';
                        }
                    }
                    if($tagLd){
                        foreach($tagLd as $tagl){
                            $tag .= 'sl_'.$tagl.'_e ';
                        }
                    }
                    if($tagMonth){
                        foreach($tagMonth as $tagm){
                            $tag .= 'sm_'.$tagm.'_e ';
                        }
                    }
                    if($tagFree){
                        $tag .= 'sf_'.$tagFree.'_e ';
                    }

                    $cChild = array();
                    $oldKeys = array();
                    $_allchild = 0;

                    $criteria = new CDbCriteria();
                    $criteria->compare('pid', $mediaid);
                    $criteria->compare('weeknum', $this->selectedWeeknum);
                    $criteria->compare('classid', $this->selectedClassId);
                    $criteria->compare('category', 'week');
                    $criteria->compare('childid', '<>0');
                    $modelLinks = ChildMediaLinks::model()->findAll($criteria);
                    foreach($modelLinks as $link){
                        $oldKeys[$link->childid] = $link->childid;
                    }

                    if($allchild){
                        ChildMediaLinks::model()->deleteAllByAttributes(array(
                            'pid'=>$mediaid,
                            'weeknum'=>$this->selectedWeeknum,
                            'classid'=>$this->selectedClassId,
                            'category'=>'week',
                        ));
                        $modelLink = new ChildMediaLinks;
                        $modelLink->category = 'week';
                        $modelLink->classid = $this->selectedClassId;
                        $modelLink->childid = 0;
                        $modelLink->itemid = 0;
                        $modelLink->pid = $mediaid;
                        $modelLink->yid = $this->calendarId;
                        $modelLink->weeknum = $this->selectedWeeknum;
                        $modelLink->updated_timestamp = time();
                        $modelLink->userid = Yii::app()->user->id;
                        $modelLink->save();
                    }
                    else{
                        $criteria = new CDbCriteria();
                        $criteria->compare('pid', $mediaid);
                        $criteria->compare('weeknum', $this->selectedWeeknum);
                        $criteria->compare('classid', $this->selectedClassId);
                        $criteria->compare('category', 'week');
                        $criteria->compare('childid', 0);
                        if(ChildMediaLinks::model()->count($criteria)){
                            ChildMediaLinks::model()->deleteAll($criteria);
                            $_allchild = 1;
                        }

                        foreach($linkChildren as $cKey){
                            if(in_array($cKey, $oldKeys)){
                                unset($oldKeys[$cKey]);
                            }
                            else{
                                $modelLink = new ChildMediaLinks;
                                $modelLink->category = 'week';
                                $modelLink->classid = $this->selectedClassId;
                                $modelLink->childid = $cKey;
                                $modelLink->itemid = 0;
                                $modelLink->pid = $mediaid;
                                $modelLink->yid = $this->calendarId;
                                $modelLink->weeknum = $this->selectedWeeknum;
                                $modelLink->updated_timestamp = time();
                                $modelLink->userid = Yii::app()->user->id;
                                $modelLink->save();
                                $cChild[$cKey]=$cKey;
                            }
                        }
                    }
                    if($oldKeys){
                        $criteria = new CDbCriteria();
                        $criteria->compare('pid', $mediaid);
                        $criteria->compare('category', 'week');
                        $criteria->compare('weeknum', $this->selectedWeeknum);
                        $criteria->compare('classid', $this->selectedClassId);
                        $criteria->compare('childid', $oldKeys);
                        ChildMediaLinks::model()->deleteAll($criteria);
                    }

                    $model->tag = substr($tag, 0, -1);
                    if($model->save()){
                        $this->addMessage('state', 'success');
                        $this->addMessage('message', Yii::t('message','Data saved!'));
                        $this->addMessage('data', array('id'=>$mediaid, 'tag'=>$model->tag, 'allchild'=>$allchild, 'cChild'=>$cChild, 'oldKeys'=>$oldKeys, '_allchild'=>$_allchild));
                        $this->addMessage('callback', 'callback');
                    }
                    else{
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', Yii::t('message','Saving Failed!'));
                    }
                }
                else{
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', Yii::t('message','Saving Failed!'));
                }
                $this->showMessage();
            }
        }
        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/underscore-min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/backbone-min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/johndyer/mediaelement-and-player.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/cropper/cropper.min.js');
        $cs->registerCssFile(Yii::app()->themeManager->baseUrl.'/base/js/cropper/cropper.min.css');
        $cs->registerCssFile(Yii::app()->themeManager->baseUrl.'/base/js/johndyer/mediaelementplayer.min.css');

        $taskData = array();
        $params = array(
            'startYear' => intval($this->calendarModel->startyear),
            'branchId' => $this->branchId,
            'weekNumber' => intval($this->selectedWeeknum),
            'classId' => intval($this->selectedClassId),
            'time' => time(),
        );
        ksort($params);
        $taskData['params'] = $params;

        $cond = 'category=:category and classid=:classid and weeknum=:weeknum and yid=:yid';
        $condParams = array(
            ':category' => 'week',
            ':classid'  => $this->selectedClassId,
            ':weeknum'  => $this->selectedWeeknum,
            ':yid'      => $this->calendarId,
        );
        $countRows = Yii::app()->db->createCommand()
            ->select( 'childid,count(*) as total' )
            ->from('ivy_child_media_links_'.$params['startYear'])
            ->where($cond, $condParams )
            ->group('childid')
            ->queryAll();
        $childCount = array();
        foreach($countRows as $row){
            $childCount[$row['childid']] = $row['total'];
        }

        $cond = "category=:category and classid=:classid and weeknum=:weeknum and yid=:yid and content != ''";
        $condParams = array(
            ':category' => 'week',
            ':classid'  => $this->selectedClassId,
            ':weeknum'  => $this->selectedWeeknum,
            ':yid'      => $this->calendarId,
        );
        $_countRows = Yii::app()->db->createCommand()
            ->select( 'childid,count(*) as total' )
            ->from('ivy_child_media_links_'.$params['startYear'])
            ->where($cond, $condParams )
            ->group('childid')
            ->queryAll();
        $_childCount = array();
        foreach($_countRows as $row){
            $_childCount[$row['childid']] = $row['total'];
        }

        $cond = "classid=:classid and weeknumber=:weeknum and stat=20";
        $condParams = array(
            ':classid'  => $this->selectedClassId,
            ':weeknum'  => $this->selectedWeeknum,
        );
        $rows = Yii::app()->db->createCommand()
            ->select( 'childid' )
            ->from('ivy_notes_child')
            ->where($cond, $condParams )
            ->queryAll();
        $onlines = array();
        foreach($rows as $row){
            $onlines[] = $row['childid'];
        }

        $taskData['onlines'] = $onlines;
        $taskData['childCount'] = $childCount;
        $taskData['_childCount'] = $_childCount;
        $taskData['sConfs'] = $this->getServerConf($this->selectedClassId);
        $seCalendar = explode(',', $this->calendarModel->timepoints);
        $sMonth = intval(date('n', $seCalendar[0]));
        $eMonth = intval(date('n', $seCalendar[3]));
        $monthArr = array();
        for($i=$sMonth; $i<=12+$eMonth; $i++){
            $monthArr[]= ($i%12) ? $i%12 : 12;
        }
        $taskData['months'] = $monthArr;

        return $taskData;
    }


    public function getQiniuOpt($fileType='photo'){
        $sConfs = $this->getServerConf($this->selectedClassId);
        $expiration = 10800; //10800s = 3h
        $reExpiration = ($expiration - 1800) * 1000; //1800s = .5h

        require_once( Yii::getPathOfAlias('common.components.qiniu.rs') . '.php' );
        require_once( Yii::getPathOfAlias('common.components.qiniu.io') . '.php' );
        Qiniu_SetKeys($QINIU_ACCESS_KEY, $QINIU_SECRET_KEY);

        $putPolicy = new Qiniu_RS_PutPolicy($sConfs['bucket']);

        $putPolicy->ReturnUrl = null; //为空表示不跳转
        //$putPolicy->ReturnBody = '{"key": $(key), "hash": $(etag), "w": $(imageInfo.width), "h": $(imageInfo.height)}';

        $sandbox = ( !OA::isProduction() || in_array(Yii::app()->user->id, Yii::app()->params['mediaTestingUids']) ) ? 1: 0;

        if($sandbox)
            $putPolicy->SaveKey = '$(x:s)/$(x:t)'.$this->demoFlag.'/$(x:c)/$(x:w)/$(etag)';
        else
            $putPolicy->SaveKey = '$(x:s)/$(x:t)/$(x:c)/$(x:w)/$(etag)';

        $putPolicy->Expires = 10800;
        switch($fileType){
            case 'photo':
            default:
//                $putPolicy->MimeLimit = 'image/jpeg;image/png';
//                $putPolicy->SaveKey = '$(x:s)/$(x:t)/$(x:c)/$(x:w)/$(etag)';
                $putPolicy->CallbackUrl = $sConfs['callBackUrl'];
                $putPolicy->CallbackBody = 'key=$(key)&hash=$(etag)&pw=$(imageInfo.width)&ph=$(imageInfo.height)'.
                    '&s=$(x:s)&t=$(x:t)&c=$(x:c)&y=$(x:y)&w=$(x:w)&p=$(x:p)&svr=$(x:svr)&tst=$(x:tst)&u=$(x:u)'.
                    '&filetype=$(x:filetype)';
                break;
            case 'video':
                $putPolicy->MimeLimit = 'video/*';
//                $putPolicy->SaveKey = '$(x:s)/$(x:t)/$(x:c)/$(x:w)/$(etag)';
                $putPolicy->CallbackUrl = $sConfs['callBackUrl'];
                $putPolicy->CallbackBody = 'key=$(key)&hash=$(etag)'.
                    '&s=$(x:s)&t=$(x:t)&c=$(x:c)&y=$(x:y)&w=$(x:w)&p=$(x:p)&svr=$(x:svr)&tst=$(x:tst)&u=$(x:u)'.
                    '&filetype=$(x:filetype)';
                if (Yii::app()->params['siteFlag'] == 'daystar') {
                    $putPolicy->PersistentOps = 'vframe/jpg/offset/3;avthumb/mp4/r/24/vcodec/libx264/s/1280x720/autoscale/1/avsmart/1/wmImage/aHR0cHM6Ly9tMi5maWxlcy5pdnlraWRzLmNuL3dtL2RheXN0YXIucG5n/wmGravity/NorthEast';
                }
                else {
                    $putPolicy->PersistentOps = 'vframe/jpg/offset/3;avthumb/mp4/r/24/vcodec/libx264/s/1280x720/autoscale/1/avsmart/1/wmImage/aHR0cHM6Ly9tMi5maWxlcy5pdnlraWRzLmNuL3dtL2l2eS5wbmc=/wmGravity/NorthEast';
                }
                $putPolicy->PersistentNotifyUrl = $sConfs['persistentsUrl'];
                $putPolicy->PersistentPipeline = $sConfs['pipeline'];
                $putPolicy->ReturnBody = '{"key": $(key), "hash": $(etag)}';

                break;
        }

        $upToken = $putPolicy->Token(null);

        //自定义变量
        $xParams = array(
            'x:s'=>$this->branchId,
            'x:t'=>$this->calendarModel->startyear,
            'x:c'=>$this->selectedClassId,
            'x:y'=>$this->calendarId,
            'x:w'=>$this->selectedWeeknum,
            'x:p'=>OA::isProduction() ? 1 : 0, # 是不是生产系统
            'x:svr'=>$sConfs['id'], # 图片服务器ID
            'x:tst'=>in_array(Yii::app()->user->id, Yii::app()->params['mediaTestingUids']) ? 1 : 0, # 是否测试 根据 mediaTestingUids 配置判断
            'x:u'=>Yii::app()->user->id,
            'x:filetype'=>$fileType
        );

        return array('upToken'=>$upToken, 'sConfs'=>$sConfs, 'reExpiration'=>$reExpiration, 'xParams'=>$xParams);

    }

    public function actionQiniuOpt($fileType='photo', $classid=0, $weeknum=0)
    {
        $this->selectedClassId = $classid;
        $this->selectedWeeknum = $weeknum;
        $this->calendarId = IvyClass::model()->findByPk($this->selectedClassId)->yid;
        Yii::import('common.models.calendar.Calendar');
        $this->calendarModel = Calendar::model()->findByPk($this->calendarId);
        echo CJSON::encode($this->getQiniuOpt($fileType));
    }

    public function taskSchedule()
    {
        Yii::import('common.models.portfolio.*');
        Yii::import('common.models.schedule.*');

        if(Yii::app()->request->isPostRequest){
            $this->saveSchedule();
        }

        $class_model = IvyClass::model()->findByPk($this->selectedClassId);

        $sArr = explode('|', $class_model->periodtime);

        $classTime['start'] = strtotime($sArr[0].':'.$sArr[1]);
        $classTime['end'] = strtotime($sArr[2].':'.$sArr[3]);

        $pids = array(8=>8); //日常活动
        $criteria = new CDbCriteria();
        $criteria->compare('userid', Yii::app()->user->id);
        $items=CurFavorites::model()->findAll($criteria);
        foreach($items as $item){
            $pids[$item->pid]=$item->pid;
        }
        $items=CurActiveProject::model()->find('branch_id=:schoolid', array(':schoolid'=>$this->branchId));
        foreach(unserialize($items->projects) as $item){
            $pids[$item]=$item;
        }

        $criteria = new CDbCriteria();
        $criteria->compare('t.pid', $pids);
        $criteria->compare('t.userid', Yii::app()->user->id, false, 'or');
        $items=CurProjects::model()->with('activity')->findAll($criteria);
        $projects = array();
        $activityTitle = array();
        foreach($items as $item){
            foreach($item->activity as $activity){
                $projects[CommonUtils::autoLang($item->cn_title, $item->en_title)][$activity->aid]
                    = CommonUtils::autoLang($activity->cn_title, $activity->en_title);
                $activityTitle[$activity->aid] = array(
                    'title'=>CommonUtils::autoLang($activity->cn_title, $activity->en_title),
                    'memo'=>CommonUtils::autoLang($activity->cn_memo, $activity->en_memo),
                );
            }
        }
        Yii::app()->clientScript->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/underscore-min.js');
        Yii::app()->clientScript->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/backbone-min.js');
        Yii::app()->clientScript->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/phpfunc.js');

        Yii::app()->clientScript->registerCssFile(Yii::app()->theme->baseUrl.'/css/schedule.css');

        $crit = new CDbCriteria;
        $crit->compare('classid', $this->selectedClassId);
        $crit->compare('weeknumber', $this->selectedWeeknum);
        $schedule = ClassScheduleV2::model()->with('data')->find($crit);
        $crit = new CDbCriteria;
        $crit->compare('userid', Yii::app()->user->getId());
        $crit->index = 'scheduleid';
        $userTemplates = ScheduleTemplate::model()->with('data')->findAll( $crit );

        return array('userTemplates'=>$userTemplates, 'activityTitle'=>CJSON::encode($activityTitle), 'projects'=>$projects, 'classTime'=>$classTime,'schedule'=>$schedule);
    }

    public function saveSchedule(){
        extract($_POST);

        $crit = new CDbCriteria;
        $crit->compare('classid', $this->selectedClassId);
        $crit->compare('weeknumber', $this->selectedWeeknum);
        $schedule = ClassScheduleV2::model()->with('data')->find($crit);
        if( is_null($schedule) ){
            $schedule = new ClassScheduleV2;
            $schedule->classid = $this->selectedClassId;
            $schedule->yid = $this->calendarId;
            $schedule->weeknumber = $this->selectedWeeknum;
        }
        if( is_null($schedule->data) ){
            $schedule->data = new ClassScheduleData;
        }
        $schedule->data->data = base64_encode($scheduleData);
        $acts = explode(',',$activityIds);
        $acts = array_unique($acts);
        $schedule->activitys = implode(',', $acts);
        $schedule->timestamp = time();
        $schedule->uid = Yii::app()->user->getId();

        if($schedule->save()){
            if(!$schedule->data->id){
                $schedule->data->id = $schedule->id;
            }

            if( $schedule->data->save() ){
                $scheduleTemplate = ScheduleTemplate::model()->findByAttributes(array('userid'=>Yii::app()->user->getId(), 'scheduleid'=>$schedule->id));
                $data = array();
                if($setTemplate){
                    if(is_null($scheduleTemplate)){
                        $scheduleTemplate = new ScheduleTemplate;
                        $scheduleTemplate->userid = Yii::app()->user->getId();
                        $scheduleTemplate->scheduleid = $schedule->id;
                        $scheduleTemplate->tempname = sprintf('%d-%d Week %d', $this->calendarModel->startyear, $this->calendarModel->startyear+1, $this->selectedWeeknum);
                        $scheduleTemplate->save();
                        $data['addTpl'] = $schedule->id; //增加个人的课表模版
                        $data['tplName'] = $scheduleTemplate->tempname; //增加个人的课表模版
                        $data['tplData'] = base64_decode($schedule->data->data); //增加个人的课表模版
                    }
                }else{
                    if(!is_null($scheduleTemplate)){
                        $data['removeTpl'] = $schedule->id; //删除个人的课表模版
                        $scheduleTemplate->delete();
                    }
                }

                Yii::log(sprintf('%s,c%d,w%d,u%d',
                        $this->branchId,
                        $this->selectedClassId,
                        $this->selectedWeeknum,
                        Yii::app()->user->id
                    ),
                    CLogger::LEVEL_INFO, 'teaching.schedule');

                $this->addMessage('state', 'success');
                $this->addMessage('data', $data);
                $this->showMessage();
            }else{
                $this->addMessage('state', 'fail');
                $this->addMessage('message', $schedule->data->getErrors());
                $this->showMessage();
            }
        }else{
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $schedule->getErrors());
            $this->showMessage();
        }

    }

    public function actionExport()
    {
        Yii::import('common.models.schedule.*');
        Yii::app()->clientScript->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/underscore-min.js');
        Yii::app()->clientScript->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/backbone-min.js');
        Yii::app()->clientScript->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/phpfunc.js');

        Yii::app()->clientScript->registerCssFile(Yii::app()->theme->baseUrl.'/css/schedule.css');

        $classid = Yii::app()->request->getParam('classid', '');
        $weeknum = Yii::app()->request->getParam('weeknum', '');

        $class_model = IvyClass::model()->findByPk($classid);

        $sArr = explode('|', $class_model->periodtime);
        $classTimestart = strtotime($sArr[0].':'.$sArr[1]);

        $this->layout = '//layouts/print';
        $this->printFW = $this->branchObj->getPrintHeader();
        $crit = new CDbCriteria;
        $crit->compare('classid', $classid);
        $crit->compare('weeknumber', $weeknum);
        $schedule = ClassScheduleV2::model()->with('data')->find($crit);

        $crits = new CDbCriteria;
        $crits->compare('yid', $schedule->yid);
        $crits->compare('weeknumber', $weeknum);
        $calendarModel = CalendarWeek::model()->find($crits);

        $this->render('export', array(
            'classTimestart' => $classTimestart,
            'schedule' => $schedule,
            'class_model' => $class_model,
            'weeknum' => $weeknum,
            'calendarModel' => $calendarModel,
        ));
    }

    public function taskSchoolnews()
    {
        $type = (CommonUtils::isGradeSchool($this->selectedClassId)) ? NotesSchCla::NOTESSCHCLA_TYPE_GRADE_SCHOOLNEW : NotesSchCla::NOTESSCHCLA_TYPE_SCHOOLNEW;
        if ($this->branchId=='BJ_DS' && CommonUtils::isMiddleSchool($this->selectedClassId)) {
            $type = NotesSchCla::NOTESSCHCLA_TYPE_MIDDLE_SCHOOLNEW;
        }
        $criteria = new CDbCriteria();
        $criteria->compare('schoolid', $this->branchId);
        $criteria->compare('yid', $this->calendarId);
        $criteria->compare('weeknumber', $this->selectedWeeknum);
        $criteria->compare('type', $type);
        $model = NotesSchCla::model()->find($criteria);
        if($model === null)
            $model = new NotesSchCla();

        if(isset($_POST['NotesSchCla'])){
            if(Yii::app()->user->checkAccess('ivystaff_opschool') || Yii::app()->user->id == 8015223){
                $model->attributes=$_POST['NotesSchCla'];
                $model->schoolid = $this->branchId;
                $model->classid = 0;
                $model->yid = $this->calendarId;
                $model->type = $type;
                $model->weeknumber = $this->selectedWeeknum;
                $model->stat = isset($_POST['NotesSchCla']['stat']) ? $_POST['NotesSchCla']['stat'] : 10;
                $model->updated_timestamp = time();
                $model->userid = Yii::app()->user->id;
                $model->cd_uids = isset($_POST['NotesSchCla']['cd_uids']) ? implode(',', $_POST['NotesSchCla']['cd_uids']) : '';
                $model->operate_uid = $model->userid;
                if($model->save()){
                    $this->addMessage('state', 'success');
                    $this->addMessage('message', Yii::t('message','Data saved!'));

                    Yii::log(sprintf('%s,c%d,w%d,u%d',
                            $this->branchId,
                            $this->selectedClassId,
                            $this->selectedWeeknum,
                            Yii::app()->user->id
                        ),
                        CLogger::LEVEL_INFO, 'teaching.schoolnews');
                }
                else{
                    $this->addMessage('state', 'fail');
                    $errs = current($model->getErrors());
                    $this->addMessage('message', $errs?$errs[0]:Yii::t('message','Saving Failed!'));
                }
            }
            else{
                $this->addMessage('state', 'fail');
                $this->addMessage('message', 'Unauthorized operation, please contact IT Dept.');
            }
            $this->showMessage();
        }

        $cdids = array(54, 138, 147, 207, 182, 199, 151, 152, 154, 226, 251, 252, 340, 457, 349); # 园长 副园长 行政园长 教学园长 启明星园长、小学副校长 校长 小学副校长&PYP协调员等
        $criteria = new CDbCriteria();
        $criteria->compare('t.level', 1);
        $criteria->compare('profile.occupation_en', $cdids);
        $criteria->compare('profile.branch', $this->branchId);
        $criteria->order='profile.occupation_en DESC';
        $cdItems = User::model()->with('profile')->findAll($criteria);
        $cduids = array();
        foreach($cdItems as $cd){
            $cduids[$cd->uid] = $cd->uid;
        }

        $criteria = new CDbCriteria();
        $criteria->compare('type', AdmBranchLink::ADM_TYPE_CD);
        $criteria->compare('schoolid', $this->branchId);
        $admUsers = AdmBranchLink::model()->findAll($criteria);
        foreach($admUsers as $admuser){
            $cduids[$admuser->uid] = $admuser->uid;
        }

        if($model->cd_uids){
            $model->cd_uids = explode(',', $model->cd_uids);
            foreach($model->cd_uids as $uid){
                $cduids[$uid] = $uid;
            }
        }
        $cds = array();
        $criteria = new CDbCriteria();
        $criteria->compare('uid', $cduids);
        $criteria->index = 'uid';
        $items = User::model()->findAll($criteria);
        foreach($cduids as $uid){
            $cds[$uid]=$items[$uid]->getName();
        }

        return array('model'=>$model, 'cds'=>$cds);
    }

    public function taskClassnews()
    {
        ChildMedia::setStartYear($this->calendarModel->startyear);
        ChildMediaLinks::setStartYear($this->calendarModel->startyear);

        $criteria = new CDbCriteria();
        $criteria->compare('classid', $this->selectedClassId);
        $criteria->compare('yid', $this->calendarId);
        $criteria->compare('weeknumber', $this->selectedWeeknum);
        $criteria->compare('type', 20);
        $model = NotesSchCla::model()->find($criteria);
        if($model === null)
            $model = new NotesSchCla();
        if(isset($_POST['NotesSchCla'])){
            if(!$this->checkTeachers() || !Yii::app()->user->checkAccess('o_T_Adm_Common')){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', 'Unauthorized operation, please contact IT Dept.');
                $this->showMessage();
            }
            $model->attributes=$_POST['NotesSchCla'];
            $model->schoolid = $this->branchId;
            $model->classid = $this->selectedClassId;
            $model->yid = $this->calendarId;
            $model->type = 20;
            $model->weeknumber = $this->selectedWeeknum;
            $model->stat = isset($_POST['NotesSchCla']['stat']) ? $_POST['NotesSchCla']['stat'] : 10;
            $model->updated_timestamp = time();
            $model->userid = Yii::app()->user->id;
            $model->cd_uids = $model->userid;
            $model->operate_uid = $model->userid;
            if($model->save()){
                $pids = Yii::app()->request->getParam('pid', array());
                if($pids){
                    foreach($pids as $key=>$pid){
                        $criteria = new CDbCriteria();
                        $criteria->compare('category', 'classreport');
                        $criteria->compare('classid', $this->selectedClassId);
                        $criteria->compare('childid', 0);
                        $criteria->compare('yid', $this->calendarId);
                        $criteria->compare('weeknum', $this->selectedWeeknum);
                        $criteria->compare('pid', $pid);
                        $mediaLink=ChildMediaLinks::model()->find($criteria);
                        if($mediaLink === null)
                            $mediaLink = new ChildMediaLinks();
                        $mediaLink->category = 'classreport';
                        $mediaLink->classid = $this->selectedClassId;
                        $mediaLink->childid = 0;
                        $mediaLink->yid = $this->calendarId;
                        $mediaLink->weeknum = $this->selectedWeeknum;
                        $mediaLink->pid = $pid;
                        $mediaLink->content = isset($_POST['caption'][$key]) ? $_POST['caption'][$key] : '';
                        $mediaLink->weight = isset($_POST['weight'][$key]) ? $_POST['weight'][$key] : 0;
                        $mediaLink->updated_timestamp = time();
                        $mediaLink->userid = Yii::app()->user->id;
                        $mediaLink->save();
                    }
                }

                Yii::log(sprintf('%s,c%d,w%d,u%d',
                        $this->branchId,
                        $this->selectedClassId,
                        $this->selectedWeeknum,
                        Yii::app()->user->id
                    ),
                    CLogger::LEVEL_INFO, 'teaching.classnews');


                $this->addMessage('state', 'success');
                $this->addMessage('callback', 'callback1');
                $this->addMessage('message', Yii::t('message','Data saved!'));
            }
            else{
                $this->addMessage('state', 'fail');
                $errs = current($model->getErrors());
                $this->addMessage('message', $errs?$errs[0]:Yii::t('message','Saving Failed!'));
            }
            $this->showMessage();
        }

        $criteria = new CDbCriteria();
        $criteria->compare('t.category', 'classreport');
        $criteria->compare('t.classid', $this->selectedClassId);
        $criteria->compare('t.weeknum', $this->selectedWeeknum);
        $criteria->compare('t.yid', $this->calendarId);
        $criteria->compare('t.childid', 0);
        $criteria->order = 't.weight';
        $mediaLinks = ChildMediaLinks::model()->with('photoInfo')->findAll($criteria);
        $photoData = array();
        foreach($mediaLinks as $item){
            $photoData[]=array(
                'id' => $item->pid,
                'url'=> $item->photoInfo->getMediaUrl(true),
                'val'=> $item->content,
                'weight'=>$item->weight,
                'isNew'=>0,
            );
        }

        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/underscore-min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/backbone-min.js');

        return array('model'=>$model, 'photoData'=>$photoData);
    }
    
    public function taskGradeClassnews(){
        ChildMedia::setStartYear($this->calendarModel->startyear);
        ChildMediaLinks::setStartYear($this->calendarModel->startyear);
        $notesId = Yii::app()->request->getParam('notesId',0);
        $criteria = new CDbCriteria();
        $criteria->compare('classid', $this->selectedClassId);
        $criteria->compare('yid', $this->calendarId);
        $criteria->compare('weeknumber', $this->selectedWeeknum);
        $criteria->compare('type', NotesSchCla::NOTESSCHCLA_TYPE_CLASSNEW);
        $classNewsList = NotesSchCla::model()->findAll($criteria);
        if ($notesId){
            $model = NotesSchCla::model()->findByPk($notesId);
        }else{
            $model = new NotesSchCla();
        }
        if(isset($_POST['NotesSchCla'])){
            if(!$this->checkTeachers() || !Yii::app()->user->checkAccess('o_T_Adm_Common')){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', 'Unauthorized operation, please contact IT Dept.');
                $this->showMessage();
            }
            $model->attributes = $_POST['NotesSchCla'];
            $model->schoolid = $this->branchId;
            $model->classid = $this->selectedClassId;
            $model->yid = $this->calendarId;
            $model->type = NotesSchCla::NOTESSCHCLA_TYPE_CLASSNEW;
            $model->weeknumber = $this->selectedWeeknum;
            $model->stat = ($model->stat) ? $model->stat : 10;
            $model->updated_timestamp = time();
            $model->userid = Yii::app()->user->id;
            $model->cd_uids = $model->userid;
            $model->operate_uid = $model->userid;
            if($model->save()){
                Yii::log(sprintf('%s,c%d,w%d,u%d',
                        $this->branchId,
                        $this->selectedClassId,
                        $this->selectedWeeknum,
                        Yii::app()->user->id
                    ),
                    CLogger::LEVEL_INFO, 'teaching.gradeclassnews');
                $this->addMessage('state', 'success');
                $this->addMessage('refresh',true);
                $this->addMessage('referer',$this->createUrl('/mteaching/weekly/index',array('classid'=>$this->selectedClassId,'weeknum'=>$this->selectedWeeknum,'task'=>$this->selectedTask)));
                $this->addMessage('message', Yii::t('message','Data saved!'));
            }else{
                $this->addMessage('state', 'fail');
                $errs = current($model->getErrors());
                $this->addMessage('message', $errs?$errs[0]:Yii::t('message','Saving Failed!'));
            }
            $this->showMessage();
        }
        
        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/underscore-min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/backbone-min.js');
        return array('model'=>$model,'classNewsList'=>$classNewsList);
    }

    public function taskChildreport()
    {
        if(Yii::app()->request->isPostRequest && Yii::app()->request->isAjaxRequest && isset($_POST['NotesChild'])){
            if(!$this->checkTeachers() || !Yii::app()->user->checkAccess('o_T_Adm_Common')){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', 'Unauthorized operation, please contact IT Dept.');
                $this->showMessage();
            }
            $criteria = new CDbCriteria();
            $criteria->compare('classid', $this->selectedClassId);
            $criteria->compare('weeknumber', $this->selectedWeeknum);
            $criteria->compare('childid', $_POST['NotesChild']['childid']);
            $model = NotesChild::model()->find($criteria);
            if($model === null)
                $model = new NotesChild();
            $model->attributes=$_POST['NotesChild'];
            $model->classid=$this->selectedClassId;
            $model->yid=$this->calendarId;
            $model->weeknumber=$this->selectedWeeknum;
            $model->updated_timestamp=time();
            $model->userid=Yii::app()->user->id;
            $model->startyear=$this->calendarModel->startyear;
            if($model->save()){
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message','Data saved!'));
                $this->addMessage('callback', 'callback');
                $this->addMessage('data', array('childid'=>$model->childid, 'stat'=>$model->stat));
            }
            else{
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('message','Saving Failed!'));
            }

            $this->showMessage();
        }
        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/underscore-min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/backbone-min.js');

        $model = new NotesChild();

        $cond = "classid=:classid and weeknumber=:weeknum and stat=20";
        $condParams = array(
            ':classid'  => $this->selectedClassId,
            ':weeknum'  => $this->selectedWeeknum,
        );
        $rows = Yii::app()->db->createCommand()
            ->select( 'childid' )
            ->from('ivy_notes_child')
            ->where($cond, $condParams )
            ->queryAll();
        $onlines = array();
        foreach($rows as $row){
            $onlines[] = $row['childid'];
        }

        return array('model'=>$model, 'onlines'=>$onlines);
    }

    public function actionGetLinks($id=0, $yid=0, $classid=0, $weeknum=0)
    {
        if($id && $yid && $classid && $weeknum){
            $this->calendarId = $this->branchObj->schcalendar;
            Yii::import('common.models.calendar.Calendar');
            $this->calendarModel = Calendar::model()->findByPk($this->calendarId);

            Yii::import('common.models.portfolio.*');
            ChildMediaLinks::setStartYear($this->calendarModel->startyear);

            $criteria = new CDbCriteria();
            $criteria->compare('category', 'week');
            $criteria->compare('yid', $yid);
            $criteria->compare('classid', $classid);
            $criteria->compare('weeknum', $weeknum);
            $criteria->compare('pid', $id);
            $items = ChildMediaLinks::model()->findAll($criteria);
            $ret = array();
            foreach($items as $item){
                $ret[] = $item->childid;
            }
            echo CJSON::encode($ret);
        }
    }

    public function actionChildPhotos($id=0, $classid=0, $weeknum=0)
    {
        if($id && $classid && $weeknum){
            $this->calendarId = $this->branchObj->schcalendar;
            Yii::import('common.models.calendar.Calendar');
            $this->calendarModel = Calendar::model()->findByPk($this->calendarId);

            Yii::import('common.models.portfolio.*');
            ChildMedia::setStartYear($this->calendarModel->startyear);
            ChildMediaLinks::setStartYear($this->calendarModel->startyear);

            $criteria = new CDbCriteria();
            $criteria->compare('t.category', 'week');
            $criteria->compare('t.yid', $this->calendarId);
            $criteria->compare('t.classid', $classid);
            $criteria->compare('t.weeknum', $weeknum);
            $criteria->compare('t.childid', array(0,$id));
            $criteria->order = 't.weight';
            $items = ChildMediaLinks::model()->with('photoInfo')->findAll($criteria);
            $ret = array();
            foreach($items as $item){
                $ret[] = array(
                    'id' => $item->pid,
                    'url' => $item->photoInfo->getMediaUrl(true),
                    'caption' => $item->content,
                    'weight' => $item->weight,
                    'allchild' => $item->childid == 0 ? 1 : 0
                );
            }
            $sql = "select count(*) as count from ivy_notes_child where stat=20 and classid=".intval($classid)." and childid=".intval($id)." and weeknumber=".intval($weeknum);
            $row = Yii::app()->db->createCommand($sql)->queryRow();
            echo CJSON::encode(array('stat'=>$row['count'], 'ret'=>$ret));
        }
    }

    public function actionChildNote($id=0, $classid=0, $weeknum=0)
    {
        if($id && $classid && $weeknum){
            $criteria = new CDbCriteria();
            $criteria->compare('classid', $classid);
            $criteria->compare('weeknumber', $weeknum);
            $criteria->compare('childid', $id);
            $model = NotesChild::model()->find($criteria);
            if($model === null){
                $model = new NotesChild();
                $model->en_content='';
            }

            echo CJSON::encode(array('id'=>$model->childid, 'content'=>$model->en_content, 'stat'=>$model->stat));
        }
    }

    public function taskSpecialnotes()
    {
        if(Yii::app()->request->isPostRequest &&
            Yii::app()->request->isAjaxRequest &&
            isset($_POST['NotesSpecial']['childid'])){
            if(!$this->checkTeachers() || !Yii::app()->user->checkAccess('o_T_Adm_Common')){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', 'Unauthorized operation, please contact IT Dept.');
                $this->showMessage();
            }
            Yii::import('common.models.portfolio.*');
            ChildMediaLinks::setStartYear($this->calendarModel->startyear);

            $criteria = new CDbCriteria();
            $criteria->compare('classid', $this->selectedClassId);
            $criteria->compare('yid', $this->calendarId);
            $criteria->compare('weeknum', $this->selectedWeeknum);
            $criteria->compare('childid', $_POST['NotesSpecial']['childid']);
            $models = NotesSpecial::model()->find($criteria);
            if($models === null)
                $models = new NotesSpecial();
            $models->schoolid=$this->branchId;
            $models->classid=$this->selectedClassId;
            $models->yid=$this->calendarId;
            $models->childid=$_POST['NotesSpecial']['childid'];
            $models->yid=$this->calendarId;
            $models->weeknum=$this->selectedWeeknum;
            $models->stat=$_POST['NotesSpecial']['stat'];
            $models->timestamp=time();
            $models->uid=Yii::app()->user->id;
            $models->save();
            $pids = Yii::app()->request->getParam('pid', array());
            foreach($pids as $key=>$pid){
                $criteria = new CDbCriteria();
                $criteria->compare('category', 'snotes');
                $criteria->compare('classid', $this->selectedClassId);
                $criteria->compare('childid', $_POST['NotesSpecial']['childid']);
                $criteria->compare('yid', $this->calendarId);
                $criteria->compare('weeknum', $this->selectedWeeknum);
                $criteria->compare('pid', $pid);
                $model=ChildMediaLinks::model()->find($criteria);
                if($model === null)
                    $model = new ChildMediaLinks();
                $model->category='snotes';
                $model->classid=$this->selectedClassId;
                $model->childid=$_POST['NotesSpecial']['childid'];
                $model->yid=$this->calendarId;
                $model->weeknum=$this->selectedWeeknum;
                $model->pid=$pid;
                $model->content=$_POST['caption'][$key];
                $model->weight=$_POST['weight'][$key];
                $model->updated_timestamp=time();
                $model->userid=Yii::app()->user->id;
                $model->save();
            }
            $this->addMessage('state', 'success');
            $this->addMessage('message', Yii::t('message','Data saved!'));
			$this->addMessage('callback', 'callback1');
            $this->addMessage('data', array('childid'=>$model->childid, 'stat'=>$models->stat));
            $this->showMessage();
        }
        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/underscore-min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/backbone-min.js');

        $model = new NotesSpecial();

        $cond = "classid=:classid and weeknum=:weeknum and stat=20";
        $condParams = array(
            ':classid'  => $this->selectedClassId,
            ':weeknum'  => $this->selectedWeeknum,
        );
        $rows = Yii::app()->db->createCommand()
            ->select( 'childid' )
            ->from('ivy_notes_special')
            ->where($cond, $condParams )
            ->queryAll();
        $onlines = array();
        foreach($rows as $row){
            $onlines[] = $row['childid'];
        }
        $children = $this->getNameList($this->selectedClassId);

        $seCalendar = explode(',', $this->calendarModel->timepoints);

        $sMonth = intval(date('n', $seCalendar[0]));
        $eMonth = intval(date('n', $seCalendar[3]));
        $months = array();
        for($i=$sMonth; $i<=12+$eMonth; $i++){
            $months[]= ($i%12) ? $i%12 : 12;
        }

        return array('model'=>$model, 'onlines'=>$onlines, 'children'=>$children, 'months'=>$months);
    }

    public function actionSpecialNotes($id=0, $classid=0, $weeknum=0)
    {
        if($id && $classid && $weeknum){
            $this->calendarId = $this->branchObj->schcalendar;
            Yii::import('common.models.calendar.Calendar');
            $this->calendarModel = Calendar::model()->findByPk($this->calendarId);

            Yii::import('common.models.portfolio.*');
            ChildMedia::setStartYear($this->calendarModel->startyear);
            ChildMediaLinks::setStartYear($this->calendarModel->startyear);

            $criteria = new CDbCriteria();
            $criteria->compare('classid', $classid);
            $criteria->compare('weeknum', $weeknum);
            $criteria->compare('childid', $id);
            $model = NotesSpecial::model()->find($criteria);
            if($model === null){
                $model = new NotesSpecial();
            }

            $sConfs=$this->getServerConf($classid);
            $criteria = new CDbCriteria();
            $criteria->compare('t.category', 'snotes');
            $criteria->compare('t.classid', $classid);
            $criteria->compare('t.weeknum', $weeknum);
            $criteria->compare('t.childid', $id);
            $criteria->order='t.weight';
            $items=ChildMediaLinks::model()->with('photoInfo')->findAll($criteria);
            $photoData = array();
            foreach($items as $item){
                $photoData[]=array(
                    'id' => $item->pid,
                    'url'=> $item->photoInfo->getMediaUrl(true),
                    'val'=> $item->content,
                    'weight'=>$item->weight,
                    'isNew'=>0,
                );
            }

            echo CJSON::encode(array('id'=>$model->childid, 'stat'=>$model->stat, 'photoData'=>$photoData));
        }
    }

    public function actionDelPhotoLink($category='')
    {
        if(Yii::app()->request->isPostRequest){
            $pid=Yii::app()->request->getParam('pid',0);
            $classid=Yii::app()->request->getParam('classid',0);
            $weeknum=Yii::app()->request->getParam('weeknum',0);
            if($pid && $classid && $weeknum){
                $this->calendarId = $this->branchObj->schcalendar;
                Yii::import('common.models.calendar.Calendar');
                $this->calendarModel = Calendar::model()->findByPk($this->calendarId);

                Yii::import('common.models.portfolio.*');
                ChildMediaLinks::setStartYear($this->calendarModel->startyear);

                ChildMediaLinks::model()->deleteAllByAttributes(array('pid'=>$pid,'category'=>$category,'classid'=>$classid,'weeknum'=>$weeknum));
            }
        }
    }
	
	public function actionDelPhoto()
	{
        $id         = Yii::app()->request->getParam('id', 0);
        $classid    = Yii::app()->request->getParam('classid', 0);
        if($id && $classid){
            $this->selectedClassId = $classid;
            if(!$this->checkTeachers() || !Yii::app()->user->checkAccess('o_T_Adm_Common')){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', 'Unauthorized operation, please contact IT Dept.');
                $this->showMessage();
            }
            Yii::import('common.models.calendar.Calendar');
            $this->calendarModel = Calendar::model()->findByPk($this->calendarId);

            Yii::import('common.models.portfolio.*');
            ChildMedia::setStartYear($this->calendarModel->startyear);
            ChildMediaLinks::setStartYear($this->calendarModel->startyear);

            $criter = new CDbCriteria();
            $criter->compare('pid', $id);
            $item = ChildMediaLinks::model()->count($criter);
            if($item){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('teaching', 'Media file is used and cannot be deleted'));
            }
            else{
                $model = ChildMedia::model()->findByAttributes(array('id'=>$id, 'schoolid'=>$this->branchId));
                if($model->delete()){
                    if($model->delQiniuMedia($this->securityKey) == 'success'){
                        $this->addMessage('state', 'success');
                        $this->addMessage('message', Yii::t('global', 'Data deleted'));
                    }
                }
                else{
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', Yii::t('global', 'Deletion failed'));
                }
            }
            $this->showMessage();
        }
	}
	
	public function actionRotate()
	{
		$id         = Yii::app()->request->getParam('id', 0);
        $classid    = Yii::app()->request->getParam('classid', 0);
		if($id && $classid){
			$this->selectedClassId = $classid;
			$sConfs = $this->getServerConf($this->selectedClassId);
            if(!$this->checkTeachers() || !Yii::app()->user->checkAccess('o_T_Adm_Common')){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', 'Unauthorized operation, please contact IT Dept.');
                $this->showMessage();
            }
			Yii::import('common.models.calendar.Calendar');
            $this->calendarModel = Calendar::model()->findByPk($this->calendarId);

            Yii::import('common.models.portfolio.*');
            ChildMedia::setStartYear($this->calendarModel->startyear);
            $item = ChildMedia::model()->findByAttributes(array('id'=>$id, 'schoolid'=>$this->branchId));
			if($item->type == 'photo'){
				$array = explode(";;", $item->filename);
				if(!$array[1]){
					$item->filename = $array[0].';;A';
				}
				if($array[1] == 'A'){
					$item->filename = $array[0].';;B';
				}
				if($array[1] == 'B'){
					$item->filename = $array[0].';;C';
				}
				if($array[1] == 'C'){
					$item->filename = $array[0];
				}
				if($item->save()){
					$arr_rotate = explode(";;", $item->filename);
					$this->addMessage('state', 'success');
					$this->addMessage('message', Yii::t('teaching', 'Photo rotated'));
					$this->addMessage('data', array(
                        'photo' => $sConfs['url'].$arr_rotate[0].'!h120'.$arr_rotate[1],
                        'filename' => $item->filename,
                    ));
                }
				else{
					$this->addMessage('state', 'fail');
					$this->addMessage('message', Yii::t('teaching', 'Photo rotation failed'));
				}
				$this->showMessage();
			
			}
		}
	}


    public function actionSetAvatar()
	{
		$avatar        = Yii::app()->request->getParam('avatar', '');
		$avatarChildId = Yii::app()->request->getParam('avatarChildId', 0);
		$classid       = Yii::app()->request->getParam('classid', 0);
		if($avatar && $avatarChildId){
			$this->selectedClassId = $classid;
			if(!$this->checkTeachers() || !Yii::app()->user->checkAccess('o_T_Adm_Common')){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', 'Unauthorized operation, please contact IT Dept.');
				$this->showMessage();
            }
			$filepath = Yii::app()->params['OAUploadBasePath'].'/childmgt/';
			$item = ChildProfileBasic::model()->findByAttributes(array('childid'=>$avatarChildId, 'schoolid'=>$this->branchId));
			$filename = 'child_'.uniqid().'.jpg';
			$photo = file_get_contents($avatar);
			$old_photo = $item->photo;
			if($photo){
				file_put_contents($filepath.$filename, $photo);
				if(file_exists($filepath.$filename)){
					$item->photo = $filename;
                    $item->processAvatar($filename, $old_photo);
					if($item->save()){
                        if($old_photo != 'blank.gif'){
                            @unlink($filepath.$old_photo);
                        }
						$this->addMessage('state', 'success');
						$this->addMessage('message', Yii::t('message', 'Data Saved!'));
						$this->addMessage('data', array('childid' => $item->childid, 'photo' => $filename));
						$this->addMessage('callback', 'callback2');
					}
					else{
						$this->addMessage('state', 'fail');
						$this->addMessage('message', Yii::t('message', 'Data Saving Failed!'));
					}
				}
				else{
					$this->addMessage('state', 'fail');
					$this->addMessage('message', Yii::t('message', 'Data Saving Failed!'));
				}
			}
		}
		else{
			$this->addMessage('state', 'fail');
			$this->addMessage('message', Yii::t('message', 'Please select a child'));
		}
		$this->showMessage();
    }

    public function actionGetWeekSchedule(){
        if(isset($_POST['classid']) && isset($_POST['yid'])){
            Yii::import('common.models.schedule.*');
            $classObj = IvyClass::model()->findByAttributes(array(
                'schoolid' => $this->branchId,
                'classid' => $_POST['classid']
            ));
            if($classObj){
                $crit = new CDbCriteria();
                $crit->compare('classid', $_POST['classid']);
                $crit->compare('yid', $_POST['yid']);
                $crit->order = 'weeknumber ASC';
                $schedules = ClassScheduleV2::model()->with('data')->findAll($crit);
                foreach($schedules as $schedule){
                    $result['schedules'][$schedule->id] = base64_decode($schedule->data->data);
                    $result['schedulesLink'][$_POST['classid']][$schedule->weeknumber] = $schedule->id;
                }
                $this->addMessage('state','success');
                $this->addMessage('data', $result);
                $this->showMessage();
            }
            $this->addMessage('state','fail');
            $this->addMessage('message','Wrong Request');
            $this->showMessage();
        }else{
            Yii::import('common.models.calendar.CalendarSchool');
            $crit = new CDbCriteria();
            $crit->compare('branchid', $this->branchId);
            $crit->index = 'startyear';
            $crit->order = 'startyear DESC';

            $calendars = CalendarSchool::model()->findAll($crit);

            $currentStartyear = 0;
            foreach( $calendars as $startyear => $calendar ){
                $tmpData[$startyear] = $calendar->yid;
                if($this->branchObj->schcalendar == $calendar->yid){
                    $currentStartyear = $startyear;
                }
            }
            $yids[$currentStartyear] = $tmpData[$currentStartyear];
            if(isset( $tmpData[$currentStartyear - 1])){
                $yids[$currentStartyear - 1] = $tmpData[$currentStartyear - 1];
            }

            $data['calendars'] = $yids;
            $data['thisyear'] = $currentStartyear;
            $classes = IvyClass::getClassList($this->branchId, $yids, false, true);
            foreach($classes as $class){
                $data['classes'][$class->yid][$class->classid] = $class->title;
            }
            $this->addMessage('state', 'success');
            $this->addMessage('data', $data);
            $this->showMessage();
        }
    }

    public function actionMonthPhoto($month='', $classid=0, $childid='')
    {
        if($month && $classid && $childid){
            $this->selectedClassId = $classid;
            if(!$this->checkTeachers() || !Yii::app()->user->checkAccess('o_T_Adm_Common')){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', 'Unauthorized operation, please contact IT Dept.');
                $this->showMessage();
            }
            Yii::import('common.models.calendar.Calendar');
            $this->calendarModel = Calendar::model()->findByPk($this->calendarId);

            Yii::import('common.models.portfolio.*');
            ChildMedia::setStartYear($this->calendarModel->startyear);

            $criteria = new CDbCriteria();
            $criteria->compare('schoolid', $this->branchId);
            $criteria->compare('classid', $classid);
            $criteria->compare('tag', 'sc'.$classid.'_'.$childid.'_e', true);
            $criteria->compare('tag', 'sm_'.$month.'_e', true);
            $items = ChildMedia::model()->findAll($criteria);
            $photos = array();
            foreach($items as $item){
                $photos[$item->id] = $item->getMediaUrl(true);
            }
            if($photos){
                $this->addMessage('state', 'success');
                $this->addMessage('data', $photos);
            }
            else{
                $months = Yii::app()->getLocale()->monthNames;
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('teaching',
                    'No photos tagged for :month',
                    array(':month'=>$months[$month])));
            }
            $this->showMessage();
        }
    }

    public function actionSetAll($classid=0, $weeknum=0, $flag='weekly')
    {
        $type = Yii::app()->request->getPost('type', '');
        if($classid && $weeknum && $type){
            $this->selectedClassId = $classid;
            if(!$this->checkTeachers()){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', 'Unauthorized operation, please contact IT Dept.');
                $this->showMessage();
            }

            if($flag == 'weekly'){
                $cond = 'classid=:classid and weeknumber=:weeknum';
                $dataModel = NotesChild::model();
            }
            else{
                $cond = 'classid=:classid and weeknum=:weeknum';
                $dataModel = NotesSpecial::model();
            }

            $stat = $type == 'online' ? 20 : 10;

            $condParams = array(
                ':classid'=>$classid,
                ':weeknum'=>$weeknum
            );

            $updated = $dataModel->updateAll(
                array('stat'=>$stat),
                $cond,
                $condParams
            );

            $cond .= ' and stat=20';
            $rows = Yii::app()->db->createCommand()
                ->select( 'childid' )
                ->from($dataModel->tableName())
                ->where($cond, $condParams )
                ->queryAll();
            $onlines = array();
            foreach($rows as $row){
                $onlines[] = $row['childid'];
            }

            $this->addMessage('state', 'success');
            $this->addMessage('message', Yii::t('teaching', '有:successNum条保存成功', array(':successNum'=>$updated)));
            $this->addMessage('data', $onlines);
        }
        $this->showMessage();
    }
    
    /**
     * 小学周报全部上线、下线
     * @param type $classid
     * @param type $weeknum
     * @param type $flag
     */
    public function actionSetAllDs($classid=0, $weeknum=0, $flag='weekly')
    {
        $type = Yii::app()->request->getPost('type', '');
        if($classid && $weeknum && $type){
            $this->selectedClassId = $classid;
            if(!$this->checkTeachers()){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', 'Unauthorized operation, please contact IT Dept.');
                $this->showMessage();
            }
            Yii::import('common.models.calendar.Calendar');
            $stat = $type == 'online' ? 20 : 10;
            $onlines = array();
            $schoolClass = ChildClassLink::model()->with('calenderInfo')->findAll('t.classid=:classid and t.stat=:stat',array(':classid'=>$classid,':stat'=>ChildClassLink::STATS_ONGOING));
            if (!empty($schoolClass)){
                foreach ($schoolClass as $val){
                    $model = NotesChild::model()->find('classid=:classid and weeknumber=:weeknum and childid=:childid',array(':classid'=>$classid,':weeknum'=>$weeknum,':childid'=>$val->childid));
                    if (empty($model)){
                        $model = new NotesChild();
                        $model->classid = $classid;
                        $model->weeknumber = $weeknum;
                        $model->childid = $val->childid;
                        $model->yid = $val->calendar;
                        $model->weekid = 0;
                        $model->en_content = '';
                        $model->startyear = $val->calenderInfo->startyear;
                    }
                    $onlines[] = $val->childid;
                    $model->stat = $stat;
                    $model->updated_timestamp = time();
                    $model->userid = Yii::app()->user->getId();
                    $model->save();
                }
            }

            $this->addMessage('state', 'success');
            $this->addMessage('message', Yii::t('teaching', '有:successNum条保存成功', array(':successNum'=>count($onlines))));
            $this->addMessage('data', $onlines);
        }
        $this->showMessage();
    }
    
    public function actionDelGradeClassNews(){
        $notesId = Yii::app()->request->getPost('notesId',0);
        $classid = Yii::app()->request->getPost('classid',0);
        $weeknum = Yii::app()->request->getPost('weeknum',0);
        $task = Yii::app()->request->getPost('task','');
        if (Yii::app()->request->isAjaxRequest && $notesId){
            $notesModel = NotesSchCla::model()->findByPk($notesId);
            if (!empty($notesModel) && $notesModel->classid ==$classid && $weeknum == $notesModel->weeknumber){
                if ($notesModel->delete()){
                    $this->addMessage('state', 'success');
                    $this->addMessage('message', Yii::t('global', 'Data deleted'));
                    $this->addMessage('refresh',true);
                    $this->addMessage('referer',$this->createUrl('/mteaching/weekly/index',array('classid'=>$classid,'weeknum'=>$weeknum,'task'=>$task)));
                }else{
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', Yii::t('global', 'Deletion failed'));
                }
            }else{
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('global', 'Deletion failed'));
            }
        }
        $this->showMessage();
    }

    public function actionAsasasasa()
    {
        Yii::msg($_POST);
    }
}