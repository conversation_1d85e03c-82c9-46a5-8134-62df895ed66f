<?php

class CommissionController extends ProtectedController
{
    public $actionAccessAuths = array(
        'index'            => 'o_F_Access',
        'query'            => 'o_F_Access',
    );

    public function init(){
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'main';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site','Finance Mgt');
    }

	public function actionIndex()
	{
        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/underscore-min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/backbone-min.js');

        $branchs = Branch::model()->getBranchList();
        $this->render('index', array('branchs'=>$branchs));
	}

    public function actionQuery()
    {
        $schoolid   = Yii::app()->request->getParam('schoolid', '');
        $startdate  = Yii::app()->request->getParam('startdate', '');
        $enddate    = Yii::app()->request->getParam('enddate', '');
        $type       = Yii::app()->request->getParam('type', '');

        if($startdate && $enddate && $type){
            Yii::import('common.models.bank.*');
            $branchs = Branch::model()->getBranchList();
            $schoolfeerate = BankSchool::model()->getSchoolFeerate();
            $startdate = strtotime($startdate);
            $enddate = strtotime($enddate.' 23:59:59');
            if($type == 'alipay'){
                Yii::import('common.models.alipay.AlipayOrder');
                $condition = 'status=:status and update_timestamp >= :startdate and update_timestamp <= :enddate';
                $params[':status'] = 1;
                $params[':startdate'] = $startdate;
                $params[':enddate'] = $enddate;
                if($schoolid){
                    $condition .= ' and schoolid=:schoolid';
                    $params[':schoolid'] = $schoolid;
                }
                $items = Yii::app()->db->createCommand()
                    ->select('schoolid,sum(fact_amount) total')
                    ->from(AlipayOrder::model()->tableName())
                    ->where($condition, $params)
                    ->group('schoolid')
                    ->queryAll();
                $data = array();
                foreach($items as $item){
                    if(isset($schoolfeerate[$item['schoolid']][$type])){
                        $feerate = $schoolfeerate[$item['schoolid']][$type];
                        $data[$item['schoolid']]['total'] = number_format($item['total'], 2);
                        $data[$item['schoolid']]['feerate'] = ($feerate*100).'%';
                        $data[$item['schoolid']]['commission'] = number_format(round($item['total']*$feerate, 2), 2);
                        $data[$item['schoolid']]['title'] = $branchs[$item['schoolid']];
                    }
                }
            }
            elseif(in_array($type, array('yeepay', 'allinpay'))){
                Yii::import('common.models.yeepay.YeepayOrder');
                $condition = 'status=:status and updateTime >= :startdate and updateTime <= :enddate';
                $params[':status'] = 1;
                $params[':startdate'] = $startdate;
                $params[':enddate'] = $enddate;
                if($schoolid){
                    $condition .= ' and schoolId=:schoolid';
                    $params[':schoolid'] = $schoolid;
                }

                if($type == 'allinpay'){
                    $params[':type'] = $type;
                    $condition .= ' and r2_TrxId=:type';

                    $items = Yii::app()->db->createCommand()
                        ->select('schoolid,fact_amount,r7_Uid')
                        ->from(YeepayOrder::model()->tableName())
                        ->where($condition, $params)
                        ->queryAll();
                    $schools = array();
                    $data = array();
                    foreach($items as $item){
                        $schools[$item['schoolid']][] = array(
                            'total' => $item['fact_amount'],
                            'v' => $item['r7_Uid'],
                        );
                    }

                    foreach($schools as $schoolid=>$sitems){
                        if(isset($schoolfeerate[$schoolid][$type])){
                            $stotal = 0;
                            $commission = 0;
                            foreach($sitems as $sitem){
                                $stotal += $sitem['total'];

                                if(isset($sitem['v']) && $sitem['v'] == 'v2' ){
                                    $feerate = 0.006;
                                    $scommissin = round($sitem['total']*$feerate, 2);
                                    $commission += (($scommissin>40) ? 40 : $scommissin);
                                }
                                else{
                                    $feerate = $schoolfeerate[$schoolid][$type];
                                    $commission += round($sitem['total']*$feerate, 2);
                                }
                            }
                            $data[$schoolid]['total'] = number_format($stotal, 2);
                            $data[$schoolid]['feerate'] = ($feerate*100).'%';
                            $data[$schoolid]['commission'] = number_format($commission, 2);
                            $data[$schoolid]['title'] = $branchs[$schoolid];
                        }
                    }
                }
                else{
                    $condition .= " and r2_TrxId != 'allinpay'";

                    $items = Yii::app()->db->createCommand()
                        ->select('schoolid,sum(fact_amount) total')
                        ->from(YeepayOrder::model()->tableName())
                        ->where($condition, $params)
                        ->group('schoolId')
                        ->queryAll();
                    $data = array();
                    foreach($items as $item){
                        if(isset($schoolfeerate[$item['schoolid']][$type])){
                            $feerate = $schoolfeerate[$item['schoolid']][$type];
                            $data[$item['schoolid']]['total'] = number_format($item['total'], 2);
                            $data[$item['schoolid']]['feerate'] = ($feerate*100).'%';
                            $data[$item['schoolid']]['commission'] = number_format(round($item['total']*$feerate, 2), 2);
                            $data[$item['schoolid']]['title'] = $branchs[$item['schoolid']];
                        }
                    }
                }
            }
            $this->addMessage('state', 'success');
            $this->addMessage('message', '查询成功');
            $this->addMessage('data', $data);
            $this->addMessage('callback', 'callback');
        }
        else{
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '请先选择开始日期、结束日期、类型！');
        }
        $this->showMessage();
    }
}