<?php

/**
 * This is the model class for table "ivy_contact_problem".
 *
 * The followings are the available columns in table 'ivy_contact_problem':
 * @property integer $id
 * @property string $content
 * @property string $status
 * @property integer $uid
 * @property string $update_time
 * @property string $type
 * @property string $date_type
 * @property integer $dayTime
 * @property integer $childid
 * @property integer $classid
 * @property integer $schoolid
 */
class ContactProblem extends CActiveRecord
{
    const STATUS_SUBMIT = 1;
    const TYPE_RIDE = 10;
    const TYPE_NORIDE = 20;
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_contact_problem';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('status, uid, update_time, type, childid, classid, schoolid', 'required'),
			array('uid, dayTime, childid, classid', 'numerical', 'integerOnly'=>true),
			array('status, update_time, type, date_type', 'length', 'max'=>255),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, content, status, uid, update_time, type, date_type, dayTime, childid, classid, schoolid', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'content' => 'Content',
			'status' => 'Status',
			'uid' => 'Uid',
			'update_time' => 'Update Time',
			'type' => 'Type',
			'date_type' => 'Date Type',
			'dayTime' => 'Day Time',
			'childid' => 'Childid',
			'classid' => 'Classid',
			'schoolid' => 'Schoolid',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('content',$this->content,true);
		$criteria->compare('status',$this->status,true);
		$criteria->compare('uid',$this->uid);
		$criteria->compare('update_time',$this->update_time,true);
		$criteria->compare('type',$this->type,true);
		$criteria->compare('date_type',$this->date_type,true);
		$criteria->compare('dayTime',$this->dayTime);
		$criteria->compare('childid',$this->childid);
		$criteria->compare('classid',$this->classid);
		$criteria->compare('schoolid',$this->schoolid);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return ContactProblem the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}
