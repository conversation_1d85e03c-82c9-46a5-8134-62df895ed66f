
<div  id='container' v-cloak >
    <div class='container-fluid'>


        <ol class="breadcrumb">
            <li><?php echo CHtml::link(Yii::t('site', 'Teaching Tasks'), array('//mteaching/default/index')) ?></li>
            <li><?php echo Yii::t('directMessage', '课表和值日安排') ?></li>
        </ol>
    </div>
    <div class="container-fluid" v-if='edit'>
        <div class='mt24'>
            <button type="button" class="btn btn-primary">管理课表</button>
        </div>
        <div class='mt24' >
            <el-tabs v-model="activeName" @tab-click="handleClick">
                <el-tab-pane label="By Classes" name="class" class='row'>
                    <div class='col-md-2'>
                        <div v-for='(list,index) in classList'>
                            <div class='font14 color3 filterList' :class='currentClassid==list.classid?"filterActive":""' @click='getClassSchedule(list)'>{{list.title}}</div>
                        </div>
                    </div>
                    <div class='col-md-10'>
                        <div class='flex mb16 align-items'>
                            <div class='flex1'>
                                <label class="checkbox-inline">
                                    <input type="checkbox" id="inlineCheckbox1" value="option1"> 显示值日
                                </label>
                            </div>
                            <button type="button" class="btn btn-primary">打印</button>
                        </div>
                        <div>
                            <table class="table table-bordered">
                                <thead>
                                <tr>
                                    <td></td>
                                    <td v-for='(list) in week'>{{list}}</td>
                                </tr>
                                </thead>
                                <tbody>
                                <tr>
                                    <td>
                                        <span class='tagDuty'>Morning Arrival</span>
                                        <div class='color3'>
                                            7:45-7:55
                                        </div>
                                    </td>
                                    <td colspan="5">
                                        <div class='flex duty' v-for='index in 6'>
                                            <div class='color3'>School Gate</div>
                                            <div class='flex1 text-right'>
                                                <span class='ml10' v-for='i in 4'>
                                                    <img src="https://apps.ivyonline.cn/uploads//users/thumbs/p_60b877c383b93.png" alt="" class='img24'>
                                                    <span class='font12 color6 ml4'>Cole Wagner</span>
                                                </span>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <div class='color3 font14'>HR/MM</div>
                                        <div class='color6 font12 mt4'>08:00 - 08:30</div>
                                    </td>
                                    <td v-for='index in 5'>
                                        <div class='course' v-for='index in 2'>
                                            <div class='color3 font12'>英文 ELA</div>
                                            <div class='mt4'>
                                                <img src="https://apps.ivyonline.cn/uploads//users/thumbs/p_60b877c383b93.png" alt="" class='img24'>
                                                <span class='font12 color6 ml4'>Cole Wagner</span>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <span class='tagDuty'>Morning Arrival</span>
                                        <div class='color3 font14'>2nd</div>
                                        <div class='color6 font12 mt4'>08:00 - 08:30</div>
                                    </td>
                                    <td v-for='index in 5'>
                                        <div class='duty' v-for='index in 2'>
                                            <div class='mt4'>
                                                <img src="https://apps.ivyonline.cn/uploads//users/thumbs/p_60b877c383b93.png" alt="" class='img24'>
                                                <span class='font12 color6 ml4'>Cole Wagner</span>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </el-tab-pane>
                <el-tab-pane label="By Teacher" name="teacher"  class='row'>
                    <div class='col-md-2'>
                        <div class="list-group">
                            <a href="#" class="list-group-item active">
                                Cras justo odio
                            </a>
                            <a href="#" class="list-group-item">Dapibus ac facilisis in</a>
                            <a href="#" class="list-group-item">Morbi leo risus</a>
                            <a href="#" class="list-group-item">Porta ac consectetur ac</a>
                            <a href="#" class="list-group-item">Vestibulum at eros</a>
                        </div>
                    </div>
                    <div class='col-md-10'>
                        <div class='flex mb16 align-items'>
                            <div class='flex1'>
                                <label class="checkbox-inline">
                                    <input type="checkbox" id="inlineCheckbox1" value="option1"> 显示值日
                                </label>
                            </div>
                            <button type="button" class="btn btn-primary">打印</button>
                        </div>
                        <div>
                            <table class="table table-bordered">
                                <thead>
                                <tr>
                                    <td></td>
                                    <td v-for='(list) in week'>{{list}}</td>
                                </tr>
                                </thead>
                                <tbody>
                                <tr>
                                    <td>
                                        <span class='tagDuty'>Morning Arrival</span>
                                        <div class='color3'>
                                            7:45-7:55
                                        </div>
                                    </td>
                                    <td colspan="5">
                                        <div class='flex duty' v-for='index in 6'>
                                            <div class='color3'>School Gate</div>
                                            <div class='flex1 text-right'>
                                                <span class='ml10' v-for='i in 4'>
                                                    <img src="https://apps.ivyonline.cn/uploads//users/thumbs/p_60b877c383b93.png" alt="" class='img24'>
                                                    <span class='font12 color6 ml4'>Cole Wagner</span>
                                                </span>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <div class='color3 font14'>HR/MM</div>
                                        <div class='color6 font12 mt4'>08:00 - 08:30</div>
                                    </td>
                                    <td v-for='index in 5'>
                                        <div class='course' v-for='index in 2'>
                                            <div class='color6 font12'>英文 ELA</div>
                                            <div class='mt4 font14 color3  text-center'>
                                                2A
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <span class='tagDuty'>Morning Arrival</span>
                                        <div class='color3 font14'>2nd</div>
                                        <div class='color6 font12 mt4'>08:00 - 08:30</div>
                                    </td>
                                    <td v-for='index in 5'>
                                        <div class='duty color3 font16' v-for='index in 2'>
                                            1A
                                        </div>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </el-tab-pane>
            </el-tabs>
        </div>
    </div>
    <div v-else>
        <div class='flex editTitle align-items'>
            <div class='flex1'>
                <span class='font14 color3'>← Schedule 1</span>
            </div>
            <button type="button" class="btn btn-primary">View All</button>
        </div>
        <div class="container-fluid" >
            <div class='row mt24'>
                <div class='col-md-2'>
                    <div v-for='index in 10'>
                        <div class='font14 color3 filterList' :class='index==1?"filterActive":""'> 2023-2024 Grade 1B</div>
                    </div>
                </div>
                <div class='col-md-10'>
                    <div class='text-right mb16'>
                        <button type="button" class="btn btn-link colorRed"><span class='el-icon-delete mr4'></span>清空</button>
                        <button type="button" class="btn btn-link"><span class='el-icon-refresh mr4'></span>刷新</button>
                    </div>
                    <div>
                        <table class="table table-bordered">

                            <tbody>
                            <tr>
                                <td></td>
                                <td v-for='(list) in week'>{{list}}</td>
                            </tr>
                            <tr>
                                <td colspan="6" style='background:#FAFAFA'>
                                    <span class='tagDuty'>Morning Arrival</span>
                                    <div class='color3' >
                                        7:45-7:55
                                    </div>
                                </td>
                            </tr>
                            <tr v-for='id in 4'>
                                <td>
                                    <div class='color3 font14'>HR/MM</div>
                                    <div class='color6 font12 mt4'>08:00 - 08:30</div>
                                </td>
                                <td v-for='index in 5' @click='editTd(id,index)' :class='showTd==(id+"_"+index)?"p0":""'>
                                    <div :class='showTd==(id+"_"+index)?"addList":""'>
                                        <div class='course' v-for='indexs in 2'>
                                            <div class='color3 font12 flex'><span class='flex1'>英文 ELA</span> <span class='el-icon-delete colorRed'></span></div>
                                            <div class='mt4'>
                                                <img src="https://apps.ivyonline.cn/uploads//users/thumbs/p_60b877c383b93.png" alt="" class='img24'>
                                                <span class='font12 color6 ml4'>Cole Wagner</span>
                                            </div>
                                        </div>
                                        <span class='addBtn' v-if='showTd==(id+"_"+index)'>+</span>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td  colspan="6" style='background:#FAFAFA'>
                                    <span class='tagDuty'>Morning Arrival</span>
                                    <span class='color3 font14'>2nd</span>
                                    <span class='color6 font12 mt4'>08:00 - 08:30</span>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>
<script>

    var container = new Vue({
        el: "#container",
        data: {
            schedule_id:24,
            currentClassid:'',
            edit:true,
            activeName:'class',
            week:['Monday','Tuesday','Wednesday','Thursday','Friday'],
            showTd:null,
            classList:[]
        },
        watch:{

        },
        created: function() {
            this.getapplyClass()

        },
        computed: {

        },
        methods: {
            getapplyClass(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("applyClass") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        schedule_id:this.schedule_id,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            console.log(data)
                            that.classList=data.data
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            getClassSchedule(list){
                this.currentClassid=list.classid
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("listByClass") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        schedule_id:this.schedule_id,
                        class_id:list.classid
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            console.log(data)
                            //    that.classList=data.data
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            editTd(id,index){
                this.showTd=id+'_'+index
                console.log(this.showTd)
            }
        }
    })
</script>
<style>
    .filterList{
        padding:10px;
        font-size:14px
    }
    .filterActive{
        color: #ffffff !important;
        background-color: #428bca;
        border-color: #428bca;
        border-radius:4px
    }
    .duty{
        padding:8px 16px;
        background: #FFFBF4;
        border-radius: 4px;
        align-items:center;
        margin:4px 0;
    }
    td{
        vertical-align: middle !important;
        position: relative;
        text-align:center
    }
    .tagDuty{
        position: absolute;
        left:0;
        top:0;
        background: #F0AD4E;
        border-radius: 0px 80px 80px 0px;
        color:#fff;
        padding:2px 8px;
    }
    .img24{
        width:24px;
        height:24px;
        border-radius: 50%;
        object-fit: cover;
    }
    .course{
        padding:10px 12px;
        background: #F0F6FF;
        border-radius: 4px;
        text-align:left;
        margin:4px 0;
    }
    .editTitle{
        padding:16px;
        background: #FFFFFF;
        box-shadow: 0px 5px 8px 0px rgba(0,0,0,0.12);
    }
    .colorRed{
        color:#D9534F
    }
    .p0{
        padding:0 !important
    }
    .addList{
        border: 1px solid #4D88D2;
        padding:7px;
        position: relative;
    }
    .addBtn{
        position: absolute;
        right: -12px;
        bottom: -24px;
        width: 24px;
        height: 24px;
        background: #4D88D2;
        text-align: center;
        line-height: 24px;
        color: #fff;
        font-size: 18px;
        border-radius: 50%;
        z-index: 999;
    }
</style>