<?php

class RefundController extends BranchBasedController
{
    public function createUrl($route, $params = array(), $ampersand = '&', $parentOnly = false)
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        return parent::createUrl($route, $params, $ampersand);
    }

    public $childsName = array();
    public $courseName = array();
    public $reFindList = array();

    public function init()
    {
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'campusOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site', 'Campus Workspace');

        $this->branchSelectParams['urlArray'] = array('//masa/refund/index');

        // jquery ui
        $cs = Yii::app()->clientScript;
        $cs->registerCoreScript('jquery.ui');
        $cs->registerCssFile($cs->getCoreScriptUrl() . '/jui/css/base/jquery-ui.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/vue2.js');
    }

    // 加载退费列表的三个列表
    public function actionIndex()
    {
        // 驳回列表
        $critrefund = new CDbCriteria;
        $critrefund->compare('site_id', $this->branchId);
        $critrefund->compare('status', 2);
        $refund = new CActiveDataProvider('AsaRefund', array(
            'criteria' => $critrefund,
            'sort' => array(
                'defaultOrder' => 'updated DESC',
            ),
            'pagination'=>array(
                'pageSize'=>20,
            ),
        ));
        $childId = array();
        $courseIds = array();
        foreach($refund->getData() as $child){
            $childId[$child->childid] = $child->childid;
            $courseIds[$child->course_id] = $child->course_id;
        }

        // 待审核
        $critwith = new CDbCriteria;
        $critwith->compare('site_id', $this->branchId);
        $critwith->compare('status', array(1));
        $review = new CActiveDataProvider('AsaRefund', array(
            'criteria' => $critwith,
            'sort' => array(
                'defaultOrder' => 'updated DESC',
            ),
            'pagination'=>array(
                'pageSize'=>20,
            ),
        ));

        foreach($review->getData() as $child){
            $childId[$child->childid] = $child->childid;
            $courseIds[$child->course_id] = $child->course_id;
        }
        // 退费中
        $critwith = new CDbCriteria;
        $critwith->compare('site_id', $this->branchId);
        $critwith->compare('status', array(3,4));
        $refundIntermediate = new CActiveDataProvider('AsaRefund', array(
            'criteria' => $critwith,
            'sort' => array(
                'defaultOrder' => 'updated DESC',
            ),
            'pagination'=>array(
                'pageSize'=>20,
            ),
        ));

        foreach($refundIntermediate->getData() as $child){
            $childId[$child->childid] = $child->childid;
            $courseIds[$child->course_id] = $child->course_id;
        }
        
        if($childId){
            $childModel = ChildProfileBasic::model()->findAllByPk($childId);

            foreach($childModel as $child){
                $this->childsName[$child->childid] = array(
                    'child' => $child->getChildName(),
                    'class' => $child->ivyclass->title,
                );
            }
        }

        if($courseIds){
            $courseObj = AsaCourse::model()->findAllByPk($courseIds);

            foreach($courseObj as $child){
                $this->courseName[$child->id] = array(
                    'title' => $child->getTitle(),
                    'schedule' => $child->schedule->title,
                );
            }
        }

        $this->reFindList = CommonUtils::LoadConfig('CfgASA');

        $this->render('index', array(
            'refund' => $refund,
            'refundIntermediate' => $refundIntermediate,
            'review' => $review,
        ));
    }

    public function actionRefundOk()
    {
        $childName = Yii::app()->request->getParam('childName', "");
        $courseGroupId = intval(Yii::app()->request->getParam('courseGroup', 0));
        $courseId = intval(Yii::app()->request->getParam('course', 0));
        $refundTime = Yii::app()->request->getParam('refundTime', "");

        $crit = new CDbCriteria;
        $crit->compare('schoolid', $this->branchId);
        $crit->order = 'startyear desc';
        $asaCourseGroups = AsaCourseGroup::model()->findAll($crit);

        $courseGroups = array();
        $courses = array();
        foreach($asaCourseGroups as $courseGroup){
            $courseGroups[$courseGroup->id] = $courseGroup->getName();
            foreach($courseGroup->course as $course){
                $courses[$course->gid][$course->id] = $course->getTitle();
            }
        }

        $childids = array();
        if($childName){
            $crit = new CDbCriteria;
            $crit->addCondition("concat_ws(',',name_cn,first_name_en,middle_name_en,last_name_en) like '%{$childName}%' ");
            $childs = ChildProfileBasic::model()->findAll($crit);
            if($childs){
                foreach($childs as $childid){
                    $childids[$childid->childid] = $childid->childid;
                }
            }
        }
        
        $childids = ($childids) ? $childids : 0;
        $crits = new CDbCriteria;
        $crits->compare('site_id', $this->branchId);
        $crits->compare('status', 5);
        if($courseGroupId)$crits->compare('program_id', $courseGroupId);
        if($childName)$crits->compare('childid', $childids);
        if($courseId)$crits->compare('course_id', $courseId);
        if($refundTime)$crits->compare('dropout_date', strtotime($refundTime));
        $refundCompleted = new CActiveDataProvider('AsaRefund', array(
            'criteria' => $crits,
            'sort' => array(
                'defaultOrder' => 'updated_done DESC',
            ),
            'pagination'=>array(
                'pageSize'=>20,
            ),
        ));
        $childId = array();
        $courseIds = array();
        foreach($refundCompleted->getData() as $child){
            $childId[$child->childid] = $child->childid;
            $courseIds[$child->course_id] = $child->course_id;
        }
        if($childId){
            $childModel = ChildProfileBasic::model()->findAllByPk($childId);

            foreach($childModel as $child){
                $this->childsName[$child->childid] = array(
                    'child' => $child->getChildName(),
                    'class' => $child->ivyclass->title,
                );
            }
        }

        if($courseIds){
            $courseObj = AsaCourse::model()->findAllByPk($courseIds);

            foreach($courseObj as $child){
                $this->courseName[$child->id] = array(
                    'title' => $child->getTitle(),
                    'schedule' => $child->schedule->title,
                );
            }
        }



        $this->reFindList = CommonUtils::LoadConfig('CfgASA');

        $this->render('refundOk', array(
            'refundCompleted' => $refundCompleted,
            'courses' => $courses,
            'courseGroups' => $courseGroups,
        ));
    }

    // 退费信息的资料
    public function actionGetCourse()
    {
        //获取账单ID
        $invoiceid = intval(Yii::app()->request->getParam('invoiceid', 0));
        $specialGroup  = false;

        if ($invoiceid) {
            $invoice = AsaInvoice::model()->findByPk($invoiceid);
            $items = array();
            foreach ($invoice->item as $item) {
                $items[] = $item->id;
                $groupId = $item->course_group_id;
                if (in_array($groupId, AsaCourseGroup::getNoRefundGroupIds())) {
                    $specialGroup  = true;
                }
            }
            $data = array();
            if ($invoice->schoolid == $this->branchId) {
                $groupObj = AsaCourseGroup::model()->findByPk($groupId);

                $criteria = new CDbCriteria;
                $criteria->select = 'payee_info';
                $criteria->compare('childid', $invoice->childid);
                $criteria->order = 'updated';
                $criteria->limit = 1;
                $asaRefundPayeeInfo = AsaRefund::model()->find($criteria);
                $data['payee_info'] = array();
                if($asaRefundPayeeInfo){
                    $data['payee_info'] = CJSON::decode($asaRefundPayeeInfo->payee_info);

                }

                if($groupObj->program_type == AsaCourseGroup::PROGRAM_CAMP || $specialGroup){
                    $criteria = new CDbCriteria;
                    $criteria->compare('childid', $invoice->childid);
                    $criteria->compare('program_id', $groupObj->id);
                    $criteria->compare('status', "<>6");
                    $criteria->order = 'updated';
                    $asaRefund = AsaRefund::model()->findAll($criteria);

                    //如果有退费， 拿到退费的课程组
                    if($asaRefund){
                        $array = array();
                        $arrays = array();
                        $refu = array();
                        foreach($asaRefund as $item){
                            if($item->status == 6){
                                continue;
                            }
                            $refu[$item->course_id] = $item->refund_type;
                            if($item->status == 5){
                                $array[$item->course_id] += $item->refund_total_amount;
                            }else{
                                $arrays[$item->course_id] = 2;
                            }
                        }
                    }

                    $data['title'] = $invoice->title;
                    $data['invoiceId'] = $invoice->id;
                    $data['status'] = 1;
                    $data['pay_type_txt'] = ($invoice->pay_type == 1) ? "现金" : "微信";
                    $data['url'] = $this->createUrl('refund/camp');
                    $data['pay_type'] = $invoice->pay_type;
                    $data['pay_date'] = date('Y-m-d', $invoice->updated);
                    $data['amount_actual'] = $invoice->amount_actual;
                    foreach($invoice->item as $item){
                        $refundAmount = 0;
                        $refundStatus = 0;
                        $refundFlag = 0;
                        if($array && isset($array[$item->course_id])){
                            $refundFlag = 1;
                            $refundStatus = 1;
                            if ($specialGroup) {
                                $refundStatus = 0;
                            }
                            $refundAmount = $array[$item->course_id];
                        }
                        if (isset($arrays[$item->course_id])) {
                            $refundFlag = 1;
                            $refundStatus = 1;
                        }

                        $data['course'][$item->id] = array(
                            'title' => (isset($item->asaInvoiceItemExtra)) ? $item->asacourse->getTitle() . ' ( ' . date("Y-m-d",strtotime($item->asaInvoiceItemExtra->extra_date)) . " ) ":$item->asacourse->getTitle(),
                            'price' => $item->unit_price,
                            'courseId' => $item->id,
                            'status' => $refundStatus,
                            'refundTypeStat' => ($refu && $refu[$item->course_id]) ? $refu[$item->course_id] :  0,
                            'statusName' => $refundFlag == 1 ?  Yii::t('asa','已退金额：') . number_format($refundAmount, 2) : "",
                            'courseMoney' => $item->actual_total,
                        );
                    }

                    echo CJSON::encode($data);

                }else{

                    $scheduleId = array();
                    foreach($invoice->item as $courseidItem){
                        $scheduleId[] = $courseidItem->asacourse->schedule_id;
                    }
                    $scheduleIdDateObj = array();
                    // 获取课程的安排时间
                    $criteria = new CDbCriteria;
                    $criteria->compare('schedule_id', $scheduleId);
                    $criteria->index = 'id';
                    $scheduleItem = AsaScheduleItem::model()->findAll($criteria);
                    foreach($scheduleItem as $scheduleItm){
                        $scheduleIdDateObj[$scheduleItm->schedule_id][$scheduleItm->id] = array(
                            'dateText' => date("Y-m-d", strtotime($scheduleItm->schedule_date)),
                            'ready_s' => $scheduleItm->time_start,
                            'ready_e' => $scheduleItm->time_end,

                        );
                    }

                    $criteria = new CDbCriteria;
                    $criteria->compare('schedule_item_id', array_keys($scheduleItem));
                    $scheduleItemRevise = AsaScheduleItemRevise::model()->findAll($criteria);
                    $scheduleItemReviseArr = array();
                    if($scheduleItemRevise){
                        foreach($scheduleItemRevise as $val){
                            $scheduleItemReviseArr[$val->cid][$val->schedule_id][$val->schedule_item_id] = array(
                                'dateText' => date("Y-m-d", strtotime($val->schedule_date)),
                                'ready_s' => $val->time_start,
                                'ready_e' => $val->time_end,
                            );
                        }
                    }

                    //拿到当前课程下的退费状态
                    $criteria = new CDbCriteria;
                    $criteria->compare('invoice_item_id', $items);
                    $criteria->compare('status', '<>6');
                    $asarefund = AsaRefund::model()->findAll($criteria);

                    $find = array();
                    if ($asarefund) {

                        foreach ($asarefund as $_refund) {

                            $find[$_refund->course_id]['refund_count'] += $_refund->refund_class_count;  // 退费节数
                            $find[$_refund->course_id]['allstatus'][] = $_refund->status;   //退费状态
                            $find[$_refund->course_id]['refund_type'] = $_refund->refund_type;  // 退费类型
                        }
                    }

                    $data['title'] = $invoice->title;
                    $data['groupProgramType'] = ($groupObj->program_type == AsaCourseGroup::PROGRAM_DELAYCARE) ? 1 : 0;
                    $data['url'] = ($groupObj->program_type == AsaCourseGroup::PROGRAM_DELAYCARE) ? $this->createUrl('refund/saveRefundExtra') : $this->createUrl('refund/save');
                    $data['invoiceId'] = $invoice->id;
                    $data['pay_type_txt'] = ($invoice->pay_type == 1) ? "现金" : "微信";
                    $data['pay_type'] = $invoice->pay_type;
                    $data['pay_date'] = date('Y-m-d', $invoice->updated);
                    $data['status'] = 0;
                    $data['invoice_extra'] = ($invoice->itemExtra) ? 1 : 0;
                    $data['amount_actual'] = $invoice->amount_actual;
                    $invoiceIdS = array();

                    $refundStatusType = array(
                        AsaRefund::PROGRAM_ALL,
                        AsaRefund::PROGRAM_SECTION,
                        AsaRefund::PROGRAM_ACTIVE,
                    );

                    foreach ($invoice->item as $item) {
                        $invoiceIdS[] =  'item_' .$item->id;
                        if($find[$item->course_id]){
                            $sta = ($item->class_count - $find[$item->course_id]['refund_count'] > 0 ) ? 0 : 1 ;   //  大于0  则有退费课程
                            $find[$item->course_id]['status'] = (array_intersect($find[$item->course_id]['allstatus'], array(1,2,3,4))) ? 1 : 0;  // 1  正在退费中的
                        }

                        $refundStatus = (isset($find) && isset($find[$item->course_id])) ? $find[$item->course_id]['refund_type'] : 0;
                        $courseTime = $scheduleIdDateObj[$item->asacourse->schedule_id] ? ( $scheduleItemReviseArr[$item->asacourse->id] && $scheduleItemReviseArr[$item->asacourse->id][$item->asacourse->schedule_id]  ? $scheduleItemReviseArr[$item->asacourse->id][$item->asacourse->schedule_id] + $scheduleIdDateObj[$item->asacourse->schedule_id] : $scheduleIdDateObj[$item->asacourse->schedule_id])  : "" ;
                        $data['course'][$item->id] = array(
                            'title' => (isset($item->asaInvoiceItemExtra)) ? $item->asacourse->getTitle() . ' ( ' . date("Y-m-d",strtotime($item->asaInvoiceItemExtra->extra_date)) . " ) ":$item->asacourse->getTitle(),
                            'refundStatus' => $refundStatus,
                            'count' => $item->class_count,
                            'price' => $item->unit_price,
                            'refund_count' => ($find[$item->course_id]) ? $find[$item->course_id]['refund_count'] : 0, // 退费的节数
                            'status' => ($find[$item->course_id]) ? (($find[$item->course_id]['status'])  ? 1 : (in_array($refundStatus, $refundStatusType) ? 1 : $sta))  : 0,
                            'statusName' => ($find[$item->course_id]) ? (($find[$item->course_id]['status'])  ? '有正在退费的账单' : (($sta || in_array($refundStatus, $refundStatusType)) ? "课程全部退完" : "") )  : "",
                            'discountStatus' => 0,
                            'courseTime' => $courseTime,

                        );
                    }

                    $criteria = new CDbCriteria;
                    $criteria->compare('invoice_id', $invoiceIdS);
                    $discountLink = AsaInvoiceDiscountLink::model()->findAll($criteria);

                    if($discountLink){
                        foreach($discountLink as $linkItem){
                            $data['course'][substr($linkItem->invoice_id, 5)]['discountStatus'] = 1;
                        }
                    }

                    echo CJSON::encode($data);
                }
            } else {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('global', "没有权限操作其他校园账单"));
                $this->showMessage();
            }
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', Yii::t('global', "无效账单"));
            $this->showMessage();
        }
    }

    //增加退费信息
    public function actionSave($array = array())
    {
        $itemid = Yii::app()->request->getParam('itemid', array());
        $invoiceId = ($array) ? $array['invoiceId'] : intval(Yii::app()->request->getParam('invoiceId', 0));
        $bank = ($array) ? $array['bank'] : Yii::app()->request->getParam('bank', ""); //银行
        $accountName = ($array) ? $array['account_name'] : Yii::app()->request->getParam('account_name', "");//开户名
        $accountNumber = ($array) ? $array['account_number'] : Yii::app()->request ->getParam('account_number', "");//账号

        if($invoiceId || $array){ // 根据账单退费
            $dropoutDate = ($array) ? $array['dropout_date'] : Yii::app()->request->getParam('dropout_date', ""); //退费生效日期
            $refundReason =  ($array) ? $array['refund_reason'] : Yii::app()->request->getParam('refund_reason', ""); //退费原因
            $refundType = ($array) ? $array['refund_type']  : Yii::app()->request->getParam('refund_type', ""); //退费类型
            $refundFiles = ($array) ? $array['refund_files']  : Yii::app()->request->getParam('refund_files', "");  //申请退费文件
            $memo = ($array) ? $array['memo']  : Yii::app()->request->getParam('memo', "");//退费说明

            $invoice = AsaInvoice::model()->findByPk($invoiceId);
            if($invoice->pay_type == 1){
                if(empty($bank) || empty($accountName) || empty($accountNumber)){
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', Yii::t('message', '开户银行，开户名和卡号为必填项！'));
                    $this->showMessage();
                }
            }

            $refindItemIds = array();
            foreach($invoice->item as $items){
                $refindItemIds[] = $items->id;
                if (in_array($items->course_group_id, AsaCourseGroup::getNoRefundGroupIds())) {
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', Yii::t('message', '此课程组禁止退费'));
                    $this->showMessage();
                }
            }
            $num = 6;
            $criteria = new CDbCriteria;
            $criteria->compare('invoice_item_id', $refindItemIds);
            $criteria->compare('status', "<{$num}");
            $isRefundCount = AsaRefund::model()->findAll($criteria);

            if($isRefundCount){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('message', '账单下的课程有过退费，不可直接退全部账单!'));
                $this->showMessage();
            }
            $transaction = Yii::app()->subdb->beginTransaction();
            foreach ($invoice->item as $key => $invoItem) {
                $payType = $invoItem->asainvoice->pay_type;
                // 根据付费类型判断是否为换课
                if ($payType == 3) {
                    // 如果是换课则查找原始订单
                    $criteria = new CDbCriteria();
                    $criteria->compare('new_cid', $invoItem->course_id);
                    $criteria->compare('childid', $invoItem->asainvoice->childid);
                    $criteria->compare('status', AsaCourseExchange::SUCCESS);
                    $exchange = AsaCourseExchange::model()->find($criteria);
                    if ($exchange) {
                        $criteria = new CDbCriteria();
                        $criteria->compare('t.course_id', $exchange->old_cid);
                        $criteria->compare('asainvoice.childid', $invoItem->asainvoice->childid);
                        $criteria->with = 'asainvoice';
                        $oldInvoiceItem = AsaInvoiceItem::model()->find($criteria);
                        // 微信订单信息添加到退费说明 memo 里面
                        if ($oldInvoiceItem) {
                            $wechatOrderItem = AsaWechatpayOrder::model()->findByAttributes(array(
                                'invoice_id' => $oldInvoiceItem->order_id,
                                'childid' => $invoItem->asainvoice->childid,
                            ));
                            if ($wechatOrderItem) {
                                $memo .= "（原始换课账单ID：{$oldInvoiceItem->order_id}，微信订单：{$wechatOrderItem->orderid}）";
                            } else {
                                $memo .= "（原始换课账单ID：{$oldInvoiceItem->order_id}）";
                            }
                        }
                    }
                }
                try {
                    $model = new AsaRefund;
                    $model->setAttributes(array(
                        'site_id' => $this->branchId,
                        'childid' => $invoItem->asainvoice->childid,
                        'invoice_item_id' => $invoItem->id,
                        'program_id' => $invoItem->course_group_id,
                        'course_id' => $invoItem->course_id,
                        'refund_class_count' => $invoItem->asacourse->default_count,
                        'refund_total_amount' => $invoItem->actual_total,
                        'dropout_date' => strtotime($dropoutDate),
                        'memo' => $memo,
                        'refund_reason' => $refundReason,
                        'refund_type' => $refundType,
                        'refund_method' => ($payType == 3) ? 3 : (($payType == 2) ? 1 : 2),
                        'payee_info' => ($invoItem->asainvoice->pay_type == 1) ?  CJSON::encode(array('银行' => $bank, '姓名' => $accountName, '账号' => $accountNumber)) : "",
                        'status' => 1,
                        'refund_files' => CJSON::encode($refundFiles),
                        'updated' => time(),
                        'updated_by' => Yii::app()->user->id,
                    ));

                    if (!$model->save()) {
                        $transaction->rollback();
                        $error = current($model->getErrors());
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', Yii::t('message', $error[0]));
                        $this->showMessage();
                    }
                    $invoItem->refund_status = $model->id;
                    $invoItem->save();
                } catch (Exception $e) {
                    $transaction->rollback();
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', Yii::t('message', '非法操作'));
                    $this->showMessage();
                }
            }
            $transaction->commit();
            $this->addMessage('state', 'success');
            $this->addMessage('message', Yii::t('message', '提交成功!'));
            $this->addMessage('callback', 'refundSuccess');
            $this->showMessage();


        }else if ($itemid) { // 根据课程退费

            $refundCount = Yii::app()->request->getParam('refund_count', array()); //退费节数
            $refundMorry = Yii::app()->request->getParam('refund_morry', array()); //退费钱数
            $dropoutDate = Yii::app()->request->getParam('dropout_date', array()); //退费生效日期
            $refundReason = Yii::app()->request->getParam('refund_reason', array());//退费原因
            $refundType = Yii::app()->request->getParam('refund_type', array());   //退费类型
            $refundFiles = Yii::app()->request->getParam('refund_files', array()); //申请退费文件
            $memo = Yii::app()->request->getParam('memo', array());                //退费说明
            $invoiceItem = AsaInvoiceItem::model()->findAllByPk(array_keys($itemid));
            foreach ($invoiceItem as $items) {
                if (in_array($items->course_group_id, AsaCourseGroup::getNoRefundGroupIds())) {
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', Yii::t('message', '此课程组禁止退费'));
                    $this->showMessage();
                }
            }
            //这个账单的付款方式 如果是现金是否填写了银行卡信息
            $invoice = AsaInvoice::model()->findByPk($invoiceItem[0]->order_id);
            if($invoice->schoolid == $this->branchId){
                if($invoice->pay_type == 1){
                    if(empty($bank) || empty($accountName) || empty($accountNumber)){
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', Yii::t('message', '开户银行，开户名和卡号为必填项！'));
                        $this->showMessage();
                    }
                }

                //判断要退费的账单下额课程是否有正在退费中的账单
                $criteria = new CDbCriteria;
                $criteria->compare('invoice_item_id', array_keys($itemid));
                $criteria->compare('status', "<>6");
                $criteria->index = "id";
                $isreFund = AsaRefund::model()->findAll($criteria);
                $stat = array();

                if($isreFund){
                    foreach($isreFund as $status){
                        $stat[$status->course_id]  = 1;
                        if($status->status < 5){
                            $stat[$status->course_id]  = 2;
                        }
                    }

                    $criteria = new CDbCriteria;
                    $criteria->compare('refund_id', array_keys($isreFund));
                    $refundItem = AsaRefundItem::model()->findAll($criteria);
                    $refundItemObj = array();
                    if($refundItem){
                        foreach($refundItem as $itema){
                            $refundItemObj[$itema->course_id][$itema->schedule_item_id] = $itema->schedule_item_id;
                        }
                    }
                }

                $transaction = Yii::app()->subdb->beginTransaction();

                    foreach ($invoiceItem as $key => $invoItem) {
                        $memo = $memo[$invoItem->id];
                        $payType = $invoItem->asainvoice->pay_type;
                        // 根据付费类型判断是否为换课
                        if ($payType == 3) {
                            // 如果是换课则查找原始订单
                            $criteria = new CDbCriteria();
                            $criteria->compare('new_cid', $invoItem->course_id);
                            $criteria->compare('childid', $invoItem->asainvoice->childid);
                            $criteria->compare('status', AsaCourseExchange::SUCCESS);
                            $exchange = AsaCourseExchange::model()->find($criteria);
                            if ($exchange) {
                                $criteria = new CDbCriteria();
                                $criteria->compare('t.course_id', $exchange->old_cid);
                                $criteria->compare('asainvoice.childid', $invoItem->asainvoice->childid);
                                $criteria->with = 'asainvoice';
                                $oldInvoiceItem = AsaInvoiceItem::model()->find($criteria);
                                // 微信订单信息添加到退费说明 memo 里面
                                if ($oldInvoiceItem) {
                                    $wechatOrderItem = AsaWechatpayOrder::model()->findByAttributes(array(
                                        'invoice_id' => $oldInvoiceItem->order_id,
                                        'childid' => $invoItem->asainvoice->childid,
                                    ));
                                    if ($wechatOrderItem) {
                                        $memo .= "（原始换课账单ID：{$oldInvoiceItem->order_id}，微信订单：{$wechatOrderItem->orderid}）";
                                    } else {
                                        $memo .= "（原始换课账单ID：{$oldInvoiceItem->order_id}）";
                                    }
                                }
                            }
                        }
                        try {
                        if(empty($refundCount[$invoItem->id])){
                            $transaction->rollback();
                            $this->addMessage('state', 'fail');
                            $this->addMessage('message', Yii::t('message', '退费时间不能为空'));
                            $this->showMessage();
                        }

                        if($invoItem->class_count < count($refundCount[$invoItem->id])){
                            $transaction->rollback();
                            $this->addMessage('state', 'fail');
                            $this->addMessage('message', Yii::t('message', '退费结束不能大于购买节数'));
                            $this->showMessage();
                        }

                        if(isset($refundType[$invoItem->id]) && $refundType[$invoItem->id] == 3){
                            if(isset($refundItemObj) && isset($refundItemObj[$invoItem->course_id])){
                                foreach($refundCount[$invoItem->id] as $v){
                                    if($refundItemObj[$invoItem->course_id][$v]){
                                        $transaction->rollback();
                                        $this->addMessage('state', 'fail');
                                        $this->addMessage('message', Yii::t('message', '所选时间中,有已经退过的时间'));
                                        $this->showMessage();
                                    }
                                }
                            }

                        }else if(isset($refundType[$invoItem->id]) && $refundType[$invoItem->id] < 3){
                            if(isset($stat) && $stat[$invoItem->course_id] == 2){
                                $transaction->rollback();
                                $this->addMessage('state', 'fail');
                                $this->addMessage('message', Yii::t('message', '已经有退费，请选择特殊退费'));
                                $this->showMessage();
                            }
                        }

                        if(isset($refundMorry) && isset($refundMorry[$invoItem->id])){
                             if($invoItem->actual_total - $refundMorry[$invoItem->id] < 0 ){
                                 $transaction->rollback();
                                 $this->addMessage('state', 'fail');
                                 $this->addMessage('message', Yii::t('message', '退费钱数不能大于付款钱数'));
                                 $this->showMessage();
                             }
                        }
                        $model = new AsaRefund;


                        $model->setAttributes(array(
                            'site_id' => $this->branchId,
                            'childid' => $invoItem->asainvoice->childid,
                            'invoice_item_id' => $invoItem->id,
                            'program_id' => $invoItem->course_group_id,
                            'course_id' => $invoItem->course_id,
                            'refund_class_count' =>  count($refundCount[$invoItem->id]),
                            'refund_total_amount' => (isset($refundMorry) && isset($refundMorry[$invoItem->id])) ? $refundMorry[$invoItem->id] : $invoItem->unit_price * count($refundCount[$invoItem->id]),
                            'dropout_date' => strtotime($dropoutDate[$invoItem->id]),
                            'memo' => $memo,
                            'refund_reason' => $refundReason[$invoItem->id],
                            'refund_type' => $refundType[$invoItem->id],
                            'refund_method' => ($payType == 3) ? 3 : (($payType == 2) ? 1 : 2),
                            'payee_info' => ($invoItem->asainvoice->pay_type == 1) ?  CJSON::encode(array('银行' => $bank, '姓名' => $accountName, '账号' => $accountNumber)) : "",
                            'status' => 1,
                            'refund_files' => CJSON::encode($refundFiles[$invoItem->id]),
                            'updated' => time(),
                            'updated_by' => Yii::app()->user->id,
                        ));

                        if (!$model->save()) {
                            $transaction->rollback();
                            $error = current($model->getErrors());
                            $this->addMessage('state', 'fail');
                            $this->addMessage('message', Yii::t('message', $error[0]));
                            $this->showMessage();
                        }
                        $invoItem->refund_status = $model->id;
                        $invoItem->save();
                        if($model->refund_type == 3){
                            foreach($refundCount[$invoItem->id] as $refundItems) {
                                $refundItem = new AsaRefundItem();
                                $refundItem->refund_id = $model->id;
                                $refundItem->site_id = $this->branchId;
                                $refundItem->course_group_id = $model->program_id;
                                $refundItem->course_id = $model->course_id;
                                $refundItem->child_id = $model->childid;
                                $refundItem->schedule_id = $invoItem->asacourse->schedule_id;
                                $refundItem->schedule_item_id = $refundItems;
                                if(!$refundItem->save()){
                                    $error = current($refundItem->getErrors());
                                    $this->addMessage('state', 'fail');
                                    $this->addMessage('message', Yii::t('message', $error[0]));
                                    $this->showMessage();
                                }
                            }
                        }
                        } catch (Exception $e) {
                            $transaction->rollback();
                            $this->addMessage('state', 'fail');
                            $this->addMessage('message', Yii::t('message', '非法操作'));
                            $this->showMessage();
                        }
                    }
                $transaction->commit();
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message', '提交成功!'));
                $this->addMessage('callback', 'refundSuccess');
                $this->showMessage();
            }else{
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('message', '非法操作'));
                $this->showMessage();
            }
        }else{
            $this->addMessage('state', 'fail');
            $this->addMessage('message', Yii::t('message', '请选择课程或者账单'));
            $this->showMessage();
        }
    }

    //延时服务退费
    public function actionSaveRefundExtra()
    {
        $itemid = Yii::app()->request->getParam('itemid', array());
        $invoiceId = intval(Yii::app()->request->getParam('invoiceId', 0));
        $bank = Yii::app()->request->getParam('bank', "");                     //银行
        $accountName = Yii::app()->request->getParam('account_name', '');      // 开户名
        $accountNumber = Yii::app()->request->getParam('account_number', '');  //账号
        if($invoiceId){
            $invoice = AsaInvoice::model()->findByPk($invoiceId);
            if($invoice->pay_type == 1){
                if(empty($bank) || empty($accountName) || empty($accountNumber)){
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', Yii::t('message', '开户银行，开户名和卡号为必填项！'));
                    $this->showMessage();
                }
            }

            $refindItemIds = array();
            foreach($invoice->item as $items){
                $refindItemIds[] = $items->id;
            }

            $criteria = new CDbCriteria;
            $criteria->compare('invoice_item_id', $refindItemIds);
            $criteria->compare('status', "<6");
            $isRefundCount = AsaRefund::model()->findAll($criteria);

            if($isRefundCount){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('message', '账单下的课程有过退费，不可直接退全部账单!'));
                $this->showMessage();
            }

            $transaction = Yii::app()->subdb->beginTransaction();
            foreach ($invoice->item as $key => $invoItem) {
                try {
                    $model = new AsaRefund;
                    $model->setAttributes(array(
                        'site_id' => $this->branchId,
                        'childid' => $invoItem->asainvoice->childid,
                        'invoice_item_id' => $invoItem->id,
                        'program_id' => $invoItem->course_group_id,
                        'course_id' => $invoItem->course_id,
                        'refund_class_count' => $invoItem->asacourse->default_count,
                        'refund_total_amount' => $invoItem->actual_total,
                        'dropout_date' => strtotime($invoItem->asaInvoiceItemExtra->extra_date),
                        'refund_reason' => 10,
                        'refund_type' => 10,
                        'refund_method' => ($invoItem->asainvoice->pay_type == 2) ? 1 : 2,
                        'payee_info' => ($invoItem->asainvoice->pay_type == 1) ?  CJSON::encode(array('银行' => $bank, '姓名' => $accountName, '账号' => $accountNumber)) : "",
                        'status' => 1,
                        'refund_files' => "",
                        'updated' => time(),
                        'updated_by' => Yii::app()->user->id,
                    ));

                    if (!$model->save()) {
                        $transaction->rollback();
                        $error = current($model->getErrors());
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', Yii::t('message', $error[0]));
                        $this->showMessage();
                    }
                    $invoItem->refund_status = $model->id;
                    $invoItem->save();
                } catch (Exception $e) {
                    $transaction->rollback();
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', Yii::t('message', '非法操作'));
                    $this->showMessage();
                }
            }
            $transaction->commit();
            $this->addMessage('state', 'success');
            $this->addMessage('message', Yii::t('message', '提交成功!'));
            $this->addMessage('callback', 'refundSuccess');
            $this->showMessage();
        }else if($itemid){
            $invoiceItem = AsaInvoiceItem::model()->findAllByPk(array_keys($itemid));
            //这个账单的付款方式 如果是现金是否填写了银行卡信息
            $invoice = AsaInvoice::model()->findByPk($invoiceItem[0]->order_id);
            if($invoice->schoolid == $this->branchId){
                if($invoice->pay_type == 1){
                    if(empty($bank) || empty($accountName) || empty($accountNumber)){
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', Yii::t('message', '开户银行，开户名和卡号为必填项！'));
                        $this->showMessage();
                    }
                }

                //判断要退费的账单下额课程是否有正在退费中的账单
                $criteria = new CDbCriteria;
                $criteria->compare('invoice_item_id', array_keys($itemid));
                $criteria->compare('status', "<>6");
                $criteria->index = "id";
                $isreFund = AsaRefund::model()->count($criteria);

                if($isreFund){
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', Yii::t('message', '您选的课程有过退费'));
                    $this->showMessage();
                }

                $transaction = Yii::app()->subdb->beginTransaction();
                foreach ($invoiceItem as $key => $invoItem) {
                    try {
                        $model = new AsaRefund;
                        $model->setAttributes(array(
                            'site_id' => $this->branchId,
                            'childid' => $invoItem->asainvoice->childid,
                            'invoice_item_id' => $invoItem->id,
                            'program_id' => $invoItem->course_group_id,
                            'course_id' => $invoItem->course_id,
                            'refund_class_count' =>  1,
                            'refund_total_amount' => (isset($refundMorry) && isset($refundMorry[$invoItem->id])) ? $refundMorry[$invoItem->id] : $invoItem->actual_total,
                            'dropout_date' => strtotime($invoItem->asaInvoiceItemExtra->extra_date),
                            'refund_reason' => 10,
                            'refund_type' => 10,
                            'refund_method' => ($invoItem->asainvoice->pay_type == 2) ? 1 : 2,
                            'payee_info' => ($invoItem->asainvoice->pay_type == 1) ?  CJSON::encode(array('银行' => $bank, '姓名' => $accountName, '账号' => $accountNumber)) : "",
                            'status' => 1,
                            'refund_files' => "",
                            'updated' => time(),
                            'updated_by' => Yii::app()->user->id,
                        ));

                        if (!$model->save()) {
                            $transaction->rollback();
                            $error = current($model->getErrors());
                            $this->addMessage('state', 'fail');
                            $this->addMessage('message', Yii::t('message', $error[0]));
                            $this->showMessage();
                        }
                        $invoItem->refund_status = $model->id;
                        $invoItem->save();
                    } catch (Exception $e) {
                        $transaction->rollback();
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', Yii::t('message', '非法操作'));
                        $this->showMessage();
                    }
                }
                $transaction->commit();
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message', '提交成功!'));
                $this->addMessage('callback', 'refundSuccess');
                $this->showMessage();
            }else{
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('message', '非法操作'));
                $this->showMessage();
            }
        }else{
            $this->addMessage('state', 'fail');
            $this->addMessage('message', Yii::t('message', '请选择按账单退费或者安课程退费'));
            $this->showMessage();
        }
    }


    public function actionCamp(){
        $itemid = Yii::app()->request->getParam('itemid', array());
        $invoiceId = intval(Yii::app()->request->getParam('invoiceId', 0));
        $dropoutDate = Yii::app()->request->getParam('dropout_date', array()); //退费生效日期
        $bank = Yii::app()->request->getParam('bank', "");                     //银行
        $accountName = Yii::app()->request->getParam('account_name', '');      // 开户名
        $accountNumber = Yii::app()->request->getParam('account_number', '');  //账号
        if($itemid) {
            $refundAmount = Yii::app()->request->getParam('refundAmount', array());
            $refundType = Yii::app()->request->getParam('refund_type', array());
            $refundReason = Yii::app()->request->getParam('refund_reason', array());
            $memo = Yii::app()->request->getParam('memo', array());
            $invoiceItem = AsaInvoiceItem::model()->findAllByPk(array_keys($itemid));
            //这个账单的付款方式 如果是现金是否填写了银行卡信息

            $invoice = AsaInvoice::model()->findByPk($invoiceItem[0]->order_id);
            if($invoice->schoolid == $this->branchId) {
                if ($invoice->pay_type == 1) {
                    if (empty($bank) || empty($accountName) || empty($accountNumber)) {
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', Yii::t('message', '开户银行，开户名和卡号为必填项！'));
                        $this->showMessage();
                    }
                }

                //判断要退费的账单下额课程是否有正在退费中的账单
                $criteria = new CDbCriteria;
                $criteria->compare('invoice_item_id', array_keys($itemid));
                $criteria->compare('status', "<>6");
                $isreFund = AsaRefund::model()->findAll($criteria);
                $moneyNum = array();
                if ($isreFund) {
                    $statusArr = 0;
                    foreach($isreFund as $item){
                        $moneyNum[$item->course_id] += $item->refund_total_amount;
                        if($item->status < 5 ){
                            $statusArr = 1;
                        }
                    }
                    if($statusArr){
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', Yii::t('message', '您选的课程有正在退费'));
                        $this->showMessage();
                    }
                }

                $transaction = Yii::app()->subdb->beginTransaction();
                foreach ($invoiceItem as $key => $invoItem) {
                    try {
                        if($refundType[$invoItem->id] != 1 && empty($refundAmount[$invoItem->id])){
                            $transaction->rollback();
                            $this->addMessage('state', 'fail');
                            $this->addMessage('message', Yii::t('message', "课程金额不能为空"));
                            $this->showMessage();
                        }

                        if($refundType[$invoItem->id] != 3 && $isreFund){
                            $transaction->rollback();
                            $this->addMessage('state', 'fail');
                            $this->addMessage('message', Yii::t('message', "有过退费，只能选择特殊退费"));
                            $this->showMessage();
                        }
                        $amount = (isset($moneyNum) && isset($moneyNum[$invoItem->course_id])) ? $invoItem->actual_total - $moneyNum[$invoItem->course_id] : $invoItem->actual_total;

                        if($refundAmount[$invoItem->id] > $amount){
                            $transaction->rollback();
                            $this->addMessage('state', 'fail');
                            $this->addMessage('message', Yii::t('message', "退费金额不能大于课程金额"));
                            $this->showMessage();
                        }

                        $model = new AsaRefund;
                        $model->setAttributes(array(
                            'site_id' => $this->branchId,
                            'childid' => $invoItem->asainvoice->childid,
                            'invoice_item_id' => $invoItem->id,
                            'program_id' => $invoItem->course_group_id,
                            'course_id' => $invoItem->course_id,
                            'refund_class_count' => 1,
                            'refund_total_amount' => ($refundType[$invoItem->id] != 1) ? $refundAmount[$invoItem->id] : $invoItem->actual_total,
                            'dropout_date' => strtotime($dropoutDate[$invoItem->id]),
                            'refund_reason' => $refundReason[$invoItem->id],
                            'refund_type' => $refundType[$invoItem->id],
                            'memo' => $memo[$invoItem->id],
                            'refund_method' => ($invoItem->asainvoice->pay_type == 2) ? 1 : 2,
                            'payee_info' => ($invoItem->asainvoice->pay_type == 1) ? CJSON::encode(array('银行' => $bank, '姓名' => $accountName, '账号' => $accountNumber)) : "",
                            'status' => 1,
                            'refund_files' => "",
                            'updated' => time(),
                            'updated_by' => Yii::app()->user->id,
                        ));

                        if (!$model->save()) {
                            $transaction->rollback();
                            $error = current($model->getErrors());
                            $this->addMessage('state', 'fail');
                            $this->addMessage('message', Yii::t('message', $error[0]));
                            $this->showMessage();
                        }
                        $invoItem->refund_status = $model->id;
                        $invoItem->save();
                    } catch (Exception $e) {
                        $transaction->rollback();
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', Yii::t('message', '非法操作'));
                        $this->showMessage();
                    }
                }
                $transaction->commit();
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message', '提交成功!'));
                $this->addMessage('callback', 'refundSuccess');
                $this->showMessage();
            }
        }else if($invoiceId){
            $refundType = Yii::app()->request->getParam('refund_type',"");   //退费类型
            $refundReason = Yii::app()->request->getParam('refund_reason',"");   //退费类型
            $refundFiles = Yii::app()->request->getParam('refund_files', ""); //申请退费文件
            $memo = Yii::app()->request->getParam('memo', "");             //退费说明
            $this->actionSave(array(
                'invoiceId' => $invoiceId,
                'dropout_date' => $dropoutDate,
                'refund_type' => $refundType,
                'refund_files' => $refundFiles,
                'refund_reason' => $refundReason,
                'memo' => $memo,
                'bank' => $bank,
                'account_name' => $accountNumber,
                'account_number' => $accountName,
            ));
        }else{
            $this->addMessage('state', 'fail');
            $this->addMessage('message', Yii::t('message', '请选择退费的课程或者账单'));
            $this->showMessage();
        }
    }


    //文件上传
    public function actionRefundFiles()
    {
        $file = CUploadedFile::getInstanceByName('upload_file');
        $msg = '没有附件选中';
        $saveName = "";
        if ($file) {
            if ($file->size > 10*1024*1024) {
                $msg = '文件过大';
            } else{
                $needType = array('jpg','jpeg','png','pdf');
                if (!in_array($file->getExtensionName(), $needType)) {
                    $msg = '此文件类型不允许上传';
                }else{
                    $filePath = Yii::app()->params['xoopsVarPath'] . '/asa/refund/';
                    $ext = $file->getExtensionName();
                    $saveName = date("Ydm") . '_' . uniqid() . '.' . $ext;
                    if ($file->saveAs($filePath . $saveName)){
                        $msg = 'success';

                        $baseUrl = $this->createUrl('downloadsRefund', array('fileName'=>$saveName));
                        //$baseUrl = Yii::app()->params['xoopsVarPath'] . '/asa/refund/';
                    }else{
                        $msg = '文件上传失败';
                    }
                }
            }
        }
        echo CJSON::encode(array(
            'url' => $baseUrl,
            'saveName' => $saveName,
            'msg' => $msg,
        ));
    }

    // 加载退费列表里的模态框， 根据状态来显示
    public function actionShowRefund()
    {

        $reFindId = intval(Yii::app()->request->getParam('id',0));
        if($reFindId){
            $model = AsaRefund::model()->findByPk($reFindId);
            $reFindList = CommonUtils::LoadConfig('CfgASA');
            $refundReason = array();
            foreach($reFindList['refundReason'] as $k=>$_type){
                $refundReason[$k] = (Yii::app()->language == 'zh_cn') ? $_type['cn']: $_type['en'];
            }

            if($model->childid){
                $childModel = ChildProfileBasic::model()->findByPk($model->childid);
                $childClass = IvyClass::model()->findByPk($childModel->classid);
            }
            $type = array();
            foreach($reFindList['reFundType'] as $k=>$_type){
                $type[$k] = (Yii::app()->language == 'zh_cn') ? $_type['cn']: $_type['en'];
            }

            $exchengeRefund = array();
            $criteria = new CDbCriteria;
            $criteria->compare('link_id', $model->id);
            $courseExchengeObj = AsaCourseExchange::model()->findAll($criteria);

            if($courseExchengeObj){
                $criteria = new CDbCriteria;
                $criteria->compare('invoice_item_id', $model->invoice_item_id);
                $criteria->compare('status', "5");
                $criteria->compare('refund_type', ">4");
                $refundCount = AsaRefund::model()->count($criteria);

                foreach($courseExchengeObj as $item){
                    if($item->difference < 0){
                        $exchengeRefund = array(
                            'oldCourseTitle' => $item->asaOldCourse->getTitle(), // 旧课程名称
                            'oldCourse' => $model->asainvoiceitem->unit_price . " * " . $model->asainvoiceitem->class_count, // 旧课程的课时和单价
                            'oldCourseNum' => $item->old_course_num, // 旧课程上过的课程
                            'oldRefundCount' => $refundCount, // 旧课程退费的课时, 有则显示, 无则显示为0
                            'oldAmount' => $model->asainvoiceitem->unit_price . " * " . ($model->asainvoiceitem->class_count - $item->old_course_num - $refundCount), // 旧课程所剩余的钱数

                            'newCourse' => $item->asaNewCourse->unit_price . " * " . $item->asaNewCourse->default_count, // 新课程所需要的钱数
                            'newCourseNum' => $item->asaNewCourse->unit_price . ' * ' . $item->new_course_num, // 新课程要上的课程
                            'newCourseTitle' => $item->asaNewCourse, // 新课程信息
                            'difference' => $item->difference, //差额
                        );
                    }
                }
            }

            if($model->status == 2){

                if($model->dropout_date)$model->dropout_date = date('Y-m-d', $model->dropout_date);
                $this->renderPartial('_updateRefund', array(
                    'model' => $model,
                    'type' => $type,
                    'refundReason' => $refundReason,
                    'childsName' => $childModel,
                    'childClass' => $childClass,
                    'exchengeRefund' => $exchengeRefund,
                ));
            }else{

                $uid = array(
                    $model->updated_by,
                    $model->updated_confirm_by,
                    $model->updated_done_by,
                );

                $criteria = new CDbCriteria;
                $criteria->compare('uid', $uid);
                $criteria->index = 'uid';
                $updatedName = User::model()->findAll($criteria);

                // 是否有审核权限
                $schoolid = $this->branchId;
                $uid = Yii::app()->user->id;
                $mangerType = array(AdmBranchLink::ADM_TYPE_ASA, AdmBranchLink::ADM_TYPE_CD);
                $access = AdmBranchLink::checkAccess($schoolid, $uid, $mangerType);
                // 判断退费类型
                // if ($model->refund_method != 1) {
                //     $access = false;
                // }
                
                $this->renderPartial('_showRefund', array(
                    'model' => $model,
                    'type' => $type,
                    'reFindType' => $type,
                    'updatedName' => $updatedName,
                    'refundReason' => $refundReason,
                    'childsName' => $childModel,
                    'childClass' => $childClass,
                    'reFindList' => $reFindList,
                    'exchengeRefund' => $exchengeRefund,
                    'access' => $access,
                ));
            }
        }
    }

    //退费列表->缺少资料->修改操作
    public function actionUpdateRefund()
    {
        $id = intval(Yii::app()->request->getParam('id',0));
        $payeeInfo = Yii::app()->request->getParam('payeeInfo',array());
        $refundFiles = Yii::app()->request->getParam('refund_files', array());
        $reFind = AsaRefund::model()->findByPk($id);
        if($reFind && $reFind->site_id == $this->branchId){
            if($reFind->refund_method == 2){
                if(count(array_filter($payeeInfo)) != 3){
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', Yii::t('message','银行,开户人和账号为必填项!'));
                    $this->showMessage();
                }
            }
            $invoiceItemModel = AsaInvoiceItem::model()->findByPk($reFind->invoice_item_id);


            $reFind->attributes = $_POST['AsaRefund'];
            $reFind->dropout_date = strtotime($reFind->dropout_date);
            $reFind->refund_total_amount = $reFind->refund_class_count  *  $invoiceItemModel->unit_price;
            $reFind->payee_info = CJSON::encode($payeeInfo);
            $reFind->refund_files = ($refundFiles) ? CJSON::encode($refundFiles) : "";
            $reFind->status = ($reFind->status) ? $reFind->status : 2;
            $reFind->updated = time();
            $reFind->updated_by = Yii::app()->user->id;

            $criteria = new CDbCriteria;
            $criteria->compare('invoice_item_id', $reFind->course_id);
            $criteria->compare('status', 5);
            $reFundNum = AsaRefund::model()->findAll($criteria);
            $courseRefundNum = 0;
            foreach($reFundNum as $num){
                $courseRefundNum += $num->refund_class_count;
            }

            if($reFind->asaCourse->default_count < $reFind->refund_class_count + $courseRefundNum){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('message','退费节数不足'));
                $this->showMessage();
            }


            if($reFind->save()){
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message','success'));
                $this->addMessage('callback', 'cbUpdateRefind');
                $this->showMessage();
            }else{
                $error = current($reFind->getErrors());
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('message', $error[0]));
                $this->showMessage();
            }
        }else{
            $this->addMessage('state', 'fail');
            $this->addMessage('message', Yii::t('message', "非法操作"));
            $this->showMessage();
        }
    }

    // 现金交接查看图片
    public function actionDownloads()
    {
        ob_end_clean();
        $id = intval(Yii::app()->request->getParam('id', 0));
        $model= AsaCashHandover::model()->findByPk($id);
        if($model){
            $fileNames = $model->bank_ref_url;
            $file_dir = Yii::app()->params['OAUploadBasePath'] . '/handover/';
            $fileres = file_get_contents($file_dir . $fileNames);
            header('Content-type: image/jpeg');
            echo $fileres;
        }
    }


    //退费中的所有查看图片
    public function actionDownloadsRefund()
    {
        ob_end_clean();
        $fileName = Yii::app()->request->getParam('fileName', "");
        $extension = substr(strrchr($fileName, '.'), 1);
        $file_dir = Yii::app()->params['xoopsVarPath'] . '/asa/refund/';

        $fileres = file_get_contents($file_dir . $fileName);
        if($extension == "pdf"){
            header('Content-type: application/pdf');
        }else{
            header('Content-type: image/jpeg');
        }
        echo $fileres;
    }

    //删除缺少资料的信息
    public function actionDelRefund()
    {
        $reFindId = intval(Yii::app()->request->getParam('id',0));

        if (Yii::app()->request->isPostRequest) {
            if($reFindId){
                $cashHandover = AsaRefund::model()->findByPk($reFindId);
                if($cashHandover->site_id == $this->branchId){
                    /*$picPath = Yii::app()->params['xoopsVarPath'] . '/staff/';
                    unlink($picPath . $cashHandover->refund_files);*/
                    $cashHandover->status = 6;

                    if($cashHandover->save()){
                       $criteria = new CDbCriteria;
                        $criteria->compare('invoice_item_id', $cashHandover->invoice_item_id);
                        $criteria->compare('status', array(1,2,3,4,5));
                        $countCashHandover = AsaRefund::model()->count($criteria);
                        if(empty($countCashHandover)){
                            $invoiceItemRefund = AsaInvoiceItem::model()->findByPK($cashHandover->invoice_item_id);
                            if($invoiceItemRefund){
                                $invoiceItemRefund->refund_status = 0;
                                $invoiceItemRefund->save();
                            }
                        }

                        $this->addMessage('state', 'success');
                        $this->addMessage('message', Yii::t('message','success'));
                        $this->addMessage('callback', 'cbDelRefind');
                        $this->showMessage();
                    }else{
                        $error = current($cashHandover->getErrors());
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', Yii::t('message', $error[0]));
                        $this->showMessage();
                    }
                }else{
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', Yii::t('message', '请选择所在校园的项目'));
                    $this->showMessage();
                }
            }
        }
    }

    //删除图片文件
    public function actionDelFiles()
    {
        if (Yii::app()->request->isPostRequest){
            $fileName = Yii::app()->request->getParam('fileName', '');
            $filePath = Yii::app()->params['xoopsVarPath'] . '/asa/refund/';
            unlink($filePath . $fileName);
            $this->addMessage('state', 'success');
            $this->addMessage('message', Yii::t('message','success'));
            $this->showMessage();

        }
    }

    //  补全资料地方的删除照片
    public function actionDelReFundFiles()
    {
        if (Yii::app()->request->isPostRequest){
            $id = intval(Yii::app()->request->getParam('id', 0));
            $fileName = Yii::app()->request->getParam('fileName', '');
            $reFund = AsaRefund::model()->findByPk($id);
            if($reFund && $reFund->site_id == $this->branchId){
                if(in_array($fileName,CJSON::decode($reFund->refund_files))){
                    $fileNames = array_diff(CJSON::decode($reFund->refund_files),array($fileName));
                    $reFund->refund_files = CJSON::encode($fileNames);
                    if($reFund->save()){
                        $filePath = Yii::app()->params['xoopsVarPath'] . '/asa/refund/';
                        unlink($filePath . $fileName);
                        $this->addMessage('state', 'success');
                        $this->addMessage('message', Yii::t('message','success'));
                        $this->showMessage();
                    }
                }else{
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', Yii::t('message','非法操作'));
                    $this->showMessage();
                }
            }else{
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('message','非法操作'));
                $this->showMessage();
            }

        }
    }

    // 退费审核
    public function actionRefundreview()
    {
        // 权限判断
        $schoolid = $this->branchId;
        $uid = Yii::app()->user->id;
        $type = array(AdmBranchLink::ADM_TYPE_ASA, AdmBranchLink::ADM_TYPE_CD);
        $access = AdmBranchLink::checkAccess($schoolid, $uid, $type);
        if (!$access) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', Yii::t('message','非法操作'));
            $this->showMessage();
        }
        // 退费操作
        if (Yii::app()->request->isPostRequest) {
            $status =  Yii::app()->request->getParam('status');
            $id =  Yii::app()->request->getParam('id', 0);
            $model = AsaRefund::model()->findByPk($id);
            if (!$model) {
                $this->addMessage('state', 'fail');
                $this->addMessage('callback', 'cb');
                $this->addMessage('message', Yii::t('message','未找到退费'));
                $this->showMessage();
            }
            if ($model->status != 1) {
                $this->addMessage('state', 'fail');
                $this->addMessage('callback', 'cb');
                $this->addMessage('message', Yii::t('message','退费状态有误'));
                $this->showMessage();
            }
            if (in_array($status, array(2, 4))) {
                // 驳回
                $model->status = $status;
                if ($status == 2) {
                    $model->updated = time();
                    $model->updated_by = Yii::app()->user->id;
                    $message = '驳回成功';
                }
                // 通过
                if ($status == 4) {
                    // 如果是特殊课程组，不能直接退
                    if (in_array($model->program_id, AsaCourseGroup::getNoRefundGroupIds())) {
                        $status = 3;
                    }
                    if ($model->refund_method != 1) {
                        $status = 3;
                    }
                    $model->status = $status;
                    $model->updated_confirm = time();
                    $model->updated_confirm_by = Yii::app()->user->id;
                    if ($status == 4) {
                        $model->updated_done = time();
                        $model->updated_done_by = Yii::app()->user->id;
                    }
                    $message = '提交成功';
                }
                if ($model->save()) {
                    // 如果是通过，发送MQ
                    if ($model->status == 4) {
                        Yii::import('common.components.AliYun.MQ.MQProducer');
                        if($model->refund_method == 1){
                            if($model->refund_total_amount > 0){
                                if (CommonUtils::isProduction()) {
                                    CommonUtils::addProducer(MQProducer::TAG_ASA, "WechatPay.wechatRefund", $model->id);
                                }
                            }else{
                                $model->status = 5;
                                $model->save();
                                CommonUtils::addProducer(MQProducer::TAG_ASA, "Invoice.releaseHoldSpot", CJSON::encode(array($model->course_id)));
                            }
                        }else{
                            CommonUtils::addProducer(MQProducer::TAG_ASA, "Invoice.releaseHoldSpot", CJSON::encode(array($model->course_id)));
                        }
                    }
                    $this->addMessage('state', 'success');
                    $this->addMessage('callback', 'cb');
                    $this->addMessage('message', Yii::t('message', $message));
                    $this->showMessage();
                } else {
                    $this->addMessage('state', 'fail');
                    $this->addMessage('callback', 'cb');
                    $this->addMessage('message', Yii::t('message', '保存失败'));
                    $this->showMessage();
                }
            }
        }
        $this->addMessage('state', 'fail');
        $this->addMessage('callback', 'cb');
        $this->addMessage('message', Yii::t('message','fail'));
        $this->showMessage();
    }

    public function getButton($data)
    {
        if($data->status == 2) {
            echo CHtml::link('编辑', array('showRefund', 'id' => $data->id, "branchId" => Yii::app()->controller->branchId), array('class' => 'J_modal btn btn-xs btn-info')) . ' ';
            echo CHtml::link('作废', array('delRefund', 'id' => $data->id), array('class' => 'J_ajax_del btn btn-xs btn-danger', 'data-msg' => "确认作废吗?"));
        }else{
            echo CHtml::link('查看', array('showRefund', 'id' => $data->id), array('class' => 'J_modal btn btn-xs btn-primary drop'));
        }
    }

    public function AttendMenu()
    {
        $mainMenu = array(
            array('label'=>Yii::t('','退费中'), 'url'=>array("/masa/refund/index")),
            array('label'=>Yii::t('','退费完成'), 'url'=>array("/masa/refund/refundOk")),
        );
        return $mainMenu;
    }

    public function getAmount($data)
    {
        if(in_array($data->refund_type, array(4,5) )){
            return "¥ " . $data->refund_total_amount . "( 换课退费 )";
        }else{
            return "¥ " . $data->asainvoiceitem->unit_price . " * " . $data->refund_class_count . " = " . $data->refund_total_amount;
        }
    }

    public function getChildName($data)
    {
        return $this->childsName[$data->childid]['child'] . "<br>" .  $this->childsName[$data->childid]['class'];
    }


    public function getCourseName($data)
    {
        return $this->courseName[$data->course_id]['title'] . "<br>" .  $this->courseName[$data->course_id]['schedule'];
    }

    public function getReason($data)
    {
        $reFindType = array();
        foreach($this->reFindList['refundReason'] as $k=>$v){
            $reFindType[$k] = (Yii::app()->language == 'zh_cn') ? $v['cn']: $v['en'];
        }
        return $reFindType[$data->refund_reason];
    }

    public function getMethod($data)
    {
        $config = CommonUtils::LoadConfig('CfgASA');
        if($data->refund_method){
            switch (Yii::app()->language) {
                case "zh_cn":
                    return ($data->openid) ? $config['refundMethod'][$data->refund_method]['cn'] . "( 自主退费 )"  : $config['refundMethod'][$data->refund_method]['cn'];
                    break;
                case "en_us":
                    return ($data->openid) ? $config['refundMethod'][$data->refund_method]['en'] . "( 自主退费 )"  : $config['refundMethod'][$data->refund_method]['en'];
                    break;
            }
        }
    }
}