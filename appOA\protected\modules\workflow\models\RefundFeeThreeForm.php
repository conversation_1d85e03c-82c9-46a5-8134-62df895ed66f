<?php

/* 
 * 退费表单
 */
class RefundFeeThreeForm extends CFormModel
{
    public $startdate;
    public $enddate;
    public $amount;
    public $invoice_id;
    public $title;
    public function rules(){
        return array(
			array('title, startdate, enddate, amount,invoice_id', 'required'),
            array('startdate, enddate, amount,invoice_id', 'numerical', 'integerOnly'=>false),
            array('amount','numerical','min'=>0.01,'tooSmall'=>Yii::t("workflow", "退费金额大于 [ 0 ]"))
		);
    }
    
    public function attributeLabels(){
        return array(
			'startdate'=>Yii::t("workflow", "退费开始日期"),
			'enddate'=>Yii::t("workflow", "退费结束日期"),
			'amount'=>Yii::t("workflow", "退费金额"),
		);
    }
    
    
}
