<?php

class MoperationModule extends CWebModule
{
	public function init()
	{
		// this method is called when the module is being created
		// you may place code here to customize the module or the application

		// import the module-level models and components
		$this->setImport(array(
			'moperation.models.*',
			'moperation.components.*',
		));
	}

	public function beforeControllerAction($controller, $action)
	{
		if(parent::beforeControllerAction($controller, $action))
		{
			// this method is called before any module controller action is performed
			// you may place customized code here
			return true;
		}
		else
			return false;
	}

    public function getMenu()
    {
        $action = Yii::app()->urlManager->parseUrl(Yii::app()->request);

        $mainMenu = array(
            array('label'=>Yii::t('asa','增加分类模板'), 'url'=>array("/masa/vendor/index")),
            array('label'=>Yii::t('asa','Program Management'), 'url'=>array("/masa/course/index")),
            array('label'=>Yii::t('asa','Staff Attendance'), 'url'=>array("/masa/attend/index"), 'active'=>in_array($action, array('masa/attend/index', 'masa/attend/otherStaffs', 'masa/attend/report', 'masa/attend/statistics'))),
        );
        return $mainMenu;
    }

    public function getHomeMenu()
    {
        $action = Yii::app()->urlManager->parseUrl(Yii::app()->request);

        $mainMenu = array(
            array('label'=>Yii::t('asa','校园自查报告'), 'url'=>array("/moperation/security/showSafety")),
            array('label'=>Yii::t('asa','自查项目配置'), 'url'=>array("/moperation/security/updateConfiguration")),
            array('label'=>Yii::t('asa','校园事故报告'), 'url'=>array("/moperation/security/accidentTrack")),
            array('label'=>Yii::t('asa','月累计事故及同期比较'), 'url'=>array("/moperation/security/comparisonAccident")),
        );
        return $mainMenu;
    }
}
