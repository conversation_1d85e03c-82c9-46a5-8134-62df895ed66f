<?php
if(!defined("IVY_POLICY"))
    throw new CException('DO NOT USE THIS FILE DIRECTLY');

return array( 'policy'=>array(

    //是否老生老办法
    ENABLE_CONSTANT_TUITION => true,

    //年保育费
    //ANNUAL_CHILDCARE_AMOUNT => 8000,

    //是否开放课外活动账单
    ENABLE_AFTERSCHOOLS => true,

    //图书馆工本费
    FEETYPE_LIBCARD => 10,

    //学期四个时间点；取前后两端
    TIME_POINTS   => array(202309,202402,202403,202407),

    //年付开通哪些缴费类型
//    PAYGROUP_ANNUAL => array(
//        ITEMS => array(
//            FEETYPE_TUITION,
//            FEETYPE_LUNCH,
//            FEETYPE_SCHOOLBUS,
//        ),
//        TUITION_CALC_BASE => BY_MONTH,
//    ),
//
//    //学期付开通哪些缴费类型
   PAYGROUP_SEMESTER => array(
       ITEMS => array(
           FEETYPE_TUITION,
           FEETYPE_LUNCH,
        //    FEETYPE_SCHOOLBUS,
       ),
       TUITION_CALC_BASE => BY_MONTH,
   ),
//
//    PAYGROUP_MONTH => array(
//        ITEMS => array(
//            //FEETYPE_TUITION,
//            FEETYPE_LUNCH,
//            //FEETYPE_SCHOOLBUS,
//        ),
//        TUITION_CALC_BASE => BY_MONTH,
//    ),

    //计算基准：BY_MONTH, 或者 BY_SETTING, 前者表示所有的学费都基于月费用计算，后者表示所有的学费都基于学期或学年
    TUITION_CALCULATION_BASE => array(

        BY_MONTH => array(

            //月收费金额
            AMOUNT => array(
                FULL5D => 7500,
                HALF5D => 5799,
                HALF5D1 => 4000,
                // 亲子班
                // HALF5D1 => 780,
                // HALF5D2 => 1528,
                // HALF5D3 => 2895,
                // HALF5D4 => 790,
                // FULL5D2 => 6800,
                // HALF5D2 => 5399,
                //FULL3D => 4285,
            ),

            //收费月份，及其权重
            MONTH_FACTOR => array(
                202309=>1,
                202310=>1,
                202311=>1,
                202312=>1,
                202401=>1,
                202402=>0.6,
                202403=>1,
                202404=>1,
                202405=>1,
                202406=>1,
                202407=>1
            ),

            //折扣
            GLOBAL_DISCOUNT => array(
                DISCOUNT_ANNUAL => '1:10:2023-12-01:round',
                DISCOUNT_SEMESTER1 => '1:1:2024-02-28:round',
                DISCOUNT_SEMESTER2 => '1:1:2024-07-28:round'
            )

        ),

    ),

    //每餐费用
    LUNCH_PERDAY => 30,

    //账单到期日和账单开始日之差
    DUEDAYS_OFFSET => 1,

));