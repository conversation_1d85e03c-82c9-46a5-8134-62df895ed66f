<?php
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of Mims
 *
 * <AUTHOR>
 */
class Mims
{
    /**
    ** 只能在 ProtectedController 的派生类 中调用
    **/
    static function Nav($childs=null){
        $navigation = array(
            'portfolio'=>array(
                'label'=>Yii::t("navigations",'Portfolio'),
                'url'=>array('//child/profile/welcome'),
                'items'=>array(
                    'welcome'=>array(
                        'label'=>Yii::t("navigations", 'Welcome'),
                        'url'=>array('/child/profile/welcome'),
                        //'visible'=>false,
                    ),
                    'classlist'=>array(
                        'label'=>Yii::t("navigations", 'Classes'),
                        'url'=>array('//portfolio/portfolio/classes'),
                    ),
                    'journal'=>array(
                        'label'=>Yii::t("navigations", 'Weekly Journal'),
                        'url'=>array('//portfolio/portfolio/journal'),
                    ),
                    //'project'=>array(
                    //    'label'=>Yii::t("navigations", 'Project Report'),
                    //    'url'=>array('//portfolio/portfolio/project'),
                    //),
                    'semester'=>array(
                        'label'=>Yii::t("navigations", 'Semester Report'),
                        'url'=>array('//portfolio/portfolio/semester'),
                    ),
                    'progress'=>array(
                        'label'=>Yii::t("navigations", 'Progress Report'),
                        'url'=>array('//portfolio/portfolio/progress'),
                    ),
                    'project'=>array(
                        'label'=>Yii::t("navigations", 'SS Report'),
                        'url'=>array('//portfolio/portfolio/secondaryProgress'),
                    ),
                    //'projectview'=>array(
                    //    'label'=>Yii::t("navigations", 'Project Report View'),
                    //    'url'=>array('//portfolio/portfolio/projectView'),
                    //    'visible'=>false,
                    //),
                    'gallery'=>array(
                        'label'=>Yii::t("navigations", 'Photo Gallery'),
                        'url'=>array('//portfolio/portfolio/gallerylist'),
                    ),
                    'download'=>array(
                        'label'=>Yii::t("navigations", 'Download Photos'),
                        'url'=>array('//portfolio/portfolio/download'),
                    ),
                    'share'=>array(
                        'label'=>Yii::t("navigations", 'Share Portfolio'),
                        'url'=>array('/child/profile/share'),
                    ),
                    ),
                'itemOptions'=>array(
                    'class'=>'mainitem item-2'
                ),
                'submenuOptions'=>array(
                    'class'=>'subitem'
                )
            ),
            'profile' => array(
                'label'=>Yii::t("navigations", 'Profile'),
                'url'=>array('/child/profile/profile'),
                'items'=>array(
                    'profile'=>array(
                        'label'=>Yii::t("navigations", 'Edit Profile'),
                        'url'=>array('/child/profile/profile'),
                    ),
                    'misc'=>array(
                        'label'=>Yii::t("navigations", 'Misc Information'),
                        'url'=>array('/child/profile/misc'),
                    ),
                    'privacy'=>array(
                        'label'=>Yii::t("navigations", 'Privacy Settings'),
                        'url'=>array('/child/profile/privacy'),
                    ),
                ),
                'itemOptions'=>array(
                    'class'=>'mainitem item-1'
                ),
                'submenuOptions'=>array(
                    'class'=>'subitem'
                )
            ),
            'payment'=>array(
                'label'=>Yii::t("navigations", 'Payment'),
                'url'=>array('//child/payment/summary'),
                'items'=>array(
                    'summary'=>array(
                        'label'=>Yii::t("navigations", 'Summary'),
                        'url'=>array('//child/payment/summary'),
                    ),
                    'history'=>array(
                        'label'=>Yii::t("navigations", 'Payment History'),
                        'url'=>array('/child/payment/history'),
                    ),
                    'tuition_credit'=>array(
                        'label'=>Yii::t("navigations", 'Tuition Deposit'),
                        'url'=>array('/child/payment/tcredit'),
                    ),
                    'general_credit'=>array(
                        'label'=>Yii::t("navigations", 'General Credit'),
                        'url'=>array('/child/payment/credit'),
                    ),
                   'online'=>array(
                       'label'=>Yii::t("navigations", 'Make Payment'),
                       'url'=>array('/child/payment/online'),
                       //'visible'=>false,
                   ),
                    // 'points'=>array(
                    //     'label'=>Yii::t("navigations", 'Ivy Rewards Club'),
                    //     'url'=>array('/child/payment/points'),
                    // ),
                ),
                'itemOptions'=>array(
                    'class'=>'mainitem item-3'
                ),
                'submenuOptions'=>array(
                    'class'=>'subitem'
                )


            ),
            'resource'=>array(
                'label'=>Yii::t("navigations", 'Resources'),
                'url'=>array('//child/resource/index'),
                'items'=>array(
                    'index'=>array(
                        'label'=>Yii::t("navigations", 'Index'),
                        'url'=>array('//child/resource/index'),
                    ),
                    'library'=>array(
                        'label'=>Yii::t("navigations", 'Library'),
                        'url'=>array('//child/resource/library'),
                    ),
                    'handbook'=>array(
                        'label'=>Yii::t("navigations", 'Parent Handbook'),
                        'url'=>array('//child/resource/content','category'=>'handbook'),
                    ),
                    'newsletter'=>array(
                        'label'=>Yii::t("navigations", 'At Ivy and Beyond'),
                        'url'=>array('//child/resource/content','category'=>'newsletter'),
                    ),
                    'mi'=>array(
                        'label'=>Yii::t("navigations", 'Multiple Intelligences'),
                        'url'=>array('//child/resource/content','category'=>'mi'),
                    ),
//                    'songs'=>array(
//                        'label'=>Yii::t("navigations", 'Children Songs'),
//                        'url'=>array('//child/resource/content','category'=>'songs'),
//                    ),
                    'newsongs'=>array(
                        'label'=>Yii::t("navigations", 'Children Songs'),
                        'url'=>array('//child/resource/content','category'=>'newsongs'),
                    ),
                    'graduate'=>array(
                        'label'=>Yii::t("navigations", 'Ivy Graduates'),
                        'url'=>array('//child/resource/content','category'=>'graduate'),
                    ),
                    'prepare'=>array(
                        'label'=>Yii::t("navigations", 'Getting Ready'),
                        'url'=>array('//child/resource/content','category'=>'prepare'),
                    ),
                ),
                'itemOptions'=>array(
                    'class'=>'mainitem item-4'
                ),
                'submenuOptions'=>array(
                    'class'=>'subitem'
                )
            ),
            'middle'=>array(
                'label'=>Yii::t("navigations", 'Information'),
                'url'=>array('//child/middle/speed', 'id'=>10),
                'items'=>array(
					/*'schoolpdates'=>array(
                        'label'=>Yii::t("navigations", 'School Updates'),
                        'url'=>array('//child/middle/speed', 'id'=>10),
                    ),
                    'minutes'=>array(
                        'label'=>Yii::t("navigations", 'Coffee Talk Minutes'),
                        'url'=>array('//child/middle/speed', 'id'=>20),
                    ),
                    'corner'=>array(
                        'label'=>Yii::t("navigations", 'IB Corner'),
                        'url'=>array('//child/middle/speed/', 'id'=>30),
                    ),
                    'resources'=>array(
                        'label'=>Yii::t("navigations", 'Parent Resources'),
                        'url'=>array('//child/middle/speed/', 'id'=>40),
                    ),

                   'speed'=>array(
							'label'=>Yii::t("navigations", 'ES/MS Update'),
							'url'=>array('//child/middle/speed'),
                    ),
                    'newstaff'=>array(
                        'label'=>Yii::t("navigations", 'New Teachers and Staff'),
                        'url'=>array('//child/middle/newstaff'),
                    ),
                    'summary'=>array(
                        'label'=>Yii::t("navigations", 'Meeting Minutes'),
                        'url'=>array('//child/middle/summary'),
                    ),
                    'video'=>array(
                        'label'=>Yii::t("navigations", 'New Facility'),
                        'url'=>array('//child/middle/video'),
                    ),
                    'construction'=>array(
                        'label'=>Yii::t("navigations", 'Construction Safety Panel'),
                        'url'=>array('//child/middle/construction'),
                    ),
                    'schedule'=>array(
                        'label'=>Yii::t("navigations", 'Construction Schedule'),
                        'url'=>array('//child/middle/schedule'),
                    ),
                    'pypintro'=>array(
                        'label'=>Yii::t("navigations", 'IB Corner'),
                        'url'=>array('//child/middle/pypIntro'),
                    ),
                    'daystarDevelopment'=>array(
                        'label'=>Yii::t("navigations", 'Three Year Plan'),
                        'url'=>array('//child/middle/daystarDevelopment'),
                    ),
                    'supportingEnglish'=>array(
                        'label'=>Yii::t("navigations", 'Parent Resources'),
                        'url'=>array('//child/middle/supportingEnglish'),
                    ),                    
                    'welcomeBack'=>array(
                        'label'=>Yii::t("navigations", 'Arrival & Dismissal Procedures'),
                        'url'=>array('//child/middle/welcomeBack'),
                    ),
                    '(HOS)CoffeeTalkMemo'=>array(
                        'label'=>Yii::t("navigations", 'Coffee Talk Memo'),
                        'url'=>array('//child/middle/coffeeTalkMemo'),
                    ),*/
                ),
                'itemOptions'=>array(
                    'class'=>'mainitem item-4'
                ),
                'submenuOptions'=>array(
                    'class'=>'subitem'
                )
            ),
            'support'=>array(
                'label'=>Yii::t("navigations", 'Support'),
                'url'=>array('/child/support/campus'),
                'items'=>array(
                    'campus'=>array(
                        'label'=>Yii::t("navigations", 'Contact Campus'),
                        'url'=>array('//child/support/campus'),
                    ),
//                    'lunch'=>array(
//                        'label'=>Yii::t("navigations", 'Cancel Lunch'),
//                        'url'=>array('/child/support/lunch'),
//                    ),
//                    'pta'=>array(
//                        'label'=>Yii::t("navigations",'Parent Teacher Association'),
//                        'url'=>array('//child/support/pta'),
//                    ),
                    'wechat'=>array(
                        'label'=>Yii::t("navigations", 'Wechat Support'),
                        'url'=>array('//child/support/wechat'),
                    ),
                    'email'=>array(
                        'label'=>Yii::t("navigations", 'Email Support'),
                        'url'=>array('//child/support/email'),
                    ),
//                    'ptc'=>array(
//                        'label'=>Yii::t("navigations", 'Reserve PTC Slot'),
//                        'url'=>array('//child/support/ptc'),
//                    )
                ),
                'itemOptions'=>array(
                    'class'=>'mainitem item-5'
                ),
                'submenuOptions'=>array(
                    'class'=>'subitem'
                )
            )
        );

        //根据学校配置隐藏菜单
        $CfgBranch = Mims::LoadConfig('CfgBranch');
        $_CampusId = Yii::app()->getController()->getSchoolId();
        $_classId = (Yii::app()->request->getParam('classid',0)) ? Yii::app()->request->getParam('classid') : Yii::app()->getController()->getClassId();

        //重要信息目录获取
        $_CampusId = Yii::app()->getController()->getSchoolId();

        Yii::import('common.models.attendance.*');
        $criteria = new CDbCriteria;
        $criteria->compare('schoolid', $_CampusId);
        $criteria->compare('pid', 0);
        $inforObj = InformationFrame::model()->findAll($criteria);

        if($inforObj){
            foreach($inforObj as $k=>$item){
                $navigation['middle']['label'] = Yii::t("navigations", 'Information');
                if($k < 1){
                    $navigation['middle']['url'] = array('//child/middle/speed', 'id'=>$item->id);
                }
                $navigation['middle']['items'][] = array(
                    'label'=> (Yii::app()->language == 'zh_cn') ? $item->title_cn : $item->title_en ,
                    'url'=>array('//child/middle/speed', 'id'=>$item->id),
                );
            }
        }

        //检查图书馆
        //$library_auth = $CfgBranch['library_auth'];
        //if (is_null($_CampusId) || !isset($library_auth[$_CampusId]) || false === $library_auth[$_CampusId]) {
        //    unset($navigation['resource']['items']['library']);
        //}
        //检查是否可下载
        // $download_auth = $CfgBranch['download_auth'];
        // if (is_null($_CampusId) || !isset($download_auth[$_CampusId]) || false === $download_auth[$_CampusId]) {
        //     unset($navigation['portfolio']['items']['download']);
        // }
        if(!is_null($_CampusId) && isset($CfgBranch['hidePTA'][$_CampusId]) && true === $CfgBranch['hidePTA'][$_CampusId] ){
            unset($navigation['support']['items']['pta']);
        }
        if(!is_null($_CampusId) && isset($CfgBranch['showPTC']) && !in_array($_CampusId, $CfgBranch['showPTC']) ){
            unset($navigation['support']['items']['ptc']);
        }

        //if(!in_array(Yii::app()->getController()->getChildId(), array(3954, 1392, 2815, 3923)) ){
        //    unset($navigation['payment']['items']['points']);
        //}
        //根据用户隐藏菜单
        if (!$_classId || CommonUtils::isGradeSchool($_classId, true)){
            Yii::import('common.models.portfolio.SReport');
            $criteria = new CDbCriteria();
            $criteria->compare('childid', Yii::app()->getController()->getChildId());
            $criteria->compare('stat', 20);
            $rCount = SReport::model()->count($criteria);
            if (!$rCount) {
                unset($navigation['portfolio']['items']['semester']);
            }
        }else{
            unset($navigation['portfolio']['items']['progress']);
        }
        if(Mims::unIvy() || in_array($_CampusId, array('BJ_TT'))){
            unset($navigation['payment']['items']['points']);
        }

        if(Mims::unIvy()){
            unset($navigation['resource']['items']['newsletter']);
            unset($navigation['resource']['items']['handbook']);
            unset($navigation['resource']['items']['graduate']);
            unset($navigation['resource']['items']['prepare']);
        }

        if(Yii::app()->params['ownerConfig']['key'] == 'BJ_DS'){
            unset($navigation['resource']);
            unset($navigation['support']['items']['lunch']);
        }
        
        $userRole = Yii::app()->getController()->getRole();
        if(!empty($userRole)){
            switch ($userRole) {
                case "staff":
                break;

                case "parent":

                break;

                case "visitor":
                    unset($navigation['profile']['items']);
                    unset($navigation['payment']['items']);
                break;

            }
            return $navigation;
        }
    }

    static function getShortCuts(){
        $navs = Mims::Nav();
        $shortCuts = array(
            $navs['portfolio']['items']['journal'],
            $navs['portfolio']['items']['gallery'],
            $navs['portfolio']['items']['share'],
            $navs['profile']['items']['profile'],
            isset($navs['support']['items']['lunch']) ? $navs['support']['items']['lunch'] : '',
            $navs['support']['items']['email'],
        );
        return $shortCuts;
    }

    static function sendMail($emails, $subject,$message){
        $mailer = Yii::createComponent('application.extensions.mailer.EMailer');
        //$mailer->IsSendmail();
		/*
        $mailer->IsSMTP();
        $mailer->SMTPAuth=true;
        $mailer->SMTPSecure = 'ssl';
        $mailer->Host = 'smtp.gmail.com';
        $mailer->Port = 465;
        $mailer->Username = '****@gmail.com';
        $mailer->Password = '****';
		*/

        /*
        $mailer->IsSMTP();
        $mailer->SMTPAuth=true;
        $mailer->Host = 'mail.ivygroup.cn';
        $mailer->Username = 'no-reply';
        $mailer->Password = 'barney';

		$mailer->Sender = '<EMAIL>';
        $mailer->From = '<EMAIL>';
        $mailer->FromName = 'IvyOnline';

        if(is_array($emails)){
            foreach($emails as $email)
                $mailer->AddAddress($email);
        }else{
            $mailer->AddAddress($emails);
        }
        $mailer->CharSet = 'UTF-8';
        */
        $mailer->iniAsTest();
        $mailer->Subject = $subject;
        $mailer->MsgHTML($message);
        $mailer->IsHTML(true);
        return $mailer->Send();
    }

    static function CreateUploadUrl($uploadSubPath){
        return Yii::app()->params['uploadBaseUrl'] . $uploadSubPath;
    }

    /**
     * 格式化日期
     * @param	Object	$timestamp	Description
     * @param	String	$dateWidth	'medium', 'short', 'long', null
     * @param	String	$timeWidth	'medium', 'short', 'long', null
     * @return	Object				Description
     */
    static function formatDateTime($timestamp, $dateWidth='medium', $timeWidth=null){
        if($timestamp)
        return Yii::app()->dateFormatter->formatDateTime($timestamp,$dateWidth, $timeWidth);
    }

    /**
     * Summary
     * @param	Object	$category	配置文件名 目录在app/comp/config下
     * @return	Object				返回
     * 使用方法 若文件名为 CfgTest.php
     * 	$ret = Mims::LoadConfig('CfgTest');
     */
    static function LoadConfig($category){
        $alias = 'application.components.config.'.$category;
        $config = require(Yii::getPathOfAlias($alias).'.php');
        return $config;
    }

    /**
     * Summary
     * @param	Object	$category	配置文件名 目录在app/comp/helper下
     * @return	Object				返回
     * 使用方法 若类名为 HTest
     * 		$helper = Mims::LoadHelper('HTest');
     *		$helper->display();
     */
    static function LoadHelper($func){
        $alias = 'application.components.helper.'.$func;
        $helper = Yii::createComponent($alias);
        return $helper;
    }

    static function genSecurityCode(){
        //$checkCode = $_SERVER['REMOTE_ADDR'];
        //
        //    $checkCode = substr( $checkCode, 0, strrpos( $checkCode, '.', 1 ) );
        //    $checkCode = substr( $checkCode, 0, strrpos( $checkCode, '.', 1 ) );

        $checkCode = 'MySecolithkey%&*';
        $checkCode .= @$_SERVER['HTTP_USER_AGENT'];
        $checkCode = md5( $checkCode );
        return $checkCode;
    }

    static function checkSecurity(){
        $checkCode = Mims::genSecurityCode();
        if ( !isset( $_SESSION['xos_http_SessionService']['securityCode'] ) || $_SESSION['xos_http_SessionService']['securityCode'] != $checkCode ){
            Yii::app()->user->logout();
            return false;
        }else{
            return true;
        }
    }

    static function useIS()
    {
        if ( isset(Yii::app()->params['useIS']) && Yii::app()->params['useIS'] === true ){
            return true;
        }
        else {
            return false;
        }
    }

    const RC_LUNCH_CANCEL_EXCEED_TIME = 20011;
    const RC_LUNCH_CANCEL_NONE_SCHOOLDAY = 20012;
    const RC_LUNCH_CANCEL_TARGET_UNPAID = 20013;
    const RC_LUNCH_CANCEL_ALREADY_FUNDED = 20014;

    static function getRequestLang($force=true){
        if($force){
            $lang = strtolower(Yii::app()->request->preferredLanguage);
            if(!in_array($lang, array("en_us","zh_cn"))){
                $lang = "en_us";
            }
        }else{
            $lang = Yii::app()->request->preferredLanguage;
        }
        return $lang;
    }

    static function setLangCookie($lang){
        $lang = strtolower(trim($lang));
		$cookiename = "child_mims_lang";
		if(!in_array($lang, array("zh_cn","en_us"))){
            $lang = 'en_us';
		}

        unset(Yii::app()->request->cookies[$cookiename]);
        $cookie = new CHttpCookie($cookiename, $lang);
        $cookie->expire = time()+31104000;
        Yii::app()->request->cookies[$cookiename]=$cookie;
        return $lang;
    }

    static function isProduction(){
        if ( strtolower(Yii::app()->params['productionDomain']) == strtolower($_SERVER['HTTP_HOST']) ) {
            return true;
        }
       else {
           return false;
       }
    }

    static function lunchCancel($childId, $targetDate, $userid){
		Yii::import('common.components.policy.*');
		Yii::import('common.models.invoice.*');
		Yii::import('common.models.calendar.*');
		return PolicyApi::cancelChildLunchByDate($childId, $targetDate, $userid);
	}

	static function lunchRecover($childId, $targetDate, $userid){
		Yii::import('common.components.policy.*');
		Yii::import('common.models.invoice.*');
		Yii::import('common.models.calendar.*');
		return PolicyApi::recoverChildLunchByDate($childId, $targetDate, $userid);
	}

    static function sendLunchEmail($child, $targetDate, $action){
		$campus = Branch::model()->with('info')->findByPk($child->schoolid);
		$mailer = Yii::createComponent('common.extensions.mailer.EMailer');
        if (Yii::app()->language === 'zh_cn'){
            $actTitle = ($action=='cancel') ? '取消用餐' : '恢复用餐';
            $mailer->Subject = sprintf(Mims::unIvy()?'【 '.Yii::app()->params['ownerConfig']['name_cn'].' - %s】孩子信息：%s（%s）':'【艾毅在线助手 - %s】孩子信息：%s（%s）', $campus->abb, $child->getChildName(), $actTitle);
        }else{
            $actTitle = ($action=='cancel') ? 'Cancel Lunch' : 'Recover Lunch';
            $mailer->Subject = sprintf(Mims::unIvy()?'【 '.Yii::app()->params['ownerConfig']['name_en'].' - %s】child：%s（%s）':'[IvyOnline - %s] Child: %s (%s)' , $campus->abb, $child->getChildName(), $actTitle);
        }
		$mailer->AddAddress($campus->info->support_email);
		$mailer->AddReplyTo($campus->info->support_email);
        $pEmails = array();
		$pids = array();
		if($child->fid) $pids[] = $child->fid;
		if($child->mid) $pids[] = $child->mid;
		$parents = User::model()->findAllByPk($pids, '`level`>:level', array(':level'=>0));
		if(!empty($parents)){
			foreach($parents as $parent){
				$pEmails[] = $parent->email;
			}
		}
		foreach($pEmails as $_pe){
			$mailer->AddCC($_pe);
		}

		$mailer->iniMail(self::isProduction()); // 此行代码要放到AddAddress, AddCC方法下面
		$mailer->getView('lunchCancel', array(
				'child'=>$child,
				'targetDate'=>date('Y-m-d', strtotime($targetDate)),
				'action'=>$action,
				'supportEmail'=> $campus->info->support_email,
				'otherinfo' => '',//implode(',',$pEmails)
			), 'wechat');
		return $mailer->Send();
	}

    static function unIvy()
    {
        if(isset(Yii::app()->params['unIvy'])){
            return true;
        }
        else{
            return false;
        }
    }
}
