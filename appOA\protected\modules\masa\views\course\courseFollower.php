<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
    <h4 class="modal-title" id="exampleModalLabel"><?php echo $model->getTitle() ?></h4>
</div>

<div class="modal-body">
    <?php if(!$followers): ?>
        <h4 class="text-center">暂无数据</h4>
    <?php else: ?>
        <table class="table table-bordered">
            <tr>
                <th>编号</th>
                <th>学生</th>
                <th>班级</th>
                <th>父亲</th>
                <th>母亲</th>
                <th>父亲邮箱</th>
                <th>母亲邮箱</th>
            </tr>
            <?php $i=1;$email=''; foreach ($followers as $follower) : ?>
            <?php 
                $parents = $follower->child->getParents();
                $cParent = $follower->user->parent; 
                $father = $parents['father']->parent ? $parents['father']->parent : new IvyParent();
                $mother = $parents['mother']->parent ? $parents['mother']->parent : new IvyParent();
            ?>
            <tr>
                <td><?php echo $i; ?></td>
                <td><?php echo $follower->child->getChildName(); ?></td>
                <td><?php echo $follower->child->ivyclass->title; ?></td>
                <td><?php echo $father->getName() .' '. $father->mphone; ?> <?php if($father->pid == $cParent->pid) {echo '<span class="glyphicon glyphicon-ok"></span>';}?></td>
                <td><?php echo $mother->getName() .' '. $mother->mphone; ?> <?php if($mother->pid == $cParent->pid) {echo '<span class="glyphicon glyphicon-ok"></span>';}?></td>
                <td><?php echo $parents['father']->email; ?></td>
                <td><?php echo $parents['mother']->email; ?></td>
                <?php 
                    if ($parents['father']->email) {
                        $email .= $parents['father']->email . ';';
                    }                    
                    if ($parents['mother']->email) {
                        $email .= $parents['mother']->email . ';';
                    }
                ?>
            </tr>
            <?php $i++; endforeach;$email = trim($email, ';') ?>
        </table>
        <button class="btn btn-default email">复制邮件地址</button>
    <?php endif; ?>
</div>
<div class="modal-footer">
    <button type="button" class="btn btn-default"  data-dismiss="modal"><?php echo Yii::t('global','Cancel');?></button>
</div>

<script>
    var email = '<?php echo $email; ?>'
    var clipboard = new Clipboard('.email', {
        container: document.getElementById('modal'),
        text: function(trigger) {
            return email;
        }
    });
    clipboard.on('success', function(e) {
        resultTip({msg:'已复制到剪贴板'});
    });
    // clipboard.on('error', function(e) {
    //     console.log(e);
    // });
</script>