<?php

/**
 * This is the model class for table "ivy_groups_users_link".
 *
 * The followings are the available columns in table 'ivy_groups_users_link':
 * @property integer $linkid
 * @property integer $groupid
 * @property integer $uid
 */
class XoGroupsUsersLink extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @return XoGroupsUsersLink the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_groups_users_link';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('groupid, uid', 'numerical', 'integerOnly'=>true),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('linkid, groupid, uid', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'user'=>array(self::BELONGS_TO,'User','uid'),
            'profile'=>array(self::BELONGS_TO,'UserProfile','uid'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'linkid' => 'Linkid',
			'groupid' => 'Groupid',
			'uid' => 'Uid',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('linkid',$this->linkid);
		$criteria->compare('groupid',$this->groupid);
		$criteria->compare('uid',$this->uid);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
    
    public function addUserToGroup($groupId, $userId)
    {
        $criteria = new CDbCriteria();
        $criteria->compare('groupid', $groupId);
        $criteria->compare('uid', $userId);
        $count = $this->count($criteria);
        if (!$count){
            $model = new XoGroupsUsersLink;
            $model->groupid = $groupId;
            $model->uid = $userId;
            $model->save();
        }
        
        return true;
    }
    
    public function removeAllGroup($userId)
    {
        if ($userId){
            $criteria=new CDbCriteria;
            $criteria->compare('uid', $userId);
            $this->deleteAll($criteria);
        }
    }
}