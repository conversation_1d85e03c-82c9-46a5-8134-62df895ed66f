<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span>
    </button>
    <h4 class="modal-title" id="exampleModalLabel"><?php echo $invoiceTitle . "( " . $classTitle . " )"?></h4>
</div>
<?php
$form = $this->beginWidget('CActiveForm', array(
    'id' => 'course-forms',
    'enableAjaxValidation' => false,
    'htmlOptions' => array('class' => 'form-horizontal', 'role' => 'form'),
)); ?>
<div class="modal-body">
    <!--课程名字-->
    <table class="table table-bordered">
        <tr>
            <td><?php echo Yii::t('asa','课程名字'); ?></td>
            <td><?php echo Yii::t('asa','课程时间标题'); ?></td>
            <td><?php echo Yii::t('asa','课时 * 单价 = 总价'); ?></td>
            <td><?php echo Yii::t('asa','报名人数 / 总人数'); ?></td>
        </tr>
    <?php
        if($courseList){
        foreach($courseList as $key => $item){
            $courseNum = ($courseNumber[$key]) ? $courseNumber[$key] : 0 ;            ?>
            <tr>
                <td><?php echo $item['title'] ?></td>
                <td><?php echo $item['schedule'] ?></td>
                <td><?php echo $item['default_count'] . " * " . $item['unit_price'] . " = " . $item['actual_total']?></td>
                <td><?php echo $courseNum . '/' . $item['capacity'] ?></td>
            </tr>
    <?php }}?>
    </table>
</div>
<?php $this->endWidget(); ?>
