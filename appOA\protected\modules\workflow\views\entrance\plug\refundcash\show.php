<!-- Modal -->
<?php echo CHtml::form('/workflow/entrance/index', 'post',array('class'=>'J_ajaxForm'));?>
<div class="modal fade" id="<?php echo $data['modalNameId']?>" tabindex="-1" role="dialog" aria-labelledby="workflowModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel"><?php echo CommonUtils::autoLang($data['workflow']->definationObj->defination_name_cn,$data['workflow']->definationObj->defination_name_en);?> - <?php echo CommonUtils::autoLang($data['workflow']->nodeObj->node_name_cn,$data['workflow']->nodeObj->node_name_cn);?></h4>
            </div>
            <div class="modal-body" id="workflow-modal-body">
                <div class="media">
                    <div class="media-left media-middle">
                        <a href="<?php echo Yii::app()->controller->createUrl('/child/invoice/finance',array('t'=>'balance','childid'=>$data['childCreit']->childid));?>" target="_blank">
                            <?php echo CHtml::image(CommonUtils::childPhotoUrl($data['childCreit']->child->photo), '', array("width"=>80,'style'=>'padding-bottom:5px;'));?>
                        </a>
                    </div>
                    <div class="media-body">
                        <h4 class="media-heading"><a href="<?php echo Yii::app()->controller->createUrl('/child/invoice/finance',array('t'=>'balance','childid'=>$data['childCreit']->childid));?>" target="_blank"><?php echo $data['childCreit']->child->getChildName();?></a></h4>
                        <p>个户余额：<?php echo number_format($data['childCreit']->child->credit,2);?></p>
                        <p>提现金额：<?php echo number_format($data['childCreit']->amount,2);?></p>
                    </div>
                </div>
                <div class="retshow">
                    <h3><?php echo Yii::t('workflow', 'Comments:'); ?></h3>
                     <?php 
                        $processList = $data['workflow']->getWorkflowProcess();
                        foreach ($processList as $process):?>
                    <h4>
                        <em><?php echo Yii::t('workflow', 'Date submitted:');?>： <?php echo OA::formatDateTime($process->start_time);?></em>
                        <span><?php echo $process->user->getName();?></span>
                    </h4>
                    <div class="clearfix"></div>
                    <div class="comment">
                        <label><?php echo Yii::t('workflow', 'Option:'); ?></label>
                        <p><?php echo nl2br(htmlspecialchars($process->process_desc));?></p>
                    </div>
                    <?php endforeach;?>
                </div>
                <?php if ($data['workflow']->useRole && $data['workflow']->getOperateStatus() == WorkflowOperation::WORKFLOW_STATS_UNOP && !$data['workflow']->isFristNode()):?>
                <div class="form-group" style="padding-top:50px;">
                    <?php echo CHtml::textArea('memo','',array('class'=>'form-control','row'=>3));?>
                </div>
                <?php endif;?>
            </div>
            <?php 
            if ($data['workflow']->useRole && $data['workflow']->getOperateStatus() == WorkflowOperation::WORKFLOW_STATS_UNOP && !$data['workflow']->isFristNode()):?>
            <div class="modal-footer">
                <?php echo CHtml::hiddenField('definationId', $data['workflow']->getWorkflowId());?>
                <?php echo CHtml::hiddenField('nodeId', $data['workflow']->getCurrentNodeId());?>
                <?php echo CHtml::hiddenField('branchId', $data['workflow']->schoolId);?>
                <?php echo CHtml::hiddenField('operationId', $data['workflow']->getOperateId());?>
                <?php echo CHtml::hiddenField('action', 'show');?>
                <?php echo CHtml::hiddenField('buttonCategory','');?>
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('workflow', 'Cancel');?></button>
                <button type="button" class="btn btn-danger J_ajax_submit_btn" data-category="<?php echo WorkflowOperation::WORKFLOW_STATS_OPDENY;?>"><?php echo Yii::t('workflow', 'No');?></button>
                <button type="button" class="btn btn-primary J_ajax_submit_btn" data-category="<?php echo WorkflowOperation::WORKFLOW_STATS_OPED;?>">确定提现</button>
            </div>
            <?php endif;?>
        </div>
    </div>
</div>
<?php CHtml::endForm();?>