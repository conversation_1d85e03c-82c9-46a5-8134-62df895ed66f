<?php

/**
 * This is the model class for table "ivy_asa_refund".
 *
 * The followings are the available columns in table 'ivy_asa_refund':
 * @property integer $id
 * @property string $site_id
 * @property string $openid
 * @property integer $invoice_item_id
 * @property integer $program_id
 * @property integer $course_id
 * @property integer $refund_class_count
 * @property string $memo
 * @property integer $refund_reason
 * @property integer $refund_type
 * @property integer $dropout_date
 * @property integer $refund_total_amount
 * @property integer $refund_method
 * @property string $payee_info
 * @property integer $status
 * @property string $refund_files
 * @property string $receipt_no
 * @property string $updated
 * @property integer $updated_by
 * @property integer $childid
 * @property integer $updated_confirm
 * @property integer $updated_confirm_by
 * @property integer $updated_done
 * @property integer $updated_done_by
 */
class AsaRefund extends CActiveRecord
{
    const PROGRAM_ALL = 1;  // 全部退费 退班
    const PROGRAM_SECTION = 2; // 部分退费 退班
    const PROGRAM_SPECIAL = 3; // 特殊退费
    const PROGRAM_ACTIVE = 4; // 换课退费

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_asa_refund';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('invoice_item_id, program_id, course_id, refund_class_count, refund_reason, refund_type, dropout_date, refund_method, status, updated_by, childid, updated_confirm, updated_confirm_by, updated_done, updated_done_by, income_status, share_status', 'numerical', 'integerOnly'=>true),
			array('site_id, openid, refund_files, receipt_no, updated', 'length', 'max'=>255),
			array('memo, payee_info', 'safe'),
            array('refund_total_amount', 'numerical'),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, site_id, openid, invoice_item_id, program_id, updated_confirm, updated_confirm_by, updated_done, updated_done_by, course_id, refund_class_count, memo, refund_reason, refund_type, dropout_date, refund_total_amount, refund_method, payee_info, status, refund_files, receipt_no, updated, updated_by, childid', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'asaCourse' => array(self::BELONGS_TO, 'AsaCourse', array('course_id'=>'id')),
            'asaCourseGroup' => array(self::BELONGS_TO, 'AsaCourseGroup', array('program_id'=>'id')),
            'asainvoiceitem' => array(self::BELONGS_TO, 'AsaInvoiceItem', array('invoice_item_id'=>'id')),
            'asaRefunditem' => array(self::HAS_MANY, 'AsaRefundItem', array('refund_id'=>'id')),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'openid' => 'Openid',
            'site_id' => Yii::t("asa", "学校"),
            'childid' => Yii::t("asa", "孩子姓名"),
            'invoice_item_id' => Yii::t("asa", "账单关联表ID"),
            'program_id' => Yii::t("asa", "课程组"),
            'course_id' => Yii::t("asa", "账单名称"),
            'refund_class_count' => Yii::t("asa", "课时"),
            'refund_total_amount' => Yii::t("asa", "价格"),
            'dropout_date' => Yii::t("asa", "退费日期"),
            'memo' => Yii::t("asa", "退费介绍"),
            'refund_reason' => Yii::t("asa", "退费原因"),
            'refund_type' => Yii::t("asa", "退费类型"),
            'refund_method' => Yii::t("asa", "退费途径"),
            'payee_info' => Yii::t("asa", "银行信息"),
            'status' => Yii::t("asa", "退费状态"),
            'refund_files' => Yii::t("asa", "退费文件"),
            'receipt_no' => Yii::t("asa", "银行回单号"),
            'updated' => Yii::t("asa", "提交时间"),
            'updated_by' => Yii::t("asa", "退费人"),
            'updated_confirm'  => Yii::t("asa", "确定时间"),
            'updated_confirm_by'  => Yii::t("asa", "确定人"),
            'updated_done'  => Yii::t("asa", "退费完成时间"),
            'updated_done_by'  => Yii::t("asa", "退费人"),
            'income_status'  => Yii::t("asa", "用友收入处理状态"),
            'share_status'  => Yii::t("asa", "用友分摊处理状态"),
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('site_id',$this->site_id,true);
		$criteria->compare('openid',$this->openid,true);
		$criteria->compare('invoice_item_id',$this->invoice_item_id);
		$criteria->compare('program_id',$this->program_id);
		$criteria->compare('course_id',$this->course_id);
		$criteria->compare('refund_class_count',$this->refund_class_count);
		$criteria->compare('memo',$this->memo,true);
		$criteria->compare('refund_reason',$this->refund_reason);
		$criteria->compare('refund_type',$this->refund_type);
		$criteria->compare('dropout_date',$this->dropout_date);
		$criteria->compare('refund_total_amount',$this->refund_total_amount);
		$criteria->compare('refund_method',$this->refund_method);
		$criteria->compare('payee_info',$this->payee_info,true);
		$criteria->compare('status',$this->status);
		$criteria->compare('refund_files',$this->refund_files,true);
		$criteria->compare('receipt_no',$this->receipt_no,true);
		$criteria->compare('updated',$this->updated,true);
		$criteria->compare('updated_by',$this->updated_by);
		$criteria->compare('childid',$this->childid);
		$criteria->compare('updated_confirm',$this->updated_confirm);
		$criteria->compare('updated_confirm_by',$this->updated_confirm_by);
		$criteria->compare('updated_done',$this->updated_done);
		$criteria->compare('updated_done_by',$this->updated_done_by);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return AsaRefund the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	public static function refundMoery($childId, $courseId)
    {
        $refunds = array('refundAmount' => 0, 'refundMoney' => 0);
        $criteria=new CDbCriteria;
        $criteria->compare('childid', $childId);
        $criteria->compare('course_id', $courseId);
        $criteria->compare('refund_type', 3);
        $criteria->compare('status', 5);
        $refundObj = AsaRefund::model()->findAll($criteria);

        if($refundObj){
            $unit_price = "";
            $refund = array();
            foreach($refundObj as $item){
                $unit_price = $item->asainvoiceitem->unit_price;
                $refund[] = count($item->asaRefunditem);
            }

            if ($refund) {
                $refunds = array('refundAmount' => array_sum($refund), 'refundMoney' => $unit_price * array_sum($refund));
            }
        }
        return $refunds;
    }

    // 	根据支付状态判断退费类型
    public static function refundType($payType) {
    	$refund = array(
    		'1' => '2',
    		'2' => '1',
    		'3' => '2',
    	);
    	return isset($refund[$payType]) ? $refund[$payType] : '2' ;
    }
}
