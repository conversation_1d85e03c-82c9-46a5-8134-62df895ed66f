<?php

/**
 * This is the model class for table "wechat_qrcode_scene".
 *
 * The followings are the available columns in table 'wechat_qrcode_scene':
 * @property integer $id
 * @property integer $userid
 * @property integer $created
 * @property integer $expire
 * @property integer $requested
 * @property string $ticket
 * @property integer $updated
 * @property integer $scanned
 */
class WechatQRScene extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'wechat_qrcode_scene';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('ticket', 'required'),
			array('userid, created, expire, requested, updated, scanned', 'numerical', 'integerOnly'=>true),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, userid, created, expire, requested, ticket, updated, scanned', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'userid' => 'Userid',
			'created' => 'Created',
			'expire' => 'Expire',
			'requested' => 'Requested',
			'ticket' => 'Ticket',
			'updated' => 'Updated',
			'scanned' => 'Scanned',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('userid',$this->userid);
		$criteria->compare('created',$this->created);
		$criteria->compare('expire',$this->expire);
		$criteria->compare('requested',$this->requested);
		$criteria->compare('ticket',$this->ticket,true);
		$criteria->compare('updated',$this->updated);
		$criteria->compare('scanned',$this->scanned);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return WechatQRScene the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}
