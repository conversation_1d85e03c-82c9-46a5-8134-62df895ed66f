<?php

class RespondController extends Controller
{
	/**
		POST DATA
		array(
			'scheduleData', 'classid', 'weeknum', 'yid', 'startyear','activityIds', 'setTemplate', 'securityCode'
		)
	 */
	public function actionSchedule(){
		extract($_POST);
		$myCode = md5(sprintf('%s&%s&%s&%s&%s',$startyear, $weeknum, $classid, Yii::app()->user->getId(),$this->securityKey));
		
		if($securityCode == $myCode){
			Yii::import('common.models.schedule.*');
			$crit = new CDbCriteria;
			$crit->compare('classid', $classid);
			$crit->compare('weeknumber', $weeknum);
			$schedule = ClassScheduleV2::model()->with('data')->find($crit);
			if( is_null($schedule) ){
				$schedule = new ClassScheduleV2;
				$schedule->classid = $classid;
				$schedule->yid = $yid;
				$schedule->weeknumber = $weeknum;
			}
			if( is_null($schedule->data) ){
				$schedule->data = new ClassScheduleData;
			}
			$schedule->data->data = base64_encode($scheduleData);
			$acts = explode(',',$activityIds);
			$acts = array_unique($acts);
			$schedule->activitys = implode(',', $acts);
			$schedule->timestamp = time();
			$schedule->uid = Yii::app()->user->getId();
			
			if($schedule->save()){
				if(!$schedule->data->id){
					$schedule->data->id = $schedule->id;
				}
				
				if( $schedule->data->save() ){
					$scheduleTemplate = ScheduleTemplate::model()->findByAttributes(array('userid'=>Yii::app()->user->getId(), 'scheduleid'=>$schedule->id));
					$data = array();;
					if($setTemplate){
						if(is_null($scheduleTemplate)){
							$scheduleTemplate = new ScheduleTemplate;
							$scheduleTemplate->userid = Yii::app()->user->getId();
							$scheduleTemplate->scheduleid = $schedule->id;
							$scheduleTemplate->tempname = sprintf('%d-%d Week %d', $startyear, $startyear+1, $weeknum);
							$scheduleTemplate->save();
							$data['addTpl'] = $schedule->id; //增加个人的课表模版
							$data['tplName'] = $scheduleTemplate->tempname; //增加个人的课表模版
						}
					}else{
						if(!is_null($scheduleTemplate)){
							$data['removeTpl'] = $schedule->id; //删除个人的课表模版
							$scheduleTemplate->delete();
						}
					}
					$this->addMessage('state', 'success');
					$this->addMessage('data', $data);
					$this->showMessage();
				}else{
					$this->addMessage('state', 'fail');
					$this->addMessage('message', $schedule->data->getErrors());
					$this->showMessage();
				}
			}else{
				$this->addMessage('state', 'fail');
				$this->addMessage('message', $schedule->getErrors());
				$this->showMessage();
			}
			
		}else{
			$this->addMessage('state', 'fail');
			$this->addMessage('message', Yii::t('global', 'Wrong digital signature') );
			$this->showMessage();
		}
	}
}