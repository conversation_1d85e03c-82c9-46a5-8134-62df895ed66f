/**
 * Copyright (c) Tiny Technologies, Inc. All rights reserved.
 * Licensed under the LGPL or a commercial license.
 * For LGPL see License.txt in the project root for license information.
 * For commercial licenses see https://www.tiny.cloud/
 *
 * Version: 5.1.6 (2020-01-28)
 */
!function(s){"use strict";function i(){}function a(t){return function(){return t}}function t(t){return t}function e(){return l}var n,r=tinymce.util.Tools.resolve("tinymce.PluginManager"),u=a(!1),c=a(!0),l=(n={fold:function(t,e){return t()},is:u,isSome:u,isNone:c,getOr:d,getOrThunk:f,getOrDie:function(t){throw new Error(t||"error: getOrDie called on none.")},getOrNull:a(null),getOrUndefined:a(undefined),or:d,orThunk:f,map:e,each:i,bind:e,exists:u,forall:c,filter:e,equals:o,equals_:o,toArray:function(){return[]},toString:a("none()")},Object.freeze&&Object.freeze(n),n);function o(t){return t.isNone()}function f(t){return t()}function d(t){return t}function m(e){return function(t){return function(t){if(null===t)return"null";var e=typeof t;return"object"==e&&(Array.prototype.isPrototypeOf(t)||t.constructor&&"Array"===t.constructor.name)?"array":"object"==e&&(String.prototype.isPrototypeOf(t)||t.constructor&&"String"===t.constructor.name)?"string":e}(t)===e}}function b(t){for(var e=[],n=0,r=t.length;n<r;++n){if(!U(t[n]))throw new Error("Arr.flatten item "+n+" was not an array, input: "+t);S.apply(e,t[n])}return e}var p,g,h,y,v=function(n){function t(){return o}function e(t){return t(n)}var r=a(n),o={fold:function(t,e){return e(n)},is:function(t){return n===t},isSome:c,isNone:u,getOr:r,getOrThunk:r,getOrDie:r,getOrNull:r,getOrUndefined:r,or:t,orThunk:t,map:function(t){return v(t(n))},each:function(t){t(n)},bind:e,exists:e,forall:e,filter:function(t){return t(n)?o:l},toArray:function(){return[n]},toString:function(){return"some("+n+")"},equals:function(t){return t.is(n)},equals_:function(t,e){return t.fold(u,function(t){return e(n,t)})}};return o},w={some:v,none:e,from:function(t){return null===t||t===undefined?l:v(t)}},T=m("string"),_=m("object"),U=m("array"),x=m("boolean"),A=m("function"),I=Array.prototype.slice,S=Array.prototype.push,D=(A(Array.from)&&Array.from,function(){return(D=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t}).apply(this,arguments)}),C={},O={exports:C};p=undefined,g=C,h=O,y=undefined,function(t){"object"==typeof g&&void 0!==h?h.exports=t():"function"==typeof p&&p.amd?p([],t):("undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).EphoxContactWrapper=t()}(function(){return function s(i,a,u){function c(e,t){if(!a[e]){if(!i[e]){var n="function"==typeof y&&y;if(!t&&n)return n(e,!0);if(l)return l(e,!0);var r=new Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r}var o=a[e]={exports:{}};i[e][0].call(o.exports,function(t){return c(i[e][1][t]||t)},o,o.exports,s,i,a,u)}return a[e].exports}for(var l="function"==typeof y&&y,t=0;t<u.length;t++)c(u[t]);return c}({1:[function(t,e,n){var r,o,i=e.exports={};function a(){throw new Error("setTimeout has not been defined")}function u(){throw new Error("clearTimeout has not been defined")}function c(t){if(r===setTimeout)return setTimeout(t,0);if((r===a||!r)&&setTimeout)return r=setTimeout,setTimeout(t,0);try{return r(t,0)}catch(e){try{return r.call(null,t,0)}catch(e){return r.call(this,t,0)}}}!function(){try{r="function"==typeof setTimeout?setTimeout:a}catch(t){r=a}try{o="function"==typeof clearTimeout?clearTimeout:u}catch(t){o=u}}();var l,s=[],f=!1,d=-1;function m(){f&&l&&(f=!1,l.length?s=l.concat(s):d=-1,s.length&&p())}function p(){if(!f){var t=c(m);f=!0;for(var e=s.length;e;){for(l=s,s=[];++d<e;)l&&l[d].run();d=-1,e=s.length}l=null,f=!1,function n(t){if(o===clearTimeout)return clearTimeout(t);if((o===u||!o)&&clearTimeout)return o=clearTimeout,clearTimeout(t);try{return o(t)}catch(e){try{return o.call(null,t)}catch(e){return o.call(this,t)}}}(t)}}function g(t,e){this.fun=t,this.array=e}function h(){}i.nextTick=function(t){var e=new Array(arguments.length-1);if(1<arguments.length)for(var n=1;n<arguments.length;n++)e[n-1]=arguments[n];s.push(new g(t,e)),1!==s.length||f||c(p)},g.prototype.run=function(){this.fun.apply(null,this.array)},i.title="browser",i.browser=!0,i.env={},i.argv=[],i.version="",i.versions={},i.on=h,i.addListener=h,i.once=h,i.off=h,i.removeListener=h,i.removeAllListeners=h,i.emit=h,i.prependListener=h,i.prependOnceListener=h,i.listeners=function(t){return[]},i.binding=function(t){throw new Error("process.binding is not supported")},i.cwd=function(){return"/"},i.chdir=function(t){throw new Error("process.chdir is not supported")},i.umask=function(){return 0}},{}],2:[function(t,f,e){(function(e){function r(){}function i(t){if("object"!=typeof this)throw new TypeError("Promises must be constructed via new");if("function"!=typeof t)throw new TypeError("not a function");this._state=0,this._handled=!1,this._value=undefined,this._deferreds=[],s(t,this)}function o(r,o){for(;3===r._state;)r=r._value;0!==r._state?(r._handled=!0,i._immediateFn(function(){var t=1===r._state?o.onFulfilled:o.onRejected;if(null!==t){var e;try{e=t(r._value)}catch(n){return void u(o.promise,n)}a(o.promise,e)}else(1===r._state?a:u)(o.promise,r._value)})):r._deferreds.push(o)}function a(t,e){try{if(e===t)throw new TypeError("A promise cannot be resolved with itself.");if(e&&("object"==typeof e||"function"==typeof e)){var n=e.then;if(e instanceof i)return t._state=3,t._value=e,void c(t);if("function"==typeof n)return void s(function r(t,e){return function(){t.apply(e,arguments)}}(n,e),t)}t._state=1,t._value=e,c(t)}catch(o){u(t,o)}}function u(t,e){t._state=2,t._value=e,c(t)}function c(t){2===t._state&&0===t._deferreds.length&&i._immediateFn(function(){t._handled||i._unhandledRejectionFn(t._value)});for(var e=0,n=t._deferreds.length;e<n;e++)o(t,t._deferreds[e]);t._deferreds=null}function l(t,e,n){this.onFulfilled="function"==typeof t?t:null,this.onRejected="function"==typeof e?e:null,this.promise=n}function s(t,e){var n=!1;try{t(function(t){n||(n=!0,a(e,t))},function(t){n||(n=!0,u(e,t))})}catch(r){if(n)return;n=!0,u(e,r)}}var t,n;t=this,n=setTimeout,i.prototype["catch"]=function(t){return this.then(null,t)},i.prototype.then=function(t,e){var n=new this.constructor(r);return o(this,new l(t,e,n)),n},i.all=function(t){var c=Array.prototype.slice.call(t);return new i(function(o,i){if(0===c.length)return o([]);var a=c.length;function u(e,t){try{if(t&&("object"==typeof t||"function"==typeof t)){var n=t.then;if("function"==typeof n)return void n.call(t,function(t){u(e,t)},i)}c[e]=t,0==--a&&o(c)}catch(r){i(r)}}for(var t=0;t<c.length;t++)u(t,c[t])})},i.resolve=function(e){return e&&"object"==typeof e&&e.constructor===i?e:new i(function(t){t(e)})},i.reject=function(n){return new i(function(t,e){e(n)})},i.race=function(o){return new i(function(t,e){for(var n=0,r=o.length;n<r;n++)o[n].then(t,e)})},i._immediateFn="function"==typeof e?function(t){e(t)}:function(t){n(t,0)},i._unhandledRejectionFn=function(t){"undefined"!=typeof console&&console&&console.warn("Possible Unhandled Promise Rejection:",t)},i._setImmediateFn=function(t){i._immediateFn=t},i._setUnhandledRejectionFn=function(t){i._unhandledRejectionFn=t},void 0!==f&&f.exports?f.exports=i:t.Promise||(t.Promise=i)}).call(this,t("timers").setImmediate)},{timers:3}],3:[function(c,t,l){(function(t,e){var r=c("process/browser.js").nextTick,n=Function.prototype.apply,o=Array.prototype.slice,i={},a=0;function u(t,e){this._id=t,this._clearFn=e}l.setTimeout=function(){return new u(n.call(setTimeout,window,arguments),clearTimeout)},l.setInterval=function(){return new u(n.call(setInterval,window,arguments),clearInterval)},l.clearTimeout=l.clearInterval=function(t){t.close()},u.prototype.unref=u.prototype.ref=function(){},u.prototype.close=function(){this._clearFn.call(window,this._id)},l.enroll=function(t,e){clearTimeout(t._idleTimeoutId),t._idleTimeout=e},l.unenroll=function(t){clearTimeout(t._idleTimeoutId),t._idleTimeout=-1},l._unrefActive=l.active=function(t){clearTimeout(t._idleTimeoutId);var e=t._idleTimeout;0<=e&&(t._idleTimeoutId=setTimeout(function(){t._onTimeout&&t._onTimeout()},e))},l.setImmediate="function"==typeof t?t:function(t){var e=a++,n=!(arguments.length<2)&&o.call(arguments,1);return i[e]=!0,r(function(){i[e]&&(n?t.apply(null,n):t.call(null),l.clearImmediate(e))}),e},l.clearImmediate="function"==typeof e?e:function(t){delete i[t]}}).call(this,c("timers").setImmediate,c("timers").clearImmediate)},{"process/browser.js":1,timers:3}],4:[function(t,e,n){var r=t("promise-polyfill"),o="undefined"!=typeof window?window:Function("return this;")();e.exports={boltExport:o.Promise||r}},{"promise-polyfill":2}]},{},[4])(4)});function P(t){s.setTimeout(function(){throw t},0)}function E(t){return yt(dt(t))}function L(a){return function(){for(var t=new Array(arguments.length),e=0;e<t.length;e++)t[e]=arguments[e];if(0===t.length)throw new Error("Can't merge zero objects");for(var n={},r=0;r<t.length;r++){var o=t[r];for(var i in o)bt.call(o,i)&&(n[i]=a(n[i],o[i]))}return n}}function N(t,e){return Math.max(parseInt(t,10),parseInt(e,10))}function j(t){return t.style.marginLeft&&t.style.marginRight&&t.style.marginLeft===t.style.marginRight?Ct(t.style.marginLeft):""}function R(t){return t.style.marginTop&&t.style.marginBottom&&t.style.marginTop===t.style.marginBottom?Ct(t.style.marginTop):""}function F(t){return t.style.borderWidth?Ct(t.style.borderWidth):""}function k(t,e){return t.hasAttribute(e)?t.getAttribute(e):""}function z(t,e){return t.style[e]?t.style[e]:""}function M(t){return null!==t.parentNode&&"FIGURE"===t.parentNode.nodeName}function B(t,e,n){t.setAttribute(e,n)}function H(t){M(t)?function(t){var e=t.parentNode;Rt.insertAfter(t,e),Rt.remove(e)}(t):function(t){var e=Rt.create("figure",{"class":"image"});Rt.insertAfter(e,t),e.appendChild(t),e.appendChild(Rt.create("figcaption",{contentEditable:"true"},"Caption")),e.contentEditable="false"}(t)}function G(t,e){var n=t.getAttribute("style"),r=e(null!==n?n:"");0<r.length?(t.setAttribute("style",r),t.setAttribute("data-mce-style",r)):t.removeAttribute("style")}function W(t,r){return function(t,e,n){t.style[e]?(t.style[e]=Ot(n),G(t,r)):B(t,e,n)}}function q(t,e){return t.style[e]?Ct(t.style[e]):k(t,e)}function $(t,e){var n=Ot(e);t.style.marginLeft=n,t.style.marginRight=n}function J(t,e){var n=Ot(e);t.style.marginTop=n,t.style.marginBottom=n}function V(t,e){var n=Ot(e);t.style.borderWidth=n}function X(t,e){t.style.borderStyle=e}function Z(t){return"FIGURE"===t.nodeName}function K(t,e){var n=s.document.createElement("img");return B(n,"style",e.style),!j(n)&&""===e.hspace||$(n,e.hspace),!R(n)&&""===e.vspace||J(n,e.vspace),!F(n)&&""===e.border||V(n,e.border),!function(t){return z(t,"borderStyle")}(n)&&""===e.borderStyle||X(n,e.borderStyle),t(n.getAttribute("style"))}function Q(t,e){return{src:k(e,"src"),alt:k(e,"alt"),title:k(e,"title"),width:q(e,"width"),height:q(e,"height"),"class":k(e,"class"),style:t(k(e,"style")),caption:M(e),hspace:j(e),vspace:R(e),border:F(e),borderStyle:z(e,"borderStyle")}}function Y(t,e,n,r,o){n[r]!==e[r]&&o(t,r,n[r])}function tt(r,o){return function(t,e,n){r(t,n),G(t,o)}}function et(t,e){var n=t.dom.styles.parse(e),r=Pt(n),o=t.dom.styles.parse(t.dom.styles.serialize(r));return t.dom.styles.serialize(o)}function nt(t){var e=t.selection.getNode(),n=t.dom.getParent(e,"figure.image");return n?t.dom.select("img",n)[0]:e&&("IMG"!==e.nodeName||jt(e))?null:e}function rt(e,t){var n=e.dom,r=n.getParent(t.parentNode,function(t){return e.schema.getTextBlockElements()[t.nodeName]},e.getBody());return r?n.split(r,t):t}function ot(e,t){var n=function(t,e){var n=s.document.createElement("img");if(Ft(t,Tt(e,{caption:!1}),n),B(n,"alt",e.alt),e.caption){var r=Rt.create("figure",{"class":"image"});return r.appendChild(n),r.appendChild(Rt.create("figcaption",{contentEditable:"true"},"Caption")),r.contentEditable="false",r}return n}(function(t){return et(e,t)},t);e.dom.setAttrib(n,"data-mce-id","__mcenew"),e.focus(),e.selection.setContent(n.outerHTML);var r=e.dom.select('*[data-mce-id="__mcenew"]')[0];if(e.dom.setAttrib(r,"data-mce-id",null),Z(r)){var o=rt(e,r);e.selection.select(o)}else e.selection.select(r)}function it(t,e){var n=nt(t);n?e.src?function(e,t){var n=nt(e);if(Ft(function(t){return et(e,t)},t,n),function(t,e){t.dom.setAttrib(e,"src",e.getAttribute("src"))}(e,n),Z(n.parentNode)){var r=n.parentNode;rt(e,r),e.selection.select(n.parentNode)}else e.selection.select(n),Lt(e,t,n)}(t,e):function(t,e){if(e){var n=t.dom.is(e.parentNode,"figure.image")?e.parentNode:e;t.dom.remove(n),t.focus(),t.nodeChanged(),t.dom.isEmpty(t.getBody())&&(t.setContent(""),t.selection.setCursorLocation())}}(t,n):e.src&&ot(t,e)}function at(t){return T(t.value)?t.value:""}function ut(e){return void 0===e&&(e=at),function(t){return t?w.from(t).map(function(t){return zt(t,e)}):w.none()}}var ct=O.exports.boltExport,lt=function(t){var n=w.none(),e=[],r=function(t){o()?a(t):e.push(t)},o=function(){return n.isSome()},i=function(t){!function(t,e){for(var n=0,r=t.length;n<r;n++){e(t[n],n)}}(t,a)},a=function(e){n.each(function(t){s.setTimeout(function(){e(t)},0)})};return t(function(t){n=w.some(t),i(e),e=[]}),{get:r,map:function(n){return lt(function(e){r(function(t){e(n(t))})})},isReady:o}},st={nu:lt,pure:function(e){return lt(function(t){t(e)})}},ft=function(n){function t(t){n().then(t,P)}return{map:function(t){return ft(function(){return n().then(t)})},bind:function(e){return ft(function(){return n().then(function(t){return e(t).toPromise()})})},anonBind:function(t){return ft(function(){return n().then(function(){return t.toPromise()})})},toLazy:function(){return st.nu(t)},toCached:function(){var t=null;return ft(function(){return null===t&&(t=n()),t})},toPromise:n,get:t}},dt=function(t){return ft(function(){return new ct(t)})},mt=function(t){return ft(function(){return ct.resolve(t)})},pt=function(n){return{is:function(t){return n===t},isValue:c,isError:u,getOr:a(n),getOrThunk:a(n),getOrDie:a(n),or:function(t){return pt(n)},orThunk:function(t){return pt(n)},fold:function(t,e){return e(n)},map:function(t){return pt(t(n))},mapError:function(t){return pt(n)},each:function(t){t(n)},bind:function(t){return t(n)},exists:function(t){return t(n)},forall:function(t){return t(n)},toOption:function(){return w.some(n)}}},gt=function(n){return{is:u,isValue:u,isError:c,getOr:t,getOrThunk:function(t){return t()},getOrDie:function(){return function(t){return function(){throw new Error(t)}}(String(n))()},or:function(t){return t},orThunk:function(t){return t()},fold:function(t,e){return t(n)},map:function(t){return gt(n)},mapError:function(t){return gt(t(n))},each:i,bind:function(t){return gt(n)},exists:u,forall:c,toOption:w.none}},ht={value:pt,error:gt,fromOption:function(t,e){return t.fold(function(){return gt(e)},pt)}},yt=function(i){return D(D({},i),{toCached:function(){return yt(i.toCached())},bindFuture:function(e){return yt(i.bind(function(t){return t.fold(function(t){return mt(ht.error(t))},function(t){return e(t)})}))},bindResult:function(e){return yt(i.map(function(t){return t.bind(e)}))},mapResult:function(e){return yt(i.map(function(t){return t.map(e)}))},mapError:function(e){return yt(i.map(function(t){return t.mapError(e)}))},foldResult:function(e,n){return i.map(function(t){return t.fold(e,n)})},withTimeout:function(t,o){return yt(dt(function(e){var n=!1,r=s.setTimeout(function(){n=!0,e(ht.error(o()))},t);i.get(function(t){n||(s.clearTimeout(r),e(t))})}))}})},vt=E,bt=Object.prototype.hasOwnProperty,wt=L(function(t,e){return _(t)&&_(e)?wt(t,e):e}),Tt=L(function(t,e){return e}),_t=tinymce.util.Tools.resolve("tinymce.dom.DOMUtils"),Ut=tinymce.util.Tools.resolve("tinymce.util.Promise"),xt=tinymce.util.Tools.resolve("tinymce.util.XHR"),At=function(t){return t.getParam("images_upload_url","","string")},It=function(t){return t.getParam("images_upload_handler",undefined,"function")},St={hasDimensions:function(t){return t.getParam("image_dimensions",!0,"boolean")},hasUploadTab:function(t){return t.getParam("image_uploadtab",!0,"boolean")},hasAdvTab:function(t){return t.getParam("image_advtab",!1,"boolean")},getPrependUrl:function(t){return t.getParam("image_prepend_url","","string")},getClassList:function(t){return t.getParam("image_class_list")},hasDescription:function(t){return t.getParam("image_description",!0,"boolean")},hasImageTitle:function(t){return t.getParam("image_title",!1,"boolean")},hasImageCaption:function(t){return t.getParam("image_caption",!1,"boolean")},getImageList:function(t){return t.getParam("image_list",!1)},hasUploadUrl:function(t){return!!At(t)},hasUploadHandler:function(t){return!!It(t)},getUploadUrl:At,getUploadHandler:It,getUploadBasePath:function(t){return t.getParam("images_upload_base_path",undefined,"string")},getUploadCredentials:function(t){return t.getParam("images_upload_credentials",!1,"boolean")},isAutomaticUploadsEnabled:function(t){return t.getParam("automatic_uploads",!0,"boolean")}},Dt=function(t,e){function n(t){r.parentNode&&r.parentNode.removeChild(r),e(t)}var r=s.document.createElement("img");r.onload=function(){var t={width:N(r.width,r.clientWidth),height:N(r.height,r.clientHeight)};n(ht.value(t))},r.onerror=function(){n(ht.error("Failed to get image dimensions for: "+t))};var o=r.style;o.visibility="hidden",o.position="fixed",o.bottom=o.left="0px",o.width=o.height="auto",s.document.body.appendChild(r),r.src=t},Ct=function(t){return t=t&&t.replace(/px$/,"")},Ot=function(t){return 0<t.length&&/^[0-9]+$/.test(t)&&(t+="px"),t},Pt=function(t){if(t.margin){var e=String(t.margin).split(" ");switch(e.length){case 1:t["margin-top"]=t["margin-top"]||e[0],t["margin-right"]=t["margin-right"]||e[0],t["margin-bottom"]=t["margin-bottom"]||e[0],t["margin-left"]=t["margin-left"]||e[0];break;case 2:t["margin-top"]=t["margin-top"]||e[0],t["margin-right"]=t["margin-right"]||e[1],t["margin-bottom"]=t["margin-bottom"]||e[0],t["margin-left"]=t["margin-left"]||e[1];break;case 3:t["margin-top"]=t["margin-top"]||e[0],t["margin-right"]=t["margin-right"]||e[1],t["margin-bottom"]=t["margin-bottom"]||e[2],t["margin-left"]=t["margin-left"]||e[1];break;case 4:t["margin-top"]=t["margin-top"]||e[0],t["margin-right"]=t["margin-right"]||e[1],t["margin-bottom"]=t["margin-bottom"]||e[2],t["margin-left"]=t["margin-left"]||e[3]}delete t.margin}return t},Et=function(t,e){var n=St.getImageList(t);"string"==typeof n?xt.send({url:n,success:function(t){e(JSON.parse(t))}}):"function"==typeof n?n(e):e(n)},Lt=function(t,e,n){function r(){n.onload=n.onerror=null,t.selection&&(t.selection.select(n),t.nodeChanged())}n.onload=function(){e.width||e.height||!St.hasDimensions(t)||t.dom.setAttribs(n,{width:String(n.clientWidth),height:String(n.clientHeight)}),r()},n.onerror=r},Nt=function(r){return new Ut(function(t,e){var n=new s.FileReader;n.onload=function(){t(n.result)},n.onerror=function(){e(n.error.message)},n.readAsDataURL(r)})},jt=function(t){return"IMG"===t.nodeName&&(t.hasAttribute("data-mce-object")||t.hasAttribute("data-mce-placeholder"))},Rt=_t.DOM,Ft=function(t,e,n){var r=Q(t,n);Y(n,r,e,"caption",function(t,e,n){return H(t)}),Y(n,r,e,"src",B),Y(n,r,e,"alt",B),Y(n,r,e,"title",B),Y(n,r,e,"width",W(0,t)),Y(n,r,e,"height",W(0,t)),Y(n,r,e,"class",B),Y(n,r,e,"style",tt(function(t,e){return B(t,"style",e)},t)),Y(n,r,e,"hspace",tt($,t)),Y(n,r,e,"vspace",tt(J,t)),Y(n,r,e,"border",tt(V,t)),Y(n,r,e,"borderStyle",tt(X,t))},kt=tinymce.util.Tools.resolve("tinymce.util.Tools"),zt=function(t,o){var i=[];return kt.each(t,function(t){var e=T(t.text)?t.text:T(t.title)?t.title:"";if(t.menu!==undefined){var n=zt(t.menu,o);i.push({text:e,items:n})}else{var r=o(t);i.push({text:e,value:r})}}),i},Mt=function(t,e){return function(t,e){for(var n=0;n<t.length;n++){var r=e(t[n],n);if(r.isSome())return r}return w.none()}(t,function(t){return function(t){return Object.prototype.hasOwnProperty.call(t,"items")}(t)?Mt(t.items,e):t.value===e?w.some(t):w.none()})},Bt=ut,Ht=function(t){return ut(at)(t)},Gt=function(t,e){return t.bind(function(t){return Mt(t,e)})};function Wt(a){function e(t,e,n,r){var o,i;(o=new s.XMLHttpRequest).open("POST",a.url),o.withCredentials=a.credentials,o.upload.onprogress=function(t){r(t.loaded/t.total*100)},o.onerror=function(){n("Image upload failed due to a XHR Transport error. Code: "+o.status)},o.onload=function(){var t;o.status<200||300<=o.status?n("HTTP Error: "+o.status):(t=JSON.parse(o.responseText))&&"string"==typeof t.location?e(function(t,e){return t?t.replace(/\/$/,"")+"/"+e.replace(/^\//,""):e}(a.basePath,t.location)):n("Invalid JSON: "+o.responseText)},(i=new s.FormData).append("file",t.blob(),t.filename()),o.send(i)}return a=kt.extend({credentials:!1,handler:e},a),{upload:function(t){return!a.url&&function(t){return t===e}(a.handler)?Ut.reject("Upload url missing from the settings."):function(r,o){return new Ut(function(t,e){try{o(r,t,e,i)}catch(n){e(n.message)}})}(t,a.handler)}}}function qt(n){var r=Bt(function(t){return n.convertURL(t.value||t.url,"src")}),t=dt(function(e){Et(n,function(t){e(r(t).map(function(t){return b([[{text:"None",value:""}],t])}))})}),e=Ht(St.getClassList(n)),o=St.hasAdvTab(n),i=St.hasUploadTab(n),a=St.hasUploadUrl(n),u=St.hasUploadHandler(n),c=function(e){var t=nt(e);return t?Q(function(t){return et(e,t)},t):{src:"",alt:"",title:"",width:"",height:"","class":"",style:"",caption:!1,hspace:"",vspace:"",border:"",borderStyle:""}}(n),l=St.hasDescription(n),s=St.hasImageTitle(n),f=St.hasDimensions(n),d=St.hasImageCaption(n),m=St.getUploadUrl(n),p=St.getUploadBasePath(n),g=St.getUploadCredentials(n),h=St.getUploadHandler(n),y=St.isAutomaticUploadsEnabled(n),v=w.some(St.getPrependUrl(n)).filter(function(t){return T(t)&&0<t.length});return t.map(function(t){return{image:c,imageList:t,classList:e,hasAdvTab:o,hasUploadTab:i,hasUploadUrl:a,hasUploadHandler:u,hasDescription:l,hasImageTitle:s,hasDimensions:f,hasImageCaption:d,url:m,basePath:p,credentials:g,handler:h,automaticUploads:y,prependURL:v}})}function $t(t){var e=t.imageList.map(function(t){return{name:"images",type:"selectbox",label:"Image list",items:t}}),n=t.classList.map(function(t){return{name:"classes",type:"selectbox",label:"Class",items:t}});return b([[{name:"src",type:"urlinput",filetype:"image",label:"Source"}],e.toArray(),t.hasDescription?[{name:"alt",type:"input",label:"Image description"}]:[],t.hasImageTitle?[{name:"title",type:"input",label:"Image title"}]:[],t.hasDimensions?[{name:"dimensions",type:"sizeinput"}]:[],[{type:"grid",columns:2,items:b([n.toArray(),t.hasImageCaption?[{type:"label",label:"Caption",items:[{type:"checkbox",name:"caption",label:"Show caption"}]}]:[]])}]])}function Jt(t){return{src:{value:t.src,meta:{}},images:t.src,alt:t.alt,title:t.title,dimensions:{width:t.width,height:t.height},classes:t["class"],caption:t.caption,style:t.style,vspace:t.vspace,border:t.border,hspace:t.hspace,borderstyle:t.borderStyle,fileinput:[]}}function Vt(t){return{src:t.src.value,alt:t.alt,title:t.title,width:t.dimensions.width,height:t.dimensions.height,"class":t.classes,style:t.style,caption:t.caption,hspace:t.hspace,vspace:t.vspace,border:t.border,borderStyle:t.borderstyle}}function Xt(t,e){var n=e.getData();(function(t,e){return/^(?:[a-zA-Z]+:)?\/\//.test(e)?w.none():t.prependURL.bind(function(t){return e.substring(0,t.length)!==t?w.some(t+e):w.none()})})(t,n.src.value).each(function(t){e.setData({src:{value:t,meta:n.src.meta}})})}function Zt(t,e){var n=e.getData(),r=n.src.meta;if(r!==undefined){var o=wt({},n);!function(t,e,n){t.hasDescription&&T(n.alt)&&(e.alt=n.alt),t.hasImageTitle&&T(n.title)&&(e.title=n.title),t.hasDimensions&&(T(n.width)&&(e.dimensions.width=n.width),T(n.height)&&(e.dimensions.height=n.height)),T(n["class"])&&Gt(t.classList,n["class"]).each(function(t){e.classes=t.value}),t.hasImageCaption&&x(n.caption)&&(e.caption=n.caption),t.hasAdvTab&&(T(n.vspace)&&(e.vspace=n.vspace),T(n.border)&&(e.border=n.border),T(n.hspace)&&(e.hspace=n.hspace),T(n.borderstyle)&&(e.borderstyle=n.borderstyle))}(t,o,r),e.setData(o)}}function Kt(t,e,n,r){Xt(e,r),Zt(e,r),function(t,e,n,r){var o=r.getData(),i=o.src.value,a=o.src.meta||{};a.width||a.height||!e.hasDimensions||t.imageSize(i).get(function(t){t.each(function(t){n.open&&r.setData({dimensions:t})})})}(t,e,n,r),function(t,e,n){var r=n.getData(),o=Gt(t.imageList,r.src.value);e.prevImage=o,n.setData({images:o.map(function(t){return t.value}).getOr("")})}(e,n,r)}function Qt(t,e,n){var r=Pt(t(n.style)),o=wt({},n);return o.vspace=function(t){return t["margin-top"]&&t["margin-bottom"]&&t["margin-top"]===t["margin-bottom"]?Ct(String(t["margin-top"])):""}(r),o.hspace=function(t){return t["margin-right"]&&t["margin-left"]&&t["margin-right"]===t["margin-left"]?Ct(String(t["margin-right"])):""}(r),o.border=function(t){return t["border-width"]?Ct(String(t["border-width"])):""}(r),o.borderstyle=function(t){return t["border-style"]?String(t["border-style"]):""}(r),o.style=function(t,e,n){return e(t(e(n)))}(t,e,r),o}function Yt(u,c,e,l){var t=l.getData();l.block("Uploading image"),function(t){return 0===t.length?w.none():w.some(t[0])}(t.fileinput).fold(function(){l.unblock()},function(n){function r(){l.unblock(),s.URL.revokeObjectURL(i)}function o(t){l.setData({src:{value:t,meta:{}}}),l.showTab("general"),Kt(u,c,e,l)}var i=s.URL.createObjectURL(n),a=Wt({url:c.url,basePath:c.basePath,credentials:c.credentials,handler:c.handler});Nt(n).then(function(t){var e=u.createBlobCache(n,i,t);c.automaticUploads?a.upload(e).then(function(t){o(t),r()})["catch"](function(t){r(),u.alertErr(l,t)}):(u.addToBlobCache(e),o(e.blobUri()),l.unblock())})})}function te(n,r,o){return function(t,e){"src"===e.name?Kt(n,r,o,t):"images"===e.name?function(t,e,n,r){var o=r.getData(),i=Gt(e.imageList,o.images);i.each(function(t){""===o.alt||n.prevImage.map(function(t){return t.text===o.alt}).getOr(!1)?""===t.value?r.setData({src:t,alt:n.prevAlt}):r.setData({src:t,alt:t.text}):r.setData({src:t})}),n.prevImage=i,Kt(t,e,n,r)}(n,r,o,t):"alt"===e.name?o.prevAlt=t.getData().alt:"style"===e.name?function(t,e){var n=e.getData(),r=Qt(t.parseStyle,t.serializeStyle,n);e.setData(r)}(n,t):"vspace"===e.name||"hspace"===e.name||"border"===e.name||"borderstyle"===e.name?function(t,e,n){var r=wt(Jt(e.image),n.getData()),o=K(t.normalizeCss,Vt(r));n.setData({style:o})}(n,r,t):"fileinput"===e.name&&Yt(n,r,o,t)}}function ee(n){return function(t){var e=function(t){return{prevImage:Gt(t.imageList,t.image.src),prevAlt:t.image.alt,open:!0}}(t);return{title:"Insert/Edit Image",size:"normal",body:function(t){return t.hasAdvTab||t.hasUploadUrl||t.hasUploadHandler?{type:"tabpanel",tabs:b([[ie(t)],t.hasAdvTab?[oe(t)]:[],t.hasUploadTab&&(t.hasUploadUrl||t.hasUploadHandler)?[ue(t)]:[]])}:{type:"panel",items:ae(t)}}(t),buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],initialData:Jt(t.image),onSubmit:n.onSubmit(t),onChange:te(n,t,e),onClose:function(t){return function(){t.open=!1}}(e)}}}function ne(e){var t={onSubmit:function(r){return function(n){return function(t){var e=wt(Jt(n.image),t.getData());r.undoManager.transact(function(){it(r,Vt(e))}),r.editorUpload.uploadImagesAuto(),t.close()}}}(e),imageSize:function(e){return function(t){return vt(function(n){Dt(e.documentBaseURI.toAbsolute(t),function(t){var e=t.map(function(t){return{width:String(t.width),height:String(t.height)}});n(e)})})}}(e),addToBlobCache:function(e){return function(t){e.editorUpload.blobCache.add(t)}}(e),createBlobCache:function(r){return function(t,e,n){return r.editorUpload.blobCache.create({blob:t,blobUri:e,name:t.name?t.name.replace(/\.[^\.]+$/,""):null,base64:n.split(",")[1]})}}(e),alertErr:function(n){return function(t,e){n.windowManager.alert(e,t.close)}}(e),normalizeCss:function(e){return function(t){return et(e,t)}}(e),parseStyle:function(e){return function(t){return e.dom.parseStyle(t)}}(e),serializeStyle:function(n){return function(t,e){return n.dom.serializeStyle(t,e)}}(e)};return{open:function(){return qt(e).map(ee(t)).get(function(t){e.windowManager.open(t)})}}}function re(i){return function(t){for(var e,n=t.length,r=function(t){t.attr("contenteditable",i?"true":null)};n--;){var o=t[n];void 0,(e=o.attr("class"))&&/\bimage\b/.test(e)&&(o.attr("contenteditable",i?"false":null),kt.each(o.getAll("figcaption"),r))}}}var oe=function(t){return{title:"Advanced",name:"advanced",items:[{type:"input",label:"Style",name:"style"},{type:"grid",columns:2,items:[{type:"input",label:"Vertical space",name:"vspace",inputMode:"numeric"},{type:"input",label:"Horizontal space",name:"hspace",inputMode:"numeric"},{type:"input",label:"Border width",name:"border",inputMode:"numeric"},{type:"selectbox",name:"borderstyle",label:"Border style",items:[{text:"Select...",value:""},{text:"Solid",value:"solid"},{text:"Dotted",value:"dotted"},{text:"Dashed",value:"dashed"},{text:"Double",value:"double"},{text:"Groove",value:"groove"},{text:"Ridge",value:"ridge"},{text:"Inset",value:"inset"},{text:"Outset",value:"outset"},{text:"None",value:"none"},{text:"Hidden",value:"hidden"}]}]}]}},ie=function(t){return{title:"General",name:"general",items:$t(t)}},ae=$t,ue=function(t){return{title:"Upload",name:"upload",items:[{type:"dropzone",name:"fileinput"}]}},ce=function(t){t.addCommand("mceImage",ne(t).open)},le=function(t){t.on("PreInit",function(){t.parser.addNodeFilter("figure",re(!0)),t.serializer.addNodeFilter("figure",re(!1))})},se=function(e){e.ui.registry.addToggleButton("image",{icon:"image",tooltip:"Insert/edit image",onAction:ne(e).open,onSetup:function(t){return e.selection.selectorChangedWithUnbind("img:not([data-mce-object],[data-mce-placeholder]),figure.image",t.setActive).unbind}}),e.ui.registry.addMenuItem("image",{icon:"image",text:"Image...",onAction:ne(e).open}),e.ui.registry.addContextMenu("image",{update:function(t){return Z(t)||function(t){return"IMG"===t.nodeName}(t)&&!jt(t)?["image"]:[]}})};!function fe(){r.add("image",function(t){le(t),se(t),ce(t)})}()}(window);