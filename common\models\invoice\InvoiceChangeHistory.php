<?php

/**
 * This is the model class for table "ivy_invoice_change_history".
 *
 * The followings are the available columns in table 'ivy_invoice_change_history':
 * @property integer $hid
 * @property integer $invoice_id
 * @property integer $new_invoiceid
 * @property integer $invoice_type
 * @property integer $operator_uid
 * @property integer $operate_timestamp
 * @property integer $auditor_uid
 * @property integer $auditor_timestamp
 */
class InvoiceChangeHistory extends CActiveRecord
{
    const INVOICE_TYPE_APPLICATION_CHANGE = 10;

    /**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_invoice_change_history';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('invoice_id, new_invoiceid, invoice_type, operate_timestamp', 'required'),
			array('invoice_id, new_invoiceid, invoice_type, operator_uid, operate_timestamp, auditor_uid, auditor_timestamp', 'numerical', 'integerOnly'=>true),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('hid, invoice_id, new_invoiceid, invoice_type, operator_uid, operate_timestamp, auditor_uid, auditor_timestamp', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'invoice'=>array(self::BELONGS_TO,'Invoice','invoice_id'),
            'newInvoice'=>array(self::BELONGS_TO,'Invoice','new_invoiceid'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'hid' => 'Hid',
			'invoice_id' => 'Invoice',
			'new_invoiceid' => 'New Invoiceid',
			'invoice_type' => 'Invoice Type',
			'operator_uid' => 'Operator Uid',
			'operate_timestamp' => 'Operate Timestamp',
			'auditor_uid' => 'Auditor Uid',
			'auditor_timestamp' => 'Auditor Timestamp',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('hid',$this->hid);
		$criteria->compare('invoice_id',$this->invoice_id);
		$criteria->compare('new_invoiceid',$this->new_invoiceid);
		$criteria->compare('invoice_type',$this->invoice_type);
		$criteria->compare('operator_uid',$this->operator_uid);
		$criteria->compare('operate_timestamp',$this->operate_timestamp);
		$criteria->compare('auditor_uid',$this->auditor_uid);
		$criteria->compare('auditor_timestamp',$this->auditor_timestamp);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return InvoiceChangeHistory the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}
