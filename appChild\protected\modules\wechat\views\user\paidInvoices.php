 <script>
$(function(){   
 	var stop, billListNums = 1;
	stop = true;
	refreshList();
	$(".container").scroll(function(){ 
			refreshList();	//调用下拉刷新代码
});
//下拉刷新代码开始
	function refreshList(){
		var billListContent ; 
		if(stop==true){ //如果标记变量为true执行刷新动作
			stop=false;
			$("#billList").append("<p id='waiting_' class='weui_media_box weui_media_text'><?php echo Yii::t('wechat', 'Loading data...'); ?></p>");
			billListContent="";//清空添加内容
			$.getJSON("<?php echo $this->createUrl('paidInvoice'); ?>",{pageNum:billListNums}, function(json){	//发起Ajax请求
				$("#waiting_").remove();//发起请求时删除加载提示
				$("#billListComplete").remove();//删除之前的加载完毕提示
				if(json.data.invoiceNum == 0){	//请求页面数量如果为0说明已经全部加载完成
					$("#billList").append('<div class="weui-loadmore weui-loadmore_line" id="billListComplete"><span class="weui-loadmore__tips"><?php echo Yii::t('wechat', 'The end'); ?></span></div>');
					refreshList = function(){};
				}
				else{
					for(var i = 0;i < json.data.invoiceInfo.length;i++){	//否则将json数据加载到列表中
						billListContent =  "<div class='weui_media_box weui_media_text'><h4 class='weui_media_title'> "+json.data.invoiceInfo[i].title + "</h4><p class='weui_media_desc'><?php echo Yii::t('wechat', 'Amount: '); ?>"+ json.data.invoiceInfo[i].amount +"; <?php echo Yii::t('wechat', 'Date: '); ?>"+ json.data.invoiceInfo[i].date +"</p></div>";
						$("#billList").append(billListContent);		//添加数据
					}
					billListNums++;	//当前请求页数+1
				}
				stop=true;
			});
		} 
	} 
	//下拉刷新代码结束
});  
</script>
<div class="weui_cells_title"><?php echo Yii::t('navigations', 'Payment History'); ?></div>
<div class="panel">
	<div class="bd">
		<div class="weui_panel weui_panel_access">
			<?php
			     //选择孩子
			    $this->widget('ext.wechat.ChildSelector',array(
			        'childObj'=>$childObj,
			        'myChildObjs'=>$this->myChildObjs,
			        'jumpUrl'=>'paidInvoices',
			    ));
			 ?>
			<div id="billList" class="weui_panel_bd"></div>
		</div>
	</div>
</div>
<style>
	.weui-loadmore_line .weui-loadmore__tips {
        position: relative;
        top: -0.9em;
        padding: 0 .55em;
        background-color: #FFFFFF;
        color: #808080;
        font-size: 16px
    }
    .weui-loadmore__tips {
        display: inline-block;
        vertical-align: middle;
    }
    .weui-loadmore_line {
        border-top: 1px solid #ddd;
        margin-top: 2.4em;
    }
    .weui-loadmore {
        width: 65%;
        margin: 0.5em auto;
        line-height: 1.6em;
        font-size: 14px;
        text-align: center;
    }
    #waiting_{
    	text-align: center;
    }
</style>