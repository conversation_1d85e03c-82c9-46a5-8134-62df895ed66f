<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
    <h4 class="modal-title" id="exampleModalLabel"><?php echo $productsCat->id?' 更新商品分类：':'添加商品分类：'; ?></h4>
</div>
<?php $form=$this->beginWidget('CActiveForm', array(
    'id'=>'courseGroup-form',
    'enableAjaxValidation'=>false,
    'htmlOptions'=>array('class'=>'J_ajaxForm form-horizontal', 'role'=>'form', 'enctype'=>"multipart/form-data"),
)); ?>

<div class="modal-body">
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($productsCat,'title_cn'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->textField($productsCat,'title_cn',array('maxlength'=>255,'class'=>'form-control')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($productsCat,'title_en'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->textField($productsCat,'title_en',array('maxlength'=>255,'class'=>'form-control')); ?>
        </div>
    </div>
<!--介绍-->
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($productsCat,'desc_cn'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->textField($productsCat,'desc_cn',array('maxlength'=>500,'class'=>'form-control')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($productsCat,'desc_en'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->textField($productsCat,'desc_en',array('maxlength'=>500,'class'=>'form-control')); ?>
        </div>
    </div>


    <?php if($productsCat->img): ?>
        <div class="form-group">
            <label class="col-xs-2 control-label"></label>
            <div class="col-xs-9">
                <img style="width: 100%" src="<?php echo Yii::app()->params['OAUploadBaseUrl'] . '/productsCat/' . $productsCat->img . '?v' . time() ?>">
            </div>
        </div>
    <?php  endif; ?>
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($productsCat,'img'); ?></label>
        <div class="col-xs-2">
            <?php echo $form->fileField($productsCat,'photo',array('maxlength'=>255)); ?>
        </div>
        <label class="col-xs-6">建议尺寸500*500</label>
    </div>
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($productsCat,'sort'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->textField($productsCat,'sort',array('class'=>'form-control')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($productsCat,'status'); ?></label>
        <div class="col-xs-9" style="padding-top: 0.5%">
            <label>
            <?php echo $form->checkbox($productsCat,'status', array('1'=>"开启"),array('template'=>'<label class="radio-inline">{input}{label}</label>','separator'=>'')); ?>有效
            </label>
        </div>
    </div>
</div>
<div class="modal-footer">
    <button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global','Submit');?></button>
    <button type="button" class="btn btn-default"  data-dismiss="modal"><?php echo Yii::t('global','Cancel');?></button>
</div>
<?php $this->endWidget(); ?>
