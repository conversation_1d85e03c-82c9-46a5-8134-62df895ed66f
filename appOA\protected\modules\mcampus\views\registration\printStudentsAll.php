<style>
	.container-fluid{
		width:860px;
		margin:0 auto
	}
	.page{
		margin-top:20px
	}
	img{
		margin-top:10px;
		max-width:100%;
		height:auto
	}
</style>
<style media="print">
	.page {
		page-break-after: always
	}
    .sign{
        float: right;
        margin-right:20px;
        margin-bottom:20px
    }
    .sign img{
        width:100px;
    }
</style>
<div class="container-fluid">
        <div class="row">
            <?php
            $country = Country::getData();
            foreach ($info as $val): ?>
                <?php $childInfo =  $val["information"]["basicsInfo"];
                 $parents = $childInfo->getParents();
                 $childExtraInfo =  $val["information"]["info"];
                 ?>
                <table class="table">
                    <tbody>
                    <tr><td>⼉童姓名</td><td><?php echo $childInfo->getChildName() ?></td></tr>
                    <tr><td>出⽣⽇期</td><td><?php echo $childInfo->birthday_search ?></td></tr>
                    <tr><td>身份证件类型（有中国户⼝报中国户⼝）</td><td><?php echo isset($childExtraInfo->idCardType) ? $childExtraInfo->idCardType : "" ?></td></tr>
                    <tr><td>身份证号码或护照号码</td><td><?php echo isset($childExtraInfo->idCard) ? $childExtraInfo->idCard : "" ?></td></tr>
                    <tr><td>⾎型</td><td><?php echo isset($childExtraInfo->bloodType) ? $childExtraInfo->bloodType : "" ?></td></tr>
                    <tr><td>国籍</td><td><?php echo $country[$childInfo->country] ?></td></tr>
                    <tr><td>⺠族</td><td><?php echo isset($childExtraInfo->minzu) ? $childExtraInfo->minzu : "" ?></td></tr>
                    <tr><td>出⽣所在地</td><td><?php echo isset($childExtraInfo->placeOfBirth) ? $childExtraInfo->placeOfBirth : "" ?></td></tr>
                    <tr><td>籍贯</td><td><?php echo isset($childExtraInfo->nativePlace) ? $childExtraInfo->nativePlace : "" ?></td></tr>
                    <tr><td>户⼝性质</td><td><?php echo isset($childExtraInfo->residentType) ? $childExtraInfo->residentType : "" ?></td></tr>
                    <tr><td>是否为独生子女</td><td><?php echo isset($childExtraInfo->isOnlyChild) ? $childExtraInfo->isOnlyChild : "" ?></td></tr>
                    <tr><td>户⼝所在地</td><td><?php echo isset($childExtraInfo->residentAddress) ? $childExtraInfo->residentAddress : "" ?></td></tr>
                    <tr><td>现住址</td><td><?php echo isset($childExtraInfo->address) ? $childExtraInfo->address : "";?></td></tr>

                    <tr><td>监护⼈姓名</td><td><?php echo isset($childExtraInfo->guardianName) ? $childExtraInfo->guardianName : "" ?></td></tr>
                    <tr><td>监护⼈身份证件号码</td><td><?php echo isset($childExtraInfo->guardianIdCard) ? $childExtraInfo->guardianIdCard : "" ?></td></tr>
                    <tr><td>监护⼈与⼉童关系</td><td><?php echo isset($childExtraInfo->guardianEelationship) ? $childExtraInfo->guardianEelationship : "" ?></td></tr>

                    <tr><td>⽗亲姓名</td><td><?php echo isset($childExtraInfo->fatherName) ? $childExtraInfo->fatherName : ""; ?></td></tr>
                    <tr><td>⽗亲联系⽅式</td><td><?php echo isset($childExtraInfo->fatherTel) ? $childExtraInfo->fatherTel : ""; ?></td></tr>
                    <tr><td>⽗亲邮箱</td><td><?php echo isset($childExtraInfo->fatherEmail) ? $childExtraInfo->fatherEmail : ""; ?></td></tr>
                    <tr><td>⺟亲姓名</td><td><?php echo isset($childExtraInfo->motherName) ? $childExtraInfo->motherName : ""; ?></td></tr>
                    <tr><td>⺟亲联系⽅式</td><td><?php echo isset($childExtraInfo->motherTel) ? $childExtraInfo->motherTel : ""; ?></td></tr>
                    <tr><td>⺟亲邮箱</td><td><?php echo isset($childExtraInfo->motherEmail) ? $childExtraInfo->motherEmail : ""; ?></td></tr>

                    <tr><td>过敏史（请详细列出过敏程度及治疗⽅法）</td><td><?php echo $childExtraInfo->allergy ?></td></tr>
                    <tr><td>请在此说明除过敏外任何既往病史（包括脱⾅，⻣折，哮喘等）</td><td><?php echo $childExtraInfo->medicalHistory ?></td></tr>
                    <tr><td>紧急联系⼈姓名1</td><td><?php echo $childExtraInfo->personName1 ?></td></tr>
                    <tr><td>紧急联系⼈关系1</td><td><?php echo $childExtraInfo->relationship1 ?></td></tr>
                    <tr><td>紧急联系⼈联系方式1</td><td><?php echo $childExtraInfo->contactNumber1 ?></td></tr>
                    <tr><td>紧急联系⼈姓名2</td><td><?php echo $childExtraInfo->personName2 ?></td></tr>
                    <tr><td>紧急联系⼈关系2</td><td><?php echo $childExtraInfo->relationship2 ?></td></tr>
                    <tr><td>紧急联系⼈联系方式2</td><td><?php echo $childExtraInfo->contactNumber2 ?></td></tr>
                    <tr><td>如遇紧急情况，学生需要医疗服务，而学校无法联系到上述联系人，请在下列横线注明家长希望把学生送至的医院或诊所名。若家长不指定任何医院，学生将被送往三甲公立医院就诊。如果家长指定医院不在三甲公立医院，家长将要承担相关费用。</td><td><?php echo $childExtraInfo->hospital ?></td></tr>
                    <tr><td>您是否同意学校将您孩⼦的照⽚⽤于市场推⼴？</td><td><?php echo $childExtraInfo->is_promote == 1 ? "是" : "否"  ?></td></tr>
                    </tbody>
                </table>
                <div class='page'>
                    <div><?php echo $val["agreement"]["informationPhotoCn"]; ?></div>
                    <div><?php echo $val["agreement"]["informationPhotoEn"]; ?></div>
                    <div class="sign"><img src="<?php echo $val["agreement"]["autograph"] ?>" alt=""></div>
                </div>
            <?php endforeach; ?>
        </div>
</div>
