<?php

/**
 * This is the model class for table "ivy_calendar_day".
 *
 * The followings are the available columns in table 'ivy_calendar_day':
 * @property integer $did
 * @property integer $date
 * @property integer $yid
 * @property integer $sid
 * @property integer $date_timestamp
 * @property integer $day_type
 * @property integer $event
 * @property string $title_cn
 * @property string $title_en
 * @property string $memo_cn
 * @property string $memo_en
 * @property integer $privacy
 * @property string $schoolid
 * @property integer $updated_timestamp
 * @property integer $userid
 */
class CalendarDay extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CalendarDay the static model class
	 */
	const DAY_HOLIDAY = 10;
	const DAY_EVENT = 20;
	const DAY_TOSCHOOLDAY = 30;
	const DAY_TYPE_FULL = 10;
	const DAY_TYPE_HALF = 20;

    public static function getTimeType(){
        return array(
            self::DAY_TYPE_FULL => Yii::t('labels', 'Full Day'),
            self::DAY_TYPE_HALF => Yii::t('labels', 'Half Day')
        );
    }

    public static function getDayType(){
        return array(
            self::DAY_HOLIDAY => Yii::t('labels', 'Holiday'),
            self::DAY_EVENT => Yii::t('labels', 'Event'),
            self::DAY_TOSCHOOLDAY => Yii::t('labels', 'Schoolday Make-up')
        );
    }

	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_calendar_day';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('date, yid, sid, day_type, event, userid', 'required'),
			array('date, yid, sid, date_timestamp, day_type, event, privacy, updated_timestamp, userid', 'numerical', 'integerOnly'=>true),
			array('title_cn, title_en', 'length', 'max'=>255),
			array('schoolid', 'length', 'max'=>10),
			array('memo_cn, memo_en', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('did, date, yid, sid, date_timestamp, day_type, event, title_cn, title_en, memo_cn, memo_en, privacy, schoolid, updated_timestamp, userid', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'did' => 'Did',
			'date' => 'Date',
			'yid' => 'Yid',
			'sid' => 'Sid',
			'date_timestamp' => 'Date Timestamp',
			'day_type' => Yii::t('labels', 'Calendar Time'),
			'event' => Yii::t('labels', 'Calendar Day Type'),
			'title_cn' => Yii::t('labels', 'Title (Chinese)'),
			'title_en' => Yii::t('labels', 'Title (English)'),
			'memo_cn' => Yii::t('labels', 'Desc (Chinese)'),
			'memo_en' => Yii::t('labels', 'Desc (English)'),
			'privacy' => Yii::t('labels', 'Staff Only'),
			'schoolid' => 'Schoolid',
			'updated_timestamp' => 'Updated Timestamp',
			'userid' => 'Userid',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('did',$this->did);
		$criteria->compare('date',$this->date);
		$criteria->compare('yid',$this->yid);
		$criteria->compare('sid',$this->sid);
		$criteria->compare('date_timestamp',$this->date_timestamp);
		$criteria->compare('day_type',$this->day_type);
		$criteria->compare('event',$this->event);
		$criteria->compare('title_cn',$this->title_cn,true);
		$criteria->compare('title_en',$this->title_en,true);
		$criteria->compare('memo_cn',$this->memo_cn,true);
		$criteria->compare('memo_en',$this->memo_en,true);
		$criteria->compare('privacy',$this->privacy);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('updated_timestamp',$this->updated_timestamp);
		$criteria->compare('userid',$this->userid);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/*
	 * 取校历范围内的假期和已转的教学天数
	 * @param int $calendarId
	 * @param int $startDate
	 * @param int $endDate
	 * @return array
	 */
	public function getHolidayAndSchoolDay($calendarId,$startDate,$endDate){
		$ret = array();
		$command = Yii::app()->db->createCommand();
		$command->select('date_timestamp,date,day_type,event')->from('ivy_calendar_day');
		$command->where('yid=:calendarId and date_timestamp>=:startDate and date_timestamp<=:endDate',array(':calendarId'=>$calendarId,':startDate'=>$startDate,':endDate'=>$endDate));
		$command->andWhere(array('in','event',array(CalendarDay::DAY_HOLIDAY,CalendarDay::DAY_TOSCHOOLDAY)));
		$list = $command->queryAll();
		if (is_array($list) && count($list)){
			foreach ($list as $val){
				$ret[$val['event']][$val['day_type']][$val['date_timestamp']] = $val['date'];
			}
		}
		return $ret;
	}
}