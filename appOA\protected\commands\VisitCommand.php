<?php

/*
 * To change this template, choose Tools | Templates
 */
class VisitCommand extends CConsoleCommand
{
    public function init() {
        parent::init();
        Yii::import('common.models.visit.*');
    }
    
    public function actionSendmail($branchId='', $targetDate=''){
        //current time
        if ($targetDate){
            $startTime = strtotime($targetDate);
        }else{
            $startTime = strtotime('today')+86400;
        }
        //get school info
        $criteria = new CDbCriteria();
        if(strtolower($branchId)!= 'all'){
			$criteria->compare('t.branchid', $branchId);
		}
        $criteria->compare('t.status', 10);
        $criteria->compare('t.type', 20);
        $criteria->index = 'branchid';
		$branches = Branch::model()->with('info')->findAll($criteria);
        foreach($branches as $branchId=>$branch){
			$criteria= new CDbCriteria;
            $criteria->compare('t.schoolid',$branchId);
            $criteria->compare('t.appointment_date',$startTime);
            $visitModel = IvyVisitsRecord::model()->with('basic')->findAll($criteria);
            if (!empty($visitModel)){
                
                $mailer = Yii::createComponent('common.extensions.mailer.EMailer');
               
                $mailer->AddAddress($branch->info->support_email);
                $mailer->AddReplyTo('<EMAIL>');
                $isProduction = defined("IS_PRODUCTION") ? IS_PRODUCTION : false;
                $mailer->Subject = sprintf('【 %s 预约参观提醒 - %s】%s', $isProduction ? "" : "测试", $branch->title, date('Y-m-d',$startTime));

                $mailP = ($isProduction) ? 'sendmail' : 'smtp';
                $mailer->iniMail($isProduction, $mailP); // 此行代码要放到AddAddress, AddCC方法下面
                $mailer->getCommandView('visit', array(
                    'result' => $visitModel,
                    'branch' => $branch,
                    'startTime' => $startTime,
                        ), 'main');
                if (!$mailer->Send()) {
                    print_r($mailer->getError());
                }else{
                    echo $branchId."...ok!\r\n";
                }
            }
        }
    }

    public function actionActiveDate()
    {
        $sql = "select * from ivy_visits_basic_info where status = 70 and active_date = ".strtotime('today');
        $row = Yii::app()->db->createCommand($sql)->queryAll();
        foreach($row as $rs){
            IvyVisitsBasicInfo::model()->updateByPk($rs['id'], array('status'=>10, 'state_timestamp'=>time(), 'update_timestamp'=>time()));
            echo $rs['id']."\n";
        }
    }

    // 导出最近一年的来访记录入学对比结果
    public function actionExportClv()
    {
        $sta = date('Y', time()) - 1 . "-09-01";
        $end = date('Y', time()) . "-11-30";

        $criteria = new CDbCriteria();
        $criteria->compare('register_timestamp', '>=' . strtotime($sta));
        $criteria->addCondition('recent_log >= ' . strtotime($end), 'OR');

        $count = IvyVisitsBasicInfo::model()->count($criteria);
        $batchNum = 200;
        $cycle = ceil($count / $batchNum);
        $csv = $this->_t('学校').",";
        $csv .= $this->_t('来访ID').",";
        $csv .= $this->_t('来访姓名').",";
        $csv .= $this->_t('孩子姓名').",";
        $csv .= $this->_t('来访状态').",";
        $csv .= $this->_t('是否转化').",";
        $csv .= $this->_t('孩子转化时间').",";
        $csv .= $this->_t('家长ID').",";
        $csv .= $this->_t('孩子ID')."\r\n";
        $visitStatus = IvyVisitsBasicInfo::status();

        $crit = new CDbCriteria();
        $crit->compare('status', 10);
        $crit->index = "branchid";
        $branchModel = Branch::model()->findAll($crit);


        for ($i = 0; $i < $cycle; $i++) {
            // echo $i*200 . "\r\n";
            $criteria->limit = $batchNum;
            $criteria->offset = $batchNum * $i;
            $basicModels = IvyVisitsBasicInfo::model()->findAll($criteria);
            foreach ($basicModels as $basic) {
                $flag = 0;
                $childid = '';
                $schoolid = '';
                $pid = '';
                $createdTimestamp = "";
                $tel = $basic->tel;
                $email = $basic->email;
                // 根据邮箱查找是否转化为系统孩子
                if ($email) {
                    $user = User::model()->findByAttributes(array('email' => $email));
                    if ($user) {
                        if ($user->parent) {
                            $pid = $user->parent->pid;
                            $childids = unserialize($user->parent->childs);
                            sort($childids);
                            $childid = $childids[0];
                            echo $email . $user->uid . "\r\n";
                        }
                    } else {
                        // 根据电话查找是否转化为系统孩子
                        if ($tel) {
                            $parent = IvyParent::model()->findByAttributes(array('mphone' => $tel));
                            if ($parent) {
                                $pid = $parent->pid;
                                $childids = unserialize($parent->childs);
                                sort($childids);
                                $childid = $childids[0];
                                echo $tel . $parent->pid . "\r\n";
                            }
                        }
                    }
                }
                if ($childid) {
                    $flag = 1;
                    $childModel = ChildProfileBasic::model()->findByPk($childid);
                    if ($childModel) {
                        $schoolid = $childModel->schoolid;
                        $createdTimestamp = date("Y-m-d", $childModel->created_timestamp);
                    }
                } else {
                    $crit = new CDbCriteria;
                    $crit->compare('basic_id', $basic->id);
                    $crit->order = 'update_timestamp DESC';
                    $record = IvyVisitsRecord::model()->find($crit);
                    if ($record) {
                        $schoolid = $record->schoolid;
                    }
                }
                $csv .= $this->_t($branchModel[$schoolid]->title).",";
                $csv .= $this->_t($basic->id).",";
                $csv .= $this->_t($basic->parent_name).",";
                $csv .= $this->_t($basic->child_name).",";
                $csv .= $this->_t($visitStatus[$basic->status]).",";
                $csv .= $this->_t($flag).",";
                $csv .= $this->_t($createdTimestamp).",";
                $csv .= $this->_t($pid).",";
                $csv .= $this->_t($childid)."\r\n";
            }
        }
        file_put_contents(time().'.csv', $csv);
    }

    public function actionVisitingRecord()
    {

        $criteria = new CDbCriteria();
        $criteria->select = 'branchid';
        $criteria->compare('status', 10);
        $criteria->compare('type', array(20,50));
        $schoolLost = Branch::model()->findAll($criteria);

        $num = 1;
        foreach ($schoolLost as $item) {
            $criteria = new CDbCriteria();
            $criteria->compare('schoolid', $item->branchid);
            $criteria->group = 'id ASC';
            $criteria->index = 'basic_id';
            $count = IvyVisitsRecord::model()->count($criteria);

            $batchNum = 200;
            $cycle = ceil($count / $batchNum);
            for ($i = 0; $i < $cycle; $i++) {
                $criteria = new CDbCriteria();
                $criteria->compare('schoolid', $item->branchid);
                $criteria->group = 'id ASC';
                $criteria->index = 'basic_id';
                $criteria->limit = $batchNum;
                $criteria->offset = $batchNum * $i;
                $visitsRecordModels = IvyVisitsRecord::model()->findAll($criteria);
                foreach ($visitsRecordModels as $k => $val) {
                    $criteria = new CDbCriteria;
                    $criteria->compare('school_id',$item->branchid);
                    $criteria->compare('basic_id', $val->basic_id);
                    $model = VisitsBasicLink::model()->find($criteria);
                    if($model){
                        $model->visit_id = $val->id;
                        if (!$model->save()) {
                            $err = current($model->getErrors());
                            echo $err[0];
                            exit;
                        }
                        echo $item->branchid . ' - ' . $num . ' - OK' . "\n";
                        $num++;
                    }else{
                        $model = new VisitsBasicLink();
                        $model->school_id = $item->branchid;
                        $model->basic_id = $val->basic_id;
                        $model->visit_id = $val->id;
                        $model->status = $val->basic->status;
                        $model->updated_at = time();
                        $model->updated_by = 5;
                        if (!$model->save()) {
                            $err = current($model->getErrors());
                            echo $err[0];
                            exit;
                        }
                        echo $item->branchid . ' - ' . $num . ' - OK' . "\n";
                        $num++;
                    }
                }
            }
        }
    }

    public function _t($str='')
    {
        $str = "\"".$str."\"";
        $str = str_replace(',', '，', $str);
        return iconv("UTF-8", "GBK", $str);
    }

    /**
     *
     * Grade 1 Progress Report (v2018)
     * 小学1年级评估报告模版 (v2018)
     * 测试SoapClient用
     */
    public function ActionSs()
    {
        $id = 26640;
        $classid = 926;
        $security = 'I3v4y7Onl8ine6Mi433ms';
        $html = 'Test';
        $html = base64_encode($html);
        $client = new SoapClient("http://m1.files.ivykids.cn/pdf");
        $result = $client->html2pdf(
            $id,
            $classid,
            $html,
            md5($id.$classid.$html.$security)
        );
        echo $result."/n";
    }
}
