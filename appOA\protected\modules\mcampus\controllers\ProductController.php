<?php

class ProductController extends BranchBasedController
{

    public $childsName;
    public $childsNextClass;
    public $productsOutofStockListCount = array();
    public $userName;
    public $className;
    public $productsAttrs;
    public $printFW = array();
    public $actionAccessAuths = array(
        'index' => 'o_A_Access',
        'refund' => 'ivystaff_opschool',
    );

    //需要检查是否有保存权限的action
    private $needFinalPostCheck = array(
        'productscat',//更新商品分类
        'updateproducts',//更新商品
        'productsattr',//更新商品属性
        'updatastock',//更新库存
        'updateattrstatus',//修改属性状态
        'deleteproducts',//删除商品
        'updateprice',//修改价格
        'tag',//属性修改
        'schoolshared',//跨校园分享
        'confirmorder',//确认发放
        'updatephoto',//增加图片
        'deletephoto',//删除图片
        'saveoutofstock',//确认到货
        'refund',//退款状态
    );

    public function beforeAction(CAction $action)
    {
        parent::beforeAction($action);
        $actionId = strtolower($action->getId());
        if (in_array($actionId, $this->needFinalPostCheck)) {
            $valid = Yii::app()->user->checkAccess('ivystaff_opschool');
            if (!$valid) {
                if (Yii::app()->request->isPostRequest) {
                    $ret['state'] = 'fail';
                    $ret['message'] = Yii::t("message", 'No permission');
                    echo CJSON::encode($ret);
                    Yii::app()->end();
                }
            }
        }
        return true;
    }

    public function createUrl($route, $params = array(), $ampersand = '&')
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        return parent::createUrl($route, $params, $ampersand);
    }

    public function init()
    {
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";
        Yii::import('common.models.products.*');

        $this->modernMenuFlag = 'campusOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site', 'Campus Workspace');

        //初始化选择校园页面
        $this->branchSelectParams['hideOffice'] = false;
        $this->branchSelectParams['urlArray'] = array('//mcampus/product/index');
        $cs = Yii::app()->clientScript;
        $cs->registerCoreScript('jquery.ui');
        $cs->registerCssFile($cs->getCoreScriptUrl() . '/jui/css/base/jquery-ui.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/vue2.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/plupload/plupload.full.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/xlsx.full.min.js');
    }


    public function actionIndex()
    {
        $pcid = Yii::app()->request->getParam('pcid', '');
        $productsCatObj = Products::getProductsCat($this->branchId);
        $dataProvider = array();
        $catShare = array();
        $invoiceCont = 0;
        if ($pcid) {
            $model = new Products();
            $criteria = new CDbCriteria;
            $criteria->compare('cid', $pcid);
            $criteria->compare('status', "<>99");
            $dataProvider = new CActiveDataProvider($model, array(
                'criteria' => $criteria,
                'pagination' => array('pageSize' => 20),
                'sort' => array(
                    'defaultOrder' => 't.sort ASC',
                ),
            ));

            $criteria = new CDbCriteria;
            $criteria->compare('status', 0);
            $criteria->compare('school_id', $this->branchId);
            $criteria->compare('cid', $pcid);
            $productsOutofStock = ProductsOutofStock::model()->findAll($criteria);
            if ($productsOutofStock) {
                foreach ($productsOutofStock as $val) {
                    $this->productsOutofStockListCount[$val->pid] += 1;
                }
            }
            //分类所属学校
            $criteria = new CDbCriteria;
            $criteria->compare('id', $pcid);
            $ProductsCat = ProductsCat::model()->find($criteria);

            //共享库存 和推荐
            $criteria = new CDbCriteria;
            $criteria->compare('cid', $pcid);
            if ($ProductsCat->school_id != $this->branchId) {
                $criteria->compare('school_id', $this->branchId);
            }
            $catShare = ProductsCatShare::model()->find($criteria);
        }
        //查询各商品的待处理订单数量
        $criteria = new CDbCriteria;
        $criteria->compare('status', ProductsInvoice::STATS_PAID);//待处理
        $criteria->compare('school_id', $this->branchId);
        $criteria->select          = 'cid,COUNT(*) as num';//必须是表里面存在的字段否则无法取到值
        $criteria->group           = 'cid';
        $criteria->index           = 'cid';
        $pending_invoice = ProductsInvoiceItem::model()->findAll($criteria);
        $productOrderCount = array();
        foreach ($pending_invoice as $pcid_key => $item){
            $productOrderCount[$pcid_key] = $item->num;
        }
        $invoiceCont = empty($productOrderCount[$pcid]) ? 0 : $productOrderCount[$pcid];
        $this->render('index', array(
            'productsCatObj' => $productsCatObj,
            'pcid' => $pcid,
            'dataProvider' => $dataProvider,
            'catShare' => $catShare,
            'schoolId' => $this->branchId,
            'invoiceCont' => $invoiceCont,
            'productOrderCount' => $productOrderCount,
        ));
    }

    // 增加商品分类
    public function actionProductsCat()
    {
        $pcid = Yii::app()->request->getParam('pcid', '');
        $productsCat = ProductsCat::model()->findByPk($pcid);

        if (empty($productsCat)) {
            $productsCat = new ProductsCat();
        }

        if (Yii::app()->request->isPostRequest) {
            $productsCat->photo = CUploadedFile::getInstance($productsCat, 'photo');
            $productsCat->attributes = $_POST['ProductsCat'];
            $productsCat->school_id = $this->branchId;
            $productsCat->updated_time = time();
            $productsCat->updated_userid = Yii::app()->user->id;
            if ($productsCat->save()) {
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message', 'success'));
                $this->addMessage('callback', 'cbSuccess');
            } else {
                $error = current($productsCat->getErrors());
                $this->addMessage('state', 'fail');
                $this->addMessage('message', $error[0]);
            }
            $this->showMessage();
        }

        $this->renderpartial('update', array(
            'productsCat' => $productsCat,
        ));
    }

    //增加商品
    public function actionUpdateProducts()
    {
        $id = Yii::app()->request->getParam('id', '');
        $pcid = Yii::app()->request->getParam('pcid', '');

        $model = Products::model()->findByPk($id);
        if (empty($model)) {
            $model = new Products();
        }
        if (Yii::app()->request->isPostRequest) {
            $model->attributes = $_POST['Products'];
            $model->school_id = $this->branchId;
            $model->updated_time = time();
            $model->updated_userid = Yii::app()->user->id;
            if ($model->save()) {
                $criteria = new CDbCriteria;
                $criteria->compare('pid', $id);
                $criteria->compare('school_id', $this->branchId);
                $ProductsFeatured = ProductsFeatured::model()->find($criteria);
                if ($_POST['Products']['Featured'] == 1 && empty($ProductsFeatured)) {
                    #设置推荐
                    $ProductsFeatured = new ProductsFeatured();
                    $ProductsFeatured->pid = $id;
                    $ProductsFeatured->school_id = $this->branchId;
                    $ProductsFeatured->updated_time = time();
                    $ProductsFeatured->updated_userid = $this->staff->uid;
                    $ProductsFeatured->save();
                } elseif ($_POST['Products']['Featured'] == 0 && !empty($ProductsFeatured)) {
                    #取消推荐
                    $ProductsFeatured->delete();
                }
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message', 'success'));
                $this->addMessage('callback', 'cbSuccess');
            } else {
                $error = current($model->getErrors());
                $this->addMessage('state', 'fail');
                $this->addMessage('message', $error[0]);
            }
            $this->showMessage();
        }

        $this->renderpartial('updateProducts', array(
            'model' => $model,
            'pcid' => $pcid,
        ));
    }

    //增加商品属性
    public function actionProductsAttr()
    {
        $config = Products::config();
        if (Yii::app()->request->isPostRequest) {
            $num = count($config[$_POST['attrType']]['item']); // 要增加属性每个必须有的数量
            $status = 0;
            $attr = array();
            foreach ($_POST['type'] as $key => $item) {
                if (count(array_filter(array_values($item))) != $num && array_filter(array_values($item))) {
                    $status = 1;
                } else if (count(array_filter(array_values($item))) == $num) {
                    $attr[$key] = $item;
                }
            }
            if ($status) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', "属性未填全, 属性或者全部填写,或者全部不填写");
                $this->showMessage();
            }
            if ($attr) {
                $counts = 0;

                foreach ($attr as $k => $v) {
                    $criteria = new CDbCriteria;
                    $criteria->compare('pid', $_POST['paid']);
                    foreach ($v as $key => $Item) {
                        $_attr = "attr" . $key;
                        $criteria->compare($_attr, $Item);
                    }
                    $count = ProductsAttr::model()->count($criteria);
                    if ($count) {
                        $counts += $count;
                    }
                }
                if ($counts) {
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', "增加属性中, 已经有过增加");
                    $this->showMessage();
                }

                foreach ($attr as $key => $t) {
                    $model = new ProductsAttr();
                    $model->pid = $_POST['paid'];
                    $model->type = $_POST['attrType'];
                    foreach ($t as $k => $attr) {
                        $_attr = "attr" . $k;
                        $model->$_attr = $attr;
                    }
                    $model->unit_price = $_POST['price'][$key];
                    $model->status = 1;
                    $model->save();

                    $productsModel = new ProductsStock();
                    $productsModel->cid = $model->products->cid;
                    $productsModel->pid = $model->pid;
                    $productsModel->paid = $model->id;
                    $productsModel->num = 0;
                    $productsModel->school_id = $this->branchId;
                    $productsModel->updated_userid = Yii::app()->user->id;
                    $productsModel->updated_time = time();
                    $productsModel->save();
                }
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message', 'success'));
                $this->addMessage('callback', 'cbSuccess');
                $this->showMessage();
            } else {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', "未填写任何属性");
                $this->showMessage();
            }
        }
    }

    // 跨校园共享
    public function actionSchoolShared()
    {
        $schoolId = Yii::app()->request->getParam('schoolId', array());
        $cid = Yii::app()->request->getParam('cid', array());
        $schoolAll = Branch::model()->getBranchList(null, true);
        $allBranch = array();
        if (in_array($this->staff->profile->branch, array('BJ_TYG', 'BJ_SS', 'BJ_BU'))) {
            foreach ($schoolAll as $k => $schoolItem) {
                if ($this->branchId == $schoolItem['id']) {
                    continue;
                }
                $allBranch[$schoolItem['id']] = $schoolItem['title'];
            }
        } else {
            $schoolAll = Branch::model()->findAllByPk($this->adminBranch);
            foreach ($schoolAll as $k => $schoolItem) {
                if ($schoolItem->status != 10 || $this->branchId == $schoolItem->branchid) {
                    continue;
                }
                $allBranch[$schoolItem->branchid] = $schoolItem->title;
            }
        }
        $criteria = new CDbCriteria;
        $criteria->compare('cid', $cid);
        $modelObj = ProductsCatShare::model()->findAll($criteria);
        $checkAll = array();
        if ($modelObj) {
            foreach ($modelObj as $item) {
                $checkAll[] = $item->school_id;
            }
        }
        $model = new ProductsCatShare;
        if (Yii::app()->request->isPostRequest) {
            if ($modelObj) {
                foreach ($modelObj as $item) {
                    $item->delete();
                }
            }
            if ($schoolId) {
                $share_stock = $_POST['ProductsCatShare']['share_stock'];
                $share_featured = $_POST['ProductsCatShare']['share_featured'];
                if (!empty($share_featured) && empty($share_stock)) {
                    $this->addMessage('state', 'success');
                    $this->addMessage('message', Yii::t('message', 'success'));
                    $this->showMessage();
                    return false;
                }
                foreach ($schoolId as $siteItem) {
                    $model = new ProductsCatShare();
                    $model->cid = $cid;
                    $model->school_id = $siteItem;
                    $model->share_stock = $share_stock;
                    $model->share_featured = $share_featured;
                    $model->updated_time = time();
                    $model->updated_userid = Yii::app()->user->id;
                    $model->save();
                    //设置共享库存的校园
                    $criteria = new CDbCriteria;
                    $criteria->compare('cid', $cid);
                    $share_stock_product = Products::model()->findAll($criteria);
                    $pid = array();
                    foreach ($share_stock_product as $item) {
                        $pid[] = $item->id;
                    }
                    if ($share_stock && $share_featured) {
                        //删除自主设置的首页推荐商品
                        $criteria = new CDbCriteria;
                        $criteria->compare('pid', $pid);
                        $criteria->compare('school_id', $siteItem);
                        ProductsFeatured::model()->deleteAll($criteria);
                    }
                    //设置学校库存
                    //所有商品的属性
                    if(!empty($pid)){
                        $criteria = new CDbCriteria;
                        $criteria->compare('pid', $pid);
                        $criteria->index = 'id';
                        $attrObj = ProductsAttr::model()->findAll($criteria);
                        foreach ($attrObj as $attrObjItem){
                            $stockUpdateModel = ProductsStock::model()->findByAttributes(array('cid'=>$cid,'pid'=>$attrObjItem['pid'],'paid'=>$attrObjItem['id']));
                            if(!$stockUpdateModel){
                                $stockUpdateModel = new ProductsStock();
                                $stockUpdateModel->cid = $cid;
                                $stockUpdateModel->pid = $attrObjItem['pid'];
                                $stockUpdateModel->paid = $attrObjItem['id'];
                                $stockUpdateModel->num = 0;
                                $stockUpdateModel->school_id = $siteItem;
                                $stockUpdateModel->updated_userid = Yii::app()->user->id;
                                $stockUpdateModel->updated_time = time();
                                $stockUpdateModel->save();
                            }
                        }
                    }
                }
            }
            $this->addMessage('state', 'success');
            $this->addMessage('message', Yii::t('message', 'success'));
            $this->addMessage('callback', 'cbSchoolshare');
            $this->showMessage();
        }
        //总部设置库存共享可以设置选中的所有分享学校，但不能分开设置，例如选中三个分享学校 只设置两个学校共享库存
        $criteria = new CDbCriteria;
        $criteria->compare('cid', $cid);
        $CatShare = ProductsCatShare::model()->find($criteria);

        $this->renderPartial('schoolShared', array(
            'model' => $model,
            'cid' => $cid,
            'allBranch' => $allBranch,
            'checkAll' => $checkAll,
            'share_stock' => empty($CatShare) ? $model : $CatShare,
            'share_featured' => empty($CatShare) ? $model : $CatShare,
        ));
    }


    // 增加库存
    public function actionUpdataStock()
    {
        $pid = Yii::app()->request->getParam('pid', ''); // 商品ID

        $model = Products::model()->findByPk($pid);

        $criteria = new CDbCriteria;
        $criteria->compare('pid', $pid);
        $criteria->index = 'id';
        $attrObj = ProductsAttr::model()->findAll($criteria); //所有商品的属性
        $config = Products::config();
        $arrtType = array();
        //是否为共享库存商品 共享库存商品不能管理库存只可以查看
        $is_share_stock = false;
        $cid = $model->cid;//被分享的商品cat_id
        $criteria = new CDbCriteria;
        $criteria->compare('cid', $cid);
        $criteria->compare('school_id', $this->branchId);
        $catShare = ProductsCatShare::model()->find($criteria);
        if (!empty($catShare->share_stock)) {
            $is_share_stock = true;
        }
        $school_id = $this->branchId;
        if ($is_share_stock) {
            $school_id = $model->school_id;
        }
        //分类
        $productsCatModel = ProductsCat::model()->findByPk($cid);

        if ($attrObj) {
            $share_stock = '';
            if ($is_share_stock) {
                //共享库存
                $criteria = new CDbCriteria;
                $criteria->compare('paid', array_keys($attrObj));
                $criteria->compare('school_id', $model->school_id);
                $criteria->index = 'paid';
                $share_stock = ProductsStock::model()->findAll($criteria); // 当前商品 当前校园下所有库存   根据商品ID 作为下标
            }
            //本校库存
            $criteria = new CDbCriteria;
            $criteria->compare('paid', array_keys($attrObj));
            $criteria->compare('school_id', $this->branchId);
            $criteria->index = 'paid';
            $stockObj = ProductsStock::model()->findAll($criteria); // 当前商品 当前校园下所有库存   根据商品ID 作为下标

            $criteria = new CDbCriteria;
            $criteria->compare('status', 0);
            $criteria->compare('school_id', $school_id);
            $criteria->compare('aid', array_keys($attrObj));
            $productsOutofStock = ProductsOutofStock::model()->findAll($criteria);

            foreach ($attrObj as $attrItem) {
                $i = 1;
                foreach ($config[$attrItem->products->type]['item'] as $ck => $cv) {
                    $attrIndex = 'attr' . $i;
                    $arrt[$attrItem->id][] = Yii::t('products', $cv['option'][$attrItem->$attrIndex]);
                    $i++;
                }
                $count = 0;
                if ($productsOutofStock) {
                    foreach ($productsOutofStock as $val) {
                        if ($val->aid == $attrItem->id) {
                            $count += 1;
                        }
                    }
                }

                $arrtType[$attrItem->id] = array(
                    'id' => $attrItem->id,
                    'title' => $attrItem->type,
                    'count' => $count,
                    'status' => $attrItem->status,
                    'price' => $attrItem->unit_price,
                    'scribing_price' => $attrItem->scribing_price,
                    'stock' => (isset($stockObj) && isset($stockObj[$attrItem->id])) ? $stockObj[$attrItem->id]->num : 0,
                    'share_stock' => (isset($share_stock) && isset($share_stock[$attrItem->id])) ? $share_stock[$attrItem->id]->num : 0,
                    //'operating' => CHtml::link(Yii::t('curriculum','库存管理'), array('updatestock', 'paid' => $attrItem->id, "branchId"=>Yii::app()->controller->branchId), array('class' => 'J_modal btn btn-xs btn-info')),
                );
            }
        }

        $this->render('stock', array(
            'arrtType' => $arrtType,
            'pid' => $pid,
            'model' => $model,
            'branchId' => $this->branchId,
            'arrt' => $arrt,
            'is_share_stock' => $is_share_stock,
            'productsCatModel' => $productsCatModel,
        ));
    }

    // 修改属性状态
    public function actionUpdateAttrStatus()
    {
        $id = Yii::app()->request->getParam('id', '');
        $status = Yii::app()->request->getParam('status', '');
        if (Yii::app()->request->isPostRequest) {
            $modelArrt = ProductsAttr::model()->findByPk($id);
            if ($modelArrt) {
                $modelArrt->status = ($status) ? 0 : 1;
                if ($modelArrt->save()) {
                    $this->addMessage('state', 'success');
                    $this->addMessage('message', Yii::t('message', 'success'));
                } else {
                    $error = current($modelArrt->getErrors());
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', $error[0]);
                }
                $this->showMessage();
            }
        }
    }

    //删除属性 只可以删除没有订单的属性
    public function actionRemoveAttr()
    {
        $id = Yii::app()->request->getParam('id', '');
        if (Yii::app()->request->isPostRequest) {
            //是否有订单
            $criteria = new CDbCriteria;
            $criteria->compare('paid', $id);
            $ProductsInvoiceItem = ProductsInvoiceItem::model()->findAll($criteria);
            if(empty($ProductsInvoiceItem)){
                if(ProductsAttr::model()->deleteByPk($id)){
                    $criteria = new CDbCriteria;
                    $criteria->compare('paid', $id);
                    $criteria->compare('school_id', $this->branchId);
                    $ProductsStock = ProductsStock::model()->find($criteria);
                    if(!empty($ProductsStock)){
                        $ProductsStock->delete();
                    }
                    $this->addMessage('state', 'success');
                    $this->addMessage('message', Yii::t('message', 'success'));
                }else{
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', 'fail');
                }
            }else{
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '已有订单不可删除！');
            }
            $this->showMessage();
        }
    }

    //获取已经保存的图片信息
    public function actionGetPhoto(){
        $pid = Yii::app()->request->getParam('pid', '');
        $filePath = Yii::app()->params['OAUploadBaseUrl'] . '/products/';
        $criteria = new CDbCriteria;
        $criteria->compare('pid', $pid);
        $criteria->order = 'sort asc';
        $productsImgModel = ProductsImg::model()->findAll($criteria);
        foreach ($productsImgModel as $k=>$v){
            $productsImgModel[$k]['img'] = $filePath.$v['img'];
        }
        $this->addMessage('state', 'success');
        $this->addMessage('data', $productsImgModel);
        $this->showMessage();
    }

    //设置顺序
    public function actionSetPhotoSort()
    {
        $data = Yii::app()->request->getParam('data', '');
        if (Yii::app()->request->isPostRequest) {
            foreach ($data as $k=>$paid){
                $productsImgModel = ProductsImg::model()->findByPk($paid);
                $productsImgModel->sort = $k+1;
                $productsImgModel->save();
            }
            $this->addMessage('state', 'success');
            $this->addMessage('message', 'success!');
            $this->showMessage();
        }
    }

    public function actionUpdatestock()
    {
        $paid = Yii::app()->request->getParam('paid', ''); // 商品属性ID

        $attrModel = ProductsAttr::model()->findByPk($paid);

        $criteria = new CDbCriteria;
        $criteria->compare('paid', $paid);
        $criteria->compare('school_id', $this->branchId);
        $stockModel = ProductsStock::model()->find($criteria);
        if (empty($stockModel)) {
            $stockModel = new ProductsStock();
        }
        $dataProvider = array();
        if ($stockModel->id) {
            $model = new ProductsStockItem();
            $criteria = new CDbCriteria;
            $criteria->compare('psid', $stockModel->id);
            $criteria->compare('school_id', $this->branchId);
            $dataProvider = new CActiveDataProvider($model, array(
                'criteria' => $criteria,
                'pagination' => array('pageSize' => 20),
                'sort' => array(
                    'defaultOrder' => 't.updated_time DESC',
                ),
            ));
        }
        if ($dataProvider) {
            $userId = array();
            foreach ($dataProvider->getData() as $user) {
                $userId[$user->updated_userid] = $user->updated_userid;
            }

            if ($userId) {
                $userModel = User::model()->findAllByPk($userId);
                foreach ($userModel as $item) {
                    $this->userName[$item->uid] = $item->getName();
                }
            }
        }

        $criteria = new CDbCriteria;
        $criteria->compare('aid', $paid);
        $criteria->compare('status', 0);
        $criteria->compare('school_id', $this->branchId);
        $count = ProductsOutofStock::model()->count($criteria);

        if (Yii::app()->request->isPostRequest) {
            $num = Yii::app()->request->getParam('num', '');
            $isArrival = Yii::app()->request->getParam('isArrival', 0);
            $memo = Yii::app()->request->getParam('memo', '');
            $psid = Yii::app()->request->getParam('psid', ''); // 商品ID


            if (!is_numeric($num)) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '请输入数字');
                $this->showMessage();
            }
            if(empty($num)){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '请输入数量');
                $this->showMessage();
            }

            if (empty($memo)) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '备注不能为空');
                $this->showMessage();
            }

            if ($psid) {
                $stockUpdateModel = ProductsStock::model()->findByPk($psid);
                $stockUpdateModel->num = $stockUpdateModel->num + $num;
            } else {
                $stockUpdateModel = new ProductsStock();
                $stockUpdateModel->cid = $attrModel->products->cid;
                $stockUpdateModel->pid = $attrModel->pid;
                $stockUpdateModel->paid = $paid;
                $stockUpdateModel->num = $num;
                $stockUpdateModel->school_id = $this->branchId;
                $stockUpdateModel->updated_userid = Yii::app()->user->id;
                $stockUpdateModel->updated_time = time();
            }

            if ($stockUpdateModel->save()) {
                $stockItemModel = new ProductsStockItem();
                $stockItemModel->cid = $stockUpdateModel->cid;
                $stockItemModel->pid = $stockUpdateModel->pid;
                $stockItemModel->paid = $stockUpdateModel->paid;
                $stockItemModel->psid = $stockUpdateModel->id;
                $stockItemModel->num = $num;
                $stockItemModel->memo = $memo;
                $stockItemModel->school_id = $this->branchId;
                $stockItemModel->updated_userid = Yii::app()->user->id;
                $stockItemModel->updated_time = time();
                if ($stockItemModel->save()) {
                    if ($isArrival) {
                        $crit = new CDbCriteria;
                        $crit->compare('status', 0);
                        $crit->compare('school_id', $this->branchId);
                        $crit->compare('cid', $stockItemModel->cid);
                        $crit->compare('pid', $stockItemModel->pid);
                        $crit->compare('aid', $stockItemModel->paid);
                        $models = ProductsOutofStock::model()->findAll($crit);
                        $childList = array();
                        foreach ($models as $val) {
                            $val->stock_id = $stockItemModel->id;
                            $val->status = 1;
                            $val->updated_by = time();
                            $val->updated_at = Yii::app()->user->id;
                            $val->save();
                            $childList[] = $val->child_id;
                        }

                        $emailList = array();
                        $model = ChildProfileBasic::model()->findAllByPk($childList);
                        foreach ($model as $val) {
                            $parent = $val->getParents();
                            $rule = '/([\w\.\_]{2,10})@(\w{1,}).([a-z]{2,4})/';
                            if (isset($parent['mother']) && preg_match($rule, $parent['mother']->email)) {
                                $emailList[] = $parent['mother']->email;
                            }
                            if (isset($parent['father']) && preg_match($rule, $parent['father']->email)) {
                                $emailList[] = $parent['father']->email;
                            }
                        }
                        if ($emailList) {
                            $config = Products::config();
                            $mailer = Yii::createComponent('common.extensions.mailer.EMailer');
                            $mailer->Subject = "校服到货提醒";
//                            foreach ($emailList as $email){
//                                $mailer->AddAddress($email);
//                            }
                            //$mailer->AddCC($this->branchObj->info->support_email);
                            foreach ($emailList as $key => $email) {
                                $mailer->AddBcc($email);
                            }
                            //$mailer->AddAddress("<EMAIL>");
                            $mailer->getView('noticeArrival', array(
                                "title_cn" => $stockUpdateModel->product->title_cn,
                                "title_en" => $stockUpdateModel->product->title_en,
                                "num" => $num,
                                "color" => $stockUpdateModel->attr->attr1,
                                "size" => $config["uniform"]["item"]["size"]["option"][$stockUpdateModel->attr->attr2]
                            ), 'todsparent');
                            $mailer->iniMail(OA::isProduction()); // 此行代码要放到AddAddress, AddCC方法下面
                            $mailer->Send();
                        }
                    }
                    $this->addMessage('state', 'success');
                    $this->addMessage('message', Yii::t('message', 'success'));
                    $this->addMessage('callback', 'cbSchoolshare');
                    $this->showMessage();
                } else {
                    $error = current($stockItemModel->getErrors());
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', $error[0]);
                    $this->showMessage();
                }
            } else {
                $error = current($stockUpdateModel->getErrors());
                $this->addMessage('state', 'fail');
                $this->addMessage('message', $error[0]);
                $this->showMessage();
            }

        }

        $this->renderPartial('updateStock', array(
            'stockModel' => $stockModel,
            'dataProvider' => $dataProvider,
            'paid' => $paid,
            'count' => $count,
        ));
    }

    //修改价格
    public function actionUpdatePrice()
    {
        $paid = Yii::app()->request->getParam('paid', '');
        $price = Yii::app()->request->getParam('price', '');
        $scribing = Yii::app()->request->getParam('scribing', 0);
        if (Yii::app()->request->isPostRequest) {
            $modelArrt = ProductsAttr::model()->findByPk($paid);
            if ($modelArrt) {
                if ($scribing == 1) {
                    $modelArrt->scribing_price = $price;
                } else {
                    $modelArrt->unit_price = $price;
                }
                if ($modelArrt->save()) {
                    $this->addMessage('state', 'success');
                    $this->addMessage('message', Yii::t('message', 'success'));
                } else {
                    $error = current($modelArrt->getErrors());
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', $error[0]);
                }
                $this->showMessage();
            }
        }
    }

    public function actionShowPhoto()
    {
        $pid = Yii::app()->request->getParam('pid', '');

        $criteria = new CDbCriteria;
        $criteria->compare('pid', $pid);
        $criteria->order = 'sort';
        $productsImgModel = ProductsImg::model()->findAll($criteria);

        $proudctsModel = Products::model()->findByPk($pid);
        $this->renderPartial('updatePhoto', array(
            'pid' => $pid,
            'proudctsModel' => $proudctsModel,
            'productsImgModel' => $productsImgModel,
            'branchId' => $this->branchId,
        ));
    }

    public function actionShowQrCode()
    {
        $pid = Yii::app()->request->getParam('pid', '');
        $data = CommonUtils::requestDsOnline('product/getQrCode', array(
            'school_id' => $this->branchId,
            'pid' => $pid,
        ));
        $this->renderPartial('qrCode', array(
            'data' => $data,
        ));
    }

    //图片打标签
    public function actionTag()
    {
        if (Yii::app()->request->isPostRequest) {
            $model = ProductsImg::model()->findByPk($_POST['imgId']);
            unset($_POST['imgId']);
            $ImgTag = (array_filter(array_values($_POST))) ? implode(";", $_POST) : "";

            if ($model) {
                $model->tag = $ImgTag;
                if ($model->save()) {
                    $this->addMessage('state', 'success');
                    $this->addMessage('message', Yii::t('message', 'success'));
                } else {
                    $error = current($model->getErrors());
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', $error[0]);
                }
                $this->showMessage();
            } else {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '参数错误');
                $this->showMessage();
            }
        }
    }

    // 增加图片
    public function actionUpdatePhoto()
    {
        $pid = Yii::app()->request->getParam('pid', '');

        if (Yii::app()->request->isPostRequest) {
            $file = CUploadedFile::getInstanceByName('file');
            if ($file) {
                $filePath = Yii::app()->params['OAUploadBasePath'] . '/products/';
                $fileName = strtolower('products_' . uniqid() . '.' . $file->getExtensionName());
                $file->saveAs($filePath . $fileName);

                Yii::import('application.extensions.image.Image');
                $image = new Image($filePath . $fileName);
                $image->quality(100);
                $image->resize(500, 500);
                $image->save($filePath . $fileName);

                $image = new Image($filePath . $fileName);
                $min = min($image->__get('width'), $image->__get('height'));
                $image->crop($min, $min);
                $image->resize(80, 80);
                $image->save($filePath . 'thumbs/' . $fileName);

            }

            $model = new ProductsImg();
            $model->img = $fileName;
            $model->pid = $pid;
            if ($model->save()) {
                echo CJSON::encode(array(
                    'url' => Yii::app()->params['OAUploadBaseUrl'] . '/products/' . $fileName . '?v' . time(),
                    'id' => $model->id,
                ));
            }
        }
    }

    // 根据商品图品ID 删除图片
    public function actionDeletePhoto()
    {
        $imgId = Yii::app()->request->getParam('imgId', '');
        $filePath = Yii::app()->params['OAUploadBasePath'] . '/products/';
        if (Yii::app()->request->isPostRequest) {
            $productsImgModel = ProductsImg::model()->findByPk($imgId);
            if ($productsImgModel) {
                $img = $productsImgModel->img;
                $id = $productsImgModel->id;
                if ($productsImgModel->delete()) {
                    unlink($filePath . $img);
                    unlink($filePath . 'thumbs/' . $img);
                    $this->addMessage('state', 'success');
                    $this->addMessage('message', Yii::t('message', 'success'));
                    $this->addMessage('data', array('imgId' => $id . '_photo'));
                    $this->addMessage('callback', 'cbSuccess');
                    $this->showMessage();
                }
            }
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '参数错误');
            $this->addMessage('callback', 'cbSuccess');
            $this->showMessage();
        }

    }

    //设置首页推荐
    public function actionHomePage()
    {
        $id = Yii::app()->request->getParam('id', '');
        $criteria = new CDbCriteria;
        $criteria->compare('pid', $id);
        $criteria->compare('school_id', $this->branchId);
        $model = ProductsFeatured::model()->find($criteria);
        if (empty($model)) {
            $ProductsFeatured = new ProductsFeatured();
            $ProductsFeatured->pid = $id;
            $ProductsFeatured->school_id = $this->branchId;
            $ProductsFeatured->updated_time = time();
            $ProductsFeatured->updated_userid = $this->staff->uid;
            $res = $ProductsFeatured->save();
        } else {
            $res = $model->delete();
        }
        if ($res) {
            $this->addMessage('state', 'success');
            $this->addMessage('message', Yii::t('message', 'success'));
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', Yii::t('message', 'fail'));
        }
        $this->addMessage('callback', 'cbSuccess');
        $this->showMessage();

    }

    //删除商品
    public function actionDeleteProducts()
    {
        $id = Yii::app()->request->getParam('id', '');
        $model = Products::model()->findByPk($id); //商品
        if ($model) {
            $criteria = new CDbCriteria;
            $criteria->compare('pid', $model->id);
            $stockModel = ProductsStockItem::model()->count($criteria);
            if (!$stockModel) {
                $model->status = 99;
                if ($model->save()) {
                    $this->addMessage('state', 'success');
                    $this->addMessage('message', Yii::t('message', 'success'));
                    $this->addMessage('callback', 'cbSuccess');
                } else {
                    $error = current($model->getErrors());
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', $error[0]);
                }
                $this->showMessage();
            } else {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '有库存, 不可删除');
                $this->showMessage();
            }
        }
    }

    //缺货预定列表
    public function actionShowOutOfStock()
    {
        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/vue2.js');

        $cid = Yii::app()->request->getParam('pcid', "");
        // 查询需求
        $criteria = new CDbCriteria;
        $criteria->compare('status', 0);
        $criteria->compare('school_id', $this->branchId);
        $criteria->compare('cid', $cid);
        $productsOutOfStockModel = ProductsOutofStock::model()->findAll($criteria);
        $outOfStockInfo = array();
        $config = Products::config();
        $info = array();
        if ($productsOutOfStockModel) {
            foreach ($productsOutOfStockModel as $val) {
                $info[$val->pid]["title"] = $val->products->getTitle();
                $info[$val->pid]["item"][$val->aid]["color"] = $config["uniform"]["item"]["color"]["option"][$val->attr1];
                $info[$val->pid]["item"][$val->aid]["size"] = $config["uniform"]["item"]["size"]["option"][$val->attr2];
                $info[$val->pid]["item"][$val->aid]["registerAt"] = date("Y-m-d H:i:s", $val->register_at);
                $info[$val->pid]["item"][$val->aid]["count"] += 1;
                $info[$val->pid]["item"][$val->aid]["item"][] = array(
                    "id" => $val->id,
                    "registerNum" => $val->register_num,
                    "childName" => $val->child->getChildName(),
                    "name" => $val->products->getTitle(),
                );
            }
            foreach ($info as $key => $val) {
                $outOfStockInfo[$key]["title"] = $val["title"];
                $outOfStockInfo[$key]["item"] = array_values($val["item"]);
            }
        }
        sort($outOfStockInfo);
        $this->render('outOfStock', array(
            'outOfStockInfo' => $outOfStockInfo));
    }

    //缺货预定列表
    public function actionShowOutOfStockList()
    {
        $cid = Yii::app()->request->getParam('pcid', "");

        // 查询需求
        $criteria = new CDbCriteria;
        // todo 是否显示已发送通知的登记
        $criteria->compare('status', 0);
        $criteria->compare('school_id', $this->branchId);
        $criteria->compare('cid', $cid);
        $productsOutOfStockModel = ProductsOutofStock::model()->findAll($criteria);
        $config = Products::config();
        $keyToIndex = array();
        $dataArray = array();
        if ($productsOutOfStockModel) {
            foreach ($productsOutOfStockModel as $val) {
                if (!isset($dataArray[$val->pid])) {
                    $dataArray[$val->pid] = array(
                        "title" => $val->products->getTitle(),
                        "items" => array(),
                    );
                }
                $index = $val->attr1 . "-" . $val->attr2;
                $key = array_search($index, $keyToIndex);
                if ($key == false) {
                    $key = count($keyToIndex);
                    $keyToIndex[] = $index;
                }
                if (!isset($dataArray[$val->pid]["items"][$key])) {
                    $dataArray[$val->pid]["items"][$key] = array(
                        "color" => Yii::t('products', $config["uniform"]["item"]["color"]["option"][$val->attr1]),
                        "size" => Yii::t('products', $config["uniform"]["item"]["size"]["option"][$val->attr2]),
                        "register" => array()
                    );
                }
                $dataArray[$val->pid]["items"][$key]["register"][] = array(
                    "id" => $val->id,
                    "child" => $val->child->getChildName(),
                    "status" => $val->status,
                );
            }
        }
        sort($dataArray);
        $this->render('outOfStock', array('outOfStockInfo' => $dataArray));
    }

    //确认到货
    public function actionSaveOutOfStock()
    {
        $ids = Yii::app()->request->getParam('ids', "");
        if (!$ids) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', "数据错误");
            $this->showMessage();
        }
        $productsOutOfStockModel = ProductsOutofStock::model()->findAllByPk($ids);
        if ($productsOutOfStockModel) {
            foreach ($productsOutOfStockModel as $val) {
                $val->status = 1;
                $val->updated_by = Yii::app()->user->id;
                $val->updated_at = time();
                $val->save();
            }
            $this->addMessage('state', 'success');
            $this->addMessage('message', Yii::t('message', 'success'));
            $this->addMessage('callback', 'cbSuccess');
            $this->showMessage();
        }
        $this->addMessage('state', 'fail');
        $this->addMessage('message', "数据错误");
        $this->showMessage();
    }

    //显示确认订单
    public function actionShowConfirmOrder()
    {
        $status = Yii::app()->request->getParam('status', 0);
        $username = Yii::app()->request->getParam('username', "");
        $classId = Yii::app()->request->getParam('classId', "");
        $pcid = Yii::app()->request->getParam('pcid', "");
        $startdate = Yii::app()->request->getParam('startdate', "");
        $enddate = Yii::app()->request->getParam('enddate', "");


        /* ----  获取所有有效班级 stat ---- */
        Yii::import('common.models.calendar.*');
        Yii::import('common.models.invoice.ChildReserve');
        $crit = new CDbCriteria();
        $crit->compare('branchid', $this->branchId);
        $crit->compare('is_selected', 1);
        $calendars = CalendarSchool::model()->find($crit);

        $crit = new CDbCriteria();
        $crit->compare('schoolid', $this->branchId);
        $crit->compare('yid', $calendars->yid);
        $classModel = IvyClass::model()->findAll($crit);

        $classList = array();
        if ($classModel) {
            foreach ($classModel as $item) {
                $classList[$item->classid] = $item->title;
            }
        }

        /* ----  获取所有有效班级 end ---- */
        /* ----  根据状态获取所有未发放，未确定 和 已发放的数据 stat ---- */
        $childModel = array();
        if ($username) {
            $criteria = new CDbCriteria;
            $criteria->addCondition("concat_ws('',name_cn,first_name_en,middle_name_en,last_name_en) like '%{$username}%' ");
            $criteria->index = "childid";
            $childModel = ChildProfileBasic::model()->findAll($criteria);
        }

        $model = new ProductsInvoiceItem();
        $criteria = new CDbCriteria;
        if ($childModel) {
            $criteria->compare('childid', array_keys($childModel));
        }
        if ($classId) {
            $criteria->compare('classid', $classId);
        }
        if ($startdate = strtotime($startdate)) {
            $criteria->compare('updated_time', '>=' . $startdate);
        }
        if ($enddate = strtotime($enddate)) {
            $criteria->compare('updated_time', '<=' . $enddate);
        }
        $criteria->compare('school_id', $this->branchId);
        $criteria->compare('status', $status);
        $criteria->compare('cid', $pcid);
        // 计算总金额
        $criteria->select = 'sum(num * unit_price) as totalAmount';
        $totalAmount = ProductsInvoiceItem::model()->find($criteria)->totalAmount;
        $criteria->select = '*';

        $dataProvider = new CActiveDataProvider($model, array(
            'criteria' => $criteria,
            'pagination' => array('pageSize' => 20),
            'sort' => array(
                'defaultOrder' => 't.updated_time ASC',
            ),
        ));

        $classIds = array();
        $productArrt = array();
        foreach ($dataProvider->getData() as $child) {
            $childId[$child->childid] = $child->childid;
            $classIds[$child->classid] = $child->classid;
            $productArrt[$child->paid] = $child->paid;
        }

        if ($childId && $productArrt) {
            $config = Products::config();
            $productsAttrModel = ProductsAttr::model()->findAllByPk($productArrt);
            foreach ($productsAttrModel as $attr) {
                $configAS = $config[$attr->type]['item'];
                $i = 1;
                foreach ($configAS as $types) {
                    $_attr = "attr" . $i;
                    $this->productsAttrs[$attr->id][] = Yii::t('products', $types['option'][$attr->$_attr]);
                    $i++;
                }
            }
        }

        /* ---- 获取孩子名字和下学年班级 ---- */
        if ($childId) {
//            $childModel = ChildProfileBasic::model()->findAllByPk($childId);
//            foreach ($childModel as $child) {
//                $this->childsName[$child->childid] = $child->getChildName();
//            }
            $criteria = new CDbCriteria;
            $criteria->compare('t.childid', $childId);
            $criteria->index = "childid";
            $criteria->with = "nextYear";
            $childModel = ChildProfileBasic::model()->findAll($criteria);
            foreach ($childModel as $child) {
                $this->childsName[$child->childid] = $child->getChildName();
                if ($child->nextYear->stat == 20) {
                    $this->childsNextClass[$child->childid] = $child->nextYear->ivyclass->title;

                } else {
                    $this->childsNextClass[$child->childid] = '';
                }
            }
            foreach ($dataProvider->getData() as $child_item) {
                $child_item->childsNextClass = $this->childsNextClass[$child_item->childid];
            }
        }
        /* ---- 获取孩子班级 ---- */
        if ($classIds) {
            $classModel = IvyClass::model()->findAllByPk($classIds);
            foreach ($classModel as $class) {
                $this->className[$class->classid] = $class->title;
            }
        }
        /* ----  根据状态获取所有未发放，未确定 和 已发放的数据 end---- */

        $crit = new CDbCriteria();
        $crit->select = '*, Sum(num) as countnum';
        $crit->compare('t.status', $status);
        $crit->compare('t.school_id', $this->branchId);
        $crit->compare('t.cid', $pcid);
        $crit->with = array('invoice');
        $crit->group = 'paid';
        $invoiceItemModel = ProductsInvoiceItem::model()->findAll($crit);


        $productsCatModel = ProductsCat::model()->findByPk($pcid);

        $this->render('confirm', array(
            'dataProvider' => $dataProvider,
            'status' => $status,
            'classList' => $classList,
            'productsAttrModel' => $productsAttrModel,
            'invoiceItemModel' => $invoiceItemModel,
            'productsCatModel' => $productsCatModel,
            'pcid' => $pcid,
            'totalAmount' => $totalAmount,
        ));
    }

    //显示每个账单下的详细信息
    public function actionShowInvoiceItem()
    {
        $iid = Yii::app()->request->getParam('iid', '');

        $criteria = new CDbCriteria;
        $criteria->compare('iid', $iid);
        $invoiceItemModel = ProductsInvoiceItem::model()->findAll($criteria);

        $productsModel = Products::model()->findByPk($invoiceItemModel[0]->cid);

        $this->renderPartial('showInvoiceItem', array(
            'invoiceItemModel' => $invoiceItemModel,
            'productsModel' => $productsModel,
        ));
    }

    //确认订单，修改库存
    public function actionConfirmOrder()
    {
        $ids = Yii::app()->request->getParam('ids', array());
        $status = Yii::app()->request->getParam('status', array());

        $invoiceItemModels = ProductsInvoiceItem::model()->findAllByPk(array_filter($ids));

        if ($invoiceItemModels) {
            $stock = array();
            foreach ($invoiceItemModels as $item) {
                $stock[$item->paid] += $item->num;
            }

            if ($stock) {
                $transaction = Yii::app()->subdb->beginTransaction();
                try {
                    // 下单自动变动库存，不在确认订单时变动了
                    // if($status == ProductsInvoice::STATS_PAID){
                    //     $criteria=new CDbCriteria;
                    //     $criteria->compare('paid', array_keys($stock));
                    //     $criteria->compare('school_id', $this->branchId);
                    //     $stockModel = ProductsStock::model()->findAll($criteria);
                    //     foreach($stockModel as $stockItem){
                    //         $stockItem->num -= $stock[$stockItem->paid];
                    //         $stockItem->updated_userid = Yii::app()->user->id;
                    //         $stockItem->updated_time = time();
                    //         if(!$stockItem->save()){
                    //             $transaction->rollBack();
                    //             $error = current($stockItem->getErrors());
                    //             $this->addMessage('state', 'fail');
                    //             $this->addMessage('message', $error[0]);
                    //             $this->showMessage();
                    //         }
                    //         $stockItemModel = new ProductsStockItem();
                    //         $stockItemModel->cid = $stockItem->cid;
                    //         $stockItemModel->pid = $stockItem->pid;
                    //         $stockItemModel->paid = $stockItem->paid;
                    //         $stockItemModel->psid = $stockItem->id;
                    //         $stockItemModel->num = '-' . $stock[$stockItem->paid];
                    //         $stockItemModel->memo = "家长前台买服装, 后台确认减少库存明细";
                    //         $stockItemModel->school_id = $stockItem->school_id;
                    //         $stockItemModel->updated_userid = Yii::app()->user->id;
                    //         $stockItemModel->updated_time = time();
                    //         if(!$stockItemModel->save()){
                    //             $transaction->rollBack();
                    //             $error = current($stockItemModel->getErrors());
                    //             $this->addMessage('state', 'fail');
                    //             $this->addMessage('message', $error[0]);
                    //             $this->showMessage();
                    //         }
                    //     }
                    // }

                    foreach ($invoiceItemModels as $item) {
                        $item->status = $status + 10;
                        $item->updated_userid = Yii::app()->user->id;
                        $item->updated_time = time();
                        if (!$item->save()) {
                            $transaction->rollBack();
                            $error = current($item->getErrors());
                            $this->addMessage('state', 'fail');
                            $this->addMessage('message', $error[0]);
                            $this->showMessage();
                        }
                        $invoiceItemStatusModel = new ProductsInvoiceItemStatus();
                        $invoiceItemStatusModel->invoice_item_id = $item->id;
                        $invoiceItemStatusModel->status = $item->status;
                        $invoiceItemStatusModel->uid = Yii::app()->user->id;
                        $invoiceItemStatusModel->timestamp = time();
                        if (!$invoiceItemStatusModel->save()) {
                            $transaction->rollBack();
                            $error = current($invoiceItemStatusModel->getErrors());
                            $this->addMessage('state', 'fail');
                            $this->addMessage('message', $error[0]);
                            $this->showMessage();
                        }
                    }

                    $transaction->commit();
                    $this->addMessage('state', 'success');
                    $this->addMessage('message', Yii::t('message', 'success'));
                    $this->addMessage('callback', 'cbSuccess');
                    $this->showMessage();
                } catch (Exception $e) {
                    $transaction->rollBack();
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', $e->getMessage());
                    $this->showMessage();
                }
            }
        }
    }

    // 批量设置退款状态
    public function actionRefund()
    {
        $ids = Yii::app()->request->getParam('ids', array());
        $status = Yii::app()->request->getParam('status');
        $invoiceItemModels = ProductsInvoiceItem::model()->findAllByPk($ids);

        if ($invoiceItemModels) {
            $transaction = Yii::app()->subdb->beginTransaction();
            try {
                $refundIds = array();
                foreach ($invoiceItemModels as $item) {
                    $item->status = ProductsInvoice::STATS_REFUNDING;
                    $item->updated_userid = Yii::app()->user->id;
                    $item->updated_time = time();
                    if (!$item->save()) {
                        $transaction->rollBack();
                        $error = current($item->getErrors());
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', $error[0]);
                        $this->showMessage();
                    }
                    $invoiceItemStatusModel = new ProductsInvoiceItemStatus();
                    $invoiceItemStatusModel->invoice_item_id = $item->id;
                    $invoiceItemStatusModel->status = $item->status;
                    $invoiceItemStatusModel->uid = Yii::app()->user->id;
                    $invoiceItemStatusModel->timestamp = time();
                    if (!$invoiceItemStatusModel->save()) {
                        $transaction->rollBack();
                        $error = current($invoiceItemStatusModel->getErrors());
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', $error[0]);
                        $this->showMessage();
                    }
                    $refundIds[] = $item->id;
                }
                $transaction->commit();
                // 发送 MQ 退款
                CommonUtils::addProducer('product', "Products.productRefund", CJSON::encode($refundIds), 0);

                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message', 'success'));
                $this->addMessage('callback', 'cbSuccess');
                $this->showMessage();
            } catch (Exception $e) {
                $transaction->rollBack();
                $this->addMessage('state', 'fail');
                $this->addMessage('message', $e->getMessage());
                $this->showMessage();
            }
        }
    }

    // 导出库存
    public function actionPrintStock()
    {
        $cid = Yii::app()->request->getParam('cid', '');
        $ProductsCat = ProductsCat::model()->findByPk($cid);
        //是否共享的商品
        $criteria = new CDbCriteria;
        $criteria->compare('cid', $cid);
        $criteria->compare('school_id', $this->branchId);
        $catShare = ProductsCatShare::model()->findAll($criteria);
        if(!empty($catShare)){
            //发起共享的学校id
            $share_school_id = $ProductsCat->school_id;
            //共享库存
            $criteria = new CDbCriteria();
            $criteria->compare('t.school_id', $share_school_id);
            $criteria->compare('product.cid', $cid);
            $criteria->compare('product.status', '<>99');
            $criteria->with = array('product', 'attr');
            $criteria->order = 'product.sort DESC';
            $criteria->index = 'paid';
            $shareStocksModel = ProductsStock::model()->findAll($criteria);
        }
        $criteria = new CDbCriteria();
        $criteria->compare('t.school_id', $this->branchId);
        $criteria->compare('product.cid', $cid);
        $criteria->compare('product.status', '<>99');
//        $criteria->compare('attr.status', Products::STATUS_NORMAL);#只要设置展示的库存
        $criteria->with = array('product', 'attr');
        $criteria->order = 'product.sort DESC';
        $stocksModel = ProductsStock::model()->findAll($criteria);
        $ProductsConfig = Products::config();
        $attr2 = array();#所有的规格
        $data = array();
        foreach ($stocksModel as $item) {
            #得到所有的规格
            $attr2[$item->attr->type][$item->attr->attr2] = $item->attr->attr2;
            $data[$item->product->id]['title'] = $item->product->getTitle();
            $data[$item->product->id]['color'][$item->attr->attr1] = $item->attr->attr1;
            $data[$item->product->id]['size_num_by_color'][$item->attr->attr1][$item->attr->attr2] = $item->num;
            if(!empty($shareStocksModel)){
                $data[$item->product->id]['size_num_by_color_share'][$item->attr->attr1][$item->attr->attr2] = $shareStocksModel[$item->attr->id]['num'];//$item->num;
            }
        }
        $return_data['title'] = $ProductsCat->getName() . date('Y-m-d') . "_库存.xlsx";
        $size_option = array();
        $items0 = array(
            '品类',
            '颜色',
        );
        foreach ($attr2 as $type => $item) {
            foreach ($item as $value) {
                $size = $ProductsConfig[$type]['item']['size']['option'][$value];
                if(empty($catShare)){
                    $size_option[] = $size;
                }else{
                    $size_option[] = $size.'-本校';
                    $size_option[] = $size.'-共享';
                }
            }
        }
        $return_data['items'][0] = array_merge($items0, $size_option);
        $k = 0;
        foreach ($data as $product_id => $item) {
            foreach ($item['color'] as $color) {
                $k++;
                $items = array(
                    $item['title'],
                    $ProductsConfig['uniform']['item']['color']['option'][$color],
                );
                $size_options = array();
                foreach ($attr2 as $color_value) {
                    foreach ($color_value as $one_attr2) {
                        if (isset($item['size_num_by_color'][$color][$one_attr2])) {
                            $size_options[] = $item['size_num_by_color'][$color][$one_attr2];
                        } else {
                            $size_options[] = '-';
                        }
                        if(isset($item['size_num_by_color_share'][$color][$one_attr2])){
                            $size_options[] = $item['size_num_by_color_share'][$color][$one_attr2];
                        }else{
                            $size_options[] = '-';
                        }
                    }
                }
                $return_data['items'][$k] = array_merge($items, $size_options);
            }
        }
        $this->addMessage('state', 'success');
        $this->addMessage('data', $return_data);
        $this->showMessage();
    }

    //打印缺货登记
    public function actionPrintOutOfStock1()
    {
        $ProductsConfig = Products::config();
        $cid = Yii::app()->request->getParam('cid', '');
        $ProductsCat = ProductsCat::model()->findByPk($cid);
        $productsOutofStock = Yii::app()->db->createCommand()
            ->select('cid,pid,aid,attr1,attr2,SUM( register_num ) as num')
            ->from('mimssub.ivy_products_outof_stock')
            ->where('school_id=:school_id and cid=:cid', array(':school_id' => $this->branchId, ':cid' => $cid))
            ->group('cid,pid,aid,attr1,attr2')
            ->queryAll();
        $aid_arr = array();
        foreach ($productsOutofStock as $item) {
            $aid_arr[] = $item['aid'];
        }

        $products = Products::model()->findAllByAttributes(array('cid' => $cid));
        $productsById = array();
        foreach ($products as $key => $item) {
            $productsById[$item['id']]['title_cn'] = $item->title_cn;
            $productsById[$item['id']]['title_en'] = $item->title_en;
        }
        $attr2 = array();#所有的规格
        $data = array();

        $ProductsAttr = ProductsAttr::model()->findAllByPk($aid_arr);
        $ProductsAttrById = array();
        foreach ($ProductsAttr as $item) {
            $ProductsAttrById[$item->id] = array(
                'type' => $item['type'],
                'id' => $item['id'],
            );
        }

        foreach ($productsOutofStock as $item) {
            #得到所有的规格
            $pid = $item['pid'];
            $aid = $item['aid'];
            $type = $ProductsAttrById[$aid]['type'];
            $attr2[$type][$item['attr2']] = $item['attr2'];
            $title_cn = $productsById[$pid]['title_cn'];
            $title_en = $productsById[$pid]['title_en'];
            $data[$pid]['title'] = $this->getName($title_cn, $title_en);
            $data[$pid]['color'][$item['attr1']] = $item['attr1'];
            $data[$pid]['size_num_by_color'][$item['attr1']][$item['attr2']] = $item['num'];
        }

        $return_data['title'] = $ProductsCat->getName() . date('Y-m-d') . "_缺货登记.xlsx";
        $size_option = array();
        $items0 = array(
            '品类',
            '颜色',
        );
        foreach ($attr2 as $type => $item) {
            foreach ($item as $value) {
                $size = $ProductsConfig[$type]['item']['size']['option'][$value];
                $size_option[] = $size;
            }
        }
        $return_data['items'][0] = array_merge($items0, $size_option);
        $k = 0;
        foreach ($data as $product_id => $item) {
            foreach ($item['color'] as $color) {
                $k++;
                $items = array(
                    $item['title'],
                    $ProductsConfig['uniform']['item']['color']['option'][$color],
                );
                $size_options = array();
                foreach ($attr2 as $color_value) {
                    foreach ($color_value as $one_attr2) {
                        if (isset($item['size_num_by_color'][$color][$one_attr2])) {
                            $size_options[] = $item['size_num_by_color'][$color][$one_attr2];
                        } else {
                            $size_options[] = '-';
                        }
                    }
                }
                $return_data['items'][$k] = array_merge($items, $size_options);
            }
        }
        $this->addMessage('state', 'success');
        $this->addMessage('data', $return_data);
        $this->showMessage();
    }

    //导出缺货明细 包含学生信息
    public function actionPrintOutOfStock()
    {
        $ProductsConfig = Products::config();
        $cid = Yii::app()->request->getParam('cid', '');
        $criteria = new CDbCriteria;
        $criteria->compare('status', 0);
        $criteria->compare('school_id', $this->branchId);
        $criteria->compare('cid', $cid);
        $criteria->order = "child_id";
        $productsOutofStock = ProductsOutofStock::model()->findAll($criteria);
        $items0 = array(
            'ID',
            Yii::t("labels", 'Child Name'),
            Yii::t("site", 'Product'),
            Yii::t("labels", 'Color'),
            Yii::t("labels", 'Size'),
            Yii::t("site", 'Quantity'),
        );
        $ProductsCat = ProductsCat::model()->findByPk($cid);
        $return_data['title'] = $ProductsCat->getName() . date('Y-m-d') . "_缺货登记.xlsx";
        $return_data['items'][0] = $items0;
        foreach ($productsOutofStock as $item) {
            $color = $ProductsConfig['uniform']['item']['color']['option'][$item->attr1];
            $size = $ProductsConfig['uniform']['item']['size']['option'][$item->attr2];
            $return_data['items'][] = array(
                $item->child_id,
                $item->child->getChildName(),
                $item->products->getTitle(),
                $color,
                $size,
                $item->register_num,
            );
        }
        $this->addMessage('state', 'success');
        $this->addMessage('data', $return_data);
        $this->showMessage();
    }

    public function actionPrintOrder()
    {
        $classId = Yii::app()->request->getParam('classid', '');
        $userName = Yii::app()->request->getParam('userName', '');
        $pcid = Yii::app()->request->getParam('pcid', '');
        $status = Yii::app()->request->getParam('status', '');

        $childModel = array();
        $child = array();
        $productsAttrs = array();
        if ($userName) {
            $criteria = new CDbCriteria;
            $criteria->addCondition("concat_ws('',name_cn,first_name_en,middle_name_en,last_name_en) like '%{$userName}%' ");
            $criteria->index = "childid";
            $childModel = ChildProfileBasic::model()->findAll($criteria);
        }

        $criteria = new CDbCriteria;
        if ($childModel) {
            $criteria->compare('childid', array_keys($childModel));
        }
        if ($classId) {
            $criteria->compare('classid', $classId);
        }
        $criteria->compare('school_id', $this->branchId);
        $criteria->compare('status', $status);
        $criteria->compare('cid', $pcid);
        $productsModel = ProductsInvoiceItem::model()->findAll($criteria);
        if ($productsModel) {
            $childid = array();
            foreach ($productsModel as $item) {
                $childid[] = $item->childid;
                $productArrt[] = $item->paid;
            }
            if ($childid) {
                Yii::import('common.models.invoice.ChildReserve');
                $criteria = new CDbCriteria;
                $criteria->compare('t.childid', $childid);
                $criteria->index = "childid";
                $criteria->with = "nextYear";
                $child = ChildProfileBasic::model()->findAll($criteria);
            }
            $config = Products::config();
            $productsAttrModel = ProductsAttr::model()->findAllByPk($productArrt);
            foreach ($productsAttrModel as $attr) {
                $configAS = $config[$attr->type]['item'];
                $i = 1;
                foreach ($configAS as $types) {
                    $_attr = "attr" . $i;
                    $productsAttrs[$attr->id][] = Yii::t('products', $types['option'][$attr->$_attr]);
                    $i++;
                }
            }
        }

        switch ($status) {
            case 20:
                $print_title = '等待确定';
                break;
            case 30:
                $print_title = '等待发放';
                break;
            case 40:
                $print_title = '已经发放';
                break;
            case 45:
                $print_title = '等待退款';
                break;
            case 50:
                $print_title = '退款完成';
                break;
            default:
                $print_title = '未知类型';
        }
        $return_data['title'] = $this->branchId . '_' . $print_title . '_' . date("Y-m-d") . '.xlsx';
        $return_data['items'][0] = array(
            'ID',
            '姓名',
            '本学年班级',
            '下学年班级',
            '类型',
            '规格',
            '金额',
            '数量',
            '时间',
        );
        if ($productsModel) {
            foreach ($productsModel as $k => $item) {
                if ($productsAttrs) {
                    $datas = "";
                    $value = $productsAttrs[$item->paid];
                    foreach ($value as $val) {
                        $datas .= $val . " ";
                    }
                }
                $return_data['items'][$k + 1] = array(
                    $item->childid,
                    (isset($child) && $child[$item->childid]) ? $child[$item->childid]->getChildName() : $item->childid,
                    (isset($child) && $child[$item->childid]) ? $child[$item->childid]->ivyclass->title : $item->classid,
                    (isset($child) && $child[$item->childid] && !empty($child[$item->childid]->nextYear->ivyclass->title)) ? $child[$item->childid]->nextYear->ivyclass->title : '',
                    $item->product->getTitle(),
                    $datas,
                    $item->unit_price,
                    $item->num,
                    date("Y-m-d H:i:s", $item->updated_time),
                );
            }
        }
        $this->addMessage('state', 'success');
        $this->addMessage('data', $return_data);
        $this->showMessage();
    }

    public function _t($str = '')
    {
        $str = "\"" . $str . "\"";
        return iconv("UTF-8", "GBK//IGNORE", $str);
    }

    public function getButton($data)
    {
        //查询是否共享库存 共享库存得商品也共享推荐且不能自己设置推荐
        $featured_school_id = $this->branchId;
        $is_share_featured = false;
        $cid = $data->cid;//被分享的商品cat_id
        $criteria = new CDbCriteria;
        $criteria->compare('cid', $cid);
        $criteria->compare('school_id', $this->branchId);
        $catShare = ProductsCatShare::model()->find($criteria);
        if (!empty($catShare->share_stock) && 1 == $catShare->share_stock) {
            $criteria = new CDbCriteria;
            $criteria->compare('id', $catShare->cid);
            $ProductsCat = ProductsCat::model()->find($criteria);
            //共享推荐
            if (!empty($catShare->share_featured) && 1 == $catShare->share_featured) {
                $is_share_featured = true;
                $featured_school_id = $ProductsCat->school_id;
            }
        }
        echo CHtml::link('属性', array('updataStock', 'pid' => $data->id, "branchId" => Yii::app()->controller->branchId), array('class' => 'btn btn-xs btn-info', "target" => "_blank")) . ' ';
        echo CHtml::link('图片', array('showPhoto', 'pid' => $data->id, "branchId" => Yii::app()->controller->branchId), array('class' => 'J_modal btn btn-xs btn-info','id'=>'photo_'.$data->id)) . ' ';
        //艾毅校园展示二维码按钮
        if (in_array($this->branchObj->branchid,array('BJ_IASLT', 'BJ_OE'))) {
            echo CHtml::link('二维码', array('showQrCode', 'pid' => $data->id, "branchId" => Yii::app()->controller->branchId), array('class' => 'J_modal btn btn-xs btn-info')) . ' ';
        }

        $filteredData = ProductsFeatured::model()->findAll(array(
            'condition' => 'school_id = :schoolId and pid = :pid',
            'params' => array(':schoolId' => $featured_school_id, ':pid' => $data->id),
        ));
        if ($is_share_featured) {
            //共享库存得商品且设置了共享推荐的不能设置推荐商品
            if (!empty($filteredData)) {
                echo CHtml::link('正在推荐', "#", array('class' => 'btn btn-xs btn-success', 'onclick' => "return false;")) . ' ';
            } else {
//                echo CHtml::link('首页推荐', array('homePage', 'id' => $data->id, "branchId" => Yii::app()->controller->branchId), array('class' => 'J_ajax_del btn btn-xs btn-info','data-msg' => "确认更新推荐状态吗")). ' ';
            }
        } else {
            if (!empty($filteredData)) {
                echo CHtml::link('正在推荐', array('homePage', 'id' => $data->id, "branchId" => Yii::app()->controller->branchId), array('class' => 'J_ajax_del btn btn-xs btn-success', 'data-msg' => "确认更新推荐状态吗")) . ' ';
            } else {
                echo CHtml::link('首页推荐', array('homePage', 'id' => $data->id, "branchId" => Yii::app()->controller->branchId), array('class' => 'J_ajax_del btn btn-xs btn-info', 'data-msg' => "确认更新推荐状态吗")) . ' ';
            }
        }
        if ($data->school_id == $this->branchId) {
            echo CHtml::link(Yii::t('global', 'Edit'), array('updateProducts', 'id' => $data->id, 'pcid' => $data->cid, "branchId" => Yii::app()->controller->branchId), array('class' => 'J_modal btn btn-xs btn-info')) . ' ';
            echo CHtml::link(Yii::t('global', 'Delete'), array('deleteProducts', 'id' => $data->id, "branchId" => Yii::app()->controller->branchId), array('class' => 'J_ajax_del btn btn-xs btn-danger'));
        }
    }

    public function getReserveCount($data)
    {
        return $this->productsOutofStockListCount[$data->id] ? $this->productsOutofStockListCount[$data->id] : 0;
    }

    public function getChildName($data)
    {

        return $this->childsName[$data->childid];
    }

    public function getChildClass($data)
    {
        return $this->className[$data->classid];
    }

    public function getMenu()
    {
        $status = Yii::app()->request->getParam('status', '');
        $pcid = Yii::app()->request->getParam('pcid', '');
        $mainMenu = array(
            array('label' => Yii::t('user', '等待确定'), 'url' => array("/mcampus/product/showConfirmOrder", 'pcid' => $pcid, 'status' => ProductsInvoice::STATS_PAID), 'active' => $status == '20' ? true : false),
            array('label' => Yii::t('user', '等待发放'), 'url' => array("/mcampus/product/showConfirmOrder", 'pcid' => $pcid, 'status' => ProductsInvoice::STATS_CONFIRM), 'active' => $status == '30' ? true : false),
            array('label' => Yii::t('user', '已经发放'), 'url' => array("/mcampus/product/showConfirmOrder", 'pcid' => $pcid, 'status' => ProductsInvoice::STATS_SEND), 'active' => $status == '40' ? true : false),
            array('label' => Yii::t('user', '等待退款'), 'url' => array("/mcampus/product/showConfirmOrder", 'pcid' => $pcid, 'status' => ProductsInvoice::STATS_REFUNDING), 'active' => $status == '45' ? true : false),
            array('label' => Yii::t('user', '退款完成'), 'url' => array("/mcampus/product/showConfirmOrder", 'pcid' => $pcid, 'status' => ProductsInvoice::STATS_REFUNDED), 'active' => $status == '50' ? true : false),
        );
        return $mainMenu;
    }

    public function getUserName($data)
    {
        return $this->userName[$data->updated_userid];
    }

    public function getStatus($data)
    {
        return ($data->status) ? Yii::t("global", "Active") : Yii::t("global", "Inactive");
    }

    public function getProducts($data)
    {
        $datas = "";
        $value = $this->productsAttrs[$data->paid];
        foreach ($value as $val) {
            $datas .= $val . " ";
        }
        return $datas;
        //return ($data->status) ? Yii::t("global","Active") : Yii::t("global","Inactive");
    }

    public function getRefundInfo($data)
    {
        if (!$data->refund_info) {
            return false;
        }
        $array = explode(';', $data->refund_info);
        return $array[1];
    }

    public function getName($title_cn, $title_en)
    {
        switch (Yii::app()->language) {
            case "zh_cn":
                return $title_cn;
                break;
            case "en_us":
                return $title_en;
                break;
        }
    }


}

