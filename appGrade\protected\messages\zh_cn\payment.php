<?php
/**
 * Message translations.
 *
 * This file is automatically generated by 'yiic message' command.
 * It contains the localizable messages extracted from source code.
 * You may modify this file by translating the extracted messages.
 *
 * Each array element represents the translation (value) of a message (key).
 * If the value is empty, the message is considered as not translated.
 * Messages that no longer need translation will have their translations
 * enclosed between a pair of '@@' marks.
 *
 * Message string can be used with plural forms format. Check i18n section
 * of the guide for details.
 *
 * NOTE, this file must be saved in UTF-8 encoding.
 *
 * @version $Id: $
 */
return array (
  'This product is temporarily out of stock!' => '产品暂时没有库存！',
  'Gift order has been successfully submitted.' => '兑换申请提交成功！',
  'Thank you for your order. The gift will be delivered to the campus in about one month and our office staff will inform you when it has arrived.' => '兑换申请提交成功！礼品到达校园后工作人员会尽快发放给您。',
  'Temporarily out of stock. We will replenish as soon as possible!' => '库存暂时短缺，我们会尽快补货！',
  'No gifts ordered yet.' => '还未兑换任何礼物',
  'Insufficient Ivy Points' => '积分不够',
  'You do not have sufficient Ivy Points to order this product!' => '积分不够换此产品!',
  
  'Confirm the order?' => '确认兑换吗？',
  'Confirm to cancel the order?' => '确认取消订单吗？',
  'Order submitted' => '订单已提交',
  'Order confirmed' => '订单已确认',
  'Preparing to ship' => '准备发往校园',
  'On route to campus' => '已经发往校园',
  'Delivered to campus' => '校园已接收',
  'Gift delivered' => '礼品已发放',
  '自2013年5月20日起，在线支付（不包含网银转账）的金额将自动转化为艾毅积分（1元=1分），积分可换取艾毅幼儿园礼品库中同等积分值的产品。积分余额是指总的积分减去兑换礼品所用积分余下的部分。' => '',
  'Order has been cancelled!' => '订单取消成功!',
  'Order is being proceeded and cannot be cancelled!' => '订单已处理不能取消!',
  'Cancel the order' => '取消订单',
  'Order' => '我要兑换',
  'Order Date' => '兑换时间',
  'Order Status' => '当前状态',
  'Payment Summary' => '付款信息',
  ':points points' => ':points 积分',
  'Account Credits' => '帐户余额',
  'Alipay' => '支付宝',
  'All' => '所有',
  'All Expense' => '所有消费',
  'All Refund' => '所有退款',
  'All invoices have been paid.  Thank you very much!' => '所有账单都已经付清，非常感谢您的支持与配合！',
  'Amount' => '金额',
  'Amount Due' => '应付金额',
  'Amount Paid' => '已付金额',
  'Amount after discount' => '折扣后金额',
  'Are you sure to cancel?' => '确定取消餐？',
  'Are you sure to recover?' => '确定恢复餐？',
  'Balance' => '余额',
  'Bank List' => '银行列表',
  'Bank Transfer' => '银行转账',
  'Confirm Payment' => '金额确认',
  'Current Balance' => '积分余额',
  'Date of Invoice' => '账单开出时间',
  'Deposit' => '预缴学费',
  'Deposit Amount' => '预缴学费金额',
  'Discount' => '折扣',
  'Due Date' => '过期日期',
  'Duration' => '账单期间',
  'Exchange History' => '兑换记录',
  'Fapiao No.' => '发票号码',
  'Favorite' => '最近使用',
  'General Credit History' => '个人账户明细',
  'General Credit Transactions' => '个人账户明细',
  'Gifts Available' => '礼品兑换库',
  'In Stock: :stock' => '库存：:stock',
  'Invoice' => '账单',
  'Invoice Amount' => '账单金额',
  'Invoice Information' => '账单信息',
  'Invoice Status' => '账单状态',
  'Invoice Type' => '账单类型',
  'Ivy Points' => '艾毅积分',
  'Late Payment Rate' => '滞纳金比例',
  'Late fee Date' => '滞纳金计费',
  'Lunch Refund Only' => '午餐退费',
  'Make Payment' => '开始支付',
  'Make Payment Online' => '在线支付',
  'Most online banking sites do not support this browser. Please use IE explorer.' => '支付网关通常不直接支持该浏览器，请使用IE。',
  'No bank information found for this campus.' => '本校园尚未完善银行转账信息。',
  'Office Signature' => '校园行政签名',
  'Online Banking' => '网银支付',
  'Online Banking FAQ' => '常见问题',
  'Online Banking YP' => '易宝支付',
  'Original Amount' => '原始金额',
  'Original Invoice Information' => '原始账单信息',
  'Other Banks' => '其他银行',
  'Parent Signature' => '家长签名',
  'Payment Information' => '付款信息',
  'Payment Type' => '付款方式',
  'Please make the online payment via a bank where the online banking has been enabled.' => '网银支付：请选择一家开通过网银的银行进行在线支付。',
  'Please select a bank' => '选择银行',
  'Please select at least one invoice.' => '请至少选择一张账单。',
  'Please select up to 6 invoices each time to proceed the payment.' => '一次最多支付六笔账单，请分批支付。',
  'Print' => '打印',
  'Program Name' => '项目名称',
  'Receipt' => '收据',
  'Redirecting, please wait.' => '重定向中，请稍后...',
  'Refund Lunch' => '午餐退费',
  'Refund to Tuition Deposit' => '退款至预缴学费',
  'Refund to general credit' => '退款至个人账户',
  'Remaining Ivy Points' => '在线支付积分余额',
  'Selected Bank' => '支付银行',
  'Service Information' => '服务信息',
  'Sorry, online payment is not available for this campus.' => '本校园尚未开通在线支付。',
  'Subtotal' => '小计',
  'System error, please retry.' => '系统错误，请重试。',
  'Thank you for the reminder. I wish to proceed to make the payment using this browser.' => '谢谢提醒！我可以使用本浏览器进行网银支付。',
  'This credit is generated from tuition, lunch and other refunds.  It can be applied to any invoice, or you can also request for a cash refund.  To use, please <a href="{url}">inform</a> our campus support staff of this request.' => '个人账户中的余额是从学费、餐费以及其它类型的退费中产生。这部分金额可以转入任何费用账单中，或者您也可以申请现金退款。如果您需要使用个人账户余额，请<a href="{url}">通知</a>我们的校园支持团队。',
  'This credit is the balance of the deposit that you have paid to guarantee a seat in advance and will be automatically applied to your outstanding tuition invoices.' => '预缴学费是为了保证学位而提前缴纳的学费押金，该部分金额将会自动转入您的未付学费账单中。',
  'Tips' => '提示',
  'Title' => '标题',
  'Total' => '总计',
  'Total Ivy Points Earned' => '在线支付总积分',
  'Transaction Time' => '交易时间',
  'Transactions' => '明细',
  'Tuition Deposit History' => '预缴学费明细',
  'Unpaid Invoices' => '未付账单',
  'Use Tuition Deposit' => '使用预缴学费余额',
  'Use general credit' => '使用个人账户余额',
  'Used Ivy Points' => '已兑换积分',
  'YeePay' => '易宝支付',
  'You can also make a bank transfer with the bank information provided below.' => '您也可以使用下面提供的银行信息进行银行转账。',
  '自2013年5月20日起，在线支付（不包含网银转账）的金额将自动转化为艾毅积分（1元=1分），积分可换取艾毅幼儿园礼品库中同等积分值的产品。积分余额是指总的积分减去兑换礼品所用积分余下的部分。' => '',
  'Starting from :date, any invoice paid through the System\'s Online Banking or Wechat feature (excluding online Bank Transfer) will automatically earn Reward Points (1 RMB=1 point).  Parents can exchange Reward Points for gifts in the Rewards Club.' => '自:date起，通过幼儿园管理系统在线网银支付（不含网银银行转账）、微信支付的金额将自动转化为系统积分（1元=1分），积分可换取幼儿园礼品库中同等积分值的产品。',
  'Please input correct number, thanks!' => '请输入正确的数量，谢谢！',
  'Procedure for Gift Exchange' => '积分兑换活动流程',
  '1. Orders can only be cancelled on the same day that it was made.  It cannot be cancelled after that.' => '1. 家长在幼儿园管理系统上完成兑换操作后，当天内可以在幼儿园管理系统上取消订单；次日即不能取消订单。',
  '2. Exchanged gifts will be delivered to each campus from our distribution center once every month.' => '2. 兑换的奖品将由总部工作人员统一备齐并发往校园，每月发送一次。',
  '3. Office staff will provide the gift to parents as soon as it arrives on campus.' => '3. 校园工作人员收到礼品后将发放给家长，完成兑换。',
  'Any invoice paid through System\'s Online Banking or Wechat will automatically earn Reward Points (1 RMB=1 point).' => '通过幼儿园管理系统网银、微信成功支付任意账单，支付的金额将自动转化为系统积分（1元=1分）。',

  'Wechat Pay' => '微信支付',
  'Choice Your Bank Card Type:' => '选择卡类型：',
  'Debit Card' => '储蓄卡',
  'Credit Card' => '信用卡',
  'Multiple Payment' => '多次付款',

  'Payment Amount: ' => '本次支付：',
  'For partial payments, you must select only 1 invoice that you want to make payment for!' => '部分付款每次只能选择一个账单支付！',
  'Please enter a valid payment amount!' => '请正确填写支付金额！',
  'Your payment amount cannot exceed the total amount of the invoice(s)!' => '支付金额不能超过账单金额！',
  'Please scan QR code from Wechat to complete payment!' => '请使用微信扫一扫，扫描上面的二维码完成付款！',
  'Payment successful!' => '支付成功！',
  'Invoice List' => '账单列表',
  'Semester' => '学期',
  'Annual' => '学年',
  'A kind reminder to please use your points to exchange gifts before December 31, 2020. After December 31st, the exchange of points for gifts will be terminated and the points will be cleared. The accumulated payment points that will expire on June 30th, 2020, and the fees paid after June 30th will no longer be included in the points. We have prepared gifts. You are welcome to exchange it in time. If you have any questions, please contact us. Thank you very much!' => '温馨的提醒您，请您在2020年12月31日前用您的积分兑换小礼品。12月31日之后，将终止积分兑换礼品活动，届时积分将被清零。为了感谢您的支持，缴费累计积分将截止到2020年6月30日，6月30日之后再交纳的费用将不再被积分。我们准备了具有艾毅特色的小礼品，欢迎您及时兑换。如果有问题，请您联系我们。谢谢！',

);
