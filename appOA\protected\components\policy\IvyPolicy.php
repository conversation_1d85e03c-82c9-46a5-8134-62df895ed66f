<?php
/**
 * Ivy 收费政策配置读取
 */
class IvyPolicy{

	public $startYear = 0;
	public $schoolId = '';
	public $type;

	public $configs;
	public $consts;
    public $constantSchoolList = array('BJ_IA','BJ_CP'); //老生老办法特殊学校
	protected $yid=0;

	function __construct( $type, $startYear, $schoolId) {
		if(!defined("IVY_POLICY"))
		define("IVY_POLICY", true);

        $constantFiles = require('constants/default.php');
        $constant = ( in_array($schoolId, array_keys($constantFiles)) ) ? $constantFiles[$schoolId] : $constantFiles['default'];
        require('constants/'.$constant.'.php');

		$_branchConfigsFile = Yii::getPathOfAlias(strtolower("application.components.policy.{$startYear}.{$type}.{$schoolId}")) . ".php";

		if(!file_exists($_branchConfigsFile)){
			$_branchConfigsFile = Yii::getPathOfAlias(strtolower("common.components.policy.{$startYear}.{$type}.{$schoolId}")) . ".php";
		}

        if(!file_exists($_branchConfigsFile)){
            $this->configs = null;
        }else{
            $bconfigs = new CConfiguration($_branchConfigsFile) ;
            $this->schoolId = $schoolId;
            $this->startYear = $startYear;
            $this->configs = $bconfigs["policy"];
        }
	}

	function getYid()
	{
		if(empty($this->schoolId)){
			return 0;
		}
		if (!$this->yid)
		{
			$this->setYid();
		}
		return $this->yid;
	}

	function setYid()
	{
        Yii::import('common.models.calendar.CalendarSchool');
		$this->yid = CalendarSchool::model()->getCalendarId($this->schoolId,$this->startYear);
	}

	function getMenus(){
		$defaultMenu = array(
			CAT_INITIALS => array(
				'label' => Yii::t("invoice", "初始账单"),
				'submenuOptions' => array('class'=>'subm'),
				'items' => array(
					REG_DEPOSIT => array(
						'label' => Yii::t("invoice", "入园材料费及预缴学费"),
						'url' => array('//child/invoice/geninvoice', 't'=>'REG_DEPOSIT')
					),
					ANNUAL_CHILDCARE_AMOUNT => array(
						'label' => Yii::t("invoice", "年保育费"),
						'url' => array('//child/invoice/geninvoice', 't'=>'ANNUAL_CHILDCARE_AMOUNT')
					),
				),
				'itemOptions'=>array('class'=>'level0')
			),
			CAT_NORMAL => array(
				'label' => Yii::t("invoice", "常规账单"),
				'submenuOptions' => array('class'=>'subm'),
				'items' => array(
					PAYGROUP_ANNUAL => array(
						'label' => Yii::t("invoice", "年付费"),
						'url' => array('//child/invoice/geninvoice', 't'=>'PAYGROUP_ANNUAL')
					),
					PAYGROUP_SEMESTER => array(
						'label' => Yii::t("invoice", "学期付费"),
						'url' => array('//child/invoice/geninvoice', 't'=>'PAYGROUP_SEMESTER')
					),
					PAYGROUP_MONTH => array(
						'label' => Yii::t("invoice", "月付费"),
						'url' => array('//child/invoice/task'),
					),
					HOLIDAY_MONTH => array(
						'label' => Yii::t("invoice", "寒暑假"),
						'url' => array('//child/invoice/geninvoice', 't'=>'HOLIDAY_MONTH')
					),
				),
				'itemOptions'=>array('class'=>'level0')
			),
			CAT_OTHER => array(
				'label' => Yii::t("invoice", "其他账单"),
				'submenuOptions' => array('class'=>'subm'),
				'items' => array(
					ENABLE_AFTERSCHOOLS => array(
						'label' => Yii::t("invoice", "课外活动及亲子班"),
						'url' => array('//child/invoice/geninvoice', 't'=>'ENABLE_AFTERSCHOOLS')
					),
					FREE_LUNCH => array(
						'label' => Yii::t("invoice", "自由区间餐费"),
						'url' => array('//child/invoice/geninvoice', 't'=>'FREE_LUNCH')
					),
					FEETYPE_LIBCARD => array(
						'label' => Yii::t("invoice", "图书卡工本费"),
						'url' => array('//child/invoice/geninvoice', 't'=>'FEETYPE_LIBCARD')
					),
				),
				'itemOptions'=>array('class'=>'level0')
			),
		);

		if( !isset($this->configs[ANNUAL_CHILDCARE_AMOUNT]) || empty($this->configs[ANNUAL_CHILDCARE_AMOUNT]))
			unset($defaultMenu[CAT_INITIALS]['items'][ANNUAL_CHILDCARE_AMOUNT]);

		foreach( array(PAYGROUP_ANNUAL, PAYGROUP_SEMESTER, PAYGROUP_MONTH) as $_k ){
			if( !isset($this->configs[$_k]) || empty($this->configs[$_k]))
				unset($defaultMenu[CAT_NORMAL]['items'][$_k]);
		}

		if( !isset($this->configs[HOLIDAY_MONTH]) || empty($this->configs[HOLIDAY_MONTH]))
			unset($defaultMenu[CAT_NORMAL]['items'][HOLIDAY_MONTH]);

		if( !isset($this->configs[ENABLE_AFTERSCHOOLS]) || empty($this->configs[ENABLE_AFTERSCHOOLS]))
			unset($defaultMenu[CAT_OTHER]['items'][ENABLE_AFTERSCHOOLS]);

		if( !isset($this->configs[FEETYPE_LIBCARD]) || empty($this->configs[FEETYPE_LIBCARD]))
			unset($defaultMenu[CAT_OTHER]['items'][FEETYPE_LIBCARD]);

		if(empty($defaultMenu[CAT_OTHER]['items'])){
			unset($defaultMenu[CAT_OTHER]);
		}
		return $defaultMenu;

	}

	/**
	 * 得到某个孩子的月付费计算基准
	 * @param	Number	$childId		e.g. 1395
	 * @param	String	$tuitionType	e.g. FULL5D,
	 * @param   String  $byType         e.g. BY_ANNUAL
	 * @return  array
	 */
	public function getConstantMonthlyAmount($childId=0, $tuitionType='',$byType=''){
		$amountList = array();
		if ($childId && !empty($tuitionType) && !empty($byType))
		{
			//取学校设置是否为老生老办法
			$flag = OA::constantFeeFlag('equal',$tuitionType);
			$constantTuition = (isset($this->configs[ENABLE_CONSTANT_TUITION])) ? $this->configs[ENABLE_CONSTANT_TUITION] : false;
            //学校多收费模式是否开启
            if (isset($this->configs[MORE_FEE_BY_SCHOOL]) && $this->configs[MORE_FEE_BY_SCHOOL] === true){
                //查询孩子的班级类型
                $classtype = $this->getSchoolFeeflag($childId);
                $configs = $this->configs[TUITION_CALCULATION_BASE][$classtype];
            }else{
                $configs = $this->configs[TUITION_CALCULATION_BASE];
            }
			$configAmount = (isset($configs[$byType][AMOUNT][$tuitionType])) ? $configs[$byType][AMOUNT][$tuitionType] : 0;
			if ($constantTuition === true && $flag ===true)
			{
				$amount = $configAmount;
				if ($this->schoolId)
				{
					$criteria = new CDbCriteria;
					$criteria->compare('childid', $childId);
					$criteria->compare('schoolid', $this->schoolId);
					//$criteria->compare('tuition_type', OA::constantFeeFlag('remove',$tuitionType));
					$model = ChildConstantTuition::model()->find($criteria);
					if (!empty($model))
					{
						if ($model->amount>0.01){
							$amount = $model->amount;
						}elseif ($model->amount<0.01 && $model->startyear>0) {
							//处理老生老办法
							$tuitionType = OA::constantFeeFlag('remove',$tuitionType);
							$type = OA::POLICY_PAY;
							$_branchConfigsFile = Yii::getPathOfAlias(strtolower("application.components.policy.{$model->startyear}.{$type}.{$this->schoolId}")) . ".php";
							if(!file_exists($_branchConfigsFile)){
								$_branchConfigsFile = Yii::getPathOfAlias(strtolower("common.components.policy.{$model->startyear}.{$type}.{$this->schoolId}")) . ".php";
							}
							$bconfigs = new CConfiguration($_branchConfigsFile);
							$amount = (isset($bconfigs['policy'][TUITION_CALCULATION_BASE][$byType][AMOUNT][$tuitionType])) ? $bconfigs['policy'][TUITION_CALCULATION_BASE][$byType][AMOUNT][$tuitionType] : 0;
						}
					}
				}
				$amountList[FROM_CONSTANT] = $amount;
			}
			else
			{
				$amountList[FROM_CONFIG] = $configAmount;
			}
		}
		return $amountList;
	}

	public function getBindDiscount($childId=0){
		$result = array();
		$criteria = new CDbCriteria;
		$criteria->compare('t.childid', $childId);
		$criteria->compare('t.status', 1);
		$criteria->compare('DiscountSchool.flag', 0);
		$criteria->order = 't.id Desc';
		$model = ChildDiscountLink::model()->with('DiscountSchool')->find($criteria);

		if (!empty($model))
		{
			$result['discoundId']['id'] = $model->DiscountSchool->id;
			$result['discoundId']['title_cn'] = $model->DiscountSchool->discountTitle->title_cn;
			$result['discoundId']['title_en'] = $model->DiscountSchool->discountTitle->title_en;
			$result['discoundId']['discount'] = $model->DiscountSchool->discount;
			$result['discoundId']['expire'] = $model->DiscountSchool->expire_date;
			$result['discoundId']['active'] = ($model->DiscountSchool->expire_date < strtotime('today') || $model->DiscountSchool->stat == 0 || $model->DiscountSchool->stat == 90) ? false : true;
		}
		return $result;
	}

	/**
	 * 得到某学年每日午餐退费金额
	 * @return	int
	 */
	public function getLunchDailyRefund()
	{
		$lunchPerday = (isset($this->configs[LUNCH_PERDAY])) ? $this->configs[LUNCH_PERDAY] : 0;
		return $lunchPerday;
	}

    /**
	 * Summary
	 * 'language'
     * 'startdate'
     * 'enddate'
     * 'byType'
     * 'feeType'
     * 'feeProgram'
     * 'childId'
     * 'discountId'
     * 'serviceDay'
     * 'duetime'
     * classId
     * title
     * memo
	 * @return	Object					InvoiceModel
	 */
	public function genInvoiceFromCommonSetting($commonSetting)
	{
		//这里面需要处理有押金时的学费账单金额
        $model = new Invoice;
        extract($commonSetting);

        $feeType = strtoupper($feeType);
        switch ($feeType)
        {
            case 'FEETYPE_TUITION':
            case 'FEETYPE_LUNCH':
            case 'FEETYPE_CHILDCARE':
            case 'FEETYPE_SCHOOLBUS':
                $params = array(
                    'p_byType' => $byType,
                    'p_feeType' => $feeType,
                    'p_startDate' => $startdate,
                    'p_endDate' => $enddate,
                    'p_feeProgram' => $feeProgram,
                    'p_childId' => $childId,
                    'p_discountId' => $discountId,
                    'p_lunchType' => !is_array($serviceDay) ? unserialize($serviceDay) : $serviceDay,
                    'p_busFeeid' => $feeProgram,
                );

            $specifiedAmount = array();
            $extConfig = $this->configs[TUITION_CALCULATION_BASE][BY_MONTH][PRE_FILTER];
            if ( isset($extConfig) ){ // ES开设的双语班将调过此判断
                $bid = strtolower($this->schoolId);
                Yii::import('application.components.policy.include.Utils_'.$bid);
                $className = 'Utils_'.$bid;
                $extPolicy = new $className;
                $exts = $extPolicy->run($childId, $extConfig, $this, $startdate);
                // 区分新生老生
                if ( in_array($feeProgram, array('FULL5D')) ) {
                    $specifiedAmount = array(BY_MONTH=>$exts[AMOUNT]);
                }

            }

            $ret = $this->getCalculatedAmount($params,$specifiedAmount);
            //$ret = $this->getCalculatedAmount($params);
                $model->title=isset($title)?$title:self::genInvoiceTitle(array(
                    'byType'=>$byType,
                    'feeType'=>$feeType,
                    'startDate' => $ret['full']['startDate'],
                    'endDate' => $ret['full']['endDate'],
                    'feeProgram' => $feeProgram,
                    'language' => $language,
                ));
                if ($byType == PAYGROUP_MONTH)
                {
                	$model->installment = date('Y-m',$startdate);
                }
                $model->startdate=$ret['full']['startDate'];
                $model->enddate=$ret['full']['endDate'];
                $model->amount=$ret['full']['amount'];
                $model->original_amount=$ret['full']['amount'];
                $model->child_service_info= is_array($serviceDay) ? serialize($serviceDay) : $serviceDay;

                if ($feeType == 'FEETYPE_TUITION'){
                    $model->depositAmount = $this->getDeposit($childId);
                    if ($discountId){
                        $model->discount_id=$discountId;
                        $model->nodiscount_amount=$ret['full']['noDiscountAmount'];
                    }
                }
                if ($feeType == 'FEETYPE_SCHOOLBUS'){
                    $model->fee_other_id=$feeProgram;
                }

                break;
            case 'FEETYPE_MATERIAL':
            case 'FEETYPE_DEPOSIT':
            case 'FEETYPE_AFTERSCHOOL':
            case 'FEETYPE_CAROUSEL':
            case 'FEETYPE_BREAKFAST':
                $feemodel = FeemgtFeeOther::model()->findByPk($feeProgram);
                $model->title=isset($title)?$title:self::genInvoiceTitle(array(
                    'byType'=>$byType,
                    'feeType'=>$feeType,
                    'startDate' => $startdate,
                    'endDate' => $enddate,
                    'feeProgram' => $feeProgram,
                    'language' => $language,
                    'projectNum'=>$project_num,
                ));
                $model->startdate=$startdate;
                $model->enddate=$enddate;
                $model->amount= in_array($feeType, array(FEETYPE_AFTERSCHOOL,FEETYPE_CAROUSEL)) ? round($feemodel->amount*$project_num) : $feemodel->amount;
                $model->original_amount=in_array($feeType, array(FEETYPE_AFTERSCHOOL,FEETYPE_CAROUSEL)) ? round($feemodel->amount*$project_num) : $feemodel->amount;
                if ($feeType == 'FEETYPE_AFTERSCHOOL' || $feeType == 'FEETYPE_CAROUSEL'){
                    $model->afterschool_id=$feeProgram;
                    $model->fee_other_id=$feeProgram;
                    $model->project_num=$project_num;
                }
                break;
            case 'FEETYPE_LIBCARD':
                $model->title=isset($title)?$title:self::genInvoiceTitle(array(
                    'byType'=>$byType,
                    'feeType'=>$feeType,
                    'feeProgram' => $feeProgram,
                    'language' => $language,
                ));
                $model->amount=$this->configs['FEETYPE_LIBCARD'];
                $model->original_amount=$this->configs['FEETYPE_LIBCARD'];
                break;
        }
        $theParm = array('childId'=>$childId, 'feeType'=>$feeType, 'feeProgram'=>$feeProgram, 'startDate'=>$model->startdate, 'endDate'=>$model->enddate);
        $model->exist = $this->checkRepeatInvoice($theParm);
        $model->calendar_id=$this->getYid();
        $model->schoolid=$this->schoolId;
        $model->childid=$childId;
        $model->fee_program=$feeProgram;
        $model->amountid=$feeProgram;
        $model->fee_type = $byType;
        $model->payment_type = $feeType;
        if ($model->startdate)
            $model->duetime=isset($duetime) ? $duetime : OA::formatDateTime($model->startdate + $this->configs['DUEDAYS_OFFSET']*3600*24);
        else
            $model->duetime=isset($duetime) ? $duetime : OA::formatDateTime(time() + 2*3600*24);
        $model->inout='in';
        $model->classid = isset($classId)?$classId:0;
        $model->memo=isset($memo)?$memo:'';
        $model->userid=Yii::app()->user->id;
        $model->timestamp=time();
        $model->status=10;
        return $model;
	}

    public function genPlanFromCommonSetting($commonSetting)
    {
        extract($commonSetting);

        $month = date('Y', $startdate).date('m', $startdate);

        $criteria = new CDbCriteria();
        $criteria->compare('childid', $childId);
        $criteria->compare('schoolid', $this->schoolId);
        $criteria->compare('calendarid', $this->getYid());
        $criteria->compare('month', $month);
        $payment_type = $GLOBALS['paymentMappings']['dbFeeType'][$feeType];
        $criteria->compare('payment_type', $payment_type);
        $model = MonthlyFeeTask::model()->find($criteria);
        if ($model === null){
            $model = new MonthlyFeeTask;
        }

        $specifiedAmount = array();
        $extConfig = $this->configs[TUITION_CALCULATION_BASE][BY_MONTH][PRE_FILTER];
        if ( isset($extConfig) ){ // ES开设的双语班将调过此判断
            $bid = strtolower($this->schoolId);
            Yii::import('application.components.policy.include.Utils_'.$bid);
            $className = 'Utils_'.$bid;
            $extPolicy = new $className;
            $exts = $extPolicy->run($childId, $extConfig, $this, $startdate);
            if (isset($exts['linkedInvoice']) && $exts['linkedInvoice']){
                $model->linked_invoice_params = serialize($exts['linkedInvoice']);
            }

            // 区分新生老生
            if ( in_array($feeProgram, array('FULL5D')) ) {
                $specifiedAmount = array(BY_MONTH=>$exts[AMOUNT]);
            }
        }
        $params = array(
            'p_byType' => $byType,
            'p_feeType' => $feeType,
            'p_startDate' => $startdate,
            'p_endDate' => $enddate,
            'p_feeProgram' => $feeProgram,
            'p_childId' => $childId,
            'p_discountId' => $discountId,
            'p_lunchType' => $serviceDay,
            'p_busFeeid' => $feeProgram,
        );
        $ret=$this->getCalculatedAmount($params, $specifiedAmount);

        $model->planid=$planid;
        $model->childid=$childId;
        $model->schoolid=$this->schoolId;
        $model->calendarid=$this->getYid();
        $model->month=$month;
        $model->classid=$classId;
        $model->fee_program=$feeProgram;
        $model->amount=$ret['full']['amount'];
        $model->startdate=$startdate;
         if ($feeType == 'FEETYPE_TUITION'){
            if ($discountId){
                $model->discountid=$discountId;
                $model->nodiscount_amount=$ret['full']['noDiscountAmount'];
            }
            else {
                $model->discountid=0;
                $model->nodiscount_amount=null;
            }
        }
        $model->payment_type=$feeType;
        $model->title=isset($title)?$title:self::genInvoiceTitle(array(
            'byType'=>$byType,
            'feeType'=>$feeType,
            'startDate' => $ret['full']['startDate'],
            'endDate' => $ret['full']['endDate'],
            'feeProgram' => $feeProgram,
            'language' => $language,
        ));
        $model->child_service_info=is_array($serviceDay) ? serialize($serviceDay) : $serviceDay;
        $model->userid=Yii::app()->user->id;
        $model->create_timestamp=time();
        $model->plantime= PolicyApi::getPlandate($startdate);

        return $model;
    }

    public function calcEndDate($dateConfig, $feeProgram, $defaultDate)
    {
        if (is_array($dateConfig)) {
            return isset($dateConfig[$feeProgram]) ? $dateConfig[$feeProgram] : $defaultDate;
        }
        else {
            return $dateConfig;
        }
    }

	/**
	 * 返回费用计算值
	 * @param	string	$p_byType		计算基准, e.g. BY_MONTH, BY_ANNUAL, BY_SEMESTER1, BY_SEMESTER2
	 * @param	string	$p_feeType		账单类型, e.g. FEETYPE_TUITION, FEETYPE_LUNCH, FEETYPE_CHILDCARE,
	 * @param	string	$p_startDate	就读时间
	 * @param	string	$p_endDate		结束时间，只针对寒暑假
	 * @param	string	$p_feeProgram	费用标准, e.g. FULL5D, HALF2D, OTHER_PROGRAM_CHN
	 * @param	int 	$p_childId		孩子标识
	 * @param	int		$p_discountId	折扣标识
	 * @param 	array	$p_lunchType	餐费类型	  e.g. array('mon'=>10,'tue'=>10,'wed'=>10,'thu'=>10,'fri'=>10)
	 * @param	int		$p_busFeeid		校车费配置ID
	 * @param	array	$specifiedAmount		指定某种计算基准的基准金额 e.g. array(BY_MONTH=>450)
	 * @return	array				Description
	 */
	public function getCalculatedAmount($params, $specifiedAmount=null)
	{
		extract($params);

		$result = null;
		if (empty($p_byType) || empty($p_feeType) || empty($p_startDate))
		{
			return $result;
		}
		$byType = $p_byType;
		$startDate = $p_startDate;
		$calendarSemesterList = CalendarSemester::model()->getSemesterTimeStamp($this->getYid());
		//开始日期是否在校历之内
		if ($startDate<$calendarSemesterList['fall_start'])
		{
			$startDate = $calendarSemesterList['fall_start'];
		}
		$byType = $this->getSemester($byType, $startDate);
		$globalDiscount = null;
		$baseConfig = $this->configs[TUITION_CALCULATION_BASE];
		switch (strtoupper($p_byType))
		{
			case PAYGROUP_MONTH:
				$calendarStartDate = $calendarSemesterList['fall_start'];
				$endDate = mktime(0,0,0,date('m',$startDate),date('t',$startDate),date('Y',$startDate));
				break;
			case PAYGROUP_SEMESTER:
				if ($startDate <= $calendarSemesterList['fall_end'])
				{
					$calendarStartDate = $calendarSemesterList['fall_start'];
					$endDate = $calendarSemesterList['fall_end'];
					if (isset($baseConfig[BY_SEMESTER1][ENDDATE]) && $baseConfig[BY_SEMESTER1][ENDDATE]) {
						$endDate = $this->calcEndDate($baseConfig[BY_SEMESTER1][ENDDATE], $p_feeProgram, $endDate);
					}
					$globalDiscount = DISCOUNT_SEMESTER1;
				}
				else
				{
					$calendarStartDate = $calendarSemesterList['spring_start'];
					$endDate = $calendarSemesterList['spring_end'];
					if (isset($baseConfig[BY_SEMESTER2][ENDDATE]) && $baseConfig[BY_SEMESTER2][ENDDATE]) {
						$endDate = $this->calcEndDate($baseConfig[BY_SEMESTER2][ENDDATE], $p_feeProgram, $endDate);
					}
					$globalDiscount = DISCOUNT_SEMESTER2;
				}
				break;
			case PAYGROUP_ANNUAL:
				$calendarStartDate = $calendarSemesterList['fall_start'];
				$endDate = $calendarSemesterList['spring_end'];
				if (isset($baseConfig[BY_ANNUAL][ENDDATE]) && $baseConfig[BY_ANNUAL][ENDDATE]) {
					$endDate = $this->calcEndDate($baseConfig[BY_ANNUAL][ENDDATE], $p_feeProgram, $endDate);
				}
				$globalDiscount = DISCOUNT_ANNUAL;
				break;
			case HOLIDAY_MONTH:
				$calendarStartDate = $calendarSemesterList['fall_start'];
				$endDate = $p_endDate;
				break;
			case FREE_LUNCH:
				$endDate = min($p_endDate, $calendarSemesterList['spring_end']);
				break;
            case ANNUAL_CHILDCARE_AMOUNT:
                if ($this->configs[CALC_CHILDCARE_PEROID] ==1){
                    $calendarStartDate = $calendarSemesterList['fall_start'];
                    $endDate = $calendarSemesterList['spring_end'];
                }elseif ($this->configs[CALC_CHILDCARE_PEROID] ==2) {
                    if ($startDate <= $calendarSemesterList['fall_end'])
                    {
                        $calendarStartDate = $calendarSemesterList['fall_start'];
                        $endDate = $calendarSemesterList['fall_end'];
                    }
                    else
                    {
                        $calendarStartDate = $calendarSemesterList['spring_start'];
                        $endDate = $calendarSemesterList['spring_end'];
                    }
                }
                break;
			default:
				return $result;
		}
		//get schoolday
		$schoolDays = CalendarSchoolDays::model()->countCalendarSchoolday($this->getYid(),$startDate,$endDate,$calendarStartDate, $p_feeType==FEETYPE_LUNCH ? true : false);
        $classtype = null;
        $lunchFlag = false;
        if (isset($this->configs[MORE_FEE_BY_SCHOOL]) && $this->configs[MORE_FEE_BY_SCHOOL] === true){
            $classtype = $this->getSchoolFeeflag($p_childId);
            $lunchFlag = true;
        }
		//初始化计算方法
		$Fee = new FeeArithmetic($this->configs,$schoolDays,$startDate,$endDate,$globalDiscount,$classtype);
		//业务
		switch (strtoupper($p_feeType))
		{
			case FEETYPE_TUITION:
				if ($byType == BY_MONTH)
				{
					if (empty($specifiedAmount))
					{
						$amountList = $this->getConstantMonthlyAmount($p_childId,$p_feeProgram,$byType);
					}
					else
					{
						$amountList = $specifiedAmount;
					}

				}
				else
				{
					$amountList = $this->getConstantMonthlyAmount($p_childId,$p_feeProgram,$byType);
				}
				if (is_array($amountList) && count($amountList))
				{
					$result = $Fee->getTuitionFee($byType,current($amountList),$this->schoolId,$p_discountId,$p_byType);
				}
				else
				{
					return $result;
				}
				break;
			case FEETYPE_LUNCH:
                if ($lunchFlag === true){
                    if(is_array($this->configs[TUITION_CALCULATION_BASE][$classtype][BY_MONTH][LUNCH_PERDAY])){
                        $amount = isset($this->configs[TUITION_CALCULATION_BASE][$classtype][BY_MONTH][LUNCH_PERDAY][$params['p_feeProgram']]) ? $this->configs[TUITION_CALCULATION_BASE][$classtype][BY_MONTH][LUNCH_PERDAY][$params['p_feeProgram']] : 0;
                    }else{
                        $amount = isset($this->configs[TUITION_CALCULATION_BASE][$classtype][BY_MONTH][LUNCH_PERDAY]) ? $this->configs[TUITION_CALCULATION_BASE][$classtype][BY_MONTH][LUNCH_PERDAY] : 0;
                    }
                }else{
                    if(is_array($this->configs[LUNCH_PERDAY])){
                        $amount = (isset($this->configs[LUNCH_PERDAY][$params['p_feeProgram']])) ? $this->configs[LUNCH_PERDAY][$params['p_feeProgram']] : 0;
                    }else{
                        $amount = (isset($this->configs[LUNCH_PERDAY])) ? $this->configs[LUNCH_PERDAY] : 0;
                    }
                }
				$result = $Fee->getLunchFee($amount,$p_lunchType);
				break;
			case FEETYPE_CHILDCARE:
				$amount = (isset($this->configs[ANNUAL_CHILDCARE_AMOUNT])) ? $this->configs[ANNUAL_CHILDCARE_AMOUNT] : 0;
				$result = $Fee->getChildcareFee($amount,$p_byType);
				break;
			case FEETYPE_SCHOOLBUS:
				$feeModel = FeemgtFeeOther::model()->findByPk($p_busFeeid);
				$amount = $feeModel->amount;
				$result = $Fee->getBusFee($amount,$p_byType);
				break;
			default:
				return $result;
		}
		return $result;
	}

    public function  saveInvoice($model, $feeType){
        global $paymentMappings;
        $model->payment_type = $paymentMappings['dbFeeType'][$model->payment_type];
        $model->fee_type = (isset($paymentMappings['invoicePeriodTypes'][$model->fee_type])) ? $paymentMappings['invoicePeriodTypes'][$model->fee_type] : 0;
//        if(strtotime($model->duetime))
//        $model->duetime = strtotime($model->duetime);
        switch($feeType){
            case FEETYPE_TUITION:
            	/*处理预缴学费*/
                $depsoited = false;
                $useDeposit = 0;
                $model->fee_program = OA::constantFeeFlag('remove',$model->fee_program);
                $model->depositAmount=$this->getDeposit($model->childid);
                if ($model->depositAmount)
                {
                	if ($model->depositAmount-$model->amount >= 0)
                	{
                		$useDeposit = $model->amount;
                		$model->amount = 0.00;
                	}
                	else
                	{
                		$useDeposit = $model->depositAmount;
                		$model->amount = $model->amount-$model->depositAmount;
                	}
                    $depsoited = true;
                }
                if ($model->save() && $depsoited == true)
                {
                    $temModel = new TemporaryDeposit;
                    $temModel->invoice_id = $model->invoice_id;
                    $temModel->childid = $model->childid;
                    $temModel->yid = $model->calendar_id;
                    $temModel->amount = $useDeposit;
                    $temModel->save();
                }
                /*处理代收代缴等情况*/
                if (count($model->connectedInvoices))
                {
                	$connectedInvoices = array();
                	foreach ($model->connectedInvoices as $val)
                	{
                		$newModel = new Invoice();
                		$newModel->setAttributes($model->getAttributes());
                		$newModel->amount = $val[AMOUNT];
                		$newModel->original_amount = $val[AMOUNT];
                		$newModel->payment_type = $paymentMappings['dbFeeType'][$val[FEETYPE]];
                		$newModel->discount_id = isset($val[DISCOUNTID]) ? $val[DISCOUNTID] :0;
                		$newModel->title = $val[TITLE];
                		if ($newModel->save())
                		{
                			Invoice::model()->updateByPk($model->invoice_id, array('flag'=>$newModel->invoice_id));
                			$connectedInvoices[] = $newModel;
                		}
                		unset($newModel);
                	}
                	$model->connectedInvoices = null;
                	$model->connectedInvoices = $connectedInvoices;
                }
                return $model;
                break;
            case FEETYPE_LUNCH:
                if (isset($this->configs[MORE_FEE_BY_SCHOOL]) && $this->configs[MORE_FEE_BY_SCHOOL] === true){
                    $model->child_service_info = str_replace(40,20,$model->child_service_info);
                    $model->save();
                }else{
                    // 获取退费的配置信息
                    $typeRefund = OA::POLICY_REFUND;
                    $_branchConfigsFile = Yii::getPathOfAlias(strtolower("application.components.policy.{$this->startYear}.{$typeRefund}.{$this->schoolId}")) . ".php";
                    $bconfigsRefund = new CConfiguration($_branchConfigsFile) ;

                    $config = $this->configs['LUNCH_PERDAY'];
                    $configRedfund = $bconfigsRefund['policy']['LUNCH_PERDAY'];

                    $model->child_service_info = str_replace(40,20,$model->child_service_info);
                    if($model->save()){
                        $modelLunchUnit = new InvoiceLunchUnit();
                        $modelLunchUnit->invoice_id = $model->invoice_id;
                        $modelLunchUnit->price = (isset($config)) ? ((is_array($config)) ? $config[$model->fee_program] : $config ) : 0 ;
                        $modelLunchUnit->refund = (isset($configRedfund)) ? ((is_array($configRedfund)) ? $configRedfund[$model->fee_program] : $configRedfund ) : 0 ;
                        $modelLunchUnit->save();
                    }
                }
                return $model;
                break;
            default:
                $model->save();
               	return $model;
                break;
        }

    }

	/**
	 * 得到某参数数组下可选学费的列表，参数包括孩子id，付费时间段等信息
	* @param	array||string	$type			Description
	 * @param	string			$byType			Description
	 * @param	int				$childId		Description
	 * @param	int				$startYear		Description
	 * @return	Object							Description
	 */
	public function getTuitionOptionsList($startYear, $byType, $childId)
	{
		$result = null;
		if (isset($this->configs[$byType]) && isset($this->configs[$byType][TUITION_CALC_BASE]))
		{
		    $fromType = $this->getSemester($byType, $startDate);
            //判断学校多收费模式是否开启
            if (isset($this->configs[MORE_FEE_BY_SCHOOL]) && $this->configs[MORE_FEE_BY_SCHOOL] === true){
                //查询孩子的班级类型
                $classtype = $this->getSchoolFeeflag($childId);
                $configs = $this->configs[TUITION_CALCULATION_BASE][$classtype];
            }else{
                $configs = $this->configs[TUITION_CALCULATION_BASE];
            }
		    if (isset($configs[$fromType][AMOUNT]) && count($configs[$fromType][AMOUNT]))
		    {
				global $paymentMappings;
				if (isset($this->configs[ENABLE_CONSTANT_TUITION]) && $this->configs[ENABLE_CONSTANT_TUITION] === true)
				{
					$criteria = new CDbCriteria;
					$criteria->compare('childid', $childId);
					$criteria->compare('schoolid', $this->schoolId);
					$model = ChildConstantTuition::model()->find($criteria);
					if (!empty($model) && (($model->amount+$model->startyear)>0.01))
					{
						if ($model->amount>0.01 && !in_array($this->schoolId,$this->constantSchoolList)){
							$result[OA::constantFeeFlag('add', $model->tuition_type)] = array(
							'amount' => $model->amount,
							'cn_title' => '双轨制价格' . $paymentMappings['programTitle'][$model->tuition_type],
							'en_title' => '双轨制价格' . $paymentMappings['programTitle'][$model->tuition_type],
							);
						}else{
							//针对IA
							$type = OA::POLICY_PAY;
							$_branchConfigsFile = Yii::getPathOfAlias(strtolower("application.components.policy.{$model->startyear}.{$type}.{$this->schoolId}")) . ".php";
							if(!file_exists($_branchConfigsFile)){
								$_branchConfigsFile = Yii::getPathOfAlias(strtolower("common.components.policy.{$model->startyear}.{$type}.{$this->schoolId}")) . ".php";
							}
							$bconfigs = new CConfiguration($_branchConfigsFile);
							if ($byType === PAYGROUP_SEMESTER && $fromType != BY_MONTH)
							{
								foreach (array(BY_SEMESTER1,BY_SEMESTER2) as $sVal)
								{
									foreach ($bconfigs['policy'][TUITION_CALCULATION_BASE][$sVal][AMOUNT] as $k => $v)
									{
									$result[OA::constantFeeFlag('add', $k)] = array(
										'amount' => isset($result[OA::constantFeeFlag('add', $k)]['amount']) ? Yii::t('payment', ':amount1 / :amount2', array(':amount1'=>$result[OA::constantFeeFlag('add', $k)]['amount'], ':amount2'=>$v)) : $v,
										'cn_title' => '双轨制价格' . $paymentMappings['programTitle'][$k],
										'en_title' => '双轨制价格' . $paymentMappings['programTitle'][$k],
									);
									}
								}
							}
							else
							{
								foreach ($bconfigs['policy'][TUITION_CALCULATION_BASE][$fromType][AMOUNT] as $k => $v)
								{
									$result[OA::constantFeeFlag('add', $k)] = array(
									'amount' => $v,
									'cn_title' => '双轨制价格' . $paymentMappings['programTitle'][$k],
									'en_title' => '双轨制价格' . $paymentMappings['programTitle'][$k],
									);
								}
							}
						}
					}
				}
				if ($byType === PAYGROUP_SEMESTER && $fromType != BY_MONTH)
				{
					foreach (array(BY_SEMESTER1,BY_SEMESTER2) as $sVal)
					{
						foreach ($configs[$sVal][AMOUNT] as $k => $v)
						{
							$result[$k] = array(
							'amount' => isset($result[$k]['amount']) ? Yii::t('payment', ':amount1 / :amount2', array(':amount1'=>$result[$k]['amount'], ':amount2'=>$v)) : $v,
							'cn_title' => $paymentMappings['programTitle'][$k],
							'en_title' => $paymentMappings['programTitle'][$k],
							);
						}
					}
				}
				else
				{
					foreach ($configs[$fromType][AMOUNT] as $k => $v)
					{
						$result[$k] = array(
							'amount' => $v,
							'cn_title' => $paymentMappings['programTitle'][$k],
							'en_title' => $paymentMappings['programTitle'][$k],
						);
					}
				}
		    }
		}
		return $result;
	}

	/**
	 * 获取配置中的缴费金额
	 * @param	array||string	$type			Description
	 * @param	string			$byType			Description
	 * @param	int				$childId		Description
	 * @return	array							Description
	 */
	public function getConfigedFeeAmounts($type, $startYear, $byType, $childId)
	{
		$result = null;
		if(!is_array($type)){
			$type = array($type);
		}
		global $paymentMappings;
		foreach($type as $_t)
		switch($_t){
			case FEETYPE_TUITION:
				$result[$_t] = $this->getTuitionOptionsList($startYear, $byType, $childId);
				break;
			case FEETYPE_LUNCH:
                //判断学校多收费模式是否开启
                if (isset($this->configs[MORE_FEE_BY_SCHOOL]) && $this->configs[MORE_FEE_BY_SCHOOL] === true){
                    //查询孩子的班级类型
                    $classtype = $this->getSchoolFeeflag($childId);
                    $configs = $this->configs[TUITION_CALCULATION_BASE][$classtype];
                    $amount = $configs[BY_MONTH][LUNCH_PERDAY];
                }else{
                    $amount = $this->configs[LUNCH_PERDAY];
                }

                if(is_array($amount)){
                    foreach ($amount as $k=>$item) {
                        $result[$_t][$k] = array(
                            'cn_title' => $paymentMappings['lunch'][$k],
                            'en_title' => $paymentMappings['lunch'][$k],
                            'amount'   => $item
                        );
                    }
                }else{
                    $result[$_t][LUNCH_PERDAY] = array(
                        'cn_title' => $paymentMappings['titleFeeType'][FEETYPE_LUNCH],
                        'en_title' => $paymentMappings['titleFeeType'][FEETYPE_LUNCH],
                        'amount'   => $amount
                    );
                }
				break;
			case FEETYPE_LIBCARD:
				$result[$_t][FEETYPE_LIBCARD] = array(
					'cn_title' => $paymentMappings['titleFeeType'][FEETYPE_LIBCARD],
					'en_title' => $paymentMappings['titleFeeType'][FEETYPE_LIBCARD],
					'amount'   => $this->configs[FEETYPE_LIBCARD]
				);
				break;
            case FEETYPE_CHILDCARE:
				$result[$_t][FEETYPE_CHILDCARE] = array(
					'cn_title' => $paymentMappings['titleFeeType'][FEETYPE_CHILDCARE],
					'en_title' => $paymentMappings['titleFeeType'][FEETYPE_CHILDCARE],
					'amount'   => $this->configs[ANNUAL_CHILDCARE_AMOUNT]
				);
				break;
		}

		return $result;
	}

	/**
	 * 根据开始日期取得上下学期
	 * @param string $byType
	 * @param string $startYear
	 * @return tom
	 */

	public function getSemester($byType,$startDate)
	{
		$fromType = null;
		if (strtoupper($byType) == PAYGROUP_SEMESTER)
		{
			if ($this->configs[$byType][TUITION_CALC_BASE] != BY_MONTH)
			{
				$startMonth = date('Ym',$startDate);

				// 如果第一学期开始与第二学期结束月份相同
				if ($this->configs[TIME_POINTS][1] == $this->configs[TIME_POINTS][2]) {
					$criter = new CDbCriteria();
					$criter->compare('yid', $this->getYid());
					$criter->compare('school_start_timestamp', '<=' . $startDate);
					$criter->compare('school_end_timestamp', '>=' . $startDate);
					$model = CalendarSemester::model()->find($criter);
					if ($model) {
						if ($model->semester_flag == 10) {
							return $this->configs[$byType][TUITION_CALC_BASE].'1';
						} else {
							return $this->configs[$byType][TUITION_CALC_BASE].'2';
						}
					}
				}

				if ($startMonth >= $this->configs[TIME_POINTS][0] && $startMonth <= $this->configs[TIME_POINTS][1])
				{
					$fromType = $this->configs[$byType][TUITION_CALC_BASE].'1';
				}
				else
				{
					$fromType = $this->configs[$byType][TUITION_CALC_BASE].'2';
				}
			}
			else
			{
				$fromType = $this->configs[$byType][TUITION_CALC_BASE];
			}
		}
		else
		{
			$fromType = $this->configs[$byType][TUITION_CALC_BASE];
		}
		return $fromType;
	}

	/**
	 * 根据参数拼出账单标题，参数待定
	 * @param	string	$byType		计算基准, e.g. BY_MONTH, BY_ANNUAL, BY_SEMESTER1, BY_SEMESTER2
	 * @param	string	$feeType	账单类型, e.g. FEETYPE_TUITION, FEETYPE_LUNCH, FEETYPE_CHILDCARE,
	 * @param	int		$startDate	就读时间
	 * @param	int		$endDate	结束时间
	 * @param	string	$feeProgram	费用标准, e.g. FULL5D, HALF2D, OTHER_PROGRAM_CHN
	 * @param	string	$language	费用标准, e.g. zh_cn/en_us
	 * @param	number	$projectNum	课外活动节数, e.g. 5
	 * @return	array				Description
	 */
    public function genInvoiceTitle($params)
    {
    	extract($params);
    	$ret = null;
		global $paymentMappings;
		$pattern = array(
			'period' => ":beginTime - :endTime :feeProgram :feeType",
			'single' => ":singleTime :feeProgram :feeType",
		);

    	switch ($feeType)
    	{
    		case FEETYPE_MATERIAL:
    		case FEETYPE_DEPOSIT:
                $ret = OA::t("payment", $pattern['period'], $language,
                    array(
                        ":beginTime"=>$this->startYear,
                        ":endTime"=>$this->startYear + 1,
						':feeProgram' => '',
                        ':feeType'=> OA::t("payment", $paymentMappings['titleFeeType'][$feeType], $language),
                    ), null);

    			break;
			case FEETYPE_SCHOOLBUS:
				// 单独处理启明星账单标题
				if (in_array($this->schoolId, array('BJ_DS', 'BJ_SLT', 'BJ_QFF'))) {
					if ($byType == PAYGROUP_SEMESTER) {
						$ret = sprintf("%s-%s第一学期校车费/Term 1 School Bus Fee", $this->startYear, $this->startYear + 1);
						if (date('m', $startDate) < 7) {
//							$ret = sprintf("%s-%s第二学期校车费/Term 2 School Bus Fee", $this->startYear, $this->startYear + 1);
							$ret = sprintf("%s-%s后六学月校车/the Last 6 School Months Bus Fee", $this->startYear, $this->startYear + 1);
						}
					}
					if ($byType == PAYGROUP_ANNUAL) {
						$ret = sprintf("%s-%s学年校车费/Annual School Bus Fee", $this->startYear, $this->startYear + 1);
					}
					break;
				}
    		case FEETYPE_LUNCH:
				// 单独处理启明星账单标题
				if (in_array($this->schoolId, array('BJ_DS', 'BJ_SLT', 'BJ_QFF'))) {
					if ($byType == PAYGROUP_SEMESTER) {
						$ret = sprintf("%s-%s第一学期餐费/Term 1 Lunch Fee", $this->startYear, $this->startYear + 1);
						if (date('m', $startDate) < 7) {
//							$ret = sprintf("%s-%s第二学期餐费/Term 2 Lunch Fee", $this->startYear, $this->startYear + 1);
							$ret = sprintf("%s-%s后六学月校餐/the Last 6 School Months Meal Fee", $this->startYear, $this->startYear + 1);
						}
					}
					if ($byType == PAYGROUP_ANNUAL) {
						$ret = sprintf("%s-%s学年餐费/Annual Lunch Fee", $this->startYear, $this->startYear + 1);
					}
					break;
				}
    		case FEETYPE_CHILDCARE:
    			if ($byType == PAYGROUP_MONTH)
    			{
    				$ret = OA::t("payment", $pattern['single'], $language,
                    array(
                        ':singleTime'=> date( OA::t("payment", "M, Y", $language),$startDate),
						':feeProgram'=>'',
                        ':feeType'=> OA::t("payment", $paymentMappings['titleFeeType'][$feeType], $language),
                    ));
    			}
    			elseif ($byType == HOLIDAY_MONTH)
    			{
					$ret = OA::t("payment", $pattern['period'], $language,
					array(
						':beginTime'=> date( OA::t("payment", "M d, Y", $language),$startDate),
						':endTime'=> date( OA::t("payment", "M d, Y", $language),$endDate),
						':feeProgram' => '',
						':feeType'=> OA::t("payment", $paymentMappings['titleFeeType'][$feeType], $language),
					));
    			}
                elseif ($byType == FREE_LUNCH){
                    if ($startDate == $endDate){
                        $ret = OA::t("payment", $pattern['single'], $language,
                        array(
                            ':singleTime'=> date( OA::t("payment", "M d, Y", $language),$startDate),
                            ':feeProgram'=>'',
                            ':feeType'=> OA::t("payment", $paymentMappings['titleFeeType'][$feeType], $language),
                        ));
                    }
                    else{
                        $ret = OA::t("payment", $pattern['period'], $language,
                        array(
                            ':beginTime'=> date( OA::t("payment", "M d, Y", $language),$startDate),
                            ':endTime'=> date( OA::t("payment", "M d, Y", $language),$endDate),
                            ':feeProgram' => '',
                            ':feeType'=> OA::t("payment", $paymentMappings['titleFeeType'][$feeType], $language),
                        ));
                    }
                }
    			else
    			{
    				$ret = OA::t("payment", $pattern['period'], $language,
                    array(
                        ':beginTime'=> date( OA::t("payment", "M, Y", $language),$startDate),
                        ':endTime'=> date( OA::t("payment", "M, Y", $language),$endDate),
						':feeProgram' => '',
                        ':feeType'=> OA::t("payment", $paymentMappings['titleFeeType'][$feeType], $language),
                    ));
    			}
    			break;
    		case FEETYPE_TUITION:
    			$formType = $this->getSemester($byType, $startDate);
				$feeProgram = OA::constantFeeFlag('remove',$feeProgram);
    			if (count($this->configs[TUITION_CALCULATION_BASE][$formType][AMOUNT])>=2)
    			{
    				if ($byType == PAYGROUP_MONTH)
    				{
	    				$ret = OA::t("payment", $pattern['single'], $language,
	                    array(
	                        ':singleTime'=> date( OA::t("payment", "M, Y", $language),$startDate),
	                        ':feeProgram'=> OA::t("payment", $paymentMappings['programTitle'][$feeProgram], $language),
	                        ':feeType'=> OA::t("payment", $paymentMappings['titleFeeType'][$feeType], $language),
	                    ));
    				}
    				elseif ($byType == HOLIDAY_MONTH)
    				{
	    				$ret = OA::t("payment", $pattern['period'], $language,
	                    array(
	                        ':beginTime'=> date( OA::t("payment", "M d, Y", $language),$startDate),
	                        ':endTime'=> date( OA::t("payment", "M d, Y", $language),$endDate),
	                        ':feeProgram'=> OA::t("payment", $paymentMappings['programTitle'][$feeProgram], $language),
	                        ':feeType'=> OA::t("payment", $paymentMappings['titleFeeType'][$feeType], $language),
	                    ));
    				}
    				else
    				{
	    				$ret = OA::t("payment", $pattern['period'], $language,
	                    array(
	                        ':beginTime'=> date( OA::t("payment", "M, Y", $language),$startDate),
	                        ':endTime'=> date( OA::t("payment", "M, Y", $language),$endDate),
	                        ':feeProgram'=> OA::t("payment", $paymentMappings['programTitle'][$feeProgram], $language),
	                        ':feeType'=> OA::t("payment", $paymentMappings['titleFeeType'][$feeType], $language),
	                    ));
    				}
    			}
    			else
    			{
    				if ($byType == PAYGROUP_MONTH)
    				{
	    				$ret = OA::t("payment", $pattern['single'], $language,
	                    array(
	                        ':singleTime'=> date( OA::t("payment", "M, Y", $language),$startDate),
							':feeProgram'=> '',
	                        ':feeType'=> OA::t("payment", $paymentMappings['titleFeeType'][$feeType], $language),
	                    ));
    				}
    				elseif ($byType == HOLIDAY_MONTH)
    				{
	    				$ret = OA::t("payment", $pattern['period'], $language,
	                    array(
	                        ':beginTime'=> date( OA::t("payment", "M d, Y", $language),$startDate),
	                        ':endTime'=> date( OA::t("payment", "M d, Y", $language),$endDate),
							':feeProgram'=> '',
	                        ':feeType'=> OA::t("payment", $paymentMappings['titleFeeType'][$feeType], $language),
	                    ));
    				}
    				else
    				{
	    				$ret = OA::t("payment", $pattern['period'], $language,
	                    array(
	                        ':beginTime'=> date( OA::t("payment", "M, Y", $language),$startDate),
	                        ':endTime'=> date( OA::t("payment", "M, Y", $language),$endDate),
							':feeProgram'=> '',
	                        ':feeType'=> OA::t("payment", $paymentMappings['titleFeeType'][$feeType], $language),
	                    ), null);
						// 单独处理启明星账单标题
						if (in_array($this->schoolId, array('BJ_DS', 'BJ_SLT', 'BJ_QFF'))) {
							if ($byType == PAYGROUP_SEMESTER) {
								$ret = sprintf("%s-%s第一学期学费/Term 1 Tuition", $this->startYear, $this->startYear + 1);
								if (date('m', $startDate) < 7) {
									$ret = sprintf("%s-%s第二学期学费/Term 2 Tuition", $this->startYear, $this->startYear + 1);
								}
							}
							if ($byType == PAYGROUP_ANNUAL) {
								$ret = sprintf("%s-%s学年学费/Annual Tuition", $this->startYear, $this->startYear + 1);
							}
							break;
						}
    				}
    			}
    			break;
    		case FEETYPE_AFTERSCHOOL:
    			$ret = Yii::t('payment', 'Extended Care').' × '.$projectNum;
    			break;
    		case FEETYPE_CAROUSEL:
    			$ret = OA::t("payment", $pattern['period'], $language,
					array(
						':beginTime'=> date( OA::t("payment", "M d, Y", $language),$startDate),
						':endTime'=> date( OA::t("payment", "M d, Y", $language),$endDate),
						':feeProgram' => '',
						':feeType'=> OA::t("payment", $paymentMappings['titleFeeType'][$feeType], $language),
					)).' × '.$projectNum;
    			break;
            case FEETYPE_BREAKFAST:
                $ret = OA::t("payment", $pattern['period'], $language,
					array(
						':beginTime'=> date( OA::t("payment", "M d, Y", $language),$startDate),
						':endTime'=> date( OA::t("payment", "M d, Y", $language),$endDate),
						':feeProgram' => '',
						':feeType'=> OA::t("payment", $paymentMappings['titleFeeType'][$feeType], $language),
					));
    			break;
    		case FEETYPE_COLLECTION:
    		case FEETYPE_SUBSIDY:
    			$ret = OA::t("payment", $pattern['single'], $language,
    			array(
                        ':singleTime'=> date( OA::t("payment", "M, Y", $language),$startDate),
						':feeProgram'=>'',
                        ':feeType'=> OA::t("payment", $paymentMappings['titleFeeType'][$feeType], $language),
    			), null);
    			break;
    		case FEETYPE_LIBCARD:
    			$ret = OA::t('payment', $paymentMappings['titleFeeType'][$feeType], $language);
    			break;
    	}

    	return $ret;
    }

    public function getDeposit($childId=0)
    {
        if ($childId){
            $yid = $this->getYid();
			// 查找条件下是否有符合条件的预缴学费账单
			$criteria = new CDbCriteria();
			$criteria->compare('calendar_id', $yid);
			$criteria->compare('payment_type', 'deposit');
			$criteria->compare('childid', $childId);
			$criteria->compare('schoolid', $this->schoolId);
			$criteria->compare('status', Invoice::STATS_PAID);
			$depositInvoice = Invoice::model()->count($criteria);
			if (!$depositInvoice) {
				return 0;
			}
            $ret = PolicyApi::getDepositAmount($childId, $yid, false);
			if(!is_null($ret)){
				$ret = array_shift($ret);
				return $ret['amount'];
			}
        }
    }

    /**
     * 验证开学费账单必要条件(预缴学费还未付款、有使用预缴学费的学费账单还未付款、有预缴学费正在退费)
     * @param int $childId
     * @return boolean
     */

    public function checkTuitionCreation($childId)
    {
    	global $paymentMappings;
    	//预缴学费还未付款
    	$criter = new CDbCriteria();
    	$criter->compare('childid', $childId);
    	$criter->compare('calendar_id', $this->getYid());
    	$criter->compare('schoolid', $this->schoolId);
    	$criter->compare('`inout`', 'in');
    	$criter->compare('payment_type', $paymentMappings['dbFeeType'][FEETYPE_DEPOSIT]);
    	$criter->addNotInCondition('status', array(20,99,88));
    	if (Invoice::model()->exists($criter))
    	{
    		return RC::ERR_TC_DEPOSIT_NOT_PAID;
    	}

    	//有使用预缴学费的学费账单还未付款
    	$criter = new CDbCriteria();
    	$criter->compare('childid', $childId);
    	$criter->compare('yid', $this->getYid());
    	if (TemporaryDeposit::model()->exists($criter))
    	{
            return RC::ERR_TC_TUITION_WITH_DEPOSIT_NOT_PAID;
    	}

    	//有预缴学费正在退费
    	$criter = new CDbCriteria();
    	$criter->compare('childid', $childId);
    	$criter->compare('yid', $this->getYid());
    	$criter->compare('schoolid', $this->schoolId);
    	$criter->compare('payment_type', $paymentMappings['dbFeeType'][FEETYPE_DEPOSIT]);
    	$criter->addInCondition('status', array(10, 77));
    	if (InvoiceChildRefund::model()->exists($criter))
    	{
    		return RC::ERR_TC_DEPOSIT_IN_REFUND_PROGRESS;
    	}

    	return true;
    }

    /**
     * 取孩子所在的班级
     * @param int $childId
     * @param int $recentClassId
     * @return number
     */
    public function getClassId($childId=0,$recentClassId=0){
    	$classId = 0;
    	if ( !$childId)
    	{
    		return $classId;
    	}
    	$branchModel = Branch::model()->getBranchInfo($this->schoolId,array('schcalendar'));
    	$currentCalendarId = $branchModel['schcalendar'];
    	if ($currentCalendarId != $this->getYid())
    	{
    		$reserveModel = ChildReserve::model()->find('childid=:childId and schoolid=:schoolId and calendar=:calendarId',array(':childId'=>$childId,':calendarId'=>$this->getYid(),':schoolId'=>$this->schoolId));
    		if (!empty($reserveModel))
    		{
    			$classId = $reserveModel->classid;
    		}
    	}
    	else
    	{
            if (!$recentClassId){
                $childModel = ChildProfileBasic::model()->findByPk($childId);
                $recentClassId = $childModel->classid;
            }
    		$classId = $recentClassId;
    	}
        return $classId;
    }

    /**
     * 初始化月计划生成学费账单对象
     * @param object $taskModel
     * @return Invoice
     */
    public function genPlanModelSetting($taskModel, $opuid=0)
    {
    	global $paymentMappings;
    	$dbFeeType = $paymentMappings['dbFeeType'];
    	$dbFeeType = array_flip($dbFeeType);
    	$model = new Invoice;
    	$model->setAttributes($taskModel->getAttributes(array('amount','nodiscount_amount','schoolid','classid','childid','startdate','title','child_service_info','userid','fee_program')));
        if ($opuid){
            $model->userid = $opuid;
        }
    	$model->calendar_id = $taskModel->calendarid;
    	$model->original_amount = $taskModel->amount;
    	$model->discount_id = $taskModel->discountid;
    	$model->payment_type = $dbFeeType[$taskModel->payment_type];
    	$model->inout = 'in';
    	$model->fee_type = PAYGROUP_MONTH;
    	$model->enddate = strtotime($taskModel->month.date('t',$taskModel->startdate));
    	$model->installment = date('Y-m',$taskModel->startdate);
    	$model->gen_latefees_date = 0;
    	$model->duetime  = $taskModel->startdate + $this->configs['DUEDAYS_OFFSET'] * 24 * 3600;
    	$model->timestamp = time();
    	$model->status = Invoice::STATS_UNPAID;
        if ($dbFeeType[$taskModel->payment_type] == 'FEETYPE_SCHOOLBUS'){
            $model->fee_other_id=$taskModel->fee_program;
        }
    	if (!empty($taskModel->linked_invoice_params))
    	{
    		$model->connectedInvoices = unserialize($taskModel->linked_invoice_params);
    	}

        $theParm = array('childId'=>$model->childid, 'feeType'=>$model->payment_type, 'feeProgram'=>$taskModel->fee_program, 'startDate'=>$model->startdate, 'endDate'=>$model->enddate);
        $model->exist = $this->checkRepeatInvoice($theParm);

    	return $model;
    }

	/**
     * 检查重复的账单
     * @param int $childId			孩子主键		e.g.	1392
     * @param string $feeType		费用类型		e.g.	FEETYPE_TUITION, FEETYPE_LUNCH, FEETYPE_BUS
     * @param string $feeProgram	费用标准		e.g.	FULL5D, HALF2D, OTHER_PROGRAM_CHN
     * @param int $startDate		开始日期		e.g.	时间戳
     * @param int $endDate			结束日期		e.g.	时间戳
     */
    public function checkRepeatInvoice($params)
    {
    	extract($params);
    	global $paymentMappings;
    	$ret = array('create'=>true,'list'=>null);
    	if (!empty($feeType) && $childId)
    	{
    		switch ($feeType)
    		{
    			case FEETYPE_TUITION:
    			case FEETYPE_LUNCH:
    			case FEETYPE_CHILDCARE:
    			case FEETYPE_SCHOOLBUS:
    			case FEETYPE_SUBSIDY:
    			case FEETYPE_COLLECTION:
    			case FEETYPE_AFTERSCHOOL:
    				$criteria = new CDbCriteria();
    				$criteria->compare('t.childid', $childId);
    				$criteria->compare('t.schoolid', $this->schoolId);
    				$criteria->compare('t.calendar_id', $this->getYid());
    				$criteria->compare('t.payment_type', $paymentMappings['dbFeeType'][$feeType]);
    				$criteria->compare('t.status', '<>'.Invoice::STATS_CHANGE_ARCHIVED);
    				$criteria->compare('t.status', '<>'.Invoice::STATS_CANCELLED);
                    $criteria->addCondition('('.$startDate.'>=t.startdate and t.enddate>='.$startDate .') OR ' .
						'(' . $endDate.'>=t.startdate and t.enddate>='.$endDate .')');
                    //$criteria->addCondition($startDate.'>=t.startdate and t.enddate>='.$startDate );
                    //$criteria->addCondition($endDate.'>=t.startdate and t.enddate>='.$endDate, 'OR');
    				$model = Invoice::model()->findAll($criteria);
    				if (!empty($model))
    				{
    					foreach ($model as $val)
    					{
    						$ret['list'][$val->invoice_id] = $val->title;
    					}
    				}
    				return $ret;
    				break;
    			case FEETYPE_MATERIAL:
    			case FEETYPE_DEPOSIT:
    				$criteria = new CDbCriteria();
    				$criteria->select = 'invoice_id,title';
    				$criteria->compare('t.childid', $childId);
    				$criteria->compare('t.schoolid', $this->schoolId);
    				$criteria->compare('t.calendar_id', $this->getYid());
                    $criteria->compare('t.payment_type', $paymentMappings['dbFeeType'][$feeType]);
                    $criteria->addNotInCondition('status', array(Invoice::STATS_CHANGE_ARCHIVED, Invoice::STATS_CANCELLED));
    				$model = Invoice::model()->findAll($criteria);
    				if (!empty($model))
    				{
    					foreach ($model as $val)
    					{
    						$ret['list'][$val->invoice_id] = $val->title;
    					}
    				}
    				return $ret;
    				break;
    			case FEETYPE_LIBCARD:
    				$criteria = new CDbCriteria();
    				$criteria->compare('t.childid', $childId);
    				$criteria->compare('t.schoolid', $this->schoolId);
                    $criteria->compare('t.payment_type', $paymentMappings['dbFeeType'][$feeType]);
                    $criteria->addNotInCondition('status', array(Invoice::STATS_CHANGE_ARCHIVED, Invoice::STATS_CANCELLED));
    				$model = Invoice::model()->findAll($criteria);
    				if (!empty($model))
    				{
    					foreach ($model as $val)
    					{
    						$ret['list'][$val->invoice_id] = $val->title;
    					}
    				}
    				return $ret;
    				break;
    		}
    	}
    }
    
    /**
     * 学校多收费模式下，收费的标记
     * @param int $childid
     * @return string
     * <AUTHOR> Yu <<EMAIL>>
     */
    public function getSchoolFeeflag($childid){
        $feeByClass = IvyClass::getClassTypeForFee();
        $classid = $this->getClassId($childid);
        $model = IvyClass::model()->findByPk($classid);
        return $feeByClass[$model->classtype];
    }
}