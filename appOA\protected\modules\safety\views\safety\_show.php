<?php
$securityType = CommonUtils::LoadConfig('CfgSecurity');
$month = array();
for ($m = 1; $m < 13; $m++) {
    $month[sprintf("%02d", $m)] = sprintf("%02d", $m);
}

$year = array();
for ($y = 2016; $y <= date('Y', time()); $y++) {
    $year[$y] = $y;
}


$s = array();
if($safetyData){
    foreach ($safetyData as $k=>$item) {
        $s[$item['type']][$k] = $item['name'];
    }
}

?>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('//mcampus/default/index')) ?></li>
        <li><?php echo CHtml::link(Yii::t('site', 'Routines'), array('//mcampus/default/index')) ?></li>
        <li class="active"><?php echo Yii::t('site', '安全事故管理') ?></li>
    </ol>

    <div class="row">
        <div class="col-md-12 mb10">
            <?php
            $this->widget('zii.widgets.CMenu', array(
                'items' => $this->module->getMenu(),
                'id' => 'pageCategory',
                'htmlOptions' => array('class' => 'nav nav-tabs'),
                'activeCssClass' => 'active',
            ));
            ?>
        </div>
        <div class="col-md-12">
            <div class="row">
                <!-- 搜索框 -->
                <form class="" style="float: left;width: 100%" action="<?php echo $this->createUrl('index'); ?>"
                      method="get">
                    <?php echo Chtml::hiddenField('branchId', $this->branchId); ?>
                    <?php echo Chtml::hiddenField('type', $type); ?>
                    <div class="col-sm-2 form-group">
                        <?php echo CHtml::dropDownList('year', $yearData, $year, array('class'=> 'form-control', 'empty' => Yii::t('teaching', '年份'))) ?>
                    </div>
                    <div class="col-sm-2 form-group">
                        <?php echo CHtml::dropDownList('month', $monthData, $month, array('class'=> 'form-control', 'empty' => Yii::t('teaching', '月份'))) ?>
                    </div>
                    <div class="">
                        <div class="">
                            <button class="btn btn-default ml5" type="submit"><span
                                        class="glyphicon glyphicon-search"> </span></button>
                        </div>
                    </div>
                </form>
            </div>

            <?php if($month): ?>
            <div class="row">
                <div class="col-md-2 mb10">
                    <ul class="nav nav-pills nav-stacked background-gray" id="pageCategory">
                        <li <?php echo ($id == 9999999) ? "class='active'" : '' ; ?>>
                            <a href="<?php echo $this->createUrl('showCheck', array('year'=> $yearData, 'month' => $monthData, 'type' => $type)) ?>">
                                <?php echo Yii::t('safety','Monthly self-inspection table') ?>
                            </a>
                        </li>
                        <?php if($safetyData): ?>
                            <?php foreach($safetyData as $key=>$item): ?>
                                <li <?php echo ($id == $key) ? "class='active'" : '' ; ?>>
                                    <a href="<?php echo $this->createUrl('index',array('id' => $key,'year'=> $yearData, 'month' => $monthData, 'type' => $type, 'mid' => $item['mid'])) ?>">
                                  
                                    	<div class="media">							        
						             	<span class="pull-left label label-<?php echo $item['status'] == 1 ? 'success' : 'warning' ; ?> ">
                                            <?php echo $item['status'] == 1 ? '已提交' : '未提交' ; ?>
                                        </span>
							            <div class="media-body">
							                 <?php echo $item['name']; ?>
							            </div>
							        
							        </div>
                                    </a>
                                </li>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </ul>
                </div>
                <div class="col-md-10">
                    <div class="mb10">
                        <div class="well well-sm">
                        	<p><span><?php echo Yii::t('safety','school') ?>：<?php echo $school ?></span> <a href="<?php echo $this->createUrl('printMonthly', array('year'=> $yearData,  'month' => $monthData, 'type' => $type)) ?>"  class="btn btn-sm btn-info pull-right" target="_blank"><?php echo Yii::t('global', 'Print');?></a></p>
                            <span><?php echo Yii::t('safety','date') ?>：<?php echo date("Y-m-d", time()) ?></span>
                        
                               
                       
                        </div>
                        <div class="bs-example" data-example-id="simple-responsive-table">
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <thead>
                                    <tr>
                                        <th colspan="4" class="text-center">
                                            <?php echo Yii::t('safety','Summary of monthly self-inspection results') ?>
                                        </th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <tr>
                                        <td><?php echo Yii::t('safety','Inspection category') ?></td>
                                        <td><?php echo Yii::t('safety','Inspection item') ?></td>
                                        <td><?php echo Yii::t('safety','Inspection department/person') ?></td>
                                        <td><?php echo Yii::t('safety','Score') ?></td>
                                    </tr>
                                    <?php
                                    foreach ($securityType['type'] as $k=>$item):
                                        ?>
                                        <tr>
                                            <td  class="col-md-3" rowspan="<?php echo isset($s[$k]) ? count($s[$k]) + 1 : 2 ; ?>"><?php echo Yii::app()->language == 'zh_cn' ? $item['cn'] : $item['en'] ; ?></td>
                                        </tr>
                                        <?php if($s[$k]){?>
                                        <?php foreach($s[$k] as $key => $val): ?>
                                            <tr>
                                                <td><?php echo $val ?></td>
                                                <td>
                                                    <?php
                                                    if(isset($user) && isset($user[$key])){
                                                        foreach ($user[$key] as $userName) {
                                                            echo $userName . ' ';
                                                        }
                                                    }
                                                    ?>
                                                </td>
                                                <td><?php echo (isset($scoreArr) && isset($scoreArr[$key])) ? $scoreArr[$key] : 0 ; ?></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php }else{ ?>
                                        <tr>
                                            <td>无</td>
                                            <td></td>
                                            <td></td>
                                        </tr>
                                    <?php } ?>
                                    <?php  endforeach; ?>
                                    <tr>
                                        <td colspan="2"><?php echo Yii::t('safety','Overall score of self-inspection for safety in the month') ?></td>
                                        <td></td>
                                        <td></td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <tbody>
                                    <tr>
                                        <td rowspan="7" class="text-center" width="100"><?php echo Yii::t('safety','Monthly self-inspection table') ?></td>
                                    </tr>
                                    <tr>
                                        <td rowspan="7" class="text-center" width="100"><?php echo Yii::t('safety','Description of scoring:') ?></td>
                                    </tr>
                                    <tr>
                                        <td><?php echo Yii::t('safety','5 points  good state') ?></td>
                                    </tr>
                                    <tr>
                                        <td><?php echo Yii::t('safety','4 points  rectify within 2 weeks') ?></td>
                                    </tr>
                                    <tr>
                                        <td><?php echo Yii::t('safety','3 points   rectify within 1 week') ?></td>
                                    </tr>
                                    <tr>
                                        <td><?php echo Yii::t('safety','Below 3 points  rectify immediately') ?></td>
                                    </tr>
                                    <tr>
                                        <td><?php echo Yii::t('safety','N/A  the item is not applicable for scoring') ?></td>
                                    </tr>
                                    </tbody>
                                </table>
                                <table class="table">
                                    <tr>
                                        <td width="100"><input type="checkbox" id="check" ></td>
                                        <td><?php echo Yii::t('safety','Inspection item') ?></td>
                                    </tr>
                                    <?php  foreach($safetyData as $k=>$v):?>
                                        <tr>
                                            <td>
                                                <?php if($v['status'] == 1){ ?>
                                                    <label class="label label-success ">已提交</label>
                                                <?php  }else{ ?>
                                                    <input type="checkbox" id="<?php echo $k ?>"  class="checks" onclick="checkd()" name="check" value="<?php echo $v['mid']; ?>">
                                                <?php  } ?>
                                            </td>
                                            <td><?php echo $v['name']?></td>
                                        </tr>
                                    <?php  endforeach;?>
                                </table>
                                <input type="button" class="J_modal btn btn-primary" value="确认" onclick="saves()">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>
<script>

    $("#checkd").click(function(){
        var allCheckNum = $(".checks").length;
        var checkedNum = $(".checks:checked").length;
        if (allCheckNum == checkedNum) {
            $("#check").prop('checked',true);
        } else {
            $("#check").prop('checked',false);
        }
    });

    $("#check").click(function(){
        if($('#check').is(':checked')) {
            $(".checks").prop('checked',true);
        }else{
            $(".checks").prop('checked',false);
        }
    });

    function saves() {
        obj = document.getElementsByName("check");
        check_val = [];
        for(k in obj){
            if(obj[k].checked)
                check_val.push(obj[k].value);
        }

        if(check_val.length == 0){
            resultTip({error: 'warning', msg: '请选择'});
            return false;
        }

        $.ajax({
            type: "POST",
            url: "<?php echo $this->createUrl('review')?>",
            data: {check_val:check_val},
            dataType: "json",
            success: function(data){
                if(data.state == 'success'){
                    resultTip({msg: data.message, callback: function(){
                        reloadPage(window);
                    }});
                }else{
                    resultTip({error: 'warning', msg: data.message});
                }
            }
        });
    }
</script>