<?php
class LoginController extends Controller{
    public function __construct($id, $module = null) {
        parent::__construct($id, $module);
        $token = Yii::app()->request->getParam('token','');
        if ($token != md5(CommonUtils::SECURITY_KEY)){
            $this->module->_sendResponse(403);
        }
    }
    
    /**
     * 登陆验证
     */
    public function actionIndex(){
        $username = Yii::app()->request->getPost('username', null);
        $password = Yii::app()->request->getPost('password', null);
        if ($username ===null || $password===null){
            $result = array(
                'Message'=>'Sorry,参数错误',
                'Status'=>'Error'
            );
            $this->module->_sendResponse(200,CJSON::encode($result));
        }
        $criter = new CDbCriteria();
        $criter->compare('email', $username);
        $criter->compare('level', '>0');
        $criter->compare('isstaff', 1);
        $user = User::model()->with('profile')->find($criter);
        if($user->pass == md5($password)){
            $result = array(
                'Message'=>'登陆成功',
                'Status'=>'OK'
            );
            //判断权限
            if ($user->profile->branch == User::HQ_BRANCH_ID){
                $result['schoolid'] = 'BJ_OE';
            }else{
                $result['schoolid'] = $user->profile->branch;
            }
            $result['userAppid'] = md5($user->uid.$user->pass);
            $this->module->_sendResponse(200, CJSON::encode($result));
        }else{
            $result = array(
                'Message'=>'登陆的用户名或密码错误',
                'Status'=>'Error'
            );
            $this->module->_sendResponse(200, CJSON::encode($result));
        }
    }
}
