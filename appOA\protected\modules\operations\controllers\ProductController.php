<?php

class ProductController extends ProtectedController
{
	/**
	 * @var string the default layout for the views. Defaults to '//layouts/column2', meaning
	 * using two-column layout. See 'protected/views/layouts/column2.php'.
	 */
	public $layout='//layouts/column2';
    
    public function init() {
        parent::init();
        Yii::import('common.models.points.*');
        
        $this->mainMenu = array(
			array(
				'label' => '订单管理',
				'url' => array('/operations/order/admin')
			),
			array(
				'label' => '商品管理',
				'url' => array('/operations/product/admin')
			),
		);
		
    }
	
	public function beforeAction($action){
		if($action->getId() != 'nopermit'){
			if(!Yii::app()->user->checkAccess('tHQOperations')){
				$this->forward('nopermit');
			}
		}
		return true;
	}
	
	public function actionNopermit(){
		$this->render('nopermit');
	}

    /**
	 * Updates a particular model.
	 * If update is successful, the browser will be redirected to the 'view' page.
	 * @param integer $id the ID of the model to be updated
	 */
	public function actionUpdate($id=0)
	{
		$model=$this->loadModel($id);

		// Uncomment the following line if AJAX validation is needed
		// $this->performAjaxValidation($model);

		if(isset($_POST['PointsProduct']))
		{
			$model->attributes=$_POST['PointsProduct'];
            $model->userid =  Yii::app()->user->id;
            $model->updated =  time();
			if($model->save()){
                $this->addMessage('state', 'success');
                $this->addMessage('refresh', true);
                $this->addMessage('referer', $this->createUrl('admin'));
                $this->addMessage('message', Yii::t('message','Data saved!'));
            }else {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('message','Saving Failed!'));
            }
            $this->showMessage();
		}

		$this->render('update',array(
			'model'=>$model,
		));
	}

	/**
	 * Deletes a particular model.
	 * If deletion is successful, the browser will be redirected to the 'index' page.
	 * @param integer $id the ID of the model to be deleted
	 */
	public function actionDelete($id)
	{
		if(Yii::app()->request->isPostRequest)
		{
			// we only allow deletion via POST request
			$order = PointsOrder::model()->exists('productid=:productid',array(':productid'=>$id));
			$stock = PointsStock::model()->exists('product_id=:product_id',array(':product_id'=>$id));
			if ($stock || $order)
			{
				echo Yii::t('points', '已经增加库存或已生成订单，不能删除！');
			}
			else
			{
				$this->loadModel($id)->delete();
				// if AJAX request (triggered by deletion via admin grid view), we should not redirect the browser
				if(!isset($_GET['ajax']))
				{
					$this->redirect(isset($_POST['returnUrl']) ? $_POST['returnUrl'] : array('admin'));
				}
			}

		}
		else
			throw new CHttpException(400,'Invalid request. Please do not repeat this request again.');
	}

	/**
	 * Manages all models.
	 */
	public function actionAdmin()
	{
		$model=new PointsProduct('search');
		$model->unsetAttributes();  // clear any default values
		if(isset($_GET['PointsProduct']))
			$model->attributes=$_GET['PointsProduct'];

		$this->render('admin',array(
			'model'=>$model,
		));
	}

	/**
	 * Returns the data model based on the primary key given in the GET variable.
	 * If the data model is not found, an HTTP exception will be raised.
	 * @param integer the ID of the model to be loaded
	 */
	public function loadModel($id)
	{
		$model=PointsProduct::model()->findByPk((int)$id);
		if($model===null)
			$model = new PointsProduct;
		return $model;
	}

	/**
	 * Performs the AJAX validation.
	 * @param CModel the model to be validated
	 */
	protected function performAjaxValidation($model)
	{
		if(isset($_POST['ajax']) && $_POST['ajax']==='points-product-form')
		{
			echo CActiveForm::validate($model);
			Yii::app()->end();
		}
	}
	
	public function actionGallery($id = 0)
	{
		$cfgs = OA::LoadConfig('CfgPhoto');
        $cfgsModules = $cfgs['points'];
		$imageModel = PointsImages::model()->getProductImages($id);
		$model = PointsProduct::model()->findByPk($id);
//		$subdir = $cfgsModules['filePrefix'] . $id;
		$thumbdir = $cfgsModules['sizes']['thumbW'] ? 'thumb/' : '';
		$imageUrl = $cfgsModules['uploadUrl'] . $thumbdir;
		$this->render('gallery',array('productid'=>$id,'imageUrl'=>$imageUrl,'imageModel'=>$imageModel,'model'=>$model));
	}
	
	public function actionUpload($productid = 0)
	{
		if ($productid)
		{
			// HTTP headers for no cache etc
        	header('Content-type: text/plain; charset=UTF-8');
            header("Expires: Mon, 26 Jul 1997 05:00:00 GMT");
            header("Last-Modified: " . gmdate("D, d M Y H:i:s") . " GMT");
            header("Cache-Control: no-store, no-cache, must-revalidate");
            header("Cache-Control: post-check=0, pre-check=0", false);
            header("Pragma: no-cache");

            // Settings
            $cfgs = OA::LoadConfig('CfgPhoto');
            $cfgsModules = $cfgs['points'];
//            $subdir = $cfgsModules['filePrefix'] . $productid;
//            $targetDir = $cfgsModules['subDir'] . $subdir;
            $targetDir = $cfgsModules['subDir'];
            $cleanupTargetDir = false; // Remove old files
            $maxFileAge = 60 * 60; // Temp file age in seconds
            // 5 minutes execution time
            @set_time_limit(5 * 60);
            // usleep(5000);
            // Get parameters
            $chunk = isset($_REQUEST["chunk"]) ? $_REQUEST["chunk"] : 0;
            $chunks = isset($_REQUEST["chunks"]) ? $_REQUEST["chunks"] : 0;
            $fileName = isset($_REQUEST["name"]) ? $_REQUEST["name"] : '';
            $suffix = strtolower(substr($fileName, strrpos($fileName, '.')));
            // Clean the fileName for security reasons
            $fileName = preg_replace('/[^\w\._\s]+/', '', $fileName);
            // Create target dir
            if (!file_exists($targetDir))
            {
            	 OA::autoMkDirs($targetDir);
            }
            // Remove old temp files
            if (is_dir($targetDir) && ($dir = opendir($targetDir))) {
                while (($file = readdir($dir)) !== false) {
                    $filePath = $targetDir . DIRECTORY_SEPARATOR . $file;

                    // Remove temp files if they are older than the max age
                    if (preg_match('/\\.tmp$/', $file) && (filemtime($filePath) < time() - $maxFileAge))
                        @unlink($filePath);
                }

                closedir($dir);
            } else
                throw new CHttpException(500, Yii::t('points', "Can't open temporary directory."));

            // Look for the content type header
            if (isset($_SERVER["HTTP_CONTENT_TYPE"]))
                $contentType = $_SERVER["HTTP_CONTENT_TYPE"];

            if (isset($_SERVER["CONTENT_TYPE"]))
                $contentType = $_SERVER["CONTENT_TYPE"];

            if (strpos($contentType, "multipart") !== false) {
                if (isset($_FILES['file']['tmp_name']) && is_uploaded_file($_FILES['file']['tmp_name'])) {
                    // Open temp file
                    $out = fopen($targetDir . DIRECTORY_SEPARATOR . $fileName, $chunk == 0 ? "wb" : "ab");
                    if ($out) {
                        // Read binary input stream and append it to temp file
                        $in = fopen($_FILES['file']['tmp_name'], "rb");

                        if ($in) {
                            while ($buff = fread($in, 4096))
                                fwrite($out, $buff);
                        } else
                            throw new CHttpException(500, Yii::t('points', "Can't open input stream."));

                        fclose($out);
                        unlink($_FILES['file']['tmp_name']);
                    } else
                        throw new CHttpException(500, Yii::t('points', "Can't open output stream."));
                } else
                    throw new CHttpException(500, Yii::t('points', "Can't move uploaded file."));
            } else {
                // Open temp file
                $out = fopen($targetDir . DIRECTORY_SEPARATOR . $fileName, $chunk == 0 ? "wb" : "ab");
                if ($out) {
                    // Read binary input stream and append it to temp file
                    $in = fopen("php://input", "rb");

                    if ($in) {
                        while ($buff = fread($in, 4096))
                            fwrite($out, $buff);
                    } else
                        throw new CHttpException(500, Yii::t('points', "Can't open input stream."));

                    fclose($out);
                } else
                    throw new CHttpException(500, Yii::t('points', "Can't open output stream."));
            }

            // After last chunk is received, process the file
            $ret = array('result' => '1');
            if (intval($chunk) + 1 >= intval($chunks)) {

                $originalname = $fileName;
                if (isset($_SERVER['HTTP_CONTENT_DISPOSITION'])) {
                    $arr = array();
                    preg_match('@^attachment; filename="([^"]+)"@', $_SERVER['HTTP_CONTENT_DISPOSITION'], $arr);
                    if (isset($arr[1]))
                        $originalname = $arr[1];
                }

                // **********************************************************************************************
                // Do whatever you need with the uploaded file, which has $originalname as the original file name
                // and is located at $targetDir . DIRECTORY_SEPARATOR . $fileName
                // **********************************************************************************************



                $oldname = $targetDir . DIRECTORY_SEPARATOR . $fileName;
                $fileName = uniqid() . $suffix;
                @rename($oldname, $targetDir . DIRECTORY_SEPARATOR . $fileName);

                $image = Yii::app()->image->load($targetDir . DIRECTORY_SEPARATOR . $fileName);
                $image->quality(100);
                $image->resize($cfgsModules['sizes']['normalW'], $cfgsModules['sizes']['normalH']);
                $image->save();
                
                if ($cfgsModules['sizes']['thumbW']) {
                    $image = Yii::app()->image->load($targetDir . DIRECTORY_SEPARATOR . $fileName);
                    $subtargetDir = $targetDir . DIRECTORY_SEPARATOR . 'thumb';
                    if (!file_exists($subtargetDir))
                    {
                    	OA::autoMkDirs($subtargetDir);
                    }
                    $image->resize($cfgsModules['sizes']['thumbW']);
                    $image->save($subtargetDir . DIRECTORY_SEPARATOR . $fileName);
                }
				
                $picmodel = new PointsImages;
                $picmodel->productid = $productid;
                $picmodel->image = $fileName;
                if ($picmodel->save()) {
                    $thumbdir = $cfgsModules['sizes']['thumbW'] ? 'thumb/' : '';
                    $photourl = $cfgsModules['uploadUrl'] . $thumbdir;
                    $ret['pic'] = $photourl . $picmodel->image;
                    $ret['id'] = $picmodel->id;
                } else {
                    $ret['result'] = 0;
                }
            }

            // Return response
            die(json_encode($ret));
		}
	}
	
	
	public function actionDelpic()
	{
		$id = Yii::app()->request->getPost('id',0);
		$model = PointsImages::model()->findByPk($id);
		if ($model->delete())
		{
            $criteria = new CDbCriteria;
            $criteria->compare('cover', $id);
            PointsProduct::model()->updateAll(array('cover'=>0), $criteria);
			$cfgs = OA::LoadConfig('CfgPhoto');
            $cfgsModules = $cfgs['points'];
            @unlink($cfgsModules['subDir'] . '/thumb/' . $model->image);
            @unlink($cfgsModules['subDir'] . $model->image);
            echo CJSON::encode(
                    array(
                        'status' => 'success',
                    )
            );
		}
		else
		{
			echo CJSON::encode(
                    array(
                        'status' => 'failure',
                        'message' => Yii::t("points", '删除失败'),
                    )
            );
		}
	}
	
	
	public function actionCover()
	{
		$id = Yii::app()->request->getPost('id', 0);
		$model = PointsImages::model()->findByPk($id);

		if (PointsProduct::model()->updateByPk($model->productid, array('cover' => $id))) {
			echo CJSON::encode(
			array(
                        'status' => 'success',
                        'message' => Yii::t("bview", '设置成功'),
			)
			);
		} else {
			echo CJSON::encode(
			array(
                        'status' => 'failure',
                        'message' => Yii::t("bview", '设置失败'),
			)
			);
		}
	}
	
	
	public function actionPic($id=0)
	{
		if ($id)
		{
			$model = PointsImages::model()->findByPk($id);
			$cfgs = OA::LoadConfig('CfgPhoto');
            $cfgsModules = $cfgs['points'];
//            $subdir = $cfgsModules['filePrefix'] . $model->productid;
            $thumbdir = $cfgsModules['sizes']['thumbW'] ? 'thumb/' : '';
			$imageUrl = $cfgsModules['uploadUrl'] . $thumbdir;
			if (isset($_POST['PointsImages']))
			{
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('message','Saving Failed!'));
				$model->attributes = $_POST['PointsImages'];
				if ($model->save()) {
				 	$this->addMessage('state', 'success');
                    $this->addMessage('message', Yii::t('message','Data saved!'));
				}
                $this->showMessage();
			}
            $this->layout = '//layouts/dialog';
			$this->render('pic', array('model' => $model, 'imageUrl' => $imageUrl));
		}
	}
	
}
