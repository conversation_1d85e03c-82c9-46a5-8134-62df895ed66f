<div>
    <div class='flex align-items  mb24 mt10'>
        <span class='font14 color3'>处理人：</span>
        <div class='flex1 flex align-items lib_class_handle'>
        </div>
    </div>
    <p class='mt5'>
        <span class='notAllowed'>
            <label class="radio-inline font14 color6 lableHeight eventsNone">
                <input type="radio"   name="balance" value="1"> 有学籍
            </label>
        </span>
        <span class='notAllowed'>
            <label class="radio-inline font14 color6 lableHeight eventsNone ml24">
                <input type="radio"  name="balance" value="0"> 无学籍
            </label>
        </span>
    </p>
    <p class='xueji hide'>
        <span class='font14 color3'> 学籍状态：</span>
        <span class='xuejiStatus'>
        </span>
    </p>
    <textarea class="form-control" rows="3" disabled placeholder="<?php echo Yii::t('withdrawal','Comment');?>" name="remark" id='remark'  class='font14'></textarea>
    <p class='mb10 mt20'>
        <span class='font14 color3 mr16' >展示在家长端确认页面</span> 
        <label class="switch">
            <input type="checkbox" name="show_prompt" id='show_prompt' disabled />
            <span class="slider"></span>
        </label>
        <div class='prompt hide'>
            <textarea class="form-control" rows="2" name='prompt'  disabled id='prompt' placeholder="<?php echo Yii::t('leave','Input');?>"  class='font14'></textarea>
        </div>
        <div class='text-right font14 color3'><span class='submit'></span>  <span class='cur-p ml10 redColor canRevoke' onclick='cancelSubmit()'>撤销</span></div>
    </p>
</div>

<script>
   
     lib()
    $(function () {
        $('[data-toggle="tooltip"]').tooltip()
    })
     function lib(){
        let forms=childDetails.forms.find(item2 =>item2.key === 'enrollment')
        for(var key in forms.owner_info){
            if (key !== '2' && key !== '5') {
                if(forms.implementer==key && forms.data._id){
                    $('.lib_class_handle').append('<div class="flex align-items mr16 relative"><img src='+forms.owner_info[key].photoUrl+' alt="" class="avatar38"/><div class="ml8"><div class="font14 color3 ">'+forms.owner_info[key].name+'</div><div class="font12 color6">处理时间：'+forms.implementAt+'</div></div></div>')
                }else if(!forms.data._id){
                    $('.lib_class_handle').append('<div class="flex align-items mr16"><img src='+forms.owner_info[key].photoUrl+' alt="" class="img28"/><div class="font14 color3 ml8">'+forms.owner_info[key].name+'</div></div>')
                }
            }
        }
        if(forms.status=='1'){
            $('input[type="checkbox"][name="status"][value='+forms.status+']').prop('checked', true);
        }
        if(forms.data.balance){
            $('input[type="radio"][name="balance"][value='+forms.data.balance+']').prop('checked', true);
        }
        if(forms.data.remark!=''){
            $('#remark').val(forms.data.remark);
        }
        if(forms.data.prompt!=''){
            $('#prompt').val(forms.data.prompt);
        }
        if(forms.data.show_prompt=='1'){
            $('.prompt').removeClass('hide')
            $('input[name="show_prompt"]').prop('checked', true);
        }else{
            $('input[name="show_prompt"]').prop('checked', false);
            $('.prompt').addClass('hide')
        }
        if(forms.data.balance=='1'){
            $('.xuejiStatus').html('')
            for(let i=0;i<container.indexDataList.enrollmentStateList.length;i++){
                $('.xuejiStatus').append('<span class="notAllowed"><label class="radio-inline font14 color6 lableHeight mr20 eventsNone"><input type="radio" name="enrollment_state" value='+container.indexDataList.enrollmentStateList[i].key+'> '+container.indexDataList.enrollmentStateList[i].value+'</label></span>')
            }
            $('input[type="radio"][name="enrollment_state"][value='+forms.data.enrollment_state+']').prop('checked', true);
            $('.xueji').removeClass('hide')
        }
        if(forms.canRevoke){
            $('.canRevoke').show()
            $('.submit').html('由 '+childDetails.staffInfo[forms.implementer].name+' <?php echo Yii::t('global','Submit');?>')
        }else{
            $('.canRevoke').hide()
            $('.submit').html()
        }
     }
     function cancelSubmit(){
        container.cancelSubmitData('enrollment')
    }
</script>
<style> 
/* The switch - the box surrounding the slider */
.switch {
    position: relative;
    display: inline-block;
    width: 40px;
    height: 20px;
    border-radius: 50px;
}
 
/* Hide default HTML checkbox */
.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}
 
/* The slider */
.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  -webkit-transition: .4s;
  transition: .4s;
  border-radius:60px
}
 
.slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left:2px;
    bottom: 2px;
    background-color: white;
    -webkit-transition: .4s;
    transition: .4s;
    border-radius: 50%;
}
 
input:checked + .slider {
  background-color: #428bca ;
}
 
input:checked + .slider:before {
  -webkit-transform: translateX(19px);
  -ms-transform: translateX(19px);
  transform: translateX(19px);
}
 
/* Rounded sliders */
.slider.round {
  border-radius: 34px;
}
 
.slider.round:before {
  border-radius: 50%;
}
.xueji{
    padding:8px 10px;
    background:#F7F7F8;
    border-radius:4px;
    margin-bottom:16px
}
</style>
