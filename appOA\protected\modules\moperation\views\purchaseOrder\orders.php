<div class="container-fluid">
	 <ol class="breadcrumb">
	     <li><?php echo CHtml::link(Yii::t('site','HQ Operations'), array('default/index'));?></li>
	     <li><?php echo CHtml::link(Yii::t('site','Support'), array('default/index'));?></li>
	     <li class="active"><?php echo Yii::t('site','订单管理') ;?></li>
	 </ol>
	 <div class="row">
	    <div class="col-md-2">
	        <div class="list-group" id="classroom-status-list">
	            <a href="<?php echo $this->createUrl('purchaseProducts/index');?>" class="list-group-item status-filter ">商品管理</a>
	            <a href="<?php echo $this->createUrl('purchaseStandard/index');?>" class="list-group-item status-filter">标配管理</a>
	            <a href="<?php echo $this->createUrl('purchaseOrder/index');?>" class="list-group-item status-filter">生成订单</a>
	            <a href="<?php echo $this->createUrl('purchaseOrder/index');?>" class="list-group-item status-filter active">订单管理</a>
	        </div>
	    </div>
	    <div class="col-md-10">
	        <div class="panel panel-default">
	            <div class="panel-body">
					<?php 
						$this->widget('ext.ivyCGridView.BsCGridView',array(
							'id'=>'purchase-order',
		                    'afterAjaxUpdate'=>'js:function(){head.Util.ajaxDel()}',
							'dataProvider'=>$orders,
							'colgroups'=>array(
							    array(
							        "colwidth"=>array(150,100,150,100,100,100),
							    )
							),
							'columns'=>array(
								array(
									'name'=>'订单标题',
									'value'=>'$data->title',
									),
								array(
									'name'=>'采购单编号',
									'value'=>'$data->pid',
									),
								array(
									'name'=>'相关申请',
									'value'=>'$data->application->title',
									),
								array(
									'name'=>'操作人',
									'value'=>'User::model()->findByPk($data->add_user)->getName()',
									),
								array(
									'name'=>'添加时间',
									'value'=>'date("Y-m-d", $data->add_timestamp)',
									),
								array(
									'name'=>'操作',
									'type'=>'raw',
									'value'=>'CHtml::Link("查看","",array(
										"class"=>"btn btn-xs btn-info","onclick"=>"getProducts($data->id)"))
										." ".CHtml::Link("编辑","",array("class"=>"btn btn-xs btn-info","onclick"=>"editOrder($data->id)"))
										." ".CHtml::Link("删除",Yii::app()->createUrl("moperation/purchaseOrder/delete",array("id"=>$data->id)),array("class"=>"J_ajax_del btn btn-xs btn-danger"))',
									),
							)
						));
					 ?>
	            </div>
	        </div>
	    </div>
	</div>
</div>
<!-- 模态框 -->
<?php $showId = Yii::app()->request->getParam('showId', ''); ?> 
<div class="modal fade" id="modal" tabindex="-1" role="dialog" aria-labelledby="modal">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">

	        <div class="modal-footer">
	        	<button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global','Submit');?></button>
	        	<button type="button" class="btn btn-default"  data-dismiss="modal"><?php echo Yii::t('global','Cancel');?></button>
	        </div>
        </div>
    </div>
</div>

<script>
	// 查看订单
	var showId='<?php echo Yii::app()->request->getParam('showId', '') ?>' ;
	if(showId!=''){
		getProducts (showId) 
	}
	
	function getProducts (id) {
	    $('#modal .modal-content').load('<?php echo $this->createUrl('getOrder') ?>'+'?id='+id,function () {
	        $('#modal').modal({
	          show: true,
	          backdrop: 'static'
	        });
	        head.Util.ajaxForm( $('#modal') );
	    });
	}
	// 编辑订单	
	function editOrder (id) {
	    $('#modal .modal-content').load('<?php echo $this->createUrl('edit') ?>'+'?id='+id,function () {
	        $('#modal').modal({
	          show: true,
	          backdrop: 'static'
	        });
	        head.Util.ajaxForm( $('#modal') );
	    });
	}
	// 回调
	function cbOrder() {
	    $('#modal').modal('hide');
	    $.fn.yiiGridView.update('purchase-order');
	}
</script>