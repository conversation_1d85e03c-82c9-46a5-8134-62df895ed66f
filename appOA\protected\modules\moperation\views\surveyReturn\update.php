<?php $form = $this->beginWidget('CActiveForm', array(
    'id' => 'visits-form',
    'enableAjaxValidation' => false,
    'htmlOptions' => array('class' => 'J_ajaxForm form-horizontal', 'role' => 'form'),
    'action' => $this->createUrl('updateback', array('id' => $model->id)),
));
?>
<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">×</span>
    </button>
    <h4 class="modal-title">增加</h4>
</div>
<div class="modal-body">
    <div  class="form-group">
            <!-- 英文名 -->
        <div class="form-group">
            <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'startyear'); ?></label>
            <div class="col-xs-8">
                <?php echo $form->dropDownList($model, 'startyear', $startyearList, array('class' => 'form-control')); ?>
            </div>
        </div>
        <div class="form-group">
            <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'title_cn'); ?></label>
            <div class="col-xs-8">
                <?php echo $form->textField($model, 'title_cn', array('maxlength' => 255, 'class' => 'form-control')); ?>
            </div>
        </div>
        <div class="form-group">
            <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'title_en'); ?></label>
            <div class="col-xs-8">
                <?php echo $form->textField($model, 'title_en', array('maxlength' => 255, 'class' => 'form-control')); ?>
            </div>
        </div>
        <div class="form-group">
            <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'status'); ?></label>
            <div class="col-xs-8">
                <?php echo $form->dropDownList($model, 'status', array(0 => '无效',1 =>'有效'), array('class' => 'form-control')); ?>
            </div>
        </div>
    </div>
</div>
<div class="modal-footer">
    <button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global', 'Submit'); ?></button>
    <button type="button" class="btn btn-default"
            data-dismiss="modal"><?php echo Yii::t('global', 'Cancel'); ?></button>
</div>

<?php $this->endWidget(); ?>
