<?php $this->subPageTitle = Yii::t('wechat', 'My Children'); ?>
<div class="container">
    <div class="px-2 text-center fixed-top text-white header" style="background: linear-gradient(to right, #007bff, #1eaaf1)">
        <small><?php echo Yii::t("reg", 'Click on the "Child" to start the registration process'); ?></small></div>
    <div class="mt-5">
        <h6 class"text-secondary"><small><span class="fas fa-user-circle"></span> <?php echo Yii::t("user", 'Children'); ?></small></h6>
        <?php
        $flag = false;
        $schoolids = array();
        $statusConfig = RegStudents::getStudentConfig();
        foreach ($childObjs as $childObj){
            if ($regStudent = RegStudents::getActive(null, $childObj->childid)){
                $flag = true;
                $branchInfo = Branch::model()->getBranchInfo($regStudent->schoolid);

                $schoolids[$regStudent->schoolid]['title'] = $branchInfo->title;
                $schoolids[$regStudent->schoolid]['status'] = 1;

                $status = $statusConfig[$regStudent->status];
                $btnTxt = Yii::t("reg", "Start");
                if($regStudent->status == RegStudents::STATUS_AUDITED) {
                    $btnTxt =Yii::t("reg", "View");
                }
                if($regStudent->start_time > time() || $regStudent->end_time < time()){
                    $schoolids[$regStudent->schoolid]['status'] = 0;
                    $btnTxt = Yii::t("reg", "View");
                }
            ?>
        <div class="media mt-3 p-2 rounded-lg shadow-sm">
            <img src="<?php echo CommonUtils::childPhotoUrl($childObj->photo, 'small');?>" class="shadow-sm mr-3 child-head" alt="<?php echo $childObj->getChildName(); ?>">
            <div class="media-body position-relative">
                <h6 class="mt-0"><?php echo $childObj->getChildName(); ?></h6>
                <div class="box-info">
                    <small><?php echo Yii::t('wechat', 'Birthday: ') . date('Y/m/d', $childObj->birthday); ?></small><br>
                    <small><?php echo Yii::t('reg', 'School: ') . $branchInfo->title;?></small><br>
                    <small><?php echo Yii::t('reg', 'Period for registration: ') . date('m/d', $regStudent->start_time).' - '.date('m/d', $regStudent->end_time); ?></small><br>
                    <small><?php echo Yii::t('reg', 'Status: ') . $status; ?></small>
                </div>
                <a class="btn btn-primary float-right position-absolute" style="top: 0.25rem; right: 0.5rem;" href="<?php $action = $this->getSteps(1, 'first', $regStudent->schoolid); echo $this->createUrl('regExtraInfo/' . $action , array('childid' => $childObj->childid)); ?>" ><?php echo $btnTxt;?></a>
            </div> 
        </div>
        <?php }};?>
    </div>

    <?php 
        $stepConfig = RegExtraInfo::getStepConfig();
        foreach ($schoolids as $s=>$item) : 
            $steps = RegExtraInfo::getStepsBySchool($s);
        ?>
    <div class="mt-3 mb-3">
        <ul class="list-group steps" style="font-size: 12px;">
            <li class="list-group-item">
                <h6><small><?php echo $item['title']; ?> <?php echo Yii::t("reg", 'Registration steps'); ?></small></h6></li>
        <?php foreach ($steps as $k => $step) : ?>
            <li class="list-group-item">
                <span class="shadow-sm step-no align-middle rounded-circle"><?php echo $k+1; ?></span><?php echo $stepConfig[$step]; ?></li>
        <?php endforeach; ?>
        </ul>
    </div>
    <?php endforeach; ?>
    <?php if ($flag === false) : ?>
        <p class="text-center text-primary"><span class="fas fa-info-circle fa-5x"></span></p>
        <p class="text-center text-primary"><?php echo Yii::t("reg", 'You do not have a student who needs to register'); ?><br><small class="text-secondary text-center"><?php echo Yii::t("reg", 'If you have any questions, please contact the school'); ?></small></p>
    <?php endif ?>
</div>
<style>
    <style>
    .header {
        height: 2rem;
    }
    img.child-head {
        width: 60px;
        height: 60px;
        border-radius: 30px;
    }
    .steps li.list-group-item {
        border: 0;
        padding: 0.5rem 1rem;
        background-color:#eef3f6
    }
    .steps li.list-group-item span {
        display: inline-block;
        width: 30px;
        height: 30px;
        color: #fff;
        background: linear-gradient(to right, #007bff, #1eaaf1);
        margin-right: 0.5em;
        text-align: center;
        line-height: 30px;
    }
    .box-info {
        line-height: 1.5;
    }
    .media {
        background-color: #f8f8f8;
    }
</style>
