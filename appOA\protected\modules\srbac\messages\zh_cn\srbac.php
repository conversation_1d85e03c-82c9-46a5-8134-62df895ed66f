<?php
/**
 * 中文翻译版本的 srbac.演示站，无权进入srbac--正在开发中
 * 需要国外代理访问，大家可以找spyros获取帮助:http://sys2009.200u.com/Belief
 * 网址区分大小写
 * <AUTHOR>
 * @link http://sys2009.200u.com/Belief
 */

/**
 * @如果你在中共大陆，那么您需要使用国外代理访问本站，虽然此免费空间很快，可是被中共屏蔽  
 * @()
 * @since 1.0.2
 */

return array (
  "Fields with"=>"有",
  "are required"=>"号的项目必填",
  "Name"=>"姓名",
  "Itemname"=>"项目",
  "Type"=>"类型",
  "Description"=>"描述",
  "Bizrule"=>"商务规则",
  "Data"=>"数据",
  "Save"=>"保存",
  "Create"=>"添加",
  "Managing AuthItem"=>"管理授权项",
  "AuthItem List" => "授权项列表",
  "New AuthItem" => "新建授权项",
  "Update"=>"更新",
  "Delete"=>"删除",
  "Users"=>"用户",
  "Roles"=>"角色",
  "Tasks"=>"任务",
  "Manage AuthItem"=>"管理授权项",
  "Roles member"=>"角色成员",
  "Update AuthItem"=>"更新授权项",
  "Delete AuthItem"=>"删除授权项",
  "View AuthItem"=>"浏览授权项",
  "Assign Operations to Tasks"=>"分配各个操作给各个任务",
  "Task"=>"任务",
  "Assign Roles to Users"=>"分配各角色给各用户",
  "User"=>"用户",
  "Assign Tasks to Roles"=>"分配各任务给各角色",
  "Role"=>"角色",
  "Assigned Tasks"=>"已分配任务",
  "Not Assigned Tasks"=>"尚未分配任务",
  "Assigned Operations"=>"已分配操作",
  "Not Assigned Operations"=>"尚未分配操作",
  "Assigned Roles"=>"已经分配角色",
  "Not Assigned Roles"=>"尚未分配角色",
  "Install Srbac"=>"安装 Srbac",
  "Your Database, AuthManager and srbac settings:"=>"你的数据库，授权管理器和srbac设置",
  "Database"=>"数据库",
  "Driver"=>"驱动",
  "Connection"=>"连接",
  "Item Table"=>"项目表",
  "Assignment Table"=>"分配表",
  "Item child table"=>"项目级别表",
  "User id"=>"用户ID",
  "User name"=>"用户名",
  "User class"=>"用户类型",
  "Install"=>"安装",
  "AuthManager"=>"授权管理器",
  "srbac"=>"srbac",
  "Database is not Configured"=>"数据库没有配置",
  "AuthManager is not Configured"=>"授权管理器没有配置",
  "srbac is not Configured"=>"srbac 没有配置",
  "Srbac is already Installed.<br />Overwrite it?<br />"=>"Srbac已经安装.<br />要重复安装覆盖原有文件吗?<br />",
  "Overwrite"=>"覆盖",
  "Srbac installed successfuly"=>"Srbac 已经安装成功",
  "Error while installing srbac.<br />Please check your database and try again"=>"安装 srbac期间发生错误.<br />请检查你的数据库后重试",
  "Create demo authItems?"=>"建立演示Demo授权项目吗?",
  "Create New Item"=>"建立新项目",
  "Really delete"=>"真的要删除吗",

  // New Version 1.02
  "Autocreate Auth Items"=>"自动建立授权项",
  "Auth items" =>"授权项",
  "Actions"=>"动作Actions",
  "Search"=>"搜索",
  "All"=>"全部",
  "Yes"=>"是",
  "Updating list"=>"更新列表",
  "created successfully"=>"添加成功",
  "updated successfully"=>"更新成功",
  "Deleted successfully"=>"删除成功",
  "Error while updating"=>"更新时发生错误",
  "Error while creating"=>"添加时发生错误",
  "Error while deleting"=>"删除时发生错误",
  "Possible there's already an item with the same name"=>"可能已经有名字相同的项目存在了",
  "Role(s) Assigned" =>"已分配角色",
  "Role(s) Revoked"=>"已撤销角色",
  "Task(s) Assigned" =>"已分配任务",
  "Task(s) Revoked"=>"已撤销任务",
  "Operation(s) Assigned"=>"已分配操作",
  "Operation(s) Revoked"=>"已撤销操作",
  "Operations"=>"操作",
  "Controller"=>"控制器",
  "Action"=>"动作",
  "Scan"=>"扫描",
  "Scanning for Auth Items for controller"=>"扫描控制器授权项",
  "Automatic creation of Auth Items for controller"=>"自动建立控制器授权项",
  "Check All"=>"检查全部",
  "Create tasks"=>"添加任务",
  "Creating tasks"=>"正在添加任务",
  "Creating operations"=>"正在添加操作",
  "You are not authorized for this action"=>"你无权运行",
  "Error while trying to access"=>"访问发生错误",
  "Delete All Auth Items of controller"=>"删除控制器下全部授权项",
  "Delete Tasks"=>"删除任务",
  "deleted"=>"已删除",
  "Delete operations"=>"删除操作",
  "Delete tasks"=>"删除任务",
  "Can't revoke this role"=>"不能撤销此角色",
  "Managing auth items"=>"管理授权项",
  "Assign to users"=>"分配授权项",
  "User's assignments"=>"用户已经获授权",
  "Srbac frontpage"=>"srbac首页",

  //Version 1.0.3
  "Module"=>"模块",
  "Pages that access is always allowed"=>"永远允许访问的页面",
  "srbac must be in debug mode" =>"Srbac 必须处于debug调试模式",
  "Yii version"=>"Yii版本",
  "Wrong Yii version, lower required version is"=>"Yii版本错误, 最低版本要求",
  "There is an error in your configuration"=>"你的配置当中有错误",

  //Version 1.1
  "Clever Assigning"=>"智能分配",
  "Help"=>"帮助",
  "Edit always allowed list"=>"编辑总是允许列表",
  "The following authItems are saved in the always allowed file" =>
  "以下授权列表保存在总是允许的文件allow.php中",
  "srbac is not configuered to use the alwaysAllowed GUI editor."=>
  "没有把Srbac配置为使用总是允许的图形界面GUI编辑器 ",
  "Remove alwaysAllowed attribute from srbac configuration or set it to 'gui'."=>
  "从srbac配置中去掉 alwaysAllowed属性或者设置其为'gui'."
);
?>
