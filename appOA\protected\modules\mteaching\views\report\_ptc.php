<?php $form=$this->beginWidget('CActiveForm', array(
    'id'=>'visits-form',
    'enableAjaxValidation'=>false,
    'htmlOptions'=>array('class'=>'J_ajaxForm form-horizontal', 'role'=>'form'),
));
?>
<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">×</span></button>
    <h4 class="modal-title"><?php echo $modalTitle;?></h4>
</div>
<div class="panel-body">
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo "课程"; ?></label>

        <div class="col-xs-9">
            <?php echo CHtml::textField('', $model->reportCourse()->getName(), array('class' => 'form-control', 'disabled'=>'disabled')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo "老师" ?></label>
        <div class="col-xs-9">
            <?php echo $form->dropDownList($model,'teacherid',$user_name, array('class'=>'form-control')); ?>
        </div>
    </div>
</div>
<div class="modal-footer">
    <button type="submit"
            class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global', 'Submit'); ?></button>
    <button type="button" class="btn btn-default"
            data-dismiss="modal"><?php echo Yii::t('global', 'Cancel'); ?></button>
</div>
<?php $this->endWidget(); ?>