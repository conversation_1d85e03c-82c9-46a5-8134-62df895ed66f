<?php if($singleBranch):?>
<div id="branch-selector">
	<div class="selected" style="background-image: none;"><?php $allBranch = $this->getController()->getAllBranch(); echo $allBranch[$this->selectedBid]['title'];?></div>
</div>


<?php else:?>
<div id="branch-selector">
	<div class="selected"><?php echo (empty($this->selectedBid)) ? $this->selectLabel : $this->allBranches[$this->selectedBid]['title']?></div>
	<div class="list" style="display:<?php echo ($this->showList)?'block': 'none';?>;">
		<?php foreach($this->accessBranches as $bid):
			if( $this->hideOffice && $this->allBranches[$bid]['type']== 10) continue;
			$links = array_merge($this->urlArray, array($this->keyId=>$bid));
			$css = "";
			$css = ( !empty($this->selectedBid) && $bid == $this->selectedBid)? "current" : "";
			echo CHtml::link($this->allBranches[$bid]['title'], $links, array("class"=>$css, 'id'=>'school_'.$bid));
		endforeach;?>
		<em>×</em>
	</div>
</div>

<script>
$('#branch-selector').bind('click',function(){
	$('#branch-selector .list').toggle();
})
</script>

<?php endif;?>