function displayMediaList(){
	var ul = $('#media-list');
	
	if(mediaList.length==0){
		
	}else{
		ul.html('');
		for(var i in mediaList){
			picAssigns = new Array;
			var item = $('<li></li>')
				.attr('id', 'mc'+i)
				.attr('securityCode', mediaList[i].securityCode)
				.attr('year', mediaList[i].year)
				.attr('sid', mediaList[i].sid)
				.attr('mediaid', i)
				.html('<em class="tagged">'+mediaList[i].links.length+'</em><img class="preview" src="'+mediaList[i].thumb+'">');
			ul.append(item);
			picAssigns[i] = i;
		}
		
		$('ul#media-list li').mouseover(function(){
			opdiv = $(this).find('div.itemOp');
			if(opdiv.length == 0){
				opdiv = $('<div class="itemOp"></div>').html($('#itemOp-template').html());
				$(this).append(opdiv);
			}
		});
		
		$('ul#media-list img.preview').click(function(){
			quickLink(this);
		});	
	}
}

function quickLink(dom1){
	if(showQuickLink == 0) return; //在保存的时候不允许打开此弹出窗口
	var dom = $(dom1).parents('ul#media-list li');
	
	$('input[name|="TagLD[]"]').removeAttr('checked');
	$('#TagFree').val('');
	
	var pid = $(dom).attr('mediaid');
	
	if(mediaList[pid]['tags']['learndomain']){
		for(var i=0; i<mediaList[pid]['tags']['learndomain'].length; i++){
			$('input.LDList[value|="'+mediaList[pid]['tags']['learndomain'][i]+'"]').attr('checked','checked');
		}
	}
	$('#TagFree').val(mediaList[pid]['tags']['freeword']);
	
	
	$('ul#media-list li').removeClass("selected");
	dom.addClass("selected");

	$('#TagChild_all').removeAttr('checked');
	$('#LinkChild_all').removeAttr('checked');
	
		
	//初始化孩子信息
	$('div.height300').animate({scrollTop:0},200);
	$('input.TagChild').removeAttr('checked');
	$('input.LinkChild').removeAttr('checked');
	if(mediaList[pid]['tags']['childid']){
		for(var i=0; i<mediaList[pid]['tags']['childid'].length; i++){
			var id = '#TagChild_'+mediaList[pid]['tags']['childid'][i];
			$(id).attr('checked','checked');
		}
	}
	if(mediaList[pid]['links']){
		for(var i=0; i<mediaList[pid]['links'].length; i++){
			var id = '#LinkChild_'+mediaList[pid]['links'][i];
			$(id).attr('checked','checked');
		}
	}
	
	//初始化图片信息
	previewdom = $(dom).find('img.preview');
	$('#MediaAssignForm_mediaId').val($(dom).attr("mediaid"));
	src = previewdom.attr("src");
	src = src.replace("/thumbs/", '/');
	
	var tmpImg = document.getElementById('assign-medis-preview');
	tmpImg.onload = function(){
		openQuickAssignWindow();
	}
	if(tmpImg.src == src){
		openQuickAssignWindow();
	}else{
		tmpImg.src = src;		
	}
}

function openQuickAssignWindow(){
	$('#J_quickMediaAssign_pop').show().css({
		left : ($(window).width() - region_pop1.outerWidth())/2,
		top : ($(window).height() - region_pop1.outerHeight())/2// + $(document).scrollTop()
	});
}

var childAvatar;
var jcrop_api;
var ii=0;
//var coordData;
//var scaleData;
head.use('jcrop', function(){
	initJcrop();
    function initJcrop()//{{{
    {
		if($('#img-avatar-source').attr("src")!=""){
			$('#img-avatar-source').Jcrop({
				onRelease: releaseCheck,
				onSelect: updateCoords,
				boxWidth:400,
				boxHeight:300,
				aspectRatio:1,
				minSize:[200,200]
			},function(){
				jcrop_api = this;
			});		
		}
    };
	
    function releaseCheck()
    {
		//jcrop_api.setOptions({ allowSelect: true});
		jcrop_api.setSelect([100,100,300,300]);
    };
	
	function updateCoords(c)
	{
		$('#avatar_coords').val( JSON.stringify(c) );
		$('#avatar_scales').val( jcrop_api.getScaleFactor().toString() );
	};
	
	childAvatar = function(obj){
		
		var item = $(obj).parents('ul#media-list li');
		
		var imgSrc = item.children('img.preview').attr('src').replace('/thumbs/', '/m/') ;
		var imgSrcRefresh = imgSrc + '?_=' + Math.round(Math.random() * 100);
		
		$('#avatarChildId').val('');
		$('#J_childAvatar_pop_sub').addClass('disabled').attr('disabled','disabled');
		$('#avatar_sid').val(item.attr('sid'));
		$('#crop_window').hide();
		$('#avatar_source').val(imgSrc);
		$('#img-avatar-source').attr('src', imgSrcRefresh );
		jcrop_api.setImage(imgSrcRefresh,function(){
			//jcrop_api.animateTo([100,100,300,300]);
			jcrop_api.setSelect([100,100,300,300]);
		});
		$('#crop_window').show();
		$('#J_childAvatar_pop').show().css({
			left : ($(window).width() - region_pop1.outerWidth())/2,
			top : ($(window).height() - region_pop1.outerHeight())/2// + $(document).scrollTop()
		});	
	}
});

function item_rotate(obj){
	var item = $(obj).parents('ul#media-list li');
	var loading = $('<em class="loading"></em>');
	item.prepend(loading);
	$('ul#media-list li').removeClass("selected");
	item.addClass("selected");
	$.ajax({
		url: url_MediaModify,
		dataType: 'json',
		type: 'POST',
		data:{op:'rotate',pid:item.attr("mediaid"), year:item.attr("year"), sid:item.attr("sid"), securityCode: item.attr("securityCode")}
	}).done(function(data){
		if(data.status == 'success'){
			if(data.op == 'rotate'){
				var img = item.children('img');
				if ( img.attr("src").indexOf("?") < 0){
					img.attr("src", img.attr("src") + "?t=" + new Date().getTime() );
				}else{
					img.attr("src", img.attr("src") + "&t=" + new Date().getTime() );
				}
			}
		}else{
			alert(data.message);
			return false;
		}
		item.children('em.loading').remove();
	});
}

function item_delete(obj){
	proceed = confirm('<?php echo Yii::t("teaching", "Sure to delete?");?>');
	if(proceed){
		var item = $(obj).parents('ul#media-list li');
		$.ajax({
			url: url_MediaModify,
			dataType: 'json',
			type: 'POST',
			data:{op:'delete',pid:item.attr("mediaid"),year:item.attr("year"), sid:item.attr("sid"), securityCode: item.attr("securityCode")}
		}).done(function(data){
			if(data.status == 'success'){
				if(data.op == 'delete'){
					item.hide(400,function(){item.remove();});
				}
			}else{
				alert(data.message);
				return false;
			}
		});
	}
}

function saveTagAssigns(){
	showQuickLink = 0;
	var mid = $('#MediaAssignForm_mediaId').val();
	var li = $('#mc'+mid);
	li.prepend($('<div class="info"></div>').html('Saving...'));
	
	$('#J_quickMediaAssign_pop').hide();

	$.ajax({
		url: url_SaveTagging,
		dataType: 'json',
		type: 'POST',
		data: $('#tag-assign-form').serialize()
	}).done(function(data){
		var mediaid = data.data['mediaid'];
		if(data.data['mediaList'][mediaid])
		mediaList[mediaid] = data.data['mediaList'][mediaid];
		displayMediaList();
		
		var theTime = Math.round(new Date().getTime()/1000);
		if( (theTime - lastStated) > 10 ){
			refreshStat();
			lastStated = theTime;
		}else{
			$('ul#child-list li em.stats').html("?");
			pendingRefreshStats = true;
		}

		li.children('div.info').hide(500);
		li.children('div.info').remove();
		
	});
	showQuickLink = 1;
}


