<?php

class DirectMessageController extends TeachBasedController
{
    // 访问action的初级权限
    public $actionAccessAuths = array(
        'index'                 => 'o_T_Access'
    );

    public $toReplyNum;
    public $type;
    public $managetypeList;
    public $schoolType;
    public $schoolList;


    public function init()
    {
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'teaching';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site', 'Teaching Tasks');

        //初始化选择校园页面
        $schoolList = CommonUtils::allSchoolList();
        foreach ($this->accessBranch as $key => $item) {
            if (!in_array($item, $schoolList)) {
                unset($this->accessBranch[$key]);
            }
        }
        $this->branchSelectParams['urlArray'] = array('//mteaching/directMessage/index');
        // 自动重定向未选择学校前的 action
        if ($redirectAction = $_GET['redirectAction']) {
            $this->branchSelectParams['urlArray'] = array('//mteaching/directMessage/' . $redirectAction);
        }
        $cs = Yii::app()->clientScript;
        $cs->registerCoreScript('jquery.ui');
        $cs->registerCssFile($cs->getCoreScriptUrl() . '/jui/css/base/jquery-ui.css');
        $cs->registerCssFile(Yii::app()->themeManager->baseUrl . '/base/js/viewer/viewer.css');

        // $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/vue2.js');
        // 七牛上传所需文件
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/plupload/plupload.full.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/qiniu.min.js');
        // 引入图表所需文件
        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/echarts.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/mark/mark.min.js');
        // elementui
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/element/vue.global.js');
        $cs->registerCssFile(Yii::app()->themeManager->baseUrl . '/base/js/element/index.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/element/index.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/v-calendar/v-calendar.umd.min.js');
        $cs->registerCssFile(Yii::app()->theme->baseUrl . '/css/calendar.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/tinymce_7.2.1/tinymce.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/jquery.qrcode.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/viewer/viewer.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/hls/hls.min.js');
        //标签字体需要的文件
        $cs->registerCssFile(Yii::app()->themeManager->baseUrl . '/label/iconfont.css?v=20231124');
        Yii::import('common.models.portfolio.*');
        $this->initType();
    }

    public function beforeAction($action) {
        $this->schoolType = 'ivy';
        $this->schoolList = CommonUtils::ivySchoolList();
        if (in_array($_GET['branchId'], CommonUtils::dsSchoolList())) {
            $this->schoolType = 'ds';
            $this->schoolList = CommonUtils::dsSchoolList();
        }
        return parent::beforeAction($action);
    }

    public function actionIndex()
    {
        $this->render('index', array("data" => array()));
    }

    public function actionEditOne()
    {
        $journalId = Yii::app()->request->getParam('journal_id');
        if (!$journalId) {
            $requestUrl = 'directMessage/newId';
            $requestData['schoolId'] = $this->branchId;
            $res = CommonUtils::requestDsOnline($requestUrl, $requestData);
            $journalId = $res['data'];
            $this->redirect($this->createUrl('editOne', array('journal_id' => $journalId)));
        }
        $this->render('edit', array("data" => array()));
    }

    public function actionFeedback()
    {
        $this->branchSelectParams['urlArray'] = array('//mteaching/directMessage/feedback');
        $calendarYids = $this->getCalendars();
        $startyear = $this->calendarStartYear[$calendarYids['currentYid']];

        $data = array(
            "managetypeList" => $this->managetypeList,
            "startyear" => $startyear,
            "classList" => $this->getClassList($calendarYids['currentYid']),
            "gradeGroupList" => IvyClass::getGradeGroupListBySchoolId($this->branchId),
        );

        $this->render('feedback', array("data" => $data));
    }

    public function actionDept()
    {
        $this->render('dept');
    }

    /**
     * 转发可选老师列表
     *
     * @return void
     */
    public function actionChildTeacherList()
    {
        $requestData = array(
            'childId' => Yii::app()->request->getParam('childId'),
        );
        $requestUrl = 'directMessage/childTeacherList';
        $this->remote($requestUrl, $requestData);
    }

    /**
     * 转发可选部门列表
     *
     * @return void
     */
    public function actionChildDeptList()
    {
        $requestUrl = 'directMessage/childDeptList';
        $this->remote($requestUrl);
    }

    /**
     * 转发的详细信息
     *
     * @return void
     */
    public function actionRepostDetail()
    {
        $commentId = Yii::app()->request->getParam('commentId');
        if (!$commentId) {
            $this->fail();
        }
        $requestUrl = 'directMessage/comment/repostDetail/' . $commentId;
        $this->remote($requestUrl);
    }


    /**
     * 可选老师列表
     *
     * @return void
     */
    public function actionTeacherList()
    {
        $requestUrl = 'directMessage/teacherList';
        $this->remote($requestUrl);
    }

    /**
     * 可选老师搜索
     *
     * @return void
     */
    public function actionTeacherSearch()
    {
        $requestData = array(
            'searchString' => Yii::app()->request->getParam('searchString'),
        );
        $requestUrl = 'directMessage/teacherSearch';
        $this->remote($requestUrl, $requestData);
    }

    /**
     * 首页列表
     *
     * @return void
     */
    public function actionList()
    {
        $page = Yii::app()->request->getParam('page', 1);
        $requestUrl = 'directMessage/list?page=' . $page;
        $requestData = array(
            'otherTeacherId' => Yii::app()->request->getParam('otherTeacherId', 0),
            'date' => Yii::app()->request->getParam('date'),
            'searchString' => Yii::app()->request->getParam('searchString'),
            'schoolId' => $this->branchId,
        );
        $this->remote($requestUrl, $requestData);
    }

    /**
     * 最近发布列表
     *
     * @return void
     */
    public function actionLatest()
    {
        $requestUrl = 'directMessage/latest';
        $this->remote($requestUrl);
    }

    /**
     * 查看详情
     *
     * @return void
     */
    public function actionView()
    {
        $journalId = Yii::app()->request->getParam('journal_id');
        $requestUrl = 'directMessage/view/' . $journalId;
        $this->remote($requestUrl);
    }

    /**
     * 新增编辑
     *
     * @return void
     */
    public function actionEdit()
    {
        $journalId = Yii::app()->request->getParam('journal_id');
        $requestUrl = 'directMessage/edit/' . $journalId;
        $this->remote($requestUrl);
    }

    /**
     * 克隆
     *
     * @return void
     */
    public function actionCopy()
    {
        $journalId = Yii::app()->request->getParam('journal_id');
        $requestUrl = 'directMessage/copy/' . $journalId;
        $requestData = array(
            'keepTargets' => Yii::app()->request->getParam('keepTargets'),
            'keepJointAdmins' => Yii::app()->request->getParam('keepJointAdmins'),
            'keepAttachments' => Yii::app()->request->getParam('keepAttachments'),
        );
        $this->remote($requestUrl, $requestData);
    }

    /**
     * 发布
     *
     * @return void
     */
    public function actionPublish()
    {
        $journalId = Yii::app()->request->getParam('journal_id');
        $requestUrl = 'directMessage/publish/' . $journalId;
        $requestData = array(
            'publishDate' => Yii::app()->request->getParam('publishDate'),
        );
        $this->remote($requestUrl, $requestData);
    }

    /**
     * 修改发布时间
     *
     * @return void
     */
    public function actionPublishDate()
    {
        $journalId = Yii::app()->request->getParam('journal_id');
        $requestUrl = 'directMessage/publishDate/' . $journalId;
        $requestData = array(
            'publishDate' => Yii::app()->request->getParam('publishDate'),
        );
        $this->remote($requestUrl, $requestData);
    }

    /**
     * 取消发布放入草稿箱
     *
     * @return void
     */
    public function actionOffline()
    {
        $journalId = Yii::app()->request->getParam('journal_id');
        $requestUrl = 'directMessage/offline/' . $journalId;
        $this->remote($requestUrl);
    }

    /**
     * 删除
     *
     * @return void
     */
    public function actionDelete()
    {
        $journalId = Yii::app()->request->getParam('journal_id');
        $requestUrl = 'directMessage/delete/' . $journalId;
        $this->remote($requestUrl);
    }


    /**
     * 添加协同管理
     *
     * @return void
     */
    public function actionJointAdminsAdd()
    {
        $journalId = Yii::app()->request->getParam('journal_id');
        $staffId = Yii::app()->request->getParam('staff_id');
        $requestUrl = 'directMessage/jointAdmins/add';
        $requestData = array('journalId' => $journalId, 'staffId' => $staffId);
        $this->remote($requestUrl, $requestData);
    }

    /**
     * 删除协同管理
     *
     * @return void
     */
    public function actionJointAdminsDel()
    {
        $journalId = Yii::app()->request->getParam('journal_id');
        $staffId = Yii::app()->request->getParam('staff_id');
        $requestUrl = 'directMessage/jointAdmins/del';
        $requestData = array('journalId' => $journalId, 'staffId' => $staffId);
        $this->remote($requestUrl, $requestData);
    }

    /**
     * 清空协同管理
     *
     * @return void
     */
    public function actionJointAdminsDelAll()
    {
        $journalId = Yii::app()->request->getParam('journal_id');
        $requestUrl = 'directMessage/jointAdmins/delAll';
        $requestData = array('journalId' => $journalId);
        $this->remote($requestUrl, $requestData);
    }

    /**
     * 批量添加发布对象
     *
     * @return void
     */
    public function actionTargetsAdd()
    {
        $journalId = Yii::app()->request->getParam('journal_id');
        $targets = Yii::app()->request->getParam('targets');
        $requestUrl = 'directMessage/targets/add/' . $journalId;
        $requestData = array('journalId' => $journalId, 'targets' => $targets);
        $this->remote($requestUrl, $requestData);
    }

    /**
     * 删除发布对象
     *
     * @return void
     */
    public function actionTargetsDel()
    {
        $journalId = Yii::app()->request->getParam('journal_id');
        $targetId = Yii::app()->request->getParam('target_id');
        $requestUrl = 'directMessage/targets/del/' . $journalId . '/' . $targetId;
        $this->remote($requestUrl);
    }

    /**
     * 删除全部发布对象
     *
     * @return void
     */
    public function actionTargetsDelAll()
    {
        $journalId = Yii::app()->request->getParam('journal_id');
        $requestUrl = 'directMessage/targets/delAll/' . $journalId;
        $this->remote($requestUrl);
    }

    /**
     * 保存
     *
     * @return void
     */
    public function actionSave()
    {
        $journalId = Yii::app()->request->getParam('journal_id');
        $requestUrl = 'directMessage/save/' . $journalId;
        $requestData = array('data' => Yii::app()->request->getParam('data'));
        $this->remote($requestUrl, $requestData);
    }

    /**
     * 全部发布对象
     *
     * @return void
     */
    public function actionSubscribers()
    {
        $journalId = Yii::app()->request->getParam('journal_id');
        $requestUrl = 'directMessage/subscribers/' . $journalId;
        $this->remote($requestUrl);
    }

    /**
     * 分布日期
     *
     * @return void
     */
    public function actionOverviewDay()
    {
        $requestUrl = 'directMessage/overviewDay';
        $requestData = array(
            'startDate' => Yii::app()->request->getParam('startDate'),
            'endDate' => Yii::app()->request->getParam('endDate'),
            'otherTeacherId' => Yii::app()->request->getParam('otherTeacherId'),
        );
        $this->remote($requestUrl, $requestData);
    }

    /**
     * 评论的孩子ID
     *
     * @return void
     */
    public function actionCommentChildIdList()
    {
        $journalId = Yii::app()->request->getParam('journal_id');
        $requestUrl = 'directMessage/comment/childIdList/' . $journalId;
        $this->remote($requestUrl);
    }

    /**
     * 未回复的反馈数量
     *
     * @return void
     */
    public function actionUnRepliedNum()
    {
        $requestUrl = 'directMessage/unRepliedNum';
        $this->remote($requestUrl);
    }


    /**
     * 草稿箱列表
     *
     * @return void
     */
    public function actionDraft()
    {
        $requestUrl = 'directMessage/draft';
        $this->remote($requestUrl);
    }

    /**
     * 收藏列表
     *
     * @return void
     */
    public function actionFavorite()
    {
        $requestUrl = 'directMessage/favorite';
        $this->remote($requestUrl);
    }

    /**
     * 添加收藏
     *
     * @return void
     */
    public function actionFavoriteAdd()
    {
        $journalId = Yii::app()->request->getParam('journal_id');
        if (!$journalId) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', 'journal id 不能为空');
            $this->showMessage();
        }
        $requestData = array(
            'favorite_title' => Yii::app()->request->getParam('favorite_title'),
        );
        $requestUrl = 'directMessage/favorite/add/' . $journalId;
        $this->remote($requestUrl, $requestData);
    }

    /**
     * 删除收藏
     *
     * @return void
     */
    public function actionFavoriteDel()
    {
        $journalId = Yii::app()->request->getParam('journal_id');
        if (!$journalId) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', 'journal id 不能为空');
            $this->showMessage();
        }

        $requestUrl = 'directMessage/favorite/del/' . $journalId;
        $this->remote($requestUrl);
    }

    /**
     * 收藏置顶
     *
     * @return void
     */
    public function actionFavoriteSetTop()
    {
        $favoriteId = Yii::app()->request->getParam('favorite_id');
        if (!$favoriteId) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', 'favorite id 不能为空');
            $this->showMessage();
        }

        $requestUrl = 'directMessage/favorite/setTop/' . $favoriteId;
        $this->remote($requestUrl);
    }

    /**
     * 收藏取消置顶
     *
     * @return void
     */
    public function actionFavoriteCancelTop()
    {
        $favoriteId = Yii::app()->request->getParam('favorite_id');
        if (!$favoriteId) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', 'favorite id 不能为空');
            $this->showMessage();
        }

        $requestUrl = 'directMessage/favorite/cancelTop/' . $favoriteId;
        $this->remote($requestUrl);
    }

    /**
     * 已授权列表
     *
     * @return void
     */
    public function actionAuthorizer()
    {
        $requestUrl = 'directMessage/authorizer/list';
        $this->remote($requestUrl);
    }

    /**
     * 添加授权
     *
     * @return void
     */
    public function actionAuthorizerAdd()
    {
        $staffId = Yii::app()->request->getParam('staff_id');
        if (!$staffId) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', 'staff id 不能为空');
            $this->showMessage();
        }

        $requestUrl = 'directMessage/authorizer/add/' . $staffId;
        $this->remote($requestUrl);
    }

    /**
     * 删除授权
     *
     * @return void
     */
    public function actionAuthorizerDel()
    {
        $staffId = Yii::app()->request->getParam('staff_id');
        if (!$staffId) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', 'staff id 不能为空');
            $this->showMessage();
        }

        $requestUrl = 'directMessage/authorizer/del/' . $staffId;
        $this->remote($requestUrl);
    }

    /**
     * 未回复列表
     *
     * @return void
     */
    public function actionUnRepliedList()
    {
        $requestUrl = 'directMessage/unRepliedList';
        $requestData = array(
            'otherTeacherId' => Yii::app()->request->getParam('otherTeacherId', 0),
        );
        $this->remote($requestUrl, $requestData);
    }

    /**
     * 已回复列表
     *
     * @return void
     */
    public function actionRepliedList()
    {
        $page = Yii::app()->request->getParam('page', 1);
        $requestUrl = 'directMessage/repliedList?page=' . $page;
        $requestData = array(
            'otherTeacherId' => Yii::app()->request->getParam('otherTeacherId', 0),
        );
        $this->remote($requestUrl, $requestData);
    }


    // *************************************部门管理****************************************

    /**
     * 部门列表
     */
    public function actionDeptList()
    {
        $requestUrl = 'directMessage/dept/list';
        $this->remote($requestUrl);
    }

    /**
     * 部门详情
     */
    public function actionDeptView()
    {
        $deptId = Yii::app()->request->getParam('deptId');
        $requestUrl = 'directMessage/dept/view/' . $deptId;
        $this->remote($requestUrl);
    }

    /**
     * 部门保存
     */
    public function actionDeptSave()
    {
        if(!$this->isAdmin()) {
            $this->fail('no access');
        }
        $requestUrl = 'directMessage/dept/save';
        $requestData = array(
            'deptId' => Yii::app()->request->getParam('deptId'),
            'data' => Yii::app()->request->getParam('data', array()),
        );
        $this->remote($requestUrl, $requestData);
    }

    /**
     * 部门删除
     */
    public function actionDeptDel()
    {
        if(!$this->isAdmin()) {
            $this->fail('no access');
        }
        $deptId = Yii::app()->request->getParam('deptId');
        $requestUrl = 'directMessage/dept/del/' . $deptId;
        $this->remote($requestUrl);
    }

    /**
     * 部门添加员工
     */
    public function actionDeptStaffAdd()
    {
        if(!$this->isAdmin()) {
            $this->fail('no access');
        }
        $deptId = Yii::app()->request->getParam('deptId');
        $staffId = Yii::app()->request->getParam('staffId');
        $requestUrl = "directMessage/dept/addStaff/$deptId/$staffId";
        $this->remote($requestUrl);
    }

    /**
     * 部门删除员工
     */
    public function actionDeptStaffDel()
    {
        if(!$this->isAdmin()) {
            $this->fail('no access');
        }
        $deptId = Yii::app()->request->getParam('deptId');
        $staffId = Yii::app()->request->getParam('staffId');
        $requestUrl = "directMessage/dept/delStaff/$deptId/$staffId";
        $this->remote($requestUrl);
    }

    /**
     * 部门排序
     */
    public function actionDeptSort()
    {
        if(!$this->isAdmin()) {
            $this->fail('no access');
        }
        $requestUrl = "directMessage/dept/sort";
        $requestData = array(
            'deptIdList' => Yii::app()->request->getParam('deptIdList'),
        );
        $this->remote($requestUrl, $requestData);
    }

    public function actionContactList()
    {
        $requestUrl = "directMessage/contact/list";
        $this->remote($requestUrl);
    }

    /**
     * 老师已绑定信息
     */
    public function actionTeacherBindInfo()
    {
        $requestUrl = "directMessage/teacherBind/info";
        $this->remote($requestUrl);
    }

    /**
     * 获取绑定二维码
     */
    public function actionTeacherBindQrcode()
    {
        $staffId = Yii::app()->request->getParam('staff_id');
        $requestUrl = "directMessage/teacherBind/qrcode";
        $this->remote($requestUrl, array('staff_id' => $staffId));
    }

    /**
     * 取消绑定
     */
    public function actionTeacherUnbind()
    {
        $requestUrl = "directMessage/teacherBind/unbind";
        $this->remote($requestUrl);
    }


    /**
     * 分享列表
     */
    public function actionShareList()
    {
        $requestUrl = "directMessage/share/list";
        $this->remote($requestUrl);
    }


    /**
     * 添加分享人
     */
    public function actionShareAdd()
    {
        $staffId = Yii::app()->request->getParam('staff_id');
        $requestUrl = "directMessage/share/add/" . $staffId;
        $this->remote($requestUrl);
    }

    /**
     * 删除分享人
     */
    public function actionShareDel()
    {
        $staffId = Yii::app()->request->getParam('staff_id');
        $requestUrl = "directMessage/share/del/" . $staffId;
        $this->remote($requestUrl);
    }


    /**
     * 添加分享人
     */
    public function actionManagerShareAdd()
    {
        $requestData = array(
            'uid' => Yii::app()->request->getParam('uid'),
            'sharedToUid' => Yii::app()->request->getParam('sharedToUid'),
        );
        $requestUrl = "directMessage/manager/share/add";
        $this->remote($requestUrl, $requestData);
    }

    /**
     * 删除分享人
     */
    public function actionManagerShareDel()
    {
        $requestData = array(
            'uid' => Yii::app()->request->getParam('uid'),
            'sharedToUid' => Yii::app()->request->getParam('sharedToUid'),
        );
        $requestUrl = "directMessage/manager/share/del";
        $this->remote($requestUrl, $requestData);
    }

    /**
     * 当前用户的管理信息
     */
    public function actionManagerInfo()
    {
        $requestUrl = "directMessage/manager/info";
        $this->remote($requestUrl);
    }


    /**
     * 获取学部的分享数据
     */
    public function actionManagerShareList()
    {
        $gradeGroup = Yii::app()->request->getParam('grade_group');
        if (!$gradeGroup) {
            $this->fail('参数错误');
        }
        $requestUrl = "directMessage/manager/shareData/$gradeGroup";
        $this->remote($requestUrl);
    }


    /**
     * 管理列表
     */
    public function actionManagerList()
    {
        $gradeGroup = Yii::app()->request->getParam('grade_group');
        if (!$gradeGroup) {
            $this->fail('参数错误');
        }
        $requestData = array(
            'gradeGroup' => $gradeGroup,
        );
        $requestUrl = "directMessage/manager/list";
        $this->remote($requestUrl, $requestData);
    }

    /**
     * 添加管理人
     */
    public function actionManagerAdd()
    {
        $staffId = Yii::app()->request->getParam('staff_id');
        $gradeGroup = Yii::app()->request->getParam('grade_group');
        $requestData = array(
            'staffId' => $staffId,
            'gradeGroup' => $gradeGroup,
        );
        $requestUrl = "directMessage/manager/add";
        $this->remote($requestUrl, $requestData);
    }

    /**
     * 删除管理人
     */
    public function actionManagerDel()
    {
        $staffId = Yii::app()->request->getParam('staff_id');
        $gradeGroup = Yii::app()->request->getParam('grade_group');
        $requestData = array(
            'staffId' => $staffId,
            'gradeGroup' => $gradeGroup,
        );
        $requestUrl = "directMessage/manager/del";
        $this->remote($requestUrl, $requestData);
    }

    /**
     * 转发评论
     *
     * @return void
     */
    public function actionRepost()
    {
        $comment_id_list = Yii::app()->request->getParam('comment_id_list');
        $repost_type = Yii::app()->request->getParam('repost_type');
        $repost_to = Yii::app()->request->getParam('repost_to');
        $repost_mark = Yii::app()->request->getParam('repost_mark');
        $requestData = array(
            'comment_id_list' => $comment_id_list,
            'repost_type' => $repost_type,
            'repost_to' => $repost_to,
            'repost_mark' => $repost_mark,
        );
        $requestUrl = "directMessage/comment/repostComment";
        $this->remote($requestUrl, $requestData); 
    }

    public function actionQrcode()
    {
        $this->layout = "//layouts/column3";
        $this->render('qrcode');
    }
    public function actionDaily()
    {
        $this->layout = "//layouts/column3";
        // $this->render('daily');
        $this->render('daily', array("data" => array()));
    }
    // *************************************方法********************************************

    public function initType()
    {
        $this->type = Yii::app()->request->getParam('type', 'teacer');
        if (Yii::app()->user->checkAccess('t_T_task')) {
            $this->managetypeList = array('teacher');
        }
        if (Yii::app()->user->checkAccess('ivystaff_it') || Yii::app()->user->checkAccess('ivystaff_cd')) {
            $this->managetypeList = array('teacher', 'leader');
        }
    }

    /**
     * 检查管理员权限
     *
     * @return boolean
     */
    public function isAdmin()
    {
        // todo 暂时改成 it
        return Yii::app()->user->checkAccess('ivystaff_it');
    }

    /**
     * 检查it权限
     *
     * @return boolean
     */
    public function isDev()
    {
        return Yii::app()->user->checkAccess('ivystaff_it');
    }

    public function fail($message = 'fail', $data = array())
    {
        $this->addMessage('state', 'fail');
        $this->addMessage('message', $message);
        $this->addMessage('data', $data);
        $this->showMessage();
    }

    // 获取班级列表
    public function getClassList($yid)
    {
        $classList = array();
        $models = IvyClass::getClassList($this->branchId, $yid);
        foreach ($models as $class) {
            $classList[] = array(
                'classid' => $class->classid,
                'title' => $class->title,
            );
        }
        return $classList;
    }

    public function remote($requestUrl, $requestData = array())
    {
        $requestData['schoolId'] = $this->branchId;
        $res = CommonUtils::requestDsOnline($requestUrl, $requestData);
        if ($res['code'] == 0) {
            if(strpos($requestUrl,'directMessage/view') !== false){
                $res['data']['school_title'] = $this->branchObj['title'];
            }
            $this->addMessage('state', 'success');
            $this->addMessage('message', $res['msg']);
            $this->addMessage('data', $res['data']);
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->addMessage('data', $res['data']);
        }
        $this->showMessage();
    }
}
