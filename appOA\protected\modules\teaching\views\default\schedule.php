<script>
	var currentClassId = <?php echo $this->currentClassId;?>;
	var currentWeeknum = <?php echo $this->currentWeeknum;?>;
	var currentStartYear = <?php echo $this->currentStartYear;?>;
	var currentCalendarId = <?php echo $this->currentCalendarId;?>;
	var url_SaveSchedule = "<?php echo $this->createUrl('//teaching/respond/schedule');?>";
	var securityCode = "<?php echo md5(sprintf('%s&%s&%s&%s&%s',$this->currentStartYear, $this->currentWeeknum, $this->currentClassId, Yii::app()->user->getId(),$this->securityKey));?>";
	
	var pendingSave = false;
	var clipboard = new Array;
	var schduleId = <?php echo is_null($schedule) ? 0 : $schedule->id; ?>;
    var aTitle = <?php echo $activityTitle?>;
	
	<?php foreach($userTemplates as $ut):?>
	<?php if(!empty($ut->data->data)):?>
	var userScheduleTemplats_<?php echo $ut->scheduleid;?> = <?php echo base64_decode($ut->data->data);?>;
	<?php endif;?>
	<?php endforeach;?>
	
	var bgColorMapping = {
		188:'#FFDBDB',	//自选活动
		189:'#D5FFC6',	//英文圆圈
		190:'#D2D6FF',	//间点
		191:'#DBF0FF',	//户外活动
		192:'#AFCC6A',	//午餐
		193:'#CCCCCC',	//午睡
		194:'#FFFFDB',	//中文圆圈
		195:'#C55186',	//园外活动
		196:'#DBF0FF'	//特色课
		//188:'#FFDBDB',	//自选活动
		//189:'#D5FFC6',	//英文圆圈
		//190:'#D2D6FF',	//间点
		//191:'#DBF0FF',	//户外活动
		//192:'#D5FFC6',	//午餐
		//193:'#CCCCCC',	//午睡
		//194:'#FFFFDB',	//中文圆圈
		//195:'#D5FFC6',	//园外活动
		//196:'#DBF0FF'	//特色课
	};
	
	var userScheduleTemplats_0 = [
		{
			activityId: 188,
			content: "",
			period: 60,
			title: aTitle['188']['title']
		},
		{
			activityId: 189,
			content: "",
			period: 30,
			title: aTitle['189']['title']
		},
		{
			activityId: 190,
			content: "",
			period: 30,
			title: aTitle['190']['title']
		},
		{
			activityId: 191,
			content: "",
			period: 60,
			title: aTitle['191']['title']
		},
		{
			activityId: 192,
			content: "",
			period: 30,
			title: aTitle['192']['title']
		},
		{
			activityId: 193,
			content: "",
			period: 120,
			title: aTitle['193']['title']
		},
		{
			activityId: 188,
			content: "",
			period: 40,
			title: aTitle['188']['title']
		},
		{
			activityId: 190,
			content: "",
			period: 20,
			title: aTitle['190']['title']
		},
		{
			activityId: 194,
			content: "",
			period: 25,
			title: aTitle['194']['title']
		},
		{
			activityId: 191,
			content: "",
			period: 35,
			title: aTitle['191']['title']
		}
	];
</script>
	<?php
		$scheduleData = base64_decode($schedule->data->data);
	?>
<div id="schedule">


	<div id='cssmenu' class="actions" style="visibility: hidden;">	
		<ul>
		   <li class='has-sub'><a href='javascript:void(0);'><span>Set Schedule</span></a>
			  <ul>
				 <li id="tpl-system" class='has-sub'><a href='javascript:void(0);' onclick="setSchedule(0);"><span>System Default</span></a></li>
				 <li id="tpl-user" class='has-sub'><a href='javascript:void(0);'><span>My Template</span></a>
					<?php if(count($userTemplates)):?>
						<ul>
							<?php foreach($userTemplates as $tpl):?>
								<li class="user-tpl-item" id="tpl-user-<?php echo $tpl->scheduleid;?>" scheduleid="<?php echo $tpl->scheduleid;?>"><a onclick="setSchedule(<?php echo $tpl->scheduleid;?>)" href='javascript:void(0);'><span><?php echo $tpl->tempname;?></span></a></li>
							<?php endforeach;?>
						</ul>
					<?php endif;?>
				 </li>
			  </ul>
		   </li>
		   
		   <li class='active save-btn last fr'><a id="btn-save-schedule" href='javascript:void(0);'><span>Save</span></a>
			   <em id='save-hint' style="display:none;" title="有新改动尚未保存"></em>
			   </li>
		   <li class="fr" style="height: 27px;line-height:27px;color:#dedede;"><label>
			   <?php
				echo CHtml::checkBox('asTemplate', is_null($schedule) ? false : in_array($schedule->id, array_keys($userTemplates)) ? true : false);
				?>Set as template</label></li>
		</ul>	
	</div>
	
	<div class="clear"></div>

    <div id="sLeft">
        <ul><li class="hli">Time</li></ul>
		
		<!--place holder-->
        <ul class="hul" id="timeNodes"></ul>
    </div>
    <div id="sRight">
        <ul>
            <?php for ($i = 0; $i <= 4; $i++) { ?>
				<?php echo CHtml::openTag('li', array('class'=>"hli fl thx daytitle", 'id'=>'wd_'.$i, 'dayindex'=>$i));?>
				<label><?php echo Yii::app()->locale->getWeekDayName($i + 1); ?></label>
				<?php echo CHtml::closeTag('li');?>
            <?php } ?>
            <li class="clear"></li>
        </ul>
        <ul>
            <?php for ($i = 0; $i <= 4; $i++) { ?>
                <li id='day-<?php echo $i?>' class="fl thx J_sde daydata" data-id='<?php echo $i?>'></li>
            <?php } ?>
            <li class="clear"></li>
        </ul>
    </div>
    <div class="clear"></div>
</div>

<!--=========================弹窗============================-->
<div class="core_pop_wrap" id="J_schedule_pop" style="display:none;">
	<div class="core_pop">
		<div style="width:500px;">
			<div class="pop_top">
				<a href="" id="J_schedule_pop_x" class="pop_close"><?php echo Yii::t('global','Close');?></a>
				<strong id='d_tit'></strong>
			</div>
			<div class="pop_cont">
				<div class="pop_table" style="height:auto;" id="TS-Form-Zone"> <!--Place Holder-->
				</div>
			</div>
			<div class="pop_bottom tac">
				<?php echo CHtml::hiddenField('TSFormCurrentDayIndex', 0); ?>
				<?php echo CHtml::hiddenField('TSFormCurrentTSIndex', 0); ?>
				<?php echo CHtml::hiddenField('TSFormPosition', 0); ?>
                <button id="J_schedule_pop_close" type="submit" class="btn fr"><?php echo Yii::t('global','Cancel');?></button>
				<button type="button" onclick="saveTimeSlot();"  class="btn btn_submit mr10 fr" id="J_schedule_pop_sub"><?php echo Yii::t('global','OK');?></button>
                <div class="tips_error" style="display:none;" id="J_etip"><?php echo Yii::t('teaching','Select both campus and class.');?></div>
			</div>
		</div>
	</div>
</div>
<!--===========================结束==========================-->

<!-- Templates -->

<script type="text/template" id="item-timeslot-template">
<h3><%= title %></h3><% print(nl2br(content)) %>
</script>

<script type="text/template" id="timeslot-form-template">
	<table width="100%">
		<tr>
			<th>
				课程活动
			</th>
			<td>
				<?php echo CHtml::dropDownList('TSFormProject', '', $projects, array('class'=>'select_5','empty'=>'Please Select','onchange'=>'projectChange()'));?>
			</td>
		</tr>
		<tr>
			<th>
				时长
			</th>
			<td>
				<?php
				for($i=0;$i<9;$i++){
					$hour[$i]=$i;
				}
				echo CHtml::dropDownList('TSFormSethour', '', $hour);
				?>
				小时
				<?php
				for($i=0;$i<60;$i+=5){
					$min[$i]=$i;
				}
				echo CHtml::dropDownList('TSFormSetmin', '', $min);
				?>
				分钟
			</td>
		</tr>
		<tr>
			<th>
				Title
			</th>
			<td>
				<?php echo CHtml::textField('TSFormTitle', '', array('class'=>'input length_5'))?>
				<div id="project_tit"></div>
			</td>
		</tr>
		<tr>
			<th>
				Content
			</th>
			<td>
				<?php echo CHtml::textArea('TSFormContent', '<%= content %>', array('class'=>'length_5', 'style'=>'height: 92px;'))?>
			</td>
		</tr>
	</table>
</script>

<div id="toolbar-template" style="display:none;">
<a title="<?php echo Yii::t('global', 'Edit');?>" class="sbtn s_i_edit" href="javascript:void(0);" onclick="TS_edit(this)">Edit</a>
<a title="<?php echo Yii::t('global', 'Delete');?>" class="sbtn s_i_delete" href="javascript:void(0);" onclick="TS_delete(this)">Del</a>
<a title="<?php echo Yii::t('global', 'Prepend');?>" class="sbtn s_i_prepend" href="javascript:void(0);" onclick="TS_prepend(this)">Prepend</a>
<a title="<?php echo Yii::t('global', 'Append');?>" class="sbtn s_i_append" href="javascript:void(0);" onclick="TS_append(this)">Append</a>
</div>

<div id="toolbar-wday-template" style="display:none;">
<a title="<?php echo Yii::t('global', 'Copy');?>" href="javascript:void(0);" class="sbtn s_d_copy" onclick="WD_clipBoard(this)">Copy</a>
<a title="<?php echo Yii::t('global', 'Paste');?>" href="javascript:void(0);" class="sbtn s_d_paste" onclick="WD_paste(this)">Paste</a>
<a title="<?php echo Yii::t('global', 'Clear');?>" href="javascript:void(0);" class="sbtn s_d_clear" onclick="WD_clear(this)">Clear</a>
</div>

<script type="text/javascript">
var DisplayLengthRatio = 2.8;
var dialogShowing = false;
var currentFormData;
var currentFormView;
var activityIds = new Array;
var scheduleWeekData = new Array;

<?php
//初始化数据
if(!empty($scheduleData)): ?>
	var dataStr = <?php echo $scheduleData;?>;
	for(var i=0;i<5;i++){
		var dayData = 'scheduleWeekData[i] = ' + dataStr[i] + ';';
		eval( dayData );
	}
<?php else: ?>
	for(var i=0;i<5;i++){
		scheduleWeekData[i] = new Array;
	}
<?php endif; ?>

var DailySchedules = new Array;
function iniTimeSlotItemTool(){
	$('div.timeslot').hover(
		function(){
			if($(this).children('div.toolbar').length == 0){
				$(this).prepend($('<div></div>').addClass('toolbar').html($('#toolbar-template').html()));
			}
		}
	);
	$('li.daytitle').hover(
		function(){
			if($(this).children('div.toolbar').length == 0){
				$(this).prepend($('<div></div>').addClass('toolbar').html($('#toolbar-wday-template').html()));
			}
		}
	);
}

region_pop = $('#J_schedule_pop');

function projectChange(){
	var defaultContent = "2. \r\n\r\n3. \r\n\r\n4. \r\n\r\n所有区域都开放。\r\nAll learning centers open.";
    var proVal = $('#TSFormProject').val();
    var _tit = $('#TSFormProject option:selected').text();
    if ( 187 < proVal && proVal < 197 )
    {
        $('#TSFormTitle').show().val( _tit );
        $('#project_tit').html( '' );
    }
    else if( proVal > 0)
    {
        $('#TSFormTitle').hide().val( '自由选择时间 (Free choice Time)' );
        var ahl = '1. <a href="<?php echo Yii::app()->params['OABaseUrl']?>/child/show/activity_show.php?aid='+proVal+'" target="_blank">'+_tit+'</a>';
        $('#project_tit').html( '<div>'+ahl+'</div><div>'+aTitle[proVal]['memo']+'&nbsp;</div>' );
		
		var _content = $('#TSFormContent').val();
		if(_content == ''){
			$('#TSFormContent').val( defaultContent );
		}
    }else{
		$('#TSFormTitle').show().val( '' );
        $('#project_tit').html( '' );
        $('#TSFormContent').val('');	
	}
}

function getTimeSlots(classStart, classEnd){
	timeNodes = new Array();
	var i=0;
	var _t = classStart;
	
	var timeNodes = new Array;
	if(_t.getMinutes() != 0 && _t.getMinutes() != 30){
		timeNodes[i] = {
			period: 60 - _t.getMinutes(),
			text: ( ( _t.getHours() < 10) ? '0' + _t.getHours() : _t.getHours() ) + ':' + _t.getMinutes(),
			flag: 'first',
			odd: ( i % 2 ) ? 1 : 0,
		}
		_t.setMinutes( 60 );
		i++;
	}
	while(_t < classEnd){
		timeNodes[i] = {
			flag: (i==0)?'first':'',
			period: 30,
			text: ( ( _t.getHours() < 10) ? '0' + _t.getHours() : _t.getHours() ) + ':' + ( ( _t.getMinutes() < 10) ? '0' + _t.getMinutes() : _t.getMinutes() ),
			odd: ( i % 2 ) ? 1 : 0,
		}
		_t.setMinutes(_t.getMinutes() + 30, 0, 0);
		i++;
	}
	if(_t.getTime() == classEnd.getTime()){
		timeNodes[--i].flag = 'end';
		timeNodes[i].period = 30;
	}else{
		timeNodes[--i].flag = 'end';
		timeNodes[i].period = (classEnd.getMinutes() > 30 ) ? classEnd.getMinutes() - 30 : classEnd.getMinutes();
	}
	
	return timeNodes;
}

$(function(){
	var classStart = new Date(parseInt(<?php echo ( $classTime['start'] * 1000 );?>));
	var classEnd = new Date(parseInt(<?php echo ( $classTime['end'] * 1000 );?>));
	
	var timeNodes = new Array;
	timeNodes = getTimeSlots(classStart, classEnd);
	var timeNodesCollection = new Backbone.Collection;
	
	var timeNodeView = Backbone.View.extend({
		tagName: 'li',
		className: 'tb',
		attributes:{
		},
		initialize:function(){
			this.className = this.model.get('odd') ? 'bg1' : 'bg2';
		},
		render: function(){
			this.$el.html( _.template('<div class="tbt"><%= text %></div>', this.model.attributes) );
			this.$el.attr('style','height:'+ DisplayLengthRatio * this.model.get('period') + 'px;');
			this.$el.addClass( this.model.get('odd') ? 'bg1' : 'bg2' );
			return this;
		}
	});
	var timeNodesView = Backbone.View.extend({
		el: $("#timeNodes"),
		initialize: function(){
			this.listenTo(timeNodesCollection, 'reset', this.addAll);
			timeNodesCollection.reset(timeNodes);
		},
		addAll: function(){
			timeNodesCollection.each(this.addOne,this);
		},
		addOne: function(timeNode){
			var item = new timeNodeView({model: timeNode});
			this.$el.append(item.render().el);
		}
	});
	
	var pageTimeNodes = new timeNodesView;
	
	//***********************************

	var TimeSlot = Backbone.Model.extend({
		defaults: function(){
			return {
				day: 0,
				sindex: 0,
				period: 0,
				title: '',
				activityId: 0,
				content: '',
				startTime: 0,
				color: ''
			}
		},
	});
	var DailySchedule = Backbone.Collection.extend({
		model: TimeSlot
	});
	var timeSlotView = Backbone.View.extend({
		className: 'timeslot',
		attributes:{
		},
		template: _.template($('#item-timeslot-template').html()),
		initialize:function(){
			this.className = this.model.get('odd') ? 'bg1' : 'bg2';
		},
		render: function(){
			this.$el.html( this.template(this.model.attributes) );
			this.$el.css('height', DisplayLengthRatio * this.model.get('period'));
			this.$el.attr('sindex', this.model.get('sindex'));
			this.$el.attr('day', this.model.get('day'));
			this.$el.attr('id', 'ts-' + this.model.get('day') + '-' + this.model.get('sindex'));
			
			var pid = parseInt(this.model.get('activityId'));
			if ( 187 < pid && pid < 197 ){
				console.log(bgColorMapping.pid);
				this.$el.css('background-color', bgColorMapping[pid]);
			}
			return this;
		}
	});
	
	
	for(var i=0;i<5; i++){
		DailySchedules[i] = new DailySchedule;
	}

	pendingSave = false;
	
	var DailyView = Backbone.View.extend({
		attributes: {
			sindex: 0,
			day: 0,
		},
		initialize: function(){
			this.listenTo(this.collection, 'reset', this.addAll);
		},
		addAll: function(){
			activityIds[ this.attributes.day ] = new Array;
			this.$el.html('');
			this.attributes.sindex=0;
			this.collection.each(this.addOne, this);
			iniTimeSlotItemTool();
			if(pendingSave){
				$('#save-hint').attr('title','有新改动尚未保存').removeClass('saved').addClass('pending').show();
			}
		},
		addOne: function(timeslot){
			timeslot.set('sindex', this.attributes.sindex);
			timeslot.set('day', this.attributes.day);
			if( timeslot.get('activityId') ){
				activityIds[ this.attributes.day ].push(timeslot.get('activityId'));
			}
			var slot = new timeSlotView({model:timeslot});
			this.$el.append(slot.render().el);
			this.attributes.sindex++;
			
		}
	});
	
	var TimeSlotForm = Backbone.View.extend({
		el: $('#TS-Form-Zone'),
		template: _.template($('#timeslot-form-template').html()),
		initialize: function(){
			this.listenTo(this.model,'change', this.render);
		},
		render: function(){
			
			this.$el.html( this.template(this.model.attributes) );
			$('#TSFormProject').val(this.model.get('activityId'));
			$('#TSFormContent').val(this.model.get('content'));
			$('#TSFormTitle').val(this.model.get('title'));
			
			$('#TSFormSethour').val(parseInt(this.model.get('period')/60));
			$('#TSFormSetmin').val( (this.model.get('period')) % 60 );
			
			return this;
		}
	});
	
	var Daily = new Array;
	for(var i=0;i<5; i++){
		Daily[i] = new DailyView({
			collection: DailySchedules[i],
			el: $('#day-'+i),
			attributes:{
				day: i,
				sindex: 0
			}
		});
	}
	
	for(var i=0;i<5; i++){
		DailySchedules[i].reset(scheduleWeekData[i]);
	}
	
	$('#J_schedule_pop_x, #J_schedule_pop_close').on('click', function(e){
		e.preventDefault();
		region_pop.hide();
		dialogShowing = false;
	});

currentFormData = new TimeSlot;
currentFormView = new TimeSlotForm({model:currentFormData});

});

/**
	isEdit: -1: 新建, other: 编辑
	position: 0 修改本身； -1 之前插入； 1 之后插入
 */
function openTS_editDialog(element, isEdit, position){
	var id = $(element).parents('li.daydata').attr('data-id');
	$('#d_tit').html( $('#wd_'+id + ' label').text() + ' <?php echo Yii::t('teaching','Schedule');?>' );
	$('#TSFormCurrentDayIndex').val(id);
	$('#TSFormPosition').val( position );
	
	if(isEdit==-1 || position!=0){
		$('#TSFormCurrentTSIndex').val(scheduleWeekData[id].length);
		currentFormData.set("period", 60);
		currentFormData.set("activityId", 0);
		currentFormData.set("content", '');
		currentFormData.set("title", '');
		//projectChange();
	}else{
		var _sindex = $(element).parents('div.timeslot').attr('sindex');
		if(isset(scheduleWeekData[id]) && isset(scheduleWeekData[id][_sindex])){
			currentFormData.set(scheduleWeekData[id][_sindex]);
            currentFormData.set("content", scheduleWeekData[id][_sindex].content.replace(/<div(.*?)<\/div>/gi, ''));
			projectChange();
			$('#TSFormCurrentTSIndex').val(_sindex);
		}
	}
	if(position!=0){
		var _sindex = $(element).parents('div.timeslot').attr('sindex');
		$('#TSFormCurrentTSIndex').val(_sindex);
	}

	region_pop.show().css({
		left : ($(window).width() - region_pop.outerWidth())/2,
		top : ($(window).height() - region_pop.outerHeight())/2 //+ $(document).scrollTop()
	});

	dialogShowing = true;
}

function WD_clear(obj){
	if(dialogShowing) return true;
	if(confirm('确定删除当天的日程安排')){
		pendingSave=true;
		var dayIndex = parseInt($(obj).parents('li.daytitle').attr('dayindex'));
		scheduleWeekData[dayIndex] = [];
		DailySchedules[dayIndex].reset(scheduleWeekData[dayIndex]);
	}
}
function WD_clipBoard(obj){
	if(dialogShowing) return true;
	var dayIndex = $(obj).parents('li.daytitle').attr('dayindex');
	clipboard = new Array;
	for(var i=0; i<scheduleWeekData[dayIndex].length; i++){
		clipboard.push( scheduleWeekData[dayIndex][i] );
	}
}
function WD_paste(obj){
	if(dialogShowing) return true;
	var dayIndex = $(obj).parents('li.daytitle').attr('dayindex');
	if( scheduleWeekData[dayIndex].length == 0 || ( scheduleWeekData[dayIndex].length && confirm('此操作将覆盖本日原有数据，确定继续？') ) ){
		scheduleWeekData[dayIndex] = [];
		for(var i=0; i<clipboard.length; i++){
			scheduleWeekData[dayIndex].push( clipboard[i] );
		}
		DailySchedules[dayIndex].reset(scheduleWeekData[dayIndex]);	
	}
}
function reindexArray( array )
{
	var result = [];
	for( var key in array )
		result.push( array[key] );
	return result;
};
function TS_edit(obj){
	openTS_editDialog(obj, 0, 0);
}
function TS_prepend(obj){
	openTS_editDialog(obj, 0, -1);
}
function TS_append(obj){
	openTS_editDialog(obj, 0, 1);
}

function TS_delete(obj){
	if(dialogShowing) return true;
	var dom = $(obj).parents('div.timeslot');
	var dayIndex = dom.attr('day');
	var slIndex = dom.attr('sindex');
	delete(scheduleWeekData[dayIndex][slIndex]);
	scheduleWeekData[dayIndex] = reindexArray(scheduleWeekData[dayIndex]);
	pendingSave=true;
	DailySchedules[dayIndex].reset(scheduleWeekData[dayIndex]);
}

function setSchedule(id){
	if(confirm('此操作将覆盖本周所有数据，确定继续？')){
		if(id){
			eval('var dataStr = userScheduleTemplats_' + id + ';');
			for(var i=0;i<5;i++){
				scheduleWeekData[i] = [];
				var cmd = ' scheduleWeekData['+i+'] = ' + dataStr[i] + ';'
				eval(cmd);
				DailySchedules[i].reset(scheduleWeekData[i]);
			}
		}else{
			for(var i=0;i<5;i++){
				scheduleWeekData[i] = [];
				scheduleWeekData[i] = userScheduleTemplats_0.slice(0);
				DailySchedules[i].reset(scheduleWeekData[i]);
			}			
		}
	}
}

function saveTimeSlot(){
	var dayIndex = $('#TSFormCurrentDayIndex').val();
	var tsIndex = $('#TSFormCurrentTSIndex').val();
	var position = $('#TSFormPosition').val();
	
	var p = parseInt($('#TSFormSethour').val() * 60 ) + parseInt($('#TSFormSetmin').val());
	if(parseInt(p)>0){
        var actId = $('#TSFormProject').val();
        $('#project_tit').find('div:eq(1)').html('&nbsp;');
		var editedObj = {
			title: $('#TSFormTitle').val(),
			content: (187 < actId && actId < 197) ? $('#TSFormContent').val() : $('#project_tit').html() + $('#TSFormContent').val(),
			activityId: actId,
			period: p
		};
		var start = 0;
		if(position == -1){
			start = parseInt(tsIndex);
		}
		if(position == 1){
			start = parseInt(tsIndex) + 1;
		}
		if(position != 0){
			scheduleWeekData[dayIndex].splice(start, 0, editedObj);
			pendingSave = true;
		}else{
			var beforeSave = JSON.stringify( scheduleWeekData[dayIndex][tsIndex] );		
			scheduleWeekData[dayIndex][tsIndex] = editedObj;
			if (beforeSave != scheduleWeekData[dayIndex][tsIndex]) pendingSave=true;
		}
	
		$('#J_schedule_pop_x').click();
		DailySchedules[dayIndex].reset(scheduleWeekData[dayIndex]);	
	}
}

head.use('dialog', 'draggable', function(){
	region_pop.draggable( { handle : '.pop_top'} );
    $('li.J_sde').hover(
    function(e){
        var html = '<a id="add-sde" href="javascript:;" role="button"></a>';
        $(this).append(html);
        $('#add-sde').on('click', function(e){
            e.preventDefault();
			openTS_editDialog(this, -1, 0);
        });
    },
    function(e){
        $('#add-sde').remove();
    });
    
    $('#J_schedule_pop_x, #J_schedule_pop_close').on('click', function(e){
		e.preventDefault();
		region_pop.hide();
	});
    
	var saving = false;
	$('#btn-save-schedule').click(function(){
		if(saving) return false;
		
		saving = true;
		$(this).attr('disabled','disabled');
		$(this).text('Saving...');
		
		var dayName = new Array(0,1,2,3,4);
		var postData = {0:'',1:'',2:'',3:'',4:''};
		for(var i=0;i<scheduleWeekData.length;i++){
			postData[dayName[i]] = JSON.stringify(scheduleWeekData[i]);
		}
	
		$.ajax({
			url: '<?php echo $this->createUrl('//teaching/respond/schedule');?>',
			dataType: 'json',
			type: 'POST',
			data:{
				scheduleData: JSON.stringify(postData),
				classid : currentClassId,
				weeknum : currentWeeknum,
				startyear: currentStartYear,
				activityIds: activityIds.toString(),
				yid: currentCalendarId,
				setTemplate: $('#asTemplate').is(':checked') ? 1 : 0,
				securityCode: securityCode
			}
		}).done(function(data){
			if(data.state == 'success'){
				$('#save-hint').attr('title', '保存成功').removeClass('pending').addClass('saved').show();
				if(data.data['removeTpl']){
					$('#tpl-user-'+data.data['removeTpl']).remove();
					if ( $('li#tpl-user li').length == 0 ){
						$('li#tpl-user').hide();
					}
				}
				if(data.data['addTpl']){
					var li_ele = $('<li></li>')
							.addClass('user-tpl-item')
							.attr('scheduleid',data.data['addTpl'])
							.attr('id', 'tpl-user-'+data.data['addTpl'])
							.html("<a onclick='setSchedule("+data.data['addTpl']+")' href='javascript:void(0);'><span>"+data.data['tplName']+"</span></a>");
					if($('li#tpl-user ul').length == 0){
						var ul_obj = $('<ul></ul>').append(li_ele);
						$('li#tpl-user').append(ul_obj).show();
					}else{
						$('li#tpl-user ul').append(li_ele).show();
					}
				}
				$('#save-hint').hide(1000);
			}else{
				alert(data.message);
			}
			$('#btn-save-schedule').removeAttr('disabled');
			$('#btn-save-schedule').text('Save');
			saving = false;
			return false;
		});
	});
	
	$(".actions").css("visibility", "visible");
	if ( $('#tpl-user li').length == 0 ){
		$('#tpl-user').hide();
	}
});
</script>