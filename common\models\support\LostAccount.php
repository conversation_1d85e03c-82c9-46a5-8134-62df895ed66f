<?php

/**
 * This is the model class for table "ivy_lost_account".
 *
 * The followings are the available columns in table 'ivy_lost_account':
 * @property integer $id
 * @property string $schoolid
 * @property string $childname
 * @property integer $birthday
 * @property string $parentname
 * @property string $email
 * @property string $phone
 * @property string $memo
 * @property string $ip
 * @property integer $timestamp
 */
class LostAccount extends CActiveRecord
{
    public $verifyCode;
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return LostAccount the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_lost_account';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('schoolid, childname, birthday, parentname, email, phone, ip, timestamp, verifyCode', 'required'),
			array('phone, timestamp', 'numerical', 'integerOnly'=>true),
			array('schoolid', 'length', 'max'=>10),
			array('childname, parentname, email, ip', 'length', 'max'=>64),
			array('phone', 'length', 'min'=>11, 'max'=>11),
            array('email', 'email'),
			array('memo', 'safe'),
            array('verifyCode', 'captcha', 'allowEmpty' => !CCaptcha::checkRequirements()),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, schoolid, childname, birthday, parentname, email, phone, memo, ip, timestamp', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'schoolid' => Yii::t("labels", "Campus"),
			'childname' => Yii::t("auth", "Name of Child"),
			'birthday' => Yii::t("labels", "Date of Birth"),
			'parentname' => Yii::t("auth", "Name of Parent"),
			'email' => Yii::t("labels", "Email"),
			'phone' => Yii::t("labels", "Mobile Phone"),
			'memo' => Yii::t("auth", "Special Notes"),
            'verifyCode' => Yii::t("auth", "Verify Code"),
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('childname',$this->childname,true);
		$criteria->compare('birthday',$this->birthday);
		$criteria->compare('parentname',$this->parentname,true);
		$criteria->compare('email',$this->email,true);
		$criteria->compare('phone',$this->phone,true);
		$criteria->compare('memo',$this->memo,true);
		$criteria->compare('ip',$this->ip,true);
		$criteria->compare('timestamp',$this->timestamp);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}