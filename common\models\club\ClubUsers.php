<?php

/**
 * This is the model class for table "ivy_club_users".
 *
 * The followings are the available columns in table 'ivy_club_users':
 * @property integer $uid
 * @property string $uname
 */
class ClubUsers extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_club_users';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('uid, uname', 'required'),
			array('uid', 'numerical', 'integerOnly'=>true),
			array('uname', 'length', 'min'=>5, 'max'=>20, 'encoding'=>'GBK','tooShort'=>'用户名太短，请至少使用{min}个字符或3个汉字','tooLong'=>'用户名太长，请不要超过{max}个字符或10个汉字'),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('uid, uname', 'safe', 'on'=>'search'),

            array('uname', 'unique'),
            array('uname', 'match', 'pattern'=>'/^((?!管理|admin|\<|\>|sysop|ivyschools|ivygroup|艾毅).)*$/i', 'message'=>'显示名不被允许'),
//            array('uname', 'match', 'pattern'=>'/^[\w\s,((?!管理).),(?!(admin).),(?!(<).),(?!(>).)]+$/', 'message'=>'显示名不被允许'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'user' => array(self::BELONGS_TO, 'User', 'uid'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'uid' => 'Uid',
			'uname' => '显示名',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('uid',$this->uid);
		$criteria->compare('uname',$this->uname,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return ClubUsers the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}
