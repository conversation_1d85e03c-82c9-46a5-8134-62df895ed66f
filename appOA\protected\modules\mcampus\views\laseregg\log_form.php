<div class="modal-header">
	<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
	<h4 class="modal-title" id="exampleModalLabel"><?php echo $model->id?'更新记录：':'新建记录：'; ?></h4>
</div>
<?php $form=$this->beginWidget('CActiveForm', array(
	'id'=>'laseregg-info-form',
	'enableAjaxValidation'=>false,
	'htmlOptions'=>array('class'=>'J_ajaxForm form-horizontal', 'role'=>'form'),
)); ?>
<div class="modal-body">
	<div class="form-group">
		<label class="col-xs-2 control-label"><?php echo $form->labelEx($model,'log_timestamp'); ?></label>
		<div class="col-xs-3">
			<?php $model->time_hour= $model->isNewRecord ? intval(date('H', time())) : intval(date('H', $model->log_timestamp));echo $form->dropDownList($model, 'time_hour', $this->time_hour , array('class'=>'select_2 form-control', 'empty'=>Yii::t("global", 'Please Select'))); ?>
		</div>
		<div class="col-xs-3">
			<?php if(!$model->isNewRecord)$model->time_minute=date('i', $model->log_timestamp);echo $form->dropDownList($model,'time_minute', $this->time_minute , array('class'=>'select_2 form-control', 'empty'=>Yii::t("global", 'Please Select'))); ?>
		</div>
	</div>
	<div class="form-group">
		<label class="col-xs-2 control-label"><?php echo $form->labelEx($model,'laseregg_mac'); ?></label>
		<div class="col-xs-6">
			<?php echo $form->dropDownList($model,'laseregg_mac', $lasereggArr , array('class'=>'form-control', 'empty'=>Yii::t("global", 'Please Select'))); ?>
		</div>
	</div>
	<div class="form-group">
		<label class="col-xs-2 control-label"><?php echo $form->labelEx($model,'laseregg_num'); ?></label>
		<div class="col-xs-6">
			<?php echo $form->textField($model,'laseregg_num',array('maxlength'=>255,'class'=>'form-control')); ?>
		</div>
	</div>
</div>
<div class="modal-footer">
	<button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global','Submit');?></button>
	<button type="button" class="btn btn-default"  data-dismiss="modal"><?php echo Yii::t('global','Cancel');?></button>
</div>

<?php $this->endWidget(); ?>
