<script>
    var qParams = {
        startYear: <?php echo $taskData['params']['startYear'];?>,
        classId: <?php echo $taskData['params']['classId'];?>,
        weekNumber: <?php echo $taskData['params']['weekNumber'];?>,
        page: 1,
        domainId: 0,
        childId: 0,
        freeTag: ''
    };

    var childDataRaw = <?php echo CJSON::encode($this->getNameList($taskData['params']['classId'])); ?>;
    var childData = _.sortBy(childDataRaw, 'name');
    var childCount = <?php echo CJSON::encode($taskData['childCount'])?>;
    var _childCount = <?php echo CJSON::encode($taskData['_childCount'])?>;
    var childpublic = _.isUndefined(childCount[0]) ? 0 : childCount[0];
    var _childpublic = _.isUndefined(_childCount[0]) ? 0 : _childCount[0];
    var ldomain = <?php echo CJSON::encode(CHtml::listData(Term::model()->ld()->findAll(),'diglossia_id',
        function($model){
            return ( Yii::app()->language == 'en_us' ) ? $model->entitle : $model->cntitle;
        }))?>;
    var months = <?php echo CJSON::encode($taskData['months'])?>;
    var monthLabels = <?php echo CJSON::encode(Yii::app()->getLocale()->monthNames);?>;
    var onlines = <?php echo CJSON::encode($taskData['onlines'])?>;

    var listAvatarChild = false;
</script>

<?php
$token = sprintf('%s&%s&%s&%s', $taskData['params']['branchId'], $taskData['params']['startYear'], $taskData['params']['classId'], $this->securityKey);
$token = md5($token);

?>

<script>
    //link已包含branchId

    //调用远程图片服务器使用这个
<!--    var tmpUrl = '--><?php //echo $this->createUrl("//notifyBroker/fetchWeeklyMediaJS", array('token'=>$token, 'time'=>$taskData['params']['time']));?><!--';-->
    //调用本机图片服务器使用这个
    var tmpUrl = '<?php echo $this->createUrl("//notifyBroker/fetchWeekly", array('token'=>$token, 'time'=>$taskData['params']['time']));?>';
//    var page = 0;
    var contentData;
    var isDs = "<?php echo in_array($this->branchId, CommonUtils::dsSchoolList()) ? 1 : 0; ?>"
    var suffix = '!w600';
    var suffixA = '!w600A';
    var suffixB = '!w600B';
    var suffixC = '!w600C';
    if (isDs == 1) {
        suffix = '!ww600';
        suffixA = '!ww600A';
        suffixB = '!ww600B';
        suffixC = '!ww600C';
    }
    function loadMedia(obj){
        qParams.page = parseInt( $(obj).parents('li[pageno]').attr('pageno') );

        loadContentData();
    }

    function renderPage(){
        if( contentData.pageData.pageCount > 1){
            $('#media-pagination').empty().show();
            for(var i=1; i<=contentData.pageData.pageCount; i++){
                $('<li pageno='+i+'></li>').html('<a href="javascript:;" onclick="loadMedia(this)">'+i+'</a>').appendTo($('#media-pagination'));
            }
            $('#media-pagination li[pageno|='+ ( parseInt(contentData.pageData.currentPage) + 1 ) + ']').addClass('active');
        }else{
            $('#media-pagination').empty().hide();
        }

        $('#media-container').empty();
        _.each(contentData.mediaData, function(_data,_index){
            if(_data.type == 'video' && parseInt(_data.server)>=20){
                $('#media-container').append( _.template($('#videoitem-action-tmpl').html(), _data) )
            }else{
                $('#media-container').append( _.template($('#imgitem-action-tmpl').html(), _data) )
            }

        });
    }

    function loadContentData(){
        var url = tmpUrl;
        _.each(qParams,function(_d,_i){
            url +='&' + _i + '=' + _d;
        });
        url += '&_=' + (new Date()).valueOf();
        head.load(
            url,
            function() {
                renderPage();
            }
        );
    }

    function applyFilter(){
        qParams.page = 1;
        qParams.childId = $('#filter_childid').val();
        qParams.weekNumber = $('#filter_wnumber').val();
        qParams.domainId = $('#filter_ldomain').val();
        qParams.freeTag = $('#filter_freetag').val();
        qParams.type = $('#filter_type').val();
        loadContentData();
    }

    loadContentData();

    var renderChildList = null;
    var displayLargeImg = false;

    $(function(){
        var childItemTemplate = _.template( $('#child-li-item-template').html() );
        renderChildList = function(){
            $('#weekly-media-child-list').html('');
            var childidObj = $('#filter_childid');
            var ldomainObj = $('#filter_ldomain');
            childidObj.empty();
            ldomainObj.empty();
            var _option = $('<option></option>').val(0).text('<?php echo Yii::t("teaching", "All Students");?>');
            var _opt = $('<option></option>').val(0).text('<?php echo Yii::t("teaching", "All Domains");?>');
            childidObj.append(_option);
            ldomainObj.append(_opt);
            _.each(childData, function(_data,_id){
                var _option = $('<option></option>').val(_data.id).text(_data.name);
                childidObj.append(_option);

                var cCount = !_.isUndefined(childCount[_data.id]) ? childCount[_data.id] : 0;
                _data['count'] = parseInt(cCount) + parseInt(childpublic);

                var _cCount = !_.isUndefined(_childCount[_data.id]) ? _childCount[_data.id] : 0;
                _data['_count'] = parseInt(_cCount) + parseInt(_childpublic);

                var _item = childItemTemplate(_data);
                var _child = _.first($(_item));

                if(parseInt(_data['count']) > 2 && parseInt(_data['_count']) > 2 ){
                    $(_child).addClass('item-pass');
                }else{
                    $(_child).addClass('item-nopass');
                }

                //如果周报告上线
                if(_.indexOf(onlines, _data.id) != -1) $(_child).addClass('online');

                $('#weekly-media-child-list').append( $(_child) );
            });
            $.each(ldomain, function(_id, _data){
                var _opt = $('<option></option>').val(_id).text(_data);
                ldomainObj.append(_opt);
            });
            childidObj.removeAttr('disabled');
            ldomainObj.removeAttr('disabled');
        }

        renderChildList();

        $('#playerModal').on('shown.bs.modal', function(){
            $('#player video')[0].player.play();
        });

        $('#playerModal').on('hide.bs.modal', function(){
            $('#player video')[0].player.pause();
        });
    });

    function setAvatar(id)
    {
        var image = $('#avatar-avatar');
        if(!_.isEmpty(image.attr('src'))){
            image.cropper("destroy");
        }
        var imgUrl = contentData['mediaData'][id]._url;
        var fileid = contentData['mediaData'][id].filename;
        var decollator = ';;';
        if(fileid.indexOf(decollator) > -1){
            var fileArr = fileid.split(decollator);
            switch(fileArr[1])
            {
                case 'A':
                    imgUrl = imgUrl.replace(suffixA, "?imageView/2/w/600/q/90/format/JPG|imageMogr2/rotate/90");
                    break;
                case 'B':
                    imgUrl = imgUrl.replace(suffixB, "?imageView/2/w/600/q/90/format/JPG|imageMogr2/rotate/180");
                    break;
                case 'C':
                    imgUrl = imgUrl.replace(suffixC, "?imageView/2/w/600/q/90/format/JPG|imageMogr2/rotate/270");
                    break;
            }
        }
        else{
            imgUrl = imgUrl.replace(suffix, "?imageView/2/w/600/q/90/format/JPG");
        }
        image.attr('src', imgUrl);
        image.cropper({
            aspectRatio: 1,
            minWidth: 200,
            minHeight: 200,
            zoomable: false,
            preview: "#childSetAvatar .avatar-preview",
            done: function(data) {
                var w = Math.round(data.width);
                var h = Math.round(data.width);
                var x = Math.round(data.x);
                var y = Math.round(data.y);
                $('#avatar').val(imgUrl+'|imageMogr2/crop/!'+w+'x'+h+'a'+x+'a'+y+'|imageView/2/w/200');
            }
        });

        if(!listAvatarChild){
            $('#avatar-chilid-list').empty();
            _.each(childData, function(_data){
                $('#avatar-chilid-list').append( _.template($('#avatar-child-item-tmpl').html(), _data) );
            });
            listAvatarChild = true;
        }

        $('#childSetAvatar').modal({backdrop:false});
    }

    function openAssign(id)
    {
        var sc = [], sl = [], sm=[], sf = '';
        var objData = _.isUndefined(contentData['mediaData'][id]) ? null : contentData['mediaData'][id];
        if(objData.tag){
            var arrData = objData.tag.split(' ');
            for(var i=0; i<arrData.length; i++){
                var _t = arrData[i].substr(0, 2);
                var _v = arrData[i].split('_')[1];
                if(_t == 'sc'){
                    sc.push(_v);
                }
                else if(_t == 'sl'){
                    sl.push(_v);
                }
                else if(_t == 'sm'){
                    sm.push(parseInt(_v));
                }
                else if(_t == 'sf'){
                    sf = _v;
                }
            }
        }
        objData['sc'] = sc;
        objData['sl'] = sl;
        objData['sm'] = sm;
        objData['sf'] = sf;
        $.getJSON('<?php echo $this->createUrl('getLinks')?>',
            {id: id, yid: objData.yid, classid: classid, weeknum: weeknum},
            function(data){
                objData['clinks'] = data;
                var tagTmpl = _.template( $('#tag-action-tmpl').html(), objData );
                displayLargeImg = false;
                $('#assignPhoto .modal-body div.img-wrapper').hide();
                $('#assignPhoto .modal-body div.mainContent').html( tagTmpl );
                $('#assignPhoto').modal({backdrop:false});
                head.Util.checkAll();
                if(_.indexOf(data, '0') !== -1){
                    $('#allchild').click();
                }
            }
        );
    }

    function delMedia(id, classid)
    {
        if(confirm('<?php echo Yii::t("message", "Sure to delete?");?>')){
            $.post('<?php echo $this->createUrl('delPhoto')?>', {id: id, classid: classid}, function(data){
				if(data.state == 'success'){
					$('#item-'+id).remove();
                    resultTip({msg: data.message});
				}
				else{
					alert(data.message);
				}
			}, 'json');
        }
    }

    //做完标记回调
    function callback(data)
    {
        if( !_.isUndefined(contentData['mediaData'][data.id]) ){
            contentData['mediaData'][data.id]['tag'] = data.tag;
        }
        if( data.allchild == 0 ){
            if(data.cChild){
                for(var i in data.cChild){
                    var cc = _.isUndefined(childCount[i]) ? 0 : childCount[i];
                    childCount[i] =  parseInt(cc) + 1;
                }
            }
            if(data.oldKeys){
                for(var i in data.oldKeys){
                    var cc = _.isUndefined(childCount[i]) ? 0 : childCount[i];
                    childCount[i] =  parseInt(cc) - 1;
                }
            }
            if(data._allchild){
                childpublic = parseInt(childpublic) - 1;
            }
        }
        else{
            childpublic = parseInt(childpublic) + 1;
        }
        renderChildList();
        setTimeout(function(){
            $('#assignPhoto').modal('hide');
        }, 600);
    }

    // 编辑图片描述回调
    function callback1(data)
    {
        if(data.delphoto){
            childCount[data.childid] -= data.delphoto;
        }
        _childCount[data.childid] = data.aqlCount;
        _childpublic = data.totCount;
		if(data.stat == 20){
			onlines.push(data.childid);
		}
		else{
			if(onlines.indexOf(data.childid) != -1){
				delete onlines[onlines.indexOf(data.childid)];
			}
		}
        renderChildList();
        setTimeout(function(){
            $('#childPhoto').modal('hide');
        }, 600);
    }

	function callback2(data)
    {
		childDataRaw[data.childid].photo = data.photo;
		renderChildList();
		setTimeout(function(){
            $('#childSetAvatar').modal('hide');
        }, 600);
	}

    function doubleA(_this)
    {
        var p = $(_this).parent().find('input');
        var p0 = $(p[0]);
        var p1 = $(p[1]);
        if(p0.attr('checked') && p1.attr('checked')){
            p0.attr('checked', false);
            p1.attr('checked', false);
        }
        else{
            p0.attr('checked', true);
            p1.attr('checked', true);
        }
    }

    function childPhoto(id, count)
    {
        if(count>0){
            $.getJSON('<?php echo $this->createUrl('childPhotos')?>',
                {id: id, classid: classid, weeknum: weeknum},
                function(data){
                var html = '<input type="hidden" name="childid" value="'+id+'">';
                for(var i=0; i<data['ret'].length; i++){
                    html += _.template($('#photo-item-tmpl').html(), data['ret'][i]);
                }
                $('#childPhoto .modal-body div.mainContent').html( html );
                $('#childPhoto').modal({backdrop:false});
                $('#childPhoto .modal-title').html(childDataRaw[id].name+'<small><?php echo Yii::t("teaching","Weekly Media");?></small>');
                if(data.stat == 1){
                    $('#stat').attr('checked', true);
                }
                else{
                    $('#stat').attr('checked', false);
                }
            });
        }
        else{
            alert('<?php echo Yii::t("message", "Please assign photos first");?>');
        }
    }

    function delPhoto(_this)
    {
        if( $(_this).attr('checked') == 'checked' ){
            $('#item_photo_'+$(_this).val()).find('.J_caption').hide('blind', 100);
        }
        else{
            $('#item_photo_'+$(_this).val()).find('.J_caption').show('blind', 100);
        }
    }

    function play(id)
    {
        if( $('#player video').length<1 || ($('#player video').length>0 && $('#player video').attr('src') != contentData['mediaData'][id]._url)){
            $('#player').html('<video src="'+contentData['mediaData'][id]._url+'" type="video/mp4" controls="controls" preload="none" autoplay="autoplay"></video>');
            $('video').mediaelementplayer();
        }
        $('#playerModal').modal();
    }

    function rotate(id, classid)
    {
        $.post('<?php echo $this->createUrl('rotate')?>', {id: id, classid: classid}, function(data){
            contentData['mediaData'][id]['filename'] = data.data.filename;
            contentData['mediaData'][id]['url'] = data.data.photo;
            contentData['mediaData'][id]['_url'] = data.data.photo.replace('!h120', '!ww600');
            var Img = new Image();
            Img.src = data.data.photo;
            Img.onload = function (){
                $('#item-'+id+' a.img-warp').html(Img);
            }
        }, 'json');
    }

    function enlargeImage(obj)
    {
        if(displayLargeImg) {
            displayLargeImg = false;
            $('#assignPhoto .modal-body div.img-wrapper').hide(50);
            $('#assignPhoto .modal-body div.img-wrapper img').attr('src', "");
        } else {
            var imgSrc = $(obj).attr("src");
            imgSrc = imgSrc.replace('!h120', suffix);
            var imgEle = $('#assignPhoto .modal-body div.img-wrapper img');
            imgEle.attr('src', imgSrc);
            imgEle.load(function(){
                $('#assignPhoto .modal-body div.img-wrapper').show('blind', {}, 200, function(){
                    displayLargeImg = true;
                });
            });
        }
    }

    function showBigPhoto(id, url) {
        var obj = $('#item_photo_'+id+' .J_cxt')
        if (obj.find('#big_photo_'+id).length){
            obj.find('#big_photo_'+id).remove()
        }
        else {
            url = url.replace('!h120', suffix);
            var img = "<img class='img-thumbnail' src='"+url+"' id='big_photo_"+id+"'>"
            obj.prepend(img)
        }
    }

    function setAll(type)
    {
        $.post(
            '<?php echo $this->createUrl('setAllDs', array('classid'=>$this->selectedClassId, 'weeknum'=>$this->selectedWeeknum))?>',
            {type: type},
            function(data){
                if(data.state == 'success'){
                    resultTip({msg: data.message});
                    onlines = data.data;
                    renderChildList();
                }
                else{
                    resultTip({msg: data.message, error: 1});
                }
            },
            'json'
        );
    }

    function assignTag(_this)
    {
        var obj = $(_this);
        if(obj.attr('checked') == 'checked'){
            obj.parents('.J_warp_tagweek').find("input[data-yid='J_check_c1']").attr('checked', true);
        }
    }

</script>

<div class="col-md-9">
    <div class="panel panel-default">
        <div class="panel-heading">
            <div class="form-inline" role="form">
                <div class="form-group">
                    <div class="input-group">
                        <div class="input-group-addon"><span class="glyphicon glyphicon-tag"></span></div>
                        <select class="form-control" size="1" name="filter[ldomain]" id="filter_ldomain" disabled>
                            <!--place holder-->
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <div class="input-group">
                        <div class="input-group-addon"><span class="glyphicon glyphicon-user"></span></div>
                        <select class="form-control" size="1" name="filter[childid]" id="filter_childid" disabled>
                            <!--place holder-->
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <div class="input-group">
                        <div class="input-group-addon"><span class="glyphicon glyphicon-calendar"></span></div>
                        <?php
                        Yii::import('common.models.calendar.CalendarWeek');
                        $cwCrit = new CDbCriteria();
                        $cwCrit->compare('yid', $this->calendarId );
                        $cwCrit->order = 'weeknumber DESC';
                        echo CHtml::dropDownList(
                            'filter[wnumber]',
                            $taskData['params']['weekNumber'],
                            CHtml::listData(CalendarWeek::model()->findAll($cwCrit),'weeknumber',
                                function($model){
                                    return sprintf('W%02d %s ~ %s',
                                        $model->weeknumber,
                                        Yii::app()->dateFormatter->format('yyyy.MM.dd', $model->monday_timestamp),
                                        Yii::app()->dateFormatter->format('yyyy.MM.dd', $model->monday_timestamp + 7 * 24 * 3600)
                                        );
                                }),
                            array('class'=>'form-control', 'empty'=>Yii::t("teaching","All Weeks"))
                        );
                        ?>
                    </div>
                </div>
                <div class="form-group">
                    <div class="input-group">
                        <div class="input-group-addon"><span class="glyphicon glyphicon-film"></span></div>
                        <?php
                            echo CHtml::dropDownList(
                                'filter[type]',
                                $taskData['params']['type'],
                                array('photo' => Yii::t('teaching', 'Photo'), 'video' => Yii::t('teaching', 'Video')),
                                array('class' => 'form-control', 'empty' => Yii::t("teaching", "All Type"))
                            );
                        ?>
                    </div>
                </div>
                <div class="form-group">
                    <div class="input-group">
                        <div class="input-group-addon"><span class="glyphicon glyphicon-comment"></span></div>
                        <?php
                        echo CHtml::textField('filter[freetag]', '', array('class'=>'form-control','placeholder'=>Yii::t('teaching','Free Tag')));
                        ?>
                    </div>
                </div>
                <button class="btn btn-primary" onclick="applyFilter();"><?php echo Yii::t('global','Filter');?></button>
            </div>
        </div>
        <div class="panel-body">
            <div>
                <ul class="pagination pagination-sm" id="media-pagination"></ul>
            </div>
            <div id="media-container">
            </div>
        </div>
    </div>
</div>

<div class="col-md-3">

    <div class="page-help alert alert-info" role="alert" data-page="<?php echo $this->selectedTask?>">
        <h3><?php echo Yii::t('teaching','Instructions');?></h3>
        <ol>
            <li><?php echo Yii::t('teaching',
                'Click a photo/video, make Tag or assign photo to Weekly Report.');?></li>
            <li><?php echo Yii::t('teaching',
                'Numbers attached to students listed below present how many photos assigned to this student this
                weekly report.');
                ?></li>
            <li><?php echo Yii::t('teaching','Click the student name, a window pops up with list of all photos
            assigned, please make captions accordingly.');?></li>
        </ol>
    </div>

    <div class="panel panel-default">
        <div class="panel-heading">
            <button class="btn btn-primary" onclick="renderChildList();"><?php echo Yii::t('global','Refresh');?></button>
            <div class="pull-right dropdown">
                <button class="btn btn-default" data-toggle="dropdown"><?php echo Yii::t('teaching',
                        'Batch');?> <span class="caret"></span></button>
                <ul class="dropdown-menu">
                    <li><a href="javascript:;" onclick="setAll('online');"><?php echo Yii::t('teaching','Make All Online');?></a></li>
                    <li><a href="javascript:;" onclick="setAll('offline');"><?php echo Yii::t('teaching','Make All Offline');?></a></li>
                </ul>
            </div>
        </div>
        <div class="panel-body">
            <ul class="media-list" id="weekly-media-child-list">

            </ul>
        </div>
    </div>
</div>

<style>
    .img-item{background: #f2f2f2;border: 1px solid #d5d5d5;width: auto !important}
    .child-face{max-width: 40px;}
    .child-face2{width: 70px;}
    #weekly-media-child-list .media{padding-left: 22px; position: relative}
    #weekly-media-child-list h5.flag{position: absolute;top: 2px; left: 0px; font-size:16px;color: #f2f2f2}
    #weekly-media-child-list .item-pass span.badge{background: #008000}
    #weekly-media-child-list .item-nopass span.badge{background: #ff0000}
    #weekly-media-child-list .online h5.flag{color: #008000}
</style>

<script type="text/template" id="child-li-item-template">
    <li class="media class-child" childid=<%- id %>>
        <a class="pull-left" href="javascript:childPhoto(<%= id%>, <%= count%>);">
            <img class="media-object child-face img-thumbnail"
                 src="<?php echo Yii::app()->params['OAUploadBaseUrl']?>/childmgt/<%= photo %>" title="<%- name %>">
        </a>
        <div class="media-body">
            <h5 class="media-heading">
                <a href="<%= ccUrl%>" target="_blank" title="<?php echo Yii::t('teaching','Frontend Preview');?>">
                    <span class="glyphicon glyphicon-play"></span>
                </a>
                <a href="javascript:childPhoto(<%= id%>, <%= count%>);"><%- name %></a>
            </h5>
            <span title="<?php echo Yii::t('teaching','media count');?>" class="badge"><%= count%></span>
        </div>
        <h5 class="flag"><span class="glyphicon glyphicon-ok-sign"></span></h5>
    </li>
</script>

<script type="text/template" id="imgitem-action-tmpl">
    <div id="item-<%= id%>" class="img-thumbnail img-item pull-left mr10 mb10 p10">
        <div class="dropdown">
            <a class="dropdown-toggle" href="#" data-toggle="dropdown"><span class="glyphicon glyphicon-list"></span></a>
            <ul class="dropdown-menu" role="menu" aria-labelledby="dropdownMenu1">
                <li>
                    <a href="javascript:openAssign(<%= id%>);">
                        <span class="glyphicon glyphicon-tag"></span> <?php echo Yii::t('teaching','Make Tag');?></a>
                </li>
                <% if(server == 20){%>
                <li>
                    <a href="javascript:delMedia(<%= id%>, <%= classid%>);">
                    <span class="glyphicon glyphicon-remove"></span> <?php echo Yii::t('global','Delete');?></a>
                </li>
                <li>
                    <a href="javascript:rotate(<%= id%>, <%= classid%>);">
                    <span class="glyphicon glyphicon-retweet"></span> <?php echo Yii::t('global','Rotate');?></a>
                </li>
                <li>
                    <a href="javascript:setAvatar(<%= id%>)">
                    <span class="glyphicon glyphicon-user"></span> <?php echo Yii::t('teaching','Set Profile Photo');?></a>
                </li>
                <%}%>
            </ul>
        </div>
        <a class="img-warp" href="javascript:openAssign(<%= id%>);"><img src="<%= url%>"></a>
    </div>
</script>

<script type="text/template" id="videoitem-action-tmpl">
    <div id="item-<%= id%>" class="img-thumbnail img-item pull-left mr10 mb10 p10">
        <div class="dropdown">
            <a class="dropdown-toggle" href="#" data-toggle="dropdown"><span class="glyphicon glyphicon-list"></span></a>
            <ul class="dropdown-menu" role="menu" aria-labelledby="dropdownMenu1">
                <li><a href="javascript:play(<%= id%>);">
                    <span class="glyphicon glyphicon-play"></span> <?php echo Yii::t('global','Play Video');?></a></li>
                <li><a href="javascript:delMedia(<%= id%>, <%= classid%>);">
                    <span class="glyphicon glyphicon-remove"></span> <?php echo Yii::t('global','Delete');?></a></li>
                <li><a href="javascript:openAssign(<%= id%>);">
                    <span class="glyphicon glyphicon-tag"></span> <?php echo Yii::t('teaching','Make Tag');?></a></li>
            </ul>
        </div>
        <a href="javascript:openAssign(<%= id%>);"><img src="<%= url%>"></a>
    </div>
</script>

<!--设置孩子头像-->
<script type="text/template" id="avatar-child-item-tmpl">
    <div class="col-md-6 mb10">
        <div class="pull-left mr10">
            <img class="media-object child-face2 img-thumbnail"
                 src="<?php echo Yii::app()->params['OAUploadBaseUrl']?>/childmgt/<%- photo %>" title="<%- name %>">
        </div>

        <label class="pull-left">
            <span style="display: block;width: 108px;height:15px;overflow: hidden;"><%- name %></span>
            <input type="radio" value="<%= id %>" name="avatarChildId">
        </label>
    </div>
</script>
<!--分配周报告照片-->
<script type="text/template" id="tag-action-tmpl">
    <div class="row">
        <div class="col-md-3">
            <img title="<?php echo Yii::t('teaching','Click see large photo');?>"
                 src="<%= contentData.mediaData[id].url%>"
                 class="img-thumbnail clickable"
                 onclick="enlargeImage(this)">
            <input type="hidden" name="mediaid" value="<%= id%>">
            <hr>
            <input type="text" class="form-control"
                   name="TagFree" placeholder="<?php echo Yii::t('teaching','Free Tag');?>" value="<%- sf%>">
            <% _.each(ldomain, function(_data, _id){%>
            <div class="checkbox">
                <label>
                    <input type="checkbox" value="<%= _id%>"
                           name="TagLD[]" <% if( _.indexOf(sl, _id) != -1){ %>checked<%}%>><%= _data%>
                    </label>
            </div>
            <%});%>
        </div>
        <div class="col-md-9">
            <?php if($this->branchObj->group==10):?>
            <h5><?php echo Yii::t('teaching','Select a Month (Only for Special Notes)');?></h5>
            <div class="form-inline">
                <% _.each(months, function(_data){%>
                <div class="checkbox mr15">
                    <label><input type="checkbox" value="<%= _data%>"
                                  name="TagMonth[]" <% if( _.indexOf(sm, _data) != -1){
                                  %>checked<%}%>> <% print(monthLabels[_data]) %></label>
                </div>
                <%})%>
            </div>
            <hr>
            <?php endif;?>
            <div class="row J_check_wrap">
                <div>
                    <div class="col-md-6">
                        <div class="checkbox">
                            <label><input type="checkbox" class="J_check_all" data-checklist="J_check_c1" data-direction="y">
                            <?php echo Yii::t('teaching','Tag to all class students');?></label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="checkbox">
                            <label><input type="checkbox" class="J_check_all" data-checklist="J_check_c2"
                                          data-direction="y" name="allchild" value="1" id="allchild">
                                <?php echo Yii::t('teaching','Assign to all students weekly media');?></label>
                        </div>
                    </div>
                    <div class="clearfix"></div>
                </div>
                <div style="height: 410px;overflow-y: auto;">
                    <% _.each(childData, function(_data){%>
                    <div class="col-md-4 mb10">
                        <div class="pull-left mr10">
                            <img class="media-object child-face2 img-thumbnail" src="<?php echo Yii::app()->params['OAUploadBaseUrl']?>/childmgt/<%= _data.photo %>" title="<%= _data.name %>">
                        </div>
                        <div class="pull-left J_warp_tagweek">
                            <div style="width: 108px;height:15px;overflow: hidden;"><%= _data.name %></div>
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" value="<%= _data.id%>" name="TagChild[]"
                                           title="<?php echo Yii::t('teaching','Tag');?>"
                                           class="J_check" data-yid="J_check_c1" <% if( _.indexOf(sc, _data.id) != -1){ %>checked<%}%>>
                                           <?php echo Yii::t('teaching','Tag');?></label>
                            </div>
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" value="<%= _data.id%>" name="LinkChild[]"
                                              title="<?php echo Yii::t('teaching','Weekly media');?>"
                                              class="J_check" data-yid="J_check_c2" <% if( _.indexOf(clinks, _data.id) != -1){ %>checked<%}%>
                                              onclick="assignTag(this)">
                                              <?php echo Yii::t('teaching','Weekly media');?></label>
                            </div>
                        </div>
                    </div>
                    <%});%>
                </div>
            </div>
        </div>
    </div>

</script>

<script type="text/template" id="photo-item-tmpl">
    <div class="row" id="item_photo_<%= id%>">
        <div class="col-md-3">
            <img src="<%= url%>" class="img-thumbnail" onclick="showBigPhoto(<%= id%>, '<%= url%>')" title="<?php echo Yii::t('teaching','Click see large photo');?>">
            <input type="hidden" name="mediaid[<%= id%>]" value="<%= id%>">
        </div>
        <div class="col-md-9 J_cxt">
            <% if(allchild){%>
            <p><?php echo Yii::t('teaching','This photo is for whole class');?></p>
            <%}else{%>
            <div class="checkbox">
                <label><input type="checkbox" name="delphoto[<%= id%>]" value="<%= id%>" onchange="delPhoto(this);">
                    <?php echo Yii::t('teaching','Remove from weekly journal. (File will not be deleted.)');?>
                    </label>
            </div>
            <%}%>
            <div class="J_caption">
                <input type="text" class="form-control" name="caption[<%= id%>]" placeholder="<?php echo Yii::t('teaching', 'Input caption here');?>" value="<%- caption%>">
                <br/>
                <input type="text" class="form-control length_2 pull-left mr10" name="weight[<%= id%>]" value="<%= weight%>" placeholder="排序">
                <p class="form-control-static"><?php echo Yii::t('teaching','sort order, photos with small number displays first.');?></p>
            </div>
        </div>
    </div>
    <hr>
</script>

<div class="modal" id="assignPhoto">
    <div class="modal-dialog modal-lg">
        <?php echo CHtml::form('', 'post', array('class'=>'J_ajaxForm'));?>
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                <h4 class="modal-title"><?php echo Yii::t('teaching','Media Tagging & Weekly Media Assignment');?></h4>
            </div>
            <div class="modal-body">
                <div class="img-wrapper text-center p10">
                    <img src="" onclick="enlargeImage(this)" class="img-thumbnail clickable">
                </div>
                <div class="mainContent"></div>
            </div>
            <div class="modal-footer" style="line-height: 30px;">
                <button type="button" class="btn btn-default pull-right" data-dismiss="modal">
                    <?php echo Yii::t('global','Close');?></button>
                <button type="submit" class="btn btn-primary J_ajax_submit_btn pull-right mr10">
                    <?php echo Yii::t('global','Save');?></button>
            </div>
        </div><!-- /.modal-content -->
        <?php echo CHtml::endForm();?>
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->

<div class="modal" id="childPhoto">
    <div class="modal-dialog modal-lg">
        <?php echo CHtml::form('', 'post', array('class'=>'J_ajaxForm'));?>
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">
                    <span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                <h4 class="modal-title"></h4>
            </div>
            <div class="modal-body">
                <div class="mainContent">

                </div>
            </div>
            <div class="modal-footer" style="line-height: 30px;">
                <div class="pull-left">
                    <label>
                        <input type="checkbox" name="stat" value="1" id="stat"> <?php echo Yii::t('teaching','Make Online');?></label>
                </div>
                <button type="button" class="btn btn-default pull-right" data-dismiss="modal">
                    <?php echo Yii::t('global','Close');?></button>
                <button type="submit" class="btn btn-primary J_ajax_submit_btn pull-right mr10">
                    <?php echo Yii::t('global','Save');?></button>
            </div>
        </div><!-- /.modal-content -->
        <?php echo CHtml::endForm();?>
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->

<div class="modal" id="childSetAvatar">
    <div class="modal-dialog modal-lg">
        <?php echo CHtml::form($this->createUrl('setAvatar', array('classid'=>$this->selectedClassId)), 'post', array('class'=>'J_ajaxForm'));?>
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">
                    <span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                <h4 class="modal-title"><?php echo Yii::t('teaching','Set Profile Photo');?></h4>
            </div>
            <div class="modal-body">
                <div class="row mb15">
                    <div class="col-md-5">
                        <h5><?php echo Yii::t('teaching','Please adjust photo selection area to crop.');?></h5>
                        <div id="avatar-wrapper">
                            <img id="avatar-avatar" src="" />
                        </div>
                        <h5 class="text-primary"><?php echo Yii::t('teaching','Preview');?></h5>
                        <div class="avatar-preview img-preview-normal pull-left mr10"></div>
                        <div class="avatar-preview img-preview-thumb"></div>
                    </div>
                    <div class="col-md-7">
                        <h5><?php echo Yii::t('navs','Child List');?></h5>
                        <div id="avatar-chilid-list" class="row">

                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer" style="line-height: 30px;">
                <?php echo CHtml::hiddenField('avatar');?>
                <button type="button" class="btn btn-default pull-right" data-dismiss="modal">
                    <?php echo Yii::t('global','Close');?></button>
                <button type="submit" class="btn btn-primary J_ajax_submit_btn pull-right mr10">
                    <?php echo Yii::t('global','Save');?></button>
            </div>
        </div><!-- /.modal-content -->
        <?php echo CHtml::endForm();?>
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->

<div class="modal fade" id="playerModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content" id="player"></div>
    </div>
</div>

<style>
    #player .mejs-container, #player .mejs-container video{
        width: 900px !important;
    }
    .avatar-preview{
        overflow: hidden;
        text-align:center;
    }
    .img-preview-normal{
        height:200px;
        width: 200px;
    }
    .img-preview-thumb{
        height:120px;
        width: 120px;
    }
    img.clickable {
        cursor: pointer;
    }
</style>