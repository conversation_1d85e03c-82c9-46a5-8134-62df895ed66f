<div id="childinfo">
    <?php $this->widget('ext.childInfo.ChildInfo', array('childObj'=>$this->childObj))?>
</div>

<div class="h_a"><?php echo Yii::t('child', 'Finance');?></div>
<div class="table_c1">
<table width=100%>
	<colgroup>
		<col class="th" width=180>
		<col width=null>
	</colgroup>
	<tbody>
		<tr>
			<td class="level0">
				<?php
					$paymentMenu = array(
                        'invoice' => array(
                            'label' => Yii::t('child', 'Invoices'),
                            'url' => array('//child/index/finance', 't'=>'invoice'),
                        ),
                        'fapiao' => array(
                            'label' => Yii::t('child', 'Fapiao'),
                            'url' => array('//child/index/finance', 't'=>'fapiao'),
                        ),						
                        'pcredit' => array(
                            'label' => Yii::t('child', 'Personal Credit'),
                            'url' => array('//child/index/finance', 't'=>'credit'),
                        ),
                        'lunch' => array(
                            'label' => Yii::t('child', 'Lunch Cancellation'),
                            'url' => array('//child/index/finance', 't'=>'lunch'),
                        ),
                        'balance' => array(
                            'label' => Yii::t('child', 'Balance'),
                            'url' => array('//child/index/finance', 't'=>'balance'),
                        ),
                    );
                    if($this->childObj->school->type == 50){
                        unset($paymentMenu['lunch']);
                    }
					$this->widget('zii.widgets.CMenu', array(
						'items' => $paymentMenu,
                        'htmlOptions' => array('class'=>'subm'),
                        'activeCssClass' => 'current'                        
                        )
					);
				?>
			</td>
			<td>
                <div>
					<?php $this->renderPartial(sprintf("_finance_%s", $t), array("dataProvider"=>$dataProvider,"depositList"=>$depositList,"t"=>$t, 'siblings'=>$siblings, 'cMod'=>$cMod));?>
				</div>
			</td>
		</tr>
	</tbody>
</table>
</div>