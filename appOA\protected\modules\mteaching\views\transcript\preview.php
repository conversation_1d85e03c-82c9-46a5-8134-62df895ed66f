<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en" lang="en">
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<meta name="language" content="en" />
	<title><?php echo $data['data']['childName']?> - Transcript <?php echo date("Ymd");?>  </title>
  <style>
    .flex{
        display:flex
    }
    .flex1{
        flex:1
    }
    .color6{
        color:#666666;
    }
    .colorBlue{
      color:#12348A !important;
    }
    .fontFamily{
      font-family: "Avenir"
    }
    .fontFamily1{
      font-family: "Arial"
    }
    body,html{
      margin:0;
      padding:0;
      background:#efefef;
      font-size:14px;
      font-family: 'Arial',<PERSON><PERSON><PERSON>,'Trebuchet','Utopia';
    }
    @media print{
      body {
        page-break-after: auto;
        page-break-before: auto;
        page-break-inside: auto;
        margin:0;
        padding:0
      }
      @page {
        margin: 0;
        size: auto;
      }
      .printData{
          page-break-after: always;
          overflow:hidden;
          transform-origin: top left; 
          padding:40px !important;
          margin:0; 
          border:none !important
      }
      .grade{
          background-color: #12348A !important;
          padding:5px;
          -webkit-print-color-adjust: exact; 
      }
      .printBtn{
          display:none
      }
      .tableBto{
        -webkit-print-color-adjust: exact;
        background:rgba(18,52,138,0.1) !important;
      }
    }
    .printData{
      height:958px;
      width:1400px;   /* 1030 */
      border:1px solid #ddd;
      margin:0 auto;
      padding:20px;
      background:#fff;
      position: relative;
      overflow:hidden;
    }
    .logo{
      width:380px;
      left:20px;
      top:20px
    }
    .title{
      text-align:center
    }
    .stuInfo{
      padding-top:15px;
      font-size:15px
    }
    .name1{
      width:181px;
      display:inline-block;
      text-align:right;
      margin-right:10px
    }
    .name{
      width:170px;
      display:inline-block;
      text-align:right;
      margin-right:10px
    }
    .grade{
      background-color: #12348A;
      padding:5px;
      font-weight:600;
      border-bottom: 0.5px solid #333333;
      color:#fff
    }
    table{
      font-size:13px !important;
      border-collapse: collapse;
      border-spacing: 0;
      width:100%
    }
    table th{
      text-align:left;
    }
    th,td{
      padding:2px 4px;
      line-height:12px;
      vertical-align: top;
    }
    .sign{
      height:65px;
      margin:10px 0
    }
    .cis{
      width:100%;
      margin-top:12px
    }
    .title1{
      font-size:16px;
      font-weight:600;
      padding-bottom:8px
    }
    .title2{
      font-size: 22px;
      font-weight: 600;
      text-align: center;
      padding: 10px;
      height: 40px;
      line-height: 40px;
    }
    .text-center{
      text-align:center
    }
    .notes{
      border: 1px solid #333333;
      padding:10px;
      min-height:40px
    }
    .notestitle1{
      padding-bottom:4px
    }
    .pl-50{
      padding-left:50px;
    }
    .pr-50{
      padding-right:50px;
    }
    .mt-8{
      margin-top:8px
    }
    .text-right{
      text-align:right
    }
    .font16{
      font-size:30px
    }
    .font15{
      font-size:26px
    }
    .font600{
      font-weight:600;
    }
    .mt-10{
      margin-top:10px
    }
    .mt-15{
      margin-top:15px
    }
    .tableBto{
      text-align: center;
      background:rgba(18,52,138,0.1)
    }
    .printBtn{
      width:1440px;
      text-align:right;
      margin:20px auto
    }
    .printBtn button{
      display: inline-block;
      margin-bottom: 0;
      font-weight: 400;
      text-align: center;
      white-space: nowrap;
      vertical-align: middle;
      -ms-touch-action: manipulation;
      touch-action: manipulation;
      cursor: pointer;
      background-image: none;
      border: 1px solid transparent;
      padding: 6px 12px;
      font-size: 14px;
      line-height: 1.42857143;
      border-radius: 4px;
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
      user-select: none;
      color: #fff;
      background-color: #337ab7;
      border-color: #2e6da4;
      margin-left:20px
    }
    .mt-25{
      margin-top:153px
    }
    .mr-5{
      margin-right:10px
    }
    .pull-right{
      float: right;
      margin-right: 20px;
    }
    .p0 td{
      padding:6px
    }
    .font12{
      font-size:12px;
    }
    .pb5{
      padding-bottom:5px
    }
    .mt-5{
      margin-top:5px
    }
    .watermark-container  {
      position: relative;
      overflow: hidden;
    }
    .watermark {
      position: absolute;
      top: 0;
      left:0;
      right: 0;
      bottom: 0;
      z-index: 1;
      color: rgba(0, 0, 0, 0.05);
      font-size:26px;
      width: 280px;
      height:26px;
      text-align:right
    }
    .layout{
      table-layout: fixed;
    }
    .positionText{
      position: absolute;
      text-align: center;
      width: 100%;
      top: 50%;
      font-size: 50px;
      z-index: 9;
    }
</style>
</head>
<body>
        <?php if ($withToolBar): ?>
            <div class='printBtn'><button type="primary" size="medium" onclick="btnPrint()">打印  / 下载 PDF</button></div>
        <?php endif; ?>
        <div class='printData' id='printData'>
          <div class='flex'>
            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/ds_logo.png' ?>" alt="" class='logo'>
            <div class='title mt-10 flex1'>
              <div class='font16 font600 colorBlue fontFamily'>High School Transcript</div>
              <div class='colorBlue fontFamily mt-8 font14'>No.2, Shunbai Road, Chaoyang District, Beijing, China 100012</div>
              <div class='colorBlue fontFamily font14'>Issued: <?php echo $data['data']['issued']?></div>
            </div>
          </div>
            <div class='flex stuInfo'>
                <div class='flex1'>
                    <div><span class='name1 colorBlue fontFamily'>Student:</span><?php echo $data['data']['childName']?></div>
                    <div><span class='name1 colorBlue fontFamily'>Parent(s):</span><?php echo $data['data']['parentName']?></div>
                    <div><span class='name1 colorBlue fontFamily'>Enrollment Date:</span><?php echo $data['data']['startDate']?></div>
                    <?php if($data['data']['pursuing_old'] == 1): ?>
                      <div><span class='name1 colorBlue fontFamily'>Pursuing Full IB Diploma:</span><?php echo $data['data']['pursuing']==1 ? 'Yes' : 'No' ?></div>
                    <?php elseif($data['data']['pursuing'] == 1): ?>
                      <div><span class='name1 colorBlue fontFamily'><?php echo $data['data']['pursuing_title'] ?>:</span><?php echo $data['data']['pursuing_text']=='Blank' ? '' : $data['data']['pursuing_text'] ?></div>
                    <?php else: ?>
                      <div></div>
                    <?php endif;?>
                </div>
                <div class='flex1'>
                    <div><span class='name colorBlue fontFamily'>Gender:</span><?php echo $data['data']['childGender']?></div>
                    <div><span class='name colorBlue fontFamily'>Date of Birth:</span><?php echo $data['data']['childBirth']?></div>
                    <div><span class='name colorBlue fontFamily'><?php echo $data['data']['graduation']==1 ? 'Graduation' : 'Withdraw' ?> Date:  </span><?php echo $data['data']['endDate']=='' ?  $data['data']['end_str'] :  $data['data']['endDate'] ?></div>
                </div>
            </div>
            <div class="watermark-container mt-10" id='watermarked-container'>
              <div style='position: relative;'>
                
                <table id='course' class='layout'>
                  <tbody>
                    <tr>
                      <?php foreach ($data['data']['items'] as $key => $item){?>
                        <td style='padding:0;border: 1px solid #000;position: relative;z-index:999'>
                          <?php if($item['watermark_text']!='' && $item['watermark_color']!=''): ?>
                            <div class='positionText' style='color:<?php echo $item['watermark_color']?>'><?php echo $item['watermark_text']?></div>
                          <?php endif;?>
                          <div class='flex grade fontFamily'>
                              <div class='mr-5 flex1'><?php echo $item['classTitle']?></div>
                              <div><?php echo $item['yearTitle']?></div>
                          </div>
                          <table  style='position: relative;z-index:999'>
                            <thead class='fontFamily mb10'>
                                <th width='80%' class='pb5'>Course</th>
                                <th>Semester</th>
                                <th>Grade</th>
                                <th>Credit</th>
                            </thead>
                            <tbody class='font12'>
                                <?php foreach ($item['list'] as $_item){?>
                                <tr>
                                    <td><?php echo $_item['title']?></td>
                                    <?php if($_item['credit']=='' && $_item['customCredit']==''): ?>
                                      <td class='text-center' colspan='3'>In Progress</td>
                                    <?php else:?>
                                      <td class='text-center'><?php echo $_item['completion']?></td>
                                      <td class='text-center'><?php echo $_item['grade']?></td>
                                      <td class='text-center'><?php echo $_item['customCredit']=='' ? $_item['credit'] : $_item['customCredit'] ?></td>
                                    <?php endif;?>
                                </tr>
                                <?php }?>  
                            </tbody>
                            </tfoot>
                          </table>
                        </td>
                      <?php }?>
                    </tr>
                    <tr>
                      <?php foreach ($data['data']['items'] as $key => $item){?>
                        <td style='padding:0;border:1px solid #000' >
                        <div class='tableBto '>
                          <table>
                            <thead style='visibility: collapse;'>
                                  <th width='80%'>Course</th>
                                  <th>Semester</th>
                                  <th>Grade</th>
                                  <th>Credit</th>
                              </thead>
                              <tbody  class='p0' >
                                <tr >
                                  <td  colspan='2'>
                                  <span class='fontFamily colorBlue font600'>Year's G.P.A.:</span></td>
                                  <td colspan='2' style='border-left: 1px solid #000;color:#000' class='font600'><?php if ($item['gpa']!=0) echo $item['gpa']; ?></td>
                                </tr>
                              </tbody>
                          </table>
                        </div>
                        </td>
                      <?php }?>
                    </tr>
                  </tbody>
                </table>
              </div>
              <table style=' table-layout: fixed;'>
                <tr >
                      <td></td>
                      <td></td>
                      <td></td>
                      <td  style='padding:0;'>
                      <div class=' 'style='padding:0;margin-top:20px;<?php echo $key+1==count($data['data']['items']) ? 'border:1px solid #000' :'' ?>' >
                        <table>
                          <thead style='visibility: collapse;'>
                                <th width='80%'>Course</th>
                                <th>Semester</th>
                                <th>Grade</th>
                                <th>Credit</th>
                            </thead>
                            <tbody  class='p0'>
                              <?php if($key+1==count($data['data']['items'])): ?>
                                <tr  >
                                  <td colspan='2' style='padding:0'><div class='title2 colorBlue fontFamily'>Cumulative G.P.A.: </div></td>
                                  <td colspan='2' style='padding:0;color:#000'><div class='title2 fontFamily1' style='text-align:left'><?php echo $data['data']['gpa']?></div></td>
                                </tr>
                              <?php endif;?>
                            </tbody>
                        </table>
                      </div>
                      </td>
                  </tr>
              </table>
              <div class='notes' style='margin-top:-62px;width:45%'>
                  <span class='title1 notestitle1 colorBlue'>NOTES:</span>
                  <span class='fontFamily1 color3'> <?php echo nl2br($data['data']['notes']); ?></span>
              </div>
            </div>
            <div class='flex mt-15 fontFamily '>
                <div class='flex1 pr-50' >
                    <div class='colorBlue'>
                      <div class='title1 text-center'>Explanation of Marks</div>
                      <div class='mt-10'>Pass/Fail: Course taken on a Pass/No Credit basis, per school policy.</div>
                      <div class='mt-10'>Two semesters equal full credit. Grades are issued in January and June.</div>
                      <!-- <div class='mt-10'> Until 2022-2023 academic year, Chinese Moral and Legal Education was named Chinese Character Education, and Integrated Humanities was named Individuals and Societies.</div> -->
                      <div class='title1 mt-25 colorBlue'>Authorized Signature</div>
                      <div> <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/sign.png?v20241105' ?>" class="sign" ></div>
                      <div class='colorBlue'>Mr. Jonah Olken-Dann</div>
                      <div  class='colorBlue mt-6'>Secondary School Executive Principal</div>
                    </div>
                  </div>
                <div class='flex1 pl-50 colorBlue'>
                    <div>G.P.A. computations (following NASSP and AACRAO) include all courses taken for credit at Daystar Academy except pass grades. Courses taken at any other school are excluded. Grades are unweighted. Letter grades are not included in GPA caculations.</div>
                    <div class='title1 mt-8 colorBlue'>GPA Scale</div>
                    <div>7: Consistently attained an excellent level of achievement.</div>
                    <div>6: Attained a very good level of achievement.</div>
                    <div>5: Attained a good level of achievement.</div>
                    <div>4: Attained a satisfactory level of achievement.</div>
                    <div>3: Attained the minimum level of achievement.</div>
                    <div>2: Attained a limited level of achievement</div>
                    <div>1: Attained a very limited level of achievement.</div>
                    <div>NA: Not Yet Assessed.</div>
                    <div>NE: No Evidence - Insufficient evidence of learning exists to make a fair evaluation of student performance against expectations.</div>
                    <div class='' style='width:50%'>
                      <div><img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/cis.png' ?>"  class="cis" alt="" ></div>
                      <div class='colorBlue mt-5'>Accreditations & Authorizations</div>
                  </div>
                </div>
            </div>
        </div>
<script>
  console.log(<?php echo  CJSON::encode($data) ?>)
    function btnPrint(){
        window.print();
    }
    var items=<?php echo CJSON::encode($data['data']['items']); ?>;
    window.onload=function(){
      const container = document.getElementById('watermarked-container');
      const watermarkText = 'DAYSTAR ACADEMY';
      const watermark = document.createElement('div');
      watermark.className = 'watermark';
      watermark.textContent = watermarkText;
      const course = document.getElementById('course');
      let num=items.length==1 || items.length==2?4:items.length
      var len=container.offsetWidth/num
      for (let i = 0; i < 3; i++) {
        for (let j = 0; j < num; j++) {
          const newWatermark = watermark.cloneNode(true);
          if(i==2){
            if(num==4){
              newWatermark.style.transform = `translate(${j*len}px,${container.offsetHeight-30}px) rotate(-12deg)`;
            }else{
              newWatermark.style.transform = `translate(${j*len}px,${container.offsetHeight}px) rotate(-12deg)`;
            }
          }else if(i==1){
            newWatermark.style.transform = `translate(${j*len}px,${course.offsetHeight}px) rotate(-12deg)`;
          }else{
            newWatermark.style.transform = `translate(${j*len}px,100px) rotate(-12deg)`;
          }
          newWatermark.style.width = `${container.offsetWidth/num}px`
          container.appendChild(newWatermark);
        }
      }
    }
 
</script>
</body>
</html>

