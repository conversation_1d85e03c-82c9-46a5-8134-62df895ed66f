<?php
$this->renderPartial('header');
?>

<div class="h_a">学年</div>
<div class="prompt_text">
    <?php
    $this->beginWidget('CActiveForm', array(
        'id'=>'start-year-form',
        'method'=>'get',
        'action'=>$this->createUrl('/child/list/operations', array('category'=>'classes')),
    ));
    echo CHtml::label('选择学年：', 'yid');
    echo CHtml::dropDownList('yid', $yid, $schyears);
    $this->endWidget();
    ?>
</div>
<div class="mb10">
    <a href="<?php echo $this->createUrl("/child/list/operations", array("category"=>"classes", "act"=>"editClass"))?>" role="button" class="btn">
        <span class="add"></span>
        添加班级
    </a>
</div>
<?php
$this->widget('ext.ivyCGridView.IvyCGridView', array(
    'dataProvider'=>$dataProvider,
    'id'=>'class-list',
    'colgroups'=>array(
        array(
            "colwidth"=>array(200, 100, 100),
//            "htmlOptions"=>array("align"=>"left", "span"=>2)
        )
    ),
    'columns'=>array(
		'title',
        'capacity',
        'child_age',
        array(
            'class'=>'CButtonColumn',
            'template'=>'{update}&nbsp;&nbsp;{targetnumber}',
            'updateButtonImageUrl'=>false,
            'updateButtonUrl'=>'Yii::app()->controller->createUrl("/child/list/operations", array("category"=>"classes", "act"=>"editClass", "id"=>$data->classid))',
            'buttons'=>array(
                'targetnumber'=>array(
                    'label'=>'设置目标人数',
                    'url'=>'Yii::app()->controller->createUrl("/child/list/operations", array("category"=>"classes", "act"=>"targetNumber", "id"=>$data->classid))',
                    'options'=>array('class'=>'J_dialog'),
                ),
            ),
        ),
    ),
));
?>

<script type="text/javascript">
$('#yid').on('change', function(){
this.form.submit();
});
</script>