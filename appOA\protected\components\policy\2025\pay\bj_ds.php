<?php
if(!defined("IVY_POLICY"))
throw new CException('DO NOT USE THIS FILE DIRECTLY');

return array( 'policy'=>array(

	//是否老生老办法
	ENABLE_CONSTANT_TUITION => true,
    //FENTAN_FACTOR => DAILY,
    //校园多收费摸式
    MORE_FEE_BY_SCHOOL => true,

	//是否开放课外活动账单
	ENABLE_AFTERSCHOOLS => false,

    NEWFUNCTION => true,

	//图书馆工本费
	FEETYPE_LIBCARD => 10,

	//学期四个时间点；取前后两端
    TIME_POINTS   => array(202509,202602,202602,202606),

	// //年付开通哪些缴费类型
	  PAYGROUP_ANNUAL => array(
	  	ITEMS => array(
	  		FEETYPE_TUITION,
//	  		FEETYPE_LUNCH,
//	  		FEETYPE_SCHOOLBUS,
	  	),
	  	TUITION_CALC_BASE => BY_MONTH,
	  ),

	// //学期付开通哪些缴费类型
	  PAYGROUP_SEMESTER => array(
	  	ITEMS => array(
	  		FEETYPE_TUITION,
//	  		FEETYPE_LUNCH,
//	  		FEETYPE_SCHOOLBUS,
	  	),
	  	TUITION_CALC_BASE => BY_MONTH,
	  ),

	// //月份开通哪些缴费类型
	// PAYGROUP_MONTH => array(
	// 	ITEMS => array(
	// 		FEETYPE_TUITION,
	// 		FEETYPE_LUNCH,
	// 		FEETYPE_SCHOOLBUS,
	// 	),
	// 	TUITION_CALC_BASE => BY_MONTH,
	// ),

	//计算基准：BY_MONTH, 或者 BY_SETTING, 前者表示所有的学费都基于月费用计算，后者表示所有的学费都基于学期或学年
	TUITION_CALCULATION_BASE => array(
        'BY_TODDLER' =>array(
            BY_MONTH => array(
                //月收费金额
                AMOUNT => array(
                    FULL5D => 21800,
                    FULL5D1 => 19300,
                    // FULL5D2 => 21000,
                ),
                //收费月份，及其权重
                MONTH_FACTOR => array(
                    202508=>0,
                    202509=>1,
                    202510=>1,
                    202511=>1,
                    202512=>1,
                    202601=>1,
                    202602=>1,
                    202603=>1,
                    202604=>1,
                    202605=>1,
                    202606=>1,
                ),
                //折扣
                GLOBAL_DISCOUNT => array(
                    DISCOUNT_ANNUAL => '1:10:2026-06-01:round'
                ),
                LUNCH_PERDAY=> 972
            ),
        ),
        'BY_KINDERGARTEN' =>array(
            BY_MONTH => array(
                //月收费金额
                AMOUNT => array(
                    FULL5D => 21800,
                    FULL5D1 => 19300,
                    // FULL5D2 => 21000,
                ),
                //收费月份，及其权重
                MONTH_FACTOR => array(
                    202508=>0,
                    202509=>1,
                    202510=>1,
                    202511=>1,
                    202512=>1,
                    202601=>1,
                    202602=>1,
                    202603=>1,
                    202604=>1,
                    202605=>1,
                    202606=>1,
                ),
                //折扣
                GLOBAL_DISCOUNT => array(
                    DISCOUNT_ANNUAL => '1:10:2026-06-01:round'
                ),
                LUNCH_PERDAY=> 972
            ),
        ),
        'BY_GRADE' =>array(
            BY_MONTH => array(
                //月收费金额
                AMOUNT => array(
                    FULL5D => 23400,
                    FULL5D1 => 20000,
                    // FULL5D2 => 19200,
                ),
                //收费月份，及其权重
                MONTH_FACTOR => array(
                    202508=>0,
                    202509=>1,
                    202510=>1,
                    202511=>1,
                    202512=>1,
                    202601=>1,
                    202602=>1,
                    202603=>1,
                    202604=>1,
                    202605=>1,
                    202606=>1,
                ),
                //折扣
                GLOBAL_DISCOUNT => array(
                    DISCOUNT_ANNUAL => '1:10:2026-06-01:round'
                ),
                LUNCH_PERDAY=> 972
            ),
        ),
        'BY_MIDDLE' =>array(
            BY_MONTH => array(
                //月收费金额
                AMOUNT => array(
                    FULL5D => 25400,
                    FULL5D1 => 21100,
                    // FULL5D2 => 19600,
//                    NEWSTUDENT => 15000,
                ),
                //收费月份，及其权重
                MONTH_FACTOR => array(
                    202508=>0,
                    202509=>1,
                    202510=>1,
                    202511=>1,
                    202512=>1,
                    202601=>1,
                    202602=>1,
                    202603=>1,
                    202604=>1,
                    202605=>1,
                    202606=>1,
                ),
                //折扣
                GLOBAL_DISCOUNT => array(
                    DISCOUNT_ANNUAL => '1:10:2026-06-01:round'
                ),
                LUNCH_PERDAY=> 972
            ),
        ),
        'BY_HIGH' =>array(
            BY_MONTH => array(
                //月收费金额
                AMOUNT => array(
                    FULL5D => 26400,
                    FULL5D1 => 21600,
                    // FULL5D2 => 19600,
//                    NEWSTUDENT => 15000,
                ),
                //收费月份，及其权重
                MONTH_FACTOR => array(
                    202508=>0,
                    202509=>1,
                    202510=>1,
                    202511=>1,
                    202512=>1,
                    202601=>1,
                    202602=>1,
                    202603=>1,
                    202604=>1,
                    202605=>1,
                    202606=>1,
                ),
                //折扣
                GLOBAL_DISCOUNT => array(
                    DISCOUNT_ANNUAL => '1:10:2026-06-01:round'
                ),
                LUNCH_PERDAY=> 972
            ),
        ),
        'BY_HIGH_DP' =>array(
            BY_MONTH => array(
                //月收费金额
                AMOUNT => array(
                    FULL5D => 26900,
                    FULL5D1 => 22100,
                    // FULL5D2 => 19600,
//                    NEWSTUDENT => 15000,
                ),
                //收费月份，及其权重
                MONTH_FACTOR => array(
                    202508=>0,
                    202509=>1,
                    202510=>1,
                    202511=>1,
                    202512=>1,
                    202601=>1,
                    202602=>1,
                    202603=>1,
                    202604=>1,
                    202605=>1,
                    202606=>1,
                ),
                //折扣
                GLOBAL_DISCOUNT => array(
                    DISCOUNT_ANNUAL => '1:10:2026-06-01:round'
                ),
                LUNCH_PERDAY=> 972
            ),
        ),
        BY_SEMESTER1 => array(
//			ENDDATE => 1737648000 //2026-1-24
            ENDDATE => array(
                FULL5D => 1769702400,
                FULL5D1 => 1769702400,
//                LUNCH_PERDAY => 1735574400,
//                41845 => 1735574400,
//                41846 => 1735574400,
//                41847 => 1735574400,
//                41848 => 1735574400,
//                41849 => 1735574400,
//                41850 => 1735574400,
//                41851 => 1735574400,
//                41852 => 1735574400,
//                41853 => 1735574400,
//                41854 => 1735574400,
//                41855 => 1735574400,
//                41856 => 1735574400,
//                41857 => 1735574400,
//                41858 => 1735574400,
//                41859 => 1735574400,
//                41860 => 1735574400,
//                41861 => 1735574400,
//                41862 => 1735574400,
//                41863 => 1735574400,
            )
		),
	),

	//每餐费用
	//LUNCH_PERDAY => 30,

	//账单到期日和账单开始日之差
	DUEDAYS_OFFSET => 0,

));