<?php

/**
 * This is the model class for table "ivy_survey_template".
 *
 * The followings are the available columns in table 'ivy_survey_template':
 * @property integer $id
 * @property string $title_cn
 * @property string $title_en
 * @property string $subtitle_cn
 * @property string $subtitle_en
 * @property string $schoolid
 * @property integer $status
 * @property integer $update_user
 * @property integer $update_time
 */
class WSurveyTemplate extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @return SurveyTemplate the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_survey_template';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
				array('status, update_user, update_time', 'numerical', 'integerOnly'=>true),
				array('title_cn, title_en, subtitle_cn, subtitle_en, schoolid', 'length', 'max'=>255),
				// The following rule is used by search().
				// Please remove those attributes that should not be searched.
				array('id, title_cn, title_en, subtitle_cn, subtitle_en, schoolid, status, update_user, update_time', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}


	/**
	 * 得到模板名称
	 * @param int $id
	 * @return multitype:
	 * <AUTHOR>
	 * @time 2012-6-27
	 */
	public function getTemplateTitle($template_id = null,$subtitle = false) {

		$template_info = $this->getTemplateList($template_id);
		$template_list = null;
		if (!empty($template_info)) {
			$tk = new HtoolKits();
			if(true == $subtitle){
				foreach ($template_info as $template) {
					//if(empty($template->subtitle_cn) && empty($template->subtitle_en)){
					//	$template_list[$template->id] = $tk->getContentByLang($template->title_cn, $template->title_en);
					//}else{
					$template_list[$template->id] = $tk->getContentByLang($template->subtitle_cn, $template->subtitle_en);
					//}
				}
			}else{
				foreach ($template_info as $template) {
					$template_list[$template->id] = $tk->getContentByLang($template->title_cn, $template->title_en);
				}
			}
		}
		return $template_list;
	}

	/**
	 * 得到模板列表
	 * @param int $id
	 * @return multitype:
	 * <AUTHOR>
	 * @time 2012-6-27
	 */
	public function getTemplateList($template_id = null) {
		$st_cri = new CDbCriteria();
		$st_cri->compare('status', 0);
		$st_cri->compare('id', $template_id);
		$st_cri->order = 'id DESC';
		return $this->findAll($st_cri);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
				'id' => 'ID',
				'title_cn' => 'Title Cn',
				'title_en' => 'Title En',
				'subtitle_cn' => 'Subtitle Cn',
				'subtitle_en' => 'Subtitle En',
				'schoolid' => 'Schoolid',
				'status' => 'Status',
				'update_user' => 'Update User',
				'update_time' => 'Update Time',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('title_cn',$this->title_cn,true);
		$criteria->compare('title_en',$this->title_en,true);
		$criteria->compare('subtitle_cn',$this->subtitle_cn,true);
		$criteria->compare('subtitle_en',$this->subtitle_en,true);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('status',$this->status);
		$criteria->compare('update_user',$this->update_user);
		$criteria->compare('update_time',$this->update_time);

		return new CActiveDataProvider($this, array(
				'criteria'=>$criteria,
		));
	}

    public function getTitle()
    {
        return Yii::app()->language == "zh_cn" ? $this->title_cn : $this->title_en;
    }

    public function getSubtitle()
    {
        return Yii::app()->language == "zh_cn" ? $this->subtitle_cn : $this->subtitle_en;
    }
}