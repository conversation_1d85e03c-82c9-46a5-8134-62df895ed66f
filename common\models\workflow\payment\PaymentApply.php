<?php

/**
 * This is the model class for table "ivy_workflow_payment".
 *
 * The followings are the available columns in table 'ivy_workflow_payment':
 * @property integer $id
 * @property integer $startyear
 * @property string  $school_id
 * @property integer $budget
 * @property integer $price_total
 * @property integer $state
 * @property integer $created_by
 * @property integer $created_at
 * @property integer $updated_by
 * @property integer $updated_at
 */
class PaymentApply extends CActiveRecord
{
	const CANCEL = -2;
	const REFUSE = -1;
	const INPROGRESS = 0;
	const AGREE = 1;

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_workflow_payment';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('created_by, created_at, updated_by, updated_at, school_id', 'required'),
			array('title, bank_user, bank_account, bank_name, school_id, payment_note', 'length', 'max'=>255),
			array('startyear, budget, price_total, state, created_by, created_at, updated_by, updated_at, payment_user, payment_time', 'numerical', 'integerOnly' => true),
			array('school_id, add_note', 'safe'),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, startyear, school_id, budget, price_total, state, created_by, created_at, updated_by, updated_at', 'safe', 'on' => 'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array();
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'startyear' => 'Startyear',
			'school_id' => 'School',
			'budget' => 'Budget',
			'price_total' => 'Price Total',
			'state' => 'State',
			'payment_user' => 'Payment User',
			'payment_time' => 'Payment Time',
			'payment_note' => 'Payment Note',
			'add_note' => 'Add Note',
			'created_by' => 'Created By',
			'created_at' => 'Created At',
			'updated_by' => 'Updated By',
			'updated_at' => 'Updated At',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria = new CDbCriteria;

		$criteria->compare('id', $this->id);
		$criteria->compare('startyear', $this->startyear);
		$criteria->compare('school_id', $this->school_id, true);
		$criteria->compare('budget', $this->budget);
		$criteria->compare('price_total', $this->price_total);
		$criteria->compare('state', $this->state);
		$criteria->compare('created_by', $this->created_by);
		$criteria->compare('created_at', $this->created_at);
		$criteria->compare('updated_by', $this->updated_by);
		$criteria->compare('updated_at', $this->updated_at);

		return new CActiveDataProvider($this, array(
			'criteria' => $criteria,
		));
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return PaymentApply the static model class
	 */
	public static function model($className = __CLASS__)
	{
		return parent::model($className);
	}

	public static function categoryList($flag)
	{
		$flagArray = array(
			'school' => array(
				'224113', '224114', '64010103'
			),
			'hr' => array(
				'1601', '1701', '64010103'
			),
			'edu' => array(
				'64010101', '6602'
			),
		);
		return isset($flagArray[$flag]) ? $flagArray[$flag] : array();
	}

	public static function allCategoryList()
	{
		return array(
			'123103' => array(
				'title' => '借款',
				'items' => array(),
			),
			'1601' => array(
				'title' => '固定资产CAPEX',
				'items' => array(
					array('code' => '160101', 'title' => 'Renovation装修'),
					array('code' => '160102', 'title' => 'Furniture办公家具'),
					array('code' => '160103', 'title' => 'Equipment电子设备'),
					array('code' => '160105', 'title' => 'Books图书'),
					array('code' => '160106', 'title' => 'Toys玩具'),
				),
			),
			'1701' => array(
				'title' => '无形资产CAPEX-Software',
				'items' => array(),
			),
			'224102' => array(
				'title' => '退费 Refunds',
				'items' => array(),
			),
			'224103' => array(
				'title' => 'SY 党建专款',
				'items' => array(),
			),
			'22411001' => array(
				'title' => '教育专项基金',
				'items' => array(),
			),
			'224113' => array(
				'title' => '校餐Lunch',
				'items' => array(
					array('code' => '2241130201', 'title' => 'School meal-Lunch cost餐费成本/午餐'),
					array('code' => '2241130202', 'title' => 'School meal-snack cost餐费成本/间点'),
					array('code' => '2241130301', 'title' => 'SchoolMealKitchenEnergyConsumption'),
					array('code' => '2241130302', 'title' => 'School meal-kitchen labor餐费成本/人员成本'),
					array('code' => '2241130303', 'title' => 'School meal-equipment餐费成本/厨房成本'),
				),
			),
			'224114' => array(
				'title' => '校车',
				'items' => array(
					array('code' => '2241140201', 'title' => '停车/过路Parking/ETC'),
					array('code' => '2241140202', 'title' => '油费Fuel'),
					array('code' => '2241140203', 'title' => '司机成本Driver'),
					array('code' => '**********', 'title' => 'School bus-bus rental租赁费'),
					array('code' => '**********', 'title' => '乘客保险Insurance'),
					array('code' => '**********', 'title' => '校车装饰Bus Other'),
				),
			),
			'64010101' => array(
				'title' => '教育教学 Material and Overhead',
				'items' => array(
					array('code' => '**********', 'title' => 'Books and Magzaines图书资料'),
					array('code' => '**********', 'title' => 'Health care医疗费用'),
					array('code' => '**********', 'title' => 'Classroom Supplies& Teaching Materials班级用品'),
					array('code' => '**********', 'title' => 'Field Trip园外活动'),
					array('code' => '**********', 'title' => 'School Event (MCE only)校园活动'),
					array('code' => '**********', 'title' => 'Toys玩教具'),
					array('code' => '**********', 'title' => 'Director discretionary expenses园长机动'),
					array('code' => '**********', 'title' => 'Daily necessities生活用品'),
					array('code' => '**********', 'title' => 'Stationery办公用品'),
					array('code' => '**********', 'title' => 'Liability insurance责任保险'),
					array('code' => '**********', 'title' => '服务费Service fee'),
					array('code' => '**********', 'title' => 'School beautification校园美化'),
					array('code' => '**********', 'title' => 'Medical fees医疗支出'),
				),
			),
			'64010102' => array(
				'title' => '设备费用Facilities Cost',
				'items' => array(
                    array('code' => '**********', 'title' => 'Rents房租'),
                    array('code' => '**********', 'title' => 'Utilities取暖、水电能耗'),
                    array('code' => '**********', 'title' => 'Armark物业费'),
                    array('code' => '**********', 'title' => 'Repairs and Maintenance维修及保养费用'),
                ),
			),
			'64010103' => array(
				'title' => '人事Persenal Cost',
				'items' => array(
					array('code' => '**********', 'title' => '社保Social Insurance (Company)'),
					array('code' => '221123', 'title' => '住房公积金 Housing Prouident Fund'),
					array('code' => '**********', 'title' => '服务费 Service Fee'),
					array('code' => '**********', 'title' => '外籍保险Medical Insurance'),
					array('code' => '**********', 'title' => '招聘Recruitment'),
					array('code' => '**********', 'title' => '招聘Recruitment'),
					array('code' => '************', 'title' => '中介服务费（其他）Service Fee Other'),
					array('code' => '**********', 'title' => '签证Visa'),
					array('code' => '**********', 'title' => '外籍机票Flight Stipend'),
					array('code' => '**********', 'title' => '员工体检Physical Check'),
					array('code' => '**********', 'title' => '员工PD'),
					array('code' => '**********', 'title' => '福利费Staff Welfare'),
					array('code' => '**********', 'title' => '餐费Teachers Meal'),
					array('code' => '**********', 'title' => '培训费Trainning'),
					array('code' => '**********', 'title' => '工资及奖金Salary and Bonus'),
					array('code' => '66020314', 'title' => '工会经费 Labour-Union'),
					array('code' => '66020315', 'title' => '残疾人就业保障金 Residual payments'),
				),
			),
			'64010104' => array(
				'title' => '市场MCE',
				'items' => array(
					array('code' => '640101040101', 'title' => '广告宣传\数字营销Advertising\branding\video\wechat'),
					array('code' => '640101040102', 'title' => '广告宣传\创意服务/品牌/Website'),
					array('code' => '640101040104', 'title' => '广告宣传\活动Events\翻译Translation'),
					array('code' => '640101040105', 'title' => '广告宣传\社区及招生Recruiting Commission'),
					array('code' => '6401010403', 'title' => '单册及小礼品Brochures\Gifts\Open house Printing'),
				),
			),
			'6602' => array(
				'title' => '管理费用Total G&A',
				'items' => array(
					array('code' => '66020101', 'title' => 'Transportation市内交通'),
					array('code' => '66020102', 'title' => 'Information&Telecommunication信息/通讯'),
					array('code' => '66020103', 'title' => 'Courier and Postage快递邮寄'),
					array('code' => '66020104', 'title' => 'Daily Supplies生活用品'),
					array('code' => '66020105', 'title' => 'Working Meal工作餐饮'),
					array('code' => '66020106', 'title' => 'License&Local Fees证照及政府费用'),
					array('code' => '66020107', 'title' => 'Audit Fees审计/会计服务'),
					array('code' => '66020108', 'title' => 'Office Supplies办公用品'),
					array('code' => '66020109', 'title' => '财产保险Property insurance'),
					array('code' => '66020110', 'title' => 'Travel差旅费'),
					array('code' => '66020111', 'title' => 'Conference会务费'),
					array('code' => '66020112', 'title' => 'Tech Service技术服务费'),
					array('code' => '66020116/6801', 'title' => '其他税费tax and surcharge'),
					array('code' => '66020117', 'title' => 'Contract Tax印花税'),
					array('code' => '66020119', 'title' => 'Consulting Service法律及专业咨询服务费'),
					array('code' => '66020201', 'title' => 'Rents房租'),
					array('code' => '********', 'title' => 'Utilities取暖、水电能耗'),
					array('code' => '********', 'title' => 'Armark物业费'),
					array('code' => '********', 'title' => 'Repairs and Maintenance维修及保养费用'),
				),
			),
			'6603' => array(
				'title' => '财务费用Finace Expenses',
				'items' => array(),
			),
			'224120' => array(
				'title' => '其他Others',
				'items' => array(
					array('code' => '2241201', 'title' => '往来款-应付Account current-Payable'),
					array('code' => '2241202', 'title' => '其他应收款-个人Other Receivables-Employees'),
					array('code' => '2241203', 'title' => '其他Others'),
				),
			),
			'2221' => array(
				'title' => '应交税费Tax Payable',
				'items' => array(
					array('code' => '222102', 'title' => '应交城建税及教育费附加 Urban maintenance and construction tax and Education surcharges Payable'),
					array('code' => '222103', 'title' => '应交企业所得税 Income tax Payable'),
					array('code' => '222104', 'title' => '应交印花税 Stamp tax Payable'),
					array('code' => '222105', 'title' => '应交房产税及土地使用税 Housing Property tax and Land use tax payable'),
					array('code' => '222107', 'title' => '应交增值税  Value added tax Payable'),
					array('code' => '222120', 'title' => '应交个人所得税 Individual income tax Payable'),
				),
			),
		);
	}
}
