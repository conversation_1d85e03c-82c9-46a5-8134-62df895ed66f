<?php
$_yids = $this->getCalendars();
Yii::import('common.models.visit.*');

$class_list_new = AdmissionsDs::getConfig();
$class_one = array("0"=>Yii::t('user','New Enrollment - No Current Class Assignment'), "-1"=> Yii::t('user','Previously Enrolled - No Current Class Assignment'));
$class_list = $class_one + $class_list_new['grade'] + $class_list_new['grade_ayalt'] + $class_list_new['grade_slt'];
$currentYearClassIds = array_keys($class_list);//班级ID

$_classes = IvyClass::getClassList($this->branchId, $_yids);
$classes = array();
$classIds = array();
foreach($_classes as $_class){
    $classes[$_class->yid][$_class->classid] = array('id'=>$_class->classid,'title'=>$_class->title,'age'=>$_class->child_age);
    $classIds[$_class->classid] = $_class->classid;
}
foreach(array('currentYid','nextYid') as $_m){
    $classItems[$_m] = ($_m == 'currentYid') ? Yii::t('site','This school year class placement') : Yii::t('site','Next school year class placement');
    if (is_array($classes) && count($classes)){
        if(count($classes[$_yids[$_m]])){
            foreach($classes[$_yids[$_m]] as $class){
                $classItems[$_m.'-'.$class['id']] = ' -- ' .$class['title'];
            }
        }
    }
}
$classItems['setStatus'] = Yii::t('site', 'Status Setting');
$classItems['setGraduated'] = ' -- ' . Yii::t('site', 'Set to graduated');
$classItems['setDroppedout'] = ' -- '. Yii::t('site', 'Set to dropout');
$nextyid = isset($_yids['nextYid']) ? $_yids['nextYid'] : 0;
// $childData = $this->getStudentsWithNextYearInfo($this->branchId, NULL, $nextyid, '<100');
$childData = $this->getNewregStudents();
?>

<script>
    var isCurrent = <?php echo ($category=='current')? 1: 0;?>;
    var nextYid = <?php echo $nextyid; ?>;
</script>

<div class="col-md-10">
    <div class="row mb15">
        <div class="col-md-12">
            <h1 class="flex align-items font24  mt20">
                <span class="dropdown text-primary">
                    <a data-toggle=dropdown href="#"><span class="glyphicon glyphicon-list glyphiconList"></span></a>
                    <ul class="dropdown-menu">
                        <li>
                            <a onclick="exportChildInfo(this)" href="javascript:void(0)">
                                <span class="glyphicon glyphicon-export"></span> <?php echo Yii::t('site', 'Export Name List');?>
                            </a>
                        </li>
                        <li>
                            <a onclick="parentAccount(this)" href="javascript:void(0)">
                                <span class="glyphicon glyphicon-user"></span> <?php echo Yii::t('site', 'Parent Account'); ?>
                            </a>
                        </li>
                    </ul>
                </span>
                <span class='fontBold ml12'><?php echo Yii::t('site','Newly Registered');?> </span>
                <div class='flex align_end'>
                    <span class="label label-default ml8" id="total-number"></span>
                    <small class='ml8 font12'><?php echo Yii::t('user','No class placement in current year. (outdated data included)');?></small> 
                </div>
            </h1>
        </div>
    </div>



    <?php foreach($class_list as $k => $v){ ?>
    <div class="row">
        <div class="col-md-12 col-wrapper">
            <form class="form-inline J_ajaxForm" role="form" method="post" id="form-inline-<?php echo $k;?>">
                <div class="class-item" age="0">
                    <div class="panel panel-danger">
                        <div class="panel-heading">
                            <label class="checkbox-inline">
                                <?php echo CHtml::checkBox('classid_'.$k, false, array('data-checklist'=>'J_check_c1_'.$k, 'class'=>'J_check_all', 'data-direction'=>'y'));?>
                                <span class="text"><?php echo Yii::t('global','Select All');?></span>
                                <span class="badge"></span>
                            </label>
                            <!-- <div class="checkbox">
                                <label>
                                    <?php echo CHtml::checkBox('classid_'.$k, false, array('data-checklist'=>'J_check_c1_'.$k, 'class'=>'J_check_all', 'data-direction'=>'y'));?>
                                    <span class="text">全选</span>
                                </label>
                                <span class="badge"></span>
                            </div> -->
                            <span class="pull-right mt4"><?php echo $v?></span>
                        </div>
                        <div class="panel-body J_check_wrap" id="class-box-<?php echo $k; ?>">

                        </div>
                        <div class="panel-footer">
                            <?php
                            echo CHtml::dropDownList('AssignForm[classid]',null,$classItems,array(
                                'class'=>'form-control select_4 mb5',
                                'empty'=>Yii::t('site','Class Placement & Status Setting'),
                                'options'=>array(
                                    'currentYid'=>array('disabled'=>true),
                                    'nextYid'=>array('disabled'=>true),
                                    'setStatus'=>array('disabled'=>true)
                                )));
                            ?>
                            <button type="submit" class="btn btn-default J_ajax_submit_btn mb5"><?php echo Yii::t('global','Submit');?></button>
                            <div class="pull-right" style="line-height: 30px;">
                                <span class="mr5" id="tboy" style="display: none;">
                                    <small><a href="javascript:filteByAttr('gender', 1);"><?php echo Yii::t('child','Boy');?>（<span></span>）</a></small>
                                </span>
                                <span class="mr5" id="tgirl" style="display: none;">
                                    <small><a href="javascript:filteByAttr('gender', 2);"><?php echo Yii::t('child','Girl');?>（<span></span>）</a></small>
                                </span>
                                <span class="mr5" id="tchina" style="display: none;">
                                    <small><a href="javascript:filteByAttr('country', 36);"><?php echo Yii::t('child','CN Mainland');?>（<span></span>）</a></small>
                                </span>
                                <span class="mr5" id="thmt" style="display: none;">
                                    <small><a href="javascript:filteByAttr('country', 175);"><?php echo Yii::t('child','CN Other');?>（<span></span>）</a></small>
                                </span>
                                <span class="mr5" id="tforeign" style="display: none;">
                                    <small><a href="javascript:filteByAttr('country', 'other');"><?php echo Yii::t('child','Foreigner');?>（<span></span>）</a></small>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Parent Contact Modal -->
    <div class="modal" id="contactModal" tabindex="-1" role="dialog" aria-labelledby="contactModalLabel"
        aria-hidden="true">
    <div class="modal-dialog  modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title" id="contactModalLabelTitle"></h4>
            </div>

            <div class="modal-body">
                <div class="row">
                    <div class="col-md-12">
                        <div class="table-responsive">
                            <table id="parent-info" class="table">
                                <thead>
                                <tr>
                                    <th class="col-md-4"><?php echo Yii::t('site', 'Childern'); ?></th>
                                    <th class="col-md-4"><?php echo Yii::t('site', 'Father'); ?></th>
                                    <th class="col-md-4"><?php echo Yii::t('site', 'Mother'); ?></th>
                                </tr>
                                </thead>
                                <tbody></tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        </div>
    </div>
<?php } ?>

</div>

<script type="text/template" id="child-item-template">
    <div class="col-md-4 col-sm-4 col-xs-6">
        <div class="checkbox" style='height:45px'>
            <div>            
                <label title="注册：<%= created%>" id="child_<%= id%>" country="<%= country%>" gender="<%= gender%>">
                    <input data-yid="J_check_c1_<%=classid%>" class="J_check" value="<%=id%>"  type="checkbox" name="AssignForm[childid][]" style='float: left;margin-top: 3px;margin-right: 5px;'> 
                    <%= showGender(gender)%>
                    <span> <%= name %></span>  
                    <span><%= age%></span>
                </label>
                <a target="_blank" href="<% print(childBaseUrl+'&childid='+id); %>"><span class="glyphicon glyphicon-link"></span></a>
            </div>
            <% if(nextClassId>0 && !_.isUndefined(classList[nextYid][nextClassId])){ %>
                <span class="text-danger ml20"><% if(nextClassId>0 && !_.isUndefined(classList[nextYid][nextClassId])) print(classList[nextYid][nextClassId]['title']);%></span>
            <% }else{ %>
                <span class="ml20" style='background:#ff8a35;color:#fff;padding:0 2px;display:inline-block'>No Class</span>
            <% }%>
            <small class="text-success" id="childid-<%=id%>"></small>
        </div>
    </div>
</script>

<script>
    var childData = <?php echo CJSON::encode($childData); ?>;
    var classList = <?php echo CJSON::encode($classes); ?>;
    var studentCount = {};
    var showStats;
    var total=0;
    var template = _.template($('#child-item-template').html());
    var totalBoy=0, totalGirl=0, totalChina=0, totalHMT=0, totalForeign=0;
    var parentAccount;
    var showParents;
    var parents = {};
    $(function(){
        $('.class-item').hide();
        showStats = function(){
            var boxlist = $('.class-item');
            for(var i=0; i<boxlist.length; i++){
                var count = $(boxlist[i]).find('div.panel-body div.checkbox').length;
                total += count;
                $(boxlist[i]).find('.panel-heading span.badge').html(count);
            }
            $('#total-number').html(total);
        }
        if(!_.isNull(childData)){
            for(var i=0; i<childData.length; i++){
                if(childData[i]['classid'] != 0){
                    var box = $('#class-box-'+childData[i]['classid']);
                }else{
                    var box = $('#class-box-0');
                }
                var view = template(childData[i]);
                box.parents('.class-item').show();
                box.append(view);

                if (childData[i].gender == 1){
                    totalBoy += 1;
                }
                else {
                    totalGirl += 1;
                }
                if (childData[i].country == 36){
                    totalChina += 1;
                }
                else if (childData[i].country == 175) {
                    totalHMT += 1;
                }
                else {
                    totalForeign += 1;
                }
            }

            showStats();
//            head.Util.ajaxForm();
            head.Util.checkAll();
        }

        if (totalBoy)
            $('#tboy').show().find('span').text(totalBoy);
        if (totalGirl)
            $('#tgirl').show().find('span').text(totalGirl);
        if (totalChina)
            $('#tchina').show().find('span').text(totalChina);
        if (totalHMT)
            $('#thmt').show().find('span').text(totalHMT);
        if (totalForeign)
            $('#tforeign').show().find('span').text(totalForeign);

    });

    function setReturnStatus(data){
        if (!_.isNull(data)){
            $(data).each(function(index) {
                $('#childid-'+data[index].childid).html(data[index].title);
                $('#childid-'+data[index].childid).parent().find('input').removeAttr('checked');
                if (data[index].disable){
                    $('#childid-'+data[index].childid).parent().find('input').attr('disabled','disabled');
                }
            })
        }
    }

    // 导出孩子列表
    exportChildInfo = function (obj) {
        var url = "<?php echo $this->createUrl('//mcampus/student/exportChildInfo', array('category' => $category)); ?>";
        $.ajax({
          url: url,
          type: 'POST',
          // dataType: 'json',
          data: {},
          success: function (res) {
            res = eval("("+res+")");
            if (res.state == 'success') {
                var data = res.data.items;
                const filename = res.data.title;
                const ws_name = "SheetJS";

                const worksheet = XLSX.utils.aoa_to_sheet(data);
                const workbook = XLSX.utils.book_new();
                XLSX.utils.book_append_sheet(workbook, worksheet, ws_name);
                // generate Blob
                const wbout = XLSX.write(workbook, {bookType: 'xlsx', type: 'array'});
                const blob = new Blob([wbout], {type: 'application/octet-stream'});
                // save file
                let link = document.createElement('a');
                link.href = URL.createObjectURL(blob);
                link.download = filename;
                link.click();
                setTimeout(function() {
                    // 延时释放掉obj
                    URL.revokeObjectURL(link.href);
                    link.remove();
                }, 500);
            }
          },
          error: function(XMLHttpRequest, textStatus, errorThrown){  
            // alert(XMLHttpRequest.readyState + XMLHttpRequest.status + XMLHttpRequest.responseText);
          },
        }).done(function ( data, textStatus, jqXHR) {
            // console.log(jqXHR.status);
        });
    }

    function showGender(gender)
    {
        var genderImg = (gender==1)?'https://mega.ivymik.cn/icon-boy01.png':'https://mega.ivymik.cn/icon-girl01.png';
        return '<img src="'+genderImg+'" style="width: 16px;height:16px;margin-top:-4px">';
    }

    function filteByAttr (type, data) {
        var _items = $('#class-box-0 .checkbox>label');
        _items.removeClass('highlight');
        for(var i in childData) {
            if(type == 'country' && data == 'other') {
                $('#class-box-0 label[country!=36][country!=175]').addClass('highlight');
            }
            else {
                $('#class-box-0 label[' + type + '|=' + data + ']').addClass('highlight');
            }
        }
    };

    // 家长账户
    parentAccount = function (obj) {
        var childIds = [];
        _.each(childData, function(item, i) {
            childIds.push(item.id);
        });
        var postData = {};
        postData.childIds = encodeURI(childIds.join())
        postData.rangeAll = 0;
        $.ajax({
            url: "<?php echo $this->createUrl('//mcampus/student/getParentAccount');?>",
            type: 'POST',
            dataType: 'json',
            async: false,
            data: postData
        }).done(function (data) {
            if (data.state == 'success') {
                var cids = _.keys(data.data);
                for (var m = 0; m < cids.length; m++) {
                    parents[cids[m]] = data.data[cids[m]];
                }
            } else {
                alert(data.message);
            }
        });

        showParents(obj, childIds);
        $("#contactModal").modal();
    };
    var show = "<?php echo Yii::app()->user->checkAccess('ivystaff_it') ? 1 : 0; ?>"
    var accountName = {mmx: 'ASA', ivy: 'IVY', ds: 'DSO'};
    showParents = function (obj, childIds) {
        $('#parent-info tbody').empty();
        if ($('#parent-info tbody').html() == '') {
            var thdr = $('<tr></tr>').append('');
            $('#parent-info tbody').append(thdr);
        }

        $('.modal-title').html($(obj).parents('.panel-heading').find('span.text').html());
        $('.modal-title .print-link').show();
        var tr = '';
        $.each(childIds, function (_index, _item) {
            var _childid = _item;
            var child = parents[_childid];
            var father = parents[_childid]['father'];
            var mother = parents[_childid]['mother'];
            tr += '<tr>';
            tr += '<td class="text-center">' + child['photo'];
            tr += '<p>' + child['name'] + '</p></td>';
            if (father['email']) {
                tr += '<td><p class="color6"><span class="glyphicon glyphicon-user mr5"> </span> ' + father['name'] + '</p>';
                tr += '<p class="color6"><span class="glyphicon glyphicon-phone mr5"> </span> ' + father['mphone'] + '</p>';
                tr += '<p class="color6"><span class="glyphicon glyphicon-envelope mr5"> </span> ' + father['email'] + '</p>';
                tr += '<p class="color6"><span class="glyphicon glyphicon-lock mr5"> </span> ' + father['passwordText'] + father['passChanged'] + '</p>';
                tr += '<p class="textBlue" onclick="showWechat(this)"><img src="/themes/base/images/wechat.png" class="mr2" /> ' + father['asd'];
                if(father.fWechat.length!=0){
                    tr += '<span class="glyphicon glyphicon-chevron-down ml5 text-primary up" ></span><span class="glyphicon glyphicon-chevron-up down ml5 text-primary" ></span></p>';
                    tr += '<div class="wechat mb20">'
                    for(var key in father.fWechat){
                        tr += '<div class="relative pb20"><p class="flex"><span class="color6 flex1">'+accountName[key]+' 已绑定微信</span> <span>'
                        if(show==1){
                            tr += '<span class="glyphicon glyphicon-qrcode font20" onclick="showQrcode(this)" data-key='+key+' data-id='+father.pid+'></span>'
                        }
                        tr += '</span></p><div class="qrcode"></div>'
                        for(var i=0;i<father.fWechat[key].length;i++){
                            tr += '<div class="flex mt10"><span class="imgParent"><img src=' + father.fWechat[key][i]['headimgurl']+' /></span><span class="flex1 color6 ml10 wechatName" >'+ father.fWechat[key][i]['nickname']
                            if(father.fWechat[key][i]['isDev']==1){
                                tr += ' <span class="label label-info">IT DEV</span></span>'
                            }
                            tr += '</div>'
                        }
                        tr+='</div>';
                    }
                    tr+='</div>'
                }
                tr += father['loginInfo'] + '</td>';
            } else {
                tr += '<td><p class="color6"><?php echo Yii::t('site', 'No Info'); ?></p></td>';
            }
            if (mother['email']) {
                tr += '<td><p class="color6"><span class="glyphicon glyphicon-user mr5"> </span> ' + mother['name'] + '</p>';
                tr += '<p class="color6"><span class="glyphicon glyphicon-phone mr5"> </span> ' + mother['mphone'] + '</p>';
                tr += '<p class="color6"><span class="glyphicon glyphicon-envelope mr5"> </span> ' + mother['email'] + '</p>';
                tr += '<p class="color6"><span class="glyphicon glyphicon-lock mr5"> </span> ' + mother['passwordText'] + mother['passChanged'] + '</p>';
                tr += '<p class="textBlue" onclick="showWechat(this)"><img src="/themes/base/images/wechat.png" class="mr2"/> ' + mother['asd']
                if(mother.mWechat.length!=0){
                    tr += '<span class="glyphicon glyphicon-chevron-down ml5 text-primary up" ></span><span class="glyphicon glyphicon-chevron-up down ml5 text-primary" ></span></p>';
                    tr += '<div class="wechat mb20">'
                    for(var key in mother.mWechat){
                        tr += '<div class="relative pb20"><p class="flex"><span class="color6 flex1">'+accountName[key]+' 已绑定微信</span> <span>'
                        if(show==1){
                            tr += '<span class="glyphicon glyphicon-qrcode font20" onclick="showQrcode(this)" data-key='+key+' data-id='+mother.pid+'></span>'
                        }
                        tr += '</span></p><div class="qrcode"></div>'
                        for(var i=0;i<mother.mWechat[key].length;i++){
                            tr += '<div class="flex mt10"><span class="imgParent"><img src=' + mother.mWechat[key][i]['headimgurl']+' /></span><span class="flex1 color6 ml10 wechatName" >'+ mother.mWechat[key][i]['nickname']
                            if(mother.mWechat[key][i]['isDev']==1){
                                tr += ' <span class="label label-info">IT DEV</span></span>'
                            }
                            tr += '</div>'
                        }
                        tr+='</div>';
                    }
                    tr+='</div>'
                }
                tr += mother['loginInfo'] + '</td>';
            } else {
                tr += '<td><p class="color6"><?php echo Yii::t('site', 'No Info'); ?></p></td>';
            }
            tr += '</tr>';
        });
        $('#parent-info tbody').html(tr);
    };

    // 获取绑定二维码
    showWechat= function (obj) {
        var node=$(obj).find('.up')
        if(!node.is(':visible')){
            $(obj).parent().find('.wechat').hide()
            $(obj).find('.up').show()
            $(obj).find('.down').hide()
        }else{
            $(obj).parent().find('.wechat').show()
            $(obj).find('.up').hide()
            $(obj).find('.down').show()
        }
    }
    showQrcode = function (obj) {
        let account=$(obj).attr('data-key')
        let pid=$(obj).attr('data-id')
        $('.qrcode').hide()
        $.ajax({
            url: "<?php echo $this->createUrl('//mcampus/student/getBindQrcode');?>",
            type: 'POST',
            dataType: 'json',
            async: false,
            data: {account, pid}
        }).done(function (data) {
            if (data.state == 'success') {
                var str='<p class="mt10 text-right"><span class="glyphicon glyphicon-remove mr10 font14" onclick="hideQrcode(this)"></span></p><div class="text-center"><img src='+data.data+' /></div><div class="color6 font14 text-center mt10">扫码绑定微信</div>'
                $(obj).parent().parent().next().html(str)
                $(obj).parent().parent().next().show()
            } else {
                alert(data.message);
            }
        });
    }
    hideQrcode= function (obj) {
        $(obj).parent().parent().hide()
    }
</script>

<style>
    .highlight{
        color: #ffffff;
        background-color: #312a29;
    }
    .img-circle{
        object-fit: cover;
        height:80px
    }
    .wechat{
        background: #FAFAFA;
        padding:16px 16px 0;
        display:none
    }
    .wechat .imgParent img{
        width:32px;
        height:32px;
        border-radius:50%
    }
    .down{
        display:none
    }
    .wechatName{
        line-height:32px
    }
    .font20{
        font-size:20px
    }
    .qrcode{
        position: absolute;
        width: 200px;
        height: 200px;
        right: 30px;
        top: 20px;
        background: #fff;
        border-radius:6px;
        box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.2);
        display:none;
        z-index:9
    }
    .qrcode img{
        width:120px;
        height:120px
    }
    .mr2{
        margin-right:2px
    }
    .textBlue{
        color:#4D88D2
    }
    .align_end{
        align-items:end
    }
</style>
