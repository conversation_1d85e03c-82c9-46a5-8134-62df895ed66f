<?php

/**
 * This is the model class for table "ivy_asa_course_staff".
 *
 * The followings are the available columns in table 'ivy_asa_course_staff':
 * @property integer $id
 * @property integer $program_id
 * @property integer $course_id
 * @property integer $vendor_id
 * @property string $site_id
 * @property string $job_type
 * @property integer $unit_salary
 * @property string $status
 * @property integer $updated
 * @property integer $updated_by
 * @property integer $unit_type
 */
class AsaCourseStaff extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_asa_course_staff';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('program_id, course_id, vendor_id, site_id, job_type, unit_salary, updated, updated_by, unit_type, status', 'required'),
			array('program_id, course_id, vendor_id, updated, updated_by, unit_type', 'numerical', 'integerOnly'=>true),
			array('site_id, job_type, status', 'length', 'max'=>255),
			array('unit_salary', 'numerical'),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, program_id, course_id, vendor_id, site_id, job_type, unit_salary, status, updated, updated_by, unit_type', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
			"vendor" => array(self::BELONGS_TO, 'AsaVendor', "vendor_id"),
			"courseGroup" => array(self::BELONGS_TO, 'AsaCourseGroup', "program_id")
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'program_id' => 'Program',
			'course_id' => 'Course',
			'vendor_id' => Yii::t('child','Name'),
			'site_id' => 'Site',
			'job_type' => Yii::t('child','职位'),
			'unit_salary' => Yii::t('child','课时费'),
			'status' => Yii::t('asainvoice','Status'),
			'updated' => Yii::t('global','Update Time'),
			'updated_by' => 'Updated By',
			'unit_type' => Yii::t('child','服务类型'),
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('program_id',$this->program_id);
		$criteria->compare('course_id',$this->course_id);
		$criteria->compare('vendor_id',$this->vendor_id);
		$criteria->compare('site_id',$this->site_id,true);
		$criteria->compare('job_type',$this->job_type,true);
		$criteria->compare('unit_salary',$this->unit_salary);
		$criteria->compare('status',$this->status,true);
		$criteria->compare('updated',$this->updated);
		$criteria->compare('updated_by',$this->updated_by);
		$criteria->compare('unit_type',$this->unit_type);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return AsaCourseStaff the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}
