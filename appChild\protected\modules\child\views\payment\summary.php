<?php
$this->breadcrumbs = array(
    Yii::t("navigations", "Payment") => array('/child/payment/summary'),
    Yii::t("navigations", "Summary"),
);
?>

<h1><label><?php echo Yii::t("payment", 'Account Credits'); ?></label></h1>
<script type="text/javascript">

    function viewPrintInvoice(invoice_id){

        if(invoice_id == undefined){
            invoice_id = $("#unpaid_invoices .items input[type=checkbox]").not('#unpaid_invoice_id_all').serialize();
        }else{
            invoice_id = "unpaid_invoice_id="+invoice_id;
        }
        if(invoice_id != ""){
            var base_url = "<?php echo $this->createUrl("payment/printInvoice") ?>";
            $("#common_colorbox").attr('href',base_url+"?"+invoice_id);
            $("#common_colorbox").click();
            $("#noticeDialog").dialog("close");

        }else{

            $("#noticeDialog").html("<div><?php echo Yii::t("payment", 'Please select at least one invoice.'); ?></div>");
            $("#noticeDialog").dialog("open");
            setTimeout('$("#noticeDialog").dialog("close");',2000);
        }

    }

    function viewPrintReceipt(invoice_id){
        if(invoice_id == undefined){
            invoice_id = $("#paid_invoices .items input[type=checkbox]").not('#paid_invoice_id_all').serialize();
        }else{
            invoice_id = "paid_invoice_id="+invoice_id;
        }
        if(invoice_id != ""){
            var base_url = "<?php echo $this->createUrl("payment/printInvoice", array('for' => 'receipt')); ?>";
            $("#common_colorbox").attr('href',base_url+"&"+invoice_id);
            $("#common_colorbox").click();
            $("#noticeDialog").dialog("close");

        }else{
            $("#noticeDialog").html("<div><?php echo Yii::t("payment", 'Please select at least one invoice.'); ?></div>");
            $("#noticeDialog").dialog("open");
            setTimeout('$("#noticeDialog").dialog("close");',2000);

        }

    }

    //开始打印
    function startPrint(){
        if ($.browser.msie) {
            $("#print_info").printArea({
                mode: 'popup', popClose: true
            });
        }
        else {
            $("#print_info").printArea();
        }
    }

</script>

<div class="credit-balance credit-tuition">
    <div class="span-6 info">
        <span class="title"><?php echo Yii::t("navigations", "Tuition Deposit"); ?><span class="unit">￥</span></span>
        <span class="credit <?php
if ($credit < 0.01) {
    echo "class='zero'";
}
?>"><?php echo CHtml::link($depositAmount, array("//child/payment/tcredit"), array("title" => Yii::t("payment", "Transactions"))); ?></span>
        <div class="span-12 last desc hover"><?php echo Yii::t("payment", "This credit is the balance of the deposit that you have paid to guarantee a seat in advance and will be automatically applied to your outstanding tuition invoices."); ?></div>
        <em class="hover"></em>
    </div>

</div>
<div class="clear"></div>
<div class="credit-balance credit-general">
    <div class="span-6 info">
        <span class="title"><?php echo Yii::t("navigations", "General Credit"); ?><span class="unit">￥</span></span>
        <span class="credit <?php
              if ($credit < 0.01) {
                  echo "class='zero'";
              }
?>"><?php echo CHtml::link($credit, array("//child/payment/credit"), array("title" => Yii::t("payment", "Transactions"))); ?></span>
        <div class="span-12 last desc hover"><?php echo Yii::t("payment", 'This credit is generated from tuition, lunch and other refunds.  It can be applied to any invoice, or you can also request for a cash refund.  To use, please <a href="{url}">inform</a> our campus support staff of this request.', array('{url}' => $this->createUrl('//child/support/email'))); ?> </div>
        <em class="hover"></em>
    </div>

</div>
<div class="clear"></div>

<?php
$schoolConfig = PointsConfig::model()->getSchoolConfig($this->getSchoolId());
if($schoolConfig->status){
?>
<!-- <h1><label><?php echo Yii::t("payment", 'Ivy Points'); ?></label></h1>
<div class="credit-balance credit-tuition" style="height:100px;">
    <div class="span-6 info">
        <span class="title"><?php echo Yii::t("payment", "Current Balance"); ?></span>
        <span class="credit"><?php echo CHtml::link($points, array("/child/payment/points")); ?></span>
    </div>
</div>
<div class="clear"></div> -->
<?php }?>

<?php
$colorbox = $this->widget('application.extensions.colorbox.JColorBox');
$colorbox->addInstance('.colorbox', array(
    'iframe' => false,
    'width' => '750',
    'height' => '80%'
));
$colorbox->addInstance(
        '.colorboxForPrint', array(
    'iframe' => false,
    'width' => '750',
    'height' => '80%',
    'title' => '<span class="btn001">' . CHtml::link('<span>' . Yii::t("payment", "Print") . '</span>', "javascript:void();", array('onclick' => "startPrint();return false;")) . '</span>'
));

echo CHtml::link("", "#", array(
    "id" => "common_colorbox",
    "class" => "colorboxForPrint",
    "style" => "display:none",
));

$this->widget('zii.widgets.jui.CJuiDialog', array(
    'id' => 'noticeDialog',
    'options' => array(
        // 提示
        'title' => Yii::t("payment", 'Tips'),
        'autoOpen' => false,
    ),
));
?>

<?php
$printSrc = Yii::app()->theme->baseUrl . '/images/icons/printer32.png';

//if(!empty($unPayList->data)) {
?>
<h1>
    <label><?php echo Yii::t("payment", 'Unpaid Invoices'); ?> </label>
</h1>
<?php
$makePayment = null;
$printInvoiceLink = null;
if (!empty($unPayList->data)) {

    $makePayment = CHtml::openTag('span', array('class' => 'btn001'));
    $makePaymentText = CHtml::openTag('span');
    $makePaymentText .= Yii::t("payment", 'Make Payment Online');
    $makePaymentText .= CHtml::closeTag('span');
    $makePayment .= CHtml::link($makePaymentText, $this->createUrl("//child/payment/online"), array('class' => 'bigger'));
    $makePayment .= CHtml::closeTag('span');

    $printInvoiceLink = CHtml::openTag("a", array(
                "title" => Yii::t("payment", "Print"),
                "href" => "javascript:void();",
                "onclick" => 'viewPrintInvoice();return false;',
            ));
    $printInvoiceLink.= CHtml::image($printSrc, Yii::t("payment", "Print"));
    $printInvoiceLink.= CHtml::closeTag("a");
}
$invoice_model = Invoice::model();
$this->widget('zii.widgets.grid.CGridView', array(
    'cssFile' => Yii::app()->theme->baseUrl . '/widgets/gridview/styles.css',
    'id' => 'unpaid_invoices',
    'dataProvider' => $unPayList,
    'enableSorting' => false,
    'emptyText' => Yii::t("payment", 'All invoices have been paid.  Thank you very much!'),
    'columns' => array(
        array(
            'class' => 'CCheckBoxColumn',
            'id' => "unpaid_invoice_id",
            'value' => '$data->invoice_id',
            'selectableRows' => 2,
            'checkBoxHtmlOptions' => array(
                'name' => 'unpaid_invoice_id[]',
            ),
            'footer' => $printInvoiceLink,
        ),
        array(
            'name' => 'title',
            'type' => 'raw',
            'htmlOptions' => array("class" => "alignLeft"),
            'value' => 'CHtml::link($data->title,"javascript:void();",array("onclick"=>"viewPrintInvoice($data->invoice_id);return false;"))',
        ),
        //				array(
        //        			'name'   =>'payment_type',
        //					'value'  => 'Invoice::feeType($data->payment_type)',
        //				),
        array(
            'name' => 'amount',
            'value' => 'Invoice::formatAmount(null,$data->amount)',
        ),
        array(
            'header' => Yii::t("payment", 'Amount Paid'),
            'value' => array($invoice_model, 'renderPaidAmountF')
        ),
        array(
            'name' => 'duetime',
            'value' => 'Mims::formatDateTime($data->duetime)',
            'footer' => Yii::app()->params['ownerConfig']['key'] == 'BJ_DS' ? '' : $makePayment,
        ),
    ),
    'ajaxUpdate' => false,
    'template' => "{items}"
));
?>
<h1>
    <label><?php echo Yii::t("navigations", 'Payment History'); ?> </label> <span>
        <?php
        $more = CHtml::link(Yii::t("global", 'More'), array('//child/payment/history'));
        if (!empty($payList->data))
            echo $more;
        ?>
    </span>
</h1>
<?php
$printReceiptLink = null;

if (!empty($payList->data)) {
    $printReceiptLink = CHtml::openTag("a", array(
                "title" => Yii::t("payment", "Print"),
                "href" => "javascript:void();",
                "onclick" => 'viewPrintReceipt();return false;',
            ));
    $printReceiptLink.= CHtml::image($printSrc, Yii::t("payment", "Print"));
    $printReceiptLink.= CHtml::closeTag("a");
}
$this->widget('zii.widgets.grid.CGridView', array(
    'cssFile' => Yii::app()->theme->baseUrl . '/widgets/gridview/styles.css',
    'id' => 'paid_invoices',
    'dataProvider' => $payList,
    'enableSorting' => false,
    'columns' => array(
        array(
            'class' => 'CCheckBoxColumn',
            'id' => "paid_invoice_id",
            'value' => '$data->invoice_id',
            'selectableRows' => 2,
            'checkBoxHtmlOptions' => array(
                'name' => 'paid_invoice_id[]',
            ),
            'footer' => empty($payList->data) ? null : $printReceiptLink,
        ),
        array(
            'name' => 'title',
            'type' => 'raw',
            'htmlOptions' => array("class" => "alignLeft"),
            'value' => 'CHtml::link($data->title,"javascript:void();",array("onclick"=>"viewPrintReceipt($data->invoice_id);return false;"))',
            'footer' => empty($payList->data) ? null : $more
        ),
        //				array(
        //        			'name'   =>'payment_type',
        //					'value'  => 'Invoice::feeType($data->payment_type)',
        //				),
// 					array(
// 							'name'=>'amount',
// 							'value'  => 'Invoice::formatAmount($data->inout,$data->amount)',
// 					),
        array(
            'name' => 'amount',
            'value' => 'Yii::app()->numberFormatter->format("#,##0.00",$data->amount)',
        ),
        array(
            'name' => 'last_paid_timestamp',
            'value' => 'Mims::formatDateTime($data->last_paid_timestamp, "medium", "short")',
        ),
    ),
    'ajaxUpdate' => false,
    'template' => "{items}"
));
?>

<?php
/*
  <h1>
  <label><?php echo Yii::t("payment",'General Credit Transactions');?> </label> <span>
  <?php $more = CHtml::link(Yii::t("global",'More'), array('//child/payment/credit'));
  if (!empty($creditList->data)) echo $more;
  ?>
  </span>
  </h1>


  if(1||!empty($creditList->data)) {
  $this->widget('zii.widgets.grid.CGridView', array(
  'cssFile'=>Yii::app()->theme->baseUrl.'/widgets/gridview/styles.css',
  'dataProvider'=> $creditList,
  'enableSorting'=>false,
  'columns'=>array(
  array(
  'headerHtmlOptions'=>array('class'=>'icon-24'),
  'htmlOptions' => array('class'=>'icon-column'),
  'name' => 'inout',
  'header' => '',
  'type'=>'raw',
  'value' => '$data->renderInout()'
  ),
  array(
  'name' =>'itemname',
  'value'  => 'Invoice::feeType($data->itemname)',
  'footer' => (empty($creditList->data))? null:$more,
  ),
  array(
  'name'   =>'amount',
  'value' => 'Invoice::formatAmount($data->inout,$data->amount)'

  ),
  //				array(
  //        			'name'=>'inout',
  //					'value' => 'Invoice::formatInout($data->inout)'
  //				),
  array(
  'name'=>'updated_timestamp',
  'value' => 'Mims::formatDateTime($data->updated_timestamp,"medium","short")'
  ),
  ),
  'ajaxUpdate'=>false,
  'template'=>"{items}"
  ));
  }
 */
?>



<?php
if(Yii::app()->params['ownerConfig']['key'] == 'BJ_DS'){
    $nextUrl = $this->createUrl("//child/support/campus",array("demo"=>"guide"));
}
else{
    $nextUrl = $this->createUrl("//child/resource/index", array("demo" => "guide"));
}

Yii::import('ext.guide.class.*');
$this->widget('ext.guide.Guide', array(
    'options' => array(
        'guides' => array(
            0 => GuideItem::navGuide(10, null, true, "navigations_payment"),
            10 => GuideItem::subNavGuide($nextUrl, null, "subnav_payment"),
        ),
    )
));
?>