<?php

class SurveyController extends ProtectedController
{
    public $batchNum = 30;
    public $actionAccessAuths = array(
    	'index'=>'oOperationsView',
    );
    
    public function init() {
        parent::init();
        Yii::import('common.models.survey.*');

        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'hqOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site','HQ Operations');
        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/vue2.js');
        $cs->registerCssFile($cs->getCoreScriptUrl().'/jui/css/base/jquery-ui.css');
        $cs->registerScriptFile($cs->getCoreScriptUrl().'/jui/js/jquery-ui.min.js');
    }
    /****************************** 问卷管理 **********************************/
    // 问卷列表
	public function actionIndex()
	{
	    $serviceYear = Yii::app()->request->getParam('serviceYear', date("Y", time()));

        $surveyData = array(); // 模板数据
        $data = array(); // 分配给校园后总数和完成度
        $branchModel = new Branch();
        $branch = $branchModel->getBranchList(null,true);
        $schoolList = array();
        foreach ($branch as $key=>$val){
            $schoolList[$key] = $val['abb'];
        }
        Yii::import('common.models.survey.Survey');
        // 根据年份获取这个年份的调查问卷
        $criteria = new CDbCriteria();
        $criteria->index = 'service_year';
        $criteria->order = 'service_year DESC';
        $serviceYearModel = Survey::model()->findAll($criteria);
        $yearList = array();
        foreach ($serviceYearModel as $val){
            $end = $val->service_year + 1;
            $yearList[$val->service_year] = $val->service_year . ' - ' . $end;
        }

	    // 根据年份获取这个年份的调查问卷
        $criteria = new CDbCriteria();
        $criteria->compare('service_year', $serviceYear);
        $criteria->index = 'id';
        $criteria->order = 'id desc';
	    $surveyModel = Survey::model()->findAll($criteria);
        $has_report = array();
	    if($surveyModel){
            // 根据调查报告的ID，获取每个报告下开启校园的信息
            $criteria = new CDbCriteria();
            $criteria->compare('survey_id', array_keys($surveyModel));
            $criteria->compare('is_published', 1);
            $surveyDetail = SurveyDetail::model()->findAll($criteria);
            /*
            * 是否生产报告查询
            */
            $surveyStr = implode(',', array_keys($surveyModel));
            $sql = 'SELECT survey_id, schoolid, has_report, update_time FROM `ivy_survey_detail` where `survey_id` in (' . $surveyStr . ') group by survey_id,schoolid';
            $sc = Yii::app()->db->createCommand($sql)->queryAll();
            if ($sc) {
                foreach ($sc as $val) {
                    $has_report[$val['survey_id']][$val['schoolid']] = array(
                        'has_report' => $val['has_report'],
                        'update_time' => date("Y-m-d H:i", $val['update_time']),
                    );
                }
            }
        }

        $dateailArray = array();
	    if($surveyDetail) {
            foreach ($surveyDetail as $val) {
                $hasReportSchool = 0;
                if(isset($has_report) && $has_report[$val->survey_id][$val->schoolid]['has_report'] == 1){
                    $hasReportSchool = 1;
                }
                $dateailArray[$val->survey_id][$val->schoolid] = array(
                    'id' => $val->id,
                    'title' => $val->schoolid,
                    'start_time' => date("Y-m-d", $val->start_time),
                    'end_time' => date("Y-m-d", $val->end_time),
                    'has_report' => $hasReportSchool,
                );
            }
        }

	    /*
	     * 拼成想要的数组发送模板
	     * array(
	     *     报告标题  str
	     *     开发校园的数组 array
	     * )
	     */
        if($surveyModel) {
            foreach ($surveyModel as $val) {
                $descArr = json_decode($val->desc, true);
                
                $criteria = new CDbCriteria();
                $criteria->compare('survey_id', $val->id);
                $countModel = WSurveyReport::model()->count($criteria);
                $datas = ($dateailArray && $dateailArray[$val->id]) ? $dateailArray[$val->id] : array();
                $count = ($countModel) ? 1 : 0;
                $surveyData[] = array(
                    'survey_id' => $val->id,
                    'title' => $val->getTitle(),
                    'has_report' => $count,
                    'data' => $datas,
                    'common_cn' => isset($descArr['common_cn']) ? $descArr['common_cn'] : '',
                    'common_en' => isset($descArr['common_en']) ? $descArr['common_en'] : '',
                    'teacher_cn' => isset($descArr['teacher_cn']) ? $descArr['teacher_cn'] : '',
                    'teacher_en' => isset($descArr['teacher_en']) ? $descArr['teacher_en'] : '',
                    'teacher_title_cn' => isset($descArr['teacher_title_cn']) ? $descArr['teacher_title_cn'] : '',
                    'teacher_title_en' => isset($descArr['teacher_title_en']) ? $descArr['teacher_title_en'] : '',
                );
            }
        }
        $surveyFeedback = array();
        if($dateailArray) {
            $criteria = new CDbCriteria();
            $criteria->compare('survey_id', array_keys($dateailArray));
            $criteria->order = 'survey_id DESC';
            $surveyFeedback = SurveyFeedback::model()->findAll($criteria);
        }

        $schoolSurvey = array();
        if($surveyFeedback) {
            foreach ($surveyFeedback as $item) {
                $schoolSurvey[$item->survey_id][$item->schoolid][] = array(
                    'is_fb' => $item->is_fb,
                    'id' => $item->id,
                );
            }

            foreach ($schoolSurvey as $key => $val) {
                $data[$key]['total'] = 0;
                $data[$key]['complete'] = 0;
                foreach ($val as $school => $item) {
                    $data[$key]['total'] += count($item);
                    $data[$key][$school]['is_fb'] = 0;
                    foreach ($item as $v) {
                        $data[$key][$school]['count'] += 1;
                        if ($v['is_fb'] == 1) {
                            $data[$key][$school]['is_fb'] += 1;
                            $data[$key]['complete'] += 1;
                        }
                    }
                }
            }
        }

		$this->render('index',array(
            'surveyData' => $surveyData,
            'data' => $data,
            'schoolList' => $schoolList,
            'yearList' => $yearList,
            'serviceYear' => $serviceYear,
        ));
	}

    // 新建，更新问卷
    public function actionUpdateSurvey()
    {
        $schoolid = Yii::app()->request->getParam('schoolid', '');
        $surveyid = Yii::app()->request->getParam('surveyid', '');

        if(Yii::app()->request->isPostRequest){
            $surveyDetail = array();
            if($surveyid && $schoolid) {
                $criteria = new CDbCriteria();
                $criteria->compare('survey_id', $surveyid);
                $criteria->compare('schoolid', $schoolid);
                $surveyDetail = SurveyDetail::model()->find($criteria);
            }
            if(empty($surveyDetail)){
                $surveyDetail = new SurveyDetail();
                $surveyDetail->survey_id = $surveyid;
                $surveyDetail->schoolid = $schoolid;
            }
            if($_POST['SurveyDetail']) {
                $surveyDetail->attributes = $_POST['SurveyDetail'];
                $surveyDetail->start_time = ($surveyDetail->start_time) ? strtotime($surveyDetail->start_time) : "";
                $surveyDetail->end_time = ($surveyDetail->end_time) ? strtotime($surveyDetail->end_time) : "";
                $surveyDetail->update_user = Yii::app()->user->id;
                $surveyDetail->update_time = time();

                if (!$surveyDetail->save()) {
                    $this->addMessage('state', 'fail');
                    $err = current($surveyDetail->getErrors());
                    $this->addMessage('message', $err ? $err[0] : '失败');
                    $this->showMessage();
                }
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message', 'success'));
                $this->addMessage('callback', 'cbUpdateSurvey');
                $this->showMessage();
            }else{
                $data['before_memo_en'] = $surveyDetail->before_memo_en;
                $data['before_memo_cn'] = $surveyDetail->before_memo_cn;
                $data['process_memo_en'] = $surveyDetail->process_memo_en;
                $data['process_memo_cn'] = $surveyDetail->process_memo_cn;
                $data['after_memo_en'] = $surveyDetail->after_memo_en;
                $data['after_memo_cn'] = $surveyDetail->after_memo_cn;
                $data['start_time'] = ($surveyDetail->start_time) ? date("Y-m-d", $surveyDetail->start_time) : 0;
                $data['end_time'] = ($surveyDetail->end_time) ? date("Y-m-d", $surveyDetail->end_time) : 0;
                $data['is_published'] = ($surveyDetail->is_published) ? $surveyDetail->is_published : 1;
                $this->addMessage('state', 'success');
                $this->addMessage('data', $data);
                $this->showMessage();
            }

        }
    }

    // 预览问卷
    public function actionPreviewSurvey()
    {
        $surveyid = Yii::app()->request->getParam('surveyid', '');
        $surveyModel = Survey::model()->findByPk($surveyid);
        $data = $this->survey($surveyModel);

        $this->render('previewSurvey',array(
            'data' => $data,
        ));
        
    }

    public function actionUpdateSurveyDesc()
    {
        $surveyId = Yii::app()->request->getParam('survey_id', '');

        $this->addMessage('state', 'fail');
        
        if (!$surveyId) {
            $this->addMessage('message', 'survey id not null');
            $this->showMessage();
        }
        $commonCn = Yii::app()->request->getParam('common_cn', '');
        $commonEn = Yii::app()->request->getParam('common_en', '');
        $teacherCn = Yii::app()->request->getParam('teacher_cn', '');
        $teacherEn = Yii::app()->request->getParam('teacher_en', '');
        $teacherTitleCn = Yii::app()->request->getParam('teacher_title_cn', '');
        $teacherTitleEn = Yii::app()->request->getParam('teacher_title_en', '');

        $desc = array(
            'common_cn' => $commonCn,
            'common_en' => $commonEn,
            'teacher_cn' => $teacherCn,
            'teacher_en' => $teacherEn,
            'teacher_title_cn' => $teacherTitleCn,
            'teacher_title_en' => $teacherTitleEn,
        );
        Yii::import('common.models.survey.Survey');
        $surveyModel = Survey::model()->findByPk($surveyId);
        if (!$surveyModel) {
            $this->addMessage('message', 'survey not found');
            $this->showMessage();
        }
        $surveyModel->desc = json_encode($desc);
        if (!$surveyModel->intro) {
            $surveyModel->intro = 'intro';
        }
        if (!$surveyModel->save()) {
            $errors = current($surveyModel->getErrors());
            $this->addMessage('message', $errors[0]);
            $this->showMessage();
        }

        $this->addMessage('state', 'success');
        $this->addMessage('message', 'success');
        $this->showMessage();
    }
    
    // 生成报告操作
    public function actionGeneralSurveyReport()
    {
        $surveyid = Yii::app()->request->getParam('surveyid', '');

        if($surveyid) {
            $criteria = new CDbCriteria();
            $criteria->compare('survey_id', $surveyid);
            $surveyDetail = WSurveyDetail::model()->findAll($criteria);

            if ($surveyDetail) {
                $sql = 'SELECT schoolid, topic_id, count(id) AS total FROM `ivy_survey_feedback_option` where `survey_id` = ' . $surveyid . ' group by schoolid,topic_id';
                $sc = Yii::app()->db->createCommand($sql)->queryAll();

                foreach ($surveyDetail as $val) {
                    $val->has_report = 1;
                    $val->update_time = time();
                    $val->update_user = Yii::app()->user->id;
                    $val->save();
                }

                WSurveyReport::model()->deleteAllByAttributes(array('survey_id' => $surveyid));

                if ($sc) {
                    foreach ($sc as $val) {
                        $reportModel = new WSurveyReport();
                        $reportModel->survey_id = $surveyid;
                        $reportModel->topic_id = $val['635'];
                        $reportModel->schoolid = $val['schoolid'];
                        $reportModel->votes = $val['total'];
                        $reportModel->status = 0;
                        $reportModel->update_user = Yii::app()->user->id;
                        $reportModel->update_time = time();
                        $reportModel->save();
                    }
                }
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message', '更新数据成功'));
                $this->showMessage();
            }
        }
        $this->addMessage('state', 'fail');
        $this->addMessage('message', Yii::t('message', '参数错误'));
        $this->showMessage();
    }

    // 问卷完成/未完成学生名单
    public function actionFeedbackList()
    {
        $schoolid = Yii::app()->request->getParam('schoolId', '');
        $surveyid = Yii::app()->request->getParam('surveyId', '');
        $isFb = Yii::app()->request->getParam('isFb', '');
        $offsetData = Yii::app()->request->getParam('offset', 1);

        $is_Fb = ($isFb == 1) ? $isFb : 0;
        $criteria = new CDbCriteria();
        if($schoolid){
            $criteria->compare('schoolid', $schoolid);
        }
        $criteria->compare('survey_id', $surveyid);
        $criteria->compare('is_fb', $is_Fb);
        if($isFb == 1){
            $offset = ($offsetData - 1) * $this->batchNum;
            $criteria->order  = 'fb_time DESC';
            $criteria->limit = $this->batchNum;    //取1条数据，如果小于0，则不作处理
            $criteria->offset = $offset;   //两条合并起来，则表示 limit 10 offset 1,或者代表了。limit 1,10
        }
        $surveyFeedback = SurveyFeedback::model()->findAll($criteria);

        $data = array();
        if($is_Fb) {
            $branch = new Branch();
            $schoolList = $branch->getBranchList();
            $criteria = new CDbCriteria();
            if($schoolid){
                $criteria->compare('schoolid', $schoolid);
            }
            $criteria->compare('survey_id', $surveyid);
            $criteria->compare('is_fb', $is_Fb);
            $total = SurveyFeedback::model()->count($criteria);

            $totalPages = ($total) ? ceil($total/$this->batchNum) : 0;

            if($surveyFeedback) {
                foreach ($surveyFeedback as $val) {
                    $data[] = array(
                        'id' => $val->id,
                        'surveyTitle' => $val->survey->getTitle(),
                        'childName' => $val->child->getChildName(),
                        'childid' => $val->childid,
                        'school' => $val->schoolid,
                        'fbTime' => date("Y-m-d H:i", $val->fb_time),
                    );
                }
            }

            $this->render('feedback',array(
                'data' => $data,
                'schoolList' => $schoolList,
                'schoolid' => $schoolid,
                'surveyid' => $surveyid,
                'isFb' => $isFb,
                'totalPages' => $totalPages,
            ));
        }else{
            if($surveyFeedback) {
                foreach ($surveyFeedback as $val) {
                    $data[$val->classid]['classTitle'] = $val->ivyclass->title;
                    $data[$val->classid]['data'][$val->childid] = $val->child->getChildName();
                }
            }
            echo json_encode($data);
        }
       /* Yii::msg($data);
        // 控制查看完成或未完成由参数控制
        $this->render('index',array(

        ));*/
    }

    // 预览已完成的问卷反馈
    public function actionPreviewFeedback()
    {
        $surveyid = Yii::app()->request->getParam('surveyId', '');
        $childid = Yii::app()->request->getParam('childId', '');
        $schoolid = Yii::app()->request->getParam('schoolid', '');
        $status = Yii::app()->request->getParam('status', '');

        $feedBackData = array();
        $data = array();
        $titledata = array();
        if($surveyid && $childid) {
            $childModel = ChildProfileBasic::model()->findByPk($childid);
            $schoolModel = Branch::model()->findByPk($schoolid);

            $surveyModel = Survey::model()->findByPk($surveyid);
            $criteria = new CDbCriteria();
            $criteria->compare('survey_id', $surveyid);
            $criteria->compare('schoolid', $schoolid);
            $surveyDetailModel = WSurveyDetail::model()->find($criteria);

            $criteria = new CDbCriteria();
            $criteria->compare('survey_id', $surveyid);
            $criteria->compare('schoolid', $schoolid);
            $criteria->compare('childid', $childid);
            $surveyFeedbackModel = WSurveyFeedback::model()->find($criteria);

            $data = $this->survey($surveyModel, false);

            $criteria = new CDbCriteria();
            $criteria->compare('survey_id', $surveyid);
            $criteria->compare('childid', $childid);
            $feedbackOptionModel = WSurveyFeedbackOption::model()->findAll($criteria);
            $feedBackData1 = array();
            foreach ($feedbackOptionModel as $val) {
                $feedBackData1[$val->topic_id][] = $val->option_answer;
            }

            foreach ($feedBackData1 as $key=>$val) {
                if(count($val) > 1){
                    $feedBackData[$key] = $val;
                }else{
                    $feedBackData[$key] = $val[0];
                }

            }

            $titledata['time'] = date("Y-m-d", $surveyDetailModel->start_time) . ' / ' . date("Y-m-d", $surveyDetailModel->end_time);
            $titledata['fb_time'] = date("Y-m-d H:i", $surveyFeedbackModel->fb_time);
            $titledata['childName'] = $childModel->getChildName(false,false,true);
            $titledata['childClass'] = $childModel->ivyclass->title;
            $titledata['schoolName'] = $schoolModel->title;
        }

        $this->render('previewFeedback',array(
            'data' => $data,   // 模板题显示
            'feedBackData' => $feedBackData, // 孩子选项数据
            'titledata' => $titledata, // 基本信息
            'status' => $status, // 查看还是编辑
        ));
    }


    // 预览模板题目公共方法
    /*
     * 0 单选 1多选
     * 0 1 可选 2 3必选 2 不带备注 3带备注
     *
     */
    public function survey($surveyModel, $childNameType = true)
    {
        $data = array();
        //  模板
        $template = array();
        if($surveyModel) {
            $id = unserialize($surveyModel->template_id);
            $data['surveyTitle'] = $surveyModel->getTitle();
            $criteria = new CDbCriteria();
            $criteria->compare('id', $id);
            $criteria->index = 'id';
            $template = WSurveyTemplate::model()->findAll($criteria);
        }

        // 题目
        $topicModels = array();
        if($template) {
            $criteria = new CDbCriteria();
            $criteria->compare('template_id ', array_keys($template));
            $criteria->order = 'display_order ASC';
            $topicModels = WSurveyTopic::model()->findAll($criteria);
        }


        $optionsModels = array();
        $optionGroupModels = array();
        // 项目选项
        if($topicModels) {
            $group_id = array();
            foreach ($topicModels as $val){
                $group_id[] = $val->option_group;
            }

            $criteria = new CDbCriteria();
            $criteria->compare('id', $group_id);
            $criteria->index = 'id';
            $optionGroupModels = WSurveyOptionGroup::model()->findAll($criteria);

            $criteria = new CDbCriteria();
            $criteria->compare('group_id', $group_id);
            $criteria->order = 'weight DESC';
            $optionsModels = WSurveyOptions::model()->findAll($criteria);
        }
        // 选项值
        $optionsArray = array();
        if($optionsModels) {
            foreach ($optionsModels as $val) {
                $optionsArray[$val->group_id][] = array(
                    'title' => $val->getTitle($childNameType),
                    'option_value' => $val->option_value,
                );
            }
        }

        // 把模板， 题目， 选项拼接
        if($topicModels){
            foreach ($topicModels as $val){
                //$status = (isset($optionGroupModels) && $optionGroupModels[$val->option_group]) ? $optionGroupModels[$val->option_group]->option_cat : 0 ;
                $data['data'][$val->id]['title'] = $val->getTitle($childNameType);
                $data['data'][$val->id]['status'] = $optionGroupModels[$val->option_group]->option_cat;
                $data['data'][$val->id]['binary_flag'] = $val->binary_flag;
                if(isset($optionsArray) && $optionsArray[$val->option_group]) {
                    $data['data'][$val->id]['data'] = $optionsArray[$val->option_group];
                }
            }
        }

        return $data;
    }

    /****************************** 模版管理 **********************************/
    // 模版列表
    public function actionTemplate()
    {
        $page = Yii::app()->request->getParam('page', 1);

        $criteria = new CDbCriteria();
        $offset = ($page - 1) * $this->batchNum;
        $criteria->order  = 'id DESC';
        $criteria->limit = $this->batchNum;    //取1条数据，如果小于0，则不作处理
        $criteria->offset = $offset;   //两条合并起来，则表示 limit 10 offset 1,或者代表了。limit 1,10
        $templateModel = WSurveyTemplate::model()->findAll($criteria);
        $templateList = array();
        foreach ($templateModel as $val){
            $templateList[] = array(
                'id' => $val->id,
                'title' => $val->getTitle(),
                'subtitle' => $val->getSubtitle(),
                'update_time' => date("Y-m-d H:i:s", $val->update_time),
            );
        }

        $total = WSurveyTemplate::model()->count();

        $totalPages = ($total) ? ceil($total/$this->batchNum) : 0;

        $this->render('template',array(
            'templateList' => $templateList,
            'totalPages' => $totalPages,
            'page' => $page,
        ));
    }

    // 预览模版
    public function actionPreviewTemplate()
    {
        $this->render('template',array(

        ));
    }

    // 新建、更新模版
    public function actionUpdateTemplate()
    {
        $tempid = Yii::app()->request->getParam('tempid', '');

        if(Yii::app()->request->isPostRequest) {
            $model = WSurveyTemplate::model()->findByPk($tempid);
            if (!$model) {
                $model = new WSurveyTemplate();
            }
            if($_POST['WSurveyTemplate']) {
                $model->attributes = $_POST['WSurveyTemplate'];
                $model->status = 0;
                $model->update_user = Yii::app()->user->id;
                $model->update_time = time();
                if(!$model->title_cn && !$model->title_en){
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', '中文名称和英文名称必填填写一个');
                    $this->showMessage();
                }
                if ($model->save()) {
                    $this->addMessage('state', 'success');
                    $this->addMessage('message', Yii::t('message', 'success'));
                    $this->addMessage('callback', 'cbVisit');
                } else {
                    $this->addMessage('state', 'fail');
                    $err = current($model->getErrors());
                    $this->addMessage('message', $err ? $err[0] : '失败');
                }
                $this->showMessage();
            }else{
                $data['title_cn'] = $model->title_cn;
                $data['title_en'] = $model->title_en;
                $data['subtitle_cn'] = $model->subtitle_cn;
                $data['subtitle_en'] = $model->subtitle_en;
                $this->addMessage('state', 'success');
                $this->addMessage('data', $data);
                $this->showMessage();
            }

        }
    }

    // 模版题库管理
    public function actionTemplateTopic()
    {
        $tempid = Yii::app()->request->getParam('tempid', '');

        $criteria = new CDbCriteria();
        $criteria->compare('template_id', $tempid);
        $criteria->compare('status', 0);
        $criteria->order = 'display_order ASC';
        $topicModel = WSurveyTopic::model()->findAll($criteria);
        $topic = array();
        foreach ($topicModel as $val){
            $topic[]  = array(
                'id' => $val->id,
                'title_cn' => $val->title_cn,
                'title_en' => $val->title_en,
                'binary_flag' => $val->binary_flag,
                'option_group_title' => $val->optionGroup->getTitle(),
                'option_cat' => $val->optionGroup->option_cat,  // 1 单选  2多选
            );
        }

        $this->render('templateTopic',array(
            'topic' => $topic,
        ));
    }

    // 添加、更新模版题目
    public function actionUpdateTemplateTopic()
    {
        $tempid = Yii::app()->request->getParam('tempid', '');
        $topicid = Yii::app()->request->getParam('topicid', '');
        $allow_feedback = Yii::app()->request->getParam('allow_feedback', '');
        $binary_flag = Yii::app()->request->getParam('binary_flag', '');

        if(Yii::app()->request->isPostRequest)
        {
            $model = WSurveyTopic::model()->findByPk($topicid);
            if(!$model){
                $criteria = new CDbCriteria();
                $criteria->compare('template_id', $tempid);
                $count = WSurveyTopic::model()->count($criteria);
                $model = new WSurveyTopic();
                $model->display_order = $count+1;
            }

            if($_POST['WSurveyTopic']) {
                $model->attributes = $_POST['WSurveyTopic'];
                $model->template_id = $tempid;
                $model->binary_flag = $allow_feedback ? ($binary_flag == 1 ? 3 : 1) : ($binary_flag == 1 ? 2 : 0) ;
                $model->status = 0;
                $model->update_user = Yii::app()->user->id;
                $model->update_time = time();
                if ($model->save()) {
                    $this->addMessage('state', 'success');
                    $this->addMessage('message', Yii::t('message', 'success'));
                    $this->addMessage('callback', 'cbVisit');
                } else {
                    $this->addMessage('state', 'fail');
                    $err = current($model->getErrors());
                    $this->addMessage('message', $err ? $err[0] : '失败');
                }
                $this->showMessage();
            }else{
                $feedback1 = in_array($model->binary_flag,array(0,2)) ? 0 : 1;
                $binary_flag = in_array($model->binary_flag,array(2,3)) ? 1 : 2;
                $data['id'] = $model->id;
                $data['template_id'] = $model->template_id;
                $data['title_cn'] = $model->title_cn;
                $data['title_en'] = $model->title_en;
                $data['option_group'] = $model->optionGroup->getTitle();
                $data['binary_flag'] = $binary_flag;
                $data['allow_feedback'] = $feedback1;

                $this->addMessage('state', 'success');
                $this->addMessage('data', $data);
                $this->showMessage();
            }
        }
    }

    // 排序模版题目
    public function actionSortTemplateTopic()
    {
        $topic_order = Yii::app()->request->getParam('topic_order', array());

        if(Yii::app()->request->isPostRequest){
            if($topic_order) {
                foreach ($topic_order as $key => $val) {
                    $topicModel = WSurveyTopic::model()->findByPk($key);
                    $topicModel->display_order = $val;
                    $topicModel->save();
                }
            }
            $this->addMessage('state', 'success');
            $this->addMessage('message', Yii::t('message', '更新顺序成功'));
            $this->showMessage();
        }
    }

    // 删除模版题目
    public function actionDeleteTemplateTopic()
    {
        $topic_id = Yii::app()->request->getParam('topic_id', '');
        if(Yii::app()->request->isPostRequest){
            $topicModel = WSurveyTopic::model()->findByPk($topic_id);
            if($topicModel) {
                $topicModel->status = 1;
                $topicModel->save();
            }
            $this->addMessage('state', 'success');
            $this->addMessage('message', Yii::t('message', '删除成功'));
            $this->showMessage();
        }
    }

    /****************************** 选项组管理 **********************************/
    // 选项组列表
    public function actionGroup()
    {
        $crit = new CDbCriteria();
        $crit->compare('status', WSurveyOptionGroup::STATUS_NORMAL);
        $crit->order = 'update_time DESC';
        $groupsModel = WSurveyOptionGroup::model()->findAll($crit);
        $groupData = array();
        $i = 0;
        foreach ($groupsModel as $model) {
            $groupData[$i]['id'] = $model->id;
            $groupData[$i]['title_cn'] = $model->title_cn;
            $groupData[$i]['title_en'] = $model->title_en;
            $groupData[$i]['option_cat'] = $model->option_cat;
            $groupData[$i]['option_order'] = unserialize($model->option_order);
            $i++;
        }
        unset($groupsModel);
        // 取出所有选项组
        $this->render('group',array(
            'groupData' => $groupData,
        ));
    }

    // 新建、更新选项组
    public function actionUpdateGroup()
    {
        if (!Yii::app()->request->isPostRequest) {
            return false;
        }
        $groupId = Yii::app()->request->getPost('groupId');
        $title_cn = Yii::app()->request->getPost('title_cn');
        $title_en = Yii::app()->request->getPost('title_en');
        $option_cat = Yii::app()->request->getPost('option_cat');

        $this->addMessage('state', 'fail');
        $groupModel = WSurveyOptionGroup::model()->findByPk($groupId);
        if (!$groupModel) {
            $groupModel = new WSurveyOptionGroup();
        }
        $groupModel->title_cn = $title_cn;
        $groupModel->title_en = $title_en;
        $groupModel->option_cat = $option_cat;
        $groupModel->update_user = $this->staff->uid;
        $groupModel->update_time = time();
        if (!$groupModel->save()) {
            $error = current($groupModel->getErrors());
            $this->addMessage('message', $error[0]);
            $this->showMessage();
        }
        $this->addMessage('state', 'success');
        $this->addMessage('message', 'success');
        $this->showMessage();
    }

    // 删除选项组（包括关联的选项） http://oa.mims.cn/modules/survey/ajax/template_ajax.php
    public function actionDeleteGroup()
    {
        if (!Yii::app()->request->isPostRequest) {
           return false;
        }
        $groupId = Yii::app()->request->getPost('groupId');

        $this->addMessage('state', 'fail');
        $groupModel = WSurveyOptionGroup::model()->findByPk($groupId);
        if (!$groupModel) {
           $this->addMessage('message', 'group 不存在');
           $this->showMessage();
        }
        $groupModel->status = WSurveyOptionGroup::STATUS_DELETED;
        if (!$groupModel->save()) {
            $error = current($groupModel->getErrors());
            $this->addMessage('message', $error[0]);
            $this->showMessage();
        }
        $this->addMessage('state', 'success');
        $this->addMessage('message', 'success');
        $this->showMessage();
    }

    // 根据groupId获取选项
    public function actionGetTopic()
    {
        if (!Yii::app()->request->isPostRequest) {
           return false;
        }
        $groupId = Yii::app()->request->getPost('groupId');
        $this->addMessage('state', 'fail');
        if (!$groupId) {
            $this->addMessage('message', 'groupId 不能为空');
            $this->showMessage();
        }
        $crit = new CDbCriteria();
        $crit->compare('group_id', $groupId);
        $crit->compare('status', WSurveyOptionGroup::STATUS_NORMAL);
        $crit->order = 'weight ASC';
        $optionsModel = WSurveyOptions::model()->findAll($crit);
        $optionsData = array();
        $i = 0;
        foreach ($optionsModel as $model) {
            $optionsData[$i]['id'] = $model->id;
            $optionsData[$i]['title_cn'] = $model->title_cn;
            $optionsData[$i]['title_en'] = $model->title_en;
            $optionsData[$i]['option_value'] = $model->option_value;
            $optionsData[$i]['weight'] = $model->weight;
            $i++;
        }
        unset($optionsModel);
        $this->addMessage('state', 'success');
        $this->addMessage('data', $optionsData);
        $this->showMessage();
    }

    // 新建、更新选项
    public function actionUpdateTopic()
    {
        if (!Yii::app()->request->isPostRequest) {
            return false;
        }
        $optionId = Yii::app()->request->getPost('optionId');
        $groupId = Yii::app()->request->getPost('groupId');
        $title_cn = Yii::app()->request->getPost('title_cn');
        $title_en = Yii::app()->request->getPost('title_en');
        $option_value = Yii::app()->request->getPost('option_value');
        $weight = Yii::app()->request->getPost('weight');

        $this->addMessage('state', 'fail');
        $optionModel = WSurveyOptions::model()->findByPk($optionId);
        if (!$optionModel) {
            $optionModel = new WSurveyOptions();
        }
        $optionModel->group_id = $groupId;
        $optionModel->title_cn = $title_cn;
        $optionModel->title_en = $title_en;
        $optionModel->option_value = $option_value;
        $optionModel->weight = $weight;
        $optionModel->update_user = $this->staff->uid;
        $optionModel->update_time = time();
        if (!$optionModel->save()) {
            $error = current($optionModel->getErrors());
            $this->addMessage('message', $error[0]);
            $this->showMessage();
        }
        $this->addMessage('state', 'success');
        $this->addMessage('message', 'success');
        $this->showMessage();
    }

    // 排序选项
    public function actionSortTopic()
    {
        if (!Yii::app()->request->isPostRequest) {
           return false;
        }
        $groupId = Yii::app()->request->getPost('groupId');
        $optionOrder = Yii::app()->request->getPost('option_order');

        $this->addMessage('state', 'fail');
        if (!$groupId || !$optionOrder) {
            $this->addMessage('message', '参数错误');
            $this->showMessage();
        }
        $groupModel = WSurveyOptionGroup::model()->findByPk($groupId);
        if (!$groupModel) {
            $this->addMessage('message', 'group 不存在');
            $this->showMessage();
        }
        $groupModel->option_order = serialize($optionOrder);
        $crit = new CDbCriteria();
        $crit->compare('id', $optionOrder);
        $crit->index = 'id';
        $optionsModel = WSurveyOptions::model()->findAll($crit);
        foreach ($optionOrder as $key => $optionId) {
            if (isset($optionsModel[$optionId])) {
                $optionsModel[$optionId]->weight = $key+1;
                $optionsModel[$optionId]->save();
            }
        }
        $this->addMessage('message', 'success');
        $this->addMessage('state', 'success');
        $this->showMessage();
    }

    // 删除选项
    public function actionDeleteTopic()
    {
        if (!Yii::app()->request->isPostRequest) {
           return false;
        }
        $optionId = Yii::app()->request->getPost('optionId');

        $this->addMessage('state', 'fail');
        $optionModel = WSurveyOptions::model()->findByPk($optionId);
        if (!$optionModel) {
           $this->addMessage('message', 'option 不存在');
           $this->showMessage();
        }
        $optionModel->status = WSurveyOptionGroup::STATUS_DELETED;
        if (!$optionModel->save()) {
            $error = current($optionModel->getErrors());
            $this->addMessage('message', $error[0]);
            $this->showMessage();
        }
        $this->addMessage('state', 'success');
        $this->addMessage('message', 'success');
        $this->showMessage();
    }

    // 获取侧边栏菜单
    public function getSubMenu()
    {   
       $subMenu = array(
           array('label'=>Yii::t('user','问卷管理'), 'url'=>array("/moperation/survey/index")),
           array('label'=>Yii::t('user','模版管理'), 'url'=>array("/moperation/survey/template")),
           array('label'=>Yii::t('user','选项管理'), 'url'=>array("/moperation/survey/group")),
       );
       return $subMenu;
    }



}
