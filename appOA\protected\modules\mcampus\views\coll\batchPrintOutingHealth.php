<style>
    .container-fluid{
        width:860px;
        margin:0 auto
    }
    .page{
        margin-top:20px
    }
    img{
        margin-top:10px;
        max-width:100%;
        height:auto
    }
    .sign{
        float: right;
        margin-right:20px;
        margin-bottom:20px;
        max-height: 15mm;
    }
    .sign img{
        width:200px;
        height: 110px;
    }
    @media print {
        .print_img{
            max-height: 290mm;
            display: block;
            margin: 0 auto;
        }
        .alwaysAttr{
            page-break-after:always;
        }

        .print_protocol_img
        {
            max-height: 290mm;
            display: block;
            margin: 0 auto;
        }
    }
</style>
<?php $studentNum = count($child_infos);$i=0;?>
<?php foreach ($child_infos as $child_id=>$child_info){?>
    <?php
    $i++;
    //将所有图片合并
    $all_photo[$child_id] = array();
    foreach ($step_data[$child_id]['medical_insurance_photo'] as $insurance_photo){
        if($step_data[$child_id]['medical_insurance']==1){
            $all_photo[$child_id][] = array(
                'type'=>'insurance',
                'photo'=>$insurance_photo,
            );
        }
    }
    foreach ($step_data[$child_id]['medicine_specify'] as $medicine_specify){
        if($medicine_specify['Rx']==1){
            foreach ($medicine_specify['Rx_photo_url'] as $Rx_photo_url){
                $all_photo[$child_id][] = array(
                    'type'=>'Rx',
                    'photo'=>$Rx_photo_url,
                );
            }
        }
    }
    ?>
    <div class="container-fluid">
        <div class ="row">
            <table class="table">
                <tbody>
                <tr >
                    <th width="25%"><?php echo Yii::t('reg', 'Name') ?></th>
                    <td colspan="3"><?php echo $child_info->getChildName() ?></td>
                </tr>
                <tr >
                    <th width="25%"><?php echo Yii::t('labels', 'Date of Birth') ?></th>
                    <td colspan="3"><?php echo $child_info->birthday_search ?></td>
                </tr>
                <tr >
                    <th width="25%"><?php echo Yii::t('labels', 'Class') ?></th>
                    <td colspan="3"><?php echo $child_info->ivyclass->title?></td>
                </tr>
                <tr >
                    <th width="25%"><?php echo Yii::t('coll', 'Dietary restrictions during this trip') ?></th>
                    <td colspan="3">
                        <?php if ($step_data[$child_id]['dietet'] == 'none'){
                            echo Yii::t('coll', 'None');
                        }elseif ($step_data[$child_id]['dietet'] == 'vegetarian_diet'){
                            echo Yii::t('coll', 'Vegetarian diet');
                        }elseif ($step_data[$child_id]['dietet'] == 'no_pork'){
                            echo Yii::t('coll', 'No pork');
                        }elseif ($step_data[$child_id]['dietet'] == 'food_allergy'){
                            echo Yii::t('coll', 'Food allergy');
                        }
                        ?>
                        <?php if(!empty($step_data[$child_id]['dietet_desc'])){
                            echo "（".$step_data[$child_id]['dietet_desc']."）";
                        }?>
                    </td>
                </tr>

                <tr >
                    <th width="25%"><?php echo Yii::t('coll', 'Medical alert & restrictions in details') ?></th>
                    <td colspan="3">
                        <?php foreach ($step_data[$child_id]['warning'] as $k=>$v){
                            if($v=='none'){
                                echo Yii::t('coll', 'None');
                            }elseif ($v=='Asthma'){
                                echo Yii::t('coll', 'Asthma');
                            }elseif ($v=='heart_Disease'){
                                echo Yii::t('coll', 'Heart Disease');
                            }elseif ($v=='Epilepsy'){
                                echo Yii::t('coll', 'Epilepsy');
                            }elseif ($v=='Sleepwalking'){
                                echo Yii::t('coll', 'Sleepwalking');
                            }elseif ($v=='Others'){
                                echo Yii::t('coll', 'Others');
                            }
                            echo " ";
                        }?>
                        <?php if(!empty($step_data[$child_id]['warning_specify'])){
                            echo "（".$step_data[$child_id]['warning_specify']."）";
                        }?>
                    </td>
                </tr>
                <tr >
                    <th width="25%"><?php echo Yii::t('coll', 'Will the student bring medication on this trip?') ?></th>
                    <td colspan="3">
                        <?php echo $step_data[$child_id]['medicine']==1 ?  Yii::t('coll','Yes') : Yii::t('coll','No') ?>
                    </td>
                </tr>
                <?php if($step_data[$child_id]['medicine']==1){?>
                        <?php foreach ($step_data[$child_id]['medicine_specify'] as $k=>$v){?>
                        <tr>
                            <th><?php echo  Yii::t('coll','Medication name').' '.($k+1) ?></th>
                            <td><?php echo $v['name']?></td>
                        </tr>
                        <tr>
                            <th><?php echo Yii::t('coll', 'Dosage / time')?></th>
                            <td><?php echo $v['dose']?></td>
                        </tr>
                        <tr>
                            <th><?php echo Yii::t('coll', 'Dosing time / symptoms')?></th>
                            <td><?php echo $v['use_time']?></td>
                        </tr>
                        <tr>
                            <th><?php echo Yii::t('coll','Date of administration From')?></th>
                            <?php if(count($v['use_days']) >1){?>
                                <td><?php echo $v['use_days'][0].' '.Yii::t('coll', 'To').' '.$v['use_days'][1]?></td>
                            <?php }else{?>
                                <td><?php echo $v['use_days'][0]?></td>
                            <?php }?>
                        </tr>
                        <tr>
                            <th><?php echo Yii::t('coll', 'Please specify if needed')?></th>
                            <td><?php echo $v['medical_special'];?></td>
                        </tr>
                        <tr>
                            <th><?php echo Yii::t('coll', 'Is it prescription medication')?></th>
                            <td><?php echo $v['Rx']==1 ? Yii::t('coll','Yes')  : Yii::t('coll','No') ?></td>
                        </tr>
                        <?php if($v['Rx'] == 1){?>
                        <tr>
                            <th><?php echo Yii::t('coll', 'Doctor\'s prescription') ?></th>
                            <td>
                                <?php echo Yii::t('coll', 'See annex')?>
                            </td>
                        </tr>
                        <?php }?>
                        <?php }?>
                <?php }?>

                <tr >
                    <th width="25%"><?php echo Yii::t('coll', 'Does the student have a medical insurance provider') ?></th>
                    <td colspan="3">
                        <?php echo $step_data[$child_id]['medical_insurance']==1 ? Yii::t('coll','Yes')  : Yii::t('coll','No')  ?>
                    </td>
                </tr>
                <?php if($step_data[$child_id]['medical_insurance']==1){?>
                    <tr >
                        <th width="25%"><?php echo Yii::t('coll', 'Type of the insurance') ?></th>
                        <td colspan="3">
                            <?php if($step_data[$child_id]['medical_type']=='Accidental'){
                                echo Yii::t('coll', 'Accidental medical insurance');
                            }elseif ($step_data[$child_id]['medical_type']=='High-End'){
                                echo Yii::t('coll', 'High-End medical insurance');
                            }else{
                                echo Yii::t('coll', 'Other medical insurance (Applicable for outpatient and emergency services)');
                            }?>
                        </td>
                    </tr>
                    <tr >
                        <th width="25%"><?php echo Yii::t('coll', 'Medical insurance provider') ?></th>
                        <td  colspan="3"><?php echo $step_data[$child_id]['medical_provider']?></td>
                    </tr>
                    <tr >
                        <th width="25%"><?php echo Yii::t('coll', 'Medical insurance policy number') ?></th>
                        <td colspan="3"><?php echo $step_data[$child_id]['medical_insurance_number']?></td>
                    </tr>
                    <tr >
                        <th width="25%"><?php echo Yii::t('coll', 'Medical insurance policy expiry date') ?></th>
                        <td colspan="3"><?php echo $step_data[$child_id]['medical_insurance_validity']?></td>
                    </tr>
                    <tr>
                        <th><?php echo Yii::t('coll', 'insurance card/policy page') ?></th>
                        <td>
                            <?php if(count($step_data[$child_id]['medical_insurance_photo']) >0 ){
                                echo Yii::t('coll', 'See annex');
                            }else{
                                echo Yii::t('coll', 'None');
                            }?>
                        </td>
                    </tr>
                <?php }?>
                </tbody>
            </table>
<!--        有附件才添加分页-->
            <?php if(!empty($all_photo[$child_id])){?>
                <div style="page-break-after:always;"></div>
            <?php }?>
        </div>
<!--        有附件才展示-->
        <?php if(!empty($all_photo[$child_id])){?>
            <div>
                <?php foreach ($all_photo[$child_id] as $k=>$val){?>
                    <div>
                        附件页
                        <?php if($val['type'] == 'Rx'){
                            echo Yii::t('coll', 'Doctor\'s prescription');
                        }else{
                            echo  Yii::t('coll', 'insurance card/policy page');
                        }?>
                        <?php echo $child_info->getChildName() ?>
                        <?php echo $child_info->ivyclass->title?>
                    </div>
                    <p class="pageBreak">
                        <?php if(count($all_photo[$child_id]) == $k+1){?>
                            <img  class="img-responsive print_img" src="<?php echo $val['photo']; ?>">
                        <?php }else{?>
                            <img  class="img-responsive print_img alwaysAttr" src="<?php echo $val['photo']; ?>">
                        <?php }?>
                    </p>
                <?php }?>
            </div>
        <?php }?>
    </div>
    <?php if($studentNum != $i){?>
    <div style="page-break-after:always;"></div>
    <?php }?>
<?php }?>

