
<style>
.nav-wizard > li{
    margin:5px 0
}
.nav-wizard > li>a:hover{
   background:#d5d5d5
}
[v-cloak] {
    display: none !important;
}
.reportAmend{
    display: none;
}
.reportAmendCheckbox {
    cursor: pointer;
}
.rotate_half {
    transform: rotate(-180deg);
    -ms-transform: rotate(-180deg);
    -moz-transform: rotate(-180deg);
    -webkit-transform: rotate(-180deg);
}
.reportTbody > tr > td, .reportTbody > tr > th {
            vertical-align: middle !important;
           /* text-align: center;*/
}
.table > tbody + tbody {
   border-top: none
}
.spinner {
  margin: 100px auto;
  width: 90px;
  height: 90px;
  position: relative;
  text-align: center;
   
  -webkit-animation: rotate 2.0s infinite linear;
  animation: rotate 2.0s infinite linear;
}
.dot1, .dot2 {
  width: 60%;
  height: 60%;
  display: inline-block;
  position: absolute;
  top: 0;
  background-color: #428BCA;
  border-radius: 100%;
   
  -webkit-animation: bounce 2.0s infinite ease-in-out;
  animation: bounce 2.0s infinite ease-in-out;
}
.dot2 {
  top: auto;
  bottom: 0px;
  -webkit-animation-delay: -1.0s;
  animation-delay: -1.0s;
} 
@-webkit-keyframes rotate { 100% { -webkit-transform: rotate(360deg) }}
@keyframes rotate { 100% { transform: rotate(360deg); -webkit-transform: rotate(360deg) }}
 
@-webkit-keyframes bounce {
  0%, 100% { -webkit-transform: scale(0.0) }
  50% { -webkit-transform: scale(1.0) }
}
@keyframes bounce {
  0%, 100% {
    transform: scale(0.0);
    -webkit-transform: scale(0.0);
  } 50% {
    transform: scale(1.0);
    -webkit-transform: scale(1.0);
  }
}
</style>
<div class="container-fluid">
<ol class="breadcrumb">
    <li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('//mcampus/default/index')) ?></li>
    <li><?php echo CHtml::link(Yii::t('site', 'Routines'), array('//mcampus/default/index')) ?></li>
    <li class="active"><?php echo Yii::t('site', 'ASA Management') ?></li>
</ol>

<div class="row">
    <div class="col-md-2 mb10">
        <?php
        $this->widget('zii.widgets.CMenu', array(
            'items' => $this->module->getMenu(),
            'id' => 'pageCategory',
            'htmlOptions' => array('class' => 'nav nav-pills nav-stacked background-gray'),
            'activeCssClass' => 'active',
        ));
        ?>
    </div>
    <div class="col-md-2">
        <?php
        $this->widget('zii.widgets.CMenu', array(
            'items' => $this->module->getAttendSubMenu(),
            'id' => 'pageCategory',
            'htmlOptions' => array('class' => 'nav nav-pills nav-stacked background-gray'),
            'activeCssClass' => 'active',
        ));
        ?>
    </div>
    <div class="col-md-8" >    
        <div class="mb10 row" id="mon">
            <div class="col-sm-2 form-group">
            <div class="btn-group">
              <button type="button" class="btn btn-default dropdown-toggle month" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                请选择 <span class="caret"></span>
              </button>
              <ul class="dropdown-menu">

                <li v-for="mon in month"><a :href ="'<?php echo Yii::app()->createUrl('/masa/pay/summaryTable', array('branchId' => $this->branchId)); ?>&month='+mon+''" >{{mon}}</a></li>
              </ul>
            </div>                
            </div>
        </div> 
        <div class="spinner">
          <div class="dot1"></div>
          <div class="dot2"></div>
        </div>
        <div id='list'>
        <div class="listul"  v-cloak >
            <ul class="nav nav-wizard" style="padding-bottom: 20px;">
                <li>
                    <a href='javascript:;'>{{time}} ASA工资结算 </a>
                </li>
                <li>
                    <a href="<?php echo $this->createUrl('attend/index'); ?>">课程考勤</a>
                </li>
                <li>
                    <a href="<?php echo $this->createUrl('attend/otherStaffs'); ?>">工作人员考勤</a>
                </li>
                <li>
                    <a href="<?php echo $this->createUrl('attend/statistics'); ?>">分成统计</a>
                </li>
                <li>
                    <a  href='<?php echo $this->createUrl('index'); ?>'>特殊调整 / 结算设置</a>
                </li>
                <li  class="active">
                    <a href='javascript:void(0)'>工资汇总表</a>
                </li>
                 <li>
                    <a href="javascript:void(0)">提交</a>
                </li>
            </ul>
        </div>
        <div class="clearfix"></div> 
        <div class="tab-pane fade in active" >
          <div v-if="vendors.length>0">       
            <div class="panel panel-default">
              <div class="panel-heading">{{time}}劳务工资表</div>
              <table class="table"  >
                
                 <thead  class="reportTbody">
                 <tr>
                      <th>#</th>
                      <th>姓名</th>
                      <th>身份证号/护照号</th>
                      <th>类型</th>
                      <!-- <th>费用合并（IVY KG 使用）</th> -->
                      <th>课程</th>
                      <th>职位</th>                          
                      <th>课时费×课时=小计</th>
                      <th>调整金额</th>
                      <th>总计</th>
                      <th>开户行预留姓名<br>银行账号</th>
                  </tr>
                  </thead>
                  <tbody class="reportTbody" v-for="(tData,indexs) in vendors">
                     <tr v-for="(course,cid,index) in tData.courses">
                        <td width="50" :rowspan='arrLength(tData.courses)' v-if='index===0'>{{indexs+1}}</td>
                        <td width="80" :rowspan='arrLength(tData.courses)' v-if='index===0'>{{tData.name}} <span v-if="tData.fee_merge == 1" class="label label-primary">费用合并</span></td>
                        <td width="80" :rowspan='arrLength(tData.courses)' v-if='index===0'>{{tData.identify}}</td>
                        <td width="50" :rowspan='arrLength(tData.courses)' v-if='index===0'>{{tData.type}}</td>
                         <td width="100">
                            <div v-for="(data,index) in course">
                                <p  v-if='cid == time'>{{data.course_name}}</p>
                                <p  v-else> {{cid}}  : {{data.course_name}}</p> 
                            </div>
                        </td>
                        <td width="80">
                            <div v-for="(data,index) in course">
                                <p>{{data.position}}</p>       
                            </div>
                        </td>
                        <td width="100">   
                          <div v-for="(data,index) in course">
                            <div v-if='data.course_count.constructor!=Array'>
                               <p v-if='data.assistant_pay>0'>
                                 {{data.unit_price}} × {{len1(data.course_count)}} - {{data.assistant_pay}}=
                                {{parsefloat(data.unit_price) * data.course_count - parsefloat(data.assistant_pay)}}
                                </p>
                                <p v-else> {{data.unit_price}} × {{len1(data.course_count)}} =
                                 {{parsefloat(data.unit_price) * data.course_count}}</p>
                            </div>
                            <div v-else>
                                <p v-if='data.assistant_pay>0'>{{data.unit_price}} × {{and(data.course_count)}}  - {{data.assistant_pay}}={{parsefloat(data.unit_price) *add(data.course_count)  - parsefloat(data.assistant_pay)}}</p>
                                <p v-else>
                                    {{data.unit_price}} × {{and(data.course_count)}}={{parsefloat(data.unit_price) *add(data.course_count)}}
                                </p>  
                            </div>
                          </div>
                        </td>
                        <td width="100" :rowspan='arrLength(tData.courses)' v-if='index===0'>
                            <div  v-for="(course,cid,index) in tData.variation_amout">
                             <div v-for="(courses,cids,indexs) in tData.variation_memo">
                             <div v-if='cid == cids'>  
                                 <div v-if="course==0 || course==0.00"></div>                               
                                 <div v-else>
                                    <p>金额： {{course}}</p>
                                    <p v-html="'备注: '+ courses+''"></p>
                                 </div>
                             </div>
                             </div>                     
                            </div>
                        </td>
                        <td width="50" :rowspan='arrLength(tData.courses)' v-if='index===0'>{{tData.amount}}</td>
                        <td width="80" :rowspan='arrLength(tData.courses)' v-if='index===0'>{{tData.bank_name}}<br/>{{tData.bank_account}}</td>
                    </tr>
                </tbody>
              </table>
            </div>
          </div>
          <div  v-else  class="panel panel-default">
            <div class="panel-body">暂无数据</div>  
          </div> 
            <div>
              <p class="pull-right">总额 : {{parsefloat(list)}}</p>
            </div>
            <div class="clearfix"></div>
            <div class="pull-right">
                <div v-if='status==10' class="">
                   <div class="checkbox">
                    <label>
                      <input type="checkbox" class="check" v-model="canSub" >行政人员确认该列表无误,提交后不可修改
                    </label>
                  </div>
                   <div class="modal-footer">
                    <button :class="{ 'disabled': !canSub} " v-bind:disabled="!canSub"
                            class="btn btn-primary modifybtn btncheck"
                            @click="btnsubmit()">提交</button>
                  </div>
                </div>
                <div v-else  class="">
                  <div class="checkbox">
                    <label v-if='canSub==false'>
                      <input type="checkbox" checked onclick="return false">行政人员确认该列表无误,提交后不可修改
                    </label>
                    <label v-else>
                      <input type="checkbox"  v-model="canSub" onclick="return false">行政人员确认该列表无误,提交后不可修改
                    </label>
                  </div>
                  <div class="modal-footer">
                    <button disabled="disabled" class="btn btn-primary" disabled="disabled">已提交</button>
                  </div>
                </div>
                <div class="daa" v-if='schoolStatus==0'>
                  <div class="" v-if='status==15'>
                     <div class="checkbox">
                      <label>
                        <input type="checkbox" class="check" v-model="leader" >园长确认该列表无误,提交后不可修改
                      </label>
                    </div>
                     <div class="modal-footer">
                         <button class="btn btn-primary" @click="overrule()">驳回</button>
                      <button :class="{ 'disabled': !leader} " v-bind:disabled="!leader"
                              class="btn btn-primary modifybtn leadercheck"
                              @click="leadersubmit()">提交</button>
                    </div>
                  </div>
                  <div v-else  class="">
                    <div v-if='status==10'>
                      <div class="checkbox">
                        <label>
                          <input type="checkbox" onclick="return false">园长确认该列表无误,提交后不可修改
                        </label>
                      </div>
                      <div class="modal-footer">
                        <button disabled="disabled" class="btn btn-primary" disabled="disabled">提交</button>
                      </div>
                    </div>
                    <div v-else>
                      <div class="checkbox">
                        <label v-if='leader==false'>
                          <input type="checkbox" checked onclick="return false">园长确认该列表无误,提交后不可修改
                        </label>
                        <label v-else>
                          <input type="checkbox"  v-model="leader" onclick="return false">园长确认该列表无误,提交后不可修改
                        </label>
                      </div>
                      <div class="modal-footer">
                        <button disabled="disabled" class="btn btn-primary" disabled="disabled">已提交</button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>               
         </div>
    </div> 
</div> 
    <script>
     var date1 = '2017-11';
        var date=new Date;       
        var dataArr = []; 
        var month=date.getMonth()+1;
            month =(month<10 ? "0"+month:month); 
        var date2 = (date.getFullYear().toString()+'-'+month.toString());
        date1 = date1.split('-');
        date1 = parseInt(date1[0]) * 12 + parseInt(date1[1]);
        date2 = date2.split('-');
        date2 = parseInt(date2[0]) * 12 + parseInt(date2[1]);
        var cha = Math.abs(date1 - date2);        
        date.setMonth(date.getMonth()+1, 1)
        for (var i = 0; i < cha; i++) {
            date.setMonth(date.getMonth() - 1);
            var month=date.getMonth()+1;
            month =(month<10 ? "0"+month:month); 
            dataArr.push(date.getFullYear().toString()+month.toString())
        }    
      $('#list').hide()
      function getQueryString(name) { 
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i"); 
            var r = window.location.search.substr(1).match(reg); 
            if (r != null) return unescape(r[2]); return null; 
        }
        var mon = new Vue({
        el: "#mon",
        data: {          
            month:dataArr,
        },
      });
        if(getQueryString("month")==null || getQueryString("month")==''){
            $('.spinner').remove()
        }
        if(getQueryString("month")!=null && getQueryString("month")!=''){
           $.ajax({
            url:'<?php echo $this->createUrl('getTableData'); ?>',
            type: 'get',
            dataType:'json',
            success:function(data){
                $('.spinner').remove()
                if(data.state='success'){
                    list.vendors=data.data.vendors   
                    list.list=data.data.total
                    list.schoolStatus=data.data.schoolStatus 
                    $('#list').show()
                }else{
                    resultTip({error: 'warning', msg: data.message});
                }
               
            },error:function(data){
                resultTip({error: 'warning', msg: '请求错误'});
                $('.spinner').remove()
            }
        }) 
       $('.month').html(getQueryString("month") +' <span class="caret"></span>') 
     var list = new Vue({
        el: "#list",
        data: {          
            month:dataArr,       
            vendors:'',          
            list:'',
            schoolStatus:'',
            canSub: false,
            leader:false,
            time:getQueryString("month"),
            status: '<?php echo $this->report->status; ?>'
        },
        methods: {
                parsefloat(num) {
                    return parseFloat(num);
                },
                arrLength(arr) {
                   return Object.keys(arr).length;
                },
                add(add) {
                  var sum = 0;
                  for(var i= 0 ;i< add.length; i++) {
                        sum += parseInt(add[i]);
                  }
                  return sum;
                },
                and(add) {
                  var sum=''
                  for(var i= 0 ;i< add.length; i++) {
                          sum+=add[i]+'+'
                     }
                     var and='('+sum.substring(0,sum.length-1)+')'
                  return and;
                },
                len1(add) {
                  var sum=''
                  for(var i= 0 ;i< add.length; i++) {
                          sum+=add[i]
                     }
                  return sum;
                },


                overrule(){
                    $('.modifybtn').addClass('disabled').attr('disabled', 'disabled')
                    $.ajax({
                        url:'<?php echo $this->createUrl('overrule'); ?>',
                        type: 'post',
                        dataType:'json',
                        success:function(data){
                            if(data.state=='success'){
                                resultTip({"msg": data.message})
                                $('.check').prop("checked",true)
                                Vue.set(list, 'status', '10');
                                console.log('<?php echo $this->report->status; ?>')
                            }else{
                                resultTip({error: 'warning', msg: data.message});
                                $('.modifybtn').removeClass('disabled').removeAttr('disabled');
                            }
                        },error:function(data){
                            resultTip({error: 'warning', msg: '请求错误'});
                            $('.modifybtn').removeClass('disabled').removeAttr('disabled');
                        }
                    })
                },
                btnsubmit(){  
                    $('.modifybtn').addClass('disabled').attr('disabled', 'disabled')            
                    $.ajax({
                        url:'<?php echo $this->createUrl('saveSummaryTable'); ?>',
                        type: 'post',
                        dataType:'json',
                        success:function(data){
                            if(data.state=='success'){
                                resultTip({"msg": data.message})
                                $('.check').prop("checked",true)
                                Vue.set(list, 'status', '15');
                             }else{
                              resultTip({error: 'warning', msg: data.message});
                              $('.modifybtn').removeClass('disabled').removeAttr('disabled');
                             }
                        },error:function(data){
                            resultTip({error: 'warning', msg: '请求错误'});
                          $('.modifybtn').removeClass('disabled').removeAttr('disabled');
                        }
                    }) 
              },
              leadersubmit(){
                 $('.modifybtn').addClass('disabled').attr('disabled', 'disabled')            
                    $.ajax({
                        url:'<?php echo $this->createUrl('directorCheck'); ?>',
                        type: 'post',
                        dataType:'json',
                        success:function(data){
                            if(data.state=='success'){
                                resultTip({"msg": data.message})
                                $('.check').prop("checked",true)
                                Vue.set(list, 'status', '20');
                                console.log('<?php echo $this->report->status; ?>')
                             }else{
                              resultTip({error: 'warning', msg: data.message});
                              $('.modifybtn').removeClass('disabled').removeAttr('disabled');
                             }
                        },error:function(data){
                            resultTip({error: 'warning', msg: '请求错误'});
                          $('.modifybtn').removeClass('disabled').removeAttr('disabled');
                        }
                    }) 
              }
        }
      });
        }
    </script>               
<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>