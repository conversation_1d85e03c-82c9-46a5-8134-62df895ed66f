<?php
$month = array();
for ($m = 1; $m < 13; $m++) {
    $month[sprintf("%02d", $m)] = sprintf("%02d", $m);
}

$year = array();
for ($y = 2016; $y <= date('Y', time()); $y++) {
    $year[$y] = $y;
}
$yearData = ($yearData) ? $yearData : date("Y", time());
$monthData = ($monthData) ? sprintf("%02d", $monthData) : date("m", time());

?>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('//mcampus/default/index')) ?></li>
        <li><?php echo CHtml::link(Yii::t('site', 'Routines'), array('//mcampus/default/index')) ?></li>
        <li class="active"><?php echo Yii::t('site', '安全事故管理') ?></li>
    </ol>

    <div class="row">
        <div class="col-md-12 mb10">
            <?php
            $this->widget('zii.widgets.CMenu', array(
                'items' => $this->module->getMenu(),
                'id' => 'pageCategory',
                'htmlOptions' => array('class' => 'nav nav-tabs'),
                'activeCssClass' => 'active',
            ));
            ?>
        </div>
        <div class="col-md-12">
            <div class="row">
                <!-- 搜索框 -->
                <form class="" action="<?php echo $this->createUrl('index', array('type' => $type)); ?>"
                      method="get">
                    <?php echo Chtml::hiddenField('branchId', $this->branchId); ?>
                    <?php echo Chtml::hiddenField('type', $type); ?>
                    <div class="col-sm-2 form-group">
                        <?php echo CHtml::dropDownList('year', $yearData, $year, array('class'=> 'form-control', 'empty' => Yii::t('teaching', '年份'))) ?>
                    </div>
                    <div class="col-sm-2 form-group">
                        <?php echo CHtml::dropDownList('month', $monthData, $month, array('class'=> 'form-control', 'empty' => Yii::t('teaching', '月份'))) ?>
                    </div>
                    <div class="">
                        <div class="">
                            <button class="btn btn-default ml5" type="submit">
                                <span class="glyphicon glyphicon-search"> </span>
                            </button>
                        </div>
                    </div>
                </form>
            </div>
            <?php if($month): ?>
            <div class="row">
                <div class="col-md-2 mb10">
                    <ul class="nav nav-pills nav-stacked background-gray" id="pageCategory">
                        <?php if($safetyData): ?>
                            <li>
                                <a href="<?php echo $this->createUrl('showCheck', array('year'=> $yearData, 'month' => $monthData, 'type' => $type)) ?>">
                                    <?php echo Yii::t('safety','Monthly self-inspection table') ?>
                                </a>
                            </li>
                            <?php foreach($safetyData as $key=>$item): ?>
                                <li <?php echo ($id == $key) ? "class='active'" : '' ; ?>>
                                    <a href="<?php echo $this->createUrl('index', array('id' => $key,'year'=> $yearData, 'month' => $monthData, 'mid' => $item['mid'], 'type' => $type)) ?>">
                                        <div class="media">							        
							             	 <span class="pull-left label label-<?php echo $item['status'] == 1 ? 'success' : 'warning' ; ?> ">
                                            <?php echo $item['status'] == 1 ? '已提交' : '未提交' ; ?>
                                        </span>
							            <div class="media-body">
							               <?php echo $item['name']; ?>
							            </div>
							        
							        </div>
                                    </a>
                                </li>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </ul>
                </div>
                <?php if($safetySelfCategoryArr): ?>
                <div class="col-md-10">
                    <div class="mb10">
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <tr>
                                    <th colspan="8">
                                        <?php echo $model->getName(); ?>
                                        <a href="<?php echo $this->createUrl('print', array('id' => $id, 'year'=> $yearData, 'mid' => $mid, 'month' => $monthData, 'type' => $type)) ?>"  class="btn btn-sm btn-info pull-right" role="button" target="_blank"><?php echo Yii::t('global', 'Print');?></a>
                                    </th>
                                </tr>
                                <tr>
                                    <th width="100" rowspan="2"><?php echo Yii::t('safety','Category') ?></th>
                                    <th colspan="2" rowspan="2"><?php echo Yii::t('safety','Detailed rules') ?></th>
                                    <th colspan="2"><?php echo Yii::t('safety','Work arrangement') ?></th>
                                    <th rowspan="2"><?php echo Yii::t('safety','Inspection record') ?></th>
                                    <th rowspan="2"><?php echo Yii::t('safety','Rectification measures (measure+deadline)') ?></th>
                                    <th width="200"><?php echo Yii::t('safety','Self-inspection score') ?></th>
                                </tr>
                                <tr>
                                    <td><?php echo Yii::t('safety','Responsible person (name)') ?></td>
                                    <td><?php echo Yii::t('safety','Record/document') ?></td>
                                    <td><?php echo Yii::t('safety','0~5 points or N/A (not applicable)') ?></td>
                                </tr>
                            <?php

                            foreach ($safetySelfCategoryArr as $k=>$item): ?>
                                <?php if(isset($ruleArr) && isset($ruleArr[$k])): ?>
                                    <tr>
                                        <td rowspan="<?php echo count($ruleArr[$k]) + 1 ?>"><?php echo $item;  ?></td>
                                    </tr>
                                    <?php
                                    $i = 1;
                                    foreach ($ruleArr[$k] as $ks=>$val): ?>
                                        <tr>
                                            <td><?php echo $i ?></td>
                                            <td><?php echo $val ?></td>
                                            <td id="principal<?php echo $ks ?>"><?php echo isset($safetySelfItemArr[$ks])  ? $safetySelfItemArr[$ks]['principal'] : '' ?></td>
                                            <td><?php echo  isset($safetySelfItemArr[$ks])  ? $safetySelfItemArr[$ks]['document'] : ''; ?></td>
                                            <td><?php echo CHtml::textArea('record' . $ks, isset($safetySelfItemArr[$ks])  ? $safetySelfItemArr[$ks]['record'] : '', array('class' => 'form-control')); ?></td>
                                            <td><?php echo CHtml::textArea('impore' . $ks, isset($safetySelfItemArr[$ks])  ? $safetySelfItemArr[$ks]['impore'] : '', array('class' => 'form-control')); ?></td>
                                            <td>
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <?php echo CHtml::dropDownList('score' . $ks, isset($safetySelfItemArr[$ks])  ? $safetySelfItemArr[$ks]['score'] : '',array(0=> 0,1=>1,2=>2,3=>3,4=>4,5=>5), array('empty' => 'N/A', 'class'=>'form-control')) ?>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <input type="button" class="J_modal btn btn-primary btn-sm" value="<?php echo Yii::t('global', 'OK')?>" onclick="save(<?php echo $k; ?>,<?php echo $ks; ?>,<?php echo $safetySelfItemArr[$ks]['id'] ?>)" />
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php
                                    $i++;
                                    endforeach; ?>
                                <?php endif; ?>
                            <?php endforeach; ?>
                            </table>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php
$this->renderPartial('//layouts/common/branchSelectBottom');

?>
<script>
    function save(cid,rid) {
        var record = $("#record"+rid).val();
        var impore = $("#impore"+rid).val();
        var score = $("#score"+rid).val();
        if( !record || !impore){
            alert('不能为空');
            return false;
        }

        $.ajax({
            type: "POST",
            url: "<?php echo $this->createUrl('updateRules')?>",
            data: {year:<?php echo $yearData ?>, month:<?php echo $monthData ?>,  record:record, impore:impore, score:score, cid:cid, rid:rid, sid:<?php echo $id ?>,mid:<?php echo $mid; ?>},
            dataType: "json",
            success: function(data){
                if(data.state == 'success'){
                    resultTip({msg: '增加成功', callback: function(){
                        $("#principal"+rid).text(data.data.uid);
                    }});
                }else{
                    resultTip({error: 'warning', msg: data.message});
                }
            }
        });
    }
</script>
