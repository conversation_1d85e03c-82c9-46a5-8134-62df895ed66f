<?php
$this->breadcrumbs=array(
	'Points Orders'=>array('admin'),
	'Manage',
);
?>

<div class="table_list">
	<table width="100%">
		<colgroup>
			<col width="150">
		</colgroup>
		<thead>
			<tr>
				<td>订单管理</td>
				<td></td>
			</tr>
		</thead>
		<tbody>
			<tr>
				<td>总部管理节点</td>
				<td class="J_ce">
					
					<?php
					$statusList = PointsOrder::model()->statusList();
					foreach(array(PointsStatus::STATS_CREATED, PointsStatus::STATS_CONFIRMED, PointsStatus::STATS_READYTOSHIP) as $stat){
						echo CHtml::link($statusList[$stat].'(<span>'.count(${'sData'.$stat}).'</span>)', '#', array('class'=>'mr15 btn', 'rel'=>'s'.$stat));
					}
					?>
				
				</td>
			</tr>
			<tr>
				<td>校园管理节点</td>
				<td class="J_ce">
					<?php
					foreach(array(PointsStatus::STATS_SHIPPING, PointsStatus::STATS_RECEIVED) as $stat){
						echo CHtml::link($statusList[$stat].'(<span>'.count(${'sData'.$stat}).'</span>)', '#', array('class'=>'mr15 btn', 'rel'=>'s'.$stat));
					}
					
					echo CHtml::link($statusList[PointsStatus::STATS_COMPLETED], '#', array('class'=>'mr15 btn', 'rel'=>'s210'));
					?>
					
				
				</td>
			</tr>
		</tbody>
	</table>
</div>

<div class="fmain">
    <?php foreach ($statusList as $s=>$v):?>
    <div id="s<?php echo $s?>" class="J_wr" style="display: none;">
        <?php $this->renderPartial('orderview'.$s, array('sData'=>${'sData'.$s}, 'branchId'=>$branchId, 'v'=>$v, 's'=>$s))?>
    </div>
    <?php endforeach;?>
</div>

<script type="text/javascript">
function pack(_this)
{
    $(_this).attr('disabled', 'disabled');
    $.ajax({
        'type': 'post',
        'url': $(_this.form).attr('action'),
        'data': $(_this.form).serialize(),
        'dataType': 'json',
        'success': function(data){
            if (data.status == 'success'){
                $.fn.yiiGridView.update("way_list");
                $.fn.yiiGridView.update("ready_list");
                $('#mydialog').dialog('close');
            }
        }
    });
}
$('.split').live('click', function(){
    if (confirm('确定拆分此包吗？')){
        $.get($(this).attr('href'), null, function(data){
            if (data.status == 'success'){
                reloadPage(window)
            }
        }, 'json')
    }
    return false;
});
$('.receive').live('click', function(){
    if (confirm('您确定已经收到此包了？')){
        $.get($(this).attr('href'), null, function(data){
            if (data.status == 'success'){
                reloadPage(window)
            }
        }, 'json')
    }
    return false;
});
$('.godone').live('click', function(){
    if (confirm('您确定直接发放次订单吗？')){
        $.get($(this).attr('href'), null, function(data){
            if (data.state == 'success'){
                reloadPage(window)
            }
        }, 'json')
    }
    return false;
});
$('.J_ce a.btn').on('click', function(){
    $('.fmain div.J_wr').each(function(){
        $(this).hide();
    });
    $('#'+$(this).attr('rel')).show();
    return false;
})
$(document).ready(function(){
    var schoolsCount = <?php echo $schoolsCount;?>;
    for(var schoolid in schoolsCount){
        $('#branch-selector div.list a#school_'+schoolid).append('<i class="extra-number">'+schoolsCount[schoolid]+'</i>');
    }
})
</script>

<style type="text/css">
    fieldset{
        border: 3px solid #E5F1F4;
        margin-bottom: 20px;
    }
    fieldset legend{
        margin-left: 20px;
        font-weight: bold;
    }
    fieldset div.fmain{
        padding: 0 20px;
    }
</style>