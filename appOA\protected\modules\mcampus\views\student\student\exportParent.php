<style>
    #clsa td{
       border: solid 1px #ccc;
    }
    .width-reports{
        width:100px;
    }

</style>
<div class="panel panel-default">
    <div class="panel-heading"><?php echo $classeTitle->title ?></div>
    <table class="table" id="clsa">
        <tr>
            <td><?php echo Yii::t('user','Child Name')?></td>
            <td><?php echo Yii::t('user','父母姓名')?></td>
            <td><?php echo Yii::t('user','父母电话')?></td>
            <td><?php echo Yii::t('user','父母邮箱')?></td>
            <td><?php echo Yii::t('user','父母微信绑定')?></td>
            <td><?php echo Yii::t('user','父母密码')?></td>
            <td><?php echo Yii::t('user','父母登陆最后时间')?></td>
        </tr>
        <?php foreach($date as $item): ?>
        <tr>
            <td rowspan="2"><?php echo $item['name'] ?></td>
            <td><?php echo $item['father']['name'] ?></td>
            <td><?php echo $item['father']['mphone'] ?></td>
            <td><?php echo $item['father']['email'] ?></td>
            <td><?php echo $item['father']['asd'] ?></td>
            <td><?php echo $item['father']['passwordText'] .$item['father']['passChanged']?></td>
            <td><?php echo $item['father']['loginInfo'] ?></td>

        </tr>
        <tr>
            <td><?php echo $item['mother']['name'] ?></td>
            <td><?php echo $item['mother']['mphone'] ?></td>
            <td><?php echo $item['mother']['email'] ?></td>
            <td><?php echo $item['mother']['asd'] ?></td>
            <td><?php echo $item['mother']['passwordText'] . $item['mother']['passChanged']?></td>
            <td><?php echo $item['mother']['loginInfo'] ?></td>
        </tr>
        <?php endforeach; ?>
    </table>
</div>