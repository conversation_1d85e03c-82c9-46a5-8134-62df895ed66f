<?php

class MasaModule extends CWebModule
{
	public function init()
	{
		// this method is called when the module is being created
		// you may place code here to customize the module or the application

		// import the module-level models and components
//		$this->setImport(array(
//			'charts.models.*',
//			'charts.components.*',
//		));
        Yii::import('common.models.asainvoice.*');
	}

	public function beforeControllerAction($controller, $action)
	{
        if(parent::beforeControllerAction($controller, $action))
        {
            // this method is called before any module controller action is performed
            // you may place customized code here
            return true;
        }
        else
            return false;
	}

    public function getMenu()
    {
        $action = Yii::app()->urlManager->parseUrl(Yii::app()->request);

        $mainMenu = array(
            array('label'=>Yii::t('asa','Staffs and Vendors'), 'url'=>array("/masa/vendor/index")),
            array('label'=>Yii::t('asa','Program Management'), 'url'=>array("/masa/course/index")),
            array('label'=>Yii::t('asa','Invoice Management'), 'url'=>array("/masa/invoice/index")),
            array('label'=>Yii::t('asa','Invoice List'), 'url'=>array("/masa/invoice/summary")),
            array('label'=>Yii::t('asa','Staff Attendance'), 'url'=>array("/masa/attend/index"), 'active'=>in_array($action, array('masa/attend/index', 'masa/attend/otherStaffs', 'masa/attend/report', 'masa/attend/statistics'))),
            array('label'=>Yii::t('asa','Cash Handover'), 'url'=>array("/masa/handover/index")),
            // array('label'=>Yii::t('asa','Reports'), 'url'=>array("/masa/report/index")),
            array('label'=>Yii::t('asa','Refunds'), 'url'=>array("/masa/refund/index"), 'active'=>in_array($action, array('masa/refund/index', 'masa/refund/refundOk'))),
            array('label'=>Yii::t('asa','Parents Feedbacks'), 'url'=>array("/masa/feedback/index"), 'active'=>in_array($action, array('masa/feedback/index', 'masa/feedback/demand'))),
            array('label'=>Yii::t('asa','报销及付款'), 'url'=>array("/masa/expense/index")),
            array('label'=>Yii::t('asa','Class Switch'), 'url'=>array("/masa/exchange/index")),
            array('label'=>Yii::t('asa','Program Revenue'), 'url'=>array("/masa/income/index")),
        );
        return $mainMenu;
    }

    public function getAttendMenu()
    {
        $attendMenu = array(
            array('label'=>Yii::t('user','课程考勤'), 'url'=>array("/masa/attend/index")),
            array('label'=>Yii::t('user',' 工作人员考勤'), 'url'=>array("/masa/attend/otherStaffs",)),
            //array('label'=>Yii::t('user',' 考勤汇总'), 'url'=>array("/masa/attend/report")),
            array('label'=>Yii::t('user',' 分成统计'), 'url'=>array("/masa/attend/statistics")),
        );
        return $attendMenu;
    }

    public function getteachertype()
    {
        $typeList = CommonUtils::LoadConfig('CfgASA');
        $jobType = array();
        foreach($typeList['job_type'] as $k=>$job_type){
            $jobType['jobType'][$k] = (Yii::app()->language == 'zh_cn') ? $job_type['cn'] : $job_type['en'];
        }

        foreach($typeList['type'] as $k=>$job_type){
            $jobType['type'][$k] = (Yii::app()->language == 'zh_cn') ? $job_type['cn'] : $job_type['en'];
        }

        foreach($typeList['unit_type'] as $k=>$job_type){
            $jobType['unit_type'][$k] = (Yii::app()->language == 'zh_cn') ? $job_type['cn'] : $job_type['en'];
        }

        return $jobType;
    }

    public function getAttendSubMenu()
    {
        return array(
            array('label'=>Yii::t('asa','考勤统计'), 'url'=>array("/masa/attend/index")),
            array('label'=>Yii::t('asa','工资统计'), 'url'=>array("/masa/pay/index")),
            array('label'=>Yii::t('asa','费用申请'), 'url'=>array("/masa/pay/fee")),
        );
    }
}
