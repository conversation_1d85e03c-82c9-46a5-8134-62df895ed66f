<?php
	foreach(Yii::app()->user->getFlashes() as $key => $message) {
        echo '<div class="flash-' . $key . '">' . $message . "</div>\n";
    }
?>

<div class="form profile_bg">
<?php $form=$this->beginWidget('CActiveForm', array(
	'id'=>'child-form',
	'enableClientValidation'=>true,
	'clientOptions'=>array(
		'validateOnSubmit'=>true,
	),
	'htmlOptions' => array('enctype' => 'multipart/form-data'),
)); ?>

	<?php //echo CHtml::errorSummary($model); ?>


	<dl class="row">
		<dt>
		<?php $_child = $this->myChildObjs[$this->childid];?>
		<?php echo CHtml::image(CommonUtils::childPhotoUrl($_child->photo, 'small'), "", array("class"=>"thumb-small") );?>
		</dt>
		<dd>&nbsp;</dd>
	</dl>

	<dl class="row">
		<dt>
		<?php echo CHtml::activeLabelEx($model,'photo'); ?>
		</dt>
		<dd>
		<?php echo CHtml::activeFileField($model,'uploadPhoto') ?>
		<?php echo CHtml::error($model,'uploadPhoto'); ?>
		</dd>
	</dl>

	<dl class="row">
		<dt>
		<?php echo CHtml::activeLabelEx($model,'birthday_search'); ?>
		</dt>
		<dd>
		<?php
			$this->widget('zii.widgets.jui.CJuiDatePicker', array(
				"model"=>$model,
				"attribute"=>"birthday_search",
				"options"=>array(
					'changeMonth'=>true,
					'changeYear'=>true,
					'dateFormat'=>'yy-mm-dd', //很重要，不要随便修改 (RAN)
				),
				"htmlOptions"=>array(
					'disabled'=>'disabled'
				)
			));
		?>
		<?php echo CHtml::error($model,'birthday_search'); ?>
		</dd>
	</dl>

	<dl class="row">
		<dt>
		<?php echo CHtml::activeLabelEx($model,'name_cn'); ?>
		</dt>
		<dd>
		<?php echo CHtml::activeTextField($model,'name_cn') ?>
		<?php echo CHtml::error($model,'name_cn'); ?>
		</dd>
	</dl>

	<dl class="row">
		<dt>
		<?php echo CHtml::activeLabelEx($model,'nick'); ?>
		</dt>
		<dd>
		<?php echo CHtml::activeTextField($model,'nick') ?>
		<?php echo CHtml::error($model,'nick'); ?>
		</dd>
	</dl>

	<dl class="row">
		<dt>
		<?php echo CHtml::activeLabelEx($model,'first_name_en'); ?>
		</dt>
		<dd>
		<?php echo CHtml::activeTextField($model,'first_name_en') ?>
		<?php echo CHtml::error($model,'first_name_en'); ?>
		</dd>
	</dl>



	<dl class="row">
		<dt>
		<?php echo CHtml::activeLabelEx($model,'last_name_en'); ?>
		</dt>
		<dd>
		<?php echo CHtml::activeTextField($model,'last_name_en') ?>
		<?php echo CHtml::error($model,'last_name_en'); ?>
		</dd>
	</dl>


	<dl class="row">
		<dt>
		<?php echo CHtml::activeLabelEx($model,'gender'); ?>
		</dt>
		<dd>
		<?php echo CHtml::activeDropDownList($model,'gender',$cfgs["gender"]); ?>
		<?php echo CHtml::error($model,'gender'); ?>
		</dd>
	</dl>


	<dl class="row">
		<dt>
		<?php echo CHtml::activeLabelEx($model,'country'); ?>
		</dt>
		<dd>
		<?php echo CHtml::activeDropDownList($model,'country', Country::model()->getData(), array('empty'=>Yii::t("global", 'Please Select'))) ?>
		<?php echo CHtml::error($model,'country'); ?>
		</dd>
	</dl>


	<dl class="row">
		<dt>
		<?php echo CHtml::activeLabelEx($model,'lang'); ?>
		</dt>
		<dd>
		<?php echo CHtml::activeDropDownList($model,'lang', Term::model()->getLangList(), array('empty'=>Yii::t("global", 'Please Select'))) ?>
		<?php echo CHtml::error($model,'lang'); ?>
		</dd>
	</dl>

	<dl class="row submit">
		<dd>
		<?php if(Yii::app()->user->checkAccess('editProfile',array("childid"=>$this->getChildId()))){
			if ($model->status >= ChildProfileBasic::STATS_GRADUATED) {
				echo CHtml::submitButton(Yii::t("global", "Save"), array("class"=>"w100 bigger","disabled"=>"disabled"));
			}else{
				echo CHtml::submitButton(Yii::t("global", "Save"), array("class"=>"w100 bigger"));
			}
		}else{
			echo CHtml::submitButton(Yii::t("global", "Save"), array("class"=>"w100 bigger","disabled"=>"disabled"));
		}
		?>
		</dd>
	</dl>


<?php $this->endWidget(); ?>
</div><!-- form -->