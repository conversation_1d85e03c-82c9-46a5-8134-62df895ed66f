<?php
class AqiController extends Controller
{
	public $citys = array(
			'bj'=>'北京',
			'tj'=>'天津',
			'cd'=>'成都',
			'nb'=>'宁波',
			'xa'=>'西安',
	);

	public function actionIndex()
	{
		$this->layout = '//layouts/blank';
		$this->setPageTitle('Ivy PM2.5');

		Yii::import('common.models.laseregg.*');

		$criteira = new CDbCriteria();
		$criteira->compare('status', 10);
		$branch = CHtml::listData(Branch::model()->findAll($criteira), 'branchid', 'abb');

		$branchLasereggs = array();
		$criteira = new CDbCriteria();
		$criteira->order='schoolid';
		$items = LasereggInfo::model()->findAll($criteira);
		foreach($items as $item){
			$branchLasereggs[$item->schoolid][$item->laseregg_mac] = $item->laseregg_name;
		}
		$ds = $branchLasereggs['BJ_DS'];
		unset($branchLasereggs['BJ_DS']);
		array_unshift($branchLasereggs, $ds);

		$criteira = new CDbCriteria();
		$criteira->compare('log_timestamp', '>'.(time()-21600));
		$criteira->order='log_timestamp desc';
		$items = LasereggLog::model()->findAll($criteira);
		$aqi = array();
		foreach($items as $item){
			if(!isset($aqi[$item->laseregg_mac]))
				$aqi[$item->laseregg_mac] = array(
					'num' => $item->laseregg_num,
					'time' => date('H:i', $item->log_timestamp),
				);
		}

		$this->render('index', array('branchLasereggs'=>$branchLasereggs, 'citys'=>$this->citys, 'branch'=>$branch, 'aqi'=>$aqi));
	}

	public function actionCityAqi()
	{
		//$url = 'http://sg1.aqicn.org/aqicn/services/search/?lang=cn&s=';
		$url = 'http://www.pm25.in/api/querys/pm2_5.json?token=Xp51QrouaGA2kCCBvssL&city=';
		$aqis = array();
		foreach($this->citys as $cid=>$city){
			$cityaqi = json_decode(file_get_contents($url.$city));
			$aqis[$cid] = end($cityaqi);
		}
		echo json_encode($aqis);die;
	}

	public function actionAqi($laseregg='')
	{
		Yii::import('common.models.laseregg.*');
		if($laseregg){
			$criteira = new CDbCriteria();
			$criteira->compare('log_timestamp', '>'.strtotime('today'));
			$criteira->compare('laseregg_mac', $laseregg);
			$criteira->order='log_timestamp';
			$items = LasereggLog::model()->findAll($criteira);
			$ret = array();
			foreach($items as $item){
				$ret[] = array('x'=>floatval($item->log_timestamp.'000'),'y'=>floatval($item->laseregg_num));
			}
			echo CJSON::encode($ret);
		}
	}
}