
<style type="text/css">
 [v-cloak] {
	display: none;
}
.att-report th {
	background-color: #f5f5f5;
}
.att-report tbody td {
	text-align: center;
}
th div.progress {
	margin-bottom: 0px;
}
.table{
	color:#606266
}
.table tr th,.table tr td {
	vertical-align:middle !important;
	text-align:center;
	color:#606266
}
.pink{
	background:#FFECEE
}
.progress{
	background:#fff;
}
.loading{
	width:98%;
	height:90%;
	background:#fff;
	position: absolute; 
	opacity: 0.5;
	z-index: 99
}
.loading span{
	width:100%;
	height:60%;
	display:block;
	background: url("<?php echo Yii::app()->theme->baseUrl?>/images/loading.gif")no-repeat center center ;
}
.label-taken{
    margin-bottom: 1rem;
}
</style>
<div id='container'  v-cloak>
	
    <h3 class='color3 font14'><strong style='font-size:18px' class='mr20'><?php echo Yii::t('attends', 'Attendance Submission Report'); ?></strong> <small><?php echo Yii::t('attends', 'Daily Attendance Submission By Teacher') ?></small></h3>
	<div class="form-inline mt15 mb20"> 
		<label for="exampleInputName2"><?php echo Yii::t("admissions", "Date");?></label>
		<input type="text" class="form-control form-group ml10" id="targetDate" placeholder="<?php echo Yii::t("newDS", "Select a date");?>" @change='showData()'   :value='targetDate'>
		<span class="label label-default ml10">#{{weekNum}} <?php echo Yii::t("lunch", "Week");?> / {{category}}</span>
	</div>
    <div>
        <span class="label label-default">TAKEN</span>
        <span class="label label-warning">NOT TAKEN</span>
    </div>

    <br>
	<div class='loading'  v-if='showLoading'>
		<span></span>
	</div>
	<table class="table table-bordered att-report" style='table-layout: fixed' v-if='Object.keys(total).length != 0 '>
		<thead>
			<th width='150'><?php echo Yii::t("attends", "Teachers");?></th>
			<th v-for='(list,key,index) in total'>
				<div class='font14'>{{timetableTimes[index].label}} </div>
                <div style='font-weight:200'>{{timetableTimes[index].timeslot}}</div>
				<div class='flex mt5'>
					<div class="progress flex1">
						<div class="progress-bar progress-bar-success" role="progressbar" aria-valuenow="40" aria-valuemin="0" aria-valuemax="100" :style="'width:'+progress(key)">
							<span class="sr-only">{{progress(key)}}</span>
						</div>
					</div>
					<div style='width:40px'>
						{{taken[key]}}/{{total[key]}}
					</div>
				</div>
				
			</th>
		</thead>
		<tbody>
			<tr v-for='(list,index) in teacherInfo'>
				<th style='text-align:left !important'>{{list.name}}</th>
				<template v-if='reports[list.uid]'>
					<template  v-for='(_list,key,i) in reports[list.uid].total'>
                        <template  v-if='_list!=0'>
                            <td>
                                <template v-for="(value,index,kk) in _list">
                                    <template v-if='reports[list.uid].params'>
                                        <label v-if="reports[list.uid].takenCode && (reports[list.uid].takenCode[key] !== undefined ||reports[list.uid].takenCode[key] != null ||reports[list.uid].takenCode[key]>0)">
                                        <span v-if="reports[list.uid].takenCode[key][index]" class="label label-default label-taken">
                                            {{reports[list.uid].params[key].coursecode[kk]}}
                                        </span>
                                            <span v-else class="label label-warning label-taken" style="cursor:pointer;" @click='details(reports[list.uid].params[key],list.uid,kk)'>
                                                {{reports[list.uid].params[key].coursecode[kk]}}
                                            </span>
                                        </label>
                                        <label v-else>
                                            <span class="label label-warning label-taken" style="cursor:pointer;"  @click='details(reports[list.uid].params[key],list.uid,kk)'>{{reports[list.uid].params[key].coursecode[kk]}}</span>
                                        </label>
                                        <br>
                                    </template>
                                </template>
                            </td>
                        </template>
                        <td v-else>
                        </td>
<!--						<template  v-if='_list!=0'>-->
<!--							<td   v-if='reports[list.uid].takenCode && reports[list.uid].takenCode[key]'>-->
<!--							TAKEN-->
<!--							</td>-->
<!--							<td class='pink' v-else>-->
<!--							<a href="javascript:;" @click='details(reports[list.uid].params[key],list.uid)'>NOT TAKEN</a>	-->
<!--							</td>-->
<!--						</template>-->
<!--						<td v-else>-->
<!--						</td>-->
					</template>
				</template>
			</tr>
		</tbody>
	</table>
	<div class="modal fade" id="courseStu" tabindex="-1" role="dialog" aria-labelledby="modal" aria-hidden="false" data-backdrop="static">
		<div class="modal-dialog modal-lg" role="document" style="width: 1000px;">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
					<h4 class="modal-title" ><?php echo Yii::t('campus','Student Attendance') ?> <span id="remarks_t"></span></h4>
				</div>
				<div class="modal-body" v-html='html'>
					
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-primary" :disabled='statusFrom==0?true:disabled' @click='submitForm()'><?php echo Yii::t("global", 'Save');?></button>
					<button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", 'Close');?></button>
				</div>
			</div>
		</div>
	</div>
</div>

<script>
	
	$(function() {
		$("#targetDate").datepicker({
			dateFormat: "yy-mm-dd",
		});
		$( "#targetDate").on('change', function () {
			container.targetDate = $('#targetDate').val();
			container.showData()
		});
	});
	var container = new Vue({
        el: "#container",
        data: {
			"targetDate":'',
			"category": "",
			"weekNum":'',
			"total": {},
			"taken": {},
			"teacherInfo": {},
			"reports": {},
			timetableTimes:'',
			showLoading:false,
			html:'',
			statusFrom:'',
			disabled:false
        },
		created(){
			this.targetDate=this.newDate()
			this.showData()
		},
        methods: {
			progress(index){
				var progress=this.total[index] <= 0? "0%" : Math.round((this.taken[index] / this.total[index]) * 10000) / 100.0 + "%";
				return progress
			},
			newDate(){           
				var date = new Date();
                const Y = date.getFullYear() + '-';
                const M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
                const D = (date.getDate() < 10 ? '0'+date.getDate() : date.getDate()) + '';
                const h = (date.getHours() < 10 ? '0'+date.getHours() : date.getHours()) ;
                const m = (date.getMinutes() <10 ? '0'+date.getMinutes() : date.getMinutes());
                const s = date.getSeconds(); // 秒
                const dateString = Y + M + D
                    return dateString;
                
            },
			showData(){
				this.showLoading=true
				let that=this
				$.ajax({
                    url: '<?php echo $this->createUrl("reportR01") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
						targetDate:this.targetDate
                    },
                    success: function(data) {
                        if(data.state=='success'){
                            if(data.data == null){
                                that.teacherInfo = [];
                                that.showLoading=false
                            }else{
                                that.category=data.data.category
                                that.timetableTimes=data.data.timetableTimes
                                that.weekNum=data.data.weekNum
                                that.total=data.data.total
                                that.taken=data.data.taken
                                that.teacherInfo=data.data.teacherInfo
                                that.reports=data.data.reports
                                that.showLoading=false
                            }
                        }else{
                            that.teacherInfo = [];
                            that.showLoading=false 
                        }
                    }
                })
			},
			details(item,uid,key){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("/mteaching/attendance/courseStudentNew") ?>',
                    type: "get",
                    data: {
                        uid:uid,
                        weekday:item.weekday,
                        course_code:item.coursecode[key], //可选,
                        datatime:this.targetDate
                    },
                    success: function(data) {
                        that.html=data
                        $("#courseStu").modal('show');
                        setTimeout(function () {
                            that.statusFrom=$('#status').val()
                        }, 300)
                       
                            
                    }
                })
            },
            submitForm(){
                let that=this
                var targetUrl = $("#visits-form").attr("action");
                this.disabled=true
                $.ajax({
                    type: "POST",
                    dataType: "json",
                    url: targetUrl,
                    data: $('#visits-form').serialize(),
                    success: function (data) {
                        if(data.state=='success'){  
                            resultTip({
                                msg: data.state
                            });
                            $("#courseStu").modal('hide');
                            that.showData()
                        }else{
                            resultTip({
                                    error: 'warning',
                                    msg: data.message
                                });
                        }
                        that.disabled=false
                    },
                    error: function(data) {
                        that.disabled=false
                     }
                });
            }
		}
    })
	function calcNum() {
        var num = {
            10: 0,
            11: 0,
            20: 0,
            30: 0,
            31: 0,
            40: 0,
            41: 0,
            42: 0,
            0: 0,
            1: 0,
            60:0,//电子设备未违规
            61:0,//手机违规
            62:0,//其他电子违规
            63:0,//全部违规
        };
        
        $('#visits-form .table input[type="radio"]:checked').each(function (key, val) {
            var v = $(val).val();
            num[v] += 1;
        });
        for (var key in num){
            var val = num[key];
            if (key == 31) {
                key=30
                val += num[30]
            }
            if(key == 41 || key == 42){
                key=40
                val = num[40]+num[41]+num[42]
            }
            if (key == 11) {
                key=10
                val += num[10]
            }
            if(key == 61 || key == 62 || key ==63){
                key=61
                val = num[61]+num[62]+num[63]
            }

            $('#current_'+key).text(val);
        }
        
    }

    function types(e, childid, min){
        $('#action_'+childid+'.attendance button').removeClass('active').eq(1).addClass('active').find('span.tflag').text( $(e).text() );
        $('#hospital_2_'+childid).attr('checked', true);
        $('#late_'+childid).val(min);
        calcNum();
    }

    function types1(e, childid, min,num){
        $('#action_'+childid+'.attendance button').removeClass('active').eq(num).addClass('active').find('span.tflag').text( $(e).text() );
        $('#hospital_'+min+'_'+childid).attr('checked', true);
        calcNum();
    }
    function types3(e, childid, min){
        $('#action_electronic_'+childid+'.attendance button').removeClass('active').eq(0).addClass('active').find('span.tflag').text( $(e).text() );
        $('#electronic_'+min+'_'+childid).attr('checked', true);
        calcNum();
    }

    function allSet(type, min) {
        if (type == 20) {
            $('.act button').removeClass('active');
            $('.table tbody tr .buttons[act] .btn_label_'+type).addClass('active').find('span.tflag').text( min+' <?php echo Yii::t('attends','Minutes');?>' );
            $('.table tbody tr .buttons[act] .btn_label_'+type+' input[type="radio"]').attr('checked', true);
            $('.table tbody tr .buttons[act] .btn_label_'+type+' input[type="hidden"]').val(min);
        }
        else if (type == 30 || type == 31) {
            $('.act button').removeClass('active');
            var text = type == 30 ? '<?php echo Yii::t('campus', 'Personal Leave');?>' : '<?php echo Yii::t('campus', 'Sick Leave');?>'
            $('.table tbody tr .buttons[act] .btn_label_30').addClass('active').find('span.tflag').text( text );
            $('.table tbody tr .buttons[act] .btn_label_30 input[value="'+type+'"]').attr('checked', true);
        }
        else if (type == 40 || type == 41 || type==42){
            $('.act button').removeClass('active');
            if(type==40){
                var text = '<?php echo Yii::t('attends', 'Absent');?>'
            }else if (type==41){
                var text = '<?php echo Yii::t('attends', 'Internal Suspension');?>'
            }else if(type==42){
                var text = '<?php echo Yii::t('attends', 'External Suspension');?>'
            }else{
                var text = '<?php echo Yii::t('attends', 'Absent');?>'
            }
            $('.table tbody tr .buttons[act] .btn_label_40').addClass('active').find('span.tflag').text( text );
            $('.table tbody tr .buttons[act] .btn_label_40 input[value="'+type+'"]').attr('checked', true);
        }
        else if (type == 10 || type == 11) {
            $('.act button').removeClass('active');
            var text = type == 10 ? '<?php echo Yii::t('attends', 'Present');?>' : '<?php echo Yii::t('attends', 'Online Present');?>'
            $('.table tbody tr .buttons[act] .btn_label_10').addClass('active').find('span.tflag').text( text );
            $('.table tbody tr .buttons[act] .btn_label_10 input[value="'+type+'"]').attr('checked', true);
        }

        else if(type == 61 || type == 62 ||type == 63){
            $("[id^='action_electronic_'] button").removeClass('active');
            if(type == 61 ){
                var text = '<?php echo Yii::t('attends', 'Phone');?>'
            }else if(type == 62){
                var text = '<?php echo Yii::t('attends', 'Other Personal Devices');?>'
            }else if(type == 63){
                var text = '<?php echo Yii::t('attends', 'Both');?>'
            } else{
                var text = '<?php echo Yii::t('attends', 'Phone');?>'
            }

            $('.table tbody tr .buttons .btn_label_61').addClass('active').find('span.tflag').text( text );
            $('.table tbody tr .buttons .btn_label_61 input[value="'+type+'"]').attr('checked', true);
        }

        else {
            $('.table tbody tr .buttons[act] .btn_label_'+type).click();
        }
        calcNum();
    }

    function allSets(type, min) {
        $('.table tbody tr .buttons .btn_label_'+type).click();
        calcNum();
    }
    function typeSel() {
        setTimeout(function () {
            calcNum();
        }, 300)
    }
</script>
