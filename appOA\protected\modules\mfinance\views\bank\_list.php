<?php
$start = Yii::app()->request->getParam('start');
$end = Yii::app()->request->getParam('end', date('Y-m-d'));
$id = Yii::app()->request->getParam('id');
$receiptid = Yii::app()->request->getParam('receiptid');
$title = Yii::app()->request->getParam('title');
$bamount = Yii::app()->request->getParam('bamount');

$crit = new CDbCriteria;
$crit->compare('schoolid', $this->branchId);

if ($id) {
    $crit->compare('id', $id, true);
}
if ($receiptid) {
    $crit->compare('receiptid', $receiptid, true);
}
if ($title) {
    $crit->compare('title', $title, true);
}
if ($bamount) {
    $crit->compare('bamount', $bamount);
}

$pageSize = 50;
$isSearch = $start && $end;
if ($isSearch) {
    $exportTitle = sprintf("%s_%s_%s", $this->branchId, $start, $end);
    $pageSize = 600;
    $startTime = strtotime($start);
    $endTime = strtotime($end);
    $crit->compare('btimestamp', '>=' . $startTime);
    $crit->compare('btimestamp', '<=' . $endTime);
}
$listModel = new CActiveDataProvider('Banktransfer', array(
    'criteria' => $crit,
    'sort' => array(
        'defaultOrder'=>'btimestamp DESC',
    ),
    'pagination'=>array(
        'pageSize'=>$pageSize,
    ),
));



?>
<div class="mb10 row">
    <!-- 搜索框 -->
    <form  class="" style="float: left;width: 100%" action="<?php echo $this->createUrl('index'); ?>" method="get" onsubmit="return checkDateRange();">
        <?php echo Chtml::hiddenField('selectMenu', 'list'); ?>
        <?php echo Chtml::hiddenField('branchId', $this->branchId); ?>
        <div class="col-sm-2">
            <input type="text" class="form-control" placeholder="交易流水号"  name="id" autocomplete="off" value="<?php echo $id; ?>">
        </div>
        <div class="col-sm-2">
            <input type="text" class="form-control" placeholder="银行回单号"  name="receiptid" autocomplete="off" value="<?php echo $receiptid; ?>">
        </div>
        <div class="col-sm-2">
            <input type="text" class="form-control" placeholder="标题"  name="title" autocomplete="off" value="<?php echo $title; ?>">
        </div>
        <div class="col-sm-2">
            <input type="text" class="form-control" placeholder="到帐金额"  name="bamount" autocomplete="off" value="<?php echo $bamount; ?>">
        </div>
        <div class="col-sm-12"><br></div>
        <div class="col-sm-2">
            <input type="text" class="form-control datepicker" placeholder="开始时间"  name="start" autocomplete="off" value="<?php echo $start; ?>">
        </div>
        <div class="col-sm-2">
            <input type="text" class="form-control datepicker" placeholder="截止时间"  name="end" autocomplete="off" value="<?php echo $end; ?>">
        </div>
        <div class="col-sm-2">
            <button class="btn btn-primary" type="submit">查找</button>
        </div>
        <div class="col-sm-6 text-right">
            <?php if ($isSearch): ?>
                <span id="countSpan" style="font-size: 14px; margin-right: 16px;"></span>
                <span id="totalSpan" style="font-size: 14px; margin-right: 20px;"></span>
                <button class="btn btn-primary ml5" type="button" onclick="exportTable()">导出</button>
            <?php endif; ?>
        </div>
    </form>
</div>
<div class="panel panel-default">
    <div class="panel-heading">
      <h3 class="panel-title"><?php echo $this->menuItem[$this->selectMenu];?></h3>
    </div>
    <div class="panel-body">
        <?php
            $this->widget('ext.ivyCGridView.BsCGridView', array(
                'id'=>'account-grid',
                'dataProvider'=>$listModel,
                // 'filter'=>$model,
//                'colgroups'=>array(
//                    array(
//                        //"colwidth"=>array(null,150,150,150),
//                    )
//                ),
                'columns'=>array(
                    array(
                        'name'=>'id',
                        'type'=>'raw',
                        'value'=>array($this,'getOrderInfo'),
                    ),
                    array(
                        'name'=>'receiptid',
                        'value'=>'$data->receiptid',
                        'filter'=>CHtml::activeTextField($model, 'receiptid',array('class'=>'form-control'))
                    ),
                    array(
                        'name'=>'title',
                        'value'=>'$data->title',
                        'filter'=>CHtml::activeTextField($model, 'title',array('class'=>'form-control'))
                    ),
                    array(
                        'name'=>'banktransferDetail.invoiceid',
                        'type'=>'raw',
                        'value'=>array($this,'getInvoiceInfo'),
                    ),
                    array(
                        'name'=>'bamount',
                        'value'=>'$data->bamount',
                        'filter'=>CHtml::activeTextField($model, 'bamount',array('class'=>'form-control'))
                    ),
                    array(
                        'name'=>'btimestamp',
                        'value'=>'OA::formatDateTime($data->btimestamp)',
                        'filter'=>false
                    ),
                    array(
                        'name'=>'company_payment',
                        'value'=>'$data->company_payment ? "是" : "否"',
                        'filter'=>false
                    ),
                ),
            )); ?>

    </div>
</div>

<div class="modal fade" id="viewOrder">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title">转账详情</h4>
            </div>
            <div class="modal-body"></div>
        </div>
    </div>
</div>

<script>
    $('.datepicker').datepicker({
        changeMonth: true,
        changeYear: true,
        dateFormat:'yy-mm-dd'
    });
    function viewOrder(obj)
    {
        var orderid = $(obj).text();
        $.post('<?php echo $this->createUrl('orderInfo')?>', {id: orderid}, function(data){
            $('#viewOrder .modal-body').html(data);
            $('#viewOrder').modal();
        });
    }
    function printOrder() {
        $('.noPrint').hide();
        $("#print-area" + " a").removeAttr('href');
        $("#print-area").printThis();
        setTimeout(function(){
            $('.noPrint').show();
        },1000)
    }
    function exportTable() {        
        // 获取 tbody 中的所有行
        const rows = $("#account-grid table>tbody tr");
        
        // 创建一个新的工作表数据数组
        const data = [];
        data.push(['交易流水', '电子回单', '标题', '金额', '到账时间', '公司付款']);

        $("#account-grid table > tbody tr").each(function () {
            // 获取当前行的所有单元格
            const cells = $(this).find("td");

            // 获取指定列的单元格
            const transid =  cells.eq(0).text(); // 交易流水 列
            const receiptid =  cells.eq(1).text(); // 电子回单 列
            const title =  cells.eq(2).text(); // 标题 列
            const amount =  cells.eq(4).text(); // 金额 列
            const btdate =  cells.eq(5).text(); // 到账时间 列
            const company =  cells.eq(6).text(); // 是否公司付款 列
            
            // 将数据添加到数组中
            data.push([transid, receiptid, title, amount, btdate, company]);
        });
        
        
        // 创建工作表
        const ws = XLSX.utils.aoa_to_sheet(data);
        
        // 创建工作簿
        const wb = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');
        
        // 导出为 .xlsx 文件
        XLSX.writeFile(wb, '<?= $exportTitle; ?>.xlsx');
    }

    function formatAmount(o_amount) {
        let amount = Number(o_amount);
        if (typeof amount !== 'number' || isNaN(amount)) {
            return '';
        }
        try {
            return amount.toLocaleString('zh-CN', {
                style: 'currency',
                currency: 'CNY'
            });
        } catch (error) {
            return '';
        }
    }

    function checkDateRange() {
        const startDate = new Date(document.querySelector('input[name="start"]').value);
        const endDate = new Date(document.querySelector('input[name="end"]').value);
        const oneMonth = 31 * 24 * 60 * 60 * 1000; // One month in milliseconds

        if ((endDate - startDate) > oneMonth) {
            alert('开始时间与截止时间不能超过一个月');
            return false;
        }
        return true;
    }

    $(function () {
        var count = 0;
        var total = 0;
        $("#account-grid table > tbody tr").each(function () {
            const cells = $(this).find("td");
            const amount = parseFloat(cells.eq(4).text());
            if (amount > 0) {                
                count++;
                total += amount
            }
        });
        $("#countSpan").text('总条数：' + count);
        $("#totalSpan").text('总金额：' + formatAmount(total));
    });

</script>