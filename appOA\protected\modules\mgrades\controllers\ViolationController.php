<?php

class ViolationController extends BranchBasedController
{

    public function init()
    {
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";
        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/element/vue.global.js');
        $cs->registerCssFile(Yii::app()->themeManager->baseUrl.'/base/js/element/index.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/element/index.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/v-calendar/v-calendar.umd.min.js');
        $cs->registerCssFile(Yii::app()->theme->baseUrl.'/css/calendar.css');
//        $this->multipleBranch = true;
        $this->modernMenuFlag = 'campusOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site', 'Campus Workspace');
        $this->setPageTitle('Violation');
        //初始化选择校园页面
        $this->branchSelectParams['hideOffice'] = true;
        $this->branchSelectParams['hideKG'] = true;
        $this->branchSelectParams['urlArray'] = array('//mgrades/violation/index');
    }

    public function createUrl($route, $params = array(), $ampersand = '&', $parentOnly = false)
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        return parent::createUrl($route, $params, $ampersand);
    }

    public function checkAuth()
    {
        if (!(Yii::app()->user->checkAccess('ivystaff_it') || Yii::app()->user->checkAccess('ivystaff_opschool'))) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', Yii::t('message', '无权限'));
            $this->showMessage();
        }
    }

    public function actionIndex()
    {
        $this->render('index', array());
    }

    public function actionConfig()
    {
        $requestUrl = 'attendance/violation/config';
        $this->remote($requestUrl, array());
    }

    public function actionSetCache()
    {
        $type = Yii::app()->request->getParam("type");
        $year = Yii::app()->request->getParam('year', 0);
        $semester = Yii::app()->request->getParam("semester");
        $requestUrl = 'attendance/violation/setCache';
        $this->remote($requestUrl, array(
            'type'       => $type,
            'start_year' => $year,
            'semester'   => $semester
        ));
    }

    public function actionList()
    {
        $requestUrl = 'attendance/violation/list';
        $type = Yii::app()->request->getParam("type");
        $year = Yii::app()->request->getParam('year', 0);
        $semester = Yii::app()->request->getParam("semester");
        $this->remote($requestUrl, array(
            'type'     => $type,
            'year'     => $year,
            'semester' => $semester
        ));
    }

    public function actionAllList()
    {
        $requestUrl = 'attendance/violation/allList';
        $type = Yii::app()->request->getParam("type");
        $year = Yii::app()->request->getParam('year', 0);
        $semester = Yii::app()->request->getParam("semester");
        $class_id = Yii::app()->request->getParam("class_id", 0);
        $search_name = Yii::app()->request->getParam("search_name", 0);
        $page = Yii::app()->request->getParam("page", 1);
        $this->remote($requestUrl, array(
            'type'        => $type,
            'year'        => $year,
            'semester'    => $semester,
            'class_id'    => $class_id,
            'search_name' => $search_name,
            'page'        => $page,
        ));
    }

    public function actionDetail()
    {
        $requestUrl = 'attendance/violation/detail';
        $child_id = Yii::app()->request->getParam("child_id");
        $year = Yii::app()->request->getParam('year', 0);
        $semester = Yii::app()->request->getParam("semester");
        $this->remote($requestUrl, array(
            'child_id' => $child_id,
            'year'     => $year,
            'semester' => $semester,
        ));
    }

    public function actionSave()
    {
        $requestUrl = 'attendance/violation/save';
        $child_list = Yii::app()->request->getParam("child_list", array());
        $year = Yii::app()->request->getParam('year', 0);
        $semester = Yii::app()->request->getParam("semester");
        $violation_type = Yii::app()->request->getParam("violation_type");
        $consequence = Yii::app()->request->getParam("consequence");
        $consequence_time = Yii::app()->request->getParam("consequence_time");
        $consequence_comment = Yii::app()->request->getParam("consequence_comment");
        $this->remote($requestUrl, array(
            'child_list'          => $child_list,
            'year'                => $year,
            'semester'            => $semester,
            'violation_type'      => $violation_type,
            'consequence'         => $consequence,
            'consequence_time'    => $consequence_time,
            'consequence_comment' => $consequence_comment,
        ));
    }

    public function remote($requestUrl, $requestData = array())
    {
        $this->checkAuth();
        $requestData['school_id'] = $this->branchId;
        $res = CommonUtils::requestDsOnline($requestUrl, $requestData);
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('message', $res['msg']);
            $this->addMessage('data', $res['data']);
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->addMessage('data', $res['data']);
        }
        $this->showMessage();
    }

}