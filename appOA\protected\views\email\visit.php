<div style="color:#274257;">
    <div style="font-family: 'Microsoft Yahei'">
        <h2 style="font-size:13px;">
            <?php echo sprintf('%s %s共有 %s 个校园参观信息：',date('Y-m-d',$startTime),$branch->title, count($result));?>
        </h2>
        <div>请注意：本名单统计于本邮件发送之时，不包含在这之后的新增的操作名单。</div>
        <table border='1' cellpadding='2' cellspacing='0' width="90%">
            <tr>
                <th width="100" style="font-family: 'Microsoft Yahei';font-size:12px;">预约日期</th>
                <th width="100" style="font-family: 'Microsoft Yahei';font-size:12px;">预约时间</th>
                <th width="100" style="font-family: 'Microsoft Yahei';font-size:12px;">孩子姓名</th>
                <th width="120" style="font-family: 'Microsoft Yahei';font-size:12px;">家长姓名</th>
                <th width="120" style="font-family: 'Microsoft Yahei';font-size:12px;">电话</th>
                <th width="120" style="font-family: 'Microsoft Yahei';font-size:12px;">接待语言</th>
            </tr>
            <?php
            foreach($result as $val):
            ?>
            <tr>
                <td style="font-family: 'Microsoft Yahei';font-size:12px;">
                    <?php echo OA::formatDateTime($val->appointment_date);?>
                </td>
                
                <td style="font-family: 'Microsoft Yahei';font-size:14px;color: red;">
                    <?php echo $val->appointment_time;?>
                </td>
                <td style="font-family: 'Microsoft Yahei';font-size:12px;">
                    <?php echo $val->basic->child_name;?>
                </td>
                <td style="font-family: 'Microsoft Yahei';font-size:12px;">
                    <?php echo $val->basic->parent_name;?>
                </td>
                <td style="font-family: 'Microsoft Yahei';font-size:12px;">
                    <?php echo $val->basic->tel;?>
                </td>
                <td style="font-family: 'Microsoft Yahei';font-size:12px;">
                    <?php echo $val->basic->receive_language;?>
                </td>
            </tr>
            <?php endforeach;?>
        </table>
    </div>
    <div style="padding-top: 10px;">详细信息请登录艾毅在线，点击右侧相关任务提醒查看。</div>
</div>
