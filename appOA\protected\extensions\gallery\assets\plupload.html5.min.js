(function(d){var e,c;function b(g,h){var f;if("FileReader" in window){f=new FileReader();f.readAsDataURL(g);f.onload=function(){h(f.result)}}else{return h(g.getAsDataURL())}}function a(l,n,k,f,m){var h,g,j,i;b(l,function(o){h=document.createElement("canvas");h.style.display="none";document.body.appendChild(h);g=h.getContext("2d");j=new Image();j.onload=function(){var s,p,q,r,t;i=Math.min(n/j.width,k/j.height);if(i<1){s=Math.round(j.width*i);p=Math.round(j.height*i);h.width=s;h.height=p;g.drawImage(j,0,0,s,p);t=new c();t.init(atob(o.substring(o.indexOf("base64,")+7)));r=t.APP1({width:s,height:p});o=h.toDataURL(f);o=o.substring(o.indexOf("base64,")+7);o=atob(o);if(r){t.init(o);t.setAPP1(r);o=t.getBinary()}h.parentNode.removeChild(h);m({success:true,data:o})}else{m({success:false})}};j.src=o})}d.runtimes.Html5=d.addRuntime("html5",{getFeatures:function(){var k,g,j,h,f,i=window;g=j=h=f=false;if(i.XMLHttpRequest){k=new XMLHttpRequest();j=!!k.upload;g=!!(k.sendAsBinary||k.upload)}if(g){h=!!(File&&(File.prototype.getAsDataURL||i.FileReader)&&k.sendAsBinary);f=!!(File&&File.prototype.slice)}e=navigator.userAgent.indexOf("Safari")>0;return{html5:g,dragdrop:i.mozInnerScreenX!==undefined||f||e,jpgresize:h,pngresize:h,multipart:h||!!i.FileReader||!!i.FormData,progress:j,chunking:f||h}},init:function(i,j){var f={},g;function h(n){var l,k,m=[],o;for(k=0;k<n.length;k++){l=n[k];o=d.guid();f[o]=l;m.push(new d.File(o,l.fileName,l.fileSize))}if(m.length){i.trigger("FilesAdded",m)}}g=this.getFeatures();if(!g.html5){j({success:false});return}i.bind("Init",function(o){var s,q=[],n,r,l=o.settings.filters,m,p,k=document.body;s=document.createElement("div");s.id=o.id+"_html5_container";for(n=0;n<l.length;n++){m=l[n].extensions.split(/,/);for(r=0;r<m.length;r++){p=d.mimeTypes[m[r]];if(p){q.push(p)}}}d.extend(s.style,{position:"absolute",background:i.settings.shim_bgcolor||"transparent",width:"100px",height:"100px",overflow:"hidden",zIndex:99999,opacity:i.settings.shim_bgcolor?"":0});s.className="plupload html5";if(i.settings.container){k=document.getElementById(i.settings.container);k.style.position="relative"}k.appendChild(s);s.innerHTML='<input id="'+i.id+'_html5" style="width:100%;" type="file" accept="'+q.join(",")+'" '+(i.settings.multi_selection?'multiple="multiple"':"")+" />";document.getElementById(i.id+"_html5").onchange=function(){h(this.files);this.value=""}});i.bind("PostInit",function(){var k=document.getElementById(i.settings.drop_element);if(k){if(e){d.addEvent(k,"dragenter",function(o){var n,l,m;n=document.getElementById(i.id+"_drop");if(!n){n=document.createElement("input");n.setAttribute("type","file");n.setAttribute("id",i.id+"_drop");n.setAttribute("multiple","multiple");n.onchange=function(){h(this.files);this.value=""}}l=d.getPos(k,document.getElementById(i.settings.container));m=d.getSize(k);d.extend(n.style,{position:"absolute",display:"block",top:l.y+"px",left:l.x+"px",width:m.w+"px",height:m.h+"px",opacity:0});k.appendChild(n)});return}d.addEvent(k,"dragover",function(l){l.preventDefault()});d.addEvent(k,"drop",function(m){var l=m.dataTransfer;if(l&&l.files){h(l.files)}m.preventDefault()})}});i.bind("Refresh",function(k){var l,m,n;l=document.getElementById(i.settings.browse_button);m=d.getPos(l,document.getElementById(k.settings.container));n=d.getSize(l);d.extend(document.getElementById(i.id+"_html5_container").style,{top:m.y+"px",left:m.x+"px",width:n.w+"px",height:n.h+"px"})});i.bind("UploadFile",function(k,m){var n=k.settings,p,l;function o(q){var t=0,s=0;function r(){var A=q,H,I,D,E,F=0,w="----pluploadboundary"+d.guid(),z,B,x,y="--",G="\r\n",C="",v,u=k.settings.url;if(m.status==d.DONE||m.status==d.FAILED||k.state==d.STOPPED){return}E={name:m.target_name||m.name};if(n.chunk_size&&g.chunking){z=n.chunk_size;D=Math.ceil(m.size/z);B=Math.min(z,m.size-(t*z));if(typeof(q)=="string"){A=q.substring(t*z,t*z+B)}else{A=q.slice(t*z,B)}E.chunk=t;E.chunks=D}else{B=m.size}H=new XMLHttpRequest();I=H.upload;if(I){I.onprogress=function(J){m.loaded=Math.min(m.size,s+J.loaded-F);k.trigger("UploadProgress",m)}}if(!k.settings.multipart||!g.multipart){u=d.buildUrl(k.settings.url,E)}else{E.name=m.target_name||m.name}H.open("post",u,true);H.onreadystatechange=function(){var J,L;if(H.readyState==4){try{J=H.status}catch(K){J=0}if(J>=400){k.trigger("Error",{code:d.HTTP_ERROR,message:"HTTP Error.",file:m,status:J})}else{if(D){L={chunk:t,chunks:D,response:H.responseText,status:J};k.trigger("ChunkUploaded",m,L);s+=B;if(L.cancelled){m.status=d.FAILED;return}m.loaded=Math.min(m.size,(t+1)*z)}else{m.loaded=m.size}k.trigger("UploadProgress",m);if(!D||++t>=D){m.status=d.DONE;k.trigger("FileUploaded",m,{response:H.responseText,status:J});p=q=f[m.id]=null}else{r()}}H=A=x=C=null}};d.each(k.settings.headers,function(K,J){H.setRequestHeader(J,K)});if(k.settings.multipart&&g.multipart){if(!H.sendAsBinary){x=new FormData();d.each(d.extend(E,k.settings.multipart_params),function(K,J){x.append(J,K)});x.append(k.settings.file_data_name,A);H.send(x);return}H.setRequestHeader("Content-Type","multipart/form-data; boundary="+w);d.each(d.extend(E,k.settings.multipart_params),function(K,J){C+=y+w+G+'Content-Disposition: form-data; name="'+J+'"'+G+G;C+=unescape(encodeURIComponent(K))+G});v=d.mimeTypes[m.name.replace(/^.+\.([^.]+)/,"$1")]||"application/octet-stream";C+=y+w+G+'Content-Disposition: form-data; name="'+k.settings.file_data_name+'"; filename="'+unescape(encodeURIComponent(m.name))+'"'+G+"Content-Type: "+v+G+G+A+G+y+w+y+G;F=C.length-A.length;A=C}else{H.setRequestHeader("Content-Type","application/octet-stream")}if(H.sendAsBinary){H.sendAsBinary(A)}else{H.send(A)}}r()}p=f[m.id];l=k.settings.resize;if(g.jpgresize){if(l&&/\.(png|jpg|jpeg)$/i.test(m.name)){a(p,l.width,l.height,/\.png$/i.test(m.name)?"image/png":"image/jpeg",function(q){if(q.success){m.size=q.data.length;o(q.data)}else{o(p.getAsBinary())}})}else{o(p.getAsBinary())}}else{o(p)}});j({success:true})}});c=function(){var h,i,x,r,s,m,q,t,C,o,z,v,j,B,f,A,y,k,g;function n(){var G=false,E;function H(J,L){var I=G?0:-8*(L-1),M=0,K;for(K=0;K<L;K++){M|=(E.charCodeAt(J+K)<<Math.abs(I+K*8))}return M}function D(I,K,J){E=E.substr(0,I)+K+E.substr((J===true?K.length:0)+I)}function F(J,K,M){var N="",I=G?0:-8*(M-1),L;for(L=0;L<M;L++){N+=String.fromCharCode((K>>Math.abs(I+L*8))&255)}D(J,N,true)}return{II:function(I){if(I===f){return G}else{G=I}},init:function(I){E=I},SEGMENT:function(I,K,J){if(!arguments.length){return E}if(typeof K=="number"){return E.substr(parseInt(I,10),K)}D(I,K,J)},BYTE:function(I){return H(I,1)},SHORT:function(I){return H(I,2)},LONG:function(I,J){if(J===f){return H(I,4)}else{F(I,J,4)}},SLONG:function(I){var J=H(I,4);return(J>2147483647?J-4294967296:J)},STRING:function(I,J){var K="";for(J+=I;I<J;I++){K+=String.fromCharCode(H(I,1))}return K}}}C=new n();A={274:"Orientation",34665:"ExifIFDPointer",34853:"GPSInfoIFDPointer"};y={36864:"ExifVersion",40961:"ColorSpace",40962:"PixelXDimension",40963:"PixelYDimension",36867:"DateTimeOriginal",33434:"ExposureTime",33437:"FNumber",34855:"ISOSpeedRatings",37377:"ShutterSpeedValue",37378:"ApertureValue",37383:"MeteringMode",37384:"LightSource",37385:"Flash",41986:"ExposureMode",41987:"WhiteBalance",41990:"SceneCaptureType",41988:"DigitalZoomRatio",41992:"Contrast",41993:"Saturation",41994:"Sharpness"};k={0:"GPSVersionID",1:"GPSLatitudeRef",2:"GPSLatitude",3:"GPSLongitudeRef",4:"GPSLongitude"};g={ColorSpace:{1:"sRGB",0:"Uncalibrated"},MeteringMode:{0:"Unknown",1:"Average",2:"CenterWeightedAverage",3:"Spot",4:"MultiSpot",5:"Pattern",6:"Partial",255:"Other"},LightSource:{1:"Daylight",2:"Fliorescent",3:"Tungsten",4:"Flash",9:"Fine weather",10:"Cloudy weather",11:"Shade",12:"Daylight fluorescent (D 5700 - 7100K)",13:"Day white fluorescent (N 4600 -5400K)",14:"Cool white fluorescent (W 3900 - 4500K)",15:"White fluorescent (WW 3200 - 3700K)",17:"Standard light A",18:"Standard light B",19:"Standard light C",20:"D55",21:"D65",22:"D75",23:"D50",24:"ISO studio tungsten",255:"Other"},Flash:{0:"Flash did not fire.",1:"Flash fired.",5:"Strobe return light not detected.",7:"Strobe return light detected.",9:"Flash fired, compulsory flash mode",13:"Flash fired, compulsory flash mode, return light not detected",15:"Flash fired, compulsory flash mode, return light detected",16:"Flash did not fire, compulsory flash mode",24:"Flash did not fire, auto mode",25:"Flash fired, auto mode",29:"Flash fired, auto mode, return light not detected",31:"Flash fired, auto mode, return light detected",32:"No flash function",65:"Flash fired, red-eye reduction mode",69:"Flash fired, red-eye reduction mode, return light not detected",71:"Flash fired, red-eye reduction mode, return light detected",73:"Flash fired, compulsory flash mode, red-eye reduction mode",77:"Flash fired, compulsory flash mode, red-eye reduction mode, return light not detected",79:"Flash fired, compulsory flash mode, red-eye reduction mode, return light detected",89:"Flash fired, auto mode, red-eye reduction mode",93:"Flash fired, auto mode, return light not detected, red-eye reduction mode",95:"Flash fired, auto mode, return light detected, red-eye reduction mode"},ExposureMode:{0:"Auto exposure",1:"Manual exposure",2:"Auto bracket"},WhiteBalance:{0:"Auto white balance",1:"Manual white balance"},SceneCaptureType:{0:"Standard",1:"Landscape",2:"Portrait",3:"Night scene"},Contrast:{0:"Normal",1:"Soft",2:"Hard"},Saturation:{0:"Normal",1:"Low saturation",2:"High saturation"},Sharpness:{0:"Normal",1:"Soft",2:"Hard"},GPSLatitudeRef:{N:"North latitude",S:"South latitude"},GPSLongitudeRef:{E:"East longitude",W:"West longitude"}};function p(D,K){var F=C.SHORT(D),H,N,P,J,I,E,G,L,M=[],O={};for(H=0;H<F;H++){G=E=D+12*H+2;P=K[C.SHORT(G)];if(P===f){continue}J=C.SHORT(G+=2);I=C.LONG(G+=2);G+=4;M=[];switch(J){case 1:case 7:if(I>4){G=C.LONG(G)+B}for(N=0;N<I;N++){M[N]=C.BYTE(G+N)}break;case 2:if(I>4){G=C.LONG(G)+B}O[P]=C.STRING(G,I-1);continue;case 3:if(I>2){G=C.LONG(G)+B}for(N=0;N<I;N++){M[N]=C.SHORT(G+N*2)}break;case 4:if(I>1){G=C.LONG(G)+B}for(N=0;N<I;N++){M[N]=C.LONG(G+N*4)}break;case 5:G=C.LONG(G)+B;for(N=0;N<I;N++){M[N]=C.LONG(G+N*4)/C.LONG(G+N*4+4)}break;case 9:G=C.LONG(G)+B;for(N=0;N<I;N++){M[N]=C.SLONG(G+N*4)}break;case 10:G=C.LONG(G)+B;for(N=0;N<I;N++){M[N]=C.SLONG(G+N*4)/C.SLONG(G+N*4+4)}break;default:continue}L=(I==1?M[0]:M);if(g.hasOwnProperty(P)&&typeof L!="object"){O[P]=g[P][L]}else{O[P]=L}}return O}function w(){var D=t+4;B+=t;if(C.STRING(D,4).toUpperCase()!=="EXIF"||C.SHORT(D+=4)!==0){return}C.II(C.SHORT(D+=2)==18761);if(C.SHORT(D+=2)!==42){return}j=B+C.LONG(D+=2);h=p(j,A);z=("ExifIFDPointer" in h?B+h.ExifIFDPointer:f);v=("GPSInfoIFDPointer" in h?B+h.GPSInfoIFDPointer:f);return true}function l(D,I,H){var G=D.SHORT(H),F,E;for(E=0;E<G;E++){F=H+12*E+2;if(D.SHORT(F)==I){return F+8}}}function u(G,E){var F,H,I=z!=f?z-t:f,D=new n();D.init(q);D.II(C.II());if(I===f){return}F=l(D,40962,I);if(F!==f){D.LONG(F,G)}H=l(D,40963,I);if(H!==f){D.LONG(H,E)}q=D.SEGMENT()}return{init:function(D){B=10;h=i=x=r=s=m=q=t=o=f;C.init(D);if(C.SHORT(0)!==65496){return false}switch(C.SHORT(2)){case 65504:s=2;m=C.SHORT(4)+2;if(C.SHORT(m)==65505){t=m;o=C.SHORT(m+2)+2}break;case 65505:t=2;o=C.SHORT(4)+2;break;default:return false}if(o!==f){w()}},APP1:function(D){if(t===f&&o===f){return}q=q||(q=C.SEGMENT(t,o));if(D!==f&&"width" in D&&"height" in D){u(D.width,D.height)}return q},EXIF:function(){i=p(z,y);i.ExifVersion=String.fromCharCode(i.ExifVersion[0],i.ExifVersion[1],i.ExifVersion[2],i.ExifVersion[3]);return i},GPS:function(){x=p(v,k);x.GPSVersionID=x.GPSVersionID.join(".");return x},setAPP1:function(D){if(t!==f){return false}C.SEGMENT((s?s+m:2),D)},getBinary:function(){return C.SEGMENT()}}}})(plupload);