<?php
/**
* 微信支付异步接收
*/
class WechatPayController extends Controller
{
	/**
	 * 微信在线支付
	 * @param  [xml] $value [description]
	 * @return [xml]        [description]
	 */
	public function actionWechatPay()
	{
		//获取通知的数据
		// $xml = $GLOBALS['HTTP_RAW_POST_DATA'];
		$xml = file_get_contents('php://input');
		Yii::log($xml, 'info', 'wechatpay');
		$arr = array();
		Yii::import('common.extensions.wxPay.*');
		$nativePay = new NativePay();
		$nativePay->init();
		if (!$nativePay->fromXml($xml)) {
			$arr['return_code'] = 'FAIL';
			$arr['return_msg'] = 'xml数据异常';
			$nativePay->toXml($arr);
			Yii::app()->end();
		}
		//判断通信标识
		if($nativePay->values['return_code'] != 'SUCCESS'){
			$arr['return_code'] = 'FAIL';
			$arr['return_msg'] = '通信失败';
			$nativePay->toXml($arr);
			Yii::app()->end();
		}
		$schoolId = $nativePay->values['attach'];
		$wxpayInfo = CommonUtils::LoadConfig('CfgWxPayGlobal');
		$nativePay->cfg = $wxpayInfo[$schoolId];
		$nativePay->init();
		//验证签名
		if(!$nativePay->checkSign()){
			$arr['return_code'] = 'FAIL';
			$arr['return_msg'] = '验证签名失败';
			$nativePay->toXml($arr);
			Yii::app()->end();
		}
		//查看支付状态
		if ($nativePay->values['result_code'] != 'SUCCESS') {
			$arr['return_code'] = 'SUCCESS';
			$arr['return_msg'] = '验证签名成功，订单支付失败';
			$nativePay->toXml($arr);
			Yii::app()->end();
		}
		if (!isset($wxpayInfo[$schoolId])) {
			$arr['return_code'] = 'FAIL';
			$arr['return_msg'] = '学校不存在';
			$nativePay->toXml($arr);
			Yii::app()->end();
		}
		Yii::import('common.models.wxpay.*');
		$wechatPayOrder = WechatPayOrder::model()->findByPk($nativePay->values['out_trade_no']);
		// if ($schoolId != $wechatPayOrder->schoolid) {
		// 	$arr['return_code'] = 'FAIL';
		// 	$arr['return_msg'] = '学校错误';
		// 	$nativePay->toXml($arr);
		// 	Yii::app()->end();
		// }
		if (!$wechatPayOrder) {
			$arr['return_code'] = 'FAIL';
			$arr['return_msg'] = '订单号不存在';
			$nativePay->toXml($arr);
			Yii::app()->end();
		}
		//验证订单状态
		if ($wechatPayOrder->status == 1) {
			$arr['return_code'] = 'SUCCESS';
			$arr['return_msg'] = '订单已处理';
			$nativePay->toXml($arr);
			Yii::app()->end();
		}
		//支付成功，处理账单
		$operatorid = $wechatPayOrder->uid;
		$total_fee = ($nativePay->values['total_fee'])*0.01;
		$total_fee = round($total_fee,2);
		$wechatPayOrder->status = 1;
		$wechatPayOrder->fact_amount = $total_fee;
		$wechatPayOrder->update_timestamp = time();
		if(!$wechatPayOrder->save()){
			$arr['return_code'] = 'FAIL';
			$arr['return_msg'] = '订单处理失败';
			$nativePay->toXml($arr);
			Yii::app()->end();
		}
		//订单处理成功，处理账单
		Yii::import('common.models.invoice.*');
		$wechatPayOrderItemObjs = $wechatPayOrder->items;
		
		$invoiceObjs = array();
		$productInvoice = array();
		foreach ($wechatPayOrderItemObjs as $wechatPayOrderItemObj) {
			// 如果账单类型是校服，发送MQ处理
			if ($wechatPayOrderItemObj->invoice->payment_type == 'uniform') {
				$productInvoice[] = $wechatPayOrderItemObj->invoice_id;
			}
			$invoiceObjs[] = $wechatPayOrderItemObj->invoice;
		}
		Yii::import('common.components.policy.*');
		$policyApi = new PolicyApi($wechatPayOrder->schoolid);
		$payType = InvoiceTransaction::TYPE_WX_NATIVE;
		$payResult = $policyApi->Pay($invoiceObjs, $payType, $total_fee, $operatorid);
		//判断账单支付结果
		if ($payResult != 0) {
			$wechatPayOrder->status = 0;
			$wechatPayOrder->save();

			$arr['return_code'] = 'FAIL';
			$arr['return_msg'] = '账单支付失败';
			$nativePay->toXml($arr);
			Yii::app()->end();
		}
		$arr['return_code'] = 'SUCCESS';
		$arr['return_msg'] = '账单支付成功';
		$nativePay->toXml($arr);

		// MQ 处理商品购买
		if (count($productInvoice) > 0) {
			Yii::import('common.components.AliYun.MQ.MQProducer');
			CommonUtils::addProducer(MQProducer::TAG_PRODUCT, "Invoice.updateProductsInvoice", CJSON::encode($productInvoice), 0);
		}
		
		// 发送邮件
		if (count($invoiceObjs)==1 && current($invoiceObjs)->payment_type=='registration' && current($invoiceObjs)->childid==0) {
			PolicyApi::admissionsToEmail(current($invoiceObjs)->admission_id);
		} else{
			PolicyApi::wxpayToEmail(array($wechatPayOrder->orderid));
		}
		Yii::app()->end();
	}

	/**
	 * 模拟微信的异步通知,本地测试用
	 * @param string $value [description]
	 */
	public function actionSimulation()
	{
		// $arr = array(
		// 	'return_code'=>'SUCCESS',
		// 	'appid'=>'wx200048dfee9f23b2',
		// 	'mch_id'=>'**********',
		// 	'nonce_str'=>'5K8264ILTKCH16CQ2502SI8ZNMTM67VS',
		// 	'sign'=>'318950054D4CCB0A0D258762551C0B28',
		// 	'result_code'=>'SUCCESS',
		// 	'openid'=>'wxd930ea5d5a258f4f',
		// 	'trade_type'=>'NATIVE',
		// 	'bank_type'=>'ICBC_DEBIT',
		// 	'total_fee'=>'56900',
		// 	'cash_fee'=>'56900',
		// 	'transaction_id'=>'1217752501201407033233368018',
		// 	'out_trade_no'=>'************',
		// 	'time_end'=>'**************',
		// );
		// $key = 's10adc3949ba59abbe56e057f20f883e';

		// 手动生成sign
		// ksort($arr);
		// $string = "";
		// foreach ($arr as $k => $v)
		// {
		// 	if($k != "sign" && $v != "" && !is_array($v)){
		// 		$string .= $k . "=" . $v . "&";
		// 	}
		// }
		
		// $string = trim($string, "&");
		// //签名步骤二：在string后加入KEY
		// $string = $string . "&key=".$key;
		// //签名步骤三：MD5加密
		// $string = md5($string);
		// //签名步骤四：所有字符转为大写
		// $result = strtoupper($string);
		// echo $result;die();
		
		// Yii::import('common.extensions.wxPay.*');
		// $nativePay = new NativePay();
	 // 	echo $nativePay->simulation($arr);
	}
}