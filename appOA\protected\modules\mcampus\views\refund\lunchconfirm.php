<?php
 //查询所选学校（当天之前）未审核的午餐取消申请
$criter = new CDbCriteria();
$criter->compare('t.schoolid', $this->branchId);
$criter->compare('t.operator_uid', 0);
$criter->compare('t.child_credit_id', 0);
$criter->addCondition('t.target_timestamp<' . strtotime('today+1'));
$criter->limit = 1000;
$criter->order = 't.childid, t.target_timestamp desc';
$dataList = RefundLunch::model()->with('childInfo')->findAll($criter);
$cancelData = array();
if (!empty($dataList)){
    foreach ($dataList as $val){
        $classData[$val->classid] = $val->classInfo->title;
        $cancelData[$val->classid][$val->childid]['childid'] = $val->childid;
        $cancelData[$val->classid][$val->childid]['ids'] = isset($cancelData[$val->classid][$val->childid]['ids']) ? $cancelData[$val->classid][$val->childid]['ids'].','.$val->id : $val->id;
        $cancelData[$val->classid][$val->childid]['skey'] = md5(OA::SECURITY_KEY.$val->childid.$cancelData[$val->classid][$val->childid]['ids']);
        $cancelData[$val->classid][$val->childid]['name'] = $val->childInfo->getChildName();
        $cancelData[$val->classid][$val->childid]['data'][] = array(
            'id'=>$val->id,
            'childid'=>$val->childid,
            'target_timestamp'=>OA::formatDateTime($val->target_timestamp,'full'),
            'amount'=>$val->amount,
            'userid'=> $val->userid,
            'updated_timestamp' =>OA::formatDateTime($val->updated_timestamp,'short','short'),
        );
    }
    foreach ($cancelData as $classid => $items) {
        foreach ($items as $childid => $item) {
            $cancelData[$classid][$childid]['num'] = count($cancelData[$classid][$childid]['data']);
        }
    }
}
//xm($cancelData);
?>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site','Campus Operations'), array('//mcampus/default/index'))?></li>
        <li><?php echo CHtml::link(Yii::t('site','Billings'), array('//mcampus/default/index'))?></li>
        <li><?php echo Yii::t('site','Lunch Refund')?></li>
    </ol>
    <div class="row" id="check-lunch-list">
        <div class="col-md-12" id="cancel-left">

        </div>
    </div>
</div>
<script type="text/template" id="cancel-lunch-template">
    
    <div class="col-md-6">
    <div class="panel panel-default" id="panel-<%=childid%>" role="tablist">
        <div class="panel-heading" role="tab" id="tab-<%=childid%>">
            <h3 class="panel-title collapsed" role="button" data-toggle="collapse" href="#group-<%=childid%>" aria-expanded="true" aria-controls="group-<%=childid%>">
                <%=name%>（<%=num%>）
            </h3>
        </div>
        <form action="" id='child<%= childid%>'>
        <div id="group-<%=childid%>" class="panel-collapse collapse" role="tabpanel" aria-labelledby="tab-<%=childid%>" aria-expanded="true">
            <div class="panel-body">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th><?php echo Yii::t('campus', 'Cancellation date'); ?></th>
                            <th><?php echo Yii::t('campus', 'Request submitted'); ?></th>
                            <th><?php echo Yii::t('campus', 'Amount'); ?> (￥)</th>
                        </tr>
                    </thead>
                    <tbody>
                        <%for(var i=0;i<data.length;i++){%>
                        <tr>
                            <td><%=data[i].target_timestamp%></td>
                            <td><%=data[i].updated_timestamp%></td>
                            <td><%=data[i].amount%></td>
                            <?php echo CHtml::hiddenField('ids[]', '<%=data[i].id%>', array('encode'=>false));?>
                        </tr>
                        <%}%>
                    </tbody>
                </table>
            </div>
            <div class="panel-footer">
                <?php echo CHtml::hiddenField('skey', '<%=skey%>', array('encode'=>false));?>
                <?php echo CHtml::hiddenField('childid', '<%=childid%>', array('encode'=>false));?>
                <label class="mr15"><?php echo CHtml::checkBox('check',null,array('onclick'=>'activateBtn(this);'));?><?php echo Yii::t('campus', 'Confirm'); ?></label>
                <button type="button" class="btn btn-primary disabled" onclick="save('<%= childid%>',this)"><?php echo Yii::t('global','Save');?></button>
                <span id="text<%= childid%>" class="text-success"></span>
            </div>
        </div>
        </form>
    </div>

    </div>
    
</script>
<?php $this->renderPartial('//layouts/common/branchSelectBottom');?>
<script>
var cancelData = <?php echo CJSON::encode($cancelData);?>;
var classData = <?php echo CJSON::encode($classData);?>;
var cancelTemplate = _.template($('#cancel-lunch-template').html());
function  save(id,obj) {
    $(obj).addClass('disabled');
    $.ajax({
        url: "<?php echo $this->createUrl('/mcampus/refund/saveLunchConfirm')?>",
        type: 'post',
        dataType: 'json',
        data: $('#child'+id).serialize(),
        success:function(data){
           if(data.state=='success'){
            $('#text'+id).html('<i class="glyphicon glyphicon-ok text-success"></i>'+data.message)
            setTimeout(function () {
                location.reload()
            }, 2000);
           }else{
            $(obj).removeClass('disabled');
            $('#text'+id).html('<i class="glyphicon glyphicon-remove text-warning"></i'+data.message)
           }
        },
        error:function(data) {
            $('#text'+id).html('<i class="glyphicon glyphicon-ok text-success"></i>请求错误')
        }
    })
}
$(function(){
    //初始化取消午餐名单
    renderCancelData = function (){
        var i=1;
        if (_.isEmpty(cancelData)){
            $("#check-lunch-list").html('<div class="col-md-12"><div class="alert alert-warning alert-dismissible" role="alert">无数据！</div></div>');
        }
        _.each(classData,function(title, classid){
            // if (i%2){
                $("#cancel-left").append("<div class='col-md-12'><h3>"+title+"</h2></div>");
            // }else{
                // $("#cancel-right").append("<h3 class='panel-title'>"+title+"</h3>");
            // }
            _.each(cancelData[classid], function(val,key){
                var formatData = cancelTemplate(val);
                // if (i%2){
                    $("#cancel-left").append(formatData);
                // }else{
                    // $("#cancel-right").append(formatData);
                // }
            });
            i++;
        });
    }
    renderCancelData();

    activateBtn = function(_this){
        var btn = $(_this).parent().next();
        if (_this.checked){
            btn.removeClass('disabled');
        }else{
            btn.addClass('disabled');
        }
    }

    updateCallback = function(data){
        setTimeout("removeTable("+data+")",2000);
    }

    removeTable = function (data){
        $('#panel-'+data).remove()
    }
})
</script>

