<link rel="stylesheet" type="text/css"
      href="<?php echo Yii::app()->theme->baseUrl . '/css/allinpay.css?t=' . Yii::app()->params['refreshAssets']; ?>"/>
<div id="invoiceinfo" class="confirminfo">
    <?php
    if (!empty($dataProvider)) {
        Mims::LoadHelper('HtoolKits');
        $tk = new HtoolKits();
        $nf = new CNumberFormatter('zh-cn');
        $beforePaidInfoForm = $this->beginWidget('CActiveForm', array(
            'id' => 'custom_alipay_online',
            'enableAjaxValidation' => false,
                ));
        ?>
        <div class="paylist">
            <?php
            $invoice_model = Invoice::model();
            $this->widget('zii.widgets.grid.CGridView', array(
                'cssFile' => Yii::app()->theme->baseUrl . '/widgets/gridview/styles.css',
                'dataProvider' => $dataProvider,
                'enablePagination' => false,
                'enableSorting' => false,
                'columns' => array(
                    array(
                        'name' => 'title',
                        'type' => 'raw',
                        'value' => 'CHtml::link($data->title,Yii::app()->getController()->createUrl("//child/payment/viewInvoice", array("invoice_id"=>$data->invoice_id)),array("class"=>"colorbox"))',
                        'htmlOptions' => array('style'=>'text-align:left;'),
                    ),
                    array(
                        'name' => 'amount',
                        'htmlOptions' => array('style'=>'text-align:right;'),
                    ),
                    array(
                        'name' => Yii::t("payment", 'Amount Paid'),
                        'value' => array($invoice_model, 'renderPaidAmountF'),
                        'htmlOptions' => array('style'=>'text-align:right;'),
                    ),
                    array(
                        'header' => Yii::t("payment", 'Amount Due'),
                        'type' => 'raw',
                        'value' => array($invoice_model, 'renderDueAmountHtml'),
                        'htmlOptions' => array('style'=>'text-align:right;'),
                    ),
                ),
                'template' => '{items}',
            ));
            ?>
        </div>
        <?php $this->endWidget(); ?>

        <div id="confirm-info">
            <div class="fr big-size">
                <?php echo Yii::t("payment", 'Payment Amount: ');?>
                <span id="should_pay"><?php echo $nf->format('#,##0.00', $totalDueAmount); ?>
                </span>
            </div>
        </div>
        <div class="clear"></div>
        <div align="center">
            <?php 
                if (!empty($url)) {
                    $filename = 'qrcode_'.Yii::app()->user->id.'_'.uniqid();
                    $this->widget('ext.qrcode.QRCodeGenerator',array(
                        'data' => $url,
                        'subfolderVar' => true,
                        'matrixPointSize' => 8,
                        'filename' => $filename,
                        'filePath' => Yii::app()->params['uploadPath'],
                        'fileUrl' => Yii::app()->params['uploadBaseUrl'],
                    ));
                }else{
                    echo "二维码生成失败，请重新发起支付";
                }
             ?>
            <p class="box prepend-top"><?php echo Yii::t("payment", 'Please scan QR code from Wechat to complete payment!');?></p>
        </div>
        <div id="operatecol">
            <?php echo CHtml::link('<span>' . Yii::t("global", 'Back') . '</span>', $this->createUrl('//child/payment/online'), array("class" => "back")); ?>
        </div>
    <?php } ?>
</div>

<script>
    var orderid = '<?php echo $orderid;?>';
    function checkState()
    {
        $.ajax({
            type: 'post',
            url: '<?php echo $this->createUrl('//child/wxpay/checkState')?>',
            data: {id: orderid},
            dataType: 'json'
        }).done(function(data){
            if(data.state == 'success'){
                alert('<?php echo Yii::t("payment", 'Payment successful!');?>');
                window.location.href='<?php echo $this->createUrl('//child/payment/online');?>';
            }
            else {
                checkState();
            }
        });
    }
    checkState();
</script>
