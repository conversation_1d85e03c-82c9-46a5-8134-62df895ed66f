<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('//mcampus/default/index')) ?></li>
        <li><?php echo CHtml::link(Yii::t('site', 'Routines'), array('//mcampus/default/index')) ?></li>
        <li class="active"><?php echo Yii::t('site', '课后课管理')?></li>
    </ol>

    <div class="row">
        <!-- 左侧菜单 -->
        <div class="col-md-2 col-sm-2 mb10">
            <?php
            $this->widget('zii.widgets.CMenu', array(
                'items' => $this->module->getMenu(),
                'id' => 'pageCategory',
                'htmlOptions' => array('class' => 'nav nav-pills nav-stacked background-gray'),
                'activeCssClass' => 'active',
            ));
            ?>
        </div>

        <div class="col-md-10 col-sm-12">
            <!--<div class="mb10 col-sm-1 row">
                <a href="<php /*echo $this->createUrl('exportAdmissions', array("childid"=>"")) */?>" class="btn btn-info" target="_blank"><?php /*echo Yii::t('user', 'Export');*/?></a>
            </div>-->
            <div class="mb10 row">
                <!-- 搜索框 -->
                <form  class="" style="float: left;width: 100%" action="<?php echo $this->createUrl('summary'); ?>" method="get">
                    <div class="col-sm-2 form-group">
                        <?php echo Chtml::dropDownList('group', $groupId, $groupData, array('class'=>'form-control')); ?>
                    </div>
                    <div class="col-sm-2 form-group">
                        <?php echo Chtml::dropDownList('course', $courseId, $currentCourseData, array('class'=>'form-control')); ?>
                    </div>
                    <div class="row"></div>
                    <?php echo Chtml::hiddenField('branchId',$this->branchId); ?>
                    <div class="col-sm-2 form-group">
                        <input type="text" class="form-control" name="username" placeholder="孩子姓名" value="<?php echo Yii::app()->request->getParam('username','')?Yii::app()->request->getParam('username',''):''; ?>">
                    </div>
                    <div class="col-sm-2 form-group">
                        <?php echo Chtml::dropDownList('paytype', $_GET['paytype'], array('1'=>'现金','2'=>'微信'),array('class'=>'form-control',  'empty' => Yii::t('teaching', '支付方式'))); ?>
                    </div>
                    <div class="col-sm-2 form-group">
                        <?php echo Chtml::dropDownList('invoiceStatus', $_GET['invoiceStatus'], array('10'=>'未付款','20'=>'款付清','99'=>'作废'),array('class'=>'form-control',  'empty' => Yii::t('teaching', '支付状态'))); ?>
                    </div>
                    <div class="">
                        <!-- 入学时间 -->
                        <div class="col-sm-2" style="float: left;">
                            <input type="text" class="form-control" id="datepicker" placeholder="支付时间"   name="times" value="<?php echo Yii::app()->request->getParam('times','')?Yii::app()->request->getParam('times',''):''; ?>">
                        </div>
                        <!-- 内容匹配 -->
                        <div class="">
                            <button class="btn btn-default ml5" type="submit"><span class="glyphicon glyphicon-search"> </span> </button>
                            <?php if($_GET['invoiceStatus']): ?>
                                <a href="javascript:void(0);" onclick="exportParent()" class="btn btn-info" target="_blank"><?php echo Yii::t('user', 'Export');?></a>
                            <?php endif; ?>
                        </div>
                    </div>
                </form>
            </div>

            <div class="col-md-12"></div>
            <div class="panel panel-default">
                <div class="panel-body">
                    <?php
                    $this->widget('ext.ivyCGridView.BsCGridView', array(
                        'id'=>'confirmed-visit-grid',
                        'afterAjaxUpdate'=>'js:head.Util.modal',
                        'dataProvider'=>$dataProvider,
                        'template'=>"{items}{pager}",
                        'colgroups'=>array(
                            array(
                                "colwidth"=>array(200,100,100,100,100,100,100),
                            )
                        ),
                        'columns'=>array(
                            array(
                                'name'=>Yii::t('site','名字和课程'),
                                'type'=>'raw',
                                'value'=>'$data->title',
                            ),
                            array(
                                'name'=>Yii::t('site','原始金额'),
                                'type'=>'raw',
                                'value'=>'$data->amount_original',
                            ),
                            array(
                                'name'=>Yii::t('site','实付金额'),
                                'type'=>'raw',
                                'value'=>'$data->amount_actual',
                            ),
                            array(
                                'name'=>Yii::t('site','支付方式'),
                                'type'=>'raw',
                                'value'=> array($this, 'getPaytype'),
                            ),
                            array(
                                'name'=>Yii::t('site','支付状态'),
                                'type'=>'raw',
                                'value' => array($this, 'getTeacher'),
                            ),
                            /*array(
                                'name'  =>'start_grade',
                                'type'=>'raw',
                                'value'=> ($this->branchId == "BJ_IASLT") ? '$data->getCatename("grade_ayalt")': '$data->getCatename("grade")',
                            ),*/
                            array(
                                'name'=>'更新时间',
                                'type'=>'raw',
                                'value'=>'date("Y-m-d H:i", $data->updated)',
                            ),
                            array(
                                'name'=>'操作',
                                'value'=> array($this, 'getButton'),
                            ),
                        ),
                    ));
                    ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    var courseData = <?php echo json_encode($courseData); ?>;
    var courseId = <?php echo $courseId; ?>;

    var modal = '<div class="modal fade" id="modal"  tabindex="-1" role="dialog" aria-labelledby="modal"><div class="modal-dialog" role="document"><div class="modal-content"></div></div></div>';
    $('body').append(modal);
    $('#datepicker').datepicker({
        changeMonth: true,
        changeYear: true,
        dateFormat:'yy-mm-dd'
    });

    function exportParent() {
        var childName = '<?php echo Yii::app()->request->getParam('username','') ?>';
        var course = '<?php echo Yii::app()->request->getParam('course','') ?>';
        var paytype = '<?php echo Yii::app()->request->getParam('paytype','') ?>';
        var invoiceStatus = '<?php echo Yii::app()->request->getParam('invoiceStatus','') ?>';
        var times = '<?php echo Yii::app()->request->getParam('times','') ?>';
        var url = '<?php echo $this->createUrl('export') ?>';
        $.ajax({
            url: url,
            type: 'POST',
            data: {childName:childName,course:course,paytype:paytype,invoiceStatus:invoiceStatus,times:times},
            success: function (res) {
                if (res.state == 'success') {
                    var data = res.data.items;
                    const filename = res.data.title;
                    const ws_name = "SheetJS";

                    const worksheet = XLSX.utils.aoa_to_sheet(data);
                    const workbook = XLSX.utils.book_new();
                    XLSX.utils.book_append_sheet(workbook, worksheet, ws_name);
                    // generate Blob
                    const wbout = XLSX.write(workbook, {bookType: 'xlsx', type: 'array'});
                    const blob = new Blob([wbout], {type: 'application/octet-stream'});
                    // save file
                    let link = document.createElement('a');
                    link.href = URL.createObjectURL(blob);
                    link.download = filename;
                    link.click();
                    setTimeout(function() {
                        // 延时释放掉obj
                        URL.revokeObjectURL(link.href);
                        link.remove();
                    }, 500);
                }
            },
            dataType: 'json'
        });
    }

    $(document).ready(function() {
        initSelect();
        // 绑定 select 的 change 事件
        $("#group").change(function() {
            initSelect();
        });

        function initSelect() {
            var selectedValue = $("#group").val();
            var currentCourse = courseData[selectedValue]
            if (currentCourse) {
                $("#course").empty();
                currentCourse.forEach(function(item) {
                    let selsected = courseId == item.id;
                    $("#course").append(new Option(item.title, item.id, selsected, selsected)); // 添加选项
                });
            }

        }
    });
    </script>
</script>

<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>
