<?php

/**
 * This is the model class for table "ivy_scholarship_intro".
 *
 * The followings are the available columns in table 'ivy_scholarship_intro':
 * @property integer $id
 * @property string $schoolid
 * @property integer $startyear
 * @property string $intro_cn
 * @property string $intro_en
 * @property string $item1_info
 * @property string $item2_info
 * @property string $item3_info
 * @property string $item4_info
 * @property string $item5_info
 * @property integer $status
 * @property integer $created_at
 * @property integer $created_by
 * @property integer $updated_at
 * @property integer $updated_by
 */
class ScholarshipIntro extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_scholarship_intro';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('schoolid, startyear, created_at, created_by, updated_at, updated_by', 'required'),
			array('startyear, status, created_at, created_by, updated_at, updated_by', 'numerical', 'integerOnly'=>true),
			array('schoolid', 'length', 'max'=>255),
			array('intro_cn, intro_en, item1_info, item2_info, item3_info, item4_info, item5_info', 'safe'),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, schoolid, startyear, intro_cn, intro_en, item1_info, item2_info, item3_info, item4_info, item5_info, status, created_at, created_by, updated_at, updated_by', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'schoolid' => 'Schoolid',
			'startyear' => 'Startyear',
			'intro_cn' => Yii::t('lable','奖学金政策'),
			'intro_en' => Yii::t('lable','奖学金政策'),
			'item1_info' => 'Item1 Info',
			'item2_info' => 'Item2 Info',
			'item3_info' => 'Item3 Info',
			'item4_info' => 'Item4 Info',
			'item5_info' => 'Item5 Info',
			'status' => 'Status',
			'created_at' => 'Created At',
			'created_by' => 'Created By',
			'updated_at' => 'Updated At',
			'updated_by' => 'Updated By',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('startyear',$this->startyear);
		$criteria->compare('intro_cn',$this->intro_cn,true);
		$criteria->compare('intro_en',$this->intro_en,true);
		$criteria->compare('item1_info',$this->item1_info,true);
		$criteria->compare('item2_info',$this->item2_info,true);
		$criteria->compare('item3_info',$this->item3_info,true);
		$criteria->compare('item4_info',$this->item4_info,true);
		$criteria->compare('item5_info',$this->item5_info,true);
		$criteria->compare('status',$this->status);
		$criteria->compare('created_at',$this->created_at);
		$criteria->compare('created_by',$this->created_by);
		$criteria->compare('updated_at',$this->updated_at);
		$criteria->compare('updated_by',$this->updated_by);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return ScholarshipIntro the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

    public static function getScholarshipConfig()
    {
        $type = array(
            'BJ_DS' => array(
                100 => array(
                    'title' => Yii::t('lable','奖学金简介'),
                    'Introduction' => Yii::t('lable',''),
                ),
                1 => array(
                    'title' => Yii::t('lable','多元化奖学金'),
                    'Introduction' => Yii::t('lable','此项奖学金针对在学术方面表现优异的6-12年级学生，或者参与课外活动方面表现突出的6-12年级学生。'),
                ),
                2 => array(
                    'title' => Yii::t('lable','许士军博士奖学金'),
                    'Introduction' => Yii::t('lable','此项奖学金针对在英语语言能力方面表现优异的1-12年级学生。'),
                ),
                3 => array(
                    'title' => Yii::t('lable','助学金'),
                    'Introduction' => Yii::t('lable','助学金针对在入学评估及面试过程中表现突出且家庭需要经济支持的1-12年级学生。'),
                ),
				4 => array(
                    'title' => Yii::t('lable','启明星前沿学者奖学金'),
                    'Introduction' => Yii::t('lable','助学金针对在入学评估及面试过程中表现突出且家庭需要经济支持的1-12年级学生。'),
                ),
            ),
            'BJ_SLT' => array(
                100 => array(
                    'title' => Yii::t('lable','奖学金简介'),
                    'Introduction' => Yii::t('lable',''),
                ),
                1 => array(
                    'title' => Yii::t('lable','多元化奖学金'),
                    'Introduction' => Yii::t('lable','此项奖学金针对在学术方面表现优异的6-12年级学生，或者参与课外活动方面表现突出的6-12年级学生。'),
                ),
                2 => array(
                    'title' => Yii::t('lable','许士军博士奖学金'),
                    'Introduction' => Yii::t('lable','此项奖学金针对在英语语言能力方面表现优异的1-12年级学生。'),
                ),
                3 => array(
                    'title' => Yii::t('lable','助学金'),
                    'Introduction' => Yii::t('lable','助学金针对在入学评估及面试过程中表现突出且家庭需要经济支持的1-12年级学生。'),
                ),
				4 => array(
                    'title' => Yii::t('lable','启明星前沿学者奖学金'),
                    'Introduction' => Yii::t('lable','助学金针对在入学评估及面试过程中表现突出且家庭需要经济支持的1-12年级学生。'),
                ),
            ),

        );
        return $type;
    }
}
