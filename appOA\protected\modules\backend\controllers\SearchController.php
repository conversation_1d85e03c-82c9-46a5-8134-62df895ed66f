<?php
/***
 * phpwind 风格程序下线后此 extension 可以删掉
 */
class SearchController extends ProtectedController
{
	
	public function actionChild()
	{
		$this->layout = "//layouts/blank";
		$this->render('child');
	}
	
	public function actionChildoutput($term, $displayType="", $withAlumni="",$allowMultipleSchool=false)
	{
		$limit = 50;
		$source=array();
		$crit = new CDbCriteria;
		$crit->limit = $limit;
		
		$crit->compare('tdata', $term, true);
		$myBranch = $this->staff->profile->branch;
		$officeBranch = CommonUtils::LoadConfig('CfgOffice');
        $accessAll = ( in_array($myBranch, $officeBranch) ||  $this->staff->admtype || $allowMultipleSchool)? true : false;
        if(!$accessAll){
            $crit->compare('schoolid', $this->staff->profile->branch);
        }
		$crit->order = 'status ASC, schoolid ASC';
		$simpleDisplay = ($displayType == 'simpleDisplay')? true: false;
		$displayAlumni = ($withAlumni == 'withAlumni')? true: false;
		
		if(!$displayAlumni){
			$crit->compare('status', '<100');
		}
		$founds = FtsChild::model()->findAll($crit);
		$i=0;
		
		if(!$simpleDisplay){
			$source[]=array(
				'label'=>Yii::t("message","Close"),
				'value'=>-1,
			);		
		}
		
		if($founds){
			foreach($founds as $found){
				if($simpleDisplay){
					preg_match('/{(.*)}/', $found->tdata, $matches);
					$_dataStr = $matches[1];
				}
				else{
					$_dataStr = mb_ereg_replace("[\{\}]", '', $found->tdata);
				}
				$source[] = array(
					'label'=> $_dataStr,
					'value'=> $found->id,
				);
				$i++;
			}		
		}else{
			$source[] = array(
				'label' => Yii::t('message', 'Sorry, no match found!'),
				'value' => 0,
			);
		}
		if($limit == $i ){
			$source[] = array(
				'label'=> Yii::t('message', '查询结果只显示:limit条信息，其余已被隐藏，请精确条件后再查询。', array(':limit'=>$limit)),
				'value'=> -2,
			);		
		}
		
		echo CJSON::encode($source);
	}
	
	public function actionStaffoutput($term, $flag){
		$limit = 50;
		$source = array();
		$crit = new CDbCriteria;
		$crit->limit = $limit;
		
		$crit->compare('tdata', $term, true);
		
		$flag = Yii::app()->request->getParam('flag','');
		if(strtolower($flag) != 'withAlumni'){
			$crit->compare('active', 1);
		}
		
		$crit->order = 'schoolid ASC';
		$founds = FtsStaff::model()->findAll($crit);
		$i=0;
		if($founds){
			foreach($founds as $found){
				preg_match('/{(.*)}/', $found->tdata, $matches);
				
				$source[] = array(
					'label'=> $matches[1],
					'value'=> $found->id,
				);
				$i++;
			}
		}else{
			$source[] = array(
				'label' => Yii::t('message', 'Sorry, no match found!'),
				'value' => 0,
			);
		}
		if($limit == $i){
			$source[] = array(
				'label'=> Yii::t('message', '查询结果只显示:limit条信息，其余已被隐藏，请精确条件后再查询。', array(':limit'=>$limit)),
				'value'=> -2,
			);		
		}
		
		echo CJSON::encode($source);		
	}
	
}