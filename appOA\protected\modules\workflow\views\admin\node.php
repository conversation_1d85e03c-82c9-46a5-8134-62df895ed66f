<?php 
    $nodeList = array();
    if (!empty($definationModel->workflowNode)){
        foreach ($definationModel->workflowNode as $node){
            if ($node->executor){
                $executor = unserialize($node->executor);
                $arrKey = key($executor);
                $arrVal = current($executor);
                $branchTypeSelect = isset($executor['branchType']) ? $executor['branchType'] : 0;
            }else{
                $arrKey = 0;
                $arrVal = '';
                $branchTypeSelect = 0;
            }
            if ($arrKey === WorkflowDefination::WORKFLOW_AUTH_SYSTEM_POSITION){
                $arrTag = 1;
            }else{
                $arrTag = (empty($arrVal)) ? 2 : 3;
            }
            $nodeList[] = array(
                'node_id'=>$node->node_id,
                'defination_id'=>$node->defination_id,
                'node_name_cn'=>$node->node_name_cn,
                'node_name_en'=>$node->node_name_en,
                'node_type'=>$node->node_type,
                'executor'=> $arrKey,
                'executeId'=> $arrVal,
                'branchType'=> $branchTypeSelect,
                'executeTag'=> $arrTag,
                'field'=> $node->field,
                'field2'=> $node->field2,
            );
        }
    }
    $labelsTxt = $definationModel->attributeLabels();
    $nodeModel = new WorkflowNode();
    $labels = $nodeModel->attributeLabels();
?>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site','HQ Operations'), array('/moperation'));?></li>
        <li><?php echo CHtml::link(Yii::t('site','Basic Configurations'), array('/moperation'));?></li>
        <li><?php echo CHtml::link(Yii::t('site','Workflow Management'),array('//workflow/admin/index'));?></li>
        <li class="active"><?php echo Yii::t('site','工作流节点');?></li>
    </ol>
    <div class="row">
        <div class="col-sm-12">
            <a type="button" class="btn btn-primary mb10 J_ajax_del" data-msg='您确定添加工作流节点吗？' href="<?php echo $this->createUrl('/workflow/admin/addNode',array('definationId'=>$definationModel->defination_id));?>"><span class="glyphicon glyphicon-plus"></span> 新建工作流程节点</a>
        </div>
    </div>
    <div class="panel panel-default">
        <div class="panel-heading"><?php echo $labelsTxt['defination_name_cn'];?>: <?php echo $definationModel->defination_name_cn;?>   <a type="button" href="javascript:void(0);" class="label label-danger" style="padding:2px 5px;" onclick="nodeSort();"><span class="glyphicon glyphicon-floppy-disk"></span> 排序保存</a></div>
        <div class="panel-body">
            <div class="sortable" id="workflow-node-list"></div>
        </div>
    </div>
</div>
<!-- 工作流程模板 -->
<div class="modal fade" id="newWorkflowModal" tabindex="-1" role="dialog" aria-labelledby="newWorkflowModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title" id="myModalLabel"><?php echo '工作流程节点编辑';?> <small></small></h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-12">
                        <div class="well">
                            说明
                        </div>
                        <form class="form-horizontal J_ajaxForm" action="<?php echo $this->createUrl('//workflow/admin/saveNode');?>" method="POST">
                            <div id="form-data">
                                <!--place holder-->
                            </div>

                            <div class="pop_bottom">
                                <button onclick="$('#newWorkflowModal').modal('hide')" type="button" class="btn btn-default pull-right"><?php echo Yii::t('global','Cancel');?></button>
                                <button type="submit" class="btn btn-primary J_ajax_submit_btn pull-right mr10"><?php echo Yii::t('global','Submit');?></button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php 
$this->renderPartial('templates/nodeTemplate',array('clabels'=>$labels));
?>
<script>
    var definationModel = <?php echo CJSON::encode($definationModel);?>;
    var nodeList = <?php echo CJSON::encode($nodeList);?>;
    var multSchool = <?php echo CJSON::encode($multSchool);?>; //管理多校园
    var branchTypeList = <?php echo CJSON::encode($branchType);?>; //学校类型
    var positionList = <?php echo CJSON::encode($positionList);?>; //职位
    var workflowNodeTemplate = _.template($("#workflow-node-template").html());
    var workflowNodeEditTemplate = _.template($("#workflow-node-edit-template").html());
    var nodeModel = Backbone.Model.extend({defaults:{node_id: "",defination_id: "",node_name_cn: "",node_name_en: "",node_type: "",init_function: "",run_function: "",save_function: "",transit_function: "",executor: "",execute_type: "",filter_adm: "",remind: "",field: "",field2: "",max_day: "",allow_repeat: "",}})
    var nodesCollection = Backbone.Collection.extend({
        model: nodeModel
    });
    var selectIndex = '';
    var selectTwo = '';
    var nodesort='';
    $(function(){
        //动态添加节点
        newWorkflowNodes = function(data){
            $("#workflow-node-list").append(workflowNodeTemplate(data));
        }

        //初始化添加回调函数
        addCallback = function(data){
            if (length>6){
                resultTip('工作流最多只能有（6）个节点！');
                return false;
            }
            nodes.add(data);
            rendWorkflowList();
        }

        //删除工作流节点
        deleteNodeCallback = function(data){
            var node = nodes.findWhere({node_id:data.node_id});
            nodes.remove(node);
            rendWorkflowList();
        }

         //渲染列表
        rendWorkflowList = function(){
            $("#workflow-node-list").empty();
            if (!_.isEmpty(definationModel.node_order)){
                var models = (definationModel.node_order).split(",");
                _.each(models,function(data,key){
                    var node = nodes.findWhere({node_id:data});
                    key = key+1;
                    newWorkflowNodes({data:node,length:key});
                })
            }else{
                _.each(nodes.models,function(data,key){
                    key = key+1;
                    newWorkflowNodes({data:data,length:key});
                })
            }
            head.Util.ajaxDel();
        }
        
        //节点编辑
        editNode = function(nodeId){
            $("#workflow-node-edit").empty();
            var node = nodes.findWhere({node_id:nodeId.toString()});
            if (_.isEmpty(node)){
                return false;
            }
            $("#newWorkflowModal #form-data").html(workflowNodeEditTemplate({data:node}));
            $('#newWorkflowModal').modal();
            $('#form-data #WorkflowNode_node_type option[value="'+node.get('node_type')+'"]').attr('selected', true);
            if (node.get('executor') == 'user_group'){
                $('#form-data #WorkflowNode_executor_0').attr("checked",true);
            }else if (node.get('executor') == 'system_position'){
                $('#form-data #WorkflowNode_executor_1').attr("checked",true);
            }else if (node.get('executor') == 'external_user_level'){
                $('#form-data #WorkflowNode_executor_2').attr("checked",true);
            }else if (node.get('executor') == 'external_assigned_user'){
                $('#form-data #WorkflowNode_executor_3').attr("checked",true);
            }
            var executeSelect = document.createElement("select");
            var labelExecutorId = $('#label-executorid');
            if (!_.isUndefined(node.get('executeTag')) && (node.get('executeTag') == '3')){
                rendSelectOne(executeSelect,labelExecutorId);
                $('#form-data #executeId option[value="'+node.get('executeId')+'"]').attr('selected', true);
            }else if(!_.isUndefined(node.get('executeTag')) && (node.get('executeTag') == '1')){
                rendSelectTwo(executeSelect,labelExecutorId);
                $('#form-data #branchType option[value="'+node.get('branchType')+'"]').attr('selected', true);
                $(executeSelect).change();
                var executeIds = (node.get('executeId')).split(",");
                _.each(executeIds,function(val,key){
                    $('#form-data #executeId option[value="'+val+'"]').attr('selected', true);
                })
            }
        }
        
        appendHtml = function(_this){
            var selected = $(_this).val();
            if (selectIndex == selected){
                return false;
            }
            var labelExecutorId = $('#label-executorid');
            labelExecutorId.empty();
            executeSelect = document.createElement("select");  
            //管理多校园
            if (selected === 'user_group'){
                rendSelectOne(executeSelect,labelExecutorId);
            }else if(selected === 'system_position'){
                rendSelectTwo(executeSelect,labelExecutorId);
            }
            selectIndex = selected;
        }
        
        //渲染下拉框
        rendSelectOne = function(executeSelect,labelExecutorId){
             //添加下拉框
            executeSelect.id = "executeId";
            executeSelect.name = "WorkflowNode[executeId]";
            executeSelect.className = 'form-control length_5';
            executeSelect.options.add(new Option("<?php echo Yii::t('global', 'Please Select');?>","")); 
            _.each(multSchool,function(key,value){
                executeSelect.options.add(new Option(key,value)); 
            })
            labelExecutorId.html(executeSelect);
        }
        
        //渲染下拉框
        rendSelectTwo = function(executeSelect,labelExecutorId){
            //添加下拉框
            executeSelect.id = "branchType";
            executeSelect.name = "WorkflowNode[branchType]";
            executeSelect.className = 'form-control length_5';
            executeSelect.options.add(new Option("<?php echo Yii::t('global', 'Please Select');?>","")); 
            _.each(branchTypeList,function(key,value){
                executeSelect.options.add(new Option(key,value)); 
            })
            labelExecutorId.html(executeSelect);
            selectTwo = executeSelect;
            $('#branchType').attr("onchange","rendSelectThree(this)");
        }
        
        rendSelectThree = function(_this){
            var labelExecutorId = $('#label-executorid');
            var positionSelect = document.createElement("select"); 
            positionSelect.id = 'executeId';
            positionSelect.name = "WorkflowNode[executeId][]";
            positionSelect.size = 15;
            positionSelect.className = 'form-control length_5';
            if (!_.isEmpty(positionList[$(_this).val()])){
                _.each(positionList[$(_this).val()],function(value,key){
                    positionSelect.options.add(new Option(value,key)); 
                });
            }
            labelExecutorId.empty();
            labelExecutorId.append(selectTwo);
            labelExecutorId.append(positionSelect);
            $("#executeId").attr('multiple','multiple');
        }
        
        //节点更新回调函数
        saveNodeCallback = function(data){
            $('#newWorkflowModal').modal('hide');
            var node = nodes.findWhere({node_id:data.node_id});
            node.set({
                node_name_cn:data.node_name_cn,
                node_name_en:data.node_name_en,
                node_type:data.node_type,
                executor:data.executor,
                executeId:data.executeId,
                branchType:data.branchType,
                executeTag:data.executeTag,
                field:data.field,
                field2:data.field2,
            });
            rendWorkflowList();
        }
        
        nodeSort = function(){
            $("#workflow-node-list").find('div.workflow-block').each(function(){
                nodesort +=  (_.isEmpty(nodesort)) ?  $(this).data('nodeid') : ","+$(this).data('nodeid') ;
            })
            if (nodesort && definationModel.node_order != nodesort){
                $.ajax(
                { 
                    url: "<?php echo $this->createUrl('//workflow/admin/saveNodeSort');?>", 
                    data: {node_order:nodesort,defination_id:definationModel.defination_id},
                    async:false,
                    type:'POST',
                    dataType:'json',
                    success: function(data){
                        if (data.state === 'success'){
                            resultTip({msg: data.message, error: 1});
                            definationModel.node_order = data.data.node_order;
                            rendWorkflowList();
                        }
                    }
                });
            }
            nodesort = '';
        }
        rendWorkflowList();
    })
    //初始化函数
    $( ".sortable" ).sortable({axis:'x',cursor:'move'});
    
    //初始化对象
    var nodes = new nodesCollection();
    //初始化数据
    _.each(nodeList,function(data,key){
        nodes.add(data);
    })
</script>