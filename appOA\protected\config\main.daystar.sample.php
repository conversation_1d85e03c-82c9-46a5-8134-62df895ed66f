<?php

// uncomment the following to define a path alias
// Yii::setPathOfAlias('local','path/to/local-folder');

// This is the main Web application configuration. Any writable
// CWebApplication properties can be configured here.
$dbname = "mims";
$subdbname = "mimssub";
$dbuser = "root";
$dbpass = "";

$senderConfig = array(
    'host' => 'mail.daystarchina.cn',
    'port' => 26,
    'username' => '<EMAIL>',
    'password' => 'daystar13579',
    'sender' => '<EMAIL>',
    'from' => '<EMAIL>',
    'fromname' => 'Daystar',
);

$cookieDomain = ".ivynew.cn";
$baseUrl = "http://ds.ivynew.cn";

$loginUrl = '/user/auth/login';
$logoutUrl = "/user/auth/logout";
$OABaseUrl = "http://oa.ivynew.cn";
$OAUploadBaseUrl = "http://oa.ivyonline.cn/uploads";
//$OAUploadBaseUrl = "http://oa.ivynew.cn/uploads";
$OAUploadBasePath = "F:/works/ivyoa/trunk/oasrc/htdocs/uploads";

$OABaseUploadPath = "";

$ccPreviewUrl = "http://apps-child.ivynew.cn/";

$basePath = dirname(__FILE__) . DIRECTORY_SEPARATOR . '..';

$uploadBaseUrl = $baseUrl . "/uploads/";
$uploadPath = $basePath . "/../uploads";

$xoopsVarPath = 'F:/works/ivyoa/trunk/oasrc/XOOPS-data';
$respondeUrl= $OABaseUrl.'/responder/responder.php';

$themesUrl = "http://apps.ivynew.cn/themes"; //theme地址，可以设置成单独的一个域名
$themeBasePath = dirname(__FILE__) . DIRECTORY_SEPARATOR . '..' . DIRECTORY_SEPARATOR . '..' . DIRECTORY_SEPARATOR . 'themes';

$standardLogin = true;

return array(
	'basePath'=>$basePath,
	'name'=>'Daystar@IvyOnline',
	'language'=>'zh_cn',

	// preloading 'log' component
	'preload'=>array('log'),
	'defaultController'=>'backend/apps/index',
//	'defaultController'=>'user/login',
	'theme'=>'modern',

	// autoloading model and component classes
	'import'=>array(
		'common.models.*',
		'common.components.*',
		'application.components.*',
        'application.modules.rights.*',
        'application.modules.rights.components.*',
//		'application.modules.srbac.controllers.SBaseController'
	),

	'modules'=>array(
		// uncomment the following to enable the Gii tool
		'gii'=>array(
			'class'=>'system.gii.GiiModule',
			'password'=>'test!',
		 	// If removed, Gii defaults to localhost only. Edit carefully to taste.
			'ipFilters'=>array('127.0.0.1','::1'),
		),
		'backend',
		'child',
		'teaching',
		'api'=>array(
			'ipFilters'=>array('127.0.0.1','::1'),
		),
		'settings','charts','club',
		'operations','operation',
        'user'=>array(
            //'loginUserType' => 'parent',
            'limitCampusIds' => array('BJ_DS','BJ_OG'),
            //'excludeCampusIds' => array('BJ_OE','BJ_OG'),
        ),
		'srbac'=>array(
			'userclass'=>'User',
			'userid'=>'uid',
			'username'=>'email',
			'delimeter'=>'@',
			'debug'=>false,
			'pageSize'=>50,
			'superUser'=>'superDude',
			'css'=>'srbac.css',
			'ipFilters'=>array('127.0.0.1','::1'),
		),
        'msettings','mcampus','mpub','mteaching','moperation','mhr',
        'rights'=>array('install'=>false),
		'webshell',
	),

	// application components
	'components'=>array(
        'session'=>array(
            'autoCreateSessionTable'=>false,
            'class'=>'application.components.CXoDbHttpSession',
            'cookieParams'=>array('domain'=>$cookieDomain,'lifetime'=>0,'path'=>'/'),
            'connectionID' => 'db',
            'timeout' => 28800,
            'sessionTableName' =>'ivy_session',
            'sessionName' => 'XOSESSIONID2',
        ),
		'authManager'=>array(
//			'class'=>'RDbAuthManager',
			'class'=>'application.modules.srbac.components.SDbAuthManager',
			'connectionID'=>'db',
			'itemTable'=>'auth_oa_items',
			'assignmentTable'=>'auth_oa_assignments',
			'itemChildTable'=>'auth_oa_itemchildren',
            'defaultRoles'=>array('ivystaff'),
		),
		'user'=>array(
			// enable cookie-based authentication
            'class'=>'application.components.CXoWebUserStaff',
//            'class'=>'RWebUser',
			'loginUrl'=>$loginUrl,
			'logoutUrl' =>$logoutUrl,
            'allowAutoLogin'=>true,
		),
        'themeManager'=>array(
            'basePath'=>$themeBasePath,
            'baseUrl'=>$themesUrl
        ),
		// uncomment the following to enable URLs in path-format
		'urlManager'=>array(
			'showScriptName'=>false,
			'urlFormat'=>'path',
			'rules'=>array(
				'previewCC/<id:.*>'=>'backend/frontend/index',
				'<module:\w+>/<controller:\w+>/<action:\w+>' => '<module>/<controller>/<action>',
				'<controller:\w+>/<id:\d+>'=>'<controller>/view',
				'<controller:\w+>/<action:\w+>/<id:\d+>'=>'<controller>/<action>',
				'<controller:\w+>/<action:\w+>'=>'<controller>/<action>',
			),
		),
		'db'=>array(
			'connectionString' => 'mysql:host=localhost;dbname='.$dbname,
			'emulatePrepare' => true,
			'username' => $dbuser,
			'password' => $dbpass,
			'charset' => 'utf8',
			'schemaCachingDuration' => 30000,
            'enableProfiling'=>true,
            'enableParamLogging' => true,
		),
		'subdb'=>array(
            'class'=>'CDbConnection',
			'connectionString' => 'mysql:host=localhost;dbname='.$subdbname,
			'emulatePrepare' => true,
			'username' => $dbuser,
			'password' => $dbpass,
			'charset' => 'utf8',
//			'schemaCachingDuration' => 30000,
		),
		'cache'=>array(
		   'class'=>'CFileCache',
		),
		'errorHandler'=>array(
			// use 'site/error' action to display errors
            //'errorAction'=>'backend/default',
        ),
		'log'=>array(
			'class'=>'CLogRouter',
			'routes'=>array(
//                array(
//                    'class'=>'ext.yii-debug-toolbar.YiiDebugToolbarRoute',
//                    'ipFilters'=>array('127.0.0.1'),
//                ),
                array(
                    'class'=>'ext.db_profiler.DbProfileLogRoute',
                    'countLimit' => 1, // How many times the same query should be executed to be considered inefficient
                    'slowQueryMin' => 0.01, // Minimum time for the query to be slow
                    'enabled' => false
                ),
				array(
					'class'=>'CFileLogRoute',
					'levels'=>'error, warning',
				),
                array(
                    'class'=>'CDbLogRoute',
                    'autoCreateLogTable'=>true,
                    'levels'=>'info',
                    'connectionID'=>'subdb',
                    'logTableName'=>'mimslog',
                ),
//				array( // configuration for the toolbar
//				  'class'=>'ext.yiidebugtb.XWebDebugRouter',
//				  'config'=>'alignLeft, opaque, runInDebug, fixedPos, collapsed, yamlStyle',
//				  'levels'=>'error, warning, trace, profile, info',
//				  'allowedIPs'=>array('127.0.0.1','::1','************','192\.168\.1[0-5]\.[0-9]{3}'),
//				),
				// uncomment the following to show log messages on web pages
				/*
				array(
					'class'=>'CWebLogRoute',
				),
				*/
			),
		),
		'image'=>array(
          'class'=>'application.extensions.image.CImageComponent',
            // GD or ImageMagick
            'driver'=>'GD',
            // ImageMagick setup path
            //'params'=>array('directory'=>'/opt/local/bin'),
        ),
		'widgetFactory'=>array(
			'widgets'=>array(
				'CGridView'=>array(
					'cssFile'=>false,
				)
			)
		),
	),

	// application-level parameters that can be accessed
	// using Yii::app()->params['paramName']
	'params'=>array(
		// this is used in contact page
        'siteFlag'=>'daystar',
        'defaultCampusId' => 'TJ_ES', //若有管理多校园的帐号进入系统，默认为该校园; 比如TYG的员工登录DS的校园SITE,指定显示DS的信息，而非TYG的信息
		'adminEmail'=>'<EMAIL>',
		'ccPreviewUrl' => $ccPreviewUrl,
        'OABaseUrl' => $OABaseUrl,
        'OAUploadBaseUrl' => $OAUploadBaseUrl,
        'OAUploadBasePath' => $OAUploadBasePath,
        'OABaseUploadPath' => $OABaseUploadPath,
		'assetsVersion' => '20130505',
		'uploadBaseUrl' => $uploadBaseUrl,
		'uploadPath' => $uploadPath,
        'xoopsVarPath' => $xoopsVarPath,
        'YeepayRespondeUrl' => $respondeUrl,
		'mediaNotifyUrl' => 'http://apps.ivynew.cn/ivyMediaResponder/weeklyPhoto',
        'standardLogin' => $standardLogin,
        'senderConfig' => $senderConfig,
	),
);