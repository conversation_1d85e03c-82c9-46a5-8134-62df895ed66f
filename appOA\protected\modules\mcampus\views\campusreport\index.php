<?php 
$purchaseProducts = Yii::app()->request->getParam('PurchaseProducts','');
 ?>

<div class="container-fluid">
	<ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site','HQ Operations'), array

('default/index'));?></li>
        <li><?php echo CHtml::link(Yii::t('site','Support'), array('default/index'));?></li>
        <li class="active"><?php echo Yii::t('site','园长报告') ;?></li>
    </ol>
	<div class="form-horizontal">
		<div class="form-group">
			<div class="col-sm-2">
				<?php echo CHtml::dropDownList( 'weeknumber', $weekids, $week,array('id'=>'weeknumber','onchange'=>'getContent(this.value)','class'=>'form-control'));   ?>
			</div>
		</div>
	</div>
	<div class="row">
        <div class="col-md-2">
            <div class="list-group" id="classroom-status-list">
                <?php if($report): ?>
					<div class="list-group-item status-filter active" id= "weeknumber" ><span><?php echo $weekids; ?></span><span>已完成</span><span><?php echo $schoolidtitle[0]['title']?></span></div>
					<span  class="list-group-item status-filter" style="cursor:pointer" onclick="preview(0,0)">预览</span>			
					<?php else: ?>
					<div class="list-group-item status-filter active" id= "weeknumber" ><span><?php echo $weekids; ?></span><span>未完成</span><span><?php echo $schoolidtitle[0]['title']?></span></div>
					<span  class="list-group-item status-filter" style="cursor: pointer" onclick="injuries(0)">受伤报告</span>
					<span  class="list-group-item status-filter" style="cursor: pointer" onclick="aiviter(0)">主要活动及建议</span>
					<span  class="list-group-item status-filter " style="cursor: pointer" onclick="preview(1,3)">预览</span>
				<?php endif; ?>	
            </div>
        </div>
		<!--受伤报告列表 -->
	<div class="col-md-10" id="injureds">
		<div class="panel-body">
			
		</div>
	</div>
    </div>
</div>
<!--主要活动及建议 -->
<div class="modal fade" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
  <div class="modal-dialog" role="document">
    <div class="modal-content" id="cancelinvoices">
      
    </div>
  </div>
</div>
<?php $this->renderPartial('//layouts/common/branchSelectBottom');?>

<script>

function cbinjured(){
	$('#myModal').modal('hide');
	injuries(0);
   // $.fn.yiiGridView.update('injured_report');
}

<!--主要活动及建议 -->
function aiviter(id){
	var found = '<?php echo $found  ?>'
	var weeknumber = '<?php echo $weekids  ?>'
		$.ajax({
            type: "POST",
			url: "<?php echo $this->createUrl('activites'); ?>",
			data: {"found":found,"weeknumber":weeknumber}, 
			dataType: "json",
			success: function(data){
				if(data){
					$('#cancelinvoices').html(data.data)
					$('#myModal').modal('show');
					head.Util.ajaxForm( $('#myModal') );
				}
			}
		});
}
<!--受伤报告 -->
function injuries(id){
	var found = '<?php echo $found  ?>'
	var weeknumber = '<?php echo $weekids  ?>'
	var style = '1'
		$.ajax({
            type: "POST",
			url: "<?php echo $this->createUrl('injuries'); ?>",
			data: {"found":found,"weeknumber":weeknumber,'style':style}, 
			dataType: "json",
			success: function(data){
				if(data){
					$('#injureds').html(data.data)
					head.Util.ajaxForm( $('#injureds') );
				}
			}
		});
}
<!--预览 -->
function preview(style,status){
	var found = '<?php echo $found  ?>'
	var weeknumber = '<?php echo $weekids  ?>'
		$.ajax({
            type: "POST",
			url: "<?php echo $this->createUrl('preview'); ?>",
			data: {"found":found,"weeknumber":weeknumber,"style":style,"status":status}, 
			dataType: "json",
			success: function(data){
					if(data.state == 'success'){
						$('#injureds').html(data.data)
					}else if(data.state == 'updata'){
						alert("成功")
						location.reload()
					}else if(data.state == 'delete'){
						alert("撤销成功")
						location.reload()
					}else{
						$('#injureds').html(data.data)
					}
				}
		});
}

function cbInvoice(data){
	$('#cancelinvoices').html(data)
}

function getContent(value){
		var weekid = value;
		$(weekid).attr("selected", true); //设置Select的Text值为jQuery的项选中 
		window.location.href="<?php echo $this->createUrl('Index');?>"+'&weekid='+ weekid;
	}

</script>