<?php
if (true === $this->lang || 'cn' == $this->lang) {
    $df_cn = new CDateFormatter('zh-cn');
    $this->render('mailHeader_lang', array('lang' => 'cn'));
    ?>

    <div style="font-family: 'Microsoft Yahei'">

        <p>亲爱的家长，</p>

        <p>此邮件确认 [<?php echo $class_name; ?>] 班的 [<?php echo $child_name; ?>] 修改了午餐信息：</p>

        <table cellspacing="0" cellpadding="0" border="1" align="center" style="border-collapse:collapse;width:320px;">
            <tr>
                <th width="50%" style="color:#000000;font-family:'Microsoft Yahei';font-size: 12px;" bgcolor="#f2f2f2">操作
                </th>
                <th width="50%" style="color:#999999;font-family:'Microsoft Yahei';font-size: 12px;" bgcolor="#dedede">目标日期
                </th>
            </tr>
            <tr>
                <td style="color:#000000;font-size:12px;padding:4px;font-family:'Microsoft Yahei'" bgcolor="#f2f2f2">
                    <?php
                    if ('cancel' == $action) {
                        echo '取消午餐';
                    } else if ('recover' == $action) {
                        echo '恢复午餐';
                    }
                    ?>
                </td>
                <td style="color:#999999;font-size:12px;padding:4px;font-family:'Microsoft Yahei'" bgcolor="#dedede">
                    <?php echo $df_cn->formatDateTime($update_date, 'medium',null); ?>
                </td>
            </tr>
        </table>

        <p>如遇到问题，请联系校园 <?php echo CHtml::link($support_email, 'mailto:' . $support_email) ?>。</p>

        <p>
            <?php echo $df_cn->formatDateTime($date_time, 'medium', 'short'); ?>
        </p>

        <p>该邮件为系统自动发送。</p>

    </div>

    <?php
    $this->render('mailFooter_lang', array('lang' => 'cn'));
}
?>


<?php
if (true === $this->lang || 'en' == $this->lang) {
    $df_en = new CDateFormatter('en-us');
    $this->render('mailHeader_lang', array('lang' => 'en'));
    ?>

    <div style="font-family: 'Trebuchet MS','Microsoft Yahei',Arial,Helvetica,sans-serif">

        <p>Dear Parents,</p>

        <p>This confirms that [<?php echo $child_name; ?>] at class [<?php echo $class_name; ?>] has made the following change to his/her lunch:</p>

        <table cellspacing="0" cellpadding="0" border="1" align="center" style="border-collapse:collapse;width:320px;">
            <tr>
                <th width="50%" style="color:#000000;font-family:'Trebuchet MS';font-size: 12px;" bgcolor="#f2f2f2">Operation
                </th>
                <th width="50%" style="color:#999999;font-family:'Trebuchet MS';font-size: 12px;" bgcolor="#dedede">Date
                </th>
            </tr>
            <tr>
                <td style="color:#000000;font-size:12px;padding:4px;font-family:'Trebuchet MS'" bgcolor="#f2f2f2">
                    <?php
                    if ('cancel' == $action) {
                        echo 'Cancel Lunch';
                    } else if ('recover' == $action) {
                        echo 'Resume Lunch';
                    }
                    ?>
                </td>
                <td style="color:#999999;font-size:12px;padding:4px;font-family:'Trebuchet MS'" bgcolor="#dedede">
                    <?php echo $df_en->formatDateTime($update_date, 'medium',null); ?>
                </td>
            </tr>
        </table>

        <p>If you have any questions, please feel free to contact the campus <?php echo CHtml::link($support_email, 'mailto:' . $support_email) ?>.</p>

        <p>
            <?php echo $df_en->formatDateTime($date_time, 'medium', 'short'); ?>
        </p>

        <p>This is an automatic email.</p>

    </div>

    <?php
    $this->render('mailFooter_lang', array('lang' => 'en'));
}
?>
