<?php if(!empty($model)):?>
<table width="100%" >
	<colgroup>
		<col width="140">
	</colgroup>
	<tbody>
		<tr>
			<th>
				<?php echo CHtml::activeLabelEx($model,'user_avatar'); ?>
			</th>
			<td>
				<?php
				if($model->user_avatar):
					echo CHtml::openTag('p');
					echo CHtml::image(OA::CreateOAUploadUrl('users', $model->user_avatar), '');
					echo CHtml::closeTag('p');
				endif;
				?>
				<?php echo CHtml::activeFileField($model,'uploadPhoto', array('name'=>"User[$role][uploadPhoto]")) ?>
				<?php echo CHtml::error($model,'uploadPhoto'); ?>
			</td>
		</tr>
		<?php foreach(array("cn_name", "en_firstname", "en_lastname") as $key):?>
		<tr>
			<th>
				<?php echo CHtml::activeLabelEx($model->parent,$key); ?>
			</th>
			<td>
				<?php echo CHtml::activeTextField($model->parent,$key,array('name'=>"IvyParent[$role][$key]",'class'=>'input')) ?>
			</td>
		</tr>
		<?php endforeach;?>
        <tr>
            <th>
                <?php echo CHtml::activeLabelEx($model->parent,'relationship'); ?>
            </th>
            <td>
                <?php
                echo CHtml::activeDropDownList($model->parent,'relationship', IvyParent::config(), array('name'=>"IvyParent[$role][relationship]")) ?>
            </td>
        </tr>
		<tr>
			<th>
				<?php echo CHtml::activeLabelEx($model->parent,'gender'); ?>
			</th>
			<td>
				<?php echo CHtml::activeDropDownList($model->parent,'gender', OA::getChildGenderList(), array('name'=>"IvyParent[$role][gender]", 'disabled'=>'disabled')) ?>
			</td>
		</tr>

		<tr>
			<th>
				<?php echo CHtml::activeLabelEx($model->parent,'country'); ?>
			</th>
			<td>
				<?php
				echo CHtml::activeDropDownList($model->parent,'country', Country::getData(), array('name'=>"IvyParent[$role][country]",'empty' => Yii::t("global", 'Please Select'), 'class'=>'length_3')) ?>
			</td>
		</tr>
        <!--沟通语言--->
        <tr>
            <th>
                <?php echo Yii::t('child','Language'); ?>
            </th>
            <td>
                <?php
                echo CHtml::activeDropDownList($model->parent,'language',IvyParent::model()->getLanguage(), array('name'=>"IvyParent[$role][language]",'empty' => Yii::t("global", 'Please Select'), 'class'=>'length_3')) ?>
            </td>
        </tr>

		<?php foreach(array("company", "job", "tel", "fax", "mphone","ID_card") as $key):?>
		<tr>
			<th>
				<?php echo CHtml::activeLabelEx($model->parent,$key); ?>
			</th>
			<td>
				<?php echo CHtml::activeTextField($model->parent,$key,array('name'=>"IvyParent[$role][$key]",'class'=>'input')) ?>
			</td>
		</tr>
		<?php endforeach;?>
		<tr>
			<th>
				<?php echo CHtml::activeLabelEx($model, 'email'); ?>
			</th>
			<td>
				<?php echo CHtml::link($model->email, 'mailto:'.$model->email) ?>
			</td>
		</tr>
	</tbody>
</table>
<?php else:?>
	<?php echo Yii::t("child","Create a parent account from :parentLink", array(':parentLink'=>CHtml::link(Yii::t("child", "Parent Account"), array("//child/profile/accounts"))) );?>
<?php endif;?>