/**
 * Copyright (c) Tiny Technologies, Inc. All rights reserved.
 * Licensed under the LGPL or a commercial license.
 * For LGPL see License.txt in the project root for license information.
 * For commercial licenses see https://www.tiny.cloud/
 *
 * Version: 5.1.6 (2020-01-28)
 */
!function(l){"use strict";var x=function(){return(x=Object.assign||function(n){for(var e,t=1,r=arguments.length;t<r;t++)for(var o in e=arguments[t])Object.prototype.hasOwnProperty.call(e,o)&&(n[o]=e[o]);return n}).apply(this,arguments)};function u(n,e){var t={};for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&e.indexOf(r)<0&&(t[r]=n[r]);if(null!=n&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(n);o<r.length;o++)e.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(n,r[o])&&(t[r[o]]=n[r[o]])}return t}function w(){}function y(n){return n}var i=function(t,r){return function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];return t(r.apply(null,n))}},b=function(n){return function(){return n}};function d(r){for(var o=[],n=1;n<arguments.length;n++)o[n-1]=arguments[n];return function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];var t=o.concat(n);return r.apply(null,t)}}function m(t){return function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];return!t.apply(null,n)}}function o(n){return function(){throw new Error(n)}}function t(n){return n()}function n(){return f}var e,c=b(!1),a=b(!0),f=(e={fold:function(n,e){return n()},is:c,isSome:c,isNone:a,getOr:g,getOrThunk:s,getOrDie:function(n){throw new Error(n||"error: getOrDie called on none.")},getOrNull:b(null),getOrUndefined:b(undefined),or:g,orThunk:s,map:n,each:w,bind:n,exists:c,forall:a,filter:n,equals:r,equals_:r,toArray:function(){return[]},toString:b("none()")},Object.freeze&&Object.freeze(e),e);function r(n){return n.isNone()}function s(n){return n()}function g(n){return n}function S(n,t){return jn(n,function(n,e){return{k:e,v:t(n,e)}})}function p(n,e){return Vn.call(n,e)}function h(n,e){var t=function(n,e){for(var t=0;t<n.length;t++){var r=n[t];if(r.test(e))return r}return undefined}(n,e);if(!t)return{major:0,minor:0};function r(n){return Number(e.replace(t,"$"+n))}return zn(r(1),r(2))}function v(n,e){return function(){return e===n}}function O(n,e){return function(){return e===n}}function T(e){return function(n){return function(n){if(null===n)return"null";var e=typeof n;return"object"==e&&(Array.prototype.isPrototypeOf(n)||n.constructor&&"Array"===n.constructor.name)?"array":"object"==e&&(String.prototype.isPrototypeOf(n)||n.constructor&&"String"===n.constructor.name)?"string":e}(n)===e}}function k(n,e){return-1<function(n,e){return le.call(n,e)}(n,e)}function E(n,e){for(var t=0,r=n.length;t<r;t++){if(e(n[t],t))return!0}return!1}function C(n,e){for(var t=0,r=n.length;t<r;t++){e(n[t],t)}}function D(n,e){for(var t=[],r=0,o=n.length;r<o;r++){var i=n[r];e(i,r)&&t.push(i)}return t}function M(n,e,t){return function(n,e){for(var t=n.length-1;0<=t;t--){e(n[t],t)}}(n,function(n){t=e(t,n)}),t}function I(n,e,t){return C(n,function(n){t=e(t,n)}),t}function R(n,e){for(var t=0,r=n.length;t<r;t++){var o=n[t];if(e(o,t))return Fn.some(o)}return Fn.none()}function A(n,e){for(var t=0,r=n.length;t<r;t++){if(e(n[t],t))return Fn.some(t)}return Fn.none()}function F(n){for(var e=[],t=0,r=n.length;t<r;++t){if(!ue(n[t]))throw new Error("Arr.flatten item "+t+" was not an array, input: "+n);de.apply(e,n[t])}return e}function B(n,e){var t=me(n,e);return F(t)}function V(n,e){for(var t=0,r=n.length;t<r;++t){if(!0!==e(n[t],t))return!1}return!0}function N(n){var e=se.call(n,0);return e.reverse(),e}function j(n,e){return D(n,function(n){return!k(e,n)})}function _(n){return[n]}function P(n,e){var t=String(e).toLowerCase();return R(n,function(n){return n.search(t)})}function H(n,e){return-1!==n.indexOf(e)}function z(e){return function(n){return H(n,e)}}function L(){return xe.get()}function G(n,e){Ke(n,n.element(),e,{})}function U(n,e,t){Ke(n,n.element(),e,t)}function $(n){G(n,He())}function W(n,e,t){Ke(n,e,t,{})}function X(t){var r,o=!1;return function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];return o||(o=!0,r=t.apply(null,n)),r}}function q(n){return n.dom().nodeName.toLowerCase()}function Y(e){return function(n){return function(n){return n.dom().nodeType}(n)===e}}function K(n){var e=rt(n)?n.dom().parentNode:n.dom();return e!==undefined&&null!==e&&e.ownerDocument.body.contains(e)}function J(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];if(e.length!==t.length)throw new Error('Wrong number of arguments to struct. Expected "['+e.length+']", got '+t.length+" arguments");var r={};return C(e,function(n,e){r[n]=b(t[e])}),r}}function Q(n){return n.slice(0).sort()}function Z(e,n){if(!ue(n))throw new Error("The "+e+" fields must be an array. Was: "+n+".");C(n,function(n){if(!oe(n))throw new Error("The value "+n+" in the "+e+" fields was not a string.")})}function nn(o,i){var u=o.concat(i);if(0===u.length)throw new Error("You must specify at least one required or optional field.");return Z("required",o),Z("optional",i),function(n){var t=Q(n);R(t,function(n,e){return e<t.length-1&&n===t[e+1]}).each(function(n){throw new Error("The field: "+n+" occurs more than once in the combined fields: ["+t.join(", ")+"].")})}(u),function(e){var t=Bn(e);V(o,function(n){return k(t,n)})||function(n,e){throw new Error("All required keys ("+Q(n).join(", ")+") were not specified. Specified keys were: "+Q(e).join(", ")+".")}(o,t);var n=D(t,function(n){return!k(u,n)});0<n.length&&function(n){throw new Error("Unsupported keys for object: "+Q(n).join(", "))}(n);var r={};return C(o,function(n){r[n]=b(e[n])}),C(i,function(n){r[n]=b(Object.prototype.hasOwnProperty.call(e,n)?Fn.some(e[n]):Fn.none())}),r}}function en(n,e,t){return 0!=(n.compareDocumentPosition(e)&t)}function tn(n,e){var t=n.dom();if(t.nodeType!==ct)return!1;var r=t;if(r.matches!==undefined)return r.matches(e);if(r.msMatchesSelector!==undefined)return r.msMatchesSelector(e);if(r.webkitMatchesSelector!==undefined)return r.webkitMatchesSelector(e);if(r.mozMatchesSelector!==undefined)return r.mozMatchesSelector(e);throw new Error("Browser lacks native selectors")}function rn(n){return n.nodeType!==ct&&n.nodeType!==at||0===n.childElementCount}function on(n,e){var t=e===undefined?l.document:e.dom();return rn(t)?[]:me(t.querySelectorAll(n),Qe.fromDom)}function un(n,e){var t=e===undefined?l.document:e.dom();return rn(t)?Fn.none():Fn.from(t.querySelector(n)).map(Qe.fromDom)}function cn(n,e){return n.dom()===e.dom()}function an(n){return Qe.fromDom(n.dom().ownerDocument)}function fn(n){return Fn.from(n.dom().parentNode).map(Qe.fromDom)}function sn(n,e){var t=n.dom().childNodes;return Fn.from(t[e]).map(Qe.fromDom)}function ln(e,t){fn(e).each(function(n){n.dom().insertBefore(t.dom(),e.dom())})}function dn(n,e){(function(n){return Fn.from(n.dom().nextSibling).map(Qe.fromDom)})(n).fold(function(){fn(n).each(function(n){st(n,e)})},function(n){ln(n,e)})}function mn(e,t){(function(n){return sn(n,0)})(e).fold(function(){st(e,t)},function(n){e.dom().insertBefore(t.dom(),n.dom())})}function gn(e,n){C(n,function(n){st(e,n)})}function pn(n){n.dom().textContent="",C(ft(n),function(n){lt(n)})}function hn(n,e){st(n.element(),e.element())}function vn(e,n){var t=e.components();!function(n){C(n.components(),function(n){return lt(n.element())}),pn(n.element()),n.syncComponents()}(e);var r=j(t,n);C(r,function(n){dt(n),e.getSystem().removeFromWorld(n)}),C(n,function(n){n.getSystem().isConnected()?hn(e,n):(e.getSystem().addToWorld(n),hn(e,n),K(e.element())&&mt(n)),e.syncComponents()})}function yn(e){var n=fn(e.element()).bind(function(n){return e.getSystem().getByDom(n).toOption()});!function(n){dt(n),lt(n.element()),n.getSystem().removeFromWorld(n)}(e),n.each(function(n){n.syncComponents()})}function bn(u){return function(){for(var n=new Array(arguments.length),e=0;e<n.length;e++)n[e]=arguments[e];if(0===n.length)throw new Error("Can't merge zero objects");for(var t={},r=0;r<n.length;r++){var o=n[r];for(var i in o)xt.call(o,i)&&(t[i]=u(t[i],o[i]))}return t}}function xn(n){return Ot.defaultedThunk(b(n))}function wn(e){return function(n){return p(n,e)?Fn.from(n[e]):Fn.none()}}function Sn(n,e){return wn(e)(n)}function On(n,e){var t={};return t[n]=e,t}function Tn(n,e){return function(n,t){var r={};return Nn(n,function(n,e){k(t,e)||(r[e]=n)}),r}(n,e)}function kn(n,e){return function(e,t){return function(n){return p(n,e)?n[e]:t}}(n,e)}function En(n,e){return On(n,e)}function Cn(n){return function(n){var e={};return C(n,function(n){e[n.key]=n.value}),e}(n)}function Dn(n,e){var t=function(n){var e=[],t=[];return C(n,function(n){n.fold(function(n){e.push(n)},function(n){t.push(n)})}),{errors:e,values:t}}(n);return 0<t.errors.length?function(n){return yt.error(F(n))}(t.errors):function(n,e){return 0===n.length?yt.value(e):yt.value(wt(e,St.apply(undefined,n)))}(t.values,e)}function Mn(n,e){return function(n,e){return p(n,e)&&n[e]!==undefined&&null!==n[e]}(n,e)}var In,Rn,An=function(t){function n(){return o}function e(n){return n(t)}var r=b(t),o={fold:function(n,e){return e(t)},is:function(n){return t===n},isSome:a,isNone:c,getOr:r,getOrThunk:r,getOrDie:r,getOrNull:r,getOrUndefined:r,or:n,orThunk:n,map:function(n){return An(n(t))},each:function(n){n(t)},bind:e,exists:e,forall:e,filter:function(n){return n(t)?o:f},toArray:function(){return[t]},toString:function(){return"some("+t+")"},equals:function(n){return n.is(t)},equals_:function(n,e){return n.fold(c,function(n){return e(t,n)})}};return o},Fn={some:An,none:n,from:function(n){return null===n||n===undefined?f:An(n)}},Bn=Object.keys,Vn=Object.hasOwnProperty,Nn=function(n,e){for(var t=Bn(n),r=0,o=t.length;r<o;r++){var i=t[r];e(n[i],i)}},jn=function(n,r){var o={};return Nn(n,function(n,e){var t=r(n,e);o[t.k]=t.v}),o},_n=function(n,t){var r=[];return Nn(n,function(n,e){r.push(t(n,e))}),r},Pn=function(n){function e(){return t}var t=n;return{get:e,set:function(n){t=n},clone:function(){return Pn(e())}}},Hn=function(){return zn(0,0)},zn=function(n,e){return{major:n,minor:e}},Ln={nu:zn,detect:function(n,e){var t=String(e).toLowerCase();return 0===n.length?Hn():h(n,t)},unknown:Hn},Gn="Edge",Un="Chrome",$n="Opera",Wn="Firefox",Xn="Safari",qn=function(n){var e=n.current;return{current:e,version:n.version,isEdge:v(Gn,e),isChrome:v(Un,e),isIE:v("IE",e),isOpera:v($n,e),isFirefox:v(Wn,e),isSafari:v(Xn,e)}},Yn={unknown:function(){return qn({current:undefined,version:Ln.unknown()})},nu:qn,edge:b(Gn),chrome:b(Un),ie:b("IE"),opera:b($n),firefox:b(Wn),safari:b(Xn)},Kn="Windows",Jn="Android",Qn="Linux",Zn="Solaris",ne="FreeBSD",ee="ChromeOS",te=function(n){var e=n.current;return{current:e,version:n.version,isWindows:O(Kn,e),isiOS:O("iOS",e),isAndroid:O(Jn,e),isOSX:O("OSX",e),isLinux:O(Qn,e),isSolaris:O(Zn,e),isFreeBSD:O(ne,e),isChromeOS:O(ee,e)}},re={unknown:function(){return te({current:undefined,version:Ln.unknown()})},nu:te,windows:b(Kn),ios:b("iOS"),android:b(Jn),linux:b(Qn),osx:b("OSX"),solaris:b(Zn),freebsd:b(ne),chromeos:b(ee)},oe=T("string"),ie=T("object"),ue=T("array"),ce=T("boolean"),ae=T("function"),fe=T("number"),se=Array.prototype.slice,le=Array.prototype.indexOf,de=Array.prototype.push,me=function(n,e){for(var t=n.length,r=new Array(t),o=0;o<t;o++){var i=n[o];r[o]=e(i,o)}return r},ge=(ae(Array.from)&&Array.from,function(n,t){return P(n,t).map(function(n){var e=Ln.detect(n.versionRegexes,t);return{current:n.name,version:e}})}),pe=function(n,t){return P(n,t).map(function(n){var e=Ln.detect(n.versionRegexes,t);return{current:n.name,version:e}})},he=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,ve=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:function(n){return H(n,"edge/")&&H(n,"chrome")&&H(n,"safari")&&H(n,"applewebkit")}},{name:"Chrome",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,he],search:function(n){return H(n,"chrome")&&!H(n,"chromeframe")}},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:function(n){return H(n,"msie")||H(n,"trident")}},{name:"Opera",versionRegexes:[he,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:z("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:z("firefox")},{name:"Safari",versionRegexes:[he,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:function(n){return(H(n,"safari")||H(n,"mobile/"))&&H(n,"applewebkit")}}],ye=[{name:"Windows",search:z("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:function(n){return H(n,"iphone")||H(n,"ipad")},versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:z("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"OSX",search:z("mac os x"),versionRegexes:[/.*?mac\ os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:z("linux"),versionRegexes:[]},{name:"Solaris",search:z("sunos"),versionRegexes:[]},{name:"FreeBSD",search:z("freebsd"),versionRegexes:[]},{name:"ChromeOS",search:z("cros"),versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/]}],be={browsers:b(ve),oses:b(ye)},xe=Pn(function(n,e){var t=be.browsers(),r=be.oses(),o=ge(t,n).fold(Yn.unknown,Yn.nu),i=pe(r,n).fold(re.unknown,re.nu);return{browser:o,os:i,deviceType:function(n,e,t,r){var o=n.isiOS()&&!0===/ipad/i.test(t),i=n.isiOS()&&!o,u=n.isiOS()||n.isAndroid(),c=u||r("(pointer:coarse)"),a=o||!i&&u&&r("(min-device-width:768px)"),f=i||u&&!a,s=e.isSafari()&&n.isiOS()&&!1===/safari/i.test(t),l=!f&&!a&&!s;return{isiPad:b(o),isiPhone:b(i),isTablet:b(a),isPhone:b(f),isTouch:b(c),isAndroid:n.isAndroid,isiOS:n.isiOS,isWebView:b(s),isDesktop:b(l)}}(i,o,n,e)}}(l.navigator.userAgent,function(n){return l.window.matchMedia(n).matches})),we=b("touchstart"),Se=b("touchmove"),Oe=b("touchend"),Te=b("mousedown"),ke=b("mousemove"),Ee=b("mouseup"),Ce=b("mouseover"),De=b("keydown"),Me=b("keyup"),Ie=b("input"),Re=b("change"),Ae=b("click"),Fe=b("transitionend"),Be=b("selectstart"),Ve={tap:b("alloy.tap")},Ne=b("alloy.focus"),je=b("alloy.blur.post"),_e=b("alloy.paste.post"),Pe=b("alloy.receive"),He=b("alloy.execute"),ze=b("alloy.focus.item"),Le=Ve.tap,Ge=b("alloy.longpress"),Ue=b("alloy.system.init"),$e=b("alloy.system.attached"),We=b("alloy.system.detached"),Xe=b("alloy.focusmanager.shifted"),qe=b("alloy.highlight"),Ye=b("alloy.dehighlight"),Ke=function(n,e,t,r){var o=x({target:e},r);n.getSystem().triggerEvent(t,e,S(o,b))},Je=function(n){if(null===n||n===undefined)throw new Error("Node cannot be null or undefined");return{dom:b(n)}},Qe={fromHtml:function(n,e){var t=(e||l.document).createElement("div");if(t.innerHTML=n,!t.hasChildNodes()||1<t.childNodes.length)throw l.console.error("HTML does not have a single root node",n),new Error("HTML must have a single root node");return Je(t.childNodes[0])},fromTag:function(n,e){var t=(e||l.document).createElement(n);return Je(t)},fromText:function(n,e){var t=(e||l.document).createTextNode(n);return Je(t)},fromDom:Je,fromPoint:function(n,e,t){var r=n.dom();return Fn.from(r.elementFromPoint(e,t)).map(Je)}},Ze=(l.Node.ATTRIBUTE_NODE,l.Node.CDATA_SECTION_NODE,l.Node.COMMENT_NODE,l.Node.DOCUMENT_NODE),nt=(l.Node.DOCUMENT_TYPE_NODE,l.Node.DOCUMENT_FRAGMENT_NODE,l.Node.ELEMENT_NODE),et=l.Node.TEXT_NODE,tt=(l.Node.PROCESSING_INSTRUCTION_NODE,l.Node.ENTITY_REFERENCE_NODE,l.Node.ENTITY_NODE,l.Node.NOTATION_NODE,"undefined"!=typeof l.window?l.window:Function("return this;")(),Y(nt)),rt=Y(et),ot=X(function(){return it(Qe.fromDom(l.document))}),it=function(n){var e=n.dom().body;if(null===e||e===undefined)throw new Error("Body is not available yet");return Qe.fromDom(e)},ut=function(n,e){return en(n,e,l.Node.DOCUMENT_POSITION_CONTAINED_BY)},ct=nt,at=Ze,ft=(L().browser.isIE(),function(n){return me(n.dom().childNodes,Qe.fromDom)}),st=(J("element","offset"),function(n,e){n.dom().appendChild(e.dom())}),lt=function(n){var e=n.dom();null!==e.parentNode&&e.parentNode.removeChild(e)},dt=function(n){G(n,We());var e=n.components();C(e,dt)},mt=function(n){var e=n.components();C(e,mt),G(n,$e())},gt=function(n,e,t){n.getSystem().addToWorld(e),t(n.element(),e.element()),K(n.element())&&mt(e),n.syncComponents()},pt=function(n,e,t){t(n,e.element());var r=ft(e.element());C(r,function(n){e.getByDom(n).each(mt)})},ht=function(t){return{is:function(n){return t===n},isValue:a,isError:c,getOr:b(t),getOrThunk:b(t),getOrDie:b(t),or:function(n){return ht(t)},orThunk:function(n){return ht(t)},fold:function(n,e){return e(t)},map:function(n){return ht(n(t))},mapError:function(n){return ht(t)},each:function(n){n(t)},bind:function(n){return n(t)},exists:function(n){return n(t)},forall:function(n){return n(t)},toOption:function(){return Fn.some(t)}}},vt=function(t){return{is:c,isValue:c,isError:a,getOr:y,getOrThunk:function(n){return n()},getOrDie:function(){return o(String(t))()},or:function(n){return n},orThunk:function(n){return n()},fold:function(n,e){return n(t)},map:function(n){return vt(t)},mapError:function(n){return vt(n(t))},each:w,bind:function(n){return vt(t)},exists:c,forall:a,toOption:Fn.none}},yt={value:ht,error:vt,fromOption:function(n,e){return n.fold(function(){return vt(e)},ht)}},bt=function(u){if(!ue(u))throw new Error("cases must be an array");if(0===u.length)throw new Error("there must be at least one case");var c=[],t={};return C(u,function(n,r){var e=Bn(n);if(1!==e.length)throw new Error("one and only one name per case");var o=e[0],i=n[o];if(t[o]!==undefined)throw new Error("duplicate key detected:"+o);if("cata"===o)throw new Error("cannot have a case named cata (sorry)");if(!ue(i))throw new Error("case arguments must be an array");c.push(o),t[o]=function(){var n=arguments.length;if(n!==i.length)throw new Error("Wrong number of arguments to case "+o+". Expected "+i.length+" ("+i+"), got "+n);for(var t=new Array(n),e=0;e<t.length;e++)t[e]=arguments[e];return{fold:function(){if(arguments.length!==u.length)throw new Error("Wrong number of arguments to fold. Expected "+u.length+", got "+arguments.length);return arguments[r].apply(null,t)},match:function(n){var e=Bn(n);if(c.length!==e.length)throw new Error("Wrong number of arguments to match. Expected: "+c.join(",")+"\nActual: "+e.join(","));if(!V(c,function(n){return k(e,n)}))throw new Error("Not all branches were specified when using match. Specified: "+e.join(", ")+"\nRequired: "+c.join(", "));return n[o].apply(null,t)},log:function(n){l.console.log(n,{constructors:c,constructor:o,params:t})}}}}),t},xt=Object.prototype.hasOwnProperty,wt=bn(function(n,e){return ie(n)&&ie(e)?wt(n,e):e}),St=bn(function(n,e){return e}),Ot=bt([{strict:[]},{defaultedThunk:["fallbackThunk"]},{asOption:[]},{asDefaultedOptionThunk:["fallbackThunk"]},{mergeWithThunk:["baseThunk"]}]),Tt=Ot.strict,kt=Ot.asOption,Et=Ot.defaultedThunk,Ct=Ot.mergeWithThunk,Dt=(bt([{bothErrors:["error1","error2"]},{firstError:["error1","value2"]},{secondError:["value1","error2"]},{bothValues:["value1","value2"]}]),function(n){return wn(n)}),Mt=function(n,e){return Sn(n,e)};(Rn=In=In||{})[Rn.Error=0]="Error",Rn[Rn.Value=1]="Value";function It(n,e,t){return n.stype===In.Error?e(n.serror):t(n.svalue)}function Rt(n){return{stype:In.Value,svalue:n}}function At(n){return{stype:In.Error,serror:n}}function Ft(n){return i(mr,F)(n)}function Bt(n){return ie(n)&&100<Bn(n).length?" removed due to size":JSON.stringify(n,null,2)}function Vt(n,e){return mr([{path:n,getErrorInfo:e}])}function Nt(n,e,t){return Sn(e,t).fold(function(){return function(n,e,t){return Vt(n,function(){return'Could not find valid *strict* value for "'+e+'" in '+Bt(t)})}(n,t,e)},lr)}function jt(n,e,t){var r=Sn(n,e).fold(function(){return t(n)},y);return lr(r)}function _t(u,c,n,a){return n.fold(function(r,t,n,o){function i(n){var e=o.extract(u.concat([r]),a,n);return hr(e,function(n){return On(t,a(n))})}function e(n){return n.fold(function(){var n=On(t,a(Fn.none()));return lr(n)},function(n){var e=o.extract(u.concat([r]),a,n);return hr(e,function(n){return On(t,a(Fn.some(n)))})})}return n.fold(function(){return gr(Nt(u,c,r),i)},function(n){return gr(jt(c,r,n),i)},function(){return gr(function(n,e){return lr(Sn(n,e))}(c,r),e)},function(n){return gr(function(e,n,t){var r=Sn(e,n).map(function(n){return!0===n?t(e):n});return lr(r)}(c,r,n),e)},function(n){var e=n(c),t=hr(jt(c,r,b({})),function(n){return wt(e,n)});return gr(t,i)})},function(n,e){var t=e(c);return lr(On(n,a(t)))})}function Pt(r){return{extract:function(e,n,t){return pr(r(t,n),function(n){return function(n,e){return Vt(n,function(){return e})}(e,n)})},toString:function(){return"val"},toDsl:function(){return xr.itemOf(r)}}}function Ht(n){var i=Or(n),u=M(n,function(e,n){return n.fold(function(n){return wt(e,En(n,!0))},b(e))},{});return{extract:function(n,e,t){var r=ce(t)?[]:function(e){var n=Bn(e);return D(n,function(n){return Mn(e,n)})}(t),o=D(r,function(n){return!Mn(u,n)});return 0===o.length?i.extract(n,e,t):function(n,e){return Vt(n,function(){return"There are unsupported fields: ["+e.join(", ")+"] specified"})}(n,o)},toString:i.toString,toDsl:i.toDsl}}function zt(t,i){function u(n,e){return function(o){return{extract:function(t,r,n){var e=me(n,function(n,e){return o.extract(t.concat(["["+e+"]"]),r,n)});return br(e)},toString:function(){return"array("+o.toString()+")"},toDsl:function(){return xr.arrOf(o)}}}(Pt(t)).extract(n,y,e)}return{extract:function(t,r,o){var n=Bn(o),e=u(t,n);return gr(e,function(n){var e=me(n,function(n){return Sr.field(n,n,Tt(),i)});return Or(e).extract(t,r,o)})},toString:function(){return"setOf("+i.toString()+")"},toDsl:function(){return xr.setOf(t,i)}}}function Lt(e,t,r,n,o){return Mt(n,o).fold(function(){return function(n,e,t){return Vt(n,function(){return'The chosen schema: "'+t+'" did not exist in branches: '+Bt(e)})}(e,n,o)},function(n){return n.extract(e.concat(["branch: "+o]),t,r)})}function Gt(n,o){return{extract:function(e,t,r){return Mt(r,n).fold(function(){return function(n,e){return Vt(n,function(){return'Choice schema did not contain choice key: "'+e+'"'})}(e,n)},function(n){return Lt(e,t,r,o,n)})},toString:function(){return"chooseOn("+n+"). Possible values: "+Bn(o)},toDsl:function(){return xr.choiceOf(n,o)}}}function Ut(e){return Pt(function(n){return e(n).fold(mr,lr)})}function $t(e,n){return zt(function(n){return fr(e(n))},n)}function Wt(n,e,t){return sr(function(n,e,t,r){var o=e.extract([n],t,r);return vr(o,function(n){return{input:r,errors:n}})}(n,e,y,t))}function Xt(n){return n.fold(function(n){throw new Error(Dr(n))},y)}function qt(n,e,t){return Xt(Wt(n,e,t))}function Yt(n,e){return Gt(n,S(e,Or))}function Kt(n){return Er(n,n,Tt(),Tr())}function Jt(n,e){return Er(n,n,Tt(),e)}function Qt(n,e){return Er(n,n,Tt(),Or(e))}function Zt(n){return Er(n,n,kt(),Tr())}function nr(n,e){return Er(n,n,kt(),e)}function er(n,e){return nr(n,Or(e))}function tr(n,e){return nr(n,Ht(e))}function rr(n,e){return Er(n,n,xn(e),Tr())}function or(n,e,t){return Er(n,n,xn(e),t)}function ir(n,e){return kr(n,e)}function ur(n,e){return cn(n.element(),e.event().target())}var cr,ar,fr=function(n){return n.fold(At,Rt)},sr=function(n){return It(n,yt.error,yt.value)},lr=Rt,dr=function(n){var e=[],t=[];return C(n,function(n){It(n,function(n){return t.push(n)},function(n){return e.push(n)})}),{values:e,errors:t}},mr=At,gr=function(n,e){return n.stype===In.Value?e(n.svalue):n},pr=function(n,e){return n.stype===In.Error?e(n.serror):n},hr=function(n,e){return n.stype===In.Value?{stype:In.Value,svalue:e(n.svalue)}:n},vr=function(n,e){return n.stype===In.Error?{stype:In.Error,serror:e(n.serror)}:n},yr=function(n,e){var t=dr(n);return 0<t.errors.length?Ft(t.errors):function(n,e){return 0<n.length?lr(wt(e,St.apply(undefined,n))):lr(e)}(t.values,e)},br=function(n){var e=dr(n);return 0<e.errors.length?Ft(e.errors):lr(e.values)},xr=bt([{setOf:["validator","valueType"]},{arrOf:["valueType"]},{objOf:["fields"]},{itemOf:["validator"]},{choiceOf:["key","branches"]},{thunk:["description"]},{func:["args","outputSchema"]}]),wr=bt([{field:["name","presence","type"]},{state:["name"]}]),Sr=bt([{field:["key","okey","presence","prop"]},{state:["okey","instantiator"]}]),Or=function(r){return{extract:function(n,e,t){return function(e,t,n,r){var o=me(n,function(n){return _t(e,t,n,r)});return yr(o,{})}(n,t,r,e)},toString:function(){return"obj{\n"+me(r,function(n){return n.fold(function(n,e,t,r){return n+" -> "+r.toString()},function(n,e){return"state("+n+")"})}).join("\n")+"}"},toDsl:function(){return xr.objOf(me(r,function(n){return n.fold(function(n,e,t,r){return wr.field(n,t,r)},function(n,e){return wr.state(n)})}))}}},Tr=b(Pt(lr)),kr=Sr.state,Er=Sr.field,Cr=Pt(lr),Dr=function(n){return"Errors: \n"+function(n){var e=10<n.length?n.slice(0,10).concat([{path:[],getErrorInfo:function(){return"... (only showing first ten failures)"}}]):n;return me(e,function(n){return"Failed path: ("+n.path.join(" > ")+")\n"+n.getErrorInfo()})}(n.errors)+"\n\nInput object: "+Bt(n.input)},Mr=b(Cr),Ir=(cr=ae,ar="function",Pt(function(n){var e=typeof n;return cr(n)?lr(n):mr("Expected type: "+ar+" but got: "+e)}));function Rr(n,e,t,r,o){return n(t,r)?Fn.some(t):ae(o)&&o(t)?Fn.none():e(t,r,o)}function Ar(n,e,t){for(var r=n.dom(),o=ae(t)?t:b(!1);r.parentNode;){r=r.parentNode;var i=Qe.fromDom(r);if(e(i))return Fn.some(i);if(o(i))break}return Fn.none()}function Fr(n,e,t){return Rr(function(n,e){return e(n)},Ar,n,e,t)}function Br(n,o){var i=function(n){for(var e=0;e<n.childNodes.length;e++){var t=Qe.fromDom(n.childNodes[e]);if(o(t))return Fn.some(t);var r=i(n.childNodes[e]);if(r.isSome())return r}return Fn.none()};return i(n.dom())}function Vr(n){if(!Mn(n,"can")&&!Mn(n,"abort")&&!Mn(n,"run"))throw new Error("EventHandler defined by: "+JSON.stringify(n,null,2)+" does not have can, abort, or run!");return qt("Extracting event.handler",Ht([rr("can",b(!0)),rr("abort",b(!1)),rr("run",w)]),n)}function Nr(t){var n=function(e,r){return function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return I(e,function(n,e){return n&&r(e).apply(undefined,t)},!0)}}(t,function(n){return n.can}),e=function(e,r){return function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return I(e,function(n,e){return n||r(e).apply(undefined,t)},!1)}}(t,function(n){return n.abort});return Vr({can:n,abort:e,run:function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];C(t,function(n){n.run.apply(undefined,e)})}})}function jr(n){return Cn(n)}function _r(n,e){return{key:n,value:Vr({abort:e})}}function Pr(n,e){return{key:n,value:Vr({run:e})}}function Hr(n,t,r){return{key:n,value:Vr({run:function(n,e){t.apply(undefined,[n,e].concat(r))}})}}function zr(n){return function(t){return{key:n,value:Vr({run:function(n,e){ur(n,e)&&t(n,e)}})}}}function Lr(n,e,t){return function(t,r){return Pr(t,function(n,e){n.getSystem().getByUid(r).each(function(n){!function(n,e,t,r){n.getSystem().triggerEvent(t,e,r.event())}(n,n.element(),t,e)})})}(n,e.partUids[t])}function Gr(n){return Pr(n,function(n,e){e.cut()})}function Ur(n,e){var t=n.toString(),r=t.indexOf(")")+1,o=t.indexOf("("),i=t.substring(o+1,r-1).split(/,\s*/);return n.toFunctionAnnotation=function(){return{name:e,parameters:Ro(i)}},n}function $r(n){return{classes:n.classes!==undefined?n.classes:[],attributes:n.attributes!==undefined?n.attributes:{},styles:n.styles!==undefined?n.styles:{}}}function Wr(t,r,o){return Mo(function(n,e){o(n,t,r)})}function Xr(o,i,u){return function(n,e,t){var r=t.toString(),o=r.indexOf(")")+1,i=r.indexOf("("),u=r.substring(i+1,o-1).split(/,\s*/);return n.toFunctionAnnotation=function(){return{name:e,parameters:Ro(u.slice(0,1).concat(u.slice(3)))}},n}(function(t){for(var n=[],e=1;e<arguments.length;e++)n[e-1]=arguments[e];var r=[t].concat(n);return t.config({name:b(o)}).fold(function(){throw new Error("We could not find any behaviour configuration for: "+o+". Using API: "+u)},function(n){var e=Array.prototype.slice.call(r,1);return i.apply(undefined,[t,n.config,n.state].concat(e))})},u,i)}function qr(n){return{key:n,value:undefined}}function Yr(n){var e=qt("Creating behaviour: "+n.name,No,n);return function(n,e,t,r,o,i){var u=Ht(n),c=er(e,[tr("config",n)]);return Ao(u,c,e,t,r,o,i)}(e.fields,e.name,e.active,e.apis,e.extra,e.state)}function Kr(n,e,t){if(!(oe(t)||ce(t)||fe(t)))throw l.console.error("Invalid call to Attr.set. Key ",e,":: Value ",t,":: Element ",n),new Error("Attribute value was not simple");n.setAttribute(e,t+"")}function Jr(n,e,t){Kr(n.dom(),e,t)}function Qr(n,e){var t=n.dom();Nn(e,function(n,e){Kr(t,e,n)})}function Zr(n,e){var t=n.dom().getAttribute(e);return null===t?undefined:t}function no(n,e){var t=n.dom();return!(!t||!t.hasAttribute)&&t.hasAttribute(e)}function eo(n,e){n.dom().removeAttribute(e)}function to(n,e){var t=Zr(n,e);return t===undefined||""===t?[]:t.split(" ")}function ro(n){return n.dom().classList!==undefined}function oo(n,e){return function(n,e,t){var r=to(n,e).concat([t]);return Jr(n,e,r.join(" ")),!0}(n,"class",e)}function io(n,e){return function(n,e,t){var r=D(to(n,e),function(n){return n!==t});return 0<r.length?Jr(n,e,r.join(" ")):eo(n,e),!1}(n,"class",e)}function uo(n,e){ro(n)?n.dom().classList.add(e):oo(n,e)}function co(n){0===(ro(n)?n.dom().classList:function(n){return to(n,"class")}(n)).length&&eo(n,"class")}function ao(n,e){ro(n)?n.dom().classList.remove(e):io(n,e),co(n)}function fo(n,e){return ro(n)&&n.dom().classList.contains(e)}function so(n,e,t){ao(n,t),uo(n,e)}function lo(n){n.dom().focus()}function mo(n){n.dom().blur()}function go(n){var e=n!==undefined?n.dom():l.document;return Fn.from(e.activeElement).map(Qe.fromDom)}function po(e){return go(an(e)).filter(function(n){return e.dom().contains(n.dom())})}function ho(n){return n.dom().innerHTML}function vo(n,e){var t=an(n).dom(),r=Qe.fromDom(t.createDocumentFragment()),o=function(n,e){var t=(e||l.document).createElement("div");return t.innerHTML=n,ft(Qe.fromDom(t))}(e,t);gn(r,o),pn(n),st(n,r)}function yo(n){return function(n,e){return Qe.fromDom(n.dom().cloneNode(e))}(n,!1)}function bo(n){return function(n){var e=Qe.fromTag("div"),t=Qe.fromDom(n.dom().cloneNode(!0));return st(e,t),ho(e)}(yo(n))}function xo(n){return bo(n)}function wo(n){for(var e=[],t=function(n){e.push(n)},r=0;r<n.length;r++)n[r].each(t);return e}function So(n,e){for(var t=0;t<n.length;t++){var r=e(n[t],t);if(r.isSome())return r}return Fn.none()}var Oo,To,ko,Eo=function(n,e,t){return Fr(n,function(n){return e(n).isSome()},t).bind(e)},Co=zr($e()),Do=zr(We()),Mo=zr(Ue()),Io=(Oo=He(),function(n){return Pr(Oo,n)}),Ro=function(n){return me(n,function(n){return function(n,e){return function(n,e,t){return""===e||!(n.length<e.length)&&n.substr(t,t+e.length)===e}(n,e,n.length-e.length)}(n,"/*")?n.substring(0,n.length-"/*".length):n})},Ao=function(t,n,r,o,e,i,u){function c(n){return Mn(n,r)?n[r]():Fn.none()}var a=S(e,function(n,e){return Xr(r,n,e)}),f=S(i,function(n,e){return Ur(n,e)}),s=x(x(x({},f),a),{revoke:d(qr,r),config:function(n){var e=qt(r+"-config",t,n);return{key:r,value:{config:e,me:s,configAsRaw:X(function(){return qt(r+"-config",t,n)}),initialConfig:n,state:u}}},schema:function(){return n},exhibit:function(n,t){return c(n).bind(function(e){return Mt(o,"exhibit").map(function(n){return n(t,e.config,e.state)})}).getOr($r({}))},name:function(){return r},handlers:function(n){return c(n).map(function(n){return kn("events",function(n,e){return{}})(o)(n.config,n.state)}).getOr({})}});return s},Fo={init:function(){return Bo({readState:function(){return"No State required"}})}},Bo=function(n){return n},Vo=function(n){return Cn(n)},No=Ht([Kt("fields"),Kt("name"),rr("active",{}),rr("apis",{}),rr("state",Fo),rr("extra",{})]),jo=Ht([Kt("branchKey"),Kt("branches"),Kt("name"),rr("active",{}),rr("apis",{}),rr("state",Fo),rr("extra",{})]),_o=b(undefined),Po=/* */Object.freeze({toAlpha:function(n,e,t){so(n.element(),e.alpha,e.omega)},toOmega:function(n,e,t){so(n.element(),e.omega,e.alpha)},isAlpha:function(n,e,t){return fo(n.element(),e.alpha)},isOmega:function(n,e,t){return fo(n.element(),e.omega)},clear:function(n,e,t){ao(n.element(),e.alpha),ao(n.element(),e.omega)}}),Ho=[Kt("alpha"),Kt("omega")],zo=Yr({fields:Ho,name:"swapping",apis:Po}),Lo=tinymce.util.Tools.resolve("tinymce.dom.DOMUtils"),Go=tinymce.util.Tools.resolve("tinymce.ThemeManager"),Uo=function(n){var e=l.document.createElement("a");e.target="_blank",e.href=n.href,e.rel="noreferrer noopener";var t=l.document.createEvent("MouseEvents");t.initMouseEvent("click",!0,!0,l.window,0,0,0,0,0,!1,!1,!1,!1,0,null),l.document.body.appendChild(e),e.dispatchEvent(t),l.document.body.removeChild(e)},$o={formatChanged:b("formatChanged"),orientationChanged:b("orientationChanged"),dropupDismissed:b("dropupDismissed")},Wo=/* */Object.freeze({events:function(e){return jr([Pr(Pe(),function(o,i){var u=e.channels,n=function(n,e){return e.universal()?n:D(n,function(n){return k(e.channels(),n)})}(Bn(u),i);C(n,function(n){var e=u[n],t=e.schema,r=qt("channel["+n+"] data\nReceiver: "+xo(o.element()),t,i.data());e.onReceive(o,r)})})])}}),Xo="unknown";(ko=To=To||{})[ko.STOP=0]="STOP",ko[ko.NORMAL=1]="NORMAL",ko[ko.LOGGING=2]="LOGGING";function qo(e,n,t){switch(Mt(bi.get(),e).orThunk(function(){var n=Bn(bi.get());return So(n,function(n){return-1<e.indexOf(n)?Fn.some(bi.get()[n]):Fn.none()})}).getOr(To.NORMAL)){case To.NORMAL:return t(wi());case To.LOGGING:var r=function(e,t){var r=[],o=(new Date).getTime();return{logEventCut:function(n,e,t){r.push({outcome:"cut",target:e,purpose:t})},logEventStopped:function(n,e,t){r.push({outcome:"stopped",target:e,purpose:t})},logNoParent:function(n,e,t){r.push({outcome:"no-parent",target:e,purpose:t})},logEventNoHandlers:function(n,e){r.push({outcome:"no-handlers-left",target:e})},logEventResponse:function(n,e,t){r.push({outcome:"response",purpose:t,target:e})},write:function(){var n=(new Date).getTime();k(["mousemove","mouseover","mouseout",Ue()],e)||l.console.log(e,{event:e,time:n-o,target:t.dom(),sequence:me(r,function(n){return k(["cut","stopped","response"],n.outcome)?"{"+n.purpose+"} "+n.outcome+" at ("+xo(n.target)+")":n.outcome})})}}}(e,n),o=t(r);return r.write(),o;case To.STOP:return!0}}function Yo(n,e,t){return qo(n,e,t)}function Ko(n,e,t){return function(){var n=new Error;if(n.stack===undefined)return;var e=n.stack.split("\n");R(e,function(e){return 0<e.indexOf("alloy")&&!E(xi,function(n){return-1<e.indexOf(n)})}).getOr(Xo)}(),Er(e,e,t,Ut(function(t){return yt.value(function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];return t.apply(undefined,n)})}))}function Jo(n){return Ko(0,n,xn(w))}function Qo(n){return Ko(0,n,xn(Fn.none))}function Zo(n){return Ko(0,n,Tt())}function ni(n){return Ko(0,n,Tt())}function ei(n,e){return ir(n,b(e))}function ti(n){return ir(n,y)}function ri(n,e,t){var r=e.aria;r.update(n,r,t.get())}function oi(e,n,t){n.toggleClass.each(function(n){t.get()?uo(e.element(),n):ao(e.element(),n)})}function ii(n,e,t){Mi(n,e,t,!t.get())}function ui(n,e,t){t.set(!0),oi(n,e,t),ri(n,e,t)}function ci(n,e,t){t.set(!1),oi(n,e,t),ri(n,e,t)}function ai(n,e,t){Mi(n,e,t,e.selected)}function fi(){function n(n,e){e.stop(),$(n)}return[Pr(Ae(),n),Pr(Le(),n),Gr(we()),Gr(Te())]}function si(n,e){e.ignore||(lo(n.element()),e.onFocus(n))}function li(n){return n.style!==undefined&&ae(n.style.getPropertyValue)}function di(n,e,t){if(!oe(t))throw l.console.error("Invalid call to CSS.set. Property ",e,":: Value ",t,":: Element ",n),new Error("CSS value must be a string: "+t);li(n)&&n.style.setProperty(e,t)}function mi(n,e){var t=n.dom();Nn(e,function(n,e){di(t,e,n)})}function gi(n,e){var t=n.dom(),r=l.window.getComputedStyle(t).getPropertyValue(e),o=""!==r||K(n)?r:$i(t,e);return null===o?undefined:o}function pi(n,e){var t=n.dom(),r=$i(t,e);return Fn.from(r).filter(function(n){return 0<n.length})}function hi(n,e){!function(n,e){li(n)&&n.style.removeProperty(e)}(n.dom(),e),no(n,"style")&&""===function(n){return n.replace(/^\s+|\s+$/g,"")}(Zr(n,"style"))&&eo(n,"style")}function vi(n){return n.dom().offsetWidth}var yi,bi=Pn({}),xi=["alloy/data/Fields","alloy/debugging/Debugging"],wi=b({logEventCut:w,logEventStopped:w,logNoParent:w,logEventNoHandlers:w,logEventResponse:w,write:w}),Si=b([Kt("menu"),Kt("selectedMenu")]),Oi=b([Kt("item"),Kt("selectedItem")]),Ti=(b(Or(Oi().concat(Si()))),b(Or(Oi()))),ki=Qt("initSize",[Kt("numColumns"),Kt("numRows")]),Ei=b(ki),Ci=[Jt("channels",$t(yt.value,Ht([Zo("onReceive"),rr("schema",Mr())])))],Di=Yr({fields:Ci,name:"receiving",active:Wo}),Mi=function(n,e,t,r){(r?ui:ci)(n,e,t)},Ii=/* */Object.freeze({onLoad:ai,toggle:ii,isOn:function(n,e,t){return t.get()},on:ui,off:ci,set:Mi}),Ri=/* */Object.freeze({exhibit:function(n,e,t){return $r({})},events:function(n,e){var t=function(e,t,r){return Io(function(n){r(n,e,t)})}(n,e,ii),r=Wr(n,e,ai);return jr(F([n.toggleOnExecute?[t]:[],[r]]))}}),Ai=function(n,e,t){Jr(n.element(),"aria-expanded",t)},Fi=[rr("selected",!1),Zt("toggleClass"),rr("toggleOnExecute",!0),or("aria",{mode:"none"},Yt("mode",{pressed:[rr("syncWithExpanded",!1),ei("update",function(n,e,t){Jr(n.element(),"aria-pressed",t),e.syncWithExpanded&&Ai(n,e,t)})],checked:[ei("update",function(n,e,t){Jr(n.element(),"aria-checked",t)})],expanded:[ei("update",Ai)],selected:[ei("update",function(n,e,t){Jr(n.element(),"aria-selected",t)})],none:[ei("update",w)]}))],Bi=Yr({fields:Fi,name:"toggling",active:Ri,apis:Ii,state:(yi=!1,{init:function(){var e=Pn(yi);return{get:function(){return e.get()},set:function(n){return e.set(n)},clear:function(){return e.set(yi)},readState:function(){return e.get()}}}})}),Vi=function(t,r){return Di.config({channels:En($o.formatChanged(),{onReceive:function(n,e){e.command===t&&r(n,e.state)}})})},Ni=function(n){return Di.config({channels:En($o.orientationChanged(),{onReceive:n})})},ji=function(n,e){return{key:n,value:{onReceive:e}}},_i="tinymce-mobile",Pi={resolve:function(n){return _i+"-"+n},prefix:b(_i)},Hi=/* */Object.freeze({focus:si,blur:function(n,e){e.ignore||mo(n.element())},isFocused:function(n){return function(n){var e=an(n).dom();return n.dom()===e.activeElement}(n.element())}}),zi=/* */Object.freeze({exhibit:function(n,e){var t=e.ignore?{}:{attributes:{tabindex:"-1"}};return $r(t)},events:function(t){return jr([Pr(Ne(),function(n,e){si(n,t),e.stop()})].concat(t.stopMousedown?[Pr(Te(),function(n,e){e.event().prevent()})]:[]))}}),Li=[Jo("onFocus"),rr("stopMousedown",!1),rr("ignore",!1)],Gi=Yr({fields:Li,name:"focusing",active:zi,apis:Hi}),Ui=function(n,e,t){var r=n.dom();di(r,e,t)},$i=function(n,e){return li(n)?n.style.getPropertyValue(e):""};function Wi(r,o){function n(n){var e=o(n);if(e<=0||null===e){var t=gi(n,r);return parseFloat(t)||0}return e}function i(o,n){return I(n,function(n,e){var t=gi(o,e),r=t===undefined?0:parseInt(t,10);return isNaN(r)?n:n+r},0)}return{set:function(n,e){if(!fe(e)&&!e.match(/^[0-9]+$/))throw new Error(r+".set accepts only positive integer values. Value was "+e);var t=n.dom();li(t)&&(t.style[r]=e+"px")},get:n,getOuter:n,aggregate:i,max:function(n,e,t){var r=i(n,t);return r<e?e-r:0}}}function Xi(n){return bu.get(n)}function qi(n,e,t){return D(function(n,e){for(var t=ae(e)?e:c,r=n.dom(),o=[];null!==r.parentNode&&r.parentNode!==undefined;){var i=r.parentNode,u=Qe.fromDom(i);if(o.push(u),!0===t(u))break;r=i}return o}(n,t),e)}function Yi(n,e){return D(function(e){return fn(e).map(ft).map(function(n){return D(n,function(n){return!cn(e,n)})}).getOr([])}(n),e)}function Ki(n,e){return on(e,n)}function Ji(n){return un(n)}function Qi(n,e,t){return Ar(n,function(n){return tn(n,e)},t)}function Zi(n,e){return un(e,n)}function nu(n,e,t){return Rr(tn,Qi,n,e,t)}function eu(n,e,t){var r=N(n.slice(0,e)),o=N(n.slice(e+1));return R(r.concat(o),t)}function tu(n,e,t){var r=N(n.slice(0,e));return R(r,t)}function ru(n,e,t){var r=n.slice(0,e),o=n.slice(e+1);return R(o.concat(r),t)}function ou(n,e,t){var r=n.slice(e+1);return R(r,t)}function iu(t){return function(n){var e=n.raw();return k(t,e.which)}}function uu(n){return function(e){return V(n,function(n){return n(e)})}}function cu(n){return!0===n.raw().shiftKey}function au(n){return!0===n.raw().ctrlKey}function fu(n,e){return{matches:n,classification:e}}function su(n,e,t,r){var o=n+e;return r<o?t:o<t?r:o}function lu(n,e,t){return n<=e?e:t<=n?t:n}function du(t,r,n,o){var e=Ki(t.element(),"."+r.highlightClass);C(e,function(e){E(o,function(n){return n.element()===e})||(ao(e,r.highlightClass),t.getSystem().getByDom(e).each(function(n){r.onDehighlight(t,n),G(n,Ye())}))})}function mu(n,e,t,r){du(n,e,0,[r]),wu(n,e,t,r)||(uo(r.element(),e.highlightClass),e.onHighlight(n,r),G(r,qe()))}function gu(t,e,n,r){var o=Ki(t.element(),"."+e.itemClass);return A(o,function(n){return fo(n,e.highlightClass)}).bind(function(n){var e=su(n,r,0,o.length-1);return t.getSystem().getByDom(o[e]).toOption()})}function pu(n,e,t){e.exists(function(e){return t.exists(function(n){return cn(n,e)})})||U(n,Xe(),{prevFocus:e,newFocus:t})}function hu(){function o(n){return po(n.element())}return{get:o,set:function(n,e){var t=o(n);n.getSystem().triggerFocus(e,n.element());var r=o(n);pu(n,t,r)}}}var vu,yu,bu=Wi("height",function(n){var e=n.dom();return K(n)?e.getBoundingClientRect().height:e.offsetHeight}),xu=m(cu),wu=function(n,e,t,r){return fo(r.element(),e.highlightClass)},Su=function(n,e,t,r){var o=Ki(n.element(),"."+e.itemClass);return Fn.from(o[r]).fold(function(){return yt.error("No element found with index "+r)},n.getSystem().getByDom)},Ou=function(e,n,t){return Zi(e.element(),"."+n.itemClass).bind(function(n){return e.getSystem().getByDom(n).toOption()})},Tu=function(e,n,t){var r=Ki(e.element(),"."+n.itemClass);return(0<r.length?Fn.some(r[r.length-1]):Fn.none()).bind(function(n){return e.getSystem().getByDom(n).toOption()})},ku=function(e,n,t){var r=Ki(e.element(),"."+n.itemClass);return wo(me(r,function(n){return e.getSystem().getByDom(n).toOption()}))},Eu=/* */Object.freeze({dehighlightAll:function(n,e,t){return du(n,e,0,[])},dehighlight:function(n,e,t,r){wu(n,e,t,r)&&(ao(r.element(),e.highlightClass),e.onDehighlight(n,r),G(r,Ye()))},highlight:mu,highlightFirst:function(e,t,r){Ou(e,t).each(function(n){mu(e,t,r,n)})},highlightLast:function(e,t,r){Tu(e,t).each(function(n){mu(e,t,r,n)})},highlightAt:function(e,t,r,n){Su(e,t,r,n).fold(function(n){throw new Error(n)},function(n){mu(e,t,r,n)})},highlightBy:function(e,t,r,n){var o=ku(e,t);R(o,n).each(function(n){mu(e,t,r,n)})},isHighlighted:wu,getHighlighted:function(e,n,t){return Zi(e.element(),"."+n.highlightClass).bind(function(n){return e.getSystem().getByDom(n).toOption()})},getFirst:Ou,getLast:Tu,getPrevious:function(n,e,t){return gu(n,e,0,-1)},getNext:function(n,e,t){return gu(n,e,0,1)},getCandidates:ku}),Cu=[Kt("highlightClass"),Kt("itemClass"),Jo("onHighlight"),Jo("onDehighlight")],Du=Yr({fields:Cu,name:"highlighting",apis:Eu});(yu=vu=vu||{}).OnFocusMode="onFocus",yu.OnEnterOrSpaceMode="onEnterOrSpace",yu.OnApiMode="onApi";function Mu(n,e,t,i,u){function c(e,t,n,r,o){return function(n,e){return R(n,function(n){return n.matches(e)}).map(function(n){return n.classification})}(n(e,t,r,o),t.event()).bind(function(n){return n(e,t,r,o)})}var r={schema:function(){return n.concat([rr("focusManager",hu()),or("focusInside","onFocus",Ut(function(n){return k(["onFocus","onEnterOrSpace","onApi"],n)?yt.value(n):yt.error("Invalid value for focusInside")})),ei("handler",r),ei("state",e),ei("sendFocusIn",u)])},processKey:c,toEvents:function(r,o){var n=r.focusInside!==vu.OnFocusMode?Fn.none():u(r).map(function(t){return Pr(Ne(),function(n,e){t(n,r,o),e.stop()})});return jr(n.toArray().concat([Pr(De(),function(n,e){c(n,e,t,r,o).fold(function(){!function(e,t){var n=iu([32].concat([13]))(t.event());r.focusInside===vu.OnEnterOrSpaceMode&&n&&ur(e,t)&&u(r).each(function(n){n(e,r,o),t.stop()})}(n,e)},function(n){e.stop()})}),Pr(Me(),function(n,e){c(n,e,i,r,o).each(function(n){e.stop()})})]))}};return r}function Iu(n){function i(n,e){var t=n.visibilitySelector.bind(function(n){return nu(e,n)}).getOr(e);return 0<Xi(t)}function e(e,t){(function(n,e){var t=Ki(n.element(),e.selector),r=D(t,function(n){return i(e,n)});return Fn.from(r[e.firstTabstop])})(e,t).each(function(n){t.focusManager.set(e,n)})}function u(e,n,t,r,o){return o(n,t,function(n){return function(n,e){return i(n,e)&&n.useTabstopAt(e)}(r,n)}).fold(function(){return r.cyclic?Fn.some(!0):Fn.none()},function(n){return r.focusManager.set(e,n),Fn.some(!0)})}function c(e,n,t,r){var o=Ki(e.element(),t.selector);return function(n,e){return e.focusManager.get(n).bind(function(n){return nu(n,e.selector)})}(e,t).bind(function(n){return A(o,d(cn,n)).bind(function(n){return u(e,o,n,t,r)})})}var t=[Zt("onEscape"),Zt("onEnter"),rr("selector",'[data-alloy-tabstop="true"]:not(:disabled)'),rr("firstTabstop",0),rr("useTabstopAt",b(!0)),Zt("visibilitySelector")].concat([n]),r=b([fu(uu([cu,iu([9])]),function(n,e,t,r){var o=t.cyclic?eu:tu;return c(n,0,t,o)}),fu(iu([9]),function(n,e,t,r){var o=t.cyclic?ru:ou;return c(n,0,t,o)}),fu(iu([27]),function(e,t,n,r){return n.onEscape.bind(function(n){return n(e,t)})}),fu(uu([xu,iu([13])]),function(e,t,n,r){return n.onEnter.bind(function(n){return n(e,t)})})]),o=b([]);return Mu(t,Fo.init,r,o,function(){return Fn.some(e)})}function Ru(n){return"input"===q(n)&&"radio"!==Zr(n,"type")||"textarea"===q(n)}function Au(n,e,t){return Ru(t)&&iu([32])(e.event())?Fn.none():function(n,e,t){return W(n,t,He()),Fn.some(!0)}(n,0,t)}function Fu(n,e){return Fn.some(!0)}function Bu(n,e,t){return t.execute(n,e,n.element())}function Vu(n){var t=Pn(Fn.none());return Bo({readState:function(){return t.get().map(function(n){return{numRows:n.numRows(),numColumns:n.numColumns()}}).getOr({numRows:"?",numColumns:"?"})},setGridSize:function(n,e){t.set(Fn.some({numRows:b(n),numColumns:b(e)}))},getNumRows:function(){return t.get().map(function(n){return n.numRows()})},getNumColumns:function(){return t.get().map(function(n){return n.numColumns()})}})}function Nu(e,t){return function(n){return"rtl"===Kc(n)?t:e}}function ju(i){return function(n,e,t,r){var o=i(n.element());return Jc(o,n,e,t,r)}}function _u(n,e){var t=Nu(n,e);return ju(t)}function Pu(n,e){var t=Nu(e,n);return ju(t)}function Hu(o){return function(n,e,t,r){return Jc(o,n,e,t,r)}}function zu(n){return!function(n){return n.offsetWidth<=0&&n.offsetHeight<=0}(n.dom())}function Lu(n,e,t){var r=d(cn,e),o=Ki(n,t);return function(e,n){return A(e,n).map(function(n){return ea({index:n,candidates:e})})}(D(o,zu),r)}function Gu(n,e){return A(n,function(n){return cn(e,n)})}function Uu(t,n,r,e){return e(Math.floor(n/r),n%r).bind(function(n){var e=n.row()*r+n.column();return 0<=e&&e<t.length?Fn.some(t[e]):Fn.none()})}function $u(o,n,i,u,c){return Uu(o,n,u,function(n,e){var t=n===i-1?o.length-n*u:u,r=su(e,c,0,t-1);return Fn.some({row:b(n),column:b(r)})})}function Wu(i,n,u,c,a){return Uu(i,n,c,function(n,e){var t=su(n,a,0,u-1),r=t===u-1?i.length-t*c:c,o=lu(e,0,r-1);return Fn.some({row:b(t),column:b(o)})})}function Xu(e,t,n){Zi(e.element(),t.selector).each(function(n){t.focusManager.set(e,n)})}function qu(o){return function(n,e,t,r){return Lu(n,e,t.selector).bind(function(n){return o(n.candidates(),n.index(),r.getNumRows().getOr(t.initSize.numRows),r.getNumColumns().getOr(t.initSize.numColumns))})}}function Yu(n,e,t,r){return t.captureTab?Fn.some(!0):Fn.none()}function Ku(n,e,t,o){var i=function(n,e,t){var r=su(e,o,0,t.length-1);return r===n?Fn.none():function(n){return"button"===q(n)&&"disabled"===Zr(n,"disabled")}(t[r])?i(n,r,t):Fn.from(t[r])};return Lu(n,t,e).bind(function(n){var e=n.index(),t=n.candidates();return i(e,e,t)})}function Ju(e,t,r){return function(n,e){return e.focusManager.get(n).bind(function(n){return nu(n,e.selector)})}(e,r).bind(function(n){return r.execute(e,t,n)})}function Qu(e,t){t.getInitial(e).orThunk(function(){return Zi(e.element(),t.selector)}).each(function(n){t.focusManager.set(e,n)})}function Zu(n,e,t){return Ku(n,t.selector,e,-1)}function nc(n,e,t){return Ku(n,t.selector,e,1)}function ec(r){return function(n,e,t){return r(n,e,t).bind(function(){return t.executeOnMove?Ju(n,e,t):Fn.some(!0)})}}function tc(n,e,t,r){return t.onEscape(n,e)}function rc(n,e,t){return Fn.from(n[e]).bind(function(n){return Fn.from(n[t]).map(function(n){return ma({rowIndex:e,columnIndex:t,cell:n})})})}function oc(n,e,t,r){var o=n[e].length,i=su(t,r,0,o-1);return rc(n,e,i)}function ic(n,e,t,r){var o=su(t,r,0,n.length-1),i=n[o].length,u=lu(e,0,i-1);return rc(n,o,u)}function uc(n,e,t,r){var o=n[e].length,i=lu(t+r,0,o-1);return rc(n,e,i)}function cc(n,e,t,r){var o=lu(t+r,0,n.length-1),i=n[o].length,u=lu(e,0,i-1);return rc(n,o,u)}function ac(e,t){t.previousSelector(e).orThunk(function(){var n=t.selectors;return Zi(e.element(),n.cell)}).each(function(n){t.focusManager.set(e,n)})}function fc(n,e){return function(o,t,i){var u=i.cycles?n:e;return nu(t,i.selectors.row).bind(function(n){var e=Ki(n,i.selectors.cell);return Gu(e,t).bind(function(t){var r=Ki(o,i.selectors.row);return Gu(r,n).bind(function(n){var e=function(n,e){return me(n,function(n){return Ki(n,e.selectors.cell)})}(r,i);return u(e,n,t).map(function(n){return n.cell()})})})})}}function sc(e,t,r){return r.focusManager.get(e).bind(function(n){return r.execute(e,t,n)})}function lc(e,t){Zi(e.element(),t.selector).each(function(n){t.focusManager.set(e,n)})}function dc(n,e,t){return Ku(n,t.selector,e,-1)}function mc(n,e,t){return Ku(n,t.selector,e,1)}function gc(e,n){return function(n,e,t){return or(n,e,Or(t))}(e,{},me(n,function(n){return function(e,t){return Er(e,e,kt(),Pt(function(n){return mr("The field: "+e+" is forbidden. "+t)}))}(n.name(),"Cannot configure "+n.name()+" for "+e)}).concat([ir("dump",y)]))}function pc(n){return n.dump}function hc(n,e){return x(x({},n.dump),Vo(e))}function vc(n,e,t,r){return t.uiType===Pa?function(n,e,t,r){return n.exists(function(n){return n!==t.owner})?Ha.single(!0,b(t)):Mt(r,t.name).fold(function(){throw new Error("Unknown placeholder component: "+t.name+"\nKnown: ["+Bn(r)+"]\nNamespace: "+n.getOr("none")+"\nSpec: "+JSON.stringify(t,null,2))},function(n){return n.replace()})}(n,0,t,r):Ha.single(!1,b(t))}function yc(e,t,n,r){var o=S(r,function(n,e){return function(n,e){var t=!1;return{name:b(n),required:function(){return e.fold(function(n,e){return n},function(n,e){return n})},used:function(){return t},replace:function(){if(!0===t)throw new Error("Trying to use the same placeholder more than once: "+n);return t=!0,e}}}(e,n)}),i=function(e,t,n,r){return B(n,function(n){return za(e,t,n,r)})}(e,t,n,o);return Nn(o,function(n){if(!1===n.used()&&n.required())throw new Error("Placeholder: "+n.name()+" was not found in components list\nNamespace: "+e.getOr("none")+"\nComponents: "+JSON.stringify(t.components,null,2))}),i}function bc(n){var e=(new Date).getTime();return n+"_"+Math.floor(1e9*Math.random())+ ++$a+String(e)}function xc(n){function e(n){return n.name}return n.fold(e,e,e,e)}function wc(t,r){return function(n){var e=qt("Converting part type",r,n);return t(e)}}function Sc(n,e,t,r){return wt(e.defaults(n,t,r),t,{uid:n.partUids[e.name]},e.overrides(n,t,r))}function Oc(o,n){var e={};return C(n,function(n){(function(n){return n.fold(Fn.some,Fn.none,Fn.some,Fn.some)})(n).each(function(t){var r=af(o,t.pname);e[t.name]=function(n){var e=qt("Part: "+t.name+" in "+o,Or(t.schema),n);return x(x({},r),{config:n,validated:e})}})}),e}function Tc(n,e,t){return function(n,t,e){var i={},r={};return C(e,function(n){n.fold(function(r){i[r.pname]=La(!0,function(n,e,t){return r.factory.sketch(Sc(n,r,e,t))})},function(n){var e=t.parts[n.name];r[n.name]=b(n.factory.sketch(Sc(t,n,e[cf()]),e))},function(r){i[r.pname]=La(!1,function(n,e,t){return r.factory.sketch(Sc(n,r,e,t))})},function(o){i[o.pname]=Ga(!0,function(e,n,t){var r=e[o.name];return me(r,function(n){return o.factory.sketch(wt(o.defaults(e,n,t),n,o.overrides(e,n)))})})})}),{internals:b(i),externals:b(r)}}(0,e,t)}function kc(n,e,t){return yc(Fn.some(n),e,e.components,t)}function Ec(n,e,t){var r=e.partUids[t];return n.getSystem().getByUid(r).toOption()}function Cc(n,e,t){return Ec(n,e,t).getOrDie("Could not find part: "+t)}function Dc(e,n){var t=function(n){return me(n,xc)}(n);return Cn(me(t,function(n){return{key:n,value:e+"-"+n}}))}function Mc(e){return Er("partUids","partUids",Ct(function(n){return Dc(n.uid,e)}),Mr())}function Ic(n){return En(ff,n)}function Rc(r){return function(n,e){var t=e.toString(),r=t.indexOf(")")+1,o=t.indexOf("("),i=t.substring(o+1,r-1).split(/,\s*/);return n.toFunctionAnnotation=function(){return{name:"OVERRIDE",parameters:Ro(i.slice(1))}},n}(function(n){for(var e=[],t=1;t<arguments.length;t++)e[t-1]=arguments[t];return r.apply(undefined,[n.getApis()].concat([n].concat(e)))},r)}function Ac(n){return bc(n)}function Fc(n,e,t,r,o){var i=function(n,e){return(0<n.length?[Qt("parts",n)]:[]).concat([Kt("uid"),rr("dom",{}),rr("components",[]),ti("originalSpec"),rr("debug.sketcher",{})]).concat(e)}(r,o);return qt(n+" [SpecSchema]",Ht(i.concat(e)),t)}function Bc(n,e,t,r,o){var i=hf(o),u=function(n){return B(n,function(n){return n.fold(Fn.none,Fn.some,Fn.none,Fn.none).map(function(n){return Qt(n.name,n.schema.concat([ti(cf())]))}).toArray()})}(t),c=Mc(t),a=Fc(n,e,i,u,[c]),f=Tc(0,a,t);return r(a,kc(n,a,f.internals()),i,f.externals())}var Vc,Nc,jc,_c,Pc,Hc,zc,Lc,Gc,Uc,$c=Iu(ir("cyclic",b(!1))),Wc=Iu(ir("cyclic",b(!0))),Xc=[rr("execute",Au),rr("useSpace",!1),rr("useEnter",!0),rr("useControlEnter",!1),rr("useDown",!1)],qc=Mu(Xc,Fo.init,function(n,e,t,r){var o=t.useSpace&&!Ru(n.element())?[32]:[],i=t.useEnter?[13]:[],u=t.useDown?[40]:[],c=o.concat(i).concat(u);return[fu(iu(c),Bu)].concat(t.useControlEnter?[fu(uu([au,iu([13])]),Bu)]:[])},function(n,e,t,r){return t.useSpace&&!Ru(n.element())?[fu(iu([32]),Fu)]:[]},function(){return Fn.none()}),Yc=/* */Object.freeze({flatgrid:Vu,init:function(n){return n.state(n)}}),Kc=function(n){return"rtl"===gi(n,"direction")?"rtl":"ltr"},Jc=function(e,t,n,r,o){return r.focusManager.get(t).bind(function(n){return e(t.element(),n,r,o)}).map(function(n){return r.focusManager.set(t,n),!0})},Qc=Hu,Zc=Hu,na=Hu,ea=nn(["index","candidates"],[]),ta=[Kt("selector"),rr("execute",Au),Qo("onEscape"),rr("captureTab",!1),Ei()],ra=qu(function(n,e,t,r){return $u(n,e,t,r,-1)}),oa=qu(function(n,e,t,r){return $u(n,e,t,r,1)}),ia=qu(function(n,e,t,r){return Wu(n,e,t,r,-1)}),ua=qu(function(n,e,t,r){return Wu(n,e,t,r,1)}),ca=b([fu(iu([37]),_u(ra,oa)),fu(iu([39]),Pu(ra,oa)),fu(iu([38]),Qc(ia)),fu(iu([40]),Zc(ua)),fu(uu([cu,iu([9])]),Yu),fu(uu([xu,iu([9])]),Yu),fu(iu([27]),function(n,e,t,r){return t.onEscape(n,e)}),fu(iu([32].concat([13])),function(e,t,r,n){return function(n,e){return e.focusManager.get(n).bind(function(n){return nu(n,e.selector)})}(e,r).bind(function(n){return r.execute(e,t,n)})})]),aa=b([fu(iu([32]),Fu)]),fa=Mu(ta,Vu,ca,aa,function(){return Fn.some(Xu)}),sa=[Kt("selector"),rr("getInitial",Fn.none),rr("execute",Au),Qo("onEscape"),rr("executeOnMove",!1),rr("allowVertical",!0)],la=b([fu(iu([32]),Fu)]),da=Mu(sa,Fo.init,function(n,e,t,r){var o=[37].concat(t.allowVertical?[38]:[]),i=[39].concat(t.allowVertical?[40]:[]);return[fu(iu(o),ec(_u(Zu,nc))),fu(iu(i),ec(Pu(Zu,nc))),fu(iu([13]),Ju),fu(iu([32]),Ju),fu(iu([27]),tc)]},la,function(){return Fn.some(Qu)}),ma=nn(["rowIndex","columnIndex","cell"],[]),ga=[Qt("selectors",[Kt("row"),Kt("cell")]),rr("cycles",!0),rr("previousSelector",Fn.none),rr("execute",Au)],pa=fc(function(n,e,t){return oc(n,e,t,-1)},function(n,e,t){return uc(n,e,t,-1)}),ha=fc(function(n,e,t){return oc(n,e,t,1)},function(n,e,t){return uc(n,e,t,1)}),va=fc(function(n,e,t){return ic(n,t,e,-1)},function(n,e,t){return cc(n,t,e,-1)}),ya=fc(function(n,e,t){return ic(n,t,e,1)},function(n,e,t){return cc(n,t,e,1)}),ba=b([fu(iu([37]),_u(pa,ha)),fu(iu([39]),Pu(pa,ha)),fu(iu([38]),Qc(va)),fu(iu([40]),Zc(ya)),fu(iu([32].concat([13])),function(e,t,r){return po(e.element()).bind(function(n){return r.execute(e,t,n)})})]),xa=b([fu(iu([32]),Fu)]),wa=Mu(ga,Fo.init,ba,xa,function(){return Fn.some(ac)}),Sa=[Kt("selector"),rr("execute",Au),rr("moveOnTab",!1)],Oa=b([fu(iu([38]),na(dc)),fu(iu([40]),na(mc)),fu(uu([cu,iu([9])]),function(n,e,t){return t.moveOnTab?na(dc)(n,e,t):Fn.none()}),fu(uu([xu,iu([9])]),function(n,e,t){return t.moveOnTab?na(mc)(n,e,t):Fn.none()}),fu(iu([13]),sc),fu(iu([32]),sc)]),Ta=b([fu(iu([32]),Fu)]),ka=Mu(Sa,Fo.init,Oa,Ta,function(){return Fn.some(lc)}),Ea=[Qo("onSpace"),Qo("onEnter"),Qo("onShiftEnter"),Qo("onLeft"),Qo("onRight"),Qo("onTab"),Qo("onShiftTab"),Qo("onUp"),Qo("onDown"),Qo("onEscape"),rr("stopSpaceKeyup",!1),Zt("focusIn")],Ca=Mu(Ea,Fo.init,function(n,e,t){return[fu(iu([32]),t.onSpace),fu(uu([xu,iu([13])]),t.onEnter),fu(uu([cu,iu([13])]),t.onShiftEnter),fu(uu([cu,iu([9])]),t.onShiftTab),fu(uu([xu,iu([9])]),t.onTab),fu(iu([38]),t.onUp),fu(iu([40]),t.onDown),fu(iu([37]),t.onLeft),fu(iu([39]),t.onRight),fu(iu([32]),t.onSpace),fu(iu([27]),t.onEscape)]},function(n,e,t){return t.stopSpaceKeyup?[fu(iu([32]),Fu)]:[]},function(n){return n.focusIn}),Da=$c.schema(),Ma=Wc.schema(),Ia=da.schema(),Ra=fa.schema(),Aa=wa.schema(),Fa=qc.schema(),Ba=ka.schema(),Va=Ca.schema(),Na=(Uc=qt("Creating behaviour: "+(Gc={branchKey:"mode",branches:/* */Object.freeze({acyclic:Da,cyclic:Ma,flow:Ia,flatgrid:Ra,matrix:Aa,execution:Fa,menu:Ba,special:Va}),name:"keying",active:{events:function(n,e){return n.handler.toEvents(n,e)}},apis:{focusIn:function(e,t,r){t.sendFocusIn(t).fold(function(){e.getSystem().triggerFocus(e.element(),e.element())},function(n){n(e,t,r)})},setGridSize:function(n,e,t,r,o){Mn(t,"setGridSize")?t.setGridSize(r,o):l.console.error("Layout does not support setGridSize")}},state:Yc}).name,jo,Gc),Vc=Yt(Uc.branchKey,Uc.branches),Nc=Uc.name,jc=Uc.active,_c=Uc.apis,Pc=Uc.extra,Hc=Uc.state,Lc=er(Nc,[nr("config",zc=Vc)]),Ao(zc,Lc,Nc,jc,_c,Pc,Hc)),ja=gc,_a=hc,Pa="placeholder",Ha=bt([{single:["required","valueThunk"]},{multiple:["required","valueThunks"]}]),za=function(i,u,c,a){return vc(i,0,c,a).fold(function(n,e){var t=e(u,c.config,c.validated),r=Mt(t,"components").getOr([]),o=B(r,function(n){return za(i,u,n,a)});return[x(x({},t),{components:o})]},function(n,e){var t=e(u,c.config,c.validated);return c.validated.preprocess.getOr(y)(t)})},La=Ha.single,Ga=Ha.multiple,Ua=b(Pa),$a=0,Wa=bt([{required:["data"]},{external:["data"]},{optional:["data"]},{group:["data"]}]),Xa=rr("factory",{sketch:y}),qa=rr("schema",[]),Ya=Kt("name"),Ka=Er("pname","pname",Et(function(n){return"<alloy."+bc(n.name)+">"}),Mr()),Ja=ir("schema",function(){return[Zt("preprocess")]}),Qa=rr("defaults",b({})),Za=rr("overrides",b({})),nf=Or([Xa,qa,Ya,Ka,Qa,Za]),ef=Or([Xa,qa,Ya,Ka,Qa,Za]),tf=Or([Xa,Ja,Ya,Kt("unit"),Ka,Qa,Za]),rf=wc(Wa.required,nf),of=wc(Wa.optional,ef),uf=wc(Wa.group,tf),cf=b("entirety"),af=function(n,e){return{uiType:Ua(),owner:n,name:e}},ff=bc("alloy-premade"),sf=b("alloy-id-"),lf=b("data-alloy-id"),df=sf(),mf=lf(),gf=function(n,e){Object.defineProperty(n.dom(),mf,{value:e,writable:!0})},pf=function(n){var e=tt(n)?n.dom()[mf]:null;return Fn.from(e)},hf=function(n){return n.hasOwnProperty("uid")?n:x(x({},n),{uid:Ac("uid")})};function vf(n){var e=qt("Sketcher for "+n.name,zs,n),t=S(e.apis,Rc),r=S(e.extraApis,function(n,e){return Ur(n,e)});return x(x({name:b(e.name),partFields:b([]),configFields:b(e.configFields),sketch:function(n){return function(n,e,t,r){var o=hf(r);return t(Fc(n,e,o,[],[]),o)}(e.name,e.configFields,e.factory,n)}},t),r)}function yf(n){var e=qt("Sketcher for "+n.name,Ls,n),t=Oc(e.name,e.partFields),r=S(e.apis,Rc),o=S(e.extraApis,function(n,e){return Ur(n,e)});return x(x({name:b(e.name),partFields:b(e.partFields),configFields:b(e.configFields),sketch:function(n){return Bc(e.name,e.configFields,e.partFields,e.factory,n)},parts:b(t)},r),o)}function bf(n){var e=Qe.fromHtml(n),t=ft(e),r=function(n){var e=n.dom().attributes!==undefined?n.dom().attributes:[];return I(e,function(n,e){var t;return"class"===e.name?n:x(x({},n),((t={})[e.name]=e.value,t))},{})}(e),o=function(n){return Array.prototype.slice.call(n.dom().classList,0)}(e),i=0===t.length?{}:{innerHtml:ho(e)};return x({tag:q(e),classes:o,attributes:r},i)}function xf(n){return{dom:$s(n)}}function wf(n){return Vo([Bi.config({toggleClass:Pi.resolve("toolbar-button-selected"),toggleOnExecute:!1,aria:{mode:"pressed"}}),Vi(n,function(n,e){(e?Bi.on:Bi.off)(n)})])}function Sf(n,e){var t=e.ui.registry.getAll().icons;return Fn.from(t[n]).fold(function(){return $s('<span class="${prefix}-toolbar-button ${prefix}-toolbar-group-item ${prefix}-icon-'+n+' ${prefix}-icon"></span>')},function(n){return $s('<span class="${prefix}-toolbar-button ${prefix}-toolbar-group-item">'+n+"</span>")})}function Of(e){return of({name:e+"-edge",overrides:function(n){return n.model.manager.edgeActions[e].fold(function(){return{}},function(r){return{events:jr([Hr(we(),r,[n]),Hr(Te(),r,[n]),Hr(ke(),function(n,e,t){t.mouseIsDown.get()&&r(n,t)},[n])])}})}})}function Tf(n,e,t){e.store.manager.onLoad(n,e,t)}function kf(n,e,t){e.store.manager.onUnload(n,e,t)}function Ef(){var n=Pn(null);return Bo({set:n.set,get:n.get,isNotSet:function(){return null===n.get()},clear:function(){n.set(null)},readState:function(){return{mode:"memory",value:n.get()}}})}function Cf(){var i=Pn({}),u=Pn({});return Bo({readState:function(){return{mode:"dataset",dataByValue:i.get(),dataByText:u.get()}},lookup:function(n){return Mt(i.get(),n).orThunk(function(){return Mt(u.get(),n)})},update:function(n){var e=i.get(),t=u.get(),r={},o={};C(n,function(e){r[e.value]=e,Mt(e,"meta").each(function(n){Mt(n,"text").each(function(n){o[n]=e})})}),i.set(x(x({},e),r)),u.set(x(x({},t),o))},clear:function(){i.set({}),u.set({})}})}function Df(n,e,t,r){var o=e.store;t.update([r]),o.setValue(n,r),e.onSetValue(n,r)}function Mf(n,e){ll.set(n,e)}function If(n){return ll.get(n)}function Rf(n){var e=n.event().raw();if(function(n){return-1!==n.type.indexOf("touch")}(e)){var t=e;return t.touches!==undefined&&1===t.touches.length?Fn.some(t.touches[0]).map(function(n){return ml(n.clientX,n.clientY)}):Fn.none()}var r=e;return r.clientX!==undefined?Fn.some(r).map(function(n){return ml(n.clientX,n.clientY)}):Fn.none()}function Af(n){return n.model.minX}function Ff(n){return n.model.minY}function Bf(n){return n.model.minX-1}function Vf(n){return n.model.minY-1}function Nf(n){return n.model.maxX}function jf(n){return n.model.maxY}function _f(n){return n.model.maxX+1}function Pf(n){return n.model.maxY+1}function Hf(n,e,t){return e(n)-t(n)}function zf(n){return Hf(n,Nf,Af)}function Lf(n){return Hf(n,jf,Ff)}function Gf(n){return zf(n)/2}function Uf(n){return Lf(n)/2}function $f(n){return n.stepSize}function Wf(n){return n.snapToGrid}function Xf(n){return n.snapStart}function qf(n){return n.rounded}function Yf(n,e){return n[e+"-edge"]!==undefined}function Kf(n){return Yf(n,"left")}function Jf(n){return Yf(n,"right")}function Qf(n){return Yf(n,"top")}function Zf(n){return Yf(n,"bottom")}function ns(n){return n.model.value.get()}function es(n){return{x:b(n)}}function ts(n){return{y:b(n)}}function rs(n,e){return{x:b(n),y:b(e)}}function os(n,e){U(n,gl(),{value:e})}function is(n,e,t,r){return n<e?n:t<n?t:n===e?e-1:Math.max(e,n-r)}function us(n,e,t,r){return t<n?n:n<e?e:n===t?t+1:Math.min(t,n+r)}function cs(n,e,t){return Math.max(e,Math.min(t,n))}function as(n){var e=n.min,t=n.max,r=n.range,o=n.value,i=n.step,u=n.snap,c=n.snapStart,a=n.rounded,f=n.hasMinEdge,s=n.hasMaxEdge,l=n.minBound,d=n.maxBound,m=n.screenRange,g=f?e-1:e,p=s?t+1:t;if(o<l)return g;if(d<o)return p;var h=function(n,e,t){return Math.min(t,Math.max(n,e))-e}(o,l,d),v=cs(h/m*r+e,g,p);return u&&e<=v&&v<=t?function(u,t,c,a,n){return n.fold(function(){var n=u-t,e=Math.round(n/a)*a;return cs(t+e,t-1,c+1)},function(n){var e=(u-n)%a,t=Math.round(e/a),r=Math.floor((u-n)/a),o=Math.floor((c-n)/a),i=n+Math.min(o,r+t)*a;return Math.max(n,i)})}(v,e,t,i,c):a?Math.round(v):v}function fs(n){var e=n.min,t=n.max,r=n.range,o=n.value,i=n.hasMinEdge,u=n.hasMaxEdge,c=n.maxBound,a=n.maxOffset,f=n.centerMinEdge,s=n.centerMaxEdge;return o<e?i?0:f:t<o?u?c:s:(o-e)/r*a}function ss(n){return n.element().dom().getBoundingClientRect()}function ls(n,e){return n[e]}function ds(n){var e=ss(n);return ls(e,pl)}function ms(n){var e=ss(n);return ls(e,"right")}function gs(n){var e=ss(n);return ls(e,"top")}function ps(n){var e=ss(n);return ls(e,"bottom")}function hs(n){var e=ss(n);return ls(e,"width")}function vs(n){var e=ss(n);return ls(e,"height")}function ys(n,e,t){return(n+e)/2-t}function bs(n,e){var t=ss(n),r=ss(e),o=ls(t,pl),i=ls(t,"right"),u=ls(r,pl);return ys(o,i,u)}function xs(n,e){var t=ss(n),r=ss(e),o=ls(t,"top"),i=ls(t,"bottom"),u=ls(r,"top");return ys(o,i,u)}function ws(n,e){U(n,gl(),{value:e})}function Ss(n){return{x:b(n)}}function Os(n,e,t){var r={min:Af(e),max:Nf(e),range:zf(e),value:t,step:$f(e),snap:Wf(e),snapStart:Xf(e),rounded:qf(e),hasMinEdge:Kf(e),hasMaxEdge:Jf(e),minBound:ds(n),maxBound:ms(n),screenRange:hs(n)};return as(r)}function Ts(t){return function(n,e){return function(n,e,t){var r=(0<n?us:is)(ns(t).x(),Af(t),Nf(t),$f(t));return ws(e,Ss(r)),Fn.some(r)}(t,n,e).map(function(){return!0})}}function ks(n,e,t,r,o,i){var u=function(e,n,t,r,o){var i=hs(e),u=r.bind(function(n){return Fn.some(bs(n,e))}).getOr(0),c=o.bind(function(n){return Fn.some(bs(n,e))}).getOr(i),a={min:Af(n),max:Nf(n),range:zf(n),value:t,hasMinEdge:Kf(n),hasMaxEdge:Jf(n),minBound:ds(e),minOffset:0,maxBound:ms(e),maxOffset:i,centerMinEdge:u,centerMaxEdge:c};return fs(a)}(e,i,t,r,o);return ds(e)-ds(n)+u}function Es(n,e){U(n,gl(),{value:e})}function Cs(n){return{y:b(n)}}function Ds(n,e,t){var r={min:Ff(e),max:jf(e),range:Lf(e),value:t,step:$f(e),snap:Wf(e),snapStart:Xf(e),rounded:qf(e),hasMinEdge:Qf(e),hasMaxEdge:Zf(e),minBound:gs(n),maxBound:ps(n),screenRange:vs(n)};return as(r)}function Ms(t){return function(n,e){return function(n,e,t){var r=(0<n?us:is)(ns(t).y(),Ff(t),jf(t),$f(t));return Es(e,Cs(r)),Fn.some(r)}(t,n,e).map(function(){return!0})}}function Is(n,e,t,r,o,i){var u=function(e,n,t,r,o){var i=vs(e),u=r.bind(function(n){return Fn.some(xs(n,e))}).getOr(0),c=o.bind(function(n){return Fn.some(xs(n,e))}).getOr(i),a={min:Ff(n),max:jf(n),range:Lf(n),value:t,hasMinEdge:Qf(n),hasMaxEdge:Zf(n),minBound:gs(e),minOffset:0,maxBound:ps(e),maxOffset:i,centerMinEdge:u,centerMaxEdge:c};return fs(a)}(e,i,t,r,o);return gs(e)-gs(n)+u}function Rs(n,e){U(n,gl(),{value:e})}function As(n,e){return{x:b(n),y:b(e)}}function Fs(t,r){return function(n,e){return function(n,e,t,r){var o=0<n?us:is,i=e?ns(r).x():o(ns(r).x(),Af(r),Nf(r),$f(r)),u=e?o(ns(r).y(),Ff(r),jf(r),$f(r)):ns(r).y();return Rs(t,As(i,u)),Fn.some(i)}(t,r,n,e).map(function(){return!0})}}function Bs(e,t,r,n){return Xs.forToolbar(t,function(){var n=r();e.setContextToolbar([{label:t+" group",items:n}])},{},n)}function Vs(n){return[function(o){function i(n){return n<0?"black":360<n?"white":"hsl("+n+", 100%, 50%)"}return Nl.sketch({dom:$s('<div class="${prefix}-slider ${prefix}-hue-slider-container"></div>'),components:[Nl.parts()["left-edge"](xf('<div class="${prefix}-hue-slider-black"></div>')),Nl.parts().spectrum({dom:$s('<div class="${prefix}-slider-gradient-container"></div>'),components:[xf('<div class="${prefix}-slider-gradient"></div>')],behaviours:Vo([Bi.config({toggleClass:Pi.resolve("thumb-active")})])}),Nl.parts()["right-edge"](xf('<div class="${prefix}-hue-slider-white"></div>')),Nl.parts().thumb({dom:$s('<div class="${prefix}-slider-thumb"></div>'),behaviours:Vo([Bi.config({toggleClass:Pi.resolve("thumb-active")})])})],onChange:function(n,e,t){var r=i(t.x());Ui(e.element(),"background-color",r),o.onChange(n,e,r)},onDragStart:function(n,e){Bi.on(e)},onDragEnd:function(n,e){Bi.off(e)},onInit:function(n,e,t,r){var o=i(r.x());Ui(e.element(),"background-color",o)},stepSize:10,model:{mode:"x",minX:0,maxX:360,getInitialValue:function(){return{x:function(){return o.getInitialValue()}}}},sliderBehaviours:Vo([Ni(Nl.refresh)])})}(n)]}function Ns(n){var e=n.selection.getStart(),t=Qe.fromDom(e),r=Qe.fromDom(n.getBody()),o=function(e,n){return(tt(n)?Fn.some(n):fn(n).filter(tt)).map(function(n){return Fr(n,function(n){return pi(n,"font-size").isSome()},e).bind(function(n){return pi(n,"font-size")}).getOrThunk(function(){return gi(n,"font-size")})}).getOr("")}(function(n){return cn(r,n)},t);return R(Hl,function(n){return o===n}).getOr("medium")}function js(n){return[xf('<span class="${prefix}-toolbar-button ${prefix}-icon-small-font ${prefix}-icon"></span>'),function(n){return Pl({onChange:n.onChange,sizes:Ll,category:"font",getInitialValue:n.getInitialValue})}(n),xf('<span class="${prefix}-toolbar-button ${prefix}-icon-large-font ${prefix}-icon"></span>')]}function _s(n){var e=function t(n){return n.uid!==undefined}(n)&&Mn(n,"uid")?n.uid:Ac("memento");return{get:function(n){return n.getSystem().getByUid(e).getOrDie()},getOpt:function(n){return n.getSystem().getByUid(e).toOption()},asSpec:function(){return x(x({},n),{uid:e})}}}var Ps,Hs,zs=Ht([Kt("name"),Kt("factory"),Kt("configFields"),rr("apis",{}),rr("extraApis",{})]),Ls=Ht([Kt("name"),Kt("factory"),Kt("configFields"),Kt("partFields"),rr("apis",{}),rr("extraApis",{})]),Gs=vf({name:"Button",factory:function(n){function t(e){return Mt(n.dom,"attributes").bind(function(n){return Mt(n,e)})}var e=function(n){return jr(F([n.map(function(t){return Io(function(n,e){t(n),e.stop()})}).toArray(),fi()]))}(n.action),r=n.dom.tag;return{uid:n.uid,dom:n.dom,components:n.components,events:e,behaviours:_a(n.buttonBehaviours,[Gi.config({}),Na.config({mode:"execution",useSpace:!0,useEnter:!0})]),domModification:{attributes:function(){if("button"!==r)return{role:t("role").getOr("button")};var n=t("type").getOr("button"),e=t("role").map(function(n){return{role:n}}).getOr({});return x({type:n},e)}()},eventOrder:n.eventOrder}},configFields:[rr("uid",undefined),Kt("dom"),rr("components",[]),ja("buttonBehaviours",[Gi,Na]),Zt("action"),Zt("role"),rr("eventOrder",{})]}),Us=Yr({fields:[],name:"unselecting",active:/* */Object.freeze({events:function(n){return jr([_r(Be(),b(!0))])},exhibit:function(n,e){return $r({styles:{"-webkit-user-select":"none","user-select":"none","-ms-user-select":"none","-moz-user-select":"-moz-none"},attributes:{unselectable:"on"}})}})}),$s=function(n){var e=function(n,r){return n.replace(/\$\{([^{}]*)\}/g,function(n,e){var t=r[e];return function(n){var e=typeof n;return"string"==e||"number"==e}(t)?t.toString():n})}(n,{prefix:Pi.prefix()});return bf(e)},Ws=function(n,e,t,r){return Gs.sketch({dom:Sf(n,r),action:e,buttonBehaviours:wt(Vo([Us.config({})]),t)})},Xs={forToolbar:Ws,forToolbarCommand:function(n,e){return Ws(e,function(){n.execCommand(e)},{},n)},forToolbarStateAction:function(n,e,t,r){var o=wf(t);return Ws(e,r,o,n)},forToolbarStateCommand:function(n,e){var t=wf(e);return Ws(e,function(){n.execCommand(e)},t,n)},getToolbarIconButton:Sf},qs=of({schema:[Kt("dom")],name:"label"}),Ys=Of("top-left"),Ks=Of("top"),Js=Of("top-right"),Qs=Of("right"),Zs=Of("bottom-right"),nl=Of("bottom"),el=Of("bottom-left"),tl=[qs,Of("left"),Qs,Ks,nl,Ys,Js,el,Zs,rf({name:"thumb",defaults:b({dom:{styles:{position:"absolute"}}}),overrides:function(n){return{events:jr([Lr(we(),n,"spectrum"),Lr(Se(),n,"spectrum"),Lr(Oe(),n,"spectrum"),Lr(Te(),n,"spectrum"),Lr(ke(),n,"spectrum"),Lr(Ee(),n,"spectrum")])}}}),rf({schema:[ir("mouseIsDown",function(){return Pn(!1)})],name:"spectrum",overrides:function(t){function r(e,n){return o.getValueFromEvent(n).map(function(n){return o.setValueFrom(e,t,n)})}var o=t.model.manager;return{behaviours:Vo([Na.config({mode:"special",onLeft:function(n){return o.onLeft(n,t)},onRight:function(n){return o.onRight(n,t)},onUp:function(n){return o.onUp(n,t)},onDown:function(n){return o.onDown(n,t)}}),Gi.config({})]),events:jr([Pr(we(),r),Pr(Se(),r),Pr(Te(),r),Pr(ke(),function(n,e){t.mouseIsDown.get()&&r(n,e)})])}}})],rl=/* */Object.freeze({onLoad:Tf,onUnload:kf,setValue:function(n,e,t,r){e.store.manager.setValue(n,e,t,r)},getValue:function(n,e,t){return e.store.manager.getValue(n,e,t)},getState:function(n,e,t){return t}}),ol=/* */Object.freeze({events:function(t,r){var n=t.resetOnDom?[Co(function(n,e){Tf(n,t,r)}),Do(function(n,e){kf(n,t,r)})]:[Wr(t,r,Tf)];return jr(n)}}),il=/* */Object.freeze({memory:Ef,dataset:Cf,manual:function(){return Bo({readState:function(){}})},init:function(n){return n.store.manager.state(n)}}),ul=[Zt("initialValue"),Kt("getFallbackEntry"),Kt("getDataKey"),Kt("setValue"),ei("manager",{setValue:Df,getValue:function(n,e,t){var r=e.store,o=r.getDataKey(n);return t.lookup(o).fold(function(){return r.getFallbackEntry(o)},function(n){return n})},onLoad:function(e,t,r){t.store.initialValue.each(function(n){Df(e,t,r,n)})},onUnload:function(n,e,t){t.clear()},state:Cf})],cl=[Kt("getValue"),rr("setValue",w),Zt("initialValue"),ei("manager",{setValue:function(n,e,t,r){e.store.setValue(n,r),e.onSetValue(n,r)},getValue:function(n,e,t){return e.store.getValue(n)},onLoad:function(e,t,n){t.store.initialValue.each(function(n){t.store.setValue(e,n)})},onUnload:w,state:Fo.init})],al=[Zt("initialValue"),ei("manager",{setValue:function(n,e,t,r){t.set(r),e.onSetValue(n,r)},getValue:function(n,e,t){return t.get()},onLoad:function(n,e,t){e.store.initialValue.each(function(n){t.isNotSet()&&t.set(n)})},onUnload:function(n,e,t){t.clear()},state:Ef})],fl=[or("store",{mode:"memory"},Yt("mode",{memory:al,manual:cl,dataset:ul})),Jo("onSetValue"),rr("resetOnDom",!1)],sl=Yr({fields:fl,name:"representing",active:ol,apis:rl,extra:{setValueFrom:function(n,e){var t=sl.getValue(e);sl.setValue(n,t)}},state:il}),ll=Wi("width",function(n){return n.dom().offsetWidth}),dl=function(t,r){return{left:b(t),top:b(r),translate:function(n,e){return dl(t+n,r+e)}}},ml=dl,gl=b("slider.change.value"),pl="left",hl=Ts(-1),vl=Ts(1),yl=Fn.none,bl=Fn.none,xl={"top-left":Fn.none(),top:Fn.none(),"top-right":Fn.none(),right:Fn.some(function(n,e){os(n,es(_f(e)))}),"bottom-right":Fn.none(),bottom:Fn.none(),"bottom-left":Fn.none(),left:Fn.some(function(n,e){os(n,es(Bf(e)))})},wl=/* */Object.freeze({setValueFrom:function(n,e,t){var r=Os(n,e,t),o=Ss(r);return ws(n,o),r},setToMin:function(n,e){var t=Af(e);ws(n,Ss(t))},setToMax:function(n,e){var t=Nf(e);ws(n,Ss(t))},findValueOfOffset:Os,getValueFromEvent:function(n){return Rf(n).map(function(n){return n.left()})},findPositionOfValue:ks,setPositionFromValue:function(n,e,t,r){var o=ns(t),i=ks(n,r.getSpectrum(n),o.x(),r.getLeftEdge(n),r.getRightEdge(n),t),u=If(e.element())/2;Ui(e.element(),"left",i-u+"px")},onLeft:hl,onRight:vl,onUp:yl,onDown:bl,edgeActions:xl}),Sl=Fn.none,Ol=Fn.none,Tl=Ms(-1),kl=Ms(1),El={"top-left":Fn.none(),top:Fn.some(function(n,e){os(n,ts(Vf(e)))}),"top-right":Fn.none(),right:Fn.none(),"bottom-right":Fn.none(),bottom:Fn.some(function(n,e){os(n,ts(Pf(e)))}),"bottom-left":Fn.none(),left:Fn.none()},Cl=/* */Object.freeze({setValueFrom:function(n,e,t){var r=Ds(n,e,t),o=Cs(r);return Es(n,o),r},setToMin:function(n,e){var t=Ff(e);Es(n,Cs(t))},setToMax:function(n,e){var t=jf(e);Es(n,Cs(t))},findValueOfOffset:Ds,getValueFromEvent:function(n){return Rf(n).map(function(n){return n.top()})},findPositionOfValue:Is,setPositionFromValue:function(n,e,t,r){var o=ns(t),i=Is(n,r.getSpectrum(n),o.y(),r.getTopEdge(n),r.getBottomEdge(n),t),u=Xi(e.element())/2;Ui(e.element(),"top",i-u+"px")},onLeft:Sl,onRight:Ol,onUp:Tl,onDown:kl,edgeActions:El}),Dl=Fs(-1,!1),Ml=Fs(1,!1),Il=Fs(-1,!0),Rl=Fs(1,!0),Al={"top-left":Fn.some(function(n,e){os(n,rs(Bf(e),Vf(e)))}),top:Fn.some(function(n,e){os(n,rs(Gf(e),Vf(e)))}),"top-right":Fn.some(function(n,e){os(n,rs(_f(e),Vf(e)))}),right:Fn.some(function(n,e){os(n,rs(_f(e),Uf(e)))}),"bottom-right":Fn.some(function(n,e){os(n,rs(_f(e),Pf(e)))}),bottom:Fn.some(function(n,e){os(n,rs(Gf(e),Pf(e)))}),"bottom-left":Fn.some(function(n,e){os(n,rs(Bf(e),Pf(e)))}),left:Fn.some(function(n,e){os(n,rs(Bf(e),Uf(e)))})},Fl=/* */Object.freeze({setValueFrom:function(n,e,t){var r=Os(n,e,t.left()),o=Ds(n,e,t.top()),i=As(r,o);return Rs(n,i),i},setToMin:function(n,e){var t=Af(e),r=Ff(e);Rs(n,As(t,r))},setToMax:function(n,e){var t=Nf(e),r=jf(e);Rs(n,As(t,r))},getValueFromEvent:function(n){return Rf(n)},setPositionFromValue:function(n,e,t,r){var o=ns(t),i=ks(n,r.getSpectrum(n),o.x(),r.getLeftEdge(n),r.getRightEdge(n),t),u=Is(n,r.getSpectrum(n),o.y(),r.getTopEdge(n),r.getBottomEdge(n),t),c=If(e.element())/2,a=Xi(e.element())/2;Ui(e.element(),"left",i-c+"px"),Ui(e.element(),"top",u-a+"px")},onLeft:Dl,onRight:Ml,onUp:Il,onDown:Rl,edgeActions:Al}),Bl=[rr("stepSize",1),rr("onChange",w),rr("onChoose",w),rr("onInit",w),rr("onDragStart",w),rr("onDragEnd",w),rr("snapToGrid",!1),rr("rounded",!0),Zt("snapStart"),Jt("model",Yt("mode",{x:[rr("minX",0),rr("maxX",100),ir("value",function(n){return Pn(n.mode.minX)}),Kt("getInitialValue"),ei("manager",wl)],y:[rr("minY",0),rr("maxY",100),ir("value",function(n){return Pn(n.mode.minY)}),Kt("getInitialValue"),ei("manager",Cl)],xy:[rr("minX",0),rr("maxX",100),rr("minY",0),rr("maxY",100),ir("value",function(n){return Pn({x:b(n.mode.minX),y:b(n.mode.minY)})}),Kt("getInitialValue"),ei("manager",Fl)]})),gc("sliderBehaviours",[Na,sl]),ir("mouseIsDown",function(){return Pn(!1)})],Vl=b("mouse.released"),Nl=yf({name:"Slider",configFields:Bl,partFields:tl,factory:function(i,n,e,t){function u(n){return Cc(n,i,"thumb")}function c(n){return Cc(n,i,"spectrum")}function r(n){return Ec(n,i,"left-edge")}function o(n){return Ec(n,i,"right-edge")}function a(n){return Ec(n,i,"top-edge")}function f(n){return Ec(n,i,"bottom-edge")}function s(n,e){v.setPositionFromValue(n,e,i,{getLeftEdge:r,getRightEdge:o,getTopEdge:a,getBottomEdge:f,getSpectrum:c})}function l(n,e){h.value.set(e);var t=u(n);return s(n,t),i.onChange(n,t,e),Fn.some(!0)}function d(t){var n=i.mouseIsDown.get();i.mouseIsDown.set(!1),n&&Ec(t,i,"thumb").each(function(n){var e=h.value.get();i.onChoose(t,n,e)})}function m(n,e){e.stop(),i.mouseIsDown.set(!0),i.onDragStart(n,u(n))}function g(n,e){e.stop(),i.onDragEnd(n,u(n)),d(n)}var p,h=i.model,v=h.manager;return{uid:i.uid,dom:i.dom,components:n,behaviours:hc(i.sliderBehaviours,[Na.config({mode:"special",focusIn:function(n){return Ec(n,i,"spectrum").map(Na.focusIn).map(b(!0))}}),sl.config({store:{mode:"manual",getValue:function(n){return h.value.get()}}}),Di.config({channels:(p={},p[Vl()]={onReceive:d},p)})]),events:jr([Pr(gl(),function(n,e){l(n,e.event().value())}),Co(function(n,e){var t=h.getInitialValue();h.value.set(t);var r=u(n);s(n,r);var o=c(n);i.onInit(n,r,o,h.value.get())}),Pr(we(),m),Pr(Oe(),g),Pr(Te(),m),Pr(Ee(),g)]),apis:{resetToMin:function(n){v.setToMin(n,i)},resetToMax:function(n){v.setToMax(n,i)},changeValue:l,refresh:s},domModification:{styles:{position:"relative"}}}},apis:{resetToMin:function(n,e){n.resetToMin(e)},resetToMax:function(n,e){n.resetToMax(e)},refresh:function(n,e){n.refresh(e)}}}),jl=function(n,r){var e={onChange:function(n,e,t){r.undoManager.transact(function(){r.formatter.apply("forecolor",{value:t}),r.nodeChanged()})},getInitialValue:function(){return-1}};return Bs(n,"color-levels",function(){return Vs(e)},r)},_l=Ht([Kt("getInitialValue"),Kt("onChange"),Kt("category"),Kt("sizes")]),Pl=function(n){var o=qt("SizeSlider",_l,n);return Nl.sketch({dom:{tag:"div",classes:[Pi.resolve("slider-"+o.category+"-size-container"),Pi.resolve("slider"),Pi.resolve("slider-size-container")]},onChange:function(n,e,t){var r=t.x();!function(n){return 0<=n&&n<o.sizes.length}(r)||o.onChange(r)},onDragStart:function(n,e){Bi.on(e)},onDragEnd:function(n,e){Bi.off(e)},model:{mode:"x",minX:0,maxX:o.sizes.length-1,getInitialValue:function(){return{x:function(){return o.getInitialValue()}}}},stepSize:1,snapToGrid:!0,sliderBehaviours:Vo([Ni(Nl.refresh)]),components:[Nl.parts().spectrum({dom:$s('<div class="${prefix}-slider-size-container"></div>'),components:[xf('<div class="${prefix}-slider-size-line"></div>')]}),Nl.parts().thumb({dom:$s('<div class="${prefix}-slider-thumb"></div>'),behaviours:Vo([Bi.config({toggleClass:Pi.resolve("thumb-active")})])})]})},Hl=["9px","10px","11px","12px","14px","16px","18px","20px","24px","32px","36px"],zl={candidates:b(Hl),get:function(n){return function(e){return A(Hl,function(n){return n===e})}(Ns(n)).getOr(2)},apply:function(e,n){(function(n){return Fn.from(Hl[n])})(n).each(function(n){!function(n,e){Ns(n)!==e&&n.execCommand("fontSize",!1,e)}(e,n)})}},Ll=zl.candidates(),Gl=window.Promise?window.Promise:(Ps=Ul.immediateFn||"function"==typeof window.setImmediate&&window.setImmediate||function(n){l.setTimeout(n,1)},Hs=Array.isArray||function(n){return"[object Array]"===Object.prototype.toString.call(n)},Ul.prototype["catch"]=function(n){return this.then(null,n)},Ul.prototype.then=function(t,r){var o=this;return new Ul(function(n,e){Wl.call(o,new Kl(t,r,n,e))})},Ul.all=function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];var a=Array.prototype.slice.call(1===n.length&&Hs(n[0])?n[0]:n);return new Ul(function(o,i){if(0===a.length)return o([]);var u=a.length;function c(e,n){try{if(n&&("object"==typeof n||"function"==typeof n)){var t=n.then;if("function"==typeof t)return void t.call(n,function(n){c(e,n)},i)}a[e]=n,0==--u&&o(a)}catch(r){i(r)}}for(var n=0;n<a.length;n++)c(n,a[n])})},Ul.resolve=function(e){return e&&"object"==typeof e&&e.constructor===Ul?e:new Ul(function(n){n(e)})},Ul.reject=function(t){return new Ul(function(n,e){e(t)})},Ul.race=function(o){return new Ul(function(n,e){for(var t=0,r=o;t<r.length;t++)r[t].then(n,e)})},Ul);function Ul(n){if("object"!=typeof this)throw new TypeError("Promises must be constructed via new");if("function"!=typeof n)throw new TypeError("not a function");this._state=null,this._value=null,this._deferreds=[],Jl(n,$l(Xl,this),$l(ql,this))}function $l(n,e){return function(){return n.apply(e,arguments)}}function Wl(r){var o=this;null!==this._state?Ps(function(){var n=o._state?r.onFulfilled:r.onRejected;if(null!==n){var e;try{e=n(o._value)}catch(t){return void r.reject(t)}r.resolve(e)}else(o._state?r.resolve:r.reject)(o._value)}):this._deferreds.push(r)}function Xl(n){try{if(n===this)throw new TypeError("A promise cannot be resolved with itself.");if(n&&("object"==typeof n||"function"==typeof n)){var e=n.then;if("function"==typeof e)return void Jl($l(e,n),$l(Xl,this),$l(ql,this))}this._state=!0,this._value=n,Yl.call(this)}catch(t){ql.call(this,t)}}function ql(n){this._state=!1,this._value=n,Yl.call(this)}function Yl(){for(var n=0,e=this._deferreds;n<e.length;n++){var t=e[n];Wl.call(this,t)}this._deferreds=[]}function Kl(n,e,t,r){this.onFulfilled="function"==typeof n?n:null,this.onRejected="function"==typeof e?e:null,this.resolve=t,this.reject=r}function Jl(n,e,t){var r=!1;try{n(function(n){r||(r=!0,e(n))},function(n){r||(r=!0,t(n))})}catch(o){if(r)return;r=!0,t(o)}}function Ql(n){return function e(t){return new Gl(function(n){var e=new l.FileReader;e.onloadend=function(){n(e.result)},e.readAsDataURL(t)})}(n).then(function(n){return n.split(",")[1]})}function Zl(o,i){(function(n){return Ql(n)})(i).then(function(r){o.undoManager.transact(function(){var n=o.editorUpload.blobCache,e=n.create(bc("mceu"),i,r);n.add(e);var t=o.dom.createHTML("img",{src:e.blobUri()});o.insertContent(t)})})}function nd(t){var e=_s({dom:{tag:"input",attributes:{accept:"image/*",type:"file",title:""},styles:{visibility:"hidden",position:"absolute"}},events:jr([Gr(Ae()),Pr(Re(),function(n,e){(function(n){var e=n.event(),t=e.raw().target.files||e.raw().dataTransfer.files;return Fn.from(t[0])})(e).each(function(n){Zl(t,n)})})])});return Gs.sketch({dom:Xs.getToolbarIconButton("image",t),components:[e.asSpec()],action:function(n){e.get(n).element().dom().click()}})}function ed(n){return n.dom().textContent}function td(n){return 0<n.length}function rd(n){return n===undefined||null===n?"":n}function od(n,e,t){return t.text.toOption().filter(td).fold(function(){return function(n){return Zr(n,"href")===ed(n)}(n)?Fn.some(e):Fn.none()},Fn.some)}function id(n,e){var t=e.selection.getRng();n(),e.selection.setRng(t)}function ud(n){return n.dom().value}function cd(n,e){if(e===undefined)throw new Error("Value.set was undefined");n.dom().value=e}function ad(n){return x(x({},function(n){return Vo([Gi.config({onFocus:!1===n.selectOnFocus?w:function(n){var e=n.element(),t=ud(e);e.dom().setSelectionRange(0,t.length)}})])}(n)),hc(n.inputBehaviours,[sl.config({store:{mode:"manual",initialValue:n.data.getOr(undefined),getValue:function(n){return ud(n.element())},setValue:function(n,e){ud(n.element())!==e&&cd(n.element(),e)}},onSetValue:n.onSetValue})]))}function fd(n,e){var t=_s(hm.sketch({inputAttributes:{placeholder:xm.translate(e)},onSetValue:function(n,e){G(n,Ie())},inputBehaviours:Vo([dm.config({find:Fn.some}),bm.config({}),Na.config({mode:"execution"})]),selectOnFocus:!1})),r=_s(Gs.sketch({dom:$s('<button class="${prefix}-input-container-x ${prefix}-icon-cancel-circle ${prefix}-icon"></button>'),action:function(n){var e=t.get(n);sl.setValue(e,"")}}));return{name:n,spec:mm.sketch({dom:$s('<div class="${prefix}-input-container"></div>'),components:[t.asSpec(),r.asSpec()],containerBehaviours:Vo([Bi.config({toggleClass:Pi.resolve("input-container-empty")}),dm.config({find:function(n){return Fn.some(t.get(n))}}),fm("input-clearing",[Pr(Ie(),function(n){var e=t.get(n);(0<sl.getValue(e).length?Bi.off:Bi.on)(n)})])])})}}function sd(n,e,t){e.disabled&&Sm(n,e)}function ld(n,e){return!0===e.useNative&&k(wm,q(n.element()))}function dd(n){Jr(n.element(),"disabled","disabled")}function md(n){eo(n.element(),"disabled")}function gd(n){Jr(n.element(),"aria-disabled","true")}function pd(n){Jr(n.element(),"aria-disabled","false")}function hd(e,n,t){n.disableClass.each(function(n){ao(e.element(),n)}),(ld(e,n)?md:pd)(e),n.onEnabled(e)}function vd(n,e){return ld(n,e)?function(n){return no(n.element(),"disabled")}(n):function(n){return"true"===Zr(n.element(),"aria-disabled")}(n)}function yd(n){return"<alloy.field."+n+">"}function bd(){function e(){t.get().each(function(n){n.destroy()})}var t=Pn(Fn.none());return{clear:function(){e(),t.set(Fn.none())},isSet:function(){return t.get().isSome()},set:function(n){e(),t.set(Fn.some(n))},run:function(n){t.get().each(n)}}}function xd(){var e=Pn(Fn.none());return{clear:function(){e.set(Fn.none())},set:function(n){e.set(Fn.some(n))},isSet:function(){return e.get().isSome()},on:function(n){e.get().each(n)}}}function wd(n){function r(e,n,t){return Gs.sketch({dom:$s('<span class="${prefix}-icon-'+n+' ${prefix}-icon"></span>'),action:function(n){U(n,u,{direction:e})},buttonBehaviours:Vo([Em.config({disableClass:Pi.resolve("toolbar-navigation-disabled"),disabled:!t})])})}function o(n,o){var i=Ki(n.element(),"."+Pi.resolve("serialised-dialog-screen"));Zi(n.element(),"."+Pi.resolve("serialised-dialog-chain")).each(function(r){0<=c.state.currentScreen.get()+o&&c.state.currentScreen.get()+o<i.length&&(pi(r,"left").each(function(n){var e=parseInt(n,10),t=If(i[0]);Ui(r,"left",e-o*t+"px")}),c.state.currentScreen.set(c.state.currentScreen.get()+o))})}function i(e){var n=Ki(e.element(),"input");Fn.from(n[c.state.currentScreen.get()]).each(function(n){e.getSystem().getByDom(n).each(function(n){!function(n,e){n.getSystem().triggerFocus(e,n.element())}(e,n.element())})});var t=f.get(e);Du.highlightAt(t,c.state.currentScreen.get())}var u="navigateEvent",e=Or([Kt("fields"),rr("maxFieldIndex",n.fields.length-1),Kt("onExecute"),Kt("getInitialValue"),ir("state",function(){return{dialogSwipeState:xd(),currentScreen:Pn(0)}})]),c=qt("SerialisedDialog",e,n),a=_s(Mm(function(t){return{dom:$s('<div class="${prefix}-serialised-dialog"></div>'),components:[mm.sketch({dom:$s('<div class="${prefix}-serialised-dialog-chain" style="left: 0px; position: absolute;"></div>'),components:me(c.fields,function(n,e){return e<=c.maxFieldIndex?mm.sketch({dom:$s('<div class="${prefix}-serialised-dialog-screen"></div>'),components:[r(-1,"previous",0<e),t.field(n.name,n.spec),r(1,"next",e<c.maxFieldIndex)]}):t.field(n.name,n.spec)})})],formBehaviours:Vo([Ni(function(n,e){!function(n,e){Zi(n.element(),"."+Pi.resolve("serialised-dialog-chain")).each(function(n){Ui(n,"left",-c.state.currentScreen.get()*e.width+"px")})}(n,e)}),Na.config({mode:"special",focusIn:function(n){i(n)},onTab:function(n){return o(n,1),Fn.some(!0)},onShiftTab:function(n){return o(n,-1),Fn.some(!0)}}),fm("form-events",[Co(function(e,n){c.state.currentScreen.set(0),c.state.dialogSwipeState.clear();var t=f.get(e);Du.highlightFirst(t),c.getInitialValue(e).each(function(n){sl.setValue(e,n)})}),Io(c.onExecute),Pr(Fe(),function(n,e){"left"===e.event().raw().propertyName&&i(n)}),Pr(u,function(n,e){var t=e.event().direction();o(n,t)})])])}})),f=_s({dom:$s('<div class="${prefix}-dot-container"></div>'),behaviours:Vo([Du.config({highlightClass:Pi.resolve("dot-active"),itemClass:Pi.resolve("dot-item")})]),components:B(c.fields,function(n,e){return e<=c.maxFieldIndex?[xf('<div class="${prefix}-dot-item ${prefix}-icon-full-dot ${prefix}-icon"></div>')]:[]})});return{dom:$s('<div class="${prefix}-serializer-wrapper"></div>'),components:[a.asSpec(),f.asSpec()],behaviours:Vo([Na.config({mode:"special",focusIn:function(n){var e=a.get(n);Na.focusIn(e)}}),fm("serializer-wrapper-events",[Pr(we(),function(n,e){var t=e.event();c.state.dialogSwipeState.set(Im(t.raw().touches[0].clientX))}),Pr(Se(),function(n,e){var t=e.event();c.state.dialogSwipeState.on(function(n){e.event().prevent(),c.state.dialogSwipeState.set(Rm(n,t.raw().touches[0].clientX))})}),Pr(Oe(),function(r){c.state.dialogSwipeState.on(function(n){var e=a.get(r),t=-1*Am(n);o(e,t)})})])])}}function Sd(e){function n(n){return function(){throw new Error("The component must be in a context to send: "+n+"\n"+xo(e().element())+" is not in context.")}}return{debugInfo:b("fake"),triggerEvent:n("triggerEvent"),triggerFocus:n("triggerFocus"),triggerEscape:n("triggerEscape"),build:n("build"),addToWorld:n("addToWorld"),removeFromWorld:n("removeFromWorld"),addToGui:n("addToGui"),removeFromGui:n("removeFromGui"),getByUid:n("getByUid"),getByDom:n("getByDom"),broadcast:n("broadcast"),broadcastOn:n("broadcastOn"),broadcastEvent:n("broadcastEvent"),isConnected:b(!1)}}function Od(n,o){var i={};return Nn(n,function(n,r){Nn(n,function(n,e){var t=kn(e,[])(i);i[e]=t.concat([o(r,n)])})}),i}function Td(n){return n.cHandler}function kd(n,e){return{name:b(n),handler:b(e)}}function Ed(n,e,t){var r=x(x({},t),function(n,e){var t={};return C(n,function(n){t[n.name()]=n.handlers(e)}),t}(e,n));return Od(r,kd)}function Cd(n){var i=function(n){return ae(n)?{can:b(!0),abort:b(!1),run:n}:n}(n);return function(n,e){for(var t=[],r=2;r<arguments.length;r++)t[r-2]=arguments[r];var o=[n,e].concat(t);i.abort.apply(undefined,o)?e.stop():i.can.apply(undefined,o)&&i.run.apply(undefined,o)}}function Dd(n,e,t){var r=e[t];return r?function(u,c,n,a){var e=n.slice(0);try{var t=e.sort(function(n,e){var t=n[c](),r=e[c](),o=a.indexOf(t),i=a.indexOf(r);if(-1===o)throw new Error("The ordering for "+u+" does not have an entry for "+t+".\nOrder specified: "+JSON.stringify(a,null,2));if(-1===i)throw new Error("The ordering for "+u+" does not have an entry for "+r+".\nOrder specified: "+JSON.stringify(a,null,2));return o<i?-1:i<o?1:0});return yt.value(t)}catch(r){return yt.error([r])}}("Event: "+t,"name",n,r).map(function(n){var e=me(n,function(n){return n.handler()});return Nr(e)}):function(n,e){return yt.error(["The event ("+n+') has more than one behaviour that listens to it.\nWhen this occurs, you must specify an event ordering for the behaviours in your spec (e.g. [ "listing", "toggling" ]).\nThe behaviours that can trigger it are: '+JSON.stringify(me(e,function(n){return n.name()}),null,2)])}(t,n)}function Md(n){return Wt("custom.definition",Or([Er("dom","dom",Tt(),Or([Kt("tag"),rr("styles",{}),rr("classes",[]),rr("attributes",{}),Zt("value"),Zt("innerHtml")])),Kt("components"),Kt("uid"),rr("events",{}),rr("apis",{}),Er("eventOrder","eventOrder",function(n){return Ot.mergeWithThunk(b(n))}({"alloy.execute":["disabling","alloy.base.behaviour","toggling","typeaheadevents"],"alloy.focus":["alloy.base.behaviour","focusing","keying"],"alloy.system.init":["alloy.base.behaviour","disabling","toggling","representing"],input:["alloy.base.behaviour","representing","streaming","invalidating"],"alloy.system.detached":["alloy.base.behaviour","representing","item-events","tooltipping"],mousedown:["focusing","alloy.base.behaviour","item-type-events"],touchstart:["focusing","alloy.base.behaviour","item-type-events"],mouseover:["item-type-events","tooltipping"]}),Mr()),Zt("domModification")]),n)}function Id(e,n){C(n,function(n){uo(e,n)})}function Rd(e,n){C(n,function(n){ao(e,n)})}function Ad(n,e){return function(e,n){var t=me(n,function(n){return er(n.name(),[Kt("config"),rr("state",Fo)])}),r=Wt("component.behaviours",Or(t),e.behaviours).fold(function(n){throw new Error(Dr(n)+"\nComplete spec:\n"+JSON.stringify(e,null,2))},function(n){return n});return{list:n,data:S(r,function(n){var e=n.map(function(n){return{config:n.config,state:n.state.init(n.config)}});return function(){return e}})}}(n,e)}function Fd(n){var e=function(n){var e=kn("behaviours",{})(n),t=D(Bn(e),function(n){return e[n]!==undefined});return me(t,function(n){return e[n].me})}(n);return Ad(n,e)}function Bd(n,e,t){var r=function(n){return x(x({},n.dom),{uid:n.uid,domChildren:me(n.components,function(n){return n.element()})})}(n),o=function(n){return n.domModification.fold(function(){return $r({})},$r)}(n),i={"alloy.base.modification":o};return function(n,e){return x(x({},n),{attributes:x(x({},n.attributes),e.attributes),styles:x(x({},n.styles),e.styles),classes:n.classes.concat(e.classes)})}(r,0<e.length?function(e,n,t,r){var o=x({},n);C(t,function(n){o[n.name()]=n.exhibit(e,r)});function i(n){return M(n,function(n,e){return x(x({},e.modification),n)},{})}var u=Od(o,function(n,e){return{name:n,modification:e}}),c=M(u.classes,function(n,e){return e.modification.concat(n)},[]),a=i(u.attributes),f=i(u.styles);return $r({classes:c,attributes:a,styles:f})}(t,i,e,r):o)}function Vd(n,e,t){var r={"alloy.base.behaviour":function(n){return n.events}(n)};return function(n,e,t,r){var o=Ed(n,t,r);return Hm(o,e)}(t,n.eventOrder,e,r).getOrDie()}function Nd(t){function n(){return s}var r=Pn(_m),e=Xt(Md(t)),o=Fd(t),i=function(n){return n.list}(o),u=function(n){return n.data}(o),c=function(n){var e=Qe.fromTag(n.tag);Qr(e,n.attributes),Id(e,n.classes),mi(e,n.styles),n.innerHtml.each(function(n){return vo(e,n)});var t=n.domChildren;return gn(e,t),n.value.each(function(n){cd(e,n)}),n.uid,gf(e,n.uid),e}(Bd(e,i,u)),a=Vd(e,i,u),f=Pn(e.components),s={getSystem:r.get,config:function(n){var e=u;return(ae(e[n.name()])?e[n.name()]:function(){throw new Error("Could not find "+n.name()+" in "+JSON.stringify(t,null,2))})()},hasConfigured:function(n){return ae(u[n.name()])},spec:b(t),readState:function(n){return u[n]().map(function(n){return n.state.readState()}).getOr("not enabled")},getApis:function(){return e.apis},connect:function(n){r.set(n)},disconnect:function(){r.set(Sd(n))},element:b(c),syncComponents:function(){var n=ft(c),e=B(n,function(n){return r.get().getByDom(n).fold(function(){return[]},function(n){return[n]})});f.set(e)},components:f.get,events:b(a)};return s}function jd(n){var e=jm(n),t=e.events,r=u(e,["events"]),o=function(n){var e=kn("components",[])(n);return me(e,Gm)}(r),i=x(x({},r),{events:x(x({},Nm),t),components:o});return yt.value(Nd(i))}function _d(n){var e=Qe.fromText(n);return zm({element:e})}function Pd(n){(po(n.element()).isNone()||Gi.isFocused(n))&&(Gi.isFocused(n)||Gi.focus(n),U(n,$m,{item:n}))}function Hd(n){U(n,Wm,{item:n})}function zd(n,e,t,r){var o=n.getSystem().build(r);gt(n,o,t)}function Ld(n,e,t,r){var o=og(n);R(o,function(n){return cn(r.element(),n.element())}).each(yn)}function Gd(e,n,t,r,o){var i=og(e);return Fn.from(i[r]).map(function(n){return Ld(e,0,0,n),o.each(function(n){zd(e,0,function(n,e){!function(n,e,t){sn(n,t).fold(function(){st(n,e)},function(n){ln(n,e)})}(n,e,r)},n)}),n})}function Ud(n,e){var t={};Nn(n,function(n,e){C(n,function(n){t[n]=e})});var r=e,o=function(n){return jn(n,function(n,e){return{k:n,v:e}})}(e),i=S(o,function(n,e){return[e].concat(ug(t,r,o,e))});return S(t,function(n){return Mt(i,n).getOr([n])})}function $d(n,e,t,r){return Mt(e.routes,r.start).bind(function(n){return Mt(n,r.destination)})}function Wd(t,r,n){(function(e,t,r){return dg(e,t).bind(function(n){return lg(e,t,r,n)})})(t,r,n).each(function(n){var e=n.transition;ao(t.element(),e.transitionClass),eo(t.element(),r.destinationAttr)})}function Xd(n,e,t,r){Wd(n,e,t),no(n.element(),e.stateAttr)&&Zr(n.element(),e.stateAttr)!==r&&e.onFinish(n,r),Jr(n.element(),e.stateAttr,r)}function qd(n){return Mt(n,"format").getOr(n.title)}function Yd(n){return Mn(n,"items")?function(n){var e=wt(Tn(n,["items"]),{menu:!0}),t=Sg(n.items);return{item:e,menus:wt(t.menus,En(n.title,t.items)),expansions:wt(t.expansions,En(n.title,n.title))}}(n):{item:n,menus:{},expansions:{}}}function Kd(n){var e=n.replace(/\|/g," ").trim();return 0<e.length?e.split(/\s+/):[]}function Jd(n){var e=n.toolbar!==undefined?n.toolbar:Eg;return ue(e)?Cg(e):Kd(e)}function Qd(n){function e(){n.stopPropagation()}function t(){n.preventDefault()}var r=Qe.fromDom(n.target),o=i(t,e);return function(n,e,t,r,o,i,u){return{target:b(n),x:b(e),y:b(t),stop:r,prevent:o,kill:i,raw:b(u)}}(r,n.clientX,n.clientY,e,t,o,n)}function Zd(n,e,t,r,o){var i=function(e,t){return function(n){e(n)&&t(Qd(n))}}(t,r);return n.dom().addEventListener(e,i,o),{unbind:d(Ig,n,e,i,o)}}function nm(n,e,t){return function(n,e,t,r){return Zd(n,e,t,r,!1)}(n,e,Rg,t)}function em(n,e,t){return function(n,e,t,r){return Zd(n,e,t,r,!0)}(n,e,Rg,t)}function tm(n){var e=n.matchMedia("(orientation: portrait)").matches;return{isPortrait:b(e)}}var rm,om,im=function(n){var e=Qe.fromDom(n.selection.getStart());return nu(e,"a")},um={getInfo:function(n){return im(n).fold(function(){return function(n){return{url:"",text:n.selection.getContent({format:"text"}),title:"",target:"",link:Fn.none()}}(n)},function(n){return function(n){var e=ed(n),t=Zr(n,"href"),r=Zr(n,"title"),o=Zr(n,"target");return{url:rd(t),text:e!==t?rd(e):"",title:rd(r),target:rd(o),link:Fn.some(n)}}(n)})},applyInfo:function(e,o){o.url.toOption().filter(td).fold(function(){!function(e,n){n.link.bind(y).each(function(n){e.execCommand("unlink")})}(e,o)},function(t){var r=function(n,e){var t={};return t.href=n,e.title.toOption().filter(td).each(function(n){t.title=n}),e.target.toOption().filter(td).each(function(n){t.target=n}),t}(t,o);o.link.bind(y).fold(function(){var n=o.text.toOption().filter(td).getOr(t);e.insertContent(e.dom.createHTML("a",r,e.dom.encode(n)))},function(e){var n=od(e,t,o);Qr(e,r),n.each(function(n){!function(n,e){n.dom().textContent=e}(e,n)})})})},query:im},cm=L(),am=function(n,e){(cm.os.isAndroid()?id:t)(e,n)},fm=function(n,e){return{key:n,value:{config:{},me:function(n,e){var t=jr(e);return Yr({fields:[Kt("enabled")],name:n,active:{events:b(t)}})}(n,e),configAsRaw:b({}),initialConfig:{},state:Fo}}},sm=/* */Object.freeze({getCurrent:function(n,e,t){return e.find(n)}}),lm=[Kt("find")],dm=Yr({fields:lm,name:"composing",apis:sm}),mm=vf({name:"Container",factory:function(n){var e=n.dom,t=e.attributes,r=u(e,["attributes"]);return{uid:n.uid,dom:x({tag:"div",attributes:x({role:"presentation"},t)},r),components:n.components,behaviours:pc(n.containerBehaviours),events:n.events,domModification:n.domModification,eventOrder:n.eventOrder}},configFields:[rr("components",[]),gc("containerBehaviours",[]),rr("events",{}),rr("domModification",{}),rr("eventOrder",{})]}),gm=vf({name:"DataField",factory:function(t){return{uid:t.uid,dom:t.dom,behaviours:_a(t.dataBehaviours,[sl.config({store:{mode:"memory",initialValue:t.getInitialValue()}}),dm.config({find:Fn.some})]),events:jr([Co(function(n,e){sl.setValue(n,t.getInitialValue())})])}},configFields:[Kt("uid"),Kt("dom"),Kt("getInitialValue"),ja("dataBehaviours",[sl,dm])]}),pm=b([Zt("data"),rr("inputAttributes",{}),rr("inputStyles",{}),rr("tag","input"),rr("inputClasses",[]),Jo("onSetValue"),rr("styles",{}),rr("eventOrder",{}),gc("inputBehaviours",[sl,Gi]),rr("selectOnFocus",!0)]),hm=vf({name:"Input",configFields:pm(),factory:function(n,e){return{uid:n.uid,dom:function(n){return{tag:n.tag,attributes:x({type:"text"},n.inputAttributes),styles:n.inputStyles,classes:n.inputClasses}}(n),components:[],behaviours:ad(n),eventOrder:n.eventOrder}}}),vm=/* */Object.freeze({exhibit:function(n,e){return $r({attributes:Cn([{key:e.tabAttr,value:"true"}])})}}),ym=[rr("tabAttr","data-alloy-tabstop")],bm=Yr({fields:ym,name:"tabstopping",active:vm}),xm=tinymce.util.Tools.resolve("tinymce.util.I18n"),wm=["input","button","textarea","select"],Sm=function(e,n,t){n.disableClass.each(function(n){uo(e.element(),n)}),(ld(e,n)?dd:gd)(e),n.onDisabled(e)},Om=/* */Object.freeze({enable:hd,disable:Sm,isDisabled:vd,onLoad:sd,set:function(n,e,t,r){(r?Sm:hd)(n,e,t)}}),Tm=/* */Object.freeze({exhibit:function(n,e,t){return $r({classes:e.disabled?e.disableClass.map(_).getOr([]):[]})},events:function(t,n){return jr([_r(He(),function(n,e){return vd(n,t)}),Wr(t,n,sd)])}}),km=[rr("disabled",!1),rr("useNative",!0),Zt("disableClass"),Jo("onDisabled"),Jo("onEnabled")],Em=Yr({fields:km,name:"disabling",active:Tm,apis:Om}),Cm=[gc("formBehaviours",[sl])],Dm=function(r,n,e){return{uid:r.uid,dom:r.dom,components:n,behaviours:hc(r.formBehaviours,[sl.config({store:{mode:"manual",getValue:function(n){var e=function(n,e){var t=n.getSystem();return S(e.partUids,function(n,e){return b(t.getByUid(n))})}(n,r);return S(e,function(n,e){return n().bind(function(n){return function(n,e){return n.fold(function(){return yt.error(e)},yt.value)}(dm.getCurrent(n),"missing current")}).map(sl.getValue)})},setValue:function(t,n){Nn(n,function(e,n){Ec(t,r,n).each(function(n){dm.getCurrent(n).each(function(n){sl.setValue(n,e)})})})}}})]),apis:{getField:function(n,e){return Ec(n,r,e).bind(dm.getCurrent)}}}},Mm=(Rc(function(n,e,t){return n.getField(e,t)}),function(n){var t,e=(t=[],{field:function(n,e){return t.push(n),function(n,e,t){return{uiType:Ua(),owner:n,name:e,config:t,validated:{}}}("form",yd(n),e)},record:function(){return t}}),r=n(e),o=e.record(),i=me(o,function(n){return rf({name:n,pname:yd(n)})});return Bc("form",Cm,i,Dm,r)}),Im=function(n){return{xValue:n,points:[]}},Rm=function(n,e){if(e===n.xValue)return n;var t=0<e-n.xValue?1:-1,r={direction:t,xValue:e};return{xValue:e,points:(0===n.points.length?[]:n.points[n.points.length-1].direction===t?n.points.slice(0,n.points.length-1):n.points).concat([r])}},Am=function(n){if(0===n.points.length)return 0;var e=n.points[0].direction,t=n.points[n.points.length-1].direction;return-1===e&&-1===t?-1:1===e&&1===t?1:0},Fm=X(function(t,r){return[{label:"the link group",items:[wd({fields:[fd("url","Type or paste URL"),fd("text","Link text"),fd("title","Link title"),fd("target","Link target"),function(n){return{name:n,spec:gm.sketch({dom:{tag:"span",styles:{display:"none"}},getInitialValue:function(){return Fn.none()}})}}("link")],maxFieldIndex:["url","text","title","target"].length-1,getInitialValue:function(){return Fn.some(um.getInfo(r))},onExecute:function(n){var e=sl.getValue(n);um.applyInfo(r,e),t.restoreToolbar(),r.focus()}})]}]}),Bm=[{title:"Headings",items:[{title:"Heading 1",format:"h1"},{title:"Heading 2",format:"h2"},{title:"Heading 3",format:"h3"},{title:"Heading 4",format:"h4"},{title:"Heading 5",format:"h5"},{title:"Heading 6",format:"h6"}]},{title:"Inline",items:[{title:"Bold",icon:"bold",format:"bold"},{title:"Italic",icon:"italic",format:"italic"},{title:"Underline",icon:"underline",format:"underline"},{title:"Strikethrough",icon:"strikethrough",format:"strikethrough"},{title:"Superscript",icon:"superscript",format:"superscript"},{title:"Subscript",icon:"subscript",format:"subscript"},{title:"Code",icon:"code",format:"code"}]},{title:"Blocks",items:[{title:"Paragraph",format:"p"},{title:"Blockquote",format:"blockquote"},{title:"Div",format:"div"},{title:"Pre",format:"pre"}]},{title:"Alignment",items:[{title:"Left",icon:"alignleft",format:"alignleft"},{title:"Center",icon:"aligncenter",format:"aligncenter"},{title:"Right",icon:"alignright",format:"alignright"},{title:"Justify",icon:"alignjustify",format:"alignjustify"}]}],Vm=jr([(rm=Ne(),om=function(n,e){var t=e.event().originator(),r=e.event().target();return!function(n,e,t){return cn(e,n.element())&&!cn(e,t)}(n,t,r)||(l.console.warn(Ne()+" did not get interpreted by the desired target. \nOriginator: "+xo(t)+"\nTarget: "+xo(r)+"\nCheck the "+Ne()+" event handlers"),!1)},{key:rm,value:Vr({can:om})})]),Nm=/* */Object.freeze({events:Vm}),jm=y,_m=Sd(),Pm=function(n,e){return function(n,e){return{cHandler:n,purpose:b(e)}}(d.apply(undefined,[n.handler].concat(e)),n.purpose())},Hm=function(n,i){var e=_n(n,function(r,o){return(1===r.length?yt.value(r[0].handler()):Dd(r,i,o)).map(function(n){var e=Cd(n),t=1<r.length?D(i[o],function(e){return E(r,function(n){return n.name()===e})}).join(" > "):r[0].name();return En(o,function(n,e){return{handler:n,purpose:b(e)}}(e,t))})});return Dn(e,{})},zm=function(n){var e=qt("external.component",Ht([Kt("element"),Zt("uid")]),n),t=Pn(Sd());e.uid.each(function(n){gf(e.element,n)});var r={getSystem:t.get,config:Fn.none,hasConfigured:b(!1),connect:function(n){t.set(n)},disconnect:function(){t.set(Sd(function(){return r}))},getApis:function(){return{}},element:b(e.element),spec:b(n),readState:b("No state"),syncComponents:w,components:b([]),events:b({})};return Ic(r)},Lm=Ac,Gm=function(e){return function(n){return Mt(n,ff)}(e).fold(function(){var n=e.hasOwnProperty("uid")?e:x({uid:Lm("")},e);return jd(n).getOrDie()},function(n){return n})},Um=Ic,$m="alloy.item-hover",Wm="alloy.item-focus",Xm=b($m),qm=b(Wm),Ym=[Kt("data"),Kt("components"),Kt("dom"),rr("hasSubmenu",!1),Zt("toggling"),ja("itemBehaviours",[Bi,Gi,Na,sl]),rr("ignoreFocus",!1),rr("domModification",{}),ei("builder",function(n){return{dom:n.dom,domModification:x(x({},n.domModification),{attributes:x(x(x({role:n.toggling.isSome()?"menuitemcheckbox":"menuitem"},n.domModification.attributes),{"aria-haspopup":n.hasSubmenu}),n.hasSubmenu?{"aria-expanded":!1}:{})}),behaviours:_a(n.itemBehaviours,[n.toggling.fold(Bi.revoke,function(n){return Bi.config(x({aria:{mode:"checked"}},n))}),Gi.config({ignore:n.ignoreFocus,stopMousedown:n.ignoreFocus,onFocus:function(n){Hd(n)}}),Na.config({mode:"execution"}),sl.config({store:{mode:"memory",initialValue:n.data}}),fm("item-type-events",function a(){for(var n=0,e=0,t=arguments.length;e<t;e++)n+=arguments[e].length;var r=Array(n),o=0;for(e=0;e<t;e++)for(var i=arguments[e],u=0,c=i.length;u<c;u++,o++)r[o]=i[u];return r}(fi(),[Pr(Ce(),Pd),Pr(ze(),Gi.focus)]))]),components:n.components,eventOrder:n.eventOrder}}),rr("eventOrder",{})],Km=[Kt("dom"),Kt("components"),ei("builder",function(n){return{dom:n.dom,components:n.components,events:jr([function(n){return Pr(n,function(n,e){e.stop()})}(ze())])}})],Jm=b([rf({name:"widget",overrides:function(e){return{behaviours:Vo([sl.config({store:{mode:"manual",getValue:function(n){return e.data},setValue:function(){}}})])}}})]),Qm=[Kt("uid"),Kt("data"),Kt("components"),Kt("dom"),rr("autofocus",!1),rr("ignoreFocus",!1),ja("widgetBehaviours",[sl,Gi,Na]),rr("domModification",{}),Mc(Jm()),ei("builder",function(t){function r(n){return Ec(n,t,"widget").map(function(n){return Na.focusIn(n),n})}function n(n,e){return Ru(e.event().target())||t.autofocus&&e.setSource(n.element()),Fn.none()}var e=Tc(0,t,Jm()),o=kc("item-widget",t,e.internals());return{dom:t.dom,components:o,domModification:t.domModification,events:jr([Io(function(n,e){r(n).each(function(n){e.stop()})}),Pr(Ce(),Pd),Pr(ze(),function(n,e){t.autofocus?r(n):Gi.focus(n)})]),behaviours:_a(t.widgetBehaviours,[sl.config({store:{mode:"memory",initialValue:t.data}}),Gi.config({ignore:t.ignoreFocus,onFocus:function(n){Hd(n)}}),Na.config({mode:"special",focusIn:t.autofocus?function(n){r(n)}:_o(),onLeft:n,onRight:n,onEscape:function(n,e){return Gi.isFocused(n)||t.autofocus?(t.autofocus&&e.setSource(n.element()),Fn.none()):(Gi.focus(n),Fn.some(!0))}})])}})],Zm=Yt("type",{widget:Qm,item:Ym,separator:Km}),ng=b([uf({factory:{sketch:function(n){var e=qt("menu.spec item",Zm,n);return e.builder(e)}},name:"items",unit:"item",defaults:function(n,e){return e.hasOwnProperty("uid")?e:x(x({},e),{uid:Ac("item")})},overrides:function(n,e){return{type:e.type,ignoreFocus:n.fakeFocus,domModification:{classes:[n.markers.item]}}}})]),eg=b([Kt("value"),Kt("items"),Kt("dom"),Kt("components"),rr("eventOrder",{}),gc("menuBehaviours",[Du,sl,dm,Na]),or("movement",{mode:"menu",moveOnTab:!0},Yt("mode",{grid:[Ei(),ei("config",function(n,e){return{mode:"flatgrid",selector:"."+n.markers.item,initSize:{numColumns:e.initSize.numColumns,numRows:e.initSize.numRows},focusManager:n.focusManager}})],matrix:[ei("config",function(n,e){return{mode:"matrix",selectors:{row:e.rowSelector,cell:"."+n.markers.item},focusManager:n.focusManager}}),Kt("rowSelector")],menu:[rr("moveOnTab",!0),ei("config",function(n,e){return{mode:"menu",selector:"."+n.markers.item,moveOnTab:e.moveOnTab,focusManager:n.focusManager}})]})),Jt("markers",Ti()),rr("fakeFocus",!1),rr("focusManager",hu()),Jo("onHighlight")]),tg=b("alloy.menu-focus"),rg=yf({name:"Menu",configFields:eg(),partFields:ng(),factory:function(n,e,t,r){return{uid:n.uid,dom:n.dom,markers:n.markers,behaviours:hc(n.menuBehaviours,[Du.config({highlightClass:n.markers.selectedItem,itemClass:n.markers.item,onHighlight:n.onHighlight}),sl.config({store:{mode:"memory",initialValue:n.value}}),dm.config({find:Fn.some}),Na.config(n.movement.config(n,n.movement))]),events:jr([Pr(qm(),function(e,t){var n=t.event();e.getSystem().getByDom(n.target()).each(function(n){Du.highlight(e,n),t.stop(),U(e,tg(),{menu:e,item:n})})}),Pr(Xm(),function(n,e){var t=e.event().item();Du.highlight(n,t)})]),components:e,eventOrder:n.eventOrder,domModification:{attributes:{role:"menu"}}}}}),og=function(n,e){return n.components()},ig=Yr({fields:[],name:"replacing",apis:/* */Object.freeze({append:function(n,e,t,r){zd(n,0,st,r)},prepend:function(n,e,t,r){zd(n,0,mn,r)},remove:Ld,replaceAt:Gd,replaceBy:function(e,n,t,r,o){var i=og(e);return A(i,r).bind(function(n){return Gd(e,0,0,n,o)})},set:function(e,n,t,r){!function(n,t){var r=an(t),e=go(r).bind(function(e){function n(n){return cn(e,n)}return n(t)?Fn.some(t):Br(t,n)}),o=n(t);e.each(function(e){go(r).filter(function(n){return cn(n,e)}).fold(function(){lo(e)},w)})}(function(){var n=me(r,e.getSystem().build);vn(e,n)},e.element())},contents:og})}),ug=function(t,r,o,n){return Mt(o,n).bind(function(n){return Mt(t,n).bind(function(n){var e=ug(t,r,o,n);return Fn.some([n].concat(e))})}).getOr([])},cg=function(n){return"prepared"===n.type?Fn.some(n.menu):Fn.none()},ag={init:function(){function o(t){return function(n,e){for(var t=Bn(n),r=0,o=t.length;r<o;r++){var i=t[r],u=n[i];if(e(u,i,n))return Fn.some(u)}return Fn.none()}(i.get(),function(n,e){return n===t})}var i=Pn({}),u=Pn({}),c=Pn({}),a=Pn(Fn.none()),f=Pn({}),s=function(n){return e(n).bind(cg)},e=function(n){return Mt(u.get(),n)},t=function(n){return Mt(i.get(),n)};return{setMenuBuilt:function(n,e){var t;u.set(x(x({},u.get()),((t={})[n]={type:"prepared",menu:e},t)))},setContents:function(n,e,t,r){a.set(Fn.some(n)),i.set(t),u.set(e),f.set(r);var o=Ud(r,t);c.set(o)},expand:function(t){return Mt(i.get(),t).map(function(n){var e=Mt(c.get(),t).getOr([]);return[n].concat(e)})},refresh:function(n){return Mt(c.get(),n)},collapse:function(n){return Mt(c.get(),n).bind(function(n){return 1<n.length?Fn.some(n.slice(1)):Fn.none()})},lookupMenu:e,lookupItem:t,otherMenus:function(n){var e=f.get();return j(Bn(e),n)},getPrimary:function(){return a.get().bind(s)},getMenus:function(){return u.get()},clear:function(){i.set({}),u.set({}),c.set({}),a.set(Fn.none())},isClear:function(){return a.get().isNone()},getTriggeringPath:function(n,r){var e=D(t(n).toArray(),function(n){return s(n).isSome()});return Mt(c.get(),n).bind(function(n){var t=N(e.concat(n));return function(n){for(var e=[],t=0;t<n.length;t++){var r=n[t];if(!r.isSome())return Fn.none();e.push(r.getOrDie())}return Fn.some(e)}(B(t,function(n,e){return function(n,t,r){return s(n).bind(function(e){return o(n).bind(function(n){return t(n).map(function(n){return{triggeredMenu:e,triggeringItem:n,triggeringPath:r}})})})}(n,r,t.slice(0,e+1)).fold(function(){return a.get().is(n)?[]:[Fn.none()]},function(n){return[Fn.some(n)]})}))})}}},extractPreparedMenu:cg},fg=b("collapse-item"),sg=vf({name:"TieredMenu",configFields:[ni("onExecute"),ni("onEscape"),Zo("onOpenMenu"),Zo("onOpenSubmenu"),Zo("onRepositionMenu"),Jo("onCollapseMenu"),rr("highlightImmediately",!0),Qt("data",[Kt("primary"),Kt("menus"),Kt("expansions")]),rr("fakeFocus",!1),Jo("onHighlight"),Jo("onHover"),Qt("markers",[Kt("backgroundMenu")].concat(Si()).concat(Oi())),Kt("dom"),rr("navigateOnHover",!0),rr("stayInDom",!1),gc("tmenuBehaviours",[Na,Du,dm,ig]),rr("eventOrder",{})],apis:{collapseMenu:function(n,e){n.collapseMenu(e)},highlightPrimary:function(n,e){n.highlightPrimary(e)},repositionMenus:function(n,e){n.repositionMenus(e)}},factory:function(c,n){function r(r,o,n){return S(n,function(n,e){function t(){return rg.sketch(x(x({dom:n.dom},n),{value:e,items:n.items,markers:c.markers,fakeFocus:c.fakeFocus,onHighlight:c.onHighlight,focusManager:c.fakeFocus?function(){function o(n){return Du.getHighlighted(n).map(function(n){return n.element()})}return{get:o,set:function(e,n){var t=o(e);e.getSystem().getByDom(n).fold(w,function(n){Du.highlight(e,n)});var r=o(e);pu(e,t,r)}}}():hu()}))}return e===o?{type:"prepared",menu:r.getSystem().build(t())}:{type:"notbuilt",nbMenu:t}})}function a(n){return sl.getValue(n).value}function u(e,n){Du.highlight(e,n),Du.getHighlighted(n).orThunk(function(){return Du.getFirst(n)}).each(function(n){W(e,n.element(),ze())})}function f(e,n){return wo(me(n,function(n){return e.lookupMenu(n).bind(function(n){return"prepared"===n.type?Fn.some(n.menu):Fn.none()})}))}function s(e,n,t){var r=f(n,n.otherMenus(t));C(r,function(n){Rd(n.element(),[c.markers.backgroundMenu]),c.stayInDom||ig.remove(e,n)})}function l(n,r){var e=function(r){return o.get().getOrThunk(function(){var t={},n=Ki(r.element(),"."+c.markers.item),e=D(n,function(n){return"true"===Zr(n,"aria-haspopup")});return C(e,function(n){r.getSystem().getByDom(n).each(function(n){var e=a(n);t[e]=n})}),o.set(Fn.some(t)),t})}(n);Nn(e,function(n,e){var t=k(r,e);Jr(n.element(),"aria-expanded",t)})}function d(r,o,i){return Fn.from(i[0]).bind(function(n){return o.lookupMenu(n).bind(function(n){if("notbuilt"===n.type)return Fn.none();var e=n.menu,t=f(o,i.slice(1));return C(t,function(n){uo(n.element(),c.markers.backgroundMenu)}),K(e.element())||ig.append(r,Um(e)),Rd(e.element(),[c.markers.backgroundMenu]),u(r,e),s(r,o,i),Fn.some(e)})})}var m,e,o=Pn(Fn.none()),g=ag.init(),i=function(n){return S(c.data.menus,function(n,e){return B(n.items,function(n){return"separator"===n.type?[]:[n.data.value]})})};(e=m=m||{})[e.HighlightSubmenu=0]="HighlightSubmenu",e[e.HighlightParent=1]="HighlightParent";function p(o,i,u){void 0===u&&(u=m.HighlightSubmenu);var n=a(i);return g.expand(n).bind(function(r){return l(o,r),Fn.from(r[0]).bind(function(t){return g.lookupMenu(t).bind(function(n){var e=function(n,e,t){if("notbuilt"!==t.type)return t.menu;var r=n.getSystem().build(t.nbMenu());return g.setMenuBuilt(e,r),r}(o,t,n);return K(e.element())||ig.append(o,Um(e)),c.onOpenSubmenu(o,i,e,N(r)),u===m.HighlightSubmenu?(Du.highlightFirst(e),d(o,g,r)):(Du.dehighlightAll(e),Fn.some(i))})})})}function h(e,t){var n=a(t);return g.collapse(n).bind(function(n){return l(e,n),d(e,g,n).map(function(n){return c.onCollapseMenu(e,t,n),n})})}function t(t){return function(e,n){return nu(n.getSource(),"."+c.markers.item).bind(function(n){return e.getSystem().getByDom(n).toOption().bind(function(n){return t(e,n).map(function(){return!0})})})}}function v(n){return Du.getHighlighted(n).bind(Du.getHighlighted)}var y=jr([Pr(tg(),function(t,r){var n=r.event().item();g.lookupItem(a(n)).each(function(){var n=r.event().menu();Du.highlight(t,n);var e=a(r.event().item());g.refresh(e).each(function(n){return s(t,g,n)})})}),Io(function(e,n){var t=n.event().target();e.getSystem().getByDom(t).each(function(n){0===a(n).indexOf("collapse-item")&&h(e,n),p(e,n,m.HighlightSubmenu).fold(function(){c.onExecute(e,n)},function(){})})}),Co(function(e,n){(function(n){var e=r(n,c.data.primary,c.data.menus),t=i();return g.setContents(c.data.primary,e,c.data.expansions,t),g.getPrimary()})(e).each(function(n){ig.append(e,Um(n)),c.onOpenMenu(e,n),c.highlightImmediately&&u(e,n)})})].concat(c.navigateOnHover?[Pr(Xm(),function(n,e){var t=e.event().item();!function(e,n){var t=a(n);g.refresh(t).bind(function(n){return l(e,n),d(e,g,n)})}(n,t),p(n,t,m.HighlightParent),c.onHover(n,t)})]:[])),b={collapseMenu:function(e){v(e).each(function(n){h(e,n)})},highlightPrimary:function(e){g.getPrimary().each(function(n){u(e,n)})},repositionMenus:function(r){g.getPrimary().bind(function(e){return v(r).bind(function(n){var e=a(n),t=function(n){return _n(n,function(n){return n})}(g.getMenus()),r=wo(me(t,ag.extractPreparedMenu));return g.getTriggeringPath(e,function(n){return function(n,e,t){return So(e,function(n){if(!n.getSystem().isConnected())return Fn.none();var e=Du.getCandidates(n);return R(e,function(n){return a(n)===t})})}(0,r,n)})}).map(function(n){return{primary:e,triggeringPath:n}})}).fold(function(){(function(n){return Fn.from(n.components()[0]).filter(function(n){return"menu"===Zr(n.element(),"role")})})(r).each(function(n){c.onRepositionMenu(r,n,[])})},function(n){var e=n.primary,t=n.triggeringPath;c.onRepositionMenu(r,e,t)})}};return{uid:c.uid,dom:c.dom,markers:c.markers,behaviours:hc(c.tmenuBehaviours,[Na.config({mode:"special",onRight:t(function(n,e){return Ru(e.element())?Fn.none():p(n,e,m.HighlightSubmenu)}),onLeft:t(function(n,e){return Ru(e.element())?Fn.none():h(n,e)}),onEscape:t(function(n,e){return h(n,e).orThunk(function(){return c.onEscape(n,e).map(function(){return n})})}),focusIn:function(e,n){g.getPrimary().each(function(n){W(e,n.element(),ze())})}}),Du.config({highlightClass:c.markers.selectedMenu,itemClass:c.markers.menu}),dm.config({find:function(n){return Du.getHighlighted(n)}}),ig.config({})]),eventOrder:c.eventOrder,apis:b,events:y}},extraApis:{tieredData:function(n,e,t){return{primary:n,menus:e,expansions:t}},singleData:function(n,e){return{primary:n,menus:En(n,e),expansions:{}}},collapseItem:function(n){return{value:bc(fg()),meta:{text:n}}}}}),lg=function(n,e,t,r){return $d(0,e,0,r).bind(function(e){return e.transition.map(function(n){return{transition:n,route:e}})})},dg=function(n,e,t){var r=n.element();return no(r,e.destinationAttr)?Fn.some({start:Zr(n.element(),e.stateAttr),destination:Zr(n.element(),e.destinationAttr)}):Fn.none()},mg=/* */Object.freeze({findRoute:$d,disableTransition:Wd,getCurrentRoute:dg,jumpTo:Xd,progressTo:function(t,r,o,i){!function(n,e){no(n.element(),e.destinationAttr)&&(Jr(n.element(),e.stateAttr,Zr(n.element(),e.destinationAttr)),eo(n.element(),e.destinationAttr))}(t,r);var n=function(n,e,t,r){return{start:Zr(n.element(),e.stateAttr),destination:r}}(t,r,0,i);lg(t,r,o,n).fold(function(){Xd(t,r,o,i)},function(n){Wd(t,r,o);var e=n.transition;uo(t.element(),e.transitionClass),Jr(t.element(),r.destinationAttr,i)})},getState:function(n,e,t){var r=n.element();return no(r,e.stateAttr)?Fn.some(Zr(r,e.stateAttr)):Fn.none()}}),gg=/* */Object.freeze({events:function(o,i){return jr([Pr(Fe(),function(t,n){var r=n.event().raw();dg(t,o).each(function(e){$d(0,o,0,e).each(function(n){n.transition.each(function(n){r.propertyName===n.property&&(Xd(t,o,i,e.destination),o.onTransition(t,e))})})})}),Co(function(n,e){Xd(n,o,i,o.initialState)})])}}),pg=[rr("destinationAttr","data-transitioning-destination"),rr("stateAttr","data-transitioning-state"),Kt("initialState"),Jo("onTransition"),Jo("onFinish"),Jt("routes",$t(yt.value,$t(yt.value,Ht([tr("transition",[Kt("property"),Kt("transitionClass")])]))))],hg=Yr({fields:pg,name:"transitioning",active:gg,apis:mg,extra:{createRoutes:function(n){var r={};return Nn(n,function(n,e){var t=e.split("<->");r[t[0]]=En(t[1],n),r[t[1]]=En(t[0],n)}),r},createBistate:function(n,e,t){return Cn([{key:n,value:En(e,t)},{key:e,value:En(n,t)}])},createTristate:function(n,e,t,r){return Cn([{key:n,value:Cn([{key:e,value:r},{key:t,value:r}])},{key:e,value:Cn([{key:n,value:r},{key:t,value:r}])},{key:t,value:Cn([{key:n,value:r},{key:e,value:r}])}])}}}),vg=Pi.resolve("scrollable"),yg={register:function(n){uo(n,vg)},deregister:function(n){ao(n,vg)},scrollable:b(vg)},bg=function(n,e,t,r,o){return{data:{value:n,text:e},type:"item",dom:{tag:"div",classes:o?[Pi.resolve("styles-item-is-menu")]:[]},toggling:{toggleOnExecute:!1,toggleClass:Pi.resolve("format-matches"),selected:t},itemBehaviours:Vo(o?[]:[Vi(n,function(n,e){(e?Bi.on:Bi.off)(n)})]),components:[{dom:{tag:"div",attributes:{style:r},innerHtml:e}}]}},xg=function(n,e,t,r){return{value:n,dom:{tag:"div"},components:[Gs.sketch({dom:{tag:"div",classes:[Pi.resolve("styles-collapser")]},components:r?[{dom:{tag:"span",classes:[Pi.resolve("styles-collapse-icon")]}},_d(n)]:[_d(n)],action:function(n){if(r){var e=t().get(n);sg.collapseMenu(e)}}}),{dom:{tag:"div",classes:[Pi.resolve("styles-menu-items-container")]},components:[rg.parts().items({})],behaviours:Vo([fm("adhoc-scrollable-menu",[Co(function(n,e){Ui(n.element(),"overflow-y","auto"),Ui(n.element(),"-webkit-overflow-scrolling","touch"),yg.register(n.element())}),Do(function(n){hi(n.element(),"overflow-y"),hi(n.element(),"-webkit-overflow-scrolling"),yg.deregister(n.element())})])])}],items:e,menuBehaviours:Vo([hg.config({initialState:"after",routes:hg.createTristate("before","current","after",{transition:{property:"transform",transitionClass:"transitioning"}})})])}},wg=function(r){var n=function(r,o){var n=xg("Styles",[].concat(me(r.items,function(n){return bg(qd(n),n.title,n.isSelected(),n.getPreview(),Mn(r.expansions,qd(n)))})),o,!1),e=S(r.menus,function(n,e){var t=me(n,function(n){return bg(qd(n),n.title,n.isSelected!==undefined&&n.isSelected(),n.getPreview!==undefined?n.getPreview():"",Mn(r.expansions,qd(n)))});return xg(e,t,o,!0)}),t=wt(e,En("styles",n));return{tmenu:sg.tieredData("styles",t,r.expansions)}}(r.formats,function(){return e}),e=_s(sg.sketch({dom:{tag:"div",classes:[Pi.resolve("styles-menu")]},components:[],fakeFocus:!0,stayInDom:!0,onExecute:function(n,e){var t=sl.getValue(e);return r.handle(e,t.value),Fn.none()},onEscape:function(){return Fn.none()},onOpenMenu:function(n,e){var t=If(n.element());Mf(e.element(),t),hg.jumpTo(e,"current")},onOpenSubmenu:function(n,e,t){var r=If(n.element()),o=Qi(e.element(),'[role="menu"]').getOrDie("hacky"),i=n.getSystem().getByDom(o).getOrDie();Mf(t.element(),r),hg.progressTo(i,"before"),hg.jumpTo(t,"after"),hg.progressTo(t,"current")},onCollapseMenu:function(n,e,t){var r=Qi(e.element(),'[role="menu"]').getOrDie("hacky"),o=n.getSystem().getByDom(r).getOrDie();hg.progressTo(o,"after"),hg.progressTo(t,"current")},navigateOnHover:!1,highlightImmediately:!0,data:n.tmenu,markers:{backgroundMenu:Pi.resolve("styles-background-menu"),menu:Pi.resolve("styles-menu"),selectedMenu:Pi.resolve("styles-selected-menu"),item:Pi.resolve("styles-item"),selectedItem:Pi.resolve("styles-selected-item")}}));return e.asSpec()},Sg=function(n){return M(n,function(n,e){var t=Yd(e);return{menus:wt(n.menus,t.menus),items:[t.item].concat(n.items),expansions:wt(n.expansions,t.expansions)}},{menus:{},expansions:{},items:[]})},Og={expand:Sg},Tg=function(r,n){function o(n){return function(){return r.formatter.match(n)}}function i(n){return function(){return r.formatter.getCssText(n)}}var e=Mt(n,"style_formats").getOr(Bm),t=function(n){return me(n,function(n){if(Mn(n,"items")){var e=t(n.items);return wt(function(n){return wt(n,{isSelected:b(!1),getPreview:b("")})}(n),{items:e})}return Mn(n,"format")?function(n){return wt(n,{isSelected:o(n.format),getPreview:i(n.format)})}(n):function(n){var e=bc(n.title),t=wt(n,{format:e,isSelected:o(e),getPreview:i(e)});return r.formatter.register(e,t),t}(n)})};return t(e)},kg=function(t,n,r){var e=function(e,n){var t=function(n){return B(n,function(n){return n.items===undefined?!Mn(n,"format")||e.formatter.canApply(n.format)?[n]:[]:0<t(n.items).length?[n]:[]})},r=t(n);return Og.expand(r)}(t,n);return wg({formats:e,handle:function(n,e){t.undoManager.transact(function(){Bi.isOn(n)?t.formatter.remove(e):t.formatter.apply(e)}),r()}})},Eg=["undo","bold","italic","link","image","bullist","styleselect"],Cg=function(n){return B(n,function(n){return ue(n)?Cg(n):Kd(n)})},Dg=function(e,r){function n(n){return function(){return Xs.forToolbarCommand(r,n)}}function t(n){return function(){return Xs.forToolbarStateCommand(r,n)}}function o(n,e,t){return function(){return Xs.forToolbarStateAction(r,n,e,t)}}function i(){return kg(r,h,function(){r.fire("scrollIntoView")})}function u(n,e){return{isSupported:function(){var e=r.ui.registry.getAll().buttons;return n.forall(function(n){return Mn(e,n)})},sketch:e}}var c=n("undo"),a=n("redo"),f=t("bold"),s=t("italic"),l=t("underline"),d=n("removeformat"),m=o("unlink","link",function(){r.execCommand("unlink",null,!1)}),g=o("unordered-list","ul",function(){r.execCommand("InsertUnorderedList",null,!1)}),p=o("ordered-list","ol",function(){r.execCommand("InsertOrderedList",null,!1)}),h=Tg(r,r.settings);return{undo:u(Fn.none(),c),redo:u(Fn.none(),a),bold:u(Fn.none(),f),italic:u(Fn.none(),s),underline:u(Fn.none(),l),removeformat:u(Fn.none(),d),link:u(Fn.none(),function(){return function(e,t){return Xs.forToolbarStateAction(t,"link","link",function(){var n=Fm(e,t);e.setContextToolbar(n),am(t,function(){e.focusToolbar()}),um.query(t).each(function(n){t.selection.select(n.dom())})})}(e,r)}),unlink:u(Fn.none(),m),image:u(Fn.none(),function(){return nd(r)}),bullist:u(Fn.some("bullist"),g),numlist:u(Fn.some("numlist"),p),fontsizeselect:u(Fn.none(),function(){return function(n,e){var t={onChange:function(n){zl.apply(e,n)},getInitialValue:function(){return zl.get(e)}};return Bs(n,"font-size",function(){return js(t)},e)}(e,r)}),forecolor:u(Fn.none(),function(){return jl(e,r)}),styleselect:u(Fn.none(),function(){return Xs.forToolbar("style-formats",function(n){r.fire("toReading"),e.dropup().appear(i,Bi.on,n)},Vo([Bi.config({toggleClass:Pi.resolve("toolbar-button-selected"),toggleOnExecute:!1,aria:{mode:"pressed"}}),Di.config({channels:Cn([ji($o.orientationChanged(),Bi.off),ji($o.dropupDismissed(),Bi.off)])})]),r)})}},Mg=function(n,t){var e=Jd(n),r={};return B(e,function(n){var e=!Mn(r,n)&&Mn(t,n)&&t[n].isSupported()?[t[n].sketch()]:[];return r[n]=!0,e})},Ig=function(n,e,t,r){n.dom().removeEventListener(e,t,r)},Rg=b(!0),Ag=tinymce.util.Tools.resolve("tinymce.util.Delay"),Fg=tm,Bg=function(r,e){var n=Qe.fromDom(r),o=null,t=nm(n,"orientationchange",function(){Ag.clearInterval(o);var n=tm(r);e.onChange(n),i(function(){e.onReady(n)})}),i=function(n){Ag.clearInterval(o);var e=r.innerHeight,t=0;o=Ag.setInterval(function(){e!==r.innerHeight?(Ag.clearInterval(o),n(Fn.some(r.innerHeight))):20<t&&(Ag.clearInterval(o),n(Fn.none())),t++},50)};return{onAdjustment:i,destroy:function(){t.unbind()}}},Vg=function(n){var e=L().os.isiOS(),t=tm(n).isPortrait();return e&&!t?n.screen.height:n.screen.width};function Ng(n){var e=n.raw();return e.touches===undefined||1!==e.touches.length?Fn.none():Fn.some(e.touches[0])}function jg(t){var r=Pn(Fn.none()),o=Pn(!1),i=function n(t,r){var o=null;return{cancel:function(){null!==o&&(l.clearTimeout(o),o=null)},schedule:function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];o=l.setTimeout(function(){t.apply(null,n),o=null},r)}}}(function(n){t.triggerEvent(Ge(),n),o.set(!0)},400),u=Cn([{key:we(),value:function(t){return Ng(t).each(function(n){i.cancel();var e={x:b(n.clientX),y:b(n.clientY),target:t.target};i.schedule(t),o.set(!1),r.set(Fn.some(e))}),Fn.none()}},{key:Se(),value:function(n){return i.cancel(),Ng(n).each(function(e){r.get().each(function(n){!function(n,e){var t=Math.abs(n.clientX-e.x()),r=Math.abs(n.clientY-e.y());return 5<t||5<r}(e,n)||r.set(Fn.none())})}),Fn.none()}},{key:Oe(),value:function(e){i.cancel();return r.get().filter(function(n){return cn(n.target(),e.target())}).map(function(n){return o.get()?(e.prevent(),!1):t.triggerEvent(Le(),e)})}}]);return{fireIfReady:function(e,n){return Mt(u,n).bind(function(n){return n(e)})}}}var _g=function(t){var e=jg({triggerEvent:function(n,e){t.onTapContent(e)}});return{fireTouchstart:function(n){e.fireIfReady(n,"touchstart")},onTouchend:function(){return nm(t.body(),"touchend",function(n){e.fireIfReady(n,"touchend")})},onTouchmove:function(){return nm(t.body(),"touchmove",function(n){e.fireIfReady(n,"touchmove")})}}},Pg=6<=L().os.version.major,Hg=function(r,e,t){function o(n){return!cn(n.start(),n.finish())||n.soffset()!==n.foffset()}function n(){var n=r.doc().dom().hasFocus()&&r.getSelection().exists(o);t.getByDom(e).each(!0===(n||go(u).filter(function(n){return"input"===q(n)}).exists(function(n){return n.dom().selectionStart!==n.dom().selectionEnd}))?Bi.on:Bi.off)}var i=_g(r),u=an(e),c=[nm(r.body(),"touchstart",function(n){r.onTouchContent(),i.fireTouchstart(n)}),i.onTouchmove(),i.onTouchend(),nm(e,"touchstart",function(n){r.onTouchToolstrip()}),r.onToReading(function(){mo(r.body())}),r.onToEditing(w),r.onScrollToCursor(function(n){n.preventDefault(),r.getCursorBox().each(function(n){var e=r.win(),t=n.top()>e.innerHeight||n.bottom()>e.innerHeight?n.bottom()-e.innerHeight+50:0;0!=t&&e.scrollTo(e.pageXOffset,e.pageYOffset+t)})})].concat(!0==Pg?[]:[nm(Qe.fromDom(r.win()),"blur",function(){t.getByDom(e).each(Bi.off)}),nm(u,"select",n),nm(r.doc(),"selectionchange",n)]);return{destroy:function(){C(c,function(n){n.unbind()})}}},zg=function(n,e){var t=parseInt(Zr(n,e),10);return isNaN(t)?0:t};function Lg(n){return Dp.getOption(n)}function Gg(n){return function(n){return Lg(n).filter(function(n){return 0!==n.trim().length||-1<n.indexOf("\xa0")}).isSome()}(n)||k(Mp,q(n))}function Ug(n,e,t){var r=n.document.createRange();return function(t,n){n.fold(function(n){t.setStartBefore(n.dom())},function(n,e){t.setStart(n.dom(),e)},function(n){t.setStartAfter(n.dom())})}(r,e),function(t,n){n.fold(function(n){t.setEndBefore(n.dom())},function(n,e){t.setEnd(n.dom(),e)},function(n){t.setEndAfter(n.dom())})}(r,t),r}function $g(n,e,t,r,o){var i=n.document.createRange();return i.setStart(e.dom(),t),i.setEnd(r.dom(),o),i}function Wg(n){return{left:b(n.left),top:b(n.top),right:b(n.right),bottom:b(n.bottom),width:b(n.width),height:b(n.height)}}function Xg(n,e,t){return e(Qe.fromDom(t.startContainer),t.startOffset,Qe.fromDom(t.endContainer),t.endOffset)}function qg(n,e){return function(n,e){var t=e.ltr();return t.collapsed?e.rtl().filter(function(n){return!1===n.collapsed}).map(function(n){return Vp.rtl(Qe.fromDom(n.endContainer),n.endOffset,Qe.fromDom(n.startContainer),n.startOffset)}).getOrThunk(function(){return Xg(0,Vp.ltr,t)}):Xg(0,Vp.ltr,t)}(0,function(o,n){return n.match({domRange:function(n){return{ltr:b(n),rtl:Fn.none}},relative:function(n,e){return{ltr:X(function(){return Ug(o,n,e)}),rtl:X(function(){return Fn.some(Ug(o,e,n))})}},exact:function(n,e,t,r){return{ltr:X(function(){return $g(o,n,e,t,r)}),rtl:X(function(){return Fn.some($g(o,t,r,n,e))})}}})}(n,e))}function Yg(n,e,t){return e>=n.left&&e<=n.right&&t>=n.top&&t<=n.bottom}function Kg(t,r,n,e,o){function i(n){var e=t.dom().createRange();return e.setStart(r.dom(),n),e.collapse(!0),e}var u=function(n){return Dp.get(n)}(r).length,c=function(n,e,t,r,o){if(0===o)return 0;if(e===r)return o-1;for(var i=r,u=1;u<o;u++){var c=n(u),a=Math.abs(e-c.left);if(t<=c.bottom){if(t<c.top||i<a)return u-1;i=a}}return 0}(function(n){return i(n).getBoundingClientRect()},n,e,o.right,u);return i(c)}function Jg(n){return Br(n,Gg)}function Qg(n){return jp(n,Gg)}function Zg(n,e){return e-n.left<n.right-e}function np(n,e,t){var r=n.dom().createRange();return r.selectNode(e.dom()),r.collapse(t),r}function ep(e,n,t){var r=e.dom().createRange();r.selectNode(n.dom());var o=r.getBoundingClientRect(),i=Zg(o,t);return(!0===i?Jg:Qg)(n).map(function(n){return np(e,n,i)})}function tp(n,e,t){var r=e.dom().getBoundingClientRect(),o=Zg(r,t);return Fn.some(np(n,e,o))}function rp(n,e,t,r){var o=n.dom().createRange();o.selectNode(e.dom());var i=o.getBoundingClientRect();return function(n,e,t,r){var o=n.dom().createRange();o.selectNode(e.dom());var i=o.getBoundingClientRect(),u=Math.max(i.left,Math.min(i.right,t)),c=Math.max(i.top,Math.min(i.bottom,r));return Np(n,e,u,c)}(n,e,Math.max(i.left,Math.min(i.right,t)),Math.max(i.top,Math.min(i.bottom,r)))}function op(n,e){var t=q(n);return"input"===t?Ap.after(n):k(["br","img"],t)?0===e?Ap.before(n):Ap.after(n):Ap.on(n,e)}function ip(n,e,t,r){var o=function(n,e,t,r){var o=an(n).dom().createRange();return o.setStart(n.dom(),e),o.setEnd(t.dom(),r),o}(n,e,t,r),i=cn(n,t)&&e===r;return o.collapsed&&!i}function up(n,e,t,r,o){!function(n,e){Fn.from(n.getSelection()).each(function(n){n.removeAllRanges(),n.addRange(e)})}(n,$g(n,e,t,r,o))}function cp(n,e,t,r,o){!function(u,n){qg(u,n).match({ltr:function(n,e,t,r){up(u,n,e,t,r)},rtl:function(n,e,t,r){var o=u.getSelection();if(o.setBaseAndExtent)o.setBaseAndExtent(n.dom(),e,t.dom(),r);else if(o.extend)try{!function(n,e,t,r,o,i){e.collapse(t.dom(),r),e.extend(o.dom(),i)}(0,o,n,e,t,r)}catch(i){up(u,t,r,n,e)}else up(u,t,r,n,e)}})}(n,function(n,e,t,r){var o=op(n,e),i=op(t,r);return Bp.relative(o,i)}(e,t,r,o))}function ap(n){var e=Qe.fromDom(n.anchorNode),t=Qe.fromDom(n.focusNode);return ip(e,n.anchorOffset,t,n.focusOffset)?Fn.some(Ip.create(e,n.anchorOffset,t,n.focusOffset)):function(n){if(0<n.rangeCount){var e=n.getRangeAt(0),t=n.getRangeAt(n.rangeCount-1);return Fn.some(Ip.create(Qe.fromDom(e.startContainer),e.startOffset,Qe.fromDom(t.endContainer),t.endOffset))}return Fn.none()}(n)}function fp(n){return Fn.from(n.getSelection()).filter(function(n){return 0<n.rangeCount}).bind(ap)}function sp(n,e){return function(n){var e=n.getClientRects(),t=0<e.length?e[0]:n.getBoundingClientRect();return 0<t.width||0<t.height?Fn.some(t).map(Wg):Fn.none()}(function(i,n){return qg(i,n).match({ltr:function(n,e,t,r){var o=i.document.createRange();return o.setStart(n.dom(),e),o.setEnd(t.dom(),r),o},rtl:function(n,e,t,r){var o=i.document.createRange();return o.setStart(t.dom(),r),o.setEnd(n.dom(),e),o}})}(n,e))}function lp(n){return{left:n.left,top:n.top,right:n.right,bottom:n.bottom,width:b(2),height:n.height}}function dp(n){return{left:b(n.left),top:b(n.top),right:b(n.right),bottom:b(n.bottom),width:b(n.width),height:b(n.height)}}function mp(t){if(t.collapsed){var r=Qe.fromDom(t.startContainer);return fn(r).bind(function(n){var e=Bp.exact(r,t.startOffset,n,function(n){return"img"===q(n)?1:Lg(n).fold(function(){return ft(n).length},function(n){return n.length})}(n));return sp(t.startContainer.ownerDocument.defaultView,e).map(lp).map(_)}).getOr([])}return me(t.getClientRects(),dp)}function gp(n,e){Jr(n,Hp,e)}function pp(n){return{top:b(n.top()),bottom:b(n.top()+n.height())}}function hp(n,e){var t=function(n){return zg(n,Hp)}(e),r=n.innerHeight;return r<t?Fn.some(t-r):Fn.none()}function vp(n){return Fn.some(Qe.fromDom(n.dom().contentWindow.document.body))}function yp(n){return Fn.some(Qe.fromDom(n.dom().contentWindow.document))}function bp(n){return Fn.from(n.dom().contentWindow)}function xp(n){return bp(n).bind(fp)}function wp(n){return n.getFrame()}function Sp(n,t){return function(e){return e[n].getOrThunk(function(){var n=wp(e);return function(){return t(n)}})()}}function Op(n,e,t,r){return n[t].getOrThunk(function(){return function(n){return nm(e,r,n)}})}function Tp(n){return{left:b(n.left),top:b(n.top),right:b(n.right),bottom:b(n.bottom),width:b(n.width),height:b(n.height)}}function kp(t,r){var o=null;return{cancel:function(){null!==o&&(l.clearTimeout(o),o=null)},throttle:function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];null!==o&&l.clearTimeout(o),o=l.setTimeout(function(){t.apply(null,n),o=null},r)}}}function Ep(n){return"true"===Zr(n,uh)?function(n){return 0<n.dom().scrollLeft||function(n){n.dom().scrollLeft=1;var e=0!==n.dom().scrollLeft;return n.dom().scrollLeft=0,e}(n)}(n):function(n){return 0<n.dom().scrollTop||function(n){n.dom().scrollTop=1;var e=0!==n.dom().scrollTop;return n.dom().scrollTop=0,e}(n)}(n)}var Cp,Dp=function iy(t,r){var e=function(n){return t(n)?Fn.from(n.dom().nodeValue):Fn.none()};return{get:function(n){if(!t(n))throw new Error("Can only get "+r+" value of a "+r+" node");return e(n).getOr("")},getOption:e,set:function(n,e){if(!t(n))throw new Error("Can only set raw "+r+" value of a "+r+" node");n.dom().nodeValue=e}}}(rt,"text"),Mp=["img","br"],Ip={create:J("start","soffset","finish","foffset")},Rp=bt([{before:["element"]},{on:["element","offset"]},{after:["element"]}]),Ap={before:Rp.before,on:Rp.on,after:Rp.after,cata:function(n,e,t,r){return n.fold(e,t,r)},getStart:function(n){return n.fold(y,y,y)}},Fp=bt([{domRange:["rng"]},{relative:["startSitu","finishSitu"]},{exact:["start","soffset","finish","foffset"]}]),Bp={domRange:Fp.domRange,relative:Fp.relative,exact:Fp.exact,exactFromRange:function(n){return Fp.exact(n.start(),n.soffset(),n.finish(),n.foffset())},getWin:function(n){return function(n){return Qe.fromDom(n.dom().ownerDocument.defaultView)}(function(n){return n.match({domRange:function(n){return Qe.fromDom(n.startContainer)},relative:function(n,e){return Ap.getStart(n)},exact:function(n,e,t,r){return n}})}(n))},range:Ip.create},Vp=bt([{ltr:["start","soffset","finish","foffset"]},{rtl:["start","soffset","finish","foffset"]}]),Np=function(n,e,t,r){return rt(e)?function(e,t,r,o){var n=e.dom().createRange();n.selectNode(t.dom());var i=n.getClientRects();return So(i,function(n){return Yg(n,r,o)?Fn.some(n):Fn.none()}).map(function(n){return Kg(e,t,r,o,n)})}(n,e,t,r):function(e,n,t,r){var o=e.dom().createRange(),i=ft(n);return So(i,function(n){return o.selectNode(n.dom()),Yg(o.getBoundingClientRect(),t,r)?Np(e,n,t,r):Fn.none()})}(n,e,t,r)},jp=function(n,i){var u=function(n){for(var e=ft(n),t=e.length-1;0<=t;t--){var r=e[t];if(i(r))return Fn.some(r);var o=u(r);if(o.isSome())return o}return Fn.none()};return u(n)},_p=(document.caretPositionFromPoint||document.caretRangeFromPoint,function(n){var e=n.getSelection();return e!==undefined&&0<e.rangeCount?mp(e.getRangeAt(0)):[]}),Pp=function(n){n.focus();var e=Qe.fromDom(n.document.body);(go().exists(function(n){return k(["input","textarea"],q(n))})?function(n){Ag.setTimeout(function(){n()},0)}:t)(function(){go().each(mo),lo(e)})},Hp="data-"+Pi.resolve("last-outer-height"),zp=function(n,r){var e=Qe.fromDom(r.document.body),t=nm(Qe.fromDom(n),"resize",function(){hp(n,e).each(function(t){(function(n){var e=_p(n);return 0<e.length?Fn.some(e[0]).map(pp):Fn.none()})(r).each(function(n){var e=function(n,e,t){return e.top()>n.innerHeight||e.bottom()>n.innerHeight?Math.min(t,e.bottom()-n.innerHeight+50):0}(r,n,t);0!==e&&r.scrollTo(r.pageXOffset,r.pageYOffset+e)})}),gp(e,n.innerHeight)});gp(e,n.innerHeight);return{toEditing:function(){Pp(r)},destroy:function(){t.unbind()}}},Lp={getBody:Sp("getBody",vp),getDoc:Sp("getDoc",yp),getWin:Sp("getWin",bp),getSelection:Sp("getSelection",xp),getFrame:wp,getActiveApi:function(c){var a=wp(c);return vp(a).bind(function(u){return yp(a).bind(function(i){return bp(a).map(function(o){var n=Qe.fromDom(i.dom().documentElement),e=c.getCursorBox.getOrThunk(function(){return function(){return function(n){return fp(n).map(function(n){return Bp.exact(n.start(),n.soffset(),n.finish(),n.foffset())})}(o).bind(function(n){return sp(o,n).orThunk(function(){return function(n){return fp(n).filter(function(n){return cn(n.start(),n.finish())&&n.soffset()===n.foffset()}).bind(function(n){var e=n.start().dom().getBoundingClientRect();return 0<e.width||0<e.height?Fn.some(e).map(Tp):Fn.none()})}(o)})})}}),t=c.setSelection.getOrThunk(function(){return function(n,e,t,r){cp(o,n,e,t,r)}}),r=c.clearSelection.getOrThunk(function(){return function(){!function(n){n.getSelection().removeAllRanges()}(o)}});return{body:b(u),doc:b(i),win:b(o),html:b(n),getSelection:d(xp,a),setSelection:t,clearSelection:r,frame:b(a),onKeyup:Op(c,i,"onKeyup","keyup"),onNodeChanged:Op(c,i,"onNodeChanged","SelectionChange"),onDomChanged:c.onDomChanged,onScrollToCursor:c.onScrollToCursor,onScrollToElement:c.onScrollToElement,onToReading:c.onToReading,onToEditing:c.onToEditing,onToolbarScrollStart:c.onToolbarScrollStart,onTouchContent:c.onTouchContent,onTapContent:c.onTapContent,onTouchToolstrip:c.onTouchToolstrip,getCursorBox:e}})})})}},Gp="data-ephox-mobile-fullscreen-style",Up="position:absolute!important;",$p="top:0!important;left:0!important;margin:0!important;padding:0!important;width:100%!important;height:100%!important;overflow:visible!important;",Wp=L().os.isAndroid(),Xp=function(n,e){function t(r){return function(n){var e=Zr(n,"style"),t=e===undefined?"no-styles":e.trim();t!==r&&(Jr(n,Gp,t),Jr(n,"style",r))}}var r=function(n,e,t){return qi(n,function(n){return tn(n,e)},t)}(n,"*"),o=B(r,function(n){return function(n,e){return Yi(n,function(n){return tn(n,e)})}(n,"*")}),i=function(n){var e=gi(n,"background-color");return e!==undefined&&""!==e?"background-color:"+e+"!important":"background-color:rgb(255,255,255)!important;"}(e);C(o,t("display:none!important;")),C(r,t(Up+$p+i)),t((!0===Wp?"":Up)+$p+i)(n)},qp=function(){var n=function(n){return on(n)}("["+Gp+"]");C(n,function(n){var e=Zr(n,Gp);"no-styles"!==e?Jr(n,"style",e):eo(n,"style"),eo(n,Gp)})},Yp=function(){var e=Ji("head").getOrDie(),n=Ji('meta[name="viewport"]').getOrThunk(function(){var n=Qe.fromTag("meta");return Jr(n,"name","viewport"),st(e,n),n}),t=Zr(n,"content");return{maximize:function(){Jr(n,"content","width=device-width, initial-scale=1.0, user-scalable=no, maximum-scale=1.0")},restore:function(){t!==undefined&&null!==t&&0<t.length?Jr(n,"content",t):Jr(n,"content","user-scalable=yes")}}},Kp=function(e,n){var t=Yp(),r=bd(),o=bd();return{enter:function(){n.hide(),uo(e.container,Pi.resolve("fullscreen-maximized")),uo(e.container,Pi.resolve("android-maximized")),t.maximize(),uo(e.body,Pi.resolve("android-scroll-reload")),r.set(zp(e.win,Lp.getWin(e.editor).getOrDie("no"))),Lp.getActiveApi(e.editor).each(function(n){Xp(e.container,n.body()),o.set(Hg(n,e.toolstrip,e.alloy))})},exit:function(){t.restore(),n.show(),ao(e.container,Pi.resolve("fullscreen-maximized")),ao(e.container,Pi.resolve("android-maximized")),qp(),ao(e.body,Pi.resolve("android-scroll-reload")),o.clear(),r.clear()}}},Jp=function(n,e){var t=_s(mm.sketch({dom:$s('<div aria-hidden="true" class="${prefix}-mask-tap-icon"></div>'),containerBehaviours:Vo([Bi.config({toggleClass:Pi.resolve("mask-tap-icon-selected"),toggleOnExecute:!1})])})),r=function(t,r){var o=null;return{cancel:function(){null!==o&&(l.clearTimeout(o),o=null)},throttle:function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];null===o&&(o=l.setTimeout(function(){t.apply(null,n),o=null},r))}}}(n,200);return mm.sketch({dom:$s('<div class="${prefix}-disabled-mask"></div>'),components:[mm.sketch({dom:$s('<div class="${prefix}-content-container"></div>'),components:[Gs.sketch({dom:$s('<div class="${prefix}-content-tap-section"></div>'),components:[t.asSpec()],action:function(n){r.throttle()},buttonBehaviours:Vo([Bi.config({toggleClass:Pi.resolve("mask-tap-icon-selected")})])})]})]})},Qp=Or([Qt("editor",[Kt("getFrame"),Zt("getBody"),Zt("getDoc"),Zt("getWin"),Zt("getSelection"),Zt("setSelection"),Zt("clearSelection"),Zt("cursorSaver"),Zt("onKeyup"),Zt("onNodeChanged"),Zt("getCursorBox"),Kt("onDomChanged"),rr("onTouchContent",w),rr("onTapContent",w),rr("onTouchToolstrip",w),rr("onScrollToCursor",b({unbind:w})),rr("onScrollToElement",b({unbind:w})),rr("onToEditing",b({unbind:w})),rr("onToReading",b({unbind:w})),rr("onToolbarScrollStart",y)]),Kt("socket"),Kt("toolstrip"),Kt("dropup"),Kt("toolbar"),Kt("container"),Kt("alloy"),ir("win",function(n){return an(n.socket).dom().defaultView}),ir("body",function(n){return Qe.fromDom(n.socket.dom().ownerDocument.body)}),rr("translate",y),rr("setReadOnly",w),rr("readOnlyOnInit",b(!0))]),Zp=function(n){var e=qt("Getting AndroidWebapp schema",Qp,n);Ui(e.toolstrip,"width","100%");var t=Gm(Jp(function(){e.setReadOnly(e.readOnlyOnInit()),o.enter()},e.translate));e.alloy.add(t);var r={show:function(){e.alloy.add(t)},hide:function(){e.alloy.remove(t)}};st(e.container,t.element());var o=Kp(e,r);return{setReadOnly:e.setReadOnly,refreshStructure:w,enter:o.enter,exit:o.exit,destroy:w}},nh=b([Kt("dom"),rr("shell",!0),gc("toolbarBehaviours",[ig])]),eh=b([of({name:"groups",overrides:function(n){return{behaviours:Vo([ig.config({})])}}})]),th=yf({name:"Toolbar",configFields:nh(),partFields:eh(),factory:function(e,n,t,r){var o=function(n){return e.shell?Fn.some(n):Ec(n,e,"groups")},i=e.shell?{behaviours:[ig.config({})],components:[]}:{behaviours:[],components:n};return{uid:e.uid,dom:e.dom,components:i.components,behaviours:hc(e.toolbarBehaviours,i.behaviours),apis:{setGroups:function(n,e){o(n).fold(function(){throw l.console.error("Toolbar was defined to not be a shell, but no groups container was specified in components"),new Error("Toolbar was defined to not be a shell, but no groups container was specified in components")},function(n){ig.set(n,e)})}},domModification:{attributes:{role:"group"}}}},apis:{setGroups:function(n,e,t){n.setGroups(e,t)}}}),rh=b([Kt("items"),(Cp=["itemSelector"],Qt("markers",me(Cp,Kt))),gc("tgroupBehaviours",[Na])]),oh=b([uf({name:"items",unit:"item"})]),ih=yf({name:"ToolbarGroup",configFields:rh(),partFields:oh(),factory:function(n,e,t,r){return{uid:n.uid,dom:n.dom,components:e,behaviours:hc(n.tgroupBehaviours,[Na.config({mode:"flow",selector:n.markers.itemSelector})]),domModification:{attributes:{role:"toolbar"}}}}}),uh="data-"+Pi.resolve("horizontal-scroll"),ch={exclusive:function(n,e){return nm(n,"touchmove",function(n){nu(n.target(),e).filter(Ep).fold(function(){n.raw().preventDefault()},w)})},markAsHorizontal:function(n){Jr(n,uh,"true")}};function ah(){function e(n){var e=!0===n.scrollable?"${prefix}-toolbar-scrollable-group":"";return{dom:$s('<div aria-label="'+n.label+'" class="${prefix}-toolbar-group '+e+'"></div>'),tgroupBehaviours:Vo([fm("adhoc-scrollable-toolbar",!0===n.scrollable?[Mo(function(n,e){Ui(n.element(),"overflow-x","auto"),ch.markAsHorizontal(n.element()),yg.register(n.element())})]:[])]),components:[mm.sketch({components:[ih.parts().items({})]})],markers:{itemSelector:"."+Pi.resolve("toolbar-group-item")},items:n.items}}function t(){th.setGroups(r,o.get()),Bi.off(r)}var r=Gm(th.sketch({dom:$s('<div class="${prefix}-toolbar"></div>'),components:[th.parts().groups({})],toolbarBehaviours:Vo([Bi.config({toggleClass:Pi.resolve("context-toolbar"),toggleOnExecute:!1,aria:{mode:"none"}}),Na.config({mode:"cyclic"})]),shell:!0})),n=Gm(mm.sketch({dom:{classes:[Pi.resolve("toolstrip")]},components:[Um(r)],containerBehaviours:Vo([Bi.config({toggleClass:Pi.resolve("android-selection-context-toolbar"),toggleOnExecute:!1})])})),o=Pn([]);return{wrapper:b(n),toolbar:b(r),createGroups:function(n){return me(n,i(ih.sketch,e))},setGroups:function(n){o.set(n),t()},setContextToolbar:function(n){Bi.on(r),th.setGroups(r,n)},restoreToolbar:function(){Bi.isOn(r)&&t()},refresh:function(){},focus:function(){Na.focusIn(r)}}}function fh(n,e){ig.append(n,Um(e))}function sh(n,e){ig.remove(n,e)}function lh(e,n){return n.getAnimationRoot.fold(function(){return e.element()},function(n){return n(e)})}function dh(n){return n.dimension.property}function mh(n,e){return n.dimension.getDimension(e)}function gh(n,e){var t=lh(n,e);Rd(t,[e.shrinkingClass,e.growingClass])}function ph(n,e){ao(n.element(),e.openClass),uo(n.element(),e.closedClass),Ui(n.element(),dh(e),"0px"),vi(n.element())}function hh(n,e){ao(n.element(),e.closedClass),uo(n.element(),e.openClass),hi(n.element(),dh(e))}function vh(n,e,t,r){t.setCollapsed(),Ui(n.element(),dh(e),mh(e,n.element())),vi(n.element()),gh(n,e),ph(n,e),e.onStartShrink(n),e.onShrunk(n)}function yh(n,e,t,r){var o=r.getOrThunk(function(){return mh(e,n.element())});t.setCollapsed(),Ui(n.element(),dh(e),o),vi(n.element());var i=lh(n,e);ao(i,e.growingClass),uo(i,e.shrinkingClass),ph(n,e),e.onStartShrink(n)}function bh(n,e,t){var r=mh(e,n.element());("0px"===r?vh:yh)(n,e,t,Fn.some(r))}function xh(n,e,t){var r=lh(n,e),o=fo(r,e.shrinkingClass),i=mh(e,n.element());hh(n,e);var u=mh(e,n.element());(o?function(){Ui(n.element(),dh(e),i),vi(n.element())}:function(){ph(n,e)})(),ao(r,e.shrinkingClass),uo(r,e.growingClass),hh(n,e),Ui(n.element(),dh(e),u),t.setExpanded(),e.onStartGrow(n)}function wh(n,e,t){var r=lh(n,e);return!0===fo(r,e.growingClass)}function Sh(n,e,t){var r=lh(n,e);return!0===fo(r,e.shrinkingClass)}function Oh(e,t){var r=Gm(mm.sketch({dom:{tag:"div",classes:[Pi.resolve("dropup")]},components:[],containerBehaviours:Vo([ig.config({}),Nh.config({closedClass:Pi.resolve("dropup-closed"),openClass:Pi.resolve("dropup-open"),shrinkingClass:Pi.resolve("dropup-shrinking"),growingClass:Pi.resolve("dropup-growing"),dimension:{property:"height"},onShrunk:function(n){e(),t(),ig.set(n,[])},onGrown:function(n){e(),t()}}),Ni(function(n,e){o(w)})])})),o=function(n){l.window.requestAnimationFrame(function(){n(),Nh.shrink(r)})};return{appear:function(n,e,t){!0===Nh.hasShrunk(r)&&!1===Nh.isTransitioning(r)&&l.window.requestAnimationFrame(function(){e(t),ig.set(r,[n()]),Nh.grow(r)})},disappear:o,component:b(r),element:r.element}}function Th(n){return 8===n.raw().which&&!k(["input","textarea"],q(n.target()))&&!function(n,e,t){return nu(n,e,t).isSome()}(n.target(),'[contenteditable="true"]')}function kh(e,n){var t=qt("Getting GUI events settings",_h,n),r=jg(t),o=me(["touchstart","touchmove","touchend","touchcancel","gesturestart","mousedown","mouseup","mouseover","mousemove","mouseout","click"].concat(["selectstart","input","contextmenu","change","transitionend","drag","dragstart","dragend","dragenter","dragleave","dragover","drop","keyup"]),function(n){return nm(e,n,function(e){r.fireIfReady(e,n).each(function(n){n&&e.kill()}),t.triggerEvent(n,e)&&e.kill()})}),i=Pn(Fn.none()),u=nm(e,"paste",function(e){r.fireIfReady(e,"paste").each(function(n){n&&e.kill()}),t.triggerEvent("paste",e)&&e.kill(),i.set(Fn.some(l.setTimeout(function(){t.triggerEvent(_e(),e)},0)))}),c=nm(e,"keydown",function(n){t.triggerEvent("keydown",n)?n.kill():!0===t.stopBackspace&&Th(n)&&n.prevent()}),a=function(n,e){return jh?em(n,"focus",e):nm(n,"focusin",e)}(e,function(n){t.triggerEvent("focusin",n)&&n.kill()}),f=Pn(Fn.none()),s=function(n,e){return jh?em(n,"blur",e):nm(n,"focusout",e)}(e,function(n){t.triggerEvent("focusout",n)&&n.kill(),f.set(Fn.some(l.setTimeout(function(){t.triggerEvent(je(),n)},0)))});return{unbind:function(){C(o,function(n){n.unbind()}),c.unbind(),a.unbind(),s.unbind(),u.unbind(),i.get().each(l.clearTimeout),f.get().each(l.clearTimeout)}}}function Eh(n,e){var t=Mt(n,"target").map(function(n){return n()}).getOr(e);return Pn(t)}function Ch(n,r,e,t,o,i){var u=n(r,t),c=function(n,e){var t=Pn(!1),r=Pn(!1);return{stop:function(){t.set(!0)},cut:function(){r.set(!0)},isStopped:t.get,isCut:r.get,event:b(n),setSource:e.set,getSource:e.get}}(e,o);return u.fold(function(){return i.logEventNoHandlers(r,t),Ph.complete()},function(e){var t=e.descHandler();return Td(t)(c),c.isStopped()?(i.logEventStopped(r,e.element(),t.purpose()),Ph.stopped()):c.isCut()?(i.logEventCut(r,e.element(),t.purpose()),Ph.complete()):fn(e.element()).fold(function(){return i.logNoParent(r,e.element(),t.purpose()),Ph.complete()},function(n){return i.logEventResponse(r,e.element(),t.purpose()),Ph.resume(n)})})}function Dh(n,e,t){var r=function(n){var e=Pn(!1);return{stop:function(){e.set(!0)},cut:w,isStopped:e.get,isCut:b(!1),event:b(n),setSource:o("Cannot set source of a broadcasted event"),getSource:o("Cannot get source of a broadcasted event")}}(e);return C(n,function(n){var e=n.descHandler();Td(e)(r)}),r.isStopped()}var Mh,Ih=function(n){return Gm(Gs.sketch({dom:$s('<div class="${prefix}-mask-edit-icon ${prefix}-icon"></div>'),action:function(){n.run(function(n){n.setReadOnly(!1)})}}))},Rh=function(){return Gm(mm.sketch({dom:$s('<div class="${prefix}-editor-socket"></div>'),components:[],containerBehaviours:Vo([ig.config({})])}))},Ah=function(n,e,t,r){(!0===t?zo.toAlpha:zo.toOmega)(r),(t?fh:sh)(n,e)},Fh=/* */Object.freeze({refresh:function(n,e,t){if(t.isExpanded()){hi(n.element(),dh(e));var r=mh(e,n.element());Ui(n.element(),dh(e),r)}},grow:function(n,e,t){t.isExpanded()||xh(n,e,t)},shrink:function(n,e,t){t.isExpanded()&&bh(n,e,t)},immediateShrink:function(n,e,t){t.isExpanded()&&vh(n,e,t)},hasGrown:function(n,e,t){return t.isExpanded()},hasShrunk:function(n,e,t){return t.isCollapsed()},isGrowing:wh,isShrinking:Sh,isTransitioning:function(n,e,t){return!0===wh(n,e)||!0===Sh(n,e)},toggleGrow:function(n,e,t){(t.isExpanded()?bh:xh)(n,e,t)},disableTransitions:gh}),Bh=/* */Object.freeze({exhibit:function(n,e){var t=e.expanded;return $r(t?{classes:[e.openClass],styles:{}}:{classes:[e.closedClass],styles:En(e.dimension.property,"0px")})},events:function(t,r){return jr([function(n,e){return zr(n)(e)}(Fe(),function(n,e){e.event().raw().propertyName===t.dimension.property&&(gh(n,t),r.isExpanded()&&hi(n.element(),t.dimension.property),(r.isExpanded()?t.onGrown:t.onShrunk)(n))})])}}),Vh=[Kt("closedClass"),Kt("openClass"),Kt("shrinkingClass"),Kt("growingClass"),Zt("getAnimationRoot"),Jo("onShrunk"),Jo("onStartShrink"),Jo("onGrown"),Jo("onStartGrow"),rr("expanded",!1),Jt("dimension",Yt("property",{width:[ei("property","width"),ei("getDimension",function(n){return If(n)+"px"})],height:[ei("property","height"),ei("getDimension",function(n){return Xi(n)+"px"})]}))],Nh=Yr({fields:Vh,name:"sliding",active:Bh,apis:Fh,state:/* */Object.freeze({init:function(n){var e=Pn(n.expanded);return Bo({isExpanded:function(){return!0===e.get()},isCollapsed:function(){return!1===e.get()},setCollapsed:d(e.set,!1),setExpanded:d(e.set,!0),readState:function(){return"expanded: "+e.get()}})}})}),jh=L().browser.isFirefox(),_h=Ht([(Mh="triggerEvent",Jt(Mh,Ir)),rr("stopBackspace",!0)]),Ph=bt([{stopped:[]},{resume:["element"]},{complete:[]}]),Hh=function(e,t,r,n,o,i){return Ch(e,t,r,n,o,i).fold(function(){return!0},function(n){return Hh(e,t,r,n,o,i)},function(){return!1})},zh=function(n,e,t,r,o){var i=Eh(t,r);return Hh(n,e,t,r,i,o)},Lh=J("element","descHandler"),Gh=function(n,e){return{id:b(n),descHandler:b(e)}};function Uh(){var i={};return{registerId:function(r,o,n){Nn(n,function(n,e){var t=i[e]!==undefined?i[e]:{};t[o]=Pm(n,r),i[e]=t})},unregisterId:function(t){Nn(i,function(n,e){n.hasOwnProperty(t)&&delete n[t]})},filterByType:function(n){return Mt(i,n).map(function(n){return _n(n,function(n,e){return Gh(e,n)})}).getOr([])},find:function(n,e,t){var r=Dt(e)(i);return Eo(t,function(n){return function(t,r){return pf(r).fold(function(){return Fn.none()},function(n){var e=Dt(n);return t.bind(e).map(function(n){return Lh(r,n)})})}(r,n)},n)}}}function $h(){function r(n){var e=n.element();return pf(e).fold(function(){return function(n,e){var t=bc(df+n);return gf(e,t),t}("uid-",n.element())},function(n){return n})}var o=Uh(),i={},u=function(n){pf(n.element()).each(function(n){delete i[n],o.unregisterId(n)})};return{find:function(n,e,t){return o.find(n,e,t)},filter:function(n){return o.filterByType(n)},register:function(n){var e=r(n);Mn(i,e)&&function(n,e){var t=i[e];if(t!==n)throw new Error('The tagId "'+e+'" is already used by: '+xo(t.element())+"\nCannot use it for: "+xo(n.element())+"\nThe conflicting element is"+(K(t.element())?" ":" not ")+"already in the DOM");u(n)}(n,e);var t=[n];o.registerId(t,e,n.events()),i[e]=n},unregister:u,getById:function(n){return Dt(n)(i)}}}var Wh=function(t){function r(e){return fn(t.element()).fold(function(){return!0},function(n){return cn(e,n)})}function o(n,e){return u.find(r,n,e)}function i(t){var n=u.filter(Pe());C(n,function(n){var e=n.descHandler();Td(e)(t)})}var u=$h(),n=kh(t.element(),{triggerEvent:function(e,t){return Yo(e,t.target(),function(n){return function(n,e,t,r){var o=t.target();return zh(n,e,t,o,r)}(o,e,t,n)})}}),c={debugInfo:b("real"),triggerEvent:function(e,t,r){Yo(e,t,function(n){zh(o,e,r,t,n)})},triggerFocus:function(e,t){pf(e).fold(function(){lo(e)},function(n){Yo(Ne(),e,function(n){!function(n,e,t,r,o){var i=Eh(t,r);Ch(n,e,t,r,i,o)}(o,Ne(),{originator:b(t),kill:w,prevent:w,target:b(e)},e,n)})})},triggerEscape:function(n,e){c.triggerEvent("keydown",n.element(),e.event())},getByUid:function(n){return g(n)},getByDom:function(n){return p(n)},build:Gm,addToGui:function(n){f(n)},removeFromGui:function(n){s(n)},addToWorld:function(n){e(n)},removeFromWorld:function(n){a(n)},broadcast:function(n){l(n)},broadcastOn:function(n,e){d(n,e)},broadcastEvent:function(n,e){m(n,e)},isConnected:b(!0)},e=function(n){n.connect(c),rt(n.element())||(u.register(n),C(n.components(),e),c.triggerEvent(Ue(),n.element(),{target:b(n.element())}))},a=function(n){rt(n.element())||(C(n.components(),a),u.unregister(n)),n.disconnect()},f=function(n){!function(n,e){gt(n,e,st)}(t,n)},s=function(n){yn(n)},l=function(n){i({universal:b(!0),data:b(n)})},d=function(n,e){i({universal:b(!1),channels:b(n),data:b(e)})},m=function(n,e){var t=u.filter(n);return Dh(t,e)},g=function(n){return u.getById(n).fold(function(){return yt.error(new Error('Could not find component with uid: "'+n+'" in system.'))},yt.value)},p=function(n){var e=pf(n).getOr("not found");return g(e)};return e(t),{root:b(t),element:t.element,destroy:function(){n.unbind(),lt(t.element())},add:f,remove:s,getByUid:g,getByDom:p,addToWorld:e,removeFromWorld:a,broadcast:l,broadcastOn:d,broadcastEvent:m}},Xh=b(Pi.resolve("readonly-mode")),qh=b(Pi.resolve("edit-mode"));function Yh(n){var e=Gm(mm.sketch({dom:{classes:[Pi.resolve("outer-container")].concat(n.classes)},containerBehaviours:Vo([zo.config({alpha:Xh(),omega:qh()})])}));return Wh(e)}var Kh=function(n,e){var t=Qe.fromTag("input");mi(t,{opacity:"0",position:"absolute",top:"-1000px",left:"-1000px"}),st(n,t),lo(t),e(t),lt(t)},Jh=function(n){var e=n.getSelection();if(0<e.rangeCount){var t=e.getRangeAt(0),r=n.document.createRange();r.setStart(t.startContainer,t.startOffset),r.setEnd(t.endContainer,t.endOffset),e.removeAllRanges(),e.addRange(r)}},Qh=function(n,e){go().each(function(n){cn(n,e)||mo(n)}),n.focus(),lo(Qe.fromDom(n.document.body)),Jh(n)},Zh={stubborn:function(n,e,t,r){function o(){Qh(e,r)}var i=nm(t,"keydown",function(n){k(["input","textarea"],q(n.target()))||o()});return{toReading:function(){Kh(n,mo)},toEditing:o,onToolbarTouch:function(){},destroy:function(){i.unbind()}}},timid:function(n,e,t,r){function o(){mo(r)}return{toReading:function(){o()},toEditing:function(){Qh(e,r)},onToolbarTouch:function(){o()},destroy:w}}},nv=function(e,r,t,o,n){function i(){r.run(function(n){n.refreshSelection()})}function u(n,e){var t=n-o.dom().scrollTop;r.run(function(n){n.scrollIntoView(t,t+e)})}function c(){r.run(function(n){n.clearSelection()})}function a(){e.getCursorBox().each(function(n){u(n.top(),n.height())}),r.run(function(n){n.syncHeight()})}var f=_g(e),s=kp(a,300),l=[e.onKeyup(function(){c(),s.throttle()}),e.onNodeChanged(i),e.onDomChanged(s.throttle),e.onDomChanged(i),e.onScrollToCursor(function(n){n.preventDefault(),s.throttle()}),e.onScrollToElement(function(n){n.element(),u(r,o)}),e.onToEditing(function(){r.run(function(n){n.toEditing()})}),e.onToReading(function(){r.run(function(n){n.toReading()})}),nm(e.doc(),"touchend",function(n){cn(e.html(),n.target())||cn(e.body(),n.target())}),nm(t,"transitionend",function(n){"height"===n.raw().propertyName&&function(){var e=Xi(t);r.run(function(n){n.setViewportOffset(e)}),i(),a()}()}),em(t,"touchstart",function(n){r.run(function(n){n.highlightSelection()}),function(e){r.run(function(n){n.onToolbarTouch(e)})}(n),e.onTouchToolstrip()}),nm(e.body(),"touchstart",function(n){c(),e.onTouchContent(),f.fireTouchstart(n)}),f.onTouchmove(),f.onTouchend(),nm(e.body(),"click",function(n){n.kill()}),nm(t,"touchmove",function(){e.onToolbarScrollStart()})];return{destroy:function(){C(l,function(n){n.unbind()})}}};var ev,tv,rv,ov,iv={},uv={exports:iv};ev=undefined,tv=iv,rv=uv,ov=undefined,function(n){"object"==typeof tv&&void 0!==rv?rv.exports=n():"function"==typeof ev&&ev.amd?ev([],n):("undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).EphoxContactWrapper=n()}(function(){return function s(i,u,c){function a(e,n){if(!u[e]){if(!i[e]){var t="function"==typeof ov&&ov;if(!n&&t)return t(e,!0);if(f)return f(e,!0);var r=new Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r}var o=u[e]={exports:{}};i[e][0].call(o.exports,function(n){return a(i[e][1][n]||n)},o,o.exports,s,i,u,c)}return u[e].exports}for(var f="function"==typeof ov&&ov,n=0;n<c.length;n++)a(c[n]);return a}({1:[function(n,e,t){var r,o,i=e.exports={};function u(){throw new Error("setTimeout has not been defined")}function c(){throw new Error("clearTimeout has not been defined")}function a(n){if(r===setTimeout)return setTimeout(n,0);if((r===u||!r)&&setTimeout)return r=setTimeout,setTimeout(n,0);try{return r(n,0)}catch(e){try{return r.call(null,n,0)}catch(e){return r.call(this,n,0)}}}!function(){try{r="function"==typeof setTimeout?setTimeout:u}catch(n){r=u}try{o="function"==typeof clearTimeout?clearTimeout:c}catch(n){o=c}}();var f,s=[],l=!1,d=-1;function m(){l&&f&&(l=!1,f.length?s=f.concat(s):d=-1,s.length&&g())}function g(){if(!l){var n=a(m);l=!0;for(var e=s.length;e;){for(f=s,s=[];++d<e;)f&&f[d].run();d=-1,e=s.length}f=null,l=!1,function t(n){if(o===clearTimeout)return clearTimeout(n);if((o===c||!o)&&clearTimeout)return o=clearTimeout,clearTimeout(n);try{return o(n)}catch(e){try{return o.call(null,n)}catch(e){return o.call(this,n)}}}(n)}}function p(n,e){this.fun=n,this.array=e}function h(){}i.nextTick=function(n){var e=new Array(arguments.length-1);if(1<arguments.length)for(var t=1;t<arguments.length;t++)e[t-1]=arguments[t];s.push(new p(n,e)),1!==s.length||l||a(g)},p.prototype.run=function(){this.fun.apply(null,this.array)},i.title="browser",i.browser=!0,i.env={},i.argv=[],i.version="",i.versions={},i.on=h,i.addListener=h,i.once=h,i.off=h,i.removeListener=h,i.removeAllListeners=h,i.emit=h,i.prependListener=h,i.prependOnceListener=h,i.listeners=function(n){return[]},i.binding=function(n){throw new Error("process.binding is not supported")},i.cwd=function(){return"/"},i.chdir=function(n){throw new Error("process.chdir is not supported")},i.umask=function(){return 0}},{}],2:[function(n,l,e){(function(e){function r(){}function i(n){if("object"!=typeof this)throw new TypeError("Promises must be constructed via new");if("function"!=typeof n)throw new TypeError("not a function");this._state=0,this._handled=!1,this._value=undefined,this._deferreds=[],s(n,this)}function o(r,o){for(;3===r._state;)r=r._value;0!==r._state?(r._handled=!0,i._immediateFn(function(){var n=1===r._state?o.onFulfilled:o.onRejected;if(null!==n){var e;try{e=n(r._value)}catch(t){return void c(o.promise,t)}u(o.promise,e)}else(1===r._state?u:c)(o.promise,r._value)})):r._deferreds.push(o)}function u(n,e){try{if(e===n)throw new TypeError("A promise cannot be resolved with itself.");if(e&&("object"==typeof e||"function"==typeof e)){var t=e.then;if(e instanceof i)return n._state=3,n._value=e,void a(n);if("function"==typeof t)return void s(function r(n,e){return function(){n.apply(e,arguments)}}(t,e),n)}n._state=1,n._value=e,a(n)}catch(o){c(n,o)}}function c(n,e){n._state=2,n._value=e,a(n)}function a(n){2===n._state&&0===n._deferreds.length&&i._immediateFn(function(){n._handled||i._unhandledRejectionFn(n._value)});for(var e=0,t=n._deferreds.length;e<t;e++)o(n,n._deferreds[e]);n._deferreds=null}function f(n,e,t){this.onFulfilled="function"==typeof n?n:null,this.onRejected="function"==typeof e?e:null,this.promise=t}function s(n,e){var t=!1;try{n(function(n){t||(t=!0,u(e,n))},function(n){t||(t=!0,c(e,n))})}catch(r){if(t)return;t=!0,c(e,r)}}var n,t;n=this,t=setTimeout,i.prototype["catch"]=function(n){return this.then(null,n)},i.prototype.then=function(n,e){var t=new this.constructor(r);return o(this,new f(n,e,t)),t},i.all=function(n){var a=Array.prototype.slice.call(n);return new i(function(o,i){if(0===a.length)return o([]);var u=a.length;function c(e,n){try{if(n&&("object"==typeof n||"function"==typeof n)){var t=n.then;if("function"==typeof t)return void t.call(n,function(n){c(e,n)},i)}a[e]=n,0==--u&&o(a)}catch(r){i(r)}}for(var n=0;n<a.length;n++)c(n,a[n])})},i.resolve=function(e){return e&&"object"==typeof e&&e.constructor===i?e:new i(function(n){n(e)})},i.reject=function(t){return new i(function(n,e){e(t)})},i.race=function(o){return new i(function(n,e){for(var t=0,r=o.length;t<r;t++)o[t].then(n,e)})},i._immediateFn="function"==typeof e?function(n){e(n)}:function(n){t(n,0)},i._unhandledRejectionFn=function(n){"undefined"!=typeof console&&console&&console.warn("Possible Unhandled Promise Rejection:",n)},i._setImmediateFn=function(n){i._immediateFn=n},i._setUnhandledRejectionFn=function(n){i._unhandledRejectionFn=n},void 0!==l&&l.exports?l.exports=i:n.Promise||(n.Promise=i)}).call(this,n("timers").setImmediate)},{timers:3}],3:[function(a,n,f){(function(n,e){var r=a("process/browser.js").nextTick,t=Function.prototype.apply,o=Array.prototype.slice,i={},u=0;function c(n,e){this._id=n,this._clearFn=e}f.setTimeout=function(){return new c(t.call(setTimeout,window,arguments),clearTimeout)},f.setInterval=function(){return new c(t.call(setInterval,window,arguments),clearInterval)},f.clearTimeout=f.clearInterval=function(n){n.close()},c.prototype.unref=c.prototype.ref=function(){},c.prototype.close=function(){this._clearFn.call(window,this._id)},f.enroll=function(n,e){clearTimeout(n._idleTimeoutId),n._idleTimeout=e},f.unenroll=function(n){clearTimeout(n._idleTimeoutId),n._idleTimeout=-1},f._unrefActive=f.active=function(n){clearTimeout(n._idleTimeoutId);var e=n._idleTimeout;0<=e&&(n._idleTimeoutId=setTimeout(function(){n._onTimeout&&n._onTimeout()},e))},f.setImmediate="function"==typeof n?n:function(n){var e=u++,t=!(arguments.length<2)&&o.call(arguments,1);return i[e]=!0,r(function(){i[e]&&(t?n.apply(null,t):n.call(null),f.clearImmediate(e))}),e},f.clearImmediate="function"==typeof e?e:function(n){delete i[n]}}).call(this,a("timers").setImmediate,a("timers").clearImmediate)},{"process/browser.js":1,timers:3}],4:[function(n,e,t){var r=n("promise-polyfill"),o="undefined"!=typeof window?window:Function("return this;")();e.exports={boltExport:o.Promise||r}},{"promise-polyfill":2}]},{},[4])(4)});function cv(n){l.setTimeout(function(){throw n},0)}function av(n,e,t){return Math.abs(n-e)<=t?Fn.none():n<e?Fn.some(n+t):Fn.some(n-t)}function fv(e,t){return So([{width:320,height:480,keyboard:{portrait:300,landscape:240}},{width:320,height:568,keyboard:{portrait:300,landscape:240}},{width:375,height:667,keyboard:{portrait:305,landscape:240}},{width:414,height:736,keyboard:{portrait:320,landscape:240}},{width:768,height:1024,keyboard:{portrait:320,landscape:400}},{width:1024,height:1366,keyboard:{portrait:380,landscape:460}}],function(n){return function(n,e){return n?Fn.some(e):Fn.none()}(e<=n.width&&t<=n.height,n.keyboard)}).getOr({portrait:t/5,landscape:e/4})}function sv(n){var e=Fg(n).isPortrait(),t=function(n){return fv(n.screen.width,n.screen.height)}(n),r=e?t.portrait:t.landscape;return(e?n.screen.height:n.screen.width)-n.innerHeight>r?0:r}function lv(n,e){var t=an(n).dom().defaultView;return Xi(n)+Xi(e)-sv(t)}function dv(n){return zg(n,Iv)}function mv(n,e){var t=function(n){return Zr(n,Rv)}(n);return Mv.fixed(n,t,e)}function gv(n,e){return Mv.scroller(n,e)}function pv(n){var e=dv(n);return("true"===Zr(n,Av)?gv:mv)(n,e)}function hv(n,e,t){var r=an(n).dom().defaultView.innerHeight;return Jr(n,Fv,r+"px"),r-e-t}function vv(n){var e=pi(n,"top").getOr("0");return parseInt(e,10)}function yv(n){return parseInt(n.dom().scrollTop,10)}function bv(n,e){var t=e+Nv(n)+"px";Ui(n,"top",t)}var xv=uv.exports.boltExport,wv=function(n){var t=Fn.none(),e=[],r=function(n){o()?u(n):e.push(n)},o=function(){return t.isSome()},i=function(n){C(n,u)},u=function(e){t.each(function(n){l.setTimeout(function(){e(n)},0)})};return n(function(n){t=Fn.some(n),i(e),e=[]}),{get:r,map:function(t){return wv(function(e){r(function(n){e(t(n))})})},isReady:o}},Sv={nu:wv,pure:function(e){return wv(function(n){n(e)})}},Ov=function(t){function n(n){t().then(n,cv)}return{map:function(n){return Ov(function(){return t().then(n)})},bind:function(e){return Ov(function(){return t().then(function(n){return e(n).toPromise()})})},anonBind:function(n){return Ov(function(){return t().then(function(){return n.toPromise()})})},toLazy:function(){return Sv.nu(n)},toCached:function(){var n=null;return Ov(function(){return null===n&&(n=t()),n})},toPromise:t,get:n}},Tv=function(n){return Ov(function(){return new xv(n)})},kv=function(n){return Ov(function(){return xv.resolve(n)})},Ev=function(){var f=null;return{animate:function(r,o,n,i,e,t){function u(n){c=!0,e(n)}var c=!1;Ag.clearInterval(f);function a(n){Ag.clearInterval(f),u(n)}f=Ag.setInterval(function(){var t=r();av(t,o,n).fold(function(){Ag.clearInterval(f),u(o)},function(n){if(i(n,a),!c){var e=r();(e!==n||Math.abs(e-o)>Math.abs(t-o))&&(Ag.clearInterval(f),u(o))}})},t)}}},Cv=lv,Dv=function(n,e,t){var r=lv(e,t),o=Xi(e)+Xi(t)-r;Ui(n,"padding-bottom",o+"px")},Mv=bt([{fixed:["element","property","offsetY"]},{scroller:["element","offsetY"]}]),Iv="data-"+Pi.resolve("position-y-fixed"),Rv="data-"+Pi.resolve("y-property"),Av="data-"+Pi.resolve("scrolling"),Fv="data-"+Pi.resolve("last-window-height"),Bv=function(n){var e=Ki(n,"["+Iv+"]");return me(e,pv)},Vv=function(r,o,i,u){function n(){var n=t.innerHeight;return function(n){return zg(n,Fv)}(r)<n}function e(){if(d){var n=Xi(i),e=Xi(u),t=hv(r,n,e);Jr(r,Iv,n+"px"),Ui(r,"height",t+"px"),Dv(o,r,u)}}var t=an(r).dom().defaultView,c=function(n){var e=Zr(n,"style");mi(n,{position:"absolute",top:"0px"}),Jr(n,Iv,"0px"),Jr(n,Rv,"top");return{restore:function(){Jr(n,"style",e||""),eo(n,Iv),eo(n,Rv)}}}(i),a=Xi(i),f=Xi(u),s=function(n,e,t){var r=Zr(t,"style");yg.register(t),mi(t,{position:"absolute",height:e+"px",width:"100%",top:n+"px"}),Jr(t,Iv,n+"px"),Jr(t,Av,"true"),Jr(t,Rv,"top");return{restore:function(){yg.deregister(t),Jr(t,"style",r||""),eo(t,Iv),eo(t,Av),eo(t,Rv)}}}(a,hv(r,a,f),r),l=function(n){var e=Zr(n,"style");mi(n,{position:"absolute",bottom:"0px"}),Jr(n,Iv,"0px"),Jr(n,Rv,"bottom");return{restore:function(){Jr(n,"style",e||""),eo(n,Iv),eo(n,Rv)}}}(u),d=!0;return Dv(o,r,u),{setViewportOffset:function(n){Jr(r,Iv,n+"px"),e()},isExpanding:n,isShrinking:m(n),refresh:e,restore:function(){d=!1,c.restore(),s.restore(),l.restore()}}},Nv=dv,jv=Ev(),_v="data-"+Pi.resolve("last-scroll-top"),Pv=function(t,r,o){return Tv(function(n){var e=d(yv,t);jv.animate(e,r,15,function(n){t.dom().scrollTop=n,Ui(t,"top",vv(t)+15+"px")},function(){t.dom().scrollTop=r,Ui(t,"top",o+"px"),n(r)},10)})},Hv=function(o,i){return Tv(function(n){var e=d(yv,o);Jr(o,_v,e());var t=Math.abs(i-e()),r=Math.ceil(t/10);jv.animate(e,i,r,function(n,e){zg(o,_v)!==o.dom().scrollTop?e(o.dom().scrollTop):(o.dom().scrollTop=n,Jr(o,_v,n))},function(){o.dom().scrollTop=i,Jr(o,_v,i),n(i)},10)})},zv=function(i,u){return Tv(function(n){function e(n){Ui(i,"top",n+"px")}var t=d(vv,i),r=Math.abs(u-t()),o=Math.ceil(r/10);jv.animate(t,u,o,e,function(){e(u),n(u)},10)})},Lv=function(e,t,r){var o=an(e).dom().defaultView;return Tv(function(n){bv(e,r),bv(t,r),o.scrollTo(0,r),n(r)})};function Gv(i,n){return n(function(t){var r=[],o=0;0===i.length?t([]):C(i,function(n,e){n.get(function(e){return function(n){r[e]=n,++o>=i.length&&t(r)}}(e))})})}function Uv(n,r){return n.fold(function(n,e,t){return function(n,e,t,r){return Ui(n,e,t+r+"px"),kv(r)}(n,e,r,t)},function(n,e){return function(n,e,t){var r=e+t,o=pi(n,"top").getOr(t),i=r-parseInt(o,10),u=n.dom().scrollTop+i;return Pv(n,u,r)}(n,r,e)})}function $v(e,t,n,r,o,i){var u=function f(t){var r=Pn(Sv.pure({}));return{start:function(e){var n=Sv.nu(function(n){return t(e).get(n)});r.set(n)},idle:function(n){r.get().get(function(){n()})}}}(function(n){return Lv(e,t,n)}),c=kp(function(){u.idle(function(){Xv(n,r.pageYOffset).get(function(){(function(){var n=_p(i);return Fn.from(n[0]).bind(function(n){var e=n.top()-t.dom().scrollTop;return e>r.innerHeight+5||e<-5?Fn.some({top:b(e),bottom:b(e+n.height())}):Fn.none()})})().each(function(n){t.dom().scrollTop=t.dom().scrollTop+n.top()}),u.start(0),o.refresh()})})},1e3),a=nm(Qe.fromDom(r),"scroll",function(){r.pageYOffset<0||c.throttle()});return Xv(n,r.pageYOffset).get(y),{unbind:a.unbind}}var Wv=function(n,e,t,r,o){var i=Cv(e,t),u=d(Jh,n);i<r||i<o?Hv(e,e.dom().scrollTop-i+o).get(u):r<0&&Hv(e,e.dom().scrollTop+r).get(u)},Xv=function(n,e){var t=Bv(n);return function(n){return Gv(n,Tv)}(me(t,function(n){return Uv(n,e)}))},qv=function(n){var t=n.cWin(),e=n.ceBody(),r=n.socket(),o=n.toolstrip(),i=n.toolbar(),u=n.contentElement(),c=n.keyboardType(),a=n.outerWindow(),f=n.dropup(),s=Vv(r,e,o,f),l=c(n.outerBody(),t,ot(),u,o,i),d=Bg(a,{onChange:w,onReady:s.refresh});d.onAdjustment(function(){s.refresh()});var m=nm(Qe.fromDom(a),"resize",function(){s.isExpanding()&&s.refresh()}),g=$v(o,r,n.outerBody(),a,s,t),p=function v(t,e){var n=t.document,r=Qe.fromTag("div");function o(n){var e=Qe.fromTag("span");return Id(e,[Pi.resolve("layer-editor"),Pi.resolve("unfocused-selection")]),mi(e,{left:n.left()+"px",top:n.top()+"px",width:n.width()+"px",height:n.height()+"px"}),e}uo(r,Pi.resolve("unfocused-selections")),st(Qe.fromDom(n.documentElement),r);var i=nm(r,"touchstart",function(n){n.prevent(),Qh(t,e),u()}),u=function(){pn(r)};return{update:function(){u();var n=_p(t),e=me(n,o);gn(r,e)},isActive:function(){return 0<ft(r).length},destroy:function(){i.unbind(),lt(r)},clear:u}}(t,u),h=function(){p.clear()};return{toEditing:function(){l.toEditing(),h()},toReading:function(){l.toReading()},onToolbarTouch:function(n){l.onToolbarTouch(n)},refreshSelection:function(){p.isActive()&&p.update()},clearSelection:h,highlightSelection:function(){p.update()},scrollIntoView:function(n,e){Wv(t,r,f,n,e)},updateToolbarPadding:w,setViewportOffset:function(n){s.setViewportOffset(n),zv(r,n).get(y)},syncHeight:function(){Ui(u,"height",u.dom().contentWindow.document.body.scrollHeight+"px")},refreshStructure:s.refresh,destroy:function(){s.restore(),d.destroy(),g.unbind(),m.unbind(),l.destroy(),p.destroy(),Kh(ot(),mo)}}},Yv=function(r,n){var o=Yp(),i=xd(),u=xd(),c=bd(),a=bd();return{enter:function(){n.hide();var t=Qe.fromDom(l.document);Lp.getActiveApi(r.editor).each(function(n){i.set({socketHeight:pi(r.socket,"height"),iframeHeight:pi(n.frame(),"height"),outerScroll:l.document.body.scrollTop}),u.set({exclusives:ch.exclusive(t,"."+yg.scrollable())}),uo(r.container,Pi.resolve("fullscreen-maximized")),Xp(r.container,n.body()),o.maximize(),Ui(r.socket,"overflow","scroll"),Ui(r.socket,"-webkit-overflow-scrolling","touch"),lo(n.body());var e=nn(["cWin","ceBody","socket","toolstrip","toolbar","dropup","contentElement","cursor","keyboardType","isScrolling","outerWindow","outerBody"],[]);c.set(qv(e({cWin:n.win(),ceBody:n.body(),socket:r.socket,toolstrip:r.toolstrip,toolbar:r.toolbar,dropup:r.dropup.element(),contentElement:n.frame(),cursor:w,outerBody:r.body,outerWindow:r.win,keyboardType:Zh.stubborn,isScrolling:function(){return u.get().exists(function(n){return n.socket.isScrolling()})}}))),c.run(function(n){n.syncHeight()}),a.set(nv(n,c,r.toolstrip,r.socket,r.dropup))})},refreshStructure:function(){c.run(function(n){n.refreshStructure()})},exit:function(){o.restore(),a.clear(),c.clear(),n.show(),i.on(function(n){n.socketHeight.each(function(n){Ui(r.socket,"height",n)}),n.iframeHeight.each(function(n){Ui(r.editor.getFrame(),"height",n)}),l.document.body.scrollTop=n.scrollTop}),i.clear(),u.on(function(n){n.exclusives.unbind()}),u.clear(),ao(r.container,Pi.resolve("fullscreen-maximized")),qp(),yg.deregister(r.toolbar),hi(r.socket,"overflow"),hi(r.socket,"-webkit-overflow-scrolling"),mo(r.editor.getFrame()),Lp.getActiveApi(r.editor).each(function(n){n.clearSelection()})}}},Kv=function(n){var e=qt("Getting IosWebapp schema",Qp,n);Ui(e.toolstrip,"width","100%"),Ui(e.container,"position","relative");var t=Gm(Jp(function(){e.setReadOnly(e.readOnlyOnInit()),o.enter()},e.translate));e.alloy.add(t);var r={show:function(){e.alloy.add(t)},hide:function(){e.alloy.remove(t)}},o=Yv(e,r);return{setReadOnly:e.setReadOnly,refreshStructure:o.refreshStructure,enter:o.enter,exit:o.exit,destroy:w}};function Jv(n,e,t){n.system().broadcastOn([$o.formatChanged()],{command:e,state:t})}function Qv(m){return{getNotificationManagerImpl:function(){return{open:b({progressBar:{value:w},close:w,text:w,getEl:b(null),moveTo:w,moveRel:w,settings:{}}),close:w,reposition:w,getArgs:b({})}},renderUI:function(){var n=m.getElement(),e=ny(m);!1===function(n){return!1===n.settings.skin}(m)?(m.contentCSS.push(e.content),Lo.DOM.styleSheetLoader.load(e.ui,ty(m))):ty(m)();function t(){m.fire("ScrollIntoView")}var f=L().os.isAndroid()?function c(n){var e=Yh({classes:[Pi.resolve("android-container")]}),t=ah(),r=bd(),o=Ih(r),i=Rh(),u=Oh(w,n);return e.add(t.wrapper()),e.add(i),e.add(u.component()),{system:b(e),element:e.element,init:function(n){r.set(Zp(n))},exit:function(){r.run(function(n){n.exit(),ig.remove(i,o)})},setToolbarGroups:function(n){var e=t.createGroups(n);t.setGroups(e)},setContextToolbar:function(n){var e=t.createGroups(n);t.setContextToolbar(e)},focusToolbar:function(){t.focus()},restoreToolbar:function(){t.restoreToolbar()},updateMode:function(n){Ah(i,o,n,e.root())},socket:b(i),dropup:b(u)}}(t):function a(n){var e=Yh({classes:[Pi.resolve("ios-container")]}),t=ah(),r=bd(),o=Ih(r),i=Rh(),u=Oh(function(){r.run(function(n){n.refreshStructure()})},n);return e.add(t.wrapper()),e.add(i),e.add(u.component()),{system:b(e),element:e.element,init:function(n){r.set(Kv(n))},exit:function(){r.run(function(n){ig.remove(i,o),n.exit()})},setToolbarGroups:function(n){var e=t.createGroups(n);t.setGroups(e)},setContextToolbar:function(n){var e=t.createGroups(n);t.setContextToolbar(e)},focusToolbar:function(){t.focus()},restoreToolbar:function(){t.restoreToolbar()},updateMode:function(n){Ah(i,o,n,e.root())},socket:b(i),dropup:b(u)}}(t);!function(n,e){pt(n,e,dn)}(Qe.fromDom(n),f.system());function s(n,e,t,r){!1===r&&m.selection.collapse();var o=i(n,e,t);f.setToolbarGroups(!0===r?o.readOnly:o.main),m.setMode(!0===r?"readonly":"design"),m.fire(!0===r?ry():oy()),f.updateMode(r)}function l(n,e){return m.on(n,e),{unbind:function(){m.off(n)}}}var r=n.ownerDocument.defaultView,d=Bg(r,{onChange:function(){f.system().broadcastOn([$o.orientationChanged()],{width:Vg(r)})},onReady:w}),i=function(n,e,t){var r=n.get();return{readOnly:r.backToMask.concat(e.get()),main:r.backToMask.concat(t.get())}};return m.on("init",function(){f.init({editor:{getFrame:function(){return Qe.fromDom(m.contentAreaContainer.querySelector("iframe"))},onDomChanged:function(){return{unbind:w}},onToReading:function(n){return l(ry(),n)},onToEditing:function(n){return l(oy(),n)},onScrollToCursor:function(e){m.on("ScrollIntoView",function(n){e(n)});return{unbind:function(){m.off("ScrollIntoView"),d.destroy()}}},onTouchToolstrip:function(){n()},onTouchContent:function(){(function(n){return po(n).bind(function(n){return f.system().getByDom(n).toOption()})})(Qe.fromDom(m.editorContainer.querySelector("."+Pi.resolve("toolbar")))).each($),f.restoreToolbar(),n()},onTapContent:function(n){var e=n.target();if("img"===q(e))m.selection.select(e.dom()),n.kill();else if("a"===q(e)){f.system().getByDom(Qe.fromDom(m.editorContainer)).each(function(n){zo.isAlpha(n)&&Uo(e.dom())})}}},container:Qe.fromDom(m.editorContainer),socket:Qe.fromDom(m.contentAreaContainer),toolstrip:Qe.fromDom(m.editorContainer.querySelector("."+Pi.resolve("toolstrip"))),toolbar:Qe.fromDom(m.editorContainer.querySelector("."+Pi.resolve("toolbar"))),dropup:f.dropup(),alloy:f.system(),translate:w,setReadOnly:function(n){s(a,c,u,n)},readOnlyOnInit:function(){return!1}});var n=function(){f.dropup().disappear(function(){f.system().broadcastOn([$o.dropupDismissed()],{})})},e={label:"The first group",scrollable:!1,items:[Xs.forToolbar("back",function(){m.selection.collapse(),f.exit()},{},m)]},t={label:"Back to read only",scrollable:!1,items:[Xs.forToolbar("readonly-back",function(){s(a,c,u,!0)},{},m)]},r=Dg(f,m),o=Mg(m.settings,r),i={label:"The extra group",scrollable:!1,items:[]},u=Pn([{label:"the action group",scrollable:!0,items:o},i]),c=Pn([{label:"The read only mode group",scrollable:!0,items:[]},i]),a=Pn({backToMask:[e],backToReadOnly:[t]});ey(f,m)}),m.on("remove",function(){f.exit()}),m.on("detach",function(){!function(e){var n=ft(e.element());C(n,function(n){e.getByDom(n).each(dt)}),lt(e.element())}(f.system()),f.system().destroy()}),{iframeContainer:f.socket().element().dom(),editorContainer:f.element().dom()}}}}var Zv=tinymce.util.Tools.resolve("tinymce.EditorManager"),ny=function(n){var e=Mt(n.settings,"skin_url").fold(function(){return Zv.baseURL+"/skins/ui/oxide"},function(n){return n});return{content:e+"/content.mobile.min.css",ui:e+"/skin.mobile.min.css"}},ey=function(r,n){var e=Bn(n.formatter.get());C(e,function(e){n.formatter.formatChanged(e,function(n){Jv(r,e,n)})}),C(["ul","ol"],function(t){n.selection.selectorChanged(t,function(n,e){Jv(r,t,n)})})},ty=(b(["x-small","small","medium","large","x-large"]),function(n){function e(){n._skinLoaded=!0,n.fire("SkinLoaded")}return function(){n.initialized?e():n.on("init",e)}}),ry=b("toReading"),oy=b("toEditing");!function uy(){Go.add("mobile",Qv)}()}(window);