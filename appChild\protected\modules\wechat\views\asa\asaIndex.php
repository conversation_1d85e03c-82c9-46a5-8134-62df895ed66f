<style type="text/css">
    p.pic {
        text-align: center;
        color: #999;
        font-size: 12px;
        line-height: 16px;
    }

    p.pic img {
        max-width: 100%;
        margin-bottom: 8px;
        box-shadow: 4px 4px 2px #cecece;
    }

</style>
<ul class="navbar navbar-fixed-top sticky-top navbar-light bg-faded" style="min-height: 10px;background: #f7f7f7">
    <ul class="nav nav-pills nav-fill">
        <li role="presentation" class="text-center cChange active" style="width: 49%;padding: 10px 15px 5px"><a
                href="javascript:" style="padding:4px 15px;">中文</a></li>
        <li role="presentation" class="text-center eChange switchLan" style="width: 49%;padding: 10px 15px 5px"><a
                style="padding:4px 15px;" href="javascript:">English</a>
        </li>
    </ul>
</ul>
<div class="weui_msg" style="padding-top: 90px">
    <div class="weui_text_area" id="cPage">
        <p class="weui_msg_desc text-left">
            请按照以下步骤来为您的孩子报名萌犸象活动中心。
        </p>

        <p class="weui_msg_desc text-left">
            1. 请扫描以下二维码进入报名平台。
        </p>

        <div class="weui_icon_area"><img src="http://osspub.ivykids.cn/asa/qrcode.jpg"></div>
        <p class="weui_msg_desc text-left">
            2. 如果您的微信账户已经绑定“艾毅幼儿园学校家长服务”，您将会进入以下界面，点击您的孩子名字，然后进入报名界面。如果您有多个孩子在艾毅幼儿园就读，请点击任意一名孩子的名字进入报名界面。
        </p>

        <p class="pic">
            <img src="https://m2.files.ivykids.cn/cloud01-file-8025768FiBcSfTBQC3q6_9k3eL391DQFSWe.png">
        </p>

        <p class="weui_msg_desc text-left">
            请注意：如果您的微信账号还未绑定到“艾毅幼儿园学校家长服务”，请使用您在学校注册的手机号来进行验证，并绑定到“艾毅幼儿园学校家长服务”。或者您也可以选择使用艾毅幼儿园在线平台的邮箱和密码来完成该步骤。
        </p>

        <p class="pic">

            <img src="https://m2.files.ivykids.cn/cloud01-file-8025768FlKf5FqNPqLDUZaeotCVu4CTvJgn.png">


        </p>

        <p class="weui_msg_desc text-left">
            3. 请在“课程”里找到“2017-2018学年第一季课外活动”类似的课程分组，点击后可以浏览当前的课程列表，您可以点击每门课程来了解课程内容。请注意，在课程报名开始日之前，家长仅可以浏览课程。
        </p>

        <p class="pic">

            <img src="https://m2.files.ivykids.cn/cloud01-file-8025768FgoTa97QrKDAVyO1LiM0Hs0FZ8kN.png">

            <br>

            <img src="https://m2.files.ivykids.cn/cloud01-file-8025768FpsQ-AHK7XLfjn6Ciu1lm016lT95.png">

        </p>

        <p class="weui_msg_desc text-left">
            4. 当报名开始后，请点击”报名”按钮进行注册。付款必须在15分钟之内完成，否则课程将会自动被取消，您需要再次报名。
        </p>

        <p class="pic">

            <img src="https://m2.files.ivykids.cn/cloud01-file-8025768FnLFvNWXFXJ3KeF4vEWQNqg3IfvQ.png">

            <br>
            <img src="https://m2.files.ivykids.cn/cloud01-file-8025768Fq2Y9VJAbAtrwbwqWm_QSBKgrzMl.png">

        </p>

        <p class="weui_msg_desc text-left">
            5. 我们目前仅接受微信付款。如果账单金额超出了您的银行限额，请查看以下两种方式来帮助您解决问题：<br>1) 您可以选择每次只支付一门课程，一般来说，单门课程的金额不会超出支付限额。<br>2)
            您也可以在报名开的”下方点击“已购课程”，然后点击您计划取消的课程。微信钱包没有支付限额。<br>如果您在支付过程中遇到了问题，请拨打电话询问校园。
        </p>

        <p class="weui_msg_desc text-left">
            6. 每门课程的详细介绍页面有退款截止日期在这个日期之前，家长都可以申请全额退款。请在“我的”下方点击“已购课程”，然后点击您计划取消的课程，如果您使用微信支付，课程款将会原路退回。
        </p>

        <p class="pic">
            <img src="https://m2.files.ivykids.cn/cloud01-file-8025768FugvDeWh_jOFAjr9bv38dGmWWbnc.png">
            <br/>
            <img src="https://m2.files.ivykids.cn/cloud01-file-8025768FnYqByjHQW0CIsMWA9_JiE7gfL63.png">
        </p>

        <p class="weui_msg_desc text-left">
            7. 如果您有多个孩子在艾毅幼儿园上学，您可以在报名界面里面看到一个“切换”按钮，点击该按钮，您所有在艾毅幼儿园就读的孩子都会显示出来，请点击每个孩子来完成报名。
        </p>

        <p class="weui_msg_desc text-left">
            如果您在报名或者付款时遇到任何问题，请拨打电话询问校园。
        </p>
    </div>

    <div class="weui_text_area" id="ePage" style="display: none">
        <p class="weui_msg_desc text-left">
            Please follow the instructions below to sign your child up for Mammoth ASA.
        </p>

        <p class="weui_msg_desc text-left">
            1. Please Scan the QR code below to enter the platform.
        </p>

        <div class="weui_icon_area"><img src="http://osspub.ivykids.cn/asa/qrcode.jpg"></div>
        <p class="weui_msg_desc text-left">
            2. If your wechat account has been linked to your Ivy schools Online account, you will enter the page below
            and click on your child’s name to enter the registration platform. If you have more than 1 child registered
            at Ivy schools, click any child’s name to enter the platform.
        </p>

        <p class="pic">
            <img src="https://m2.files.ivykids.cn/cloud01-file-8025768FvTrU62LQu_TxGoDNcJBUahYF_C0.png">
        </p>

        <p class="weui_msg_desc text-left">
            Note: If your wechat account has not been linked to your Ivy schools Online account, please use your child’s
            school registered phone number to verify and link to the account, or use your Ivy schools Online email
            account and password to complete this step.
        </p>

        <p class="pic">

            <img src="https://m2.files.ivykids.cn/cloud01-file-8025768FlKPJqD1Zxv2CmrxxGC8KsP2tjHD.png">


        </p>

        <p class="weui_msg_desc text-left">
            3. Please click an item under “Program” to view the current list of courses offered and click
            each course for the full description. Please note the dates listed for purchase.
        </p>

        <p class="pic">

            <img src="https://m2.files.ivykids.cn/cloud01-file-8025768FiZ9nq3CchA6VkeZ88gm6A6Aym4Z.png">

            <br>

            <img src="https://m2.files.ivykids.cn/cloud01-file-8025768FkZcvq4bLZ0Qrg_ZNGdB19HHDtae.png">

        </p>

        <p class="weui_msg_desc text-left">
            4. When the registration is available, please click “Enroll” to sign up. The payment needs to be made within
            15 minutes, otherwise the courses will be cancelled automatically and your need to sign up for the courses
            again.
        </p>

        <p class="pic">

            <img src="https://m2.files.ivykids.cn/cloud01-file-8025768FoLZzTpTrzCYAU-7CBfi8Jq0RjMX.png">

            <br>

            <img src="https://m2.files.ivykids.cn/cloud01-file-8025768FjPftbkRI3DNaYZMpSWg9NCTQf-1.png">

        </p>

        <p class="weui_msg_desc text-left">
            5. We only accept payment through wechat at the moment. If the total payment amount exceeds your bank
            account limit, please see the two options below to help solve the problem.<br>1) you may choose to purchase
            one course each time and pay for each course separately. The amount of each course normally will not exceed
            the payment limit.<br>2) you may top up your wechat wallet before the registration starts and use the wallet
            balance to pay for the courses. There is no limit for the wechat wallet when you process a payment.
        </p>

        <p class="weui_msg_desc text-left">
            6. Each course is fully refundable so long as the cancellation request is made prior to the due date of refund, which is listed in the description of the course. Please go to “Paid Invoices” under “Me” and click on the courses
            that you would like to cancel. The amount will be refunded to your bank account.
        </p>

        <p class="pic">
            <img src="https://m2.files.ivykids.cn/cloud01-file-8025768Funyq9kGH8wBdyk31C6cLvREZZnS.png">
            <br/>
            <img src="https://m2.files.ivykids.cn/cloud01-file-8025768Fq45OYFtRpo9-CD7nrUaBeIHRDNG.png">
        </p>

        <p class="weui_msg_desc text-left">
            7. If you have more than 1 child registered at Ivy schools, you will be able to see a “Switch” button on the
            registration page and by clicking on that button all your children that are registered at Ivy schools will
            appear. Click on each child individually to complete the registration.
        </p>

        <p class="weui_msg_desc text-left">
            If you have any questions related to the registration or the payment process, please contact our school.
        </p>
    </div>
</div>
<script>

    $('.eChange').on('click', function () {
        $('.cChange').removeClass('active');
        $(this).addClass('active');
        $('#cPage').hide();
        $('#ePage').show();
    });
    $('.cChange').on('click', function () {
        $('.eChange').removeClass('active');
        $(this).addClass('active');
        $('#ePage').hide();
        $('#cPage').show();
    });
</script>