<?php
$medicalCn = '';
$medicalEn = '';
$filedata = '';
$data = '';

if($configModel){
    $baseContentCn = ($configModel->content_cn) ? json_decode($configModel->content_cn) : '';
    $medicalCn = ($baseContentCn) ? nl2br(base64_decode($baseContentCn->medical_content)) : '';

    $baseContentCn = ($configModel->content_en) ? json_decode($configModel->content_en) : '';
    $medicalEn = ($baseContentCn) ? nl2br(base64_decode($baseContentCn->medical_content)) : '';
}
if($model){
    $data = json_decode($model->data);
    $filedata = ($data->filedata) ? $data->filedata : '' ;
}

$annexData = json_decode($regExtraInfo->data);

$medicalReport = array();
if($annexData->medicalReport){
    foreach ($annexData->medicalReport as $item){
        $medicalReport[] = RegExtraInfo::getOssImageUrl($item);;
    }
}
$vaccineReport = array();
if($annexData->vaccineReport){
    foreach ($annexData->vaccineReport as $item){
        $vaccineReport[] = RegExtraInfo::getOssImageUrl($item);;
    }
}
$healthReport = array();
if($annexData->healthReport){
    foreach ($annexData->healthReport as $item){
        $healthReport[] = RegExtraInfo::getOssImageUrl($item);;
    }
}

?>
<style>
.container-fluid{
    width:860px;
    margin:0 auto
}
.page{
    margin-top:20px
}
img{
    margin-top:10px;
    max-width:100%;
    height:auto
}
.bus{
	width:950px;
	margin:0 auto;
}
.sign{
    float: right;
    margin-right:20px;
    margin-bottom:20px
}
.sign img{
    width:100px;
}
</style>
<div class="container-fluid">
    <div class ="row">
    <?php if($data){ ?>
        <table class="table">
            <tbody>
            <tr>
                <th  width="25%">姓名</th>
                <td colspan="3"><?php echo $regExtraInfo->childProfile->getChildName(); ?></td>
            </tr>
            <tr>
                <th>出⽣⽇期</th>
                <td colspan="3"><?php echo $regExtraInfo->childProfile->birthday_search; ?></td>
            </tr>
            <tr>
                <th>班级</th>
                <td colspan="3"><?php echo $regExtraInfo->childProfile->ivyclass->title; ?></td>
            </tr>
            <tr>
                <th>意向医院1</th>
                <td colspan="3"><?php echo $data->preferredHospitalOne; ?></td>
            </tr>
            <tr>
                <th>意向医院2</th>
                <td colspan="3"><?php echo $data->preferredHospitalTwo; ?></td>
            </tr>
            <tr>
                <th>保险公司</th>
                <td colspan="3"><?php echo $data->insuranceCompany; ?></td>
            </tr>
            <tr>
                <th>紧急联系人1</th>
                <td colspan="3"><?php echo $data->oneEmergencyName; ?></td>
            </tr>
            <tr>
                <th>紧急联系人电话1</th>
                <td colspan="3"><?php echo $data->oneEmergencyPhone; ?></td>
            </tr>
            <tr>
                <th>紧急联系人2</th>
                <td colspan="3"><?php echo $data->twoEmergencyName; ?></td>

            </tr>
            <tr>
                <th>紧急联系人电话2</th>
                <td colspan="3"><?php echo $data->twoEmergencyPhone; ?></td>
            </tr>
            <tr>
                <th>多动症</th>
                <td><?php echo ($data->ADHD  == 1) ? '是' : '否' ; ?></span></td>
                <th width="20%">心脏病</th>
                <td><?php echo ($data->heartDisorder  == 1) ? '是' : '否' ; ?></td>
            </tr>
            <tr>
                <th>过敏（食品、药品、其他）</th>
                <td><?php echo ($data->allergies  == 1) ? '是' : '否' ; ?></td>
                <th>耳部疾病（听力）</th>
                <td><?php echo ($data->frequent  == 1) ? '是' : '否' ; ?></td>
            </tr>
            <tr>
                <th>哮喘</th>
                <td><?php echo ($data->asthma  == 1) ? '是' : '否' ; ?></td>
                <th>肝炎</th>
                <td><?php echo ($data->hepatitis  == 1) ? '是' : '否' ; ?></td>
            </tr>
            <tr>
                <th>背部或脊柱问题</th>
                <td><?php echo ($data->problems  == 1) ? '是' : '否' ; ?></td>
                <th>消化系统疾病</th>
                <td><?php echo ($data->gastrointertianl  == 1) ? '是' : '否' ; ?></td>
            </tr>
            <tr>
                <th>骨折</th>
                <td><?php echo ($data->fractures  == 1) ? '是' : '否' ; ?></td>
                <th>皮肤病</th>
                <td><?php echo($data->skinProblems  == 1) ? '是' : '否' ; ?></td>
            </tr>
            <tr>
                <th>糖尿病</th>
                <td><?php echo ($data->diabetes  == 1) ? '是' : '否' ; ?></td>
                <th>癫病</th>
                <td><?php echo ($data->seizureDisorde  == 1) ? '是' : '否' ;  ?></td>
            </tr>
            <tr>
                <th>视力/色觉问题</th>
                <td><?php echo ($data->visionProblems == 1) ? '是' : '否' ; ?></td>
                <th>肺结核</th>
                <td><?php echo ($data->tuberculosis == 1) ? '是' : '否' ; ?></td>
            </tr>
            <tr>
                <th>咳嗽、咳痰持续2周以上</th>
                <td><?php echo ($data->tuberculosisOne  == 1) ? '是' : '否' ;  ?></td>
                <th>反复咳出的痰中带血</th>
                <td><?php echo ($data->tuberculosisTwo == 1) ? '是' : '否' ; ?></td>
            </tr>
            <tr>
                <th>反复发热持续2周以上</th>
                <td><?php echo ($data->tuberculosisThree  == 1) ? '是' : '否' ;  ?></td>
                <th>经常见面的家人、亲戚、朋友中2年内是否有肺结核病人</th>
                <td><?php echo ($data->tuberculosisFour == 1) ? '是' : '否' ; ?></td>
            </tr>
            <tr>
                <th>过敏信息（请详述）（包括但不限于食物及药品） </th>
                <td colspan="3"><?php echo $data->specialFood; ?></td>
            </tr>
            <tr>
                <th>其他疾病</th>
                <td colspan="3"><?php echo $data->other; ?></td>
            </tr>
            <tr>
                <th>该学生有定期使用（口服或者注射）任何药物吗？如有，请详细说明。 </th>
                <td colspan="3"><?php echo $data->otherDiseases; ?></td>
            </tr>
            </tbody>
        </table>
    <?php } ?>
    </div>
    <div style="page-break-after:always;"></div>
    <div class="row">
        <div><?php echo $medicalCn; ?></div>
        <div><?php echo $medicalEn; ?></div>
        <div class="sign"><img src="<?php echo ($filedata) ? RegExtraInfo::getOssImageUrl($filedata) : ""?>" alt=""></div>
    </div>
    <div style="page-break-after:always;"></div>
    <div class="row">
        <?php
        if($medicalReport){
            foreach ($medicalReport as $val){
                ?>
                <p><img class="img-responsive" src="<?php echo $val; ?>"></p>
            <?php   }
        }
        ?>
        <?php
        if($vaccineReport){
            foreach ($vaccineReport as $val){
                ?>
                <p><img class="img-responsive" src="<?php echo $val; ?>"></p>
            <?php   }
        }
        ?>
        <?php
        if($healthReport){
            foreach ($healthReport as $val){
                ?>
                <p><img class="img-responsive" src="<?php echo $val; ?>"></p>
            <?php   }
        }
        ?>
    </div>
</div>