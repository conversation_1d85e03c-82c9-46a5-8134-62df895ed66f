<?php
	$calcFinal = true;
	$finalFactor1 = 0;
	$finalFactor2 = 0;
	
	echo CHtml::openTag('li', array("childid"=>$child->childid));
	echo CHtml::openTag("span", array("class"=>"name mr15"));
	$sts = $child->getSwitchStatus(false);
	echo CHtml::link($child->getChildName(), array("/child/index/index", "childid"=>$child->childid), array("target"=>"_blank"));
	if(isset($sts['txt'])){
		echo CHtml::openTag("i", array("class"=>"stats mr15"));
		echo $sts['txt'];
		echo CHtml::closeTag("i");
	}
	echo CHtml::closeTag("span");
	if($enabledConstantTuition):
		echo CHtml::openTag('span',array('class'=>'constant'));
		if(!empty($child->constant)){
			echo ($useStartYearOfSchool === true) ? $child->constant->startyear : $child->constant->amount;
			$finalFactor1 = $child->constant->amount;
		}
		else echo 0;
		echo CHtml::closeTag('span');
	else:
		$calcFinal = false;
	endif;
        if($child->bindDiscount->status == 1 && $child->bindDiscount->DiscountSchool->stat == 10){
		$discountTitle = CommonUtils::autoLang($child->bindDiscount->DiscountSchool->discountTitle->title_cn, $child->bindDiscount->DiscountSchool->discountTitle->title_en);
		echo CHtml::openTag('span',array('class'=>'discount mr5'));
		echo Yii::t("child", ":discountTitle (:discount)", array(":discountTitle"=>$discountTitle, ":discount"=>$child->bindDiscount->DiscountSchool->discount));
		echo CHtml::closeTag('span');
        }
        if ($useStartYearOfSchool === false){
		$finalFactor2 = $child->bindDiscount->DiscountSchool->discount;
		$final = round( ($finalFactor1 * $finalFactor2) / 100 );
		if($final > 0.01){
			echo CHtml::openTag('span',array('class'=>'final mr10'));
			echo Yii::t('payment', 'Final Amount: ') . OA::formatMoney($final);
			echo CHtml::closeTag('span');
		}
		if($child->bindDiscount->status == 1 && $child->bindDiscount->DiscountSchool->stat == 10){
			echo CHtml::openTag('span',array('class'=>'mr15'));
			echo CHtml::link(Yii::t('child','Cancel Discount'), array('//child/index/cancelBinding', 'childid'=>$child->childid,'bindingId'=>$child->bindDiscount->id), array('class'=>'btn J_dialog','title'=>Yii::t('child','Cancel Discount')));
			echo CHtml::closeTag('span');
		}
	}
	echo CHtml::closeTag('li');
?>