<?php
$this->breadcrumbs=array(
	'Ivy Ppackages'=>array('index'),
	'Manage',
);

$this->menu=array(
	array('label'=>'Create IvyPPackage', 'url'=>array('create')),
);
$colorbox = $this->widget('common.extensions.colorbox.JColorBox');
$colorbox->addInstance('.colorbox', array('iframe' => false, 'width' => '650', 'height' => '545'));
?>

<h1>Manage Ivy Ppackages</h1>

<?php $this->widget('zii.widgets.grid.CGridView', array(
	'id'=>'ivy-ppackage-grid',
	'dataProvider'=>$model->search(),
	'filter'=>$model,
	'columns'=>array(
		'id',
		'cn_title',
		'en_title',
		array(
            'name' => 'status',
         	'value' => '$data->showStatus()',
         	'filter'=>CHtml::activeDropDownList($model, 'status',array(Yii::t('operations', '显示'),Yii::t('operations', '隐藏')), array('empty'=>Yii::t("global", 'Please Select'))),
        ),
		'weight',
		array(
			'class'=>'CButtonColumn',
            'template' => '{view} {update} {delete}',
		 	'updateButtonUrl' => 'Yii::app()->createUrl("/operations/package/create", array("id" => $data->id))',
		 	'viewButtonUrl' => 'Yii::app()->createUrl("/operations/package/join", array("id" => $data->id))',
			'viewButtonOptions'=> array('class'=>'colorbox'),
		),
	),
)); ?>
