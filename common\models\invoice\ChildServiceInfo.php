<?php

/**
 * This is the model class for table "ivy_child_service_info".
 *
 * The followings are the available columns in table 'ivy_child_service_info':
 * @property integer $id
 * @property integer $invoice_id
 * @property integer $transaction_id
 * @property string $schoolid
 * @property integer $classid
 * @property integer $childid
 * @property integer $yid
 * @property string $payment_type
 * @property integer $startdate
 * @property integer $enddate
 * @property integer $mon
 * @property integer $tue
 * @property integer $wed
 * @property integer $thu
 * @property integer $fri
 */
class ChildServiceInfo extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return ChildServiceInfo the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_child_service_info';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('invoice_id, transaction_id, schoolid, classid, childid, yid, payment_type, startdate, enddate', 'required'),
			array('invoice_id, transaction_id, classid, childid, yid, startdate, enddate, mon, tue, wed, thu, fri', 'numerical', 'integerOnly'=>true),
			array('schoolid, payment_type', 'length', 'max'=>100),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, invoice_id, transaction_id, schoolid, classid, childid, yid, payment_type, startdate, enddate, mon, tue, wed, thu, fri', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'calendar'=> array(self::BELONGS_TO, 'Calendar', 'yid'),
            'childInfo' => array(self::BELONGS_TO, 'ChildProfileBasic', 'childid'),
            'classInfo' => array(self::BELONGS_TO, 'IvyClass', 'classid'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'invoice_id' => 'Invoice',
			'transaction_id' => 'Transaction',
			'schoolid' => 'Schoolid',
			'classid' => 'Classid',
			'childid' => 'Childid',
			'yid' => 'Yid',
			'payment_type' => 'Payment Type',
			'startdate' => 'Startdate',
			'enddate' => 'Enddate',
			'mon' => 'Mon',
			'tue' => 'Tue',
			'wed' => 'Wed',
			'thu' => 'Thu',
			'fri' => 'Fri',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('invoice_id',$this->invoice_id);
		$criteria->compare('transaction_id',$this->transaction_id);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('classid',$this->classid);
		$criteria->compare('childid',$this->childid);
		$criteria->compare('yid',$this->yid);
		$criteria->compare('payment_type',$this->payment_type,true);
		$criteria->compare('startdate',$this->startdate);
		$criteria->compare('enddate',$this->enddate);
		$criteria->compare('mon',$this->mon);
		$criteria->compare('tue',$this->tue);
		$criteria->compare('wed',$this->wed);
		$criteria->compare('thu',$this->thu);
		$criteria->compare('fri',$this->fri);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
	
	/**
	 * 取得孩子符合条件的交费历史
	 * @param int $childid
	 * @param string $feeType
	 * @param int $currentdate
	 * @return object
	 * <AUTHOR>
	 */
	public function getChildFeeInfo($childid,$feeType,$calendarId,$currentdate,$tag = false)
	{
		$criteria = new CDbCriteria;
		$criteria->compare('childid', $childid);
		$criteria->compare('payment_type', $feeType);
		$criteria->compare('yid', $calendarId);
		if ($tag == true)
		{
			$criteria->addCondition("startdate<=".$currentdate);
			$criteria->addCondition("enddate>=".$currentdate);
		}
		else 
		{
			$criteria->addCondition("enddate>=".$currentdate);
		}
		
		$criteria->order = "enddate ASC";
		return $this->findAll($criteria);
	}
    
    public function getCurrentDateFeeInfo($childid,$feeType,$currentdate){
        $currentdate = strtotime($currentdate);
        $criteria = new CDbCriteria;
		$criteria->compare('childid', $childid);
		$criteria->compare('payment_type', $feeType);
        $criteria->addCondition("startdate<=".$currentdate);
		$criteria->addCondition("enddate>=".$currentdate);
        return $this->model()->with('calendar')->find($criteria);
    }
}