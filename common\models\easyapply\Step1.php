<?php

/**
 * ContactForm is the model behind the contact form.
 */
class Step1
{
    public $apply_id;
    public $school_id;
    public $schoolyear_id;
    public $grade_id;
    public $study_history;
    public $is_staff;
    public $xueji;
    public $house;  // 居住情况
    public $social_security;  // 社保情况
    public $parentsSituation;  // 居住证情况
    public $visa_type;  // 签证情况
    public $check_type;  // 住宿情况
    public $many_children;
    public $has_child_study;
    public $notice;

    public function processData($apply, $child)
    {

        $studyHistory = array();
        if($child->school_history){
            $adchildHistory = json_decode($child->school_history);
            foreach ($adchildHistory as $val){
                $studyHistory[] = array(
                    'school' => $val->school,
                    'city' => $val->location,
                    'studyDate' => $val->studytime,
                    'grade' => $val->grade,
                    'course' => $val->curriculum,
                    'language' => $val->language,
                );
            }
        }

        $this->apply_id = $apply->id;
        $this->school_id = $apply->school_id;
        $this->schoolyear_id = $apply->schoolyear_id;
        $this->grade_id = $apply->grade_id;
        $this->study_history = json_encode($studyHistory);
        $this->is_staff = $child->is_staff;

        return json_encode($this);
    }
}
