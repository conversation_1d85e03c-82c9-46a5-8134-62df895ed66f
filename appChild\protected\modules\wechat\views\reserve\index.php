<script src="<?php echo Yii::app()->theme->baseUrl; ?>/js/vue.js"></script>
<style>
    .m15{
    	margin: 15px 0
    }
    [v-cloak]{display: none;}
    .tableList{
   		width: 100%;
   		border-width: 1px;
		border-color: #999999;
		border-collapse: collapse;
		margin:15px 0;
		color: #666
    }
	table, td, th {
	    text-align: center;
	    height:40px;
        position:relative;
	}
	.weui_article{
	    padding: 5px 15px;
	}
	.weui_icon_waiting_circle:before{
		color: #09BB07
	}
    .gray{
    	background:#999999;
    }
    .green{
    	background:#68BA00;
    }
    .blue{
    	background:#3A87AD;
    }
    .state{
    	width:8px;
    	height:8px;
    	display: inline-block;
    	border-radius:4px;
    }
    .date{
    	position:absolute;
    	bottom:2px;
    	left:50%;
    	margin-left: -4px
    }
    .stateText{
    	margin:0 10px
    }
    .trbg{
    	background: #F8F8F8
    }
    .bluepot{
    	position:absolute;
    	right:40%;
    	bottom:2px;
    	margin-right: -4px
    }
    .greenpot{
		position:absolute;
    	left:40%;
    	bottom:2px;
    	margin-left: -4px
    }
    .btn{
    	background: #54A9F7 !important;
    	border-radius: 50%;
    	padding:8px 9px;
    	color: #fff;
    	z-index:999;
    }
</style>
<div class="weui_cells_title"><?php echo Yii::t("support", "Parent Meeting Appointment"); ?></div>
<div class="weui_panel" style="margin: 0">
    <?php
    //选择孩子
    $this->widget('ext.wechat.ChildSelector', array(
        'childObj' => $this->childObj,
        'myChildObjs' => $this->myChildObjs,
        'jumpUrl' => 'index',
    ));
    ?>
</div>
<div class="tabbar" id="parent"  v-cloak>
	<div v-if='onTime!=""'>
	    <div class="weui_panel">
        <div class="weui_cell">
            <div class="weui_cell_hd"></div>
            <div class="weui_cell_bd weui_cell_primary">
                <p><?php echo Yii::t("support", "Your Scheduled Appointment"); ?></p>
            </div>
        </div>
        <div class="weui_cell">
            <div class="weui_cell_hd"></div>
            <div class="weui_cell_bd weui_cell_primary">
                <p><i class="weui_icon_waiting_circle"></i> {{month}}</p>
            </div>
            <div class="weui_cell_ft">{{datalist.time}}</div>
        </div>
    </div>
	     <div class="weui_cells_title"><?php echo Yii::t("support", "Scheduled appointment cannot be modified online within 2 days of the reserved time slot; please contact the lead teacher directly to reschedule in this situation"); ?></div>
	    <div class="bd spacing m15">
	       <a href="javascript:;" class="weui_btn weui_btn_disabled weui_btn_primary" v-if='updateStatus==1'>
	       <?php echo Yii::t("support", "Reschedule"); ?></a>
	        <a href="javascript:;" class="weui_btn weui_btn_primary" v-else @click='modify()'><?php echo Yii::t("support", "Reschedule"); ?></a>
	    </div>
	</div>
	<div v-else>
		<div class="bottom" v-if='week.length>0'>
			<div class="weui_panel ">
			    <div class="weui_cells_title">
				    <span class="gray state"></span><span class="stateText"><?php echo Yii::t("support", "Occupied"); ?></span>
				    <span class="green state"></span><span class="stateText"><?php echo Yii::t("support", "Available"); ?></span>
				    <span class="blue state"></span><span class="stateText"><?php echo Yii::t("support", "My Appointment"); ?></span>
			    </div>
		    </div>
			<div class=""  v-for='(dates,id) in week'>
				<div class="weui_cells_title">{{monlist[id]}}</div>
				<div class="weui_panel">
					<div class="weui_article">
				    	<table class="tableList">
					    	<thead>
					    		<tr>
					    		    <th><?php echo Yii::t("support", "S"); ?></th>
					    		    <th><?php echo Yii::t("support", "M"); ?></th>
					    		    <th><?php echo Yii::t("support", "T"); ?></th>
					    		    <th><?php echo Yii::t("support", "W"); ?></th>
					    		    <th><?php echo Yii::t("wechat", "T"); ?></th>
					    		    <th><?php echo Yii::t("support", "F"); ?></th>
					    		    <th><?php echo Yii::t("wechat", "S"); ?></th>
					    		</tr>
				    		</thead>
				    		<tbody>
				    			<tr v-for='(days,index) in dates' >
				    			<template v-for='(dayss,indexs) in days'>
						    		<template v-if='data[dayss]'>
						    		    <template>
						    		    	<template v-if='busyTime[dayss]'>
												<td width="50" @click='datedays(dayss)'>
													<span :class="{ 'gray state date' : classmon != dayss}"></span>
													<span  :class="{ 'btn' : classmon == dayss}">{{String(dayss).substring(6,8)}}</span>
												</td>
						    		    	</template>
						    		    	<template v-else>
						    		    		<template  v-if='busydefault[dayss]==defaults[dayss]'>
						    		    			<td width="50" v-if='ondate==dayss' @click='datedays(dayss)'>
							    		    			<span :class="{ 'green state greenpot' : classmon != dayss}"></span>
							    		    			<span  :class="{ 'blue state bluepot' : classmon != dayss}"></span>
							    		    			<span :class="{ 'btn' : classmon == dayss}">{{String(dayss).substring(6,8)}}</span>
						    		    			</td>
						    		    			<td width="50" v-else @click='datedays(dayss)'>
							    		    			<span :class="{ 'green state date' : classmon != dayss}"></span>
							    		    			<span  :class="{ 'btn' : classmon == dayss}">{{String(dayss).substring(6,8)}}</span>
						    		    			</td>
												</template>
												<template v-else>
													<td width="50"  @click='datedays(dayss)'>
														<span :class="{ 'gray state date' : classmon != dayss}"></span>
														<span :class="{ 'btn' : classmon == dayss}">{{String(dayss).substring(6,8)}}</span>
													</td>
												</template>
						    		    	</template>
						    		    </template>
						    		</template>
						    		<template v-else>
						    			<td width="50" v-if='dayss!=0' style="color:#D0D0D0">{{String(dayss).substring(6,8)}}</td>
						    			<td width="50" v-else></td>
						    		</template>
				    			</template>
				    			</tr>
				    		</tbody>
				    	</table>
			    	</div>
		    	</div>
		    </div>
	    </div>
	    <div v-else>
	    	<?php echo $this->renderPartial('../bind/_null', array('text' => Yii::t("support", "No available time slot currently (PTC are generally held at the end of each semester)."), 'title' => Yii::t("survey", "No Data") )); ?>
	    </div>
	    <div class="bd" style="margin-top: 10px" v-if='week.length>0'>
	        <template v-if="isRouterAlive">
	        	<div class="weui_cells_title"><?php echo Yii::t("support", "My Appointment"); ?></div>
	            <div class="weui_panel" >
			        <div class=" weui_cells_access" v-if='datalist.length!=0'>
				        <a class="weui_cell" href="javascript:;" @click='datedays(month.substring(0,4)+month.substring(5,7)+month.substring(8,10))'>
				            <div class="weui_cell_bd weui_cell_primary">
				                <p><i class="weui_icon_waiting_circle"></i> {{month}}</p>
				            </div>
				            <div class="weui_cell_ft">{{datalist.time}}</div>
				        </a>
				    </div>
			        <div class="weui_cell" v-else>
			            <div class="weui_cell_bd weui_cell_primary">
			                <p><?php echo Yii::t('support','Please make your appointment in green tagged date.') ?></p>
			            </div>
			        </div>
		        </div>

	        	<div class="weui_cells_title"  v-if='language!=1'><?php echo Yii::t('support','Our teachers will share their observations of your child\'s strength, interests and challenges with you during the conference. If there is anything else that you would like to discuss with the teachers, please indicate below:') ?></div>
	        	<div class="weui_cells_title"  v-if='language==1'><?php echo Yii::t('support','Our teachers will share their observations of your child\'s strength, interests and challenges with you during the conference.') ?></div>
		        <div class="weui_cells weui_cells_form" v-if='language!=1'>
			        <div class="weui_cell">
			            <div class="weui_cell_bd weui_cell_primary">
			                <textarea class="weui_textarea" placeholder="<?php echo Yii::t('support','please indicate below:') ?>" rows="3" id='memo' :value='memo'></textarea>
			            </div>
			        </div>
			    </div>
		    </template>
	     	<div class="bd spacing m15">
	           <a href="javascript:;" class="weui_btn weui_btn_primary subbtn" @click="subbtn()"><?php echo Yii::t("global", "Submit"); ?></a>
	        </div>
	    </div>
	    <div id="actionSheet_wrap">
		    <div class="weui_mask_transition" id="mask"></div>
		    <div class="weui_actionsheet" id="weui_actionsheet" style="max-height: 100%;overflow-y:scroll">
		        <div class="weui_actionsheet_menu">
		        	<div class="weui_actionsheet_cell">{{maskmon}}</div>
		            <div class="" v-for='(dates,index) in masklist'>
						 <div class="weui_cells weui_cells_radio" style="margin-top:0">   
		                	<label class="weui_cell weui_check_label"   v-if='dates.name==0 && dates.default==0 && dates.status==0'  @click='pitchons(dates.time)'>
					            <div class="weui_cell_bd weui_cell_primary">
					                <p><i class="weui_icon_waiting_circle"></i> {{dates.time}}</p>
					            </div>
					            <div class="weui_cell_ft">
					                <input type="radio" class="weui_check" name="radio1" v-model="checked"  :value='dates.id'>
					                <span class="weui_icon_checked"></span>
					            </div>
					        </label>
					        <label class="weui_cell weui_check_label"   v-if='dates.default==1'  @click='pitchons(dates.time)'>
					            <div class="weui_cell_bd weui_cell_primary">
					                <p><i class="weui_icon_waiting_circle"></i> {{dates.time}}</p>
					            </div>
					            <div class="weui_cell_ft">
					                <input type="radio" class="weui_check" name="radio1" v-model="checked"  :value='dates.id'>
					                <span class="weui_icon_checked"></span>
					            </div>
					        </label>
					        <label class="weui_cell weui_check_label" v-if='dates.default==0 && dates.name!=0'>
					            <div class="weui_cell_bd weui_cell_primary">
					                <p style="color: #888"><i class="weui_icon_waiting_circle"></i> {{dates.time}}</p>
					            </div>
					            <div class="weui_cell_ft">Busy</div>
					        </label>
					         <label class="weui_cell weui_check_label"  v-if='dates.status==1 && dates.name==0'>
					            <div class="weui_cell_bd weui_cell_primary">
					                <p style="color: #888"><i class="weui_icon_waiting_circle"></i> {{dates.time}}</p>
					            </div>
					            <div class="weui_cell_ft">Busy</div>
					        </label>
					    </div>
		            </div>
		        </div>
		        <div class="weui_actionsheet_action">
		            <div class="weui_actionsheet_cell" id="actionsheet_cancel"><?php echo Yii::t("global", "Cancel"); ?></div>
		        </div>
		    </div>
		</div>
		<div id="toast" style="display: none;">
		    <div class="weui_mask_transparent"></div>
		    <div class="weui_toast">
		        <i class="weui_icon_toast"></i>
		        <p class="weui_toast_content"><?php echo Yii::t("wechat", "Submitted Successfully"); ?></p>
		    </div>
		</div>   
	</div>
	<div class="weui_dialog_alert" id="dialog" style="display: none">
	    <div class="weui_mask"></div>
	    <div class="weui_dialog">
	        <div class="weui_dialog_hd"><strong class="weui_dialog_title"><?php echo Yii::t("payment", "Tips"); ?></strong></div>
	        <div class="weui_dialog_bd">{{mess}}</div>
	        <div class="weui_dialog_ft">
	            <a href="javascript:;" class="weui_btn_dialog primary" @click='dialog()'><?php echo Yii::t("wechat", "OK"); ?></a>
	        </div>
	    </div>
	</div>
	<div id="loadingToast" class="weui_loading_toast" style="display: none;">
	    <div class="weui_mask_transparent"></div>
	    <div class="weui_toast">
	        <div class="weui_loading">
	            <div class="weui_loading_leaf weui_loading_leaf_0"></div>
	            <div class="weui_loading_leaf weui_loading_leaf_1"></div>
	            <div class="weui_loading_leaf weui_loading_leaf_2"></div>
	            <div class="weui_loading_leaf weui_loading_leaf_3"></div>
	            <div class="weui_loading_leaf weui_loading_leaf_4"></div>
	            <div class="weui_loading_leaf weui_loading_leaf_5"></div>
	            <div class="weui_loading_leaf weui_loading_leaf_6"></div>
	            <div class="weui_loading_leaf weui_loading_leaf_7"></div>
	            <div class="weui_loading_leaf weui_loading_leaf_8"></div>
	            <div class="weui_loading_leaf weui_loading_leaf_9"></div>
	            <div class="weui_loading_leaf weui_loading_leaf_10"></div>
	            <div class="weui_loading_leaf weui_loading_leaf_11"></div>
	        </div>
	        <p class="weui_toast_content"><?php echo Yii::t("wechat", "Submission is in process"); ?></p>
	    </div>
	</div>
</div>
<script>
    var datas = <?php echo json_encode($data); ?>;
    var timeTable = <?php echo json_encode($timeTable); ?>;
    var memo = <?php echo json_encode($memo); ?>;
    var language='<?php echo $momeStatus; ?>';
	var parent = new Vue({
	        el: "#parent",
	        data: {
	            data: datas,//可预约数据
	            mess:'',//弹出框显示
	            memo:memo,//备注
	            isRouterAlive:true,
	            week:timeTable,//日历数据
	            datalist:'',//选中的数据
	            monlist:'',//月份
	            month:'',//选中的日期
	            backup:timeTable,
	            onTime:'',//预约的时间显示
	            ondate:'',//判断显示
	            masklist:'',//actionSheet_wrap列表
	            busyTime:'',//不可预约时间
	            busydefault:'',//已预约满的月份
	            defaults:'',//可预约的月份
	            hisclass:'',//历史存入日期
	            maskmon:'',//点击的日期
	            checked:'',//选中的id
	            classmon:'',//是否超过48小时
	            updateStatus:'',
	            classtd:'',
	            hosid:'',
	            hosmon:'',
	            language:language,
	        }, 
	        created: function () {
	        	var mondata=this.backup
	        	var mons=[]
	        	for(var i=0;i<mondata.length;i++){
        			for(var j=0;j<mondata[i].length;j++){
        				for(var k=0;k<mondata[i][j].length;k++){
        					if(mondata[i][j][k]!=0){
        						mons.push(String(mondata[i][j][k]).substring(0,4)+'-'+String(mondata[i][j][k]).substring(4,6))
        					}
        				}
        			}
        		}
        		var time = mons.filter(function(element,index,self){
	                return self.indexOf(element) === index;
	             });
        		//月份
        		this.monlist=time
        		var data={}
        		var busydefault={}
        		var defaults={}
        		var busydata={}
        		for(key in this.data){
        			for(var j=0;j<this.data[key].length;j++){
        				//default==1当前点击
        				if(this.data[key][j].default==1){
							this.month=String(key).substring(0,4)+'-'+String(key).substring(4,6)+'-'+String(key).substring(6,8)
							this.onTime=key
							this.hosid=this.data[key][j]
							this.hosmon=String(key).substring(0,4)+'-'+String(key).substring(4,6)+'-'+String(key).substring(6,8)
							this.updateStatus=this.data[key][j].updateStatus
							this.datalist=this.data[key][j]
							this.checked=this.data[key][j].id
        				}
        				//status==1时间段超过的
        				if(this.data[key][j].status==1){
        					busydata[key]=this.data[key]
        				} 
        				//区分预约完的和没预约的
        				if(this.data[key][j].status==0){   
        					busydefault[key]=this.data[key]          
                        	if(this.data[key][j].default==1 || this.data[key][j].name==0){
                        		 defaults[key]=this.data[key]
                        	} 
        				}
        			}
        		}
        		this.busydefault=busydefault
        		this.defaults=defaults		
        		this.busyTime=busydata
	        },
	        update: function () {
	        },
	        methods: {
	        	modify(){
					this.ondate=this.onTime
					this.onTime=''
	        	},
	        	datedays(date){
	        		var mask = $('#mask');
	                var weuiActionsheet = $('#weui_actionsheet');
	                weuiActionsheet.addClass('weui_actionsheet_toggle');
	                mask.show()
	                .focus()//加focus是为了触发一次页面的重排(reflow or layout thrashing),使mask的transition动画得以正常触发
	                .addClass('weui_fade_toggle').one('click', function () {
                		var classdate=parent.month.substring(0,4)+parent.month.substring(5,7)+parent.month.substring(8,10)
                		if(parent.classtd!=''){
                		 	parent.classmon=classdate
                		}else{
                			parent.classmon=''	
                		}
	                    parent.$options.methods.hideActionSheet(weuiActionsheet, mask);
	                });
	                mask.unbind('transitionend').unbind('webkitTransitionEnd');
	                $('#actionsheet_cancel').one('click', function () {
	                	var classdate=parent.month.substring(0,4)+parent.month.substring(5,7)+parent.month.substring(8,10)
                		if(parent.classtd!=''){
                		 	parent.classmon=classdate
                		}else{
                			parent.classmon=''	
                		}
	                  	parent.$options.methods.hideActionSheet(weuiActionsheet, mask);
	                });
	                for(key in this.data){
	        			if(date==key){
	        				this.hisclass=date
	        				if(date!=this.ondate){
	                			parent.classmon=date
	                		}
        					this.classmon=date
	        				this.maskmon=String(date).substring(0,4)+'-'+String(date).substring(4,6)+'-'+String(date).substring(6,8)
	        				this.masklist=this.data[key]
	        				return
	        			}else{
	        				this.masklist=''
	        			}
	        		}	
	        	},
	        	pitchons(time){
	        		$('.container').scrollTop($('#parent').height())
	        		var mask = $('#mask');
	        		this.classtd=this.hisclass
	                var weuiActionsheet = $('#weui_actionsheet');
	        		parent.$options.methods.hideActionSheet(weuiActionsheet, mask);
	        		this.month=String(this.hisclass).substring(0,4)+'-'+String(this.hisclass).substring(4,6)+'-'+String(this.hisclass).substring(6,8)
	        		for(key in this.data){
	        			if(key==this.hisclass){
	        				for(var i=0;i<this.data[key].length;i++){
		    					if(time==this.data[key][i].time){
		    						this.datalist=this.data[key][i]
		    					}
		    				}
	        			}
	        		}
	        	},
	        	hideActionSheet(weuiActionsheet, mask) {
                    weuiActionsheet.removeClass('weui_actionsheet_toggle');
                    mask.removeClass('weui_fade_toggle');
                    mask.on('transitionend', function () {
                        mask.hide();
                    }).on('webkitTransitionEnd', function () {
                        mask.hide();
                    })
                },
		        subbtn(){
		        	$('.subbtn').attr('disabled',true);
		        	if(this.checked==''){
		        		this.mess='<?php echo Yii::t("support", "Please click on the desired time slot in green to schedule an appointment."); ?>'
		        		$('#dialog').show()
		        		$('.subbtn').attr('disabled',false);
		        		return
		        	}	
		        	$('#loadingToast').show()
		        	$.ajax({
		        		url: "<?php echo $this->createUrl('update')?>",
		        		type: 'post',
		        		dataType: 'json',
		        		data: {unikey:this.checked,memo:$('#memo').val()},
		        		success:function(data){
	                       if(data.state=='success'){
	                       	    $('#loadingToast').hide()
				        		$('#toast').show();
							    setTimeout(function () {
							        $('#toast').hide()
							        location.reload()
							    }, 2000);
							}
						    if(data.state=='fail'){
				                 parent.datalist=[]
			        			parent.mess=data.message
						    	$('#dialog').show()
						    	$('.subbtn').attr('disabled',false);
						    	$('#loadingToast').hide()
						    }
		        		},
	                    error:function(data) {
	                    	parent.mess='请求错误'
					    	$('#dialog').show()
					    	$('.subbtn').attr('disabled',false);
					    	$('#loadingToast').hide()
	                    }
		        	})	
				},
				dialog(){
					if(this.datalist.length==0){
						location.reload()
					}
					$('#loadingToast').hide()
					$('#dialog').hide()
				}
	        }
	    })
</script>