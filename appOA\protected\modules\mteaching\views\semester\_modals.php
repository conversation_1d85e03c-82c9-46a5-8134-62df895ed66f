<?php
$learningDomains = Term::model()->ld()->findAll();
?>

<!-- ld-add Modal: Learning Domain Modal -->
<div class="modal" id="ldAddModal" tabindex="-1" role="dialog" aria-labelledby="ldAddModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title" id="ldAddModalLabel">Learning Domain <small></small></h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-12">
                        <div class="well">
                            排序数字越小排序越靠前，设置完相关信息后请点击保存
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div>
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" value="Learning Domain 1"> Learning Domain 1
                                </label>
                            </div>
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" value="Learning Domain 2"> Learning Domain 2
                                </label>
                            </div>
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" value="Learning Domain 3"> Learning Domain 3
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">


                    </div>
                    <div class="col-md-12">
                        <div class="pop_bottom">
                            <button onclick="$('#ldAddModal').modal('hide')" type="button" class="btn btn-default pull-right"><?php echo Yii::t('global','Cancel');?></button>
                            <button onclick="test();" type="submit" class="btn btn-primary J_ajax_submit_btn pull-right mr10"><?php echo Yii::t('global','Submit');?></button>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>


<!-- lp-add Modal: Learning Project Modal -->
<div class="modal" id="lpAddModal" tabindex="-1" role="dialog" aria-labelledby="lpAddModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title" id="lpAddModalLabel">Learning Domain <small></small></h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-12">
                        <div class="well">
                            排序数字越小排序越靠前，设置完相关信息后请点击保存
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div>
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" value="Learning Domain 1"> Learning Project 1
                                </label>
                            </div>
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" value="Learning Domain 2"> Learning Project 2
                                </label>
                            </div>
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" value="Learning Domain 3"> Learning Project 3
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">


                    </div>
                    <div class="col-md-12">
                        <div class="pop_bottom">
                            <button onclick="$('#lpAddModal').modal('hide')" type="button" class="btn btn-default pull-right"><?php echo Yii::t('global','Cancel');?></button>
                            <button onclick="test2();" type="submit" class="btn btn-primary J_ajax_submit_btn pull-right mr10"><?php echo Yii::t('global','Submit');?></button>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>


<script>
    var test = test2 = null;
    $(function(){
        openLearningDomainModal = function(){
            $('#ldAddModal').modal();
        };

        openLearningProjectModal = function(){
            $('#lpAddModal').modal();
        };

        test = function(){
            $('#ldAddModal').modal('hide');
            childReportData['learning-domains'].push({title:'Test new 1#',items:[],new:true});
            childReportData['learning-domains'].push({title:'Test new 2#',items:[],new:true});
            childReportData['learning-domains'].push({title:'Test new 3#',items:[],new:true});
            childReportData['learning-domains'].push({title:'Test new 4#',items:[],new:true});

            drawTreeView();
        }

        test2 = function(){
            $('#lpAddModal').modal('hide');
            childReportData['learning-projects'].push({title:'Test new project 1#',items:[],text: 'blablabla',new:true});
            childReportData['learning-projects'].push({title:'Test new project 2#',items:[],text: 'blablabla',new:true});
            childReportData['learning-projects'].push({title:'Test new project 3#',items:[],text: 'blablabla',new:true});
            childReportData['learning-projects'].push({title:'Test new project 4#',items:[],text: 'blablabla',new:true});

            drawTreeView();
        }
    })
</script>