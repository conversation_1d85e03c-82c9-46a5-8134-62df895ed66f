<style>
    [v-cloak] {
        display: none;
    }
    .p8{
        padding:8px
    }
    .input{
        margin:8px 0
    }
    .dept{
        font-size: 14px;
        color: #333;
        padding: 8px;
    }
    .dept:hover{
        background: #F2F3F5;
        border-radius: 4px;
        cursor: pointer;
    }
    .overFlow{
        max-height:240px;
        margin-top:8px
    }
    .scroll-box::-webkit-scrollbar-thumb {
        /*滚动条里面小方块*/
        border-radius   : 10px;
        background-color: #ccc !important;
        background-image: none !important
    }
    .scroll-box::-webkit-scrollbar-track {
        /*滚动条里面轨道*/
        box-shadow   : inset 0 0 5px rgba(0, 0, 0, 0);
        background   : #fff;
        border-radius: 10px;
        border:none
    }
</style>
<link href="<?php echo Yii::app()->theme->baseUrl; ?>/css/bootstrap.css" rel="stylesheet">
<link href="<?php echo Yii::app()->theme->baseUrl; ?>/css/css.css" rel="stylesheet">
<link href="<?php echo Yii::app()->themeManager->baseUrl; ?>/base/js/element/index.css" rel="stylesheet">
<div class="p8" id='container'>
    <div class='' >
        <div v-if='initDataList.length!=0 && !loading'>
            <el-input placeholder="<?php echo Yii::t("directMessage", "Filter by text"); ?>" size='small' v-model='searchText' clearable></el-input>
            <div v-if='dataList.length==0'>
             <el-empty description="<?php echo Yii::t("ptc", "No Data"); ?>" :image-size="100"></el-empty>
            </div>
            <div class='overFlow overflow-y scroll-box ' v-else>
                <div class='dept' v-for='(list,index) in  dataList' @click='checked(list.id)'>
                    {{list.name}}
                </div>
            </div>
        </div>
        <div v-else-if='!loading'>
            <div class="alert alert-warning mt20" role="alert"><?php echo Yii::t("directMessage", "Not available in this campus"); ?></div>
        </div>
    </div>
</div>
<script src="<?php echo Yii::app()->themeManager->baseUrl; ?>/base/js/element/vue.global.js"></script>
<script src="<?php echo Yii::app()->themeManager->baseUrl; ?>/base/js/element/index.js"></script>
<script>
    var calendarInfo = <?php echo json_encode($calendarInfo); ?>;
    var yid = '<?php echo $yid; ?>';
   
     var container = new Vue({
        el: "#container",
        data: {
            copydataList:[],
            checkData:{},
            searchText:'',
            loading:true,
            initDataList:[],
        },
        watch: {
            searchText(){

            }
        },
        computed: {
            dataList: function() {
                var search = this.searchText;
                var searchVal = ''; //搜索后的数据
                if(search) {
                    searchVal =this.copydataList.filter(function(product) {
                        return Object.keys(product).some(function(key) {
                            return String(product['name'].toLowerCase()).indexOf(search.toLowerCase()) !== -1;
                        })
                    })
                    return searchVal;
                }
                return this.copydataList;
            },
        },
        created: function() {
            window.parent.postMessage({
                mceAction: 'loading',
                content: {}
            }, '*');
            this.getInit()
        },
        methods: {
            getInit(){
                let that=this
                this.loading=true
                $.ajax({
                    url: '<?php echo $this->createUrl("/mcampus/dept/deptChoseList") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.initDataList=data.data
                            that.loading=false
                            that.copydataList=JSON.parse(JSON.stringify(data.data));
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            that.loading=false
                        }
                    },
                    error: function(data) {
                        that.loading=false
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            checked(id){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("/mcampus/dept/deptQrcode") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        deptId:id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                           that.checkData=data.data.qrcode
                           that.insertImg()
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
                
            },
            insertImg(){
                let that=this
                if((/Safari/.test(navigator.userAgent) && !/Chrome/.test(navigator.userAgent))){
                    window.parent.postMessage({
                        mceAction: 'insertImg',
                        content:that.checkData
                    }, '*'); 
                }else{
                    window.parent.postMessage({
                        mceAction: 'insertImg',
                        content: that.checkData
                    }, '*');
                }
            },
        }
    })
</script>
