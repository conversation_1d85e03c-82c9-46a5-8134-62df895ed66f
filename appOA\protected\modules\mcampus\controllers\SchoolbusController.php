<?php

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
class SchoolbusController extends BranchBasedController
{
    public $actionAccessAuths = array(
        'index'                     => 'o_A_Access',
        'ExportChildBySchoolbus'    => 'o_A_Adm_Support',
        'AssignChildForSchoolbus'   => 'o_A_Adm_Support',
        'DeteteChildBySchoolbus'    => 'o_A_Adm_Support',
        'UpdateChildBySchoolbus'    => 'o_A_Adm_Support',
        'SaveSchoolbus'             => 'o_A_Adm_Support',
        'DeleteSchoolbus'           => 'o_A_Adm_Support',
    );

    public $dialogWidth;
    public function createUrl($route, $params = array(), $ampersand = '&'){
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        return parent::createUrl($route, $params, $ampersand);
    }

    public function init(){
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'campusOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site', 'Campus Operations');

        //初始化选择校园页面
        $this->branchSelectParams['hideOffice'] = true;
        $this->branchSelectParams['urlArray'] = array('//mcampus/schoolbus/index','category'=>'current');
        Yii::import('common.models.schoolbus.*');
    }

    /*
     * 获取某一学年已交校车费的孩子、已分配校车的孩子
     */
    public function actionIndex(){
        $startYear = Yii::app()->request->getParam('startYear',0);
        $calendarList = $this->getCalendars();
        $calendarStartYear = array_flip($this->calendarStartYear);
        $currentSchoolYear = $this->calendarStartYear[$calendarList['currentYid']];
        $schoolbusList = null;
        $childInfo = null;
        $childBySchoolbusList = null;
        if ($startYear){
            //get schoolbus info
            $schoolbusList = $this->getSchoolBus($this->branchId);
            if ($startYear >= $currentSchoolYear){
                //取当前学年及下学年已交校车费的孩子
                Yii::import('common.models.invoice.Invoice');

                $crite = new CDbCriteria;
                $crite->compare('t.calendar_id', $calendarStartYear[$startYear]);
                $crite->compare('t.schoolid', $this->branchId);
                $crite->compare('t.payment_type', 'bus');
                $crite->compare('t.inout', 'in');
                $crite->compare('t.status', array(Invoice::STATS_PAID,Invoice::STATS_UNPAID,Invoice::STATS_PARTIALLY_PAID));
                $crite->index = 'childid';
                $crite->group = 't.childid';
                $childList = Invoice::model()->with('childprofile')->findAll($crite);
                if (!empty($childList)){
                    foreach ($childList as $val){
                        $childInfo[$val->childid]['childName'] = $val->childprofile->getChildName();
                        $childInfo[$val->childid]['compound'] = $val->childprofile->compound;
                    }
                }
            }
            //取已选学年已分配校车的孩子相关信息
            $crite = new CDbCriteria;
            $crite->compare('t.startyear', $startYear);
            $crite->compare('t.schoolid', $this->branchId);
            $crite->order = 't.time1 ASC';
            $childByschoolbus = SchoolBusChild::model()->with('childinfo')->findAll($crite);
            if (!empty($childByschoolbus)){
                foreach ($childByschoolbus as $val){
                    $childBySchoolbusList[$val['busid']][] = array(
                        'id'=>$val->id,
                        'childid'=>$val->childid,
                        'childName'=>CHtml::encode($val->childinfo->getChildName()),
                        'time1'=>CHtml::encode($val['time1']),
                        'time2'=>CHtml::encode($val['time2']),
                        'addr1'=>CHtml::encode($val['addr1']),
                        'addr2'=>CHtml::encode($val['addr2']),
                        'remark'=>CHtml::encode($val['remark']),
                    );
                }
            }
        }
        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/underscore-min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/backbone-min.js');
        $cs->registerCoreScript('jquery.ui');
        $cs->registerCssFile($cs->getCoreScriptUrl().'/jui/css/base/jquery-ui.css');
        $cs->registerScriptFile($cs->getCoreScriptUrl().'/jui/js/jquery-ui-i18n.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/phpfunc.js');
        $this->render('index',
                array(
                    'startYear'=>$startYear,
                    'currentSchoolYear'=>$currentSchoolYear,
                    'schoolbusList'=>$schoolbusList,
                    'childBySchoolbusList'=>$childBySchoolbusList,
                    'childInfo'=>$childInfo
                )
        );
    }

    public function actionSelect(){
        $this->render('//layouts/common/branchSelect');
    }

    /*
     * 获取某学校校车的信息
     */
    public function getSchoolBus($schoolId,$status=array(10)){
        $list = Yii::app()->db->createCommand()
                ->from(SchoolBus::model()->tableName())
                ->where('branchid=:branchid',array(':branchid'=>$schoolId))
                ->andWhere(array('in','state',$status))
                ->order('bus_title asc')->queryAll();
        return $list;
    }

    /*
     * 导出做某辆校车孩子的信息
     */
    public function actionExportChildBySchoolbus(){
        Yii::import('common.models.child.*');
        $startYear = Yii::app()->request->getParam('startYear',0);
        $busId = Yii::app()->request->getParam('busId',0);
        $ret = $this->getChildBySchoolbus($startYear, $busId);
        $model = SchoolBus::model()->findByPk($busId);
        header("Content-type:application/vnd.ms-excel");
        header("Content-Disposition:filename=BUS-".$model->bus_title.".xls");
        echo _t('校车编号') . "\t";
        echo _t($model->bus_title) . "\n";
        echo _t("车牌号") . "\t";
        echo _t($model->bus_code) . "\n";
        echo _t("车型") . "\t";
        echo _t($model->bus_type) . "\n";
        echo _t("实际座位数") . "\t";
        echo _t($model->acctual_children) . "\n";
        echo _t("司机姓名") . "\t";
        echo _t($model->driver_name) . "\n";
        echo _t("司机手机") . "\t";
        echo _t($model->driver_mobile) . "\n";
        echo _t("阿姨姓名") . "\t";
        echo _t($model->aunt_name) . "\n";
        echo _t("阿姨手机") . "\t";
        echo _t($model->aunt_mobile) . "\n";
        echo "\n";
        echo _t('孩子姓名') . "\t";
        echo _t('班级') . "\t";
        echo _t('接站时间') . "\t";
        echo _t('接站地址') . "\t";
        echo _t('送达时间') . "\t";
        echo _t('送达地址') . "\t";
        echo _t('孩子联系人') . "\t";
        echo _t('父母信息') . "\t";
        echo _t('父亲邮箱') . "\t";
        echo _t('母亲邮箱') . "\t";
        echo _t('备注') . "\n";
        foreach ($ret as $val) {
            echo _t($val['child_name']) . "\t";
            echo _t($val['class_name']) . "\t";
            echo _t($val['time1']) . "\t";
            echo _t($val['addr1']) . "\t";
            echo _t($val['time2']) . "\t";
            echo _t($val['addr2']) . "\t";
            if ($val['upickup']) {
                $txt = preg_replace("/\s/", "", $val['upickup']);
                $txt = preg_replace('/<br\\s*?\/??>/i', "\n", $txt);
                echo _t(trim($txt)) . "\t";
            } else {
                echo "\t";
            }
            $txt = '';
            if ($val['parent']) {
                $txt = preg_replace("/\s/", "", $val['parent']);
                $txt = preg_replace('/<br\\s*?\/??>/i', "\n", $txt);
            }
            echo _t(trim($txt)) . "\t";
            echo _t($val['fEmail']) . "\t";
            echo _t($val['mEmail']) . "\t";
            echo _t($val['remark']) . "\n";
        }
    }

    /*
     * 获取座校车孩子的信息
     */
    public function getChildBySchoolbus($startYear, $busId){
        $ret = array();
        $crite = new CDbCriteria();
        $crite->compare('t.startyear', $startYear);
        $crite->compare('t.busid', $busId);
        $crite->compare('t.schoolid', $this->branchId);
        $schoolBusList = SchoolBusChild::model()->with('childinfo','childmisc')->findAll($crite);

        if (!empty($schoolBusList)){
             $pids = array();
            foreach ($schoolBusList as $val){
                $ret[$val->childid] = array(
                    'childid' => $val->childid,
                    'class_name' => $val->childinfo->ivyclass->title,
                    'child_name' => $val->childinfo->getChildName(),
                    'time1' => $val->time1,
                    'addr1' => $val->addr1,
                    'time2' => $val->time2,
                    'addr2' => $val->addr2,
                    'remark' => $val->remark,
                    'upickup' => nl2br($val->childmisc->upickup),
                );
                if ($val->childinfo->fid)
                    $pids[$val->childinfo->fid]=$val->childinfo->fid;
                if ($val->childinfo->mid)
                    $pids[$val->childinfo->mid]=$val->childinfo->mid;
                $cpids[$val->childid]=array('f'=>$val->childinfo->fid,'m'=>$val->childinfo->mid);
            }
            $criteria = new CDbCriteria();
            $criteria->compare('pid', $pids);
            $criteria->index='pid';
            $criteria->select = 'mphone,pid';
            $parents = IvyParent::model()->findAll($criteria);
            foreach ($cpids as $ccid=>$cp){

                if ($parents[$cp['f']]->mphone && $parents[$cp['m']]->mphone){
                    $mphone = '爸爸：'.$parents[$cp['f']]->mphone.'<br>妈妈：'.$parents[$cp['m']]->mphone;
                }elseif ($parents[$cp['f']]->mphone) {
                    $mphone = '爸爸：'.$parents[$cp['f']]->mphone;
                }elseif ($parents[$cp['m']]->mphone) {
                    $mphone = '妈妈：'.$parents[$cp['m']]->mphone;
                }else{
                    $mphone= '';
                }
                $ret[$ccid]['parent'] = $mphone;
                $ret[$ccid]['mEmail'] = isset($parents[$cp['m']]->user) ? $parents[$cp['m']]->user->email: "";
                $ret[$ccid]['fEmail'] = isset($parents[$cp['f']]->user) ? $parents[$cp['f']]->user->email: "";
            }
        }
        return $ret;
    }

    /*
     * 批量分配孩子到校车
     */
    public function actionAssignChildForSchoolbus(){
        if (Yii::app()->request->isAjaxRequest && $_POST['SchoolBusChild']){

            $busId = $_POST['SchoolBusChild']['busId'];
            $startYear = $_POST['SchoolBusChild']['startYear'];
            $childIds = $_POST['SchoolBusChild']['childIds'];
            $this->addMessage('state', 'fail');
            $this->addMessage('message', Yii::t('message','Failed!'));
            if ($busId && $startYear && count($childIds)){
                //检查孩子是否为本校学生
                $command = Yii::app()->db->createCommand();
                $command->from(ChildProfileBasic::model()->tableName());
                $command->where("schoolid=:schoolid", array(':schoolid'=>$this->branchId));
                $command->andWhere(array('in','childid',$childIds));
                $command->select('childid');
                $assignChildIds = $command->queryColumn();
                //查询某个孩子是否已经分配到某辆校车内（过滤重复）
                $command = Yii::app()->db->createCommand();
                $command->from(SchoolBusChild::model()->tableName());
                $command->where("startyear=:startyear and busid=:busid and schoolid=:schoolid", array(":startyear"=>$startYear,":busid"=>$busId,':schoolid'=>$this->branchId));
                $command->andWhere(array('in','childid',$childIds));
                $command->select('childid');
                $assignChildIds = $command->queryColumn();
                //批量分配孩子
                if (count($assignChildIds)){
                    $childIds = array_diff($childIds, $assignChildIds);
                }
                $data['busId'] = $busId;
                if (count($childIds)){
                    foreach ($childIds as $childId){
                        $model = new SchoolBusChild();
                        $model->childid = $childId;
                        $model->busid = $busId;
                        $model->startyear = $startYear;
                        $model->schoolid = $this->branchId;
                        $model->userid = Yii::app()->user->getId();
                        $model->update_timestamp = time();
                        if ($model->save()){
                            $data['childInfo'][] = $model->getAttributes();
                            $this->addMessage('state', 'success');
                            $this->addMessage('message', Yii::t('message','success'));
                        }
                    }
                    $this->addMessage('data',$data);
//                    $this->addMessage('callback', 'addCallback');
                    $this->addMessage('refresh', true);
                }
            }
            $this->showMessage();
        }
    }

    /*
     * 删除已经分配给校车的孩子
     */
    public function actionDeteteChildBySchoolbus(){
        $dbId = !empty($_POST['dbId']) ? $_POST['dbId'] : 0;
        $childId = !empty($_POST['childId']) ? $_POST['childId'] : 0;
        $this->addMessage('state', 'fail');
        $this->addMessage('message', Yii::t('message','Failed!'));
        if (Yii::app()->request->isAjaxRequest && $dbId && $childId){
            $model = SchoolBusChild::model()->findByPk($dbId);
            $busId = $model->busid;
            if (($model->schoolid === $this->branchId) && ($model->childid === $childId)){
                if ($model->delete()){
                    $this->addMessage('state', 'success');
                    $this->addMessage('message', Yii::t('message','success'));
                    $this->addMessage('callback', 'deleteCallback');
                    $this->addMessage('data',array('childId'=>$childId,'busId'=>$busId));
                }
            }
        }
        $this->showMessage();
    }

    /*
     * 批量更新座校车孩子的乘车信息
     */
    public function actionUpdateChildBySchoolbus(){
        $data = array();
        $this->addMessage('state', 'fail');
        $this->addMessage('message', Yii::t('message','Failed!'));
        if (Yii::app()->request->isAjaxRequest && isset($_POST['SchoolBusChild'])){
            foreach ($_POST['SchoolBusChild']['id'] as $k=>$id){
                $model = SchoolBusChild::model()->findByPk($id);
                if ($model->childid === $_POST['SchoolBusChild']['childid'][$k]){
                    $model->time1 = $_POST['SchoolBusChild']['time1'][$k];
                    $model->time2 = $_POST['SchoolBusChild']['time2'][$k];
                    $model->addr1 = $_POST['SchoolBusChild']['addr1'][$k];
                    $model->addr2 = $_POST['SchoolBusChild']['addr2'][$k];
                    $model->remark = $_POST['SchoolBusChild']['remark'][$k];
                    $model->update_timestamp = time();
                    $model->userid = Yii::app()->user->getId();
                    if ($model->save()){
                        $data['busId'] = $model->busid;
                        $data['childInfo'][] = array(
                            'id'=>$model->id,
                            'childid'=>$model->childid,
                            'time1'=>CHtml::encode($model->time1),
                            'time2'=>CHtml::encode($model->time2),
                            'addr1'=>CHtml::encode($model->addr1),
                            'addr2'=>CHtml::encode($model->addr2),
                            'remark'=>CHtml::encode($model->remark),
                        );
                        $this->addMessage('state', 'success');
                        $this->addMessage('message', Yii::t('message','success'));
                    }else{
                        $data['busId'] = $model->busid;
                        $data['childInfo'][$model->childid] = $model->getAttributes();
                    }
                }
            }
            $this->addMessage('data', $data);
            $this->addMessage('callback', 'updateCallback');
        }
        $this->showMessage();
    }

    /*
     * 保存校车
     */
    public function actionSaveSchoolbus(){
        if (Yii::app()->request->isAjaxRequest){
            $bid = Yii::app()->request->getPost('bid',0);
            $model = SchoolBus::model()->findByPk($bid);
            if ($model === null)
                $model = new SchoolBus;

            if(isset($_POST['SchoolBus'])){
                $model->setAttributes($_POST['SchoolBus']);
                $model->branchid = $this->branchId;
                $model->userid = Yii::app()->user->id;
                $model->update_timestamp = time();
                if ($model->save()){
                    $this->addMessage('state', 'success');
                    $this->addMessage('message', Yii::t('message','success'));
                    $this->addMessage('refresh', true);
                }
                else {
                    $this->addMessage('state', 'fail');
                    $errs = current($model->getErrors());
                    $this->addMessage('message', $errs?$errs[0]:Yii::t('message','Failed!'));
                }
                $this->showMessage();
            }
        }
    }

    /*
     * 删除校车信息
     */
    public function actionDeleteSchoolbus(){
        $this->addMessage('state', 'fail');
        $this->addMessage('message', Yii::t('message','Failed!'));
        if (Yii::app()->request->isAjaxRequest) {
            $id = Yii::app()->request->getParam('busId', 0);
            $exists = SchoolBusChild::model()->exists('busid=:busid and schoolid=:schoolid',array(":busid"=>$id,":schoolid"=>$this->branchId));
            if (!$exists){
                $model = SchoolBus::model()->findByPk($id);
                if ($model->branchid == $this->branchId && $model->delete()) {
                    $this->addMessage('state', 'success');
                    $this->addMessage('message', Yii::t('message','success'));
                    $this->addMessage('callback', 'deleteSchoolbusCallback');
                    $this->addMessage('data', $id);
                }
            }else{
                $this->addMessage('message', '校车某一个学年已经分配了孩子，不能删除。');
            }
        }
        $this->showMessage();
    }

}


function _t($str='')
{
    $str = "\"".$str."\"";
    return iconv("UTF-8", "GBK", $str);
}
?>
