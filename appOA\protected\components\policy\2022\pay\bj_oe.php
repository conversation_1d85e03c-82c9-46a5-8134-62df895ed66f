<?php
if(!defined("IVY_POLICY"))
throw new CException('DO NOT USE THIS FILE DIRECTLY');

return array( 'policy'=>array(

	//是否老生老办法
	ENABLE_CONSTANT_TUITION => true,
    // 按天收费
    FENTAN_FACTOR => DAILY,

	//年保育费
	//ANNUAL_CHILDCARE_AMOUNT => 8000,

	//是否开放课外活动账单
	ENABLE_AFTERSCHOOLS => true,

	//图书馆工本费
	FEETYPE_LIBCARD => 10,

	//学期四个时间点；取前后两端
	TIME_POINTS   => array(202209,202212,202301,202307),

	//年付开通哪些缴费类型
//	PAYGROUP_ANNUAL => array(
//		ITEMS => array(
//			FEETYPE_TUITION,
//			FEETYPE_LUNCH,
//			FEETYPE_SCHOOLBUS,
//		),
//		TUITION_CALC_BASE => BY_MONTH,
//	),
//
	//学期付开通哪些缴费类型
	PAYGROUP_SEMESTER => array(
		ITEMS => array(
			FEETYPE_TUITION,
			FEETYPE_LUNCH,
           FEETYPE_SCHOOLBUS,
		),
		TUITION_CALC_BASE => BY_SEMESTER,
	),
//
//    PAYGROUP_MONTH => array(
//        ITEMS => array(
//            FEETYPE_TUITION,
//            FEETYPE_LUNCH,
//            FEETYPE_SCHOOLBUS,
//        ),
//        TUITION_CALC_BASE => BY_MONTH,
//    ),

	//计算基准：BY_MONTH, 或者 BY_SETTING, 前者表示所有的学费都基于月费用计算，后者表示所有的学费都基于学期或学年
	TUITION_CALCULATION_BASE => array(


        BY_MONTH => array(

            //月收费金额
            // AMOUNT => array(
            //    FULL5D => 10500,
            //    HALF5D => 7560,
                // FULL3D => 6840,
                // HALF3D => 5050,
                // FULL2D => 4750,
                // HALF2D => 4321,
            // ),

            //收费月份，及其权重
            MONTH_FACTOR => array(
                202209=>1,
                202210=>1,
                202211=>1,
                202212=>1,
                202301=>.55,
                202302=>1,
                202303=>1,
                202304=>1,
                202305=>1,
                202306=>1,
                202307=>1
            ),

            //折扣
            GLOBAL_DISCOUNT => array(
                DISCOUNT_ANNUAL => '1:1:2023-06-01:round',
                DISCOUNT_SEMESTER1 => '1:1:2023-02-01:round',
                DISCOUNT_SEMESTER2 => '1:1:2023-07-20:round'
            )
        ),

        BY_SEMESTER1 => array(
            AMOUNT => array(
                FULL5D => 99076,
                FULL5D2 => 40143,
                // HALF2D => 28500,
                // HALF3D => 41500,
                HALF5D => 28100,
            ),
        ),

        BY_SEMESTER2 => array(
            AMOUNT => array(
                FULL5D => 85934,
                FULL5D2 => 55256,
                // HALF2D => 28500,
                // HALF3D => 41500,
                // HALF5D => 55322,
            ),
            ENDDATE => array(
                FULL5D => 1688054400,
                FULL5D2 => 1688054400,
                RS_FULL5D => 1688054400,
                RS_FULL5D2 => 1690473600,
                RS_FULL5D3 => 1688054400,
                LUNCHA => 1688054400,
                LUNCHB => 1690473600,
                // 双语班校车费
                41680 => 1690473600,
                41681 => 1690473600,
                41682 => 1690473600,
                // 国际班校车费
                41738 => 1688054400,
                41739 => 1688054400,
                41740 => 1688054400,
            )
        ),
	),

	//每餐费用
    LUNCH_PERDAY => array(
        LUNCHA => 45,
        LUNCHB => 45,
    ),

	//账单到期日和账单开始日之差
	DUEDAYS_OFFSET => 1,

));