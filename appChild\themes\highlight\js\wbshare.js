function sinaWb(pic, tit){
  tit = '#艾毅分享# ' + tit;
  var param = {
    url:location.href,
    type:'3',
    //count:'', /**是否显示分享数，1显示(可选)*/
    appkey:'3731183707', /**您申请的应用appkey,显示分享来源(可选)*/
    title:tit, /**分享的文字内容(可选，默认为所在页面的title)*/
    pic:pic, /**分享图片的路径(可选)*/
    ralateUid:'1892623913', /**关联用户的UID，分享微博会@该用户(可选)*/
	language:'zh_cn', /**设置语言，zh_cn|zh_tw(可选)*/
    rnd:new Date().valueOf()
  }
  var temp = [];
  for( var p in param ){
    temp.push(p + '=' + encodeURIComponent( param[p] || '' ) )
  }
  //document.write('<iframe allowTransparency="true" frameborder="0" scrolling="no" src="http://hits.sinajs.cn/A1/weiboshare.html?' + temp.join('&') + '" width="'+ _w+'" height="'+_h+'"></iframe>')
  window.open("http://service.weibo.com/share/share.php?"+ temp.join('&'), "_blank", "width=615,height=505");
}
function qqWb(pic, tit){
    tit = encodeURIComponent('#艾毅分享# ') + tit + encodeURIComponent('@ivyschools');
    var _url = encodeURIComponent('');
//    var _assname = encodeURI("ivyschools");//你注册的帐号，不是昵称
    var _appkey = encodeURI("801255464");//你从腾讯获得的appkey
    var _pic = encodeURI(pic);//（例如：var _pic='图片url1|图片url2|图片url3....）
    var _t = tit;//标题和描述信息
//    var metainfo = document.getElementsByTagName("meta");
//    for(var metai = 0;metai < metainfo.length;metai++){
//        if((new RegExp('description','gi')).test(metainfo[metai].getAttribute("name"))){
//            _t = metainfo[metai].attributes["content"].value;
//        }
//    }
//    _t =  encodeURI('#艾毅分享# ')+_t;//请在这里添加你自定义的分享内容
//    if(_t.length > 120){
//        _t= _t.substr(0,117)+'...';
//    }
    //_t = encodeURI(_t);

    var _u = 'http://share.v.t.qq.com/index.php?c=share&a=index&url='+_url+'&appkey='+_appkey+'&pic='+_pic+'&title='+_t;
    window.open( _u,'', 'width=700, height=680, top=0, left=0, toolbar=no, menubar=no, scrollbars=no, location=yes, resizable=no, status=no' );
}
function previewOnly(){
	alert("没有权限"+"\r\n"+"No permission");
	return true;
}