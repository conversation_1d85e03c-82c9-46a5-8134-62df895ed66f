<?php

// uncomment the following to define a path alias
// Yii::setPathOfAlias('local','path/to/local-folder');

// This is the main Web application configuration. Any writable
// CWebApplication properties can be configured here.

$dbname = "dbname";
$subdbname = 'mimssub';
$dbuser = "dbuser";
$dbpass = "dbpass";

$cookieDomain = ".site.com"; //与OA相同主域名，共享子域名cookie
$oaUrl = "http://oa.site.com"; //OA 地址
$baseUrl = "http://apps-child.site.com";
$themesUrl = "http://www.site.com/themes"; //真正孩子前台的theme地址，可以设置成单独的一个域名

$preventDirectLogin = true;

$uploadBaseUrl = $oaUrl . "/uploads/"; //开发时为显示图片可以设置为实际OA的upload URL地址
$uploadPath = '/path/to/oa/uploads/'; 
$xoopsVarPath = '/path/to/XOOPS-data';
$onlineBanking = "sandbox"; //sandbox, production 
$YeepayRespondeUrl= $oaUrl . '/responder/responder.php';
$basePath = dirname(__FILE__) . DIRECTORY_SEPARATOR . '..';
$themeBasePath = dirname(__FILE__) . DIRECTORY_SEPARATOR . '..' . DIRECTORY_SEPARATOR . '..' . DIRECTORY_SEPARATOR . 'themes';

require_once $basePath . DIRECTORY_SEPARATOR . 'components' . DIRECTORY_SEPARATOR . 'utils.php';

$sessionTableName ="ivy_session"; //与OA共享的session表，不可更改
$sessionName = "XOSESSIONID2";  //sessionId的名字，必须跟XOOPS那边的一样，不可更改
$sessionKeyPrefix = md5('Yii.CXoWebUser.Apps.Mims'); //必须与xoops中yiiSSO.php中的保持一致，否则无法SSO，不可更改

$preventDirectLogin = true;   //本站不允许直接登录
$shareOptions = array(
	"useProduction"=>true, // 查看孩子分享时，是否使用正式站的域名进行分享
	"productionUrl"=>'http://www.ivyonline.cn', //正式站的域名
);
return array(
	'basePath'=>$basePath,
	'name'=>'IvyOnline Child Center',
	'homeUrl'=>$baseUrl,

	// preloading 'log' component
	'preload'=>array('log'),
    'theme'=>'highlight',
    'defaultController'=>'user/login',

	// autoloading model and component classes
	'import'=>array(
		'common.models.*',
		'application.components.*',
        'common.components.*',
	),

    'sourceLanguage'=>'en_us',
    'language'=>'zh_cn',
	'localeDataPath'=>dirname(__FILE__).DIRECTORY_SEPARATOR.'..'.'/components/locale_data/',
	'modules'=>array(
		// uncomment the following to enable the Gii tool
	/*	
		'gii'=>array(
			'class'=>'system.gii.GiiModule',
			'password'=>'test!',
		 	// If removed, Gii defaults to localhost only. Edit carefully to taste.
			'ipFilters'=>array('127.0.0.1','::1'),
		),
        */
		'user'=>array(
			"OAUrl" => $oaUrl,
			"OALoginUrl" => $oaUrl . "/login.php"
		),
        'child'=>array(),
        'portfolio'=>array(),
	),

	// application components
	'components'=>array(
	
        'session'=>array(
            'autoCreateSessionTable'=>false,
            'class'=>'application.components.CXoDbHttpSession',
            'cookieParams'=>array('domain'=>$cookieDomain,'lifetime'=>0),
            'connectionID' => 'db',
            'timeout' => 28800,
            'sessionTableName' =>$sessionTableName,
            'sessionName' => $sessionName,
        ),
		'user'=>array(
			// enable cookie-based authentication
            'class'=>'application.components.CXoWebUserStaff',
            'allowAutoLogin'=>true,
			'_keyPrefix' => $sessionKeyPrefix,
		),
        'authManager'=>array(
            'class'=>'CDbAuthManager',
            'connectionID' => 'db',
            'defaultRoles'=>array('visitor')
        ),
		// uncomment the following to enable URLs in path-format
		'urlManager'=>array(
            'baseUrl'=>$baseUrl,
			'urlFormat'=>'path',
            //'urlSuffix'=>'.ivy',
			'rules'=>array(
				//'<controller:\w+>/<id:\d+>'=>'<controller>/view',
                'v/<id:\w+>'=>'access/index',
                '<childid:\d+>' => 'child/profile/welcome',
                '<childid:\d+>/portfolio/portfolio/journal/<startyear:\d+>-<classid:\d+>-<weeknum:\d+>' => 'portfolio/portfolio/journal',
                '<childid:\d+>/portfolio/portfolio/semester/<startyear:\d+>-<semester:\d+>' => 'portfolio/portfolio/semester',
                '<childid:\d+>/child/resource/content/<category:\w+>' => 'child/resource/content',
                '<childid:\d+>/child/profile/profile/<t:\w+>' => 'child/profile/profile',
                '<childid:\d+>/<module:\\w+>/<controller:\w+>/<action:\w+>' => '<module>/<controller>/<action>',
                '<childid:\d+>/<module:\\w+>/<controller:\w+>/<action:\w+>/<classid:\d+>' => '<module>/<controller>/<action>',
				'<childid:\d+>/<controller:\w+>/<action:\w+>/<id:\d+>'=>'<controller>/<action>',
				'<childid:\d+>/<controller:\w+>/<action:\w+>'=>'<controller>/<action>',
			),
		),

        'themeManager'=>array(
			'basePath'=>$themeBasePath,
            'baseUrl'=>$themesUrl
        ),

		// uncomment the following to use a MySQL database
		'db'=>array(
			'connectionString' => 'mysql:host=localhost;dbname='.$dbname,
			'emulatePrepare' => true,
			'username' => $dbuser,
			'password' => $dbpass,
			'charset' => 'utf8',
			'schemaCachingDuration' => 300,
		),
        'subdb'=>array(
            'class'=>'CDbConnection',
            'connectionString' => 'mysql:host=localhost;dbname='.$subdbname,
            'emulatePrepare' => true,
            'username' => $dbuser,
            'password' => $dbpass,
            'charset' => 'utf8',
        ),
		'cache'=>array(
				'class'=>'CFileCache',
		),
		'weatherCache'=>array(
				'class'=>'CDbCache',
				'connectionID'=>'db',
				'autoCreateCacheTable'=>false,
				'keyPrefix'=>'weather_',
				'cacheTableName'=>'ivy_yii_dbcache',
		),		
        'request'=>array(
            'enableCookieValidation'=>true,  
        ),
		'errorHandler'=>array(
			// use 'site/error' action to display errors
            'errorAction'=>'site/error',
        ),
		'log'=>array(
			'class'=>'CLogRouter',
			'routes'=>array(
				array(
					'class'=>'CFileLogRoute',
					'levels'=>'error, warning',
				),
				// uncomment the following to show log messages on web pages
				/*
				array(
					'class'=>'CWebLogRoute',
				),
				*/
			),
		),
	),

	// application-level parameters that can be accessed
	// using Yii::app()->params['paramName']
	'params'=>array(
		// this is used in contact page
		'adminEmail'=>'<EMAIL>',
		'uploadBaseUrl'=>$uploadBaseUrl,
		'uploadPath'=>$uploadPath,
		// xoops data 目录 用于存放 在线支付的日志文件
		'xoopsVarPath'=>$xoopsVarPath,
		// 商户接收支付成功数据的地址
		'YeepayRespondeUrl'=>$YeepayRespondeUrl,
		'onlineBanking' => $onlineBanking, //sandbox, production
		'preventDirectLogin' => $preventDirectLogin,
		'shareOptions'=>$shareOptions
	),
);
