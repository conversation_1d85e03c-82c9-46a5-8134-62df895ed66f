<?php

/**
 * 将 widget 的 action 请求写在widget结构之内便于管理
 * 参考 http://www.yiiframework.com/wiki/146/how-to-use-a-widget-as-an-action-provider/(没有用这个啊 看 3713 )
 * why do you directly config the actions function of controller oh
 * <AUTHOR>
 * @date 2012-07-25
 */
class votingAction extends CAction{

	public function run(){

		Yii::import('common.models.vote.*');
		$json_data['flag'] = 'unauthorized';
		if(Yii::app()->request->isAjaxRequest){
			$childid = $this->getController()->getChildId();
			if(Yii::app()->user->checkAccess('adminChild',array("childid"=>$childid)))
			{
				$execute_failed = false;
				$vr_model = new WVoteResult();
				$vr_model->attributes = $_POST;
				$vr_model->vote_time = time();

				$option_result = Yii::app()->request->getPost('vote_option');
				$vr_trans= $vr_model->dbConnection->beginTransaction();
				if(is_array($option_result)){
					foreach ($option_result as $value) {
						$vr_model->result = $value;
						$flag = $vr_model->save();
						if(empty($flag)){
							$execute_failed = true;
							$json_data['flag'] = 'failed';
							break;
						}
						$vr_model->setIsNewRecord(true);
						$vr_model->id = null;
					}
				}else if(!empty($option_result)){
					$vr_model->result = $option_result;
					$flag = $vr_model->save();
					if(empty($flag)){
						$execute_failed = true;
						$json_data['flag'] = 'failed';
					}
				}else {
					$json_data['flag'] = 'failed';
				}
				if (true == $execute_failed) {
					// 回滚
					$vr_trans->rollback();
				} else {
					$json_data['flag'] = "success";
					// 提交
					$vr_trans->commit();
				}
			}
		}
		echo CJSON::encode($json_data);
		Yii::app()->end();
	}

}