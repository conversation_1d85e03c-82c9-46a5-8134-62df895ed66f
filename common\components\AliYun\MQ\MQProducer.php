<?php
/**
 * Created by PhpStorm.
 * User: R.X.
 * Date: 2017/5/18 0018
 * Time: 16:35
 */


class MQProducer
{
    private $mq_tag;
    private $mq_key;
    private $mq_body;
    private $mq_delay_minutes = 0;
    private $messageId = "";

    private $utils;
    private $configs;

    const TAG_ASA = 'asa';
    const TAG_PRODUCT = 'product';
    const TAG_WX_WORK = 'wxwork';
    const TAG_APPLY = 'apply';

    //构造函数
    function __construct($mq_tag, $mq_key, $mq_body, $delay_minutes = 0)
    {

        require_once(dirname(__FILE__).'/MQUtils.php');

        $this->utils = new MQUtils();

        $this->mq_tag = $mq_tag;
        $this->mq_key = $mq_key;
        $this->mq_body = $mq_body;
        $this->mq_delay_minutes = $delay_minutes;

        $this->configs = $this->utils->getConfigs();


        // if($this->configs['is_server'] ) {
        //     $this->mq_tag = MQUtils::TXT_PREFIX_PROD . $this->mq_tag;
        // } else {
        //     $this->mq_tag = MQUtils::TXT_PREFIX_TEST . $this->mq_tag;
        // }

    }
    //计算md5
    private function md5($str)
    {
        return md5($str);
    }
    //发布消息流程
    public function process()
    {

        //计算时间戳
        $date = time()*1000;
        $startdelivertime = $date + $this->mq_delay_minutes * 1000 * 60;

        //POST请求url
        $postUrl = sprintf("%s/message/?topic=%s&time=%s&tag=%s&key=%s",
            $this->configs['URL'],
            $this->configs['Topic'],
            $date,
            $this->mq_tag,
            $this->mq_key);

        //添加定时任务
        $postUrl = $postUrl."&startdelivertime=".$startdelivertime;

        //签名字符串
        $signString = $this->configs['Topic'] . MQUtils::TXT_NEWLINE .
            $this->configs['ProducerID'] . MQUtils::TXT_NEWLINE .
            $this->md5($this->mq_body). MQUtils::TXT_NEWLINE .$date;
        //计算签名
        $sign = $this->utils->calSignature($signString, $this->configs['Sk']);
        //初始化网络通信模块
        $ch = curl_init();
        //构造签名标记
        $signFlag = sprintf("%s:%s", MQUtils::TXT_SIG, $sign);
        //构造密钥标记
        $akFlag = sprintf("%s:%s", MQUtils::TXT_ACCESSKEY, $this->configs['Ak']);
        //标记
        $producerFlag = sprintf("%s:%s", MQUtils::TXT_PID, $this->configs['ProducerID'] );
        //构造HTTP请求头部内容类型标记
        $contentFlag = "Content-Type:text/html;charset=UTF-8";
        //构造HTTP请求头部
        $headers = array(
            $signFlag,
            $akFlag,
            $producerFlag,
            $contentFlag,
        );

        if(isset($this->configs['Send_to_remote']) && $this->configs['Send_to_remote']) {

            //设置HTTP头部内容
            curl_setopt($ch,CURLOPT_HTTPHEADER,$headers);
            //设置HTTP请求类型,此处为POST
            curl_setopt($ch,CURLOPT_CUSTOMREQUEST,"POST");
            //设置HTTP请求的URL
            curl_setopt($ch,CURLOPT_URL,$postUrl);
            //设置HTTP请求的body
            curl_setopt($ch,CURLOPT_POSTFIELDS,$this->mq_body);
            //构造执行环境
            ob_start();
            //开始发送HTTP请求
            curl_exec($ch);
            //获取请求应答消息
            $result = ob_get_contents();
            //清理执行环境
            ob_end_clean();
            //打印请求应答结果

            if($result){
                $ret = json_decode($result);

                if(is_object($ret) && ($ret->msgId)) {
                    $this->messageId = $ret->msgId;
                    // echo "\n Production: mq sent to server. {$this->messageId} \n" ;
                } else {
                    Yii::log($result, 'info', 'MQProducer');
                }

            }else{
                Yii::log('Curl error', 'info', 'MQProducer');
                // print_r('curl error');
            }

            //关闭连接
            curl_close($ch);


        } else {
            // echo "\n Testing: mq sent to local db.\n";
        }

        $this->saveToDB();

    }


    private function saveToDB()
    {

        Yii::app()->subdb->createCommand()->insert('mq_log', array(
            "mq_tag" => $this->mq_tag,
            "mq_key" => $this->mq_key,
            "mq_body" => $this->mq_body,
            "mq_delay_min" => $this->mq_delay_minutes,
            "msg_id" => $this->messageId,
            "updated" => time(),
        ));

    }

}
