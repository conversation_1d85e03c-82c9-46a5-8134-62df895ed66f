<?php

class ListController extends BranchBasedController
{
	public $branchMainMenu=null;
	public $branchObj=null;
	public $catList=null; //孩子状态列表子菜单
	public $catOp=null; //校园管理子菜单
    public $dialogWidth;
	public $constantSchoolList = array('BJ_IA','BJ_CP', 'BJ_IASLT', 'BJ_OE', 'BJ_OG'); //老生老办法特殊学校
    public function createUrl($route, $params = array(), $ampersand = '&')
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        return parent::createUrl($route, $params, $ampersand);
    }

	public function init(){
		parent::init();

		$this->catList = array(
			ChildProfileBasic::STATS_REGISTERED => Yii::t('navs', 'Registered'),
			ChildProfileBasic::STATS_ACTIVE => Yii::t('navs', 'Active'),
			ChildProfileBasic::STATS_DROPPINGOUT => Yii::t('navs', 'Dropping Out'),
			ChildProfileBasic::STATS_DROPOUT => Yii::t('navs', 'Dropout'),
			ChildProfileBasic::STATS_GRADUATED => Yii::t('navs', 'Graduated'),
		);
		$this->catOp = array(
			'bus' => Yii::t('navs','School Bus'),
//			'classes' => Yii::t('navs','Classes'),
		);

		$this->branchSelectParams['urlArray'] = array('//child/list/classes','flag'=>'current');
        $this->branchMainMenu = array(
			'list' => array(
				'label' => Yii::t('navs', 'Child List'),
                'url'=> array('/child/list/index'),
                'itemOptions' => array(
                    'id' => 'c-cat-item'
                ),
                'submenuOptions' => array(
                    'class' => 'subitem length_2'
                )
			),
			'1' => array(
				'label' => Yii::t('campus', 'Current Year'),
				'url' => array('/child/list/classes','flag'=>'current')
			),
			'2' => array(
				'label' => Yii::t('campus', 'Next Year'),
				'url' => array('/child/list/classes','flag'=>'next')
			),
			'3' => array(
				'label' => Yii::t('campus', 'Year Switching'),
				'url' => array('/child/list/batch')
			),
			'4' => array(
				'label' => Yii::t('campus', 'Discounts'),
				'url' => array('/child/list/constant')
			),
			'operations' => array(
				'label' => Yii::t('navs','OP admin'),
                'url'=> array('/child/list/operations'),
                'itemOptions' => array(
                    'id' => 'c-cat-item'
                ),
//                'submenuOptions' => array(
//                    'class' => 'subitem length_2'
//                ),
			),
		);

		foreach($this->catList as $_k=>$_v){
			$this->branchMainMenu['list']['items'][] = array(
				'label' =>$_v,
				'url' => array('/child/list/index', 'status'=>$_k)
			);
		};

        if (OA::isProduction()){
			unset($this->catOp['classes']);
		}
		foreach($this->catOp as $_k=>$_v){
			$this->branchMainMenu['operations']['items'][] = array(
				'label' =>$_v,
				'url' => array('/child/list/operations', 'category'=>$_k)
			);
		};
	}

    public function actionSelect()
	{
        $this->subMenu = array();
		//$this->branchSelectParams["urlArray"] = array("//child/list/index");
		$this->branchSelectParams["showList"] = true;
		$this->render('select');
	}

	public function actionIndex()
	{
		$status=Yii::app()->request->getParam("status", ChildProfileBasic::STATS_REGISTERED);

		if(!isset($this->catList[$status])) $status = ChildProfileBasic::STATS_REGISTERED;

		$_GET['status'] = $status;

		$this->branchMainMenu['list']['label'] = $this->catList[$status];
		$branchId=$this->branchId;

		$crit = new CDbCriteria;
		$crit->compare("schoolid", $branchId);
		if($status == ChildProfileBasic::STATS_REGISTERED){
			$crit->compare("status", $status);
			$crit->compare("classid", '0');
		}elseif($status == ChildProfileBasic::STATS_ACTIVE){
			$crit->compare("status", array(10,20));
		}else{
			$crit->compare("status", $status);
		}

		$dataProvider=new CActiveDataProvider('ChildProfileBasic', array(
			'criteria'=>$crit,

			'pagination'=>array(
				'pageSize'=>20,
			),
		));
		$this->render('index', array('dataProvider'=>$dataProvider));
	}

	public function actionClasses()
	{

		$calendar = null;
		Yii::import('common.models.calendar.*');
		Yii::import('common.models.invoice.ChildReserve');
		$this->branchObj = Branch::model()->findByPk($this->branchId);

		$flag=strtolower(Yii::app()->request->getParam("flag", "current"));
		$flag= in_array($flag ,array("current", "next"))?$flag:"current";
		$_GET['flag'] = $flag;

		$type=strtolower(Yii::app()->request->getParam("type", "namelist"));
		$type= in_array($type ,array("namelist", "invoicelist"))?$type:"namelist";
		$_GET['type'] = $type;

        $pageSubMenu = array(
			array(
				'label' => Yii::t("campus", "Name List"),
				'url' => array('//child/list/classes','flag'=>$flag, 'type'=>'namelist')
			),
			array(
				'label' => Yii::t("campus", "Invoice Status"),
				'url' => array('//child/list/classes','flag'=>$flag, 'type'=>'invoicelist')
			),
		);

		switch($type){
			case 'namelist':
				switch($flag){
					case 'current':
						$calendar = Calendar::model()->with(
							array('currentClasses'=>array('params'=>array(':schoolid'=>$this->branchId)))
						)->findByPk($this->branchObj->schcalendar);
						break;
					case 'next':
						$nextYid = $this->branchObj->getNextCalendarId();
						if($nextYid){
							$calendar = Calendar::model()->with(
							array('nextClasses'=>array('params'=>array(':schoolid'=>$this->branchId)))
						)->findByPk($nextYid);
						}
					break;
				}
				$this->render('classes_'.$flag, array("calendar"=>$calendar, "pageSubMenu"=>$pageSubMenu));
			break;
			case 'invoicelist':
				switch($flag){
					case 'current':
						$crit = new CDbCriteria;
						$crit->compare('yid', $this->branchObj->schcalendar);
						$crit->compare('schoolid', $this->branchId);
						$crit->order = 'child_age ASC,title ASC';
						$classIds = CHtml::listData(IvyClass::model()->findAll($crit), 'classid', 'title');
						$classes = $this->getInvoicesOverviewByClassid(array_shift(array_keys($classIds)), $this->branchObj->schcalendar, $flag);
						$calendar = Calendar::model()->findByPk($this->branchObj->schcalendar);
						break;
					case 'next':
						$nextYid = $this->branchObj->getNextCalendarId();
						if($nextYid){
							$classIds = CHtml::listData(IvyClass::model()->findAllByAttributes(array(
								'yid'=> $nextYid,
								'schoolid' => $this->branchId
							)), 'classid', 'title');

							$classes = $this->getInvoicesOverviewByClassid(array_shift(array_keys($classIds)), $nextYid, $flag);

							$calendar = Calendar::model()->findByPk($nextYid);
						}
					break;
				}
				$this->render('class_invoice', array("classes" => $classes, "calendar"=>$calendar, "pageSubMenu"=>$pageSubMenu, "flag"=>$flag, "classIds"=>$classIds));

			break;
		}
	}

	public function actionBatch(){
		$calendar = null;
		Yii::import('common.models.calendar.*');
		Yii::import('common.models.invoice.ChildReserve');
		$this->branchObj = Branch::model()->findByPk($this->branchId);
        $currentCalendar = Calendar::model()->findByPk($this->branchObj->schcalendar);
        $currentMonth = date('n',  time());
        $currentYear = date('Y',  time());
        if ($currentMonth>=3 && $currentMonth<=9 && $currentYear != $currentCalendar->startyear)
        {
            $flag=strtolower(Yii::app()->request->getParam("flag", "current"));
            $flag= in_array($flag ,array("current", "newreg"))?$flag:"current";
            $nextYid = $this->branchObj->getNextCalendarId();
            switch($flag){
                case 'current':
                    $calendar = Calendar::model()->with(
                        array('currentClasses'=>array('params'=>array(':schoolid'=>$this->branchId)))
                    )->findByPk($this->branchObj->schcalendar);
                    $this->render('batch', array("calendar"=>$calendar, "nextyid"=>$nextYid, "flag"=>$flag,"show"=>true));
                    break;
                case 'newreg':
                    $crit = new CDbCriteria;
                    $crit->compare("t.schoolid", $this->branchId);
                    $crit->compare("t.classid", 0);
                    $crit->compare("t.status", 0);
                    $crit->order = 'birthday DESC';
                    $children = ChildProfileBasic::model()->with('nextYear')->findAll($crit);
                    if($nextYid){
                        $calendar = Calendar::model()->findByPk($nextYid);
                    }
                    $this->render('batch', array("calendar"=>$calendar, "nextyid"=>$nextYid, "flag"=>$flag, "children"=>$children,"show"=>true));
                break;
            }
        }
        else
        {
            $this->render('batch', array("show"=>false));
        }
	}

    public function actionProcessBat()
    {
		if (Yii::app()->user->checkAccess('oChildGeneralAdmin')) {
			if ($_POST['ids']){
				Yii::import('common.models.invoice.*');
				Yii::import('application.components.policy.PolicyApi');
				$t = $_POST['t'];
				$state=0;
				if ($t == 'f'){
					$state = ChildProfileBasic::STATS_GRADUATED;
				}
				elseif ($t == 'o'){
					$state = ChildProfileBasic::STATS_DROPOUT;
				}
                $policyApi = new PolicyApi($this->branchId);
				$ret = $policyApi->changeChildStatus($_POST['ids'], $_POST['flag'], $_POST['toClassid'], $state);
				$this->addMessage('state', $ret['status'] ? 'success' : 'fail');
				if(!$ret['status']){
					$this->addMessage('message', Yii::t("child", "所选孩子中有未付账单未处理，请处理之后再试。"));
				}
				$this->addMessage('data', isset($ret['childIds']) ? $ret['childIds'] : array());
				$this->showMessage();
			}
		}else{
				$this->addMessage('state', 'fail');
				$this->addMessage('message', Yii::t("child", "Unauthorized operation, please contact IT Dept."));
				$this->showMessage();
		}
    }

	private function getInvoicesOverviewByClassid($classid,$calendarid,$flag='current'){
			Yii::import('common.models.invoice.*');
			Yii::import('common.models.calendar.Calendar');
			Yii::import('application.components.policy.PolicyApi');
            new PolicyApi($this->branchId);
			$calendar = Calendar::model()->findByPk($calendarid);
			$flag = strtolower($flag);

			$crit = new CDbCriteria;
			$crit->index = 'classid';
			switch($flag){
				default:
				case 'current':
					$theClasses = IvyClass::model()->with(array(
						'children'=>array(
							'order'=>'status ASC',
							'with'=>array(
								'invoices'=>array(
									'condition'=>"invoices.inout=:inout AND invoices.calendar_id=:calendarid AND invoices.status not in (88,99) AND invoices.payment_type in ('tuition','lunch','bus')",
									'params'=>array(':inout'=>'in',':calendarid'=>$calendarid),
									'together'=>false,
									'order'=>'invoices.startdate ASC'
								)
							),
							'index'=>'childid'
						),
					))->findAllByAttributes(array('classid'=>$classid, 'schoolid'=>$this->branchId), $crit);
				break;
				case 'next':
					$theClasses = IvyClass::model()->with(array(
						'reserveChildren'=>array(
							'order'=>'status ASC',
							'with'=>array(
								'childProfile'=>array(
									'with'=> array(
										'invoices'=>array(
											'condition'=>"invoices.inout=:inout AND invoices.calendar_id=:calendarid AND invoices.status not in (88,99) AND invoices.payment_type in ('tuition','lunch','bus')",
											'params'=>array(':inout'=>'in',':calendarid'=>$calendarid),
											'together'=>false,
											'order'=>'invoices.startdate ASC'
										),
									),
								)
							),
							'index'=>'childid'
						),
					))->findAllByAttributes(array('classid'=>$classid, 'schoolid'=>$this->branchId), $crit);
				break;
			}

			if($theClasses){
				$m=9;
				for($i=0;$i<12;$i++){
					$sY = $calendar->startyear;
					$thetime = mktime(0,0,0,$m,1,$sY);
					$_m = date("Ym", $thetime);
					$defaults[] = $_m;
					$m++;
				}

				switch($flag){
					default:
					case 'current':
						foreach($theClasses as $classid=>$theClass){
							foreach($theClass->children as $cid=>$cval){
								foreach($theClass->children[$cid]->invoices as $invoice){
									$start_month = date("Ym",$invoice->startdate);
									$end_month = date("Ym",$invoice->enddate);
									$startM = date("m",$invoice->startdate);
									$startY = date("Y",$invoice->startdate);
									$cycleM = $startM;
									if($start_month != $end_month){
										$current_month = date("Ym",$invoice->startdate);
										while( $current_month < $end_month){
											$_time = mktime(0,0,0,$cycleM, 1, $startY);
											$current_month = date("Ym", $_time);
											$data[$classid][$cid][$invoice->payment_type][$current_month][$invoice->invoice_id] = $invoice->status;
											$cycleM++;
										}
									}else{
										$data[$classid][$cid][$invoice->payment_type][$start_month][$invoice->invoice_id] = $invoice->status;
									}
								}
							}
						}
					break;
					case 'next':
						foreach($theClasses as $classid=>$theClass){
							foreach($theClass->reserveChildren as $cid=>$cval){
								foreach($theClass->reserveChildren[$cid]->childProfile->invoices as $invoice){
									$start_month = date("Ym",$invoice->startdate);
									$end_month = date("Ym",$invoice->enddate);

									$startM = date("m",$invoice->startdate);
									$startY = date("Y",$invoice->startdate);
									$cycleM = $startM;
									if($start_month != $end_month){
										$current_month = date("Ym",$invoice->startdate);
										while( $current_month < $end_month){
											$_time = mktime(0,0,0,$cycleM, 1, $startY);
											$current_month = date("Ym", $_time);
											$data[$classid][$cid][$invoice->payment_type][$current_month][$invoice->invoice_id] = $invoice->status;
											$cycleM++;
										}
									}else{
										$data[$classid][$cid][$invoice->payment_type][$start_month][$invoice->invoice_id] = $invoice->status;
									}

								}
							}
						}
					break;
				}
			}
			return array('theClasses'=>$theClasses, 'data'=>$data, 'defaults'=>$defaults);
	}

	public function actionViewInvoicesByClassid($classid, $calendarid, $flag='current'){
		//if (Yii::app()->user->checkAccess('oChildGeneralView')) {
			Yii::import('common.models.invoice.*');
			Yii::import('common.models.calendar.Calendar');
			Yii::import('application.components.policy.PolicyApi');
            $policyApi = new PolicyApi($this->branchId);
			$calendar = Calendar::model()->findByPk($calendarid);
			$flag = strtolower($flag);

			$classes = $this->getInvoicesOverviewByClassid($classid, $calendarid, $flag);

			if(isset($classes['theClasses'])){
				if(Yii::app()->request->isAjaxRequest){
					$html = $this->renderPartial('_single_class_invoice', array('classes'=>$classes, 'defaults'=>$defaults, 'flag'=>$flag, 'policyApi'=>$policyApi), true);
					echo $html;
				}else{
					$html = $this->render('_single_class_invoice', array('defaults'=>$defaults, 'classes'=>$classes, 'flag'=>$flag, 'policyApi'=>$policyApi));
				}
			}
		//}
	}

	public function actionConstant(){

		$flag=strtolower(Yii::app()->request->getParam("flag", ""));
		$flag= in_array($flag ,array("current", "next","change"))?$flag:"current";
		$_GET['flag'] = $flag; //初始默认选中项

        $pageSubMenu = array(
			array(
				'label' => Yii::t("campus", "By Current Year"),
				'url' => array('//child/list/constant','flag'=>'current')
			),
			array(
				'label' => Yii::t("campus", "By Next Year"),
				'url' => array('//child/list/constant','flag'=>'next')
			),
            array(
				'label' => Yii::t("campus", "政府补贴调节"),
				'url' => array('//child/list/constant','flag'=>'change')
			),
		);

		if (!in_array($this->branchId,array('TJ_EC','TJ_ES')))
        {
           unset($pageSubMenu[2]);
        }

		$calendar = null;
		Yii::import('common.models.calendar.*');
		Yii::import('common.models.invoice.*');
		$this->branchObj = Branch::model()->findByPk($this->branchId);


		switch($flag){
			case 'current':
				$calendar = Calendar::model()->with(
					array('currentClasses'=>array(
						'params'=>array(':schoolid'=>$this->branchId),
						'with'=>array('children'=>array(
								'with'=>array(
									'constant'=>array('together'=>false),
									'bindDiscount'=>array('together'=>false),
								),
								'select'=> 'childid, nick, country, status, classid, name_cn, first_name_en, last_name_en',
								'together'=>false,
								'order'=>'children.status ASC, children.name_cn ASC, children.first_name_en ASC',
							),
						),
						'select'=>'currentClasses.title,currentClasses.classid',
					))
					)->findByPk($this->branchObj->schcalendar);
				break;
			case 'next':
				$nextYid = $this->branchObj->getNextCalendarId();
				if($nextYid){
					$calendar = Calendar::model()->with(
					array('nextClasses'=>array(
						'params'=>array(':schoolid'=>$this->branchId),
						'with'=>array(
							'reserveChildren'=>array(
								'with'=>array(
									'childProfile'=>array(
										'with'=>array(
											'constant'=>array('together'=>false),
											'bindDiscount'=>array('together'=>false),
										),
										'order'=>'reserveChildren.stat ASC, childProfile.name_cn ASC, childProfile.first_name_en ASC',
									)
								)
							),
							),
						)
					)
					)->findByPk($nextYid);
				}
			break;
            case 'change':
                global $paymentMappings;
                $discountList = array('0'=>Yii::t('global','Please Select'));
                $discountModel = DiscountSchool::model()->with('discountTitle')->findAll('flag=1 and schoolid=:schoolid',array(':schoolid'=>$this->branchId));
                if (!empty($discountModel)){
                    foreach ($discountModel as $val){
                        $discountList[$val->id] = $val->discountTitle->title_cn;
                    }
                }
                if (isset($_POST['Invoice']))
                {
                    $crite = new CDbCriteria();
                    $crite->compare('invoices.schoolid', $this->branchId);
                    $crite->compare('invoices.payment_type', 'preschool_subsidy');
                    $crite->compare('invoices.status', Invoice::STATS_UNPAID);
                    if ($_POST['Invoice']['discount_id']){
                        $crite->compare('invoices.discount_id', $_POST['Invoice']['discount_id']);
                    }
                    if ($_POST['Invoice']['startdate'] && $_POST['Invoice']['enddate']){
                        $crite->addCondition('invoices.startdate>='.  strtotime($_POST['Invoice']['startdate']).' and invoices.enddate<='.strtotime($_POST['Invoice']['enddate']));
                    }
                    $model = ChildProfileBasic::model()->with('invoices')->findAll($crite);
                    $data = $this->renderPartial('invoice_list',array('model'=>$model),true);
                    echo CJSON::encode($data);
                    Yii::app()->end();
                }
                break;
		}

		$enabledConstantTuition = false;
		$policy = OA::getPolicy(OA::POLICY_PAY, $calendar->startyear, $this->branchId);
		if(!empty($policy) && !empty($policy->configs) && isset($policy->configs[ENABLE_CONSTANT_TUITION]) && $policy->configs[ENABLE_CONSTANT_TUITION]){
			$enabledConstantTuition = true;
		}
		//老生老办法需要使用开始年（startYear）学校
		$useStartYearOfSchool = false;
		if (in_array($this->branchId, $this->constantSchoolList)){
			$useStartYearOfSchool = true;
		}
		$this->render('constant_'.$flag, array("calendar"=>$calendar, "pageSubMenu"=>$pageSubMenu, "enabledConstantTuition"=>$enabledConstantTuition,'discountList'=>$discountList,"useStartYearOfSchool"=>$useStartYearOfSchool));
	}

	public function actionProcessConstant(){
		if(Yii::app()->request->isPostRequest){
			if (Yii::app()->request->getIsAjaxRequest()) {
				if(Yii::app()->user->checkAccess('tSuperAdmin')||Yii::app()->user->checkAccess('ivystaff_pd')||in_array(Yii::app()->user->id, array(8007998, 8012568))){
					if(isset($_POST['childid']) && isset($_POST['value'])){
						extract($_POST);
						Yii::import('common.models.invoice.ChildConstantTuition');
						$child = ChildProfileBasic::model()->with(array(
							'constant'=>array('params'=>array(':schoolid'=>$this->branchId))
						))->findByPk($childid);
						if($child){
							if(is_null($child->constant)){
								$child->constant = new ChildConstantTuition;
								$child->constant->tuition_type = 'FULL5D';
								$child->constant->schoolid = $this->branchId;
								$child->constant->childid = $childid;
							}
							if (in_array($this->branchId, $this->constantSchoolList)){
								$child->constant->startyear = intval($value);
								$child->constant->amount = 0.00;
							}else{
                                $child->constant->schoolid = $this->branchId;
								$child->constant->amount = intval($value);
							}
							if(!$child->constant->save()){
								$this->addMessage('state', 'fail');
								$this->addMessage('message', Yii::t("global","System Error"));
							}else{
								$this->addMessage('state', 'success');
								$this->addMessage('data', (in_array($this->branchId, $this->constantSchoolList))? $child->constant->startyear : $child->constant->amount);
								$this->addMessage('message', Yii::t("global","Data Saved!"));
							}
							$this->showMessage();
						}
					}
				}else{
					$this->addMessage('state', 'fail');
					$this->addMessage('message', Yii::t("global","No Permission!"));
					$this->showMessage();
				}

			}
		}
		$this->addMessage('state', 'fail');
		$this->addMessage('message', Yii::t("global","System Error"));
		$this->showMessage();

	}

	public function actionOperations(){
		$category = Yii::app()->request->getParam("category", "bus");
		if(!isset($this->catOp)) $category = 'bus';

		$_GET['category'] = $category;
		$this->branchMainMenu['operations']['label'] = $this->catOp[$category];

		$func = 'doOperations'.ucfirst($category);
		try{
			$this->$func();
		}catch(Exception $e){
			echo $e->getMessage();
		}
	}

	public function doOperationsBus(){
		$type = Yii::app()->request->getParam("type", "busassigned");
		if(!in_array($type, array('busassigned','namelist'))) $category = 'busassigned';
		$_GET['type'] = $type;

        $pageSubMenu = array(
			array(
				'label' => '分配孩子',
				'url' => array('//child/list/operations', 'category'=>'bus')
			),
			array(
				'label' => '校车管理',
				'url' => array('//child/list/operations', 'category'=>'busList')
			),
		);

		Yii::import('common.models.calendar.Calendar');
		Yii::import('common.models.invoice.Invoice');
		Yii::import('common.models.schoolbus.*');
		Yii::import('common.models.child.*');

        if (isset($_GET['act'])){
            $func = 'doAct'.ucfirst($_GET['act']);
            $this->$func();
            Yii::app()->end();
        }

		$branch = Branch::model()->with('calendar')->findByPk($this->branchId);

        $startyear = Yii::app()->request->getParam("startyear", $branch->calendar->startyear);

        $criteira = new CDbCriteria();
        $criteira->compare('branchid', $this->branchId);
        $criteira->compare('state', 10);
        $criteira->order='bus_title asc';
		$schoolBuses = SchoolBus::model()->with(array('child'=>array(
            'condition'=>'startyear=:startyear',
            'params'=>array(':startyear'=>$startyear),
            'order'=>'time1'
            ),
        ))->findAll($criteira);

        $cpids = array();
        $pids = array();
        foreach ($schoolBuses as $bus){
            foreach ($bus->child as $bc){
                if ($bc->childinfo->fid)
                    $pids[$bc->childinfo->fid]=$bc->childinfo->fid;
                if ($bc->childinfo->mid)
                    $pids[$bc->childinfo->mid]=$bc->childinfo->mid;
                $cpids[$bc->childid]=array('f'=>$bc->childinfo->fid,'m'=>$bc->childinfo->mid);
            }
        }

        $criteria = new CDbCriteria();
        $criteria->compare('pid', $pids);
        $criteria->index='pid';
        $parents = IvyParent::model()->findAll($criteria);
        foreach ($cpids as $ccid=>$cp){
            $pphones[$ccid] = array('f'=>$parents[$cp['f']]->mphone, 'm'=>$parents[$cp['m']]->mphone);
        }

		$this->render('operations/schoolbus',array('branch'=>$branch, 'schoolBuses'=>$schoolBuses, 'startyear'=>$startyear, 'pageSubMenu'=>$pageSubMenu, 'pphones'=>$pphones));
	}

    public function doActEditbus()
    {
        $id = Yii::app()->request->getParam('id', 0);
        $startyear = Yii::app()->request->getParam('startyear', 0);
        $this->layout = '//layouts/dialog';
        $this->dialogWidth = 600;
        $this->branchObj = Branch::model()->findByPk($this->branchId);

        $model = SchoolBusChild::model()->findByPk($id);
        if ($model === null){
            $model = new SchoolBusChild;
            $model->startyear=$startyear;
        }

        if(isset($_POST['SchoolBusChild'])){
            $this->addMessage('state', 'fail');
            $this->addMessage('message', Yii::t('message','Failed!'));
            if ($id){
                $model->setAttributes($_POST['SchoolBusChild']);
                $model->schoolid = $this->branchId;
                $model->userid = Yii::app()->user->id;
                $model->update_timestamp = time();
                if ( !isset($_POST['SchoolBusChild']['time1']) ){
                    $model->time1='N/A';
                    $model->addr1='N/A';
                }
                if ( !isset($_POST['SchoolBusChild']['time2']) ){
                    $model->time2='N/A';
                    $model->addr2='N/A';
                }
                if ($model->save()){
                    $this->addMessage('state', 'success');
                    $this->addMessage('message', Yii::t('message','success'));
                    $this->addMessage('refresh', true);
                }
                else {
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', Yii::t('message','Failed!'));
                }
            }
            else {
                foreach ($_POST['SchoolBusChild']['childid'] as $childid){
                    $model = new SchoolBusChild;
                    $model->setAttributes($_POST['SchoolBusChild']);
                    $model->childid = $childid;
                    $model->schoolid = $this->branchId;
                    $model->userid = Yii::app()->user->id;
                    $model->update_timestamp = time();
                    if ( !isset($_POST['SchoolBusChild']['time1']) ){
                        $model->time1='N/A';
                        $model->addr1='N/A';
                    }
                    if ( !isset($_POST['SchoolBusChild']['time2']) ){
                        $model->time2='N/A';
                        $model->addr2='N/A';
                    }
                    if ($model->save()){
                        $this->addMessage('state', 'success');
                        $this->addMessage('message', Yii::t('message','success'));
                        $this->addMessage('refresh', true);
                    }
                    else {
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', Yii::t('message','Failed!'));
                    }
                }
            }
            $this->showMessage();
        }

        $this->render('operations/editbus', array('model'=>$model));
    }

    public function doActExport()
    {
        $id = Yii::app()->request->getParam('id', 0);
        $startyear = Yii::app()->request->getParam('startyear', 0);

        $model=SchoolBus::model()->with(array('child'=>array(
            'condition'=>'startyear=:startyear',
            'params'=>array(':startyear'=>$startyear))
        ))->findByPk($id);

        header("Content-type:application/vnd.ms-excel");
        header("Content-Disposition:filename=BUS-".$model->bus_title.".xls");

        if ($model->child){
            $cpids = array();
            $pids = array();
            foreach ($model->child as $bc){
                if ($bc->childinfo->fid)
                    $pids[$bc->childinfo->fid]=$bc->childinfo->fid;
                if ($bc->childinfo->fid)
                    $pids[$bc->childinfo->mid]=$bc->childinfo->mid;
                $cpids[$bc->childid]=array('f'=>$bc->childinfo->fid,'m'=>$bc->childinfo->mid);
            }

            $criteria = new CDbCriteria();
            $criteria->compare('pid', $pids);
            $criteria->index='pid';
            $parents = IvyParent::model()->findAll($criteria);
            foreach ($cpids as $ccid=>$cp){
                $pphones[$ccid] = array('f'=>$parents[$cp['f']]->mphone, 'm'=>$parents[$cp['m']]->mphone);
            }
            echo _t('校车编号')."\t";
            echo _t($model->bus_title)."\n";
            echo _t("车牌号")."\t";
            echo _t($model->bus_code)."\n";
            echo _t("车型")."\t";
            echo _t($model->bus_type)."\n";
            echo _t("实际座位数")."\t";
            echo _t($model->acctual_children)."\n";
            echo _t("司机姓名")."\t";
            echo _t($model->driver_name)."\n";
            echo _t("司机手机")."\t";
            echo _t($model->driver_mobile)."\n";
            echo _t("阿姨姓名")."\t";
            echo _t($model->aunt_name)."\n";
            echo _t("阿姨手机")."\t";
            echo _t($model->aunt_mobile)."\n";
            echo "\n";
            echo _t('孩子姓名')."\t";
            echo _t('班级')."\t";
            echo _t('接站时间')."\t";
            echo _t('接站地址')."\t";
            echo _t('送达时间')."\t";
            echo _t('送达地址')."\t";
            echo _t('孩子联系人')."\t";
            echo _t('父母信息')."\t";
            echo _t('备注')."\n";
            foreach ($model->child as $child){
                echo _t($child->childinfo->getChildName())."\t";
                echo _t($child->childinfo->ivyclass->title)."\t";
                echo _t($child->time1)."\t";
                echo _t($child->addr1)."\t";
                echo _t($child->time2)."\t";
                echo _t($child->addr2)."\t";
                if ($child->childmisc->upickup){
                    $txt = $child->childmisc->upickup;
                    $txt=preg_replace("/\s/","",$txt);
                    echo _t(trim($txt))."\t";
                }else{
                    echo "\t";
                }
                $txt = '';
                $txt .= $pphones[$child->childid]['f'] ? "父亲：".$pphones[$child->childid]['f'] : '';
                $txt .= $pphones[$child->childid]['m'] ? "母亲：".$pphones[$child->childid]['m'] : '';
                $txt=preg_replace("/\s/","",$txt);
                echo _t(trim($txt))."\t";
                echo _t($child->remark)."\n";
            }
        }
    }

    public function doOperationsBusList()
    {
        $pageSubMenu = array(
			array(
				'label' => '分配孩子',
				'url' => array('//child/list/operations', 'category'=>'bus')
			),
			array(
				'label' => '校车管理',
				'url' => array('//child/list/operations', 'category'=>'busList')
			),
		);

        Yii::import('common.models.schoolbus.*');
        Yii::import('common.models.operations.IvyVendor');

        if (isset($_GET['act'])){
            $func = 'doAct'.ucfirst($_GET['act']);
            $this->$func();
            Yii::app()->end();
        }

        $criteria = new CDbCriteria();
        $criteria->compare('branchid', $this->branchId);
        $criteria->with='vendor';
        $dataProvider = new CActiveDataProvider('SchoolBus', array(
            'criteria'=>$criteria,
            'pagination'=>array('pageSize'=>20),
        ));

        $this->render('operations/schoolbuslist',array('pageSubMenu'=>$pageSubMenu, 'dataProvider'=>$dataProvider));
    }

    public function doActEditBusList()
    {
        Yii::import('common.models.operations.*');
        $id = Yii::app()->request->getParam('id', 0);
        $this->layout = '//layouts/dialog';
        $this->dialogWidth = 600;
        $this->branchObj = Branch::model()->findByPk($this->branchId);

        $model = SchoolBus::model()->findByPk($id);
        if ($model === null)
            $model = new SchoolBus;

        if(isset($_POST['SchoolBus'])){
            $model->setAttributes($_POST['SchoolBus']);
            $model->userid = Yii::app()->user->id;
            $model->update_timestamp = time();
            if ($model->save()){
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message','success'));
                $this->addMessage('refresh', true);
            }
            else {
                $this->addMessage('state', 'fail');
                $errs = current($model->getErrors());
                $this->addMessage('message', $errs?$errs[0]:Yii::t('message','Failed!'));
            }
            $this->showMessage();
        }

        $cityModel = Term::model()->city()->findAll();

        $cityArr = array();
        if (!$model->isNewRecord){
            $criteria = new CDbCriteria();
            $criteria->compare('t.diglossia_id', $model->city_id);
            $criteria->compare('vendor.vendor_type_id', array(2,15));
            $items = IvyVendorCitylink::model()->with('vendor')->findAll($criteria);

            foreach($items as $item){
                $cityArr[$item->vendor_id] = CommonUtils::autoLang($item->vendor->cn_title, $item->vendor->en_title);
            }
        }

        $this->render('operations/editbuslist', array('model'=>$model, 'cityModel'=>$cityModel, 'cityArr'=>$cityArr));
    }

    public function doActDelBusList()
    {
        $id = Yii::app()->request->getParam('id', 0);
        $model = SchoolBus::model()->findByPk($id);
        if ($model->delete()){

        }
    }

    public function doActDelbus()
    {
        $id = Yii::app()->request->getParam('id', 0);
        $model = SchoolBusChild::model()->findByPk($id);
        if ($model->delete()){
            $this->addMessage('state', 'success');
            $this->addMessage('message', Yii::t('message','success'));
        }
        else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', Yii::t('message','Failed!'));
        }
        $this->showMessage();
    }

	public function doOperationsClasses()
    {

        Yii::import('common.models.calendar.*');

        $branch = Branch::model()->findByPk($this->branchId);
        $yid = Yii::app()->request->getParam('yid', $branch->schcalendar);

        if (isset($_GET['act'])){
            $func = 'doAct'.ucfirst($_GET['act']);
            $this->$func();
            Yii::app()->end();
        }

        $schyears = array();
        $criteria = new CDbCriteria;
        $criteria->compare('branchid', $this->branchId);
        $criteria->order='startyear desc';
        $models = CalendarSchool::model()->findAll($criteria);
        foreach($models as $mod){
            $schyears[$mod->yid] = $mod->startyear.' ~ '.($mod->startyear+1);
        }

        $criteria = new CDbCriteria();
        $criteria->compare('yid', $yid);
        $criteria->compare('schoolid', $this->branchId);

        $dataProvider=new CActiveDataProvider('IvyClass', array(
			'criteria'=>$criteria,
			'pagination'=>array(
				'pageSize'=>20,
			),
		));

        $this->render('operations/classes', array('schyears'=>$schyears, 'yid'=>$yid, 'dataProvider'=>$dataProvider));
	}

    /*
    public function doActEditClass()
    {
        $id = Yii::app()->request->getParam('id', 0);
        $model = IvyClass::model()->with('schoolInfo')->findByPk($id);
        if ($model  === null)
            $model = new IvyClass;

        if ($this->branchId != $model->schoolid){
            $model->schoolid = $this->branchId;
        }

        if (isset($_POST['IvyClass'])){
            $oldPhoto = $model->picture;
            $model->attributes=$_POST['IvyClass'];
            $model->periodtime=implode('|', $_POST['IvyClass']['periodtime']);

            $model->uploadPhoto = CUploadedFile::getInstance($model, 'uploadPhoto');
            if ($model->uploadPhoto) {
                $delold = $oldPhoto == 'blank.gif' ? false : true;
                $upResult = OA::processPicUpload($model->uploadPhoto, 'classPhoto', $oldPhoto, $delold);
                $model->picture = count($upResult)==3 ? $upResult['filename'] : 'blank.gif';
            }
            if ($model->save()){
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message','success'));
            }
            else {
                $this->addMessage('state', 'fail');
                $errs = current($model->getErrors());
                $this->addMessage('message', $errs?$errs[0]:Yii::t('message','Failed!'));
            }
            $this->showMessage();
        }

        $schyears = array();
        $criteria = new CDbCriteria;
        $criteria->compare('branchid', $this->branchId);
        $criteria->order='startyear desc';
        $models = CalendarSchool::model()->findAll($criteria);
        foreach($models as $mod){
            $schyears[$mod->yid] = $mod->startyear.' ~ '.($mod->startyear+1);
            if ($mod->is_selected == 1){
                break;
            }
        }

        $this->render('operations/_class_form', array('model'=>$model, 'schyears'=>$schyears));
    }
    */

    public function doActTargetNumber()
    {
        $this->layout = '//layouts/dialog';
        $this->dialogWidth = 400;

        $id = Yii::app()->request->getParam('id', 0);

        $model = IvyClass::model()->findByPk($id);

        if (isset($_POST['IvyClass'])){
            $model->attributes=$_POST['IvyClass'];
            if ($model->save()){
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message','success'));
            }
            else {
                $errs = current($model->getErrors());
                $this->addMessage('message', $errs?$errs[0]:Yii::t('message','Failed!'));
            }
            $this->showMessage();
        }

        $this->render('operations/_target_form', array('model'=>$model));
    }

    public function actionProcessVendor()
    {
        $id = Yii::app()->request->getParam('id', 0);

        Yii::import('common.models.operations.*');

        $criteria = new CDbCriteria();
        $criteria->compare('t.diglossia_id', $id);
        $criteria->compare('vendor.vendor_type_id', array(2,15));
        $items = IvyVendorCitylink::model()->with('vendor')->findAll($criteria);

        $ret = array();
        foreach($items as $item){
            $ret[$item->vendor_id] = CommonUtils::autoLang($item->vendor->cn_title, $item->vendor->en_title);
        }

        echo CJSON::encode($ret);
    }

    public function actionSubsidy()
    {
        Yii::import('common.models.invoice.Invoice');
        if(Yii::app()->request->isPostRequest){
			if (Yii::app()->request->getIsAjaxRequest()) {
                $type = Yii::app()->request->getParam('type',null);
                $childId = Yii::app()->request->getParam('childId',0);
                $invoiceId = Yii::app()->request->getParam('invoiceId',0);
                if ($invoiceId && $childId && !empty($type)){
                    $model = Invoice::model()->findByPk($invoiceId);
                    if (!empty($model) && ($model->childid == $childId)) {
                        if ($type == 'again') {
                            $oldModel = Invoice::model()->find('flag='.$invoiceId);
                            $ret = $this->renderPartial('subsidy_form',array('model'=>$model,'type'=>$type,'oldModel'=>$oldModel),true);
                        }elseif ($type == 'change') {
                            if ($model->status == Invoice::STATS_UNPAID){
                                $ret = $this->renderPartial('subsidy_form',array('model'=>$model,'type'=>$type),true);
                            }
                        }
                    }
                    echo CJSON::encode($ret);
                }
            }
        }
    }

    public function actionProcessSubsidy()
    {
        Yii::import('common.models.invoice.Invoice');
         if(Yii::app()->request->isPostRequest){
			if (Yii::app()->request->getIsAjaxRequest()){
                $type = Yii::app()->request->getParam('type', null);
                $childId = Yii::app()->request->getParam('childId', 0);
                $amount = Yii::app()->request->getParam('amount', 0);
                $invoiceId = Yii::app()->request->getParam('invoiceId', 0);
                if ($invoiceId && $childId && !empty($type) && $amount) {
                    $model = Invoice::model()->findByPk($invoiceId);
                    if (!empty($model) && ($model->childid == $childId)) {
                        if ($type == 'again') {
                            $oldModel = Invoice::model()->find('flag=' . $invoiceId);
                            $model = new Invoice;
                            $model->setAttributes($oldModel->getAttributes());
                            $model->title = "补".$oldModel->title;
                            $model->original_amount = $amount;
                            $model->amount = $amount;
                            $model->nodiscount_amount = null;
                            $model->receivable_status = 0;
                            $model->apportion_status = 0;
                            $model->send_timestamp = 0;
                            $model->memo = '';
                            $model->discount_id  = 0;
                            $model->userid = Yii::app()->user->getId();
                            $model->timestamp = time();
                            $model->status = Invoice::STATS_UNPAID;
                            $model->last_paid_timestamp = 0;
                            if ($model->save()) {
                                $ret = '成功';
                            }else{
                                $ret = '失败';
                            }
                        }elseif ($type == 'change') {
                            if ($model->status == Invoice::STATS_UNPAID) {
                               $model->original_amount = $amount;
                               $model->amount = $amount;
                               $model->userid = Yii::app()->user->getId();
                               $model->timestamp = time();
                               if ($model->save()){
                                   $ret = '成功';
                               }else{
                                   $ret = '失败';
                               }
                            }
                        }
                    }else{
                         $ret = '参数不匹配';
                    }
                    echo CJSON::encode($ret);
                }
            }
         }
    }

    public function actionExportChildInfo(){
        Yii::import('common.models.child.ChildMisc');
        //调取孩子所在国家（全部国家信息有缓存）
        $countryList = Country::model()->getCountryList();
        $basicModel = ChildProfileBasic::model()->with('ivyclass','misc')->findAll('t.status=:status and t.schoolid=:schoolid',array(':status'=>ChildProfileBasic::STATS_ACTIVE,':schoolid'=>$this->branchId));
        header("Content-type:application/vnd.ms-excel");
        header("Content-Disposition:filename=".$this->branchId."_child_list.xls");
        if (!empty($basicModel)){
            //父亲信息
            $fids = CHtml::listData($basicModel, 'fid', 'fid');
            $crite = new CDbCriteria;
            $crite->select = 't.cn_name,t.en_firstname,t.en_lastname,t.mphone';
            $crite->compare('pid', $fids);
            $crite->index = 'pid';
            $fInfo = IvyParent::model()->with('ivyUser')->findAll($crite);
            //母亲信息
            $mids = CHtml::listData($basicModel, 'mid', 'mid');
            $crite = new CDbCriteria;
            $crite->select = 't.cn_name,t.en_firstname,t.en_lastname,t.mphone';
            $crite->index = 'pid';
            $crite->compare('pid', $mids);
            $mInfo = IvyParent::model()->with('ivyUser')->findAll($crite);
            echo _t('编号')."\t";
            echo _t('中文名字')."\t";
            echo _t('英文名字')."\t";
            echo _t('生日')."\t";
            echo _t('昵称')."\t";
            echo _t('姓别')."\t";
            echo _t('国家')."\t";
            echo _t('班级')."\t";
            echo _t('帐户余额')."\t";
            echo _t('发票抬头')."\t";
            echo _t('注册时间')."\t";
            echo _t('照片授权')."\t";
            echo _t('特殊餐')."\t";
            echo _t('接种疫苗')."\t";
            echo _t('体检')."\t";
            echo _t('爸爸')."\t";
            echo _t('邮箱')."\t";
            echo _t('电话')."\t";
            echo _t('妈妈')."\t";
            echo _t('邮箱')."\t";
            echo _t('电话')."\n";
//            $childStr = "编号,姓名,生日,昵称,姓别,国家,班级,帐户余额,发票抬头,注册时间\r\n";
            foreach ($basicModel as $val){
                $gender = $val->gender == 1 ? '男' : '女';
                $photo = ($val->misc == null || $val->misc->agree_photo_open) == 1 ? '是' : '否';
                $allergy = $val->misc->sign_allergy == 1 ?  '是' : '否';
                $vaccine = $val->misc->vaccine == 1 ?  '是' : '否';
                $physical = $val->misc->physical == 1 ?  '是' : '否';
//                $childStr.= $val->childid.",".$val->getChildName().",".$val->birthday_search.",".$val->nick.",".$gender.",".$countryList[$val->country].",".$val->ivyclass->title.",".$val->credit.",".$val->invoice_title.",".OA::formatDateTime($val->created_timestamp,'short')."\r\n";
                 echo $val->childid."\t";
                 echo _t(trim($val->name_cn))."\t";
                 echo _t(trim($val->last_name_en).' '.trim($val->first_name_en))."\t";
                 echo $val->birthday_search."\t";
                 echo _t($val->nick)."\t";
                 echo _t($gender)."\t";
                 echo _t($countryList[$val->country])."\t";
                 echo _t($val->ivyclass->title)."\t";
                 echo $val->credit."\t";
                 echo _t($val->invoice_title)."\t";
                 echo OA::formatDateTime($val->created_timestamp,'short')."\t";
                 echo _t($photo)."\t";
                 echo _t($allergy)."\t";
                 echo _t($vaccine)."\t";
                 echo _t($physical)."\t";
                 echo _t($fInfo[$val->fid]->getName())."\t";
                 echo " ".$fInfo[$val->fid]->ivyUser->email."\t";
                 echo _t($fInfo[$val->fid]->mphone)."\t";
                 echo _t($mInfo[$val->mid]->getName())."\t";
                 echo " ".$mInfo[$val->mid]->ivyUser->email."\t";
                 echo _t($mInfo[$val->mid]->mphone)."\n";
            }
            unset($childStr);
            unset($basicModel);
        }
    }
}

function _t($str='')
{
    $str = "\"".$str."\"";
    return iconv("UTF-8", "GBK", $str);
}

