<!-- Modal -->
<?php echo CHtml::form('', 'post', array('class' => 'J_ajaxForm', 'autocomplete' => 'off')); ?>
<div class="modal fade" id="<?php echo $data['modalNameId'] ?>" tabindex="-1" role="dialog" aria-labelledby="workflowModalLabel" data-backdrop="static">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id=""><?php echo CommonUtils::autoLang($data['workflow']->definationObj->defination_name_cn, $data['workflow']->definationObj->defination_name_en); ?> - <?php echo CommonUtils::autoLang($data['workflow']->nodeObj->node_name_cn, $data['workflow']->nodeObj->node_name_en); ?></h4>
            </div>
            <div class="modal-body" id="workflow-modal-body">
                <?php if ($data['workflow']->useRole != 1) : ?>
                    <div class="justify-content-center">
                        <div class="col-md-8">
                            <!-- 使用Bootstrap的警告框样式 -->
                            <div class="alert alert-danger text-center" role="alert">
                                <h2> 您无提交权限。</h2>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
                <div class="row">
                    <div class="col-xs-12 form-group">
                        <label class="col-xs-4">合同标题 *：</label>
                        <div class="col-xs-4">
                            <input name="title" class="form-control" type="text" value="<?php echo $data['contractApplyModel']->title; ?>">
                        </div>
                    </div>
                    <div class="col-xs-12 form-group">
                        <label class="col-xs-4">合同甲方 *：</label>
                        <div class="col-xs-4">
                            <input id="party_a" name="party_a" class="form-control" value="<?php echo $data['contractApplyModel']->party_a; ?>" placeholder="合同方">
                        </div>
                    </div>

                    <div class="col-xs-12 form-group">
                        <label class="col-xs-4">合同乙方 *：</label>
                        <div class="col-xs-4">
                            <input id="party_b" name="party_b" class="form-control" value="<?php echo $data['contractApplyModel']->party_b; ?>" placeholder="合同方">
                        </div>
                    </div>

                    <div class="col-xs-12 form-group">
                        <label class="col-xs-4">合同开始 *：</label>
                        <div class="col-xs-4">
                            <input id="start_date" name="start_date" class="form-control date" value="<?php echo $data['contractApplyModel']->start_date; ?>" placeholder="合同开始">
                        </div>
                    </div>

                    <div class="col-xs-12 form-group">
                        <label class="col-xs-4">合同结束 *：</label>
                        <div class="col-xs-4">
                            <input id="end_date" name="end_date" class="form-control date" value="<?php echo $data['contractApplyModel']->end_date; ?>" placeholder="合同结束">
                        </div>
                    </div>
                    <div class="col-xs-12 form-group">
                        <label class="col-xs-4">合同类型 *：</label>
                        <div class="col-xs-4">
                            <?php echo CHtml::dropDownList('category', $data['contractApplyModel']->category, $data['categoryList'], array('class' => 'form-control', 'empty' => Yii::t('global', 'Please Select'))); ?>
                        </div>
                    </div>
                    <div class="col-xs-12 form-group">
                        <label id="price_title" class="col-xs-4">合同总额（含税） *：</label>
                        <div class="col-xs-4">
                            <p id="price_warning" class="mb5" style="display: none;color: #B8860B;">框架合同的金额为预估金额，仅作为内部参考，不作为结算依据。</p>
                            <input id="price_total" type="number" min="0" name="price_total" class="form-control" value="<?php echo $data['contractApplyModel']->price_total; ?>" placeholder="输入金额">
                        </div>
                    </div>
                    <div class="col-xs-12 form-group">
                        <label class="col-xs-4">付款周期 *：</label>
                        <div class="col-xs-4">
                                <?php echo CHtml::dropDownList('cycle', $data['contractApplyModel']->cycle, $data['cycleList'], array('class' => 'form-control', 'empty' => Yii::t('global', 'Please Select'))); ?>
                        </div>
                    </div>
                    <div class="col-xs-12 form-group">
                        <label class="col-xs-4">收款方 *：</label>
                        <div class="col-xs-4">
                            <input id="vendor_id" name="vendor_id" value="<?php echo $data['contractApplyModel']->vendor_id; ?>" hidden>
                            <input id="bank_user" name="bank_user" class="form-control ui-autocomplete-input" value="<?php echo $data['contractApplyModel']->bank_user; ?>" placeholder="输入关键字搜索供应商" autocomplete="off">
                        </div>
                    </div>
                    <div class="col-xs-12 form-group">
                        <label class="col-xs-4">银行账号 *：</label>
                        <div class="col-xs-4">
                            <input id="bank_account" name="bank_account" class="form-control" type="text" value="<?php echo $data['contractApplyModel']->bank_account; ?>">
                        </div>
                    </div>
                    <div class="col-xs-12 form-group">
                        <label class="col-xs-4">开户银行 *：</label>
                        <div class="col-xs-4">
                            <input id="bank_name" name="bank_name" class="form-control" type="text" value="<?php echo $data['contractApplyModel']->bank_name; ?>">
                        </div>
                    </div>

                    <!-- 选择是否为预算标准 -->
                    <div class="col-xs-12 form-group">
                        <label class="col-xs-4">预算标准 *：</label>
                        <div class="col-xs-4 input-group">
                            <div class="col-xs-6">
                                <label>
                                    <input <?php if (!$data['paymentApply']->id > 0 || $data['paymentApply']->budget == 1) echo 'checked'; ?> type="radio" name="budget" value="1">
                                    预算内
                                </label>
                            </div>
                            <div class="col-xs-6">
                                <label>
                                    <input <?php if ($data['paymentApply']->id > 0 && $data['paymentApply']->budget == 0) echo 'checked'; ?> type="radio" name="budget" value="0">
                                    预算外
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-12 form-group">
                        <label class="col-xs-4">合同标的 *：</label>
                        <div class="col-xs-4">
                            <textarea class="form-control" name="memo" rows="3"></textarea>
                        </div>
                    </div>
                    <div class="col-xs-12 form-group">
                        <div class="well">
                            提交前请确认已通过邮件审批
                        </div>
                    </div>
                    <div class="col-xs-12">
                        <!-- 显示附件 -->
                        <div id="fileList">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>已上传资料</th>
                                        <th>资料说明</th>
                                        <th>操作</th>
                                    </tr>
                                    <?php foreach ($data['workflow']->getAttachmentList() as $uploadFile) : ?>
                                        <tr>
                                            <td><a target="_blank" href="<?php echo $uploadFile['bigimages']; ?>"><?php echo $uploadFile['notes']; ?></a></td>
                                            <td><input type="text" onchange="changFileName(this,<?php echo $uploadFile['id'] ?>)" class="form-control" placeholder="输入资料说明(XX班级XX申请单)" /></td>
                                            <td>
                                                <a class="btn btn-danger btn-xs" onclick="deleteFile(this,<?php echo $uploadFile['id'] ?>)"><span class="glyphicon glyphicon-trash"> </span></a>
                                            </td>
                                            <input type="hidden" name="files[]" value="<?php echo $uploadFile['id'] ?>">
                                        </tr>
                                    <?php endforeach; ?>
                                </thead>
                            </table>
                        </div>
                        <!-- 上传 -->
                        <div class="btn-group btn-group-justified" role="group">
                            <input id="file" type="file" name="file" multiple="true" style="display:none">
                            <div class="btn-group" role="group">
                                <a id="filebtn" class="btn btn-default" onclick="$('#file').click();">上传新资料</a>
                            </div>
                        </div>
                        <br>
                        <span class="text-warning" id="spaninfo"></span>
                    </div>

                </div>
            </div>
            <div class="retshow">
                <h3><?php echo Yii::t('workflow', 'Comments:'); ?></h3>
                <?php
                $processList = $data['workflow']->getWorkflowProcess();
                $processLabelList = array(
                    WorkflowOperation::WORKFLOW_STATS_STEP => '退回修改',
                    WorkflowOperation::WORKFLOW_STATS_RESET => '退回修改',
                    WorkflowOperation::WORKFLOW_STATS_UNOP =>  '通过',
                    WorkflowOperation::WORKFLOW_STATS_OPDENY =>  '拒绝并作废',
                    WorkflowOperation::WORKFLOW_STATS_OPED =>  '通过',
                );
                foreach ($processList as $process) : $processLabel = $processLabelList[$process->state];
                    if ($data['workflow']->nodeIds[0] == $process->current_node_index) {
                        $processLabel = '提交';
                    } ?>
                    <h4>
                        <em><?php echo Yii::t('workflow', 'Date submitted:'); ?> <?php echo date("Y-m-d H:i:s", $process->start_time); ?></em>
                        <span><?php echo $process->user->getName(); ?></span>
                    </h4>
                    <div class="clearfix"></div>
                    <div class="comment">
                        <label><?php echo $processLabel . ': '; ?></label>
                        <p><?php echo nl2br(htmlspecialchars($process->process_desc)); ?></p>
                    </div>
                <?php endforeach; ?>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('global', 'Cancel'); ?></button>
                <button type="button" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global', 'Submit'); ?></button>
            </div>
        </div>
    </div>
</div>
<?php CHtml::endForm(); ?>
<script>
    const definationId = <?php echo $data['workflow']->definationObj->defination_id; ?>;
    const operationId = '<?php echo $data['workflow']->getOperateId(); ?>';
    const nodeId = '<?php echo $data['workflow']->nodeObj->node_id; ?>';
    const branchId = '<?php echo $data['workflow']->schoolId; ?>';


    $('.date').datepicker({
        changeMonth: true,
        changeYear: true,
        'dateFormat': 'yy-mm-dd'
    });



    //上传文件
    $(function() {
        const searchUrl = '<?php echo Yii::app()->createUrl("/workflow/index/searchVendor", array('branchId' => $data['workflow']->schoolId)); ?>';

        $("#bank_user").autocomplete({
            source: searchUrl,
            // minLength: 2,
            select: function(event, ui) {
                if (ui.item.value <= 0) {
                    return false;
                }
                $("#vendor_id").val(ui.item.vendor_id);
                $("#bank_user").val(ui.item.bank_user);
                $("#bank_name").val(ui.item.bank_name);
                $("#bank_account").val(ui.item.bank_account);
            },
        });

        $('#file').change(function() {
            $('#spaninfo').text("");
            var action = 'saveUploadFile';
            var url = '<?php echo Yii::app()->createUrl("/workflow/entrance/index") . "?definationId=' +definationId+ '&nodeId=' +nodeId+ '&branchId=' +branchId+ '&operationId=' +operationId+ '&action=' +action+ '"; ?>';
            // 循环 this.files
            for (var i = 0; i < this.files.length; i++) {
                var formData = new FormData();
                var _file = this.files[i]
                var fileName = _file.name
                formData.append('file', _file);

                $.ajax({
                    url: url,
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(data) {
                        var msg = eval('(' + data + ')');
                        if (msg.state == 'success') {
                            var html = '<tr><td><a target="_blank" href="' + msg.data.url + '">' + msg.data.fileName + '</a></td>';
                            html += '<td><input type="text" onchange=changFileName(this,' + msg.data.fileId + ') class="form-control" placeholder="输入资料说明"></td>'
                            html += '<td><a class="btn btn-danger btn-xs" onclick="deleteFile(this,' + msg.data.fileId + ')"><span class="glyphicon glyphicon-trash"> </span></a></td></tr>';
                            html += '<input type="hidden" name="files[]" value=' + msg.data.fileId + ' />';
                            $('#fileList > table').append(html);
                        } else {
                            var oldText = $('#spaninfo').text();
                            $('#spaninfo').text(msg.message + oldText);
                        }
                    }
                });
            }
        });
        
        $('#category').on('change', function() {
            if ($(this).val() == 2) {
                $("#price_title").text('预估金额 *：');
                $("#price_warning").show();
            } else {
                $("#price_title").text('合同总额（含税） *：');
                $("#price_warning").hide();
            }
        });
    });
    //删除文件
    function deleteFile(btn, id) {
        if (confirm('确定要删除这个文件吗?')) {
            var action = 'deleteUploadFile';
            var url = '<?php echo Yii::app()->createUrl("/workflow/entrance/index") . "?definationId=' +definationId+ '&nodeId=' +nodeId+ '&branchId=' +branchId+ '&operationId=' +operationId+ '&action=' +action+ '"; ?>';

            $.ajax({
                url: url,
                type: 'POST',
                data: {
                    id: id
                },
                success: function(data) {
                    var msg = eval('(' + data + ')');
                    if (msg.state == 'success') {
                        $(btn).parent().parent().remove();
                    };
                    $('#spaninfo').text(msg.message);
                }
            });
        };
    }
    //修改文件名
    function changFileName(btn, id) {
        var notes = $(btn).val();
        if (notes == "") {
            return false
        };
        var action = 'changeFileName';
        var url = '<?php echo Yii::app()->createUrl("/workflow/entrance/index") . "?definationId=' +definationId+ '&nodeId=' +nodeId+ '&branchId=' +branchId+ '&operationId=' +operationId+ '&action=' +action+ '"; ?>';

        $.ajax({
            url: url,
            type: 'POST',
            data: {
                id: id,
                notes: notes
            },
            success: function(data) {
                var msg = eval('(' + data + ')');
                if (msg.state == 'success') {
                    var con = $(btn).parent().prev().children().text(notes);
                }
                $('#spaninfo').text(msg.message);
            }
        });
    }
</script>