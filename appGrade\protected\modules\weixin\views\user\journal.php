<div id="journal">
	<div class="child-avatar">
		<?php echo CHtml::image(CommonUtils::childPhotoUrl($child->photo));?>
	</div>
	<h2><?php echo $child->getChildName();?><span><?php echo sprintf('第%s周', $noteObj->weeknumber);?></span></h2>
	<p class="week"><?php echo $noteObj->cInfo->title;?><br />
	<?php echo sprintf('%s - %s', CommonUtils::formatDateTime($noteObj->weekInfo->monday_timestamp), CommonUtils::formatDateTime($noteObj->weekInfo->monday_timestamp + 5 * 24 * 3600));?></p>
	
	<?php if(isset($campusClassRpts[10])):?>

		<?php if(!empty($campusClassRpts[10]->en_important) && strlen($campusClassRpts[10]->en_important)>10):?>
		<h3>要事提醒</h3>
		<div class="html">
			<?php echo MimsAPI::clearHtmlContent($campusClassRpts[10]->en_important);?>
		</div>
		<?php endif;?>
		
		<?php if(!empty($campusClassRpts[10]->en_content) && strlen($campusClassRpts[10]->en_content)>10):?>
		<h3>学校新闻</h3>
		<div class="html">
			<?php
				$CDuids = explode(',',$campusClassRpts[10]->cd_uids);
				$cds = User::model()->with('staffInfo','profile')->findAllByPk($CDuids);
				echo CHtml::openTag('p');
				echo "园长：";
				foreach($cds as $cd){
					echo CHtml::openTag('span',array('class'=>'uname'));
					echo $cd->getName();
					echo CHtml::closeTag('span');
				}
				echo CHtml::closeTag('p');
				
				foreach($cds as $cd){
					$img = Yii::app()->params['uploadBaseUrl'] . 'infopub/staff/' . $cd->staffInfo->staff_photo;
					echo CHtml::image($img, $cd->getName(), array('class'=>'face'));
				}
				
				echo MimsAPI::clearHtmlContent($campusClassRpts[10]->en_content);
			?>
		</div>
		<?php endif;?>
	<?php endif;?>
	
	<?php if(isset($campusClassRpts[20])):?>
		<?php if(!empty($campusClassRpts[20]->en_content) && strlen($campusClassRpts[10]->en_content)>10):?>
		<h3>班级报告</h3>
		<div class="html">
			<p>老师姓名（左起）：
			<?php foreach($teachers as $staff){
				echo CHtml::openTag('span', array('class'=>'uname'));
				echo $staff->userWithProfile->getName();
				echo CHtml::closeTag('span');
			}
			?>
			</p>
			<?php foreach($teachers as $staff){
				$tPhoto = (empty($staff->staffInfo->staff_photo)) ? 'blank.jpg' : $staff->staffInfo->staff_photo;
				$img = Yii::app()->params['uploadBaseUrl'] . 'infopub/staff/' . $tPhoto;
				echo CHtml::image($img, $staff->userWithProfile->getName(), array('class'=>'face'));
			}
			?>
			
			<?php echo MimsAPI::clearHtmlContent($campusClassRpts[20]->en_content);?>
		</div>
		<?php endif;?>
	<?php endif;?>
	
	<?php
	if(!empty($noteObj->en_content) && strlen($noteObj->en_content)>10):
	?>
	<h3>个人报告</h3>
	<div class="html">
		<?php echo MimsAPI::clearHtmlContent($noteObj->en_content);?>
	</div>
	<?php endif;?>
	
	<?php
	if($showSpecialReport):
	?>
	<h3>个人报告</h3>
	<div>
		<?php foreach ($childReportPhotos as $photo):?>
			<?php
				echo CHtml::image($photo->photoInfo->getMediaUrl());
			?>
			<?php if(!empty($photo->content)):?>
			<p class="caption">
				<?php echo $photo->content;?>
			</p>
			<?php endif;?>
		<?php endforeach;?>
	</div>
	<?php endif;?>
	
	<?php if(count($photos)):?>
	<h3>每周视频及照片</h3>
	<div>
		<?php foreach ($photos as $photo):?>
			<?php
                if($photo->photoInfo->type == 'photo'){
                    echo CHtml::image($photo->photoInfo->getMediaUrl());
                }else{
                    echo $photo->photoInfo->renderMedia();
                }

			?>
			<?php if(!empty($photo->content)):?>
			<p class="caption">
				<?php echo $photo->content;?>
			</p>
			<?php endif;?>
		<?php endforeach;?>
	</div>
	<?php endif;?>
</div>