
<style type="text/css">
 [v-cloak] {
	display: none;
}
.att-report th {
	background-color: #f5f5f5;
}
.att-report tbody td {
	text-align: center;
}
th div.progress {
	margin-bottom: 0px;
}
.table{
	color:#606266
}
.table tr th,.table tr td {
	vertical-align:middle !important;
	text-align:center;
	color:#606266
}
.pink{
	background:#FFECEE
}
.progress{
	background:#fff;
}
.loading{
	width:98%;
	height:90%;
	background:#fff;
	position: absolute; 
	opacity: 0.5;
	z-index: 99
}
.loading span{
	width:100%;
	height:60%;
	display:block;
	background: url("<?php echo Yii::app()->theme->baseUrl?>/images/loading.gif")no-repeat center center ;
}
</style>
<div id='container'  v-cloak>
    <h3 class='color3 font14'><strong style='font-size:18px' class='mr20'><?php echo Yii::t('attends', 'Daily Attendance by Period'); ?></strong></h3>
	<div class="form-inline mt15 mb20"> 
		<label for="exampleInputName2"><?php echo Yii::t("admissions", "Date");?></label>
		<input type="text" class="form-control form-group ml10" id="targetDate" placeholder="<?php echo Yii::t("newDS", "Select a date");?>" @change='showData()'   :value='targetDate'>
		<span class="label label-default ml10">#{{weekNum}} <?php echo Yii::t("lunch", "Week");?> / {{category}}</span>
	</div>
	<div class='loading'  v-if='showLoading'>
		<span></span>
	</div>
	<table class="table table-bordered att-report" style='table-layout: fixed'>
		<thead>
			<th width='100'>#</th>
			<th>学生总数</th>
			<th v-for='(list,key,index) in items[1]'>
				<div class='font14'>{{numberTypes[key]}}</div>
			</th>
		</thead>
		<tbody>
			<tr v-for='(list,index) in timetableTimes'>
				<th>
					<p>{{list.label}}</p>
					<div>{{list.timeslot}}</div> 
				</th>
				<td >
					<span v-if='studentSum[list.period]'>{{studentSum[list.period].total}}</span>
				</td>
				<td v-for='(_item,key,i) in items[list.period]'>
					<a href="javascript:;" v-if='_item!=0 && key!=11 && key!=10' @click='showStu(list.period,key)'>{{_item}}</a>
					<span v-else-if='_item!=0'>{{_item}}</span> 
					<!-- <span class="label " v-if='key==10' :class='progress(_item,studentSum[list.period].total)<80?"label-danger":"label-success"'>{{ progress(_item,studentSum[list.period].total)}}%</span> -->
				</td>
			</tr>
		</tbody>
	</table>
	<div class="modal fade" id="childModel" tabindex="-1" role="dialog" aria-labelledby="modal" aria-hidden="false">
		<div class="modal-dialog modal-lg" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
					<h4 class="modal-title" ><?php echo Yii::t('attends','Student List') ?> <span id="remarks_t"></span></h4>
				</div>
				<div class="form-horizontal">
					<div class="modal-body">
						<div class='p10'>
							<!-- <p>
								<button class="btn btn-default mr10" type="button" :class='key==type?"btn-primary":""'  v-for='(list,key,index) in childData.groupCount' @click='showStu(period,key)'>
										{{numberTypes[key]}} <span class="badge">{{list}}</span>
								</button>
							</p> -->
							<ul class="nav nav-pills mb20" role="tablist">
								<li role="presentation"  :class='key==type?"active":""'  v-for='(list,key,index) in childData.groupCount' @click='showStu(period,key)'><a href="javascript:;">{{numberTypes[key]}} <span class="badge">{{list}}</span></a></li>
							</ul>
							<div class="alert alert-danger mt20"  v-if='childData.studentList && childData.studentList.length==0' role="alert"><?php echo Yii::t('attends','No students') ?></div>
							<div class='col-md-4' v-else v-for='(list,index) in childData.studentList' >
								<div class="media mt15">
									<div class="media-left pull-left media-middle">
									<a href="javascript:void(0)">
										<img :src="list.avatar" data-holder-rendered="true" style='width:48px;height:48px' class="media-object img-circle image"></a>
									</div> 
									<div class="media-body pt10 media-middle">
										<h4 class="media-heading font12">({{list.id}}) {{list.name}}</h4> 
										<div class="text-muted">{{list.class_name}}</div>
									</div>
								</div>    
							</div>
							<div class='clearfix'></div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<script>
	
	$(function() {
		$("#targetDate").datepicker({
			dateFormat: "yy-mm-dd ",
		});
		$( "#targetDate").on('change', function () {
			container.targetDate = $('#targetDate').val();
			container.showData()
		});
	});
	var container = new Vue({
        el: "#container",
        data: {
			"targetDate":'',
			"category": "",
			"weekNum":'',
			"numberTypes": {},
			"items": {},
			"studentSum": {},
			"reports": {},
			timetableTimes:'',
			childData:{},
			period:'',
			type:'',
			showLoading:false
        },
		created(){
			this.targetDate=this.newDate()
			this.showData()
		},
        methods: {
			progress(num,total){
				var progress=total <= 0? "0" : Math.round((num / total) * 10000) / 100.0;
				return progress
			},
			newDate(){           
					var date = new Date();
                const Y = date.getFullYear() + '-';
                const M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
                const D = (date.getDate() < 10 ? '0'+date.getDate() : date.getDate()) + ' ';
                const h = (date.getHours() < 10 ? '0'+date.getHours() : date.getHours()) ;
                const m = (date.getMinutes() <10 ? '0'+date.getMinutes() : date.getMinutes());
                const s = date.getSeconds(); // 秒
                const dateString = Y + M + D
                    return dateString;
                
            },
			showData(){
				this.showLoading=true
				let that=this
				$.ajax({
                    url: '<?php echo $this->createUrl("reportR02") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
						targetDate:this.targetDate
                    },
                    success: function(data) {
                        if(data.state=='success'){  
                            that.category=data.data.category
                            that.timetableTimes=data.data.timetableTimes
                            that.weekNum=data.data.weekNum
                            that.numberTypes=data.data.numberTypes
                            that.items=data.data.items
                            that.studentSum=data.data.studentSum
                            that.showLoading=false
                        }else{
                            that.showLoading=false 
                        }
                    }
                })
			},
			showStu(period,type){
				let that=this
				this.period=period
				this.type=type
				$.ajax({
                    url: '<?php echo $this->createUrl("showAttendStudent") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
						targetDate:this.targetDate,
						period:period,
						type:type
                    },
                    success: function(data) {
                        if(data.state=='success'){  
							that.childData=data.data
                        	$('#childModel').modal('show');
                        }else{
                        }
                    }
                })
			}
		}
    })
</script>
