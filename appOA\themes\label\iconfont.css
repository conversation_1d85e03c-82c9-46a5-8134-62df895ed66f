@font-face {
  font-family: "iconfont"; /* Project id 4315231 */
  src: url('iconfont.woff2?t=1731045479586') format('woff2'),
       url('iconfont.woff?t=1731045479586') format('woff'),
       url('iconfont.ttf?t=1731045479586') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-good2:before {
  content: "\e665";
}

.icon-arrow_down1:before {
  content: "\e663";
}

.icon-arrow_up1:before {
  content: "\e664";
}

.icon-good_fill2:before {
  content: "\e662";
}

.icon-process:before {
  content: "\e660";
}

.icon-inspect:before {
  content: "\e661";
}

.icon-success:before {
  content: "\e65f";
}

.icon-gerenshuju:before {
  content: "\e65e";
}

.icon-good:before {
  content: "\e65a";
}

.icon-quote_fill:before {
  content: "\e65b";
}

.icon-find:before {
  content: "\e65c";
}

.icon-good_fill:before {
  content: "\e65d";
}

.icon-screen:before {
  content: "\e659";
}

.icon-triangle_down:before {
  content: "\e653";
}

.icon-heart_fill:before {
  content: "\e654";
}

.icon-rhombus:before {
  content: "\e655";
}

.icon-triangle_up:before {
  content: "\e656";
}

.icon-arrow_up:before {
  content: "\e657";
}

.icon-circle:before {
  content: "\e658";
}

.icon-star:before {
  content: "\e63c";
}

.icon-rhombus_fill:before {
  content: "\e63e";
}

.icon-arrow_right_fill:before {
  content: "\e63d";
}

.icon-heart:before {
  content: "\e640";
}

.icon-flag:before {
  content: "\e641";
}

.icon-triangle_up_fill:before {
  content: "\e642";
}

.icon-arrow_up_fill:before {
  content: "\e643";
}

.icon-triangle_left:before {
  content: "\e644";
}

.icon-arrow_down:before {
  content: "\e645";
}

.icon-triangle_right:before {
  content: "\e646";
}

.icon-flag_fill:before {
  content: "\e647";
}

.icon-circle_fill:before {
  content: "\e648";
}

.icon-triangle_left_fill:before {
  content: "\e649";
}

.icon-triangle_right_fill:before {
  content: "\e64a";
}

.icon-arrow_right:before {
  content: "\e64b";
}

.icon-triangle_down_fill:before {
  content: "\e64c";
}

.icon-asterisk_fill:before {
  content: "\e64d";
}

.icon-star_fill:before {
  content: "\e64e";
}

.icon-arrow_down_fill:before {
  content: "\e64f";
}

.icon-square_fill:before {
  content: "\e650";
}

.icon-arrow_left:before {
  content: "\e651";
}

.icon-square:before {
  content: "\e652";
}

.icon-arrow_left_fill:before {
  content: "\e63f";
}

