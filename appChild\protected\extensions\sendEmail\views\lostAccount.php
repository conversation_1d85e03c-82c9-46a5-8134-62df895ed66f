<?php
if (true === $this->lang || 'cn' == $this->lang) {
    $df_cn = new CDateFormatter('zh-cn');
    $this->render('mailHeader_lang', array('lang' => 'cn'));
    ?>

    <div style="font-family: 'Microsoft Yahei'">

        <p>致校园管理团队，</p>

        <p>家长登录<?php echo Mims::unIvy()?'幼儿园管理系统':'艾毅在线'; ?>时遇到账户未注册问题，请联系家长确认以下信息无误后，再进行账户修改。</p>

        <p>
        孩子姓名：<?php echo $model->childname?><br>
        
        就读校园：<?php echo $school?><br>
        
        孩子生日：<?php echo $df_cn->formatDateTime($model->birthday, 'medium', null)?><br>
        
        家长姓名：<?php echo $model->parentname?><br>
        
        家长邮件：<?php echo $model->email?><br>
        
        家长手机：<?php echo $model->phone?><br>
        
        备注信息：<?php echo $model->memo?><br>
        </p>

        <p>谢谢!</p>

        <p>
            <?php echo $df_cn->formatDateTime($date_time, 'medium', 'short'); ?>
        </p>

        <p>该邮件为系统自动发送。</p>

    </div>

    <?php
    $this->render('mailFooter_lang', array('lang' => 'cn'));
}


?>


<?php
if (true === $this->lang || 'en' == $this->lang) {
    $df_en = new CDateFormatter('en-us');
    $this->render('mailHeader_lang', array('lang' => 'en'));
    ?>

    <div style="font-family: 'Trebuchet MS','Microsoft Yahei',Arial,Helvetica,sans-serif">

        <p>Dear Campus Management Team,</p>
        
        <p>Please verify the information below submitted by a parent who was trying to <?php echo Mims::unIvy()?'the system':'IvyOnline';?> with an unregistered email, and update the account information accordingly.</p>

        <p>
            Name of Child: <?php echo $model->childname?><br>
        
            Enrolled Campus: <?php echo $school?><br>

            Date of Birth: <?php echo $df_cn->formatDateTime($model->birthday, 'medium', null)?><br>

            Parent’s Name: <?php echo $model->parentname?><br>

            Email Address: <?php echo $model->email?><br>

            Phone Number: <?php echo $model->phone?><br>

            Note: <?php echo $model->memo?><br>
        </p>
        
        <p>Thanks!</p>
        
        <p>
            <?php echo $df_en->formatDateTime($date_time, 'medium', 'short'); ?>
        </p>

        <p>This is an automatic email.</p>

    </div>
    <?php
    $this->render('mailFooter_lang', array('lang' => 'en'));
}
?>


