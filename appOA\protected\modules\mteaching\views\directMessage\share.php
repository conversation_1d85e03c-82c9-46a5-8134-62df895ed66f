
<div >
    <div v-if='shareType=="view"' style='margin-bottom:40px'><div class="alert alert-warning p10" role="alert"><?php echo Yii::t("reg", "This Qrcode is for internal preview, DO NOT share to parents.");?></div></div>
    <div class='relative pb20 mt20'>
        <div class='Options'>
            <div><label><?php echo Yii::t("reg", "Options");?></label></div>
            <div class='mb20'>
                <!-- <div class="checkbox">
                    <label>
                        <input type="checkbox" id="inlineCheckbox1" v-model='showSummary'> <?php echo Yii::t("reg", "Dispaly abstract");?>
                    </label>
                </div> -->
                <div class="checkbox">
                    <label>
                        <input type="checkbox" id="inlineCheckbox2" v-model='showPublisher'> <?php echo Yii::t("reg", "Display name and title");?>
                    </label>
                </div>
            </div>
            <p style='color:#F0AD4E' class='font14 flex'><span class='glyphicon glyphicon-info-sign mr5 pt2'></span><span class='flex1'><?php echo Yii::t("reg", "Please use screenshot tool to capture the picture.");?></span></p>
        </div>
        <div  class='Thumbnail'>
            <div :class='htmlContent==1?"checkImg":""' @click='htmlContent=1' class='mb20'>
                <img v-if='branchId=="BJ_IASLT" || branchId=="BJ_OE"' src='https://m2.files.ivykids.cn/cloud01-file-8025768FiiNxuOp94Ue0xbmdneI7IjZxjVk.png'  alt="">
                <img v-else src="http://m2.files.ivykids.cn/cloud01-file-8025768Fvx8x6eNPaQ0XZLdqNPICkriD4Vd.png"  >
            </div>
            <div :class='htmlContent==2?"checkImg":""' @click='htmlContent=2' class='mb20'>
                <img v-if='branchId=="BJ_IASLT" || branchId=="BJ_OE"' src='https://m2.files.ivykids.cn/cloud01-file-8025768FvEy8e6x31t8pAqFzCodJEj5XAea.png'  alt="">
                <img v-else src="http://m2.files.ivykids.cn/cloud01-file-8025768FkunNlr5VhkAvfwlt9cVFicZDavE.png"  >
            </div>
            <div :class='htmlContent==3?"checkImg":""' @click='htmlContent=3' class='mb20'>
                <img v-if='branchId=="BJ_IASLT" || branchId=="BJ_OE"' src='https://m2.files.ivykids.cn/cloud01-file-8025768Fkuw-Vc_H_O4q3HNpyDqcs15ydX8.png'  alt="">
                <img v-else src="http://m2.files.ivykids.cn/cloud01-file-8025768FnacKp-4ia2p4sPlbyDfG5ntgE6R.png"  >
            </div>
        </div>
        
        <div  class='shareContent' :style="branchId == 'BJ_IASLT' || branchId == 'BJ_OE'?'background: #42B36A':'background: #428BCA'" ref='shareContent1' v-if='htmlContent==1 && shareList.journalData'>
            <div class='bgPto relative' 
            :style="{backgroundImage: branchId == 'BJ_IASLT' || branchId == 'BJ_OE' ? 'url(https://m2.files.ivykids.cn/cloud01-file-8025768Fta3ITlMZF7Y3pZzb2jtYCmb__ED.png)' : 'url(http://m2.files.ivykids.cn/cloud01-file-8025768FsL-G-mcYYSw5mn_EmjZHNBW2XXo.png)'}" >
                <div class='sharePto text-center'>
                    <img class='logo' v-if='branchId=="BJ_IASLT" || branchId=="BJ_OE"' src='https://m2.files.ivykids.cn/cloud01-file-8025768FsPX1GEYk30H0SHFAloq0uJkXnnu.png'  alt="">
                    <img class='logo' v-else  src="https://m2.files.ivykids.cn/cloud01-file-8025768FuSnI0PSaaQ94OwfrBy7P4WW9hM3.png" alt="">
                    <div class=' titleHover'>特别沟通 Direct Message</div>
                    <div class='schoolTitle mt8'>{{shareList.school_title}}</div>
                    <div class='view' v-if='shareType=="view"'>
                        <div>内部预览，请勿分享给家长</div>
                        <div>Internal preview only, do not share to parents</div>
                    </div>
                </div>
                <div class='contentText p20 ' style='background:#fff'>
                    <div class='color3 font16 titleHover  fontWight' >{{shareList.journalData.title}}</div>
                    <div class="media mt10 mb20" v-if='shareList.journalData.sign_as_uid && showPublisher'>
                        <div class="media-left pull-left media-middle">
                            <a href="javascript:void(0)">
                                <img class="media-object img-circle shareAvatar" :src="shareList.userInfo[shareList.journalData.sign_as_uid].photoUrl" data-holder-rendered="true" >
                            </a>
                        </div>
                        <div class="media-body pt5 media-middle">
                            <h4  class="media-heading font12">{{shareList.userInfo[shareList.journalData.sign_as_uid].name}}</h4>
                            <div class='text-muted'>
                                {{shareList.userInfo[shareList.journalData.sign_as_uid].hrPosition}}
                            </div>
                        </div>
                    </div>
                    <div class='time mt5 color6'>{{shareList.journalData.format_publish_at}}</div>
                    <div class='wechat '>
                        <img  :src="shareType=='view'?previewQrcode:shareQrcode" class='wechatImg mt10'>
                        <!-- <div class='wechatTitle color6'>微信长按识别，查看详情</div>
                        <div class='wechatTitle color6'>Wechat long press qrcode for details</div> -->
                        <div class='wechatTitle color6 mt10'>微信长按识别 Wechat long press for details</div>
                    </div>
                </div>
                <div class='bottom'>
                    <img src="http://m2.files.ivykids.cn/cloud01-file-8025768Fq95CMHmofnMY8zDky250kSK0jjw.png" alt="">
                </div>
            </div>
        </div>
        <div  class='shareContent' :style="branchId == 'BJ_IASLT' || branchId == 'BJ_OE'?'border:1px solid #1F7949':'border:1px solid #0710B0'" ref='shareContent2' v-if='htmlContent==2'> 
            <div class='bgPto relative' :style="{backgroundImage: branchId == 'BJ_IASLT' || branchId == 'BJ_OE' ? 'url(https://m2.files.ivykids.cn/cloud01-file-8025768Fsen2rX5pvTyeTiAUBb-BVAaqxAB.png)' : 'url(http://m2.files.ivykids.cn/cloud01-file-8025768Fm92t6oYcOnmwk1MbwEGNhQlaQZV.png)'}"  >
                <div class='sharePto text-center'>
                    <img class='logo' v-if='branchId=="BJ_IASLT" || branchId=="BJ_OE"' src='https://m2.files.ivykids.cn/cloud01-file-8025768FsPX1GEYk30H0SHFAloq0uJkXnnu.png'  alt="">
                    <img class='logo' v-else  src="https://m2.files.ivykids.cn/cloud01-file-8025768FuSnI0PSaaQ94OwfrBy7P4WW9hM3.png" alt="">
                    <div class=' titleHover' :style="branchId == 'BJ_IASLT' || branchId == 'BJ_OE'?'color:#1F7949;margin-top:50px':'color:#061F9D;margin-top:40px'">特别沟通 Direct Message</div>
                    <div class='schoolTitle1' :style="branchId == 'BJ_IASLT' || branchId == 'BJ_OE'?'color:#1F7949':'color:#061F9D'">{{shareList.school_title}}</div>
                    <div class='view' v-if='shareType=="view"'>
                        <div>内部预览，请勿分享给家长</div>
                        <div>Internal preview only, do not share to parents</div>
                    </div>
                </div>
                <div class='contentText p20' :style="branchId == 'BJ_IASLT' || branchId == 'BJ_OE'?'background: #EEFAEF':'background: #F2F5FF'">
                    <div>
                        <div class='color3 font16   titleHover fontWight' >{{shareList.journalData.title}}</div>
                        <div class="media mt10 mb20" v-if='shareList.journalData.sign_as_uid && showPublisher'>
                            <div class="media-left pull-left media-middle">
                                <a href="javascript:void(0)">
                                    <img class="media-object img-circle shareAvatar" :src="shareList.userInfo[shareList.journalData.sign_as_uid].photoUrl" data-holder-rendered="true" >
                                </a>
                            </div>
                            <div class="media-body pt5 media-middle">
                                <h4  class="media-heading font12">{{shareList.userInfo[shareList.journalData.sign_as_uid].name}}</h4>
                                <div class='text-muted'>{{shareList.userInfo[shareList.journalData.sign_as_uid].hrPosition}}</div>
                            </div>
                        </div>
                    </div>
                    <div class='time mt5 color6'>{{shareList.journalData.format_publish_at}}</div>
                </div>
                <div class='wechat text-center p20'>
                    <img :src="shareType=='view'?previewQrcode:shareQrcode" class='wechatImg'>
                    <!-- <div class='wechatTitle color6 mt10'>微信长按识别，查看详情</div>
                    <div class='wechatTitle color6'>Wechat long press qrcode for details</div> -->
                    <div class='wechatTitle color6 mt10'>微信长按识别 Wechat long press for details</div>

                </div>
            </div>
        </div>
        <div  class='shareContent' ref='shareContent3' v-if='htmlContent==3' :style="{
            'background-size': 'cover',
            'background-image': branchId == 'BJ_IASLT' || branchId == 'BJ_OE' ? 'url(https://m2.files.ivykids.cn/cloud01-file-8025768Fiod_I3tJjnFxWXQ_7ZkA_ynZjtR.png)' : 'url(http://m2.files.ivykids.cn/cloud01-file-8025768FsubjqpWc9t2CQXvvEdMV4d9leHW.png)'
            }" > 
            <div class='bgPto relative' style='' >
                <div class='sharePto text-center'>
                    <img class='logo' v-if='branchId=="BJ_IASLT" || branchId=="BJ_OE"' src='https://m2.files.ivykids.cn/cloud01-file-8025768FsPX1GEYk30H0SHFAloq0uJkXnnu.png'  alt="">
                    <img class='logo' v-else  src="https://m2.files.ivykids.cn/cloud01-file-8025768FuSnI0PSaaQ94OwfrBy7P4WW9hM3.png" alt="">
                    <div class=' titleHover' >特别沟通 Direct Message</div>
                    <div class='schoolTitle'>{{shareList.school_title}}</div>
                    <div class='view' v-if='shareType=="view"'>
                        <div>内部预览，请勿分享给家长</div>
                        <div>Internal preview only, do not share to parents</div>
                    </div>
                </div>
                <div class='contentText white p8'>
                    <div >
                        <div class=' font16  titleHover fontWight' >{{shareList.journalData.title}}</div>
                        <div class="media mt10 mb20" v-if='shareList.journalData.sign_as_uid && showPublisher'>
                            <div class="media-left pull-left media-middle">
                                <a href="javascript:void(0)">
                                    <img class="media-object img-circle shareAvatar" :src="shareList.userInfo[shareList.journalData.sign_as_uid].photoUrl" data-holder-rendered="true" >
                                </a>
                            </div>
                            <div class="media-body pt5 media-middle">
                                <h4  class="media-heading font12">{{shareList.userInfo[shareList.journalData.sign_as_uid].name}}</h4>
                                <div class=''>{{shareList.userInfo[shareList.journalData.sign_as_uid].hrPosition}}</div>
                            </div>
                        </div>
                    </div>
                    <div class='time mt5 white08'>{{shareList.journalData.format_publish_at}}</div>
                </div>
                <div class='wechat p24 white flex'>
                    <div class='flex1 white08'>
                        <div class='wechatTitle'>微信长按识别</div>
                        <div class='wechatTitle '>Wechat long press for details</div>
                    </div>
                    <img :src="shareType=='view'?previewQrcode:shareQrcode" class='wechatImg'>                    
                </div>
            </div>
        </div>
    </div>
</div>