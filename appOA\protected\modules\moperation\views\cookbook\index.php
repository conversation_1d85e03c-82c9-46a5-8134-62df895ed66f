<style>
	
    [v-cloak] {
		display: none;
	}
	.pto{
		/*width:200px*/
	}
	.switch {
	  position: relative;
	  display: inline-block;
	  width: 40px;
	  height:20px;
	}

	.switch input {display:none;}

	.slider {
	  position: absolute;
	  cursor: pointer;
	  top: 0;
	  left: 0;
	  right: 0;
	  bottom: 0;
	  background-color: #ccc;
	  -webkit-transition: .4s;
	  transition: .4s;
	}

	.slider:before {
	  position: absolute;
	  content: "";
	  height:15px;
	  width:15px;
	  left:2.5px;
	  bottom:2.5px;
	  background-color: white;
	  -webkit-transition: .4s;
	  transition: .4s;
	}

	input:checked + .slider {
	  background-color: green;
	}

	input:focus + .slider {
	  box-shadow: 0 0 1px #2196F3;
	}

	input:checked + .slider:before {
	  -webkit-transform: translateX(20px);
	  -ms-transform: translateX(20px);
	  transform: translateX(20px);
	}

	.slider.round {
	  border-radius:10px;
	}

	.slider.round:before {
	  border-radius: 50%;
	}
</style>
<div class="container-fluid">
	 <ol class="breadcrumb">
	     <li><?php echo CHtml::link(Yii::t('site','HQ Operations'), array('default/index'));?></li>
	     <li><?php echo CHtml::link(Yii::t('site','Support'), array('default/index'));?></li>
	     <li class="active"><?php echo Yii::t('site','周餐谱审核') ;?></li>
	 </ol>
	 <div class="row" id='lunch' v-cloak>
		 <div class="col-md-12">
			<p class="form-inline">
				<input type="text" class="form-control form-group" id="datepicker" :value='date'  placeholder="请选择日期">
				<button type="button" class="btn btn-primary" @click='prev()'>上一周</button>
				<button type="button" class="btn btn-primary" @click='next()'>下一周</button>
			</p>
		</div>
		<div class="col-md-12">
			<div class="panel panel-default">
			    <div class="panel-body" v-if='monthData.length!=0'>
			    	<p><button type="button" class="btn btn-primary btn-sm"  @click="boxShow()">{{!boxshow ? '展开' :'收起' }}</button></p>
			    	<ul class="list-group mt15" ref='showImg'>
			    		<li class="list-group-item" v-for='(schoool,key,index) in branch'>
			    			<div>
				    			<div class="pull-left" >
				    				<label class="switch"  v-if='monthData[key]'>
									    <input type='checkbox' :id="'checked'+index" name='colorname' v-if='monthData[key].status==1' checked :value='index' @click="close(monthData[key].id,$event)">
									    <input type='checkbox' :id="'checked'+index" name='colorname' v-else :value='index' @click="close(monthData[key].id,$event)">
									    <div class="slider round"></div>
									</label>
									<label class="switch"  v-else>
									    <input type='checkbox' disabled>
									    <div class="slider round"></div>
									</label>
				    			</div>
					        	<span class="label label-primary ml15">{{schoool}}</span>
								<!-- <span class="ml15" @click="expandClazzToggle(monthData[key])">{{monthData[key].title}}  </span> -->
								<a href="javascript:;" @click="expandClazzToggle(monthData[key],$event)" class="ml15"  v-if='monthData[key]'>{{monthData[key].title}} </a>
								<span class="ml15"  v-if='monthData[key]'> 第{{monthData[key].weekNum}}周</span>
								<span v-else class="ml15 text-danger">暂无周餐谱</span>
							</div>
							<div  v-show="monthData[key]._expanded"  v-if='monthData[key]'>
								<div v-if='monthData[key].menu_cate_allergy.length==0 && monthData[key].menu_cate.length==0'>
									<h4 class="mt15">暂无周餐谱</h4>
								</div>
								<div v-else>
									<div  v-if='monthData[key].menu_cate.length!=0'>
										<h4 class="mt15">正常餐</h4>
										<table class="table">
											<thead>
												<tr>
													<th>类别/周</th>
													<th v-for='(week,idx) in monthData[key].commonWeekCate'>{{weekdays[week]}}</th>
												</tr>
											</thead>
											<tbody>
												<tr v-for='(lunch,idx) in monthData[key].menu_cate'>
													<td width="5%">{{lunchs[lunch]}}</td>
													<td width="10%" v-for='(week,idx) in monthData[key].commonWeekCate'>
														<template  v-if='monthData[key].commonData[lunch] && monthData[key].commonData[lunch][week]'>
															<img v-if='monthData[key].commonData[lunch][week].photo' src="" alt=""  class="img-thumbnail" :data-img='monthData[key].commonData[lunch][week].photo' class="imgs">
															<p v-html='monthData[key].commonData[lunch][week].title'></p>
														</template>	
													</td>
												</tr>
											</tbody>
										</table>
									</div>
									<div v-if='monthData[key].menu_cate_allergy.length!=0'>
										<h4 class="mt15">特殊餐</h4>
										<table class="table">
											<thead>
												<tr>
													<th>类别/周</th>
													<th v-for='(week,idx) in monthData[key].allergyWeekCate'>{{weekdays[week]}}</th>
												</tr>
											</thead>
											<tbody>
												<tr v-for='(lunch,idx) in monthData[key].menu_cate_allergy'>
													<td width="5%">{{lunchs[lunch]}}</td>
													<td width="10%" v-for='(week,idx) in monthData[key].allergyWeekCate'>
														<template v-if='monthData[key].allergyData[lunch] && monthData[key].allergyData[lunch][week]'>
															<img v-if='monthData[key].allergyData[lunch][week].photo' src="" alt=""  class="img-thumbnail" :data-img='monthData[key].allergyData[lunch][week].photo' class="imgs">
															<p v-html='monthData[key].allergyData[lunch][week].title'></p>
														</template>
													</td>
												</tr>
											</tbody>
										</table>
									</div>
								</div>
							</div>
			    		</li>
			    	</ul>
			    </div>
			    <div class="panel-body" v-else>
			    	暂无数据
			    </div>
			</div>
		</div>
	</div>
</div>
<script>
	$( function() {
		$( "#datepicker").datepicker({
		  dateFormat: "yy-mm-dd",
		});
		$( "#datepicker").on('change', function () {
            var dates = $('#datepicker').val();
            window.location.href = '<?php echo $this->createUrl('index'); ?>?weekTime=' + dates;
        });
	})
	var date = new Date();
    var year = date.getFullYear();
    var month = date.getMonth() + 1;
    var strDate = date.getDate();
    if (month >= 1 && month <= 9) {
        month = "0" + month;
    }
    if (strDate >= 0 && strDate <= 9) {
        strDate = "0" + strDate;
    }
    var currentdate = year+"-"+month+"-"+strDate;
	var lunchs = <?php echo json_encode($lunchs); ?>;
    var monthData = <?php echo json_encode($monthData) ?>;
    var weekTime = <?php echo json_encode($weekTime) ?>;
    var branch = <?php echo json_encode($branch) ?>;
    console.log(branch)
    console.log(weekTime)
    console.log(lunchs)
    console.log(monthData)
    if(weekTime==''){
    	weekTime=currentdate
    }
    var weekdays = ["mon", "tue", "wed", "thu", "fri","Sat","Sun"]
	var lunch = new Vue({
            el: "#lunch",
            data: {
                boxshow:false,
                monthData:monthData,
                lunchs:lunchs,
                date:weekTime,
                branch:branch,
                _expanded:'',
                showHide:'',
                weekdays:{
						"mon": "<?php echo Yii::t('attends', 'Mon')?>",
						"tue": "<?php echo Yii::t('attends', 'Tue')?>",
						"wed": "<?php echo Yii::t('attends', 'Wed')?>",
						"thu": "<?php echo Yii::t('attends', 'Thu')?>",
						"fri": "<?php echo Yii::t('attends', 'Fri')?>",
						"sat": "<?php echo Yii::t('attends', 'Sat')?>",
						"sun": "<?php echo Yii::t('attends', 'Sun')?>",
					}
			},
            created: function() {
            	for(key in monthData){
            		Vue.set(monthData[key], '_expanded', false)
            	}
            },
            methods: {
            	expandClazzToggle(clazz,e) {
            		var el = e.currentTarget;
            		var imgData=$(el).parent().parent().find('img')
            		for(var i=0;i<imgData.length;i++){
            			var imgShow=imgData[i]
            			imgShow.src=$(imgShow).attr('data-img')
            		}
			        clazz._expanded = !clazz._expanded; 
			        for(key in monthData){
		            	if(monthData[key]._expanded){
		            		lunch.boxshow=true
		            	}else{
		            		lunch.boxshow=false
		            	}
		            }
			    },
			    boxShow(){
				    for(var i=0;i<$('img').length;i++){
            			var imgShow=$('img')[i]
            			imgShow.src=$(imgShow).attr('data-img')
            		}
			    	if(this.boxshow){
			    		for(key in monthData){
		            		Vue.set(monthData[key], '_expanded', false)
		            	}
		            	this.boxshow = !this.boxshow
			    	}else{
			    		for(key in monthData){
		            		Vue.set(monthData[key], '_expanded', true)
		            	}
		            	this.boxshow = !this.boxshow
			    	}
			    	
			    },
            	fun_date(aa,weekTime){
				    var date1 = new Date(weekTime);
				    var date2 = new Date(weekTime);
				    date2.setDate(date1.getDate()+aa);
				    var month = date2.getMonth() + 1;
			    	var strDate = date2.getDate();
				    if (month >= 1 && month <= 9) {
				        month = "0" + month;
				    }
				    if (strDate >= 0 && strDate <= 9) {
				        strDate = "0" + strDate;
				    }
				    var time2 = date2.getFullYear()+"-"+month+"-"+strDate;
				    return time2
				},
                prev(){
                	var prevDate=this.$options.methods.fun_date(-7,this.date)
                	window.location.href = '<?php echo $this->createUrl('index'); ?>?weekTime=' + prevDate;
                },
                next(){
                	var nextDate=this.$options.methods.fun_date(7,this.date)
                	window.location.href = '<?php echo $this->createUrl('index'); ?>?weekTime=' + nextDate;
                },
                close(id,e){
                	if(e.target.checked){    
                		e.target.checked=false          	
	            		this.$options.methods.ajaxData(id,false,e)

	                }else{
	                	e.target.checked=true    
	                	this.$options.methods.ajaxData(id,true,e)
	                }
                },
                ajaxData(id,bol,e){
                	$.ajax({
	                    url: '<?php echo $this->createUrl("edit")?>',
	                    type: "post",
	                    async: true,
	                    dataType: 'json',
	                    data: {
	                        id:id,
	                    },
	                    success: function(res) {
	                        if (res.state == 'success') {
	                        	e.target.checked=!bol
	                           resultTip({"msg": res.message})
	                        } else {
	                        	e.target.checked=bol
	                            resultTip({
									error: 'warning',
									msg:res.message
								});
	                        }
	                    },
	                    error: function() {
	                        alert("请求错误")
	                    }
	                });
                }
            }
        })
</script>