<?php

class QuoteController extends ProtectedController
{
    public $actionAccessAuths = array(
        'index' => 'o_A_Adm_Quote',
        'lib' => 'o_A_Adm_Quote',
        'getquotelist' => 'o_A_Adm_Quote',
        'getquotedetail' => 'o_A_Adm_Quote',
        'searchquote' => 'o_A_Adm_Quote',
        'setweeklyquote' => 'o_A_Adm_Quote',
        'setlike' => 'o_A_Adm_Quote',
        'quotestanklist' => 'o_A_Adm_Quote',
        'savequotestank' => 'o_A_Adm_Quote',
        'delquotestank' => 'o_A_Adm_Quote',
        'likeuserlist' => 'o_A_Adm_Quote',
    );

    public function init()
    {
        parent::init();

        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'campusOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site', 'Campus Workspace');

        $cs = Yii::app()->clientScript;
        $cs->registerCoreScript('jquery.ui');
        $cs->registerCssFile($cs->getCoreScriptUrl().'/jui/css/base/jquery-ui.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/vue2.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/vue-tree/vue.min.js');
        $cs->registerCssFile(Yii::app()->themeManager->baseUrl.'/base/js/element/index.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/element/index.js');
    }

    public function actionIndex()
    {
        $this->render('index');
    }

    public function actionLib()
    {
        $this->render('lib');
    }

    public function actionGetQuoteList()
    {
        $url = 'quote/getQuoteList';
        $year = Yii::app()->request->getParam('year', date('Y'));
        $index = Yii::app()->request->getParam('index', 0);
        $data = array(
            'year'  => $year,
            'index' => $index,
        );
        $this->remote($url, 'get', $data);
    }

    public function actionGetQuoteDetail()
    {
        $url = 'quote/getQuoteDetail';
        $year = Yii::app()->request->getParam('year', date('Y'));
        $weekNumber = Yii::app()->request->getParam('weekNumber', 0);
        $page = Yii::app()->request->getParam('page', 1);
        $data = array(
            'year'       => $year,
            'weekNumber' => $weekNumber,
            'page' => $page,
        );
        $this->remote($url, 'get', $data);
    }

    public function actionSearchQuote()
    {
        $url = 'quote/searchQuote';
        $keyword = Yii::app()->request->getParam('keyword');
        $year = Yii::app()->request->getParam('year', date('Y'));
        $weekNumber = Yii::app()->request->getParam('weekNumber', 0);
        $page = Yii::app()->request->getParam('page', 1);
        $data = array(
            'keyword' => $keyword,
            'year'       => $year,
            'weekNumber' => $weekNumber,
            'page'    => $page
        );
        $this->remote($url, 'get', $data);
    }

    public function actionSetWeeklyQuote()
    {
        $url = 'quote/setWeeklyQuote';
        $year = Yii::app()->request->getParam('year');
        $weekNumber = Yii::app()->request->getParam('weekNumber');
        $type = Yii::app()->request->getParam('type');
        $quote_id = Yii::app()->request->getParam('id');
        $content = Yii::app()->request->getParam('content');
        $author = Yii::app()->request->getParam('author');
        $data = array(
            'type'       => $type,
            'quote_id'   => $quote_id,
            'content'    => $content,
            'author'     => $author,
            'year'       => $year,
            'weekNumber' => $weekNumber,
        );
        $this->remote($url, 'post', $data);
    }

    public function actionSetLike()
    {
        $url = 'quote/setLike';
        $log_id = Yii::app()->request->getParam('log_id');
        $data = array(
            'log_id' => $log_id,
        );
        $this->remote($url, 'post', $data);
    }

    public function actionQuotesTankList()
    {
        $requestUrl = "quote/quotesTankList";
        $pageSize = Yii::app()->request->getParam('pageSize', 20);//
        $page = Yii::app()->request->getParam('page', 1);
        $data = array(
            'page'     => $page,
            'pageSize' => $pageSize,
        );
        $this->remote($requestUrl, 'get', $data);
    }

    public function actionSaveQuotesTank()
    {
        $sayingData = Yii::app()->request->getParam('listData', 0);
        $requestUrl = "quote/saveQuotesTank";
        $this->remote($requestUrl, 'post', $sayingData);
    }

    public function actionDelQuotesTank()
    {
        $id = Yii::app()->request->getParam('id', 0);
        $requestUrl = "quote/delQuotesTank";
        $this->remote($requestUrl, 'delete', array(
            'id' => $id
        ));
    }

    public function actionLikeUserList()
    {
        $requestUrl = "quote/likeUserList";
        $quote_id = Yii::app()->request->getParam('id', 0);
        $data = array(
            'quote_id' => $quote_id,
        );
        $this->remote($requestUrl, 'get', $data);
    }


    //名人名言点赞
    public function actionSetSiLike()
    {
        $id = Yii::app()->request->getParam('id', 0);
        $requestUrl = "quote/setLike";
        $this->remote($requestUrl, 'post', array('log_id'=>$id));
    }

    public function remote($requestUrl, $method = 'post', $requestData = array())
    {
        $res = CommonUtils::requestDsOnline2($requestUrl, $requestData, $method);
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('message', $res['msg']);
            $this->addMessage('data', $res['data']);
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->addMessage('data', $res['data']);
        }
        $this->showMessage();
    }
}
