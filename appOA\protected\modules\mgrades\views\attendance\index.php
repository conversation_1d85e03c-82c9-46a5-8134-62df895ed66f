<!-- 加载底部导航栏 -->
<?php
$this->branchSelectParams['extraUrlArray'] = array('//mgrades/attendance/index');
$this->renderPartial('//layouts/common/branchSelectBottom');

function statistics($data = '',$teacherData)
{
    $dataArr = array();
    if(isset($teacherData) && isset($teacherData[$data])){
        foreach ($teacherData[$data] as $val) {
            $dataArr[] = $val;
        }
    }
    return $dataArr;
}
$typeData = array(
    TimetableRecords::ATTENDANCE_STATUS => "P",
    TimetableRecords::LATE_STATUS => "T",
    TimetableRecords::LEAVE_STATUS => "L",
    TimetableRecords::ABSENTEE_STATUS => "A",
);
?>
<div class="container-fluid">
    <!-- 面包屑导航 -->
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('//mcampus/default/index')) ?></li>
        <li class="active"><?php echo Yii::t('site', 'G6-12 Student Attendance'); ?></li>
    </ol>
    <div class="row">
        <div class="col-md-2">
            <?php
                $this->widget('zii.widgets.CMenu',array(
                    'items'=> $this->siderMenu,
                    'htmlOptions'=>array('class'=>'nav nav-pills nav-stacked text-right background-gray mb10'),
                    'activeCssClass'=>'active',
                    'itemCssClass'=>''
                ));
            ?>
        </div>
        <div class="col-md-10">
            <h3><?php echo Yii::t('attends','My Courses');?></h3>
            <h4>
                <?php $this->renderPartial('_select'); ?>
            </h4>
            <h4>
                <span class="glyphicon glyphicon-user"></span>
                <span><?php echo $userModel ? $userModel->getName() : $uid ?></span>
                <span title="<?php echo Yii::t('attends', 'View Schedules of Other Teachers')?>" onclick="chooseTeacher('<?php echo $weekTime; ?>','<?php echo $tid; ?>')" class="glyphicon glyphicon-chevron-down"></span>
            </h4>
            <h4>
                <span class="glyphicon glyphicon-time"></span>
                <span><?php echo $titleTime; ?></span>
                <?php echo CHtml::textField('weekTime', "", array('class'=>'datepickers form-control hide', 'onchange' => 'weekTime()'));?>
            </h4>
            <?php if($teacherData):?>
            <table class="table table-bordered">
                <thead>
                <tr>
                    <th class="text-center" width="50">#</th>
                    <?php
                    foreach ($strDate as $k => $val):
                        $month = date('Ym', $k);
                        $day = date('d', $k);
                        if (!in_array($day, $schoolDays[$month]['schoolday_array'])) {
                            
                        }
                        $active = strtotime($val) == strtotime('today') ? 'active' : '';
                        ?>
                        <th class='text-center <?php echo $active?>'>
                            <?php
                                echo date("D", strtotime($val)); 
                                $month = date('Ym', $k);
                                $day = date('d', $k);
                                if (!in_array($day, $schoolDays[$month]['schoolday_array'])) {
                                    echo Yii::t('attends', '（假期）');
                                }
                             ?>
                            <p class="help-block text-muted"><?php echo $val;?></p>
                        </th>
                    <?php endforeach;?>
                </tr>
                </thead>
                <tbody>
                <?php for ($i = 1; $i < 9; $i++) : ?>
                    <tr>
                        <td class="text-center" height="70"><?php echo $i; ?></td>
                        <?php
                        $num = 1;
                        foreach ($strDate as $k => $val){
                            $data = statistics($num . "-$i", $teacherData);
                            $warning = isset($data) && count($data) > 1 ? 'warning' : '' ;
                            $active = strtotime($val) == strtotime('today') ? 'active' : '';
                            echo "<td class='$active" . " " . "$warning'>";
                            if(isset($data)){
                                $month = date('Ym', $k);
                                $day = date('d', $k);
                                if (!in_array($day, $schoolDays[$month]['schoolday_array'])) {
                                    $num++;
                                    continue;
                                }
                                foreach ($data as $value){
                                    echo "<h5><a href='". $this->createUrl("courseStudent", array('uid'=> $uid, 'tid' => $tid, 'weekday' => $num . "-$i" ,'course_code' => $value['course_code'], 'datatime' => $val)) ."'>" . $value['course_code'] .' '. $value['course_title'] . "</a></h5>";
                                    if($numberArr[strtotime($val)][$num . "-$i"][$value['course_code']]){
                                        echo "<div>";
                                        foreach ($numberArr[strtotime($val)][$num . "-$i"][$value['course_code']] as $type=>$item) {
                                            if ($item>0) {
                                                $css = 'btn-success';
                                                if ($type == TimetableRecords::LATE_STATUS) {
                                                    $css = 'btn-info';
                                                }
                                                elseif ($type == TimetableRecords::LEAVE_STATUS) {
                                                    $css = 'btn-warning';
                                                }
                                                elseif ($type == TimetableRecords::ABSENTEE_STATUS) {
                                                    $css = 'btn-danger';
                                                }
                                                echo "<button type=\"button\" class='btn btn-xs $css'>" .   $typeData[$type] ." <span class=\"badge\">". $item . "</span></button> ";
                                            }
                                        }
                                        echo "</div>";
                                    }
                                }
                            }
                            echo "</td>";
                            $num++;
                        }
                        ?>
                    </tr>
                <?php endfor; ?>
                </tbody>
            </table>
            <?php else:?>
                <div class="alert alert-warning" role="alert"><?php echo Yii::t('attends', 'No course has been assigned to you.')?></div>
            <?php endif;?>
        </div>
    </div>
</div>
<div class="modal fade" id="remarks" tabindex="-1" role="dialog" aria-labelledby="modal" aria-hidden="false">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" ><?php echo Yii::t('attends','Select a Teacher') ?> <span id="remarks_t"></span></h4>
            </div>
            <div class="form-horizontal">
                <div class="modal-body">
                    <div name='teacherId' id="mySelect">
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    var teacherList;
    $('.datepickers').datepicker({
        changeMonth: true,
        changeYear: true,
        dateFormat:'yy-mm-dd',
        showOn: "button"
    });

    function weekTime() {
        var weekTime = $("#weekTime").val();
        var url ='<?php echo $this->createUrl('index');?>&weekTime=' + weekTime + '&teacherId=<?php echo $teacherId ?>';
        window.location.href = url;
    }

    function chooseTeacher(weekTime,tid) {
        var child = '';
        if(teacherList){
            $("#remarks").modal('show');
            $.each(teacherList,function(key,val){
                child += "<a class='mb5 btn btn-default' href='<?php echo $this->createUrl('index') ?>&teacherId="+ key +"'>" + val +  "</a> ";
            });
            $("#mySelect").html(child);
        }else{
            $.ajax({
                type: "POST",
                url: "<?php echo $this->createUrl('teacher')?>",
                data: {tid:tid},
                dataType: "json",
                success: function(data){
                    $("#remarks").modal('show');
                    var list = '';
                    if(data){

                        $.each(data,function(key,val){
                            child += "<a class='mb5 btn btn-default' href='<?php echo $this->createUrl('index') ?>&teacherId="+ key +"'>" + val +  "</a> ";
                        });
                        // child += "<p class='clearfix'></p>";
                        $("#mySelect").html(child);
                        teacherList = data;
                    }
                }
            });
        }
    }

</script>
