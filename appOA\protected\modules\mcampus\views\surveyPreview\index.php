<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo Yii::t('site', 'Campus Workspace'); ?></li>
        <li><?php echo Yii::t('site', 'Advanced'); ?></li>
        <li class="active"><?php echo CommonUtils::autoLang('调查问卷预览', 'Survey Preview'); ?></li>
    </ol>
    <div class="row">
        <div class="col-md-12">
            <h3><?php echo $survey->getTitle(); ?></h3>
            <div class="topic-wrapper">

                <?php
                $topicNum = 1;
                foreach ($topicData as $topic) :
                ?>
                    <div class="panel panel-default" id="topic-<?php echo $topic['id']; ?>">
                        <div class="panel-heading">
                            <span class="topic-number pull-left">
                                <span class="label label-primary"><?php echo $topicNum; ?></span></span>
                            <h4>
                                <?php echo nl2br($topic['title_cn']); ?>
                            </h4>
                            <h4>
                                <?php echo nl2br($topic['title_en']); ?>
                            </h4>
                        </div>
                        <div class="panel-body">
                            <div style="height: 2em;">
                                <?php if (in_array($topic['binary_flag'], array(0, 1))) {
                                    $lable = CommonUtils::autoLang('可选填', 'Optional');
                                    echo '<span class="label label-default">' . $lable . '</span>';
                                } ?>
                                <?php if ($topic['cat'] == 2) {
                                    $lable = CommonUtils::autoLang('可多选', 'multiple-choice');
                                    echo '<span class="label label-default">' . $lable . '</span>';
                                } ?>
                                <?php if (in_array($topic['binary_flag'], array(1, 3))) {
                                    $lable = CommonUtils::autoLang('显示备注框', 'Memo box enabled');
                                    echo '<span class="label label-default">' . $lable . '</span>';
                                } ?>
                            </div>
                            <?php if ($topic['option_group'] && isset($optionsData[$topic['option_group']])) : ?>
                                <div class="col-md-6 col-sm-12 table-responsive">
                                    <div class="row">
                                        <?php foreach ($optionsData[$topic['option_group']] as $_option) : ?>
                                            <div>
                                                <div class="col-md-4 col-sm-2 col-xs-4 text-right">
                                                    <?php echo $_option['title_cn']; ?>
                                                </div>
                                                <div class="col-md-8 col-sm-10 col-xs-8">
                                                    <?php echo $_option['title_en']; ?>
                                                </div>
                                                <div class="clearfix"></div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            <?php endif; ?>

                        </div>
                    </div>

                    <?php if ($topicNum % 5 == 0) : ?>
                        <div class="page-breaker"></div>
                    <?php endif; ?>
                <?php
                    $topicNum++;
                endforeach;
                ?>
            </div>
        </div>
    </div>
</div>

<script>
    $(function() {
        refreshReport = function(obj) {
            $(obj).attr('disabled', 'disabled');
            $.ajax({
                type: 'post',
                dataType: 'json',
                data: {
                    genReport: 1
                }
            }).done(function(data) {
                if (data.state == 'success') {
                    location.reload();
                } else {
                    $(obj).removeattr('disabled');
                }
            })
        }
    })
</script>

<style>
    .topic-number {
        font-size: 200%;
        margin-right: 0.5em;
        margin-top: 0px;
    }

    .progress-wrapper {
        width: 180px;
    }

    .progress {
        margin-bottom: 10px;
    }

    .progress-bar {
        color: #000000;
        white-space: nowrap;
    }

    .progress-text {
        display: none;
    }

    @media print {
        body {
            padding-top: 0 !important;
        }

        button {
            display: none !important;
        }

        .progress-wrapper {
            display: none;
        }

        .progress-text {
            display: inline;
        }

        h3,
        h4 {
            font-size: 90%
        }

        .topic-number {
            font-size: 100%
        }

        .panel-heading {
            padding: 0 15px;
        }

        .page-breaker {
            page-break-after: always
        }
    }
</style>