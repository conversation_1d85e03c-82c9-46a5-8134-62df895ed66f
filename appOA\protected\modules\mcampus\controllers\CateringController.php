<?php

class CateringController extends BranchBasedController{

    public $printFW = array();
    public $weeklinkMenu = array();
    public $weeklinkAllergy = array();
    public $lunchs = array();

    public $actionAccessAuths = array(
        'index'             => 'o_A_Access',
        'edit'              => 'o_A_Access',
        'edit2'             => 'o_A_Access',
        'save2'             => 'o_A_Access',
        'upload'            => 'o_A_Access',
        'delphoto'          => 'o_A_Access',
        'copy'              => 'o_A_Access',
        'changeab'          => 'o_A_Access',
        'setstatus'         => 'o_A_Access',
        'weeklyschedule'    => 'o_A_Access',
        'setmenu'           => 'o_A_Access',
    );

    public $leftMenu;

    public function createUrl($route, $params = array(), $ampersand = '&')
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        return parent::createUrl($route, $params, $ampersand);
    }

    public function init(){
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'campusOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site','Campus Workspace');
        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/plupload/plupload.full.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/vue2.js');

        //初始化选择校园页面
        $this->branchSelectParams['hideOffice'] = true;
        $this->branchSelectParams['urlArray'] = array('//mcampus/catering/weeklySchedule');

        Yii::import('common.models.lunchmenu.*');

        $this->leftMenu = array(
            array('label'=>Yii::t('lunch', 'Available'), 'url'=>array('/mcampus/catering/index', 'type'=>'active')),
            array('label'=>Yii::t('lunch', 'Ceased'), 'url'=>array('/mcampus/catering/index', 'type'=>'disabled')),
            array('label'=>Yii::t('lunch', 'Weekly Arrangement'), 'url'=>array('/mcampus/catering/weeklySchedule'))
        );
    }

    public function actionIndex($type='')
    {

        $criteria = new CDbCriteria();
        $criteria->compare('schoolid', $this->branchId);
        if($type == 'disabled'){
            $criteria->compare('status', 1);
        }
        else{
            $criteria->compare('status', 0);
        }
        $criteria->order = 'weight desc';
        $dataProvider = new CActiveDataProvider('CateringMenu', array(
            'criteria'=>$criteria,
        ));

        foreach ($dataProvider->getData() as $val){
            $menuId[] = $val->id;
        }

        $criteria = new CDbCriteria();
        $criteria->compare('menu_id', $menuId);
        $criteria->compare('status', 1);
        $weekLink = CateringMenuWeeklink::model()->findAll($criteria);
        if($weekLink){
            foreach ($weekLink as $val){
                $this->weeklinkMenu[$val->menu_id] = 1;
            }
        }

        $criteria = new CDbCriteria();
        $criteria->compare('allergy_id', $menuId);
        $criteria->compare('status', 1);
        $weekLink = CateringMenuWeeklink::model()->findAll($criteria);
        if($weekLink){
            foreach ($weekLink as $val){
                $this->weeklinkAllergy[$val->allergy_id] = 1;
            }
        }

//        $items = CateringMenu::model()->findAll($criteria);

        $this->render('index', array('type'=>$type, 'dataProvider'=>$dataProvider));
    }

    public function getButton($data, $row)
    {
        echo CHtml::link(Yii::t('global', 'Print'), array('print', 'id'=>$data->id), array('class'=>'btn btn-primary btn-xs', 'target'=>'_blank')).' ';
        $weeklinkMenu = ($this->weeklinkMenu) ? $this->weeklinkMenu : array();
        $weeklinkAllergy = ($this->weeklinkAllergy) ? $this->weeklinkAllergy : array();
        if(($weeklinkMenu && $weeklinkMenu[$data->id]) || ($weeklinkAllergy & $weeklinkAllergy[$data->id])){
            echo "<p title='不可修改' class='btn btn-warning btn-xs' onclick='edits()'>" . Yii::t('lunch', 'Edit') . "</p>";
        }else{
            echo CHtml::link(Yii::t('lunch', 'Edit'), array('edit', 'id'=>$data->id), array('class'=>'btn btn-primary btn-xs'));
        }
        echo ' <button class="btn btn-primary btn-xs" onclick="copy(this, '.$data->id.')">'.Yii::t('lunch', 'Copy').'</button> ';
        if($data->is_allergy == 1){
            $changeTitle = Yii::t('lunch', 'Change to Normal Lunch');
            $changeClass = 'btn-danger';
            $tipTitle = Yii::t('lunch', 'Are you sure you want to change the menu to normal lunch?');
        }
        else{
            $changeTitle = Yii::t('lunch', 'Change to Special Lunch');
            $changeClass = 'btn-primary';
            $tipTitle = Yii::t('lunch', 'Are you sure you want to change the menu to special lunch?');
        }
        if(($weeklinkMenu && $weeklinkMenu[$data->id]) || ($weeklinkAllergy & $weeklinkAllergy[$data->id])){
            echo "<p title='不可修改' class='btn btn-warning btn-xs' onclick='edits()' >" . Yii::t('lunch', 'Change to Special Lunch') . "</p>"  . ' ';
        }else{
            echo CHtml::link($changeTitle, array('changeAb', 'id'=>$data->id), array('data-msg'=>$tipTitle, 'class'=>'btn btn-primary btn-xs J_ajax_del '.$changeClass)) . ' ';
        }

        if($data->status == 1){
            $stateTitle = Yii::t('lunch', 'Activate');
            $tipTitle = Yii::t('lunch', 'Are you sure you want to activate the menu?');
        }
        else{
            $stateTitle = Yii::t('lunch', 'Cease');
            $tipTitle = Yii::t('lunch', 'Are you sure you want to cease the menu?');
        }
        echo CHtml::link($stateTitle, array('setStatus', 'id'=>$data->id), array('data-msg'=>$tipTitle, 'class'=>'btn btn-primary btn-xs J_ajax_del'));
    }

    public function actionEdit($id=0)
    {
        Yii::import('common.models.operations.IvyVendor');
        Yii::import('common.models.operations.IvyVendorCitylink');

        $citys = array();
        $lunchs = array();
        $vendorWhthCity = array();
        $model = CateringMenu::model()->findByPk($id);
        if($model == null){
            $model = new CateringMenu();
        }
        else{
            $model->menu_cate = unserialize($model->menu_cate);
        }

        if(isset($_POST['CateringMenu'])){
            $model->attributes = $_POST['CateringMenu'];
            $model->schoolid = $this->branchId;
            $model->yid = $this->branchObj->schcalendar;
            $model->menu_cate = serialize($_POST['CateringMenu']['menu_cate']);
            $model->title_cn = trim($_POST['CateringMenu']['title_cn']);
            $model->nutrition = trim($_POST['CateringMenu']['nutrition']);
            $model->memo = trim($_POST['CateringMenu']['memo']);
            $model->update_user = Yii::app()->user->id;
            $model->update_timestamp = time();
            if($model->save()){
                $this->addMessage('state', 'success');
                $this->addMessage('refresh', true);
                $this->addMessage('referer', $this->createUrl('edit2', array('id'=>$model->id)));
                $this->addMessage('message', Yii::t('message', 'Data Saved!'));
            }
            else{
                $this->addMessage('state', 'fail');
                $err = current($model->getErrors());
                $this->addMessage('message', $err[0]);
            }
            $this->showMessage();
        }

        $cityModels = Term::model()->city()->findAll();
        foreach($cityModels as $city){
            $citys[$city->diglossia_id] = $city->getContent();
        }

        $lunchModels = Term::model()->lunch()->findAll();
        foreach($lunchModels as $lunch){
            $lunchs[$lunch->diglossia_id] = $lunch->getContent();
        }

        $criteria = new CDbCriteria();
        $criteria->compare('vendor.vendor_type_id', 1);
        $criteria->compare('vendor.status', 0);
        $vendorModels = IvyVendorCitylink::model()->with('vendor')->findAll($criteria);
        foreach($vendorModels as $vendor){
            $vendorWhthCity[$vendor->diglossia_id][$vendor->vendor_id] = $vendor->vendor->getName();
        }

        $this->render('edit', array('model'=>$model, 'citys'=>$citys, 'lunchs'=>$lunchs, 'vendorWhthCity'=>$vendorWhthCity));
    }

    public function actionEdit2($id=0)
    {
        if($id){
            $model = CateringMenu::model()->findByPk($id);
            $cates = unserialize($model->menu_cate);

            $lunchs = array();
            $lunchModels = Term::model()->lunch()->findAll();
            foreach($lunchModels as $lunch){
                if(in_array($lunch->diglossia_id, $cates)){
                    $lunchs[$lunch->diglossia_id] = $lunch->getContent();
                }
            }

            $menus = array();
            $menuModels = CateringMenuDetail::model()->findAllByAttributes(array('menu_id'=>$id));
            foreach($menuModels as $menu){
                $menus[$menu->category][$menu->weekday] = array(
                    'content' => $menu->food_list,
                    'photo' => $menu->photo,
                );
            }

            $cs = Yii::app()->clientScript;
            $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/plupload/plupload.full.min.js');

            $this->render('edit2', array('model'=>$model, 'lunchs'=>$lunchs, 'menus'=>$menus));
        }
    }

    public function actionSave2()
    {
        if(Yii::app()->request->isPostRequest){
            $id = Yii::app()->request->getPost('id', 0);
            $data = Yii::app()->request->getPost('menu', array());

            if($id){
                $this->addMessage('state', 'success');
                $this->addMessage('refresh', true);
                $this->addMessage('referer', $this->createUrl('index'));
                $this->addMessage('message', Yii::t('lunch', 'No date need to be saved.'));
                foreach($data as $cid=>$weekdays){
                    foreach($weekdays as $weekday=>$content){
                        $model = CateringMenuDetail::model()->findByAttributes(array('menu_id'=>$id, 'weekday'=>$weekday, 'category'=>$cid));
                        if($model == null)
                            $model = new CateringMenuDetail();

                        $model->menu_id = $id;
                        $model->weekday = $weekday;
                        $model->category = $cid;
                        $model->food_list = trim($content);
                        $model->update_user = Yii::app()->user->id;
                        $model->update_timestamp = time();
                        if(!$model->save()){
                            $this->addMessage('state', 'fail');
                            $err = current($model->getErrors());
                            $this->addMessage('message', $err[0]);
                        }
                        else{
                            $this->addMessage('state', 'success');
                            $this->addMessage('message', Yii::t('message', 'Data Saved!'));
                            $this->addMessage('refresh', true);
                            $this->addMessage('referer', $this->createUrl('index'));
                        }
                    }
                }
            }
        }
        $this->showMessage();
    }

    public function actionUpload()
    {
        if(Yii::app()->request->isPostRequest){
            $id = Yii::app()->request->getPost('id', 0);
            $weekday = Yii::app()->request->getPost('weekday', '');
            $lkey = Yii::app()->request->getPost('lkey', '');
            if($id && $weekday && $lkey){
                $uploader = CUploadedFile::getInstanceByName('file');
                $filePath = Yii::app()->params['OAUploadBasePath'].'/lunch/';
                $fileName = 'lunch_' . uniqid() . '.' . $uploader->getExtensionName();
                if($uploader->saveAs($filePath.$fileName)){
                    Yii::import('application.extensions.image.Image');
                    $image = new Image($filePath.$fileName);
                    $imgData = getimagesize($filePath.$fileName);
                    if($imgData[0] > 800 && $imgData[1] > 600){
                        // if($imgData[0] > $imgData[1]){
                        //     $t = $imgData[1] / 0.75;
                        //     $image->crop($t, $imgData[1]);
                        // } if($imgData[0] < $imgData[1]){
                        //     $ts = $imgData[0] * 0.75;
                        //     $image->crop($imgData[0], $ts);
                        // }
                        // $image->save();

                        // $image = new Image($filePath.$fileName);
                        if ($imgData[0] > $imgData[1]) {
                            $image->resize(800, 600, Image::HEIGHT);
                        } else {
                            $image->resize(800, 600, Image::WIDTH);
                        }
                    }

                    $image->crop(800, 600);                   


                    if ($image->save()) {
                        $model = CateringMenuDetail::model()->findByAttributes(array('menu_id' => $id, 'weekday' => $weekday, 'category' => $lkey));
                        if ($model == null)
                            $model = new CateringMenuDetail();
                        $model->menu_id = $id;
                        $model->weekday = $weekday;
                        $model->category = $lkey;
                        $oldname = $model->photo;
                        $model->photo = $fileName;
                        $model->update_user = Yii::app()->user->id;
                        $model->update_timestamp = time();
                        if ($model->save()) {
                            // 判断是否有其他餐谱使用此照片
                            $photoNum = CateringMenuDetail::model()->count('photo=:photo', array(':photo' => $oldname));
                            if ($photoNum == 0) {
                                @unlink($filePath . $oldname);
                            }
                            $this->addMessage('state', 'success');
                            $this->addMessage('data', array(
                                'photo' => Yii::app()->params['OAUploadBaseUrl'] . '/lunch/' . $fileName,
                                'weekday' => $weekday,
                                'lkey' => $lkey,
                            ));
                            $this->addMessage('message', Yii::t('message', 'Data Saved!'));
                        } else {
                            $this->addMessage('state', 'fail');
                            $err = current($model->getErrors());
                            $this->addMessage('message', $err[0]);
                        }
                    }
                }
            }
            $this->showMessage();
        }
    }

    public function actionDelPhoto()
    {
        if(Yii::app()->request->isPostRequest){
            $id = Yii::app()->request->getPost('id', 0);
            $weekday = Yii::app()->request->getPost('weekday', '');
            $lkey = Yii::app()->request->getPost('lkey', '');

            $filePath = Yii::app()->params['OAUploadBasePath'].'/lunch/';
            $model = CateringMenuDetail::model()->findByAttributes(array('menu_id'=>$id, 'weekday'=>$weekday, 'category'=>$lkey));
            $oldname = $model->photo;
            if($oldname){
                $model->photo = '';
                if($model->save()){
                    // 判断是否有其他餐谱使用此照片
                    $photoNum = CateringMenuDetail::model()->count('photo=:photo', array(':photo'=>$oldname));
                    if ($photoNum == 0) {
                        @unlink($filePath.$oldname);
                    }
                    $this->addMessage('state', 'success');
                    $this->addMessage('callback', 'cbDel');
                    $this->addMessage('data', array('weekday' => $weekday, 'lkey' => $lkey));
                    $this->addMessage('message', Yii::t('global', 'Data deleted'));
                }
                else{
                    $this->addMessage('state', 'fail');
                    $err = current($model->getErrors());
                    $this->addMessage('message', $err[0]);
                }
            }
            else{
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('lunch', 'No photo need to be delayed.'));
            }
            $this->showMessage();
        }
    }

    public function actionCopy()
    {
        if(Yii::app()->request->isPostRequest){
            $oldid = Yii::app()->request->getPost('oldid', 0);
            $newName = Yii::app()->request->getPost('newName', '');

            if($oldid && $newName){
                $oldModel = CateringMenu::model()->findByPk($oldid);
                $newModel = new CateringMenu();
                $newModel->attributes = $oldModel->attributes;
                $newModel->title_cn = $newName;
                $newModel->update_user = Yii::app()->user->id;
                $newModel->update_timestamp = time();
                if($newModel->save()){
                    $items = CateringMenuDetail::model()->findAllByAttributes(array('menu_id'=>$oldid));
                    $transaction = Yii::app()->db->beginTransaction();
                    foreach($items as $item){
                        $subModel = new CateringMenuDetail();
                        $subModel->attributes = $item->attributes;
                        $subModel->menu_id = $newModel->id;
                        $subModel->update_user = Yii::app()->user->id;
                        $subModel->update_timestamp = time();
                        if(!$subModel->save()){
                            $transaction->rollback();
                            $this->addMessage('state', 'fail');
                            $err = current($subModel->getErrors());
                            $this->addMessage('message', $err[0]);
                            $this->showMessage();
                        }
                    }
                    $transaction->commit();
                    $this->addMessage('state', 'success');
                    $this->addMessage('refresh', true);
                    $this->addMessage('message', Yii::t('message', 'Data Saved!'));
                }
                else{
                    $this->addMessage('state', 'fail');
                    $err = current($newModel->getErrors());
                    $this->addMessage('message', $err[0]);
                }
            }
            else{
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('lunch', 'Please fill in the new title.'));
            }
            $this->showMessage();
        }
    }

    public function actionChangeAB()
    {
        if(Yii::app()->request->isPostRequest){
            $id = Yii::app()->request->getPost('id', 0);

            if($id){
                $model = CateringMenu::model()->findByPk($id);
                if($model->is_allergy == 1){
                    $model->is_allergy = 0;
                }
                else{
                    $model->is_allergy = 1;
                }
                $model->save();
                $this->addMessage('state', 'success');
                $this->addMessage('callback', 'cbReload');
                $this->addMessage('message', Yii::t('message', 'Data Saved!'));
                $this->showMessage();
            }
        }
    }

    public function actionSetStatus()
    {
        if(Yii::app()->request->isPostRequest){
            $id = Yii::app()->request->getPost('id', 0);

            if($id){
                $model = CateringMenu::model()->findByPk($id);
                if($model->status == 1){
                    $model->status = 0;
                }
                else{
                    $model->status = 1;
                }
                $model->save();
                $this->addMessage('state', 'success');
                $this->addMessage('callback', 'cbReload');
                $this->addMessage('message', Yii::t('message', 'Data Saved!'));
                $this->showMessage();
            }
        }
    }

    public function actionWeeklySchedule()
    {
        Yii::import('common.models.calendar.CalendarWeek');
        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/datatables/jquery.dataTables.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/datatables/dataTables.bootstrap.min.js');
        $cs->registerCssFile(Yii::app()->themeManager->baseUrl . '/base/js/datatables/dataTables.bootstrap.min.css');
        $this->branchSelectParams['urlArray'] = array('//mcampus/catering/weeklySchedule');
        $menuCommon = array();
        $menuAllergy = array();
        $yid = $this->branchObj->schcalendar;

        $criteria = new CDbCriteria();
        $criteria->compare('yid', $yid);
        $criteria->order = 'monday_timestamp asc';
        $weeks = CalendarWeek::model()->findAll($criteria);

        $criteria = new CDbCriteria();
        $criteria->compare('schoolid', $this->branchId);
        $criteria->compare('status', 0);
        $criteria->order = 'weight';
        $menus = CateringMenu::model()->findAll($criteria);
        foreach($menus as $menu){
            if($menu->is_allergy){
                $menuAllergy[$menu->id] = $menu->title_cn;
            }
            else{
                $menuCommon[$menu->id] = $menu->title_cn;
            }
        }

        $schedule = array();
        $criteria = new CDbCriteria();
        $criteria->compare('t.yid', $yid);
        $criteria->compare('t.schoolid', $this->branchId);
        $links = CateringMenuWeeklink::model()->with(array('common', 'allergy', 'review'))->findAll($criteria);
        foreach($links as $link){
            $schedule[$link->week_num]['review_by'] = $link->review ? $link->review->getName() : '';
            $schedule[$link->week_num]['review_at'] = $link->review_at ? date('Y-m-d H:i:s', $link->review_at) : '';
            $schedule[$link->week_num]['status'] = $link->status;
            $schedule[$link->week_num]['id'] = $link->id;
            $schedule[$link->week_num]['data'] = array(
                'common' => array(
                    'id'=>$link->menu_id,
                    'title'=>$link->common->title_cn,
                ),
                'allergy' => array(
                    'id'=>$link->allergy_id,
                    'title'=>$link->allergy->title_cn,
                ),
            );
        }

        $this->render('weeklyschedule', array('weeks'=>$weeks, 'menuCommon'=>$menuCommon, 'menuAllergy'=>$menuAllergy, 'schedule'=>$schedule, 'type' => $this->branchObj->type));
    }

    public function actionSetMenu()
    {
        if(Yii::app()->request->isPostRequest){
            $id = Yii::app()->request->getPost('id', 0);
            $weeknum = Yii::app()->request->getPost('weeknum', 0);
            $type = Yii::app()->request->getPost('type', '');

            if($weeknum && $type){
                Yii::import('common.models.calendar.CalendarWeek');

                $yid = $this->branchObj->schcalendar;
                $criteria = new CDbCriteria();
                $criteria->compare('yid', $yid);
                $criteria->compare('weeknumber', $weeknum);
                $week = CalendarWeek::model()->find($criteria);

                $criteria = new CDbCriteria();
                $criteria->compare('week_num', $weeknum);
                $criteria->compare('schoolid', $this->branchId);
                $criteria->compare('yid', $yid);
                $model = CateringMenuWeeklink::model()->find($criteria);
                if($model == null)
                    $model = new CateringMenuWeeklink();
                if($type == 'common'){
                    $model->menu_id = $id;
                }
                else{
                    $model->allergy_id = $id;
                }

                if($model->status == 1){
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', '总部已经确认，不可修改');
                    $this->showMessage();
                }
                $model->week_num = $weeknum;
                $model->monday_timestamp = $week->monday_timestamp;
                $model->schoolid = $this->branchId;
                $model->yid = $yid;
                $model->status = 0;
                $model->update_user = Yii::app()->user->id;
                $model->update_timestamp = time();
                if($model->save()){
                    $this->addMessage('state', 'success');
                    $this->addMessage('message', Yii::t('message', 'Data Saved!'));
                }
                else{
                    $this->addMessage('state', 'fail');
                    $err = current($model->getErrors());
                    $this->addMessage('message', isset($err[0]) ? $err[0] : Yii::t('message', 'Data Saving Failed!'));
                }
            }
            $this->showMessage();
        }
    }

    // 每周安排 新
    public function actionEditNew()
    {
        $lunchs = $this->mealCategory();

        $id = Yii::app()->request->getParam('id', '');
        $type = Yii::app()->request->getParam('type', ''); // menu 正常餐 allergy 特殊餐

        $weeklink = CateringMenuWeeklink::model()->findByPk($id);
        $mealData = array();
        if($weeklink){
            $menu_id = ($type == 'menu') ? $weeklink->menu_id :$weeklink->allergy_id;
            $menuModel = CateringMenu::model()->findByPk($menu_id);
            if($menuModel){
                $mealData['menu_id'] = $menuModel->id;
                $mealData['status'] = $weeklink->status;
                $mealData['week_cate'] = unserialize($menuModel->week_cate);
                $mealData['menu_cate'] = unserialize($menuModel->menu_cate);
                $mealData['memo'] = $menuModel->memo;
                $mealData['nutrition'] = $menuModel->nutrition;
                $mealData['detail'] = array();
                if($menuModel->dailyMenus) {
                    foreach ($menuModel->dailyMenus as $val) {
                        $mealData['detail'][$val->category][$val->weekday]['content'] = $val->food_list;
                        $mealData['detail'][$val->category][$val->weekday]['photo'] = ($val->photo) ? Yii::app()->params['OAUploadBaseUrl'].'/lunch/'.$val->photo : "";
                    }
                }
            }
        }

        $this->render('editNew', array(
            'lunchs' => $lunchs,
            'type' => $type,
            'mealData' => $mealData,
        ));
    }

    // 复制列表 以当前周为准，拿之前的20周
    public function actionCodoList()
    {
        $monday = Yii::app()->request->getParam('monday', ''); // 周一时间戳

        $criteria = new CDbCriteria();
        $criteria->compare('schoolid', $this->branchId);
        $criteria->compare('monday_timestamp', "<$monday");
        $criteria->addCondition("menu_id>0 OR allergy_id>0");
        $criteria->limit = 20;
        $criteria->order = 'monday_timestamp DESC';
        $menuModel = CateringMenuWeeklink::model()->findAll($criteria);

        $oldMeal = array();
        if($menuModel){
            foreach ($menuModel as $val) {
                $oldMeal[] = array(
                    'title' => $val->week_num,
                    'menu_id' => $val->menu_id,
                    'allergy_id' => $val->allergy_id,
                    'time' => date("Y-m-d", $val->monday_timestamp) . ' - ' .date("Y-m-d", $val->monday_timestamp + 86400 * 4),
                );
            }
        }

        echo json_encode($oldMeal);
    }

    // 根据meun_id拿数据
    public function actionMenuData()
    {
        $menu_id = Yii::app()->request->getParam('menu_id', '');

        $menuModel = CateringMenu::model()->findByPk($menu_id);
        $meal = array();
        if($menuModel){
            $meal['week_cate'] = unserialize($menuModel->week_cate);
            $meal['menu_cate'] = unserialize($menuModel->menu_cate);
            $meal['memo'] = $menuModel->memo;
            $meal['nutrition'] = $menuModel->nutrition;
            $meal['detail'] = array();
            if($menuModel->dailyMenus) {
                foreach ($menuModel->dailyMenus as $val) {
                    $meal['detail'][$val->category][$val->weekday]['content'] = $val->food_list;
                    $meal['detail'][$val->category][$val->weekday]['photo'] = ($val->photo) ? Yii::app()->params['OAUploadBaseUrl'].'/lunch/'.$val->photo : "";
                }
            }
        }

        echo json_encode($meal);
    }

    public function actionSaveNew()
    {
        $id = Yii::app()->request->getParam('id', '');
        $type = Yii::app()->request->getParam('type', ''); // menu_id 正常餐 allergy_id 特殊餐
        $memo = Yii::app()->request->getParam('memo', array()); // 备注
        $nutrition = Yii::app()->request->getParam('nutrition', array()); // 备注
        $menu_cate = Yii::app()->request->getParam('menu_cate', array());   // 类别
        $week_cate = Yii::app()->request->getParam('week_cate', array());   // 类别
        $data = Yii::app()->request->getParam('data', array()); // 餐谱数据
        $weeknum = Yii::app()->request->getParam('week', ''); // 周数
        $monday = Yii::app()->request->getParam('monday', ''); // 周一时间戳

        $yid = $this->branchObj->schcalendar;
        $weeklink = CateringMenuWeeklink::model()->findByPk($id);

        $menu_id = 0;
        if($weeklink){
            $menu_id = ($type == 'menu') ? $weeklink->menu_id :$weeklink->allergy_id;
            if($weeklink->status == 1){
                if(!$menu_id){
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', '已审核，如需增加清联系后勤');
                    $this->showMessage();
                }
            }
        }

        if(!$week_cate){
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '星期不能为空');
            $this->showMessage();
        }


        $connection = Yii::app()->db->beginTransaction();
        try {
            $model = CateringMenu::model()->findByPk($menu_id);
            if (!$model) {
                $model = new CateringMenu();
            }

            if($weeklink->status == 1){
                if($model->menu_cate && count(unserialize($model->menu_cate)) != count($menu_cate)){
                    $connection->rollBack();
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', '已经审核，不可修改类别');
                    $this->showMessage();
                }
                if($model->week_cate && count(unserialize($model->week_cate)) != count($week_cate)){
                    $connection->rollBack();
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', '已经审核，不可修改星期');
                    $this->showMessage();
                }
            }

            $model->schoolid = $this->branchId;
            $model->yid = $yid;
            $model->week_cate = serialize($week_cate);
            $model->menu_cate = serialize($menu_cate);
            $model->title_cn = ($model->title_cn) ? $model->title_cn : "每周食谱 Weekly Menu #" . $weeknum;
            $model->memo = trim($memo);
            $model->nutrition = trim($nutrition);
            $model->status = 0;
            $model->update_user = Yii::app()->user->id;
            $model->update_timestamp = time();
            if ($model->save()) {
                if($data) {
                    $exitData = array();
                    foreach ($data as $cid => $weekdays) {
                        foreach ($weekdays as $weekday => $content) {
                            $criteria = new CDbCriteria();
                            $criteria->compare('menu_id', $menu_id);
                            $criteria->compare('weekday', $weekday);
                            $criteria->compare('category', $cid);
                            $detailModel = CateringMenuDetail::model()->find($criteria);

                            if ($detailModel == null){
                                $detailModel = new CateringMenuDetail();
                            }


                            if($weeklink && $detailModel){
                                if(trim($detailModel->food_list) != trim($content) && $weeklink->status == 1){
                                    $exitData[$cid][$weekday] = array(
                                        'old' => nl2br(trim($detailModel->food_list)),
                                        'new' => nl2br(trim($content)),
                                    );
                                }
                            }
                            $detailModel->menu_id = $model->id;
                            $detailModel->weekday = $weekday;
                            $detailModel->category = $cid;
                            $detailModel->food_list = trim($content);
                            $detailModel->update_user = Yii::app()->user->id;
                            $detailModel->update_timestamp = time();

                            if (!$detailModel->save()) {
                                $connection->rollBack();
                                $this->addMessage('state', 'fail');
                                $err = current($detailModel->getErrors());
                                $this->addMessage('message', isset($err[0]) ? $err[0] : Yii::t('message', 'Data Saving Failed!'));
                                $this->showMessage();
                            }
                        }
                    }
                }

                if(!$weeklink){
                    $weeklink = new CateringMenuWeeklink();
                    $weeklink->week_num = $weeknum;
                    $weeklink->monday_timestamp = $monday;
                    $weeklink->schoolid = $this->branchId;
                    $weeklink->yid = $yid;
                    $weeklink->status = 0;
                    $weeklink->menu_id = 0;
                    $weeklink->allergy_id = 0;
                }

                if($type == 'menu' && $weeklink->menu_id == 0){
                    $weeklink->menu_id = $model->id;
                    $weeklink->update_user = Yii::app()->user->id;
                    $weeklink->update_timestamp = time();
                    if(!$weeklink->save()){
                        $connection->rollBack();
                        $this->addMessage('state', 'fail');
                        $err = current($weeklink->getErrors());
                        $this->addMessage('message', isset($err[0]) ? $err[0] : Yii::t('message', 'Data Saving Failed!'));
                        $this->showMessage();
                    }
                }

                if($type == 'allergy' && $weeklink->allergy_id == 0){
                    $weeklink->allergy_id = $model->id;
                    $weeklink->update_user = Yii::app()->user->id;
                    $weeklink->update_timestamp = time();
                    if(!$weeklink->save()){
                        $connection->rollBack();
                        $this->addMessage('state', 'fail');
                        $err = current($weeklink->getErrors());
                        $this->addMessage('message', isset($err[0]) ? $err[0] : Yii::t('message', 'Data Saving Failed!'));
                        $this->showMessage();
                    }
                }

                if($exitData){
                    $weekData = CateringMenu::getWeekDayName();
                    $sub = '[' . $this->branchObj->title . '] ';
                    $date = date('Y-m-d H:i', time());
                    $subject = $date. ' 修改食谱';
                    $main = 'main';
//                    $mailer = Yii::createComponent('common.extensions.mailer.EMailer');
                    $mailer = Yii::createComponent('common.extensions.mailer.Aliyun');
                    $mailer->Subject = $sub . $subject ;
                    $mailer->AddAddress('<EMAIL>');
                    $mailer->AddCC($this->staff->email);
                    $mailer->getView('meal', array('weekData' => $weekData, 'weeklinkModel' => $weeklink, 'lunch' => $this->mealCategory(), 'branchId' => $this->branchObj->title, 'exitData'=>$exitData), $main);
                    $mailer->iniMail( OA::isProduction());
                    if (!$mailer->Send()) {
                        $connection->rollBack();
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message',$mailer->ErrorInfo);
                        $this->showMessage();
                    }
                }

                $connection->commit();
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message', 'success'));
                $this->showMessage();
            }else{
                $connection->rollBack();
                $this->addMessage('state', 'fail');
                $err = current($model->getErrors());
                $this->addMessage('message', isset($err[0]) ? $err[0] : Yii::t('message', 'Data Saving Failed!'));
                $this->showMessage();
            }
        } catch (\Exception $e) {
            $connection->rollBack();
            $this->addMessage('state', 'fail');
            $this->addMessage('message', Yii::t('message', '异常,请稍后再试!'));
            $this->showMessage();
        }

    }

    public function actionDetWeeklink()
    {
        if(Yii::app()->request->isPostRequest){
            $id = Yii::app()->request->getParam('id', '');
            $type = Yii::app()->request->getParam('type', array()); // menu_id 正常餐 allergy_id 特殊餐
            if(count($type) == 0 || $type['menu'] != 1 && $type['allergy'] != 1){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('message', '请选择要删除的类型！'));
                $this->showMessage();
            }
            $weeklink = CateringMenuWeeklink::model()->findByPk($id);

            if($weeklink && $weeklink->status == 0){
                $menuId = array();
                if(isset($type['menu'])){
                    if($weeklink->menu_id > 0) {
                        $menuId['menu'] = $weeklink->menu_id;
                    }
                    $weeklink->menu_id = 0;
                }
                if(isset($type['allergy'])){
                    if($weeklink->allergy > 0) {
                        $menuId['allergy'] = $weeklink->allergy_id;
                    }
                    $weeklink->allergy_id = 0;
                }

                if($weeklink->save()){
                    if($menuId) {
                        $criteria = new CDbCriteria();
                        $criteria->compare('id', $menuId);
                        $menuModel = CateringMenu::model()->findAll($criteria);
                        if ($menuModel) {
                            foreach ($menuModel as $val){
                                $val->delete();
                            }
                        }
                        $criteria = new CDbCriteria();
                        $criteria->compare('menu_id', $menuId);
                        $menuDetaiModel = CateringMenuDetail::model()->findAll($criteria);

                        if ($menuDetaiModel) {
                            foreach ($menuDetaiModel as $item) {
                                $item->delete();
                            }
                        }
                    }

                    $this->addMessage('state', 'success');
                    $this->addMessage('message', Yii::t('message', 'success'));
                    $this->addMessage('callback', 'cbSuccess');
                    $this->showMessage();
                }
            }else{
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('message', '已审核，请联系后勤部！'));
                $this->showMessage();
            }
        }

    }

    public function mealCategory()
    {
        $lunchModels = Term::model()->lunch()->findAll();
        if($lunchModels) {
            foreach ($lunchModels as $lunch) {
                $this->lunchs[$lunch->diglossia_id] = $lunch->getContent();
            }
        }
        return $this->lunchs;
    }

    function actionPrint($id=0, $week=0, $monday=0)
    {
        $this->layout = '//layouts/print';

        $this->printFW = $this->branchObj->getPrintHeader();

        $model = CateringMenu::model()->with('dailyMenus')->findByPk($id);
        $model->menu_cate = unserialize($model->menu_cate);

        $lunchs = array();
        $lunchModels = Term::model()->lunch()->findAll();
        foreach($lunchModels as $lunch){
            $lunchs[$lunch->diglossia_id] = array($lunch->cntitle, $lunch->entitle);
        }

        $details = array();
        foreach($model->dailyMenus as $detail){
            $details[$detail->category][$detail->weekday] = $detail->food_list;
        }
        $weekday = array(
            'mon'=>'mon',
            'tue'=>'tue',
            'wed'=>'wed',
            'thu'=>'thu',
            'fri'=>'fri',
        );
        $weekday = $model->week_cate  ? unserialize($model->week_cate) : $weekday;
        $asd = CateringMenu::getWeekDayName();
        $weekdayData = array_intersect_key($asd, array_flip($weekday));
        //Yii::msg($asd);
        $this->render('print', array('model'=>$model, 'weekdayData' => $weekdayData, 'details'=>$details, 'lunchs'=>$lunchs, 'week'=>$week, 'monday'=>$monday));
    }
}
