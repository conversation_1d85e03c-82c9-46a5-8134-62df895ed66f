<?php

/* 
 * 孩子退学更改孩子状态
 */
class SetChildStatusCommand extends CConsoleCommand{
    public function actionIndex(){
        Yii::import('application.components.policy.PolicyApi');
        Yii::import('common.models.child.ChildLeaveSchool');
        $criter = new CDbCriteria;
        $criter->compare('status', array(0,20,50));
        $criter->addCondition('leave_school_time<='.  time());
        $leaveSchoolList =ChildLeaveSchool::model()->findAll($criter);
        $errorTxt = '';
        if (!empty($leaveSchoolList)){
            $policyApi = new PolicyApi();
            foreach ($leaveSchoolList as $model){
                if($model->status == 50){
                    $childStatus = ChildProfileBasic::STATS_GRADUATED;
                }else{
                    $childStatus = ChildProfileBasic::STATS_DROPOUT;
                }
                if ($policyApi->setChildStatus($model->childid,$childStatus,1)){
                    $model->status = 1;
                    if ($model->save()){
                        echo $model->childid."\r\n";
                    }
                }else{
                    echo $model->childid."\r\n";
                    $errorTxt .= (empty($errorTxt)) ? $model->childid : ','.$model->childid;
                }
            }
        }else{
            echo "\r\n end.";
        }
        if (!empty($errorTxt)){
            echo "执行失败的ID：".$errorTxt;
        }
    }
}

