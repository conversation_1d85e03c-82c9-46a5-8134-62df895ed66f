<?php

class DefaultController extends Controller
{
	public function init(){
//        $cookies['child_mims_lang']=new CHttpCookie('child_mims_lang','zh_cn');
        Mims::setLangCookie('zh_cn');
        parent::init();
        Yii::import('common.models.club.*');
		$this->defaultAction = 'home';
		Yii::app()->params['enableSideBar'] = false;
		
		Yii::app()->theme = 'boots';
		$this->layout = '//layouts/main';
		$this->siteTitle = Yii::t("global","IvyOnline");
		
		$this->menu = array(
//			array('label'=>Yii::t('club', '宝贝去哪'),'url'=>array('//club/default/list','category'=>'travel')),
			//array('label'=>Yii::t('club', '厨神在哪里'),'url'=>array('//club/default/list','category'=>'chef')),
//			array('label'=>Yii::t('club', '我要投稿'),'url'=>array('//club/default/contribute','category'=>'chef')),
		);
		Yii::app()->clientScript->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/jquery.lazyload.min.js');

        if (!Yii::app()->user->isGuest){
            if (!Yii::app()->user->getState('clubUsername')){
                $clubUser = ClubUsers::model()->findByPk(Yii::app()->user->id);
                Yii::app()->user->setState('clubUsername', $clubUser !== null ? $clubUser->uname : '');
            }
        }

        $this->siteTitle = Yii::t("global","Club@IvySchools");
	}
	
	public function actionHome(){
		Yii::app()->params['enableSideBar'] = false;
        $articles = ClubArticle::model()->with(array('branch','coverImg'))->findAll();
		$this->render('home', array('articles'=>$articles));
	}
	
	public function actionView($id=0, $page=1){
		Yii::app()->params['enableSideBar'] = true;

        $model = ClubArticle::model()->findByPk($id);

        $url = Yii::app()->params['appsUrl'].'/clubApi/getArticle';
        $t = time();
        if ($model->language == ClubArticle::CLUB_LANGUAGE_IBS){
            $cacheKey = sprintf('article-%d-%s-%s', $id, Yii::app()->language, $page);
        }
        elseif($model->language == ClubArticle::CLUB_LANGUAGE_CHA){
            $cacheKey = sprintf('article-%d-%s-%s', $id, 'zh_cn', $page);
        }
        elseif($model->language == ClubArticle::CLUB_LANGUAGE_ENG){
            $cacheKey = sprintf('article-%d-%s-%s', $id, 'en_us', $page);
        }
        $params = array(
            'postTime'=>$t,
            'flagid'=>$id,
            'page'=>$page,
            'postKey'=>md5(sprintf("%s&%s&%s", $t, $id, CommonUtils::SECURITY_KEY)),
            'cacheKey'=>$cacheKey
        );
        $query = http_build_query($params, '', '&');
        $options = array(
            'http'=>array(
                'method'=>'POST',
                'header' => "Content-Type: application/x-www-form-urlencoded\r\n".
                    "Content-Length: ".strlen($query)."\r\n".
                    "User-Agent:MyAgent/1.0\r\n",
                'content'=>$query,
            ),
        );
        $context = stream_context_create($options);
        $content = file_get_contents($url, false, $context);
        $content = str_ireplace(' src=', ' data-original=', $content); //图片 lazy load
        $this->subPageTitle = $model->getTitle();

        $articles = ClubArticle::model()->findAll();

		$this->render('view', array('content'=>$content, 'model'=>$model, 'page'=>$page, 'articles'=>$articles));
	}
	
	public function actionList(){
		$this->render('list');
	}
	
	public function actionIndex()
	{
		$this->render('index');
	}

    public function actionProcessComment($article_id=0)
    {
        if (Yii::app()->request->isPostRequest && isset($_POST['ClubComment'])){

            $modelP = null;
            if(isset($_POST['ClubComment']['pid'])){
                $pid = $_POST['ClubComment']['pid'];
                $modelP = ClubComment::model()->findByPk($pid);
            }
            $model = new ClubComment();
            $model->userid = Yii::app()->user->id;
            $model->article_id = $article_id;
            $model->parent_id = $modelP != null ? $modelP->id : 0;
            $model->mention_uid = $modelP != null ? $modelP->userid : 0;
            $model->content = $_POST['ClubComment']['content'];
            $model->created = time();
            $model->status = 1;
            $model->privacy = 0;
            if($model->save()){
                echo CJSON::encode(array('state'=>'success'));
            }
            else{
                $err = current($model->getErrors());
                echo CJSON::encode(array('state'=>'fail', 'msg'=>$err?$err[0]:'保存失败！'));
            }
        }
    }

    public function actionGetComments($article_id=0, $page=1)
    {
        $limit = 10;
        $sql = "select count(*) as count from ".ClubComment::model()->tableName()." where status = 1 and article_id = ".$article_id." order by created desc";
        $count = Yii::app()->db->createCommand($sql)->queryRow();
        $sql = "select * from ".ClubComment::model()->tableName()." where status = 1 and article_id = ".$article_id." order by created desc limit ".($page-1)*$limit.", ".$limit;
        $comments = Yii::app()->db->createCommand($sql)->queryAll();

        $userids = array();
        foreach($comments as $comment){
            $userids[$comment['userid']] = $comment['userid'];
            $userids[$comment['mention_uid']] = $comment['mention_uid'];
        }
        $criteria = new CDbCriteria();
        $criteria->compare('t.uid', $userids);
        $criteria->index = 'uid';
        $users = ClubUsers::model()->with('user')->findAll($criteria);

        $this->renderPartial('comments', array('comments'=>$comments, 'users'=>$users, 'count'=>$count['count'], 'limit'=>$limit, 'page'=>$page));
    }
}