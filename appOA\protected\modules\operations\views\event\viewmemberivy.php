<?php
$this->widget('zii.widgets.CMenu',array(
    'items'=> $pageSubMenu,
    'activeCssClass'=>'current',
    'id'=>'page-subMenu',
    'htmlOptions'=>array('class'=>'pageSubMenu')
));
echo CHtml::openTag("div", array("class"=>"c"));
echo CHtml::closeTag("div");
?>

<div class="h_a">活动名称</div>
<div class="mb10 prompt_text">
    <ul>
        <li><?php echo CommonUtils::autoLang($model->cn_title, $model->en_title)?></li>
    </ul>
</div>
<div class="mb10">
    <a href="<?php echo $this->createUrl("/operations/event/updatememberivy", array('event_id'=>$model->id));?>" class="btn J_dialog" title="添加"><span class="add"></span>添加</a>
    <a href="<?php echo $this->createUrl("/operations/event/exportivy", array('id'=>$model->id));?>" class="btn"><span class="add"></span>导出Excel</a>
</div>

<?php
$this->widget('ext.ivyCGridView.IvyCGridView', array(
	'id'=>'event-memberivy-grid',
	'dataProvider'=>$dataProvider,
    'colgroups'=>array(
        array(
            "colwidth"=>array(80,80,80,80,80,80,80,80,80,80,80,80),
        )
    ),
	'columns'=>array(
		'parent_name',
        'telphone',
        'email',
        array(
            'name'=>'schoolid',
            'value'=>'Yii::app()->controller->branchArr[$data->schoolid]',
        ),
        'ivyclass',
		'child_name',
		'adult_number',
		'kid_number',
        array(
            'name'=>'timestamp',
            'value'=>'OA::formatDateTime($data->timestamp, "medium", "short")',
        ),
        array(
            'class'=>'CButtonColumn',
            'template' => '{update} {delete}',
            'updateButtonImageUrl'=>false,
            'deleteButtonImageUrl'=>false,
            'updateButtonUrl'=>'Yii::app()->createUrl("/operations/event/updateMemberivy", array("id" => $data->id, "event_id"=>'.$model->id.'))',
            'deleteButtonUrl'=>'Yii::app()->createUrl("/operations/event/deleteMemberivy", array("id" => $data->id))',
            'updateButtonOptions'=>array('class'=>'J_dialog'),
        ),
	),
)); ?>