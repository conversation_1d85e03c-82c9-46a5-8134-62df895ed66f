<?php
$calendar = null;
Yii::import('common.models.calendar.*');
Yii::import('common.models.invoice.*');
if (!DiscountSchool::checkView($this->branchId, Yii::app()->user->roles, Yii::app()->user->id)) {
    $this->redirect(array('//denied/index'));
    Yii::app()->end();
}
$this->branchObj = Branch::model()->findByPk($this->branchId);
$this->getCalendars();
$childData = array();
$classList = array();
if ($category === 'currentdst'){
    $calendar = Calendar::model()->with(
        array('currentClasses'=>array(
            'params'=>array(':schoolid'=>$this->branchId),
            'with'=>array('children'=>array(
                'with'=>array(
                    'constant'=>array('together'=>false),
                    'bindDiscount'=>array('together'=>false),
                ),
                'select'=> 'childid, nick, country, status, classid, name_cn, first_name_en, last_name_en,schoolid',
                'together'=>false,
                'order'=>'children.status ASC, children.name_cn ASC, children.first_name_en ASC',
            ),
            ),
            'select'=>'currentClasses.title,currentClasses.classid',
            'order'=>'currentClasses.child_age ASC'
        ))
    )->findByPk($this->calendarYids['currentYid']);
    foreach ($calendar->currentClasses as $classes){
        $count = 0;
        foreach ($classes->children as $childs){
            if ($childs->status == 20) {
                $childData[$childs->childid] = array(
                    'childid'=>$childs->childid,
                    'classid'=>$childs->classid,
                    'classtitle'=>$classes->title,
                    'schoolid'=>$childs->schoolid,
                    'name'=>$childs->getChildName(),
                    'amount'=>($childs->constant) ? ($childs->constant->startyear) ? $childs->constant->startyear : $childs->constant->amount  : 0,
                    'discount'=>($childs->bindDiscount) ? $childs->bindDiscount->DiscountSchool->discount : '-1',
                    'bindDiscountId'=>($childs->bindDiscount) ? $childs->bindDiscount->id : '',
                    'discountStat'=>($childs->bindDiscount) ? $childs->bindDiscount->DiscountSchool->stat : '',
                    'discountChildStat'=>($childs->bindDiscount) ? $childs->bindDiscount->status : 0,
                    'expire_date'=>($childs->bindDiscount) ? $childs->bindDiscount->DiscountSchool->expire_date : '',
                    'discountTitle'=>($childs->bindDiscount) ? CommonUtils::autoLang($childs->bindDiscount->DiscountSchool->discountTitle->title_cn, $childs->bindDiscount->DiscountSchool->discountTitle->title_en) : '',
                );
                $count++;
            }
        }
        $classList[$classes->classid]['classid'] = $classes->classid;
        $classList[$classes->classid]['title'] = $classes->title;
        $classList[$classes->classid]['child_age'] = $classes->child_age;
        $classList[$classes->classid]['count'] = $count;
    }
    sort($childData);
}else{
    $calendar = Calendar::model()->with(
            array('nextClasses' => array(
                    'params' => array(':schoolid' => $this->branchId),
                    'with' => array(
                        'reserveChildren' => array(
                            'with' => array(
                                'childProfile' => array(
                                    'with' => array(
                                        'constant' => array('together' => false),
                                        'bindDiscount' => array('together' => false),
                                    ),
                                    'order' => 'reserveChildren.stat ASC, childProfile.name_cn ASC, childProfile.first_name_en ASC',
                                )
                            )
                        ),
                    ),
                )
            )
        )->findByPk($this->calendarYids['nextYid']);
    foreach ($calendar->nextClasses as $classes){
        foreach ($classes->reserveChildren as $childs){
            $childData[$childs->childid] = array(
                'childid'=>$childs->childid,
                'classid'=>$childs->classid,
                'classtitle'=>$classes->title,
                'schoolid'=>$childs->schoolid,
                'name'=>$childs->childProfile->getChildName(),
                'amount'=>($childs->childProfile->constant) ? ($childs->childProfile->constant->startyear) ? $childs->childProfile->constant->startyear : $childs->childProfile->constant->amount  : 0,
                'discount'=>($childs->childProfile->bindDiscount) ? $childs->childProfile->bindDiscount->DiscountSchool->discount : '-1',
                'bindDiscountId'=>($childs->childProfile->bindDiscount) ? $childs->childProfile->bindDiscount->id : '',
                'discountStat'=>($childs->childProfile->bindDiscount) ? $childs->childProfile->bindDiscount->DiscountSchool->stat : '',
                'discountChildStat'=>($childs->childProfile->bindDiscount) ? $childs->childProfile->bindDiscount->status : 0,
                'expire_date'=>($childs->childProfile->bindDiscount) ? $childs->childProfile->bindDiscount->DiscountSchool->expire_date : '',
                'discountTitle'=>($childs->childProfile->bindDiscount) ? CommonUtils::autoLang($childs->childProfile->bindDiscount->DiscountSchool->discountTitle->title_cn, $childs->childProfile->bindDiscount->DiscountSchool->discountTitle->title_en) : '',
            );
        }
        $classList[$classes->classid]['classid'] = $classes->classid;
        $classList[$classes->classid]['title'] = $classes->title;
        $classList[$classes->classid]['child_age'] = $classes->child_age;
        $classList[$classes->classid]['count'] = count($classes->reserveChildren);
    }
    sort($childData);
}
$enabledConstantTuition = 0;
$policy = OA::getPolicy(OA::POLICY_PAY, $calendar->startyear, $this->branchId);
if(!empty($policy) && !empty($policy->configs) && isset($policy->configs[ENABLE_CONSTANT_TUITION]) && $policy->configs[ENABLE_CONSTANT_TUITION]){
    $enabledConstantTuition = 1;
}
//老生老办法需要使用开始年（startYear）学校
$useStartYearOfSchool = 0;
if (in_array($this->branchId, $this->constantSchoolList)){
    $useStartYearOfSchool = 1;
}

?>
<div class="col-md-10">
    <div class="row mb15">
        <div class="col-md-12">
            <h1 class="flex align-items font24 fontBold mt20">
                <span class="dropdown text-primary">
                    <a data-toggle="dropdown" href="#"><span class="glyphicon glyphicon-list glyphiconList"></span></a>
                    <ul class="dropdown-menu">
                        <li><a onclick="exportChildInfo(this)" href="javascript:void(0)"><span class="glyphicon glyphicon-export"></span> <?php echo Yii::t('site', 'Export Name List'); ?>                               </a></li>
                    </ul>
                </span>
                <span class='ml12'><?php echo Yii::t('site','Discounts & Prices');?></span>
                <span class="label label-default ml8" id="total-number"></span>
            </h1>
        </div>
    </div>
    <div class="row">
        <div class="col-md-12">
            <?php foreach($classList as $val):?>
                <div class="class-item" age="<?php echo $val["child_age"];?>">
                    <div class="panel panel-default">
                        <div class="panel-heading"><span class="text"><?php echo $val["title"];?>  <span class="badge"><?php echo $val["count"];?></span></span></div>
                        <div class="panel-body table-responsive" id="class-box-<?php echo $val["classid"];?>">
                            <table class="table table-hover">
                                <thead></thead>
                                <tbody>

                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            <?php endforeach;?>
        </div>
    </div>
</div>
<script type="text/template" id="child-item-template">
    <tr>
        <td width="120"><a target="_blank" href="<% print(childBaseUrl+'&childid='+childid); %>"><%= name %></a></td>
        <td childIndex=<%= childIndex %> childid=<%= childid %>>
            <span class="col-md-2 col-xs-4 amount"><input class="form-control" id="constant-<%=childid%>" type="text" size="6" childid="<%=childid%>" value="<%= amount %>" readonly="readonly"></span>
            <% if (discount>=0 && discountChildStat>0){%>
                <span class="mr15"><%= discountTitle %>(<%= discount %>)</span>
                <% if (useStartYearOfSchool==0){%><span class="final mr15"><?php echo Yii::t('student', 'Final Amount: ');?><%=formatMoney(Math.round(amount*discount/100))%></span><%}%>
                <span>
                <a href="<% print('/child/index/cancelBinding?childid='+childid+'&bindingId='+bindDiscountId+'&branchId='+schoolid); %>" class="btn btn-default J_dialog" title="<?php echo Yii::t('child','Cancel Discount');?>">
                    <span class="glyphicon glyphicon-remove"></span>
                    <?php echo Yii::t('child','Cancel Discount');?></a>
                </span>
            <%}%>
        </td>
    </tr>
</script>
<script>
    var childData = <?php echo CJSON::encode($childData); ?>;
    var enabledConstantTuition = <?php echo $enabledConstantTuition; ?>;
    var useStartYearOfSchool = <?php echo $useStartYearOfSchool; ?>;
    var template = _.template($('#child-item-template').html());
    var exportData = [];
    if(!_.isNull(childData)){
        for(var i=0; i<childData.length; i++){
            childData[i]['childIndex']=i;
            var view = template(childData[i],useStartYearOfSchool);
            var box = $('#class-box-'+childData[i]['classid']+' .table-hover tbody');
            box.append(view);
            if (childData[i].discount != '-1') {
                exportData.push([childData[i].childid, childData[i].name, childData[i].classtitle, childData[i].discountTitle, childData[i].discount]);
            }
        }
    }
    if(enabledConstantTuition){
        $('input.form-control').bind('dblclick',function(){
            $(this).attr('readonly',false);
            var id = $(this).attr('id');
            var childid = $(this).attr('childid');
            oldData =$(this).attr('value');
            $("#"+id).blur(function(){
                if(parseInt(oldData) == parseInt($("#"+id).val())){
                    $("#"+id).val(oldData);
                    $("#"+id).attr('readonly','readonly');
                    $("#"+id).unbind('blur');
                }else{
                    $.ajax({
                        type: "POST",
                        url: "<?php echo $this->createUrl('//child/list/processConstant');?>",
                        data: "childid="+childid+"&value="+$("#"+id).val(),
                        dataType: "json",
                        async:false,
                        success: function(data){
                            if(data.state == 'success'){
                                $("#"+id).val(data.data);
                                $("#"+id).attr('readonly','readonly');
                            }else{
                                alert(data.message);
                                $("#"+id).val(oldData);
                            }
                            $("#"+id).unbind('blur');
                        }
                    });
                }
            });
            $("#"+id).keydown(function(e){
                if(e.keyCode==13){
                   $(this).blur();
                }
            });
        })
    }

    function formatMoney(data){
        return $.number(data,2);
    }

    function removeElement(data){
        var obj = $('table.table-hover td[childid|="'+data.childId+'"]').find('span');
        for (var i=1; i<obj.length; i++){
            obj[i].remove();
        }
    }

    // 导出孩子列表
    function exportChildInfo(obj) {        
        const filename = 'childlist.xlsx';
        const ws_name = "SheetJS";

        const worksheet = XLSX.utils.aoa_to_sheet(exportData);
        const workbook = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(workbook, worksheet, ws_name);
        // generate Blob
        const wbout = XLSX.write(workbook, {bookType: 'xlsx', type: 'array'});
        const blob = new Blob([wbout], {type: 'application/octet-stream'});
        // save file
        let link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = filename;
        link.click();
        setTimeout(function() {
            // 延时释放掉obj
            URL.revokeObjectURL(link.href);
            link.remove();
        }, 500);
    }
</script>