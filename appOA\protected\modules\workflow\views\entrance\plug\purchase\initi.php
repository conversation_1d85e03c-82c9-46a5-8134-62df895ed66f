<?php 
    $purchaseProducts = PurchaseProducts::model()->findAll(); 
    $purchaseStandards = PurchaseStandard::model()->findAll('status=:status',array(':status'=>1));

    $schoolTypeModel = PurchaseStandard::model()->getType('school_type');
    $proTypeModel = PurchaseStandard::model()->getType('pro_type');
?>
<!-- Modal -->
<?php echo CHtml::form('','post',array('class'=>'J_ajaxForm'));?>
<div class="modal fade" id="<?php echo $data['modalNameId']?>" tabindex="-1" role="dialog" aria-labelledby="workflowModalLabel" data-backdrop="static">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id=""><?php echo CommonUtils::autoLang($data['workflow']->definationObj->defination_name_cn,$data['workflow']->definationObj->defination_name_en);?> - <?php echo CommonUtils::autoLang($data['workflow']->nodeObj->node_name_cn,$data['workflow']->nodeObj->node_name_en);?></h4>
            </div>
            <div class="modal-body" id="workflow-modal-body">
                <div class="row">
                    <!-- 采购申请标题 -->
                    <div class="col-xs-12">
                        <label class="col-xs-4">采购标题 *：</label>
                        <div class="col-xs-6">
                            <input type="text" name="PurchaseApplication[title]" class="form-control">
                        </div>
                    </div>
                    <!-- 选择是否为预算标准 -->
                    <div class="col-xs-12">
                        <br>
                        <label class="col-xs-4">预算标准 *：</label>
                        <div class="col-xs-6">
                            <div class="col-xs-6">
                              <label>
                                <input type="radio" name="PurchaseApplication[budget]" value="1">
                                预算内
                              </label>
                            </div>
                            <div class="col-xs-6">
                              <label>
                                <input type="radio" name="PurchaseApplication[budget]" value="0">
                                预算外
                              </label>
                            </div>
                        </div>
                    </div>
                    <!-- 采购申请标题 -->
                    <div class="col-xs-12">
                        <br>
                        <label class="col-xs-4">交货日期 *：</label>
                        <div class="col-xs-6">
                            <input type="text" name="PurchaseApplication[delivery_timestamp]" id="date" class="form-control">
                        </div>
                    </div>
                    <div class="col-xs-12">
                        <br>
                        <label class="col-xs-4">联络信息：</label>
                        <div class="col-xs-6">
                            <input type="text" name="PurchaseApplication[content]" placeholder="接货人姓名/手机号" class="form-control">
                        </div>
                    </div>
                    <!-- 添加库商品商品 -->
                    <div class="col-xs-12">
                        <br>
                        <label class="col-xs-4">添加库商品：</label>
                        <div class="col-xs-6">
                            <input type="text" id="search" class="form-control" placeholder="输入关键字查询库商品" title="输入关键字查询库商品">
                         </div>
                         <div class="col-xs-2">
                            <!-- <button class="btn btn-default btn-xs" onclick="addProduct(this)" type="button">添加</button> -->
                         </div>
                    </div>
                    <!-- 添加标配 -->
                    <div class="col-xs-12">
                        <br>
                        <label class="col-xs-4">添加标配：</label>
                        <div class="col-xs-6">
                            <select name="" id="schoolType" onchange="choiceStandard()" class="form-control">
                                <option value="">校园类型</option>
                                <?php 
                                    foreach ($schoolTypeModel as $schoolType) {
                                        echo '<option value="'.$schoolType->school_type.'">'.$schoolType->school_type.'</option>';
                                    }
                                 ?>
                            </select>
                            <br>
                            <select name="" id="proType" onchange="choiceStandard()" class="form-control">
                                <option value="">商品类型</option>
                                <?php 
                                    foreach ($proTypeModel as $proType) {
                                        echo '<option value="'.$proType->pro_type.'">'.$proType->pro_type.'</option>';
                                    }
                                 ?>
                            </select>
                            <br>
                            <select name="" id="select" class="form-control " onchange="addStandard(this)">   
                                <option value="0">选择标配</option>
                                <?php 
                                    // foreach ($purchaseStandards as $purchaseStandard) {
                                    // echo '<option data-price="'. $purchaseStandard->getTotalMoney() . '" value="'.$purchaseStandard->id.'">'.$purchaseStandard->cn_title.'</option>';
                                    // } 
                                ?>
                            </select>
                        </div>
                        <div class="col-xs-2">
                            <!-- <button class="btn btn-default btn-xs" onclick="addStandard(this)" type="button">添加</button> -->
                        </div>
                    </div>
                    <!-- 添加自定义商品 -->
                    <div class="col-xs-12">
                        <br>
                        <label class="col-xs-4 control-label">添加自定义商品:</label>
                        <!-- <div class="col-xs-4"></div> -->
                        <div class="col-xs-4">
                            <button class="btn btn-default btn-xs" type="button" onclick="addCustom()">添加</button>
                        </div>
                    </div>
                    <div class="col-xs-12">
                        <!-- 显示附件 -->
                        <div id="fileList">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>已上传资料</th>
                                        <th>资料说明</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                            </table>
                        </div>
                        <!-- 上传 -->
                        <div class="btn-group btn-group-justified" role="group">
                            <input id="file" type="file" name="file" style="display:none">
                            <div class="btn-group" role="group">
                                <a id="filebtn" class="btn btn-default" onclick="$('#file').click();">上传新资料</a>
                            </div>
                        </div>
                        <br>
                        <span class="text-warning" id="spaninfo"></span>
                    </div>
                    <!-- 显示列表 -->
                    <div class="col-xs-12">
                        <br>
                        <table id="list" class="table table-hover">
                            <thead>
                                <tr>
                                    <th>商品名</th>
                                    <th>数量</th>
                                    <th>单价</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                        <p>&nbsp;&nbsp;商品数量：<span id="purchase_num"></span><input type="hidden" name="purchase_num" value="0"></p>
                        <p>&nbsp;&nbsp;采购总价：<span id="purchase_price"></span><input type="hidden" name="purchase_price" value="0"></p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('global','Cancel');?></button>
                <button type="button" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global','Submit');?></button>
            </div>
        </div>
    </div>
</div>
<?php CHtml::endForm();?>
<script>
    $('#date').datepicker({'dateFormat':'yy-mm-dd'});
    var i = 0;
    //添加系统库商品
    function addProduct (str) {
        var arr = str.split('%');

        var title = arr[0];
        
        var id = arr[1];

        var price = arr[2];

        var num = arr[4];

        var cid = arr[3];

        if (title == undefined || id == undefined || price == undefined) {
            return false;
        };

        var con = '<tr><td><a target="_blank" href="<?php echo Yii::app()->createUrl("moperation/purchaseProducts/view"); ?>?id='+id+'">'+title+'</a><input type="hidden" name="purchaseArr['+i+'][PurchaseApplicationItem][title]" value="'+title+'" /></td>';
            con += '<td><input name="purchaseArr['+i+'][PurchaseApplicationItem][num]" value="' +num+ '" onchange="calculatePrice()" type="number" min=1 class="length_1" /></td>';
            con += '<td>'+price+'<input type="hidden" name="purchaseArr['+i+'][PurchaseApplicationItem][price]" value="'+price+'" /></td>';
            con += '<td><button class="btn btn-xs btn-danger" type="button" onclick="deleteList(this)">删除</button></td>';
            con += '<input type="hidden" name="purchaseArr['+i+'][PurchaseApplicationItem][type]" value="<?php echo PurchaseApplicationItem::PURCHASE_PRODUCT; ?>" />';
            con += '<input type="hidden" name="purchaseArr['+i+'][PurchaseApplicationItem][cid]" value="'+cid+'" />';
            con += '<input type="hidden" name="purchaseArr['+i+'][PurchaseApplicationItem][pid]" value="'+id+'" /></tr>';
        i++;
        $('#list tbody').prepend(con);
        calculatePrice();
    }

    //添加标配(备用)
    function addStandard1 (btn) {
        var selectedObj = $(btn).find("option:selected");

        var id = selectedObj.val();

        var title = selectedObj.text();

        var price = selectedObj.attr('data-price');

        if (title == undefined || id == undefined || price == undefined) {
            return false;
        };

        var con = '<tr><td><a target="_blank" href="<?php echo Yii::app()->createUrl("moperation/purchaseStandard/view"); ?>?id='+id+'">' +title+ '<input type="hidden" name="purchaseArr['+i+'][PurchaseApplicationItem][title]" value="'+title+'" /></td>';
            con += '<td><input name="purchaseArr['+i+'][PurchaseApplicationItem][num]" value="1" onchange="calculatePrice()" type="number" min=1 class="length_1" /></td>';
            con += '<td>' +price+ '<input type="hidden" name="purchaseArr['+i+'][PurchaseApplicationItem][price]" value="'+price+'" /></td>';
            con += '<td><button class="btn btn-xs btn-danger" type="button" onclick="deleteList(this)">删除</button></td>';
            con += '<input type="hidden" name="purchaseArr['+i+'][PurchaseApplicationItem][type]" value="<?php echo PurchaseApplicationItem::PURCHASE_STAND; ?>" />';
            con += '<input type="hidden" name="purchaseArr['+i+'][PurchaseApplicationItem][pid]" value="'+id+'" /></tr>';
        i++;
        $('#list tbody').prepend(con);
        calculatePrice();
    }

    //添加标配商品
    function addStandard (btn) {
        var selectedObj = $(btn).find("option:selected");

        var id = selectedObj.val();

        if (id == undefined) {
            return false;
        };

        $.post(
            '<?php echo Yii::app()->createUrl("moperation/purchaseStandard/getProducts"); ?>', 
            {id: id}, 
            function(data, status, xhr) {
                if (status == 'success') {
                    var arr = eval(data);
                    for (var i = arr.length - 1; i >= 0; i--) {
                        addProduct(arr[i]);
                    }
                }
        });
    }
    //添加自定义商品
    function addCustom (btn) {
        var con = '<tr><td><input name="purchaseArr['+i+'][PurchaseApplicationItem][title]" type="text" class="length_2"/></td>';
            con += '<td><input name="purchaseArr['+i+'][PurchaseApplicationItem][num]" value="1" onchange="calculatePrice()" type="number" min=1 class="length_1" /></td>';
            con += '<td><input name="purchaseArr['+i+'][PurchaseApplicationItem][price]" value="1" onchange="calculatePrice()" type="number" min=1 class="length_1" /></td>';
            con += '<td><button class="btn btn-xs btn-danger" type="button" onclick="deleteList(this)">删除</button></td>';
            con += '<input type="hidden" name="purchaseArr['+i+'][PurchaseApplicationItem][type]" value="<?php echo PurchaseApplicationItem::PURCHASE_CUSTOM; ?>" />';
            con += '<input type="hidden" name="purchaseArr['+i+'][PurchaseApplicationItem][cid]" value="0" />';
            con += '<input type="hidden" name="purchaseArr['+i+'][PurchaseApplicationItem][pid]" value="0" /></tr>';
        i++;
        $('#list tbody').prepend(con);
        calculatePrice();
    }

    //删除显示列表项
    function deleteList (btn) {
        $(btn).parent().parent().remove();
        calculatePrice();
    }

    //计算价格
    function calculatePrice () {
        var purchase_price = 0;
        var purchase_num = 0;
        var purchase_allNum = 0;
        $('#list tbody tr').each(function() {
            var num = $(this).find('input').eq(1).val();
            var price = $(this).find('input').eq(2).val();
            purchase_price += parseInt(num) * parseFloat(price);
            purchase_num ++;
            purchase_allNum += parseInt(num);
        });
        $('#purchase_num').text(purchase_num + ' * ' + purchase_allNum);

        $('#purchase_price').text(purchase_price.formatMoney());
        $('#purchase_price').next().val(purchase_price);
    }
    //筛选标配
    function choiceStandard() {
        var schoolType = $('#schoolType').val();
        var proType = $('#proType').val();
        if (schoolType && proType) {
            $.post(
                '<?php echo $this->createUrl('index',array('definationId'=>$data['workflow']->definationObj->defination_id,'branchId'=>$data['workflow']->opertationObj->branchid,'action'=>'getStandard')); ?>', 
                {schoolType: schoolType,proType: proType},
                function(data, textStatus, xhr) {
                    var res = eval('('+data+')');
                    $('#select').html(res.data);
            });
        }
    }
    //转换货币格式
    Number.prototype.formatMoney = function (places, symbol, thousand, decimal) {
        places = !isNaN(places = Math.abs(places)) ? places : 2;
        symbol = symbol !== undefined ? symbol : "¥ ";
        thousand = thousand || ",";
        decimal = decimal || ".";
        var number = this,
            negative = number < 0 ? "-" : "",
            i = parseInt(number = Math.abs(+number || 0).toFixed(places), 10) + "",
            j = (j = i.length) > 3 ? j % 3 : 0;
        return symbol + negative + (j ? i.substr(0, j) + thousand : "") + i.substr(j).replace(/(\d{3})(?=\d)/g, "$1" + thousand) + (places ? decimal + Math.abs(number - i).toFixed(places).slice(2) : "");
    };
    // 筛选商品
    $(function() {
        $( "#search" ).autocomplete({
          source: "<?php echo Yii::app()->createUrl('moperation/purchaseProducts/search');?>",
          select: function( event, ui ) {addProduct(ui.item.value);this.value="";return false;},
        });
      });

    //上传文件
    var definationId = <?php echo $data['workflow']->definationObj->defination_id; ?>;
    var nodeId = <?php echo $data['workflow']->nodeObj->node_id; ?>;
    var branchId = ''
    var operationId = 0;
    $(function(){
        $('#file').change(function(){
            var action = 'saveUploadFile';
            var url = '<?php echo Yii::app()->createUrl("/workflow/entrance/index")."?definationId=' +definationId+ '&nodeId=' +nodeId+ '&branchId=' +branchId+ '&operationId=' +operationId+ '&action=' +action+ '"; ?>';
            var formData = new FormData();
            formData.append('file', this.files[0]);
            $.ajax({
                url : url,
                type : 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success:function (data) {
                    var msg = eval('('+data+')');
                    if (msg.state == 'success') {
                        var html = '<tr><td><a target="_blank" href="' +msg.data.url+ '">' +msg.data.fileName+ '</a></td>';
                            html += '<td><input type="text" onchange=changFileName(this,' +msg.data.fileId+ ') class="form-control" placeholder="输入资料说明(XX班级XX申请单)"></td>'
                            html += '<td><a class="btn btn-danger btn-xs" onclick="deleteFile(this,' +msg.data.fileId+ ')"><span class="glyphicon glyphicon-trash"> </span></a></td></tr>';
                            html += '<input type="hidden" name="files[]" value=' +msg.data.fileId+ ' />';
                        $('#fileList > table').append(html);
                    };
                    $('#spaninfo').text(msg.message);
                }
            });
        });
    });
    //删除文件
    function deleteFile (btn,id) {
        if (confirm('确定要删除这个文件吗?')) {
            var action = 'deleteUploadFile';
            var url = '<?php echo Yii::app()->createUrl("/workflow/entrance/index")."?definationId=' +definationId+ '&nodeId=' +nodeId+ '&branchId=' +branchId+ '&operationId=' +operationId+ '&action=' +action+ '"; ?>';
            
            $.ajax({
                url : url,
                type : 'POST',
                data: {id : id},
                success:function (data) {
                    var msg = eval('('+data+')');
                    if (msg.state == 'success') {
                        $(btn).parent().parent().remove();
                    };
                    $('#spaninfo').text(msg.message);
                }    
            });
        };
    }
    //修改文件名
    function changFileName (btn,id) {
        var notes = $(btn).val();
        if (notes=="") {return false};
        var action = 'changeFileName';
        var url = '<?php echo Yii::app()->createUrl("/workflow/entrance/index")."?definationId=' +definationId+ '&nodeId=' +nodeId+ '&branchId=' +branchId+ '&operationId=' +operationId+ '&action=' +action+ '"; ?>';
        
        $.ajax({
            url : url,
            type : 'POST',
            data: {id : id,notes : notes},
            success:function (data) {
                var msg = eval('('+data+')');
                if (msg.state == 'success') {
                    var con = $(btn).parent().prev().children().text(notes);
                }
                $('#spaninfo').text(msg.message);
            }    
        });
    }
</script>