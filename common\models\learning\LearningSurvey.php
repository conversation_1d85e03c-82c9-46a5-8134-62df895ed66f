<?php

/**
 * This is the model class for table "ivy_learning_survey".
 *
 * The followings are the available columns in table 'ivy_learning_survey':
 * @property integer $id
 * @property string $satisfaction
 * @property string $valuable
 * @property string $worthless
 * @property string $grouping_impression
 * @property string $improve
 * @property integer $Grouping_quality
 * @property integer $correlation
 * @property integer $food
 * @property integer $group_sessions
 * @property integer $keynote
 * @property integer $venue
 * @property string $schoolid
 * @property integer $uid
 * @property integer $times
 * @property string $status
 */
class LearningSurvey extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_learning_survey';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('satisfaction, valuable, worthless, grouping_impression, improve, Grouping_quality, correlation, food, keynote, venue, schoolid, uid, times, status', 'required'),
			array('Grouping_quality, correlation, food, group_sessions, keynote, venue, uid, times', 'numerical', 'integerOnly'=>true),
			array('schoolid', 'length', 'max'=>11),
			array('status', 'length', 'max'=>255),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, satisfaction, valuable, worthless, grouping_impression, improve, Grouping_quality, correlation, food, group_sessions, keynote, venue, schoolid, uid, times, status', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'satisfaction' => 'Satisfaction',
			'valuable' => 'Valuable',
			'worthless' => 'Worthless',
			'grouping_impression' => 'Grouping Impression',
			'improve' => 'Improve',
			'Grouping_quality' => 'Grouping Quality',
			'correlation' => 'Correlation',
			'food' => 'Food',
			'group_sessions' => 'Group Sessions',
			'keynote' => 'Keynote',
			'venue' => 'Venue',
			'schoolid' => 'Schoolid',
			'uid' => 'Uid',
			'times' => 'Times',
			'status' => 'Status',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('satisfaction',$this->satisfaction,true);
		$criteria->compare('valuable',$this->valuable,true);
		$criteria->compare('worthless',$this->worthless,true);
		$criteria->compare('grouping_impression',$this->grouping_impression,true);
		$criteria->compare('improve',$this->improve,true);
		$criteria->compare('Grouping_quality',$this->Grouping_quality);
		$criteria->compare('correlation',$this->correlation);
		$criteria->compare('food',$this->food);
		$criteria->compare('group_sessions',$this->group_sessions);
		$criteria->compare('keynote',$this->keynote);
		$criteria->compare('venue',$this->venue);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('uid',$this->uid);
		$criteria->compare('times',$this->times);
		$criteria->compare('status',$this->status,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return LearningSurvey the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}
