<!-- Modal -->
<?php echo CHtml::form('/workflow/entrance/index', 'post',array('class'=>'J_ajaxForm'));?>
<div class="modal fade" id="<?php echo $data['modalNameId']?>" tabindex="-1" role="dialog" aria-labelledby="workflowModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel"><?php echo CommonUtils::autoLang($data['workflow']->definationObj->defination_name_cn,$data['workflow']->definationObj->defination_name_en);?> - <?php echo CommonUtils::autoLang($data['workflow']->nodeObj->node_name_cn,$data['workflow']->nodeObj->node_name_cn);?></h4>
            </div>
            <div class="modal-body" id="workflow-modal-body">
                <ul class="nav nav-wizard mb20">
                    <li style="width:50%;"><a>第一步：选择孩子</a></li>
                    <li style="width:50%;" class="active"><a>第二步：提现金额</a></li>
                </ul>
                <div class="alert alert-warning" role="alert">
                    余额提现是在收到家长提出的提现申请之后，将孩子个人账户的部分或全部余额以银行转账的形式汇入家长提供的银行账户，网银操作完成之后需要财务人员使用本功能进行记录。
                </div>
                <p>孩子名称：<?php echo $data['profileBasic']->getChildName();?>  [ 当前余额 ]：<?php echo CHtml::tag('span',array('style'=>'color:red;'),number_format($data['profileBasic']->credit,2));?></p>
                <div class="form-group">
                    <?php echo CHtml::textField("WorkflowChildCredit[amount]", '', array('placeholder'=>'提现金额','class'=>'form-control'));?>
                </div>
            </div>
            <div class="modal-footer">
                <?php echo CHtml::hiddenField('definationId', $data['workflow']->getWorkflowId());?>
                <?php echo CHtml::hiddenField('nodeId', $data['workflow']->getCurrentNodeId());?>
                <?php echo CHtml::hiddenField('branchId', $data['workflow']->schoolId);?>
                <?php echo CHtml::hiddenField('operationId', $data['workflow']->getOperateId());?>
                <?php echo CHtml::hiddenField('step', 'two');?>
                <?php echo CHtml::hiddenField('buttonCategory', '');?>
                <?php echo CHtml::hiddenField('action', 'publicSave');?>
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('workflow', 'Cancel');?></button>
                <button type="button" class="btn btn-info J_ajax_submit_btn" data-category="previous">上一步</button>
                <button type="button" class="btn btn-primary J_ajax_submit_btn" data-category="next">提交</button>
            </div>
        </div>
    </div>
</div>
<?php echo CHtml::endForm();?>
<script type=" text/javascript">
    var nodeListHtml = '<?php echo $data['workflow']->getNodeListUseHtml();?>';
    var currentOperatorId = <?php echo $data['workflow']->getOperateId();?>;
    if (!$('#operation_id_'+currentOperatorId).html()){
        $('#wait-check-list').prepend(nodeListHtml);
        head.Util.ajaxDel();
        head.Util.ajax();
    }
</script