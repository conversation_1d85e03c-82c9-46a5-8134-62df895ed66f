<?php

/**
 * This is the model class for table "ivy_child_constant_tuition".
 *
 * The followings are the available columns in table 'ivy_child_constant_tuition':
 * @property integer $childid
 * @property double $amount
 * @property string $schoolid
 * @property string $tuition_type
 */
class ChildConstantTuition extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @return ChildConstantTuition the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_child_constant_tuition';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('schoolid, tuition_type', 'required'),
			array('childid', 'numerical', 'integerOnly'=>true),
			array('amount', 'numerical'),
			array('schoolid', 'length', 'max'=>10),
			array('tuition_type', 'length', 'max'=>255),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('childid, amount, schoolid, tuition_type,startyear', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
			'ChildProfile'=>array(self::BELONGS_TO,'ChildProfileBasic','childid','select'=>'first_name_en,last_name_en,name_cn,status'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'childid' => 'Childid',
			'amount' => 'Amount',
			'schoolid' => 'Schoolid',
			'tuition_type' => 'Tuition Type',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('childid',$this->childid);
		$criteria->compare('amount',$this->amount);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('tuition_type',$this->tuition_type,true);

		return new CActiveDataProvider(get_class($this), array(
			'criteria'=>$criteria,
		));
	}
}