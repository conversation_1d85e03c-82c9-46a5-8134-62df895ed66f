<?php

/**
 * This is the model class for table "ivy_workflow_group".
 *
 * The followings are the available columns in table 'ivy_workflow_group':
 * @property integer $groupid
 * @property string $name
 * @property string $description
 * @property integer $group_type
 * @property string $group_alias
 */
class WorkflowGroup extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @return WorkflowGroup the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_workflow_group';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('group_alias', 'required'),
			array('group_type', 'numerical', 'integerOnly'=>true),
			array('name, group_alias', 'length', 'max'=>255),
			array('description', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('groupid, name, description, group_type, group_alias', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'groupid' => 'Groupid',
			'name' => 'Name',
			'description' => 'Description',
			'group_type' => 'Group Type',
			'group_alias' => 'Group Alias',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('groupid',$this->groupid);
		$criteria->compare('name',$this->name,true);
		$criteria->compare('description',$this->description,true);
		$criteria->compare('group_type',$this->group_type);
		$criteria->compare('group_alias',$this->group_alias,true);

		return new CActiveDataProvider(get_class($this), array(
			'criteria'=>$criteria,
		));
	}
}