<?php
class recentJournal extends CWidget{
	public $classId;
    public $yId;
    public $schoolId;
    public $childId;
	
	public function init(){
        
	}
	
	public function run(){

        Yii::import('common.models.portfolio.*');
        Yii::import('common.models.calendar.Calendar');
        Yii::import('common.models.calendar.CalendarWeek');
        
        $criteria = new CDbCriteria();
        $criteria->compare('t.childid', $this->childId);
//        $criteria->compare('t.yid', $this->yId);
        $criteria->compare('t.stat', 20);
        $criteria->order='t.updated_timestamp desc';
        $criteria->limit=5;
        $report = NotesChild::model()->with('yInfo')->findAll($criteria);
        
//        $weeks = CalendarWeek::weeks($this->yId, true);
        
        $recentjournal  = array();
        $mondayarr      = array();
        foreach ($report as $rep){
//            $recentjournal[$rep->weeknumber]['txt']=$weeks[$rep->weeknumber]['txt'];
            $recentjournal[$rep->weeknumber]['startyear']=$rep->yInfo->startyear;
            $recentjournal[$rep->weeknumber]['classid']=$rep->classid;
            $mondayarr[$rep->weeknumber]=$rep->updated_timestamp;
        }
        $mintemp = $mondayarr ? min($mondayarr) : 0;
        $minweek = $mondayarr ? min(array_keys($mondayarr)) : 0;
        
        $criteria = new CDbCriteria();
        $criteria->compare('t.childid', $this->childId);
//        $criteria->compare('t.classid', $this->classId);
//        $criteria->compare('t.yid', $this->yId);
        $criteria->compare('t.stat', 20);
        $criteria->compare('t.timestamp', '>='.$mintemp);
        $criteria->order='t.timestamp desc';
        $criteria->limit=1;
        $semester = SemesterReport::model()->with('yInfo')->find($criteria);
        
        if (isset($semester->timestamp) && $semester->timestamp > $mondayarr[$minweek]){
            unset($mondayarr[$minweek]);
            $tmp = $semester->yInfo->startyear.'-'.($semester->yInfo->startyear+1).' ';
            $tmp .= $semester->semester == 10 ? Yii::t("portfolio", "Fall") : Yii::t("portfolio", "Spring");
            $mondayarr['semester'] = $tmp;
        }
        arsort($mondayarr);
        
        $this->render('recentjournal',array(
            'recentjournal'=>$recentjournal,
            'mondayarr'=>$mondayarr,
        ));
	}
	
}