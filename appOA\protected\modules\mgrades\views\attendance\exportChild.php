<?php

function statistics($courseArr = '', $teacherName, $course_code, $term, $isRepeat, $classroomArr)
{
    $courseName = $courseArr[$course_code];
    if ($term == 1 || $term == 2) {
        $courseName = "<strong>Term{$term}:</strong> " . $courseName;
    }
    $pColor = '';
    if ($isRepeat) {
        $pColor = 'text-danger';
    }
    if($teacherName[$course_code]){
        $data = "<div class='course {$pColor}'>" . $courseName . "</div>" .
        "<div class='teacher'><span class='glyphicon glyphicon-user'></span> " . $teacherName[$course_code] ."</div>";
    }else{
        $data = "<div class='course {$pColor}'>" . $courseName . "</div>";
    }
    if($classroomArr[$course_code]){
        $data .= "<div class='teacher'><span class='glyphicon glyphicon-map-marker'></span> " . $classroomArr[$course_code] ."</div>";
    }
    return $data;
}
if($studentData){
foreach ($studentData as $child=>$val){ ?>

            <table class="table" style="margin-bottom: 0">
                <tbody>
                    <tr>
                        <td>
                            <h3>
                                <?php echo $childData[$child]['name'] ?>
                                <small>
                                    <?php echo $calendar_yearly_data['school_year']?>
                                </small>
                            </h3>
                            <h5>
                                <small style="color: #666"><?php echo $scheduleType?></small>
                            </h5>
                        </td>
                        <td>
                            <div id="qrcode_<?php echo $child?>" style="float: right"></div>
                            <script>
                                $('#qrcode_<?php echo $child?>').qrcode({width: 80,height: 80,text: "<?php echo $QRcode[$child]?>"});
                            </script>
                        </td>
                    </tr>
                </tbody>
            </table>
            <table class="table table-bordered">
                <thead>
                <tr>
                    <th width="10">#</th>
                    <th width="20%"><?php echo Yii::t('attends','Mon') ?></th>
                    <th width="20%"><?php echo Yii::t('attends','Tue') ?></th>
                    <th width="20%"><?php echo Yii::t('attends','Wed') ?></th>
                    <th width="20%"><?php echo Yii::t('attends','Thu') ?></th>
                    <th width="20%"><?php echo Yii::t('attends','Fri') ?></th>
                </tr>
                </thead>
                <tbody>
                <?php foreach ($val as $period=>$weekItem){?>
                    <tr class="courseArr">
                        <th scope="row">
                            <?php if($period == 1){?>
                                HR
                            <?php }else{?>
                            <?php echo '#'.($period-1); ?>
                            <?php }?>
                        </th>
                        <?php foreach($weekItem as $ca=>$item){?>
                            <td>
                                <?php foreach ($item as $key=>$value){?>
                                    <?php echo "<div class='course text-danger'><strong>".$key.":</strong>".$courses[$value['course_code']]."</div>"?>
                                    <?php echo '<div class="teacher"><span class="glyphicon glyphicon-user"></span> '.$teacher[$value['course_id']]['name'].'</div>'?>
                                    <?php echo '<div class="teacher"><span class="glyphicon glyphicon-map-marker"></span>'.$value['class_room_name'].'</div>'?>
                                <?php }?>
                            </td>
                        <?php }?>
                    </tr>
                <?php }?>

                </tbody>
            </table>


    <div style="page-break-before:always"></div>
<?php
}
} ?>

<style type="text/css">
div.course {font-size: 13px;}
div.teacher {font-size: 12px; color: #777}
.pagebreak {page-break-after:always;}
@media print {
    div.teacher{
        color: #777 !important;
    }
}
</style>