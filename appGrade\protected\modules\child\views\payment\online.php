
<?php
/*
 * <PERSON><PERSON>
 * 2012-7-18
 *
 */
$this->breadcrumbs = array(
    Yii::t("navigations", "Payment") => array('/child/payment/summary'),
    Yii::t("navigations", "Make Payment"),
);
?>

<h1>
    <label><?php echo Yii::t("navigations", 'Make Payment'); ?> </label>
</h1>

<div id="invoiceinfo" class="invoiceinfo">

    <?php
    $colorbox = $this->widget('application.extensions.colorbox.JColorBox');
    $colorbox->addInstance('.colorbox', array('iframe' => false, 'width' => '725', 'height' => '80%'));


    $footer1 = (!empty($dataProvider->data)) ? Yii::t('payment', 'Total') : null;
    $footer2 = (!empty($dataProvider->data)) ? '￥<span id="due_amount_total"></span>' : null;
    ?>
    <form method="post" id="confirm_invoice_onlineBank"
          action="<?php echo $this->createUrl('payment/onlineConfirm'); ?>">
        <div class="paylist">
            <?php
            $invoice_model = Invoice::model();
            $this->widget('zii.widgets.grid.CGridView', array(
                'cssFile' => Yii::app()->theme->baseUrl . '/widgets/gridview/styles.css',
                'id' => 'unpaid-list',
                'dataProvider' => $dataProvider,
                'enableSorting' => false,
                'emptyText' => Yii::t("payment", 'All invoices have been paid.  Thank you very much!'),
                'columns' => array(
                    array(
                        'class' => 'CCheckBoxColumn',
                        'header' => Yii::t("global", 'Please Select'),
                        'checked' => '$data->invoice_id',
                        'value' => '$data->invoice_id',
                        'selectableRows' => 2,
                        'checkBoxHtmlOptions' => array(
                            'name' => 'invoice_id[]',
                        ),
                    ),
                    array(
                        'name' => 'title',
                        'type' => 'raw',
                        'value' => 'CHtml::link($data->title,Yii::app()->getController()->createUrl("//child/payment/viewInvoice", array("invoice_id"=>$data->invoice_id)),array("class"=>"colorbox"))',
                    ),
                    'amount',
                    array(
                        'header' => Yii::t("payment", 'Amount Paid'),
                        'value' => array($invoice_model, 'renderPaidAmountF'),
                        'footer' => $footer1
                    ),
                    array(
                        'header' => Yii::t("payment", 'Amount Due'),
                        'type' => 'raw',
                        'value' => array($invoice_model, 'renderDueAmountHtml'),
                        'footer' => $footer2,
                        'htmlOptions' => array('width' => 145),
                    ),
                ),
                'template' => '{items}', //不用显示{summary}
            ));
            ?>
        </div>

        <?php
        if (!empty($dataProvider->data)) {
            if (!empty($paymentMethod)) {
                $tabTitles = array(
                    'yeepay' => Yii::t("payment", "Online Banking YP"),
                    'alipay' => Yii::t("payment", "Online Banking"),
                    'bankTransfer' => Yii::t("payment", "Bank Transfer"),
                    'wxpay' => Yii::t("payment", "Wechat Pay"),
                );

                $juiTabs = null;
                foreach ($paymentMethod as $pm) {
                    $juiTabs[$tabTitles[$pm]]['id'] = 'tab' . ucfirst($pm);
                    if ($pm == $currentPaymentMethod) {
                        $juiTabs[$tabTitles[$pm]]['content'] = $currentPaymentMethodHtml;
                    } else {
                        $juiTabs[$tabTitles[$pm]]['ajax'] = $this->createUrl('//child/' . $pm . '/onlineBankInfo', array('tid' => 'tab' . ucfirst($pm)));
                    }
                }

                $loadingImgUrl = Yii::app()->theme->baseUrl . '/images/loading.gif';
                $this->widget('zii.widgets.jui.CJuiTabs', array(
                    'tabs' => $juiTabs,
                    // additional javascript options for the tabs plugin
                    'options' => array(
                        'collapsible' => false,
                        'ajaxOptions' => array(
                            'type' => 'post',
                            'beforeSend' => 'js:function( event, ui){
                            var loadUrl = ui.url;
                            var tidIndex = loadUrl.indexOf("tid");
                            var tid = loadUrl.substring(tidIndex+4);
                            if($("#"+tid).html() == ""){
                            $("#"+tid).html("<p style=\"text-align:center\"><img src=\'' . $loadingImgUrl . '\'></p>");
                                    return true;
                            }else{
                                    return false;
                            }
			}',
                            'success' => 'js:function(data, textStatus, jqXHR){
                                jQuery(\'.colorbox\').colorbox({\'iframe\':false,\'width\':\'725\',\'height\':\'80%\'});
                            }',
                        )
                    ),
                ));
                $this->beginWidget('zii.widgets.jui.CJuiDialog', array(
                    'id' => 'mydialog',
                    // additional javascript options for the dialog plugin
                    'options' => array(
                        // 提示
                        'title' => Yii::t("payment", 'Tips'),
                        'autoOpen' => false,
                    ),
                ));

                $this->endWidget('zii.widgets.jui.CJuiDialog');
                echo CHtml::hiddenField('childid', $childId);
                echo CHtml::hiddenField('payment_mothod');
            } else {
                echo '<div class="flash-info">';
                echo Yii::t("payment", 'Sorry, online payment is not available for this campus.');
                echo '</div>';
            }
        }
        ?>

    </form>

    <div id="unpaid_invoice_max_tip" style="display: none;">
        <div>
            <?php echo Yii::t("payment", 'Please select up to 6 invoices each time to proceed the payment.'); ?>
        </div>
    </div>
</div>
