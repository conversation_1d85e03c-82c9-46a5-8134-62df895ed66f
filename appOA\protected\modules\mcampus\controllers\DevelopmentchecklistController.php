<?php

class DevelopmentchecklistController extends BranchBasedController{

    public function createUrl($route, $params = array(), $ampersand = '&')
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        return parent::createUrl($route, $params, $ampersand);
    }

    public function init(){
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";
       
        $this->modernMenuFlag = 'campusOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site','Campus Workspace');

        //初始化选择校园页面
        $this->branchSelectParams['hideOffice'] = false;
        $this->branchSelectParams['urlArray'] = array('//mcampus/developmentchecklist/index');
        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/vue2.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/echarts.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/underscore-min.js');
        $cs->registerCssFile($cs->getCoreScriptUrl().'/jui/css/base/jquery-ui.css');
        $cs->registerScriptFile($cs->getCoreScriptUrl().'/jui/js/jquery-ui.min.js');
    }

    /*
     *
     * 查询年份拿到class
     * 根据 学校班级去查询所有的data
     *
     * category  大分类
     * templast  根据大分类拿到分类下所有小分类的ID
     *
     * 根绝小分类ID 去匹配data分数算出总和
     *
     */
    public function actionIndex()
    {
        $yid = Yii::app()->request->getParam('yid', '');
        $classid = Yii::app()->request->getParam('classArr', 'all');
        $semester = Yii::app()->request->getParam('semester', '');

        $dataArr = array();
        Yii::import('common.models.calendar.*');
        Yii::import('common.models.childdev.*');
        $model = new CalendarSchool();
        $yid = ($yid) ? $yid : $model->getCurrentSchoolYearCalendar($this->branchId);
        $semester = ($semester) ? $semester : $this->getSemesterIndex($yid, time());

        $criteria = new CDbCriteria();
        $criteria->compare('branchid', $this->branchId);
        $criteria->order='startyear asc';
        $calendarModel = CalendarSchool::model()->findAll($criteria);
        $schoolList = array();
        foreach ($calendarModel as $item){
            $schoolList[$item->yid] = $item->startyear;
        }
        $class = array('all' => '全园');
        $criteria = new CDbCriteria();
        $criteria->compare('schoolid', $this->branchId);
        $criteria->compare('yid', $yid);
        $criteria->compare('classtype', array('n','b','p','k'));
        $criteria->order='child_age asc';
        $ivyClassModel = IvyClass::model()->findAll($criteria);
        $classList = array();
        $classArr = array();
        foreach ($ivyClassModel as $val){
            $classList[$val->classid] = $val->title;
            $classArr[$val->classid] = $val->classtype;
        }

        $classList = $class + $classList;
        if(!$classArr[$classid]){
            $dataArr = $this->allRecord($semester, $yid, $schoolList[$yid]);
        }else{
            $dataArr = $this->onlyrClassRecord($semester, $yid, $classArr[$classid], $schoolList[$yid], $classid);
        }

        $classArrData = $classArr + array('all' => 'all');

        $this->render('index', array(
            'dataArr' => $dataArr,
            'yid' => $yid,
            'schoolList' => $schoolList,
            'classArr' => $classid,
            'classtype' => $classArrData[$classid],
            'semester' => $semester,
            'classList' => $classList,
        ));
    }

    // 根据时间判断是第一学期还是第二学期
    public function getSemesterIndex($yid, $timestamp)
    {
        Yii::import('common.models.calendar.CalendarSemester');
        $semesterInfo = CalendarSemester::model()->getSemesterTimeStamp($yid);
        $semester = 1;
        if (isset($semesterInfo) && $timestamp >= $semesterInfo['spring_start']) {
            $semester = 2;
        }
        return $semester;
    }

    public function actionClass()
    {
        $yid = Yii::app()->request->getParam('yid', '');

        $class = array('all' => '全园');
        $criteria = new CDbCriteria();
        $criteria->compare('schoolid', $this->branchId);
        $criteria->compare('yid', $yid);
        $criteria->compare('classtype', array('n','b','p','k'));
        $criteria->order='child_age asc';
        $ivyClassModel = IvyClass::model()->findAll($criteria);
        $classList = array();
        if($ivyClassModel) {
            foreach ($ivyClassModel as $val) {
                $classList[$val->classid] = $val->title;
            }
        }

        echo json_encode($classList);
    }

    public function onlyrClassRecord($semester, $yid, $class_type, $startyear, $class)
    {
        $isOld = ($startyear > 2017) ? 1 : 0;
        $dataIntegration['dataArr'] = array();
        $dataIntegration['singleClassData'] = array();
        $dataIntegration['allData'] = array();
        // ------------------- 根据班级拿到选项 start  ------------------- //
        if($isOld){
            $sql = "SELECT * FROM `ivy_dev_category`  where `class_type` = '" . $class_type . "'";
        }else{
            $sql = "SELECT * FROM `ivy_dev_category_old`  where `class_type` = '" . $class_type . "'";
        }

        $categoryModel = Yii::app()->db->createCommand($sql)->queryAll();

        $data = array();
        foreach ($categoryModel as $val){
            if($val['pid'] == 0){
                $data[$val['id']]['title'] = (Yii::app()->language == 'zh_cn') ? $val['cn_title'] : $val['en_title'] ;
            }
            if($data[$val['pid']] && $val['parent_id'] == 0){
                $data[$val['pid']]['arr'][$val['id']]['title'] = (Yii::app()->language == 'zh_cn') ? $val['cn_title'] : $val['en_title'] ;
            }

            if($data[$val['pid']]['arr'][$val['parent_id']] && $val['parent_id'] != 0){
                $data[$val['pid']]['arr'][$val['parent_id']]['arr'][$val['id']] = (Yii::app()->language == 'zh_cn') ? $val['cn_title'] : $val['en_title'] ;
            }
        }

        $bigFData = array();
        foreach ($data as $cai_id => $val){
            $dataIntegration['dataArr']['bAxis'][$cai_id] = $val['title'];
            foreach ($val['arr'] as $sub_id => $item){
                $dataIntegration['dataArr']['mAxis'][$sub_id] = $item['title'];
                if(isset($item['arr'])){
                    foreach ($item['arr'] as $key => $value) {
                        $dataIntegration['dataArr']['sAxis'][$sub_id][$key] = $value;
                    }
                }else{
                    $dataIntegration['dataArr']['sAxis'][$sub_id][$sub_id] = $item['title'];
                }

            }
        }

       // Yii::msg($dataIntegration);
        // ------------------- 根据班级拿到选项 start   拼成数组------------------- //
        // ------------------- 根据班级 期数 校历 学校 拿到数据 ------------------- //
        $criteria = new CDbCriteria();
        $criteria->compare('class_type', $class_type);
        if($semester == 1){
            $criteria->compare('fall_display', 2);
        }else{
            $criteria->compare('spring_display', 2);
        }
        $criteria->compare('branchid', $this->branchId);
        $criteria->compare('classid', $class);
        $criteria->compare('yid', $yid);
        $devModel = DevData::model()->findAll($criteria);
        $dataIntegration['totle'] = ($devModel) ? count($devModel) : 0;
        // 最小分类ID查询   cat_id 最大分类ID   sub_id  第二级分类ID
        $cat_ids =  join(',', array_keys($data));
        if($isOld){
            $sql = "SELECT * FROM `ivy_dev_templates`  where `cat_id` IN (" . $cat_ids . ")";
        }else{
            $sql = "SELECT * FROM `ivy_dev_templates_old`  where `cat_id` IN (".  $cat_ids . ")";
        }
        $devTemplateModel = Yii::app()->db->createCommand($sql)->queryAll();

        // ------------------- 根据班级 期数 校历 学校 拿到数据 ------------------- //
        /*
         * 拼接数据
         *
         */
        $bigData = array();
        $dataIntegration['proportion'] = array();
        foreach ($devTemplateModel as $val) {
            $bigData[$val['cat_id']][$val['id']] = $val['id'];
            $bigFData[$val['sub_id']][$val['id']] = $val['id'];
            $dataIntegration['proportion'][$val['cat_id']][1] = 0;
            $dataIntegration['proportion'][$val['cat_id']][2] = 0;
            $dataIntegration['proportion'][$val['cat_id']][3] = 0;
            $dataIntegration['proportionX'][$val['sub_id']][1] = 0;
            $dataIntegration['proportionX'][$val['sub_id']][2] = 0;
            $dataIntegration['proportionX'][$val['sub_id']][3] = 0;
        }

        $childData = array();
        $dataS = array();
        $dataX = array();
        if($devModel){
            foreach ($devModel as $val){
                $fall = unserialize($val->development);
                //Yii::msg($fall);
                if ($semester == 1) {
                    // 大分类
                    foreach ($bigData as $cai_id=>$item){
                        $num = 0;
                        foreach ($item as $value){
                            $num += $fall['fall'][$value];
                        }
                        $dataS[$val->childid][$cai_id] = round($num/count($item),0);
                    }
                    // 小分类
                    foreach ($bigFData as $sub_id=>$item){
                        $num = 0;
                        foreach ($item as $value){
                            $num += $fall['fall'][$value];
                        }
                        $dataX[$val->childid][$sub_id] = round($num/count($item),0);
                    }

                    foreach ($fall['fall'] as $tid => $v) {
                        $childData[$tid] += $v;
                    }
                }
                if ($semester == 2) {
                    // 大分类
                    foreach ($bigData as $cai_id=>$item){
                        $num = 0;
                        foreach ($item as $value){
                            $num += $fall['fall'][$value];
                        }
                        $dataS[$val->childid][$cai_id] = round($num/count($item),0);
                    }
                    // 小分类
                    foreach ($bigFData as $sub_id=>$item){
                        $num = 0;
                        foreach ($item as $value){
                            $num += $fall['fall'][$value];
                        }
                        $dataX[$val->childid][$sub_id] = round($num/count($item),0);
                    }
                    foreach ($fall['spring'] as $tid => $v) {
                        $childData[$tid] += $v;
                    }
                }
            }
        }

        if($dataS) {
            foreach ($dataS as $val) {
                foreach ($val as $key => $value) {
                    if ($value == 1) {
                        $dataIntegration['proportion'][$key][1] += 1;
                    }
                    if ($value == 2) {
                        $dataIntegration['proportion'][$key][2] += 1;
                    }
                    if ($value == 3) {
                        $dataIntegration['proportion'][$key][3] += 1;
                    }
                }
            }
        }

        if($dataX) {
            foreach ($dataX as $val) {
                foreach ($val as $key => $value) {
                    if ($value == 1) {
                        $dataIntegration['proportionX'][$key][1] += 1;
                    }
                    if ($value == 2) {
                        $dataIntegration['proportionX'][$key][2] += 1;
                    }
                    if ($value == 3) {
                        $dataIntegration['proportionX'][$key][3] += 1;
                    }
                }
            }
        }

        if($childData){
            $devTemplateArr = array();
            foreach ($devTemplateModel as $val){
                $devTemplateArr[$val['cat_id']][$val['sub_id']]['fraction'] += $childData[$val['id']];
                $devTemplateArr[$val['cat_id']][$val['sub_id']]['num'] += 1;
            }

            if($devTemplateArr){
                foreach ($devTemplateArr as $val){
                    foreach ($val as $tid => $item){
                        $dataIntegration['singleClassData'][$tid] = round($item['fraction'] / $item['num'] / count($devModel), 2);
                    }
                }

                $all = array();
                foreach ($devTemplateArr as $key=>$item){
                    foreach ($item as $val){
                        $all[$key]['fraction'] += $val['fraction'];
                        $all[$key]['num'] += $val['num'];
                    }
                }

                if($all){
                    foreach ($all as $k=>$val){
                        $dataIntegration['allData'][$k] = round($val['fraction'] / $val['num'] / count($devModel), 2);
                    }
                }
            }
        }
        
        return $dataIntegration;
    }

    // 全园的数据
    public function allRecord($semester, $yid, $startyear)
    {
        // 根据班级ID 和学校ID 拿到数据
        $criteria = new CDbCriteria();
        $criteria->compare('class_type', "<>n");
        if($semester == 1){
            $criteria->compare('fall_display', 2);
        }else{
            $criteria->compare('spring_display', 2);
        }
        $criteria->compare('branchid', $this->branchId);
        $criteria->compare('yid', $yid);
        $devModel = DevData::model()->findAll($criteria);

        /*$criteria = new CDbCriteria();
        $criteria->compare('class_type', "<>n");
        $devTemplateModel = DevTemplate::model()->findAll($criteria);*/

        $isOld = ($startyear > 2017) ? 1 : 0;
        if($isOld){
            $sql = "SELECT * FROM ivy_dev_templates  where 'class_type' != 'n'";
        }else{
            $sql = "SELECT * FROM ivy_dev_templates_old  where 'class_type' != 'n'";
        }
        $devTemplateModel = Yii::app()->db->createCommand($sql)->queryAll();
        $config = DevCategory::getConfig();
        $array = array();
        foreach ($config['typeOf'] as $k=>$item){
            foreach ($devTemplateModel as $val){
                if(in_array($val['cat_id'], $item)){
                    $array[$k][] = $val['id'];
                }
            }
        }
        $dataArr = array();
        $dataArr['xAxis'] = $config['titleList'];
        $dataArr['datas'] = array();
        $dataArr['totle'] = 0;

        $data = array();
        $childDatas = array();
        if($devModel) {
            $dataArr['totle'] = count($devModel);
            foreach ($array as $key => $val) {
                foreach ($devModel as $item) {
                    $fall = unserialize($item->development);
                    if ($semester == 1) {
                        foreach ($fall['fall'] as $tid => $v) {
                            if (in_array($tid, $val)) {
                                $childDatas[$item->childid][$key]['data'] += $v;
                                $childDatas[$item->childid][$key]['num'] += 1;
                                $data[$key]['fen'] += $v;
                                $data[$key]['num'] += 1;
                            }
                        }
                    }

                    if ($semester == 2) {
                        foreach ($fall['spring'] as $tid => $v) {
                            if (in_array($tid, $val)) {
                                $childDatas[$item->childid][$key]['data'] += $v;
                                $childDatas[$item->childid][$key]['num'] += 1;
                                $data[$key]['fen'] += $v;
                                $data[$key]['num'] += 1;
                            }
                        }
                    }
                }
            }
        }

        $childDataO = array();
        if($childDatas) {
            foreach ($childDatas as $key=> $val) {
                foreach ($val as $k=>$value){
                    $childDataO[$key][$k] = round($value['data'] / $value['num'], 0);
                }
            }

            foreach ($childDataO as $val) {
                foreach ($val as $key => $value) {
                    if(!$dataArr['proportion'][$key][1]){
                        $dataArr['proportion'][$key][1] = 0;
                    }
                    if(!$dataArr['proportion'][$key][2]){
                        $dataArr['proportion'][$key][2] = 0;
                    }
                    if(!$dataArr['proportion'][$key][3]){
                        $dataArr['proportion'][$key][3] = 0;
                    }

                    if ($value == 1) {
                        $dataArr['proportion'][$key][1] += 1;
                    }
                    if ($value == 2) {
                        $dataArr['proportion'][$key][2] += 1;
                    }
                    if ($value == 3) {
                        $dataArr['proportion'][$key][3] += 1;
                    }
                }
            }
        }

        if($data) {
            foreach ($data as $val) {
                $dataArr['datas'][] = round($val['fen'] / $val['num'], 2);
            }
        }
        return $dataArr;
    }
}

?>
