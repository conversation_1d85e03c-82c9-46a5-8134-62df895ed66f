<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('//mcampus/default/index')) ?></li>
        <li><?php echo CHtml::link(Yii::t('site', 'Routines'), array('//mcampus/default/index')) ?></li>
        <li class="active"><?php echo Yii::t('site', '课后课管理') ?></li>
        <li class="active"><?php echo Yii::t('site', '退费列表') ?></li>
    </ol>

    <div class="row">
        <div class="col-md-2 col-sm-2 mb10">
            <?php
            $this->widget('zii.widgets.CMenu', array(
                'items' => $this->module->getMenu(),
                'id' => 'pageCategory',
                'htmlOptions' => array('class' => 'nav nav-pills nav-stacked background-gray'),
                'activeCssClass' => 'active',
            ));
            ?>
        </div>
        <div class="col-md-10 col-sm-12">
            <!--分类菜单-->
            <?php
            $this->widget('zii.widgets.CMenu', array(
                'items' => $this->AttendMenu(),
                'id' => 'AttendMenu',
                'htmlOptions' => array('class' => 'nav nav-tabs'),
                'activeCssClass' => 'active',
                'itemCssClass' => ''
            ));
            ?>

            <div class="col-md-12">
                <div class="mb20">
                </div>
                <div class="mb10 row">
                    <!-- 搜索框 -->
                    <form class="" style="float: left;width: 100%" action="<?php echo $this->createUrl('refundOk'); ?>"
                          method="get">
                        <?php echo Chtml::hiddenField('branchId', $this->branchId); ?>
                        <div class="col-sm-2 form-group">
                            <?php echo Chtml::textField('childName', $_GET['childName'], array('class' => 'form-control', 'placeholder' => '孩子姓名')) ?>
                        </div>
                        <div class="col-sm-2 form-group">
                            <?php echo Chtml::dropDownList('courseGroup', $_GET['courseGroup'], $courseGroups, array('class' => 'form-control', 'onChange' => 'redirec()', 'empty' => Yii::t('teaching', '请选择课程组'))); ?>
                        </div>
                        <div class="col-sm-2 form-group">
                            <?php echo Chtml::dropDownList('course', $_GET['course'], array(), array('class' => 'form-control', 'empty' => Yii::t('teaching', '请选择课程'))); ?>
                        </div>
                        <div class="col-sm-2 form-group">
                            <?php echo Chtml::textField('refundTime', $_GET['refundTime'], array('class' => 'form-control datepicker', 'placeholder' => '退费时间')) ?>
                        </div>
                        <div class="">
                            <div class="">
                                <button class="btn btn-default ml5" type="submit"><span
                                        class="glyphicon glyphicon-search"> </span></button>
                            </div>
                        </div>
                    </form>
                </div>
                <?php
                $this->widget('ext.ivyCGridView.BsCGridView', array(
                    'id' => 'refundCompleted',
                    'afterAjaxUpdate' => 'js:head.Util.modal',
                    'dataProvider' => $refundCompleted,
                    'template' => "{items}{pager}",
                    //状态为无效时标红
                    //'rowCssClassExpression' => '( $data->status == 0 ? "active" : "" )',
                    'colgroups' => array(
                        array(
                            "colwidth" => array(50, 100, 200, 100, 100, 100, 100),
                        )
                    ),
                    'columns' => array(
                        array(
                            'name' => '#',
                            'value'=>'$data->id',
                        ),
                        array(
                            'name' => Yii::t('child', 'childName'),
                            'value' => array($this, "getChildName"),
                            "type" => "raw",
                        ),
                        array(
                            'name' => Yii::t('child', '课程名称 (时间表名称)'),
                            'value' => array($this, "getCourseName"),
                            "type" => "raw",
                        ),
                        array(
                            'name' => "refund_total_amount",
                            'value'=> array($this, "getAmount"),
                        ),
                        array(
                            'name' => 'refund_method',
                            'value' => array($this, "getMethod"),
                        ),
                        array(
                            'name' => 'dropout_date',
                            'value' => 'date("Y-m-d", $data->dropout_date)',
                        ),
                        array(
                            'name' => Yii::t('asa','操作'),
                            'value' => array($this, "getButton"),
                        ),
                    ),
                ));
                ?>
            </div>
        </div>
    </div>
</div>
<script>
    var  courseVal;
    var modal = '<div class="modal fade" id="modal" class="1123" tabindex="-1" role="dialog" aria-labelledby="modal"><div class="modal-dialog" role="document"><div class="modal-content"></div></div></div>';
    $('body').append(modal);

    $('.datepicker').datepicker({
        changeMonth: true,
        changeYear: true,
        dateFormat: 'yy-mm-dd',
    });

    var courses = <?php echo CJSON::encode($courses); ?>;
    var courseId = <?php echo ($_GET['course']) ? $_GET['course'] : 0 ?>;
    function cbDelRefind() {
        $.fn.yiiGridView.update('refund');
    }
    function cbUpdateRefind() {
        $('#refund-forms').modal('hide');
        $.fn.yiiGridView.update('refund');
    }

    function redirec() {
        courseVal = $('#courseGroup option:selected').val();
        if(courseVal){
            var courseList;
            $.each(courses[courseVal], function (i,l) {
                courseList += '<option value="' +i + '">'+l+'</option>';
            })
            $("#course").html(courseList);
        }else{
            $("#course").html('<option value="">请选择课程</option>');
        }
    }

    if(courseId){
        redirec();
        $("#course").val(courseId);
    }

</script>
<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>