<div style="font-family: 'Microsoft Yahei'">   
	<p>
		Dear Support,
	</p>
	<p>
		<?php echo $title_en ?> has <span style="color:rgb(255,165,0);"><b><?php echo count($numbers);?></b></span> new feedback, please login IvyOnline (Teaching -> Feedback) to reply.
	</p>
	<p><?php echo $subject_time ?></p>
	<p>Auto Email, please do not reply.</p>
	<p>Thanks.</p>
	<p>
		<?php echo $title ?> 共有 <span style="color:rgb(255,165,0);"><b><?php echo count($numbers);?> </b></span> 条周报告反馈未回复，请登录艾毅在线 “教学管理” -> “周报告反馈”进行回复。
	</p>
	
	<table border='1' cellpadding='2' cellspacing='0' width="90%">
		<tr>
			<th width="100" style="font-family: 'Microsoft Yahei';font-size:12px;">孩子姓名 Child Name</th>
			<th width="100" style="font-family: 'Microsoft Yahei';font-size:12px;">班级 Class</th>
			<th width="100" style="font-family: 'Microsoft Yahei';font-size:12px;">评论内容 Content</th>
			<th width="100" style="font-family: 'Microsoft Yahei';font-size:12px;">评论时间 Time</th>
		</tr>
		<?php foreach($numbers as $sch =>$number): ?>
			<tr>
				<td style="font-family: 'Microsoft Yahei';font-size:12px;">
					<?php echo $childInfo[$number['child']];?>
				</td>
				
				<td style="font-family: 'Microsoft Yahei';font-size:12px;">
					<?php echo $classInfo[$number['class']];?>
				</td>
				<td style="font-family: 'Microsoft Yahei';font-size:12px;">
					<?php echo $number['content'];?>
				</td>
				<td style="font-family: 'Microsoft Yahei';font-size:12px;">
					<?php echo date('Y-m-d H:i', $number['time']);?>
				</td>
			</tr>
			<?php endforeach;?>
	</table>
	<br>
	<p><?php echo $subject_time ?></p>
	<p>系统自动邮件，请不要回复。</p>
	<p>谢谢</p>

</div>