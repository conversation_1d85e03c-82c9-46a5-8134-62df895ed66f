//显示孩子名单
function displayChildList(nameList){
	var ul = $('#child-list');
	ul.html('');
	for(var i in nameList){
		photo = OAUploadBaseUrl + '/childmgt/' + nameList[i].photo;
		var item = $('<li></li>').attr('id', 'c'+i).attr('childid', i).html('<img class="face" src="'+photo+'">' + nameList[i].name);
		ul.append(item);
		
		$('#avatarChildId').append('<option value="'+i+'">'+nameList[i].name+'</option>');
		
		childIds.push(i);
	}
}

function previewCurrentAvatar(obj){
	var childId = $(obj).val();
	if(childId){
		var photo = OAUploadBaseUrl + '/childmgt/' + nameList[childId].photo;
		$('#current-avatar-preview').attr('src', photo).show();
		$('#J_childAvatar_pop_sub').removeClass('disabled').removeAttr('disabled');
	}else{
		$('#J_childAvatar_pop_sub').addClass('disabled').attr('disabled','disabled');
		$('#current-avatar-preview').hide();
	}
}

displayChildList(nameList);
$('#refresh-stat').show();
refreshStat();
lastStated = Math.round(new Date().getTime()/1000);

head.use('dialog', 'draggable', function() {
    region_pop = $('#J_childWeeklyMedia_pop');
    region_pop.draggable( { handle : '.pop_top'} );
    
    $('#J_childWeeklyMedia_pop_x, #J_childWeeklyMedia_pop_close').on('click', function(e){
		e.preventDefault();
		region_pop.hide();
	});
	
    region_pop1 = $('#J_quickMediaAssign_pop');
    region_pop1.draggable( { handle : '.pop_top'} );
    
    $('#J_quickMediaAssign_pop_x, #J_quickMediaAssign_pop_close').on('click', function(e){
		e.preventDefault();
		region_pop1.hide();
	});	
    
    region_pop2 = $('#J_childAvatar_pop');
    region_pop2.draggable( { handle : '.pop_top'} );
    
    $('#J_childAvatar_pop_x, #J_childAvatar_pop_close').on('click', function(e){
		e.preventDefault();
		region_pop2.hide();
	});	
    
});

$('a.syncCheck').click(function(){
	var cid = $(this).attr('childid');
	console.log(cid);
	var id1 = "#TagChild_" + cid;
	var id2 = "#LinkChild_" + cid;
	if($(id1).is(":checked") == $(id2).is(":checked")) {
		if($(id1).is(":checked")){
			$(id1).removeAttr('checked');
			$(id2).removeAttr('checked');
		}else{
			$(id1).attr('checked','checked');
			$(id2).attr('checked','checked');		
		}
	}else{
		if($(id1).is(":checked")){
			$(id2).attr('checked','checked');
		}else{
			$(id2).removeAttr('checked');
		}
	}
});

function doLinkRemove(ele){
	var dom=$(ele);
	var id = 'pitem-' + dom.attr('lid');
	$("div#"+id).toggle();
}

$('ul#child-list li').click(function(){
	if(pendingRefreshStats){
		refreshStat();
	}

	var childid = $(this).attr('childid');
	if(assignedChildren && isset(assignedChildren[childid])){
		$('#current-child').html('<li>' + $(this).html() + '</li>');
		$("#child-assigned-media tr").remove();
		for(var i in assignedChildren[childid]){
			var pid = assignedChildren[childid][i].pid;
			var id = assignedChildren[childid][i].id;
			var caption="";
			if(assignedChildren[childid][i].caption)
				caption=assignedChildren[childid][i].caption;
			if(isset(assignedMedias[pid])){
				var tr = $('<tr></tr>').html('<th><img class="full-preview" src="' + assignedMedias[pid] + '"/></th>'+
				'<td valign=top>' +
				'<div class="t1">' +
					'<label><input lid="'+id+'" onchange="doLinkRemove(this)" name="mediaLinkRemove['+pid+']" type="checkbox" />从周报告中移除，本操作不会删除媒体文件本身</label>' +
					'<div class="p10" id="pitem-'+id+'">' +
						'<input type="hidden" name="childid" value="'+childid+'">' + 
						'<input type="hidden" name="linkid['+pid+']" value="'+id+'" /> '+
						'<input placeholder="Caption" name="mediaCaption['+pid+']" type="text" class="input length_5 mb10" value="'+caption+'" /><br />'+
						'<label><input type="checkbox" name="forceCaption['+pid+']" value="'+pid+'" />将此文字作为本周所有已使用本媒体文件的注释</label><br /><br />' +
					'</div>' +
				'</div>' +
				'</td>'
				);
			}
			$("#child-assigned-media").append(tr);
		}
		$('.weeklyMediaByWeek').animate({scrollTop:0},200);
		$('#J_childWeeklyMedia_pop').show().css({
			left : ($(window).width() - region_pop.outerWidth())/2,
			top : ($(window).height() - region_pop.outerHeight())/2 //+ $(document).scrollTop()
		});
	}else{
		$('#J_childWeeklyMedia_pop').hide();
	}
});

//保存单个孩子每周媒体之后的回调
function postUpdatebyChild(data){
	$('#J_childWeeklyMedia_pop').hide();
	var childid = data.childid;
	if(data.byChild){
		if(data.bySingleChild)
			assignedChildren[childid] = data.byChild[childid];
		else
			assignedChildren = data.byChild;
	}
	else{
		assignedChildren[childid] = [];
	}
	//console.log(assignedChildren[childid]);
	
	if(data.mediaList){
		for(var i in data.mediaList){
			mediaList[i] = data.mediaList[i];
		}
	}
	displayMediaList();
	displayStat();
	
}

//保存孩子头像之后的回调
function postAvatarUpload(data){
	$('#J_childAvatar_pop').hide();
	nameList[data.childid].photo = data.filename;
	$('#c'+data.childid).children('img.face').attr('src', data.photo);
}

function refreshStat(){
	$('#child-list li em').remove();
	if(childIds.length > 0){
		$.ajax({
			url: url_RefreshStats,
			dataType: 'json',
			type: 'POST',
			data:{
				childids: childIds,
				classid: currentClassId,
				weeknum: currentWeeknum,
				startyear: currentStartYear,
				securityCode: refreshStatsSecurityCode,
				}
		}).done(function(data){
			if(data.status == 'success'){
				assignedChildren = data.data['byChild'];
				assignedMedias = data.data['medias'];
				
				displayStat();
				
				pendingRefreshStats = false;
			}else{
				alert(data.message);
				return false;
			}
		});
	}
}

function displayStat(){
	$('#child-list li em.stats').remove();
	for(i=0;i<childIds.length;i++){
		dom=$('li#c'+childIds[i]);
		if(dom.length!=0){
			var em = $('<em></em>').addClass('stats');
			var num;
			if(assignedChildren && isset(assignedChildren[childIds[i]])){
				num = assignedChildren[childIds[i]].length;
			}
			else{
				num = 0;
			}
			var cssnum;
			if(num < 3) cssnum =1
			else if(num < 8 ) cssnum=2
			else cssnum=3;
			var extraCss = 'stats-' + cssnum;
			em.addClass(extraCss);
			em.html(num);
			dom.prepend(em);
		}
	}
}
