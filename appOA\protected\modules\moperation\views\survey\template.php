<style>
    [v-cloak] {
        display: none;
    }
</style>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site','HQ Operations'), array('/moperation'))?></li>
        <li><?php echo CHtml::link(Yii::t('site','问卷管理'), array('index'))?></li>
        <li class="active"><?php echo Yii::t('site','模版管理') ?></li>
    </ol>

    <div class="row">
        <div class="col-md-2 col-sm-2">
            <?php

            $this->widget('zii.widgets.CMenu',array(
                'items'=> $this->getSubMenu(),
                'id'=>'pageCategory',
                'htmlOptions'=>array('class'=>'nav nav-pills nav-stacked text-left background-gray'),
                'activeCssClass'=>'active',
                'itemCssClass'=>''
            ));
            ?>
        </div>
        <div class="col-md-10 col-sm-10" id='template' v-cloak>
            <div >
                <button type="submit" class="btn btn-primary pull-right" @click='addTem()'>添加</button>
                <div class="clearfix"></div>
            </div>
            <div class="panel panel-default mt15">
              <div class="panel-body">
              <table class="table">
                <thead>
                    <tr>
                        <th>模板名称</th>
                        <th>更新时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for='(datas,index) in data'>
                        <td width="50%">{{datas.title}} <span v-if='datas.subtitle'>（{{datas.subtitle}}）</span></td>
                        <td width="20%">{{datas.update_time}}</td>
                        <td width="30%">
                            <a :href="'<?php echo $this->createUrl('templateTopic') ?>?tempid='+datas.id+''" class="btn btn-default btn-xs" role="button" target="_blank">题库管理</a>
                            <button type="button" class="btn btn-default btn-xs">复制模板</button>
                            <button type="button" class="btn btn-default btn-xs"  @click='addTem(datas.id)'>编辑信息</button>
                            <a :href="'<?php echo $this->createUrl('previewTemplate') ?>?surveyid='+datas.id+''" class="btn btn-default btn-xs" role="button" target="_blank">预览模板</a>
                        </td>
                    </tr>
                </tbody>
              </table>
              <nav aria-label="Page navigation" class="text-left"  v-if='data.length!=0'>
                <ul class="pagination">
                    <li v-if='currentPage>1'>
                        <a :href="'<?php echo $this->createUrl('template') ?>?page='+prev(currentPage)+''" aria-label="Previous">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                    <li v-else  class="disabled">
                        <a  aria-label="Previous">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                    <li v-for='(data,index) in totalPage' :class="{ active:index+1==currentPage }">
                        <a  :href="'<?php echo $this->createUrl('template') ?>?page='+plus(index)+''">{{index+1}}</a>
                    </li>
                    <li  v-if='currentPage<totalPage'  >
                        <a :href="'<?php echo $this->createUrl('template') ?>?page='+plus(currentPage)+''" aria-label="Next">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                    <li  v-else  class="disabled">
                        <a  aria-label="Next">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                </ul>
            </nav>
            </div>
            </div>
            <div class="modal fade bs-example-modal-lg" id="addTem" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true" data-backdrop="static">
                <div class="modal-dialog ">
                    <?php $form = $this->beginWidget('CActiveForm', array(
                        'id' => 'expense-forms',
                        'enableAjaxValidation' => false,
                        'action' => ($model->id) ? $this->createUrl('expenesAdd', array('expenesId' => $model->id)) : $this->createUrl('updateTemplate'),
                        'htmlOptions' => array('class' => 'J_ajaxForm', 'role' => 'form', 'enctype' => "multipart/form-data"),
                    )); ?>
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-hidden="true">
                            &times;
                        </button>
                            <h4 class="modal-title" id="myModalLabel">添加模板</h4>
                        </div>
                        <div class="modal-body">
                            <input type="text" :value="thisTempid" name='tempid' class="hidden">
                            <div class="form-horizontal">
                                <div class="form-group">
                                
                                    <label for="inputEmail3" class="col-sm-2 control-label">中文名称</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" name="WSurveyTemplate[title_cn]" :value='EditTmp.title_cn'>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="inputEmail3" class="col-sm-2 control-label">英文名称</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" name="WSurveyTemplate[title_en]" :value='EditTmp.title_en'>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="inputEmail3" class="col-sm-2 control-label">中文副标题</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" name="WSurveyTemplate[subtitle_cn]" :value='EditTmp.subtitle_cn'>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="inputEmail3" class="col-sm-2 control-label">英文副标题</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" name="WSurveyTemplate[subtitle_en]" :value='EditTmp.subtitle_en'>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
                            <button type="button" class="btn btn-primary J_ajax_submit_btn">添加</button>
                        </div>
                    </div>
                    <?php $this->endWidget(); ?>
                </div>
            </div>
        </div>
</div>
<script>
//预览模板 复制模板  题库管理选项列表 增加选项 编辑题目提交
    var templateList = <?php echo json_encode($templateList) ?>;
    var page = <?php echo $page ?>;
    var totalPages = <?php echo $totalPages ?>;
    console.log(templateList)
    var template = new Vue({
            el: "#template",
            data: {
                data: templateList,
                totalPage:totalPages,
                currentPage:page,
                EditTmp:'',
                thisTempid:'',
            },
            created: function() {},
            methods: {
                plus(index){
                    return Number(index)+1
                },
                prev(index){
                    return  Number(index)-1
                },
                addTem(tempid){
                    console.log(tempid)
                    this.thisTempid=tempid
                    $.ajax({
                        url: "<?php echo $this->createUrl('updateTemplate')?>",
                        type: 'post',
                        dataType: 'json',
                        data:{
                            tempid:tempid,
                        },
                        success:function(data){
                           console.log(data)
                            if(data.state=='success'){
                                template.EditTmp=data.data
                                $('#addTem').modal('show')
                            }else{
                                resultTip({
                                    error: 'warning',
                                    msg:data.message
                                });
                            }
                        },
                        error:function(data) {
                            console.log(data)
                            alert('请求错误')
                        }
                    })
                }
            }
        })
    function cbVisit(){
        setTimeout(function(){ 
            window.location.reload();
        },1000)
    }
</script>