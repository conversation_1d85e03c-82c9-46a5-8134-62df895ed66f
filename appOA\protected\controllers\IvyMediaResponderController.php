<?php

// to do:
// 1. 自动创建 media_yyyy 的表，标记给小s完成

class IvyMediaResponderController extends Controller
{
	public function actionweeklyPhoto(){
		set_time_limit( 300 );
		
		Yii::import('common.models.yunMedia.*');
		
		$params = json_decode( base64_decode($_POST['ext-param']), true );
		
		$yunMedia = new YunMedia;
		$yunMedia->id = $_POST['mkey'];
		$yunMedia->serverid = $params['serverid'];
		$yunMedia->type = 'photo';
		$yunMedia->year = $params['startyear'];
		$yunMedia->data = $_POST['ext-param'];
		$yunMedia->created = time();
		$yunMedia->save();
		
		Yii::import('common.models.portfolio.*');
		
		ChildMedia::setStartYear( $params['startyear'] );
		$media = new ChildMedia;
		$media->schoolid = $params['branchid'];
		$media->classid = $params['classid'];
		$media->yid = $params['yid'];
		$media->weeknum = $params['weeknumber'];
		$media->type = 'photo';
		$media->filename = $params['filename'];
		$media->tag = '';
		$media->server = $params['serverid'];
		$media->timestamp = time();
		$media->uid = $params['uid'];
		if ( $media->save() ){
			$yunMedia->media_id = $media->id;
			$yunMedia->save();
		}else{
			
			//Yii::app()->cache->set('test001', time() . ' ' . print_r($media->getErrors(), true));
		}
		
		echo 'ok';
		Yii::app()->end();	
	}
	
	public function actionShowtest(){
		$result = Yii::app()->cache->get('test001');
		echo $result;
	}

	public function actionModify(){
		
		extract($_POST);
		
		$serverConfig = CommonUtils::getConfig('common.config.CfgMediaServer', '.local.php');
		if(is_null($serverConfig)){
			$serverConfig = CommonUtils::getConfig('common.config.CfgMediaServer', '.php');
		}
	
		$mediaServer = $serverConfig['servers'][ $sid ];
		
		if($pid){
			$myCode = md5(sprintf('%s&%s&%s', Yii::app()->user->getId(), $pid, $mediaServer['securityKey']));
		}elseif($mkey){
			$myCode = md5(sprintf('%s&%s&%s', Yii::app()->user->getId(), $mkey, $mediaServer['securityKey']));
		}
		
		
		//验证签名
		if( $myCode == $securityCode ){
			
			//从媒体库过来的请求
			if($pid && $year){
				Yii::import('common.models.portfolio.*');
				ChildMedia::setStartYear($year);
				$localMedia = ChildMedia::model()->findByPk($pid);
				$subPath = sprintf('s_%s/c_%s/w_%s/', $localMedia->schoolid, $localMedia->classid, $localMedia->weeknum);
				$mkey = md5(sprintf('%s-%s-%s-%s', $subPath, $localMedia->filename , $localMedia->server, $localMedia->uid));
			}else{
			//即刻上传之后的操作，此时没有pid
			}
			
			switch($op){
				case 'rotate':
					$url = $mediaServer['modifyUrl'];
					$params = array(
						'op' => $op,
						'mkey' => $mkey,
						'uid' => Yii::app()->user->getId(),
						'sid' => $sid,
						'securityCode' => md5(sprintf('%s&%s&%s&%s', Yii::app()->user->getId(), $sid, $mkey, $mediaServer['securityKey']))
					);
					$options = array(
						'http' => array(
							'method' => 'POST',
							'content' => http_build_query($params,'','&')
						)
					);
					
					$context = stream_context_create( $options );
					$result = file_get_contents( $url, false, $context);
					
					echo $result;
					Yii::app()->end();
				break;
				
				// 注意上传之后的即可删除跟从媒体库的删除操作流程不一样，代码也是两套
				case 'delete':
					Yii::import('common.models.yunMedia.*');
					$mediaItem = YunMedia::model()->findByPk($mkey);
					if(!is_null($mediaItem) && $mediaItem->year && $mediaItem->media_id){
						Yii::import('common.models.portfolio.*');
						ChildMedia::setStartYear($mediaItem->year);
						$localMedia = ChildMedia::model()->findByPk($mediaItem->media_id);
						if(!is_null($localMedia))
							$localMedia->delete();
						$mediaItem->delflag = 1;
						$mediaItem->save();
					}
					echo CJSON::encode(array('status'=>'success','op'=>$op));
					Yii::app()->end();
				break;
			}
		}
		
		echo CJSON::encode(array('status'=>'fail', 'message'=>'System Error'));
		Yii::app()->end();
	}
	
	public function actionWeeklyAssign(){
		/* print_r($_POST);
		[mediaid] => 220
		[TagLD] => Array
			(
				[0] => 12
				[1] => 26
				[2] => 14
			)
	
		[TagFree] => efdf dfdsf sdfdf sdfdfdf 
		[TagChild] => Array
			(
				[0] => 3878
				[1] => 5410
			)
	
		[LinkChild] => Array
			(
				[0] => 3878
				[1] => 5376
				[2] => 5410
				[3] => 5547
			)
	
		[classid] => 308
		[weeknum] => 6
		[startyear] => 2013
		[securityCode] => bfd3313ff55ecee9d77a166c103c95da
		*/
		extract($_POST);
		
		$myCode = md5(sprintf('%s&%s&%s&%s&%s', $classid,$weeknum,$startyear,Yii::app()->user->getId(),$this->securityKey));
		if($myCode == $securityCode && $startyear && $mediaid && $weeknum){
			Yii::import('common.models.portfolio.*');
			ChildMedia::setStartYear($startyear);
			ChildMediaLinks::setStartYear($startyear);
		
			$media = ChildMedia::model()->with(array('weeklyLinks'=>array(
				'condition' => 'category="week" AND weeklyLinks.classid=:classid AND weeklyLinks.weeknum=:weeknum AND weeklyLinks.pid=:mediaid',
				'params' => array(
					':classid' => $classid,
					':weeknum' => $weeknum,
					':mediaid' => $mediaid,
				),
				'together'=>false
			)))->findByPk($mediaid);
			
			$tags = array();
			if(!is_null($media)){
				if($TagLD && count($TagLD)){
					foreach($TagLD as $_ld){
						$tags[] = sprintf('sl_%d_e', $_ld);
					}
				}
				if($TagChild && count($TagChild)){
					foreach($TagChild as $_cid){
						$tags[] = sprintf('sc%d_%d_e', $classid, $_cid);
					}
				}
				if($TagFree && $TagFree != ""){
					$tags[] = sprintf('sf_%s_e', $TagFree);
				}
			}
			
			$media->tag = implode(' ', $tags);
			$media->timestamp = time();
			$media->uid = Yii::app()->user->getId();
			$media->save();
					
			//为了保存文字信息，不采取全部删除再添加的方式；系统逐个处理
			$linkToChilds = array();
			if($LinkChild && count($LinkChild)){
				foreach($LinkChild as $_cid){
					$linkToChilds[$_cid] = $_cid;
				}
			}
			
			foreach($media->weeklyLinks as $linkObj){
				if(!in_array($linkObj->childid, $linkToChilds)){
					$linkObj->delete();
				}else{
					unset($linkToChilds[$linkObj->childid]);
				}
			}
			
			if(count($linkToChilds)){
				foreach($linkToChilds as $cid){
					$link = new ChildMediaLinks;
					$link->category = 'week';
					$link->classid = $classid;
					$link->childid = $cid;
					$link->yid = $yid;
					$link->weeknum = $weeknum;
					$link->itemid = 0;
					$link->pid = $mediaid;
					$link->weight = 1;
					$link->updated_timestamp = time();
					$link->userid = Yii::app()->user->getId();
					if(!$link->save()){
						print_r($link->getErrors());
					}
				}
			}

			$media = ChildMedia::model()->with(array('weeklyLinks'=>array(
				'condition' => 'category="week" AND weeklyLinks.classid=:classid AND weeklyLinks.weeknum=:weeknum AND weeklyLinks.pid=:mediaid',
				'params' => array(
					':classid' => $classid,
					':weeknum' => $weeknum,
					':mediaid' => $mediaid,
				),
				'together'=>false
			)))->findByPk($mediaid);

			$data['mediaList'] = $this->getFormatedData($media);
			$data['mediaid'] = $mediaid;

			echo CJSON::encode(array('status'=>'success','data'=>$data));
		}else{
			echo CJSON::encode(array('status'=>'failed','message'=>'数字签名错误'));
		}
		
	}
	
	public function actionWeeklyAssignStats(){
		extract($_POST);
		$mycode = md5(sprintf('%s&%s&%s&%s&%s',$startyear, $weeknum, $classid, Yii::app()->user->getId(),$this->securityKey));
		
		if($mycode == $securityCode){
			
		
			$tmpData = $this->getMediaByChild($startyear, $classid, $weeknum, $childid);

			$result['status'] = 'success';
			$result['data']['byChild'] = $tmpData['childLinks'];
			$result['data']['medias'] = $tmpData['medias'];
			echo CJSON::encode($result);
			Yii::app()->end();
		}else{
			echo CJSON::encode(array('status'=>'failed','message'=>'数字签名错误'));
			Yii::app()->end();
		}
		
	}
	
	private function getMediaByChild($startyear, $classid, $weeknum, $childid){
		$serverConfigs = OA::getMediaServerConfig();
		
		Yii::import('common.models.portfolio.*');
		
		ChildMedia::setStartYear($startyear);
		ChildMediaLinks::setStartYear($startyear);
		
		$result = array();
		
		$criteria = new CDbCriteria();
		$criteria->compare('t.classid', $classid);
		$criteria->compare('t.weeknum', $weeknum);
		if($childid)
		$criteria->compare('t.childid', $childid);
		
		$criteria->compare('t.category', 'week'); //实际上有week和snotes两种类型；后者用于IA的个人报告
		
		$criteria->order = 't.weight ASC, t.id asc';
		$childLinksObjs = ChildMediaLinks::model()->with('photoInfo')->findAll($criteria);
		
		foreach($childLinksObjs as $linkObj){
			$result['childLinks'][$linkObj->childid][] = array(
				'pid' => $linkObj->pid,
				'id' => $linkObj->id,
				'caption' => CHtml::encode($linkObj->content),
			);
			if(!isset($result['medias'][$linkObj->pid]) && !is_null($linkObj->photoInfo)){
				$result['medias'][$linkObj->pid] = $serverConfigs['servers'][$linkObj->photoInfo->server]['url'] . $linkObj->photoInfo->getSubPath() .  $linkObj->photoInfo->filename;
			}
		}
		return $result;
	}
	
	private function getFormatedData($medias, $classid, $startyear){
		if(!is_array($medias)){
			$medias = array($medias);
		}
		
		$serverConfigs = OA::getMediaServerConfig();
		$mediaList = array();
		foreach($medias as $media){
			$links = array();
			if(count($media->weeklyLinks)){
				foreach($media->weeklyLinks as $linkObj){
					$links[] = $linkObj->childid;
				}
			}
			
			$tags = array();
			if($media->tag != ""){
				preg_match_all('/sc'.$classid.'_(\d+)_e/', $media->tag, $out1);
				preg_match_all('/sl_(\d+)_e/', $media->tag, $out2);
				preg_match_all('/sf_([^_]*)_e/', $media->tag, $out3);
				
				$tags['childid'] = ($out1 && isset($out1[1])) ? $out1[1]: array();
				$tags['learndomain'] = ($out2 && isset($out2[1])) ? $out2[1]: array();
				$freeword = ($out3 && isset($out3[1])) ? $out3[1][0]: '';
				$tags['freeword'] = CHtml::encode($freeword);
			}
			
						
			$subPath = $media->getSubPath();
			$mediaList[$media->id] = array(
				'pid' => $media->id,
				'sid' => $media->server,
				'year' => $startyear,
				'securityCode' => md5(sprintf('%s&%s&%s', Yii::app()->user->getId(), $media->id, $serverConfigs['servers'][$media->server]['securityKey'])),
				'thumb' => $serverConfigs['servers'][$media->server]['url'] . $media->getSubPath() . 'thumbs/' . $media->filename,
				'links' => $links,
				'tags' => $tags
			);
		}
		return $mediaList;
	}
	
	public function actionWeeklyMediaDetail(){
		/*	
		Array
		(
			[chilid] => 3878
			[linkid] => Array
				(
					[220] => 259
					[224] => 262
					[266] => 265
				)
		
			[mediaCaption] => Array
				(
					[220] => caption text
					[224] => caption text
					[266] => 
				)
		
			[forceCaption] => Array
				(
					[220] => 220
				)
		
			[mediaLinkRemove] => Array
				(
					[266] => on
				)
		
			[classid] => 308
			[weeknum] => 6
			[startyear] => 2013
			[securityCode] => bfd3313ff55ecee9d77a166c103c95da
		)
		*/
		extract($_POST);
		$myCode = md5(sprintf('%s&%s&%s&%s&%s', $classid,$weeknum,$startyear,Yii::app()->user->getId(),$this->securityKey));
		if(($myCode == $securityCode) && $startyear && $childid && $classid && $weeknum ){
			Yii::import('common.models.portfolio.*');
			ChildMedia::setStartYear($startyear);
			ChildMediaLinks::setStartYear($startyear);
		
			//处理删除
			if($mediaLinkRemove && count($mediaLinkRemove)){
				foreach(array_keys($mediaLinkRemove) as $pid){
					unset($mediaCaption[$pid]);
					unset($forceCaption[$pid]);
					if(isset($linkid[$pid]))
						$removeLinkIds[] = $linkid[$pid];
				}
				ChildMediaLinks::model()->deleteAllByAttributes(array('id'=>$removeLinkIds));
			}
			
			//处理媒体注释
			$hasForceItem = false;
			if($mediaCaption && count($mediaCaption)){
				foreach($mediaCaption as $pid=>$caption){
					if($forceCaption && isset($forceCaption[$pid]) && isset($linkid[$pid])){
						ChildMediaLinks::model()->updateAll(
							array(
								'content'=>$mediaCaption[$pid],
								'updated_timestamp'=>time(),
								'userid'=>Yii::app()->user->getId()
							),
							'category=:category AND classid=:classid AND weeknum=:weeknum AND pid=:pid',
							array(
								':category' => 'week',
								':classid'  => $classid,
								':weeknum'  => $weeknum,
								':pid'      => $pid
							)
						);
						$hasForceItem = true;
					}else{
						ChildMediaLinks::model()->updateByPk($linkid[$pid],
							array(
								'content'=>$mediaCaption[$pid],
								'updated_timestamp'=>time(),
								'userid'=>Yii::app()->user->getId()
							));
					}
				}
			}
			
			$result['state'] = 'success';
			
			$findbyChild = ( $hasForceItem )? 0: $childid;
			$tmpData = $this->getMediaByChild($startyear, $classid, $weeknum, $findbyChild);
			$result['data']['byChild'] = $tmpData['childLinks'];
			$result['data']['bySingleChild'] = $findbyChild;
			
			if($mediaLinkRemove && count($mediaLinkRemove)){
				$criteria = new CDbCriteria;
				$criteria->compare('t.classid', $classid);
				$criteria->compare('t.weeknum', $weeknum);
				$criteria->compare('t.id', array_keys($mediaLinkRemove));
				$medias = ChildMedia::model()->with(array('weeklyLinks'=>array(
					'condition' => 'category="week" AND weeklyLinks.classid=:classid AND weeklyLinks.weeknum=:weeknum',
					'params' => array(
						':classid' => $classid,
						':weeknum' => $weeknum,
						//':childid' => $childid,
					),
					//'together'=>false
				)))->findAll($criteria);
				
				$mediaList = $this->getFormatedData($medias, $classid, $startyear);
			}
			
			$result['data']['mediaList'] = $mediaList;
			$result['data']['childid'] = $childid;
			$result['callback'] = 'postUpdatebyChild';
			$result['message'] = 'Data Saved.';
			
			echo CJSON::encode($result);
		}else{
			echo CJSON::encode(array('status'=>'failed','message'=>'数字签名错误'));
		}
		Yii::app()->end();
		
	}
	
	public function actionSetAvatar(){
		extract($_POST);
		$myCode = md5(sprintf('%s&%s&%s&%s&%s', $classid,$weeknum, $startyear,Yii::app()->user->getId(),$this->securityKey));
		
		if( ( $myCode == $securityCode ) && $avatarChildId){
			$child = ChildProfileBasic::model()->findByPk($avatarChildId);
			if(is_null($child)){
				echo CJSON::encode(array(
					'state' => 'failed',
					'message' => 'No child specified'
				));
				Yii::app()->end();			
			}
			$serverConfig = CommonUtils::getConfig('common.config.CfgMediaServer', '.local.php');
			if(is_null($serverConfig)){
				$serverConfig = CommonUtils::getConfig('common.config.CfgMediaServer', '.php');
			}
			
			$mediaServer = $serverConfig['servers'][ $avatar['sid'] ];
			$cfgs = OA::LoadConfig('CfgPhoto');
			$params = $cfgs['childPhoto'];
			$subPath = $params['subDir'] . '/';
			$subThumbPath = $params['subDir'] . '/thumbs/';
			$url = $mediaServer['cropResizeUrl'];
			
			//开始请求远程服务器裁图
			$postParams = array(
				'uid' => Yii::app()->user->getId(),
				'avatar' => $avatar,
				'photoParams' => $params,
				'securityCode' => md5(sprintf('%s&%s&%s&%s', Yii::app()->user->getId(), $avatar['sid'], $avatar['source'], $mediaServer['securityKey']))
			);
			$options = array(
				'http' => array(
					'method' => 'POST',
					'content' => http_build_query($postParams,'','&')
				)
			);
			
			$context = stream_context_create( $options );
			$result = file_get_contents( $url, false, $context);
			//远程请求结束
			
			$returnData = CJSON::decode($result);
			
			if($returnData['state'] == 'success' && isset($returnData['data']['image'])){
				
				$psize = isset($params['sizes']) ? $params['sizes'] : $cfgs['defaultSizes'];
				$imgUrl = $returnData['data']['image'];
				$imgUrl = str_replace(array(" "),"%20",$imgUrl);
				
				//preg_match("/(\.\w{3}$)/", $imgUrl, $matches);
				//if(is_array($matches) && isset($matches[1])){
				//	$ext = strtolower($matches[1]);
				//}else{
				//	$ext = '.jpg';
				//}
				
				$fileName = $params['filePrefix'] . uniqid() . $returnData['data']['ext'];
				
				$opts  = array('http' =>array(
						'method' => 'GET',
						'timeout'=> '60'
					));
				try{
					$imageString = file_get_contents( $imgUrl, false, stream_context_create($opts) );
				}catch(Exception $exc){
					echo CJSON::encode(array(
						'state' => 'failed',
						'message' => 'Server Error',
						'remoteData' => $returnData
					));
					Yii::app()->end();
				}

				$subPath = $params['subDir'] . '/';
				$subThumbPath = $params['subDir'] . '/thumbs/';
				
				$destNormal = $subPath . $fileName;
				$destThumb = $subThumbPath . $fileName;
				
				if($imageString){
					file_put_contents($destNormal, $imageString);
				}else{
					echo CJSON::encode(array(
						'state' => 'failed',
						'message' => 'Server Error 2',
						'remoteData' => $returnData
					));
					Yii::app()->end();			
				}
				
				Yii::import('application.extensions.image.Image');
				$image = new Image($destNormal);
				$image->resize($psize['thumbW'], null)->save($destThumb);
				
				$oldPhoto = $child->photo;
				$child->photo = $fileName;
				$child->save();
				if($oldPhoto != 'blank.gif'){
					@unlink($subPath . $oldPhoto);
					@unlink($subThumbPath . $oldPhoto);
				}
				
				echo CJSON::encode(array('state'=>'success','callback'=>'postAvatarUpload', 'message'=>Yii::t('global','Data Saved.'),'data'=>array('childid'=>$avatarChildId,'filename'=>$fileName,'photo'=>$params['uploadUrl'].'/thumbs/'.$fileName)));
			
			}
			
		}else{
			echo CJSON::encode(array('state'=>'failed'));
		}
	}
	
	public function actionMediaSearch(){
		//extract($_POST);
		extract($_GET);
		$myCode = md5(sprintf('%s&%s&%s&%s&%s', $classid,$weeknum, $startyear,Yii::app()->user->getId(),$this->securityKey));
		
		$serverConfigs = OA::getMediaServerConfig();
		
		if( $myCode == $securityCode ){
			$type = 'photo';
			
			$tagSearch = array();
			if(intval($MediaSearch['ldid'])){
				$tagSearch[] = '%' . sprintf('sl_%d_e', intval($MediaSearch['ldid'])) . '%';
			}
			if(intval($MediaSearch['childid'])){
				$tagSearch[] = '%' . sprintf('sc%d_%d_e', $classid, intval($MediaSearch['childid'])) . '%';
			}
			$freeTag = trim( $MediaSearch['freetag'] );
			if(!empty($freeTag)){
				$tagSearch[] = '%' . $freeTag . '%';
			}
			
			$mediaCommandStat = Yii::app()->db->createCommand();
			$mediaCommandData = Yii::app()->db->createCommand();
			
			$mediaCommandStat->select('count(*) as total');
			$mediaCommandData->select('*');
				
			$mediaCommandStat->from( sprintf('ivy_child_media_%s', $startyear))
				//->where('classid=:classid AND type=:type', array(':classid'=>$classid, ':type'=>$type));
				->where( array('and', 'classid='.$classid, 'type="' . $type . '"') );
				
			$mediaCommandData->from( sprintf('ivy_child_media_%s', $startyear))
				->where('classid=:classid AND type=:type', array(':classid'=>$classid, ':type'=>$type));
				
			if(count($tagSearch)){
				$mediaCommandStat->andWhere(array('LIKE','tag', $tagSearch));
				$mediaCommandData->andWhere(array('LIKE','tag', $tagSearch));
			}
			if(intval($MediaSearch['weeknum'])){
				$mediaCommandStat->andWhere('weeknum=:weeknum', array(':weeknum'=>intval($MediaSearch['weeknum'])));
				$mediaCommandData->andWhere('weeknum=:weeknum', array(':weeknum'=>intval($MediaSearch['weeknum'])));
			}
			$sql = $mediaCommandStat->text;
			$countRow = $mediaCommandStat->queryRow();
			$count = array_shift($countRow);
			
			$pages = new CPagination($count);
			$pages->pageSize = 18;
			$page = isset($page) ? $page : 1;
			
			$mediaCommandData->limit( $pages->pageSize );
			$mediaCommandData->offset( $pages->pageSize * ($page - 1) );
			$sql = $mediaCommandData->text;

			$mediaRawData = $mediaCommandData->queryAll();
			
			foreach($mediaRawData as $media){
				$subPath = sprintf('s_%s/c_%s/w_%s/', $media['schoolid'], $media['classid'], $media['weeknum'] );
				$mediaData[] = array(
					'thumb' => $serverConfigs['servers'][$media['server']]['url'] . $subPath . 'thumbs/' . $media['filename'],
					'normal' => $serverConfigs['servers'][$media['server']]['url'] . $subPath . $media['filename'],
					'mediaid' => $media['id'],
					'sid' => $media['server'],
					'startyear' => $startyear,
				);
			}
			
			echo CJSON::encode(
				array('state'=>'success',
					'tagSearch'=>$tagSearch,
					'message'=>'',
					'data'=>array(
						'mediaData'=>$mediaData,
						'pages'=>$this->widget('CLinkPager', array('currentPage'=> $page - 1, 'pages' => $pages, 'id'=>'media-pager'), true),
					),
					'callback'=>'postMediaSearch'
					));
		}else{
			echo CJSON::encode(array('state'=>'failed'));
		}
		
		
	}
}