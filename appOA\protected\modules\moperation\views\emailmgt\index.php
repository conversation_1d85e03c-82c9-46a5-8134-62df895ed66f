<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site','HQ Operations'), array('/moperation'))?></li>
        <li><?php echo CHtml::link(Yii::t('site','Marketing'), array('/moperation'))?></li>
        <li class="active"><?php echo Yii::t('','邮件地址管理') ?></li>
    </ol>
    <div class="row">
        <div class="col-md-6">
            <?php
            $this->widget('ext.ivyCGridView.BsCGridView', array(
                'id'=>'email-mgt-grid',
                'dataProvider'=>$model->search(),
				'colgroups'=>array(
                    array(
                        "colwidth"=>array(300,300),
                    )
                ),
				'filter'=>$model,
                'columns'=>array(
                    'email',
                    'random_code',
                    array(
                        'name' => 'status',
                        'filter' => false,
                        'type'=>'raw',
                        'value' => array($this, 'getButton'),
                    ),
                ),
            )); ?>
        </div>
    </div>
</div>

<script>
function updateState(email, type)
{
	$.ajax({
		type: 'post',
		dataType: 'json',
		url: "<?php echo $this->createUrl('updateState');?>",
		data: {
			email: email,
			type: type
		},
		success: function( data ) {
			if(data.state == 'success'){
				$.fn.yiiGridView.update('email-mgt-grid');
				resultTip({msg:data.message});
			}
			else{
				alert(data.message);
			}
		}
	});
}
</script>
