<?php

class AsaController extends ProtectedController
{
    public $actionAccessAuths = array(
        'index'            => 'o_A_Access',
        'query'            => 'o_A_Access',
        'viewItem'         => 'o_A_Access',
        'export'           => 'o_A_Access',
        'update'           => 'o_A_Access',
        'pay'              => 'tSuperAdmin',
    );

    public $childsName = array();
    public $schoolsName = array();
    public $courseNamewa = array();
    public $usersName = array();

    public function init(){
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'main';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site','Finance Mgt');
        Yii::import('common.models.asainvoice.*');

        $cs = Yii::app()->clientScript;
        $cs->registerCoreScript('jquery.ui');
        $cs->registerCssFile($cs->getCoreScriptUrl().'/jui/css/base/jquery-ui.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/vue2.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/jquery.printThis.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/xlsx.full.min.js');
    }

	public function actionIndex()
	{
        $this->schoolsName = $this->getAllBranch();
        $crit = new CDbCriteria;
        $crit->compare('status', array(AsaCashHandover::STATUS_SUBMITTED));
        $cashHandoverl = new CActiveDataProvider('AsaCashHandover', array(
            'criteria' => $crit,
            'sort' => array(
                'defaultOrder' => 'created DESC',
            ),
            'pagination'=>array(
                'pageSize'=>50,
            ),
        ));

        $users = array();
        foreach($cashHandoverl->getData() as $child){
            $users[$child->creater] = $child->creater;
        }
        if($users){
            $usersModel = User::model()->findAllByPk($users);

            foreach($usersModel as $user){
                $this->usersName[$user->uid] = $user->getName();
            }
        }

        $this->render('index', array(
            'cashHandoverl' => $cashHandoverl,
        ));
	}

    //审核列表
    public function actionExpense()
	{
        $this->schoolsName = $this->getAllBranch();
        $crit = new CDbCriteria;
        $crit->compare('status', array(AsaExpense::STATS_REFER));
        $expenseModel = new CActiveDataProvider('AsaExpense', array(
            'criteria' => $crit,
            'sort' => array(
                'defaultOrder' => 'created DESC',
            ),
            'pagination'=>array(
                'pageSize'=>50,
            ),
        ));

        $users = array();
        foreach($expenseModel->getData() as $child){
            $users[$child->expense_uid] = $child->expense_uid;
        }
        if($users){
            $usersModel = User::model()->findAllByPk($users);

            foreach($usersModel as $user){
                $this->usersName[$user->uid] = $user->getName();
            }
        }
        $this->schoolsName = $this->getAllBranch();

        $this->render('expense', array(
            'expenseModel' => $expenseModel,
        ));
	}

    public function actionDownloadsHand()
    {
        ob_end_clean();
        $id = Yii::app()->request->getParam('id', 0);
        $model= AsaCashHandover::model()->findByPk($id);
        $fileNames = $model->bank_ref_url;
        $file_dir = Yii::app()->params['xoopsVarPath'] . '/asa/handover/';
        $fileres = file_get_contents($file_dir . $fileNames);
        if($fileres){
            header('Content-type: image/jpeg');
            echo $fileres;
        }else{
            $oss = CommonUtils::initOSS('private');
            $filePath = 'asa/handover/' . $fileNames;
            $url = $oss->get_sign_url($filePath);
            Yii::app()->request->redirect($url);
        }

    }

    //审核模态框
    public  function actionShowExpense()
    {
        $expenseId = Yii::app()->request->getParam('expenseId','');
        if($expenseId){
            $model = AsaExpense::model()->findByPk($expenseId);
            $branchObj = Branch::model()->findByPk($model->branch);
            if(Yii::app()->request->isPostRequest){
                $model->attributes = $_POST['AsaExpense'];
                $model->updated = time();
                if(!$model->save()){
                    $error = current($model->getErrors());
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', Yii::t('message', $error[0]));
                    $this->showMessage();
                }
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message','success'));
                $this->addMessage('callback', 'cbUpdateExpense');
                $this->showMessage();
            }

            $typeList = CommonUtils::LoadConfig('CfgASA');
            $bank = array();
            foreach($typeList['bank'] as $k=>$item){
                $bank[$k] = Yii::app()->language == 'zh_cn' ? $item['cn'] : $item['en'];
            }

            $this->renderpartial('showExpense', array(
                'model' => $model,
                'branchObj' => $branchObj,
                'bank' => $bank,
                ));
        }else{
            $this->addMessage('state', 'fail');
            $this->addMessage('message', Yii::t('message',"参数错误"));
            $this->showMessage();
        }
    }

    public function actionDownloadsExpense()
    {
        ob_end_clean();
        $fileName = Yii::app()->request->getParam('fileName', "");
        $extension = substr(strrchr($fileName, '.'), 1);
        $file_dir = Yii::app()->params['xoopsVarPath'] . '/asa/expense/';

        $fileres = file_get_contents($file_dir . $fileName);
        if($fileres){
            if($extension == "pdf"){
                header('Content-type: application/pdf');
            }else{
                header('Content-type: image/jpeg');
            }
            echo $fileres;
        }else{
            $oss = CommonUtils::initOSS('private');
            $filePath = 'asa/expense/' . $fileName;
            $url = $oss->get_sign_url($filePath);
            Yii::app()->request->redirect($url);
        }
    }

    //审核成功
    public function actionExpenseHistory()
    {
        $username = Yii::app()->request->getParam('username','');
        $branch = Yii::app()->request->getParam('branch','');
        $starTime = Yii::app()->request->getParam('starTime','');
        $starEnd = Yii::app()->request->getParam('starEnd','');
        $type = Yii::app()->request->getParam('type','');
        $this->schoolsName = $this->getAllBranch();

        $userObj = array();
        if($username){
            $crit = new CDbCriteria;
            if($username){$crit->addCondition("concat_ws(',',name,uname) like '%{$username}%' ");}
            $crit->index = "uid";
            $userObj = User::model()->findAll($crit);
        }

        $crits = new CDbCriteria;
        $crits->compare('status', array(AsaExpense::STATS_PAST));
        if($userObj){$crits->compare('expense_uid', array_keys($userObj));}
        if($branch){$crits->compare('branch', $branch);}
        if($type){$crits->compare('type', $type);}
        if($starTime){
            $starTime = strtotime($starTime);
            $crits->compare('created', ">={$starTime}");}
        if($starEnd){
            $starEnd = strtotime($starEnd);
            $crits->compare('created', "<={$starEnd}");
        }
        $expenseModel = new CActiveDataProvider('AsaExpense', array(
            'criteria' => $crits,
            'sort' => array(
                'defaultOrder' => 'created DESC',
            ),
            'pagination'=>array(
                'pageSize'=>50,
            ),
        ));

        $users = array();
        foreach($expenseModel->getData() as $child){
            $users[$child->expense_uid] = $child->expense_uid;
        }
        if($users){
            $usersModel = User::model()->findAllByPk($users);

            foreach($usersModel as $user){
                $this->usersName[$user->uid] = $user->getName();
            }
        }
        $school = array();
        foreach($this->schoolsName as $k=>$item){

            $school[$k] = $item['title'];
        }

        $this->schoolsName = $this->getAllBranch();
        $this->render('expenseHistory', array(
            'expenseModel' => $expenseModel,
            'schoolsName' => $school,
        ));
    }

    public function actionUpdateHandover()
    {
        $cashHandoverId = Yii::app()->request->getParam('id','');
        $cashHandover = Yii::app()->request->getParam('AsaCashHandover','');
        $model = AsaCashHandover::model()->findByPk($cashHandoverId);
        if(Yii::app()->request->isPostRequest){
            if(!$cashHandover['status']){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('message', '请选择状态'));
                $this->showMessage();
            }
            $model->attributes = $_POST['AsaCashHandover'];
            $model->received = time();
            $model->receiver = Yii::app()->user->id;
            if($model->save()){
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message','success'));
                $this->addMessage('callback', 'cbUpdateHandover');
                $this->showMessage();
            }else{
                $error = current($model->getErrors());
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('message', $error[0]));
                $this->showMessage();
            }

        }

        $invoicesId = explode(',', $model->invoice_ids);
        $crit = new CDbCriteria;
        $crit->compare('id', $invoicesId);
        $invoices = AsaInvoice::model()->findAll($crit);

        $uidName = User::model()->findByPk($model->creater);
        $this->renderpartial('_updateHandover', array(
            'model' => $model,
            'uidName' => $uidName,
            'invoices' => $invoices,
        ));
    }

    public function actionHandover()
    {
        $this->schoolsName = $this->getAllBranch();
        $crit = new CDbCriteria;
        $crit->compare('status', AsaCashHandover::STATUS_CONFIRMED);
        $cashHandoverl = new CActiveDataProvider('AsaCashHandover', array(
            'criteria' => $crit,
            'sort' => array(
                'defaultOrder' => 'created DESC',
            ),
            'pagination'=>array(
                'pageSize'=>50,
            ),
        ));
        $users = array();
        foreach($cashHandoverl->getData() as $child){
            $users[$child->creater] = $child->creater;
        }
        if($users){
            $usersModel = User::model()->findAllByPk($users);

            foreach($usersModel as $user){
                $this->usersName[$user->uid] = $user->getName();
            }
        }


        $this->render('handover', array(
            'cashHandoverl' => $cashHandoverl,
        ));
    }

    public function actionShowHandover()
    {
        $cashHandoverId = Yii::app()->request->getParam('id','');
        $status = AsaCashHandover::getConfig();
        if($cashHandoverId){
            $model = AsaCashHandover::model()->findByPk($cashHandoverId);
            $crit = new CDbCriteria;
            $crit->compare('uid', array($model->creater,$model->receiver));
            $userName = User::model()->findAll($crit);
            $userNames = array();
            foreach($userName as $k=>$_name){
                $userNames[$_name->uid] = $_name->getName();
            }

            $invoicesId = explode(',', $model->invoice_ids);
            $crit = new CDbCriteria;
            $crit->compare('id', $invoicesId);
            $invoices = AsaInvoice::model()->findAll($crit);

            $this->renderpartial('_showHandover', array(
                'model' => $model,
                'status' => $status,
                'userNames' => $userNames,
                'invoices' => $invoices,
            ));
        }
    }

    public function getMenu()
    {
        $mainMenu = array(
            array('label'=>Yii::t('','现金交接'), 'url'=>array("/mfinance/asa/index")),
            array('label'=>Yii::t('','退费管理'), 'url'=>array("/mfinance/asa/refund")),
            array('label'=>Yii::t('','报销及付款'), 'url'=>array("/mfinance/asa/expense")),
            array('label'=>Yii::t('','课程收入'), 'url'=>array("/mfinance/asa/actualAmount")),
            array('label'=>Yii::t('','考勤汇总'), 'url'=>array("/mfinance/asa/report")),
            array('label'=>Yii::t('','其他收入'), 'url'=>array("/mfinance/asa/otherAmount")),
        );
        return $mainMenu;
    }

    public function AttendMenu()
    {
        $action = Yii::app()->urlManager->parseUrl(Yii::app()->request);
        $mainMenu = array(
            array('label'=>Yii::t('','现金交接'), 'url'=>array("/mfinance/asa/index")),
            array('label'=>Yii::t('','交接历史'), 'url'=>array("/mfinance/asa/handover"), 'active'=>in_array($action, array('mfinance/asa/handover'))),
        );
        return $mainMenu;
    }

    public function ExpenseMen()
    {
        $action = Yii::app()->urlManager->parseUrl(Yii::app()->request);
        $mainMenu = array(
            array('label'=>Yii::t('','审核中'), 'url'=>array("/mfinance/asa/expense")),
            array('label'=>Yii::t('','审核成功'), 'url'=>array("/mfinance/asa/expenseHistory"), 'active'=>in_array($action, array('mfinance/asa/expenseHistory'))),
        );
        return $mainMenu;
    }

    public function RefundMenu()
    {
        $mainMenu = array(
            array('label'=>Yii::t('','退费管理'), 'url'=>array("/mfinance/asa/refund")),
            array('label'=>Yii::t('','退费历史'), 'url'=>array("/mfinance/asa/refunds")),
        );
        return $mainMenu;
    }

    public function getButton($data)
    {
        if($data->status == AsaCashHandover::STATUS_SUBMITTED){
            echo CHtml::link('提交', array('updateHandover', 'id' => $data->id), array('class' => 'J_modal btn btn-xs btn-info'));
        }else{
            echo CHtml::link('查看', array('showHandover', 'id' => $data->id), array('class' => 'J_modal btn btn-xs btn-info'));
        }
    }

    public  function getCreate($data)
    {
        echo $this->usersName[$data->creater];
    }

    public function getExpenseButton($data)
    {
        $present = '查看';
        if($data->status == AsaExpense::STATS_REFER){
            $present = '提交';
        }
        echo CHtml::link($present, array('showExpense', 'expenseId' => $data->id), array('class' => 'J_modal btn btn-xs btn-info'));
    }

    public function getSchoolName($data)
    {
        return $this->schoolsName[$data->site_id]['title'];
    }

    public function actionRefund(){
        $this->schoolsName = $this->getAllBranch();
        $config = CommonUtils::LoadConfig('CfgASA');
        $status = $config['status'];

        $dsSchoolList = CommonUtils::dsSchoolList();

        //获取已提交数据
        $crit = new CDbCriteria;
        $crit->compare('t.status',1);
        $crit->compare('t.refund_method', '<>1');
        $crit->addNotInCondition('site_id', $dsSchoolList);
        $submitted = new CActiveDataProvider('AsaRefund', array(
            'criteria' => $crit,
            'sort' => array(
                'defaultOrder' => 'updated DESC',
            ),
            'pagination'=>array(
                'pageSize'=>10,
            ),
        ));

        //获取已提交数据
        $crit = new CDbCriteria;
        $crit->compare('t.status',1);
        $crit->compare('t.refund_method', '<>1');
        $crit->compare('site_id', $dsSchoolList);
        $submittedDs = new CActiveDataProvider('AsaRefund', array(
            'criteria' => $crit,
            'sort' => array(
                'defaultOrder' => 'updated DESC',
            ),
            'pagination'=>array(
                'pageSize'=>10,
            ),
        ));

        $childId = array();
        $courseIds = array();
        foreach($submitted->getData() as $child){
            $childId[$child->childid] = $child->childid;
            $courseIds[$child->course_id] = $child->course_id;
        }
        foreach($submittedDs->getData() as $child){
            $childId[$child->childid] = $child->childid;
            $courseIds[$child->course_id] = $child->course_id;
        }

        // if($childId){
        //     $childModel = ChildProfileBasic::model()->findAllByPk($childId);

        //     foreach($childModel as $child){
        //         $this->childsName[$child->childid] = array(
        //             'child' => $child->getChildName(),
        //             'class' => $child->ivyclass->title,
        //         );
        //     }
        // }

        // if($courseIds) {
        //     $courseObj = AsaCourse::model()->findAllByPk($courseIds);

        //     foreach ($courseObj as $course) {
        //         $this->courseNamewa[$course->id] = array(
        //             'title' => $course->getTitle(),
        //             'schedule' => $course->schedule->title,
        //         );
        //     }
        // }

        //获取已确认数据
        $criteria = new CDbCriteria;
        $criteria->compare('t.status',3);
        $criteria->addNotInCondition('site_id', $dsSchoolList);
        $confirmed = new CActiveDataProvider('AsaRefund', array(
            'criteria' => $criteria,
            'sort' => array(
                'defaultOrder' => 'updated DESC',
            ),
            'pagination'=>array(
                'pageSize'=>10,
            ),
        ));

        //获取已确认数据
        $criteria = new CDbCriteria;
        $criteria->compare('t.status',3);
        $criteria->compare('site_id', $dsSchoolList);
        $confirmedDs = new CActiveDataProvider('AsaRefund', array(
            'criteria' => $criteria,
            'sort' => array(
                'defaultOrder' => 'updated DESC',
            ),
            'pagination'=>array(
                'pageSize'=>10,
            ),
        ));

        foreach($confirmed->getData() as $child){
            $childId[$child->childid] = $child->childid;
            $courseIds[$child->course_id] = $child->course_id;
        }
        foreach($confirmedDs->getData() as $child){
            $childId[$child->childid] = $child->childid;
            $courseIds[$child->course_id] = $child->course_id;
        }
        // if($childId){
        //     $childModel = ChildProfileBasic::model()->findAllByPk($childId);

        //     foreach($childModel as $child){
        //         $this->childsName[$child->childid] = array(
        //             'child' => $child->getChildName(),
        //             'class' => $child->ivyclass->title,
        //         );
        //     }
        // }

        // if($courseIds) {
        //     $courseObj = AsaCourse::model()->findAllByPk($courseIds);

        //     foreach ($courseObj as $course) {
        //         $this->courseNamewa[$course->id] = array(
        //             'title' => $course->getTitle(),
        //             'schedule' => $course->schedule->title,
        //         );
        //     }
        // }

        $criteria = new CDbCriteria;
        $criteria->compare('t.status',4);
        $refundError = new CActiveDataProvider('AsaRefund', array(
            'criteria' => $criteria,
            'sort' => array(
                'defaultOrder' => 'updated DESC',
            ),
            'pagination'=>array(
                'pageSize'=>10,
            ),
        ));

        foreach($refundError->getData() as $child){
            $childId[$child->childid] = $child->childid;
            $courseIds[$child->course_id] = $child->course_id;
        }
        if($childId){
            $childModel = ChildProfileBasic::model()->findAllByPk($childId);

            foreach($childModel as $child){
                $this->childsName[$child->childid] = array(
                    'child' => $child->getChildName(),
                    'class' => $child->ivyclass->title,
                );
            }
        }

        if($courseIds) {
            $courseObj = AsaCourse::model()->findAllByPk($courseIds);

            foreach ($courseObj as $course) {
                $this->courseNamewa[$course->id] = array(
                    'title' => $course->getTitle(),
                    'schedule' => $course->schedule->title,
                );
            }
        }

        //分配数据到模版
        $this->render('refund',array(
            'submitted' => $submitted,
            'submittedDs' => $submittedDs,
            'confirmed' => $confirmed,
            'confirmedDs' => $confirmedDs,
            'refundError' => $refundError,
        ));
    }

    public function actionRefunds(){
        $this->schoolsName = $this->getAllBranch();

        $childName = Yii::app()->request->getParam('childName', "");
        $courseGroupId = intval(Yii::app()->request->getParam('courseGroup', 0));
        $courseId = intval(Yii::app()->request->getParam('course', 0));

        $crit = new CDbCriteria;
        //$crit->compare('schoolid', $this->branchId);
        $startYear = date('Y') - 3;
        $crit->compare('startyear', '>' . $startYear);
        $crit->order = "startyear DESC";
        $asaCourseGroups = AsaCourseGroup::model()->findAll($crit);
        $courseGroups = array();
        $courses = array();
        foreach($asaCourseGroups as $courseGroup){
            $courseGroups[$courseGroup->id] = $courseGroup->getName() . '(' .$courseGroup->schoolid.')';
            foreach($courseGroup->course as $course){
                $courses[$course->gid][$course->id] = $course->getTitle();
            }
        }

        $childids = array();
        $courseIds = array();
        if($childName){
            $crit = new CDbCriteria;
            $crit->addCondition("concat_ws(',',name_cn,first_name_en,middle_name_en,last_name_en) like '%{$childName}%' ");
            $childs = ChildProfileBasic::model()->findAll($crit);
            if($childs){
                foreach($childs as $childid){
                    $childids[$childid->childid] = $childid->childid;
                }
            }
        }

        $childids = ($childids) ? $childids : 0;

        $criteria = new CDbCriteria;
        $criteria->compare('t.status',5);
        if($courseGroupId)$criteria->compare('program_id', $courseGroupId);
        if($childName)$criteria->compare('childid', $childids);
        if($courseId)$criteria->compare('course_id', $courseId);
        $refund = new CActiveDataProvider('AsaRefund', array(
            'criteria' => $criteria,
            'sort' => array(
                'defaultOrder' => 'updated_done DESC',
            ),
            'pagination'=>array(
                'pageSize'=>10,
            ),
        ));

        foreach($refund->getData() as $child){
            $childId[$child->childid] = $child->childid;
            $courseIds[$child->course_id] = $child->course_id;
        }
        if($childId){
            //获取孩子姓名
            $childModel = ChildProfileBasic::model()->findAllByPk($childId);

            foreach($childModel as $child){
                $this->childsName[$child->childid] = array(
                    'child' => $child->getChildName(),
                    'class' => $child->ivyclass->title,
                );
            }
        }

        if($courseIds) {
            $courseObj = AsaCourse::model()->findAllByPk($courseIds);

            foreach ($courseObj as $course) {
                $this->courseNamewa[$course->id] = array(
                    'title' => $course->getTitle(),
                    'schedule' => $course->schedule->title,
                );
            }
        }


        $this->render('refunds', array(
            'refund' => $refund,
            'courses' => $courses,
            'courseGroups' => $courseGroups,
        ));
    }

    public function actionupdateRefund(){

        $this->schoolsName = $this->getAllBranch();
        //接收请求参数
        $id = Yii::app()->request->getParam('id', '');
        $state = Yii::app()->request->getParam('status', '');
        $attributes = $_POST['AsaRefund'];
        $receipt_no = $attributes['receipt_no'];

        $model = AsaRefund::model()->findByPk($id);


        $criteria = new CDbCriteria;
        $criteria->compare('course_id', $model->course_id);
        $criteria->compare('childid', $model->childid);
        $criteria->compare('invoice_item_id', $model->invoice_item_id);
        $criteria->compare('status', 5);
        $oldModel = AsaRefund::model()->findAll($criteria);
        $oldRefundMun = 0;
        if($oldModel){
            foreach ($oldModel as $item) {
                $oldRefundMun += $item->refund_total_amount;
            }
        }
        //获取文件名
        $fileNames = $model->refund_files;
        $fileNamess = CJSON::decode($fileNames);

        //获取学校名
        $branchName =$this->schoolsName[$model->site_id]['title'];

        //获取用户名
        $uid = array(
            $model->updated_by,
            $model->updated_confirm_by,
            $model->updated_done_by,
        );

        $criteria = new CDbCriteria;
        $criteria->compare('uid', $uid);
        $criteria->index = 'uid';
        $user = User::model()->findAll($criteria);
        //$userName = $user->name;

        //获取课程组名
        $program_id = $model->program_id;
        $courseGroup = AsaCourseGroup::model()->findByPk($program_id);
        $courseGroupName = $courseGroup->getName();

        //获取课程名
        $course_id = $model->course_id;
        $course = AsaCourse::model()->findByPk($course_id);
        $courseName = $course->getTitle();

        //获取孩子姓名
        $childModel = ChildProfileBasic::model()->findByPk($model->childid);

        $childClass = IvyClass::model()->findByPk($childModel->classid);

        $config = CommonUtils::LoadConfig('CfgASA');
        //定义修改状态

        if($model->status == 1){
            $status = array(
                2=>$config['status'][2]['cn'],
                3=>$config['status'][3]['cn'],
            );
        }elseif($model->status == 3){
            if($model->refund_method == 1){
                $status = array(
                    4=>$config['status'][5]['cn'],
                );
            }else{
                $status = array(
                    5=>$config['status'][5]['cn'],
                );
            }
        }elseif($model->status == 4){
            $status = array(
                4=>$config['status'][5]['cn'],
            );
        }

        if($_POST['AsaRefund']){
            if(!$_POST['AsaRefund']['status']){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('message', '状态不能为空'));
                $this->showMessage();
            }
            $model->setAttributes(array(
                'id' => $id,
                'status' => $_POST['AsaRefund']['status'],
                'receipt_no' => $receipt_no,
            ));

            if($model->status == 3){
                $model->updated_confirm = time();
                $model->updated_confirm_by = Yii::app()->user->id;
            }

            if(in_array($model->status, array(4,5))){
                $model->updated_done = time();
                $model->updated_done_by = Yii::app()->user->id;
                // 个人账户退款只有 IT 可以确认
                if ($model->refund_reason < 4 && $model->refund_method == 3 && Yii::app()->user->id != 8014547) {
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', Yii::t('message', '无法操作个人账户类型的退费'));
                    $this->showMessage();
                }
            }


            if($model->save()){
                if(in_array($model->status, array(4,5))){
                    Yii::import('common.components.AliYun.MQ.MQProducer');
                    if($model->refund_method == 1){
                        if($model->refund_total_amount > 0){
                            CommonUtils::addProducer(MQProducer::TAG_ASA, "WechatPay.wechatRefund", $model->id);
                        }else{
                            $model->status = 5;
                            $model->save();
                            CommonUtils::addProducer(MQProducer::TAG_ASA, "Invoice.releaseHoldSpot", CJSON::encode(array($model->course_id)));
                        }
                    }else{
                        CommonUtils::addProducer(MQProducer::TAG_ASA, "Invoice.releaseHoldSpot", CJSON::encode(array($model->course_id)));
                    }
                }

                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message','提交成功!'));
                $this->addMessage('callback', 'cbUpdateCash');
                $this->showMessage();
            }else{
                $error = current($model->getErrors());
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('message', $error[0]));
                $this->showMessage();
            }
        }

        // 根据退费方式判断课程是否为换课
        $exchangeData = array();
        $firstInvoiceData = array();
        if ($model->refund_method == 3) {
            $criteria = new CDbCriteria();
            $criteria->compare('new_cid', $course_id);
            $criteria->compare('childid', $model->childid);
            $exchangeModel = AsaCourseExchange::model()->find($criteria);
            if ($exchangeModel) {
                $payTypeConfig = array(1=>'现金', 2=>'微信', 3=>'个人账户');
                $first_invoice = $exchangeModel->first_invoice;
                if ($first_invoice) {
                    $first_invoice_model = AsaInvoiceItem::model()->findByPk($first_invoice);
                    $firstInvoiceData['course_name'] = '';
                    $firstInvoiceData['course_id'] = $first_invoice_model->course_id;
                    $firstInvoiceData['type'] = $payTypeConfig[$first_invoice_model->asainvoice->pay_type];
                    $firstInvoiceData['amount'] = $first_invoice_model->actual_total;
                    if ($first_invoice_model->asainvoice->pay_type == 2) {
                        $first_wechat_order = AsaWechatpayOrder::model()->findByAttributes(array('childid' => $model->childid,'invoice_id' => $first_invoice_model->order_id, 'status'=>1));
                        if ($first_wechat_order) {
                            $firstInvoiceData['id'] = $first_wechat_order->orderid;
                        }
                    }

                    $courseIds = array();
                    $criteria = new CDbCriteria();
                    $criteria->compare('first_invoice', $first_invoice);
                    $criteria->compare('status', AsaCourseExchange::SUCCESS);
                    $criteria->order = 'id ASC';
                    $exchangeModels = AsaCourseExchange::model()->findAll($criteria);
                    if ($exchangeModels) {
                        // 缴费记录
                        $invoiceData = array();
                        $wechatOrderData = array();
                        $pay_invoice = json_decode(end($exchangeModels)->pay_invoice);
                        if ($pay_invoice) {
                            $criteria = new CDbCriteria();
                            $criteria->compare('invoice_id', $pay_invoice);
                            $criteria->compare('status', 1);
                            $wechatOrderModels = AsaWechatpayOrder::model()->findAll($criteria);
                            foreach ($wechatOrderModels as $wechatOrderModel) {
                                $wechatOrderData[$wechatOrderModel->invoice_id] = $wechatOrderModel->orderid;
                            }
                            $invoiceModels = AsaInvoice::model()->findAllByPk($pay_invoice);
                        }
                        if ($invoiceModels) {
                            foreach ($invoiceModels as $v) {
                                $invoiceData[$v->id] = $v->pay_type;
                            }
                        }
                        // 退费记录
                        $refundData = array();
                        $refund_invoice = json_decode(end($exchangeModels)->refund_invoice);
                        if ($refund_invoice) {
                            $refundModels = AsaRefund::model()->findAllByPk($refund_invoice);
                        }
                        if ($refundModels) {
                            foreach ($refundModels as $v) {
                                $refundData[$v->id] = $v->refund_method;
                            }
                        }
                        foreach ($exchangeModels as $v) {
                            $courseIds[] = $v->new_cid;
                            $courseIds[] = $v->old_cid;
                            $exchangeData[$v->id] = array(
                                'new_cid' => $v->new_cid,
                                'old_cid' => $v->old_cid,
                                'new_course_num' => $v->new_course_num,
                                'old_course_num' => $v->old_course_num,
                                'new_course_amount' => $v->new_course_amount,
                                'old_course_amount' => $v->old_course_amount,
                                'difference' => $v->difference,
                                'pay_type' => '',
                                'pay_id' => '',
                            );
                            if ($v->difference > 0) {
                                if (isset($invoiceData[$v->link_id])) {
                                    $exchangeData[$v->id]['pay_type'] = $payTypeConfig[$invoiceData[$v->link_id]];
                                }
                                if (isset($wechatOrderData[$v->link_id])) {
                                    $exchangeData[$v->id]['pay_id'] = $wechatOrderData[$v->link_id];
                                }
                            }
                            if ($v->difference < 0) {
                                if (isset($refundData[$v->link_id])) {
                                    $exchangeData[$v->id]['pay_type'] = $payTypeConfig[$refundData[$v->link_id]];
                                }
                            }
                        }
                        $courseModels = AsaCourse::model()->findAllByPk($courseIds);
                        $courseData = array();
                        foreach ($courseModels as $courseModel) {
                            if ($courseModel->id == $firstInvoiceData['course_id']) {
                                $firstInvoiceData['course_name'] = $courseModel->getTitle();
                            }
                            $courseData[$courseModel->id]['name'] = $courseModel->getTitle();
                        }
                        foreach ($exchangeData as $k => $v) {
                            $exchangeData[$k]['new_course_name'] = $courseData[$v['new_cid']]['name'];
                            $exchangeData[$k]['old_course_name'] = $courseData[$v['old_cid']]['name'];
                        }

                    }
                }
            }
        }

         //分配数据到模版
        $this->renderPartial('detail',array(
            'status' => $status,
            'model' => $model,
            'config' => $config,
            'fileNames' => $fileNamess,
            'userName' => $user,
            'branchName'=> $branchName,
            'childClass'=> $childClass,
            'courseGroupName' => $courseGroupName,
            'courseName' => $courseName,
            'childsName' => $childModel,
            'oldRefundMun' => $oldRefundMun,
            'exchangeData' => $exchangeData,
            'firstInvoiceData' => $firstInvoiceData,
        ));
    }

    public function actionAnain()
    {
        $id = Yii::app()->request->getParam('id', '');
        $model = AsaRefund::model()->findByPk($id);
        if($model){
            Yii::import('common.components.AliYun.MQ.MQProducer');
            CommonUtils::addProducer(MQProducer::TAG_ASA, "WechatPay.wechatRefund", $model->id);
            $this->addMessage('state', 'success');
            $this->addMessage('message', Yii::t('message','提交成功!'));
            $this->addMessage('callback', 'cbUpdateCash');
            $this->showMessage();
        }
    }

    // 考勤汇总查询数据
    public function actionReport()
    {
        $select_year = Yii::app()->request->getParam('year', '');
        Yii::import('common.models.asainvoice.*');
        $criteria = new  CDbCriteria();
        $criteria->compare('status', array(AsaMonthReport::CONFIRM,AsaMonthReport::SUBMIT));
        $criteria->order = 'report_month DESC';
        $reports = AsaMonthReport::model()->findAll($criteria);

        $list = array();
        if ($reports) {
            foreach ($reports as $report) {
                $list[$report->report_month][$report->schoolid]['id'] = $report->id;
                $list[$report->report_month][$report->schoolid]['status'] = $report->status;
                $list[$report->report_month][$report->schoolid]['amount'] = $report->total_amount;
            }
        }

        $yearList = array();
        $newList = array();
        foreach ($list as $report_month=>$item){
            $year = substr($report_month, 0, 4);
            $month = substr($report_month, 4, 2);
            if((int)$month >=8){
                $year = $month==8 ? $year-1 : $year;
                $yearList[$year] = $year;
            }
        }

        if(empty($select_year)){
            $select_year = current($yearList);
        }
        foreach ($list as $report_month=>$item){
            if($report_month >= $select_year.'09' && $report_month <($select_year+1).'09' ){
                $newList[$report_month] = $item;
            }
        }
        $criteria = new  CDbCriteria();
        $criteria->compare('status', Branch::STATUS_ACTIVE);
        $branchModel = Branch::model()->findAll($criteria);

        $schoolList = array();
        foreach($branchModel as $item){
            $schoolList[$item->branchid] = $item->title;
        }
        $this->render('reports', array(
            'list' => $newList,
            'yearList' => array_keys($yearList),
            'schoolList' => $schoolList,
        ));
    }

    // 查看某个校园某月的统计
    public function actionShowRepost()
    {
        Yii::import('common.models.asainvoice.*');
        $schoolid = Yii::app()->request->getParam('schoolid', '');
        $report_month = Yii::app()->request->getParam('report_month', '');

        $reportModel = AsaMonthReport::model()->findByAttributes(array('schoolid'=> $schoolid, 'report_month'=> $report_month));
        $re = $reportModel->showData(array('schoolid'=> $schoolid, 'report_month'=> $report_month));
        echo json_encode($re);
    }

    //修改报表状态
    public function actionReviewReport()
    {
        Yii::import('common.models.asainvoice.*');

        $reportid = Yii::app()->request->getParam('reportid', '');
        $status = Yii::app()->request->getParam('status', '');

        if(!$reportid || !$status){
            echo CJSON::encode(array('state' => 'fail', 'message' => "参数错误"));
            Yii::app()->end();
        }
        $model = AsaMonthReport::model()->findByPk($reportid);
        if($model){
            $schoolid = $model->schoolid;
            $model->status = $status;
            $model->create_time = time();
            $model->uid = Yii::app()->user->id;
            if($model->save()){
                $criteria = new CDbCriteria();
                $criteria->with = array('course', 'vendor');
                // 教师类型为外部个人或内部个人
                $criteria->compare('vendor.type', array(1, 3));
                $criteria->compare('t.settle_month', strtotime($model->report_month . '01'));
                $criteria->compare('t.report_month', $model->report_month);
                $criteria->compare('t.school_id', $schoolid);
                $thirdParts = AsaThirdpartReport::model()->findAll($criteria);
                if($thirdParts){
                    foreach ($thirdParts as $val){
                        $val->status = ($status == 10) ? 0 : 2;
                        $val->save();
                    }
                }
                echo CJSON::encode(array('state' => 'success', 'message' => "成功"));
            }else{
                $err = current($model->getErrors());
                echo CJSON::encode(array('state' => 'fail', 'message' => $err[0]));
            }
        }else{
            echo CJSON::encode(array('state' => 'fail', 'message' => "参数错误"));
        }
    }

    public function getRefundMethod($data){
        $config = CommonUtils::LoadConfig('CfgASA');
        if($data->refund_method){
             switch (Yii::app()->language) {
                case "zh_cn":
                    return ($data->openid) ? $config['refundMethod'][$data->refund_method]['cn'] . "( 自主退费 )"  : $config['refundMethod'][$data->refund_method]['cn'];
                    break;
                case "en_us":
                    return ($data->openid) ? $config['refundMethod'][$data->refund_method]['en'] . "( 自主退费 )"  : $config['refundMethod'][$data->refund_method]['en'];
                    break;
            }
        }
    }

    public function getRefundReason($refund_reason){
        //获取退费原因
        $config = CommonUtils::LoadConfig('CfgASA');
        if($refund_reason->refund_reason){
             switch (Yii::app()->language) {
                case "zh_cn":
                    return $config['refundReason'][$refund_reason->refund_reason]['cn'];
                    break;
                case "en_us":
                    return $config['refundReason'][$refund_reason->refund_reason]['en'];
                    break;
            }
        }
    }

    public function getChildName($data)
    {
         return $this->childsName[$data->childid]['child'] . "<br>" . $this->childsName[$data->childid]['class'];
    }

    public function getAmount($data)
    {
        if(in_array($data->refund_type, array(4,5) )){
            return "¥ " . $data->refund_total_amount . "( 换课退费 )";
        }else{
            return "¥ " . $data->asainvoiceitem->unit_price . " * " . $data->refund_class_count . " = " . $data->refund_total_amount;
        }
    }

    public function getUserName($data)
    {
        return $this->usersName[$data->expense_uid];
    }

    public function getBranch($data)
    {
        return $this->schoolsName[$data->branch]['title'];
    }

    public function getClassName($data)
    {
        return $this->childsName[$data->childid]['class'];
    }

    public function getCourseName($data)
    {
        return $this->courseNamewa[$data->course_id]['title'] . "<br>" . $this->courseNamewa[$data->course_id]['schedule'];
    }

    public function actionDownloads()
    {
        ob_end_clean();
        $fileName = Yii::app()->request->getParam('fileName', "");
        $extension = substr(strrchr($fileName, '.'), 1);
        $file_dir = Yii::app()->params['xoopsVarPath'] . '/asa/refund/';
        $fileres = file_get_contents($file_dir . $fileName);

        if($fileres){
            if($extension == "pdf"){
                header('Content-type: application/pdf');
            }else{
                header('Content-type: image/jpeg');
            }
            echo $fileres;
        }else{
            $oss = CommonUtils::initOSS('private');
            $filePath = 'asa/refund/' . $fileName;
            $url = $oss->get_sign_url($filePath);
            Yii::app()->request->redirect($url);
        }
    }

    public function getCourseButton($data)
    {
        $item  = ($data->status == 4) ? "重新提交" : "详细信息" ;
         echo CHtml::link($item, array('updateRefund', 'id' => $data->id), array('class' => 'J_modal btn btn-xs btn-info'));
    }

    public function actionActualAmount()
    {
        $groups = array();
        $crit = new CDbCriteria;
        $crit->order = "startyear DESC";
        $groupModel = AsaCourseGroup::model()->findAll($crit);

        foreach($groupModel as $gModel) {
            $groups[$gModel->schoolid][] =array('name'=>$gModel->getName(),'id'=>$gModel->id);
        }
        $this->render('actualamount', array('groups' => $groups));
    }

    public function actionQueryAmount()
    {
        $groupId = Yii::app()->request->getPost('groupId');
        if ($groupId) {
            $data = array();
            $refunds = array();
            $teachers = array();
            $vendors = array();
            $reimbursements = array();
            $unsettles = array();
            $variations = array();

            // 收费金额（去掉补差价账单类型）
            $sql = "SELECT SUM(A.actual_total) as amount, A.course_id, A.order_id, B.title_cn, B.title_en FROM ivy_asa_invoice_item A left join ivy_asa_invoice C on A.order_id=C.id , ivy_asa_course B  where A.status = 20 and A.course_group_id = ".$groupId."  and A.course_id = B.id and C.created_from!=4 group by A.course_id order by A.course_id asc";
            $amounts = Yii::app()->subdb->createCommand($sql)->queryAll();

            // 退费金额
            $sql = "SELECT sum(refund_total_amount) as amount, course_id FROM ivy_asa_refund where status < 6 and  program_id =".$groupId." group by course_id ORDER BY course_id ASC";
            $refund = Yii::app()->subdb->createCommand($sql)->queryAll();
            foreach($refund as $_refund) {
                $refunds[$_refund['course_id']] = $_refund['amount'];
            }

            // // 考勤支出（已结算的）
            // $oldVariation = array();
            // $sql = "SELECT *, i.course_id as cid, v.id as vid FROM ivy_asa_month_report_item as i JOIN ivy_asa_month_report_variation as v ON i.report_id=v.report_id and i.vendor_id=v.vendor_id where v.settlement=1 and i.group_id =".$groupId;
            // $teacher = Yii::app()->subdb->createCommand($sql)->queryAll();

            // foreach($teacher as $_teacher) {
            //     $oldVariation[$_teacher['vid']] = $_teacher['variation_amout'];
            //     $teachers[$_teacher['cid']] += ($_teacher['course_count']*$_teacher['unit_price']);
            // }

            // 第三方考勤（内部个人）
            $criteria = new CDbCriteria();
            $criteria->with = array('vendor');
            // 教师类型为外部个人或内部个人
            $criteria->compare('vendor.type', array(1, 3));
            $criteria->compare('t.program_id', $groupId);
            $thirdParts = AsaThirdpartReport::model()->findAll($criteria);
            $thirdTeachers = array();
            $thirdMonth = array();
            $thirdTeacherReportIds = array();
            $thirdTeacherVendorIds = array();

            foreach($thirdParts as $_teacher) {
                $vendorIdList[$_teacher['course_staff_id']] = $_teacher['course_staff_id'];
                if ($_teacher->vendor->type == 1) {
                    $teachers[$_teacher['course_id']] += $_teacher['total_amount'];
                }
                if ($_teacher->vendor->type == 3) {
                    $thirdTeacherReportIds[] = $_teacher['id'];
                    $thirdTeacherVendorIds[] = $_teacher['course_staff_id'];
                    $key = $_teacher['course_month'];
                    $thirdMonth[] = explode('_', $key)[1];
                }
            }
            if ($thirdMonth) {
                // 第三方个人分成（结算为工资）
                $sql = "SELECT a.*,c.report_month FROM ivy_asa_month_report_variation as a LEFT JOIN ivy_asa_vendor as b ON a.vendor_id=b.vendor_id LEFT JOIN ivy_asa_month_report as c ON a.report_id=c.id
                 WHERE a.settlement=1 and b.type=3 and a.vendor_id in (".implode(',', $thirdTeacherVendorIds).") and c.report_month in (".implode(',', $thirdMonth).")";
                $res = Yii::app()->subdb->createCommand($sql)->queryAll();
                foreach ($res as $item) {
                    $key = $item['report_month'] .'-'. $item['vendor_id'];
                    $thirdTeachers[] = $key;
                }
            }
            foreach($thirdParts as $_teacher) {
                if ($_teacher->vendor->type == 3) {
                    $thirdMonth = explode('_', $_teacher['course_month'])[1];
                    $key = $thirdMonth .'-'. $_teacher['course_staff_id'];
                    if (in_array($key, $thirdTeachers)) {
                        $teachers[$_teacher['course_id']] += $_teacher['total_amount'];
                    }
                }
            }


            // 考勤支出（已结算的）
            $oldVariation = array();
            // $sql = "SELECT * FROM ivy_asa_month_report_item where group_id = $groupId";
            $sql = "SELECT *, i.course_id as cid, v.id as vid, v.settlement as settlement FROM ivy_asa_month_report_item as i JOIN ivy_asa_month_report_variation as v ON i.report_id=v.report_id and i.vendor_id=v.vendor_id and i.group_id =".$groupId;
            $teacher = Yii::app()->subdb->createCommand($sql)->queryAll();

            $vendorIdList = array();
            $reportIdList = array();
            foreach($teacher as $_teacher) {
                $vendorIdList[$_teacher['vendor_id']] = $_teacher['vendor_id'];
                $reportIdList[$_teacher['report_id']] = $_teacher['report_id'];
                if ($_teacher['settlement'] == 1) {
                    $teachers[$_teacher['course_id']] += ($_teacher['course_count']*$_teacher['unit_price']);
                }
            }

            if ($vendorIdList && $reportIdList) {
                $vendorIdStr = implode(',', $vendorIdList);
                $reportIdStr = implode(',', $reportIdList);
                // old variation
                $sql = "SELECT * FROM  ivy_asa_month_report_variation where settlement=1 and report_id in ($reportIdStr) and vendor_id in ($vendorIdStr)";
                $report_variations = Yii::app()->subdb->createCommand($sql)->queryAll();
                foreach ($report_variations as $item) {
                    $oldVariation[$item['id']] = $item['variation_amout'];
                }
            }


            // 报销金额
            $sql = "SELECT i.* FROM ivy_asa_expense_item as i JOIN ivy_asa_expense as t ON i.eid=t.id where i.groupid =".$groupId." and t.status=20 and t.type=1";
            $reimbursement = Yii::app()->subdb->createCommand($sql)->queryAll();
            foreach($reimbursement as $_reimbursement) {
                $reimbursements[$_reimbursement['courseid']] += $_reimbursement['amount'];
            }
            // 付款金额
            $sql = "SELECT i.* FROM ivy_asa_expense_item as i JOIN ivy_asa_expense as t ON i.eid=t.id where i.groupid =".$groupId." and t.status=20 and t.type=2";
            $reimbursement = Yii::app()->subdb->createCommand($sql)->queryAll();
            foreach($reimbursement as $_reimbursement) {
                $vendors[$_reimbursement['courseid']] += $_reimbursement['amount'];
            }
            // 查找已结算的调整金额
            $sql = "SELECT i.* FROM ivy_asa_month_report_variation_item AS i LEFT JOIN ivy_asa_month_report_variation AS v ON v.id = i.variation_id WHERE i.program_id =".$groupId." AND v.settlement=1";
            $variationItems = Yii::app()->subdb->createCommand($sql)->queryAll();
            foreach ($variationItems as $variationItem) {
                if (isset($oldVariation[$variationItem['variation_id']])) {
                    unset($oldVariation[$variationItem['variation_id']]);
                }
                $variations[$variationItem['course_id']] += $variationItem['variation_amout'];
            }

            $total = array(0, 0, 0, 0, 0, 0);
            $tAmount = isset($teachers[0]) ? $teachers[0] : 0;
            $vAmount = isset($vendors[0]) ? $vendors[0] : 0;
            $eAmount = isset($reimbursements[0]) ? $reimbursements[0] : 0;
            $variation = isset($variations[0]) ? $variations[0] : 0;
            $variation += array_sum($oldVariation);
            $actual = 0-$tAmount-$vAmount-$eAmount-$variation;
            $data[0] = array(
                'title_cn' => '整体运营',
                'title_en' => 'General Operations',
                'amount' => '',
                'refund' => '',
                'income' => '',
                'teacher' => number_format($tAmount, 2),
                'variation' => number_format($variation, 2),
                'vendor' => number_format($vAmount, 2),
                'reimbursement' => number_format($eAmount, 2),
                'actual' => number_format($actual, 2),
                'wage' => '',
                'wechat' => '',
                'margin' => '',
            );
            $total[3] = $tAmount;
            $total[4] = $variation;
            $total[5] = $vAmount;
            $total[6] = $eAmount;
            $total[7] = $actual;
            foreach ($amounts as $_amount){
                $rAmount = isset($refunds[$_amount['course_id']]) ? $refunds[$_amount['course_id']] : 0;
                $tAmount = isset($teachers[$_amount['course_id']]) ? $teachers[$_amount['course_id']] : 0;
                $vAmount = isset($vendors[$_amount['course_id']]) ? $vendors[$_amount['course_id']] : 0;
                $eAmount = isset($reimbursements[$_amount['course_id']]) ? $reimbursements[$_amount['course_id']] : 0;
                $variation = isset($variations[$_amount['course_id']]) ? $variations[$_amount['course_id']] : 0;
                $income = $_amount['amount']-$rAmount;
                $wage = ($income)*0.06;
                $wechat = ($income)*0.006;
                $actual = $income-$tAmount-$vAmount-$eAmount-$variation-$wage-$wechat;
                $margin = '';
                if ($actual > 0) {
                    $margin = round($actual/$income, 4)*100 . '%';
                }
                $data[$_amount['course_id']] = array(
                    'title_cn' => $_amount['title_cn'],
                    'title_en' => $_amount['title_en'],
                    'amount' => number_format($_amount['amount'], 2),
                    'refund' => number_format($rAmount, 2),
                    'income' => number_format($income, 2),
                    'teacher' => number_format($tAmount, 2),
                    'vendor' => number_format($vAmount, 2),
                    'reimbursement' => number_format($eAmount, 2),
                    'variation' => number_format($variation, 2),
                    'actual' => number_format($actual, 2),
                    'wage' => number_format($wage, 2),
                    'wechat' => number_format($wechat, 2),
                    'margin' => $margin,
                );
                $total[0] += $_amount['amount'];
                $total[1] += $rAmount;
                $total[2] += $income;
                $total[8] += $wage;
                $total[9] += $wechat;
                $total[3] += $tAmount;
                $total[4] += $variation;
                $total[5] += $vAmount;
                $total[6] += $eAmount;
                $total[7] += $actual;
            }

            // 无收入的课程
            $noIncomeCourseIds = array();
            foreach ($teachers as $courseId => $price) {
                if (isset($data[$courseId])) {
                    continue;
                }
                $noIncomeCourseIds[] = $courseId;
            }
            foreach ($reimbursements as $courseId => $price) {
                if (isset($data[$courseId])) {
                    continue;
                }
                $noIncomeCourseIds[] = $courseId;
            }
            foreach ($vendors as $courseId => $price) {
                if (isset($data[$courseId])) {
                    continue;
                }
                $noIncomeCourseIds[] = $courseId;
            }
            foreach ($variations as $courseId => $price) {
                if (isset($data[$courseId])) {
                    continue;
                }
                $noIncomeCourseIds[] = $courseId;
            }
            if ($noIncomeCourseIds) {
                $noIncomeCourseIds = array_unique($noIncomeCourseIds);
                $noIncomeCourseModels = AsaCourse::model()->findAllByPk($noIncomeCourseIds);
                foreach ($noIncomeCourseModels as $noIncomeCourseModel) {
                    $courseId = $noIncomeCourseModel->id;
                    $courseTitleCn = $noIncomeCourseModel->title_cn;
                    $courseTitleEn = $noIncomeCourseModel->title_en;
                    if (isset($data[$courseId])) {
                        continue;
                    }
                    $rAmount = isset($refunds[$courseId]) ? $refunds[$courseId] : 0;
                    $tAmount = isset($teachers[$courseId]) ? $teachers[$courseId] : 0;
                    $vAmount = isset($vendors[$courseId]) ? $vendors[$courseId] : 0;
                    $eAmount = isset($reimbursements[$courseId]) ? $reimbursements[$courseId] : 0;
                    $variation = isset($variations[$courseId]) ? $variations[$courseId] : 0;
                    $income = 0;
                    $wage = ($income)*0.06;
                    $wechat = ($income)*0.006;
                    $actual = $income-$tAmount-$vAmount-$eAmount-$variation-$wage-$wechat;
                    $margin = '';
                    if ($actual > 0) {
                        $margin = round($actual/$income, 4)*100 . '%';
                    }
                    $data[$courseId] = array(
                        'title_cn' => $courseTitleCn,
                        'title_en' => $courseTitleEn,
                        'amount' => number_format(0, 2),
                        'refund' => number_format($rAmount, 2),
                        'income' => number_format($income, 2),
                        'teacher' => number_format($tAmount, 2),
                        'vendor' => number_format($vAmount, 2),
                        'reimbursement' => number_format($eAmount, 2),
                        'variation' => number_format($variation, 2),
                        'actual' => number_format($actual, 2),
                        'wage' => number_format($wage, 2),
                        'wechat' => number_format($wechat, 2),
                        'margin' => $margin,
                    );
                    $total[0] += 0;
                    $total[1] += $rAmount;
                    $total[2] += $income;
                    $total[8] += $wage;
                    $total[9] += $wechat;
                    $total[3] += $tAmount;
                    $total[4] += $variation;
                    $total[5] += $vAmount;
                    $total[6] += $eAmount;
                    $total[7] += $actual;
                }
            }


            $this->addMessage('state', 'success');
            $totalMargin = '';
            if ($total[7] > 0) {
                $totalMargin = round($total[7]/$total[2], 4)*100 . '%';
            }
            $this->addMessage('data', array('data' => $data, 'total' => array(
                number_format($total[0], 2),
                number_format($total[1], 2),
                number_format($total[2], 2),
                number_format($total[3], 2),
                number_format($total[4], 2),
                number_format($total[5], 2),
                number_format($total[6], 2),
                number_format($total[7], 2),
                number_format($total[8], 2),
                number_format($total[9], 2),
                $totalMargin,
            )));
            $this->addMessage('callback', 'callback');
            $this->addMessage('message', '查询成功');
        }
        else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '请选一个课程组');
        }
        $this->showMessage();
    }

    // 其他收入
    public function actionOtherAmount()
    {
        $groups = array();
        $crit = new CDbCriteria;
        $crit->order = "startyear DESC";
        $groupModel = AsaCourseGroup::model()->findAll($crit);

        foreach($groupModel as $gModel) {
            $groups[$gModel->schoolid][] =array('name'=>$gModel->getName(),'id'=>$gModel->id);
        }

        $model = new AsaOtherIncome();

        $this->render('otheramount', array('groups' => $groups, 'model' => $model));
    }

    // 其他收入
    public function actionOtherList()
    {
        if(Yii::app()->request->isPostRequest){
            $schoolId = Yii::app()->request->getParam('schoolId');
            $groupId = Yii::app()->request->getParam('groupId');
            $data = array();
            if($schoolId && $groupId) {
                $criteria = new  CDbCriteria();
                $criteria->compare('school_id', $schoolId);
                $criteria->compare('group_id', $groupId);
                $criteria->compare('status', 1);
                $asaOtherIncomeModel = AsaOtherIncome::model()->findAll($criteria);

                if ($asaOtherIncomeModel) {
                    $category = AsaOtherIncome::category();
                    foreach ($asaOtherIncomeModel as $val) {
                        $contract = json_decode($val->contract);
                        $info = '';
                        foreach ($contract as $item){
                            $info .= '<label class="btn btn-success btn-xs"><a style="color:#fff" target="_blank" href="' . $this->createUrl("ossfileRedit", array("filePath" => $item)) .'">' . $item . '</a></label> ';
                        }

                        $data[] = array(
                            'id' => $val->id,
                            'category' => $category[$val->category],
                            'start_time' => date("Y-m-d", $val->start_time),
                            'contract' => $info,
                            'end_time' => date("Y-m-d", $val->end_time),
                            'fee' => $val->fee,
                            'created_by' => $val->created_by,
                            'created_at' => date("Y-m-d", $val->created_at),
                        );
                    }
                }
            }
            $this->addMessage('state', 'success');
            $this->addMessage('data', $data);
            $this->addMessage('message', '查询成功');
            $this->showMessage();
        }
    }

    /**
     * 上传附件
     */
    public function actionUploadatta()
    {
        $file = CUploadedFile::getInstanceByName('upload_file');
        $oss = CommonUtils::initOSS('private');
        $objectPath = 'asaotherincome/';
        $saveName = '';
        $msg = '没有附件选中';
        if ($file) {
            if ($file->size > 10*1024*1024) {
                $msg = '文件过大';
            } else{
                $needType = array('jpg','jpeg','png','rar','doc','txt','pdf','gif','zip','xlsx','xls','docx');
                if (!in_array(strtolower($file->getExtensionName()), $needType)) {
                    $msg = '此文件类型不允许上传';
                }else{
                    $ext = $file->getExtensionName();
                    $fileName = $file->name;
                    $saveName = '5_' . uniqid() . '.' . $ext;

                    if(!$oss->uploadFile($objectPath . $saveName, $file->getTempName())) {
                        $filePath = Yii::app()->params['OAUploadBasePath'] . '/asaotherincome/';
                        if ($file->saveAs($filePath . $saveName)) {
                            $msg = 'success';
                            $baseUrl = Yii::app()->params['OAUploadBaseUrl'] . '/mailattachment/';
                        } else {
                            $msg = '文件上传失败';
                        }
                    }else{
                        $msg = 'success';
                        $baseUrl = Yii::app()->params['OAUploadBaseUrl'] . '/mailattachment/';
                    }
                }
            }
        }

        echo CJSON::encode(array(
            'url' => $this->createUrl('ossfileRedit', array('filePath' => $saveName)),
            'fileName' => $fileName,
            'saveName' => $saveName,
            'msg' => $msg,
        ));

    }

    // OSS 文件跳转链接
    public function actionOssfileRedit($filePath)
    {
        if ($filePath) {
            $oss = CommonUtils::initOSS('private');
            $filePath = 'asaotherincome/' . $filePath;
            $url = $oss->get_sign_url($filePath);
            Yii::app()->request->redirect($url);
        }
        return false;
    }

    // OSS 删除
    public function actionDelOssfileRedit()
    {
        $filePath = Yii::app()->request->getParam('saveName');
        if ($filePath) {
            $oss = CommonUtils::initOSS('private');
            $filePath = 'asaotherincome/' . $filePath;
            $oss->delete_object($filePath);
            $this->addMessage('state', 'success');
            $this->addMessage('message', 'success');
            $this->showMessage();
        }
        return false;
    }



    //  增加其他账单
    public  function actionSaveOther()
    {
        if(Yii::app()->request->isPostRequest){
            $model = new AsaOtherIncome();
            $model->attributes = $_POST['AsaOtherIncome'];
           /* $model->school_id = 'CD_FC';
            $model->group_id = 33;*/
            $model->contract = ($model->contract) ? json_encode($model->contract) : '';
            $model->start_time = ($model->start_time) ? strtotime($model->start_time) : '';
            $model->end_time = ($model->end_time) ? strtotime($model->end_time) : '';
            $model->status = 1;
            $model->updated_by = $this->staff->uid;
            $model->updated_at = time();
            $model->created_by = $this->staff->uid;
            $model->created_at = time();
            if(!$model->save()){
                $this->addMessage('state', 'fail');
                $err = current($model->getErrors());
                $this->addMessage('message', $err[0]);
                $this->showMessage();
            }
            $this->addMessage('callback', 'callback');
            $this->addMessage('state', 'success');
            $this->addMessage('message', 'success');
            $this->showMessage();
        }
        $this->addMessage('state', 'fail');
        $this->addMessage('message', '失败');
        $this->showMessage();
    }
}
