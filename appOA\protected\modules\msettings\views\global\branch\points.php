<?php
$bModel = Branch::model();
$bModel->getMetaData()->addRelation(
    'pointsExchange',
    array(
        CActiveRecord::HAS_ONE,
        'BranchVar',
        'branchid',
        'on'=>'category=:category',
        'params'=>array(':category'=>'pointsExchange'),
    )
);
$crit = new CDbCriteria;
$crit->index = 'branchid';
$crit->compare('type', '<>'.Branch::TYPE_OFFICE);
$crit->compare('status', Branch::STATUS_ACTIVE);
$crit->order='`group` ASC';
$models = $bModel->with('pointsExchange')->findAll($crit);
?>
<div class="col-md-5">
    <?php echo CHtml::beginForm('', 'post', array('class'=>'J_ajaxForm')); ?>
    <table class="table table-hover">
        <thead>
            <tr>
                <th>Campus</th>
                <th>Enable Points Exchange</th>
                <th>Show products below x points (0 = no limit)</th>
            </tr>
        </thead>
        <tbody>
        <?php
        foreach($models as $branchid => $branch):
            if($branch->pointsExchange==null){
                $branch->pointsExchange = new BranchVar();
            }
        ?>
            <tr>
                <th style="vertical-align:middle">
                    <?php echo '<span class="glyphicon glyphicon-tags program'.$branch['group'].'"></span>'; ?>
                    <?php echo $branch['title'];?>
                </th>
                <td><div class="checkbox"><label><?php echo CHtml::activeCheckBox($branch->pointsExchange, 'flag', array('name'=>'BranchVar['.$branchid.'][flag]')); ?> 启用</label></div></td>
                <td><?php echo CHtml::activeTextField($branch->pointsExchange, "data", array('name'=>'BranchVar['.$branchid.'][data]', 'class'=>'form-control')); ?></td>
            </tr>
        <?php
        endforeach;
        ?>
        </tbody>
        <tfoot>
        <tr>
            <td colspan="3">
                <button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global','Submit');?></button>
            </td>
        </tr>
        </tfoot>
    </table>
    <?php echo CHtml::endForm(); ?>
</div>
