<?php
/**
 * Message translations.
 *
 * This file is automatically generated by 'yiic message' command.
 * It contains the localizable messages extracted from source code.
 * You may modify this file by translating the extracted messages.
 *
 * Each array element represents the translation (value) of a message (key).
 * If the value is empty, the message is considered as not translated.
 * Messages that no longer need translation will have their translations
 * enclosed between a pair of '' marks.
 *
 * Message string can be used with plural forms format. Check i18n section
 * of the guide for details.
 *
 * NOTE, this file must be saved in UTF-8 encoding.
 *
 * @version $Id: $
 */
return array(
    "Medical certificate"=>"医疗证明",
    "Reasons of exemption from PE"=>'免体原因',
    "Exemption Date"=>'免体日期',
    "Confirm whether students need to exemption from physical education"=>'确认学生是否需要在校免体',
    'Please describe in detail the medical reasons why your child needs to apply for exemption from PE'=>'请详述孩子因医疗情况需免体原因',
    'Please fill in the application dates for your child exemption from PE'=>'请填写您孩子所需免体日期',
    'Please upload the medical certificate issued by doctor (Diagnosis certificate with "medical excuse from physical education and dates" written on it or Medical Excuse Form From Physical Education)'=>'请上传医生开具的医疗证明（写有免体字样和日期的诊断证明或免体证明）',
    'Please describe in detail the reasons why your child needs to take medication at school'=>'请详述孩子需在校用药原因',
    'Excuse PE for more than 3 days, doctor’s note will be required to upload (Diagnosis certificate with "medical excuse from physical education and dates" written on it or Medical Excuse Form From Physical Education)'=>'3天以上的免体，请上传医生开具的医疗证明（写有免体字样和日期的诊断证明或免体证明）',
    'Dietary restrictions during this trip' => '旅行期间的饮食禁忌',
    'Medical alert & restrictions in details' => '健康警示和禁忌',
    'Will the student bring medication on this trip?' => '此次出行学生是否会携带药品',
    'Please specify if needed' => '如有特殊情况，请描述',
    'Medication name' => '药物名称',
    'Is it prescription medication' => '是否为处方药',
    'Doctor\'s prescription' => '医生处方',
    'Dosage / time' => '每次给药剂量',
    'Dosing time / symptoms' => '给药时间/症状',
    'Date of administration From' => '给药日期',
    'Date of administration' => '给药日期',
    'Yes' => '是',
    'No' => '否',
    'None' => '无',
    'Vegetarian diet' => '素食',
    'No pork' => '不含猪肉的食品',
    'Food allergy' => '食物过敏',
    'Asthma' => '哮喘',
    'Heart Disease' => '心脏病',
    'Epilepsy' => '癫痫',
    'Sleepwalking' => '梦游',
    'Others' => '其他',
    'Does the student have a medical insurance provider' => '学生是否有医疗保险',
    'Type of the insurance' => '医疗保险类型',
    'Medical insurance provider' => '医疗保险提供机构名称',
    'Medical insurance policy number' => '医疗保险保单号码',
    'Medical insurance policy expiry date' => '医疗保险保单有效截止日期',
    'insurance card/policy page' => '保险卡/保单页照片',
    'Accidental medical insurance' => '意外医疗保险',
    'High-End medical insurance' => '高端医疗保险',
    'Other medical insurance (Applicable for outpatient and emergency services)' => '其它医疗保险（门急诊可用）',
    'See annex' => '见附件',
    'Chinese ID card' => '中国身份证',
    'Passport' => '护照',
    'Other documents (Hong Kong, Macau, Taiwan)' => '其他证件（港澳台）',
    'ID NO.' => '身份证号',
    'Passport NO.' => '护照号',
    'ID number' => '证件号',
    'Passport Expired Date' => '护照有效期至',
    'ID card expiry date'=>'证件有效期至',
    'Relationship to student' => '紧急联系人与学生关系',
    'Emergency contact phone number' => '紧急联系人电话',
    'Emergency contact person for this trip' => '本次出行紧急联系人姓名',
    'Full name as on ID card' => '本次出行使用的证件上的姓名',
    'To' => '至',
    'What identification documents are being used by the student for this trip' => '学生本次出行使用的证件',
    'Chinese visa number on your passport/or foreign permanent resident ID card number' => '护照上的中国签证号码/或外国人永久居留证号码',
    'Students passport\'s personal information page and valid visa page/ or foreign permanent resident ID card page' => '护照本人信息页和有效签证页/或外国人永久居留证照片',
    'All pages of the documents' => '证件正反面照片',
    'What identification documents are being used by the student for this trip?'=>'学生本次出行使用的证件是？',
    'Confirm whether students need to take medication at school'=>'确认学生是否需要在校服药',
    'Confirm whether student needs to be tagged' => '确认是否需要标记学生',
    'Other Information'=>'其它情况说明',
    'Dosage per Time'=>'每次给药剂量',
    'Medication'=>'药物',
    'Signature'=>'签名',
    'Agreement'=>'协议',
    'Attach'=>'附件',
    'Reason for medication'=>'用药原因',
    'Reasons'=>'原因',
    'Medical Information'=>'医疗信息',
    "pick-up" => "上学线路",
    "drop-off"  => "放学线路",
    "View routes" => "查看线路",
    "Pick-up point" => "上学",
    "Drop-off point" => "放学",
);
