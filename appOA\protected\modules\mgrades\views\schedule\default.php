<?php
foreach($classes as $class){
    $_cdata = array(
        'classid' => $class->classid,
        'title' => $class->title,
        'schedule_code' => $class->schedule_code,
        'room_code' => $class->introduction,
        'grade_flag' => $class->classtype,
    );
    $pageContent['classData'][$class->classid] = $_cdata;
    if(empty($class->schedule_code)){
        $pageContent['timeslot']['none'][] = $class->classid;
    }else{
        $pageContent['timeslot'][$class->schedule_code][] = $class->classid;
    }
    foreach($class->teacherInfo as $teacher){
        $pageContent['classTeacher'][$class->classid][] = $teacher->teacherid;
    }
}

$pageContent['dayLabels'] = array(
    '1' => 'Monday/周一',
    '2' => 'Tuesday/周二',
    '3' => 'Wednesday/周三',
    '4' => 'Thursday/周四',
    '5' => 'Friday/周五',
);

$pageContent['subjects'] = $programCfg['programs'];
$pageContent['teachers'] = $teachers;
$pageContent['teachersubject'] = $teachersubject;

?>

<script>
    var pageContent = <?php echo CJSON::encode($pageContent);?>;
</script>

<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo Yii::t('site','Campus Workspace');?></li>
        <li class="active"><?php echo Yii::t('campus','Course Schedule');?></li>
    </ol>

    <div class="row">
        <div class="col-md-12 mb10">
            <h4>
                <a target="_blank" class="btn btn-primary btn-lg" href="<?php echo $this->createUrl('scheduleOverview');?>" id="viewSwitch">
                    <span class="glyphicon glyphicon-th"></span> <?php echo Yii::t('campus','Course Schedule Overview');?></a>
            </h4>
        </div>
        <div class="col-md-2">
            <div id="class-timeslot-wrapper">
                <?php
                //如果有多个时间模版，且有尚未分配的班级，则显示下面内容
                ?>
                <div class="alert alert-warning no-schedule-class-wrapper" style="display: none;">
                    <h5><?php echo CommonUtils::addColon( Yii::t('campus','Classes Without Time Slot Template') );?></h5>
                    <ul class="class-assignment-list list-inline" data-attr="none">
                        <!--place holder-->
                    </ul>
                </div>
                <?php

                ?>
                <?php
                foreach($programCfg['schedules'] as $_key => $_timetpl):
                    ?>
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <?php echo CommonUtils::addColon( Yii::t('campus','Time Slot Template') );?><?php echo $_key;?>
                            <a href="javascript:;" onclick="previewTimeSlotTemplate()">
                                <span class="glyphicon glyphicon-zoom-in"></span>
                            </a>
                        </div>
                        <div class="panel-body">
                            <ul class="class-assignment-list list-inline" data-attr="<?php echo $_key;?>">
                                <!--place holder-->
                            </ul>
                            <button type="button" class="btn btn-primary" onclick="openAssign('<?php echo $_key;?>');">
                                <span class="glyphicon glyphicon-plus"></span> <?php echo Yii::t('campus','Assign Class');?></button>
                        </div>
                    </div>

                    <div style="display: none;" class="class-weekly-schedule-tpl" schedule-code="<?php echo $_key?>">
                        <div class="panel panel-default">
                            <div class="panel-heading class-title"><h4>
                                    <label></label>
                                    <a href="javascript:;" onclick="loadScheduleByClass(0, true)" class="pull-right"><span class="glyphicon glyphicon-refresh"></span></a>
                                </h4></div>
                            <div class="panel-body">
                                <table class="table table-striped">
                                    <thead>
                                    <tr>
                                        <th></th>
                                        <?php
                                        for($i=1; $i<6; $i++):
                                            ?>
                                            <th><?php echo $pageContent['dayLabels'][$i];?></th>
                                        <?php
                                        endfor;
                                        ?>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <?php
                                        foreach($_timetpl as $_timeslot):
                                    ?>
                                            <tr>
                                                <th>
                                                    <?php echo $_timeslot; ?>
                                                </th>
                                                <?php
                                                for($i=1; $i<6; $i++):
                                                ?>
                                                <td class="schedule-timeslot-item" data-timeslot="<?php echo $_timeslot; ?>" data-weekday="<?php echo $i;?>"></td>
                                                <?php
                                                endfor;
                                                ?>
                                            </tr>
                                    <?php
                                        endforeach;
                                    ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                <?php
                endforeach;
                ?>
            </div>
        </div>
        <div class="col-md-10 schedule-main-wrapper">

        </div>
    </div>
</div>

<?php

$this->renderPartial('_editTimeSlotModal', array('programCfg'=>$programCfg));
$defaultTimeSlotModel = array(
    'id' => 0,
    'branchid' => $this->branchId,
    'weekday' => 0,
	'timeslot' => '',
	'subject_flag' => '',
    'teacher_uid' => 0,
	'grade_flag' => '',
	'classid' => 0,
	'schedule_code' => '',
	'room_code' => '',
	'calendarid' => $this->branchObj->schcalendar
);
?>


<script type="text/template" id="class-item-editbtn-template">
    <li>
        <a href="javascript:;" onclick="loadScheduleByClass(<%= classid %>, false)" class-id=<%= classid %>><% print(pageContent.classData[classid].title); %></a>
        <% if(pageContent.classData[classid].schedule_code){%>
        <a href="<?php echo $this->createUrl('delAssignCla');?>&classid=<%= classid%>" class="J_ajax_del"><span class="glyphicon glyphicon-remove"></span></a>
        <% }%>
    </li>
</script>

<script type="text/template" id="class-item-content-template">
    <div>
        <h5><% print(pageContent.subjects[subject_flag]);%></h5>
        <h5><% print(pageContent.teachers[teacher_uid]);%></h5>
    </div>
</script>

<script>
    var drawClassList = null; //重绘班级列表
    var loadScheduleByClass = null; //加载某一个班级的课程安排
    var classItemBtnTemplate = _.template($('#class-item-editbtn-template').html());
    var classItemContentTemplate = _.template($('#class-item-content-template').html());
    var timeslotItemTemplate = _.template($('#timeslot-form-template').html());
    var drawScheduleTable = null; //把课程表数据render到页面
    var editTimeSlot = null; //编辑某一时间段的安排
    var editTimeSlotButton = '<a href="javascript:;" onclick="editTimeSlot(this)"><span class="glyphicon glyphicon-pencil"></span></a>';
    var currentSelectedClassId = null;
    var DefaultTimeSlotModel = Backbone.Model.extend({});
    var defaultTimeSlot = new DefaultTimeSlotModel;
    var scheduleData = {};
    defaultTimeSlot.set(<?php echo CJSON::encode($defaultTimeSlotModel);?>);

    var showNonAssignedClass = null;

    $(function(){
        showNonAssignedClass = function(){
            if(! _.isEmpty( $('.class-assignment-list[data-attr|="none"]').html() ) ){
                $('.no-schedule-class-wrapper').show();
            }else{
                $('.no-schedule-class-wrapper').hide();
            }
        }

        drawClassList = function(){
            $('ul.class-assignment-list[data-attr]').empty();
            if(!_.isEmpty(pageContent.classData) && !_.isEmpty(pageContent.timeslot)){
                _.each(pageContent.timeslot, function(item0, index0){
                    _.each(pageContent.timeslot[index0], function(item1, index1){
                        var _class = classItemBtnTemplate({classid:item1});
                        $('ul.class-assignment-list[data-attr|="'+index0+'"]').append(_class)
                    })
                })
            }
            showNonAssignedClass();
            head.Util.ajaxDel();
        }

        loadScheduleByClass = function(classid, forceRefresh){
            if(classid > 0){
                currentSelectedClassId = classid;
            }

            var schedule_code = pageContent.classData[currentSelectedClassId].schedule_code;
            if(!_.isEmpty(schedule_code)){

                //初次加载或者强制刷新
                if(_.isUndefined(scheduleData[currentSelectedClassId]) || _.isUndefined(scheduleData[currentSelectedClassId][schedule_code]) || forceRefresh){
                    $.ajax({
                        url: '<?php echo $this->createUrl('LoadScheduleDataByClass')?>',
                        type: 'post',
                        dataType: 'json',
                        data:{classid: currentSelectedClassId},
                        async: false
                    }).done(function(data){
                        if(data.state == 'success'){
                            scheduleData[currentSelectedClassId] = {};
                            scheduleData[currentSelectedClassId][data.data.schedule_code] = data.data.data;
                        }
                    });
                }
                $('div.class-weekly-schedule-tpl').hide();
                var _table = $('div.class-weekly-schedule-tpl[schedule-code|="'+schedule_code+'"]');
                _table.find('div.class-title h4 label').html(pageContent.classData[currentSelectedClassId].title);
                _table.appendTo('div.schedule-main-wrapper').show();
                var _tdItems = _table.find('td.schedule-timeslot-item');
                _tdItems.empty();
                _table.hide();

                drawScheduleTable(currentSelectedClassId, schedule_code, _tdItems);
                _table.show('blind',{},500);
                _tdItems.append(editTimeSlotButton);
            }
        }

        editTimeSlot = function(obj){
            var dataDuck = $(obj).parents('td.schedule-timeslot-item');

            //下面要判断，如果之前没有值，走下面的逻辑，如果有值，还没写
            var selectedTimeSlot = defaultTimeSlot.clone();
            selectedTimeSlot.set({
                timeslot: dataDuck.attr('data-timeslot'),
                weekday: dataDuck.attr('data-weekday'),
                grade_flag: pageContent.classData[currentSelectedClassId].grade_flag,
                schedule_code: pageContent.classData[currentSelectedClassId].schedule_code,
                room_code: pageContent.classData[currentSelectedClassId].room_code,
                classid: currentSelectedClassId,
                subject_flag: dataDuck.attr('data-subject_flag'),
                teacher_uid: dataDuck.attr('data-teacher_uid'),
                id: dataDuck.attr('data-id')
            });

            var _innerForm = timeslotItemTemplate(selectedTimeSlot.attributes);
            $('#form-timeslot-inner-wrapper').empty().html(_innerForm);
            $('#editTimeSlotModal #form-hint-title').html(
                pageContent.dayLabels[selectedTimeSlot.get('weekday')] + ' <small>'+selectedTimeSlot.get('timeslot')+'</small>');

            if(selectedTimeSlot.get('teacher_uid')==0){
                var teachers = '<div class="radio"><label><input type="radio" name="postData[teacher_uid]" value="0" checked> <?php echo Yii::t('global','N/A');?></label></div>';
            }
            else{
                var teachers = '<div class="radio"><label><input type="radio" name="postData[teacher_uid]" value="0"> <?php echo Yii::t('global','N/A');?></label></div>';
            }
            _.each(pageContent['classTeacher'][currentSelectedClassId], function(val){
                teachers += '<div class="radio J_radio"><label>';
                if(val == selectedTimeSlot.get('teacher_uid')){
                    teachers += '<input type="radio" name="postData[teacher_uid]" value="'+val+'" checked>';
                }
                else{
                    teachers += '<input type="radio" name="postData[teacher_uid]" value="'+val+'">';
                }
                teachers += pageContent['teachers'][val];
                teachers += '</label></div>';
            });
            $('#editTimeSlotModal #class-teachers').html(teachers);
            $('#postData_subject_flag').val( selectedTimeSlot.get('subject_flag') );
            $('#postData_id').val( selectedTimeSlot.get('id') );
            $('#J_fail_info').hide();
            $('#editTimeSlotModal').modal();
        }

        //打开分配窗口
        openAssign = function(key){
            if(!_.isUndefined(pageContent.timeslot['none'])){
                var html = '<div class="row">';
                _.each(pageContent.timeslot['none'], function(value){
                    html += '<div class="col-sm-12 checkbox"><label><input type="checkbox" name="cla[]" value="'+value+'">'+pageContent['classData'][value].title+'</label></div>';
                });
                html += '</div>';
                $('#schedule').val(key);
                $('#openAssign #pendingCla').html(html);
                $('#openAssign').modal();
            }
        }

        //分配保存回调
        cbAssign = function(data){
            for(var n in pageContent['timeslot'].none){
                if(in_array(pageContent['timeslot']['none'][n], data.cla)){
                    delete pageContent['timeslot']['none'][n];
                }
            }
            if(_.isUndefined(pageContent['timeslot'][data.schedule])){
                pageContent['timeslot'][data.schedule] = [];
            }
            for(var c in data.cla){
                pageContent['timeslot'][data.schedule].push(data.cla[c]);
                pageContent['classData'][data.cla[c]].schedule_code = data.schedule;
            }
            drawClassList();
            setTimeout(function(){
                $('#openAssign').modal('hide');
            }, 1000);
        }

        //取消分配回调
        cbDelAssign = function(data){
            if(_.isUndefined(pageContent['timeslot']['none'])){
                pageContent['timeslot']['none'] = [data.classid];
            }
            else{
                pageContent['timeslot']['none'].push(data.classid);
            }
            for(var c in pageContent['timeslot'][data.old_code]){
                if(data.classid == pageContent['timeslot'][data.old_code][c]){
                    delete pageContent['timeslot'][data.old_code][c];
                }
            }
            pageContent['classData'][data.classid].schedule_code = '';
            drawClassList();
        }

        drawScheduleTable = function(classid, schedule_code, tdObjs){
            $('.class-weekly-schedule-tpl td.schedule-timeslot-item').removeAttr('data-subject_flag').removeAttr('data-teacher_uid').removeAttr('data-id');
            if(!_.isEmpty(scheduleData[classid]) && !_.isEmpty(scheduleData[classid][schedule_code])){
                _.each(scheduleData[classid][schedule_code], function(item,i){
                    var _tdTarget = tdObjs.parents('table').find('td[data-timeslot|="'+item.timeslot+'"][data-weekday|="'+item.weekday+'"]');
                    _tdTarget.attr('data-subject_flag', item.subject_flag);
                    _tdTarget.attr('data-teacher_uid', item.teacher_uid);
                    _tdTarget.attr('data-id', item.id);
                    var _content = classItemContentTemplate(item);
                    _tdTarget.html(_content);
                })
            }
        }

        cbSchedule = function(data){
            if(_.isUndefined(scheduleData[data.classid][data.schedule_code])){
                scheduleData[data.classid][data.schedule_code] = {};
            }
            scheduleData[data.classid][data.schedule_code][data.id] = data;
            loadScheduleByClass(data.classid, false);
            setTimeout(function(){
                $('#editTimeSlotModal').modal('hide');
            }, 1000);
        }

        cSubject = function(_this){
            var val = $(_this).val();
            $('#class-teachers input[type="radio"]').each(function(){
                if(in_array(val, pageContent['teachersubject'][$(this).val()])){
                    $(this).parents('div.J_radio').show();
                }
                else{
                    $(this).parents('div.J_radio').hide();
                    $(this).removeAttr('checked');
                }
            });
        }

        previewTimeSlotTemplate = function(){
            $('#TimeSlotTemplate').modal();
        }

        drawClassList();
    })
</script>

<style>
    #class-teachers div.radio.J_radio{display:inline-block;width: 48%}
</style>