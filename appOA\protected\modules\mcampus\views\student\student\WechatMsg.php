<!--<h1></h1>-->
<div id="app">
    <table class="table table-hover" v-show="msg_list.length!=0">
        <tr>
            <td>first</td>
            <td>keyword1</td>
            <td>category</td>
            <td>desc</td>
            <td>flag</td>
            <td>created_at</td>
            <td>p_created_at</td>
            <td>errcode</td>
        </tr>
        <tr v-for="(item,index) in msg_list" :key="index" :class="item.errcode!=0 ? 'danger' : ''">
            <td>{{item.first}}</td>
            <td>{{item.keyword1}}</td>
            <td>{{item.category}}</td>
            <td>{{item.desc}}</td>
            <td>{{item.flag}}</td>
            <td>{{item.created_at}}</td>
            <td>{{item.p_created_at}}</td>
            <td>{{item.errcode}}</td>
        </tr>
    </table>
    <div class="mt15" v-show='msg_list.length==0'>
        <div class="alert alert-warning" role="alert"><?php echo Yii::t('ptc', 'No Data') ?></div>
    </div>
</div>
<script>
    var container = new Vue({
        "el": '#app',
        data: {
            openid:'<?php echo $openid?>',
            msg_list:[],
            title:'errcode2',
        },
        created: function () {
            this.showList()
        },
        methods: {
            showList(){
                let that = this
                $.ajax({
                    url: '<?php echo $this->createUrl('getWechatPushMsg'); ?>',
                    type: 'post',
                    dataType: 'json',
                    data: {
                        openid: this.openid
                    },
                    success: function (data) {
                        if (data.state == 'success') {
                            that.msg_list = data.data
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function (data) {
                        resultTip({
                            error: 'warning',
                            msg: '请求错误'
                        });
                    }
                })
            }
        }
    })
</script>
