<div>
    <div class='flex relative'>
        <div class='dot'></div>
        <span class='stepBorderLeft'></span>
        <div class='ml8 pb24 flex1 width90'>
            <div class='font14 color3 mb20'>
                <span class='fontBold'><?php echo Yii::t('withdrawal','Send WeChat request form');?></span> 
                <span class='textHide'>
                    <span class='bluebg cur-p ml16' onclick='expand(1)'>收起 <span class='el-icon-arrow-up'></span></span> 
                </span> 
            </div>
            <div class='overscroll'>
                <div id='wechatBox'></div>
            </div>
        </div>
    </div>
</div>
<script>
    function expand(type){
        if(type==1){
            $('.overscroll').hide()
            $('.textHide').html("<span class='bluebg cur-p ml16' onclick='expand()'>展开 <span class='el-icon-arrow-down'></span></span>")
        }else{
            $('.overscroll').show()
            $('.textHide').html("<span class='bluebg cur-p ml16' onclick='expand(1)'>收起 <span class='el-icon-arrow-up'></span></span>") 
        }
    }
    init()
    function init(){    
        let send_list=childDetails.forms[0].send_list
        var send_log={}
        var latest_send={}
        if(childDetails.forms[0].data.send_log){
            send_log=childDetails.forms[0].data.send_log
        }
        if(childDetails.forms[0].data.latest_send){
            latest_send=childDetails.forms[0].data.latest_send
        }
        var list=''
        for(let i=0;i<send_list.length;i++){
           let parentType=send_list[i].parentType=='father'?"父亲":"母亲"
           if(latest_send[send_list[i].openid]){
                list="<div class='wechatList'>"
                if(latest_send[send_list[i].openid].can_send){
                    list+="<div class='flex1'><div class='flex align-items'><img src="+send_list[i].headimgurl+" alt='' class='img28'><div class='font14 color3 ml8'>"+send_list[i].nickname+"</div><div class='font14 color6'>｜"+parentType+"</div></div><div class='font12 color6 mt10 cur-p' onclick='sendLog(this)' data-id="+send_list[i].openid+">最近一次发送："+latest_send[send_list[i].openid].date+" <span class='el-icon-arrow-right'></span></div>"
                }else{
                    list+="<div class='flex1'><div class='flex align-items'><img src="+send_list[i].headimgurl+" alt='' class='img28'><div class='font14 color3 ml8'>"+send_list[i].nickname+"</div><div class='font14 color6'>｜"+parentType+"</div></div><div class='font12 color6 mt10 cur-p' onclick='sendLog(this)' data-id="+send_list[i].openid+">最近一次发送："+latest_send[send_list[i].openid].date+" <span class='el-icon-arrow-right'></span></div>"
                }
               
                if(!latest_send[send_list[i].openid].can_send){
                    list+="<div class='mt5 yellow'>"+latest_send[send_list[i].openid].interval_date+"可再次发送</div></div></div>"
                }
            }else{
                list="<div class='wechatList'><div class='flex1'><div class='flex align-items'><img src="+send_list[i].headimgurl+" alt='' class='img28'><div class='font14 color3 ml8'>"+send_list[i].nickname+"</div><div class='font14 color6'>｜"+parentType+"</div></div><div class='font12 color9 mt10'>无发送记录</div></div></div>"
            }
            $('#wechatBox').append(list)
        }
    }
    function allChecked(){
        var objs = window.document.getElementsByName("sendList");
        for(var   i=0;i<objs.length;i++){
            if (objs[i].type == "checkbox" && objs[i].disabled==false){
                objs[i].checked = true;     
            }          
        }
    }
    function sendWechat(){
        const checkboxes = document.querySelectorAll('input[type="checkbox"][name="sendList"]:checked');
        const values = Array.from(checkboxes).map(checkbox => checkbox.value); // 将选中复选框的值放入数组
        console.log(values)
        
        $.ajax({
            url: '<?php echo $this->createUrl("sendAppForm") ?>',
            type: "post",
            dataType: 'json',
            data: {
                applicationId: container.applicationId,
                node:'application',
                task:'application_send',
                openids:values
            },
            success: function(data) {
                if (data.state == 'success') {
                    container.openModal(container.applicationId,'')
                    resultTip({
                        msg: data.state
                    });
                } else {
                    resultTip({
                        error: 'warning',
                        msg: data.message
                    });
                }
            },
            error: function(data) {},
        })
    }
    function sendLog(that){
        let list=$(that).attr('data-id')
        let log=childDetails.forms[0].data.send_log
        var html=''
        for(let i=0;i<log[list].length;i++){
            html+='<div>'+log[list][i].date+'</div>'
        }
        $('#dateList').html(html) 
        $('#dateListModal').modal('show')

    }

</script>
<style>
    .wechatList{
        height: 88px;
        background: #FAFAFA;
        border-radius: 4px;
        display:inline-block;
        padding:6px 16px;
        margin-right:16px
    }
    .img28{
        width: 28px;
        height: 28px;
        border-radius: 50%;
        object-fit: cover;
    }
    .yellow{
        color: #F0AD4E;
    }
    .overscroll{
    }
    #wechatBox{
        overflow-x: auto;        
        white-space: nowrap;
    }
</style>