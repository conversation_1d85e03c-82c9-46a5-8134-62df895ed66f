<?php

/**
 * This is the model class for table "ivy_schoolyear_stats_child".
 *
 * The followings are the available columns in table 'ivy_schoolyear_stats_child':
 * @property integer $id
 * @property string $type
 * @property integer $yid
 * @property integer $startyear
 * @property string $term
 * @property integer $startdate
 * @property integer $enddate
 * @property string $schoolid
 * @property integer $num
 * @property string $childids
 * @property string $classids
 * @property integer $updated_at
 */
class SchoolyearStatsChild extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_schoolyear_stats_child';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('type, yid, startyear, startdate, enddate, schoolid, num, childids, classids, updated_at', 'required'),
			array('yid, startyear, startdate, enddate, num, updated_at', 'numerical', 'integerOnly'=>true),
			array('type', 'length', 'max'=>7),
			array('term', 'length', 'max'=>1),
			array('schoolid', 'length', 'max'=>30),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, type, yid, startyear, term, startdate, enddate, schoolid, num, childids, classids, updated_at', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'type' => 'Type',
			'yid' => 'Yid',
			'startyear' => 'Startyear',
			'term' => 'Term',
			'startdate' => 'Startdate',
			'enddate' => 'Enddate',
			'schoolid' => 'Schoolid',
			'num' => 'Num',
			'childids' => 'Childids',
			'classids' => 'Classids',
			'updated_at' => 'Updated At',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('type',$this->type,true);
		$criteria->compare('yid',$this->yid);
		$criteria->compare('startyear',$this->startyear);
		$criteria->compare('term',$this->term,true);
		$criteria->compare('startdate',$this->startdate);
		$criteria->compare('enddate',$this->enddate);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('num',$this->num);
		$criteria->compare('childids',$this->childids,true);
		$criteria->compare('classids',$this->classids,true);
		$criteria->compare('updated_at',$this->updated_at);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return SchoolyearStatsChild the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}
