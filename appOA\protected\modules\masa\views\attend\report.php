<?php

$typeList = CommonUtils::LoadConfig('CfgASA');
$jobType = array();
foreach ($typeList['job_type'] as $k => $job_type) {
    $jobType[$k] = (Yii::app()->language == 'zh_cn') ? $job_type['cn'] : $job_type['en'];
}

$type = array();
foreach ($typeList['type'] as $k => $job_type) {
    $type[$k] = (Yii::app()->language == 'zh_cn') ? $job_type['cn'] : $job_type['en'];
}
$attendance_result = array();
foreach ($typeList['attendance_result'] as $k => $job_type) {
    $attendance_result[$k] = (Yii::app()->language == 'zh_cn') ? $job_type['cn'] : $job_type['en'];
}
?>
    <style>
        [v-cloak] {
            display: none;
        }

        .tableHr {
            margin-top: 2px;
            margin-bottom: 2px;
        }

        .btnLoading {
            height: 12px;
            width: 12px;
            vertical-align: text-top;
            background: url(/themes/blue/images/loading.gif) no-repeat scroll center center transparent;
            display: inline-block;
        }

        .reportTbody > tr > td, .reportTbody > tr > th {
            vertical-align: middle !important;
            text-align: center;
        }

        .glyphicon {
            transition: .5s;
        }

        .reportAmendCheckbox {
            cursor: pointer;
        }

        .rotate_half {
            transform: rotate(-180deg);
            -ms-transform: rotate(-180deg);
            -moz-transform: rotate(-180deg);
            -webkit-transform: rotate(-180deg);
        }
    </style>
    <div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('//mcampus/default/index')) ?></li>
        <li><?php echo CHtml::link(Yii::t('site', 'Routines'), array('//mcampus/default/index')) ?></li>
        <li class="active"><?php echo Yii::t('site', 'ASA Management') ?></li>
    </ol>

    <div class="row">
        <div class="col-md-2 col-sm-2 mb10">
            <?php
            $this->widget('zii.widgets.CMenu', array(
                'items' => $this->module->getMenu(),
                'id' => 'pageCategory',
                'htmlOptions' => array('class' => 'nav nav-pills nav-stacked background-gray'),
                'activeCssClass' => 'active',
            ));
            ?>
        </div>
        <div class="col-md-10 col-sm-12">
            <?php
            $this->widget('zii.widgets.CMenu', array(
                'items' => $this->module->getAttendMenu(),
                'id' => 'AttendMenu',
                'htmlOptions' => array('class' => 'nav nav-tabs'),
                'activeCssClass' => 'active',
            ));
            ?>
            <!-- Tab panes -->
            <div class="tab-content">
                <div class="col-md-12 mb10" id="attendReportVue" v-cloak>
                    <div v-if="isEmptyObj(reportData)">
                        <div class="">
                            <div class="panel-heading"><h4>有追加的签到：<span class="badge">{{reportData.length}}</span></h4>
                            </div>

                            <div class="table-responsive" id="schedule-staff">
                                <table valign="middle" class="table table-hover">
                                    <colgroup>
                                        <col width="150">
                                        <col width="150">
                                        <col width="150">
                                        <col width="150">
                                        <col width="150">
                                    </colgroup>
                                    <thead>
                                    <tr>
                                        <th>
                                            <input type="checkbox" class="J_check_all" data-direction="y"
                                                   @click="checkAll()"
                                                   data-checklist="J_check_i1"/>
                                            课程
                                        </th>
                                        <th>老师姓名</th>
                                        <th>开始时间</th>
                                        <th>结束时间</th>
                                    </tr>
                                    </thead>
                                    <tbody class="J_check_wrap">
                                    <tr v-for="rData in reportData">
                                        <td>
                                            <input type="checkbox" class="J_check" :value="rData.id"
                                                   v-model="reportDataCheck" data-yid="J_check_i1" name=""/>
                                            {{rData.course}}
                                        </td>
                                        <td>{{rData.vendor}}</td>
                                        <td>{{rData.service_start}}</td>
                                        <td>{{rData.service_end}}</td>
                                    </tr>
                                    </tbody>
                                </table>
                                <div class="keys" style="display:none"
                                     title="/masa/course/index?groupId=18&amp;branchId=BJ_DS">
                                    <span>50</span><span>52</span><span>54</span>
                                </div>
                                <div style="clear: both"></div>
                            </div>
                            <div class="panel-footer form-inline">
                                <select class="form-control" v-model="checkYear">
                                    <option value="">请选择年份</option>
                                    <option :value="year" v-for="(data,year) in reportList">{{year}}</option>
                                </select>
                                <select class="form-control selectMon">
                                    <option value="0">请选择月份</option>
                                    <option :value="num" v-for="num in 12">{{num}}月</option>
                                </select>
                                <button type="button" class="btn btn-default"
                                        @click="repatch()"><?php echo Yii::t('global', 'Submit'); ?></button>
                            </div>
                        </div>
                        <hr/>
                    </div>
                    <br>
                    <div v-for="year in yearData" class="panel panel-default">
                        <div class="panel-heading">
                            {{year}}年
                        </div>
                        <div class="panel-body">
                        <div class="">
                            <h5 class="col-md-12">月考勤数据：</h5>

                            <div class="btn-group dropup mr10 mb10" v-for="(list,mon) in reportList[year]">
                                <button type="button" class="btn"
                                        :class="{ 'btn-default': list.status == 10 , 'btn-success': list.status == 20 || list.status == 30 }"
                                        @click="create(year,mon,list.status,'general','')">{{mon}}月 <span
                                        v-if="list.status == 0">（未生成）</span><span class="btnLoading"
                                                                                  v-if="list.status == 99"></span><span
                                        v-if="list.amount">（¥ {{list.amount}} ）</span>
                                    <span class="glyphicon glyphicon-ok" aria-hidden="true"
                                          v-if="list.status == 20 || list.status == 30  "></span>
                                </button>
                                <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown"
                                        aria-haspopup="true" aria-expanded="false" v-if="list.status == 10">
                                    <span class="caret"></span>
                                </button>
                                <ul class="dropdown-menu" v-if="list.status == 10">
                                    <li><a data-toggle="modal" @click="showReport(year,mon,'show')">查看详细列表</a></li>
                                    <li><a class="" style="color:#a94442"
                                           @click="create(year,mon,list.status,'general','recreate')">重新生成</a></li>
                                </ul>
                            </div>
                        </div>
                        </div>
                    </div>
                    <div class="modal fade" id="attendReportModal" tabindex="-1" role="dialog"
                         data-backdrop="static" data-keyboard="false" aria-labelledby="myModalLabel">
                        <div class="modal-dialog modal-lg" style="width: 90%" role="document">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <button type="button" class="close" data-dismiss="modal"
                                            aria-label="Close"><span
                                            aria-hidden="true">&times;</span></button>
                                    <h4 class="modal-title"><span id="myModalLabel"></span>月</h4>
                                </div>
                                <div class="modal-body">
                                    <div class="table-responsive">
                                        <table class="table table-bordered" style="vertical-align: middle;">
                                            <thead>
                                            <tr>
                                                <th colspan="4"><h4 class="text-center">校园：{{tableData.school}}</h4>
                                                </th>
                                                <th colspan="5"><h4 class="text-center">
                                                        考勤区间：{{tableData.report_month}}</h4></th>
                                                <th><h4 class="text-center">最后更新：{{tableData.update}}</h4>
                                                </th>
                                            </tr>
                                            </thead>
                                            <tbody class="reportTbody">
                                            <tr>
                                                <th width="50">NO.</th>
                                                <td width="100">姓名<br/>Chinese Name</td>
                                                <td width="100">身份证号码 / 护照号码<br/>ID Card # / Password #</td>
                                                <td width="100">老师职位</td>
                                                <td width="100">性质<br/>Source</td>
                                                <td width="100">课程<br/>Activity</td>
                                                <td width="150">课时小计<br>
                                                    课时费 × 课时 = 小计<br/>
                                                    (Cost × Activities = Total Cost)
                                                </td>
                                                <td width="100">调整金额<br/>Modify Amount</td>
                                                <td width="100">总计<br/>Total Cost</td>
                                                <td width="100">开户行预留姓名 / 银行账号<br/> / Bank Account</td>
                                            </tr>
                                            <tr v-for="(tData,id,index) in tableData.vendors">
                                                <th width="50">{{index + 1}}</th>
                                                <td width="100">{{tData.name}}<br/>{{tData.name_en}}</td>
                                                <td width="100">'{{tData.identity}}</td>
                                                <td width="100">{{tData.type}}<br/>{{tData.type_en}}</td>
                                                <td width="100">
                                                    <div v-for="(course,cid,index) in tData.courses">
                                                        <span>{{course.position}}<br/>{{course.position_en}}</span>
                                                        <hr v-if="index + 1 < arrLength(tData.courses)"
                                                            class="tableHr"/>
                                                    </div>
                                                </td>
                                                <td width="100">
                                                    <div v-for="(course,cid,index) in tData.courses">
                                                        <span>{{course.course_name}}<br/>{{course.course_name_en}}</span>
                                                        <hr v-if="index + 1 < arrLength(tData.courses)"
                                                            class="tableHr"/>
                                                    </div>
                                                </td>
                                                <td width="100">
                                                    <div v-for="(course,cid,index) in tData.courses">
                                                        {{course.unit_price}} × {{course.course_count}} =
                                                        {{parsefloat(course.unit_price) * course.course_count}}
                                                        <hr v-if="index + 1 < arrLength(tData.courses)"
                                                            class="tableHr"/>
                                                    </div>
                                                </td>
                                                <td class="" v-if="general" style="text-align: left" width="150">
                                                    <h5 class="reportAmendCheckbox text-center text-primary"><i
                                                            class="glyphicon glyphicon-chevron-up"
                                                            :class="{ 'rotate_half': tData.variation_amount || tData.variation_memo}"></i>
                                                        修改此项</h5>

                                                    <div class="reportAmend"
                                                         v-show="tData.variation_amount || tData.variation_memo">
                                                        <div class="input-group mb10">
                                                            <span class="input-group-addon"
                                                                  style="padding: 6px 6px;">¥</span>
                                                            <input type="number"
                                                                   :value="tData.variation_amount" placeholder="可输入负数"
                                                                   :id="'reportAmendAmount' + id "
                                                                   class="form-control input-sm"/>
                                                        </div>
                                                        <textarea name="" :id="'reportAmendText' + id"
                                                                  placeholder="备注"
                                                                  :value="tData.variation_memo"
                                                                  class="form-control mb10">

                                                        </textarea>
                                                        <input type="checkbox"
                                                               :checked="tData.variation_settle != 0"
                                                               :id="'reportAmendSettlement' + id "
                                                               class=""/>是否结算
                                                        <a href="javascript:;" @click="tableVariation(id)"
                                                           :id="'reportAmendAjaxSubmit' + id "
                                                           class="btn btn-primary btn-lg btn-block btn-xs"><?php echo Yii::t('global', '提交'); ?>
                                                        </a>
                                                    </div>

                                                </td>
                                                <td width="100" v-if="!general">
                                                    <h5 v-if="tData.variation_amount">
                                                        金额：{{tData.variation_amount}}

                                                    </h5>
                                                    <h5 v-if="tData.variation_memo">备注：
                                                        {{tData.variation_memo}}
                                                    </h5>
                                                </td>
                                                <td width="100" v-if="tData.variation_amount">
                                                    {{parsefloat(tData.amount) +
                                                    parsefloat(tData.variation_amount)}}
                                                </td>
                                                <td width="100" v-else>{{tData.amount}}</td>

                                                <td width="100">{{tData.bank_title}}<br/>'{{tData.bank_account}}</td>
                                            </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                    <a :href="'<?php echo $this->createUrl('printreport'); ?>&reportId=' + tableData.report_id"
                                       target="_blank" type="button" class="btn_submit pull-right">打印</a>

                                    <div style="clear: both"></div>
                                    <div class="checkbox pull-right" v-if="general">
                                        <label>总额：{{tableData.total_amount}}</label>
                                        <label>未结算：{{tableData.unsettle_amount}}</label>
                                        <br>
                                        <label>
                                            <input v-model="canSub" type="checkbox"> 确认该列表无误,提交后不可修改
                                        </label>
                                    </div>
                                    <div style="clear: both"></div>
                                </div>
                                <div class="modal-footer" v-if="general">
                                    <input type="reset" class="_reset" style="display: none"/>
                                    <button type="submit" :class="{ 'disabled': !canSub}" v-bind:disabled="!canSub"
                                            class="btn btn-primary"
                                            @click="ReportSubmit()"><?php echo Yii::t('global', 'Submit'); ?></button>
                                    <button type="button" class="btn btn-default"
                                            data-dismiss="modal"><?php echo Yii::t('global', 'Cancel'); ?></button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
    <script>
        var reportList = <?php echo CJSON::encode($list); ?>;   //考勤汇总数据
        var reportData = <?php echo CJSON::encode($data); ?>;   //有追加的数据
        var yearData = <?php echo CJSON::encode($yearArray); ?>;   //年份数据
        var attendReportVue = new Vue({
            el: "#attendReportVue",
            data: {
                reportList: reportList,
                reportData: reportData,
                yearData: yearData,
                reportDataCheck: [],    //选中的补签数据
                years: '',
                month: '',       //操作的是哪个月
                tableData: '',  //列表数据
                reportId: '',  //列表ID
                canSub: false,  //是否让用户提交
                general: true,    //判断是否仅查看列表
                checkYear:''
            },
            created: function () {

            },
            updated: function () {

            },
            methods: {
                isEmptyObj: function (obj) {
                    if (Object.keys(obj).length > 0) {
                        return true;
                    }
                    return false;
                },
                addedNum: function (num) {
                    var _num;
                    if (num < 10) {
                        _num = '0' + num;
                    } else {
                        _num = num
                    }
                    return _num
                },
                arrLength: function (arr) {
                    return Object.keys(arr).length;
                },
                parsefloat: function (num) {
                    return parseFloat(num);
                },
                repatch: function (attendanceId) {
                    var _this = this;
                    var _mon = this.addedNum($('.selectMon').val());
                    if (_mon == 0 || !_this.isEmptyObj(_this.reportDataCheck) || _this.checkYear == '') {
                        return false;
                    }
                    $.ajax({
                        url: '<?php echo $this->createUrl('repatch'); ?>',
                        data: {month: _this.checkYear + '' + _mon, attendanceId: _this.reportDataCheck},
                        type: 'post',
                        dataType: 'json',
                        success: function (data) {
                            if (data.state == 'success') {
                                alert('success');
                                location.reload();
                            } else {
                                alert(data.message)
                            }
                        },
                        error: function (data) {
                        }
                    });
                },
                create: function (year, mon, status, action, recreate) {
                    if (status == 10 && recreate != 'recreate') {
                        return false;
                    }
                    var _this = this;
                    if (status == 20 || status == 30) {   //仅查看
                        $.ajax({
                            url: '<?php echo $this->createUrl('showReport'); ?>',
                            data: {month: year + '' + mon, action: 'show'},
                            type: 'post',
                            dataType: 'json',
                            success: function (data) {
                                $('#myModalLabel').text(data.report_month);
                                _this.tableData = data;
                                _this.general = false;
                                _this.years = year;
                                $('#attendReportModal').modal();
                            },
                            error: function (data) {
                            }
                        });
                    } else {         //生成
                        $.each(_this.reportList[year], function (i, list) {
                            if (i == mon) {
                                list.status = 99;
                            }
                        });
                        $.ajax({
                            url: '<?php echo $this->createUrl('showReport'); ?>',
                            data: {month: year + '' + mon, action: action},
                            type: 'post',
                            dataType: 'json',
                            success: function (data) {
                                if (data.state == 'success') {
                                    $.each(_this.reportList[year], function (i, list) {
                                        if (i == mon) {
                                            list.status = 10;
                                            list.amount = data.data.total_amount;
                                        }
                                    });
                                    //console.log(data.data.reportRepatchDate);
                                    if(typeof data.data.reportRepatchDate != "undefined"){
                                        var html = "";
                                        $.each(data.data.reportRepatchDate, function (i, list) {
                                            html += "<div>"+ i +"</div>"
                                            html += "<table class='table table-bordered'>"
                                            $.each(list, function (ia, lists) {
                                                html += "<tr>"
                                                html += "<td>"
                                                html += lists.name
                                                html += "</td>"
                                                html += "<td>"
                                                html += lists.title
                                                html += "</td>"
                                                html += "</tr>"
                                            });
                                            html += "</table>"
                                        });
                                        $('#model_repatch .modal-body p').html(html);
                                        $('#model_repatch').modal('show')
                                    }
                                    resultTip({"msg": "create success"})
                                }
                            },
                            error: function (data) {
                            }
                        });
                    }
                }, showReport: function (year, mon, action) {
                    var _this = this;
                    this.month = mon;
                    this.years = year;
                    this.canSub = false;
                    $.ajax({
                        url: '<?php echo $this->createUrl('showReport'); ?>',
                        data: {month: year + '' + mon, action: action},
                        type: 'post',
                        dataType: 'json',
                        success: function (data) {
                            $('#myModalLabel').text(data.report_month);
                            _this.reportId = data.report_id;
                            _this.tableData = data;
                            _this.general = true;
                            $('#attendReportModal').modal();

                        },
                        error: function (data) {
                        }
                    });
                }, tableVariation: function (id) {
                    var _this = this;
                    $('#reportAmendAjaxSubmit' + id).addClass('disabled').attr('disabled', 'disabled');
                    var _amount = $('#reportAmendAmount' + id).val();
                    var _memo = $('#reportAmendText' + id).val();
                    var _settle = $('#reportAmendSettlement' + id).is(':checked') ? 1 : 0;
                    $.ajax({
                        url: '<?php echo $this->createUrl('variation'); ?>',
                        data: {reportId: _this.reportId, vendorId: id, amount: _amount, memo: _memo, settle: _settle},
                        type: 'post',
                        dataType: 'json',
                        success: function (data) {
                            $('#reportAmendAjaxSubmit' + id).removeClass('disabled').removeAttr('disabled');
                            if (data.state == 'success') {
                                alert('价格调整成功');
                                Vue.set(_this.tableData.vendors[id], 'variation_amount', _amount);
                                Vue.set(_this.tableData.vendors[id], 'variation_memo', _memo);
                                Vue.set(_this.tableData.vendors[id], 'variation_settle', _settle);
                                Vue.set(_this.tableData.vendors[id], 'total_amount', data.data.total_amount);
                                var reportbBtnAmount = parseFloat((data.data.total_amount)).toFixed(2);
                                Vue.set(_this.reportList[_this.years][_this.month], 'amount', reportbBtnAmount);

                            } else {
                                alert(data.message)
                            }
                        },
                        error: function (data) {
                            $('#reportAmendAjaxSubmit' + id).removeClass('disabled').removeAttr('disabled');
                        }
                    });
                }
                , ReportSubmit: function () {
                    var _this = this;
                    $.ajax({
                        url: '<?php echo $this->createUrl('reportSubmit'); ?>',
                        data: {month: _this.years + '' + _this.month},
//                        data: {month: _this.years + ''},
                        type: 'post',
                        dataType: 'json',
                        success: function (data) {
                            if (data.state == 'success') {
                                $.each(_this.reportList[_this.years], function (i, list) {
                                    if (i == _this.month) {
                                        list.status = 20;
                                    }
                                });
                                $('#attendReportModal').modal('hide');
                                location.reload();
                            } else {
                                alert(data.message)
                            }
                        },
                        error: function (data) {
                            alert(data.state)
                        }
                    });
                }, checkAll: function () {
                    var _this = this;
                    _this.reportDataCheck.splice(0, _this.reportDataCheck.length);
                    if ($('.J_check_all').is(':checked')) {
                        $.each($('#attendReportVue .J_check'), function (i, check) {
                            _this.reportDataCheck.push($(check).attr('value'));
                        });
                    } else {
                        _this.reportDataCheck.splice(0, _this.reportDataCheck.length);
                    }
                }
            },
            computed: {}
        });
        $(document).on('click', '.reportAmendCheckbox', function () {
            if ($(this).find('i').hasClass('rotate_half')) {
                $(this).find('i').removeClass('rotate_half');
            } else {
                $(this).find('i').addClass('rotate_half');
            }
            $(this).siblings('.reportAmend').slideToggle(200);
        })

        var reportAmendAjax = function (id) {
            $('#reportAmendAjaxSubmit' + id).prop('disabled', 'disabled');
            var _amount = $('#reportAmendAmount' + id).val();
            var _text = $('#reportAmendText' + id).val();
            $.ajax({
                url: '<?php echo $this->createUrl('iii'); ?>',
                data: {amount: _amount, text: _text},
                type: 'post',
                dataType: 'json',
                success: function (data) {
                    resultTip({"msg": "保存成功"})
                    $('#reportAmendAjaxSubmit' + id).removeProp('disabled');
                    if (data.state == 'success') {
                        resultTip({"msg": "保存成功"})
                    } else {
                    }
                },
                error: function (data) {

                    resultTip({"msg": "保存失败"})
                }
            });
        }
    </script>
<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>

<div class="modal fade" id="model_repatch" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title">本月追加记录</h4>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning" role="alert">本月签到老师已追加到其他月，有问题请联系IT部门</div>
                <p></p>
            </div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div>
