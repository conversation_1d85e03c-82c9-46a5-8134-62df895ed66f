<?php
$week = Yii::app()->request->getParam('week', '');
$monday = Yii::app()->request->getParam('monday', '');
$type = Yii::app()->request->getParam('type', '');
$mealType = ($type == 'menu') ? '正常餐' : '特殊餐';
$timeday = date("Y-m-d", $monday) . ' - '. date("Y-m-d", $monday+4*86400);

?>
<style>
	.ml0 {
		margin-left: 0 !important
	}

	[v-cloak] {
		display: none;
	}
</style>
<div class="container-fluid">
	<ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site','Campus Operations'), array('//mcampus/default/index'))?></li>
        <li><?php echo CHtml::link(Yii::t('site','Campus Support'), array('//mcampus/default/index'))?></li>
		<li><?php echo CHtml::link(Yii::t('site','周食谱管理'), array('//mcampus/catering/weeklySchedule'))?></li>
		<li class="active">第<?php echo $week . "周" ?></li>
	</ol>
	<div class="row" id='lunch' v-cloak>
		<div class="col-md-12" >
			<div class="panel panel-default">
			    <div class="panel-heading">
						<button v-if='mealData.status!=1' class="btn btn-primary btn-xs" @click='copy(<?php echo Yii::app()->request->getParam('monday', ' '); ?>)'><?php echo Yii::t('lunch', 'Copy The Menu')?></button>
						<div v-else>餐饮</div>
			    </div>
			    <div class="panel-body">
			   		<form id='formData'>
						<div class="col-md-6">
							<div class="form-horizontal">
								<input type="text" value="<?php echo Yii::app()->request->getParam('week', ''); ?>" name='week' hidden>
								<input type="text" value="<?php echo Yii::app()->request->getParam('id', ''); ?>" name='id' hidden>
								<input type="text" value="<?php echo Yii::app()->request->getParam('monday', ''); ?>" name='monday' hidden>
								<input type="text" value="<?php echo Yii::app()->request->getParam('branchId', ''); ?>" name='branchId' hidden>
								<input type="text" value="<?php echo Yii::app()->request->getParam('type', ''); ?>" name='type' hidden>
								<div class="form-group">
								    <label class="col-sm-2 control-label">第<?php echo $week . "周" ?></label>
								    <div class="col-sm-10">
								      <p class="form-control-static"><?php echo $mealType ?>  （<?php echo $timeday ?>）</p>
								    </div>
								</div>
								<div class="form-group">
									<label for="inputPassword3" class="col-sm-2 control-label"><?php echo Yii::t('lunch', 'Meals Included')?></label>
									<div class=" col-sm-10">
										<p>
											<label class="checkbox-inline"  v-for='(lunchData,key,index) in lunchs'>
												<input type="checkbox" id="inlineCheckbox1" @change="checkNumber(key)" :value="key" v-model='lunchdata' name='menu_cate[]'>
										    	{{lunchData}}
											</label>
										</p>
									</div>
								</div>
								<div class="form-group">
									<label for="inputPassword3" class="col-sm-2 control-label">选择周</label>
									<div class=" col-sm-10">
										<p>
											<label class="checkbox-inline" v-for='(week,key,index) in weekdays'>
												<input type="checkbox" id="inlineCheckbox1" @change="checkWeek(key)" :value="key" v-model='weekDatas'>
										    	{{week.weeks}}
											</label>
										</p>
									</div>
								</div>
								<p>
									<input type="checkbox" id="inlineCheckbox1" :value="weekdays[week].en" name='week_cate[]'  v-for='(week,idx) in sortWeek' checked hidden>
								</p>
								<div class="form-group">
									<label for="inputPassword3" class="col-sm-2 control-label"><?php echo Yii::t('lunch', 'Nutrition')?></label>
									<div class="col-sm-10">
										<textarea class="form-control" rows="8" name='nutrition' disabled="disabled" :value='mealData.nutrition' v-if='mealData.status==1'></textarea>
										<textarea class="form-control" rows="8" name='nutrition' :value='mealData.nutrition' v-else></textarea>
									</div>
								</div>
								<div class="form-group">
									<label for="inputPassword3" class="col-sm-2 control-label"><?php echo Yii::t('lunch', 'Memo')?></label>
									<div class="col-sm-10">
										<textarea class="form-control" rows="8" name='memo' :value='mealData.memo' v-if='mealData.status==1'  disabled="disabled"></textarea>
										<textarea class="form-control" rows="8" name='memo' :value='mealData.memo' v-else></textarea>
									</div>
								</div>
							</div>
						</div>
						<div class="col-md-12">
							<table class="table mt15" v-if='sortItems.length!=0' :key='menuKey'>
								<thead>
									<tr>
										<th width="4%"></th>
										<th width="10%" v-for='(week,idx) in sortWeek'>
											{{weekdays[week].weeks}}
										</th>
									</tr>
								</thead>
								<tbody>
									<tr v-for='(lunch,index) in sortItems'>
										<th>{{lunchs[lunch]}}</th>
										<td v-for='(week,idx) in sortWeek'>
											<textarea class="form-control mb10" rows="8" :name="'data['+lunch +']['+weekdays[week].en+']'" :id="'menu_'+lunch+'_'+weekdays[week].en+''" v-if='mealData.length!=0 && mealData.detail[lunch] && mealData.detail[lunch][weekdays[week].en]' :value='mealData.detail[lunch][weekdays[week].en].content'></textarea>
											<textarea class="form-control mb10" rows="8" :name="'data['+lunch+']['+weekdays[week].en+']'" :id="'menu_'+lunch+'_'+weekdays[week].en+''" v-else></textarea>
											<div class="mb10 text-center" :id="'img-'+lunch+''+weekdays[week].en+''" v-if='mealData.length!=0 && mealData.menu_id'>
												<img :src="mealData.detail[lunch][weekdays[week].en].photo" style="width:100%" v-if='mealData.length!=0 && mealData.detail[lunch]  && mealData.detail[lunch][weekdays[week].en] && mealData.detail[lunch][weekdays[week].en].photo'>
											</div>
											<p class="text-center" v-if='mealData.length!=0 && mealData.menu_id'  @click.stop>
												<button class="btn btn-primary btn-xs" type="button" :id="'upload-'+lunch+''+weekdays[week].en+''"><?php echo Yii::t('lunch', 'Upload Photos');?></button>
												<button type="button" class="btn btn-danger btn-xs" @click='delPht(lunch,weekdays[week].en)'>
													<?php echo Yii::t('lunch', 'Delete Photos')?>
												</button>
											</p>
										</td>
									</tr>
								</tbody>
							</table>
							<p class="text-center mt15"><button type="button" class="btn btn-primary" @click='subData()' id='subbtn'><?php echo Yii::t('reg', 'Submit')?></button></p>
						</div>
					</form>
			    </div>
			</div>
		</div>
		<div class="modal fade" tabindex="-1" role="dialog" id='copyWeek' data-backdrop="static" data-keyboard="false">
			<div class="modal-dialog" role="document">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
						<h4 class="modal-title"><?php echo Yii::t('lunch', 'Copy The Menu')?></h4>
					</div>
					<div class="modal-body">
						<div class="form-horizontal">
							<div class="form-group">
								<label for="inputEmail3" class="col-sm-2 control-label">复制周</label>
								<div class="col-sm-10">
									<select class="form-control" v-model="provinceId">
										<option value=""><?php echo Yii::t('reg', 'Choose')?></option>
										<option v-for='(week,index) in copyweek' :value='week'>Week {{week.title}} （{{week.time}}）</option>
									</select>
								</div>
							</div>
							<div class="form-group" v-if='provinceId'>
								<label for="inputEmail3" class="col-sm-2 control-label">餐类</label>
								<div class="col-sm-10">
									<label class="radio-inline">
									    <input type="radio" name='menu_id' :value='provinceId.menu_id'>正常餐
									</label>
									<label class="radio-inline" v-if='provinceId.allergy_id!=0'>
									    <input type="radio" name='menu_id' :value='provinceId.allergy_id'><?php echo Yii::t('lunch', 'Special Requirements')?>
									</label>
								</div>
							</div>
						</div>
					</div>
					<div class="modal-footer">
						<button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('global', 'Close')?></button>
						<button type="button" class="btn btn-primary" @click='copyData()'><?php echo Yii::t('global', 'OK')?></button>
						<span id="J_fail_info" class="text-warning"></span>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
<script>
	var lunchs = <?php echo json_encode($lunchs); ?>;
	var mealData = <?php echo json_encode($mealData); ?>;
	console.log(mealData)
	var lunch = new Vue({
		el: "#lunch",
		data: {
			lunchs: lunchs,
			lunchdata: [],
			mealData: mealData,
			copyweek:'',
			provinceId:'',
			datas:[],
			menuKey:1,
			weekdays:{
				"1": {
					"en": "mon",
					"weeks": "<?php echo Yii::t('attends', 'Mon')?>"
				},
				"2": {
					"en": "tue",
					"weeks": "<?php echo Yii::t('attends', 'Tue')?>"
				},
				"3": {
					"en": "wed",
					"weeks": "<?php echo Yii::t('attends', 'Wed')?>"
				},
				"4": {
					"en": "thu",
					"weeks": "<?php echo Yii::t('attends', 'Thu')?>"
				},
				"5": {
					"en": "fri",
					"weeks": "<?php echo Yii::t('attends', 'Fri')?>"
				},
				"6": {
					"en": "sat",
					"weeks": "<?php echo Yii::t('attends', 'Sat')?>"
				},
				"7": {
					"en": "sun",
					"weeks": "<?php echo Yii::t('attends', 'Sun')?>"
				}
			},
			weekDatas:[],
		},
		computed: {
			sortItems: function() {
				return this.lunchdata.sort(sortNumber);
			},
			sortWeek: function() {
				return this.weekDatas.sort(sortNumber);
			}
		},
		created: function() {
			if(mealData.length != 0) {
				this.load()
				for(var i = 0; i < mealData.menu_cate.length; i++) {
					this.lunchdata.push(mealData.menu_cate[i])
				}
				if(!mealData.week_cate){
					for(var i=0;i<5;i++){
						this.weekDatas.push(i+1)
					}
				}else{
					for(week in this.weekdays){
						for(var i = 0; i < mealData.week_cate.length; i++) {
							if(this.weekdays[week].en==mealData.week_cate[i]){
								this.weekDatas.push(week)
							}
						}
					}
				}
			}else{
				for(var i=0;i<5;i++){
					this.weekDatas.push(i+1)
				}
			}
		},
		methods: {
			load:function(){
				this.$nextTick(function(){
					for(var k in this.lunchdata){
					    var lkey = this.lunchdata[k];
					    for(var i in this.weekdays){
					        config['browse_button'] = 'upload-'+lkey+this.weekdays[i].en;
					        config['multipart_params'] = {lkey: lkey , weekday: this.weekdays[i].en, id: this.mealData.menu_id};
					        var uploader = new plupload.Uploader(config);
					        uploader.init();
					    }
					}
				})
			},
			checkNumber:function(key){
				if(this.mealData.status==1){
					for(var i = 0; i < mealData.menu_cate.length; i++) {
						this.lunchdata.push(mealData.menu_cate[i])
					}
					console.log(this.lunchdata)
					this.lunchdata=mealData.menu_cate
				}else{
					if(mealData.length!=0){
						++this.menuKey
						this.load()
					}
				}
			},
			checkWeek:function(key){
				if(this.mealData.status==1){
					if(mealData.week_cate){
						var weekNum=[]
						for(week in this.weekdays){
							for(var i = 0; i < mealData.week_cate.length; i++) {
								if(this.weekdays[week].en==mealData.week_cate[i]){
									weekNum.push(week)
								}
							}
						}
						this.weekDatas=weekNum
					}else{
						var defWeek=[]
						for(var i=0;i<5;i++){
							defWeek.push(i+1)
						}
						this.weekDatas=defWeek
					}
				}else{
					if(mealData.length!=0){
						++this.menuKey
						this.load()
					}
				}
			},
			copy:function(monday) {
				$.ajax({
					url: '<?php echo $this->createUrl("codoList")?>',
					type: "post",
					dataType: 'json',
					data: {
						monday: monday
					},
					success: function(res) {
						lunch.copyweek=res
						$('#copyWeek').modal('show')
					},
					error: function(res) {
						alert("请求错误")
					}
				});
			},
            copyData:function(){
            	$.ajax({
					url: '<?php echo $this->createUrl("MenuData")?>',
					type: "post",
					dataType: 'json',
					data: {
						menu_id: $("input[name='menu_id']:checked").val()
					},
					success: function(res) {
						if(lunch.provinceId==''){
							$('#J_fail_info').html('<i class="glyphicon glyphicon-remove text-warning"></i>请选择复制周')
						}else if($("input[name='menu_id']:checked").val()==undefined){
							$('#J_fail_info').html('<i class="glyphicon glyphicon-remove text-warning"></i>请选择餐类')
						}else{
							$('#J_fail_info').html('')
							lunch.lunchdata=[]
							lunch.weekDatas=[]
							Vue.set(lunch,'mealData',res)
							if(res.length!=0){
								for(var i = 0; i < res.menu_cate.length; i++) {
									lunch.lunchdata.push(res.menu_cate[i])
								}
								if(!res.week_cate){
									for(var i=0;i<5;i++){
										lunch.weekDatas.push(i+1)
									}
								}else{
									for(week in lunch.weekdays){
										for(var i = 0; i < res.week_cate.length; i++) {
											if(lunch.weekdays[week].en==res.week_cate[i]){
												lunch.weekDatas.push(week)
											}
										}
									}
								}
							}else{
								lunch.lunchdata=[]
								for(var i=0;i<5;i++){
									lunch.weekDatas.push(i+1)
								}
							}
							$('#copyWeek').modal('hide')
						}

					},
					error: function(res) {
						alert("请求错误")
					}
				});
            },
			subData:function() {
				$("#subbtn").attr('disabled',true);
				$.ajax({
					url: '<?php echo $this->createUrl("saveNew")?>',
					type: "post",
					dataType: 'json',
					data: $('#formData').serialize(),
					success: function(res) {
						if(res.state=="success"){
							resultTip({
								msg: res.message
							});
							setTimeout(function(){
								window.location.href='<?php echo $this->createUrl("weeklySchedule")?>'
							},1000)
						}else{
							$("#subbtn").attr('disabled',true);
							resultTip({msg: err.message, error: 1});
						}
					},
					error: function() {
						$("#subbtn").attr('disabled',true);
						alert("请求错误")
					}
				});
			},
			delPht:function(key,week){
				console.log(week)
				$.ajax({
					url: '<?php echo $this->createUrl("delPhoto")?>',
					type: "post",
					dataType: 'json',
					data:{
						lkey:key,
						id:mealData.menu_id,
						weekday:week
					},
					success: function(res) {
						if(res.state=="success"){
							resultTip({
								msg: res.message
							});
							$('#img-' + res.data.lkey + res.data.weekday).html('');
						}else{
							resultTip({msg: err.message, error: 1});
						}
					},
					error: function() {
						alert("请求错误")
					}
				});
			}
		}
	})
	function sortNumber(a, b) {
		return a - b
	}
	 var config = {
        runtimes : 'html4',
        url : '<?php echo $this->createUrl('upload')?>',
        filters : {
            max_file_size : '10mb',
            mime_types: [
                {title : "<?php echo Yii::t('teaching', 'Image files');?>", extensions : "jpg,gif,png,jpeg"}
            ]
        },
        init: {
            FilesAdded: function(up, files) {
                up.start();
            },
            Error: function(up, err) {
                resultTip({msg: err.message, error: 1});
            },
            FileUploaded: function(up, file, info) {

                var response = eval('('+info.response+')');
                if(info.status == 200){
                    var img = new Image();
                    img.className = 'img-rounded';
                    img.style.width = '100%';
                    img.src = response.data.photo;
                    img.onload = function(){
                        $('#img-'+response.data.lkey+response.data.weekday).html(img);
                    }
                    resultTip({msg: response.message});
                }
            }
        }
    };
</script>
