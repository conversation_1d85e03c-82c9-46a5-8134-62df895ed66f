<div>
    <div class='flex align-items  mb24 mt10'>
        <span class='font14 color3'>处理人：</span>
        <div class='flex1 flex align-items lib_class_handle'>
        </div>
    </div>
    <form class="J_ajaxForm formStu" action="<?php echo $this->createUrl("saveNode");?>" method="post"  onsubmit="return check(this)">
        <div >    
            <label class="checkbox-inline">
                <input type="checkbox" name="closed" value="1"> <?php echo Yii::t('global','Close');?>
            </label>
        </div>
        <div class='hideInput'></div>
        <input type="hidden" name="type" id="type" value='it'>
        <input type="hidden" name="applicationId" id="applicationId">
        <input type="hidden" name="node" id="node">
        <div class='modal-footer borderTopNone'>
            <label class="checkbox-inline">
                <input type="checkbox" name="status" value="1"> <?php echo Yii::t('withdrawal', 'Mark this step as completed'); ?>
            </label>
            <button type="submit" class="btn btn-primary ml24 submitSend"><?php echo Yii::t('global','Submit');?></button>
            <button type="button" class="btn btn-primary J_ajax_submit_btn ml24"><?php echo Yii::t('global','Submit');?></button>
            
        </div>
    </form>
</div>

<script>
    lib()
    function lib(){
        let forms=childDetails.forms.find(item2 =>item2.key === 'it')
        for(var key in forms.owner_info){
            if (key !== '2' && key !== '5') {
            $('.lib_class_handle').append('<div class="flex align-items mr16"><img src='+forms.owner_info[key].photoUrl+' alt="" class="img28"/><div class="font14 color3 ml8">'+forms.owner_info[key].name+'</div></div>')
            }
        }
        $('#type').val(forms.key)
        if(forms.data.closed && forms.data.closed=='1'){
            $('input[type="checkbox"][name="closed"][value='+forms.data.closed+']').prop('checked', true);
        }
        $('.submitSend').show()
        $('.J_ajax_submit_btn').hide()
    }
    function check(_this){
        let balanceId=$('input[name="closed"]:checked').val()
        $('#J_fail_info').remove();
        var button = $(_this).find('button[type="submit"]');
        var flag = true, msg=[];
        if(!balanceId){
            $('.hideInput').html('<input type="hidden" checked name="closed" value="0">')
        }else{
            $('.hideInput').html('')
        }
        if(flag){
            $('.submitSend').hide()
            $('.J_ajax_submit_btn').show()
            $('.formStu .J_ajax_submit_btn').click();
            return false;
        }
        else{
            $( '<span id="J_fail_info" class="text-warning"><i class="glyphicon glyphicon-remove text-warning"></i> ' + msg.join('、') + '！</span>' ).appendTo(button.parent()).fadeIn( 'fast' );
            return false;
        }
    }
</script>
