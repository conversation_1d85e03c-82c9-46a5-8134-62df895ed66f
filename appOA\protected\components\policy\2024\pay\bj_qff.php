<?php
if(!defined("IVY_POLICY"))
throw new CException('DO NOT USE THIS FILE DIRECTLY');

return array( 'policy'=>array(

    //是否老生老办法
    ENABLE_CONSTANT_TUITION => true,

    //年保育费
    //ANNUAL_CHILDCARE_AMOUNT => 8000,

    //是否开放课外活动账单
    ENABLE_AFTERSCHOOLS => false,

    //图书馆工本费
    FEETYPE_LIBCARD => 10,

    //学期四个时间点；取前后两端
    TIME_POINTS   => array(202409,202401,202502,202506),

    //年付开通哪些缴费类型
//    PAYGROUP_ANNUAL => array(
//        ITEMS => array(
//            FEETYPE_TUITION,
//            FEETYPE_LUNCH,
//            FEETYPE_SCHOOLBUS,
//        ),
//        TUITION_CALC_BASE => BY_MONTH,
//    ),

   //学期付开通哪些缴费类型
   PAYGROUP_SEMESTER => array(
       ITEMS => array(
           FEETYPE_TUITION,
           FEETYPE_LUNCH,
           FEETYPE_SCHOOLBUS,
       ),
       TUITION_CALC_BASE => BY_MONTH,
   ),

   //月份开通哪些缴费类型
    PAYGROUP_MONTH => array(
        ITEMS => array(
            FEETYPE_TUITION,
            FEETYPE_LUNCH,
            FEETYPE_SCHOOLBUS,
        ),
        TUITION_CALC_BASE => BY_MONTH,
    ),

    //计算基准：BY_MONTH, 或者 BY_SETTING, 前者表示所有的学费都基于月费用计算，后者表示所有的学费都基于学期或学年
    TUITION_CALCULATION_BASE => array(

        BY_MONTH => array(

            //月收费金额
            AMOUNT => array(
                FULL5D => 19000, // 新生
                FULL5D1 => 15000, // 新生
                HALF5D => 10500, // 新生
                HALF5D1 => 8379, // 新生
                FULL5D6 => 10000,
                FULL5D7 => 13000,

                 FULL5D4 => 16994.4,
                 FULL5D2 => 14490,
                 FULL5D3 => 16100,
                 FULL5D5 => 12800,
                 HALF5D1 => 8820,
            ),

            //收费月份，及其权重
            MONTH_FACTOR => array(
                202409=>1,
                202410=>1,
                202411=>1,
                202412=>1,
                202501=>1,
                202502=>1,
                202503=>1,
                202504=>1,
                202505=>1,
                202506=>1
            ),

            //折扣
            GLOBAL_DISCOUNT => array(
                // DISCOUNT_ANNUAL => '0.95:10:2024-12-01:round',
                // DISCOUNT_SEMESTER1 => '0.97:10:2025-02-01:round',
                // DISCOUNT_SEMESTER2 => '0.97:10:2025-05-31:round'
            )

        ),
        BY_SEMESTER1 => array(
            ENDDATE => array(
                LUNCHA => 1735574400,
                LUNCHB => 1735574400,
                41825 => 1735574400,
                41826 => 1735574400,
                41827 => 1735574400,
                41828 => 1735574400,
                41829 => 1735574400,
                41830 => 1735574400,
                41831 => 1735574400,
                41832 => 1735574400,
                41833 => 1735574400,
                41834 => 1735574400,
                41835 => 1735574400,
                41836 => 1735574400,
                41837 => 1735574400,
                41838 => 1735574400,
                41839 => 1735574400,
                41840 => 1735574400,
                41841 => 1735574400,
                41842 => 1735574400,
                41843 => 1735574400,              
            )
			// ENDDATE => 1738252800 //2025-1-31
		),
    ),

    //每餐费用
	LUNCH_PERDAY => array(
	    LUNCHA => 882,
	    LUNCHB => 0,
	),
    // LUNCH_PERDAY => 819,
    // 判断是否使用 月付餐费
    LUNCH_PERMONTH => 1,

    //账单到期日和账单开始日之差
    DUEDAYS_OFFSET => 1,

));