if (typeof(GV) == 'undefined') {
    var GV = {
        'themeManagerBaseUrl': '/themes',
        'themeBaseUrl': '',
        'lang': "zh_cn"
    };
}
head.tmpl = function (str, data) {
    var fn = !/\W/.test(str) ? cache[str] = cache[str] || tmpl(str) :
        new Function("obj", "var p=[],print=function(){p.push.apply(p,arguments);};" +
        "with(obj){p.push('" +
        str.replace(/[\r\t\n]/g, " ").split("<%").join("\t").replace(/((^|%>)[^\t]*)'/g, "$1\r").replace(/\t=(.*?)%>/g, "',$1,'").split("\t").join("');").split("%>").join("p.push('").split("\r").join("\\'") + "');}return p.join('');");
    return data ? fn(data) : fn;
};
head.Util = {};
(function () {
    // if ($.browser.msie) {
    //     //ie 都不缓存
    //     $.ajaxSetup({
    //         cache: false
    //     });
    // }

    //不支持placeholder浏览器下对placeholder进行处理
    if (document.createElement('input').placeholder !== '') {
        $('[placeholder]').focus(function () {
            var input = $(this);
            if (input.val() == input.attr('placeholder')) {
                input.val('');
                input.removeClass('placeholder');
            }
        }).blur(function () {
            var input = $(this);
            if (input.val() == '' || input.val() == input.attr('placeholder')) {
                input.addClass('placeholder');
                input.val(input.attr('placeholder'));
            }
        }).blur().parents('form').submit(function () {
            $(this).find('[placeholder]').each(function () {
                var input = $(this);
                if (input.val() == input.attr('placeholder')) {
                    input.val('');
                }
            });
        });
    }

    head.Util.aDialog = function () {
        var wrap = arguments[0] ? arguments[0] : $(document);
        //所有加了dialog类名的a链接，自动弹出它的href
        if (wrap.find('a.J_dialog').length) {
            head.load(GV.themeManagerBaseUrl + '/base/js/dialog/dialog.js', function () {
                wrap.find('.J_dialog').unbind("click");
                wrap.find('.J_dialog').on('click', function (e) {
                    e.preventDefault();
                    var _this = $(this);
                    head.dialog.open($(this).prop('href'), {
                        onClose: function () {
                            _this.focus();//关闭时让触发弹窗的元素获取焦点
                        },
                        title: _this.prop('title'),
                        resize: $(this).attr('non-resize') ? false : true
                    });
                }).attr('role', 'button');
            });
        }
    };

    head.Util.ajaxForm = function () {
        var wrap = arguments[0] ? arguments[0] : $(document);
        //所有的ajax form提交,由于大多业务逻辑都是一样的，故统一处理
        var ajaxForm_list = wrap.find('form.J_ajaxForm');
        if (ajaxForm_list.length) {
            head.load(GV.themeManagerBaseUrl + '/base/js/dialog/dialog.js', GV.themeManagerBaseUrl + '/base/js/util_libs/ajaxForm.js', function () {

                // if ($.browser.msie) {
                //     //ie8及以下，表单中只有一个可见的input:text时，会整个页面会跳转提交
                //     ajaxForm_list.on('submit', function (e) {
                //         //表单中只有一个可见的input:text时，enter提交无效
                //         e.preventDefault();
                //     });
                // }

                wrap.find('button.J_ajax_submit_btn').on('click', function (e) {
                    e.preventDefault();
                    /*var btn = $(this).find('button.J_ajax_submit_btn'),
                     form = $(this);*/
                    var btn = $(this),
                        form = btn.parents('form.J_ajaxForm');
                    if (btn.data('category')) {
                        $('#buttonCategory').val(btn.data('category'));
                    }
                    //批量操作 判断选项
                    if (btn.data('subcheck')) {
                        btn.parent().find('span').remove();
                        if (form.find('input.J_check:checked').length) {
                            var msg = btn.data('msg');
                            if (msg) {
                                head.dialog({
                                    type: 'confirm',
                                    isMask: false,
                                    message: btn.data('msg'),
                                    follow: btn,
                                    onOk: function () {
                                        btn.data('subcheck', false);
                                        btn.click();
                                    }
                                });
                            } else {
                                btn.data('subcheck', false);
                                btn.click();
                            }

                        } else {
                            $('<span class="tips_error">请至少选择一项</span>').appendTo(btn.parent()).fadeIn('fast');
                        }
                        return false;
                    }

                    //ie处理placeholder提交问题
                    // if ($.browser.msie) {
                    //     form.find('[placeholder]').each(function () {
                    //         var input = $(this);
                    //         if (input.val() == input.attr('placeholder')) {
                    //             input.val('');
                    //         }
                    //     });
                    // }

                    form.ajaxSubmit({
                        url: btn.data('action') ? btn.data('action') : form.attr('action'),			//按钮上是否自定义提交地址(多按钮情况)
                        dataType: 'json',
                        beforeSubmit: function (arr, $form, options) {
                            var text = btn.text();

                            //按钮文案、状态修改
                            btn.text(text + '...').prop('disabled', true);
                        },
                        success: function (data, statusText, xhr, $form) {
                            var text = btn.text();

                            //按钮文案、状态修改
                            btn.text(text.replace('...', '')).parent().find('i,span').remove();

                            form.find('div.form-group[model-attribute]').removeClass('has-error');

                            if (data.state === 'success') {
                                $('<span id="J_success_info" class="text-success"><i class="glyphicon glyphicon-ok text-success"></i> ' + data.message + '</span>').appendTo(btn.parent()).fadeIn('slow').delay(1000).fadeOut(function () {

                                    if (data.refresh === true) {
                                        if (data.referer) {
                                            //返回带跳转地址
                                            if (window.parent.head.dialog) {
                                                //iframe弹出页
                                                window.parent.location.href = data.referer;
                                            } else {
                                                window.location.href = data.referer;
                                            }
                                        } else {
                                            if (window.parent.head.dialog) {
                                                reloadPage(window.parent);
                                            } else {
                                                reloadPage(window);
                                            }
                                        }
                                    }
                                    else {
                                        btn.removeProp('disabled');
                                        window.parent.head.dialog.closeAll();
                                    }
                                });
                            } else if (data.state === 'fail') {
                                $('<span id="J_fail_info" class="text-warning"><i class="glyphicon glyphicon-remove text-warning"></i> ' + data.message + '</span>').appendTo(btn.parent()).fadeIn('fast');
                                btn.removeProp('disabled');
                                if (data.error) {
                                    $.each(data.error, function (attr) {
                                        form.find('div.form-group[model-attribute|="' + attr + '"]').addClass('has-error');
                                    });
                                }
                            }
                            if (typeof(data.callback) !== 'undefined') {
                                eval(data.callback + '(data.data)');
                            }
                        }
                    });
                });

            });
        }
    };

    head.Util.closeDialog = function () {
        var wrap = arguments[0] ? arguments[0] : $(document);
        //dialog弹窗内的关闭方法
        wrap.find('#J_dialog_close').on('click', function (e) {
            e.preventDefault();
            if (window.parent.head.dialog) {
                window.parent.head.dialog.closeAll();
            }
        });
    };

    head.Util.ajaxDel = function () {
        var wrap = arguments[0] ? arguments[0] : $(document);
        //所有的删除操作
        if (wrap.find('a.J_ajax_del').length) {
            head.load(GV.themeManagerBaseUrl + '/base/js/dialog/dialog.js', function () {

                wrap.find('.J_ajax_del').on('click', function (e) {
                    e.preventDefault();
                    var $this = $(this), href = $this.prop('href'), msg = $this.data('msg');
                    var okText = $this.data('oktext'), cancelText = $this.data('canceltext');
                    var params = {
                        okText: okText ? okText : '确定',
                        cancelText: cancelText ? cancelText : '取消',
                        message: msg ? msg : '确定要删除吗？',
                        type: 'confirm',
                        isMask: false,
                        zIndex: 1999,
                        follow: $(this),//跟随触发事件的元素显示
                        onOk: function () {
                            var urlArr = href.split('?');
                            $.post(urlArr[0], urlArr[1], function (data) {
                                if (data.state === 'success') {
                                    resultTip({
                                        msg: data.message, callback: function () {
                                            if (data.refresh === true) {
                                                if (data.referer) {
                                                    location.href = data.referer;
                                                } else {
                                                    reloadPage(window);
                                                }
                                            }
                                        }
                                    });
                                } else if (data.state === 'fail') {
                                    head.dialog.alert(data.message);
                                }
                                if (typeof(data.callback) !== 'undefined') {
                                    eval(data.callback + '(data.data)');
                                }
                            }, 'json');
                        }
                    };
                    head.dialog(params);
                });
            });
        }
    };

    head.Util.ajax = function () {
        var wrap = arguments[0] ? arguments[0] : $(document);
        //所有的删除操作
        if (wrap.find('a.J_ajax').length) {
            wrap.find('.J_ajax').on('click', function (e) {
                e.preventDefault();
                var $this = $(this), href = $this.prop('href');
                var urlArr = href.split('?');
                var type = ($this.data('method')) ? $this.data('method') : 'get';
                $.ajax({
                    url: urlArr[0],
                    data: urlArr[1],
                    type: type,
                    dataType: 'json',
                    success: function (data) {
                        if (data.state === 'fail') {
                            head.dialog.alert(data.message);
                            return false;
                        }
                        if (typeof(data.callback) !== 'undefined') {
                            eval(data.callback + '(data.data)');
                        }
                    },
                    error: function () {
                        alert("访问异常！");
                    }
                });
            });
        }
    };

    head.Util.checkAll = function () {
        var wrap = arguments[0] ? arguments[0] : $(document);
        /*复选框全选(支持多个，纵横双控全选)。
         *实例：版块编辑-权限相关（双控），验证机制-验证策略（单控）
         *说明：
         *	"J_check"的"data-xid"对应其左侧"J_check_all"的"data-checklist"；
         *	"J_check"的"data-yid"对应其上方"J_check_all"的"data-checklist"；
         *	全选框的"data-direction"代表其控制的全选方向(x或y)；
         *	"J_check_wrap"同一块全选操作区域的父标签class，多个调用考虑
         */
        if (wrap.find('.J_check_wrap').length) {
            var total_check_all = wrap.find('input.J_check_all');
                    //遍历所有全选框
                    $.each(total_check_all, function () {
                        var check_all = $(this), check_items;

                        //分组各纵横项
                        var check_all_direction = check_all.data('direction');
                        check_items = $('input.J_check[data-' + check_all_direction + 'id="' + check_all.data('checklist') + '"]');

                        //点击全选框
                        check_all.change(function (e) {
                            var check_wrap = check_all.parents('.J_check_wrap'); //当前操作区域所有复选框的父标签（重用考虑）

                            if ($(this).attr('checked')) {
                                //全选状态
                                check_items.attr('checked', true);

                                //所有项都被选中
                                if (check_wrap.find('input.J_check').length === check_wrap.find('input.J_check:checked').length) {
                                    check_wrap.find(total_check_all).attr('checked', true);
                                }
                            } else {
                                //非全选状态
                                check_items.removeAttr('checked');
                                //另一方向的全选框取消全选状态
                                var direction_invert = check_all_direction === 'x' ? 'y' : 'x';
                                check_wrap.find($('input.J_check_all[data-direction="' + direction_invert + '"]')).removeAttr('checked');
                            }
                });
                //点击非全选时判断是否全部勾选
                check_items.change(function () {

                    if ($(this).attr('checked')) {

                        if (check_items.filter(':checked').length === check_items.length) {
                            //已选择和未选择的复选框数相等
                            check_all.attr('checked', true);
                        }

                    } else {
                        check_all.removeAttr('checked');
                    }
                });
            });
        }
    };

    head.Util.tipsCommon = function() {
        var wrap = arguments[0] ? $('#' + arguments[0]) : $(document);
        if (wrap.find('.tipRelative').length) {
            var tipRelative = wrap.find('.tipRelative');
            $.each(tipRelative, function () {
               let dataId=$(this).attr('data-id')
               var tipsShown = getTipsCookie('tipsShown') || {};
               if (!tipsShown[dataId]) {
                let position=$(this).attr('data-position')
                let title=$(this).attr('data-title')
                let desc=$(this).attr('data-desc')
                let btnText=$(this).attr('data-btnText')
                var html = '';
                var height=$(this).outerHeight(true)
                var width=$(this).outerWidth()
                if (position == 'top') {
                    let left=((width)/2)-12
                    let top=height+10
                    html += '<span class="popoverCom popoverTop" id='+dataId+' style="left:'+(left)+'px;top:'+top+'px">';
                } else if (position == 'left') {
                    let left=width+10
                    let top=(height/2)-12
                    html += '<span class="popoverCom popoverLeft"  id='+dataId+' style="left:'+left+'px;top:'+top+'px">';
                } else if (position == 'right') {
                    let left=width+10
                    let top=(height/2)-12
                    html += '<span class="popoverCom popoverRight" id='+dataId+' style="right:'+left+'px;top:'+top+'px">';
                } else if (position == 'bottom') {
                    let left=((width)/2)-12
                    let top=height+10
                    html += '<span class="popoverCom popoverBottom"  id='+dataId+' style="left:'+left+'px;bottom:'+top+'px">'; 
                }
                html+='<div class="popoverTitle"><span class="flex1">' + title + '</span><span class="glyphicon glyphicon-remove" data-id='+dataId+' onclick="closePopover(this)"></span></div><div class="popoverDesc">' + desc + '</div><div><span class="popoverBtn" data-id='+dataId+' onclick="closePopover(this)">'+btnText+'</span></div></span>'
                $(this).append(html);
                }
            })
        }
    };
    head.Util.aDialog();
    head.Util.ajaxDel();
    head.Util.ajaxForm();
    head.Util.checkAll();
    head.Util.closeDialog();
    head.Util.ajax();
    head.Util.tipsCommon();
})();
function setTipsCookie(name, value) {
    var expires = new Date("2099-12-31").toUTCString();
    value = JSON.stringify(value);
    document.cookie = name + "=" + encodeURIComponent(value) + "; expires=" + expires + ";path=/;";
}

function getTipsCookie(name) {
    var nameEQ = name + "=";
    var ca = document.cookie.split(';');
    for (var i = 0; i < ca.length; i++) {
        var c = ca[i].trim();
        if (c.indexOf(nameEQ) === 0) {
            return JSON.parse(decodeURIComponent(c.substring(nameEQ.length, c.length)));
        }
    }
    return null;
}
function closePopover(popupId) {
    event.stopPropagation();
    let dataId=$(popupId).attr('data-id')
    $('#'+dataId).hide();
    var tipsShown = getTipsCookie('tipsShown') || {};
    tipsShown[dataId] = true;
    setTipsCookie('tipsShown', tipsShown);
}

//重新刷新页面，使用location.reload()有可能导致重新提交
function reloadPage(win) {
    var location = win.location;
    location.href = location.pathname + location.search;
}

//浮出提示_居中
function resultTip(options) {

    var cls = (options.error ? 'warning' : 'success');
    var delay = typeof options.delay == 'undefined' ? 1500 : options.delay;
    var pop = $('<div style="left:50%;top:30%;z-index: 99999;" class="pop_showmsg_wrap"><span class="pop_showmsg"><span class="tip-' + cls + '">' + options.msg + '</span></span></div>');

    pop.appendTo($('body')).fadeIn(function () {
        pop.css({
            marginLeft: -pop.innerWidth() / 2
        }); //水平居中
    }).delay(delay).fadeOut(function () {
        pop.remove();

        //回调
        if (options.callback) {
            options.callback();
        }
    });

}

//弹窗居中定位 非ie6 fixed定位
function popPos(wrap) {
    var ie6 = false,
        pos = 'fixed',
        top,
        win_height = $(window).height(),
        wrap_height = wrap.outerHeight();

    // if ($.browser.msie && $.browser.version < 7) {
    //     ie6 = true;
    //     pos = 'absolute';
    // }

    if (win_height < wrap_height) {
        top = 0;
    } else {
        top = ($(window).height() - wrap.outerHeight()) / 2;
    }

    wrap.css({
        position: pos,
        top: top + (ie6 ? $(document).scrollTop() : 0),
        left: ($(window).width() - wrap.innerWidth()) / 2
    }).show();
}

function reloadCommon() {
    alert('reloadCommon() 此函数已作废');
    var jsUrl = GV.themeManagerBaseUrl + '/base/js/common.js';
    $('script[src^="' + jsUrl + '"]').remove();
    head.load(jsUrl + '?v' + new Date().getTime());
}

//弹窗定位
head.Util.popPos = function (wrap) {
    var ie6 = false,
        top,
        win_height = $(window).height(),
        wrap_height = wrap.outerHeight();

    // if ($.browser.msie && $.browser.version < 7) {
    //     ie6 = true;
    // }

    if (win_height < wrap_height) {
        top = 0;
    } else {
        top = ($(window).height() - wrap.outerHeight()) / 2;
    }

    wrap.css({
        top: top + (ie6 ? $(document).scrollTop() : 0),
        left: ($(window).width() - wrap.innerWidth()) / 2,
        position: (ie6 ? 'absolute' : 'fixed')
    }).show();
};

// 模态框

head.Util.modal = function () {
    var wrap = arguments[0] ? $('#' + arguments[0]) : $(document);
    wrap.find('.J_modal').on('click', function (e) {
        e.preventDefault();
        var _isStatic = 'static';
        $(this).hasClass('drop') ? _isStatic = true : '';
        $('#modal .modal-content').load($(this).attr('href'), function () {
            $('#modal').modal({
                show: true,
                backdrop: _isStatic,
                keyboard:false
            });
            head.Util.ajaxForm($('#modal'));
        });
    });
};
head.Util.modal();


