{"version": 3, "file": "backbone-min.js", "sources": ["backbone.js"], "names": ["root", "this", "previousBackbone", "Backbone", "array", "push", "slice", "splice", "exports", "VERSION", "_", "require", "$", "j<PERSON><PERSON><PERSON>", "Zepto", "ender", "noConflict", "emulateHTTP", "emulateJSON", "Events", "on", "name", "callback", "context", "eventsApi", "_events", "events", "ctx", "once", "self", "off", "apply", "arguments", "_callback", "retain", "ev", "names", "i", "l", "j", "k", "keys", "length", "trigger", "args", "call", "allEvents", "all", "triggerEvents", "stopListening", "obj", "listeningTo", "_listeningTo", "remove", "_listenId", "id", "isEmpty", "event<PERSON><PERSON>litter", "action", "rest", "key", "concat", "test", "split", "a1", "a2", "a3", "listenMethods", "listenTo", "listenToOnce", "each", "implementation", "method", "uniqueId", "bind", "unbind", "extend", "Model", "attributes", "options", "attrs", "cid", "collection", "parse", "defaults", "result", "set", "changed", "initialize", "prototype", "validationError", "idAttribute", "toJSON", "clone", "sync", "get", "attr", "escape", "has", "val", "unset", "changes", "silent", "changing", "prev", "current", "_validate", "_changing", "_previousAttributes", "isEqual", "_pending", "clear", "has<PERSON><PERSON>ed", "changedAttributes", "diff", "old", "previous", "previousAttributes", "fetch", "model", "success", "resp", "wrapError", "save", "xhr", "validate", "wait", "serverAttrs", "isObject", "isNew", "patch", "destroy", "url", "base", "url<PERSON><PERSON>r", "char<PERSON>t", "encodeURIComponent", "constructor", "<PERSON><PERSON><PERSON><PERSON>", "error", "modelMethods", "unshift", "Collection", "models", "comparator", "_reset", "reset", "setOptions", "add", "merge", "addOptions", "map", "singular", "isArray", "index", "_byId", "indexOf", "_removeReference", "existing", "sort", "at", "targetModel", "sortable", "sortAttr", "isString", "toAdd", "toRemove", "modelMap", "order", "_prepareModel", "_onModelEvent", "orderedModels", "previousModels", "pop", "shift", "where", "first", "findWhere", "Error", "sortBy", "pluck", "invoke", "create", "event", "methods", "attributeMethods", "value", "iterator", "isFunction", "View", "pick", "viewOptions", "_ensureElement", "delegateEvents", "delegateEventSplitter", "tagName", "selector", "$el", "find", "render", "setElement", "element", "delegate", "undelegateEvents", "el", "match", "eventName", "className", "type", "methodMap", "params", "dataType", "data", "contentType", "JSON", "stringify", "_method", "beforeSend", "setRequestHeader", "processData", "noXhrPatch", "ActiveXObject", "ajax", "window", "XMLHttpRequest", "dispatchEvent", "update", "delete", "read", "Router", "routes", "_bindRoutes", "optionalParam", "namedP<PERSON><PERSON>", "splatParam", "escapeRegExp", "route", "isRegExp", "_routeToRegExp", "router", "history", "fragment", "_extractParameters", "navigate", "replace", "optional", "RegExp", "exec", "param", "decodeURIComponent", "History", "handlers", "bindAll", "location", "routeStripper", "rootStripper", "isExplorer", "trailingSlash", "pathStripper", "started", "interval", "getHash", "href", "getFragment", "forcePushState", "_hasPushState", "_wantsHashChange", "pathname", "start", "hashChange", "_wantsPushState", "pushState", "docMode", "document", "documentMode", "oldIE", "navigator", "userAgent", "toLowerCase", "iframe", "hide", "appendTo", "contentWindow", "checkUrl", "_checkUrlInterval", "setInterval", "loc", "atRoot", "search", "hash", "replaceState", "title", "loadUrl", "stop", "clearInterval", "e", "any", "handler", "_updateHash", "open", "close", "assign", "protoProps", "staticProps", "parent", "child", "Surrogate", "__super__"], "mappings": "CAQA,WAOE,GAAIA,GAAOC,IAIX,IAAIC,GAAmBF,EAAKG,QAG5B,IAAIC,KACJ,IAAIC,GAAOD,EAAMC,IACjB,IAAIC,GAAQF,EAAME,KAClB,IAAIC,GAASH,EAAMG,MAInB,IAAIJ,EACJ,UAAWK,WAAY,YAAa,CAClCL,EAAWK,YACN,CACLL,EAAWH,EAAKG,YAIlBA,EAASM,QAAU,OAGnB,IAAIC,GAAIV,EAAKU,CACb,KAAKA,SAAaC,WAAY,YAAcD,EAAIC,QAAQ,aAIxDR,GAASS,EAAIZ,EAAKa,QAAUb,EAAKc,OAASd,EAAKe,OAASf,EAAKY,CAI7DT,GAASa,WAAa,WACpBhB,EAAKG,SAAWD,CAChB,OAAOD,MAMTE,GAASc,YAAc,KAMvBd,GAASe,YAAc,KAevB,IAAIC,GAAShB,EAASgB,QAIpBC,GAAI,SAASC,EAAMC,EAAUC,GAC3B,IAAKC,EAAUvB,KAAM,KAAMoB,GAAOC,EAAUC,MAAcD,EAAU,MAAOrB,KAC3EA,MAAKwB,UAAYxB,KAAKwB,WACtB,IAAIC,GAASzB,KAAKwB,QAAQJ,KAAUpB,KAAKwB,QAAQJ,MACjDK,GAAOrB,MAAMiB,SAAUA,EAAUC,QAASA,EAASI,IAAKJ,GAAWtB,MACnE,OAAOA,OAKT2B,KAAM,SAASP,EAAMC,EAAUC,GAC7B,IAAKC,EAAUvB,KAAM,OAAQoB,GAAOC,EAAUC,MAAcD,EAAU,MAAOrB,KAC7E,IAAI4B,GAAO5B,IACX,IAAI2B,GAAOlB,EAAEkB,KAAK,WAChBC,EAAKC,IAAIT,EAAMO,EACfN,GAASS,MAAM9B,KAAM+B,YAEvBJ,GAAKK,UAAYX,CACjB,OAAOrB,MAAKmB,GAAGC,EAAMO,EAAML,IAO7BO,IAAK,SAAST,EAAMC,EAAUC,GAC5B,GAAIW,GAAQC,EAAIT,EAAQU,EAAOC,EAAGC,EAAGC,EAAGC,CACxC,KAAKvC,KAAKwB,UAAYD,EAAUvB,KAAM,MAAOoB,GAAOC,EAAUC,IAAW,MAAOtB,KAChF,KAAKoB,IAASC,IAAaC,EAAS,CAClCtB,KAAKwB,UACL,OAAOxB,MAETmC,EAAQf,GAAQA,GAAQX,EAAE+B,KAAKxC,KAAKwB,QACpC,KAAKY,EAAI,EAAGC,EAAIF,EAAMM,OAAQL,EAAIC,EAAGD,IAAK,CACxChB,EAAOe,EAAMC,EACb,IAAIX,EAASzB,KAAKwB,QAAQJ,GAAO,CAC/BpB,KAAKwB,QAAQJ,GAAQa,IACrB,IAAIZ,GAAYC,EAAS,CACvB,IAAKgB,EAAI,EAAGC,EAAId,EAAOgB,OAAQH,EAAIC,EAAGD,IAAK,CACzCJ,EAAKT,EAAOa,EACZ,IAAKjB,GAAYA,IAAaa,EAAGb,UAAYA,IAAaa,EAAGb,SAASW,WACjEV,GAAWA,IAAYY,EAAGZ,QAAU,CACvCW,EAAO7B,KAAK8B,KAIlB,IAAKD,EAAOQ,aAAezC,MAAKwB,QAAQJ,IAI5C,MAAOpB,OAOT0C,QAAS,SAAStB,GAChB,IAAKpB,KAAKwB,QAAS,MAAOxB,KAC1B,IAAI2C,GAAOtC,EAAMuC,KAAKb,UAAW,EACjC,KAAKR,EAAUvB,KAAM,UAAWoB,EAAMuB,GAAO,MAAO3C,KACpD,IAAIyB,GAASzB,KAAKwB,QAAQJ,EAC1B,IAAIyB,GAAY7C,KAAKwB,QAAQsB,GAC7B,IAAIrB,EAAQsB,EAActB,EAAQkB,EAClC,IAAIE,EAAWE,EAAcF,EAAWd,UACxC,OAAO/B,OAKTgD,cAAe,SAASC,EAAK7B,EAAMC,GACjC,GAAI6B,GAAclD,KAAKmD,YACvB,KAAKD,EAAa,MAAOlD,KACzB,IAAIoD,IAAUhC,IAASC,CACvB,KAAKA,SAAmBD,KAAS,SAAUC,EAAWrB,IACtD,IAAIiD,GAAMC,MAAkBD,EAAII,WAAaJ,CAC7C,KAAK,GAAIK,KAAMJ,GAAa,CAC1BD,EAAMC,EAAYI,EAClBL,GAAIpB,IAAIT,EAAMC,EAAUrB,KACxB,IAAIoD,GAAU3C,EAAE8C,QAAQN,EAAIzB,eAAiBxB,MAAKmD,aAAaG,GAEjE,MAAOtD,OAMX,IAAIwD,GAAgB,KAKpB,IAAIjC,GAAY,SAAS0B,EAAKQ,EAAQrC,EAAMsC,GAC1C,IAAKtC,EAAM,MAAO,KAGlB,UAAWA,KAAS,SAAU,CAC5B,IAAK,GAAIuC,KAAOvC,GAAM,CACpB6B,EAAIQ,GAAQ3B,MAAMmB,GAAMU,EAAKvC,EAAKuC,IAAMC,OAAOF,IAEjD,MAAO,OAIT,GAAIF,EAAcK,KAAKzC,GAAO,CAC5B,GAAIe,GAAQf,EAAK0C,MAAMN,EACvB,KAAK,GAAIpB,GAAI,EAAGC,EAAIF,EAAMM,OAAQL,EAAIC,EAAGD,IAAK,CAC5Ca,EAAIQ,GAAQ3B,MAAMmB,GAAMd,EAAMC,IAAIwB,OAAOF,IAE3C,MAAO,OAGT,MAAO,MAMT,IAAIX,GAAgB,SAAStB,EAAQkB,GACnC,GAAIT,GAAIE,GAAK,EAAGC,EAAIZ,EAAOgB,OAAQsB,EAAKpB,EAAK,GAAIqB,EAAKrB,EAAK,GAAIsB,EAAKtB,EAAK,EACzE,QAAQA,EAAKF,QACX,IAAK,GAAG,QAASL,EAAIC,GAAIH,EAAKT,EAAOW,IAAIf,SAASuB,KAAKV,EAAGR,IAAM,OAChE,KAAK,GAAG,QAASU,EAAIC,GAAIH,EAAKT,EAAOW,IAAIf,SAASuB,KAAKV,EAAGR,IAAKqC,EAAK,OACpE,KAAK,GAAG,QAAS3B,EAAIC,GAAIH,EAAKT,EAAOW,IAAIf,SAASuB,KAAKV,EAAGR,IAAKqC,EAAIC,EAAK,OACxE,KAAK,GAAG,QAAS5B,EAAIC,GAAIH,EAAKT,EAAOW,IAAIf,SAASuB,KAAKV,EAAGR,IAAKqC,EAAIC,EAAIC,EAAK,OAC5E,SAAS,QAAS7B,EAAIC,GAAIH,EAAKT,EAAOW,IAAIf,SAASS,MAAMI,EAAGR,IAAKiB,IAIrE,IAAIuB,IAAiBC,SAAU,KAAMC,aAAc,OAKnD3D,GAAE4D,KAAKH,EAAe,SAASI,EAAgBC,GAC7CrD,EAAOqD,GAAU,SAAStB,EAAK7B,EAAMC,GACnC,GAAI6B,GAAclD,KAAKmD,eAAiBnD,KAAKmD,gBAC7C,IAAIG,GAAKL,EAAII,YAAcJ,EAAII,UAAY5C,EAAE+D,SAAS,KACtDtB,GAAYI,GAAML,CAClB,KAAK5B,SAAmBD,KAAS,SAAUC,EAAWrB,IACtDiD,GAAIqB,GAAgBlD,EAAMC,EAAUrB,KACpC,OAAOA,QAKXkB,GAAOuD,KAASvD,EAAOC,EACvBD,GAAOwD,OAASxD,EAAOW,GAIvBpB,GAAEkE,OAAOzE,EAAUgB,EAYnB,IAAI0D,GAAQ1E,EAAS0E,MAAQ,SAASC,EAAYC,GAChD,GAAIC,GAAQF,KACZC,KAAYA,KACZ9E,MAAKgF,IAAMvE,EAAE+D,SAAS,IACtBxE,MAAK6E,aACL,IAAIC,EAAQG,WAAYjF,KAAKiF,WAAaH,EAAQG,UAClD,IAAIH,EAAQI,MAAOH,EAAQ/E,KAAKkF,MAAMH,EAAOD,MAC7CC,GAAQtE,EAAE0E,YAAaJ,EAAOtE,EAAE2E,OAAOpF,KAAM,YAC7CA,MAAKqF,IAAIN,EAAOD,EAChB9E,MAAKsF,UACLtF,MAAKuF,WAAWzD,MAAM9B,KAAM+B,WAI9BtB,GAAEkE,OAAOC,EAAMY,UAAWtE,GAGxBoE,QAAS,KAGTG,gBAAiB,KAIjBC,YAAa,KAIbH,WAAY,aAGZI,OAAQ,SAASb,GACf,MAAOrE,GAAEmF,MAAM5F,KAAK6E,aAKtBgB,KAAM,WACJ,MAAO3F,GAAS2F,KAAK/D,MAAM9B,KAAM+B,YAInC+D,IAAK,SAASC,GACZ,MAAO/F,MAAK6E,WAAWkB,IAIzBC,OAAQ,SAASD,GACf,MAAOtF,GAAEuF,OAAOhG,KAAK8F,IAAIC,KAK3BE,IAAK,SAASF,GACZ,MAAO/F,MAAK8F,IAAIC,IAAS,MAM3BV,IAAK,SAAS1B,EAAKuC,EAAKpB,GACtB,GAAIiB,GAAMhB,EAAOoB,EAAOC,EAASC,EAAQC,EAAUC,EAAMC,CACzD,IAAI7C,GAAO,KAAM,MAAO3D,KAGxB,UAAW2D,KAAQ,SAAU,CAC3BoB,EAAQpB,CACRmB,GAAUoB,MACL,EACJnB,MAAYpB,GAAOuC,EAGtBpB,IAAYA,KAGZ,KAAK9E,KAAKyG,UAAU1B,EAAOD,GAAU,MAAO,MAG5CqB,GAAkBrB,EAAQqB,KAC1BE,GAAkBvB,EAAQuB,MAC1BD,KACAE,GAAkBtG,KAAK0G,SACvB1G,MAAK0G,UAAa,IAElB,KAAKJ,EAAU,CACbtG,KAAK2G,oBAAsBlG,EAAEmF,MAAM5F,KAAK6E,WACxC7E,MAAKsF,WAEPkB,EAAUxG,KAAK6E,WAAY0B,EAAOvG,KAAK2G,mBAGvC,IAAI3G,KAAK0F,cAAeX,GAAO/E,KAAKsD,GAAKyB,EAAM/E,KAAK0F,YAGpD,KAAKK,IAAQhB,GAAO,CAClBmB,EAAMnB,EAAMgB,EACZ,KAAKtF,EAAEmG,QAAQJ,EAAQT,GAAOG,GAAME,EAAQhG,KAAK2F,EACjD,KAAKtF,EAAEmG,QAAQL,EAAKR,GAAOG,GAAM,CAC/BlG,KAAKsF,QAAQS,GAAQG,MAChB,OACElG,MAAKsF,QAAQS,GAEtBI,QAAeK,GAAQT,GAAQS,EAAQT,GAAQG,EAIjD,IAAKG,EAAQ,CACX,GAAID,EAAQ3D,OAAQzC,KAAK6G,SAAW,IACpC,KAAK,GAAIzE,GAAI,EAAGC,EAAI+D,EAAQ3D,OAAQL,EAAIC,EAAGD,IAAK,CAC9CpC,KAAK0C,QAAQ,UAAY0D,EAAQhE,GAAIpC,KAAMwG,EAAQJ,EAAQhE,IAAK0C,IAMpE,GAAIwB,EAAU,MAAOtG,KACrB,KAAKqG,EAAQ,CACX,MAAOrG,KAAK6G,SAAU,CACpB7G,KAAK6G,SAAW,KAChB7G,MAAK0C,QAAQ,SAAU1C,KAAM8E,IAGjC9E,KAAK6G,SAAW,KAChB7G,MAAK0G,UAAY,KACjB,OAAO1G,OAKTmG,MAAO,SAASJ,EAAMjB,GACpB,MAAO9E,MAAKqF,IAAIU,MAAW,GAAGtF,EAAEkE,UAAWG,GAAUqB,MAAO,SAI9DW,MAAO,SAAShC,GACd,GAAIC,KACJ,KAAK,GAAIpB,KAAO3D,MAAK6E,WAAYE,EAAMpB,OAAY,EACnD,OAAO3D,MAAKqF,IAAIN,EAAOtE,EAAEkE,UAAWG,GAAUqB,MAAO,SAKvDY,WAAY,SAAShB,GACnB,GAAIA,GAAQ,KAAM,OAAQtF,EAAE8C,QAAQvD,KAAKsF,QACzC,OAAO7E,GAAEwF,IAAIjG,KAAKsF,QAASS,IAS7BiB,kBAAmB,SAASC,GAC1B,IAAKA,EAAM,MAAOjH,MAAK+G,aAAetG,EAAEmF,MAAM5F,KAAKsF,SAAW,KAC9D,IAAIY,GAAKZ,EAAU,KACnB,IAAI4B,GAAMlH,KAAK0G,UAAY1G,KAAK2G,oBAAsB3G,KAAK6E,UAC3D,KAAK,GAAIkB,KAAQkB,GAAM,CACrB,GAAIxG,EAAEmG,QAAQM,EAAInB,GAAQG,EAAMe,EAAKlB,IAAS,UAC7CT,IAAYA,OAAeS,GAAQG,EAEtC,MAAOZ,IAKT6B,SAAU,SAASpB,GACjB,GAAIA,GAAQ,OAAS/F,KAAK2G,oBAAqB,MAAO,KACtD,OAAO3G,MAAK2G,oBAAoBZ,IAKlCqB,mBAAoB,WAClB,MAAO3G,GAAEmF,MAAM5F,KAAK2G,sBAMtBU,MAAO,SAASvC,GACdA,EAAUA,EAAUrE,EAAEmF,MAAMd,KAC5B,IAAIA,EAAQI,YAAe,GAAGJ,EAAQI,MAAQ,IAC9C,IAAIoC,GAAQtH,IACZ,IAAIuH,GAAUzC,EAAQyC,OACtBzC,GAAQyC,QAAU,SAASC,GACzB,IAAKF,EAAMjC,IAAIiC,EAAMpC,MAAMsC,EAAM1C,GAAUA,GAAU,MAAO,MAC5D,IAAIyC,EAASA,EAAQD,EAAOE,EAAM1C,EAClCwC,GAAM5E,QAAQ,OAAQ4E,EAAOE,EAAM1C,GAErC2C,GAAUzH,KAAM8E,EAChB,OAAO9E,MAAK6F,KAAK,OAAQ7F,KAAM8E,IAMjC4C,KAAM,SAAS/D,EAAKuC,EAAKpB,GACvB,GAAIC,GAAOR,EAAQoD,EAAK9C,EAAa7E,KAAK6E,UAG1C,IAAIlB,GAAO,YAAeA,KAAQ,SAAU,CAC1CoB,EAAQpB,CACRmB,GAAUoB,MACL,EACJnB,MAAYpB,GAAOuC,EAGtBpB,EAAUrE,EAAEkE,QAAQiD,SAAU,MAAO9C,EAKrC,IAAIC,IAAUD,EAAQ+C,KAAM,CAC1B,IAAK7H,KAAKqF,IAAIN,EAAOD,GAAU,MAAO,WACjC,CACL,IAAK9E,KAAKyG,UAAU1B,EAAOD,GAAU,MAAO,OAI9C,GAAIC,GAASD,EAAQ+C,KAAM,CACzB7H,KAAK6E,WAAapE,EAAEkE,UAAWE,EAAYE,GAK7C,GAAID,EAAQI,YAAe,GAAGJ,EAAQI,MAAQ,IAC9C,IAAIoC,GAAQtH,IACZ,IAAIuH,GAAUzC,EAAQyC,OACtBzC,GAAQyC,QAAU,SAASC,GAEzBF,EAAMzC,WAAaA,CACnB,IAAIiD,GAAcR,EAAMpC,MAAMsC,EAAM1C,EACpC,IAAIA,EAAQ+C,KAAMC,EAAcrH,EAAEkE,OAAOI,MAAa+C,EACtD,IAAIrH,EAAEsH,SAASD,KAAiBR,EAAMjC,IAAIyC,EAAahD,GAAU,CAC/D,MAAO,OAET,GAAIyC,EAASA,EAAQD,EAAOE,EAAM1C,EAClCwC,GAAM5E,QAAQ,OAAQ4E,EAAOE,EAAM1C,GAErC2C,GAAUzH,KAAM8E,EAEhBP,GAASvE,KAAKgI,QAAU,SAAYlD,EAAQmD,MAAQ,QAAU,QAC9D,IAAI1D,IAAW,QAASO,EAAQC,MAAQA,CACxC4C,GAAM3H,KAAK6F,KAAKtB,EAAQvE,KAAM8E,EAG9B,IAAIC,GAASD,EAAQ+C,KAAM7H,KAAK6E,WAAaA,CAE7C,OAAO8C,IAMTO,QAAS,SAASpD,GAChBA,EAAUA,EAAUrE,EAAEmF,MAAMd,KAC5B,IAAIwC,GAAQtH,IACZ,IAAIuH,GAAUzC,EAAQyC,OAEtB,IAAIW,GAAU,WACZZ,EAAM5E,QAAQ,UAAW4E,EAAOA,EAAMrC,WAAYH,GAGpDA,GAAQyC,QAAU,SAASC,GACzB,GAAI1C,EAAQ+C,MAAQP,EAAMU,QAASE,GACnC,IAAIX,EAASA,EAAQD,EAAOE,EAAM1C,EAClC,KAAKwC,EAAMU,QAASV,EAAM5E,QAAQ,OAAQ4E,EAAOE,EAAM1C,GAGzD,IAAI9E,KAAKgI,QAAS,CAChBlD,EAAQyC,SACR,OAAO,OAETE,EAAUzH,KAAM8E,EAEhB,IAAI6C,GAAM3H,KAAK6F,KAAK,SAAU7F,KAAM8E,EACpC,KAAKA,EAAQ+C,KAAMK,GACnB,OAAOP,IAMTQ,IAAK,WACH,GAAIC,GAAO3H,EAAE2E,OAAOpF,KAAM,YAAcS,EAAE2E,OAAOpF,KAAKiF,WAAY,QAAUoD,GAC5E,IAAIrI,KAAKgI,QAAS,MAAOI,EACzB,OAAOA,IAAQA,EAAKE,OAAOF,EAAK3F,OAAS,KAAO,IAAM,GAAK,KAAO8F,mBAAmBvI,KAAKsD,KAK5F4B,MAAO,SAASsC,EAAM1C,GACpB,MAAO0C,IAIT5B,MAAO,WACL,MAAO,IAAI5F,MAAKwI,YAAYxI,KAAK6E,aAInCmD,MAAO,WACL,MAAOhI,MAAKsD,IAAM,MAIpBmF,QAAS,SAAS3D,GAChB,MAAO9E,MAAKyG,aAAchG,EAAEkE,OAAOG,OAAiB8C,SAAU,SAKhEnB,UAAW,SAAS1B,EAAOD,GACzB,IAAKA,EAAQ8C,WAAa5H,KAAK4H,SAAU,MAAO,KAChD7C,GAAQtE,EAAEkE,UAAW3E,KAAK6E,WAAYE,EACtC,IAAI2D,GAAQ1I,KAAKyF,gBAAkBzF,KAAK4H,SAAS7C,EAAOD,IAAY,IACpE,KAAK4D,EAAO,MAAO,KACnB1I,MAAK0C,QAAQ,UAAW1C,KAAM0I,EAAOjI,EAAEkE,OAAOG,GAAUW,gBAAiBiD,IACzE,OAAO,SAMX,IAAIC,IAAgB,OAAQ,SAAU,QAAS,SAAU,OAAQ,OAGjElI,GAAE4D,KAAKsE,EAAc,SAASpE,GAC5BK,EAAMY,UAAUjB,GAAU,WACxB,GAAI5B,GAAOtC,EAAMuC,KAAKb,UACtBY,GAAKiG,QAAQ5I,KAAK6E,WAClB,OAAOpE,GAAE8D,GAAQzC,MAAMrB,EAAGkC,KAiB9B,IAAIkG,GAAa3I,EAAS2I,WAAa,SAASC,EAAQhE,GACtDA,IAAYA,KACZ,IAAIA,EAAQwC,MAAOtH,KAAKsH,MAAQxC,EAAQwC,KACxC,IAAIxC,EAAQiE,iBAAoB,GAAG/I,KAAK+I,WAAajE,EAAQiE,UAC7D/I,MAAKgJ,QACLhJ,MAAKuF,WAAWzD,MAAM9B,KAAM+B,UAC5B,IAAI+G,EAAQ9I,KAAKiJ,MAAMH,EAAQrI,EAAEkE,QAAQ0B,OAAQ,MAAOvB,IAI1D,IAAIoE,IAAcC,IAAK,KAAM/F,OAAQ,KAAMgG,MAAO,KAClD,IAAIC,IAAcF,IAAK,KAAM/F,OAAQ,MAGrC3C,GAAEkE,OAAOkE,EAAWrD,UAAWtE,GAI7BoG,MAAO1C,EAIPW,WAAY,aAIZI,OAAQ,SAASb,GACf,MAAO9E,MAAKsJ,IAAI,SAAShC,GAAQ,MAAOA,GAAM3B,OAAOb,MAIvDe,KAAM,WACJ,MAAO3F,GAAS2F,KAAK/D,MAAM9B,KAAM+B,YAInCoH,IAAK,SAASL,EAAQhE,GACpB,MAAO9E,MAAKqF,IAAIyD,EAAQrI,EAAEkE,QAAQyE,MAAO,OAAQtE,EAASuE,KAI5DjG,OAAQ,SAAS0F,EAAQhE,GACvB,GAAIyE,IAAY9I,EAAE+I,QAAQV,EAC1BA,GAASS,GAAYT,GAAUrI,EAAEmF,MAAMkD,EACvChE,KAAYA,KACZ,IAAI1C,GAAGC,EAAGoH,EAAOnC,CACjB,KAAKlF,EAAI,EAAGC,EAAIyG,EAAOrG,OAAQL,EAAIC,EAAGD,IAAK,CACzCkF,EAAQwB,EAAO1G,GAAKpC,KAAK8F,IAAIgD,EAAO1G,GACpC,KAAKkF,EAAO,eACLtH,MAAK0J,MAAMpC,EAAMhE,UACjBtD,MAAK0J,MAAMpC,EAAMtC,IACxByE,GAAQzJ,KAAK2J,QAAQrC,EACrBtH,MAAK8I,OAAOxI,OAAOmJ,EAAO,EAC1BzJ,MAAKyC,QACL,KAAKqC,EAAQuB,OAAQ,CACnBvB,EAAQ2E,MAAQA,CAChBnC,GAAM5E,QAAQ,SAAU4E,EAAOtH,KAAM8E,GAEvC9E,KAAK4J,iBAAiBtC,GAExB,MAAOiC,GAAWT,EAAO,GAAKA,GAOhCzD,IAAK,SAASyD,EAAQhE,GACpBA,EAAUrE,EAAE0E,YAAaL,EAASoE,EAClC,IAAIpE,EAAQI,MAAO4D,EAAS9I,KAAKkF,MAAM4D,EAAQhE,EAC/C,IAAIyE,IAAY9I,EAAE+I,QAAQV,EAC1BA,GAASS,EAAYT,GAAUA,MAAgBrI,EAAEmF,MAAMkD,EACvD,IAAI1G,GAAGC,EAAGiB,EAAIgE,EAAOvC,EAAO8E,EAAUC,CACtC,IAAIC,GAAKjF,EAAQiF,EACjB,IAAIC,GAAchK,KAAKsH,KACvB,IAAI2C,GAAWjK,KAAK+I,YAAegB,GAAM,MAASjF,EAAQgF,OAAS,KACnE,IAAII,GAAWzJ,EAAE0J,SAASnK,KAAK+I,YAAc/I,KAAK+I,WAAa,IAC/D,IAAIqB,MAAYC,KAAeC,IAC/B,IAAInB,GAAMrE,EAAQqE,IAAKC,EAAQtE,EAAQsE,MAAOhG,EAAS0B,EAAQ1B,MAC/D,IAAImH,IAASN,GAAYd,GAAO/F,KAAc,KAI9C,KAAKhB,EAAI,EAAGC,EAAIyG,EAAOrG,OAAQL,EAAIC,EAAGD,IAAK,CACzC2C,EAAQ+D,EAAO1G,EACf,IAAI2C,YAAiBH,GAAO,CAC1BtB,EAAKgE,EAAQvC,MACR,CACLzB,EAAKyB,EAAMiF,EAAYxE,UAAUE,aAKnC,GAAImE,EAAW7J,KAAK8F,IAAIxC,GAAK,CAC3B,GAAIF,EAAQkH,EAAST,EAAS7E,KAAO,IACrC,IAAIoE,EAAO,CACTrE,EAAQA,IAAUuC,EAAQA,EAAMzC,WAAaE,CAC7C,IAAID,EAAQI,MAAOH,EAAQ8E,EAAS3E,MAAMH,EAAOD,EACjD+E,GAASxE,IAAIN,EAAOD,EACpB,IAAImF,IAAaH,GAAQD,EAAS9C,WAAWmD,GAAWJ,EAAO,KAEjEhB,EAAO1G,GAAKyH,MAGP,IAAIV,EAAK,CACd7B,EAAQwB,EAAO1G,GAAKpC,KAAKwK,cAAczF,EAAOD,EAC9C,KAAKwC,EAAO,QACZ8C,GAAMhK,KAAKkH,EAIXA,GAAMnG,GAAG,MAAOnB,KAAKyK,cAAezK,KACpCA,MAAK0J,MAAMpC,EAAMtC,KAAOsC,CACxB,IAAIA,EAAMhE,IAAM,KAAMtD,KAAK0J,MAAMpC,EAAMhE,IAAMgE,EAE/C,GAAIiD,EAAOA,EAAMnK,KAAKyJ,GAAYvC,GAIpC,GAAIlE,EAAQ,CACV,IAAKhB,EAAI,EAAGC,EAAIrC,KAAKyC,OAAQL,EAAIC,IAAKD,EAAG,CACvC,IAAKkI,GAAUhD,EAAQtH,KAAK8I,OAAO1G,IAAI4C,KAAMqF,EAASjK,KAAKkH,GAE7D,GAAI+C,EAAS5H,OAAQzC,KAAKoD,OAAOiH,EAAUvF,GAI7C,GAAIsF,EAAM3H,QAAW8H,GAASA,EAAM9H,OAAS,CAC3C,GAAIwH,EAAUH,EAAO,IACrB9J,MAAKyC,QAAU2H,EAAM3H,MACrB,IAAIsH,GAAM,KAAM,CACd,IAAK3H,EAAI,EAAGC,EAAI+H,EAAM3H,OAAQL,EAAIC,EAAGD,IAAK,CACxCpC,KAAK8I,OAAOxI,OAAOyJ,EAAK3H,EAAG,EAAGgI,EAAMhI,SAEjC,CACL,GAAImI,EAAOvK,KAAK8I,OAAOrG,OAAS,CAChC,IAAIiI,GAAgBH,GAASH,CAC7B,KAAKhI,EAAI,EAAGC,EAAIqI,EAAcjI,OAAQL,EAAIC,EAAGD,IAAK,CAChDpC,KAAK8I,OAAO1I,KAAKsK,EAActI,MAMrC,GAAI0H,EAAM9J,KAAK8J,MAAMzD,OAAQ,MAG7B,KAAKvB,EAAQuB,OAAQ,CACnB,IAAKjE,EAAI,EAAGC,EAAI+H,EAAM3H,OAAQL,EAAIC,EAAGD,IAAK,EACvCkF,EAAQ8C,EAAMhI,IAAIM,QAAQ,MAAO4E,EAAOtH,KAAM8E,GAEjD,GAAIgF,GAASS,GAASA,EAAM9H,OAASzC,KAAK0C,QAAQ,OAAQ1C,KAAM8E,GAIlE,MAAOyE,GAAWT,EAAO,GAAKA,GAOhCG,MAAO,SAASH,EAAQhE,GACtBA,IAAYA,KACZ,KAAK,GAAI1C,GAAI,EAAGC,EAAIrC,KAAK8I,OAAOrG,OAAQL,EAAIC,EAAGD,IAAK,CAClDpC,KAAK4J,iBAAiB5J,KAAK8I,OAAO1G,IAEpC0C,EAAQ6F,eAAiB3K,KAAK8I,MAC9B9I,MAAKgJ,QACLF,GAAS9I,KAAKmJ,IAAIL,EAAQrI,EAAEkE,QAAQ0B,OAAQ,MAAOvB,GACnD,KAAKA,EAAQuB,OAAQrG,KAAK0C,QAAQ,QAAS1C,KAAM8E,EACjD,OAAOgE,IAIT1I,KAAM,SAASkH,EAAOxC,GACpB,MAAO9E,MAAKmJ,IAAI7B,EAAO7G,EAAEkE,QAAQoF,GAAI/J,KAAKyC,QAASqC,KAIrD8F,IAAK,SAAS9F,GACZ,GAAIwC,GAAQtH,KAAK+J,GAAG/J,KAAKyC,OAAS,EAClCzC,MAAKoD,OAAOkE,EAAOxC,EACnB,OAAOwC,IAITsB,QAAS,SAAStB,EAAOxC,GACvB,MAAO9E,MAAKmJ,IAAI7B,EAAO7G,EAAEkE,QAAQoF,GAAI,GAAIjF,KAI3C+F,MAAO,SAAS/F,GACd,GAAIwC,GAAQtH,KAAK+J,GAAG,EACpB/J,MAAKoD,OAAOkE,EAAOxC,EACnB,OAAOwC,IAITjH,MAAO,WACL,MAAOA,GAAMyB,MAAM9B,KAAK8I,OAAQ/G,YAIlC+D,IAAK,SAAS7C,GACZ,GAAIA,GAAO,KAAM,WAAY,EAC7B,OAAOjD,MAAK0J,MAAMzG,EAAIK,KAAOtD,KAAK0J,MAAMzG,EAAI+B,MAAQhF,KAAK0J,MAAMzG,IAIjE8G,GAAI,SAASN,GACX,MAAOzJ,MAAK8I,OAAOW,IAKrBqB,MAAO,SAAS/F,EAAOgG,GACrB,GAAItK,EAAE8C,QAAQwB,GAAQ,MAAOgG,OAAa,KAC1C,OAAO/K,MAAK+K,EAAQ,OAAS,UAAU,SAASzD,GAC9C,IAAK,GAAI3D,KAAOoB,GAAO,CACrB,GAAIA,EAAMpB,KAAS2D,EAAMxB,IAAInC,GAAM,MAAO,OAE5C,MAAO,SAMXqH,UAAW,SAASjG,GAClB,MAAO/E,MAAK8K,MAAM/F,EAAO,OAM3B+E,KAAM,SAAShF,GACb,IAAK9E,KAAK+I,WAAY,KAAM,IAAIkC,OAAM,yCACtCnG,KAAYA,KAGZ,IAAIrE,EAAE0J,SAASnK,KAAK+I,aAAe/I,KAAK+I,WAAWtG,SAAW,EAAG,CAC/DzC,KAAK8I,OAAS9I,KAAKkL,OAAOlL,KAAK+I,WAAY/I,UACtC,CACLA,KAAK8I,OAAOgB,KAAKrJ,EAAEgE,KAAKzE,KAAK+I,WAAY/I,OAG3C,IAAK8E,EAAQuB,OAAQrG,KAAK0C,QAAQ,OAAQ1C,KAAM8E,EAChD,OAAO9E,OAITmL,MAAO,SAASpF,GACd,MAAOtF,GAAE2K,OAAOpL,KAAK8I,OAAQ,MAAO/C,IAMtCsB,MAAO,SAASvC,GACdA,EAAUA,EAAUrE,EAAEmF,MAAMd,KAC5B,IAAIA,EAAQI,YAAe,GAAGJ,EAAQI,MAAQ,IAC9C,IAAIqC,GAAUzC,EAAQyC,OACtB,IAAItC,GAAajF,IACjB8E,GAAQyC,QAAU,SAASC,GACzB,GAAIjD,GAASO,EAAQmE,MAAQ,QAAU,KACvChE,GAAWV,GAAQiD,EAAM1C,EACzB,IAAIyC,EAASA,EAAQtC,EAAYuC,EAAM1C,EACvCG,GAAWvC,QAAQ,OAAQuC,EAAYuC,EAAM1C,GAE/C2C,GAAUzH,KAAM8E,EAChB,OAAO9E,MAAK6F,KAAK,OAAQ7F,KAAM8E,IAMjCuG,OAAQ,SAAS/D,EAAOxC,GACtBA,EAAUA,EAAUrE,EAAEmF,MAAMd,KAC5B,MAAMwC,EAAQtH,KAAKwK,cAAclD,EAAOxC,IAAW,MAAO,MAC1D,KAAKA,EAAQ+C,KAAM7H,KAAKmJ,IAAI7B,EAAOxC,EACnC,IAAIG,GAAajF,IACjB,IAAIuH,GAAUzC,EAAQyC,OACtBzC,GAAQyC,QAAU,SAASD,EAAOE,EAAM1C,GACtC,GAAIA,EAAQ+C,KAAM5C,EAAWkE,IAAI7B,EAAOxC,EACxC,IAAIyC,EAASA,EAAQD,EAAOE,EAAM1C,GAEpCwC,GAAMI,KAAK,KAAM5C,EACjB,OAAOwC,IAKTpC,MAAO,SAASsC,EAAM1C,GACpB,MAAO0C,IAIT5B,MAAO,WACL,MAAO,IAAI5F,MAAKwI,YAAYxI,KAAK8I,SAKnCE,OAAQ,WACNhJ,KAAKyC,OAAS,CACdzC,MAAK8I,SACL9I,MAAK0J,UAKPc,cAAe,SAASzF,EAAOD,GAC7B,GAAIC,YAAiBH,GAAO,CAC1B,IAAKG,EAAME,WAAYF,EAAME,WAAajF,IAC1C,OAAO+E,GAETD,EAAUA,EAAUrE,EAAEmF,MAAMd,KAC5BA,GAAQG,WAAajF,IACrB,IAAIsH,GAAQ,GAAItH,MAAKsH,MAAMvC,EAAOD,EAClC,KAAKwC,EAAM7B,gBAAiB,MAAO6B,EACnCtH,MAAK0C,QAAQ,UAAW1C,KAAMsH,EAAM7B,gBAAiBX,EACrD,OAAO,QAIT8E,iBAAkB,SAAStC,GACzB,GAAItH,OAASsH,EAAMrC,iBAAmBqC,GAAMrC,UAC5CqC,GAAMzF,IAAI,MAAO7B,KAAKyK,cAAezK,OAOvCyK,cAAe,SAASa,EAAOhE,EAAOrC,EAAYH,GAChD,IAAKwG,IAAU,OAASA,IAAU,WAAarG,IAAejF,KAAM,MACpE,IAAIsL,IAAU,UAAWtL,KAAKoD,OAAOkE,EAAOxC,EAC5C,IAAIwC,GAASgE,IAAU,UAAYhE,EAAM5B,YAAa,OAC7C1F,MAAK0J,MAAMpC,EAAMH,SAASG,EAAM5B,aACvC,IAAI4B,EAAMhE,IAAM,KAAMtD,KAAK0J,MAAMpC,EAAMhE,IAAMgE,EAE/CtH,KAAK0C,QAAQZ,MAAM9B,KAAM+B,aAQ7B,IAAIwJ,IAAW,UAAW,OAAQ,MAAO,UAAW,SAAU,QAC5D,SAAU,cAAe,QAAS,OAAQ,SAAU,SAAU,SAC9D,SAAU,QAAS,MAAO,OAAQ,MAAO,UAAW,WAAY,SAChE,MAAO,MAAO,UAAW,OAAQ,QAAS,OAAQ,OAAQ,UAAW,OACrE,OAAQ,OAAQ,OAAQ,UAAW,aAAc,UAAW,UAC5D,cAAe,UAAW,QAG5B9K,GAAE4D,KAAKkH,EAAS,SAAShH,GACvBsE,EAAWrD,UAAUjB,GAAU,WAC7B,GAAI5B,GAAOtC,EAAMuC,KAAKb,UACtBY,GAAKiG,QAAQ5I,KAAK8I,OAClB,OAAOrI,GAAE8D,GAAQzC,MAAMrB,EAAGkC,KAK9B,IAAI6I,IAAoB,UAAW,UAAW,SAG9C/K,GAAE4D,KAAKmH,EAAkB,SAASjH,GAChCsE,EAAWrD,UAAUjB,GAAU,SAASkH,EAAOnK,GAC7C,GAAIoK,GAAWjL,EAAEkL,WAAWF,GAASA,EAAQ,SAASnE,GACpD,MAAOA,GAAMxB,IAAI2F,GAEnB,OAAOhL,GAAE8D,GAAQvE,KAAK8I,OAAQ4C,EAAUpK,KAiB5C,IAAIsK,GAAO1L,EAAS0L,KAAO,SAAS9G,GAClC9E,KAAKgF,IAAMvE,EAAE+D,SAAS,OACtBM,KAAYA,KACZrE,GAAEkE,OAAO3E,KAAMS,EAAEoL,KAAK/G,EAASgH,GAC/B9L,MAAK+L,gBACL/L,MAAKuF,WAAWzD,MAAM9B,KAAM+B,UAC5B/B,MAAKgM,iBAIP,IAAIC,GAAwB,gBAG5B,IAAIH,IAAe,QAAS,aAAc,KAAM,KAAM,aAAc,YAAa,UAAW,SAG5FrL,GAAEkE,OAAOiH,EAAKpG,UAAWtE,GAGvBgL,QAAS,MAITvL,EAAG,SAASwL,GACV,MAAOnM,MAAKoM,IAAIC,KAAKF,IAKvB5G,WAAY,aAKZ+G,OAAQ,WACN,MAAOtM,OAKToD,OAAQ,WACNpD,KAAKoM,IAAIhJ,QACTpD,MAAKgD,eACL,OAAOhD,OAKTuM,WAAY,SAASC,EAASC,GAC5B,GAAIzM,KAAKoM,IAAKpM,KAAK0M,kBACnB1M,MAAKoM,IAAMI,YAAmBtM,GAASS,EAAI6L,EAAUtM,EAASS,EAAE6L,EAChExM,MAAK2M,GAAK3M,KAAKoM,IAAI,EACnB,IAAIK,IAAa,MAAOzM,KAAKgM,gBAC7B,OAAOhM,OAkBTgM,eAAgB,SAASvK,GACvB,KAAMA,IAAWA,EAAShB,EAAE2E,OAAOpF,KAAM,YAAa,MAAOA,KAC7DA,MAAK0M,kBACL,KAAK,GAAI/I,KAAOlC,GAAQ,CACtB,GAAI8C,GAAS9C,EAAOkC,EACpB,KAAKlD,EAAEkL,WAAWpH,GAASA,EAASvE,KAAKyB,EAAOkC,GAChD,KAAKY,EAAQ,QAEb,IAAIqI,GAAQjJ,EAAIiJ,MAAMX,EACtB,IAAIY,GAAYD,EAAM,GAAIT,EAAWS,EAAM,EAC3CrI,GAAS9D,EAAEgE,KAAKF,EAAQvE,KACxB6M,IAAa,kBAAoB7M,KAAKgF,GACtC,IAAImH,IAAa,GAAI,CACnBnM,KAAKoM,IAAIjL,GAAG0L,EAAWtI,OAClB,CACLvE,KAAKoM,IAAIjL,GAAG0L,EAAWV,EAAU5H,IAGrC,MAAOvE,OAMT0M,iBAAkB,WAChB1M,KAAKoM,IAAIvK,IAAI,kBAAoB7B,KAAKgF,IACtC,OAAOhF,OAOT+L,eAAgB,WACd,IAAK/L,KAAK2M,GAAI,CACZ,GAAI5H,GAAQtE,EAAEkE,UAAWlE,EAAE2E,OAAOpF,KAAM,cACxC,IAAIA,KAAKsD,GAAIyB,EAAMzB,GAAK7C,EAAE2E,OAAOpF,KAAM,KACvC,IAAIA,KAAK8M,UAAW/H,EAAM,SAAWtE,EAAE2E,OAAOpF,KAAM,YACpD,IAAIoM,GAAMlM,EAASS,EAAE,IAAMF,EAAE2E,OAAOpF,KAAM,WAAa,KAAK+F,KAAKhB,EACjE/E,MAAKuM,WAAWH,EAAK,WAChB,CACLpM,KAAKuM,WAAW9L,EAAE2E,OAAOpF,KAAM,MAAO,UAwB5CE,GAAS2F,KAAO,SAAStB,EAAQ+C,EAAOxC,GACtC,GAAIiI,GAAOC,EAAUzI,EAGrB9D,GAAE0E,SAASL,IAAYA,OACrB9D,YAAad,EAASc,YACtBC,YAAaf,EAASe,aAIxB,IAAIgM,IAAUF,KAAMA,EAAMG,SAAU,OAGpC,KAAKpI,EAAQqD,IAAK,CAChB8E,EAAO9E,IAAM1H,EAAE2E,OAAOkC,EAAO,QAAUe,IAIzC,GAAIvD,EAAQqI,MAAQ,MAAQ7F,IAAU/C,IAAW,UAAYA,IAAW,UAAYA,IAAW,SAAU,CACvG0I,EAAOG,YAAc,kBACrBH,GAAOE,KAAOE,KAAKC,UAAUxI,EAAQC,OAASuC,EAAM3B,OAAOb,IAI7D,GAAIA,EAAQ7D,YAAa,CACvBgM,EAAOG,YAAc,mCACrBH,GAAOE,KAAOF,EAAOE,MAAQ7F,MAAO2F,EAAOE,SAK7C,GAAIrI,EAAQ9D,cAAgB+L,IAAS,OAASA,IAAS,UAAYA,IAAS,SAAU,CACpFE,EAAOF,KAAO,MACd,IAAIjI,EAAQ7D,YAAagM,EAAOE,KAAKI,QAAUR,CAC/C,IAAIS,GAAa1I,EAAQ0I,UACzB1I,GAAQ0I,WAAa,SAAS7F,GAC5BA,EAAI8F,iBAAiB,yBAA0BV,EAC/C,IAAIS,EAAY,MAAOA,GAAW1L,MAAM9B,KAAM+B,YAKlD,GAAIkL,EAAOF,OAAS,QAAUjI,EAAQ7D,YAAa,CACjDgM,EAAOS,YAAc,MAMvB,GAAIT,EAAOF,OAAS,SAAWY,EAAY,CACzCV,EAAOtF,IAAM,WACX,MAAO,IAAIiG,eAAc,sBAK7B,GAAIjG,GAAM7C,EAAQ6C,IAAMzH,EAAS2N,KAAKpN,EAAEkE,OAAOsI,EAAQnI,GACvDwC,GAAM5E,QAAQ,UAAW4E,EAAOK,EAAK7C,EACrC,OAAO6C,GAGT,IAAIgG,SAAoBG,UAAW,eAAiBA,OAAOF,iBAAmBE,OAAOC,iBAAkB,GAAKA,iBAAgBC,cAG5H,IAAIhB,IACF3B,OAAU,OACV4C,OAAU,MACVhG,MAAU,QACViG,SAAU,SACVC,KAAU,MAKZjO,GAAS2N,KAAO,WACd,MAAO3N,GAASS,EAAEkN,KAAK/L,MAAM5B,EAASS,EAAGoB,WAQ3C,IAAIqM,GAASlO,EAASkO,OAAS,SAAStJ,GACtCA,IAAYA,KACZ,IAAIA,EAAQuJ,OAAQrO,KAAKqO,OAASvJ,EAAQuJ,MAC1CrO,MAAKsO,aACLtO,MAAKuF,WAAWzD,MAAM9B,KAAM+B,WAK9B,IAAIwM,GAAgB,YACpB,IAAIC,GAAgB,cACpB,IAAIC,GAAgB,QACpB,IAAIC,GAAgB,0BAGpBjO,GAAEkE,OAAOyJ,EAAO5I,UAAWtE,GAIzBqE,WAAY,aAQZoJ,MAAO,SAASA,EAAOvN,EAAMC,GAC3B,IAAKZ,EAAEmO,SAASD,GAAQA,EAAQ3O,KAAK6O,eAAeF,EACpD,IAAIlO,EAAEkL,WAAWvK,GAAO,CACtBC,EAAWD,CACXA,GAAO,GAET,IAAKC,EAAUA,EAAWrB,KAAKoB,EAC/B,IAAI0N,GAAS9O,IACbE,GAAS6O,QAAQJ,MAAMA,EAAO,SAASK,GACrC,GAAIrM,GAAOmM,EAAOG,mBAAmBN,EAAOK,EAC5C3N,IAAYA,EAASS,MAAMgN,EAAQnM,EACnCmM,GAAOpM,QAAQZ,MAAMgN,GAAS,SAAW1N,GAAMwC,OAAOjB,GACtDmM,GAAOpM,QAAQ,QAAStB,EAAMuB,EAC9BzC,GAAS6O,QAAQrM,QAAQ,QAASoM,EAAQ1N,EAAMuB,IAElD,OAAO3C,OAITkP,SAAU,SAASF,EAAUlK,GAC3B5E,EAAS6O,QAAQG,SAASF,EAAUlK,EACpC,OAAO9E,OAMTsO,YAAa,WACX,IAAKtO,KAAKqO,OAAQ,MAClBrO,MAAKqO,OAAS5N,EAAE2E,OAAOpF,KAAM,SAC7B,IAAI2O,GAAON,EAAS5N,EAAE+B,KAAKxC,KAAKqO,OAChC,QAAQM,EAAQN,EAAOzD,QAAU,KAAM,CACrC5K,KAAK2O,MAAMA,EAAO3O,KAAKqO,OAAOM,MAMlCE,eAAgB,SAASF,GACvBA,EAAQA,EAAMQ,QAAQT,EAAc,QACtBS,QAAQZ,EAAe,WACvBY,QAAQX,EAAY,SAAS5B,EAAOwC,GACnC,MAAOA,GAAWxC,EAAQ,YAE3BuC,QAAQV,EAAY,QAClC,OAAO,IAAIY,QAAO,IAAMV,EAAQ,MAMlCM,mBAAoB,SAASN,EAAOK,GAClC,GAAI/B,GAAS0B,EAAMW,KAAKN,GAAU3O,MAAM,EACxC,OAAOI,GAAE6I,IAAI2D,EAAQ,SAASsC,GAC5B,MAAOA,GAAQC,mBAAmBD,GAAS,SAcjD,IAAIE,GAAUvP,EAASuP,QAAU,WAC/BzP,KAAK0P,WACLjP,GAAEkP,QAAQ3P,KAAM,WAGhB,UAAW8N,UAAW,YAAa,CACjC9N,KAAK4P,SAAW9B,OAAO8B,QACvB5P,MAAK+O,QAAUjB,OAAOiB,SAK1B,IAAIc,GAAgB,cAGpB,IAAIC,GAAe,YAGnB,IAAIC,GAAa,aAGjB,IAAIC,GAAgB,KAGpB,IAAIC,GAAe,SAGnBR,GAAQS,QAAU,KAGlBzP,GAAEkE,OAAO8K,EAAQjK,UAAWtE,GAI1BiP,SAAU,GAIVC,QAAS,SAAStC,GAChB,GAAIlB,IAASkB,GAAU9N,MAAM4P,SAASS,KAAKzD,MAAM,SACjD,OAAOA,GAAQA,EAAM,GAAK,IAK5B0D,YAAa,SAAStB,EAAUuB,GAC9B,GAAIvB,GAAY,KAAM,CACpB,GAAIhP,KAAKwQ,gBAAkBxQ,KAAKyQ,kBAAoBF,EAAgB,CAClEvB,EAAWhP,KAAK4P,SAASc,QACzB,IAAI3Q,GAAOC,KAAKD,KAAKoP,QAAQa,EAAe,GAC5C,KAAKhB,EAASrF,QAAQ5J,GAAOiP,EAAWA,EAAS3O,MAAMN,EAAK0C,YACvD,CACLuM,EAAWhP,KAAKoQ,WAGpB,MAAOpB,GAASG,QAAQU,EAAe,KAKzCc,MAAO,SAAS7L,GACd,GAAI2K,EAAQS,QAAS,KAAM,IAAIjF,OAAM,4CACrCwE,GAAQS,QAAU,IAIlBlQ,MAAK8E,QAAmBrE,EAAEkE,QAAQ5E,KAAM,KAAMC,KAAK8E,QAASA,EAC5D9E,MAAKD,KAAmBC,KAAK8E,QAAQ/E,IACrCC,MAAKyQ,iBAAmBzQ,KAAK8E,QAAQ8L,aAAe,KACpD5Q,MAAK6Q,kBAAqB7Q,KAAK8E,QAAQgM,SACvC9Q,MAAKwQ,iBAAsBxQ,KAAK8E,QAAQgM,WAAa9Q,KAAK+O,SAAW/O,KAAK+O,QAAQ+B,UAClF,IAAI9B,GAAoBhP,KAAKsQ,aAC7B,IAAIS,GAAoBC,SAASC,YACjC,IAAIC,GAAqBnB,EAAWT,KAAK6B,UAAUC,UAAUC,kBAAoBN,GAAWA,GAAW,EAGvG/Q,MAAKD,MAAQ,IAAMC,KAAKD,KAAO,KAAKoP,QAAQW,EAAc,IAE1D,IAAIoB,GAASlR,KAAKyQ,iBAAkB,CAClCzQ,KAAKsR,OAASpR,EAASS,EAAE,+CAA+C4Q,OAAOC,SAAS,QAAQ,GAAGC,aACnGzR,MAAKkP,SAASF,GAKhB,GAAIhP,KAAKwQ,cAAe,CACtBtQ,EAASS,EAAEmN,QAAQ3M,GAAG,WAAYnB,KAAK0R,cAClC,IAAI1R,KAAKyQ,kBAAqB,gBAAkB3C,UAAYoD,EAAO,CACxEhR,EAASS,EAAEmN,QAAQ3M,GAAG,aAAcnB,KAAK0R,cACpC,IAAI1R,KAAKyQ,iBAAkB,CAChCzQ,KAAK2R,kBAAoBC,YAAY5R,KAAK0R,SAAU1R,KAAKmQ,UAK3DnQ,KAAKgP,SAAWA,CAChB,IAAI6C,GAAM7R,KAAK4P,QACf,IAAIkC,GAASD,EAAInB,SAASvB,QAAQ,SAAU,SAAWnP,KAAKD,IAI5D,IAAIC,KAAKyQ,kBAAoBzQ,KAAK6Q,gBAAiB,CAIjD,IAAK7Q,KAAKwQ,gBAAkBsB,EAAQ,CAClC9R,KAAKgP,SAAWhP,KAAKsQ,YAAY,KAAM,KACvCtQ,MAAK4P,SAAST,QAAQnP,KAAKD,KAAOC,KAAK4P,SAASmC,OAAS,IAAM/R,KAAKgP,SAEpE,OAAO,UAIF,IAAIhP,KAAKwQ,eAAiBsB,GAAUD,EAAIG,KAAM,CACnDhS,KAAKgP,SAAWhP,KAAKoQ,UAAUjB,QAAQU,EAAe,GACtD7P,MAAK+O,QAAQkD,gBAAiBjB,SAASkB,MAAOlS,KAAKD,KAAOC,KAAKgP,SAAW6C,EAAIE,SAKlF,IAAK/R,KAAK8E,QAAQuB,OAAQ,MAAOrG,MAAKmS,WAKxCC,KAAM,WACJlS,EAASS,EAAEmN,QAAQjM,IAAI,WAAY7B,KAAK0R,UAAU7P,IAAI,aAAc7B,KAAK0R,SACzEW,eAAcrS,KAAK2R,kBACnBlC,GAAQS,QAAU,OAKpBvB,MAAO,SAASA,EAAOtN,GACrBrB,KAAK0P,SAAS9G,SAAS+F,MAAOA,EAAOtN,SAAUA,KAKjDqQ,SAAU,SAASY,GACjB,GAAI9L,GAAUxG,KAAKsQ,aACnB,IAAI9J,IAAYxG,KAAKgP,UAAYhP,KAAKsR,OAAQ,CAC5C9K,EAAUxG,KAAKsQ,YAAYtQ,KAAKoQ,QAAQpQ,KAAKsR,SAE/C,GAAI9K,IAAYxG,KAAKgP,SAAU,MAAO,MACtC,IAAIhP,KAAKsR,OAAQtR,KAAKkP,SAAS1I,EAC/BxG,MAAKmS,WAMPA,QAAS,SAASnD,GAChBA,EAAWhP,KAAKgP,SAAWhP,KAAKsQ,YAAYtB,EAC5C,OAAOvO,GAAE8R,IAAIvS,KAAK0P,SAAU,SAAS8C,GACnC,GAAIA,EAAQ7D,MAAM9K,KAAKmL,GAAW,CAChCwD,EAAQnR,SAAS2N,EACjB,OAAO,UAYbE,SAAU,SAASF,EAAUlK,GAC3B,IAAK2K,EAAQS,QAAS,MAAO,MAC7B,KAAKpL,GAAWA,IAAY,KAAMA,GAAWpC,UAAWoC,EAExD,IAAIqD,GAAMnI,KAAKD,MAAQiP,EAAWhP,KAAKsQ,YAAYtB,GAAY,IAG/DA,GAAWA,EAASG,QAAQc,EAAc,GAE1C,IAAIjQ,KAAKgP,WAAaA,EAAU,MAChChP,MAAKgP,SAAWA,CAGhB,IAAIA,IAAa,IAAM7G,IAAQ,IAAKA,EAAMA,EAAI9H,MAAM,GAAI,EAGxD,IAAIL,KAAKwQ,cAAe,CACtBxQ,KAAK+O,QAAQjK,EAAQqK,QAAU,eAAiB,gBAAiB6B,SAASkB,MAAO/J,OAI5E,IAAInI,KAAKyQ,iBAAkB,CAChCzQ,KAAKyS,YAAYzS,KAAK4P,SAAUZ,EAAUlK,EAAQqK,QAClD,IAAInP,KAAKsR,QAAWtC,IAAahP,KAAKsQ,YAAYtQ,KAAKoQ,QAAQpQ,KAAKsR,SAAW,CAI7E,IAAIxM,EAAQqK,QAASnP,KAAKsR,OAAON,SAAS0B,OAAOC,OACjD3S,MAAKyS,YAAYzS,KAAKsR,OAAO1B,SAAUZ,EAAUlK,EAAQqK,cAKtD,CACL,MAAOnP,MAAK4P,SAASgD,OAAOzK,GAE9B,GAAIrD,EAAQpC,QAAS,MAAO1C,MAAKmS,QAAQnD,IAK3CyD,YAAa,SAAS7C,EAAUZ,EAAUG,GACxC,GAAIA,EAAS,CACX,GAAIkB,GAAOT,EAASS,KAAKlB,QAAQ,qBAAsB,GACvDS,GAAST,QAAQkB,EAAO,IAAMrB,OACzB,CAELY,EAASoC,KAAO,IAAMhD,KAO5B9O,GAAS6O,QAAU,GAAIU,EAQvB,IAAI9K,GAAS,SAASkO,EAAYC,GAChC,GAAIC,GAAS/S,IACb,IAAIgT,EAKJ,IAAIH,GAAcpS,EAAEwF,IAAI4M,EAAY,eAAgB,CAClDG,EAAQH,EAAWrK,gBACd,CACLwK,EAAQ,WAAY,MAAOD,GAAOjR,MAAM9B,KAAM+B,YAIhDtB,EAAEkE,OAAOqO,EAAOD,EAAQD,EAIxB,IAAIG,GAAY,WAAYjT,KAAKwI,YAAcwK,EAC/CC,GAAUzN,UAAYuN,EAAOvN,SAC7BwN,GAAMxN,UAAY,GAAIyN,EAItB,IAAIJ,EAAYpS,EAAEkE,OAAOqO,EAAMxN,UAAWqN,EAI1CG,GAAME,UAAYH,EAAOvN,SAEzB,OAAOwN,GAITpO,GAAMD,OAASkE,EAAWlE,OAASyJ,EAAOzJ,OAASiH,EAAKjH,OAAS8K,EAAQ9K,OAASA,CAGlF,IAAI0D,GAAW,WACb,KAAM,IAAI4C,OAAM,kDAIlB,IAAIxD,GAAY,SAASH,EAAOxC,GAC9B,GAAI4D,GAAQ5D,EAAQ4D,KACpB5D,GAAQ4D,MAAQ,SAASlB,GACvB,GAAIkB,EAAOA,EAAMpB,EAAOE,EAAM1C,EAC9BwC,GAAM5E,QAAQ,QAAS4E,EAAOE,EAAM1C,OAIvClC,KAAK5C"}