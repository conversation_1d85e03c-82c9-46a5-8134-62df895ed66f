<style>
    .switch {
        position: relative;
        display: inline-block;
    }

    .switch input {
        display: none;
    }

    .slider {
        margin: 0;
        display: inline-block;
        position: relative;
        width: 40px;
        height: 20px;
        border: 1px solid #dcdfe6;
        outline: none;
        border-radius: 10px;
        box-sizing: border-box;
        background: #dcdfe6;
        cursor: pointer;
        transition: border-color .31s, background-color .3s;
        vertical-align: middle;
    }

    .slider:before {
        content: "";
        position: absolute;
        top: 1px;
        left: 1px;
        border-radius: 50%;
        transition: all .3s;
        width: 16px;
        height: 16px;
        background-color: #fff;
    }

    input:checked+.slider {
        background-color: #2196F3;
    }

    input:checked+.slider:before {
        left: 100%;
        margin-left: -17px;
    }
</style>

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <h1 class="page-header">
                <span class="glyphicon glyphicon-cog"></span> 微信 token 列表
            </h1>
        </div>
    </div>
    <div class="row">
        <div class="col-md-12">
            <h3><span class="glyphicon glyphicon-refresh"></span> 删除 token </h3>
            <button class = 'btn btn-primary' onclick="refresh('ds')">ds</button>
            <button class = 'btn btn-primary' onclick="refresh('ivy')">ivy</button>
            <button class = 'btn btn-primary' onclick="refresh('mmx')">mmx</button>
        </div>
        <div class="col-md-12">
            <h3><span class="glyphicon glyphicon-list"></span> token 列表</h3>
        <?php
			$this->widget('ext.ivyCGridView.BsCGridView', array(
				'id' => 'cardsList',
				'afterAjaxUpdate' => 'js:head.Util.modal',
				'dataProvider' => $dataProvider,
				'template' => "{items}{pager}",
				'colgroups' => array(
					array(
						"colwidth" => array(200, 200, 200, 200, 200, 200),
					)
				),
				'columns' => array(
					'wechat_key',
					'appid',
					'token',
					'expires_in',
					array(
						'name'=>'status',
						'value'=> '($data->updated_at + $data->expires_in > time()) ? "有效" : "无效" ',
					),
					array(
						'name'=>'updated_at',
						'value'=> 'date("Y-m-d H:i:s", $data->updated_at)',
					),
				),
			));
			?>
        </div>
    </div>
</div>

<script>
    function refresh(key) {
        if (confirm("确定删除吗？")) {
            $.ajax({
                url: '<?php echo $this->createUrl('delToken'); ?>',
                data: {"key": key},
                type: 'POST',
                dataType: 'json',
                success: function(data) {
                    if (data.state === 'fail') {
                        head.dialog.alert(data.message);
                        return false;
                    }
                    resultTip({msg: data.message});
                    if (typeof(data.callback) !== 'undefined') {
                        eval(data.callback + '(data.data)');
                    }
                },
                error: function() {
                    alert("访问异常！");
                }
            });
        }
    }
</script>