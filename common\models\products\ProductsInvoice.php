<?php

/**
 * This is the model class for table "ivy_products_invoice".
 *
 * The followings are the available columns in table 'ivy_products_invoice':
 * @property integer $id
 * @property integer $invoice_id
 * @property integer $childid
 * @property integer $classid
 * @property string $school_id
 * @property double $amount
 * @property integer $product_count
 * @property integer $status
 * @property string $open_id
 * @property integer $updated_userid
 * @property integer $updated_time
 */
class ProductsInvoice extends CActiveRecord
{
	const STATS_UNPAID = 10;            //未付款
	const STATS_PAID = 20;              //款付清
	const STATS_CONFIRM = 30;    		//已确认
	const STATS_SEND = 40; 				//已发放
	const STATS_WAITREFUND = 41; 		//等待退款
	const STATS_REFUNDING = 45; 		//退款中
	const STATS_REFUNDED = 50; 			//已退款
	const STATS_EXCHANGE = 55; 		    //换货中
	const STATS_EXCHANGED = 60; 		//已换货

	const STATS_CANCELLED = 99;         //作废
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_products_invoice';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('invoice_id, childid, classid, school_id, amount, product_count, status, updated_userid, updated_time', 'required'),
			array('invoice_id, childid, classid, product_count, status, updated_userid, updated_time', 'numerical', 'integerOnly'=>true),
			array('amount', 'numerical'),
			array('school_id, open_id', 'length', 'max'=>255),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, invoice_id, childid, classid, school_id, amount, product_count, status, open_id, updated_userid, updated_time', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'invoice_id' => 'Invoice',
			'childid' => Yii::t('user','Child Name'),
			'classid' => Yii::t('teaching','class'),
			'school_id' => Yii::t('user','School Name'),
			'amount' => Yii::t('payment','Amount'),
			'product_count' => Yii::t('glogob','商品数量'),
			'status' => 'Status',
			'open_id' => 'Open',
			'updated_userid' => 'Updated Userid',
			'updated_time' => Yii::t('laseregg','Add Timestamp'),
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('invoice_id',$this->invoice_id);
		$criteria->compare('childid',$this->childid);
		$criteria->compare('classid',$this->classid);
		$criteria->compare('school_id',$this->school_id,true);
		$criteria->compare('amount',$this->amount);
		$criteria->compare('product_count',$this->product_count);
		$criteria->compare('status',$this->status);
		$criteria->compare('open_id',$this->open_id,true);
		$criteria->compare('updated_userid',$this->updated_userid);
		$criteria->compare('updated_time',$this->updated_time);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return ProductsInvoice the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	public static function statusConfig()
	{
		return array(
			10 => Yii::t('products', '未付款'),
			20 => Yii::t('products', '待确认'),
			30 => Yii::t('products', '已确认'),
			40 => Yii::t('products', '已发放'),
			41 => Yii::t('products', '等待退款'),
            45 => Yii::t('products', '退款中'),
            50 => Yii::t('products', '已退款'),
            55 => Yii::t('products', '换货中'),
            60 => Yii::t('products', '已发放'),#完成换货
			99 => Yii::t('products', '未付款'),
		);
	}
}
