<?php

class ReportHtmlController extends Controller
{

    public $lang;
    public $type;
    public $id;
    public $childId;

    public function init()
    {
        $this->layout = '';
        $this->lang = Yii::app()->request->getParam('lang') == 'zh_cn' ? 'zh_cn' : 'en_us';
        $this->type = Yii::app()->request->getParam('type', 'es');
        $this->id = Yii::app()->request->getParam('rid', 0);
        $this->childId = Yii::app()->request->getParam('childId', 0);
        Yii::app()->language = $this->lang;
    }

    public function actionCover()
    {
        if (!$this->id) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', 'id not be null');
            $this->showMessage();
        }
        if (!$this->childId) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', 'child id not be null');
            $this->showMessage();
        }
        if (!in_array($this->type, array('es', 'ms'))) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', 'type not be null');
            $this->showMessage();
        }
        if ($this->type == 'es') {
            $data = $this->getEsData($this->id, $this->childId);
        } elseif ($this->type == 'ms') {
            $data = $this->getMsData($this->id, $this->childId);
        } else {
            $data = array();
        }
        $data['type'] = $this->type;
        $data['lang'] = $this->lang;
        $this->renderPartial('cover', array('data' => $data));
    }

    public function actionHeader()
    {
        if (!$this->id) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', 'id not be null');
            $this->showMessage();
        }
        if (!$this->childId) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', 'child id not be null');
            $this->showMessage();
        }
        if (!in_array($this->type, array('es', 'ms'))) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', 'type not be null');
            $this->showMessage();
        }
        if ($this->type == 'es') {
            $data = $this->getEsData($this->id, $this->childId);
        } elseif ($this->type == 'ms') {
            $data = $this->getMsData($this->id, $this->childId);
        } else {
            $data = array();
        }
        $data['type'] = $this->type;
        $data['lang'] = $this->lang;
        $this->renderPartial('header', array('data' => array(
            'childNameCn' => $data['childNameCn'],
            'childNameEn' => $data['childNameEn'],
            'classTitle'  => $data['classTitle'],
            'semester'    => $data['semester'],
            'startyear'   => $data['startyear'],
            'type'        => $this->type,
            'lang'        => $this->lang,
        )));
    }


    public function actionFooter()
    {
        $this->renderPartial('footer', array('data' => array(
            'type' => $this->type,
            'lang' => $this->lang,
        )));
    }

    function getEsData($id, $childId)
    {
        Yii::import('common.models.reportCards.*');
        $report = ReportsData::model()->findByPk($id);
        if ($report->child_id != $childId) {
            return array();
        }
        if (!$report) {
            return array();
        }
        $childModel = ChildProfileBasic::model()->findByPk($report->child_id);
        $classModel = IvyClass::model()->findByPk($report->class_id);
        $schoolDetailModel = BranchInfo::model()->findByPk($classModel->schoolid);
        $dateFormat = Yii::app()->language == 'en_us' ? 'F j, Y' : 'Y-m-d';
        $reportDate = $report->uptime ? date($dateFormat, $report->uptime) : '';

        return array(
            'startyear'     => $report->startyear,
            'semester'      => $report->semester,
            'childNameCn'   => $childModel->getReportChildNameByLang('zh_cn'),
            'childNameEn'   => $childModel->getReportChildNameByLang('en_us'),
            'classTitle'    => $classModel->title,
            'reportDate'    => $reportDate,
            'schoolName'    => CommonUtils::autoLang($schoolDetailModel->title_cn, $schoolDetailModel->title_en),
            'schoolAddress' => CommonUtils::autoLang($schoolDetailModel->address_cn, $schoolDetailModel->address_en),
            'schoolPhone'   => $schoolDetailModel->tel,
            'schoolEmail'   => $schoolDetailModel->email,
        );
    }

    function getMsData($id, $childId)
    {
        Yii::import('common.models.secondary.*');
        $reportChildModel = AchievementReportChild::model()->findByAttributes(array('report_id' => $id, 'childid' => $childId));
        if (!$reportChildModel) {
            return array();
        }
        $reportModel = AchievementReport::model()->findByPk($reportChildModel->report_id);
        $childModel = ChildProfileBasic::model()->findByPk($childId);
        $classModel = IvyClass::model()->findByPk($childModel->classid);
        $schoolDetailModel = BranchInfo::model()->findByPk($classModel->schoolid);

        $dateFormat = Yii::app()->language == 'en_us' ? 'F j, Y' : 'Y-m-d';
        $reportDate = $reportModel->end_time ? date($dateFormat, $reportModel->end_time) : '';

        return array(
            'startyear'     => $reportModel->startyear,
            'semester'      => $reportModel->cycle < 3 ? 1 : 2,
            'childNameCn'   => $childModel->getReportChildNameByLang('zh_cn'),
            'childNameEn'   => $childModel->getReportChildNameByLang('en_us'),
            'classTitle'    => $classModel->title,
            'reportDate'    => $reportDate,
            'schoolName'    => CommonUtils::autoLang($schoolDetailModel->title_cn, $schoolDetailModel->title_en),
            'schoolAddress' => CommonUtils::autoLang($schoolDetailModel->address_cn, $schoolDetailModel->address_en),
            'schoolPhone'   => $schoolDetailModel->tel,
            'schoolEmail'   => $schoolDetailModel->email,
        );
    }


    public function actionGetHtml()
    {
        $html = Yii::app()->request->getParam('html', '');
        echo base64_decode($html);
    }
}
