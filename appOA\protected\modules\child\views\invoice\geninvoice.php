<div class="h_a"><?php echo Yii::t('invoice', 'Create Invoice');?></div>
<?php $form=$this->beginWidget('CActiveForm', array(
	'id'=>'geninvoice-form',
    'htmlOptions'=>array('class'=>'J_ajaxForm form'),
	//'enableAjaxValidation'=>true,
	'clientOptions' => array(
		//'validateOnSubmit' => true,
		//'afterValidate' => 'js:invoicePreview'
	),
));

?>
<div class="table_full form">
    <table width=100%>
        <colgroup>
            <col class="th" width=180>
            <col width=null>
        </colgroup>
        <tbody>
        <?php foreach($cmodel->itemConfig['COMMON'] as $cFiled):?>
		<tr>
			<th><?php echo $form->labelEx($cmodel, $cFiled);?></th>
            <td>
                <ul class="switch_list" style="height:auto;">
                    <?php
						$cmodel->fData['title'] = array('zh_cn'=>'中文 Chinese', 'en_us'=>'英文 English');
						echo $cmodel->renderField($cFiled);
						//echo $form->error($cmodel, $cFiled);
					?>
                </ul>
            </td>
        </tr>
        <?php endforeach;?>
        <?php $i=0;foreach( $cmodel->itemModels as $fee => $model):?>
        <tr>
            <?php if ($i == 0):?>
            <th rowspan="<?php echo count($cmodel->itemModels)?>"><?php echo $form->labelEx($cmodel, 'availableFees');?></th>
            <?php endif;?>
            <td>
                <ul class="switch_list" style="height:auto;">
                    <?php foreach ($cmodel->itemConfig['INVOICE'][$fee] as $iFiled):?>
                    <li>
                        <?php
                        $model->fData['feetype'][$fee] = OA::t("payment", $paymentMappings['titleFeeType'][$fee]);
                        $model->fData['feeamount'][$fee] = isset($feeamounts[$fee])?$feeamounts[$fee]:array();
                        $model->fData['discount'][$fee] = $discounts;
                        $model->fData['_discount'][$fee] = !$bind_dis['discoundId']['active'] ? $bind_dis['discoundId']['id'] : 0;
                        echo $model->renderField($iFiled, $fee);
						//echo $form->error($model, $iFiled);
                        ?>
                    </li>
                    <?php endforeach;?>
                </ul>
            </td>
        </tr>
        <?php $i++;endforeach;?>
    </table>
</div>
<div class="mb10 tac" id="J_sub">
    <button class="btn btn_submit mr10" type="button" onclick="invoicePreview(this)"><?php echo Yii::t('invoice', 'Preview Invoice');?></button>
</div>
<?php $this->endWidget(); ?>

<script>
$('a.assitDate').bind('click', function() {
	var targetEle = '#' + $(this).attr("tId");
	var v = $(this).attr("a-date");
	$(targetEle).val(v);
});
</script>