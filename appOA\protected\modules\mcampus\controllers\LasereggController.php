<?php

class LasereggController extends BranchBasedController
{

	public $actionAccessAuths = array(
		'Update'=>array('access'=>'o_A_Access', 'admin'=>'ivystaff_opgeneral'),
		'Delete'=>'ivystaff_opgeneral',

		'LogIndex'=>'o_A_Access',
		'LogAdd'=>'o_A_Adm_Support',
		'LogUpdate'=>'o_A_Adm_Support',
		'LogDelete'=>'o_A_Adm_Support',
	);

	public $dialogWidth = 600;
	public $time_hour = array('00','01','02','03','04','05','06','07','08','09',10,11,12,13,14,15,16,17,18,19,20,21,22,23);
	public $time_minute = array('00'=>'00','10'=>'10','20'=>'20','30'=>'30','40'=>'40','50'=>'50');

	public function createUrl($route, $params = array(), $ampersand = '&')
	{
	    if (empty($params['branchId'])) {
	        $params['branchId'] = $this->branchId;
	    }
	    return parent::createUrl($route, $params, $ampersand);
	}
	
	public function init(){
	    parent::init();
	    Yii::app()->theme = 'blue';
	    $this->layout = "//layouts/column1";
		Yii::import('common.models.laseregg.LasereggInfo');
		Yii::import('common.models.laseregg.LasereggLog');

		$this->modernMenuFlag = 'campusOp';
		$this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
		$this->modernMenuCategoryTitle = Yii::t('site','Campus Workspace');

		//初始化选择校园页面
		$this->branchSelectParams['hideOffice'] = false;
		$this->branchSelectParams['urlArray'] = array('//mcampus/laseregg/logIndex');
	}

	public function actionUpdate($id=0)
	{
		$model=$this->loadInfoModel($id);

		if(isset($_POST['LasereggInfo']))
		{
			$model->attributes=$_POST['LasereggInfo'];
			$model->add_timestamp = time();
			$model->add_user = $this->staff->uid;
			if(!$model->save()){
				$err = current($model->getErrors());
				//ajax通知
	            $this->addMessage('state', 'fail');
	            $this->addMessage('message', Yii::t('message', $err[0]));
	            $this->showMessage();
			}
			$this->addMessage('state', 'success');
			$this->addMessage('message', 'Data saved');
			$this->addMessage('callback', 'cbLasereggInfo');
			$this->showMessage();

		}

		$this->renderPartial('update',array(
			'model'=>$model,
		));
	}

	public function actionDelete()
	{
		if (isset($_POST['id'])) {
			$id = Yii::app()->request->getPost('id', 0);
			$model = $this->loadInfoModel($id);
			if($model->delete()){
				$this->addMessage('state', 'success');
				$this->addMessage('callback', 'cbLasereggInfo');
				$this->addMessage('message', Yii::t('message','Data saved!'));
			}else{
				$this->addMessage('state', 'fail');
				$this->addMessage('message', '');
			}
			$this->showMessage();
		}
	}

	public function actionIndex()
	{
		$model=new LasereggInfo('search');
		$model->schoolid = $this->branchId;
		$this->render('index',array(
			'model'=>$model,
		));
	}

	public function showBtn($data)
	{

		echo '<button class="btn btn-info btn-xs" type="button" onclick="update('.$data->id.')" title="更新"><span class="glyphicon glyphicon-pencil"></span></button>  ';
		
		echo '<a href="'.$this->createUrl('delete', array('id'=>$data->id)).'" class="btn btn-danger btn-xs J_ajax_del" ><span class="glyphicon glyphicon-trash"></span></a>';
	}

// ********************************* LasereggLog ****************************************

	public function actionLogAdd()
	{
		$model = new LasereggLog();

		if(isset($_POST['LasereggLog']))
		{
			$lasereggNum = $_POST['lasereggNum'];
			$timestamp = time();
			foreach ($lasereggNum as $k => $v) {
				if (!$v) {
					continue;
				}

				$model = new LasereggLog();
				$model->attributes=$_POST['LasereggLog'];
				$log_timestamp = strtotime($model->time_hour.':'.$model->time_minute);
				$criteira = new CDbCriteria();
				$criteira->compare('laseregg_mac', $k);
				$criteira->compare('log_timestamp', $log_timestamp);
				$oldModel = LasereggLog::model()->find($criteira);
				if ($oldModel) {
					$model = $oldModel;
					$model->attributes=$_POST['LasereggLog'];
				}
				$model->add_type = LasereggLog::MAN;
				$model->log_timestamp = $log_timestamp;
				$model->add_timestamp = $timestamp;
				$model->add_user = $this->staff->uid;
				$model->laseregg_mac = $k;
				$model->laseregg_num = $v;
				if(!$model->save()){
					$err = current($model->getErrors());
					//ajax通知
		            $this->addMessage('state', 'fail');
		            $this->addMessage('message', Yii::t('message', $err[0]));
		            $this->showMessage();
				}
			}
			$this->addMessage('state', 'success');
			$this->addMessage('message', 'Data saved');
			$this->addMessage('callback', 'cbLasereggInfo');
			$this->showMessage();
		}

		$lasereggArr = $this->getLasereggArr();
		$this->renderPartial('log_add',array(
			'model'=>$model,
			'lasereggArr'=>$lasereggArr,
		));

	}

	public function actionLogUpdate($id=0)
	{
		$model=$this->loadLogModel($id);

		if(isset($_POST['LasereggLog']))
		{
			$model->attributes=$_POST['LasereggLog'];
			$model->add_type = LasereggLog::MAN;
			$time = strtotime($model->time_hour.':'.$model->time_minute);
			$model->log_timestamp = $time;
			$model->add_timestamp = time();
			$model->add_user = $this->staff->uid;

			if(!$model->save()){
				$err = current($model->getErrors());
				//ajax通知
	            $this->addMessage('state', 'fail');
	            $this->addMessage('message', Yii::t('message', $err[0]));
	            $this->showMessage();
			}
			$this->addMessage('state', 'success');
			$this->addMessage('message', 'Data saved');
			$this->addMessage('callback', 'cbLasereggInfo');
			$this->showMessage();
		}
		$lasereggArr = $this->getLasereggArr();
		$this->renderPartial('log_update',array(
			'model'=>$model,
			'lasereggArr'=>$lasereggArr,
		));
	}

	public function actionLogDelete()
	{
		if (isset($_POST['id'])) {
			$id = Yii::app()->request->getPost('id', 0);
			$model = $this->loadLogModel($id);
			if($model->delete()){
				$this->addMessage('state', 'success');
				$this->addMessage('callback', 'cbLasereggInfo');
				$this->addMessage('message', Yii::t('message','Data Saved!'));
			}else{
				$this->addMessage('state', 'fail');
				$this->addMessage('message', '');
			}
			$this->showMessage();
		}
	}
	
	public function actionLogIndex()
	{
		$lasereggMac = array_keys($this->getLasereggArr());
		$model = LasereggLog::model();
		if (count($lasereggMac)!=0) {
			$model->laseregg_mac = $lasereggMac;
		} else{
			$model->laseregg_mac = " ";
		}
		$this->render('log_index',array(
			'model'=>$model,
		));
	}

	public function showLogBtn($data)
	{

		if ($data->add_type == LasereggLog::AUTO) {
			return;
		}
		echo '<button class="btn btn-info btn-xs" type="button" onclick="update('.$data->id.')" title="更新"><span class="glyphicon glyphicon-pencil"></span></button>  ';
		
		echo '<a href="'.$this->createUrl('logDelete', array('id'=>$data->id)).'" class="btn btn-danger btn-xs J_ajax_del" ><span class="glyphicon glyphicon-trash"></span></a>';
	}

	public function loadInfoModel($id)
	{
		$model = LasereggInfo::model()->findByPk($id);
		if($model === null)
			$model = new LasereggInfo();
		return $model;
	}

	public function loadLogModel($id)
	{
		$model = LasereggLog::model()->findByPk($id);
		if($model === null)
			$model = new LasereggLog();
		return $model;
	}

	//获取所有学校列表
	public function getAllBranchId()
	{
		$criteira = new CDbCriteria();
		$criteira->compare('status', 10);
		$branchList = CHtml::listData(Branch::model()->findAll($criteira), 'branchid', 'title');
		return $branchList;
	}

	// 获取当前学校的镭豆
	public function getLasereggArr()
	{
		$criteria = new CDbCriteria;
		$criteria->compare('schoolid', $this->branchId);
		$laseregg = LasereggInfo::model()->findAll($criteria);
		$lasereggArr = array();
		foreach ($laseregg as $v) {
			$lasereggArr[$v->laseregg_mac] = $v->showName();
		}
		return $lasereggArr;
	}

	public function getSchoolTitle($data)
	{
		Yii::import('common.models.Branch');
		$title = Branch::model()->getBranchInfo($data->schoolid, 'title');
		return $title;
	}

}
