<?php

/**
 * This is the model class for table "ivy_alipay_order".
 *
 * The followings are the available columns in table 'ivy_alipay_order':
 * @property string $id
 * @property double $fact_amount
 * @property double $payable_amount
 * @property string $schoolid
 * @property integer $childid
 * @property integer $payment_method
 * @property string $bankid
 * @property integer $operator_id
 * @property integer $status
 * @property integer $settlement_status
 * @property integer $order_time
 * @property integer $update_timestamp
 */
class AlipayOrder extends CActiveRecord {

    /**
     * Returns the static model of the specified AR class.
     * @param string $className active record class name.
     * @return AlipayOrder the static model class
     */
    public static function model($className = __CLASS__) {
        return parent::model($className);
    }

    /**
     * @return string the associated database table name
     */
    public function tableName() {
        return 'ivy_alipay_order';
    }

    /**
     * @return array validation rules for model attributes.
     */
    public function rules() {
        // NOTE: you should only define rules for those attributes that
        // will receive user inputs.
        return array(
            array('id, schoolid, childid, payment_method, operator_id, order_time, update_timestamp', 'required'),
            array('childid, payment_method, operator_id, status, settlement_status, order_time, update_timestamp', 'numerical', 'integerOnly' => true),
            array('fact_amount, payable_amount', 'numerical'),
            array('id, schoolid, bankid', 'length', 'max' => 25),
            // The following rule is used by search().
            // Please remove those attributes that should not be searched.
            array('id, fact_amount, payable_amount, schoolid, childid, payment_method, bankid, operator_id, status, settlement_status, order_time, update_timestamp', 'safe', 'on' => 'search'),
        );
    }

    /**
     * @return array relational rules.
     */
    public function relations() {
        // NOTE: you may need to adjust the relation name and the related
        // class name for the relations automatically generated below.
        return array(
            'ChildProfile' => array(self::BELONGS_TO, 'ChildProfileBasic', 'childid'),
            'school' => array(self::BELONGS_TO, 'Branch', 'schoolid'),
            'items' => array(self::HAS_MANY, 'AlipayOrderDetail', 'order_id'),
        );
    }

    /**
     * 获取订单后缀
     * @param   $orderPrefix 订单前缀
     * @return boolean
     */
    public function getPostfix($orderPrefix) {
        $postfix = 0;
        $fix_cri = new CDbCriteria();
        $fix_cri->addCondition("`id` like '" . $orderPrefix . "%'");
        $fix_cri->order = 'id desc';
        $order = $this->find($fix_cri);
        if (!empty($order)) {
            $id = substr($order->id, 9, 3);
            $postfix = intval($id) + 1;
        } else {
            $postfix = 1;
        }
        return sprintf("%03s\n", $postfix);
    }

    /**
     * @return array customized attribute labels (name=>label)
     */
    public function attributeLabels() {
        return array(
            'id' => 'ID',
            'fact_amount' => 'Fact Amount',
            'payable_amount' => 'Payable Amount',
            'schoolid' => 'Schoolid',
            'childid' => 'Childid',
            'payment_method' => 'Payment Method',
            'bankid' => 'Bankid',
            'operator_id' => 'Operator',
            'status' => 'Status',
            'settlement_status' => 'Settlement Status',
            'order_time' => 'Order Time',
            'update_timestamp' => 'Update Timestamp',
        );
    }

    /**
     * Retrieves a list of models based on the current search/filter conditions.
     * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
     */
    public function search() {
        // Warning: Please modify the following code to remove attributes that
        // should not be searched.

        $criteria = new CDbCriteria;

        $criteria->compare('id', $this->id, true);
        $criteria->compare('fact_amount', $this->fact_amount);
        $criteria->compare('payable_amount', $this->payable_amount);
        $criteria->compare('schoolid', $this->schoolid, true);
        $criteria->compare('childid', $this->childid);
        $criteria->compare('payment_method', $this->payment_method);
        $criteria->compare('bankid', $this->bankid, true);
        $criteria->compare('operator_id', $this->operator_id);
        $criteria->compare('status', $this->status);
        $criteria->compare('settlement_status', $this->settlement_status);
        $criteria->compare('order_time', $this->order_time);
        $criteria->compare('update_timestamp', $this->update_timestamp);

        return new CActiveDataProvider($this, array(
                    'criteria' => $criteria,
                ));
    }

}