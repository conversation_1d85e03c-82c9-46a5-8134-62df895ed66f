<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site','Campus Operations'), array('/mcampus/default/index'))?></li>
        <li class="active"><?php echo Yii::t('site','奖学金列表') ?></li>
    </ol>

    <div class="row">
        <div class="col-md-2 col-sm-2 mb10">
            <?php
            $this->widget('zii.widgets.CMenu', array(
                'items' => $this->getMenu(),
                'id' => 'pageCategory',
                'htmlOptions' => array('class' => 'nav nav-pills nav-stacked background-gray'),
                'activeCssClass' => 'active',
                'itemCssClass' => ''
            ));
            ?>
        </div>
        <div class="col-md-10 col-sm-10">
            <div class="background-gray p8">
                <?php echo CHtml::link('<span class="glyphicon glyphicon-plus"></span> 增加', array('updateYear'), array('class'=>'J_modal btn btn-primary', 'title'=>'添加活动'));?>
            </div>
            <?php
            $this->widget('ext.ivyCGridView.BsCGridView', array(
                'id'=>'scholarship-list',
                'dataProvider'=>$dataProvider,
                //	'filter'=>$model,
                'template'=>"{items}{pager}{summary}",
                'colgroups'=>array(
                    array(
                        "colwidth"=>array(150,NULL),
                    //  "htmlOptions"=>array("align"=>"left", "span"=>2)
                    )
                ),
                'columns'=>array(
                    array(
                        'name'=> Yii::t('user','学年'),
                        'type'=>'raw',
                        'value'=>'$data->startyear',
                    ),
                    /*array(
                        'name'=> Yii::t('user','状态'),
                        'type'=>'raw',
                        'value'=>'$data->status == 1 ? "有效" : "无效" ',
                    ),*/
                    array(
                        'name' => Yii::t('global', 'Action'),
                        'type' => 'raw',
                        'value' => array($this, 'getButtons'),
                    ),
                ),
            )); ?>
        </div>
    </div>
<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>
<script>
    var modal = '<div class="modal fade" id="modal" class="1123" tabindex="-1" role="dialog" aria-labelledby="modal"><div class="modal-dialog" role="document"><div class="modal-content"></div></div></div>';
    $('body').append(modal);

    function cbSuccess() {
        $('#modal').modal('hide');
        $.fn.yiiGridView.update('scholarship-list', {
            complete: function () {
                head.Util.ajaxDel();
                head.Util.modal();
            }
        });
    }

</script>
