<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site','Campus Operations'), array('//mcampus/default/index'))?></li>
        <li><?php echo CHtml::link(Yii::t('site','Basic'), array('//mcampus/default/index'))?></li>
        <li class="active">招生管理</li>
    </ol>

    <div class="row">
        <!-- 左侧菜单 -->
        <div class="col-md-2 col-sm-2">
            <?php
            $this->widget('zii.widgets.CMenu',array(
                'items'=> $this->getMenu(),
                'id'=>'pageCategory',
                'htmlOptions'=>array('class'=>'nav nav-pills nav-stacked background-gray'),
                'activeCssClass'=>'active',
                'itemCssClass'=>''
            ));
            ?>
        </div>
        <div class="col-md-10 col-sm-10">
            <div class="col-md-2">
                <input id="datepicker" class="form-control" name="date"
                       value="<?php echo $_GET['date'] ? $_GET['date'] : date('Y-m-d', time()); ?>" readonly style="cursor: pointer">
            </div>
            <div class="col-md-12">
                <div id="charts" style="height:400px;"></div>
            </div>
        </div>
    </div>
</div>

<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>

<script>
    $('#datepicker').datepicker({
        dateFormat: 'yy-mm-dd',
        maxDate: '<?php echo date('Y-m-d', time()); ?>'
    });

    $('#datepicker').bind('change', function (e) {
        window.location.href = '<?php echo $this->createUrl('progression'); ?>&date=' + $(this).val();
    });

    var data = <?php echo json_encode($data); ?>;
    console.log(data);
    var myChart = echarts.init(document.getElementById('charts'));
    var option = {
        tooltip : {
            trigger: 'axis',
            axisPointer : {            // 坐标轴指示器，坐标轴触发有效
                type : 'shadow'        // 默认为直线，可选为：'line' | 'shadow'
            }
        },
        xAxis: {
            type: 'category',
            data: <?php echo json_encode($data['xAxis']); ?>
        },
        legend: {
            data:['Elementary','Kindergarten'],
        },
        yAxis: {
            type: 'value'
        },
        series: <?php echo json_encode($data['series']); ?>
    };
    myChart.setOption(option);

</script>