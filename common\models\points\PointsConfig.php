<?php

/**
 * This is the model class for table "ivy_points_config".
 *
 * The followings are the available columns in table 'ivy_points_config':
 * @property string $schoolid
 * @property integer $status
 * @property integer $maximum
 */
class PointsConfig extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @return PointsConfig the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_points_config';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('schoolid', 'required'),
			array('schoolid','unique'),
			array('status, maximum', 'numerical', 'integerOnly'=>true),
			array('schoolid', 'length', 'max'=>10),
            array('status, maximum', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('schoolid, status, maximum', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'schoolid' => Yii::t('points', '学校'),
			'status' => Yii::t('points', '开放'),
			'maximum' => Yii::t('points', '积分最高值'),
		);
	}

	public function showStatus()
	{
		return ($this->status) ? Yii::t('points', '是') : Yii::t('points', '否');
	}
	
	public function showSchool()
	{
		$branchList = Branch::model()->getBranchList();
		return $branchList[$this->schoolid];
	}
	
	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('status',$this->status);
		$criteria->compare('maximum',$this->maximum);

		return new CActiveDataProvider(get_class($this), array(
			'criteria'=>$criteria,
		));
	}
	
	/**
	 *  取学校的积分配置
	 * @param string $schoolid
	 * @return ArrayObject
	 * <AUTHOR>
	 * @copyright ivygroup
	 */
	public function getSchoolConfig($schoolid)
	{
		return $this->findByPk($schoolid);
	}
}