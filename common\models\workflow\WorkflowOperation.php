<?php

/**
 * This is the model class for table "ivy_workflow_operation".
 *
 * The followings are the available columns in table 'ivy_workflow_operation':
 * @property integer $id
 * @property integer $operation_type
 * @property integer $operation_object_id
 * @property integer $defination_id
 * @property string $branchid
 * @property integer $state
 * @property integer $current_node_index
 * @property integer $start_user
 * @property string $exte1
 * @property string $exte2
 * @property string $exte3
 * @property string $exte4
 * @property integer $start_time
 * @property integer $end_time
 * @property integer $notify_time
 */
class WorkflowOperation extends CActiveRecord
{
    const WORKFLOW_STATS_STEP = 0;            //工作流流程停留在中某点节
    const WORKFLOW_STATS_UNOP = 10;            //工作流流程未操作
    const WORKFLOW_STATS_OPDENY = 30;            //工作流流程被拒绝
    const WORKFLOW_STATS_OPED = 20;            //工作流流程已完成
    const WORKFLOW_STATS_RESET = 40;            //工作流流程已完成

     /**
	 * Returns the static model of the specified AR class.
	 * @return WorkflowOperation the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_workflow_operation';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
            array('defination_id', 'required'),
			array('operation_type, operation_object_id, defination_id, state, current_node_index, start_user, start_time, end_time, notify_time', 'numerical', 'integerOnly'=>true),
			array('branchid', 'length', 'max'=>10),
			array('exte1, exte2, exte3, exte4', 'length', 'max'=>255),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, operation_type, operation_object_id, defination_id, branchid, state, current_node_index, start_user, exte1, exte2, exte3, exte4, start_time, end_time', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'defination'=>array(self::BELONGS_TO,'WorkflowDefination','defination_id'),
            'detail'=>array(self::HAS_MANY,'WorkflowOperationDetail','operation_id'),
            'userInfo'=>array(self::BELONGS_TO,'User','start_user'),
            'payment'=>array(self::HAS_ONE,'PaymentApply', array('id' => 'operation_object_id')),
            'contract'=>array(self::HAS_ONE,'ContractApply', array('id' => 'operation_object_id')),
			'role' => array(self::HAS_MANY, 'WorkflowRole', '', 'on' => 't.id = role.operation_id', 'joinType'=>'RIGHT JOIN'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'operation_type' => 'Operation Type',
			'operation_object_id' => 'Operation Object',
			'defination_id' => 'Defination',
			'branchid' => 'Branchid',
			'state' => 'State',
			'current_node_index' => 'Current Node Index',
			'start_user' => 'Start User',
			'exte1' => 'Exte1',
			'exte2' => 'Exte2',
			'exte3' => 'Exte3',
			'exte4' => 'Exte4',
			'start_time' => 'Start Time',
			'end_time' => 'End Time',
			'notify_time' => 'Notify Time',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('operation_type',$this->operation_type);
		$criteria->compare('operation_object_id',$this->operation_object_id);
		$criteria->compare('defination_id',$this->defination_id);
		$criteria->compare('branchid',$this->branchid,true);
		$criteria->compare('state',$this->state);
		$criteria->compare('current_node_index',$this->current_node_index);
		$criteria->compare('start_user',$this->start_user);
		$criteria->compare('exte1',$this->exte1,true);
		$criteria->compare('exte2',$this->exte2,true);
		$criteria->compare('exte3',$this->exte3,true);
		$criteria->compare('exte4',$this->exte4,true);
		$criteria->compare('start_time',$this->start_time);
		$criteria->compare('end_time',$this->end_time);

		return new CActiveDataProvider(get_class($this), array(
			'criteria'=>$criteria,
		));
	}

    static public function getConfig(){
        return array(
            10 => "工作流流程进行中",
            20 => "工作流流程已完成",
            30 => "工作流流程被拒绝",
        );
    }

    static public function getWay(){
        return array(
            10 => '一般退费',
            20 => '退学退费',
            30 => '服务退费',
            40 => '特殊退费',
            50 => '毕业退费',
        );
	}
	
	/**
     * 获取当前节点的用户信息
     *
     * @return array
     */
    public static function getCurrentUser($operationid)
    {
        $userList = array();
        $criter = new CDbCriteria;
        $criter->compare('t.operation_id', $operationid);
        $criter->compare('t.current_operator', 1);
        
        $roleModel = WorkflowRole::model()->with('user')->findAll($criter);
        foreach ($roleModel as $role) {
			if (!$role->user) {
				continue;
			}
            $userList[$role->assign_user] = $role->user->getName();
        }
        return $userList;
    }

}