<?php
class HrController extends ProtectedController{
    
    public $catList=null; //操作菜单
    public $dialogWidth = 550;
    public function init(){
        parent::init();
    }
    public function actionIndex($t='leavelist'){
        $_GET['t']  = $t;
        switch ($t){
            case 'ot':
                $model = new OTItem();
                if (isset($_POST['OTItem'])) {
                    $model->attributes = $_POST['OTItem'];
                    $model->startdate = strtotime($_POST['OTItem']['startdate']);
                    $model->enddate = strtotime($_POST['OTItem']['enddate']);
                    $model->hours = UserLeave::setLeaveFormat($_POST['OTItem']['ot_num'],$_POST['OTItem']['ot_hour']);
                    $uid = Yii::app()->user->getId();
                    $model->staff_uid = $uid;
                    $model->branchid = $this->staff->profile->branch;
                    //查员工的部门（天元港的员工）或所在班级（学校员工）
                    $dep = DepPosLink::model()->findByPk($this->staff->profile->occupation_en);
                    $model->classid = $dep->department_id;
                    $model->status = LeaveItem::STATUS_WAITING;
                    $model->approver = StaffApprover::getApproverUser($model->staff_uid);
                    $model->creator = $uid;
                    $model->created = time();
                    if ($model->validate()) {
                        if ($model->save()){
                             //添加用户通知信息
                            Yii::import('application.components.user.Notification');
                            $notification = new Notification();
                            $data = array(
                                'staff_uid' => $model->approver,
                                'flagid' => $model->id,
                                'type' => 'hr_ot',
                            );
                            $notification->save($data);
                            $this->addMessage('state', 'success');
                            $this->addMessage('refresh', true);
                            $this->addMessage('message', Yii::t('message','success'));
                        }
                    }else{
                        $model->startdate = $_POST['OTItem']['startdate'];
                        $model->enddate = $_POST['OTItem']['enddate'];
                        $this->addMessage('state', 'fail');
                        $err = current($model->getErrors());
                        $this->addMessage('message', $err ? $err[0] : '失败');
                    }
                    $this->showMessage();
                }
                $this->layout='//layouts/dialog';
                $this->dialogWidth = 600;
                $this->render('hr_ot',array('t'=>$t,'model'=>$model));
                break;
            case 'leave':
                $uid = Yii::app()->user->getId();
                $type = Yii::app()->request->getParam('type',0);
                $model = new LeaveItem();
                $model->staff_uid = $uid;
                $model->type = $type;
                if (isset($_POST['LeaveItem'])) {
                    $model->attributes = $_POST['LeaveItem'];
                    $model->startdate = strtotime($_POST['LeaveItem']['startdate']);
                    $model->enddate = strtotime($_POST['LeaveItem']['enddate']);

                    if ($uid == $model->staff_uid) {
                        $staff = $this->staff;
                        $model->branchid = $staff->profile->branch;
                    } else {
                        $staff = User::model()->with('profile')->findByPk($model->staff_uid);
                        $model->branchid = $staff->profile->branch;
                    }
                    //查员工的部门（天元港的员工）或所在班级（学校员工）
                    $dep = DepPosLink::model()->findByPk($staff->profile->occupation_en);
                    $model->classid = $dep->department_id;
                    $model->hours = UserLeave::setLeaveFormat($_POST['LeaveItem']['leave_num'], $_POST['LeaveItem']['leave_hour']);
                    $model->applydate = time();
                    $model->status = LeaveItem::STATUS_WAITING;
                    $model->created = time();
                    $model->creator = $uid;
                    $model->approver = StaffApprover::getApproverUser($model->staff_uid);
                    if (in_array($model->type, array(UserLeave::TYPE_ANNUAL_LEAVE, UserLeave::TYPE_SICK_LEAVE, UserLeave::TYPE_NO_PAY_LEAVE, UserLeave::TYPE_DAYS_OFF))) {
                        $model->setScenario('save');
                    } else {
                        $model->setScenario('otherSave');
                    }
                    if ($model->validate()) {
                        if ($model->save()) {
                            //添加用户通知信息
                            Yii::import('application.components.user.Notification');
                            $notification = new Notification();
                            $data = array(
                                'staff_uid' => $model->approver,
                                'flagid' => $model->id,
                                'type' => 'hr_leave',
                            );
                            $notification->save($data);
                            $this->addMessage('state', 'success');
                            $this->addMessage('refresh', true);
                            $this->addMessage('message', Yii::t('message','success'));
                        }
                    } else {
                        $model->startdate = OA::formatDateTime($model->startdate);
                        $model->enddate = OA::formatDateTime($model->enddate);
                        if (in_array($model->type, array(UserLeave::TYPE_ANNUAL_LEAVE, UserLeave::TYPE_SICK_LEAVE, UserLeave::TYPE_DAYS_OFF))) {
                            //查询员工假期分录信息
                            $leaveList = UserLeave::model()->getCountUserLeave($model->staff_uid, $model->type);
                        } else {
                            $leaveList = array('valid' => true);
                        }
                        $this->addMessage('state', 'fail');
                        $err = current($model->getErrors());
                        $this->addMessage('message', $err ? $err[0] : '失败');
                    }
                    $this->showMessage();
                }
                if (in_array($model->type, array(UserLeave::TYPE_ANNUAL_LEAVE, UserLeave::TYPE_SICK_LEAVE, UserLeave::TYPE_DAYS_OFF))) {
                    //查询员工假期分录信息
                    $leaveList = UserLeave::model()->getCountUserLeave($model->staff_uid, $model->type);
                } else {
                    $leaveList = array('valid' => true);
                }
                $this->layout='//layouts/dialog';
                $this->render('hr_leave',array('t'=>$t,'model'=>$model,'data'=>array('leaveList'=>$leaveList)));
                break;
            case 'leavelist':
                $uId = Yii::app()->user->getId();
                
                //查询当前用户的所有假期信息
                $leaveList = UserLeave::getUserLeaveAllData($uId);
                //使用过的假期
                $useLeaveItem = LeaveItem::getAllLeaveItem($uId);

//                Yii::app()->clientScript->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/underscore-min.js');
//                Yii::app()->clientScript->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/backbone-min.js');
                $this->render('index',array('t'=>$t,'data'=>array(
                    'leaveList'=>$leaveList,
                    'useLeaveItem'=>$useLeaveItem,
                    )));
                break;
            case 'otlist':
                $uId = Yii::app()->user->getId();
                $useOTItem = OTItem::getAllOTItem($uId);
                //等待审核的加班信息
                $crit = new CDbCriteria();
                $crit->compare('t.staff_uid',$uId);
                $crit->compare('t.approvedate', 0);
                $crit->order = 't.id DESC,t.startdate DESC';
                $dataProvider = new CActiveDataProvider('OTItem', array(
                            'criteria' => $crit,
                            'pagination' => array(
                                'pageSize' => 20,
                            ),
                        ));
                //已审核的加班信息
                $crit = new CDbCriteria();
                $crit->compare('t.staff_uid',$uId);
                $crit->compare('t.status', LeaveItem::STATUS_AGREE);
                $crit->addCondition('t.approvedate>0');
                $crit->order = 't.id DESC,t.startdate DESC';
                $dataProvidered = new CActiveDataProvider('OTItem', array(
                            'criteria' => $crit,
                            'pagination' => array(
                                'pageSize' => 20,
                            ),
                        ));
                $this->render('index',array('t'=>$t,'data'=>array(
                    'dataProvider'=>$dataProvider,
                    'dataProvidered'=>$dataProvidered,
                    'useOTItem'=>$useOTItem,
                    )));
                break;
            default :
                $this->render('index',array('t'=>$t));
                break;
        }
    }
    
    public function actionDelete(){
        $id = Yii::app()->request->getParam('id',0);
        $t = Yii::app()->request->getParam('t',null);
        $uId = Yii::app()->user->getId();
        if (Yii::app()->request->isPostRequest) {
            if (!empty($t) && $id){
                $t = strtolower($t);
                Yii::import('application.components.user.Notification');
                $notification = new Notification();
                if ($t == 'leave') {
                    $leaveModel = LeaveItem::model()->findByPk($id);
                    if ($leaveModel->status == LeaveItem::STATUS_WAITING && $leaveModel->approvedate == 0) {
                        if (in_array($uId, array($leaveModel->staff_uid, $leaveModel->creator))) {
                            if ($leaveModel->delete()) {
                                //删除成功,更新当前剩余假期总数
                                if (in_array($leaveModel->type, array(UserLeave::TYPE_ANNUAL_LEAVE, UserLeave::TYPE_SICK_LEAVE))) {
                                    UserLeave::updateUserLeaveBalance($leaveModel->staff_uid, $leaveModel->leave_id);
                                }
                                //删除用户通知
                                $notification->deleteData($leaveModel->approver, $leaveModel->id,'hr_leave');
                            }
                        }
                    }
                    unset($leaveModel);
                } elseif ($t == 'ot') {
                    $otModel = OTItem::model()->findByPk($id);
                    if ($uId == $otModel->staff_uid && $otModel->status == LeaveItem::STATUS_WAITING) {
                        if ($otModel->delete()) {
                            //删除用户通知
                            $notification->deleteData($otModel->approver, $otModel->id,'hr_ot');
                        }
                    }
                }
            }
        }else{
             throw new CHttpException(400, 'Invalid request. Please do not repeat this request again.');
        }
    }
    
    public function actionCount(){
        Yii::import('application.components.user.Notification');
        $notification = new Notification();
        $data = $notification->getData(Yii::app()->user->getId());
        $this->render('count',array('data'=>$data));
    }
    
    public function actionLeavecheck(){
        //等待审核的请假信息
        $crit = new CDbCriteria();
        $crit->compare('t.approver', Yii::app()->user->getId());
        $crit->compare('t.approvedate', 0);
        $crit->compare('t.status', LeaveItem::STATUS_WAITING);
        $crit->order = 't.id DESC,t.startdate DESC';
        $crit->with = 'appUser';
        $dataProvider = new CActiveDataProvider('LeaveItem', array(
                    'criteria' => $crit,
                    'pagination' => array(
                        'pageSize' => 20,
                    ),
                ));
        $this->render('leavecheck',array('dataProvider'=>$dataProvider));
    }
    
    public function actionLeavechecking(){
        //审核请假
        $id = Yii::app()->request->getParam('id', 0);
        $agree = Yii::app()->request->getParam('agree', 'no');
        if (Yii::app()->request->isPostRequest) {
            if ($id) {
                $model = LeaveItem::model()->findByPk($id);
                $uid = Yii::app()->user->getId();
                if ($model->approver == $uid) {
                    $model->approvedate = time();
                    $model->status = (strtolower($agree) == 'ok') ? LeaveItem::STATUS_AGREE : LeaveItem::STATUS_REJECT;
                    //处理请假材料
                    $_type = array(UserLeave::TYPE_MARRIAGE_LEAVE, UserLeave::TYPE_MATERNITY_LEAVE, UserLeave::TYPE_BEREAVEMENT_LEAVE);
                    if (($model->status == LeaveItem::STATUS_AGREE)) {
                        if ((in_array($model->type, $_type) || (($model->type == UserLeave::TYPE_SICK_LEAVE) && ($model->hours >= UserLeave::DAY * UserLeave::FLAG)))) {
                            $model->doc_flag = LeaveItem::DOC_FLAG_HAVE;
                        }
                        //更新假期余额
                        if (in_array($model->type, array(UserLeave::TYPE_ANNUAL_LEAVE, UserLeave::TYPE_SICK_LEAVE, UserLeave::TYPE_DAYS_OFF))) {
                            //计算剩余假期balance
                            $leaveModel = UserLeave::model()->findByPk($model->leave_id);
                            $leaveModel->balance = $leaveModel->balance-$model->hours;
                            $leaveModel->update();
                        }
                    }
                    if ($model->update()) {
                        //更新通知
                        Yii::import('application.components.user.Notification');
                        $notification = new Notification();
                        $notification->update($uid,$id,'hr_leave');
                        return TRUE;
                    }
                }
            }
        }
    }
    
    public function actionOtcheck(){
        //审核加班
        $crit = new CDbCriteria();
        $crit->compare('t.approver', Yii::app()->user->getId());
        $crit->compare('t.approvedate', 0);
        $crit->compare('t.status', LeaveItem::STATUS_WAITING);
        $crit->order = 't.id DESC,t.startdate DESC';
        $crit->with = 'appUser';
        $dataProvider = new CActiveDataProvider('OTItem', array(
                    'criteria' => $crit,
                    'pagination' => array(
                        'pageSize' => 20,
                    ),
                ));
        $this->render('otcheck',array('dataProvider'=>$dataProvider));
    }
    
    public function actionOtchecking(){
        //审核请假
        $id = Yii::app()->request->getParam('id', 0);
        $agree = Yii::app()->request->getParam('agree', 'no');
        $uid = Yii::app()->user->getId();
        if (Yii::app()->request->isPostRequest) {
            if ($id) {
                $model = OTItem::model()->findByPk($id);
                if ($model->approver == $uid) {
                    $model->approvedate = time();
                    $model->status = (strtolower($agree) == 'ok') ? LeaveItem::STATUS_AGREE : LeaveItem::STATUS_REJECT;
                    if ($model->update()) {
                        //真对调休的情况
                        if (($model->status ==LeaveItem::STATUS_AGREE) &&($model->type == OTItem::OT_CONVER_HOLIDAY)){
                            $startYear = date('m',$model->enddate) < 9 ? date('Y',$model->enddate) : date('Y',$model->enddate)+1;
                            $leaveModel = new UserLeave();
                            $leaveModel->type = UserLeave::TYPE_DAYS_OFF;
                            $leaveModel->staff_uid = $model->staff_uid;
                            $leaveModel->hours = $model->hours;
                            $leaveModel->startdate = $model->enddate;
                            $leaveModel->enddate = strtotime(sprintf(OTItem::OT_DURATION,$startYear));
                            $leaveModel->startyear = $startYear-1;
                            $leaveModel->balance = $model->hours;
                            $leaveModel->flag = $model->id;
                            $leaveModel->created = time();
                            $leaveModel->creator = $uid;
                            $leaveModel->save();
                        }
                        //更新通知
                        Yii::import('application.components.user.Notification');
                        $notification = new Notification();
                        $notification->update($uid,$id,'hr_ot');
                        return TRUE;
                    }
                }
            }
        }
    }
    
    public function actionDoc() {
        //人事材料确认
        $branchId = Yii::app()->request->getParam('branchid',null);
        $crit = new CDbCriteria();
        $crit->compare('t.doc_flag', 1);
        $crit->compare('t.doc_status', 0);
        if (!empty($branchId)){
            $crit->compare('t.branchid', $branchId);
        }
        $crit->order = 't.type ASC,t.startdate DESC';
        $crit->with = array('userLeave');
        $dataProvider = new CActiveDataProvider('LeaveItem', array(
                    'criteria' => $crit,
                    'pagination' => array(
                        'pageSize' => 20,
                    ),
                ));
        $this->render('doc',array('dataProvider'=>$dataProvider));
    }
    
    public function actionDocchecking(){
        //人事材料确认
        $id = Yii::app()->request->getParam('id', 0);
        $agree = Yii::app()->request->getParam('agree', 'no');
        if (Yii::app()->request->isPostRequest) {
            if ($id) {
                $model = LeaveItem::model()->findByPk($id);
                if (($model->doc_status == LeaveItem::DOC_STATUS_NOTHING) && ($model->doc_flag == LeaveItem::DOC_FLAG_HAVE)){
                    $model->doc_status = LeaveItem::DOC_STATUS_HAVE;
                    $model->doc_uid = Yii::app()->user->getId();
                    $model->doc_created = time();
                    if ($model->update()){
                        return TRUE;
                    }
                }
            }
        }
    }
    
    public function actionLeaveView(){
        $this->dialogWidth = 850;
        $type = Yii::app()->request->getParam('type',0);
        $status = Yii::app()->request->getParam('status');
        $uid = Yii::app()->user->getId();
        $crit = new CDbCriteria();
        $crit->compare('t.staff_uid', $uid);
        $crit->compare('t.status', $status);
        $crit->compare('t.type', $type);
        $crit->order = 't.startdate DESC';
        $crit->with = array('userLeave');
        $dataProvider = new CActiveDataProvider('LeaveItem', array(
            'criteria' => $crit,
        ));
        $this->layout='//layouts/dialog';
        $this->render('leaveView',array('dataProvider'=>$dataProvider,'status'=>$status));
    }
}

?>
