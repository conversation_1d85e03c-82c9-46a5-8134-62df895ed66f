<?php
$category=Yii::app()->request->getParam('category','current');
$type=Yii::app()->request->getParam('t','all');

$branches = $this->getAllBranch();

?>
<script>
    var childBaseUrl = '<?php echo $this->createUrl('//child/index/index');?>';
</script>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site','Campus Operations'), array('//mcampus/default/index'))?></li>
        <li><?php echo CHtml::link(Yii::t('site','Basic'), array('//mcampus/default/index'))?></li>
        <li class="active"><?php echo Yii::t('site','Student Management');?></li>
    </ol>

    <div class="row">
        <div class="col-md-12 mb10" id="student-top-bar">
            <div class="form-inline">
                <div class="btn-group form-group">
                    <button type="button" class="btn btn-primary dropdown-toggle" data-toggle="dropdown">
                        <?php echo Yii::t('site','Student Registration');?> <span class="caret"></span></button>
                    <ul class="dropdown-menu" role="menu">
                        <li><a href="#" onclick="regristerNew();"><?php echo Yii::t('campus','New Registration');?></a></li>
                        <li class="divider"></li>
                        <li><a href="#" onclick="regristerSibing();"><?php echo Yii::t('campus','Sibling Registration');?></a></li>
                    </ul>
                </div>

                <?php $this->widget('ext.search.ChildSearchHandy', array(
                    'inputCSS' => 'form-control form-group',
                    'displayLabel'=>'',
                    'popPosition' => array('collision'=>"none"),
                    'restrictSchool' => $this->branchId,
                    'allowMultipleSchool' => $this->staff->profile->branch == 'BJ_TYG',
                    'abb' => $this->branchObj->abb,
                )) ?>

            </div>
        </div>

        <div class="col-md-2">

            <?php
//            $startMonth = ($this->branchObj->type == 50) ? 1 : 3;
            $startMonth = 1;
            $mainMenu = array(
                array('label'=>Yii::t('site','Current Students'), 'url'=>array("//mcampus/student/index","category"=>"current")),
                array('label'=>Yii::t('site','Students of next Year'), 'url'=>array("//mcampus/student/index","category"=>"next"),'visible'=>OA::isShowNextYear_t()),
                array('label'=>Yii::t('site','Newly Registered'), 'url'=>array("//mcampus/student/index","category"=>"newreg")),
                array('label'=>Yii::t('site','Group transfer to next school year'), 'url'=>array("//mcampus/student/index","category"=>'switch'),'visible'=>OA::isShowNextYear_t()),
                array('label'=>Yii::t('site','Discount of this Year'), 'url'=>array("//mcampus/student/index","category"=>"currentdst")),
                array('label'=>Yii::t('site','Discount and tuition rate for next year'), 'url'=>array("//mcampus/student/index","category"=>"nextdst"),'visible'=>OA::isShowNextYear_t()),
                array('label'=>Yii::t('site','Inactive Students'), 'url'=>array("//mcampus/student/index","category"=>"other")),
                array('label'=>Yii::t('site','Students withdraw in the middle of the school year'), 'url'=>array("//mcampus/student/index","category"=>"quit")),
                array('label'=>Yii::t('site','Mark students'), 'url'=>array("//mcampus/student/index","category"=>"markUp"),'visible'=>OA::isShowNextYear_t()),
                array('label'=>Yii::t('site','Dropping Out'), 'url'=>array("//mcampus/student/index","category"=>"droppingOut")),
                );
            if ($this->isTeacher()) {
                $mainMenu = array(
                    array('label'=>Yii::t('site','Current Students'), 'url'=>array("//mcampus/student/index","category"=>"current")),
                    array('label'=>Yii::t('site','Students of next Year'), 'url'=>array("//mcampus/student/index","category"=>"next"),'visible'=>OA::isShowNextYear_t()),
                );
            }

            $this->widget('zii.widgets.CMenu',array(
                'items'=> $mainMenu,
                'htmlOptions'=>array('class'=>'nav nav-pills nav-stacked text-right background-gray'),
                'activeCssClass'=>'active',
                'itemCssClass'=>''
            ));
            ?>
        </div>
        <?php
        switch($category){
            case 'current':
            case 'next':
                $this->renderPartial("student/current",array('category'=>$category));
                break;
            case 'currentdst':
            case 'nextdst':
                $this->renderPartial("student/currentdst",array('category'=>$category));
                break;
            default:
                if ($category == 'switch' && in_array($this->branchId, array('BJ_DS', 'BJ_SLT', 'BJ_QFF'))) {
                    $this->renderPartial("student/switch_ds",array('category'=>'switch_ds'));
                } else {
                    $this->renderPartial("student/{$category}",array('category'=>$category));
                }
                break;
        }

        ?>
    </div>
</div>

<?php

$this->branchSelectParams['extraUrlArray'] = array('//mcampus/student/index','category'=>$category);

$this->renderPartial('//layouts/common/branchSelectBottom');
$this->renderPartial('student/regchild',array('branches'=>$branches));
?>
<style>
    .glyphiconList{
        font-size:20px;
        padding:5px 6px 6px;

    }
    .glyphiconList:hover{
        background:#f2f3f5;
        border-radius:4px;
        color:#4D88D2
    }
    .open .glyphiconList{
        background:#f2f3f5;
        border-radius:4px;
        color:#4D88D2
    }
</style>
<script>
    var regristerNew, regristerSibing;
    var myBranchId = '<?php echo $this->branchId;?>';
    var postSearchUrl = "<?php echo $this->createUrl('sibingAllSearch');?>";
    var getSibingsUrl = "<?php echo $this->createUrl('getSiblings');?>";
    var childPhotoBaseUrl = "<?php echo Yii::app()->params['OAUploadBaseUrl'].'/childmgt/';?>";
    var SSCampusChange;
    var selectedBranchId = myBranchId;
    var iniSearchForm;
    var showSearch; //显示查询结果
    var chooseChild;

    $(function(){
        regristerNew = function(){
            head.js([
                    "<?php echo Yii::app()->clientScript->getCoreScriptUrl().'/jui/css/base/jquery-ui.css';?>",
                    "<?php echo Yii::app()->clientScript->getCoreScriptUrl().'/jui/js/jquery-ui-i18n.min.js';?>"],
                function(){
                    $('#register-new').remove();
                    $('#student-top-bar').after($('<div id="register-new" class="col-md-12 mb10" style="display: none;"></div>'));
                    $('#register-new').html($('#regform-new-template').html());
                    $('#ChildProfileBasic_est_enter_date').datepicker({dateFormat:'yy-mm-dd',changeMonth: true,changeYear: true, firstDay: 0});
                    $('#ChildProfileBasic_birthday_search').datepicker({dateFormat:'yy-mm-dd',changeMonth: true,changeYear: true, maxDate: -30, minDate:'-20y', firstDay: 0});
                    $('#ChildProfileBasic_birthday_search').datepicker("setDate", "-2y");
                    $('#register-new').show('blind',{},500, function(){
                        head.Util.ajaxForm($('#register-new'));

                        $('#fatherEmail, #motherEmail, #fatherMobile, #motherMobile').change(function(){
                            $(this).parent().find('div.alert').remove();
                        });
                    });
                })
        }

        regristerSibing = function(){
            head.js([
                    "<?php echo Yii::app()->theme->baseUrl.'/js/student/sibling.js?t='.time();?>",
                    "<?php echo Yii::app()->clientScript->getCoreScriptUrl().'/jui/css/base/jquery-ui.css';?>",
                    "<?php echo Yii::app()->clientScript->getCoreScriptUrl().'/jui/js/jquery-ui-i18n.min.js';?>"],
                function(){
                    $('#register-new').remove();
                    $('#student-top-bar').after($('<div id="register-new" class="col-md-12 mb10" style="display: none;"></div>'));
                    $('#register-new').html($('#regform-sibling-template').html());
                    $('#ChildProfileBasic_est_enter_date').datepicker(
                        {dateFormat:'yy-mm-dd',changeMonth: true,changeYear: true, firstDay: 0});
                    $('#ChildProfileBasic_birthday_search').datepicker(
                        {dateFormat:'yy-mm-dd',changeMonth: true,changeYear: true, maxDate: -30, minDate:'-20y', firstDay: 0});
                    $('#ChildProfileBasic_birthday_search').datepicker("setDate", "-2y");
                    $('#register-new').show('blind',{},500, function(){
                        head.Util.ajaxForm($('#register-new'));
                    });
                }
            )
        }
    });

    // 邮件电话存在 回调
    function cbFail(data)
    {
        var html = '<div class="alert alert-danger" role="alert">' + data.info + '</div>';
        var obj = $('#'+data.key).parent();
        obj.find('div.alert').remove();
        obj.append(html);
    }

    // 成功！！！ 回调
    function cbSuccess(data)
    {
        setTimeout(function(){
            $('#fill-form').html( _.template($('#success-template').html(), data) );
            $("html,body").animate({scrollTop:0},200);
        }, 1000);
    }
</script>

<style>
    .register{background: #dedede;padding-top: 10px}
    a.ui-state-default{color:#333}
    .font24{
        font-size:24px
    }
</style>

