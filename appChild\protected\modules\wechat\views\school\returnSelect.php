<style>
	.mt0{
		margin-top:0
	}
	.bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        height: 100%;
        background-image: url('http://img.admissions.daystaracademy.cn/img/ea/home-110.jpg');	
        background-size: cover;
        filter: blur(5px);
    }
    .bg-mask{
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.2);
    }
	.box{
		width:100%
	}
	.cell{
		position: relative;
		display: flex;
		flex-direction: column;
		width: 80%;
		height:70%;
		margin: 0 10%;
	}
	.weui_cells_title{
		color:#fff
	}
	.circle{
		border: 5px solid #fff;
		width:100px;
		box-shadow: 0px 3px 5px #dfd6d6;
		height: 100px;
		border-radius:50%;
		-webkit-border-radius:50%;
    	overflow: hidden;
		background-color: transparent;
	}
	dl{
		text-align:center
	}
	dd{
		text-align:center
	}
	dd span{
		text-align:center;
		color:#000;
    	border-radius: 15px;
		margin-top:10px;
		background: #fff;
		display:inline-block;
		padding: 1px 10px;
	}
	.btn{
		margin-top:100px;
		width:80%;
		border-radius: 20px;
	}
	.weui_cells_radio .weui_check:checked+.weui_icon_checked:before {
        background: #09BB07;
        color: #fff;
        margin: 0;
    }
	.title,.weui_cells_radio {
        margin-top: 0;
		position: relative;
		flex:1;
		display: flex;
        justify-content: center;
        align-items: center;
    }
	.weui_cells_radio:first-child{ 
		border-bottom:1px solid #ccc;
	}
	.order {
	    height: 36px;
		line-height: 36px;
		text-align: center;
		position: relative;
		/* width: 80%;
		margin: 0 10%; */
		margin-top: 50px;
	}
	.order .line {
		display:inline-block;
		width:30px;
		border-top:1px solid #fff;
		vertical-align:5px;  
	}
	.order .txt {
		color: #fff;
	}
	dl dt{
		border-radius: 50%;
		width: 110px;
		height: 110px;
		overflow:hidden;
		margin-bottom:10px;
		background-color:transparent;
		-webkit-border-radius:50%;

	}
</style>
<div class='container'>
	<div class="bg"></div>
	<div class="bg-mask"></div>
	<div class="order">
			<span class="line"></span>
			<span class="txt"><?php echo Yii::t("reg", "Please click the photo or name to start"); ?></span>
			<span class="line"></span>
		</div>
		<div class="cell">
			<?php 
			foreach ($returnChildModels as $childid => $model) :
				$childObj = $childObjs[$childid];
				$returnid = $model->id;
				$$avatar = '';
				if($childObj->photo){
					$avatar = CommonUtils::childPhotoUrl($childObj->photo);
				}
				$url = $this->createUrl('reEnrollment', array('returnid' => $returnid));

			?>
			<div class="weui_cells_radio">
			<!-- <img class="circle" src="<?php echo $avatar; ?>">    -->
				<label class="weui_check_label" onclick="hrefDAta('<?php echo $childid ?>', '<?php echo $url; ?>')">
					<dl>
						<dt>
							<img class="circle" src="<?php echo $avatar; ?>">    
						</dt>
						<dd><span><?php echo $childObj->getChildName(); ?></span></dd>
					</dl>
				</label>
				</div>
			<?php endforeach; ?>
		</div>
		<div style='width: 80%;color:#fff;position: relative;margin-left: 10%;font-size: 14px;text-align:center'><?php echo Yii::t("reg", "Siblings need to fill the form respectively."); ?></div>
 
<script>
var urlData=''
function hrefDAta(childid, url){
	//设置孩子cookie
	var date = new Date();
	date.setTime(date.getTime()+(30*24*3600000));
	var expires = "; expires="+date.toGMTString();
	document.cookie = "childid="+childid+expires+"; path=/";
	location.href=url
}
</script>
