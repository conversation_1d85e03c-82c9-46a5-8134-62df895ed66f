<style>
    .arrow {
        top: -11px !important;
    }
    .table tr td,
    .table tr th {
        vertical-align: middle !important;
    }
    .wid {
        width: 100% !important;
    }
    .table>tbody+tbody {
        border-top: none;
    }
    [v-cloak] {
        display: none;
    }
    .popover {
        max-width: 340px;
    }
    .listdata {
        overflow: auto;
    }
    
.colorpicker {
    z-index: 9999;
}

    .table {
        width: 100% !important;
    }
    .search_checkbox {
        margin: 0;
        padding: 0;
        margin-left: 15px;
        display: inline-block;
        height: 30px;
    }
    .search_checkbox input[type=checkbox] {
        height: 0px;
        width: 0px;
        visibility: hidden;
    }
    .search_checkbox label {
        cursor: pointer;
        height: 20px;
        border-radius: 10px;
        display: inline-block;
        background-color: gray;
        width: 40px;
        text-indent: -99999px;
        position: relative;
        float: left;
        margin-top: 5px;
    }
    .search_checkbox label::before {
        content: '';
        display: inline-block;
        background-color: white;
        height: 15px;
        width: 15px;
        position: absolute;
        top: 2.5px;
        left: 2.5px;
        border-radius: 7px;
        transition: 0.3s;
    }
    .search_checkbox input:checked+label {
        background: #428BCA;
    }
    .search_checkbox input:checked+label:before {
        left: calc(100% - 2.5px);
        transform: translateX(-100%);
    }
    .search_checkbox label:active:before {
        width: 20px;
    }
    .checkspan {
        margin-top: 7px;
        display: inline-block;
    }
    .selectcolor{
        display: none;
    }
</style>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li>
            <?php echo Yii::t('user','Report Card Analytic Reports');?>
        </li>
        <li>
            <?php
            $typeData = Yii::app()->request->getParam('type', 'roster1');
            echo ($typeData == 'roster1') ? Yii::t('user','Roster by Reporting Period') : Yii::t('user','Roster by Criterion'); ?>
        </li>
    </ol>
    <div class="row">
        <div class="col-md-2">
            <?php
            $this->widget('zii.widgets.CMenu', array(
                'items' => $this->module->getMenu(),
                'id' => 'pageCategory',
                'htmlOptions' => array('class' => 'nav nav-pills nav-stacked background-gray'),
                'activeCssClass' => 'active',
            ));
            ?>
        </div>
        <div id="datalist" v-cloak class="col-md-10">
            <div class="row">
                <div class="col-md-2">
                    <div class="btn-group mb15 wid">
                        <button type="button" class="btn btn-default dropdown-toggle  wid" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">{{calenderList[yid]}} <span class="caret"></span></button>
                        <ul class="dropdown-menu wid">
                            <li v-for="(val, key, index) in calenderList">
                                <a :href="'<?php echo $this->createUrl('index', array('branchId' => $branchId, 'type' => $type)); ?>&yid='+key+''">{{val}}</a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="listheight col-md-2">
                    <span class="list-group-item" v-for='(list,index) in data' @click="addClassFun(index,list.title)" :class='{active:index==qwerqwre}'>{{list.title}}</span>
                </div>
                <div class="col-md-10">
                    <div class="vuelist" v-if='datalist'>
                        <div class="panel panel-default" :style="height">
                            <div class="panel-heading">{{checkcoursename}}</div>

                            <ul class="list-group listdata" :style="insideheight">
                                <template v-for='(list,id,index) in datalist.items'>
                                    <li class="list-group-item ">
                                        <p>{{id}}</p>
                                        <div class="col-xs-12 col-sm-12 col-md-12 col-lg-6" v-for='(data,indexs) in list'>
                                            <div class="checkbox">
                                                <label>
                                                      <input type="checkbox" :id="data.course_code" :value="data.course_code" name='item'>{{data.course_title}}
                                                </label>
                                            </div>
                                        </div>
                                        <div class="clearfix"></div>
                                    </li>
                                </template>
                            </ul>
                        </div>
                        <div>
                            <p class="pull-right">
                                <button type="button" class="btn btn-primary yes" data-dismiss="modal" onclick='add()'><?php echo Yii::t('global','OK') ?></button>
                            </p>
                        </div>
                    </div>
                </div>
               
                
            </div>
            <div class="row">
             <p class="col-md-12 mt15 selectcolor"><button type="button" class="btn btn-primary"  data-target=".bs-example-modal-lg" onclick='slectColor()'>选择颜色</button></p>
                <div id="tel" class="col-md-12">
                    
                </div>
            </div>
        </div>
    </div>
</div>
<script type="text/template" id="type1-template">

    <div class="mt15">
        <a class="btn btn-primary pull-left mr15" download='table<%=code%>.xls' href="#" onclick="return ExcellentExport.excel(this, 'table<%=code%>', 'Sheet Name Here');"><?php echo Yii::t('user','Excel Export'); ?></a>
        <p class='search_checkbox checkbox pull-left'>
            <span class="mr15 checkspan pull-left"><?php echo Yii::t('user','Coloring switch'); ?></span>
            <input type='checkbox' id='che<%=code%>' name='colorname' value='<%=code%>' checked="checked" onclick="color(this,'<%=code%>')">
            <label for='che<%=code%>'></label>
        </p>
    </div>
    <table class="table  table-bordered tabledata" id='table<%=code%>'>
        <thead>
            <tr>
                <th colspan="10">
                    <?php echo Yii::t('user','Course Name:') ?>
                    <%=title%>
                </th>
                <th colspan="4">
                    <?php echo Yii::t('user','Course Code:') ?>
                    <%=code%>
                </th>
                <th colspan="4">
                    <?php echo Yii::t('user','Grade:') ?>
                    <%=grade%>
                </th>
                <th></th>
            </tr>
            <tr>
                <th colspan="20">
                    <% for(key in classes){ %>
                    <label class="checkbox-inline ml15">
                         <input type="checkbox" id="<%=key%>" checked name='<%=code%>' value='<%=key%>' onclick="check(this,'<%=key%>','<%=code%>')">
                       <%=key%>
                       <% for(var j=0;j < alias.length;j++){ %> 
                        
                            <%if(key==alias[j].course_code){ %> 
                                 (<%=alias[j].alias%>)
                            <%} %> 
                       <%}%>
                    </label>
                    <%
                    }%>
                </th>
            </tr>
            <tr>
            <th></th>
                <th></th>
                <th colspan="4">
                    <?php echo Yii::t('user','Reporting Period 1') ?>
                </th>
                <th colspan="4">
                    <?php echo Yii::t('user','Reporting Period 2') ?>
                </th>
                <th colspan="4">
                    <?php echo Yii::t('user','Reporting Period 3') ?>
                </th>
                <th colspan="4">
                    <?php echo Yii::t('user','Reporting Period 4') ?>
                </th>
                <th>
                    <?php echo Yii::t('user','Year-End Score') ?>
                </th>
            </tr>
            <tr>
                <td  class="nosort" width='60'>Student ID</td>
                <td class="nosort">Student Name</td>
                <% for(var q=0;q< 4;q++){ %>
                <td>
                    <span href="javascript:;" onmouseover="resetPopups(this,'A','','<%=code%>')" data-toggle="popover">A</span>
                </td>
                <td>
                    <span href="javascript:;" onmouseover="resetPopups(this,'B','','<%=code%>')" data-toggle="popover">B</span>
                </td>
                <td>
                    <span href="javascript:;" onmouseover="resetPopups(this,'C','','<%=code%>')" data-toggle="popover">C</span>
                </td>
                <td>
                    <span href="javascript:;" onmouseover="resetPopups(this,'D','','<%=code%>')" data-toggle="popover">D</span>
                </td>
                <%}%>
                <td class="nosort"></td>
            </tr>
        </thead>
        <tbody>
            <tr class="info">
                
                    <td></td>
                <td>Grade-Level Average</td>
                <% bigdata(classes) %>
                <% if(peroid1_0==0){%>
                <td class="peroid1_0<%=code%>  total1<%=code%>">-</td>
                <%}else{ %>
                <td class="peroid1_0<%=code%>  total1<%=code%>">
                    <%=(peroid1_0/total1_0.length).toFixed(2) %>
                </td>
                <% }%>
                <% if(peroid1_1==0){%>
                <td class="peroid1_1<%=code%>  total1<%=code%>">-</td>
                <%}else{ %>
                <td class="peroid1_1<%=code%>  total1<%=code%>">
                    <%=(peroid1_1/total1_1.length).toFixed(2) %>
                </td>
                <% }%>
                <% if(peroid1_2==0){%>
                <td class="peroid1_2<%=code%>  total1<%=code%>">-</td>
                <%}else{ %>
                <td class="peroid1_2<%=code%>  total1<%=code%>">
                    <%=(peroid1_2/total1_2.length).toFixed(2) %>
                </td>
                <% }%>
                <% if(peroid1_3==0){%>
                <td class="peroid1_3<%=code%>  total1<%=code%>">-</td>
                <%}else{ %>
                <td class="peroid1_3<%=code%>  total1<%=code%>">
                    <%=(peroid1_3/total1_3.length).toFixed(2) %>
                </td>
                <% }%>
                <% if(peroid2_0==0){%>
                <td class="peroid2_0<%=code%>  total2<%=code%>">-</td>
                <%}else{ %>
                <td class="peroid2_0<%=code%>  total2<%=code%>">
                    <%=(peroid2_0/total2_0.length).toFixed(2) %>
                </td>
                <% }%>
                <% if(peroid2_1==0){%>
                <td class="peroid2_1<%=code%>  total2<%=code%>">-</td>
                <%}else{ %>
                <td class="peroid2_1<%=code%>  total2<%=code%>">
                    <%=(peroid2_1/total2_1.length).toFixed(2) %>
                </td>
                <% }%>
                <% if(peroid2_2==0){%>
                <td class="peroid2_2<%=code%>  total2<%=code%>">-</td>
                <%}else{ %>
                <td class="peroid2_2<%=code%>  total2<%=code%>">
                    <%=(peroid2_2/total2_2.length).toFixed(2) %>
                </td>
                <% }%>
                <% if(peroid2_3==0){%>
                <td class="peroid2_3<%=code%>  total2<%=code%>">-</td>
                <%}else{ %>
                <td class="peroid2_3<%=code%>  total2<%=code%>">
                    <%=(peroid2_3/total2_3.length).toFixed(2) %>
                </td>
                <% }%>
                <% if(peroid3_0==0){%>
                <td class="peroid3_0<%=code%>  total3<%=code%>">-</td>
                <%}else{ %>
                <td class="peroid3_0<%=code%>  total3<%=code%>">
                    <%=(peroid3_0/total3_0.length).toFixed(2) %>
                </td>
                <% }%>
                <% if(peroid3_1==0){%>
                <td class="peroid3_1<%=code%>  total3<%=code%>">-</td>
                <%}else{ %>
                <td class="peroid3_1<%=code%>  total3<%=code%>">
                    <%=(peroid3_1/total3_1.length).toFixed(2) %>
                </td>
                <% }%>
                <% if(peroid3_2==0){%>
                <td class="peroid3_2<%=code%>  total3<%=code%>">-</td>
                <%}else{ %>
                <td class="peroid3_2<%=code%>  total3<%=code%>">
                    <%=(peroid3_2/total3_2.length).toFixed(2) %>
                </td>
                <% }%>
                <% if(peroid3_3==0){%>
                <td class="peroid3_3<%=code%>  total3<%=code%>">-</td>
                <%}else{ %>
                <td class="peroid3_3<%=code%>  total3<%=code%>">
                    <%=(peroid3_3/total3_3.length).toFixed(2) %>
                </td>
                <% }%>
                <% if(peroid4_0==0){%>
                <td class="peroid4_0<%=code%>  total4<%=code%>">-</td>
                <%}else{ %>
                <td class="peroid4_0<%=code%>  total4<%=code%>">
                    <%=(peroid4_0/total4_0.length).toFixed(2) %>
                </td>
                <% }%>
                <% if(peroid4_1==0){%>
                <td class="peroid4_1<%=code%>  total4<%=code%>">-</td>
                <%}else{ %>
                <td class="peroid4_1<%=code%>  total4<%=code%>">
                    <%=(peroid4_1/total4_1.length).toFixed(2) %>
                </td>
                <% }%>
                <% if(peroid4_2==0){%>
                <td class="peroid4_2<%=code%>  total4<%=code%>">-</td>
                <%}else{ %>
                <td class="peroid4_2<%=code%>  total4<%=code%>">
                    <%=(peroid4_2/total4_2.length).toFixed(2) %>
                </td>
                <% }%>
                <% if(peroid4_3==0){%>
                <td class="peroid4_3<%=code%>  total4<%=code%>">-</td>
                <%}else{ %>
                <td class="peroid4_3<%=code%>  total4<%=code%>">
                    <%=(peroid4_3/total4_3.length).toFixed(2) %>
                </td>
                <% }%>
                <td></td>
            </tr>
            <% for(key in classes){  %>
            <tr class="total active  <%=key%> code<%= code %>">
                 <td>
                  
                </td>
                <td>Class Average <%=key%></td>
                <% tabledata(classes[key]) %>
                <% if(smallperoid1_0==0){%>
                <td class="subtotal1<%=code%>">-</td>
                <%}else{ %>
                <td class="subtotal1<%=code%>">
                    <%=(smallperoid1_0/peroid1_num0).toFixed(2) %>
                </td>
                <% }%>
                <% if(smallperoid1_1==0){%>
                <td class="subtotal1<%=code%>">-</td>
                <%}else{ %>
                <td class="subtotal1<%=code%>">
                    <%=(smallperoid1_1/peroid1_num1).toFixed(2) %>
                </td>
                <% }%>
                <% if(smallperoid1_2==0){%>
                <td class="subtotal1<%=code%>">-</td>
                <%}else{ %>
                <td class="subtotal1<%=code%>">
                    <%=(smallperoid1_2/peroid1_num2).toFixed(2) %>
                </td>
                <% }%>
                <% if(smallperoid1_3==0){%>
                <td class="subtotal1<%=code%>">-</td>
                <%}else{ %>
                <td class="subtotal1<%=code%>">
                    <%=(smallperoid1_3/peroid1_num3).toFixed(2) %>
                </td>
                <% }%>
                <% if(smallperoid2_0==0){%>
                <td class="subtotal2<%=code%>">-</td>
                <%}else{ %>
                <td class="subtotal2<%=code%>">
                    <%=(smallperoid2_0/peroid2_num0).toFixed(2) %>
                </td>
                <% }%>
                <% if(smallperoid2_1==0){%>
                <td class="subtotal2<%=code%>">-</td>
                <%}else{ %>
                <td class="subtotal2<%=code%>">
                    <%=(smallperoid2_1/peroid2_num1).toFixed(2) %>
                </td>
                <% }%>
                <% if(smallperoid2_2==0){%>
                <td class="subtotal2<%=code%>">-</td>
                <%}else{ %>
                <td class="subtotal2<%=code%>">
                    <%=(smallperoid2_2/peroid2_num2).toFixed(2) %>
                </td>
                <% }%>
                <% if(smallperoid2_3==0){%>
                <td class="subtotal2<%=code%>">-</td>
                <%}else{ %>
                <td class="subtotal2<%=code%>">
                    <%=(smallperoid2_3/peroid2_num3).toFixed(2) %>
                </td>
                <% }%>
                <% if(smallperoid3_0==0){%>
                <td class="subtotal3<%=code%>">-</td>
                <%}else{ %>
                <td class="subtotal3<%=code%>">
                    <%=(smallperoid3_0/peroid3_num0).toFixed(2) %>
                </td>
                <% }%>
                <% if(smallperoid3_1==0){%>
                <td class="subtotal3<%=code%>">-</td>
                <%}else{ %>
                <td class="subtotal3<%=code%>">
                    <%=(smallperoid3_1/peroid3_num1).toFixed(2) %>
                </td>
                <% }%>
                <% if(smallperoid3_2==0){%>
                <td class="subtotal3<%=code%>">-</td>
                <%}else{ %>
                <td class="subtotal3<%=code%>">
                    <%=(smallperoid3_2/peroid3_num2).toFixed(2) %>
                </td>
                <% }%>
                <% if(smallperoid3_3==0){%>
                <td class="subtotal3<%=code%>">-</td>
                <%}else{ %>
                <td class="subtotal3<%=code%>">
                    <%=(smallperoid3_3/peroid3_num3).toFixed(2) %>
                </td>
                <% }%>
                <% if(smallperoid4_0==0){%>
                <td class="subtotal4<%=code%>">-</td>
                <%}else{ %>
                <td class="subtotal4<%=code%>">
                    <%=(smallperoid4_0/peroid4_num0).toFixed(2) %>
                </td>
                <% }%>
                <% if(smallperoid4_1==0){%>
                <td class="subtotal4<%=code%>">-</td>
                <%}else{ %>
                <td class="subtotal4<%=code%>">
                    <%=(smallperoid4_1/peroid4_num1).toFixed(2) %>
                </td>
                <% }%>
                <% if(smallperoid4_2==0){%>
                <td class="subtotal4<%=code%>">-</td>
                <%}else{ %>
                <td class="subtotal4<%=code%>">
                    <%=(smallperoid4_2/peroid4_num2).toFixed(2) %>
                </td>
                <% }%>
                <% if(smallperoid4_3==0){%>
                <td class="subtotal4<%=code%>">-</td>
                <%}else{ %>
                <td class="subtotal4<%=code%>">
                    <%=(smallperoid4_3/peroid4_num3).toFixed(2) %>
                </td>
                <% }%>
                <td></td>
            </tr>
            <% for(var j=0;j < classes[key].length;j++){ %>
            <tr class="<%=key%> code<%= code %> ">
                <td>
                    <%=classes[key][j].childid%>
                </td>
                <td>
                    <%=classes[key][j].name%> 
                </td>
                <% for(var k = 0; k < classes[key][j].peroid1.length; k++) { %>
                <td class="td1<%=code%>">
                    <% if(classes[key][j].peroid1[k] != '-') { %>
                    <span href="javascript:;" onmouseover="resetPopups(this,'<%=k%>','<%= classes[key][j].peroid1[k] %>','<%=code%>')" data-toggle="popover">
                        <%= classes[key][j].peroid1[k] %>
                    </span>
                    <% }else{%>
                    <span><%= classes[key][j].peroid1[k] %></span>
                    <% }%>
                </td>
                <% }%>
                <% for(var k = 0; k < classes[key][j].peroid2.length; k++) { %>
                <td class="td2<%=code%>">
                    <% if(classes[key][j].peroid2[k] != '-') { %>
                    <span href="javascript:;" onmouseover="resetPopups(this,'<%=k%>','<%= classes[key][j].peroid2[k] %>','<%=code%>')" data-toggle="popover">
                        <%= classes[key][j].peroid2[k] %>
                    </span>
                    <% }else{%>
                    <span><%= classes[key][j].peroid2[k] %></span>
                    <% }%>
                </td>
                <% }%>
                <% for(var k = 0; k < classes[key][j].peroid3.length; k++) { %>
                <td class="td3<%=code%>">
                    <% if(classes[key][j].peroid3[k] != '-') { %>
                    <span href="javascript:;" onmouseover="resetPopups(this,'<%=k%>','<%= classes[key][j].peroid3[k] %>','<%=code%>')" data-toggle="popover">
                        <%= classes[key][j].peroid3[k] %>
                    </span>
                    <% }else{%>
                    <span><%= classes[key][j].peroid3[k] %></span>
                    <% }%>
                </td>
                <% }%>
                <% for(var k = 0; k < classes[key][j].peroid4.length; k++) { %>
                <td class="td4<%=code%>">
                    <% if(classes[key][j].peroid4[k] != '-') { %>
                    <span href="javascript:;" onmouseover="resetPopups(this,'<%=k%>','<%= classes[key][j].peroid4[k] %>','<%=code%>')" data-toggle="popover">
                        <%= classes[key][j].peroid4[k] %>
                    </span>
                    <% }else{%>
                    <span><%= classes[key][j].peroid4[k] %></span>
                    <% }%>
                </td>
                <% }%>
                <td><%=classes[key][j].yearend%></td>
            </tr>
            <%}%>
            <%}%>
        </tbody>
    </table>
</script>
<script type="text/template" id="type2-template">
    <div class="mt15">
        <a class="btn btn-primary pull-left mr15" download='table<%=code%>.xls' href="#" onclick="return ExcellentExport.excel(this, 'table<%=code%>', 'Sheet Name Here');"><?php echo Yii::t('user','Excel Export'); ?></a>
        <p class='search_checkbox checkbox pull-left'>
            <span class="mr15 checkspan pull-left"><?php echo Yii::t('user','Coloring switch'); ?></span>
            <input type='checkbox' id='che<%=code%>' name='colorname' value='<%=code%>' checked="checked" onclick="color(this,'<%=code%>')">
            <label for='che<%=code%>'></label>
        </p>
    </div>
    <table class="table  table-bordered tabledata" id='table<%=code%>'>
        <thead>
            <tr>
                <th colspan="10">Course Name:
                    <%=title%>
                </th>
                <th colspan="4">Course Code:
                    <%=code%>
                </th>
                <th colspan="4">Grade:
                    <%=grade%>
                </th>
                <th></th>
            </tr>
            <tr>
                <th colspan="21">
                    <% for(key in classes){ %>
                    <label class="checkbox-inline">
                      <input type="checkbox" id="<%=key%>" checked name='<%=code%>' value='<%=key%>' onclick="check(this,'<%=key%>','<%=code%>')"> <%=key%>
                    </label>
                    <%}%>
                </th>
            </tr>
            <tr>
            <th></th>
                <th></th>
                <th colspan="4">Criterion A</th>
                <th colspan="4">Criterion B</th>
                <th colspan="4">Criterion C</th>
                <th colspan="4">Criterion D</th>
                <th>Year-End Score</th>
            </tr>
            <tr>
            <td  class="nosort" width='60'>Student ID</td>
                <td class="nosort">Student Name</td>
                <% for(var q=0;q< 4;q++){ %>
                <td>1</td>
                <td>2</td>
                <td>3</td>
                <td>4</td>
                <%}%>
                <td class="nosort"></td>
            </tr>
        </thead>
        <tbody>
            <tr class="info">
            <td></td>
                <td>Grade-Level Average</td>
                <% bigdata(classes) %>
                <% if(peroid1_0==0){%>
                <td class="peroid1_0<%=code%> total1<%=code%>">-</td>
                <%}else{ %>
                <td class="peroid1_0<%=code%> total1<%=code%>">
                    <%=(peroid1_0/total1_0.length).toFixed(2) %>
                </td>
                <% }%>
                <% if(peroid2_0==0){%>
                <td class="peroid2_0<%=code%> total2<%=code%>">-</td>
                <%}else{ %>
                <td class="peroid2_0<%=code%> total2<%=code%>">
                    <%=(peroid2_0/total2_0.length).toFixed(2) %>
                </td>
                <% }%>
                <% if(peroid3_0==0){%>
                <td class="peroid3_0<%=code%> total3<%=code%>">-</td>
                <%}else{ %>
                <td class="peroid3_0<%=code%> total3<%=code%>">
                    <%=(peroid3_0/total3_0.length).toFixed(2) %>
                </td>
                <% }%>
                <% if(peroid4_0==0){%>
                <td class="peroid4_0<%=code%> total4<%=code%>">-</td>
                <%}else{ %>
                <td class="peroid4_0<%=code%> total4<%=code%>">
                    <%=(peroid4_0/total4_0.length).toFixed(2) %>
                </td>
                <% }%>
                <% if(peroid1_1==0){%>
                <td class="peroid1_1<%=code%> total1<%=code%>">-</td>
                <%}else{ %>
                <td class="peroid1_1<%=code%> total1<%=code%>">
                    <%=(peroid1_1/total1_1.length).toFixed(2) %>
                </td>
                <% }%>
                <% if(peroid2_1==0){%>
                <td class="peroid2_1<%=code%> total2<%=code%>">-</td>
                <%}else{ %>
                <td class="peroid2_1<%=code%> total2<%=code%>">
                    <%=(peroid2_1/total2_1.length).toFixed(2) %>
                </td>
                <% }%>
                <% if(peroid3_1==0){%>
                <td class="peroid3_1<%=code%> total3<%=code%>">-</td>
                <%}else{ %>
                <td class="peroid3_1<%=code%> total3<%=code%>">
                    <%=(peroid3_1/total3_1.length).toFixed(2) %>
                </td>
                <% }%>
                <% if(peroid4_1==0){%>
                <td class="peroid4_1<%=code%> total4<%=code%>">-</td>
                <%}else{ %>
                <td class="peroid4_1<%=code%> total4<%=code%>">
                    <%=(peroid4_1/total4_1.length).toFixed(2) %>
                </td>
                <% }%>
                <% if(peroid1_2==0){%>
                <td class="peroid1_2<%=code%> total1<%=code%>">-</td>
                <%}else{ %>
                <td class="peroid1_2<%=code%> total1<%=code%>">
                    <%=(peroid1_2/total1_2.length).toFixed(2) %>
                </td>
                <% }%>
                <% if(peroid2_2==0){%>
                <td class="peroid2_2<%=code%> total2<%=code%>">-</td>
                <%}else{ %>
                <td class="peroid2_2<%=code%> total2<%=code%>">
                    <%=(peroid2_2/total2_2.length).toFixed(2) %>
                </td>
                <% }%>
                <% if(peroid3_2==0){%>
                <td class="peroid3_2<%=code%> total3<%=code%>">-</td>
                <%}else{ %>
                <td class="peroid3_2<%=code%> total3<%=code%>">
                    <%=(peroid3_2/total3_2.length).toFixed(2) %>
                </td>
                <% }%>
                <% if(peroid4_2==0){%>
                <td class="peroid4_2<%=code%> total4<%=code%>">-</td>
                <%}else{ %>
                <td class="peroid4_2<%=code%> total4<%=code%>">
                    <%=(peroid4_2/total4_2.length).toFixed(2) %>
                </td>
                <% }%>
                <% if(peroid1_3==0){%>
                <td class="peroid1_3<%=code%> total1<%=code%>">-</td>
                <%}else{ %>
                <td class="peroid1_3<%=code%> total1<%=code%>">
                    <%=(peroid1_3/total1_3.length).toFixed(2) %>
                </td>
                <% }%>
                <% if(peroid2_3==0){%>
                <td class="peroid2_3<%=code%> total2<%=code%>">-</td>
                <%}else{ %>
                <td class="peroid2_3<%=code%> total2<%=code%>">
                    <%=(peroid2_3/total2_3.length).toFixed(2) %>
                </td>
                <% }%>
                <% if(peroid3_3==0){%>
                <td class="peroid3_3<%=code%> total3<%=code%>">-</td>
                <%}else{ %>
                <td class="peroid3_3<%=code%> total3<%=code%>">
                    <%=(peroid3_3/total3_3.length).toFixed(2) %>
                </td>
                <% }%>
                <% if(peroid4_3==0){%>
                <td class="peroid4_3<%=code%> total4<%=code%>">-</td>
                <%}else{ %>
                <td class="peroid4_3<%=code%> total4<%=code%>">
                    <%=(peroid4_3/total4_3.length).toFixed(2) %>
                </td>
                <% }%>
                <td></td>
            </tr>
            <% for(key in classes){ %>
            <tr class="total active <%=key%> code<%= code %>">
            <td></td>
                <td>Class Average <%=key%></td>
                <% tabledata(classes[key]) %>
                <% if(smallperoid1_0==0){%>
                <td class="subtotal1<%=code%>">-</td>
                <% }else{%>
                <td class="subtotal1<%=code%>">
                    <%=(smallperoid1_0/peroid1_num0).toFixed(2) %>
                </td>
                <% }%>
                <% if(smallperoid2_0==0){%>
                <td class="subtotal2<%=code%>">-</td>
                <% }else{%>
                <td class="subtotal2<%=code%>">
                    <%=(smallperoid2_0/peroid2_num0).toFixed(2) %>
                </td>
                <% }%>
                <% if(smallperoid3_0==0){%>
                <td class="subtotal3<%=code%>">-</td>
                <% }else{%>
                <td class="subtotal3<%=code%>">
                    <%=(smallperoid3_0/peroid3_num0).toFixed(2) %>
                </td>
                <% }%>
                <% if(smallperoid4_0==0){%>
                <td class="subtotal4<%=code%>">-</td>
                <% }else{%>
                <td class="subtotal4<%=code%>">
                    <%=(smallperoid4_0/peroid4_num0).toFixed(2) %>
                </td>
                <% }%>
                <% if(smallperoid1_1==0){%>
                <td class="subtotal1<%=code%>">-</td>
                <% }else{%>
                <td class="subtotal1<%=code%>">
                    <%=(smallperoid1_1/peroid1_num1).toFixed(2) %>
                </td>
                <% }%>
                <% if(smallperoid2_1==0){%>
                <td class="subtotal2<%=code%>">-</td>
                <% }else{%>
                <td class="subtotal2<%=code%>">
                    <%=(smallperoid2_1/peroid2_num1).toFixed(2) %>
                </td>
                <% }%>
                <% if(smallperoid3_1==0){%>
                <td class="subtotal3<%=code%>">-</td>
                <% }else{%>
                <td class="subtotal3<%=code%>">
                    <%=(smallperoid3_1/peroid3_num1).toFixed(2) %>
                </td>
                <% }%>
                <% if(smallperoid4_1==0){%>
                <td class="subtotal4<%=code%>">-</td>
                <% }else{%>
                <td class="subtotal4<%=code%>">
                    <%=(smallperoid4_1/peroid4_num1).toFixed(2) %>
                </td>
                <% }%>
                <% if(smallperoid1_2==0){%>
                <td class="subtotal1<%=code%>">-</td>
                <% }else{%>
                <td class="subtotal1<%=code%>">
                    <%=(smallperoid1_2/peroid1_num2).toFixed(2) %>
                </td>
                <% }%>
                <% if(smallperoid2_2==0){%>
                <td class="subtotal2<%=code%>">-</td>
                <% }else{%>
                <td class="subtotal2<%=code%>">
                    <%=(smallperoid2_2/peroid2_num2).toFixed(2) %>
                </td>
                <% }%>
                <% if(smallperoid3_2==0){%>
                <td class="subtotal3<%=code%>">-</td>
                <% }else{%>
                <td class="subtotal3<%=code%>">
                    <%=(smallperoid3_2/peroid3_num2).toFixed(2) %>
                </td>
                <% }%>
                <% if(smallperoid4_2==0){%>
                <td class="subtotal4<%=code%>">-</td>
                <% }else{%>
                <td class="subtotal4<%=code%>">
                    <%=(smallperoid4_2/peroid4_num2).toFixed(2) %>
                </td>
                <% }%>
                <% if(smallperoid1_3==0){%>
                <td class="subtotal1<%=code%>">-</td>
                <% }else{%>
                <td class="subtotal1<%=code%>">
                    <%=(smallperoid1_3/peroid1_num3).toFixed(2) %>
                </td>
                <% }%>
                <% if(smallperoid2_3==0){%>
                <td class="subtotal2<%=code%>">-</td>
                <% }else{%>
                <td class="subtotal2<%=code%>">
                    <%=(smallperoid2_3/peroid2_num3).toFixed(2) %>
                </td>
                <% }%>
                <% if(smallperoid3_3==0){%>
                <td class="subtotal3<%=code%>">-</td>
                <% }else{%>
                <td class="subtotal3<%=code%>">
                    <%=(smallperoid3_3/peroid3_num3).toFixed(2) %>
                </td>
                <% }%>
                <% if(smallperoid4_3==0){%>
                <td class="subtotal4<%=code%>">-</td>
                <% }else{%>
                <td class="subtotal4<%=code%>">
                    <%=(smallperoid4_3/peroid4_num3).toFixed(2) %>
                </td>
                <% }%>
                <td></td>
            </tr>
            <% for(var j=0;j < classes[key].length;j++){ %>
            <tr class="<%=key%> code<%= code %>">
            <td> <%=classes[key][j].childid%></td>
                <td><%=classes[key][j].name%></td>
                <% for(var k = 0; k < 4; k++) { %>
                <td class="td1<%=code%>">
                    <% if(classes[key][j].peroid1[k] != '-') { %>
                    <span href="javascript:;" onmouseover="resetPopups(this,'<%=k%>','<%= classes[key][j].peroid1[k] %>','<%=code%>')" data-toggle="popover">
                        <%= classes[key][j].peroid1[k] %>
                    </span>
                    <% }else{%>
                    <span><%= classes[key][j].peroid1[k] %></span>
                    <% }%>
                </td>
                <td class="td2<%=code%>">
                    <% if(classes[key][j].peroid2[k] != '-') { %>
                    <span href="javascript:;" onmouseover="resetPopups(this,'<%=k%>','<%= classes[key][j].peroid2[k] %>','<%=code%>')" data-toggle="popover">
                        <%= classes[key][j].peroid2[k] %>
                    </span>
                    <% }else{%>
                    <span><%= classes[key][j].peroid2[k] %></span>
                    <% }%>
                </td>
                <td class="td3<%=code%>">
                    <% if(classes[key][j].peroid3[k] != '-') { %>
                    <span href="javascript:;" onmouseover="resetPopups(this,'<%=k%>','<%= classes[key][j].peroid3[k] %>','<%=code%>')" data-toggle="popover">
                        <%= classes[key][j].peroid3[k] %>
                    </span>
                    <% }else{%>
                    <span><%= classes[key][j].peroid3[k] %></span>
                    <% }%>
                </td>
                <td class="td4<%=code%>">
                    <% if(classes[key][j].peroid4[k] != '-') { %>
                    <span href="javascript:;" onmouseover="resetPopups(this,'<%=k%>','<%= classes[key][j].peroid4[k] %>','<%=code%>')" data-toggle="popover">
                        <%= classes[key][j].peroid4[k] %>
                    </span>
                    <% }else{%>
                    <span><%= classes[key][j].peroid4[k] %></span>
                    <% }%>
                </td>
                <% }%>
                <td><%=classes[key][j].yearend%></td>
            </tr>
            <%}%>
            <%}%>
        </tbody>
    </table>
</script>
    <div class="modal fade bs-example-modal-lg" id='colorMode' tabindex="-1" role="dialog" aria-labelledby="myLargeModalLabel" data-backdrop="static" data-keyboard="false">
      <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
         <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-hidden="true">
                &times;
            </button>
            <h4 class="modal-title" id="myModalLabel">
              选择颜色
            </h4>
        </div>
          <?php $form = $this->beginWidget('CActiveForm', array(
                'id' => 'expense-forms',
                'enableAjaxValidation' => false,
                'action' => ($model->id) ? $this->createUrl('expenesAdd', array('expenesId' => $model->id)) : $this->createUrl('updateColor'),
                'htmlOptions' => array('class' => 'J_ajaxForm form-horizontal', 'role' => 'form', 'enctype' => "multipart/form-data"),
            )); ?>
        <div class="modal-body">
            <div class="list-group col-md-4">
                <li class="list-group-item "  onclick="$('.add').show();$('#colorTit').val('')" ><span class="glyphicon glyphicon-plus"></span> 新建</li>
                <span class="list-group-item"  @click="addClassFun(index,tie.id)" :class='{active:index==listindex}' v-for='(tie,index) in title'>{{tie.title}}</span>
                <p class="list-group-item add">
                   <input type="text" class="form-control" id="colorTit" placeholder="请输入名称"  autocomplete="off" >
                   <a class="btn btn-primary btn-xs pull-right mt15" @click='addColorCon()'>确定</a>
                   <span class="clearfix"></span>
                </p> 
            </div>
             
            <div class="col-md-8"  v-if='colorjson!=""'>
                <div class="mb15">
                <div class="form-group col-md-6">
                      <input type="text" class="form-control" :value='colorjson.title' name='title'>
                  </div>
                    <p class="pull-right">
                    <label class="checkbox-inline" id='checkdata' v-if='colorjson.status=="1"'><input type="checkbox" value="1" name="status"  checked="checked">应用当前颜色</label>
                     <label class="checkbox-inline" id='checkdata' v-if='colorjson.status=="0"'><input type="checkbox" value="1" name="status">应用当前颜色</label>
                     </p>
                    <input type="text" name='color_id' :value='colorjson.id' class="hidden">
                </div>
                <table class="table table-bordered">
                    <tr>
                     <td>#</td>
                     <td>1</td>
                     <td>2</td>
                     <td>3</td>
                     <td>4</td>
                    </tr>
                    <template  v-if='colorjson.data.length==0'>
                        <tr v-for='(id,i) in 9'>
                            <td>{{i}}</td>
                             <td>   
                                <input type="color" value="" :name='"data[peroid1][" + i +"]"'  class="onchange">        
                             </td>
                             <td>
                                <input type="color" value="" :name='"data[peroid2][" + i +"]"' class="onchange"> 
                             </td>
                             <td>
                                <input type="color" value="" :name='"data[peroid3][" + i +"]"' class="onchange"> 
                             </td>
                             <td>
                                <input type="color" value="" :name='"data[peroid4][" + i +"]"' class="onchange"> 
                             </td> 
                        </tr>
                    </template>
                    <template v-else>
                       <tr v-for='(id,i) in 9'>
                            <td>{{i}}</td>
                             <td>   
                                <input type="color" :value="colorjson.data.peroid1[i]" :name='"data[peroid1][" + i +"]"'>        
                             </td>
                             <td>
                                <input type="color" :value="colorjson.data.peroid2[i]" :name='"data[peroid2][" + i +"]"'> 
                             </td>
                             <td>
                                <input type="color" :value="colorjson.data.peroid3[i]" :name='"data[peroid3][" + i +"]"'> 
                             </td>
                             <td>
                                <input type="color" :value="colorjson.data.peroid4[i]" :name='"data[peroid4][" + i +"]"'> 
                             </td> 
                        </tr>
                    </template>
                </table>
            </div>
            <div class="clearfix"></div>
           
            <div class="clearfix"></div>
        </div>   
         <div class="modal-footer">
            <button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global','Submit');?></button>
            <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
        </div>
        <?php $this->endWidget(); ?>
      </div>
    </div>
</div>
<script> 
    var data = <?php echo json_encode($data) ?>;
    var datatype = <?php echo json_encode($type) ?>;
    var calenderList = <?php echo json_encode($calenderList) ?>;
    var yid = <?php echo $yid ?>;
    var fractionColor = <?php echo json_encode($fractionColor) ?>;
    var jsondata = {
        "peroid1": {
            "0": "#d9534f",
            "1": "#d9534f",
            "2": "#d9534f",
            "3": "",
            "4": "",
            "5": "",
            "6": "",
            "7": "",
            "8": ""
        },
        "peroid2": {
            "0": "#d9534f",
            "1": "#d9534f",
            "2": "#d9534f",
            "3": "",
            "4": "",
            "5": "",
            "6": "",
            "7": "",
            "8": ""
        },
        "peroid3": {
            "0": "#d9534f",
            "1": "#d9534f",
            "2": "#d9534f",
            "3": "#f0ad4e",
            "4": "#f0ad4e",
            "5": "#696969",
            "6": "#696969",
            "7": "#696969",
            "8": "#696969"
        },
        "peroid4": {
            "0": "#d9534f",
            "1": "#d9534f",
            "2": "#d9534f",
            "3": "#f0ad4e",
            "4": "#f0ad4e",
            "5": "#696969",
            "6": "#696969",
            "7": "#696969",
            "8": "#696969"
        }
    }
    var type1 = _.template($('#type1-template').html());
    var type2 = _.template($('#type2-template').html());
    var datalists = ''
    var jsons = new Vue({
        el: "#colorMode",
        data: {
            json:'',
            title:[],
            yid:yid,
            listindex: '-1',
            colorjson:'',
            checkeddata:fractionColor,
            status:"",
            onedata:fractionColor
            },
        methods: {
            setClass(key) {
                let obj = {'colorpicker-element': true}
                obj[`datacolor${key}`] = true
                return obj
            },
            addClassFun(index,id){
                this.listindex=index
                this.colorid=id  
                this.colorjson=''    
                this.colorjson=this.title[index]
                if(this.title[index].status=='1'){
                    $('#checkdata').html(' <input type="checkbox" value="1" name="status"  checked="checked">应用当前颜色')
                }else{
                    $('#checkdata').html(' <input type="checkbox" value="1" name="status" >应用当前颜色')
                }
            },
            addColorCon(){
               $.ajax({
                    url: '<?php echo $this->createUrl("updateColor")?>',
                    type: "POST",
                    async: true,
                    dataType: 'json',
                    data: {
                        title:$('#colorTit').val(),
                        yid: yid
                    },
                    success: function(data) {
                       if(data.state == 'success') {
                         jsons.title.push(data.data)
                         $('.add').hide()
                       }
                    },
                    error: function() {
                        alert("请求错误")
                    }
                }); 
            },            
        }
    })
    function cbSuccess(data){
        setTimeout(function (){
        $('#colorMode').modal('hide')
        }, 1000);
        if(data.status=='1'){
            jsons.json=data.data
            jsons.checkeddata=data
        }else{
            if(data.id==jsons.checkeddata.id){
                jsons.json=jsondata
                jsons.checkeddata=''
                jsons.onedata=''
            }            
        }
        var datafor=[]
        $("input[name='colorname']:checked").each(function() {
           datafor.push($(this).val())
        });
        for(var i=0;i<datafor.length;i++){
             colorcode(datafor[i])
        }
    }
    function slectColor(){
        $('.add').hide()
         jsons.title=[]
         $.ajax({
            url: '<?php echo $this->createUrl("showFractionColor")?>',
            type: "POST",
            async: true,
            dataType: 'json',
            data: {
                yid: yid
            },
            success: function(data) {
                jsons.title=data
                 $('#colorMode').modal('show')
                 if(jsons.checkeddata!=""){
                    for(var i=0;i<data.length;i++){
                        if(data[i].id==jsons.checkeddata.id){
                            jsons.listindex=i
                            jsons.colorjson=jsons.checkeddata
                            $('#checkdata').html('<input type="checkbox" value="1" name="status"  checked="checked">应用当前颜色')
                        }
                     }
                 }else{
                    if( jsons.onedata.length!=0){
                        for(var i=0;i<data.length;i++){
                            if(data[i].id== jsons.onedata.id){
                                jsons.listindex=i
                                jsons.colorjson=fractionColor
                            }
                        }
                    }else{
                       if(data.length!=0){
                            jsons.listindex=0
                            jsons.colorjson=data[0]
                       }
                    }
                 }
            },
            error: function(data) {
                alert("请求错误")
            }
        }); 
    }
    var datalist = new Vue({
        el: "#datalist",
        data: {
            checkedNames: [],
            data: data,
            qwerqwre: "0",
            datalist:'',
            calenderList: calenderList,
            yid: yid,
            couponSelected: '',
            checkcoursename:'',
            height: {　　　　　　
                'max-height': '',
            },
            insideheight: {　　　　　　
                height: '',
            }
        },
        mounted: function() {
            if(data.length!=0){
               this.datalist=data[0]
                 this.checkcoursename=data[0].title
            }
            this.$nextTick(function(){
               this.addEvent()
            })
        },
        methods: {
            addEvent() {
                if($('.listheight').height() <= 100) {
                    $('.panel').css("max-height", $('.listheight').height() + 202 + 'px')
                    $('.listdata').css("max-height", $('.listheight').height() + 200 - $('.panel-heading').outerHeight() + 'px')
                } else {
                    $('.panel').css("max-height", $('.listheight').height() + 2 + 'px')
                    $('.listdata').css("max-height", $('.listheight').outerHeight() - $('.panel-heading').outerHeight() + 'px')
                }
            },
            addClassFun(index, name) {
                this.checkcoursename = name
                this.qwerqwre = index;
                this.datalist = this.data[this.qwerqwre]
                $('#tel').html('')
                $('.yes').removeClass('disabled').removeAttr('disabled');
                $('.yes').html('<?php echo Yii::t('global','OK'); ?>')
                $('.vuelist input[type=checkbox]').attr('checked', false);
            },
        }
    })
    function color(obj, code) {
        if($(obj).is(':checked')) {
            colorcode(code)
        } else {
            for(var i = 1; i < 5; i++) {
                $(".td" + i + code).each(function() {
                    $(this).css('background', '')
                    $(this).css('color', '')
                    $(this).find('a').css('color', '')
                });
                $(".total" + i + code).each(function() {
                    $(this).css('background', '')
                    $(this).css('color', '')
                    $(this).find('a').css('color', '')
                });
                $(".subtotal" + i + code).each(function() {
                    $(this).css('background', '')
                    $(this).css('color', '')
                    $(this).find('a').css('color', '')
                });
            }
        }
    }
    function add() {
        $('.yes').addClass('disabled').attr('disabled', 'disabled')
        $('.yes').html('<?php echo Yii::t('user','Fetching data...'); ?>')
        var items = []
        $("input[name='item']:checkbox:checked").each(function() {
            items.push($(this).val())
        });
        if(items.length == 0) {
            resultTip({
                error: 'warning',
                msg: '请选择科目'
            });
            $('.yes').removeClass('disabled').removeAttr('disabled');
            $('.yes').html('<?php echo Yii::t('global','OK'); ?>')
            return
        }
        $.ajax({
            url: '<?php echo $this->createUrl("studentRoster")?>',
            type: 'post',
            dataType: 'json',
            data: {
                code: items,
                yid: yid
            },
            success: function(data) {
                if(data.state == 'success') {
                    $('#tel').html('')
                    datalists = data
                    $.each(data.data, function(_i, _v) {
                        if(datatype == 'roster1') {
                            var view = type1(_v);
                            $('#tel').append(view);
                        } else {
                            var view = type2(_v);
                            $('#tel').append(view);
                        }
                        $('.yes').removeClass('disabled').removeAttr('disabled');
                        $('.yes').html('<?php echo Yii::t('global','OK'); ?>')
                        $('.selectcolor').show()
                    })
                    if(jsons.json==''){
                        if(fractionColor.length!=0){
                            jsons.json=fractionColor.data
                        }else{
                            jsons.json=jsondata
                        }
                    }
                    colordata()
                } else {
                    $('.yes').removeClass('disabled').removeAttr('disabled');
                    $('.yes').html('<?php echo Yii::t('global','OK'); ?>')
                    resultTip({
                        error: 'warning',
                        msg: data.message
                    });
                }
                $('.tabledata').dataTable({
                    paging: false,
                    info: false,
                    searching: false,
                    "aaSorting": [],
                    "aoColumnDefs": [{
                        "targets": 'nosort', //列的样式名
                        "orderable": false //包含上样式名‘nosort’的禁止排序
                    }],
                });
            },
            error: function(data) {
                $('.yes').removeClass('disabled').removeAttr('disabled');
                $('.yes').html('<?php echo Yii::t(' global','OK'); ?>')
                resultTip({
                    error: 'warning',
                    msg: '请求错误'
                });
            }
        })
    }
    function colorcode(code) {
        for(var i = 1; i < 5; i++) {
            $(".td" + i + code).each(function() {
                $(this).css('background', jsons.json['peroid' + i][$.trim($(this).text())])
                 fontColor(this,i,$.trim($(this).text()))
            });
            $(".total" + i + code).each(function() {   
                if(jsons.json['peroid' + i][parseInt($.trim($(this).text()))]==""){
                   $(this).css("background","#d9edf7");
                }else{
                   $(this).css('background', jsons.json['peroid' + i][parseInt($.trim($(this).text()))]) 
                }
                fontColor(this,i,$.trim($(this).text()))
            });
            $(".subtotal" + i + code).each(function() {          
                if(jsons.json['peroid' + i][parseInt($.trim($(this).text()))]==""){
                   $(this).css("background","#f5f5f5");
                }else{
                   $(this).css('background', jsons.json['peroid' + i][parseInt($.trim($(this).text()))]) 
                }
                fontColor(this,i,$.trim($(this).text()))
            });
        }
    }
    function colordata() {
        for(var j = 0; j < datalists.data.length; j++) {
            for(key in datalists.data[j].classes) {
                for(var i = 1; i < 5; i++) {
                    $(".td" + i + datalists.data[j].code).each(function() {
                        $(this).css({"width":"45","cursor":"pointer","font-size":"13px","background":jsons.json['peroid' + i][$.trim($(this).text())]});
                        fontColor(this,i,$.trim($(this).text()))
                    });
                    $(".total" + i + datalists.data[j].code).each(function() {
                        fontColor(this,i,$.trim($(this).text()))
                        if(jsons.json['peroid' + i][parseInt($.trim($(this).text()))]==""){
                           $(this).css({"width":"45","font-size":"13px","background":"#d9edf7"});
                        }else{
                           $(this).css({"width":"45","font-size":"13px","background":jsons.json['peroid' + i][parseInt($.trim($(this).text()))]}); 
                        }
                    });
                    $(".subtotal" + i + datalists.data[j].code).each(function() {
                        fontColor(this,i,$.trim($(this).text()))
                        if(jsons.json['peroid' + i][parseInt($.trim($(this).text()))]==""){
                            $(this).css({"width":"45","font-size":"13px","background":"#f5f5f5"});
                        }else{
                            $(this).css({"width":"45","font-size":"13px","background":jsons.json['peroid' + i][parseInt($.trim($(this).text()))]});
                        }
                    });
                }
            }
        }
    }
    function fontColor(obj,i,text){  
        if(jsons.json['peroid' + i][parseInt(text)]=='#d9534f' || jsons.json['peroid' + i][parseInt(text)]=='#f0ad4e' || jsons.json['peroid' + i][parseInt(text)]=='#696969' ){
            $(obj).css('color','#fff')
        }else{
            $(obj).css('color','')
        }
    }
    function tabledata(classes) {
        smallperoid1_0 = 0, smallperoid1_1 = 0, smallperoid1_2 = 0, smallperoid1_3 = 0, smallperoid2_0 = 0, smallperoid2_1 = 0, smallperoid2_2 = 0, smallperoid2_3 = 0, smallperoid3_0 = 0, smallperoid3_1 = 0, smallperoid3_2 = 0, smallperoid3_3 = 0, smallperoid4_0 = 0, smallperoid4_1 = 0, smallperoid4_2 = 0, smallperoid4_3 = 0,
        peroid1_num0 = 0, peroid1_num1 = 0, peroid1_num2 = 0, peroid1_num3 = 0, peroid2_num0 = 0, peroid2_num1 = 0, peroid2_num2 = 0, peroid2_num3 = 0, peroid3_num0 = 0, peroid3_num1 = 0, peroid3_num2 = 0, peroid3_num3 = 0, peroid4_num0 = 0, peroid4_num1 = 0, peroid4_num2 = 0, peroid4_num3 = 0;
        for(var j = 0; j < classes.length; j++) {
            for(var k = 0; k < 4; k++) {
                if(classes[j].peroid1[k] != '-') {
                    smallname = "smallperoid1_" + k;
                    smallnum = "peroid1_num" + k;
                    window[smallname] += parseInt(classes[j].peroid1[k]);
                    window[smallnum] += parseInt(classes[j].peroid1[k].length);
                }
                if(classes[j].peroid2[k] != '-') {
                    smallname1 = "smallperoid2_" + k;
                    smallnum1 = "peroid2_num" + k;
                    window[smallname1] += parseInt(classes[j].peroid2[k]);
                    window[smallnum1] += parseInt(classes[j].peroid2[k].length);
                }
                if(classes[j].peroid3[k] != '-') {
                    smallname2 = "smallperoid3_" + k;
                    smallnum2 = "peroid3_num" + k;
                    window[smallname2] += parseInt(classes[j].peroid3[k]);
                    window[smallnum2] += parseInt(classes[j].peroid3[k].length);
                }
                if(classes[j].peroid4[k] != '-') {
                    smallname3 = "smallperoid4_" + k;
                    smallnum3 = "peroid4_num" + k;
                    window[smallname3] += parseInt(classes[j].peroid4[k]);
                    window[smallnum3] += parseInt(classes[j].peroid4[k].length);
                }
            }
        }
    }
    function bigdata(classes) {
        peroid1_0 = 0, peroid1_1 = 0, peroid1_2 = 0, peroid1_3 = 0, peroid2_0 = 0, peroid2_1 = 0, peroid2_2 = 0, peroid2_3 = 0, peroid3_0 = 0, peroid3_1 = 0, peroid3_2 = 0, peroid3_3 = 0, peroid4_0 = 0, peroid4_1 = 0, peroid4_2 = 0, peroid4_3 = 0,
        total1_0 = [], total1_1 = [], total1_2 = [], total1_3 = [], total2_0 = [], total2_1 = [], total2_2 = [], total2_3 = [], total3_0 = [], total3_1 = [], total3_2 = [], total3_3 = [], total4_0 = [], total4_1 = [], total4_2 = [], total4_3 = [];
        for(key in classes) {
            for(var j = 0; j < classes[key].length; j++) {
                var renum = /^[0-9]+.?[0-9]*$/;
                for(var k = 0; k < 4; k++) {
                    if(renum.test(classes[key][j].peroid1[k])) {
                        bigname = "peroid1_" + k;
                        bignum = "total1_" + k;
                        window[bigname] += parseInt(classes[key][j].peroid1[k]);
                        window[bignum].push(classes[key][j].peroid1[k].length)
                    }
                    if(renum.test(classes[key][j].peroid2[k])) {
                        bigname = "peroid2_" + k;
                        bignum = "total2_" + k;
                        window[bigname] += parseInt(classes[key][j].peroid2[k]);
                        window[bignum].push(classes[key][j].peroid2[k].length)
                    }
                    if(renum.test(classes[key][j].peroid3[k])) {
                        bigname = "peroid3_" + k;
                        bignum = "total3_" + k;
                        window[bigname] += parseInt(classes[key][j].peroid3[k]);
                        window[bignum].push(classes[key][j].peroid3[k].length)
                    }
                    if(renum.test(classes[key][j].peroid4[k])) {
                        bigname = "peroid4_" + k;
                        bignum = "total4_" + k;
                        window[bigname] += parseInt(classes[key][j].peroid4[k]);
                        window[bignum].push(classes[key][j].peroid4[k].length)
                    }
                }
            }
        }
    }
    function check(obj, keyid, code) {
        var items = []
        $("input[name=" + code + "]:checked").each(function() {
            items.push($(this).val())
        });
        var datatotal = []
        for(var i = 0; i < datalists.data.length; i++) {
            for(var j = 0; j < items.length; j++) {
                for(key in datalists.data[i].classes) {
                    if(key == items[j]) {
                        datatotal.push(datalists.data[i].classes[key])
                    }
                }
            }
        }
        if($(obj).is(':checked')) {
            $('.' + keyid).find('tr').eq(0).removeClass('node').addClass('total')
            $('.' + keyid).show()
            if($('#che' + code).is(':checked')) {
                colorcode(code)
            }
        } else {
            $('.' + keyid).find('tr').eq(0).removeClass('total').addClass('node')
            $('.' + keyid).hide()
        }
             sumperoid1_0 = 0, sumperoid1_1 = 0,sumperoid1_2 = 0,sumperoid1_3 = 0,sumperoid2_0 = 0,sumperoid2_1 = 0,sumperoid2_2 = 0,sumperoid2_3 = 0,sumperoid3_0 = 0,sumperoid3_1 = 0,sumperoid3_2 = 0,sumperoid3_3 = 0,sumperoid4_0 = 0,sumperoid4_1 = 0,sumperoid4_2 = 0,sumperoid4_3 = 0;sumtotal1_0 = [],sumtotal1_1 = [],sumtotal1_2 = [],sumtotal1_3 = [],sumtotal2_0 = [],sumtotal2_1 = [],sumtotal2_2 = [],sumtotal2_3 = [],sumtotal3_0 = [],sumtotal3_1 = [],sumtotal3_2 = [],sumtotal3_3 = [],sumtotal4_0 = [],sumtotal4_1 = [],sumtotal4_2 = [],sumtotal4_3 = [];
        for(var j = 0; j < datatotal.length; j++) {
            for(var i = 0; i < datatotal[j].length; i++) { 　
                var renum = /^[0-9]+.?[0-9]*$/;
                for(var k = 0; k < 4; k++) {
                    if(renum.test(datatotal[j][i].peroid1[k])) {
                        varname = "sumperoid1_" + k;
                        num = "sumtotal1_" + k;
                        window[varname] += parseInt(datatotal[j][i].peroid1[k]);
                        window[num].push(datatotal[j][i].peroid1[k].length)
                    }
                    if(renum.test(datatotal[j][i].peroid2[k])) {
                        varname = "sumperoid2_" + k;
                        num = "sumtotal2_" + k;
                        window[varname] += parseInt(datatotal[j][i].peroid2[k]);
                        window[num].push(datatotal[j][i].peroid2[k].length)
                    }
                    if(renum.test(datatotal[j][i].peroid3[k])) {
                        varname = "sumperoid3_" + k;
                        num = "sumtotal3_" + k;
                        window[varname] += parseInt(datatotal[j][i].peroid3[k]);
                        window[num].push(datatotal[j][i].peroid3[k].length)
                    }
                    if(renum.test(datatotal[j][i].peroid4[k])) {
                        varname = "sumperoid4_" + k;
                        num = "sumtotal4_" + k;
                        window[varname] += parseInt(datatotal[j][i].peroid4[k]);
                        window[num].push(datatotal[j][i].peroid4[k].length)
                    }
                }
            }
        }
        if(sumtotal1_0.length == 0) {
            $('.peroid1_0' + code).html('0.00')
        } else {
            $('.peroid1_0' + code).html((sumperoid1_0 / sumtotal1_0.length).toFixed(2))
        }
        if(sumtotal1_1.length == 0) {
            $('.peroid1_1' + code).html('0.00')
        } else {
            $('.peroid1_1' + code).html((sumperoid1_1 / sumtotal1_1.length).toFixed(2))
        }
        if(sumtotal1_2.length == 0) {
            $('.peroid1_2' + code).html('0.00')
        } else {
            $('.peroid1_2' + code).html((sumperoid1_2 / sumtotal1_2.length).toFixed(2))
        }
        if(sumtotal1_3.length == 0) {
            $('.peroid1_3' + code).html('0.00')
        } else {
            $('.peroid1_3' + code).html((sumperoid1_3 / sumtotal1_3.length).toFixed(2))
        }
        if(sumtotal2_0.length == 0) {
            $('.peroid2_0' + code).html('0.00')
        } else {
            $('.peroid2_0' + code).html((sumperoid2_0 / sumtotal2_0.length).toFixed(2))
        }
        if(sumtotal2_1.length == 0) {
            $('.peroid2_1' + code).html('0.00')
        } else {
            $('.peroid2_1' + code).html((sumperoid2_1 / sumtotal2_1.length).toFixed(2))
        }
        if(sumtotal2_2.length == 0) {
            $('.peroid2_2' + code).html('0.00')
        } else {
            $('.peroid2_2' + code).html((sumperoid2_2 / sumtotal2_2.length).toFixed(2))
        }
        if(sumtotal2_3.length == 0) {
            $('.peroid2_3' + code).html('0.00')
        } else {
            $('.peroid2_3' + code).html((sumperoid2_3 / sumtotal2_3.length).toFixed(2))
        }
        if(sumtotal3_0.length == 0) {
            $('.peroid3_0' + code).html('0.00')
        } else {
            $('.peroid3_0' + code).html((sumperoid3_0 / sumtotal3_0.length).toFixed(2))
        }
        if(sumtotal3_1.length == 0) {
            $('.peroid3_1' + code).html('0.00')
        } else {
            $('.peroid3_1' + code).html((sumperoid3_1 / sumtotal3_1.length).toFixed(2))
        }
        if(sumtotal3_2.length == 0) {
            $('.peroid3_2' + code).html('0.00')
        } else {
            $('.peroid3_2' + code).html((sumperoid3_2 / sumtotal3_2.length).toFixed(2))
        }
        if(sumtotal3_3.length == 0) {
            $('.peroid3_3' + code).html('0.00')
        } else {
            $('.peroid3_3' + code).html((sumperoid3_3 / sumtotal3_3.length).toFixed(2))
        }
        if(sumtotal4_0.length == 0) {
            $('.peroid4_0' + code).html('0.00')
        } else {
            $('.peroid4_0' + code).html((sumperoid4_0 / sumtotal4_0.length).toFixed(2))
        }
        if(sumtotal4_1.length == 0) {
            $('.peroid4_1' + code).html('0.00')
        } else {
            $('.peroid4_1' + code).html((sumperoid4_1 / sumtotal4_1.length).toFixed(2))
        }
        if(sumtotal4_2.length == 0) {
            $('.peroid4_2' + code).html('0.00')
        } else {
            $('.peroid4_2' + code).html((sumperoid4_2 / sumtotal4_2.length).toFixed(2))
        }
        if(sumtotal4_3.length == 0) {
            $('.peroid4_3' + code).html('0.00')
        } else {
            $('.peroid4_3' + code).html((sumperoid4_3 / sumtotal4_3.length).toFixed(2))
        }
        if($('#che' + code).is(':checked')) {
            colorcode(code)
        }
    }
    function resetPopups(obj, index, text, code) {
        var str = ''
        for(var i = 0; i < datalists.data.length; i++) {
            if(code == datalists.data[i].code) {
                if(datalists.data[i].tips != 0) {
                    if(index == 'A') {
                        str = datalists.data[i].tips.A.tip
                    }
                    if(index == 'B') {
                        str = datalists.data[i].tips.B.tip
                    }
                    if(index == 'C') {
                        str = datalists.data[i].tips.C.tip
                    }
                    if(index == 'D') {
                        str = datalists.data[i].tips.D.tip
                    }
                    if(index == 0) {
                        for(key in datalists.data[i].tips.A.items) {
                            if(text == key) {
                                str = datalists.data[i].tips.A.items[key]
                            }
                        }
                    }
                    if(index == 1) {
                        for(key in datalists.data[i].tips.B.items) {
                            if(text == key) {
                                str = datalists.data[i].tips.B.items[key]
                            }
                        }
                    }
                    if(index == 2) {
                        for(key in datalists.data[i].tips.C.items) {
                            if(text == key) {
                                str = datalists.data[i].tips.C.items[key]
                            }
                        }
                    }
                    if(index == 3) {
                        for(key in datalists.data[i].tips.D.items) {
                            if(text == key) {
                                str = datalists.data[i].tips.D.items[key]
                            }
                        }
                    }
                }
            }
        }
        $(obj).popover({content: str, trigger: 'hover', html: true,placement: 'bottom',container: 'body'}).popover('show');
    }
</script>
<?php
$this->branchSelectParams['extraUrlArray'] = array('//mteaching/reportanalytic/index');
$this->renderPartial('//layouts/common/branchSelectBottom');
?>