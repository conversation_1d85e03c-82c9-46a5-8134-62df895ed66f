<div class="parent-<?php echo $role;?>" id="pid-<?php echo $model->uid;?>">
	<table width="100%">
		<colgroup>
			<col width="160">
		</colgroup>
		<tbody>
			<tr>
				<th>
					<?php echo CHtml::activeLabelEx($model,'level'); ?>
				</th>
				<td>
                    <div>
					<?php
						echo CHtml::activeCheckBox($model,'level',array('name'=>"User[$role][level]", "onClick"=>"toggleUserInput('".$role."',this)")) ?>
						
					<?php if ( $model->isNewRecord ):?>
                     <?php echo Yii::t("child","Create a parent account");?>
                    <?php else:?>
                    <?php echo Yii::t("child","Deselect to disable account")?></div>
                    <div>
                        <?php echo CHtml::ajaxLink('发送帐号信息给家长', $this->createUrl('/child/profile/processAccountToParent', array('uid'=>$model->uid)), array('type'=>'post', 'dataType'=>'json', 'success'=>'js:function(data){alert(data.message);$(this).parent().removeClass("tips_loading");$(this).show();}', 'context'=>'js:this', 'beforeSend'=>'js:function(){$(this).parent().addClass("tips_loading");$(this).hide();}'), array('class'=>'btn', 'confirm'=>Yii::t("child", '确定发送？')));?>&nbsp;
                    </div>
                    <?php endif;?>
				</td>
			</tr>
	
			<tr>
				<th>
					<?php echo CHtml::activeLabelEx($model,'email'); ?>
				</th>
				<td>
					<?php echo Yii::t("child","Input mobile phone number if parent has no email.");?><br/>
					<?php echo CHtml::activeTextField($model,'email',array('name'=>"User[$role][email]", 'class'=>'input length_4')) ?>
					<?php echo CHtml::error($model, 'email');?>
				</td>
			</tr>
			
			
			<?php $passKey = $model->isNewRecord ? 'iniPassword' : 'changePassword'; ?>
			<tr>
				<th>
					<?php echo CHtml::activeLabelEx($model,$passKey); ?>
				</th>
				<td>
					<?php if($model->isNewRecord):?>
					<span><?php echo Yii::t("child","Use auto generate.");?></span>
					<?php else:?>
					<span><?php echo Yii::t("child","Leave blank");//密码修改之后会自动向用户发送新密码。?></span>
					<?php endif;?><br/>
					
					<?php echo CHtml::activeTextField($model,$passKey,array('name'=>"User[$role][$passKey]", 'class'=>'input')) ?>
					<?php echo CHtml::link(Yii::t('child','Generate'),'javascript:void(0)',array('class'=>'setPass btn', 'textTarget'=>CHtml::getIdByName('User['.$role.']['.$passKey.']')));?>
					<?php echo CHtml::error($model, $passKey);?>
				</td>
			</tr>
		</tbody>
	</table>
</div>

<?php
if(!$model->level):
?>
<script>
toggleUserInput('<?php echo $role?>','#<?php echo CHtml::getIdByName('User['.$role.'][level]')?>');
</script>
<?php
endif;
?>