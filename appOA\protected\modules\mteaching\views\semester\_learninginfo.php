<div class="row">
    <div class="panel panel-default">
        <div class="panel-heading">
            <h3 class="panel-title"><?php echo Yii::t('curriculum','学习领域适用标准');?> - <span id="child-name"></span></h3>
        </div>
        <div class="panel-body">
            <span id="child-name"></span>
            <form class="form-inline mb5 J_ajaxForm" enctype="multipart/form-data" action="<?php echo $this->createUrl('childLearningTypeSave', array('classid' => $this->selectedClassId, 'semester' => $this->selectedSemester, 'childid' => $this->selectedChildId)); ?>">
                <div class="form-group">
                    <!-- <label for="learning_type">学习领域适用标准：</label> -->
                    <?php echo CHtml::dropDownList('learning_type', $learningDomainsType, $learningDomainsTypeList, array('class' => 'form-control', 'disabled' => $learningDomainsTypeFixed)); ?>
                </div>
                <button type="submit" class="btn btn-primary J_ajax_submit_btn" style="<?php echo $learningDomainsTypeFixed ? 'display:none' : '' ?>"><?php echo Yii::t('global', 'Save'); ?></button>
                <?php if ($learningDomainsTypeFixed): ?>
                    <a href="<?php echo $this->createUrl('resetLearningDomiansData', array('classid' => $this->selectedClassId, 'semester' => $this->selectedSemester, 'childid' => $this->selectedChildId)); ?>" class="btn btn-danger J_ajax_del" data-msg="<?= Yii::t('teaching', 'Sure to delete?'); ?>" type="button"><?= Yii::t('teaching', 'Clear Data'); ?></a>
                <?php endif; ?>
            </form>
        </div>
    </div>
    <?php foreach ($learningModels as $i => $model) :
        $flag = 1;  // 0 为可选两张照片，1为可选一张
        if ($branchGroup == 20 || $this->branchId == 'BJ_QFF') {
            if ($model->special_type == 1) {
                $flag = 0;
            }
            if ($branchId == "BJ_OE" && in_array($classtype, array('n', 'b'))) {
                if (in_array($model->special_type, array(1, 2))) {
                    continue;
                }
            } else {
                if ($model->special_type == 3) {
                    continue;
                }
            }
        } else {
            if ($branchId == "BJ_IASLT" && $classtype == "k") {
                if ($model->special_type == 3) {
                    continue;
                }
                if ($model->special_type == 1) {
                    $flag = 0;
                }
            } else {
                if (in_array($model->special_type, array(1, 2))) {
                    continue;
                }
            }
        }
    ?>
        <div class="panel panel-default">
            <div class="panel-heading">
                <h3 class="panel-title"><?php echo CommonUtils::autoLang($model->title_cn, $model->title_en); ?></h3>
            </div>
            <div class="panel-body">
                <p><?php echo nl2br(CommonUtils::autoLang($model->intro_cn, $model->intro_en)); ?></p>
                <?php
                $form = $this->beginWidget('CActiveForm', array(
                    'id' => 'learning-form' . $model->id,
                    'action' => $this->createUrl(
                        'saveLearningInfo',
                        array(
                            'classid' => $this->selectedClassId,
                            'semester' => $this->selectedSemester,
                            'childid' => $this->selectedChildId
                        )
                    ),
                    'htmlOptions' => array('class' => 'J_ajaxForm', 'enctype' => 'multipart/form-data'),
                ));
                $formModel = isset($formModels[$model->id]) ? $formModels[$model->id] : new LearningSemester();
                ?>

                <?php $num = 1;
                if ($flag) : $num = 2; ?>
                    <div class="form-group col-md-12">
                        <div class="form-group col-md-8">
                            <img class="img-responsive" src="<?php echo isset($photosData[$formModel->intro_img]) ? $photosData[$formModel->intro_img] . $formModel->intro_img_cropper . '/thumbnail/2000x2000>'  : ''; ?>" style="<?php echo !$formModel->intro_img ? 'display: none;' : ''; ?>" id="<?php echo $model->id; ?>_intro_img">

                        </div>
                        <?php echo $form->hiddenField($formModel, 'intro_img', array('class' => 'form-control', 'data-url' => $photosData[$formModel->intro_img] . '!w600')); ?>
                        <?php echo $form->hiddenField($formModel, 'intro_img_cropper', array('class' => 'form-control')); ?>
                    </div>
                <?php endif; ?>
                <div class="form-group col-md-12">
                    <div class="form-group col-md-8">
                        <img class="img-responsive" src="<?php echo isset($photosData[$formModel->items_img]) ? $photosData[$formModel->items_img] . $formModel->items_img_cropper . '/thumbnail/2000x2000>' : ''; ?>" style="<?php echo !$formModel->items_img ? 'display: none;' : ''; ?>" id="<?php echo $model->id; ?>_items_img">
                    </div>
                    <!-- <div class="photo-select col-md-12">
                        <button type="button" class="btn btn-default" onclick="selectPhoto('<?php echo $model->id; ?>', 'items_img', '<?php echo $cropperRatio[$i][1]; ?>');">
                            <span class="glyphicon glyphicon-picture"></span> <?php echo Yii::t('teaching', 'Select Photo'); ?>
                        </button>
                    </div> -->
                    <div class="photo-select col-md-12">
                        <button type="button" class="btn btn-default" onclick="selectPhoto('<?php echo $model->id; ?>', <?php echo $num; ?>, '<?php echo $cropperRatio[$i][0]; ?>', '<?php echo $cropperRatio[$i][1]; ?>');">
                            <span class="glyphicon glyphicon-picture"></span> <?php echo Yii::t('teaching', 'Select Photo'); ?>
                        </button>
                    </div>

                    <?php echo $form->hiddenField($formModel, 'items_img', array('class' => 'form-control', 'data-url' => $photosData[$formModel->items_img] . '!w600')); ?>
                    <?php echo $form->hiddenField($formModel, 'items_img_cropper', array('class' => 'form-control')); ?>
                </div>
                <?php if ($branchGroup != 30): ?>
                    <div class="form-group col-md-12">
                        <?php echo $form->labelEx($formModel, 'proof_text', array('class' => '')); ?>
                        <?php echo $form->textArea($formModel, 'proof_text', array('class' => 'form-control')); ?>
                    </div>
                <?php endif; ?>
                <div class="form-group col-md-12">
                    <table class="table table-bordered">
                        <tr>
                            <th><?php echo $model->keyMilestones(); ?></th>
                            <?php
                            foreach ($optionTitle as $key => $value) {
                                echo '<th style="width: 200px;">' . $value . '</th>';
                            }
                            ?>
                        </tr>
                        <?php
                        foreach ($model->items as $item) :
                            $option = '';
                            if (isset($optionData[$model->id])) {
                                if (isset($optionData[$model->id][$item->id])) {
                                    $option = $optionData[$model->id][$item->id];
                                }
                            }
                        ?>
                            <tr>
                                <td><?php echo CommonUtils::autoLang($item->title_cn, $item->title_en); ?></td>
                                <input type="hidden" name="options[<?php echo $item->id; ?>]" value="0">
                                <?php foreach ($optionTitle as $key => $value) : ?>
                                    <td>
                                        <div class="radio">
                                            <label>
                                                <input type="radio" class="options_<?php echo $item->id; ?>" name="options[<?php echo $item->id; ?>]" onclick="checkRadio(this)" value="<?php echo $key ?>" <?php if ($option == $key) echo 'checked = checked' ?>>
                                                <?php echo $value; ?>
                                            </label>
                                        </div>
                                    </td>
                                <?php endforeach ?>
                            </tr>
                        <?php endforeach; ?>
                    </table>
                </div>
                <input type="text" hidden name="id" value="<?php echo $formModel->id; ?>">
                <input type="text" hidden name="LearningSemester[lid]" value="<?php echo $model->id; ?>">
                <button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global', 'Save'); ?></button>
                <?php $this->endWidget(); ?>
            </div>
        </div>
    <?php endforeach ?>
    <?php if ($branchGroup == 30): ?>
        <div class="panel panel-default">
            <div class="panel-heading">
                <h3 class="panel-title">领域总结</h3>
            </div>
            <div class="panel-body">
                <p>领域总结</p>
                <?php
                $form = $this->beginWidget('CActiveForm', array(
                    'id' => 'learning-form0',
                    'action' => $this->createUrl(
                        'saveLearningInfo',
                        array(
                            'classid' => $this->selectedClassId,
                            'semester' => $this->selectedSemester,
                            'childid' => $this->selectedChildId
                        )
                    ),
                    'htmlOptions' => array('class' => 'J_ajaxForm', 'enctype' => 'multipart/form-data'),
                ));
                $formModel = isset($formModels[0]) ? $formModels[0] : new LearningSemester();
                ?>

                <div class="form-group col-md-12">
                    <?php echo $form->textArea($formModel, 'proof_text', array('class' => 'form-control', 'rows' => '6')); ?>
                </div>
                <input type="text" hidden name="id" value="<?php echo $formModel->id; ?>">
                <input type="text" hidden name="LearningSemester[lid]" value="0">
                <button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global', 'Save'); ?></button>
                <?php $this->endWidget(); ?>
            </div>
        </div>
    <?php endif; ?>
</div>

<script>
    var cropper_intro_img;
    var cropper_items_img;
    var cropper_ratio_intro = 1;
    var cropper_ratio_item = 1;
    var tempId;
    var img;
    var tempradio = null;

    head.Util.ajaxDel();

    $('#child-name').text(currentChildData['name']);

    function checkRadio(checkedRadio) {
        if (tempradio == checkedRadio) {
            tempradio.checked = false;
            tempradio = null;
        } else {
            tempradio = checkedRadio;
        }
    }
    selectPhoto = function(id, num, ratio1, ratio2) {
        cropper_ratio_intro = ratio1;
        cropper_ratio_item = ratio2;
        if (cropper_intro_img) {
            var objName = '#' + id + '_intro_img';
            if (cropper_intro_img.getCroppedCanvas()) {
                $(objName).attr('src', cropper_intro_img.getCroppedCanvas().toDataURL('image/jpeg'));
            }
            cropper_intro_img.destroy();
        }
        if (cropper_items_img) {
            var objName = '#' + id + '_items_img';
            if (cropper_items_img.getCroppedCanvas()) {
                $(objName).attr('src', cropper_items_img.getCroppedCanvas().toDataURL('image/jpeg'));
            }
            cropper_items_img.destroy();
        }
        tempId = id;
        img = name;
        var id1 = $('#learning-form' + id + ' #LearningSemester_intro_img').val()
        var id2 = $('#learning-form' + id + ' #LearningSemester_items_img').val()
        var url1 = $('#learning-form' + id + ' #LearningSemester_intro_img').attr('data-url')
        var url2 = $('#learning-form' + id + ' #LearningSemester_items_img').attr('data-url')
        var datas = []
        if (num == 1) {
            if (url2 != '!w600') {
                datas.push({
                    id: id2,
                    url: url2
                })
            }
        } else {
            if (url1 != '!w600') {
                datas.push({
                    id: id1,
                    url: url1
                })
            }
            if (url2 != '!w600') {
                datas.push({
                    id: id2,
                    url: url2
                })
            }
        }
        _SM_selectPhotos(num, datas)
    };
    cbPhotoSelected = function(data) {
        var imgObjIntro = tempId + '_intro_img'
        var imgObjItems = tempId + '_items_img'
        var objIntro = $('#' + imgObjIntro)
        var objItems = $('#' + imgObjItems)

        objIntro.show();
        objItems.show();
        var i = 0;
        for (var i = 0; i < data.length; i++) {
            var newUrl;
            if (data[i].url.indexOf("!w600A") > 0 || data[i].url.indexOf("!ww600A") > 0) {
                newUrl = data[i].url.replace(/!w600A|!ww600A/g, "!A")
            } else if (data[i].url.indexOf("!w600B") > 0 || data[i].url.indexOf("!ww600B") > 0) {
                newUrl = data[i].url.replace(/!w600B|!ww600B/g, "!B")
            } else if (data[i].url.indexOf("!w600C") > 0 || data[i].url.indexOf("!ww600C") > 0) {
                newUrl = data[i].url.replace(/!w600C|!ww600C/g, "!C")
            } else {
                newUrl = data[i].url.replace(/!w600|!ww600/g, "")
            }
            if (data.length == 1) {
                objItems.attr('src', newUrl + '?imageMogr/auto-orient')
                var inputId = '#learning-form' + tempId + ' #LearningSemester_items_img';
                $(inputId).attr('data-url', data[i].url)
                $(inputId).val(data[i].id)
            } else {
                if (i == 0) {
                    objIntro.attr('src', newUrl + '?imageMogr/auto-orient')
                    var inputId = '#learning-form' + tempId + ' #LearningSemester_intro_img';
                    $(inputId).val(data[i].id)
                    $(inputId).attr('data-url', data[i].url)

                }
                if (i == 1) {
                    objItems.attr('src', newUrl + '?imageMogr/auto-orient')
                    var inputId = '#learning-form' + tempId + ' #LearningSemester_items_img';
                    $(inputId).attr('data-url', data[i].url)
                    $(inputId).val(data[i].id)
                }
            }
        }
        // _.each(data, function(imgUrl, imgId) {
        //     console.log(imgUrl)
        //     console.log(imgId)
        //     var newUrl;
        //     if (imgUrl.indexOf("!w600A") > 0) {
        //         newUrl = imgUrl.replace(/!w600A/, "!A")
        //     } else if (imgUrl.indexOf("!w600B") > 0) {
        //         newUrl = imgUrl.replace(/!w600B/, "!B")
        //     } else if (imgUrl.indexOf("!w600C") > 0) {
        //         newUrl = imgUrl.replace(/!w600C/, "!C")
        //     } else {
        //         newUrl = imgUrl.replace(/!w600/, "")
        //     }
        //     if (i == 0) {
        //         objIntro.attr('src', newUrl + '?imageMogr/auto-orient')
        //         var inputId = '#learning-form' + tempId + ' #LearningSemester_intro_img';
        //         $(inputId).val(imgId)
        //     }
        //     if (i == 1) {
        //         objItems.attr('src', newUrl + '?imageMogr/auto-orient')
        //         var inputId = '#learning-form' + tempId + ' #LearningSemester_items_img';
        //         $(inputId).val(imgId)
        //     }
        //     i++;
        // })


        if (data.length > 1) {
            cropper_intro_img = new Cropper(objIntro[0], {
                aspectRatio: cropper_ratio_intro,
                cropBoxResizable: false,
                viewMode: 1,
                autoCropArea: 0.999,
                zoomable: false,
                rotatable: false,
                preview: '',
                crop: function(event) {
                    var cropper_data = '?imageMogr2/imageMogr/auto-orient/crop/!' + event.detail.width + 'x' + event.detail.height + 'a' + event.detail.x + 'a' + event.detail.y;
                    var inputId = '#learning-form' + tempId + ' #LearningSemester_intro_img_cropper';
                    $(inputId).val(cropper_data);
                },
            });
        }
        cropper_items_img = new Cropper(objItems[0], {
            aspectRatio: cropper_ratio_item,
            cropBoxResizable: false,
            viewMode: 1,
            autoCropArea: 0.999,
            zoomable: false,
            rotatable: false,
            preview: '',
            crop: function(event) {
                var cropper_data = '?imageMogr2/imageMogr/auto-orient/crop/!' + event.detail.width + 'x' + event.detail.height + 'a' + event.detail.x + 'a' + event.detail.y;
                var inputId = '#learning-form' + tempId + ' #LearningSemester_items_img_cropper';
                $(inputId).val(cropper_data);
            },
        });

        $('#_SM_selectPhotos').modal('hide');
    };
    cbSave = function(data) {
        if (data.state = 'success') {
            var id = data.id;
            if (cropper_intro_img) {
                if (cropper_intro_img.getCroppedCanvas()) {
                    var objName = '#' + id + '_intro_img';
                    var objSrc = cropper_intro_img.getCroppedCanvas().toDataURL('image/jpeg');
                    $(objName).attr('src', objSrc);
                }
                cropper_intro_img.destroy();
            }
            if (cropper_items_img) {
                if (cropper_items_img.getCroppedCanvas()) {
                    var objName = '#' + id + '_items_img';
                    var objSrc = cropper_items_img.getCroppedCanvas().toDataURL('image/jpeg');
                    $(objName).attr('src', objSrc);
                }
                cropper_items_img.destroy();
            }
        }
    }

    resetLearningData = function(ele) {
        if (confirm("确定清除所有学习领域数据")) {
            $.ajax();
        }
    }
</script>