<?php

class LoginController extends Controller {

    public $defaultAction = 'login';
    public $layout = '//layouts/auth.login';

    /**
     * Displays the login page
     */
    public function actionLogin() {
        
		$this->setSubPageTitle(Yii::t("user","Login"));
        if (Yii::app()->user->isGuest) {
            $model = new UserLogin;
            // collect user input data
            if (isset($_POST['UserLogin'])) {
                $model->attributes = $_POST['UserLogin'];
				if(isset( $_POST['UserLogin']['lang']) ){
					$lang = $_POST['UserLogin']['lang'];
					$lang = Mims::setLangCookie($lang);
					Yii::app()->language = $lang;
				}
                // validate user input and redirect to previous page if valid
                if ($model->validate()) {
                    $this->lastLogin();
                    //if (strpos(Yii::app()->user->returnUrl,'/index.php')!==false){
                        if (count(Yii::app()->user->getAdminCids()) > 1){
                            if (strpos(Yii::app()->user->returnUrl, 'scholarship')!==false){
                                $this->redirect(Yii::app()->user->getReturnUrl());
                            } else {
                                $this->redirect(Yii::app()->controller->module->selectUrl);
                            }
                    }
                    else{
//                        $this->redirect(Yii::app()->controller->module->returnUrl);
                        $this->redirect(Yii::app()->user->getReturnUrl());
                    }
                    //}
                    //else
                    //$this->redirect(Yii::app()->user->returnUrl);
                }elseif (Yii::app()->params['oaRedirect'] === true && $model->redirectToOA) {
                    $this->layout = "//layouts/auth.blank";
                    $this->render('/user/oaportal', array("model" => $model));
                    Yii::app()->end();
                }
            }

            // display the login form
            $this->render('/user/login', array('model' => $model ));
        } else {
            $this->redirect(Yii::app()->controller->module->returnUrl);
        }
    }

    public function actionAjaxLogin()
    {
        if( Yii::app()->request->isAjaxRequest ){
            if (isset($_POST['UserLogin'])) {
                $checkUname = Yii::app()->request->getParam('checkUname', 0);
                $model = new UserLogin;
                $model->attributes = $_POST['UserLogin'];
                if(isset( $_POST['UserLogin']['lang']) ){
                    $lang = $_POST['UserLogin']['lang'];
                    $lang = Mims::setLangCookie($lang);
                    Yii::app()->language = $lang;
                }
                // validate user input and redirect to previous page if valid
                if ($model->validate()) {
                    $this->lastLogin();
                    if ( $checkUname ){
                        $exist = $this->checkUname();
                        if (!$exist)
                            echo CJSON::encode(array('state'=>'pass', 'msg'=>'已经登录成功，首次评论请设定您的显示名。'));
                        else
                            echo CJSON::encode(array('state'=>'success'));
                    }
                    else{
                        echo CJSON::encode(array('state'=>'success'));
                    }
                }
                else{
                    echo CJSON::encode(array('state'=>'fail', 'msg'=>$model->getErrors()));
                }
            }
        }
    }

    private function lastLogin() {
        $lastLogin = User::model()->findByPk(Yii::app()->user->id);
        $lastLogin->last_login = time();
        $lastLogin->save();
    }

    private function checkUname()
    {
        Yii::import('common.models.club.ClubUsers');
        $attrs = array('uid'=>Yii::app()->user->id);
        $count = ClubUsers::model()->countByAttributes($attrs);
        return $count;
    }

    public function actionUname()
    {
        Yii::import('common.models.club.ClubUsers');
        $uname = Yii::app()->request->getParam('uname', '');
        $uname = trim($uname);
        $model = ClubUsers::model()->findByPk(Yii::app()->user->id);
        if ($model === null)
            $model = new ClubUsers();
        $model->uid = Yii::app()->user->id;
        $model->uname = $uname;
        if($model->save()){
            Yii::app()->user->setState('clubUsername', $model->uname);
            echo CJSON::encode(array('state'=>'success'));
        }
        else{
            $err = current($model->getErrors());
            echo CJSON::encode(array('state'=>'fail', 'msg'=>$err?$err[0]:'保存失败！'));
        }
    }
}