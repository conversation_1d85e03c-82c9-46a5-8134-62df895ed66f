<?php

class VendorController extends BranchBasedController
{
    public function createUrl($route, $params = array(), $ampersand = '&', $parentOnly = false)
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        return parent::createUrl($route, $params, $ampersand);
    }

    public function init(){
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'campusOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site','Campus Workspace');

        $this->branchSelectParams['urlArray'] = array('//masa/vendor/index');

        // jquery ui
        $cs = Yii::app()->clientScript;
        $cs->registerCoreScript('jquery.ui');
        $cs->registerCssFile($cs->getCoreScriptUrl().'/jui/css/base/jquery-ui.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/vue2.js');
    }

	public function actionIndex()
    {
        $teacherType = Yii::app()->request->getParam('teacherType');
        $feeMerge = Yii::app()->request->getParam('feeMerge');
        //获取添加的老师
        $critre = new CDbCriteria;
        $critre->compare('site_id', $this->branchId);
        if ($teacherType) {
            $critre->compare('type', $teacherType);
        }
        if (in_array($feeMerge, array(0, 1))) {
            $critre->compare('fee_merge', $feeMerge);
        }
        $teacherModel = new CActiveDataProvider('AsaVendor', array(
            'criteria' => $critre,
            'sort' => array(
                'defaultOrder'=>'active DESC,updated DESC',
            ),
            'pagination'=>array(
                'pageSize'=>50,
            ),
        ));

        $this->render('index',array(
            'teacherModel' => $teacherModel,
        ));
	}

    // 增加员工和机构
    public function actionUpdateTeacher($vendor_id = 0)
    {
        Yii::import('common.models.staff.*');
        Yii::import('common.models.asainvoice.*');

        $teacherId = intval(Yii::app()->request->getParam('teacherId', 0)); //老师id
        $teacherPhone = Yii::app()->request->getParam('teacherPhone', '');  //联系方式
        $teacherEmail = Yii::app()->request->getParam('teacherEmail', '');  //老师职位
        $teacherType = Yii::app()->request->getParam('teacherType', '');    //
        $active = Yii::app()->request->getParam('active', '');              //老师职老师来源位(主班/助教)
        $name_cn = Yii::app()->request->getParam('name_cn', '');
        $name_en = Yii::app()->request->getParam('name_en', '');
        $fee_merge = Yii::app()->request->getParam('fee_merge_' . $teacherType, 0);

        if ($fee_merge == 1) {
            $fee_merge = 1;
        } else {
            $fee_merge = 0;
        }
        
        //获取当前学校所有未离职的员工
        $criteria = new CDbCriteria;
        $criteria->compare('t.level', 1);
        $criteria->compare('t.isstaff', 1);
        $criteria->compare('profile.branch', $this->branchId);
        $criteria->with = 'profile';       //调用profile
        $criteria->index = 'uid';
        $teacherNameList = User::model()->findAll($criteria);

        //循环未离职员工
        $teacherlist = array();
        foreach($teacherNameList as $teacher){
            $teacherlist[]=array(
                'value' => $teacher->getName(),
                'teacherPhone'=>$teacher->staff->mobile_telephone,
                'teacherId'=>$teacher->uid,
                'teacherEmail'=>$teacher->email,
            );
        }

        //获取模态切换按钮数据
        $typeList = CommonUtils::LoadConfig('CfgASA');

        $model = AsaVendor::model()->findByPk($vendor_id);
        if (!$model) {
            $model = new AsaVendor;
            $model->active = 1;
        }

        if (Yii::app()->request->isPostRequest){
            if($teacherId){
                $teacherName = User::model()->findByPk($teacherId);
                $enName = $teacherName->profile->first_name . " " . $teacherName->profile->last_name;
                $enName = trim($enName);

                $model->name_cn = ($teacherName->name) ? $teacherName->name : $enName;
                $model->name_en = ($enName) ? $enName : $teacherName->name;
            }else{
                $model->name_cn =  $name_cn;
                $model->name_en =  $name_en;
            }

            $criteria = new CDbCriteria;
            $criteria->compare('type', $teacherType);
            $criteria->compare('site_id', $this->branchId);
            if(isset($model->vendor_id)){
                $criteria->compare('vendor_id', "<>{$model->vendor_id}");
            }
            $criteria->compare('cellphone', $teacherPhone);
            $phonecount = AsaVendor::model()->count($criteria);
            if($phonecount){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('message', "此手机号码已添加,请重新填写手机号码"));
                $this->showMessage();
            }

            if(!preg_match("/^1[3456789v]{1}\d{9}$/",$teacherPhone)){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('message', "请填写正确的手机号码"));
                $this->showMessage();
            }

            $model->site_id = $this->branchId;
            $model->ivy_uid = $teacherId;
            $model->cellphone = $teacherPhone;
            $model->email = $teacherEmail;
            $model->type = $teacherType;
            $model->active = $active;
            $model->fee_merge = $fee_merge;
            $model->updated = time();
            $model->updated_by = $this->staff->uid;
            if ($model->save()) {
                if($model->type == 1){
                    $teacherStaff = Staff::model()->findByPk($teacherId);
                    $teacherStaff->mobile_telephone = $teacherPhone;
                    if(!$teacherStaff->save()){
                        $error = current($model->getErrors());
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', Yii::t('message', $error[0]));
                    }
                }
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message','success'));
                $this->addMessage('callback', 'cbTeacher');
            }else{
                $error = current($model->getErrors());
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('message', $error[0]));
            }
            $this->showMessage();
        }
        $this->renderPartial('updateTeacher', array(
            'model' => $model,
            'typeList'=>$typeList,
            'teacherList'=>$teacherlist,
        ));
    }

    public function actionupdateBankInfo($vendor_id = 0){
        Yii::import('common.models.asainvoice.*');

        $typeList = CommonUtils::LoadConfig('CfgASA');
        //通过vendor_id获取老师信息
        $vendorModel = AsaVendor::model()->findByPk($vendor_id);
        //查看银行信息是否已经存在
        $model = AsaVendorInfo::model()->findByPk($vendor_id);
        if (!$model) {
            $model = new AsaVendorInfo;
            if ($vendorModel) {
                Yii::import('common.models.staff.*');
                $staff = Staff::model()->findByPk($vendorModel->ivy_uid);
                if ($staff) {
                    $model->identify = $staff->card_id;
                }
            }
        }


        $identify = Yii::t('asa', 'Identity Number');
        if($vendorModel->type == 2){
            $identify = Yii::t('asa', '统一信用代码(税号)');
        }

        //获取银行信息
        $bank=$typeList['bank'];

        if (Yii::app()->request->isPostRequest){
            $model->attributes = $_POST['AsaVendorInfo'];
            $model->vendor_id = $vendor_id;
            $model->bank = Yii::app()->request->getParam('bank','');
            if(!$model->identify){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', $identify . "不能为空");
                $this->showMessage();
            }
            if ($model->save()) {
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message','success'));
                $this->addMessage('callback', 'cbTeacher');
                $this->showMessage();
            }else{
                $error = current($model->getErrors());
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('message', $error[0]));
                $this->showMessage();
            }
        }


        $this->renderPartial('bank',array(
            'model' => $model,
            'bank'  => $bank,
            'vendorName'  => $vendorModel,
            'identify'  => $identify,
        ));
    }

    public function actionDeleteTeacher()
    {
        if (Yii::app()->request->isPostRequest) {
            $vendor_id = Yii::app()->request->getParam('vendor_id', 0);
            $model = AsaVendor::model()->findByPk($vendor_id);
            if (!$model) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('message', '该老师已分配课程！'));
                $this->showMessage();
            }

            //查看老师是否已分配课程
            $criteria = new CDbCriteria;
            $criteria->compare('vendor_id', $vendor_id);
            $coursestaff = AsaCourseStaff::model()->count($criteria);

            if (!$coursestaff) {
                if ($model->deleteByPk($vendor_id)) {
                    $this->addMessage('state', 'success');
                    $this->addMessage('callback', 'cbTeacher');
                    $this->addMessage('message', Yii::t('message', 'success'));
                }
            } else {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('message', '该老师已分配课程！'));
            }
            $this->showMessage();
        }
    }

    public function getBank($bank){
        $typeList = CommonUtils::LoadConfig('CfgASA');

        if($bank->vendorInfo){
            switch (Yii::app()->language) {
                case "zh_cn":
                    return $typeList['bank'][$bank->vendorInfo->bank]['cn'] .  ' ( ' . $bank->vendorInfo->bank_account . ' )' ;
                    break;
                case "en_us":
                    return $typeList['bank'][$bank->vendorInfo->bank]['en'] .  ' ( ' . $bank->vendorInfo->bank_account . ' )';
                    break;
            }
        }
    }

    public function getType($type){
        $typeList = CommonUtils::LoadConfig('CfgASA');
        if($type->type){
             switch (Yii::app()->language) {
                case "zh_cn":
                    return $typeList['type'][$type->type]['cn'];
                    break;
                case "en_us":
                    return $typeList['bank'][$type->type]['en'];
                    break;
            }
        }

    }

    public function getButton($data)
    {
        echo CHtml::link('付款信息', array('updateBankInfo', 'vendor_id' => $data->vendor_id), array('class' => 'J_modal btn btn-xs btn-info')) . ' ';
        echo CHtml::link('编辑', array('updateTeacher', 'vendor_id' => $data->vendor_id), array('class' => 'J_modal btn btn-xs btn-info')) . ' ';
        echo CHtml::link('删除', array('deleteTeacher', 'vendor_id' => $data->vendor_id), array('class' => 'J_ajax_del btn btn-xs btn-danger'));
    }


}
