<?php
/**
 * Message translations.
 *
 * This file is automatically generated by 'yiic message' command.
 * It contains the localizable messages extracted from source code.
 * You may modify this file by translating the extracted messages.
 *
 * Each array element represents the translation (value) of a message (key).
 * If the value is empty, the message is considered as not translated.
 * Messages that no longer need translation will have their translations
 * enclosed between a pair of '@@' marks.
 *
 * Message string can be used with plural forms format. Check i18n section
 * of the guide for details.
 *
 * NOTE, this file must be saved in UTF-8 encoding.
 *
 * @version $Id: $
 */
return array(
    'Add Child' => '注册孩子',
    'All Classes' => '分班记录',
    'Are you sure to assign class?' => '',
    'Assign' => '分班',
    'Balance' => '余额信息',
    'Calendar' => '',
    'Campus' => '',
    'Change Status' => '改变状态',
    'Change Status?' => '修改状态？',
    'Class' => '班级',
    'Class Assignments' => '分班情况',
    'Create a parent account' => '创建家长帐号',
    'Current School Year (:startYear - :endYear)' => '当前学年（:startYear - :endYear）',
    'Current Status' => '当前状态',
    'Deselect to disable account' => '取消选中将不能登录',
    'Fapiao' => '发票助手',
    'Father' => '父亲信息',
    'Father\'s Account' => '父亲账号',
    'Finance' => '财务信息',
    'Generate' => '',
    'Input mobile phone number if parent has no email.' => '没有电邮请使用手机号码',
    'Invoices' => '账单信息',
    'Leave blank' => '不修改请留空',
    'Lunch Cancellation' => '午餐取消记录',
    'Mother' => '母亲信息',
    'Mother\'s Account' => '母亲账号',
    'Name' => '姓名',
    'Next School Year (:startYear - :endYear)' => '下学年（:startYear - :endYear）',
    'No class of next year found!' => '',
    'Personal Credit' => '个人账户明细',
    //'Credit' => '个户余额',
    'Please :assignClassLink first before creating invoices.' => '请先 :assignClassLink 再开账单。',
    'assign class' => '分班',
    'Re-assign' => '重新分班',
    'No class found, :createLink' => '没有找到班级，:createLink',
    'Create Class?' => '创建班级？',
    'Unauthorized operation, please contact IT Dept.' => '',
    'Updated Date' => '',
    'Use auto generate.' => '',
    'View Invoice Information' => '账单状态浏览',
    '个人余额' => '',
    'DOB' => '出生日期',
    'Basic Profile' => '信息资料',
    'Parent Account' => '家长帐号',
    'Create Invoice' => '开账单',
    'Status & Class' => '状态及分班',
    'Cancel Lunch' => '取消午餐',
    '分班' => '',
    '学年信息' => '',
    '孩子资料' => '',
    '家庭住址' => '',
    'Parent Info' => '家长信息',
    'Child Info' => '孩子信息',
    '性别' => '',
    '所选孩子中有未付账单未处理，请处理之后再试。' => '',
    'Active' => '有效',
    'Miscellaneous' => '杂项信息',
    '毕业' => '',
    '注册' => '',
    '注册孩子' => '',
    'Dropped Out' => '已退学',
    'Dropping Out' => '退学处理中',
    'Estimated Leave Date' => '预计退学时间',
    'Please fill in the estimated leave date' => '请输入预计退学的时间',
    'Graduated' => '已毕业',
    'Newly Registered' => '新注册',
    '班级名单' => '',
    '确定操作？' => '',
    'Select at least one child' => '至少选择一个孩子',
    'Cancel Discount' => '取消折扣绑定',
    '请先在家长帐号中创建账户' => '',
    '请逐班处理' => '',
    '转校' => '',
    '退学' => '',
    '退学处理中' => '',
    'Female' => '女',
    'Male' => '男',
    'Create a parent account from :parentLink' => '请先在 :parentLink 中创建家长帐号',

    'Boy' => '男',
    'Girl' => '女',
    'CN Mainland' => '中国大陆',
    'CN Other' => '港澳台',
    'Foreigner' => '外籍',
    'Email' => '邮箱',
    'Language' => '沟通语言',
    '%s only' => '仅限 %s',
    'Breakdown of Next Year Student' => '下学年学生来源统计',
    'Clicking a number will highlight corresponding students (scroll down to student list)' => '点击数字高亮显示学生（下滚至列表查看）',
    'This table is generated based on class placement and tuition/deposit for the next year.' => '本数据根据下学年分班状态及学费账单/定位金生成',
    'Rules are as follows: ' => '规则如下：',
    'New Students: ' => '新生：',
    'No tuition billing records at this school in the current academic year.' => '当前学年本校无学费账单',
    'Transfer Students: ' => '转校生：',
    'Currently enrolled in another school.' => '当前就读学校为其它学校',
    'Returning Students: ' => '返校生：',
    'Currently enrolled at this school.' => '当前就读学校为本校',
    'Tuition & Deposit Unpaid: ' => '定位金和学费均未支付：',
    'Deposit Paid, Tuition NOT Paid: ' => '只支付定位金，未支付学费：',
    'None of Deposit and Tuition Paid: ' => '定位金及学费均未支付：'
);
