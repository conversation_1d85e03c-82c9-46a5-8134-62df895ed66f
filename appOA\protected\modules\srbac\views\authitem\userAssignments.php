<h4><?php echo $user->getName()?> <small><?php echo $user->email;?></small></h4>

<div class="btn-group mb15">
    <button type="button" class="btn btn-success dropdown-toggle" data-toggle="dropdown">添加更多 <span class="caret"></span></button>
    <ul class="dropdown-menu" role="menu" id="add-item">
        <li><a href="#" onclick="addAuth('role')">角色 (Role)</a></li>
        <li><a href="#" onclick="addAuth('operation')">操作 (Operation)</a></li>
    </ul>
</div>

<div class="clearfix"></div>

<div class="J_thebox hidden" id="add-role-box">
    <div class="panel panel-primary">
        <div class="panel-body">
            <div class="form-group">
                <label>Roles</label>
                <?php
                $_roles = array();
                foreach($userNotAssignedRoles as $role){
                    $_roles[$role->name] = $role->name.' '.$role->description;
                }
                echo CHtml::activeDropDownList($model,'name[assign]', $_roles,
                array('size'=>$this->module->listBoxNumberOfLines,'multiple'=>'multiple','class'=>'form-control'));
                ?>
            </div>
            <button type="submit" class="btn btn-default J_ajax_submit_btn">Submit</button>
        </div>
    </div>
</div>

<div class="J_thebox hidden" id="add-operation-box">
    <?php $this->renderPartial('tabViews/_operations', array('taskData'=>$datat, 'descriptions'=>$descriptions));?>
</div>

<div class="clearfix"></div>

<script>var auths=[];</script>
<?php foreach ($data as $i=>$roles) { ?>
    <script>auths.push('<?php echo $i;?>');</script>
    <div class="col-md-4">
        <div class="panel panel-info">
            <div class="panel-heading"><?php echo $descriptions[$i].' '.$i; ?>
                <span class="pull-right">
                    <a class="btn btn-danger btn-xs J_ajax_del" title="删除" href="<?php echo $this->createUrl('assign', array('revokeRoles'=>1,'User[uid]'=>$user->uid,'AuthItem[name][revoke][]'=>$i));?>"><i class="glyphicon glyphicon-remove"></i></a>
                </span>
            </div>
            <?php if($roles):?>
            <div class="panel-body">
                <?php foreach ($roles as $j=>$tasks) { ?>
                    <script>auths.push('<?php echo $j;?>');</script>
                    <div class="col-md-12">
                        <div class="panel panel-default">
                            <div class="panel-heading">
                                <?php echo $descriptions[$j].' '.$j; ?>
                            </div>
                            <?php if($tasks):?>
                            <div class="panel-body">
                                <?php foreach ($tasks as $j=>$opers) { ?>
                                    <script>auths.push('<?php echo $opers;?>');</script>
                                    <span class="label label-default"><?php echo $descriptions[$opers].' '.$opers;  ?></span>
                                <?php } ?>
                            </div>
                            <?php endif;?>
                        </div>
                    </div>
                <?php }?>
            </div>
            <?php endif;?>
        </div>
    </div>
<?php } ?>

<script>
    function addAuth(auth){
        $('.J_thebox').addClass('hidden').find('input,select').attr('disabled', true);
        $('#add-'+auth+'-box').removeClass('hidden').find('input,select').attr('disabled', false);
        if(auth == 'operation'){
            var separator = ',';
            var authsStr = auths.join(separator)+separator;
            $('#add-operation-box input[type="checkbox"]').each(function(){
                if( authsStr.indexOf($(this).val()+separator) > -1 ){
                    $(this).attr('checked', true).attr('disabled', true);
                }
            });
        }
    }
</script>