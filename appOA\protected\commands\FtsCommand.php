<?php

/**
 * Full Text Search: Child and Staff
 */

class FtsCommand extends CConsoleCommand {

	public $abbs;

	public function init(){
		Yii::setPathOfAlias('common', dirname(__FILE__) . '/../../../common');
		Yii::import('common.models.*');
		Yii::import('common.models.child.*');
		Yii::import('common.models.staff.*');
		
        $criteria = new CDbCriteria;
        $criteria->compare('type', '>0');
        $criteria->index = 'abb';
        $models = Branch::model()->findAll($criteria);
        $this->abbs = CHtml::listData($models, 'branchid', 'abb');
	}
	public $batchNum = 200;
    
    public function dataHandle($child, $parents)
    {
		$abbs = $this->abbs;

		if(empty($child->schoolid) || !isset($abbs[$child->schoolid])) return false;

        
        $dataArr = array();
        $dataArr['_SCHOOL_'] = $abbs[$child->schoolid];
        $dataArr['_SCHOOL_'] .= ' ('.$child->childid.')';
        $cname = trim($child->name_cn);
        $nick = trim($child->nick);
        $ename = trim(sprintf("%s %s", $child->first_name_en, $child->last_name_en));

        $dataArr['_NAME_'] = trim( $cname . ' ' . $nick . ' ' . $ename );
        $dataArr['_DOB_'] = $child->birthday_search;
        

        $data = '{' . implode(" ", $dataArr) . '}'; //分隔符
        $data .= "\r\n";
		
		//if($child->invoice_title ) $data .= $child->invoice_title . "\r\n" ;
		
        foreach( array('fid','mid') as $_p){
            $pid = $child->getAttribute($_p);
            if($pid){
                echo $pid."\r\n";
                $pdataArr = array();
                $cname = trim($parents[$pid]->cn_name);
                $ename = trim(sprintf("%s %s", $parents[$pid]->en_firstname, $parents[$pid]->en_lastname));
                $pdataArr['_NAME_'] = trim($cname . ' ' . $ename);
                echo $pdataArr['_NAME_'];
                $pdataArr['_EMAIL_'] = isset($parents[$pid]->user) ? $parents[$pid]->user->email : '';
                $pdataArr['_MPHONE_'] = $parents[$pid]->mphone;
                $pdataArr['_TELE_'] = $parents[$pid]->tel;
                foreach($pdataArr as $_k => $_v){
                    if(empty($_v)) unset($pdataArr[$_k]);
                }
                $data .= implode(" ", $pdataArr);
				$data .= "\r\n";
            }
        }

        $ftsChild = FtsChild::model()->findByPk($child->childid);
        if ($ftsChild === null)
            $ftsChild = new FtsChild;
        $ftsChild->id = $child->childid;
        $ftsChild->tdata = $data;
        $ftsChild->schoolid = $child->schoolid;
        $ftsChild->status = $child->status;
		
        $ftsChild->updated = time();
        if($ftsChild->save()){
            echo ' ' . $ftsChild->id . 'ok ';
        }else{
            print_r($ftsChild->getErrors());
            echo ' !' . $ftsChild->id . 'faied! ';
        }
    }
	
	/**
	 * 将孩子相关信息集中到一个字段
	 */
	public function actionInitChild(){
	
		$total = ChildProfileBasic::model()->count();
		$cycle = ceil($total/$this->batchNum);
		$criteria = new CDbCriteria;
		
		for($i=0; $i<$cycle; $i++){
		//for($i=0; $i<1; $i++){
			$parentIds = array();
			$criteria->limit=$this->batchNum;
			$criteria->offset=$i*$this->batchNum;
			$criteria->order = 'childid DESC';
			$children = ChildProfileBasic::model()->findAll($criteria);
			foreach($children as $child){
				if($child->fid)	$parentIds[$child->fid] = $child->fid;
				if($child->mid)	$parentIds[$child->mid] = $child->mid;
			}
			
			$crits = new CDbCriteria;
			$crits->index = 'pid';
			$parents = IvyParent::model()->with('user')->findAllByPk($parentIds, $crits);
			
			//αβγδεζηθιρποξνμλκστυφχψω
			foreach($children as $child){
				$this->dataHandle($child, $parents);
			}
		}
		
	}
    
    public function actionSync() {
        $parentIds = array();
        $criteria = new CDbCriteria();
        $criteria->compare('t.flag', 1);
        $count = ChildSync::model()->count($criteria);
        $cycle = ceil($count/$this->batchNum);
        for($i=0; $i<$cycle; $i++){
//            $criteria->offset=$i*$this->batchNum;
            $criteria->limit=$this->batchNum;
            $children = ChildSync::model()->with('child')->findAll($criteria);
            if ($children){
                foreach($children as $child){
                    if(is_object($child->child)){
                        if($child->child->fid)	$parentIds[$child->child->fid] = $child->child->fid;
                        if($child->child->mid)	$parentIds[$child->child->mid] = $child->child->mid;
                    }
                }

                $crits = new CDbCriteria;
                $crits->index = 'pid';
                $parents = IvyParent::model()->with('user')->findAllByPk(array_keys($parentIds), $crits);

                foreach ($children as $child){
                    $child->flag = 0;
                    $child->save();
                    $this->dataHandle($child->child, $parents);
                }
            }
        }
        
        $criteria = new CDbCriteria;
        $criteria->compare('t.flag', 1);
        $count = StaffSync::model()->count($criteria);
        if ($count){
            $staffs = StaffSync::model()->with(array('user'))->findAll($criteria);
            
            foreach($staffs as $staff){
                $this->staffHandle($staff->user);
                
                $staff->flag = 0;
                $staff->save();
            }
        }
    }
	
	public function actionInitStaff(){
	
		$total = User::model()->allStaff()->count();
		$cycle = ceil($total/$this->batchNum);
		$criteria = new CDbCriteria;

		for($i=0; $i<$cycle; $i++){
		//for($i=0; $i<1; $i++){
			$criteria->limit=$this->batchNum;
			$criteria->offset=$i*$this->batchNum;
			$staffs = User::model()->allStaff()->with(array('profile'=>array('select'=>'first_name,last_name, branch')))->findAll($criteria);
			
            foreach($staffs as $staff){
                $this->staffHandle($staff);
            }
		}
		
	}
    
    public function staffHandle($staff)
    {
        $ftsStaff = FtsStaff::model()->findByPk($staff->uid);
        if(empty($ftsStaff)){
            $ftsStaff = new FtsStaff;
            $ftsStaff->id = $staff->uid;
        }
        $ftsStaff->active = $staff->level;
        $ftsStaff->schoolid = $staff->profile->branch;
        $ftsStaff->updated = time();
        $ftsStaff->tdata = sprintf("{%s | %s} %s (%d)", $this->abbs[$staff->profile->branch], $staff->getName(), $staff->email, $staff->uid);

        if(!$ftsStaff->save()){
            echo "Error: ". $staff->uid;
            print_r($ftsStaff->getErrors());
            echo "\r\n";
        }else{
            echo '.';
        }

        
    }

}