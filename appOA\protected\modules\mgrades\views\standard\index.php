<style>
    .bg<PERSON>hite{
        background-color: #FAFAFA !important;
    }
    .p0{
        padding:0
    }
    .numCard{
        width: 31px;
        height: 100%;
        margin-right: 16px;
        background: #EBEDF0;
        border-radius:2px 0px 0px 0px;
        height: 76px;
        text-align: center;
        line-height: 76px;
        font-size: 14px;
        color: #666666;
    }
    .borderTop{
        border-top:1px solid #E8EAED;
    }
    .standard{
        width: 100%;
        background: #FAFAFA;
        border-radius: 4px;
        border: 1px solid #EBEDF0;
        /* padding:16px; */
    }
    .sortable-ghost{
        background: rgba(77,136,210,0.1) !important;
        border: 1px solid #4D88D2;
    }
    .borderRight{
        border-right:1px solid #EBEDF0;
    }
    .border{
        border:1px solid #EBEDF0;
        
        padding:16px
    }
    .border-radius{
        border-radius:4px;
    }
    .blueColor{
        color:#4D88D2
    }
    .scroll-box::-webkit-scrollbar-thumb {
        /*滚动条里面小方块*/
        border-radius   : 10px;
        background-color: #ccc;
        background-image: none
    }
    .scroll-box::-webkit-scrollbar-track {
        /*滚动条里面轨道*/
        box-shadow   : inset 0 0 5px rgba(0, 0, 0, 0);
        background   : transparent;
        border-radius: 10px;
        border:none
    }
    .form-control{
        box-shadow: none;
        border: 1px solid #dcdfe6;
        box-sizing: border-box;
    }
    .form-control::-webkit-input-placeholder { /* Chrome/Opera/Safari */
        color: #ccc;
    }
    .el-input__inner,.form-control{
        height:34px;
        box-shadow:none
    }
    .el-input__prefix, .el-input__suffix{
        top:4px
    }
    .form-control:focus {
        box-shadow: none;
    }
    .text1{
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
    }
    .width0{
        width:0
    }
</style>
<div class="container-fluid"  id='container'>
    <ol class="breadcrumb">
        <li><?php echo Yii::t('site', 'Campus Workspace'); ?></li>
        <li><?php echo Yii::t('site', 'Reports Templates'); ?></li>
        <li class="active">标准库管理</li>
    </ol>

    <div class="row" >
        <div class="col-md-2 col-sm-12">
            <div class="list-group" id="classroom-status-list">
                <a href="<?php echo $this->createUrl('index'); ?>" class="list-group-item status-filter active"><?php echo Yii::t("newDS", "标准库管理"); ?></a>
                <a href="<?php echo $this->createUrl('option'); ?>" class="list-group-item status-filter"><?php echo Yii::t("newDS", "评定标准管理"); ?></a>
                <a href="<?php echo $this->createUrl('template'); ?>" class="list-group-item status-filter"><?php echo Yii::t("newDS", "评估报告模板"); ?></a>
            </div>
        </div>
        <div class="col-md-10 col-sm-12">
            <div class='mb24'>
                <button type="button" class="btn  mr16" :class='grade==key?"btn-primary":"btn-default"' v-for='(list,key,index) in config.gradeList' @click='getGrade(list,key)'>{{list}}</button>
            </div>
            <div class='row'>
                <div class="col-md-2 col-sm-12" >
                    <div class="list-group" id="classroom-status-list">
                        <a href="javascript:;"  v-for='(list,key,index) in config.programConfig' :class='program==key?"active":""' @click='getProgram(list,key)' class="list-group-item status-filter ">{{list}}</a>
                    </div>
                </div>
                <div  class="col-md-10 col-sm-12" v-if='grade!="" && program!=""'>
                    <div v-if='!isSort'>
                        <div class='loading'  v-if='!showData'>
                            <span></span>
                        </div>
                        <div class='flex mb16'>
                            <div class='flex1'>
                                <button type="button" class="btn btn-primary font14" @click='addList()'><span class='glyphicon glyphicon-plus'></span> 添加分类</button>
                            </div>
                            <div v-if='listData.items && listData.items.length!=0'>
                                <button type="button" class="btn btn-link font14" @click='sortList()'>排序</button>
                            </div>
                        </div>
                        
                        <div v-if='showData && listData.items && listData.items.length!=0'>
                            <div class="panel panel-default" v-for='(list,index) in listData.items'>
                                <div class="panel-heading bgWhite p0">
                                    <div class='flex align-items'>
                                        <div class='numCard'>{{index+1}}</div>
                                        <div class='flex1'>
                                            <div class='font16 color3 mb5 fontBold'>{{list.title}}</div>
                                            <div class='font14 color6' v-if='config.optionGroupList[list.option_groupid]'>{{config.optionGroupList[list.option_groupid].title}}</div>
                                        </div>
                                        <div class='mr24'>
                                            <button type="button" class="btn btn-link mr16 font14" @click='delList(index,list)'>删除</button>
                                            <button type="button" class="btn btn-primary font14" @click='editList(list)'>编辑</button>
                                        </div>
                                    </div>
                                </div>
                                <div class="panel-body p24">
                                    <div class='font14 color3 flex align-items pt16 pb16 ' :class='idx!=0?"borderTop":""' v-for='(item,idx) in listData.standardData[list.id]'>
                                        <div style='width:150px'>{{item.standard_code}}</div>
                                        <div class='flex1 pr24'>
                                            <div>{{item.title_cn}}</div>
                                            <div>{{item.title_en}}</div>
                                        </div>
                                        
                                        <div style='width:150px' class='color6'>{{config.startYearList.list[item.startyear]}}启用</div>
                                </div>
                                </div>
                            </div>
                        </div>
                        <div v-else-if='showData && (listData.length==0 || !listData.items)'>
                            <el-empty description="暂无数据"></el-empty>
                        </div>
                    </div>
                    <div v-if='isSort' class='border border-radius'>
                        <div class='flex mb24'>
                            <div class='flex1 font14 color3 fontBold'>排序</div>
                            <div >
                                <button type="button" class="btn btn-link font14" @click='cancelSortList()'>取消排序</button>
                                <button type="button" class="btn btn-primary font14" @click='saveSortList()'>保存</button>
                            </div>
                        </div>
                        <div id='cardSortable'>
                            <div class='bgWhite mb16' v-for='(list,index) in sortListData'>
                                <div class='flex align-items border-radius '>
                                    <div class='numCard'>{{index+1}}</div>
                                    <div class='flex1'>
                                        <div class='font16 color3 mb5'>{{list.title}}</div>
                                        <div class='font14 color6' v-if='config.optionGroupList[list.option_groupid]'>{{config.optionGroupList[list.option_groupid].title}}</div>
                                    </div>
                                    <div class='mr24' >
                                        <el-tooltip class="item" effect="dark" content="拖动" placement="top">
                                            <span class='glyphicon glyphicon-move font16 color9 cur-p listDataItem'></span>
                                        </el-tooltip>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="addListModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel" data-backdrop="static">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("directMessage", "添加分类");?></h4>
            </div>
            <div class="modal-body p24 overflow-y scroll-box" v-if='program!=""'  :style="'max-height:'+(height-160)+'px;overflow-x: hidden;'">
                <!-- <div class='overflow-y scroll-box' :style="'max-height:'+(height-210)+'px;overflow-x: hidden;'"> -->
                    <div class='color3 font16'><span class='el-icon-s-management blueColor mr5 font18'></span><span class='fontBold'>{{config.programConfig[program]}}</span></div>
                    <div class='row mt24'>
                        <div class='col-md-6'>
                            <div class='font14 mb8'>分类名称（中文）</div>
                            <div><input type="text" class="form-control" placeholder="请输入" v-model='title_cn'></div>
                        </div>
                        <div class='col-md-6'>
                            <div class='font14 mb8'>分类名称（英文）</div>
                            <div><input type="text" class="form-control" placeholder="请输入" v-model='title_en'></div>
                        </div>
                    </div>
                    <div class='row mt24'>
                        <div class='col-md-6'>
                            <div class='font14 mb8'>适用评定标准</div>
                            <div>
                            <el-select v-model="option_groupid" style='width:100%' placeholder="请选择">
                                <el-option
                                v-for='(item,index) in config.optionGroupList'
                                :key="item.id"
                                :label="item.title"
                                :value="item.id">
                                </el-option>
                            </el-select>
                            </div>
                        </div>
                    </div>
                    <div class='font14 mb8 mt24 flex'>
                        <div class='flex1'>添加标准项</div>
                        <div><span class='btn-link' @click='sortStandard' v-if='standardList.length>1'>排序</span></div>
                    </div>
                    <div>
                        <div class='standard flex align-items mb16' v-for='(list,index) in standardList' :id='index'>
                            <div class='flex1 color6 borderRight'>
                                <div class='p16'>
                                    <div class='row'>
                                        <div class='col-md-3'>
                                            <div class='font14 mb8'>Standard Code</div>
                                            <div><input type="text" class="form-control" placeholder="请输入" v-model='list.standard_code'></div>
                                        </div>
                                        <div class='col-md-9'>
                                            <div class='font14 mb8'>Item（Chinese）</div>
                                            <div><input type="text" class="form-control" placeholder="请输入" v-model='list.title_cn'></div>
                                        </div>
                                    </div>
                                    <div class='row mt16'>
                                        <div class='col-md-3'>
                                            <div class='font14 mb8'>启用学年</div>
                                            <div>
                                                <el-select v-model="list.startyear" style='width:100%' placeholder="请选择">
                                                    <el-option
                                                    v-for='(list,key,index) in config.startYearList.list'
                                                    :key="key"
                                                    :label="list"
                                                    :value="key">
                                                    </el-option>
                                                </el-select>
                                            </div>
                                        </div>
                                        <div class='col-md-9'>
                                            <div class='font14 mb8'>Item（English）</div>
                                            <div><input type="text" class="form-control" placeholder="请输入" v-model='list.title_en'></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class='ml16'>
                                <div class='mr16'><span class='glyphicon glyphicon-trash font16 color6 cur-p' @click='delItem(index)'></span></div>
                            </div>
                        </div>
                        <div><button type="button" class="btn-link font14" @click='addStandard()'><span class='el-icon-circle-plus-outline'></span> 新增项</button></div>
                    </div>
                <!-- </div> -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "取消");?></button>
                <button type="button" class="btn btn-primary" @click="saveStandard()"><?php echo Yii::t("newDS", "保存");?></button>
            </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="sortListModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel" data-backdrop="static">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("directMessage", "添加分类");?></h4>
            </div>
            <div class="modal-body overflow-y scroll-box" v-if='program!=""'  :style="'max-height:'+(height-150)+'px;overflow-x: hidden;'">
                <div class='font14 mb8 mb12 flex align-items'>
                    <div class='flex1 fontBold'>标准项排序</div>
                    <div>
                        <span class='btn-link'  @click='saveSortStandard("close")'>取消排序</span>
                        <button type="button" class="btn btn-primary ml16" @click='saveSortStandard("save")'>保存</button>
                    </div>
                </div>
                <div id="sortable">
                    <div class='standard flex align-items mb12' v-for='(list,index) in sortStandardList' :id='index'>
                        <div class='flex1 color6 borderRight'>
                            <div class='p5 flex color3 align-items'>
                                <div style='width:150px'>{{list.standard_code}}</div>
                                <div class='flex1 ml24 mr24 width0'>
                                    <div class='text1'>{{list.title}}</div>
                                </div>
                            </div>
                        </div>
                        <div class='ml16 mr16'>
                            <el-tooltip  effect="dark" content="拖动" placement="top">
                                <span class='glyphicon glyphicon-move font12 color6 cur-p item' ></span>
                            </el-tooltip>
                        </div>
                    </div>
                </div>
            </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="delModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
        <div class="modal-dialog modal-sm" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("newDS", "删除"); ?></h4>
                </div>
                <div class="modal-body p24" >
                    确定删除吗
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" @click='delList("del")'>确定</button>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    var height=document.documentElement.clientHeight;   
    var container = new Vue({
        el: "#container",
        data: {
            height:height,
            config:{},
            grade:'',
            program:'',
            listData:{},
            title_cn:'',
            title_en:'',
            option_groupid:'',
            standardList:[],
            itemId:'',
            isSort:false,
            delData:{},
            delIndex:'',
            sortStandardList:[],
            sortListData:[],
            showData:true
        },
        watch:{
        },
        created: function() {
            this.getConfig()
        },
        computed: {},
        methods: {
            getConfig(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("config") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.config=data.data
                            that.program=Object.keys(data.data.programConfig)[0]
                            that.grade=Object.keys(data.data.gradeList)[0]
                            that.getList()
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            getGrade(list,key){
                this.grade=key
                if(this.program!=''){
                    this.getList()
                }
            },
            getProgram(list,key){
                this.program=key
                if(this.grade!=''){
                    this.getList()
                }
            },
            getList(){
                let that=this
                this.isSort=false
                this.showData=false
                $.ajax({
                    url: '<?php echo $this->createUrl("categoryList") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        program:this.program,
                        grade:this.grade
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.listData=data.data
                            if(data.data.items){
                                that.sortListData=JSON.parse(JSON.stringify(data.data.items))
                            }
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                        that.showData=true
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                        that.showData=true
                    },
                })
                
            },
            addList(){
                $('#addListModal').modal('show')
                this.standardList=[]
                this.itemId=0
                this.title_cn=''
                this.title_en=''
                this.option_groupid=''
                this.standardList.push({
                    standard_code: "",
                    title_cn: "",
                    title_en: "",
                    startyear: ''
                })
            },
            addStandard(){
                this.standardList.push({
                    standard_code: "",
                    title_cn: "",
                    title_en: "",
                    startyear: ''
                })
            },
            delList(index,item){
                if(index!='del'){
                    this.delData=item
                    this.delIndex=index
                    $('#delModal').modal('show')
                    return
                }
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("categoryDel") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        id:this.delData.id,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                          that.listData.items.splice(that.delIndex,1)
                          resultTip({
                                msg: data.message
                            });
                          $('#delModal').modal('hide')
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            editList(list){
                this.title_cn=list.title_cn
                this.title_en=list.title_en
                this.option_groupid=list.option_groupid
                this.standardList=this.listData.standardData[list.id]?JSON.parse(JSON.stringify(this.listData.standardData[list.id])):[]
                this.standardList.forEach(item => {
                    item.startyear=item.startyear+''
                });
                $('#addListModal').modal('show')
                this.itemId=list.id
            },
            sortItem(){
                var that=this
                Sortable.create(document.getElementById('sortable'), {
                    handle: ".item",
                    ghostClass: "sortable-ghost", 
                    animation: 150, //动画参数
                    onEnd: function (evt) { //拖拽完毕之后发生该事件
                        var itemEl = evt.item;  // 拖拽的元素
                        that.sortStandardList.splice(evt.newIndex, 0, that.sortStandardList.splice(evt.oldIndex, 1)[0])
                        var newArray = that.sortStandardList.slice(0)
                        that.sortStandardList = []
                        that.$nextTick(function () {
                            that.sortStandardList = newArray
                        })
                    }
                });
            },
            sortStandard(){
                this.sortStandardList=JSON.parse(JSON.stringify(this.standardList))
                $('#sortListModal').modal('show')
                $('#addListModal').modal('hide')
                this.$nextTick(function () {
                    this.sortItem()
                });
            },
            saveSortStandard(type){
                if(type=='save'){
                    this.standardList=this.sortStandardList
                }
                $('#sortListModal').modal('hide')
                $('#addListModal').modal('show')

            },
            delItem(index){
                this.standardList.splice(index,1)
            },
            saveStandard(){
                let that=this
                if(this.title_cn.trim()==''){
                    resultTip({
                        error: 'warning',
                        msg:'请输入分类名称中文'
                    });
                    return
                }
                if(this.title_en.trim()==''){
                    resultTip({
                        error: 'warning',
                        msg:'请输入分类名称英文'
                    });
                    return
                }
                if(this.option_groupid==''){
                    resultTip({
                        error: 'warning',
                        msg:'请选择适用评定标准'
                    });
                    return
                }
                for(var i=0;i<this.standardList.length;i++){
                    if(this.standardList[i].standard_code=='' || this.standardList[i].title_cn=='' || this.standardList[i].startyear=='' || this.standardList[i].title_en==''){
                        resultTip({
                            error: 'warning',
                            msg:'请填写完整标准项'
                        });
                        return 
                    }
                }
                this.standardList.forEach((item,index) => {
                    item.weight=index+1
                });
                $.ajax({
                    url: '<?php echo $this->createUrl("categoryUpdate") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        id:this.itemId,
                        program:this.program,
                        grade:this.grade,
                        title_cn:this.title_cn,
                        title_en:this.title_en,
                        option_groupid: this.option_groupid,
                        items:this.standardList
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                          that.getList()
                          resultTip({
                                msg: data.message
                            });
                          $('#addListModal').modal('hide')
                            // that.listData=data.data
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            sortList(){
                this.isSort=true
                var that=this
                that.sortListData=JSON.parse( JSON.stringify (this.listData.items))
                var list=JSON.parse( JSON.stringify (this.listData.items))
                that.$nextTick(function () {
                    Sortable.create(document.getElementById('cardSortable'), {
                        handle: ".listDataItem",
                        ghostClass: "sortable-ghost", 
                        animation: 150, //动画参数
                        onEnd: function (evt) { //拖拽完毕之后发生该事件
                            list.splice(evt.newIndex, 0, list.splice(evt.oldIndex, 1)[0])
                            var newArray = list.slice(0)
                            that.sortListData = []
                            that.$nextTick(function () {
                                that.sortListData = newArray
                            })
                        }
                    });
                })
            },
            cancelSortList(){
                this.isSort=false
            },
            saveSortList(){
                let that=this
                var categoryId=[]
                this.sortListData.forEach(item => {
                    categoryId.push(item.id)
                });
                $.ajax({
                    url: '<?php echo $this->createUrl("categorySort") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        categoryIdList:categoryId
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                          that.getList()
                          resultTip({
                                msg: data.message
                            });
                            that.isSort=false
                            that.listData.items=that.sortListData
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            }
        }
    })
</script>
