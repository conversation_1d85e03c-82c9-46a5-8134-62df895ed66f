<style>
    [v-cloak] {
        display: none;
    }
    .image{
        width:125px;
        height:90px;
        border-radius:8px;
        object-fit: contain;
    }
    .font18{
        font-size:18px;
    }
    .grey{
        background:#FAFAFA;
        padding:16px 24px
    }
    .selectNum{
        background: #D9EDF7;
        border-radius: 6px;
        border: 1px solid #BCE8F1;
        font-size: 14px;
        color: #31708F;
        padding:6px 16px
    }
    .table_wrap {
        width: 100%;
        overflow: auto;
    }
    .tableShow {
        table-layout: fixed;
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
        border-top: 1px solid #DDDDDD;
    }
    .tableShow    thead tr th {
        position: sticky;
        top: 0;
        background: #FAFAFA;
        z-index: 1;
    }
     .tableShow  th:first-child,
     .tableShow   td:first-child{
        position: sticky;
        left: 0;
        background: #FAFAFA;
        right: 0px;
        border-left: 1px solid #DDDDDD ;
        border-right:none ;
        width: 130px;
        box-shadow: 4px 0px 6px 0px rgba(0,0,0,0.0600);
    }
    /* .scrollbar::-webkit-scrollbar {
        width : 10px; 
        height:10px;
    }
    .scrollbar::-webkit-scrollbar-thumb {
        border-radius   : 10px;
        background-color: skyblue;
        background-image: -webkit-linear-gradient(
        45deg,
        rgba(255, 255, 255, 0.2) 25%,
        transparent 25%,
        transparent 50%,
        rgba(255, 255, 255, 0.2) 50%,
        rgba(255, 255, 255, 0.2) 75%,
        transparent 75%,
        transparent
        );
    }
    .scrollbar::-webkit-scrollbar-track {
        box-shadow   : inset 0 0 5px rgba(0, 0, 0, 0.2);
        border-radius: 10px;
        background   : #ededed;
    } */
    .top{
        width: 0;
        height: 0;
        border-width: 0 5px 5px;
        border-style: solid;
        border-color: transparent transparent #BFBFBF;
        position: absolute;
        top: 2px;
        cursor: pointer;
    }
    .bottom{
        width: 0;
        height: 0;
        border-left: 5px solid transparent;
        border-right: 5px solid transparent;
        border-top: 5px solid #BFBFBF;
        cursor: pointer;
        position: absolute;
        top:10px
    }
    .topActive{
        border-color: transparent transparent #4D88D2;
    }
    .bottomActive{
        border-top: 5px solid #4D88D2;
    }
    .avatar{
        width: 44px;
        height: 44px;
        border-radius:50%;
        object-fit: cover;
    }
    .labelBg{
        background:#E8EAED
    }
    .border{
        border: 1px solid #DDDDDD;
    }
    .last .dropdown-menu{
        left: -35px;
        min-width: 100px;
    }
    .change{
        display: inline-block;
        width: 18px;
        height: 18px;
        background: #D9534F;
        text-align: center;
        line-height: 18px;
        border-radius: 50%;
        color: #fff;
        font-size: 12px;
    }
    tr td,tr th{
        vertical-align: middle !important;
    }
    .lineHeight{
        line-height:30px
    }
    .point{
        display:inline-block;
        width:5px;
        height:5px;
        border-radius:50%;
        position: absolute;
        top: 39%;
    }
    .green{
        background:#5CB85C;
    }
    .red{
        background:#D9534F;
    }
    .colorRed{
        color:#D9534F
    }
    .returnItem{
        border: 1px solid #ddd;
        border-radius: 4px;
        display: inline-block;
        font-size: 14px;
        color:#666;
        height: 31px;
        line-height: 31px;
    }
    .returnItem span{
        display: inline-block;
        padding: 0 10px;
        cursor: pointer;
        height:30px
    }
    .returnItemActive{
        color:#fff;
        background:#4D88D2
    }
    .returnItemActive .activeNum{
        color: #666;
        background: #fff;
    }
    .activeNum{
        padding: 0 9px;
        display: inline-block;
        background: #666;
        height: 16px !important;
        line-height: 16px;
        border-radius: 16px;
        color: #fff;
    }
    .tip-box {
        border-radius: 3px;
        padding: 5px 0;
        position: absolute;
        background: #fff;
        display: none;
        line-height: 1.5em;
        z-index: 999;
        box-shadow: 0 6px 12px rgb(0 0 0 / 18%);
        right:20px
    }
    .tip-box li{
        list-style: none;
    }
    .tip-box li a{
        display: block;
        padding: 3px 20px;
        clear: both;
        font-weight: 400;
        line-height: 1.42857143;
        color: #333;
        white-space: nowrap;
    } 
   #stocking{
    table-layout: fixed;
    word-wrap: break-word;
   }
   .noBg{
       background:#fff !important
   }
   .text-center{
       text-align:center !important
   }
   td{
    word-wrap: break-word;
    word-break: break-all;
   }
   .core_pop_wrap{
       width:240px
   }
   .pop_cont{
       padding:24px
   }
   .bgfa{           
        background: #FAFAFA;
        width:100px
   }
</style>
<?php
$statusName = array(
    '20' => Yii::t('user','等待确认'),
    '30' => Yii::t('user','等待发放'),
    '40' => Yii::t('user','已经发放'),
    '41' => Yii::t('user','等待退款'),
    '45' => Yii::t('user','退款中'),
    '50' => Yii::t('user','退款完成'),
    '55' => Yii::t('user','等待换货'),
    '60' => Yii::t('user','换货完成'),
);
?>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('//mcampus/default/index')) ?></li>
        <li><?php echo CHtml::link(Yii::t('site', '商品列表'), array('//mcampus/product/index')) ?></li>
        <li><?php echo CHtml::link(Yii::t('site', $productsCatModel->getName()), array('//mcampus/product/index', 'pcid' => $productsCatModel->id)) ?></li>
        <li><?php echo $statusName[$status]; ?></li>
    </ol>
    <div class="row">
        <div class="col-md-2 col-sm-2 mb10">
            <?php
            $this->widget('zii.widgets.CMenu', array(
                'items' => $this->getMenu(),
                'id' => 'pageCategory',
                'htmlOptions' => array('class' => 'nav nav-pills nav-stacked background-gray'),
                'activeCssClass' => 'active',
                'itemCssClass' => ''
            ));
            ?>
            <div class="el-divider el-divider--horizontal"><div class="el-divider__text is-center">换货</div></div>
            <?php
            $this->widget('zii.widgets.CMenu', array(
                'items' => $this->getExchangeMenu(),
                'id' => 'pageCategory',
                'htmlOptions' => array('class' => 'nav nav-pills nav-stacked background-gray'),
                'activeCssClass' => 'active',
                'itemCssClass' => ''
            ));
            ?>
            <div class="el-divider el-divider--horizontal"><div class="el-divider__text is-center">退款</div></div>
            <?php
            $this->widget('zii.widgets.CMenu', array(
                'items' => $this->getRefundMenu(),
                'id' => 'pageCategory',
                'htmlOptions' => array('class' => 'nav nav-pills nav-stacked background-gray'),
                'activeCssClass' => 'active',
                'itemCssClass' => ''
            ));
            ?>

        </div>
        <div class="col-md-10 col-sm-10">
            <?php echo $this->renderPartial($template)?>
        </div>
    </div>
</div>
<script>
     $(document).click(function(event){
        var _con = $('#tip-box');  
        if(!_con.is(event.target) && _con.has(event.target).length === 0){
            $('#tip-box').hide();  
        }
    });
</script>
<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>
