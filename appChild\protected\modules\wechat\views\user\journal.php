<?php
$childId = $childObj->childid;
$schoold = $childObj->schoolid;
$lang = Yii::app()->language == 'zh_cn' ? 'zh' : 'en';
?>
<style>
    .flex{
        display:flex;
        padding: 10px 15px;
        align-items:center
    }
    .flex1{
        flex:1;
        font-size: 15px;
        font-weight: 600;
        color: #323233;
    }
    .url{
        font-size: 14px;
        font-weight: 400;
        color: #4D88D2;
        
    }
    .weui_icon_search{
        margin-right:4px
    }
    .weui_icon_search:before{
        color: #4D88D2;
    }
</style>
<div class="cell">
    <div class="bd">
        <div class="weui_cells_title"><?php echo Yii::t('portfolio', 'Journal'); ?></div>
        <div class="weui_cells weui_cells_access">
            <?php
            //选择孩子
            $this->widget('ext.wechat.ChildSelector', array(
                'childObj' => $childObj,
                'myChildObjs' => $this->myChildObjs,
                'jumpUrl' => 'journal',
            ));
            ?>
            <?php if(in_array($schoold, array('BJ_DS', 'BJ_SLT'))):?>
            <div class='flex'>
                <div class='flex1'><?php echo Yii::t('portfolio', 'All Journals'); ?></div>
                <a href='https://open.weixin.qq.com/connect/oauth2/authorize?appid=wxb1a42b81111e29f3&redirect_uri=https://wx.daystaracademy.cn/JournalSearch&response_type=code&scope=snsapi_userinfo&state=ds#wechat_redirect' class='url'><i class="weui_icon_search" ></i><?php echo Yii::t('portfolio', 'Search or View by Teachers'); ?></a>
            </div>
            <?php endif;?>
            <?php
            //选择学年
            $this->widget('ext.wechat.CalendarSelector', array(
                'currentYid' => $yid,
                'calendarArr' => $calendarArr,
                'jumpUrl' => 'journal',
            ));
            ?>
            <?php if (empty($noteArr) && empty($journalData)) : ?>
                <div class="msg">
                    <div class="weui_msg">
                        <div class="weui_icon_area"><i class="weui_icon_msg weui_icon_info"></i></div>
                        <div class="weui_text_area">
                            <p class="weui_msg_desc"><?php echo Yii::t('portfolio', 'No weekly journal found.'); ?></p>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <?php foreach ($weekDays as $week => $timestamp) : ?>
                <?php if (isset($noteArr[$yid][$week])) : $note = $noteArr[$yid][$week]; ?>
                    <a href="<?php echo $this->createUrl('article', array('classid' => $note->classid, 'yid' => $note->yid, 'weeknum' => $note->weeknumber, 'startyear' => $note->startyear, 'childid' => $note->childid)); ?>" class="weui_cell js_grid" data-id="article">
                        <div class="weui_cell_bd weui_cell_primary">
                            <p>Week <?php echo str_pad($note->weeknumber, 2, 0, STR_PAD_LEFT);
                                    echo '<span style="padding-left: 10px;color:#999;">' . date('m/d', $weekDays[$note->weeknumber]) . ' - ' . date('m/d', $weekDays[$note->weeknumber] + 4 * 24 * 3600) . '</span>';
                                    if (in_array($note->weeknumber, $unreadWeek)) echo ' <i class="weui_icon_info"></i>' ?></p>
                        </div>
                        <div class="weui_cell_ft"><?php echo sprintf(Yii::t('wechat', '%s photos'), $photoNum[$note->weeknumber] ? $photoNum[$note->weeknumber] : 0); ?></div>
                    </a>
                <?php endif; ?>
                <?php if (isset($journalData[$week])) :  ?>
                    <?php
                    $journal = $journalData[$week];
                    $schoolId = implode(',', $journal['schools']);
                    $startYear = $calendarArr[$yid];
                    $urlPrefix = Mims::isProduction() ? 'https://wx.daystaracademy.cn/' : 'http://192.168.149.13:8080/';
                    $returnUrl = $this->createUrl('journal');
                    $t = time();
                    $openid = $this->openid;
                    $state = $this->state;
                    $token = md5($openid . $t . CommonUtils::SECURITY_KEY);
                    $url = $urlPrefix . "journal/list/$childId/$schoolId/$startYear/$week?state=$state&lang=$lang&openid=$openid&t=$t&token=$token";
                    ?>
                    <a href="<?php echo $url; ?>" class="weui_cell js_grid" data-id="article">
                        <div class="weui_cell_bd weui_cell_primary">
                            <p>Week <?php echo str_pad($week, 2, 0, STR_PAD_LEFT);
                                    echo '<span style="padding-left: 10px;color:#999;">' . $journal['startDate'] . ' - ' . $journal['endDate']; ?></p>
                        </div>
                        <div class="weui_cell_ft"><?php echo sprintf(Yii::t('wechat', '%s journals'), $journal['count']); ?></div>
                    </a>
                <?php endif; ?>
            <?php endforeach; ?>
        </div>
    </div>
</div>
