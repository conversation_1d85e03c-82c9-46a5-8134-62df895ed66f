<?php $form=$this->beginWidget('CActiveForm', array(
	'id'=>'task-setting-form',
	'enableAjaxValidation'=>false,
	'htmlOptions'=>array('class'=>'J_ajaxForm'),
));

$cmodel->fData['title'] = array('zh_cn'=>'中文', 'en_us'=>'英文');
$model->fData['feeamount'][$fee] = isset($feeamounts[$fee])?$feeamounts[$fee]:array();
$model->fData['discount'][$fee] = $discounts;
$model->fData['_discount'][$fee] = !$bind_dis['discoundId']['active'] ? $bind_dis['discoundId']['id'] : 0;
?>
<div class="table_full form" style="height:220px;_height:220px;">
    <table width="100%">
        <colgroup>
            <col class="th" />
            <col />
        </colgroup>
        <tbody>
            <tr>
                <th><?php echo $form->labelEx($cmodel,'language'); ?></th>
                <td>
                    <ul class="switch_list">
                        <?php echo $cmodel->renderField('language');?>
                    </ul>
                </td>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($cmodel,'startdate'); ?></th>
                <td><?php echo $cmodel->renderField('startdate');?></td>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($model,'feeamount'); ?></th>
                <td>
                    <?php
                    echo $model->renderField('feeamount', $fee);
                    echo $form->hiddenField($model, 'feetype', array('value'=>1, 'name'=>'InvoiceItemForm['.$fee.'][feetype]'));
                    ?>
                </td>
            </tr>
            <?php if ($fee == 'FEETYPE_TUITION'):?>
            <tr>
                <th><?php echo $form->labelEx($model,'discount'); ?></th>
                <td><?php echo $model->renderField('discount', $fee);?></td>
            </tr>
            <?php endif;?>
        </tbody>
    </table>
</div>
<div class="pop_bottom">
    <button type="button" class="btn fr" id="J_dialog_close"><?php echo Yii::t('global','Cancel');?></button>
    <button class="btn fr btn_submit mr10 J_ajax_submit_btn"><?php echo Yii::t('global','Submit');?></button>
</div>
<?php $this->endWidget(); ?>
<script type="text/javascript">
function cb()
{
    parent.ginv('<?php echo $this->createUrl('/child/invoice/task');?>');
}
$('a.assitDate').bind('click', function() {
	var targetEle = '#' + $(this).attr("tId");
	var v = $(this).attr("a-date");
	$(targetEle).val(v);
});
$('.service-day i').live('click', function(){
    var cssprefix = 'day-';
    var halfday = $(this).attr('halfday');
    var dayArray = halfday ? ['20', '40'] : ['10', '40'];
    for (var i in dayArray){
        if ($(this).hasClass(cssprefix+dayArray[i])){
            var j = parseInt(i)+1;
            if (j>=dayArray.length){
                var j = 0;
            }
            $(this).removeClass().addClass(cssprefix+dayArray[j]);
            $('#'+$(this).attr('rel')).val(dayArray[j]);
            break;
        }
    }
});

function showServiceInfo(ele){
	var v=$(ele).val();
	var reg = new RegExp('[2,3,4]D');
	found = reg.exec(v);
	if(found){
		$('.service-day').show();
		$('.service-day i').removeAttr('class');
		$('.service-day i').addClass('day-40');
		$('.service-day input').val(40);
		$('input.showServiceDay').val(1);
	}else{
		$('.service-day').hide();
		$('.service-day i').removeAttr('class');
		$('.service-day i').addClass('day-10');
		$('.service-day input').val(10);
		$('input.showServiceDay').val(0);
	}
}
</script>