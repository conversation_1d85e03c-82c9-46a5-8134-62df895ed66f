<text:p xmlns:text="urn:oasis:names:tc:opendocument:xmlns:text:1.0">
  <draw:frame xmlns:draw="urn:oasis:names:tc:opendocument:xmlns:drawing:1.0" xmlns:svg="urn:oasis:names:tc:opendocument:xmlns:svg-compatible:1.0" svg:width="12.567708175cm" svg:height="16.848541467cm" draw:style-name="Frame">
    <draw:text-box>
    textnode
      <draw:frame svg:width="12.567708175cm" svg:height="15.848541467cm" draw:style-name="Image">
        <draw:image xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="Pictures/kristian.jpg"/> textnode
      </draw:frame>
      textnode
      <text:p text:style-name="Text">Image <text:sequence xmlns:style="urn:oasis:names:tc:opendocument:xmlns:style:1.0" text:ref-name="refImage1" style:num-format="1" text:formula="ooow:Image+1" text:name="Image">1</text:sequence>: Dette er en test caption</text:p>
      textnode
    </draw:text-box>
  </draw:frame>
</text:p>
