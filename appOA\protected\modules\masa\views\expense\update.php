<div class="modal-header">
    <button type="button" class="close closeModal" data-dismiss="modal" aria-label="Close"><span
            aria-hidden="true">&times;</span>
    </button>
    <h4 class="modal-title" id="exampleModalLabel"><?php echo $typeArr[$type] . ($model->id ? '更新：' : '添加：'); ?></h4>
</div>

<div class="modal-body" id="eaVueModal">
    <?php $form = $this->beginWidget('CActiveForm', array(
        'id' => 'expense-forms',
        'enableAjaxValidation' => false,
        'action' => ($model->id) ? $this->createUrl('expenesAdd', array('expenesId' => $model->id)) : $this->createUrl('expenesAdd'),
        'htmlOptions' => array('class' => 'J_ajaxForm form-horizontal', 'role' => 'form', 'enctype' => "multipart/form-data"),
    )); ?>
    <div class="">
        <div class="form-group">
            <label class="col-md-2 control-label">报销人* </label>

            <div class="col-md-9">
                <select name="AsaExpense[expense_uid]" class="form-control teacher" @change="changeTeacher()"
                        :value="eaModel.expense_uid">
                    <option value="">请选择</option>
                    <option v-for="(teacher,tid) in eaTeacher" :value="tid">{{teacher}}</option>
                </select>
            </div>
        </div>
        <div class="form-group">
            <label class="col-md-2 control-label">标题* </label>

            <div class="col-md-9">
                <input type="text" name="AsaExpense[title]" :value="eaModel.title" class="form-control"/>
            </div>
        </div>
<!--         <div class="form-group">
            <label class="col-md-2 control-label">申请人日期* </label>

            <div class="col-md-9">
                <input type="text" name="AsaExpense[created]" :value="eaModel.created" class="form-control datepicker"/>
            </div>
        </div> -->


        <div class="form-group">
            <label class="col-md-2 control-label">收款方户名 </label>

            <div class="col-md-9">
                <input readonly="readonly" type="text" name="AsaExpense[payee_user]" id="payee_user" :value="eaModel.payee_user"
                       class="form-control"/>
            </div>
        </div>
        <div class="form-group">
            <label class="col-md-2 control-label">收款方账号 </label>

            <div class="col-md-9">
                <input readonly="readonly" type="text" name="AsaExpense[payee_account]" id="payee_account" :value="eaModel.payee_account"
                       class="form-control"/>
            </div>
        </div>
        <div class="form-group">
            <label class="col-md-2 control-label">开户行 </label>

            <div class="col-md-9">
                <select name="AsaExpense[payee_bank]" class="form-control" id="payee_bank" :value="eaModel.payee_bank">
                    <option value="">请选择</option>
                    <option v-for="(b,bvalue) in bank" :value="bvalue">{{b}}</option>
                </select>
            </div>
        </div>
        <input type="hidden" name="expenesId" :value="eid"/>
        <input type="hidden" name="AsaExpense[type]" :value="eaType"/>
    </div>
    <div class="modal-footer">
        <button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global', '保存'); ?></button>
    </div>
    <?php $this->endWidget(); ?>
    <div class="page-header" v-if="eid != null && isEmptyObj(eaList)">
        <h4>报销明细：</h4>
        <table class="table table-hover">
            <thead>
            <tr>
                <th width="200">课程组</th>
                <th width="160">课程</th>
                <th width="160">内容摘要</th>
                <th width="160">报销金额</th>
                <th>文件</th>
                <th></th>
            </tr>
            </thead>
            <tbody>
            <tr v-for="list in eaList">
                <td v-if="list.groupid != 0 && list.groupid != ''">{{eaGroup[list.groupid]}}</td>
                <td v-else>课后课</td>
                <td v-if="list.courseid != 0 && list.courseid != ''">{{eaCourse[list.groupid][list.courseid]}}</td>
                <td v-else>课后课</td>
                <td>{{list.content}}</td>
                <td>{{list.amount}}</td>
                <td><a class="btn btn-info btn-xs mr5" v-for="(f,index) in list.files" target="_blank"
                       :href="f"><span>附件{{index + 1}}</span></a></td>
                <td>
                    <a :href="'<?php echo $this->createUrl('DelExpenesItem') ?>&expenesItemId=' + list.id"
                       class="btn btn-sm J_ajax_del" style="padding-top: 0"><i
                            class="glyphicon glyphicon-trash"></i></a>
                </td>
            </tr>
            </tbody>
        </table>
        <div class="pull-right">总额：¥ {{allPrice}}</div>
        <div style="clear: both"></div>
    </div>
    <h4 v-else-if="eid != null">
        暂无数据
        <hr/>
    </h4>
    <div id="addForm" v-show="!isEmptyObj(eaList)">
        <?php $form = $this->beginWidget('CActiveForm', array(
            'id' => 'expense-forms2',
            'enableAjaxValidation' => false,
            'action' => $this->createUrl('expenseItemAdd'),
            'htmlOptions' => array('class' => 'J_ajaxForm form-horizontal', 'role' => 'form'),
        )); ?>
        <div class="form-horizontal" v-if="eid != null">
            <div class="form-group">
                <label class="col-md-2 control-label">选择课程组</label>

                <div class="col-md-9">
                    <select name="AsaExpenseItem[groupid]" class="form-control" id="eaGroup" v-model="selectGroup">
                        <option value="">请选择</option>
                        <option v-for="(group,gid) in eaGroup" :value="gid">{{group}}</option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label class="col-md-2 control-label">选择课程</label>

                <div class="col-md-9">
                    <select id="eaCourse" name="AsaExpenseItem[courseid]" class="form-control">
                        <option value="">总体运营</option>
                        <option v-for="(course,cid) in eaCourse[selectGroup]" :value="cid">{{course}}</option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label class="col-md-2 control-label">报销内容</label>

                <div class="col-md-9">
                    <input type="text" name="AsaExpenseItem[content]" class="form-control" id="eaContent"/>
                </div>
            </div>
            <div class="form-group">
                <label class="col-md-2 control-label">报销金额 *</label>

                <div class="col-md-9">
                    <input type="text" class="form-control" name="AsaExpenseItem[amount]" id="eaPrice"
                           placeholder="请输入金额"/>
                </div>
            </div>
            <div class="form-group">
                <label class="col-md-2 control-label">报销文件</label>

                <div class="col-md-9">
                    <input id="inputfile" onchange="uploadata()" type="file">

                    <p class="help-block">报销说明，行政报销申请等附加材料</p>

                    <div id="attachment" class="mb5">
                    </div>
                    <span id="fileinfo" class="text-warning"></span>
                </div>
            </div>
            <input type="hidden" name="AsaExpenseItem[eid]" :value="eid">
        </div>
        <div class="modal-footer" v-show="eid != null">
            <span class="text-warning pull-left mt15" style="display: none" id="addListWarning">
                <i class="glyphicon glyphicon-remove text-warning"></i><span></span>
            </span>
            <input type="reset" class="_reset" style="display: none"/>
            <button class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global', '新增'); ?></button>
        </div>
        <?php $this->endWidget(); ?>
    </div>
    <div class="modal-body continueAddBtn" v-show="isEmptyObj(eaList)">
        <button class="btn btn-primary pull-right" @click="showAddForm"><?php echo Yii::t('global', '继续添加报销明细'); ?></button>
        <div style="clear: both"></div>
    </div>

</div>
</div>
<script>

    var eaGroup = <?php echo json_encode($group)?>;
    var eaCourse = <?php echo json_encode($course)?>;
    var eaTeacher = <?php echo json_encode($teacher)?>;
    var eaType = <?php echo json_encode($type)?>;
    var eaModel = <?php echo json_encode($model->attributes)?>;
    var eaList = <?php echo json_encode($modelItem)?>;
    var teacherInfo = <?php echo json_encode($teacherInfo)?>;
    var bank = <?php echo json_encode($bank)?>;
    var eid;
    var eaVue = new Vue({
        el: "#eaVueModal",
        data: {
            eaList: eaList,
            eaModel: eaModel,
            eaTeacher: eaTeacher,
            eaType: eaType,
            eaGroup: eaGroup,
            eaCourse: eaCourse,
            selectGroup: "",
            eid: null,
            teacherInfo: teacherInfo,
            bank: bank
        },
        created: function () {
            this.eid = eaModel['id'];
        },
        updated: function () {
            head.Util.ajaxDel();
        },
        methods: {
            addEaList: function (data) {
                var _this = this;
                if (!_this.isEmptyObj(_this.eaList)) {
                    _this.eaList = {};
                }
                Vue.set(this.eaList, data.id, data);
                $('._reset').click();
                this.selectGroup = "";
                $('#attachment').empty();
                $('.continueAddBtn').show();
                $('#addForm').hide();
            },
            delEaList: function (id) {
                var _this = this;
                $.each(_this.eaList, function (i, list) {
                    if (list.id == id) {
                        Vue.delete(_this.eaList, i)
                    }
                });
                resultTip({"msg": "删除成功"})
            },
            setEid: function (data) {
                this.eid = data.eid;
            },
            setModel: function (data) {
                Vue.set(this.eaModel, 'expense_uid', data.expense_uid);
                Vue.set(this.eaModel, 'title', data.title);
                Vue.set(this.eaModel, 'payee_user', data.payee_user);
                Vue.set(this.eaModel, 'payee_account', data.payee_account);
                Vue.set(this.eaModel, 'payee_bank', data.payee_bank);
                Vue.set(this.eaModel, 'created', data.created);
            },
            isEmptyObj: function (obj) {
                if (Object.keys(obj).length > 0) {
                    return true;
                }
                return false;
            },
            changeTeacher: function () {
                var _this = this;
                if (_this.eaModel.title != null) {
                    return false;
                }
                var _value = $('.teacher').val();
                if (_value != '') {
                    $('#payee_user').val(_this.teacherInfo[_value]['bankName']);
                    $('#payee_account').val(_this.teacherInfo[_value]['bankAccount']);
                    $('#payee_bank').val(_this.teacherInfo[_value]['bank']);
                }
            },showAddForm:function(){
                $('.continueAddBtn').hide();
                $('#addForm').show();
            }
        },
        computed: {
            allPrice: function () {
                var _price = 0;
                $.each(this.eaList, function (i, list) {
                    _price += parseFloat(list.amount);
                });
                return _price.toFixed(2);
            }
        }
    });
    var refundSuccess = function () {
        resultTip({"msg": "保存成功"})
    };
    function uploadata() {
        $("#fileinfo").text('');
        var data = new FormData();
        $.each($('#inputfile')[0].files, function (i, file) {
            data.append('upload_file', file);
        });
        $.ajax({
            url: '<?php echo $this->createUrl('expenseFiles'); ?>',
            data: data,
            type: 'post',
            dataType: 'json',
            contentType: false,    //不可缺
            processData: false,    //不可缺
            success: function (data) {
                if (data.msg == 'success') {
                    //清空文件域
                    $("#inputfile").after($("#inputfile").clone().val(""));
                    $("#inputfile").remove();
                    //显示附件
                    var atta = '<span class="mr5"><a class="btn btn-info btn-xs" target="_blank" href="' + data.url + '">' + data.saveName + ' <span></span></a><input type="hidden" name="AsaExpenseItem[files][]" class="eaFile" value="' + data.saveName + '" > <span class="btn btn-danger btn-xs delfile" _value="' + data.saveName + '"><i class="glyphicon glyphicon-remove"></i></span></span> ';
                    $("#attachment").append(atta);
                } else {
                    $("#fileinfo").text(data.msg);
                }
            },
            error: function (data) {
                $("#fileinfo").text(data.msg);
            }
        });
        $("#inputfile").after($("#inputfile").clone().val(""));
        $("#inputfile").remove();
    }
    $(document).on('click', '.delfile', function () {
        var _this = $(this);
        var filename = $(this).attr('_value');
        $.ajax({
            url: '<?php echo $this->createUrl('delFiles'); ?>',
            data: {fileName: filename},
            type: 'post',
            dataType: 'json',
            success: function (data) {
                if (data.state == 'success') {
                    _this.parent().remove()
                } else {
                    $("#fileinfo").text(data.message);
                }
            },
            error: function (data) {
                $("#fileinfo").text(data.message);
            }
        });
    })
    var cbSuccess = function (data) {
        eaVue.setEid(data)
        eaVue.setModel(data)
    };
    var cbAddEalist = function (data) {
        eaVue.addEaList(data);
    };
    var cbDelEaList = function (data) {
        eaVue.delEaList(data);
    };
    head.Util.ajaxDel();
    $('.datepicker').datepicker({
        changeMonth: true,
        changeYear: true,
        dateFormat: 'yy-mm-dd'
    });
    $('.closeModal').on('click', function () {
        $.fn.yiiGridView.update('expenseList', {
            complete: function () {
                head.Util.ajaxDel()
            }
        });
    })
</script>
