<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo Yii::t('site', 'Campus Workspace'); ?></li>
        <li><?php echo Yii::t('site', 'Advanced'); ?></li>
        <li class="active"><?php echo Yii::t('site', 'Survey Report'); ?></li>
    </ol>
    <div class="row">
        <div class="col-md-12">
            <h3>
                [<?php echo $this->branchObj->abb;?>]
                <?php echo $surveyDetail->survey->getTitle();?>
                <small><?php echo $tModel[0]->getTitle()?></small>
            </h3>

            <button type="button" class="btn btn-success" style="margin-bottom: 10px"
                    data-survery-id="<?php echo $surveyDetail->survey->id; ?>"
                    data-survery-branch="<?php echo $this->branchId; ?>"
                    onclick="refreshReport(this)">
                <span class="glyphicon glyphicon-refresh" aria-hidden="true"></span> <?php echo Yii::t('cadmin',
                    'Refresh Report'); ?>
            </button>
            <ul>
                <li><?php echo sprintf('%s ~ %s', OA::formatDateTime($surveyDetail->start_time),
                        OA::formatDateTime($surveyDetail->end_time)); ?></li>
                <li><?php echo $takenCount; ?></li>
            </ul>
            <div class="topic-wrapper">

                <?php
                $topicNum = 1;
                foreach ($topicData as $topic):
                    ?>
                    <div class="panel panel-default" id="topic-<?php echo $topic['id']; ?>">
                        <div class="panel-heading">
                        <span class="topic-number pull-left">
                            <span class="label label-primary"><?php echo $topicNum; ?></span></span>
                            <h4>
                                <div class="mb5"><?php echo nl2br($topic['title_cn']); ?></div>
                                <div><?php echo nl2br($topic['title_en']); ?></div>
                            </h4>
                        </div>
                        <div class="panel-body">

                            <?php if ($reportText && isset($reportText[$topic['id']])): ?>
                                <div class="col-md-6 col-sm-12">
                                    <ol>
                                        <?php foreach ($reportText[$topic['id']] as $_text):
                                            $_text = trim($_text);
                                            ?>
                                            <?php if (!empty($_text)): ?>
                                            <li>
                                                <p class="text-muted"><?php echo $_text; ?></p>
                                            </li>
                                        <?php endif; ?>
                                        <?php endforeach; ?>
                                    </ol>
                                </div>
                            <?php endif; ?>

                            <?php if ($topic['option_group'] && isset($optionsData[$topic['option_group']])): ?>
                                <div class="col-md-6 col-sm-12 table-responsive">
                                    <div class="row">
                                        <?php foreach ($optionsData[$topic['option_group']] as $_option):
                                            $_sum = array_sum($reportData['total'][$topic['id']]);
                                            ?>
                                            <div>
                                                <div class="col-md-4 col-sm-2 col-xs-4 text-right">
                                                    <?php echo Yii::app()->language == 'zh_cn' ?
                                                        $_option['title_cn'] : $_option['title_en']; ?>
                                                </div>
                                                <div class="col-md-8 col-sm-10 col-xs-8">
                                                    <?php
                                                    $_vote = intval($reportData['total'][$topic['id']][$_option['option_value']]);
                                                    $_percent = ($_vote) ? sprintf('%01.4f', $_vote / $_sum) : '0';

                                                    $_percentLabel = ($_vote) ? sprintf('%01.2f%% (%s)',
                                                        100 * $_vote / $_sum,
                                                        $_vote) : '0';
                                                    ?>

                                                    <div class="progress-wrapper">
                                                        <div class="progress"
                                                             option-value="<?php echo $_option['option_value']; ?>">
                                                            <div class="progress-bar"
                                                                 aria-valuenow="<?php echo 100 * $_percent; ?>"
                                                                 aria-valuemin="0"
                                                                 aria-valuemax="100"
                                                                 style="width: <?php echo 100 * $_percent; ?>%">
                                                                <?php echo $_percentLabel; ?>
                                                            </div>
                                                        </div>
                                                    </div>

                                                <span class="progress-text">
                                                    <?php echo $_percentLabel; ?>
                                                </span>
                                                </div>
                                                <div class="clearfix"></div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>

                                    <!--家长调查问卷-->
                                    <?php if ($surveyDetail->survey->respondents == 'parent'): ?>
                                        <table class="table table-bordered table-striped">
                                            <!--<tr>
                                            <th></th>
                                            <?php foreach ($optionsData[$topic['option_group']] as $_option): ?>
                                            <th><?php echo $_option['option_value']; ?></th>
                                            <?php endforeach; ?>
                                        </tr>-->
                                            <?php foreach ($classData as $_classid => $_title):
                                                $_votes = $reportData['detail'][$topic['id']][$_classid];
                                                $_total = intval(array_sum($_votes));
                                                ?>
                                                <tr>
                                                    <th>
                                                        <?php echo $_title; ?>
                                                        <span class="badge"><?php echo $_total; ?></span>
                                                    </th>
                                                    <?php foreach ($optionsData[$topic['option_group']] as $_option):

                                                        ?>
                                                        <td>
                                                        <span class="text-muted">
                                                        <?php
                                                        if (intval($_votes[$_option['option_value']])) {
                                                            echo sprintf('%01.2f%% (%s)',
                                                                100 * $_votes[$_option['option_value']] / $_total,
                                                                $_votes[$_option['option_value']]);
                                                        } else {
                                                            echo ' - ';
                                                        }?>
                                                        </span>
                                                        </td>
                                                    <?php endforeach; ?>

                                                </tr>
                                            <?php endforeach; ?>
                                        </table>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>

                        </div>
                    </div>

                    <?php if ($topicNum % 5 == 0): ?>
                    <div class="page-breaker"></div>
                <?php endif;?>
                    <?php
                    $topicNum++;
                endforeach;
                ?>
            </div>
        </div>
    </div>
</div>

<script>
    $(function () {
        refreshReport = function (obj) {
            $(obj).attr('disabled', 'disabled');
            $.ajax({
                type: 'post',
                dataType: 'json',
                data: {genReport: 1}
            }).done(function (data) {
                if (data.state == 'success') {
                    location.reload();
                } else {
                    $(obj).removeattr('disabled');
                }
            })
        }
    })
</script>

<style>
    .topic-number {
        font-size: 200%;
        margin-right: 0.5em;
        margin-top: 0px;
    }

    .progress-wrapper {
        width: 180px;
    }

    .progress {
        margin-bottom: 10px;
    }

    .progress-bar {
        color: #000000;
        white-space: nowrap;
    }

    .progress-text {
        display: none;
    }

    @media print {
        body {
            padding-top: 0 !important;
        }

        button {
            display: none !important;
        }

        .progress-wrapper {
            display: none;
        }

        .progress-text {
            display: inline;
        }

        h3, h4 {
            font-size: 90%
        }

        .topic-number {
            font-size: 100%
        }

        .panel-heading {
            padding: 0 15px;
        }

        .page-breaker {
            page-break-after: always
        }
    }
</style>