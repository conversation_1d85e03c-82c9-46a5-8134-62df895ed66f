<script type="text/template" id="staff-item-template">
    <tr>
        <td><?php echo CHtml::checkBox("UserLeave[staff_uid][<%=item.uid%>]",null,array('value'=>'<%=item.uid%>','class'=>'J_check','encode'=>false,'id'=>'UserLeave_staff_uid_<%=classify%>_<%=item.uid%>'));?></td>
        <td>
            <%=item.name%>
            <?php echo CHtml::hiddenField('startYear','<%=startYear%>',array('encode'=>false))?>
        </td>
        <td><?php echo CHtml::textField('UserLeave[hours][<%=item.uid%>]','<%=hours%>', array('class'=>'form-control','encode'=>false,'disabled'=>'disabled','datastaffid'=>'<%=item.uid%>','id'=>'UserLeave_hours_<%=classify%>_<%=item.uid%>'));?></td>
        <td>
            <%=dates.startDate%>~<%=dates.endDate%>
        </td>
    </tr>
</script>

<script type="text/template" id="staff-banance-leave">
    <%
        if (_.isUndefined(waitDataList[id])){
            var waitData = '';
            var disabled = '';
        }else{
            var waitData = waitDataList[id].sumTxt;
            var disabled = 'disabled';
        }
    %>
    <tr>
        <td id="extension-list-<%=id%>">
            <% if (waitData){%>
                <?php echo CHtml::checkBox("AsLeaveExtensionHistory[leave_id][<%=id%>]",null,array('value'=>'<%=id%>','class'=>'J_check','encode'=>false,'id'=>'AsLeaveExtensionHistory_leave_id_<%=id%>','disabled'=>'<%=disabled%>'));?>
            <%}else{%>
                <?php echo CHtml::checkBox("AsLeaveExtensionHistory[leave_id][<%=id%>]",null,array('value'=>'<%=id%>','class'=>'J_check','encode'=>false,'id'=>'AsLeaveExtensionHistory_leave_id_<%=id%>'));?>
            <%}%>
            <?php echo CHtml::hiddenField("AsLeaveExtensionHistory[staff_uid][<%=id%>]", '<%=staff_uid%>',array('encode'=>false,'disabled'=>'disabled'));?>
        </td>
        <td><%=name%></td>
        <td><%=hoursTxt%> <span title="正在审核<%=waitData%>" class="label label-info"><%=waitData%></span></td>
        <td><%=startdate%>~<span id="AsLeaveExtensionHistory_enddate_<%=id%>"><%=enddate%></span></td>
        <td><?php echo CHtml::textField('AsLeaveExtensionHistory[extension_date][<%=id%>]', '<%=extensionDate%>', array('class'=>'form-control','encode'=>false,'disabled'=>'disabled','dataleaveid'=>'<%=id%>'));?></td>
        <td><?php echo CHtml::textArea('AsLeaveExtensionHistory[desc][<%=id%>]', null, array('class'=>'form-control','encode'=>false,'disabled'=>'disabled','dataleaveid'=>'<%=id%>')); ?></td>
        <td></td>
    </tr>
</script>

<script type="text/template" id="staff-discounted">
    <%
        if (_.isUndefined(waitDataList[id])){
            var waitData = '';
            var disabled = '';
        }else{
            var waitData = waitDataList[id].sumTxt;
            var disabled = 'disabled';
        }
    %>
    <tr>
        <td id="check-list-<%=id%>">
            <% if (waitData){%>
                <?php echo CHtml::checkBox("LeaveItem[leave_id][<%=id%>]",null,array('value'=>'<%=id%>','class'=>'J_check','encode'=>false,'id'=>'LeaveItem_leave_id_<%=id%>','disabled'=>'<%=disabled%>'));?>
            <%}else{%>
                <?php echo CHtml::checkBox("LeaveItem[leave_id][<%=id%>]",null,array('value'=>'<%=id%>','class'=>'J_check','encode'=>false,'id'=>'LeaveItem_leave_id_<%=id%>'));?>
            <%}%>
            <?php echo CHtml::hiddenField("LeaveItem[staff_uid][<%=id%>]", '<%=staff_uid%>',array('encode'=>false,'disabled'=>'disabled'));?>
        </td>
        <td><%=name%></td>
        <td><%=hoursTxt%> <span title="正在审核<%=waitData%>" class="label label-info"><%=waitData%></span></td>
        <td><%=startdate%>~<%=enddate%></td>
        <td><?php echo CHtml::textArea('LeaveItem[desc][<%=id%>]', null, array('class'=>'form-control','encode'=>false,'disabled'=>'disabled','dataleaveid'=>'<%=id%>')); ?></td>
        <td></td>
    </tr>
</script>