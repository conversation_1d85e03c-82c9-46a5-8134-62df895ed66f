<?php
	$sex = OA::getChildGenderList();
	$country = Country::model()->getData();
	$depPosdata = HrPosition::model()->findAll();	
	$depPos = CHtml::listData($depPosdata, 'id', (Yii::app()->language == "zh_cn") ?  'cn_name' : 'en_name');
	
 ?>
<div class="row">
    <div class="col-md-12">
        <div class="btn-group mb10">
            <button type="button" class="btn btn-primary" onclick="addNewStaff(0,0)">
                <?php echo Yii::t('ivyer', 'Register Staff');?></button>
        </div>
        <?php
        $this->widget('ext.ivyCGridView.BsCGridView', array(
            'id'=>'new-staff-grid',
            'dataProvider'=>$taskData['dataProvider'],
            'afterAjaxUpdate'=>'js:function(){head.Util.ajaxDel()}',
            'colgroups'=>array(
                array(
                    "colwidth"=>array(100,100,50,200,100,100,100, 100),
                )
            ),
            'columns'=>array(
                array(
                    'name'=>Yii::t('ivyer', 'English Name'),
                    'value'=>'$data->profile->first_name." ".$data->profile->last_name',
                ),
                array(
                    'name'=>'name',
                    'sortable'=>false,
                ),
                array(
                    'name'=>'profile.user_gender',
                    'value'=>'$data->profile->getGender()',
                ),
                array(
                    'header' => Yii::t('message', 'Official Email'),
                    'name'=>'email',
                    'sortable'=>false,
                ),
                array(
                    'name'=>Yii::t('labels', 'Position'),
                    'value'=>'$data->profile->occupation ? $data->profile->occupation->getName() : ""',
                ),
                array(
                    'name'=>Yii::t("labels",'Start Date'),
                    'value'=>'date("Y-m-d", $data->staff->startdate)',
                ),
                array(
                    'header' => Yii::t('labels', 'Status'),
//                    'name'=>'status',
                    // 'value'=> "Yii::t('message', 'Awaiting for approval')",
                    'value'=> array($this, 'getStatus'),
                    'type'=>'raw',
                ),
                array(
                    'name'=>Yii::t('global', 'Action'),
                    'type'=>'raw',
                    'value' => array($this, 'getButton'),
                ),
            ),
        )); ?>
    </div>
</div>

<?php
$this->branchSelectParams['extraUrlArray'] = array('//mcampus/staffProfile/index', 'task'=>'registration');
$this->renderPartial('//layouts/common/branchSelectBottom');
?>

<div  data-backdrop='static' class="modal fade" id="addstaff">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span onclick="quxiao()" aria-hidden="true">&times;</span></button>
                <h4 class="modal-title"><?php echo Yii::t('ivyer', 'Register Staff');?></h4>
            </div>
            <?php
            $form=$this->beginWidget('CActiveForm', array(
                'id'=>'newstaff-form',
                'action'=>$this->createUrl('saveNewStaff'),
                'htmlOptions'=>array('class'=>'J_ajaxForm form-horizontal', 'role'=>'form','enctype'=>"multipart/form-data"),
            ));
            ?>
            <div class="modal-body">

            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" onclick="quxiao()" data-dismiss="modal"><?php echo Yii::t("global", 'Cancel');?></button>
                <button type="button" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t("global", 'Submit');?></button>
            </div>
            <?php
            $this->endWidget();
            ?>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->



<div  data-backdrop='static' class="modal fade" id="addstaff_two">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span onclick="quxiao()" aria-hidden="true">&times;</span></button>
                <h4 class="modal-title"><?php echo Yii::t('ivyer', 'Register Staff');?></h4>
            </div>
            <?php
            $form=$this->beginWidget('CActiveForm', array(
                'id'=>'newstaff-forms',
                'action'=>$this->createUrl('auditemployee'),
                'htmlOptions'=>array('class'=>'J_ajaxForm form-horizontal', 'role'=>'form','enctype'=>"multipart/form-data"),
            ));
            ?>
            <div class="modal-body_two">

            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" onclick="quxiao()" data-dismiss="modal"><?php echo Yii::t("global", 'Cancel');?></button>
                <button type="button" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t("global", 'Submit');?></button>
            </div>
            <?php
            $this->endWidget();
            ?>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->

<script type="text/template" id="new-staff-template">
    <?php
    Yii::import('common.models.staff.*');
    $userLabels = User::model()->attributeLabels();
    $profileLabels = UserProfile::model()->attributeLabels();
    $staffLabels = Staff::model()->attributeLabels();
    ?>
    <input type="hidden" name="id" value="<%= uid%>">
    <div class="form-group">
        <label class="col-xs-3 control-label" for="UserProfile_first_name">* <?php echo $profileLabels['first_name']?></label>
        <div class="col-xs-9">
            <input class="form-control" name="UserProfile[first_name]" id="UserProfile_first_name" type="text" value="<%= firstname%>"placeholder="<?php echo Yii::t('message','Input pinyin for no English name');?>">
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 control-label" for="UserProfile_last_name">* <?php echo $profileLabels['last_name']?></label>
        <div class="col-xs-9">
            <input class="form-control" name="UserProfile[last_name]" id="UserProfile_last_name" type="text"  value="<%= lastname%>"placeholder="<?php echo Yii::t('message','Input pinyin for no English name');?>">
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 control-label" for="User_name"><?php echo $userLabels['name']?></label>
        <div class="col-xs-9">
            <input class="form-control" name="User[name]" id="User_name" type="text" value="<%= name%>" placeholder="<?php echo Yii::t('message','Leave blank for nothing');?>
            ">
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 control-label">* <?php echo $profileLabels['user_gender']?></label>
        <div class="col-xs-9">	
			<?php echo CHtml::dropDownList('UserProfile[user_gender]', '', $sex, array('class'=>'form-control', 'empty'=>Yii::t('global', 'Please Select')))?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 control-label">* <?php echo $profileLabels['nationality']?></label>
        <div class="col-xs-9">
            <?php echo CHtml::dropDownList('UserProfile[nationality]', '', $country, array('class'=>'form-control', 'empty'=>Yii::t('global', 'Please Select')))?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 control-label">* <?php echo $profileLabels['occupation_en']?></label>
        <div class="col-xs-9">
            <?php echo CHtml::dropDownList('UserProfile[occupation_en]', '', DepPosLink::model()->getDepPos($this->branchId), array('class'=>'form-control', 'empty'=>Yii::t('global', 'Please Select')))?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 control-label" for="User_email">* <?php echo Yii::t('message','Official Email');?></label>
        <div class="col-xs-9">
            <input class="form-control" name="User[email]" id="User_email" type="text"  value="<%= email%>" placeholder="<?php echo Yii::t('message','Official Email');?>" onblur="checkEmail()" data-toggle="popover" data-trigger="manual" data-placement="top" data-content="提示：您使用的邮箱不是公司邮箱">
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 control-label" for="Staff_pemail">* <?php echo $staffLabels['pemail']?></label>
        <div class="col-xs-9">
            <input class="form-control" name="Staff[pemail]" id="Staff_pemail"  type="text" value="<%= pemail%>"  placeholder="<?php echo Yii::t('message','Personal Email, for emergent contact only.');?>">
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 control-label" for="Staff_mobile_telephone">* <?php echo $staffLabels['mobile_telephone']?></label>
        <div class="col-xs-9">
            <input class="form-control" name="Staff[mobile_telephone]" id="Staff_mobile_telephone"  type="text" value="<%= mobile_telephone%>">
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 control-label" for="Staff_card_id">* <?php echo $staffLabels['card_id']?></label>
        <div class="col-xs-9">
            <input class="form-control" name="Staff[card_id]" id="Staff_card_id"  type="text" value="<%= card_id%>">
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 control-label" for="Staff_contract_type">* <?php echo $staffLabels['contract_type']?></label>
        <div class="col-xs-9">
            <?php echo CHtml::dropDownList('Staff[contract_type]', 'full_time', Staff::contractTypeList(), array('class'=>'form-control', 'empty'=>Yii::t("labels",'Contract Type')))?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 control-label" for="Staff_startdate">* <?php echo $staffLabels['startdate']?></label>
        <div class="col-xs-9">
            <input class="form-control" name="Staff[startdate]"  id="Staff_startdate" type="text" value="<%= startdate%>"  placeholder="<?php echo Yii::t('message','required');?>">
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 control-label" for="Staff_hiring"></label>
        <div class="col-xs-9">
            <span class="btn-danger">以下上传图片的最大为2MB!</span>
        </div>
    </div>
	<div class="form-group">
        <label class="col-xs-3 control-label" for="Staff_hiring">* <?php echo  Yii::t("labels",'Application Form') ?></label>
        <div class="col-xs-9">
			<% if(attachment && !_.isUndefined(attachment["hiring"]) && !_.isUndefined(attachment["hiring"]["photo"]) && attachment["hiring"]["photo"]){%>
				</p>已有附件</p>
			<% }%>
			<input type="file" name="Staff[hiring_file]" class="new_Employee_Input" id="Staff_hiring">
        </div>
    </div>
	<div class="form-group">
        <label class="col-xs-3 control-label" for="Staff_idcard">* <?php echo  Yii::t("labels",'Idcard') ?></label>
        <div class="col-xs-9">
			<% if(attachment && !_.isUndefined(attachment["idcard"]) && !_.isUndefined(attachment["idcard"]["photo"]) && attachment["idcard"]["photo"]){%>
            </p>已有附件</p>
			<% }%>
			<input  name="Staff[idcard_file]" class="new_Employee_Input" id="Staff_idcard" type="file" />
        </div>
    </div>
	<div class="form-group">
        <label class="col-xs-3 control-label" for="Staff_bankcard">* <?php echo  Yii::t("labels",'Bankcard') ?></label>
        <div class="col-xs-9">
			<% if(attachment && !_.isUndefined(attachment["bankcard"]) && !_.isUndefined(attachment["bankcard"]["photo"]) && attachment["bankcard"]["photo"]){%>
            </p>已有附件</p>
			<% }%>
			<input  name="Staff[bankcard_file]" class="new_Employee_Input" id="Staff_bankcard" type="file" />
        </div>
    </div><div class="form-group">
        <label class="col-xs-3 control-label" for="Staff_registration">* <?php echo  Yii::t("labels",'Registration Form') ?></label>
        <div class="col-xs-9">
			<% if(attachment && !_.isUndefined(attachment["registration"]) && !_.isUndefined(attachment["registration"]["photo"]) && attachment["registration"]["photo"]){%>
            </p>已有附件</p>
			<% }%>
			<input  name="Staff[registration_file]" class="new_Employee_Input" id="Staff_registration" type="file" />
        </div>
    </div>
	<div class="form-group">
        <label class="col-xs-3 control-label" for="Staff_dagree"><?php echo  Yii::t("labels",'Dagree Certificate') ?></label>
        <div class="col-xs-9">
			<% if(attachment && !_.isUndefined(attachment["dagree"]) && !_.isUndefined(attachment["dagree"]["photo"]) && attachment["dagree"]["photo"]){%>
            </p>已有附件</p>
			<% }%>
			<input  name="Staff[dagree_file]" class="new_Employee_Input" id="Staff_dagree" type="file"/>
			<input class="form-control" name="Staff[dagree]" type="text"
			value="<% if(attachment && !_.isUndefined(attachment["dagree"]) && !_.isUndefined(attachment["dagree"]["said"]) && attachment["dagree"]["said"]){%><%= attachment["dagree"]["said"]%><% }%>" placeholder="如果没有学历和学位证明请写明原因">
        </div>
    </div>
	<div class="form-group">
        <label class="col-xs-3 control-label" for="Staff_releaseLetter"><?php echo  Yii::t("labels",'Release Letter') ?></label>
        <div class="col-xs-9">
			<% if(attachment && !_.isUndefined(attachment["releaseLetter"]) && !_.isUndefined(attachment["releaseLetter"]["photo"]) && attachment["releaseLetter"]["photo"]){%>
            </p>已有附件</p>
			<% }%>
			<input  name="Staff[releaseLetter_file]" class="new_Employee_Input" id="Staff_releaseLetter" type="file"/>
			<input class="form-control" name="Staff[releaseLetter]"  type="text" value="<% if(attachment && !_.isUndefined(attachment["releaseLetter"]) && !_.isUndefined(attachment["releaseLetter"]["said"]) && attachment["releaseLetter"]["said"]){%><%= attachment["releaseLetter"]["said"]%><% }%>" placeholder="如果没有离职证明请写明原因">
        </div>
    </div>
	
	<div class="form-group">
        <label class="col-xs-3 control-label" for="Staff_other"><?php echo  Yii::t("labels",'Other Certificates') ?></label>
        <div class="col-xs-9">
			<% if(attachment && !_.isUndefined(attachment["other"]) && !_.isUndefined(attachment["other"]["photo"]) && attachment["other"]["photo"]){%>
            </p>已有附件</p>
			<% }%>
			<input type="file" name="Staff[other_file]" class="new_Employee_Input" id="Staff_other" >
        </div>
    </div>
</script>

<script type="text/template" id="new-staff-template-two">
    <?php
    Yii::import('common.models.staff.*');
    $userLabels = User::model()->attributeLabels();
    $profileLabels = UserProfile::model()->attributeLabels();
    $staffLabels = Staff::model()->attributeLabels();
    ?>
    <input type="hidden" name="id" value="<%= uid%>">
    <div class="form-group">
        <label class="col-xs-3 control-label" for="UserProfile_first_name">* <?php echo $profileLabels['first_name']?></label>
        <div class="col-xs-9">
			<div class="mt"><%= firstname%></div>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 control-label" for="UserProfile_last_name">* <?php echo $profileLabels['last_name']?></label>
        <div class="col-xs-9">
			<div class="mt"><%= lastname%></div>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 control-label" for="User_name"><?php echo $userLabels['name']?></label>
        <div class="col-xs-9">
			<div class="mt"><%= name%></div>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 control-label">* <?php echo $profileLabels['user_gender']?></label>
		<div class="col-xs-9">
			<div class="mt" id="sex"></div>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 control-label">* <?php echo $profileLabels['nationality']?></label>
        <div class="col-xs-9">
            <div class="mt" id="country"></div>	
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 control-label">* <?php echo $profileLabels['occupation_en']?></label>
        <div class="col-xs-9">
			<div class="mt" id="deppos"></div>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 control-label" for="User_email">* <?php echo Yii::t('message','Official Email');?></label>
        <div class="col-xs-9">
			<div class="mt"><%= email%></div>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 control-label" for="Staff_pemail">* <?php echo $staffLabels['pemail']?></label>
        <div class="col-xs-9">
			<div class="mt"><%= pemail%></div>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 control-label" for="Staff_mobile_telephone">* <?php echo $staffLabels['mobile_telephone']?></label>
        <div class="col-xs-9">
            <div class="mt"><%= mobile_telephone%></div>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 control-label" for="Staff_card_id">* <?php echo $staffLabels['card_id']?></label>
        <div class="col-xs-9">
            <input class="form-control" name="Staff[card_id]" id="Staff_card_id"  type="text" value="<%= card_id%>">
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 control-label" for="Staff_contract_type">* <?php echo $staffLabels['contract_type']?></label>
        <div class="col-xs-9">
            <?php echo CHtml::dropDownList('Staff[contract_type]', 'full_time', Staff::contractTypeList(), array('class'=>'form-control', 'empty'=>Yii::t("labels",'Contract Type')))?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 control-label" for="Staff_startdate">* <?php echo $staffLabels['startdate']?></label>
        <div class="col-xs-9">
            <div class="mt"><%= startdate%></div>
        </div>
    </div>
	<div class="form-group">
        <label class="col-xs-3 control-label" for="Staff_attachment">* <?php echo  Yii::t("labels",'Application Form') ?></label>
        <div class="col-xs-9">
			<% if(!_.isUndefined(attachment["hiring"]) && !_.isUndefined(attachment["hiring"]["photo"]) && attachment["hiring"]["photo"]){%>
				<p class="mt"><a href="<?php echo $this->createUrl('downloads');?>&id=<%= uid%>&attachment=hiring" target="_blank" class="btn btn-info btn-xs"><span class="glyphicon glyphicon-file"></span> 点击查看附件</a></p>
			<% }%>
        </div>
    </div>
	<div class="form-group">
        <label class="col-xs-3 control-label" for="Staff_attachment">* <?php echo  Yii::t("labels",'Idcard') ?></label>
        <div class="col-xs-9">
			<% if(!_.isUndefined(attachment["idcard"]) && !_.isUndefined(attachment["idcard"]["photo"]) && attachment["idcard"]["photo"]){%>
				<p class="mt"><a href="<?php echo $this->createUrl('downloads');?>&id=<%= uid%>&attachment=idcard" target="_blank" class="btn btn-info btn-xs"><span class="glyphicon glyphicon-file"></span> 点击查看附件</a></p>
			<% }%>			
        </div>
    </div>
	<div class="form-group">
        <label class="col-xs-3 control-label" for="Staff_attachment">* <?php echo  Yii::t("labels",'Bankcard') ?></label>
        <div class="col-xs-9">
			<% if(!_.isUndefined(attachment["bankcard"]) && !_.isUndefined(attachment["bankcard"]["photo"]) && attachment["bankcard"]["photo"]){%>
				<p class="mt"><a href="<?php echo $this->createUrl('downloads');?>&id=<%= uid%>&attachment=bankcard" target="_blank" class="btn btn-info btn-xs"><span class="glyphicon glyphicon-file"></span> 点击查看附件</a></p>
			<% }%>
        </div>
    </div>
	<div class="form-group">
        <label class="col-xs-3 control-label" for="Staff_attachment">* <?php echo  Yii::t("labels",'Registration Form') ?></label>
        <div class="col-xs-9">
			<% if(!_.isUndefined(attachment["registration"]) && !_.isUndefined(attachment["registration"]["photo"]) && attachment["registration"]["photo"]){%>
				<p class="mt"><a href="<?php echo $this->createUrl('downloads');?>&id=<%= uid%>&attachment=registration" target="_blank" class="btn btn-info btn-xs"><span class="glyphicon glyphicon-file"></span> 点击查看附件</a></p>
			<% }%>
        </div>
    </div>
	<div class="form-group">
        <label class="col-xs-3 control-label" for="Staff_attachment"><?php echo  Yii::t("labels",'Dagree Certificate') ?></label>
        <div class="col-xs-9">
			<% if(!_.isUndefined(attachment["dagree"]) && !_.isUndefined(attachment["dagree"]["photo"]) && attachment["dagree"]["photo"]){%>
				<p class="mt"><a href="<?php echo $this->createUrl('downloads');?>&id=<%= uid%>&attachment=dagree" target="_blank" class="btn btn-info btn-xs"><span class="glyphicon glyphicon-file"></span> 点击查看附件</a></p>
			<% }else{%>
				无附件
			<%}%>
			<% if(!_.isUndefined(attachment["dagree"]) && !_.isUndefined(attachment["dagree"]["said"]) && attachment["dagree"]["said"]){%>
				<div class="mt"><%= attachment["dagree"]["said"]%></div>
			<% }else{%>
				无说明
			<%}%>
        </div>
    </div>
	<div class="form-group">
        <label class="col-xs-3 control-label" for="Staff_attachment"><?php echo  Yii::t("labels",'Release Letter') ?></label>
        <div class="col-xs-9">
			<% if(!_.isUndefined(attachment["releaseLetter"]) && !_.isUndefined(attachment["releaseLetter"]["photo"]) && attachment["releaseLetter"]["photo"]){%>
				<p class="mt"><a href="<?php echo $this->createUrl('downloads');?>&id=<%= uid%>&attachment=releaseLetter" target="_blank" class="btn btn-info btn-xs"><span class="glyphicon glyphicon-file"></span> 点击查看附件</a></p>
			<% }else{%>
				无附件
			<%}%>
			<% if(!_.isUndefined(attachment["releaseLetter"]) && !_.isUndefined(attachment["releaseLetter"]["said"]) && attachment["releaseLetter"]["said"]){%>
				<div class="mt"><%= attachment["releaseLetter"]["said"]%></div>
			<% }else{%>
				无说明
			<%}%>
        </div>
    </div>
	<div class="form-group">
        <label class="col-xs-3 control-label" for="Staff_attachment"><?php echo  Yii::t("labels",'Other Certificates') ?></label>
        <div class="col-xs-9">
			<% if(!_.isUndefined(attachment["other"]) && !_.isUndefined(attachment["other"]["photo"]) && attachment["other"]["photo"]){%>
				<p class="mt"><a href="<?php echo $this->createUrl('downloads');?>&id=<%= uid%>&attachment=other" target="_blank" class="btn btn-info btn-xs"><span class="glyphicon glyphicon-file"></span> 点击查看附件</a></p>
			<% }else{ %>
				无其他附件上传
			<% } %>
        </div>
    </div>
	<div class="form-group">
        <label 	class="col-xs-3 control-label" for="Staff_attachment"><?php echo  Yii::t("labels",'Review the status') ?></label>
        <div class="col-xs-9 mt">
			<input type="radio" value="-1" name="level" id="Job" onclick="audits(1)" /><label  for="Job"><?php echo  Yii::t("labels",'Yes') ?></label >&nbsp
			<input type="radio" value="-3" name="level" id="Job_t" onclick="audits(0)"/><label for="Job_t"><?php echo  Yii::t("labels",'No') ?></label >
			<input class="form-control" id="audit" name="why" style="display:none;" placeholder="请写明原因">
        </div>
    </div>
</script>

<script>
	var sex = <?php echo  CJSON::encode($sex) ?>;
	var country = <?php  echo  CJSON::encode($country) ?>;
	var deppos = <?php  echo CJSON::encode($depPos) ?>;	
	function audits(id){
		if(id == 1){
			$('#audit').val("");
			$("#audit").css("display","none");	
		}else{
			$("#audit").css("display","block");	
		}
	}
    //检测是否为公司邮箱
    function checkEmail () {
        var email = $('#User_email').val();
        email = email.split('@');
        var str = email.slice(-1)
        if (str == 'ivygroup.org' || str =='ivyschools.com' || str =='daystarchina.cn') {
            $('#User_email').popover('hide');
        }else{
            $('#User_email').popover('show');
        }
        var t=setTimeout("$('#User_email').popover('hide')",3000)
    }

    function addNewStaff(uid,adint)
    {
        var ret = {
            uid : 0,			
            firstname: '',
            lastname: '',
            name: '',
            gender: '',
            nationality: '',
            occupation: '',
            email: '',
            pemail: '',
            startdate: '',
            attachment: '',
            detection: '',
            mobile_telephone: '',
            card_id: '',
            contract_type: '',
        };
        if(uid != 0){

            $.ajax({
                type: 'get',
                url: '<?php echo $this->createUrl('getNewstaffs')?>',
                async: false,
                data: {uid: uid},
                dataType: 'json'
            }).done(function(data){
                ret = data;
            });
        }
            $('#addstaff .modal-body').html(_.template($('#new-staff-template').html(), ret));
            $('#addstaff .modal-body #UserProfile_user_gender').val(ret.gender);
            $('#addstaff .modal-body #UserProfile_nationality').val(ret.nationality);
            $('#addstaff .modal-body #UserProfile_occupation_en').val(ret.occupation);
            $('#addstaff .modal-body #Staff_contract_type').val(ret.contract_type);
            $('#addstaff').modal();
            $('#Staff_startdate').datepicker({'dateFormat':'yy-mm-dd'});
            head.Util.ajaxDel();
    }

    function addNewStaffs(uid,adint)
    {
        var ret = {
            uid : 0,
            firstname: '',
            lastname: '',
            name: '',
            gender: '',
            nationality: '',
            occupation: '',
            email: '',
            pemail: '',
            startdate: '',
            attachment: '',
            detection: '',
            mobile_telephone: '',
            card_id: '',
            contract_type: '',
        };
        if(uid != 0){
            $.ajax({
                type: 'get',
                url: '<?php echo $this->createUrl('getNewstaff')?>',
                async: false,
                data: {uid: uid},
                dataType: 'json'
            }).done(function(data){
                ret = data;
            });
        }
            $('#addstaff_two .modal-body_two').html(_.template($('#new-staff-template-two').html(), ret));
            $('#addstaff_two .modal-body_two #sex').html(sex[ret.gender]);
            $('#addstaff_two .modal-body_two #country').html(country[ret.nationality]);
            $('#addstaff_two .modal-body_two #deppos').html(deppos[ret.occupation]);
            $('#addstaff_two').modal();
            $('#Staff_startdate').datepicker({'dateFormat':'yy-mm-dd'});
            head.Util.ajaxDel();
    }
    //审核新员工
    function checkNewStaff (uid) {
        $.ajax({
            url:'<?php echo $this->createUrl('checkNewStaff');?>',
            data:{uid: uid},
            type:'post',
            dataType:'json',
            success:function (data) {
                if (data.msg == 'success') {
                    cbDelNewStaff();
                };
            }
        });
    }

    // 新员工登记成功回调
    function cbAddNewStaff()
    {
        $.fn.yiiGridView.update('new-staff-grid');
        $('#addstaff').modal('hide');
    }
	function cbAddNewStaffs()
    {
        $.fn.yiiGridView.update('new-staff-grid');
        $('#addstaff_two').modal('hide');
    }
    // 新员工删除成功回调
    function cbDelNewStaff()
    {
        $.fn.yiiGridView.update('new-staff-grid');
    }
	function quxiao(){
		$("#J_fail_info").empty();
	}
</script>