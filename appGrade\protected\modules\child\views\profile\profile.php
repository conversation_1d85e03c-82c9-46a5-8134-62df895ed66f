<?php
$this->breadcrumbs=array(
	Yii::t("navigations","Profile")=>array('/child/profile'),
	Yii::t("navigations","Edit Profile"),
);?>

<h1><label><?php echo Yii::t("navigations",'Edit Profile');?></label></h1>

<p class="desc"><?php echo Yii::t("userinfo", 'You can edit the profiles and your home address here. Please keep this updated with your most recent information.'); ?></p>

	<?php
		$initialClass = ($t=='child')?'active':'';

		$this->widget('zii.widgets.CMenu',array(
		'id'=>'profile-filter',
		'htmlOptions'=>array('class'=>'status-filter'),
		'items'=>array(
			array('label'=>Yii::t("userinfo",'Child Profile'),   'url'=>array('//child/profile/profile',"t"=>"child"), 'itemOptions'=>array('class'=>$initialClass)),
			array('label'=>Yii::t("userinfo",'Parent Profile'),  'url'=>array('//child/profile/profile',"t"=>"parent")),
			array('label'=>Yii::t("userinfo",'Home Address'), 'url'=>array('//child/profile/profile',"t"=>"address")),
			array('label'=>Yii::t("userinfo",'Change Password'), 'url'=>array('//child/profile/profile',"t"=>"password")),
		),
	)); ?>
<div class="clear"></div>

<?php $this->renderPartial(sprintf("_profile_%s", $t), array("model"=>$model,"cfgs"=>$cfgs,"extra"=>$extra));?>

<?php
$nextUrl = $this->createUrl("//child/payment/summary",array("demo"=>"guide"));

Yii::import('ext.guide.class.*');
$this->widget('ext.guide.Guide', array(
    'options'=>array(
        'guides'=>array(

			0=>GuideItem::navGuide(10, null, true, "navigations_profile"),
			
			10 =>GuideItem::subNavGuide($nextUrl, null, "subnav_profile"),		
			

        )
    )
));
//endif;
?>