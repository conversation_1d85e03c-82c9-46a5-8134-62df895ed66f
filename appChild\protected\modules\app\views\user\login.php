<div class="container">

	<form class="form-signin" action="" id="loginForm">
<!--		<div>-->
<!--			<label for="inputEmail" class="sr-only">Email address</label>-->
<!--			<input type="text" class="form-control" id="username" name="UserLogin[username]" placeholder="请输入家长帐号" autofocus>-->
<!--		</div>-->
<!--		<div>-->
<!--			<label for="inputPassword" class="sr-only">Password</label>-->
<!--			<input type="password" class="form-control" id="password" name="UserLogin[password]" placeholder="请输入密码">-->
<!--		</div>-->
<!--		<button class="btn btn-lg btn-primary btn-block" type="button" id="signbtn">登录</button>-->
	</form>

</div> <!-- /container -->

<div style="position: fixed;bottom: 0;width: 100%;height: 50px;line-height: 50px;text-align: center;">
    <a href="https://beian.miit.gov.cn/" target="_blank" style="color: #999;">京ICP备12041446号</a>
</div>

<script>

	$('input#username, input#password').keyup(function(event){
		if(event.keyCode==13){
			$('#signbtn').click();
		}
	});

	$('#signbtn', '#loginForm').on('click', function(){
		$.ajax({
			type: 'POST',
			url: '<?php echo $this->createUrl('//user/login/ajaxLogin');?>',
			data: $('#loginForm').serialize(),
			dataType: 'json',
			beforeSend: function( xhr ){
				$('.loginForm div').removeClass('has-error');
			}
		}).done(function(data){
			if (data.state == 'fail'){
				for(var datum in data['msg']){
					$('#'+datum).parent('div').addClass('has-error');
				}
			}
			else if (data.state == 'success'){
				window.location.href = '<?php echo $this->createUrl('//app/user/home');?>';
			}
		});
	});
</script>