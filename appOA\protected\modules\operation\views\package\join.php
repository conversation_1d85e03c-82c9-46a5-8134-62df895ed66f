<style type='text/css'>
.bgimage
{
	background: url("/images/loading.gif")  no-repeat 50% 10%; 
}
</style>
<script type="text/javascript">
/*搜索产品*/
function searchProduct()
{
	var p = $('#search_product').val();
	if (p.length>=0)
	{
		$("#productIds").addClass("bgimage");
		$.ajax({
            type: "POST",
            url: "<?php echo Yii::app()->createUrl('/operations/package/searchProduct') ;?>",
            data: "title="+p,
            success: function(data) {
				$('option', $("#productIds")).remove();
				$("#productIds").removeClass("bgimage");
				$("#productIds").append(data);
    		}
        });
	}
}

/*添加产品*/
function addProduct()
{
	var packageId = $("#package_id").val();
	var productIds = $("#productIds").val();
	if (packageId && productIds)
	{
		$("#assignedProdcut").addClass("bgimage");
		$.ajax({
	        type: "POST",
	        url: "<?php echo Yii::app()->createUrl('/operations/package/assignProduct') ;?>",
	        data: "packageId=" + packageId + "&productIds=" + productIds,
	        success: function(data) {
				$('option', $("#assignedProdcut")).remove();
				$("#assignedProdcut").removeClass("bgimage");
				$("#assignedProdcut").append(data);
			}
	    });
	}
}

/*删除产品*/
function delProduct()
{
	var packageId = $("#package_id").val();
	var assignedProdcut = $("#assignedProdcut").val();
	if (packageId && assignedProdcut)
	{
		$("#assignedProdcut").addClass("bgimage");
		$.ajax({
	        type: "POST",
	        url: "<?php echo Yii::app()->createUrl('/operations/package/delProduct') ;?>",
	        data: "packageId=" + packageId + "&productIds=" + assignedProdcut,
	        success: function(data) {
				$('option', $("#assignedProdcut")).remove();
				$("#assignedProdcut").removeClass("bgimage");
				$("#assignedProdcut").append(data);
			}
	    });
	}
}
</script>
<h1><?php echo Yii::t('operations','为 "{code}" 添加产品', array('{code}'=>$model->getTitle()));?></h1>
<div style="text-align: center;">
	<div style="padding: 10px;">
		<div style="float: left;">
			<p style="padding-top:4px;"><input type="text" onkeyup="searchProduct();" style="width:210px;border:1px solid #ababab;height:22px;color:#585858;padding:0 4px 0 18px;background:url('/images/bg_nav_search.jpg') no-repeat scroll 0 0 transparent;" id="search_product" name="search_product"></p>
			<p style="padding-top: 10px;">
				<select id="productIds" style="width: 234px; height: 340px; border: 1px solid #ababab; color: #585858; padding: 4px;" multiple="multiple" size="1" name="productIds">
				
				</select>
			</p>
		</div>
		<div style="line-height: 100px; width: 96px; float: left; text-align: center; padding-top: 100px;">
			<p><span class="btn2"><span><button onclick="addProduct();" type="button" name="ajaxsubmit" style="width:80px;"><?php echo Yii::t('operations', 'Add')?>&gt;&gt;</button></span></span></p>
			<p style="padding-top: 20px;"><span class="btn2"><span>
			<button onclick="delProduct();" type="button" name="ajaxsubmit" style="width:80px;">&lt;&lt;<?php echo Yii::t('operations', 'Delete')?></button>
			</span></span></p>
		</div>
		<div style="float: right;">
			<p style="width: 210px; border: 1px solid #ababab; color: #585858; padding: 4px;"><?php echo Yii::t('operations', 'Assigned products')?></p>
			<p style="padding-top: 10px;">
				<select style="width: 234px; height: 340px; border: 1px solid #ababab; color: #585858; padding: 4px;" multiple="multiple" size="1" id="assignedProdcut" name="assignedProdcut">
				<?php echo $str;?>
				</select>
			</p>
		</div>
		<div style="clear: both;"></div>
	</div>
</div>
<input type="hidden" name="package_id" id="package_id" value="<?php echo $model->id;?>">