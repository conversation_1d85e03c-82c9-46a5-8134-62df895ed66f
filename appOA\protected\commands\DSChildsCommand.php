<?php
//批量导入DS孩子
class DSChildsCommand extends CConsoleCommand{

    public function init(){
		Yii::import('common.models.child.DsChildList');
	}

    public function actionImport(){

        $childsModels = DsChildList::model()->findAll('status=:status',array(':status'=>0));
        if (!empty($childsModels)){
            foreach ($childsModels as $childsModel){
                //查询孩子是否之前在IVY学校上过幼儿园（邮箱与手机）
                $emails = array();
                $phones = array();
                $emailExist = false;
                $mobileExist = false;
                $check = false;
                //判断手机
                if ($childsModel->mother_phone){
                    $phones['motherPhone'] = $childsModel->mother_phone;
                }

                if ($childsModel->father_phone){
                    $phones['fatherPhone'] = $childsModel->father_phone;
                }

                if (count($phones)){
                    foreach ($phones as $val){
                        if ($val){
                            if(!CommonUtils::checkMobile(trim($val))){
                                $childsModel->error = "手机格式不正确";
                                $childsModel->status = 1;
                                $childsModel->save(false);
                                echo $val.".....\n";
                                echo $childsModel->id."....\n";
                                echo "Error Phone Format.";
                                $check = true;
    //                            continue 2;
                            }
                        }
                    }
                    $criter = new CDbCriteria();
                    $criter->compare('mphone',$phones);
                    $mobileExist = IvyParent::model()->exists($criter);
                }

                //判断邮件
                if ($childsModel->mother_email){
                    $emails['motherEmail'] = $childsModel->mother_email;
                }
                if ($childsModel->father_email){
                    $emails['fatherEmail'] = $childsModel->father_email;
                }
                if (count($emails) || count($phones)){
                    //判断邮箱格式
                    if (count($emails)){
                        $valid = new CEmailValidator;
                        foreach ($emails as $val){
                            if ($val){
                                if (!$valid->validateValue(trim($val))){
                                    $childsModel->error = "邮件格式不正确";
                                    $childsModel->status = 1;
                                    $childsModel->save();
                                    echo $val.".....\n";
                                    echo $childsModel->id."....\n";
                                    echo "Error Email Format.";
                                    $check = true;
    //                                continue 2;
                                }
                            }
                        }
                    }

                    if (count($phones) && count($emails)){
                        $emailAndphones = $emails+$phones;
                    }elseif(count($phones) && !count($emails)){
                        $emailAndphones = $phones;
                    }elseif(!count($phones) && count($emails)){
                        $emailAndphones = $emails;
                    }
                    $criter = new CDbCriteria();
                    $criter->compare('email',$emailAndphones);
//                    $criter->compare('isstaff', 0);
                    $emailExist = User::model()->exists($criter);
                }


                if ($emailExist == true || $mobileExist == true){

                    //查询孩子的学校ID，向大表里保存
                    $criter = new CDbCriteria;
                    $criter->compare('email', $emailAndphones);
                    $userModel = User::model()->find($criter);
                    if (!empty($userModel)){
                        $criter = new CDbCriteria;
                        $criter->condition = 'mid='.$userModel->uid.' or fid='.$userModel->uid;
                        $childBasicModel = ChildProfileBasic::model()->findAll($criter);
                        if (!empty($childBasicModel)){
                            foreach ($childBasicModel as $childVal){
                                if ($childVal->birthday == strtotime($childsModel->birthday)){
                                    $childsModel->childid = $childVal->childid;
                                    $childsModel->schoolid = $childVal->schoolid;
                                }
                            }

                        }
                    }
                    //查询父母表根据孩子查询学校ID，向大表保存
                    if (count($phones)){
                        $criter = new CDbCriteria();
                        $criter->compare('mphone',$phones);
                        $parentModel = IvyParent::model()->find($criter);
                        if (!empty($parentModel)){
                            $criter = new CDbCriteria;
                            $criter->condition = 'mid='.$parentModel->pid.' or fid='.$parentModel->pid;
                            $childBasicModel = ChildProfileBasic::model()->findAll($criter);
                            if (!empty($childBasicModel)){
                                foreach ($childBasicModel as $childVal){
                                    if ($childVal->birthday == strtotime($childsModel->birthday)){
                                        $childsModel->childid = $childVal->childid;
                                        $childsModel->schoolid = $childVal->schoolid;
                                    }
                                }
                            }
                        }
                    }

                    $childsModel->error = "手机或邮箱已存在系统里";
                    $childsModel->status = 1;
                    $childsModel->save(false);
                    print_r($emailAndphones)."...\n";
                    print_r($phones)."...\n";
                    echo "Email or Phone Existing system"."...\n";
                    echo $childsModel->id."....";
                    $check = true;
//                    continue;
                }
                if(!count($emails) && !count($phones)){
                    $childsModel->error = "家长邮箱与手机都没有提供";
                    $childsModel->status = 1;
                    $childsModel->save(false);
                    echo "Email and phone not filled."."...\n";
                    echo $childsModel->id."....";
                    $check = true;
//                    continue;
                }
                if ($check === false){
                    //处理孩子信息
                    $childModel = new ChildProfileBasic();
                    $childModel->seniority = 1;
                    $childModel->family_id = 0;
                    $childModel->barcode = $childsModel->student_number;
                    $childModel->birthday = strtotime($childsModel->birthday);
                    $childModel->birthday_search = date('Y-m-d',$childModel->birthday);
                    $childModel->name_cn = $childsModel->name_cn;
                    $childModel->first_name_en = ucwords($childsModel->first_name);
                    $childModel->middle_name_en = ucwords($childsModel->middle_name);
                    $childModel->last_name_en = ucwords($childsModel->last_name);
                    $childModel->nick = $childsModel->nickname;
                    $childModel->gender = $this->getGenderRelations($childsModel->gender);
                    $childModel->country = $this->getCountryRelations($childsModel->nationality);
                    $childModel->lang = $this->getLanguageRelations($childsModel->native_language);
                    $childModel->schoolid = "BJ_DS";
                    $childModel->status = ChildProfileBasic::STATS_REGISTERED;
                    $childModel->fid = 0;
                    $childModel->mid = 0;
                    $childModel->updated_timestamp = time();
                    $childModel->created_timestamp = time();
                    $childModel->create_uid = 0;
                    $childModel->compound = $childsModel->address_chinese;
                    if ($childModel->save()){
                        //保存孩子父母信息
                        //father
                        $fid = 0;
                        if (isset($phones['fatherPhone']) || isset($emails['fatherEmail'])){
                            $default_password = CommonUtils::randPass();
                            $pModel = new User();
                            $pModel->email = isset($emails['fatherEmail']) ? $emails['fatherEmail'] : $phones['fatherPhone'];
                            $pModel->iniPassword = $pModel->pass = $default_password;
                            $pModel->mphone = isset($phones['fatherPhone']) ? $phones['fatherPhone'] : 0;
                            $pModel->pass = md5($pModel->pass);
                            $pModel->level = 1;
                            $pModel->user_regdate = time();
                            $pModel->timezone_offset = '8.0';
                            if (!$pModel->save()){
                                print_r($childModel->childid);
                                print_r($pModel->getErrors());
                                exit;
                            }
                            $pModel->profile = new UserProfile;
                            $pModel->profile->uid = $pModel->uid;
                            $pModel->profile->user_gender = 1;
                            $pModel->profile->first_name = $childsModel->father_first_name;
                            $pModel->profile->last_name = $childsModel->father_last_name;
                            if (!$pModel->profile->save()){
                                print_r($childModel->childid);
                                print_r($pModel->profile->getErrors());
                                exit;
                            }

                            $otherUser = IvyParent::model()->findByPk($pModel->uid);
                            $pModel->parent = new IvyParent;
                            $pModel->parent->setScenario('staffCreate');
                            $pModel->parent->pid = $pModel->uid;
                            $pModel->parent->cn_name = $childsModel->father_chinese_name;
                            $pModel->parent->en_firstname = $childsModel->father_first_name;
                            $pModel->parent->en_lastname = $childsModel->father_last_name;
                            $pModel->parent->password_text = $default_password;
                            $pModel->parent->gender = 1;
                            $pModel->parent->mphone = $pModel->mphone;
                            $pModel->parent->childs = $otherUser === null ? serialize(array($childModel->childid)) : $otherUser->childs;
                            $pModel->parent->family_id = md5($childModel->childid);
                            if (! $pModel->parent->save()){
                                print_r($childModel->childid);
                                print_r($pModel->parent->getErrors());
                                exit;
                            }

                            $pModel->profilesite = new UserProfileSite;
                            $pModel->profilesite->uid = $pModel->uid;
                            $pModel->profilesite->save();
                            if (! $pModel->profilesite->save()){
                                print_r($childModel->childid);
                                print_r($pModel->profilesite->getErrors());
                                exit;
                            }
                            $pModel->createParentRole();

                            $fid = $pModel->parent->pid;
                        }
                        //mather
                        $mid = 0;
                        if (isset($phones['motherPhone']) || isset($emails['motherEmail'])){
                            $default_password = CommonUtils::randPass();
                            $pModel = new User();
                            $pModel->email = isset($emails['motherEmail']) ? $emails['motherEmail'] : $phones['motherPhone'];
                            $pModel->iniPassword = $pModel->pass = $default_password;
                            $pModel->mphone = isset($phones['motherPhone']) ? $phones['motherPhone'] : 0;
                            $pModel->pass = md5($pModel->pass);
                            $pModel->level = 1;
                            $pModel->user_regdate = time();
                            $pModel->timezone_offset = '8.0';
                            if (!$pModel->save()){
                                print_r($childModel->childid);
                                print_r($pModel->getErrors());
                                exit;
                            }

                            $pModel->profile = new UserProfile;
                            $pModel->profile->uid = $pModel->uid;
                            $pModel->profile->user_gender = 2;
                            $pModel->profile->first_name = $childsModel->mother_first_name;
                            $pModel->profile->last_name = $childsModel->mother_last_name;
                            if (!$pModel->profile->save()){
                                print_r($childModel->childid);
                                print_r($pModel->profile->getErrors());
                                exit;
                            }

                            $otherUser = IvyParent::model()->findByPk($pModel->uid);
                            $pModel->parent = new IvyParent;
                            $pModel->parent->setScenario('staffCreate');
                            $pModel->parent->pid = $pModel->uid;
                            $pModel->parent->cn_name = $childsModel->mother_chinese_name;
                            $pModel->parent->en_firstname = $childsModel->mother_first_name;
                            $pModel->parent->en_lastname = $childsModel->mother_last_name;
                            $pModel->parent->password_text = $default_password;
                            $pModel->parent->gender = 2;
                            $pModel->parent->mphone = $pModel->mphone;
                            $pModel->parent->childs = $otherUser === null ? serialize(array($childModel->childid)) : $otherUser->childs;
                            $pModel->parent->family_id = md5($childModel->childid);
                            if (!$pModel->parent->save()){
                                print_r($childModel->childid);
                                print_r($pModel->parent->getErrors());
                                exit;
                            }

                            $pModel->profilesite = new UserProfileSite;
                            $pModel->profilesite->uid = $pModel->uid;
                            if (!$pModel->profilesite->save()){
                                print_r($childModel->childid);
                                print_r($pModel->profilesite->getErrors());
                                exit;
                            }
                            $pModel->createParentRole();

                            $mid = $pModel->parent->pid;
                        }

                        $childModel->fid = $fid;
                        $childModel->mid = $mid;
                        $childModel->family_id = md5($childModel->childid);
                        if (!$childModel->save()){
                            print_r($childModel->childid);
                            print_r($childModel->getErrors());
                            exit;
                        }
                        //HomeAddress
                        Yii::import('common.models.child.HomeAddress');
                        $homeModel = new HomeAddress;
                        $homeModel->childid = $childModel->childid;
                        $homeModel->en_address = ($childsModel->address_english) ? $childsModel->address_english : $childsModel->address_chinese;
                        $homeModel->cn_address = $childsModel->address_chinese;
                        $homeModel->en_telephone = $childsModel->home_no;
                        $homeModel->fid = $fid;
                        $homeModel->mid = $mid;
                        $homeModel->family_id = $childModel->family_id;
                        if (!$homeModel->save(false)){
                            print_r($childModel->childid);
                            print_r($homeModel->getErrors());
                            exit;
                        }
                        //杂项
                        Yii::import('common.models.child.ChildMisc');
                        $childMisc = new ChildMisc();
                        $childMisc->childid = $childModel->childid;
                        $childMisc->ucontact = $childsModel->emergency_contact;
                        $childMisc->ucontact =  $childMisc->ucontact.$childsModel->other_emergency_contact;
                        $childMisc->allergy = $childsModel->allergies;
                        $childMisc->physical = $this->getHealth($childsModel->healthcheck);
                        $childMisc->privacy = serialize(array('father_email','mother_email','mother_mobile','father_mobile','home_phone'));
                        if (!$childMisc->save(false)){
                            print_r($childModel->childid);
                            print_r($childMisc->getErrors());
                            exit;
                        }
                        $childsModel->status = 1; //更改状态
                        $childsModel->childid = $childModel->childid; //更改状态
                        $childsModel->save(FALSE);

                    }else{
                        $childModel->error = $childModel->getErrors();
                        print_r($childModel->id."............\n");
                        print_r($childModel->getErrors());exit;
                    }
                    echo '.';
                }else{
                    continue;
                }
            }
        }

    }

    //国家
    public function getCountryRelations($id){
        $id = strtolower($id);
        $ret = array(
            "at" =>10,
            "au" =>9,
            "ca" =>31,
            "cn" =>36,
            "cn/en" =>36,
            "de" =>66,
            "dk" =>48,
            "dutch" =>125,
            "fr" =>61,
            "gb" =>187,
            "german" =>66,
            "hk" =>175,
            "in" =>78,
            "it" =>84,
            "jo" =>87,
            "jp" =>86,
            "my" =>106,
            "nl" =>125,
            "no" =>132,
            "nz" =>126,
            "pt" =>142,
            "se" =>172,
            "sg" =>160,
            "south african" =>165,
            "uk" =>187,
            "us" =>188,
        );

        return ($id) ? $ret[$id] : '';
    }

    //语言
    public function getLanguageRelations($id){
        $id = strtolower($id);
        $ret = array(
            "cantonese" =>76,
            "chinese" =>1,
            "chinese/arabic" =>1,
            "chinese/english" =>1,
            "danish" =>71,
            "dutch" =>0,
            "farsi" =>0,
            "english" =>2,
            "english,danish"=>2,
            "english/chinese" =>2,
            "french" =>65,
            "german" =>71,
            "india" =>70,
            "italian" =>69,
            "japanese" =>67,
            "norwegian" =>0,
            "portuguese" =>0,
            "swedish&spannish" =>79,
        );
        return ($id) ? (isset($ret[$id]) ? $ret[$id] : 0) : '';
    }

    //性别
    public function getGenderRelations($id){
        $id = strtolower($id);
        $ret = array(
            'm'=>1,
            'f'=>2,
        );
        return ($id) ? $ret[$id] : '';
    }

    //健康体检
    public function getHealth($id){
        $id = strtolower($id);
        $ret = array(
            'no'=>0,
            '没填'=>0,
            'add'=>1,
        );
        return ($id) ? (isset($ret[$id]) ? $ret[$id] : 0) : 0;
    }

}