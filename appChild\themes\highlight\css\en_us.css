body, .ui-widget
{
	font: normal 10pt "Trebuchet MS","Microsoft Yahei",Arial,Helvetica,sans-serif !important;
}
.sf-menu .mainitem{
    width: 106px;
	text-align: left;
}
.sf-menu a.sf-with-ul {
	padding-left: 1.5em;
	text-align: left;
}
.key-reminder h5.title{
	margin:0;
	padding:0;
	height: 35px;
	width: 290px;
	position: absolute;
	background:url("../images/keyreminder/title_en_bg.png") no-repeat left top transparent;
	left: 46px;
	top: 20px;
}
.key-reminder:hover h5.title{
	background:url("../images/keyreminder/title_en_bg.png") no-repeat left -35px transparent;
}
.week-misc{
	background: url("../images/weekmisc_en.png") no-repeat 1px top transparent;
}
.week-misc .title{
	background: url("../images/week_title_en.png") no-repeat 1px top transparent;
}
.week-misc:hover .title{
	background: url("../images/week_title_en.png") no-repeat 1px -38px transparent;
}
.week-misc .menu:hover a{
	background: url("../images/weekmisc_hover_en.png") no-repeat left top transparent;	
}
.week-misc .calendar:hover a{
	background: url("../images/weekmisc_hover_en.png") no-repeat left -60px transparent;	
}
.follow-us{
	display: none;
}
#birthday-countdown{
	background:url("../images/birthday_bg_en.png") no-repeat left top transparent;
}
.birthday-single-day{
	background:url("../images/birthday_bg_single_en.png") no-repeat left top transparent !important;
}
#birthday-countdown .num-days{
    left: 145px;
	top: 0px;
}
#birthday-countdown .num-year{
    color: #FFFFFF;
    font-family: simhei;
    font-size: 70px;
    height: 80px;
    left: 260px;
    line-height: 80px;
    position: absolute;
    text-align: center;
    top: 45px;
    width: 80px;
}

.n-content .txt{left: 20px !important; bottom: 8px !important; width: 570px !important;font-size: 12px !important; line-height: 20px !important;}