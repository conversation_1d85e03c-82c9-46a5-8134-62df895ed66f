<?php
//$labelMapping = IvyStatsItem::getFinanceType();
//$this->renderPartial('_finance_data_template');

?>


<div class="container-fluid dashboard-page" id="container-general"></div>

<div class="modal" id="overall-progress">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-body">
                <div class="progress">
                    <div class="progress-bar" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%;">
                        0%
                    </div>
                </div>
                <div class="text-center hint">Loading Data...</div>
            </div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->

<?php
//$this->renderPartial('_dashboard_footer_campus_list');
?>

    <?php
    $_citys = Yii::app()->db->createCommand()
        ->select('*')
        ->from('ivy_diglossia')
        ->where('category_id = 7')
        ->order('entitle ASC')
        ->queryAll();

    foreach($_citys as $_c){
        $citys[$_c['diglossia_id']] = array(
            'en' => $_c['entitle'],
            'cn' => $_c['cntitle']
        );
    }

    $_allBranch = $this->getAllBranch(null);
    $_allBranch['BJ_DS']['group'] = '20';
    $branchArray = array();
    foreach($_allBranch as $_b){
        $_b['link'] = $this->createUrl('//charts/default/dashboard', array('schoolid'=>$_b['id']));
        $branchArray[] = $_b;
    }
    ?>

<script>
    var baseThemeUrl = "<?php echo Yii::app()->theme->baseUrl; ?>";
    var branchObjs = <?php echo CJSON::encode($_allBranch);?>;
    var cityObjs = <?php echo CJSON::encode($citys);?>;
    var branchTypeMapping = {50:'IBS', 30:'MIK', 20: 'IBS', 10: 'IA'};
    var branchList = <?php echo CJSON::encode($branchArray);?>;
    var currentBranchId = '';
    var branchListColl = new Backbone.Collection;
    var getDataRequest; //首页请求；请求时进度条显示
    var getCampusDataRequest; //校园信息请求；请求时进度条显示
    var specifiedDate='<?php echo date('Y-m-d');?>';    //显示这天的数据
    var dateObject = new Date();
    var todayDate = dateObject.getFullYear()+'-'+(dateObject.getMonth()+1)+'-'+dateObject.getDate();
    var financeData = {}; //财务数据
    var financeSelectedYear = {}; //财务数据

    //默认页面
    function loadGenealInfo(date){

        getDataRequest = [
            {
                url:"<?php echo Yii::app()->createUrl('//charts/default/generateData',array('actionType'=>'getTPL')) ;?>",
                data: 'type=general&placeholder=container-general&date='+date
            },
            {
                url:"<?php echo Yii::app()->createUrl('//charts/default/generateData',array('actionType'=>'getJSON')) ;?>",
                varName: 'test1',
                data: 'type=general&date='+date
            },
            {
                url:"<?php echo Yii::app()->createUrl('//charts/default/generateData',array('actionType'=>'getJSON')) ;?>",
                varName: 'test2',
                data: 'type=target&date='+date
            }
        ];

        head.load("<?php echo Yii::app()->theme->baseUrl; ?>/js/pages/dashboard.general.js", function(){
            getData();

            <?php if(isset($_GET['schoolid'])):?>
            setTimeout(function () {
                showCampus("<?php echo $_GET['schoolid'];?>");
                loadCampusInfo({schoolid:"<?php echo $_GET['schoolid'];?>"}, $('.campus-actions a').eq(0));
            }, 500)
            <?php endif;?>
        });
    }
    loadGenealInfo(specifiedDate);

    //校园财务数据页面
    function loadCampusInfo(params,obj){
        getCampusDataRequest = {
            finance: [{
                url: "<?php echo Yii::app()->createUrl('//charts/default/generateCampusFinance',array('actionType'=>'getTPL')) ;?>",
                data: params
            },
            {
                url: "<?php echo Yii::app()->createUrl('//charts/default/generateCampusFinance',array('actionType'=>'getJSON')) ;?>",
                data: params
            }],
            enroll:[{
                url: "<?php echo Yii::app()->createUrl('//charts/default/generateCampusEnroll',array('actionType'=>'getTPL')) ;?>",
                data: params
            },
            {
                url: "<?php echo Yii::app()->createUrl('//charts/default/attendance',array('actionType'=>'count')) ;?>",
                data: {schoolid: params.schoolid, specifiedDate: specifiedDate}
            },
            {
                url: "<?php echo Yii::app()->createUrl('//charts/default/generateCampusEnroll',array('actionType'=>'getJSON')) ;?>",
                data: {schoolid: params.schoolid, specifiedDate: specifiedDate}
            }
            ],
            report:[{
                url: "<?php echo Yii::app()->createUrl('//charts/default/generateCampusReport',array('actionType'=>'getTPL')) ;?>",
                data: params
            },
            {
                url: "<?php echo Yii::app()->createUrl('//charts/default/generateCampusReport',array('actionType'=>'getJSON')) ;?>",
                data: {schoolid: params.schoolid, specifiedDate: specifiedDate}
            },
            {
                url: "<?php echo Yii::app()->createUrl('//charts/default/generateCampusLunch',array('actionType'=>'getJSON')) ;?>",
                data: {schoolid: params.schoolid, specifiedDate: specifiedDate}
            },
            {
                url: "<?php echo Yii::app()->createUrl('//charts/default/visit',array('actionType'=>'getJSON')) ;?>",
                data: {schoolid: params.schoolid, specifiedDate: specifiedDate}
            }],
            cost:[{
                url: "<?php echo Yii::app()->createUrl('//charts/default/generateCampusCost',array('actionType'=>'getTPL')) ;?>",
                data: params
            },
            {
                url: "<?php echo Yii::app()->createUrl('//charts/default/generateCampusCost',array('actionType'=>'getJSON')) ;?>",
                data: params
            }]
        };
        var _targetDataType = obj.attr('dataType');
        obj.siblings().removeClass('active');
        obj.addClass('active');
        switch (_targetDataType){
            case 'enroll':
                head.load("<?php echo Yii::app()->theme->baseUrl; ?>/js/pages/dashboard.campus.enroll.js?t=<?php echo time();?>", function(){
                    getCampusEnrollData(params['schoolid']);
                });
                break;
            case 'finance':
                head.load("<?php echo Yii::app()->theme->baseUrl; ?>/js/pages/dashboard.campus.finance.js?t=<?php echo time();?>", function(){
                    getCampusFinanceData(params['schoolid']);
                });
                break;
            case 'report':
                head.load("<?php echo Yii::app()->theme->baseUrl; ?>/js/pages/dashboard.campus.report.js?t=<?php echo time();?>", function(){
                    getCampusReportData(params['schoolid']);
                });
                break;
            case 'cost':
                head.load("<?php echo Yii::app()->theme->baseUrl; ?>/js/pages/dashboard.campus.cost.js?t=<?php echo time();?>", function(){
//                    getCampusFinanceData(params['schoolid']);
                    alert(_targetDataType);
                });
                break;
        }
    }

    function echoA(num, childids)
    {
        if(num > 0 && num<800){
            var rdm = Math.floor(Math.random()*100000);
            return "<a id='J_"+rdm+"' href='javascript:;' cids='"+childids+"' onclick='showChildren(this)'>"+num+"</a>";
        }
        else{
            return num;
        }
    }

    var counter=[];
    var tmpid='';
    function showChildren(_this)
    {
        var cids = $(_this).attr('cids');
        var _id = $(_this).attr('id');

        if(tmpid != '' && tmpid != _id){
            $('#'+tmpid).popover('hide');
        }

        tmpid = _id;
        if(!in_array(_id, counter) && cids.length>0){
            var tourl = $(_this).attr('tourl') ? $(_this).attr('tourl') : '';
            $.get('<?php echo Yii::app()->controller->createUrl('showChildren')?>', {cids: cids, tourl: tourl}, function(data){
                $(_this).popover({placement:'auto',trigger:'focus',html:true,title:'孩子列表<button type="button" class="close">×</button>',content:data,trigger:'click'});
                $('.close').live('click',function(){
                    $(_this).popover('hide');
                });
                $(_this).popover('toggle');
                counter.push(_id);
            });
        }
    }
</script>

<style>
    .popover{
        max-width: 1000px !important;
        z-index: 9999;
    }
</style>
