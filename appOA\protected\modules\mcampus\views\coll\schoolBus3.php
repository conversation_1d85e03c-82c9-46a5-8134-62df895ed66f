<div id='homeAddress'>
    <div class="progress">
        <div class="progress-bar progress-bar-success" :style="'width:'+ parsefloat(filled/total) +'%;'">
            <span v-if='filled!=0' :title="filled+'/'+total">{{parsefloat(filled/total)}}% ({{filled}}/{{total}})</span>
            <span v-else>0% ({{filled}}/{{total}})</span>
        </div>
        <!--        <div class="progress-bar progress-bar-warning progress-bar-striped" style="'width:0%">-->
        <!--            <span >0% (0/--><?php //echo $data['count']?><!--)</span>-->
        <!--        </div>-->
        <!--        <div class="progress-bar progress-bar-danger" :style="'width:'+ parsefloat(unfilled/total) +'%;'">-->
        <!--            <span v-if='unfilled!=0' :title="unfilled+'/'+total">{{parsefloat(unfilled/total)}}% ({{unfilled}}/{{total}})</span>-->
        <!--            <span v-else>0% ({{unfilled}}/{{total}})</span>-->
        <!--        </div>-->
    </div>
    <div class='pull-left' id="nameSearchComp"></div>
    <p class='pull-right'>
        <button type="button" class="btn btn-default btn-sm"  v-if='type=="filled"' :disabled='btnCon' @click='batchPass("modal")'>批量通过</button>
        <button type="button" class="btn btn-default btn-sm ml10"  @click='exportTab()'><?php echo Yii::t('user', 'Export') ?></button>
    </p>
    <div class='clearfix'></div>
    <div class='relative'>
        <div class='loading' v-show='loading'>
            <span></span>
        </div>
        <ul class="nav nav-tabs" role="tablist">
            <li role="presentation" class="active"><a href="#home" aria-controls="home" role="tab" data-toggle="tab"  @click='getInfo("filled")'>已填写 <span class="badge">{{filled}}</span></a></li>
            <li role="presentation"><a href="#unstart" aria-controls="unstart" role="tab" data-toggle="tab" @click='getInfo("unfilled")'>未填写 <span class="badge badge-error">{{unfilled}}</span></a></li>
        </ul>
        <div class="tab-content">
            <div role="tabpanel" class="tab-pane active" id="home">
                <table class="table table-bordered mt20" id='filled' v-show='list.length!=0'>
                    <thead>
                    <tr>
                        <th width='30' class="nosort" >
                            <input type="checkbox" id="inlineCheckbox1" value="option1"  v-model='all'  @change='checkAll($event)'>
                        </th>
                        <th width='80'><?php echo Yii::t('global', 'Name') ?></th>
                        <th width='100'><?php echo Yii::t('labels', 'Class') ?></th>
                        <th width='50'>乘坐班车</th>
                        <th width='50'><?php echo Yii::t('reg','Newly applied bus student')?></th>
                        <th width='70'><?php echo Yii::t('reg', 'Method (Round Trip/Morning Only/Afternoon Only)') ?></th>
                        <th width='50'><?php echo Yii::t('reg','Any changes on bus stop')?></th>
                        <th width='50'><?php echo Yii::t('reg', 'Apply New Pick-up/Drop-off Point') ?></th>
                        <th width='120'>乘坐地点</th>
                        <!-- <th ><?php echo Yii::t('event', 'Signature') ?></th> -->
                        <th width='50'><?php echo Yii::t('labels', 'Status') ?></th>
                        <th width='100'>提交时间</th>
                        <th width='80' class="nosort"><?php echo Yii::t('global', 'Action') ?></th>
                    </tr>
                    </thead>
                    <tbody v-if='isShow'>
                    <tr v-for='(item,index) in list'>
                        <th>
                            <input type="checkbox" id="inlineCheckbox1" :value="item.childid" v-model='child_ids'  @change='childCheck($event)'>
                        </th>
                        <td >
                            <a href="javascript:;"  @click="overalls(item.childid)">
                                {{item.name}}
                            </a>
                        </td>
                        <td > {{item.class_name}}</td>
                        <td >{{item.step_data.need_bus==1?"是":"否"}} </td>
                        <template v-if="item.step_data.need_bus==1">
                            <td >{{item.step_data.new_bus==2?"是":"否"}} </td>
                            <td >{{item.step_data.journey==1?"<?php echo Yii::t('reg', 'One-way journey TO SCHOOL') ?>":item.step_data.journey==2?"<?php echo Yii::t('reg', 'One-way journey BACK HOME') ?>":"<?php echo Yii::t('reg', 'Two-way journey') ?>"}} </td>
                            <td v-if="item.step_data.new_bus==2">
                                --
                            </td>
                            <td v-else>
                                {{item.step_data.change_ride==2?"是":"否"}}
                            </td>
                            <td >{{item.step_data.customize==1?"是":"否"}} </td>
                            <td >{{item.step_data.parking}} </td>
                            <!-- <td ><img :src="item.step_data.sign_url" alt="" class='signImg'> </td> -->
                        </template>
                       <template v-else>
                           <td >--</td>
                           <td >--</td>
                           <td >--</td>
                           <td >--</td>
                           <td >--</td>
                       </template>
                        <td >
                            <i class='glyphicon glyphicon-remove-sign' v-if='item.step_status==3' style='color:#D5514E'></i>
                            <i class='glyphicon glyphicon-ok-sign'  v-if='item.step_status==4' style='color:#5cb85c'></i>
                            <i class='glyphicon glyphicon-registration-mark'  v-if='item.step_status==1' style='color:#f0ad4e'></i>
                            <i class='glyphicon glyphicon-question-sign'  v-if='item.step_status==0' style='color:#f0ad4e'></i>
                            {{statusList[item.step_status]}}</td>

                        <td >{{item.step_data.submitted_at}} </td>
                        <td >
                            <a href='javascript:;' @click='auditList(index)'><?php echo Yii::t('global', 'View Detail') ?></a>
                        </td>
                    </tr>
                    </tbody>
                </table>

                <div class="mt15" v-show='list.length==0'>
                    <div class="alert alert-warning" role="alert"><?php echo Yii::t('ptc', 'No Data') ?></div>
                </div>
            </div>
            <!-- 未填写的数据 -->
            <div role="tabpanel" class="tab-pane " id="unstart">
                <table class="table table-bordered  mt20" id='unfilled' v-show='unList.length!=0'>
                    <thead>
                    <tr>
                        <th><?php echo Yii::t('global', 'Name') ?></th>
                        <th><?php echo Yii::t('labels', 'Class') ?></th>
                        <th><?php echo Yii::t('labels', 'Date of Birth') ?></th>
                        <th><?php echo Yii::t('labels', 'Status') ?></th>
                    </tr>
                    </thead>
                    <tbody  v-if='isShow'>
                    <tr v-for='(item,index) in unList'>
                        <td >
                            <a href="javascript:;"  @click="overalls(item.childid)">
                                {{item.name}}
                            </a>
                        </td>
                        <td > {{item.class_name}}</td>
                        <td >{{item.birthday}}</td>
                        <td >{{statusList[item.step_status]}}</td>
                    </tr>
                    </tbody>
                </table>

                <div class="mt15" v-show='unList.length==0'>
                    <div class="alert alert-warning" role="alert"><?php echo Yii::t('ptc', 'No Data') ?></div>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" tabindex="-1" role="dialog" id='auditDialog' data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title"><?php echo Yii::t('global', 'View Detail') ?></h4>
                </div>
                <div class="modal-body" v-if='Object.keys(rejectionList).length != 0'>
                    <div class="media col-md-12 ">
                        <div class="media-left pull-left media-middle">
                            <a href="javascript:void(0)">
                                <img :src="rejectionList.avatar" data-holder-rendered="true" class="media-object img-circle avatarImage">
                            </a>
                        </div>
                        <div class="media-body pt10 media-middle">
                            <h4 class="media-heading font14">{{rejectionList.name}}</h4>
                            <div class="text-muted">{{rejectionList.class_name}}</div>
                        </div>
                    </div>
                    <div class=' font14 '>
                        <div class='mb10 mt20 col-md-12'><strong>当前乘坐信息</strong> </div>
                        <div  v-if='rejectionList.bus_site_data'>
                            <div class='mb10 col-md-4'><span class='color6'>乘坐方式：</span> <span class='color3'>{{ rejectionList.bus_site_data.journey.value==1?"<?php echo Yii::t('reg', 'One-way journey TO SCHOOL') ?>":rejectionList.bus_site_data.journey.value==2?"<?php echo Yii::t('reg', 'One-way journey BACK HOME') ?>":"<?php echo Yii::t('reg', 'Two-way journey') ?>"}}</span></div>
                            <div class='mb10 col-md-4'><span class='color6'>乘坐线路：</span><span class='color3'>{{rejectionList.bus_site_data.routes_data.title}}</span></div>
                            <div class='mb10 col-md-4'><span class='color6'>乘坐站点：</span><span class='color3'>{{ rejectionList.bus_site_data.sites_data.title_cn}}</span></div>
                            <div class='col-md-4'><span class='color6'>档位：</span><span class='color3'>{{ rejectionList.bus_site_data.sites_data.fee_level_title}}</span></div>
                        </div>
                        <div v-else class='mb10 col-md-4 color3'>无</div>
                    </div>
                    <div class='clearfix'></div>
                    <hr>
                    <div class='pt20 col-md-12 '>
                        <div class='borderRight'></div>
                        <div class='col-md-6 col-sm-6  scroll-box' style='max-height:350px;overflow-y:auto'>
                            <p class='font14'><strong>提交记录</strong></p>
                            <div v-if='rejectionList.step_data.submitted_log && rejectionList.step_data.submitted_log.length'>
                                <div v-for='(list,index) in rejectionList.step_data.submitted_log' class='ml10' style='border-left:1px dashed #ccc'>
                                    <p style='margin-left:-7px;background: #fff;'>
                                        <span class='font14'>
                                            <span class='glyphicon glyphicon-time greenIcon '></span>
                                            <span v-if='list.identity=="parents"'>{{list.submitted_time}} 家长提交({{list.user_name}})</span>
                                            <span v-if='list.identity=="staff"'>{{list.submitted_time}} 老师修改({{list.user_name}})</span>
                                        </span>
                                    </p>
                                    <div class='ml20 pb20' >
                                        <div class=' mt10 color3'><span class='color6 mr10'>乘坐班车：</span>{{list.need_bus==1?"是":"否"}}</div>
                                        <template v-if='list.need_bus==1'>
                                            <div class=' mt10 color3'><span class='color6 mr10'>新申请校车学生：</span>{{list.new_bus==2?"是":"否"}}</div>
                                            <div class=' mt10 color3'><span class='color6 mr10'><?php echo Yii::t('reg', 'Method (Round Trip/Morning Only/Afternoon Only)') ?>：</span>{{list.journey==1?"<?php echo Yii::t('reg', 'One-way journey TO SCHOOL') ?>":list.journey==2?"<?php echo Yii::t('reg', 'One-way journey BACK HOME') ?>":"<?php echo Yii::t('reg', 'Two-way journey') ?>"}}</div>
                                            <div class=' mt10 color3'><span class='color6 mr10'><?php echo Yii::t('reg', 'Apply New Pick-up/Drop-off Point') ?>：</span>{{list.customize==1?"是":"否"}}</div>
                                            <div class=' mt10 color3'>
                                                <span class='color6 mr10'>乘坐地点是否变更：</span>
                                                {{list.new_bus==2 ? '--':list.change_ride==1?"否":"是"}}
                                            </div>
                                            <div class='mt10 color3' v-if='list.change_ride==2  || list.new_bus == 2'>
                                                <span class='color6 mr10'>乘坐地点：</span>
                                                <span v-if='list.bus_sites_id!=""'>{{extraData.busSites[list.bus_sites_id].full_title}}</span>
                                                <span v-else>{{list.parking}}</span>
                                            </div>
                                            <div class='mt10 color3' v-if='list.perform && list.perform!=""'>
                                                <span class='color6 mr10'>同步状态：</span>
                                                <span v-if='list.perform=="1"'>立即同步</span>
                                                <span v-if='list.perform=="2"'>定时同步：{{list.perform_time}}</span>
                                            </div>
                                        </template>
                                    </div>
                                </div>
                            </div>
                            <div v-else>
                                暂无数据
                            </div>
                        </div>
                        <div class='col-md-6'>
                            <div  v-show='!editBusInfo'>
                                <div class=' mt10 color3'><span class='color6 mr10'>乘坐班车：</span>{{rejectionList.step_data.need_bus==1?"是":"否"}}</div>
                                <template v-if='rejectionList.step_data.need_bus==1'>
                                    <div class=' mt10 color3'><span class='color6 mr10'>新申请校车学生：</span>{{rejectionList.step_data.new_bus==2?"是":"否"}}</div>
                                    <div class=' mt10 color3'><span class='color6 mr10'><?php echo Yii::t('reg', 'Method (Round Trip/Morning Only/Afternoon Only)') ?>：</span>{{rejectionList.step_data.journey==1?"<?php echo Yii::t('reg', 'One-way journey TO SCHOOL') ?>":rejectionList.step_data.journey==2?"<?php echo Yii::t('reg', 'One-way journey BACK HOME') ?>":"<?php echo Yii::t('reg', 'Two-way journey') ?>"}}</div>
                                    <div class=' mt10 color3'><span class='color6 mr10'><?php echo Yii::t('reg', 'Apply New Pick-up/Drop-off Point') ?>：</span>{{rejectionList.step_data.customize==1?"是":"否"}}</div>

                                    <div class=' mt10 color3'>
                                        <span class='color6 mr10'>乘坐地点是否变更：</span>
                                        {{rejectionList.step_data.new_bus==2 ? '--':rejectionList.step_data.change_ride==1?"否":"是"}}
                                    </div>
                                    <div class="mt10 color3"v-if="(rejectionList.step_data.change_ride === 1 && rejectionList.step_data.parking) || rejectionList.step_data.change_ride === 2 || rejectionList.step_data.new_bus === 2 ">
                                        <span class="color6 mr10">乘坐地点：</span>
                                        <span v-if="rejectionList.step_data.change_ride === 2 && rejectionList.step_data.bus_sites_id">
                                            {{ extraData.busSites[rejectionList.step_data.bus_sites_id].full_title }}
                                        </span>
                                        <span v-else>{{ rejectionList.step_data.parking }}</span>
                                    </div>
                                    <div class='mt10 color3' v-if='rejectionList.step_data.perform && rejectionList.step_data.perform!=""'>
                                        <span class='color6 mr10'>同步状态：</span>
                                        <span v-if='rejectionList.step_data.perform=="1"'>立即同步</span>
                                        <span v-if='rejectionList.step_data.perform=="2"'>定时同步：{{rejectionList.step_data.perform_time}}</span>
                                    </div>
                                </template>
                                <div class=' mt20 mb20' v-if='rejectionList.step_data.need_bus==1 && rejectionList.sign_url!=""'>
                                    <span class='pull-left'><?php echo Yii::t('event', 'Signature') ?>：</span> <img :src="rejectionList.sign_url" alt="" class='signImgDetails'>
                                </div>
                                <div class='pull-right mt16'>
                                    <button type="button"  class="btn btn-primary ml16" :disabled='btnCon' @click='editInfo()'><?php echo Yii::t("global", "修改资料");?></button>
                                </div>
                                <div class='clearfix'></div>
                            </div>
                            <div v-show='editBusInfo'>
                                <div class='mb10'>
                                    <span class='color3 font12 width100'><?php echo Yii::t('reg', '乘坐班车：') ?></span>
                                    <label class="radio-inline" v-for="(list,key,i) in yes_no">
                                        <input type="radio" :value="list.value" v-model="saveInfo.step_data.need_bus" > {{list.label}}
                                    </label>
                                </div>
                                <div v-if='saveInfo.step_data.need_bus==1'>
                                    <div class='mb10'>
                                        <span class='color3 font12 width100'><?php echo Yii::t('reg', '新申请校车学生：') ?></span>
                                        <label class="radio-inline" v-for="(list,key,i) in no_yes">
                                            <input type="radio" :value="list.value" v-model="saveInfo.step_data.new_bus" > {{list.label}}
                                        </label>
                                    </div>
                                    <div class='mb10'>
                                        <span class='color3 font12 width100'><?php echo Yii::t('reg', '乘坐方式：') ?></span>
                                        <el-select v-model="saveInfo.step_data.journey" class='length_3' size='small' clearable placeholder="请选择">
                                        <el-option
                                            v-for="(list,key,i) in journeyData"
                                            :key="list.value"
                                            :label="list.label"
                                            :value="list.value">
                                            </el-option>
                                        </el-select>
                                    </div>
                                    <div v-if='saveInfo.step_data.new_bus==1' class='mb10'>
                                        <span class='color3 font12 width100'>乘坐地点是否变更：</span>
                                        <label class="radio-inline" v-for="(list,key,i) in no_yes">
                                            <input type="radio" :value="list.value" v-model="saveInfo.step_data.change_ride" > {{list.label}}
                                        </label>
                                    </div>
                                    <div class='mb10 flex' v-if='saveInfo.step_data.change_ride==2  || saveInfo.step_data.new_bus == 2'>
                                        <span class='color3 font12 pt8'><?php echo Yii::t('reg', '乘坐地点：') ?></span>
                                        <div class='flex1 ml10'>
                                            <div class='flex align-items' style='height:32px;'>
                                                <div class="radio" style='display:inline-block'>
                                                    <label>
                                                        <input type="radio"    value="0" v-model='saveInfo.step_data.customize'> 选择已有站点
                                                    </label>
                                                </div>
                                                <el-select v-model="bus_sites_id" v-if='saveInfo.step_data.customize==0' class='length_4 ml10' size='small' filterable clearable placeholder="请选择站点" @change='fee_level()'>
                                                    <el-option
                                                    v-for="(list,key,i) in extraData.busSites"
                                                    :key="list.id"
                                                    :label="list.full_title"
                                                    :value="list.id">
                                                    </el-option>
                                                </el-select>
                                            </div>
                                            <div class='flex align-items' style='height:32px;'>
                                                <div class="radio" style='display:inline-block'>
                                                    <label>
                                                        <input type="radio"   value="1" v-model='saveInfo.step_data.customize'> 申请新站点
                                                    </label>
                                                </div>
                                                <el-input class='length_3 ml10' size='small' v-if='saveInfo.step_data.customize==1'  v-model="saveInfo.step_data.parking" placeholder="请输入内容"></el-input>
                                            </div>
                                            <div class='redIcon' v-if='fee_level_title=="1"'>档位发生变化与当前乘坐档位不同</div>
                                        </div>
                                    </div>
                                    <div class='mt16 flex' >
                                        <span class='color3 font12'><?php echo Yii::t('reg', '同步状态：') ?></span>
                                        <div class='flex1 ml10'>
                                            <div class="radio " style='margin-top:0px'>
                                                <label>
                                                    <input type="radio" value="1" v-model='statusBus'> 立即同步
                                                </label>
                                            </div>
                                            <div class='flex align-items' style='height:32px;'>

                                            
                                            <div class="radio" style='display:inline-block'>
                                                <label>
                                                    <input type="radio"  value="2" v-model='statusBus'> 定时同步
                                                </label>
                                            </div>
                                            <el-date-picker
                                                class='ml10'
                                                v-if='statusBus=="2"'
                                                size='small'
                                                type="date"
                                                format="yyyy-MM-dd"
                                                value-format="yyyy-MM-dd"
                                                placement="bottom-start"
                                                v-model='perform_time'
                                                :picker-options="pickerOptions"
                                                placeholder="<?php echo Yii::t('ptc','Select a date') ?>">
                                            </el-date-picker>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class='pull-right mt16'>
                                    <button type="button"  class="btn btn-default ml16" :disabled='btnCon' @click='editBusInfo=false'><?php echo Yii::t("global", "取消");?></button>
                                    <button type="button"  class="btn btn-primary ml16" :disabled='btnCon' @click='saveBusInfo()'><?php echo Yii::t("global", "保存");?></button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class='clearfix'></div>
                    <hr>
                    <div class='col-md-12 col-sm-12 font14'>
                        <div class='borderRight'></div>

                        <div class='col-md-6 col-sm-6 scroll-box' style='max-height:250px;overflow-y:auto'>
                            <p><strong>当前审核状态</strong></p>
                            <div>
                                {{statusList[rejectionList.step_status]}}
                                <button type="button" class="btn btn-primary  btn-xs pull-right" @click='resetStatus()'  v-if='rejectionList.step_status==3 || rejectionList.step_status==4'><?php echo Yii::t('global', 'Reset') ?></button>
                            </div>
                            <p class='mt10'><strong>审核记录</strong></p>
                            <div>
                                <div v-for='(list,index) in rejectionList.audit_log' class='ml10' style='border-left:1px dashed #ccc'>
                                    <p style='margin-left:-7px;background: #fff;' >
                                <span v-if='list.status==4'>
                                <span class='glyphicon glyphicon-ok-sign greenIcon'></span><span> <?php echo Yii::t('global', 'Confirmed') ?></span>
                                </span>
                                        <span v-if='list.status==3'>
                                <span class='glyphicon glyphicon-info-sign redIcon'></span> <span> <?php echo Yii::t('reg', 'Rejected') ?></span>
                                </span>
                                        <span v-if='list.status==0'>
                                <span class='glyphicon glyphicon-question-sign yellowIcon'></span> <span> <?php echo Yii::t('global', 'Reset') ?></span>
                                </span>
                                    </p>
                                    <p class='ml10' style='background:#F7F7F8;line-height:22px'  v-if='list.comment!="" && list.comment!=null'><?php echo Yii::t('reg', '备注：') ?>{{list.comment}}</p>
                                    <p class='pl10 font12'>{{list.date}} {{list.user}}</p>
                                </div>
                            </div>
                        </div>
                        <div class='col-md-6 col-sm-6' >
                            <p><strong>审核</strong><span class='font12 color6'>（审核通过后将按同步状态进行同步操作）</span></p>
                            <div class='p10 grey'>
                                <label class="radio ml20">
                                    <input type="radio" id="inlineradio1" v-model='audit_status' value="4"> <?php echo Yii::t('labels', 'Yes') ?>
                                </label>
                                <label class="radio ml20">
                                    <input type="radio" id="inlineradio2" v-model='audit_status' value="3"> 未通过
                                </label>
                                <textarea class="form-control" rows="3" placeholder='请输入备注' v-model='comment'></textarea>
                                <div v-if='audit_status==3 || type_status==2'>
                                    <p class='mt10'>修改建议将直接显示在家长端，请准确措辞。</p>   
                                    <div class="checkbox">
                                        <label>
                                            <input type="checkbox"  v-model='send_wechat'>将审核结果发送至家长微信
                                        </label>
                                    </div>
                                </div>
                                <!-- <div v-if='audit_status==4 && rejectionList.step_data.bus_routes_id.length>1'>
                                    <p class='mt10'>选择线路</p>   
                                    <div class="checkbox">
                                        <select class="form-control" v-model='bus_routes_id'>
                                            <option value=''>请选择乘坐线路</option>
                                            <option v-for='(list,i) in rejectionList.step_data.bus_routes_id' :value='list'>{{extraData.busRoutes[list].title}}</option>
                                        </select>
                                    </div>
                                </div> -->
                            </div>
                            
                            <div class='mt20'>
                                <button type="button"  class="btn btn-primary pull-right" :disabled='btnCon' @click='saveInfoStatus()'><?php echo Yii::t("newDS", "确认");?></button>
                                <button type="button" class="btn btn-default  pull-right mr10" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                            </div>
                        </div>
                    </div>
                    <div class='clearfix'></div>
                    <!-- <div class="panel panel-default mt20"  v-if='rejectionList.step_status==4 && rejectionList.step_data.need_bus==1'>
                        <div class="panel-heading">
                            <h3 class="panel-title"><?php echo Yii::t('reg', '校车站点路线') ?></h3>
                        </div>
                        <div class="panel-body">
                            <div class=''>
                                <div></div>
                                <div class='mt16 flex'>
                                    <span class='color3 font12'><?php echo Yii::t('reg', '选择站点：') ?></span>
                                    <el-select v-model="bus_sites_id" class='length_6 ml10' size='small' filterable clearable placeholder="请选择站点">
                                        <el-option
                                        v-for="(list,key,i) in extraData.busSites"
                                        :key="list.id"
                                        :label="list.title_cn"
                                        :value="list.id">
                                        </el-option>
                                    </el-select>
                                </div>
                                <div class='mt16 flex' >
                                    <span class='color3 font12'><?php echo Yii::t('reg', '同步状态：') ?></span>
                                    <div class='flex1 ml10'>
                                        <div class="radio " style='margin-top:0px'>
                                            <label>
                                                <input type="radio" value="1" v-model='statusBus'> 立即同步
                                            </label>
                                        </div>
                                        <div class='flex align-items' style='height:32px;'>

                                        
                                        <div class="radio" style='display:inline-block'>
                                            <label>
                                                <input type="radio"  value="2" v-model='statusBus'> 定时同步
                                            </label>
                                        </div>
                                        <el-date-picker
                                            class='ml10'
                                            v-if='statusBus=="2"'
                                            size='small'
                                            type="date"
                                            format="yyyy-MM-dd"
                                            value-format="yyyy-MM-dd"
                                            placement="bottom-start"
                                            v-model='perform_time'
                                            placeholder="<?php echo Yii::t('ptc','Select a date') ?>">
                                        </el-date-picker>
                                        </div>
                                    </div>
                                </div>
                                <div class='pull-right mt16'>
                                    <span class='color6 font12 mt8 ml10' v-if="rejectionList.step_data.remarks_at">最近保存时间：{{rejectionList.step_data.remarks_at}}</span>
                                    <button type="button"  class="btn btn-primary ml16" :disabled='btnCon' @click='saveRemarks()'><?php echo Yii::t("global", "保存");?></button>
                                </div>
                            </div>
                        </div>
                    </div> -->
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="stuDetails" tabindex="-1" role="dialog" >
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" ><?php echo Yii::t('attends', '整体情况') ?> <span id="remarks_t"></span></h4>
                </div>
                <div class="modal-body color6" v-if='childData.child'>
                    <p><strong class='font14 color3'>学生资料</strong></p>
                    <div class='flex'>
                        <div style='width:80px'><img :src="childData.child.avatar" class='stuImg'></div>
                        <div class='flex1'>
                            <p class='mt10'><strong class='font14 color3 studentName'>{{childData.child.name}}</strong> </p>
                            <p class='mb20'>{{childData.child.class_name}}</p>
                            <div  v-if='childData.parents.finfo!=null'>
                                <div class='col-md-4'>
                                    <strong class='color3'><?php echo Yii::t('global', 'Father') ?>：</strong>
                                    <span class='glyphicon glyphicon-user'></span> {{childData.parents.finfo.name}}
                                </div>
                                <div class='col-md-4'>
                                    <span class='glyphicon glyphicon-phone'></span> {{childData.parents.finfo.mphone}}
                                </div>
                                <div class='col-md-4'>
                                    <span class='glyphicon glyphicon-envelope'></span> {{childData.parents.finfo.email}}
                                </div>
                            </div>
                            <div class='clearfix'></div>
                            <div class='mt10' v-if='childData.parents.minfo!=null'>
                                <div class='col-md-4'>
                                    <strong class='color3'><?php echo Yii::t('global', 'Mother') ?>：</strong>
                                    <span class='glyphicon glyphicon-user'></span> {{childData.parents.minfo.name}}
                                </div>
                                <div class='col-md-4'>
                                    <span class='glyphicon glyphicon-phone'></span> {{childData.parents.minfo.mphone}}
                                </div>
                                <div class='col-md-4'>
                                    <span class='glyphicon glyphicon-envelope'></span> {{childData.parents.minfo.email}}
                                </div>
                            </div>
                        </div>
                    </div>
                    <ul class="nav nav-wizard pb20 mt20">
                        <li v-for='(list,index) in childData.step_status'>
                            <a href="javascript::" class='color6' :data-tab="list.step_id" :data-status="list.status" :data-name="childData.child.name"><span class="number">{{index+1}}</span>{{list.title}}</a>
                            <p class='mt10 color6' >
                                <span :class="list.status==4?'greenStep':list.status==3?'redStep':list.status==1 || list.status==2?'orgStep':''">{{ statusList[list.status]}}</span></p>
                        </li>
                    </ul>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('global', 'Cancel') ?></button>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="tipsModal" tabindex="-1" role="dialog" >
        <div class="modal-dialog modal-sm" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" ><?php echo Yii::t('attends', '批量通过') ?> <span id="remarks_t"></span></h4>
                </div>
                <div class="modal-body color6" >
                    <div class='color3 font14'>确认批量通过审核吗？</div> 
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('global', 'Cancel') ?></button>
                    <button type="button" class="btn btn-primary" :disabled='btnTips' @click='batchPass()'><?php echo Yii::t('global', '确定') ?></button>
                </div>
            </div>
        </div>
    </div>
</div>
<script>

    var step_id = '<?php echo $step_id?>';
    var coll_id = '<?php echo $coll_id?>';
    var type = '<?php echo $type?>';
    var type_status = '<?php echo $type_status?>';

    var  registrations = new Vue({
        el: "#homeAddress",
        data: {
            type_status:type_status,
            list:[],
            unfilled:0,
            coll_id:coll_id,
            filled:0,
            total:0,
            type:type,
            logList:[],
            statusList:[],
            audit_status:'',
            comment:'',
            child_id:'',
            childData:{},
            child_ids:[],
            rejectionList:{},
            unList:{},
            btnCon:false,
            send_wechat:true,
            isShow:true,
            loading:false,
            all:false,
            btnTips:false,
            extraData:{},
            bus_routes_id:'',
            bus_sites_id:'',
            statusBus:'',
            perform_time:'',
            rejectionListIndex:null,
            yes_no:[{
                label:'是',
                value:1
            },{
                label:'否',
                value:2
            }],
            no_yes:[{
                label:'是',
                value:2
            },{
                label:'否',
                value:1
            }],
            journeyData:[{
                label:'<?php echo Yii::t('reg', 'One-way journey TO SCHOOL') ?>',
                value:1
            },{
                label:'<?php echo Yii::t('reg', 'One-way journey BACK HOME') ?>',
                value:2
            },{
                label:'<?php echo Yii::t('reg', 'Two-way journey') ?>',
                value:3
            }],
            editBusInfo:false,
            saveInfo:{},
            pickerOptions: {
                disabledDate(time) {
                return time.getTime() < Date.now() - 8.64e7// 如果没有后面的-8.64e7就是不可以选择
                } 
            }, 
            fee_level_title:"" 
        },
        created: function() {
            this.getInfo(this.type)
            eventBus.$on('listChanged', list => {
                if(sessionStorage.getItem('tabType') === 'filled'){
                    this.list = list;
                }else{
                    this.unList = list
                }
            });
        },
        watch: {
        },
        methods: {
           
            overalls(id){
                let that=this
                $.ajax({
                    url:'<?php echo $this->createUrlReg('getStudentOverall'); ?>',
                    type: 'get',
                    dataType:'json',
                    data:{
                        child_id:id,
                        coll_id:coll_id,
                    },
                    success:function(data){
                        if(data.state=="success"){
                            that.childData=data.data
                            $('#stuDetails').modal('show')
                        }else{
                            resultTip({error: 'warning', msg: data.message});
                        }
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            parsefloat(num) {
                return Number(num * 100).toFixed(2);
            },
            getInfo(type,status){
                let that = this
                this.type=type
                this.isShow=false
                $.ajax({
                    url:'<?php echo $this->createUrlReg('stepTemplateInfo'); ?>',
                    type: 'get',
                    dataType:'json',
                    data:{
                        coll_id:coll_id,
                        step_id:step_id,
                        type:type
                    },
                    success:function(data){
                        if(data.state=="success"){
                            if(type=='unfilled'){
                                that.unList = data.data.list
                            }else{
                                that.list = data.data.list
                            }
                            if(data.data.extraData){
                                that.extraData=data.data.extraData
                            }
                            that.unfilled = data.data.unfilled
                            that.filled = data.data.filled
                            that.total = data.data.count
                            that.statusList=data.data.statusList
                            that.logList=data.data.logList
                            that.isShow=true
                            sessionStorage.setItem('tabType', type);
                            sessionStorage.setItem('listUpdated',  JSON.stringify(data.data.list));
                            that.$forceUpdate();
                            that.$nextTick(()=>{
                                that.DataTable(data)
                            })
                            if(status){
                                that.auditList(that.rejectionListIndex)
                            }
                        }else{
                            resultTip({error: 'warning', msg: data.message});
                        }
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            DataTable(data){
                let that=this
                that.$nextTick(()=>{
                    $('#stuDetails').modal('hide');
                    $('#nameSearchComp').html(data.data.nameSearchComp)
                    if(that.type=='filled'){
                        var table = $('#filled').DataTable({
                            aaSorting: [10, 'desc'], // 默认排序
                            paging: false,
                            info: false,
                            searching: false,
                            "destroy": true,
                            columnDefs: [ {
                                "targets": 'nosort',
                                "orderable": false
                            } ],
                        });
                    }else{
                        var table = $('#unfilled').DataTable({
                            aaSorting: [1, 'desc'], // 默认排序
                            paging: false,
                            info: false,
                            "destroy": true,
                            searching: false,
                        });
                    }
                })

            },
            auditList(index){
                this.rejectionListIndex=index
                this.rejectionList=this.list[index]
                this.bus_sites_id=this.list[index].step_data.bus_sites_id
                this.statusBus=this.list[index].step_data.perform
                this.perform_time=this.list[index].step_data.perform_time
                this.bus_routes_id=this.list[index].step_data.bus_routes_id
                this.child_id=this.list[index].childid
                this.audit_status=''
                this.comment=''
                this.saveInfo= JSON.parse(JSON.stringify(this.list[index]));
                this.editBusInfo=false
                $('#auditDialog').modal('show')
            },
            editInfo(){
                this.saveInfo= JSON.parse(JSON.stringify(this.list[this.rejectionListIndex]));
                this.statusBus=this.list[this.rejectionListIndex].step_data.perform
                this.bus_sites_id=this.list[this.rejectionListIndex].step_data.bus_sites_id
                this.perform_time=this.list[this.rejectionListIndex].step_data.perform_time
                if (this.rejectionList.bus_site_data &&  this.rejectionList.bus_site_data.sites_data.fee_level !== '' && this.bus_sites_id ) {
                    const currentFeeLevel = this.rejectionList.bus_site_data.sites_data.fee_level;
                    const extraFeeLevel = this.extraData.busSites[this.bus_sites_id]?.fee_level;
                    this.fee_level_title = currentFeeLevel !== extraFeeLevel ? '1' : '';
                } else {
                     this.fee_level_title = '';
                }
                this.editBusInfo=true
            },
            exportTab() {
                if(this.type=='filled'){
                    const filename ='校车已填写.xlsx';
                    const ws_name = "SheetJS";
                    var exportDatas = [];
                    for(var i=0;i<this.list.length;i++){
                        var data={
                            'ID':this.list[i].childid,
                            '<?php echo Yii::t('global', 'Name') ?>':this.list[i].name,
                            "<?php echo Yii::t('labels', 'Class') ?>":this.list[i].className,
                            "乘坐班车":this.list[i].step_data.need_bus==1?"是":"否",
                            "<?php echo Yii::t('labels', 'Status') ?>":this.statusList[this.list[i].step_status],
                            "提交时间":this.list[i].step_data.submitted_at,
                        }
                        if(this.list[i].step_data.need_bus==1){
                            data=Object.assign(data,{                          
                                "<?php echo Yii::t('reg','Newly applied bus student')?>":this.list[i].step_data.new_bus==2?"是":"否",
                                "<?php echo Yii::t('reg', 'Method (Round Trip/Morning Only/Afternoon Only)') ?>":this.list[i].step_data.journey==1?"<?php echo Yii::t('reg', 'One-way journey TO SCHOOL') ?>":this.list[i].step_data.journey==2?"<?php echo Yii::t('reg', 'One-way journey BACK HOME') ?>":"<?php echo Yii::t('reg', 'Two-way journey') ?>",
                                "<?php echo Yii::t('reg','Any changes on bus stop')?>":this.list[i].step_data.new_bus==2?'--':this.list[i].step_data.change_ride==2?"是":"否",
                                "<?php echo Yii::t('reg', 'Apply New Pick-up/Drop-off Point') ?>":this.list[i].step_data.customize==1?"是":"否",
                                "乘坐地点":this.list[i].step_data.parking
                            })
                        }else{
                            data=Object.assign(data,{                           
                                "<?php echo Yii::t('reg','Newly applied bus student')?>":"--",
                                "<?php echo Yii::t('reg', 'Method (Round Trip/Morning Only/Afternoon Only)') ?>":"--",
                                "<?php echo Yii::t('reg','Any changes on bus stop')?>":"--",
                                "<?php echo Yii::t('reg', 'Apply New Pick-up/Drop-off Point') ?>":"--",
                                "乘坐地点":"--"
                            })
                        }
                        exportDatas.push(data)
                    }
                    var wb=XLSX.utils.json_to_sheet(exportDatas,{
                        origin:'A1',// 从A1开始增加内容
                        header: ['ID','<?php echo Yii::t('global', 'Name') ?>', '<?php echo Yii::t('labels', 'Class') ?>', '乘坐班车','<?php echo Yii::t('reg','Newly applied bus student')?>','<?php echo Yii::t('reg', 'Method (Round Trip/Morning Only/Afternoon Only)') ?>','<?php echo Yii::t('reg','Any changes on bus stop')?>','<?php echo Yii::t('reg', 'Apply New Pick-up/Drop-off Point') ?>','乘坐地点','<?php echo Yii::t('labels', 'Status') ?>','提交时间'],
                    });
                    const workbook = XLSX.utils.book_new();
                    XLSX.utils.book_append_sheet(workbook, wb, ws_name);
                    const wbout = XLSX.write(workbook, {bookType: 'xlsx', type: 'array'});
                    const blob = new Blob([wbout], {type: 'application/octet-stream'});
                    let link = document.createElement('a');
                    link.href = URL.createObjectURL(blob);
                    link.download = filename;
                    link.click();
                    setTimeout(function() {
                        // 延时释放掉obj
                        URL.revokeObjectURL(link.href);
                        link.remove();
                    }, 500);
                }else{
                    var title='校车未填写.xlsx'   
                    const ws_name = "SheetJS";
                    var exportDatas = [];
                    for(var i=0;i<this.unList.length;i++){
                        exportDatas.push(
                            {
                            'ID':this.unList[i].childid,
                            '<?php echo Yii::t('global', 'Name') ?>':this.unList[i].name,
                            "<?php echo Yii::t('labels', 'Class') ?>":this.unList[i].className,
                            "<?php echo Yii::t('labels', 'Date of Birth') ?>":this.unList[i].birthday,
                            "<?php echo Yii::t('labels', 'Status') ?>":this.statusList[this.unList[i].step_status],
                            });
                    }
                    var wb=XLSX.utils.json_to_sheet(exportDatas,{
                        origin:'A1',// 从A1开始增加内容
                        header: ['ID','<?php echo Yii::t('global', 'Name') ?>', '<?php echo Yii::t('labels', 'Class') ?>','<?php echo Yii::t('labels', 'Date of Birth') ?>','<?php echo Yii::t('labels', 'Status') ?>'],
                    });
                    const workbook = XLSX.utils.book_new();
                    XLSX.utils.book_append_sheet(workbook, wb, ws_name);
                    const wbout = XLSX.write(workbook, {bookType: 'xlsx', type: 'array'});
                    const blob = new Blob([wbout], {type: 'application/octet-stream'});
                    let link = document.createElement('a');
                    link.href = URL.createObjectURL(blob);
                    link.download = title;
                    link.click();
                    setTimeout(function() {
                        // 延时释放掉obj
                        URL.revokeObjectURL(link.href);
                        link.remove();
                    }, 500);
                }

            },
            checkAll(e){
                this.child_ids=[]
                if(e.target.checked){
                    for(var i=0;i<this.list.length;i++){
                        this.child_ids.push(this.list[i].childid)
                        // if(this.list[i].step_status!=4){
                        // }
                    }
                    this.all=true
                }else{
                    this.all=false
                }
            },
            childCheck(e){
                // let checkListLen=this.list.filter((item)=>{return item.step_status!=4});
                if(e.target.checked){
                    if(this.child_ids.length==this.list.length){
                        this.all=true
                    }else{
                        this.all=false
                    }
                }else{
                    this.all=false
                }
            },
            batchPass(modal){
                let that = this
                if(this.child_ids.length==0){
                    resultTip({error: 'warning', msg: '请选择学生'});
                    return
                }
                if(modal){
                    $('#tipsModal').modal('show')
                    return
                }
                this.loading=true
                this.btnCon=true
                this.btnTips=true
                $.ajax({
                    url:'<?php echo $this->createUrlReg('batchPass'); ?>',
                    type: 'post',
                    dataType:'json',
                    data:{
                        child_ids:this.child_ids,
                        step_id:step_id,
                        coll_id:this.coll_id
                    },
                    success:function(data){
                        if(data.state=="success"){
                            resultTip({
								msg: data.state,
							});
                            that.getInfo(that.type)
                            $('#auditDialog').modal('hide')
                            $('#tipsModal').modal('hide')
                        }else{
                            resultTip({error: 'warning', msg: data.message});
                        }
                        that.btnCon=false
                        that.loading=false
                        that.btnTips=false
                    },
                    error:function(data){
                        that.btnCon=false
                        that.loading=false
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            resetStatus(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrlReg("stepAudit") ?>',
                    type: "get",
                    dataType: 'json',
                    data:{
                        child_id:this.child_id,
                        step_id:step_id,
                        audit_status:0,
                        coll_id:this.coll_id,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            resultTip({
                                msg: data.state
                            });
                            that.getInfo(that.type)
                            $('#auditDialog').modal('hide')
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                    },
                })
            },
            saveInfoStatus(){
                let that = this
                if(this.audit_status==''){
                    resultTip({
                        error: 'warning',
                        msg: '请选择审核状态'
                    });
                    return
                }
                that.btnCon=true
                var dataList={
                    child_id:this.child_id,
                    step_id:step_id,
                    audit_status:this.audit_status, 
                    coll_id:this.coll_id,
                    comment: this.comment,
                }
                if(this.audit_status==3 || this.type_status==2){
                   var dataList=Object.assign({send_wechat:that.send_wechat?1:0},dataList)
                }
                // if(this.audit_status==4 && this.rejectionList.step_data.bus_routes_id.length>1){
                //     if(that.bus_routes_id==''){
                //         that.btnCon=false
                //         resultTip({
                //             error: 'warning',
                //             msg: '请选择线路'
                //         });
                //         return
                //     }
                //     var dataList=Object.assign({bus_routes_id:that.bus_routes_id},dataList)
                // }
                $.ajax({
                    url:'<?php echo $this->createUrlReg('stepAudit'); ?>',
                    type: 'get',
                    dataType:'json',
                    data:dataList,
                    success:function(data){
                        if(data.state=="success"){
                            resultTip({
                                msg: data.state
                            });
                            that.getInfo(that.type)
                            $('#auditDialog').modal('hide')
                        }else{
                            resultTip({error: 'warning', msg: data.message});
                        }
                        that.btnCon=false
                    },
                    error:function(data){
                        that.btnCon=false
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            batchStatus(){

            },
            saveRemarks(){
                let that = this
                if(this.statusBus==''){
                    resultTip({error: 'warning', msg: '请选择同步状态'});
                    return
                }
                if(this.statusBus=='2' && this.perform_time==''){
                    resultTip({error: 'warning', msg: '请选择同步日期'});
                    return
                }
                $.ajax({
                    url:'<?php echo $this->createUrlReg('saveRemarks'); ?>',
                    type: 'get',
                    dataType:'json',
                    data:{
                        child_id:this.child_id,
                        step_id:step_id,
                        remarks:{perform_time:this.statusBus=='2'?this.perform_time:'',perform:this.statusBus},
                        coll_id:this.coll_id,
                    },
                    success:function(data){
                        if(data.state=="success"){
                            resultTip({
                                msg: data.state
                            });
                            that.getInfo(that.type,'init')
                        }else{
                            resultTip({error: 'warning', msg: data.message});
                        }
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            fee_level(){
                if (this.rejectionList.bus_site_data &&  this.rejectionList.bus_site_data.sites_data.fee_level !== '' && this.bus_sites_id ) {
                    const currentFeeLevel = this.rejectionList.bus_site_data.sites_data.fee_level;
                    const extraFeeLevel = this.extraData.busSites[this.bus_sites_id]?.fee_level;
                    this.fee_level_title = currentFeeLevel !== extraFeeLevel ? '1' : '';
                } else {
                     this.fee_level_title = '';
                }
            },
            saveBusInfo(){
                let that = this
                if(this.statusBus==''){
                    resultTip({error: 'warning', msg: '请选择同步状态'});
                    return
                }
                if(this.statusBus=='2' && this.perform_time==''){
                    resultTip({error: 'warning', msg: '请选择同步日期'});
                    return
                }
                $.ajax({
                    url:'<?php echo $this->createUrlReg('staffSaveStep'); ?>',
                    type: 'get',
                    dataType:'json',
                    data:{
                        child_id:this.child_id,
                        step_id:step_id,
                        coll_id:this.coll_id,
                        data:{
                            perform_time:this.statusBus=='2'?this.perform_time:'',
                            perform:this.statusBus,
                            customize: this.saveInfo.step_data.customize, //1-申请新站点 2-选择已有
                            journey: this.saveInfo.step_data.journey, //乘坐方式
                            need_bus:this.saveInfo.step_data.need_bus, //1-需要班车 2-不需要班车
                            change_ride: this.saveInfo.step_data.change_ride, //1-不更换 2-更换
                            new_bus: this.saveInfo.step_data.new_bus, //1-现有 2-新申请
                            bus_sites_id:this.saveInfo.step_data.customize=='0' || (this.saveInfo.step_data.customize==2 && this.saveInfo.step_data.change_ride == 1)?this.bus_sites_id:'',
                            parking:this.saveInfo.step_data.customize=='1'?this.saveInfo.step_data.parking:'',
                        },
                    },
                    success:function(data){
                        if(data.state=="success"){
                            resultTip({
                                msg: data.state
                            });
                            that.getInfo(that.type,'init')
                        }else{
                            resultTip({error: 'warning', msg: data.message});
                        }
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            }
        }
    })
</script>
<style>
    a[data-status="0"] {
        cursor: default;
        text-decoration: none;
    }
    .width100{
        width:110px;
        display: inline-block;
    }
    .scroll-box::-webkit-scrollbar-thumb {
        /*滚动条里面小方块*/
        border-radius   : 10px;
        background-color: #ccc;
        background-image: none
    }
    .scroll-box::-webkit-scrollbar-track {
        /*滚动条里面轨道*/
        box-shadow   : inset 0 0 5px rgba(0, 0, 0, 0);
        background   : #fff;
        border-radius: 10px;
        border:none
    }
    .borderRight{
       border-right: 1px solid #E4E7ED;
        position: absolute;
        left: 50%;
        top: 0;
        width: 0;
        height: 100%;
    }
</style>
