<?php

/**
 * This is the model class for table "ivy_child_study_history".
 *
 * The followings are the available columns in table 'ivy_child_study_history':
 * @property integer $hid
 * @property integer $childid
 * @property string $schoolid
 * @property integer $classid
 * @property integer $calendar
 * @property integer $semester
 * @property integer $stat
 * @property integer $timestamp
 * @property integer $userid
 * @property integer $from_classid
 */
class ChildStudyHistory extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @return IvyChildStudyHistory the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_child_study_history';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('schoolid, classid, stat, timestamp, userid', 'required'),
			array('childid, classid, calendar, semester, stat, timestamp, userid, from_classid', 'numerical', 'integerOnly'=>true),
			array('schoolid', 'length', 'max'=>255),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('hid, childid, schoolid, classid, calendar, semester, stat, timestamp, userid, from_classid', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'classInfo'=>array(self::BELONGS_TO, 'IvyClass', 'classid'),
            'classNonTogether'=>array(self::BELONGS_TO, 'IvyClass', array('classid'=>'classid'), 'together'=>false),
            'calendarNonTogether'=>array(self::BELONGS_TO, 'Calendar', array('calendar'=>'yid'), 'together'=>false),
            'child'=>array(self::BELONGS_TO,'ChildProfileBasic','childid'),
            'userInfo'=>array(self::BELONGS_TO, 'User', 'userid'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'hid' => 'Hid',
			'childid' => 'Childid',
			'schoolid' => 'Schoolid',
			'classid' => 'Classid',
			'calendar' => 'Calendar',
			'semester' => 'Semester',
			'stat' => 'Stat',
			'timestamp' => 'Timestamp',
			'userid' => 'Userid',
			'from_classid' => 'From Classid',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('hid',$this->hid);
		$criteria->compare('childid',$this->childid);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('classid',$this->classid);
		$criteria->compare('calendar',$this->calendar);
		$criteria->compare('semester',$this->semester);
		$criteria->compare('stat',$this->stat);
		$criteria->compare('timestamp',$this->timestamp);
		$criteria->compare('userid',$this->userid);
		$criteria->compare('from_classid',$this->from_classid);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}