<div class="container-fluid">
	<ol class="breadcrumb">
		<li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('//mcampus/default/index')) ?></li>
		<li><?php echo CHtml::link(Yii::t('site', 'Routines'), array('//mcampus/default/index')) ?></li>
        <li class="active"><?php echo Yii::t('site', 'Pick-up card list') ?></li>
	</ol>

	<div class="row">
		<div class="col-md-2 col-sm-2 mb10">
			<?php
			$this->widget('zii.widgets.CMenu', array(
				'items' => $this->module->getMenu(),
				'id' => 'pageCategory',
				'htmlOptions' => array('class' => 'nav nav-pills nav-stacked background-gray'),
				'activeCssClass' => 'active',
			));
			?>
		</div>
		<div class="col-md-10 col-sm-10">
			<div class="mb10 col-sm-1 row">
			</div>
			<div class="mb10 row">
				<!-- 搜索框 -->
				<form  class="" style="float: left;width: 100%" action="<?php echo $this->createUrl('index'); ?>" method="get">
					<?php echo Chtml::hiddenField('branchId',$this->branchId); ?>
					<div class="col-sm-2 form-group">
						<input type="text" class="form-control" name="username" placeholder="孩子姓名" value="<?php echo Yii::app()->request->getParam('username','')?Yii::app()->request->getParam('username',''):''; ?>">
					</div>
					<div class="col-sm-2 form-group">
						<?php echo Chtml::dropDownList('sign',$_GET['sign'], Cards::getConfig(),array('class'=>'form-control')); ?>
					</div>

					<div class="">
						<div class="">
							<button class="btn btn-default ml5" type="submit"><span class="glyphicon glyphicon-search"> </span> </button>
						</div>
					</div>
				</form>
			</div>
			<?php
			$this->widget('ext.ivyCGridView.BsCGridView', array(
				'id' => 'cardsList',
				'afterAjaxUpdate' => 'js:head.Util.modal',
				'dataProvider' => $cardsObj,
				'template' => "{items}{pager}",
				//状态为无效时标红
				//'rowCssClassExpression' => '( $data->active == 0 ? "active" : "" )',
				'colgroups' => array(
					array(
						"colwidth" => array(200, 200, 200, 200, 200),
					)
				),
				'columns' => array(
					'cid',
					array(
						'name'=>'childid',
						'value'=>array($this,'getChildName'),
					),
					array(
					    'name' => Yii::t('site','Parent name'),
					    'value' => array($this, 'getParentName'),
					),
					array(
					    'name' => Yii::t('site','Contact number'),
					    'value' => array($this, 'getParentTel'),
					),
					array(
					    'name' => Yii::t('site','Relationship'),
					    'value' => array($this, 'getParentRelation'),
					),
					array(
					    'name' => Yii::t('site','Photo'),
					    'value' => array($this, 'GetPhoto'),
					),
					array(
						'name'=>'sign',
						'value'=>array($this,'getSign'),
					),
					array(
						'name'=>'updated',
						'value'=> 'date("Y-m-d", $data->updated)',
					),
					array(
						'name' => Yii::t('global', 'Action'),
						'value' => array($this, 'getButton'),
					),
				),

			));
			?>
		</div>
	</div>
</div>
<link href="//cdn.bootcss.com/cropper/4.0.0/cropper.min.css" rel="stylesheet">
<script src="//cdn.bootcss.com/cropper/4.0.0/cropper.min.js"></script>
<script>
	var modal = '<div class="modal fade" id="modal" class="1123" tabindex="-1" role="dialog" aria-labelledby="modal"><div class="modal-dialog modal-lg" role="document"><div class="modal-content"></div></div></div>';
	$('body').append(modal);

	function cbUpdateCard()
	{
		// location.reload();
		$('#modal').modal('hide');
		$.fn.yiiGridView.update('cardsList');
	}
</script>

<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>