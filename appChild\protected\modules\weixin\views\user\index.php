<?php if($status==1):?>
	<p>
		输入幼儿园系统帐号登录信息与微信绑定，您可以使用微信取消用餐、查看账单、查看周报告、发送邮件给校园支持团队等。
	</p>
	<p>
	<?php
		//echo Yii::app()->session->sessionID;
		//echo '<br />';
		//echo $wechatName;
		//print_r($this->wechatObj->getUserMsg());
	?>
	</p>
	
	<div class="form">
	<?php $form=$this->beginWidget('CActiveForm',array(
		'id'=>'bind-form'
	)); ?>
		<?php
		$error = CHtml::errorSummary($model,"");
		if(!empty($error)):?>
		<div class="msg-notice"><?php echo $form->errorSummary($model); ?></div>
		<?php endif;?>
		<p>
		<?php echo $form->label($model, 'username'); ?>
		<?php echo $form->textField($model,'username',array('class'=>'input')) ?>
		</p>
		
		<p>
		<?php echo $form->label($model, 'password'); ?>
		<?php echo $form->passwordField($model, 'password', array('class'=>'input','autocomplete'=>'off')); ?>
		</p>
		
		<?php echo $form->hiddenField($model, 'wechatUname');?>
		<p>
		<?php echo CHtml::submitButton(Yii::t("wechat", "绑定微信帐号"),array('class'=>'btn')); ?>
		</p>
	<?php $this->endWidget(); ?>
	</div>
<?php elseif($status==2): ?>
	<div class="msg-notice">帐号已绑定成功，您可以使用微信菜单功能了。</div>
<?php else:?>
	<div class="msg-notice">链接已失效，请重试！</div>
<?php endif;?>