<?php

class PaymentController extends ProtectedController
{
    public function init(){
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'sysConfig';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site','System Management');

        Yii::import('common.models.bank.*');
    }

    public function actionVendor($category='account')
    {
        if($category == 'account'){
            if(isset($_POST['BankAccount'])){
                $id = isset($_POST['BankAccount']['id']) ? $_POST['BankAccount']['id'] : 0;
                $model = BankAccount::model()->findByPk($id);
                if($model == null)
                    $model = new BankAccount();
                $model->attributes=$_POST['BankAccount'];
                $model->updated = time();
                $model->uid = Yii::app()->user->id;
                if($model->save()){
                    $this->addMessage('state', 'success');
                    $this->addMessage('message', '成功');
                    $this->addMessage('callback', 'callback');
                }
                else{
                    $this->addMessage('state', 'fail');
                    $err = current($model->getErrors());
                    $this->addMessage('message', $err[0]);
                }
                $this->showMessage();
            }
            $model=new BankAccount('search');
            $model->unsetAttributes();
            if(isset($_GET['BankAccount']))
                $model->attributes=$_GET['BankAccount'];
            $this->render('vendor', array('category'=>$category, 'model'=>$model));
        }
        elseif($category == 'relation'){
            $criteria = new CDbCriteria();
            $criteria->compare('inactive', 0);
            $items = BankAccount::model()->findAll($criteria);
            $this->render('vendor', array('category'=>$category, 'items'=>$items));
        }
    }

    public function getButton($data)
    {
        echo '<a class="btn btn-info btn-xs mb5" href="javascript:;" onclick="addAccount('.$data->id.')"><span class="glyphicon glyphicon-pencil"></span></a> ';
        echo '<a class="J_ajax_del btn btn-danger btn-xs mb5" href="'.$this->createUrl("delAccount", array("id"=>$data->id)).'"><span class="glyphicon glyphicon-remove"></span></a>';
    }

    public function getSchoolText($data)
    {
        $this->getAllBranch();
        echo $this->allBranch[$data->schoolid]['title'];
    }

    public function getSchoolButton($data)
    {
        echo '<a class="J_ajax_del btn btn-danger btn-xs" href="'.$this->createUrl("delSchool", array("id"=>$data->id)).'"><span class="glyphicon glyphicon-remove"></span></a>';
    }

    public function getInactive($data)
    {
        $types = array(
            0 => '正常',
            1 => '无效',
        );
        echo isset($types[$data->inactive]) ? $types[$data->inactive] : '';
    }

    public function actionGetAccount($id=0)
    {
        if($id){
            $model = BankAccount::model()->findByPk($id);
            echo CJSON::encode($model->attributes);
        }
    }

    public function actionDelAccount()
    {
        $id = Yii::app()->request->getParam('id', 0);
        if($id){
            $model = BankAccount::model()->findByPk($id);
            if($model){
                if($model->delete()){
                    $this->addMessage('state', 'success');
                    $this->addMessage('message', '删除成功');
                    $this->addMessage('callback', 'callback1');
                }
                else{
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', '删除失败');
                }
            }
        }
        $this->showMessage();
    }

    public function actionAccountRelation($id=0)
    {
        if($id){
            $sModel = new BankSchool('search');
            $sModel->unsetAttributes();
            $sModel->accountid = $id;
            $sHtml = $this->renderPartial('vendor/school', array('sModel'=>$sModel), true);

            $cModel = new BankChannel('search');
            $cModel->unsetAttributes();
            $cModel->accountid = $id;
            $cHtml = $this->renderPartial('vendor/channel', array('cModel'=>$cModel), true);

            $model = BankAccount::model()->findByPk($id);

            echo CJSON::encode(array('sHtml'=>$sHtml, 'cHtml'=>$cHtml, 'model'=>$model->attributes));
        }
    }

    public function actionGetSchool()
    {
        $branchs = Branch::model()->getBranchList();

        $eBank = array();
        $bankschools = BankSchool::model()->findAll();
        foreach($bankschools as $bankschool){
            $eBank[$bankschool->schoolid] = $bankschool->schoolid;
        }

        $ret = array();
        foreach($branchs as $bkey=>$branch){
            if(!in_array($bkey, $eBank)){
                $ret[$bkey] = $branch;
            }
        }
        echo CJSON::encode($ret);
    }

    public function actionSaveSchool()
    {
        if(Yii::app()->request->isPostRequest){
            $accountid = Yii::app()->request->getParam('accountid', 0);
            $schoolid = Yii::app()->request->getParam('schoolid', '');
            $criteria = new CDbCriteria();
            $criteria->compare('accountid', $accountid);
            $criteria->compare('schoolid', $schoolid);
            $model = BankSchool::model()->find($criteria);
            if($model == null)
                $model = new BankSchool();
            $model->accountid = $accountid;
            $model->schoolid = $schoolid;
            $model->updated = time();
            $model->uid = Yii::app()->user->id;
            if($model->save()){
                $this->addMessage('state', 'success');
                $this->addMessage('message', '成功！');
                $this->addMessage('callback', 'sSchool');
            }
            else{
                $this->addMessage('state', 'fail');
                $err = current($model->getErrors());
                $this->addMessage('message', $err[0]);
            }
            $this->showMessage();
        }
    }

    public function actionDelSchool()
    {
        if(Yii::app()->request->isPostRequest){
            $id = Yii::app()->request->getParam('id', '');
            $model = BankSchool::model()->findByPk($id);
            if($model->delete()){
                $this->addMessage('state', 'success');
                $this->addMessage('message', '成功！');
            }
            else{
                $this->addMessage('state', 'fail');
                $err = current($model->getErrors());
                $this->addMessage('message', $err[0]);
            }
            $this->addMessage('callback', 'dSchool');
            $this->showMessage();
        }
    }

    public function actionSaveChannel()
    {
        if(Yii::app()->request->isPostRequest){
            $id = isset($_POST['BankChannel']['id']) ? $_POST['BankChannel']['id'] : 0;
            $model = BankChannel::model()->findByPk($id);
            if($model == null)
                $model = new BankChannel();
            $model->attributes = $_POST['BankChannel'];
            $model->updated = time();
            $model->uid = Yii::app()->user->id;
            if($model->save()){
                $this->addMessage('state', 'success');
                $this->addMessage('message', '成功！');
                $this->addMessage('callback', 'sChannel');
            }
            else{
                $this->addMessage('state', 'fail');
                $err = current($model->getErrors());
                $this->addMessage('message', $err[0]);
            }
            $this->showMessage();
        }
    }

    public function getChannelButton($data)
    {
        echo '<a class="btn btn-info btn-xs" href="javascript:;" onclick="addChannel('.$data->id.')"><span class="glyphicon glyphicon-pencil"></span></a> ';
        echo '<a class="J_ajax_del btn btn-danger btn-xs" href="'.$this->createUrl("delChannel", array("id"=>$data->id)).'"><span class="glyphicon glyphicon-remove"></span></a>';
    }

    public function actionGetChannel($id)
    {
        if($id){
            $model = BankChannel::model()->findByPk($id);
            echo CJSON::encode($model->attributes);
        }
    }

    public function actionDelChannel()
    {
        if(Yii::app()->request->isPostRequest){
            $id = Yii::app()->request->getParam('id', 0);
            if($id){
                $model = BankChannel::model()->findByPk($id);
                if($model->delete()){
                    $this->addMessage('state', 'success');
                    $this->addMessage('message', '成功！');
                }
                else{
                    $this->addMessage('state', 'fail');
                    $err = current($model->getErrors());
                    $this->addMessage('message', $err[0]);
                }
                $this->addMessage('callback', 'dSchool');
                $this->showMessage();
            }
        }
    }
}