<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
    <h4 class="modal-title" id="exampleModalLabel"><?php echo Yii::t("asa","Payment Info");?></h4>
</div>
<?php $form=$this->beginWidget('CActiveForm', array(
    'id'=>'course-form',
    'enableAjaxValidation'=>false,
    'htmlOptions'=>array('class'=>'J_ajaxForm form-horizontal', 'role'=>'form'),
)); ?>
<div class="modal-body">
    <div class="form-group">
        <div class="col-xs-12 text-center">
            <h3><?php echo $vendorName->getName() ?></h3>
        </div>
    </div>    
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'bank'); ?></label>
        <div class="col-xs-8">
            <select class="form-control" name="bank">
            <?php foreach($bank as $key=>$val){;?>
            <option value="<?php echo $key;?>" <?php echo ($model->bank == $key) ? 'selected' : '' ; ?> ><?php echo Yii::app()->language == 'zh_cn' ? $val['cn'] : $val['en'] ;?></option>
            <?php };?>
            </select>

        </div>
    </div>    
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'bank_address'); ?></label>
        <div class="col-xs-8">
            <?php echo $form->textField($model,'bank_address',array('maxlength'=>255,'class'=>'form-control')); ?>
            <p class="help-block"><?php echo Yii::t("asa","开户行为工商银行的不需要填写支行信息");?></p>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'bank_account'); ?></label>
        <div class="col-xs-8">
            <?php echo $form->textField($model,'bank_account',array('maxlength'=>255,'class'=>'form-control')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'bank_name'); ?></label>
        <div class="col-xs-8">
            <?php echo $form->textField($model,'bank_name',array('maxlength'=>255,'class'=>'form-control')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $identify; ?> *</label>
        <div class="col-xs-8">
            <?php echo $form->textField($model,'identify',array('maxlength'=>30,'class'=>'form-control')); ?>
            <p class="help-block"><?php echo Yii::t("asa","For tax submission usage only.");?></p>
        </div>
    </div> 
</div>
<div class="modal-footer">
    <button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global','Submit');?></button>
    <button type="button" class="btn btn-default"  data-dismiss="modal"><?php echo Yii::t('global','Cancel');?></button>
</div>

<?php $this->endWidget(); ?>
