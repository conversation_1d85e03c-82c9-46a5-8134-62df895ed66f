<?php
$step2 = array(1, 2, 3, 4);
?>
<style>
    .crop{
        position:fixed;
        left: 0;
        top:0;
        width:100%;
        height: 100%;
        z-index:99;
    }
    .crop p{
        position: absolute;
        left: 50%;
        bottom: 0;
        margin-left: -137px;
    }
    .cropImg{
        width:200px
    }
    .photo-box{

    }
    .text{
        font-size:14px !important;
        color:red
    }
</style>
<?php echo $this->renderPartial('steps_header', array('currentStep'=>2)); ?>
<div class="container">
    <h5 class="htitle"><span class="fas fa-id-card-alt"></span> <?php echo $this->getStepTitle($this->step); ?></h5>
    <?php echo $this->renderPartial('_nouser_header'); ?>
    <div class="edit-content">
    <?php 
        $content = RegConfig::getContent($this->regStudent->reg_id, $this->step);
        if ($content) {
            echo CommonUtils::autoLang(base64_decode($content['cn']['card']), base64_decode($content['en']['card']));
        }
     ?>
    </div>
    
    <!-- 修改孩子头像 -->
    <h5 class="sub-title"><span><?php echo Yii::t('reg', "Student Profile") ?></span> <span class='text'><?php echo Yii::t('reg', "(ID photo taken within 3 months)") ?></span>  </h5>
    <div class="row mb-3">
        <input class="form-control-file" style="display:none;" onchange="fileChange(this,5)" accept="image/*" id="RegStep2Form_5_photoFile" type="file" data-enpassusermodified="yes">
        <div class="col-6 text-center">
            <img class="shadow img-fluid mb-2 rounded" src="<?php echo CommonUtils::childPhotoUrl($this->childObj->photo);?>" id="5_photoShow">
            <div><button class="btn btn-info btn-sm" type="button" onclick="choseImg(5)"><?php echo Yii::t('reg', "Change profile") ?></button></div>
        </div>
        <div class="col-6">
            <p class="mt-3"><?php echo $this->childObj->getChildName() ?></p>
        </div>
    </div>

    <!-- 英文名 -->
    <?php $form = $this->beginWidget('CActiveForm', array(
        'id' => 'sep1-form',
        'enableAjaxValidation' => false,
        'htmlOptions' => array(
            // 'class'=>'J_ajaxForm form-horizontal',
            'role' => 'form',
            'enctype' => 'multipart/form-data'
        ),
    )); ?>
    <div style="display: none;">
        <img src="#" id="cropper">
        <p>
            <button class="btn btn-primary btn-sm" type="button" onclick="cropperRotate(-90)"><?php echo Yii::t('reg', "Left Rotation") ?></button>
            <button class="btn btn-primary btn-sm" type="button" onclick="cropperRotate(90)"><?php echo Yii::t('reg', "Right Rotation") ?></button>
            <button class="btn btn-primary btn-sm" type="button" onclick="cropperSave()"><?php echo Yii::t('user', "Save") ?></button>
            <button class="btn btn-primary btn-sm" type="button" onclick="cropperReset()"><?php echo Yii::t('reg', "Reset") ?></button>
            <button class="btn btn-primary btn-sm" type="button" onclick="cropperBack()"><?php echo Yii::t('reg', "Out") ?></button>
        </p>
    </div>

    <?php if ($this->regStudent->schoolid == 'BJ_DS'): ?>
    <!-- 车牌号信息 -->
    <h5 class="sub-title"><span><?php echo Yii::t('reg', "Vehicle Plate Number") ?></span></h5>
    <div class="row mb-3">
        <div class="col-12">
            <input class="form-control" placeholder="" name="memo" id="memo" type="text" value="<?php echo $carInfo; ?>">
            <small id="emailHelp" class="form-text text-muted"><?php echo Yii::t('reg', "Using comma if registering more than one vehicle.") ?></small>
        </div>
    </div>
    <?php endif;?>
    <?php foreach ($formArray as $k => $formModel) {
        $showName = $k.'_photoShow';
        $inputName = 'RegStep2Form_'.$k.'_photoFile';
    ?>
        <h5 class="sub-title"><span><?php echo Yii::t('reg', 'People who may drop off/collect your child ') . ($k+1); ?></span></h5>
        <div class="row mb-3">
            <div class="col-6 text-center">
                <?php if($formModel->photo):?>
                    <img class="shadow img-fluid mb-2 rounded" src="<?php echo (strpos($formModel->photo, 'base64') === false) ? $formModel->getOssImageUrl($formModel->photo) : $formModel->photo; ?>" id="<?php echo $showName; ?>">
                <?php else:?>
                    <img class="shadow img-fluid mb-2 rounded" id="<?php echo $showName; ?>" style="display: none;">
                    <svg class="shadow img-fluid mb-2 rounded" width="200" height="200" id="<?php echo $showName; ?>_svg">
                        <rect width="100%" height="100%" fill="#868e96"></rect>
                        <text x="50%" y="50%" fill="#dee2e6" dy=".3em" text-anchor="middle"><?php echo Yii::t('reg', "Upload photo") ?></text>
                    </svg>
                <?php endif;?>
                <div><button class="btn btn-info btn-sm" type="button" onclick="choseImg(<?php echo $k; ?>)"><?php echo Yii::t('reg', "Upload photo") ?></button></div>
                <?php echo $form->fileField($formModel, '['. $k .']photoFile', array('class' => 'form-control-file', 'style'=>'display:none;', 'onchange'=>'fileChange(this,'.$k.')', 'accept'=>"image/*")); ?>
                <?php echo $form->hiddenField($formModel, '['. $k .']photo'); ?>
            </div>
            <div class="col-6">
                <div class="mb-2">
                    <?php echo $form->textField($formModel, '['. $k .']name', array('class' => 'form-control', 'placeholder'=>Yii::t("reg", 'Name'))); ?>
                </div>
                <div class="mb-2">
                    <?php echo $form->textField($formModel, '['. $k .']relation', array('class' => 'form-control', 'placeholder'=>Yii::t("reg", 'Relation'))); ?>
                </div>
                <div class="mb-2">
                    <?php echo $form->textField($formModel, '['. $k .']tel', array('class' => 'form-control', 'placeholder'=>Yii::t("reg", 'Tel'))); ?>
                </div>
            </div>
        </div>
    <?php } ?>

    <div class="mt-3">
        <button type="submit" class="btn btn-primary btn-lg btn-block examine"><?php echo Yii::t("reg", 'Submit'); ?></button>
        <p class="text-black-50 mt-3 text-center examine_status">
        </p>
    </div>
    <?php $this->endWidget(); ?>
</div>

<div class="modal fade" id="theModal" tabindex="-1" role="dialog" aria-labelledby="theModalLabel">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="theModalLabel">请仔细阅读校车协议</h4>
            </div>
            <div class="modal-body">
                <p>亲爱的家长们：<br/>
                    学校代表家长与班车公司协调班车服务。启明星学校目前与外包班车公司合作，为学生提供安全、方便和快捷的班车服务。家长交付的班车服务费将由学校转交给班车公司。与所有启明星其它运营一样，我们的班车服务也是非营利性的。在过去的6年里，学校对班车进行补贴，让几乎每一个班车乘坐需求得到满足。<br/>
                    根据每年秋季入学新生和返校生在申请班车时填写的家庭住址情况，启明星班车线路按学年在暑假时进行调整。在合理安排时间和距离的情况下，班车站点的设立将尽量给学生提供方便。尽管我们的班车路线将尽可能满足大部分家长的需求，但启明星无法保证门到门的接送服务。<br/>
                    3. 2017-2018 班车费用计算标准如下：<br/>
                    1). A地区 家校距离校园<6千米 = 9900元<br/>
                    2). B地区 6千米 ≤家校距离 ≤15千米 = 12760元<br/>
                    3). 家校距离>15千米 = 15620 元<br/>

                    6. 家校距离按照班车运行的实际距离计算-我们将尽最大可能来保持最短距离。请注意家校距离和班车费用并不是按从您家到学校的最短距离计算的</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary btn-sm"
                        data-dismiss="modal"><?php echo Yii::t('reg', '同意') ?></button>
            </div>
        </div>
    </div>
</div>

<script>
    if ($('#RegStep1Form_needBus_1').attr("checked") == 'checked') {
        $('#journeyBox').removeClass('hide');
    }

    $('input[name="RegStep1Form[needBus]"]').click(function () {
        if ($(this).val() == 1) {
            $('#journeyBox').removeClass('hide');
        }
        else {
            $('#journeyBox').addClass('hide');
        }
    });

    function choseImg(k) {
        var fileObj = $('#RegStep2Form_'+k+'_photoFile');
        $(fileObj).click();
    }

    var index;
    var cropper;
    function fileChange(obj, k) {
        index = k;
        if (obj.files) {
            var file = obj.files[0];
            if (file) {
                // if (cropper) {
                //     cropper.destroy();
                // }
                fr = new FileReader();
                fr.onload = function (e) {
                    // var origin_image = $('#'+k+'_photoShow');
                    var image = $('#cropper');
                    image[0].src = fr.result;
                    //初始化
                    cropper = new Cropper(image[0], {
                        aspectRatio: 1,
                        viewMode:1,
                        dragMode: 'move',
                    });
                    $('#cropper').parent().show()
                    $('#cropper').parent().addClass('crop');
                    $(obj).val("");
                };
                fr.readAsDataURL(file);
            }
        }
    }

    function cropperRotate(num) {
        if (cropper) {
            cropper.rotate(num);
        }
    }

    function cropperSave() {
        if (cropper) {
            var imageData = cropper.getCroppedCanvas().toDataURL('image/jpeg', 0.9);
            $('#'+index+'_photoShow').attr('src', imageData);
            $('#RegStep2Form_'+index+'_photo').val(imageData);
            cropper.destroy();
            $('#cropper').parent().hide();
            $('#cropper').parent().removeClass('crop');
            $('#'+index+'_photoShow').show();
            $('#'+index+'_photoShow_svg').hide();
            // 单独处理上传头像
            if (index == 5) {
                var blob = dataURLtoBlob(imageData);
                var formData = new FormData();
                formData.append('ChildProfileBasic[uploadPhoto]', blob);
                var request = new XMLHttpRequest();
                request.open('POST', '<?php echo $this->createUrl('/wechat/user/uploadPhoto', array('childid'=>$this->childObj->childid)); ?>', true);
                request.send(formData);
            }
        }
    }
    function cropperReset() {
        if (cropper) {
            cropper.reset();
        }
    }
    function cropperBack() {
        if (cropper) {
            cropper.destroy();
            $('#cropper').parent().hide();
            $('#cropper').parent().removeClass('crop');
        }
    }

    function dataURLtoBlob(dataurl) {
        var arr = dataurl.split(','), mime = arr[0].match(/:(.*?);/)[1],
            bstr = atob(arr[1]), n = bstr.length, u8arr = new Uint8Array(n);
        while(n--){
            u8arr[n] = bstr.charCodeAt(n);
        }
        return new Blob([u8arr], {type:mime});
    }
</script>