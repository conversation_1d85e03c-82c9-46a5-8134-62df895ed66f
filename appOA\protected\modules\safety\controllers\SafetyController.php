<?php

class SafetyController extends BranchBasedController
{
    public $printFW = array();
    public function createUrl($route, $params = array(), $ampersand = '&')
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        return parent::createUrl($route, $params, $ampersand);
    }

    public function init(){
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'campusOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site','Campus Workspace');

        $this->branchSelectParams['urlArray'] = array('//safety/safety/index');

        // jquery ui
        $cs = Yii::app()->clientScript;
        $cs->registerCoreScript('jquery.ui');
        $cs->registerCssFile($cs->getCoreScriptUrl().'/jui/css/base/jquery-ui.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/vue2.js');
        Yii::import('common.models.security.*');
    }

	public function actionIndex()
    {
        $type = Yii::app()->request->getParam('type', "");

        $safetyData = array();
        $model = array();
        $safetySelfCategoryArr = array();
        $ruleArr = array();
        $safetySelfItemArr = array();


        $id = Yii::app()->request->getParam('id', 0);
        $mid = Yii::app()->request->getParam('mid', 0);
        $yearData = Yii::app()->request->getParam('year', "");
        $monthData = Yii::app()->request->getParam('month', "");

        if($monthData && $yearData){
            $data = $yearData . sprintf("%02d", $monthData);
            $timeData = date("Ym", time());
            if($data <= $timeData){
                $safetyData = $this->getMenu($data, $timeData);
                if($id){
                    $model = SafetySelf::model()->findByPk($id);
                    if($model){
                        $criteria = new CDbCriteria();
                        $criteria->compare('school_id', $this->branchId);
                        $criteria->compare('month', $data);
                        $criteria->compare('sid', $model->id);
                        $criteria->compare('status', '<>9999');
                        $criteria->order = "rid";
                        $safetySelfItemModel = SafetySelfItem::model()->findAll($criteria);
                        $ruleArr = array();

                        $safetySelfItemArr = array();
                        if($safetySelfItemModel){
                            foreach ($safetySelfItemModel as $item){
                                $ruleArr[$item->cid][$item->rid] = $item->rulesName->getName();
                                $safetySelfItemArr[$item->rid] = array(
                                    'principal' => isset($item->userName) ? $item->userName->getName() : "",
                                    'document' => isset($item->rulesName) ? $item->rulesName->getDocName() : "",
                                    'record' => $item->record,
                                    'impore' => $item->impore,
                                    'score' => $item->score,
                                    'id' => $item->id,
                                );
                                $safetySelfCategory[$item->cid] = $item->cid;
                            }

                        }else{
                            $rulesData = $this->Rules($model->id, $data, $mid);
                            $ruleArr = $rulesData['ruleArr'];
                            $safetySelfItemArr = $rulesData['safetySelfItemArr'];
                            $safetySelfCategory = $rulesData['safetySelfCategoryId'];
                        }
                        $safetySelfCategoryModel = SafetySelfCategory::model()->findAllByPk($safetySelfCategory);

                        foreach ($safetySelfCategoryModel as $item) {
                            $safetySelfCategoryArr[$item->id] = $item->getName();
                        }
                    }
                }
            }
        }

        $this->render('index', array(
            'safetyData' => $safetyData,
            'id' => $id,
            'model' => $model,
            'ruleArr' => $ruleArr,
            'safetySelfCategoryArr' => $safetySelfCategoryArr,
            'monthData' => $monthData,
            'yearData' => $yearData,
            'safetySelfItemArr' => $safetySelfItemArr,
            'mid' => $mid,
        ));
	}

	public function actionShowCheck()
    {
        $type = Yii::app()->request->getParam('type', "");
        $yearData = Yii::app()->request->getParam('year', 0);
        $monthData = Yii::app()->request->getParam('month', 0);
        $data = $yearData . sprintf("%02d", $monthData);
        $timeData = date("Ym", time());
        $safetyData = array();
        if($data <= $timeData) {
            $safetyData = $this->getMenu($data, $timeData);
        }

        $criteria = new CDbCriteria();
        $criteria->compare('month', $data);
        $criteria->compare('school_id', $this->branchId);
        $criteria->compare('status', 1);
        $itemModel = SafetySelfItem::model()->findAll($criteria);
        $scoreArr = array();
        $user = array();
        if($itemModel){
            foreach ($itemModel as $item) {
                $scoreArr[$item->sid] += $item->score;
                $user[$item->sid][$item->principal] = $item->userName->getName();
            }
        }

        $this->render('_show', array(
            'type' => $type,
            'safetyData' => $safetyData,
            'scoreArr' => $scoreArr,
            'yearData' => $yearData,
            'monthData' => $monthData,
            'school' => $this->branchObj->abb,
            'user' => $user,
            'id' => 9999999,
        ));
    }

	public function actionUpdateRules()
    {
        $data = array(
            'state' => 'fail',
            'message' => '参数错误',
        );

        if($_POST['mid']){
            $monthModel = SafetySelfMonth::model()->findByPk($_POST['mid']);
            if($monthModel->status != 1){
                $criteria = new CDbCriteria();
                $criteria->compare('month', $_POST['year'] . sprintf("%02d",$_POST['month']));
                $criteria->compare('cid', $_POST['cid']);
                $criteria->compare('rid', $_POST['rid']);
                $criteria->compare('school_id', $this->branchId);
                $criteria->compare('status', array(0,1));
                $safetySelfItem = SafetySelfItem::model()->find($criteria);
                if($safetySelfItem->principal){
                    $safetySelfItem->status = 9999;
                    $safetySelfItem->save();

                    $model = new SafetySelfItem();
                    $model->school_id = $this->branchId;
                    $model->month = $_POST['year'] . sprintf("%02d", $_POST['month']);
                    $model->sid = $_POST['sid'];
                    $model->cid = $_POST['cid'];
                    $model->rid = $_POST['rid'];
                    $model->mid = $_POST['mid'];
                    $model->sort = 1;
                    $model->principal = Yii::app()->user->id;
                    $model->record = $_POST['record'];
                    $model->impore = $_POST['impore'];
                    $model->score = $_POST['score'];
                    $model->status = 1;
                    $model->created_at = time();
                    $model->created_by = Yii::app()->user->id;
                    $model->updated_at = time();
                    $model->updated_by = Yii::app()->user->id;
                    if($model->save()){
                        $user = User::model()->findByPk($model->principal);
                        $data = array(
                            'state' => 'success',
                            'data' => array(
                                'uid' => $user->getName(),
                                'message' => '成功',
                            ),
                        );

                    }else{
                        $error = current($model->getErrors());
                        $data = array(
                            'state' => 'fail',
                            'message' => $error,
                        );
                    }
                }else{
                    $safetySelfItem->principal = Yii::app()->user->id;
                    $safetySelfItem->record = $_POST['record'];
                    $safetySelfItem->impore = $_POST['impore'];
                    $safetySelfItem->score = $_POST['score'];
                    $safetySelfItem->status = 1;
                    if($safetySelfItem->save()){
                        $user = User::model()->findByPk($safetySelfItem->principal);
                        $data = array(
                            'state' => 'success',
                            'data' => array(
                                'uid' => $user->getName(),
                            ),
                        );

                    }else{
                        $error = current($safetySelfItem->getErrors());
                        $data = array(
                            'state' => 'fail',
                            'message' => $error,
                        );
                    }
                }
            }else{
                $data = array(
                    'state' => 'fail',
                    'message' => '已经提交，修改请联系安全组',
                );
            }
        }
        echo json_encode($data);
    }

    public function actionReview()
    {
        $data = array(
            'state' => 'fail',
            'message' => '参数错误',
        );
        if (Yii::app()->request->isPostRequest) {
            $check_val = Yii::app()->request->getParam('check_val', 0);
            if($check_val){
                $monthModel = SafetySelfMonth::model()->findAllByPk($check_val);
                $criteria = new CDbCriteria();
                $criteria->compare('mid', $check_val);
                $criteria->index = 'mid';
                $count = SafetySelfItem::model()->findAll($criteria);
                if(count($count) == count($check_val)){
                    foreach ($monthModel as $item){
                        $item->status = 1;
                        $item->updated_at = time();
                        $item->updated_by = Yii::app()->user->id;
                        $item->save();
                    }
                    $data = array(
                        'state' => 'success',
                        'message' => '成功',
                    );
                }else{
                    $data = array(
                        'state' => 'fail',
                        'message' => '有未生成数据的项目',
                    );
                }
            }else{
                $data = array(
                    'state' => 'fail',
                    'message' => '未找到数据',
                );
            }
        }
        echo json_encode($data);
    }

    public function getMenu($data, $timeData)
    {
        $safetyData = array();
        $criteria = new CDbCriteria();
        $criteria->compare('schoold_id', $this->branchId);
        $criteria->compare('month', $data);
        $safetySelfMonthModel = SafetySelfMonth::model()->findAll($criteria);

        if($safetySelfMonthModel){
            foreach($safetySelfMonthModel as $item){
                $safetyData[$item->sid] = array(
                    'status' => $item->status,
                    'name' => $item->self->getName(),
                    'mid' => $item->id,
                    'type' => $item->self->type,
                );
            }
        }
        if($data <= $timeData){
            $criteria = new CDbCriteria();
            $criteria->compare('status', 1);
            if($safetyData){
                $sidData = array_keys($safetyData);
                $criteria->addNotInCondition('id', $sidData);
            }
            $criteria->order = 'sort DESC';
            $safetySelfModel = SafetySelf::model()->findAll($criteria);
            if($safetySelfModel){
                foreach($safetySelfModel as $item){
                    $model = new SafetySelfMonth();
                    $model->schoold_id = $this->branchId;
                    $model->month = $data;
                    $model->sid = $item->id;
                    $model->status = 0;
                    $model->created_at = time();
                    $model->created_by = Yii::app()->user->id;
                    $model->updated_at = time();
                    $model->updated_by = Yii::app()->user->id;
                    $model->save();
                    $safetyData[$item->id] = array(
                        'status' => 0,
                        'name' => $item->getName(),
                        'mid' => $model->id,
                        'type' => $item->type,
                    );
                }
            }
        }

        return $safetyData;
    }

    public function Rules($sid, $data, $mid)
    {
        $ruleArr = array();
        $safetySelfItemArr = array();
        $safetySelfCategoryId = array();
        $criteria = new CDbCriteria();
        $criteria->compare('sid', $sid);
        $criteria->compare('status', 1);
        $criteria->compare('start_time', "<={$data}");
        $criteria->order = 'sort DESC';
        $safetySelfRulesModel = SafetySelfRules::model()->findAll($criteria);

        foreach ($safetySelfRulesModel as $val)  {
            if(empty($val->del_time) || $val->del_time > $data){
                $ruleArr[$val->cid][$val->id] = $val->getName();
                $model = new SafetySelfItem();
                $model->school_id = $this->branchId;
                $model->month = $data;
                $model->sid = $val->sid;
                $model->cid = $val->cid;
                $model->rid = $val->id;
                $model->mid = $mid;
                //$model->document = $val->document;
                $model->sort = 1;
                $model->status = 0;
                $model->created_at = time();
                $model->created_by = Yii::app()->user->id;
                $model->updated_at = time();
                $model->updated_by = Yii::app()->user->id;
                $model->save();
                $safetySelfItemArr[$val->id] = array(
                    'principal' => "",
                    'document' => $val->getDocName(),
                    'record' => "",
                    'impore' => "",
                    'score' => "",
                    'id' => $val->id,
                );
                $safetySelfCategoryId[$val->cid] = $val->cid;
            }
        }

        $ruledata['ruleArr'] = $ruleArr;
        $ruledata['safetySelfItemArr'] = $safetySelfItemArr;
        $ruledata['safetySelfCategoryId'] = $safetySelfCategoryId;

        return $ruledata;

    }

    public function actionAccident()
    {
        $critre = new CDbCriteria;
        $critre->compare('school_id', $this->branchId);
        $critre->addNotInCondition('status', array(9999));
        $dataProvider = new CActiveDataProvider('SafetyIncidentReport', array(
            'criteria' => $critre,
            'pagination'=>array(
                'pageSize'=>50,
            ),
        ));

        $this->render('accident', array(
            'dataProvider' => $dataProvider,
        ));
    }

    public function actionUpdate()
    {
        $id = Yii::app()->request->getParam('id', 0);
        $model = SafetyIncidentReport::model()->findByPk($id);
        if(empty($model)){
            $model = new SafetyIncidentReport();
        }
        $userModel_t = User::model()->findByPk(Yii::app()->user->id);

        if(Yii::app()->request->isPostRequest){
            $model->attributes = $_POST['SafetyIncidentReport'];
            if(empty($_POST['SafetyIncidentReport']['month']) || empty($_POST['SafetyIncidentReport']['minute'])){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '报告时间不能为空');
                $this->showMessage();
            }
            if($model->incident_type == 1){
                if(empty($model->child_id)  || empty($_POST['SafetyIncidentReport']['injured_part_child']) || empty($model->involved_teacher)){
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', '出事学生资料未填写全');
                    $this->showMessage();
                }
                $childModel = ChildProfileBasic::model()->findByPk($model->child_id);
                if($childModel->schoolid != $this->branchId){
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', '所选孩子不是本校园');
                    $this->showMessage();
                }
                $model->class_id = $childModel->classid;
            }

            if($model->incident_type == 2){
                if(empty($model->staff) || empty($_POST['SafetyIncidentReport']['injured_part_teacher'])){
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', '出事老师资料未填写全');
                    $this->showMessage();
                }
                $userModel = User::model()->findByPk($model->staff);
                if($userModel->profile->branch != $this->branchId){
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', '所选老师不是本校园');
                    $this->showMessage();
                }
                $model->staff_position = $userModel->profile->occupation_en;
            }
            $model->injured_part = ($model->incident_type == 1) ? $_POST['SafetyIncidentReport']['injured_part_child'] : $_POST['SafetyIncidentReport']['injured_part_teacher'] ;

            $model->school_id = $this->branchId;
            $model->reported_date = time();
            $model->reported_by = $userModel_t->uid;
            $model->reported_position = $userModel_t->profile->occupation_en;
            $model->incident_date = strtotime($_POST['SafetyIncidentReport']['month'] . $_POST['SafetyIncidentReport']['minute']);
            $model->status = 0;
            $model->created_at = time();
            $model->created_by = Yii::app()->user->id;
            $model->updated_at = time();
            $model->updated_by = Yii::app()->user->id;

            if($model->save()){

                /*$mailer = Yii::createComponent('common.extensions.mailer.EMailer');
                $mailer->Subject = sprintf('%s转来一个潜在客户', 'CS');
                $mailer->AddAddress('<EMAIL>');
                $mailer->AddCC('<EMAIL>');
                $mailer->getView('safety', array('branchId' => '1'), 'main');
                $mailer->iniMail( OA::isProduction());
                $mailer->Send();*/

                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message','success'));
                $this->addMessage('callback', 'cbReport');
            } else {
                $this->addMessage('state', 'fail');
                $err = current($model->getErrors());
                $this->addMessage('message', $err ? $err[0] : '失败');
            }
            $this->showMessage();
        }
        $childBasicModel = array();
        $userArr = array();
        $user = array();
        if($model->id){
            $model->month = date("Y-m-d", $model->incident_date);
            $model->minute = date("H:i:s", $model->incident_date);
            $model->incident_date = date("Y-m-d H:i:s", $model->incident_date);
            $model->reported_date = date("Y-m-d", $model->reported_date);
            if($model->incident_type == 1){
                $model->injured_part_child = $model->injured_part;
            }
            if($model->incident_type == 2){
                $model->injured_part_teacher = $model->injured_part;
            }

            $userArr = array($model->involved_teacher => $model->involved_teacher);
            if($model->incident_type == 1){
                $criteria = new CDbCriteria();
                $criteria->compare('childid', $model->child_id);
                $criteria->index = "childid";
                $childBasicModel = ChildProfileBasic::model()->find($criteria);

            }

            if($model->incident_type == 2){
                $userArr[$model->staff] = $model->staff;
            }

            $criteria = new CDbCriteria();
            $criteria->compare('uid', $userArr);
            $criteria->index = "uid";
            $user = User::model()->findAll($criteria);
        }

        $model->reported_by = $userModel_t->getName();
        $model->reported_position = $userModel_t->profile->occupation->getName();

        $this->render('_update', array(
            'model' => $model,
            'userModel' => $userModel_t,
            'userArr' => $userArr,
            'user' => $user,
            'childBasicModel' => $childBasicModel,
        ));
    }

    public function actionDelete()
    {
        $id = Yii::app()->request->getParam('id', 0);
        if(Yii::app()->request->isPostRequest){
            $model = SafetyIncidentReport::model()->findByPk($id);
            if($model && $model->status < 1){
                $model->status = 9999;
                if($model->save()){
                    $this->addMessage('state', 'success');
                    $this->addMessage('message', Yii::t('message','success'));
                    $this->addMessage('callback', 'cbDelReport');
                } else {
                    $this->addMessage('state', 'fail');
                    $err = current($model->getErrors());
                    $this->addMessage('message', $err ? $err[0] : '失败');
                }
                $this->showMessage();
            }else{
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '不可删除,请联系总部');
                $this->showMessage();
            }

        }
    }

    public function actionNewReport()
    {
        $id = Yii::app()->request->getParam('id', 0);
        if(Yii::app()->request->isPostRequest) {
            $model = SafetyIncidentReport::model()->findByPk($id);
            if($model){
                $model->status = 1;
                if($model->save()){
                    $this->addMessage('state', 'success');
                    $this->addMessage('message', Yii::t('message','success'));
                } else {
                    $this->addMessage('state', 'fail');
                    $err = current($model->getErrors());
                    $this->addMessage('message', $err ? $err[0] : '失败');
                }
                $this->showMessage();
            }
        }
    }

    //查看事故进度
    public function actionShowReport()
    {
        Yii::import('common.models.child.*');
        Yii::import('common.models.hr.*');
        $id = Yii::app()->request->getParam('id', "");
        $model = SafetyIncidentReport::model()->findByPk($id);

        $criteria = new CDbCriteria();
        $criteria->compare('report', $model->id);
        $criteria->compare('school_id', $model->school_id);
        $criteria->compare('status', SafetyIncidentReport::STATUS_ACTIVE);
        $trackModel = SafetyIncidentTrack::model()->find($criteria);
        if(!$trackModel){
            $trackModel = new SafetyIncidentTrack();
        }
        $userArr = array($model->reported_by => $model->reported_by);
        $position = array($model->reported_position => $model->reported_position);
        $childModel = array();
        if($model->incident_type == 1){
            $criteria = new CDbCriteria();
            $criteria->compare('childid', $model->child_id);
            $criteria->index = "childid";
            $childModel = ChildProfileBasic::model()->find($criteria);
            $userArr[$model->involved_teacher] = $model->involved_teacher;
        }

        if($model->incident_type == 2){
            $userArr[$model->staff] = $model->staff;
            $position[$model->staff_position] = $model->staff_position;
        }

        $criteria = new CDbCriteria();
        $criteria->compare('id', $position);
        $criteria->index = "id";
        $hrModel = HrPosition::model()->findAll($criteria);

        $criteria = new CDbCriteria();
        $criteria->compare('uid', $userArr);
        $criteria->index = "uid";
        $userModel = User::model()->findAll($criteria);


        $this->render('follow', array(
            'model' => $model,
            'trackModel' => $trackModel,
            'userModel' => $userModel,
            'childModel' => $childModel,
            'hrModel' => $hrModel,
            'schoolid' => $this->branchObj->abb,
        ));
    }

    // 打印校园安全自查
    public function actionPrint()
    {
        $this->layout = '//layouts/print';
        $this->printFW = $this->branchObj->getPrintHeader();

        $id = Yii::app()->request->getParam('id', 0);
        $mid = Yii::app()->request->getParam('mid', 0);
        $yearData = Yii::app()->request->getParam('year', "");
        $monthData = Yii::app()->request->getParam('month', "");

        $data = $yearData . sprintf("%02d", $monthData);

        // 安全报告大类型
        $criteria = new CDbCriteria();
        $criteria->compare('schoold_id', $this->branchId);
        $criteria->compare('month', $data);
        $safetySelfMonthModel = SafetySelfMonth::model()->findAll($criteria);
        $safetyData = array();
        foreach ($safetySelfMonthModel as $item){
            $safetyData[$item->sid] = array(
                'status' => $item->status,
                'name' => $item->self->getName(),
                'mid' => $item->id,
                'type' => $item->self->type,
            );
        }

        $model = SafetySelf::model()->findByPk($id);

        $criteria = new CDbCriteria();
        $criteria->compare('school_id', $this->branchId);
        $criteria->compare('month', $data);
        $criteria->compare('sid', $model->id);
        $criteria->compare('status', '<>9999');
        $criteria->order = "rid";
        $safetySelfItemModel = SafetySelfItem::model()->findAll($criteria);

        $safetySelfItemArr = array();
        $ruleArr = array();
        $safetySelfCategory = array();
        foreach ($safetySelfItemModel as $item){
            $ruleArr[$item->cid][$item->rid] = $item->rulesName->getName();
            $safetySelfItemArr[$item->rid] = array(
                'principal' => isset($item->userName) ? $item->userName->getName() : "",
                'document' => isset($item->rulesName) ? $item->rulesName->getDocName() : "",
                'record' => $item->record,
                'impore' => $item->impore,
                'score' => $item->score,
                'id' => $item->id,
            );
            $safetySelfCategory[$item->cid] = $item->cid;
        }

        $safetySelfCategoryModel = SafetySelfCategory::model()->findAllByPk($safetySelfCategory);
        $safetySelfCategoryArr = array();
        foreach ($safetySelfCategoryModel as $item) {
            $safetySelfCategoryArr[$item->id] = $item->getName();
        }
        $yearMonth = $yearData . '-' .sprintf("%02d", $monthData);
        $this->render('print', array(
            'model' => $model,
            'safetyData' => $safetyData,
            'safetySelfItemArr' => $safetySelfItemArr,
            'safetySelfCategoryArr' => $safetySelfCategoryArr,
            'ruleArr' => $ruleArr,
            'yearMonth' => $yearMonth,
        ));
    }

    // 打印校园安全自查
    public function actionPrintMonthly()
    {
        $this->layout = '//layouts/print';
        $this->printFW = $this->branchObj->getPrintHeader();

        $type = Yii::app()->request->getParam('type', "");
        $yearData = Yii::app()->request->getParam('year', 0);
        $monthData = Yii::app()->request->getParam('month', 0);
        $data = $yearData . sprintf("%02d", $monthData);
        $timeData = date("Ym", time());
        $safetyData = array();
        if($data <= $timeData) {
            $safetyData = $this->getMenu($data, $timeData);
        }

        $criteria = new CDbCriteria();
        $criteria->compare('month', $data);
        $criteria->compare('school_id', $this->branchId);
        $criteria->compare('status', 1);
        $itemModel = SafetySelfItem::model()->findAll($criteria);
        $scoreArr = array();
        $user = array();
        if($itemModel){
            foreach ($itemModel as $item) {
                $scoreArr[$item->sid] += $item->score;
                $user[$item->sid][$item->principal] = $item->userName->getName();
            }
        }
        $yearMonth = $yearData . '-' .sprintf("%02d", $monthData);
        $this->render('printMonthly', array(
            'type' => $type,
            'safetyData' => $safetyData,
            'scoreArr' => $scoreArr,
            'user' => $user,
            'yearMonth' => $yearMonth,
        ));
    }


    public function getButton($data)
    {
        if($data->status >= 1){
            echo '<a title="已经提交" class="btn btn-success btn-xs" href="javascript:;">已经提交</a> ';
            echo CHtml::link('查看', array('showReport', 'id' => $data->id), array('class' => 'btn btn-xs btn-info drop')) . ' ';
        }else{
            echo '<a title="提交" class="btn btn-warning btn-xs" href="javascript:;" onclick="addNewRepoet('.$data->id.')">提交</span></a> ';
            echo CHtml::link('编辑', array('update', 'id' => $data->id, "branchId"=>Yii::app()->controller->branchId), array('class' => 'btn btn-xs btn-info')) . ' ';
        }
        echo CHtml::link('删除', array('delete', 'id' => $data->id, "branchId"=>Yii::app()->controller->branchId), array('class' => 'J_ajax_del btn btn-xs btn-danger'));
    }
}