<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('//mcampus/default/index')) ?></li>
        <li><?php echo CHtml::link(Yii::t('site', 'Routines'), array('//mcampus/default/index')) ?></li>
        <li class="active"><?php echo Yii::t('site', '课后课管理') ?></li>
        <li class="active"><?php echo Yii::t('site', '退费列表') ?></li>
    </ol>

    <div class="row">
        <div class="col-md-2 col-sm-2 mb10">
            <?php
            $this->widget('zii.widgets.CMenu', array(
                'items' => $this->module->getMenu(),
                'id' => 'pageCategory',
                'htmlOptions' => array('class' => 'nav nav-pills nav-stacked background-gray'),
                'activeCssClass' => 'active',
            ));
            ?>
        </div>

        <div class="col-md-9">

            <select autocomplete="off" class="form-control" style="width: auto" id="selectstartyear">
                <option value="">请选择学年</option>
                <?php
                if(empty($_GET['year'])){
                    //默认最新的学年
                    $getUrlYear = $startyearArr[0];
                }else{
                    $getUrlYear = $_GET['year'];
                }
                foreach ($startyearArr as $value){
                    if(Yii::app()->language == 'zh_cn'){
                        $msg = ' 学年';
                    }else{
                        $msg =' School Year';
                    }
                    $schoolYear = $value.'-'.($value+1).$msg;
                    if($value == $getUrlYear){
                        echo "<option value='$value' selected>$schoolYear</option>";
                    }else{
                        echo "<option value='$value'>$schoolYear</option>";
                    }
                }
                ?>
            </select>
            <br>
            <?php
            echo "<span class=span_year$year>";
            foreach ($newCourseGroup as $year => $courseGroupItem){
                if($getUrlYear == $year){
                    echo "<ul class='nav nav-pills mb20 year$year'>";
                }else{
                    echo "<ul class='nav nav-pills mb20 year$year' style='display: none'>";
                }
                foreach ($courseGroupItem as $v) {
                    $href = $this->createUrl('index', array('groupId' => $v['id'],'year'=>$year));
                    $active = '';
                    $title = Yii::app()->language == "zh_cn" ?
                        empty($v['title_cn']) ? $v['title_en'] : $v['title_cn']
                        :
                        empty($v['title_en']) ? $v['title_cn'] : $v['title_en'];
                    if ($v['id'] == $groupId) {
                        $active = 'active';
                    }
                    echo "<li class='background-gray mr5 mb5 {$active}'><a href='{$href}'>";
                    echo $title;
                    echo '</a></li>';
                }
                echo '</ul>';
            }
            echo '</span>';
            ?>

        </div>

        <div class="col-md-10" id="courseObj">
            <?php if ($courseObj): ?>
            <div class="">
                <div class="panel panel-default">
                    <ul class="nav nav-pills panel-body stackedUl">

                        <ul class="nav nav-pills nav-stacked col-md-2">
                            <?php
                            echo CHtml::dropDownList('SchoolBusChild[busId]', $courseId, $courseList,
                                array('class' => 'form-control changeCourse', 'empty' => '请选择'));
                            ?>
                        </ul>
                        <?php
                        foreach ($childObj as $v) {
                            $href = $this->createUrl('index', array('groupId' => $groupId, 'courseId' => $courseId, 'childid' => $v['childid']));
                            $active = '';

                            echo "<li class='{$active}'><a href='javascript:void(0)' style='padding: 7px 15px;' _value='{$v['childid']}'>";
                            echo $v['childName'];
                            echo '</a></li>';
                        }
                        ?>
                    </ul>
                </div>
            </div>

        </div>
        <?php if ($childObj): ?>
        <div class="col-md-10" id="childObj">
            <div class="exchangeBox" style="display: none">
                <form action="<?php echo $this->createUrl('updataExchange') ?>" method="post"
                      class="J_ajaxForm form-horizontal">
                    <div class="" id="exchangeVueModel" v-if="childId" v-cloak>
                        <div class="page-header" style="margin-top: 0">
                            <h3 id="selected-childname" style="margin-top: 0">
                                <span class="glyphicon glyphicon-user" aria-hidden="true"></span> <span
                                    class="child-name">
                                    {{childData[childId].childName}}
                                    </span>
                                <small>
                                    {{childData[childId].classid}}
                                </small>

                            </h3>

                        </div>
                        <div class="col-md-5 ">
                        <div class="panel panel-default">
                            <div class="panel-heading"><h4 class="text-center"><?php echo Yii::t('asa', '当前课程'); ?></h4></div>
                            <div class="panel-body">
                                <div class="form-group">
                                    <label
                                        class="col-xs-4 control-label text-right"><?php echo Yii::t('asa', '所在课程：'); ?></label>

                                    <div class="col-xs-8 form-control-static">
                                        {{courseData[courseId].title_cn}} ( {{refundData.courseUnitPrice}} ×
                                        {{refundData.courseNumber}} )
                                    </div>
                                </div>
                                <div class="form-group">

                                    <div class="col-xs-4 text-right">
                                        <label class="control-label"><?php echo Yii::t('asa', '旧课程消耗节数：'); ?></label>
                                    </div>
                                    <div class="col-xs-8">
                                        <div v-if="isEmpty(refundData)">
                                            <select name="oldCourseDetail" class="form-control" @change="changeCourse"
                                                    v-model="oldPriceNum">
                                                <option
                                                    value=""><?php echo Yii::t('global', 'Please Select'); ?></option>
                                                <option value="0"
                                                        v-if="parseFloat(refundData.courseNumber) - parseFloat(refundData.status) > 0">
                                                    0
                                                </option>
                                                <option :value="num"
                                                        v-for="num in parseFloat(refundData.courseNumber) - parseFloat(refundData.status) - 1">
                                                    {{num}}
                                                </option>
                                            </select>
                                            <span class="text-danger" v-if="refundData.status != 0">已退过 {{refundData.status}} 节</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="col-xs-4 text-right">
                                        <label class="control-label">
                                            <?php echo Yii::t('asa', '旧课程金额：'); ?>
                                        </label>
                                    </div>
                                    <div class="col-xs-8 form-control-static">
                                        <span v-if="oldPriceNum"> 已消耗金额：¥ {{toFixed2(refundData.courseUnitPrice * oldPriceNum)}}</span>
                                    </div>
                                </div>

                                <div class="form-group" style="margin-bottom: 0">
                                    <div class="col-xs-4">
                                    </div>
                                    <div class="col-xs-8">
                                        <span v-if="oldPriceNum">剩余金额：¥{{toFixed2( (refundData.courseUnitPrice * (refundData.courseNumber - refundData.status) ) - (refundData.courseUnitPrice * oldPriceNum) )}}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        </div>
                        <div class="col-md-1 text-center">
                            <i style="font-size: 28px;padding-top: 50px"
                               class="glyphicon glyphicon-arrow-right"></i>
                        </div>
                        <div class="col-md-6">
                        <div class=" panel panel-default">
                            <div class="panel-heading"><h4 class="text-center"><?php echo Yii::t('asa', '调换课程'); ?></h4></div>
                            <div class="panel-body">
                                <div class="form-group">
                                    <label class="col-xs-4 control-label text-right ">
                                        <?php echo Yii::t('asa', '调换至：'); ?>
                                    </label>

                                    <div class="col-xs-4">
                                        <select class="form-control" v-model="changeGroup" @change="changeGroups">
                                            <option value=""><?php echo Yii::t('asa', '请选择课程组'); ?></option>
                                            <option :value="courseGroup.id" v-for="courseGroup in courseGroupData">
                                                {{courseGroup.title_cn}}
                                            </option>
                                        </select>
                                    </div>
                                    <div class="col-xs-4">
                                        <select name="NewCourseId" class="form-control" v-model="exchangeCourse"
                                                @change="changeNewCourse">
                                            <option value=""><?php echo Yii::t('asa', '请选择课程'); ?></option>
                                            <option :value="course.id" v-if="courseId != course.id"
                                                    v-for="course in newCourseData">{{course.title_cn}} <span
                                                    class="pull-right"> ( {{course.unit_price}} × {{course.default_count}} ) </span>
                                            </option>
                                        </select>
                                    </div>
                                </div>

                                <div class="form-group">

                                    <div class="col-xs-4 text-right">
                                        <label class=" control-label"><?php echo Yii::t('asa', '新课程需上节数：'); ?></label>
                                    </div>
                                    <div class="col-xs-8">
                                        <div v-if="exchangeCourse !=''">
                                            <select name="newCourseDetail" class="form-control" @change="changeCourse"
                                                    v-model="newPriceNum">
                                                <option
                                                    value=""><?php echo Yii::t('global', 'Please Select'); ?></option>
                                                <option :value="num"
                                                        v-for="num in parseFloat(newCourseData[exchangeCourse].default_count)">
                                                    {{num}}
                                                </option>
                                            </select>
                                        </div>
                                        <div class="form-control-static"
                                             v-else><?php echo Yii::t('asa', '请先选择调换课程'); ?></div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="col-xs-4 text-right">
                                        <label class="control-label">
                                            <?php echo Yii::t('asa', '新课程金额：'); ?>
                                        </label>
                                    </div>
                                    <div class="col-xs-8 form-control-static" v-if="exchangeCourse && newPriceNum">
                                        ¥ {{toFixed2( newCourseData[exchangeCourse].unit_price * newPriceNum )}}
                                    </div>
                                </div>

                                <div class="form-group" style="margin-bottom: 0">
                                </div>
                            </div>
                        </div>
                        </div>
                        <input type="hidden" name="oldCourseId" :value="courseId"/>
                        <input type="hidden" name="childId" :value="childId"/>
                        <input type="hidden" name="difference" :value="difference"/>
                    </div>
                    <div style="clear: both"></div>
                    <div class="modal-footer">

                        <div class="">

                        </div>
                        <div class="form-group" id="differencePrice" style="font-size: 20px;display: none">
                            <div class="col-md-offset-8 col-xs-2 text-right">
                                <label class="control-label differencePriceText">
                                        <?php echo Yii::t('asa', '需补差价：'); ?>
                                </label>
                            </div>
                            <div class="col-xs-2 text-right" style="font-size: 30px;color: #E45720">
                                ¥ <p class="differencePriceNum" style="display: inline-block"></p>
                            </div>
                        </div>
                        <div class="form-group" id="differenceTitle" style="display: none">
                            <label
                                class="col-md-offset-6 col-md-2 text-right control-label"><?php echo Yii::t('asa', '差价账单标题'); ?>
                            </label>

                            <div class="col-md-4">
                                <input type="text" name="inoviceTitle" class="form-control"
                                       placeholder="例：舞蹈课换武术课差价账单"/>
                            </div>
                        </div>
                        <button type="button" class="btn btn-primary J_ajax_submit_btn pull-right">
                            <?php echo Yii::t('global', '确认换课'); ?>
                        </button>
                    </div>
                </form>
            </div>
            <div id="hasRefundChild" style="display: none"><?php echo Yii::t('asa', '该学生有正在退费课程或已退完课程'); ?></div>
        </div>
    </div>
</div>
<script>
    var courseData = <?php echo CJSON::encode($courseObj); ?>;
    var courseList = <?php echo CJSON::encode($courseList); ?>;
    var courseGroupData = <?php echo CJSON::encode($courseGroup); ?>;
    console.log(courseGroupData);
    var groupId = <?php echo CJSON::encode($groupId); ?>;
    var courseId = <?php echo CJSON::encode($courseId); ?>;
    var childData = <?php echo CJSON::encode($childObj); ?>;
    var exchangeVue = new Vue({     //换课数据模型
        el: "#exchangeVueModel",
        data: {
            courseGroupData: courseGroupData,
            courseData: courseData,
            newCourseData: courseData,
            childData: childData,
            changeGroup: groupId,
            childId: '',
            exchangeCourse: '',
            courseId: courseId,
            groupId: groupId,
            oldPriceNum: '',
            newPriceNum: '',
            refundData: {}    //账单信息
        }, beforeUpdate: function () {
        },
        updated: function () {
        },
        methods: {
            parseFloat: function (num) {
                return parseFloat(num);
            },
            inverse: function (num) {
                return -num;
            },
            isEmpty: function (obj) {
                return Object.keys(obj).length;
            },
            changeNewCourse: function (num) {
                this.newPriceNum = '';
            },
            changeCourse: function () {
                var _this = this;
                if (this.difference < 0) {
                    $('#differenceTitle').show().find('input').removeAttr('disabled');
                    $('#differencePrice').show().find('.differencePriceText').text('<?php echo Yii::t('asa', '需补差价'); ?>');
                    $('.differencePriceNum').text( _this.inverse( _this.difference ) );
                    $('.J_ajax_submit_btn').text('<?php echo Yii::t('asa', '生成账单'); ?>');
                }else if (this.difference > 0){
                    $('#differenceTitle').hide().find('input').attr('disabled', 'disabled');
                    $('#differencePrice').show().find('.differencePriceText').text('<?php echo Yii::t('asa', '需退差价'); ?>');
                    $('.differencePriceNum').text(_this.difference);
                    $('.J_ajax_submit_btn').text('<?php echo Yii::t('asa', '确认换课'); ?>');
                } else {
                    $('#differenceTitle').hide().find('input').attr('disabled', 'disabled');
                    $('#differencePrice').hide();
                    $('.J_ajax_submit_btn').text('<?php echo Yii::t('asa', '确认换课'); ?>');
                }
            },
            toFixed2: function (num) {
                return num.toFixed(2);
            },
            changeGroups: function () {
                var _this = this;
                var _groupId = this.changeGroup;
                if (!_groupId) {
                    _this.exchangeCourse = '';
                    return false
                }
                $.ajax({
                    type: "POST",
                    url: "<?php echo $this->createUrl('demandCourse');?>",
                    data: {groupId: _groupId},
                    dataType: 'json',
                    success: function (list) {
                        _this.exchangeCourse = '';
                        _this.newCourseData = list;
                    }
                })
            }
        },
        computed: {
            difference: function () {
                var _this = this;
                var _difference = 0;
                if (_this.newPriceNum != '' && _this.oldPriceNum != '' && _this.exchangeCourse != '') {
                    var _oldPrice = parseFloat(( (_this.refundData['courseNumber'] - _this.refundData['status']) * _this.refundData['courseUnitPrice']) - (_this.refundData['courseUnitPrice'] * _this.oldPriceNum));
                    var _newPrice = parseFloat(_this.newCourseData[_this.exchangeCourse]['unit_price'] * _this.newPriceNum);
                    return ( _oldPrice - _newPrice ).toFixed(2);

                } else {
                    return 0;
                }
            }
        }
    });
    var exchangeSuccess = function () {
        resultTip({"msg": "更换课程成功"});
        $('.J_ajax_submit_btn').addClass('disabled')
        setTimeout(function () {
            location.reload();
        }, 1000)
    }
    $('.stackedUl > li').on('click', function () {
        $('.stackedUl > li').removeClass('active');
        $(this).addClass('active');
        exchangeVue.childId = $(this).find('a').attr('_value');
        exchangeVue.changeGroup = groupId;
        exchangeVue.oldPriceNum = '';
        exchangeVue.newPriceNum = '';
        exchangeVue.exchangeCourse = '';
        $.ajax({
            type: "POST",
            url: "<?php echo $this->createUrl('showRefund');?>",
            data: {courseId: exchangeVue.courseId, childId: exchangeVue.childId},
            dataType: 'json',
            success: function (data) {
                if (data.status != 99) {
                    exchangeVue.refundData = data;
                    $('.exchangeBox').show();
                    $('#hasRefundChild').hide();
                } else {
                    $('#hasRefundChild').show();
                    $('.exchangeBox').hide();
                }
            }
        })
    })
</script>

<?php endif; ?>
<?php endif; ?>
<script>
    $('.changeCourse').on('change', function () {
        var _courseId = $(this).val();
        location.href = '<?php echo $this->createUrl('index', array('groupId' => $groupId,'year'=>$_GET['year']));?>&courseId=' + _courseId;
    });
    $("#selectstartyear").change(function (){
        var id = $(this).val();
        $(".year"+id).show().siblings().hide();
        $("#courseObj").html('')
        $("#childObj").html('')
    })
</script>
<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>