<?php

/**
 * This is the model class for table "ivy_vendor".
 *
 * The followings are the available columns in table 'ivy_vendor':
 * @property integer $vendor_id
 * @property string $cn_title
 * @property string $en_title
 * @property string $contact_method
 * @property integer $is_company
 * @property integer $vendor_type_id
 * @property integer $is_corporatechain
 * @property string $vendor_city
 * @property string $digfield_arr
 * @property string $website
 * @property string $linkman
 * @property string $fax
 * @property string $address
 * @property string $email
 * @property integer $dept_id
 * @property string $lead_time
 * @property integer $is_signed
 * @property string $intro
 * @property string $memo
 * @property integer $status
 * @property integer $update_timestamp
 * @property integer $update_user
 */
class IvyVendor extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @return IvyVendor the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_vendor';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('email', 'required'),
			array('is_company, vendor_type_id, is_corporatechain, dept_id, is_signed, status, update_timestamp, update_user', 'numerical', 'integerOnly'=>true),
			array('cn_title, en_title', 'length', 'max'=>150),
			array('contact_method, website, linkman, address, email, lead_time', 'length', 'max'=>225),
			array('vendor_city, digfield_arr', 'length', 'max'=>255),
			array('fax', 'length', 'max'=>15),
			array('intro, memo', 'length', 'max'=>1024),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('vendor_id, cn_title, en_title, contact_method, is_company, vendor_type_id, is_corporatechain, vendor_city, digfield_arr, website, linkman, fax, address, email, dept_id, lead_time, is_signed, intro, memo, status, update_timestamp, update_user', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'vendor_id' => 'Vendor',
			'cn_title' => 'Cn Title',
			'en_title' => 'En Title',
			'contact_method' => 'Contact Method',
			'is_company' => 'Is Company',
			'vendor_type_id' => 'Vendor Type',
			'is_corporatechain' => 'Is Corporatechain',
			'vendor_city' => 'Vendor City',
			'digfield_arr' => 'Digfield Arr',
			'website' => 'Website',
			'linkman' => 'Linkman',
			'fax' => 'Fax',
			'address' => 'Address',
			'email' => 'Email',
			'dept_id' => 'Dept',
			'lead_time' => 'Lead Time',
			'is_signed' => 'Is Signed',
			'intro' => 'Intro',
			'memo' => 'Memo',
			'status' => 'Status',
			'update_timestamp' => 'Update Timestamp',
			'update_user' => 'Update User',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('vendor_id',$this->vendor_id);
		$criteria->compare('cn_title',$this->cn_title,true);
		$criteria->compare('en_title',$this->en_title,true);
		$criteria->compare('contact_method',$this->contact_method,true);
		$criteria->compare('is_company',$this->is_company);
		$criteria->compare('vendor_type_id',$this->vendor_type_id);
		$criteria->compare('is_corporatechain',$this->is_corporatechain);
		$criteria->compare('vendor_city',$this->vendor_city,true);
		$criteria->compare('digfield_arr',$this->digfield_arr,true);
		$criteria->compare('website',$this->website,true);
		$criteria->compare('linkman',$this->linkman,true);
		$criteria->compare('fax',$this->fax,true);
		$criteria->compare('address',$this->address,true);
		$criteria->compare('email',$this->email,true);
		$criteria->compare('dept_id',$this->dept_id);
		$criteria->compare('lead_time',$this->lead_time,true);
		$criteria->compare('is_signed',$this->is_signed);
		$criteria->compare('intro',$this->intro,true);
		$criteria->compare('memo',$this->memo,true);
		$criteria->compare('status',$this->status);
		$criteria->compare('update_timestamp',$this->update_timestamp);
		$criteria->compare('update_user',$this->update_user);

		return new CActiveDataProvider(get_class($this), array(
			'criteria'=>$criteria,
		));
	}
	
	
	/**
	 * 取供应商的名称
	 * @return ArrayIterator;
	 * <AUTHOR>
	 * @copyright IvyGroup
	 */
	public function getVendorTitleInfo($status=0)
	{
		$titleList = array();
		$criteria = new CDbCriteria;
		$criteria->select = 'vendor_id,cn_title,en_title';
		if ($status == 0 || $status == 1)
		{
			$criteria->compare('status', $status);
		}
		$titleInfo = $this->findAll($criteria);
		if (!empty($titleInfo))
		{
			foreach ($titleInfo as $v)
			{
				$titleList[$v->vendor_id] = (Yii::app()->language == "zh_cn") ?  $v->cn_title : $v->en_title;
			}
		}
		return $titleList;
	}
	
	public function getName()
	{
		return (Yii::app()->language == "zh_cn") ?  $this->cn_title : $this->en_title;
	}
}