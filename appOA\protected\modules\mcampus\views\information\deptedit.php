<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span>
    </button>
    <h4 class="modal-title" id="exampleModalLabel"><?php echo $model->id ? '更新：' : '添加：'; ?></h4>
</div>
<?php $form=$this->beginWidget('CActiveForm', array(
    'id'=>'dept-edit-inform',
    'enableAjaxValidation'=>false,
    'htmlOptions'=>array('class'=>'J_ajaxForm form-horizontal', 'role'=>'form'),
)); ?>
    <div class="pop_cont">
        <?php if($grade){ ?>
            <div class="form-group">
                <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'pid'); ?></label>
                <div class="col-xs-9">
                    <?php
                    echo $form->dropDownList($model, 'pid', $grade, array('empty'=>Yii::t('global','Please Select'), 'class'=>'form-control'));
                    ?>
                </div>
            </div>
        <?php } ?>
        <div class="form-group">
            <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'title_cn'); ?></label>
            <div class="col-xs-9">
                <?php echo $form->textField($model, 'title_cn', array('class'=>'form-control'));?>
            </div>
        </div>
        <div class="form-group">
            <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'title_en'); ?></label>
            <div class="col-xs-9">
                <?php echo $form->textField($model, 'title_en', array('class'=>'form-control'));?>
            </div>
        </div>
    </div>
<div class="modal-footer">
    <button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global', 'Submit'); ?></button>
    <button type="button" class="btn btn-default"
            data-dismiss="modal"><?php echo Yii::t('global', 'Cancel'); ?></button>
</div>
<?php $this->endWidget(); ?>