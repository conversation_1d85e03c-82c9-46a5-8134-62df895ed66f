<!-- 加载底部导航栏 -->
<?php
//$this->branchSelectParams['extraUrlArray'] = array('//mgrades/attendance/index');
//$this->renderPartial('//layouts/common/branchSelectBottom');
$types = array(
    0 => Yii::t('attends', 'Action'),
    9 => Yii::t('attends', 'Reset'),
    10 => Yii::t('attends', 'Present'),
    11 => Yii::t('attends', 'Online Present'),
    30 => Yii::t('attends', 'Personal Leave'),
    31 => Yii::t('attends', 'Sick Leave'),
    40 => Yii::t('attends', 'Absent'),
    41 => Yii::t('attends', 'Internal Suspension'),
    42 => Yii::t('attends', 'External Suspension'),
    205 => sprintf(Yii::t('attends', 'Tardy %s Minutes'), 5),
    2010 => sprintf(Yii::t('attends', 'Tardy %s Minutes'), 10),
    2015 => sprintf(Yii::t('attends', 'Tardy %s Minutes'), 15),
    2020 => sprintf(Yii::t('attends', 'Tardy %s Minutes'), 20),
);

?>

<style>
    .weekNum{
        width:72px;
        height:50px;
        background: #FAFAFA;
        border-radius: 4px;
        border: 1px solid #EBEEF5;
        color:#666666;
    }
    .weekNum:hover{
        color:#fff !important;
        background: #4D88D2;
        border-radius: 4px;
        border: 1px solid #4D88D2;
        cursor:pointer 
    }
    .borderBto{
        border-bottom: 1px solid #EBEEF5;
    }
    .lineHeight{
        line-height:25px
    }
    .weekActive{
        color:#fff !important;
        background: #4D88D2;
        border-radius: 4px;
        border: 1px solid #4D88D2;
    }
    .weekActive .weekColor{
        color:#fff !important;
    }
    .border{
        width: 0;
        height: 0;
        border-top: 50px solid #5CB85C;
        border-right: 50px solid transparent;
        position: absolute;
        left: 0px;
        top: 0px;
    }
    .holiday{
        border-top: 50px solid #5CB85C;
    }
    .replace{
        border-top: 50px solid #F0AD4E;
    }
    .holidayText{
        position: absolute;
        left: 10px; 
        top: -48px;
        color: #fff;
        font-size: 18px;
        font-weight: 200;
    }
    .table{
        color:#606266
    }
    .table tr th {
        vertical-align:middle !important;
        text-align:center;
        color:#606266
    }
    .replaceCourse{
        color:#F0AD4E;
        position: absolute;
        top:20px;
        right: 20px;
        width: 42px;
    }
    .yellow{
        background:#FDEBC0 !important;
    }
    .blue{
        background:#EFF5FF;
    }
    .gray{
        background:#FAFAFA;
    }
    .font16{
        font-size:16px
    }
    .course{
        padding:8px; 
        border-radius:4px;
        color:#666666
    }
    .prev{
        position: absolute;
        left: 30px;
        top: 0;
        font-size: 18px;
        height: 52px;
        background: #fff;
        line-height: 50px;
        width:30px;
        z-index: 99;
        top:-1px;
    }
    .prevLeft{
        left:0;
    }
    .next{
        position: absolute;
        right: 30px;
        top: 0;
        font-size: 18px;
        height: 52px;
        background: #fff;
        line-height: 50px;
        width:30px;
        z-index: 99;
        top:-1px;
    }
    .nextRight{
        right:0;
    }
    .dis{
        color:#C0C4CC
    }
    .loading{
        width:98%;
        height:90%;
        background:#fff;
        position: absolute; 
        opacity: 0.5;
        z-index: 99
    }
    .loading span{
        width:100%;
        height:60%;
        display:block;
        background: url("<?php echo Yii::app()->theme->baseUrl?>/images/loading.gif")no-repeat center center ;
    }
    [v-cloak] {
        display: none;
    }
    .animation{
        animation:margin-left: 92px; 0.1s infinite;
        -webkit-animation:margin-left: 92px; 0.5s infinite;
    }
    @keyframes mymove
    {
        100% {margin-left: 92px;}
    }

    @-webkit-keyframes mymove /*Safari and Chrome*/
    {
        100% {margin-left: 92px;}
    }
    .actOpacity{
        opacity: .5;
        cursor: not-allowed !important; 
    }
    .bgYellow{
        background:#FFF7E3;
    }
    .borderBto{
        border-bottom:1px solid #EBEEF5
    }
    .pink{
        background:#FFECEE
    }
    .minWidth{
        min-width:60px
    }
    .red-point{
        position: relative;
    }

    .red-point::before{
        content: "";
        border: 3px solid red;/*设置红色*/
        border-radius:3px;/*设置圆角*/
        position: absolute;
        z-index: 1000;
        right: 0%;
        margin-top: -10px;
    }
</style>
<div class="container-fluid">
    <!-- 面包屑导航 -->
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('//mcampus/default/index')) ?></li>
        <li class="active"><?php echo Yii::t('site', 'G6-12 Schedule Management'); ?></li>
    </ol>
    <div class="row">
        <div class="col-md-2">
            <?php
                $this->widget('zii.widgets.CMenu',array(
                    'items'=> $this->siderMenu,
                    'htmlOptions'=>array('class'=>'nav nav-pills nav-stacked text-right background-gray mb10'),
                    'activeCssClass'=>'active',
                    'itemCssClass'=>''
                ));
            ?>
        </div>
        <div class="col-md-10"  id='container'  v-cloak >
            <div class="row">
                <p class='color3 pb20 col-md-12 borderBto' ><strong style='font-size:18px'><?php echo Yii::t('attends','Student Attendance Overview') ?></strong> <span class='ml20 font14'>{{startYear}}</span> </p>
                <div  class="col-md-2 mt20">
                    <select class="form-control input-sm mb5"  v-model="classId">
                        <option value="-1" selected><?php echo Yii::t('global','Please Select') ?></option>
                        <option v-for="(list,key,index) in classArr" :value="list.id">{{list.name}}</option>
                    </select>
                </div>
                <div class='clearfix'></div>
                <div class="col-md-2 mt20">
                    <div class="list-group">
                    <a class="list-group-item" href="javascript:void(0);"  v-for='(list,idx) in childArr[classId]'
                       :class='{"active":list.childId==studentId}'
                       @click='getData(list.childId)'>
                        {{list.childName}}
                    </a>
                    </div>
                </div>
                <div class="col-md-10 mt20 relative" v-if='studentId!=""'>
                    <div class='loading'  v-if='showLoading'>
                        <span></span>
                    </div>
                    <div  v-if='schedule.length!=0 && !showLoading'>
                        <div class='clearfix'></div>
                        <div class='relative' v-if='week.item'>
                            <div class='glyphicon glyphicon-step-backward prev prevLeft color6' v-if='offsetLeft<0'  @click='headWeek()'></div>
                            <div class='glyphicon glyphicon-step-backward prev prevLeft dis color6' v-else></div>
                            <div class='glyphicon glyphicon-chevron-left prev color6' v-if='offsetLeft<0'  @click='nextWeek()'></div>
                            <div class='glyphicon glyphicon-chevron-left prev dis color6' v-else></div>
                            <div style='width:100% ;padding:0 60px;overflow:hidden;' ref='week'>
                                <div :style="'margin-left:'+ animateLeft +'px;width:'+weekWidth"  class='animation'>
                                    <div class='pull-left mr20 weekNum text-center font12'  v-for='(list,index) in week.item' :class='list.number==currentWeek.number?"weekActive":list.category==null?"actOpacity":""' @click='eventWeek(list)'  aria-hidden="true" onMouseOver="$(this).tooltip('show')"  data-toggle="tooltip" data-html='true' :title='`<div>week ${list.number}</div>${formatTime(list.days[0])} — ${formatTime(list.days[4])}`'  data-placement="top">
                                        <div class='borderBto lineHeight'>week {{list.number}}</div>   
                                        <div class='weekColor lineHeight'><strong>{{list.category==null?'-':list.category}}</strong> </div>
                                    </div>
                                </div>
                                <div class='clearfix'></div>
                            </div>
                            <div class='glyphicon glyphicon-chevron-right next color6' v-if='offsetLeft>=Difference' @click='prevWeek()'></div>
                            <div class='glyphicon glyphicon-chevron-right next color6 dis' v-else></div>
                            <div class='glyphicon glyphicon-step-forward next nextRight color6' v-if='offsetLeft>=Difference' @click='footWeek()'></div>
                            <div class='glyphicon glyphicon-step-forward next nextRight color6 dis' v-else></div>
                            
                        </div>
                        <div class='loading'  v-if='tabLoading'>
                            <span></span>
                        </div>
                        <table class='table table-bordered mt20' style='table-layout: fixed' ref='table' v-if='currentWeek!=null'>
                            <thead >
                                <tr class='text-center'>
                                    <th width='100' >
                                    <?php echo Yii::t('labels','Time') ?>
                                    </th>
                                    <th v-for='(list,id) in currentWeek.days' class='relative font14'>
                                        <div class='border holiday' v-if='holidaysData(list)' onMouseOver="$(this).tooltip('show')"  data-toggle="tooltip" data-html='true' :title='holidays[list]'  data-placement="top">
                                            <div class='holidayText' >H</div>  
                                        </div>
                                        <div class='border replace' v-if='replaceCourse(list)' onMouseOver="$(this).tooltip('show')"  data-toggle="tooltip" data-html='true' :title='`${replaceData[list].replace_tip}`'  data-placement="top">
                                            <div class='holidayText' >E</div>  
                                        </div>
                                        <div>{{currentWeek.weekDayData[id]}} <span v-if='list==nowDay'  class="label label-info">Today</span> </div>
                                        <div>{{formatTime(list)}}</div>
                                        <div class="btn-group mt5" v-if='!holidaysData(list,id)'>
                                            <button type="button" class="btn btn-default btn-xs minWidth"><?php echo Yii::t('global','Please Select') ?></button>
                                            <button type="button" class="btn btn-default  btn-xs dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                <span class="caret"></span>
                                                <span class="sr-only">Toggle Dropdown</span>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li v-for='(type,keys,i) in types'><a href="javascript:;" @click='changeStudentAll(list,keys,id)'>{{type}}</a></li>
                                            </ul>
                                        </div>
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for='(data,index) in schedule[currentWeek.category]'>
                                    <th class='text-center'>
                                        <div class='font14'>{{timetableTimes[index].label}}</div>
                                        <div style='font-weight:200'>{{timetableTimes[index].timeslot}}</div>
                                    </th>
                                    <td v-for='(item,idx) in currentWeek.days' :class='holidaysData(item)?"gray":replaceCourse(item)?"bgYellow":""'>
                                        <template v-for='(_item,i) in data[idx]' >                                        
                                            <div v-if='_item!=null && !replaceCourse(item) && !holidaysData(item)' :class='data[idx].length!=1?"pink mb5":"blue"'  class='course color6 ' >
                                                <p class='color3'><strong>{{ courses[_item[1]]}}</strong></p>
                                                <p class='color3'>{{_item[3]}}</p>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-default btn-xs minWidth">{{findSign(idx,index)}}</button>
                                                    <button type="button" class="btn btn-default  btn-xs dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                        <span class="caret"></span>
                                                        <span class="sr-only">Toggle Dropdown</span>
                                                    </button>
                                                    <ul class="dropdown-menu">
                                                        <li v-for='(type,keys,i) in types'><a href="javascript:;" @click='changeStudent(idx,index,keys,_item[1])'>{{type}}</a></li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </template>
                                        <template v-for='(_item,i) in course(item,index)' >  
                                            <div v-if='replaceCourse(item) && course(item,index)!=null'  :class='course(item,index).length!=1?"pink mb5":"yellow"'  class='course color6 ' >
                                                <p class='color3'><strong>{{courses[_item[1]]}}</strong> </p>
                                                <p class='color3'>{{_item[3]}}</p>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-default btn-xs minWidth">{{findSign(idx,index)}}</button>
                                                    <button type="button" class="btn btn-default  btn-xs dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                        <span class="caret"></span>
                                                        <span class="sr-only">Toggle Dropdown</span>
                                                    </button>
                                                    <ul class="dropdown-menu">
                                                        <li v-for='(type,keys,i) in types'><a href="javascript:;" @click='changeStudent(idx,index,keys,_item[1])'>{{type}}</a></li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </template>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="alert alert-warning" v-else-if='!showLoading' role="alert"><?php echo Yii::t('attends', 'No course has been assigned to you.')?></div>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    var childArr = <?php echo CJSON::encode($childArr); ?>;
    var classArr  = <?php echo CJSON::encode($classArr ); ?>;
    var types = <?php unset($types[0]);echo CJSON::encode($types); ?>;
    var tid = '<?php echo $tid; ?>';
    //var continuousAbsence = '<?php //echo CJSON::encode($continuousAbsence); ?>//';
    //console.log(continuousAbsence)
    var container = new Vue({
        el: "#container",
        data: {
            types:types,
            childArr:childArr,
            classArr:classArr,
            // continuousAbsence:continuousAbsence,
            week: {},
            schedule:[],
            replaceData: {},
            courses: {},
            holidays: {},
            currentWeek:{},
            replaceDay:100,
            holidayIndex:100,
            teacherName:name,
            weekWidth:'',
            offsetLeft:0,
            Difference:0,
            width:276,
            weekLang:['<?php echo Yii::t('labels','Mon') ?>','<?php echo Yii::t('labels','Tue') ?>','<?php echo Yii::t('labels','Wed') ?>','<?php echo Yii::t('labels','Thu') ?>','<?php echo Yii::t('labels','Fri') ?>'],
            teacherList:[],
            showLoading:false,
            timetableTimes:{},
            animateLeft:0,
            studentId:'',
            replaceList:{},
            disabled:false,
            startYear:'',
            classId:'-1',
            nowDay:'',
            tabLoading:false,
            signList:[]
        },
        methods: {
            course(list,index){
                var len=[]
                if(this.replaceData[list]){
                    let replaceList=this.replaceData[list]
                    if(this.schedule[replaceList.category]){
                        for(var i=0;i<this.schedule[replaceList.category].length;i++){
                            len.push(this.schedule[replaceList.category][i][replaceList.day-1])
                        }
                    }
                } 
                return len[index]
            },
            findSign(index,id){
                let i=id+1
                var len=''
                if(this.signList[this.currentWeek.days[index]]){
                    if(this.signList[this.currentWeek.days[index]][i]){
                        len=types[this.signList[this.currentWeek.days[index]][i]]
                    } else{
                        len='<?php echo Yii::t('global','Please Select') ?>'
                    }
                }else{
                    len='<?php echo Yii::t('global','Please Select') ?>'

                }
                return len
            },
            formatTime(date){
                let data=date+''
                let year=data.substring(0,4)
                let mon=data.substring(4,6)
                let day=data.substring(6,8)
                return year+'/'+mon+'/'+day
            },
            getData(id){
                let that=this
                this.showLoading=true
                if(id!=''){
                    this.teacherName=this.teacherList[id]
                }
                this.studentId=id
                $.ajax({
                    url: '<?php echo $this->createUrl("studentInfo") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        studentId:this.studentId,
                        weeknumber:this.currentWeek.number, //可选,
                    },
                    success: function(data) {
                        if(data.state=='success'){  
                            that.week=data.data.week
                            that.schedule=data.data.schedule
                            that.holidays=data.data.holidays
                            that.courses=data.data.courses
                            that.replaceData=data.data.replace
                            that.weekWidth=that.week.item.length*92+'px'
                            that.nowDay=data.data.nowDay
                            that.timetableTimes=data.data.timetableTimes
                            if(!that.currentWeek.days){
                                for(var i=0;i<that.week.item.length;i++){
                                    if(that.week.item[i].number==that.week.current){
                                            that.currentWeek=that.week.item[i]
                                    }
                                }
                            }
                            let start=data.data.week.item[0].monday+''
                            let end=data.data.week.item[data.data.week.item.length-1].monday+''
                            that.startYear=start.substring(0,4)+"-"+end.substring(0,4)+' <?php echo Yii::t('labels','School Year') ?>'
                            if(id!=''){
                                $("#remarks").modal('hide');
                            }
                            that.showLoading=false
                            if(that.schedule.length!=0){
                                that.$nextTick(()=>{ 
                                    that.Difference=that.$refs.week.offsetWidth-that.week.item.length*92
                                    var len=parseInt(that.$refs.week.offsetWidth/92)
                                    if(that.week.current>len/2){
                                        if(-that.week.current*92>that.Difference && that.week.current<=that.week.item.length-len){
                                            that.offsetLeft=-(that.week.current-len/2)*92
                                            that.animateLeft=-(that.week.current-len/2)*92
                                        }else{
                                            that.offsetLeft=that.Difference
                                            that.animateLeft=that.Difference
                                        }
                                    }else{
                                        that.offsetLeft=0
                                        that.animateLeft=0
                                    }
                                });
                                that.getAttendance()
                            }
                        }else{
                            that.showLoading=false 
                        }
                    }
                })
            },
            getAttendance(){
                let that=this
                this.tabLoading=true
                $.ajax({
                    url: '<?php echo $this->createUrl("studentAttendanceOverview") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        studentId:this.studentId,
                        weekMon:this.currentWeek.monday, //可选,
                    },
                    success: function(data) {
                        if(data.state=='success'){  
                            if(data.data.attendance){
                                that.signList=data.data.attendance
                            }else{
                                that.signList=[]
                            }
                        }
                        that.tabLoading=false
                    }
                })
            },
            eventWeek(data){
                if(data.category==null){
                    return
                }
                this.currentWeek=data
                this.holidayIndex=100
                this.replaceDay=100
                this.getAttendance()

            },
            replaceCourse(list){
                if(this.replaceData[list]){
                    // this.replaceDay=index
                    // this.replaceList=this.replaceData[list]
                    // console.log(this.replaceList)
                    return true
                } 
            },
            holidaysData(list){
                if(this.holidays[list]){
                    // this.holidayIndex=index
                    return true
                }
            },
            prevWeek(){
                if(this.offsetLeft-this.Difference<this.width){
                    this.offsetLeft=this.Difference-120
                    $(".animation").animate({marginLeft:this.offsetLeft+'px'});
                }else{
                    this.offsetLeft=this.offsetLeft-this.width
                    $(".animation").animate({marginLeft:this.offsetLeft+'px'});

                }
            },
            nextWeek(){
                if(this.offsetLeft>-this.width){
                    this.offsetLeft=0
                    $(".animation").animate({marginLeft:'0px'});

                }else{
                    this.offsetLeft=this.offsetLeft+this.width
                    $(".animation").animate({marginLeft:this.offsetLeft+'px'});

                }
            },
            headWeek(){
                this.offsetLeft=0
                $(".animation").animate({marginLeft:'0px'});
            },
            footWeek(){
                this.offsetLeft=this.Difference-120
                $(".animation").animate({marginLeft:this.offsetLeft+'px'});
            },
            changeStudent(index,idx,type,course_code){
                let that=this
                $.ajax({
                    type: 'post',
                    url: '<?php echo $this->createUrl('updateRecordsOne')?>',
                    data: { type: type, period:idx+1, datatime: this.currentWeek.days[index], childid: this.studentId, weekday: index+1, course_code: course_code },
                    dataType: 'json',
                    async: false
                }).done(function(ret){
                    data = ret;
                    if (data.state == 'success') {
                        that.getAttendance()
                        resultTip({"msg": "<?php echo Yii::t("message", "Data Saved!"); ?>"})
                    } else {
                        resultTip({"msg": "<?php echo Yii::t("message", "Data Saving Failed!"); ?>"})
                    }
                });
            },
            changeStudentAll(list,type,index){
                let that=this
                $.ajax({
                    type: 'post',
                    url: '<?php echo $this->createUrl('updateRecordsAll')?>',
                    data: { type: type,datatime: list, childid: this.studentId,weekday: index+1, },
                    dataType: 'json',
                    async: false
                }).done(function(ret){
                    data = ret;
                    if (data.state == 'success') {
                        that.getAttendance()
                        resultTip({"msg": "<?php echo Yii::t("message", "Data Saved!"); ?>"})
                    } else {
                        resultTip({"msg": "<?php echo Yii::t("message", "Data Saving Failed!"); ?>"})
                    }
                });
            },
            // test(childId) {
            //     let that = this
            //     continuousAbsence = JSON.parse(that.continuousAbsence)
            //     const child_ids = Object.keys(continuousAbsence)
            //    return child_ids.indexOf(childId.toString()) !== -1
            // }
        }
    })

</script>
