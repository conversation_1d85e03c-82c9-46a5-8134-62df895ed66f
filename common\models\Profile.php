<?php

/**
 * This is the model class for table "ivy_parent".
 *
 * The followings are the available columns in table 'ivy_parent':
 * @property integer $pid
 * @property string $password_text
 * @property string $cn_name
 * @property string $en_firstname
 * @property string $en_lastname
 * @property integer $gender
 * @property integer $language
 * @property string $country
 * @property string $province
 * @property string $company
 * @property string $job
 * @property string $address
 * @property string $post
 * @property string $tel
 * @property string $mphone
 * @property string $fax
 * @property string $childs
 */
class Profile extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @return Profile the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return Yii::app()->getModule('user')->tableProfiles;
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('password_text, en_firstname, en_lastname', 'required'),
			array('pid, gender, language', 'numerical', 'integerOnly'=>true),
			array('password_text, company, childs', 'length', 'max'=>255),
			array('cn_name, en_firstname, en_lastname, country, province, job, address, post, tel, mphone, fax', 'length', 'max'=>150),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('pid, password_text, cn_name, en_firstname, en_lastname, gender, language, country, province, company, job, address, post, tel, mphone, fax, childs', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		$relations = array(
			'user'=>array(self::HAS_ONE, 'User', 'uid'),
		);
		if (isset(Yii::app()->getModule('user')->profileRelations)) $relations = array_merge($relations,Yii::app()->getModule('user')->profileRelations);
		return $relations;
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'pid' => 'Pid',
			'password_text' => 'Password Text',
			'cn_name' => 'Cn Name',
			'en_firstname' => 'En Firstname',
			'en_lastname' => 'En Lastname',
			'gender' => 'Gender',
			'language' => 'Language',
			'country' => 'Country',
			'province' => 'Province',
			'company' => 'Company',
			'job' => 'Job',
			'address' => 'Address',
			'post' => 'Post',
			'tel' => 'Tel',
			'mphone' => 'Mphone',
			'fax' => 'Fax',
			'childs' => 'Childs',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('pid',$this->pid);
		$criteria->compare('password_text',$this->password_text,true);
		$criteria->compare('cn_name',$this->cn_name,true);
		$criteria->compare('en_firstname',$this->en_firstname,true);
		$criteria->compare('en_lastname',$this->en_lastname,true);
		$criteria->compare('gender',$this->gender);
		$criteria->compare('language',$this->language);
		$criteria->compare('country',$this->country,true);
		$criteria->compare('province',$this->province,true);
		$criteria->compare('company',$this->company,true);
		$criteria->compare('job',$this->job,true);
		$criteria->compare('address',$this->address,true);
		$criteria->compare('post',$this->post,true);
		$criteria->compare('tel',$this->tel,true);
		$criteria->compare('mphone',$this->mphone,true);
		$criteria->compare('fax',$this->fax,true);
		$criteria->compare('childs',$this->childs,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}