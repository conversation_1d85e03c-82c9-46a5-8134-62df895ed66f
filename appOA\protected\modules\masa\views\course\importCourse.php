<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span>
    </button>
    <h4 class="modal-title" id="exampleModalLabel">导入课程</h4>
</div>
<?php $form = $this->beginWidget('CActiveForm', array(
    'id' => 'course-form',
    'enableAjaxValidation' => false,
    'action' =>$this->createUrl('import'),
    'htmlOptions' => array('class' => 'J_ajaxForm form-horizontal', 'role' => 'form'),
)); ?>
<div class="modal-body">
    <!-- 课程组-->
    <div class="form-group">
        <label class="col-sm-2 control-label required"><?php echo Yii::t('asa', '选择源项目组') ?> * </label>
        <div class="col-xs-10">
            <?php echo CHtml::dropDownList('oldgroupId', '', $groupArr, array('class' => 'form-control changeGroup', 'empty' => Yii::t('asa','请选择'))); ?>
            <input type="hidden" name="groupId" value="<?php echo $groupId; ?>">
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-2 control-label required"><?php echo Yii::t('asa', '课程时间') ?> * </label>
        <div class="col-xs-10">
            <?php echo CHtml::dropDownList('scheduleTime', '', $scheduleArr, array('class' => 'form-control', 'empty' => Yii::t('asa','请选择'))); ?>
            <p class="help-block" style="color:#428bca">新课程导入之后所用时间段</p>
        </div>
    </div>
    <div class="form-group checkCourse" style="display: none">
        <label class="col-sm-2 text-right required"><?php echo Yii::t('asa', '源项目组课程列表') ?> * </label>
        <div class="col-xs-10 checkCourseList">

        </div>
    </div>
</div>
<div class="modal-footer">
    <span id="start_fail_info" class="pull-left text-warning" style="display: none;"><i
            class="glyphicon glyphicon-remove text-warning"></i><?php echo Yii::t('asa', '当天该时间段已有课程') ?></span>
    <button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global', 'Submit'); ?></button>
    <button type="button" class="btn btn-default"
            data-dismiss="modal"><?php echo Yii::t('global', 'Cancel'); ?></button>
</div>
<?php $this->endWidget(); ?>
<script>
    $('.changeGroup').change(function() {
        var _groupId = $('.changeGroup').val();
        if(!_groupId) {$('.checkCourseList').empty();return}
        $.ajax({
            url: '<?php echo $this->createUrl('courseList'); ?>',
            data: {groupId:_groupId},
            type: 'post',
            dataType: 'json',
            success: function (data) {
                $('.checkCourseList').empty();
                if (data.state == 'success') {
                    var _list = '';
                    if(!Object.keys(data.data).length){$('.checkCourseList').append('<span class=""><?php echo Yii::t('asa', '该课程组下无课程') ?>'); return;}
                    $.each(data.data, function (i, list) { //判断当天开始时间是否重复
                        _list += '<div><label><input type="checkbox" name="course['+ i +']"/> '+ list.title +' （'+list.timeTitle+'）</label></div> '
                    });
                    $('.checkCourseList').append(_list);
                    $('.checkCourse').show();
                } else {
                    alert(data.message)
                }
            },
            error: function (data) {
                alert(data.state)
            }
        });

    });
</script>