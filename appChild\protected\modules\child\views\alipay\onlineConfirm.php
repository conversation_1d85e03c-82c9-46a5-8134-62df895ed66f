<?php
/*
 * <PERSON><PERSON>
 * 2012-11-19
 *
 */
?>


<div id="invoiceinfo" class="confirminfo">
    <?php
    if (!empty($dataProvider)) {

        Mims::LoadHelper('HtoolKits');
        $tk = new HtoolKits();
        $nf = new CNumberFormatter('zh-cn');
        $beforePaidInfoForm = $this->beginWidget('CActiveForm', array(
            'id' => 'custom_alipay_online',
            'enableAjaxValidation' => false,
                ));
        ?>
        <div class="paylist">
            <?php
            $invoice_model = Invoice::model();
            $this->widget('zii.widgets.grid.CGridView', array(
                'cssFile' => Yii::app()->theme->baseUrl . '/widgets/gridview/styles.css',
                'dataProvider' => $dataProvider,
                'enablePagination' => false,
                'enableSorting' => false,
                'columns' => array(
                    array(
                        'name' => 'title',
                        'type' => 'raw',
                        'value' => 'CHtml::link($data->title,Yii::app()->getController()->createUrl("//child/payment/viewInvoice", array("invoice_id"=>$data->invoice_id)),array("class"=>"colorbox"))',
                    ),
                    'amount',
                    array(
                        'name' => Yii::t("payment", 'Amount Paid'),
                        'value' => array($invoice_model, 'renderPaidAmountF'),
                    ),
                    array(
                        'header' => Yii::t("payment", 'Amount Due'),
                        'type' => 'raw',
                        'value' => array($invoice_model, 'renderDueAmountHtml'),
                    ),
                ),
                'template' => '{items}',
            ));
            ?>
        </div>
        <?php echo CHtml::hiddenField('mySign', $mySign); ?>
        <?php echo CHtml::hiddenField('payment_mothod', 'alipay'); ?>
        <?php
        foreach ($requestParameterAttributes as $attribute) {
            echo CHtml::activeHiddenField($requestParameter, $attribute);
        }
        ?>
        <?php $this->endWidget(); ?>

        <div id="confirm-info">
            <div class="fr big-size">
                <?php echo Yii::t("payment", 'Total'); ?>
                <span id="should_pay"><?php echo $nf->format('#,##0.00', $requestParameter->total_fee); ?>
                </span>
            </div>
            <div class="normal-size bank">
                <p>
                    <?php echo addColon(Yii::t("payment", 'Selected Bank')); ?>
                </p>
                <?php echo CHtml::label('', '', array('class' => $selectedBank['id'], 'title' => $tk->getContentByLang($selectedBank['cn_name'], $selectedBank['en_name']))); ?>
            </div>
        </div>
        <div class="clear"></div>
        
        <?php
            $file = '/alipay/quota/'.$selectedBank['id'];
            if($this->getViewFile($file) != false){
                echo CHtml::openTag('div',array('class'=>'bank-quota'));
                    echo CHtml::openTag('h2');
                    echo Yii::t("payment", ":bankTitle 网上支付限额", array(":bankTitle"=>$selectedBank['cn_name']));
                    echo CHtml::closeTag('h2');
                    echo CHtml::openTag('p', array('class'=>'orange'));
                    echo Yii::t("payment", "推荐使用储蓄卡网银进行支付。如果您的银行卡（尤其是信用卡）受限于银行限额不能支付，请使用其他支付方式，感谢您的配合。");
                    echo CHtml::closeTag('p');
                    $this->renderPartial($file);
                echo CHtml::closeTag('div');
            }
        ?>
        
        <div id="operatecol">

            <?php echo CHtml::link('<span>' . Yii::t("global", 'Back') . '</span>', $this->createUrl('//child/payment/online'), array("class" => "back")); ?>
            <!--<div class="fr">-->
                <?php
                if (Yii::app()->user->checkAccess('viewPayment', array("childid" => $this->getChildId()))):
                    $ajaxOptions = array(
                        'type' => 'POST',
                        'dataType' => 'JSON',
                        'data' => 'js:jQuery("#custom_alipay_online").serialize()',
                        'beforeSend' => 'showRequest',
                        'success' => 'showResponse',
                    );
                    echo CHtml::openTag('span', array('class' => 'btn001'));
                    echo CHtml::ajaxLink(CHtml::tag('span') . Yii::t("payment", 'Make Payment') . CHtml::closeTag('span'), $this->createUrl('//child/payment/onlineBeforePaid'), $ajaxOptions, array('class' => 'operatecol', 'id' => 'pay_money'));
                    echo CHtml::closeTag('span');
                else:
                    echo CHtml::button(Yii::t("payment", 'Make Payment'), array("class" => "bigger w100", "disabled" => "disabled"));
                endif;
                echo $alipaySubmitHtml;
                ?>
            <!--</div>-->

        </div>
        <div align=center>
            <span id="show_paymoney_info"></span>
        </div>
    <?php } ?>
</div>
