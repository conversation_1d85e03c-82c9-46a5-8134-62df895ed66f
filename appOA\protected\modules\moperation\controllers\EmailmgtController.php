<?php

class EmailmgtController extends ProtectedController
{
    public function init(){
        parent::init();
        Yii::import('common.models.content.MailImportInfo');
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'hqOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site','HQ Operations');
    }

    public function actionIndex()
    {
		$model=new MailImportInfo('search');
		$model->unsetAttributes();  // clear any default values
		if(isset($_GET['MailImportInfo']))
			$model->attributes=$_GET['MailImportInfo'];

		$this->render('index',array(
			'model'=>$model,
		));
    }

    public function getButton($data, $row){
        $status = $data->status;
        if( $status == 1 ) {
            echo '<a class="btn btn-danger btn-xs" href="javascript:;" onclick="updateState(\''.$data->email.'\', 0)"><span class="glyphicon glyphicon-pencil"></span></a>';
        }elseif( $status == 0 ) {
            echo '<a class="btn btn-info btn-xs" href="javascript:;" onclick="updateState(\''.$data->email.'\', 1)"><span class="glyphicon glyphicon-pencil"></span></a>';
        }
    }
	
	public function actionUpdateState()
	{
		$email = Yii::app()->request->getParam('email', '');
		$type = Yii::app()->request->getParam('type', '');
		
		$model = MailImportInfo::model()->findByPK($email);
		$model->status = $type;
		if($model->save()){
			$this->addMessage('state', 'success');
			$this->addMessage('message', Yii::t('', '保存成功！'));
		}
		else{
			$this->addMessage('state', 'fail');
			$this->addMessage('message', Yii::t('', '保存失败！'));
		}
		$this->showMessage();
	}
}