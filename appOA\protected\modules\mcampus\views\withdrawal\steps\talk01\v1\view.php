<div>
    <div class="panel panel-default">
        <div class="panel-heading"><?php echo Yii::t('withdrawal','Management Interview(s)');?></div>
        <div class="panel-body">
            <div class='ml8'>
                <div class=' form-horizontal font14'>
                    <div class='flex flexStart mb20'>
                        <div class='color6 flexWidth'><?php echo Yii::t('withdrawal','Interviewer');?></div>
                        <div class='flex1 color3'>
                        <span class='talkUser'></span>
                        </div>
                    </div>
                    <div class='flex  flexStart'>
                        <div class='color6 flexWidth'><?php echo Yii::t('withdrawal','Interview result');?></div>
                        <div class='flex1'>
                            <div class='mb12 '>
                                <div class="radio mb10 talkResultFlex">
                                    <span class='notAllowed'>
                                        <label class='lableHeight  eventsNone'>
                                            <input type="radio" name="talk_result"  class='talkResult ' value="1">
                                            <span><?php echo Yii::t('withdrawal','Schedule school board interview');?></span> 
                                        </label>
                                    </span>
                                    <span id='talkList' class='hide'></span>
                                </div>
                                <div class="radio mb10">
                                    <span class='notAllowed'>
                                        <label class='lableHeight eventsNone'>
                                            <input type="radio" name="talk_result"  class='talkResult' value="2">
                                            <?php echo Yii::t('withdrawal','End withdrawal, continue study');?>
                                        </label>
                                    </span>
                                </div>
                                <div class="radio mb10">
                                    <span class='notAllowed'>
                                        <label class='lableHeight eventsNone'>
                                            <input type="radio" name="talk_result"  class='talkResult' value="3">
                                            <?php echo Yii::t('withdrawal','Continue withdrawal');?>
                                        </label>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    let list=childDetails.forms.find(item2 =>item2.key === 'talk01')
    let data=list.data
    if(data.talk_user){
        $('.talkUser').text(data.staff_info[data.talk_user].name)
        $('input[type="radio"][name="talk_result"][value='+data.talk_result+']').prop('checked', true);
        if(data.talk_result=='1'){
            var addOtherList={}
            if(data.talk_other){
                addOtherList=data.staff_info[data.talk_other]
            }else{
                addOtherList=childDetails.info.appInfo.staffInfo[childDetails.info.appInfo.talkDefault]
            }
            // let addOtherList=childDetails.info.appInfo.staffInfo[childDetails.info.appInfo.talkDefault]
            var talkDom='<span class="flex align-items addTalk">'+
                '<img alt="" class="avatar20" src='+addOtherList.photoUrl+'> '+
                '<div class="color3 font12 ml4">'+addOtherList.name+'</div>'+
            '</span>'
            $('#talkList').html(talkDom)
            $('#talkList').removeClass('hide')
        }
    }
</script>
<style>
    .avatar20{
        width: 20px;
        height: 20px;
        border-radius: 50%;
        object-fit: cover;
    }
    .addTalk{
        padding:4px;
        display:inline-flex;
        background: #F2F3F5;
        border-radius: 2px;
        margin-left:16px;
    }
    .talkResultFlex{
        display: flex;
        align-items: center;
        height:24px;
        padding-top: 0 !important;
    }
    .eventsNone{
        opacity: 0.7;
        pointer-events: none;
    }
    .notAllowed{
        cursor: not-allowed;
    }
</style>