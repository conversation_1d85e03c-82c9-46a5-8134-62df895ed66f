<?php

class RegistrationController extends BranchBasedController{

    public $yid;
    public $regModel;
    public $accessToken;
    public $printFW;

    public function createUrlReg($route, $params = array(), $ampersand = '&')
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        if (empty($params['yid'])) {
            $params['yid'] = $this->yid;
        }
        return parent::createUrl($route, $params, $ampersand);
    }

    public function init(){
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'campusOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site','Campus Workspace');

        Yii::import('common.models.regextrainfo.*');
        Yii::import('common.models.calendar.*');
        Yii::import('common.models.child.HomeAddress');
        //初始化选择校园页面
        $this->branchSelectParams['hideOffice'] = true;
        $this->branchSelectParams['urlArray'] = array('//mcampus/registration/markstudent');
        $cs = Yii::app()->clientScript;
        $cs->registerCoreScript('jquery.ui');
        $cs->registerCssFile($cs->getCoreScriptUrl().'/jui/css/base/jquery-ui.css');
        $cs->registerScriptFile($cs->getCoreScriptUrl().'/jui/js/jquery-ui.min.js');
        $cs->registerCssFile(Yii::app()->themeManager->baseUrl.'/modern/css/wizard/bootstrap-nav-wizard.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/vue2.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/excellentexport.js');

        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/jquery.jPrintArea.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/xlsx.full.min.js');
    }


    public function beforeAction($action) {
        parent::beforeAction($action);
        // 初始化通用数据
        $this->yid = Yii::app()->request->getParam('yid');

        if (!$this->yid) {
            $this->getCalendars();
            // 默认下学年的yid
            if (isset($this->calendarYids['nextYid'])) {
                $this->yid = $this->calendarYids['nextYid'];
            } else {
                $this->yid = $this->calendarYids['currentYid'];
            }
        }
        if (!$this->branchId) {
            return true;
        }
        $this->regModel = Reg::model()->findByAttributes(array('yid'=>$this->yid, 'schoolid'=>$this->branchId));
        if ($this->regModel) {
            return true;
        } else {
            if($this->generalReg($this->yid)){
                return true;
            }
        }
    }

     // 标记新生
    public function actionMarkstudent()
    {
        $yid = Yii::app()->request->getParam('yid', $this->regModel->yid);

        $criteria = new CDbCriteria();
        $criteria->compare('branchid', $this->branchId);
        $criteria->order = 'startyear desc';
        $calendarModel = CalendarSchool::model()->findAll($criteria);
        // 根据当前校园查询出所有得学年
        $startYearList = array();
        $yearList = array();

        foreach ($calendarModel as $val){
            $yearEnd = $val->startyear  + 1;
            $startYearList[$val->yid] = $val->startyear . ' - ' . $yearEnd;
            $yearList[$val->yid] = $val->startyear;
        }

        $data = array();

        $criteria = new CDbCriteria();
        $criteria->compare('schoolid', $this->branchId);
        $criteria->compare('statryear', $yearList[$yid]);
        $schoolStartyearModel = SchoolStartyear::model()->findAll($criteria);

        $schoolStartyearChildid = array();
        $schoolStartyearClass = array();
        if($schoolStartyearModel) {
            foreach ($schoolStartyearModel as $val) {
                $schoolStartyearChildid[$val->childid] = $val->childid;
                $schoolStartyearClass[$val->childid] = $val->classid;
            }
        }

        // 根据regId 查询开放孩子得状态
        $criteria = new CDbCriteria();
        $criteria->compare('reg_id', $this->regModel->id);
        $criteria->compare('schoolid', $this->branchId);
        $regStudentModel = RegStudents::model()->findAll($criteria);

        $regStudentList = array();
        $studentsChildid = array();
        $studentsClassid = array();
        if($regStudentModel) {
            foreach ($regStudentModel as $val) {
                $studentsChildid[$val->childid] = $val->childid;
                $studentsClassid[$val->childid] = $val->classid;
                $regStudentList[$val->childid] = array(
                    'status' => $val->status,
                    'is_selected' => $val->is_selected
                );
            }
        }

        $childidList = $schoolStartyearChildid + $studentsChildid;
        $classidList = $schoolStartyearClass + $studentsClassid;

        $classList = array();
        if($childidList) {
            Yii::import('common.models.invoice.*');
            $childModel = ChildProfileBasic::model()->findAllByPk($childidList);
            /*// 过滤掉下半年未分班学生
            $criteria = new CDbCriteria();
            $criteria->compare('calendar', $this->regModel->yid);
            $criteria->compare('schoolid', $this->branchId);
            $criteria->compare('stat', 20);
            $criteria->index = 'childid';
            $reserves = ChildReserve::model()->findAll($criteria);
            foreach ($childModel as $key=>$item) {
               if (!isset($reserves[$item->childid])) {
                    unset($regStudentList[$item->childid]);
                    unset($childModel[$key]);
               }
            }*/

            if ($childModel) {
                foreach ($childModel as $item) {
                    $criteria = new CDbCriteria();
                    $criteria->compare('childid', $item->childid);
                    $criteria->compare('payment_type', 'tuition');
                    $criteria->compare('schoolid', $this->branchId);
                    $criteria->compare('calendar_id', $yid);
                    $criteria->compare('`inout`', 'in');
                    $criteria->compare('status', array(Invoice::STATS_UNPAID, Invoice::STATS_PAID, Invoice::STATS_PARTIALLY_PAID));
                    $invoicModel = Invoice::model()->find($criteria);

                    if ($invoicModel && $invoicModel->status == Invoice::STATS_PAID) {
                        $data[$classidList[$item->childid]]['paid'][] = array(
                            'childid' => $item->childid,
                            'childName' => $item->getChildName(),
                            'className' => $item->ivyclass->title,
                            'status' => $item->status,
                            'schoolid' => $item->schoolid,
                            'childUrl' => $this->createUrl('//child/index/index', array('childid' => $item->childid)),
                        );
                    } else {
                        $data[$classidList[$item->childid]]['unPaid'][] = array(
                            'childid' => $item->childid,
                            'childName' => $item->getChildName(),
                            'className' => $item->ivyclass->title,
                            'status' => $item->status,
                            'schoolid' => $item->schoolid,
                            'invoiceStatus' => ($invoicModel->status == Invoice::STATS_PARTIALLY_PAID) ? '部分付清' : '未付款',
                            'childUrl' => $this->createUrl('//child/index/index', array('childid' => $item->childid)),
                        );
                    }
                }
            }
        }

        if($classidList) {
            $crit = new CDbCriteria();
            $crit->compare('classid', $classidList);
            $crit->order = 'child_age ASC, title ASC';
            $classModel = IvyClass::model()->findAll($crit);
            if($classModel) {
                foreach ($classModel as $item) {
                    $paid = ($data[$item->classid]['paid']) ? count($data[$item->classid]['paid']) : 0;
                    $unPaid = ($data[$item->classid]['unPaid']) ? count($data[$item->classid]['unPaid']) : 0;
                    $classList[] = array(
                        'id' => $item->classid,
                        'title' => $item->title,
                        'count' => $paid+$unPaid,
                    );
                }
            }
        }
        $completeConfig = RegExtraInfo::completeConfig();
        $studentConfig = RegStudents::getStudentConfig();
        $this->render('markstudent', array(
            'startYearList' => $startYearList,          // 校历数组
            'startdate' => date("Y-m-d", $this->regModel->startdate), // 开放时间开始
            'enddate' => date("Y-m-d", $this->regModel->enddate), // 开发时间结束
            'yid' => $yid,                              // 当前校历ID
            'classList' => $classList,                  // 班级
            'data' => $data,                            // 数据
            'studentConfig' => $studentConfig,
            'completeConfig' => $completeConfig,
            'regStudentList' => $regStudentList,        // 以孩子ID 为键名得数组 键值为状态
                                                        // 0：默认；1：进行中；2：完成；3：审核完成；4：驳回
        ));
    }

    // 单独增加某个学生得数据
    public function actionUpdateChild()
    {
        $childid = Yii::app()->request->getParam('childid', '');
        $start_time = Yii::app()->request->getParam('start_time', '');
        $end_time = Yii::app()->request->getParam('end_time', '');

        Yii::import('common.models.invoice.*');

        if(!$childid){
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '孩子不能为空');
            $this->showMessage();
        }
        $criteria = new CDbCriteria();
        $criteria->compare('childid', $childid);
        $criteria->compare('calendar', $this->regModel->yid);
        $criteria->compare('schoolid', $this->branchId);
        $criteria->compare('stat', 20);
        $model= ChildReserve::model()->find($criteria);

        if(!$model) {
            $model = ChildProfileBasic::model()->findByPk($childid);
        }

        if($model){ //暂时不检查下学年是否分班 By Steven 2019.8.29
            if(!$model->classid){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('message', '孩子班级不能为空'));
                $this->showMessage();
            }

            $criteria = new CDbCriteria();
            $criteria->compare('childid', $childid);
            $criteria->compare('reg_id', $this->regModel->id);
            $regStudents = RegStudents::model()->find($criteria);

            if(!$regStudents) {
                $regStudents = new RegStudents();
                $regStudents->reg_id = $this->regModel->id;
                $regStudents->classid = $model->classid;
                $regStudents->childid = $model->childid;
                $regStudents->status = 0;
                $regStudents->updated_by = Yii::app()->user->id;
                $regStudents->updated_at = time();
                $regStudents->schoolid = $this->branchId;
                $regStudents->is_selected = 1;
                $regStudents->start_time = strtotime($start_time);
                $regStudents->end_time = strtotime($end_time);
                if (!$regStudents->save()) {
                    $this->addMessage('state', 'fail');
                    $err = current($regStudents->getErrors());
                    $this->addMessage('message', $err ? $err[0] : '失败');
                    $this->showMessage();
                }
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message', 'success'));
                $this->addMessage('callback', 'cbUpdateChild');
                $this->showMessage();
            }
            $this->addMessage('state', 'fail');
            $this->addMessage('message', Yii::t('message', '孩子已经添加过'));
            $this->showMessage();
        }
        $this->addMessage('state', 'fail');
        $this->addMessage('message', '下半年未分班');
        $this->showMessage();
    }

    // 保存标记的学生
    public function actionSavemark()
    {
        $students = Yii::app()->request->getParam('students', array());
        $startTime = Yii::app()->request->getParam('startTime', '');
        $startEnd = Yii::app()->request->getParam('startEnd', '');
        if(!$startTime || !$startEnd){
            $this->addMessage('state', 'fail');
            $this->addMessage('message', Yii::t('message', '开始和结束时间必须填写'));
            $this->showMessage();
        }

        $this->regModel->startdate = strtotime($startTime);
        $this->regModel->enddate = strtotime($startEnd);
        $this->regModel->save();
        $childData = array();
        $end_time = strtotime($startEnd) + 86399;

        if($students) {
            foreach ($students as $class => $childArr) {
                foreach ($childArr as $child) {
                    $criteria = new CDbCriteria();
                    $criteria->compare('childid', $child);
                    $criteria->compare('reg_id', $this->regModel->id);
                    //$criteria->compare('classid', $class);
                    //$criteria->compare('schoolid', $this->branchId);
                    $model = RegStudents::model()->find($criteria);
                    if (!$model) {
                        $model = new RegStudents();
                        $model->reg_id = $this->regModel->id;
                        $model->classid = $class;
                        $model->childid = $child;
                        $model->status = 0;
                        $model->is_selected = 1;
                        $model->updated_by = Yii::app()->user->id;
                        $model->updated_at = time();
                        $model->schoolid = $this->branchId;
                    }else {
                        if ($model->is_selected == 0) {
                            $model->is_selected = 1;
                            $model->updated_by = Yii::app()->user->id;
                            $model->updated_at = time();
                            $model->schoolid = $this->branchId;
                        }
                    }
                    $model->start_time = strtotime($startTime);
                    $model->end_time = $end_time;
                    $model->save();
                    $childData[] = $model->childid;
                }
            }
        }

        $criteria = new CDbCriteria();
        $criteria->compare('reg_id', $this->regModel->id);
        $criteria->compare('schoolid', $this->branchId);
        if($childData) {
            $criteria->addNotInCondition('childid', $childData);
        }
        $regStudentsModel = RegStudents::model()->findAll($criteria);

        if($regStudentsModel){
            foreach ($regStudentsModel as $val) {
                if($val->is_selected == 1 ){
                    $val->is_selected = 0;
                    $val->save();
                }
            }
        }

        $this->addMessage('state', 'success');
        $this->addMessage('message', Yii::t('message', '成功'));
        $this->showMessage();
    }

    // 获取已经开放学生列表 准备发微信通知
    public function actionTaskData()
    {
        // 过滤掉下半年未分班学生
        Yii::import('common.models.invoice.ChildReserve');
        /*$criteria = new CDbCriteria();
        $criteria->compare('calendar', $this->regModel->yid);
        $criteria->compare('schoolid', $this->branchId);
        $criteria->compare('stat', 20);
        $criteria->index = 'childid';
        $reserves = ChildReserve::model()->findAll($criteria);*/

        $criteria = new CDbCriteria();
        $criteria->compare('reg_id', $this->regModel->id);
        $criteria->compare('schoolid', $this->branchId);
        //$criteria->compare('childid', array_keys($reserves));
        $criteria->compare('status', array(RegStudents::STATUS_DEFAULT, RegStudents::STATUS_CONDUCT, RegStudents::STATUS_OVERRULE));
        $criteria->compare('is_selected', 1);
        $criteria->index = 'childid';
        $regStudentModel = RegStudents::model()->findAll($criteria);
        $chlidArr = array();
        $regStudents = array();
        foreach ($regStudentModel as $val){
            $chlidArr[] = $val->childid;
            $regStudents[] = $val->id;
        }
        $model = new RegStudents();
        $childData['children'] = $model->getNameList($chlidArr,$this->branchObj->type);

        $send_time = time() - 86400;
        $criteria = new CDbCriteria();
        $criteria->compare('reg_student_id', $regStudents);
        $criteria->compare('childid', $chlidArr);
        $criteria->compare('send_time', ">={$send_time}");
        $wxnotifPtcModel = WxnotifReg::model()->findAll($criteria);
        $childData['expired'] = array();
        if($wxnotifPtcModel){
            foreach ($wxnotifPtcModel as $item){
                $childData['expired'][$item->childid] = $item->childid;
            }
        }

        echo json_encode($childData);
    }

    // 拿到标记孩子父母得邮件
    /*
     * type 1 拿全部 2 拿未绑定微信
     */
    public function actionChildsParentEmail()
    {
        $type = Yii::app()->request->getParam('type', '1');

        // 过滤掉下半年未分班学生
       /* Yii::import('common.models.invoice.ChildReserve');
        $criteria = new CDbCriteria();
        $criteria->compare('calendar', $this->regModel->yid);
        $criteria->compare('schoolid', $this->branchId);
        $criteria->compare('stat', 20);
        $criteria->index = 'childid';
        $reserves = ChildReserve::model()->findAll($criteria);*/

        $criteria = new CDbCriteria();
        $criteria->compare('reg_id', $this->regModel->id);
        $criteria->compare('schoolid', $this->branchId);
        //$criteria->compare('childid', array_keys($reserves));
        $criteria->compare('is_selected', 1);
        $criteria->index = 'childid';
        $regModel = RegStudents::model()->findAll($criteria);
        $childParentEmail = array();
        if($regModel){
            if($type == 2){
                $childParentEmail = $this->NoParentAll(array_keys($regModel));
            }else{
                $childParentEmail = $this->parentAll(array_keys($regModel));
            }
        }
        $childParentEmail = ($childParentEmail) ? implode(';', $childParentEmail) : '';

        $this->addMessage('state', 'success');
        $this->addMessage('data', $childParentEmail);
        $this->showMessage();
    }

    // 全部家长邮件
    public function parentAll($childids)
    {
        $childsModel = ChildProfileBasic::model()->findAllByPk($childids);
        $childParentEmail = array();
        if($childsModel){
            $parents = array();
            foreach ($childsModel as $val){
                if($val->fid){
                    $parents[$val->fid] = $val->fid;
                }
                if($val->mid){
                    $parents[$val->mid] = $val->mid;
                }
            }

            if($parents) {
                $userModel = User::model()->findAllByPk($parents);
                if($userModel) {
                    foreach ($userModel as $item) {
                        $childParentEmail[] = $item->email;
                    }
                }
            }
        }
        return $childParentEmail;
    }

    // 未绑定家长邮件
    public function NoParentAll($childid)
    {
        $childsModel = ChildProfileBasic::model()->findAllByPk($childid);
        $childParentEmail = array();
        if($childsModel){
            $parents = array();
            foreach ($childsModel as $val){
                if($val->fid){
                    $parents[$val->fid] = $val->fid;
                }
                if($val->mid){
                    $parents[$val->mid] = $val->mid;
                }
            }

            $parentOpenid = array();
            Yii::import('common.models.wechat.*');
            $crit = new CDbCriteria();
            $crit->compare('userid', $parents);
            $crit->compare('valid', 1);
            if ($this->branchObj->type == 50) {
                $crit->compare('account', 'ds');
            } else {
                $crit->compare('account', 'ivy');
            }
            $crit->with = 'info';
            $wechatModel = WechatUser::model()->findAll($crit);

            if($wechatModel) {
                foreach ($wechatModel as $val) {
                    $parentOpenid[$val->userid] = 1;
                }
            }

            $childParentsID = array();
            foreach($childsModel as $child) {
                $fidArray = 0;
                $midArray = 0;
                if ($child->fid && $parentOpenid[$child->fid]) {
                    $fidArray = $parentOpenid[$child->fid];
                }

                if ($child->mid && $parentOpenid[$child->mid]) {
                    $midArray = $parentOpenid[$child->mid];
                }
                $parent = $fidArray + $midArray;
                if(!$parent){
                    if ($child->fid) {
                        $childParentsID[$child->fid] = $child->fid;
                    }

                    if ($child->mid) {
                        $childParentsID[$child->mid] = $child->mid;
                    }
                }
            }

            if($childParentsID) {
                $userModel = User::model()->findAllByPk($childParentsID);
                if($userModel) {
                    foreach ($userModel as $item) {
                        $childParentEmail[] = $item->email;
                    }
                }
            }
        }
        return $childParentEmail;
    }

    // 给已经开放得学生发送微信通知
    public function actionSendMessage()
    {
        Yii::import('common.models.ptc.*');
        Yii::import('common.models.wechat.*');

        $childid = Yii::app()->request->getParam('childid', '');

        $this->accessToken = ($this->branchObj->type == 50) ? CommonUtils::getAccessToken('ds'): CommonUtils::getAccessToken('ivy');

        if($this->accessToken) {
            $criteria = new CDbCriteria();
            $criteria->compare('childid', $childid);
            $criteria->compare('reg_id', $this->regModel->id);
            $regStudentsModel = RegStudents::model()->find($criteria);

            if ($regStudentsModel) {
                $send_time = time() - 86400;
                $criteria = new CDbCriteria();
                $criteria->compare('classid', $regStudentsModel->classid);
                $criteria->compare('reg_student_id', $regStudentsModel->id);
                $criteria->compare('childid', $childid);
                $criteria->compare('send_time', ">={$send_time}");
                $wxnotifRegModel = WxnotifReg::model()->count($criteria);
                $message = Yii::t('teaching', 'Notification can be sent only once within 24 hours.');

                if (!$wxnotifRegModel) {
                    $childModel = ChildProfileBasic::model()->findByPk($childid);
                    $parent = array();
                    if ($childModel->fid) {
                        $parent['fid'] = $childModel->fid;
                    }
                    if ($childModel->mid) {
                        $parent['mid'] = $childModel->mid;
                    }
                    $model = new WxnotifReg();
                    $model->childid = $childModel->childid;
                    $model->schoolid = $this->branchId;
                    $model->classid = $regStudentsModel->classid;
                    $model->reg_student_id = $regStudentsModel->id;
                    $model->send_time = time();
                    $model->updated_at = time();
                    $model->updated_by = Yii::app()->user->id;

                    if ($model->save()) {
                        $message = '成功，家长未绑定微信';
                        $data = array();
                        if ($parent) {
                            $criteria = new CDbCriteria();
                            $criteria->compare('userid', $parent);
                            if ($this->branchObj->type == 50) {
                                $criteria->compare('account', 'ds');
                            } else {
                                $criteria->compare('account', 'ivy');
                            }
                            $criteria->compare('valid', 1);
                            $wechatUserModel = WechatUser::model()->findAll($criteria);

                            if ($wechatUserModel) {
                                $childModel = ChildProfileBasic::model()->findByPk($childid);
                                $num = 0;
                                foreach ($wechatUserModel as $val) {
                                    $modelItme = new Wxnotif();
                                    $modelItme->openid = $val->openid;
                                    $modelItme->wx_account = ($this->branchObj->type == 50) ? 'ds' : 'ivy';
                                    $modelItme->task_type = ($this->branchObj->type == 50) ? 'dsReg' : 'ivyReg';
                                    $modelItme->taskid = $model->id;
                                    $modelItme->notify_at = time();
                                    $modelItme->randtag = uniqid();
                                    $modelItme->updated_at = time();
                                    $modelItme->updated_by = Yii::app()->user->id;
                                    $modelItme->save();
                                    $status = $this->sendMessageData($model->childid,$this->branchObj->type, $val->openid, $modelItme, $regStudentsModel, $this->regModel->startyear);
                                    $modelItme->notified = $status;
                                    $modelItme->save();
                                    if ($status == 0) {
                                        $num++;
                                        $information = array();
                                        if ($val->info) {
                                            $information = json_decode($val->info->info, true);
                                        }
                                        $data[$childModel->childid][] = array(
                                            'name' => ($information) ? $information['nickname'] : "",
                                            'headimgurl' => ($information) ? $information['headimgurl'] : "",
                                        );
                                    }
                                }

                                $message = '微信通知发送完成' . $num . '个';
                            }
                        }

                        $this->addMessage('state', 'success');
                        $this->addMessage('message', $message);
                        $this->addMessage('data', $data);
                        $this->showMessage();
                    }
                } else {
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', $message);
                    $this->showMessage();
                }
            }
        }else{
            $this->addMessage('state', 'fail');
            $this->addMessage('message', 'token获取失败');
            $this->showMessage();
        }
    }

    // 发送模板消息
    public function sendMessageData($childid,$schoolid,$openid,$modelItme, $regStudentsModel,$startyear)
    {
        $url = 'https://api.weixin.qq.com/cgi-bin/message/template/send?access_token=' . $this->accessToken;
        // 微信模板消息内容

        if ($schoolid == 50) {
            $appid = 'wxb1a42b81111e29f3';
            $state = 'ds';
            $template_id = 'Le3QnWYs515kiWvJcS8DpkE4ZOuMa7OUpg5U-zyhkBw';
            $ceshiOpenid = 'oHBKPwQUsreFHcZkzsq0CdW7VqEQ';
        } else {
            $appid = 'wx903fba9d4709cf10';
            $state = 'ivy';
            $template_id = 'CCQW07gGa_kIwD_Rt0vsigoP9HY29eGgqGDV1M3O7C0';
            $ceshiOpenid = 'ouwmTjiT6aIh_TLd6Kldr2Cx4tCs';
        }

        $redirectUrl = "http://www.ivyonline.cn/wechat/regExtraInfo/index";
        $touser  = (OA::isProduction()) ? $openid  : $ceshiOpenid;
        $endyear = $startyear + 1;
        $data = array(
            'touser' => $touser,
            'template_id' => $template_id,
            'url' => 'https://open.weixin.qq.com/connect/oauth2/authorize?appid='.$appid.'&redirect_uri='.$redirectUrl.'&response_type=code&scope=snsapi_base&state='.$state.'#wechat_redirect',
            'data' => array(
                'first' => array('value' => '点击“详请”进入'. $startyear.'-'. $endyear .'新生入学登记。Click on "Details" to start student registration.'),
                'keyword1' => array('value' => '入学登记 Registration'),
                'keyword2' => array('value' => '完善资料 Information Submission'),
                'keyword3' => array('value' => '等待家长提交 Waiting for submission'),
                'remark' => array('value' => '请于'. date("Y-m-d", $regStudentsModel->end_time) .'日前完成登记工作。Please complete before '. date("Y-m-d", $regStudentsModel->end_time).'.', 'color' => '#ff6726'),
            ),
        );
        $jsonData = json_encode($data);
        $res = CommonUtils::httpGet($url, $jsonData);
        $jsonRes = CJSON::decode($res);

        Yii::log($state.'=>'. $childid .'=>'.$openid.'=>'.$jsonRes['errcode'], CLogger::LEVEL_INFO, 'wechatReg.teacher');
        return $jsonRes['errcode'];
    }

    // 完成情况
    public function actionProcess()
    {
        $this->branchSelectParams['urlArray'] = array('//mcampus/registration/process');
        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/datatables/jquery.dataTables.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/datatables/dataTables.bootstrap.min.js');
        $cs->registerCssFile(Yii::app()->themeManager->baseUrl.'/base/js/datatables/dataTables.bootstrap.min.css');

        $stepId = Yii::app()->request->getParam('stepId', '1');

        $config = RegExtraInfo::getStepConfig();//获取所有的配置信息
        $completeConfig = RegExtraInfo::completeConfig();//获取步骤对应的状态信息
        $steps = RegExtraInfo::getStepsBySchool($this->branchId);//获取学校设置的步骤
        $startYearList = $this->yearList();

        // 根据stepId 来取数据 默认取的是第一步校车的数据
//        $this->regModel = Reg::model()->findByAttributes(array('yid'=>$this->yid, 'schoolid'=>$this->branchId));
        $regId = $this->regModel->id;#此id通过yid和schoolid从ivy_reg表查询
        $allNum = 0;

        // 过滤掉下半年未分班学生
        Yii::import('common.models.invoice.ChildReserve');
        /*$criteria = new CDbCriteria();
        $criteria->compare('calendar', $this->regModel->yid);
        $criteria->compare('schoolid', $this->branchId);
        $criteria->compare('stat', 20);
        $criteria->index = 'childid';
        $reserves = ChildReserve::model()->findAll($criteria);*/

        $criteria = new CDbCriteria();
        $criteria->compare('t.reg_id', $regId);
        $criteria->compare('t.is_selected', 1);
        //$criteria->compare('t.childid', array_keys($reserves));
        $criteria->index = 'childid';

        $regStudents = RegStudents::model()->findAll($criteria);//获取所有选中的学生
        $allNum = count($regStudents);

        // 获取详细数据
        $data = $this->getProcessData($regId, $stepId);//获取已经填写的的学生信息
        // 过滤掉下半年未分班学生
        foreach ($data as $i => $item) {
            unset($regStudents[$item['childid']]);
            /*if (!isset($reserves[$item['childid']])) {
                unset($data[$i]);
            }*/
        }

        sort($data);

        $unStart = array();
        $unChildids = array();
        $unClassids = array();
        foreach ($regStudents as $childid => $item) {
            $unChildids[] = $childid;
            $unClassids[] = $item->classid;
        }
        $unChild = ChildProfileBasic::model()->findAllByPk($unChildids, array('index'=>'childid'));
        $unClass = IvyClass::model()->findAllByPk($unClassids, array('index'=>'classid'));
        foreach ($regStudents as $childid => $item) {
            $unStart[] = array(
                'childid' => $childid,
                'childid_name' => $unChild[$childid]->getChildName(),
                'gender' => $unChild[$childid]->gender,
                'child_birthday' => date('Y-m-d', $unChild[$childid]->birthday),
                'class_title' => isset($unClass[$item->classid]) ? $unClass[$item->classid]->title : '',
            );
        }

        $res = array(
            'allNum' => $allNum,
            'step' => $stepId,
            'stepNum' => count($data),
            'data' => $data,
            'unStart' => $unStart,
            'unStartNum' => count($unStart),
        );

        $this->render('index', array(
        	'config' => $config,
        	'completeConfig' => $completeConfig,
        	'startYearList' => $startYearList,
        	'yid' => $this->regModel->yid,
        	'steps' => $steps,
        	'stepId' => $stepId,
        	'res' => $res,
        ));
    }

    // 语言配置
    public function actionlanguage()
    {
        $langcn = $this->languageData();
        $this->addMessage('state', 'success');
        $this->addMessage('data', $langcn);
        $this->showMessage();
    }

    public function languageData()
    {
        $langModels = Term::model()->lang()->findAll();

        $titleLang = (Yii::app()->language == "zh_cn") ? "cntitle" : "entitle";
        $langcn = CHtml::listData($langModels, 'diglossia_id', $titleLang);

        return $langcn;
    }

    // 获取每一步的数据
    public function getProcessData($regId, $step)
    {
       $data = array();
       $childids = array();
       $classids = array();
       $i = 0;
       $stepModels = RegStudents::getStudentnumByStep($regId, $step);
       foreach ($stepModels as $key => $item) {
           $info = json_decode($item->data);
           if(in_array($step, array(1,4,7,8,10))){
               if(isset($info->filedata)){
                    $filedata = $info->filedata;
                    $info->filedata = ($filedata) ? RegExtraInfo::getOssImageUrl($filedata) : "";
                    $info->filedataThumb = ($filedata) ? RegExtraInfo::getThumbOssImageUrl($filedata) : "";
               }
           }
           $childid = $item->childid;
           $classid = $item->student->classid;
           $childids[] = $childid;
           $classids[] = $classid;
           $data[$i]['id'] = $item->id;
           $data[$i]['updated'] = date("Y-m-d H:i:s", $item->updated);
           $data[$i]['childid'] = $childid;
           $data[$i]['complete'] = $item->complete;
           $data[$i]['reject_memo'] = $item->reject_memo;
           $data[$i]['link_id'] = $item->link_id;
           $data[$i]['memo'] = $item->memo;
           $data[$i]['classid'] = $classid;
           $data[$i]['childid_name'] = '';
           $data[$i]['childid_birthday'] = '';
           $data[$i]['child_photo'] = '';
           $data[$i]['class_title'] = '';
           $data[$i]['parent_id'] = $item->parent_id;
           $data[$i]['parent_phone'] = $item->parent_phone;
           $data[$i]['info'] = $info;
           unset($stepModels[$key]);
           $i++;
       }

       $childModels = ChildProfileBasic::model()->findAllByPk($childids, array('index'=>'childid'));
       $classModels = IvyClass::model()->findAllByPk($classids, array('index'=>'classid'));
       foreach ($data as $key => $item) {
           if (isset($childModels[$item['childid']])) {
               $data[$key]['childid_name'] = $childModels[$item['childid']]->getChildName();
               $data[$key]['gender'] = $childModels[$item['childid']]->gender;
               $data[$key]['child_birthday'] = date('Y-m-d', $childModels[$item['childid']]->birthday);
               $data[$key]['child_photo'] = CommonUtils::childPhotoUrl($childModels[$item['childid']]->photo);
           }
           if (isset($classModels[$item['classid']])) {
               $data[$key]['class_title'] = $classModels[$item['classid']]->title;
           }
       }

       return $data;
    }

    // 获取某个学生的整体完成情况
    public function actionOverall($childid)
    {
        $regId = $this->regModel->id;
        $childid = Yii::app()->request->getParam('childid');
        $this->addMessage('state', 'fail');
        if (!$childid) {
            $this->addMessage('message', 'childid 不能为空');
            $this->showMessage();
        }
        $childModel = ChildProfileBasic::model()->findByPk($childid);
        if (!$childModel) {
            $this->addMessage('message', '孩子不存在');
            $this->showMessage();
        }

        $data = array(
            'parents' => array(
                'father' => array('name'=>'', 'mphone'=>'', 'email'=>''),
                'mother' => array('name'=>'', 'mphone'=>'', 'email'=>''),
            ),
            'childSteps' => array(),
            'steps' => array()
        );
        $parents = $childModel->getParents();

        if ($parents['father']) {
            $data['parents']['father']['name'] = $parents['father']->getName();
            $data['parents']['father']['mphone'] = $parents['father']['parent']->mphone;
            $data['parents']['father']['email'] = $parents['father']->email;
        }

        if ($parents['mother']) {
            $data['parents']['mother']['name'] = $parents['mother']->getName();
            $data['parents']['mother']['mphone'] = $parents['mother']['parent']->mphone;
            $data['parents']['mother']['email'] = $parents['mother']->email;
        }

        $models = RegExtraInfo::getChildReginfo($regId, $childid);

        $steps = RegExtraInfo::getStepsBySchool($this->branchId);
        $config = RegExtraInfo::getStepConfig();

        foreach ($steps as $key => $step) {
            $data['steps'][$step] = $config[$step];
        }

        $childSteps = array();
        foreach ($models as $model) {
            $childSteps[$model->step]['complete'] = $model->complete;
        }
        $data['childSteps'] = $childSteps;

        $this->addMessage('state', 'success');
        $this->addMessage('data', $data);
        $this->showMessage();
    }

    // 标记每一步的状态（通过，驳回等）
    public function actionCheck()
    {
        $stepIds = Yii::app()->request->getParam('step_ids');
        $rejectMemo = Yii::app()->request->getParam('reject_memo');
        $complete = Yii::app()->request->getParam('complete');

        $this->addMessage('state', 'fail');
        if ($stepIds == null || $complete == null) {
            $this->addMessage('message', '参数错误');
            $this->showMessage();
        }
        // to do 日志记录操作人
        Yii::log($this->staff->uid .': '. $complete .'-'. json_encode($stepIds), CLogger::LEVEL_INFO, 'registration');
        // 修改学生状态
        $models = RegExtraInfo::model()->findAllByPk($stepIds);

        $this->accessToken = ($this->branchObj->type == 50) ? CommonUtils::getAccessToken('ds'): CommonUtils::getAccessToken('ivy');

        foreach ($models as $model) {
        	if ($complete == -1) {
        		if (!$rejectMemo) {
        			$this->addMessage('message', '驳回理由不能为空');
        			$this->showMessage();
        		}
                RegExtraInfo::showMessageOverrule($model->childid,$this->branchObj->type,$model->openid,$model->step);
        		$model->reject_memo = $rejectMemo;
        	}
            $model->complete = $complete;
            $model->save();
            $model->student->updateStatus();
       }

        $this->addMessage('state', 'success');
        $this->addMessage('message', 'success');
        $this->showMessage();
    }

    // 导出审核过的数据
    public function actionExport()
    {
        $step = Yii::app()->request->getParam('step', 1);

        $stepModels = RegStudents::getStudentnumByStep($this->regModel->id, $step);
        $data = array();
        $title = RegExtraInfo::getStepConfig();
        $data['title'] = $this->branchId . "_" . $title[$step] . "已审核.xlsx";
        if($step == RegExtraInfo::PROCESS_BUS){
            $data['items'][0] = array('孩子姓名', '班级', '生日', '状态', '是否乘坐班车', '乘坐方式', '新增站点', '乘坐地点');
        }else if($step == RegExtraInfo::PROCESS_CARD){
            $data['items'][0] = array('孩子姓名', '班级', '生日', '状态', '接送人1姓名', '接送人1关系', '接送人1电话'
            , '接送人2姓名', '接送人2关系', '接送人2电话', '接送人3姓名', '接送人3关系', '接送人3电话'
            , '接送人4姓名', '接送人4关系', '接送人4电话', '车牌号码'
            );
        }else if($step == RegExtraInfo::PROCESS_UNIFORM){
            $data['items'][0] = array('孩子姓名', '班级', '生日', '状态', '性别');
        }else if($step == RegExtraInfo::PROCESS_MEDICAL){
            $data['items'][0] = array('孩子姓名', '班级', '生日', '状态',
                '意向医院1', '意向医院2 ', '保险公司', '紧急联系人 1', '紧急联系人电话 1', '紧急联系人 2',
                '紧急联系人电话 2', '多动症', '心脏病','过敏（食品、药品、其他）', '耳部疾病（听力）', '哮喘',
                '肝炎', '背部或脊柱问题', '消化系统疾病','骨折', '皮肤病', '糖尿病',
                '视力问题', '癫病', '结核','食物过敏源', '其他疾病（请写明）',
                '体检报告', '疫苗打针记录需复印件', '北京儿童保健记录');
        }else if($step == RegExtraInfo::PROCESS_LUNCH){
            $data['items'][0] = array('孩子姓名', '班级', '生日', '状态', '是否需要午餐', '特殊说明', '过敏说明');
        }else if($step == RegExtraInfo::PROCESS_LANG){
            $languageList = $this->languageData();
            $data['items'][0] = array('孩子姓名', '班级', '生日', '状态', '第一种语言', '家庭常用语言', '孩子常用');
        }else if($step == RegExtraInfo::PROCESS_FEE){
            $data['items'][0] = array('孩子姓名', '班级', '生日', '状态');
        }else if($step == RegExtraInfo::PROCESS_SAFE){
            $data['items'][0] = array('孩子姓名', '班级', '生日', '状态');
        }else if($step == RegExtraInfo::PROCESS_FAMILYID){
            $data['items'][0] = array('孩子姓名', '班级', '生日', '状态');
        }else if($step == RegExtraInfo::PROCESS_INFORMATION){
            $data['items'][0] = array('孩子姓名', '班级', '生日', '状态', '过敏', '病史', '紧急联系人', '关系', '电话', '紧急联系人', '关系', '电话', '医院', '是否用于推广', '签名');
        }else if($step == RegExtraInfo::PROCESS_MATERIAL){
            $data['items'][0] = array('孩子姓名', '班级', '生日', '状态');
        }else{
            $data['items'][0] = array('孩子姓名', '班级', '生日', '状态');
        }
        $customize = array(1 => '', 2 => '是');
        if($stepModels) {
            // 过滤掉下半年未分班学生
            Yii::import('common.models.invoice.ChildReserve');
           /* $criteria = new CDbCriteria();
            $criteria->compare('calendar', $this->regModel->yid);
            $criteria->compare('schoolid', $this->branchId);
            $criteria->compare('stat', 20);
            $criteria->index = 'childid';
            $reserves = ChildReserve::model()->findAll($criteria);*/
            $completeConfig = RegExtraInfo::completeConfig();
            foreach ($stepModels as $i => $item) {
                /*if (!isset($reserves[$item->childid])) {
                    continue;
                }*/
                $childModels = ChildProfileBasic::model()->findByPk($item->childid, array('index'=>'childid'));
                $classModels = IvyClass::model()->findByPk($item->student->classid, array('index'=>'classid'));
                $info = json_decode($item->data);

                $array = array();
                $array[] = $childModels->getChildName();
                $array[] = $classModels->title;
                $array[] = date('Y-m-d', $childModels->birthday);
                $array[] = $completeConfig[$item->complete];
                if($step == RegExtraInfo::PROCESS_BUS){
                    $busStyle = RegExtraInfo::getBusStyle();
                    if (isset($info->needBus) && $info->needBus == 1) {
                        $array[] = (isset($info->needBus) && $info->needBus == 1) ? '是' : '否' ;
                        $array[] = isset($info->journey) ? $busStyle[$info->journey] : '' ;
                        $array[] = isset($info->customize) ? $customize[$info->customize] : ' ';
                        $array[] = isset($info->parking) ? $info->parking : '' ;
                    } else {
                        $array[] = '否';
                        $array[] = '-' ;
                        $array[] = '-';
                        $array[] = '-' ;
                    }

                }else if($step == RegExtraInfo::PROCESS_CARD){
                    foreach ($info as $val){
                        $array[] = $val->name;
                        $array[] = $val->relation;
                        $array[] = $val->tel;
                    }
                    $array[] = $item->memo;
                }else if($step == RegExtraInfo::PROCESS_UNIFORM){
                    $array[] = ($info->gender == 1) ? '男' : '女' ;
                }else if($step == RegExtraInfo::PROCESS_MEDICAL){
                    $array[] = $info->preferredHospitalOne;
                    $array[] = $info->preferredHospitalTwo;
                    $array[] = $info->insuranceCompany;
                    $array[] = $info->oneEmergencyName;
                    $array[] = $info->oneEmergencyPhone;
                    $array[] = $info->twoEmergencyName;
                    $array[] = $info->twoEmergencyPhone;
                    $array[] = ($info->ADHD == 1) ? '是' : '否';
                    $array[] = ($info->heartDisorder == 1) ? '是' : '否';
                    $array[] = ($info->allergies == 1) ? '是' : '否';
                    $array[] = ($info->frequent == 1) ? '是' : '否';
                    $array[] = ($info->asthma == 1) ? '是' : '否';
                    $array[] = ($info->hepatitis == 1) ? '是' : '否';
                    $array[] = ($info->problems == 1) ? '是' : '否';
                    $array[] = ($info->gastrointertianl == 1) ? '是' : '否';
                    $array[] = ($info->fractures == 1) ? '是' : '否';
                    $array[] = ($info->skinProblems == 1) ? '是' : '否';
                    $array[] = ($info->diabetes == 1) ? '是' : '否';
                    $array[] = ($info->visionProblems == 1) ? '是' : '否';
                    $array[] = ($info->seizureDisorde == 1) ? '是' : '否';
                    $array[] = ($info->tuberculosis == 1) ? '是' : '否';
                    $array[] = $info->specialFood;
                    $array[] = $info->other;
                    $array[] = count($info->medicalReport);
                    $array[] = count($info->vaccineReport);
                    $array[] = count($info->healthReport);
                    // 未确定
                }else if($step == RegExtraInfo::PROCESS_LUNCH){
                    $lunchConfig = array(
                        1=>'我的孩子在学校享用正常午餐',
                        2=>'我的孩子在学校享用特殊不含猪肉午餐',
                        3=>'我的孩子在学校享用特殊纯素食午餐',
                    );
                    if ($info->lunch) {
                        $array[] = '是';
                        $array[] = $lunchConfig[$info->lunchSort] ;
                    } else {
                        $array[] = '否';
                        $array[] = '' ;
                    }
                    $array[] = $info->allergen ;
                }else if($step == RegExtraInfo::PROCESS_LANG){
                    $array[] = $languageList[$info->firstLanguage];
                    $array[] = $languageList[$info->familyLanguage];
                    $array[] = $languageList[$info->commonlyLanguage];
                }else if($step == RegExtraInfo::PROCESS_FEE){

                }else if($step == RegExtraInfo::PROCESS_SAFE) {

                }else if($step == RegExtraInfo::PROCESS_FAMILYID){

                }else if($step == RegExtraInfo::PROCESS_INFORMATION){
                    $array[] = $info->allergy;
                    $array[] = $info->medicalHistory;
                    $array[] = $info->personName1;
                    $array[] = $info->relationship1;
                    $array[] = $info->contactNumber1;
                    $array[] = $info->personName2;
                    $array[] = $info->relationship2;
                    $array[] = $info->contactNumber2;
                    $array[] = $info->hospital;
                    $array[] = ($info->is_promote == 1) ? '是' : '否';
                    $array[] = ($info->filedata) ? '是' : '否';
                }else if($step == RegExtraInfo::PROCESS_MATERIAL){

                }else{

                }
                $data['items'][$i + 1] = $array;
            }
        }

        $this->addMessage('state', 'success');
        $this->addMessage('data', $data);
        $this->showMessage();

    }

    // 打印协议和签名
    public function actionPrint()
    {
        $this->layout = "//layouts/print";
        $this->printFW = $this->branchObj->getPrintHeader();

        $infoId = Yii::app()->request->getParam('infoId','');
        $stepId = Yii::app()->request->getParam('stepId','');
        $status = Yii::app()->request->getParam('status',''); // 不为空就是打印协议和签名  现在只有第四步用到

        $model = array();
        if($infoId) {
            // 根据步奏拿配置
            $criteria = new CDbCriteria();
            $criteria->compare('step', $stepId);
            $criteria->compare('schoolid', $this->branchId);
            $criteria->compare('reg_id', $this->regModel->id);
            $configModel = RegConfig::model()->find($criteria);

            // 根据Reg_info的id 查询数据
            $model = RegExtraInfo::model()->findByPk($infoId);

            if($stepId == 4){
                $regExtraInfo = RegExtraInfo::model()->findByPk($infoId);
            }
        }

        $view = RegExtraInfo::getPrint();

        $this->render($view[$stepId], array(
            'model' => $model,
            'regExtraInfo' => $regExtraInfo,
            'configModel' => $configModel,
            'status' => $status,
        ));
    }

    // 打印附件 第四步
    public function actionPrintAnnex()
    {
        $this->layout = "//layouts/print";
        //$this->printFW = $this->branchObj->getPrintHeader();

        $infoId = Yii::app()->request->getParam('infoId','');
        $stepId = Yii::app()->request->getParam('stepId','');
        $model = RegExtraInfo::model()->findByPk($infoId);
        $this->render('printAnnex', array(
            'model' => $model,
        ));
    }

    // 查看审核详情（历史记录）
    public function actionShowDetail()
    {
        $stepId = Yii::app()->request->getParam('step_id', '');
        $model = RegExtraInfo::model()->findByPk($stepId);
        if (!$model) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', 'ID错误');
            $this->showMessage();
        }
        $data = RegExtraInfo::showDetail($model);
        $this->addMessage('state', 'success');
        $this->addMessage('data', $data);
        $this->showMessage();
    }


    // 接送卡列表
    public function actionPrintcardList()
    {
        $this->branchSelectParams['urlArray'] = array('//mcampus/registration/printcardList');
        $classData = array();

        // 过滤掉下半年未分班学生
        Yii::import('common.models.invoice.ChildReserve');
        /*$criteria = new CDbCriteria();
        $criteria->compare('calendar', $this->regModel->yid);
        $criteria->compare('schoolid', $this->branchId);
        $criteria->compare('stat', 20);
        $criteria->index = 'childid';
        $reserves = ChildReserve::model()->findAll($criteria);*/

        $criteria = new CDbCriteria();
        $criteria->compare('reg_id', $this->regModel->id);
        $criteria->compare('schoolid', $this->branchId);
        //$criteria->compare('childid', array_keys($reserves));
        $criteria->compare('step', RegExtraInfo::PROCESS_CARD);
        $criteria->compare('complete', 1);
        $criteria->compare('status', 1);
        $criteria->index = 'childid';
        $regInfoModel = RegExtraInfo::model()->findAll($criteria);

        if($regInfoModel){
            $criteria = new CDbCriteria();
            $criteria->compare('reg_id', $this->regModel->id);
            $criteria->compare('is_selected', 1);
            $criteria->compare('childid', array_keys($regInfoModel));
            $criteria->index = 'classid';
            $regStudents = RegStudents::model()->findAll($criteria);

            if($regStudents){
                $classModel = IvyClass::model()->findAllByPk(array_keys($regStudents));
                if($classModel) {
                    foreach ($classModel as $value) {
                        $classData[$value->classid] = $value->title;
                    }
                }
            }
        }

        $startYearList = $this->yearList();
        $this->printFW = $this->branchObj->getPrintHeader();
        $data = array();
        $logo = "http://m2.files.ivykids.cn/cloud01-file-8025768FpN4sDubssQGgIeKSWowx2bdhdsT.png";
        if($this->branchObj->type == 50){
            $logo = "http://mega.ivymik.cn/reg/student/ds-hoz-logo.png";
        }else if($this->branchId == 'BJ_QFF'){
            $logo ="http://m2.files.ivykids.cn/cloud01-file-8025768FpM36Fm8VH0pyYCjqRBA2_HMOimk.png";
        }

        $nextYear = $this->regModel->startyear+1;
        $year = $this->regModel->startyear . " - " . $nextYear;

        $branchTitle = $this->branchObj->info->getTitle();

        $this->render('printcardList', array(
            'startYearList' => $startYearList,
            'yid' => $this->regModel->yid,
            'classData' => $classData,
            'data' => $data,
            'logo' => $logo,
            'year' => $year,
            'branchTitle' => $branchTitle,
        ));
    }

    // 根据班级拿到打印卡得数据
    public function actionClassCard()
    {
        // 过滤掉下半年未分班学生
        Yii::import('common.models.invoice.ChildReserve');
        /*$criteria = new CDbCriteria();
        $criteria->compare('calendar', $this->regModel->yid);
        $criteria->compare('schoolid', $this->branchId);
        $criteria->compare('stat', 20);
        $criteria->index = 'childid';
        $reserves = ChildReserve::model()->findAll($criteria);*/

        $classId = Yii::app()->request->getParam('classid', array());

        $criteria = new CDbCriteria();
        $criteria->compare('reg_id', $this->regModel->id);
        $criteria->compare('classid', $classId);
        //$criteria->compare('childid', array_keys($reserves));
        $criteria->compare('is_selected', 1);
        $criteria->index = 'childid';
        $regStudents = RegStudents::model()->findAll($criteria);

        if($regStudents) {
            $data = $this->cradData(array_keys($regStudents));
        }

        $this->addMessage('state', 'success');
        $this->addMessage('data', $data);
        $this->showMessage();

    }

    // 打印接送卡
    public function actionPrintcard()
    {
        $this->layout = "//layouts/print";
        $this->printFW = $this->branchObj->getPrintHeader();

        $childid = Yii::app()->request->getParam('childid', array());

        $data = array();
        if($childid) {
            $data = $this->cradData($childid);
        }
        $logo = "http://m2.files.ivykids.cn/cloud01-file-8025768FpN4sDubssQGgIeKSWowx2bdhdsT.png";
        if($this->branchObj->type == 50){
            $logo = "http://mega.ivymik.cn/reg/student/ds-hoz-logo.png";
        }else if($this->branchId == 'BJ_QFF'){
            $logo ="http://m2.files.ivykids.cn/cloud01-file-8025768FpM36Fm8VH0pyYCjqRBA2_HMOimk.png";
        }
        $nextYear = $this->regModel->startyear+1;
        $year = $this->regModel->startyear . " - " . $nextYear;

        $branchTitle = $this->branchObj->info->getTitle();
        $this->render('printcard', array(
            'year' => $year,
            'data' => $data,
            'logo' => $logo,
            'branchTitle' => $branchTitle,
        ));
    }

    public function actionPrintStudentIdCard()
    {
        $this->layout = "//layouts/print";
        $this->printFW = $this->branchObj->getPrintHeader();
        $step_id = Yii::app()->request->getParam('step_id', '');
        $info = array();
        $classTitle = '';
        $childName = '';
        if($step_id){
            $model = RegExtraInfo::model()->findByPk($step_id);
            $classTitle = IvyClass::model()->findByPk($model->student->classid);
            $childName = $model->student->childProfile->getChildName();
            $safeData = json_decode($model->data);
            if($safeData) {
                foreach ($safeData as $key => $parent) {
                    if ($parent) {
                        foreach ($parent as $item) {
                            $info[$key][] = RegExtraInfo::getOssImageUrl($item);
                        }
                    }
                }
            }
        }
        $logo = "http://mega.ivymik.cn/reg/student/sl-hoz-logo.png";
        if($this->branchObj->type == 50){
            $logo = "http://mega.ivymik.cn/reg/student/ds-hoz-logo.png";
        }
        $type = array(
            'studentdata' => Yii::t('reg','Student Passport Photo'),
            'parentsdata' => Yii::t('reg','Mother Passport Photo'),
            'vaccinedata' => Yii::t('reg','Photo Copy of Vaccine Record'),
            'insurancedata' => Yii::t('reg','Commercial Medical Insurance Card Photo'),
            );
        $this->render('printStudentIdCard', array(
            'info' => $info,
            'logo' => $logo,
            'classTitle' => $classTitle,
            'childName' => $childName,
            'type' => $type,
        ));
    }

    public function cradData($childid)
    {
        $data = array();
        if($childid) {
            $criteria = new CDbCriteria();
            $criteria->compare('reg_id', $this->regModel->id);
            $criteria->compare('childid', $childid);
            $criteria->compare('schoolid', $this->branchId);
            $criteria->compare('step', RegExtraInfo::PROCESS_CARD);
            $criteria->compare('status', 1);
            $criteria->compare('complete', 1);
            $regExtraInfoModel = RegExtraInfo::model()->findAll($criteria);

            if ($regExtraInfoModel) {
                foreach ($regExtraInfoModel as $val) {
                    $child = ChildProfileBasic::model()->findByPk($val->childid);
                    $parents = json_decode($val->data, true);
                    $data[$val->childid]['childName'] = $child->name_cn;
                    $data[$val->childid]['childName_en'] = trim(sprintf("%s %s", trim($child->first_name_en), trim($child->last_name_en)));
                    $data[$val->childid]['photo'] = CommonUtils::childPhotoUrl($child->photo);
                    foreach ($parents as $parent) {
                        if ($parent['name'] && $parent['photo']) {
                            $printUrl = RegExtraInfo::getOssImageUrl($parent['photo']);
                            $url = RegExtraInfo::getThumbOssImageUrl($parent['photo']);
                            $data[$val->childid]['parent'][] = array(
                                'name' => $parent['name'],
                                'relation' => $parent['relation'],
                                'tel' => $parent['tel'],
                                'photo' => $url,
                                'printPhoto' => $printUrl,
                            );
                        }
                    }
                }
            }
        }

        return $data;
    }

    // 注册配置
    public function actionRegistconfig()
    {
        $this->branchSelectParams['urlArray'] = array('//mcampus/registration/registconfig');
        $config = RegExtraInfo::getStepConfig();
        $steps = RegExtraInfo::getStepsBySchool($this->branchId);
        array_unshift($config, '欢迎页');
        array_unshift($steps, 0);

        $stepId = Yii::app()->request->getParam('step_id',0);

        $criteria = new CDbCriteria();
        $criteria->compare('step', $stepId);
        $criteria->compare('schoolid', $this->branchId);
        $criteria->compare('reg_id', $this->regModel->id);
        $model = RegConfig::model()->find($criteria);

        if(!$model){
            $model = new RegConfig();
        }

        $yearList = $this->yearList();
        $this->render('registconfig', array(
            'config' => $config,
            'steps' => $steps,
            'yearList' => $yearList,
            'yid' => $this->regModel->yid,
            'model' => $model,
            'stepId' => $stepId,
        ));
    }

    /*
     * 增加每一步得配置
     */
    public function actionAddConfig()
    {
        $stepId = Yii::app()->request->getParam('step_id', 0);
        $regConfig = Yii::app()->request->getParam('RegConfig', array());

        $configId = $regConfig['id'];
        if($stepId === 0){
            $content_cn['welcome'] = ($regConfig['welcome_cn']) ? base64_encode($regConfig['welcome_cn']) : "";
            $content_en['welcome'] = ($regConfig['welcome_en']) ? base64_encode($regConfig['welcome_en']) : "";
            $this->udateConfigData($configId, $content_cn, $content_en, $stepId);
        }else if ($stepId == 1){ // 班车
            $content_cn['bus'] = ($regConfig['bus_cn']) ? base64_encode($regConfig['bus_cn']) : "";
            $content_en['bus'] = ($regConfig['bus_en']) ? base64_encode($regConfig['bus_en']) : "";
            $this->udateConfigData($configId, $content_cn, $content_en, $stepId);
        }else if ($stepId == 2){ // 接送卡
            $content_cn['card'] = ($regConfig['card_cn']) ? base64_encode($regConfig['card_cn']) : "";
            $content_en['card'] = ($regConfig['card_en']) ? base64_encode($regConfig['card_en']) : "";
            $this->udateConfigData($configId, $content_cn, $content_en, $stepId);
        }else if ($stepId == 3){ // 校服
            $content_cn['uniform_man'] = ($regConfig['uniform_man_cn']) ? base64_encode($regConfig['uniform_man_cn']) : "";
            $content_cn['uniform_woman'] = ($regConfig['uniform_woman_cn']) ? base64_encode($regConfig['uniform_woman_cn']) : "";
            $content_en['uniform_man'] = ($regConfig['uniform_man_en']) ? base64_encode($regConfig['uniform_man_en']) : "";
            $content_en['uniform_woman'] = ($regConfig['uniform_woman_en']) ? base64_encode($regConfig['uniform_woman_en']) : "";
            $content_cn['uniform_primary_man'] = ($regConfig['uniform_primary_man_cn']) ? base64_encode($regConfig['uniform_primary_man_cn']) : "";
            $content_cn['uniform_primary_woman'] = ($regConfig['uniform_primary_woman_cn']) ? base64_encode($regConfig['uniform_primary_woman_cn']) : "";
            $content_en['uniform_primary_man'] = ($regConfig['uniform_primary_man_en']) ? base64_encode($regConfig['uniform_primary_man_en']) : "";
            $content_en['uniform_primary_woman'] = ($regConfig['uniform_primary_woman_en']) ? base64_encode($regConfig['uniform_primary_woman_en']) : "";
            $this->udateConfigData($configId, $content_cn, $content_en, $stepId);
        }else if ($stepId == 4){ // 医疗
            $content_cn['medical'] = ($regConfig['medical_cn']) ? base64_encode($regConfig['medical_cn']) : "";
            $content_en['medical'] = ($regConfig['medical_en']) ? base64_encode($regConfig['medical_en']) : "";

            $content_cn['medical_kindergarten'] = ($regConfig['medical_kindergarten_cn']) ? base64_encode($regConfig['medical_kindergarten_cn']) : "";
            $content_en['medical_kindergarten'] = ($regConfig['medical_kindergarten_en']) ? base64_encode($regConfig['medical_kindergarten_en']) : "";

            $content_cn['medical_content'] = ($regConfig['medical_content_cn']) ? base64_encode($regConfig['medical_content_cn']) : "";
            $content_en['medical_content'] = ($regConfig['medical_content_en']) ? base64_encode($regConfig['medical_content_en']) : "";

            $this->udateConfigData($configId, $content_cn, $content_en, $stepId);
        }else if ($stepId == 5){ // 午餐
            $content_cn['lunch'] = ($regConfig['lunch_cn']) ? base64_encode($regConfig['lunch_cn']) : "";
            $content_en['lunch'] = ($regConfig['lunch_en']) ? base64_encode($regConfig['lunch_en']) : "";
            $this->udateConfigData($configId, $content_cn, $content_en, $stepId);
        }else if ($stepId == 6){ // 家庭语言
            $content_cn['lang'] = ($regConfig['lang_cn']) ? base64_encode($regConfig['lang_cn']) : "";
            $content_en['lang'] = ($regConfig['lang_en']) ? base64_encode($regConfig['lang_en']) : "";
            $this->udateConfigData($configId, $content_cn, $content_en, $stepId);
        }else if ($stepId == 7){ // 收费协议
            $content_cn['fee'] = ($regConfig['fee_cn']) ? base64_encode($regConfig['fee_cn']) : "";
            $content_en['fee'] = ($regConfig['fee_en']) ? base64_encode($regConfig['fee_en']) : "";

            $this->udateConfigData($configId, $content_cn, $content_en, $stepId);
        }else if ($stepId == 8){ // 安全协议
            $content_cn['safe'] = ($regConfig['safe_cn']) ? base64_encode($regConfig['safe_cn']) : "";
            $content_en['safe'] = ($regConfig['safe_en']) ? base64_encode($regConfig['safe_en']) : "";

            $this->udateConfigData($configId, $content_cn, $content_en, $stepId);
        }else if ($stepId == 9){ // 家庭证件
            $content_cn['student'] = ($regConfig['student_cn']) ? base64_encode($regConfig['student_cn']) : "";
            $content_en['student'] = ($regConfig['student_en']) ? base64_encode($regConfig['student_en']) : "";
            $this->udateConfigData($configId, $content_cn, $content_en, $stepId);
        }else if ($stepId == 10){ // 学生家庭信息采集
            $content_cn['parents'] = ($regConfig['parents_cn']) ? base64_encode($regConfig['parents_cn']) : "";
            $content_en['parents'] = ($regConfig['parents_en']) ? base64_encode($regConfig['parents_en']) : "";

            $this->udateConfigData($configId, $content_cn, $content_en, $stepId);
        }else if ($stepId == 11){ // 材料费
            $content_cn['material'] = ($regConfig['material_cn']) ? base64_encode($regConfig['material_cn']) : "";
            $content_en['material'] = ($regConfig['material_en']) ? base64_encode($regConfig['material_en']) : "";

            $this->udateConfigData($configId, $content_cn, $content_en, $stepId);
        }
        $this->addMessage('state', 'success');
        $this->addMessage('message', '成功');
        $this->addMessage('callback', 'cbSuccess');
        $this->showMessage();
    }

    public function udateConfigData($configId, $content_cn, $content_en, $stepId)
    {
        $model = RegConfig::model()->findByPk($configId);
        if(!$model){
            $model = new RegConfig();
            $model->reg_id = $this->regModel->id;
            $model->step = $stepId;
            $model->schoolid = $this->branchId;
            $model->created_at = time();
            $model->created_by = Yii::app()->user->id;
        }

        $model->content_cn = json_encode($content_cn);
        $model->content_en = json_encode($content_en);
        $model->updated_at = time();
        $model->updated_by = Yii::app()->user->id;
        if(!$model->save()){
            return false;
        }
        return true;
    }

    // 站点显示
    public function actionCarsitelist()
    {
        $this->branchSelectParams['urlArray'] = array('//mcampus/registration/carsitelist');
        Yii::import('common.models.calendar.*');
        Yii::import('common.models.visit.*');

        $startYearList = $this->yearList();
        $criteria = new CDbCriteria();
        $criteria->compare('t.schoolid', $this->branchId);
        $regExtraInfoModel = Reg::model()->findAll($criteria);

        $oldCarSite = array();
        if($regExtraInfoModel){
            foreach ($regExtraInfoModel as $val){
                if($val->regCarSite) {
                    $endYear = $val->startyear + 1;
                    $oldCarSite[$val->id] = $val->startyear . ' - ' . $endYear;
                }
            }
        }


        $criteria = new CDbCriteria();
        $criteria->compare('reg_id', $this->regModel->id);
        $criteria->compare('schoolid', $this->branchId);
        $regCarSite = RegCarSite::model()->findAll($criteria);
        $carSiteList = array();

        if($regCarSite){
            foreach ($regCarSite as $item){
                $carSiteList[] = array(
                    'id' => $item->id,
                    'name_cn' => $item->name_cn,
                    'name_en' => $item->name_en,
                    'park_address_cn' => $item->park_address_cn,
                    'park_address_en' => $item->park_address_en,
                    'site_name' => $item->site_name,
                    'longitude' => $item->longitude,
                    'latitude' => $item->latitude,
                    'created_at' => date("Y-m-d", $item->created_at),
                );
            }
        }
        //  现在站点数量
        $siteNum = ($carSiteList) ? count($carSiteList) : 0;

        $model = new EventImportForm;
        $this->render('carsitelist', array(
            'oldCarSite' => $oldCarSite,
            'carSiteList' => $carSiteList,
            'startYearList' => $startYearList,
            'model' => $model,
            'yid' => $this->regModel->yid,
            'siteNum' => $siteNum,
        ));
    }

    // 导入站点名称  CSV
    public function actionImport()
    {
        Yii::import('common.models.visit.*');
        $model = new EventImportForm;
        $valArray = array();

        if (isset($_POST['EventImportForm'])) {
            $model->attributes = $_POST['EventImportForm'];
            if ($model->validate()) {
                $model->csv = CUploadedFile::getInstance($model, 'csv');
                if ($model->csv) {
                    $fileName = uniqid() . '.' . $model->csv->getExtensionName();
                    $model->csv->saveAs(Yii::app()->params['OAUploadBasePath'] . '/' . $fileName);
                    if (is_file(Yii::app()->params['OAUploadBasePath'] . '/' . $fileName)) {
                        $fileArray = file(Yii::app()->params['OAUploadBasePath'].'/'.$fileName);
                        @unlink(Yii::app()->params['OAUploadBasePath'].'/'.$fileName);
                        foreach($fileArray as $val){
                            $val = iconv('GB2312', 'UTF-8', $val);
                            $val = str_replace(';',',', $val);
                            $valArray[] = explode(',', $val);
                        }
                        array_shift($valArray);
                        $this->addMessage('state', 'success');
                        $this->addMessage('message', '成功');
                        $this->addMessage('data', $valArray);
                        $this->addMessage('callback', 'cbSite');
                        $this->showMessage();
                    }
                }
            }
            else {
                $this->addMessage('state', 'fail');
                $errs = current($model->getErrors());
                $this->addMessage('message', $errs?$errs[0]:Yii::t('message','Failed!'));
            }
        }
        $this->showMessage();
    }

    // 增加站点录入
    public function actionCarsite()
    {
        $carSiteId = Yii::app()->request->getParam('carSiteId','');
        $name_cn = Yii::app()->request->getParam('name_cn','');
        $name_en = Yii::app()->request->getParam('name_en','');
        $park_address_cn = Yii::app()->request->getParam('park_address_cn','');
        $park_address_en = Yii::app()->request->getParam('park_address_en','');
        $siteName = Yii::app()->request->getParam('siteName','');
        $longitude = Yii::app()->request->getParam('longitude','');
        $latitude = Yii::app()->request->getParam('latitude','');
        $status = Yii::app()->request->getParam('status','10');

        if(Yii::app()->request->isPostRequest ){
            $model = RegCarSite::model()->findByPk($carSiteId);
            if (!$model) {
                $model = new RegCarSite();
                $model->reg_id = $this->regModel->id;
                $model->schoolid = $this->branchId;
                $model->created_at = time();
                $model->created_by = Yii::app()->user->id;
                $model->site_name = $siteName;
                $model->longitude = $longitude;
                $model->latitude = $latitude;
            }

            $model->name_cn = $name_cn;
            $model->name_en = $name_en;
            $model->park_address_cn = $park_address_cn;
            $model->park_address_en = $park_address_en;
            $model->status = $status;
            $model->updated_at = time();
            $model->updated_by = Yii::app()->user->id;

            if ($model->save()) {
                $data = array(
                    'id' => $model->id,
                    'name_cn' => $model->name_cn,
                    'name_en' => $model->name_en,
                    'park_address_cn' => $model->park_address_cn,
                    'park_address_en' => $model->park_address_en,
                    'site_name' => $model->site_name,
                    'longitude' => $model->longitude,
                    'latitude' => $model->latitude,
                    'created_at' => strtotime($model->created_at),
                );
                $this->addMessage('state', 'success');
                $this->addMessage('data', $data);
                $this->addMessage('message', Yii::t('message', 'success'));
                $this->addMessage('callback', 'cbVisit');
            } else {
                $this->addMessage('state', 'fail');
                $err = current($model->getErrors());
                $this->addMessage('message', $err ? $err[0] : '失败');
            }
            $this->showMessage();
        }
        $this->addMessage('state', 'fail');
        $this->addMessage('message', '非法操作');
        $this->showMessage();
    }

    // 复制站点
    public function actionCopyCarsite()
    {
        $reg_id = Yii::app()->request->getParam('reg_id','');

        if($reg_id){
            $criteria = new CDbCriteria();
            $criteria->compare('reg_id', $reg_id);
            $regCarSite = RegCarSite::model()->findByPk($criteria);
            if($regCarSite){
                foreach ($regCarSite as $val) {
                    $model = new RegCarSite();
                    $model->reg_id = $this->regModel->id;
                    $model->name_cn = $val->name_cn;
                    $model->name_en = $val->name_en;
                    $model->park_address_cn = $val->park_address_cn;
                    $model->park_address_en = $val->park_address_en;
                    $model->site_name = $val->site_name;
                    $model->longitude = $val->longitude;
                    $model->latitude = $val->latitude;
                    $model->schoolid = $val->schoolid;
                    $model->status = 10;
                    $model->updated_at = time();
                    $model->updated_by = Yii::app()->user->id;
                    $model->created_at = time();
                    $model->created_by = Yii::app()->user->id;
                    $model->save();
                }
            }
        }
    }

    // 删除站点
    public function actionDelCarsite()
    {
        $regCarSiteId = Yii::app()->request->getParam('regCarSiteId','');
        if($regCarSiteId){
            $model = RegCarSite::model()->findByPk($regCarSiteId);
            if($model){
                $model->delete();
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message', 'success'));
                $this->showMessage();
            }
        }
        $this->addMessage('state', 'fail');
        $this->addMessage('message', '非法操作');
        $this->showMessage();
    }

    // 查找、生成默认的regmodel
    public function generalReg($yid)
    {
        Yii::import('common.models.calendar.*');
        $calendar = Calendar::model()->findByPk($yid);
        if (!$calendar) {
            return false;
        }
        $dateArr = explode(',', $calendar->timepoints);
        $regModel = new Reg();
        $regModel->yid = $yid;
        $regModel->schoolid = $this->branchId;
        $regModel->startyear = $calendar->startyear;
        $regModel->startdate = $dateArr[0];
        $regModel->enddate = $dateArr[3];
        $regModel->updated_by = Yii::app()->user->id;
        $regModel->updated_at = time();
        if (!$regModel->save()) {
            return false;
        }

        $this->regModel = $regModel;
        return true;
    }

    // 艾毅入学申请审核完成数据
    public function actionStudentsList(){
        $yid = Yii::app()->request->getParam('yid', $this->regModel->yid);
        $search = Yii::app()->request->getParam('search', "");

        $criteria = new CDbCriteria();
        $criteria->compare('branchid', $this->branchId);
        $criteria->order = 'startyear desc';
        $calendarModel = CalendarSchool::model()->findAll($criteria);

        // 根据当前校园查询出所有得学年
        $startYearList = array();
        $yearList = array();

        foreach ($calendarModel as $val){
            $yearEnd = $val->startyear  + 1;
            $startYearList[$val->yid] = $val->startyear . ' - ' . $yearEnd;
            $yearList[$val->yid] = $val->startyear;
        }


        $criteria = new CDbCriteria();
        $criteria->compare('yid', $yid);
        $criteria->compare('schoolid', $this->branchId);
        $regModel = Reg::model()->find($criteria);

        $sql = "SELECT childid, count(*) as count FROM `ivy_reg_extra_info` `t` WHERE reg_id=". $regModel->id ." AND status=1 group by childid HAVING count > 4";
        $refund = Yii::app()->subdb->createCommand($sql)->queryAll();

        $regStudentsInfo = array();

        foreach ($refund as $val){
            $regStudentsInfo[$val["childid"]] = $val["childid"];
        }

        $model = new RegStudents;
        $criteria = new CDbCriteria();

        if($search) {
            $crit = new CDbCriteria();
            $crit->addCondition("concat_ws(',',name_cn,first_name_en,middle_name_en,last_name_en) like '%{$search}%' ");
            $crit->index = "childid";
            $childidModel = ChildProfileBasic::model()->findAll($crit);
            $childids = array();
            if($childidModel){
                foreach ($childidModel as $key => $val){
                    if($regStudentsInfo[$key]){
                        $childids[$key] = $key;
                    }
                }
                $childid = $childids ? array_keys($childids) : 0;
                $criteria->compare('childid', $childid);

            }
        }else{
            $criteria->compare('childid', $regStudentsInfo);
        }

        $criteria->compare('reg_id', $regModel->id);
        $criteria->compare('schoolid', $this->branchId);

        $dataProvider = new CActiveDataProvider($model, array(
            'criteria'=>$criteria,
            'sort' => array(
                //'defaultOrder' => 'id desc',
            ),
            'pagination'=>array(
                'pageSize'=>2000,
            ),
        ));

        $this->render('studentsList', array(
            'startYearList' => $startYearList,
            'yearList' => $yearList,
            'dataProvider' => $dataProvider,
            'branchId' => $this->branchId,
            'yid' => $yid,
        ));
    }

    public function getButton($data)
    {
        echo CHtml::link(Yii::t('global', '打印学生信息'), array('printStudentAllInfo', 'ids' => $data->id, "branchId"=>Yii::app()->controller->branchId), array('class' => 'btn btn-primary btn-xs mb5', 'target' => '_blank')) . ' ';
    }

    // 批量打印学⽣家庭证件
    public function actionPrintStudent()
    {
        $this->layout = "//layouts/print";
        $this->printFW = $this->branchObj->getPrintHeader();
        $ids = Yii::app()->request->getParam('ids','');
        $idList = explode(",",$ids);

        $regStudentsModel = RegStudents::model()->findAllByPk($idList);

        $childIds = array();

        foreach ($regStudentsModel as $val){
            $childIds[] = $val->childid;
        }

        $criteria = new CDbCriteria();
        $criteria->compare('reg_id', $this->regModel->id);
        $criteria->compare('childid', $childIds);
        $criteria->compare('status', 1);
        $criteria->compare('step', 9);
        $regExtraInfoModel = RegExtraInfo::model()->findAll($criteria);

        $info = array();
        foreach ($regExtraInfoModel as $key => $val){
            $info[$key]["childName"] = $val->childProfile->getChildName();
            $info[$key]["className"] = $val->childProfile->ivyclass->title;
            $safeData = json_decode($val->data);
            $data = array();
            if($safeData) {
                foreach ($safeData as $k => $parent) {
                    if ($parent) {
                        foreach ($parent as $item) {
                            $data[$k][] = RegExtraInfo::getOssImageUrl($item);
                        }
                    }
                }
            }
            $info[$key]["data"]["studentdata"] = isset($data["studentdata"]) ? $data["studentdata"]: array();
            $info[$key]["data"]["parentsdata"] = isset($data["parentsdata"]) ? $data["parentsdata"]: array();
            $info[$key]["data"]["vaccinedata"] = isset($data["vaccinedata"]) ? $data["vaccinedata"]: array();
            $info[$key]["data"]["insurancedata"] = isset($data["insurancedata"]) ? $data["insurancedata"]: array();
        }

        $type = array(
            'studentdata' => Yii::t('reg','Student Passport Photo'),
            'parentsdata' => Yii::t('reg','Mother Passport Photo'),
            'vaccinedata' => Yii::t('reg','Photo Copy of Vaccine Record'),
            'insurancedata' => Yii::t('reg','Commercial Medical Insurance Card Photo'),
        );

        $this->render('printStudentsIdCard', array(
            'info' => $info,
            'type' => $type,
        ));
    }
    public function actionPrintStudentAllInfo()
    {
        $this->layout = "//layouts/print";
        $this->printFW = $this->branchObj->getPrintHeader();
        $ids = Yii::app()->request->getParam('ids','');
        $idList = explode(",",$ids);

        $regStudentsModel = RegStudents::model()->findAllByPk($idList);

        $childIds = array();

        foreach ($regStudentsModel as $val){
            $childIds[] = $val->childid;
        }

        $criteria = new CDbCriteria();
        $criteria->compare('reg_id', $this->regModel->id);
        $criteria->compare('childid', $childIds);
        $criteria->compare('status', 1);
        $criteria->compare('step', array(8,9,10));
        $regExtraInfoModel = RegExtraInfo::model()->findAll($criteria);


        $criteria = new CDbCriteria();
        $criteria->compare('reg_id', $this->regModel->id);
        $criteria->compare('schoolid', $this->branchId);
        $criteria->compare('step', 8);
        $regConfigModel = RegConfig::model()->find($criteria);
        $baseContentCn = ($regConfigModel->content_cn) ? json_decode($regConfigModel->content_cn) : '';
        $feeCn = ($baseContentCn) ? nl2br(base64_decode($baseContentCn->safe)) : '';
        $baseContentCn = ($regConfigModel->content_en) ? json_decode($regConfigModel->content_en) : '';
        $feeEn = ($baseContentCn) ? nl2br(base64_decode($baseContentCn->safe)) : '';

        $info = array();
        foreach ($regExtraInfoModel as $key => $val){
            $info[$val->childid]["childName"] = $val->childProfile->getChildName();
            $info[$val->childid]["className"] = $val->childProfile->ivyclass->title;
            $safeData = json_decode($val->data);
            $data = array();
            if($safeData) {
                foreach ($safeData as $k => $parent) {
                    if ($parent) {
                        foreach ($parent as $item) {
                            $data[$k][] = RegExtraInfo::getThumbOssImageUrl($item);
                        }
                    }
                }
            }
            if($val->step == 8) {
                $info[$val->childid]["agreement"]["informationPhotoCn"] = $feeCn;
                $info[$val->childid]["agreement"]["informationPhotoEn"] = $feeEn;
                $info[$val->childid]["agreement"]["autograph"] = RegExtraInfo::getOssImageUrl($safeData->filedata);
            }
            if($val->step == 10) {
                $info[$val->childid]["information"]["basicsInfo"] = $val->childProfile;
                $info[$val->childid]["information"]["info"] = $safeData;
            }

        }
        //Yii::msg($info);
        $type = array(
            'studentdata' => Yii::t('reg','Student Passport Photo'),
            'parentsdata' => Yii::t('reg','Mother Passport Photo'),
            'vaccinedata' => Yii::t('reg','Photo Copy of Vaccine Record'),
            'insurancedata' => Yii::t('reg','Commercial Medical Insurance Card Photo'),
            'agreement' => Yii::t('reg','协议'),
            'information' => Yii::t('reg','学生资料'),
            'data' => Yii::t('reg','学生证件'),
        );

        $this->render('printStudentsAll', array(
            'info' => $info,
            'type' => $type,
        ));
    }
    public function actionExportChildsInfo(){
        $id = Yii::app()->request->getParam('id','');

        $regStudentsModel = RegStudents::model()->findAllByPk($id);
        $childIds = array();
        foreach ($regStudentsModel as $val){
            $childIds[] = $val->childid;
        }
        $country = Country::getData();

        $criteria = new CDbCriteria();
        $criteria->compare('reg_id', $this->regModel->id);
        $criteria->compare('childid', $childIds);
        $criteria->compare('status', 1);
        $criteria->compare('step', 10);
        $regExtraInfoModel = RegExtraInfo::model()->findAll($criteria);

        $data = array();
        $title = RegExtraInfo::getStepConfig();
        $data['title'] = $this->branchId . "_学生学籍信息.xlsx";
        $data['items'][0] = array('孩子姓名', '出⽣⽇期', '身份证件类型', '身份证号码或护照号码',
            '⾎型', '国籍', '⺠族', '出⽣所在地'
        , '籍贯', '户⼝性质', '是否为独生子女'
        , '户⼝所在地', '现住址', '出⽣监护⼈姓名'
        , '监护⼈身份证件号码', '监护⼈与⼉童关系', '⽗亲姓名'
        , '⽗亲联系⽅式', '⽗亲邮箱', '⺟亲姓名'
        , '⺟亲联系⽅式', '⺟亲邮箱', '过敏史'
        , '请在此说明除过敏外任何既往病史', '紧急联系⼈姓名1', '紧急联系⼈电话1','紧急联系⼈关系1', '紧急联系⼈姓名2', '紧急联系⼈电话2','紧急联系⼈关系2'
        , '如遇紧急情况，学生需要医疗服务，而学校无法联系到上述联系人，请在下列横线注明家长希望把学生送至的医院或诊所名。若家长不指定任何医院，学生将被送往三甲公立医院就诊。如果家长指定医院不在三甲公立医院，家长将要承担相关费用。', '您是否同意学校将您孩⼦的照⽚⽤于市场推⼴');
        if($regExtraInfoModel) {
            foreach ($regExtraInfoModel as $i => $item) {
                $childExtraInfo = json_decode($item->data);
                $childInfo = $item->childProfile;
                $array = array();
                $array[] = $childInfo->getChildName();
                $array[] = $childInfo->birthday_search;
                $array[] = isset($childExtraInfo->idCardType) ? $childExtraInfo->idCardType : "";
                $array[] = $childInfo->identity;
                $array[] = isset($childExtraInfo->bloodType) ? $childExtraInfo->bloodType : "";

                $array[] = $country[$childInfo->country];
                $array[] = isset($childExtraInfo->minzu) ? $childExtraInfo->minzu : "";
                $array[] = isset($childExtraInfo->placeOfBirth) ? $childExtraInfo->placeOfBirth : "";
                $array[] = isset($childExtraInfo->nativePlace) ? $childExtraInfo->nativePlace : "";
                $array[] = isset($childExtraInfo->residentType) ? $childExtraInfo->residentType : "";

                $array[] = isset($childExtraInfo->isOnlyChild) ? $childExtraInfo->isOnlyChild : "";
                $array[] = isset($childExtraInfo->residentAddress) ? $childExtraInfo->residentAddress : "";
                $array[] = isset($childExtraInfo->address) ? $childExtraInfo->address : "";;

                $array[] = isset($childExtraInfo->guardianName) ? $childExtraInfo->guardianName : "" ;
                $array[] = isset($childExtraInfo->guardianIdCard) ? $childExtraInfo->guardianIdCard : "";
                $array[] = isset($childExtraInfo->guardianEelationship) ? $childExtraInfo->guardianEelationship : "";

                $array[] = isset($childExtraInfo->fatherName) ? $childExtraInfo->fatherName : "";
                $array[] = isset($childExtraInfo->fatherTel) ? $childExtraInfo->fatherTel : "";
                $array[] = isset($childExtraInfo->fatherEmail) ? $childExtraInfo->fatherEmail : "";
                $array[] = isset($childExtraInfo->motherName) ? $childExtraInfo->motherName : "";
                $array[] = isset($childExtraInfo->motherTel) ? $childExtraInfo->motherTel : "";
                $array[] = isset($childExtraInfo->motherEmail) ? $childExtraInfo->motherEmail : "";

                $array[] = $childExtraInfo->allergy;
                $array[] = $childExtraInfo->medicalHistory;

                $array[] = $childExtraInfo->personName1;
                $array[] = $childExtraInfo->relationship1;
                $array[] = $childExtraInfo->contactNumber1;
                $array[] = $childExtraInfo->personName2;
                $array[] = $childExtraInfo->relationship2;
                $array[] = $childExtraInfo->contactNumber2;

                $array[] = $childExtraInfo->hospital;
                $array[] = $childExtraInfo->is_promote == 1 ? "是" : "否";
                $data['items'][$i + 1] = $array;
            }
        }

        $this->addMessage('state', 'success');
        $this->addMessage('data', $data);
        $this->showMessage();
    }

    // 获取侧边栏菜单
    public function getSubMenu()
    {
       $subMenu = array(
           array('label'=>Yii::t('user','标记学生'), 'url'=>"markstudent"),
           array('label'=>Yii::t('user','完成情况'), 'url'=>"process"),
           array('label'=>Yii::t('user','打印接送卡'), 'url'=>"printcardList"),
           array('label'=>Yii::t('user','注册配置'), 'url'=>"registconfig"),
           array('label'=>Yii::t('user','班车站点'), 'url'=>"carsitelist"),
           array('label'=>Yii::t('user','完成列表'), 'url'=>"studentsList"),
       );
       if($this->branchObj->type == 50){
           array_pop($subMenu);
       }

       return $subMenu;
    }

    public function yearList()
    {
        Yii::import('common.models.calendar.*');
        $startYearList = array();
        $criteria = new CDbCriteria();
        $criteria->compare('branchid', $this->branchId);
        $criteria->order = 'startyear desc';
        $calendarModel = CalendarSchool::model()->findAll($criteria);
        // 根据当前校园查询出所有得学年
        $startYearList = array();
        foreach ($calendarModel as $val){
            $yearEnd = $val->startyear  + 1;
            $startYearList[$val->yid] = $val->startyear . ' - ' . $yearEnd;
        }

        return $startYearList;
    }
}

?>
