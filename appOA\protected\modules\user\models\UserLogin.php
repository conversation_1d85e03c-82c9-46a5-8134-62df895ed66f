<?php

/**
 * LoginForm class.
 * LoginForm is the data structure for keeping
 * user login form data. It is used by the 'login' action of 'SiteController'.
 */
class UserLogin extends CFormModel
{
	public $username;
	public $password;
	public $rememberMe;
    public $status;
	public $redirectToOA=false;

	/**
	 * Declares the validation rules.
	 * The rules state that username and password are required,
	 * and password needs to be authenticated.
	 */
	public function rules()
	{
		return array(
			// username and password are required
			array('username, password', 'required'),
//			array('username', 'email'),
			// rememberMe needs to be a boolean
			array('rememberMe', 'boolean'),
			// password needs to be authenticated
			array('password', 'authenticate'),
		);
	}

	/**
	 * Declares attribute labels.
	 */
	public function attributeLabels()
	{
		return array(
			'rememberMe'=>Yii::t("user", "Remember Me"),
			'username'=>Yii::t("user", "Email"),
			'password'=>Yii::t("user", "Password"),
		);
	}

	/**
	 * Authenticates the password.
	 * This is the 'authenticate' validator as declared in rules().
	 */
	public function authenticate($attribute,$params)
	{
		if(!$this->hasErrors())  // we only want to authenticate when no input errors
		{
			$identity=new OAUserIdentity($this->username,$this->password);
			$identity->authenticate();
            $loginMessage = '';
			switch($identity->errorCode){
				case OAUserIdentity::ERROR_EMAIL_INVALID:
				case OAUserIdentity::ERROR_USERNAME_INVALID:
                default:
                    $loginMessage = Yii::t("user", "Email does not exist.");
					$this->addError("username", $loginMessage);
					break;
				case OAUserIdentity::ERROR_STATUS_NOTACTIV:
                case OAUserIdentity::ERROR_STATUS_BAN:
                    $loginMessage = Yii::t("user", "Your account is not activated.");
					$this->addError("username", $loginMessage);
					break;
				case OAUserIdentity::ERROR_IS_STAFF:
                    $loginMessage = Yii::t("user", "Your account is for staff.");
                    $this->addError("username", $loginMessage);
					break;
				case OAUserIdentity::ERROR_IS_NOT_STAFF:
                    $loginMessage = Yii::t("user", "Your account is for parent.");
                    $this->addError("username", $loginMessage);
					break;
				case OAUserIdentity::ERROR_PROFILE_MISSING:
                    $loginMessage = Yii::t("user", "User profile is missing.");
                    $this->addError("username", $loginMessage);
					break;
				case OAUserIdentity::ERROR_WRONG_CAMPUS:
                    $loginMessage = Yii::t("user", "You are not allowed to login from this site");
                    $this->addError("username", $loginMessage);
					break;
				case OAUserIdentity::ERROR_PASSWORD_INVALID:
                    $loginMessage = Yii::t("user", "Password is incorrect.");
                    $this->addError("password", $loginMessage);
					break;
                case OAUserIdentity::ERROR_NONE:
                    $duration=$this->rememberMe ? 3600*24*30 : 0; // 30 days
                    $login = Yii::app()->user->login($identity,$duration);
                    if(!$login){
                        $loginMessage = Yii::t("user", "Your account has no permission set.");
                        $this->addError("username", $loginMessage);
                    }else{

                    }
                    break;
			}
            Yii::app()->user->setFlash('loginMessage', $loginMessage);
		}
	}
}
