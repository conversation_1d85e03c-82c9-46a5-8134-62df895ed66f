<?php
/**
 * Message translations.
 *
 * This file is automatically generated by 'yiic message' command.
 * It contains the localizable messages extracted from source code.
 * You may modify this file by translating the extracted messages.
 *
 * Each array element represents the translation (value) of a message (key).
 * If the value is empty, the message is considered as not translated.
 * Messages that no longer need translation will have their translations
 * enclosed between a pair of '@@' marks.
 *
 * Message string can be used with plural forms format. Check i18n section
 * of the guide for details.
 *
 * NOTE, this file must be saved in UTF-8 encoding.
 *
 * @version $Id: $
 */
return array (
    'Logout' => '退出',
    'No' => '否',
    'Yes' => '是',
    'Add' => '增加           ',
    'User Center' => '用户中心',
    'My Profile' => '我的资料',
    'Leave & Overtime' => '休假及加班',
    'System Settings' => '系统设置',

    'Please sign in' => '请登录',
    'Email' => '电子邮件',
    'Password' => '用户密码',
    'Remember Me' => '记住登录',
    'Login' => '登录',

    'Edit Profile' => '编辑个人资料',

    'Basic Information' => '基本信息',
    'Detailed Information' => '详细信息',
    'Change Password' => '修改密码',
    'Upload Photo' => '上传头像',
    'Select a Photo' => '选取图片',

    'Daily Bulletin' => '每日公告',
    'Shortcuts' => '快速链接',
    'Lost Password?' => '忘记密码？',
    'Send Verification' => '发送验证信',

    'New password' => '新密码',
    'Repeat password' => '重复密码',

    'Old Version' => '旧版',

    'Toddler (Age 2-3)' => '学步班（2-3 岁）',
    'Casa (Age 3-6)' => '蒙台梭利混龄班（3-6岁）',
    'Kinder (Age 5-6)' => '大班（5-6岁）',
    'Grade 1' => '一年级',
    'Grade 2' => '二年级',
    'Grade 3' => '三年级',
    'Grade 4' => '四年级',
    'Grade 5' => '五年级',
    'Grade 6' => '六年级',
    'Grade 7' => '七年级',
    'Grade 8' => '八年级',
    'Grade 9' => '九年级',
    'Grade 10' => '十年级',
    // 学历
    'College' => '大专',
    'Undergraduate' => '本科',
    'Postgraduate' => '硕士',
    'Doctorate' => '博士',
    'Others' => '其它',
    'Status' => '状态',
    // 性别
    'Male' => '男',
    'Female' => '女',
    // 入学申请
    'Chinese Name' => '中文姓名',
    'English Name' => '英文姓名',
    'Gender' => '性别',
    'Date Of Birth' => '出生日期',
    'Nationality' => '国籍',
    'Passport/ID Card No.' => '护照号/身份证号',
    'Valid Date for Visa' => '签证有效期',
    'Valid Date for Resident Permit' => '居留许可证有效期',
    'Native Language' => '母语',
    'Other Languages' => '其他语言',
    'Requested Starting Date' => '入学日期',
    'Requested Starting Grade' => '入学年级',
    'Requires Bus Service' => '是否需要校车',
    'Home Address' => '家庭详细住址',
    'Requires Xueji (Chinese student only)' => '是否需要学籍',
    'IVY/Daystar Staff' => '是否为艾毅/启明星员工',
    // 学习经历
    'School History' => '学习经历',
    'School Name' => '学校名称',
    'City, Country' => '所在城市',
    'Dates Attended' => '就读时间',
    'Grade' => '年级',
    'Curriculum' => '课程',
    'Instruction Language' => '教学语言',
    // 兄弟姐妹信息
    'Sibling Information' => '兄弟姐妹信息',
    'Name' => '姓名',
    'Age' => '年龄',
    'Gender' => '性别',
    'Current School' => '目前就读学校',
    'Grade' => '年级',
    // 家长信息
    'Parent Information' => '家长信息',
    'Father/Guardian' => '父亲/监护人',
    'Mother/Guardian' => '母亲/监护人',
    'Language Spoken at Home' => '家庭交流使用的语言',
    'Employer' => '工作单位',
    'Position' => '职位',
    'Educational Background' => '学历',
    'Emergency Contact' => '紧急联系人',
    'Emergency Contact Phone' => '紧急联系人电话',
    'Emergency Contact Email' => '紧急联系人邮箱',


    'Date Of Bith' => '孩子生日',
    'Valid Date for Resident Permit' => '居留许可证有效期',
    'Sibling Information' => '兄弟姐妹信息',


    'Father Name' => '父亲名字',
    'Father Nation' => '父亲国籍',
    'Father Lang' => '父亲母语',
    'Father Home Lang' => '父亲家庭交流语言',
    'Father Employer' => '父亲工作单位',
    'Father Position' => '父亲职位',
    'Father Education' => '父亲学历',
    'Father Phone' => '父亲联系电话',
    'Father Email' => '父亲邮箱',
    'Mother Name' => '母亲名字',
    'Mother Nation' => '母亲国籍',
    'Mother Lang' => '母亲母语',
    'Mother Home Lang' => '目前家庭交流语言',
    'Mother Employer' => '母亲工作单位',
    'Mother Position' => '母亲职位',
    'Mother Education' => '母亲学历',
    'Mother Phone' => '母亲联系电话',
    'Mother Email' => '母亲邮箱',
    'Phone Number' => '紧急联系人电话',
    'Talent' => '特长',
    'Remark' => '备注',



    'Learning Difficulties' => '学习困难',
    'Tutorial' => '辅导课',
    'Export' => '导出',
    'Application date' => '申请日期',
    'Hu kou book or passport attachment' => '户口本或护照复印件',
    'Special education course attachment' => '特殊教育课程复印件',
    'Psychological consultation attachment' => '心里咨询附件',
    'Recommendation Form' => '老师推荐信',
    'Ever been fired' => '是否被开除过',
    'Health problems' => '健康问题',
    'Disability or disorder' => '残疾或障碍',
    'Copies of progress reports or transcripts for the last two years' => '近两年学业报告',




    'Requested Starting Gender' => '入学年级',
    'Child Avatar' => '学生头像',

    'Student Name At least one entry.' => '学生姓名至少填写一项',
    'Guardian information required: name, phone, email' => '监护人信息必填项：姓名、电话、邮箱',
    'child avatar cannot be blank' => '孩子照片不能为空',
    'progress reports or transcripts cannot be blank' => '学业报告不能为空',
    "child's and parents' valid passports and residence permits or the copy of child's and parents' Chinese Hukouben cannot be blank" => '学生及家长户口或护照扫描件不能为空',

    "Child Name" => "孩子姓名",
    "Bar code" => "条形码",
    "Child Sum" => "孩子每天总和",
    "TLI Website" => "TLI 网站",
    "Help Desk" => "IT/后勤服务",
    "Please select a filter" => "选择筛选条件",
    "By Class" => "按班级",
    "By Course" => "按课程",
    "By Teacher" => "按老师",
    "Excel Export" => "导出Excel表格",
    "Roster by Reporting Period" => "按报告周期统计",
    "Roster by Criterion" => "按评估标准统计",
    "Campus Summary Reports" => "报告总览",
    "Report Card Analytic Reports" => "评估报告统计分析",
    "Clear Selections" => "清空选择",
    "No teacher assigned" => "未分配老师",
    "Fetching data..." => "数据读取中...",
    "Clear Selection" => "清空表格",
    "Report is outdated, click to update"=>"数据有更新，点击按钮重新生成报告",
    "Generate Report"=>"生成报告",
    "Generating reports, please wait"=>"生成报告中，请稍候再试",
    "Coloring switch"=>"颜色高亮",
    "View Report" => "查看报告",
    "First Language" => "第一语言",
    "Home Language" => "家庭语言",
    "Usually Speak Language" => "孩子语言",
    "Time slot configuration & schedule result" => "时间段设置及预约结果",
    "The list only contains newly registered students with HLS investigation result." => "本列表仅显示填写过HLS调查的新注册学生名单",
    'Updated: ' => '更新：' ,
    'Wechat Binding' => '微信绑定',
    'Public Photo' => '对外照片',
    'Public photos are managed by HR department, please contact HR if you need to make adjustments.' => '对外照片由HR统一管理，若您有调整需求，请联系HR部门。',

    'New Enrollment - No Current Class Assignment' => '新注册，当前学年无班级' ,
    'Previously Enrolled - No Current Class Assignment' => '曾经就读过，当前学年无班级',
    'No class placement in current year. (outdated data included)' => '当前学年无班级（含过期数据）'
);
