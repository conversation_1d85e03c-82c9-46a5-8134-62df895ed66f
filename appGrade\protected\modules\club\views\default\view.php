<?php $articleTitle = $model->getTitle(); ?>
      <div class="row row-offcanvas row-offcanvas-left">
	  
        <div class="col-lg-3 col-md-3 col-sm-3 col-xs-6 sidebar-offcanvas sidebar" id="sidebar" role="navigation">
		  <div class="affix">
			<div class="list-group">
				<?php
				  foreach($articles as $art):
					  $class = 'list-group-item';
					  if($model->id == $art->id){
						  $class .= ' active';
					  }
					  $link = array('//club/default/view','id'=>$art->id);
					  echo CHtml::link($art->getTitle(),
						  $link,
						  array('class'=>$class)
					  );
				  endforeach;
				?>
			</div>
		  </div>
        </div><!--/span-->	  

        <div class="col-lg-9 col-md-9 col-sm-9 col-xs-12">
		
			<ol class="breadcrumb">
			  <li><?php echo CHtml::link('首页',array('//club/default/home'));?></li>
			  <li class="active"><?php echo $articleTitle;?></li>
			</ol>
			
			<div id="article-wrap">
                <div id="title">
                    <div class="bs-callout bs-callout-info"><h1><?php echo $articleTitle;?></h1></div>
                    <h4> <span class="glyphicon glyphicon-user"></span><span class="author"><?php echo $model->getTitle('author_text');?></span> • <small><?php echo Yii::t('club', '来自 :branch 校园', array(':branch' => $model->branch->title));?></small> </h4>
                </div>
                <div class="article-view">
                    <?php echo $content; ?>
                </div>

                <?php
                $pages = unserialize($model->page_num);
                $lpages = (count($pages) == 1) ? current($pages) : $pages[Yii::app()->language];

                if($lpages > 1):
                ?>
                <div id="pages" class="text-center">
                    <ul class="pagination">
                        <?php for($i=1; $i<=$lpages; $i++):?>
                        <li class="<?php echo $page==$i?'active':''?>">
                            <?php echo CHtml::link($i, array('/club/default/view', 'id'=>$model->id, 'page'=>$i));?>
                        </li>
                        <?php endfor;?>
                    </ul>
                </div>
                <?php endif;?>
				<div id="comments">
					<div class="bs-callout bs-callout-info">
						<h4>评论</h4>
						<p>
                            <form id="comment-content">
						    <textarea class="form-control" rows="4" name="ClubComment[content]" id="comment-box" placeholder="您怎么看？抱歉评论功能目前只对艾毅家长开放。"></textarea>
                            </form>
						</p>
                        <div class="alert alert-danger sr-only" id="comment-alert"></div>
						<div class="text-right">
						    <button type="button" class="btn btn-lg btn-primary" id="submit-comment">提交评论</button>
						</div>
						
						<hr>
						
						<div id="comment-list"></div>
					</div>
					
				</div>
			</div>
		
        </div><!--/span-->


      </div><!--/row-->

      <hr>
<!--回复模版-->
<div id="tpl-reply" class="sr-only">
    <form class="comment-reply">
        <div><textarea class="form-control" rows="2" name="ClubComment[content]" id="reply-box"></textarea></div>
        <p></p>
        <div class="alert alert-danger sr-only" id="reply-alert"></div>
        <div class="text-right">
            <input type="hidden" name="ClubComment[pid]" value="0" id="reply-pid"/>
            <button type="button" class="btn btn-primary" id="submit-reply">提交回复</button>
        </div>
    </form>
</div>
<script>
    $(function() {
        $('#article-wrap .article-view img').addClass('img-responsive shadow-img lazy');
        $("img.lazy").lazyload({
            effect : "fadeIn"
        });
    });

    $("#comment-box", '#comments').on('click', function(){
        <?php if(Yii::app()->user->isGuest):?>
            openLoginModel();
        <?php elseif(!Yii::app()->user->getState('clubUsername')):?>
            openClubUnameModel();
        <?php endif;?>
    });

    function loadComment(page)
    {
        $.ajax({
            type: 'get',
            url: '<?php echo $this->createUrl('/club/default/getComments', array('article_id'=>$model->id));?>',
            data: {page: page},
            beforeSend: function( xhr ){

            }
        }).done(function(data){
            $('#comment-list').html(data);
        });
    }
    loadComment(1);

    <?php if(!Yii::app()->user->isGuest && Yii::app()->user->getState('clubUsername')):?>
        $('#submit-comment', '#comments').on('click', function(){
            $.ajax({
                type: 'POST',
                url: '<?php echo $this->createUrl('/club/default/processComment', array('article_id'=>$model->id));?>',
                data: $('#comment-content').serialize(),
                dataType: 'json',
                beforeSend: function( xhr ){

                }
            }).done(function(data){
                if (data.state == 'success'){
                    $('#comment-alert').addClass('sr-only');
                    $('#comment-box').val('');
                    loadComment(1);
                }
                else{
                    $('#comment-alert').removeClass('sr-only').html(data.msg);
                }
            });
        });

        function reply(id)
        {
            var tpl = $('#tpl-reply').html();
            var cobj = $('#comment-'+id);
            if(cobj.attr('reply') == 'on'){
                cobj.attr('reply', 'off');
                $('#comment-'+id+' .comment-wrapper .comment-reply').remove();
            }
            else{
                $('.comment').attr('reply', 'off');
                $('.comment .comment-reply').remove();
                cobj.attr('reply', 'on');
                $('#comment-'+id+' .comment-wrapper').append(tpl);
                $('#comment-'+id+' .comment-wrapper').find('form').attr('id', 'reply-form');
                $('#comment-'+id+' .comment-wrapper').find('#reply-pid').val(id);
            }

            $('#submit-reply').on('click', function(){
                $.ajax({
                    type: 'POST',
                    url: '<?php echo $this->createUrl('/club/default/processComment', array('article_id'=>$model->id));?>',
                    data: $('#reply-form').serialize(),
                    dataType: 'json',
                    beforeSend: function( xhr ){

                    }
                }).done(function(data){
                    if (data.state == 'success'){
                        $('#comment-'+id+' .comment-wrapper #reply-alert').addClass('sr-only');
                        loadComment(1);
                    }
                    else{
                        $('#comment-'+id+' .comment-wrapper #reply-alert').removeClass('sr-only').html(data.msg);
                    }
                });
            });
        }
    <?php endif;?>
</script>
