<?php
class ExamListController extends BranchBasedController {
    public function createUrl($route, $params = array(), $ampersand = '&')
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        return parent::createUrl($route, $params, $ampersand);
    }

    public function init(){
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'campusOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site','Campus Workspace');
        //初始化选择校园页面
        $this->branchSelectParams['hideOffice'] = true;
        $this->branchSelectParams['urlArray'] = array('//mteaching/examList/index');
        $cs = Yii::app()->clientScript;
        $cs->registerCoreScript('jquery.ui');
        $cs->registerCssFile($cs->getCoreScriptUrl().'/jui/css/base/jquery-ui.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/element/vue.global.js');
        $cs->registerCssFile(Yii::app()->themeManager->baseUrl . '/base/js/element/index.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/element/index.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/xlsx.full.min.js');

    }

    public function actionIndex()
    {
        $this->render('index');
    }

    public function actionGetMAPExamList()
    {
        $requestUrl = 'examList/getMAPExamList';
        $this->remote($requestUrl, array(),'get');
    }

    public function actionAddSSConfig()
    {
        $requestUrl = 'examList/addSSConfig';
        $this->remote($requestUrl, array(
            'list'=>empty($_POST['list']) ? array() : $_POST['list']
        ),'post');
    }

    public function actionGetSSClassCourseTeacher()
    {
        $requestUrl = 'examList/getSSClassCourseTeacher';
        $grade =  Yii::app()->request->getParam('grade','');
        $this->remote($requestUrl, array(
            'grade'=>$grade
        ),'get');
    }

    public function actionGetESConfig()
    {
        $requestUrl = 'examList/getESConfig';
        $this->remote($requestUrl, array(),'get');
    }

    public function actionAddESConfig()
    {
        $requestUrl = 'examList/addESConfig';
        $this->remote($requestUrl, array(
            'list'=>empty($_POST['list']) ? array() : $_POST['list']
        ),'post');
    }

    public function actionGenerateMAPExamList()
    {
        $requestUrl = 'examList/generateMAPExamList';
        $this->remote($requestUrl, array(),'get');
    }

    public function actionGenerateLCExamList()
    {
        $requestUrl = 'examList/generateLCExamList';
        $this->remote($requestUrl, array(),'get');
    }
    /**
     * 可选老师搜索
     *
     * @return void
     */
    public function actionTeacherSearch()
    {
        $requestData = array(
            'teacher_name' => Yii::app()->request->getParam('searchString'),
        );
        $requestUrl = 'behavior/teacherSearch';
        $this->remote($requestUrl, $requestData,'post');
    }

    public function remote($requestUrl, $requestData = array(),$method)
    {
        if (empty($requestData['school_id'])) {
            $requestData['school_id'] = $this->branchId;
        }
        $res = CommonUtils::requestDsOnline2($requestUrl, $requestData,$method);
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('message', $res['msg']);
            $this->addMessage('data', $res['data']);
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->addMessage('data', $res['data']);
        }
        $this->showMessage();
    }
}