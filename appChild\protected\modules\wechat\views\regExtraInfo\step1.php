<?php echo $this->renderPartial('steps_header', array('currentStep'=>1)); ?>
<style>
    #signature-div {
        width: 100%;
        height: 100%;
        display: none;
        position: fixed;
        left: 0;
        top: 0;
        z-index: 999;
        background-color: white;
    }
    
    #signature-div p {
        position: absolute;
        bottom: 0;
        left: 50%;
        margin-left: -91px
    }
    
    .signature-pad {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: white;
    }
    
    .signCan {
        position: relative;
        -webkit-box-flex: 1;
        -ms-flex: 1;
        flex: 1;
        border: 1px solid #f4f4f4;
    }
    .signBody {
        position: relative;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -ms-flex-direction: column;
        flex-direction: column;
        font-size: 10px;
        width: 100%;
        height: 100%;
        max-height:300px;
        background-color: #fff;
        border-radius: 4px;
        padding: 16px;
    }
    #signature-data img{
        width:100px
    }
    #mapCon {
        width: 100%;
        height: 100%;
        position: fixed;
        left: 0;
        top: 0;
        z-index: 9999;
        display: none;
    }
    
    #mapCon .out {
        position: absolute;
        height: 30px;
        width: 100%;
        background: #000;
        line-height: 30px;
        text-align: right;
        padding-right: 10px;
        z-index: 999;
        opacity: 0.4;
        color: #fff
    }
    
    .mb0 {
        margin-bottom: 0
    }
    
    .mt15 {
        margin-top: 15px
    }
    
    html,
    body,
    #mapExample {
        width: 100%;
        height: 100%;
    }
    
    #startingFrom .radio:first-child {
        margin-top: 0;
    }
    
    .hidden {
        display: none;
    }
    .signText{
        padding:16px;
    }
</style>
<div class="container">
    <h5 class="htitle"><span class="fas fa-bus"></span> <?php echo $this->getStepTitle($this->step); ?></h5>

    <?php echo $this->renderPartial('_nouser_header'); ?>

    <?php $form = $this->beginWidget('CActiveForm', array(
            'id' => 'sep1-form',
            'enableAjaxValidation' => false,
            'htmlOptions' => array(
                // 'class'=>'J_ajaxForm form-horizontal',
                'role' => 'form'
            ),
        )); ?>
    <p>
        <?php echo Yii::t('reg', "Would you like to apply for the school bus service for your child?") ?>
    </p>
    <div >
        <?php echo CHtml::activeRadioButtonList($formModel, 'needBus', array(
                0 => Yii::t('reg', 'No, we will not need the school bus service and will arrange our own drop off and pick up.'),
                1 => Yii::t('reg', 'Yes. I would like to apply the school bus service for my child.'),
            ), array(
                'template' => '<div class="form-check">{input} {label}</div>',
                'separator' => '',
                'class'=>'form-check-input'
            )) ?>
    </div>
    <div id="journeyBox" class="hidden">
        <div class="text-center" style="color:#fd7e14">
            <small>    <span class="fa fa-ellipsis-h"></span></small><?php echo Yii::t('reg', "Sign up for school bus") ?>
            <small><span class="fa fa-ellipsis-h"></span></small>
        </div>
        <div class="form-group" id='startingFrom'>
            <h5 class="sub-title">
                    <span>1. <?php echo Yii::t('reg', "Method (Round Trip/Morning Only/Afternoon Only)") ?></span>
                </h5>
            <?php echo $form->radioButtonList($formModel, 'journey', array(
                    1 => Yii::t('reg', 'Two-way journey'),
                    2 => Yii::t('reg', 'One-way journey TO SCHOOL'),
                    3 => Yii::t('reg', 'One-way journey BACK HOME'),
                ), array(
                    'template' => '<div class="form-check">{input} {label}</div>',
                    'separator' => '',
                    'class'=>'form-check-input'
                )); ?>
        </div>
        <h5 class="sub-title">
                <span>2. <?php echo Yii::t('reg', "Pick-up/Drop-off Point") ?></span>
            </h5>
        <div class="selectLoca">
            <p>
                <?php $parking = $formModel->parking ? $formModel->parking : Yii::t('reg', "Please selec")?>
                <span class="ml-2" style="color:#fd7e14">
                        <small>
                            <span class="fa fa-map-marker-alt"></span> <?php echo Yii::t('reg', "My Pick-up/Drop-off Point:") ?><span id="stop_txt"><?php echo $parking;?></span>
                </small>
                </span>
                <?php echo $form->hiddenField($formModel,'parking'); ?>
                <?php echo $form->hiddenField($formModel,'customize'); ?>
            </p>
        </div>
        <p>
            <button class="btn btn-info btn-sm mt-1" type="button" id='selectMap'><span class="fa fa-search-location"></span> <?php echo Yii::t('reg', "Existing Pick-up/Drop-off Point") ?></button>
            <button class="btn btn-info btn-sm mt-1" type="button" id='addLocation'><span class="fa fa-plus-circle"></span> <?php echo Yii::t('reg', "Apply New Pick-up/Drop-off Point") ?></button>
        </p>
        <!--                 <div class="form-group">
                <?php echo $form->checkBox($formModel, 'themselves'); ?>
                <label for="themselves"><?php echo $form->labelEx($formModel, 'themselves'); ?></label>
            </div> -->
        <!-- 协议签字 -->
        <h5 class="sub-title">
                <span>3. <?php echo Yii::t('reg', "School Bus Agreement") ?></span>
            </h5>
        <div>
            <div class="edit-content">
                <?php
                    $content = RegConfig::getContent($this->regStudent->reg_id, $this->step);
                    if ($content) {
                        echo CommonUtils::autoLang(base64_decode($content['cn']['bus']), base64_decode($content['en']['bus']));
                    }
                 ?>
            </div>
            <div class="clearfix"></div>
            <div id="mapCon">
                <p class="out" onclick='out()'><?php echo Yii::t('bind', "Cancel") ?></p>
                <div id='mapExample'></div>
            </div>
            <button class="btn btn-info btn-sm" type="button" id="signature-save">
                    <span class="fa fa-pen"></span><?php echo Yii::t('reg', "Agree & sign") ?>
                </button>
            <div class="wrapper" id="signature-div">
                <div class="signBody">
                    <div class="signCan">
                        <canvas id="signature-pad" class="signature-pad"></canvas>
                    </div>
                </div>
                <div class="signText"><?php echo Yii::t('reg', "Rotate your phone horizontal for better view to sign") ?></div>
                <canvas id = "watermark" width = "100%"  height = "150px" style="display:none;"></canvas>
                <p>
                    <button type="button" class="btn btn-primary exit"><?php echo Yii::t('bind', "Confirm") ?></button>
                    <button type="button" class="btn btn-outline-primary clear"><?php echo Yii::t('reg', "Rewrite") ?></button>
                    <button type="button" class="btn btn-outline-dark dropOut"><?php echo Yii::t('bind', "Cancel") ?></button>
                </p>
            </div>
            <!--  <p>
                    <img src="<?php echo $formModel->filedata ? $formModel->filedata : ''?>" id="signature-data">
                </p> -->
            <p id="signature-data">
                <?php if($formModel->filedata): ?>
                <img src="<?php echo (strpos($formModel->filedata, 'base64') === false) ? Mims::OssUploadUrl($formModel->filedata) : $formModel->filedata; ?>">
                <?php endif; ?>
            </p>
            <input type="text" id="filedata" name="RegStep1Form[filedata]" value="<?php echo $formModel->filedata ?$formModel->filedata : ''?>" hidden="hidden">
        </div>
    </div>
    <div class="mt-3 pb-3">
        <button type="submit" id="submit_btn" class="btn btn-primary btn-lg btn-block examine"><?php echo Yii::t('reg', 'Submit') ?></button>
        <p class="text-black-50 mt-3 text-center examine_status">

        </p>
    </div>
    <?php $this->endWidget(); ?>
</div>
<div class="modal fade" id="addAddress">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title"><?php echo Yii::t('reg', "Apply New Pick-up/Drop-off Point") ?></h4>
                <button type="button" class="close" data-dismiss="modal">&times;</button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="form-group">
                        <label for="exampleInputName2"><?php echo Yii::t('reg', "Please enter") ?></label>
                        <div>
                            <span class="ml-2" style="color:#1eaaf1">
                        <small>
                            <span class="fa fa-info-circle"></span> <?php echo Yii::t('reg', "Format Example: Chaoyang District Rongke Olive City Phase III South Exit") ?>
                            </small>
                            </span>
                        </div>
                        <div>
                            <span class="ml-2" style="color:#fd7e14">
                        <small>
                            <span class="fa fa-map-marker-alt"></span> <?php echo Yii::t('reg', "New pick-up/drop-off point must meet a minimum three students requirement.  Please refer to Bus Handbook for more details.") ?></small>
                            </span>
                        </div>
                        <input type="text" class="form-control" id="community" placeholder="<?php echo Yii::t('reg', "Address+compound+pick up and drop off point") ?>">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal" onclick='cancelAdd()'><?php echo Yii::t('bind', "Cancel") ?></button>
                <button type="button" class="btn btn-primary" id='addSite'><?php echo Yii::t('reg', "Submit") ?></button>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript">
  window._AMapSecurityConfig = {
    serviceHost: "https://apps.ivyonline.cn/_AMapService",
  };
</script>
<script type="text/javascript" src="https://webapi.amap.com/maps?v=1.4.14&key=166c9d81f042ef0ae55c7a2c85e19a7e"></script>
<script>
    var carSiteList = <?php echo json_encode($carSiteList) ?>;
    if($('#RegStep1Form_needBus_1').attr("checked") == 'checked') {
        $('#journeyBox').show();
    }

    $('input[name="RegStep1Form[needBus]"]').click(function() {
        if($(this).val() == 1) {
            $('#journeyBox').show();
        } else {
            $('#journeyBox').hide();
        }
    });
    var scrollTops;
    // 点击退出全屏
    $('.exit').click(function() {
        var canvas = document.getElementById('signature-pad');
        var data = canvas.toDataURL("image/png");
        // $("#signature-data").attr('src', data);
        $("#signature-data").html('<img src=' + data + ' >')
        $("#filedata").val(data);
        $("#signature-data").show();
        $('#signature-div').hide();
        var width = canvas.width / 8
        var height = canvas.height / 8
        $('#signature-data img').height(height)
        $('#signature-data img').width(width)
        $('html').css({
            'overflow': 'initial',
            'position': 'initial'
        });
        $('html,body').scrollTop(scrollTops);
    })
    // 重写
    $('.clear').click(function() {
        signatureInit();
    })
    var canvas = document.getElementById('signature-pad');
    $('.dropOut').click(function(){
        $('#signature-div').hide();
        clea = canvas.getContext("2d");
        clea.clearRect(0,0,500,500);
        $('html').css({
            'overflow': 'initial',
            'position': 'initial'
        });
        $('html,body').scrollTop(scrollTops);
    })
    // 点击全名书写
    $('#signature-save').click(function() {
        //canvas全屏
        $('#signature-div').show();
        signatureInit();
    });
    // 初始化画板
    function signatureInit() {
        scrollTops = $(window).scrollTop();
        $('html').css({
            'overflow': 'hidden',
            'position': 'fixed'
        })
        ratio = Math.max(window.devicePixelRatio || 1, 1);
        canvas.width = canvas.offsetWidth * ratio;
        canvas.height = canvas.offsetHeight * ratio;
        canvas.getContext("2d").scale(ratio, ratio);
        var signaturePad = new SignaturePad(canvas);
        watermark(canvas);
    }

    function hengshuping() {
        if(window.orientation == 180 || window.orientation == 0) {
            if($('.wrapper').is(':visible')){
                clea = canvas.getContext("2d");
                clea.clearRect(0,0,500,500);
                setTimeout("signatureInit()",200);
            }
        }
        if(window.orientation == 90 || window.orientation == -90) {
            if($('.wrapper').is(':visible')){
                clea = canvas.getContext("2d");
                clea.clearRect(0,0,500,500);
                setTimeout("signatureInit()",200);
            }
        }
    }
    window.addEventListener("onorientationchange" in window ? "orientationchange" : "resize", hengshuping, false);
    // 绘制水印
    function watermark(canvas) {
        var date = new Date();
        var year = date.getFullYear();
        var month = date.getMonth() + 1;
        var strDate = date.getDate();
        if(month >= 1 && month <= 9) {
            month = "0" + month;
        }
        if(strDate >= 0 && strDate <= 9) {
            strDate = "0" + strDate;
        }
        var currentdate = year + month + strDate
        var cw = $('#watermark')[0];   
        cw.width = $(document).width();
        var ctx = cw.getContext("2d");   //返回一个用于在画布上绘图的环境
        
       
        ctx.fillStyle = "rgba(100,100,100,0.1)";
        if(window.orientation == 90 || window.orientation == -90) {
            ctx.font="30px 黑体";  
            ctx.rotate(-10*Math.PI/180);
            $('.signText').hide()
            ctx.fillText("<?php echo Yii::t('reg', "School Bus Agreement Signature") ?>" + currentdate + "",100, 130);
        } else if(window.orientation == 0 || window.orientation == 180) {
            ctx.font="20px 黑体";  
            ctx.rotate(-18*Math.PI/180);
             $('.signText').show()
           ctx.fillText("<?php echo Yii::t('reg', "School Bus Agreement Signature") ?>" + currentdate + "", 0, 130);
        }
        
        ctx.rotate('20*Math.PI/180');  //坐标系还原
        var crw =canvas;  
        ctxr = canvas.getContext("2d");
        ctxr.width = $(window).height();
        ctxr.clearRect(0,0,500,500);  //清除整个画布 
        var pat = ctxr.createPattern(cw, "repeat");    //在指定的方向上重复指定的元素  
        ctxr.fillStyle = pat;  
        ctxr.fillRect(0, 0, crw.width, crw.height);
    }
    
    //地图
    $('#selectMap').click(function() {
        $('#mapCon').show();
        $('.addAddress').hide();
        init()
        scrollTops = $(window).scrollTop();
        $('html').css({
            'overflow': 'hidden',
            'position': 'fixed'
        })

    });
    //初始化地图
    function init() {
        map = new AMap.Map('mapExample', {
            resizeEnable: true,
            zoom: 12,
            resizeEnable: true
        });
        AMap.plugin(['AMap.ToolBar', 'AMap.Scale'],
            function() {
                map.addControl(new AMap.Scale());
            });
        AMap.service('AMap.Geocoder', function() { //回调函数
            geocoder = new AMap.Geocoder({
                city: "北京", //城市，默认：“全国”
            });
        });
        AMap.plugin('AMap.Geolocation', function() {
            var geolocation = new AMap.Geolocation({
                enableHighAccuracy: true, //是否使用高精度定位，默认:true
                timeout: 10000, //超过10秒后停止定位，默认：5s
                buttonPosition: 'RB', //定位按钮的停靠位置
                buttonOffset: new AMap.Pixel(10, 20), //定位按钮与设置的停靠位置的偏移量，默认：Pixel(10, 20)
                zoomToAccuracy: true, //定位成功后是否自动调整地图视野到定位点

            });
            map.addControl(geolocation);
            geolocation.getCurrentPosition(function(status, result) {
                if(status == 'complete') {} else {
                    alert('<?php echo Yii::t('reg', "Location Pin Failed") ?>')
                }
            });
        });
        for(var i = 0; i < carSiteList.length; i++) {
            var marker = new AMap.Marker({
                icon: 'https://webapi.amap.com/theme/v1.3/markers/n/mark_b.png',
                offset: new AMap.Pixel(-13, -30),
            });
            marker.setPosition(new AMap.LngLat(carSiteList[i].longitude, carSiteList[i].latitude));
            marker.setMap(map);
            marker.content = "<p class='mt15'><b><?php echo Yii::t('reg', "Compound Name") ?></b><span>" + carSiteList[i].name_cn + "</span></p>" +
                "<p><b><?php echo Yii::t('reg', "Pick-up/Drop-off Point") ?></b><span>" + carSiteList[i].park_address_cn + "</span></p><p><button type='button' class='btn btn-primary btn-sm' onclick='showInfo(" + carSiteList[i].id + ")'><?php echo Yii::t('reg', "Select") ?></button></p>";
            marker.on('click', markerClick);
        }
        map.setFitView()
    }
    var infoWindow = new AMap.InfoWindow({
        offset: new AMap.Pixel(0, -30)
    }); //信息窗口
    //点击点出现详情
    function markerClick(e) {
        infoWindow.setContent(e.target.content);
        infoWindow.open(map, e.target.getPosition());
    }

    //信息窗口事件
    function showInfo(id) {
        for(var i = 0; i < carSiteList.length; i++) {
            if(carSiteList[i].id == id) {
                // $('.selectLoca').show()
                $('.selectLoca p #stop_txt').text(carSiteList[i].name_cn + carSiteList[i].park_address_cn)
                $('#RegStep1Form_parking').val(carSiteList[i].name_cn + carSiteList[i].park_address_cn)
                $('#RegStep1Form_customize').val('1')
                
                $('#mapCon').hide();
                $('html').css({
                    'overflow': 'initial',
                    'position': 'initial'
                });
                $('html,body').scrollTop(scrollTops);

            }
        }
    }
    //退出地图
    function out() {
        map && map.destroy();
        $('#mapCon').hide();
        $('html').css({
            'overflow': 'initial',
            'position': 'initial'
        });
        $('html,body').scrollTop(scrollTops);
    }
    $('#addLocation').click(function() {
        //$('.addAddress').show();
        $('html').css({
            'overflow': 'initial',
            'position': 'initial'
        });
        $('html,body').scrollTop(scrollTops);
        $('#addAddress').modal('show')
    });
    $('#addSite').click(function() {
        if($('#community').val() != '') {
            $('.selectLoca p #stop_txt').text($('#community').val())
            $('#RegStep1Form_parking').val($('#community').val())
            $('#RegStep1Form_customize').val('2')
            // $('.selectLoca').show()
            $('#addAddress').modal('hide')
            $('html').css({
                'overflow': 'initial',
                'position': 'initial'
            });
            $('html,body').scrollTop(scrollTops);
        } else {
            alert('<?php echo Yii::t('reg', "Compound Name") ?>')
        }
    });

    function cancelAdd() {
        $('#community').val('')
        $('.addAddress').hide();
    }
</script>