<?php
$H = array('08' => '08', '09' => '09', 10 => 10, 11 => 11, 12 => 12, 13 => 13, 14 => 14, 15 => 15, 16 => 16, 17 => 17, 18 => 18, 19 => 19, 20 => 20);
$S = array('00' => '00', '05' => '05', 10 => 10, 15 => 15, 20 => 20, 25 => 25, 30 => 30, 35 => 35, 40 => 40, 45 => 45, 50 => 50, 55 => 55);
$HourS = array(
    'start' => array(
        'stat' => '08',
        'end' => '00',
    ),
    'end' => array(
        'stat' => '09',
        'end' => '00',
    )
);
?>
<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span>
    </button>
    <h4 class="modal-title" id="exampleModalLabel"><?php echo $model->id ? '更新：' : '添加：'; ?></h4>
</div>
<?php $form = $this->beginWidget('CActiveForm', array(
    'id' => 'course-form',
    'enableAjaxValidation' => false,
    'htmlOptions' => array('class' => 'J_ajaxForm form-horizontal', 'role' => 'form'),
)); ?>
<div class="modal-body">
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'title_cn'); ?></label>

        <div class="col-xs-10">
            <?php echo $form->textField($model, 'title_cn', array('maxlength' => 255, 'class' => 'form-control')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'title_en'); ?></label>

        <div class="col-xs-10">
            <?php echo $form->textField($model, 'title_en', array('maxlength' => 255, 'class' => 'form-control')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'vendor'); ?></label>

        <div class="col-xs-10">
            <?php echo $form->textField($model, 'vendor', array('maxlength' => 255, 'class' => 'form-control')); ?>
            <p class="text-info"><?php echo Yii::t('asainvoice', '第三方培训机构请写出机构名称，老师请注明老师名字'); ?></p>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'unit_price'); ?></label>

        <div class="col-xs-10">
            <?php echo $form->textField($model, 'unit_price', array('maxlength' => 255, 'class' => 'form-control')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'default_count'); ?></label>

        <div class="col-xs-10">
            <?php echo $form->textField($model, 'default_count', array('maxlength' => 255, 'class' => 'form-control')); ?>
        </div>
    </div>
    <!-- 开课时间 -->
    <div class="form-group">
        <label class="col-sm-2 control-label required">上课时间 * </label>

        <div class="col-sm-10" id="timelist">
            <label>已增加的时间段: </label>

            <div class="form-inline mb5" id="timelistin">
                <?php if ($checkweek): ?>
                    <?php foreach ($checkweek as $key => $_week): ?>
                        <?php foreach ($_week as $k => $week) {
                            ?>
                            <div class="form-inline mb5 ml5 has_been_added" _value="<?php echo $key; ?>">
                                <label class="mr5"><?php echo $weekday[$key]; ?></label>
                                <label class="">开始</label>
                                <input type="text" class="form-control starttime"
                                       name="period[<?php echo $key; ?>][<?php echo $k; ?>][startTime]"
                                       _value="<?php echo $week['startTime']; ?>" value="<?php echo $week['startTime']; ?>"
                                       readonly="">
                                <label class="">结束</label>
                                <input type="text" class="form-control"
                                       name="period[<?php echo $key; ?>][<?php echo $k; ?>][endTime]"
                                       value="<?php echo $week['endTime']; ?>" readonly="">
                                <button class="btn btn-danger btn-sm ml5 removetimes">
                                    <i class="glyphicon glyphicon-remove"></i>
                                </button>
                            </div>
                        <?php } ?>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
            <hr/>
            <div class="form-inline" id="">
                <?php
                echo CHtml::dropDownList('', 1, $weekday, array('class' => "form-control mr10", 'id' => 'selectweek'));
                ?>
                <label>开始</label>
                <?php
                echo CHtml::dropDownList('', $HourS['start']['stat'], $H, array('class' => "form-control mr10", 'id' => 'courseready_s'));
                echo CHtml::dropDownList('', $HourS['start']['end'], $S, array('class' => "form-control mr10", 'id' => 'courseready_e'));
                echo " <label>结束</label> ";
                echo CHtml::dropDownList('', $HourS['end']['stat'], $H, array('class' => "form-control mr10", 'id' => 'courseend_s'));
                echo CHtml::dropDownList('', $HourS['end']['end'], $S, array('class' => "form-control mr10", 'id' => 'courseend_e'));
                ?>
                <a href="JavaScript:void(0)" onclick="AddTime('timelistin')" class="btn btn-info">增加时间段</a>
            </div>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'status'); ?></label>

        <div class="col-xs-10">
            <?php echo $form->dropDownList($model, 'status', array(1 => '有效', 0 => '无效'), array('class' => 'form-control')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'gid'); ?></label>

        <div class="col-xs-10">
            <?php echo $form->dropDownList($model, 'gid', $groupArray, array('class' => 'form-control')); ?>
        </div>
    </div>
</div>
<div class="modal-footer">
    <span id="start_fail_info" class="pull-left text-warning" style="display: none;"><i
            class="glyphicon glyphicon-remove text-warning"></i> 当天该时间段已有课程</span>
    <button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global', 'Submit'); ?></button>
    <button type="button" class="btn btn-default"
            data-dismiss="modal"><?php echo Yii::t('global', 'Cancel'); ?></button>
</div>
<?php $this->endWidget(); ?>

<script>
    var kindex = 9999;
    function AddTime(date) {
        var week = $('#timelist #selectweek').find("option:selected").text();
        var weekval = $('#timelist #selectweek').find("option:selected").val();
        var courseready_s = $('#courseready_s').val();
        var courseready_e = $('#courseready_e').val();
        var courseend_s = $('#courseend_s').val();
        var courseend_e = $('#courseend_e').val();
        var startflag = true;

        $.each($('.has_been_added'), function (i, n) { //判断当天开始时间是否重复
            if ($(n).attr('_value') == weekval) {
                if ($(n).find('.starttime').attr('_value') === (courseready_s + ":" + courseready_e)) {
                    startflag = false;
                }
            }
        });

        if (startflag) {
            $('#start_fail_info').hide();
            $("#" + date).append('<div class="form-inline mb5 ml5 has_been_added" _value="' + weekval + '" ><label class="mr10">' + week + '</label><label class="mr5">开始</label><input type="text" class="form-control starttime" name="period[' + weekval + '][' + kindex + '][startTime]" _value="' + courseready_s + ':' + courseready_e + '"value="' + courseready_s + ":" + courseready_e + '" readonly/><label class="mr5 ml5">结束</label><input type="text" class="form-control" name="period[' + weekval + '][' + kindex + '][endTime]" value="' + courseend_s + ":" + courseend_e + '" readonly/><button class="btn btn-danger btn-sm ml5 removetimes"><i class="glyphicon glyphicon-remove"></i></button></div>');
            kindex++;
        } else {
            $('#start_fail_info').show();
        }

    }
    $("body").on("click", ".removetimes", function (e) {
        $(this).parent().remove();
        kindex--;
    })

</script>