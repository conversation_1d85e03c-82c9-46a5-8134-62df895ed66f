<style>
    .tableShow  td, .tableShow  th {
        border-bottom: 1px solid #DDDDDD;
        border-top:none !important;
        background:#fff;
        width: 135px;
        vertical-align: middle !important;
    }
    thead tr th {
        background: #FAFAFA;
    }
     .tableShow  th:first-child,
     .tableShow   td:first-child{
        position: sticky;
        left: 0;
        background: #FAFAFA;
        right: 0px;
        border-left: 1px solid #DDDDDD ;
        border-right:none ;
        width: 140px;
        box-shadow: 4px 0px 6px 0px rgba(0,0,0,0.0600);
    }
    .tableShow  th:last-child,
    .tableShow  td:last-child {
        position: sticky;
        left: 0;
        background: #FAFAFA;
        right: 0px;
        border-right:none ;
        width: 100px;
        box-shadow: -4px 0px 6px 0px rgba(0,0,0,0.0600);
        border-right: 1px solid #DDDDDD;
     }
    .tableShow  th:last-child,
    .tableShow  th:first-child {
        z-index: 3;
        background: #FAFAFA;
    }
    .overflow-out{
        overflow: auto;
    }
    .flex-center{
        display: flex;
        align-items: center;
    }
    .min-hei-34{
        height: auto;
        min-height: 34px;
    }
    .line-norm{
        line-height: normal
    }
</style>
<div id='container' v-cloak>
    <div class="" v-if='dataList.products_cat'>
        <div class="mb20 flex">
            <div>
                <img :src="dataList.products_cat.img" class='image' alt="">
            </div>
            <div class='flex1 ml10'>
                <div class='font18 mt15'><label>{{dataList.products_cat.title}}</label> </div>
                <div class='mt5'><span class="label label-default">等待确认：{{dataList.total_count}}</span></div>
            </div>
        </div>
       <div class='grey mb5'>
            <div class='form-inline'>
                <div class="form-group mr10">
                    <input type="text" class="form-control"  placeholder="孩子姓名" v-model='child_name' @keyup.enter='searchInit()'>
                </div>
                <div class="form-group mr10">
                    <select class="form-control" v-model='class_id'>
                        <option value="" selected>全部班级</option>
                        <option v-for='(list,index) in class_list' :value='list.classid'>{{list.title}}</option>
                    </select>
                </div>
                <div class="form-group mr10">
                    <input type="text" class="form-control datepicker" id="startdate"
                                   placeholder="<?php echo Yii::t('user', '开始日期') ?>"
                                    >
                </div>
                <div class="form-group mr10">
                <input type="text" class="form-control datepicker" id="enddate"
                                   placeholder="<?php echo Yii::t('user', '结束日期') ?>"
                                    >
                </div>
                <button type="submit" class="btn btn-primary pull-right ml15" :disabled='disabled' @click='searchInit()'>搜索</button>
                <button type="submit" class="btn btn-default pull-right" :disabled='disabled' @click='reset()' >重置</button>
            </div>
        </div>
        <ul class="nav nav-tabs mt20" role="tablist">
          <li role="presentation" class="active"><a href="#confirmation" aria-controls="home" role="tab" data-toggle="tab"  @click='init()'>等待确认</a></li>
          <li role="presentation"><a href="#statistics" aria-controls="profile" role="tab" data-toggle="tab" @click='stocking()'>备货统计</a></li>
        </ul>
        <div class="tab-content">
            <div role="tabpanel" class="tab-pane fade in active" id="confirmation">
                <div  v-if='dataList.list.length!=0'>
                    <div class='flex mt15 pt10'>
                        <div class='flex1'>
                            <span class='selectNum'>已选择{{checkList.length}}项</span>
                        </div>
                        <div>
                            <button type="submit" class="btn btn-default mr10" @click='exportData' :disabled='disabled'>导出</button>
                            <button type="submit" class="btn btn-primary" @click='showConfirm("all")' :disabled='disabled'>确认订单</button>
                        </div>
                    </div>
                    <div class='table_wrap scrollbar mt20  mb20' >
                        <table class="table tableShow" id='table' >
                            <thead>
                                <tr>
                                    <th width="100" style='z-index:2;vertical-align: middle !important; width:110px;' class='font14 '>
                                        <label class="checkbox-inline">
                                            <input type="checkbox" v-model='checkAll' @click='allChecked($event)'><?php echo Yii::t("ptc", "订单号");?>
                                        </label>
                                    </th>
                                    <th  class="">
                                        <div class='relative'>
                                            <span class='mr10'>学生</span>
                                            <span class='top' :class='sort=="childid.asc"?"topActive":""' @click='sort=="childid.asc"?sort="":sort="childid.asc";init()'></span>
                                            <span class='bottom' :class='sort=="childid.desc"?"bottomActive":""'  @click='sort=="childid.desc"?sort="":sort="childid.desc";init()'></span>
                                        </div>
                                    </th>
                                    <th  class="">
                                        <div class='relative'>
                                            <span class='mr10'>账单班级</span>
                                        </div>
                                    </th>
                                    <th  class=""  style=' width:215px;'>
                                        <div class='relative'>
                                            <span class='mr10'>商品</span>
                                            <span class='top' :class='sort=="pid.asc"?"topActive":""' @click='sort=="pid.asc"?sort="":sort="pid.asc";init()'></span>
                                            <span class='bottom' :class='sort=="pid.desc"?"bottomActive":""'  @click='sort=="pid.desc"?sort="":sort="pid.desc";init()'></span>
                                        </div>
                                    </th>
                                    <th  class="" style=' width:66px;'>
                                        <div class='relative'>
                                            <span class='mr10'>金额</span>
                                            <span class='top' :class='sort=="sum_price.asc"?"topActive":""' @click='sort=="sum_price.asc"?sort="":sort="sum_price.asc";init()'></span>
                                            <span class='bottom' :class='sort=="sum_price.desc"?"bottomActive":""'  @click='sort=="sum_price.desc"?sort="":sort="sum_price.desc";init()'></span>
                                        </div>
                                    </th>
                                    <th  class="" style=' width:76px;'>
                                        <div class='relative'>
                                            <span class='mr10'>商品数量</span>
                                            <span class='top' :class='sort=="num.asc"?"topActive":""' @click='sort=="num.asc"?sort="":sort="num.asc";init()'></span>
                                            <span class='bottom' :class='sort=="num.desc"?"bottomActive":""'  @click='sort=="num.desc"?sort="":sort="num.desc";init()'></span>
                                        </div>
                                    </th>
                                    <th  class=""  style=' width:126px;'>
                                        下单时间
                                    </th>
                                    <th  class="" style=' width:126px;'>
                                        <div class='relative'>
                                            <span class='mr10'>操作时间</span>
                                            <span class='top' :class='sort=="updated_time.asc"?"topActive":""' @click='sort=="updated_time.asc"?sort="":sort="updated_time.asc";init()'></span>
                                            <span class='bottom' :class='sort=="updated_time.desc"?"bottomActive":""'  @click='sort=="updated_time.desc"?sort="":sort="updated_time.desc";init()'></span>
                                        </div>
                                    </th>
                                    <th  class="" style='z-index:2;vertical-align: middle !important; width: 80px;' class='font14 '>
                                        操作
                                    </th>
                                </tr>
                            </thead>
                            <tbody  v-if='dataList.list.length!=0'>
                                <tr  v-for='(list,index) in dataList.list'>
                                    <td  class=''>
                                        <label class="checkbox-inline">
                                            <input type="checkbox"  :value="list.id" v-model='checkList' @click='Checked()'> {{list.order_id}}
                                        </label>
                                    </td>
                                    <td  class="">
                                        <div>{{list.child_name}}</div>
                                        <div>{{list.child_class}}</div>
                                    </td>
                                    <td  class="">
                                        {{list.class_name}}
                                    </td>
                                    <td  class="">
                                        <p>{{list.product_title}}</p>
                                        <div>{{list.attr1_title}} {{list.attr2_title}}</div>
                                    </td>
                                    <td  class="">
                                        {{list.sum_price}}
                                    </td>
                                    <td  class="">
                                        {{list.num}}
                                    </td>
                                    <td  class="">
                                        <div>{{list.order_date}}</div>
                                        <div>{{list.order_time}}</div>
                                    </td>
                                    <td  class="">
                                        <div>{{list.updated_date}}</div>
                                        <div>{{list.updated_time}}</div>
                                    </td>
                                    <td  class="">
                                        <a href="javascript:;" class='ml10' @click='showConfirm(list)'>确认订单</a>
                                    </td>
                                </tr>
                            </tbody>
                            <tbody  v-else>
                                <tr >
                                    <td colspan="9" class='noBg text-center'><span class="empty">没有找到数据.</span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class='flex' v-if='dataList.list.length!=0'>
                        <div  class='flex1'>
                            <span>共 {{dataList.total_count}} 项</span>
                            <span>总金额：{{dataList.total_amount}}</span>
                        </div>
                        <div>
                            <nav aria-label="Page navigation"   class="text-left ml10" v-if='dataList.page_count>1'>
                                <ul class="pagination">
                                    <li v-if='currentPage >1'>
                                        <a href="javascript:void(0)" @click="plus(1)" aria-label="Previous">
                                            <span aria-hidden="true">&laquo;</span>
                                        </a>
                                    </li>
                                    <li v-else class="disabled">
                                        <a aria-label="Previous">
                                            <span aria-hidden="true">&laquo;</span>
                                        </a>
                                    </li>
                                    <li class="previous" v-if='currentPage >1'>
                                        <a href="javascript:void(0)" @click="prev">‹</a>
                                    </li>
                                    <li class="disabled" v-else>
                                        <a href="javascript:void(0)">‹</a>
                                    </li>
                                    <li v-for='(data,index) in page_count' :class="{ active:data==currentPage }">
                                        <a href="javascript:void(0)" @click="plus(data)">{{data}}</a>
                                    </li>
                                    <li class="previous" v-if='currentPage <CopyPage'>
                                        <a href="javascript:void(0)" @click="next">›</a>
                                    </li>
                                    <li class="previous disabled" v-else>
                                        <a href="javascript:void(0)">›</a>
                                    </li>
                                    <li v-if='currentPage < CopyPage'>
                                        <a href="javascript:void(0)" @click="plus(CopyPage)" aria-label="Next">
                                            <span aria-hidden="true">&raquo;</span>
                                        </a>
                                    </li>
                                    <li v-else class="disabled">
                                        <a aria-label="Next">
                                            <span aria-hidden="true">&raquo;</span>
                                        </a>
                                    </li>
                                </ul>
                            </nav>
                        </div>
                    </div>
                </div>
                <div v-else>
                    <el-empty description="暂无数据"></el-empty>
                </div>
            </div>
            <div role="tabpanel" class="tab-pane fade" id="statistics">
                <div  v-if='dataList.list.length!=0'>
                    <div class='flex mt15 pt10'>
                        <div class='flex1'>
                        </div>
                        <div>
                            <button type="submit" class="btn btn-primary mr10" @click='exportStocking'>导出表格</button>
                            <?php if($this->branchId != 'BJ_DS'){?>
                            <button type="submit" class="btn btn-primary mr10" @click='printPrepare'>打印备货单</button>
                            <?php }?>
                        </div>
                    </div>
                    <table class="table mt20 border" id='stocking' >
                        <thead>
                            <tr>
                                <th  class="">
                                    商品
                                </th>
                                <th  class="">
                                    颜色
                                </th>
                                <th  class="">
                                    尺寸
                                </th>
                                <th  class="">
                                    数量
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr  v-for='(list,index) in stockingList'>
                                <td  class="">
                                    {{list.title}}
                                </td>
                                <td  class="">
                                    {{list.color}}
                                </td>
                                <td  class="">
                                    {{list.size}}
                                </td>
                                <td  class="">
                                    {{list.num}}
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div v-else>
                    <el-empty description="暂无数据"></el-empty>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="confirmModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
        <div class="modal-dialog modal-sm" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("newDS", "确认订单");?></h4>
                </div>
                <div class="modal-body">
                    确认订单
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                    <button type="button" class="btn btn-primary" @click='confirmData()' :disabled='disabled'><?php echo Yii::t("newDS", "确认订单");?></button>
                </div>
            </div>
        </div>
    </div>
    <!--导出备货单所需要填写的数据-->
    <div class="modal fade" id="confirmModalPrepare" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("newDS", "确认打印信息");?></h4>
                </div>
                <div class="modal-body overflow-out">
                    <div class="form-group overflow-out flex-center">
                        <label class="col-xs-2 control-label line-norm">甲方</label>
                        <div class="col-xs-10 min-hei-34">
                            <input type="text" class="form-control" id="party_a" placeholder="甲方" v-model="prepareExtra.party_a">
                        </div>
                    </div>
                    <div class="form-group overflow-out flex-center">
                        <label class="col-xs-2 control-label line-norm">乙方</label>
                        <div class="col-xs-10 min-hei-34">
                            <input type="text" class="form-control" id="party_b" placeholder="乙方"  v-model="prepareExtra.party_b">
                        </div>
                    </div>
                    <div class="form-group overflow-out flex-center">
                        <label class="col-xs-2 control-label line-norm">交货日期</label>
                        <div class="col-xs-10 min-hei-34">
                            <input type="text" class="form-control" id="delivery_detail" placeholder="交货日期"  v-model="prepareExtra.time">
                        </div>
                    </div>
                    <div class="form-group overflow-out flex-center">
                        <label class="col-xs-2 control-label line-norm">交货地点</label>
                        <div class="col-xs-10 min-hei-34">
                            <input type="text" class="form-control" id="delivery_detail" placeholder="交货地点"  v-model="prepareExtra.address">
                        </div>
                    </div>
                    <div class="form-group overflow-out flex-center">
                        <label class="col-xs-2 control-label line-norm">校园验收人联系电话</label>
                        <div class="col-xs-10 min-hei-34">
                            <input type="text" class="form-control" id="acceptor_detail" placeholder="校园验收人联系电话"  v-model="prepareExtra.acceptor_detail">
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                    <button type="button" class="btn btn-primary" @click='printPDF()'><?php echo Yii::t("newDS", "预览");?></button>
                </div>
            </div>
        </div>
    </div>
</div>
    <script>
        var childBaseUrl = '<?php echo $this->createUrl('//child/index/index')."&childid=";?>';
         var container = new Vue({
        el: "#container",
        data: {
            dataList:{},
            class_list:[],
            child_name:'',
            class_id:'',
            startdate:'',
            enddate:'',
            stockingList:{},
            prepareExtra:{
                party_a:'',
                party_b:'',
                time:'',
                address:'',
                acceptor_detail:''
            },
            checkAll:false,
            checkList:[],
            refundData:{},
            currentPage:1,
            confirmList:{},
            exchangeList:{
                child:{},
                list:{},
                attr:{}
            },
            CopyPage:0,
            page_count:[],
            disabled:false,
            sort:'',
            pcid:'<?php echo $_GET["pcid"]?>',
            state:'<?php echo $_GET["state"]?>',
            status:'<?php echo $_GET["status"]?>',
        },
        watch:{},
        created:function(){
            this.init('page')
        },
        mounted(){
             $('.datepicker').datepicker({
                 changeMonth: true,
                 changeYear: true,
                 dateFormat: 'yy-mm-dd'
             });
         },
        methods:{
            filterSize(list){
                var data=[]
                this.exchangeList.attr.forEach((item,index) => {
                   if(item.color==list){
                        data=item.attr
                   }
                })
                return data
            },
            pagesSize(type){
                var _this = this;
                if(_this.currentPage<10){
                    if(_this.CopyPage>=10){
                        _this.page_count=[1,2,3,4,5,6,7,8,9,10]
                    }else{
                        var numPage=[]
                        for(var i=1;i<=_this.CopyPage;i++){
                            numPage.push(i)
                        }
                        _this.page_count=numPage
                    }
                }else if(_this.currentPage<=_this.CopyPage){
                    if(_this.CopyPage-_this.currentPage>=4){
                        var minPage=_this.currentPage-5
                        var maxPage=_this.currentPage+4
                    }else{
                        var minPage=_this.currentPage-(9-((_this.CopyPage-_this.currentPage)))
                        var maxPage=_this.currentPage+(_this.CopyPage-_this.currentPage)
                    }
                    var numPage=[]
                    for(var i=minPage;i<=maxPage;i++){
                        numPage.push(i)
                    }
                    _this.page_count=numPage
                }
            },
            next(){
                var _this = this;
                _this.currentPage = Number(this.currentPage) + 1
                this.init()
                this.pagesSize()
            },
            prev() {
                var _this = this;
                _this.currentPage = Number(this.currentPage) - 1
                this.init()
                this.pagesSize()
            },
            plus(index) {
                var _this = this;
                _this.currentPage = Number(index)
                this.init()
                this.pagesSize()

            },
            initPage(){
                var _this = this;
                _this.CopyPage=JSON.parse(JSON.stringify(_this.dataList.page_count))
                if(_this.CopyPage>=10){
                    _this.page_count=[1,2,3,4,5,6,7,8,9,10]
               }else{
                    var numPage=[]
                    for(var i=1;i<=_this.CopyPage;i++){
                        numPage.push(i)
                    }
                    _this.page_count=numPage
               }
            },
            reset(){
                this.child_name=''
                this.class_id=''
                this.startdate=''
                this.enddate=''
                this.disabled=true
                $('#startdate').val('')
                $('#enddate').val('')
                this.currentPage=1
                this.init('page')
            },
            pageSize(index){
                this.currentPage=index
                this.init()
            },
            searchInit(){
                this.currentPage=1
                this.init('page')
            },
            init(page){
                var that=this
                this.disabled=true
                $.ajax({
                    url: '<?php echo $this->createUrl("getProductOrder") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        status:20,
                        "search_input[child_name]":this.child_name,
                        "search_input[class_id]": this.class_id,
                        "search_input[startdate]":this.startdate,
                        "search_input[enddate]": this.enddate,
                        page:this.currentPage,
                        sort:this.sort
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.dataList=data.data
                            that.class_list=data.data.class_list
                            if(page){
                                that.initPage()
                            }
                            that.$nextTick(()=>{
                                $("#startdate").datepicker({
                                    dateFormat: "yy-mm-dd ",
                                });
                                $( "#startdate").on('change', function () {
                                    container.startdate = $('#startdate').val();
                                });
                                $("#enddate").datepicker({
                                    dateFormat: "yy-mm-dd ",
                                });
                                $( "#enddate").on('change', function () {
                                    container.enddate = $('#enddate').val();
                                });
                            })
                        } else {
                            head.dialog.alert(data.message);
                        }
                        that.disabled=false
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                        that.disabled=false
                    }
                })
            },
            stocking(){
                var that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("getPrepareProduct") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        status:20,
                        "search_input[child_name]":this.child_name,
                        "search_input[class_id]": this.class_id,
                        "search_input[startdate]":this.startdate,
                        "search_input[enddate]": this.enddate,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.stockingList = data.data.list
                            that.prepareExtra = data.data.extra
                        } else {
                            head.dialog.alert(data.message);
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    }
                })
            },
            exportData(){
                var that=this
                that.disabled=true
                $.ajax({
                    url: '<?php echo $this->createUrl("exportProductOrder") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        status:20,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            const filename ='等待确认.xlsx';
                            const ws_name = "SheetJS";
                            var exportDatas = [];
                            for(var i=0;i<data.data.list.length;i++){
                                exportDatas.push({
                                    "订单号":data.data.list[i].order_id,
                                    "学生id":data.data.list[i].childid,
                                    "姓名":data.data.list[i].child_name_cn+'\n'+data.data.list[i].child_name_en,
                                    "学生班级":data.data.list[i].child_class,
                                    "账单班级":data.data.list[i].class_name,
                                    "类型":data.data.list[i].export_product_title,
                                    "规格":data.data.list[i].attr1_title_cn +' '+ data.data.list[i].attr2_title_cn+"\n"+data.data.list[i].attr1_title_en + ' ' + data.data.list[i].attr2_title_en,
                                    "数量":data.data.list[i].num,
                                    "金额":data.data.list[i].sum_price,
                                    "下单时间":data.data.list[i].order_time,
                                    "操作时间":data.data.list[i].updated_time,
                                })
                            }
                            var wb=XLSX.utils.json_to_sheet(exportDatas,{
                                origin:'A1',// 从A1开始增加内容
                                header: ['订单号',"学生id",'姓名','学生班级','账单班级','类型','规格','数量','金额','下单时间','操作时间']
                            });
                            for (let key in wb){
                                wb[key]['s'] = {
                                    font: {
                                        sz: 10,
                                        bold: false,//设置标题是否加粗
                                    },
                                    alignment: {
                                        horizontal: 'center',
                                        vertical: 'center',
                                        wrapText: true
                                    },//设置标题水平竖直方向居中，并自动换行展示
                                    fill: {
                                        fgColor: { rgb: 'ebebeb' }//设置标题单元格的背景颜色
                                    },
                                    border: {//添加边框
                                        bottom: {
                                            style: 'thin',
                                            color: '000000'
                                        },
                                        left: {
                                            style: 'thin',
                                            color: '000000'
                                        },
                                        right: {
                                            style: 'thin',
                                            color: '000000'
                                        },
                                        top: {
                                            style: 'thin',
                                            color: '000000'
                                        }
                                    }
                                };
                            }
                            wb['!cols'] = [{wpx:100},{wpx:100},{wpx:120},{wpx:120},{wpx:200},{wpx:200}];
                            openDownload(sheet2blob(wb,ws_name), filename);
                        } else {
                            head.dialog.alert(data.message);
                        }
                        that.disabled=false
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                        that.disabled=false
                    }
                })

            },
            exportStocking(){
                var title='备货统计'
                var elt = document.getElementById('stocking');
                var wb = XLSX.utils.table_to_book(elt, {sheet:title});
                return XLSX.writeFile(wb, title+'.xlsx');
            },
            allChecked(e){
                this.checkList=[]
                if(this.checkAll){
                    this.dataList.list.forEach((item,index) => {
                        this.checkList.push(item.id)
                    })
                }
            },
            Checked(){
                if(this.checkList.length==this.dataList.list.length){
                    this.checkAll=true
                }else{
                    this.checkAll=false
                }
            },
            showConfirm(data){
                if(data=='all' && this.checkList.length==0){
                    resultTip({
                        error: 'warning',
                        msg:'请选择订单'
                    });
                    return
                }
                this.confirmList=data
                $('#confirmModal').modal('show')

            },
            confirmData(){
                var that=this
                this.disabled=true
                $.ajax({
                    url: '<?php echo $this->createUrl("confirmOrder") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        status:30,
                        ids:this.confirmList=='all'?this.checkList:[this.confirmList.id]
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            resultTip({
                                msg: data.state
                            });
                          $('#confirmModal').modal('hide')
                            that.init()
                        } else {
                          $('#confirmModal').modal('hide')
                            head.dialog.alert(data.message);
                        }
                        that.disabled=false
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                        that.disabled=false
                    }
                })
            },
            printPrepare(){
                $('#confirmModalPrepare').modal('show')
            },
            printPDF(){
                let that = this
                $.ajax({
                    url: '<?php echo $this->createUrl("storePrepareDetail") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        status:20,
                        pcid:that.pcid,
                        "search_input[child_name]":this.child_name,
                        "search_input[class_id]": this.class_id,
                        "search_input[startdate]":this.startdate,
                        "search_input[enddate]": this.enddate,
                        data_input:that.prepareExtra
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            $('#confirmModalPrepare').modal('hide')
                            let id = data.data
                            window.open('<?php echo $this->createUrl('printPrepare'); ?>&id='+id,'_blank');
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    }
                })
            }
        }
    })
    </script>
