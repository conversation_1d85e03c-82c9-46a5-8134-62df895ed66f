<div class="col-md-2">
    <div class="list-group class-list-group">
        <!-- place holder -->
    </div>
</div>
<div class="col-md-8">
    <table class="table table-bordered class-schedule">
        <thead></thead>
        <tbody></tbody>
    </table>
</div>
<div class="col-md-2">
    <div class="list-group subject-list-group">
        <!-- place holder -->
    </div>
</div>

<script type="text/template" id="classes-class-item-template">
    <a href="javascript:;" data-classid=<%= classid %> onclick="loadClassSchedule(this)" class="list-group-item"><%- title %></a>
</script>

<script type="text/template" id="schedules-item-template">
    <a href="javascript:;" data-scode=<%= scode %> onclick="setViewTimeslotScheduleParams('scode', '<%= scode %>', this)" class="list-group-item"><%- scode %></a>
</script>

<script type="text/template" id="classes-subject-item-template">
    <a href="javascript:;" data-subject=<%= scode %> onclick="filterSubject(this, wrapper)" class="list-group-item">
    <% print(programCfg['programs'][scode]); %>
    </a>
</script>

<script type="text/template" id="classes-schedule-item-template">
    <h4><% print(programCfg['programs'][subject_flag]); %></h4>
    <div class="text-muted">
        <% for( var jsoni in JSON.parse(teacher_uid)) { %>
            <% if(!_.isUndefined(pageData.teacherList[JSON.parse(teacher_uid)[jsoni]])){print %>
        <h5><span class="glyphicon glyphicon-user"></span> <%- pageData.teacherList[JSON.parse(teacher_uid)[jsoni]].name %></h5>
            <% } %>
        <% } %>


        <% if(!_.isUndefined(pageData.teacherList[teacher_uid])){print %>
        <h5><span class="glyphicon glyphicon-user"></span> <%- pageData.teacherList[teacher_uid].name %></h5>
        <% } %>
    </div>
</script>

<script type="text/template" id="classes-schedule-classinfo-template">
    <h5 class="text-muted"><% print(pageData.classList[classid].title); %></h5>
</script>

<script>
    var classesClassItemTemplate = _.template($('#classes-class-item-template').html());
    var scheduleItemTemplate = _.template($('#schedules-item-template').html());
    var classesSubjectItemTemplate = _.template($('#classes-subject-item-template').html());
    var classesScheduleItemTemplate = _.template($('#classes-schedule-item-template').html());
    var classesScheduleClassInfoTemplate = _.template($('#classes-schedule-classinfo-template').html());
    var classGroupedScheduleData = null; //按班级分类的课表数据
    var sortedClassIdWithData = [];
    var classScheduleData = null; //班级课表数据
//    var allValidScheduleData = null; //去除了AB冗余数据：某班时间表为B，去除A的数据

    var filterSubject = function(obj, wrapper){
        var _subject = $(obj).data('subject');
        wrapper.find('td').removeClass('info');
        wrapper.find('td[data-subject]').each(function(){
            var subjectList = $(this).data('subject').split(',');
            if (!_.isEmpty(_.where(subjectList,_subject))){
                $(this).addClass('info');
            }
        });
    };

    var renderDataclasses = function(viewid){
        classGroupedScheduleData = _.groupBy(pageData.scheduleList, 'classid');

        classScheduleData = {};
        var i=0;
        _.each(pageData.sortedClassIds, function(_classId){
            if(!_.isEmpty(classGroupedScheduleData[_classId])){
                classScheduleData[_classId] = classGroupedScheduleData[_classId];
                sortedClassIdWithData[i++] = _classId;
            }

        });

        wrapper = $('.view-zone[data-viewid|="'+viewid+'"]');
        wrapper.find('div.class-list-group').empty();
        _.each(pageData.sortedClassIds, function(_classId){
            if(!_.isEmpty(classGroupedScheduleData[_classId])){
                var _class = classesClassItemTemplate(pageData.classList[_classId]);
                wrapper.find('div.class-list-group').append(_class);
            }
        });
    }

    var loadClassSchedule = function(obj){
        $(obj).siblings().removeClass('active');
        $(obj).addClass('active');
        var _classid = $(obj).data('classid');

        wrapper.find('div.subject-list-group').empty();
        wrapper.find('table.class-schedule thead').empty();
        wrapper.find('table.class-schedule tbody').empty();
        if(_.isEmpty(classGroupedScheduleData)){
            classGroupedScheduleData = _.groupBy(pageData.scheduleList, 'classid');
        }

        if(_.isEmpty(classGroupedScheduleData[_classid])){

        }else{
            var assignedSubjects = _.keys( (_.indexBy(classGroupedScheduleData[_classid], 'subject_flag')) );
            if(!_.isEmpty(assignedSubjects)){
                wrapper.find('div.subject-list-group').empty();
                _.each(assignedSubjects, function(_scode){
                    var _subject = classesSubjectItemTemplate({scode:_scode});
                    wrapper.find('div.subject-list-group').append(_subject);
                })
                wrapper.find('div.subject-list-group').prepend(classesSubjectItemTemplate({scode:'ALL'}));

                renderScheduleTable(_classid, classScheduleData[_classid]);
            }
        }
    }

    var renderScheduleTable = function(classid, scheduleData){
        var table = wrapper.find('table.class-schedule');
        var thead = table.find('thead');
        var tbody = table.find('tbody');
        table.hide();
        thead.empty();
        tbody.empty();

        //画表头
        var _tr = $('<tr class="active"></tr>').append('' +
            '<th class="warning" width=120><h4>'+
            '<span class="glyphicon glyphicon-calendar"></span> ' +
            pageData.classList[classid].title+'</h4></th>' +
            '');
        _.each( pageData.dayLabels, function(day, i){
            _tr.append($('<th class="active"></th>').html('<h4>'+day+'</h4>'));
        } );
        thead.append(_tr);

        //画表体
        _.each( programCfg.schedules[scheduleCode], function(timeslot){
            var _tr = $('<tr></tr>').append($('<th class="active"></th>').html('<h5>'+timeslot+'</h5>'));
            _.each( pageData.dayLabels, function(day, i){
                _tr.append($('<td width="18%"></td>').attr('data-day',i).attr('data-timeslot',timeslot));
            } );
            tbody.append(_tr);
        });
        //填充表格内容
        var subject_flag = '';
        _.each(scheduleData, function(item,index){
            var _item = classesScheduleItemTemplate(item);
            if (tbody.find('td[data-timeslot|="'+item.timeslot+'"][data-day|="'+item.weekday+'"]').text()){
                subject_flag = (_.isEmpty(subject_flag)) ? subject_flag+item.subject_flag : subject_flag+','+item.subject_flag;
            }else{
                subject_flag = item.subject_flag;
            }
            tbody.find('td[data-timeslot|="'+item.timeslot+'"][data-day|="'+item.weekday+'"]').attr("data-subject",subject_flag).append(_item);
        })

        table.show('blind',{},500);
    }
</script>