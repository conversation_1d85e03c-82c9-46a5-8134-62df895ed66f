<div id="search" style="margin-bottom: 10px">
    <el-row class="demo-autocomplete">
        <el-col :span="20">
            <el-input
                ref="searchInput"
                class="inline-input"
                size="small"
                prefix-icon="el-icon-search"
                v-model="search_name"
                placeholder="搜索学生"
                :trigger-on-focus="false"
                @select="handleSelect"
                clearable
                @input="oninput"
            ></el-input>
        </el-col>
    </el-row>
</div>

<script>
    var search = new Vue({
        el: "#search",
        data:{
            search_name: sessionStorage.getItem('chosen_name'),
            list: JSON.parse(sessionStorage.getItem('listUpdated')),
            parentListData: JSON.parse(sessionStorage.getItem('listUpdated')),//父级的原数据
        },
        created: function() {},
        computed: {},
        mounted() {
            if(this.search_name){
                this.handleSelect({value:this.search_name})
            }
        },
        methods: {
            oninput(value){
                let that = this;
                if(!value){
                    that.handleSelect({value:''})
                }else{
                    that.handleSelect({value:value})
                }
            },
            handleSelect(item) {
                let that = this;
                if(item.value === ''){
                    that.list = that.parentListData;
                    sessionStorage.setItem('chosen_name', '');
                }else{
                    that.list = that.parentListData.filter(student => student.name.toLowerCase().includes(item.value.toLowerCase()));
                }
                eventBus.$emit('listChanged', that.list);
            },
        },

    })
    window.addEventListener('beforeunload', function(event) {
        sessionStorage.removeItem('chosen_name');
    });
</script>