<?php
/**
 * Message translations.
 *
 * This file is automatically generated by 'yiic message' command.
 * It contains the localizable messages extracted from source code.
 * You may modify this file by translating the extracted messages.
 *
 * Each array element represents the translation (value) of a message (key).
 * If the value is empty, the message is considered as not translated.
 * Messages that no longer need translation will have their translations
 * enclosed between a pair of '@@' marks.
 *
 * Message string can be used with plural forms format. Check i18n section
 * of the guide for details.
 *
 * NOTE, this file must be saved in UTF-8 encoding.
 *
 * @version $Id: $
 */
return array (
    "Daystar Academy Entrance Test Evaluation (Academic Year 2017-2018)" => "启明星学校入学测试表（2017-2018学年）",
    "Test Criteria: A.Excellent B.Pass C.Need further test D.Couldn’t Pass" => "测试结果：A.优秀 B.通过 C.有待进一步测试 D.不通过",
    "Parent Contact" => "家长联系方式",
    "Application Grade" => "申请年级",
    "Chinese Test" => "语文测试",
    "Evaluated by" => "测试老师",
    "Evaluation Date" => "测试时间",
    "Test result" => "测试结果",
    "Oral Chinese" => "口语表达",
    "Character Writing" => "汉字书写",
    "Reading Comprehension" => "阅读理解",
    "Foundation" => "英语基础",
    "Writing" => "写作",
    "Focus and Behavior" => "专注与行为",
    "CSL" => "是否需要中文支持",
    "Note" => "备注",
    "Math Test" => "数学测试",
    "Math Operations" => "基本运算",
    "Knowledge" => "知识掌握",
    "English Test" => "英文测试",
    "ESL" => "是否需要英文支持",
    "Whether to pass the interview" => "是否通过面试",

    "content of interview" => "面试内容",
    "Separation Anxiety" => "与家长分离情况",
    "Can be	interviewed	separately	from parents?" => "能否独立进入面试环境？",
    "Eye contact?" => "是否有眼神交流？",
    "Can answer questions?" => "是否愿意与对人对话？",
    "Eye-hand cooperation" => "手眼协调",
    "Strong	hand" => "惯用手",
    "Pencil	grasps" => "握笔姿势",
    "Length of concentration?" => "是否能专注于工作？",
    "pack away works?" => "工作结束后能否将物品归位？",
    "For children above 5" => "以下适用于五岁测试",
    "Verbal	ability" => "语言表达",
    "Reading ability" => "阅读",
    "Math" => "数学",
    "Feedback" => "反馈",
    "Gender" => "学生性别",
    "Current Class" => "班级",
    "Assignment Student Card" => "学生在校基本情况卡",
    "Daystar Academy Kindergarten Interview" => "启明星幼儿园面试",

    "Chinese Teacher" => "中文老师",
    "English Teacher" => "英文老师",
    "LEAP/CSL Teacher" => "LEAP/CSL 老师",
    "Speaking Level" => "口语表达水平",
    "Listening Level" => "听力水平",
    "Reading Level" => "阅读水平",
    "Writing Level" => "写作水平",
    "Math Level" => "数学水平",
    "Behavior" => "行为规范",
    "Effort" => "付出的努力",
    "Positive Peer Relationship" => "与同龄人相处",
    "Other Comment" => "其他评语",
    "Exceeds grade level standards" => "超出年级水平阶段",
    "Proficient in grade level standards" => "处于年级水平达标阶段",
    "Developing progress in grade level standards" => "处于年级水平发展阶段",
    "Beginning progress in grade level standards" => "处于年级水平起步阶段",
    "Consistently display desired behavior" => "表明学生一如既往表现良好",
    "Often displays desired behavior" => "表明学生大部分时间表现良好",
    "Improvement needed" => "表明学生很少或有时表现良好",
);
