<div style="color:#274257;">
    <div style="font-family: 'Microsoft Yahei'">
        <?php echo $mailDesc;?>
        
        <?php foreach( array_keys($result) as $classid ):?>
        <h2 style="font-size:14px;">
            <?php echo isset($classInfo[$classid]) ? $classInfo[$classid] : ''; ?>
        </h2>
        <table border='1' cellpadding='2' cellspacing='0' width="90%">
            <tr>
                <th width="80" style="font-family: 'Microsoft Yahei';font-size:12px;">孩子姓名</th>
                <th width="200" style="font-family: 'Microsoft Yahei';font-size:12px;">账单标题</th>
                <th width="80" style="font-family: 'Microsoft Yahei';font-size:12px;">原始金额</th>
                <th width="80" style="font-family: 'Microsoft Yahei';font-size:12px;">实际金额</th>
                <th style="font-family: 'Microsoft Yahei';font-size:12px;">运行结果</th>
            </tr>
            <?php
            foreach(array_keys($result[$classid]) as $childid):
                $countbyChild =  count($result[$classid][$childid]);
            ?>
            <tr>
                <td style="font-family: 'Microsoft Yahei';font-size:12px;" 
                <?php if($countbyChild>1) echo sprintf(" rowspan=%s", $countbyChild);?>
                >
                    <?php echo isset($children[$childid])?$children[$childid]:'' . ' ' . $childid;?>
                </td>
                
                <td style="font-family: 'Microsoft Yahei';font-size:12px;">
                    <?php echo CHtml::encode( $result[$classid][$childid][0]['title'] );?>
                </td>
                <td style="font-family: 'Microsoft Yahei';font-size:12px;">
                    <?php echo $result[$classid][$childid][0]['amount'];?>
                </td>
                <td style="font-family: 'Microsoft Yahei';font-size:12px;">
                    <?php echo $result[$classid][$childid][0]['finalAmount'];?>
                </td>
                <td style="font-family: 'Microsoft Yahei';font-size:12px;">
                    <?php
                        if($result[$classid][$childid][0]['result'])
                            echo '成功';
                        else{
                            echo CHtml::openTag('span', array('style'=>'color:red;'));
                            echo '失败: ';
                            echo CHtml::encode( $result[$classid][$childid][0]['message'] );
                            echo CHtml::closeTag('span');
                        }
                    ?>
                </td>
            </tr>
            
            <?php
            if($countbyChild > 1):
            
            for($i=1; $i<$countbyChild; $i++){
                if (isset($result[$classid][$childid][$i])):
            ?>
            <tr>
                <td style="font-family: 'Microsoft Yahei';font-size:12px;">
                    <?php echo CHtml::encode( $result[$classid][$childid][$i]['title'] );?>
                </td>
                <td style="font-family: 'Microsoft Yahei';font-size:12px;">
                    <?php echo $result[$classid][$childid][$i]['amount'];?>
                </td>
                <td style="font-family: 'Microsoft Yahei';font-size:12px;">
                    <?php echo $result[$classid][$childid][$i]['finalAmount'];?>
                </td>
                <td style="font-family: 'Microsoft Yahei';font-size:12px;">
                    <?php
                        if($result[$classid][$childid][$i]['result'])
                            echo '成功';
                        else{
                            echo CHtml::openTag('span', array('style'=>'color:red;'));
                            echo '失败: ';
                            echo CHtml::encode( $result[$classid][$childid][$i]['message'] );
                            echo CHtml::closeTag('span');
                        }
                    ?>
                </td>
            </tr>
            
            <?php
            endif;
            }
            endif;
            ?>
            
            <?php endforeach;?>
        </table>
        <?php endforeach;?>
    </div>
</div>
