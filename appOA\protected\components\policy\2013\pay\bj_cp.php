<?php
if(!defined("IVY_POLICY"))
throw new CException('DO NOT USE THIS FILE DIRECTLY');

return array( 'policy'=>array(

	//是否老生老办法
	//ENABLE_CONSTANT_TUITION => false,

	//年保育费
	//ANNUAL_CHILDCARE_AMOUNT => 8000,

	//是否开放课外活动账单
	ENABLE_AFTERSCHOOLS => true,

	//图书馆工本费
	FEETYPE_LIBCARD => 10,

	//学期四个时间点；取前后两端
	TIME_POINTS   => array(201309,201312,201401,201406),

	//寒暑假月份，及其每月计费天数
	//HOLIDAY_MONTH => array(201402=>20,201408=>20),

	//月份开通哪些缴费类型
	PAYGROUP_MONTH => array(
		ITEMS => array(
			FEETYPE_TUITION,
			FEETYPE_LUNCH,
			FEETYPE_SCHOOLBUS,
		),
		TUITION_CALC_BASE => BY_MONTH,
	),

	//年付开通哪些缴费类型
	PAYGROUP_ANNUAL => array(
		ITEMS => array(
			FEETYPE_TUITION,
			FEETYPE_LUNCH,
			FEETYPE_SCHOOLBUS,
		),
		TUITION_CALC_BASE => BY_ANNUAL,
	),

	//学期付开通哪些缴费类型
	PAYGROUP_SEMESTER => array(
		ITEMS => array(
			FEETYPE_TUITION,
			FEETYPE_LUNCH,
			FEETYPE_SCHOOLBUS,
		),
		TUITION_CALC_BASE => BY_SEMESTER,
	),

	//月份开通哪些缴费类型
	//PAYGROUP_MONTH => array(
	//	ITEMS => array(
	//		FEETYPE_TUITION,
	//		FEETYPE_LUNCH,
	//		FEETYPE_SCHOOLBUS,
	//	),
	//	TUITION_CALC_BASE => BY_MONTH,
	//),

	//计算基准：BY_MONTH, 或者 BY_SETTING, 前者表示所有的学费都基于月费用计算，后者表示所有的学费都基于学期或学年
	TUITION_CALCULATION_BASE => array(
		BY_MONTH => array(

			//月收费金额
			AMOUNT => array(
				FULL5D => 15000,
				HALF5D => 10000,
				HALF3D => 7200,
				HALF2D => 5000,
			),

			//收费月份，及其权重
			MONTH_FACTOR => array(
				201309=>1,
				201310=>1,
				201311=>1,
				201312=>1,
				201401=>1,
				201402=>1,
				201403=>1,
				201404=>1,
				201405=>1,
				201406=>1,
			),

			//折扣

			//GLOBAL_DISCOUNT => array(
			//	DISCOUNT_ANNUAL => '1:1:2013-12-01'
			//)


		),
		//东湖配置实例

		BY_ANNUAL => array(
			AMOUNT => array(
				FULL5D => 145500,
				FULL4D => 130950,
				HALF2D => 48500,
				HALF3D => 69840,
				HALF5D => 97000,
			),
		),

		BY_SEMESTER1 => array(
			AMOUNT => array(
				FULL5D => 60000,
				FULL4D => 54000,
				HALF2D => 20000,
				HALF3D => 28800,
				HALF5D => 40000,
			),
		),

		BY_SEMESTER2 => array(
			AMOUNT => array(
				FULL5D => 90000,
				FULL4D => 81000,
				HALF2D => 30000,
				HALF3D => 43200,
				HALF5D => 60000,
			),
		),

	),

	//每餐费用
	LUNCH_PERDAY => 28,

	//账单到期日和账单开始日之差
	DUEDAYS_OFFSET => 0,

    //代金卷金额
    VOUCHER_AMOUNT => 500,

));

?>