<?php

/**
 * This is the model class for table "ivy_user_profile_site".
 *
 * The followings are the available columns in table 'ivy_user_profile_site':
 * @property integer $uid
 * @property string $online
 * @property integer $online_level
 * @property string $user_language
 * @property integer $user_language_level
 * @property string $user_interest
 * @property integer $user_interest_level
 * @property string $user_skype
 * @property integer $user_skype_level
 * @property string $user_msn
 * @property integer $user_msn_level
 * @property string $user_mphone
 * @property integer $user_mphone_level
 * @property string $user_phone
 * @property integer $user_phone_level
 * @property string $role_category
 * @property integer $role_category_level
 * @property string $role
 * @property integer $role_level
 * @property integer $register_time
 * @property integer $register_time_level
 * @property integer $has_lms
 * @property integer $has_lms_level
 */
class UserProfileSite extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return UserProfileSite the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_user_profile_site';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('uid, online_level, user_language_level, user_interest_level, user_skype_level, user_msn_level, user_mphone_level, user_phone_level, role_category_level, role_level, register_time, register_time_level, has_lms, has_lms_level', 'numerical', 'integerOnly'=>true),
			array('online', 'length', 'max'=>10),
			array('user_skype, user_msn', 'length', 'max'=>100),
			array('user_mphone, user_phone, role_category, role', 'length', 'max'=>32),
			array('user_language, user_interest', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('uid, online, online_level, user_language, user_language_level, user_interest, user_interest_level, user_skype, user_skype_level, user_msn, user_msn_level, user_mphone, user_mphone_level, user_phone, user_phone_level, role_category, role_category_level, role, role_level, register_time, register_time_level, has_lms, has_lms_level', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'uid' => 'Uid',
			'online' => 'Online',
			'online_level' => 'Online Level',
			'user_language' => 'User Language',
			'user_language_level' => 'User Language Level',
			'user_interest' => 'User Interest',
			'user_interest_level' => 'User Interest Level',
			'user_skype' => 'User Skype',
			'user_skype_level' => 'User Skype Level',
			'user_msn' => 'User Msn',
			'user_msn_level' => 'User Msn Level',
			'user_mphone' => 'User Mphone',
			'user_mphone_level' => 'User Mphone Level',
			'user_phone' => 'User Phone',
			'user_phone_level' => 'User Phone Level',
			'role_category' => 'Role Category',
			'role_category_level' => 'Role Category Level',
			'role' => 'Role',
			'role_level' => 'Role Level',
			'register_time' => 'Register Time',
			'register_time_level' => 'Register Time Level',
			'has_lms' => 'Has Lms',
			'has_lms_level' => 'Has Lms Level',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('uid',$this->uid);
		$criteria->compare('online',$this->online,true);
		$criteria->compare('online_level',$this->online_level);
		$criteria->compare('user_language',$this->user_language,true);
		$criteria->compare('user_language_level',$this->user_language_level);
		$criteria->compare('user_interest',$this->user_interest,true);
		$criteria->compare('user_interest_level',$this->user_interest_level);
		$criteria->compare('user_skype',$this->user_skype,true);
		$criteria->compare('user_skype_level',$this->user_skype_level);
		$criteria->compare('user_msn',$this->user_msn,true);
		$criteria->compare('user_msn_level',$this->user_msn_level);
		$criteria->compare('user_mphone',$this->user_mphone,true);
		$criteria->compare('user_mphone_level',$this->user_mphone_level);
		$criteria->compare('user_phone',$this->user_phone,true);
		$criteria->compare('user_phone_level',$this->user_phone_level);
		$criteria->compare('role_category',$this->role_category,true);
		$criteria->compare('role_category_level',$this->role_category_level);
		$criteria->compare('role',$this->role,true);
		$criteria->compare('role_level',$this->role_level);
		$criteria->compare('register_time',$this->register_time);
		$criteria->compare('register_time_level',$this->register_time_level);
		$criteria->compare('has_lms',$this->has_lms);
		$criteria->compare('has_lms_level',$this->has_lms_level);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}