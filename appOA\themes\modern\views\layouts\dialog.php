<!doctype html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<meta name="language" content="en" />
	<meta http-equiv="X-UA-Compatible" content="IE=edge" />
	
	<link href="<?php echo Yii::app()->theme->baseUrl; ?>/css/admin_style.css?v<?php echo Yii::app()->params['assetsVersion'];?>" rel="stylesheet" />
	<link href="<?php echo Yii::app()->theme->baseUrl; ?>/css/main.css?v<?php echo Yii::app()->params['assetsVersion'];?>" rel="stylesheet" />
    <script>
	var GV = {
		JS_ROOT : "<?php echo Yii::app()->request->baseUrl; ?>/js/dev/",	//js目录
		JS_VERSION : "<?php echo Yii::app()->params['assetsVersion'];?>",	//版本号,强制刷新
		TOKEN : '',	//token ajax全局
		URL : {
			LOGIN : ''
		},
        'themeManagerBaseUrl': '<?php echo Yii::app()->themeManager->baseUrl;?>',
        'themeBaseUrl': '<?php echo Yii::app()->theme->baseUrl;?>',
        'lang': "<?php echo Yii::app()->language;?>"
	};
	</script>
</head>
<body class="body_none" style="width:<?php echo isset($this->dialogWidth)?$this->dialogWidth:300?>px;">
    <?php Yii::app()->clientScript->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/head.min.js');?>
	<?php Yii::app()->clientScript->registerCoreScript('jquery');?>
    <?php echo $content; ?>
    <script type="text/javascript" src="<?php echo Yii::app()->themeManager->baseUrl; ?>/base/js/common.js"></script>
</body>
</html>