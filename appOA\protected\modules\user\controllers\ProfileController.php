<?php

/**
 * Class ProfileController
 */
class ProfileController extends ProtectedController
{
    public function init(){
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";
        $this->modernMenuFlag = 'main';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        Yii::import('common.models.staff.Staff');
        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/jquery.qrcode.min.js');
    }

	public function actionIndex()
	{
        $category = Yii::app()->request->getParam('category','basic');
        if (strtolower($category) == 'upload'){
            $cs = Yii::app()->clientScript;
            $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/underscore-min.js');
            $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/backbone-min.js');
            $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/plupload/plupload.full.min.js');
            $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/cropper/cropper.min.js');
            $cs->registerCssFile(Yii::app()->themeManager->baseUrl.'/base/js/cropper/cropper.min.css');
            

        } else if ( strtolower($category) == 'expan' ) {
            $cs = Yii::app()->clientScript;
            $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/pinyin.js');
        }
		$this->render('index',array('category'=>$category));
	}

    /*
     * 保存基本信息
     */
    public function actionSaveBasic(){
        if (Yii::app()->request->isPostRequest){
            $this->staff->attributes = $_POST['User'];
            $this->staff->profile->attributes = $_POST['UserProfile'];
            if(empty($this->staff->profile->attributes['occupation_en'])){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '职位职责不可为空');
                $this->showMessage();
            }
            $this->staff->profile->setScenario('editStaff');
            if ($this->staff->profile->validate()){
                if ($this->staff->save() && $this->staff->profile->save()){
                    Staff::updateWechat($this->staff->uid,'update');
                    $this->addMessage('state', 'success');
                    $this->addMessage('message', Yii::t('message','success'));
                    $this->showMessage();
                }else{
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', Yii::t('user', '提交的信息有错误.'));
                    $this->showMessage();
                }
            }else{
                $this->addMessage('state', 'fail');
                $this->addMessage('error', $this->staff->profile->getErrors());
                $this->addMessage('message', '请填写必填项');
                $this->showMessage();
            }
        }
    }

    //保存扩展信息
    public function actionSaveExpan(){
        if (Yii::app()->request->isAjaxRequest && Yii::app()->request->isPostRequest){
            $model = Staff::model()->findByPk($this->staff->uid);
            if (empty($model)){
                $model = new Staff();
                $model->uid = $this->staff->sid;
            }
            $model->attributes = $_POST['Staff'];
            $model->card_id_due = isset($_POST['Staff']['card_id_due']) ? strtotime($_POST['Staff']['card_id_due']) : 0;
            $model->birthday = isset($_POST['Staff']['birthday_search']) ? strtotime($_POST['Staff']['birthday_search']) : 0;
            $model->visa_expiry = isset($_POST['Staff']['visa_expiry']) ? strtotime($_POST['Staff']['visa_expiry']) : 0;
            $model->setScenario('editUser');
            if ($model->validate()){
                if ($model->save()){
                    Staff::updateWechat($model->sid,'update');
                    $this->addMessage('state', 'success');
                    $this->addMessage('message', Yii::t('message','success'));
                    $this->showMessage();
                }else{
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', Yii::t('user', '提交的信息有错误.'));
                    $this->showMessage();
                }
            }else{
                $this->addMessage('state', 'fail');
                $this->addMessage('error', $model->getErrors());
                $this->addMessage('message', '请填写必填项');
                $this->showMessage();
            }
        }
    }

    //更改密码保存
    public function actionChangePd(){
        if (Yii::app()->request->isAjaxRequest && Yii::app()->request->isPostRequest){
            $model = User::model()->findByPk($this->staff->uid);
            if (!empty($model)){
                if ($model->pass == md5($_POST['User']['pass'])){
                    $model->changePassword = trim($_POST['User']['changePassword']);
                    $model->verifyPassword = trim($_POST['User']['verifyPassword']);
                    $model->setScenario('create');
                    if ($model->validate()){
                        $model->pass = md5($model->changePassword);
                        if ($model->save()){
                            $this->addMessage('state', 'success');
                            $this->addMessage('message', Yii::t('message','success'));
                            $this->showMessage();
                        }else{
                            $this->addMessage('state', 'fail');
                            $this->addMessage('message', Yii::t('user', '提交的信息有错误.'));
                            $this->showMessage();
                        }
                    }else{
                        $errs = current($model->getErrors());
                        $this->addMessage('state', 'fail');
                        $this->addMessage('error', $model->getErrors());
                        $this->addMessage('message', $errs?$errs[0]:'请填写必填项');
                        $this->showMessage();
                    }
                }else{
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', '密码错误！');
                    $this->showMessage();
                }
            }
        }
    }

    //上传头像
    public function actionUpAvatar(){
        $file = CUploadedFile::getInstanceByName('file');
        if ($file) {
            $filePath = Yii::app()->params['OAUploadBasePath'] . '/users/';
            $fileName = strtolower('tmp_' . $this->staff->uid . '.' . $file->getExtensionName());
            $file->saveAs($filePath . $fileName);
            echo CJSON::encode(array(
                'url' => Yii::app()->params['OAUploadBaseUrl'] . '/users/' . $fileName . '?v' . time(),
                'ext' => $file->getExtensionName(),
            ));
        }
    }

    //保存裁剪过的头像
    public function actionSaveAvatar(){
        $data       = Yii::app()->request->getParam('data','');
        $ext        = Yii::app()->request->getParam('ext','');
        if($data && $ext){
            $staffid = $this->staff->uid;
            $ext = strtolower($ext);
            $file = Yii::app()->params['OAUploadBasePath'].'/users/tmp_'.$staffid.'.'.$ext;
            if(file_exists($file)){
                Yii::import('common.models.classTeacher.InfopubStaffExtend');
                Yii::import('application.extensions.image.Image');
                $image = new Image($file);
                $image->quality(100);
                //$image->sharpen(100);
                $data = CJSON::decode($data);
                $image->crop($data['width'], $data['height'], $data['y'], $data['x']);
                $image->resize(200,200);
                $filename = 'p_'.uniqid().'.'.$ext;
                $subDir = 'users/';
                $subThumbDir = $subDir . 'thumbs/';
                $rootUploadPath = rtrim(Yii::app()->params['OAUploadBasePath'], '/') . '/';
                $image->save( $rootUploadPath . $subDir . $filename);
                $aliYunOss['new'][] = $subDir . $filename;
                $image->resize(80,80);
                $image->save( $rootUploadPath . $subThumbDir . $filename );
                $aliYunOss['new'][] = $subThumbDir . $filename;
                @unlink($file);
                if($this->staff->user_avatar && $this->staff->user_avatar !== 'blank.gif'){
                    $fileDel[] = $rootUploadPath . $subDir . $this->staff->user_avatar;
                    $fileDel[] = $rootUploadPath . $subThumbDir . $this->staff->user_avatar;
                    foreach($fileDel as $_del){
                        @unlink( $_del );
                        $aliYunOss['del'][] = $_del;
                    }
                }

                $this->staff->user_avatar = $filename;
                $this->staff->save(false);

                Staff::updateWechat($staffid, 'update');

                CommonUtils::processAliYunOSS($aliYunOss, $rootUploadPath);

                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message','Data saved!'));
                $this->addMessage('refresh',true);
            }
        }
        else{
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '请选头像！');
        }
        $this->showMessage();
    }

    // 获取 ass 收入信息
    public function actionAsainfo()
    {
        Yii::import('common.models.asainvoice.*');
        $typeList = CommonUtils::LoadConfig('CfgASA');

        $uid = 0;
        if(Yii::app()->user->checkAccess('ivystaff_it')){
            $uid = intval(Yii::app()->request->getParam('uid', 0));
        }
        $uid = ($uid) ? $uid : $this->staff->uid;

        $userModel = User::model()->findByPk($uid);

        $vendor = AsaVendor::model()->findByAttributes(array('ivy_uid' => $uid, 'active' => 1));
        $reportItems = array();
        $variation = array();
        $data = array();

        if($vendor){
            $criteria = new CDbCriteria;
            $criteria->compare('vendor_id', $vendor->vendor_id);
            $criteria->order = 'id DESC';
            $reportItems = AsaMonthReportItem::model()->findAll($criteria);

            // 查找调整金额
            $criteria = new CDbCriteria;
            $criteria->compare('vendor_id', $vendor->vendor_id);
            $criteria->index = 'report_id';
            $variation = AsaMonthReportVariation::model()->findAll($criteria);

            foreach ($reportItems as $reportItem) {
                $data[$reportItem->report->report_month][] = $reportItem;
            }
        }

        $this->render('asainfo', array(
            'data' => $data,
            'typeList' => $typeList,
            'variation' => $variation,
            'userModel' => $userModel,
        ));
    }

    // 获取课程详细信息
    public function actionCoursedetail()
    {
        Yii::import('common.models.asainvoice.*');
        $report_id = Yii::app()->request->getParam('report_id');
        $vendor_id = Yii::app()->request->getParam('vendor_id');
        $course_id = Yii::app()->request->getParam('course_id');

        if (!$report_id || !$vendor_id) {
            return false;
        }

        $criteria = new CDbCriteria();
        $criteria->compare('report_id', $report_id);
        $criteria->compare('vendor_id', $vendor_id);
        if(!is_null($course_id)){
            $criteria->compare('course_id', $course_id);
        }
        $criteria->compare('status', array(1,3,10));
        $criteria->order = 'service_start';
        $model = AsaStaffAttendance::model()->findAll($criteria);

        $criteria = new CDbCriteria();
        $criteria->compare('report_id', $report_id);
        $criteria->compare('substituter', $vendor_id);
        if(!is_null($course_id)){
            $criteria->compare('course_id', $course_id);
        }
        $criteria->compare('status', array(1,3,10));
        $criteria->order = 'service_start';
        $substituter = AsaStaffAttendance::model()->findAll($criteria);

        if (!$model) {
            $model = $substituter;
        } else {
            foreach ($model as $k => $item) {
                if ($item->vendor) {
                    unset($model[$k]);
                }
            }
        }

        $this->renderpartial('coursedetail', array('model' => $model));
    }

    /**
     * 老师已绑定信息
     */
    public function actionTeacherBindInfo()
    {
        $requestUrl = "directMessage/teacherBind/info";
        $this->remote($requestUrl);
    }

    /**
     * 获取绑定二维码
     */
    public function actionTeacherBindQrcode()
    {
        $staffId = Yii::app()->request->getParam('staff_id');
        $requestUrl = "directMessage/teacherBind/qrcode";
        $this->remote($requestUrl, array('staff_id' => $staffId));
    }

    /**
     * 取消绑定
     */
    public function actionTeacherUnbind()
    {
        $requestUrl = "directMessage/teacherBind/unbind";
        $this->remote($requestUrl);
    }

    public function remote($requestUrl, $requestData = array())
    {
        $requestData['schoolId'] = $this->staff->profile->branch;
        $res = CommonUtils::requestDsOnline($requestUrl, $requestData);
        if ($res['code'] == 0) {
            if(strpos($requestUrl,'directMessage/view') !== false){
                $res['data']['school_title'] = $this->branchObj['title'];
            }
            $this->addMessage('state', 'success');
            $this->addMessage('message', $res['msg']);
            $this->addMessage('data', $res['data']);
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->addMessage('data', $res['data']);
        }
        $this->showMessage();
    }
}
