<?php
/**
 * Controller is the customized base controller class.
 * All controller classes for this application should extend from this base class.
 */
class Controller extends CController
{
	/**
	 * @var string the default layout for the controller view. Defaults to '//layouts/column1',
	 * meaning using a single column layout. See 'protected/views/layouts/column1.php'.
	 */
	public $layout='//layouts/column1';

	/**
	 * @var array context menu items. This property will be assigned to {@link CMenu::items}.
	 */
	public $menu=array();
	
	public $siteTitle = "";
	public $subPageTitle = "";
	/**
	 * @var array the breadcrumbs of the current page. The value of this property will
	 * be assigned to {@link CBreadcrumbs::links}. Please refer to {@link CBreadcrumbs::links}
	 * for more details on how to specify this property.
	 */
	public $breadcrumbs=array();
	public $cacheDuration=300;

    public function init()
    {
        if( Yii::app()->params['maintain'] === true ){
            $this->redirect(Yii::app()->params['ivyschoolsUrl']);
        }

        $langcookie     = Yii::app()->request->cookies['child_mims_lang'];
        $themecookie    = Yii::app()->request->cookies['child_mims_theme'];
//        $language = empty($langcookie) ?  Yii::app()->request->preferredLanguage : $langcookie->value;
        $language = empty($langcookie) ?  Mims::getRequestLang() : $langcookie->value;
        $theme = empty($themecookie) ?  Yii::app()->theme : $themecookie->value;
        Yii::app()->language = $language;
        Yii::app()->theme = $theme;
        Yii::app()->clientScript->registerScript('ym','function m(message){console.log(message);}',CClientScript::POS_END);
		
		$this->siteTitle = Yii::app()->name;
    }
	
	public function getPageTitle(){
		return sprintf("%s - %s", $this->siteTitle, $this->subPageTitle);
	}
	
	public function setSubPageTitle($value, $reverse=true){
		if(is_array($value)){
			if ($reverse) $value = array_reverse($value);
			$tmp = array();
			foreach($value as $k=>$v){
				if(is_array($v))
					$tmp[] = Yii::t("navigations", $k);
				else{
					$tmp[] = $v;
				}
			}
			$this->subPageTitle = implode(" · ", $tmp);
		}else{
			$this->subPageTitle = $value;
		}
	}
	
	public function genCacheId($id,$namespace=""){
		return sprintf("%s_%s_%s", Yii::app()->language, $id, $namespace);
	}
}
