/**
 * 横向柱形图 http://blog.lrenwang.com/post/94/
 * 
 * @version 1.2 兼容 IE7 FireFox
 */
var bar = function(id, title, data, option_num_text) {
	// 展示的id
	this.id = '';

	// 标题
	this.title = '';
	// 后加的 弹框的标题头
	this.header_title = '';

	// 数据
	this.data = '';

	// 宽
	this.width = 500;

	// 背景图片位置
	this.bgimg = 'plan.gif';

	// 动画速度
	this.speed = 1000;

	// 投票总数
	var num_all = 0;
	this.show = function() {
		// 添加一个table对象
		$("#" + this.id)
				.append(
						"<table style='border-collapse:separate; border-spacing: 6px; border-spacing: expression(this.cellSpacing=6);' width='"
								+ this.width
								+ "' cellpadding=0 border=0 style='font-size:12px;' ></table>")

		// 计算总数
		$(this.data).each(function(i, n) {
			num_all += parseInt(n[1]);
		})
		if (num_all == 0) {
			num_all = 1;
		}
		var that = this;

		// 起始
		var s_img = [ 0, -52, -104, -156, -208, 0, -52, -104, -156, -208, 0,
				-52 ];
		// 中间点起始坐标
		var c_img = [ -312, -339, -367, -395, -420, -312, -339, -367, -395,
				-420, -312, -339 ];
		// 结束
		var e_img = [ -26, -78, -130, -182, -234, -26, -78, -130, -182, -234,
				-26, -78 ];
		var that = this;
		var div;
		$(this.data)
				.each(
						function(i, n) {

							// 计算比例
							var bili = (n[1] * 100 / num_all).toFixed(2);

							// 计算图片长度, *0.96是为了给前后图片留空间
							var img = parseFloat(bili) * 0.96;

							if (img > 0) {
								div = "<div style='width:3px;height:16px;background:url("
										+ that.bgimg
										+ ") 0px "
										+ s_img[i]
										+ "px ;float:left;'></div><div fag='"
										+ img
										+ "' style='width:0%;height:16px;background:url("
										+ that.bgimg
										+ ") 0 "
										+ c_img[i]
										+ "px repeat-x ;float:left;'></div><div style='width:3px;height:16px;background:url("
										+ that.bgimg
										+ ") 0px "
										+ e_img[i]
										+ "px ;float:left;'></div>";
							} else {
								div = '';
							}
							$("#" + that.id + " table")
									.append(
											"<tr><td width='30%' align='right' ><span title='header=["
													+ n[4]
													+ "] body=["
													+ n[3]
													+ "]'>"
													+ n[0]
													+ n[5]
													+ "</span></td><td width='60%' bgcolor='#ffffff' >"
													+ div
													+ "</td><td width='10%' ><label for='vote_option_"
													+ n[6]
													+ "' style='display: inline-block;width: 80px;'>"
													+ n[1]
													+ " ("
													+ bili
													+ "%)</label></td><td><span>"
													+ n[2]
													+ "</span></td></tr>")
						})

		this.play();
	}

	this.play = function() {
		var that = this;
		$("#" + this.id + " div").each(function(i, n) {
			if ($(n).attr('fag')) {
				$(n).animate({
					width : $(n).attr('fag') + '%'
				}, that.speed)
			}
		})
	}
}
