<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
    <h4 class="modal-title" id="exampleModalLabel"><?php echo $model->vendor_id?'更新':'添加'; ?></h4>
</div>
<?php $form=$this->beginWidget('CActiveForm', array(
    'id'=>'course-form',
    'enableAjaxValidation'=>false,
    'htmlOptions'=>array('class'=>'J_ajaxForm form-horizontal', 'role'=>'form'),
)); ?>
    <div class="modal-body">
        <ul class="nav nav-tabs" id="myTab">
            <input type="hidden" id="keys" value="<?php echo $model->type;?>"/>
            <?php foreach($typeList['type'] as $key=>$type){;?>
            <li id='#tab<?php echo $key;?>'><a href='#tab<?php echo $key;?>' role="tab" checktab='tab<?php echo $key;?>' checknum='$key' data-toggle="tab" class="tabs"><?php echo $type['cn']?></a></li>
            <?php }?>
        </ul>
        <div class="row">
            <div class="tab-content" style="padding: 14px">
                <div class="tab-pane" id="tab1">
                    <div role="tabpanel" class="col-md-12">   
                        <div class="form-group">
                            <input type="hidden" id="teacherId" name="teacherId">
                            <input type="text" id="project" name="project" class="form-control" 
                            placeholder="请搜索老师" value="<?php echo $model->name_cn;?>">
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="form-group">
                            <input type="text" class="form-control" name="teacherPhone" id="teacherPhone"
                                placeholder="联系方式ʽ" value="<?php echo $model->cellphone;?>">
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="form-group">
                            <input type="text" class="form-control" name="teacherEmail" id="teacherEmail" 
                                placeholder="电子邮箱" value="<?php echo $model->email;?>">
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="form-group">
                            <select class="form-control" name="active">
                                <option value="1" <?php if($model->active == 1){echo 'selected';}?>><?php echo "有效";?></option>
                                <option value="0" <?php if($model->active == 0){echo 'selected';}?>><?php echo "无效";?></option>
                            </select>
                        </div>
                    </div>
                    <input type="hidden" name="teacherType" value="1"/>
                </div>

                <div class="tab-pane" id="tab2">
                    <div role="tabpanel" class="col-md-12">   
                        <div class="form-group">
                            <input type="hidden" name="teacherId" disabled>
                            <input type="text"  name="project" class="form-control"  disabled
                            placeholder="请输入老师中文名" value="<?php echo $model->name_cn;?>">
                        </div>
                    </div>
                    <div role="tabpanel" class="col-md-12">   
                        <div class="form-group">
                            <input type="hidden" name="teacherId" disabled>
                            <input type="text"  name="projects" class="form-control"  disabled
                            placeholder="请输入老师英文名" value="<?php echo $model->name_en;?>">
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="form-group">
                            <input type="text" class="form-control" name="teacherPhone"  disabled
                                placeholder="联系方式" value="<?php echo $model->cellphone;?>">
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="form-group">
                            <input type="text" class="form-control" name="teacherEmail"  disabled
                                placeholder="电子邮箱" value="<?php echo $model->email;?>">
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="form-group">
                            <select class="form-control" name="active" disabled>
                                <option value="1" <?php if($model->active == 1){echo 'selected';}?>><?php echo "有效";?></option>
                                <option value="0" <?php if($model->active == 0){echo 'selected';}?>><?php echo "无效";?></option>
                            </select>
                        </div>
                    </div>
                    <input type="hidden" name="teacherType" value="2" disabled/>
                </div>

                <div class="tab-pane" id="tab3">
                    <div role="tabpanel" class="col-md-12">   
                        <div class="form-group">
                            <input type="hidden" name="teacherId" disabled>
                            <input type="text"  name="project" class="form-control" disabled 
                            placeholder="请输入老师中文名" value="<?php echo $model->name_cn;?>">
                        </div>
                    </div>
                    <div role="tabpanel" class="col-md-12">   
                        <div class="form-group">
                            <input type="hidden" name="teacherId" disabled>
                            <input type="text"  name="projects" class="form-control"  disabled
                            placeholder="请输入老师英文名" value="<?php echo $model->name_en;?>">
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="form-group">
                            <input type="text" class="form-control" name="teacherPhone" disabled
                                placeholder="联系方式" value="<?php echo $model->cellphone;?>">
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="form-group">
                            <input type="text" class="form-control" name="teacherEmail" disabled 
                                placeholder="电子邮箱" value="<?php echo $model->email;?>">
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="form-group">
                            <select class="form-control" name="active" disabled>
                                <option value="1" <?php if($model->active == 1){echo 'selected';}?>><?php echo "有效";?></option>
                                <option value="0" <?php if($model->active == 0){echo 'selected';}?>><?php echo "无效";?></option>
                            </select>
                        </div>
                    </div>
                    <input type="hidden" name="teacherType" value="3" disabled/>
               
                </div>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <label id="J_fail_info" class="text-warning" style="display: none;"><i
                class="glyphicon glyphicon-remove text-warning"></i><span></span></label>
        <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
        <button type="button" class="btn btn-primary J_ajax_submit_btn"><?php echo $model->vendor_id?'更新老师':'添加老师'; ?></button>
        <input type="reset" class="_reset" style="display: none"/>
    </div>

<script>
    var projects = <?php echo CJSON::encode($teacherList);?>;

    $("#project").autocomplete({
        minLength: 0,
        source: projects,
        focus: function (event, ui) {
            $("#project").val(ui.item.value);
            if (ui.item.teacherPhone !== '') {
                $("#teacherPhone").val(ui.item.teacherPhone).attr('readonly', 'readonly');
            } else {
                $("#teacherPhone").val('').removeAttr('readonly');
            }

            if (ui.item.teacherEmail !== '') {
                $("#teacherEmail").val(ui.item.teacherEmail).attr('readonly', 'readonly');
            } else {
                $("#teacherEmail").val('').removeAttr('readonly');
            }
            return false;
        },
        select: function (event, ui) {
            $("#project").val(ui.item.value);
            $("#teacherId").val(ui.item.teacherId);
            return false;
        }
    });


        <?php if($model->vendor_id == 0){?>
/*        alert(1);
        die;*/
        //表示点击添加老师时触发事件
        $(function () {
            $('#myTab a:first').tab('show');
        })
        <?php }else{?>
/*            alert(2);
            die;*/
        //表示点击编辑老师时触发的事件
        $(function () {
            var val = $('#keys').val();            

            $('#myTab > li').eq(val-1).addClass('active');
            $('.tab-content > .tab-pane').eq(val-1).addClass('active');
            $('.tab-content > .tab-pane').eq(0).find('input,select').attr('disabled','disabled');
            $('.tab-pane.active').find('input,select').removeAttr('disabled');

         })
        <?php }?>

        //var checktaball = $(".tab-pane");
        $('.tab-pane' + 'input').attr('disabled', 'disabled');
        //$('.tab-pane' + 'select').attr('disabled', 'disabled');
        $('.tab-pane' + 'option').attr('disabled', 'disabled');

        $('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
            var checktabname = $(e.target).attr('checktab');// 激活的标签页
            $('#' + checktabname + ' :input').removeAttr('disabled');

            var checkdtabname = $(e.relatedTarget).attr('checktab');// 前一个激活的标签页
            $('#' + checkdtabname + ' :input').attr('disabled', 'disabled');
        })

</script>

<?php $this->endWidget(); ?>