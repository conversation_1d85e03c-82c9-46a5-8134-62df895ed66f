<div class="h_a">添加活动</div>

<?php $form=$this->beginWidget('CActiveForm', array(
	'id'=>'event-form',
	'enableAjaxValidation'=>false,
    'htmlOptions'=>array('class'=>'J_ajaxForm'),
));
?>
<div class="table_full">
    <table width="100%" class="" style="margin-bottom:0;">
        <col width="150" />
        <col width="400" />
        <col />
        <tbody>
            <tr>
                <th><?php echo $form->labelEx($model,'code'); ?></th>
                <td>
                    <?php echo $form->textField($model,'code',array('maxlength'=>255,'class'=>'input length_6')); ?>
                </td>
                <td><div class="fun_tips"><?php echo $form->error($model,'code'); ?></div></td>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($model,'en_title'); ?></th>
                <td>
                    <?php echo $form->textField($model,'en_title',array('maxlength'=>255,'class'=>'input length_6')); ?>
                </td>
                <td><div class="fun_tips"><?php echo $form->error($model,'en_title'); ?></div></td>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($model,'cn_title'); ?></th>
                <td>
                    <?php echo $form->textField($model,'cn_title',array('maxlength'=>255,'class'=>'input length_6')); ?>
                </td>
                <td><div class="fun_tips"><?php echo $form->error($model,'cn_title'); ?></div></td>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($model,'school_id'); ?></th>
                <td>
                    <?php echo $form->dropDownList($model,'school_id',$this->branchArr, array('onchange'=>'show1(this)')); ?>
                </td>
                <td><div class="fun_tips"><?php echo $form->error($model,'school_id'); ?></div></td>
            </tr>
            <tr id="e_show" style="<?php if ($model->school_id != 'BJ_TYG'):?>display:none;<?php endif;?>">
                <th></th>
                <td>
                    <?php echo CHtml::checkBoxList('SchoolEventsShow[schoolid][]', $sArray, $this->branchArr);?>
                </td>
                <td></td>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($model,'event_date'); ?></th>
                <td>
                    <?php
                    $this->widget('zii.widgets.jui.CJuiDatePicker', array(
                        "model"=>$model,
                        "attribute"=>"event_date",
                        "options"=>array(
                            'changeMonth'=>true,
                            'changeYear'=>true,
                            'dateFormat'=>'yy-mm-dd',
                        ),
                        'htmlOptions'=>array(
                            'class'=>'input'
                        ),
                    ));
                    ?>
                </td>
                <td><div class="fun_tips"><?php echo $form->error($model,'event_date'); ?></div></td>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($model,'apply_duedate'); ?></th>
                <td>
                    <?php
                    $this->widget('zii.widgets.jui.CJuiDatePicker', array(
                        "model"=>$model,
                        "attribute"=>"apply_duedate",
                        "options"=>array(
                            'changeMonth'=>true,
                            'changeYear'=>true,
                            'dateFormat'=>'yy-mm-dd',
                        ),
                        'htmlOptions'=>array(
                            'class'=>'input'
                        ),
                    ));
                    ?>
                </td>
                <td><div class="fun_tips"><?php echo $form->error($model,'apply_duedate'); ?></div></td>
            </tr>
            <?php if ($model->new_poster_en):?>
            <tr>
                <th><?php echo $form->labelEx($model,'new_poster_en'); ?></th>
                <td>
                    <?php echo CHtml::image(Yii::app()->params['OAUploadBaseUrl'].'/event/'.$model->new_poster_en, '', array('style'=>'width:360px;')); ?>
                </td>
                <td></td>
            </tr>
            <?php endif;?>
            <tr>
                <th><?php echo $form->labelEx($model,'poster_en'); ?></th>
                <td>
                    <?php echo $form->fileField($model,'poster_en'); ?>
                </td>
                <td><div class="fun_tips">950x400</div></td>
            </tr>
            <?php if ($model->new_poster_cn):?>
            <tr>
                <th><?php echo $form->labelEx($model,'new_poster_cn'); ?></th>
                <td>
                    <?php echo CHtml::image(Yii::app()->params['OAUploadBaseUrl'].'/event/'.$model->new_poster_cn, '', array('style'=>'width:360px;')); ?>
                </td>
                <td></td>
            </tr>
            <?php endif;?>
            <tr>
                <th><?php echo $form->labelEx($model,'poster_cn'); ?></th>
                <td>
                    <?php echo $form->fileField($model,'poster_cn'); ?>
                </td>
                <td><div class="fun_tips">950x400</div></td>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($model,'stat'); ?></th>
                <td>
                    <?php echo $form->radioButtonList($model, 'stat', $this->cfg['eventstat']); ?>
                </td>
                <td><div class="fun_tips"><?php echo $form->error($model,'stat'); ?></div></td>
            </tr>
        </tbody>
    </table>
</div>
<div class="btn_wrap">
    <div class="btn_wrap_pd">
        <button class="btn btn_submit mr10 J_ajax_submit_btn"><?php echo Yii::t('global','Submit');?></button>
    </div>
</div>
<?php $this->endWidget(); ?>

<script type="text/javascript">
function show1(_this)
{
    if ( $(_this).val() == 'BJ_TYG') {
        $('#e_show').show();
    }
    else {
        $('#e_show').hide();
    }
}
</script>