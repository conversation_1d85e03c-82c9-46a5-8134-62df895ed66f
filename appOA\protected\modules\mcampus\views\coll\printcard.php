<?php
if ($this->branchObj->type == 50) {
// $imgSrc='http://m2.files.ivykids.cn/cloud01-file-8025768FtLJ88p3PQvUtBNeUSKGQPXQBynO.jpg';
// $logoWdith='imgCss';
// $imgSrc='http://m2.files.ivykids.cn/cloud01-file-5FpdD6zT4ph_0GlllvUzvxebzo_eM.jpg';
 $imgSrc='https://m2.files.ivykids.cn/cloud01-file-5FlQzKJovaAD4S53-EuvacnHxpkC7.jpg';
 $logoWdith='hide';
}else {
	$imgSrc='http://m2.files.ivykids.cn/cloud01-file-8025768FqoRUgyX5mkN_M9pWPUH9fEAKZvK.jpg';
	$logoWdith='ivyimgCss';
}
if($this->branchId == 'BJ_QFF'){
    $imgSrc='https://m2.files.ivykids.cn/cloud01-file-8025768Fvhwd17XZV4h5Tm4E6j4YLlXZFl9.png';
    $logoWdith='hide';
} 
$duplexPrint = Yii::app()->request->getPost('duplex_print', '');
?>
<style>
<?php if(Yii::app()->language == 'en_us'):?>
/*@font-face {*/
/*    font-family: 'Myriad Pro';*/
/*    src: url('*/<?php //echo Yii::app()->themeManager->baseUrl; ?>/*/base/fonts/MyriadPro-Regular.woff2') format('woff2');*/
/*    font-weight: normal;*/
/*    font-style: normal;*/
/*    font-display: swap;*/
/*}*/
body {
    margin: 0;
    padding: 0;
    font-family: 'Myriad Pro';
    font-weight: normal;
    font-style: normal;
}
<?php else:?>
/*@font-face {*/
/*    font-family: 'SourceHanSans';*/
/*    src: url('*/<?php //echo Yii::app()->themeManager->baseUrl; ?>/*/base/fonts/SourceHanSansCN-VF.otf.woff2') format('woff2');*/
/*    font-weight: normal;*/
/*    font-style: normal;*/
/*    font-display: swap;*/
/*}*/
body {
    margin: 0;
    padding: 0;
    font-family: '思源黑体 CN';
    font-weight: normal;
    font-style: normal;
}
<?php endif;?>
.text-center {
    text-align: center;
}
.page {
    width: 210mm;
    min-height: 297mm;
    margin: 10mm auto;
    background: white;
    box-shadow: 0 0 0.5cm rgba(0,0,0,0.5);
}
.split{
    page-break-after: always;
}
.clearfix:before {
    content: " ";
    display: table;
}
.clearfix:after {
    clear: both;
}
.card-box {
    width: 142mm;
    height: 106mm;
    margin: 0 auto;
    position: relative;
}
.card-bg {
    width: 142mm;
    height: 106mm;
    position: absolute;
    left: 0;
    top: 0;
}
.card-box .card-content {
    width: 136mm;
    height: 101mm;
    position: absolute;
    left: 3mm;
    top: 3mm;
    display: flex;
    overflow: hidden;
}
.box-half {
    width: 68mm;
}
.box-left {
    padding-left: 6mm;
    padding-top: 9mm;
}
.child-avatar {
    width: 25mm;
    height: 25mm;
    margin-bottom: 3mm;
    border-radius: 1mm;
}
.text-1 {
    font-size: 3mm;
    font-weight: bold;
    margin-bottom: 2mm;
}
.text-2 {
    font-size: 2.4mm;
    width: 25mm;
    height: 4mm;
    margin-bottom: 6mm;
}
.text-3 {
    font-size: 4mm;
    font-weight: bold;
    margin-bottom: 2mm;
}
.sub-box-1 {
    float: left;
    margin-right: 3mm;
}
.sub-box-2 {
    margin-left: 16mm;
}
.sub-box-3 {
    color: rgb(255, 255, 255);
    font-size: 4.2mm;
    position: absolute;
    left: 110mm;
    bottom: 4mm;
    height: 4.2mm;
}
.parent-avatar {
    margin: 25mm 18mm 3mm 0;
    width: 31mm;
    height: 31mm;
    border-radius: 1mm;
}
.card-box-2 {
    width: 70mm;
    height: 100mm;
    position: relative;
}
.card-2-bg {
    width: 70mm;
    height: 100mm;
}
.card-2-content {
    position: absolute;
    left: 3mm;
    top: 3mm;
    width: 68mm;
    height: 101mm;
}
.card-2-content-1 {
    position: absolute;
    left: 10mm;
    top: 9mm;
}
.warp-2 {
    width: 160mm;
    margin: 0 auto;
}
.page-1 {
    display: flex;
    flex-wrap: wrap;
    /*justify-content: space-between;*/
}
.sub-box-4 {
    color: rgb(255, 255, 255);
    font-size: 4.2mm;
    position: absolute;
    left: 40mm;
    bottom: 4mm;
    height: 4.2mm;
}
.tips-block {
    text-align: center;
    font-size: 14px;
    background-color: #ffecba;
    top: 0;
    left: 0;
    width: 100%;
    height: 44px;
    line-height: 44px;
    z-index: 9;
}
.mb16 {
    margin-bottom: 16px;
}
.mt-16 {
    margin-top: -16px;
}
.web-only input {
    padding: 4px;
    width: 66px;
}
.web-only span{
    font-size: 12px;
    color: #666;
    margin-left: 16px;
}
@media print {
    .web-only {
        display: none;
    }
}
</style>
<div>
    <div class="tips-block mb16 web-only">使用本打印功能，请确保您的电脑已安装下列字体 <a href="http://mega.ivymik.cn/fonts%2FMyriadPro-Regular.otf">MyriadPro</a>&nbsp;&nbsp;<a href="http://mega.ivymik.cn/fonts%2FSourceHanSansCN-Regular_0.otf">思源黑体</a> （下载后双击打开进行安装）</div>
    <?php
    function schoolYearLess($schoolYear)
    {
        if ($schoolYear) {
            $schoolYear = trim($schoolYear);
            return substr($schoolYear, 0, 5) . substr($schoolYear, 7);
        }
        return $schoolYear;
    }
    function classLess($classStr = '')
    {
        $classStr = trim($classStr);
        $prune = array('A', 'B', 'C', 'D', 'E', 'F');
        $endStr = substr($classStr, -1, 1);
        if (in_array($endStr, $prune)) {
            return substr($classStr, 0, -1);
        }
        return $classStr;
    }
    if (!$duplexPrint):
    $i=0;
    foreach ($data['pickups'] as $item):
    ?>
	<div class="card-box <?php echo ($i+1)%2==0?'split':''; ?>">
        <img src="<?php echo $imgSrc;?>" alt="" class="card-bg">
        <div class="card-content">
            <div class="box-half">
                <div class="box-left">
                    <?php foreach ($item['children'] as $child):?>
                    <div class="sub-box-1">
                        <img src="<?php echo $child['avatar'];?>" alt="" class="child-avatar">
                        <div class="text-1"><?php echo $child['name'];?></div>
                        <svg class="text-2">
                            <text font-size="2.4mm" y="12" x="0" fill="#000" >
                                <?php
                                if ($this->branchObj->type == 50 || $this->branchId == 'BJ_QFF') {
                                    echo classLess(schoolYearLess($child['className']));
                                }
                                else {
                                    echo schoolYearLess($child['className']);
                                }
                                ?>
                            </text>
                        </svg>
                    </div>
                    <?php endforeach;?>
                    <div class="clearfix"></div>
                </div>
            </div>
            <div class="box-half box-right">
                <div class="sub-box-2">
                    <img src="<?php echo $item['head_url'];?>" alt="" class="parent-avatar">
                    <div class="text-3"><?php echo Yii::t('global','Name') . Yii::t('global',': ') . trim($item['name']);?></div>
                    <div class="text-3"><?php echo Yii::t('asa','Phone') . Yii::t('global',': ') . trim($item['phone']);?></div>
                    <div class="text-3"><?php echo Yii::t('site','Relationship') . Yii::t('global',': ') . trim($item['relation']);?></div>
                </div>
                <svg class="sub-box-3" style="bottom: 6mm;">
                    <text font-size="4.2mm" y="14" x="10" fill="#ffffff">
                        <?php echo $data['school_title']; ?>
                    </text>
                </svg>
                <svg class="sub-box-3" style="bottom: 1mm;">
                    <text font-size="4.2mm" y="14" x="10" fill="#ffffff">
                        <?php echo schoolYearLess($data['start_year']); ?>
                    </text>
                </svg>
            </div>
        </div>
	</div>
    <?php
    $i++;
    endforeach;
    else:?>
        <div class="tips-block mb16 mt-16 web-only">
            <label>打印机偏移量(mm)：</label>
            <input type="number" id="offset_print" value="0" onchange="offsetPrint(this)" min="-10" max="10">
            <span>请打印出样张测量后调整</span>
        </div>
    <?php
    foreach (array_chunk($data['pickups'], 4) as $i=>$item1):
    ?>
    <div class="warp-2">
        <div class="page-1" id="duplex_page_one">
        <?php foreach ($item1 as $j=>$item):?>
        <div class="card-box-2">
<!--            <img src="http://m2.files.ivykids.cn/cloud01-file-5FpwBl_RlIsRfqfGEK9uo9k8dF3Al.jpg" class="card-2-bg" alt="">-->
            <img src="https://m2.files.ivykids.cn/cloud01-file-5FjrRMOaDUBmk3ZniA0L0y2grS7fd.jpg" class="card-2-bg" alt="">
            <div class="card-2-content">
                <div class="sub-box-2">
                    <img src="<?php echo $item['head_url'];?>" alt="" class="parent-avatar">
                    <div class="text-3"><?php echo Yii::t('global','Name') . Yii::t('global',': ') . trim($item['name']);?></div>
                    <div class="text-3"><?php echo Yii::t('asa','Phone') . Yii::t('global',': ') . trim($item['phone']);?></div>
                    <div class="text-3"><?php echo Yii::t('site','Relationship') . Yii::t('global',': ') . trim($item['relation']);?></div>
                </div>
                <svg class="sub-box-4" style="bottom: 11mm;">
                    <text font-size="4.2mm" y="14" x="0" fill="#ffffff" >
                        <?php echo $data['school_title']; ?>
                    </text>
                </svg>
                <svg class="sub-box-4" style="bottom: 6mm;">
                    <text font-size="4.2mm" y="14" x="0" fill="#ffffff" >
                        <?php echo schoolYearLess($data['start_year']); ?>
                    </text>
                </svg>
            </div>
        </div>
        <?php endforeach;?>
        </div>
        <div class="split"></div>
        <div class="page-1" style="justify-content:right;">
        <?php
        $count = count($item1);
        if ($count%2!=0) {
            $item1[$count] = null;
        }
        foreach ($item1 as $j=>$item):
            $jj = ($j%2==0) ? $j+1 : $j-1;
            $item = $item1[$jj];
        ?>
        <div class="card-box-2">
<!--            <img src="http://m2.files.ivykids.cn/cloud01-file-5Fou0Oe8BLimvgcAp30iB8RG4R8PJ.jpg" class="card-2-bg" alt="">-->
            <img src="https://m2.files.ivykids.cn/cloud01-file-5Fl_oBnW7AEr462szdsF6UiEcK61l.jpg" class="card-2-bg" alt="">
            <div class="card-2-content-1">
                <?php foreach ($item['children'] as $child):?>
                    <div class="sub-box-1">
                        <img src="<?php echo $child['avatar'];?>" alt="" class="child-avatar">
                        <div class="text-1"><?php echo $child['name'];?></div>
                        <svg class="text-2">
                            <text font-size="2.4mm" y="12" x="0" fill="#000" >
                                <?php echo classLess(schoolYearLess($child['className']));?>
                            </text>
                        </svg>
                    </div>
                <?php endforeach;?>
                <div class="clearfix"></div>
            </div>
        </div>
        <?php endforeach;?>
        </div>
    </div>
    <div class="split"></div>
    <?php endforeach;?>
    <?php endif;?>
</div>

<script>
    function offsetPrint(e)
    {
        $('#duplex_page_one').css('margin-left', $(e).val()+'mm')
    }
</script>
