<?php

/**
 * This is the model class for table "ivy_p_account".
 *
 * The followings are the available columns in table 'ivy_p_account':
 * @property integer $id
 * @property string $title
 * @property integer $status
 * @property integer $ufida_status
 * @property integer $create_uid
 * @property integer $udate_timestamp
 */
class IvyPAccount extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @return IvyPAccount the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_p_account';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('title, update_timestamp', 'required'),
			array('status, ufida_status, create_uid, update_timestamp', 'numerical', 'integerOnly'=>true),
			array('title', 'length', 'max'=>255),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, title, status, ufida_status, create_uid, update_timestamp', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => Yii::t('operations', '标识'),
			'title' => Yii::t('operations', '标题'),
			'status' =>  Yii::t('operations', '状态'),
			'ufida_status' => 'Ufida Status',
			'create_uid' => 'Create Uid',
			'update_timestamp' => Yii::t('operations', '更新日期')
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('title',$this->title,true);
		$criteria->compare('status',$this->status);
		$criteria->compare('ufida_status',$this->ufida_status);
		$criteria->compare('create_uid',$this->create_uid);
		$criteria->compare('update_timestamp',$this->update_timestamp);

		return new CActiveDataProvider(get_class($this), array(
			'criteria'=>$criteria,
		));
	}
	
	/**
	 * 显示科目的状态
	 * @return string
	 * <AUTHOR>
	 * @copyright IvyGroup
	 */
	public function showStatus()
	{
		return ($this->status) ? Yii::t('operations', '隐藏') : Yii::t('operations', '显示');
	}
	
 	/**
	 * 取科目的名称
	 * @return ArrayIterator;
	 * <AUTHOR>
	 * @copyright IvyGroup
	 */
	public function getNameInfo()
	{
		$nameList = array();
		$criteria = new CDbCriteria;
		$criteria->select = 'id,title';
		$criteria->compare('status', 0);
		$nameInfo = $this->findAll($criteria);
		if (!empty($nameInfo))
		{
			foreach ($nameInfo as $v)
			{
				$nameList[$v->id] = $v->title;
			}
		}
		return $nameList;
	}
}