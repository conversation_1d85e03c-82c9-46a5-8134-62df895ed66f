<?php if ($schoolid):?>
    <div class="member">
        <ul>
            <?php foreach ($ptas as $pta):?>
            <li class="item">
                <label>
                <?php echo CHtml::image('http://oa.ivyonline.cn/uploads/users/thumbs/'.$pta->user->user_avatar, '', array('id'=>'avatar_'.$pta->user->uid))?>
                <div><input type="checkbox" name="member" value="<?php echo $pta->user->uid?>" <?php echo in_array($pta->user->uid, $usered) ? 'checked' : ''?>><span id="name_<?php echo $pta->user->uid?>" class="fljs"><?php echo CommonUtils::autoLang($pta->user->name, $pta->profile->first_name.' '.$pta->profile->last_name);?></span></div>
                </label>
            </li>
            <?php endforeach;?>
            <li class="clear"></li>
        </ul>
    </div>
    <div class="pop_bottom">
        <button type="button" class="btn fr" id="J_dialog_close"><?php echo Yii::t('global','Cancel');?></button>
        <button class="btn fr btn_submit mr10" onclick="assignname();">确定</button>
    </div>
<?php else:?>
    <h2><center>请先选择学校</center></h2>
<?php endif; ?>

<style>
    .member{
        overflow-y: auto;
        max-height: 300px;
        padding: 0 5px;
    }
    .member li.item{
        float: left;
        margin: 5px;
        width: 70px;
        height: 90px;
        overflow: hidden;
    }
    .member ul{
        margin-bottom: 20px;
    }
    .member li.item img{
        width: 70px;
        height: 70px;
    }
</style>

<script>
function assignname()
{
    var h = '', v = '';
    $('.member input[name="member"]').each(function(){
        var _this = $(this);
        if (_this.attr('checked') == 'checked'){
            v += _this.val()+',';
            h += '<img src="'+$("#avatar_"+_this.val()).attr("src")+'" style="width:30px;"> ';
        }
    });
    parent.document.getElementById("PtaMemo_pta_members").value = v;
    parent.document.getElementById("ptamembers").innerHTML = h;
    if(window.parent.head.dialog) {
        window.parent.head.dialog.closeAll();
    }
}
</script>