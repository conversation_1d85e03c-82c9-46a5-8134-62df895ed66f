<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
    <h4 class="modal-title" id="exampleModalLabel"><?php echo Yii::t('site','Inventory management') ?></h4>
</div>
<div class="modal-body">
    <?php
    $form=$this->beginWidget('CActiveForm', array(
        'id'=>'courseGroup-form',
        'enableAjaxValidation'=>false,
        'htmlOptions'=>array('class'=>'J_ajaxForm form-horizontal', 'role'=>'form'),
    )); ?>
    <div class="form-group">

        <label class="col-xs-2 control-label"><?php echo Yii::t("site", "Quantity") ?> *</label>
        <div class="col-xs-4">
            <?php echo CHtml::textField('num', "", array('maxlength'=>255,'class'=>'form-control')); ?>
            <?php echo CHtml::HiddenField("paid", $paid); ?>
            <?php echo CHtml::HiddenField("psid", $stockModel->id); ?>
        </div>
        <label class="col-xs-2 control-label"><?php echo Yii::t("site", "Current inventory") ?></label>
        <div class="col-xs-3">
            <label class="control-label"><?php echo (isset($stockModel) && $stockModel->id)? $stockModel->num :  '0' ; ?></label>
        </div>

    </div>
    <div class="form-group">
    </div>
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo Yii::t('invoice','Memo') ?> *</label>
        <div class="col-xs-9">
            <?php echo CHtml::textArea('memo', "", array('class'=>'form-control')); ?>
        </div>
    </div>
    <?php if($count): ?>
        <div class="form-group">
            <label class="col-xs-2 control-label"><?php echo Yii::t('invoice','缺货登记') ?></label>
            <div class="col-xs-9">
                <label class="control-label"><?php echo CHtml::checkBoxList('isArrival', 1, array(1 => "缺货登记"), array('class'=>'control'));?></label>
            </div>
        </div>
    <?php endif; ?>
    <div class="col-md-2 col-md-offset-10"><button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global','Submit');?></button>
    </div>
    <hr/>
    <div class="clearfix"></div>
    <?php if($dataProvider): ?>
        <div class="form-group">
            <label class="col-xs-2 control-label"><?php echo Yii::t('site','Inventory details have been added') ?></label>
            <div class="col-xs-9">
                <?php
                $this->widget('ext.ivyCGridView.BsCGridView', array(
                    'id'=>'confirmed-visit-grid',
                    'afterAjaxUpdate'=>'js:head.Util.modal',
                    'dataProvider'=>$dataProvider,
                    'template'=>"{items}{pager}{summary}",
                    'colgroups'=>array(/*
                        array(
                            "colwidth"=>array(100, 700, 100, 100),
                        )*/
                    ),
                    'columns'=>array(
                        array(
                            'name'=> Yii::t('site','Quantity'),
                            'type'=>'raw',
                            'value'=>'$data->num',
                        ),
                        array(
                            'name'=> Yii::t('invoice','Memo'),
                            'type'=>'raw',
                            'value'=>'$data->memo',
                        ),
                        array(
                            'name'=> Yii::t('labels','Created Time'),
                            'type'=>'raw',
                            'value'=>'date("Y-m-d", $data->updated_time)',
                        ),
                        array(
                            'name'=> Yii::t('invoice','Created By'),
                            'type'=>'raw',
                            'value'=> array($this, 'getUserName'),
                        ),
                    ),
                ));
                ?>
            </div>
        </div>
    <?php endif; ?>
</div>
<script>
    function cbSchoolshare(){
        location.reload();
    }
</script>
<?php $this->endWidget(); ?>