
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site','Campus Workspace'))?></li>
        <li class="active"><?php echo Yii::t('site','Routines');?></li>
    </ol>
    <div class="row">
        <div class="col-md-2">
            <?php
            $mainMenu = array(
                array('label'=>Yii::t('user','入职待审'), 'url'=>array("//mcampus/staff/index","category"=>"join")),
                array('label'=>Yii::t('user','离职待审'), 'url'=>array("//mcampus/staff/index","category"=>"resign")),
            );

            $this->widget('zii.widgets.CMenu',array(
                'items'=> $mainMenu,
                'htmlOptions'=>array('class'=>'nav nav-pills nav-stacked text-right background-gray'),
                'activeCssClass'=>'active',
                'itemCssClass'=>''
            ));
            ?>
        </div>
        <div class="col-md-10">
            <?php
                switch ($category):
                    case 'join':
                        echo $this->renderPartial('staff/_join',array("dataProvider"=>$dataProvider));
                        break;
                    case 'update':
                        echo $this->renderPartial('staff/_addForm',array("model"=>$model));
                        break;
                    default:
                        echo $this->renderPartial('staff/_checkForm',array("model"=>$model));
                        break;
                endswitch;
            ?>
            <?php // echo $this->renderPartial('staff/_'.$category,array("dataProvider"=>$dataProvider));?>
        </div>
    </div>
</div>

<!-- Modal -->
<?php
    $model = new UserLeaveApplication();
?>
<div class="modal" id="leave-applicate-edit-modal" tabindex="-1" role="dialog" aria-labelledby="leaveApplicateEditModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title" id="myModalLabel">离职登记申请 <small></small></h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-12">
                        <div class="well">
                            说明
                        </div>
                        <form class="form-horizontal J_ajaxForm" action="<?php echo $this->createUrl('//mcampus/');?>" method="POST" role="form">
                        <?php $form=$this->beginWidget('CActiveForm', array(
                                'id'=>'leave-form',
                                'enableAjaxValidation'=>false,
                                'htmlOptions'=>array('class'=>'J_ajaxForm form-horizontal'),
                            ));
                        ?>
                            <div class="form-group">
                                <?php echo $form->labelEx($model,'uid',array('class'=>"col-sm-3 control-label")); ?>
                                <div class="col-sm-9">
                                    <?php echo $form->textField($model,'uid',array('maxlength'=>255,'class'=>'form-control')); ?>
                                </div>
                            </div>
                            <div class="form-group">
                                <?php echo $form->labelEx($model,'application_date',array('class'=>"col-sm-3 control-label")); ?>
                                <div class="col-sm-9">
                                    <?php
                                        $this->widget('zii.widgets.jui.CJuiDatePicker', array(
                                            "model" => $model,
                                            "attribute" => "application_date",
                                            "options" => array(
                                                'changeMonth' => true,
                                                'changeYear' => true,
                                                'dateFormat' => 'yy-mm-dd',
                                            ),
                                            "htmlOptions" => array(
                                                'class' => 'form-control'
                                            )
                                        ));
                                    ?>
                                </div>
                            </div>
                            <div class="form-group">
                                <?php echo $form->labelEx($model,'leave_date',array('class'=>"col-sm-3 control-label")); ?>
                                <div class="col-sm-9">
                                    <?php
                                        $this->widget('zii.widgets.jui.CJuiDatePicker', array(
                                            "model" => $model,
                                            "attribute" => "leave_date",
                                            "options" => array(
                                                'changeMonth' => true,
                                                'changeYear' => true,
                                                'dateFormat' => 'yy-mm-dd',
                                            ),
                                            "htmlOptions" => array(
                                                'class' => 'form-control'
                                            )
                                        ));
                                    ?>
                                </div>
                            </div>
                            <div class="form-group">
                                <?php echo $form->labelEx($model,'cause',array('class'=>"col-sm-3 control-label")); ?>
                                <div class="col-sm-9">
                                    <?php echo $form->checkBoxList($model,'cause',$this->causeCfg,array('maxlength'=>255,'class'=>'')); ?>
                                </div>
                            </div>
                            <div class="form-group">
                                <?php echo $form->labelEx($model,'memo',array('class'=>"col-sm-3 control-label")); ?>
                                <div class="col-sm-9">
                                    <?php echo $form->textArea($model,'memo',array('maxlength'=>255,'class'=>'form-control')); ?>
                                </div>
                            </div>
                        <?php $this->endWidget(); ?>
                        <div class="pop_bottom">
                            <button onclick="$('#leave-applicate-edit-modal').modal('hide')" type="button" class="btn btn-default pull-right"><?php echo Yii::t('global','Cancel');?></button>
                            <button type="submit" class="btn btn-primary J_ajax_submit_btn pull-right mr10"><?php echo Yii::t('global','Submit');?></button>
                        </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

$this->branchSelectParams['extraUrlArray'] = array('//mcampus/staff/index');
$this->renderPartial('//layouts/common/branchSelectBottom');
?>
<script>
 $(function(){
    leaveApplication = function(){
        $('#leave-applicate-edit-modal').modal();
    }
 })
</script>