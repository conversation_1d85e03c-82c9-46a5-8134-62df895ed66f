<?php

class EnterpriseController extends BranchBasedController
{
    public $demoFlag = '-demo-'; //必须与通知服务器一致
    public $userModel = array();
    public $actionAccessAuths = array(
        'Index'             => 'o_A_Access',
    );

    public function createUrl($route, $params = array(), $ampersand = '&')
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        return parent::createUrl($route, $params, $ampersand);
    }

    public function init(){
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";
        Yii::import('common.models.wechat.*');
        $this->modernMenuFlag = 'campusOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site','Campus Workspace');
        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/vue2.js');
        // $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/xlsx.full.min.js');
        // $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/echarts.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/plupload/plupload.full.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/qiniu.min.js');
        // $cs->registerCssFile($cs->getCoreScriptUrl().'/jui/css/base/jquery-ui.css');
        // $cs->registerScriptFile($cs->getCoreScriptUrl().'/jui/js/jquery-ui.min.js');
        $this->branchSelectParams['hideOffice'] = false;
        $this->branchSelectParams['urlArray'] = array('//mcampus/enterprise/index');
    }


    public function actionIndex()
    {
        $criteria = new CDbCriteria;
        $criteria->compare('t.isstaff', 1);
        $criteria->compare('t.level', 1);
        $criteria->compare('profile.branch', $this->branchId);
        $criteria->with = 'profile';
        $criteria->index = 'uid';
        $this->userModel = User::model()->findAll($criteria);
        $dataProvider = '';
        if($this->userModel) {
            $criteria = new CDbCriteria;
            $criteria->compare('t.category', 'signature');
            $criteria->compare('t.belong_user', array_keys($this->userModel));
            $criteria->compare('t.status', 1);
            $dataProvider = new CActiveDataProvider('WxworkFile', array(
                'criteria' => $criteria,
                'sort' => array(
                    //'defaultOrder' => 'uname asc',
                ),
            ));
        }
        $this->render('index',array(
            'dataProvider'=>$dataProvider,
            'userModel'=>$this->userModel,
        ));
    }

    public function getUserName($data)
    {
        echo ($this->userModel[$data->belong_user]) ? $this->userModel[$data->belong_user]->getName() : $data->belong_user;
    }

    public function getOssImageUrl($data)
    {
        if($data->src){
            $osscs = CommonUtils::initOSSCS('private');
            $style = 'image/resize,h_100';
            $src = $osscs->getImageUrl($data->src, $style);

            echo '<img src="' . $src . '"/>';
        }
    }
}

