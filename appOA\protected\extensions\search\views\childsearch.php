<script>
function trim(str){
    return str.replace(/(^\s*)|(\s*$)/g, "");
}
function nl2br (str, is_xhtml) {
	var breakTag = (is_xhtml || typeof is_xhtml === 'undefined') ? '<br />' : '<br>';     
	return (str + '').replace(/([^>\r\n]?)(\r\n|\n\r|\r|\n)/g, '$1'+ breakTag +'$2'); 
}
function fly(t, id)
{
    var url;
    switch(t)
    {
        case 'c':
            url = '<?php echo Yii::app()->createUrl('/child/index/index')?>?childid='+id;
        break;
        case 'f':
            url = '<?php echo Yii::app()->createUrl('/child/index/cCUrl')?>?childid='+id;
        break;
    }
    window.open(url);
}
function closePop(){
	jQuery('#'+'<?php echo $this->name;?>').val(""); 
	jQuery('#'+'<?php echo $this->name;?>').data( 'autocomplete' ).term=""; 
	jQuery('#'+'<?php echo $this->name;?>').data( 'autocomplete' ).close(); 
}
</script>
<label for="<?php echo $this->name;?>" class="mr5">
	<?php echo Yii::t('navs','Child Search');?>
</label>
<?php
$hint = Yii::t("message", "Search by child ID, name, DOB, parent name, email, Phone NO.");

$this->widget('zii.widgets.jui.CJuiAutoComplete',array(
    'name'=>$this->name,
	'sourceUrl'=>Yii::app()->createUrl('//backend/search/childoutput', array('displayType'=>$this->simpleDisplay ? 'simpleDisplay' : '', 'withAlumni'=>'withAlumni')),
    'options'=>array(
        'minLength'=>'1',
		'delay'=>1000,
		'position'=> $this->popPosition,
		'select'=>'js:function(event,ui){return false;}',
    ),
    'htmlOptions'=>array(
		'class'=>'input ' . $this->inputCSS,
		'placeholder' => $hint,
		'title' => $hint
    ),
));


$js = "jQuery('#".$this->name."')";
$js .= ".data( 'autocomplete' )._renderItem = function( ul, item ) {
			if(item.value==-1){
				return $( '<li onclick=\'closePop();\'></li>' ).html('×').addClass('close').appendTo(ul);
			}else if(item.value==-2){
				return $( '<li></li>' ).addClass('full').data('item.autocomplete', item).append( '<div>' + item.label + '</div>' ).appendTo(ul);
			}else{
				var re = new RegExp( '(' + $.ui.autocomplete.escapeRegex(this.term) + ')', 'gi' );
				var highlightedResult = item.label.replace( re, '<b>$1</b>' );
				highlightedResult = nl2br(highlightedResult);
				var extra1 = (item.value==0) ? '' : '<a href=\'javascript:void(0)\' onclick=\"fly(\'c\','+item.value+')\"> ".Yii::t('message', 'Profile Detail')." </a>';
				var extra2 = (item.value==0) ? '' : '<a href=\'javascript:void(0)\' onclick=\"fly(\'f\','+item.value+')\"> ".Yii::t('message', 'Front End')." </a>';
				return $( '<li></li>' )
					.attr('cid',item.value)
					.data( 'item.autocomplete', item )
					.append( '<div class=\'info\'>' + highlightedResult + '</div>' + '<div class=\'op\'>' + extra1 + extra2 + '</div><div class=\'c\'></div>')
					.appendTo( ul );			
			}
        };";
$js .= "jQuery('#".$this->name."')";
$js .=".data('autocomplete').close = function(event){
			if(trim(this.term)!=''){
				return false;
			}else{
				if ( this.menu.element.is( ':visible' ) ) {
					this.menu.element.hide();
					this.menu.blur();
					this.isNewMenu = true;
					this._trigger( 'close', event );
				}
			}
		};";
$js .= "jQuery('#".$this->name."')";
$js .=".data('autocomplete')._resizeMenu = function(){
			var ul = this.menu.element;
			ul.outerWidth(600);
		};";
$js .= "jQuery('#".$this->name."')";
$js .=".data('autocomplete')._move = function( event ){
			return false;
		};";
$cs = Yii::app()->getClientScript();
$cs->registerScript(__CLASS__.'#'.$this->name, $js);
?>

<style>
.ui-autocomplete{
	max-height: 600px;
	overflow-y: auto;
}
.ui-menu .ui-menu-item a {
	text-align: right;
	color:#ff6600 !important;
}
.ui-menu .ui-menu-item a:hover {
	text-decoration: underline;
}
.ui-menu .ui-menu-item {
	border-bottom: 1px solid #dedede;
	font-family: "Microsoft Yahei";
	line-height: 20px;
}
.ui-menu .ui-menu-item:hover {
	background: #FFFFE9;
	border-bottom-color: #F4D5BA;
	color:#333;
}
a.ui-state-hover{
	background:none !important;
	border: 0 !important;
	margin: 0 !important;
}
.ui-menu .ui-menu-item a{
}
li.close{
	background:#f2f2f2;
	float: right;
	display: inline-block;
	width: 14px;
	height: 14px;
	text-align: center;
	margin: 2px 4px 0 0;
}
li.full{
	clear: both;
}
li.ui-menu-item .info{
	padding: 4px;
	width: 470px;
	float: left;
}
li.ui-menu-item .op{
	padding: 4px 0;
	float: right;
	width: 90px;
}
li.ui-menu-item .op a{
	display: block;
}
.info b{
	background:red;
	color:#fff;
	padding:0 2px;
}
</style>