<?php

$typeList = CommonUtils::LoadConfig('CfgASA');
$jobType = array();
foreach ($typeList['job_type'] as $k => $job_type) {
    $jobType[$k] = (Yii::app()->language == 'zh_cn') ? $job_type['cn'] : $job_type['en'];
}

$type = array();
foreach ($typeList['type'] as $k => $job_type) {
    $type[$k] = (Yii::app()->language == 'zh_cn') ? $job_type['cn'] : $job_type['en'];
}
$attendance_result = array();
foreach ($typeList['attendance_result'] as $k => $job_type) {
    $attendance_result[$k] = (Yii::app()->language == 'zh_cn') ? $job_type['cn'] : $job_type['en'];
}
?>
    <style>

        /*vue 防闪现*/
        [v-cloak] {
            display: none;
        }

        .bggray {
            background: #eee;
            border-radius: 4px;
        }

        .teacher_notsignin {
            border-radius: 4px;
            background: #eee;
            text-decoration: line-through
        }

        .teacher_spare {
            border-radius: 4px;
            background: #f7ecb5;
        }

        .bg-warning {
            background-color: #fcf8e3 !important;
        }

        .bg-success {
            background-color: #dff0d8 !important;
        }
        
        .invoiceLoading {
            background: url(/themes/blue/images/loading.gif) no-repeat scroll center center transparent;
            height: 50px;
        }
    </style>
    <div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('//mcampus/default/index')) ?></li>
        <li><?php echo CHtml::link(Yii::t('site', 'Routines'), array('//mcampus/default/index')) ?></li>
        <li class="active"><?php echo Yii::t('site', 'ASA Management') ?></li>
    </ol>

    <div class="row">
        <div class="col-md-2 mb10">
            <?php
            $this->widget('zii.widgets.CMenu', array(
                'items' => $this->module->getMenu(),
                'id' => 'pageCategory',
                'htmlOptions' => array('class' => 'nav nav-pills nav-stacked background-gray'),
                'activeCssClass' => 'active',
            ));
            ?>
        </div>
        <div class="col-md-2">
            <?php
            $this->widget('zii.widgets.CMenu', array(
                'items' => $this->module->getAttendSubMenu(),
                'id' => 'pageCategory',
                'htmlOptions' => array('class' => 'nav nav-pills nav-stacked background-gray'),
                'activeCssClass' => 'active',
            ));
            ?>
        </div>
        <div class="col-md-8">
            <?php
            $this->widget('zii.widgets.CMenu', array(
                'items' => $this->module->getAttendMenu(),
                'id' => 'AttendMenu',
                'htmlOptions' => array('class' => 'nav nav-tabs'),
                'activeCssClass' => 'active',
            ));
            ?>
            <!-- Tab panes -->
            <div class="tab-content">
                <div role="tabpanel" class="tab-pane pt10 active" id="day_attendance">

                    <div class="col-md-12 mb10">

                        <select autocomplete="off" class="form-control" style="width: auto" id="selectstartyear">
                            <option value="">请选择学年</option>
                            <?php
                            if(empty($_GET['year'])){
                                //默认最新的学年
                                $getUrlYear = $startyearArr[0];
                            }else{
                                $getUrlYear = $_GET['year'];
                            }
                            foreach ($startyearArr as $value){
                                if(Yii::app()->language == 'zh_cn'){
                                    $msg = ' 学年';
                                }else{
                                    $msg =' School Year';
                                }
                                $schoolYear = $value.'-'.($value+1).$msg;
                                if($value == $getUrlYear){
                                    echo "<option value='$value' selected>$schoolYear</option>";
                                }else{
                                    echo "<option value='$value'>$schoolYear</option>";
                                }
                            }
                            ?>
                        </select>
                        <br>
                        <label><?php echo commonUtils::addColon(Yii::t("asa", "Select an ASA project")); ?></label>
                        <?php
                        echo "<span class=span_year$year>";
                        $selected_title = "";
                        $program_type = "";
                        $groupStartTime = "";
                        $groupEndTime = "";

                        foreach ($newCourseGroup as $year => $courseGroup){
                            if($getUrlYear == $year){
                                echo "<ul class='nav nav-pills mb20 year$year'>";
                            }else{
                                echo "<ul class='nav nav-pills mb20 year$year' style='display: none'>";
                            }
                            foreach ($courseGroup as $v) {
                                $href = $this->createUrl('index', array('groupId' => $v['id'],'year'=>$year));
                                $active = '';
                                $title = $v['title'];
                                if (!empty($groupId) && $v['id'] == $groupId) {
                                    $active = 'active';
                                }
                                echo "<li class='background-gray mr5 mb5 {$active}'><a href='{$href}'>";
                                echo $title;
                                echo '</a></li>';
                            }
                            echo '</ul>';
                        }
                        echo '</span>';
                        ?>
                    </div>

                    <?php if ($groupId): ?>

                        <!-- <div class="col-md-12"> -->
                            <hr>
                            <div class="col-md-12 form-group" id="courseDate">
                                <label class="control-label" for="inputSuccess1">请选择课程日期</label>

                                <div class="datepicker mb10" name="" id=""></div>
                            </div>


                            <div class="col-md-12" id="vendorAttendVueModel" v-cloak>
                                <h3 id="selected-date-header">
                                    <span class="glyphicon glyphicon-calendar" aria-hidden="true"></span> {{ thedate
                                    }}</span>
                                    <small><?php echo Yii::t("asa", "Teachers' Attendance"); ?></small>
                                </h3>
                                <hr>
                                <div class="invoiceLoading" style="display: none"></div>
                                <!-- block start -->
                                <div class="row">
                                    <!-- block start -->
                                    <div id="" v-if="scheduleData.length == 0" class="row mb10">
                                        <div class="col-md-12">
                                            <div class="alert alert-warning text-center">
                                                <span class="glyphicon glyphicon-info-sign" aria-hidden="true"></span>
                                                <?php echo Yii::t("asa", "所选日期没有ASA课程或课程未分配老师"); ?>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- block end -->
                                    <div v-else>
                                        <div class="col-md-6" v-for="(schedule, sid) in scheduleData">
                                            <div class="panel panel-default">
                                                <!-- Default panel contents -->
                                                <div class="panel-heading">{{courseData[sid].title}}</div>
                                                <div class="panel-body bggray">
                                                    <ul class="nav nav-pills">
                                                        <li v-for="(vendor, vendorid) in courseData[sid].vendors">
                                                            <a href="javascript:">{{vendorData[vendorid].title}}<span
                                                                    class="badge">{{jobType[vendor.job_type]}}</span></a>
                                                        </li>
                                                    </ul>
                                                </div>
                                                <!-- List group -->
                                                <ul class="list-group" v-for="(sc,id) in schedule">
                                                    <li class="list-group-item bg-warning">
                                                        <span class="glyphicon glyphicon-time"
                                                              aria-hidden="true"></span>

                                                        {{sc.time_start}} - {{sc.time_end}}

                                                        <a key="222"
                                                           :href="'<?php echo $this->createUrl('delAttendance') ?>&course_schedule_id=' + id + '&targetDate=' + thedate"
                                                           data-msg="确认取消签到吗?"
                                                           v-if="isArray(attendanceByDayData[sid]['attendeds'][id])"
                                                           class="btn btn-danger btn-xs pull-right J_ajax_del">取消签到</a>
                                                        <a key="1231"
                                                           :href="'javascript:attendanceModal(\''+ id + '\',' + sid +')'"
                                                           class="btn btn-primary btn-xs pull-right" v-else-if="courseData[sid].vendors">签到</a>
                                                        <p class="text-danger" v-else>请先给课程分配老师</p>
                                                        <div class="col-md-12" v-if="attendanceByDayData">
                                                            <ul class="list-inline"
                                                                v-for="(attendanceData,adid) in attendanceByDayData[sid]">

                                                                <li class=""
                                                                    v-for="(attendeds,aid) in attendanceData[id]"
                                                                    :class="[{ 'text-success': attendeds.status == 1, 'text-danger': attendeds.status == 2, 'text-warning': attendeds.status == 3}]">
                                                                <span v-if="attendeds.status == 1">
                                                                    <span class="glyphicon glyphicon-ok"></span>
                                                                    <span>{{vendorData[aid].title}}</span>
                                                                </span>
                                                                <span v-else-if="attendeds.status == 2">
                                                                    <span class="glyphicon glyphicon-ban-circle"></span>
                                                                    <span>{{vendorData[aid].title}}</span>
                                                                </span>
                                                                <span v-else-if="attendeds.status == 3">
                                                                    <span class="glyphicon glyphicon-transfer"></span>
                                                                    <del>{{vendorData[aid].title}}</del>
                                                                    <ins>{{vendorData[attendeds.substituter].title}}
                                                                    </ins>
                                                                </span>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                        <div class="clearfix"></div>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <!-- </div> -->
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
<?php if ($groupId): ?>
    <div class="modal fade" id="teacherlistModel" tabindex="-1" data-backdrop="static">
        <div class="modal-dialog" role="document">
            <form action="<?php echo $this->createUrl('addAttendance') ?>" method="post" class="J_ajaxForm">
                <div class="modal-content">
                    <div  id="teacherlistVue" v-if="selectedData.courseId > 0">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                                aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title" id="">录入考勤 {{courseData[selectedData.courseId].title}}</h4>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="form-group form-inline">
                                <input type="hidden" id="scheduleId" name="scheduleId"/>
                                <input type="hidden" id="" name="serviceTime" :value="thedate"/>

                                <div class="col-md-12 mb10" v-for="(vendor,vendorId,vindex) in attendTeacherList">
                                    <h5 class="col-md-2">{{vendorData[vendorId].title}}</h5>
                                    <div class="col-md-10" >
                                        <div v-if="vendorData[vendorId].type != 1">
                                            <label class="radio-inline" >
                                                <input type="radio" :name="'status['+ vendor +']'" @click="spareclick(1,vindex)" :value="1">{{attendance_result[1]}}
                                            </label>
                                            <label class="radio-inline" >
                                                <input type="radio" :name="'status['+ vendor +']'" @click="spareclick(2,vindex)" :value="2">{{attendance_result[2]}}
                                            </label>
                                        </div>
                                        <div v-else>
                                            <label class="radio-inline" v-for="(result,index) in attendance_result">
                                                <input type="radio" :name="'status['+ vendor +']'"
                                                       @click="spareclick(index,vindex)" :value="index">{{result}}
                                            </label>
                                        </div>

                                        <select class="form-control ml10 spare_select" style="max-width: 100%" v-if="spareflag[vindex].flag"
                                                :name="'substituter[' + vendorId + ']'">
                                            <optgroup v-for="(type,id,index) in vendorTypeData"
                                                      :label="vendorType[id]">
                                                <option v-for="(t,tid) in type" :value="t">
                                                    {{vendorData[t].title}}
                                                </option>
                                            </optgroup>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    </div>
                    <div class="modal-footer" style="background: #fff;">
                        <button type="button" class="btn btn-primary J_ajax_submit_btn">保存</button>
                        <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                        <input type="reset" class="_reset" style="display: none"/>
                    </div>
                </div>

            </form>
        </div>
    </div>
    <script>
        var thedate = '<?php echo $thedate; ?>';
        var courseData = <?php echo CJSON::encode($courseData); ?>;
        var vendorData = <?php echo CJSON::encode($vendorData); ?>;
        var attendedData = <?php echo CJSON::encode($attendedData); ?>;
        var jobType = <?php echo CJSON::encode($jobType); ?>;
        var attendance_result = <?php echo CJSON::encode($attendance_result); ?>;
        var scheduleData = <?php echo CJSON::encode($scheduleData); ?>;
        var vendorType = <?php echo CJSON::encode($type); ?>;
        var vendorTypeData = <?php echo CJSON::encode($vendorTypeData); ?>;
        var attendanceByDayData = <?php echo CJSON::encode($attendanceByDayData); ?>;
        var dateTime = <?php echo CJSON::encode($dateTime); ?>;//全部安排时间
        //正在操作签到的数据 课程id 及 日程安排id
        var selectedData = {
            courseId: 0,
            scheduleId: ''
        };


        var returndata;

        var courseDate = [{date: ''}];    //课程日期

        var vendorAttendVue = new Vue({     //签到数据模型
            el: "#vendorAttendVueModel",
            data: {
                thedate:thedate,
                courseData:courseData,
                vendorData:vendorData,
                scheduleData:scheduleData,
                jobType:jobType,
                attendanceByDayData:attendanceByDayData
            }, beforeUpdate: function () {
            },
            updated: function () {
                head.Util.ajaxDel();
            }, methods: {
                addnullAttendance: function (cid, groupName) {
                    Vue.set(this.attendanceByDayData[cid], groupName, []);
                },
                addAttendance: function (courseid, courseGroupId, attends) {
                    Vue.set(this.attendanceByDayData[courseid]['attendeds'], courseGroupId, attends);
                },
                removeAttendance: function (cid, gcid) {
                    Vue.set(this.attendanceByDayData[cid]['attendeds'], gcid, []);
                },
                isArray: function (data) {
                    if ($.isArray(data)) {
                        return false;
                    } else {
                        return true;
                    }
                },
                timeCompletion: function (num) {
                    var _num;
                    if (num < 10) {
                        _num = '0' + num
                    } else {
                        _num = num
                    }
                    return _num;
                }
            }
        });

        //------------
        var teacherlistVue = new Vue({  //老师列表模型
            el: "#teacherlistVue",
            data: {
                thedate:thedate,
                courseData:courseData,
                selectedData:selectedData,
                vendorData:vendorData,
                vendorType:vendorType,
                vendorTypeData:vendorTypeData,
                attendance_result:attendance_result,
                spareflag:[],
                attendTeacherList:{}
            }, created: function () {
            },
            updated: function () {
            },
            methods: {
                spareclick: function (index, vidnex) {
                    if (index < 3) {
                        this.spareflag[vidnex].flag = false;
                    } else {
                        this.spareflag[vidnex].flag = true;
                    }
                }
            }
        });

        function attendanceModal(course_schedule_id, course_id) {      //呼出模态框

            $.ajax({
                url: '<?php echo $this->createUrl('addTeacher') ?>',
                data: {scheduleId: course_schedule_id, courseId: course_id},
                type: 'get',
                timeout: 50000,
                dataType: 'json',
                global: false
            }).done(function (data, status, xhr) {
                teacherlistVue.selectedData.courseId = course_id;
                teacherlistVue.selectedData.scheduleId = course_schedule_id;
                teacherlistVue.spareflag.splice(0, teacherlistVue.spareflag.length);
                $('._reset').click();           //重置表单
                setTimeout(function () {  //默认选中出勤
                    $('.modal-body .radio-inline').find(':radio[value=1]').prop('checked', 'checked');
                    $('#scheduleId').val(course_schedule_id);
                }, 0);
                $.each(data, function (i, list) {
                    teacherlistVue.spareflag.push({flag: false})
                });
                teacherlistVue.attendTeacherList = data;

                $('#teacherlistModel').modal();
            }).fail(function (xml, status, text) {
            });

        }

        function attendanced(datas) {    //保存成功后回调
            var courseid = datas['courseid'];   //课程id
            var courseGroupId;
            $('#teacherlistModel').modal('hide');
            $.each(datas.attends, function (i, attends) {
                courseGroupId = i;                  //组合id
                if (vendorAttendVue.attendanceByDayData[courseid]) {      //判断签到下面有没有这个课程id
                    vendorAttendVue.addAttendance(courseid, courseGroupId, attends);
                } else {     //没有需要先加一层课程id 下面再加一层attendeds
                    vendorAttendVue.addnullAttendance(courseid, 'attendeds');
                }
            });

        }
        function cancelAttendance(data) {     //删除成功后回调
            var cid = data.courseid;     //课程id
            var gcid = data.scheduleId;  //组合id
            vendorAttendVue.removeAttendance(cid, gcid);
        }

        $('.datepicker').datepicker({
            dateFormat: 'yy-mm-dd',
            onSelect: function (dateText, inst) {    //选中事件 第一个参数是选中时间 第二个参数是选中的对象
                $('.invoiceLoading').show();
                $('#selected-date-header span[data-tag|="date-text"]').html(dateText);
                $.ajax({
                    url: '<?php echo $this->createUrl('index') ?>',
                    data: {thedate: dateText, groupId: <?php echo $groupId; ?>},
                    type: 'get',
                    timeout: 50000,
                    dataType: 'json',
                    global: false
                }).done(function (data, status, xhr) {
                    $('.invoiceLoading').hide();
                    vendorAttendVue.scheduleData = data.scheduleData;
                    vendorAttendVue.attendanceByDayData = data.attendanceByDayData;
                    vendorAttendVue.thedate = dateText;
                    teacherlistVue.scheduleData = data.scheduleData;
                    teacherlistVue.thedate = dateText;


                }).fail(function (xml, status, text) {
                });
            },
            beforeShowDay : function(date){//日期控件显示面板之前
                var syear = date.getFullYear();
                var smonth = vendorAttendVue.timeCompletion(date.getMonth() + 1);
                var sday = vendorAttendVue.timeCompletion(date.getDate());
                var selectDay = syear+'-'+smonth+'-'+sday;
                var cssClass = '';
                $.each(dateTime, function (i, time) {
                    if(time == selectDay){
                        cssClass = 'date-active';
                    }
                });
                return [true, cssClass];
            }
        });
    </script>
<?php endif; ?>
<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>
<script>
    $("#selectstartyear").change(function (){
        var id = $(this).val();
        $(".year"+id).show().siblings().hide();
        $('#courseDate').html('')
        $("#vendorAttendVueModel").html('')
        $('.background-gray.mr5.mb5.active').removeClass('active');
    })
</script>
