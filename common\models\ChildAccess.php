<?php

/**
 * This is the model class for table "childaccess".
 *
 * The followings are the available columns in table 'childaccess':
 * @property string $accesskey
 * @property integer $childid
 */
class ChildAccess extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @return ChildAccess the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'childaccess';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('accesskey', 'required'),
			array('childid', 'numerical', 'integerOnly'=>true),
			array('accesskey', 'length', 'max'=>128),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('accesskey, childid', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'accesskey' => 'Accesskey',
			'childid' => 'Childid',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('accesskey',$this->accesskey,true);
		$criteria->compare('childid',$this->childid);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}