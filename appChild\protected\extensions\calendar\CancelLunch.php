<?php
class CancelLunch extends CWidget{
	public $calendarId;
	public $lunchType;
	public $childId;
	public $schoolId;
	
	public function init(){
		Yii::import('common.models.calendar.*');
	}
	
	public function run(){
		$calendarSemesterList = CalendarSemester::model()->getSemesterTimeStamp($this->calendarId);
		$schoolYearStart = $calendarSemesterList['fall_start'];
		//get child_service_info
		$childServiceObj = ChildServiceInfo::model()->getChildFeeInfo($this->childId, $this->lunchType,$this->calendarId,$schoolYearStart);
		$invoiceRefundArray = array();
		$invoiceServiceMonth = array();
		if (is_array($childServiceObj) && count($childServiceObj))
		{
			foreach ($childServiceObj as $k => $v)
			{
				$invoiceServiceMonth[$k]['startdate'] = $v->startdate;
				$invoiceServiceMonth[$k]['enddate'] = $v->enddate;
                $weekArr = array('Mon' => $v->mon, 'Tue' => $v->tue, 'Wed' => $v->wed, 'Thu' => $v->thu, 'Fri' => $v->fri);
                foreach ($weekArr as $key => $val){
                    if (in_array($val, array(10,30))){
                         $invoiceServiceMonth[$k]['inday'][] = $key;
                    }
                }
			}
		}
		else
		{
			$this->render("_noservice");
			return;
		}
		// get lunch max date
		$dayTamp = 9*3600;
		$maxServiceMonth = max($invoiceServiceMonth);
		$invoiceEnddate = $maxServiceMonth["enddate"];
		$schoolDayArr = CalendarSchoolDays::model()->getCalendarSchoolDay($this->calendarId, $schoolYearStart, $invoiceEnddate);
		$descs = CalendarSchool::model()->getCalendarDesc($this->calendarId,$this->schoolId);
		if (is_array($schoolDayArr) && count($schoolDayArr))
		{
			foreach ($schoolDayArr as $csa){
				$key = str_replace("-","",$csa->month_label);
				$caldarSchooldayArray[$key] = (!empty($csa->schoolday_array)) ? explode(',',$csa->schoolday_array) : array();
			}
			$datenum = mktime(0,0,0,date("m",$schoolYearStart),1,date("Y",$schoolYearStart));
			$i = 0;
			$month = new PublicMonth(date('Y', $schoolYearStart), date('m',$schoolYearStart),null);
			while( $datenum <= $invoiceEnddate ){
				$month->resetYm(date('Y', $datenum), date('m',$datenum));
				$_key = date('Ym',$datenum);
				$schoolDays = (!empty($caldarSchooldayArray)) ? $caldarSchooldayArray[$_key] : array();
				$months[$_key]['calendars'] = $month->getMonthData($schoolDays);
				$months[$_key]['schoolday'] = count($schoolDays);
				$i++;
				$datenum = mktime(0,0,0, date('m',$schoolYearStart) + $i, 1, date('Y',$schoolYearStart));
			}
	
			$lunchInday = array();
			$currentdate = mktime(0,0,0,date('m',time()),date('d',time()),date('Y',time()));
			$dueCanceldate = $currentdate+$dayTamp;
			foreach ($invoiceServiceMonth as $ism){
				$day = 0;
				$daynum =  mktime(0,0,0,date("m",$ism['startdate']),date("d",$ism['startdate'])+$day,date("Y",$ism['startdate']));
				while ($daynum <= $ism['enddate']){
					//if ($daynum >= $dueCanceldate){
						if (in_array(date('d',$daynum),$caldarSchooldayArray[date('Ym',$daynum)])){
							if (in_array(date('D',$daynum),$ism['inday']) || date('w',$daynum) == 0 || date('w',$daynum) == 6){
								$lunchInday[date('Ymd',$daynum)]['in'] = 1;
							}else{
								$lunchInday[date('Ymd',$daynum)]['in'] = 0;
							}
						}
					//}
					$day++;
					$daynum = mktime(0,0,0,date("m",$ism['startdate']),date("d",$ism['startdate'])+$day,date("Y",$ism['startdate']));
				}
			}
			//已经取消过的退费invoice_refund_lunch
			$refundLunchList = array();
			$refundLunchArr = RefundLunch::model()->getRefundLunch($this->childId, $schoolYearStart, $invoiceEnddate);
			if (is_array($refundLunchArr) && count($refundLunchArr)){
				foreach ($refundLunchArr as $rla){
					$refundLunchList[$rla->target_date]['is_check'] = ($rla->operator_uid) ? 1 : 0;
				}
			}
			unset($refundLunchArr);
		}
		$this->render('cancellunch',array("months"=>$months,"descs"=>$descs,"lunchInday"=>$lunchInday,"refundLunchList"=>$refundLunchList,'dayTamp'=>$dayTamp,'schoolYear'=>date('Y',$schoolYearStart)));
	}
	
}