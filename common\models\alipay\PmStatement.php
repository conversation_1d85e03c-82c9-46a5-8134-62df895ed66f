<?php

/**
 * This is the model class for table "ivy_pm_statement".
 *
 * The followings are the available columns in table 'ivy_pm_statement':
 * @property integer $id
 * @property string $type
 * @property string $partner_id
 * @property string $settlement_title
 * @property double $xpay_settlement_amount
 * @property double $ivy_settlement_amount
 * @property integer $xpay_settlement_time
 * @property string $toaccount_title
 * @property double $bank_toaccount_amount
 * @property double $ivy_toaccount_amount
 * @property double $actual_toaccount_amount
 * @property double $handling_amount
 * @property integer $bank_toaccount_time
 * @property integer $status
 * @property integer $settlement_update_user
 * @property integer $toaccount_update_user
 * @property integer $handover_status
 * @property integer $updated
 */
class PmStatement extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_pm_statement';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('type, partner_id, updated', 'required'),
			array('xpay_settlement_time, bank_toaccount_time, status, settlement_update_user, toaccount_update_user, handover_status, bank_id', 'numerical', 'integerOnly'=>true),
			array('xpay_settlement_amount, ivy_settlement_amount, bank_toaccount_amount, ivy_toaccount_amount, handling_amount, actual_toaccount_amount', 'numerical'),
			array('type', 'length', 'max'=>15),
			array('partner_id', 'length', 'max'=>25),
			array('settlement_title, toaccount_title', 'length', 'max'=>255),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, type, partner_id, settlement_title, xpay_settlement_amount, ivy_settlement_amount, xpay_settlement_time, toaccount_title, bank_toaccount_amount, ivy_toaccount_amount, bank_toaccount_time, status, settlement_update_user, toaccount_update_user, handover_status', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'items' => array(self::HAS_MANY, 'PmStatementOrderLink', 'statement_id'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'type' => 'Type',
			'partner_id' => 'Partner',
			'settlement_title' => 'Settlement Title',
			'xpay_settlement_amount' => 'Xpay Settlement Amount',
			'ivy_settlement_amount' => 'Ivy Settlement Amount',
			'xpay_settlement_time' => 'Xpay Settlement Time',
			'toaccount_title' => 'Toaccount Title',
			'bank_toaccount_amount' => 'Bank Toaccount Amount',
			'ivy_toaccount_amount' => 'Ivy Toaccount Amount',
			'handling_amount' => 'Handling Fee',
			'actual_toaccount_amount' => 'Actual Toaccount Amount',
			'bank_toaccount_time' => 'Bank Toaccount Time',
			'status' => 'Status',
			'settlement_update_user' => 'Settlement Update User',
			'toaccount_update_user' => 'Toaccount Update User',
			'handover_status' => 'Handover Status',
			'updated' => 'Updated',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('type',$this->type,true);
		$criteria->compare('partner_id',$this->partner_id,true);
		$criteria->compare('settlement_title',$this->settlement_title,true);
		$criteria->compare('xpay_settlement_amount',$this->xpay_settlement_amount);
		$criteria->compare('ivy_settlement_amount',$this->ivy_settlement_amount);
		$criteria->compare('xpay_settlement_time',$this->xpay_settlement_time);
		$criteria->compare('toaccount_title',$this->toaccount_title,true);
		$criteria->compare('bank_toaccount_amount',$this->bank_toaccount_amount);
		$criteria->compare('ivy_toaccount_amount',$this->ivy_toaccount_amount);
		$criteria->compare('bank_toaccount_time',$this->bank_toaccount_time);
		$criteria->compare('status',$this->status);
		$criteria->compare('settlement_update_user',$this->settlement_update_user);
		$criteria->compare('toaccount_update_user',$this->toaccount_update_user);
		$criteria->compare('handover_status',$this->handover_status);
		$criteria->compare('updated',$this->updated);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return PmStatement the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}
