<?php

/**
 * This is the model class for table "ivy_tag_link".
 *
 * The followings are the available columns in table 'ivy_tag_link':
 * @property string $tl_id
 * @property string $tag_id
 * @property integer $tag_modid
 * @property string $tag_catid
 * @property string $tag_itemid
 * @property string $tag_time
 */
class TagLink extends CActiveRecord
{
	public $num;
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_tag_link';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('tag_modid', 'numerical', 'integerOnly'=>true),
			array('tag_id, tag_catid, tag_itemid, tag_time', 'length', 'max'=>10),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('tl_id, tag_id, tag_modid, tag_catid, tag_itemid, tag_time', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
			'tag'=>array(self::BELONGS_TO,'TagTag','tag_id'),
			'projects'=>array(self::BELONGS_TO,'CurProjects','tag_itemid'),
			'activity'=>array(self::BELONGS_TO,'CurActivityInfo','tag_itemid'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'tl_id' => 'Tl',
			'tag_id' => 'Tag',
			'tag_modid' => 'Tag Modid',
			'tag_catid' => 'Tag Catid',
			'tag_itemid' => 'Tag Itemid',
			'tag_time' => 'Tag Time',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('tl_id',$this->tl_id,true);
		$criteria->compare('tag_id',$this->tag_id,true);
		$criteria->compare('tag_modid',$this->tag_modid);
		$criteria->compare('tag_catid',$this->tag_catid,true);
		$criteria->compare('tag_itemid',$this->tag_itemid,true);
		$criteria->compare('tag_time',$this->tag_time,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return TagLink the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}
