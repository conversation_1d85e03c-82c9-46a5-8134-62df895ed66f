
<style type="text/css">
 [v-cloak] {
	display: none;
}
.att-report th {
	background-color: #f5f5f5;
}
.att-report tbody td {
	text-align: center;
}
th div.progress {
	margin-bottom: 0px;
}
.table{
	color:#606266
}
.table tr th,.table tr td {
	vertical-align:middle !important;
	text-align:center;
	color:#606266
}
.pink{
	background:#FFECEE
}
.progress{
	background:#fff;
}
.loading{
	width:98%;
	height:90%;
	background:#fff;
	position: absolute; 
	opacity: 0.5;
	z-index: 99
}
.loading span{
	width:100%;
	height:60%;
	display:block;
	background: url("<?php echo Yii::app()->theme->baseUrl?>/images/loading.gif")no-repeat center center ;
}
</style>
<div id='container'  v-cloak>
	<h3 class='color3 font14'><strong style='font-size:18px' class='mr20'><?php echo Yii::t('attends', 'Daily Grades Attendance Report'); ?></strong> <small><?php echo Yii::t('attends', 'Daily Attendance is based on 2nd period attendence.') ?></small></h3>
	<div class="form-inline mt15 mb20"> 
		<label for="exampleInputName2"><?php echo Yii::t("admissions", "Date");?></label>
		<input type="text" class="form-control form-group ml10" id="targetDate" placeholder="<?php echo Yii::t("newDS", "Select a date");?>" @change='showData()'   :value='targetDate'>
		<span class="label label-default ml10">#{{weekNum}} <?php echo Yii::t("lunch", "Week");?> / {{category}}</span>
	</div>
	<div class='loading'  v-if='showLoading'>
		<span></span>
	</div>
	<table class="table table-bordered att-report" style='table-layout: fixed' v-if='classData.length!=0 && !showLoading'>
		<thead>
			<th width='150'><?php echo Yii::t("labels", "Class");?></th>
			<template v-for='(_item,key,i) in items[0]'>
				<th v-if='numberTypes[key]'>
					<div class='font14'>{{numberTypes[key]}}</div>
				</th>
			</template>
		</thead>
		<tbody>
			<tr v-for='(list,key,index) in classData'>
				<th>
					<p>{{list}}</p>
				</th>
				<template v-for='(_item,keys,i) in items[key]'>
					<td v-if='numberTypes[keys]'>
						<a href="javascript:;" v-if='_item.total!=0' @click='showStu(keys,key)'>{{_item.total}}</a>
						<span class="label ml5" v-if='_item.total!=0' :class='_item.percent<80?"label-danger":"label-success"'>{{ _item.percent}}%</span>
					</td>
				</template>
			</tr>
			<tr>
				<th>ALL</th>
				<template v-for='(_item,key,i) in items[0]'>
					<td v-if='numberTypes[key]'>
						<a href="javascript:;" v-if='_item.total!=0' @click='showStu(key,"0")'>{{_item.total}}</a>
						<span class="label " v-if='_item.total!=0' :class='_item.percent<80?"label-danger":"label-success"'>{{ _item.percent}}%</span>
					</td>
				</template>
			</tr>
		</tbody>
	</table>
	<div class="alert alert-danger" role="alert" v-else-if='!showLoading'><?php echo Yii::t('asa','No data found.') ?></div>
	<div class="modal fade" id="childModel" tabindex="-1" role="dialog" aria-labelledby="modal" aria-hidden="false">
		<div class="modal-dialog modal-lg" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
					<h4 class="modal-title" ><?php echo Yii::t('attends','Student List') ?> <span id="remarks_t"></span></h4>
				</div>
				<div class="form-horizontal">
					<div class="modal-body">
						<div class='p10'>
							<ul class="nav nav-pills mb20" role="tablist">
								<li role="presentation"  :class='key==type?"active":""'  v-for='(list,key,index) in childData.groupCount'  @click='showStu(key,classId)'><a href="javascript:;">{{numberTypes[key]}} <span class="badge">{{list}}</span></a></li>
							</ul>
							<div class="alert alert-danger mt20"  v-if='childData.studentList && childData.studentList.length==0' role="alert"><?php echo Yii::t('attends','No students') ?></div>
							<div class='col-md-4' v-else v-for='(list,index) in childData.studentList' >
								<div class="media mt15">
									<div class="media-left pull-left media-middle">
									<a href="javascript:void(0)">
										<img :src="list.avatar" data-holder-rendered="true" style='width:48px;height:48px' class="media-object img-circle image"></a>
									</div> 
									<div class="media-body pt10 media-middle">
										<h4 class="media-heading font12">({{list.id}}) {{list.name}}</h4> 
										<div class="text-muted">{{list.class_name}}</div>
									</div>
								</div>    
							</div>
							<div class='clearfix'></div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<script>
	
	$(function() {
		$("#targetDate").datepicker({
			dateFormat: "yy-mm-dd ",
		});
		$( "#targetDate").on('change', function () {
			container.targetDate = $('#targetDate').val();
			container.showData()
		});
	});
	var container = new Vue({
        el: "#container",
        data: {
			"classData":{},
			"numberTypes": {},
			"items": {},
			showLoading:false,
			childData:{},
			weekNum:'',
			category:'',
			classId:'',
			type:''
        },
		created(){
			this.targetDate=this.newDate()
			this.showData()
		},
        methods: {
			progress(num,total){
				var progress=total <= 0? "0" : Math.round((num / total) * 10000) / 100.0;
				return progress
			},
			newDate(){           
					var date = new Date();
                const Y = date.getFullYear() + '-';
                const M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
                const D = (date.getDate() < 10 ? '0'+date.getDate() : date.getDate()) + ' ';
                const h = (date.getHours() < 10 ? '0'+date.getHours() : date.getHours()) ;
                const m = (date.getMinutes() <10 ? '0'+date.getMinutes() : date.getMinutes());
                const s = date.getSeconds(); // 秒
                const dateString = Y + M + D
                    return dateString;
                
            },
			showData(){
				this.showLoading=true
				let that=this
				$.ajax({
                    url: '<?php echo $this->createUrl("reportR03") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
						targetDate:this.targetDate
                    },
                    success: function(data) {
                        if(data.state=='success'){  
							that.category=data.data.category
							that.weekNum=data.data.weekNum
                            that.classData=data.data.classData
                            that.numberTypes=data.data.numberTypes
                            that.items=data.data.items
                            that.showLoading=false
                        }else{
                            that.showLoading=false 
                        }
                    }
                })
			},
			showStu(type,classId){
				let that=this
				this.type=type
				this.classId=classId
				$.ajax({
                    url: '<?php echo $this->createUrl("showAttendStudent2") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
						targetDate:this.targetDate,
						type:type,
						classid:classId
                    },
                    success: function(data) {
                        if(data.state=='success'){  
							that.childData=data.data
                        	$('#childModel').modal('show');
                        }else{
                        }
                    }
                })
			}
		}
    })
</script>
