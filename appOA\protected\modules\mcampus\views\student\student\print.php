<style>
    #clsa td{
       border: solid 1px #ccc;
    }
    .width-reports{
        width:100px;
    }

</style>
<div class="panel panel-default">
    <div class="panel-heading"><?php echo $classID->title ?>, <?php echo Yii::t('campus', 'Sign-up Sheet for ____ (month)')?></div>
    <table class="table" id="clsa">
        <tr>
            <td class="width-reports"><?php echo Yii::t('user','Child Name')?></td>
            <td><?php echo Yii::t('user','Bar code')?></td>
            <?php
            for($i = 31;$i>0;$i--){ ?>
            <td><?php echo $i; ?></td>
            <?php } ?>
        </tr>
            <?php foreach($child as $v): ?>
            <tr style="border-style: solid; border-width:1px;">
                <td class="width-reports"><?php echo $v['name']; ?></td>
                <?php $optionsArray = array(
                        'elementId' => $v['childids'],
                        'value' => $v['childids'],
                        'type' => 'code128',
                        'settings' => array(
                            'barWidth' => 1
                        )
                    );
                    ?>
                <td><?php $this->widget("common.extensions.barcode.Barcode", $optionsArray);?></td>
                <?php
                for($i = 31;$i>0;$i--){ ?>
                    <td></td>
                <?php } ?>
            </tr>
            <?php usleep(1000); endforeach;?>
        <tr>
            <td class="width-reports"><?php echo Yii::t('user','Child Sum')?></td>
            <td></td>
            <?php
            for($i = 31;$i>0;$i--){ ?>
                <td></td>
            <?php } ?>
        </tr>
    </table>
</div>