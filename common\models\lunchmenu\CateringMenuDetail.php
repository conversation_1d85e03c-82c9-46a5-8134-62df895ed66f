<?php

/**
 * This is the model class for table "ivy_catering_menu_detail".
 *
 * The followings are the available columns in table 'ivy_catering_menu_detail':
 * @property integer $id
 * @property integer $menu_id
 * @property string $weekday
 * @property integer $category
 * @property string $food_list
 * @property integer $status
 * @property integer $update_user
 * @property integer $update_timestamp
 */
class CateringMenuDetail extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CateringMenuDetail the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_catering_menu_detail';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('menu_id', 'required'),
			array('menu_id, category, status, update_user, update_timestamp', 'numerical', 'integerOnly'=>true),
			array('weekday', 'length', 'max'=>25),
			array('food_list, photo', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, menu_id, weekday, category, food_list, status, update_user, update_timestamp', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'termInfo'=>array(self::BELONGS_TO, 'Term', 'category','with'=>false)
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'menu_id' => 'Menu',
			'weekday' => 'Weekday',
			'category' => 'Category',
			'food_list' => 'Food List',
			'status' => 'Status',
			'update_user' => 'Update User',
			'update_timestamp' => 'Update Timestamp',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('menu_id',$this->menu_id);
		$criteria->compare('weekday',$this->weekday,true);
		$criteria->compare('category',$this->category);
		$criteria->compare('food_list',$this->food_list,true);
		$criteria->compare('status',$this->status);
		$criteria->compare('update_user',$this->update_user);
		$criteria->compare('update_timestamp',$this->update_timestamp);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}