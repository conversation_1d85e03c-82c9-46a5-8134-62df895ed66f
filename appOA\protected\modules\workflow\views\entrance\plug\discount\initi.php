<!-- Modal -->
<?php echo CHtml::form('/workflow/entrance/index', 'post',array('class'=>'J_ajaxForm'));?>
<div class="modal fade" id="<?php echo $data['modalNameId']?>" tabindex="-1" role="dialog" aria-labelledby="workflowModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel"><?php echo CommonUtils::autoLang($data['workflow']->definationObj->defination_name_cn,$data['workflow']->definationObj->defination_name_en);?> - <?php echo CommonUtils::autoLang($data['workflow']->nodeObj->node_name_cn,$data['workflow']->nodeObj->node_name_cn);?></h4>
            </div>
            <div class="modal-body" id="workflow-modal-body">
                <div class="form-group" model-attribute='discount_id'>
                    <label for="exampleInputEmail1">折扣名称</label>
                    <?php echo CHtml::dropDownList('ChildDiscountLink[discount_id]', '', $data['discount'],array('class'=>'form-control','empty'=>Yii::t('global', 'Please Select')));?>
                </div>
                <div class="form-group">
                    <label for="exampleInputEmail1">选择班级</label>
                    <?php echo CHtml::dropDownList('classid', '', $data['classList'],array('class'=>'form-control','empty'=>Yii::t('global', 'Please Select'),'onchange'=>'findChildren(this)'));?>
                </div>
                <div class="form-group">
                    <div class="col-md-5" style="padding-left:0;">
                        <?php echo CHtml::dropDownList('l_childid', '', array(),array('class'=>'form-control mb15','multiple'=>'multiple','size'=>2,'style'=>'height:300px;'));?>
                    </div>
                    <div class="col-md-2">
                        <div class="col-md-12 glyphicon glyphicon-chevron-right" style="margin-top:70px;" style="cursor:pointer;" onclick="moveRight();"></div>
                        <div class="col-md-12 glyphicon glyphicon-chevron-left" style="margin-top:70px;" style="cursor:pointer;" onclick="moveLeft();"></div>
                    </div>
                    <div class="col-md-5" style="padding-right:0;">
                        <?php echo CHtml::dropDownList('r_childid', '', array(),array('class'=>'form-control mb15','multiple'=>'multiple','size'=>2,'style'=>'height:300px;'));?>
                    </div>
                </div>
                <div class="col-12">
                    <!-- 显示附件 -->
                    <div id="fileList">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>已上传资料</th>
                                    <th>资料说明</th>
                                    <th>操作</th>
                                </tr>
                                <?php foreach ($data['workflow']->getAttachmentList() as $uploadFile) : ?>
                                    <tr>
                                        <td><a target="_blank" href="<?php echo $uploadFile['bigimages']; ?>"><?php echo $uploadFile['notes']; ?></a></td>
                                        <td><input type="text" onchange="changFileName(this,<?php echo $uploadFile['id'] ?>)" class="form-control" placeholder="输入资料说明(XX班级XX申请单)" /></td>
                                        <td>
                                            <a class="btn btn-danger btn-xs" onclick="deleteFile(this,<?php echo $uploadFile['id'] ?>)"><span class="glyphicon glyphicon-trash"> </span></a>
                                        </td>
                                        <input type="hidden" name="files[]" value="<?php echo $uploadFile['id'] ?>">
                                    </tr>
                                <?php endforeach; ?>
                            </thead>
                        </table>
                    </div>
                    <!-- 上传 -->
                    <div class="btn-group btn-group-justified" role="group">
                        <input id="file" type="file" name="file" multiple="true" style="display:none">
                        <div class="btn-group" role="group">
                            <a id="filebtn" class="btn btn-default" onclick="$('#file').click();">上传新资料</a>
                        </div>
                    </div>
                    <br>
                    <span class="text-warning" id="spaninfo"></span>
                </div>
                <div class="clearfix"></div>
                <div class="form-group">
                    <label for="exampleInputEmail1">意见</label>
                    <?php echo CHtml::textArea('ChildDiscountLink[memo]','',array('class'=>'form-control','row'=>3));?>
                </div>
            </div>
            <div class="modal-footer">
                <?php echo CHtml::hiddenField('definationId', $data['workflow']->definationObj->defination_id);?>
                <?php echo CHtml::hiddenField('nodeId', $data['workflow']->nodeObj->node_id);?>
                <?php echo CHtml::hiddenField('branchId', $data['workflow']->schoolId);?>
                <?php echo CHtml::hiddenField('action', 'save');?>
                <div id="already_choose_child_hidden"></div>
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('workflow', 'Cancel');?></button>
                <button type="button" class="btn btn-primary J_ajax_submit_btn">提交</button>
            </div>
        </div>
    </div>
</div>
<?php CHtml::endForm();?>
<script>
    var postUrl = '<?php echo Yii::app()->createUrl('/mcampus/classes/getChildrenList',array('branchId'=>$data['workflow']->schoolId));?>';
    const definationId = <?php echo $data['workflow']->definationObj->defination_id; ?>;
    const operationId = '<?php echo $data['workflow']->getOperateId(); ?>';
    const nodeId = '<?php echo $data['workflow']->nodeObj->node_id; ?>';
    const branchId = '<?php echo $data['workflow']->schoolId; ?>';
    //上传文件
    $(function() {
        $('#file').change(function() {
            $('#spaninfo').text("");
            var action = 'saveUploadFile';
            var url = '<?php echo Yii::app()->createUrl("/workflow/entrance/index") . "?definationId=' +definationId+ '&nodeId=' +nodeId+ '&branchId=' +branchId+ '&operationId=' +operationId+ '&action=' +action+ '"; ?>';
            // 循环 this.files
            for (var i = 0; i < this.files.length; i++) {
                var formData = new FormData();
                var _file = this.files[i]
                var fileName = _file.name
                formData.append('file', _file);

                $.ajax({
                    url: url,
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(data) {
                        var msg = eval('(' + data + ')');
                        if (msg.state == 'success') {
                            var html = '<tr><td><a target="_blank" href="' + msg.data.url + '">' + msg.data.fileName + '</a></td>';
                            html += '<td><input type="text" onchange=changFileName(this,' + msg.data.fileId + ') class="form-control" placeholder="输入资料说明(XX班级XX申请单)"></td>'
                            html += '<td><a class="btn btn-danger btn-xs" onclick="deleteFile(this,' + msg.data.fileId + ')"><span class="glyphicon glyphicon-trash"> </span></a></td></tr>';
                            html += '<input type="hidden" name="files[]" value=' + msg.data.fileId + ' />';
                            $('#fileList > table').append(html);
                        } else {
                            var oldText = $('#spaninfo').text();
                            $('#spaninfo').text(msg.message + oldText);
                        }
                    }
                });
            }
        });
    });
    //删除文件
    function deleteFile(btn, id) {
        if (confirm('确定要删除这个文件吗?')) {
            var action = 'deleteUploadFile';
            var url = '<?php echo Yii::app()->createUrl("/workflow/entrance/index") . "?definationId=' +definationId+ '&nodeId=' +nodeId+ '&branchId=' +branchId+ '&operationId=' +operationId+ '&action=' +action+ '"; ?>';

            $.ajax({
                url: url,
                type: 'POST',
                data: {
                    id: id
                },
                success: function(data) {
                    var msg = eval('(' + data + ')');
                    if (msg.state == 'success') {
                        $(btn).parent().parent().remove();
                    };
                    $('#spaninfo').text(msg.message);
                }
            });
        };
    }
    //修改文件名
    function changFileName(btn, id) {
        var notes = $(btn).val();
        if (notes == "") {
            return false
        };
        var action = 'changeFileName';
        var url = '<?php echo Yii::app()->createUrl("/workflow/entrance/index") . "?definationId=' +definationId+ '&nodeId=' +nodeId+ '&branchId=' +branchId+ '&operationId=' +operationId+ '&action=' +action+ '"; ?>';

        $.ajax({
            url: url,
            type: 'POST',
            data: {
                id: id,
                notes: notes
            },
            success: function(data) {
                var msg = eval('(' + data + ')');
                if (msg.state == 'success') {
                    var con = $(btn).parent().prev().children().text(notes);
                }
                $('#spaninfo').text(msg.message);
            }
        });
    }
</script>