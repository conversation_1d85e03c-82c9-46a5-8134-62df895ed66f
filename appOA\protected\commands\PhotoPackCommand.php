<?php

class PhotoPackCommand extends CConsoleCommand {

	public $batchNum=200;

	public function init(){
		Yii::setPathOfAlias('common', dirname(__FILE__) . '/../../../common');
		Yii::import('common.models.portfolio.*');
	}

    public function actionMake() {
        $crit = new CDbCriteria();
        $crit->compare('pack_time', 0);
        $crit->order='create_time ASC';
        $packModel = TaskPackpic::model()->find($crit);


        if($packModel) {
            $packInfo = $this->makePhotoList($packModel->startyear,
                $packModel->childid,
                $packModel->sweek,
                $packModel->eweek
            );
            $filename = sprintf('downloader-%d-%d-%d-%d.zip', $packModel->childid, $packModel->startyear,
                $packModel->sweek,
                $packModel->eweek);
            $fileInfo = $this->makeZip($packInfo['filePath'], $filename);

            $oss = CommonUtils::initOSS('private');
            $upload = $oss->upload_by_file($fileInfo['subPath'], $fileInfo['root']);

            //上传阿里云成功，修改数据库;
            if($upload->status == 200) {
                echo "success uploaded...";
                $packModel->pcount = $packInfo['picCount'];
                $packModel->dlpath = $fileInfo['subPath'];
                $packModel->pack_time = time();
                $packModel->save();
            }
            //删除临时文件
            @unlink($fileInfo['root'] . $fileInfo['subPath']);
            @unlink($packInfo['filePath']);

            $this->sentNotification($packModel->pemail);
        }
    }

    private function makeZip($photoListFile, $zipfile) {
        require_once 'PEAR.php';
        PEAR::setErrorHandling(PEAR_ERROR_DIE);

        include("Archive/Tar.php");
        $dir = dirname(__FILE__);
        $sourceData = $dir . "/../components/data/downloader/";
        $baseDir = rtrim(Yii::app()->basePath, '/')  . '/runtime/';
        $subDir = 'packphoto/' . $zipfile;

        @unlink($baseDir . $subDir);
        $tar = new Archive_Tar($baseDir . $subDir);
        $tar->addModify($sourceData.'lib/wget.exe', 'downloader', $sourceData);
        $tar->addModify($sourceData.'downloader.bat', 'downloader', $sourceData);
        $tar->addModify($sourceData.'photos/index.txt', 'downloader', $sourceData);

        $fileDir = dirname($photoListFile);
        $tar->addModify($photoListFile, 'downloader/lib', $fileDir);

        return array(
            'root' => rtrim($baseDir, '/') . '/',
            'subPath' => ltrim($subDir, '/')
        );
    }

    private function sentNotification($toEmail) {
        $isProduction = defined("IS_PRODUCTION")? IS_PRODUCTION : false;

        $mailer = Yii::createComponent('common.extensions.mailer.EMailer');
        $mailer->Subject = 'IvySchools Photo Download 艾毅幼儿园照片下载';
        $mailer->getCommandView('photoDownload', array(), 'main');

        $mailer->AddAddress( $toEmail );
        $mailer->iniMail($isProduction);
        $mailer->Send();
    }

    private function makePhotoList($startyear, $childid, $sweek, $eweek){
        $baseDir = rtrim(Yii::app()->basePath, '/')  . '/runtime/';
        $subDir = 'packphoto/' . substr($childid, -1) . '/';
        $fileName = $childid . "_" . $startyear . "_" . $sweek . "-" . $eweek . ".list";

        CommonUtils::createDir($baseDir . $subDir);

        ChildMedia::setStartYear($startyear);
        ChildMediaLinks::setStartYear($startyear);

        $sql = "select distinct(classid) from ivy_child_media_links_".$startyear." where childid=".$childid;
        $items = Yii::app()->db->createCommand($sql)->queryAll();
        $classids = array();
        foreach($items as $item){
            $classids[] = $item['classid'];
        }

        // 此处条件会有分班bug
        $criteria = new CDbCriteria();
        $criteria->compare('t.classid', $classids);
        $criteria->compare('t.childid', array($childid, 0));
        $criteria->compare('t.category', 'week');
        $criteria->addBetweenCondition('t.weeknum', $sweek, $eweek);
        $count = ChildMediaLinks::model()->count($criteria);

        $r_or_n = PATH_SEPARATOR == ':' ? "\n" : "\r\n";

        $file_path = $baseDir . $subDir . $fileName;

        $fhandle = fopen($file_path, "w+");
        $pcount = 0;

        $cycle = ceil($count/$this->batchNum);
        $classids = array();
        $classTitles = array();
        for($i=0; $i<$cycle; $i++) {
            echo $i . "\n";
            $criteria->limit = $this->batchNum;
            $criteria->offset = $i * $this->batchNum;
            $photoLinks = ChildMediaLinks::model()->with('photoInfo')->findAll($criteria);
            $lines = array();
            foreach ($photoLinks as $link) {
                if($link->photoInfo->type == 'photo') {
                    $pcount++;
                    $url = $link->photoInfo->getMediaUrl();
                    $lines[$pcount] = array(
                        'cid' => $link->photoInfo->classid,
                        'data' => sprintf("week%d\%d\%s",
                            $link->photoInfo->weeknum,
                            $pcount,
                            $url),
                    );
                    $classids[$link->photoInfo->classid] = $link->photoInfo->classid;
                }
            }

            $cIds = array_keys($classids);
            $tIds = array_keys($classTitles);
            $diff = array_diff($cIds, $tIds);

            if(!empty($diff)) {
                $classTitles = CHtml::listData(IvyClass::model()->findAllByPk($classids), 'classid', 'title');
                foreach($classTitles as $cid=>$ctitle) {
                    $classTitles[$cid] = preg_replace("/\s/","",$ctitle);
                }
                print_r($classTitles);
            }

            foreach($lines as $_index => $_arr) {
                $txt = $classTitles[$_arr['cid']] . "\\" . $_arr['data'];
                fwrite($fhandle, $txt . $r_or_n);
            }
        }

        fclose($fhandle);
        return array(
            'filePath' => $file_path,
            'picCount' => $pcount,
        );
    }



}