<style>
    .panel-body{
        padding:0px;
        position: relative;
    }
    .allCheck {
        position: absolute;
        right: 10px;
        top: 4px;
    }
    .addChild {
        font-size: 17px;
        color: #409EFF;
        width: 16px;
        height: 16px;
        display: inline-block;
        border: 1px solid #409EFF;
        text-align: center;
        line-height: 15px;
        border-radius: 50%;
    }
    .childHover{
        padding:8px;
        border: 1px solid #fff;
    }
    .childHover:hover{
        background: rgba(77,136,210,0.1);
        border-radius: 4px;
        border: 1px solid #4D88D2;
        cursor: pointer;
    }
    .border {
        border: 1px solid #E8EAED;
        border-radius: 4px;
    }
    .childList {
        max-height: 250px;
        min-height: 40px;
        padding: 14px;
        overflow-y: auto;
        border: 1px solid #E8EAED;
        border-radius: 4px;
    }
    .lineHeight {
        line-height: 32px;
    }
    .max500{
        max-height:500px;
        padding-bottom:24px
    }
    .list{
        list-style: none;
        padding: 0;
    }
    .groupList{
        padding:10px;
        align-items:center;
        font-size:14px
    }
    .groupList a{
        color:#333;
        display:block
    }
    .groupList:hover,.currentGroup{
        background: rgba(77,136,210,0.1);
        border-radius: 4px;
        cursor: pointer;
        color: #4D88D2;
    }
    .groupList:hover a,.currentGroup a{
        color: #4D88D2;
    }
</style>
<div id='container' v-cloak >
    <div class='row mb20' v-if='department.length>0'>
        <div class="col-md-12">
            <ul class="nav nav-tabs" id="tablist">
                <li role="presentation" v-for='(list,index) in department' :class="groupType==list?'active':''" @click='tabgroupType(list)'><a >{{groupName[list]}}</a></li>
            </ul>
        </div>
    </div>
    <div class="row">
        <div class="col-md-2">
        <?php
            $mainMenu = array(
                array('label'=>Yii::t('referral','Support Groups'), 'url'=>array("//mteaching/student/index","category"=>"adminGroups","category2"=>"supportList")),
                array('label'=>Yii::t('referral','Head of Grade'), 'url'=>array("//mteaching/student/index","category"=>"adminGroups","category2"=>"headGroup")),
                array('label'=>Yii::t('referral','Principal Office'), 'url'=>array("//mteaching/student/index","category"=>"adminGroups","category2"=>"office")),
                array('label'=>Yii::t('referral','Head of Department'), 'url'=>array("//mteaching/student/index","category"=>"adminGroups","category2"=>"subject")),
            );
            $this->widget('zii.widgets.CMenu',array(
                'id' => 'user-profile-item',
                'items'=> $mainMenu,
                'htmlOptions'=>array('class'=>'list'),
                'activeCssClass'=>'currentGroup',
                'itemCssClass'=>'groupList'
            ));
        ?>
        </div>
        <div class="col-md-10" >
            <div class="panel panel-default"  v-if='dataList.study'>
                <div class="panel-heading">
                    <div class='font16 color3 fontBold'><span class='glyphicon glyphicon-bookmark waitingColor font14'></span> <?php echo Yii::t("referral", "Learning Support"); ?></div>
                    <!-- <div class='color6 font14'><?php echo Yii::t("referral", "I am the explanatory text for this label"); ?></div> -->
                </div>
                <div class="panel-body " >
                    <span class='borderLeftpos'></span>
                    <div class='col-md-6'>
                        <div class="row module  " >
                            <div class='flex p24'>
                                <span class='flex1 font14'><?php echo Yii::t("teaching", "Teacher"); ?></span>
                                <div>
                                    <button type="button" class="btn btn-link" v-if='dataList.study.teacher && dataList.study.teacher.length>0'  @click='batchDel("study",2,"teacher")'><?php echo Yii::t("directMessage", "Clear All");?></button>
                                    <button type="button" class="btn btn-primary" @click='addTeacher("study",2)'><span class='el-icon-circle-plus-outline'></span> <?php echo Yii::t("global", "Add"); ?></button>
                                </div>
                            </div>
                            <div v-if='dataList.study.teacher && dataList.study.teacher.length>0'  class='overflow-y scroll-box max500'>
                                <div class='col-xs-6 '  v-for='(list,index) in dataList.study.teacher'>
                                    <div class="media listMedia" >
                                        <div class="media-left pull-left media-middle">
                                            <a href="javascript:void(0)">
                                                <img :src="dataList.teacher_list[list].photoUrl" data-holder-rendered="true" class="avatar">
                                            </a>
                                        </div>
                                        <div class="media-right pull-right pt12 text-right">
                                            <span class='el-icon-circle-close font16 closeIcon' @click='delTeacher("study",list,index,2)'></span>
                                        </div>
                                        <div class="media-body pt4 media-middle">
                                            <div class="lineHeight20  text-primary">
                                                <span class='font14 color3 nowrap'>{{dataList.teacher_list[list].name}}</span>
                                            </div>
                                            <div class="font12 color6 nowrap">{{dataList.teacher_list[list].hrPosition}}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div v-else><el-empty :image-size="100" description="<?php echo Yii::t("ptc", "No Data"); ?>"></el-empty></div>
                        </div>   
                    </div>
                    <div class='col-md-6'>
                        <div class="row module " >
                            <div class='flex p24'>
                                <span class='flex1 font14'><?php echo Yii::t("ptc", "Student"); ?></span>
                                <div>
                                    <button type="button" class="btn btn-link" v-if='dataList.study.student && dataList.study.student.length>0'   @click='batchDel("study",2,"stu")'><?php echo Yii::t("directMessage", "Clear All");?></button>
                                    <button type="button" class="btn btn-primary" @click='addStudent("study",2)'><span class='el-icon-circle-plus-outline'></span> <?php echo Yii::t("global", "Add"); ?></button>
                                </div>
                            </div>
                            <div v-if='dataList.study.student && dataList.study.student.length>0'  class='overflow-y scroll-box max500'>                    
                                <div class='col-xs-6 '   v-for='(list,index) in dataList.study.student'>
                                    <div class="media listMedia" >
                                        <div class="media-left pull-left media-middle">
                                            <a href="javascript:void(0)">
                                                <img :src="dataList.student_list[list].avatar" data-holder-rendered="true" class="avatar">
                                            </a>
                                        </div>
                                        <div class="media-right pull-right pt12 text-right">
                                            <span class='el-icon-circle-close font16 closeIcon' @click='delStudent("study",list,index,2)'></span>
                                        </div>
                                        <div class="media-body pt4 media-middle">
                                            <div class="lineHeight20  text-primary">
                                                <span class='font14 color3 nowrap'>{{dataList.student_list[list].name}}</span>
                                            </div>
                                            <div class="font12 color6 nowrap">{{dataList.student_list[list].className}}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div v-else><el-empty :image-size="100" description="<?php echo Yii::t("ptc", "No Data"); ?>"></el-empty></div>
                        </div>   
                    </div>
                </div>
            </div>
            <div class="panel panel-default mt24" v-if='dataList.behavior'>
                <div class="panel-heading">
                    <div class='font16 color3 fontBold'><span class='glyphicon glyphicon-bookmark colorRed font14'></span> <?php echo Yii::t("referral", "Behavior Support"); ?></div>
                    <!-- <div class='color6 font14'><?php echo Yii::t("referral", "I am the explanatory text for this label"); ?></div> -->
                </div>
                <div class="panel-body ">
                    <span class='borderLeftpos'></span>
                    <div class='col-md-6'>
                        <div class="row module " >
                            <div class='flex p24'>
                                <span class='flex1 font14'><?php echo Yii::t("teaching", "Teacher"); ?></span>
                                <div>
                                    <button type="button" class="btn btn-link" v-if='dataList.behavior.teacher && dataList.behavior.teacher.length>0' @click='batchDel("behavior",3,"teacher")'><?php echo Yii::t("directMessage", "Clear All");?></button>
                                    <button type="button" class="btn btn-primary"  @click='addTeacher("behavior",3)'><span class='el-icon-circle-plus-outline'></span> <?php echo Yii::t("global", "Add"); ?></button>
                                </div>
                            </div>
                            <div v-if='dataList.behavior.teacher && dataList.behavior.teacher.length>0'  class='overflow-y scroll-box max500'>
                                <div class='col-xs-6 '  v-for='(list,index) in dataList.behavior.teacher'>
                                    <div class="media listMedia" >
                                        <div class="media-left pull-left media-middle">
                                            <a href="javascript:void(0)">
                                                <img :src="dataList.teacher_list[list].photoUrl" data-holder-rendered="true" class="avatar">
                                            </a>
                                        </div>
                                        <div class="media-right pull-right pt12 text-right">
                                            <span class='el-icon-circle-close font16 closeIcon' @click='delTeacher("behavior",list,index,3)'></span>
                                        </div>
                                        <div class="media-body pt4 media-middle">
                                            <div class="lineHeight20  text-primary">
                                                <span class='font14 color3 nowrap'>{{dataList.teacher_list[list].name}}</span>
                                            </div>
                                            <div class="font12 color6 nowrap">{{dataList.teacher_list[list].hrPosition}}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div v-else><el-empty :image-size="100" description="<?php echo Yii::t("ptc", "No Data"); ?>"></el-empty></div>
                        </div>   
                    </div>
                    <div class='col-md-6'>
                        <div class="row module ">
                            <div class='flex p24'>
                                <span class='flex1 font14'><?php echo Yii::t("ptc", "Student"); ?></span>
                                <div>
                                    <button type="button" class="btn btn-link" v-if='dataList.behavior.student && dataList.behavior.student.length>0' @click='batchDel("behavior",3,"stu")'><?php echo Yii::t("directMessage", "Clear All");?></button>
                                    <button type="button" class="btn btn-primary"  @click='addStudent("behavior",3)'><span class='el-icon-circle-plus-outline'></span> <?php echo Yii::t("global", "Add"); ?></button>
                                </div>
                            </div>
                            <div v-if='dataList.behavior.student && dataList.behavior.student.length>0'  class='overflow-y scroll-box max500'>
                                <div class='col-xs-6 '   v-for='(list,index) in dataList.behavior.student'>
                                    <div class="media listMedia" >
                                        <div class="media-left pull-left media-middle">
                                            <a href="javascript:void(0)">
                                                <img :src="dataList.student_list[list].avatar" data-holder-rendered="true" class="avatar">
                                            </a>
                                        </div>
                                        <div class="media-right pull-right pt12 text-right">
                                            <span class='el-icon-circle-close font16 closeIcon' @click='delStudent("behavior",list,index,3)'></span>
                                        </div>
                                        <div class="media-body pt4 media-middle">
                                            <div class="lineHeight20  text-primary">
                                                <span class='font14 color3 nowrap'>{{dataList.student_list[list].name}}</span>
                                            </div>
                                            <div class="font12 color6 nowrap">{{dataList.student_list[list].className}}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div v-else><el-empty :image-size="100" description="<?php echo Yii::t("ptc", "No Data"); ?>"></el-empty></div>
                        </div>   
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="addClassModal" tabindex="-1" role="dialog" data-backdrop="static" data-keyboard="false" >
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentCommentsModalLabel"><?php echo Yii::t("global", "Add"); ?></h4>
                </div>
                <div class="modal-body p0 relative">
                    <span class='borderLeftpos'></span>
                    <div style='max-height:600px;' class='p24 row'>
                        <div class='col-md-6 col-sm-6'>
                            <div>
                                <el-input
                                placeholder="<?php echo Yii::t("global", "Search");?>"
                                v-model='searchText' 
                                clearable>
                                </el-input>
                            </div>
                            <div  class="tab-pane active mt15 scroll-box" id="class" v-if='searchText==""'  style='max-height:500px;overflow-y:auto'>
                                <div v-for='(list,index) in classList' class='relative mb16'>
                                    <p  @click='getChild(list)'>
                                        <span  class='font14 color606 cur-p'>{{list.title}} </span>
                                        <span class='el-icon-arrow-down ml5' v-if='classId!=list.classid'></span>
                                        <span class='el-icon-arrow-up ml5' v-else></span>
                                    </p>
                                    <p  class='allCheck' v-if='classId==list.classid'><button class="btn btn-default pull-right btn-xs" type="button" @click='selectAll(list,index)'  v-if='list.childData && list.childData.length!=0'><?php echo Yii::t("global", "Select All");?></button></p>
                                    <div  class='border scroll-box mr10 childList' v-if='classId==list.classid'>
                                        <div v-if='!childLoading'>
                                            <div class='' v-if='list.childData && list.childData.length!=0'>
                                                <div class="media m0 listMedia" v-for='(item,idx) in list.childData'>
                                                    <div class="media-left pull-left media-middle">
                                                        <a href="javascript:void(0)">
                                                            <img :src="item.avatar" data-holder-rendered="true" class="media-object img-circle avatar32">
                                                        </a>
                                                    </div>
                                                    <div v-if='item.stuLoading'>
                                                        <div v-if='item.disabled' class="media-right pull-right text-muted lineHeight">
                                                            <span class='cur-p mt10 color9'><?php echo Yii::t("newDS", "Subscribed");?></span>
                                                        </div>
                                                        <div v-else class="media-right pull-right text-muted" @click='assignChildren(item,index,idx)'>
                                                            <span class='cur-p font16 bluebg mt12 el-icon-circle-plus-outline'></span>
                                                        </div>
                                                    </div>
                                                    <div class='childLoading' v-else>
                                                        <span></span>
                                                    </div>
                                                    <div class="media-body media-middle">
                                                        <h4 class="media-heading font14 lineHeight">{{item.name}}</h4>
                                                    </div>
                                                </div>
                                            </div>
                                            <div v-else>
                                                <div class='font12 text-muted text-center'><?php echo Yii::t("newDS", "no student in this class");?></div>
                                            </div>
                                        </div>
                                        <div class='loading' v-else>
                                            <span></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div v-else>
                                <div v-if='searchChildList.length!=0' class="mt15 scroll-box" style='max-height:500px;overflow-y:auto'>                               
                                    <div class="media mt10 listMedia" v-for='(item,idx) in searchChildList'>
                                        <div class="media-left pull-left media-middle">
                                            <a href="javascript:void(0)">
                                                <img :src="item.avatar" data-holder-rendered="true" class="media-object img-circle avatar32">
                                            </a>
                                        </div>
                                        <div v-if='item.disabled' class="media-right pull-right text-muted lineHeight">
                                            <span class='cur-p mt10'><?php echo Yii::t("newDS", "Subscribed");?></span>
                                        </div>
                                        <div v-else class="media-right pull-right text-muted" @click='assignChildren(item,idx,"search")'>
                                            <span class='cur-p font16 bluebg mt12 el-icon-circle-plus-outline'></span>
                                        </div>
                                        <div class="media-body media-middle">
                                            <h4 class="media-heading font14 color3">{{item.name}}</h4>
                                            <div class="text-muted color6">{{item.className}}</div>
                                        </div>
                                    </div>
                                </div>
                                <div v-else-if='searchText!=""'>
                                        <div class='font14 color6 text-center mt20'><?php echo Yii::t("ptc", "No Data"); ?></div>    
                                </div>
                            </div>
                        </div>
                        <div class='col-md-6 col-sm-6 borderLeft'>
                            <p class='mt10 font14 color606'>
                            <?php echo Yii::t("newDS", " ");?>{{childSelected.length}}<?php echo Yii::t("newDS", " student(s) selected");?>
                                <button class="btn btn-link pull-right btn-xs font14" v-if='childSelected.length!=0' type="button" @click='batchDel("modal")'><?php echo Yii::t("directMessage", "Clear All");?></button>
                            </p>
                            <div class='scroll-box p10 overflow-y' style='height:500px'>
                                <div class="media m0 listMedia" v-for='(list,index) in childSelected'>
                                    <div class="media-left pull-left media-middle">
                                        <a href="javascript:void(0)">
                                            <img :src="list.avatar" data-holder-rendered="true" class="media-object img-circle avatar32">
                                        </a>
                                    </div>
                                    <div class="media-right pull-right text-muted" @click='Unassign(list,index)'>
                                        <span class='closeChild cur-p mt10 font16 el-icon-circle-close'></span>
                                    </div>
                                    <div class="media-body media-middle">
                                        <h4 class="media-heading font14 color3">{{list.name}}</h4>
                                        <div class="text-muted color6">{{list.className}}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class='clearfix'></div>
                    </div>
                </div> 
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Cancel"); ?></button>
                    <button type="button" class="btn btn-primary" @click='setChild()'><?php echo Yii::t("global", "OK"); ?></button>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="addTeacherModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel"  aria-labelledby="wantSayModalLabel"  data-backdrop="static">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("global", "Add"); ?></h4>
                </div>
                <div class="modal-body">
                  
                        <div class='color6 font14 '><?php echo Yii::t("global", "Search"); ?></div>
                        <div  class='flex mt16 mb16'>
                            <el-select
                                    v-model="teacherUid"
                                    filterable
                                    remote
                                    @visible-change="loseFocus"
                                    clearable
                                    class='inline-input flex1 formControl'
                                    reserve-keyword
                                    placeholder="<?php echo Yii::t("directMessage", "Staff Name"); ?>"
                                    :remote-method="remoteMethod"
                                    prefix-icon="el-icon-search"
                                    :loading="loading">
                                <el-option
                                        v-for="item in options"
                                        :key="item.uid"
                                        :label="item.name"
                                        class='optionSearch mb8'
                                        :value="item.uid">
                                    <div class="media">
                                        <div class="media-left pull-left media-middle">
                                            <a href="javascript:void(0)">
                                                <img :src="item.photoUrl" data-holder-rendered="true" class="media-object img-circle avatar">
                                            </a>
                                        </div>
                                        <div class="media-body mt5 media-middle">
                                            <h4 class="media-heading font14 color3 text_overflow">{{item.name}}</h4>
                                            <div class="text-muted text_overflow font12">{{ item.hrPosition }}</div>
                                        </div>
                                    </div>
                                </el-option>
                            </el-select>
                            <button type="button" class="btn btn-primary ml16"  :disabled='teacherUid==""?true:false' @click='confirmTeacher'><?php echo Yii::t("global", "Add"); ?></button>
                        </div>
                        <div class="row row-no-gutters" v-if='viewType!=""'>
                            <div class='col-xs-6 pb16'   v-for='(list,index) in  dataList[viewType].teacher'>
                                <div class="media listMedia" >
                                    <div class="media-left pull-left media-middle">
                                        <a href="javascript:void(0)">
                                            <img :src="dataList.teacher_list[list].photoUrl" data-holder-rendered="true" class="avatar">
                                        </a>
                                    </div>
                                    <div class="media-right pull-right pt12 text-right">
                                        <span class='el-icon-circle-close font16 closeIcon' @click='delTeacher(viewType,list,index,sendType)'></span>
                                    </div>
                                    <div class="media-body pt4 media-middle">
                                        <div class="lineHeight20  text-primary">
                                            <span class='font14 color3 nowrap'>{{dataList.teacher_list[list].name}}</span>
                                        </div>
                                        <div class="font12 color6 nowrap">{{dataList.teacher_list[list].hrPosition}}</div>
                                    </div>
                                </div>
                            </div>
                        </div>    
                </div>
            </div>
        </div>
    </div>
    <!-- 清空确认框 -->
    <div class="modal fade" id="clearModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
        <div class="modal-dialog modal-sm" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel">
                       <span><?php echo Yii::t("directMessage", "Clear All"); ?></span>
                    </h4>
                </div>
                <div class="modal-body" >
                    <div ><?php echo Yii::t("directMessage", "确认清空吗？"); ?></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                    <button type="button" class="btn btn-primary" @click='clearStu()' v-if='role=="stu"'><?php echo Yii::t("message", "OK");?></button>
                    <button type="button" class="btn btn-primary" @click='clearTeacher()' v-else><?php echo Yii::t("message", "OK");?></button>
                </div>
            </div>
        </div>
    </div>
    <!-- 删除确认框 -->
    <div class="modal fade" id="delModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
        <div class="modal-dialog modal-sm" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel">
                       <span><?php echo Yii::t("global", "Delete"); ?></span>
                    </h4>
                </div>
                <div class="modal-body" >
                    <div ><?php echo Yii::t("directMessage", "Proceed to remove?"); ?></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                    <button type="button" class="btn btn-primary" v-if='delType=="teacher"' @click='delTeacher("del")'><?php echo Yii::t("message", "OK");?></button>
                    <button type="button" class="btn btn-primary" v-if='delType=="student"' @click='delStudent("del")'><?php echo Yii::t("message", "OK");?></button>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>
<script>
    var department= <?php echo CJSON::encode($department)?>;
    var height=document.documentElement.clientHeight;
    var container = new Vue({
        el: "#container",
        data: {
            groupName:{
                ES:'<?php echo Yii::t("referral", "Elementary School"); ?>',
                MS:'<?php echo Yii::t("referral", "Secondary School"); ?>',
            },
            department:department,
            search:'',
            groupType:'',
            height:height,
            dataList:{},
            addChildType:'',
            classList:[],
            classId:'',
            childSelected:[],
            applyChild:{},
            searchText:'',
            teacherUid:{},
            teacherSelected:[],
            options:[],
            loading:false,
            searchChildList:[],
            viewType:'',
            sendType:'',
            clearShow:'',
            clearSend:'',
            role:'',
            delType:'',
            delTeacherList:{},
            delStudentList:{}
        },
        watch:{
            searchText(old,newValue){
                if(old!=''){
                    this.searchChild()
                }
            }
        },
        created: function() {
            let currentType=sessionStorage.getItem('groupType') || ''
            if(department.indexOf(currentType)!=-1){
                this.groupType=currentType
            }else{
                this.groupType=department[0]
                sessionStorage.removeItem("groupType");
            }
            this.getClass()
            this.getData()
        },
        computed: {},
        methods: {
            loseFocus(val) {
                // 下拉框隐藏时
                if (!val) {
                this.options=[]
                }
            },
            tabgroupType(type){
                sessionStorage.setItem('groupType',type);
                this.groupType=type
                this.getData()
            },
            getData(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("getSupport") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        group:this.groupType
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.dataList=data.data
                            // that.dataList.student_list.forEach(item => {
                            //     item.loading=true
                            // });
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            searchChild(){
                var childId=[]
                if(this.childSelected!=null){
                    for(var i=0;i<this.childSelected.length;i++){
                        childId.push(this.childSelected[i].id)
                    }
                }
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("studentSearch") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        child_name:this.searchText,
                        group:this.groupType
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            for(var i=0;i<data.data.length;i++){
                                data.data[i].loading=true
                                if (childId.indexOf(data.data[i].id+'')!=-1) {
                                    data.data[i].disabled=true
                                }else{
                                    data.data[i].disabled=false
                                }
                            }
                            that.searchChildList=data.data
                            that.$forceUpdate()
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            getClass(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("classList") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        group:this.groupType
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            data.data.forEach(item => {
                                item.childData=[]
                            })
                            that.classList=data.data

                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            addApply(){
                this.searchChildList=[]
                $('#newApplyModal').modal('show')
            },
            showDetails(){
                $('#contentModal').modal('show')
            },
            addTeacher(viewType,sendType){
                this.viewType=viewType
                this.sendType=sendType
                this.options=[]
                $('#addTeacherModal').modal('show')
            },
            confirmTeacher(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("setSupportTeacher") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        teacher_id:this.teacherUid,
                        type:this.sendType,//2-学习计划支持 3-行为计划支持
                        status: "1",//1-设置 2取消
                        group:this.groupType
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.teacherUid=''
                            that.options = []
                            resultTip({
                                msg: data.message
                            });
                            that.getData()
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            clearTeacher(){
                let that=this
                var targetId=[]
                for(var i=0;i<that.dataList[this.clearShow].teacher.length;i++){
                    targetId.push(that.dataList[this.clearShow].teacher[i])
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("setSupportTeacher") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        teacher_id:targetId,
                        type:this.clearSend,//2-学习计划支持 3-行为计划支持
                        status: "2",//1-设置 2取消
                        group:this.groupType
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.getData()
                            resultTip({
                                msg: data.state
                            });
                            $('#clearModal').modal('hide')
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            delTeacher(show,list,index,type){
                this.delType='teacher'
                if(show!='del'){
                    this.delTeacherList={
                        show:show,
                        list:list,
                        index:index,
                        type:type
                    }
                    $('#delModal').modal('show')
                    return
                }
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("setSupportTeacher") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        teacher_id:this.delTeacherList.list,
                        type:this.delTeacherList.type,//2-学习计划支持 3-行为计划支持
                        status: "2",//1-设置 2取消
                        group:this.groupType
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.dataList[that.delTeacherList.show].teacher.splice(that.delTeacherList.index,1)
                            resultTip({
                                msg: data.message
                            });
                            $('#delModal').modal('hide')
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            remoteMethod(query) {
                let that=this
                if (query !== '') {
                    this.loading = true;
                    $.ajax({
                        url: '<?php echo $this->createUrl("teacherSearch") ?>',
                        type: "post",
                        dataType: 'json',
                        data: {
                            teacher_name:query,
                            group:this.groupType
                        },
                        success: function(data) {
                            if (data.state == 'success') {
                                that.options = Object.values(data.data) ;
                            }else{
                                resultTip({
                                    error: 'warning',
                                    msg: data.message
                                });
                            }
                            that.loading = false;
                        },
                        error: function(data) {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            that.loading = false;
                        },
                    })
                } else {
                    this.options = [];
                }
            },
            addStudent(viewType,sendType){
                this.viewType=viewType
                this.sendType=sendType
                var list=[]
                if(this.dataList[viewType].student){
                    this.dataList[viewType].student.forEach(item => {
                        this.dataList.student_list[item].loading=true
                        list.push(this.dataList.student_list[item])
                    });
                }           
                this.searchChildList=[]
                this.searchText=''
                this.classId=''
                this.childSelected=list;
                $('#addClassModal').modal('show')
            },
            addChild(type){
                this.addChildType=type
                this.searchChildList=[]
                this.searchText=''
                this.classId=''
                this.childSelected=JSON.parse(JSON.stringify(this.confirmChild));
                $('#addClassModal').modal('show')
            },
            selectChild(list){
                this.applyChild=list
                $('#addClassModal').modal('hide')
            },
            getChild(list){
                let that=this
                if(that.classId==list.classid){
                    that.classId=''
                    return
                }
                that.classId=list.classid
                var childId=[]
                if(this.childSelected!=null){
                    for(var i=0;i<this.childSelected.length;i++){
                        childId.push(this.childSelected[i].id)
                    }
                }
                for(var i=0;i<that.classList.length;i++){
                    if(that.classList[i].classid==list.classid){
                        if(that.classList[i].childData.length!=0){
                            for(var j=0;j<that.classList[i].childData.length;j++){
                                if (childId.indexOf(that.classList[i].childData[j].id)!=-1) {
                                    that.classList[i].childData[j].disabled=true
                                }else{
                                    that.classList[i].childData[j].disabled=false
                                }
                            }
                            that.$forceUpdate()
                            return
                        }
                    }
                }
                that.childLoading=true
                $.ajax({
                    url: '<?php echo $this->createUrl("childList") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        classId:list.classid
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            for(var i=0;i<data.data.length;i++){
                                data.data[i].classTitle=list.title
                                data.data[i].stuLoading=true
                                data.data[i].loading=true
                                if (childId.indexOf(data.data[i].id)!=-1) {
                                    data.data[i].disabled=true
                                }else{
                                    data.data[i].disabled=false
                                }
                            }
                            that.sortData(data.data)
                            for(var i=0;i<that.classList.length;i++){
                                if(that.classList[i].classid==list.classid){
                                    that.classList[i].childData=data.data
                                }
                            }
                            that.childLoading=false
                            that.$forceUpdate()
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            sortData(list){
                <?php if (Yii::app()->language == 'zh_cn') { ?>
                    list.sort((a, b)=> a.name.localeCompare(b.name, 'zh'));
                <?php } else { ?>
                    list.sort((a, b) => a.name.charCodeAt(0) - b.name.charCodeAt(0));
                <?php } ?>
            },
            selectAll(list,index){
                for(var i=0;i<list.childData.length;i++){
                    if (!list.childData[i].disabled) {
                        this.childSelected.push(list.childData[i])
                        Vue.set(this.classList[index].childData[i], 'disabled', true);
                    }
                }
                this.$forceUpdate()
            },
            assignChildren(list,index,idx){
                if(idx=='search'){
                    this.searchChildList[index].disabled=true
                }else{
                    Vue.set(this.classList[index].childData[idx], 'disabled', true);

                }
                this.$forceUpdate()
                this.childSelected.push(list)
            },
            Unassign(data,index){
                for(var i=0;i<this.classList.length;i++){
                    for(var j=0;j<this.classList[i].childData.length;j++){
                        if(data.id==this.classList[i].childData[j].id){
                            Vue.set(this.classList[i].childData[j], 'disabled', false);
                        }
                    }
                }
                for(var i=0;i<this.searchChildList.length;i++){
                    if(data.id==this.searchChildList[i].id){
                        Vue.set(this.searchChildList[i], 'disabled', false);
                    }
                }
                this.$forceUpdate()
                this.childSelected.splice(index,1)
            },
            setChild(){
                let that=this
                var classids=[]
                this.childSelected.forEach(item => {
                    classids.push(item.id)
                });
                $.ajax({
                    url: '<?php echo $this->createUrl("setLabel") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        child_id:classids,
                        position:this.sendType,
                        num:'1'
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.getData()
                            $('#addClassModal').modal('hide')
                            resultTip({
                                msg: data.message
                            });
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            delStudent(show,list,index,type){
                this.delType='student'
                if(show!='del'){
                    this.delStudentList={
                        show:show,
                        list:list,
                        index:index,
                        type:type
                    }
                    $('#delModal').modal('show')
                    return
                }
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("setLabel") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        child_id:[this.delStudentList.list],
                        position:this.delStudentList.type,
                        num:'0'
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            for(var i=0;i<that.classList.length;i++){
                                for(var j=0;j<that.classList[i].childData.length;j++){
                                    if(that.classList[i].childData[j].id==list){
                                        Vue.set(that.classList[i].childData[j], 'disabled', false);
                                    }
                                }
                            }
                            for(var i=0;i<that.searchChildList.length;i++){
                                if(that.searchChildList[i].id==list){
                                    Vue.set(that.searchChildList[i], 'disabled', false);
                                }
                            }
                            that.dataList[that.delStudentList.show].student.splice(that.delStudentList.index,1)
                            resultTip({
                                msg: data.message
                            });
                            $('#delModal').modal('hide')
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            clearStu(){
                let that=this
                var targetId=[]
                for(var i=0;i<that.dataList[this.clearShow].student.length;i++){
                    targetId.push(that.dataList[this.clearShow].student[i])
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("setLabel") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        child_id:targetId,
                        position:this.clearSend,
                        num:'0'
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.getData()
                            resultTip({
                                msg: data.state
                            });
                            for(var i=0;i<that.classList.length;i++){
                                for(var j=0;j<that.classList[i].childData.length;j++){
                                    Vue.set(that.classList[i].childData[j], 'disabled', false);
                                    
                                }
                            }
                            $('#clearModal').modal('hide')
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            batchDel(data,type,role){
                this.clearShow=data
                this.clearSend=type
                this.role=role
                $('#clearModal').modal('show')             
            },
        }
    })
</script>
