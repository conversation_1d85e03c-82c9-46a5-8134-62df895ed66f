<?php

class SurveyController extends ProtectedController {

    public function init() {
        parent::init();
        Mims::<PERSON><PERSON><PERSON><PERSON><PERSON>('HtoolKits');
        Yii::import('common.models.survey.*');
    }

    /*
     * @see CController::actions()
     */

    public function actions() {
        return array(
            //'feedback'=>'ext.survey.actions.feedbackAction',
            'feedbackModel' => 'ext.survey.actions.feedbackModelAction',
        );
    }

    public function actionIndex($id = 0) {
        $schoolid = $this->getSchoolId();
        $survey_obj = Survey::model()->getOneSurvey($id, $schoolid);

        if (empty($survey_obj) || $survey_obj->isNewRecord) {
            //该问卷未启用
            Yii::app()->user->setFlash('notice', Yii::t("survey", 'Disable'));
            $this->redirect(Yii::app()->createUrl('/' . $this->childid . '/child/profile/welcome'));
        } else {
            $fb_flag = SurveyFeedback::model()->isFeedback($id, $this->childid);
            if ('has_fb' == $fb_flag) {
                //该问卷已回复
                Yii::app()->user->setFlash('notice', Yii::t("survey", 'Replied'));
                $this->redirect(Yii::app()->createUrl('/' . $this->childid . '/child/profile/welcome'));
            } elseif ('not_fb' == $fb_flag) {

                $param = '';

                if($survey_obj->survey_type == Survey::SURVEY_TYPE_TWO){
                    $addParams = CommonUtils::LoadConfig('CfgSurveyParams');
                    if(isset($addParams[$id])){
                        $param = isset($addParams[$id][$schoolid]) ? $addParams[$id][$schoolid] :
                            '';
                    }

                }

                $tk = new HtoolKits();
                $langKey = Yii::app()->language == 'en_us' ? 'en' : 'cn';
                $survey_obj->surveyDetail->end_time = Mims::formatDateTime($survey_obj->surveyDetail->end_time, 'long');
//                Yii::app()->clientScript->registerCssFile(Yii::app()->theme->baseUrl . '/css/vote_survey.css');
                Yii::app()->clientScript->registerCssFile(Yii::app()->theme->baseUrl . '/css/vote_survey.css?t='.Yii::app()->params['refreshAssets']);
                $this->render('index', array('survey_id' => $id, 'langKey' => $langKey, "survey_obj" => $survey_obj,
                    'param'=>Mims::formatDateTime(strtotime($param),'long')
                ));
            } elseif ('inexistence' == $fb_flag) {
                //该问卷不存在
                Yii::app()->user->setFlash('notice', Yii::t("survey", 'Inexistence'));
                $this->redirect(Yii::app()->createUrl('/' . $this->childid . '/child/profile/welcome'));
            }
        }
    }

}