<?php
$form = $this->beginWidget('CActiveForm', array(
    'id' => 'child_interivew',
    'enableAjaxValidation' => false,
    'action' => $this->createUrl('interviewResults', array('childid' => $child->id)),
    'htmlOptions' => array('class' => 'J_ajaxForm', 'role' => 'form'),
));
$gender = array(1=>'Boy', 2=> 'Girl');
Yii::import('common.models.visit.*');
$grade = AdmissionsDs::getConfig();
$data = array();
$countries = Country::model()->getCountryList('','','en');
$Diglossia = Diglossia::model()->getLanguage('true');

if($childinterview->discipline_stat){
    $data = json_decode($childinterview->discipline_stat,true);
    $child->interviewDate = date("Y-m-d", $data['interviewDate']);
}


$schoolHistoryArr = array();
if($child->school_history){
    $school_history = json_decode($child->school_history,true);
    foreach ($school_history as $val){
        if($val['school']){
            $schoolHistoryArr[] = $val['school'];
        }
    }
    if($schoolHistoryArr){
        $schoolHistoryArr = implode(',',$schoolHistoryArr);
    }
}

?>

<table class="table table-bordered">
    <h3 class="text-center"><?php echo (isset($calender) && isset($calender[$child->start_year])) ? $calender[$child->start_year] : '' ; ?> Admissions Assessment Check List ( ELC & G1 )</h3><br/>
</table>
<div>
    <table class="table table-bordered">
        <tr>
            <td><?php echo Yii::t('user', 'Student Behavior') ?>:</td>
            <td>
                <?php
                    echo CHTml::radioButtonList('interview[studentInterview]', ($data && $data['studentInterview']) ? $data['studentInterview'] : '', array(1=>'A',2=>'B',3=>'C',4=>'D'), array('separator'=>'', 'template'=>'<div class="radio-inline col-md-2">{input} {label}</div>'))
                ?>
            </td>
            <td rowspan="3">
                <div>
                    <span><?php echo Yii::t('user','Student Interviewer'); ?> :</span>
                    <span><?php echo ($data) ? $data['studentAcademic'] : ""; ?></span>
                </div>
                <br>
                <br>
                <br>
                <div>
                    <span><?php echo Yii::t('user','Parents Interviewer'); ?> :</span>
                    <span><?php echo ($data) ? $data['parentsInterviews'] : ""; ?></span>
                </div>
            </td>
        </tr>
        <tr>
            <td><?php echo Yii::t('user', 'Student Academic') ?>:</td>
            <td>
            <?php echo CHTml::radioButtonList('interview[academic]', ($data && $data['academic']) ? $data['academic'] : '', array(1=>'A',2=>'B',3=>'C',4=>'D'), array('separator'=>'', 'template'=>'<div class="radio-inline col-md-2">{input} {label}</div>')) ?>
            </td>
        </tr>
        <tr>
            <td><?php echo Yii::t('user', 'Parents Interview') ?>:</td>
            <td><?php echo CHTml::radioButtonList('interview[parentsInterview]', ($data && $data['parentsInterview']) ? $data['parentsInterview'] : '', array(1=>'A',2=>'B',3=>'C',4=>'D'), array('separator'=>'', 'template'=>'<div class="radio-inline col-md-2">{input} {label}</div>')) ?></td>
        </tr>
        <tr>
            <td>Note:</td>
            <td colspan="2">
                A: <?php echo Yii::t('user','Outstanding'); ?>
                B: <?php echo Yii::t('user','Above Average'); ?>
                C: <?php echo Yii::t('user','Average'); ?>
                D: <?php echo Yii::t('user','Below Average'); ?>
            </td>
        </tr>
    </table>
</div>

    <div>
        <h3>Student Information</h3>
        <table class="table table-bordered">
        <thead>
            <colgroup>
              <col style="width:50%">
              <col style="width:50%">
           </colgroup>
        </thead>
           <tbody>
            <tr>
                <td>English Name: <?php echo $child->en_name_last . ' ' .$child->en_name_middle . ' ' .$child->en_name?></td>
                <td>Chinese Name: <?php echo $child->cn_name . ' ' .$child->cn_name_last ?></td>
            </tr>
            <tr>
                <td>Nationality: <?php echo $countries[$child->nationality] ?></td>
                <td>Mother Language: <?php echo $Diglossia[$child->native_lang] ?></td>
            </tr>
            <tr>
                <td>Gender: <?php echo $gender[$child->gender] ?></td>
                <td>DOB: <?php echo date("Y-m-d", $child->birthday) ?></td>
            </tr>
            <tr>
                <td>Starting Year & Grade: <?php echo $grade['grade_en'][$child->start_grade] ?></td>
                <td>Previous School: <?php echo ($schoolHistoryArr) ? $schoolHistoryArr : '' ?></td>
            </tr>
            <tr>
                <td>Father:
                    <?php echo ($child->father_name) ? $child->father_name . '，' : '' ; ?>
                    <?php echo ($child->father_education) ? $grade['education'][$child->father_education]  . '，' : '' ; ?>
                    <?php echo ($child->father_position) ? $child->father_position  . '，' : '' ; ?>
                    <?php echo ($child->father_employer) ? $child->father_employer : '' ; ?>，
                </td>
                <td>
                    Mother:
                    <?php echo ($child->mother_name) ? $child->mother_name  . '，' : '' ?>
                    <?php echo ($child->mother_education) ? $grade['education'][$child->mother_education]  . '，' : '' ?>
                    <?php echo ($child->mother_position) ? $child->mother_position  . '，' : '' ?>
                    <?php echo ($child->mother_employer) ? $child->mother_employer : '' ?>
                </td>
            </tr>
            <tr>
                <td>Siblings at Daystar: <?php $siblingsDaystar = array( 1 => 'Yes', 2 => 'No' );
                    echo ($data && $data['siblingsDaystar']) ? $siblingsDaystar[$data['siblingsDaystar']] : '未选择' ;
                    ?>
                </td>
                <td>Staff Child: <?php echo ($child->is_staff == 1) ? "Yes" : "No" ?></td>
            </tr>
            </tbody>
        </table>
    </div>
<div>
    <h3><?php echo Yii::t('user','Other Materials') ?></h3>
    <table class="table table-bordered">
        <tr>
            <td width="200">学籍 (Xueji):</td>
            <td>
                <?php echo ($data) ? $data['xueji'] : ""; ?>
            </td>
            <td  width="200">
                <?php echo ($data) ? $data['xueji2'] : ""; ?>
            </td>
        </tr>
        <tr>
            <td width="200"><?php echo Yii::t('user', 'School Report') ?>:</td>
            <td>
                <?php echo ($data) ? $data['schoolReport'] : ""; ?>
            </td>
            <td>
                <?php echo ($data) ? $data['schoolReport2'] : ""; ?>
            </td>
        </tr>
        <tr>
            <td width="200"><?php echo Yii::t('user', 'Recommendation Letter') ?>:</td>
            <td>
                <?php echo ($data) ? $data['letter'] : ""; ?>
            </td>
            <td>
                <?php echo ($data) ? $data['letter2'] : ""; ?>
            </td>
        </tr>
    </table>
</div>



<div>
    
    <h3><?php echo Yii::t('user','Interview Date') ?>:
        <?php echo ($childinterview) ? date("Y-m-d", $childinterview->interview_time) : "为分配面试" ?>
    </h3>
    <h3>Person in Charge:</h3>
    <table class="table table-bordered">
        <tr>
            <td width="200"><?php echo Yii::t('user', 'Department') ?>:</td>
            <td><?php echo Yii::t('user', 'Accept') ?>:</td>
            <td><?php echo Yii::t('user', 'Reject') ?>:</td>
            <td><?php echo Yii::t('user', 'Note') ?>:</td>
            <td><?php echo Yii::t('user', 'Signature &Date') ?>:</td>
        </tr>
        <tr>
            <td width="200"><?php echo Yii::t('user', 'Admissions Director') ?>:</td>
            <td>
                <?php echo ($data) ? $data['director'] : ""; ?>
            </td>
            <td>
                <?php echo ($data) ? $data['director2'] : ""; ?>
            </td>
            <td>
                <?php echo ($data) ? $data['director3'] : ""; ?>
            </td>
            <td>
                <?php echo ($data) ? $data['director4'] : ""; ?>
            </td>
        </tr>
        <tr>
            <td width="200"><?php echo Yii::t('user', 'Principal') ?>:</td>
            <td>
                <?php echo ($data) ? $data['principal'] : ""; ?>
            </td>
            <td>
                <?php echo ($data) ? $data['principal2'] : ""; ?>
            </td>
            <td>
                <?php echo ($data) ? $data['principal3'] : ""; ?>
            </td>
            <td>
                <?php echo ($data) ? $data['principal4'] : ""; ?>
            </td>
        </tr>
    </table>
</div>
<?php $this->endWidget(); ?>