<?php
require(dirname(__FILE__) . '/sms/aliyun-php-sdk-core/Config.php');
use Sms\Request\V20160927 as Sms;

class Message {
    private $appkey = 'LTAITKwt2uxxgxms';
    private $secret = 'SBcHfAVOAoZjcUIcNSyqdnqdiFQHkU';
    public $signName;
    public $templateCode;
    public $number;
    public $content;

    public function setAttributes($number, $content, $templateCode = 'SMS_52110106', $signName = '艾毅教育')
    {
        if (!$number || !$content) {
            return false;
        }
        $this->number = $number;
        $this->content = $content;
        $this->templateCode = $templateCode;
        $this->signName = $signName;
    }

    public function send()
    {
        $iClientProfile = DefaultProfile::getProfile("cn-beijing", $this->appkey, $this->secret);        
        $client = new DefaultAcsClient($iClientProfile);    
        $request = new Sms\SingleSendSmsRequest();
        $request->setSignName($this->signName);/*签名名称*/
        $request->setTemplateCode($this->templateCode);/*模板code*/
        $request->setRecNum($this->number);/*目标手机号*/
        $request->setParamString($this->content);/*模板变量，数字一定要转换为字符串*/
        try {
            $response = $client->getAcsResponse($request);
            // print_r($response);
        }
        catch (ClientException  $e) {
            // print_r($e->getErrorCode());   
            // print_r($e->getErrorMessage());   
        }
        catch (ServerException  $e) {        
            // print_r($e->getErrorCode());   
            // print_r($e->getErrorMessage());
        }
    }

} 
