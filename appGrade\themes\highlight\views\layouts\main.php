<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en" lang="en">
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<meta name="language" content="en" />

	<?php
		$bgset = isset($_COOKIE['child_mims_bgset'])?$_COOKIE['child_mims_bgset']:'body-blue';
	?>
	<!-- blueprint CSS framework -->
	<link rel="stylesheet" type="text/css" href="<?php echo Yii::app()->theme->baseUrl; ?>/css/screen.css" media="screen, projection" />
	<link rel="stylesheet" type="text/css" href="<?php echo Yii::app()->theme->baseUrl; ?>/css/print.css" media="print" />
	<!--[if lt IE 8]>
	<link rel="stylesheet" type="text/css" href="<?php echo Yii::app()->themeManager->baseUrl; ?>/base/css/ie.css" media="screen, projection" />
	<![endif]-->
	<link rel="stylesheet" type="text/css" href="<?php echo Yii::app()->theme->baseUrl; ?>/css/form.css?t=<?php echo Yii::app()->params['refreshAssets'];?>" />
	<link rel="stylesheet" type="text/css" href="<?php echo Yii::app()->theme->baseUrl; ?>/css/main.css?t=<?php echo Yii::app()->params['refreshAssets'];?>" />
	<link rel="stylesheet" type="text/css" href="<?php echo Yii::app()->theme->baseUrl; ?>/css/bg.css?t=<?php echo Yii::app()->params['refreshAssets'];?>" />
    <link rel="stylesheet" type="text/css" href="<?php echo Yii::app()->theme->baseUrl; ?>/css/<?php echo Yii::app()->getLanguage()?>.css?t=<?php echo Yii::app()->params['refreshAssets'];?>" />
	<link rel="SHORTCUT ICON" href="/favicon.ico" />
	
    <script type="text/javascript">
        var yiiLang='<?php echo Yii::app()->getLanguage()?>',theme='<?php echo Yii::app()->theme->baseUrl?>';
        function normalText(s)
        {
            return s.replace(/font-size/gi, '').replace(/font-family/gi, '').replace(/margin/gi, '').replace(/padding/gi, '');
        }
    </script>
	
	<script type="text/javascript" src="<?php echo Yii::app()->themeManager->baseUrl; ?>/base/js/callback.js"></script>

	<?php
	if(Yii::app()->params['baiduStats'])
		$this->renderPartial('//statsScripts/baiduStats_head');
	?>
	
	<title><?php echo CHtml::encode($this->pageTitle); ?></title>
</head>

<body class="<?php echo $bgset?>">

<div class="themepage container showgrid" id="page" style="min-height:1040px;">

	<div id="header">
		<div id="logo" class="span-9">
			<?php
			$this->widget('ext.childSelector.ChildSelector', array(
				'myChildObjs' => $this->myChildObjs,
				'currentCid'  => $this->childid,
				'id'=> "child-selector",
				'class'=> "",
			));?>		
		
		</div>
		<div id="maintop" class="span-15 last">
			<div class="links">
			
				<?php if(Yii::app()->user->getId()):?>
				<span>
					<?php echo Yii::t("user",'Welcome back, :username', array(":username"=>Yii::app()->user->getName()));?>
				</span>
					<?php if (isset($this->role) &&($this->role != 'staff')):;?>
					<span>
						<?php echo CHtml::link(Yii::t("user",'Logout'), $this->createUrl("//user/logout"));?>
					</span>
					<?php endif; ?>
				<?php elseif(count(Yii::app()->user->getVisitCIds())):?>
				<span>
					<?php echo Yii::t("user",'Welcome, :username. ', array(":username"=>Yii::app()->user->getName()));?>
				</span>
				<span>
					<?php echo CHtml::link(Yii::t("user",'Logout'), $this->createUrl("//user/logout"));?>
				</span>
				<?php endif; ?>
				<span> <?php echo CHtml::ajaxLink(Yii::t("global", '中文' ),
												$this->createUrl("//site/lang",
																	  array("lang"=>"zh_cn")
																),
												array(
													  "dataType"=>"json",
													  "success"=>"function(data, textStatus, jqXHR){callback(data,'reload')}",
												))
				?> </span>
				<span> <?php echo CHtml::ajaxLink(Yii::t("global", 'English' ),
												$this->createUrl("//site/lang",
																	  array("lang"=>"en_us")
																),
												array(
													  "dataType"=>"json",
													  "success"=>"function(data, textStatus, jqXHR){callback(data,'reload')}",
												))
				?> </span>
				
				<div class="weather">
					<?php $this->widget('application.extensions.weather.Weather', array("view"=>"mini"));?>
				</div>
				
			</div>
			<div class="top-bar">
				<?php if(!Yii::app()->user->getIsGuest()):?>
					<div class="guide-icon">
						<?php
							echo CHtml::link(Yii::t("global","Guide"), array("//child/profile/welcome","demo"=>"guide"), array("id"=>"guide-icon","target"=>"_blank","title"=>Yii::t("global","User Guide")));
						?>
					</div>
				<?php endif;?>
			</div>
			<div class="nav-box">
			
				<?php $this->widget('zii.widgets.CMenu',array(
					'itemTemplate'=>'{menu}<em></em>',
					'htmlOptions'=>array(
						'class'=>"sf-menu",
						),
					'activateParents'=>true,
					'id'=>'navigation',
					'items'=>$this->nav,
				)); ?>
				
				<?php $this->widget('ext.replyReminder.ReplyReminderWidget');?>
				<?php $this->widget('ext.unpaidReminder.UnpaidReminderWidget');?>
				
			</div><!-- mainmenu -->
			
		</div>
		

			
		
		<div class="clear"></div>

	</div><!-- header -->
	
	<div class="clear"></div>

	
	<div id="mainbox">
		<?php echo $content; ?>
	</div>
	
	<div class="clear"></div>
	<div id="footer">
		<div class="span-6">
            <?php if(Mims::unIvy()): ?>
                <h5 class="logo-s" style="background: none;"></h5>
            <?php else:?>
			    <h5 class="logo-s"></h5>
            <?php endif;?>
		</div>
		<div class="span-18 last foot-content">
			<?php echo $this->renderPartial('//layouts/_footer_links');?>
		</div>
		<div class="clear"></div>
	</div><!-- footer -->
	<div class="clear"></div>
	
</div><!-- page -->
<script type="text/javascript" src="<?php echo Yii::app()->themeManager->baseUrl; ?>/base/js/hoverIntent.js"></script>
<script type="text/javascript" src="<?php echo Yii::app()->themeManager->baseUrl; ?>/base/js/superfish.js"></script>

			<script type="text/javascript">
			//$(function(){
				$("ul.sf-menu").superfish({
				delay		: 100,
				speed		: 'fast',
				dropShadows:   false
				});
			//});
			</script>	

<!--[if IE 6]>
<?php $this->renderPartial("//layouts/ie6_alert"); ?>
<![endif]-->

<?php $this->renderPartial("//layouts/themeroller"); ?>


<?php
if(Yii::app()->params['baiduStats'])
	$this->renderPartial('//statsScripts/baiduStats_common');
?>
</body>
</html>