<?php

class OnlineFileController extends BranchBasedController
{
    public $demoFlag = '-demo-'; //必须与通知服务器一致
    public $title = array();
    public $batchNum = 100;
    public $actionAccessAuths = array(
        'Index'             => 'o_A_Access',
    );

    public function createUrl($route, $params = array(), $ampersand = '&')
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        return parent::createUrl($route, $params, $ampersand);
    }

    public function init(){
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";
        Yii::import('common.models.online.*');
        $this->modernMenuFlag = 'campusOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site','Campus Workspace');
        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/vue2.js');
        // $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/xlsx.full.min.js');
        // $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/echarts.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/plupload/plupload.full.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/qiniu.min.js');
        // $cs->registerCssFile($cs->getCoreScriptUrl().'/jui/css/base/jquery-ui.css');
        // $cs->registerScriptFile($cs->getCoreScriptUrl().'/jui/js/jquery-ui.min.js');
        $this->branchSelectParams['hideOffice'] = false;
        $this->branchSelectParams['urlArray'] = array('//mcampus/onlineFile/index');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/jquery.qrcode.min.js');
    }


    /**
     * 模板主页面
     */
    public function actionIndex()
    {
        $onlineInfo = $this->info($this->staff->uid, $this->branchId, 0, 'list');
        $this->render('index', array(
            'onlineInfo' => $onlineInfo
        ));
    }

    public function actionOnlineList()
    {
        $id = Yii::app()->request->getParam('id', 0);
        $type = Yii::app()->request->getParam('type', 'list');
        $page = Yii::app()->request->getParam('page', 1);
        $is_user = Yii::app()->request->getParam('isUser', 0);
        $onlineInfo['info'] = $this->info($this->staff->uid, $this->branchId, $id, $type, $is_user, $page, $this->batchNum);

        $criteria = new CDbCriteria;
        $criteria->compare('school_id', $this->branchId);
        $criteria->compare('status', array(OnlineFile::STATUS_INVALID, OnlineFile::STATUS_VALID));
        if($is_user){
            $criteria->compare('created_by', $this->staff->uid);
        }else{
            $criteria->addCondition("authority=0 OR created_by ={$this->staff->uid}");
        }
        $criteria->compare('type', array('video','photo','pdf','mp3','zip'));
        $total = OnlineFile::model()->count($criteria);
        $onlineInfo['total'] = ($total) ? ceil($total / $this->batchNum) : 0;

        $this->addMessage('state', 'success');
        $this->addMessage('data', $onlineInfo);
        $this->addMessage('message', Yii::t('event', 'success'));
        $this->showMessage();
    }

    public static function info($uid,$school, $pid, $type, $is_user = 0, $page = 1, $batchNum = 100)
    {
        $criteria = new CDbCriteria();
        $criteria->compare('school_id', $school);
        $criteria->compare('status', array(OnlineFile::STATUS_INVALID, OnlineFile::STATUS_VALID));
        if($is_user){
            $criteria->compare('created_by', $uid);
        }else{
            $criteria->addCondition("authority=0 OR created_by ={$uid}");
        }
        if($type == 'list'){
            $criteria->compare('pid', $pid);
            $criteria->order = 'type ASC, created_at DESC';
        }else{
            $offset = ($page - 1) * $batchNum;
            $criteria->compare('type', array('video','photo','pdf','mp3','zip'));
            $criteria->limit = $batchNum; //取1条数据，如果小于0，则不作处理
            $criteria->offset = $offset; //两条合并起来，则表示 limit 10 offset 1,或者代表了。limit 1,10
            $criteria->order = 'created_at DESC';
        }

        $onlineModel = OnlineFile::model()->findAll($criteria);

        $serverConfs = CommonUtils::LoadConfig('CfgOnlineServer');


        $onlineInfo = array();
        $sConfs = (!OA::isProduction()) ? $serverConfs['dev'] : $serverConfs['prod'];

        if($onlineModel){
            foreach ($onlineModel as $val){
                $onlineInfo[] = array(
                    'id' => $val->id,
                    'school_id' => $val->school_id,
                    'pid' => $val->pid,
                    'type' => $val->type,
                    'display' => $val->display,
                    'path_original' => $sConfs['url'] . '/' . $val->path_original,
                    'path_processed' => ($val->path_processed) ? $sConfs['url'] . '/' . $val->path_processed : "",
                    'handle' => $val->handle,
                    'handle_status' => $val->handle_status,
                    'introduction_cn' => $val->introduction_cn,
                    'size' => ($val->size) ? $val->switchSize($val->size) : "",
                    'status' => $val->status,
                    'thumbnail' => ($val->thumbnail) ? $sConfs['url'] . '/' . $val->thumbnail : "",
                    'authority' => $val->authority,
                    'created_at' => date("Y-m-d H:i:s", $val->created_at),
                    'created_by' => ($val->createdBy) ? $val->createdBy->getName() : $val->created_by,
                    'updated_at' => date("Y-m-d H:i:s", $val->updated_at),
                    'updated_by' => ($val->updatedBy) ? $val->updatedBy->getName() : $val->updated_by,
                );
            }
        }

        return $onlineInfo;
    }
    /**
     * 增加或者修改
     */
    public function actionSaveOnline()
    {
        if(Yii::app()->request->isPostRequest){
            $id = Yii::app()->request->getParam('id', '');
            $pid = Yii::app()->request->getParam('pid', 0);
            $onlineModel = OnlineFile::model()->findByPk($id);

            if(!$onlineModel){
                $onlineModel = new OnlineFile();
                $onlineModel->pid = $pid;
                $onlineModel->status = 1;
                $onlineModel->school_id = $this->branchId;
                $onlineModel->created_at = time();
                $onlineModel->created_by = $this->staff->uid;
                if(!$_POST['OnlineFile']['display']){
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', '文件夹名称不能为空');
                    $this->showMessage();
                }
            }else{
                if($onlineModel->created_by != $this->staff->uid){
                    if(!Yii::app()->user->checkAccess('superDude')){
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', '不可修改其他人创建的文件夹和上传的图片或者视频');
                        $this->showMessage();
                    }
                }
            }
            $onlineModel->attributes = $_POST['OnlineFile'];
            $onlineModel->updated_at = time();
            $onlineModel->updated_by = $this->staff->uid;

            if(!$onlineModel->save()){
                $this->addMessage('state', 'fail');
                $err = current($onlineModel->getErrors());
                $this->addMessage('message', $err[0]);
                $this->showMessage();
            }
            $onlineInfo = $this->info($this->staff->uid, $this->branchId, $onlineModel->pid,'list');

            $this->addMessage('state', 'success');
            $this->addMessage('callback', 'cbSaveOnline');
            $this->addMessage('data', $onlineInfo);
            $this->addMessage('message', Yii::t('message', 'success'));
            $this->showMessage();
        }
    }

    /**
     *  获取当前文件或者文件夹的路劲
     */
    public function actionPath()
    {
        $id = Yii::app()->request->getParam('id', '');
        $this->path($id);
        $this->addMessage('state', 'success');
        $this->addMessage('callback', 'cbPath');
        $this->addMessage('data', $this->title);
        $this->addMessage('message', Yii::t('message', 'success'));
        $this->showMessage();
    }

    public function path($id)
    {
        $onlineModel = OnlineFile::model()->findByPk($id);
        if(0 < $onlineModel->pid){
            if(!$this->title) {
                $this->title = array(array('id' => $onlineModel->id, 'title' => $onlineModel->display));
            }else{
                array_unshift($this->title, array('id' => $onlineModel->id, 'title' => $onlineModel->display));
            }
            $this->path($onlineModel->pid,$this->title);
        }else{
            array_unshift($this->title, array('id' => $onlineModel->id, 'title' => $onlineModel->display));
        }
    }

    /**
     * 删除
     */
    public function actionDelOnline()
    {
        if(Yii::app()->request->isPostRequest){
            $id = Yii::app()->request->getParam('id', '');
            $onlineModel = OnlineFile::model()->findByPk($id);
            if(!$onlineModel){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '非法操作');
                $this->showMessage();
            }

            if($onlineModel->created_by != $this->staff->uid){
                if(!Yii::app()->user->checkAccess('superDude')){
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', '不可删除其他人创建的文件夹和上传的图片或者视频');
                    $this->showMessage();
                }
            }

            $criteria = new CDbCriteria();
            $criteria->compare('pid', $onlineModel->id);
            $criteria->compare('status', array(OnlineFile::STATUS_INVALID, OnlineFile::STATUS_VALID));
            $count = OnlineFile::model()->count($criteria);
            if($count){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '文件夹下有数据,不可删除');
                $this->showMessage();
            }
            $onlineModel->status = OnlineFile::STATUS_DELETE;
            $onlineModel->updated_at = time();
            $onlineModel->updated_by = $this->staff->uid;
            if(!$onlineModel->save()){
                $this->addMessage('state', 'fail');
                $err = current($onlineModel->getErrors());
                $this->addMessage('message', $err[0]);
                $this->showMessage();
            }
            $this->addMessage('state', 'success');
            $this->addMessage('callback', 'cbDelOnline');
            $this->addMessage('data', $onlineModel->id);
            $this->addMessage('message', Yii::t('message', 'success'));
            $this->showMessage();
        }
    }

    /*

     */
    public function actionGetQiniuOpt()
    {
        $id = Yii::app()->request->getParam('id', '');

        $serverConfs = CommonUtils::LoadConfig('CfgOnlineServer');

        $expiration = 10800; //10800s = 3h
        $reExpiration = ($expiration - 1800) * 1000; //1800s = .5h

        require_once( Yii::getPathOfAlias('common.components.qiniu.rs') . '.php' );
        require_once( Yii::getPathOfAlias('common.components.qiniu.io') . '.php' );
        Qiniu_SetKeys($QINIU_ACCESS_KEY, $QINIU_SECRET_KEY);

        $sConfs = (!OA::isProduction()) ? $serverConfs['dev'] : $serverConfs['prod'] ;

        $putPolicy = new Qiniu_RS_PutPolicy($sConfs['bucket']);
        $putPolicy->ForceSaveKey = true;
        $putPolicy->SaveKey = 'cloud01-file-' . $this->staff->uid . '$(etag)$(ext)';
        $putPolicy->CallbackUrl = $sConfs['callBackUrl'];
        $putPolicy->ReturnUrl = null; //为空表示不跳转
        $putPolicy->Expires = 10800;

        $putPolicy->CallbackBody = '{"key":"$(key)","fname":"$(fname)","fsize":"$(fsize)","hash":"$(etag)"'.
            ',"pid":"$(x:pid)","s":"$(x:s)"'.
            ',"p":"$(x:p)","l":"$(x:l)","x":"$(x:svr)","tst":"$(x:tst)","u":"$(x:u)","filetype":"$(x:filetype)"}';
        $putPolicy->callbackBodyType = "application/json";
        $upToken = $putPolicy->Token(null);

        $putPolicy->insertOnly = 1;
        $putPolicy->forceSaveKey = true;
        $putPolicy->SaveKey = 'cloud01-file-' . $this->staff->uid . '$(etag)' . '.mp4';
        $transcoding = 'cloud01-file-converted-' . $this->staff->uid . '$(etag)' . '.mp4';
        $saveas_key = base64_encode($sConfs['bucket'] . ':' . $transcoding);
        $putPolicy->MimeLimit = 'video/*';
        $putPolicy->callbackBodyType = 'application/json';
        if (Yii::app()->params['siteFlag'] == 'daystar') {
            $putPolicy->PersistentOps = 'vframe/jpg/offset/3/w/1280/h/720;avthumb/mp4/r/24/vcodec/libx264/s/1280x720/autoscale/1/avsmart/1/wmImage/aHR0cHM6Ly9tMi5maWxlcy5pdnlraWRzLmNuL3dtL2RheXN0YXIucG5n/wmGravity/NorthEast|saveas/' . $saveas_key;
        }
        else {
            $putPolicy->PersistentOps = 'vframe/jpg/offset/3/w/1280/h/720;avthumb/mp4/r/24/vcodec/libx264/s/1280x720/autoscale/1/avsmart/1/wmImage/aHR0cHM6Ly9tMi5maWxlcy5pdnlraWRzLmNuL3dtL2l2eS5wbmc=/wmGravity/NorthEast|saveas/' . $saveas_key;
        }
        $putPolicy->PersistentPipeline = $sConfs['pipeline'];
        $putPolicy->PersistentNotifyUrl = $sConfs['persistentsUrl'];
        //$putPolicy->ReturnBody = '{"key": $(key), "hash": $(etag)}';
        $videoToken = $putPolicy->Token(null);

        //自定义变量
        $xParams = array(
            'x:pid'=> $id, # 增加时候要存的pid
            'x:s'=> $this->branchId, # 增加时候要存的学校
            'x:p'=>OA::isProduction() ? 1 : 0, # 是不是生产系统
            'x:svr'=>$sConfs['id'], # 图片服务器ID
            'x:tst'=>in_array(Yii::app()->user->id, Yii::app()->params['mediaTestingUids']) ? 1 : 0, # 是否测试 根据 mediaTestingUids 配置判断
            'x:u'=>Yii::app()->user->id,
            'x:l'=>Yii::app()->language,
        );

        $data['token'] = $upToken;
        $data['videoToken'] = $videoToken;
        $data['sConfs'] = $sConfs;
        $data['xParams'] = $xParams;
        $this->addMessage('state', 'success');
        $this->addMessage('data', $data);
        $this->addMessage('message', Yii::t('message', 'success'));
        $this->showMessage();

    }
}

