<?php

/**
 * This is the model class for table "ivy_school_events".
 *
 * The followings are the available columns in table 'ivy_school_events':
 * @property integer $id
 * @property string $code
 * @property string $school_id
 * @property integer $city
 * @property integer $event_date
 * @property string $en_title
 * @property string $cn_title
 * @property string $img_banner
 * @property string $img_main
 * @property string $new_poster_en
 * @property string $new_poster_cn
 * @property string $new_bg
 * @property integer $stat
 * @property integer $apply_duedate
 * @property integer $userid
 * @property integer $update_timestamp
 * @property integer $introduction_cn
 * @property integer $introduction_en
 */
class SchoolEvents extends CActiveRecord
{
    public $poster_en;
    public $poster_cn;
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return SchoolEvents the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_school_events';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('code, en_title, cn_title, event_date, apply_duedate,stat', 'required'),
			array('city, event_date, stat, apply_duedate, userid, update_timestamp', 'numerical', 'integerOnly'=>true),
			array('code, en_title, cn_title, img_banner, img_main, new_poster_en, new_poster_cn', 'length', 'max'=>255),
			array('school_id', 'length', 'max'=>20),
			array('new_bg', 'length', 'max'=>7),
            array('introduction_cn, introduction_en', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, code, school_id, city, event_date, en_title, cn_title, img_banner, img_main, new_poster_en, new_poster_cn, new_bg, stat, apply_duedate, userid, update_timestamp', 'safe', 'on'=>'search'),
            array("poster_en,poster_cn", "file", "types" => "jpg, gif, png, jpeg", "allowEmpty" => true),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'show'=>array(self::HAS_MANY, 'SchoolEventsShow', 'eventid'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'code' => Yii::t('event', 'Code'),
			'school_id' => 'School',
			'city' => 'City',
			'event_date' => Yii::t('event', 'Event Date'),
			'en_title' => Yii::t('event', 'Title En'),
			'cn_title' => Yii::t('event', 'Title Cn'),
			'img_banner' => 'Img Banner',
			'img_main' => 'Img Main',
			'new_poster_en' => Yii::t('event', 'Poster En'),
			'new_poster_cn' => Yii::t('event', 'Poster Cn'),
			'poster_cn' => Yii::t('event', 'Poster Cn'),
			'poster_en' => Yii::t('event', 'Poster En'),
			'new_bg' => 'New Bg',
			'stat' => Yii::t('event', 'Stat'),
			'apply_duedate' => Yii::t('event', 'Apply Duedate'),
			'userid' => 'Userid',
			'update_timestamp' => 'Update Timestamp',
			'introduction_cn' => Yii::t('event', 'Introduction Cn'),
			'introduction_en' => Yii::t('event', 'Introduction En'),
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('code',$this->code,true);
		$criteria->compare('school_id',$this->school_id,true);
		$criteria->compare('city',$this->city);
		$criteria->compare('event_date',$this->event_date);
		$criteria->compare('en_title',$this->en_title,true);
		$criteria->compare('cn_title',$this->cn_title,true);
		$criteria->compare('img_banner',$this->img_banner,true);
		$criteria->compare('img_main',$this->img_main,true);
		$criteria->compare('new_poster_en',$this->new_poster_en,true);
		$criteria->compare('new_poster_cn',$this->new_poster_cn,true);
		$criteria->compare('new_bg',$this->new_bg,true);
		$criteria->compare('stat',$this->stat);
		$criteria->compare('apply_duedate',$this->apply_duedate);
		$criteria->compare('userid',$this->userid);
		$criteria->compare('update_timestamp',$this->update_timestamp);
		$criteria->compare('introduction_en',$this->introduction_en);
		$criteria->compare('introduction_cn',$this->introduction_cn);

        $criteria->order='event_date desc';

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}