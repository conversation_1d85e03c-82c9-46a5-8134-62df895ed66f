
name: "Continuous Integration"

on:
  pull_request:
    branches:
      - "*.x"
  push:
    branches:
      - "*.x"

env:
  fail-fast: true
  COMPOSER_ROOT_VERSION: "1.4"

jobs:
  phpunit:
    name: "PHPUnit with SQLite"
    runs-on: "ubuntu-20.04"

    strategy:
      matrix:
        php-version:
          - "7.1"
          - "7.2"
          - "7.3"
          - "7.4"
          - "8.0"

    steps:
      - name: "Checkout"
        uses: "actions/checkout@v2"
        with:
          fetch-depth: 2

      - name: "Install PHP with XDebug"
        uses: "shivammathur/setup-php@v2"
        if: "${{ matrix.php-version == '7.1' }}"
        with:
          php-version: "${{ matrix.php-version }}"
          coverage: "xdebug"
          ini-values: "zend.assertions=1"

      - name: "Install PHP with PCOV"
        uses: "shivammathur/setup-php@v2"
        if: "${{ matrix.php-version != '7.1' }}"
        with:
          php-version: "${{ matrix.php-version }}"
          coverage: "pcov"
          ini-values: "zend.assertions=1"

      - name: "Cache dependencies installed with composer"
        uses: "actions/cache@v2"
        with:
          path: "~/.composer/cache"
          key: "php-${{ matrix.php-version }}-composer-locked-${{ hashFiles('composer.lock') }}"
          restore-keys: "php-${{ matrix.php-version }}-composer-locked-"

      - name: "Install dependencies with composer"
        run: "composer update --no-interaction --no-progress"

      - name: "Run PHPUnit"
        run: "vendor/bin/phpunit --coverage-clover=coverage.xml"

      - name: "Upload coverage file"
        uses: "actions/upload-artifact@v2"
        with:
          name: "phpunit-${{ matrix.php-version }}.coverage"
          path: "coverage.xml"

  upload_coverage:
    name: "Upload coverage to Codecov"
    runs-on: "ubuntu-20.04"
    needs:
      - "phpunit"

    steps:
      - name: "Checkout"
        uses: "actions/checkout@v2"
        with:
          fetch-depth: 2

      - name: "Download coverage files"
        uses: "actions/download-artifact@v2"
        with:
          path: "reports"

      - name: "Upload to Codecov"
        uses: "codecov/codecov-action@v1"
        with:
          directory: reports
