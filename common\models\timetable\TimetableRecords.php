<?php

/**
 * This is the model class for table "ivy_timetable_records".
 *
 * The followings are the available columns in table 'ivy_timetable_records':
 * @property integer $id
 * @property integer $tid
 * @property integer $childid
 * @property integer $class_id
 * @property string  $school_id
 * @property string  $course_code
 * @property integer $class_room
 * @property integer $weekday
 * @property integer $period
 * @property integer $is_admin
 * @property integer $teacher_id
 * @property integer $target_date
 * @property integer $late_time
 * @property integer $type
 * @property string  $memo
 * @property integer $status
 * @property integer $created_at
 * @property integer $created_by
 * @property integer $updated_at
 * @property integer $updated_by
 */
class TimetableRecords extends CActiveRecord
{
    public $typeNum;

    const ATTENDANCE_STATUS = 10;#出勤
    const LATE_STATUS       = 20;#迟到
    const LEAVE_STATUS      = 30;#旷课
    const SICK_LEAVE_STATUS = 31;#病假
    const ABSENTEE_STATUS   = 40;#缺席旷课
    const ABSENTEE_STATUS2  = 41;#校内停学
    const ABSENTEE_STATUS3  = 42;#校外停学
    const DRESS_STATUS      = 0;#着装违规 0否 1是

    /**
     * @return string the associated database table name
     */
    public function tableName()
    {
        return 'ivy_timetable_records';
    }

    /**
     * @return array validation rules for model attributes.
     */
    public function rules()
    {
        // NOTE: you should only define rules for those attributes that
        // will receive user inputs.
        return array(
            array('tid, childid, school_id, course_code, class_room, weekday, period, is_admin, teacher_id, target_date, type, status, created_at, created_by, updated_at, updated_by', 'required'),
            array('tid, childid, class_room, weekday, period, is_admin, teacher_id, target_date, late_time, type, status, created_at, created_by, updated_at, updated_by', 'numerical', 'integerOnly' => true),
            array('school_id, course_code', 'length', 'max' => 255),
            array('memo, dress, typeNum, class_id', 'safe'),
            // The following rule is used by search().
            // @todo Please remove those attributes that should not be searched.
            array('id, tid, childid, school_id, course_code, class_room, weekday, period, is_admin, teacher_id, target_date, late_time, type, memo, status, created_at, created_by, updated_at, updated_by, dress, class_id', 'safe', 'on' => 'search'),
        );
    }

    /**
     * @return array relational rules.
     */
    public function relations()
    {
        // NOTE: you may need to adjust the relation name and the related
        // class name for the relations automatically generated below.
        return array();
    }

    /**
     * @return array customized attribute labels (name=>label)
     */
    public function attributeLabels()
    {
        return array(
            'id'          => 'ID',
            'tid'         => 'Tid',
            'childid'     => 'Childid',
            'class_id'    => 'Class Id',
            'school_id'   => 'School',
            'course_code' => 'Course Code',
            'class_room'  => 'Class Room',
            'weekday'     => 'Weekday',
            'period'      => 'Period',
            'is_admin'    => 'Is Admin',
            'teacher_id'  => 'Teacher',
            'target_date' => 'Target Date',
            'late_time'   => 'Late Time',
            'type'        => 'Type',
            'memo'        => 'Memo',
            'status'      => 'Status',
            'created_at'  => 'Created At',
            'created_by'  => 'Created By',
            'updated_at'  => 'Updated At',
            'updated_by'  => 'Updated By',
            'dress'       => 'Dress',
        );
    }

    /**
     * Retrieves a list of models based on the current search/filter conditions.
     *
     * Typical usecase:
     * - Initialize the model fields with values from filter form.
     * - Execute this method to get CActiveDataProvider instance which will filter
     * models according to data in model fields.
     * - Pass data provider to CGridView, CListView or any similar widget.
     *
     * @return CActiveDataProvider the data provider that can return the models
     * based on the search/filter conditions.
     */
    public function search()
    {
        // @todo Please modify the following code to remove attributes that should not be searched.

        $criteria = new CDbCriteria;

        $criteria->compare('id', $this->id);
        $criteria->compare('tid', $this->tid);
        $criteria->compare('childid', $this->childid);
        $criteria->compare('school_id', $this->school_id, true);
        $criteria->compare('course_code', $this->course_code, true);
        $criteria->compare('class_room', $this->class_room);
        $criteria->compare('weekday', $this->weekday);
        $criteria->compare('period', $this->period);
        $criteria->compare('is_admin', $this->is_admin);
        $criteria->compare('teacher_id', $this->teacher_id);
        $criteria->compare('target_date', $this->target_date);
        $criteria->compare('late_time', $this->late_time);
        $criteria->compare('type', $this->type);
        $criteria->compare('memo', $this->memo, true);
        $criteria->compare('status', $this->status);
        $criteria->compare('created_at', $this->created_at);
        $criteria->compare('created_by', $this->created_by);
        $criteria->compare('updated_at', $this->updated_at);
        $criteria->compare('updated_by', $this->updated_by);
        $criteria->compare('class_id', $this->class_id);

        return new CActiveDataProvider($this, array(
            'criteria' => $criteria,
        ));
    }

    /**
     * @return CDbConnection the database connection used for this class
     */
    public function getDbConnection()
    {
        return Yii::app()->subdb;
    }

    /**
     * Returns the static model of the specified AR class.
     * Please note that you should have this exact method in all your CActiveRecord descendants!
     * @param string $className active record class name.
     * @return TimetableRecords the static model class
     */
    public static function model($className = __CLASS__)
    {
        return parent::model($className);
    }


    public static function updateRecordsAllDay($tid, $semesterId, $schoolid, $targetDate, $type, $childid, $uid, $latetime = '', $memo = '')
    {
        $weekday = date("w", $targetDate);

        $criteria = new CDbCriteria();
        $criteria->compare('target_id', $childid);
        $criteria->compare('type', 10);
        $criteria->compare('tid', $tid);
        $criteria->order = "updated_at DESC";
        $childCaches     = TimetableWeeklyScheduleCache::model()->findAll($criteria);
        if (count($childCaches) == 0) {
            $childCache = TimetableWeeklyScheduleCache::childCache($childid, $tid, $semesterId);
        } else {
            $childCache = $childCaches[0];
            if (isset($childCaches[$semesterId])) {
                $childCache = $childCaches[$semesterId];
            }
        }

        foreach (json_decode($childCache->data) as $key => $val) {
            $coursecodes = explode(',', $val);
            $arr         = explode('-', $key);
            $period      = $arr[1];
            if ($arr[0] == $weekday) {
                foreach ($coursecodes as $course_code) {
                    $criteria = new CDbCriteria;
                    $criteria->compare('course_code', $course_code);
                    $criteria->compare('child_id', $childid);
                    $criteria->compare('tid', $tid);
                    $criteria->index = 'child_id';
                    $childidModel    = TimetableStudentData::model()->findAll($criteria);

                    $courseTeacher = TimetableCourseTeacher::model()->findByAttributes(array('course_id' => current($childidModel)->course_id, 'status' => 1));
                    $teacher_id    = $courseTeacher ? $courseTeacher->teacher_id : Yii::app()->user->id;

                    $childModel = ChildProfileBasic::model()->findByPk($childid);

                    $criteria = new CDbCriteria();
                    $criteria->compare('tid', $tid);
                    $criteria->compare('childid', $childid);
                    $criteria->compare('school_id', $schoolid);
                    $criteria->compare('course_code', $course_code);
                    $criteria->compare('target_date', $targetDate);
                    $criteria->compare('weekday', $weekday);
                    $criteria->compare('period', $period);
                    $criteria->compare('status', '<>' . 99);
                    $model = TimetableRecords::model()->find($criteria);
                    if ($model && $type == 9) {
                        $model->status     = 99;
                        $model->updated_at = time();
                        $model->updated_by = Yii::app()->user->id;
                        $model->save();
                        continue;
                    }
                    if (!$model) {
                        $model             = new TimetableRecords();
                        $model->tid        = $tid;
                        $model->childid    = $childid;
                        $model->school_id  = $schoolid;
                        $model->status     = 10;
                        $model->created_at = time();
                        $model->created_by = $uid;
                    }

                    $model->course_code = $course_code;
                    $model->class_room  = $childidModel[$childid]->class_room;
                    $model->class_id    = $childModel->classid;
                    $model->weekday     = $weekday;
                    $model->period      = $period;
                    $model->is_admin    = 1;
                    $model->teacher_id  = $teacher_id;
                    $model->target_date = $targetDate;
                    $model->late_time   = ($type == TimetableRecords::LATE_STATUS) ? $latetime : "";
                    $model->type        = $type;
                    $model->updated_at  = time();
                    $model->updated_by  = $uid;
                    if (!$model->save()) {
                        $errors = current($model->getErrors());
                        return false;
                    }
                    if ($period == 1) {
                        if (in_array($type, array(10, 20, 30, 31,40, 41, 42))) {
                            $memo = '管理员批量操作';
                            TimetableRecords::childSign($childModel, $targetDate, $type, $latetime, $memo);
                        }
                    }
                }

                TimetableRecords::updateStudentCache($course_code, $weekday, $period, $targetDate, $tid, $model->class_room);
            }
        }
        return true;
    }

    /**
     * @param $course_code  课程code
     * @param $weekday      第几天
     * @param $period       第几节
     * @param $datatime     时间
     * @param $tid          tid
     * @param $class_room   班级ID
     */
    public static function updateStudentCache($course_code, $weekday, $period, $datatime, $tid, $class_room)
    {
        $criteria = new CDbCriteria;
        $criteria->compare('course_code', $course_code);
        $courseModel = TimetableCourses::model()->find($criteria);

        $criteria = new CDbCriteria;
        $criteria->compare('course_code', $course_code);
        $criteria->compare('weekday', $weekday);
        $criteria->compare('period', $period);
        $criteria->compare('target_date', $datatime);
        $criteria->compare('tid', $tid);
        $criteria->compare('status', 10);
        $numberRecords = TimetableRecords::model()->findAll($criteria);

        $data = array(
            TimetableRecords::ATTENDANCE_STATUS => 0,
            TimetableRecords::LATE_STATUS       => 0,
            TimetableRecords::LEAVE_STATUS      => 0,
            TimetableRecords::ABSENTEE_STATUS   => 0,
        );
        if ($numberRecords) {
            foreach ($numberRecords as $val) {
                $data[$val->type] += 1;
            }
        }
        $criteria = new CDbCriteria;
        $criteria->compare('course_id', $courseModel->id);
        $criteria->compare('weekday', $weekday);
        $criteria->compare('period', $period);
        $criteria->compare('target_date', $datatime);
        $criteria->compare('tid', $tid);
        $numberCacheModel = TimetableStudentNumberCache::model()->find($criteria);

        if (!$numberCacheModel) {
            $numberCacheModel              = new TimetableStudentNumberCache();
            $numberCacheModel->tid         = $tid;
            $numberCacheModel->course_id   = $courseModel->id;
            $numberCacheModel->target_date = $datatime;
            $numberCacheModel->weekday     = $weekday;
            $numberCacheModel->period      = $period;
            $numberCacheModel->class_room  = $class_room;
            $numberCacheModel->updated_at  = time();
        }

        $numberCacheModel->data = json_encode($data);
        $numberCacheModel->save();
    }

    public static function childSign($childModel, $time, $type, $latetime = 0, $memo = '无')
    {
        Yii::import('common.models.child.*');
        Yii::import('common.models.attendance.*');

        if ($childModel) {
            if (in_array($type, array(10, 20))) {
                $model                   = new ChildDailySign();
                $model->childid          = $childModel->childid;
                $model->classid          = $childModel->classid;
                $model->schoolid         = $childModel->schoolid;
                $model->sign_timestamp   = $time;
                $model->uid              = Yii::app()->user->id;
                $model->update_timestamp = time();
                $model->save();

                if ($type == 20) {
                    $childmodel                      = new ChildVacation();
                    $childmodel->child_id            = $childModel->childid;
                    $childmodel->school_id           = $childModel->schoolid;
                    $childmodel->class_id            = $childModel->classid;
                    $childmodel->vacation_time_start = $time;
                    $childmodel->vacation_time_end   = $time;
                    $childmodel->type                = 40;
                    $childmodel->vacation_reason     = "";
                    $childmodel->begin_time          = $time + 29700 + (60 * $latetime);
                    $childmodel->vacation_reason     = $memo;
                    $childmodel->uid                 = Yii::app()->user->id;
                    $childmodel->updata_time         = time();
                    $childmodel->stat                = 1;
                    $childmodel->save();
                }
            }
            if (in_array($type, array(11, 30, 31, 40, 41, 42))) {
                //timetableRecords表=>ChildVacation表的对应关系
                $records_to_vacation_map = array(
                    11 => 11,//线上出勤
                    30 => 20,//事假
                    31 => 10,//病假
                    40 => 60,//旷课
                    41 => 60,
                    42 => 60,
                );
//                $memo_map = array(
//                    11=>'线上出勤',
//                    30=>'事假',
//                    31=>'病假',
//                    40=>'旷课',
//                    41=>'校内停学',
//                    42=>'校外停学',
//                );
                $model                      = new ChildVacation();
                $model->child_id            = $childModel->childid;
                $model->school_id           = $childModel->schoolid;
                $model->class_id            = $childModel->classid;
                $model->vacation_time_start = $time;
                $model->vacation_time_end   = $time;
                $model->type                = $records_to_vacation_map[$type];
                $model->vacation_reason     = $memo;
                $model->uid                 = Yii::app()->user->id;
                $model->updata_time         = time();
                $model->stat                = 1;
                $model->save();
            }
            return true;
        }
        return false;
    }


    public static function childViolation($childModel, $time, $type){
        //50 着装违规
        $map                = array(
            50 => 10,
        );
        $model              = new ChildViolation();
        $model->child_id    = $childModel->childid;
        $model->school_id   = $childModel->schoolid;
        $model->class_id    = $childModel->classid;
        $model->type        = $map[$type];
        $model->uid         = Yii::app()->user->id;
        $model->updata_time = $time;
        $model->save();
    }

    public static function childSignDel($childid,$schoolid, $time,$uid)
    {
        Yii::import('common.models.child.*');
        Yii::import('common.models.attendance.*');
        if ($childid) {
            $crit = new CDbCriteria();
            $crit->compare('childid', $childid);
            $crit->compare('schoolid', $schoolid);
            $crit->compare('sign_timestamp', $time);
            $model = ChildDailySign::model()->findAll($crit);
            if ($model) {
                foreach ($model as $val) {
                    $val->uid = $uid;
                    $val->delete();
                }
            }
            $criteria = new CDbCriteria();
            $criteria->compare('child_id', $childid);
            $criteria->compare('school_id', $schoolid);
            $criteria->compare('vacation_time_start', "<={$time}");
            $criteria->compare('vacation_time_end', ">={$time}");
            $criteria->compare('t.stat', 1);
            $vacationModel = ChildVacation::model()->findAll($criteria);
            if ($vacationModel) {
                foreach ($vacationModel as $val1) {
                    $val1->uid = $uid;
                    $val1->delete();
                }
            }
            $criteria2 = new CDbCriteria();
            $criteria2->compare('child_id', $childid);
            $criteria2->compare('updata_time', "<={$time}");
            $criteria2->compare('updata_time', ">={$time}");
            $violationModel = ChildViolation::model()->findAll($criteria2);
            if ($violationModel) {
                foreach ($violationModel as $val) {
                    $val->delete();
                }
            }
        }
    }

}
