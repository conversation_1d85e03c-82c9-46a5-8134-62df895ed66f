<?php

/* 
 * 临时获取孩子列表
 */
class ChildsController extends Controller{
//    public function __construct($id, $module = null) {
//        parent::__construct($id, $module);
//        $token = Yii::app()->request->getParam('token','');
//        if ($token != md5(CommonUtils::SECURITY_KEY)){
//            $this->module->_sendResponse(403);
//        }
//    }
//
//    public function actionIndex(){
//        $schoolId = Yii::app()->request->getParam('schoolid',null);
//        if (empty($schoolId)){
//            $result = array(
//                'Message'=>'Sorry,参数错误',
//                'Status'=>'Error'
//            );
//            $this->module->_sendResponse(200,  CJSON::encode($result));
//        }
//        $profileBasic = ChildProfileBasic::model()->findAll('schoolid=:schoolid and status=:status',array(':status'=>ChildProfileBasic::STATS_ACTIVE,':schoolid'=>$schoolId));
//        if (!empty($profileBasic)){
//            $childList = array();
//            $gender = OA::getChildGenderList();
//            foreach ($profileBasic as $child){
//                $childList["channels"][] =  array(
//                    "id"=>$child->childid,
//                    "classid"=>$child->classid,
//                    "name"=>$child->getChildName(),
//                    "image"=> CommonUtils::childPhotoUrl($child->photo),
//                    'gender'=>$gender[$child->gender],
//                    'birthday'=>$child->birthday_search
//                );
//            }
//            $this->module->_sendResponse(200,  CJSON::encode($childList));
//        }
//    }
//
//    /**
//     * 签到信息保存
//     */
//    public function actionSaveSignInfo(){
//        Yii::import('common.models.child.ChildDailySign');
//        $OAUploadBasePath = Yii::app()->params['OAUploadBasePath'];
//        $childid = Yii::app()->request->getParam('childid',0);
//        $target_dir = $OAUploadBasePath."/sign".$childid;
//        if(!file_exists($target_dir))
//        {
//            mkdir($target_dir, 0777, true);
//        }
//        $byte = file_get_contents("php://input");
//        $time = time();
//        $imgName = $childid."_".date('Y-m-d',$time).".jpg";
//        $file_dir = $target_dir."/".$imgName;
//        if ($fp = fopen($file_dir, 'w')){
//            if (fwrite($fp, $byte)){
//                fclose($fp);
//            }
//        }
//        $timeFormat = date('Y-m-d',$time);
//        $model = ChildDailySign::model()->find('childid=:childid and signdate=:signdate',array(':childid'=>$childid,':signdate'=>$timeFormat));
//        if (empty($model)){
//            $model = new ChildDailySign();
//        }
//        $model->childid = $childid;
//        $model->schoolid = "BJ_OE";
//        $model->signdatetemp = $time;
//        $model->signdate = $timeFormat;
//        $model->img = $imgName;
//        if ($model->save()){
//            $result = array(
//                'Message'=>'The file '.basename($_FILES["file"]["name"]).' has been uploaded.',
//                'Status'=>'OK'
//            );
//        }else{
//            $result = array(
//                'Message'=>'Sorry, there was an error uploading your file.',
//                'Status'=>'Error'
//            );
//        }
//        echo json_encode($result);
//    }
//
//    /**
//     * 查询某学校当前校历下的班级
//     */
//    public function actionGetClassList(){
//        $schoolId = Yii::app()->request->getParam('schoolid',null);
//        if (empty($schoolId)){
//            $result = array(
//                'Message'=>'Sorry,参数错误',
//                'Status'=>'Error'
//            );
//            $this->module->_sendResponse(200,  CJSON::encode($result));
//        }
//        Yii::import('common.models.calendar.CalendarSchool');
//        $objects = IvyClass::model()->findAll('schoolid=:schoolid and stat=:status',array(':schoolid'=>$schoolId,':status'=>IvyClass::STATS_OPEN));
//        $classList = array();
//        if (!empty($objects)){
//            $classList['channels'][] = array('id'=>0,'title'=>'全部孩子');
//            foreach ($objects as $class){
//                $classList['channels'][] = array('id'=>(Int)$class->classid,'title'=>$class->title);
//            }
//        }
//        $this->module->_sendResponse(200,  CJSON::encode($classList));
//    }
}

