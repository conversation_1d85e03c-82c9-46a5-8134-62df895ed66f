<div class="modal-header">
        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
        <h4 class="modal-title" id="myModalLabel">主要活动及建议</h4>
      </div>
		<?php $form=$this->beginWidget('CActiveForm', array(
				'id'=>'actiivity',
				'enableAjaxValidation'=>false,
				'htmlOptions'=>array('class'=>'J_ajaxForm form-horizontal', 'role'=>'form'),
		)); ?>
		<div class="modal-body">
			<div class="form-group">
				<label for="inputEmail3" class="col-sm-2 control-label"><?php echo $form->labelEx($model,'accontent'); ?></label>
				<div class="col-sm-5">
					<?php echo $form->textArea($model,'accontent',array('maxlength'=>255,'class'=>'form-control')); ?>
					<?php echo CHtml::hiddenField('weeknumber',$weeknumber);; ?>
					<?php echo CHtml::hiddenField('found',$found);; ?>
				</div>
			</div>
			<div class="form-group">
				<label for="inputEmail3" class="col-sm-2 control-label"><?php echo $form->labelEx($model,'comments'); ?></label>
				<div class="col-sm-5">
					<?php echo $form->textArea($model,'comments',array('maxlength'=>255,'class'=>'form-control')); ?>
				</div>
			</div>
		</div>
		<div class="modal-footer">
			<button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global','Submit');?></button>
			<button type="button" class="btn btn-default"  data-dismiss="modal"><?php echo Yii::t('global','Cancel');?></button>
		</div>
<?php $this->endWidget(); ?>


