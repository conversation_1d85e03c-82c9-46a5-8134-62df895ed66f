<?php
$type = ChildVacation::getConfig();
unset($type[ChildVacation::VACATION_SICK_LEAVE]);
unset($type[ChildVacation::VACATION_AFFAIR_LEAVE]);

$data = ChildVacation::getMonth();
?>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('//mcampus/default/index')) ?></li>
        <li><?php echo CHtml::link(Yii::t('site', 'Basic'), array('//mcampus/default/index')) ?></li>
        <?php if(Yii::app()->params['siteFlag'] == 'daystar'){?>
            <li><?php echo CHtml::link(Yii::t('site', 'Student Attendance Management'), array('//mcampus/childsign/index2')) ?></li>
        <?php }else{?>
            <li><?php echo CHtml::link(Yii::t('site', 'Student Attendance Management'), array('//mcampus/childsign/index')) ?></li>
        <?php }?>
        <li class="active"><?php echo Yii::t('campus', 'List of students on leave/tardy'); ?></li>
    </ol>

    <div class="row">
        <!-- 左侧菜单 -->
        <?php
        $this->renderPartial('/childsign/left');
        ?>

        <div class="col-md-10 col-sm-12">
            <div class="mb10">
                <div class="btn-group">
                    <button class="btn btn-primary" onclick="lateAdd(0)">
                        <?php echo Yii::t('campus', 'Add late record'); ?>
                    </button>
                    <?php if($childParents):?>
                        <button type="button" class="btn btn-primary dropdown-toggle" data-toggle="dropdown">
                            <span class="caret"></span>
                            <span class="sr-only">Toggle Dropdown</span>
                        </button>
                        <ul class="dropdown-menu" role="menu">
                            <?php foreach($childParents as $cid => $child):?>
                                <li>
                                    <a href="javascript:;" id="add_<?php echo $cid;?>" onclick="lateAdd(<?php echo $cid;?>)">
                                        <?php echo $child['name'];?> F: <?php echo $phoneNumbers[$child['fid']]?> M: <?php echo $phoneNumbers[$child['mid']]?>
                                    </a>
                                </li>
                            <?php endforeach;?>
                        </ul>
                    <?php endif;?>
                </div>

                <div class="btn-group">
                    <button class="btn btn-primary" onclick="leaveAdd(0)">
                        <?php echo Yii::t('campus', 'Add early release'); ?>
                    </button>
                    <?php if($childParents):?>
                        <button type="button" class="btn btn-primary dropdown-toggle" data-toggle="dropdown">
                            <span class="caret"></span>
                            <span class="sr-only">Toggle Dropdown</span>
                        </button>
                        <ul class="dropdown-menu" role="menu">
                            <?php foreach($childParents as $cid => $child):?>
                                <li>
                                    <a href="javascript:;" id="add_<?php echo $cid;?>" onclick="leaveAdd(<?php echo $cid;?>)">
                                        <?php echo $child['name'];?> F: <?php echo $phoneNumbers[$child['fid']]?> M: <?php echo $phoneNumbers[$child['mid']]?>
                                    </a>
                                </li>
                            <?php endforeach;?>
                        </ul>
                    <?php endif;?>
                </div>
                <!--<a class="J_modal btn btn-primary" href="#" data-toggle="modal" data-target="#model"><span class="glyphicon glyphicon-plus"></span> 新建记录</a>-->
            </div>
            <div class="mb10 row">
                <!-- 搜索框 -->
                <form action="<?php echo $this->createUrl('comeLate'); ?>" method="get">
                    <?php echo Chtml::hiddenField('branchId', $this->branchId); ?>
                    <div class="col-md-8 row">
                        <div class="form-group col-sm-3">
                            <?php echo Chtml::dropDownList('type', $_GET['type'], $type, array('class' => 'form-control' , 'empty' => Yii::t('global','Please Select'))); ?>
                        </div>
                        <div class="form-group col-sm-3">
                            <?php
                            $this->widget('zii.widgets.jui.CJuiDatePicker', array(
                                "name"=>"vacation_day",
                                "value" => Yii::app()->request->getParam('vacation_day','') ? Yii::app()->request->getParam('vacation_day','') :'' ,
                                "options"=>array(
                                    'changeMonth'=>true,
                                    'changeYear'=>true,
                                    'dateFormat'=>'yy-mm-dd',
                                ),
                                'htmlOptions' => array(
                                    'class' => 'form-control',
                                        'placeholder' => Yii::t('campus','Please select the dates'),
                                ),
                            ));
                            ?>
                        </div>
                        <!-- 孩子 -->
                        <div class="form-group col-sm-3">
                         <?php echo CHtml::textField('search', $_GET['search'], array('class' => 'form-control' , 'placeholder' => Yii::t('campus','Search'))); ?>
                        </div>
                        <div class="form-group col-sm-1">
                                <button class="btn btn-default" type="submit"><span class="glyphicon glyphicon-search"></span></button>
                        </div>
                        <?php if(Yii::app()->request->getParam('vacation_day','')){ ?>
                            <a class="btn btn-info" onclick="exporyVacation()"><?php echo Yii::t('user','Export')?></a>
                        <?php } ?>
                    </div>

                </form>
            </div>
            <div class="panel panel-default">
                <div class="panel-body">

                    <?php
                    $this->widget('ext.ivyCGridView.BsCGridView', array(
                        'id'=>'child_vacation',
                        'dataProvider'=>$dataProvider,
                        'afterAjaxUpdate'=>'js:function(){head.Util.ajaxDel()}',
                        'colgroups'=>array(
                            array(
                                "colwidth" => array(100, 150, 50, 100, 100, 100, 100, 100, 100, 100),
                            )
                        ),
                        'columns' => array(
                            array(
                                'name' => Yii::t('ivyer', 'child_id'),
                                'value' => '$data->childProfile->getChildName()',
                            ),
                            array(
                                'name' => Yii::t('ivyer', 'class_id'),
                                'value' => 'Yii::app()->controller->class_list[$data->class_id]',
                                //'value' => array($this, 'getClassid'),
                            ),
                            array(
                                'name' => Yii::t('ivyer', 'type'),
                                'value' => array($this, 'status'),
                            ),
                            array(
                                'name' => Yii::t('ivyer', 'vacation_time_start'),
                                'value' => 'date("Y-m-d", $data->vacation_time_start)',
                            ),
                            array(
                                'name' => Yii::t('campus', 'Expected Time'),
                                'value' => '($data->est_begin_time) ? date("Y-m-d H:i:s", $data->est_begin_time) : ""',
                            ),
                            array(
                                'name' => Yii::t('campus', 'Actual Time'),
                                'value' => '($data->begin_time) ? date("Y-m-d H:i:s", $data->begin_time) : ""',
                            ),
                            array(
                                'name' => Yii::t('labels','Memo'),
                                'value' => '$data->vacation_reason',
                            ),
                            array(
                                'name' => Yii::t('ivyer', 'uid'),
                                'value' => '$data->user->getName()',
                            ),
                            array(
                                'name' => Yii::t('ivyer', 'updata_time'),
                                'value' => 'date("Y-m-d H:i:s", $data->updata_time)',
                            ),
                            array(
                                'name' => Yii::t('global', 'Action'),
                                'type' => 'raw',
                                'value' => array($this, 'getButton'),
                            ),
                        ),
                    ));
                    ?>
                </div>
            </div>
        </div>
    </div>
</div>


<div class="modal fade" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                        aria-hidden="true">×</span></button>
                <h4 class="modal-title"><?php echo Yii::t('campus', 'Add leave record'); ?></h4>
            </div>
            <?php

            $model = new ChildVacation();
            if ($model->vacation_time_end == 0) {
                $model->vacation_time_end = "";
            }

            if ($model->vacation_time_start == 0) {
                $model->vacation_time_start = "";
            }
            $form = $this->beginWidget('CActiveForm', array(
                'id' => 'visits-form',
                'enableAjaxValidation' => false,
                'action' => $this->createUrl('vacationadd'),
                'htmlOptions' => array('class' => 'J_ajaxForm form-horizontal', 'role' => 'form'),
            ));
            ?>
            <div class="modal-body">
                <div class="form-group">
                    <label class="col-xs-3 control-label"><?php echo Yii::t('campus','Students on leave/tardy') ?></label>

                    <div class="col-xs-9">
                        <?php
                        $childType = ChildVacation::getConfig();
                        unset($childType[40]);
                        unset($childType[50]);
                        $this->widget('ext.search.ChildSearchBox', array(
                            'acInputCSS' => 'form-control',
                            'acName' => 'searchChild',
                            'allowMultiple' => false,
                            'allowMultipleSchool' => false,
                            'simpleDisplay' => false,
                            'extendCss' => false,
                            'useModel' => true,
                            //'allowMultiple' => 8,
                            'model' => $model,
                            'attribute' => 'child_id',
                            'htmlOptions' => array('class' => 'form-control')
                        )) ?>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-xs-3 control-label"><?php echo Yii::t('campus','Type of Leave/tardy') ?></label>

                    <div class="col-xs-9">
                        <?php echo $form->dropDownList($model, 'type',  $childType, array('class' => 'form-control', 'empty' => Yii::t('global','Please Select'))); ?>
                        <?php echo CHtml::hiddenField('type_id', ''); ?>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-xs-3 control-label"><?php echo Yii::t('campus','Start Date') ?></label>
                    <div class="col-xs-9">
                        <?php
                        $this->widget('zii.widgets.jui.CJuiDatePicker', array(
                            "model" => $model,
                            "attribute" => "vacation_time_start",
                            "options" => array(
                                'changeMonth' => true,
                                'changeYear' => true,
                                'dateFormat' => 'yy-mm-dd',
                            ),
                            'htmlOptions' => array(
                                'class' => 'form-control'
                            ),
                        ));
                        ?>
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-xs-3 control-label"><?php echo Yii::t('campus','End Date') ?></label>
                    <div class="col-xs-9">
                        <?php
                        $this->widget('zii.widgets.jui.CJuiDatePicker', array(
                            "model" => $model,
                            "attribute" => "vacation_time_end",
                            "options" => array(
                                'changeMonth' => true,
                                'changeYear' => true,
                                'dateFormat' => 'yy-mm-dd',
                            ),
                            'htmlOptions' => array(
                                'class' => 'form-control'
                            ),
                        ));
                        ?>
                    </div>
                </div>
                <!-- 备注 -->
                <div class="form-group">
                    <label class="col-xs-3 control-label"><?php echo Yii::t('labels','Memo') ?></label>
                    <div class="col-xs-9">
                        <?php echo $form->textArea($model, 'vacation_reason', array('class' => 'form-control')); ?>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="submit"
                        class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global', 'Submit'); ?></button>
                <button type="button" class="btn btn-default"
                        data-dismiss="modal"><?php echo Yii::t('global', 'Cancel'); ?></button>
            </div>
            <?php $this->endWidget(); ?>
        </div>
    </div>
</div>


<div class="modal fade" id="myModal_late" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                        aria-hidden="true">×</span></button>
                <h4 class="modal-title"><?php echo Yii::t('campus', 'Add late record'); ?></h4>
            </div>
            <?php

            $model = new ChildVacation();
            if ($model->vacation_time_end == 0) {
                $model->vacation_time_end = "";
            }

            if ($model->vacation_time_start == 0) {
                $model->vacation_time_start = "";
            }
            $form = $this->beginWidget('CActiveForm', array(
                'id' => 'visits-form',
                'enableAjaxValidation' => false,
                'action' => $this->createUrl('vacationadd'),
                'htmlOptions' => array('class' => 'J_ajaxForm form-horizontal', 'role' => 'form'),
            ));
            ?>
            <div class="modal-body">
                <div class="form-group">
                    <label class="col-xs-3 control-label"><?php echo Yii::t('campus','Students on leave/tardy') ?></label>

                    <div class="col-xs-9">
                        <?php
                        $this->widget('ext.search.ChildSearchBox', array(
                            'acInputCSS' => 'form-control',
                            'acName' => 'searchChild_late',
                            'allowMultiple' => false,
                            'allowMultipleSchool' => false,
                            'simpleDisplay' => false,
                            'extendCss' => false,
                            'useModel' => true,
                            //'allowMultiple' => 8,
                            'model' => $model,
                            'attribute' => 'child_id_late',
                            'htmlOptions' => array('class' => 'form-control')
                        )) ?>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-xs-3 control-label"><?php echo  Yii::t('campus','Chose Date') ?></label>
                    <div class="col-xs-9">
                        <?php
                        $this->widget('zii.widgets.jui.CJuiDatePicker', array(
                            "model" => $model,
                            "attribute" => "vacation_time_start_late",
                            "options" => array(
                                'changeMonth' => true,
                                'changeYear' => true,
                                'dateFormat' => 'yy-mm-dd',
                            ),
                            'htmlOptions' => array(
                                'class' => 'form-control'
                            ),
                        ));
                        ?>
                    </div>
                </div>
                <!-- 备注 -->
                <div class="form-group">
                    <label class="col-xs-3 control-label"><?php echo Yii::t('campus', 'Expected Time'); ?> <span class="required">*</span></label>
                    <div class="col-xs-3">
                        <?php echo CHtml::dropDownList('timeEst', "", $data['hour'], array('class' => "form-control mr10", 'empty' => Yii::t('campus', 'hour')));?>
                    </div>
                    <label class="col-xs-2 control-label"></label>
                    <div class="col-xs-3">
                        <?php echo CHtml::dropDownList('minuteEst', "", $data['minute'], array('class' => "form-control mr10", 'empty' => Yii::t('campus', 'minute')));?>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-xs-3 control-label"><?php echo Yii::t('campus', 'Actual Time'); ?> <span class="required">*</span></label>
                    <div class="col-xs-3">
                        <?php echo CHtml::dropDownList('time', "", $data['hour'], array('class' => "form-control mr10", 'empty' => Yii::t('campus', 'hour')));?>
                    </div>
                    <label class="col-xs-2 control-label"></label>
                    <div class="col-xs-3">
                        <?php echo CHtml::dropDownList('minute', "", $data['minute'], array('class' => "form-control mr10", 'empty' => Yii::t('campus', 'minute')));?>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-xs-3 control-label"><?php echo Yii::t('labels','Memo') ?></label>
                    <div class="col-xs-9">
                        <?php echo $form->textArea($model, 'vacation_reason_late', array('class' => 'form-control')); ?>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="submit"
                        class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global', 'Submit'); ?></button>
                <button type="button" class="btn btn-default"
                        data-dismiss="modal"><?php echo Yii::t('global', 'Cancel'); ?></button>
            </div>
            <?php $this->endWidget(); ?>
        </div>
    </div>
</div>


<div class="modal fade" id="myModal_late2" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">×</span></button>
                <h4 class="modal-title"><?php echo Yii::t('campus', 'Add early release'); ?></h4>
            </div>
            <?php

            $model = new ChildVacation();
            if ($model->vacation_time_end == 0) {
                $model->vacation_time_end = "";
            }

            if ($model->vacation_time_start == 0) {
                $model->vacation_time_start = "";
            }
            $form = $this->beginWidget('CActiveForm', array(
                'id' => 'visits-form',
                'enableAjaxValidation' => false,
                'action' => $this->createUrl('vacationadd'),
                'htmlOptions' => array('class' => 'J_ajaxForm form-horizontal', 'role' => 'form'),
            ));
            ?>
            <div class="modal-body">
                <div class="form-group">
                    <label class="col-xs-3 control-label"><?php echo Yii::t('campus','Students on leave/tardy') ?></label>

                    <div class="col-xs-9">
                        <?php
                        $this->widget('ext.search.ChildSearchBox', array(
                            'acInputCSS' => 'form-control',
                            'acName' => 'searchChild_leave',
                            'allowMultiple' => false,
                            'allowMultipleSchool' => false,
                            'simpleDisplay' => false,
                            'extendCss' => false,
                            'useModel' => true,
                            //'allowMultiple' => 8,
                            'model' => $model,
                            'attribute' => 'child_id_leave',
                            'htmlOptions' => array('class' => 'form-control')
                        ))

                        ?>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-xs-3 control-label"><?php echo  Yii::t('campus','Chose Date') ?></label>
                    <div class="col-xs-9">
                        <?php
                        $this->widget('zii.widgets.jui.CJuiDatePicker', array(
                            "model" => $model,
                            "attribute" => "vacation_time_start_leave",
                            "options" => array(
                                'changeMonth' => true,
                                'changeYear' => true,
                                'dateFormat' => 'yy-mm-dd',
                            ),
                            'htmlOptions' => array(
                                'class' => 'form-control'
                            ),
                        ));
                        ?>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-xs-3 control-label"><?php echo Yii::t('campus', 'Expected Time'); ?> <span class="required">*</span></label>
                    <div class="col-xs-3">
                        <?php echo CHtml::dropDownList('timeEst', "", $data['hour'], array('class' => "form-control mr10", 'empty' => Yii::t('campus', 'hour')));?>
                    </div>
                    <label class="col-xs-2 control-label"></label>
                    <div class="col-xs-3">
                        <?php echo CHtml::dropDownList('minuteEst', "", $data['minute'], array('class' => "form-control mr10", 'empty' => Yii::t('campus', 'minute')));?>
                        <?php echo CHtml::hiddenField('typeId', ''); ?>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-xs-3 control-label"><?php echo Yii::t('campus', 'Actual Time'); ?> <span class="required">*</span></label>
                    <div class="col-xs-3">
                        <?php echo CHtml::dropDownList('time', "", $data['hour'], array('class' => "form-control mr10", 'empty' => Yii::t('campus', 'hour')));?>
                    </div>
                    <label class="col-xs-2 control-label"></label>
                    <div class="col-xs-3">
                        <?php echo CHtml::dropDownList('minute', "", $data['minute'], array('class' => "form-control mr10", 'empty' => Yii::t('campus', 'minute')));?>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-xs-3 control-label"><?php echo Yii::t('labels','Memo') ?></label>
                    <div class="col-xs-9">
                        <?php echo $form->textArea($model, 'vacation_reason_late', array('class' => 'form-control')); ?>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="submit"
                        class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global', 'Submit'); ?></button>
                <button type="button" class="btn btn-default"
                        data-dismiss="modal"><?php echo Yii::t('global', 'Cancel'); ?></button>
            </div>
            <?php $this->endWidget(); ?>
        </div>
    </div>
</div>

<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>
<script>
    // 模态框
    /*var modal = '<div class="modal fade" id="modal" tabindex="-1" role="dialog" aria-labelledby="modal"><div class="modal-dialog" role="document"><div class="modal-content"></div></div></div>';
     $('body').append(modal);*/
    //$('#visit-time').datepicker({changeMonth: true,changeYear: true,'dateFormat':'yy-mm-dd'});


    function vacationstaff(id) {
        $.ajax({
            url: '<?php echo $this->createUrl('vacation_audit');?>',
            data: {id: id},
            type: 'post',
            dataType: 'json',
            success: function (data) {
                if (data.state == 'success') {
                    location = location;
                    //$.fn.yiiGridView.update('child_vacation');
                }
                ;
            }
        });
    }

    function vacationEdit(id) {
        $.ajax({
            url: '<?php echo $this->createUrl('vacation_edit');?>',
            data: {id: id},
            type: 'post',
            dataType: 'json',
            success: function (data) {
                emptyContent();
                $('#myModal_late').modal({backdrop: 'static', keyboard: false});
                /*$('#ChildVacation_vacation_time_start').val(data.vacation_time_start);
                $('#ChildVacation_vacation_time_end').val(data.vacation_time_end);
                $('#ChildVacation_vacation_reason').val(data.vacation_reason);
                $("#ChildVacation_type option[value='"+data.type+"']").attr("selected",true);
                $("#ChildVacation_id").val(data.id);*/
                $('#ChildVacation_vacation_time_start_late').val('<?php echo date("Y-m-d", time()); ?>');
                $('<option id = "asjkdjhajk"></option>').val(data.child_id).text(data.name).appendTo( $("#ChildVacation_child_id_late") );
            }
        });
    }

    function vacationAdd(childid) {
        emptyContent();
        if(childid){
            var childParents = <?php echo CJSON::encode($childParents);?>;
            $('<option></option>').val(childid).text(childParents[childid]['name']).appendTo( $('#ChildVacation_child_id') );
        }
        $('#type_id').val("1");
        $('#myModal').modal({backdrop: 'static'});
    }

    function lateAdd(childid) {
        emptyContent();
        if(childid){
            var childParents = <?php echo CJSON::encode($childParents);?>;
            $('<option></option>').val(childid).text(childParents[childid]['name']).appendTo( $('#ChildVacation_child_id_late') );
        }
        $('#ChildVacation_vacation_time_start_late').val('<?php echo date("Y-m-d", time()); ?>');
        $('#myModal_late').modal({backdrop: 'static'});
    }

    function leaveAdd(childid) {
        emptyContent();
        if(childid){
            var childParents = <?php echo CJSON::encode($childParents);?>;
            $('<option></option>').val(childid).text(childParents[childid]['name']).appendTo( $('#ChildVacation_child_id_leave') );
        }
        $('#typeId').val("2");
        $('#ChildVacation_vacation_time_start_leave').val('<?php echo date("Y-m-d", time()); ?>');
        $('#myModal_late2').modal({backdrop: 'static'});
    }

    function emptyContent(){
        $('#ChildVacation_child_id').empty();
        $('#ChildVacation_child_id_late').empty();
        $('#ChildVacation_vacation_reason_late').val("");
        $('#J_fail_info').empty();
        $('#myModal input').attr("value", "");
        $('#myModal select').attr("value", "");
        $('#myModal textarea').attr("value", "");
        $('#myModal_late input').attr("value", "");
        $('#myModal_late select').attr("value", "");
        $('#myModal_late textarea').attr("value", "");
        $('#myModal_late2 input').attr("value", "");
        $('#myModal_late2 select').attr("value", "");
        $('#myModal_late2 textarea').attr("value", "");
    }

    // 回调：添加成功
    function cbVacation(data) {
        if(data.late == 40){
            $('#myModal_late').modal('hide');
        }else if(data.late == 50){
            $('#myModal_late2').modal('hide');
        }else{
            $('#myModal').modal('hide');
            var addDom = $('#add_children #add_'+data.childid);
            if( data.inToday == 1 && addDom.length > 0 ){
                addDom.remove();
            }
        }
        $.fn.yiiGridView.update('child_vacation');
    }

    function cbVacation_detele() {
        $.fn.yiiGridView.update('child_vacation');
    }
    function exporyVacation() {
        var vacation_day = '<?php echo Yii::app()->request->getParam('vacation_day','') ?>';
        var type = '<?php echo $_GET['type'] ? $_GET['type'] : 2; ?>';
        var search = '<?php echo Yii::app()->request->getParam('search','') ?>';
        var url = '<?php echo $this->createUrl('exporyVacation') ?>';

        $.ajax({
            url: url,
            type: 'POST',
            data: {vacation_day:vacation_day,type:type,search:search},
            success: function (res) {
                if (res.state == 'success') {
                    var data = res.data.items;
                    const filename = res.data.title;
                    const ws_name = "SheetJS";

                    const worksheet = XLSX.utils.aoa_to_sheet(data);
                    const workbook = XLSX.utils.book_new();
                    XLSX.utils.book_append_sheet(workbook, worksheet, ws_name);
                    // generate Blob
                    const wbout = XLSX.write(workbook, {bookType: 'xlsx', type: 'array'});
                    const blob = new Blob([wbout], {type: 'application/octet-stream'});
                    // save file
                    let link = document.createElement('a');
                    link.href = URL.createObjectURL(blob);
                    link.download = filename;
                    link.click();
                    setTimeout(function() {
                        // 延时释放掉obj
                        URL.revokeObjectURL(link.href);
                        link.remove();
                    }, 500);
                }
            },
            dataType: 'json'
        });
    }
</script>
