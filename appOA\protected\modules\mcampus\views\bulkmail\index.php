<!-- 加载底部导航栏 -->
<?php
$this->branchSelectParams['extraUrlArray'] = array('//mcampus/bulkmail/index');
$this->renderPartial('//layouts/common/branchSelectBottom');
?>
<div class="container-fluid">
    <!-- 面包屑导航 -->
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('//mcampus/default/index')) ?></li>
        <li><?php echo CHtml::link(Yii::t('site', 'Routines'), array('//mcampus/default/index')) ?></li>
        <li class="active"><?php echo Yii::t('site', 'Bulk Mail'); ?></li>
    </ol>
    <div class="col-md-8 col-xs-12">
        <div class="col-md-offset-1 col-sm-11 page-header" style="margin-top: 0">
            <h2 class="">
                <?php echo Yii::t('site', 'Bulk Mail'); ?>
            </h2>
        </div>
        <form method="post" action="" id="bulkmail" class="form-horizontal">
            <!-- 发件人帐号-->
            <div class="form-group">
                <div class="col-sm-2 text-right"><label for="" class="control-label"><?php echo Yii::t('site', 'E-mail user name:'); ?></label></div>
                <div class="col-sm-10"><input name="bulkmail[user]" type="email" class="form-control"
                                              placeholder="Email" value="<?php echo $this->staff->email; ?>">
                </div>
            </div>
            <!-- 发件人密码 -->
            <div class="form-group">
                <div class="col-sm-2 text-right"><label for="" class="control-label"><?php echo Yii::t('site', 'E-mail password:'); ?></label></div>
                <div class="col-sm-10"><input name="bulkmail[password]" type="password" class="form-control"
                                              placeholder="password"></div>
            </div>
            <!-- 发件人姓名-->
            <div class="form-group">
                <div class="col-sm-2 text-right"><label for="" class="control-label"><?php echo Yii::t('site', 'Display Name:'); ?></label></div>
                <div class="col-sm-10"><input name="bulkmail[fromname]" type="email" class="form-control"
                                              placeholder="fromname"
                                              value="<?php echo $this->staff->getName(); ?>"></div>
            </div>
            <div class="form-group">
                <div class="col-sm-2">
                </div>
                <div class="col-sm-10">
                    <button type="button" class="btn btn-link btn-sm" data-toggle="collapse"
                            data-target="#bulkmailCollapse">
                        <?php echo Yii::t('ivyer', 'Advanced'); ?>
                    </button>
                </div>
            </div>
            <div id="bulkmailCollapse" class="collapse">

                <!-- SMTP地址 -->
                <div class="form-group">
                    <div class="col-sm-2 text-right"><label for="" class="control-label"><?php echo Yii::t('site', 'SMTP:'); ?></label></div>
                    <div class="col-sm-4">
                        <div class="input-group">
                            <input type="text" class="form-control" id="bulkmailSmtp" name="bulkmail[smtp]"
                                   value="smtp.office365.com">

                            <div class="input-group-btn">
                                <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown"
                                        aria-haspopup="true" aria-expanded="false"><?php echo Yii::t('global', 'Please Select'); ?> <span class="caret"></span>
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a href="javascript:;" class="smtp" _value="smtp.office365.com">smtp.office365.com</a>
                                    <li><a href="javascript:;" class="smtp" _value="smtp.exmail.qq.com">smtp.exmail.qq.com</a>
                                    </li>
                                    <li><a href="javascript:;" class="smtp"
                                           _value="smtp.ivygroup.org">smtp.ivygroup.org</a></li>
                                    <li><a href="javascript:;" class="smtp" _value="smtp.qq.com">smtp.qq.com</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- SMTP端口 -->
                <div class="form-group">
                    <div class="col-sm-2 text-right"><label for="" class="control-label"><?php echo Yii::t('site', 'SMTP PORT:'); ?></label></div>
                    <div class="col-sm-10"><input name="bulkmail[port]" type="text" class="form-control" value="25">
                    </div>
                </div>
            </div>
            <!-- 测试帐号 -->
            <div class="form-group">
                <div class="col-sm-2 text-right"><label for="" class="control-label"><?php echo Yii::t('site', 'Connect:'); ?></label></div>
                <div class="col-sm-10">
                    <button class="btn btn-default" type="button" onclick="testSend(this)"><?php echo Yii::t('site', 'Clict to connect'); ?></button>
                    <span class="mailInfo"></span>
                </div>
            </div>
            <!-- 收件人地址 -->
            <div class="form-group">
                <div class="col-sm-2 text-right">
                    <label for="" class="control-label"><?php echo Yii::t('site', 'To:'); ?></label>
                </div>
                <div class="col-sm-10">
                            <textarea name="bulkmail[to]" id="to" rows="8" class="form-control"
                                      placeholder="<?php echo Yii::t('site', 'seperate by ; or ,'); ?>"></textarea>
                </div>
            </div>
            <!-- 选择收件人 -->
            <div class="form-group">
                <div class="col-sm-2 text-right">
                    <label for="" class="control-label"><?php echo Yii::t('site', 'Selecte recipients:'); ?></label>
                </div>
                <div class="col-sm-4">
                    <div class="input-group">
                    <select class="form-control selectpicker" multiple name="" id="classselect" title="<?php echo Yii::t('site', 'Classes of current year: '); ?>">
                            <?php foreach ($class as $value) { ?>
                                <option
                                    value="<?php echo $value->classid; ?>"><?php echo $value->title; ?></option>
                            <?php } ?>                        
                    </select>
                        <div class="input-group-btn" onclick="showChild('current')" >
                            <button type="button" class="btn btn-default" data-toggle="modal"
                                    data-target="#myModal"><?php echo Yii::t('site', 'Select recipients'); ?>
                            </button>
                        </div>
                    </div>
                </div>
                <?php if($nextClass): ?>
                <!-- 下半年班级 -->
                <div class="col-sm-4">
                    <div class="input-group">
                    <select class="form-control selectpicker" multiple name="" id="nextclassselect" title="<?php echo Yii::t('site', 'Classes of next year: '); ?>">
                            <?php foreach ($nextClass as $value) { ?>
                                <option
                                    value="<?php echo $value->classid; ?>"><?php echo $value->title; ?></option>
                            <?php } ?>                        
                    </select>
                        <div class="input-group-btn" onclick="showChild('next')" >
                            <button type="button" class="btn btn-default" data-toggle="modal"
                                    data-target="#myModal"><?php echo Yii::t('site', 'Select recipients'); ?>
                            </button>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>
            <div class="form-group">
                <div class="col-sm-2 text-right"></div>
                <div class="col-sm-10">
                    <p class="text-danger"><?php echo Yii::t('site', 'Notice: "To list" will be replaced by reselecting receipients.'); ?></p>
                </div>
            </div>
            <!-- 回复给 -->
            <div class="form-group">
                <div class="col-sm-2 text-right">
                    <label for="" class="control-label"><?php echo Yii::t('site', 'Reply to:'); ?></label>
                </div>
                <div class="col-sm-10">
                    <input name="bulkmail[replyto]" type="email" class="form-control" placeholder="<?php echo Yii::t('site', 'Reply to:'); ?>">
                </div>
            </div>
            <!-- 主题 -->
            <div class="form-group">
                <div class="col-sm-2 text-right">
                    <label for="" class="control-label"><?php echo Yii::t('site', 'Subject:'); ?></label>
                </div>
                <div class="col-sm-10">
                    <input name="bulkmail[theme]" type="email" class="form-control" placeholder="<?php echo Yii::t('site', 'Subject:'); ?>">
                </div>
            </div>
            <!-- 内容 -->
            <div class="form-group">
                <div class="col-sm-2 text-right">
                    <label for="" class="control-label"><?php echo Yii::t('site', 'Content:'); ?></label>
                </div>
                <div class="col-sm-10">
                    <textarea name="bulkmail[main]" rows="8" class="form-control" placeholder="<?php echo Yii::t('site', 'Content:'); ?>"></textarea>
                </div>
            </div>
            <!-- 邮件附件 -->
            <div class="form-group">
                <div class="col-sm-2 text-right">
                    <label for="" class="control-label"><?php echo Yii::t('site', 'attachment:'); ?></label>
                </div>
                <div class="col-sm-10">
                    <div id="attachment"></div>
                    <hr>
                    <input id="inputfile" onchange="uploadatta()" type="file">
                    <br>
                    <span id="fileinfo"></span>
                </div>
            </div>
            <!-- 提交表单 -->
            <div class="form-group">
                <div class="col-sm-offset-2 col-sm-10">
                    <button onclick="submitForm()" class="btn btn-success" type="button"><?php echo Yii::t('site', 'Send'); ?></button>
                    <span id="info"></span>
                </div>
            </div>
        </form>
    </div>


    <!-- 选择孩子模态框 -->
    <div class="modal fade" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">&times;</span></button>
                    <label>
                        <h4 class="modal-title" id="myModalLabel"><input data-checklist="J_check_c1"
                                                                            class="J_check_all"
                                                                            data-direction="y" checked type="checkbox">
                            <?php echo Yii::t('site', 'Select recipients'); ?>
                        </h4>
                    </label>
                </div>
                <div class="modal-body J_check_wrap">
                    <div class="input-group" id="childern">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button"  class="btn btn-primary" data-dismiss="modal" onclick="save(this)">Add</button>
                    <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                </div>
               <!--  <h3 id="info" class="text-center text-"></h3> -->
            </div>
        </div>
    </div>
    <!-- 发送状态模态框 -->
    <div class="modal fade" id="sendModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="myModalLabel">
                        <?php echo Yii::t('site', 'Sending stats: Total: <span id="totalemail"></span>, succeeded <span id="successemail">0</span>, fail <span id="failemail">0</span>'); ?>
                        <small class="text-danger"><?php echo Yii::t('site', 'Please do not close this page while sending emails.'); ?></small>
                    </h4>
                </div>
                <div class="modal-body" style="height:80%">

                    <div class="container-fluid">
                        <div class="col-sm-8" id="waitsend">
                        </div>
                        <div class="col-sm-4" id="emailstatus">
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-primary copyMailAddress"><?php echo Yii::t('site', 'Copy failed email addresses'); ?></button>
                </div>
            </div>
        </div>
    </div>


    <!-- js代码部分 -->
    <script>
    $('#classselect').selectpicker({
        size:10   //设置select高度，同时显示5个值
    });
    $('#nextclassselect').selectpicker({
        size:10   //设置select高度，同时显示5个值
    });
        var failedMail = '';
        //显示孩子列表
        function showChild(type) {
            $('#childern').html('加载中....');
            if (type == 'current') {
                var classid = $('#classselect').val();
            } else {
                var classid = $('#nextclassselect').val();
            }
            $.ajax({
                url: '<?php echo $this->createUrl('index'); ?>',
                data: {showChild: type, classid: classid},
                type: 'post',
                dataType: 'json',
                success: function (data) {
                    var html = '';
                    for (x in data.childName) {
                        if (data.email[x])
                            html += "<div class='col-sm-4'><label class='form-group'><input type='checkbox' data-yid='J_check_c1' class='J_check' value=" + data.email[x] + " checked > " + data.childName[x] + "</label></div>";
                    }
                    $('#childern').html(html);
                    head.Util.checkAll();
                },
                error: function () {
                }
            });
        }
        //显示邮件列表
        function save() {
            $('#to').empty();
            var emailList = '';
            $('#childern input').each(function () {
                if (this.checked) {
                    emailList += $(this).val() + ';';
                }
                $('#to').val(emailList);
            });
        }
        //提交表单
        function submitForm() {
            //清空显示发送的邮件
            success = 0;
            fail = 0;
            $('#successemail').text(0);
            $('#failemail').text(0);

            $('#waitsend').empty();
            $('#emailstatus').empty();
            $.ajax({
                url: '<?php echo $this->createUrl('index'); ?>',
                data: $('#bulkmail').serialize(),
                type: 'post',
                dataType: 'json',
                success: function (data) {
                    if (data.msg == 'success') {
                        var total = 0;
                        for (x in data.emails) {
                            var label = "<label class='label label-default' id='mailAddress"+ x +"'>" + data.emails[x] + "</label><br><br>";
                            var label2 = "<label class='label label-default' id=email" + x + " ><?php echo Yii::t('site', 'Waiting to send'); ?></label><br><br>";
                            $('#waitsend').append(label);
                            $('#emailstatus').append(label2);
                            total++;
                        }
                        //显示邮件总数
                        $('#totalemail').text(total);
                        //显示模态框

                        $('#sendModal').modal({show: 'true', backdrop: 'static'});
                        // 调用发送邮件的方法
                        sendEmail(data.emails, data.mailinfo, 0);
                        //复制到剪贴板方法
                        var clipboard = new Clipboard('.copyMailAddress', {
                            container: document.getElementById('sendModal'),//动态设置target 需要指定一个容器
                            text: function (trigger) {
                                var _address = '';
                                $.each($('#waitsend').find('.failed'), function (i, val) {
                                    _address += $(val).text() + ';';
                                });
                                return _address;
                            }
                        });
                        clipboard.on('success', function(e) {
                            resultTip({msg:'<?php echo Yii::t('site', '已复制到剪贴板'); ?>'});
                        });
                    } else {
                        $('#info').text(data.info);
                    }
                },
                error: function (data) {
                    $('#info').text(data.info);
                    console.log(data.info);
                }
            });
        }
        //发送邮件
        function sendEmail(emails, mailinfo, i) {
            // console.log(emails);
            $.ajax({
                url: '<?php echo $this->createUrl('sendEmail'); ?>',
                data: {email: emails[i], mailinfo: mailinfo},
                type: 'post',
                dataType: 'json',
                success: function (data) {
                    if (data.msg == 'success') {
                        //成功的邮件数
                        success++;
                        $('#successemail').text(success);
                        $("#email" + i).text('<?php echo Yii::t('site', 'Send Success'); ?>');
                        $("#email" + i).removeClass();
                        $("#email" + i).addClass('label label-success');
                    }
                    if (data.msg == 'error') {
                        //失败的邮件数
                        fail++;
                        $('#failemail').text(fail);
                        $("#email" + i).text('<?php echo Yii::t('site', 'Send Fail'); ?>');
                        $("#mailAddress" + i).addClass('failed');
                        $("#email" + i).removeClass();
                        $("#email" + i).addClass('label label-danger');
                    }
                    if (emails[i + 1])
                        return sendEmail(emails, mailinfo, i + 1);
                },
                error: function (data) {
                    fail++;
                    $('#failemail').text(fail);
                    $("#email" + i).text('<?php echo Yii::t('site', 'Send Fail'); ?>');
                    $("#mailAddress" + i).addClass('failed');
                    $("#email" + i).removeClass();
                    $("#email" + i).addClass('label label-danger');
                    if (emails[i + 1])
                        return sendEmail(emails, mailinfo, i + 1);
                }
            });
        }
        //测试发信帐号
        function testSend(btn) {
            var _flag = true;
            if($("input[name='bulkmail[user]']").val() == '' || $("input[name='bulkmail[password]']").val() == '' || $("input[name='bulkmail[smtp]']").val() == '' || $("input[name='bulkmail[port]']").val() == ''){
                $('.mailInfo').text('<?php echo Yii::t('site', 'Please Complete email account info.'); ?>').show();
                _flag = false;
            }
            if(!_flag){
                return false;
            }
            $(btn).text('<?php echo Yii::t('site', 'connecting...'); ?>');
            $(btn).attr('disabled', 'disabled');
            $('.mailInfo').hide();
            $.ajax({
                url: '<?php echo $this->createUrl('testSend'); ?>',
                data: $('#bulkmail').serialize(),
                type: 'post',
                dataType: 'json',
                success: function (data) {
                    if (data.msg == 'success') {
                        $(btn).next().text('<?php echo Yii::t('site', 'connection succeess'); ?>').show();
                        $(btn).text('<?php echo Yii::t('site', 'Clict to connect'); ?>');
                        $(btn).removeAttr('disabled');
                    } else {
                        $(btn).next().text('<?php echo Yii::t('site', 'connection failed'); ?>').show();
                        $(btn).text('<?php echo Yii::t('site', 'Clict to connect'); ?>');
                        $(btn).removeAttr('disabled');
                    }
                },
                error: function (data) {
                    $(btn).next().text('<?php echo Yii::t('site', 'connection failed'); ?>').show();
                    $(btn).text('<?php echo Yii::t('site', 'Clict to connect'); ?>');
                    $(btn).removeAttr('disabled');

                }
            });
        }
        //上传附件
        function uploadatta() {
            var data = new FormData();
            $.each($('#inputfile')[0].files, function (i, file) {
                data.append('upload_file', file);
            });
            $.ajax({
                url: '<?php echo $this->createUrl('uploadatta'); ?>',
                data: data,
                type: 'post',
                dataType: 'json',
                contentType: false,    //不可缺
                processData: false,    //不可缺
                success: function (data) {
                    if (data.msg == 'success') {
                        //清空文件域
                        $("#inputfile").after($("#inputfile").clone().val(""));
                        $("#inputfile").remove();
                        //显示附件
                        var span = '<span onclick="delAtt(this)" class="glyphicon glyphicon-remove" aria-hidden="true"></span> ';
                        var atta = '<label class="btn btn-success btn-xs"><a style="color:#fff" target="_blank" href="' + data.url + '">' + data.fileName + '</a> '+span+'<input type="hidden" name="bulkmail[atta][]" value="' + data.saveName + ';' + data.fileName + '" /></label> ';
                        $("#attachment").append(atta);
                    } else {
                        $("#fileinfo").text(data.msg);
                    }
                },
                error: function (data) {
                    $("#fileinfo").text(data.msg);
                }
            });
        }

        // 删除附件
        function delAtt(obj) {
            var r = confirm('确定删除？');
            if (r == true) {
                $(obj).parent().remove();
            }
        }

        $('.smtp').on('click', function () {
            var _v = $(this).attr('_value');
            $('#bulkmailSmtp').val(_v);
        })
    </script>