<?php

class InvoiceItemForm extends CFormModel
{
    public $feetype;
	public $feeamount;
    public $discount_id;
    public $child_service_info;
	public $showServiceDay=false;
    public $project_num;
    public $feename;

    public $fData = array();

	/**
	 * Declares the validation rules.
	 */
	public function rules()
	{
		return array(
            array('feetype, feeamount, discount_id, child_service_info, showServiceDay, fData, project_num', 'safe'),
			array("child_service_info", "matchTuitionType")
		);
	}


    public function matchTuitionType($attribute, $params) {
        if (!$this->hasErrors()) {
			if($this->feetype > 0 && $this->feename != FEETYPE_LUNCH){
				$pattern = '/\w+(\d)D/';
				$matches = null;

				if( preg_match($pattern, $this->feeamount,$matches) ){
					$halfDays = $matches[1];
					if($halfDays == 5){
						foreach(array_keys($this->child_service_info) as $_k){
							$this->child_service_info[$_k] = 20;
						}
						return true;
					}else{
						$days['y']=$days['n']=$days['o']=0;
						foreach($this->child_service_info as $_v){
							if($_v == 20) $days['y']++;
							elseif($_v == 40) $dyas['n']++;
							else $days['o']++;
						}
						if($days['o'] || ($days['y']!=$halfDays)){
							$this->showServiceDay = true;
							$this->addError("feeamount", Yii::t("payment", "Day selections do not match tuition day type."));
						}
					}
				}else{
                    if (is_array($this->child_service_info) && count($this->child_service_info)){
                        foreach(array_keys($this->child_service_info) as $_k){
                            $this->child_service_info[$_k] = 10;
                        }
                    }
				}
			}

        }
    }
	/**
	 * Declares customized attribute labels.
	 * If not declared here, an attribute would have a label that is
	 * the same as its name with the first letter in upper case.
	 */
	public function attributeLabels()
	{
		return array(
			'title'=>Yii::t('invoice', 'Invoice Title'),
		);
	}

    public function renderField($key='feetype', $fee='')
    {
		switch($key){
			case 'feetype':
				$ret = isset($this->fData['feetype'])?'<label>'.CHtml::activeCheckBox($this, 'feetype',
                        array('name'=>'InvoiceItemForm['.$fee.'][feetype]')).$this->fData['feetype'][$fee].'</label>':'';
			break;
			case 'feeamount':
				$htmlOptions = array('class'=>'select_3');
				if($fee == 'FEETYPE_TUITION'){
					$htmlOptions['onchange'] = 'showServiceInfo(this);';
				}
				$htmlOptions['name'] = 'InvoiceItemForm['.$fee.'][feeamount]';
				$ret = isset($this->fData['feeamount'])&&$this->fData['feeamount'][$fee]?
					CHtml::activeDropDownList($this, 'feeamount', $this->fData['feeamount'][$fee],
						$htmlOptions)
					:'';
				if( in_array($fee , array('FEETYPE_TUITION', 'FEETYPE_LUNCH') ) ){
					$name = 'InvoiceItemForm['.$fee.'][child_service_info]';
					$ret .= self::renderFeeAmount($this, 'child_service_info', $fee=='FEETYPE_TUITION'?true:false, $fee, array("name"=>$name));
				}
			break;
			case 'discount':
				$ret = isset($this->fData['discount'][$fee])?
					CHtml::activeDropDownList($this, 'discount_id', $this->fData['discount'][$fee],
						array(
							'class'=>'select_2',
							'name'=>'InvoiceItemForm['.$fee.'][discount_id]',
                            'empty'=> Yii::t('global','N/A'),
                            'options'=>array(
                                $this->fData['_discount'][$fee]=>array('disabled'=>true,
                                    'label'=>$this->fData['discount'][$fee][$this->fData['_discount'][$fee]].'（已过期或被禁用）'),
                            ),
                        ))
					:'';
			break;
            case 'project_num':
                $ret = CHtml::activeNumberField($this, 'project_num', array('name'=>'InvoiceItemForm['.$fee.'][project_num]', 'class'=>'input length_1'));
            break;
            case 'service_info_show':
                $this->showServiceDay=true;
                $weekday = array('mon', 'tue', 'wed', 'thu', 'fri');
                foreach ($weekday as $day){
                    $this->child_service_info[$day]=10;
                }
            break;
		}
        return $ret;
    }

	public function renderFeeAmount($model, $attribute, $halfDay=false, $fee, $htmlOptions){

		$_name2 = 'InvoiceItemForm['.$fee.'][showServiceDay]';

		$boxId = $fee . '_SERVICE_INFO';
        $weekday = array('mon', 'tue', 'wed', 'thu', 'fri');
		$display = ($this->showServiceDay) ? "display:block;" : "display:none;";
		$defaultServiceValue = ($this->showServiceDay) ? 40 : 10;
        $tag = CHtml::openTag('div', array('class'=>'service-day '.$fee, 'id'=>$boxId, 'style'=>$display, 'pname'=>$_name2));
        foreach ($weekday as $day){
            $name = $htmlOptions['name'].'['.$day.']';
			if($this->showServiceDay)
				$tag .= CHtml::openTag('i', array('class'=>'day-'.$model->child_service_info[$day],
                    'title'=>ucfirst($day),
                    'halfday'=>$halfDay,
                    'rel'=>CHtml::getIdByName($name)));
			else
				$tag .= CHtml::openTag('i', array('class'=>'day-'.$defaultServiceValue,
                    'title'=>ucfirst($day),
                    'halfday'=>$halfDay,
                    'rel'=>CHtml::getIdByName($name)));
            $tag .= CHtml::closeTag('i');

			if($this->showServiceDay)
				$tag .= CHtml::activeHiddenField($model, 'child_service_info['.$day.']',
                    array('id'=>CHtml::getIdByName($name), 'name'=>$name));
			else
				$tag .= CHtml::activeHiddenField($model, 'child_service_info['.$day.']',
                    array('id'=>CHtml::getIdByName($name), 'value'=>$defaultServiceValue, 'name'=>$name));
        }
        $tag .= CHtml::openTag('div', array('class'=>'c'));
        $tag .= CHtml::closeTag('div');
        $tag .= CHtml::closeTag('div');

		$tag .= CHtml::activeHiddenField($model, 'showServiceDay', array('id'=>CHtml::getIdByName($_name2),
            'value'=>intval($this->showServiceDay),
            'name'=>$_name2,
            'class'=>'showServiceDay'));
        return $tag;
	}

    //public function getConfigs($key='PAYGROUP_ANNUAL'){
    //    $normalInvoice = array(
    //        'COMMON' => array(
    //            'language','startdate'
    //        ),
    //        'INVOICE' => array(
    //            FEETYPE_TUITION => array('feetype', 'feeamount', 'discount'),
    //            FEETYPE_LUNCH => array('feetype', 'feeamount' ),
    //            FEETYPE_SCHOOLBUS => array('feetype', 'feeamount'),
    //        )
    //    );
    //
    //    $ret = array(
    //        REG_DEPOSIT => array(
    //            'COMMON' => array(
    //                'language',
    //            ),
    //            'INVOICE' => array(
    //                FEETYPE_MATERIAL => array('feetype', 'feeamount'),
    //                FEETYPE_DEPOSIT => array('feetype', 'feeamount'),
    //            )
    //        ),
    //        PAYGROUP_ANNUAL => $normalInvoice,
    //        PAYGROUP_SEMESTER => $normalInvoice,
    //        PAYGROUP_MONTH => $normalInvoice,
    //        HOLIDAY_MONTH => array(
    //            'COMMON' => array(
    //                'language','startdate','enddate'
    //            ),
    //            'INVOICE' => array(
    //                FEETYPE_TUITION => array('feetype', 'feeamount', 'discount'),
    //                FEETYPE_LUNCH => array('feetype', 'feeamount'),
    //                FEETYPE_SCHOOLBUS => array('feetype', 'feeamount'),
    //            )
    //        ),
    //        ENABLE_AFTERSCHOOLS => array(
    //            'COMMON' => array(
    //                'language','startdate','enddate'
    //            ),
    //            'INVOICE' => array(
    //                FEETYPE_CAROUSEL => array('feetype', 'feeamount'),
    //                FEETYPE_AFTERSCHOOL => array('feetype', 'feeamount'),
    //            )
    //        ),
    //        FEETYPE_LIBCARD => array(
    //            'COMMON' => array(
    //                'language',
    //            ),
    //            'INVOICE' => array(
    //                FEETYPE_LIBCARD => array('feetype', 'feeamount'),
    //            )
    //        ),
    //    );
    //
    //    return isset($ret[$key]) ? $ret[$key] : $normalInvoice;
    //}
}