<?php

class NoticeController extends TeachBasedController
{
    // 访问action的初级权限
    public $actionAccessAuths = array(
        'index'                 => 'o_T_Access'
    );

    public $toReplyNum;

    public $schoolType;

    public $schoolList;

    public function init()
    {
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'teaching';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site', 'Teaching Tasks');

        //初始化选择校园页面
        $schoolList = CommonUtils::allSchoolList();
        foreach ($this->accessBranch as $key => $item) {
            if (!in_array($item, $schoolList)) {
                unset($this->accessBranch[$key]);
            }
        }
        $this->branchSelectParams['urlArray'] = array('//mteaching/notice/index');
        // 自动重定向未选择学校前的 action
        if ($redirectAction = $_GET['redirectAction']) {
            $this->branchSelectParams['urlArray'] = array('//mteaching/notice/' . $redirectAction);
        }
        $cs = Yii::app()->clientScript;
        $cs->registerCoreScript('jquery.ui');
        $cs->registerCssFile($cs->getCoreScriptUrl() . '/jui/css/base/jquery-ui.css');
        $cs->registerCssFile(Yii::app()->themeManager->baseUrl . '/base/js/viewer/viewer.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/vue2.js');
        // 七牛上传所需文件
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/plupload/plupload.full.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/qiniu.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/html2canvas.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/mark/mark.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/jquery.qrcode.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/viewer/viewer.js');
        $cs->registerCssFile(Yii::app()->themeManager->baseUrl . '/base/js/element/index.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/hls/hls.min.js');
        Yii::import('common.models.portfolio.*');
    }

    public function beforeAction($action) {
        $this->schoolType = 'ivy';
        $this->schoolList = CommonUtils::ivySchoolList();
        if (in_array($_GET['branchId'], CommonUtils::dsSchoolList())) {
            $this->schoolType = 'ds';
            $this->schoolList = CommonUtils::dsSchoolList();
        }
        return parent::beforeAction($action);
    }

    public function actionIndex()
    {
        // 引入图表所需文件
        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/echarts.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/xlsx.full.min.js');

        $data = array(
            'configList' => $this->getConfig(),
            'groupStudents' => array()
        );

        // 查找待回复 notice 数量
        $this->toReplyNum = $this->getToReplyNum();

        $this->render('index', array("data" => $data));
    }

    /**
     * 显示，ID 为0时为新增
     *
     * @param integer $id
     * @return void
     */
    public function actionEdit($id = 0)
    {
        $cs = Yii::app()->clientScript;
        // 在线编辑器
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/tinymce/tinymce.min.js');

        $notice = $this->getOneForEdit($id, Yii::app()->request->getParam('branchId', ''));
        $this->render('edit', array("data" => array(
            "id" => $id,
            "notice" => $notice['notice'],
            "addresser" => $notice['addresser'],
            "gradesStatus" => $notice['gradesStatus'],
            "jointAdmins" => $notice['jointAdmins'],
            "categoryList" => $notice['categoryList'],
            'configList' => $this->getConfig(),
        )));
    }

    /**
     * 显示，ID 为0时为新增
     *
     * @param integer $id
     * @return void
     */
    public function actionCategoryUpdate($id = 0)
    {

        $id = Yii::app()->request->getParam("id");
        $category = Yii::app()->request->getParam("category");
        $isDev = Yii::app()->request->getParam("isDev");

        $data = array(
            'category' => $category,
            'isDev' => $isDev,
        );
        $res = CommonUtils::requestDsOnline('notice/updateCategory/' . $id, $data);
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res['data']);
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
        }
        $this->showMessage();
    }

    // 反馈界面
    public function actionFeedback()
    {
        $this->branchSelectParams['urlArray'] = array('//mteaching/notice/feedback');
        $data = array();
        $this->render('feedback', array("data" => $data));
    }

    // 待回复列表
    public function actionUnreply()
    {
        $targetId = $this->getUid();
        $res = CommonUtils::requestDsOnline('notice/unReplayList', array('targetId' => $targetId, 'school_id' => $this->branchId));
        if ($res['code'] == 0) {
            $data = $res['data'];
            $childIdList = array();
            foreach ($data['items'] as $v) {
                $childIdList = array_merge($childIdList, $v['childids']);
            }

            $childIdList = array_unique($childIdList);
            $childs = array();
            if ($childIdList) {
                $childModels = ChildProfileBasic::model()->findAllByPk($childIdList);
                foreach ($childModels as $childModel) {
                    $childs[$childModel->childid] = array(
                        'name' => $childModel->getChildName(),
                        'avatar' => CommonUtils::childPhotoUrl($childModel->photo),
                        'class_name' => $childModel->ivyclass->title
                    );
                    if ($childModel->fid) {
                        $childs[$childModel->childid]["p_" . $childModel->fid] = 'f';
                    }
                    if ($childModel->mid) {
                        $childs[$childModel->childid]["p_" . $childModel->mid] = 'm';
                    }
                }
            }
            $data['childs'] = $childs;

            $this->addMessage('state', 'success');
            $this->addMessage('data', $data);
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
        }
        $this->showMessage();
    }

    // 已回复列表
    public function actionReplied()
    {
        $targetId = $this->getUid();
        $page = Yii::app()->request->getParam("page", 1);

        $take = 20;
        $skip = ($page - 1) * $take;
        $res = CommonUtils::requestDsOnline('notice/repliedList', array(
            'targetId' => (int)$targetId,
            'skip' => (int)$skip,
            'take' => $take,
            'school_id' => $this->branchId
        ));
        if ($res['code'] == 0) {
            $data = $res['data'];
            $childIdList = array();
            foreach ($data['items'] as $v) {
                $childIdList = array_merge($childIdList, $v['childids']);
            }

            $childIdList = array_unique($childIdList);
            $childs = array();
            if ($childIdList) {
                $childModels = ChildProfileBasic::model()->findAllByPk($childIdList);
                foreach ($childModels as $childModel) {
                    $childs[$childModel->childid] = array(
                        'name' => $childModel->getChildName(),
                        'avatar' => CommonUtils::childPhotoUrl($childModel->photo),
                        'class_name' => $childModel->ivyclass->title
                    );
                    if ($childModel->fid) {
                        $childs[$childModel->childid]["p_" . $childModel->fid] = 'f';
                    }
                    if ($childModel->mid) {
                        $childs[$childModel->childid]["p_" . $childModel->mid] = 'm';
                    }
                }
            }
            $data['childs'] = $childs;

            $this->addMessage('state', 'success');
            $this->addMessage('data', $data);
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
        }
        $this->showMessage();
    }

    /**
     * 获取孩子的评论列表
     *
     * @return void
     */
    public function actionItem()
    {
        $id = Yii::app()->request->getParam('id');
        $childId = Yii::app()->request->getParam('child_id');
        if (!$id || !$childId) {
            $this->addMessage('state', 'fail');
            $this->addMessage("message", "参数错误");
            $this->showMessage();
        }

        $res = CommonUtils::requestDsOnline("notice/childComments", array('id' => $id, 'childId' => (int)$childId));
        if ($res['code'] == 0) {
            $data = $res['data'];
            $staff = array();
            $data['staffIdList'][] = Yii::app()->user->getId();
            if ($data['staffIdList']) {
                $staffInfoList = $this->getUserInfo($data['staffIdList']);
                foreach ($staffInfoList as $staffInfo) {
                    $staff[$staffInfo['uid']] = array(
                        'name' => $staffInfo['name'],
                        'avatar' => $staffInfo['photo'],
                    );
                }
                unset($data['staffIdList']);
            }
            $data['staff'] = $staff;

            $this->addMessage("state", "success");
            $this->addMessage("data", $data);
        } else {
            $this->addMessage("state", "fail");
            $this->addMessage("message", $res['msg']);
        }
        $this->showMessage();
    }

    // 查看关联反馈
    public function actionViewComment()
    {
        $id = Yii::app()->request->getParam("id");
        if (!$id) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', 'id error');
            $this->showMessage();
        }

        $res = CommonUtils::requestDsOnline('journal/viewComment', array(
            'id' => $id,
        ));
        if ($res['code'] == 0) {
            $data = $res['data'];
            $childIdList = array_unique($res['data']['childIdList']);
            $res['data']['staffIdList'][] = Yii::app()->user->getId();
            $staffIdList = array_unique($res['data']['staffIdList']);

            $childs = array();
            $staff = array();
            if ($childIdList) {
                $childModels = ChildProfileBasic::model()->findAllByPk($childIdList);
                foreach ($childModels as $childModel) {
                    $childs[$childModel->childid] = array(
                        'name' => $childModel->getChildName(),
                        'avatar' => CommonUtils::childPhotoUrl($childModel->photo),
                        'class_name' => $childModel->ivyclass->title
                    );
                    if ($childModel->fid) {
                        $childs[$childModel->childid]["p_" . $childModel->fid] = 'f';
                    }
                    if ($childModel->mid) {
                        $childs[$childModel->childid]["p_" . $childModel->mid] = 'm';
                    }
                }
            }
            if ($staffIdList) {
                $staffInfoList = $this->getUserInfo($data['staffIdList']);
                foreach ($staffInfoList as $staffInfo) {
                    $staff[$staffInfo['uid']] = array(
                        'name' => $staffInfo['name'],
                        'avatar' => $staffInfo['photo'],
                    );
                }
            }
            $data['childs'] = $childs;
            $data['staff'] = $staff;
            unset($data['childIdList']);
            unset($data['staffIdList']);

            $this->addMessage('state', 'success');
            $this->addMessage('data', $data);
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
        }
        $this->showMessage();
    }

    // 保存老师评论
    public function actionSaveComment()
    {
        $id = Yii::app()->request->getParam("id");
        $childId = Yii::app()->request->getParam("child_id");
        $content = Yii::app()->request->getParam("content");
        $mark = Yii::app()->request->getParam("mark_as_staff", 0);
        $attachments = Yii::app()->request->getParam("attachments", array());
        $uid = Yii::app()->user->getId();

        $childModel = ChildProfileBasic::model()->findByPk($childId);
        if (!$childModel) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', 'child id error');
            $this->showMessage();
        }
        $data = array(
            'link_id' => $id,
            'link_type' => 'notice',
            'child_id' => $childId,
            'class_id' => $childModel->classid,
            'school_id' => $this->branchId,
            'content' => $mark == 1 ? 'No Reply Needed' : $content,
            'creator_type' => 'staff',
            'mark_as_staff' => $mark,
            'created_by' => (int)$uid,
            'attachments' => $attachments,
        );
        $res = CommonUtils::requestDsOnline('journal/saveComment', $data);
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res['data']);
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
        }
        $this->showMessage();
    }

    public function actionGetCommentQiniuToken()
    {
        $linkId = Yii::app()->request->getParam('linkId');
        if (!$linkId) {
            $this->addMessage("state", "fail");
            $this->addMessage('message', "参数错误");
            $this->showMessage();
        }
        $data = array(
            'linkId' => $linkId,
            'linkType' => 'comment',
            'isPrivate' => 1,
        );
        $res = CommonUtils::requestDsOnline('getQiniuToken', $data);
        $this->addMessage("state", "success");
        $this->addMessage('data', $res);
        $this->showMessage();
    }

    // 获取当前用户待回复 notice 数量
    public function getToReplyNum()
    {
        $targetId = Yii::app()->user->getId();
        $res = CommonUtils::requestDsOnline('notice/toReplyNum/' . $targetId, array('school_id' => $this->branchId));

        $num = 0;
        if ($res['code'] == 0) {
            $num = $res['data'];
        }
        return $num;
    }

    public function actionGetList()
    {

        $gradeGroup = Yii::app()->request->getParam('gradeGroup');
        $searchText = Yii::app()->request->getParam('searchText');
        $sign_as_uid = Yii::app()->request->getParam('staff');
        $category = Yii::app()->request->getParam('category');

        // 当前页码
        $pageNum = Yii::app()->request->getParam('pageNum', 1);
        $pageSize = Yii::app()->request->getParam('pageSize', 20);

        // 筛选条件
        $filter = array(
            'schoolId' => $this->branchId,
        );

        if ($gradeGroup) {
            $filter['gradeGroup'] = $gradeGroup;
        }
        if($searchText){
            $filter['searchText'] = $searchText;
        }
        if($sign_as_uid){
            $filter['sign_as_uid'] = $sign_as_uid;
        }
        if($category){
            $filter['category'] = $category;
        }

        $list = $this->getList($pageNum, $pageSize, $filter);

        // 计算分页
        $total = $list['total'];
        $pages = ceil($total / $pageSize);
        $pagination  = array(
            'pages' => $pages,
            'pageNum' => $pageNum,
            'pageSize' => $pageSize,
            'total' => $total
        );

        $userIdList = array();
        $noticeIdList = array();
        foreach ($list['list'] as $item) {
            $userIdList[] = $item['updated_by'];
            $noticeIdList[] = $item['_id'];
        }
        //获取所有发布过notice的署名人老师
        $sign_as_uid = array();
        $res = CommonUtils::requestDsOnline('notice/teachers', array(
            'school_id' => $this->branchId,
            'grade_groups'=>$gradeGroup
            ));

        if ($res['code'] == 0) {
            $sign_teacher_data = $res['data'];
            $sign_as_uid = array_keys($sign_teacher_data);
        }
        $userIdList = array_merge($userIdList, $sign_as_uid);
        $userIdList = array_unique($userIdList);
        $userInfoList = $this->getUserInfo($userIdList);
        foreach ($userInfoList as $k=>$v){
            $userInfoList[$k]['total'] = empty($sign_teacher_data[$v['uid']]['total']) ? 0 : $sign_teacher_data[$v['uid']]['total'];
        }
        $data = array(
            'list' => $list['list'],
            'responseData' => $list['responseData'],
            'emailTaskData' => $list['emailTaskData'],
            'addresser' => $list['addresser'],
            'categoryList' => $list['categoryList'],
            'userInfoList' => $userInfoList,
            'sign_as_uid' => $sign_as_uid,
            'pagination' => $pagination,
            'school_title'=>$this->branchObj['title'],
        );

        $this->addMessage('state', 'success');
        $this->addMessage('data', $data);
        $this->showMessage();
    }

    /**
     * 获取发布过notice的所有署名人信息
     */
    public function actionGetNoticeSignTeacher()
    {
        $gradeGroup = Yii::app()->request->getParam('gradeGroup');
        $res = CommonUtils::requestDsOnline('notice/teachers', array(
            'school_id' => $this->branchId,
            'grade_groups'=>$gradeGroup
        ));
        if (isset($res['code']) && $res['code'] == 0) {
            $userIdList = $res['data'];
            $userInfoList = $this->getUserInfo($userIdList);
            $this->addMessage("state", "success");
            $this->addMessage("message", "success");
            $this->addMessage("data", array(
                'userInfoList' => $userInfoList,
            ));
        } else {
            $this->addMessage("state", "fail");
            $this->addMessage("message", $res['msg']);
        }
        $this->showMessage();
    }

    /**
     * 获取确认进度
     *
     * @return void
     */
    public function actionGetResponseData()
    {
        $noticeId = Yii::app()->request->getParam('id');
        if (!$noticeId) {
            $this->addMessage("state", "fail");
            $this->addMessage("message", "ID 为必须");
            $this->showMessage();
        }

        $res = CommonUtils::requestDsOnline("notice/responseStudentData/" . $noticeId);
        if (isset($res['code']) && $res['code'] == 0) {
            $this->addMessage("state", "success");
            $this->addMessage("message", "success");
            $this->addMessage("data", $res['data']);
        } else {
            $this->addMessage("state", "fail");
            $this->addMessage("message", $res['msg']);
        }
        $this->showMessage();
    }

    public function actionAdd()
    {
        $data = Yii::app()->request->getPost("data");

        $data['school_ids'] = array($this->branchId);
        $data['created_by'] = Yii::app()->user->getId();
        $data['updated_by'] = Yii::app()->user->getId();

        $res = CommonUtils::requestDsOnline("notice/add", $data);
        if (isset($res['code']) && $res['code'] == 0) {
            $this->addMessage("state", "success");
            $this->addMessage("message", "success");
            $this->addMessage("data", $res['data']);
        } else {
            $this->addMessage("state", "fail");
            $this->addMessage("message", $res['msg']);
        }
        $this->showMessage();
    }

    // 复制公告需要的数据
    public function actionCopyData()
    {
        $schoolModels = $this->getAllBranch();
        $schoolList = CommonUtils::allSchoolList();
        $data = array();
        foreach ($schoolList as $schoolId) {
            $title = isset($schoolModels[$schoolId]) ? $schoolModels[$schoolId]['title'] : $schoolId;
            if (in_array($schoolId, $this->accessBranch) && $schoolId != $this->branchId) {
                $grades = $this->getGradeList($schoolId);
                $getConfig = $this->getConfig();
                $data[] = array(
                    'schoolId' => $schoolId,
                    'title' => $title,
                    'grades' => $grades,
                    'child_mark' => $this->getMarkList($schoolId),
                );
            }
        }
        $this->addMessage('state', 'success');
        $this->addMessage('data', $data);
        $this->showMessage();
    }

    // 复制公告
    public function actionCopy()
    {
        $id = Yii::app()->request->getParam('id');
        $copyData = Yii::app()->request->getParam('copyData');

        $this->addMessage("state", "fail");
        if (!$id || !$copyData || !is_array($copyData)) {
            $this->addMessage("message", "参数错误");
            $this->showMessage();
        }
        if (!$this->multipleBranch) {
            $this->addMessage("message", "无权限");
            $this->showMessage();
        }
        $data = array();
        foreach ($copyData as $item) {
            if (!isset($item['schoolId']) || !isset($item['grades']) || !is_array($item['grades'])) {
                continue;
            }
            if (!in_array($item['schoolId'], $this->accessBranch)) {
                continue;
            }
            $data[] = array(
                'schoolId' => $item['schoolId'],
                'grades' => $item['grades'],
            );
        }
        $res = CommonUtils::requestDsOnline("notice/copy/" . $id, array('targets' => $data));
        if (isset($res['code'])  && $res['code'] == 0) {
            $this->addMessage("state", "success");
            $this->addMessage("message", "success");
            $this->addMessage("data", $res['data']);
        } else {
            $this->addMessage("state", "fail");
            $this->addMessage("message", $res['msg']);
        }
        $this->showMessage();
    }

    public function actionGetOne()
    {
        $id = Yii::app()->request->getParam('id');

        $this->addMessage("state", "fail");
        if (!$id) {
            $this->addMessage("message", "ID 不能为空");
            $this->showMessage();
        }
        $notice = $this->getOne($id);
        if (!$notice) {
            $this->addMessage("message", "notice 不存在");
            $this->showMessage();
        }
        $this->addMessage("state", "success");
        $this->addMessage('data', $notice);
        $this->showMessage();
    }

    public function actionUpdate()
    {
        $data = Yii::app()->request->getPost("data");

        $id = $data['_id'];
        if (!$id) {
            $this->addMessage("message", "ID 不能为空");
            $this->showMessage();
        }

        $data['updated_by'] = Yii::app()->user->getId();

        $res = CommonUtils::requestDsOnline("notice/edit/" . $id, $data);
        if (isset($res['code'])  && $res['code'] == 0) {
            $this->addMessage("state", "success");
            $this->addMessage("message", "success");
            $this->addMessage("data", $res['data']);
        } else {
            $this->addMessage("state", "fail");
            $this->addMessage("message", $res['msg']);
        }
        $this->showMessage();
    }

    public function actionDelete()
    {
        $this->addMessage("state", "fail");
        $id = Yii::app()->request->getPost('id');
        if (!$id) {
            $this->addMessage("message", "ID 不能为空");
            $this->showMessage();
        }
        $notice = $this->getOne($id);
        if (!$notice) {
            $this->addMessage("message", "notice 不存在");
            $this->showMessage();
        }

        $data = array("_id" => $id);
        $res = CommonUtils::requestDsOnline("notice/delete/" . $id, $data);
        $this->addMessage("state", "success");
        $this->addMessage("message", "success");
        $this->addMessage("data", $res['data']);
        $this->showMessage();
    }

    public function actionOffline()
    {
        $id = Yii::app()->request->getPost('id');
        if (!$id) {
            $this->addMessage("message", "ID 不能为空");
            $this->showMessage();
        }
        $journal = $this->getOne($id);
        if (!$journal) {
            $this->addMessage("message", "notice 不存在");
            $this->showMessage();
        }
        $data = array("_id" => $id);
        $res = CommonUtils::requestDsOnline("notice/offline", $data);
        $this->addMessage("state", "success");
        $this->addMessage("message", $res['msg']);
        $this->showMessage();
    }

    /**
     * 添加协同管理
     *
     * @return void
     */
    public function actionJointAdminsAdd()
    {
        $noticeId = Yii::app()->request->getParam('noitice_id');
        $staffId = Yii::app()->request->getParam('staff_id');
        $requestUrl = 'notice/jointAdmins/add';
        $requestData = array('noticeId' => $noticeId, 'staffId' => $staffId);
        $this->remote($requestUrl, $requestData);
    }

    /**
     * 删除协同管理
     *
     * @return void
     */
    public function actionJointAdminsDel()
    {
        $noticeId = Yii::app()->request->getParam('noitice_id');
        $staffId = Yii::app()->request->getParam('staff_id');
        $requestUrl = 'notice/jointAdmins/del';
        $requestData = array('noticeId' => $noticeId, 'staffId' => $staffId);
        $this->remote($requestUrl, $requestData);
    }

    /**
     * 清空协同管理
     *
     * @return void
     */
    public function actionJointAdminsDelAll()
    {
        $noticeId = Yii::app()->request->getParam('noitice_id');
        $requestUrl = 'notice/jointAdmins/delAll';
        $requestData = array('noticeId' => $noticeId);
        $this->remote($requestUrl, $requestData);
    }

    public function actionGetQrCode()
    {
        $type = Yii::app()->request->getPost('type');
        $id = Yii::app()->request->getPost('id');
        $schoolid = Yii::app()->request->getPost('schoolid');

        if (!$type) {
            $this->addMessage("message", "Type 不能为空");
            $this->showMessage();
        }
        if ($type != 'list') {
            if (!$id) {
                $this->addMessage("message", "ID 不能为空");
                $this->showMessage();
            }
            $journal = $this->getOne($id);
            if (!$journal) {
                $this->addMessage("message", "notice 不存在");
                $this->showMessage();
            }
            $datapreview = array("_id" => $id, "type" => 'preview', "schoolid" => $this->branchId);
            $respreview = CommonUtils::requestDsOnline("notice/qrCode", $datapreview);

            $datashare = array("_id" => $id, "type" => 'share', "schoolid" => $this->branchId);
            $resshare = CommonUtils::requestDsOnline("notice/qrCode", $datashare);

            $data = array("preview" => $respreview["data"], "share" => $resshare["data"]);
        } else {
            $dataInfo = array("type" => $type, "schoolid" => $this->branchId);
            $res = CommonUtils::requestDsOnline("notice/qrCode", $dataInfo);
            $data = $res["data"];
        }

        $this->addMessage("state", "success");
        $this->addMessage("message", $res['msg']);
        $this->addMessage("data", $data);
        $this->showMessage();
    }

    /**
     * 发送提醒邮件
     *
     * @return void
     */
    public function actionSendEmail()
    {
        $id = Yii::app()->request->getPost('id');
        $pass = Yii::app()->request->getPost('pass');
        $data = array("_id" => $id);

        $uid = Yii::app()->user->getId();
        $count = 0;
        if ($uid > 0 && !empty($pass)) {
            $pass = md5($pass);
            $criteria = new CDbCriteria;
            $criteria->compare('uid', $uid);
            $criteria->compare('pass', $pass);
            $criteria->compare('isstaff', 1);
            $criteria->compare('level', '>', 0);
            $count = User::model()->count($criteria);

            if ($count == 0) {
                $this->addMessage("state", "fail");
                $this->addMessage("data", array('code' => 100, 'count' => $count));
                $this->addMessage("message", "password error");
            } else {
                $res = CommonUtils::requestDsOnline("notice/sendEmail/" . $id, $data);
                if (isset($res['code'])  && $res['code'] == 0) {
                    $this->addMessage("state", "success");
                    $this->addMessage("message", "success");
                    $this->addMessage("data", $res['data']);
                } else {
                    $this->addMessage("state", "fail");
                    $this->addMessage("message", $res['msg']);
                }
            }
        } else {
            $this->addMessage("state", "fail");
        }

        $this->showMessage();
    }

    /**
     * 获取邮件发送报告
     *
     * @return void
     */
    public function actionEmailReport()
    {

        $id = Yii::app()->request->getPost('id');
        if (!$id) {
            $this->addMessage("message", "ID 不能为空");
            $this->showMessage();
        }
        $res = CommonUtils::requestDsOnline("notice/emailReport/" . $id, array("schoolId" => $this->branchId));
        if (isset($res['code'])  && $res['code'] == 0) {

            $childIdList = array();
            $noEmailList = array();
            $errorList = array();
            $totalNum = $res['data']['receivers'];
            $successNum = $res['data']['sent_list'];
            $redundantNum = isset($res['data']['redundant']) ? $res['data']['redundant'] : 0;
            foreach ($res['data']['empty_emails_child_ids'] as $v) {
                $noEmailList[] = $v;
                $childIdList[] = $v;
            }
            foreach ($res['data']['error_list'] as $v) {
                $errorList[$v['cid']][] = $v['email'];
                $childIdList[] = $v['cid'];
            }
            $childModels = ChildProfileBasic::model()->findAllByPk($childIdList);
            $childInfo = array();
            foreach ($childModels as $childModel) {
                $childInfo[$childModel->childid] = array(
                    'childId' => $childModel->childid,
                    'childPhoto' => CommonUtils::childPhotoUrl($childModel->photo),
                    'childName' => $childModel->getChildName(),
                );
            }
            $stepList = array(
                0 => Yii::t('newDS', 'Waiting to fetch emails'),
                10 => Yii::t('newDS', 'Fetching parent emails'),
                20 => Yii::t('newDS', 'Waiting to send'),
                30 => Yii::t('newDS', 'Sending'),
                40 => Yii::t('newDS', 'Completed'),
            );
            $data = array(
                'id' => $res['data']["_id"],
                'stepList' => $stepList,
                'step' => $res['data']["step"],
                'totalNum' => $totalNum,
                'successNum' => $successNum,
                'redundantNum' => $redundantNum,
                'noEmailList' => $noEmailList,
                'errorList' => $errorList,
                'noEmailList' => $noEmailList,
                'childInfo' => $childInfo,
            );
            $this->addMessage("state", "success");
            $this->addMessage("message", "success");
            $this->addMessage("data", $data);
        } else {
            $this->addMessage("state", "fail");
            $this->addMessage("message", $res['msg']);
        }
        $this->showMessage();
    }

    public function actionComments()
    {
        $id = Yii::app()->request->getPost('id');
        if (!$id) {
            $this->addMessage("message", "ID 不能为空");
            $this->showMessage();
        }

        // // 当前页码
        // $pageNum = Yii::app()->request->getParam('pageNum', 1);
        // $pageSize = Yii::app()->request->getParam('pageSize', 20);
        // // 计算分页
        // $pages = 1;
        // $pagination  = array(
        //     'pages' => $pages,
        //     'pageNum' => $pageNum,
        //     'pageSize' => $pageSize,
        //     'total' => 1
        // );
        $data = array(
            "schoolId" => $this->branchId,
            "linkType" => 'notice'
        );

        $res = CommonUtils::requestDsOnline("notice/comments/" . $id, $data);
        if (isset($res['code'])  && $res['code'] == 0) {
            // 获取孩子，家长，班级的信息
            $resData = $res['data'];
            $childInfo = array();
            $classInfo = array();
            $staffInfoList = array();
            $commentList = array();
            $attachmentList = array();
            if ($resData) {
                $staffInfoList = $resData['staffInfoList'];
                $childIdList = $resData['childIdList'];
                $classIdList = $resData['classIdList'];
                $commentList = $resData['commentList'];
                $attachmentList = $resData['attachmentList'];

                $childModels = ChildProfileBasic::model()->findAllByPk($childIdList);
                $classModels = IvyClass::model()->findAllByPk($classIdList);
                foreach ($childModels as $childModel) {
                    $childInfo[$childModel->childid] = array(
                        'childid' => $childModel->childid,
                        'childName' => $childModel->getChildName(),
                        'childPhoto' => CommonUtils::childPhotoUrl($childModel->photo),
                        'fid' => $childModel->fid,
                        'mid' => $childModel->mid,
                    );
                }
                foreach ($classModels as $classModel) {
                    $classInfo[$classModel->classid] = array(
                        'classid' => $classModel->classid,
                        'title' => $classModel->title,
                    );
                }
            }

            $this->addMessage("state", "success");
            $this->addMessage("message", "success");
            $this->addMessage("data", array(
                'comments' => $commentList,
                'attachmentList' => $attachmentList,
                'staffInfoList' => $staffInfoList,
                'childIdList' => $childIdList,
                'childInfo' => $childInfo,
                'classInfo' => $classInfo
                )
            );
        } else {
            $this->addMessage("state", "fail");
            $this->addMessage("message", $res['msg']);
        }
        $this->showMessage();
    }

    // 公告确认相关
    // 获取公告确认的配置
    public function actionGetConfirmConfig()
    {
        $id = Yii::app()->request->getParam('id');
        $this->addMessage('state', 'fail');
        if (!$id) {
            $this->addMessage('message', 'id not be null');
            $this->showMessage();
        }
        $res = CommonUtils::requestDsOnline("notice/confirmConfig/" . $id);
        if (isset($res['code'])  && $res['code'] == 0) {
            $this->addMessage("state", "success");
            $this->addMessage("message", "success");
            $this->addMessage("data", $res);
        } else {
            $this->addMessage("state", "fail");
            $this->addMessage("message", $res['msg']);
        }

        $this->showMessage();
    }

    // 新建更新公告确认的配置标题
    public function actionUpdateConfirmConfig()
    {
        $id = Yii::app()->request->getParam('id');
        $cn = Yii::app()->request->getParam('cn');
        $en = Yii::app()->request->getParam('en');
        $this->addMessage('state', 'fail');

        $data = array(
            'id' => $id,
            'cn' => $cn,
            'en' => $en,
        );
        $res = CommonUtils::requestDsOnline("notice/updateConfig/" . $id, $data);
        if (isset($res['code'])  && $res['code'] == 0) {
            $this->addMessage("state", "success");
            $this->addMessage("message", "success");
            $this->addMessage("data", $res);
        } else {
            $this->addMessage("state", "fail");
            $this->addMessage("message", $res['msg']);
        }
        $this->showMessage();
    }

    // 新建更新公告确认的配置选项
    public function actionUpdateConfirmConfigOption()
    {
        $id = Yii::app()->request->getParam('id');
        $optionId = Yii::app()->request->getParam('optionId');
        $cn = Yii::app()->request->getParam('cn');
        $en = Yii::app()->request->getParam('en');
        $this->addMessage('state', 'fail');

        if (!$id) {
            $this->addMessage('message', 'id not be null');
            $this->showMessage();
        }
        if (!$cn) {
            $this->addMessage('message', 'cn not be null');
            $this->showMessage();
        }
        if (!$en) {
            $this->addMessage('message', 'en not be null');
            $this->showMessage();
        }
        $data = array(
            'optionId' => $optionId,
            'cn' => $cn,
            'en' => $en,
        );
        $res = CommonUtils::requestDsOnline("notice/updateConfigOption/" . $id, $data);
        if (isset($res['code'])  && $res['code'] == 0) {
            $this->addMessage("state", "success");
            $this->addMessage("message", "success");
            $this->addMessage("data", $res);
        } else {
            $this->addMessage("state", "fail");
            $this->addMessage("message", $res['msg']);
        }
        $this->showMessage();
    }

    // 排序公告确认的配置选项
    public function actionSortConfirmConfigOption()
    {
        $id = Yii::app()->request->getParam('id');
        $sort = Yii::app()->request->getParam('sort');
        $this->addMessage('state', 'fail');
        if (!$id) {
            $this->addMessage('message', 'id not be null');
            $this->showMessage();
        }
        if (!is_array($sort) || !$sort) {
            $this->addMessage('message', 'sort not be null');
            $this->showMessage();
        }
        $res = CommonUtils::requestDsOnline("notice/sortConfirmConfigOption/" . $id, array('sort' => $sort));
        if (isset($res['code'])  && $res['code'] == 0) {
            $this->addMessage("state", "success");
            $this->addMessage("message", "success");
            $this->addMessage("data", $res);
        } else {
            $this->addMessage("state", "fail");
            $this->addMessage("message", $res['msg']);
        }

        $this->showMessage();
    }

    // 删除公告确认的配置的选项
    public function actionDeleteConfirmConfigOption()
    {
        $id = Yii::app()->request->getParam('id');
        $optionId = Yii::app()->request->getParam('optionId');
        $this->addMessage('state', 'fail');
        if (!$id) {
            $this->addMessage('message', 'id not be null');
            $this->showMessage();
        }
        if (!$optionId) {
            $this->addMessage('message', 'optionId not be null');
            $this->showMessage();
        }
        $res = CommonUtils::requestDsOnline("notice/deleteConfirmConfigOption/" . $id, array('optionId' => $optionId));
        if (isset($res['code'])  && $res['code'] == 0) {
            $this->addMessage("state", "success");
            $this->addMessage("message", "success");
            $this->addMessage("data", $res);
        } else {
            $this->addMessage("state", "fail");
            $this->addMessage("message", $res['msg']);
        }

        $this->showMessage();
    }

    // 获取公告确认的统计
    public function actionConfirmOverview()
    {
        $id = Yii::app()->request->getParam('id');
        $this->addMessage('state', 'fail');

        if (!$id) {
            $this->addMessage('message', 'id not be null');
            $this->showMessage();
        }
        $res = CommonUtils::requestDsOnline("notice/getConfirmOverview/" . $id);
        if (isset($res['code']) && $res['code'] == 0) {
            $this->addMessage("state", "success");
            $this->addMessage("message", "success");
            $this->addMessage("data", $res['data']);
        } else {
            $this->addMessage("state", "fail");
            $this->addMessage("message", $res['msg']);
        }
        $this->showMessage();
    }

    /**
     * 获取公告的发布信息
     *
     * @return void
     */
    public function actionPublishInfo()
    {
        $id = Yii::app()->request->getParam('id');
        $this->addMessage('state', 'fail');

        if (!$id) {
            $this->addMessage('message', 'id not be null');
            $this->showMessage();
        }
        $res = CommonUtils::requestDsOnline("notice/publishInfo/" . $id);
        if (isset($res['code']) && $res['code'] == 0) {
            $res['data']['stepList'] = array(
                -1 => Yii::t('newDS', '没有发送任务'),
                0 => Yii::t('newDS', 'Waiting to fetch emails'),
                10 => Yii::t('newDS', 'Fetching parent emails'),
                20 => Yii::t('newDS', 'Waiting to send'),
                30 => Yii::t('newDS', 'Sending'),
                40 => Yii::t('newDS', 'Completed'),
            );
            $this->addMessage("state", "success");
            $this->addMessage("message", "success");
            $this->addMessage("data", $res['data']);
        } else {
            $this->addMessage("state", "fail");
            $this->addMessage("message", $res['msg']);
        }
        $this->showMessage();
    }

    /**
     * 公告发布
     *
     * @return void
     */
    public function actionPublish()
    {
        $id = Yii::app()->request->getParam('id');
        $publish_type = Yii::app()->request->getParam('publish_type');
        $publish_at = Yii::app()->request->getParam('publish_at');
        $expired_at = Yii::app()->request->getParam('expired_at');
        $require_emailing = Yii::app()->request->getParam('require_emailing');
        $clean_email = Yii::app()->request->getParam('clean_email', 0);
        $this->addMessage('state', 'fail');

        if (!$id) {
            $this->addMessage('message', 'id not be null');
            $this->showMessage();
        }
        $data = array(
            'publish_type' => $publish_type,
            'publish_at' => $publish_at,
            'expired_at' => $expired_at,
            'require_emailing' => $require_emailing,
            'clean_email' => $clean_email,
        );
        $res = CommonUtils::requestDsOnline("notice/publishNotice/" . $id, $data);
        if (isset($res['code']) && $res['code'] == 0) {
            $this->addMessage("state", "success");
            $this->addMessage("message", "success");
            $this->addMessage("data", $res['data']);
        } else {
            $this->addMessage("state", "fail");
            $this->addMessage("message", $res['msg']);
        }
        $this->showMessage();
    }


    /**
     * 公告发布
     *
     * @return void
     */
    public function actionSaveSurvey()
    {
        $id = Yii::app()->request->getParam('id');
        $sureyId = Yii::app()->request->getParam('survey_id');

        $this->addMessage('state', 'fail');

        if (!$id) {
            $this->addMessage('message', 'id not be null');
            $this->showMessage();
        }
        $data = array(
            'survey_id' => $sureyId
        );
        $res = CommonUtils::requestDsOnline("notice/saveSurvey/" . $id, $data);
        if (isset($res['code']) && $res['code'] == 0) {
            $this->addMessage("state", "success");
            $this->addMessage("message", "success");
            $this->addMessage("data", $res['data']);
        } else {
            $this->addMessage("state", "fail");
            $this->addMessage("message", $res['msg']);
        }
        $this->showMessage();
    }

    /**
     * 重置孩子的问卷
     *
     * @return void
     */
    public function actionResetSurvey()
    {
        $id = Yii::app()->request->getParam('id');
        $childId = Yii::app()->request->getParam('child_id');

        $this->addMessage('state', 'fail');

        if (!$id) {
            $this->addMessage('message', 'id not be null');
            $this->showMessage();
        }
        if (!$childId) {
            $this->addMessage('message', 'child id not be null');
            $this->showMessage();
        }
        $data = array(
            'child_id' => $childId
        );
        $res = CommonUtils::requestDsOnline("notice/resetSurvey/" . $id, $data);
        if (isset($res['code']) && $res['code'] == 0) {
            $this->addMessage("state", "success");
            $this->addMessage("message", "success");
            $this->addMessage("data", $res['data']);
        } else {
            $this->addMessage("state", "fail");
            $this->addMessage("message", $res['msg']);
        }
        $this->showMessage();
    }

    /**
     * 公告-问卷结果
     */
    public function actionGetSurveyResult()
    {
        $sid = Yii::app()->request->getParam('sid');
        $link_id = Yii::app()->request->getParam('id');
        $question_id = Yii::app()->request->getParam('question_id');
        $grade = Yii::app()->request->getParam('grade');
        $res = CommonUtils::requestDsOnline("notice/getSurveyResult/" ,array(
            'sid'=>$sid,
            'link_id'=>$link_id,
            'question_id'=>$question_id,
            'grade'=>$grade,
        ));
        if (isset($res['code']) && $res['code'] == 0) {
            $this->addMessage("state", "success");
            $this->addMessage("message", "success");
            $this->addMessage("data", $res['data']);
        } else {
            $this->addMessage("state", "fail");
            $this->addMessage("message", $res['msg']);
        }
        $this->showMessage();
    }

    /**
     * 公告-导出问卷结果
     */
    public function actionGetSurveyResultExport()
    {
        $sid = Yii::app()->request->getParam('sid');
        $link_id = Yii::app()->request->getParam('id');
        $res = CommonUtils::requestDsOnline("notice/getSurveyResultExport" ,array(
            'sid'=>$sid,
            'link_id'=>$link_id,
        ));
        if (isset($res['code']) && $res['code'] == 0) {
            $this->addMessage("state", "success");
            $this->addMessage("message", "success");
            $this->addMessage("data", $res['data']);
        } else {
            $this->addMessage("state", "fail");
            $this->addMessage("message", $res['msg']);
        }
        $this->showMessage();
    }

    public function actionGetSurveyResultQuestions()
    {
        $sid = Yii::app()->request->getParam('sid');
        $link_id = Yii::app()->request->getParam('id');
        $res = CommonUtils::requestDsOnline("notice/getSurveyResultQuestions/" ,array(
            'sid'=>$sid,
            'link_id'=>$link_id,
        ));
        if (isset($res['code']) && $res['code'] == 0) {
            $this->addMessage("state", "success");
            $this->addMessage("message", "success");
            $this->addMessage("data", $res['data']);
        } else {
            $this->addMessage("state", "fail");
            $this->addMessage("message", $res['msg']);
        }
        $this->showMessage();
    }

    public function actionSurveyExport()
    {
        $id = Yii::app()->request->getParam('id');
        $res = CommonUtils::requestDsOnline('notice/getConfirmExportData/'.$id);
        if (!isset($res['code']) || !$res['code'] == 0) {
            $this->addMessage("state", "fail");
            $this->addMessage("message", $res['msg']);
            $this->showMessage();
        }

        $responseData = $res['data']['responseData'];
        $childIdList = $res['data']['childIdList'];
        $items = ChildProfileBasic::model()->findAllByPk($childIdList);

        $fid = array();
        $mid = array();
        foreach ($items as $item) {
            $fid[$item->fid] = $item->fid;
            $mid[$item->mid] = $item->mid;
        }

        $criteria = new CDbCriteria();
        $criteria->compare('uid', $fid);
        $criteria->index = 'uid';
        $fUsers = User::model()->findAll($criteria);

        $criteria = new CDbCriteria();
        $criteria->compare('uid', $mid);
        $criteria->index = 'uid';
        $mUsers = User::model()->findAll($criteria);


        $ret = array();
        foreach ($items as $item) {
            $fObj = $fUsers[$item->fid]->parent;
            $mObj = $mUsers[$item->mid]->parent;
            $ret[] = array(
                'id' => $item->childid,
                'name' => $item->getChildName(true, true),
                'className' => $item->ivyclass->title,
                'fPhone' => $fObj->mphone,
                'fEmail' => $fUsers[$item->fid]->email,
                'mPhone' => $mObj->mphone,
                'mEmail' => $mUsers[$item->mid]->email,
                'option' => $responseData[$item->childid]['option'],
                'memo' => $responseData[$item->childid]['memo'],
            );
        }
        echo json_encode($ret);
    }

    /**
     * 获取调查问卷列表
     *
     * @return void
     */
    public function actionSurveyList()
    {
        $res = CommonUtils::requestDsOnline("notice/survey/list");
        if (isset($res['code']) && $res['code'] == 0) {
            $this->addMessage("state", "success");
            $this->addMessage("message", "success");
            $this->addMessage("data", $res['data']);
        } else {
            $this->addMessage("state", "fail");
            $this->addMessage("message", $res['msg']);
        }
        $this->showMessage();
    }

    /**
     * 获取下学年新生返校生数量
     *
     * @return void
     */
    public function actionGetChildMarkNum()
    {
        $this->getCalendars();
        $yid = $this->calendarYids['nextYid'];
        $res = CommonUtils::requestDsOnline("notice/getChildMarkNumByGrade", array('school_id' => $this->branchId, 'yid' => $yid));
        if (isset($res['code']) && $res['code'] == 0) {
            $this->addMessage("state", "success");
            $this->addMessage("message", "success");
            $this->addMessage("data", $res['data']);
        } else {
            $this->addMessage("state", "fail");
            $this->addMessage("message", $res['msg']);
        }
        $this->showMessage();
    }


    // ******************************************************** 常用方法 ********************************************************

    public function getConfig()
    {
        $calendarYids = $this->getCalendars();
        $startyear = $this->calendarStartYear[$calendarYids['currentYid']];
        $showNext = false;
        if ($startyear != date('Y') && in_array(date('m'), array(5, 6, 7, 8))) {
            $showNext = true;
        }
        $markList = $this->getMarkList($this->branchId);
        return array(
            'gradeGroupList' => IvyClass::getGradeGroupListBySchoolId($this->branchId),
            'showNext' => $showNext,
            'markList' => $markList,
            'levelList' => array(
                4 => Yii::t('newDS', 'What\'s Happening'),
                3 => Yii::t('newDS', 'Normal'),
                2 => Yii::t('newDS', 'Important'),
                1 => Yii::t('newDS', 'Urgent')
            ),
            'publishTypeList' => array(
                0 => Yii::t('newDS', 'Make offline'),
                10 => Yii::t('newDS', 'Publish at (also as archive time from parent view)'),
                // 20 => Yii::t('newDS', 'Schedule an online time')
            ),
            'requireResponseList' => array(
                0 => Yii::t('newDS', 'No confirmation needed'),
                1 => Yii::t('newDS', 'Simple confirmation (no selection needed)'),
                2 => Yii::t('newDS', 'Selectable confirmation (single selection)'),
                3 => Yii::t('newDS', 'Survey Confirmation (multiple questions)'),
            ),
            'gradeList' => $this->getGradeList($this->branchId),
            'startYear' => $startyear
        );
    }

    public function getMarkList($schoolId)
    {
        if ($schoolId == 'BJ_DS') {
             return array(
                '3' => Yii::t('newDS', 'ES New Students'),
                '4' => Yii::t('newDS', 'SS New Students'),
                '5' => Yii::t('newDS', 'ES Returning Students'),
                '6' => Yii::t('newDS', 'SS Returning Students'),
            );
        } else {
             return array(
                '1' => Yii::t('newDS', 'New Students'),
                '2' => Yii::t('newDS', 'Returning Students'),
            );
        }
    }

    public function getGroupStudentInfo($schoolId, $yid)
    {
        $gradeGroup = IvyClass::getGradeGroupListBySchoolId($this->branchId);
        return IvyClass::getChildInfoByGroup($schoolId, $yid, array_keys($gradeGroup));
    }

    /**
     * 根据分页跟筛选条件，获取列表数量
     *
     * @param array $filter 筛选条件
     * @return array
     */
    public function getListCount($filter)
    {
        $res = CommonUtils::requestDsOnline('notice/count', array(
            'filter' => $filter,
        ));

        if (isset($res['code'])  && $res['code'] == 0) {
            return $res['data'];
        } else {
            return array();
        }
    }

    /**
     * 根据分页跟筛选条件，获取列表
     *
     * @param int $pageNum 当前页码
     * @param int $pageSize 每页数量
     * @param array $filter 筛选条件
     * @return array
     */
    public function getList($pageNum, $pageSize, $filter)
    {
        $skip = ($pageNum - 1) * $pageSize;
        $take = $pageSize;

        $res = CommonUtils::requestDsOnline('notice/list', array(
            'skip' => (int)$skip,
            'take' => (int)$take,
            'filter' => $filter,
            'orderBy' => array('publish_at', 'DESC'),
        ));

        if (isset($res['code'])  && $res['code'] == 0) {
            return $res['data'];
        } else {
            return array();
        }
    }

    /**
     * 根据ID获取单个 notice 的数据
     *
     * @param [type] $id notice ID
     * @return void
     */
    public function getOne($id)
    {
        $res = CommonUtils::requestDsOnline('notice/' . $id);
        if (isset($res['code'])  && $res['code'] == 0) {
            return $res['data'];
        } else {
            return array();
        }
    }

    public function getOneForEdit($id, $schoolid)
    {
        $res = CommonUtils::requestDsOnline('notice/' . $id . '/' . $schoolid . '/' . Yii::app()->user->id);
        if (isset($res['code'])  && $res['code'] == 0) {
            return $res['data'];
        } else {
            return array();
        }
    }

    public function getResponseData($schoolId, $noticeIdList)
    {
        $data = array("schoolId" => $schoolId, "noticeIdList" => $noticeIdList);
        $res = CommonUtils::requestDsOnline('notice/responseData', $data);
        if (isset($res['code'])  && $res['code'] == 0) {
            return $res['data'];
        } else {
            return array();
        }
    }

    /**
     * 根据用户ID获取名称，照片等信息
     *
     * @param array $userIdList 用户ID
     * @return array
     */
    public function getUserInfo($userIdList)
    {
        $userModels = User::model()->findAllByPk($userIdList);
        $userInfoList = array();
        foreach ($userModels as $userModel) {
            $staffPhoto = empty($userModel->staffInfo->staff_photo) ? "blank.jpg" : $userModel->staffInfo->staff_photo;
            $userInfoList[] = array(
                'uid' => $userModel->uid,
                'name' => $userModel->getName(),
                'photo' => OA::CreateOAUploadUrl('infopub/staff', $staffPhoto),
                'level' => $userModel->level,
            );
        }
        return $userInfoList;
    }

    public function getGradeList($schoolId)
    {
        $classTypeList = array_merge(IvyClass::getClassTypes(false, Branch::TYPE_CAMPUS_ELEMENTARY_SCHOOL), IvyClass::getClassTypes(false, Branch::TYPE_CAMPUS_MI_PRESCHOOL));
        $config = array(
            'IVYKG' => array(
                'key' => 'IVYKG',
                'value' => Yii::t("newDS", "KG"),
                'item' => array(
                    array(
                        'key' => 'c',
                        'value' => 'CaRousel '
                    ),
                    array(
                        'key' => 'n',
                        'value' => 'Nursery '
                    ),
                    array(
                        'key' => 'b',
                        'value' => 'Pre-K1'
                    ),
                    array(
                        'key' => 'p',
                        'value' => 'Pre-K2'
                    ),
                    array(
                        'key' => 'k',
                        'value' => 'Kindergarten'
                    ),
                )
            ),
            'KG' => array(
                'key' => 'KG',
                'value' => Yii::t("newDS", "KG"),
                'item' => array(
                    array(
                        'key' => 'n',
                        'value' => 'Nursery '
                    ),
                    array(
                        'key' => 'mc',
                        'value' => 'Casa'
                    ),
                    array(
                        'key' => 'mr',
                        'value' => 'Reggio'
                    ),
                )
            ),
            'ES' => array(
                'key' => 'ES',
                'value' => Yii::t("newDS", "ES"),
                'item' => array(
                    array(
                        'key' => 'mk',
                        'value' => $classTypeList['mk']
                    ),
                    array(
                        'key' => 'e1',
                        'value' => $classTypeList['e1']
                    ),
                    array(
                        'key' => 'e2',
                        'value' => $classTypeList['e2']
                    ),
                    array(
                        'key' => 'e3',
                        'value' => $classTypeList['e3']
                    ),
                    array(
                        'key' => 'e4',
                        'value' => $classTypeList['e4']
                    ),
                    array(
                        'key' => 'e5',
                        'value' => $classTypeList['e5']
                    ),
                )
            ),
            'MS' => array(
                'key' => 'MS',
                'value' => Yii::t("newDS", "MS"),
                'item' => array(
                    array(
                        'key' => 'e6',
                        'value' => $classTypeList['e6']
                    ),
                    array(
                        'key' => 'e7',
                        'value' => $classTypeList['e7']
                    ),
                    array(
                        'key' => 'e8',
                        'value' => $classTypeList['e8']
                    )
                )
            ),
            'HS' => array(
                'key' => 'HS',
                'value' => Yii::t("newDS", "HS"),
                'item' => array(
                    array(
                        'key' => 'e9',
                        'value' => $classTypeList['e9']
                    ),
                    array(
                        'key' => 'e10',
                        'value' => $classTypeList['e10']
                    ),
                    array(
                        'key' => 'e11',
                        'value' => $classTypeList['e11']
                    ),
                    array(
                        'key' => 'e12',
                        'value' => $classTypeList['e12']
                    ),
                )
            ),
        );

        $gradeGroup = IvyClass::getGradeGroupListBySchoolId($schoolId);
        $gradeList = array();
        foreach ($gradeGroup as $key => $value) {
            $gradeList[] = $config[$key];
        }
        return $gradeList;
    }

    public function getUid()
    {
        $staff = Yii::app()->request->getParam('staff', 0);

        $uid = Yii::app()->user->getId();
        if ($staff > 0 && $this->checkViewTeachersAccess()) {
            $uid = $staff;
        }
        return $uid;
    }

    // 检查是否具有查看其他老师 journal 的权限
    public function checkViewTeachersAccess()
    {
        if (Yii::app()->user->checkAccess('ivystaff_it')) {
            return true;
        }
        if (Yii::app()->user->checkAccess('ivystaff_cd')) {
            return true;
        }
        return false;
    }

    // 获取所有的教学老师
    public function getCampusTeachers()
    {
        $teacherids = OA::getCampusTeachers($this->branchId);

        $crit = new CDbCriteria();
        $crit->select = "uid";
        $crit->compare('schoolid', $this->branchId);
        $crit->compare('type', 'edu');
        $teachers_edu = AdmBranchLink::model()->findAll($crit);
        $teacher_edu = array();
        if ($teachers_edu) {
            foreach ($teachers_edu as $_teache) {
                $teacher_edu[] = $_teache->uid;
            }
            $teacherids = array_merge($teacherids, $teacher_edu);
        }

        $teachers = array();
        if ($teacherids) {
            $crit = new CDbCriteria();
            $crit->compare('t.uid', $teacherids);
            $crit->index = 'uid';
            $teachers = User::model()->with(array('profile', 'staffInfo'))->findAll($crit);
        }

        return $teachers;
    }

    public function remote($requestUrl, $requestData = array())
    {
        $requestData['schoolId'] = $this->branchId;
        $res = CommonUtils::requestDsOnline($requestUrl, $requestData);
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('message', $res['msg']);
            $this->addMessage('data', $res['data']);
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->addMessage('data', $res['data']);
        }
        $this->showMessage();
    }
}
