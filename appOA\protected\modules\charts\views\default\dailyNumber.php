<div class="container-fluid" id="app"></div>

<script type="text/babel">
    const data = <?php echo json_encode($data);?>;

    let type = '<?php echo isset($_GET['type']) ? $_GET['type'] : 'ivy';?>';

    function DatePicker() {
        const inputRef = React.useRef(null);

        React.useEffect(() => {
            $(inputRef.current).datepicker({
                dateFormat: 'yy-mm-dd',
                maxDate: 0,
                onSelect: function (date) {
                    location.href = "/charts/default/dashboard?targetDate="+date+'&type='+type;
                }
            });

            return () => {
                $(inputRef.current).datePicker('destroy');
            }
        }, []);

        return (
            <div className="col-md-12 mb15">
                <input
                    type="text"
                    className="form-control"
                    id="targetDate"
                    style={{width: 180}}
                    ref={inputRef}
                    value="<?php echo isset($_GET['targetDate']) && $_GET['targetDate'] ? $_GET['targetDate'] : date('Y-m-d');?>"
                />
            </div>
        );
    }

    function Tabs() {
        const [selected, setSelected] = React.useState(type);

        const handleClick = (key) => {
            setSelected(key)
            type = key
        }

        const listItems = Object.entries(data.data.config).map(([key, value]) => (
            <TabItem
                k={key}
                title={value.title}
                isActive={selected === key}
                onClick={ handleClick }
            />
        ));

        return (
            <div>
                <div className="col-md-12 mb16">
                    <ul className="tabs-01">{listItems}</ul>
                </div>
                <div className="col-md-12">
                    <ShowTotal slug={selected}/>
                </div>
                <div className="col-md-12 mb16">
                    <BranchTable slug={selected}/>
                </div>
            </div>
        );
    }

    function TabItem({k, title, isActive, onClick}) {
        const handleClick = () => {
            if (onClick) {
                onClick(k);
            }
        };

        if (isActive) {
            return <li className="active">{title}</li>;
        }
        return <li onClick={handleClick}>{title}</li>;
    }

    function ShowTotal({slug}) {
        const _data = data.data;

        const targetMonth = _data.targetDate.replace('-', '').substring(0, 6)
        const items = _data['config'][slug]

        let _target = []
        items.items.map( (item) => {
            if (typeof _data['target'][item] != "undefined") {
                _target.push(_data['target'][item][targetMonth])
            }
        } )

        const target =  _target.reduce((acc, num) => acc + num, 0);

        let _invoice = []
        items.items.map( (item) => {
            if (typeof _data['number'][item] != "undefined") {
                _invoice.push(_data['number'][item]['num1']+_data['number'][item]['num2'])
            }
        } )

        const invoice =  _invoice.reduce((acc, num) => acc + num, 0);

        let _preset = []
        items.items.map( (item) => {
            if (typeof _data['preset'][item] != "undefined") {
                _preset.push(_data['preset'][item])
            }
        } )

        const preset =  _preset.reduce((acc, num) => acc + num, 0);

        return (
            <div>
                <div className="box-01">
                    <TotalItem title="<?php echo Yii::t('dashboard', 'Target');?>" number={target} />
                    <TotalItem title="<?php echo Yii::t('dashboard', 'Invoices');?>" number={invoice} />
                    <TotalItem title="<?php echo Yii::t('dashboard', 'Attendance');?>" number={preset} />
                </div>
            </div>
        )
    }

    function TotalItem({title, number}) {
        return (
            <div className="box-02">
                <span>{ title }</span>
                <span className="num-01">{ number }</span>
            </div>
        );
    }

    function BranchTable({slug}) {
        const _data = data.data;

        const branchItems = _data.config[slug]['items'].map(item => (
            <BranchItem
                branch={ item }
            />
        ));

        return (
            <div className="table-container">
                <table className="table table-bordered table-school">
                    <thead>
                    <tr>
                        <th className="text-center sticky-column" style={{width: 88}}><?php echo Yii::t('labels', 'Campus');?></th>
                        <th className="text-center" style={{width: 434}}>
                            <?php echo Yii::t('dashboard', 'Target');?> / <?php echo Yii::t('dashboard', 'Invoices');?> / <?php echo Yii::t('dashboard', 'Attendance');?>
                        </th>
                        <th className="text-center"><?php echo Yii::t('dashboard', 'Trend (5 weeks)');?></th>
                        <th className="text-center" style={{width: 358}}><?php echo Yii::t('dashboard', 'Upcoming');?></th>
                    </tr>
                    </thead>
                    <tbody>
                    {branchItems}
                    </tbody>
                </table>
            </div>
        );
    }

    function BranchItem({branch}) {
        const _data = data.data;
        const targetMonth = _data.targetDate.replace('-', '').substring(0, 6)
        const target = typeof _data['target'][branch] != "undefined" ? _data['target'][branch][targetMonth] : 0
        const invoice = typeof _data['number'][branch] != "undefined" ? _data['number'][branch]['num1']+_data['number'][branch]['num2'] : 0
        const unpaidInvoice = typeof _data['number'][branch] != "undefined" ? _data['number'][branch]['num2'] : 0
        const preset = typeof _data['preset'][branch] != "undefined" ? _data['preset'][branch] : 0
        const link = `/charts/default/data?schoolid=${branch}`;
        const link2 = `/mcampus/childsign/report?branchId=${branch}&date=${targetMonth}`;

        return (
            <tr>
                <td valign="middle" align="center" className="sticky-column">
                    <div className="text-01">
                        <a href={link}>{_data['branch'][branch]}</a>
                    </div>
                </td>
                <td>
                    <div className="box-warp">
                        <BoxNumber title="<?php echo Yii::t('dashboard', 'Target');?>" number={target} />
                        <BoxNumber title="<?php echo Yii::t('dashboard', 'Invoices');?>" number={invoice} number2={unpaidInvoice} />
                        <BoxNumber title="<?php echo Yii::t('dashboard', 'Attendance');?>" number={preset} link2={link2} />
                    </div>
                </td>
                <td>
                    <SubTable branch={branch} />
                </td>
                <td>
                    <ThisYear branch={branch} />
                    <NextYear branch={branch} />
                </td>
            </tr>
        )
    }

    function BoxNumber({title, number, number2, link2}) {
        if (typeof number2 != "undefined") {
            return (
                <div className="box-03">
                    <div className="box-03-title">{title}</div>
                    <div className="box-03-num">
                        {number}
                    </div>
                    <div className="box-03-sub-num">
                        {number2 > 0 && (
                            <span><?php echo Yii::t('dashboard', 'Unpaid');?> {number2}</span>
                        )}
                    </div>
                </div>
            );
        }
        return (
            <div className="box-03">
                <div className="box-03-title">{title}</div>
                <div className="box-03-num">
                    {link2 ? (<a target="_blank" href={link2}>{number}</a>) : number}
                </div>
            </div>
        );
    }

    function SubTable({branch}) {
        const _data = data.data;

        const { headItems, bodyInvoice, bodyTarget, bodyLastYear } = Object.entries(data.data.lastWeek).reduce((acc, [key, value]) => {
            const displayValue = value.substring(5);
            acc.headItems.push(
                <th key={key} className="text-center" style={{width: '15%'}}>{displayValue}</th>
            );

            const num1 = typeof _data['history'][branch][key] != 'undefined' ? _data['history'][branch][key]['num1'] : 0;
            const num2 = typeof _data['history'][branch][key] != 'undefined' ? _data['history'][branch][key]['num2'] : 0;
            if (num2 > 0) {
                acc.bodyInvoice.push(
                    <td key={key} className="text-center">
                        {num1+num2} / <span style={{color: '#D9534F'}}>{num2}</span>
                    </td>
                );
            } else {
                acc.bodyInvoice.push(
                    <td key={key} className="text-center">
                        {num1}
                    </td>
                );
            }

            const theMonth = value.replace('-', '').substring(0, 6)
            const targetNum = typeof _data['target'][branch] != 'undefined' ? _data['target'][branch][theMonth] : 0;
            acc.bodyTarget.push(
                <td key={key} className="text-center">{targetNum}</td>
            );


            const lastYearKey = _data['lastYearWeek'][key];
            const lastYearNum = typeof _data['history'][branch][lastYearKey] != 'undefined' ? _data['history'][branch][lastYearKey] : 0;
            acc.bodyLastYear.push(
                <td key={key} className="text-center">{lastYearNum.num1+lastYearNum.num2}</td>
            );
            return acc;
        }, { headItems: [], bodyInvoice: [], bodyTarget: [], bodyLastYear: [] });

        return (
            <div>
                <table className="table table-bordered sub-table">
                    <thead>
                    <tr>
                        <th></th>
                        {headItems}
                    </tr>
                    </thead>
                    <tbody>
                    <HistoryNumber title="<?php echo Yii::t('dashboard', 'Invoices');?>" subTitle="<?php echo Yii::t('dashboard', 'Unpaid');?>" bodyItems={bodyInvoice} />
                    <HistoryNumber title="<?php echo Yii::t('dashboard', 'Target');?>" bodyItems={bodyTarget} />
                    <HistoryNumber title="<?php echo Yii::t('dashboard', 'SPLY');?>" bodyItems={bodyLastYear} />
                    </tbody>
                </table>
            </div>
        );
    }

    function HistoryNumber({title, subTitle, bodyItems}) {
        if (subTitle) {
            return (
                <tr>
                    <td>{title} / <span style={{color: '#D9534F'}}>{subTitle}</span></td>
                    {bodyItems}
                </tr>
            );
        }
        return (
            <tr>
                <td>{title}</td>
                {bodyItems}
            </tr>
        );
    }

    function ThisYear({branch}) {
        const _ = data.data['number'][branch];

        const incomingPaid = _['num3'] + _['num6'] + _['num15'];
        const incomingUnpaid = _['num4'] + _['num7'] + _['num16'];
        const incoming = incomingPaid + incomingUnpaid;

        return (
            <div>
                <div className="box-04">
                    <div className="box-04-left">
                        <div className="font-01"><?php echo Yii::t('dashboard', 'This Y');?></div>
                        <div className="font-02">{incoming}</div>
                    </div>
                    <div className="box-04-right">
                        <?php echo Yii::t('dashboard', 'Paid');?><span style={{color: incomingPaid > 0 ? '#5CB85C' : ''}}>{incomingPaid}</span>
                        <?php echo Yii::t('dashboard', 'Unpaid');?><span style={{color: incomingUnpaid > 0 ? '#D9534F' : ''}}>{incomingUnpaid}</span>
                    </div>
                </div>
            </div>
        );
    }

    function NextYear({branch}) {
        const _ = data.data['number'][branch];

        const ArrayUtils = {
            intersection: (a, b) => [...new Set(a)].filter(x => new Set(b).has(x)),
            difference: (a, b) => a.filter(x => !new Set(b).has(x))
        };

        const inChild = ((_['childids1'] + ',' + _['childids2']) || '').split(',');
        const nextIncomePaid = ArrayUtils.intersection(ArrayUtils.difference((_['childids10'] || '').split(','), (_['childids17'] || '').split(',')), inChild).length;
        const nextIncomeUnpaid = ArrayUtils.intersection(ArrayUtils.difference((_['childids11'] || '').split(','), (_['childids17'] || '').split(',')), inChild).length;

        const inChild2 = ((_['childids1'] + ',' + _['childids2'] + ',' + _['childids17']) || '').split(',');
        const nextIncomeNewPaid = ArrayUtils.difference((_['childids10'] || '').split(','), inChild2).length;
        const nextIncomeNewUnpaid = ArrayUtils.difference((_['childids11'] || '').split(','), inChild2).length;

        return (
            <div>
                <div className="box-05">
                    <div className="box-05-left">
                        <div className="font-01"><?php echo Yii::t('dashboard', 'Next Y');?></div>
                        <div className="font-02">{nextIncomePaid+nextIncomeUnpaid+nextIncomeNewPaid+nextIncomeNewUnpaid}</div>
                    </div>
                    <div className="box-05-right">
                        <div className="box-05-right-00 box-05-right-01">
                            <div className="box-split">
                                <div className="font-01 mb5"><?php echo Yii::t('dashboard', 'Returning');?> {nextIncomePaid+nextIncomeUnpaid}</div>
                                <div>
                                    <?php echo Yii::t('dashboard', 'Paid');?><span style={{color: nextIncomePaid > 0 ? '#5CB85C' : ''}}>{nextIncomePaid}</span>
                                    <?php echo Yii::t('dashboard', 'Unpaid');?><span style={{color: nextIncomeUnpaid > 0 ? '#D9534F' : ''}}>{nextIncomeUnpaid}</span>
                                </div>
                            </div>
                        </div>
                        <div className="box-05-right-00 box-05-right-02">
                            <div className="font-01 mb5"><?php echo Yii::t('dashboard', 'New');?> {nextIncomeNewPaid+nextIncomeNewUnpaid}</div>
                            <div>
                                <?php echo Yii::t('dashboard', 'Paid');?><span style={{color: nextIncomeNewPaid > 0 ? '#5CB85C' : ''}}>{nextIncomeNewPaid}</span>
                                <?php echo Yii::t('dashboard', 'Unpaid');?><span style={{color: nextIncomeNewUnpaid > 0 ? '#D9534F' : ''}}>{nextIncomeNewUnpaid}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    function NextYearIncoming() {
        return (
            <div>
                <div className="box-05">
                    <div className="box-05-left">
                        <div className="font-01">下学年</div>
                        <div>开发中</div>
                    </div>
                    <div className="box-05-right">

                    </div>
                </div>
            </div>
        );
    }

    function App() {
        return (
            <div>
                <div className="row">
                    <DatePicker />
                </div>
                <div className="row">
                    <Tabs />
                </div>
            </div>
        );
    }

    ReactDOM.createRoot(document.getElementById("app")).render(<App />);
</script>

<style>
    .tabs-01 {
        list-style-type: none;
        font-size: 14px;
        margin: 0;
        padding: 0;
        border-bottom: 1px solid #D9D9D9;
    }
    .tabs-01 li {
        display: inline-block;
        list-style: none;
        margin-right: 32px;
        padding: 10px 8px;
        cursor: pointer;
    }
    .tabs-01 li.active {
        border-bottom: 2px solid #4D88D2;
    }
    .box-01 {
        display: flex;
        flex-wrap: wrap;
    }
    .box-02 {
        width: 160px;
        height: 46px;
        line-height: 46px;
        background: #F7F7F8;
        border-radius: 4px;
        padding: 0 20px;
        display: flex;
        justify-content: space-between;
        margin-right: 20px;
        margin-bottom: 16px;
    }
    .num-01 {
        font-weight: bold;
        font-size: 18px;
    }
    .box-warp {
        display: flex;
    }
    .box-03 {
        width: 131px;
        height: 138px;
        background: #F7F7F8;
        border-radius: 4px;
        margin-right: 12px;
        text-align: center;
    }
    .box-03:last-child {
        margin-right: 0;
    }
    .box-03 .box-03-title {
        margin-top: 16px;
        font-size: 14px;
    }
    .box-03 .box-03-num {
        margin-top: 22px;
        font-weight: bold;
        font-size: 16px;
    }
    .box-03 .box-03-sub-num {
        margin-top: 14px;
        font-size: 12px;
    }
    .box-03 .box-03-sub-num span {
        color: #D9534F;
    }
    .box-04 {
        width: 340px;
        height: 62px;
        background: #F7F7F8;
        border-radius: 4px;
        display: flex;
        margin-bottom: 12px;
    }
    .box-04 .box-04-left, .box-05 .box-05-left {
        width: 74px;
        background: #E5E6EB;
        border-radius: 4px 0px 0px 4px;
        text-align: center;
        padding-top: 5px;
    }
    .box-04 .box-04-right {
        line-height: 62px;
        text-align: center;
        width: 100%;
    }
    .box-04 .box-04-right span {
        margin-left: 8px;
        margin-right: 32px;
    }
    .box-05 {
        width: 340px;
        height: 64px;
        background: #F7F7F8;
        border-radius: 4px;
        display: flex;
    }
    .font-01 {
        font-size: 14px;
    }
    .font-02 {
        font-size: 16px;
        font-weight: bold;
    }
    .box-05-right {
        display: flex;
        width: 100%;
    }
    .box-05-right-00 {
        width: 50%;
        /*margin-left: 16px;*/
        /*margin-top: 8px;*/
        padding: 8px 12px;
    }
    .box-05-right-00 span {
        margin-left: 6px;
        margin-right: 6px;
    }
    .box-split {
        border-right: 1px solid #D9D9D9;
    }
    .table-school {
        width: 100%;
        min-width: 1354px;
        table-layout: fixed;
        border-collapse: collapse;
    }
    .sub-table {
        margin-bottom: 0;
    }
    .text-01 {
        font-weight: bold;
        height: 138px;
        line-height: 138px;
    }
    .table-container {
        overflow-x: auto;
        max-width: 100%;
    }
    .sticky-column {
        position: sticky;
        left: 0;
        background: white;
        z-index: 1;
        box-shadow: 2px 0 5px -2px rgba(0,0,0,0.1);
        border-left: 1px solid #ddd !important;
    }
    .sticky-column::before {
        content: '';
        border-left: 1px solid #ddd;
        position: absolute;
        top: 0;
        left: -1px;
        z-index: 9;
        height: 100%;
    }
    .sticky-column::after {
        content: '';
        position: absolute;
        right: -5px;
        top: 0;
        height: 100%;
        width: 5px;
        background: linear-gradient(to right, rgba(0,0,0,0.1), transparent);
    }
</style>
