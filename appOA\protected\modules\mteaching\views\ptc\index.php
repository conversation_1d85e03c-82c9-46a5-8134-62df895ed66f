<style>
     [v-cloak] {
        display: none;
    }
    .teacher{
        background: #FAFAFA;
        padding:16px 24px;
        margin-top:24px
    }
    .image{
        width:54px;
       height: 54px;
       border-radius: 8px;
       object-fit: cover;
    }
    .addImg{
        width: 54px;
       height: 54px;
       border-radius: 8px;
       object-fit: cover;
    }
    .font18{
        font-size:18px
    }
    .noStu{
        border:1px dashed #ddd;
        height:200px
    }
    .addStu{
        width:60px;
        height:60px;
        display: inline-block;
        background:#FAFAFA;
        border-radius:50%;
        text-align:center;
        line-height:60px;
        margin-top:50px;
        color:#4D88D2
    }
    .avatar{
        width:54px;
        height:54px;
        border-radius:50%;
        object-fit: cover;
    }
    .piont{
        width: 6px;
        height: 6px;
        background: #666666;
        border-radius: 50%;
        display:inline-block;
        margin-right:10px
    }
    .assessment{
        background: #FAFAFA;
        padding:16px;
        line-height: 20px;
    }
    .score tr td{
        vertical-align: middle !important
    }
    .colorc{
        color:#cccccc
    }
    .score tr th{
        text-align:center;
        padding:16px !important;
        background:#FAFAFA
    }
    .table tr th {
        position: sticky;
        top: 0;
        background: #FAFAFA;
        z-index:99
    }
    .max-height{
        overflow-y:auto
    }
    .subject{
        border-radius:4px;
        border:1px solid #E8E8E8;
        margin-bottom:16px;
        width:200px;
        float: left;
        margin-right: 20px;
    }
    .borderBro{
        border-bottom:1px solid #E8E8E8;
    }
    .subject .media{
        padding:9px;
        margin:0;
        line-height: 53px;
    }
    .font16{
        font-size:16px
    }
    .subjectShow{
        background:rgba(77, 136, 210, 0.1);
        border:1px solid #4D88D2
    }
    .myInterview{
        border:1px solid #ccc;
        border-radius:4px
    }
    .myInterview .borderLeft{
        border-left:1px solid #E5E7EB;
        padding:10px
    }
    .pt85{
        padding-top:85px
    }
    .loading{
        width:98%;
        height:95%;
        background:#fff;
        position: absolute;
        opacity: 0.5;
        z-index: 99
    }
    .loading span{
        width:100%;
        height:100%;
        display:block;
        background: url("<?php echo Yii::app()->theme->baseUrl?>/images/loading.gif")no-repeat center center ;
    }
    .tableBorder td{
        border-top:none !important
    }
    .subTop{
        top:25px
    }
    .yellow{
        color:#F0AD4E
    }
    .point{
        display:inline-block;
        width:6px;
        height:6px;
        background:#CCCCCC;
        border-radius:50%;
        margin-right:10px
    }
    .pointNum{
        display:inline-block;
        width:20px;
        height:20px;
        background:#777777;
        border-radius:50%;
        text-align:center;
        line-height:20px;
        color:#fff
    }
    .enbreak{
        text-align: justify;
        padding:10px;
        line-height:20px;
        word-wrap:break-word
    }
    .bg7{
        background-color: #777777 !important
    }
    .tab-center{
        text-align:center;
        vertical-align:middle;
        text-align:center;
        vertical-align:middle;
    }
     .el-select .el-input__inner{
         padding-right:0 !important;
     }
     .el-textarea__inner{
         padding-left:10px !important;
         padding-right:10px !important;
     }
     .file_name_t {
         background-color: rgba(119, 119, 119, 0.17);
         height: 30px;
         line-height: 32px;
         padding-left: 15px;
         margin: 0 10px;
         border-radius: 2px;
     }
     .loading_read{
         float: right;
         color: #428bca;
         margin-right: 25px;
     }
     .el-input__inner{
        padding: 0 10px !important;
     }
     .comment_div{
         margin: 16px 12px;
     }
     .pl-2{
         padding-left: 2px !important;
     }
     .pl-0{
        padding-left: 0 !important;
     }
     .pt-0{
         padding-top: 0 !important;
     }
     .w100{
         width: 100%;
     }
     .el-textarea__inner::-webkit-scrollbar {
         width: 3px;
     }
     .el-textarea__inner::-webkit-scrollbar-thumb {
         -webkit-box-shadow: inset 0 0 5px rgb(0 0 0 / 30%);
     }
     .pl24{
         padding-left: 24px;
     }
     .ml24{
         margin-left: 24px;
     }
     .mb0{
         margin-bottom: 0;
     }
     .el-scrollbar{
         max-width: 333px !important;
     }
</style>
<!--我的家长会 style-->
<style>
    .red{
        color:#D9534F
    }
    .classTitle{
        border:1px solid rgba(0, 0, 0, 0.15);
        background:rgba(0, 0, 0, 0.04);
        padding:4px 8px;
        display:inline-block
    }
    .table_wrap {
        width: 100%;
        overflow: auto;
    }
    .table_wrap .table {
        table-layout: fixed;
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
        border-left: 1px solid #DDDDDD;
        border-top: 1px solid #DDDDDD;
    }
    .table_wrap .table td,.table_wrap .table th {
        width: 250px;
        border-right: 1px solid #DDDDDD;
        border-bottom: 1px solid #DDDDDD;
        border-top:none !important;
        padding: 16px !important
    }
    /* 表头固定 */
    .table_wrap .table tr th {
        position: sticky;
        top: 0;
        background: #FAFAFA;
        z-index:1
    }
    .scrollbar::-webkit-scrollbar {
        /*滚动条整体样式*/
        width : 10px;  /*高宽分别对应横竖滚动条的尺寸*/
        height:10px;
    }
    .scrollbar::-webkit-scrollbar-thumb {
        border-radius   : 10px;
        background-color: skyblue;
        background-image: -webkit-linear-gradient(
                45deg,
                rgba(255, 255, 255, 0.2) 25%,
                transparent 25%,
                transparent 50%,
                rgba(255, 255, 255, 0.2) 50%,
                rgba(255, 255, 255, 0.2) 75%,
                transparent 75%,
                transparent
        );
    }
    .scrollbar::-webkit-scrollbar-track {
        /*滚动条里面轨道*/
        box-shadow   : inset 0 0 5px rgba(0, 0, 0, 0.2);
        border-radius: 10px;
        background   : #ededed;
    }
    .reserved{
        background: #F6F9FF;
        border-radius: 4px;
        padding:12px
    }
    .timeStart{
        border-bottom: 1px solid #E5E7EB;
    }
    .timeList{
        min-height:100px
    }
    .green{
        color:#1FA11A
    }
    .teacherImg{
        width:24px;
        height:24px;
        border-radius:50%;
        object-fit: cover;
    }
    .subTop{
        top:25px
    }
    .example{
        width: 25px;
        height: 25px;
    }
    .translate{
        position: absolute;
        width: 25px;
        height: 25px;
        bottom: -8px;
        left: 25px;
    }
    .font17{
        font-size:17px
    }
    .scoreTable{
        width:450px;
        margin-top:10px;
    }
    .scoreTable thead{
        background:#F2F3F5;
       
    }
    .scoreTable thead th{
       padding:4px 11px !important

    }
    .scoreTable tbody td{
       padding:4px 11px;
       font-size:14px
    }
    .scoreTable .subdomain{
        position: absolute;
        left: 8px;
    }
    .scoreTableLeft{
        border-radius: 50px 0 0 50px;
    }
    .scoreTableRight{
        border-radius: 0 50px 50px 0;
    }
    .text-left{
        text-align:left !important
    }
    .text-right{
        text-align:right !important
    }
    .tagScore{
        color: #4D88D2;
        padding:4px 6px;
        background: #F0F5FB;
        border-radius: 2px;
        display:inline-block;
        margin-right:8px;
        font-size: 12px;
        line-height: 12px
    }
    .tagScoreNull{
        color: #D9534F;
        padding:4px 6px;
        background: #FCF1F1;
        border-radius: 2px;
        display:inline-block;
        margin-right:8px;
        font-size: 12px;
        line-height: 12px
    }
    .el-checkbox__input.is-checked .el-checkbox__inner, .el-checkbox__input.is-indeterminate .el-checkbox__inner{
        background-color: #4D88D2;
        border-color:#4D88D2;
    }
    .mapTitle{
        background: rgba(77, 136, 210, 0.1);
        border-radius: 2px 2px 0px 0px;
        text-align: center;
        padding: 4px;
    }
    .mapScore{
        background: #F0F5FB;
        border-radius: 2px;
        text-align: left;
        color: #4D88D2;
        font-size: 12px;
        margin-top:12px
    }
    .p10{
        padding:10px
    }
    .mb2{
        margin-bottom:2px
    }
    .p024{
        padding:0 24px
    }
    .scroll-box{
        overflow-y:auto
    }
</style>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Teaching Tasks'), array('//mteaching/default/index')) ?></li>
        <li><?php echo Yii::t('newDS', 'PTC') ?></li>
    </ol>
    <div class="row" id='container' v-cloak>
        <div class='col-md-12 col-sm-12' v-if='initList.year_list'>
            <div class='flex'>
                <div class='form-inline flex1'>
                    <select class="form-control mr20" v-model='yid' @change='initDataF' :disabled="initDataDisabled">
                        <option v-for='(list,key,index) in initList.year_list.yearList' :value='key'>{{list}}</option>
                    </select>
                    <select class="form-control" v-model='semester'  @change='initDataF'  :disabled="initDataDisabled">
                        <option v-for='(list,key,index) in initList.semester.list' :value='key'>{{list}}</option>
                    </select>
                </div>
                <div class='myInterview font14 text-primary flex' v-if='initList.teacher_info'>
                    <div class='p5'>
                        <img style="width: 16px" class='mr5' src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/user.png' ?>" alt="">
                        <!-- <span class='color3 mr10'>{{initList.teacher_info.name}}</span>  -->
                        <a href="javascript:;" @click='myInterviews()' :class='ptc_sum==0?"text-muted":""'><?php echo Yii::t("ptc", "My PTC Students");?></a>
                        <span class="badge ml10 ">{{ptc_sum}}</span>
                    </div>

                </div>
                <button type="button" class="btn btn-default dropdown-toggle ml20 font14" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false"  @click='otherTeacher()'>
                    <?php echo Yii::t("ptc", "Other Teachers");?> <span class="caret ml5"></span>
                </button>
            </div>
            <div>
                <ul class="nav nav-tabs pt16">
                    <li role="presentation" :class="showTab=='PtcPreparation'?'active':''"  @click='showType("PtcPreparation")'>
                        <a href="#reserve" role="tab" data-toggle="tab">
                            <?php echo Yii::t("ptc", "PTC Preparation");?>
                        </a>
                    </li>
                    <li role="presentation" :class="showTab=='MyPTC'?'active':''"  @click='showType("MyPTC")'>
                        <a href="#time" role="tab" data-toggle="tab">
                            <?php echo Yii::t("ptc", "My Schedule");?>
                            <span class="badge" style="display: inline">{{ptc_sum}}</span>
                        </a>
                    </li>
                    <li v-if="showPtcSet==true" role="presentation" :class="showTab=='classPtcSet'?'active':''"  @click='showType("classPtcSet")' style="float: right">
                        <a href="#time" role="tab" data-toggle="tab">
                            <?php echo Yii::t("ptc", "Class PTC Settings");?>
                        </a>
                    </li>
                </ul>
            </div>
        </div>
        <template v-if="showTab == 'PtcPreparation'">
            <div class='col-md-12 col-sm-12 mb20' v-if='initList.year_list'>
                <?php if ($this->branchId == "BJ_DS"): ?>
                    <div class='flex mt20 pt10' v-if="teacher_department=='all'">
                        <ul class="nav nav-pills flex1" role="tablist">
                            <li role="presentation" :class='tabType=="myClass"?"active":""'><a href="javascript:;" @click='toggleType("myClass")'><?php echo Yii::t("ptc", "ES Classes");?></a></li>
                            <li role="presentation" :class='tabType=="course"?"active":""'><a href="javascript:;" @click='toggleType("course")'><?php echo Yii::t("ptc", "SS Courses");?></a></li>
                        </ul>
                    </div>
                <?php endif; ?>
                <div class='flex mt20' v-if='tabType=="course"'>
                    <div class='flex1'>
                        <div >
                            <span class="glyphicon glyphicon-user color6"></span>
                            <span style='color:#4D88D2' class='font16'>{{initList.teacher_name}}</span>
                            <span style='color:#4D88D2' title="<?php echo Yii::t('newDS', 'View Journals of Other Teachers')?>" @click="chooseTeacher()" class="glyphicon glyphicon-chevron-down"></span>
                        </div>
                    </div>
                    <div v-if='ptcData.list && ptcData.list.length!=0 && (tabType=="course" &&  ptcData.is_own)'>
                        <button class="btn btn-default ml20" type="button" @click='addStu'><?php echo Yii::t('ptc','Add Students') ?></button>
                        <button class="btn btn-primary ml20" type="button" @click='childScore'><?php echo Yii::t('ptc','Target Score') ?></button>
<!--                        <button class="btn btn-primary ml20" type="button" @click='editComment'>--><?php //echo Yii::t('ptc','Comments') ?><!--</button>-->
                        <button class="btn btn-primary ml20" type="button" @click='middleComment'><?php echo Yii::t('ptc','Comments') ?></button>
                    </div>
                </div>
            </div>
            <div class='mt20'  v-if='initList.class_list' >
                <template v-if='tabType=="myClass"'>
                    <div v-if='initList.class_list.length!=0'>
                        <div class='col-md-2 col-sm-12 ' >
                            <div class="list-group" >
                                <span class="list-group-item " :class='classId==list.id?"active":""' v-for='(list,index) in initList.class_list' @click='classChild(list.id)'>{{list.title}}</span>
                            </div>
                        </div>
                    </div>
                    <div v-else class='col-md-2 col-sm-12 '>
                        <div class="alert alert-warning" role="alert" ><?php echo Yii::t("ptc", "No classe has be assigned");?></div>
                    </div>
                </template>

                <template v-if='tabType=="course"'>
                    <div v-if='initList.course_list.length!=0'>
                        <div class='col-md-2 col-sm-12 ' >
                            <div class="list-group" >
                                <div class="list-group-item text-center" :class='currentCourse.id==list.id?"active":""' v-for='(list,index) in initList.course_list' @click='courseChild(list)'>
                                    <div>{{list.course_code}}</div>
                                    {{list.title}}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div v-else class='col-md-2 col-sm-12 '>
                        <div class="alert alert-warning" role="alert" ><?php echo Yii::t("ptc", "No course has be assigned");?></div>
                    </div>
                </template>

                <div class='col-md-10 col-sm-12 ' >
                    <div class='loading' v-if='subjectLoading'>
                        <span></span>
                    </div>
                    <div  v-if='tabType=="myClass"'>
                        <div class='flex' v-if='subjectList.subject_status'>
                            <div class='flex1'  >
                                <div class='subject' v-for='(list,key,index) in subjectList.subject_status' :class='currentSubject.subject_id==list.subject_id?"subjectShow":""' @click='subjectCheck(list)'>
                                    <div class='font14 color3 fontBold borderBro flex align-items'>
                                        <span class='flex1 p10'>{{list.title}}</span>
                                    <!--历史学年下数据不能替换-->
                                        <div class="btn-group" v-if='list.teacher_id!="" && startYear == currentYear'>
                                            <button type="button" class="btn btn-link dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                <span class='glyphicon glyphicon-user'></span>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li v-if='list.is_self'><a href="javascript:;" @click.stop='unclaim("cancel",list)'><?php echo Yii::t("ptc", "Cancel ownership");?></a></li>
                                                <li v-else><a href="javascript:;" @click.stop='replaceTeacher("replace",list)'><?php echo Yii::t("ptc", "Replace ownership");?></a></li>
                                        </div>
                                    </div>
                                    <div class="media" v-if='list.teacher_id!="" && subjectList.teacher_info[list.teacher_id]'>
                                        <div class="media-left pull-left media-middle">
                                            <a href="javascript:void(0)">
                                                <img :src="subjectList.teacher_info[list.teacher_id].photoUrl" data-holder-rendered="true" class="avatar">
                                            </a>
                                        </div>
                                        <div class="media-body pt10 media-middle">
                                            <h4 class="media-heading font14 mt8 color3 cur-p">{{subjectList.teacher_info[list.teacher_id].name}}</h4>
                                        </div>
                                    </div>
                                    <div v-else class='media text-center'  >
                                        <span class=' text-primary' @click.stop='claimSubjects("take",list)'><?php echo Yii::t("ptc", "Take ownership");?></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div v-else-if='tabType=="myClass"' class="alert alert-warning" role="alert"><?php echo Yii::t('ptc', 'Select a class to continue') ?></div>
                    </div>
                    <div v-if='currentSubject.is_self && ptcData.list && ptcData.list.length!=0' class='pull-right mb10'>
                        <button class="btn btn-default ml20" type="button" @click='addStu'><?php echo Yii::t('ptc','Add Students') ?></button>
                        <button class="btn btn-primary ml20" type="button" @click='childScore'><?php echo Yii::t('ptc','Target Score') ?></button>
                        <button class="btn btn-primary ml20" type="button" @click='editComment2'><?php echo Yii::t('ptc','Comments') ?></button>
                    </div>
                    <div class='loading' v-if='initLoading'>
                        <span></span>
                    </div>
                    <div v-if='ptcData.list'>
                        <div  v-if='Object.keys(ptcData.list).length==0'>
                            <div v-if='(tabType=="myClass" &&  currentSubject.is_self) || (tabType=="course" &&  ptcData.is_own)' class='noStu  text-center' @click='addStu()'>
                                <span class='glyphicon glyphicon-plus font18 addStu'></span>
                                <p class='mt15 text-primary  font14'><?php echo Yii::t("ptc", "Add students for PTC");?></p>
                            </div>
                            <div v-else>
                                <div class="alert alert-warning" role="alert" ><?php echo Yii::t("ptc", "No Data");?></div>
                            </div>
                        </div>
                        <div v-else>
                            <table class="table table-bordered  score" v-if='Object.keys(ptcData.list).length!=0'>
                                <tr>
                                    <th width='150'><?php echo Yii::t('ptc','Student') ?></th>
                                    <th width='500'><?php echo Yii::t('ptc','Score') ?></th>
                                    <th width='700'><?php echo Yii::t('ptc','Comments') ?></th>
                                </tr>
                                <template v-for='(list,index,key) in ScoreDataList'>
                                    <tr v-if="ptcData.child[list['id']]">
                                        <td class='text-center'>
                                            <div>
                                                <img :src="ptcData.child[list['id']].avatar" class='avatar' alt="">
                                            </div>
                                            <div class='color3 font14 mt10'>{{ptcData.child[list['id']].name}}</div>
                                            <p class='color6'>{{ptcData.child[list['id']].class_name}}</p>
                                            <p class='color9'><?php echo Yii::t('ptc','# Meetings:') ?> {{ptcData.list[list['id']].total}}</p>
                                            <div v-if='(tabType=="myClass" &&  currentSubject.is_self) || (tabType=="course" &&  ptcData.is_own)'><span class='glyphicon glyphicon-trash text-primary' @click="delStu(ptcData.child[list['id']])"></span></div>
                                        </td>
                                        <td>
                                            <div v-if="Object.keys( ptcData.list[list['id']]['score']).length==0">
                                                <div class='mt10 colorc text-center'><?php echo Yii::t("ptc", "No scores recorded");?></div>
                                            </div>
                                            <div v-else  class=''>
                                                <div v-if="ptcData.list[list['id']]['score']['assessment'].length!=0">
                                                    <div v-for="(item,idx) in  ptcData.list[list['id']]['score'].Math_CLA" class=' mt5'>
                                                        <div  class='relative'>
                                                            {{item.title}} :
                                                            <span class='text-primary' data-toggle="popover" data-trigger="focus" title="Dismissible popover" data-content="And here's some amazing content. It's very engaging. Right?" @click='showPopover(index,item)'><?php echo Yii::t('ptc','Number of areas for growth') ?></span>
                                                            <div class="popover bottom subTop" :id='"sub"+index'>
                                                                <div class="arrow"></div>
                                                                <h3 class="popover-title"><?php echo Yii::t('ptc','Number of areas for growth') ?>({{item.score}})</h3>
                                                                <div class="popover-content" v-if='popoverData.l'>
                                                                    <div v-if='popoverData.l.length!=0'>
                                                                        <div class='font14 color3 mb10'><strong>L（Learner）：</strong><span class='pointNum'>{{popoverData.l.length}}</span>  </div>
                                                                        <div class='flex' v-for='l in popoverData.l'>
                                                                            <div><span class='point'></span></div>
                                                                            <div class='flex1'>
                                                                                <div >{{l}}</div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div v-if='popoverData.n.length!=0'>
                                                                        <div class='font14 color3 mb10'><strong>N（Novice）：</strong><span class='pointNum'>{{popoverData.n.length}}</span> </div>
                                                                        <div class='flex' v-for='n in popoverData.n'>
                                                                            <div><span class='point'></span></div>
                                                                            <div class='flex1'>
                                                                                <div >{{n}}</div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <span class="label label-default ml5 mt5">{{item.score}}<span v-if='item.target!=null && item.target!=""'>/{{item.target}}</span></span>
                                                            <span class="label label-default ml10"  v-if='item.title=="MAP Reading" && item.standard!=null'>{{item.standard}}</span>
                                                            <span  v-if='item.target==null || item.target===""' class='yellow ml20'><span class='glyphicon glyphicon-exclamation-sign'></span> <?php echo Yii::t('ptc','Target score not set') ?></span>
                                                        </div>
                                                    </div>
                                                    <table class='scoreTable' v-if="ptcData.list[list['id']]['score'].Math_CLA_other.length!=0">
                                                        <thead>                                                            
                                                            <tr>
                                                                <th  width='210'  class='text-right color6 scoreTableLeft'><?php echo Yii::t('ptc','Subject') ?></th>
                                                                <th  width='50'  class='text-center color6 '><?php echo Yii::t("ptc", "Score");?></th>
                                                                <th  width='190'  class='scoreTableRight text-center color6'><?php echo Yii::t("ptc", "Target Score");?></th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                    
                                                            <tr  v-for="(item,idx) in  ptcData.list[list['id']]['score'].Math_CLA_other">
                                                                <td class='text-right'>
                                                                    <span class='color3'>{{item.title}}</span>
                                                                    <span class="label label-default ml10"  v-if='item.title=="MAP Reading" && item.standard!=null'>{{item.standard}}</span>
                                                                </td>
                                                                <td class='color3 text-center relative'>
                                                                    <span>{{item.score}}</span> 
                                                                </td>
                                                                <td class='color3 text-center'>
                                                                <span v-if='item.target!=null && item.target!=""'>{{item.target}}</span>
                                                                    
                                                                    <span  v-if='item.target==null || item.target==""' class='yellow ml20'><span class='glyphicon glyphicon-exclamation-sign'></span> <?php echo Yii::t('ptc','Target score not set') ?></span>
                                                                </td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                </div>
                                                <div v-if="ptcData.list[list['id']]['score']['courses'].length!=0">
                                                    <div v-for="(item,idx) in  ptcData.list[list['id']]['score'].courses" class='relative mt20'>
                                                        <div class='color3 font14'>{{item.title}} :</div> 
                                                        <table class='scoreTable'>
                                                            <thead>                                                            
                                                                <tr>
                                                                    <th  width='210' class='text-right color6 scoreTableLeft'><?php echo Yii::t("ptc", "Criteria");?></th>
                                                                    <th  width='50'  class='text-center color6'><?php echo Yii::t("ptc", "Score");?></th>
                                                                    <th  width='190'  class='scoreTableRight text-center color6'><?php echo Yii::t("ptc", "Target Score");?></th>
                                                                </tr>
                                                            </thead>
                                                            <tbody>
                                                                <tr v-for='(_item,i) in item.subdomain'>
                                                                    <td class='text-right'><span class='color3'>{{_item.subdomain}}</span></td>
                                                                    <td class='color3 text-center relative'>
                                                                        <span>{{_item.score}}</span> 
                                                                    </td>
                                                                    <td class='color3 text-center'>
                                                                        <span v-if='_item.target!=null  && _item.target!=""'>{{_item.target}}</span>
                                                                        <span class="label label-default ml10" v-if='_item.standard!=null'>{{_item.standard}}</span>
                                                                        <span  v-if='_item.target==null || _item.target==""' class='yellow'><span class='glyphicon glyphicon-exclamation-sign'></span> <?php echo Yii::t('ptc','Target score not set') ?></span>
                                                                    </td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div class='flex'>
                                                <div class='flex1'>
                                                    <div class='mt5 enbreak font14' v-if="ptcData.list[list['id']]['plan']['school_comment'] && ptcData.list[list['id']]['plan']['school_comment']!='' && ptcData.list[list['id']]['plan']['school_comment']!=null" v-html="html(ptcData.list[list['id']]['plan']['school_comment'])"></div>
                                                    <div v-else class='mt10 colorc text-center'>N/A</div>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                </template>
                            </table>
                        </div>
                    </div>
                    <div v-else-if='initList.course_list.length!=0 && tabType=="course"' class="alert alert-warning" role="alert"><?php echo Yii::t('ptc', 'Select a class to continue') ?></div>
                </div>
            </div>
        </template>
        <template v-if="showTab == 'MyPTC'">
                <div class="col-md-12 col-sm-12 mb20">
                    <div class='col-md-12 col-sm-12 mb20' v-if='initData.itemData'>
                        <div class='mt20 flex'>
                            <div class='flex1'>
                                <span  @click="myPtcChooseTeacher()">
                                    <span class="glyphicon glyphicon-user color6 font16 mr5"></span>
                                    <span style='color:#4D88D2' class='font16'>{{teacherTitle==''?teacherInfo.name:teacherTitle}}</span>
                                    <span style='color:#4D88D2' class="glyphicon glyphicon-chevron-down"></span>
                                </span>

                            </div>
                            <div>
                                <button type="button" class="btn btn-primary" @click='time'><?php echo Yii::t('ptc','Schedulable Time') ?></button>
                            </div>
                        </div>
                        <div class='flex mt20 font14' v-if='initData.itemData.length!=0'>
                            <ul class="nav nav-pills">
                                <li role="presentation" :class='currentDate=="all"?"active":""' @click='showDate("all")'><a href="javascript:;"><?php echo Yii::t("dashboard", "All");?></a></li>
                                <li role="presentation" v-for='(list,key,index) in initData.itemData' :class='currentDate==key?"active":""' @click='showDate(key)'><a href="javascript:;">{{key}}</a></li>
                            </ul>
                        </div>
                        <div class='mt20'>
                            <span class='font14 color3'><?php echo Yii::t('ptc','Legend: ') ?></span>
                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/A-E.png' ?>" class='example'  alt=""> <span class='color6'>{{initData.translationConfig[1].title}}</span>
                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/E-A.png' ?>" class='example ml20' alt=""> <span class='color6'>{{initData.translationConfig[2].title}}</span>
                            <span>
                                <a class="btn btn-primary ml24" href="<?php echo $this->createUrl('assign'); ?>" target="_blank" role="button"><?php echo Yii::t("ptc", "SS All Schedules");?></a>
                           </span>
                        </div>
                        <div class=' scrollbar  mb20 mt20'>
                            <div  v-if='initData.itemData.length==0 && initData.noTimeData.length==0'>
                                <div class="alert alert-warning" role="alert" ><?php echo Yii::t("ptc", "No Data");?></div>
                            </div>
                            <table class="table table-bordered  score" v-else>
                                <tr>
                                    <th width='100' class='text-center'><?php echo Yii::t("labels", "Time");?></th>
                                    <th width='200'><?php echo Yii::t("ptc", "Student");?></th>
                                    <th width='500'><?php echo Yii::t("ptc", "Score");?></th>
                                    <th width='700'><?php echo Yii::t("ptc", "Comments");?></th>
                                </tr>
                                <template v-if='currentDate=="all"'>
                                    <tr  v-for='(item,index) in initData.noTimeData'>
                                        <td class='text-center '>
                                            <span class='red' ><?php echo Yii::t("ptc", "Not Scheduled");?></span>
                                            <div v-if='item.location!=null' class='color6 mt10 font12'>{{item.location}}</div>
                                            <span v-if='item.parents_choose==-1'><span class='glyphicon glyphicon-exclamation-sign red'></span> <?php echo Yii::t("ptc", "Not to attend");?></span>
                                            <span  v-if='item.parents_choose==1'><span class='glyphicon glyphicon-ok-sign green'></span> <?php echo Yii::t("ptc", "Confirmed to attend"); ?></span>
                                        </td>
                                        <td>
                                            <div class="media pb10" v-if='initData.childInfo[item.child_id]'>
                                                <div class="media-left pull-left media-middle relative">
                                                    <img :src="initData.childInfo[item.child_id].avatar"  data-holder-rendered="true" class="avatar">
                                                    <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/E-A.png' ?>" class='translate' alt="" v-if='initData.translation[item.child_id]==3'>
                                                    <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/A-E.png' ?>" class='translate' alt="" v-if='initData.translation[item.child_id]==2'>
                                                </div>
                                                <div class="media-body media-middle">
                                                    <div class="font14 mt5 color3">{{initData.childInfo[item.child_id].name}}</div>
                                                    <div class=" mt5 color6">{{item.child_class_name}}</div>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div v-if='initData.scoreData[item.child_id] && initData.scoreData[item.child_id][item.subject]'>
                                                <div v-if='initData.scoreData[item.child_id][item.subject].assessment && initData.scoreData[item.child_id][item.subject].assessment.length!=0'>
                                                    <div v-for="(_item,idx) in initData.scoreData[item.child_id][item.subject].Math_CLA" class=' mt5'>
                                                        <div  class='relative assessment'>
                                                            {{_item.title}} : 
                                                            <span class='text-primary' data-toggle="popover" data-trigger="focus" title="Dismissible popover" data-content="And here's some amazing content. It's very engaging. Right?" @click='myPtcShowPopover(item.child_id,_item,"noTimeData")'><?php echo Yii::t('ptc','Number of areas for growth') ?></span>
                                                            <div class="popover bottom subTop" :id='"sub"+item.child_id'>
                                                                <div class="arrow"></div>
                                                                <h3 class="popover-title"><?php echo Yii::t('ptc','Number of areas for growth') ?>({{_item.score}})</h3>
                                                                <div class="popover-content" v-if='popoverData.l'>
                                                                    <div v-if='popoverData.l.length!=0'>
                                                                        <div class='font14 color3 mb10'><strong>L（Learner）：</strong><span class='pointNum'>{{popoverData.l.length}}</span>  </div>
                                                                        <div class='flex' v-for='l in popoverData.l'>
                                                                            <div><span class='point'></span></div>
                                                                            <div class='flex1'>
                                                                                <div >{{l}}</div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div v-if='popoverData.n.length!=0'>
                                                                        <div class='font14 color3 mb10'><strong>N（Novice）：</strong><span class='pointNum'>{{popoverData.n.length}}</span> </div>
                                                                        <div class='flex' v-for='n in popoverData.n'>
                                                                            <div><span class='point'></span></div>
                                                                            <div class='flex1'>
                                                                                <div >{{n}}</div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <span class="label label-default ml5 mt5">{{_item.score}}<span v-if='_item.target!=null && _item.target!=""'>/{{_item.target}}</span></span>
                                                            <span class="label label-default ml10"  v-if='_item.title=="MAP Reading" && _item.standard!=null'>{{_item.standard}}</span>
                                                            <span  v-if='_item.target==null || _item.target===""' class='yellow ml20'><span class='glyphicon glyphicon-exclamation-sign'></span> <?php echo Yii::t('ptc','Target score not set') ?></span>
                                                        </div>
                                                    </div>
                                                    <table class='scoreTable' v-if="initData.scoreData[item.child_id][item.subject].Math_CLA_other.length!=0">
                                                        <thead>                                                            
                                                            <tr>
                                                                <th  width='210' class='text-right color6 scoreTableLeft'><?php echo Yii::t('ptc','Subject') ?></th>
                                                                <th  width='50'  class='text-center color6'><?php echo Yii::t("ptc", "Score");?></th>
                                                                <th  width='190'  class='scoreTableRight text-center color6'><?php echo Yii::t("ptc", "Target Score");?></th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                    
                                                            <tr  v-for="(_item,idx) in  initData.scoreData[item.child_id][item.subject].Math_CLA_other">
                                                                <td class='text-right'>
                                                                    <span class='color3'>{{_item.title}}</span>
                                                                    <span class="label label-default ml10"  v-if='_item.title=="MAP Reading" && _item.standard!=null'>{{_item.standard}}</span>
                                                                </td>
                                                                <td class='color3 text-center relative'>
                                                                    
                                                                    <span>{{_item.score}}</span> 
                                                                </td>
                                                                <td class='color3 text-center'>
                                                                <span v-if='_item.target!=null && _item.target!=""'>{{_item.target}}</span>
                                                                    
                                                                    <span  v-if='_item.target==null || _item.target==""' class='yellow ml20'><span class='glyphicon glyphicon-exclamation-sign'></span> <?php echo Yii::t('ptc','Target score not set') ?></span>
                                                                </td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                </div>
                                                <div v-if='initData.scoreData[item.child_id][item.subject].courses && initData.scoreData[item.child_id][item.subject].courses.length!=0'>
                                                    <div v-for='(_item,idx) in initData.scoreData[item.child_id][item.subject].courses' class='relative mt20'>
                                                        <div class='color3 font14'>{{_item.title}} :</div> 
                                                        <table class='scoreTable'>
                                                            <thead>                                                            
                                                                <tr>
                                                                    <th  v-if="_item.title.startsWith('DP') === false" width='210' class='text-right color6 scoreTableLeft'><?php echo Yii::t("ptc", "Criteria");?></th>
                                                                    <th  width='50'  class='text-center color6 '><?php echo Yii::t("ptc", "Score");?></th>
                                                                    <th  width='190'  class='scoreTableRight text-center color6'><?php echo Yii::t("ptc", "Target Score");?></th>
                                                                </tr>
                                                            </thead>
                                                            <tbody>
                                                                <tr v-for='(__item,i) in _item.subdomain'>
                                                                    <td v-if="_item.title.startsWith('DP') === false"><span class='color3'>{{__item.subdomain}}</span></td>
                                                                    <td class='color3 text-center relative'>
                                                                        <span>{{__item.score}}</span> 
                                                                    </td>
                                                                    <td class='color3 text-center'>
                                                                        <span v-if='__item.target!=null && __item.target!=""'>{{__item.target}}</span>
                                                                        <span class="label label-default ml10" v-if='__item.standard!=null'>{{__item.standard}}</span>
                                                                        <span  v-if='__item.target==null || __item.target===""' class='yellow ml20'><span class='glyphicon glyphicon-exclamation-sign'></span> <?php echo Yii::t('ptc','Target score not set') ?></span>
                                                                    </td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                            <div v-else  >
                                                <div class='mt10 colorc text-center'><?php echo Yii::t("ptc", "No scores recorded");?></div>
                                            </div>
                                        </td>
                                        <td>
                                            <div v-if='initData.planData[item.child_id] && initData.planData[item.child_id][item.subject]'>
                                                <div class='mt5 enbreak font14' v-if='initData.planData[item.child_id][item.subject]!="" && initData.planData[item.child_id][item.subject]!=null' v-html='html(initData.planData[item.child_id][item.subject])'></div>
                                                <div v-else class='mt10 colorc text-center'>N/A</div>
                                            </div>
                                            <div v-else class='mt10 colorc text-center'>N/A</div>
                                        </td>
                                    </tr>
                                </template>
                                <template v-for='(list,key,index) in itemData'>
                                    <tr v-for='(item,i) in list'>
                                        <td class='text-center font14'>
                                            {{key}}
                                            <div class='mt5'> {{item.time}}</div>
                                            <div v-if='item.location!=null' class='color6 mt10 font12'>{{item.location}}</div>
                                            <span v-if='item.parents_choose==-1'><span class='glyphicon glyphicon-exclamation-sign red'></span> <?php echo Yii::t("ptc", "Not to attend");?></span>
                                            <span  v-if='item.parents_choose==1'><span class='glyphicon glyphicon-ok-sign green'></span> <?php echo Yii::t("ptc", "Confirmed to attend"); ?></span>
                                        </td>
                                        <td>
                                            <div class=" pb10" v-if='initData.childInfo[item.child_id]'>
                                                <div class="media-left pull-left media-middle relative">
                                                    <img :src="initData.childInfo[item.child_id].avatar"  data-holder-rendered="true" class="avatar">
                                                    <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/E-A.png' ?>" class='translate' alt="" v-if='initData.translation[item.child_id]==3'>
                                                    <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/A-E.png' ?>" class='translate' alt="" v-if='initData.translation[item.child_id]==2'>
                                                </div>
                                                <div class="pull-left media-middle mt4 ml12">
                                                    <div class="font14 mt5 color3">
                                                        <span class='pull-left' style='line-height:1'>{{initData.childInfo[item.child_id].name}}</span>
                                                        <span v-if='item.parents_choose==1 && initData.childInfo[item.child_id].grade!="ES" && (my_ptc_teacher_id=="" || my_ptc_teacher_id==user_id)'>
                                                            <div class="btn-group ml8 pull-left" >
                                                                <button type="button" class="btn btn-default btn-xs dropdown-toggle"
                                                                        data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" style="padding: 0;line-height:1">
                                                                    <span class="iconfont icon-more" style="font-size: 15px"></span>
                                                                </button>
                                                                <ul class="dropdown-menu">
                                                                    <li><a href="javascript:;" @click="setAttendStatus(item,1)"><?php echo Yii::t("ptc", "PTC occured"); ?></a></li>
                                                                    <li><a href="javascript:;" @click="setAttendStatus(item,2)"><?php echo Yii::t("ptc", "PTC not occured"); ?></a></li>
                                                                    <li><a href="javascript:;" v-if="item.attended != 0"  @click="setAttendStatus(item,0)"><?php echo Yii::t("ptc", "Reset status"); ?></a></li>
                                                                </ul>
                                                            </div>
                                                            <span class="ml10 pull-left">
                                                                <span v-if="item.attended == 1"  :key='count' class='pull-left' style='margin-top:-3px' >
                                                                    <span  :key='index' class="iconfont icon-check-circle-fill"   data-toggle="tooltip" data-placement="top" title="<?php echo Yii::t("ptc", "PTC occured"); ?>"></span>
                                                                </span>
                                                                <span v-if="item.attended == 2"   :key='count'>
                                                                    <span  :key='index' style='color:#D9534F'   class="glyphicon glyphicon-exclamation-sign font17" data-toggle="tooltip" data-placement="top" title="<?php echo Yii::t("ptc", "PTC not occured"); ?>"></span>
                                                                </span>
                                                            </span>
                                                        </span>
                                                    </div>
                                                    <div class='clearfix'></div>
                                                    <div class=" mt5 color6">{{item.child_class_name}}</div>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div v-if='initData.scoreData[item.child_id] && initData.scoreData[item.child_id][item.subject]' class=''>
                                                <div v-if='initData.scoreData[item.child_id][item.subject].assessment && initData.scoreData[item.child_id][item.subject].assessment.length!=0'>
                                                    <div v-for="(_item,idx) in initData.scoreData[item.child_id][item.subject].Math_CLA" class=' mt5'>
                                                        <div  class='relative'>
                                                             {{_item.title}} :
                                                            <span class='text-primary' data-toggle="popover" data-trigger="focus" title="Dismissible popover" data-content="And here's some amazing content. It's very engaging. Right?" @click='myPtcShowPopover(item.child_id,_item,"itemData")'><?php echo Yii::t('ptc','Number of areas for growth') ?></span>
                                                            <div class="popover bottom subTop" :id='"sub"+item.child_id'>
                                                                <div class="arrow"></div>
                                                                <h3 class="popover-title"><?php echo Yii::t('ptc','Number of areas for growth') ?>({{_item.score}})</h3>
                                                                <div class="popover-content" v-if='popoverData.l'>
                                                                    <div v-if='popoverData.l.length!=0'>
                                                                        <div class='font14 color3 mb10'><strong>L（Learner）：</strong><span class='pointNum'>{{popoverData.l.length}}</span>  </div>
                                                                        <div class='flex' v-for='l in popoverData.l'>
                                                                            <div><span class='point'></span></div>
                                                                            <div class='flex1'>
                                                                                <div >{{l}}</div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div v-if='popoverData.n.length!=0'>
                                                                        <div class='font14 color3 mb10'><strong>N（Novice）：</strong><span class='pointNum'>{{popoverData.n.length}}</span> </div>
                                                                        <div class='flex' v-for='n in popoverData.n'>
                                                                            <div><span class='point'></span></div>
                                                                            <div class='flex1'>
                                                                                <div >{{n}}</div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <span class="label label-default ml5 mt5">{{_item.score}}<span v-if='_item.target!=null && _item.target!=""'>/{{_item.target}}</span></span>
                                                            <span class="label label-default ml10"  v-if='_item.title=="MAP Reading" && _item.standard!=null'>{{_item.standard}}</span>
                                                            <span  v-if='_item.target==null || _item.target===""' class='yellow ml20'><span class='glyphicon glyphicon-exclamation-sign'></span> <?php echo Yii::t('ptc','Target score not set') ?></span>
                                                        </div>
                                                    </div>
                                                    <table class='scoreTable' v-if="initData.scoreData[item.child_id][item.subject].Math_CLA_other.length!=0">
                                                        <thead>                                                            
                                                            <tr>
                                                                <th  width='210' class='text-right color6 scoreTableLeft'><?php echo Yii::t('ptc','Subject') ?></th>
                                                                <th  width='50'  class='text-center color6 '><?php echo Yii::t("ptc", "Score");?></th>
                                                                <th  width='190'  class='scoreTableRight text-center color6'><?php echo Yii::t("ptc", "Target Score");?></th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                    
                                                            <tr  v-for="(_item,idx) in  initData.scoreData[item.child_id][item.subject].Math_CLA_other">
                                                                <td class='text-right'>
                                                                    <span class='color3'>{{_item.title}}</span>
                                                                    <span class="label label-default ml10"  v-if='_item.title=="MAP Reading" && _item.standard!=null'>{{_item.standard}}</span>
                                                                </td>
                                                                <td class='color3 text-center relative'>
                                                                    
                                                                    <span>{{_item.score}}</span> 
                                                                </td>
                                                                <td class='color3 text-center'>
                                                                <span v-if='_item.target!=null && _item.target!=""'>{{_item.target}}</span>
                                                                    
                                                                    <span  v-if='_item.target==null || _item.target==""' class='yellow ml20'><span class='glyphicon glyphicon-exclamation-sign'></span> <?php echo Yii::t('ptc','Target score not set') ?></span>
                                                                </td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                </div>
                                                <div v-if='initData.scoreData[item.child_id][item.subject].courses && initData.scoreData[item.child_id][item.subject].courses.length!=0'>
                                                    <div v-for='(_item,idx) in initData.scoreData[item.child_id][item.subject].courses' class='relative mt20'>
                                                        <div class='color3 font14'>{{_item.title}} :</div> 
                                                        <table class='scoreTable'>
                                                            <thead>                                                            
                                                                <tr>
                                                                    <th width='210' class='text-right color6 scoreTableLeft'><?php echo Yii::t("ptc", "Criteria");?></th>
                                                                    <th width='50' class='text-center color6 '><?php echo Yii::t("ptc", "Score");?></th>
                                                                    <th width='190' class='scoreTableRight text-center color6'><?php echo Yii::t("ptc", "Target Score");?></th>
                                                                </tr>
                                                            </thead>
                                                            <tbody>
                                                                <tr v-for='(__item,i) in _item.subdomain'>
                                                                    <td class='text-right'><span class='color3'>{{__item.subdomain}}</span></td>
                                                                    <td class='color3 text-center relative'>
                                                                        <span>{{__item.score}}</span> 
                                                                    </td>
                                                                    <td class='color3 text-center'>
                                                                        <span v-if='__item.target!=null && __item.target!=""'>{{__item.target}}</span>
                                                                        <span class="label label-default ml10" v-if='__item.standard!=null'>{{__item.standard}}</span>
                                                                        <span  v-if='__item.target==null || __item.target===""' class='yellow ml20'><span class='glyphicon glyphicon-exclamation-sign'></span> <?php echo Yii::t('ptc','Target score not set') ?></span>
                                                                    </td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                            <div v-else  >
                                                <div class='mt10 colorc text-center'><?php echo Yii::t("ptc", "No scores recorded");?></div>
                                            </div>
                                        </td>
                                        <td>
                                            <div v-if='initData.planData[item.child_id] && initData.planData[item.child_id][item.subject]'>
                                                <div class='mt5 enbreak font14' v-if='initData.planData[item.child_id][item.subject]!="" && initData.planData[item.child_id][item.subject]!=null' v-html='html(initData.planData[item.child_id][item.subject])'></div>
                                                <div v-else class='mt10 colorc text-center'>N/A</div>
                                            </div>
                                            <div v-else class='mt10 colorc text-center'>N/A</div>
                                        </td>
                                    </tr>
                                </template>
                            </table>
                            <div class="alert alert-warning" role="alert" v-else><?php echo Yii::t("ptc", "No Data");?></div>
                        </div>
                        <!-- 教师列表 -->
                        <div class="modal fade" id="teacherList" tabindex="-1" role="dialog" aria-labelledby="modal" aria-hidden="false">
                            <div class="modal-dialog modal-lg" role="document">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                                        <h4 class="modal-title" ><?php echo Yii::t('attends','Select a Teacher') ?> <span id="teacherList_t"></span></h4>
                                    </div>
                                    <div class="form-horizontal">
                                        <div class="modal-body" v-if='teacherPtcList.teacherData'>
                                            <div v-if='teacherPtcList.teacherData.length==0'>
                                                <div class="alert alert-warning" role="alert" ><?php echo Yii::t("ptc", "No Data");?></div>
                                            </div>
                                            <div v-else>
                                                <div v-if='teacherPtcList.teacherData[1]'>
                                                    <p class='font14 color3'><strong><?php echo Yii::t("ptc", "ES");?></strong></p>
                                                    <a v-for='(num,key,index) in teacherPtcList.teacherData[1]'  class='mb10 btn btn-default mr15' href='javascript:;' @click='myPtcShowTeacher(key)'>
                                                        {{teacherPtcList.teachersInfo[key].name}}<span class="badge ml10 bg7">{{num}}</span>
                                                    </a>
                                                </div>
                                                <div v-if='teacherPtcList.teacherData[2]'>
                                                    <p class='mt20 font14 color3'><strong><?php echo Yii::t("ptc", "SS");?></strong></p>
                                                    <a v-for='(num,key,index) in teacherPtcList.teacherData[2]'  class='mb10 btn btn-default mr15' href='javascript:;' @click='myPtcShowTeacher(key)'>
                                                        {{teacherPtcList.teachersInfo[key].name}}<span class="badge ml10 bg7">{{num}}</span>
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- 教师列表 -->
                        <div class="modal fade" id="reserve" tabindex="-1" role="dialog" aria-labelledby="modal" aria-hidden="false">
                            <div class="modal-dialog modal-lg" role="document">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                                        <h4 class="modal-title" ><?php echo Yii::t('ptc','Schedulable Time') ?> <span id="teacherList_t"></span></h4>
                                    </div>
                                    <div class="modal-body">
                                        <div class="media pb10">
                                            <div class="media-left pull-left media-middle">
                                                <img :src="teacherInfo.photo" data-holder-rendered="true" class="addImg">
                                            </div>
                                            <div class="media-body media-middle">
                                                <div class="font18 mt15 color3">{{teacherInfo.name}}</div>
                                            </div>
                                        </div>
                                        <div class="flex mt20" v-if='reserveData.classData && reserveData.classData.length!=0'>
                                            <label class="mt5 text-right"><?php echo Yii::t('ptc','Classes with ownership:') ?></label>
                                            <div class="flex1 ml20">
                                                <span class='classTitle mr10 mb5' v-for='(list,key,i) in reserveData.classData'>{{list}}</span>
                                            </div>
                                        </div>
                                        <div class='clearfix'></div>
                                        <div class='table_wrap scrollbar  mt20'>
                                            <table class="table" id='table' v-if='reserveData.scheduleList && reserveData.scheduleList.length!=0'>
                                                <tr>
                                                    <th v-for='(list,key,index) in reserveData.scheduleList' class='font16 text-center'>
                                                        <strong>{{key}}</strong>
                                                        <div class='font14 mt5 color6'>{{list.week}}</div>
                                                    </th>
                                                </tr>
                                                <tr>
                                                    <td v-for='(item,index) in reserveData.scheduleList'>
                                                        <div v-for='(_item,idx) in item.items' class='reserved mb20'>
                                                            <div class='font14 color3 pb10 timeStart'>
                                                                <span class='glyphicon glyphicon-time mr10'></span>
                                                                <strong>{{_item.start}}-{{_item.end}}</strong>
                                                            </div>
                                                            <div class='timeList'>
                                                                <div class='flex mt15 border'>
                                                                    <span class='glyphicon glyphicon-ok-sign green pt5'></span>
                                                                    <div class='flex1 ml10'>
                                                                        <div class='color3 font14'>{{reserveData.classData[_item.class_id]}}</div>
                                                                        <div v-for='(tea,keys,id) in reserveData.classTeacher[_item.class_id]' class='mt5'>
                                                                            <img :src="reserveData.teachersInfo[tea].photoUrl" class='teacherImg mr10 pull-left' alt="">
                                                                        </div>
                                                                        <div class='clearfix'></div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </td>
                                                </tr>
                                            </table>
                                            <div v-else>
                                                <div class="alert alert-warning" role="alert"><?php echo Yii::t('ptc','No Data (This function is for ES only)') ?></div>
                                            </div>
                                        </div>
                                        <div class='clearfix'></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
        </template>
        <!--班级ptc设置-->
        <div class="classPtcSet" v-show="showTab == 'classPtcSet'"></div>
        <div class='clearfix'></div>
        <!-- 添加学生 -->
        <div class="modal fade" id="childModel" tabindex="-1" role="dialog" aria-labelledby="modal" aria-hidden="false"  data-backdrop="static" data-keyboard="false">
            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title" ><?php echo Yii::t('ptc','Add students for PTC') ?> <span id="remarks_t"></span></h4>
                    </div>
                    <div class="modal-body" >
                        <div class="media pb10" v-if='initList.teacher_info'>
                            <div class="media-left pull-left media-middle">
                                <a href="javascript:void(0)"><img :src="initList.teacher_info.photoUrl"  data-holder-rendered="true" class="addImg"></a>
                            </div>
                            <div class="media-body pt10 media-middle">
                                <h4 class="media-heading font18 mt5 color3">{{initList.teacher_info.name}}</h4>
                            </div>
                        </div>
                        <div class='max-height mt20 scroll-box' :style='"max-height:"+maxHeight'>
                            <table class="table" v-if='childDatas.student_list  && childDatas.student_list.length!=0'>
                                <tr>
                                    <th width='90'>
                                        <label class="checkbox-inline">
                                            <input type="checkbox" :value="allChild" v-model='allChild' @change='allStu' ><strong><?php echo Yii::t('ptc','Select') ?></strong>
                                        </label>
                                    </th>
                                    <th width='230'><?php echo Yii::t('ptc','Student') ?></th>
                                    <th><?php echo Yii::t('ptc','Score') ?></th>
                                </tr>
                                <tr v-for="(list,key,index) in studentDataList">
                                    <td><input type="checkbox" :value="list.id" v-model='child_id' :disabled="childDatas.student_list[list['id']]['checked']==1"></td>
                                    <td>
                                        <div class="media">
                                            <div class="media-left pull-left media-middle">
                                                <a href="javascript:void(0)"><img :src="childDatas.student_list[list['id']]['avatar']" data-holder-rendered="true" class="avatar"></a>
                                            </div>
                                            <div class="media-body pt10 media-middle">
                                                <h4 class="media-heading font14 color3">{{childDatas.student_list[list['id']]['name']}}</h4>
                                                <div class="color6 font12 mt5 ">{{childDatas.student_list[list['id']]['class_name']}}</span>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div v-if="childDatas.score_list[list['id']]" class='assessment'>
                                            <div v-if="childDatas.score_list[list['id']]['assessment']">
                                                <div v-if="childDatas.score_list[list['id']]['assessment'].length!=0" >
                                                    <div v-for="(item,idx) in childDatas.score_list[list['id']]['assessment']" class='relative mt5'>
                                                        {{item.title}} :
                                                        <span class="label label-default ml5 mt5">{{item.score}}<span v-if='item.target!=null  && item.target!=""'>/{{item.target}}</span></span>
                                                        <span class="label label-default ml10"  v-if='item.title=="MAP Reading" && item.standard!=null'>{{item.standard}}</span>
                                                        <span  v-if='item.target==null || item.target===""' class='yellow ml20'><span class='glyphicon glyphicon-exclamation-sign'></span> <?php echo Yii::t('ptc','Target score not set') ?></span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div v-if="childDatas.score_list[list['id']]['courses']">
                                                <div v-if="childDatas.score_list[list['id']]['courses'].length!=0" >
                                                    <div v-for="(item,idx) in childDatas.score_list[list['id']]['courses']" class='relative mt5'>
                                                        {{item.title}} :
                                                        <p v-for='(_item,i) in item.subdomain' class='ml20 mt5'>{{_item.subdomain}} :
                                                            <span class="label label-default ml5">{{_item.score}}<span v-if='_item.target!=null  && item.target!=""'>/{{_item.target}}</span></span>
                                                            <span class="label label-default ml10" v-if='_item.standard!=null'>{{_item.standard}}</span>
                                                            <span  v-if='item.target==null || item.target===""' class='yellow ml20'><span class='glyphicon glyphicon-exclamation-sign'></span> <?php echo Yii::t('ptc','Target score not set') ?></span>
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            </table>
                            <div v-else>
                                <div class="alert alert-warning" role="alert" ><?php echo Yii::t("ptc", "No Data");?></div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" @click='saveAddStu()' v-if='saveChildModal'><?php echo Yii::t('global','Save') ?></button>
                        <button type="button" class="btn btn-primary" @click='saveAddStuSub()' v-else><?php echo Yii::t('global','Save') ?></button>
                        <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('global','Cancel') ?></button>
                    </div>
                </div>
            </div>
        </div>
        <!-- 填写成绩 -->
        <div class="modal fade" id="childScore" tabindex="-1" role="dialog" aria-labelledby="modal" aria-hidden="false"  data-backdrop="static" data-keyboard="false">
            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title" ><?php echo Yii::t('ptc','Target Score') ?> <span id="remarks_t"></span></h4>
                    </div>
                    <div class="modal-body" v-if='Object.keys(scoreList).length!=0'>
                        <div class="media pb10">
                            <div class="media-left pull-left media-middle">
                                <a href="javascript:void(0)"><img :src="initList.teacher_info.photoUrl"  data-holder-rendered="true" class="addImg"></a>
                            </div>
                            <div class="media-body pt10 media-middle">
                                <h4 class="media-heading font18 mt5 color3">{{initList.teacher_info.name}}</h4>
                            </div>
                        </div>
                        <div class='max-height mt20 scroll-box' :style='"max-height:"+maxHeight'>
                            <div v-for='(item,key,index) in ScoreDataList'>
                                <div v-if="childDatas.student_list[item['id']]">
                                    <div class='assessment' v-if='childDatas.student_list'>
                                        <div class="media">
                                            <div class="media-left pull-left media-middle">
                                                <a href="javascript:void(0)"><img :src="childDatas.student_list[item['id']].avatar" data-holder-rendered="true" class="avatar"></a>
                                            </div>
                                            <div class="media-body pt10 media-middle">
                                                <h4 class="media-heading font14 color3">{{childDatas.student_list[item['id']].name}}</h4>
                                                <div class="color6 font12 mt5 ">{{childDatas.student_list[item['id']].class_name}}</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <table class="table mt10" v-if="Object.keys(scoreList.score_list_by_child).length!=0 && scoreList.score_list_by_child[item['id']]">
                                        <tr>
                                            <td  width='200'><?php echo Yii::t("ptc", "Subject");?></td>
                                            <td  width='100'><?php echo Yii::t("ptc", "Score");?></td>
                                            <td  width='100'><?php echo Yii::t("ptc", "Target Score");?></td>
                                            <td  width='100' v-if='scoreList.have_MAPReading'><?php echo Yii::t("ptc", "Criteria");?></td>
                                        </tr>
                                        <template v-if="scoreList.score_list_by_child[item['id']]['assessment'] && scoreList.score_list_by_child[item['id']]['assessment'].length!=0">
                                            <template v-for="(list,ids) in scoreList.score_list_by_child[item['id']]['assessment']">
                                                <tr>
                                                    <td>{{list.title}} <span v-if='list.title=="Math" || list.title=="CLA"' class='ml10'><?php echo Yii::t('ptc','Please note Math score is the number of LN') ?></span></td>
                                                    <td>{{list.score}}</td>
                                                    <td><input type="text" class="form-control" :value='list.target' v-model="scoreModel[item['id']].assessment[ids].target"></td>
                                                    <template v-if='scoreList.have_MAPReading'>
                                                        <td v-if='list.show_standard'>
                                                            <select class="form-control" v-model="scoreModel[item['id']].assessment[ids].standard">
                                                                <option value=''><?php echo Yii::t("global", "Please Select");?></option>
                                                                <option v-for='(standardList,type,idx) in gradeList' :value='standardList'>{{standardList}}</option>
                                                            </select>
                                                        </td>
                                                        <td  v-else></td>
                                                    </template>
                                                </tr>
                                            </template>
                                        </template>
                                        <template v-if="scoreList.score_list_by_child[item['id']]['courses'] && scoreList.score_list_by_child[item['id']]['courses'].length!=0">
                                            <template v-for="(list,ids) in scoreList.score_list_by_child[item['id']]['courses']">
                                                <tr v-for='(_item,i) in list.subdomain'>
                                                    <td>{{list.title}} - {{_item.subdomain}}</td>
                                                    <td>{{_item.score}}</td>
                                                    <td><input type="text" class="form-control" :value='_item.target' v-model="scoreModel[item['id']].courses[ids].subdomain[i].target"></td>
                                                    <template v-if='scoreList.have_MAPReading'>
                                                        <td  v-if='_item.show_standard'>
                                                            <select class="form-control" v-model="scoreModel[item['id']].courses[ids].subdomain[i].standard">
                                                                <option value=''><?php echo Yii::t("global", "Please Select");?></option>
                                                                <option v-for='(item,type,idx) in gradeList' :value='item'>{{item}}</option>
                                                            </select>
                                                        </td>
                                                        <td  v-else></td>
                                                    </template>
                                                </tr>
                                            </template>
                                        </template>
                                    </table>
                                    <div v-else class='mt10'>
                                        <div class="alert alert-warning mt10" role="alert" ><?php echo Yii::t("ptc", "暂无成绩");?></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" @click='saveScore'><?php echo Yii::t('global','Save') ?></button>
                        <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('global','Cancel') ?></button>
                    </div>
                </div>
            </div>
        </div>
        <!-- 编辑评语 -->
        <div class="modal fade" id="editComment" tabindex="-1" role="dialog" aria-labelledby="modal" aria-hidden="false"  data-backdrop="static" data-keyboard="false">
            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title" ><?php echo Yii::t('ptc','Comments') ?> <span id="remarks_t"></span></h4>
                    </div>
                    <div class="modal-body" v-if='Object.keys(commentsData).length!=0 '>
                        <div class='max-height scroll-box' :style='"max-height:"+maxHeight' v-if='ptcData.list'>
                            <div class='flex mb20' v-for='(list,key,index) in  ScoreDataList'>
                                <template v-if="ptcData.child[list['id']]">
                                    <div>
                                        <img :src="ptcData.child[list['id']].avatar" data-holder-rendered="true" class="avatar">
                                    </div>
                                    <div class='flex1 ml20'>
                                        <h4 class="media-heading font14 color3 mt20">{{ptcData.child[list['id']].name}} <span class="color6 font12 mt5 ">{{ptcData.child[list['id']].class_name}}</span></h4>
                                        <div class='mt10' v-if="ptcData.list[list['id']]['score'].length!=0">
                                            <div v-if="ptcData.list[list['id']]['score']['assessment'].length==0 && ptcData.list[list['id']]['score']['courses'].length==0">
                                                <div class='mt10 colorc text-center'><?php echo Yii::t("ptc", "No scores recorded");?></div>
                                            </div>
                                            <div v-else  class='assessment'>
                                                <div v-if="ptcData.list[list['id']]['score']['assessment'].length!=0">
                                                    <div v-for="(item,idx) in ptcData.list[list['id']]['score']['assessment']" class='relative mt5'>
                                                        {{item.title}} :
                                                        <span class="label label-default ml5 mt5">{{item.score}}<span v-if='item.target!=null  && item.target!=""'>/{{item.target}}</span></span>
                                                        <span class="label label-default ml10"  v-if='item.title=="MAP Reading" && item.standard!=null'>{{item.standard}}</span>
                                                        <span  v-if='item.target==null || item.target===""' class='yellow ml20'><span class='glyphicon glyphicon-exclamation-sign'></span> <?php echo Yii::t('ptc','Target score not set') ?></span>
                                                    </div>
                                                </div>
                                                <div v-if="ptcData.list[list['id']]['score']['courses'].length!=0">
                                                    <div v-for="(item,idx) in ptcData.list[list['id']]['score']['courses']" class='relative mt5'>
                                                        {{item.title}} :
                                                        <p v-for='(_item,i) in item.subdomain' class='ml20 mt5'>{{_item.subdomain}} :
                                                            <span class="label label-default ml5">{{_item.score}}<span v-if='_item.target!=null  && item.target!=""'>/{{_item.target}}</span></span>
                                                            <span class="label label-default ml10" v-if='_item.standard!=null'>{{_item.standard}}</span>
                                                            <span  v-if='item.target==null || item.target===""' class='yellow ml20'><span class='glyphicon glyphicon-exclamation-sign'></span> <?php echo Yii::t('ptc','Target score not set') ?></span>
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class='mt20'>
                                            <div class='mb5'><strong><?php echo Yii::t('ptc','Input improvement plan: ') ?> </strong></div>
                                            <textarea class="form-control" rows="8" v-model="commentsData[list['id']].school_comment" :value="commentsData[list['id']].school_comment" placeholder='<?php echo Yii::t('ptc','Input improvement plan: ') ?>'></textarea>
                                        </div>
                                    </div>
                                </template>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" @click='saveComments'><?php echo Yii::t('global','Save') ?></button>
                        <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('global','Cancel') ?></button>
                    </div>
                </div>
            </div>
        </div>
        <!-- 删除 -->
        <div class="modal fade" id="delModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
            <div class="modal-dialog modal-sm" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("newDS", "Delete");?></h4>
                    </div>
                    <div class="modal-body">
                        <div ><?php echo Yii::t("ptc", "Are you sure you want to delete?");?></div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>

                        <button type="button"  class="btn btn-primary" @click='delStu("model")'><?php echo Yii::t("newDS", "Delete");?></button>
                    </div>
                </div>
            </div>
        </div>
        <!-- 认领 -->
        <div class="modal fade" id="takeOwnership" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
            <div class="modal-dialog modal-sm" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title" v-if='ownershipType=="take"'><?php echo Yii::t("ptc", "Take Subject Ownership");?></h4>
                        <h4 class="modal-title" v-if='ownershipType=="replace"'><?php echo Yii::t("ptc", "Replace ownership");?></h4>
                        <h4 class="modal-title" v-if='ownershipType=="cancel"'><?php echo Yii::t("ptc", "Cancel ownership");?></h4>
                    </div>
                    <div class="modal-body">
                        <div v-if='ownershipType=="take"'><?php echo Yii::t("ptc", "Are you the owner of this subject?");?></div>
                        <div v-if='ownershipType=="replace"'><?php echo Yii::t("ptc", "Are you the owner of this subject?");?></div>
                        <div v-if='ownershipType=="cancel"'><?php echo Yii::t("ptc", "Cancel ownership will remove all data you input for this subject, proceed?");?></div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>

                        <button type="button"  class="btn btn-primary" v-if='ownershipType=="take"'  @click='claimSubjects("model")'><?php echo Yii::t("ptc", "OK");?></button>
                        <button type="button"  class="btn btn-primary" v-if='ownershipType=="replace"'  @click='replaceTeacher("model")'><?php echo Yii::t("ptc", "OK");?></button>
                        <button type="button"  class="btn btn-primary" v-if='ownershipType=="cancel"'  @click='unclaim("model")'><?php echo Yii::t("ptc", "OK");?></button>
                    </div>
                </div>
            </div>
        </div>
        <!-- 教师列表 -->
        <div class="modal fade" id="teacherList" tabindex="-1" role="dialog" aria-labelledby="modal" aria-hidden="false">
            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title" ><?php echo Yii::t('attends','Select a Teacher') ?> <span id="teacherList_t"></span></h4>
                    </div>
                    <div class="form-horizontal">
                        <div class="modal-body">
                            <a v-for='teacherId in normalList'  class='mb5 btn btn-default mr10' href='javascript:;' @click='showTeacher(teacherId)'>
                                {{teacherId.name}}
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 我得面谈 -->
        <div class="modal fade" id="myInterview" tabindex="-1" role="dialog" aria-labelledby="modal" aria-hidden="false">
            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title" ><?php echo Yii::t("ptc", "My Schedule");?><span id="teacherList_t"></span></h4>
                    </div>
                    <div class="form-horizontal">
                        <div class="modal-body" v-if='interviewChildList.childList'>
                            <div class='flex mb20 col-md-12 col-sm-4 mb10' v-if='initList.teacher_info'>
                                <div class='flex1'>
                                    <div class="media pb10">
                                        <div class="media-left pull-left media-middle">
                                            <a href="javascript:void(0)"><img :src="initList.teacher_info.photoUrl"  data-holder-rendered="true" class="addImg"></a>
                                        </div>
                                        <div class="media-body pt10 media-middle">
                                            <h4 class="media-heading font18 mt5 color3">{{initList.teacher_info.name}}</h4>
                                        </div>
                                    </div>
                                </div>
<!--                                <div>-->
<!--                                    <a href="javascript:;" @click="goMyPtcPage()" class="btn btn-primary" > --><?php //echo Yii::t('ptc','View details') ?><!--</a>-->
<!--                                </div>-->
                            </div>
                            <div class='col-md-4 col-sm-4 mb10' v-for='teacherId in interviewChildList.childList.items'  >
                                <div class="media">
                                    <div class="media-left pull-left media-middle">
                                        <a href="javascript:void(0)"><img :src="interviewChildList.childList.childInfo[teacherId.childId].avatar" data-holder-rendered="true" class="avatar"></a>
                                    </div>
                                    <div class="media-body pt10 media-middle">
                                        <h4 class="media-heading font14 color3">{{interviewChildList.childList.childInfo[teacherId.childId].name}} ({{teacherId.subject}})</h4>
                                        <div class="color6 font12 mt5 ">{{interviewChildList.childList.childInfo[teacherId.childId].class_name}}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class='clearfix'></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 教师ptc列表 -->
        <div class="modal fade" id="otherTeacher" tabindex="-1" role="dialog" aria-labelledby="modal" aria-hidden="false">
            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title" ><?php echo Yii::t('attends','Select a Teacher') ?> <span id="teacherList_t"></span></h4>
                    </div>

                    <div class="form-horizontal" v-if='teacherPtcList.teacherData'>
                        <div class="modal-body">
                            <div v-if='teacherPtcList.teacherData.length==0'>
                                <div class="alert alert-warning" role="alert" ><?php echo Yii::t("ptc", "No Data");?></div>
                            </div>
                            <div v-else>
                                <div v-if='teacherPtcList.teacherData[1]'>
                                    <p class='color3 font14'><strong><?php echo Yii::t("ptc", "ES");?></strong></p>
                                    <a v-for='(num,key,index) in teacherPtcList.teacherData[1]'  class='mb10 btn btn-default mr15' href='javascript:;' @click='myTeacher(key)'>
                                        {{teacherPtcList.teachersInfo[key].name}}<span class="badge ml10 bg7">{{num}}</span>
                                    </a>
                                </div>
                                <div v-if='teacherPtcList.teacherData[2]'>
                                    <p  class='color3 font14 mt20'><strong><?php echo Yii::t("ptc", "SS");?></strong></p>
                                    <a v-for='(num,key,index) in teacherPtcList.teacherData[2]'  class='mb10 btn btn-default mr15' href='javascript:;' @click='myTeacher(key)'>
                                        {{teacherPtcList.teachersInfo[key].name}}<span class="badge ml10 bg7">{{num}}</span>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--提示-->
        <div class="modal fade" id="alertModal1" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel" style="z-index: 9999;">
            <div class="modal-dialog modal-sm" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title" id="parentReplyLabel">
                            <?php echo Yii::t("ptc", "Warning"); ?>
                        </h4>
                    </div>
                    <div class="modal-body">
                        <div v-html='alertContent1'>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "OK");?></button>
                    </div>
                </div>
            </div>
        </div>
        <!--小学提升计划-->
        <div v-if = 'tabType=="myClass"' class="modal fade" id="plan2" tabindex="-1" role="dialog" aria-labelledby="modal" aria-hidden="false" data-backdrop="static" data-keyboard="false">
            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title" ><?php echo Yii::t('ptc','Comments') ?> <span id="remarks_t"></span></h4>
                    </div>
                    <div class="modal-body p024">
                        <div  class='row max-height mt20 scroll-box' :style='"max-height:"+maxHeight'>
                            <button type="button" v-if='ptcData.autoCompleteMap' class="btn btn-default ml15" @click="autoTemplate()">
                                <?php echo Yii::t('ptc','Auto Complete Map Data') ?>
                            </button>
                            <div class="pull-left ml15" v-if='!ptcData.autoCompleteMap'>
                                <button type="button" id="downloadTemplate" class="btn btn-default mr15" @click="downloadTemplate()">
                                    <?php echo Yii::t('ptc','Download class template') ?>
                                </button>
                                <label>
                                    <input type="button" id="i-check" value="<?php echo Yii::t('teaching','Import');?>" class="btn btn-primary mr15" onclick="$('#i-file').click();">
                                    <input type="file" name="file" id='i-file'  accept=".xls, .xlsx" @change="uploadPlan" style="display: none">
                                </label>
                            </div>
                            <div class="pull-right mr15 mb15">
                                <button type="button" class="btn btn-primary" @click="daochu()">
                                    <?php echo Yii::t('user', 'Export');?>
                                </button>
                            </div>
                            <div class='clearfix'></div>
                            <div class='flex pdf mb20' v-if='file_name!=""'>
                                <div class='flex1 file_name_t'>
                                    <span class='glyphicon glyphicon-paperclip mr5'></span> {{file_name}}
                                </div>
                            </div>
                            <div class="row ml10 mr10">
                                <table class="table table-bordered" id="classSubjectPlan">
                                    <thead>
                                    <tr class="active tab-center">
                                        <td width="20%">ID</td>
                                        <td><?php echo Yii::t('labels','Content');?></td>
                                        <td width="43%"><?php echo Yii::t("ptc", "Comments");?></td>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <template v-for='(item,key) in ScoreDataList' :key="key">
                                        <tr>
                                            <td  align="center" style="vertical-align: middle;">
                                                <p style="margin: 0;font-weight: 600">{{item.name}}</p>
                                                <p style="margin: 8px 0;">{{classPlanData.class_title}}</p>
                                                <p style="margin: 0">
                                                    <span class="label label-default">#{{item.id}}</span>
                                                </p>
                                                <div class='mapScore' v-if='ptcData.autoCompleteMap && classPlanData.assessment && classPlanData.assessment[item.id]'>
                                                    <div class='mapTitle'>{{currentSubject.subject_id==3?"MAP - Language Arts":currentSubject.subject_id==2?"MAP - Mathematics":""}}</div>
                                                    <div class='p10'>
                                                        <div class='mb2'>StartRIT：<span v-if='classPlanData.assessment[item.id].StartRIT==null' class='red'>N/A</span><span v-else>{{classPlanData.assessment[item.id].StartRIT}}</span></div>
                                                        <div class='mb2'>StartPercentile：<span v-if='classPlanData.assessment[item.id].StartPercentile==null' class='red'>N/A</span><span v-else>{{classPlanData.assessment[item.id].StartPercentile}}</span></div>
                                                        <div class='' v-if='currentSubject.subject_id==3'>F&P Level： <span v-if='classPlanData.assessment[item.id].FPLeve!==""' >{{classPlanData.assessment[item.id].FPLeve}}</span><span v-else class='red'>N/A</span></div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="pl-0 pt-0">
                                                <template v-if="ptcPlanDataNew[item.id]">
                                                    <div class="comment_div" v-for="(key2,item2) in classPlanData.show_title_order" :key="key2">
                                                        <div class="mb5 mt10 pl-2 font14">{{score_type[key2]['title']}}</div>
                                                        <!--是否达标--->
                                                        <el-select v-if="key2 === 'supportOrOnLevel'"
                                                                   v-model="ptcPlanDataNew[item.id]['score']['supportOrOnLevel']"
                                                                   @change="plan_comment(item.id)"
                                                                   :placeholder="score_type['supportOrOnLevel']['placeholder']"
                                                                   clearable
                                                                   class="w100">
                                                            <el-option
                                                                    v-for="val in pass"
                                                                    :key="val.value"
                                                                    :label="val.label"
                                                                    :value="val.value">
                                                            </el-option>
                                                        </el-select>
                                                        <!-----在校表现-------->
                                                        <el-select v-else-if="key2 === 'performance' || key2 === 'performance2'"
                                                                   v-model="ptcPlanDataNew[item.id]['score'][key2]"
                                                                   @change="plan_comment(item.id)"
                                                                   :placeholder="score_type[key2]['placeholder']"
                                                                   clearable
                                                                   class="w100">
                                                            <el-option
                                                                    v-for="val in gradeLevelPerformance"
                                                                    :key="val.value"
                                                                    :label="val.label"
                                                                    :value="val.label">
                                                            </el-option>
                                                        </el-select>
                                                        <!--------发展阶段---------->
                                                        <el-select v-else-if ="key2 === 'development'"
                                                                   v-model="ptcPlanDataNew[item.id]['score']['development']"
                                                                   @change="plan_comment(item.id)"
                                                                   :placeholder="score_type['development']['placeholder']"
                                                                   clearable
                                                                   class="w100">
                                                            <el-option
                                                                    v-for="val in stageOfReadingDevelopment"
                                                                    :key="val.value"
                                                                    :label="val.label"
                                                                    :value="val.value">
                                                            </el-option>
                                                        </el-select>
                                                        <!--其他-->
                                                        <el-input
                                                                v-else-if="key2 === 'percentile'"
                                                                type="textarea"
                                                                resize="none"
                                                                :rows="1"
                                                                resize="none"
                                                                :disabled = "score_type[key2]['disabled'] && ptcPlanDataNew[item.id]['score']['supportOrOnLevel'] == score_type[key2]['disabled']"
                                                                :placeholder="score_type[key2]['placeholder']"
                                                                @input="plan_comment(item.id)"
                                                                v-model="ptcPlanDataNew[item.id]['score'][key2]">
                                                        </el-input>
                                                        <el-input
                                                                v-else
                                                                type="textarea"
                                                                resize="none"
                                                                :rows="2"
                                                                resize="none"
                                                                :disabled = "score_type[key2]['disabled'] && ptcPlanDataNew[item.id]['score']['supportOrOnLevel'] == score_type[key2]['disabled']"
                                                                :placeholder="score_type[key2]['placeholder']"
                                                                @input="plan_comment(item.id)"
                                                                v-model="ptcPlanDataNew[item.id]['score'][key2]">
                                                        </el-input>
                                                    </div>
                                                </template>
                                            </td>
                                            <td>
                                                <el-input
                                                        v-if="ptcPlanDataNew[item.id]"
                                                        type="textarea"
                                                        :autosize="autosize"
                                                        v-model="ptcPlanDataNew[item.id]['comment']">
                                                </el-input>
                                            </td>
                                        </tr>
                                    </template>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('global','Cancel') ?></button>
                        <button type="button" class="btn btn-primary" @click='saveAllComments'><?php echo Yii::t('global','Save') ?></button>
                    </div>
                </div>
            </div>
        </div>
        <!--自动填充MAP数据-->
        <div class="modal fade" id="templateAuto" tabindex="-1" role="dialog" aria-labelledby="modal" aria-hidden="false" data-backdrop="static" data-keyboard="false">
            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title" ><?php echo Yii::t('ptc','Auto Complete Map Data') ?> <span id="remarks_t"></span></h4>
                    </div>
                    <div class="modal-body p024 scroll-box" :style='"max-height:"+maxHeight'>
                        <el-table
                            class='mt24 mb24'
                            v-if='MAPTemplate.tableHead'
                            :header-cell-style="{background:'#fafafa',color:'#333',fontSize:'12px'}"
                            :data="ScoreDataList"
                            @selection-change="handleSelectionChange"
                            ref="multipleTable"
                            style="width: 100%">
                            <el-table-column
                            type="selection"
                            width="35">
                            </el-table-column>
                            <el-table-column
                                prop="date"
                                label="<?php echo Yii::t('ptc','Student') ?>"
                                min-width="80">
                                <template slot-scope="scope">{{ scope.row.name }}</template>
                            </el-table-column>
                            <el-table-column
                                prop="name"
                                v-for='(list,i) in MAPTemplate.tableHead'
                                min-width="100">
                                <template slot="header" slot-scope="scope">
                                   <div style='word-wrap: break-word; word-break: normal;line-height:18px'> {{score_type[list].title}}</div>
                                </template>
                                <template slot-scope="scope">
                                    <div v-if='MAPTemplateArray[scope.row.id]'>

                                    
                                    <span class='tagScore' v-if='MAPTemplateArray[scope.row.id].assessmentScore[list]'>{{MAPTemplateArray[scope.row.id].assessmentScore[list]}}</span>
                                    <span v-if='MAPTemplateArray[scope.row.id].score[list]!=""'>{{ MAPTemplateArray[scope.row.id].score[list] }}</span>
                                    <span v-else  class='tagScoreNull'>N/A</span>
                                    </div>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('global','Cancel') ?></button>
                        <button type="button" class="btn btn-primary" @click='saveTemplateAuto'><?php echo Yii::t('message','Next') ?></button>
                    </div>
                </div>
            </div>
        </div>
        <!--中学提升计划-->
        <div v-if = 'tabType=="course"' class="modal fade" id="plan3" tabindex="-1" role="dialog" aria-labelledby="modal" aria-hidden="false" data-backdrop="static" data-keyboard="false">
            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title" ><?php echo Yii::t('ptc','Comments') ?> <span id="remarks_t"></span></h4>
                    </div>
                    <div class="modal-body">
                        <div  class='row max-height scroll-box' :style='"max-height:"+maxHeight'>
                            <div class="row ml15 mr10 mb10" v-if="ptcPlanDefComment['desc']">
                                <span><?php echo Yii::t('ptc','Current Template: ')?></span>
                                <span>{{ptcPlanDefComment['desc']}}</span>
                                <span class="glyphicon glyphicon-edit" @click="showSelectTemp()"></span>
                            </div>
                            <div class="pull-left ml15">
                                <button type="button" id="downloadTemplate" class="btn btn-default mr15" @click="downloadMiddleTemplate()">
                                    <?php echo Yii::t('ptc','Download class template') ?>
                                </button>
                                <label>
                                    <input type="button" id="i-check" value="<?php echo Yii::t('teaching','Import');?>" class="btn btn-primary mr15" onclick="$('#i-file').click();">
                                    <input type="file" name="file" id='i-file'  accept=".xls, .xlsx" @change="uploadPlan" style="display: none">
                                </label>
                            </div>
<!--                            <div class="pull-right mr15 mb15">-->
<!--                                <button type="button" class="btn btn-primary" @click="daochuMiddle()">-->
<!--                                    --><?php //echo Yii::t('user', 'Export');?>
<!--                                </button>-->
<!--                            </div>-->
                            <div class='clearfix'></div>
                            <div class="row ml10 mr10">
                                <table class="table table-bordered">
                                    <thead>
                                    <tr class="active tab-center">
                                        <td width="15%">ID</td>
                                        <td><?php echo Yii::t('labels','Content');?></td>
                                        <td width="43%"><?php echo Yii::t("ptc", "Comments");?></td>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <template v-for='(item,key) in ScoreDataList' :key="key">
                                        <tr>
                                            <td  align="center" style="vertical-align: middle;">
                                                <p style="margin: 0;font-weight: 600">{{item.name}}</p>
                                                <p v-if="ptcData.list && Object.keys(ptcData.list).length != 0 && ptcData.child[item['id']]" style="margin: 8px 0;">{{ptcData.child[item['id']].class_name}}</p>
                                                <p style="margin: 0">
                                                    <span class="label label-default">#{{item.id}}</span>
                                                </p>
                                            </td>
                                            <td class="pl-0 pt-0">
                                                <template v-if="ptcPlanDataNew[item.id]">
                                                    <div class="comment_div" v-for="(item2,key2) in score_type" :key="key2">
                                                        <div class="mb5 mt10 pl-2 font14">{{item2['title']}}</div>
                                                        <el-select v-if="key2 === 'Strength1' || key2 === 'Strength2' || key2 === 'Strength3' || key2 === 'Strength4'"
                                                                   v-model="ptcPlanDataNew[item.id]['score'][key2]"
                                                                   @change="plan_comment2(item.id)"
                                                                   :placeholder="score_type[key2]['placeholder']"
                                                                   clearable
                                                                   class="w100">
                                                            <el-option
                                                                    v-for="val in StrengthDevelopmentMap['Strength']"
                                                                    :key="val.value"
                                                                    :label="val.label"
                                                                    :value="val.value">
                                                            </el-option>
                                                        </el-select>
                                                        <el-select v-else-if="key2 === 'Development1' || key2 === 'Development2' || key2 === 'Development3' || key2 === 'Development4' "
                                                                   v-model="ptcPlanDataNew[item.id]['score'][key2]"
                                                                   @change="plan_comment2(item.id)"
                                                                   :placeholder="score_type[key2]['placeholder']"
                                                                   clearable
                                                                   class="w100">
                                                            <el-option
                                                                    v-for="val in StrengthDevelopmentMap['Development']"
                                                                    :key="val.value"
                                                                    :label="val.label"
                                                                    :value="val.value">
                                                            </el-option>
                                                        </el-select>
                                                        <el-select v-else-if="item2['option'] && (key2 === 'SupportEnrichmentStrategy' || key2 === 'CourseName' || key2==='AdditionalComment')"
                                                                   v-model="ptcPlanDataNew[item.id]['score'][key2]"
                                                                   @change="plan_comment2(item.id)"
                                                                   :placeholder="score_type[key2]['placeholder']"
                                                                   clearable
                                                                   class="w100">
                                                            <el-option
                                                                    v-for="val in StrengthDevelopmentMap[key2]"
                                                                    :key="val.value"
                                                                    :label="val.label"
                                                                    :title="val.label"
                                                                    :value="val.label">
                                                            </el-option>
                                                        </el-select>
                                                        <!--其他-->
                                                        <el-input
                                                                v-else
                                                                type="textarea"
                                                                resize="none"
                                                                :rows="4"
                                                                resize="none"
                                                                :placeholder="score_type[key2]['placeholder']"
                                                                @input="plan_comment2(item.id)"
                                                                v-model="ptcPlanDataNew[item.id]['score'][key2]">
                                                        </el-input>
                                                    </div>
                                                </template>
                                            </td>
                                            <td>
                                                <el-input
                                                        v-if="ptcPlanDataNew[item.id]"
                                                        type="textarea"
                                                        :autosize="autosize"
                                                        v-model="ptcPlanDataNew[item.id]['comment']">
                                                </el-input>
                                            </td>
                                        </tr>
                                    </template>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('global','Cancel') ?></button>
                        <button type="button" class="btn btn-primary" @click='saveAllComments2'><?php echo Yii::t('global','Save') ?></button>
                    </div>
                </div>
            </div>
        </div>
        <!--中学提升计划不使用模板直接录入评语-->
        <div v-if = 'tabType=="course"' class="modal fade" id="plan4" tabindex="-1" role="dialog" aria-labelledby="modal" aria-hidden="false" data-backdrop="static" data-keyboard="false">
            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title" ><?php echo Yii::t('ptc','Comments') ?> <span id="remarks_t"></span></h4>
                    </div>
                    <div class="modal-body">
                        <div  class='row max-height scroll-box' :style='"max-height:"+maxHeight'>
                            <div class="row ml15 mr10 mb10" v-if="ptcPlanDefComment['desc']">
                                <span><?php echo Yii::t('ptc','Current Template: ')?></span>
                                <span>{{ptcPlanDefComment['desc']}}</span>
                                <span class="glyphicon glyphicon-edit" @click="showSelectTemp()"></span>
                            </div>
                            <div class='clearfix'></div>
                            <div class="row ml10 mr10">
                                <table class="table table-bordered">
                                    <thead>
                                    <tr class="active tab-center">
                                        <td width="15%">ID</td>
                                        <td width="43%"><?php echo Yii::t("ptc", "Comments");?></td>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <template v-for='(item,key) in ScoreDataList' :key="key">
                                        <tr>
                                            <td  align="center" style="vertical-align: middle;">
                                                <p style="margin: 0;font-weight: 600">{{item.name}}</p>
                                                <p v-if="ptcData.list && Object.keys(ptcData.list).length != 0 && ptcData.child[item['id']]" style="margin: 8px 0;">{{ptcData.child[item['id']].class_name}}</p>
                                                <p style="margin: 0">
                                                    <span class="label label-default">#{{item.id}}</span>
                                                </p>
                                            </td>
                                            <td>
                                                <el-input
                                                        v-if="ptcPlanDataNew[item.id]"
                                                        type="textarea"
                                                        :autosize="{ minRows: 2, maxRows: 4}"
                                                        v-model="ptcPlanDataNew[item.id]['comment']">
                                                </el-input>
                                            </td>
                                        </tr>
                                    </template>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('global','Cancel') ?></button>
                        <button type="button" class="btn btn-primary" @click='saveAllComments2'><?php echo Yii::t('global','Save') ?></button>
                    </div>
                </div>
            </div>
        </div>


        <!--中学没有设置模板的科目用户自己选择-->
        <div v-if="middle_template" class="modal fade" id="selectTemplate" tabindex="-1" role="dialog" aria-labelledby="modal" aria-hidden="false" data-backdrop="static" data-keyboard="false">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title" ><?php echo Yii::t('ptc','选择评语模板') ?> <span id="remarks_t"></span></h4>
                    </div>
                    <div class="modal-body">
                        <div  class='row max-height mt20 scroll-box' :style='"max-height:"+maxHeight'>
                            <div v-fi="middle_template.template_list.length>0" >
                                <div class="col-md-6" v-for="(item,index) in middle_template.template_list" @click="selectTemplateId(item.id)">
                                    <a href="javascript:;" class="mb5 btn btn-default mr10">{{item.desc}}</a>
                                </div>
                            </div>
                            <div v-else>
                                <div class="alert alert-warning" role="alert" ><?php echo Yii::t("ptc", "No Data");?></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--上传并预览【选择excel以后的弹窗】-->
        <div class="modal fade" id="uploadPreview" tabindex="-1" role="dialog" aria-labelledby="modal" aria-hidden="false" data-backdrop="static" data-keyboard="false">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title" ><?php echo Yii::t('ptc','Upload and preview') ?> <span id="remarks_t"></span></h4>
                    </div>
                    <div class="modal-body">
                        <div  class='row max-height mt20 scroll-box' :style='"max-height:"+maxHeight'>
                            <table class="table mb0" v-if='ScoreDataList.length!=0'>
                                <tr>
                                    <th width='30%' class="pl24">
                                        <label class="checkbox-inline">
                                            <input type="checkbox" :value="previewChildIdAll" v-model='previewChildIdAll' @change='addAllStuId' ><strong><?php echo Yii::t('ptc','Import All') ?></strong>
                                        </label>
                                    </th>
                                    <th width='45%'><?php echo Yii::t('ptc','Student') ?></th>
                                    <th><?php echo Yii::t('ptc','Current comments') ?></th>
                                </tr>
                                <tr v-for="(item,key) in ScoreDataList" :key="key" >
                                    <td style="vertical-align:middle" class="pl24">
                                        <label>
                                            <input type="checkbox" :value="item.id" v-model='previewChildId'  @change='addOneStuId' :disabled="!xlx_child_ids.includes(item.id)" style="vertical-align:middle;margin: 0">
                                            <span class="" style="vertical-align:middle;"><?php echo Yii::t('teaching','Import')?></span>
                                        </label>
                                        <p v-if="!xlx_child_ids.includes(item.id)" style="color: #D9534F;margin-left: 17px"><?php echo Yii::t('ptc','No data found on this line in EXCEL')?></p>
                                    </td>
                                    <td>
                                        <p style="margin: 0;font-weight: 600">{{item.name}}</p>
                                        <p style="margin: 0;">{{classPlanData.class_title}}</p>
                                        <p style="margin: 0">
                                            <span class="label label-default">#{{item.id}}</span>
                                        </p>
                                    </td>
                                    <td style="vertical-align:middle">
                                        <div v-if="ptcPlanDataNew[item.id] && ptcPlanDataNew[item.id]['comment']">
                                            <span class="glyphicon glyphicon-exclamation-sign" style="color: #f0ad4e"></span>
                                            <span>
                                                <?php echo Yii::t('reg', 'Yes')?>
                                            </span>
                                        </div>
                                        <span v-else>
                                            <?php echo Yii::t('global','N/A')?>
                                        </span>
                                    </td>
                                </tr>
                            </table>
                            <div v-else>
                                <div class="alert alert-warning" role="alert" ><?php echo Yii::t("ptc", "No Data");?></div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('global','Cancel') ?></button>
                        <button type="button" class="btn btn-primary" @click='buildPreview'><?php echo Yii::t('ptc','Generate preview') ?></button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>
<script>
    $(function () {
        $('body').tooltip({
            selector: '[data-toggle="tooltip"]'
        });
    })
    $(document).ready(function () {
    // 通过该方法来为每次弹出的模态框设置最新的zIndex值，从而使最新的modal显示在最前面
        $(document).on('shown.bs.modal', '.modal.in', function(event) {
            var zIndex = 1040 + (10 * $('.modal:visible').length);
                $(this).css('z-index', zIndex);
                setTimeout(function() {
                    $('.modal-backdrop').not('.modal-stack').css('z-index', zIndex - 1).addClass('modal-stack');
                }, 0);
        }).on('hidden.bs.modal', '.modal', function(event) {
            if ($('.modal.in').size() >= 1) {
                $('body').addClass('modal-open')
            }
        })
    });
    var teacherId = '<?php echo  Yii::app()->request->getParam('teacherId', '')?>'
    var user_id = '<?php echo Yii::app()->user->getId()?>'
    var globalYearListByYid = {};//全局
    var globalInitListClassList ={};
    var container = new Vue({
        el: "#container",
        data: {
            autosize:{ minRows: 12, maxRows: 16},//input
            pass: [{
                value: 'SUPPORT',
                label: '<?php echo Yii::t('ptc', 'SUPPORT');?>'
            }, {
                value: 'ON LEVEL',
                label: '<?php echo Yii::t('ptc', 'ON LEVEL');?>'
            }],
            gradeLevelPerformance:[{
                    value: 'Below',
                    label: 'Below'
                }, {
                    value: 'At',
                    label: 'At'
                }, {
                    value: 'Above',
                    label: 'Above'
            }],
            stageOfReadingDevelopment:[{
                    value: 'Emergent',
                    label: 'Emergent'
                }, {
                    value: 'Early',
                    label: 'Early'
                }, {
                    value: 'Transitional',
                    label: 'Transitional'
                }, {
                    value: 'Fluent',
                    label: 'Fluent'
            }],
            StrengthDevelopmentMap:[],//中学课程可选课程评语评价标准
            user_id:user_id,
            branchId:'<?php echo $this->branchId; ?>',
            initDataDisabled:false,//禁用学年学期选项
            tabType:'myClass',
            noStu:false,
            initList:{},
            startYear:'<?php echo  Yii::app()->request->getParam('startyear', '')?>',
            yid:'<?php echo  Yii::app()->request->getParam('yid', '')?>',
            semester:'<?php echo  Yii::app()->request->getParam('semester', '')?>',
            classId:'',
            currentCourse:{},
            childDatas:{},
            allChild:false,
            child_id:[],
            ptcData:{},
            commentsData:{},
            scoreList:{},
            scoreType:'',
            scoreModel:{},
            delList:{},
            delKey:'',
            delIndex:'',
            subjectList:{},
            currentSubject:{},
            ownershipType:'',
            ownershipData:{},
            normalList:[],
            courseTeacherid:'',
            initLoading:false,
            subjectLoading:false,
            interviewChildList:{},
            popoverData:{},
            popoverIndex:'',
            gradeList:{},
            maxHeight:'',
            ptc_sum:'',
            teacher_department:'',
            teacherPtcList:{},
            ScoreDataList:[],
            studentDataList:[],
            currentYear:'',
            // showTab:"PtcPreparation",//控制选项卡 ptc准备 我的会议安排 班级ptc设置
            showTab:'<?php echo !in_array($_GET['showTab'],array('PtcPreparation','MyPTC','classPtcSet')) ? "PtcPreparation" : $_GET['showTab']?>',//"PtcPreparation",//控制选项卡 ptc准备 我的会议安排 班级ptc设置
            yearListByYid:{},
            startYearList: "",
            semesterList:'',
            initData:{},
            startdate:'',
            teacherInfo:{},
            itemData:{},
            currentDate:'all',
            teacherTitle:'',
            reserveData:{},
            teacher_id:'',
            my_ptc_teacher_id:'<?php echo $_GET['my_ptc_teacher_id']?>',
            showPtcSet:true,
            alertContent1:'',
            count:0,
            classPlanData:'',
            ptcPlanDefComment:'',//评论模板
            score_type:'',
            ptcPlanData:"",
            ptcPlanDataNew:"",//提交的数据
            file_name:"",//excel的名字
            loading_read:false,
            read_warning:false,
            ptcImportMap:"",//不同模板列对应的数据key
            xlx_json:"",//表格的原始数据
            xlx_child_ids:[],//表格中的孩子id
            previewChildId:[],//选择生成预览的学生id
            previewChildIdAll:true,//选择全部生成预览
            middle_template:'',//中学的模板信息
            middle_template_id:'',//选中的中学评语模板id
            MAPTemplate:{},
            MAPTemplateArray:[],
            multipleSelection:[],
            saveChildModal:true
        },
		created(){
            this.initDataF()
            this.maxHeight=$(window).height()-300+'px'
		},
        methods: {
            //小学获取提升计划2数据
            editComment2(){
                let that = this
                $.ajax({
                    url: '<?php echo $this->createUrl("getClassPlanData") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        start_year:that.startYear,
                        class_id:that.classId,
                        teacher_id:that.currentSubject.teacher_id,
                        ptc_subject:that.currentSubject.subject_id,
                        semester:that.semester,
                    },
                    success: function(data) {
                        that.MAPTemplate={}
                        that.MAPTemplateArray={}
                        that.classPlanData = data.data
                        that.score_type = data.data.score_type
                        that.ptcPlanData = data.data.ptc_plan_data
                        that.ptcPlanDefComment = data.data.def_comment
                        that.ptcImportMap = data.data.import_map
                        //没有成绩的学生设置默认值
                        for (const dataKey in that.ScoreDataList) {
                            let child_id = that.ScoreDataList[dataKey]['id']
                            if(!that.ptcPlanData[child_id]){
                                let def_score = {};
                                for (const scoreTypeKey in that.score_type) {
                                    def_score[scoreTypeKey] = ''
                                }
                                that.ptcPlanData[child_id] = {
                                    'comment':'',
                                    'student_id':child_id,
                                    'score':def_score
                                }
                            }
                        }
                        if(that.currentSubject.subject_id == 1){
                            that.autosize.minRows = 12;
                        }else if(that.currentSubject.subject_id == 2){
                            if(that.classPlanData.classtype == 'e5' || that.classPlanData.classtype == 'e6'){
                                that.autosize.minRows = 25;
                            }else{
                                that.autosize.minRows = 20;
                            }
                        }else if(that.currentSubject.subject_id == 3){
                            that.autosize.minRows = 16;
                        }
                        that.ptcPlanDataNew = JSON.parse(JSON.stringify(that.ptcPlanData));
                        that.file_name = ''
                        $('#plan2').modal('show');
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                        that.initDataDisabled = false
                    }
                })
                
            },
            addAllStuId(e){
                let that = this
                that.previewChildId=[]
                if(e.target.checked){
                    for(var key in that.ScoreDataList){
                        if(that.xlx_child_ids.includes(that.ScoreDataList[key]['id'])){
                            that.previewChildId.push(that.ScoreDataList[key]['id'])
                        }
                    }
                }else{
                    that.previewChildId=[]
                }
            },
            addOneStuId(){
                let that = this
                that.previewChildIdAll = that.previewChildId.length === that.xlx_child_ids.length
            },
            //导入评语基础数据
            uploadPlan(){
                let that = this
                //获取到选中的文件
                var file = document.querySelector("#i-file").files[0];
                //可重复传同一文件
                $("#i-file").val('')
                if(!file){
                    return false;
                }
                that.loading_read = true
                that.read_warning = false
                that.file_name = file.name
                that.xlx_child_ids = []
                that.previewChildId = []
                that.previewChildIdAll = true //默认全部选中
                var type = file.name.split('.');
                if (type[type.length - 1] !== 'xlsx' && type[type.length - 1] !== 'xls') {
                    resultTip({error: 'warning', msg: '只能选择xlsx或者xls文件导入'});
                    that.loading_read = false
                    return false;
                }
                let all_score_child_id = []
                for (const ScoreDataListKey in that.ScoreDataList) {
                    all_score_child_id.push(that.ScoreDataList[ScoreDataListKey]['id'])
                }
                const reader = new FileReader();
                reader.readAsBinaryString(file);
                reader.onload = (e) => {
                    const data = e.target.result;
                    const my_excel = window.XLS.read(data, {
                        type: 'binary'
                    });
                    const sheet2JSONOpts = {
                        defval: ''//给defval赋值为空的字符串
                    }
                    that.xlx_json = window.XLS.utils.sheet_to_json(my_excel.Sheets[my_excel.SheetNames[0]],sheet2JSONOpts);
                    //表格数据检查
                    let useless_child_id = []//记录表格中多出来的学生id
                    for (const xlxJsonKey in that.xlx_json) {
                        let column = 0;
                        for (const key in that.xlx_json[xlxJsonKey]) {
                            if(column === 0){
                                if(!that.xlx_json[xlxJsonKey][key]){
                                    resultTip({error: 'warning', msg: 'student id null'});
                                    return ;
                                }
                                let child_id = $.trim(that.xlx_json[xlxJsonKey][key])
                                that.xlx_child_ids.push(child_id)
                                that.previewChildId.push(child_id)
                                if(child_id && !all_score_child_id.includes(child_id)){
                                    useless_child_id.push(child_id)
                                }
                            }
                            column++
                        }
                    }
                    if(useless_child_id.length>0){
                        var str ='<div class="mb4">'+'<?php echo Yii::t('ptc','Student data error, the following students are not included in the class')?>'+'</div>'
                        for (let i=0;i<useless_child_id.length;i++){
                            str+='<div>'+useless_child_id[i]+'</div>'
                        }
                        that.alertContent1 = str
                        $("#alertModal1").modal('show')
                        return;
                    }
                   //展示弹窗
                    $("#uploadPreview").modal('show')
                }
            },
            autoTemplate(){
                let that = this
                let child_ids=[]
                for(var key in that.classPlanData.child_list_by_id){
                    child_ids.push(key)
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("autoCompleteMapData") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        start_year:that.startYear,
                        class_id:that.classId,
                        teacher_id:that.currentSubject.teacher_id,
                        ptc_subject:that.currentSubject.subject_id,
                        semester:that.semester,
                        child_ids:child_ids,
                    },
                    success: function(data) {
                        that.MAPTemplate=data.data
                        that.MAPTemplateArray=data.data.list
                        $('#templateAuto').modal('show');
                        that.$nextTick(() => {
                            that.ScoreDataList.forEach(row => {
                                that.$refs.multipleTable.toggleRowSelection(row, true);
                            });
                        });
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                        that.initDataDisabled = false
                    }
                })
            },
            saveTemplateAuto(){
                for(let i=0;i<this.multipleSelection.length;i++){
                    let key=this.multipleSelection[i].id
                    if(this.ptcPlanDataNew[key]){
                        this.ptcPlanDataNew[key]['score']=this.MAPTemplateArray[key].score
                        this.plan_comment(key)
                    }
                }
                $('#templateAuto').modal('hide');
            },
            handleSelectionChange(val) {
                this.multipleSelection = val;
            },
            //提升计划 拼接 that.ptcPlanDefComment
            plan_comment(child_id){
                let that = this
                let child_id_all_score = that.ptcPlanDataNew[child_id]['score']
                for (const score_type_key in that.score_type) {
                    if(that.score_type[score_type_key]['disabled'] && that.score_type[score_type_key]['disabled'] == that.ptcPlanDataNew[child_id]['score']['supportOrOnLevel']){
                        that.ptcPlanDataNew[child_id]['score'][score_type_key] = ''
                    }
                //     if(that.score_type[score_type_key]['disabled'] && that.score_type[score_type_key]['disabled'] != that.ptcPlanDataNew[child_id]['score']['supportOrOnLevel']){
                //         if(that.ptcPlanDataNew[child_id]['score'][score_type_key] == ''){
                //             that.ptcPlanDataNew[child_id]['score'][score_type_key] = that.ptcPlanData[child_id]['score'][score_type_key]
                //         }
                //     }
                }
                //是否达标（英语没有达标的字段）
                let plan_comment = '';
                if(that.currentSubject.subject_id == 3){
                    //所有的数据不为空展示提升计划
                    plan_comment = that.ptcPlanDefComment[0]['comment']
                    for (const childIdAllScoreKey in that.ptcPlanDataNew[child_id]['score']) {
                        if(!$.trim(that.ptcPlanDataNew[child_id]['score'][childIdAllScoreKey])){
                            plan_comment = '';
                            break;
                        }
                    }
                }else{
                    if(child_id_all_score['supportOrOnLevel'].replace(/\s/g, "") === 'SUPPORT' || child_id_all_score['supportOrOnLevel'].replace(/\s/g, "") === '学习支持'){
                        plan_comment = that.ptcPlanDefComment[0]['support_comment'] //支持
                    }else if(child_id_all_score['supportOrOnLevel'].replace(/\s/g, "") === 'ONLEVEL' || child_id_all_score['supportOrOnLevel'].replace(/\s/g, "") === '学习拓展'){
                        plan_comment = that.ptcPlanDefComment[0]['comment'] //拓展
                    }else{
                        plan_comment = '';
                    }
                }
                for (const childIdAllScoreKey in that.ptcPlanDataNew[child_id]['score']) {
                    let str = "%"+childIdAllScoreKey+"%";
                    if($.trim(that.ptcPlanDataNew[child_id]['score'][childIdAllScoreKey])){
                        plan_comment = plan_comment.replace(str, that.ptcPlanDataNew[child_id]['score'][childIdAllScoreKey]);
                    }else{
                        plan_comment = plan_comment.replace(str, '');
                    }
                }
                that.ptcPlanDataNew[child_id]['comment'] = plan_comment
            },
            //提升计划2模板下载
            downloadTemplate() {
                let that = this
                let url='<?php echo $this->createUrl('ptc/downloadTemplate', array('branchId' => $this->branchId)); ?>&class_id='+that.classId+'&teacher_id='+that.currentSubject.teacher_id+'&ptc_subject='+that.currentSubject.subject_id+'&semester='+that.semester+'&start_year='+that.startYear
                that.downloadExcel(url)
            },
            //小学导出提升计划2数据
            daochu(){
                let that = this
                let url='<?php echo $this->createUrl('ptc/downloadTemplate', array('branchId' => $this->branchId)); ?>&is_template=0&class_id='+that.classId+'&teacher_id='+that.currentSubject.teacher_id+'&ptc_subject='+that.currentSubject.subject_id+'&semester='+that.semester+'&start_year='+that.startYear
                that.downloadExcel(url)
            },
            //小学提交提升计划2
            saveAllComments(){
                let that=this
                let child_data = []
                for (const k in that.ScoreDataList) {
                    let child_id = that.ScoreDataList[k]['id']
                    child_data.push(that.ptcPlanDataNew[child_id])
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("savePlanData") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        class_id:that.classId,
                        semester:that.semester,
                        start_year:that.startYear,
                        child_data:child_data,
                        ptc_subject:that.currentSubject.subject_id
                    },
                    success: function(data) {
                        if(data.state=='success'){
                            that.ptcList()
                            resultTip({
                                msg: data.state
                            });
                        }else{
                            resultTip({error: 'warning', msg:data.message});
                        }
                        $('#plan2').modal('hide');
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            /**中学评语导入导出 start*/
            middleComment(){
                let that = this
                let exist_setting = that.middle_template.exist_setting
                if(exist_setting){
                    that.middle_template_id = that.middle_template.template_id
                    that.editComment3()
                }else{
                    that.showSelectTemp();
                }
            },
            showSelectTemp(){
                $("#selectTemplate").modal('show');
            },
            selectTemplateId(id){
                let that = this
                that.middle_template_id = id
                that.editComment3()
            },
            editComment3(){
                let that = this
                $.ajax({
                    url: '<?php echo $this->createUrl("getMiddlePlanData") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        course_id: that.currentCourse.id,
                        semester:that.semester,
                        start_year:that.startYear,
                        yid:that.yid,
                        ptc_subject:that.currentCourse.course_code,
                        teacher_id:that.courseTeacherid,
                        template_id:that.middle_template_id,
                    },
                    success: function(data) {
                        that.classPlanData = data.data
                        that.score_type = data.data.score_type
                        that.ptcPlanData = data.data.ptc_plan_data
                        that.ptcPlanDefComment = data.data.def_comment
                        that.ptcImportMap = data.data.import_map
                        that.StrengthDevelopmentMap = data.data.StrengthDevelopmentMap
                        //没有成绩的学生设置默认值
                        for (const dataKey in that.ScoreDataList) {
                            let child_id = that.ScoreDataList[dataKey]['id']
                            if(!that.ptcPlanData[child_id]){
                                let def_score = {};
                                for (const scoreTypeKey in that.score_type) {
                                    def_score[scoreTypeKey] = ''
                                }
                                that.ptcPlanData[child_id] = {
                                    'comment':'',
                                    'student_id':child_id,
                                    'score':def_score
                                }
                            }
                        }
                        that.autosize.minRows = 36;//评语输入框的高度
                        that.ptcPlanDataNew = JSON.parse(JSON.stringify(that.ptcPlanData));
                        that.file_name = ''
                        $("#selectTemplate").modal('hide');
                        if(that.ptcImportMap === ''){
                            //只输入评语
                            that.autosize.minRows = 2;//评语输入框的高度
                            $('#plan4').modal('show');
                        }else{
                            $('#plan3').modal('show');
                        }
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                        that.initDataDisabled = false
                    }
                })


            },
            plan_comment2(child_id){
                let that = this
                let plan_comment = that.ptcPlanDefComment['comment']//模板
                let template_type = that.classPlanData.template_type //1中文 3 英文
                let Strength = ['Strength1','Strength2','Strength3','Strength4']
                let Development = ['Development1','Development2','Development3','Development4']
                let Strength_exist = 0
                let Development_exist = 0
                let Criteria;
                let Strength_str = '';
                let Development_str = '';
                let score_ex = '';
                for (const childIdAllScoreKey in that.ptcPlanDataNew[child_id]['score']) {
                    let str = "%" + childIdAllScoreKey + "%"
                    if ($.trim(that.ptcPlanDataNew[child_id]['score'][childIdAllScoreKey])) {
                        Criteria = that.ptcPlanDataNew[child_id]['score'][childIdAllScoreKey]
                        if(Strength.includes(childIdAllScoreKey)){
                            if(template_type == 1){
                                //使用中文模板的课程将英文选项转成中文 显示到评语
                                Strength_str += that.score_type[childIdAllScoreKey]['option'][that.ptcPlanDataNew[child_id]['score'][childIdAllScoreKey]]+'，'
                            }else{
                                Strength_str += that.ptcPlanDataNew[child_id]['score'][childIdAllScoreKey]+', '
                            }
                        }
                        if(Development.includes(childIdAllScoreKey)){
                            if(template_type == 1){
                                //使用中文模板的课程将英文选项转成中文 显示到评语
                                Development_str += that.score_type[childIdAllScoreKey]['option'][that.ptcPlanDataNew[child_id]['score'][childIdAllScoreKey]]+'，'
                            }else{
                                Development_str += that.ptcPlanDataNew[child_id]['score'][childIdAllScoreKey]+', '
                            }
                        }
                        plan_comment = plan_comment.replace(str, Criteria);
                    } else {
                        plan_comment = plan_comment.replace(str, '')
                    }
                    if ($.trim(that.ptcPlanDataNew[child_id]['score'][childIdAllScoreKey]).length > 0) {
                        if (Strength.includes(childIdAllScoreKey)) {
                            Strength_exist = 1
                        }
                        if (Development.includes(childIdAllScoreKey)) {
                            Development_exist = 1
                        }
                    }
                    score_ex+=$.trim(that.ptcPlanDataNew[child_id]['score'][childIdAllScoreKey])
                }
                if($.trim(score_ex).length <= 0){
                    plan_comment = '';
                }
                //如果所有表现良好的列都是空白 则省略第二段
                if(!Strength_exist){
                    plan_comment = plan_comment.replace('\r\n您的孩子在以下MYP评估标准方面表现良好：', '')
                    plan_comment = plan_comment.replace("\r\nYour child’s MYP performance indicates the following area(s) of strength: ", '');
                    plan_comment = plan_comment.replace('%Strength%', '')
                }else{
                    if(template_type == 1){
                        plan_comment = plan_comment.replace('%Strength%',Strength_str.substring(0,Strength_str.length-1))
                    }else{
                        plan_comment = plan_comment.replace('%Strength%',Strength_str.substring(0,Strength_str.length-2))
                    }
                }
                //如果所有的需要进一步提高的列都是空白 则省略第三段
                if(!Development_exist){
                    plan_comment = plan_comment.replace('\r\n您的孩子在以下MYP评估标准方面需要提高：', '');
                    plan_comment = plan_comment.replace('\r\nYour child’s MYP performance indicates the following area(s) of development: ', '');
                    plan_comment = plan_comment.replace('\r\nYour child’s MYP performance indicates the following area(s) for development: ', '');
                    plan_comment = plan_comment.replace('%Development%', '')
                }else{
                    if(template_type == 1){
                        plan_comment = plan_comment.replace('%Development%',Development_str.substring(0,Development_str.length-1))
                    }else{
                        plan_comment = plan_comment.replace('%Development%',Development_str.substring(0,Development_str.length-2))
                    }
                }
                if(template_type == 1){
                    plan_comment = plan_comment.replace('%CourseName1%', that.classPlanData.subject_title.title_cn)
                }else{
                    plan_comment = plan_comment.replace('%CourseName1%', that.classPlanData.subject_title.title_en)
                }
                plan_comment = plan_comment.replace('%AdditionalComment%','')

                that.ptcPlanDataNew[child_id]['comment'] = plan_comment
            },
            saveAllComments2(){
                let that=this
                let child_data = []
                for (const k in that.ScoreDataList) {
                    let child_id = that.ScoreDataList[k]['id']
                    child_data.push(that.ptcPlanDataNew[child_id])
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("saveMiddlePlanData") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        child_data:child_data,
                        course_id: that.currentCourse.id,
                        semester:that.semester,
                        start_year:that.startYear,
                        yid:that.yid,
                        ptc_subject:that.currentCourse.course_code,
                        teacher_id:that.courseTeacherid,
                        template_id:that.middle_template_id,
                        template_type:that.classPlanData.template_type,
                    },
                    success: function(data) {
                        if(data.state === 'success'){
                            that.ptcList()
                            resultTip({
                                msg: data.state
                            });
                        }else{
                            resultTip({error: 'warning', msg:data.message});
                        }
                        $('#plan3').modal('hide');
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            downloadMiddleTemplate(){
                let that = this
                let url='<?php echo $this->createUrl('ptc/downloadMiddleTemplate', array('branchId' => $this->branchId)); ?>&yid='+that.yid+'&ptc_subject='+that.currentCourse.course_code+'&semester='+that.semester+'&start_year='+that.startYear+'&template_id='+that.middle_template_id
                that.downloadExcel(url)
            },
            daochuMiddle(){
                let that = this
                let url='<?php echo $this->createUrl('ptc/downloadMiddleTemplate', array('branchId' => $this->branchId)); ?>&is_template=0&yid='+that.yid+'&ptc_subject='+that.currentCourse.course_code+'&semester='+that.semester+'&start_year='+that.startYear+'&template_id='+that.middle_template_id
                that.downloadExcel(url)
            },
            /**中学评语导入导出 end*/

            //更新PtcPlanDataNew中的数据
            buildPreview(){
                let that = this
                if(!that.previewChildId.length){
                    resultTip({error: 'warning', msg: '<?php echo Yii::t('ptc','Please tick at least one item')?>'});
                    return ;
                }
                for (const xlxJsonKey in that.xlx_json) {
                    let column = 0;
                    let child_id = ''
                    let def_score = {};
                    for (const scoreTypeKey in that.score_type) {
                        def_score[scoreTypeKey] =  "";//默认为空
                    }
                    for (const key in that.xlx_json[xlxJsonKey]) {
                        if(column === 0){
                            child_id = $.trim(that.xlx_json[xlxJsonKey][key])
                        }else{
                            let excel_col = String.fromCharCode(65 + parseInt(column));// A B C D
                            //更新选择的学生
                            if(that.ptcPlanDataNew[child_id] && that.previewChildId.includes(child_id)){
                                if(that.ptcImportMap[excel_col]){
                                    if(that.showTab==='MyPTC'){
                                        if($.trim(that.xlx_json[xlxJsonKey][key]) === '学习支持'|| $.trim(that.xlx_json[xlxJsonKey][key]) ==='SUPPORT'){
                                            that.xlx_json[xlxJsonKey][key] = 'SUPPORT'
                                        }else if($.trim(that.xlx_json[xlxJsonKey][key]) === '学习拓展'|| $.trim(that.xlx_json[xlxJsonKey][key]) ==='ON LEVEL'){
                                            that.xlx_json[xlxJsonKey][key] = 'ON LEVEL'
                                        }
                                    }else{
                                        if($.trim(that.xlx_json[xlxJsonKey][key]) === '评估标准 A'|| $.trim(that.xlx_json[xlxJsonKey][key]) ==='Criteria A'){
                                            that.xlx_json[xlxJsonKey][key] = 'Criteria A'
                                        }else if($.trim(that.xlx_json[xlxJsonKey][key]) === '评估标准 B'|| $.trim(that.xlx_json[xlxJsonKey][key]) ==='Criteria B'){
                                            that.xlx_json[xlxJsonKey][key] = 'Criteria B'
                                        }else if($.trim(that.xlx_json[xlxJsonKey][key]) === '评估标准 C'|| $.trim(that.xlx_json[xlxJsonKey][key]) ==='Criteria C'){
                                            that.xlx_json[xlxJsonKey][key] = 'Criteria C'
                                        }else if($.trim(that.xlx_json[xlxJsonKey][key]) === '评估标准 D'|| $.trim(that.xlx_json[xlxJsonKey][key]) ==='Criteria D'){
                                            that.xlx_json[xlxJsonKey][key] = 'Criteria D'
                                        }
                                    }
                                    def_score[that.ptcImportMap[excel_col]] = that.xlx_json[xlxJsonKey][key]
                                }
                            }
                        }
                        column++
                    }
                    if(that.ptcPlanDataNew[child_id] && that.previewChildId.includes(child_id) && that.ptcPlanDataNew[child_id]['score']){
                        that.ptcPlanDataNew[child_id]['score'] = def_score
                        if(that.tabType === "course"){
                            that.plan_comment2(child_id)
                        }else{
                            that.plan_comment(child_id)
                        }
                    }
                }
                resultTip({msg: '<?php echo Yii::t('ptc', 'Successful Import');?>'});
                that.loading_read = false
                that.read_warning = false
                that.previewChildIdAll = false
                that.file_name = ''
                that.previewChildId = []
                $("#uploadPreview").modal('hide')
            },
            downloadExcel(url){
                let link = document.createElement('a');
                link.href = url;
                link.download = '';
                link.click();
                setTimeout(function() {
                    // 延时释放掉obj
                    URL.revokeObjectURL(link.href);
                    link.remove();
                }, 500);
            },
            html(data){
              return  data.replace(/\n/g, '<br/>')
            },
            toggleType(type){
                this.classId=''
                this.ptcData={}
                this.subjectList={}
                this.currentSubject={}
                this.currentCourse={}
                this.tabType=type
            },
            initDataF(){
                this.classId=''
                this.ptcData={}
                this.childDatas={}
                this.subjectList={}
                this.currentSubject={}
                this.currentCourse={}
                this.scoreList={}
                let that=this
                that.initDataDisabled = true
                if(this.showTab === 'PtcPreparation'){
                    this.getHomePageData()
                }
                if(this.showTab === 'MyPTC'){
                    this.currentDate="all"
                    this.getHomePageData()
                    this.myPtcData()
                }

                if( this.showTab === 'classPtcSet'){
                    this.classPtcSet()
                }

            },
            getHomePageData(){
                let that = this
                $.ajax({
                    url: '<?php echo $this->createUrl("getHomePageData") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        input_semester:that.semester,
                        input_yid:that.yid,
                        teacher_id:this.tabType=='course'?this.courseTeacherid:''
                    },
                    success: function(data) {
                        if(data.state=='success'){
                            that.initList=data.data
                            that.showPtcSet=data.data.showPtcSet
                            that.teacher_department=data.data.teacher_department
                            globalInitListClassList = data.data.class_list
                            if(data.data.teacher_department=='es'){
                                that.tabType='myClass'
                            }
                            if(data.data.teacher_department=='ss'){
                                that.tabType='course'
                            }
                            that.semester=data.data.semester.current
                            that.yearListByYid = data.data.year_list
                            globalYearListByYid = data.data.year_list//赋值全局使用
                            that.startYear=data.data.year_list.select_year
                            that.gradeList=data.data.score_standard_list
                            that.ptc_sum=data.data.teacher_info.ptc_sum
                            if(that.yid==''){
                                that.yid=data.data.year_list.currentYid
                            }
                            that.currentYear = data.data.year_list.startYearList[data.data.year_list.currentYid];
                        }else{
                            resultTip({error: 'warning', msg:data.message});
                        }
                        that.initDataDisabled = false
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                        that.initDataDisabled = false
                    }
                })
            },
            classChild(id){
                let that=this
                this.classId=id
                this.ptcData={}
                this.childDatas={}
				this.currentSubject={}
                this.scoreList={}
                if(this.tabType=='myClass'){
                    this.getSubject()
                }else{
                    this.ptcList()
                }
            },
            getSubject(){
                let that=this
                this.subjectLoading=true
                $.ajax({
                    url: '<?php echo $this->createUrl("subjectStatus") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        class_id: this.classId,
                        semester:that.semester,
                        start_year:that.startYear,
                    },
                    success: function(data) {
                        if(data.state=='success'){
						    that.subjectList=data.data
                            that.subjectLoading=false
                        }else{
                            resultTip({error: 'warning', msg:data.message});
                            that.subjectLoading=false
                        }
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                        that.subjectLoading=false
                    }
                })
            },
            replaceTeacher(type,list){
                let that=this
                if(type!='model'){
                    this.ownershipData=list
                    this.ownershipType=type
                    $('#takeOwnership').modal('show')
                    return
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("saveSubjectStatus") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        id:  this.ownershipData.schedule_id,
                        user_id: this.ownershipData.teacher_id,
                    },
                    success: function(data) {
                        if(data.state=='success'){
                           that.getSubject()
                           that.currentSubject={}
                           that.ptcData={}
                           that.ptc_sum=data.data.ptc_sum
                           resultTip({
                                msg: data.state
                            });
                            $('#takeOwnership').modal('hide')
                        }else{
                            var message = '';
                            if(data.data.class_id){
                                let class_name = ''
                                let time = data.data.date
                                globalInitListClassList.forEach((item,key)=>{
                                    if(data.data.class_id == item.id){
                                        class_name = (item.title+' ')
                                    }
                                })
                                var str ='<div class="mb4">'+data.message+'</div>'+
                                    '<div class="mb4"><span>'+class_name+'</span></div>'+
                                    '<div class="mb4"><span>'+time+'</span></div>'
                                that.alertContent1 = str
                                $("#alertModal1").modal('show')
                            }else{
                                message = data.message
                                resultTip({error: 'warning', msg:message});
                            }

                        }
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            unclaim(type,list){
                let that=this
                if(type!='model'){
                    this.ownershipData=list
                    this.ownershipType=type
                    $('#takeOwnership').modal('show')
                    return
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("delSubjectStatus") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        id:  this.ownershipData.schedule_id,
                        user_id: this.ownershipData.teacher_id,
                    },
                    success: function(data) {
                        if(data.state=='success'){
                           that.getSubject()
                           resultTip({
                                msg: data.state
                            });
                            that.ptc_sum=data.data.ptc_sum
                            that.currentSubject={}
                            that.ptcData={}
                            $('#takeOwnership').modal('hide')
                        }else{
                            resultTip({error: 'warning', msg:data.message});
                        }
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            claimSubjects(type,list){
                let that=this
                this.ownershipData=list
                this.saveChildModal=false
                $.ajax({
                    url: '<?php echo $this->createUrl("getStudentListByClass") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        class_id:that.classId,
                        semester:that.semester,
                        start_year:that.startYear,
                        teacher_id:list.teacher_id,
                        ptc_subject:list.subject_id
                    },
                    success: function(data) {
                        if(data.state=='success'){
                            that.childDatas=data.data
                            if(that.childDatas.student_list.length !=0){
                                sort_list=[]
                                for(var key in that.childDatas.student_list){
                                    sort_list.push({
                                        id:key,
                                        name:that.childDatas.student_list[key]['name']
                                    })
                                }
                                that.studentDataList  = that.sortTea(sort_list)
                                that.child_id=[]
                                $('#childModel').modal('show')
                            }
                        }else{
                            resultTip({error: 'warning', msg:data.message});
                            that.initLoading=false
                        }
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                        that.initLoading=false
                    }
                })
            },
            saveAddStuSub(){
                let that=this
                if(this.child_id.length==0){
                    resultTip({error: 'warning', msg: '<?php echo Yii::t("global", "Please Select");?>'});
                    return
                }
                var child_info=[]
                for(var i=0;i<this.child_id.length;i++){
                    child_info.push({
                        child_id:this.child_id[i],
                        level:this.childDatas.student_list[this.child_id[i]].level,
                        class_id:this.childDatas.student_list[this.child_id[i]].class_id,
                    })
                }
                that.ownershipData.teacher_id=that.childDatas.teacher_info.uid
				$.ajax({
                    url: '<?php echo $this->createUrl("operateSubjectStatus2") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        subject_id: this.ownershipData.subject_id,
                        class_id: this.classId,
                        teacher_id:that.ownershipData.teacher_id,
                        user_id:user_id,
                        semester:that.semester,
                        start_year:that.startYear,
                        child_info:child_info,
                    },
                    success: function(data) {
                        if(data.state=='success'){
						   that.ptc_sum=data.data.ptc_sum
                           that.subjectList.subject_status[that.ownershipData.subject_id].teacher_id=that.ownershipData.teacher_id
                           that.subjectList.subject_status[that.ownershipData.subject_id].schedule_id=data.data.schedule_id
                           that.subjectList.subject_status[that.ownershipData.subject_id].is_self=true
                           that.subjectList.teacher_info[that.ownershipData.teacher_id]=that.childDatas.teacher_info
                           if(that.tabType=='myClass'){
                                that.subjectCheck(that.ownershipData)
                            }else{
                                that.courseChild(that.ownershipData)
                            }
                           resultTip({
                                msg: data.state
                            });
                            $('#childModel').modal('hide');

                        }else{
                            resultTip({error: 'warning', msg:data.message});
                        }
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            subjectCheck(list){
                if(list.teacher_id==''){
                    resultTip({error: 'warning', msg: '<?php echo Yii::t("ptc", "Please take ownership first.");?>'});
                    return
                }
                this.currentSubject=list
                let that=this
                that.initLoading=true
                $.ajax({
                    url: '<?php echo $this->createUrl("getStudentListByClass") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        class_id:that.classId,
                        semester:that.semester,
                        start_year:that.startYear,
                        teacher_id:list.teacher_id,
                        ptc_subject:that.tabType=='myClass'?that.currentSubject.subject_id:that.currentCourse.course_code
                    },
                    success: function(data) {
                        if(data.state=='success'){
                            that.childDatas=data.data
                            if(that.childDatas.student_list.length !=0){
                                sort_list=[]
                                for(var key in that.childDatas.student_list){
                                    sort_list.push({
                                        id:key,
                                        name:that.childDatas.student_list[key]['name']
                                    })
                                }
                                that.studentDataList  = that.sortTea(sort_list)
                            }
                        }else{
                            resultTip({error: 'warning', msg:data.message});
                            that.initLoading=false
                        }
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                        that.initLoading=false
                    }
                })
                this.ptcList()
            },
            courseChild(list){
                let that=this
                this.currentCourse=list
                this.currentSubject={}
                this.subjectList={}
                this.allChild=false
				$.ajax({
                    url: '<?php echo $this->createUrl("getStudentListByCourse") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        course_id: list.id,
                        semester:that.semester,
                        start_year:that.startYear,
                        yid:that.yid
                    },
                    success: function(data) {
                        if(data.state=='success'){
                            for(var key in data.data.list){
                                if(data.data.list[key].score.length!=0 && data.data.list[key].score.assessment.length!=0){
                                    data.data.list[key].score.Math_CLA=[]
                                    data.data.list[key].score.Math_CLA_other=[]
                                    data.data.list[key].score.assessment.forEach((list) => {
                                        if(list.title=='Math' || list.title=='CLA' ){
                                            data.data.list[key].score.Math_CLA.push(list)
                                        }else{
                                            data.data.list[key].score.Math_CLA_other.push(list)
                                        }
                                    })
                                }
                            }
                           that.childDatas=data.data
                            if(that.childDatas.student_list.length !=0){
                                sort_list=[]
                                for(var key in that.childDatas.student_list){
                                    sort_list.push({
                                        id:key,
                                        name:that.childDatas.student_list[key]['name']
                                    })
                                }
                                that.studentDataList  = that.sortTea(sort_list)
                            }

                        }else{
                            resultTip({error: 'warning', msg:data.message});
                        }
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
                this.ptcList()
            },
            ptcList(){
                let that=this
                this.commentsData={}
                this.scoreList={}
                this.scoreType=''
                that.initLoading=true
                if(this.tabType=='myClass'){
                    $.ajax({
                        url: '<?php echo $this->createUrl("getPtcByClass") ?>',
                        type: "post",
                        dataType: 'json',
                        data: {
                            class_id: this.classId,
                            semester:that.semester,
                            start_year:that.startYear,
                            teacher_id:that.currentSubject.teacher_id,
                            ptc_subject:that.currentSubject.subject_id
                        },
                        success: function(data) {
                            if(data.state=='success'){
                                for(var key in data.data.list){
                                    if(data.data.list[key].score.length!=0 && data.data.list[key].score.assessment.length!=0){
                                        data.data.list[key].score.Math_CLA=[]
                                        data.data.list[key].score.Math_CLA_other=[]
                                        data.data.list[key].score.assessment.forEach((list) => {
                                            if(list.title=='Math' || list.title=='CLA' ){
                                                data.data.list[key].score.Math_CLA.push(list)
                                            }else{
                                                data.data.list[key].score.Math_CLA_other.push(list)
                                            }
                                        })
                                    }
                                }
                                that.ptcData=data.data
                                that.initLoading=false
                                if(that.ptcData.list.length !=0){
                                    sort_list=[]
                                    for(var key in that.ptcData.list){
                                        sort_list.push({
                                            id:key,
                                            name:that.ptcData.list[key]['name']
                                        })
                                    }
                                    that.ScoreDataList  = that.sortTea(sort_list)
                                }

                            }else{
                                resultTip({error: 'warning', msg:data.message});
                                that.initLoading=false
                            }
                        },
                        error:function(data){
                            resultTip({error: 'warning', msg: '请求错误'});
                            that.initLoading=false
                        }
                    })
                }else{
                    that.initLoading=true
                    $.ajax({
                        url: '<?php echo $this->createUrl("getPtcByCourse") ?>',
                        type: "post",
                        dataType: 'json',
                        data: {
                            course_id: this.currentCourse.id,
                            semester:that.semester,
                            start_year:that.startYear,
                            yid:that.yid,
                            ptc_subject:that.currentCourse.course_code,
                            teacher_id:that.courseTeacherid
                        },
                        success: function(data) {
                            if(data.state=='success'){
                                for(var key in data.data.list){
                                    if(data.data.list[key].score.length!=0 && data.data.list[key].score.assessment.length!=0){
                                        data.data.list[key].score.Math_CLA=[]
                                        data.data.list[key].score.Math_CLA_other=[]
                                        data.data.list[key].score.assessment.forEach((list) => {
                                            if(list.title=='Math' || list.title=='CLA' ){
                                                data.data.list[key].score.Math_CLA.push(list)
                                            }else{
                                                data.data.list[key].score.Math_CLA_other.push(list)
                                            }
                                        })
                                    }
                                }
                                that.ptcData=data.data
                                that.initLoading=false
                                that.middle_template = data.data.template
                                if(Object.keys(that.ptcData.list).length!=0){
                                    sort_list=[]
                                    for(var key in that.ptcData.list){
                                        sort_list.push({
                                            id:key,
                                            name:that.ptcData.list[key]['name']
                                        })
                                    }
                                    that.ScoreDataList  = that.sortTea(sort_list)
                                }
                            }else{
                                resultTip({error: 'warning', msg:data.message});
                                that.initLoading=false
                            }
                        },
                        error:function(data){
                            resultTip({error: 'warning', msg: '请求错误'});
                            that.initLoading=false
                        }
                    })
                }
            },
            addStu(){
                this.child_id=[]
                this.allChild=false
                this.saveChildModal=true
                $('#childModel').modal('show');
            },
            allStu(e){
                this.child_id=[]
                if(e.target.checked){
                    for(var key in this.childDatas.student_list){
                        if(this.childDatas.student_list[key].checked==0){
                            this.child_id.push(key)
                        }
                    }
                }else{
                    this.child_id=[]
                }
            },
            saveAddStu(){
                let that=this
                if(this.child_id.length==0){
                    resultTip({error: 'warning', msg: '<?php echo Yii::t("global", "Please Select");?>'});
                    return
                }
                var child_info=[]
                for(var i=0;i<this.child_id.length;i++){
                    child_info.push({
                        child_id:this.child_id[i],
                        level:this.childDatas.student_list[this.child_id[i]].level,
                        class_id:this.childDatas.student_list[this.child_id[i]].class_id,
                    })
                }
				$.ajax({
                    url: '<?php echo $this->createUrl("createStudentPtc") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        semester:that.semester,
                        start_year:that.startYear,
                        child_info:child_info,
                        ptc_subject:that.tabType=='myClass'?that.currentSubject.subject_id:that.currentCourse.course_code
                    },
                    success: function(data) {
                        if(data.state=='success'){
						   that.ptc_sum=data.data.ptc_sum
                           if(that.tabType=='myClass'){
                                that.subjectCheck(that.currentSubject)
                            }else{
                                that.courseChild(that.currentCourse)
                            }
                           resultTip({
                                msg: data.state
                            });
                            $('#childModel').modal('hide');

                        }else{
                            resultTip({error: 'warning', msg:data.message});
                        }
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            editComment(){
                if(this.ptcData.list && this.ptcData.list.length==0){
                    resultTip({error: 'warning', msg: '<?php echo Yii::t("global", "Please Select");?>'});
                    return
                }
                var test={}
                for(var key in this.ptcData.list){
                    if(this.ptcData.child[key]){
                        test[key]={
                            home_comment:this.ptcData.list[key].plan.home_comment,
                            school_comment:this.ptcData.list[key].plan.school_comment,
                            class_id:this.ptcData.child[key].classid,
                        }
                    }
                }
                this.commentsData=JSON.parse(JSON.stringify(test));
                $('#editComment').modal('show');
            },
            saveComments(){
                let that=this
				$.ajax({
                    url: '<?php echo $this->createUrl("saveRemarkData") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        semester:that.semester,
                        start_year:that.startYear,
                        child_info:that.commentsData,
                        ptc_subject:that.tabType=='myClass'?that.currentSubject.subject_id:that.currentCourse.course_code
                    },
                    success: function(data) {
                        if(data.state=='success'){
                            that.ptcList()
                           resultTip({
                                msg: data.state
                            });
                            $('#editComment').modal('hide');
                        }else{
                            resultTip({error: 'warning', msg:data.message});
                        }
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            childScore(){
                var childInfo={}
                for(var key in this.ptcData.list){
                    if(this.ptcData.child[key]){
                        childInfo[key]={
                            class_id:this.ptcData.child[key].classid,
                        }
                    }
                }
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("getScoreData") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        semester:that.semester,
                        start_year:that.startYear,
                        child_info:childInfo,
                        ptc_subject:that.tabType=='myClass'?that.currentSubject.subject_id:that.currentCourse.course_code
                    },
                    success: function(data) {
                        if(data.state=='success'){
                            that.scoreList=data.data
                            that.scoreModel = JSON.parse(JSON.stringify(that.scoreList.score_list_by_child));
                            $('#childScore').modal('show');
                        }else{
                            resultTip({error: 'warning', msg:data.message});
                        }
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            saveScore(){
                let that=this
                var datas={}
                for(var key in this.scoreModel){
                    if(this.scoreModel[key].length!=0){
                        if(this.scoreModel[key].assessment.length!=0){
                            for(var i=0;i<this.scoreModel[key].assessment.length;i++){
                                datas[this.scoreModel[key].assessment[i].assessment_score_id]={
                                    standard:this.scoreModel[key].assessment[i].standard,
                                    target:this.scoreModel[key].assessment[i].target
                                }
                            }
                        }
                        if(this.scoreModel[key].courses.length!=0){
                            for(var j=0;j<this.scoreModel[key].courses.length;j++){
                                for(var k=0;k<this.scoreModel[key].courses[j].subdomain.length;k++){
                                    datas[this.scoreModel[key].courses[j].subdomain[k].assessment_score_id]={
                                        standard:this.scoreModel[key].courses[j].subdomain[k].standard,
                                        target:this.scoreModel[key].courses[j].subdomain[k].target
                                    }
                                }
                            }
                        }
                    }
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("saveScoreData") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        semester:that.semester,
                        start_year:that.startYear,
                        child_info:datas,
                        // category:that.scoreType
                    },
                    success: function(data) {
                        if(data.state=='success'){
                            that.ptcList()
                            resultTip({
                                msg: data.state
                            });
                            $('#childScore').modal('hide');
                        }else{
                            resultTip({error: 'warning', msg:data.message});
                        }
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            otherTeacher(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("ptcSchedule/teacherPtcNum") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        semester:that.semester,
                        start_year:that.startYear,
                        yid:that.yid,
                    },
                    success: function(data) {
                        if(data.state=='success'){
                            that.teacherPtcList=data.data.teacherPtcNum
                            $('#otherTeacher').modal('show');
                        }else{
                            resultTip({error: 'warning', msg:data.message});
                        }
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })

            },
            myTeacher(id){
                let that=this
                that.showTab = 'MyPTC';
                that.teacherTitle=this.teacherPtcList.teachersInfo[id].name
                that.currentDate='all'
                that.my_ptc_teacher_id = id
                that.myPtcData()
                $("#otherTeacher").modal('hide');
            },
            delStu(data){
                if(data!='model'){
                    this.delList=data
                    $('#delModal').modal('show')
                    return
                }
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("delPtcItem") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        semester:that.semester,
                        start_year:that.startYear,
                        child_id:that.delList.id,
                        subject:that.tabType=='myClass'?that.currentSubject.subject_id:that.currentCourse.course_code
                    },
                    success: function(data) {
                        if(data.state=='success'){
                            that.ptc_sum=data.data.ptc_sum
                            if(that.tabType=='myClass'){
                                that.subjectCheck(that.currentSubject)
                            }else{
                                that.courseChild(that.currentCourse)
                            }
                            resultTip({
                                msg: data.state
                            });
                            $('#delModal').modal('hide')
                        }else{
                            resultTip({error: 'warning', msg:data.message});
                        }
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            chooseTeacher(){
                let that=this
                if(this.normalList.length!=0){
                    $("#teacherList").modal('show');
                    return
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("attendance/teacher") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        startyear:that.startYear,
                    },
                    success: function(data) {
                        normalList=[]
                        for(var key in data){
                            normalList.push({
                                id:key,
                                name:data[key]['name']
                            })
                        }
                        that.normalList=that.sortTea(normalList)
                        $("#teacherList").modal('show');
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            sortTea(list){
                list.sort((x,y)=>{
                    return x.name.localeCompare(y.name)
                })
                return list
            },
            showTeacher(data){
                this.courseTeacherid=data.id
                this.teacher_id=data.id
                this.my_ptc_teacher_id = data.id
                this.initDataF()
                $("#teacherList").modal('hide');
            },
            showPopover(index,list){
                if($('#sub'+index).is(':visible')){
                    $('#sub'+i).hide()
                    return
                }
                if(list.score=='0'){
                    return
                }
                this.popoverIndex=index
                var len=Object.keys(this.ptcData.list).length
                for(var i=0;i<len;i++){
                    $('#sub'+i).hide()
                }
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("ptcSchedule/mathDetail") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        score_ext:list.score_ext
                    },
                    success: function(data) {
                        if(data.state=='success'){
                            that.popoverData=data.data
                            $('#sub'+index).show()
                        }else{
                            resultTip({error: 'warning', msg:data.message});
                        }
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })

            },
            myInterviews(){
                if(this.ptc_sum==0){
                    return
                }
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("ptcSchedule/myPtcChild") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        startyear:that.startYear,
                        semester:that.semester,
                    },
                    success: function(data) {
                        if(data.state=='success'){
                            that.interviewChildList=data.data
                            $('#myInterview').modal('show')
                        }else{
                            resultTip({error: 'warning', msg:data.message});
                        }
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })

            },
            showType(type){
                this.showTab = type
                this.initDataF()
            },
            setAttendStatus(item,attended) {
                let that = this
                $.ajax({
                    url: '<?php echo $this->createUrl("ptcSchedule/setAttendStatus") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        startyear:that.startYear,
                        semester:that.semester,
                        child_id: item.child_id,
                        day: item.day,
                        start:item.start,
                        attended:attended,
                        class_id:item.child_class_id,
                    },
                    success: function (data) {
                        if (data.state == 'success') {
                            resultTip({"msg": "<?php echo Yii::t('global', 'Success!'); ?>"})
                            // that.myPtcData(that.my_ptc_teacher_id)
                            item.attended=attended
                            that.count++
                        } else {
                            resultTip({error: 'warning', msg: data.message});
                        }
                    },
                    error: function (data) {
                        that.classLoading = false
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            myPtcData(teacher_id){
                let that=this
                if(that.yearListByYid.startYearList){
                    that.startYear = that.yearListByYid.startYearList[that.yid]
                }
                var listData={}
                if(teacher_id){
                    this.teacher_id=teacher_id
                    this.my_ptc_teacher_id=teacher_id
                    listData={
                        teacher_id:teacher_id,
                        startyear:that.startYear,
                        semester:that.semester,
                        yid:that.yid
                    }
                }else{
                    listData={
                        teacher_id:that.my_ptc_teacher_id ?that.my_ptc_teacher_id: user_id ,
                        startyear:that.startYear,
                        semester:that.semester,
                        yid:that.yid
                    }
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("ptcSchedule/myPtc") ?>',
                    type: "post",
                    dataType: 'json',
                    data:listData,
                    success: function(data) {
                        if(data.state=='success'){
                            that.teacherInfo=data.data.teacherInfo
                            for(var key in data.data.ptcData.scoreData){
                                for(var item in data.data.ptcData.scoreData[key]){
                                     if(data.data.ptcData.scoreData[key][item].assessment && data.data.ptcData.scoreData[key][item].assessment.length!=0){
                                        data.data.ptcData.scoreData[key][item].Math_CLA=[]
                                        data.data.ptcData.scoreData[key][item].Math_CLA_other=[]
                                        data.data.ptcData.scoreData[key][item].assessment.forEach((list) => {
                                            if(list.title=='Math' || list.title=='CLA' ){
                                                data.data.ptcData.scoreData[key][item].Math_CLA.push(list)
                                            }else{
                                                data.data.ptcData.scoreData[key][item].Math_CLA_other.push(list)
                                            }
                                        })
                                    }
                                }
                            }
                            that.initData=data.data.ptcData
                            that.semesterList=data.data.semesterList
                            that.startYearList=data.data.startYearList
                            that.startYear=data.data.startYear
                            that.semester=data.data.semester
                            that.itemData=JSON.parse(JSON.stringify(data.data.ptcData.itemData))
                            that.showPtcSet = data.data.showPtcSet
                            that.$nextTick(()=>{
                                $("#startDate").datepicker({
                                    dateFormat: "yy-mm-dd ",
                                    onClose : function(dateText, inst){//当日期面板关闭后触发此事件（无论是否有选择日期）
                                        that.startdate = dateText
                                        that.myPtcData()
                                    }
                                })
                            })
                        }else{
                            resultTip({error: 'warning', msg:data.message});
                        }
                        that.initDataDisabled = false
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                        that.initDataDisabled = false
                    }
                })
            },
            showDate(key){
                this.currentDate=key
                if(key=='all'){
                    this.itemData=this.initData.itemData
                }else{
                    this.itemData={}
                    this.itemData[key]=this.initData.itemData[key]
                }
            },
            myPtcChooseTeacher(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("ptcSchedule/teacherPtcNum") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        semester:that.semester,
                        start_year:that.startYear,
                        yid:that.yid,
                    },
                    success: function(data) {
                        if(data.state=='success'){
                            that.teacherPtcList=data.data.teacherPtcNum
                            $('#teacherList').modal('show');
                        }else{
                            resultTip({error: 'warning', msg:data.message});
                        }
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            myPtcShowTeacher(id){
                this.teacherTitle=this.teacherPtcList.teachersInfo[id].name
                this.currentDate='all'
                this.my_ptc_teacher_id = id
                this.myPtcData()
                $("#teacherList").modal('hide');

            },
            time(){
                let that=this
                var teacher={}
                if(that.teacher_id!=''){
                    teacher={
                        teacher_id:that.teacher_id
                    }
                }else{
                    teacher={
                        teacher_id:that.my_ptc_teacher_id ?that.my_ptc_teacher_id: user_id ,
                    }
                }

                $.ajax({
                    url: '<?php echo $this->createUrl("ptcSchedule/teacherClassSchedule") ?>',
                    type: "post",
                    dataType: 'json',
                    data:teacher,
                    success: function(data) {
                        if(data.state=='success'){
                            that.reserveData=data.data
                            $('#reserve').modal('show');
                        }else{
                            resultTip({error: 'warning', msg:data.message});
                        }
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            myPtcShowPopover(index,list,type){
                if(list.score=='0'){
                    return
                }
                this.popoverIndex=index
                for(var key in this.initData.itemData){
                    for(var i=0;i<this.initData.itemData[key].length;i++){
                        $('#sub'+this.initData.itemData[key][i].child_id).hide()
                    }
                }
                
                for(var i=0;i<this.initData.noTimeData.length;i++){
                    $('#sub'+this.initData.noTimeData[i].child_id).hide()
                }
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("ptcSchedule/mathDetail") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        score_ext:list.score_ext
                    },
                    success: function(data) {
                        if(data.state=='success'){
                            that.popoverData=data.data
                            $('#sub'+index).show()
                        }else{
                            resultTip({error: 'warning', msg:data.message});
                        }
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            classPtcSet(){
                let that = this
                if(that.yearListByYid.startYearList){
                    that.startYear = that.yearListByYid.startYearList[that.yid]
                }
                $.ajax({
                    url:'<?php echo $this->createUrlReg('stepTemplate'); ?>',
                    type: 'get',
                    dataType:'json',
                    data:{
                        pageType:'classPtcSet',
                        startyear:that.startYear,
                        semester:that.semester,
                        yid:that.yid
                    },
                    success:function(data){
                        that.initDataDisabled = false
                        that.showPtcSet = data.data.showPtcSet
                        if(data.state=="success"){
                            $('.classPtcSet').html(data.data.template)
                        }
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                        that.initDataDisabled = false
                    }
                })
            },
		}
    })
    $(document).click(function(event) {
        if(container.ptcData && container.ptcData.list && container.ptcData.list){
            var len=Object.keys(container.ptcData.list).length
            for(var i=0;i<len;i++){
                $('#sub'+i).hide()
            }
        }  
        if(container.initData && container.initData.itemData && container.initData.itemData){
            for(var key in container.initData.itemData){
                for(var i=0;i<container.initData.itemData[key].length;i++){
                    $('#sub'+container.initData.itemData[key][i].child_id).hide()
                }
            }
        }
        if(container.initData && container.initData.noTimeData && container.initData.noTimeData){
            for(var i=0;i<container.initData.noTimeData.length;i++){
                    $('#sub'+container.initData.noTimeData[i].child_id).hide()
                }
        }

    });
</script>
