<?php

class PaymentController extends CController
{
	public function init(){
		Yii::import('application.components.policy.*');
		Yii::import('common.models.invoice.*');
		Yii::import('common.models.calendar.*');
		Yii::import('common.models.*');
        parent::init();
		
		$method = Yii::app()->request->getParam("method");
		$method =  addslashes(htmlspecialchars($method));
        
        $method =  str_replace('.', '_', $method);
        $func   =  'Api_' . $method;
		try{
			$this->$func();
		}
		catch(Exception $e){
			$this->ivy_get_err($method);
		}		

	}
	
    public function actionIndex(){
        Yii::app()->end();
    }
	
	public function Api_get_schooldays($branchId = '', $startYear=0){
		extract($_REQUEST);
		$test = PolicyApi::getSchoolMonth($branchId, $startYear);
		print_r($test);
	}
	
	public function Api_get_bind()
	{
		xm(PolicyApi::cancelLunch(1453,166,'BJ_IA',2012,'2013-06-07','UNDO'));
	}
	
	public function Api_get_amount()
	{
        $a = Yii::app()->request->getParam('a', 0);
        $invoiceModel = Invoice::model()->findByPk(117285);
        $tranModel = InvoiceTransaction::model()->findByPk(119440);
		$amount =$invoiceModel->amount-$invoiceModel->paidSum;
        $amountA = $tranModel->amount;
        $amountB = $a;
        $dealAmount = $amount-$amountA;
        echo $amount."<br>";
        print_r($amountB);
        
        if ($amount >= $amountB){
            echo '<br>OK1<br>';
        }
        if (number_format($amount, 2) >= number_format($amountB,2)){
            echo 'OK2<br>';
        }
        
        if ($amountB!=$dealAmount){
            echo "ok1..........";
        }else{
            echo "no1.............";
        }
        if ((double)$amountB != (double)$dealAmount){
             echo "ok2..........";
        }else{
             echo "no2.............";
        }
        if (number_format($amountB,2) != number_format($dealAmount,2)){
             echo "ok2..........";
        }else{
             echo "no2.............";
        }
	}
	
	public function Api_get_childAmount()
	{
		xm(PolicyApi::cancelChildLunchByDate(2815, '2013-09-02'));
	}
	
	public function Api_check_relation()
	{
		$policy = new IvyPolicy('pay', 2013, 'BJ_TT');
		$policy->checkRelation(3791,true);
	}
}