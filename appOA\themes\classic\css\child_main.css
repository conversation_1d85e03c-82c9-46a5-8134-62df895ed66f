li{
    list-style-type: none;
}
a{
    text-decoration: none;
}
div.form input.readonly,
div.form textarea.readonly,
div.form checkbox.readonly,
div.form select.readonly
{
    background: #D5D5D5;
    border: 1px solid #999;
    color:#666;
}
#page{
    margin: 10px;
    width: auto;
    min-width: 950px;
}
#header{
    margin: 10px;
    padding: 10px;
    border-bottom: 2px solid #F2F2F2;
    height: 120px;
}

#face{
    float: left;
    width: 160px;
    height: 120px;
}

#face img {
    border: 5px solid #FFFFFF;
    box-shadow: 2px 2px 3px #666666;
    height: 108px;
    margin: 0 20px 0 0;
}

#baseinfo{
    float: left;
}

#childinfo ul{
    height: 30px;
    margin-bottom: 1em;
}
#childinfo li{
    float: left;
    margin: 5px;
    width: 240px;
}
#childinfo  li.long{
    width: 360px;
}

#childinfo  li span{
    font-weight: bolder;
    display: inline-block;
    width: 80px;
}

#headmenu ul{
    margin-bottom: 1em;
}
#headmenu li{
    float: left;
    width: 120px;
    margin-right: 10px;
    background-color: #EDF0F2;
    padding: 5px;
    text-align: center;
}

#headmenu li:hover,#headmenu li.current{
    background-color: #1B7DBE;
}
#headmenu li:hover a,#headmenu li.current a{
    color: #FFFFFF;
}


#rightActions{
    float: right;
    margin-right: 20px;
    height: 124px;
}
#shortcuts{
    margin-top: 80px;
}
#main{
    min-height: 320px;
}

#leftmenu{
    float: left;
    width: 180px;
}
#leftmenu li{
    margin: 5px;
    background-color: #EDF0F2;
    padding: 5px;
    text-align: center;
}

#leftmenu li:hover,#leftmenu li.current{
    background-color: #1B7DBE;
}
#leftmenu li:hover a,#leftmenu li.current a{
    color: #FFFFFF;
}

#content{
    margin-left: 180px;
    border-left: 2px solid #F2F2F2;
    padding: 10px;
    min-height: 320px;
}


#footer{
    margin: 10px;
    text-align: center;
}


/**  基本信息  **/
.short-field{
    float: left;
}
.image-preview{
    float: left;
}

.row .privacy{
    width: 120px;
    float: left;
}
.row .privacy label{
    display: inline-block;
    margin-left: 5px;
}

.parent-filter{
}
.parent-filter li{
    float: left;
    width: 120px;
    text-align: center;
    margin: 10px;
}

.parent-filter li.active{
    font-weight: bolder;
}

#accout-info label{
    font-weight: bold;
    font-size: 0.9em;
    display: block;
}

/**  基本信息  **/


/**  财务信息  **/
.credit-balance{
    text-align: right;height: 40px;
    position: relative;
}
.credit-balance .title{
    font-size: 16px;
    font-weight: bold;
    line-height: 40px;
}
.credit-balance:hover .unit{
    color:#333;
}
.credit-balance:hover a{
    color:#333;
}
.credit-balance .desc a{
    color:red;
    padding: 0 4px;
}
.credit-balance .unit{
    font-size:16px;
    color:#999;
    font-weight:bold;
    padding-left:10px;
}
.credit-balance .credit{
    display:inline-block;
    width:70px;
    text-align:left;
    font-size: 18px;
}
.credit-balance .desc{
    position:absolute;
    text-align: left;
    width:450px;
    left: 250px;
    background:#C4C4C4;
    border-radius:4px;
    height:80px;
    border-bottom-color:#f2f2f2;
    border-right-color:#f2f2f2;
    z-index:2;
    padding: 4px 6px;
}
.credit-tuition .desc{
    top:0;
}
.credit-general .desc{
    top: -40px;
}
.credit-balance .hover{
    display:none;
}
.credit-balance .info:hover .hover{
    display:block;
}
.credit-balance em{
    position: absolute;
    left: 240px;
    display:inline-block;
    width: 18px;
    background:url(../images/left_focus.png) no-repeat left center transparent;
    height: 40px;
    z-index:1;
}
.credit-balance:hover .info{

}
.credit-balance .info{
    width: 240px !important;
    margin-right: 0 !important;
    padding-right: 10px !important;
}
.credit-tuition em{
    top:0;
}
.credit-general em{
    top:0;
}
.credit-tuition{
    color:#889E3D;
}
.credit-general{
    color:#F36601;
}





/**  财务信息  **/

