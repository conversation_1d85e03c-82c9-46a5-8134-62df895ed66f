<?php

class UserController extends WeChatVisualController
{
    public $childid;
    public $childObj;
    public $signPackage;
    public $photoUploading = true;
    //调用远程学期报告需要使用
    public $semesterKey = 'I3v4y7Onl8ine6Mi433ms';

    public function beforeAction($action)
    {
//		$jssdk = new JSSDK($this->appId, $this->appSecret);
//		$this->signPackage = $jssdk->GetSignPackage();
        $this->childid = isset($_COOKIE['childid']) ? $_COOKIE['childid'] : '' ;
        if ($this->childid && isset($this->myChildObjs[$this->childid])) {
            $this->childObj = $this->myChildObjs[$this->childid];
        } else {
            $this->childObj = end($this->myChildObjs);
            $this->childid = $this->childObj->childid;
        }
        return true;
    }

    public function actionIndex()
    {

    }

    public function actionBind()
    {
        $this->subPageTitle = "绑定系统帐号";
        Yii::import('application.modules.user.components.*');
        $status = 0;
        $errors = array();
        $wechatName= $this->openid;

        $model = new UserBindForm;
        if($wechatName){
            $weChatUserObj = $this->wechatUser;
            if(!is_null($weChatUserObj) && $weChatUserObj->valid){
                $startPath = Yii::app()->request->cookies['startPath'];
                $startUrl = 'javascript:wx.closeWindow();';
                if ($startPath && stripos($startPath, 'wechat/user/bind') === false) {
                    $startUrl = Yii::app()->createUrl($startPath) . "/?state=$this->state&openid=$this->openid&access_token=$this->access_token";
                }
                $status = 2;

                $this->render('bind', array('status'=>$status, 'startUrl'=>$startUrl));
                Yii::app()->end();
            }
            $model->wechatUname = $wechatName;
            $status = 1;
        }
        if(isset($_POST['UserBindForm'])) {
            $model->attributes = $_POST['UserBindForm'];
            if($model->validate()){
                $weChatUserObj = $this->wechatUser;
                if(is_null($weChatUserObj)){
                    $weChatUserObj = new WechatUser;
                }
                $weChatUserObj->openid = $model->wechatUname;
                $weChatUserObj->userid = $model->userid;
                $weChatUserObj->valid = 1;
                $weChatUserObj->updated = time();
                if(!$weChatUserObj->save()){
                    $status = 3;
                    $errors = current($weChatUserObj->getErrors());
                }else{
                    $status = 2;
                }
            } else{
                $status = 3;
                $errors = current($model->getErrors());
            }
        }

        $this->render('bind', array('status'=>$status, 'model'=>$model, 'errors'=>$errors));
    }

    public function actionUnbind()
    {
        if(Yii::app()->request->isPostRequest){
            $weChatUserObj = $this->wechatUser;
            $weChatUserObj->valid = 0;
            if($weChatUserObj->save()){
                $ret = array(
                    'state' => 'success'
                );
            }
            else{
                $ret = array(
                    'state' => 'fail',
                    'message' => '解除绑定失败'
                );
            }
            echo CJSON::encode($ret);
        }
    }

    public function actionStudent()
    {
        $this->subPageTitle = Yii::t('wechat', 'Child Info');
        $childObjs = $this->myChildObjs;
        $this->render('student',array('childObjs'=>$childObjs));
    }

    // 周报告
    public function actionJournal()
    {
        $this->subPageTitle = Yii::t('portfolio', 'Journal');
        $yid = Yii::app()->request->getParam('yid','');
        $childid = $this->getChildId();
        $childObj = $this->myChildObjs[$childid];
        $schoolId = $childObj->schoolid;
        if(!$childObj)
            return false;
        Yii::import('common.models.portfolio.*');
        Yii::import('common.models.calendar.*');

        //查找孩子所有学年
        $criteria = new CDbCriteria;
        $criteria->compare('childid', $childid);
        $criteria->compare('stat', 20);
        $criteria->order = 'startyear desc,weeknumber desc';
        $notesChild = NotesChild::model()->findAll($criteria);

        $calendarArr = array();
        $classArr = array();
        $noteArr = array();
        $unreadWeek = array();
        $weekDays = array();
        //每周照片数
        $photoNum = '';
        
        foreach ($notesChild as $note) {
            $calendarArr[$note->yid] = $note->startyear;
            $classArr[$note->yid][] = $note->classid;
            $noteArr[$note->yid][$note->weeknumber] = $note;
        }
        if ($this->state == 'ds' || $schoolId == 'BJ_OE') {
            // 查找孩子的分班记录
            Yii::import('common.models.portfolio.ChildStudyHistory');
            $classSchoolList = $this->state == 'ivy' ? CommonUtils::ivySchoolList() : CommonUtils::dsSchoolList();
            $credit = new CDbCriteria();
            $credit->compare('t.childid', $childid);
            $credit->compare('t.schoolid', $classSchoolList);
            $credit->compare('t.stat', 20);
            // 最早开通journal的校历
            $credit->compare('t.calendar', ">=155");
            $credit->order = 'timestamp ASC';
            $credit->with = 'calendarNonTogether';
            $classHistoryList = ChildStudyHistory::model()->findAll($credit);

            $yidHistoryList = array();
            foreach ($classHistoryList as $classHistory) {
                $yidHistoryList[$classHistory->calendarNonTogether->startyear] = $classHistory->calendar;
            }
            foreach ($yidHistoryList as $_year => $_yid) {
                if ($_year > date('Y')) {
                    continue;
                } 
                if ($_year == date('Y') && date("n") <= 7 ) {
                    continue;
                }
                if (!in_array($_year, $calendarArr)) {
                    $calendarArr[$_yid] = $_year;
                }
            }
        }
        arsort($calendarArr, SORT_NUMERIC);
        if(!$yid){
            $yid = current(array_keys($calendarArr));
        }
        $startYear = $calendarArr[$yid];

        if ($notesChild) {
            if (isset($classArr[$yid])) {
                $classid = $classArr[$yid];
                //相关media
                $criteria = new CDbCriteria();
                $criteria->compare('t.classid', $classid);
                $criteria->compare('t.yid', $yid);
                $criteria->compare('t.category', 'week');
                $criteria1 = new CDbCriteria();
                $criteria1->compare('t.childid', 0);
                $criteria1->compare('t.childid', $childid, false, 'OR');
                $criteria->mergeWith($criteria1);
                $criteria->order = 't.weight asc,t.id asc';
                ChildMedia::setStartYear($startYear);
                ChildMediaLinks::setStartYear($startYear);
                $childMediaModel = ChildMediaLinks::model()->with('photoInfo')->findAll($criteria);
                $weeknum = array();
                foreach ($childMediaModel as $childMedia) {
                    $weeknum[] = $childMedia->weeknum;
                }
                $photoNum = array_count_values($weeknum);
                // 是否有未阅读回复
                Yii::import('common.models.feedback.Comments');
                $credit = new CDbCriteria();
                $credit->compare('com_child_id', $childid);
                $credit->compare('com_yid', $yid);
                $credit->compare('com_ifread', 0);
                $credit->compare('com_user_type', 1);
                $credit->select = 'com_week_num';
                $comments = Comments::model()->findAll($credit);
                foreach ($comments as $comment) {
                    $unreadWeek[] = $comment->com_week_num;
                }
                $unreadWeek = array_unique($unreadWeek);
            }
        }

        $journalData = array();
        if (in_array($schoolId, CommonUtils::allSchoolList())) {
            Yii::app()->user->setId($this->wechatUser->userid);
            $data = array(
                'state' => $this->state,
                'yid' => $yid,
                'child_id' => $childid,
            );
            $res = CommonUtils::requestDsOnline('parent/journal/overview', $data);
            if (isset($res['code']) && $res['code'] == 0) {
                $journalData = $res['data'];
            }
        }

        if ($yid) {
            $criteria = new CDbCriteria();
            $criteria->compare('yid', $yid);
            $criteria->order = 'weeknumber DESC';
            $calendarWeek = CalendarWeek::model()->findAll($criteria);
            $weekDays = array();
            if($calendarWeek){
                foreach ($calendarWeek as $weekDay) {
                    $weekDays[$weekDay->weeknumber] = $weekDay->monday_timestamp;
                }
            }
        }

        $this->render('journal', array(
            'noteArr'=>$noteArr,
            'photoNum'=>$photoNum,
            'calendarArr'=>$calendarArr,
            'yid'=>$yid,
            'childObj'=>$childObj,
            'unreadWeek'=>$unreadWeek,
            'weekDays' => $weekDays,
            'journalData' => $journalData
        ));
    }

    // 周报告详情
    public function actionArticle()
    {
        $this->subPageTitle = Yii::t('portfolio', 'Journal');

        $childid = Yii::app()->request->getParam('childid', '');
        if(!$childid){
            $childid = $this->getChildId();
        }
        $classid = Yii::app()->request->getParam('classid', '');
        $yid = Yii::app()->request->getParam('yid', '');
        $weeknum = Yii::app()->request->getParam('weeknum', '');
        $startYear = Yii::app()->request->getParam('startyear', '');

        $childObj = $this->myChildObjs[$childid];
        if(!$childObj || !$classid || !$yid || !$weeknum || !$startYear)
            return false;

        Yii::import('common.models.portfolio.*');
        Yii::import('common.models.calendar.*');
        Yii::import('common.models.feedback.Comments');
        //周反馈未读
        $com_ifread = 0;
        $criteria = new CDbCriteria;
        $criteria->compare('com_child_id',$childid);
        $criteria->compare('com_ifread',$com_ifread);
        $feedbackModels = Comments::model()->findAll($criteria);
        // 判断幼儿、小学、中学的校园新闻
        $type = NotesSchCla::NOTESSCHCLA_TYPE_SCHOOLNEW;
        if (CommonUtils::isMiddleSchool($classid)) {
            $type = NotesSchCla::NOTESSCHCLA_TYPE_MIDDLE_SCHOOLNEW;
        } elseif (CommonUtils::isGradeSchool($classid, true)) {
            $type = NotesSchCla::NOTESSCHCLA_TYPE_GRADE_SCHOOLNEW;
        }
        //学校新闻
        $criteria = new CDbCriteria();
        $criteria->compare('t.yid', $yid);
        $criteria->compare('t.schoolid', $childObj->schoolid);
        $criteria->compare('t.classid', 0);
        $criteria->compare('t.weeknumber', $weeknum);
        $criteria->compare('t.type', $type);
        $criteria->compare('t.stat', 20);
        $notesSchool = NotesSchCla::model()->find($criteria);
        //班级报告
        $criteria = new CDbCriteria();
        $criteria->compare('t.yid', $yid);
        $criteria->compare('t.classid', $classid);
        $criteria->compare('t.weeknumber', $weeknum);
        $criteria->compare('t.type', NotesSchCla::NOTESSCHCLA_TYPE_CLASSNEW);
        $criteria->compare('t.stat', 20);
        if (CommonUtils::isGradeSchool($classid, true)){
            $notesClass = NotesSchCla::model()->findAll($criteria);
        }else{
            $notesClass[] = NotesSchCla::model()->find($criteria);
        }
        //个人报告
        $criteria = new CDbCriteria;
        $criteria->compare('childid', $childid);
        $criteria->compare('classid', $classid);
        $criteria->compare('t.weeknumber', $weeknum);
        $criteria->compare('stat', 20);
        $notesChild = NotesChild::model()->find($criteria);
        //相关media
        $criteria = new CDbCriteria();
        $criteria->compare('t.classid', $classid);
        $criteria->compare('t.weeknum', $weeknum);
        $criteria->compare('t.yid', $yid);
        $criteria->compare('t.category', 'week');
        $criteria1 = new CDbCriteria();
        $criteria1->compare('t.childid', 0);
        $criteria->compare('t.weeknum', $weeknum);
        $criteria1->compare('t.childid', $childid, false, 'OR');
        $criteria->mergeWith($criteria1);
        $criteria->order = 't.weight asc,t.id asc';
        ChildMedia::setStartYear($startYear);
        ChildMediaLinks::setStartYear($startYear);
        $childMediaModel = ChildMediaLinks::model()->with('photoInfo')->findAll($criteria);
        // 是否存在反馈
        Yii::import('common.models.feedback.Comments');
        $criteria = new CDbCriteria;
        $criteria->compare('com_child_id',$childid);
        $criteria->compare('com_week_num',$weeknum);
        $criteria->compare('com_yid',$yid);
        $feedbacka = Comments::model()->count($criteria);
        // 未回复反馈数量
        $criteria = new CDbCriteria();
        $criteria->compare('com_ifread', 0);
        $criteria->compare('com_child_id', $childid);
        $criteria->compare('com_week_num', $weeknum);
        $criteria->compare('com_yid',$yid);
        $ifread = Comments::model()->count($criteria);
        $this->render('article', array(
            'weeknum'=>$weeknum,
            'notesSchool'=>$notesSchool,
            'notesClass'=>$notesClass,
            'notesChild'=>$notesChild,
            'childMediaModel'=>$childMediaModel,
            'feedbacka'=>$feedbacka,
            'ifread'=>$ifread,
            'schoolid'=>$childObj->schoolid,
        ));
    }

    // 每周餐谱
    public function actionCateringMenu()
    {
        $this->subPageTitle = Yii::t('portfolio', 'Lunch Menu');
        $childid = $this->getChildId();
        $childObj = $this->myChildObjs[$childid];
        if(!$childObj)
            return false;

        // 餐谱
        Yii::import('common.models.lunchmenu.*');
        Yii::import('common.models.calendar.*');

        $schoolObj = $childObj->school;
        $yid = $schoolObj->schcalendar;
        $criteria = new CDbCriteria();
        $criteria->compare('yid', $yid);
        $calendarWeek = CalendarWeek::model()->findAll($criteria);
        $weekDays = array();
        if($calendarWeek){
            foreach ($calendarWeek as $weekDay) {
                $weekDays[$weekDay->weeknumber] = $weekDay->monday_timestamp;
            }
        }

        $schedule = array();
        $criteria = new CDbCriteria();
        $criteria->compare('t.yid', $yid);
        $criteria->compare('t.schoolid', $childObj->schoolid);
        $criteria->compare('t.menu_id', '>0');
        $criteria->order = 'week_num desc';
        $links = CateringMenuWeeklink::model()->with(array('common', 'allergy'))->findAll($criteria);
        foreach($links as $link){
            $schedule[$link->week_num] = array(
                'common' => $link->menu_id,
                'allergy' => $link->allergy_id,
            );
        }
        // 取消午餐
        Yii::import('common.models.invoice.*');
        $schoolId = $childObj->schoolid;
        $yid = Branch::model()->getBranchInfo($schoolId, 'schcalendar');
        $CfgFeeType = Mims::LoadConfig('CfgFeeType');
        $feeType = $CfgFeeType["lunch"]["sign"];
        // $feeObj = FeemgtSchoolConfig::model()->getSchoolFeeConfig($schoolId, $yid, $feeType);
        $invoiceDays = array();
        $refundLunchDays = array();
        //判断是否有午餐政策
        // if (empty($feeObj) || ($feeObj->lunch_payment_type == 30)) {
        // } else{
            Yii::import('common.models.calendar.*');
            $startDate = strtotime(date('Y-m-d',time()));
            if (date('H', time()) >= 9) {
                $startDate += 24*3600;
            }
            //孩子的餐费信息
            $childServiceInfo = ChildServiceInfo::model()->getChildFeeInfo($childid, $feeType, $yid, $startDate);
            if ($childServiceInfo) {
                $endDate = end($childServiceInfo)->enddate;
                //学校的上学日
                $schoolDays = CalendarSchoolDays::model()->getCalendarSchoolDay($yid, $startDate, $endDate);
                $schoolDaysArr =array();
                foreach ($schoolDays as $schoolDay) {
                    $schoolDaysArr[$schoolDay->month] = explode(',', $schoolDay->schoolday_array);
                }
                //餐费退费信息
                $refundLunch = RefundLunch::model()->getRefundLunch($childid, $startDate, $endDate);
                foreach ($refundLunch as $v) {
                    $refundLunchDays[] = $v->target_date;
                }
                foreach ($childServiceInfo as $serviceInfo) {
                    $tmpDate = $serviceInfo->startdate > $startDate ? $serviceInfo->startdate : $startDate;
                    while ($tmpDate <= $serviceInfo->enddate) {
                        if(in_array(date('d', $tmpDate), $schoolDaysArr[date('Ym', $tmpDate)]))
                            $invoiceDays[] = date('Ymd', $tmpDate);
                        $tmpDate += 24*3600;
                    }
                }
                $invoiceDays = array_unique($invoiceDays);
            }
        // }

        $this->render('cateringMenu', array(
            'childObj' => $childObj,
            'schedule' => $schedule,
            'weekDays' => $weekDays,

            "yid" => $yid,
            "feeType" => $feeType,
            "childid" => $childid,
            "schoolId" => $schoolId,
            "schoolObj" => $schoolObj,
            "invoiceDays" => $invoiceDays,
            "refundLunchDays" => $refundLunchDays,
        ));
    }

    // 餐谱 2019-06-20 新
    public function actionCateringMenuDetail(){
        $this->subPageTitle = Yii::t('portfolio', 'Lunch Menu');

        Yii::import('common.models.calendar.CalendarWeek');
        Yii::import('common.models.child.ChildMisc');
        Yii::import('common.models.lunchmenu.*');
        Yii::import('common.models.calendar.*');

        $childid = $this->getChildId();
        $childObj = $this->myChildObjs[$childid];
        if(!$childObj)
            return false;

        $schoolObj = $childObj->school;
        $yid = $schoolObj->schcalendar;
        $childmisc = ChildMisc::model()->findByPk($childid);

        $week= Yii::app()->request->getParam('week', '');
        $weekList = array();
        if(in_array($childObj->schoolid, array('BJ_DS','BJ_SLT'))){
            $criteria = new CDbCriteria();
            $criteria->compare('schoolid', $childObj->schoolid);
            $criteria->compare('yid', $yid);
            $criteria->order = 'week_num ASC';
            $criteria->index = 'week_num';
            $weeklink = CateringMenuWeeklink::model()->findAll($criteria);


            $morenWeek = array();
            if ($weeklink) {
                $startData = reset($weeklink);
                $endData = end($weeklink);
                $time = $startData->monday_timestamp;
                for ($i = $startData->week_num; $i <= $endData->week_num; $i++) {
                    $weekList[$i] = sprintf(Yii::t('global', 'Week %s (%s - %s)'), $i, CommonUtils::formatDateTime($time), CommonUtils::formatDateTime($time + 7 * 24 * 3600 - 1));
                    $morenWeek[$time] = $i;
                    $time += 86400 * 7;
                }
            }

            $mondayTime = mktime(0, 0, 0, date('m'), date('d') - date('w') + 1, date('Y'));

            if (!$week) {
                $week = isset($morenWeek) ? $morenWeek[$mondayTime] : array();
            }

        }else {
            $weekList = CalendarWeek::weeks($yid);
            $weekpk = array_keys($weekList);
            if (!$week)
                $week = end($weekpk);

        }

        $criteria = new CDbCriteria();
        $criteria->compare('schoolid', $childObj->schoolid);
        $criteria->compare('yid', $yid);
        $criteria->compare('week_num', $week);
        /*if(!in_array($childObj->schoolid, array('BJ_DS','BJ_SLT'))) {
            $criteria->compare('status', 1);
        }*/
        $criteria->compare('status', 1);
        $menuLink = CateringMenuWeeklink::model()->find($criteria);

        $menuId = 0;
        if ($menuLink){
            $menuId = (isset($childmisc->sign_allergy) && $childmisc->sign_allergy) ? $menuLink->allergy_id : $menuLink->menu_id;
        }
        $commonDetails = array();
        $weekday = array('mon', 'tue', 'wed', 'thu', 'fri');
        if ($menuId){
            $menu = CateringMenu::model()->findByPk($menuId);
            $weekday = ($menu->week_cate) ? unserialize($menu->week_cate) : array('mon', 'tue', 'wed', 'thu', 'fri');

            $criteria = new CDbCriteria();
            $criteria->compare('menu_id', $menuId);
            $criteria->compare('category', unserialize($menu->menu_cate));
            $criteria->order='weight asc';
            $menuDetail = CateringMenuDetail::model()->with('termInfo')->findAll($criteria);
            foreach ($menuDetail as $mD){
                $commonDetails[$mD->weekday][$mD->category] = array(
                    'content' => $mD->food_list,
                    'photo' => ($mD->photo) ? Yii::app()->params['uploadBaseUrl'] . 'lunch/' . $mD->photo : "",
                );
            }
        }
        $lunchs = array();
        $lunchModels = Term::model()->lunch()->findAll();
        foreach($lunchModels as $lunch){
            $lunchs[$lunch->diglossia_id] = CommonUtils::autoLang($lunch->cntitle, $lunch->entitle);
        }

        $getWeekDayName = CateringMenu::getWeekDayName();

        foreach ($weekday as $val){
            $weekDays[$val] = $getWeekDayName[$val];
        }

        if(Yii::app()->request->isPostRequest){
            echo json_encode(array(
                'commonDetails' => $commonDetails,
                'weeks' => $weekList,
                'weekday' => $weekDays,
                'lunchs' => $lunchs,
                'week' => $week,
            ));
        }else{
            $this->render('cateringMenuDetail', array(
                'weeks' => $weekList,
                'weekday' => $weekDays,
                'commonDetails' => $commonDetails,
                'lunchs' => $lunchs,
                'week' => $week,
            ));
        }
    }

    // 餐谱详情 暂时无用 2019 - 06 -20
    public function actionCateringMenuDetails()
    {
        $this->subPageTitle = Yii::t('portfolio', 'Lunch Menu');

        $childid = $this->getChildId();
        $childObj = $this->myChildObjs[$childid];
        if(!$childObj)
            return false;

        Yii::import('common.models.lunchmenu.*');
        Yii::import('common.models.calendar.*');

        $schoolObj = $childObj->school;
        $yid = $schoolObj->schcalendar;

        $criteria = new CDbCriteria();
        $criteria->compare('yid', $yid);
        $calendarWeek = CalendarWeek::model()->findAll($criteria);
        $weekDayData = array();
        if($calendarWeek){
            foreach ($calendarWeek as $weekDay) {
                $weekDayData[$weekDay->weeknumber] = $weekDay->monday_timestamp;
            }
        }

        $schedule = array();
        $weekList = array();
        $criteria = new CDbCriteria();
        $criteria->compare('t.yid', $yid);
        $criteria->compare('t.schoolid', $childObj->schoolid);
        $criteria->compare('t.menu_id', '>0');

        $criteria->order = 'week_num desc';
        $links = CateringMenuWeeklink::model()->with(array('common', 'allergy'))->findAll($criteria);

        if($links) {
            foreach ($links as $link) {
                $schedule[$link->week_num] = array(
                    'common' => $link->menu_id,
                    'allergy' => $link->allergy_id,
                );
                $weekList[$link->week_num] = 'week ' . $link->week_num . ' ' . date('m/d', $weekDayData[$link->week_num]) . ' - ' . date('m/d', ($weekDayData[$link->week_num]) + 4 * 24 * 3600);
            }
        }
        // 获取当前周
        $startWeek = strtotime(date('Y-m-d', strtotime('this week')));
        $weekArray = array_flip($weekDayData);
        $todayWeek = $weekArray[$startWeek];


        $week= Yii::app()->request->getParam('week', '');
        if($schedule) {
            if ($week) {
                $commonId = $schedule[$week]['common'];
                $allergyId = $schedule[$week]['allergy'];
            } else {
                $reset = ($schedule && $schedule[$todayWeek]) ? $schedule[$todayWeek]  : reset($schedule) ;
                $commonId = $reset['common'];
                $allergyId = $reset['allergy'];
            }
        }

        $weekD = ($weekList && $weekList[$todayWeek]) ? $todayWeek : reset(array_keys($weekList));
        $weekid = ($week) ? $week : $weekD;

        $lunchs = array();
        $lunchModels = Term::model()->lunch()->findAll();
        foreach($lunchModels as $lunch){
            $lunchs[$lunch->diglossia_id] = CommonUtils::autoLang($lunch->cntitle, $lunch->entitle);
        }

        // 营养餐
        $commonModel = CateringMenu::model()->with('dailyMenus')->findByPk($commonId);
        $commonDetails = array();
        $commonWeekCate = '';
        if($commonModel){
            $commonWeekCate = unserialize($commonModel->week_cate);
            $commonMenuCate = unserialize($commonModel->menu_cate);
            CateringMenuDetail::model()->findAll();
            foreach($commonModel->dailyMenus as $detail){
                $commonDetails[$detail->weekday][$detail->category] = array(
                    'content' => $detail->food_list,
                    'photo' => ($detail->photo) ? Yii::app()->params['uploadBaseUrl'] . 'lunch/' . $detail->photo : "",
                );
            }
        }

        // 特殊餐
        $allergyModel = CateringMenu::model()->with('dailyMenus')->findByPk($allergyId);
        $allergyDetails = array();
        $allergyMenuCate = array();
        $allergyWeekCate = '';
        if ($allergyModel) {
            $allergyWeekCate = unserialize($allergyModel->week_cate);
            $allergyMenuCate = unserialize($allergyModel->menu_cate);
            foreach($allergyModel->dailyMenus as $detail){
                $allergyDetails[$detail->weekday][$detail->category] = array(
                    'content' => $detail->food_list,
                    'photo' => ($detail->photo) ? Yii::app()->params['uploadBaseUrl'] . 'lunch/'.  $detail->photo : "",
                );
            }
        }
        $getWeekDayName = array(
            'mon' => Yii::t('labels','Mon'),
            'tue' => Yii::t('labels','Tue'),
            'wed' => Yii::t('labels','Wed'),
            'thu' => Yii::t('labels','Thu'),
            'fri' => Yii::t('labels','Fri'),
            'sat' => Yii::t('labels','Sat'),
            'sun' => Yii::t('labels','Sun'),
        );
        $weekday = $commonWeekCate + $allergyWeekCate;
        foreach ($weekday as $val){
            $weekDays[$val] = $getWeekDayName[$val];
        }

        if(Yii::app()->request->isPostRequest){
            echo json_encode(array(
                'schedule' => $schedule,
                'weekDays' => $weekDays,
                'lunchs' => $lunchs,
                'commonDetails' => $commonDetails,
                'allergyDetails' => $allergyDetails,
                'weekid' => $weekid,
                'commonMenuCate' => $commonMenuCate,
                'allergyMenuCate' => $allergyMenuCate,
            ));
        }else{
            $this->render('cateringMenuDetail', array(
                'commonMenuCate' => $commonMenuCate,
                'commonDetails' => $commonDetails,
                'commonMemo' => $commonModel->memo,
                'allergyMenuCate' => $allergyMenuCate,
                'allergyDetails' => $allergyDetails,
                'allergyMemo' => $allergyModel->memo,
                'lunchs' => $lunchs,
                'weekDays' => $weekDays,
                'schedule' => $weekList,
                'weekid' => $weekid,
            ));
        }
    }

    // 取消午餐页面
    public function actionLunch()
    {
        $this->subPageTitle = Yii::t('navigations' ,'Cancel Lunch');
        $childid = $this->getChildId();
        $childObj = $this->myChildObjs[$childid];
        if (!$childObj)
            return false;

        Yii::import('common.models.invoice.*');
        $schoolId = $childObj->schoolid;
        $yid = Branch::model()->getBranchInfo($schoolId, 'schcalendar');
        $CfgFeeType = Mims::LoadConfig('CfgFeeType');
        $feeType = $CfgFeeType["lunch"]["sign"];
        // $feeObj = FeemgtSchoolConfig::model()->getSchoolFeeConfig($schoolId, $yid, $feeType);
        $invoiceDays = array();
        $refundLunchDays = array();
        //判断是否有午餐政策
        // if (empty($feeObj) || ($feeObj->lunch_payment_type == 30)) {
        // } else{
            Yii::import('common.models.calendar.*');
            $startDate = strtotime(date('Y-m-d',time()));
            if (date('H', time()) >= 9) {
                $startDate += 24*3600;
            }
            //孩子的餐费信息
            $childServiceInfo = ChildServiceInfo::model()->getChildFeeInfo($childid, $feeType, $yid, $startDate);
            if ($childServiceInfo) {
                $endDate = end($childServiceInfo)->enddate;
                //学校的上学日
                $schoolDays = CalendarSchoolDays::model()->getCalendarSchoolDay($yid, $startDate, $endDate);
                $schoolDaysArr =array();
                foreach ($schoolDays as $schoolDay) {
                    $schoolDaysArr[$schoolDay->month] = explode(',', $schoolDay->schoolday_array);
                }
                //餐费退费信息
                $refundLunch = RefundLunch::model()->getRefundLunch($childid, $startDate, $endDate);
                foreach ($refundLunch as $v) {
                    $refundLunchDays[] = $v->target_date;
                }
                foreach ($childServiceInfo as $serviceInfo) {
                    $tmpDate = $serviceInfo->startdate > $startDate ? $serviceInfo->startdate : $startDate;
                    while ($tmpDate <= $serviceInfo->enddate) {
                        if(in_array(date('d', $tmpDate), $schoolDaysArr[date('Ym', $tmpDate)]))
                            $invoiceDays[] = date('Ymd', $tmpDate);
                        $tmpDate += 24*3600;
                    }
                }
                $invoiceDays = array_unique($invoiceDays);
            }
        // }
        $this->render('lunch', array(
            "childObj" => $childObj,
            "yid" => $yid,
            "feeType" => $feeType,
            "childid" => $childid,
            "schoolId" => $schoolId,
            "invoiceDays" => $invoiceDays,
            "refundLunchDays" => $refundLunchDays,
        ));
    }

    // 取消午餐动作
    public function actionCancelLunch()
    {
        $childid = $this->getChildId();
        $childObj = $this->myChildObjs[$childid];
        if (!$childObj)
            return false;
        $userId = $this->wechatUser->userid;
        $day = Yii::app()->request->getParam('day','');
        $operate = Yii::app()->request->getParam('operate','');
        if ($operate === 'DO'){
            $message = Mims::lunchCancel($childid, $day, $userId);
            $this->sendLunchEmail($childObj, $day, 'cancel');
        } elseif ($operate === 'UNDO') {
            $message = Mims::lunchRecover($childid, $day, $userId);
            $this->sendLunchEmail($childObj, $day, 'recover');
        }
        $state = 'success';
        if ($message)
            $state = 'fail';
        $this->addMessage('message', $message);
        $this->addMessage('state', $state);
        $this->showMessage();
    }

    // 同班小伙伴
    public function actionMates()
    {
        $this->subPageTitle = $this->state == 'ivy' ? Yii::t('wechat' ,'My Friends') : Yii::t('wechat' ,'My Class');
        $childid = $this->getChildId();
        $childObj = $this->myChildObjs[$childid];
        if (!$childObj)
            return false;
        $classmates = '';
        $schoolid = $childObj->schoolid;
        if ($classId = $childObj->classid) {
            $class = IvyClass::model()->with('schoolInfo')->findByPk($classId);
            $crit = new CDbCriteria;
            $crit->compare('classid', $classId);
            $crit->compare('status', '<'. ChildProfileBasic::STATS_GRADUATED);
            $crit->order = 'childid ASC';
            $crit->index = 'childid';
            $classmates = ChildProfileBasic::model()->findAll($crit);
        }

        $cfgs = Mims::LoadConfig('CfgProfile');
        foreach($classmates as $child){
            $childs[] = array(
                'childid' => $child->childid,
                'smallface' => CommonUtils::childPhotoUrl($child->photo, 'small'),
                'face' => CommonUtils::childPhotoUrl($child->photo),
                'name' => $child->getChildName(true, true),
                'gender' => $cfgs['gender'][$child->gender],
                'age' => CommonUtils::getAge($child->birthday)
            );
        }

        $this->render('mates', array(
            'childObj' => $childObj,
            'class' => $class,
            'classmates' => $classmates,
            'childs' => $childs,
            'schoolid' => $schoolid,
        ));
    }

    // 孩子信息
    public function actionInfo()
    {
        $this->subPageTitle = Yii::t('wechat', 'Child Info');
        $childid = Yii::app()->request->getParam('childid');
        $childObj = $this->myChildObjs[$childid];
        if(!$childObj)
            return false;
        if (isset($_POST['ChildProfileBasic'])) {
            $childObj->scenario='wechat';
            $childObj->attributes = $_POST['ChildProfileBasic'];
            $childObj->uploadPhoto = CUploadedFile::getInstance($childObj, 'uploadPhoto');
            $this->addMessage('state', 'success');
            if (!$childObj->save()) {
                $this->addMessage('state', 'fail');
                $this->addMessage('error', $childObj->getErrors());
            }
            $this->showMessage();
        }
        $cfgs = Mims::LoadConfig('CfgProfile');
        $country = Country::model()->getData();
        $lang = Term::model()->getLangList();
        if ($childObj) {
            $this->render('info', array(
                'childObj'=>$childObj,
                'cfgs'=>$cfgs,
                'country'=>$country,
                'lang'=>$lang,
            ));
        }
    }

    //保存周反馈内容
    public function actionSaveFeedback()
    {
        Yii::import('common.models.feedback.Comments');

        $content = Yii::app()->request->getPost('content', '');
        $classid = Yii::app()->request->getPost('classid', 0);
        $yid = Yii::app()->request->getPost('yid', 0);
        $weeknumber = Yii::app()->request->getPost('weeknumber', 0);
        $schoolid = Yii::app()->request->getPost('schoolid', '');
        $childid = Yii::app()->request->getPost('childid', '');
        if(!$childid){
            $childid = $this->getChildId();
        }

        $model = new Comments();
        $criteria = new CDbCriteria;
        if($id = Yii::app()->request->getPost('id', '')){//周反馈回复
            $feedback = Comments::model()->findByPk($id);
            $model->com_root_id = $feedback->id;
            $classid = $feedback->com_class_id;
            $yid = $feedback->com_yid;
            $weeknumber = $feedback->com_week_num;
            $schoolid = $feedback->com_school_id;
            //修改周的第一条回复状态
            $feedback->com_ifreply = 0;
            $feedback->save();
        }
        if($_POST['comrootid']){//判断是否第一次留言
            $criteria->compare('com_child_id', $childid);
            $criteria->compare('com_week_num',$weeknumber);
            $criteria->compare('com_yid',$yid);
            $criteria->compare('com_root_id',0);
            $feedbackModel = Comments::model()->find($criteria);
            if($feedbackModel){
                $model->com_root_id = $feedbackModel->id;
                $feedbackModel->com_ifreply = 0;
                $feedbackModel->save();
            }else{
                $model->com_root_id = 0;
            }
        }
        $model->com_content = $content;
        $model->com_child_id = $childid;
        $model->com_uid = $this->wechatUser->userid;
        $model->com_class_id = $classid;
        $model->com_yid = $yid;
        $model->com_week_num = $weeknumber;
        $model->com_school_id = $schoolid;
        $model->com_ifread = 1;
        $model->com_created_time = time();
        if($model->save()){
            $this->addMessage('state', 'success');//ajax通知
        }else {
            $this->addMessage('state', 'fail');
            $errs = current($model->getErrors());
            $this->addMessage('message', $errs?$errs[0]:Yii::t('message','Saving Failed!'));
        }
        $this->showMessage();
    }

    //显示周报告反馈列表
    public function actionFeedBack()
    {
        $this->subPageTitle = Yii::t('portfolio', 'Journal Feedback & Response');
        $childid = $this->getChildId();
        $childObj = $this->myChildObjs[$childid];
        $weeknum = Yii::app()->request->getParam('weeknum',0);
        $yid = Yii::app()->request->getParam('yid',0);
        Yii::import('common.models.feedback.Comments');
        Yii::import('common.models.classTeacher.InfopubStaffExtend');

        $criteria = new CDbCriteria;
        $criteria->compare('com_child_id',$childid);
        $criteria->compare('com_week_num',$weeknum);
        $criteria->compare('com_yid',$yid);
        $criteria->order = 'com_created_time ASC';//排序条件
        $feedbackModels = Comments::model()->findAll($criteria);

        $feedbacks = array();
        foreach ($feedbackModels as $feedbackModel) {
            $feedbacks[$feedbackModel->com_root_id][$feedbackModel->id]['name'] = $feedbackModel->userWithProfile->getName();
            $feedbacks[$feedbackModel->com_root_id][$feedbackModel->id]['type'] = $feedbackModel->com_user_type;
            if ($feedbackModel->com_user_type) {
                $avatar = $feedbackModel->staffInfo->staff_photo ? $feedbackModel->staffInfo->staff_photo : 'blank.jpg';
                $feedbacks[$feedbackModel->com_root_id][$feedbackModel->id]['avatar'] = 'infopub/staff/' . $avatar;
            } else {
                $feedbacks[$feedbackModel->com_root_id][$feedbackModel->id]['avatar'] = 'users/thumbs/' . $feedbackModel->userWithProfile->user_avatar;
            }
            $feedbacks[$feedbackModel->com_root_id][$feedbackModel->id]['content'] = $feedbackModel->com_content;
            $feedbacks[$feedbackModel->com_root_id][$feedbackModel->id]['time'] = $feedbackModel->com_created_time;
            //所有变已读
            $feedbackModel->com_ifread = 1;
            $model = $feedbackModel->save();
        }

        $this->render('feedback', array(
            'feedbacks' => $feedbacks,
            'weeknum'=> $weeknum,
        ));
    }

    // 上传头像
    public function actionUploadPhoto()
    {
        Yii::app()->user->setId($this->wechatUser->userid);
        if (Yii::app()->request->isPostRequest) {
            $childid = Yii::app()->request->getParam('childid', '');
            $childObj = $this->myChildObjs[$childid];
            if (!$childObj) {
                return false;
            }

            if (!$childObj){
                $this->addMessage('state', 'fail');
                $this->showMessage();
            }
            $cfgs = Mims::LoadConfig('CfgProfile');
            $childObj->uploadPhoto = CUploadedFile::getInstance($childObj, 'uploadPhoto');
            if ($childObj->uploadPhoto) {
                $oldPhoto = $childObj->photo;
                $params = $cfgs["childPhoto"];
                $delold = false;
                $newPhoto = $this->processUpload($childObj->uploadPhoto, $params, $oldPhoto, $delold);
                if (!$newPhoto) {
                    $this->addMessage('state', 'fail');
                    $this->showMessage();
                }
                $childObj->photo = $newPhoto;
                $childObj->processAvatar($newPhoto, $oldPhoto);
            }

            if ($childObj->save(false)) {
                $data = array('photoUrl'=>CommonUtils::childPhotoUrl($childObj->photo, 'small'));
                $this->addMessage('state', 'success');
                $this->addMessage('data', $data);
            } else {
                $this->addMessage('state', 'fail');
                $this->addMessage('error', $childObj->getErrors());
            }
            $this->showMessage();
        }
    }

    // 上传头像进程
    public function processUpload($uploadedInst, $params, $oldFile = "", $deleteOld = true) {
        $subPath = trim($params['subDir'], '/') . '/';
        $subThumbPath = trim($params['subDir'], '/') . '/thumbs/';

        $normalDir = rtrim(Yii::app()->params['uploadPath'], '/') . '/' . $subPath;
        $thumbDir = rtrim(Yii::app()->params['uploadPath'], '/') . '/' . $subThumbPath;

        $type = $uploadedInst->getType();
        switch ($type) {
            case 'image/jpeg':
                $typeName = 'jpg';
                break;
            default:
                $typeName = 'png';
                break;
        }

        $fileName = $params['filePrefix'] . uniqid() . '.' . $typeName;

        $filePath = $normalDir . $fileName;
        $fileThumbPath = $thumbDir . $fileName;
        $uploadedInst->saveAs($filePath);

        Yii::import('application.extensions.image.Image');
        $image = new Image($filePath);
        if (isset($params['forceSquare']) && $params['forceSquare']) {
            $min = min($image->__get('width'), $image->__get('height'));
            $image->crop($min, $min)->save();
        }
        $image->resize($params['sizeNormal'], $params['sizeNormal'])->save();
        $image->resize($params['sizeThumb'], $params['sizeThumb'])->save($fileThumbPath);

        $aliYunOss['new'][] = $subPath.$fileName;
        $aliYunOss['new'][] = $subThumbPath.$fileName;

        // if ($deleteOld && !empty($oldFile)) {
        //     @unlink($normalDir . $oldFile);
        //     @unlink($thumbDir . $oldFile);
        //     $aliYunOss['del'][] = $subPath.$oldFile;
        //     $aliYunOss['del'][] = $subThumbPath.$oldFile;
        // }

        // CommonUtils::processAliYunOSS($aliYunOss);
        return $fileName;
    }

    // 未付账单
    public function actionUnpaidInvoices()
    {
        $this->subPageTitle = Yii::t('payment', 'Unpaid Invoices');
        $childid = $this->getChildId();
        $childObj = $this->myChildObjs[$childid];
        $schoolId = $childObj->schoolid;
        if(!$childObj)
            return false;

        // 判断是否支持微信支付
        $wxpayInfo = CommonUtils::LoadConfig('CfgWxPayGlobal');
        $wechatPay = true;
        if (!$wxpayInfo[$schoolId]) {
            $wechatPay = false;
            // 查询孩子下学年学校是否支持微信支付
            Yii::import('common.models.invoice.ChildReserve');
            if ($childObj->nextYear && $wxpayInfo[$childObj->nextYear->schoolid]) {
                $wechatPay = true;
            }
        }
        // 判断支付方式
        $payType = $this->getPayType($schoolId);
        // 查询未付账单
        Yii::import('common.models.invoice.*');
        $criteria = new CDbCriteria();
        // $criteria->condition = "childid = " .$childid. " and status in (10,30) AND schoolid='$schoolId'";
        $criteria->condition = "childid = " .$childid. " and status in (10,30)";
        // 不显示学前教育津贴
        $criteria->addNotInCondition('payment_type', array('preschool_subsidy'));
        $criteria->order = 'timestamp DESC';

        $unPaidList = array();
        $busUnPaidList = array();
        // 格式化金额
        $nf = new CNumberFormatter('zh-cn');
        if ($invoices = Invoice::model()->findAll($criteria)) {
            foreach ($invoices as $invoice) {
                // 单独处理泉发校车费
                if ($invoice->schoolid == 'BJ_QFF' && $invoice->payment_type == 'bus') {
                    $busUnPaidList[$invoice->invoice_id]['title'] = $invoice->title;
                    $busUnPaidList[$invoice->invoice_id]['amount'] = $invoice->amount;
                    $busUnPaidList[$invoice->invoice_id]['schoolid'] = $invoice->schoolid;
                    $busUnPaidList[$invoice->invoice_id]['paidAmount'] = $invoice->renderPaidAmountF($invoice);
                    $busUnPaidList[$invoice->invoice_id]['dueAmount'] = $nf->format('#,##0.00', $invoice->renderDueAmount($invoice));
                } else {
                    $unPaidList[$invoice->invoice_id]['title'] = $invoice->title;
                    $unPaidList[$invoice->invoice_id]['amount'] = $invoice->amount;
                    $unPaidList[$invoice->invoice_id]['schoolid'] = $invoice->schoolid;
                    $unPaidList[$invoice->invoice_id]['paidAmount'] = $invoice->renderPaidAmountF($invoice);
                    $unPaidList[$invoice->invoice_id]['dueAmount'] = $nf->format('#,##0.00', $invoice->renderDueAmount($invoice));
                }
            }
        }

        $this->render('unpaidInvoices', array(
            'busUnPaidList' => $busUnPaidList,
            'unPaidList' => $unPaidList,
            'childObj' => $childObj,
            'payType' => $payType,
            'wechatPay' => $wechatPay,
        ));
    }

    // 已付账单
    public function actionPaidInvoices()
    {
        $this->subPageTitle = Yii::t('navigations', 'Payment History');
        $childid = $this->getChildId();
        $childObj = $this->myChildObjs[$childid];
        if(!$childObj)
            return false;

        $this->render('paidInvoices', array('childObj' => $childObj));
    }

    // 付款确认
    public function actionPaymentConfirm()
    {
        $this->subPageTitle = Yii::t('payment', 'Payment Summary');
        $childid = $this->getChildId();
        $childObj = $this->myChildObjs[$childid];
        if(!$childObj)
            return false;

        $invoiceIds = array_keys(Yii::app()->request->getParam('invoice', ''));
        $totalFee = Yii::app()->request->getParam('totalFee', '');

        Yii::import('common.models.invoice.*');
        $invoiceObjs = Invoice::model()->findAllByPk($invoiceIds);
        if(!$invoiceObjs)
            return false;
        $amount = 0;
        if (count($invoiceObjs) == 1) {
            $invoiceObj = current($invoiceObjs);
            $schoolId = $invoiceObj->schoolid;
            // 单独处理泉发校车费
            if ($invoiceObj->schoolid == 'BJ_QFF' && $invoiceObj->payment_type == 'bus') {
                $schoolId = 'BJ_DS';
            }
            $amount = $totalFee;
            if ($totalFee > Invoice::model()->renderDueAmount($invoiceObj, null)) {
                $amount = Invoice::model()->renderDueAmount($invoiceObj, null);
            }
        } else {
            $schools = array();
            foreach ($invoiceObjs as $invoiceObj) {
                // 单独处理泉发校车费
                if ($invoiceObj->schoolid == 'BJ_QFF' && $invoiceObj->payment_type == 'bus') {
                    $schoolId = 'BJ_DS';
                } else {
                    $schoolId = $invoiceObj->schoolid;
                }
                $schools[$schoolId] = $schoolId;
                $amount += floatval(Invoice::model()->renderDueAmount($invoiceObj, null));
            }
            if (count($schools) != 1) {
                return false;
            } else{
                $schoolId = current($schools);
            }
        }

        $wxpayInfo = CommonUtils::LoadConfig('CfgWxPayGlobal');
        // $schoolId = $childObj->schoolid;
        $payType = $this->getPayType($childObj->schoolid);
        $cfg = $wxpayInfo[$schoolId];
        // 微信支付配置统一改成当前环境下的appid
        $cfg['appid'] = $this->appId;
        $invoiceTitle = end($invoiceObjs)->title;
        if (count($invoiceObjs) > 1)
            $invoiceTitle = $invoiceTitle . '等';
        $QRcodeUrl = '';
        $jsPayInfo = '';
        if ($orderId = $this->createOrder($cfg, $childObj, $amount, $invoiceObjs, $payType)) {
            if ($payType == 'JSAPI') {
                // 公众号支付
                $jsPayInfo = $this->getJsPayInfo($cfg, $invoiceTitle, end($invoiceIds) , $orderId, $amount, $schoolId);
                echo $jsPayInfo;
                Yii::app()->end();
            }
            // 二维码支付
            $QRcodeUrl = $this->getQRcode($cfg, $invoiceTitle, end($invoiceIds) , $orderId, $amount, $schoolId);
        }
        $this->render('paymentConfirm', array('QRcodeUrl' => $QRcodeUrl, 'orderId' => $orderId));
    }

    public function actionPaymentResult()
    {
        $this->subPageTitle = Yii::t('wechat', 'Payment Record');
        $result = Yii::app()->request->getParam('result', 'fail');
        $this->render('paymentResult', array('result'=>$result));
    }

    // 个人账户
    public function actionCredit()
    {
        $this->subPageTitle = Yii::t('navigations', 'General Credit');
        $childid = $this->getChildId();
        $childObj = $this->myChildObjs[$childid];
        if(!$childObj)
            return false;

        Yii::import('common.models.invoice.ChildCredit');
        Yii::import('common.models.invoice.*');
        Yii::import('common.models.calendar.Calendar');
        Yii::import('common.models.points.*');
        // 账户信息
        $criter = new CDbCriteria();
        $criter->compare('childid', $childid);
        $criter->order = 'updated_timestamp desc';
        $creditModel = ChildCredit::Model()->findAll($criter);
        $balance = ChildCredit::Model()->getChildCredit($childid);
        // 剩余预缴学费
        $deposit = DepositHistory::model()->getChildDepositBalance($childid);
        // 预缴学费详情
        $depositDetail = DepositHistory::model()->findAll('childid = :childid', array(':childid'=>$childid));
        // 积分信息
        $points = 0;
//        $schoolConfig = PointsConfig::model()->getSchoolConfig($childObj->schoolid);
//        $status = $schoolConfig->status;
        $status = false;
        if ($status)
        {
            $pointStartDate = '2013-06-01';
            if ($childObj->schoolid == 'BJ_DS') {
                $pointStartDate = '2017-06-01';
            }
            $points = InvoiceTransaction::model()->getPointsCredit($childid, strtotime($pointStartDate));
        }

        $this->render('credit', array(
            'childObj' => $childObj,
            'balance' => $balance,
            'creditModel' => $creditModel,
            'deposit' => $deposit,
            'points' => $points,
            'status' => $status,
            'depositDetail' => $depositDetail,
        ));
    }

    // 家长调查问卷
    public function actionSurvey($id)
    {
        Yii::import('common.models.survey.*');
        $fid = Yii::app()->request->getParam('id', '');
        $id = Yii::app()->request->getParam('id', '');
        // $kindClass = array(863,864,865,866,860,861,1288);
        // $highClass = array(872,1049,873,1050,879);
        if($id){
            $crit = new CDbCriteria;
            $crit->compare('survey_id', $id);
            $crit->compare('childid', array_keys($this->myChildObjs));
            $crit->select = 'childid';
            $crit->index = 'childid';
            $assignChildren = WSurveyFeedback::model()->findAll($crit);
            foreach ($this->myChildObjs as $_cid => $_val) {
                if (!in_array($_cid, array_keys($assignChildren))) {
                    unset($this->myChildObjs[$_cid]);
                }
            }
            if (count($this->myChildObjs) < 1) {
                $surveyObj = new Survey();
                $feedbackObj = new WSurveyFeedback();
            }
            else {
                $childid = $this->getChildId();
                $childObj = $this->myChildObjs[$childid];
                $schoolid = $this->myChildObjs[$childid]->schoolid;
                // if (in_array($schoolid, array('BJ_DS', 'BJ_SLT'))) {
                    // if (in_array($childObj->classid, $kindClass)) {
                    //     $id = 42;
                    // } elseif (in_array($childObj->classid, $highClass)) {
                    //     $id = 35;
                    // } else {
                    //     $id = 41;
                    // }
                // }

                // $id = 45;
                // 判断是否为 K 班孩子
                // if ($childObj->ivyclass->classtype == 'k') {
                //     $id = 42;
                // }
                // if (in_array($schoolid, array('BJ_DS', 'BJ_SLT'))) {
                //     $id = 43;
                // }

                $isFeedback = false;
                $topics = array();
                $options = array();
                $surveyObj = Survey::model()->getSurvey($id, $schoolid);
                if (!$surveyObj) {
                    $surveyObj = new Survey();
                }
                $this->subPageTitle = $surveyObj->getTitle();
                // 查找是否已经填写问卷
                $crit = new CDbCriteria;
                $crit->compare('survey_id', $id);
                $crit->compare('childid', $childid);
                $crit->compare('schoolid', $schoolid);
                $feedbackObj = WSurveyFeedback::model()->find($crit);
                if (!$feedbackObj) {
                    $feedbackObj = new WSurveyFeedback();
                }

                if ($feedbackObj && $feedbackObj->is_fb != 1) {
                    $isFeedback = true;
                    $templateid = unserialize($surveyObj->template_id);
                    if ($templateid) {
                        foreach ($templateid as $v) {
                            $topics += WSurveyTopic::model()->getSurveyTopics($v);
                            if(WSurveyTopic::model()->getTopicOptions($v)){
                                $options += WSurveyTopic::model()->getTopicOptions($v);
                            }
                        }
                    }
                }
            }
            $numberConfig = WSurveyTopic::model()->getNumberconfig($id);
            $this->render('survey', array(
                'schoolid'=>$schoolid,
                'childObj'=>$childObj,
                'isFeedback'=>$isFeedback,
                'surveyObj'=>$surveyObj,
                'topics'=>$topics,
                'options'=>$options,
                'feedbackObj'=>$feedbackObj,
                'id'=>$id,
                'fid'=>$fid,
                'endTime' => Yii::app()->language == 'en_us' ? date('l, F j', $surveyObj->surveyDetail->end_time)  : date('Y年n月j日', $surveyObj->surveyDetail->end_time),
                'numberConfig' => $numberConfig,
            ));
        }
    }

    // 保存家长调查问卷
    public function actionSaveSurvey()
    {
        Yii::import('common.models.survey.*');
        $surveyid = Yii::app()->request->getParam('surveyid', '');
        $groups = Yii::app()->request->getParam('group', '');
        $childid = Yii::app()->request->getParam('childid', '');
//        $childid = $this->getChildId();
        $childObj = $this->myChildObjs[$childid];
        $schoolid = $this->myChildObjs[$childid]->schoolid;

        $isFeedback = false;
        $topics = array();
        $surveyObj = Survey::model()->getOneSurvey($surveyid, $schoolid);
        if (!$surveyObj) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', Yii::t('survey', 'Survey does not exist'));
            $this->showMessage();
        }
        // 查找是否已经填写问卷
        $crit = new CDbCriteria;
        $crit->compare('survey_id', $surveyid);
        $crit->compare('childid', $childid);
        $crit->compare('schoolid', $schoolid);
        $feedbackModel = WSurveyFeedback::model()->find($crit);

        if($feedbackModel == null){
            $this->addMessage('state', 'fail');
            $this->addMessage('message', Yii::t('survey', 'Survey has not started or has already ended'));
            $this->showMessage();
        }

        $isFeedback = $feedbackModel->is_fb;
        if ($isFeedback) {
            $this->addMessage('state', 'success');
            $this->addMessage('message', Yii::t('survey', 'Survey already completed'));
            $this->showMessage();
        }
        $templateid = unserialize($surveyObj->template_id);
        if ($templateid) {
            foreach ($templateid as $v) {
                $topics += WSurveyTopic::model()->getSurveyTopics($v);
            }
        }
        // 验证是否选择了所有题目
        foreach ($topics as $topic) {
            if (in_array($topic['binary_flag'], array(0, 1))) {
                continue;
            }
            if (!isset($groups[$topic['id']]['value']) || empty($groups[$topic['id']]['value'])) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('survey', 'Please complete all the questions with * before submission.'));
                $this->showMessage();
            }
        }
        // 设置孩子已答题状态
        $feedbackModel->is_fb = 1;
        $feedbackModel->fb_user = $this->wechatUser->userid;
        $feedbackModel->fb_time = time();
        if(!$feedbackModel->save()){
            $this->addMessage('state', 'fail');
            $this->addMessage('message', Yii::t('global', 'Failed!'));
            $this->showMessage();
        }
        // 保存回答题目
        foreach ($groups as $k=>$group) {
            if (!is_array($group['value'])) {
                // 根据题目跟选项判断是否生成预交学费
                if ($k == 630 && $group['value'] == 1) {
                    $mqData = array();
                    $nextCid = 0;
                    Yii::import('common.models.invoice.ChildReserve');
                    if (isset($childObj->nextYear)) {
                        $nextCid = $childObj->nextYear->classid;
                    }
                    $mqData = array(
                        'childid' => $childid,
                        'schoolid' => $schoolid,
                        'classid' => $nextCid,
                        'uid' => $this->wechatUser->userid,
                    );
                    if (count($mqData) > 0) {
                        CommonUtils::addProducer('survey', "Invoice.createDepositInvoice", CJSON::encode($mqData), 0);
                    }
                }
                $model = new WSurveyFeedbackOption;
                $model->fb_id = $feedbackModel->id;
                $model->survey_id = $surveyid;
                $model->topic_id = $k;
                $model->classid = $feedbackModel->classid;
                $model->childid = $feedbackModel->childid;
                $model->userid = $feedbackModel->fb_user;
                $model->ic_id = 0;
                $model->schoolid = $schoolid;
                $model->option_answer = $group['value'];
                $model->followup = 0;
                if(!$model->save()){
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', Yii::t('global', 'Failed!'));
                    $this->showMessage();
                }
            } else {
                foreach ($group['value'] as $v) {
                    $model = new WSurveyFeedbackOption;
                    $model->fb_id = $feedbackModel->id;
                    $model->survey_id = $surveyid;
                    $model->topic_id = $k;
                    $model->classid = $feedbackModel->classid;
                    $model->childid = $feedbackModel->childid;
                    $model->userid = $feedbackModel->fb_user;
                    $model->ic_id = 0;
                    $model->schoolid = $schoolid;
                    $model->option_answer = $v['value'];
                    $model->followup = 0;
                    if(!$model->save()){
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', Yii::t('global', 'Failed!'));
                        $this->showMessage();
                    }
                }
            }
            //保存备注
            if (isset($group['memo']) && !empty($group['memo'])) {
                $feedbackText = new WSurveyFeedbackText;
                $feedbackText->fb_id = $feedbackModel->id;
                $feedbackText->survey_id = $surveyid;
                $feedbackText->topic_id = $k;
                $feedbackText->classid = $feedbackModel->classid;
                $feedbackText->childid = $feedbackModel->childid;
                $feedbackText->userid = $feedbackModel->fb_user;
                $feedbackText->ic_id = 0;
                $feedbackText->schoolid = $schoolid;
                $feedbackText->hide2school = 0;
                $feedbackText->followup = 0;
                $feedbackText->fb_text = $group['memo'];
                if(!$feedbackText->save()){
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', Yii::t('global', 'Failed!'));
                    $this->showMessage();
                }
            }
        }

        // 保存日志
        Yii::log($childid, 'info', 'survey.wechat');
        $this->addMessage('state', 'success');
        $this->addMessage('survey', 'Success');
        $this->showMessage();

    }

    /**
     * 生成支付订单
     * @param  [type] $cfg         [公众号配置]
     * @param  [type] $childObj    [孩子对象]
     * @param  [type] $totalFee    [付款金额]
     * @param  [type] $invoiceObjs [账单对象]
     * @param  string $payType     [支付方式]
     * @return [type]              [订单ID]
     */
    public function createOrder($cfg, $childObj, $totalFee, $invoiceObjs, $payType = 'NATIVE')
    {
        Yii::import('common.models.wxpay.*');
        Yii::import('common.models.invoice.*');

        $invoiceId = current($invoiceObjs)->invoice_id;
        $number_code = $cfg['number_code'];
        $time = time();
        $wechatPayOrder = new WechatPayOrder();
        $wechatPayOrder->orderid = $wechatPayOrder->genOrderID($number_code.$invoiceId);
        $wechatPayOrder->payable_amount = $totalFee;
        $wechatPayOrder->fact_amount = 0;
        $wechatPayOrder->schoolid = current($invoiceObjs)->schoolid;
        $wechatPayOrder->childid = $childObj->childid;
        $wechatPayOrder->type = $payType;
        $wechatPayOrder->status = 0;
        $wechatPayOrder->settlement_status = 0;
        $wechatPayOrder->order_time = $time;
        $wechatPayOrder->update_timestamp = $time;
        $wechatPayOrder->uid = $this->wechatUser->userid;
        if ($wechatPayOrder->save()) {
            foreach ($invoiceObjs as $invoice) {
                $wechatPayOrderItem = new WechatPayOrderItem();
                $wechatPayOrderItem->orderid = $wechatPayOrder->orderid;
                $wechatPayOrderItem->invoice_id = $invoice->invoice_id;
                $wechatPayOrderItem->amount = Invoice::model()->renderDueAmount($invoice, null);
                if (count($invoiceObjs)==1)
                    $wechatPayOrderItem->amount = $totalFee;
                $wechatPayOrderItem->save();
            }
            return $wechatPayOrder->orderid;
        }
        return false;
    }

    /**
     * 生成支付二维码
     * @param  [type] $cfg          [公众号配置]
     * @param  [type] $invoiceTitle [账单标题]
     * @param  [type] $invoiceId    [账单ID]
     * @param  [type] $orderId      [订单ID]
     * @param  [type] $totalFee     [支付金额]
     * @param  [type] $schoolId     [学校ID]
     * @return [type]               [二维码url]
     */
    public function getQRcode($cfg, $invoiceTitle, $invoiceId, $orderId, $totalFee, $schoolId)
    {
        Yii::import('common.extensions.wxPay.*');
        $nativePay = new NativePay();
        $nativePay->cfg = $cfg;
        $nativePay->values['body'] = $invoiceTitle;
        $nativePay->values['product_id'] = $invoiceId;
        $nativePay->values['notify_url'] = Yii::app()->params['appsUrl'].'/wechatPay/wechatPay';
        $nativePay->values['out_trade_no'] = $orderId;
        $nativePay->values['total_fee'] = $nativePay->transformMoney($totalFee);
        $nativePay->values['attach'] = $schoolId;
        $nativePay->init();
        $rs = $nativePay->createQRcode();
        $codeUrl = $rs['code_url'];
        return $codeUrl;
    }

    //jsPay信息
    public function getJsPayInfo($cfg, $invoiceTitle, $invoiceId, $orderId, $totalFee, $schoolId)
    {
        Yii::import('common.extensions.wxPay.*');
        $jsPay = new JsPay();
        $jsPay->cfg = $cfg;
        $jsPay->values['body'] = $invoiceTitle;
        $jsPay->values['openid'] = $this->openid;
        $jsPay->values['product_id'] = $invoiceId;
        $jsPay->values['notify_url'] = Yii::app()->params['appsUrl'].'/wechatPay/wechatPay';
        $jsPay->values['out_trade_no'] = $orderId;
        $jsPay->values['total_fee'] = $jsPay->transformMoney($totalFee);
        $jsPay->values['attach'] = $schoolId;
        $jsPay->init();
        $jsPayInfo = $jsPay->resultInfo();
        return $jsPayInfo;
    }

    // 获取childid
    public function getChildId()
    {
        $userId = $this->wechatUser->userid;
        $childid = isset($_COOKIE['childid']) ? $_COOKIE['childid'] : '' ;
        if(!$childid || !isset($this->myChildObjs[$childid])){
            $childObj = end($this->myChildObjs);
            $childid = $childObj->childid;
        }
        return $childid;
    }

    /**
     * 取消/恢复午餐发送邮件
     * @param  [type] $child      [孩子对象]
     * @param  [type] $targetDate [取消/恢复日期]
     * @param  [type] $action     [取消/恢复]
     * @return [type]             [description]
     */
    private function sendLunchEmail($child, $targetDate, $action){
        $campus = Branch::model()->with('info')->findByPk($child->schoolid);
        $mailer = Yii::createComponent('common.extensions.mailer.EMailer2');
        $actTitle = ($action=='cancel') ? '取消用餐' : '恢复用餐';
        $mailer->Subject = Mims::unIvy() ? sprintf(Yii::app()->params['ownerConfig']['name_cn'].'【微信助手 - %s】孩子信息：%s（%s）' , $campus->abb, $child->getChildName(), $actTitle) : sprintf('【艾毅在线微信助手 - %s】孩子信息：%s（%s）' , $campus->abb, $child->getChildName(), $actTitle);
        $pEmails = $this->getParentsEmail($child);
        $mailer->AddAddress($campus->info->support_email);
        $mailer->AddReplyTo($campus->info->support_email);
        foreach($pEmails as $_pe){
            $mailer->AddCC($_pe);
        }

        $mailer->iniMail($this->isProduction()); // 此行代码要放到AddAddress, AddCC方法下面
        $mailer->getView('lunchCancel', array(
            'child'=>$child,
            'targetDate'=>date('Y-m-d', strtotime($targetDate)),
            'action'=>$action,
            'supportEmail'=> $campus->info->support_email,
            'otherinfo' => '',
        ), 'wechat');
        return $mailer->Send();
    }

    //获取孩子父母的邮件地址
    private function getParentsEmail($child){
        $emails = array();
        $pids = array();
        if($child->fid) $pids[] = $child->fid;
        if($child->mid) $pids[] = $child->mid;
        $parents = User::model()->findAllByPk($pids, '`level`>:level', array(':level'=>0));
        if(!empty($parents)){
            foreach($parents as $parent){
                $emails[] = $parent->email;
            }
        }
        return $emails;
    }

    //判断是否为生产环境
    private function isProduction(){
        if ( strtolower(Yii::app()->params['productionDomain']) == strtolower($_SERVER['HTTP_HOST']) ) {
            return true;
        } else{
            return false;
        }
    }

    // 获取支付类型
    public function getPayType($schoolId)
    {
        $payType = 'JSAPI';
//        if(in_array($schoolId, array('CD_LT')))
//            $payType = 'NATIVE';
        return $payType;
    }

    /**
     * [actionPaidInvoice 获取已支付账单信息]
     * @return [type] [JSON 数据]
     */
    public function actionPaidInvoice()
    {
        if (!Yii::app()->request->isAjaxRequest) {
            $this->addMessage('message', 'The request should be AJAX.');
            $this->showMessage();
        }
        $pageNum = Yii::app()->request->getParam('pageNum', 1);
        $pageSize = Yii::app()->request->getParam('pageSize', 10);
        $childid = $this->getChildId();
        if (!$pageNum || !$childid) {
            $this->addMessage('message', 'The request param error.');
            $this->showMessage();
        }
        // 查询已付账单
        $paidInvoice = '';
        Yii::import('common.models.invoice.*');
        $count = Invoice::model()->count('childid=:childid and status=:status and `inout`=:inout', array(
            ':childid' => $childid,
            ':status' => 20,
            ':inout' => 'in',
        ));
        $criteria = new CDbCriteria();
        $criteria->compare('t.childid', $childid);
        $criteria->compare('t.status', 20);
        $criteria->compare('t.`inout`', 'in');
        $criteria->select = array('title', 'amount', 'last_paid_timestamp');
        $criteria->limit = $pageSize;
        $criteria->offset = ($pageNum - 1) * $pageSize;
        // 不显示学前教育津贴
        $criteria->addNotInCondition('t.payment_type', array('preschool_subsidy'));
        $criteria->order = 't.last_paid_timestamp DESC';
        $paidInvoices = Invoice::model()->findAll($criteria);
        $invoiceArr = array();
        foreach ($paidInvoices as $key => $paidInvoice) {
            $invoiceArr[$key]['title'] = $paidInvoice->title;
            $invoiceArr[$key]['amount'] = $paidInvoice->amount;
            $invoiceArr[$key]['date'] = $paidInvoice->last_paid_timestamp ? date('Y-m-d', $paidInvoice->last_paid_timestamp) : 0;
        }

        $totlaPage = ceil($count/$pageSize);
        $data = array(
            'totalPage' => $totlaPage,
            'pageNum' => $pageNum,
            'pageSize' => $pageSize,
            'invoiceNum' => count($paidInvoices),
            'invoiceInfo' => $invoiceArr,
        );
        $this->addMessage('message', 'success');
        $this->addMessage('data', $data);
        $this->showMessage();
    }

    // 二维码支付页面监听支付结果
    public function actionCheckState()
    {
        if(Yii::app()->request->isPostRequest){
            $id = Yii::app()->request->getPost('id');
            if(!$id)Yii::app()->end();
            set_time_limit(360);
            $i=0;
            while ($i<300){
                sleep(1);
                Yii::import('common.models.wxpay.*');
                $model = WechatPayOrder::model()->findByPk($id);
                if($model->status == 1){
                    echo CJSON::encode(array('state'=>'success'));
                    break;
                }
                $i++;
            }
        }
    }

    public function actionAsa()
    {
        $this->render('asa');
    }

    /**
     * [actionCheckinQrcode 签到二维码]
     * @param  integer $reload [是否重新生成]
     * @return [type]          [boolean]
     */
    public function actionCheckinQrcode($reload = 0)
    {
        Yii::import('common.models.wechat.CheckinQrcode');
        $childid = $this->getChildId();
        $childObj = $this->myChildObjs[$childid];
        $pid = $this->wechatUser->userid;
        $openid = $this->openid;
        $schoolid = $childObj->schoolid;
        $expired = time();

        $criteria = new CDbCriteria();
        $criteria->compare('childid', $childid);
        $criteria->compare('pid', $pid);
        $criteria->compare('openid', $openid);
        $criteria->compare('schoolid', $schoolid);
        $criteria->compare('expired', '>' . $expired);
        $criteria->order = 'expired desc';
        $model = CheckinQrcode::model()->find($criteria);
        if (!$model || $reload) {
            $model = new CheckinQrcode();
            $model->childid = $childid;
            $model->pid = $pid;
            $model->openid = $openid;
            $model->schoolid = $schoolid;
            $model->code = strtoupper(uniqid($childid, false));
            $model->expired = $expired + 60*15;
            if (!$model->save()) {
                $error = current($model->getErrors());
            }
            if ($reload) {
                $this->showMessage();
            }
        }
        $this->render('checkinQrcode', array('model' => $model));
    }

    public function actionCards()
    {
        header("Cache-Control: no-cache, no-store, must-revalidate"); // HTTP 1.1.
        header("Pragma: no-cache"); // HTTP 1.0.
        header("Expires: 0 ");

        Yii::import('common.models.identity.*');
        $this->subPageTitle = Yii::t('wechat', 'Pick-up Card');

        $childId = array();
        $parents = array();
        $open = false;

        // 判断是否存在OE的孩子
        foreach ($this->myChildObjs as $child) {
            if (in_array($child->schoolid, array('BJ_OE', 'TJ_SA', 'TJ_EC'))) {
                $childId[] = $child->childid;
            }
        }
        if (count($childId) > 0) {
            $open = true;
            $criteria = new CDbCriteria();
            $criteria->compare('childid', $childId);
            $criteria->order = "sign ASC";
            $model = Cards::model()->findAll($criteria);
            if($model){
                foreach($model as $k=>$item){
                    $data = CJSON::decode($item->data);
                    $parents[$item->childid][] = array(
                        'id' => $item->id,
                        'childid' => $item->childid,
                        'childName' => $data['child'],
                        'parentName' => $data['name'],
                        'tel' => $data['tel'],
                        'sign' => $item->sign,
                        'relation' => $data['relation'],
                        'photo' => Mims::CreateUploadUrl('crads/' . $data['photo'] . '?' . time()),
                    );
                }
            }
        }
        $this->render('cards', array(
            'open' => $open,
            'parents' => $parents
        ));
    }

    public function actionCardsInfo()
    {
        Yii::import('common.models.identity.*');
        $this->subPageTitle = Yii::t('wechat', 'Pick-up Card');
        $cardId = Yii::app()->request->getParam('cardId', 0);
        $childid = Yii::app()->request->getParam('childid', 0);
        $cardObj = Cards::model()->findByPk($cardId);

        if(!$cardObj){
            $cardObj = new Cards();
        }

        $parnet = array();
        if(Yii::app()->request->isPostRequest){
            if(empty($_POST['praentName'])){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('wechat','Parent can not be null'));
                $this->addMessage('callback', 'submitFail');
                $this->showMessage();
            }

            if(empty($_POST['parentTel'])){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('wechat','Phone  can not be null'));
                $this->addMessage('callback', 'submitFail');
                $this->showMessage();
            }

            if(!preg_match("/^1[34578]{1}\d{9}$/",$_POST['parentTel'])){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('wechat','Wrong phone format'));
                $this->addMessage('callback', 'submitFail');
                $this->showMessage();
            }

            if(empty($_POST['praentRelation'])){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('wechat','Relationship  can not be null'));
                $this->addMessage('callback', 'submitFail');
                $this->showMessage();
            }

            if(empty($_POST['parentPhoto'])){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('wechat','Avatar can not be null'));
                $this->addMessage('callback', 'submitFail');
                $this->showMessage();
            }
            $model = new Cards();
            $model->childid = $childid;
            $model->data = array('child'=>"", 'campus'=>"", 'class'=>"", 'name'=>"", 'tel'=>"", 'relation'=>"", 'expire'=>"", 'photo'=>"",);
            $model->sign = 4;
            $model->updated = time();
            $model->schoolid = $this->myChildObjs[$childid]->school->abb;

            if ($model->save()) {
                $filePath = Yii::app()->params['uploadPath'] . '/crads/';
                $file = pathinfo($filePath  . $_POST['parentPhoto'], PATHINFO_EXTENSION);
                @rename($filePath . $_POST['parentPhoto'], $filePath . $model->id . "." . $file);

                $data = array(
                    "child" => $this->myChildObjs[$childid]->getChildName(),
                    "campus" => $this->myChildObjs[$childid]->school->abb,
                    "class" => $this->myChildObjs[$childid]->ivyclass->title,
                    "name" => $_POST['praentName'],
                    "tel" => $_POST['parentTel'],
                    "relation" => $_POST['praentRelation'],
                    "photo" => $model->id . "." . $file,
                    "expire" => "",
                );
                $model->data = CJSON::encode($data);
                $model->save();
                $this->addMessage('state', 'success');
                $this->addMessage('message', "增加成功");
                $this->addMessage('callback', 'submitSuccess');
            } else {
                $error = current($model->getErrors());
                $this->addMessage('state', 'fail');
                $this->addMessage('message', $error[0]);
                $this->addMessage('callback', 'submitFail');
            }
            $this->showMessage();
        }

        if($cardObj->data){
            $parnetArr = CJSON::decode($cardObj->data);
            $parnet = array(
                'parentName' => $parnetArr['name'],
                'parentTel' => $parnetArr['tel'],
                'parentRelation' => $parnetArr['relation'],
                'parentPhoto' => Mims::CreateUploadUrl('crads/' . $parnetArr['photo'] . '?' . time()),
                'sign' => $cardObj->sign,
                'cardId' => $cardObj->id,
            );
        }

        $this->render('cardsInfo', array(
            'parnet' => $parnet,
        ));
    }

    public function actionUpdateSign()
    {
        if(Yii::app()->request->isPostRequest){
            Yii::import('common.models.identity.*');
            $cardId = Yii::app()->request->getParam('cardId', '');
            $sign = Yii::app()->request->getParam('sign', '');
            $cardObj = Cards::model()->findByPk($cardId);
            $cardObj->sign = $sign;
            $cardObj->updated = time();
            if($cardObj->save()){
                $this->addMessage('state', 'success');
                $this->addMessage('message', "增加成功");
            }else{
                $error = current($cardObj->getErrors());
                Yii::msg($error[0]);
                $this->addMessage('state', 'fail');
                $this->addMessage('message', $error[0]);
            }
            $this->showMessage();
        }
    }


    public function actionCardFiles()
    {
        $file = CUploadedFile::getInstanceByName('upload_file');
        if ($file) {
            $cardId = Yii::app()->request->getParam('cardId', '');

            if ($file->size > 10 * 1024 * 1024) {
                $msg = '文件过大';
            } else {
                $needType = array('jpg', 'jpeg', 'png', 'JPG', 'JPEG', 'PNG');
                if (!in_array($file->getExtensionName(), $needType)) {
                    $msg = '此文件类型不允许上传';
                } else {
                    $filePath = Yii::app()->params['uploadPath'] . '/crads/';

                    if (!is_dir($filePath)) {
                        mkdir($filePath, 0777);
                    }
                    $ext = $file->getExtensionName();
                    $saveName = date("Ydm") . '_' . uniqid() . '.' . $ext;

                    if ($file->saveAs($filePath . $saveName)) {
                        Yii::import('application.extensions.image.Image');
                        $image = new Image($filePath.$saveName);
                        $image->resize(600, 600);
                        if($image->save()){
                            if($cardId){
                                Yii::import('common.models.identity.*');
                                $cardObj = Cards::model()->findByPk($cardId);
                                $data = CJSON::decode($cardObj->data);
                                if(file_exists($filePath . $data["photo"])){
                                    unlink($filePath . $data["photo"]);
                                }
                                $file = pathinfo($filePath  . $saveName, PATHINFO_EXTENSION);
                                @rename($filePath . $saveName, $filePath . $cardObj->id . "." . $file);

                                $data["photo"] = $cardObj->id . "." .$file;
                                $cardObj->data = CJSON::encode($data);
                                $cardObj->save();
                            }
                            $msg = 'success';
                            $baseUrl = array('photoUrl' => Mims::CreateUploadUrl('crads/' . $saveName));
                            $this->addMessage('state', 'success');
                            $this->addMessage('data', $baseUrl);
                            $this->showMessage();
                        }else{
                            $msg = '压缩失败';
                        }
                    } else {
                        $msg = '文件上传失败';
                    }
                }
            }
        }

        $this->addMessage('state', 'fail');
        $this->addMessage('data', $msg);
        $this->showMessage();

    }

    public function actionSongs()
    {
        $this->subPageTitle = Yii::t('navigations', 'Children Songs');
        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/mobile2/js/APlayer/APlayer.min.js');
        $cs->registerCssFile(Yii::app()->themeManager->baseUrl.'/mobile2/js/APlayer/APlayer.min.css');

        Yii::import('common.models.content.Content');

        $crit = new CDbCriteria();
        $crit->compare('category', 'newsongs');
        $crit->order = 'weight';
        $songs = Content::model()->findAll($crit);

        $data = array();
        foreach ($songs as $k => $song) {
            $data[$k]['name'] = CommonUtils::autoLang($song->cn_title, $song->en_title);
            $data[$k]['artist'] = 'Ivy Teachers';
            $arr = unserialize($song->linkitem);
            $data[$k]['url'] = $arr[0];
            $data[$k]['cover'] = Yii::app()->homeUrl . "/themes/mobile2/images/ivy.jpg";
            $data[$k]['content'] = CommonUtils::autoLang($song->cn_content, $song->en_content);
        }
        $this->render('songs', array('data' => $data));
    }

    public function actionSemester()
    {
        $this->subPageTitle =Yii::t("portfolio", "Semester Report");
        $childid = $this->getChildId();
        $childObj = $this->myChildObjs[$childid];

        Yii::import('common.models.portfolio.*');
        Yii::import('common.models.reportCards.*');
        Yii::import('common.components.teaching.*');
        Yii::import('common.models.secondary.*');
        Yii::import('common.models.calendar.*');

        // 获取幼儿园报告
        $reportList = array('m'=>array(), 'k'=>array(), 'g'=>array());

        $criteria = new CDbCriteria();
        $criteria->compare('t.childid', $childid);
        $criteria->compare('t.stat', 20);
        $criteria->order = 't.startyear DESC, t.semester DESC';
        $reports = SReport::model()->findAll($criteria);

        foreach($reports as $_report){
            if(!isset($reportList['m'][$_report->startyear])){
                $reportList['m'][$_report->startyear] = array();
            }
            $reportList['m'][$_report->startyear][$_report->id]['id'] = $_report->id;
            $reportList['m'][$_report->startyear][$_report->id]['title'] = ($_report->semester == 1) ? Yii::t("portfolio", 'Fall Semester') : Yii::t("portfolio", 'Spring Semester');
        }
        //最近的学年排在前面
        krsort($reportList['m']);

        // 获取小学报告
        $criteria = new CDbCriteria();
        $criteria->compare('t.child_id', $childid);
        $criteria->compare('t.status', 1);
        $criteria->order = ('t.startyear DESC');
        $reports = ReportsData::model()->findAll($criteria);
        $reportHtml = '';
        if (!empty($reports)){
            foreach ($reports as $report){
                $reportList['k'][$report->startyear][$report->id] = $report->id;
            }
        }

        // 获取中学报告
        $crit = new CDbCriteria();
        $crit->compare('t.childid', $this->getChildId());
        $crit->compare('t.is_stat', 1);
        $crit->order = "t.calender DESC , report.start_time desc";
        $reports = AchievementReportChild::model()->with('report')->findAll($crit);

        if($reports){
            foreach($reports as $report){
                $reportList['g'][$report->calendarInfo->startyear][$report->report_id]['title'] = $report->report->getTitle();
                $reportList['g'][$report->calendarInfo->startyear][$report->report_id]['id'] = $report->id;
            }
        }

        $this->render('semester', array(
            'reportList' => $reportList,
            'childid' => $childid,
            'childObj' => $childObj,
        ));
    }

    // 下载幼儿园学期报告
    public function actionDownloadSemeter()
    {
        $reportid = Yii::app()->request->getParam('reportid');
        if ($reportid) {
            Yii::import('common.models.portfolio.SReport');
            $report = SReport::model()->findByAttributes(array('id'=>$reportid, 'childid'=>$this->getChildId()));
            if($report && $report->stat == 20 && $report->custom_pdf){
                $pdfUrl = CommonUtils::getPdfUrl($report->custom_pdf, $report->custom);
                echo '<script>location.href="'.$pdfUrl.'";setTimeout(function(){history.back()}, 10000);</script>';
            }
        }
    }

    // 下载小学报告
    public function actionDownloadSemeter2()
    {
        Yii::import('common.models.reportCards.*');
        Yii::import('common.components.teaching.*');

        $reportid = Yii::app()->request->getParam('reportid');
        $report = ReportsData::model()->findByAttributes(array('id'=>$reportid, 'child_id'=>$this->getChildId()));
        if ($report && $report->custom_pdf && $report->pdf_file) {
            $pdfUrl = CommonUtils::getPdfUrl($report->custom_pdf, $report->custom, true);
            echo '<script>location.href="'.$pdfUrl.'";setTimeout(function(){history.back()}, 10000);</script>';
        }
    }

    // 下载中学报告
    public function actionDownloadSemeter3()
    {
        Yii::import('common.models.secondary.*');
        $reportid = Yii::app()->request->getParam('reportid');
        $report = AchievementReportChild::model()->findByAttributes(array('id' => $reportid, 'childid' => $this->getChildId()));
        Yii::import('common.models.reportCards.*');
        if($report){
            if ($report->is_cache == 1 && $report->cache_pdf) {
                $pdfUrl = CommonUtils::getPdfUrl($report->cache_pdf, 0, true);
                echo '<script>location.href="'.$pdfUrl.'";setTimeout(function(){history.back()}, 10000);</script>';
            }
        }
    }
}
