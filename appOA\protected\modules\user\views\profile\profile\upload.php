<style>
    .avatar-view {
        display: block;
        margin: 15% auto 5%;
        height: 220px;
        width: 220px;
        border: 3px solid #fff;
        border-radius: 5px;
        box-shadow: 0 0 5px rgba(0,0,0,.15);
        cursor: pointer;
        overflow: hidden;
    }

    .avatar-view img {
        width: 100%;
    }

    .avatar-body {
        padding-right: 15px;
        padding-left: 15px;
    }

    .avatar-upload {
        overflow: hidden;
    }

    .avatar-upload label {
        display: block;
        float: left;
        clear: left;
        width: 100px;
    }

    .avatar-upload input {
        display: block;
        margin-left: 110px;
    }

    .avater-alert {
        margin-top: 10px;
        margin-bottom: 10px;
    }

    .avatar-wrapper {
        height: 364px;
        width: 100%;
        box-shadow: inset 0 0 5px rgba(0,0,0,.25);
        background-color: #fcfcfc;
        overflow: hidden;
        text-align: center;
        line-height: 364px;
    }

    .avatar-wrapper img {
        display: block;
        height: auto;
        max-width: 100%;
    }

    .avatar-preview {
        float: left;
        margin-right: 15px;
        border: 1px solid #eee;
        border-radius: 4px;
        background-color: #fff;
        overflow: hidden;
    }

    .avatar-preview:hover {
        border-color: #ccf;
        box-shadow: 0 0 5px rgba(0,0,0,.15);
    }

    .avatar-preview img {
        width: 100%;
    }

    .preview {
        height: 120px;
        width: 120px;
    }

    @media (min-width: 992px) {
        .avatar-preview {
            float: none;
        }
    }
    .progress{
        width: 300px;
        margin: 140px auto;
    }
</style>
<?php
Yii::import('common.models.classTeacher.InfopubStaffExtend');
$photoUrl = ($this->staff->user_avatar && $this->staff->user_avatar !== 'blank.gif') ? $this->staff->user_avatar : 'blank.jpg';
$photoPath = Yii::app()->params['OAUploadBaseUrl'].'/users/'.$photoUrl;

$publicPhoto = ($this->staff->staffInfo && $this->staff->staffInfo->staff_photo) ? $this->staff->staffInfo->staff_photo : 'blank.jpg';
?>
<div class="panel panel-default">
    <div class="panel-heading">
        <h3 class="panel-title">
            <?php echo Yii::t('user', 'Public Photo')?>
        </h3>
    </div>
    <div class="panel-body">
        <p class="mb16">
            <?php echo CHtml::image(Yii::app()->params['OAUploadBaseUrl'].'/infopub/staff/'.$publicPhoto, $this->staff->name, array('class' => 'img-rounded', 'style' => 'width: 90px'));?>
        </p>
        <p class="text-muted">
            <span class="glyphicon glyphicon-info-sign"></span>
            <?php echo Yii::t('user', 'Public photos are managed by HR department, please contact HR if you need to make adjustments.')?>
        </p>
    </div>
</div>

<div class="panel panel-default">
    <div class="panel-heading">
        <h3 class="panel-title edit-title">
            <!--上传头像-->
        </h3>
    </div>
    <form class="avatar-form J_ajaxForm" method="post" action="<?php echo $this->createUrl('saveAvatar'); ?>">
        <div class="panel-body" id="up-container">
            <button class="btn btn-primary mb15" id="select-photo" type="button"><?php echo Yii::t('user','Select a Photo');?></button>
            <div class="row mb15">
                <input type="hidden" name="staffid" id="staffid">
                <input type="hidden" name="data" id="data">
                <input type="hidden" name="ext" id="ext">
                <div class="col-md-5">
                    <div class="avatar-wrapper"></div>
                </div>
                <div class="col-md-7">
                    <div class="avatar-preview preview"><?php echo CHtml::image($photoPath,$this->staff->name);?></div>
                </div>
            </div>
        </div>
        <div class="panel-footer">
            <button type="button" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global','Save');?></button>
        </div>
    </form>
</div>
<script>
    var uploadBaseUrl = '<?php echo Yii::app()->params['OAUploadBaseUrl'];?>'; //员工照片地址
    var uploader;
    $(function(){
        uploader = new plupload.Uploader({
            runtimes : 'flash,html5,silverlight,html4',
            browse_button : 'select-photo',
            container: document.getElementById('up-container'),
            url : '<?php echo $this->createUrl('upAvatar') ?>',
            flash_swf_url : '<?php echo Yii::app()->themeManager->baseUrl . '/base/js/plupload/Moxie.swf' ?>',
            silverlight_xap_url : '<?php echo Yii::app()->themeManager->baseUrl . '/base/js/plupload/Moxie.xap' ?>',
            multi_selection : false,
            multipart_params : {},
            resize : {
                width: 500,
                height: 500
            },
            filters : {
                max_file_size : '10mb',
                mime_types: [
                    {title : "<?php echo Yii::t('teaching', 'Image files'); ?>", extensions : "jpg,gif,png,jpeg"}
                ]
            },

            init: {
                QueueChanged: function(up) {
                // Called when queue is changed by adding or removing files
                up.start();
                var html = '<div class="progress">';
                html += '<div class="progress-bar" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>';
                html += '</div>';
                    $('#up-container .avatar-wrapper').html(html);
                },
                UploadProgress: function(up, file) {
                    $('#up-container .avatar-wrapper .progress .progress-bar').attr('aria-valuenow', file.percent).css('width', file.percent + '%').html(file.percent + '%');
                },
                FileUploaded: function(up, file, info) {
                    // Called when file has finished uploading
                    if (info.status == 200) {
                        var response = eval('(' + info.response + ')');
                        $('#ext').val(response.ext);
                        var img = $('<img src="' + response.url + '">');
                        $('#up-container .avatar-wrapper').html(img);
                        img.cropper({
                            aspectRatio: 1,
                            minWidth: 200,
                            dragCrop: false,
                            preview: $('#up-container .preview'),
                            done: function(data) {
                                var json = [
                                        '{"x":' + data.x,
                                        '"y":' + data.y,
                                        '"height":' + data.height,
                                        '"width":' + data.width + "}"
                                    ].join();
                                        $('#data').val(json);
                            }
                        });
                    }
                }
            }
        });
        uploader.init();
    })
</script>