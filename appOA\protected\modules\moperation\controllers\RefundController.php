<?php

class RefundController extends ProtectedController
{
    public $actionAccessAuths = array(
    	'index' => 'oOperationsView',
    );

    public function init() {
        parent::init();
        Yii::import('common.models.calendar.*');

        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'hqOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site','HQ Operations');

        $cs = Yii::app()->clientScript;
        $cs->registerCssFile($cs->getCoreScriptUrl().'/jui/css/base/jquery-ui.css');
        $cs->registerScriptFile($cs->getCoreScriptUrl().'/jui/js/jquery-ui.min.js');
    }

	public function actionIndex()
	{
	    $year = array();

		$this->render('index');
	}

	public function actionPreviewData()
    {
        $ret = array();

        $yid    = Yii::app()->request->getPost('yid', 0);
        $type   = Yii::app()->request->getPost('type', 'tuition');
        $title  = Yii::app()->request->getPost('title', '');
        $start  = Yii::app()->request->getPost('start', '');
        $end    = Yii::app()->request->getPost('end', '');

        $file = CUploadedFile::getInstanceByName('csv');

        if ($file) {
            $filePath = Yii::app()->params['xoopsVarPath'] . '/refund/';
            $fileName = strtolower('tmp_' . $this->staff->uid . '_' . time() . '.' . $file->getExtensionName());
            $theFile = $filePath . $fileName;
            $file->saveAs($theFile);

            if (file_exists($theFile)) {
                $data = file($theFile);
                foreach ($data as $datum) {
                    $_datum = explode(',', $datum);
                    $ret[] = array(
                        'childid' => trim($_datum[0]),
                        'amount' => trim($_datum[1]),
                        'yid' => $yid,
                        'type' => $type,
                        'title' => $title,
                        'start' => $start,
                        'end' => $end,
                    );
                }
            }
        }

        if (!$ret || !$yid || !$type || !$title || !$start || !$end) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', 'Fail!');
        } else {
            $this->addMessage('state', 'success');
            $this->addMessage('message', 'Success!');
            $this->addMessage('data', $ret);
            $this->addMessage('callback', 'callback');
        }
        $this->showMessage();
    }

    public function actionProcess()
    {
        Yii::import('common.models.invoice.*');

        $yid    = Yii::app()->request->getPost('yid', 0);
        $type   = Yii::app()->request->getPost('type', 'tuition');
        $title  = Yii::app()->request->getPost('title', '');
        $start  = Yii::app()->request->getPost('start', '');
        $end    = Yii::app()->request->getPost('end', '');
        $childid= Yii::app()->request->getPost('childid', 0);
        $amount = Yii::app()->request->getPost('amount', '');

        $time = time();

        $this->addMessage('state', 'success');

        $v1 = ChildProfileBasic::model()->findByPk($childid);

        $memo = $title;
        $inout = 'in';
        $payment_type = $type;
        $startdate = strtotime($start);
        $enddate = strtotime($end);
        $criter = new CDbCriteria();
        $criter->compare('t.childid', $v1->childid);
        $criter->compare('t.inout', $inout);
        $criter->compare('t.calendar_id', $yid);
        $criter->compare('t.payment_type', $payment_type);
        $criter->compare('invoiceInfo.startdate', '<='.$startdate);
        $criter->compare('invoiceInfo.enddate', '>='.$enddate);
        $criter->compare('invoiceInfo.status', 20);
        $criter->order='timestampe DESC';
        $Transaction = InvoiceTransaction::model()->with('invoiceInfo')->find($criter);
        if($Transaction) {
            $transaction = Yii::app()->db->beginTransaction();

            $model = new Invoice;//print_r($item->childid);
            $model->calendar_id = $yid;
            $model->amount = floatval($amount);
            $model->original_amount = $model->amount;
            $model->fee_type = $Transaction->fee_type;
            $model->userid = 1;
            $model->childid = $v1->childid;
            $model->schoolid = $Transaction->schoolid;
            $model->classid = $Transaction->classid;
            $model->payment_type = $payment_type;
            $model->startdate = $startdate;
            $model->enddate = $enddate;
            $model->inout = 'out';
            $model->title = $memo;
            $model->memo = $memo;
            $model->timestamp = $time;
            $model->last_paid_timestamp = $time;
            $model->status = 20;
            if (!$model->save()) {
                $transaction->rollback();
                $this->addMessage('message', current($model->getErrors()));
            }
            $item = new InvoiceTransaction;
            $item->calendar_id = $model->calendar_id;
            $item->invoice_id = $model->invoice_id;
            $item->operator_uid = $model->userid;
            $item->amount = $model->amount;
            $item->fee_type = $model->fee_type;
            $item->childid = $v1->childid;
            $item->schoolid = $model->schoolid;
            $item->classid = $model->classid;
            $item->payment_type = $model->payment_type;
            $item->inout = $model->inout;
            $item->title = $model->title;
            $item->memo = $model->memo;
            $item->timestampe = $time;
            $item->transfer_timestamp = $time;
            $item->startdate = $model->startdate;
            $item->enddate = $model->enddate;
            $item->transactiontype = 50;
            if (!$item->save()) {
                $transaction->rollback();
                $this->addMessage('message', current($item->getErrors()));
            }

            $item_refund = new InvoiceChildRefund;
            $item_refund->childid = $v1->childid;
            $item_refund->schoolid = $model->schoolid;
            $item_refund->classid = $model->classid;
            $item_refund->yid = $model->calendar_id;
            $item_refund->invoice_id = $model->invoice_id;
            $item_refund->on_invoice_id = $Transaction->invoice_id;
            $item_refund->transaction_id = $item->id;
            $item_refund->on_transaction_id = $Transaction->id;
            $item_refund->title = $model->title;
            $item_refund->amount = $model->amount;
            $item_refund->payment_type = $model->payment_type;
            $item_refund->memo = $model->memo;
            $item_refund->startdate = $startdate;
            $item_refund->enddate = $enddate;
            $item_refund->timestamp = $time;
            $item_refund->userid = $model->userid;
            $item_refund->status = 20;
            $item_refund->refund_type = 41;
            if (!$item_refund->save()) {
                $transaction->rollback();
                $this->addMessage('message', current($item_refund->getErrors()));
            }

            $ChildCredit = new ChildCredit;
            $ChildCredit->childid = $v1->childid;
            $ChildCredit->schoolid = $model->schoolid;
            $ChildCredit->classid = $model->classid;
            $ChildCredit->yid = $model->calendar_id;
            $ChildCredit->amount = $model->amount;
            $ChildCredit->inout = $inout;
            $ChildCredit->itemname = $payment_type;
            $ChildCredit->invoice_id = $model->invoice_id;
            $ChildCredit->transaction_id = $item->id;
            $ChildCredit->userid = $model->userid;
            $ChildCredit->updated_timestamp = $time;
            $ChildCredit->balance = $ChildCredit->getChildCredit($v1->childid) + $model->amount;
            if (!$ChildCredit->save()) {
                $this->addMessage('message', current($ChildCredit->getErrors()));
            }

            $v1->credit = $ChildCredit->balance;
            if (!$v1->save()) {
                $transaction->rollback();
                $this->addMessage('message', $v1->getErrors());
            }
            $transaction->commit();
        } else {
            $this->addMessage('message', 'no invoice');
        }

        $this->showMessage();
    }
}
