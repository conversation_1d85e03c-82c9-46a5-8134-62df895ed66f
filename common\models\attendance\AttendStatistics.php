<?php

/**
 * This is the model class for table "ivy_attend_statistics".
 *
 * The followings are the available columns in table 'ivy_attend_statistics':
 * @property integer $id
 * @property string $schoolid
 * @property integer $classid
 * @property string $month
 * @property string $data
 * @property integer $updated
 * @property integer $updated_by
 */
class AttendStatistics extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_attend_statistics';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('schoolid, classid, month, updated, updated_by', 'required'),
			array('classid, updated, updated_by', 'numerical', 'integerOnly'=>true),
			array('schoolid, month', 'length', 'max'=>30),
			array('data', 'safe'),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, schoolid, classid, month, data, updated, updated_by', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'class' => array(self::BELONGS_TO, 'IvyClass',"classid")
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'schoolid' => 'Schoolid',
			'classid' => 'Classid',
			'month' => 'Month',
			'data' => 'Data',
			'updated' => 'Updated',
			'updated_by' => 'Updated By',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('classid',$this->classid);
		$criteria->compare('month',$this->month,true);
		$criteria->compare('data',$this->data,true);
		$criteria->compare('updated',$this->updated);
		$criteria->compare('updated_by',$this->updated_by);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return AttendStatistics the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

    public static function getConfig()
    {
        return array(
            1 => '<td class="present text-center">✓</td>',
            10 => '<td class="sick text-center">'.Yii::t('attends','S').'</td>',
            20 => '<td class="personal text-center">'.Yii::t('attends','P').'</td>',
            40 => '<td class="tardy text-center" >'.Yii::t('attends','T').'</td>',
            60 => '<td class="tardy text-center"> <span>✓</span> '.Yii::t('attends','T').'</td>',
            70 => '<td class="tardy text-center">'.Yii::t('attends','A').'</td>',
        );
    }
}
