<?php


class WSurveyFeedbackTextForm extends CFormModel
{
	
	public $fb_text = null;
	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('fb_id, survey_id, topic_id, classid, childid, userid, ic_id, rating, hide2school, followup', 'numerical', 'integerOnly'=>true),
			array('schoolid', 'length', 'max'=>255),
			array('fb_text', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			//array('id, fb_id, survey_id, topic_id, classid, childid, userid, ic_id, schoolid, rating, hide2school, followup, fb_text', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'fb_id' => 'Fb',
			'survey_id' => 'Survey',
			'topic_id' => 'Topic',
			'classid' => 'Classid',
			'childid' => 'Childid',
			'userid' => 'Userid',
			'ic_id' => 'Ic',
			'schoolid' => 'Schoolid',
			'rating' => 'Rating',
			'hide2school' => 'Hide2school',
			'followup' => 'Followup',
			'fb_text' => 'Fb Text',
		);
	}

}