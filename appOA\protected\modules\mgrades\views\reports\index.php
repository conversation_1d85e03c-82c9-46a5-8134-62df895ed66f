<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo Yii::t('site','Campus Workspace');?></li>
        <li class="active"><?php echo Yii::t('site','Reports Templates');?></li>
    </ol>

    <div class="row">
        <div class="col-md-12">
            <div class="mb10">
                <button type="button" class="btn btn-primary" onclick="createTemplate(0);">
                    <?php echo Yii::t('report', 'Create Template');?>
                </button>
                <a role="button" class="btn btn-default" href="<?php echo $this->createUrl('option');?>"
                   target="_blank">
                    <?php echo Yii::t('report', 'Progress Definitions');?>
                </a>
            </div>
        </div>

        <div class="col-md-12">
            <?php
            $this->widget('ext.ivyCGridView.BsCGridView', array(
                'id'=>'template-grid',
                'dataProvider'=>$dataProvider,
                'columns'=>array(
                    array(
                        'header'=>Yii::t('labels', 'Title'),
                        'value'=>'$data->getTitle()'
                    ),
                    array(
                        'name' => 'for_age',
                        'type'=>'raw',
                        'value' => array($this, 'getAge'),
                    ),
                    array(
                        'name' => 'active',
                        'type' => 'raw',
                        'value' => array($this, 'getActive'),
                    ),
                    array(
                        'name' => Yii::t('global', 'Action'),
                        'type' => 'raw',
                        'value' => array($this, 'getButton'),
                    ),
                ),
            )); ?>
        </div>
    </div>
</div>


<!-- 新建Template Modal -->
<div class="modal" id="templateEditModal" tabindex="-1" role="dialog" aria-labelledby="templateEditModalLabel"
     aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title" id="myModalLabel"><?php echo Yii::t('site','Reports Templates');?> <small></small></h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-12">
                        <form class="form-horizontal J_ajaxForm"
                              action="<?php echo $this->createUrl('//mgrades/reports/index');?>"
                              method="POST">
                            <div id="form-data">
                                <!--place holder-->
                            </div>

                            <div class="pop_bottom">
                                <button onclick="$('#templateEditModal').modal('hide')" type="button" class="btn btn-default pull-right"><?php echo Yii::t('global','Cancel');?></button>
                                <button type="submit" class="btn btn-primary J_ajax_submit_btn pull-right mr10"><?php echo Yii::t('global','Submit');?></button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Edit Category Modal -->
<div class="modal" id="categoryEditModal" tabindex="-1" role="dialog" aria-labelledby="categoryEditModalLabel">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title"><?php echo Yii::t('report','Categories Edit');?> <small></small></h4>
            </div>
            <form class="form-horizontal J_ajaxForm"
                  action="<?php echo $this->createUrl('saveCategory');?>"
                  method="POST">
            <div class="modal-body">
                <div id="body-data">

                </div>
                <div class="row">
                    <div class="col-md-12">
                        <button type="button" class="btn btn-primary btn-xs" onclick="addRoot()"><?php echo Yii::t
                                ('report', 'Add Root Category');
                            ?></button>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <input type="hidden" name="tid" id="tid">
                <button type="button" class="btn btn-default pull-right" data-dismiss="modal"><?php echo Yii::t('global','Cancel');?></button>
                <button type="submit" class="btn btn-primary J_ajax_submit_btn pull-right mr10"><?php echo Yii::t('global','Submit');?></button>
            </div>
            </form>
        </div>
    </div>
</div>

<div class="modal" id="optionEditModal" tabindex="-1" role="dialog" aria-labelledby="optionEditModalLabel">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title"><?php echo Yii::t('report','Options Edit');?> <small></small></h4>
            </div>
            <form class="form-horizontal J_ajaxForm"
                  action="<?php echo $this->createUrl('saveOption');?>"
                  method="POST">
                <div class="modal-body">
                    <div id="body-data">

                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <button type="button" class="btn btn-primary btn-xs" onclick="addOption()">
                                <?php echo Yii::t('report','Add Option');?></button>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <input type="hidden" name="tid" id="tid">
                    <button type="button" class="btn btn-default pull-right" data-dismiss="modal"><?php echo Yii::t('global','Cancel');?></button>
                    <button type="submit" class="btn btn-primary J_ajax_submit_btn pull-right mr10"><?php echo Yii::t('global','Submit');?></button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php
    $labels = ReportCardTemplate::attributeLabels();
    $classTypes = IvyClass::getClassTypes(true, $this->branchObj->type);
?>

<!--模版编辑弹窗-->
<script type="text/template" id="reportTpl-edit-template">
    <div class="form-group"  model-attribute="title_en">
        <?php echo CHtml::label($labels['title_en'], CHtml::getIdByName('ReportCardTemplate[title_en]'), array('class'=>'col-sm-3 control-label'));?>
        <div class="col-sm-9">
            <?php echo CHtml::textField('ReportCardTemplate[title_en]', '<%= title_en %>', array('class'=>'form-control','encode'=>false));?>
        </div>
    </div>
    <div class="form-group"  model-attribute="title_cn">
        <?php echo CHtml::label($labels['title_cn'], CHtml::getIdByName('ReportCardTemplate[title_cn]'), array('class'=>'col-sm-3 control-label'));?>
        <div class="col-sm-9">
            <?php echo CHtml::textField('ReportCardTemplate[title_cn]', '<%= title_cn %>', array('class'=>'form-control','encode'=>false));?>
        </div>
    </div>
    <div class="form-group" model-attribute="for_age">
        <?php echo CHtml::label($labels['for_age'], CHtml::getIdByName('ReportCardTemplate[for_age]'), array('class'=>'col-sm-3 control-label'));?>
        <div class="col-sm-9">
            <?php echo CHtml::checkBoxList('ReportCardTemplate[for_age]','<%= for_age %>', $classTypes,
            array('class'=>'checkbox-inline','encode'=>false, 'separator'=>'<br>'));?>
        </div>
    </div>
    <div class="form-group" model-attribute="active">
        <?php echo CHtml::label($labels['active'], CHtml::getIdByName('ReportCardTemplate[active]'), array('class'=>'col-sm-3 control-label'));?>
        <div class="col-sm-9">
            <div class="checkbox">
                <label>
                    <?php echo CHtml::checkBox('ReportCardTemplate[active]', false);?>
                    <?php echo $labels['active'];?>
                </label>
            </div>
        </div>
    </div>

    <?php echo CHtml::hiddenField('ReportCardTemplate[id]','<%= id %>',array('encode'=>false));?>
    <?php echo CHtml::hiddenField('ReportCardTemplate[branch_id]', $this->branchId);?>
</script>

<!--添加大分类-->
<script type="text/template" id="category-root-template">
    <div class="row mb10">
        <% if(type == 'new'){%>
        <input type="hidden" name="new[root][]" value="<%= id%>">
        <% }else{%>
        <input type="hidden" name="old[root][]" value="<%= id%>">
        <% }%>
        <input type="hidden" name="root_weight[<%= id%>]" value="<%= weight%>">
        <div class="col-md-10">
            <?php
                echo CHtml::dropDownList("program[<%=id%>][program]", "", $cfgCampusProgram['programs'], array('class'=>'form-control','empty'=>Yii::t('global', 'Please Select'),'encode'=>false));
            ?>
        </div>
   
        <div class="col-md-2">
            <a role="button" class="btn btn-default btn-sm sort-header1" style="cursor: move;"><span class="glyphicon
             glyphicon-move"></span></a>
            <button type="button" class="btn btn-danger btn-sm J_remove"><span class="glyphicon glyphicon-minus"></span></button>
            <button type="button" class="btn btn-info btn-sm J_add"><span class="glyphicon glyphicon-plus"></span></button>
        </div>
    </div>
    <div id="root-<%= id%>"></div>
    <hr>
</script>

<!--添加小分类-->
<script type="text/template" id="category-sub-template">
    <% if(type == 'new'){%>
    <input type="hidden" name="new[sub][]" value="<%= id%>">
    <% }else{%>
    <input type="hidden" name="old[sub][]" value="<%= id%>">
    <% }%>
    <input type="hidden" name="sub_title[<%= id%>][pid]" value="<%= pid%>">
    <input type="hidden" name="sub_weight[<%= id%>]" value="<%= weight%>">
    <div class="col-md-1"></div>
    <div class="col-md-4">
        <input class="form-control" name="sub_title[<%= id%>][en]" value="<%= title_en%>"
               placeholder="<?php echo Yii::t('report', 'Sub category (English)');?>">
    </div>
    <div class="col-md-1"></div>
    <div class="col-md-4">
        <input class="form-control" name="sub_title[<%= id%>][cn]" value="<%= title_cn%>"
               placeholder="<?php echo Yii::t('report', 'Sub category (Chinese)');?>">
    </div>
    <div class="col-md-2">
        <a role="button" class="btn btn-default btn-sm sort-header2" style="cursor: move;"><span class="glyphicon
        glyphicon-move"></span></a>
        <button type="button" class="btn btn-danger btn-sm J_remove_sub"><span class="glyphicon glyphicon-minus"></span></button>
    </div>
</script>

<!--选项管理-->
<script type="text/template" id="option-template">
    <% if(type == 'new'){%>
    <input type="hidden" name="new[]" value="<%= id%>">
    <% }else{%>
    <input type="hidden" name="old[]" value="<%= id%>">
    <% }%>
    <input type="hidden" name="weight[<%= id%>]" value="<%= weight%>">
    <div class="row mb10">
        <div class="col-md-5">
            <input class="form-control" name="title[<%= id%>][en]" value="<%= title_en%>"
                   placeholder="<?php echo Yii::t('report','Option Title (English)');?>">
        </div>
        <div class="col-md-5">
            <input class="form-control" name="title[<%= id%>][cn]" value="<%= title_cn%>"
                   placeholder="<?php echo Yii::t('report','Option Title (Chinese)');?>">
        </div>
        <div class="col-md-2">
            <a role="button" class="btn btn-default btn-sm sort-header" style="cursor: move;"><span class="glyphicon
            glyphicon-move"></span></a>
            <button type="button" class="btn btn-danger btn-sm J_remove"><span class="glyphicon glyphicon-minus"></span></button>
        </div>
    </div>
    <div class="row mb10">
        <div class="col-md-10">
            <input class="form-control" name="description[<%= id%>][en]" value="<%= desc_en%>"
                   placeholder="<?php echo Yii::t('report','Option Description (English)');?>"></div>
        <div class="col-md-2"></div>
    </div>
    <div class="row mb10">
        <div class="col-md-10">
            <input class="form-control" name="description[<%= id%>][cn]" value="<%= desc_cn%>"
                   placeholder="<?php echo Yii::t('report','Option Description (Chinese)');?>"></div>
        <div class="col-md-2"></div>
    </div>
    <hr>
</script>

<script>
    var ages = <?php echo CJSON::encode($ages)?>;
    var createTemplate = null; //创建新模版
    var openReportTplEditModal = null; //打开模版编辑弹窗
    var formDataTpl = _.template($('#reportTpl-edit-template').html());

    var RootList = Backbone.Collection.extend({
        comparator: 'weight'
    });
    var SubList = Backbone.Collection.extend({
        comparator: 'weight'
    });
    var OptionList = Backbone.Collection.extend({
        comparator: 'weight'
    });
    var roots = new RootList;
    var subs = new SubList;
    var options = new OptionList;
    var categoryData = {};
    var app;
    var i=0, j=10000;

    $(function(){
        createTemplate = function(id) {
            var reportTemplate = {};
            if (id == 0) {
                reportTemplate = {id:0,title_en:'',title_cn:'',for_age:'',branch_id:'',in_use:0,updated_user:0,active:0,updated:0};
            } else {
                $.ajax({
                    type: 'get',
                    url: '<?php echo $this->createUrl('getTemplate')?>',
                    data: {id: id},
                    async: false,
                    dataType: 'json'
                }).done(function(data){
                    reportTemplate = data;
                });
            }
            openReportTplEditModal(reportTemplate);
        }

        openReportTplEditModal = function(tplObj) {
            var _formData = formDataTpl(tplObj);
            $('#form-data').html(_formData);
            if(tplObj.active == 1)
                $('#ReportCardTemplate_active').attr('checked', true);
            if(ages){
                for(var _age in ages){
                    $('#ReportCardTemplate_for_age input[value="'+ages[_age]+'"]').attr('disabled', true);
                }
            }
            if(tplObj.for_age){
                for(var age in tplObj.for_age){
                    $('#ReportCardTemplate_for_age input[value="'+tplObj.for_age[age]+'"]').removeAttr('disabled').attr('checked', true);
                }
            }
            $('#templateEditModal').modal();
        }

        cbSuccess = function(data){
            ages = data;
            $.fn.yiiGridView.update('template-grid');
            $('#templateEditModal').modal('hide');
        }

        editCategory = function(tid){
            $("#categoryEditModal #body-data").html('');
            $.ajax({
                type: 'get',
                url: '<?php echo $this->createUrl('getCateGory')?>',
                data: {id: tid},
                dataType: 'json'
            }).done(function(data){
                $('#categoryEditModal').modal({backdrop: 'static', keyboard: false});
                $('#categoryEditModal #tid').val(tid);
                roots.reset(data.root);
                subs.reset(data.sub);
                selectedVal(data.root);
                $( "#categoryEditModal #body-data .item div[id^='root']" ).sortable({
                    placeholder: "ui-state-highlight",
                    handle: ".sort-header2",
                    axis: 'y',
                    opacity: 0.8,
                    start: function( event, ui ) {
                        $('.ui-state-highlight').height($(ui['item'][0]).height());
                    },
                    update: function(event, ui){
                        var cc = 1;
                        $( "#categoryEditModal #body-data .item div[id^='root']>div" ).each(function(){
                            $(this).find('input[name^="sub_weight"]').val(cc++);
                        });
                    }
                }).disableSelection();
                i=roots.length+1;
            });

        }

        addRoot = function(){
            roots.add({id:i++, type:'new', title_en: '', title_cn:'', weight:(roots.length+1)});
        }

        cbSave = function(){
            $('#categoryEditModal').modal('hide');
        }

        cbSaveOption = function(){
            $('#optionEditModal').modal('hide');
        }

        delCateGory = function(id){
            var ret=false;
            $.ajax({
                type: 'post',
                url: '<?php echo $this->createUrl('delCateGory')?>',
                data: {id: id},
                async: false,
                dataType: 'json'
            }).done(function(data){
                var error = data.state == 'success' ? 0 : 1;
                resultTip({msg: data.message, error: error});
                if(data.state == 'success'){
                    ret = true;
                }
            });
            return ret;
        }

        editOption = function(id){
            $("#optionEditModal #body-data").html('');
            $.ajax({
                type: 'get',
                url: '<?php echo $this->createUrl('getOption')?>',
                data: {id: id},
                dataType: 'json'
            }).done(function(data){
                $('#optionEditModal').modal();
                $('#optionEditModal #tid').val(id);
                options.reset(data);
            });
        }

        addOption = function(){
            options.add({id:i++, type: 'new', title_en: '', title_cn:'', desc_en:'', desc_cn:'', weight:(options.length+1)});
        }

        delOption = function(id){
            var ret=false;
            $.ajax({
                type: 'post',
                url: '<?php echo $this->createUrl('delOption')?>',
                data: {id: id},
                async: false,
                dataType: 'json'
            }).done(function(data){
                var error = data.state == 'success' ? 0 : 1;
                resultTip({msg: data.message, error: error});
                if(data.state == 'success'){
                    ret = true;
                }
            });
            return ret;
        }

        var rootView = Backbone.View.extend({
            tagName: 'div',
            className: 'item',
            events: {
                'click .J_remove': 'delRoot',
                'click .J_add': 'addSub'
            },
            initialize: function() {
                this.listenTo(this.model, 'change', this.render);
                this.listenTo(this.model, 'destroy', this.remove);
            },
            delRoot: function(){
                if(this.model.get('type') == 'old'){
                    if(delCateGory(this.model.get('id'))){
                        this.remove();
                    }
                }
                else{
                    this.remove();
                }
            },
            addSub: function(){
                subs.add({id:j++, pid:this.model.get('id'), type:'new', title_en: '', title_cn:'', weight:(subs.length+1)});
            },
            render: function(){
                this.$el.html( _.template($('#category-root-template').html(), this.model.attributes) );
                return this;
            }
        });

        var subView = Backbone.View.extend({
            tagName: 'div',
            className: 'row mb10',
            events: {
                'click .J_remove_sub': 'delSub'
            },
            initialize: function() {
                this.listenTo(this.model, 'change', this.render);
                this.listenTo(this.model, 'destroy', this.remove);
            },
            delSub: function(){
                if(this.model.get('type') == 'old'){
                    if(delCateGory(this.model.get('id'))){
                        this.remove();
                    }
                }
                else{
                    this.remove();
                }
            },
            render: function(){
                this.$el.html( _.template($('#category-sub-template').html(), this.model.attributes) );
                return this;
            }
        });

        var optionView = Backbone.View.extend({
            tagName: 'div',
            className: 'item',
            events: {
                'click .J_remove': 'delOption'
            },
            initialize: function() {
                this.listenTo(this.model, 'change', this.render);
                this.listenTo(this.model, 'destroy', this.remove);
            },
            delOption: function(){
                if(this.model.get('type') == 'old'){
                    if(delOption(this.model.get('id'))){
                        this.remove();
                    }
                }
                else{
                    this.remove();
                }
            },
            render: function(){
                this.$el.html( _.template($('#option-template').html(), this.model.attributes) );
                return this;
            }
        });

        var AppView = Backbone.View.extend({
            initialize: function () {
                this.listenTo(roots, 'reset', this.addAll);
                this.listenTo(roots, 'add', this.addOne);
                this.listenTo(subs, 'reset', this.addAllSub);
                this.listenTo(subs, 'add', this.addSub);
                this.listenTo(options, 'add', this.addOption);
                this.listenTo(options, 'reset', this.addAllOption);
            },
            addAll: function(){
                roots.each(this.addOne, this);
            },
            addOne: function(model) {
                var item = new rootView({model: model});
                $("#categoryEditModal #body-data").append(item.render().el);
            },
            addAllSub: function(){
                subs.each(this.addSub, this);
            },
            addSub: function(model) {
                var item = new subView({model: model});
                $("#categoryEditModal #body-data #root-"+model.get('pid')).append(item.render().el);
            },
            addAllOption: function(){
                options.each(this.addOption, this);
            },
            addOption: function(model){
                var item = new optionView({model: model});
                $('#optionEditModal #body-data').append(item.render().el);
            }
        });

        new AppView;

        $( "#optionEditModal #body-data" ).sortable({
            placeholder: "ui-state-highlight",
            handle: ".sort-header",
            axis: 'y',
            opacity: 0.8,
            start: function( event, ui ) {
                $('.ui-state-highlight').height($(ui['item'][0]).height());
            },
            update: function(event, ui){
                var cc = 1;
                $('#optionEditModal #body-data .item').each(function(){
                    $(this).find('input[name^="weight"]').val(cc++);
                });
            }
        }).disableSelection();

        $( "#categoryEditModal #body-data" ).sortable({
            placeholder: "ui-state-highlight",
            handle: ".sort-header1",
            axis: 'y',
            opacity: 0.8,
            start: function( event, ui ) {
                $('.ui-state-highlight').height($(ui['item'][0]).height());
            },
            update: function(event, ui){
                var cc = 1;
                $('#categoryEditModal #body-data .item').each(function(){
                    $(this).find('input[name^="root_weight"]').val(cc++);
                });
            }
        }).disableSelection();
    })
    
    //下拉框编辑默认值
    selectedVal = function(data){
        _.each(data,function(val,key){
            $("#program_"+val.id+"_program").val(val.program);
        })
    }
</script>

<style>
    .ui-state-highlight{
        background-color: #FCFAF1;
        border: 1px dashed #fcefa1;
        margin-bottom: 15px;
    }
</style>