<div class="h_a"><?php echo Yii::t("invoice","Invoice Detail");?></div>
<div class="table_full">
	<table width="100%">
		<colgroup>
			<col width="160" class="tar">
		</colgroup>
		<tbody>
			<tr>
				<th><?php echo Yii::t("invoice","Child Name");?></th>
				<td><?php echo $this->childObj->getChildName();?></td>
			</tr>
			<tr>
				<th><?php echo Yii::t("invoice","Invoice Title");?></th>
				<td><?php echo $invoice->title;?></td>
			</tr>
			<tr>
				<th><?php echo Yii::t("invoice","Fee Type");?></th>
				<td>
                    <?php echo $policyApi->renderFeeType($invoice->payment_type);?>
                    <?php if (!empty($invoice->feeOther)):?>
                        <?php if ($invoice->payment_type == 'afterschool' || $invoice->payment_type == 'carousel'):?>
                           <?php echo ' - '.$invoice->feeOther->cn_title; ?> ( <?php echo $invoice->feeOther->amount.'/'.Yii::t('invoice','节');?> )
                        <?php elseif ($invoice->payment_type == 'bus'):?>
                            ( <?php echo $invoice->feeOther->amount.'/'.Yii::t('invoice','月');?> )
                        <?php endif;?>
                    <?php endif;?>
                </td>
			</tr>
			<tr>
				<th><?php echo Yii::t("invoice","Invoice Status");?></th>
				<td><?php echo $policyApi->renderInvoiceStatus($invoice->status);?></td>
			</tr>
			<tr>
				<th><?php echo Yii::t("invoice","School / Class");?></th>
				<td><?php $ab = $this->getController()->getAllBranch(); echo $ab[$invoice->schoolid]['title'];?> /
					<?php echo $invoice->classInfo->title;?>
				</td>
			</tr>
			<tr>
				<th><?php echo Yii::t("invoice","School Calendar");?></th>
				<td>
					<?php echo $invoice->calendar->title;?>
					<?php echo Yii::t("invoice", " (:startY - :endY)", array(":startY"=>$invoice->calendar->startyear, ":endY"=>$invoice->calendar->startyear + 1))?>
				</td>
			</tr>
            <?php if ( $invoice->depositAmount > 0 ):?>
			<tr>
				<th><?php echo Yii::t("payment","Invoice Total Amount");?></th>
				<td><?php echo $invoice->original_amount;?></td>
			</tr>
            <?php endif;?>
			<tr>
				<th><?php echo Yii::t("payment","Invoice Amount");?></th>
				<td><?php echo $invoice->amount;?></td>
			</tr>
            <?php if ( $invoice->depositAmount > 0 ):?>
			<tr>
				<th><?php echo Yii::t("payment","使用预交学费");?></th>
				<td><?php echo $invoice->depositAmount;?></td>
			</tr>
            <?php endif;?>
			<?php if($invoice->child_service_info):?>
			<tr>
				<th><?php echo Yii::t("invoice","Service Information");?></th>
				<td><?php echo $policyApi->renderServiceInfo($invoice->child_service_info, $invoice->payment_type);?></td>
			</tr>
			<?php endif;?>
			<tr>
				<th><?php echo Yii::t("invoice","Invoice Peroid");?></th>
				<td><?php echo ($invoice->startdate * $invoice->enddate) ?
					OA::formatDateTime($invoice->startdate) . ' ~ '. OA::formatDateTime($invoice->enddate) : 'N/A';?></td>
			</tr>
			<tr>
				<th><?php echo Yii::t("invoice","Due Date");?></th>
				<td><?php echo ($invoice->duetime) ? OA::formatDateTime($invoice->duetime) : 'N/A';?></td>
			</tr>
            <?php if ($invoice->discount):?>
            <tr>
				<th><?php echo Yii::t("invoice","Discount");?></th>
                <td><?php echo CommonUtils::autoLang($invoice->discount->discountTitle->title_cn, $invoice->discount->discountTitle->title_en);?></td>
			</tr>
            <?php endif;?>
			<tr>
				<th><?php echo Yii::t("invoice","Created By");?></th>
				<td><?php echo $userNames[$invoice->userid] . ' ' . OA::formatDateTime($invoice->timestamp, 'long', 'short');?></td>
			</tr>				
		</tbody>
	</table>
</div>
<?php if(count($invoice->invoiceTransaction)):?>
<div class="h_a"><?php echo Yii::t("payment","Payment List");?></div>
<div class="table_full">
	<table width="100%">
		<colgroup>
			<col width="160" class="tar">
			<col width="160" class="tar">
			<col width="160" class="tar">
			<col width="160" class="tar">			
			<col width="180" class="tar">			
		</colgroup>
		<thead>
			<tr>
				<th><?php echo Yii::t("invoice","发票号");?></th>
				<th><?php echo Yii::t("invoice","付款金额");?></th>
				<th><?php echo Yii::t("invoice","付款日期");?></th>
				<th><?php echo Yii::t("invoice","付款方式");?></th>
				<th><?php echo Yii::t("invoice","操作人");?></th>
				<th><?php echo Yii::t("invoice","发票抬头");?></th>
			</tr>
		</thead>
		<tbody>
			<?php foreach ($invoice->invoiceTransaction as $trans):?>
			<tr>
				<td><?php echo $trans->invoice_number;?></td>
				<td><?php echo $trans->amount;?></td>
				<td><?php echo OA::formatDateTime($trans->timestampe, 'medium', 'medium');?></td>
				<td><?php echo $policyApi->renderPaymentType($trans->transactiontype);?></td>
				<td><?php echo $userNames[$trans->operator_uid];?></td>
				<td><?php echo $trans->invoice_title;?></td>
			</tr>
			<?php endforeach;?>
		</tbody>
	</table>
</div>
<?php endif;?>

<?php if(count($invoice->workflowRefund)):?>
<div class="h_a"><?php echo Yii::t("payment","Refund Information");?></div>
<div class="table_full">
	<table width="100%">
		<colgroup>
			<col width="180" class="tar">
			<col width="100" class="tar">
			<col width="180" class="tar">
			<col width="100" class="tar">			
			<col width="120" class="tar">			
			<col width="120" class="tar">			
		</colgroup>
		<thead>
			<tr>
				<th><?php echo Yii::t("invoice","标题");?></th>
				<th><?php echo Yii::t("invoice","退款金额");?></th>
				<th><?php echo Yii::t("invoice","退款期间");?></th>
				<th><?php echo Yii::t("invoice","Fee Type");?></th>
				<th><?php echo Yii::t("invoice","状态");?></th>
				<th><?php echo Yii::t("invoice","更新时间");?></th>
				<th><?php echo Yii::t("invoice","备注");?></th>
			</tr>
		</thead>
		<tbody>
			<?php foreach ($invoice->workflowRefund as $refund):?>
			<tr>
				<td><?php echo $refund->title;?></td>
				<td><?php echo ($refund->newamount > 0) ? $refund->newamount : $refund->amount;?></td>
				<td><?php echo ($refund->startdate * $refund->enddate) ?
					OA::formatDateTime($refund->startdate) . ' ~ '. OA::formatDateTime($refund->enddate) : 'N/A';?></td>
				<td><?php echo $policyApi->renderFeeType($refund->payment_type);?></td>
				<td><span class="org"><?php echo $policyApi->renderRefundStatus($refund->status);?></span></td>
				<td><?php echo OA::formatDateTime($refund->timestamp);?></td>
				<td><?php echo $refund->memo;?></td>
			</tr>
			<?php endforeach;?>
		</tbody>
	</table>
</div>
<?php endif;?>