<?php

/**
 * This is the model class for table "ivy_survey_return_child".
 *
 * The followings are the available columns in table 'ivy_survey_return_child':
 * @property integer $id
 * @property string $school_id
 * @property integer $yid
 * @property integer $classid
 * @property string $classType
 * @property integer $childid
 * @property integer $survey_id
 * @property integer $template_id
 * @property integer $is_answer
 * @property integer $status
 * @property integer $created_at
 * @property integer $created_by
 * @property string $updated_at
 * @property integer $updated_by
 */
class SurveyReturnChild extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_survey_return_child';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('school_id, yid, classid, childid, survey_id, template_id, status, created_at, created_by, updated_at, updated_by', 'required'),
			array('yid, classid, childid, survey_id, template_id, is_answer, status, created_at, created_by, updated_by', 'numerical', 'integerOnly'=>true),
			array('school_id, classType', 'length', 'max'=>255),
			array('updated_at', 'length', 'max'=>11),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, school_id, yid, classid, classType, childid, survey_id, template_id, is_answer, status, created_at, created_by, updated_at, updated_by', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'childInfo' => array(self::HAS_ONE, "ChildProfileBasic", array("childid" => "childid")),
            'classInfo' => array(self::HAS_ONE, "IvyClass", array("classid" => "classid")),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'school_id' => 'School',
			'yid' => 'Yid',
			'classid' => 'Classid',
			'classType' => 'Class Type',
			'childid' => 'Childid',
			'survey_id' => 'Survey',
			'template_id' => 'Template',
			'is_answer' => 'Is Answer',
			'status' => 'Status',
			'created_at' => 'Created At',
			'created_by' => 'Created By',
			'updated_at' => 'Updated At',
			'updated_by' => 'Updated By',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('school_id',$this->school_id,true);
		$criteria->compare('yid',$this->yid);
		$criteria->compare('classid',$this->classid);
		$criteria->compare('classType',$this->classType,true);
		$criteria->compare('childid',$this->childid);
		$criteria->compare('survey_id',$this->survey_id);
		$criteria->compare('is_answer',$this->is_answer);
		$criteria->compare('status',$this->status);
		$criteria->compare('created_at',$this->created_at);
		$criteria->compare('created_by',$this->created_by);
		$criteria->compare('updated_at',$this->updated_at,true);
		$criteria->compare('updated_by',$this->updated_by);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return SurveyReturnChild the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}


	public static function getSaveClass($surveyReturnModel,$classids,$uid)
    {
        $criteria = new CDbCriteria();
        $criteria->compare('schoolid', $surveyReturnModel->school_id);
        $criteria->compare('yid', $surveyReturnModel->yid);
        $criteria->compare('stat', 10);
        $criteria->compare('classtype', $classids);

        $criteria->index = 'classid';
        $classModel = IvyClass::model()->findAll($criteria);

        if($classModel) {
            // 拿到所有有效班级里的有效学生数据
            $criteria = new CDbCriteria();
            $criteria->compare('classid', array_keys($classModel));
            $criteria->compare('status', array(ChildProfileBasic::STATS_ACTIVE_WAITING, ChildProfileBasic::STATS_ACTIVE));
            $childModel = ChildProfileBasic::model()->findAll($criteria);

            if ($childModel) {
                foreach ($childModel as $child) {
                    $returnChildModel = new SurveyReturnChild();
                    $returnChildModel->school_id = $surveyReturnModel->school_id;
                    $returnChildModel->yid = $surveyReturnModel->yid;
                    $returnChildModel->classid = $child->classid;
                    $returnChildModel->classType = $classModel[$child->classid]->classtype;
                    $returnChildModel->childid = $child->childid;
                    $returnChildModel->survey_id = $surveyReturnModel->id;
                    $returnChildModel->template_id = $surveyReturnModel->tid;
                    $returnChildModel->is_answer = 0;
                    $returnChildModel->status = 1;
                    $returnChildModel->updated_by = $uid;
                    $returnChildModel->updated_at = time();
                    $returnChildModel->created_by = $uid;
                    $returnChildModel->created_at = time();
                    $returnChildModel->save();
                }
            }
        }
        return array('state' => 'success', 'message' => Yii::t('message', 'success'));
    }

    public static function getDelClass($rerutnid, $delResultChildClass)
    {
        $criteria = new CDbCriteria();
        $criteria->compare('survey_id', $rerutnid);
        $criteria->compare('classtype', $delResultChildClass);
        $criteria->compare('is_answer', 1);
        $count = SurveyReturnChild::model()->count($criteria);

        if(!$count) {
            $criteria = new CDbCriteria();
            $criteria->compare('survey_id', $rerutnid);
            $criteria->compare('classtype', $delResultChildClass);
            SurveyReturnChild::model()->deleteAll($criteria);
            return array('state' => 'success', 'message' => Yii::t('message', 'success'));
        }
        return array('state' => 'fail', 'message' => Yii::t('message', '您所选则删除的班级有已填写的人，不可删除'));
    }
}
