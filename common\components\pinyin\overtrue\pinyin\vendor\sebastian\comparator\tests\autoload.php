<?php
// @codingStandardsIgnoreFile
// @codeCoverageIgnoreStart
// this is an autogenerated file - do not edit
spl_autoload_register(
    function($class) {
        static $classes = null;
        if ($classes === null) {
            $classes = array(
                'se<PERSON><PERSON><PERSON><PERSON>\\comparator\\arraycomparatortest' => '/ArrayComparatorTest.php',
                'sebas<PERSON><PERSON><PERSON>\\comparator\\author' => '/_files/Author.php',
                'sebas<PERSON><PERSON><PERSON>\\comparator\\book' => '/_files/Book.php',
                'se<PERSON><PERSON><PERSON><PERSON>\\comparator\\classwithtostring' => '/_files/ClassWithToString.php',
                'se<PERSON><PERSON><PERSON><PERSON>\\comparator\\datetimecomparatortest' => '/DateTimeComparatorTest.php',
                'sebas<PERSON><PERSON><PERSON>\\comparator\\domnodecomparatortest' => '/DOMNodeComparatorTest.php',
                'se<PERSON><PERSON><PERSON><PERSON>\\comparator\\doublecomparatortest' => '/DoubleComparatorTest.php',
                'se<PERSON><PERSON><PERSON><PERSON>\\comparator\\exceptioncomparatortest' => '/ExceptionComparatorTest.php',
                'sebas<PERSON><PERSON><PERSON>\\comparator\\factorytest' => '/FactoryTest.php',
                'sebastianbergmann\\comparator\\mockobjectcomparatortest' => '/MockObjectComparatorTest.php',
                'sebastianbergmann\\comparator\\numericcomparatortest' => '/NumericComparatorTest.php',
                'sebastianbergmann\\comparator\\objectcomparatortest' => '/ObjectComparatorTest.php',
                'sebastianbergmann\\comparator\\resourcecomparatortest' => '/ResourceComparatorTest.php',
                'sebastianbergmann\\comparator\\sampleclass' => '/_files/SampleClass.php',
                'sebastianbergmann\\comparator\\scalarcomparatortest' => '/ScalarComparatorTest.php',
                'sebastianbergmann\\comparator\\splobjectstoragecomparatortest' => '/SplObjectStorageComparatorTest.php',
                'sebastianbergmann\\comparator\\struct' => '/_files/Struct.php',
                'sebastianbergmann\\comparator\\testclass' => '/_files/TestClass.php',
                'sebastianbergmann\\comparator\\testclasscomparator' => '/_files/TestClassComparator.php',
                'sebastianbergmann\\comparator\\typecomparatortest' => '/TypeComparatorTest.php'
            );
        }
        $cn = strtolower($class);
        if (isset($classes[$cn])) {
            require __DIR__ . $classes[$cn];
        }
    }
);
// @codeCoverageIgnoreEnd
