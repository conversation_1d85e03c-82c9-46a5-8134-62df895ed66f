<?php

/**
 * This is the model class for table "ivy_diglossia_category".
 *
 * The followings are the available columns in table 'ivy_diglossia_category':
 * @property integer $category_id
 * @property string $category_sign
 * @property string $category_entitle
 * @property string $category_cntitle
 */
class DiglossiaCategory extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return DiglossiaCategory the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_diglossia_category';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('category_sign, category_entitle, category_cntitle', 'length', 'max'=>150),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('category_id, category_sign, category_entitle, category_cntitle', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'diglossia'=>array(self::HAS_MANY, 'Term', 'category_id', 'order'=>'weight ASC'),
            'terms'=>array(self::HAS_MANY, 'Term', 'category_id', 'order'=>'weight ASC'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'category_id' => 'Category',
			'category_sign' => 'Category Sign',
			'category_entitle' => 'Category Entitle',
			'category_cntitle' => 'Category Cntitle',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('category_id',$this->category_id);
		$criteria->compare('category_sign',$this->category_sign,true);
		$criteria->compare('category_entitle',$this->category_entitle,true);
		$criteria->compare('category_cntitle',$this->category_cntitle,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}