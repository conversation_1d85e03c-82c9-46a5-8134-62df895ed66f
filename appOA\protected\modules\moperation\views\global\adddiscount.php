<?php $form=$this->beginWidget('CActiveForm', array(
    'id'=>'points-product-form',
    'enableAjaxValidation'=>false,
    'htmlOptions'=>array('class'=>'J_ajaxForm form-horizontal', 'role'=>'form'),
)); ?>
<div class="pop_cont">
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'category_id'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->dropDownList($model, 'category_id', CHtml::listData($items, 'id', CommonUtils::autoLang('title_cn', 'title_en')), array('class'=>'form-control'));?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'mailto'); ?></label>
        <div class="col-xs-9">
            <?php $this->widget('ext.search.StaffSearchBox', array(
                'acInputCSS' => 'form-control',
                'htmlOptions' => array('class'=>'form-control'),
                'data' => $data,
                'useModel' => true,
                'model' => $model,
                'attribute' => 'mailto',
                'allowMultiple' => false,
                'withAlumni' => false,
            )) ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'discount'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->textField($model, 'discount', array('class'=>'form-control'));?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'stat'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->checkBox($model, 'stat', array('value'=>10));?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'expire_date'); ?></label>
        <div class="col-xs-9">
            <?php
            $this->widget('zii.widgets.jui.CJuiDatePicker', array(
                "model"=>$model,
                "attribute"=>"expire_date",
                "options"=>array(
                    'changeMonth'=>true,
                    'changeYear'=>true,
                    'dateFormat'=>'yy-mm-dd',
                ),
                "htmlOptions"=>array(
                    'class'=>'form-control'
                )
            ));
            ?>
        </div>
    </div>
    <div class="pop_bottom">
        <button id="J_dialog_close" type="button" class="btn btn-default pull-right"><?php echo Yii::t('global','Cancel');?></button>
        <button type="submit" class="btn btn-primary J_ajax_submit_btn pull-right mr10"><?php echo Yii::t('global','Submit');?></button>
    </div>
</div>
<?php $this->endWidget(); ?>

<script>
    function callback(data)
    {
        parent.window.redis(data);
    }
</script>