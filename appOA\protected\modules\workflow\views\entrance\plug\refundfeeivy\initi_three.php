<!-- Modal -->
<?php 
    $labels = Invoice::model()->attributeLabels();
    $baseUrl = Yii::app()->params['OAUploadBaseUrl'] . '/RefundFee/';
    $policyApi = new PolicyApi();
?>
<?php echo CHtml::form('/workflow/entrance/index', 'post',array('class'=>'J_ajaxForm'));?>
<div class="modal fade" id="<?php echo $data['modalNameId']?>" tabindex="-1" role="dialog" aria-labelledby="workflowModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel"><?php echo CommonUtils::autoLang($data['workflow']->definationObj->defination_name_cn,$data['workflow']->definationObj->defination_name_en);?> - <?php echo CommonUtils::autoLang($data['workflow']->nodeObj->node_name_cn,$data['workflow']->nodeObj->node_name_en);?></h4>
            </div>
            <div class="modal-body" id="workflow-modal-body">
                <ul class="nav nav-wizard mb20">
                    <li style="width:33%;"><a>第一步：选择退费孩子</a></li>
                    <li style="width:33%;"><a>第二步：选择退费账单</a></li>
                    <li class="active" style="width:33%;"><a>第三步：确认退费金额</a></li>
                </ul>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <colgroup>
                            <col class="col-md-4">
                            <col class="col-md-2">
                            <col class="col-md-2">
                            <col class="col-md-2">               
                            <col class="col-md-2">
                            <col class="col-md-2">
                        </colgroup>
                        <thead>
                            <tr>
                                <td><?php echo $labels['title'];?></td>
                                <td><?php echo $labels['payment_type'];?></td>
                                <td><?php echo $labels['amount'];?></td>
                                <td><?php echo '预交学费'?></td>
                                <td><?php echo $labels['startdate'];?></td>
                                <td><?php echo $labels['enddate'];?></td>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($data['invoices'] as $invoice):
                                    $depositAmount = OA::formatMoney($invoice->original_amount - $invoice->amount);
                                    $exte2 = ($data['workflow']->getOperateValue('exte2')) ? $data['workflow']->getOperateValue('exte2') : $invoice->enddate;
                                ?>
                            <tr>
                                <td><?php echo $invoice->title;?></td>
                                <td><?php echo $policyApi->renderFeeType($invoice->payment_type);?></td>
                                <td><?php echo $invoice->amount;?></td>
                                <td><?php echo $depositAmount>0?$depositAmount:'0.00';?></td>
                                <td><?php echo OA::formatDateTime($invoice->startdate);?></td>
                                <td><?php echo OA::formatDateTime($invoice->enddate);?></td>
                            </tr>
                            <tr>
                                <td colspan="3">
                                    <div class="form-group" style="width: 250px;">
                                        <?php echo CHtml::textField("InvoiceChildRefund[".$invoice->invoice_id."][title]", '', array('placeholder'=>'退费标题','class'=>'form-control'));?>
                                    </div>
                                    <div class="form-group" style="width: 250px;">
                                        <?php echo CHtml::textField("InvoiceChildRefund[".$invoice->invoice_id."][amount]", $data['refundList'][$invoice->payment_type][$invoice->invoice_id], array('placeholder'=>'退费金额','class'=>'form-control'));?>
                                        <!-- <p class="bg-warning" style="padding:4px;">系统自动计算金额，可根据实际情况更改！</p> -->
                                    </div>
                                </td>
                                <td colspan="3">
                                    <div class="form-group" style="width: 250px;">
                                        <?php echo CHtml::textField("InvoiceChildRefund[".$invoice->invoice_id."][startdate]", OA::formatDateTime($data['workflow']->opertationObj->exte1), array('placeholder'=>'退费开始日期','class'=>'form-control'));?>
                                    </div>
                                    <div class="form-group" style="width: 250px;">
                                        <?php echo CHtml::textField("InvoiceChildRefund[".$invoice->invoice_id."][enddate]", OA::formatDateTime($exte2), array('placeholder'=>'退费结束日期','class'=>'form-control'));?>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach;?>
                        </tbody>
                    </table>
                </div>
                <!-- 显示附件 -->
                <div id="fileList">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>已上传资料</th>
                                <th>资料说明</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <?php foreach ($data['workflow']->getAttachmentList() as $uploadFile): ?>
                        <tr>
                            <td><a title="查看文件" target="_blank" href="<?php echo $uploadFile['bigimages']; ?>"><?php echo $uploadFile['notes']; ?></a></td>
                            <td><input onchange="changFileName(this,<?php echo $uploadFile['id']; ?>)" type="text" class="form-control" placeholder="输入资料说明(XX班级XX申请单)"></td>
                            <td><a class="btn btn-danger btn-xs" onclick="deleteFile(this,<?php echo $uploadFile['id']; ?>)"><span class="glyphicon glyphicon-trash"> </span></a></td>
                        </tr>
                        <?php endforeach; ?>
                    </table>
                    <p class="text-warning">退费需要上传：《请假申请单》、《退费/提现申请表》、《付款申请单》、《发票》</p>
                    <p class="text-warning">退学需要上传：《退学申请单》、《退费/提现申请表》、《付款申请单》、《发票》</p>
                </div>
                <!-- 上传 -->
                <div class="btn-group btn-group-justified" role="group">
                    <input id="file" type="file" name="file" style="display:none">
                    <div class="btn-group" role="group">
                        <a id="filebtn" class="btn btn-default" onclick="$('#file').click();">上传新资料</a>
                    </div>
                </div>
                <br>
                <span class="text-warning" id="spaninfo"></span>
            </div>
            <div class="modal-footer">
                <?php echo CHtml::hiddenField('definationId', $data['workflow']->getWorkflowId());?>
                <?php echo CHtml::hiddenField('nodeId', $data['workflow']->getCurrentNodeId());?>
                <?php echo CHtml::hiddenField('branchId', $data['workflow']->schoolId);?>
                <?php echo CHtml::hiddenField('operationId', $data['workflow']->getOperateId());?>
                <?php echo CHtml::hiddenField('step', 'three');?>
                <?php echo CHtml::hiddenField('buttonCategory', '');?>
                <?php echo CHtml::hiddenField('action', 'publicSave');?>
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('global','Cancel');?></button>
                <button type="button" class="btn btn-info J_ajax_submit_btn" data-category="previous"><?php echo Yii::t('workflow','Prev');?></button>
                <button type="button" class="btn btn-primary J_ajax_submit_btn" data-category="next"><?php echo Yii::t('global','Submit');?></button>
            </div>
        </div>
    </div>
</div>
<?php echo CHtml::endForm();?>
<script>
    //上传文件
    $(function(){
        $('#file').change(function(){
            var definationId = $('#definationId').val();
            var nodeId = $('#nodeId').val();
            var branchId = $('#branchId').val();
            var operationId = $('#operationId').val();
            var action = 'saveUploadFile';
            var url = '<?php echo Yii::app()->createUrl("/workflow/entrance/index")."?definationId=' +definationId+ '&nodeId=' +nodeId+ '&branchId=' +branchId+ '&operationId=' +operationId+ '&action=' +action+ '"; ?>';
            
            var formData = new FormData();
            formData.append('file', this.files[0]);
            $.ajax({
                url : url,
                type : 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success:function (data) {
                    var msg = eval('('+data+')');
                    if (msg.state == 'success') {
                        var html = '<tr><td><a target="_blank" href="' +msg.data.url+ '">' +msg.data.fileName+ '</a></td>';
                            html += '<td><input type="text" onchange=changFileName(this,' +msg.data.fileId+ ') class="form-control" placeholder="输入资料说明(XX班级XX申请单)"></td>'
                            html += '<td><a class="btn btn-danger btn-xs" onclick="deleteFile(this,' +msg.data.fileId+ ')"><span class="glyphicon glyphicon-trash"> </span></a></td></tr>';
                        $('#fileList > table').append(html);
                    };
                    $('#spaninfo').text(msg.message);
                }
            });
        });
    });
    //删除文件
    function deleteFile (btn,id) {
        if (confirm('确定要删除这个文件吗?')) {
            var definationId = $('#definationId').val();
            var nodeId = $('#nodeId').val();
            var branchId = $('#branchId').val();
            var operationId = $('#operationId').val();
            var action = 'deleteUploadFile';
            var url = '<?php echo Yii::app()->createUrl("/workflow/entrance/index")."?definationId=' +definationId+ '&nodeId=' +nodeId+ '&branchId=' +branchId+ '&operationId=' +operationId+ '&action=' +action+ '"; ?>';
            
            $.ajax({
                url : url,
                type : 'POST',
                data: {id : id},
                success:function (data) {
                    var msg = eval('('+data+')');
                    if (msg.state == 'success') {
                        $(btn).parent().parent().remove();
                    };
                    $('#spaninfo').text(msg.message);
                }    
            });
        };
    }
    //修改文件名
    function changFileName (btn,id) {
        var notes = $(btn).val();
        if (notes=="") {return false};
        var definationId = $('#definationId').val();
        var nodeId = $('#nodeId').val();
        var branchId = $('#branchId').val();
        var operationId = $('#operationId').val();
        var action = 'changeFileName';
        var url = '<?php echo Yii::app()->createUrl("/workflow/entrance/index")."?definationId=' +definationId+ '&nodeId=' +nodeId+ '&branchId=' +branchId+ '&operationId=' +operationId+ '&action=' +action+ '"; ?>';
        
        $.ajax({
            url : url,
            type : 'POST',
            data: {id : id,notes : notes},
            success:function (data) {
                var msg = eval('('+data+')');
                if (msg.state == 'success') {
                    var con = $(btn).parent().prev().children().text(notes);
                }
                $('#spaninfo').text(msg.message);
            }    
        });
    }

    var invoices = <?php echo CJSON::encode($data['invoices']);?>;
    _.each(invoices,function(val,key){
        $("#InvoiceChildRefund_"+val.invoice_id+"_startdate").datepicker({'dateFormat':'yy-mm-dd'});
        $("#InvoiceChildRefund_"+val.invoice_id+"_enddate").datepicker({'dateFormat':'yy-mm-dd'});
    })
</script>