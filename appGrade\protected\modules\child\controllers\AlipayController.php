<?php

class AlipayController extends ProtectedController {

    public function init() {
        parent::init();
        Yii::import('common.models.invoice.*');
        Yii::import('common.models.alipay.*');
    }

    public function actionOnlineBankInfo() {

        $alipayAvailable = false;
        $alipayBankInfo = Mims::LoadConfig('CfgAlipayBankInfo');
        $alipayPartnerInfo = Mims::LoadConfig('CfgAlipayPartnerInfo');
        if (isset(Yii::app()->params['onlineBanking']) && 'production' == Yii::app()->params['onlineBanking']) {
            $schoolid = $this->getSchoolId();
        } else {
            $schoolid = 'test';
        }
        if (!empty($alipayPartnerInfo[$schoolid]['partner'])) {
            $alipayAvailable = true;
        }
        $alipayBankList = array_keys($alipayBankInfo);
        $usedBank = array();
        $otherBankList = array();
        if (true == $alipayAvailable) {
            $ph_cri = new CDbCriteria();
            $ph_cri->compare('childid', $this->getChildId());
            $ph_cri->order = 'hot_value DESC';
            $ph_cri->limit = 3;
            $ph_cri->index = 'bankid';
            $habits = AlipayPayHabit::model()->findAll($ph_cri);

            if (empty($habits)) {
                $habitBank = array();
            } else {
                $habitBank = array_keys($habits);
            }

            foreach ($alipayBankList as $bankid) {
                if (in_array($bankid, $habitBank)) {
                    $usedBank[$bankid] = '';
                } else {
                    $otherBankList[$bankid] = '';
                }
            }
        }
        $this->renderPartial('/alipay/onlineBankInfo', array(
            'alipayAvailable' => $alipayAvailable,
            'usedBank' => $usedBank,
            'otherBankList' => $otherBankList
                )
        );
    }

    public function actionOnlineNotice() {

        $alipayBankInfo = Mims::LoadConfig('CfgAlipayBankInfo');

//        Yii::app()->clientScript->registerCssFile(Yii::app()->theme->baseUrl . '/css/notice.css?t=' . Yii::app()->params['refreshAssets']);
        // 输出 变量到 视图文件
        $view_data = array(
            'childId' => $this->getChildId(),
            'langKey' => Yii::app()->language == 'en_us' ? 'en' : 'cn',
            'alipayBankInfo' => $alipayBankInfo,
        );
        $this->renderPartial('/alipay/onlineNotice', $view_data);
    }

}
