<?php

/**
 * This is the model class for table "ivy_invoice_transaction".
 *
 * The followings are the available columns in table 'ivy_invoice_transaction':
 * @property integer $id
 * @property integer $invoice_id
 * @property integer $calendar_id
 * @property double $amount
 * @property string $inout
 * @property integer $timestampe
 * @property integer $childid
 * @property integer $classid
 * @property string $schoolid
 * @property string $payment_type
 * @property integer $afterschool_id
 * @property string $title
 * @property integer $startdate
 * @property integer $enddate
 * @property string $installment
 * @property string $invoice_number
 * @property integer $fee_type
 * @property integer $operator_uid
 * @property integer $agreeddate
 * @property string $memo
 * @property integer $transactiontype
 * @property integer $transfer_to_uid
 * @property integer $transfer_timestamp
 * @property integer $receipt_uid
 * @property integer $receipt_timestamp
 * @property integer $fee_other_id
 * @property integer $ufida_income
 */
class InvoiceTransaction extends CActiveRecord
{
	/*
	 * 暂时为 打印账单准备的
	 */
	// 付款方式的名称  现金 使用个人账户 pos机 在线支付等等...
	public $transactionTypeTitle = null;
	public $paidTime = null;
	
    const TYPE_CASH = 10;            	//现金
    const TYPE_TRANSFER = 20;           //银行转帐
    const TYPE_CHECK = 30;              //支票
    const TYPE_USERCREDIT = 40;         //使用个人账户
    const TYPE_TOCREDIT = 50;           //转到个人账户
    const TYPE_BANKCARD = 60;           //非易宝POS
    const TYPE_POS = 70;              	//易宝POS
    const TYPE_POS_ALLINPAY = 71;       //通联POS
    const TYPE_ONLINEPAYMENT = 80;      //阿里在线支付
    const TYPE_ONLINE_ALLINPAY = 81;    //通联在线支付
    const TYPE_WX_MICROPAY = 85;        //微信刷卡支付
    const TYPE_WX_NATIVE = 86;          //微信扫码支付
    const TYPE_VOUCHER = 90;            //代金券
    const TYPE_EDUFEE = 91;           	//学前保教费减免

	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return WInvoiceTransaction the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_invoice_transaction';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('invoice_id, calendar_id, childid, schoolid, payment_type, operator_uid, transactiontype', 'required'),
			array('invoice_id, calendar_id, timestampe, childid, classid, afterschool_id, startdate, enddate, fee_type, operator_uid, agreeddate, transactiontype, transfer_to_uid, transfer_timestamp, receipt_uid, receipt_timestamp, fee_other_id, ufida_income', 'numerical', 'integerOnly'=>true),
			array('amount', 'numerical'),
			array('inout', 'length', 'max'=>3),
			array('schoolid, payment_type, title', 'length', 'max'=>255),
			array('installment', 'length', 'max'=>100),
			array('invoice_number, memo', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, invoice_id, calendar_id, amount, inout, timestampe, childid, classid, schoolid, payment_type, afterschool_id, title, startdate, enddate, installment, invoice_number, fee_type, operator_uid, agreeddate, memo, transactiontype, transfer_to_uid, transfer_timestamp, receipt_uid, receipt_timestamp, fee_other_id, ufida_income', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'invoiceInfo'=>array(self::BELONGS_TO, 'Invoice','invoice_id'),
            'classInfo'=> array(self::BELONGS_TO, 'IvyClass','classid'),
            'childInfo' => array(self::BELONGS_TO, 'ChildProfileBasic', 'childid'),
            'branchInfo' => array(self::BELONGS_TO, 'Branch', 'schoolid'),
            'userInfo' => array(self::BELONGS_TO, 'User', 'operator_uid'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'invoice_id' => 'Invoice',
			'calendar_id' => 'Calendar',
			'amount' => 'Amount',
			'inout' => 'Inout',
			'timestampe' => Yii::t("labels",'Paid Date'),
			'childid' => 'Childid',
			'classid' => 'Classid',
			'schoolid' => 'Schoolid',
			'payment_type' => 'Payment Type',
			'afterschool_id' => 'Afterschool',
			'title' => Yii::t("labels",'Invoice Title'),
			'startdate' => 'Startdate',
			'enddate' => 'Enddate',
			'installment' => 'Installment',
			'invoice_number' => Yii::t("payment",'Fapiao No.'),
			'fee_type' => 'Fee Type',
			'operator_uid' => 'Operator Uid',
			'agreeddate' => 'Agreeddate',
			'memo' => 'Memo',
			'transactiontype' => 'Transaction Type',
			'transfer_to_uid' => 'Transfer To Uid',
			'transfer_timestamp' => 'Transfer Timestamp',
			'receipt_uid' => 'Receipt Uid',
			'receipt_timestamp' => 'Receipt Timestamp',
			'fee_other_id' => 'Fee Other',
			'ufida_income' => 'Ufida Income',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('invoice_id',$this->invoice_id);
		$criteria->compare('calendar_id',$this->calendar_id);
		$criteria->compare('amount',$this->amount);
		$criteria->compare('inout',$this->inout,true);
		$criteria->compare('timestampe',$this->timestampe);
		$criteria->compare('childid',$this->childid);
		$criteria->compare('classid',$this->classid);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('payment_type',$this->payment_type,true);
		$criteria->compare('afterschool_id',$this->afterschool_id);
		$criteria->compare('title',$this->title,true);
		$criteria->compare('startdate',$this->startdate);
		$criteria->compare('enddate',$this->enddate);
		$criteria->compare('installment',$this->installment,true);
		$criteria->compare('invoice_number',$this->invoice_number,true);
		$criteria->compare('fee_type',$this->fee_type);
		$criteria->compare('operator_uid',$this->operator_uid);
		$criteria->compare('agreeddate',$this->agreeddate);
		$criteria->compare('memo',$this->memo,true);
		$criteria->compare('transactiontype',$this->transactiontype);
		$criteria->compare('transfer_to_uid',$this->transfer_to_uid);
		$criteria->compare('transfer_timestamp',$this->transfer_timestamp);
		$criteria->compare('receipt_uid',$this->receipt_uid);
		$criteria->compare('receipt_timestamp',$this->receipt_timestamp);
		$criteria->compare('fee_other_id',$this->fee_other_id);
		$criteria->compare('ufida_income',$this->ufida_income);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
	
	/**
	 * 统计出某个孩子当前在线支付的积分
	 * @param int $childid
	 * <AUTHOR>
	 * @copyright ivygroup
	 * @return Number
	 */
	public function getPointsCredit($childid,$timestamp)
	{
		
		$credits = $this->getTotalPoints($childid, $timestamp);
		if ($credits)
		{
			$usedCredit = PointsOrder::model()->getUsedCredits($childid);
			$credits = $credits-$usedCredit;
		}
		return floor($credits);
	}
	
	/**
	 * 统计某个孩子累积的积分
	 * @param int $childid
	 * @param int $timestamp
	 * @return number
	 * <AUTHOR> 
	 */
	public function getTotalPoints($childid,$timestamp)
	{
		// 积分计算截止到20200615
		$endtimestamp = strtotime(20200701);
		$pointsObj = $this->findAllBySql('select sum(amount) as amount from '.$this->tableName().' where transactiontype in (80,81,85,86) and childid=:childid and timestampe>:timestampe and timestampe<:endtimestamp', array(':childid'=>$childid,':timestampe'=>$timestamp, ':endtimestamp'=>$endtimestamp));
		return (!empty($pointsObj[0]->amount)) ? $pointsObj[0]->amount : 0;
	}
	
	/**
	 * 获取截至到某段时间孩子在线支付的明细
	 * @param int $childid
	 * @param int $timestamp
	 * @return ArrayObject;
	 * <AUTHOR>
	 */
	public function getTotalPointsDetail($childid,$timestamp)
	{
		$criteria = new CDbCriteria;
		$criteria->select = 'title,amount,timestampe';
		$criteria->compare('transactiontype', 80);
		$criteria->compare('childid', $childid);
		$criteria->addCondition('timestampe>'.$timestamp);
		$criteria->order = 'timestampe Desc';
		return $this->findAll($criteria);
	}

	public function getPaidSum($invoiceId)
	{
		$res = $this->findAllBySql('select sum(amount) as amount from '.$this->tableName()." where invoice_id =:invoiceId and `inout`='in' ", array(':invoiceId'=>$invoiceId));
		return (!empty($res[0]->amount)) ? $res[0]->amount : 0;
	}
}