<style>
	.borderBto{
		border-bottom: 1px solid #DDDDDD;
	}
	.passBody{
		padding:0 ;
	}
</style>
<div class="container-fluid">
	<ol class="breadcrumb">
		<li><?php echo CHtml::link(Yii::t('site','Campus Operations'), array('//mcampus/default/index'))?></li>
		<li><?php echo CHtml::link(Yii::t('site','Advanced'), array('//mcampus/default/index'))?></li>
		<li class="active"><?php echo Yii::t('site','成长记录统计');?></li>
	</ol>
	<div class="row mb15">
		 <?php $form=$this->beginWidget('CActiveForm', array(
            'id'=>'courseGroup-form',
            'enableAjaxValidation'=>false,
            'action'=> $this->createUrl('index'),
            'htmlOptions'=>array('class'=>'J_ajaxForm form-horizontal', 'role'=>'form'),
        )); ?>
		<div class="col-md-2">
			<?php echo Chtml::dropDownList('yid', $yid, $schoolList,array('class'=>'form-control')); ?>
		</div>
		<div class="col-md-2">
		<?php echo Chtml::dropDownList('semester', $semester, array(1 => '第一学期' , 2 => '第二学期'),array('class'=>'form-control')); ?>
		</div>
		<div class="col-md-2">
		<?php echo Chtml::dropDownList('classArr', $classArr, $classList, array('class'=>'form-control')); ?>
		</div>
		<button type="submit" class="btn btn-primary" >确认</button>
		<?php $this->endWidget(); ?>
	</div>
	<div class="row">
	    <div class="col-md-12 nodata"></div>
	    <div class="col-md-12 report" id='report'></div>
		<div id='chart' class="col-md-12"></div>		
	</div>
	<div class="clearfix"></div>
</div>
<script id="item-templateall" type="text/template">
	<div class="panel panel-default">
	    <div class="panel-heading">
            <h3 class="panel-title">成长记录统计</h3>
	    </div>
	    <div class="panel-body passBody">
		    <div class="col-md-12 col-lg-12 col-xs-12 col-sm-12 borderBto p10">
		  		<div id='total' class="col-md-12 col-lg-6 col-xs-12 col-sm-12" style="height:350px;"></div>
			  	<div id='total1' class="col-md-12 col-lg-6 col-xs-12 col-sm-12 mt15">   
			  		<table class="table table-bordered">
				  		<thead>
				  			<tr>
				  				<th>领域</th>
				  				<th>班级平均水平</th>
				  			</tr>
				  		</thead>
			  			<tbody id='total1Table'></tbody>
			  		</table>
			  	</div>
		  	</div>
		  	<div  class="col-md-12 col-lg-12 col-xs-12 col-sm-12 mt15 p10">
		  	    <div id="allNum" class="col-md-12 col-lg-6 col-xs-12 col-sm-12" style="height:350px;"></div>
			  	<div class="col-md-12 col-lg-6 col-xs-12 col-sm-12">
			  	 	<h5 id='allPop'></h5>
			  	 	<table class="table table-bordered">
				  	 	<thead>
				  	 		<tr>
				  				<th>领域</th>
				  				<th>1星:未显示</th>
				  				<th>2星:发展中</th>
				  				<th>3星:熟练</th>
				  			</tr>
				  	 	</thead>
			  			<tbody class="allNumTab"></tbody>
			  		</table>
			  	</div>
		  	</div>
	    </div>
	</div>
</script>
<script id="item-template" type="text/template">
	<div class="panel panel-default">
	    <div class="panel-heading">
            <h3 class="panel-title" id="title<%= key%>"></h3>
	    </div>
	    <div class="panel-body passBody">
		    <div class="col-md-12 col-lg-12 col-xs-12 col-sm-12 borderBto p10">
		  		<div id="main<%= key%>" class='col-md-12 col-lg-6 col-xs-12 col-sm-12' style="height:350px;"></div>
			  	<div id='table<%= key%>' class='col-md-12 col-lg-6 col-xs-12 col-sm-12 mt15' >
		            <table class="table table-bordered">
			            <thead>
			            	<tr>
			             		<th id='thead<%= key%>'>领域</th>
			             		<th>班级平均水平</th>
			             	</tr>
			            </thead>
			            <tbody  id='tbody<%= key%>'></tbody> 	
		            </table>
			  	</div>
		  	</div>
		  	<div class="col-md-12 col-lg-12 col-xs-12 col-sm-12 mt15 p10">
		  		<div id="maintot<%= key%>" class='col-md-12 col-lg-6 col-xs-12 col-sm-12' style="height:350px;"></div>
			  	<div id='tabletot<%= key%>' class="col-md-12 col-lg-6 col-xs-12 col-sm-12">
			  		<h5 id='classNum<%= key%>'></h5>
		            <table class="table table-bordered">
			            <thead>
			            	<tr>
			             		<th id='tabletoThead<%= key%>'>领域</th>
		             			<th>1星:未显示</th>
				  				<th>2星:发展中</th>
				  				<th>3星:熟练</th>
			             	</tr>
			            </thead>
			            <tbody  id='tabletoTbody<%= key%>'></tbody> 
		            </table>
			  	</div>
		  	</div>
	   </div>
	</div>
</script>
<script>
    var dataArr = <?php echo json_encode($dataArr) ?>;
    var classArr = '<?php echo $classtype ?>';
   $("#yid").change(function(){
	    $.ajax({
		    type: "post",
		    url:"<?php echo $this->createUrl('Class')?>",
		    data: {yid:$(this).val()},
		    dataType: "json",
		    success: function(data){
                var html = '<option value="all" selected="selected">全园</option>'; 
                $.each(data, function(val, index){
                   html += '<option value='+val+'>'+index+'</option>';
                });
                $('#classArr').html(html);
            },error:function(data){
                alert('请求错误')
            }
        });
	});
    //各个领域报告
	var option1 = {
		width: 'auto',
		height: 'auto',
		tooltip: {
			trigger: 'axis',
			confine: true,
			axisPointer: { // 坐标轴指示器，坐标轴触发有效
				type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
			}
		},
		legend: {
			data: ''
		},
		grid: {
			left: '0%',
			right: '0%',
			bottom: '1%',
			containLabel: true
		},
		xAxis: [{
			type: 'category',
			data: '',
			boundaryGap:true,
			axisLabel: {
				interval: 0,
				formatter: function(value) {
					var ret = "";
					var maxLength = 10;
					var valLength = value.length;
					var rowN = Math.ceil(valLength / maxLength);
					if(rowN > 1) {
						for(var i = 0; i < rowN; i++) {
							var temp = "";
							var start = i * maxLength;
							var end = start + maxLength;
							temp = value.substring(start, end) + "\n";
							ret += temp;
						}
						return ret;
					} else {
						return value;
					}
				}
			}
		}],
		yAxis: [{
			name: '',
			type: 'value',
			min: 0,
			max: 3,
		}],
		series: [{
			name: '',
			type: 'bar',
			barWidth: 50,
			data: '',
	        itemStyle: {
				normal: {
					label: {
						show: true, //开启显示
						position: 'top', //在上方显示
						textStyle: { //数值样式
							//color: 'black',
							fontSize: 14
						}
					}
				}
			},
		}]
	};
	//各个领域人数
	var option3 = {
		width: 'auto',
		height: 'auto',
		tooltip: {
			trigger: 'axis',
            confine: true,
			axisPointer: { // 坐标轴指示器，坐标轴触发有效
				type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
			}
		},
		legend: {
			data:['1星:未显示','2星:发展中','3星:熟练']
		},
		grid: {
			left: '0%',
			right: '0%',
			bottom: '1%',
			containLabel: true
		},
		xAxis: [{
			type: 'category',
			data: '',
			boundaryGap:true,
			axisLabel: {
				interval: 0,
				formatter: function(value) {
					var ret = "";
					var maxLength = 10;
					var valLength = value.length;
					var rowN = Math.ceil(valLength / maxLength);
					if(rowN > 1) {
						for(var i = 0; i < rowN; i++) {
							var temp = "";
							var start = i * maxLength;
							var end = start + maxLength;
							temp = value.substring(start, end) + "\n";
							ret += temp;
						}
						return ret;
					} else {
						return value;
					}
				}
			}
		}],
		yAxis: [{
			type: 'value',
			name:''
		}],
		series: [
        {
            name: '1星:未显示',
            type: 'bar',
            stack: 'total',
            barWidth:50,
            label: {
                normal: {
                    show: true,
                    position: 'inside',
                }
            },
            data:''
        },
        {
            name: '2星:发展中',
            type: 'bar',
            stack: 'total',
            barWidth:50,
            label: {
                normal: {
                    show: true,
                    position: 'inside'
                }
            },
            data:''
        }, {
            name: '3星:熟练',
            type: 'bar',
            stack: 'total',
            barWidth:50,
            label: {
                normal: {
                    show: true,
                    position: 'inside'
                }
            },
            data:''
        }
        ],
	};
	//全园成长报告
	var option = {
		width: 'auto',
		height: 'auto',
		tooltip: {
			trigger: 'axis',
            confine: true,
			axisPointer: { // 坐标轴指示器，坐标轴触发有效
				type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
			}
		},
		legend: {
			data:['领域汇总']
		},
		grid: {
			left: '0%',
			right: '0%',
			bottom: '1%',
			containLabel: true
		},
		xAxis: [{
			type: 'category',
			data: '',
			boundaryGap:true,
			axisLabel: { //坐标轴刻度标签的相关设置。
				interval: 0,
				formatter: function(value) {
					var ret = "";
					var maxLength = 14;
					var valLength = value.length;
					var rowN = Math.ceil(valLength / maxLength);
					if(rowN > 1) {
						for(var i = 0; i < rowN; i++) {
							var temp = "";
							var start = i * maxLength;
							var end = start + maxLength;
							temp = value.substring(start, end) + "\n";
							ret += temp;
						}
						return ret;
					} else {
						return value;
					}
				}
			}
		}],
		yAxis: [{
			type: 'value',
			min: 0,
			max: 3,
			name:''
		}],
		series: [{
			name: '领域汇总',
			type: 'bar',
			barWidth:50,
			data: '',
			itemStyle: {
				normal: {
					label: {
						show: true, //开启显示
						position: 'top', //在上方显示
						textStyle: { //数值样式
							fontSize: 14
						}
					}
				}
			},
		}],
	};
 //全园人数
	var option2 = {
		width: 'auto',
		height: 'auto',
		tooltip: {
			trigger: 'axis',
            confine: true,
			axisPointer: { // 坐标轴指示器，坐标轴触发有效
				type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
				value:'20',
			},
		},
		legend: {
			data:['1星:未显示','2星:发展中','3星:熟练']
		},
		grid: {
			left: '0%',
			right: '0%',
			bottom: '1%',
			containLabel: true
		},
		xAxis: [{
			type: 'category',
			data: '',
			boundaryGap:true,
			axisLabel: { //坐标轴刻度标签的相关设置。
				interval: 0,
				formatter: function(value) {
					var ret = "";
					var maxLength = 14;
					var valLength = value.length;
					var rowN = Math.ceil(valLength / maxLength);
					if(rowN > 1) {
						for(var i = 0; i < rowN; i++) {
							var temp = "";
							var start = i * maxLength;
							var end = start + maxLength;
							temp = value.substring(start, end) + "\n";
							ret += temp;
						}
						return ret;
					} else {
						return value;
					}
				}
			}
		}],
		yAxis: [{
			type: 'value',
			name:'',
		}],
		series: [
        {
            name: '1星:未显示',
            type: 'bar',
            stack: 'total',
            barWidth:50,
            label: {
                normal: {
                    show: true,
                    position: 'inside',
                }
            },
            data:''
        },
        {
            name: '2星:发展中',
            type: 'bar',
            stack: 'total',
            barWidth:50,
            label: {
                normal: {
                    show: true,
                    position: 'inside',
                }
            },
            data:''
        }, {
            name: '3星:熟练',
            type: 'bar',
            stack: 'total',
            barWidth:50,
            label: {
                normal: {
                    show: true,
                    position: 'inside',
                }
            },
            data:''
        }
        ],
	};
	var all = _.template($('#item-templateall').html());
				$('#report').append(all);
	var color = ['#293C55', '#61A0A8', '#006A60', '#BB925D', '#E28492', '#457BA9', '#006A60', '#61A0A8']
	chartList = []
	chartsList=[]
	//全园
	if(classArr=='all'){
		if(dataArr.datas.length!=''){
			var alldata1=[]
            var alldata2=[]
            var alldata3=[]
            //全园报告
			myChart = echarts.init(document.getElementById('total'));
			option.xAxis[0].data = dataArr.xAxis
			option.series[0].data = dataArr.datas
			myChart.setOption(option);
            //全园人数
             for(key in dataArr.proportion){
              alldata1.push(dataArr.proportion[key][1]);
              alldata2.push(dataArr.proportion[key][2]);
              alldata3.push(dataArr.proportion[key][3]); 		
	    	}
			option2.xAxis[0].data = dataArr.xAxis
			option2.series[0].data =alldata1
			option2.series[1].data =alldata2
			option2.series[2].data = alldata3
			$('#allPop').html('总人数:'+dataArr.totle)
			//全园人数计算百分比
			for(var i=0;i<3;i++){
				option2.series[i].label.normal.formatter = function (params) {
		            if (params.value > 0) {
		               return params.value +"人"+"\n"+(Math.round(params.value / dataArr.totle * 10000) / 100.00 + "%");
		            } else {
		                return '';
		            }
            	};
			}
            option2.tooltip.formatter = function (params) {
               var relVal = params[0].name+"<br/>";
	           for(var i=0;i<params.length;i++){
                	relVal += params[i].seriesName+':'+params[i].value +"人"+"\n"+ (Math.round(params[i].value / dataArr.totle * 10000) / 100.00 + "%")+"<br/>";
                }
	            return relVal;
            };
		    myCharts = echarts.init(document.getElementById('allNum'));
			myCharts.setOption(option2);
			//表格
			for(var i=0;i< dataArr.xAxis.length;i++){
				$('#total1Table' ).append('<tr><td>'+dataArr.xAxis[i]+'</td><td>'+dataArr.datas[i]+'</td></tr>')
				$('.allNumTab' ).append('<tr><td>'+dataArr.xAxis[i]+'</td><td>'+alldata1[i]+'</td><td>'+alldata2[i]+'</td><td>'+alldata3[i]+'</td></tr>')    	
			}	
		}else{
			$('.nodata').html('<div class="alert alert-warning" role="alert"><a href="#" class="alert-link">暂无数据</a></div>')
			$('.report').hide()
		}
    }else {
    	//托班
		if(dataArr.allData.length!=''){
			var alldata=[];
		    var allxAxis=[];
		    var allNumData1=[];
		    var allNumData2=[];
		    var allNumData3=[];
		    c=0;
    	    for(key in dataArr.dataArr.bAxis){
				alldata.push(dataArr.allData[key]);
				allxAxis.push(dataArr.dataArr.bAxis[key]);
				allNumData1.push(dataArr.proportion[key][1]);
				allNumData2.push(dataArr.proportion[key][2]);
				allNumData3.push(dataArr.proportion[key][3]);
				$('#total1Table' ).append('<tr><td>'+dataArr.dataArr.bAxis[key]+'</td><td>'+dataArr.allData[key]+'</td></tr>');
				$('.allNumTab' ).append('<tr><td>'+dataArr.dataArr.bAxis[key]+'</td><td>'+dataArr.proportion[key][1]+'</td><td>'+dataArr.proportion[key][2]+'</td><td>'+dataArr.proportion[key][3]+'</td></tr>');    		
	    	}
	    	//托班报告
	    	myChart = echarts.init(document.getElementById('total'));
			option.xAxis[0].data =allxAxis;
			option.series[0].data =alldata;
			myChart.setOption(option);
            //托班人数
			option2.xAxis[0].data = allxAxis;
			option2.series[0].data = allNumData1;
			option2.series[1].data = allNumData2;
			option2.series[2].data = allNumData3;
			$('#allPop').html('总人数:'+dataArr.totle)
			for(var i=0;i<3;i++){
				option2.series[i].label.normal.formatter = function (params) {
					if (params.value > 0) {
		               return params.value +"人"+"\n"+(Math.round(params.value / dataArr.totle * 10000) / 100.00 + "%");
		            } else {
		                return '';
		            }
	            };
			}
            option2.tooltip.formatter = function (params) {
                var relVal = params[0].name+"<br/>";
	           for(var i=0;i<params.length;i++){
                	relVal += params[i].seriesName+':'+params[i].value +"人"+"\n"+ (Math.round(params[i].value / dataArr.totle * 10000) / 100.00 + "%")+"<br/>";
                }
	            return relVal;
            };
		    myCharts = echarts.init(document.getElementById('allNum'));
			myCharts.setOption(option2);
			if(classArr!='n'){
                   for(key in dataArr.dataArr.mAxis){
				var item = _.template($('#item-template').html(),key);
				$('#chart').append(item);
				var id = 'main' + key;
				var myChart1 = echarts.init(document.getElementById(id));
				var id = 'maintot' + key;
				var myCharts1 = echarts.init(document.getElementById(id));
				var xAxisdata=[];
				var seriesdata=[];
				var classData1=[];
				var classData2=[];
				var classData3=[];
				$('#classNum'+key).html('班级人数:'+dataArr.totle)
				$('#title' + key).html(dataArr.dataArr.mAxis[key]);
				$('#tabletoThead' + key ).html(dataArr.dataArr.mAxis[key]);
				$('#thead' + key ).html(dataArr.dataArr.mAxis[key]);
				option1.series[0].name =dataArr.dataArr.mAxis[key];
				option1.series[0].itemStyle.normal.color = color[c];
				c++;
				chartList[key] = myChart1;
				chartsList[key]=myCharts1;
	           	for(idx in dataArr.dataArr.sAxis[key]){
		           	xAxisdata.push(dataArr.dataArr.sAxis[key][idx]);
		           	seriesdata.push(dataArr.singleClassData[idx]);
		            classData1.push(dataArr.proportionX[idx][1]);
		            classData2.push(dataArr.proportionX[idx][2]);
		            classData3.push(dataArr.proportionX[idx][3]);
                    $('#tbody' + key ).append('<tr><td>'+dataArr.dataArr.sAxis[key][idx]+'</td><td>'+dataArr.singleClassData[idx]+'</td></tr>');
	           		$('#tabletoTbody' + key ).append('<tr><td>'+dataArr.dataArr.sAxis[key][idx]+'</td><td>'+dataArr.proportionX[idx][1]+'</td><td>'+dataArr.proportionX[idx][2]+'</td><td>'+dataArr.proportionX[idx][3]+'</td></tr>');
	           	}
	           	//各个领域报告

                option1.xAxis[0].data =xAxisdata;
                option1.series[0].data=seriesdata;
                myChart1.setOption(option1);
                //各个领域人数
                option3.xAxis[0].data = xAxisdata;
                option3.series[0].data = classData1;
				option3.series[1].data = classData2;
				option3.series[2].data = classData3;
				//计算百分比
				for(var i=0;i<3;i++){
					option3.series[i].label.normal.formatter = function (params) {
						if (params.value > 0) {
			               return params.value +"人"+"\n"+ (Math.round(params.value / dataArr.totle * 10000) / 100.00 + "%");
			            } else {
			                return '';
			            }
		            };
				}
	            option3.tooltip.formatter = function (params) {
	                var relVal = params[0].name+"<br/>";
		           for(var i=0;i<params.length;i++){
	                	relVal += params[i].seriesName+':'+params[i].value +"人"+"\n"+ (Math.round(params[i].value / dataArr.totle * 10000) / 100.00 + "%")+"<br/>";
	                }
		            return relVal;
	            };
                myCharts1.setOption(option3);              
            }
			}
		}else{
			$('.nodata').html('<div class="alert alert-warning" role="alert"><a href="#" class="alert-link">暂无数据</a></div>');
			$('.report').hide();
		}
    }
	window.onresize = function() {
		myChart.resize();
		myCharts.resize();
		for(key in chartList) {
			chartList[key].resize();
			chartsList[key].resize();
		}
	}
</script>
<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>