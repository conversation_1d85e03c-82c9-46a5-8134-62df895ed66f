<style>
    .switch {
        position: relative;
        display: inline-block;
    }

    .switch input {
        display: none;
    }

    .slider {
        margin: 0;
        display: inline-block;
        position: relative;
        width: 40px;
        height: 20px;
        border: 1px solid #dcdfe6;
        outline: none;
        border-radius: 10px;
        box-sizing: border-box;
        background: #dcdfe6;
        cursor: pointer;
        transition: border-color .31s, background-color .3s;
        vertical-align: middle;
    }

    .slider:before {
        content: "";
        position: absolute;
        top: 1px;
        left: 1px;
        border-radius: 50%;
        transition: all .3s;
        width: 16px;
        height: 16px;
        background-color: #fff;
    }

    input:checked+.slider {
        background-color: #2196F3;
    }

    input:checked+.slider:before {
        left: 100%;
        margin-left: -17px;
    }
</style>

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <h1 class="page-header">
                <span class="glyphicon glyphicon-cog"></span> 签署人配置
            </h1>
        </div>
    </div>
    <div class="row">

        <div class="col-md-2">
            <?php
            if($model->staff){
                echo $user[$model->staff]->getName();
            }
            $this->widget('ext.search.StaffSearchBox', array(
                'acInputCSS' => 'form-control',
                'acName' => 'searchStaff_staff',
                'htmlOptions' => array('class'=>'form-control'),
                'data' => array(),
                'name' => 'uid',
                'useModel' => true,
                'model' => $model,
                'attribute' => 'staff',
                'allowMultiple' => false,
                'withAlumni' => false,
            ))
            ?>
            <br>
            <button class="btn btn-primary mb5" onclick="addUser(this)">新增</button>
        </div>

        <div class="col-md-12">
            <h3><span class="glyphicon glyphicon-list"></span>配置列表</h3>
            <table class="table reportTbody">
                <thead>

                </thead>
                <tbody class="reportTbody">
                <tr>
                    <td>ID</td>
                    <td>Name</td>
                    <td>职位</td>
                    <td>操作</td>
                </tr>
                <?php foreach ($data as $item){?>
                    <tr>
                        <td width="150"><?php echo $item['uid']?></td>
                        <td width="200"><?php echo $item['name']?></td>
                        <td width="200"><?php echo $item['hrPosition']?></td>
                        <td width="150">
                            <button type="button" class="btn btn-primary btn-xs"  onclick='del(<?php echo $item['uid']?>)'> 删除</button>
                        </td>
                    </tr>
                <?php }?>

                </tbody>
            </table>

        </div>
    </div>
</div>

<script>
    function del(user_id) {
        if (confirm("确定删除吗？")) {
            $.ajax({
                url: '<?php echo $this->createUrl('delNoticeSignatory'); ?>',
                data: {"user_id": user_id},
                type: 'POST',
                dataType: 'json',
                success: function(data) {
                    if (data.state === 'fail') {
                        head.dialog.alert(data.message);
                        return false;
                    }else{
                        resultTip({msg: data.message});
                        location.reload();
                    }
                },
                error: function() {
                    alert("访问异常！");
                }
            });
        }
    }
    function addUser(btn) {
        $(btn).attr('disabled', 'disabled');
        var uid = $('#User_staff').val();
        if (!uid) {
            resultTip({
                'error': 'warning',
                'msg': '请先选择一个用户'
            })
            $(btn).removeAttr('disabled');
            return false;
        }
        var url = '<?php echo $this->createUrl('addNoticeSignatory'); ?>';
        var params = {
            'user_id': uid,
        }
        $.post(url, params, function(data) {
            if (data.state === 'success') {
                resultTip({
                    msg: data.message
                });
                location.reload();
            } else if (data.state === 'fail') {
                head.dialog.alert(data.message);
            }
            $(btn).removeAttr('disabled');
        }, 'json');
    }
</script>
<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>

