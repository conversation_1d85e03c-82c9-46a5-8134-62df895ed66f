<div class="background-gray p8">
    <button class="btn btn-primary" type="button" title="添加账户" onclick="addAccount(0)"><span class="glyphicon glyphicon-plus"></span> 添加账户</button>
</div>
<?php
$this->widget('ext.ivyCGridView.BsCGridView', array(
    'id'=>'account-grid',
    'dataProvider'=>$model->search(),
    'afterAjaxUpdate'=>'js:function(){head.Util.ajaxDel()}',
    //	'filter'=>$model,
    'colgroups'=>array(
        array(
            //"colwidth"=>array(null,150,150,150),
        )
    ),
    'columns'=>array(
        'account',
        'name',
        'bank',
        array(
            'name' => 'inactive',
            'type'=>'raw',
            'value' => array($this, 'getInactive'),
        ),
        array(
            'name'=>'',
            'type'=>'raw',
            'value' => array($this, 'getButton'),
        ),
    ),
)); ?>

<div class="modal fade" id="addaccount">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title">添加银行账户</h4>
            </div>
            <?php
            $form=$this->beginWidget('CActiveForm', array(
                'id'=>'account-form',
                'htmlOptions'=>array('class'=>'J_ajaxForm form-horizontal', 'role'=>'form'),
            ));
            ?>
            <div class="modal-body">
                <div class="form-group" model-attribute="account">
                    <label class="col-xs-3 control-label"><?php echo $form->labelEx($model, 'account'); ?></label>
                    <div class="col-xs-9">
                        <?php echo $form->textField($model, 'account', array('class'=>'form-control')); ?>
                    </div>
                </div>
                <div class="form-group" model-attribute="name">
                    <label class="col-xs-3 control-label"><?php echo $form->labelEx($model, 'name'); ?></label>
                    <div class="col-xs-9">
                        <?php echo $form->textField($model, 'name', array('class'=>'form-control')); ?>
                    </div>
                </div>
                <div class="form-group" model-attribute="bank">
                    <label class="col-xs-3 control-label"><?php echo $form->labelEx($model, 'bank'); ?></label>
                    <div class="col-xs-9">
                        <?php echo $form->textField($model, 'bank', array('class'=>'form-control')); ?>
                    </div>
                </div>
                <div class="form-group" model-attribute="inactive">
                    <label class="col-xs-3 control-label"><?php echo $form->labelEx($model, 'inactive'); ?></label>
                    <div class="col-xs-9">
                        <?php echo $form->checkBox($model, 'inactive'); ?>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary J_ajax_submit_btn">Save changes</button>
            </div>
            <?php
            echo $form->hiddenField($model, 'id');
            $this->endWidget();
            ?>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->

<script>
    function addAccount(id)
    {
        var data = {
            id: 0,
            account: '',
            name: '',
            bank: '',
            inactive: 0
        };
        if(id>0){
            $.ajax({
                type: 'get',
                url: '<?php echo $this->createUrl('getAccount')?>?id='+id,
                dataType: 'json',
                async: false
            }).done(function(ret){
                data = ret;
            });
        }
        $('#BankAccount_id').val(data.id);
        $('#BankAccount_account').val(data.account);
        $('#BankAccount_name').val(data.name);
        $('#BankAccount_bank').val(data.bank);
        if(data.inactive == 1)
            $('#BankAccount_inactive').attr('checked', true);
        else
            $('#BankAccount_inactive').attr('checked', false);
        $('#addaccount').modal();
    }

    function callback()
    {
        $.fn.yiiGridView.update('account-grid');
        $('#addaccount').modal('hide');
    }

    function callback1(data)
    {
        $.fn.yiiGridView.update('account-grid');
        if(data.state == 'success'){
            var ret = 0;
        }
        else{
            var ret = 1;
        }
        resultTip({msg: data.message, error: ret});
    }
</script>