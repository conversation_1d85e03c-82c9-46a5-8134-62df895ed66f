<?php

/**
 * This is the model class for table "pmeet_plan_item".
 *
 * The followings are the available columns in table 'pmeet_plan_item':
 * @property integer $planid
 * @property integer $target_date
 * @property string $timeslot
 * @property string $meet_index
 * @property integer $childid
 * @property integer $op_userid
 * @property integer $op_timestamp
 * @property integer $status
 * @property integer $stem
 * @property integer $openid
 */
class ParentMeetingItem extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'pmeet_plan_item';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('planid, target_date, timeslot, stem', 'required'),
			array('planid, target_date, childid, meet_index, op_userid, op_timestamp, stem, status', 'numerical', 'integerOnly'=>true),
			array('timeslot', 'length', 'max'=>32),
			array('openid', 'length', 'max'=>255),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('planid, target_date, meet_index, timeslot, openid, childid, op_userid, op_timestamp, stem, status, memo', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'plan' => array(self::BELONGS_TO, 'ParentMeetingPlan', 'planid'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'planid' => 'Planid',
			'target_date' => 'Target Date',
			'timeslot' => 'Timeslot',
			'meet_index' => 'Meet Index',
			'childid' => 'Childid',
			'op_userid' => 'Op Userid',
			'op_timestamp' => 'Op Timestamp',
			'status' => 'Status',
			'stem' => 'Stem',
			'openid' => 'Openid',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('planid',$this->planid);
		$criteria->compare('target_date',$this->target_date);
		$criteria->compare('timeslot',$this->timeslot,true);
		$criteria->compare('childid',$this->childid);
		$criteria->compare('meet_index',$this->meet_index);
		$criteria->compare('op_userid',$this->op_userid);
		$criteria->compare('op_timestamp',$this->op_timestamp);
		$criteria->compare('status',$this->status);
		$criteria->compare('stem',$this->stem);
		$criteria->compare('openid',$this->openid);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return ParentMeetingItem the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

    /**
     * @return CDbConnection the database connection used for this class
     */
    public function getDbConnection()
    {
        return Yii::app()->subdb;
    }

    public function uniKey()
    {
        return sprintf('%d_%d_%s_%s', $this->planid, $this->target_date, $this->timeslot, $this->meet_index);
    }

    public function exTimeslot()
    {
        return explode(',', $this->timeslot);
    }
}
