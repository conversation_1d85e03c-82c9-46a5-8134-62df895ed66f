<?php

/*
 * 生日提醒
 */

class BirthdayReminder extends CWidget {
    /*
     * 使用传入的参数 （参数名 today 虚拟今天的日期）
     */

    public $useTodayParam = false;
    /*
     * 提前几天开始倒计时
     */
    public $beforeDay = 7;
    /*
     * 生日之后 显示提示的天数
     */
    public $afterDay = 7;

    /*
     * 初始化 设定属性的默认值
     * 此方法会被 CController::beginWidget() 调用
     */

    public function init() {
        //格式转换 如果为空 赋于默认值
        $this->beforeDay = intval($this->beforeDay);
        $this->afterDay = intval($this->afterDay);
        if (empty($this->beforeDay)) {
            $this->beforeDay = 7;
        }
        if (empty($this->afterDay)) {
            $this->afterDay = 7;
        }
    }

    /*
     * 此方法会被 CController::endWidget() 调用
     */

    public function run() {

        // 显示 倒计时的提醒
        $showCountdown = false;
        // 显示当天及之后n天的提醒
        $showToday = false;

        $current_time = time();
        // 今天的日期
        $today = Mims::formatDateTime($current_time);

        // 如果是非产品环境 并且 允许使用today参数 今天的日期由today参数决定
        //if ('production' != Yii::app()->params['onlineBanking']) {
            if (true == $this->useTodayParam) {
                $today_param = Yii::app()->request->getQuery('today');
                if (!empty($today_param)) {
                    $today = $today_param;
                }
            }
        //}
        // 今天的时间戳
        $today_time = strtotime($today);
        // 生日的时间
        $birthday_time = null;
        // 相差年数
        $diff_years = 0;
        // 相差天数
        $diff_days = 0;
        // 孩子姓名
        $child_name = 0;

        // 得到孩子ID 孩子生日
        $childid = $this->controller->getChildid();
        if (isset($this->controller->myChildObjs[$childid])) {
            $child_info = $this->controller->myChildObjs[$childid];
            if (!empty($child_info)) {
                $birthday = $child_info['birthday'];
            }
            if (!empty($birthday)) {
                // 利用孩子的出生日期 和 今天的日期 结合算出孩子今年生日的日期
                $birthday_time = strtotime(date('Y', $today_time) . '-' . date('m-d', $birthday));
            }
        }

        if (!empty($birthday_time)) {

            // 今年生日前 N 天的时间戳
            $before_time = strtotime('-' . $this->beforeDay . ' days', $birthday_time);
            // 今年生日后 N 天的时间戳
            $after_time = strtotime('+' . $this->afterDay . ' days', $birthday_time);

            // 今天 与 出生日期 相差的年份 及 孩子的岁数
            $diff_years = intval(date('Y', $today_time) - date('Y', $birthday));
            // 必须保证大于0 
            if ($diff_years > 0) {
                if ($before_time <= $today_time && $today_time < $birthday_time) {
                    $showCountdown = true;
                    // 向上取整 向上取整
                    $diff_days = ceil(($birthday_time - $today_time ) / 86400);
                } else
                if ($birthday_time <= $today_time && $today_time <= $after_time) {
                    $showToday = true;
                    if (!empty($child_info)) {
                        $child_name = $child_info->getChildName($nick=true);
                    }
                }
            }
        }

        // 输出 变量到 视图文件
        $view_data = array(
            'showCountdown' => $showCountdown,
            'diff_years' => $diff_years,
            'diff_days' => $diff_days,
            'showToday' => $showToday,
            'child_name' => $child_name,
        );
        $this->render('birthdayReminder', $view_data);
    }

}