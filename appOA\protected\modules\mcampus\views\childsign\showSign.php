<?php
$type = ChildVacation::getConfig();
$data = ChildVacation::getMonth();
?>
<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span>
    </button>
    <h4 class="modal-title" id="exampleModalLabel"><?php echo Yii::t('asa','实际时间') ?></h4>
</div>
<?php $form = $this->beginWidget('CActiveForm', array(
    'id' => 'course-form',
    'enableAjaxValidation' => false,
    'action' => $this->createUrl('autoSign'),
    'htmlOptions' => array('class' => 'J_ajaxForm form-horizontal', 'role' => 'form'),
)); ?>
<div class="modal-body">
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'child_id'); ?></label>
        <div class="col-xs-10">
            <?php echo $childModel->getChildName() ?>
            <?php echo CHtml::HiddenField("signid", $model->child_id)?>
            <?php echo CHtml::HiddenField("vacation_id", $model->id)?>
            <?php echo CHtml::HiddenField("date", date("Y-m-d", $model->vacation_time_start))?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'type'); ?></label>
        <div class="col-xs-10">
            <?php echo $type[$model->type] ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'vacation_reason'); ?></label>
        <div class="col-xs-10">
            <?php echo $model->vacation_reason ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'est_begin_time'); ?></label>
        <div class="col-xs-10">
            <?php echo date("Y-m-d H:i:s", $model->est_begin_time) ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'time'); ?> <span class="required">*</span></label>
            <div class="col-xs-3">
                <?php echo CHtml::dropDownList('time', "", $data['hour'], array('class' => "form-control mr10", 'empty' => '时'));?>
            </div>
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'minute'); ?> *</label>
            <div class="col-xs-3">
                <?php echo CHtml::dropDownList('minute', "", $data['minute'], array('class' => "form-control mr10", 'empty' => '时'));?>
            </div>
    </div>
</div>
<div class="modal-footer">
    <button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global', 'Submit'); ?></button>
    <button type="button" class="btn btn-default"
            data-dismiss="modal"><?php echo Yii::t('global', 'Cancel'); ?></button>
</div>
<?php $this->endWidget(); ?>
