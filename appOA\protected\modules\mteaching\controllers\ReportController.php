<?php

class ReportController extends TeachBasedController
{
    public $selectedClassId = 0;
    public $selectedSemester = '';
    public $selectedTask = '';
    public $selectedChildId = 0;
    public $semesters = array(1, 2);

    public $printFW = array();

    // 访问action的初级权限
    public $actionAccessAuths = array(
        'setAllDs' => 'o_MS_RrportMgt',
        'setDsreportStatus' => 'o_MS_RrportMgt',
        'coures' => 'o_MS_Rrport',
        'saveDsreport' => 'o_MS_Rrport',
        'updatReport' => 'o_MS_Rrport',
        'delDsreport' => 'o_MS_Rrport',
        'reportchild' => 'o_MS_Rrport',
    );

    public function init()
    {
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'teaching';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site', 'Teaching Tasks');

        //初始化选择校园页面
        $this->branchSelectParams['hideOffice'] = true;
        $this->branchSelectParams['urlArray'] = array('//mteaching/report/index');
        $cs = Yii::app()->clientScript;
        if (Yii::app()->urlManager->parseUrl(Yii::app()->request) != 'mteaching/report/new') {
            $cs->registerCoreScript('jquery.ui');
            $cs->registerScriptFile($cs->getCoreScriptUrl() . '/jui/js/jquery-ui-i18n.min.js');
        }
        $cs->registerCssFile($cs->getCoreScriptUrl() . '/jui/css/base/jquery-ui.css');
        //标签字体需要的文件
        $cs->registerCssFile(Yii::app()->themeManager->baseUrl . '/label/iconfont.css?v=20231124');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/element/vue.global.js');
        $cs->registerCssFile(Yii::app()->themeManager->baseUrl . '/base/js/element/index.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/element/index.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/xlsx.full.min.js');
        Yii::import('common.models.portfolio.*');
        Yii::import('common.models.secondary.*');
    }

    public function beforeAction($action)
    {
        if (!parent::beforeAction($action)) {
            return false;
        }

        $this->selectedClassId = Yii::app()->request->getParam('classid', 0);
        $this->selectedSemester = Yii::app()->request->getParam('semester', 0);
        $this->selectedTask = Yii::app()->request->getParam('task', 0);
        $this->selectedChildId = Yii::app()->request->getParam('childid', 0);

        return true;
    }

    public function actionIndex($classid = 0, $semester = '', $task = '')
    {
        parent::initExt();

        $historyYid = Yii::app()->request->getParam('historyYid', '');
        //该校园或班级不支持此功能
        $stopped = array(
            Branch::PROGRAM_DAYSTAR => array(
                'schecklist', 'ptc'
            )
        );

        //校园支持本功能
        $stopping = false;

        if (isset($stopped[$this->branchObj->group])) {
            if (in_array($task, $stopped[$this->branchObj->group])) {
                //校园不支持本功能
                $stopping = true;
            }
        }
        $taskData = array();
        $taskData['stopping'] = $stopping;

        if (!$stopping) {
            if ($classid && $task) {
                $taskfun = 'task' . ucfirst($task);
                $taskData = $this->$taskfun();
            }
        }

        if (empty($historyYid)) {
            $criteria = new CDbCriteria();
            $criteria->compare('branchid', $this->branchId);
            $criteria->compare('is_selected', '1');
            $schoolYear = CalendarSchool::model()->find($criteria);
        }
        $yid = $historyYid ? $historyYid : $schoolYear->yid;
        $taskData['historyYid'] = $yid;
        $crit = new CDbCriteria();
        $crit->compare('calendar', $yid);
        $crit->compare('schoolid', $this->branchId);
        $crit->order = "start_time";
        $report = AchievementReport::model()->findAll($crit);


        $semesterreport = AchievementReport::model()->findByPk($semester);

        $criteria = new CDbCriteria();
        $criteria->compare('report_id', $semester);
        $criteria->compare('is_stat', 1);
        $mdel = AchievementReportChild::model()->findAll($criteria);
        $onlined = array();
        if ($mdel) {
            foreach ($mdel as $_mdel) {
                $taskData['onlined'][] = $_mdel->childid;
            }
        }


        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/underscore-min.js');

        $this->render('index', array(
            'taskData' => $taskData,
            'report' => $report,
            'semesterreport' => $semesterreport,
            'onlined' => $onlined,
            'yid' => $yid,
        ));
    }


    public function taskDsreport01()
    {
        Yii::import('common.models.reportCards.*');

        $crit = new CDbCriteria();
        $crit->compare('class_id', $this->selectedClassId);
        $crit->compare('semester', $this->selectedSemester);
        $crit->compare('status', 1);
        $crit->index = 'child_id';

        $taskData['onlined'] = array_keys(ReportsData::model()->findAll($crit));
        $taskData['children'] = $this->getNameList($this->selectedClassId);
        $taskData['calendarData'] = $this->getYidStartYearByClassIdAndBranchId($this->selectedClassId, $this->branchId);
        $cs = Yii::app()->clientScript;
        $cs->registerCoreScript('jquery.ui');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/underscore-min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/backbone-min.js');

        return $taskData;
    }

    //加载孩子页面的方法
    public function actionGetChildSemesterReport01($classid = 0, $semester = 0, $yid = 0)
    {

        Yii::import('common.models.reportCards.*');
        Yii::import('common.models.clender.*');
        Yii::import('common.models.attendance.*');
        Yii::import('common.models.hr.*');
        //学校课程
        $cfgCampusProgram = CommonUtils::LoadConfig('CfgCampusProgram');
        $childid = Yii::app()->request->getPost('childid', 0);

        $child = ChildProfileBasic::model()->findByPk($childid);

        $criteria = new CDbCriteria();
        $criteria->compare('class_id', $classid);
        $criteria->compare('child_id', $childid);
        $criteria->compare('semester', $semester);
        $model = ReportsData::model()->with('ext')->find($criteria);

        $criteria = new CDbCriteria();
        $criteria->compare('t.school', $this->branchId);
        $criteria->compare('t.stat', 1);
        $criteria->compare('grade.grade', $child->ivyclass->classtype);
        $criteria->with = "grade";
        $report = SecondaryReport::model()->find($criteria);

        $kecheng = array();
        if ($report) {
            foreach ($report->position as $v) {
                $kecheng[$v->id] = $v->getName();
            }
        }

        $crit = new CDbCriteria();
        $crit->compare('t.childid', $childid);
        $crit->compare('t.reportid', $semester);
        $crit->compare('t.status', 1);
        $crit->order = 't.create_time DESC';//
        $course = AchievementReportChildCourse::model()->with('reportCourse')->findAll($crit);

        $totaldays = "";
        $childvacation = "";

        $criteria = new CDbCriteria();
        $criteria->compare('report_id', $semester);
        $criteria->compare('childid', $childid);
        $criteria->compare('classid', $classid);
        $childrepoer = AchievementReportChild::model()->find($criteria);

        if (empty($childrepoer)) {
            $childrepoer = new AchievementReportChild;
        }

        $crit = new CDbCriteria();
        $crit->compare('calendar', $yid);
        $crit->compare('schoolid', $this->branchId);
        $crit->order = "start_time DESC";
        $reports = AchievementReport::model()->findAll($crit);

        $report_list = array();
        $option = array();
        foreach ($reports as $_repoert) {
            $report_list[] = $_repoert->id;
        }

        $crit = new CDbCriteria();
        $crit->compare('report_id', $report_list);
        $crit->compare('childid', $childid);
        $fFractions = AchievementReportChildFraction::model()->findAll($crit);

        foreach ($fFractions as $_fFractions) {
            $option[$_fFractions->courseid][$_fFractions->optionid][$_fFractions->report_id] = $_fFractions->value;
        }

        if ($childrepoer->is_stat != 1) {
            $report = AchievementReport::model()->findByPk($semester);
            $starttime = date("Y-m", $report->start_time);
            $endtime = date("Y-m", $report->end_time);
            $criteria = new CDbCriteria();
            $criteria->compare('yid', $report->calendar);
            $criteria->compare('month_label', ">=$starttime");
            $criteria->compare('month_label', "<=$endtime");
            $criteria->order = 'month_label';
            $schoolDays = CalendarSchoolDays::model()->findAll($criteria);

            $reset = reset($schoolDays);

            $reset_s = explode(",", $reset->schoolday_array);

            $end = end($schoolDays);
            $end_s = explode(",", $end->schoolday_array);

            $newArr = array();
            foreach ($reset_s as $vo) {
                if ($vo >= date('d', $report->start_time)) {
                    $newArr[] = $vo;
                };
            }

            $newArr_t = array();
            foreach ($end_s as $vo) {
                if ($vo <= date('d', $report->end_time)) {
                    $newArr_t[] = $vo;
                };
            }

            array_pop($schoolDays);
            array_shift($schoolDays);
            $totaldays = array();
            if ($schoolDays) {
                foreach ($schoolDays as $v) {
                    $totaldays[] = $v->schoolday;
                }
            }
            $totaldays[] = count($newArr);
            $totaldays[] = count($newArr_t);
            $totaldays = array_sum($totaldays);

            $criteria = new CDbCriteria();
            $criteria->compare('child_id', $childid);
            //$criteria->compare('type', "<>40");
            $criteria->compare('type', array(10, 20));
            $criteria->compare('vacation_time_start', ">={$report->start_time}");
            $criteria->compare('vacation_time_end', "<={$report->end_time}");
            $childvacation = ChildVacation::model()->findAll($criteria);

            $absent_days = array();
            if ($childvacation) {
                foreach ($childvacation as $item) {
                    if ($item->vacation_time_start == $item->vacation_time_end) {
                        $absent_days[$item->vacation_time_start] = $item->vacation_time_start;
                    } else {
                        $calendarSchlloModel = CalendarSchoolDays::model()->countCalendarSchoolday($yid, $item->vacation_time_start, $item->vacation_time_end, 0);

                        foreach ($calendarSchlloModel as $calendarItem) {
                            foreach ($calendarItem as $key => $day) {
                                foreach ($day['day'] as $dayItem) {
                                    $timeDay = $key . $dayItem;
                                    $absent_days[strtotime($timeDay)] = strtotime($timeDay);
                                }
                            }
                        }
                    }
                }
            }

            $childrepoer->calender = $yid;
            $childrepoer->schoolid = $this->branchId;
            $childrepoer->report_id = $semester;
            $childrepoer->childid = $childid;
            $childrepoer->classid = $classid;
            $childrepoer->is_stat = 0;
            $childrepoer->evaluate_number = $totaldays;
            $childrepoer->absent_days = count($absent_days);
            $childrepoer->uid = Yii::app()->user->id;
            $childrepoer->create_time = time();
            $childrepoer->update_time = time();
            $childrepoer->save();
        }

        $user_name = $this->teacherList(); //获取所有中学老师

        $html = $this->renderPartial('_dsreportinfo', array(
            'model' => $model,
            'child' => $child,
            'classid' => $classid,
            'semester' => $semester,
            'kecheng' => $kecheng,
            'totaldays' => $totaldays,
            'childvacation' => $childvacation,
            'course' => $course,
            'childrepoer' => $childrepoer,
            'user_name' => $user_name,
            'reports' => $reports,
            'option' => $option,
            'yid' => $yid,
        ));
        echo $html;
    }


    public function actionCoures()
    {
        $childid = Yii::app()->request->getParam('childid', '');
        $teacherid = Yii::app()->request->getParam('teacherid', '');
        $semester = Yii::app()->request->getParam('semester', '');
        $course = Yii::app()->request->getParam('course', '');
        $yid = Yii::app()->request->getParam('yid', '');

        if (empty($course)) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', "课程不能为空");
            $this->showMessage();
        }
        $criteria = new CDbCriteria();
        $criteria->compare('calender_id', $yid);
        $criteria->compare('childid', $childid);
        $criteria->compare('courseid', $course);
        $totalcount = AchievementReportChildTotal::model()->find($criteria);

        if (!$totalcount) {
            $totalcount = new AchievementReportChildTotal;
            $totalcount->calender_id = $yid;
            $totalcount->courseid = $course;
            $totalcount->childid = $childid;
            $totalcount->uid = Yii::app()->user->id;
            $totalcount->create_time = time();
            $totalcount->update_time = time();
            if (!$totalcount->save()) {
                $this->addMessage('state', 'fail');
                $err = current($totalcount->getErrors());
                $this->addMessage('message', $err[0]);
                $this->showMessage();
            }
        }

        $criteria = new CDbCriteria();
        $criteria->compare('childid', $childid);
        $criteria->compare('reportid', $semester);
        $criteria->compare('courseid', $course);
        $childCoursecount = AchievementReportChildCourse::model()->count($criteria);

        if (empty($childCoursecount)) {
            $model = new AchievementReportChildCourse;
            if ($_POST) {
                $model->childid = $childid;
                $model->calender = $yid;
                $model->teacherid = $teacherid;
                $model->courseid = $course;
                $model->reportid = $semester;
                $model->total_id = $totalcount->id;
                $model->create_id = Yii::app()->user->id;
                $model->update_id = Yii::app()->user->id;
                $model->status = 1;
                $model->create_time = time();
                $model->update_time = time();;
                if ($model->save()) {
                    Yii::log(CJSON::encode($model->attributes), 'info', 'msreport.coures_add');

                    $this->addMessage('state', 'success');
                    $this->addMessage('message', Yii::t('message', 'Data Saved!'));
                    $this->addMessage('callback', 'cbSuccess');
                } else {
                    $this->addMessage('state', 'fail');
                    $err = current($model->getErrors());
                    $this->addMessage('message', $err[0]);
                }
                $this->showMessage();
            }
        }
        $this->addMessage('state', 'fail');
        $this->addMessage('message', "该课程已经添加完成");
        $this->showMessage();
    }

    public function getYidStartYearByClassIdAndBranchId($classid, $branchId)
    {
        $result = Yii::app()->db->createCommand()
            ->select('c.yid as yid, y.startyear as startyear')
            ->from('ivy_class_list c')
            ->join('ivy_calendar_yearly y', 'y.yid=c.yid')
            ->where('c.classid=:classid and c.schoolid=:schoolid',
                array(
                    ':classid' => $classid,
                    ':schoolid' => $branchId
                ))
            ->queryRow();
        return $result;
    }

    //更改是否上线
    public function actionSetDsreportStatus()
    {
        if (Yii::app()->request->isAjaxRequest && Yii::app()->request->isPostRequest) {
            Yii::import('common.models.reportCards.*');

            $id = Yii::app()->request->getPost('id', 0);
            $type = Yii::app()->request->getPost('type', '');
            if ($id && $type) {
                $model = AchievementReportChild::model()->findByPk($id);
                if ($model != null) {
                    $model->is_stat = ($model->is_stat == 1) ? 0 : 1;
                    if ($model->save()) {
                        $this->addMessage('state', 'success');
                        $this->addMessage('message', Yii::t('message', 'Data Saved!'));
                    } else {
                        $this->addMessage('state', 'fail');
                        $err = current($model->getErrors());
                        $this->addMessage('message', $err[0]);
                    }
                } else {
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', Yii::t('teaching', 'Please complete the report first.'));
                }
            } else {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('teaching', 'Please complete the report first.'));
            }
            $this->showMessage();
        }
    }

    //批量上下线
    public function actionSetAllDs()
    {
        $classid = Yii::app()->request->getParam('classid', ''); //班级ID
        $yid = Yii::app()->request->getParam('yid', '');  //校历ID
        $type = Yii::app()->request->getParam('type', ''); //上线还是下线
        $semester = Yii::app()->request->getParam('semester', ''); //报告ID

        $criteria = new CDbCriteria();
        $criteria->compare('calender', $yid);
        $criteria->compare('report_id', $semester);
        $criteria->compare('classid', $classid);
        $reportChild = AchievementReportChild::model()->findAll($criteria);

        if ($reportChild) {
            $num = 0;
            foreach ($reportChild as $_reportChild) {
                $_reportChild->is_stat = ($type == "online") ? 1 : 0;
                $_reportChild->save();
                $num++;
            }

            $criteria = new CDbCriteria();
            $criteria->compare('calender', $yid);
            $criteria->compare('report_id', $semester);
            $criteria->compare('classid', $classid);
            $criteria->compare('is_stat', 1);
            $childList = AchievementReportChild::model()->findAll($criteria);
            $onlines = array();
            if ($childList) {
                foreach ($childList as $_child) {
                    $onlines[] = $_child->childid;
                }
            }

            $this->addMessage('state', 'success');
            $this->addMessage('message', Yii::t('teaching', '有:successNum条保存成功', array(':successNum' => $num)));
            $this->addMessage('data', $onlines);
            $this->showMessage();
        }

    }

    //增加总天数和缺勤天数
    public function actionReportchild()
    {
        $childid = Yii::app()->request->getParam('childid', '');
        $classid = Yii::app()->request->getParam('classid', '');
        $semester = Yii::app()->request->getParam('semester', '');
        $evaluate = Yii::app()->request->getParam('evaluate', '');
        $absent = Yii::app()->request->getParam('absent', '');
        //$counselor_cn = Yii::app()->request->getParam('counselor_cn', '');
        $counselor_en = Yii::app()->request->getParam('counselor_en', '');

        $criteria = new CDbCriteria();
        $criteria->compare('report_id', $semester);
        $criteria->compare('childid', $childid);
        $criteria->compare('classid', $classid);
        $childrepoer = AchievementReportChild::model()->find($criteria);

        if (empty($childrepoer)) {
            $childrepoer = new AchievementReportChild;
            $childrepoer->create_time = time();
        }
        $childrepoer->update_time = time();
        $childrepoer->report_id = $semester;
        $childrepoer->childid = $childid;
        $childrepoer->classid = $classid;;;
        if ($evaluate) {
            $childrepoer->evaluate_number = $evaluate;
        }
        if ($counselor_en) {
            $childrepoer->counselor_message_en = $counselor_en;
        }
        //if($counselor_cn){ $childrepoer->counselor_message_cn = $counselor_cn ;}
        if ($absent !== '') {
            $childrepoer->absent_days = $absent;
        }
        $childrepoer->uid = Yii::app()->user->id;
        if ($childrepoer->save()) {
            Yii::log(CJSON::encode($childrepoer->attributes), 'info', 'msreport.coures_total');

            $this->addMessage('state', 'success');
            $this->addMessage('message', Yii::t('message', 'success'));
            $this->addMessage('callback', 'cbSuccess');
        } else {
            $this->addMessage('state', 'fail');
            $err = current($childrepoer->getErrors());
            $this->addMessage('message', $err ? $err[0] : '失败');
        }
        $this->showMessage();
    }

    //增加课程下的资料
    public function actionSaveDsreport()
    {
        $childid = Yii::app()->request->getParam('childid', '');
        $semester = Yii::app()->request->getParam('semester', '');
        $course = Yii::app()->request->getParam('course', '');
        $coursse = Yii::app()->request->getParam('AchievementReportChildCourse', '');
        $option = Yii::app()->request->getParam('option', '');
        $optionvalue = Yii::app()->request->getParam('optionvalue', '');
        $myp = Yii::app()->request->getParam('myp', '');
        $totlevalue = Yii::app()->request->getParam('totlevalue', '');

        $criteria = new CDbCriteria();
        $criteria->compare('childid', $childid);
        $criteria->compare('reportid', $semester);
        $criteria->compare('courseid', $course);
        $childrepoer = AchievementReportChildCourse::model()->find($criteria);
        if ($childrepoer) {
            $childrepoer->teacher_message_en = $coursse['teacher_message_en'];
            $childrepoer->student_message_en = $coursse['student_message_en'];
            $childrepoer->myp = json_encode($myp);
            $childrepoer->update_time = time();
            $childrepoer->update_id = Yii::app()->user->id;
            if ($childrepoer->save()) {
                foreach ($option as $k => $_option) {
                    $criteria = new CDbCriteria();
                    $criteria->compare('cid', $childrepoer->id);
                    $criteria->compare('report_id', $semester);
                    $criteria->compare('courseid', $course);
                    $criteria->compare('optionid', $k);
                    $childFraction = AchievementReportChildFraction::model()->find($criteria);
                    if (empty($childFraction)) {
                        $childFraction = new AchievementReportChildFraction;
                    }
                    $childFraction->report_id = $semester;
                    $childFraction->childid = $childrepoer->childid;
                    $childFraction->cid = $childrepoer->id;
                    $childFraction->courseid = $course;
                    $childFraction->optionid = $k;
                    $childFraction->value = ($_option) ? $_option : '-';
                    $childFraction->uid = Yii::app()->user->id;
                    $childFraction->update_time = time();
                    if (!$childFraction->save()) {
                        $this->addMessage('state', 'fail');
                        $err = current($childFraction->getErrors());
                        $this->addMessage('message', $err ? $err[0] : '失败');
                        $this->showMessage();
                    }
                }

                $total = AchievementReportChildTotal::model()->findByPk($childrepoer->total_id);
                if ($total) {
                    $total->oprion_value = ($optionvalue) ? $optionvalue : "-";
                    $total->frecyion_total = json_encode($totlevalue);
                    $total->save();
                }

                Yii::log(CJSON::encode($childrepoer->attributes) . '-' . CJSON::encode($childFraction->attributes) . '-' . CJSON::encode($total->attributes), 'info', 'msreport.coures_detal');

                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message', 'success'));
                $this->addMessage('callback', 'cbSuccess');
                $this->showMessage();
            } else {
                $this->addMessage('state', 'fail');
                $err = current($childrepoer->getErrors());
                $this->addMessage('message', $err ? $err[0] : '失败');
                $this->showMessage();
            }
        } else {
            $this->addMessage('state', 'fail');
            $err = current($childrepoer->getErrors());
            $this->addMessage('message', $err ? $err[0] : '失败');
            $this->showMessage();
        }
    }

    //删除课程和课程下的所有数据
    public function actionDeldsreport()
    {
        $id = Yii::app()->request->getParam('id', '');
        $childCourse = AchievementReportChildCourse::model()->findByPk($id);
        if ($childCourse) {
            $criteria = new CDbCriteria();
            $criteria->compare('cid', $childCourse->id);
            $childFraction = AchievementReportChildFraction::model()->findAll($criteria);
            if ($childFraction) {
                foreach ($childFraction as $_childfraction) {
                    $_childfraction->delete();
                }

            }

            $criteria = new CDbCriteria();
            $criteria->compare('courseid', $childCourse->courseid);
            $criteria->compare('childid', $childCourse->childid);
            $criteria->compare('calender_id', $childCourse->calender);
            $childTotal = AchievementReportChildTotal::model()->findAll($criteria);
            if ($childTotal) {
                foreach ($childTotal as $_childTotal) {
                    $_childTotal->delete();
                }
            }
            Yii::log(Yii::app()->user->id . '->' . CJSON::encode($childCourse->attributes), 'info', 'msreport.coures_del');
            if ($childCourse->delete()) {
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message', 'success'));
                $this->addMessage('callback', 'cbSuccess');
            } else {
                $this->addMessage('state', 'fail');
                $err = current($childCourse->getErrors());
                $this->addMessage('message', $err ? $err[0] : '失败');
            }
            $this->showMessage();
        }
    }


    public function actionUpdatReport()
    {
        $id = Yii::app()->request->getParam('id', '');
        $teacher = Yii::app()->request->getParam('AchievementReportChildCourse', '');
        $model = AchievementReportChildCourse::model()->findByPk($id);

        $user_name = $this->teacherList();
        if ($teacher) {
            $model->teacherid = $teacher['teacherid'];
            if ($model->save()) {
                Yii::log(CJSON::encode($model->attributes), 'info', 'msreport.coures_edit');

                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message', 'success'));
                $this->addMessage('callback', 'cbSuccess');
            } else {
                $this->addMessage('state', 'fail');
                $err = current($model->getErrors());
                $this->addMessage('message', $err ? $err[0] : '失败');
            }
            $this->showMessage();
        }
        $this->renderpartial('_ptc', array(
            'model' => $model,
            'user_name' => $user_name,
            'modalTitle' => '编辑老师',
        ));
    }

    //预览启明星中学的的学生报告
    public function actionPreviewReportDs()
    {
        Yii::import('common.components.teaching.*');
        $childid = Yii::app()->request->getParam('childid', '');
        $id = Yii::app()->request->getParam('id', '');
        $classid = Yii::app()->request->getParam('classid', '');
        $yid = Yii::app()->request->getParam('yid', '');
        $courseid = Yii::app()->request->getParam('courseid', '');
        $params['childid'] = $childid;
        $params['id'] = $id;
        $params['classid'] = $classid;
        $params['yid'] = $yid;
        $params['courseid'] = $courseid;
        if ($params) {
            $reportHtml = ContentFetcher::getReportOfDs(
                $params,
                $this->branchId,
                1
            );
            echo $reportHtml;
        }

    }

    public function actionDownloadReportDs()
    {
        Yii::import('common.models.secondary.*');
        $params['childid'] = Yii::app()->request->getParam('childid', null);
        $params['id'] = Yii::app()->request->getParam('id', null);
        $params['classid'] = Yii::app()->request->getParam('classid', null);
        $params['yid'] = Yii::app()->request->getParam('yid', null);
        $params['courseid'] = Yii::app()->request->getParam('courseid', null);
        $report = AchievementReportChild::model()->findByAttributes(array('report_id' => $params['id'], 'childid' => $params['childid']));
        if ($report) {
            $uniqueId = $report->id;
            Yii::import('common.components.teaching.*');
            $params['yid'] = $report->calender;
            $params['courseid'] = $report->id;
            $html = ContentFetcher::getReportOfDs($params, $this->branchId);
            $html = base64_encode($html);
            $flag = $report->report->startyear + $report->report->cycle;
            if (in_array($report->report->cycle, array(1, 3)) || $flag < 2024 + 4) {
                $postParams = array(
                    'id' => $uniqueId,
                    'classid' => $params['classid'],
                    'lang' => Yii::app()->language == 'en_us' ? 'en' : 'cn',
                    'str' => $html,
                    'zoom' => 1.28,
                    'mac' => md5($uniqueId . $params['classid'] . $html . $this->securityKey)
                );
            } else {
                $coverUrl = Yii::app()->controller->createUrl('//reportHtml/cover', array(
                    'rid' => $params['id'],
                    'childId' => $params['childid'],
                    'type' => 'ms',
                    'lang' => Yii::app()->language,
                ));
                $headerUrl = Yii::app()->controller->createUrl('//reportHtml/header', array(
                    'rid' => $params['id'],
                    'childId' => $params['childid'],
                    'type' => 'ms',
                    'lang' => Yii::app()->language,
                ));
                $footerUrl = Yii::app()->controller->createUrl('//reportHtml/footer', array('lang' => Yii::app()->language,));
                $postParams = array(
                    'str' => $html,
                    'headerHtml' => $headerUrl,
                    'footerHtml' => $footerUrl,
                    'coverHtml' => $coverUrl,
                    'marginTop' => '30mm',
                    'marginBottom' => '30mm',
                    'id' => $uniqueId,
                    'classid' => $params['classid'],
                    'lang' => Yii::app()->language == 'en_us' ? 'en' : 'cn',
                    'mac' => md5($uniqueId . $params['classid'] . $html . $this->securityKey)
                );
            }

            try {
                $url = 'https://transfer.api.ivykids.cn/api/html2pdf';
                if (!CommonUtils::isProduction()) {
                    $url = 'http://192.168.149.137:3000/html2pdf';
                }
                $ch = curl_init();
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
                curl_setopt($ch, CURLOPT_POST, 1);
                curl_setopt($ch, CURLOPT_URL, $url);
                curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                    'Content-Type: application/json',
                ));
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($postParams));
                $result = curl_exec($ch);
                curl_close($ch);
                echo '<script>location.href="' . $result . '";setTimeout(function(){history.back()}, 10000);</script>';
            } catch (Exception $e) {
                return false;
            }
        }
    }

    /**
     * 获取所有的中学老师
     */

    public function teacherList()
    {
        $criteria = new CDbCriteria();
        $criteria->compare('department_id', 137);
        $criteria->select = 'position_id';
        $depPosLink = DepPosLink::model()->findAll($criteria);

        $position_id = array();
        foreach ($depPosLink as $v) {
            $position_id[] = $v->position_id;
        }

        $criteria = new CDbCriteria();
        $criteria->compare('t.level', 1);
        $criteria->compare('profile.occupation_en', $position_id);
        $user = User::model()->with('profile')->findAll($criteria);
        $user_name = array();
        foreach ($user as $_user) {
            $user_name[$_user->uid] = $_user->getName();
        }
        return $user_name;
    }

    public function actionNew($classid = 0, $semester = '')
    {
        $this->nonJquery = true;
        parent::initExt();

        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/jquery-1.12.4.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/jquery-ui.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/underscore-min.js');
        // bootstrap-table
        $cs->registerCssFile(Yii::app()->themeManager->baseUrl . '/base/js/bootstrap-table/bootstrap-table.min.css');
        $cs->registerCssFile(Yii::app()->themeManager->baseUrl . '/base/js/bootstrap-table/bootstrap-table-fixed-columns.min.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/bootstrap-table/bootstrap-table.min.js', CClientScript::POS_END);
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/bootstrap-table/bootstrap-table-fixed-columns.min.js', CClientScript::POS_END);
        Yii::import('common.models.timetable.*');

        // 查找当前tid
        $yid = $this->branchObj->schcalendar;

        // 当前学年的所有报告
        $crit = new CDbCriteria();
        $crit->compare('calendar', $yid);
        $crit->compare('schoolid', $this->branchId);
        $crit->order = "start_time";
        $crit->index = "id";
        $report = AchievementReport::model()->findAll($crit);
        $cReport = isset($report[$semester]) ? $report[$semester] : array();
        $report['deadline'] = 0;#已过报告截止日期
        if($report && time() > $report[$semester]->fill_in_end_time){
            $report['deadline'] = 1;
        }
        $timeTable = Timetable::model()->findByAttributes(array('yid' => $yid, 'schoolid' => $this->branchId));
//        $calendar = Calendar::model()->findByPk($timeTable->yid);

        if (!$timeTable) {
            return false;
        }

        $tid = $timeTable->id;
        $quarter = '';
        if ($cReport) {
            $quarter = $cReport->cycle;
        }
        $data = array(
            'yid' => $yid,
            'semester' => $semester,
            'quarter'=> $quarter,
        );
        $teacher_id = 0;
        // 判断是辅导员还是教师，1：辅导员，2：教师 4:升学指导 5：CAS 6:EE 7.学生目标收集
        if (in_array($classid, array(1, 3, 4, 5,6))) {
            $crit = new CDbCriteria();
            $crit->compare('yid', $yid);
            $crit->compare('stat', 10);
            $crit->compare('classtype', array("e6", "e7", 'e8', 'e9', 'e10', 'e11', 'e12'));
            $crit->order = 'child_age ASC, title ASC';
            $classModel = IvyClass::model()->findAll($crit);
            //  所有中学班级
            $classArr = array();
            $classByIdArr = array();
            foreach ($classModel as $val) {
                $classArr[$val->classid] = $val->title;
                $classByIdArr[$val->classid] = $val;
            }
            $criteria = new CDbCriteria();
            $criteria->compare('classid', array_keys($classArr));
            $criteria->compare('schoolid', $this->branchId);
            $criteria->compare('status', array(ChildProfileBasic::STATS_ACTIVE_WAITING, ChildProfileBasic::STATS_ACTIVE));
            $childModel = ChildProfileBasic::model()->findAll($criteria);

            // 所有中学班级里的所有孩子
            $childArr = array();
            $classData = array();
            $childids = array();
            foreach ($childModel as $val){
                $childids[] = $val->childid;
            }
            $allChildLabel = $this->getAllChildLabel($childids);
            foreach ($childModel as $val) {
                $last_name_en_str = !empty($val->last_name_en) ? $val->last_name_en . ',' : '';
                $nick_str = !empty($val->nick) ? '(' . $val->nick . ')' : '';
                $childArr[$val->classid][$val->childid] = array(
                    'childid' => $val->childid,
                    'classid' => $val->classid,
                    'classtype'=>$classByIdArr[$val->classid]['classtype'],
                    'childPhoto' => ($val->photo) ? CommonUtils::childPhotoUrl($val->photo) : CommonUtils::childPhotoUrl('blank.gif'),
                    'childname' => trim(sprintf("%s %s %s | %s", $last_name_en_str, $val->first_name_en, $nick_str, $val->name_cn)),
                    'nick' => trim($val->nick),
                    'childLabel' => $allChildLabel[$val->childid]['label'],
                    'reports' => array(
                        $semester => array(
                            'id' => $cReport->id,
                            'cycle' => $cReport->cycle,
                            'en' => '',
                        ),
                    ),
                );
            }
            foreach ($classArr as $key => $val) {
                if (isset($childArr[$key])) {
                    $classData[$key] = $val;
                }
            }
            // 查找孩子已有的报告
            $criteria = new CDbCriteria();
            $criteria->compare('calender', $yid);
            // $criteria->compare('report_id', $semester);
            $criteria->compare('childid', $childids);
            $childReports = AchievementReportChild::model()->findAll($criteria);
            if($classid == 1){
                $message = 'counselor_message_en';
                $temp_data = 'counselor_temp';
            }elseif ($classid == 3) {
                $message = 'social_innovation';
            } elseif ($classid == 4) {
                $message = 'college_counseling';
            } elseif ($classid == 5) {
                $message = 'cas_ee_comment';
                $temp_data = 'cas_temp';
            } elseif ($classid == 6){
                $message = 'ee_comment';
                $temp_data = 'ee_temp';
            } else {
                $message = 'counselor_message_en';
            }
            foreach ($childReports as $key => $val) {
                $childArr[$val->classid][$val->childid]['reports'][$val->report_id]['id'] = $val->report_id;
                $childArr[$val->classid][$val->childid]['reports'][$val->report_id]['cycle'] = $val->report->cycle;
                $childArr[$val->classid][$val->childid]['reports'][$val->report_id]['type'] = $val->report->type;
                $childArr[$val->classid][$val->childid]['reports'][$val->report_id]['en'] = $val->$message;
                $childArr[$val->classid][$val->childid]['reports'][$val->report_id]['temp_data'] = !empty($val->$temp_data) ? json_decode($val->$temp_data) : array();
                $childArr[$val->classid][$val->childid]['reports'][$val->report_id]['is_stat'] = $val->is_stat;
            }

            $data['childArr'] = $childArr;
            $data['classData'] = $classData;
        }
        if ($classid == 2) {
            $teacherId = Yii::app()->request->getParam('teacherId', '');
            $teacher_id = $this->staff->uid;
            $staffName = $this->staff->getName();
            $supervisor_data = Yii::app()->db->createCommand()
                ->from('mimssub.ivy_achievement_report_supervisor')
                ->where('supervisor_id=:supervisor_id and school_id=:school_id',
                    array(
                        ':supervisor_id' => $this->staff->uid,
                        ':school_id' => $this->branchId
                    ))
                ->queryRow();
            if ((Yii::app()->user->checkAccess('o_MS_RrportMgt') || $supervisor_data) && $teacherId) {
                $teacher_id = $teacherId;
                $userModel = User::model()->findByPk($teacherId);
                if ($userModel) {
                    $staffName = $userModel->getName();
                }
            }
        }
        $res = CommonUtils::requestDsOnline("achievementReport/teacherPageData", array(
            'tid' => $tid,
            'school_id' => $this->branchId,
            'teacher_id' => $teacher_id,
            'yid' => $yid,
            'semester' => $semester,
        ));
        if ($res['code'] == 0) {
            $data = array_merge($data, $res['data']);
        }
//        header('Content-Type:text/html; charset=utf-8');
//        echo "<pre>";
// var_dump($teacher_id,$data);die;
        $this->render('new', array(
            'data' => $data,
            'classid' => $classid,
            'report' => $report,
            'staffName' => $staffName,
        ));
    }

    //所有学生标签
    public function getAllChildLabel($child_ids){
        $requestUrl = 'label/allChild';
        $res = CommonUtils::requestDsOnline2($requestUrl, array('child_ids'=>$child_ids), 'get');
        return $res['data'];
    }

    // 所有中学老师 (2023-1-17 增加根据老师id获取授权课查看老师)
    public function actionTeacher()
    {
        $yid = Yii::app()->request->getParam('yid', '');
        $semester = Yii::app()->request->getParam('semester', '');
        Yii::import('common.models.timetable.*');
        $state = 'fail';
        $message = '非法操作';
        $data = array();
        if (Yii::app()->user->checkAccess('o_MS_RrportMgt')) {
            $criteria = new CDbCriteria;
            $criteria->compare('yid', $yid);
            $criteria->compare('schoolid', $this->branchId);
            $criteria->compare('status', 1);
            $tidModel = Timetable::model()->find($criteria);
            if ($tidModel) {
                $criteria = new CDbCriteria;
                $criteria->compare('tid', $tidModel->id);
                $criteria->compare('status', 1);
                $criteria->compare('schoolid', $this->branchId);
                $criteria->index = 'teacher_id';
                $teacherModel = TimetableCourseTeacher::model()->findAll($criteria);
                if ($teacherModel) {
                    $criteria = new CDbCriteria;
                    $criteria->compare('uid', array_keys($teacherModel));
                    $criteria->index = 'uid';
                    $userModel = User::model()->findAll($criteria);
                    foreach ($teacherModel as $val) {
                        $data[$val->teacher_id] = array(
                            'name' => isset($userModel[$val->teacher_id]) ? $userModel[$val->teacher_id]->getName() : $val->teacher_id,
                            'url' => $this->createUrl('report/new', array('classid' => 2, 'semester' => $semester, 'teacherId' => $val->teacher_id))
                        );
                    }
                    $state = 'success';
                    $message = '成功';
                }
            }
        } else {
            //当前老师id
            $teacher_id = $this->staff->uid;
            $res = CommonUtils::requestDsOnline("achievementReport/getTeacherList", array(
                'school_id' => $this->branchId,
                'teacher_id' => $teacher_id,
            ));
            if ($res['code'] == 0) {
                foreach ($res['data'] as $teacher_id => $item) {
                    $data[$teacher_id] = array(
                        'name' => $item['name'],
                        'url' => $this->createUrl('report/new', array('classid' => 2, 'semester' => $semester, 'teacherId' => $teacher_id))
                    );
                }
                $message = '成功';
                $state = 'success';
            }
        }


        $this->addMessage('state', $state);
        $this->addMessage('data', $data);
        $this->addMessage('message', $message);
        $this->showMessage();
    }

    // 保存教师评价报告
    public function actionSaveChildCourseReport()
    {
        $childid = Yii::app()->request->getPost('childid');
        $teacherId = Yii::app()->request->getPost('teacherId');
        $courseid = Yii::app()->request->getPost('courseid');#ivy_achievement_report_course id
        $teacherMessage = Yii::app()->request->getPost('teacherMessage');#老师评语
        $studentMessage = Yii::app()->request->getPost('studentMessage');#学生寄语
        $fra = Yii::app()->request->getPost('fra');#标准对应的分数值id
        $fids = Yii::app()->request->getPost('fids');#标准id数组
        $semester = Yii::app()->request->getPost('semester');#第几次报告的 ivy_achievement_report id
        $total = Yii::app()->request->getPost('total');#年终分数
        $final = Yii::app()->request->getPost('final');#各标准的最终成绩水平
        $timetable_records_id = Yii::app()->request->getPost('timetable_records_id');#timebable 课程id
        $timetable_records_code = Yii::app()->request->getPost('timetable_records_code');#timebable 课程code
        $childTemp = Yii::app()->request->getPost('childTemp');#模板数据
        if (!$courseid) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', 'courseid error');
            $this->showMessage();
        }
        //操作权限
        if (!Yii::app()->user->checkAccess('o_MS_RrportMgt') && $this->staff->uid != $teacherId) {
            //被审核人员数据
            $supervisor_data = Yii::app()->db->createCommand()
                ->from('mimssub.ivy_achievement_report_supervisor')
                ->where('supervisor_id=:supervisor_id and school_id=:school_id',
                    array(
                        ':supervisor_id' => $this->staff->uid,
                        ':school_id' => $this->branchId
                    ))
                ->queryAll();
            $is_supervisor = false;
            foreach ($supervisor_data as $item) {
                if ((int)$item['teacher_id'] === (int)$teacherId) {
                    $is_supervisor = true;
                    break;
                }
            }
            if ($is_supervisor === false) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t("message", "No permission"));
                $this->showMessage();
            }
        }

        if (mb_strlen($studentMessage, 'UTF-8') > 2000  || mb_strlen($teacherMessage, 'UTF-8') > 2000 ) {
            $this->addMessage('message', 'MAX 1000 characters');
            $this->showMessage();
        }

        $report = AchievementReport::model()->findByPk($semester);
        if (!empty($report->fill_in_end_time) && time() > $report->fill_in_end_time) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', Yii::t("principal", "Reporting Deadline") . '：' . date('Y/m/d H:i', $report->fill_in_end_time));
            $this->showMessage();
        }


        $yid = $this->branchObj->schcalendar;

        $criteria = new CDbCriteria();
        $criteria->compare('childid', $childid);
        $criteria->compare('calender', $yid);
        $criteria->compare('reportid', $semester);
        $criteria->compare('courseid', $courseid);
        $model = AchievementReportChildCourse::model()->find($criteria);
        if (!$model) {
            $model = new AchievementReportChildCourse();
            $model->status = 1;
            $model->create_id = $this->staff->uid;
            $model->create_time = time();
        }
        $model->timetable_records_id = $timetable_records_id;
        $model->timetable_records_code = $timetable_records_code;
        $model->calender = $yid;
        $model->childid = $childid;
        $model->reportid = $semester;
        $model->courseid = $courseid;
        $model->teacherid = $teacherId;
        $model->teacher_message_en = trim($teacherMessage);
        $model->student_message_en = trim($studentMessage);
        $model->template_data = json_encode($childTemp[$childid]);
        $model->update_id = $this->staff->uid;
        $model->update_time = time();
        if (!$model->save()) {
            $error = current($model->getErrors());
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $error[0]);
            $this->showMessage();
        }

        $criteria = new CDbCriteria();
        $criteria->compare('cid', $model->id);
        $criteria->compare('childid', $model->childid);
        $criteria->compare('report_id', $model->reportid);
        $criteria->compare('courseid', $model->courseid);
        $criteria->index = 'optionid';
        $fmodels = AchievementReportChildFraction::model()->findAll($criteria);
        $frecyion_total = array();
        foreach ($fids as $fid) {
            if (isset($fmodels[$fid])) {
                $fmodels[$fid]->timetable_records_id = $timetable_records_id;
                $fmodels[$fid]->timetable_records_code = $timetable_records_code;
                $fmodels[$fid]->value = $fra[$fid];
                $fmodels[$fid]->uid = $this->staff->uid;
                $fmodels[$fid]->update_time = time();
                $fmodels[$fid]->save();
            } else {
                $fmodel = new AchievementReportChildFraction();
                $fmodel->timetable_records_id = $timetable_records_id;
                $fmodel->timetable_records_code = $timetable_records_code;
                $fmodel->report_id = $model->reportid;
                $fmodel->childid = $model->childid;
                $fmodel->cid = $model->id;
                $fmodel->courseid = $model->courseid;
                $fmodel->optionid = $fid;
                $fmodel->value = $fra[$fid];
                $fmodel->uid = $this->staff->uid;
                $fmodel->update_time = time();
                $fmodel->save();
            }
            $frecyion_total[$fid] = $final[$fid];
        }
        if (!is_null($total)) {
            $report = AchievementReport::model()->findByPk($semester);
            if ($report && $report->cycle == 4) {
                $totalModel = AchievementReportChildTotal::model()->findByAttributes(array('calender_id' => $yid, 'childid' => $childid, 'courseid' => $courseid));
                if (!$totalModel) {
                    $totalModel = new AchievementReportChildTotal();
                    $totalModel->calender_id = $yid;
                    $totalModel->courseid = $courseid;
                    $totalModel->childid = $childid;
                    $totalModel->create_time = time();
                }
                $totalModel->timetable_records_id = $timetable_records_id;
                $totalModel->timetable_records_code = $timetable_records_code;
                $totalModel->oprion_value = $total;
                $totalModel->frecyion_total = json_encode($frecyion_total);
                $totalModel->uid = $this->staff->uid;
                $totalModel->update_time = time();
                $totalModel->save();
            }
        }

        $criteria = new CDbCriteria();
        $criteria->compare('calender', $yid);
        $criteria->compare('report_id', $semester);
        $criteria->compare('childid', $childid);
        $modelReportChild = AchievementReportChild::model()->find($criteria);

        if (!$modelReportChild) {
            $report = AchievementReport::model()->findByPk($semester);
            $childModel = ChildProfileBasic::model()->findByPk($childid);
            $modelReportChild = new AchievementReportChild();
            $modelReportChild->is_stat = 0;
            $modelReportChild->create_time = time();
            // 评估天数
            $modelReportChild->evaluate_number = $report->getStudentsTotalDays();
            // 缺勤天数
            $modelReportChild->absent_days = $report->getStudentsObsentDays($childid);
            $modelReportChild->calender = $yid;
            $modelReportChild->schoolid = $this->branchId;
            $modelReportChild->uid = $this->staff->uid;
            $modelReportChild->update_time = time();
            $modelReportChild->report_id = $semester;
            $modelReportChild->childid = $childid;
            $modelReportChild->classid = $childModel->classid;
        }
        $modelReportChild->is_cache = 0;
        $modelReportChild->save();

        $this->addMessage('message', 'Success');
        $this->addMessage('state', 'success');
        $this->showMessage();

    }

    // 保存辅导员评语
    public function actionSaveCounselorMessage()
    {
        $childid = Yii::app()->request->getPost('childid');
        $cntext = Yii::app()->request->getPost('cntext');
        $entext = Yii::app()->request->getPost('entext');
        $temp_data = Yii::app()->request->getPost('temp_data');
        $semester = Yii::app()->request->getPost('semester');
        $yid = Yii::app()->request->getPost('yid');
        $type = Yii::app()->request->getPost('type', 1);

        if (!$childid) {
            $this->addMessage('message', '孩子ID不能为空');
            $this->showMessage();
        }
        $childModel = ChildProfileBasic::model()->findByPk($childid);
        if (!$childModel) {
            $this->addMessage('message', '孩子不存在');
            $this->showMessage();
        }

        $report = AchievementReport::model()->findByPk($semester);
        if (!empty($report->fill_in_end_time) && time() > $report->fill_in_end_time) {
            $this->addMessage('message', Yii::t("principal", "Reporting Deadline") . '：' . date('Y/m/d H:i', $report->fill_in_end_time));
            $this->showMessage();
        }
        if (mb_strlen($entext, 'UTF-8') > 1000) {
            $this->addMessage('message', 'MAX 1000 characters');
            $this->showMessage();
        }
        $criteria = new CDbCriteria();
        $criteria->compare('calender', $yid);
        $criteria->compare('report_id', $semester);
        $criteria->compare('childid', $childid);
        $model = AchievementReportChild::model()->find($criteria);
        if (!$model) {
            $model = new AchievementReportChild();
            $model->is_stat = 0;
            $model->create_time = time();
        }
        // 评估天数
        $model->evaluate_number = $report->getStudentsTotalDays();
        // 缺勤天数
        $model->absent_days = $report->getStudentsObsentDays($childid);

        $model->calender = $yid;
        $model->schoolid = $this->branchId;
        $model->is_cache = 0;
        $model->report_id = $semester;
        $model->childid = $childid;
        $model->classid = $childModel->classid;
        if ($type == 3) {
            $model->social_innovation = $entext;
        } elseif ($type == 4) {
            $model->college_counseling = $entext;//升学指导
            $model->college_user = $this->staff->uid;//升学指导
            $model->college_time = time();//升学指导
        } elseif ($type == 5) {
            $model->cas_ee_comment = $entext;//CAS评语
            $model->cas_temp = json_encode($temp_data);//CAS评语模板
            $model->cas_user = $this->staff->uid;
            $model->cas_time = time();
        }elseif ($type == 6) {
            $model->ee_comment = $entext;//EE评语
            $model->ee_temp = json_encode($temp_data);//EE评语模板
            $model->ee_user = $this->staff->uid;
            $model->ee_time = time();//EE评语模板
        } else {
            $model->counselor_message_en = $entext;
            $model->counselor_temp = json_encode($temp_data);//辅导员评语模板
            $model->counselor_user = $this->staff->uid;
            $model->counselor_time = time();//辅导员评语模板
        }
        $model->uid = $this->staff->uid;
        $model->update_time = time();
        if (!$model->save()) {
            $this->addMessage('message', '保存失败');
            $this->showMessage();
        }
        $this->addMessage('state', 'success');
        $this->addMessage('message', 'success');
        $this->showMessage();
    }


    // 上线下线功能
    public function actionSaveReportMessage()
    {
        if(Yii::app()->request->isAjaxRequest && Yii::app()->request->isPostRequest){
            Yii::import('common.models.reportCards.*');

            $childid = Yii::app()->request->getPost('childid');
            $semester = Yii::app()->request->getPost('semester');
            $yid = Yii::app()->request->getPost('yid');
            $status = Yii::app()->request->getPost('status');

            if($childid && $semester && $yid){
                $reportModel = AchievementReport::model()->findByPk($semester);
                $criteria = new CDbCriteria();
                $criteria->compare('calender', $yid);
                $criteria->compare('report_id', $semester);
                $criteria->compare('childid', $childid);
                $model = AchievementReportChild::model()->find($criteria);

                if(!$model){
                    $childModel = ChildProfileBasic::model()->findByPk($childid);
                    $model = new AchievementReportChild();
                    $model->calender = $yid;
                    $model->schoolid = $this->branchId;
                    $model->report_id = $semester;
                    $model->childid = $childid;
                    $model->classid = $childModel->classid;
                    $model->uid = $this->staff->uid;
                    $model->create_time = time();
                    $model->update_time = time();
                }
                $model->evaluate_number = $reportModel->getStudentsTotalDays();
                $model->absent_days = $reportModel->getStudentsObsentDays($childid);
                $model->is_stat = ($status == 'online') ? 1 : 0;
                if($model->save()){
                    $this->addMessage('data', $model->is_stat);
                    $this->addMessage('state', 'success');
                    $this->addMessage('message', Yii::t('message', 'Data Saved!'));
                }
                else{
                    $this->addMessage('state', 'fail');
                    $err = current($model->getErrors());
                    $this->addMessage('message', $err[0]);
                }
            }
            else{
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('teaching', '参数错误！'));
            }
            $this->showMessage();
        }
    }

    //预览中学报告
    public function actionNewPreviewReportDs()
    {
        Yii::import('common.components.teaching.*');
        $childid = Yii::app()->request->getParam('childid', '');
        $report_id = Yii::app()->request->getParam('report_id', '');
        $classid = Yii::app()->request->getParam('classid', '');
        $yid = Yii::app()->request->getParam('yid', '');

        $criteria = new CDbCriteria();
        $criteria->compare('calender', $yid);
        $criteria->compare('report_id', $report_id);
        $criteria->compare('childid', $childid);
        $model = AchievementReportChild::model()->find($criteria);
//        echo "<pre>";
//var_dump($model);die;
        $params['childid'] = $childid;
        $params['id'] = $report_id;
        $params['classid'] = $classid;
        $params['yid'] = $yid;
        $params['courseid'] = $model->id;
        if ($params) {
            $reportHtml = ContentFetcher::getReportOfDs(
                $params,
                $this->branchId,
                1
            );
            echo $reportHtml;
        }
    }

    //批量上下线  暂时无用
    public function actionSetNewAllDs()
    {
        $classid = Yii::app()->request->getParam('classid_global', ''); //班级ID
        $yid = Yii::app()->request->getParam('yid', '');  //校历ID
        $type = Yii::app()->request->getParam('type', ''); //上线还是下线
        $semester = Yii::app()->request->getParam('semester', ''); //报告ID

        $reportModel = AchievementReport::model()->findByPk($semester);

        $criteria = new CDbCriteria();
        $criteria->compare('classid', $classid);
        $criteria->compare('status', array(10, 20));
        $chuldModel = ChildProfileBasic::model()->findAll($criteria);

        if ($chuldModel) {
            $onlines = array();
            foreach ($chuldModel as $val) {
                $criteria = new CDbCriteria();
                $criteria->compare('calender', $yid);
                $criteria->compare('report_id', $semester);
                $criteria->compare('classid', $classid);
                $criteria->compare('childid', $val->childid);
                $reportChild = AchievementReportChild::model()->find($criteria);
                if ($reportChild) {
                    $reportChild->evaluate_number = $reportModel->getStudentsTotalDays();
                    $reportChild->absent_days = $reportModel->getStudentsObsentDays($val->childid);
                    $reportChild->is_stat = ($type == "online") ? 1 : 0;
                    $reportChild->save();
                } else {
                    $model = new AchievementReportChild();
                    $model->evaluate_number = $reportModel->getStudentsTotalDays();//评估天数（需要上课的天数）
                    $model->absent_days = $reportModel->getStudentsObsentDays($val->childid);//缺勤天数
                    $model->calender = $yid;
                    $model->schoolid = $this->branchId;
                    $model->report_id = $semester;
                    $model->childid = $val->childid;
                    $model->classid = $classid;
                    $model->uid = $this->staff->uid;
                    $model->is_stat = ($type == "online") ? 1 : 0;
                    $model->create_time = time();
                    $model->update_time = time();
                    $model->save();
                }
                $onlines[] = $val->childid;
            }

            $this->addMessage('state', 'success');
            $this->addMessage('message', Yii::t('teaching', '有:successNum条保存成功', array(':successNum' => count($chuldModel))));
            $this->addMessage('data', $onlines);
            $this->showMessage();
        }
    }

    // 暂时无用
    public function saveCounselor($childid, $semester, $yid, $entext = '')
    {
        if (!$childid) {
            $this->addMessage('message', '孩子ID不能为空');
            $this->showMessage();
        }
        $childModel = ChildProfileBasic::model()->findByPk($childid);
        if (!$childModel) {
            $this->addMessage('message', '孩子不存在');
            $this->showMessage();
        }

        $report = AchievementReport::model()->findByPk($semester);

        $criteria = new CDbCriteria();
        $criteria->compare('calender', $yid);
        $criteria->compare('report_id', $semester);
        $criteria->compare('childid', $childid);
        $model = AchievementReportChild::model()->find($criteria);
        if (!$model) {
            $model = new AchievementReportChild();
            $model->is_stat = 0;
            $model->create_time = time();
        }
        // 评估天数
        $model->evaluate_number = $report->getStudentsTotalDays();
        // 缺勤天数
        $model->absent_days = $report->getStudentsObsentDays($childid);

        $model->calender = $yid;
        $model->schoolid = $this->branchId;
        $model->report_id = $semester;
        $model->childid = $childid;
        $model->classid = $childModel->classid;
        $model->counselor_message_en = $entext;
        $model->uid = $this->staff->uid;
        $model->update_time = time();
        if (!$model->save()) {
            return false;
        }
        return true;
    }

    public function actionGetOneSupervisor()
    {
        $teacher_id = Yii::app()->request->getParam("teacherId");
        $res = CommonUtils::requestDsOnline("achievementReport/getOneSupervisor", array(
            'school_id' => $this->branchId,
            'teacher_id' => $teacher_id,
        ));
        if ($res['code'] == 0) {
            $data = $res['data'];
            $this->addMessage('state', 'success');
            $this->addMessage('data', $data);
        } else {
            $this->addMessage('message', $res['msg']);
        }
        $this->showMessage();
    }


    public function actionGetSSTeacher()
    {
        $res = CommonUtils::requestDsOnline("achievementReport/SSTeacher", array(
            'school_id' => $this->branchId,
            'searchString' => Yii::app()->request->getParam('searchString')
        ));
        $this->addMessage('state', 'fail');
        if ($res['code'] == 0) {
            $data = $res['data'];
            $this->addMessage('state', 'success');
            $this->addMessage('data', $data);
        } else {
            $this->addMessage('message', $res['msg']);
        }
        $this->showMessage();
    }

    public function actionGetAllSupervisor()
    {
        $res = CommonUtils::requestDsOnline("achievementReport/getAllSupervisor", array(
            'school_id' => $this->branchId,
        ));
        $this->addMessage('state', 'fail');
        if ($res['code'] == 0) {
            $data = $res['data'];
            $this->addMessage('state', 'success');
            $this->addMessage('data', $data);
        } else {
            $this->addMessage('message', $res['msg']);
        }
        $this->showMessage();
    }

    public function actionAddSupervisor()
    {
        $teacher_id = Yii::app()->request->getParam('teacher_id');
        $res = CommonUtils::requestDsOnline("achievementReport/addSupervisor", array(
            'school_id' => $this->branchId,
            'teacher_id' => $teacher_id,
        ));
        $this->addMessage('state', 'fail');
        if ($res['code'] == 0) {
            $data = $res['data'];
            $this->addMessage('state', 'success');
            $this->addMessage('data', $data);
        } else {
            $this->addMessage('message', $res['msg']);
        }
        $this->showMessage();
    }

    public function actionAddAuditee()
    {
        $supervisor_id = Yii::app()->request->getParam('supervisor_id');
        $teacher_id = Yii::app()->request->getParam('teacher_id');
        $res = CommonUtils::requestDsOnline("achievementReport/addAuditee", array(
            'school_id' => $this->branchId,
            'teacher_id' => $teacher_id,
            'supervisor_id' => $supervisor_id,
        ));
        $this->addMessage('state', 'fail');
        if ($res['code'] == 0) {
            $data = $res['data'];
            $this->addMessage('state', 'success');
            $this->addMessage('data', $data);
        } else {
            $this->addMessage('message', $res['msg']);
        }
        $this->showMessage();
    }

    public function actionDelAuditee()
    {
        $supervisor_id = Yii::app()->request->getParam('supervisor_id');
        $teacher_id = Yii::app()->request->getParam('teacher_id');
        $res = CommonUtils::requestDsOnline("achievementReport/delAuditee", array(
            'school_id' => $this->branchId,
            'teacher_id' => $teacher_id,
            'supervisor_id' => $supervisor_id,
        ));
        $this->addMessage('state', 'fail');
        if ($res['code'] == 0) {
            $data = $res['data'];
            $this->addMessage('state', 'success');
            $this->addMessage('data', $data);
        } else {
            $this->addMessage('message', $res['msg']);
        }
        $this->showMessage();
    }

    public function actionDelSupervisor()
    {
        $supervisor_id = Yii::app()->request->getParam('supervisor_id');
        $res = CommonUtils::requestDsOnline("achievementReport/delSupervisor", array(
            'school_id' => $this->branchId,
            'supervisor_id' => $supervisor_id,
        ));
        $this->addMessage('state', 'fail');
        if ($res['code'] == 0) {
            $data = $res['data'];
            $this->addMessage('state', 'success');
            $this->addMessage('data', $data);
        } else {
            $this->addMessage('message', $res['msg']);
        }
        $this->showMessage();
    }

    /**
     * @throws PHPExcel_Exception
     * @throws CException
     * @throws PHPExcel_Writer_Exception
     * @throws PHPExcel_Reader_Exception
     */
    public function actionDownloadTemplate()
    {
        Yii::import('common.models.timetable.*');
        $teacherId = Yii::app()->request->getParam('teacher_id', '');
        $semester = Yii::app()->request->getParam('semester', '');
        $report_course_id = Yii::app()->request->getParam('report_course_id', '');
        $course_code = Yii::app()->request->getParam('course_code', '');
        $course_id = Yii::app()->request->getParam('course_id', '');
        $is_template = Yii::app()->request->getParam('is_template', 1);//1导出模板 0导出数据
        // 查找当前tid
        $yid = $this->branchObj->schcalendar;
        $timeTable = Timetable::model()->findByAttributes(array('yid' => $yid, 'schoolid' => $this->branchId));
        $tid = $timeTable->id;
        $data = CommonUtils::requestDsOnline("achievementReport/teacherPageData", array(
            'tid' => $tid,
            'school_id' => $this->branchId,
            'teacher_id' => $teacherId,
            'yid' => $yid,
            'semester' => $semester,
        ));
        $student_reports = array();
        if (empty($data['code'])) {
            $student = $data['data']['students'][$course_id];//学生数据
            $reports = $data['data']['reports'][$semester];//评语数据
            $data['data']['evaluationTemplate']['courseTemp'];
            $tmpId = '';
            foreach ($data['data']['evaluationTemplate']['courseTemp'] as $tmp_id=>$item){
                if(in_array($course_code,$item)){
                    $tmpId = $tmp_id;
                }
            }
            $evaluationTemplate = $data['data']['evaluationTemplate']['tempData'][$tmpId];//模板信息
            foreach ($student as $child_id => $child_item) {
                $student_reports[$child_id] = $reports[$child_id][$report_course_id];
            }
//        header('Content-Type:text/html; charset=utf-8');
//        echo "<pre>";
//        var_dump($data);die;
            //导出文件
            spl_autoload_unregister(array('YiiBase', 'autoload'));
            Yii::import('ext.PHPExcel.PHPExcel', true);
            spl_autoload_register(array('YiiBase', 'autoload'));
            $file_path_base = dirname(__FILE__) . '/../excel/';
            $res_file = $evaluationTemplate['template_file'];
            if (empty($res_file)) {
                return '';
            }
            $file = $file_path_base . '/' . $res_file;
            $fileType = pathinfo($file, PATHINFO_EXTENSION);
            //根据文件类型，加载不同类库
            $library = array(
                'xls'=>'Excel5',
                'xlsx'=>'Excel2007',
            );
            $type = $library[$fileType];
            if(empty($type)){
                return '';
            }
            $objReader = \PHPExcel_IOFactory::createReader($type);
            $objPHPExcel = $objReader->load($file, $encode = 'utf-8'); // 加载文件
            $worksheet = $objPHPExcel->getActiveSheet();
            //从第二行开始填充数据
            $row = 2;
            foreach ($student as $child_id => $item) {
                //填充
                $worksheet->getCell('A' . $row)->setValue($item['childid']); //id
                $worksheet->getCell('B' . $row)->setValue($item['childname']);//姓名
                $worksheet->getCell('C' . $row)->setValue($item['className']);//班级
                if ($is_template == 1) {
                    //填充导出数据
                    if (!empty($student_reports[$child_id]['template_data'])) {
                        foreach ($student_reports[$child_id]['template_data'] as $k => $report_item) {
                            $cell = $evaluationTemplate['excel_cell'][$k];
                            $optionsData = array_filter($evaluationTemplate['options'], function ($item) use ($k) {
                                return $item['flag'] == $k;
                            });
                            $optionsData = array_values($optionsData);
                            $value = '';
                            if(isset($optionsData[0]['options'])){
                                $option_value = array_filter($optionsData[0]['options'], function ($item) use ($report_item) {
                                    return $item['value'] == $report_item;
                                });
                                $option_value = array_values($option_value);
                                $value = $option_value[0]['title'];
                            }elseif (isset($optionsData[0]['textarea'])){
                                $value = $report_item;
                            }
                            if(!empty($cell) && !empty($row)){
                                $worksheet->getCell($cell . $row)->setValue($value);
                            }
                        }
                    }
                }
                //对齐方式
                $worksheet->getStyle('B' . $row)->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_LEFT);//左对齐
                $worksheet->getStyle('C' . $row)->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_LEFT);
                if ($row % 2 == 0) {
                    //背景色
                    $worksheet->getStyle('A' . $row)->getFill()->setFillType(\PHPExcel_Style_Fill::FILL_SOLID)->getStartColor()->setRGB("D9D9D9");
                    $worksheet->getStyle('B' . $row)->getFill()->setFillType(\PHPExcel_Style_Fill::FILL_SOLID)->getStartColor()->setRGB("D9D9D9");
                    $worksheet->getStyle('C' . $row)->getFill()->setFillType(\PHPExcel_Style_Fill::FILL_SOLID)->getStartColor()->setRGB("D9D9D9");
                    foreach ($evaluationTemplate['import_map'] as $k=>$v){
                        $worksheet->getStyle($k . $row)->getFill()->setFillType(\PHPExcel_Style_Fill::FILL_SOLID)->getStartColor()->setRGB("D9D9D9");
                    }
                }
                $row++;
            }
            //下载修改后的excel文件
            if (!$is_template) {
                $file_type = '-Template';//模板
            } else {
                $file_type = '-ExportData';//导出数据
            }
            $xlsx_name = $course_code . '-' . $file_type . '.xlsx';//下载文件的名字
            header('pragma:public');
            header('Content-type:application/vnd.ms-excel;charset=utf-8;');
            header('Content-Disposition:attachment;filename=' . $xlsx_name);//"xls"参考下一条备注
            $objWriter = \PHPExcel_IOFactory::createWriter($objPHPExcel, $type);
            //"Excel2007"生成2007版本的xlsx，"Excel5"生成2003版本的xls 调用工厂类
            return $objWriter->save('php://output');
        }
    }

    /***导出 CAS EE的模板
     * @return string|void
     * @throws CException
     * @throws PHPExcel_Exception
     * @throws PHPExcel_Reader_Exception
     * @throws PHPExcel_Writer_Exception
     */
    public function actionDownloadTemplate2()
    {
        Yii::import('common.models.timetable.*');
        $class_id = Yii::app()->request->getParam('class_id', '');#获取班级内的孩子
        $is_template = Yii::app()->request->getParam('is_template', 1);//1导出模板 0导出数据
        $CAS_EE = Yii::app()->request->getParam('CAS_EE', '');//CAS 或者 EE 或者辅导员
        $report_id = Yii::app()->request->getParam('report_id', '');
        $CAS_EE_type = array(
            1=>'counselor',
            5=>'CAS',
            6=>'EE',
        );
        //模板数据储存字段
        $db_field = array(
            1=>'counselor_temp',
            5=>'cas_temp',
            6=>'ee_temp',
        );
        // 查找当前tid
        $yid = $this->branchObj->schcalendar;
        $data = CommonUtils::requestDsOnline("achievementReport/CAS_EE", array(
            'school_id' => $this->branchId,
            'yid' => $yid,
            'class_id' => $class_id,
            'report_id' => $report_id,
        ));
        $student_reports = array();
        if (empty($data['code'])) {
            $student = $data['data']['child_list'];//学生数据
            $reports = $data['data']['reports'];//评语数据
            $tmpId = '';
            foreach ($data['data']['evaluationTemplate']['courseTemp'] as $tmp_id=>$item){
                if(in_array($CAS_EE_type[$CAS_EE],$item)){
                    $tmpId = $tmp_id;
                }
            }
            $evaluationTemplate = $data['data']['evaluationTemplate']['tempData'][$tmpId];//模板信息
            foreach ($student as $child_id => $child_item) {
                $student_reports[$child_id] = $reports[$child_id];
            }
//        header('Content-Type:text/html; charset=utf-8');
//        echo "<pre>";
//        var_dump($data);die;
            //导出文件
            spl_autoload_unregister(array('YiiBase', 'autoload'));
            Yii::import('ext.PHPExcel.PHPExcel', true);
            spl_autoload_register(array('YiiBase', 'autoload'));
            $file_path_base = dirname(__FILE__) . '/../excel/';
            $res_file = $evaluationTemplate['template_file'];
            if (empty($res_file)) {
                return '';
            }
            $file = $file_path_base . '/' . $res_file;
            $fileType = pathinfo($file, PATHINFO_EXTENSION);
            //根据文件类型，加载不同类库
            $library = array(
                'xls'=>'Excel5',
                'xlsx'=>'Excel2007',
            );
            $type = $library[$fileType];
            if(empty($type)){
                return '';
            }
            $objReader = \PHPExcel_IOFactory::createReader($type);
            $objPHPExcel = $objReader->load($file, $encode = 'utf-8'); // 加载文件
            $worksheet = $objPHPExcel->getActiveSheet();
            //从第二行开始填充数据
            $row = 2;
            foreach ($student as $child_id => $item) {
                //填充
                $worksheet->getCell('A' . $row)->setValue($item['id']); //child_id
                $worksheet->getCell('B' . $row)->setValue($item['name']);//姓名
                $worksheet->getCell('C' . $row)->setValue($item['className']);//班级
                if ($is_template == 1) {
                    //填充导出数
                    if (!empty($student_reports[$child_id][$db_field[$CAS_EE]])) {
                        foreach ($student_reports[$child_id][$db_field[$CAS_EE]] as $k => $report_item) {
                            $cell = $evaluationTemplate['excel_cell'][$k];
                            $optionsData = array_filter($evaluationTemplate['options'], function ($item) use ($k) {
                                return $item['flag'] == $k;
                            });
                            $optionsData = array_values($optionsData);
                            $value = '';
                            if(isset($optionsData[0]['options'])){
                                $option_value = array_filter($optionsData[0]['options'], function ($item) use ($report_item) {
                                    return $item['value'] == $report_item;
                                });
                                $option_value = array_values($option_value);
                                $value = $option_value[0]['title'];
                            }elseif (isset($optionsData[0]['textarea'])){
                                $value = $report_item;
                            }
                            if(!empty($cell) && !empty($row)){
                                $worksheet->getCell($cell . $row)->setValue($value);
                            }
                        }
                    }
                }
                //对齐方式
                $worksheet->getStyle('B' . $row)->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_LEFT);//左对齐
                $worksheet->getStyle('C' . $row)->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_LEFT);
                if ($row % 2 == 0) {
                    //背景色
                    $worksheet->getStyle('A' . $row)->getFill()->setFillType(\PHPExcel_Style_Fill::FILL_SOLID)->getStartColor()->setRGB("D9D9D9");
                    $worksheet->getStyle('B' . $row)->getFill()->setFillType(\PHPExcel_Style_Fill::FILL_SOLID)->getStartColor()->setRGB("D9D9D9");
                    $worksheet->getStyle('C' . $row)->getFill()->setFillType(\PHPExcel_Style_Fill::FILL_SOLID)->getStartColor()->setRGB("D9D9D9");
                    foreach ($evaluationTemplate['import_map'] as $k=>$v){
                        $worksheet->getStyle($k . $row)->getFill()->setFillType(\PHPExcel_Style_Fill::FILL_SOLID)->getStartColor()->setRGB("D9D9D9");
                    }
                }
                $row++;
            }
            //下载修改后的excel文件
            if (!$is_template) {
                $file_type = '-Template';//模板
            } else {
                $file_type = '-ExportData';//导出数据
            }
            $xlsx_name = $CAS_EE_type[$CAS_EE] . '-' . $file_type . '.xlsx';//下载文件的名字
            header('pragma:public');
            header('Content-type:application/vnd.ms-excel;charset=utf-8;');
            header('Content-Disposition:attachment;filename=' . $xlsx_name);//"xls"参考下一条备注
            $objWriter = \PHPExcel_IOFactory::createWriter($objPHPExcel, $type);
            //"Excel2007"生成2007版本的xlsx，"Excel5"生成2003版本的xls 调用工厂类
            return $objWriter->save('php://output');
        }
    }


    public function actionTeacherList()
    {
        $requestUrl = 'learningGoal/teacherList';
        $this->remote($requestUrl, array(
            'yid' => $this->branchObj->schcalendar,
        ), 'get');
    }

    public function actionStudentData()
    {
        $year = Yii::app()->request->getParam('year');
        $quarter = Yii::app()->request->getParam('quarter');
        $course_id = Yii::app()->request->getParam('course_id');
        $requestUrl = 'learningGoal/studentData';
        $this->remote($requestUrl, array(
            'yid'       => $this->branchObj->schcalendar,
            'quarter'   => $quarter,
            'course_id' => $course_id,
        ), 'get');
    }

    public function actionSendEmail()
    {
        $quarter = Yii::app()->request->getParam('quarter');
        $course_id = Yii::app()->request->getParam('course_id');
        $child_ids = Yii::app()->request->getParam('child_ids');
        $requestUrl = 'learningGoal/sendEmail';
        $this->remote($requestUrl, array(
            'yid'       => $this->branchObj->schcalendar,
            'quarter'   => $quarter,
            'child_ids' => $child_ids,
        ), 'post');
    }

    public function remote($requestUrl, $requestData = array(), $method)
    {
        if (empty($requestData['school_id'])) {
            $requestData['school_id'] = $this->branchId;
        }
        $res = CommonUtils::requestDsOnline2($requestUrl, $requestData, $method);
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('message', $res['msg']);
            $this->addMessage('data', $res['data']);
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->addMessage('data', $res['data']);
        }
        $this->showMessage();
    }
}
