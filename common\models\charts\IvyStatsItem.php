<?php

/**
 * This is the model class for table "ivy_stats_item".
 *
 * The followings are the available columns in table 'ivy_stats_item':
 * @property integer $id
 * @property integer $period_type
 * @property integer $period
 * @property string $schooldid
 * @property integer $category
 * @property integer $code
 * @property integer $subcode
 * @property double $data
 * @property integer $startyear
 * @property string $memo
 * @property integer $userid
 * @property integer $created
 * @property integer $updated
 */
class IvyStatsItem extends CActiveRecord
{
    const TYPE_Y = 1; # 按年保存数据
    const TYPE_M = 2; # 按月保存数据
    const TYPE_W = 3; # 按周保存数据
    const TYPE_D = 4; # 按天保存数据

    //主分类
    const CATEGORY_FINANCE = 1;
    //类型代码
    const CODE_FINANCE_ACTUAL = 11;//实际
    const CODE_FINANCE_BUDGET = 12;//预算
    //子类型代码
    const SUBCODE_FINANCE_ENROLLMENT = 1001;//招生人数
    const SUBCODE_FINANCE_REVENUE = 1002;//收入
    const SUBCODE_FINANCE_PERSONNEL_COST = 1003;//人事费用
    const SUBCODE_FINANCE_FACILITIES_COST = 1004;//设备费用
    const SUBCODE_FINANCE_OVERHEND = 1005;//校园日常运作成本
    const SUBCODE_FINANCE_OTHER_COSTS = 1006;//其它(折旧、市场管理费用等)
    const SUBCODE_FINANCE_CASH_FLOW = 1007;//现金流
    const SUBCODE_FINANCE_TEACHERS = 1008;//教师（全职、包括保育教师）
    const SUBCODE_FINANCE_ADMIN = 1009;//行政人员（园长、副园长、行政经理、助理、保健医）
    const SUBCODE_FINANCE_RENT = 1010;//房租
    
    const CATEGORY_VISIT = 2;  # 来访记录 主分类
    const CODE_VISIT_NEW = 21; # 新加人数
    const CODE_VISIT_INVALID = 22; # 无效人数
    const CODE_VISIT_SUCCEED = 23; # 成功人数
    const CODE_VISIT_NOLOG = 24; # 30天内未跟进人数
    const CODE_VISIT_ACTIVE = 25; # 有效人数
	
	/**
	 * Returns the static model of the specified AR class.
	 * @return IvyStatsItem the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_stats_item';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('period_type, period, category, code, data, startyear, userid, created, updated', 'required'),
			array('id, period_type, period, category, code, subcode, startyear, userid, created, updated', 'numerical', 'integerOnly'=>true),
			array('schoolid', 'length', 'max'=>10),
			array('memo', 'length', 'max'=>255),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, period_type, period, schoolid, category, code, subcode, data, startyear, memo, userid, created, updated', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'period_type' => 'Period Type',
			'period' => 'Period',
			'schooldid' => 'Schooldid',
			'category' => 'Category',
			'code' => 'Code',
			'subcode' => 'Subcode',
			'data' => 'Data',
			'startyear' => 'Startyear',
			'memo' => 'Memo',
			'userid' => 'Userid',
			'created' => 'Created',
			'updated' => 'Updated',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('period_type',$this->period_type);
		$criteria->compare('period',$this->period);
		$criteria->compare('schoolid',$this->schooldid,true);
		$criteria->compare('category',$this->category);
		$criteria->compare('code',$this->code);
		$criteria->compare('subcode',$this->subcode);
		$criteria->compare('data',$this->data);
		$criteria->compare('startyear',$this->startyear);
		$criteria->compare('memo',$this->memo,true);
		$criteria->compare('userid',$this->userid);
		$criteria->compare('created',$this->created);
		$criteria->compare('updated',$this->updated);

		return new CActiveDataProvider(get_class($this), array(
			'criteria'=>$criteria,
		));
	}
    
    /*
     * 财会数据类型列表
     * @return array type
     */
    static public function getFinanceType(){
        $type = array(
            'code'=>array(
                self::CODE_FINANCE_ACTUAL=>Yii::t('charts','实际'),
                self::CODE_FINANCE_BUDGET=>Yii::t('charts','预算'),
            ),
            'subcode'=>array(
                self::SUBCODE_FINANCE_ENROLLMENT=>Yii::t('charts','招生人数'),
                self::SUBCODE_FINANCE_REVENUE=>Yii::t('charts','收入'),
                self::SUBCODE_FINANCE_PERSONNEL_COST=>Yii::t('charts','人事费用'),
                self::SUBCODE_FINANCE_FACILITIES_COST=>Yii::t('charts','设备费用'),
                self::SUBCODE_FINANCE_OVERHEND=>Yii::t('charts','校园日常运作成本'),
                self::SUBCODE_FINANCE_RENT=>Yii::t('charts','房租'),
                self::SUBCODE_FINANCE_OTHER_COSTS=>Yii::t('charts','其它'),
                self::SUBCODE_FINANCE_CASH_FLOW=>Yii::t('charts','现金流'),
                self::SUBCODE_FINANCE_TEACHERS=>Yii::t('charts','教师'),
                self:: SUBCODE_FINANCE_ADMIN=>Yii::t('charts','行政人员'),
            )
        );
        return $type;
    }
    
    static public function getVistType()
    {
        return array(
            self::CATEGORY_VISIT => Yii::t('charts','来访记录'),
            self::CODE_VISIT_NEW => Yii::t('charts','新进潜客数'),
            self::CODE_VISIT_INVALID => Yii::t('charts','无效人数'),
            self::CODE_VISIT_SUCCEED => Yii::t('charts','成功人数'),
            self::CODE_VISIT_NOLOG => Yii::t('charts','30天内未跟进人数'),
            self::CODE_VISIT_ACTIVE => Yii::t('charts','有效潜客人数'),
        );
    }
	
	public function scopes(){
		return array(
			'finance' => array(
				'condition'=>'category='.self::CATEGORY_FINANCE,
				'order'=>'period ASC',
			)
		);
	}
	
	static public function getYearsBySchool($schoolId){
		$data = Yii::app()->db->createCommand()
					->selectDistinct('startyear')
					->from('ivy_stats_item')
					->where('schoolid=:schoolid AND category=:category',array(':schoolid'=>$schoolId, ':category'=>self::CATEGORY_FINANCE))
					->queryAll();
					
		$result = array();
		if(count($data)){
			foreach($data as $d){
				$result[] = $d['startyear'];
			}
			rsort($result);
		}
		return $result;
	}
	
    static public function getFinanceYearlyData($schoolId,$startYear){
        $data = Yii::app()->db->createCommand()
                    ->select('period,schoolid,code,subcode,data,startyear,memo')
                    ->from('ivy_stats_item')
                    ->where('schoolid=:schoolid and period_type=:period_type and category=:category and startyear=:startyear', array(
                            ':schoolid' => $schoolId,
                            ':period_type' => IvyStatsItem::TYPE_M,
                            ':category' => IvyStatsItem::CATEGORY_FINANCE,
                            ':startyear' => $startYear,
                    ))->order('period ASC')->queryAll();
        return count($data) ? $data : array();
    }
	
    
    ///*
    // * 取某学校数据库中最大的月
    // * @param string $schoolId
    // * @return int $month
    // */
    //static public function getMaxMonth($schoolId){
    //    $month = 0;
    //    $data = Yii::app()->db->createCommand()
    //                ->select('max(period) as month')
    //                ->from('ivy_stats_item')
    //                ->where('schoolid=:schoolid AND category=:category',array(':schoolid'=>$schoolId, ':category'=>self::CATEGORY_FINANCE))
    //                ->queryRow();
    //    if (is_array($data) && count($data)){
    //        $month = $data['month'];
    //    }
    //    return $month;
    //}
    //
    // /*
    // * 取某学校数据库中已存在的月份
    // * @param string $schoolId
    // * @return array $month .e.g. array(201401=>201401)
    // */
    //static public function getExistMonth($schoolId){
    //    $monthList = array();
    //    $data = Yii::app()->db->createCommand()
    //                ->select('period')
    //                ->from('ivy_stats_item')
    //                ->where('schoolid=:schoolid',array(':schoolid'=>$schoolId))
    //                ->group('period')
    //                ->queryColumn();
    //    if (count($data)){
    //        foreach ($data as $val){
    //            $monthList[$val] = $val;
    //        }
    //    }
    //    return $monthList;
    //}
    //
    ///*
    // * 取某学校某月份的财会数据
    // * @param string $schoolId
    // * @param int $month e.g. 201401
    // * @return array $data
    // */
    //public function getFinanceMonthData($schoolId,$month){
    //    $data = Yii::app()->db->createCommand()
    //                ->select('period,schoolid,code,subcode,data,startyear,memo')
    //                ->from($this->tableName())
    //                ->where('schoolid=:schoolid and period=:period and period_type=:period_type and category=:category',array(
    //                    ':schoolid'=>$schoolId,
    //                    ':period'=>$month,
    //                    ':period_type'=>IvyStatsItem::TYPE_M,
    //                    ':category'=>IvyStatsItem::CATEGORY_FINANCE,
    //                ))->queryAll();
    //    return count($data) ? $data : array();
    //}
    //
    ///*
    // * 取某学校某个学年累计到当前月份分类汇总
    // * @param string $schoolId  e.g. BJ_XHL
    // * @param int $month        e.g. 201401
    // * @param int $startYear    e.g. 2013
    // */
    //public function getFinanceYearData($schoolId,$month,$startYear){
    //    $data = Yii::app()->db->createCommand()
    //                ->select('period,schoolid,code,subcode,data,startyear,memo')
    //                ->from($this->tableName())
    //                ->where('schoolid=:schoolid and period<=:period and period_type=:period_type and category=:category and startyear=:startyear', array(
    //                        ':schoolid' => $schoolId,
    //                        ':period' => $month,
    //                        ':period_type' => IvyStatsItem::TYPE_M,
    //                        ':category' => IvyStatsItem::CATEGORY_FINANCE,
    //                        ':startyear' => $startYear,
    //                ))->queryAll();
    //    return count($data) ? $data : array();
    //}
    
    static public function getStartYear($month) {
        $year = substr($month, 0, 4);
        $month = intval(substr($month, 4, 2));
        $startYear = $month < 9 ? $year - 1 : $year;
        return $startYear;
    }
}