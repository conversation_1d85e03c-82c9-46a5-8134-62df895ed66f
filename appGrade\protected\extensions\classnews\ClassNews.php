<?php
class ClassNews extends CWidget{
	public $classId;
    public $yId;
    public $weekNum;
    public $cssFile;
    public $assetsUrl;
	
	public function init(){
		Yii::import('ext.schoolnews.models.*');

//        if($this->assetsUrl===null)
//			$this->assetsUrl=Yii::app()->getAssetManager()->publish(dirname(__FILE__) . '/assets', false, -1, YII_DEBUG);

        $cs = Yii::app()->clientScript;
        if (false !== $this->cssFile)
        {
            if (null === $this->cssFile)
                //$this->cssFile = $this->assetsUrl . '/css.css';
				$this->cssFile = Yii::app()->theme->baseUrl.'/css/portfolio.css';
            $cs->registerCssFile($this->cssFile);
        }
	}
	
	public function run(){

        Mims::LoadHelper('HtoolKits');
        
        $criteria = new CDbCriteria();
        $criteria->compare('classid', $this->classId);
        $criteria->compare('yid', $this->yId);
        $criteria->compare('weeknumber', $this->weekNum);
        $criteria->compare('stat', 20);
        $criteria->compare('type', 20);
        if (CommonUtils::isGradeSchool($this->classId)){
            $CNotesModel = NotesSchCla::model()->findAll($criteria);
        }else{
            $CNotesModel[] = NotesSchCla::model()->find($criteria);
        }
        if (!empty($CNotesModel)){
            $criteria = new CDbCriteria();
            $criteria->compare('t.classid', $this->classId);
            $criteria->compare('userWithProfile.level', "<>0");
            $criteria->order = 'isheadteacher DESC,weight ASC';
            $criteria->index='teacherid';
            $Teachers = ClassTeacher::model()->with('userWithProfile', 'staffInfo')->findAll($criteria);

            $this->render('classnews', array('CNotesModel'=>$CNotesModel,'Teachers'=>$Teachers,));
        }
	}
	
}