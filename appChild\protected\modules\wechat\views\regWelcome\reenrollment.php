<div class="main-wrapper">
    <div class="section main col-xs-9">
        <div class="bg"></div>
        <div class="bg-mask"></div>
        <div style='position:relative;'>
            <div class='lan'>
                <button type="button" class="btn btn-success btn-sm language" onclick='tabLan(this)'><?php echo (Yii::app()->language == 'zh_cn')? 'English' : '中文' ; ?></button>
            </div>
            <div class="card-body" id='ball'  style="width:90%;background:#fff;border-radius:5px">
                <h5 class="card-title"><?php echo $data['title']; ?></h5>
                <p class="time"><?php echo Yii::t("wechat", "Registration: "); ?><?php echo $data['start']; ?> ~ <?php echo $data['end']; ?></p>
                <p class="card-text"><?php echo nl2br($data['intro']); ?></p>
                <!-- <p class="text-danger">
                    <?php echo nl2br($data['intro2']); ?>
                </p> -->
            </div>
            <div class='btnBto'>
                <a href='<?php echo $data['url'];?>' class="btn btn-primary btn-lg btn-block" style='width:100%' id='hrefUrl'><?php echo Yii::t("reg", "Start"); ?>

                </a>
            </div>
        </div>
    </div>
</div>
<script>
    function tabLan(obj){
        var val=$(".language");
       if(val.text()=="中文"){
            window.location.href='<?php echo $this->createUrl('/wechat/regWelcome/reEnrollment/lang/zh_cn', array('sid' => $data['sid'], 'state' => $data['state']));?>'
            obj.innerHTML="English";
        }else{
            window.location.href='<?php echo $this->createUrl('/wechat/regWelcome/reEnrollment/lang/en_us', array('sid' => $data['sid'], 'state' => $data['state']));?>'
            obj.innerHTML="中文";
        }
    }
    var h=window.innerHeight/1.5;
    $('#ball').css('max-height',h)
</script>
<style>
    .time{
        /* text-align: center; */
        /* margin-top: 20px; */
        color: #ff8802;
        /* width: 90%; */
        /* margin-left: 5%; */
        /* padding-left: 1.25rem; */
        /* text-shadow: 0px 3px 3px #ff8802; */
        font-size:13px
    }

    .card-body{
        margin-top:28px;
        margin-left:5%;
        overflow-y:auto;
        -webkit-overflow-scrolling: touch;
    }
    .card-text{
        line-height:25px
    }
    .card-title{
        font-weight:700
    }
    .bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        height: 100%;
        background-image: url('<?php echo $data['bgimg'];?>');
        background-size: cover;
        filter: blur(2px);
    }
    .bg-mask{
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.2);
    }
    .main-wrapper {
        height: 100vh;
    }

    .section {
        height: 100%;
    }

    h4 {
        color: white;
        margin-top: 4em;
        margin-bottom: 2em;
    }
    .lan{
        padding-top:28px;
        text-align: right;
        margin-right: 5%;
    }
    .lan button{
        border-radius:4px
    }
    .btnBto{
        width:90%;
        margin:auto;
        margin-top:30px
    }
</style>
