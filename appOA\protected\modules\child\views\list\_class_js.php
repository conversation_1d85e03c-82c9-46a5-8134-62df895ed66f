<script>
	$('a.displayInvoice').bind('click',function(){
		var cid=$(this).attr('cid');
		var caid=$(this).attr('caid');
		var targetId=$(this).attr('rel');
		if($('#'+targetId).html()!=""){
			$('#'+targetId).html("");
		}else{
			$('#'+targetId).html("");
			$('#'+targetId).addClass("pop_loading");
			$.ajax({
			  type: "GET",
			  url: "<?php echo $this->createUrl('//child/list/viewInvoicesByClassid'); ?>",
			  data: {classid: cid, calendarid: caid, flag:"<?php echo $flag;?>"}
			}).done(function(html) {
			  $('#'+targetId).html(html);
			  $('#'+targetId).removeClass("pop_loading");
			});		
		}
	})
</script>