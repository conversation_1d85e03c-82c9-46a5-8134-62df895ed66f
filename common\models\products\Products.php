<?php

/**
 * This is the model class for table "ivy_products".
 *
 * The followings are the available columns in table 'ivy_products':
 * @property integer $id
 * @property integer $cid
 * @property integer $type
 * @property string $title_cn
 * @property string $title_en
 * @property double $unit_price
 * @property string $intro_cn
 * @property string $intro_en
 * @property integer $status
 * @property string $school_id
 * @property integer $updated_userid
 * @property integer $updated_time
 */
class Products extends CActiveRecord
{

	const STATUS_NORMAL = 1;
	const STATUS_DISABLED = 0;
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_products';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('cid, type, title_cn, title_en, unit_price, status, school_id, updated_userid, updated_time', 'required'),
			array('cid, status,updated_userid, updated_time, sort', 'numerical', 'integerOnly'=>true),
			array('unit_price', 'numerical'),
			array('scribing_price', 'numerical'),
			array('type, title_cn, title_en, school_id', 'length', 'max'=>255),
            array('intro_cn, intro_en', 'safe'),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, cid, type, title_cn, title_en, unit_price, scribing_price, intro_cn, intro_en, status,school_id, updated_userid, updated_time, sort', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'attr' => array(self::HAS_MANY, 'ProductsAttr', array('pid'=>'id')),
            'img' => array(self::HAS_MANY, 'ProductsImg', array('pid'=>'id')),
            'Featured'=>array(self::HAS_ONE, 'ProductsFeatured', array('pid'=>'id', 'school_id' => 'school_id')),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'cid' => 'Cid',
			'type' => 'Type',
			'title_cn' => Yii::t('asainvoice','Title Cn'),
			'title_en' => Yii::t('asainvoice','Title En'),
			'unit_price' => Yii::t('asa','价格'),
			'scribing_price' => Yii::t('asa','划线价格'),
			'intro_cn' => Yii::t('asa','商品中文介绍'),
			'intro_en' => Yii::t('asa','商品英文介绍'),
			'status' => Yii::t('invoice','Status'),
			'sort' => Yii::t('campus','Sort'),
			'featured' => Yii::t('campus','首页推荐'),
			'school_id' => 'School',
			'updated_userid' => 'Updated Userid',
			'updated_time' => 'Updated Time',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('cid',$this->cid);
		$criteria->compare('type',$this->type,true);
		$criteria->compare('title_cn',$this->title_cn,true);
		$criteria->compare('title_en',$this->title_en,true);
		$criteria->compare('unit_price',$this->unit_price);
		$criteria->compare('scribing_price',$this->scribing_price);
		$criteria->compare('intro_cn',$this->intro_cn,true);
		$criteria->compare('intro_en',$this->intro_en,true);
		$criteria->compare('status',$this->status);
		$criteria->compare('sort',$this->sort);
		$criteria->compare('school_id',$this->school_id,true);
		$criteria->compare('updated_userid',$this->updated_userid);
		$criteria->compare('updated_time',$this->updated_time);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return Products the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	public static function config()
    {
        return array(
            'uniform' => array(
                'title' => 'Uniform',
                'item' => array(
                    'color' => array(
                        'title' => 'Color',
                        'option' => array(
                            1 => Yii::t("labels",'Red'),
                            2 => Yii::t("labels",'Green'),
                            3 => Yii::t("labels",'Blue'),
                            4 => Yii::t("labels",'Khaki'),
                            5 => Yii::t("labels",'White'),
                            6 => Yii::t("labels",'Complete set'),
                            7 => Yii::t("labels",'Grey'),
                            8 => Yii::t("labels",'Navy blue'),
                            9 => Yii::t("labels",'Orange'),
                        ),
                    ),
                    'size' => array(
                        'title' => 'Size',
                        'option' => array(
                            1 => '90',
                            2 => '100',
                            3 => '110',
                            4 => '120',
                            5 => '130',
                            6 => '140',
                            7 => '150',
                            8 => '160',
                            9 => '165',
                            10 => '170',
                            11 => '175',
                            12 => '180',
                            13 => '185',
                            22 => '190',
                            23 => '195',
                            47 => '200',
                            14 => '2XS',
                            15 => 'XS',
                            16 => 'S',
                            17 => 'M',
                            18 => 'L',
                            19 => 'XL',
                            20 => '2XL',
                            21 => '3XL',
                            24 => '90 Presale',
                            25 => '100 Presale',
                            26 => '110 Presale',
                            27 => '120 Presale',
                            28 => '130 Presale',
                            29 => '140 Presale',
                            30 => '150 Presale',
                            31 => '160 Presale',
                            32 => '165 Presale',
                            33 => '170 Presale',
                            34 => '175 Presale',
                            35 => '180 Presale',
                            36 => '185 Presale',
                            37 => '190 Presale',
                            38 => '195 Presale',
                            48 => '200 Presale',
                            39 => '2XS Presale',
                            40 => 'XS Presale',
                            41 => 'S Presale',
                            42 => 'M Presale',
                            43 => 'L Presale',
                            44 => 'XL Presale',
                            45 => '2XL Presale',
                            46 => '3XL Presale',
                            49 => 'Free Size',
                        ),
                    ),
                )
            ),
        );
    }

    // 根据条件获取商品分类
    public static function getProductsCat($schoolid, $status = false, $share = true)
    {
    	$criteria = new CDbCriteria();
    	$criteria->compare('school_id', $schoolid);
    	if ($status !== false) {
	    	$criteria->compare('status', $status);
    	}
    	$criteria->order = 'sort ASC';
    	if ($share) {
    		$shareCatId = Products::getShareProductsCatId($schoolid);
    		if (count($shareCatId) > 0) {
	    		$criteria->addInCondition('id',$shareCatId, 'OR');
    		}
		}
		$models = ProductsCat::model()->findAll($criteria);
		foreach ($models as $k => $v) {
			if ($status !== false) {
				if ($v->status != $status) {
					unset($models[$k]);
				}
			}
		}
    	return $models;

    }

    // 获取共享的商品分类ID
    public static function getShareProductsCatId($schoolid)
    {
    	$shareCatId = array();
    	$criteria = new CDbCriteria();
    	$criteria->compare('school_id', $schoolid);
        $shareCat = ProductsCatShare::model()->findAll($criteria);
    	foreach ($shareCat as $item) {
    		$shareCatId[] = $item->cid;
    	}
    	return $shareCatId;
    }

    // 根据条件获取所有的商品
    public static function getProducts($schoolid, $cid = false, $limit = false, $status = Products::STATUS_NORMAL)
    {
    	$criteria = new CDbCriteria();
    	$criteria->compare('school_id', $schoolid);
    	$criteria->compare('status', $status);
    	$criteria->order = 'sort DESC';
    	if ($limit) {
    		$criteria->limit = $limit;
    	}
    	if ($cid) {
    		$criteria->compare('cid', $cid);
    	}
    	return Products::model()->findAll($criteria);
    }

    // 获取共享的所有商品
    public static function getShareProducts($schoolid, $limit = false, $status = Products::STATUS_NORMAL)
    {
    	$shareCatId = Products::getShareProductsCatId($schoolid);
    	if (count($shareCatId) == 0) {
    		return array();
    	}
    	$criteria = new CDbCriteria();
    	$criteria->compare('cid', $shareCatId);
    	$criteria->compare('status', $status);
    	$criteria->order = 'sort DESC';
    	if ($limit) {
    		$criteria->limit = $limit;
    	}
    	return Products::model()->findAll($criteria);
    }

    public function getTitle()
    {
        switch (Yii::app()->language) {
            case "zh_cn":
                return  $this->title_cn ;
                break;
            case "en_us":
                return  $this->title_en;
                break;
        }
    }
}
