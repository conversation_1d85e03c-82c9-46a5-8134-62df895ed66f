<?php

/**
 * This is the model class for table "ivy_asa_course_demand".
 *
 * The followings are the available columns in table 'ivy_asa_course_demand':
 * @property integer $id
 * @property string $site_id
 * @property string $openid
 * @property integer $parent_id
 * @property integer $childid
 * @property string $course_title
 * @property string $teacher_name
 * @property integer $teacher_contact
 * @property string $memo
 * @property integer $status
 * @property integer $updated
 */
class AsaCourseDemand extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_asa_course_demand';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('site_id, parent_id, childid, course_title, status, updated', 'required'),
			array('parent_id, childid, teacher_contact, status, updated', 'numerical', 'integerOnly'=>true),
			array('site_id, openid, course_title, teacher_name', 'length', 'max'=>255),
			array('memo', 'safe'),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, site_id, openid, parent_id, childid, course_title, teacher_name, teacher_contact, memo, status, updated', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'site_id' => Yii::t('campusreport','schoolid'),
			'openid' => 'Openid',
			'parent_id' => Yii::t('asa','Parent Name'),
			'childid' => Yii::t('campusreport','childid'),
			'course_title' => Yii::t('asa','course_title'),
			'teacher_name' => Yii::t('asa','teacher_name'),
			'teacher_contact' => Yii::t('asa','teacher_contact'),
			'memo' => Yii::t('asa','memo'),
			'status' => 'Status',
			'updated' => Yii::t('curriculum','Updated_time'),
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('site_id',$this->site_id,true);
		$criteria->compare('openid',$this->openid,true);
		$criteria->compare('parent_id',$this->parent_id);
		$criteria->compare('childid',$this->childid);
		$criteria->compare('course_title',$this->course_title,true);
		$criteria->compare('teacher_name',$this->teacher_name,true);
		$criteria->compare('teacher_contact',$this->teacher_contact);
		$criteria->compare('memo',$this->memo,true);
		$criteria->compare('status',$this->status);
		$criteria->compare('updated',$this->updated);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return AsaCourseDemand the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}
