<?php

class TranscriptController extends TeachBasedController
{
    public $actionAccessAuths = array();

    public function init()
    {
        parent::init();

        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'teaching';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site', 'Teaching Tasks');

        $this->branchSelectParams['hideOffice'] = false;
        $this->branchSelectParams['urlArray'] = array('//mteaching/transcript/index');

        $cs = Yii::app()->clientScript;
        $cs->registerCoreScript('jquery.ui');
        $cs->registerCssFile($cs->getCoreScriptUrl() . '/jui/css/base/jquery-ui.css');
        $cs->registerCssFile(Yii::app()->themeManager->baseUrl . '/base/js/viewer/viewer.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/xlsx.full.min.js');

        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/element/vue.global.js');
        $cs->registerCssFile(Yii::app()->themeManager->baseUrl . '/base/js/element/index.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/element/index.js');
        // 七牛上传所需文件
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/plupload/plupload.full.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/qiniu.min.js');

        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/viewer/viewer.js');

        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/v-calendar/v-calendar.umd.min.js');
        $cs->registerCssFile(Yii::app()->theme->baseUrl . '/css/calendar.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/sortable/Sortable.min.js');

    }

    public function createUrl($route, $params = array(), $ampersand = '&', $parentOnly = false)
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        return parent::createUrl($route, $params, $ampersand);
    }

    public function actionApi()
    {
        $url = Yii::app()->request->getParam('url');
        $apiList = $this->getApiList();
        if (!isset($apiList[$url])) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '请求地址错误');
            $this->showMessage();
        }
        $remoteUrl = $apiList[$url];
        if ($_GET) {
            $getPath = '';
            foreach ($_GET as $key => $value) {
                if ($getPath == '') {
                    $getPath .= "$key=$value";
                } else {
                    $getPath .= "&$key=$value";
                }
            }
            $remoteUrl = $remoteUrl . '?' . $getPath;
        }
        $requestData = $_POST;
        $this->remote($remoteUrl, $requestData);
    }

    public function getApiList()
    {
        return array(
            'config' => 'transcript/config',
            'courseList' => 'transcript/courseList',
            'creditSave' => 'transcript/creditSave',
            'studentGrades' => 'transcript/studentGrades',
            'studentCourseList' => 'transcript/studentCourseList',
            'studentCourseConfig' => 'transcript/studentCourseConfig',
            'studentCourseConfigSave' => 'transcript/studentCourseConfigSave',
            'transcriptGenerate' => 'transcript/transcriptGenerate',
            'transcriptPreview' => 'transcript/transcriptPreview',
            'transcriptHistory' => 'transcript/transcriptHistory',
            'transcriptWatermark' => 'transcript/transcriptWatermark',
            'transcriptWatermarkSave' => 'transcript/transcriptWatermarkSave',
        );
    }

    public function actionIndex()
    {
        $show = false;

        if (Yii::app()->user->checkAccess('ivystaff_it')) {
            $show = true;
        }
        elseif (Yii::app()->user->checkAccess('ivystaff_cd')) {
            $model = DepPosLink::model()->findByPk($this->staff->profile->occupation_en);
            if(in_array($model->department_id, array(137, 147))) {
                $show = true;
            }
        }
        elseif (in_array(Yii::app()->user->id, array(8036700, 8027100, 8030635, 8037254))) {
            $show = true;
        }

        $this->render('index', array('show' => $show));
    }

    public function actionPreview()
    {
        Yii::app()->language = 'en_us';
        $id = Yii::app()->request->getParam('id');
        $withToolBar = Yii::app()->request->getParam('withToolBar', 1);
        if (!$id) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '参数错误');
            $this->showMessage();
        }
        $res = CommonUtils::requestDsOnline('transcript/transcriptPreview', array('id' => $id, 'school_id' => $this->branchId));
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('message', $res['msg']);
            $this->addMessage('data', $res['data']);
        }
        if ($res['data']['pdf_url']) {
            echo '<script>location.href="'.$res['data']['pdf_url'].'";setTimeout(function(){history.back()}, 10000);</script>';
            Yii::app()->end();
        }
        if ($withToolBar == 1) {
            $this->renderPartial('preview', array('data' => $res, 'withToolBar' => $withToolBar));
        } else {
            $year = $res['data']['year'];
            $html = $this->renderPartial('preview', array('data' => $res, 'withToolBar' => $withToolBar), true);
            $html = base64_encode($html);
            
            $classid = $year . '0000';
            $lang = 'en';
            $postParams = array(
                'id' => $id,
                'classid' => $classid,
                'lang' => $lang,
                'str' => $html,
                'zoom' => 1.28,
                'mac' => md5($id . $classid . $html . 'I3v4y7Onl8ine6Mi433ms')
            );
            try {
                $url = 'https://transfer.api.ivykids.cn/api/html2pdf';
                $ch = curl_init();
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
                curl_setopt($ch, CURLOPT_POST, 1);
                curl_setopt($ch, CURLOPT_URL, $url);
                curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                    'Content-Type: application/json',
                ));
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($postParams));
                $result = curl_exec($ch);
                curl_close($ch);
                if (strpos($result, 'https://transfer.api.ivykids.cn/api/download') !== false) {
                    Yii::app()->subdb->createCommand()->update('ivy_transcript_history', array('pdf_url' => $result, 'is_pdf' => 1), 'id=:id', array(':id' => $id));
                }
                echo '<script>location.href="'.$result.'";setTimeout(function(){history.back()}, 10000);</script>';
            } catch (Exception $e) {
                return false;
            }
        }
    }

    public function remote($requestUrl, $requestData = array())
    {
        if (empty($requestData['school_id'])) {
            $requestData['school_id'] = $this->branchId;
        }
        $res = CommonUtils::requestDsOnline($requestUrl, $requestData);
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('message', $res['msg']);
            $this->addMessage('data', $res['data']);
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->addMessage('data', $res['data']);
        }
        $this->showMessage();
    }
}
