<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8">
    <link rel="icon" href="/favicon.ico">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, viewport-fit=cover"/>
    <title>OA</title>
  </head>
  <body>
    
  <style>
     html,body{
        height: 100%;
        margin:0;
    }
    #bg{
        max-width: 960px;
        height: 100%;
        background: linear-gradient(180deg, #FFFFFF 0%, #EDF6FF 100%);
        box-shadow: 0px 2px 16px 0px rgba(0,0,0,0.06);
        border-radius: 16px;
        margin:0 auto;
        font-family: "Helvetica Neue",Helvetica,"PingFang SC","Hiragino Sans GB","Microsoft YaHei","微软雅黑",Arial,sans-serif;
    }
    .logo_lang{
        display:flex;
        padding-top:32px;
        align-items:center
    }
    .flex1{
        flex:1
    }
    .lang{
        padding:6px 12px;
        background: #E8F3FF;
        border-radius: 16px;
        color:#1989FA;
        cursor: pointer;
    }
    .maxWidth{
        width: 70%; 
        margin:0 auto
    }
    .logo{
        width:143px;  
    }
    .langImg{
        width:13px
    }
    .text-center{
        text-align:center
    }
    .marginimg{
        width:218px;
        margin-top:98px;
    }
    .title{
        font-size: 24px;
        font-weight: 600;
        color: #323233;
        text-align:center;
        margin-top:24px;
    }
    .notes{
        text-align:center;
        font-size: 18px;
        font-weight: 400;
        color: #323233;
        margin-bottom:52px;
        margin-top:16px;
    }
    .input{
        width: 340px;
        margin:0 auto; 
    }
    .inputCSs{
        padding: 12px 20px;
        background: #FFFFFF;
        box-shadow: 0px 2px 6px 0px rgba(0,0,0,0.04);
        border-radius: 22px;
        display:flex;
        align-items:center;
        overflow: hidden;
        font-size: 14px;
    }
    .codeText{
        font-size: 14px;
        font-weight: 400;
        color: #323233;
    }
    .text{
        margin-left:20px;
        border:none;
        width:100%;
        font-size: 14px;
    }
    .text::placeholder{
        color: #C8C9CC;
    }
    .text:focus {
        outline: none; /* 去除默认的焦点边框 */
        border-style: none; /* 设置为实线边框 */
    }
    .btnMargin{
        width:550px;
        text-align:center;
        margin:0 auto
    }
    .btn{
        width: 222px;
        height: 44px;
        background: #1989FA;
        border-radius: 22px;
        border: none;
        font-size: 14px;
        font-weight: 500;
        color: #FFFFFF;
        margin-top:50px;
        text-align:center
    }
    .bvalidator_errmsg:before {
        position: absolute;
        content: "";
        width: 0;
        height: 0;
        top: -5px;
        left: 60px;
        border-right: solid 5px transparent;
        border-left: solid 5px transparent;
        border-bottom: solid 5px #333;
    }
    .bvalidator_errmsg{
        left: 0px !important;
        position: relative;
        top: 20px !important;
        white-space: nowrap;
        font-size: 12px;
        background-color: #333;
        border: 1px solid #999;
        color: #FFF;
        padding: 4px 12px !important;
        border-radius: 4px !important;
        display:none;
        filter:alpha(opacity=90);
	    opacity:.90; 
    }
    @media (max-width: 767px) {
        #bg{
            width: 100%;
            height: 100%;
            background: linear-gradient(180deg, #FFFFFF 0%, #EDF6FF 100%);
            box-shadow: 0px 2px 16px 0px rgba(0,0,0,0.06);
            border-radius: 16px;
            margin:0 auto;
        }
        .maxWidth{
            width: 100%; 
        }
        .logo_lang{
            padding:12px 16px 6px;
        }
        .logo{
            width:110px;  
        }
        .langImg{
            width:13px
        }
        .marginimg{
            width:218px;
        }
        .marginimg{
            margin-top:16px;
        }
        .mobile{
            padding:0 32px;
        }
        .title{
            font-size: 24px;
            font-weight: 600;
            color: #323233;
            text-align:left;
            margin-top:0;
        }
        .notes{
            text-align:left;
            font-size: 16px;
            font-weight: 400;
            color: #323233;
            margin-bottom:40px;
            margin-top:16px;
        }
        .btnMargin{
            width:100%
        }
        .input{
            width:100%
        }
        .inputCSs{
            padding:12px
        }
        .btn{
            margin-top:76px
        }
    }
</style>
<div id='bg'>
    <div class='box'>

    
    <div class='maxWidth'>
        <div class='logo_lang'>
            <div class='flex1'>
                <img src="https://m2.files.ivykids.cn/cloud01-file-8025768FvmXRmPSuQ5fWHXzE6tCHwMjYuLD.png" alt="" class='logo'>
            </div>
            <span class='lang'>
                <img src="https://m2.files.ivykids.cn/cloud01-file-8025768FhX4IWfE90q4uARGZiaerEwik58K.png" alt="" class='langImg'>
                <span id='lang' onclick='switch_lang(this)'></span> 
            </span>
        </div>
        <div class='text-center'>
            <img src="https://m2.files.ivykids.cn/cloud01-file-8025768FojJWjhHd1w5OtmgFHg9z0LiVtlV.png" alt="" class='marginimg'>
        </div>
            <div class='mobile'>
                <div class='title' id='title'>
                </div>
                <div class='btnMargin'>
                <div class='notes' id='notes'></div>
                <div class='input'>
                    <div class='inputCSs flex'>
                        <span class='codeText' id='codeText'></span>
                        <div class='flex1'>
                            <input type="text" class='text' name="invite_code" id='code' placeholder='' autocomplete="off" >
                            <div style="position: absolute;">
                            <div class='bvalidator_errmsg' id='bvalidator_errmsg'></div>
                            </div>
                        </div>
                    </div>
                </div>
                    <button class='btn' onclick="submitCode()" id='submit'></button>
                </div>
            </div>
            <form method="post" action="https://oa.ivyonline.cn/survey.php" id="frm_start_surveyed" name="frm_manage">
                <input value="" name="survey_id" id="survey_id" type="hidden">
                <input value="1" name="is_anonymous" id="is_anonymous" type="hidden">
                <input value="startSurveyed" name="action" id="action" type="hidden">
                <input name="invite_code" id="invite_code" type="hidden" value=''>
            </form>
    </div>
    </div>
</div>

<script>
    var api ="https://api.ivyonline.cn/api/" ;
    var id = "<?php echo  $_GET['id'];?>"
    var cl='english'
    init()
    var surveyData={}
    function init(){
        if (getCookie('lang') == null) {
            cl = 'english';
        } else {
            cl = getCookie('lang');
        }
        const url =api+'survey/home?id='+id;
        fetch(url, {
            method: 'get',
            data:{
                id:id
            }
            })
        .then((response) => response.json()) // 将响应转换为JSON格式
        .then((data) => {
            if (data.code === 0) {
                surveyData=data.data
                
                langTab()
            } else {
            }
        })
        .catch((error) => {
            console.error('获取数据失败', error);
            // 错误处理逻辑
        });
    }
    function switch_lang(_this) {
        document.getElementById("bvalidator_errmsg").style.display = "none";
        let text=document.getElementById("lang").innerText
        if(text==='English'){
            setCookie("lang",'english');
            cl = 'english';
        }else{
            setCookie("lang",'schinese_utf8');
            cl = 'schinese_utf8';
        }
        langTab()
    }
    function langTab(){
        if(cl=='english'){
            document.title =surveyData.title_en.replace('<br>', '')
            document.getElementById("lang").innerHTML ='中文'
            document.getElementById("title").innerHTML =surveyData.title_en
            document.getElementById("notes").innerHTML ='The survey is completely anonymous and no personal information are identified or kept.'
            document.getElementById("codeText").innerHTML ='Invitation Code'
            document.getElementById("submit").innerHTML ='Start'
            var element = document.getElementById('bvalidator_errmsg').innerHTML ='Invalid invitation code'
           document.getElementById("code").placeholder = "Please Input"; 
        }else{
            document.title =surveyData.title_cn.replace('<br>', '')
            document.getElementById("lang").innerHTML ='English'
            document.getElementById("title").innerHTML =surveyData.title_cn
            document.getElementById("notes").innerHTML ='调查问卷完全以匿名形式进行，系统不会记录任何个人身份信息。'
            document.getElementById("codeText").innerHTML ='邀请码'
            document.getElementById("submit").innerHTML ='开始'
            var element = document.getElementById('bvalidator_errmsg').innerHTML ='该项不能为空或此邀请码无效'
           document.getElementById("code").placeholder = "请输入"; 
        }
    }
    function submitCode(){
        document.getElementById("bvalidator_errmsg").style.display = "none";
        const inputElement = document.getElementById('code');
        const code = inputElement.value
        if(code.length!=6){
            document.getElementById("bvalidator_errmsg").style.display = "block";
            return
        }
        const url =api+'survey/codeValidity?id='+id+'&invite_code='+code;
        fetch(url, {
            method: 'post',
            })
        .then((response) => response.json()) // 将响应转换为JSON格式
        .then((data) => {
            if (data.code === 0) {
               document.getElementById("invite_code").value  = code; 
               document.getElementById("survey_id").value  =data.data; 
               start_surveyed()
            } else {
                document.getElementById("bvalidator_errmsg").style.display = "block";
            }
        })
        .catch((error) => {
            document.getElementById("bvalidator_errmsg").style.display = "block";

        });
    }
    function start_surveyed() {
        var form = document.getElementById("frm_start_surveyed");
        form.action = "https://oa.ivyonline.cn/survey.php?id="+id;
        form.submit();
    }
  
    //  设置cookies
    function setCookie(name, value) {
        var exp = new Date();
        var date = new Date();
        date.setTime(date.getTime()+(30*24*60*60*1000));
            var expires = date.toGMTString();
        document.cookie = name + "=" + escape(value) + ";expires=" + expires + ";domain=.ivyonline.cn;path=/;";
    }
    //读取cookies
    function getCookie(name) {
        var arr, reg = new RegExp("(^| )" + name + "=([^;]*)(;|$)");
        if (arr = document.cookie.match(reg))
            return unescape(arr[2]);
        else
            return null;
    }
</script>
  </body>
</html>
