<?php

/**
 * This is the model class for table "ivy_lib_borrowing_card".
 *
 * The followings are the available columns in table 'ivy_lib_borrowing_card':
 * @property integer $id
 * @property string $cardname
 * @property integer $feetype
 * @property double $fee
 * @property integer $amount
 * @property string $usertype
 * @property string $schoolid
 * @property integer $status
 * @property integer $updatetime
 */
class BorrowingCard extends CActiveRecord {

    /**
     * Returns the static model of the specified AR class.
     * @param string $className active record class name.
     * @return BorrowingCard the static model class
     */
    public static function model($className = __CLASS__) {
        return parent::model($className);
    }

    /**
     * @return string the associated database table name
     */
    public function tableName() {
        return 'ivy_lib_borrowing_card';
    }

    /**
     * @return array validation rules for model attributes.
     */
    public function rules() {
        // NOTE: you should only define rules for those attributes that
        // will receive user inputs.
        return array(
            array('cardname', 'required'),
            array('feetype, amount, status, updatetime', 'numerical', 'integerOnly' => true),
            array('fee', 'numerical'),
            array('cardname', 'length', 'max' => 255),
            array('usertype', 'length', 'max' => 10),
            array('schoolid', 'length', 'max' => 25),
            // The following rule is used by search().
            // Please remove those attributes that should not be searched.
            array('id, cardname, feetype, fee, amount, usertype, schoolid, status, updatetime', 'safe', 'on' => 'search'),
        );
    }

    /**
     * @return array relational rules.
     */
    public function relations() {
        // NOTE: you may need to adjust the relation name and the related
        // class name for the relations automatically generated below.
        return array(
        );
    }

    /**
     * @return array customized attribute labels (name=>label)
     */
    public function attributeLabels() {
        return array(
        );
    }

    /**
     * Retrieves a list of models based on the current search/filter conditions.
     * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
     */
    public function search() {
        // Warning: Please modify the following code to remove attributes that
        // should not be searched.

        $criteria = new CDbCriteria;

        $criteria->compare('id', $this->id);
        $criteria->compare('cardname', $this->cardname, true);
        $criteria->compare('feetype', $this->feetype);
        $criteria->compare('fee', $this->fee);
        $criteria->compare('amount', $this->amount);
        $criteria->compare('usertype', $this->usertype, true);
        $criteria->compare('schoolid', $this->schoolid, true);
        $criteria->compare('status', $this->status);
        $criteria->compare('updatetime', $this->updatetime);

        return new CActiveDataProvider($this, array(
                    'criteria' => $criteria,
                ));
    }

}