<style>
    body{
        -moz-user-select:none; /*火狐*/
        -webkit-user-select:none; /*webkit浏览器*/
        -ms-user-select:none; /*IE10*/
        user-select:none;
    }
    [v-cloak] {
        display: none;
    }
    .panel-body{
        height:250px;
        overflow-y:auto
    }
    [v-cloak] {
            display: none;
        }
    .panel-footer{
        height:40px;
        font-size:16px
    }
    .panel-footer a{
        color:#000
    }
    .table > thead > tr > th, .table > tbody > tr > th, .table > tfoot > tr > th, .table > thead > tr > td, .table > tbody > tr > td, .table > tfoot > tr > td{
        border-top:0px
    }
    .menu_style{
        position: absolute;
        width: 150px;
        background-color: #fff;
        border-radius: 2px;
        box-shadow: 2px 2px 14px #d0d0d0;
    }
    .menu_style>ul{
        padding-left:0
    }
    .menu_style>ul>li{
        text-indent:10px;
        height: 38px;
        line-height: 38px;
        border-bottom: 1px dashed #f0f0f0;
        cursor: pointer;
        list-style:none
    }
    .menu_style>ul>li>span{
        margin-left:10px
    }
    .menu_style>ul>li:hover{
        background: #E0EEFF;
    }
    .bg-yellow{
        color:#F7D674
    }
    .bg{
        background:#CDE8FF
    }
    .show {
    width: 100px;
    height: 100px;
    background-color: red;
    }
    .navDiv{
        line-height: 30px;
        height: 30px;
        border: 1px solid #ddd;
        margin-bottom:1px
    }
    .navPath{
        list-style:none;
        padding-left:0px;
    }
    .navPath li{
        float:left;
        margin-right: 20px;
        position: relative;
        cursor:pointer;
    }
    .left{
        position: absolute;
        top: 50%;
        right: -8px;
        margin-top: -5px;
    }
    .left:before,.left:after{
        position: absolute;
        content: '';
        border-top: 5px transparent dashed;
        border-right: 5px transparent dashed;
        border-bottom: 5px transparent dashed;
        border-left: 5px #fff solid;
    }
    .left:before{
        border-left: 5px #ccc solid;
    }
    .left:after{
        right:-8px; /*覆盖并错开1px*/
        border-left: 5px #fff solid;
    }
    ul li {
        list-style :none
    }
    .mr2{
        margin-right:5px
    }
    .Thumbnail{
        padding-left:0;
        margin-top:10px
    }
    .Thumbnail li{
        float:left;
        width:120px;
        padding-right:10px;
        padding-left:10px;
        padding-top:10px;
        margin-right:10px;
        height:150px
    }
    .Thumbnail li:hover{
        background:#F0F0F0
    }
    .Thumbnail li dl{
        text-align:center;
        width:100px
    }
    .Thumbnail li dl dt i{
        width:100px;
        height:100px;
        font-size:100px
    }
    .Thumbnail li dl dd{
        word-break:break-all;
        letter-spacing: 0;
        overflow: hidden;
        display: -webkit-box;
        text-overflow: ellipsis;
        -webkit-line-clamp: 2;  /*要显示的行数*/
        -webkit-box-orient: vertical;
        position:static
        /* font-size:12px; */
    }
    .Thumbnail li img{
        width:100px;
        height:100px
    }
    .messagevideo{
        display:none
    }
    .glyphicon{
        position:static
    }
    .pointEvents{
        pointer-events: none;
    }
    .videoI{
        /* font-size: 50px !important;
        padding-top: 25px;
        color:#fff */
        font-size: 20px  !important;
        text-align: right  !important;
        padding-top: 75px  !important;
        padding-right: 5px  !important;
        color: #fff  !important;
    }
    /* .Transcoding i{
        font-size: 20px  !important;
        text-align: right  !important;
        padding-top: 75px  !important;
        padding-right: 5px  !important;
        color: #000  !important;
    } */
    .visibility{
        visibility:hidden
    }
    .navPot{
        position:relative;
        margin-bottom:20px
    }
    .fixedBg{
        width: 200px;
        height: 31px;
        background: #fff;
        position: absolute;
        left: 105px;
        top: 0px;
        z-index: 9;
    }
    .mr-15{
        margin-right:15px
    }
    .mt-20{
        margin-top:15px
    }
    .tab-content{
        padding-bottom:70px
    }
    #qrcodeShow canvas{
        width: 200px;
    }
</style>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site','Campus Operations'), array('/mcampus/default/index'))?></li>
        <li><?php echo Yii::t('site','Cloud Drive');?></li>
    </ol>
    <div id='container' v-clickoutside="handleClose" v-cloak>
        <div class="alert alert-warning alert-dismissible" role="alert">
            <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            <p class="text-danger"><strong>注意：</strong>请勿在云文件中上传含个人信息、隐私信息等安全要求较高的文件。</p>
            <strong>操作提示：</strong>请鼠标指向文件后点击右键出现菜单后点击操作，双击打开文件夹查看图片视频。
        </div>
        <div class='navPot'>
            <button type="button" @click='addFileData("file")' class="btn btn-primary">
                <span class='glyphicon glyphicon-plus'></span>  <?php echo Yii::t('message', "New Folder") ?>
            </button>
            <template >
                <button type="button" id="pickfilesPhoto" :class="addId==0?'btn btn-default visibility':'btn btn-default'"  @click='updata("photo")'>
                    <span class="glyphicon glyphicon-arrow-up"  aria-hidden="true"></span> <?php echo Yii::t('message', "Upload Picture") ?>
                </button>
                <button type="button" id="pickfilesVideo" :class="addId==0?'btn btn-default visibility':'btn btn-default'"  @click='updata("video")'>
                    <span class=" glyphicon glyphicon-arrow-up " aria-hidden="true"></span> <?php echo Yii::t('message', "Upload Video") ?>
                </button>
                <button type="button" id="pickfilesPDF" data-toggle="tooltip" title="其他文件仅支持pdf、mp3、zip" :class="addId==0?'btn btn-default visibility':'btn btn-default'"  @click='updata("pdf")'>
                    <span class=" glyphicon glyphicon-arrow-up " aria-hidden="true"></span> <?php echo Yii::t('message', "上传其他文件 ") ?>
                </button>
            </template>
            <button type="button" class="btn btn-primary pull-right"  @click='switchView()'>{{viewTab==1?'<?php echo Yii::t('message', "Thumbnail") ?>':'列表图'}}
            </button>
            <button type="button" class="btn btn-success pull-right mr-15" :disabled='Refreshdisabled'  @click='RefreshPage()'><?php echo Yii::t('message', "Refresh") ?></button>
            <div class='fixedBg' v-if='addId==0'></div>
        </div>
        <!-- 导航条 -->
        <ul class="nav nav-tabs" role="tablist" id='myTabs'>
            <li role="presentation" class="active"><a href="#home" aria-controls="home" role="tab" data-toggle="tab"  @click='allFiles()'><?php echo Yii::t('message', "All Files") ?></a></li>
            <li role="presentation"><a href="#history" aria-controls="history" role="tab" data-toggle="tab" @click='history()'><?php echo Yii::t('message', "New Uploads") ?></a></li>
            <li role="presentation"><a href="#myData" aria-controls="myData" role="tab" data-toggle="tab" @click='myData()'><?php echo Yii::t('message', "My Uploads") ?></a></li>
        </ul>
        <!-- 表格列表 -->
        <div class="tab-content">
            <div role="tabpanel" class="tab-pane active" id="home">
                <div class='navDiv mt20'>
                    <ol class='navPath'>
                        <li style='margin-left:10px'><i class='glyphicon glyphicon-folder-close bg-yellow'  @click='path_Jump_Top("")'></i><i class="left" ></i></li>
                        <li v-for='(list,index) in navList' @click='path_Jump(list.id)'>{{list.title}}<i v-if='index!=navList.length-1' class="left" ></i></li>
                    </ol>
                    <div style='clear:both'></div>
                </div>
                <table  class="table table-hover" v-if='viewTab==1'>
                    <thead>
                        <tr>
                            <th width='300'><?php echo Yii::t('message', "Name") ?></th>
                            <th width='100'><?php echo Yii::t('message', "Time Created") ?></th>
                            <th width='100'><?php echo Yii::t('message', "Time Updated") ?></th>
                            <th width='50'><?php echo Yii::t('message', "Media Type") ?></th>
                            <th width='50' class='text-right'><?php echo Yii::t('message', "Size") ?></th>
                            <th width='200' class='text-center'><?php echo Yii::t('message', "Uploaded by") ?></th>
                        </tr>
                    </thead>
                    <tbody>
                            <tr  v-for='(list,index) in fileLists'
                            :class="{bg:fileisactive==index}"
                            @dblclick="inonlineList(list)"
                            @click='fn(index,list.type)'
                            @contextmenu.prevent="(e)=>{
                                x_index = e.layerX;
                                y_index = e.layerY;
                                ctrlId = list;
                                type = list.type;
                                indexList = index;
                                showmenu = true;
                            }">
                                <td><i class='glyphicon glyphicon-folder-close bg-yellow mr2'></i> <a href="javascript:void(0)"> {{list.display}}</a></td>
                                <td>{{list.created_at}}</td>
                                <td>{{list.updated_at}}</td>
                                <td>文件夹</td>
                                <td>{{list.size}}</td>
                                <td class='text-center'>{{list.created_by}}</td>
                            </tr>
                            <tr v-for='(list,index) in photoLists'
                                :class="{bg:photoisactive==index}"
                                @dblclick="viewData(list.type,list)"
                                @click='fn(index,list.type)'
                                @contextmenu.prevent="(e)=>{
                                    x_index = e.layerX;
                                    y_index = e.layerY;
                                    ctrlId = list;
                                    type = list.type;
                                    indexList = index;
                                    showmenu = true;
                                }"
                                >
                                <td> <i class='glyphicon glyphicon-picture mr2'></i> {{list.display}}</td>
                                <td>{{list.created_at}}</td>
                                <td>{{list.updated_at}}</td>
                                <td>照片</td>
                                <td class='text-right'><span v-if='list.size!=""'>{{list.size}}</span></td>
                                <td class='text-center'>{{list.created_by}}</td>
                            </tr>
                            <tr v-for='(list,index) in videoLists'
                            :class="{bg:videoisactive==index}"
                                @dblclick="viewData(list.type,list)"
                                @click='fn(index,list.type)'
                                @contextmenu.prevent="(e)=>{
                                    x_index = e.layerX;
                                    y_index = e.layerY;
                                    ctrlId = list;
                                    indexList = index;
                                    type = list.type;
                                    showmenu = true;
                                }"
                                >
                                <td> <i class='glyphicon glyphicon-play-circle mr2'></i> {{list.display}}
                                 {{list.handle_status==0?'<?php echo Yii::t('message', "Convert") ?>':''}}</td>
                                <td>{{list.created_at}}</td>
                                <td>{{list.updated_at}}</td>
                                <td>视频</td>
                                <td class='text-right'><span v-if='list.size!=""'>{{list.size}}</span></td>
                                <td class='text-center'>{{list.created_by}}</td>
                            </tr>
                            <tr v-for='(list,index) in pdfLists'
                            :class="{bg:pdfisactive==index}"
                                @dblclick="viewData(list.type,list)"
                                @click='fn(index,list.type)'
                                @contextmenu.prevent="(e)=>{
                                    x_index = e.layerX;
                                    y_index = e.layerY;
                                    ctrlId = list;
                                    indexList = index;
                                    type = list.type;
                                    showmenu = true;
                                }"
                                >
                                <td> <i class='glyphicon glyphicon-list-alt'></i> {{list.display}}
                                 {{list.handle_status==0?'<?php echo Yii::t('message', "Convert") ?>':''}}</td>
                                <td>{{list.created_at}}</td>
                                <td>{{list.updated_at}}</td>
                                <td>{{list.type}}</td>
                                <td class='text-right'><span v-if='list.size!=""'>{{list.size}}</span></td>
                                <td class='text-center'>{{list.created_by}}</td>
                            </tr>
                    </tbody>
                </table>
                <ul class='Thumbnail' v-else>
                    <li v-for='(list,index) in fileLists'
                        :class="{bg:fileisactive==index}"
                        @dblclick="inonlineList(list)"
                        @click='fn(index,list.type)'
                        @contextmenu.prevent="(e)=>{
                            x_index = e.layerX;
                            y_index = e.layerY;
                            ctrlId = list;
                            type = list.type;
                            indexList = index;
                            showmenu = true;
                        }">
                        <dl :title="'标题：' + list.display+
                                '\n上传人：'+list.updated_by+
                                '\n修改日期：'+list.updated_at" >
                            <dt><i class='glyphicon glyphicon-folder-close bg-yellow'></i></dt>
                            <dd>{{list.display}}</dd>
                        </dl>
                    </li>
                    <li v-for='(list,index) in photoLists'
                    :class="{bg:photoisactive==index}"
                                @dblclick="viewData(list.type,list)"
                                @click='fn(index,list.type)'
                                @contextmenu.prevent="(e)=>{
                                    x_index = e.layerX;
                                    y_index = e.layerY;
                                    ctrlId = list;
                                    type = list.type;
                                    indexList = index;
                                    showmenu = true;
                                }">
                        <dl  :title="'标题：' + list.display+
                                '\n上传人：'+list.updated_by+
                                '\n大小：'+list.size+
                                '\n修改日期：'+list.updated_at" >
                            <dt><img :src="list.path_original+'!h100'" class="img-rounded" alt=""></dt>
                            <dd>{{list.display}}</dd>
                        </dl>
                    </li>
                    <li v-for='(list,index) in videoLists'
                    :class="{bg:videoisactive==index}"
                                @dblclick="viewData(list.type,list)"
                                @click='fn(index,list.type)'
                                @contextmenu.prevent="(e)=>{
                                    x_index = e.layerX;
                                    y_index = e.layerY;
                                    ctrlId = list;
                                    indexList = index;
                                    type = list.type;
                                    showmenu = true;
                                }">
                        <dl  :title="'标题：' + list.display+
                                '\n上传人：'+list.updated_by+
                                '\n大小：'+list.size+
                                '\n修改日期：'+list.updated_at" >
                            <dt>
                                <div v-if='list.thumbnail!=""'>
                                    <div :style="'background-image:url(' + list.thumbnail+'!h100);background-size:100% 100%;width:100px;height:100px'">
                                        <i class='glyphicon glyphicon-play-circle videoI'></i>
                                    </div>
                                </div>
                                <div v-else>
                                    <div class='Transcoding' style="background-image:url(/themes/base/images/Transcoding.png);background-size:100% 100%;width:100px;height:100px">
                                        <i class='glyphicon glyphicon-play-circle videoI'></i>
                                    </div>
                                </div>
                            </dt>
                            <dd >{{list.display}}</dd>
                        </dl>
                    </li>
                    <li v-for='(list,index) in pdfLists'
                    :class="{bg:pdfisactive==index}"
                                @dblclick="viewData(list.type,list)"
                                @click='fn(index,list.type)'
                                @contextmenu.prevent="(e)=>{
                                    x_index = e.layerX;
                                    y_index = e.layerY;
                                    ctrlId = list;
                                    indexList = index;
                                    type = list.type;
                                    showmenu = true;
                                }">
                        <dl  :title="'标题：' + list.display+
                                '\n上传人：'+list.updated_by+
                                '\n大小：'+list.size+
                                '\n修改日期：'+list.updated_at" >
                            <dt>
                                <div v-if='list.handle_status!="1"'>
                                    <div class='Transcoding' style="background-image:url(/themes/base/images/Transcoding.png);background-size:100% 100%;width:100px;height:100px">
                                    </div>
                                </div>
                                <div v-else>
                                    <div class='Transcoding' style="background-image:url(/themes/base/images/pdf.png);background-size:100% 100%;width:100px;height:100px">
                                    </div>
                                </div>
                            </dt>
                            <dd >{{list.display}}</dd>
                        </dl>
                    </li>
                    <div style='clear:both'></div>
                </ul>
                <div style='clear:both'></div>
            </div>
            <div role="tabpanel" class="tab-pane" id="history">
                <table  class="table table-hover" v-if='viewTab==1'>
                    <thead>
                        <tr>
                            <th width='300'><?php echo Yii::t('message', "Name") ?></th>
                            <th width='100'><?php echo Yii::t('message', "Time Created") ?></th>
                            <th width='100'><?php echo Yii::t('message', "Time Updated") ?></th>
                            <th width='50'><?php echo Yii::t('message', "Media Type") ?></th>
                            <th width='50' class='text-right'><?php echo Yii::t('message', "Size") ?></th>
                            <th width='200' class='text-center'><?php echo Yii::t('message', "Uploaded by") ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <template v-for='(list,index) in onlineInfoHis'>
                            <tr
                                :class="{bg:isactive==index}"
                                @dblclick="viewData(list.type,list)"
                                @click='fn(index)'
                                @contextmenu.prevent="(e)=>{
                                    x_index = e.layerX;
                                    y_index = e.layerY;
                                    ctrlId = list;
                                    type = list.type;
                                    indexList = index;
                                    showmenu = true;
                                }"
                                >
                                <td>
                                    <template v-if='list.type=="photo"'>
                                        <i class='glyphicon glyphicon-picture mr2'></i> {{list.display}}
                                    </template>
                                    <template v-else-if='list.type=="video"'>
                                        <i class='glyphicon glyphicon-play-circle mr2'></i> {{list.display}}{{list.handle_status==0?'（<?php echo Yii::t('message', "Convert") ?>）':''}}
                                    </template>
                                    <template v-else>
                                        <i class='glyphicon glyphicon-list-alt mr2'></i> {{list.display}}{{list.handle_status==0?'（<?php echo Yii::t('message', "Convert") ?>）':''}}
                                    </template>

                               </td>
                                <td>{{list.created_at}}</td>
                                <td>{{list.updated_at}}</td>
                                <td>{{list.type=='photo'?'图片':list.type=='video'?'视频':list.type}}</td>
                                <td  class='text-right'><span v-if='list.size!=""'>{{list.size}}</span></td>
                                <td class='text-center'>{{list.created_by}}</td>
                            </tr>

                        </template>
                    </tbody>
                </table>
                <ul class='Thumbnail' v-else>
                    <li v-for='(list,index) in onlineInfoHis'
                    :class="{bg:isactive==index}"
                                @dblclick="viewData(list.type,list)"
                                @click='fn(index)'
                                @contextmenu.prevent="(e)=>{
                                    x_index = e.layerX;
                                    y_index = e.layerY;
                                    ctrlId = list;
                                    type = list.type;
                                    indexList = index;
                                    showmenu = true;
                                }">
                        <dl  :title="'标题：' + list.display+
                                '\n上传人：'+list.updated_by+
                                '\n大小：'+list.size+
                                '\n修改日期：'+list.updated_at" >
                            <dt>
                                <template v-if='list.type=="photo"'>
                                    <img :src="list.path_original+'!h100'" class="img-rounded" alt="">
                                </template>
                                <template v-else-if='list.type=="video"'>

                                    <div v-if='list.thumbnail!=""'>
                                        <div :style="'background-image:url(' + list.thumbnail+'!h100);width:100px;height:100px'">
                                            <i class='glyphicon glyphicon-play-circle videoI'></i>
                                        </div>
                                    </div>
                                    <div v-else>
                                        <div class='Transcoding' style="background-image:url(/themes/base/images/Transcoding.png);background-size:100% 100%;width:100px;height:100px">
                                            <i class='glyphicon glyphicon-play-circle videoI'></i>
                                        </div>
                                    </div>
                                </template>
                                 <template v-else>
                                    <div v-if='list.handle_status!="1"'>
                                        <div class='Transcoding' style="background-image:url(/themes/base/images/Transcoding.png);background-size:100% 100%;width:100px;height:100px">
                                        </div>
                                    </div>
                                    <div v-else>
                                        <div class='Transcoding' style="background-image:url(/themes/base/images/pdf.png);background-size:100% 100%;width:100px;height:100px">
                                        </div>
                                    </div>
                                </template>
                            </dt>
                            <dd>{{list.display}}</dd>
                        </dl>
                    </li>
                </ul>
                <div style='clear:both'> </div>
                <nav aria-label="Page navigation"  v-if='newUpdataPages!=1'  class='mt-20'>
                    <ul class="pagination"  v-if='newUpdataPages!=0' >
                        <li :class='newUpdataCurrentPage==1?"disabled":""'>
                            <a href="javascript:void(0)" @click='newUpdataPrev(2)' aria-label="Previous">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>
                        <li v-for='(data,index) in newUpdataPages'  :class='newUpdataPageIndex==index?"active":""'><a href="javascript:void(0)" @click='newUpdata(data,index,2)'>{{data}}</a></li>
                        <li :class='newUpdataCurrentPage==newUpdataPages?"disabled":""'>
                            <a href="javascript:void(0)" @click='newUpdataNext(2)' aria-label="Next">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                    </ul>
                </nav>
            </div>
            <div role="tabpanel" class="tab-pane" id="myData">
                <table  class="table table-hover" v-if='viewTab==1'>
                    <thead>
                        <tr>
                            <th width='300'><?php echo Yii::t('message', "Name") ?></th>
                            <th width='100'><?php echo Yii::t('message', "Time Created") ?></th>
                            <th width='100'><?php echo Yii::t('message', "Time Updated") ?></th>
                            <th width='50'><?php echo Yii::t('message', "Media Type") ?></th>
                            <th width='50' class='text-right'><?php echo Yii::t('message', "Size") ?></th>
                            <th width='200' class='text-center'><?php echo Yii::t('message', "Uploaded by") ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <template v-for='(list,index) in onlineInfoMy'>
                            <tr
                                :class="{bg:myactive==index}"
                                @dblclick="viewData(list.type,list)"
                                @click='fn(index)'
                                @contextmenu.prevent="(e)=>{
                                    x_index = e.layerX;
                                    y_index = e.layerY;
                                    ctrlId = list;
                                    type = list.type;
                                    indexList = index;
                                    showmenu = true;
                                }"
                                >
                                <td>
                                    <template v-if='list.type=="photo"'>
                                        <i class='glyphicon glyphicon-picture mr2'></i> {{list.display}}
                                    </template>
                                    <template v-else-if='list.type=="video"'>
                                        <i class='glyphicon glyphicon-play-circle mr2'></i> {{list.display}}{{list.handle_status==0?'（<?php echo Yii::t('message', "Convert") ?>）':''}}
                                    </template>
                                    <template v-else>
                                        <i class='glyphicon glyphicon-list-alt mr2'></i> {{list.display}}{{list.handle_status==0?'（<?php echo Yii::t('message', "Convert") ?>）':''}}
                                    </template>
                                </td>
                                <td>{{list.created_at}}</td>
                                <td>{{list.updated_at}}</td>
                                <td>{{list.type=='photo'?'图片':list.type=='video'?'视频':list.type}}</td>
                                <td class='text-right'><span v-if='list.size!=""'>{{list.size}}</span></td>
                                <td class='text-center'>{{list.created_by}}</td>
                            </tr>
                        </template>
                    </tbody>
                </table>
                <ul class='Thumbnail' v-else>
                    <template  v-for='(list,index) in onlineInfoMy'>
                        <li
                            :class="{bg:myactive==index}"
                            @dblclick="viewData(list.type,list)"
                            @click='fn(index)'
                            @contextmenu.prevent="(e)=>{
                                x_index = e.layerX;
                                y_index = e.layerY;
                                ctrlId = list;
                                type = list.type;
                                indexList = index;
                                showmenu = true;
                            }">
                            <dl :title="'标题：' + list.display+
                                '\n上传人：'+list.updated_by+
                                '\n大小：'+list.size+
                                '\n修改日期：'+list.updated_at" >
                                <dt>
                                    <template v-if='list.type=="photo"'>
                                        <img :src="list.path_original+'!h100'" alt="">
                                    </template>
                                    <template v-else-if='list.type=="video"'>
                                        <div v-if='list.thumbnail!=""'>
                                            <div :style="'background-image:url(' + list.thumbnail+'!h100);width:100px;height:100px'">
                                                <i class='glyphicon glyphicon-play-circle videoI'></i>
                                            </div>
                                        </div>
                                        <div v-else>
                                            <div class='Transcoding' style="background-image:url(/themes/base/images/Transcoding.png);background-size:100% 100%;width:100px;height:100px">
                                                <i class='glyphicon glyphicon-play-circle videoI'></i>
                                            </div>
                                        </div>
                                    </template>
                                    <template v-else>
                                        <div v-if='list.handle_status!="1"'>
                                            <div class='Transcoding' style="background-image:url(/themes/base/images/Transcoding.png);background-size:100% 100%;width:100px;height:100px">
                                            </div>
                                        </div>
                                        <div v-else>
                                            <div class='Transcoding' style="background-image:url(/themes/base/images/pdf.png);background-size:100% 100%;width:100px;height:100px">
                                            </div>
                                        </div>
                                    </template>
                                </dt>
                                <dd>{{list.display}}</dd>
                            </dl>
                        </li>
                    </template>
                </ul>
                <div style='clear:both'> </div>
                <nav aria-label="Page navigation" v-if='newUpdataPages!=1' class='mt-20'>
                    <ul class="pagination" v-if='newUpdataPages!=0' >
                        <li :class='newUpdataCurrentPage==1?"disabled":""'>
                            <a href="javascript:void(0)" @click='newUpdataPrev(3)' aria-label="Previous">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>
                        <li v-for='(data,index) in newUpdataPages'  :class='newUpdataPageIndex==index?"active":""'><a href="javascript:void(0)" @click='newUpdata(data,index,3)'>{{data}}</a></li>
                        <li :class='newUpdataCurrentPage==newUpdataPages?"disabled":""'>
                            <a href="javascript:void(0)" @click='newUpdataNext(3)' aria-label="Next">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                    </ul>
                </nav>
            </div>
        </div>
        <!-- 调用组件 -->
        <right-menu :x="x_index"
                :y="y_index"
                :showMenu="showmenu"
                :type='type'
                @close="closeMenu"
                @open="openDetail"
                @copy="copyPath"
                @del="delAttr"
                @edit="editMedia"
                @qrcode="getQrcode"
                @update="updateArr">
        </right-menu>
        <!--编辑文件 -->
        <div class="modal fade bs-example-modal-lg" id="editFile" tabindex="-1" role="dialog" data-backdrop="static">
            <div class="modal-dialog" >
                <?php $form = $this->beginWidget('CActiveForm', array(
                    'id' => 'expense-forms',
                    'enableAjaxValidation' => false,
                    'action' => ($model->id) ? $this->createUrl('expenesAdd', array('expenesId' => $model->id)) : $this->createUrl('saveOnline'),
                    'htmlOptions' => array('class' => 'J_ajaxForm form-horizontal', 'role' => 'form', 'enctype' => "multipart/form-data"),
                )); ?>
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">
                        &times;
                    </button>
                        <h4 class="modal-title" id="myModalLabel">
                        <?php echo Yii::t('message', "Edit") ?>{{editData.type=='file'?'文件':editData.type=='photo'?'图片':'视频'}}
                    </h4>
                    </div>
                    <div class="modal-body">
                        <form class="form-horizontal">
                            <input type="text" :value='editData.id' name='id' class='hidden'>
                            <input type="text" value='0' name='OnlineFile[authority]' class='hidden'>
                            <input type="checkbox" value='1' name='OnlineFile[status]' class='hidden'>
                            <div class="form-group">
                                <label for="inputEmail3" class="col-sm-2 control-label"><?php echo Yii::t('message', "Name") ?></label>
                                <div class="col-sm-10">
                                <input type="text" class="form-control" :value='editData.display' name='OnlineFile[display]' placeholder="<?php echo Yii::t('message', "Name") ?>">
                                </div>
                            </div>
                            <div v-if='editData.type!="file"'>
                                <div class="form-group">
                                    <label for="inputEmail3" class="col-sm-2 control-label" class='hidden'>备注</label>
                                    <div class="col-sm-10">
                                    <textarea class="form-control" rows="3" :value='editData.introduction_cn' placeholder="备注" name='OnlineFile[introduction_cn]'></textarea>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="inputPassword3" class="col-sm-2 control-label"></label>
                                <div class="col-sm-10">
                                    <template v-if='editData.authority!="0"'>
                                        <label class="checkbox-inline">
                                            <input type="checkbox" checked="checked" v-model='editData.authority' value='1' name='OnlineFile[authority]'> 后台仅自己可见（通过URL访问不受限）
                                        </label>
                                    </template>
                                    <template v-else>
                                        <label class="checkbox-inline">
                                            <input type="checkbox" value='1' name='OnlineFile[authority]' :checked="editData.isCheck" > 后台仅自己可见（通过URL访问不受限）
                                        </label>
                                    </template>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('message', "OK") ?></button>
                        <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('message', "Close Window") ?></button>
                    </div>
                </div>
                <?php $this->endWidget(); ?>
            </div>
        </div>
        <!-- 新建文件 -->
        <div class="modal fade bs-example-modal-lg" id="addFile" tabindex="-1" role="dialog" data-backdrop="static">
            <div class="modal-dialog" >
                <?php $form = $this->beginWidget('CActiveForm', array(
                    'id' => 'expense-forms',
                    'enableAjaxValidation' => false,
                    'action' => ($model->id) ? $this->createUrl('expenesAdd', array('expenesId' => $model->id)) : $this->createUrl('saveOnline'),
                    'htmlOptions' => array('class' => 'J_ajaxForm form-horizontal', 'role' => 'form', 'enctype' => "multipart/form-data"),
                )); ?>
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">
                        &times;
                    </button>
                        <h4 class="modal-title" id="myModalLabel">
                        <?php echo Yii::t('message', "New Folder") ?>
                    </h4>
                    </div>
                    <div class="modal-body">
                        <form class="form-horizontal">
                            <input type="text" :value='addData.pid' name='pid' class='hidden'>
                            <!-- <input type="text" value='0' name='OnlineFile[authority]' class="hidden"> -->
                            <input type="text" :value='addData.type' name='OnlineFile[type]'  class='hidden'>
                            <div class="form-group">
                                <label for="inputEmail3" class="col-sm-2 control-label" class='hidden'><?php echo Yii::t('message', "Name") ?></label>
                                <div class="col-sm-10">
                                <input type="text" class="form-control" id="inputEmail3" :value='addData.display'  name='OnlineFile[display]' placeholder="<?php echo Yii::t('message', "Name") ?>">
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="inputPassword3" class="col-sm-2 control-label"></label>
                                <div class="col-sm-10">
                                    <label class="checkbox-inline">
                                        <input type="checkbox" value='1' name='OnlineFile[authority]' :checked='addData.isChecked'>后台仅自己可见（通过URL访问不受限）
                                    </label>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('message', "OK") ?></button>
                        <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('message', "Close Window") ?></button>
                    </div>
                </div>
                <?php $this->endWidget(); ?>
            </div>
        </div>
        <!-- 查看视频或图片 -->
        <div class="modal fade " id="viewVideo" tabindex="-1" role="dialog">
            <div class="modal-dialog modal-lg" >
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">
                        &times;
                    </button>
                        <h4 class="modal-title" id="myModalLabel">
                        查看
                    </h4>
                    </div>
                    <div class="modal-body">
                        <div v-if='viewType=="video"' class='text-center'>
                            <template v-if='viewUrl!=""'>
                                <video :src="viewUrl" width="560" height="480" autoplay="autoplay"  controls="controls" id="video">
                                    您的浏览器不支持 HTML5 video 标签。
                                </video>
                            </template>
                            <p v-else>视频正在转码中，请稍后刷新查看</p>
                        </div>
                        <div v-else>
                            <div class='text-center' v-if='viewUrl!=""'>
                                <img :src="viewUrl" style='max-height:500px;max-width:100%' alt="" class='img-rounded'>
                            </div>
                            <p v-else>暂不可查看</p>
                        </div>
                        <div class="input-group mt20"  v-if='viewUrl!=""'>
                            <input type="text" :value='viewUrl' class="form-control pointEvents"  placeholder="" id='inviteCode' >
                            <span class="input-group-btn">
                                <button class="btn btn-default" type="button" @click='qrcodePathModel(viewUrl)'><?php echo Yii::t('message', "QR Code") ?></button>
                                <button class="btn btn-default" type="button" @click='copyPathModel(viewUrl)'><?php echo Yii::t('lunch', "Copy") ?></button>
                            </span>
                        </div><!-- /input-group -->
                    </div>
                </div>
            </div>
        </div>
        <!-- 上传进度条 -->
        <div class="modal fade bs-example-modal-lg" id="fileprogress" tabindex="-1" role="dialog" data-backdrop="static">
            <div class="modal-dialog" >
                <div class="modal-content">
                    <div class="modal-header">
                        <!-- <button type="button" class="close" data-dismiss="modal" aria-hidden="true">
                            &times;
                        </button> -->
                        <h4 class="modal-title" id="myModalLabel">
                            进度
                        </h4>
                    </div>
                    <div class="modal-body" v-if="reFresh">
                        <div  v-for='(list,index) in files'>
                            <p>{{list.name}}</p>
                            <div class="progress">
                                <div class="progress-bar" :id='list.id' role="progressbar" aria-valuenow="60" aria-valuemin="0" aria-valuemax="100" style="width:0%;">
                                </div>
                            </div>
                        </div>
                        <p class='messagevideo'>视频已经全部上传成功！可关闭进度窗口，点击刷新当前页查看视频。</p>
                    </div>
                    <div class="modal-footer"  v-if='closeBtn'>
                        <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('message', "Close Window") ?></button>
                    </div>
                </div>
            </div>
        </div>
        <!-- 二维码 -->
        <div class="modal fade " id="qrcodeFile" tabindex="-1" role="dialog" aria-labelledby="mySmallModalLabel">
            <div class="modal-dialog modal-sm" >
                <div class="modal-content" role="document">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">
                            &times;
                        </button>
                        <h4 class="modal-title" id="myModalLabel">
                            <?php echo Yii::t('message', "QR Code") ?>
                        </h4>
                    </div>
                    <div class="modal-body" style="text-align:center">
                        <div id="qrcodeShow" style="margin-bottom: 10px"></div>
                        <div style="color: #d9534f;height: 15px;font-size: 12px">
                            <span class="glyphicon glyphicon-info-sign" style="position: relative"></span>
                            <span>
                                <?php echo Yii::t('message','Right click to save QR code as picture')?>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script type="text/x-template" id="right_menu">
    <div v-if="show"
        class="menu_style"
        :style="{top:y+'px',left:x+'px'}">
        <ul>
            <!-- 分别传递事件给父元素调用 -->
            <li v-if='type=="file"' @click="()=>{$emit('open')}"><i class='glyphicon glyphicon-folder-open'></i><span><?php echo Yii::t('message', "Open Folder") ?></span></li>
            <li  v-if='type=="file"' @click="()=>{$emit('update')}"><i class='glyphicon glyphicon-edit'></i><span><?php echo Yii::t('message', "Edit") ?></span></li>
            <li v-if='type!="file"' @click="()=>{$emit('edit')}"><i class='glyphicon glyphicon-edit'></i><span><?php echo Yii::t('message', "Edit") ?></span></li>
            <li  v-if='type!="file"'@click="()=>{$emit('copy')}"><i class='glyphicon glyphicon-copyright-mark'></i><span><?php echo Yii::t('message', "Duplicate link") ?></span> </li>
            <li  v-if='type!="file"'@click="()=>{$emit('qrcode')}"><i class='glyphicon glyphicon-copyright-mark'></i><span><?php echo Yii::t('message', "QR Code") ?></span> </li>
            <li @click="()=>{$emit('del')}"><i class='glyphicon glyphicon-trash'></i><span><?php echo Yii::t('message', "Delete") ?></span> </li>
        </ul>
    </div>
</script>

<script>
$(function () { $("[data-toggle='tooltip']").tooltip(); });
    const clickoutside = {
        // 初始化指令
        bind(el, binding, vnode) {
            function documentHandler(e) {
        // 这里判断点击的元素是否是本身，是本身，则返回
            if (el.contains(e.target)) {
                return false;
        }
        // 判断指令中是否绑定了函数
            if (binding.expression) {
        // 如果绑定了函数 则调用那个函数，此处binding.value就是handleClose方法
                binding.value(e);
            }
        }
        // 给当前元素绑定个私有变量，方便在unbind中可以解除事件监听
            el.__vueClickOutside__ = documentHandler;
            document.addEventListener('click', documentHandler);
        },
        update() {},
        unbind(el, binding) {
        // 解除事件监听
            document.removeEventListener('click', el.__vueClickOutside__);
            delete el.__vueClickOutside__;
        },
    };
    var onlineInfo = <?php echo json_encode($onlineInfo) ?>;
    var path=<?php echo json_encode($path) ?>;
    // 组件
    Vue.component('right-menu', {
        template: '#right_menu',
        props:{
            x:{
                type:[Number],
                default:0
            },
            y:{
                type:[Number],
                default:0
            },
            showmenu:{
                type:[Boolean],
                default:false
            },
            type:{
                type:[String],
                default:''
            }
        },
        data(){
            return {
                show:false,
            }
        },
        methods:{
            // 点击别处时隐藏目录，并传递一个关闭事件
            closeMenu(e){
                this.show = false;
                this.isactive = -1;
                this.myactive=-1;
                this.fileisactive = -1;
                this.photoisactive = -1;
                this.videoisactive = -1;
                this.pdfisactive=-1;
                this.$emit("close",false)
            },
        },
        mounted(){
            // 监听body上的点击
            document.querySelector("body").addEventListener("click",this.closeMenu)
        },
        beforeDestroy(){
            // 移除监听
            document.querySelector("body").removeEventListener("click",this.closeMenu)
        },
        watch:{
            // 监听，保持显示状态与父元素一致
            showmenu(val){
                this.show = false
                this.show = val;
            },
        },
    })

    var container = new Vue({
        el: "#container",
        data: {
            onlineInfo:onlineInfo,
            fileLists:[],
            photoLists:[],
            videoLists:[],
            pdfLists:[],
            files:[],
            onlineInfoHis:'',
            onlineInfoMy:'',
            x_index:0,
            y_index:0,
            type:"",
            ctrlId:'',
            indexList:'',
            showmenu:false,
            role:[],
            isactive:-1,
            myactive:-1,
            fileisactive:-1,
            photoisactive:-1,
            videoisactive:-1,
            pdfisactive:-1,
            showbox: true,
            editFile:true,
            disabled:false,
            editData:{
                id:'',
                status:'',
                display:'',
                authority:"",
                isCheck:false
            },
            addData:{
                pid:'',
                type:'',
                status:'',
                display:'',
                isChecked:false
            },
            isEdit:'',
            viewType:'',
            viewUrl:'',
            viewList:'',
            navList:[],
            addId:'',
            percentage: 0,
            percentShow: false,
            token: '',
            urls: '',
            postParams:{},
            taskData:'',
            photoList:'',
            imgUrl:'',
            photoDisplay:'',
            videoUrl:'',
            videoList:'',
            tabId:1,
            viewTab:getCookie('viewTab')==null?1:getCookie('viewTab'),
            closeBtn:false,
            Refreshdisabled:false,
            newUpdataPages:'',
            newUpdataCurrentPage:1,
            newUpdataPageIndex:0,
            uploadUrl:'',
            token:'',
            localFileInfo:[],
            putExtra:{
				fname: "",
				params: {},
				mimeType: null
			},
            resume:false,
            reFresh:true
        },
        directives: {clickoutside},
        watch:{
            // 监听，保持显示状态与父元素一致
            showmenu(newVal, oldVal){
                if(this.tabId==2){
                    if(oldVal){
                        this.isactive=-1
                    }else{
                        this.isactive =this.indexList;
                    }
                }else if(this.tabId==3){
                    if(oldVal){
                        this.myactive=-1
                    }else{
                        this.myactive =this.indexList;
                    }
                }else{
                    if(oldVal){
                        this.fileisactive=-1;
                        this.photoisactive=-1;
                        this.videoisactive=-1;
                        this.pdfisactive=-1;
                    }else{
                        if(this.type=='file'){
                            this.fileisactive=this.indexList;
                            this.photoisactive=-1;
                            this.videoisactive=-1;
                            this.pdfisactive=-1;
                        }else if(this.type=='photo'){
                            this.fileisactive=-1;
                            this.videoisactive=-1;
                            this.pdfisactive=-1;
                            this.photoisactive=this.indexList;
                        }else if(this.type=='video'){
                            this.fileisactive=-1;
                            this.photoisactive=-1;
                            this.pdfisactive=-1;
                            this.videoisactive=this.indexList;
                        }else{
                            this.fileisactive=-1;
                            this.photoisactive=-1;
                            this.videoisactive=-1;
                            this.pdfisactive=this.indexList;
                        }
                    }
                }
            },
            //监听右键变化位置
            x_index(newVal, oldVal){
                if(this.tabId==2){
                    this.isactive =this.indexList;
                }else if(this.tabId==3){
                    this.myactive =this.indexList;

                }else{
                    if(this.type=='file'){
                        this.fileisactive=this.indexList;
                        this.photoisactive=-1;
                        this.videoisactive=-1;
                        this.pdfisactive=-1;
                    }else if(this.type=='photo'){
                        this.fileisactive=-1;
                        this.videoisactive=-1;
                        this.pdfisactive=-1;
                        this.photoisactive=this.indexList;
                    }else if(this.type=='video'){
                        this.fileisactive=-1;
                        this.photoisactive=-1;
                        this.pdfisactive=-1;
                        this.videoisactive=this.indexList;
                    }else{
                        this.fileisactive=-1;
                        this.videoisactive=-1;
                        this.photoisactive=-1;
                        this.pdfisactive=this.indexList;
                    }
                }
            },
            url (val) {
                this.urls = val
            }
        },
        created:function(){
            if(getCookie('tabId')==2){
                this.history()
                $('#myTabs li:eq(1) a').tab('show')
            }else if(getCookie('tabId')==3){
                $('#myTabs li:eq(2) a').tab('show')
                this.myData()
            }else{
                $('#myTabs li:eq(0) a').tab('show')
            }
            this.dataArr()
            let that=this
            let id=that.addId==''?0:that.addId
            $.ajax({
                url: '<?php echo $this->createUrl("GetQiniuOpt")?>',
                type: "post",
                async: true,
                dataType: 'json',
                data: {
                    id:id
                },
                success: function(data) {
                    if(data.state=='success'){
                        that.taskData=data.data
                        that.postParams=data.data.xParams
                        that.uploadUrl=that.taskData['sConfs']['uploadUrl'];
                        that.$nextTick(function(){
                            let btn=[{id:'pickfilesPhoto',type:'photo'},{id:'pickfilesVideo',type:'video'},{id:'pickfilesPDF',type:'pdf'}]
                            for(var i in btn){
                                config['browse_button'] =btn[i].id;
                                config['url'] =that.taskData['sConfs']['uploadUrl'];
                                config['filters']={
                                        max_file_size :btn[i].type==='photo'? '15mb' : '6072mb',
                                        // 条件1？值1:条件2？值:值3
                                        mime_types:btn[i].type=='photo'?[{title : "<?php echo Yii::t('teaching', 'Image files');?>", extensions : "jpg,gif,png,jpeg"}]: btn[i].type=='video'?[ {title : "<?php echo Yii::t('teaching', 'Video files');?>", extensions : "mp4,avi,mpg,wmv,mov,3gp,FLV,flv"}]:[ {title : "<?php echo Yii::t('teaching', 'pdf files');?>", extensions : "pdf,mp3,zip"}]
                                    };
                                var uploader = new plupload.Uploader(config);
                                uploader.init();
                            }
                        });
                    } else{
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    }
                },
                error: function(data) {
                    resultTip({
                                error: 'warning',
                                msg:'请求错误'
                            });
                }
            });
        },
        methods: {
            //缩略图
            switchView(){
                // console.log(this.viewTab)
                if(this.viewTab==1){
                    this.viewTab=2
                    setCookie('viewTab',2)
                }else{
                    this.viewTab=1
                    setCookie('viewTab',1)
                }
            },
            //刷新当前
            RefreshPage(){
                this.Refreshdisabled=true
                // console.log(this.tabId)
                let that=this
                var data={}
                if(this.tabId==2){
                    this.history()
                }else if(this.tabId==3){
                    this.myData()
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("onlineList")?>',
                    type: "post",
                    async: true,
                    dataType: 'json',
                    data:{
                        id:that.addId==""?0:that.addId,
                    },
                    success: function(data) {
                        // console.log(data)
                        if(data.state=='success'){
                            that.onlineInfo=data.data.info
                            that.dataArr()
                            that.Refreshdisabled=false
                            resultTip({
                                msg: data.message
                            });
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                                error: 'warning',
                                msg:'请求错误'
                            });
                    }
                });
            },
            updata(type){
                // console.log(type)
                if(type=='photo'){
                    this.postParams['token'] =this.taskData.token
                    this.token=this.taskData.token
                    this.postParams['x:filetype']='photo'
                }else if(type=='video'){
                    this.postParams['token'] =this.taskData.videoToken
                    this.postParams['x:filetype']='video'
                    this.token=this.taskData.videoToken
                }else{
                    this.postParams['token'] =this.taskData.token
                    this.token=this.taskData.token
                    this.postParams['x:filetype']='pdf'
                }
            },
            //处理数据
            dataArr(){
                this.fileLists=[]
                this.photoLists=[]
                this.videoLists=[]
                this.pdfLists=[]
                for(var i=0;i<this.onlineInfo.length;i++){
                    if(this.onlineInfo[i].type=='file'){
                        this.fileLists.push(this.onlineInfo[i])
                    }else if(this.onlineInfo[i].type=='photo'){
                        this.photoLists.push(this.onlineInfo[i])
                    }else if(this.onlineInfo[i].type=='video'){
                        this.videoLists.push(this.onlineInfo[i])
                    }else{
                        this.pdfLists.push(this.onlineInfo[i])
                    }
                }
            },
            //转换大小
            bytesToSize(bytes) {
                if(bytes==null){
                    return
                }
                if (bytes === 0) return '0 B';
                var k = 1024, // or 1024
                    sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'],
                    i = Math.floor(Math.log(bytes) / Math.log(k));

                 return (bytes / Math.pow(k, i)).toPrecision(3) + ' ' + sizes[i];
            },
            load(){
                let that=this
                let id=that.addId==''?0:that.addId
                $.ajax({
                    url: '<?php echo $this->createUrl("GetQiniuOpt")?>',
                    type: "post",
                    async: true,
                    dataType: 'json',
                    data: {
                        id:id
                    },
                    success: function(data) {
                        if(data.state=='success'){
                            that.postParams={}
                            that.postParams=data.data.xParams
                            that.postParams['token'] =data.data.token
                        } else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                                error: 'warning',
                                msg:'请求错误'
                            });
                    }
                });
            },
            //切换
            allFiles(){
                this.isactive=-1;
                this.myactive=-1;
                this.fileisactive=-1;
                this.photoisactive=-1;
                this.videoisactive=-1;
                this.pdfisactive=-1;
                this.tabId=1
                setCookie('tabId',1)
            },
            //点击页数
            newUpdata(data,index,int){
                let that=this
                this.newUpdataPageIndex=index
                this.newUpdataCurrentPage=data
                var datas={}
                if(int==2){
                    datas={
                        type:'history',
                        page:that.newUpdataCurrentPage
                    }
                }else{
                    datas={
                        isUser:'1',
                        type:'history',
                        page:that.newUpdataCurrentPage
                    }
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("onlineList")?>',
                    type: "post",
                    async: true,
                    dataType: 'json',
                    data:datas,
                    success: function(data) {
                        if(int==2){
                            that.onlineInfoHis=data.data.info
                            that.newUpdataPages=data.data.total
                        }else{
                            that.onlineInfoMy=data.data.info
                            that.newUpdataPages=data.data.total
                        }
                    },
                    error: function(data) {
                        resultTip({
                                error: 'warning',
                                msg:'请求错误'
                            });
                    }
                });
            },
            //下一页
            newUpdataNext(int){
                if(this.newUpdataCurrentPage==this.newUpdataPages){
                    return
                }
                this.newUpdata(parseInt(this.newUpdataCurrentPage)+1,parseInt(this.newUpdataPageIndex)+1,int)
            },
            //上一页
            newUpdataPrev(int){
                if(this.newUpdataCurrentPage==1){
                    return
                }
                this.newUpdata(parseInt(this.newUpdataCurrentPage)-1,parseInt(this.newUpdataPageIndex)-1,int)
            },
            //最近上传
            history(){
                let that=this
                this.isactive=-1;
                this.myactive=-1;
                this.fileisactive=-1;
                this.photoisactive=-1;
                this.videoisactive=-1;
                this.pdfisactive=-1;
                this.newUpdataPageIndex=0
                this.newUpdataCurrentPage=1
                this.tabId=2
                setCookie('tabId',2)
                $.ajax({
                    url: '<?php echo $this->createUrl("onlineList")?>',
                    type: "post",
                    async: true,
                    dataType: 'json',
                    data: {
                        type:'history',
                        page:that.newUpdataCurrentPage
                    },
                    success: function(data) {
                        that.onlineInfoHis=data.data.info
                        that.newUpdataPages=data.data.total
                    },
                    error: function(data) {
                        resultTip({
                                error: 'warning',
                                msg:'请求错误'
                            });
                    }
                });
            },
            //我的上传
            myData(){
                let that=this
                this.isactive=-1;
                this.myactive=-1;
                this.fileisactive=-1;
                this.photoisactive=-1;
                this.videoisactive=-1;
                this.pdfisactive=-1;
                this.newUpdataPageIndex=0
                this.newUpdataCurrentPage=1
                this.tabId=3
                setCookie('tabId',3)
                $.ajax({
                    url: '<?php echo $this->createUrl("onlineList")?>',
                    type: "post",
                    async: true,
                    dataType: 'json',
                    data: {
                        isUser:'1',
                        type:'history',
                        page:that.newUpdataCurrentPage
                    },
                    success: function(data) {
                        that.newUpdataPages=data.data.total
                        that.onlineInfoMy=data.data.info
                    },
                    error: function(data) {
                        resultTip({
                                error: 'warning',
                                msg:'请求错误'
                            });
                    }
                });
            },
            handleClose(e) {
                this.isactive = -1;
                this.fileisactive=-1;
                this.photoisactive=-1;
                this.videoisactive=-1;
                this.pdfisactive=-1;
                this.myactive=-1;
                this.showmenu = false;
            },
            //单击事件
            fn(index,type){
                if(this.tabId==2){
                    this.isactive=index;
                }else if(this.tabId==3){
                    this.myactive=index;
                }else{
                    if(type=='file'){
                        this.fileisactive=index;
                        this.photoisactive=-1;
                        this.videoisactive=-1;
                        this.pdfisactive=-1;
                    }else if(type=='photo'){
                        this.fileisactive=-1;
                        this.videoisactive=-1;
                        this.pdfisactive=-1;
                        this.photoisactive=index;
                    }else if(type=='video'){
                        this.fileisactive=-1;
                        this.photoisactive=-1;
                        this.pdfisactive=-1;
                        this.videoisactive=index;
                    }else{
                        this.fileisactive=-1;
                        this.videoisactive=-1;
                        this.photoisactive=-1;
                        this.pdfisactive=index;
                    }
                }
            },
            //文件双击事件
            inonlineList(list){
                this.addId=list.id
                this.isactive = -1;
                this.fileisactive=-1;
                this.photoisactive=-1;
                this.videoisactive=-1;
                this.pdfisactive=-1;
                this.myactive=-1;
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("onlineList")?>',
                    type: "post",
                    async: true,
                    dataType: 'json',
                    data: {
                        id:list.id,
                    },
                    success: function(data) {
                        if(data.state=='success'){
                            that.onlineInfo=data.data.info
                            let nav={id:list.id,title:list.display}
                            that.navList.push(nav)
                            that.dataArr()
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                                error: 'warning',
                                msg:'请求错误'
                            });
                    }
                });
                this.postParams['x:pid']=list.id
            },
            //路径跳转
            path_Jump(id){
                this.addId=id
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("path")?>',
                    type: "post",
                    async: true,
                    dataType: 'json',
                    data: {
                        id:id,
                    },
                    success: function(data) {
                        if(data.state=='success'){
                            that.navList=data.data
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                                error: 'warning',
                                msg:'请求错误'
                            });
                    }
                });
                $.ajax({
                    url: '<?php echo $this->createUrl("onlineList")?>',
                    type: "post",
                    async: true,
                    dataType: 'json',
                    data: {
                        id:id,
                    },
                    success: function(data) {
                        if(data.state=='success'){
                            that.onlineInfo=data.data.info
                            that.dataArr()
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                                error: 'warning',
                                msg:'请求错误'
                            });
                    }
                });
                // this.load()
                this.postParams['x:pid']=id
            },
            //顶级目录
            path_Jump_Top(){
                let that=this
                that.addId=0
                $.ajax({
                    url: '<?php echo $this->createUrl("onlineList")?>',
                    type: "post",
                    async: true,
                    dataType: 'json',
                    data: {
                    },
                    success: function(data) {
                        if(data.state=='success'){
                            that.onlineInfo=data.data.info
                            that.navList=[]
                            that.dataArr()
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                                error: 'warning',
                                msg:'请求错误'
                            });
                    }
                });
                this.postParams['x:pid']=0
                // this.load()
            },
            //视频图片双击查看
            viewData(type,data){
                console.log(data)
                if(type!='photo' && type!='video'){
                    if(data.handle_status==1){
                        window.open(data.path_original)
                        return
                    }
                }
               let that=this
                if(type=='photo'){
                    that.viewUrl=data.path_original
                }else if(type=='video'){
                    that.viewUrl=data.path_processed
                }
                that.viewType=type
                that.viewList=data
                $('#viewVideo').on('hide.bs.modal', function () {
                    if(that.viewUrl!=''){
                        if(that.viewType=='video'){
                            $('#video').get(0).pause();
                            $('#video').get(0).currentTime=0;
                        }
                    }
                });
                $('#viewVideo').on('shown.bs.modal', function () {
                    that.$nextTick(function(){
                        if(that.viewUrl!=''){
                            if(that.viewType=='video'){
                                $('#video').get(0).play();
                            }
                        }
                    })
                })
                $('#viewVideo').modal('show')
            },
           //关闭回调
            closeMenu(state){
                this.showmenu = state;
            },
            //打开详情回调
            openDetail(){
                this.inonlineList(this.ctrlId)
            },
            //复制路径
            copyPath(){
                // console.log(this.ctrlId)
                const input = document.createElement('input')
                document.body.appendChild(input)
                if(this.ctrlId.type=='video'){
                    input.setAttribute('value',this.ctrlId.path_processed)
                }else{
                    input.setAttribute('value',this.ctrlId.path_original)
                }
                input.select();   // 选择实例内容
                document.execCommand('copy')// 执行复制
                resultTip({"msg":'已复制'})
                document.body.removeChild(input); // 删除临时实例
            },
            //弹出框复制
            copyPathModel(url){
                var oInput =$('#inviteCode')
                oInput.value = url;
                oInput.select(); // 选择对象
                document.execCommand("Copy");
                resultTip({"msg":'已复制'})
            },
            qrcodePathModel(url){
                $('#qrcodeShow').empty();
                $('#qrcodeShow').qrcode({
                    width: 400,
                    height: 400,
                    text: url,
                    background: "#efeeee",//二维码的后景色
                    foreground: "#2f2c2c"//二维码的前景色
                });
                $('#qrcodeFile').modal('show')
            },
            //删除回调
            delAttr(){
               let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("delOnline")?>',
                    type: "post",
                    async: true,
                    dataType: 'json',
                    data: {
                        id:that.ctrlId.id,
                    },
                    success: function(data) {
                        if(data.state=='success'){
                            for(var i=0;i<that.onlineInfo.length;i++){
                                if(that.onlineInfo[i].id==data.data){
                                    that.onlineInfo.splice(i,1);
                                }
                            }
                            if(that.tabId==2){
                                that.onlineInfoHis.splice(that.indexList,1);
                            }
                            if(that.tabId==3){
                                that.onlineInfoMy.splice(that.indexList,1);
                            }
                            that.dataArr()
                            resultTip({"msg": data.message})
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        that.disabled=false
                        resultTip({
                                error: 'warning',
                                msg:'请求错误'
                            });
                    }
                });
            },
            //编辑回调
            updateArr(){
                this.editData={
                    id:this.ctrlId.id,
                    status:this.ctrlId.status,
                    display:this.ctrlId.display,
                    type:this.ctrlId.type,
                    authority:this.ctrlId.authority,
                    isCheck:false
                }
                this.isEdit=1
                $('#editFile').modal('show')
            },
            //视频图片编辑
            editMedia(){
                this.editData={
                    id:this.ctrlId.id,
                    type:this.ctrlId.type,
                    status:this.ctrlId.status,
                    display:this.ctrlId.display,
                    introduction_cn:this.ctrlId.introduction_cn,
                    introduction_en:this.ctrlId.introduction_en,
                    authority:this.ctrlId.authority,
                }
                this.isEdit=1
                $('#editFile').modal('show')
            },
            getQrcode(){
                if(this.ctrlId.type=='video'){
                    var qrcodeurl = this.ctrlId.path_processed
                }else{
                    var qrcodeurl = this.ctrlId.path_original
                }
                 this.qrcodePathModel(qrcodeurl)
            },
            //新建文件
            addFileData(type){
                let that=this
                this.isEdit=0
                this.addData={
                    pid:this.addId==""?0:this.addId,
                    type:type,
                    status:'',
                    display:'',
                    isChecked:false
                }
                $('#addFile').modal('show')
            },
        }
    })
    //编辑回调
    function cbSaveOnline(data){
        container.onlineInfo=data
        container.dataArr()
        if(container.tabId==2){
            $.ajax({
                url: '<?php echo $this->createUrl("onlineList")?>',
                type: "post",
                async: true,
                dataType: 'json',
                data: {
                    type:'history',
                    page:container.newUpdataCurrentPage
                },
                success: function(data) {
                    container.onlineInfoHis=data.data.info
                    container.newUpdataPages=data.data.total
                },
                error: function(data) {
                    resultTip({
                                error: 'warning',
                                msg:'请求错误'
                            });
                }
            });
        }
        if(container.tabId==3){
            $.ajax({
                url: '<?php echo $this->createUrl("onlineList")?>',
                type: "post",
                async: true,
                dataType: 'json',
                data: {
                    isUser:'1',
                    type:'history',
                    page:container.newUpdataCurrentPage
                },
                success: function(data) {
                    container.onlineInfoMy=data.data.info
                    container.newUpdataPages=data.data.total

                },
                error: function(data) {
                    resultTip({
                                error: 'warning',
                                msg:'请求错误'
                            });
                }
            });
        }
        if(container.isEdit==0){
            $('#addFile').modal('hide')
        }else{
            $('#editFile').modal('hide')
        }
        resultTip({
            "msg": '成功'
        })
    }
    //上传视频图片配置
    var config = {
        runtimes : 'html5,flash,silverlight,html4',
        container: document.getElementById('container'), // ... or DOM Element itself
        flash_swf_url : '<?php echo Yii::app()->themeManager->baseUrl.'/base/js/plupload/Moxie.swf'?>',
        silverlight_xap_url : '<?php echo Yii::app()->themeManager->baseUrl.'/base/js/plupload/Moxie.xap'?>',
        // multi_selection: false,
        // resize : {
        //     width : 2000,
        //     height : 2000
        // },
        chunk_size: '4mb',
        init: {
            FilesAdded: function(up, files) {
                // console.log(files)
                container.resume=false
                $('.messagevideo').hide()
                container.files=files
                container.reFresh= false
                container.closeBtn=false
                container.$nextTick(()=>{
                    container.reFresh = true
                })
                $('#fileprogress').modal('show')
                // $('#'+file.id).css('width','0%').html('0%');

                up.start();
            },
            BeforeUpload: function(up, file) {
                //设置参数
                key = file.name;
				container.putExtra.params["x:name"] = key.split(".")[0];
                if(container.postParams['x:filetype']=='pdf'){
                    container.putExtra.params['x:filetype']=key.split(".")[1]
                }
                chunk_size = up.getOption("chunk_size");
                $('#'+file.id).css('width','0%').html('0%');

                var directUpload = function() {
                    // console.log(file)
                    // console.log(container.putExtra)
                    container.postParams.file=file
                    var customVarList = qiniu.filterParams(container.putExtra.params);
                    // console.log(customVarList)
                    for (var i = 0; i < customVarList.length; i++) {
                    var k = customVarList[i];
                    container.postParams[k[0]] = k[1];
                    }
                    container.postParams.key = key;
                    // console.log(container.postParams)
                    if(container.uploadUrl.indexOf("?") != -1){
                        container.uploadUrl = container.uploadUrl.split("?")[0];
                    }
                    container.resume = false;

                    up.setOption({
                        'url': container.uploadUrl,
                        'multipart': true,
                        'multipart_params': container.postParams
                    });
                };
                var resumeUpload = function() {
					blockSize = chunk_size;
					if(blockSize === 0) {
						mkFileRequest(file)
						up.stop()
						return
                    }
                    // console.log(container.postParams)
                    container.resume = true;
					var headers = qiniu.getHeadersForChunkUpload(container.token)
					up.setOption({
						url: container.uploadUrl + "/mkblk/" + blockSize,
						multipart: false,
						required_features: "chunks",
						headers: {
							Authorization: "UpToken " + container.token
						},
						multipart_params: container.postParams
					});
				};
				// 判断是否采取分片上传
				if(
					(up.runtime === "html5" || up.runtime === "flash") &&
					chunk_size
				) {
					if(file.size < chunk_size) {
						directUpload();
					} else {
						resumeUpload();
					}
				} else {
					console.log(
						"directUpload because file.size < chunk_size || is_android_weixin_or_qq()"
					);
					directUpload();
				}
            },
            ChunkUploaded:function(up, file, info){
                var res = JSON.parse(info.response);
				var leftSize = info.total - info.offset;
				var chunk_size = up.getOption && up.getOption("chunk_size");
				if(leftSize < chunk_size) {
					up.setOption({
						url: container.uploadUrl + "/mkblk/" + leftSize
					});
				}
				up.setOption({
					headers: {
						Authorization: "UpToken " + container.token
					}
				});
				var dats = {
					ctx: res.ctx,
					time: new Date().getTime(),
					offset: info.offset,
					percent: file.percent
				};

				container.localFileInfo.push(dats);
				// console.log(container.localFileInfo)
            },
            UploadProgress: function(up, file) {
                // console.log(file)
                // console.log(file.percent)
                $('#'+file.id).css('width', file.percent+'%').html(file.percent+'%');
            },
            FileUploaded: function(up, file, info) {
                var response = eval('('+info.response+')');
                // console.log(info)
                console.log(response)
                if(info.status == 200){
                    if (container.resume) {
                        mkFileRequest(file)
                    } else {
                        if(container.tabId==2){
                            // container.onlineInfoHis.unshift(response.data)
                            container.history()
                        }
                        if(container.tabId==3){
                            // container.onlineInfoMy.unshift(response.data)
                            container.myData()
                        }
                        container.onlineInfo.unshift(response.data)
                        if(response.data.type=='photo'){
                            container.photoLists.unshift(response.data)
                        }else if(response.data.type=='video'){
                            container.videoLists.unshift(response.data)
                        }else{
                            container.pdfLists.unshift(response.data)

                        }
                    }
                    // mkFileRequest(file)
                }
            },
            UploadComplete: function(up, files) {
                // console.log(files)
                // console.log(up)
                if(container.postParams['x:filetype']=='video'){
                    container.closeBtn=true
                    $('.messagevideo').show()
                }else{
                    resultTip({"msg":'已全部上传成功'})
                    $('#fileprogress').modal('hide')
                }

            },
            Error: function(up, err) {
                if(err.response){
                    var response = eval('('+err.response+')');
                }
                else{
                    var response = {message: err.message};
                }
                if(response.error == 'token not specified'){
                    up.stop();
                    $.ajax({
                        url: '<?php echo $this->createUrl("GetQiniuOpt")?>',
                        type: "post",
                        async: true,
                        dataType: 'json',
                        data: {                        },
                        success: function(data) {
                            if(data.state=='success'){
                                container.postParams={}
                                container.postParams['token'] =data.data.token
                                up.setOption("multipart_params", {"token":data.data.token,})
                                up.start();
                            } else{
                                resultTip({
                                    error: 'warning',
                                    msg: data.message
                                });
                            }
                        },
                        error: function(data) {
                            resultTip({
                                error: 'warning',
                                msg:'请求错误'
                            });
                        }
                    });
                }
                // console.log(err,up)
            }
        }
    };
    function mkFileRequest(file) { // 调用sdk的url构建函数
        console.log(file)
        var size = parseFloat(file.size)

        container.postParams['x:svr']='null'
        container.putExtra.fname=file.name
        container.postParams['key']=key
        container.putExtra.params=container.postParams
        console.log(container.putExtra)
        // console.log(container.putExtra)
        var requestUrl = qiniu.createMkFileUrl(
            container.uploadUrl,
            size,
            key,
            container.putExtra,
        );
        // console.log(requestUrl)
        var ctx = []
        // console.log(container.localFileInfo)
        for(var i = 0; i < container.localFileInfo.length; i++) {
            ctx.push(container.localFileInfo[i].ctx)
        }
        // console.log(ctx)
        // 设置上传的header信息
        var headers = qiniu.getHeadersForMkFile(container.token)
        $.ajax({
            url: requestUrl,
            type: "POST",
            headers: headers,
            data:ctx.join(","),
            success: function(res) {
                // console.log(res)
                container.localFileInfo=[]
                if(res.state='success'){

                    if(container.tabId==2){
                        // container.onlineInfoHis.unshift(response.data)
                        container.history()
                    }
                    if(container.tabId==3){
                        // container.onlineInfoMy.unshift(response.data)
                        container.myData()
                    }
                    container.onlineInfo.unshift(res.data)
                    if(res.data.type=='photo'){
                        container.photoLists.unshift(res.data)
                    }else if(res.data.type=='video'){
                        container.videoLists.unshift(res.data)
                    }else{
                        container.pdfLists.unshift(res.data)
                    }
                }

            }
        })
    }
    function isExpired(time) {
        let expireAt = time + 3600 * 24 * 1000;
        return new Date().getTime() > expireAt;
    }
    //  设置cookies
    function setCookie(name, value) {
        var exp = new Date();
        exp.setTime(exp.getTime() + 60 * 60 * 1000);
        document.cookie = name + "=" + escape(value) + ";expires=" + exp.toGMTString() + ";path=/";
    }
    //读取cookies
    function getCookie(name) {
        var arr, reg = new RegExp("(^| )" + name + "=([^;]*)(;|$)");
        if (arr = document.cookie.match(reg))
            return unescape(arr[2]);
        else
            return null;
    }
</script>
</div>
<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>
