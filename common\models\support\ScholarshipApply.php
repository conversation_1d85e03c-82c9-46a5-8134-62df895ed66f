<?php

/**
 * This is the model class for table "ivy_scholarship_apply".
 *
 * The followings are the available columns in table 'ivy_scholarship_apply':
 * @property integer $id
 * @property string $schoolid
 * @property string $startyear
 * @property integer $apply_type
 * @property string $first_name
 * @property string $last_name
 * @property integer $birthday
 * @property string $current_school
 * @property string $study_time
 * @property string $apply_grade
 * @property string $apply_grade2
 * @property string $apply_reason
 * @property string $parent_email
 * @property string $parent_phone
 * @property string $head_teacher_name
 * @property string $head_teacher_email
 * @property string $class_teacher_name
 * @property string $class_teacher_email
 * @property string $attach
 * @property integer $status
 * @property integer $ip
 * @property integer $created_at
 * @property integer $created_by
 * @property integer $updated_at
 * @property integer $updated_by
 */
class ScholarshipApply extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_scholarship_apply';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('schoolid, startyear, apply_type, first_name, last_name, birthday, current_school, study_time, apply_grade, apply_grade2, parent_email, parent_phone, ip, attach, created_at, created_by, updated_at, updated_by', 'required'),
			array('apply_type, birthday, status, created_at, created_by, updated_at, updated_by', 'numerical', 'integerOnly'=>true),
			array('schoolid, startyear, first_name, last_name, current_school, study_time, apply_grade, apply_grade2, parent_email, parent_phone, head_teacher_name, head_teacher_email, class_teacher_name, class_teacher_email, ip', 'length', 'max'=>255),
			array('apply_reason', 'safe'),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, schoolid, startyear, apply_type, first_name, last_name, birthday, current_school, study_time, apply_grade, apply_grade2, apply_reason, parent_email, parent_phone, head_teacher_name, head_teacher_email, class_teacher_name, class_teacher_email, ip, attach, status, created_at, created_by, updated_at, updated_by', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'schoolid' => 'Schoolid',
			'startyear' => 'Startyear',
			'apply_type' => 'Apply Type',
			'first_name' => 'First Name',
			'last_name' => 'Last Name',
			'birthday' => 'Birthday',
			'current_school' => 'Current School',
			'study_time' => 'Study Time',
			'apply_grade' => 'Apply Grade',
			'apply_grade2' => 'Apply Grade2',
			'apply_reason' => 'Apply Reason',
			'parent_email' => 'Parent Email',
			'parent_phone' => 'Parent Phone',
			'head_teacher_name' => 'Head Teacher Name',
			'head_teacher_email' => 'Head Teacher Email',
			'class_teacher_name' => 'Class Teacher Name',
			'class_teacher_email' => 'Class Teacher Email',
			'attach' => 'Attach',
			'status' => 'Status',
			'ip' => 'Ip',
			'created_at' => 'Created At',
			'created_by' => 'Created By',
			'updated_at' => 'Updated At',
			'updated_by' => 'Updated By',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('startyear',$this->startyear,true);
		$criteria->compare('apply_type',$this->apply_type);
		$criteria->compare('first_name',$this->first_name,true);
		$criteria->compare('last_name',$this->last_name,true);
		$criteria->compare('birthday',$this->birthday);
		$criteria->compare('current_school',$this->current_school,true);
		$criteria->compare('study_time',$this->study_time,true);
		$criteria->compare('apply_grade',$this->apply_grade,true);
		$criteria->compare('apply_grade2',$this->apply_grade2,true);
		$criteria->compare('apply_reason',$this->apply_reason,true);
		$criteria->compare('parent_email',$this->parent_email,true);
		$criteria->compare('parent_phone',$this->parent_phone,true);
		$criteria->compare('head_teacher_name',$this->head_teacher_name,true);
		$criteria->compare('head_teacher_email',$this->head_teacher_email,true);
		$criteria->compare('class_teacher_name',$this->class_teacher_name,true);
		$criteria->compare('class_teacher_email',$this->class_teacher_email,true);
		$criteria->compare('ip',$this->ip,true);
		$criteria->compare('attach',$this->attach,true);
		$criteria->compare('status',$this->status);
		$criteria->compare('created_at',$this->created_at);
		$criteria->compare('created_by',$this->created_by);
		$criteria->compare('updated_at',$this->updated_at);
		$criteria->compare('updated_by',$this->updated_by);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return ScholarshipApply the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}
