<?php

class Apps2Controller extends BranchBasedController
{
    public $showWorkflow = false;

    public function init()
    {
        parent::init();
        $this->defaultToUserBranch = true;
    }
    public function createUrl2($route, $params = array(), $ampersand = '&'){
        $branchId = $this->urlQueryParams($route);
        if(empty($branchId)){
            $params['branchId'] = $this->branchId;
        }
        return parent::createUrl($route, $params, $ampersand);
    }

    public function getOkrUrl(){
        // 获取指定的云文件
        Yii::import('common.models.online.OnlineFile');
        $okrUrl = OnlineFile::getOkrUrl();
        return CommonUtils::autoLang($okrUrl['cn'],$okrUrl['en']);
    }


    //新版首页
    public function actionIndex()
    {
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";
        $this->modernMenuFlag = 'main';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/v-calendar/vue.min.js');
        $cs->registerCoreScript('jquery.ui');
        $cs->registerCssFile($cs->getCoreScriptUrl().'/jui/css/base/jquery-ui.css');
        $cs->registerScriptFile($cs->getCoreScriptUrl().'/jui/js/jquery-ui-i18n.min.js');
        $cs->registerCssFile(Yii::app()->themeManager->baseUrl . '/base/js/element/index.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/element/index.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/jquery.qrcode.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/fullCalendar/fullcalendar.global.min.js');
        $this->render('index', array());
    }

    public function actionHomePage()
    {
        if(!empty($this->branchId)){
            $requestUrl = 'homePage/'.$this->branchId;
        }else{
            $requestUrl = 'homePage/'.$this->staff->profile->branch;
        }
        $res = CommonUtils::requestDsOnline2($requestUrl, array(), 'GET');
        if (isset($res['code']) && $res['code'] == 0 && $res['data']) {
            $data = $res['data'];
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        }
        $waitsWorkflow = $data['workDone']['waitsWorkflow']['all_total'];
        // 总部报销付款权限或者有待处理的展示工作流按钮
        $userBranch = $this->staff->profile->branch;
        $workflowAccess = false;
        if ($waitsWorkflow || in_array($userBranch, CommonUtils::LoadConfig('CfgOffice'))) {
            $workflowAccess = true;
        }
        if (!$workflowAccess) {
            unset($data['workDone']['waitsWorkflow']);
        }
        //展示课后课按钮
        if(!($data['workDone']['asa']['isAsaTeacher'] && $this->branchObj->type == 50)) {
            unset($data['workDone']['asa']);
        }
        //根据siteFlag确定签到链接
        if(isset($data['workDone']['waitsAbsent'])){;
            if (Yii::app()->params['siteFlag'] == 'daystar') {
                $data['workDone']['waitsAbsent']['url'] = $data['workDone']['waitsAbsent']['url']['daystar'];
            } else {
                $data['workDone']['waitsAbsent']['url'] = $data['workDone']['waitsAbsent']['url']['other'];
            }
        }
        $data['allBranch'] = $this->getAllBranch();
        //集团策略连接
        $data['okrUrl'] = $this->getOkrUrl();
        $this->addMessage('state', 'success');
        $this->addMessage('message', 'success');
        $this->addMessage('data', $data);
        $this->showMessage();
    }

    public function urlQueryParams($url)
    {
        $urlParts = parse_url($url);
        parse_str($urlParts['query'], $queryParams);
        if (isset($queryParams['branchId'])) {
            return $queryParams['branchId'];
        }
        return '';
    }
    /**
     * 首页校历数据
     */
    public function actionCalendar()
    {
        $school_id = Yii::app()->request->getParam('school_id', 0);
        if(empty($school_id)){
            $school_id = $this->staff->profile->branch;
        }
        $requestUrl = 'homePage/calendar/'.$school_id;
        $res = CommonUtils::requestDsOnline2($requestUrl, array(), 'GET');
        if (isset($res['code']) && $res['code'] == 0) {
            $data = $res['data'];
            $this->addMessage('state', 'success');
            $this->addMessage('message', 'success');
            $this->addMessage('data', $data);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        }
    }

    public function actionLogout()
    {
        Yii::app()->user->logout();
        $this->render("logout", array("url" => Yii::app()->user->loginUrl));
        Yii::app()->end();
    }

    public function actionShortcutLink()
    {
        $sid = Yii::app()->session->sessionId;
        $res = CommonUtils::requestDsOnline('shortcuts/links?sid='.$sid, null, 'get');
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res['data']);
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', 'Fail');
        }
        $this->showMessage();
    }

    public function actionAddShortcut()
    {
        $id = Yii::app()->request->getParam('id', 0);
        $link = Yii::app()->request->getParam('input_link', '');
        $prefixLink = Yii::app()->request->getParam('prefix_url_hide', '');
        $name = Yii::app()->request->getParam('input_name', '');

        $res = CommonUtils::requestDsOnline('shortcuts/add', array(
            'id'   => $id,
            'link' => $prefixLink.$link,
            'name' => $name,
        ));
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            if ($id == 0) {
                $this->addMessage('callback', 'cbShortcut');
            } else {
                $this->addMessage('callback', 'cbShortcut2');
            }
            $this->addMessage('message', 'Success!');
            $this->addMessage('data', array(
                'id'   => $res['data']['id'],
                'link' => base64_encode($prefixLink.$link).Yii::app()->session->sessionId,
                'name' => $name,
            ));
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
        }
        $this->showMessage();
    }

    public function actionRemoveShortcut()
    {
        $id = Yii::app()->request->getParam('id', '');

        $res = CommonUtils::requestDsOnline('shortcuts/remove', array(
            'id' => $id
        ));
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('callback', 'cbShortcut');
            $this->addMessage('message', 'Success!');
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', 'Fail');
        }
        $this->showMessage();
    }

    public function actionCalendarOneMonth()
    {
        $school_id = Yii::app()->request->getParam('school_id', 0);
        $yearMonth = Yii::app()->request->getParam('yearMonth', 0);
        if(empty($school_id)){
            $school_id = $this->staff->profile->branch;
        }
        $requestUrl = 'homePage/calendarOneMonth/'.$school_id;
        $res = CommonUtils::requestDsOnline2($requestUrl, array('yearMonth'=>$yearMonth,), 'GET');
        if (isset($res['code']) && $res['code'] == 0) {
            $data = $res['data'];
            $this->addMessage('state', 'success');
            $this->addMessage('message', 'success');
            $this->addMessage('data', $data);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        }
    }
}