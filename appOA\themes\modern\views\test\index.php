<link href="<?php echo Yii::app()->theme->baseUrl; ?>/css/admin_style.css?v<?php echo Yii::app()->params['assetsVersion'];?>" rel="stylesheet" />
<div style="margin: 0 auto;width: 1000px;">
    <ul>
    <?php foreach ($cArr as $email=>$text):?>
    <li style="line-height: 30px;">
    <?php
        echo CHtml::textField('emial', $email, array('class'=>'input length_5', 'onfocus'=>'selectall(this);'));
        echo ' ';
        echo CHtml::ajaxLink($text, '', array(
            'data' => array('email'=>$email),
            'success'=>'js:succ',
            'beforeSend'=>'js:bfs',
        ), array('style'=>$curr));
    ?>
    </li>
    <?php endforeach;?>
    </ul>
</div>
<div id="proc"></div>

<script type="text/javascript">
function bfs()
{
    $('#proc').html('<div class="pop_loading" id="J_loading"></div>');
}
function succ()
{
    $('#proc').html('');
    alert('OK');
}
function selectall(_this)
{
    $(_this).select();
}
</script>