<?php 
/*
 * <PERSON>.<PERSON>
* 2012-7-23
*
*/

?>

<?php 	if(!empty($topic->option_group)):?>
<li class="option"><?php 	foreach ($topic->optionGroup->options as $ok => $option):?>
	<div class="qOption og_<?php echo $topic->id;?>">
		<?php 

		// 多选
		if('2' == $topic->optionGroup->option_cat):?>
		<input type="checkbox" value="<?php echo $option->option_value;?>"
			name="option[<?php echo $topic->id;?>][<?php echo $option->id;?>]"
			id="option_<?php echo $topic->id;?>_<?php echo $option->id;?>"
			class="cb" topic_id="<?php echo $topic->id;?>"> <label class="cb_off"
			for="option_<?php echo $topic->id;?>_<?php echo $option->id;?>"
			id="loption_<?php echo $topic->id;?>_<?php echo $option->id;?>"> <img
			alt="" src="<?php echo $this->assetsHomeUrl;?>/images/t.gif"> <span
			class="qLabel"> <?php echo HtoolKits::getContentByLang($option->title_cn,$option->title_en);?>
		</span>
		</label>

		<?php 
		// 单选
		elseif('1' == $topic->optionGroup->option_cat):?>
		<input type="radio" value="<?php echo $option->option_value;?>"
			name="option[<?php echo $topic->id;?>]"
			id="option_<?php echo $topic->id;?>_<?php echo $option->id;?>"
			class="rb" topic_id="<?php echo $topic->id;?>"> <label class="rb_off"
			for="option_<?php echo $topic->id;?>_<?php echo $option->id;?>"
			id="loption_<?php echo $topic->id;?>_<?php echo $option->id;?>"> <img
			alt="" src="<?php echo $this->assetsHomeUrl;?>/images/t.gif"> <span
			class="qLabel"><span class="hlbl">tips </span> <?php echo HtoolKits::getContentByLang($option->title_cn,$option->title_en);?>
		</span>
		</label>
		<?php endif;?>
	</div> <?php  endforeach; ?>
	<div class="clearfix"></div>
</li>
<?php 	endif;?>
