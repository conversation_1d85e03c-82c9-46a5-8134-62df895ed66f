<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site','Campus Operations'), array('//mcampus/default/index'))?></li>
        <li><?php echo CHtml::link(Yii::t('site','Basic'), array('//mcampus/default/index'))?></li>
        <li><?php echo CHtml::link(Yii::t('site', '课后课管理'), array('//mcampus/asainvoice/index')) ?></li>
        <li class="active">课后课账单</li>
    </ol>

    <div class="row">
        <!-- 左侧菜单 -->
        <div class="col-md-2 col-sm-2 mb10">
            <?php
            $this->widget('zii.widgets.CMenu',array(
                'items'=> $this->getMenu(),
                'id'=>'pageCategory',
                'htmlOptions'=>array('class'=>'nav nav-pills nav-stacked background-gray'),
                'activeCssClass'=>'active',
                'itemCssClass'=>''
            ));
            ?>
        </div>

        <div class="col-md-10 col-sm-10">
            <a class="btn btn-default J_modal" href="<?php echo $this->createUrl('updateTeacher', array('vendor_id' => 0)); ?>">添加教师</a>
                <?php
                    $this->widget('ext.ivyCGridView.BsCGridView', array(
                        'id' => 'course-grid',
                        'afterAjaxUpdate' => 'js:head.Util.modal',
                        'dataProvider' => $teacherModel,
                        'template' => "{items}{pager}",
                        //状态为无效时标红
                        'rowCssClassExpression' => '( $data->active == 0 ? "danger" : "" )',
                        'colgroups' => array(
                            array(
                                "colwidth" => array(100, 100, 100, 100, 100, 100, 100, 100, 200),
                            )
                        ),
                        'columns' => array(
                            array(
                            'name'=>'type',
                            'value'=>'$data->getTypelist("type")',
                            ),
                            array(
                                'name' =>'名字',
                                'value' => '$data->getName()',
                            ),
                            'cellphone',
                            'email',
                            array(
                                'name' =>'状态',
                                'value' => '$data->active== 1?"有效":"无效"',
                            ),
                            array(
                                'class' => 'CButtonColumn',
                                'template' => '{show} {update} {delete}',
                                'updateButtonLabel' => Yii::t('global', 'Edit'),
                                'updateButtonUrl' => 'Yii::app()->controller->createUrl("updateTeacher",array("vendor_id"=>$data->vendor_id, "branchId"=>Yii::app()->controller->branchId))',
                                'deleteButtonUrl' => 'Yii::app()->controller->createUrl("deleteTeacher",array("vendor_id"=>$data->vendor_id, "branchId"=>Yii::app()->controller->branchId))',
                                'updateButtonImageUrl' => false,
                                'deleteButtonImageUrl' => false,
                                'updateButtonOptions' => array('class' => 'J_modal btn btn-info btn-xs mb5'),
                                'deleteButtonOptions' => array('class' => 'btn btn-danger btn-xs mb5'),
                                'buttons' => array(
                                        'show' => array(
                                            'label' => '银行',
                                            'options' => array('class' => 'J_modal btn btn-xs btn-info mb5'),
                                            'url' => 'Yii::app()->controller->createUrl("updateBankInfo", array("vendor_id"=>$data->vendor_id))',
                                        ),
                                    ),
                            ),
                        ),

                    ));
                    ?>
        </div>
    </div>
</div>

<script>
    var modal = '<div class="modal fade" id="modal" class="1123" tabindex="-1" role="dialog" aria-labelledby="modal"><div class="modal-dialog" role="document"><div class="modal-content"></div></div></div>';
    $('body').append(modal);
    function cbCourse() {
        $('#modal').modal('hide');
        $.fn.yiiGridView.update('course-grid');
    }

    var type = '<?php echo json_encode($type['type'])?>'
    var job_type = '<?php echo json_encode($type['job_type'])?>'
    let teacherdata = [];
    let courseId = undefined;
    function teacherModal(id) {
        courseId = id;
        teacherdata.splice(0, teacherdata.length);
        $('#courseId').val(id);
        let teacherval = [];
        $.ajax({
            type: "POST",
            url: "<?php echo $this->createUrl('teacherlist') ?>",
            data: {id: id},
            dataType: "json",
            success: function (data) {
                teacherval = data;
            }
        });

        for (var val of teacherval) {
            teacherdata.push(val);
        }
        $('#teachermodal').modal();

    }

/*    var teacherVue = new Vue({
        el: "#teacherVueModal",
        data: {
            teacherdata
        },
        created: function () {
            Vue.set({teacherdata})
        },
        updated: function () {
            head.Util.ajaxDel();
        },
        methods: {}
    });*/
    $('._pushteacher').on('click', function () {
        let teacher = $('#teacher option:selected').val();  //老师id
        let teacherprice = $('#teacherprice').val();       //老师金额
        let teacher_job = $('#teacher_job').val();         //老师职位
        let teacherType = $('#teacherType').val();         //老师类别
        $.ajax({
            type: "POST",
            url: "<?php echo $this->createUrl('addteacher') ?>",
            data: {teacher: teacher,courseId:courseId,teacherprice:teacherprice,teacher_job:teacher_job,teacherType:teacherType},
            dataType: "json",
            success: function (data) {
                console.log(data.data)
                teacherVue.teacherdata.push({"type": "333", "name": "333", "phonenumber": "333", "position": 22})
            }
        });
    })
    function cbTeacher() {
        //var groupId = '<?php echo $model->id; ?>';
        $('#modal').modal('hide');
        window.location.reload(true);
    }

</script>


<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>