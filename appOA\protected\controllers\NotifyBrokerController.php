<?php

/**
 * Class NotifyBrokerController
 * 为提高效率，本controller下未做权限验证；只做签名验证
 */
class NotifyBrokerController extends Controller
{

    public $pageSize = 30;
    public $demoFlag = '-demo-'; //必须需IvyOnline一致

    public function filters()
    {
        return array(
            'accessControl',
        );
    }

    public function accessRules()
    {
        return array(
            array('allow',
                'actions'=>array('qiniureceiver', 'qiniuvideopers'),
                'ips'=>array('**************','**************', '*************', '127.0.0.1'),
            ),
            array('deny',
                'actions'=>array('qiniureceiver', 'qiniuvideopers'),
            ),
        );
    }

    public function init()
    {
        parent::init();
        Yii::import('common.models.media.*');
    }

    //接受Aliyun通知过来七牛媒体文件信息
    public function actionQiniuReceiver()
    {

        if(Yii::app()->request->isPostRequest){

            $productionTableBase = 'ivy_child_media_';
            $productionTableLink = 'ivy_child_media_links_';
            $sandboxTableBase = 'ivy_child_media_sandbox_';
            $sandbox = false;

            /*
            'schoolid'
            'classid'
            'yid'
            'weeknum'
            'startyear'
            'type'
            'filename'
            'server'
            'timestamp'
            'uid'
            'isProduction'
            'isTesting'
            */

            $schoolid = $classid = $yid = $weeknum = $startyear = $type = $filename = $server = $timestamp = $uid = $isProduction = $isTesting = null;

            extract($_POST);

            $tableName = $sandboxTableBase . $startyear;
            $productionTableLink .= $startyear;
            $sandbox = true;

            if( $isProduction && !$isTesting){
                $tableName = $productionTableBase . $startyear;
                $sandbox = false;
            }

            $cacheId = 'db-mims-table-years';
            $cacheYears = Yii::app()->cache->get($cacheId);

            $creatTable = true;
            if( $cacheYears===false ){
                $creatTable = true;
            }elseif(is_array($cacheYears) && in_array($startyear, $cacheYears)){
                $creatTable = false;
            }

            if($creatTable){
                $sql = "
            CREATE TABLE IF NOT EXISTS `" . $tableName . "` (
                `id` int(10) NOT NULL AUTO_INCREMENT,
                `schoolid` varchar(30) NOT NULL,
                `classid` int(10) NOT NULL,
                `yid` int(10) NOT NULL,
                `weeknum` int(10) NOT NULL,
                `tag` text NOT NULL,
                `type` enum('photo','video','audio') NOT NULL DEFAULT 'photo',
                `filename` varchar(255) NOT NULL,
                `server` tinyint(2) NOT NULL,
                `timestamp` int(10) NOT NULL,
                `uid` int(10) NOT NULL,
                PRIMARY KEY (`id`),
                KEY `schoolid` (`schoolid`,`classid`),
                KEY `yid` (`yid`,`weeknum`),
                KEY `tag` (`tag`(333)),
                KEY `type` (`type`)
                ) ENGINE=MyISAM  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ; ";
                $sql .= "create table if not exists `".$productionTableLink."` (
                        `id` int(10) not null auto_increment,
                        `category` enum('week','activity','snotes','classreport') NOT NULL default 'week',
                        `schoolid` varchar(255) not null,
                        `classid` int(10) not null,
                        `childid` int(10) not null,
                        `yid` int(10) not null,
                        `weeknum` int(10) not null,
                        `itemid` int(10) not null,
                        `pid` int(10) not null,
                        `title` varchar(255) not null,
                        `content` text,
                        `weight` int(10) not null DEFAULT  '0',
                        `updated_timestamp` int(10) not null,
                        `userid` int(10) not null,
                        primary key (`id`),
                        KEY `category`(`category`),
                        KEY `pid`(`pid`),
                        KEY `weight`(`weight`),
						KEY `classid` (`classid`),
						KEY `yid` (`yid`),
						KEY `childid` (`childid`),
						KEY `weeknum` (`weeknum`),
						KEY `schoolid` (`schoolid`)
                        ) engine = myisam;";

                Yii::app()->db->createCommand($sql)->execute();

                $cacheYears[] = $startyear;
                Yii::app()->cache->set($cacheId, $cacheYears);
            }

            $mediaModel = ChildMedia::model();
            $mediaModel->setOptions($sandbox, $startyear);

            $media = new $mediaModel;
            $attrs = array(
                'schoolid' => $schoolid,
                'classid' => $classid,
                'yid' => $yid,
                'weeknum' => $weeknum,
                'type' => $type,
                'filename' => $filename,
                'tag' => '',
                'server' => $server,
                'timestamp' => time(),
                'uid' => $uid
            );

            $media->setAttributes( $attrs );
            if ($media->save() ){
//                Yii::log($type);

                //如果是视频，写入视频记录表
                if($type == 'video'){
                    Yii::import('common.models.media.*');
//                    Yii::log($filename);
                    $video = QiniuVideo::model()->findByPk($filename);
                    if(is_null($video)) $video = new QiniuVideo;
                    if($video->isNewRecord){
                        $video->inputKey = $filename;
                        $video->server_id = $server;
                        $video->startyear = $startyear;
                        $video->created = time();
                        $_items = array($media->id);
                    }else{
                        $_items = unserialize($video->itemids);
                        if(!in_array($media->id, $_items)){
                            $_items[] = $media->id;
                        }
                    }
                    $video->itemids = serialize($_items);
                    if(!$video->save()){
                        Yii::log(print_r($video->getErrors(),true));
                    }
                }

                echo CJSON::encode(array('code'=>'success','id'=>$media->id));
            }else{
                echo CJSON::encode(array('code'=>'fail'));
                Yii::log(print_r($media->getErrors(), true));
            }
        }
    }

    public function actionQiniuVideoPers(){
        $notifyData = $_REQUEST;

        //兼容node.js请求过来的json数据
        if(!$notifyData)
            $notifyData = CJSON::decode(file_get_contents('php://input'));
//        Yii::log('VIDEO-PERS: '. CJSON::encode($notifyData));
        if(isset($notifyData['inputKey'])){
            Yii::import('common.models.media.*');
            $video = QiniuVideo::model()->findByPk( $notifyData['inputKey'] );
            if(!empty($video)){
                $video->notifyData = CJSON::encode($notifyData);
                $video->notified = time();

                //process video data
                if(strpos( $notifyData['inputKey'], $this->demoFlag ) === false){
                    $sandbox = false;
                }else{
                    $sandbox = true;
                }
                $mediaModel = ChildMedia::model();
                $mediaModel->setOptions($sandbox, $video->startyear);

                $mids = unserialize($video->itemids);
                if(count($mids)){
                    krsort($notifyData['items']);
                    //base Key;
                    $finalNames[] = substr($notifyData['inputKey'], strrpos($notifyData['inputKey'],'/')+1);

                    //part - screenshot
                    if($notifyData['code'] == 3){
                        $finalNames[] = 'videoTmp.jpg';
                    }else{
                        $finalNames[] = substr($notifyData['items']['vframe'], 0, strpos($notifyData['items']['vframe'], '=/'));
                    }

                    //part - video
                    $finalNames[] = substr($notifyData['items']['avthumb'], 0, strpos($notifyData['items']['avthumb'], '=/'));

                    $nameStr = implode(';;', $finalNames);
                    $mediaModel::model()->updateByPk($mids, array('filename'=>$nameStr));
                }
                //process end

                $video->notify_done = time();
                $video->save();
            }
        }
        echo CJSON::encode(array('itemids'=>$video->itemids));
        Yii::app()->end();
    }

    public $authTime = 86400; //授权持续一天

    /**
     * 调用远程媒体数据库使用；暂不使用
     */
    public function actionfetchWeeklyMediaJS(){
        $token = $_GET['token'];
        $page = intval($_GET['page']);
        $params = array(
            'startYear' => intval($_GET['startYear']),
            'branchId' => $_GET['branchId'],
            'weekNumber' => intval($_GET['weekNumber']),
            'classId' => intval($_GET['classId']),
            'time' => intval($_GET['time']),
            'domainId' => intval($_GET['domainId']),
            'childId' => intval($_GET['childId']),
            'freeTag' => $_GET['freeTag'],
        );
        ksort($params);

        $myToken = md5( sprintf('%s&%s&%s&%s', $params['branchId'], $params['startYear'], $params['classId'], $this->securityKey) );
        if( $token == $myToken && abs( time() - $_GET['time'] ) <= $this->authTime ){

            $ch = curl_init();
            $params['token'] = $token;
            $postData = $params;

//            curl_setopt($ch, CURLOPT_URL, 'http://www.ivynotify.cn/media/fetchWeekly?page='.$page);
            curl_setopt($ch, CURLOPT_URL, 'http://**************/media/fetchWeekly?page='.$page);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER , 1);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            $result = curl_exec($ch);

            $this->renderPartial('weeklymedia', array('contentData'=>$result));
        }
    }

    public function actionTest(){
        $cacheId = 'db-mims-table-years';

        $deleteCache = Yii::app()->request->getParam('deleteCache',0);
        if($deleteCache){
            Yii::app()->cache->delete($cacheId);
        }

        $cacheYears = Yii::app()->cache->get($cacheId);
        print_r($cacheYears);
        Yii::app()->end();
    }

    public function actionFetchWeekly()
    {

        $mediaData = array();
        $pageData = array();

        $token = $_REQUEST['token'];
        //$page = $_REQUEST['page'];
        $params = array(
            'startYear' => intval($_REQUEST['startYear']),
            'branchId' => $_REQUEST['branchId'],
            'weekNumber' => intval($_REQUEST['weekNumber']),
            'classId' => intval($_REQUEST['classId']),
            'time' => intval($_REQUEST['time']),
            'domainId' => intval($_REQUEST['domainId']),
            'childId' => intval($_REQUEST['childId']),
            'type' => $_REQUEST['type'],
            'freeTag' => $_REQUEST['freeTag'],
            'callbackVarName' => Yii::app()->request->getParam('callbackVarName', 'contentData'),
        );

        ksort($params);
        $myToken = md5( sprintf('%s&%s&%s&%s', $params['branchId'], $params['startYear'], $params['classId'], $this->securityKey) );
        if($token == $myToken && abs( time() - $_REQUEST['time'] ) <= $this->authTime ){
            $cond = 'schoolid=:schoolid and classid=:classid';
            $condParams = array(
                ':schoolid' => $params['branchId'],
                ':classid'  => $params['classId'],
            );
            if($params['weekNumber']){
                $cond .= " and weeknum=:weeknum";
                $condParams[':weeknum'] = $params['weekNumber'];
            }
            if($params['childId']){
                $cond .= " and tag like :childid";
                $condParams[':childid'] = '%sc'.$params['classId'].'_'.$params['childId'].'_e%';
            }
            if($params['domainId']){
                $cond .= " and tag like :domainId";
                $condParams[':domainId'] = '%sl_'.$params['domainId'].'_e%';
            }
            if($params['freeTag']){
                $cond .= " and tag like :freeTag";
                $condParams[':freeTag'] = '%'.$params['freeTag'].'%';
            }
            if($params['type']){
                $cond .= " and type = :type";
                $condParams[':type'] = $params['type'];
            }

            $countRow = Yii::app()->db->createCommand()
                ->select( 'count(*) as total' )
                ->from('ivy_child_media_'.$params['startYear'])
                ->where($cond, $condParams )
                ->queryRow();
            $total = $countRow['total'];

            $pages=new CPagination($total);
            $pages->pageSize = $this->pageSize;
            $pages->getOffset();


            if($total){
                $medias = Yii::app()->db->createCommand()
                    ->from('ivy_child_media_'.$params['startYear'])
                    ->where($cond, $condParams )
                    ->limit( $this->pageSize )
                    ->offset( $pages->getOffset() )
                    ->order('timestamp DESC')
                    ->queryAll();
                foreach($medias as $media){
                    $mediaData[$media['id']] = $media;
                    $mediaData[$media['id']]['url'] = CommonUtils::getMediaUrl(true, $media['server'], $media['type'], $media['filename'], $media['schoolid'], $media['classid'], $media['weeknum']);
                    $mediaData[$media['id']]['_url'] = CommonUtils::getMediaUrl(false, $media['server'], $media['type'], $media['filename'], $media['schoolid'], $media['classid'], $media['weeknum']);
                }
            }

            $pageData = array(
                'itemCount' => $pages->itemCount,
                'currentPage' => $pages->currentPage,
                'pageCount' => $pages->pageCount,
                'pageSize' => $pages->pageSize,
            );

            header('Content-Type: text/javascript; charset=UTF-8');
            echo sprintf(
                '%s = %s;',
                $params['callbackVarName'],
                CJSON::encode(array('mediaData'=>$mediaData, 'pageData'=>$pageData))
            );
        }
    }
}