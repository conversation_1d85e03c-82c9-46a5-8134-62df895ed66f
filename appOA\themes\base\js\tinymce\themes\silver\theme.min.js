/**
 * Copyright (c) Tiny Technologies, Inc. All rights reserved.
 * Licensed under the LGPL or a commercial license.
 * For LGPL see License.txt in the project root for license information.
 * For commercial licenses see https://www.tiny.cloud/
 *
 * Version: 5.1.6 (2020-01-28)
 */
!function(v){"use strict";function Z(){}function i(e,o){return function(){for(var n=[],t=0;t<arguments.length;t++)n[t]=arguments[t];return e(o.apply(null,n))}}function l(n){return n}var nn=function(n){return function(){return n}};function d(o){for(var r=[],n=1;n<arguments.length;n++)r[n-1]=arguments[n];return function(){for(var n=[],t=0;t<arguments.length;t++)n[t]=arguments[t];var e=r.concat(n);return o.apply(null,e)}}function b(e){return function(){for(var n=[],t=0;t<arguments.length;t++)n[t]=arguments[t];return!e.apply(null,n)}}function r(n){return function(){throw new Error(n)}}var u=nn(!1),a=nn(!0),n=tinymce.util.Tools.resolve("tinymce.ThemeManager"),N=function(){return(N=Object.assign||function(n){for(var t,e=1,o=arguments.length;e<o;e++)for(var r in t=arguments[e])Object.prototype.hasOwnProperty.call(t,r)&&(n[r]=t[r]);return n}).apply(this,arguments)};function c(n,t){var e={};for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&t.indexOf(o)<0&&(e[o]=n[o]);if(null!=n&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(n);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(n,o[r])&&(e[o[r]]=n[o[r]])}return e}function g(){for(var n=0,t=0,e=arguments.length;t<e;t++)n+=arguments[t].length;var o=Array(n),r=0;for(t=0;t<e;t++)for(var i=arguments[t],u=0,a=i.length;u<a;u++,r++)o[r]=i[u];return o}function t(){return s}var e,s=(e={fold:function(n,t){return n()},is:u,isSome:u,isNone:a,getOr:m,getOrThunk:f,getOrDie:function(n){throw new Error(n||"error: getOrDie called on none.")},getOrNull:nn(null),getOrUndefined:nn(undefined),or:m,orThunk:f,map:t,each:Z,bind:t,exists:u,forall:a,filter:t,equals:o,equals_:o,toArray:function(){return[]},toString:nn("none()")},Object.freeze&&Object.freeze(e),e);function o(n){return n.isNone()}function f(n){return n()}function m(n){return n}function p(t){return function(n){return function(n){if(null===n)return"null";var t=typeof n;return"object"==t&&(Array.prototype.isPrototypeOf(n)||n.constructor&&"Array"===n.constructor.name)?"array":"object"==t&&(String.prototype.isPrototypeOf(n)||n.constructor&&"String"===n.constructor.name)?"string":t}(n)===t}}function h(n,t){if(fn(n)){for(var e=0,o=n.length;e<o;++e)if(!0!==t(n[e]))return!1;return!0}return!1}function y(n,t){return pn.call(n,t)}function x(n,t){for(var e=0,o=n.length;e<o;e++){if(t(n[e],e))return!0}return!1}function w(n,t){for(var e=[],o=0;o<n.length;o+=t){var r=gn.call(n,o,o+t);e.push(r)}return e}function S(n,t){for(var e=n.length,o=new Array(e),r=0;r<e;r++){var i=n[r];o[r]=t(i,r)}return o}function C(n,t){for(var e=[],o=0,r=n.length;o<r;o++){var i=n[o];t(i,o)&&e.push(i)}return e}function k(n,t,e){return function(n,t){for(var e=n.length-1;0<=e;e--){t(n[e],e)}}(n,function(n){e=t(e,n)}),e}function O(n,t,e){return bn(n,function(n){e=t(e,n)}),e}function E(n,t){for(var e=0,o=n.length;e<o;e++){var r=n[e];if(t(r,e))return on.some(r)}return on.none()}function T(n,t){for(var e=0,o=n.length;e<o;e++){if(t(n[e],e))return on.some(e)}return on.none()}function H(n){for(var t=[],e=0,o=n.length;e<o;++e){if(!fn(n[e]))throw new Error("Arr.flatten item "+e+" was not an array, input: "+n);hn.apply(t,n[e])}return t}function B(n,t){var e=S(n,t);return H(e)}function D(n,t){for(var e=0,o=n.length;e<o;++e){if(!0!==t(n[e],e))return!1}return!0}function A(n){var t=gn.call(n,0);return t.reverse(),t}function _(n,t){return C(n,function(n){return!vn(t,n)})}function M(n){return[n]}function F(n){return 0===n.length?on.none():on.some(n[n.length-1])}function P(n,e){return kn(n,function(n,t){return{k:t,v:e(n,t)}})}function I(n,t){for(var e=wn(n),o=0,r=e.length;o<r;o++){var i=e[o],u=n[i];if(t(u,i,n))return on.some(u)}return on.none()}function R(n){return On(n,function(n){return n})}function V(n,t){return En(n,t)?on.from(n[t]):on.none()}function z(u){return function(){for(var n=new Array(arguments.length),t=0;t<n.length;t++)n[t]=arguments[t];if(0===n.length)throw new Error("Can't merge zero objects");for(var e={},o=0;o<n.length;o++){var r=n[o];for(var i in r)Bn.call(r,i)&&(e[i]=u(e[i],r[i]))}return e}}function L(e){var o,r=!1;return function(){for(var n=[],t=0;t<arguments.length;t++)n[t]=arguments[t];return r||(r=!0,o=e.apply(null,n)),o}}function j(n){return _n.defaultedThunk(nn(n))}function U(t){return function(n){return En(n,t)?on.from(n[t]):on.none()}}function W(n,t){return U(t)(n)}function G(n,t){var e={};return e[n]=t,e}function X(n,t){return function(n,e){var o={};return Cn(n,function(n,t){vn(e,t)||(o[t]=n)}),o}(n,t)}function Y(n,t){return function(t,e){return function(n){return En(n,t)?n[t]:e}}(n,t)}function q(n,t){return G(n,t)}function K(n){return function(n){var t={};return bn(n,function(n){t[n.key]=n.value}),t}(n)}function J(n,t){var e=function(n){var t=[],e=[];return bn(n,function(n){n.fold(function(n){t.push(n)},function(n){e.push(n)})}),{errors:t,values:e}}(n);return 0<e.errors.length?function(n){return an.error(H(n))}(e.errors):function(n,t){return 0===n.length?an.value(t):an.value(Dn(t,An.apply(undefined,n)))}(e.values,t)}function $(n,t){return function(n,t){return En(n,t)&&n[t]!==undefined&&null!==n[t]}(n,t)}var Q,tn,en=function(e){function n(){return r}function t(n){return n(e)}var o=nn(e),r={fold:function(n,t){return t(e)},is:function(n){return e===n},isSome:a,isNone:u,getOr:o,getOrThunk:o,getOrDie:o,getOrNull:o,getOrUndefined:o,or:n,orThunk:n,map:function(n){return en(n(e))},each:function(n){n(e)},bind:t,exists:t,forall:t,filter:function(n){return n(e)?r:s},toArray:function(){return[e]},toString:function(){return"some("+e+")"},equals:function(n){return n.is(e)},equals_:function(n,t){return n.fold(u,function(n){return t(e,n)})}};return r},on={some:en,none:t,from:function(n){return null===n||n===undefined?s:en(n)}},rn=function(e){return{is:function(n){return e===n},isValue:a,isError:u,getOr:nn(e),getOrThunk:nn(e),getOrDie:nn(e),or:function(n){return rn(e)},orThunk:function(n){return rn(e)},fold:function(n,t){return t(e)},map:function(n){return rn(n(e))},mapError:function(n){return rn(e)},each:function(n){n(e)},bind:function(n){return n(e)},exists:function(n){return n(e)},forall:function(n){return n(e)},toOption:function(){return on.some(e)}}},un=function(e){return{is:u,isValue:u,isError:a,getOr:l,getOrThunk:function(n){return n()},getOrDie:function(){return r(String(e))()},or:function(n){return n},orThunk:function(n){return n()},fold:function(n,t){return n(e)},map:function(n){return un(e)},mapError:function(n){return un(n(e))},each:Z,bind:function(n){return un(e)},exists:u,forall:a,toOption:on.none}},an={value:rn,error:un,fromOption:function(n,t){return n.fold(function(){return un(t)},rn)}},cn=p("string"),sn=p("object"),fn=p("array"),ln=p("boolean"),dn=p("function"),mn=p("number"),gn=Array.prototype.slice,pn=Array.prototype.indexOf,hn=Array.prototype.push,vn=function(n,t){return-1<y(n,t)},bn=function(n,t){for(var e=0,o=n.length;e<o;e++){t(n[e],e)}},yn=function(n){return 0===n.length?on.none():on.some(n[0])},xn=dn(Array.from)?Array.from:function(n){return gn.call(n)},wn=Object.keys,Sn=Object.hasOwnProperty,Cn=function(n,t){for(var e=wn(n),o=0,r=e.length;o<r;o++){var i=e[o];t(n[i],i)}},kn=function(n,o){var r={};return Cn(n,function(n,t){var e=o(n,t);r[e.k]=e.v}),r},On=function(n,e){var o=[];return Cn(n,function(n,t){o.push(e(n,t))}),o},En=function(n,t){return Sn.call(n,t)},Tn=function(u){if(!fn(u))throw new Error("cases must be an array");if(0===u.length)throw new Error("there must be at least one case");var a=[],e={};return bn(u,function(n,o){var t=wn(n);if(1!==t.length)throw new Error("one and only one name per case");var r=t[0],i=n[r];if(e[r]!==undefined)throw new Error("duplicate key detected:"+r);if("cata"===r)throw new Error("cannot have a case named cata (sorry)");if(!fn(i))throw new Error("case arguments must be an array");a.push(r),e[r]=function(){var n=arguments.length;if(n!==i.length)throw new Error("Wrong number of arguments to case "+r+". Expected "+i.length+" ("+i+"), got "+n);for(var e=new Array(n),t=0;t<e.length;t++)e[t]=arguments[t];return{fold:function(){if(arguments.length!==u.length)throw new Error("Wrong number of arguments to fold. Expected "+u.length+", got "+arguments.length);return arguments[o].apply(null,e)},match:function(n){var t=wn(n);if(a.length!==t.length)throw new Error("Wrong number of arguments to match. Expected: "+a.join(",")+"\nActual: "+t.join(","));if(!D(a,function(n){return vn(t,n)}))throw new Error("Not all branches were specified when using match. Specified: "+t.join(", ")+"\nRequired: "+a.join(", "));return n[r].apply(null,e)},log:function(n){v.console.log(n,{constructors:a,constructor:r,params:e})}}}}),e},Bn=Object.prototype.hasOwnProperty,Dn=z(function(n,t){return sn(n)&&sn(t)?Dn(n,t):t}),An=z(function(n,t){return t}),_n=Tn([{strict:[]},{defaultedThunk:["fallbackThunk"]},{asOption:[]},{asDefaultedOptionThunk:["fallbackThunk"]},{mergeWithThunk:["baseThunk"]}]),Mn=_n.strict,Fn=_n.asOption,In=_n.defaultedThunk,Rn=_n.mergeWithThunk,Vn=(Tn([{bothErrors:["error1","error2"]},{firstError:["error1","value2"]},{secondError:["value1","error2"]},{bothValues:["value1","value2"]}]),function(n){return U(n)}),Nn=function(n,t){return W(n,t)};(tn=Q=Q||{})[tn.Error=0]="Error",tn[tn.Value=1]="Value";function Hn(n,t,e){return n.stype===Q.Error?t(n.serror):e(n.svalue)}function Pn(n){return{stype:Q.Value,svalue:n}}function zn(n){return{stype:Q.Error,serror:n}}function Ln(n){return i(qt,H)(n)}function jn(n){return sn(n)&&100<wn(n).length?" removed due to size":JSON.stringify(n,null,2)}function Un(n,t){return qt([{path:n,getErrorInfo:t}])}function Wn(n,t,e){return W(t,e).fold(function(){return function(n,t,e){return Un(n,function(){return'Could not find valid *strict* value for "'+t+'" in '+jn(e)})}(n,e,t)},Xt)}function Gn(n,t,e){var o=W(n,t).fold(function(){return e(n)},l);return Xt(o)}function Xn(u,a,n,c){return n.fold(function(o,e,n,r){function i(n){var t=r.extract(u.concat([o]),c,n);return $t(t,function(n){return G(e,c(n))})}function t(n){return n.fold(function(){var n=G(e,c(on.none()));return Xt(n)},function(n){var t=r.extract(u.concat([o]),c,n);return $t(t,function(n){return G(e,c(on.some(n)))})})}return n.fold(function(){return Kt(Wn(u,a,o),i)},function(n){return Kt(Gn(a,o,n),i)},function(){return Kt(function(n,t){return Xt(W(n,t))}(a,o),t)},function(n){return Kt(function(t,n,e){var o=W(t,n).map(function(n){return!0===n?e(t):n});return Xt(o)}(a,o,n),t)},function(n){var t=n(a),e=$t(Gn(a,o,nn({})),function(n){return Dn(t,n)});return Kt(e,i)})},function(n,t){var e=t(a);return Xt(G(n,c(e)))})}function Yn(o){return{extract:function(t,n,e){return Jt(o(e,n),function(n){return function(n,t){return Un(n,function(){return t})}(t,n)})},toString:function(){return"val"},toDsl:function(){return te.itemOf(o)}}}function qn(n){var i=re(n),u=k(n,function(t,n){return n.fold(function(n){return Dn(t,q(n,!0))},nn(t))},{});return{extract:function(n,t,e){var o=ln(e)?[]:function(t){var n=wn(t);return C(n,function(n){return $(t,n)})}(e),r=C(o,function(n){return!$(u,n)});return 0===r.length?i.extract(n,t,e):function(n,t){return Un(n,function(){return"There are unsupported fields: ["+t.join(", ")+"] specified"})}(n,r)},toString:i.toString,toDsl:i.toDsl}}function Kn(r){return{extract:function(e,o,n){var t=S(n,function(n,t){return r.extract(e.concat(["["+t+"]"]),o,n)});return ne(t)},toString:function(){return"array("+r.toString()+")"},toDsl:function(){return te.arrOf(r)}}}function Jn(i,u){return{extract:function(e,o,r){var n=wn(r),t=function(n,t){return Kn(Yn(i)).extract(n,l,t)}(e,n);return Kt(t,function(n){var t=S(n,function(n){return oe.field(n,n,Mn(),u)});return re(t).extract(e,o,r)})},toString:function(){return"setOf("+u.toString()+")"},toDsl:function(){return te.setOf(i,u)}}}function $n(t,e,o,n,r){return Nn(n,r).fold(function(){return function(n,t,e){return Un(n,function(){return'The chosen schema: "'+e+'" did not exist in branches: '+jn(t)})}(t,n,r)},function(n){return n.extract(t.concat(["branch: "+r]),e,o)})}function Qn(n,r){return{extract:function(t,e,o){return Nn(o,n).fold(function(){return function(n,t){return Un(n,function(){return'Choice schema did not contain choice key: "'+t+'"'})}(t,n)},function(n){return $n(t,e,o,r,n)})},toString:function(){return"chooseOn("+n+"). Possible values: "+wn(r)},toDsl:function(){return te.choiceOf(n,r)}}}function Zn(t){return Yn(function(n){return t(n).fold(qt,Xt)})}function nt(t,n){return Jn(function(n){return Wt(t(n))},n)}function tt(n,t,e){return Gt(function(n,t,e,o){var r=t.extract([n],e,o);return Qt(r,function(n){return{input:o,errors:n}})}(n,t,l,e))}function et(n){return n.fold(function(n){throw new Error(le(n))},l)}function ot(n,t,e){return et(tt(n,t,e))}function rt(n,t){return Qn(n,t)}function it(n,t){return Qn(n,P(t,re))}function ut(e,o){return Yn(function(n){var t=typeof n;return e(n)?Xt(n):qt("Expected type: "+o+" but got: "+t)})}function at(t){return Zn(function(n){return vn(t,n)?an.value(n):an.error('Unsupported value: "'+n+'", choose one of "'+t.join(", ")+'".')})}function ct(n){return ce(n,n,Mn(),ie())}function st(n,t){return ce(n,n,Mn(),t)}function ft(n){return st(n,ge)}function lt(n,t){return ce(n,n,Mn(),at(t))}function dt(n){return st(n,he)}function mt(n,t){return ce(n,n,Mn(),re(t))}function gt(n,t){return ce(n,n,Mn(),ue(t))}function pt(n,t){return ce(n,n,Mn(),Kn(t))}function ht(n){return ce(n,n,Fn(),ie())}function vt(n,t){return ce(n,n,Fn(),t)}function bt(n){return vt(n,me)}function yt(n){return vt(n,ge)}function xt(n){return vt(n,he)}function wt(n,t){return vt(n,re(t))}function St(n,t){return ce(n,n,j(t),ie())}function Ct(n,t,e){return ce(n,n,j(t),e)}function kt(n,t){return Ct(n,t,me)}function Ot(n,t){return Ct(n,t,ge)}function Et(n,t,e){return Ct(n,t,at(e))}function Tt(n,t){return Ct(n,t,pe)}function Bt(n,t){return Ct(n,t,he)}function Dt(n,t,e){return Ct(n,t,re(e))}function At(n,t){return ae(n,t)}function _t(n,t,e){return 0!=(n.compareDocumentPosition(t)&e)}function Mt(n,t){var e=function(n,t){for(var e=0;e<n.length;e++){var o=n[e];if(o.test(t))return o}return undefined}(n,t);if(!e)return{major:0,minor:0};function o(n){return Number(t.replace(e,"$"+n))}return ke(o(1),o(2))}function Ft(n,t){return function(){return t===n}}function It(n,t){return function(){return t===n}}function Rt(n,t){var e=String(t).toLowerCase();return E(n,function(n){return n.search(e)})}function Vt(n,t){return-1!==n.indexOf(t)}function Nt(t){return function(n){return Vt(n,t)}}function Ht(){return je.get()}function Pt(n,t){var e=n.dom();if(e.nodeType!==Xe)return!1;var o=e;if(o.matches!==undefined)return o.matches(t);if(o.msMatchesSelector!==undefined)return o.msMatchesSelector(t);if(o.webkitMatchesSelector!==undefined)return o.webkitMatchesSelector(t);if(o.mozMatchesSelector!==undefined)return o.mozMatchesSelector(t);throw new Error("Browser lacks native selectors")}function zt(n){return n.nodeType!==Xe&&n.nodeType!==Ye||0===n.childElementCount}function Lt(n,t){var e=t===undefined?v.document:t.dom();return zt(e)?[]:S(e.querySelectorAll(n),we.fromDom)}function jt(n,t){return n.dom()===t.dom()}function Ut(n,t){return jt(n.element(),t.event().target())}var Wt=function(n){return n.fold(zn,Pn)},Gt=function(n){return Hn(n,an.error,an.value)},Xt=Pn,Yt=function(n){var t=[],e=[];return bn(n,function(n){Hn(n,function(n){return e.push(n)},function(n){return t.push(n)})}),{values:t,errors:e}},qt=zn,Kt=function(n,t){return n.stype===Q.Value?t(n.svalue):n},Jt=function(n,t){return n.stype===Q.Error?t(n.serror):n},$t=function(n,t){return n.stype===Q.Value?{stype:Q.Value,svalue:t(n.svalue)}:n},Qt=function(n,t){return n.stype===Q.Error?{stype:Q.Error,serror:t(n.serror)}:n},Zt=function(n,t){var e=Yt(n);return 0<e.errors.length?Ln(e.errors):function(n,t){return 0<n.length?Xt(Dn(t,An.apply(undefined,n))):Xt(t)}(e.values,t)},ne=function(n){var t=Yt(n);return 0<t.errors.length?Ln(t.errors):Xt(t.values)},te=Tn([{setOf:["validator","valueType"]},{arrOf:["valueType"]},{objOf:["fields"]},{itemOf:["validator"]},{choiceOf:["key","branches"]},{thunk:["description"]},{func:["args","outputSchema"]}]),ee=Tn([{field:["name","presence","type"]},{state:["name"]}]),oe=Tn([{field:["key","okey","presence","prop"]},{state:["okey","instantiator"]}]),re=function(o){return{extract:function(n,t,e){return function(t,e,n,o){var r=S(n,function(n){return Xn(t,e,n,o)});return Zt(r,{})}(n,e,o,t)},toString:function(){return"obj{\n"+S(o,function(n){return n.fold(function(n,t,e,o){return n+" -> "+o.toString()},function(n,t){return"state("+n+")"})}).join("\n")+"}"},toDsl:function(){return te.objOf(S(o,function(n){return n.fold(function(n,t,e,o){return ee.field(n,e,o)},function(n,t){return ee.state(n)})}))}}},ie=nn(Yn(Xt)),ue=i(Kn,re),ae=oe.state,ce=oe.field,se=Yn(Xt),fe=function(o){return{extract:function(n,t,e){return o().extract(n,t,e)},toString:function(){return o().toString()},toDsl:function(){return o().toDsl()}}},le=function(n){return"Errors: \n"+function(n){var t=10<n.length?n.slice(0,10).concat([{path:[],getErrorInfo:function(){return"... (only showing first ten failures)"}}]):n;return S(t,function(n){return"Failed path: ("+n.path.join(" > ")+")\n"+n.getErrorInfo()})}(n.errors)+"\n\nInput object: "+jn(n.input)},de=nn(se),me=ut(mn,"number"),ge=ut(cn,"string"),pe=ut(ln,"boolean"),he=ut(dn,"function"),ve=function(t){function n(n,t){for(var e=n.next();!e.done;){if(!t(e.value))return!1;e=n.next()}return!0}if(Object(t)!==t)return!0;switch({}.toString.call(t).slice(8,-1)){case"Boolean":case"Number":case"String":case"Date":case"RegExp":case"Blob":case"FileList":case"ImageData":case"ImageBitmap":case"ArrayBuffer":return!0;case"Array":case"Object":return Object.keys(t).every(function(n){return ve(t[n])});case"Map":return n(t.keys(),ve)&&n(t.values(),ve);case"Set":return n(t.keys(),ve);default:return!1}},be=Yn(function(n){return ve(n)?Xt(n):qt("Expected value to be acceptable for sending via postMessage")}),ye=function(n){function t(){return e}var e=n;return{get:t,set:function(n){e=n},clone:function(){return ye(t())}}},xe=function(n){if(null===n||n===undefined)throw new Error("Node cannot be null or undefined");return{dom:nn(n)}},we={fromHtml:function(n,t){var e=(t||v.document).createElement("div");if(e.innerHTML=n,!e.hasChildNodes()||1<e.childNodes.length)throw v.console.error("HTML does not have a single root node",n),new Error("HTML must have a single root node");return xe(e.childNodes[0])},fromTag:function(n,t){var e=(t||v.document).createElement(n);return xe(e)},fromText:function(n,t){var e=(t||v.document).createTextNode(n);return xe(e)},fromDom:xe,fromPoint:function(n,t,e){var o=n.dom();return on.from(o.elementFromPoint(t,e)).map(xe)}},Se=function(n,t){return _t(n,t,v.Node.DOCUMENT_POSITION_CONTAINED_BY)},Ce=function(){return ke(0,0)},ke=function(n,t){return{major:n,minor:t}},Oe={nu:ke,detect:function(n,t){var e=String(t).toLowerCase();return 0===n.length?Ce():Mt(n,e)},unknown:Ce},Ee="Firefox",Te=function(n){var t=n.current;return{current:t,version:n.version,isEdge:Ft("Edge",t),isChrome:Ft("Chrome",t),isIE:Ft("IE",t),isOpera:Ft("Opera",t),isFirefox:Ft(Ee,t),isSafari:Ft("Safari",t)}},Be={unknown:function(){return Te({current:undefined,version:Oe.unknown()})},nu:Te,edge:nn("Edge"),chrome:nn("Chrome"),ie:nn("IE"),opera:nn("Opera"),firefox:nn(Ee),safari:nn("Safari")},De="Windows",Ae="Android",_e="Solaris",Me="FreeBSD",Fe="ChromeOS",Ie=function(n){var t=n.current;return{current:t,version:n.version,isWindows:It(De,t),isiOS:It("iOS",t),isAndroid:It(Ae,t),isOSX:It("OSX",t),isLinux:It("Linux",t),isSolaris:It(_e,t),isFreeBSD:It(Me,t),isChromeOS:It(Fe,t)}},Re={unknown:function(){return Ie({current:undefined,version:Oe.unknown()})},nu:Ie,windows:nn(De),ios:nn("iOS"),android:nn(Ae),linux:nn("Linux"),osx:nn("OSX"),solaris:nn(_e),freebsd:nn(Me),chromeos:nn(Fe)},Ve=function(n,e){return Rt(n,e).map(function(n){var t=Oe.detect(n.versionRegexes,e);return{current:n.name,version:t}})},Ne=function(n,e){return Rt(n,e).map(function(n){var t=Oe.detect(n.versionRegexes,e);return{current:n.name,version:t}})},He=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,Pe=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:function(n){return Vt(n,"edge/")&&Vt(n,"chrome")&&Vt(n,"safari")&&Vt(n,"applewebkit")}},{name:"Chrome",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,He],search:function(n){return Vt(n,"chrome")&&!Vt(n,"chromeframe")}},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:function(n){return Vt(n,"msie")||Vt(n,"trident")}},{name:"Opera",versionRegexes:[He,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:Nt("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:Nt("firefox")},{name:"Safari",versionRegexes:[He,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:function(n){return(Vt(n,"safari")||Vt(n,"mobile/"))&&Vt(n,"applewebkit")}}],ze=[{name:"Windows",search:Nt("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:function(n){return Vt(n,"iphone")||Vt(n,"ipad")},versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:Nt("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"OSX",search:Nt("mac os x"),versionRegexes:[/.*?mac\ os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:Nt("linux"),versionRegexes:[]},{name:"Solaris",search:Nt("sunos"),versionRegexes:[]},{name:"FreeBSD",search:Nt("freebsd"),versionRegexes:[]},{name:"ChromeOS",search:Nt("cros"),versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/]}],Le={browsers:nn(Pe),oses:nn(ze)},je=ye(function(n,t){var e=Le.browsers(),o=Le.oses(),r=Ve(e,n).fold(Be.unknown,Be.nu),i=Ne(o,n).fold(Re.unknown,Re.nu);return{browser:r,os:i,deviceType:function(n,t,e,o){var r=n.isiOS()&&!0===/ipad/i.test(e),i=n.isiOS()&&!r,u=n.isiOS()||n.isAndroid(),a=u||o("(pointer:coarse)"),c=r||!i&&u&&o("(min-device-width:768px)"),s=i||u&&!c,f=t.isSafari()&&n.isiOS()&&!1===/safari/i.test(e),l=!s&&!c&&!f;return{isiPad:nn(r),isiPhone:nn(i),isTablet:nn(c),isPhone:nn(s),isTouch:nn(a),isAndroid:n.isAndroid,isiOS:n.isiOS,isWebView:nn(f),isDesktop:nn(l)}}(i,r,n,t)}}(v.navigator.userAgent,function(n){return v.window.matchMedia(n).matches})),Ue=(v.Node.ATTRIBUTE_NODE,v.Node.CDATA_SECTION_NODE,v.Node.COMMENT_NODE,v.Node.DOCUMENT_NODE),We=(v.Node.DOCUMENT_TYPE_NODE,v.Node.DOCUMENT_FRAGMENT_NODE,v.Node.ELEMENT_NODE),Ge=v.Node.TEXT_NODE,Xe=(v.Node.PROCESSING_INSTRUCTION_NODE,v.Node.ENTITY_REFERENCE_NODE,v.Node.ENTITY_NODE,v.Node.NOTATION_NODE,We),Ye=Ue,qe=Ht().browser.isIE()?function(n,t){return Se(n.dom(),t.dom())}:function(n,t){var e=n.dom(),o=t.dom();return e!==o&&e.contains(o)};function Ke(n,t,e,o,r){return n(e,o)?on.some(e):dn(r)&&r(e)?on.none():t(e,o,r)}function Je(n){return n.dom().nodeName.toLowerCase()}function $e(t){return function(n){return function(n){return n.dom().nodeType}(n)===t}}"undefined"!=typeof v.window?v.window:Function("return this;")();function Qe(n){var t=zr(n)?n.dom().parentNode:n.dom();return t!==undefined&&null!==t&&t.ownerDocument.body.contains(t)}function Ze(n,t,e){for(var o=n.dom(),r=dn(e)?e:nn(!1);o.parentNode;){o=o.parentNode;var i=we.fromDom(o);if(t(i))return on.some(i);if(r(i))break}return on.none()}function no(n,t,e){return Ke(function(n,t){return t(n)},Ze,n,t,e)}function to(n,r){var i=function(n){for(var t=0;t<n.childNodes.length;t++){var e=we.fromDom(n.childNodes[t]);if(r(e))return on.some(e);var o=i(n.childNodes[t]);if(o.isSome())return o}return on.none()};return i(n.dom())}function eo(n){if(!$(n,"can")&&!$(n,"abort")&&!$(n,"run"))throw new Error("EventHandler defined by: "+JSON.stringify(n,null,2)+" does not have can, abort, or run!");return ot("Extracting event.handler",qn([St("can",nn(!0)),St("abort",nn(!1)),St("run",Z)]),n)}function oo(e){var n=function(t,o){return function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return O(t,function(n,t){return n&&o(t).apply(undefined,e)},!0)}}(e,function(n){return n.can}),t=function(t,o){return function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return O(t,function(n,t){return n||o(t).apply(undefined,e)},!1)}}(e,function(n){return n.abort});return eo({can:n,abort:t,run:function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];bn(e,function(n){n.run.apply(undefined,t)})}})}function ro(n,t){Ii(n,n.element(),t,{})}function io(n,t,e){Ii(n,n.element(),t,e)}function uo(n){ro(n,mi())}function ao(n,t,e){Ii(n,t,e,{})}function co(n,t,e,o){n.getSystem().triggerEvent(e,t,o.event())}function so(n){return K(n)}function fo(n,t){return{key:n,value:eo({abort:t})}}function lo(n){return{key:n,value:eo({run:function(n,t){t.event().prevent()}})}}function mo(n,t){return{key:n,value:eo({run:t})}}function go(n,e,o){return{key:n,value:eo({run:function(n,t){e.apply(undefined,[n,t].concat(o))}})}}function po(n){return function(e){return{key:n,value:eo({run:function(n,t){Ut(n,t)&&e(n,t)}})}}}function ho(n,t,e){return function(e,o){return mo(e,function(n,t){n.getSystem().getByUid(o).each(function(n){co(n,n.element(),e,t)})})}(n,t.partUids[e])}function vo(n,r){return mo(n,function(t,n){var e=n.event(),o=t.getSystem().getByDom(e.target()).fold(function(){return Ur(e.target(),function(n){return t.getSystem().getByDom(n).toOption()},nn(!1)).getOr(t)},function(n){return n});r(t,o,n)})}function bo(n){return mo(n,function(n,t){t.cut()})}function yo(n,t){return po(n)(t)}function xo(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];if(t.length!==e.length)throw new Error('Wrong number of arguments to struct. Expected "['+t.length+']", got '+e.length+" arguments");var o={};return bn(t,function(n,t){o[n]=nn(e[t])}),o}}function wo(n){return n.slice(0).sort()}function So(t,n){if(!fn(n))throw new Error("The "+t+" fields must be an array. Was: "+n+".");bn(n,function(n){if(!cn(n))throw new Error("The value "+n+" in the "+t+" fields was not a string.")})}function Co(r,i){var u=r.concat(i);if(0===u.length)throw new Error("You must specify at least one required or optional field.");return So("required",r),So("optional",i),function(n){var e=wo(n);E(e,function(n,t){return t<e.length-1&&n===e[t+1]}).each(function(n){throw new Error("The field: "+n+" occurs more than once in the combined fields: ["+e.join(", ")+"].")})}(u),function(t){var e=wn(t);D(r,function(n){return vn(e,n)})||function(n,t){throw new Error("All required keys ("+wo(n).join(", ")+") were not specified. Specified keys were: "+wo(t).join(", ")+".")}(r,e);var n=C(e,function(n){return!vn(u,n)});0<n.length&&function(n){throw new Error("Unsupported keys for object: "+wo(n).join(", "))}(n);var o={};return bn(r,function(n){o[n]=nn(t[n])}),bn(i,function(n){o[n]=nn(Object.prototype.hasOwnProperty.call(t,n)?on.some(t[n]):on.none())}),o}}function ko(n){return we.fromDom(n.dom().ownerDocument)}function Oo(n){return we.fromDom(n.dom().ownerDocument.documentElement)}function Eo(n){return we.fromDom(n.dom().ownerDocument.defaultView)}function To(n){return on.from(n.dom().parentNode).map(we.fromDom)}function Bo(n){return on.from(n.dom().offsetParent).map(we.fromDom)}function Do(n){return S(n.dom().childNodes,we.fromDom)}function Ao(n,t){var e=n.dom().childNodes;return on.from(e[t]).map(we.fromDom)}function _o(t,e){To(t).each(function(n){n.dom().insertBefore(e.dom(),t.dom())})}function Mo(n,t){(function(n){return on.from(n.dom().nextSibling).map(we.fromDom)})(n).fold(function(){To(n).each(function(n){Pi(n,t)})},function(n){_o(n,t)})}function Fo(t,e){(function(n){return Ao(n,0)})(t).fold(function(){Pi(t,e)},function(n){t.dom().insertBefore(e.dom(),n.dom())})}function Io(t,n){bn(n,function(n){Pi(t,n)})}function Ro(n){n.dom().textContent="",bn(Do(n),function(n){zi(n)})}function Vo(n){var t=Do(n);0<t.length&&function(t,n){bn(n,function(n){_o(t,n)})}(n,t),zi(n)}function No(n){return n.dom().innerHTML}function Ho(n,t){var e=ko(n).dom(),o=we.fromDom(e.createDocumentFragment()),r=function(n,t){var e=(t||v.document).createElement("div");return e.innerHTML=n,Do(we.fromDom(e))}(t,e);Io(o,r),Ro(n),Pi(n,o)}function Po(n,t,e){if(!(cn(e)||ln(e)||mn(e)))throw v.console.error("Invalid call to Attr.set. Key ",t,":: Value ",e,":: Element ",n),new Error("Attribute value was not simple");n.setAttribute(t,e+"")}function zo(n,t,e){Po(n.dom(),t,e)}function Lo(n,t){var e=n.dom().getAttribute(t);return null===e?undefined:e}function jo(n,t){var e=n.dom();return!(!e||!e.hasAttribute)&&e.hasAttribute(t)}function Uo(n,t){n.dom().removeAttribute(t)}function Wo(n){return function(n,t){return we.fromDom(n.dom().cloneNode(t))}(n,!1)}function Go(n){return function(n){var t=we.fromTag("div"),e=we.fromDom(n.dom().cloneNode(!0));return Pi(t,e),No(t)}(Wo(n))}function Xo(n){return Go(n)}function Yo(n){var t=(new Date).getTime();return n+"_"+Math.floor(1e9*Math.random())+ ++Ui+String(t)}function qo(n){return Yo(n)}function Ko(t){function n(n){return function(){throw new Error("The component must be in a context to send: "+n+"\n"+Xo(t().element())+" is not in context.")}}return{debugInfo:nn("fake"),triggerEvent:n("triggerEvent"),triggerFocus:n("triggerFocus"),triggerEscape:n("triggerEscape"),build:n("build"),addToWorld:n("addToWorld"),removeFromWorld:n("removeFromWorld"),addToGui:n("addToGui"),removeFromGui:n("removeFromGui"),getByUid:n("getByUid"),getByDom:n("getByDom"),broadcast:n("broadcast"),broadcastOn:n("broadcastOn"),broadcastEvent:n("broadcastEvent"),isConnected:nn(!1)}}function Jo(n,t){var e=n.toString(),o=e.indexOf(")")+1,r=e.indexOf("("),i=e.substring(r+1,o-1).split(/,\s*/);return n.toFunctionAnnotation=function(){return{name:t,parameters:Qi(i)}},n}function $o(n){return q(Zi,n)}function Qo(o){return function(n,t){var e=t.toString(),o=e.indexOf(")")+1,r=e.indexOf("("),i=e.substring(r+1,o-1).split(/,\s*/);return n.toFunctionAnnotation=function(){return{name:"OVERRIDE",parameters:Qi(i.slice(1))}},n}(function(n){for(var t=[],e=1;e<arguments.length;e++)t[e-1]=arguments[e];return o.apply(undefined,[n.getApis()].concat([n].concat(t)))},o)}function Zo(n,r){var i={};return Cn(n,function(n,o){Cn(n,function(n,t){var e=Y(t,[])(i);i[t]=e.concat([r(o,n)])})}),i}function nr(n){return{classes:n.classes!==undefined?n.classes:[],attributes:n.attributes!==undefined?n.attributes:{},styles:n.styles!==undefined?n.styles:{}}}function tr(n){return n.cHandler}function er(n,t){return{name:nn(n),handler:nn(t)}}function or(n,t,e){var o=N(N({},e),function(n,t){var e={};return bn(n,function(n){e[n.name()]=n.handlers(t)}),e}(t,n));return Zo(o,er)}function rr(n){var i=function(n){return dn(n)?{can:nn(!0),abort:nn(!1),run:n}:n}(n);return function(n,t){for(var e=[],o=2;o<arguments.length;o++)e[o-2]=arguments[o];var r=[n,t].concat(e);i.abort.apply(undefined,r)?t.stop():i.can.apply(undefined,r)&&i.run.apply(undefined,r)}}function ir(n,t,e){var o=t[e];return o?function(u,a,n,c){var t=n.slice(0);try{var e=t.sort(function(n,t){var e=n[a](),o=t[a](),r=c.indexOf(e),i=c.indexOf(o);if(-1===r)throw new Error("The ordering for "+u+" does not have an entry for "+e+".\nOrder specified: "+JSON.stringify(c,null,2));if(-1===i)throw new Error("The ordering for "+u+" does not have an entry for "+o+".\nOrder specified: "+JSON.stringify(c,null,2));return r<i?-1:i<r?1:0});return an.value(e)}catch(o){return an.error([o])}}("Event: "+e,"name",n,o).map(function(n){var t=S(n,function(n){return n.handler()});return oo(t)}):function(n,t){return an.error(["The event ("+n+') has more than one behaviour that listens to it.\nWhen this occurs, you must specify an event ordering for the behaviours in your spec (e.g. [ "listing", "toggling" ]).\nThe behaviours that can trigger it are: '+JSON.stringify(S(t,function(n){return n.name()}),null,2)])}(e,n)}function ur(n){return tt("custom.definition",re([ce("dom","dom",Mn(),re([ct("tag"),St("styles",{}),St("classes",[]),St("attributes",{}),ht("value"),ht("innerHtml")])),ct("components"),ct("uid"),St("events",{}),St("apis",{}),ce("eventOrder","eventOrder",function(n){return _n.mergeWithThunk(nn(n))}({"alloy.execute":["disabling","alloy.base.behaviour","toggling","typeaheadevents"],"alloy.focus":["alloy.base.behaviour","focusing","keying"],"alloy.system.init":["alloy.base.behaviour","disabling","toggling","representing"],input:["alloy.base.behaviour","representing","streaming","invalidating"],"alloy.system.detached":["alloy.base.behaviour","representing","item-events","tooltipping"],mousedown:["focusing","alloy.base.behaviour","item-type-events"],touchstart:["focusing","alloy.base.behaviour","item-type-events"],mouseover:["item-type-events","tooltipping"]}),de()),ht("domModification")]),n)}function ar(n,t){var e=Lo(n,t);return e===undefined||""===e?[]:e.split(" ")}function cr(n){return n.dom().classList!==undefined}function sr(n,t){return function(n,t,e){var o=ar(n,t).concat([e]);return zo(n,t,o.join(" ")),!0}(n,"class",t)}function fr(n,t){return function(n,t,e){var o=C(ar(n,t),function(n){return n!==e});return 0<o.length?zo(n,t,o.join(" ")):Uo(n,t),!1}(n,"class",t)}function lr(n,t){cr(n)?n.dom().classList.add(t):sr(n,t)}function dr(n){0===(cr(n)?n.dom().classList:function(n){return ar(n,"class")}(n)).length&&Uo(n,"class")}function mr(n,t){cr(n)?n.dom().classList.remove(t):fr(n,t),dr(n)}function gr(n,t){return cr(n)&&n.dom().classList.contains(t)}function pr(t,n){bn(n,function(n){lr(t,n)})}function hr(t,n){bn(n,function(n){mr(t,n)})}function vr(n){return n.style!==undefined&&dn(n.style.getPropertyValue)}function br(n,t,e){if(!cn(e))throw v.console.error("Invalid call to CSS.set. Property ",t,":: Value ",e,":: Element ",n),new Error("CSS value must be a string: "+e);vr(n)&&n.style.setProperty(t,e)}function yr(n,t){vr(n)&&n.style.removeProperty(t)}function xr(n,t,e){var o=n.dom();br(o,t,e)}function wr(n,t){var e=n.dom();Cn(t,function(n,t){br(e,t,n)})}function Sr(n,t){var e=n.dom(),o=v.window.getComputedStyle(e).getPropertyValue(t),r=""!==o||Qe(n)?o:ru(e,t);return null===r?undefined:r}function Cr(n,t){var e=n.dom(),o=ru(e,t);return on.from(o).filter(function(n){return 0<n.length})}function kr(n,t,e){var o=we.fromTag(n);return xr(o,t,e),Cr(o,t).isSome()}function Or(n,t){var e=n.dom();yr(e,t),jo(n,"style")&&""===function(n){return n.replace(/^\s+|\s+$/g,"")}(Lo(n,"style"))&&Uo(n,"style")}function Er(n){return n.dom().offsetWidth}function Tr(n){return n.dom().value}function Br(n,t){if(t===undefined)throw new Error("Value.set was undefined");n.dom().value=t}function Dr(n){var t=we.fromTag(n.tag);!function(n,t){var e=n.dom();Cn(t,function(n,t){Po(e,t,n)})}(t,n.attributes),pr(t,n.classes),wr(t,n.styles),n.innerHtml.each(function(n){return Ho(t,n)});var e=n.domChildren;return Io(t,e),n.value.each(function(n){Br(t,n)}),n.uid,qi(t,n.uid),t}function Ar(n,t){return function(t,n){var e=S(n,function(n){return wt(n.name(),[ct("config"),St("state",nu)])}),o=tt("component.behaviours",re(e),t.behaviours).fold(function(n){throw new Error(le(n)+"\nComplete spec:\n"+JSON.stringify(t,null,2))},function(n){return n});return{list:n,data:P(o,function(n){var t=n.map(function(n){return{config:n.config,state:n.state.init(n.config)}});return function(){return t}})}}(n,t)}function _r(n){var t=function(n){var t=Y("behaviours",{})(n),e=C(wn(t),function(n){return t[n]!==undefined});return S(e,function(n){return t[n].me})}(n);return Ar(n,t)}function Mr(n,t,e){var o=function(n){return N(N({},n.dom),{uid:n.uid,domChildren:S(n.components,function(n){return n.element()})})}(n),r=function(n){return n.domModification.fold(function(){return nr({})},nr)}(n),i={"alloy.base.modification":r};return function(n,t){return N(N({},n),{attributes:N(N({},n.attributes),t.attributes),styles:N(N({},n.styles),t.styles),classes:n.classes.concat(t.classes)})}(o,0<t.length?function(t,n,e,o){var r=N({},n);bn(e,function(n){r[n.name()]=n.exhibit(t,o)});function i(n){return k(n,function(n,t){return N(N({},t.modification),n)},{})}var u=Zo(r,function(n,t){return{name:n,modification:t}}),a=k(u.classes,function(n,t){return t.modification.concat(n)},[]),c=i(u.attributes),s=i(u.styles);return nr({classes:a,attributes:c,styles:s})}(e,i,t,o):r)}function Fr(n,t,e){var o={"alloy.base.behaviour":function(n){return n.events}(n)};return function(n,t,e,o){var r=or(n,e,o);return ou(r,t)}(e,n.eventOrder,t,o).getOrDie()}function Ir(n){var t=Ji(n),e=t.events,o=c(t,["events"]),r=function(n){var t=Y("components",[])(n);return S(t,au)}(o),i=N(N({},o),{events:N(N({},ji),e),components:r});return an.value(function(e){function n(){return l}var o=ye($i),t=et(ur(e)),r=_r(e),i=function(n){return n.list}(r),u=function(n){return n.data}(r),a=Mr(t,i,u),c=Dr(a),s=Fr(t,i,u),f=ye(t.components),l={getSystem:o.get,config:function(n){var t=u;return(dn(t[n.name()])?t[n.name()]:function(){throw new Error("Could not find "+n.name()+" in "+JSON.stringify(e,null,2))})()},hasConfigured:function(n){return dn(u[n.name()])},spec:nn(e),readState:function(n){return u[n]().map(function(n){return n.state.readState()}).getOr("not enabled")},getApis:function(){return t.apis},connect:function(n){o.set(n)},disconnect:function(){o.set(Ko(n))},element:nn(c),syncComponents:function(){var n=Do(c),t=B(n,function(n){return o.get().getByDom(n).fold(function(){return[]},function(n){return[n]})});f.set(t)},components:f.get,events:nn(s)};return l}(i))}function Rr(n){var t=we.fromText(n);return iu({element:t})}var Vr,Nr,Hr,Pr=$e(We),zr=$e(Ge),Lr=L(function(){return jr(we.fromDom(v.document))}),jr=function(n){var t=n.dom().body;if(null===t||t===undefined)throw new Error("Body is not available yet");return we.fromDom(t)},Ur=function(n,t,e){return no(n,function(n){return t(n).isSome()},e).bind(t)},Wr=nn("touchstart"),Gr=nn("touchmove"),Xr=nn("touchend"),Yr=nn("touchcancel"),qr=nn("mousedown"),Kr=nn("mousemove"),Jr=nn("mouseout"),$r=nn("mouseup"),Qr=nn("mouseover"),Zr=nn("focusin"),ni=nn("focusout"),ti=nn("keydown"),ei=nn("keyup"),oi=nn("input"),ri=nn("change"),ii=nn("click"),ui=nn("transitionend"),ai=nn("selectstart"),ci={tap:nn("alloy.tap")},si=nn("alloy.focus"),fi=nn("alloy.blur.post"),li=nn("alloy.paste.post"),di=nn("alloy.receive"),mi=nn("alloy.execute"),gi=nn("alloy.focus.item"),pi=ci.tap,hi=nn("alloy.longpress"),vi=nn("alloy.sandbox.close"),bi=nn("alloy.typeahead.cancel"),yi=nn("alloy.system.init"),xi=nn("alloy.system.touchmove"),wi=nn("alloy.system.touchend"),Si=nn("alloy.system.scroll"),Ci=nn("alloy.system.resize"),ki=nn("alloy.system.attached"),Oi=nn("alloy.system.detached"),Ei=nn("alloy.system.dismissRequested"),Ti=nn("alloy.system.repositionRequested"),Bi=nn("alloy.focusmanager.shifted"),Di=nn("alloy.slotcontainer.visibility"),Ai=nn("alloy.change.tab"),_i=nn("alloy.dismiss.tab"),Mi=nn("alloy.highlight"),Fi=nn("alloy.dehighlight"),Ii=function(n,t,e,o){var r=N({target:t},o);n.getSystem().triggerEvent(e,t,P(r,nn))},Ri=po(ki()),Vi=po(Oi()),Ni=po(yi()),Hi=(Vr=mi(),function(n){return mo(Vr,n)}),Pi=(xo("element","offset"),function(n,t){n.dom().appendChild(t.dom())}),zi=function(n){var t=n.dom();null!==t.parentNode&&t.parentNode.removeChild(t)},Li=so([(Nr=si(),Hr=function(n,t){var e=t.event().originator(),o=t.event().target();return!function(n,t,e){return jt(t,n.element())&&!jt(t,e)}(n,e,o)||(v.console.warn(si()+" did not get interpreted by the desired target. \nOriginator: "+Xo(e)+"\nTarget: "+Xo(o)+"\nCheck the "+si()+" event handlers"),!1)},{key:Nr,value:eo({can:Hr})})]),ji=/* */Object.freeze({events:Li}),Ui=0,Wi=nn("alloy-id-"),Gi=nn("data-alloy-id"),Xi=Wi(),Yi=Gi(),qi=function(n,t){Object.defineProperty(n.dom(),Yi,{value:t,writable:!0})},Ki=function(n){var t=Pr(n)?n.dom()[Yi]:null;return on.from(t)},Ji=l,$i=Ko(),Qi=function(n){return S(n,function(n){return function(n,t){return function(n,t,e){return""===t||!(n.length<t.length)&&n.substr(e,e+t.length)===t}(n,t,n.length-t.length)}(n,"/*")?n.substring(0,n.length-"/*".length):n})},Zi=Yo("alloy-premade"),nu={init:function(){return tu({readState:function(){return"No State required"}})}},tu=function(n){return n},eu=function(n,t){return function(n,t){return{cHandler:n,purpose:nn(t)}}(d.apply(undefined,[n.handler].concat(t)),n.purpose())},ou=function(n,i){var t=On(n,function(o,r){return(1===o.length?an.value(o[0].handler()):ir(o,i,r)).map(function(n){var t=rr(n),e=1<o.length?C(i[r],function(t){return x(o,function(n){return n.name()===t})}).join(" > "):o[0].name();return q(r,function(n,t){return{handler:n,purpose:nn(t)}}(t,e))})});return J(t,{})},ru=function(n,t){return vr(n)?n.style.getPropertyValue(t):""},iu=function(n){var t=ot("external.component",qn([ct("element"),ht("uid")]),n),e=ye(Ko());t.uid.each(function(n){qi(t.element,n)});var o={getSystem:e.get,config:on.none,hasConfigured:nn(!1),connect:function(n){e.set(n)},disconnect:function(){e.set(Ko(function(){return o}))},getApis:function(){return{}},element:nn(t.element),spec:nn(n),readState:nn("No state"),syncComponents:Z,components:nn([]),events:nn({})};return $o(o)},uu=qo,au=function(t){return function(n){return Nn(n,Zi)}(t).fold(function(){var n=t.hasOwnProperty("uid")?t:N({uid:uu("")},t);return Ir(n).getOrDie()},function(n){return n})},cu=$o;function su(o,r){function n(n){var t=r(n);if(t<=0||null===t){var e=Sr(n,o);return parseFloat(e)||0}return t}function i(r,n){return O(n,function(n,t){var e=Sr(r,t),o=e===undefined?0:parseInt(e,10);return isNaN(o)?n:n+o},0)}return{set:function(n,t){if(!mn(t)&&!t.match(/^[0-9]+$/))throw new Error(o+".set accepts only positive integer values. Value was "+t);var e=n.dom();vr(e)&&(e.style[o]=t+"px")},get:n,getOuter:n,aggregate:i,max:function(n,t,e){var o=i(n,e);return o<t?t-o:0}}}function fu(n){return Fu.get(n)}function lu(n){return Fu.getOuter(n)}function du(n,t){return n!==undefined?n:t!==undefined?t:0}function mu(n){var t=n.dom().ownerDocument,e=t.body,o=t.defaultView,r=t.documentElement;if(e===n.dom())return Ru(e.offsetLeft,e.offsetTop);var i=du(o.pageYOffset,r.scrollTop),u=du(o.pageXOffset,r.scrollLeft),a=du(r.clientTop,e.clientTop),c=du(r.clientLeft,e.clientLeft);return Vu(n).translate(u-c,i-a)}function gu(n){return Nu.get(n)}function pu(n){return Nu.getOuter(n)}function hu(n){var t=n!==undefined?n.dom():v.document,e=t.body.scrollLeft||t.documentElement.scrollLeft,o=t.body.scrollTop||t.documentElement.scrollTop;return Ru(e,o)}function vu(n,t,e,o){return{x:nn(n),y:nn(t),width:nn(e),height:nn(o),right:nn(n+e),bottom:nn(t+o)}}function bu(n){var t=n===undefined?v.window:n,e=t.document,o=hu(we.fromDom(e)),r=t.visualViewport;if(r!==undefined)return vu(Math.max(r.pageLeft,o.left()),Math.max(r.pageTop,o.top()),r.width,r.height);var i=e.documentElement,u=i.clientWidth,a=i.clientHeight;return vu(o.left(),o.top(),u,a)}function yu(o){var n=we.fromDom(v.document),r=hu(n);return function(n,t){var e=t.owner(n),o=Hu(t,e);return on.some(o)}(o,Pu).fold(d(mu,o),function(n){var t=Vu(o),e=k(n,function(n,t){var e=Vu(t);return{left:n.left+e.left(),top:n.top+e.top()}},{left:0,top:0});return Ru(e.left+t.left()+r.left(),e.top+t.top()+r.top())})}function xu(n,t,e,o){return{x:nn(n),y:nn(t),width:nn(e),height:nn(o),right:nn(n+e),bottom:nn(t+o)}}function wu(n){var t=mu(n),e=pu(n),o=lu(n);return xu(t.left(),t.top(),e,o)}function Su(n){var t=yu(n),e=pu(n),o=lu(n);return xu(t.left(),t.top(),e,o)}function Cu(){return bu(v.window)}function ku(n,t,e){return Ze(n,function(n){return Pt(n,t)},e)}function Ou(n,t){return function(n,t){var e=t===undefined?v.document:t.dom();return zt(e)?on.none():on.from(e.querySelector(n)).map(we.fromDom)}(t,n)}function Eu(n,t,e){return Ke(Pt,ku,n,t,e)}function Tu(){var t=Yo("aria-owns");return{id:nn(t),link:function(n){zo(n,"aria-owns",t)},unlink:function(n){Uo(n,"aria-owns")}}}function Bu(t,n){return function(n){return no(n,function(n){if(!Pr(n))return!1;var t=Lo(n,"id");return t!==undefined&&-1<t.indexOf("aria-owns")}).bind(function(n){var t=Lo(n,"id"),e=ko(n);return Ou(e,'[aria-owns="'+t+'"]')})}(n).exists(function(n){return ju(t,n)})}function Du(n){for(var t=[],e=function(n){t.push(n)},o=0;o<n.length;o++)n[o].each(e);return t}function Au(n,t){for(var e=0;e<n.length;e++){var o=t(n[e],e);if(o.isSome())return o}return on.none()}var _u,Mu,Fu=su("height",function(n){var t=n.dom();return Qe(n)?t.getBoundingClientRect().height:t.offsetHeight}),Iu=function(e,o){return{left:nn(e),top:nn(o),translate:function(n,t){return Iu(e+n,o+t)}}},Ru=Iu,Vu=function(n){var t=n.dom(),e=t.ownerDocument.body;return e===t?Ru(e.offsetLeft,e.offsetTop):Qe(n)?function(n){var t=n.getBoundingClientRect();return Ru(t.left,t.top)}(t):Ru(0,0)},Nu=su("width",function(n){return n.dom().offsetWidth}),Hu=(Ht().browser.isSafari(),function(o,n){return o.view(n).fold(nn([]),function(n){var t=o.owner(n),e=Hu(o,t);return[n].concat(e)})}),Pu=/* */Object.freeze({view:function(n){return(n.dom()===v.document?on.none():on.from(n.dom().defaultView.frameElement)).map(we.fromDom)},owner:function(n){return ko(n)}}),zu=xo("point","width","height"),Lu=xo("x","y","width","height"),ju=function(t,n){return function(n,t,e){return no(n,t,e).isSome()}(n,function(n){return jt(n,t.element())},nn(!1))||Bu(t,n)},Uu="unknown";(Mu=_u=_u||{})[Mu.STOP=0]="STOP",Mu[Mu.NORMAL=1]="NORMAL",Mu[Mu.LOGGING=2]="LOGGING";function Wu(t,n,e){switch(Nn(za.get(),t).orThunk(function(){var n=wn(za.get());return Au(n,function(n){return-1<t.indexOf(n)?on.some(za.get()[n]):on.none()})}).getOr(_u.NORMAL)){case _u.NORMAL:return e(ja());case _u.LOGGING:var o=function(t,e){var o=[],r=(new Date).getTime();return{logEventCut:function(n,t,e){o.push({outcome:"cut",target:t,purpose:e})},logEventStopped:function(n,t,e){o.push({outcome:"stopped",target:t,purpose:e})},logNoParent:function(n,t,e){o.push({outcome:"no-parent",target:t,purpose:e})},logEventNoHandlers:function(n,t){o.push({outcome:"no-handlers-left",target:t})},logEventResponse:function(n,t,e){o.push({outcome:"response",purpose:e,target:t})},write:function(){var n=(new Date).getTime();vn(["mousemove","mouseover","mouseout",yi()],t)||v.console.log(t,{event:t,time:n-r,target:e.dom(),sequence:S(o,function(n){return vn(["cut","stopped","response"],n.outcome)?"{"+n.purpose+"} "+n.outcome+" at ("+Xo(n.target)+")":n.outcome})})}}}(t,n),r=e(o);return o.write(),r;case _u.STOP:return!0}}function Gu(n,t,e){return Wu(n,t,e)}function Xu(){return mt("markers",[ct("backgroundMenu")].concat(Ua()).concat(Wa()))}function Yu(n){return mt("markers",S(n,ct))}function qu(n,t,e){return function(){var n=new Error;if(n.stack===undefined)return;var t=n.stack.split("\n");E(t,function(t){return 0<t.indexOf("alloy")&&!x(La,function(n){return-1<t.indexOf(n)})}).getOr(Uu)}(),ce(t,t,e,Zn(function(e){return an.value(function(){for(var n=[],t=0;t<arguments.length;t++)n[t]=arguments[t];return e.apply(undefined,n)})}))}function Ku(n){return qu(0,n,j(Z))}function Ju(n){return qu(0,n,j(on.none))}function $u(n){return qu(0,n,Mn())}function Qu(n){return qu(0,n,Mn())}function Zu(n,t){return At(n,nn(t))}function na(n){return At(n,l)}function ta(n){return n.x()}function ea(n,t){return n.x()+n.width()/2-t.width()/2}function oa(n,t){return n.x()+n.width()-t.width()}function ra(n,t){return n.y()-t.height()}function ia(n){return n.y()+n.height()}function ua(n,t){return n.y()+n.height()/2-t.height()/2}function aa(n,t,e){return qa(ta(n),ia(n),e.southeast(),Ja(),"layout-se")}function ca(n,t,e){return qa(oa(n,t),ia(n),e.southwest(),$a(),"layout-sw")}function sa(n,t,e){return qa(ta(n),ra(n,t),e.northeast(),Qa(),"layout-ne")}function fa(n,t,e){return qa(oa(n,t),ra(n,t),e.northwest(),Za(),"layout-nw")}function la(n,t,e){return qa(function(n){return n.x()+n.width()}(n),ua(n,t),e.east(),ec(),"layout-e")}function da(n,t,e){return qa(function(n,t){return n.x()-t.width()}(n,t),ua(n,t),e.west(),oc(),"layout-w")}function ma(){return[aa,ca,sa,fa,ic,rc]}function ga(){return[ca,aa,fa,sa,ic,rc]}function pa(e,o,r){return Ni(function(n,t){r(n,e,o)})}function ha(n,t,e,o,r,i){var u=qn(n),a=wt(t,[function(n,t){return vt(n,qn(t))}("config",n)]);return uc(u,a,t,e,o,r,i)}function va(r,i,u){return function(n,t,e){var o=e.toString(),r=o.indexOf(")")+1,i=o.indexOf("("),u=o.substring(i+1,r-1).split(/,\s*/);return n.toFunctionAnnotation=function(){return{name:t,parameters:Qi(u.slice(0,1).concat(u.slice(3)))}},n}(function(e){for(var n=[],t=1;t<arguments.length;t++)n[t-1]=arguments[t];var o=[e].concat(n);return e.config({name:nn(r)}).fold(function(){throw new Error("We could not find any behaviour configuration for: "+r+". Using API: "+u)},function(n){var t=Array.prototype.slice.call(o,1);return i.apply(undefined,[e,n.config,n.state].concat(t))})},u,i)}function ba(n){return{key:n,value:undefined}}function ya(n){return K(n)}function xa(n){var t=ot("Creating behaviour: "+n.name,ac,n);return ha(t.fields,t.name,t.active,t.apis,t.extra,t.state)}function wa(n){var t=ot("Creating behaviour: "+n.name,cc,n);return function(n,t,e,o,r,i){var u=n,a=wt(t,[vt("config",n)]);return uc(u,a,t,e,o,r,i)}(it(t.branchKey,t.branches),t.name,t.active,t.apis,t.extra,t.state)}function Sa(n){n.dom().focus()}function Ca(n){var t=n!==undefined?n.dom():v.document;return on.from(t.activeElement).map(we.fromDom)}function ka(t){return Ca(ko(t)).filter(function(n){return t.dom().contains(n.dom())})}function Oa(n,e){var o=ko(e),t=Ca(o).bind(function(t){function n(n){return jt(t,n)}return n(e)?on.some(e):to(e,n)}),r=n(e);return t.each(function(t){Ca(o).filter(function(n){return jt(n,t)}).fold(function(){Sa(t)},Z)}),r}function Ea(n,t,e){function r(n){return Nn(e,n).getOr([])}function o(n,t,e){var o=_(gc,e);return{offset:function(){return Ru(n,t)},classesOn:function(){return B(e,r)},classesOff:function(){return B(o,r)}}}return{southeast:function(){return o(-n,t,["top","alignLeft"])},southwest:function(){return o(n,t,["top","alignRight"])},south:function(){return o(-n/2,t,["top","alignCentre"])},northeast:function(){return o(-n,-t,["bottom","alignLeft"])},northwest:function(){return o(n,-t,["bottom","alignRight"])},north:function(){return o(-n/2,-t,["bottom","alignCentre"])},east:function(){return o(n,-t/2,["valignCentre","left"])},west:function(){return o(-n,-t/2,["valignCentre","right"])},innerNorthwest:function(){return o(-n,t,["top","alignRight"])},innerNortheast:function(){return o(n,t,["top","alignLeft"])},innerNorth:function(){return o(-n/2,t,["top","alignCentre"])},innerSouthwest:function(){return o(-n,-t,["bottom","alignRight"])},innerSoutheast:function(){return o(n,-t,["bottom","alignLeft"])},innerSouth:function(){return o(-n/2,-t,["bottom","alignCentre"])},innerWest:function(){return o(n,-t/2,["valignCentre","right"])},innerEast:function(){return o(-n,-t/2,["valignCentre","left"])}}}function Ta(){return Ea(0,0,{})}function Ba(n,t,e,o,r,i){var u=t.x()-e,a=t.y()-o,c=r-(u+t.width()),s=i-(a+t.height()),f=on.some(u),l=on.some(a),d=on.some(c),m=on.some(s),g=on.none();return function(n,t,e,o,r,i,u,a,c){return n.fold(t,e,o,r,i,u,a,c)}(t.direction(),function(){return hc(n,f,l,g,g)},function(){return hc(n,g,l,d,g)},function(){return hc(n,f,g,g,m)},function(){return hc(n,g,g,d,m)},function(){return hc(n,f,l,g,g)},function(){return hc(n,f,g,g,m)},function(){return hc(n,f,l,g,g)},function(){return hc(n,g,l,d,g)})}function Da(n,t){var e=d(yu,t),o=n.fold(e,e,function(){var n=hu();return yu(t).translate(-n.left(),-n.top())}),r=pu(t),i=lu(t);return xu(o.left(),o.top(),r,i)}function Aa(n){return n}function _a(t,e){return function(n){return"rtl"===wc(n)?e:t}}function Ma(){return wt("layouts",[ct("onLtr"),ct("onRtl")])}function Fa(t,n,e,o){var r=n.layouts.map(function(n){return n.onLtr(t)}).getOr(e),i=n.layouts.map(function(n){return n.onRtl(t)}).getOr(o);return _a(r,i)(t)}function Ia(n,t,e){var o=n.document.createRange();return function(e,n){n.fold(function(n){e.setStartBefore(n.dom())},function(n,t){e.setStart(n.dom(),t)},function(n){e.setStartAfter(n.dom())})}(o,t),function(e,n){n.fold(function(n){e.setEndBefore(n.dom())},function(n,t){e.setEnd(n.dom(),t)},function(n){e.setEndAfter(n.dom())})}(o,e),o}function Ra(n,t,e,o,r){var i=n.document.createRange();return i.setStart(t.dom(),e),i.setEnd(o.dom(),r),i}function Va(n){return{left:nn(n.left),top:nn(n.top),right:nn(n.right),bottom:nn(n.bottom),width:nn(n.width),height:nn(n.height)}}function Na(n,t,e){return t(we.fromDom(e.startContainer),e.startOffset,we.fromDom(e.endContainer),e.endOffset)}function Ha(n,t){return function(n,t){var e=t.ltr();return e.collapsed?t.rtl().filter(function(n){return!1===n.collapsed}).map(function(n){return Dc.rtl(we.fromDom(n.endContainer),n.endOffset,we.fromDom(n.startContainer),n.startOffset)}).getOrThunk(function(){return Na(0,Dc.ltr,e)}):Na(0,Dc.ltr,e)}(0,function(r,n){return n.match({domRange:function(n){return{ltr:nn(n),rtl:on.none}},relative:function(n,t){return{ltr:L(function(){return Ia(r,n,t)}),rtl:L(function(){return on.some(Ia(r,t,n))})}},exact:function(n,t,e,o){return{ltr:L(function(){return Ra(r,n,t,e,o)}),rtl:L(function(){return on.some(Ra(r,e,o,n,t))})}}})}(n,t))}function Pa(n,t,e){return t>=n.left&&t<=n.right&&e>=n.top&&e<=n.bottom}var za=ye({}),La=["alloy/data/Fields","alloy/debugging/Debugging"],ja=nn({logEventCut:Z,logEventStopped:Z,logNoParent:Z,logEventNoHandlers:Z,logEventResponse:Z,write:Z}),Ua=nn([ct("menu"),ct("selectedMenu")]),Wa=nn([ct("item"),ct("selectedItem")]),Ga=(nn(re(Wa().concat(Ua()))),nn(re(Wa()))),Xa=mt("initSize",[ct("numColumns"),ct("numRows")]),Ya=nn(Xa),qa=xo("x","y","bubble","direction","label"),Ka=Tn([{southeast:[]},{southwest:[]},{northeast:[]},{northwest:[]},{south:[]},{north:[]},{east:[]},{west:[]}]),Ja=Ka.southeast,$a=Ka.southwest,Qa=Ka.northeast,Za=Ka.northwest,nc=Ka.south,tc=Ka.north,ec=Ka.east,oc=Ka.west,rc=function(n,t,e){return qa(ea(n,t),ra(n,t),e.north(),tc(),"layout-n")},ic=function(n,t,e){return qa(ea(n,t),ia(n),e.south(),nc(),"layout-s")},uc=function(e,n,o,r,t,i,u){function a(n){return $(n,o)?n[o]():on.none()}var c=P(t,function(n,t){return va(o,n,t)}),s=P(i,function(n,t){return Jo(n,t)}),f=N(N(N({},s),c),{revoke:d(ba,o),config:function(n){var t=ot(o+"-config",e,n);return{key:o,value:{config:t,me:f,configAsRaw:L(function(){return ot(o+"-config",e,n)}),initialConfig:n,state:u}}},schema:function(){return n},exhibit:function(n,e){return a(n).bind(function(t){return Nn(r,"exhibit").map(function(n){return n(e,t.config,t.state)})}).getOr(nr({}))},name:function(){return o},handlers:function(n){return a(n).map(function(n){return Y("events",function(n,t){return{}})(r)(n.config,n.state)}).getOr({})}});return f},ac=qn([ct("fields"),ct("name"),St("active",{}),St("apis",{}),St("state",nu),St("extra",{})]),cc=qn([ct("branchKey"),ct("branches"),ct("name"),St("active",{}),St("apis",{}),St("state",nu),St("extra",{})]),sc=nn(undefined),fc=/* */Object.freeze({events:function(t){return so([mo(di(),function(r,i){var u=t.channels,n=function(n,t){return t.universal()?n:C(n,function(n){return vn(t.channels(),n)})}(wn(u),i);bn(n,function(n){var t=u[n],e=t.schema,o=ot("channel["+n+"] data\nReceiver: "+Xo(r.element()),e,i.data());t.onReceive(r,o)})})])}}),lc=[st("channels",nt(an.value,qn([$u("onReceive"),St("schema",de())])))],dc=xa({fields:lc,name:"receiving",active:fc}),mc=/* */Object.freeze({exhibit:function(n,t){return nr({classes:[],styles:t.useFixed()?{}:{position:"relative"}})}}),gc=["valignCentre","alignLeft","alignRight","alignCentre","top","bottom","left","right"],pc=Co(["x","y","width","height","maxHeight","maxWidth","direction","classes","label","candidateYforTest"],[]),hc=xo("position","left","top","right","bottom"),vc=Tn([{none:[]},{relative:["x","y","width","height"]},{fixed:["x","y","width","height"]}]),bc=function(n,t,e){var o=Ru(t,e);return n.fold(nn(o),nn(o),function(){var n=hu();return o.translate(-n.left(),-n.top())})},yc=vc.relative,xc=vc.fixed,wc=function(n){return"rtl"===Sr(n,"direction")?"rtl":"ltr"},Sc=[ct("hotspot"),ht("bubble"),St("overrides",{}),Ma(),Zu("placement",function(n,t,e){var o=t.hotspot,r=Da(e,o.element()),i=Fa(n.element(),t,ma(),ga());return on.some(Aa({anchorBox:r,bubble:t.bubble.getOr(Ta()),overrides:t.overrides,layouts:i,placer:on.none()}))})],Cc=[ct("x"),ct("y"),St("height",0),St("width",0),St("bubble",Ta()),St("overrides",{}),Ma(),Zu("placement",function(n,t,e){var o=bc(e,t.x,t.y),r=xu(o.left(),o.top(),t.width,t.height),i=Fa(n.element(),t,[aa,ca,sa,fa,ic,rc,la,da],[ca,aa,fa,sa,ic,rc,la,da]);return on.some(Aa({anchorBox:r,bubble:t.bubble,overrides:t.overrides,layouts:i,placer:on.none()}))})],kc={create:xo("start","soffset","finish","foffset")},Oc=Tn([{before:["element"]},{on:["element","offset"]},{after:["element"]}]),Ec=(Oc.before,Oc.on,Oc.after,function(n){return n.fold(l,l,l)}),Tc=Tn([{domRange:["rng"]},{relative:["startSitu","finishSitu"]},{exact:["start","soffset","finish","foffset"]}]),Bc={domRange:Tc.domRange,relative:Tc.relative,exact:Tc.exact,exactFromRange:function(n){return Tc.exact(n.start(),n.soffset(),n.finish(),n.foffset())},getWin:function(n){var t=function(n){return n.match({domRange:function(n){return we.fromDom(n.startContainer)},relative:function(n,t){return Ec(n)},exact:function(n,t,e,o){return n}})}(n);return Eo(t)},range:kc.create},Dc=Tn([{ltr:["start","soffset","finish","foffset"]},{rtl:["start","soffset","finish","foffset"]}]);function Ac(n){return uf.get(n)}function _c(n){return uf.getOption(n)}function Mc(e,o,n,t,r){function i(n){var t=e.dom().createRange();return t.setStart(o.dom(),n),t.collapse(!0),t}var u=Ac(o).length,a=function(n,t,e,o,r){if(0===r)return 0;if(t===o)return r-1;for(var i=o,u=1;u<r;u++){var a=n(u),c=Math.abs(t-a.left);if(e<=a.bottom){if(e<a.top||i<c)return u-1;i=c}}return 0}(function(n){return i(n).getBoundingClientRect()},n,t,r.right,u);return i(a)}function Fc(n){return function(n){return _c(n).filter(function(n){return 0!==n.trim().length||-1<n.indexOf("\xa0")}).isSome()}(n)||vn(cf,Je(n))}function Ic(n){return to(n,Fc)}function Rc(n){return sf(n,Fc)}function Vc(n,t){return t-n.left<n.right-t}function Nc(n,t,e){var o=n.dom().createRange();return o.selectNode(t.dom()),o.collapse(e),o}function Hc(t,n,e){var o=t.dom().createRange();o.selectNode(n.dom());var r=o.getBoundingClientRect(),i=Vc(r,e);return(!0===i?Ic:Rc)(n).map(function(n){return Nc(t,n,i)})}function Pc(n,t,e){var o=t.dom().getBoundingClientRect(),r=Vc(o,e);return on.some(Nc(n,t,r))}function zc(n,t,e,o){var r=n.dom().createRange();r.selectNode(t.dom());var i=r.getBoundingClientRect();return function(n,t,e,o){var r=n.dom().createRange();r.selectNode(t.dom());var i=r.getBoundingClientRect(),u=Math.max(i.left,Math.min(i.right,e)),a=Math.max(i.top,Math.min(i.bottom,o));return af(n,t,u,a)}(n,t,Math.max(i.left,Math.min(i.right,e)),Math.max(i.top,Math.min(i.bottom,o)))}function Lc(n,t){return Lt(t,n)}function jc(n,t,e,o){var r=function(n,t,e,o){var r=ko(n).dom().createRange();return r.setStart(n.dom(),t),r.setEnd(e.dom(),o),r}(n,t,e,o),i=jt(n,e)&&t===o;return r.collapsed&&!i}function Uc(n){var t=we.fromDom(n.anchorNode),e=we.fromDom(n.focusNode);return jc(t,n.anchorOffset,e,n.focusOffset)?on.some(kc.create(t,n.anchorOffset,e,n.focusOffset)):function(n){if(0<n.rangeCount){var t=n.getRangeAt(0),e=n.getRangeAt(n.rangeCount-1);return on.some(kc.create(we.fromDom(t.startContainer),t.startOffset,we.fromDom(e.endContainer),e.endOffset))}return on.none()}(n)}function Wc(n,t){return function(n){var t=n.getClientRects(),e=0<t.length?t[0]:n.getBoundingClientRect();return 0<e.width||0<e.height?on.some(e).map(Va):on.none()}(function(i,n){return Ha(i,n).match({ltr:function(n,t,e,o){var r=i.document.createRange();return r.setStart(n.dom(),t),r.setEnd(e.dom(),o),r},rtl:function(n,t,e,o){var r=i.document.createRange();return r.setStart(e.dom(),o),r.setEnd(n.dom(),t),r}})}(n,t))}function Gc(n){return n.fold(function(n){return n},function(n,t,e){return n.translate(-t,-e)})}function Xc(n){return n.fold(function(n){return n},function(n,t,e){return n})}function Yc(n){return O(n,function(n,t){return n.translate(t.left(),t.top())},Ru(0,0))}function qc(n){var t=S(n,Xc);return Yc(t)}function Kc(n,t,e){var o=ko(n.element()),r=hu(o),i=function(o,n,t){var e=Eo(t.root).dom();return on.from(e.frameElement).map(we.fromDom).filter(function(n){var t=ko(n),e=ko(o.element());return jt(t,e)}).map(mu)}(n,0,e).getOr(r);return mf(i,r.left(),r.top())}function Jc(n,t){return zr(n)?hf(n,t):function(n,t){var e=Do(n);if(0===e.length)return ff(n,t);if(t<e.length)return ff(e[t],0);var o=e[e.length-1],r=zr(o)?Ac(o).length:Do(o).length;return ff(o,r)}(n,t)}function $c(n,t){return t.getSelection.getOrThunk(function(){return function(){return function(n){return on.from(n.getSelection()).filter(function(n){return 0<n.rangeCount}).bind(Uc)}(n)}})().map(function(n){var t=Jc(n.start(),n.soffset()),e=Jc(n.finish(),n.foffset());return Bc.range(t.element(),t.offset(),e.element(),e.offset())})}function Qc(n){return n.x()+n.width()}function Zc(n,t){return n.x()-t.width()}function ns(n,t){return n.y()-t.height()+n.height()}function ts(n){return n.y()}function es(n,t,e){return qa(Qc(n),ts(n),e.southeast(),Ja(),"link-layout-se")}function os(n,t,e){return qa(Zc(n,t),ts(n),e.southwest(),$a(),"link-layout-sw")}function rs(n,t,e){return qa(Qc(n),ns(n,t),e.northeast(),Qa(),"link-layout-ne")}function is(n,t,e){return qa(Zc(n,t),ns(n,t),e.northwest(),Za(),"link-layout-nw")}function us(n,t,e,o){var r=n+t;return o<r?e:r<e?o:r}function as(n,t,e){return n<=t?t:e<=n?e:n}function cs(n,t,e,o){var r=n.x(),i=n.y(),u=n.bubble().offset().left(),a=n.bubble().offset().top(),c=o.y(),s=o.bottom(),f=o.x(),l=o.right(),d=i+a,m=function(n,t,e,o,r){var i=r.x(),u=r.y(),a=r.width(),c=r.height(),s=i<=n,f=u<=t,l=s&&f,d=n+e<=i+a&&t+o<=u+c,m=Math.abs(Math.min(e,s?i+a-n:i-(n+e))),g=Math.abs(Math.min(o,f?u+c-t:u-(t+o)));return{originInBounds:l,sizeInBounds:d,limitX:as(n,r.x(),r.right()),limitY:as(t,r.y(),r.bottom()),deltaW:m,deltaH:g}}(r+u,d,t,e,o),g=m.originInBounds,p=m.sizeInBounds,h=m.limitX,v=m.limitY,b=m.deltaW,y=m.deltaH,x=nn(v+y-c),w=nn(s-v),S=function(n,t,e,o){return n.fold(t,t,o,o,t,o,e,e)}(n.direction(),w,w,x),C=nn(h+b-f),k=nn(l-h),O=function(n,t,e,o){return n.fold(t,o,t,o,e,e,t,o)}(n.direction(),k,k,C),E=pc({x:h,y:v,width:b,height:y,maxHeight:S,maxWidth:O,direction:n.direction(),classes:{on:n.bubble().classesOn(),off:n.bubble().classesOff()},label:n.label(),candidateYforTest:d});return g&&p?Sf.fit(E):Sf.nofit(E,b,y)}function ss(n,t,e,o){Or(t,"max-height"),Or(t,"max-width");var r=function(n){return{width:nn(pu(n)),height:nn(lu(n))}}(t);return function(n,e,u,a,c){function o(n,o,r,i){var t=n(e,u,a);return cs(t,s,f,c).fold(Sf.fit,function(n,t,e){return i<e||r<t?Sf.nofit(n,t,e):Sf.nofit(o,r,i)})}var s=u.width(),f=u.height();return O(n,function(n,t){var e=d(o,t);return n.fold(Sf.fit,e)},Sf.nofit(pc({x:e.x(),y:e.y(),width:u.width(),height:u.height(),maxHeight:u.height(),maxWidth:u.width(),direction:Ja(),classes:{on:[],off:[]},label:"none",candidateYforTest:e.y()}),-1,-1)).fold(l,l)}(o.preference(),n,r,e,o.bounds())}function fs(n,t,e){function o(n){return n+"px"}var r=function(n,r){return n.fold(function(){return hc("absolute",on.some(r.x()),on.some(r.y()),on.none(),on.none())},function(n,t,e,o){return Ba("absolute",r,n,t,e,o)},function(n,t,e,o){return Ba("fixed",r,n,t,e,o)})}(e.origin(),t);!function(n,t){var e=n.dom();Cn(t,function(n,t){n.fold(function(){yr(e,t)},function(n){br(e,t,n)})})}(n,{position:on.some(r.position()),left:r.left().map(o),top:r.top().map(o),right:r.right().map(o),bottom:r.bottom().map(o)})}function ls(n,t){!function(n,t){var e=Fu.max(n,t,["margin-top","border-top-width","padding-top","padding-bottom","border-bottom-width","margin-bottom"]);xr(n,"max-height",e+"px")}(n,Math.floor(t))}function ds(n,t,e){return n[t]===undefined?e:n[t]}function ms(n,t,e,o,r,i){var u=ds(i,"maxHeightFunction",Cf()),a=ds(i,"maxWidthFunction",Z),c=n.anchorBox(),s=n.origin(),f=Of({bounds:function(o,n){return n.fold(function(){return o.fold(Cu,Cu,xu)},function(e){return o.fold(e,e,function(){var n=e(),t=bc(o,n.x(),n.y());return xu(t.left(),t.top(),n.width(),n.height())})})}(s,r),origin:s,preference:o,maxHeightFunction:u,maxWidthFunction:a});Ef(c,t,e,f)}function gs(n,t,e,o,r){var i=function(n,t){return wf(n,t)}(e.anchorBox,t);ms(i,r.element(),e.bubble,e.layouts,o,e.overrides)}function ps(n,t){Pi(n.element(),t.element())}function hs(t,n){var e=t.components();!function(n){bn(n.components(),function(n){return zi(n.element())}),Ro(n.element()),n.syncComponents()}(t);var o=_(e,n);bn(o,function(n){Mf(n),t.getSystem().removeFromWorld(n)}),bn(n,function(n){n.getSystem().isConnected()?ps(t,n):(t.getSystem().addToWorld(n),ps(t,n),Qe(t.element())&&Ff(n)),t.syncComponents()})}function vs(n,t){If(n,t,Pi)}function bs(n){Mf(n),zi(n.element()),n.getSystem().removeFromWorld(n)}function ys(t){var n=To(t.element()).bind(function(n){return t.getSystem().getByDom(n).toOption()});bs(t),n.each(function(n){n.syncComponents()})}function xs(n){var t=n.components();bn(t,bs),Ro(n.element()),n.syncComponents()}function ws(n,t){Rf(n,t,Pi)}function Ss(t){var n=Do(t.element());bn(n,function(n){t.getByDom(n).each(Mf)}),zi(t.element())}function Cs(t,n,e,o){e.get().each(function(n){xs(t)});var r=n.getAttachPoint(t);vs(r,t);var i=t.getSystem().build(o);return vs(t,i),e.set(i),i}function ks(n,t,e,o){var r=Cs(n,t,e,o);return t.onOpen(n,r),r}function Os(t,e,o){o.get().each(function(n){xs(t),ys(t),e.onClose(t,n),o.clear()})}function Es(n,t,e){return e.isOpen()}function Ts(n){var t,e=ot("Dismissal",Gf,n);return(t={})[jf()]={schema:qn([ct("target")]),onReceive:function(t,n){Lf.isOpen(t)&&(Lf.isPartOf(t,n.target)||e.isExtraPart(t,n.target)||e.fireEventInstead.fold(function(){return Lf.close(t)},function(n){return ro(t,n.event)}))}},t}function Bs(n){var t,e=ot("Reposition",Xf,n);return(t={})[Uf()]={onReceive:function(t){Lf.isOpen(t)&&e.fireEventInstead.fold(function(){return e.doReposition(t)},function(n){return ro(t,n.event)})}},t}function Ds(n,t,e){t.store.manager.onLoad(n,t,e)}function As(n,t,e){t.store.manager.onUnload(n,t,e)}function _s(){var n=ye(null);return tu({set:n.set,get:n.get,isNotSet:function(){return null===n.get()},clear:function(){n.set(null)},readState:function(){return{mode:"memory",value:n.get()}}})}function Ms(){var i=ye({}),u=ye({});return tu({readState:function(){return{mode:"dataset",dataByValue:i.get(),dataByText:u.get()}},lookup:function(n){return Nn(i.get(),n).orThunk(function(){return Nn(u.get(),n)})},update:function(n){var t=i.get(),e=u.get(),o={},r={};bn(n,function(t){o[t.value]=t,Nn(t,"meta").each(function(n){Nn(n,"text").each(function(n){r[n]=t})})}),i.set(N(N({},t),o)),u.set(N(N({},e),r))},clear:function(){i.set({}),u.set({})}})}function Fs(n,t,e,o){var r=t.store;e.update([o]),r.setValue(n,o),t.onSetValue(n,o)}function Is(t,n){return Dt(t,{},S(n,function(n){return function(t,e){return ce(t,t,Fn(),Yn(function(n){return qt("The field: "+t+" is forbidden. "+e)}))}(n.name(),"Cannot configure "+n.name()+" for "+t)}).concat([At("dump",l)]))}function Rs(n){return n.dump}function Vs(n,t){return N(N({},n.dump),ya(t))}function Ns(n,t,e,o){return e.uiType===ol?function(n,t,e,o){return n.exists(function(n){return n!==e.owner})?rl.single(!0,nn(e)):Nn(o,e.name).fold(function(){throw new Error("Unknown placeholder component: "+e.name+"\nKnown: ["+wn(o)+"]\nNamespace: "+n.getOr("none")+"\nSpec: "+JSON.stringify(e,null,2))},function(n){return n.replace()})}(n,0,e,o):rl.single(!1,nn(e))}function Hs(t,e,n,o){var r=P(o,function(n,t){return function(n,t){var e=!1;return{name:nn(n),required:function(){return t.fold(function(n,t){return n},function(n,t){return n})},used:function(){return e},replace:function(){if(!0===e)throw new Error("Trying to use the same placeholder more than once: "+n);return e=!0,t}}}(t,n)}),i=function(t,e,n,o){return B(n,function(n){return il(t,e,n,o)})}(t,e,n,r);return Cn(r,function(n){if(!1===n.used()&&n.required())throw new Error("Placeholder: "+n.name()+" was not found in components list\nNamespace: "+t.getOr("none")+"\nComponents: "+JSON.stringify(e.components,null,2))}),i}function Ps(n){return n.fold(on.some,on.none,on.some,on.some)}function zs(n){function t(n){return n.name}return n.fold(t,t,t,t)}function Ls(e,o){return function(n){var t=ot("Converting part type",o,n);return e(t)}}function js(n,t,e,o){return Dn(t.defaults(n,e,o),e,{uid:n.partUids[t.name]},t.overrides(n,e,o))}function Us(r,n){var t={};return bn(n,function(n){Ps(n).each(function(e){var o=Tl(r,e.pname);t[e.name]=function(n){var t=ot("Part: "+e.name+" in "+r,re(e.schema),n);return N(N({},o),{config:n,validated:t})}})}),t}function Ws(n,t,e){return{uiType:cl(),owner:n,name:t,config:e,validated:{}}}function Gs(n){return B(n,function(n){return n.fold(on.none,on.some,on.none,on.none).map(function(n){return mt(n.name,n.schema.concat([na(Ol())]))}).toArray()})}function Xs(n){return S(n,zs)}function Ys(n,t,e){return function(n,e,t){var i={},o={};return bn(t,function(n){n.fold(function(o){i[o.pname]=ul(!0,function(n,t,e){return o.factory.sketch(js(n,o,t,e))})},function(n){var t=e.parts[n.name];o[n.name]=nn(n.factory.sketch(js(e,n,t[Ol()]),t))},function(o){i[o.pname]=ul(!1,function(n,t,e){return o.factory.sketch(js(n,o,t,e))})},function(r){i[r.pname]=al(!0,function(t,n,e){var o=t[r.name];return S(o,function(n){return r.factory.sketch(Dn(r.defaults(t,n,e),n,r.overrides(t,n)))})})})}),{internals:nn(i),externals:nn(o)}}(0,t,e)}function qs(n,t,e){return Hs(on.some(n),t,t.components,e)}function Ks(n,t,e){var o=t.partUids[e];return n.getSystem().getByUid(o).toOption()}function Js(n,t,e){return Ks(n,t,e).getOrDie("Could not find part: "+e)}function $s(n,t,e){var o={},r=t.partUids,i=n.getSystem();return bn(e,function(n){o[n]=nn(i.getByUid(r[n]))}),o}function Qs(n,t){var e=n.getSystem();return P(t.partUids,function(n,t){return nn(e.getByUid(n))})}function Zs(n){return wn(n.partUids)}function nf(n,t,e){var o={},r=t.partUids,i=n.getSystem();return bn(e,function(n){o[n]=nn(i.getByUid(r[n]).getOrDie())}),o}function tf(t,n){var e=Xs(n);return K(S(e,function(n){return{key:n,value:t+"-"+n}}))}function ef(t){return ce("partUids","partUids",Rn(function(n){return tf(n.uid,t)}),de())}function of(n,t,e,o,r){var i=function(n,t){return(0<n.length?[mt("parts",n)]:[]).concat([ct("uid"),St("dom",{}),St("components",[]),na("originalSpec"),St("debug.sketcher",{})]).concat(t)}(o,r);return ot(n+" [SpecSchema]",qn(i.concat(t)),e)}function rf(n,t,e,o,r){var i=Dl(r),u=Gs(e),a=ef(e),c=of(n,t,i,u,[a]),s=Ys(0,c,e);return o(c,qs(n,c,s.internals()),i,s.externals())}var uf=function iI(e,o){var t=function(n){return e(n)?on.from(n.dom().nodeValue):on.none()};return{get:function(n){if(!e(n))throw new Error("Can only get "+o+" value of a "+o+" node");return t(n).getOr("")},getOption:t,set:function(n,t){if(!e(n))throw new Error("Can only set raw "+o+" value of a "+o+" node");n.dom().nodeValue=t}}}(zr,"text"),af=function(n,t,e,o){return zr(t)?function(t,e,o,r){var n=t.dom().createRange();n.selectNode(e.dom());var i=n.getClientRects();return Au(i,function(n){return Pa(n,o,r)?on.some(n):on.none()}).map(function(n){return Mc(t,e,o,r,n)})}(n,t,e,o):function(t,n,e,o){var r=t.dom().createRange(),i=Do(n);return Au(i,function(n){return r.selectNode(n.dom()),Pa(r.getBoundingClientRect(),e,o)?af(t,n,e,o):on.none()})}(n,t,e,o)},cf=["img","br"],sf=function(n,i){var u=function(n){for(var t=Do(n),e=t.length-1;0<=e;e--){var o=t[e];if(i(o))return on.some(o);var r=u(o);if(r.isSome())return r}return on.none()};return u(n)},ff=(document.caretPositionFromPoint||document.caretRangeFromPoint,xo("element","offset")),lf=Tn([{screen:["point"]},{absolute:["point","scrollLeft","scrollTop"]}]),df=lf.screen,mf=lf.absolute,gf=function(n,t,e,o){var r=n,i=t,u=e,a=o;n<0&&(r=0,u=e+n),t<0&&(i=0,a=o+t);var c=df(Ru(r,i));return on.some(zu(c,u,a))},pf=function(n,i,u,a,c){return n.map(function(n){var t=[i,n.point()],e=function(n,t,e,o){return n.fold(t,e,o)}(a,function(){return qc(t)},function(){return qc(t)},function(){return function(n){var t=S(n,Gc);return Yc(t)}(t)}),o=Lu(e.left(),e.top(),n.width(),n.height()),r=Fa(c,u,u.showAbove?[sa,fa,aa,ca,rc,ic]:[aa,ca,sa,fa,ic,ic],u.showAbove?[fa,sa,ca,aa,rc,ic]:[ca,aa,fa,sa,ic,rc]);return Aa({anchorBox:o,bubble:u.bubble.getOr(Ta()),overrides:u.overrides,layouts:r,placer:on.none()})})},hf=xo("element","offset"),vf=[ht("getSelection"),ct("root"),ht("bubble"),Ma(),St("overrides",{}),St("showAbove",!1),Zu("placement",function(n,t,e){var o=Eo(t.root).dom(),r=Kc(n,0,t),i=$c(o,t).bind(function(n){return Wc(o,Bc.exactFromRange(n)).orThunk(function(){var t=we.fromText("\ufeff");return _o(n.start(),t),Wc(o,Bc.exact(t,0,t,1)).map(function(n){return zi(t),n})}).bind(function(n){return gf(n.left(),n.top(),n.width(),n.height())})}),u=$c(o,t).bind(function(n){return Pr(n.start())?on.some(n.start()):To(n.start())}).getOr(n.element());return pf(i,r,t,e,u)})],bf=[ct("node"),ct("root"),ht("bubble"),Ma(),St("overrides",{}),St("showAbove",!1),Zu("placement",function(r,i,u){var a=Kc(r,0,i);return i.node.bind(function(n){var t=n.dom().getBoundingClientRect(),e=gf(t.left,t.top,t.width,t.height),o=i.node.getOr(r.element());return pf(e,a,i,u,o)})})],yf=[ct("item"),Ma(),St("overrides",{}),Zu("placement",function(n,t,e){var o=Da(e,t.item.element()),r=Fa(n.element(),t,[es,os,rs,is],[os,es,is,rs]);return on.some(Aa({anchorBox:o,bubble:Ta(),overrides:t.overrides,layouts:r,placer:on.none()}))})],xf=it("anchor",{selection:vf,node:bf,hotspot:Sc,submenu:yf,makeshift:Cc}),wf=xo("anchorBox","origin"),Sf=Tn([{fit:["reposition"]},{nofit:["reposition","deltaW","deltaH"]}]),Cf=nn(function(n,t){ls(n,t),wr(n,{"overflow-x":"hidden","overflow-y":"auto"})}),kf=nn(function(n,t){ls(n,t)}),Of=Co(["bounds","origin","preference","maxHeightFunction","maxWidthFunction"],[]),Ef=function(n,t,e,o){var r=ss(n,t,e,o);fs(t,r,o),function(n,t){var e=t.classes();hr(n,e.off),pr(n,e.on)}(t,r),function(n,t,e){e.maxHeightFunction()(n,t.maxHeight())}(t,r,o),function(n,t,e){e.maxWidthFunction()(n,t.maxWidth())}(t,r,o)},Tf=function(n,t,e,o,r,i){var u=i.map(wu);return Bf(n,t,e,o,r,u)},Bf=function(r,i,n,t,u,a){var c=ot("positioning anchor.info",xf,t);Oa(function(){xr(u.element(),"position","fixed");var n=Cr(u.element(),"visibility");xr(u.element(),"visibility","hidden");var t=i.useFixed()?function(){var n=v.document.documentElement;return xc(0,0,n.clientWidth,n.clientHeight)}():function(n){var t=mu(n.element()),e=n.element().dom().getBoundingClientRect();return yc(t.left(),t.top(),e.width,e.height)}(r),e=c.placement,o=a.map(nn).or(i.getBounds);e(r,c,t).each(function(n){n.placer.getOr(gs)(r,t,n,o,u)}),n.fold(function(){Or(u.element(),"visibility")},function(n){xr(u.element(),"visibility",n)}),Cr(u.element(),"left").isNone()&&Cr(u.element(),"top").isNone()&&Cr(u.element(),"right").isNone()&&Cr(u.element(),"bottom").isNone()&&Cr(u.element(),"position").is("fixed")&&Or(u.element(),"position")},u.element())},Df=/* */Object.freeze({position:function(n,t,e,o,r){Tf(n,t,e,o,r,on.none())},positionWithin:Tf,positionWithinBounds:Bf,getMode:function(n,t,e){return t.useFixed()?"fixed":"absolute"}}),Af=[St("useFixed",u),ht("getBounds")],_f=xa({fields:Af,name:"positioning",active:mc,apis:Df}),Mf=function(n){ro(n,Oi());var t=n.components();bn(t,Mf)},Ff=function(n){var t=n.components();bn(t,Ff),ro(n,ki())},If=function(n,t,e){n.getSystem().addToWorld(t),e(n.element(),t.element()),Qe(n.element())&&Ff(t),n.syncComponents()},Rf=function(n,t,e){e(n,t.element());var o=Do(t.element());bn(o,function(n){t.getByDom(n).each(Ff)})},Vf=function(n,t,e){var o=t.getAttachPoint(n);xr(n.element(),"position",_f.getMode(o)),function(t,n,e,o){Cr(t.element(),n).fold(function(){Uo(t.element(),e)},function(n){zo(t.element(),e,n)}),xr(t.element(),n,o)}(n,"visibility",t.cloakVisibilityAttr,"hidden")},Nf=function(n,t,e){!function(t){return x(["top","left","right","bottom"],function(n){return Cr(t,n).isSome()})}(n.element())&&Or(n.element(),"position"),function(n,t,e){if(jo(n.element(),e)){var o=Lo(n.element(),e);xr(n.element(),t,o)}else Or(n.element(),t)}(n,"visibility",t.cloakVisibilityAttr)},Hf=/* */Object.freeze({cloak:Vf,decloak:Nf,open:ks,openWhileCloaked:function(n,t,e,o,r){Vf(n,t),ks(n,t,e,o),r(),Nf(n,t)},close:Os,isOpen:Es,isPartOf:function(t,e,n,o){return Es(0,0,n)&&n.get().exists(function(n){return e.isPartOf(t,n,o)})},getState:function(n,t,e){return e.get()},setContent:function(n,t,e,o){return e.get().map(function(){return Cs(n,t,e,o)})}}),Pf=/* */Object.freeze({events:function(e,o){return so([mo(vi(),function(n,t){Os(n,e,o)})])}}),zf=[Ku("onOpen"),Ku("onClose"),ct("isPartOf"),ct("getAttachPoint"),St("cloakVisibilityAttr","data-precloak-visibility")],Lf=xa({fields:zf,name:"sandboxing",active:Pf,apis:Hf,state:/* */Object.freeze({init:function(){var t=ye(on.none()),n=nn("not-implemented");return tu({readState:n,isOpen:function(){return t.get().isSome()},clear:function(){t.set(on.none())},set:function(n){t.set(on.some(n))},get:function(n){return t.get()}})}})}),jf=nn("dismiss.popups"),Uf=nn("reposition.popups"),Wf=nn("mouse.released"),Gf=qn([St("isExtraPart",nn(!1)),wt("fireEventInstead",[St("event",Ei())])]),Xf=qn([St("isExtraPart",nn(!1)),wt("fireEventInstead",[St("event",Ti())]),dt("doReposition")]),Yf=/* */Object.freeze({onLoad:Ds,onUnload:As,setValue:function(n,t,e,o){t.store.manager.setValue(n,t,e,o)},getValue:function(n,t,e){return t.store.manager.getValue(n,t,e)},getState:function(n,t,e){return e}}),qf=/* */Object.freeze({events:function(e,o){var n=e.resetOnDom?[Ri(function(n,t){Ds(n,e,o)}),Vi(function(n,t){As(n,e,o)})]:[pa(e,o,Ds)];return so(n)}}),Kf=/* */Object.freeze({memory:_s,dataset:Ms,manual:function(){return tu({readState:function(){}})},init:function(n){return n.store.manager.state(n)}}),Jf=[ht("initialValue"),ct("getFallbackEntry"),ct("getDataKey"),ct("setValue"),Zu("manager",{setValue:Fs,getValue:function(n,t,e){var o=t.store,r=o.getDataKey(n);return e.lookup(r).fold(function(){return o.getFallbackEntry(r)},function(n){return n})},onLoad:function(t,e,o){e.store.initialValue.each(function(n){Fs(t,e,o,n)})},onUnload:function(n,t,e){e.clear()},state:Ms})],$f=[ct("getValue"),St("setValue",Z),ht("initialValue"),Zu("manager",{setValue:function(n,t,e,o){t.store.setValue(n,o),t.onSetValue(n,o)},getValue:function(n,t,e){return t.store.getValue(n)},onLoad:function(t,e,n){e.store.initialValue.each(function(n){e.store.setValue(t,n)})},onUnload:Z,state:nu.init})],Qf=[ht("initialValue"),Zu("manager",{setValue:function(n,t,e,o){e.set(o),t.onSetValue(n,o)},getValue:function(n,t,e){return e.get()},onLoad:function(n,t,e){t.store.initialValue.each(function(n){e.isNotSet()&&e.set(n)})},onUnload:function(n,t,e){e.clear()},state:_s})],Zf=[Ct("store",{mode:"memory"},it("mode",{memory:Qf,manual:$f,dataset:Jf})),Ku("onSetValue"),St("resetOnDom",!1)],nl=xa({fields:Zf,name:"representing",active:qf,apis:Yf,extra:{setValueFrom:function(n,t){var e=nl.getValue(t);nl.setValue(n,e)}},state:Kf}),tl=Is,el=Vs,ol="placeholder",rl=Tn([{single:["required","valueThunk"]},{multiple:["required","valueThunks"]}]),il=function(i,u,a,c){return Ns(i,0,a,c).fold(function(n,t){var e=t(u,a.config,a.validated),o=Nn(e,"components").getOr([]),r=B(o,function(n){return il(i,u,n,c)});return[N(N({},e),{components:r})]},function(n,t){var e=t(u,a.config,a.validated);return a.validated.preprocess.getOr(l)(e)})},ul=rl.single,al=rl.multiple,cl=nn(ol),sl=Tn([{required:["data"]},{external:["data"]},{optional:["data"]},{group:["data"]}]),fl=St("factory",{sketch:l}),ll=St("schema",[]),dl=ct("name"),ml=ce("pname","pname",In(function(n){return"<alloy."+Yo(n.name)+">"}),de()),gl=At("schema",function(){return[ht("preprocess")]}),pl=St("defaults",nn({})),hl=St("overrides",nn({})),vl=re([fl,ll,dl,ml,pl,hl]),bl=re([fl,ll,dl,pl,hl]),yl=re([fl,ll,dl,ml,pl,hl]),xl=re([fl,gl,dl,ct("unit"),ml,pl,hl]),wl=Ls(sl.required,vl),Sl=Ls(sl.external,bl),Cl=Ls(sl.optional,yl),kl=Ls(sl.group,xl),Ol=nn("entirety"),El=/* */Object.freeze({required:wl,external:Sl,optional:Cl,group:kl,asNamedPart:Ps,name:zs,asCommon:function(n){return n.fold(l,l,l,l)},original:Ol}),Tl=function(n,t){return{uiType:cl(),owner:n,name:t}},Bl=/* */Object.freeze({generate:Us,generateOne:Ws,schemas:Gs,names:Xs,substitutes:Ys,components:qs,defaultUids:tf,defaultUidsSchema:ef,getAllParts:Qs,getAllPartNames:Zs,getPart:Ks,getPartOrDie:Js,getParts:$s,getPartsOrDie:nf}),Dl=function(n){return n.hasOwnProperty("uid")?n:N(N({},n),{uid:qo("uid")})};function Al(n){var t=ot("Sketcher for "+n.name,Jl,n),e=P(t.apis,Qo),o=P(t.extraApis,function(n,t){return Jo(n,t)});return N(N({name:nn(t.name),partFields:nn([]),configFields:nn(t.configFields),sketch:function(n){return function(n,t,e,o){var r=Dl(o);return e(of(n,t,r,[],[]),r)}(t.name,t.configFields,t.factory,n)}},e),o)}function _l(n){var t=ot("Sketcher for "+n.name,$l,n),e=Us(t.name,t.partFields),o=P(t.apis,Qo),r=P(t.extraApis,function(n,t){return Jo(n,t)});return N(N({name:nn(t.name),partFields:nn(t.partFields),configFields:nn(t.configFields),sketch:function(n){return rf(t.name,t.configFields,t.partFields,t.factory,n)},parts:nn(e)},o),r)}function Ml(n){return"input"===Je(n)&&"radio"!==Lo(n,"type")||"textarea"===Je(n)}function Fl(e,o,n,r){var t=Lc(e.element(),"."+o.highlightClass);bn(t,function(t){x(r,function(n){return n.element()===t})||(mr(t,o.highlightClass),e.getSystem().getByDom(t).each(function(n){o.onDehighlight(e,n),ro(n,Fi())}))})}function Il(n,t,e,o){Fl(n,t,0,[o]),td(n,t,e,o)||(lr(o.element(),t.highlightClass),t.onHighlight(n,o),ro(o,Mi()))}function Rl(e,t,n,o){var r=Lc(e.element(),"."+t.itemClass);return T(r,function(n){return gr(n,t.highlightClass)}).bind(function(n){var t=us(n,o,0,r.length-1);return e.getSystem().getByDom(r[t]).toOption()})}function Vl(n,t,e){var o=A(n.slice(0,t)),r=A(n.slice(t+1));return E(o.concat(r),e)}function Nl(n,t,e){var o=A(n.slice(0,t));return E(o,e)}function Hl(n,t,e){var o=n.slice(0,t),r=n.slice(t+1);return E(r.concat(o),e)}function Pl(n,t,e){var o=n.slice(t+1);return E(o,e)}function zl(e){return function(n){var t=n.raw();return vn(e,t.which)}}function Ll(n){return function(t){return D(n,function(n){return n(t)})}}function jl(n){return!0===n.raw().shiftKey}function Ul(n){return!0===n.raw().ctrlKey}function Wl(n,t){return{matches:n,classification:t}}function Gl(n,t,e){t.exists(function(t){return e.exists(function(n){return jt(n,t)})})||io(n,Bi(),{prevFocus:t,newFocus:e})}function Xl(){function r(n){return ka(n.element())}return{get:r,set:function(n,t){var e=r(n);n.getSystem().triggerFocus(t,n.element());var o=r(n);Gl(n,e,o)}}}function Yl(){function r(n){return cd.getHighlighted(n).map(function(n){return n.element()})}return{get:r,set:function(t,n){var e=r(t);t.getSystem().getByDom(n).fold(Z,function(n){cd.highlight(t,n)});var o=r(t);Gl(t,e,o)}}}var ql,Kl,Jl=qn([ct("name"),ct("factory"),ct("configFields"),St("apis",{}),St("extraApis",{})]),$l=qn([ct("name"),ct("factory"),ct("configFields"),ct("partFields"),St("apis",{}),St("extraApis",{})]),Ql=/* */Object.freeze({getCurrent:function(n,t,e){return t.find(n)}}),Zl=[ct("find")],nd=xa({fields:Zl,name:"composing",apis:Ql}),td=function(n,t,e,o){return gr(o.element(),t.highlightClass)},ed=function(n,t,e,o){var r=Lc(n.element(),"."+t.itemClass);return on.from(r[o]).fold(function(){return an.error("No element found with index "+o)},n.getSystem().getByDom)},od=function(t,n,e){return Ou(t.element(),"."+n.itemClass).bind(function(n){return t.getSystem().getByDom(n).toOption()})},rd=function(t,n,e){var o=Lc(t.element(),"."+n.itemClass);return(0<o.length?on.some(o[o.length-1]):on.none()).bind(function(n){return t.getSystem().getByDom(n).toOption()})},id=function(t,n,e){var o=Lc(t.element(),"."+n.itemClass);return Du(S(o,function(n){return t.getSystem().getByDom(n).toOption()}))},ud=/* */Object.freeze({dehighlightAll:function(n,t,e){return Fl(n,t,0,[])},dehighlight:function(n,t,e,o){td(n,t,e,o)&&(mr(o.element(),t.highlightClass),t.onDehighlight(n,o),ro(o,Fi()))},highlight:Il,highlightFirst:function(t,e,o){od(t,e).each(function(n){Il(t,e,o,n)})},highlightLast:function(t,e,o){rd(t,e).each(function(n){Il(t,e,o,n)})},highlightAt:function(t,e,o,n){ed(t,e,o,n).fold(function(n){throw new Error(n)},function(n){Il(t,e,o,n)})},highlightBy:function(t,e,o,n){var r=id(t,e);E(r,n).each(function(n){Il(t,e,o,n)})},isHighlighted:td,getHighlighted:function(t,n,e){return Ou(t.element(),"."+n.highlightClass).bind(function(n){return t.getSystem().getByDom(n).toOption()})},getFirst:od,getLast:rd,getPrevious:function(n,t,e){return Rl(n,t,0,-1)},getNext:function(n,t,e){return Rl(n,t,0,1)},getCandidates:id}),ad=[ct("highlightClass"),ct("itemClass"),Ku("onHighlight"),Ku("onDehighlight")],cd=xa({fields:ad,name:"highlighting",apis:ud}),sd=b(jl);(Kl=ql=ql||{}).OnFocusMode="onFocus",Kl.OnEnterOrSpaceMode="onEnterOrSpace",Kl.OnApiMode="onApi";function fd(n,t,e,i,u){function a(t,e,n,o,r){return function(n,t){return E(n,function(n){return n.matches(t)}).map(function(n){return n.classification})}(n(t,e,o,r),e.event()).bind(function(n){return n(t,e,o,r)})}var o={schema:function(){return n.concat([St("focusManager",Xl()),Ct("focusInside","onFocus",Zn(function(n){return vn(["onFocus","onEnterOrSpace","onApi"],n)?an.value(n):an.error("Invalid value for focusInside")})),Zu("handler",o),Zu("state",t),Zu("sendFocusIn",u)])},processKey:a,toEvents:function(o,r){var n=o.focusInside!==ql.OnFocusMode?on.none():u(o).map(function(e){return mo(si(),function(n,t){e(n,o,r),t.stop()})});return so(n.toArray().concat([mo(ti(),function(n,t){a(n,t,e,o,r).fold(function(){!function(t,e){var n=zl([32].concat([13]))(e.event());o.focusInside===ql.OnEnterOrSpaceMode&&n&&Ut(t,e)&&u(o).each(function(n){n(t,o,r),e.stop()})}(n,t)},function(n){t.stop()})}),mo(ei(),function(n,t){a(n,t,i,o,r).each(function(n){t.stop()})})]))}};return o}function ld(n){function i(n,t){var e=n.visibilitySelector.bind(function(n){return Eu(t,n)}).getOr(t);return 0<fu(e)}function t(t,e){(function(n,t){var e=Lc(n.element(),t.selector),o=C(e,function(n){return i(t,n)});return on.from(o[t.firstTabstop])})(t,e).each(function(n){e.focusManager.set(t,n)})}function u(t,n,e,o,r){return r(n,e,function(n){return function(n,t){return i(n,t)&&n.useTabstopAt(t)}(o,n)}).fold(function(){return o.cyclic?on.some(!0):on.none()},function(n){return o.focusManager.set(t,n),on.some(!0)})}function a(t,n,e,o){var r=Lc(t.element(),e.selector);return function(n,t){return t.focusManager.get(n).bind(function(n){return Eu(n,t.selector)})}(t,e).bind(function(n){return T(r,d(jt,n)).bind(function(n){return u(t,r,n,e,o)})})}var e=[ht("onEscape"),ht("onEnter"),St("selector",'[data-alloy-tabstop="true"]:not(:disabled)'),St("firstTabstop",0),St("useTabstopAt",nn(!0)),ht("visibilitySelector")].concat([n]),o=nn([Wl(Ll([jl,zl([9])]),function(n,t,e,o){var r=e.cyclic?Vl:Nl;return a(n,0,e,r)}),Wl(zl([9]),function(n,t,e,o){var r=e.cyclic?Hl:Pl;return a(n,0,e,r)}),Wl(zl([27]),function(t,e,n,o){return n.onEscape.bind(function(n){return n(t,e)})}),Wl(Ll([sd,zl([13])]),function(t,e,n,o){return n.onEnter.bind(function(n){return n(t,e)})})]),r=nn([]);return fd(e,nu.init,o,r,function(){return on.some(t)})}function dd(n,t,e){return Ml(e)&&zl([32])(t.event())?on.none():function(n,t,e){return ao(n,e,mi()),on.some(!0)}(n,0,e)}function md(n,t){return on.some(!0)}function gd(n,t,e){return e.execute(n,t,n.element())}function pd(n){var e=ye(on.none());return tu({readState:function(){return e.get().map(function(n){return{numRows:n.numRows(),numColumns:n.numColumns()}}).getOr({numRows:"?",numColumns:"?"})},setGridSize:function(n,t){e.set(on.some({numRows:nn(n),numColumns:nn(t)}))},getNumRows:function(){return e.get().map(function(n){return n.numRows()})},getNumColumns:function(){return e.get().map(function(n){return n.numColumns()})}})}function hd(i){return function(n,t,e,o){var r=i(n.element());return Tm(r,n,t,e,o)}}function vd(n,t){var e=_a(n,t);return hd(e)}function bd(n,t){var e=_a(t,n);return hd(e)}function yd(r){return function(n,t,e,o){return Tm(r,n,t,e,o)}}function xd(n){return!function(n){return n.offsetWidth<=0&&n.offsetHeight<=0}(n.dom())}function wd(n,t,e){var o=d(jt,t),r=Lc(n,e);return function(t,n){return T(t,n).map(function(n){return _m({index:n,candidates:t})})}(C(r,xd),o)}function Sd(n,t){return T(n,function(n){return jt(t,n)})}function Cd(e,n,o,t){return t(Math.floor(n/o),n%o).bind(function(n){var t=n.row()*o+n.column();return 0<=t&&t<e.length?on.some(e[t]):on.none()})}function kd(r,n,i,u,a){return Cd(r,n,u,function(n,t){var e=n===i-1?r.length-n*u:u,o=us(t,a,0,e-1);return on.some({row:nn(n),column:nn(o)})})}function Od(i,n,u,a,c){return Cd(i,n,a,function(n,t){var e=us(n,c,0,u-1),o=e===u-1?i.length-e*a:a,r=as(t,0,o-1);return on.some({row:nn(e),column:nn(r)})})}function Ed(t,e,n){Ou(t.element(),e.selector).each(function(n){e.focusManager.set(t,n)})}function Td(r){return function(n,t,e,o){return wd(n,t,e.selector).bind(function(n){return r(n.candidates(),n.index(),o.getNumRows().getOr(e.initSize.numRows),o.getNumColumns().getOr(e.initSize.numColumns))})}}function Bd(n,t,e,o){return e.captureTab?on.some(!0):on.none()}function Dd(n,t,e,r){var i=function(n,t,e){var o=us(t,r,0,e.length-1);return o===n?on.none():function(n){return"button"===Je(n)&&"disabled"===Lo(n,"disabled")}(e[o])?i(n,o,e):on.from(e[o])};return wd(n,e,t).bind(function(n){var t=n.index(),e=n.candidates();return i(t,t,e)})}function Ad(t,e,o){return function(n,t){return t.focusManager.get(n).bind(function(n){return Eu(n,t.selector)})}(t,o).bind(function(n){return o.execute(t,e,n)})}function _d(t,e){e.getInitial(t).orThunk(function(){return Ou(t.element(),e.selector)}).each(function(n){e.focusManager.set(t,n)})}function Md(n,t,e){return Dd(n,e.selector,t,-1)}function Fd(n,t,e){return Dd(n,e.selector,t,1)}function Id(o){return function(n,t,e){return o(n,t,e).bind(function(){return e.executeOnMove?Ad(n,t,e):on.some(!0)})}}function Rd(n,t,e,o){return e.onEscape(n,t)}function Vd(n,t,e){return on.from(n[t]).bind(function(n){return on.from(n[e]).map(function(n){return Um({rowIndex:t,columnIndex:e,cell:n})})})}function Nd(n,t,e,o){var r=n[t].length,i=us(e,o,0,r-1);return Vd(n,t,i)}function Hd(n,t,e,o){var r=us(e,o,0,n.length-1),i=n[r].length,u=as(t,0,i-1);return Vd(n,r,u)}function Pd(n,t,e,o){var r=n[t].length,i=as(e+o,0,r-1);return Vd(n,t,i)}function zd(n,t,e,o){var r=as(e+o,0,n.length-1),i=n[r].length,u=as(t,0,i-1);return Vd(n,r,u)}function Ld(t,e){e.previousSelector(t).orThunk(function(){var n=e.selectors;return Ou(t.element(),n.cell)}).each(function(n){e.focusManager.set(t,n)})}function jd(n,t){return function(r,e,i){var u=i.cycles?n:t;return Eu(e,i.selectors.row).bind(function(n){var t=Lc(n,i.selectors.cell);return Sd(t,e).bind(function(e){var o=Lc(r,i.selectors.row);return Sd(o,n).bind(function(n){var t=function(n,t){return S(n,function(n){return Lc(n,t.selectors.cell)})}(o,i);return u(t,n,e).map(function(n){return n.cell()})})})})}}function Ud(t,e,o){return o.focusManager.get(t).bind(function(n){return o.execute(t,e,n)})}function Wd(t,e){Ou(t.element(),e.selector).each(function(n){e.focusManager.set(t,n)})}function Gd(n,t,e){return Dd(n,e.selector,t,-1)}function Xd(n,t,e){return Dd(n,e.selector,t,1)}function Yd(n,t,e,o){var r=n.getSystem().build(o);If(n,r,e)}function qd(n,t,e,o){var r=mg(n);E(r,function(n){return jt(o.element(),n.element())}).each(ys)}function Kd(t,n,e,o,r){var i=mg(t);return on.from(i[o]).map(function(n){return qd(t,0,0,n),r.each(function(n){Yd(t,0,function(n,t){!function(n,t,e){Ao(n,e).fold(function(){Pi(n,t)},function(n){_o(n,t)})}(n,t,o)},n)}),n})}function Jd(n,t){return{key:n,value:{config:{},me:function(n,t){var e=so(t);return xa({fields:[ct("enabled")],name:n,active:{events:nn(e)}})}(n,t),configAsRaw:nn({}),initialConfig:{},state:nu}}}function $d(n,t){t.ignore||(Sa(n.element()),t.onFocus(n))}function Qd(n,t,e){var o=t.aria;o.update(n,o,e.get())}function Zd(t,n,e){n.toggleClass.each(function(n){e.get()?lr(t.element(),n):mr(t.element(),n)})}function nm(n,t,e){yg(n,t,e,!e.get())}function tm(n,t,e){e.set(!0),Zd(n,t,e),Qd(n,t,e)}function em(n,t,e){e.set(!1),Zd(n,t,e),Qd(n,t,e)}function om(n,t,e){yg(n,t,e,t.selected)}function rm(){function n(n,t){t.stop(),uo(n)}return[mo(ii(),n),mo(pi(),n),bo(Wr()),bo(qr())]}function im(n){return so(H([n.map(function(e){return Hi(function(n,t){e(n),t.stop()})}).toArray(),rm()]))}function um(n){(ka(n.element()).isNone()||bg.isFocused(n))&&(bg.isFocused(n)||bg.focus(n),io(n,Og,{item:n}))}function am(n){io(n,Eg,{item:n})}function cm(n,t){var e={};Cn(n,function(n,t){bn(n,function(n){e[n]=t})});var o=t,r=function(n){return kn(n,function(n,t){return{k:n,v:t}})}(t),i=P(r,function(n,t){return[t].concat(Hg(e,o,r,t))});return P(e,function(n){return Nn(i,n).getOr([n])})}function sm(n){return n.x()}function fm(n,t){return n.x()+n.width()/2-t.width()/2}function lm(n,t){return n.x()+n.width()-t.width()}function dm(n){return n.y()}function mm(n,t){return n.y()+n.height()-t.height()}function gm(n,t,e){return qa(sm(n),mm(n,t),e.innerSoutheast(),Ja(),"layout-se")}function pm(n,t,e){return qa(lm(n,t),mm(n,t),e.innerSouthwest(),$a(),"layout-sw")}function hm(n,t,e){return qa(sm(n),dm(n),e.innerNortheast(),Qa(),"layout-ne")}function vm(n,t,e){return qa(lm(n,t),dm(n),e.innerNorthwest(),Za(),"layout-nw")}function bm(n){var t=function e(n){return n.uid!==undefined}(n)&&$(n,"uid")?n.uid:qo("memento");return{get:function(n){return n.getSystem().getByUid(t).getOrDie()},getOpt:function(n){return n.getSystem().getByUid(t).toOption()},asSpec:function(){return N(N({},n),{uid:t})}}}function ym(n){return on.from(n()["temporary-placeholder"]).getOr("!not found!")}function xm(n,t){return on.from(t()[n]).getOrThunk(function(){return ym(t)})}var wm,Sm=ld(At("cyclic",nn(!1))),Cm=ld(At("cyclic",nn(!0))),km=[St("execute",dd),St("useSpace",!1),St("useEnter",!0),St("useControlEnter",!1),St("useDown",!1)],Om=fd(km,nu.init,function(n,t,e,o){var r=e.useSpace&&!Ml(n.element())?[32]:[],i=e.useEnter?[13]:[],u=e.useDown?[40]:[],a=r.concat(i).concat(u);return[Wl(zl(a),gd)].concat(e.useControlEnter?[Wl(Ll([Ul,zl([13])]),gd)]:[])},function(n,t,e,o){return e.useSpace&&!Ml(n.element())?[Wl(zl([32]),md)]:[]},function(){return on.none()}),Em=/* */Object.freeze({flatgrid:pd,init:function(n){return n.state(n)}}),Tm=function(t,e,n,o,r){return o.focusManager.get(e).bind(function(n){return t(e.element(),n,o,r)}).map(function(n){return o.focusManager.set(e,n),!0})},Bm=yd,Dm=yd,Am=yd,_m=Co(["index","candidates"],[]),Mm=[ct("selector"),St("execute",dd),Ju("onEscape"),St("captureTab",!1),Ya()],Fm=Td(function(n,t,e,o){return kd(n,t,e,o,-1)}),Im=Td(function(n,t,e,o){return kd(n,t,e,o,1)}),Rm=Td(function(n,t,e,o){return Od(n,t,e,o,-1)}),Vm=Td(function(n,t,e,o){return Od(n,t,e,o,1)}),Nm=nn([Wl(zl([37]),vd(Fm,Im)),Wl(zl([39]),bd(Fm,Im)),Wl(zl([38]),Bm(Rm)),Wl(zl([40]),Dm(Vm)),Wl(Ll([jl,zl([9])]),Bd),Wl(Ll([sd,zl([9])]),Bd),Wl(zl([27]),function(n,t,e,o){return e.onEscape(n,t)}),Wl(zl([32].concat([13])),function(t,e,o,n){return function(n,t){return t.focusManager.get(n).bind(function(n){return Eu(n,t.selector)})}(t,o).bind(function(n){return o.execute(t,e,n)})})]),Hm=nn([Wl(zl([32]),md)]),Pm=fd(Mm,pd,Nm,Hm,function(){return on.some(Ed)}),zm=[ct("selector"),St("getInitial",on.none),St("execute",dd),Ju("onEscape"),St("executeOnMove",!1),St("allowVertical",!0)],Lm=nn([Wl(zl([32]),md)]),jm=fd(zm,nu.init,function(n,t,e,o){var r=[37].concat(e.allowVertical?[38]:[]),i=[39].concat(e.allowVertical?[40]:[]);return[Wl(zl(r),Id(vd(Md,Fd))),Wl(zl(i),Id(bd(Md,Fd))),Wl(zl([13]),Ad),Wl(zl([32]),Ad),Wl(zl([27]),Rd)]},Lm,function(){return on.some(_d)}),Um=Co(["rowIndex","columnIndex","cell"],[]),Wm=[mt("selectors",[ct("row"),ct("cell")]),St("cycles",!0),St("previousSelector",on.none),St("execute",dd)],Gm=jd(function(n,t,e){return Nd(n,t,e,-1)},function(n,t,e){return Pd(n,t,e,-1)}),Xm=jd(function(n,t,e){return Nd(n,t,e,1)},function(n,t,e){return Pd(n,t,e,1)}),Ym=jd(function(n,t,e){return Hd(n,e,t,-1)},function(n,t,e){return zd(n,e,t,-1)}),qm=jd(function(n,t,e){return Hd(n,e,t,1)},function(n,t,e){return zd(n,e,t,1)}),Km=nn([Wl(zl([37]),vd(Gm,Xm)),Wl(zl([39]),bd(Gm,Xm)),Wl(zl([38]),Bm(Ym)),Wl(zl([40]),Dm(qm)),Wl(zl([32].concat([13])),function(t,e,o){return ka(t.element()).bind(function(n){return o.execute(t,e,n)})})]),Jm=nn([Wl(zl([32]),md)]),$m=fd(Wm,nu.init,Km,Jm,function(){return on.some(Ld)}),Qm=[ct("selector"),St("execute",dd),St("moveOnTab",!1)],Zm=nn([Wl(zl([38]),Am(Gd)),Wl(zl([40]),Am(Xd)),Wl(Ll([jl,zl([9])]),function(n,t,e){return e.moveOnTab?Am(Gd)(n,t,e):on.none()}),Wl(Ll([sd,zl([9])]),function(n,t,e){return e.moveOnTab?Am(Xd)(n,t,e):on.none()}),Wl(zl([13]),Ud),Wl(zl([32]),Ud)]),ng=nn([Wl(zl([32]),md)]),tg=fd(Qm,nu.init,Zm,ng,function(){return on.some(Wd)}),eg=[Ju("onSpace"),Ju("onEnter"),Ju("onShiftEnter"),Ju("onLeft"),Ju("onRight"),Ju("onTab"),Ju("onShiftTab"),Ju("onUp"),Ju("onDown"),Ju("onEscape"),St("stopSpaceKeyup",!1),ht("focusIn")],og=fd(eg,nu.init,function(n,t,e){return[Wl(zl([32]),e.onSpace),Wl(Ll([sd,zl([13])]),e.onEnter),Wl(Ll([jl,zl([13])]),e.onShiftEnter),Wl(Ll([jl,zl([9])]),e.onShiftTab),Wl(Ll([sd,zl([9])]),e.onTab),Wl(zl([38]),e.onUp),Wl(zl([40]),e.onDown),Wl(zl([37]),e.onLeft),Wl(zl([39]),e.onRight),Wl(zl([32]),e.onSpace),Wl(zl([27]),e.onEscape)]},function(n,t,e){return e.stopSpaceKeyup?[Wl(zl([32]),md)]:[]},function(n){return n.focusIn}),rg=Sm.schema(),ig=Cm.schema(),ug=jm.schema(),ag=Pm.schema(),cg=$m.schema(),sg=Om.schema(),fg=tg.schema(),lg=og.schema(),dg=wa({branchKey:"mode",branches:/* */Object.freeze({acyclic:rg,cyclic:ig,flow:ug,flatgrid:ag,matrix:cg,execution:sg,menu:fg,special:lg}),name:"keying",active:{events:function(n,t){return n.handler.toEvents(n,t)}},apis:{focusIn:function(t,e,o){e.sendFocusIn(e).fold(function(){t.getSystem().triggerFocus(t.element(),t.element())},function(n){n(t,e,o)})},setGridSize:function(n,t,e,o,r){$(e,"setGridSize")?e.setGridSize(o,r):v.console.error("Layout does not support setGridSize")}},state:Em}),mg=function(n,t){return n.components()},gg=xa({fields:[],name:"replacing",apis:/* */Object.freeze({append:function(n,t,e,o){Yd(n,0,Pi,o)},prepend:function(n,t,e,o){Yd(n,0,Fo,o)},remove:qd,replaceAt:Kd,replaceBy:function(t,n,e,o,r){var i=mg(t);return T(i,o).bind(function(n){return Kd(t,0,0,n,r)})},set:function(t,n,e,o){Oa(function(){var n=S(o,t.getSystem().build);hs(t,n)},t.element())},contents:mg})}),pg=/* */Object.freeze({focus:$d,blur:function(n,t){t.ignore||function(n){n.dom().blur()}(n.element())},isFocused:function(n){return function(n){var t=ko(n).dom();return n.dom()===t.activeElement}(n.element())}}),hg=/* */Object.freeze({exhibit:function(n,t){var e=t.ignore?{}:{attributes:{tabindex:"-1"}};return nr(e)},events:function(e){return so([mo(si(),function(n,t){$d(n,e),t.stop()})].concat(e.stopMousedown?[mo(qr(),function(n,t){t.event().prevent()})]:[]))}}),vg=[Ku("onFocus"),St("stopMousedown",!1),St("ignore",!1)],bg=xa({fields:vg,name:"focusing",active:hg,apis:pg}),yg=function(n,t,e,o){(o?tm:em)(n,t,e)},xg=/* */Object.freeze({onLoad:om,toggle:nm,isOn:function(n,t,e){return e.get()},on:tm,off:em,set:yg}),wg=/* */Object.freeze({exhibit:function(n,t,e){return nr({})},events:function(n,t){var e=function(t,e,o){return Hi(function(n){o(n,t,e)})}(n,t,nm),o=pa(n,t,om);return so(H([n.toggleOnExecute?[e]:[],[o]]))}}),Sg=function(n,t,e){zo(n.element(),"aria-expanded",e)},Cg=[St("selected",!1),ht("toggleClass"),St("toggleOnExecute",!0),Ct("aria",{mode:"none"},it("mode",{pressed:[St("syncWithExpanded",!1),Zu("update",function(n,t,e){zo(n.element(),"aria-pressed",e),t.syncWithExpanded&&Sg(n,t,e)})],checked:[Zu("update",function(n,t,e){zo(n.element(),"aria-checked",e)})],expanded:[Zu("update",Sg)],selected:[Zu("update",function(n,t,e){zo(n.element(),"aria-selected",e)})],none:[Zu("update",Z)]}))],kg=xa({fields:Cg,name:"toggling",active:wg,apis:xg,state:(wm=!1,{init:function(){var t=ye(wm);return{get:function(){return t.get()},set:function(n){return t.set(n)},clear:function(){return t.set(wm)},readState:function(){return t.get()}}}})}),Og="alloy.item-hover",Eg="alloy.item-focus",Tg=nn(Og),Bg=nn(Eg),Dg=[ct("data"),ct("components"),ct("dom"),St("hasSubmenu",!1),ht("toggling"),tl("itemBehaviours",[kg,bg,dg,nl]),St("ignoreFocus",!1),St("domModification",{}),Zu("builder",function(n){return{dom:n.dom,domModification:N(N({},n.domModification),{attributes:N(N(N({role:n.toggling.isSome()?"menuitemcheckbox":"menuitem"},n.domModification.attributes),{"aria-haspopup":n.hasSubmenu}),n.hasSubmenu?{"aria-expanded":!1}:{})}),behaviours:el(n.itemBehaviours,[n.toggling.fold(kg.revoke,function(n){return kg.config(N({aria:{mode:"checked"}},n))}),bg.config({ignore:n.ignoreFocus,stopMousedown:n.ignoreFocus,onFocus:function(n){am(n)}}),dg.config({mode:"execution"}),nl.config({store:{mode:"memory",initialValue:n.data}}),Jd("item-type-events",g(rm(),[mo(Qr(),um),mo(gi(),bg.focus)]))]),components:n.components,eventOrder:n.eventOrder}}),St("eventOrder",{})],Ag=[ct("dom"),ct("components"),Zu("builder",function(n){return{dom:n.dom,components:n.components,events:so([function(n){return mo(n,function(n,t){t.stop()})}(gi())])}})],_g=nn([wl({name:"widget",overrides:function(t){return{behaviours:ya([nl.config({store:{mode:"manual",getValue:function(n){return t.data},setValue:function(){}}})])}}})]),Mg=[ct("uid"),ct("data"),ct("components"),ct("dom"),St("autofocus",!1),St("ignoreFocus",!1),tl("widgetBehaviours",[nl,bg,dg]),St("domModification",{}),ef(_g()),Zu("builder",function(e){function o(n){return Ks(n,e,"widget").map(function(n){return dg.focusIn(n),n})}function n(n,t){return Ml(t.event().target())||e.autofocus&&t.setSource(n.element()),on.none()}var t=Ys(0,e,_g()),r=qs("item-widget",e,t.internals());return{dom:e.dom,components:r,domModification:e.domModification,events:so([Hi(function(n,t){o(n).each(function(n){t.stop()})}),mo(Qr(),um),mo(gi(),function(n,t){e.autofocus?o(n):bg.focus(n)})]),behaviours:el(e.widgetBehaviours,[nl.config({store:{mode:"memory",initialValue:e.data}}),bg.config({ignore:e.ignoreFocus,onFocus:function(n){am(n)}}),dg.config({mode:"special",focusIn:e.autofocus?function(n){o(n)}:sc(),onLeft:n,onRight:n,onEscape:function(n,t){return bg.isFocused(n)||e.autofocus?(e.autofocus&&t.setSource(n.element()),on.none()):(bg.focus(n),on.some(!0))}})])}})],Fg=it("type",{widget:Mg,item:Dg,separator:Ag}),Ig=nn([kl({factory:{sketch:function(n){var t=ot("menu.spec item",Fg,n);return t.builder(t)}},name:"items",unit:"item",defaults:function(n,t){return t.hasOwnProperty("uid")?t:N(N({},t),{uid:qo("item")})},overrides:function(n,t){return{type:t.type,ignoreFocus:n.fakeFocus,domModification:{classes:[n.markers.item]}}}})]),Rg=nn([ct("value"),ct("items"),ct("dom"),ct("components"),St("eventOrder",{}),Is("menuBehaviours",[cd,nl,nd,dg]),Ct("movement",{mode:"menu",moveOnTab:!0},it("mode",{grid:[Ya(),Zu("config",function(n,t){return{mode:"flatgrid",selector:"."+n.markers.item,initSize:{numColumns:t.initSize.numColumns,numRows:t.initSize.numRows},focusManager:n.focusManager}})],matrix:[Zu("config",function(n,t){return{mode:"matrix",selectors:{row:t.rowSelector,cell:"."+n.markers.item},focusManager:n.focusManager}}),ct("rowSelector")],menu:[St("moveOnTab",!0),Zu("config",function(n,t){return{mode:"menu",selector:"."+n.markers.item,moveOnTab:t.moveOnTab,focusManager:n.focusManager}})]})),st("markers",Ga()),St("fakeFocus",!1),St("focusManager",Xl()),Ku("onHighlight")]),Vg=nn("alloy.menu-focus"),Ng=_l({name:"Menu",configFields:Rg(),partFields:Ig(),factory:function(n,t,e,o){return{uid:n.uid,dom:n.dom,markers:n.markers,behaviours:Vs(n.menuBehaviours,[cd.config({highlightClass:n.markers.selectedItem,itemClass:n.markers.item,onHighlight:n.onHighlight}),nl.config({store:{mode:"memory",initialValue:n.value}}),nd.config({find:on.some}),dg.config(n.movement.config(n,n.movement))]),events:so([mo(Bg(),function(t,e){var n=e.event();t.getSystem().getByDom(n.target()).each(function(n){cd.highlight(t,n),e.stop(),io(t,Vg(),{menu:t,item:n})})}),mo(Tg(),function(n,t){var e=t.event().item();cd.highlight(n,e)})]),components:t,eventOrder:n.eventOrder,domModification:{attributes:{role:"menu"}}}}}),Hg=function(e,o,r,n){return Nn(r,n).bind(function(n){return Nn(e,n).bind(function(n){var t=Hg(e,o,r,n);return on.some([n].concat(t))})}).getOr([])},Pg=function(n){return"prepared"===n.type?on.some(n.menu):on.none()},zg={init:function(){function r(n,e,o){return f(n).bind(function(t){return function(e){return I(i.get(),function(n,t){return n===e})}(n).bind(function(n){return e(n).map(function(n){return{triggeredMenu:t,triggeringItem:n,triggeringPath:o}})})})}var i=ye({}),u=ye({}),a=ye({}),c=ye(on.none()),s=ye({}),f=function(n){return t(n).bind(Pg)},t=function(n){return Nn(u.get(),n)},e=function(n){return Nn(i.get(),n)};return{setMenuBuilt:function(n,t){var e;u.set(N(N({},u.get()),((e={})[n]={type:"prepared",menu:t},e)))},setContents:function(n,t,e,o){c.set(on.some(n)),i.set(e),u.set(t),s.set(o);var r=cm(o,e);a.set(r)},expand:function(e){return Nn(i.get(),e).map(function(n){var t=Nn(a.get(),e).getOr([]);return[n].concat(t)})},refresh:function(n){return Nn(a.get(),n)},collapse:function(n){return Nn(a.get(),n).bind(function(n){return 1<n.length?on.some(n.slice(1)):on.none()})},lookupMenu:t,lookupItem:e,otherMenus:function(n){var t=s.get();return _(wn(t),n)},getPrimary:function(){return c.get().bind(f)},getMenus:function(){return u.get()},clear:function(){i.set({}),u.set({}),a.set({}),c.set(on.none())},isClear:function(){return c.get().isNone()},getTriggeringPath:function(n,o){var t=C(e(n).toArray(),function(n){return f(n).isSome()});return Nn(a.get(),n).bind(function(n){var e=A(t.concat(n));return function(n){for(var t=[],e=0;e<n.length;e++){var o=n[e];if(!o.isSome())return on.none();t.push(o.getOrDie())}return on.some(t)}(B(e,function(n,t){return r(n,o,e.slice(0,t+1)).fold(function(){return c.get().is(n)?[]:[on.none()]},function(n){return[on.some(n)]})}))})}}},extractPreparedMenu:Pg},Lg=nn("collapse-item"),jg=Al({name:"TieredMenu",configFields:[Qu("onExecute"),Qu("onEscape"),$u("onOpenMenu"),$u("onOpenSubmenu"),$u("onRepositionMenu"),Ku("onCollapseMenu"),St("highlightImmediately",!0),mt("data",[ct("primary"),ct("menus"),ct("expansions")]),St("fakeFocus",!1),Ku("onHighlight"),Ku("onHover"),Xu(),ct("dom"),St("navigateOnHover",!0),St("stayInDom",!1),Is("tmenuBehaviours",[dg,cd,nd,gg]),St("eventOrder",{})],apis:{collapseMenu:function(n,t){n.collapseMenu(t)},highlightPrimary:function(n,t){n.highlightPrimary(t)},repositionMenus:function(n,t){n.repositionMenus(t)}},factory:function(a,n){function e(n){var t=function(o,r,n){return P(n,function(n,t){function e(){return Ng.sketch(N(N({dom:n.dom},n),{value:t,items:n.items,markers:a.markers,fakeFocus:a.fakeFocus,onHighlight:a.onHighlight,focusManager:a.fakeFocus?Yl():Xl()}))}return t===r?{type:"prepared",menu:o.getSystem().build(e())}:{type:"notbuilt",nbMenu:e}})}(n,a.data.primary,a.data.menus),e=o();return g.setContents(a.data.primary,t,a.data.expansions,e),g.getPrimary()}function c(n){return nl.getValue(n).value}function u(t,n){cd.highlight(t,n),cd.getHighlighted(n).orThunk(function(){return cd.getFirst(n)}).each(function(n){ao(t,n.element(),gi())})}function s(t,n){return Du(S(n,function(n){return t.lookupMenu(n).bind(function(n){return"prepared"===n.type?on.some(n.menu):on.none()})}))}function f(t,n,e){var o=s(n,n.otherMenus(e));bn(o,function(n){hr(n.element(),[a.markers.backgroundMenu]),a.stayInDom||gg.remove(t,n)})}function l(n,o){var t=function(o){return r.get().getOrThunk(function(){var e={},n=Lc(o.element(),"."+a.markers.item),t=C(n,function(n){return"true"===Lo(n,"aria-haspopup")});return bn(t,function(n){o.getSystem().getByDom(n).each(function(n){var t=c(n);e[t]=n})}),r.set(on.some(e)),e})}(n);Cn(t,function(n,t){var e=vn(o,t);zo(n.element(),"aria-expanded",e)})}function d(o,r,i){return on.from(i[0]).bind(function(n){return r.lookupMenu(n).bind(function(n){if("notbuilt"===n.type)return on.none();var t=n.menu,e=s(r,i.slice(1));return bn(e,function(n){lr(n.element(),a.markers.backgroundMenu)}),Qe(t.element())||gg.append(o,cu(t)),hr(t.element(),[a.markers.backgroundMenu]),u(o,t),f(o,r,i),on.some(t)})})}var m,t,r=ye(on.none()),g=zg.init(),o=function(n){return P(a.data.menus,function(n,t){return B(n.items,function(n){return"separator"===n.type?[]:[n.data.value]})})};(t=m=m||{})[t.HighlightSubmenu=0]="HighlightSubmenu",t[t.HighlightParent=1]="HighlightParent";function i(r,i,u){void 0===u&&(u=m.HighlightSubmenu);var n=c(i);return g.expand(n).bind(function(o){return l(r,o),on.from(o[0]).bind(function(e){return g.lookupMenu(e).bind(function(n){var t=function(n,t,e){if("notbuilt"!==e.type)return e.menu;var o=n.getSystem().build(e.nbMenu());return g.setMenuBuilt(t,o),o}(r,e,n);return Qe(t.element())||gg.append(r,cu(t)),a.onOpenSubmenu(r,i,t,A(o)),u===m.HighlightSubmenu?(cd.highlightFirst(t),d(r,g,o)):(cd.dehighlightAll(t),on.some(i))})})})}function p(t,e){var n=c(e);return g.collapse(n).bind(function(n){return l(t,n),d(t,g,n).map(function(n){return a.onCollapseMenu(t,e,n),n})})}function h(e){return function(t,n){return Eu(n.getSource(),"."+a.markers.item).bind(function(n){return t.getSystem().getByDom(n).toOption().bind(function(n){return e(t,n).map(function(){return!0})})})}}function v(n){return cd.getHighlighted(n).bind(cd.getHighlighted)}var b=so([mo(Vg(),function(e,o){var n=o.event().item();g.lookupItem(c(n)).each(function(){var n=o.event().menu();cd.highlight(e,n);var t=c(o.event().item());g.refresh(t).each(function(n){return f(e,g,n)})})}),Hi(function(t,n){var e=n.event().target();t.getSystem().getByDom(e).each(function(n){0===c(n).indexOf("collapse-item")&&p(t,n),i(t,n,m.HighlightSubmenu).fold(function(){a.onExecute(t,n)},function(){})})}),Ri(function(t,n){e(t).each(function(n){gg.append(t,cu(n)),a.onOpenMenu(t,n),a.highlightImmediately&&u(t,n)})})].concat(a.navigateOnHover?[mo(Tg(),function(n,t){var e=t.event().item();!function(t,n){var e=c(n);g.refresh(e).bind(function(n){return l(t,n),d(t,g,n)})}(n,e),i(n,e,m.HighlightParent),a.onHover(n,e)})]:[])),y={collapseMenu:function(t){v(t).each(function(n){p(t,n)})},highlightPrimary:function(t){g.getPrimary().each(function(n){u(t,n)})},repositionMenus:function(o){g.getPrimary().bind(function(t){return v(o).bind(function(n){var t=c(n),e=R(g.getMenus()),o=Du(S(e,zg.extractPreparedMenu));return g.getTriggeringPath(t,function(n){return function(n,t,e){return Au(t,function(n){if(!n.getSystem().isConnected())return on.none();var t=cd.getCandidates(n);return E(t,function(n){return c(n)===e})})}(0,o,n)})}).map(function(n){return{primary:t,triggeringPath:n}})}).fold(function(){(function(n){return on.from(n.components()[0]).filter(function(n){return"menu"===Lo(n.element(),"role")})})(o).each(function(n){a.onRepositionMenu(o,n,[])})},function(n){var t=n.primary,e=n.triggeringPath;a.onRepositionMenu(o,t,e)})}};return{uid:a.uid,dom:a.dom,markers:a.markers,behaviours:Vs(a.tmenuBehaviours,[dg.config({mode:"special",onRight:h(function(n,t){return Ml(t.element())?on.none():i(n,t,m.HighlightSubmenu)}),onLeft:h(function(n,t){return Ml(t.element())?on.none():p(n,t)}),onEscape:h(function(n,t){return p(n,t).orThunk(function(){return a.onEscape(n,t).map(function(){return n})})}),focusIn:function(t,n){g.getPrimary().each(function(n){ao(t,n.element(),gi())})}}),cd.config({highlightClass:a.markers.selectedMenu,itemClass:a.markers.menu}),nd.config({find:function(n){return cd.getHighlighted(n)}}),gg.config({})]),eventOrder:a.eventOrder,apis:y,events:b}},extraApis:{tieredData:function(n,t,e){return{primary:n,menus:t,expansions:e}},singleData:function(n,t){return{primary:n,menus:q(n,t),expansions:{}}},collapseItem:function(n){return{value:Yo(Lg()),meta:{text:n}}}}}),Ug=Al({name:"InlineView",configFields:[ct("lazySink"),Ku("onShow"),Ku("onHide"),xt("onEscape"),Is("inlineBehaviours",[Lf,nl,dc]),wt("fireDismissalEventInstead",[St("event",Ei())]),wt("fireRepositionEventInstead",[St("event",Ti())]),St("getRelated",on.none),St("eventOrder",on.none)],factory:function(i,n){function t(e){Lf.isOpen(e)&&nl.getValue(e).each(function(n){switch(n.mode){case"menu":Lf.getState(e).each(function(n){jg.repositionMenus(n)});break;case"position":var t=i.lazySink(e).getOrDie();_f.positionWithinBounds(t,n.anchor,e,n.getBounds())}})}var o=function(n,t,e,o){r(n,t,e,function(){return o.map(function(n){return wu(n)})})},r=function(n,t,e,o){var r=i.lazySink(n).getOrDie();Lf.openWhileCloaked(n,e,function(){return _f.positionWithinBounds(r,t,n,o())}),nl.setValue(n,on.some({mode:"position",anchor:t,getBounds:o}))},u=function(n,t,e,o){var r=function(n,t,r,e,i){function u(){return n.lazySink(t)}function a(n){return function(n){return 2===n.length}(n)?o:{}}var o="horizontal"===e.type?{layouts:{onLtr:function(){return ma()},onRtl:function(){return ga()}}}:{};return jg.sketch({dom:{tag:"div"},data:e.data,markers:e.menu.markers,onEscape:function(){return Lf.close(t),n.onEscape.map(function(n){return n(t)}),on.some(!0)},onExecute:function(){return on.some(!0)},onOpenMenu:function(n,t){_f.positionWithinBounds(u().getOrDie(),r,t,i())},onOpenSubmenu:function(n,t,e,o){var r=u().getOrDie();_f.position(r,N({anchor:"submenu",item:t},a(o)),e)},onRepositionMenu:function(n,t,e){var o=u().getOrDie();_f.positionWithinBounds(o,r,t,i()),bn(e,function(n){var t=a(n.triggeringPath);_f.position(o,N({anchor:"submenu",item:n.triggeringItem},t),n.triggeredMenu)})}})}(i,n,t,e,o);Lf.open(n,r),nl.setValue(n,on.some({mode:"menu",menu:r}))},e={setContent:function(n,t){Lf.setContent(n,t)},showAt:function(n,t,e){o(n,t,e,on.none())},showWithin:o,showWithinBounds:r,showMenuAt:function(n,t,e){u(n,t,e,function(){return on.none()})},showMenuWithinBounds:u,hide:function(n){nl.setValue(n,on.none()),Lf.close(n)},getContent:function(n){return Lf.getState(n)},reposition:t,isOpen:Lf.isOpen};return{uid:i.uid,dom:i.dom,behaviours:Vs(i.inlineBehaviours,[Lf.config({isPartOf:function(n,t,e){return ju(t,e)||function(n,t){return i.getRelated(n).exists(function(n){return ju(n,t)})}(n,e)},getAttachPoint:function(n){return i.lazySink(n).getOrDie()},onOpen:function(n){i.onShow(n)},onClose:function(n){i.onHide(n)}}),nl.config({store:{mode:"memory",initialValue:on.none()}}),dc.config({channels:N(N({},Ts(N({isExtraPart:nn(!1)},i.fireDismissalEventInstead.map(function(n){return{fireEventInstead:{event:n.event}}}).getOr({})))),Bs(N(N({isExtraPart:nn(!1)},i.fireRepositionEventInstead.map(function(n){return{fireEventInstead:{event:n.event}}}).getOr({})),{doReposition:t})))})]),eventOrder:i.eventOrder,apis:e}},apis:{showAt:function(n,t,e,o){n.showAt(t,e,o)},showWithin:function(n,t,e,o,r){n.showWithin(t,e,o,r)},showWithinBounds:function(n,t,e,o,r){n.showWithinBounds(t,e,o,r)},showMenuAt:function(n,t,e,o){n.showMenuAt(t,e,o)},showMenuWithinBounds:function(n,t,e,o,r){n.showMenuWithinBounds(t,e,o,r)},hide:function(n,t){n.hide(t)},isOpen:function(n,t){return n.isOpen(t)},getContent:function(n,t){return n.getContent(t)},setContent:function(n,t,e){n.setContent(t,e)},reposition:function(n,t){n.reposition(t)}}}),Wg=function(n,t,e){return qa(fm(n,t),dm(n),e.innerNorth(),tc(),"layout-n")},Gg=function(n,t,e){return qa(fm(n,t),mm(n,t),e.innerSouth(),nc(),"layout-s")},Xg=Al({name:"Button",factory:function(n){function e(t){return Nn(n.dom,"attributes").bind(function(n){return Nn(n,t)})}var t=im(n.action),o=n.dom.tag;return{uid:n.uid,dom:n.dom,components:n.components,events:t,behaviours:el(n.buttonBehaviours,[bg.config({}),dg.config({mode:"execution",useSpace:!0,useEnter:!0})]),domModification:{attributes:function(){if("button"!==o)return{role:e("role").getOr("button")};var n=e("type").getOr("button"),t=e("role").map(function(n){return{role:n}}).getOr({});return N({type:n},t)}()},eventOrder:n.eventOrder}},configFields:[St("uid",undefined),ct("dom"),St("components",[]),tl("buttonBehaviours",[bg,dg]),ht("action"),ht("role"),St("eventOrder",{})]}),Yg={success:"checkmark",error:"warning",err:"error",warning:"warning",warn:"warning",info:"info"},qg=Al({name:"Notification",factory:function(t){function e(n){return{dom:{tag:"div",classes:["tox-bar"],attributes:{style:"width: "+n+"%"}}}}function o(n){return{dom:{tag:"div",classes:["tox-text"],innerHtml:n+"%"}}}var r=bm({dom:{tag:"p",innerHtml:t.translationProvider(t.text)},behaviours:ya([gg.config({})])}),i=bm({dom:{tag:"div",classes:t.progress?["tox-progress-bar","tox-progress-indicator"]:["tox-progress-bar"]},components:[{dom:{tag:"div",classes:["tox-bar-container"]},components:[e(0)]},o(0)],behaviours:ya([gg.config({})])}),n={updateProgress:function(n,t){n.getSystem().isConnected()&&i.getOpt(n).each(function(n){gg.set(n,[{dom:{tag:"div",classes:["tox-bar-container"]},components:[e(t)]},o(t)])})},updateText:function(n,t){if(n.getSystem().isConnected()){var e=r.get(n);gg.set(e,[Rr(t)])}}},u=H([t.icon.toArray(),t.level.toArray(),t.level.bind(function(n){return on.from(Yg[n])}).toArray()]);return{uid:t.uid,dom:{tag:"div",attributes:{role:"alert"},classes:t.level.map(function(n){return["tox-notification","tox-notification--in","tox-notification--"+n]}).getOr(["tox-notification","tox-notification--in"])},components:[{dom:{tag:"div",classes:["tox-notification__icon"],innerHtml:function(n,t){return Au(n,function(n){return on.from(t()[n])}).getOrThunk(function(){return ym(t)})}(u,t.iconProvider)}},{dom:{tag:"div",classes:["tox-notification__body"]},components:[r.asSpec()],behaviours:ya([gg.config({})])}].concat(t.progress?[i.asSpec()]:[]).concat(Xg.sketch({dom:{tag:"button",classes:["tox-notification__dismiss","tox-button","tox-button--naked","tox-button--icon"]},components:[{dom:{tag:"div",classes:["tox-icon"],innerHtml:xm("close",t.iconProvider),attributes:{"aria-label":t.translationProvider("Close")}}}],action:function(n){t.onAction(n)}})),apis:n}},configFields:[ht("level"),ct("progress"),ct("icon"),ct("onAction"),ct("text"),ct("iconProvider"),ct("translationProvider")],apis:{updateProgress:function(n,t,e){n.updateProgress(t,e)},updateText:function(n,t,e){n.updateText(t,e)}}}),Kg=tinymce.util.Tools.resolve("tinymce.util.Delay");function Jg(n,u,o){var a=u.backstage;return{open:function(n,t){function e(){t(),Ug.hide(i)}var r=au(qg.sketch({text:n.text,level:vn(["success","error","warning","warn","info"],n.type)?n.type:undefined,progress:!0===n.progressBar,icon:on.from(n.icon),onAction:e,iconProvider:a.shared.providers.icons,translationProvider:a.shared.providers.translate})),i=au(Ug.sketch({dom:{tag:"div",classes:["tox-notifications-container"]},lazySink:u.backstage.shared.getSink,fireDismissalEventInstead:{}}));return o.add(i),0<n.timeout&&Kg.setTimeout(function(){e()},n.timeout),{close:e,moveTo:function(n,t){Ug.showAt(i,{anchor:"makeshift",x:n,y:t},cu(r))},moveRel:function(n,t){if("banner"!==t){var e=function(n){switch(n){case"bc-bc":return Gg;case"tc-tc":return Wg;case"tc-bc":return rc;case"bc-tc":default:return ic}}(t),o={anchor:"node",root:Lr(),node:on.some(we.fromDom(n)),layouts:{onRtl:function(){return[e]},onLtr:function(){return[e]}}};Ug.showAt(i,o,cu(r))}else Ug.showAt(i,u.backstage.shared.anchors.banner(),cu(r))},text:function(n){qg.updateText(r,n)},settings:n,getEl:function(){return r.element().dom()},progressBar:{value:function(n){qg.updateProgress(r,n)}}}},close:function(n){n.close()},reposition:function(n){!function(n){bn(n,function(n){return n.moveTo(0,0)})}(n),function(e){0<e.length&&(yn(e).each(function(n){return n.moveRel(null,"banner")}),bn(e,function(n,t){0<t&&n.moveRel(e[t-1].getEl(),"bc-tc")}))}(n)},getArgs:function(n){return n.settings}}}function $g(e,o){var r=null;return{cancel:function(){null!==r&&(v.clearTimeout(r),r=null)},throttle:function(){for(var n=[],t=0;t<arguments.length;t++)n[t]=arguments[t];null!==r&&v.clearTimeout(r),r=v.setTimeout(function(){e.apply(null,n),r=null},o)}}}function Qg(n,t,e,o,r){var i=new _p(t,r||n.getRoot());return Ip(n,t,on.some(e),o,i.prev,on.none())}function Zg(t,e){return Rp(we.fromDom(t.selection.getNode())).getOrThunk(function(){var n=we.fromHtml('<span data-mce-autocompleter="1" data-mce-bogus="1"></span>',t.getDoc());return Pi(n,we.fromDom(e.extractContents())),e.insertNode(n.dom()),To(n).each(function(n){return n.dom().normalize()}),Rc(n).map(function(n){t.selection.setCursorLocation(n.dom(),function(n){return"img"===Je(n)?1:_c(n).fold(function(){return Do(n).length},function(n){return n.length})}(n))}),n})}function np(n,t){return n.toString().substring(t.length).replace(/\u00A0/g," ").replace(/\uFEFF/g,"")}function tp(n,u,a,c){return void 0===c&&(c=0),function(n){return n.collapsed&&3===n.startContainer.nodeType}(u)?Qg(n,u.startContainer,u.startOffset,function(e,o,r,n){var i=n.getOr(r.length);return function(n,t,e,o){var r;for(r=t-1;0<=r;r--){var i=n.charAt(r);if(Vp.test(i))return on.none();if(i===e)break}return-1===r||t-r<o?on.none():on.some(n.substring(r+1,t))}(r,i,a,1).fold(function(){return r.match(Vp)?e.abort():e.kontinue()},function(n){var t=u.cloneRange();return t.setStart(o,i-n.length-1),t.setEnd(u.endContainer,u.endOffset),r.length<c?e.abort():e.finish({text:np(t,a),range:t,triggerChar:a})})}).fold(on.none,on.none,on.some):on.none()}function ep(e,n,o,t){return void 0===t&&(t=0),Rp(we.fromDom(n.startContainer)).fold(function(){return tp(e,n,o,t)},function(n){var t=e.createRng();return t.selectNode(n.dom()),on.some({range:t,text:np(t,o),triggerChar:o})})}function op(n,t){return{element:n,offset:t}}function rp(t,e){var n=e(),o=t.selection.getRng();return function(t,e,n){return Au(n.triggerChars,function(n){return ep(t,e,n)})}(t.dom,o,n).bind(function(n){return Lp(t,e,n)})}function ip(n){var t=n.ui.registry.getAll().popups,e=P(t,function(n){return function(n){return tt("Autocompleter",Wp,n)}(n).fold(function(n){throw new Error(le(n))},function(n){return n})}),o=function(n){var t={};return bn(n,function(n){t[n]={}}),wn(t)}(On(e,function(n){return n.ch})),r=R(e);return{dataset:e,triggerChars:o,lookupByChar:function(t){return C(r,function(n){return n.ch===t})}}}function up(n,o,t){var r=Lc(n.element(),"."+t);if(0<r.length){var e=T(r,function(n){var t=n.dom().getBoundingClientRect().top,e=r[0].dom().getBoundingClientRect().top;return Math.abs(t-e)>o}).getOr(r.length);return on.some({numColumns:e,numRows:Math.ceil(r.length/e)})}return on.none()}function ap(n,t){return ya([Jd(n,t)])}function cp(n,t,e){n.getSystem().broadcastOn([nh],{})}function sp(n){var t=we.fromHtml(n),e=Do(t),o=function(n){var t=n.dom().attributes!==undefined?n.dom().attributes:[];return O(t,function(n,t){var e;return"class"===t.name?n:N(N({},n),((e={})[t.name]=t.value,e))},{})}(t),r=function(n){return Array.prototype.slice.call(n.dom().classList,0)}(t),i=0===e.length?{}:{innerHtml:No(t)};return N({tag:Je(t),classes:r,attributes:o},i)}function fp(n){return Nn(sh,n).getOr(uh)}function lp(n){return{dom:{tag:"div",classes:[lh],innerHtml:n}}}function dp(n){return{dom:{tag:"div",classes:[dh]},components:[Rr(ih.translate(n))]}}function mp(n,t){return{dom:{tag:"div",classes:[dh]},components:[{dom:{tag:n.tag,attributes:{style:n.styleAttr}},components:[Rr(ih.translate(t))]}]}}function gp(n){return{dom:{tag:"div",classes:["tox-collection__item-accessory"],innerHtml:hh(n)}}}function pp(n){return{dom:{tag:"div",classes:[lh,"tox-collection__item-checkmark"],innerHtml:xm("checkmark",n)}}}function hp(n,t,e,o,r){var i=e?n.checkMark.orThunk(function(){return t.or(on.some("")).map(lp)}):on.none(),u=n.ariaLabel.map(function(n){return{attributes:{title:ih.translate(n)}}}).getOr({});return{dom:An({tag:"div",classes:[uh,ah].concat(r?["tox-collection__item-icon-rtl"]:[])},u),optComponents:[i,n.htmlContent.fold(function(){return n.textContent.map(o)},function(n){return on.some(function(n){return{dom:{tag:"div",classes:[dh],innerHtml:n}}}(n))}),n.shortcutContent.map(gp),n.caret]}}function vp(n,t,e,o){void 0===o&&(o=on.none());var r=ih.isRtl()&&n.iconContent.exists(function(n){return vn(bh,n)}),i=n.iconContent.map(function(n){return ih.isRtl()&&vn(vh,n)?n+"-rtl":n}).map(function(n){return function(n,t,e){return on.from(t()[n]).or(e).getOrThunk(function(){return ym(t)})}(n,t.icons,o)}),u=on.from(n.meta).fold(function(){return dp},function(n){return En(n,"style")?d(mp,n.style):dp});return"color"===n.presets?function(n,t,e,o){var r,i,u;return{dom:(r=ch,i=e.getOr(""),u=n.map(function(n){return' title="'+o.translate(n)+'"'}).getOr(""),sp("custom"===t?'<button class="'+r+' tox-swatches__picker-btn"'+u+">"+i+"</button>":"remove"===t?'<div class="'+r+' tox-swatch--remove"'+u+">"+i+"</div>":'<div class="'+r+'" style="background-color: '+t+'" data-mce-color="'+t+'"'+u+"></div>")),optComponents:[]}}(n.ariaLabel,n.value,i,t):hp(n,i,e,u,r)}function bp(n,t,e){t.disabled&&xh(n,t)}function yp(n,t){return!0===t.useNative&&vn(yh,Je(n.element()))}function xp(n){zo(n.element(),"disabled","disabled")}function wp(n){Uo(n.element(),"disabled")}function Sp(n){zo(n.element(),"aria-disabled","true")}function Cp(n){zo(n.element(),"aria-disabled","false")}function kp(t,n,e){n.disableClass.each(function(n){mr(t.element(),n)}),(yp(t,n)?wp:Cp)(t),n.onEnabled(t)}function Op(n,t){return yp(n,t)?function(n){return jo(n.element(),"disabled")}(n):function(n){return"true"===Lo(n.element(),"aria-disabled")}(n)}function Ep(n,t){var e=n.getApi(t);return function(n){n(e)}}function Tp(e,o){return Ri(function(n){Ep(e,n)(function(n){var t=e.onSetup(n);null!==t&&t!==undefined&&o.set(t)})})}function Bp(t,e){return Vi(function(n){return Ep(t,n)(e.get())})}var Dp,Ap,_p=tinymce.util.Tools.resolve("tinymce.dom.TreeWalker"),Mp=Tn([{aborted:[]},{edge:["element"]},{success:["info"]}]),Fp=Tn([{abort:[]},{kontinue:[]},{finish:["info"]}]),Ip=function(t,e,n,o,r,i){function u(){return i.fold(Mp.aborted,Mp.edge)}function a(){var n=r();return n?Ip(t,n,on.none(),o,r,on.some(e)):u()}if(function(n,t){return n.isBlock(t)||vn(["BR","IMG","HR","INPUT"],t.nodeName)||"false"===n.getContentEditable(t)}(t,e))return u();if(function(n){return n.nodeType===v.Node.TEXT_NODE}(e)){var c=e.textContent;return o(Fp,e,c,n).fold(Mp.aborted,function(){return a()},Mp.success)}return a()},Rp=function(n){return Eu(n,"[data-mce-autocompleter]")},Vp=/[\u00a0 \t\r\n]/,Np=function(e,n){n.on("keypress compositionend",e.onKeypress.throttle),n.on("remove",e.onKeypress.cancel);function o(n,t){io(n,ti(),{raw:t})}n.on("keydown",function(t){function n(){return e.getView().bind(cd.getHighlighted)}8===t.which&&e.onKeypress.throttle(t),e.isActive()&&(27===t.which&&e.cancelIfNecessary(),e.isMenuOpen()?13===t.which?(n().each(uo),t.preventDefault()):40===t.which?(n().fold(function(){e.getView().each(cd.highlightFirst)},function(n){o(n,t)}),t.preventDefault(),t.stopImmediatePropagation()):37!==t.which&&38!==t.which&&39!==t.which||n().each(function(n){o(n,t),t.preventDefault(),t.stopImmediatePropagation()}):13!==t.which&&38!==t.which&&40!==t.which||e.cancelIfNecessary())}),n.on("NodeChange",function(n){e.isActive()&&!e.isProcessingAction()&&Rp(we.fromDom(n.element)).isNone()&&e.cancelIfNecessary()})},Hp=tinymce.util.Tools.resolve("tinymce.util.Promise"),Pp=function(n){if(function(n){return n.nodeType===v.Node.TEXT_NODE}(n))return op(n,n.data.length);var t=n.childNodes;return 0<t.length?Pp(t[t.length-1]):op(n,t.length)},zp=function(n,t){var e=n.childNodes;return 0<e.length&&t<e.length?zp(e[t],0):0<e.length&&function(n){return n.nodeType===v.Node.ELEMENT_NODE}(n)&&e.length===t?Pp(e[e.length-1]):op(n,t)},Lp=function(t,n,e,o){void 0===o&&(o={});var r=n(),i=t.selection.getRng().startContainer.nodeValue,u=C(r.lookupByChar(e.triggerChar),function(n){return e.text.length>=n.minChars&&n.matches.getOrThunk(function(){return function(e){function o(n,t,e,o){var r=o.getOr(e.length);return 0===r?n.kontinue():n.finish(/\s/.test(e.charAt(r-1)))}return function(n){var t=zp(n.startContainer,n.startOffset);return Qg(e,t.element,t.offset,o).fold(nn(!0),nn(!0),l)}}(t.dom)})(e.range,i,e.text)});if(0===u.length)return on.none();var a=Hp.all(S(u,function(t){return t.fetch(e.text,t.maxResults,o).then(function(n){return{matchText:e.text,items:n,columns:t.columns,onAction:t.onAction}})}));return on.some({lookupData:a,context:e})},jp=re([ft("type"),yt("text")]),Up=re([At("type",function(){return"autocompleteitem"}),At("active",function(){return!1}),At("disabled",function(){return!1}),St("meta",{}),ft("value"),yt("text"),yt("icon")]),Wp=re([ft("type"),ft("ch"),kt("minChars",1),St("columns",1),kt("maxResults",10),xt("matches"),dt("fetch"),dt("onAction")]),Gp=[Tt("disabled",!1),yt("text"),yt("shortcut"),ce("value","value",In(function(){return Yo("menuitem-value")}),de()),St("meta",{})],Xp=re([ft("type"),Bt("onSetup",function(){return Z}),Bt("onAction",Z),yt("icon")].concat(Gp)),Yp=re([ft("type"),dt("getSubmenuItems"),Bt("onSetup",function(){return Z}),yt("icon")].concat(Gp)),qp=re([ft("type"),Tt("active",!1),Bt("onSetup",function(){return Z}),dt("onAction")].concat(Gp)),Kp=re([ft("type"),Tt("active",!1),yt("icon")].concat(Gp)),Jp=re([ft("type"),lt("fancytype",["inserttable","colorswatch"]),Bt("onAction",Z)]),$p=function(n){return ap(Yo("unnamed-events"),n)},Qp=[ct("lazySink"),ct("tooltipDom"),St("exclusive",!0),St("tooltipComponents",[]),St("delay",300),Et("mode","normal",["normal","follow-highlight"]),St("anchor",function(n){return{anchor:"hotspot",hotspot:n,layouts:{onLtr:nn([ic,rc,aa,sa,ca,fa]),onRtl:nn([ic,rc,aa,sa,ca,fa])}}}),Ku("onHide"),Ku("onShow")],Zp=/* */Object.freeze({init:function(){function e(){o.get().each(function(n){v.clearTimeout(n)})}var o=ye(on.none()),t=ye(on.none()),n=nn("not-implemented");return tu({getTooltip:function(){return t.get()},isShowing:function(){return t.get().isSome()},setTooltip:function(n){t.set(on.some(n))},clearTooltip:function(){t.set(on.none())},clearTimer:e,resetTimer:function(n,t){e(),o.set(on.some(v.setTimeout(function(){n()},t)))},readState:n})}}),nh=Yo("tooltip.exclusive"),th=Yo("tooltip.show"),eh=Yo("tooltip.hide"),oh=/* */Object.freeze({hideAllExclusive:cp,setComponents:function(n,t,e,o){e.getTooltip().each(function(n){n.getSystem().isConnected()&&gg.set(n,o)})}}),rh=xa({fields:Qp,name:"tooltipping",active:/* */Object.freeze({events:function(o,r){function e(t){r.getTooltip().each(function(n){ys(n),o.onHide(t,n),r.clearTooltip()}),r.clearTimer()}return so(H([[mo(th,function(n){r.resetTimer(function(){!function(t){if(!r.isShowing()){cp(t);var n=o.lazySink(t).getOrDie(),e=t.getSystem().build({dom:o.tooltipDom,components:o.tooltipComponents,events:so("normal"===o.mode?[mo(Qr(),function(n){ro(t,th)}),mo(Jr(),function(n){ro(t,eh)})]:[]),behaviours:ya([gg.config({})])});r.setTooltip(e),vs(n,e),o.onShow(t,e),_f.position(n,o.anchor(t),e)}}(n)},o.delay)}),mo(eh,function(n){r.resetTimer(function(){e(n)},o.delay)}),mo(di(),function(n,t){vn(t.channels(),nh)&&e(n)}),Vi(function(n){e(n)})],"normal"===o.mode?[mo(Zr(),function(n){ro(n,th)}),mo(fi(),function(n){ro(n,eh)}),mo(Qr(),function(n){ro(n,th)}),mo(Jr(),function(n){ro(n,eh)})]:[mo(Mi(),function(n,t){ro(n,th)}),mo(Fi(),function(n){ro(n,eh)})]]))}}),state:Zp,apis:oh}),ih=tinymce.util.Tools.resolve("tinymce.util.I18n"),uh="tox-menu-nav__js",ah="tox-collection__item",ch="tox-swatch",sh={normal:uh,color:ch},fh="tox-collection__item--enabled",lh="tox-collection__item-icon",dh="tox-collection__item-label",mh="tox-collection__item-caret",gh="tox-collection__item--active",ph=tinymce.util.Tools.resolve("tinymce.Env"),hh=function(n){var e=ph.mac?{alt:"&#x2325;",ctrl:"&#x2303;",shift:"&#x21E7;",meta:"&#x2318;",access:"&#x2303;&#x2325;"}:{meta:"Ctrl",access:"Shift+Alt"},t=n.split("+"),o=S(t,function(n){var t=n.toLowerCase().trim();return En(e,t)?e[t]:n});return ph.mac?o.join(""):o.join("+")},vh=["list-num-default","list-num-lower-alpha","list-num-lower-greek","list-num-lower-roman","list-num-upper-alpha","list-num-upper-roman"],bh=["list-bull-circle","list-bull-default","list-bull-square"],yh=["input","button","textarea","select"],xh=function(t,n,e){n.disableClass.each(function(n){lr(t.element(),n)}),(yp(t,n)?xp:Sp)(t),n.onDisabled(t)},wh=/* */Object.freeze({enable:kp,disable:xh,isDisabled:Op,onLoad:bp,set:function(n,t,e,o){(o?xh:kp)(n,t,e)}}),Sh=/* */Object.freeze({exhibit:function(n,t,e){return nr({classes:t.disabled?t.disableClass.map(M).getOr([]):[]})},events:function(e,n){return so([fo(mi(),function(n,t){return Op(n,e)}),pa(e,n,bp)])}}),Ch=[St("disabled",!1),St("useNative",!0),ht("disableClass"),Ku("onDisabled"),Ku("onEnabled")],kh=xa({fields:Ch,name:"disabling",active:Sh,apis:wh}),Oh=function(n){return kh.config({disabled:n,disableClass:"tox-collection__item--state-disabled"})},Eh=function(n){return kh.config({disabled:n})},Th=function(n){return kh.config({disabled:n,disableClass:"tox-tbtn--disabled"})},Bh=function(n){return kh.config({disabled:n,disableClass:"tox-tbtn--disabled",useNative:!1})};(Ap=Dp=Dp||{})[Ap.CLOSE_ON_EXECUTE=0]="CLOSE_ON_EXECUTE",Ap[Ap.BUBBLE_TO_SANDBOX=1]="BUBBLE_TO_SANDBOX";function Dh(n){return B(n,function(n){return n.toArray()})}function Ah(n,t,e){var o=ye(Z);return{type:"item",dom:t.dom,components:Dh(t.optComponents),data:n.data,eventOrder:Rh,hasSubmenu:n.triggersSubmenu,itemBehaviours:ya([Jd("item-events",[function(e,o){return Hi(function(n,t){Ep(e,n)(e.onAction),e.triggersSubmenu||o!==Ih.CLOSE_ON_EXECUTE||(ro(n,vi()),t.stop())})}(n,e),Tp(n,o),Bp(n,o)]),Oh(n.disabled),gg.config({})].concat(n.itemBehaviours))}}function _h(n){return{value:n.value,meta:An({text:n.text.getOr("")},n.meta)}}function Mh(n,t){var e=function(n){return Vh.DOM.encode(n)}(ih.translate(n));if(0<t.length){var o=new RegExp(function(n){return n.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}(t),"gi");return e.replace(o,function(n){return'<span class="tox-autocompleter-highlight">'+n+"</span>"})}return e}function Fh(t,e,n){function o(n){return io(n,Ph,{row:t,col:e})}function r(n,t){t.stop(),o(n)}var i;return au({dom:{tag:"div",attributes:(i={role:"button"},i["aria-labelledby"]=n,i)},behaviours:ya([Jd("insert-table-picker-cell",[mo(Qr(),bg.focus),mo(mi(),o),mo(ii(),r),mo(pi(),r)]),kg.config({toggleClass:"tox-insert-table-picker__selected",toggleOnExecute:!1}),bg.config({onFocus:function(n){return io(n,Hh,{row:t,col:e})}})])})}var Ih=Dp,Rh={"alloy.execute":["disabling","alloy.base.behaviour","toggling","item-events"]},Vh=tinymce.util.Tools.resolve("tinymce.dom.DOMUtils"),Nh=nn(Us("item-widget",_g())),Hh=Yo("cell-over"),Ph=Yo("cell-execute");function zh(n){return{value:nn(n)}}function Lh(n){return $h.test(n)||Qh.test(n)}function jh(n){var t=function(n){var t=n.value().replace($h,function(n,t,e,o){return t+t+e+e+o+o});return{value:nn(t)}}(n),e=Qh.exec(t.value());return null===e?["FFFFFF","FF","FF","FF"]:e}function Uh(n){var t=n.toString(16);return 1===t.length?"0"+t:t}function Wh(n){var t=Uh(n.red())+Uh(n.green())+Uh(n.blue());return zh(t)}function Gh(n,t,e,o){return{red:nn(n),green:nn(t),blue:nn(e),alpha:nn(o)}}function Xh(n){var t=parseInt(n,10);return t.toString()===n&&0<=t&&t<=255}function Yh(n){var t,e,o,r=(n.hue()||0)%360,i=n.saturation()/100,u=n.value()/100;if(i=nv(0,Zh(i,1)),u=nv(0,Zh(u,1)),0===i)return t=e=o=tv(255*u),Gh(t,e,o,1);var a=r/60,c=u*i,s=c*(1-Math.abs(a%2-1)),f=u-c;switch(Math.floor(a)){case 0:t=c,e=s,o=0;break;case 1:t=s,e=c,o=0;break;case 2:t=0,e=c,o=s;break;case 3:t=0,e=s,o=c;break;case 4:t=s,e=0,o=c;break;case 5:t=c,e=0,o=s;break;default:t=e=o=0}return t=tv(255*(t+f)),e=tv(255*(e+f)),o=tv(255*(o+f)),Gh(t,e,o,1)}function qh(n){var t=jh(n),e=parseInt(t[1],16),o=parseInt(t[2],16),r=parseInt(t[3],16);return Gh(e,o,r,1)}function Kh(n,t,e,o){var r=parseInt(n,10),i=parseInt(t,10),u=parseInt(e,10),a=parseFloat(o);return Gh(r,i,u,a)}function Jh(n){return"rgba("+n.red()+","+n.green()+","+n.blue()+","+n.alpha()+")"}var $h=/^#?([a-f\d])([a-f\d])([a-f\d])$/i,Qh=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i,Zh=Math.min,nv=Math.max,tv=Math.round,ev=/^rgb\((\d+),\s*(\d+),\s*(\d+)\)/,ov=/^rgba\((\d+),\s*(\d+),\s*(\d+),\s*(\d?(?:\.\d+)?)\)/,rv=nn(Gh(255,0,0,1)),iv=tinymce.util.Tools.resolve("tinymce.util.LocalStorage"),uv="tinymce-custom-colors";function av(n){var t=[],u=v.document.createElement("canvas");u.height=1,u.width=1;for(var a=u.getContext("2d"),c=function(n,t){var e=t/255;return("0"+Math.round(n*e+255*(1-e)).toString(16)).slice(-2).toUpperCase()},e=function(n){if(/^[0-9A-Fa-f]{6}$/.test(n))return"#"+n.toUpperCase();a.clearRect(0,0,u.width,u.height),a.fillStyle="#FFFFFF",a.fillStyle=n,a.fillRect(0,0,1,1);var t=a.getImageData(0,0,1,1).data,e=t[0],o=t[1],r=t[2],i=t[3];return"#"+c(e,i)+c(o,i)+c(r,i)},o=0;o<n.length;o+=2)t.push({text:n[o+1],value:e(n[o]),type:"choiceitem"});return t}function cv(n){return n.getParam("color_map")}function sv(n,e){var o;return n.dom.getParents(n.selection.getStart(),function(n){var t;(t=n.style["forecolor"===e?"color":"background-color"])&&(o=o||t)}),o}function fv(n){return Math.max(5,Math.ceil(Math.sqrt(n)))}function lv(n){var t=Rv(n),e=fv(t.length);return Fv(n,e)}function dv(t,e,n,o){"custom"===n?Uv(t)(function(n){n.each(function(n){Nv(n),t.execCommand("mceApplyTextcolor",e,n),o(n)})},"#000000"):"remove"===n?(o(""),t.execCommand("mceRemoveTextcolor",e)):(o(n),t.execCommand("mceApplyTextcolor",e,n))}function mv(n,t){return n.concat(Vv().concat(function(n){var t="choiceitem",e={type:t,text:"Remove color",icon:"color-swatch-remove-color",value:"remove"};return n?[e,{type:t,text:"Custom color",icon:"color-picker",value:"custom"}]:[e]}(t)))}function gv(t,e){return function(n){n(mv(t,e))}}function pv(n,t,e){var o,r;o="forecolor"===t?"tox-icon-text-color__color":"tox-icon-highlight-bg-color__color",r=e,n.setIconFill(o,r),n.setIconStroke(o,r)}function hv(o,e,r,n,i){o.ui.registry.addSplitButton(e,{tooltip:n,presets:"color",icon:"forecolor"===e?"text-color":"highlight-bg-color",select:function(e){return on.from(sv(o,r)).bind(function(n){return function(n){if("transparent"===n)return on.some(Gh(0,0,0,0));var t=ev.exec(n);if(null!==t)return on.some(Kh(t[1],t[2],t[3],"1"));var e=ov.exec(n);return null!==e?on.some(Kh(e[1],e[2],e[3],e[4])):on.none()}(n).map(function(n){var t=Wh(n).value();return Vt(e.toLowerCase(),t)})}).getOr(!1)},columns:lv(o),fetch:gv(Rv(o),Iv(o)),onAction:function(n){null!==i.get()&&dv(o,r,i.get(),function(){})},onItemAction:function(n,t){dv(o,r,t,function(n){i.set(n),jv(o,{name:e,color:n})})},onSetup:function(t){null!==i.get()&&pv(t,e,i.get());function n(n){n.name===e&&pv(t,n.name,n.color)}return o.on("TextColorChange",n),function(){o.off("TextColorChange",n)}}})}function vv(t,n,e,o){t.ui.registry.addNestedMenuItem(n,{text:o,icon:"forecolor"===n?"text-color":"highlight-bg-color",getSubmenuItems:function(){return[{type:"fancymenuitem",fancytype:"colorswatch",onAction:function(n){dv(t,e,n.value,Z)}}]}})}function bv(e,o){return function(n){var t=w(n,o);return S(t,function(n){return{dom:e,components:n}})}}function yv(n,e){var o=[],r=[];return bn(n,function(n,t){e(n,t)?(0<r.length&&o.push(r),r=[],En(n.dom,"innerHtml")&&r.push(n)):r.push(n)}),0<r.length&&o.push(r),S(o,function(n){return{dom:{tag:"div",classes:["tox-collection__group"]},components:n}})}function xv(t,e,n){return{dom:{tag:"div",classes:["tox-menu","tox-collection"].concat(1===t?["tox-collection--list"]:["tox-collection--grid"])},components:[Ng.parts().items({preprocess:function(n){return"auto"!==t&&1<t?bv({tag:"div",classes:["tox-collection__group"]},t)(n):yv(n,function(n,t){return"separator"===e[t].type})}})]}}function wv(n){return{backgroundMenu:"tox-background-menu",selectedMenu:"tox-selected-menu",selectedItem:"tox-collection__item--active",hasIcons:"tox-menu--has-icons",menu:function(n){return"color"===n?"tox-swatches":"tox-menu"}(n),tieredMenu:"tox-tiered-menu"}}function Sv(n){var t=wv(n);return{backgroundMenu:t.backgroundMenu,selectedMenu:t.selectedMenu,menu:t.menu,selectedItem:t.selectedItem,item:fp(n)}}function Cv(n,t,e){var o=wv(e);return{dom:{tag:"div",classes:H([[o.tieredMenu]])},markers:Sv(e)}}function kv(n){return n.icon!==undefined||"togglemenuitem"===n.type||"choicemenuitem"===n.type}function Ov(n){return v.console.error(le(n)),v.console.log(n),on.none()}function Ev(n,t,e,o,r){var i=function(e){return{dom:{tag:"div",classes:["tox-collection","tox-collection--horizontal"]},components:[Ng.parts().items({preprocess:function(n){return yv(n,function(n,t){return"separator"===e[t].type})}})]}}(e);return{value:n,dom:i.dom,components:i.components,items:e}}function Tv(n,t,e,o,r){var i;return"color"===r?{value:n,dom:(i=function(n){return{dom:{tag:"div",classes:["tox-menu","tox-swatches-menu"]},components:[{dom:{tag:"div",classes:["tox-swatches"]},components:[Ng.parts().items({preprocess:"auto"!==n?bv({tag:"div",classes:["tox-swatches__row"]},n):l})]}]}}(o)).dom,components:i.components,items:e}:"normal"===r&&"auto"===o?{value:n,dom:(i=xv(o,e)).dom,components:i.components,items:e}:"normal"===r&&1===o?{value:n,dom:(i=xv(1,e)).dom,components:i.components,items:e}:"normal"===r?{value:n,dom:(i=xv(o,e)).dom,components:i.components,items:e}:"listpreview"!==r||"auto"===o?{value:n,dom:function(n,t,e){var o=wv(e);return{tag:"div",classes:H([[o.menu,"tox-menu-"+t+"-column"],n?[o.hasIcons]:[]])}}(t,o,r),components:Gv,items:e}:{value:n,dom:(i=function(n){return{dom:{tag:"div",classes:["tox-menu","tox-collection","tox-collection--toolbar","tox-collection--toolbar-lg"]},components:[Ng.parts().items({preprocess:bv({tag:"div",classes:["tox-collection__group"]},n)})]}}(o)).dom,components:i.components,items:e}}function Bv(n,t,e,o,r,i,u,a){var c=function(n){return x(n,kv)}(t),s=Xv(t,e,o,"color"!==r?"normal":"color",i,u,a);return Tv(n,c,s,o,r)}function Dv(n,t){var e=Sv(t);return 1===n?{mode:"menu",moveOnTab:!0}:"auto"===n?{mode:"grid",selector:"."+e.item,initSize:{numColumns:1,numRows:1}}:{mode:"matrix",rowSelector:"."+("color"===t?"tox-swatches__row":"tox-collection__group")}}var Av="choiceitem",_v=[{type:Av,text:"Light Green",value:"#BFEDD2"},{type:Av,text:"Light Yellow",value:"#FBEEB8"},{type:Av,text:"Light Red",value:"#F8CAC6"},{type:Av,text:"Light Purple",value:"#ECCAFA"},{type:Av,text:"Light Blue",value:"#C2E0F4"},{type:Av,text:"Green",value:"#2DC26B"},{type:Av,text:"Yellow",value:"#F1C40F"},{type:Av,text:"Red",value:"#E03E2D"},{type:Av,text:"Purple",value:"#B96AD9"},{type:Av,text:"Blue",value:"#3598DB"},{type:Av,text:"Dark Turquoise",value:"#169179"},{type:Av,text:"Orange",value:"#E67E23"},{type:Av,text:"Dark Red",value:"#BA372A"},{type:Av,text:"Dark Purple",value:"#843FA1"},{type:Av,text:"Dark Blue",value:"#236FA1"},{type:Av,text:"Light Gray",value:"#ECF0F1"},{type:Av,text:"Medium Gray",value:"#CED4D9"},{type:Av,text:"Gray",value:"#95A5A6"},{type:Av,text:"Dark Gray",value:"#7E8C8D"},{type:Av,text:"Navy Blue",value:"#34495E"},{type:Av,text:"Black",value:"#000000"},{type:Av,text:"White",value:"#ffffff"}],Mv=function uI(t){void 0===t&&(t=10);var n,e=iv.getItem(uv),o=cn(e)?JSON.parse(e):[],r=t-(n=o).length<0?n.slice(0,t):n,i=function(n){r.splice(n,1)};return{add:function(n){(function(n,t){var e=y(n,t);return-1===e?on.none():on.some(e)})(r,n).each(i),r.unshift(n),r.length>t&&r.pop(),iv.setItem(uv,JSON.stringify(r))},state:function(){return r.slice(0)}}}(10),Fv=function(n,t){return n.getParam("color_cols",t,"number")},Iv=function(n){return!1!==n.getParam("custom_colors")},Rv=function(n){var t=cv(n);return t!==undefined?av(t):_v},Vv=function(){return S(Mv.state(),function(n){return{type:Av,text:n,value:n}})},Nv=function(n){Mv.add(n)},Hv=function(n){return n.fire("SkinLoaded")},Pv=function(n){return n.fire("ResizeEditor")},zv=function(n,t){return n.fire("ScrollContent",t)},Lv=function(n,t){return n.fire("ResizeContent",t)},jv=function(n,t){return n.fire("TextColorChange",t)},Uv=function(i){return function(n,t){var e,o={colorpicker:t},r=(e=n,function(n){var t=n.getData();e(on.from(t.colorpicker)),n.close()});i.windowManager.open({title:"Color Picker",size:"normal",body:{type:"panel",items:[{type:"colorpicker",name:"colorpicker",label:"Color"}]},buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],initialData:o,onAction:function(n,t){"hex-valid"===t.name&&(t.value?n.enable("ok"):n.disable("ok"))},onSubmit:r,onClose:function(){},onCancel:function(){n(on.none())}})}},Wv={register:function(n){!function(e){e.addCommand("mceApplyTextcolor",function(n,t){!function(n,t,e){n.undoManager.transact(function(){n.focus(),n.formatter.apply(t,{value:e}),n.nodeChanged()})}(e,n,t)}),e.addCommand("mceRemoveTextcolor",function(n){!function(n,t){n.undoManager.transact(function(){n.focus(),n.formatter.remove(t,{value:null},null,!0),n.nodeChanged()})}(e,n)})}(n);var t=ye(null),e=ye(null);hv(n,"forecolor","forecolor","Text color",t),hv(n,"backcolor","hilitecolor","Background color",e),vv(n,"forecolor","forecolor","Text color"),vv(n,"backcolor","hilitecolor","Background color")},getColors:mv,getFetch:gv,colorPickerDialog:Uv,getCurrentColor:sv,getColorCols:lv,calcCols:fv},Gv=[Ng.parts().items({})],Xv=function(n,e,o,r,i,u,a){return Du(S(n,function(t){return"choiceitem"===t.type?function(n){return tt("choicemenuitem",Kp,n)}(t).fold(Ov,function(n){return on.some(function(t,n,e,o,r,i,u){var a=vp({presets:e,textContent:n?t.text:on.none(),htmlContent:on.none(),ariaLabel:t.text,iconContent:t.icon,shortcutContent:n?t.shortcut:on.none(),checkMark:n?on.some(pp(u.icons)):on.none(),caret:on.none(),value:t.value},u,!0);return Dn(Ah({data:_h(t),disabled:t.disabled,getApi:function(t){return{setActive:function(n){kg.set(t,n)},isActive:function(){return kg.isOn(t)},isDisabled:function(){return kh.isDisabled(t)},setDisabled:function(n){return kh.set(t,n)}}},onAction:function(n){return o(t.value)},onSetup:function(n){return n.setActive(r),function(){}},triggersSubmenu:!1,itemBehaviours:[]},a,i),{toggling:{toggleClass:fh,toggleOnExecute:!1,selected:t.active}})}(n,1===o,r,e,u(t.value),i,a))}):on.none()}))};var Yv,qv,Kv={inserttable:function aI(o){var n=Yo("size-label"),i=function(n,t,e){for(var o=[],r=0;r<t;r++){for(var i=[],u=0;u<e;u++)i.push(Fh(r,u,n));o.push(i)}return o}(n,10,10),u=bm({dom:{tag:"span",classes:["tox-insert-table-picker__label"],attributes:{id:n}},components:[Rr("0x0")],behaviours:ya([gg.config({})])});return{type:"widget",data:{value:Yo("widget-id")},dom:{tag:"div",classes:["tox-fancymenuitem"]},autofocus:!0,components:[Nh().widget({dom:{tag:"div",classes:["tox-insert-table-picker"]},components:function(n){return B(n,function(n){return S(n,cu)})}(i).concat(u.asSpec()),behaviours:ya([Jd("insert-table-picker",[vo(Hh,function(n,t,e){var o=e.event().row(),r=e.event().col();!function(n,t,e,o,r){for(var i=0;i<o;i++)for(var u=0;u<r;u++)kg.set(n[i][u],i<=t&&u<=e)}(i,o,r,10,10),gg.set(u.get(n),[function(n,t){return Rr(t+1+"x"+(n+1))}(o,r)])}),vo(Ph,function(n,t,e){o.onAction({numRows:e.event().row()+1,numColumns:e.event().col()+1}),ro(n,vi())})]),dg.config({initSize:{numRows:10,numColumns:10},mode:"flatgrid",selector:'[role="button"]'})])})]}},colorswatch:function cI(t,n){var e=Wv.getColors(n.colorinput.getColors(),n.colorinput.hasCustomColors()),o=n.colorinput.getColorCols(),r=Bv(Yo("menu-value"),e,function(n){t.onAction({value:n})},o,"color",Ih.CLOSE_ON_EXECUTE,function(){return!1},n.shared.providers),i=Dn(N(N({},r),{markers:Sv("color"),movement:Dv(o,"color")}));return{type:"widget",data:{value:Yo("widget-id")},dom:{tag:"div",classes:["tox-fancymenuitem"]},autofocus:!0,components:[Nh().widget(Ng.sketch(i))]}}},Jv=function(t,e,n,o,r,i,u,a){void 0===a&&(a=!0);var c=vp({presets:o,textContent:on.none(),htmlContent:n?t.text.map(function(n){return Mh(n,e)}):on.none(),ariaLabel:t.text,iconContent:t.icon,shortcutContent:on.none(),checkMark:on.none(),caret:on.none(),value:t.value},u.providers,a,t.icon);return Ah({data:_h(t),disabled:t.disabled,getApi:function(){return{}},onAction:function(n){return r(t.value,t.meta)},onSetup:function(){return function(){}},triggersSubmenu:!1,itemBehaviours:function(n,t){return V(n,"tooltipWorker").map(function(e){return[rh.config({lazySink:t.getSink,tooltipDom:{tag:"div",classes:["tox-tooltip-worker-container"]},tooltipComponents:[],anchor:function(n){return{anchor:"submenu",item:n,overrides:{maxHeightFunction:kf}}},mode:"follow-highlight",onShow:function(t,n){e(function(n){rh.setComponents(t,[iu({element:we.fromDom(n)})])})}})]}).getOr([])}(t.meta,u)},c,i)},$v=function(n){var t=n.text.fold(function(){return{}},function(n){return{innerHtml:n}});return{type:"separator",dom:N({tag:"div",classes:[ah,"tox-collection__group-heading"]},t),components:[]}},Qv=function(n,t,e,o){void 0===o&&(o=!0);var r=vp({presets:"normal",iconContent:n.icon,textContent:n.text,htmlContent:on.none(),ariaLabel:n.text,caret:on.none(),checkMark:on.none(),shortcutContent:n.shortcut},e,o);return Ah({data:_h(n),getApi:function(t){return{isDisabled:function(){return kh.isDisabled(t)},setDisabled:function(n){return kh.set(t,n)}}},disabled:n.disabled,onAction:n.onAction,onSetup:n.onSetup,triggersSubmenu:!1,itemBehaviours:[]},r,t)},Zv=function(n,t,e,o,r){void 0===o&&(o=!0),void 0===r&&(r=!1);var i=r?function(n){return{dom:{tag:"div",classes:[mh],innerHtml:xm("chevron-down",n)}}}(e.icons):function(n){return{dom:{tag:"div",classes:[mh],innerHtml:xm("chevron-right",n)}}}(e.icons),u=vp({presets:"normal",iconContent:n.icon,textContent:n.text,htmlContent:on.none(),ariaLabel:n.text,caret:on.some(i),checkMark:on.none(),shortcutContent:n.shortcut},e,o);return Ah({data:_h(n),getApi:function(t){return{isDisabled:function(){return kh.isDisabled(t)},setDisabled:function(n){return kh.set(t,n)}}},disabled:n.disabled,onAction:Z,onSetup:n.onSetup,triggersSubmenu:!0,itemBehaviours:[]},u,t)},nb=function(n,t,e){var o=vp({iconContent:on.none(),textContent:n.text,htmlContent:on.none(),ariaLabel:n.text,checkMark:on.some(pp(e.icons)),caret:on.none(),shortcutContent:n.shortcut,presets:"normal",meta:n.meta},e,!0);return Dn(Ah({data:_h(n),disabled:n.disabled,getApi:function(t){return{setActive:function(n){kg.set(t,n)},isActive:function(){return kg.isOn(t)},isDisabled:function(){return kh.isDisabled(t)},setDisabled:function(n){return kh.set(t,n)}}},onAction:n.onAction,onSetup:n.onSetup,triggersSubmenu:!1,itemBehaviours:[]},o,t),{toggling:{toggleClass:fh,toggleOnExecute:!1,selected:n.active}})},tb=function(t,e){return function(n,t){return Object.prototype.hasOwnProperty.call(n,t)?on.some(n[t]):on.none()}(Kv,t.fancytype).map(function(n){return n(t,e)})};(qv=Yv=Yv||{})[qv.ContentFocus=0]="ContentFocus",qv[qv.UiFocus=1]="UiFocus";function eb(n){return n.icon!==undefined||"togglemenuitem"===n.type||"choicemenuitem"===n.type}function ob(n){return x(n,eb)}function rb(n,t,e,o,r){function i(n){return r?N(N({},n),{shortcut:on.none(),icon:n.text.isSome()?on.none():n.icon}):n}var u=e.shared.providers;switch(n.type){case"menuitem":return function(n){return tt("menuitem",Xp,n)}(n).fold(Ov,function(n){return on.some(Qv(i(n),t,u,o))});case"nestedmenuitem":return function(n){return tt("nestedmenuitem",Yp,n)}(n).fold(Ov,function(n){return on.some(Zv(i(n),t,u,o,r))});case"togglemenuitem":return function(n){return tt("togglemenuitem",qp,n)}(n).fold(Ov,function(n){return on.some(nb(i(n),t,u))});case"separator":return function(n){return tt("separatormenuitem",jp,n)}(n).fold(Ov,function(n){return on.some($v(n))});case"fancymenuitem":return function(n){return tt("fancymenuitem",Jp,n)}(n).fold(Ov,function(n){return tb(i(n),e)});default:return v.console.error("Unknown item in general menu",n),on.none()}}function ib(n,t,e,o,r,i){var u=1===o,a=!u||ob(n);return Du(S(n,function(n){return"separator"===n.type?function(n){return tt("Autocompleter.Separator",jp,n)}(n).fold(Ov,function(n){return on.some($v(n))}):function(n){return tt("Autocompleter.Item",Up,n)}(n).fold(Ov,function(n){return on.some(Jv(n,t,u,"normal",e,r,i,a))})}))}function ub(n,t,e,o,r){var i=ob(t),u=Du(S(t,function(n){function t(n){return rb(n,e,o,function(n){return r?!n.hasOwnProperty("text"):i}(n),r)}return"nestedmenuitem"===n.type&&n.getSubmenuItems().length<=0?t(An(n,{disabled:!0})):t(n)}));return(r?Ev:Tv)(n,i,u,1,"normal")}function ab(n){return jg.singleData(n.value,n)}function cb(n){function t(){n.stopPropagation()}function e(){n.preventDefault()}var o=we.fromDom(n.target),r=i(e,t);return function(n,t,e,o,r,i,u){return{target:nn(n),x:nn(t),y:nn(e),stop:o,prevent:r,kill:i,raw:nn(u)}}(o,n.clientX,n.clientY,t,e,r,n)}function sb(n,t,e,o,r){var i=function(t,e){return function(n){t(n)&&e(cb(n))}}(e,o);return n.dom().addEventListener(t,i,r),{unbind:d(gb,n,t,i,r)}}function fb(n,t,e){return function(n,t,e,o){return sb(n,t,e,o,!1)}(n,t,pb,e)}function lb(n,t,e){return function(n,t,e,o){return sb(n,t,e,o,!0)}(n,t,pb,e)}function db(n,t,e){return Eu(n,t,e).isSome()}var mb=function(u,a){function e(){return s.get().isSome()}function c(){e()&&Ug.hide(l)}function i(n,t,e,o){n.matchLength=t.text.length;var r=Au(e,function(n){return on.from(n.columns)}).getOr(1);Ug.showAt(l,{anchor:"node",root:we.fromDom(u.getBody()),node:on.from(n.element)},Ng.sketch(function(n,t,e,o){var r=e===Yv.ContentFocus?Yl():Xl(),i=Dv(t,o),u=Sv(o);return{dom:n.dom,components:n.components,items:n.items,value:n.value,markers:{selectedItem:u.selectedItem,item:u.item},movement:i,fakeFocus:e===Yv.ContentFocus,focusManager:r,menuBehaviours:$p("auto"!==t?[]:[Ri(function(o,n){up(o,4,u.item).each(function(n){var t=n.numColumns,e=n.numRows;dg.setGridSize(o,e,t)})})])}}(Tv("autocompleter-value",!0,o,r,"normal"),r,Yv.ContentFocus,"normal"))),Ug.getContent(l).each(cd.highlightFirst)}var s=ye(on.none()),f=ye(!1),l=au(Ug.sketch({dom:{tag:"div",classes:["tox-autocompleter"]},components:[],fireDismissalEventInstead:{},inlineBehaviours:ya([Jd("dismissAutocompleter",[mo(Ei(),function(){return d()})])]),lazySink:a.getSink})),d=function(){if(e()){var n=s.get().map(function(n){return n.element});Rp(n.getOr(we.fromDom(u.selection.getNode()))).each(Vo),c(),s.set(on.none()),f.set(!1)}},o=L(function(){return ip(u)}),m=function(n){(function(t){return s.get().map(function(n){return ep(u.dom,u.selection.getRng(),n.triggerChar).bind(function(n){return Lp(u,o,n,t)})}).getOrThunk(function(){return rp(u,o)})})(n).fold(d,function(r){!function(n){if(!e()){var t=Zg(u,n.range);s.set(on.some({triggerChar:n.triggerChar,element:t,matchLength:n.text.length})),f.set(!1)}}(r.context),r.lookupData.then(function(o){s.get().map(function(n){var t=r.context;if(n.triggerChar===t.triggerChar){var e=function(t,n){var e=Au(n,function(n){return on.from(n.columns)}).getOr(1);return B(n,function(i){var n=i.items;return ib(n,i.matchText,function(o,r){var n=u.selection.getRng();ep(u.dom,n,t).fold(function(){return v.console.error("Lost context. Cursor probably moved")},function(n){var t=n.range,e={hide:function(){d()},reload:function(n){c(),m(n)}};f.set(!0),i.onAction(e,t,o,r),f.set(!1)})},e,Ih.BUBBLE_TO_SANDBOX,a)})}(t.triggerChar,o);0<e.length?i(n,t,o,e):10<=t.text.length-n.matchLength?d():c()}})})})},n={onKeypress:$g(function(n){27!==n.which&&m()},50),cancelIfNecessary:d,isMenuOpen:function(){return Ug.isOpen(l)},isActive:e,isProcessingAction:f.get,getView:function(){return Ug.getContent(l)}};Np(n,u)},gb=function(n,t,e,o){n.dom().removeEventListener(t,e,o)},pb=nn(!0),hb=cb;function vb(e,o){var r=null;return{cancel:function(){null!==r&&(v.clearTimeout(r),r=null)},schedule:function(){for(var n=[],t=0;t<arguments.length;t++)n[t]=arguments[t];r=v.setTimeout(function(){e.apply(null,n),r=null},o)}}}function bb(n){var t=n.raw();return t.touches===undefined||1!==t.touches.length?on.none():on.some(t.touches[0])}function yb(e){var o=ye(on.none()),r=ye(!1),i=vb(function(n){e.triggerEvent(hi(),n),r.set(!0)},400),u=K([{key:Wr(),value:function(e){return bb(e).each(function(n){i.cancel();var t={x:nn(n.clientX),y:nn(n.clientY),target:e.target};i.schedule(e),r.set(!1),o.set(on.some(t))}),on.none()}},{key:Gr(),value:function(n){return i.cancel(),bb(n).each(function(t){o.get().each(function(n){!function(n,t){var e=Math.abs(n.clientX-t.x()),o=Math.abs(n.clientY-t.y());return 5<e||5<o}(t,n)||o.set(on.none())})}),on.none()}},{key:Xr(),value:function(t){i.cancel();return o.get().filter(function(n){return jt(n.target(),t.target())}).map(function(n){return r.get()?(t.prevent(),!1):e.triggerEvent(pi(),t)})}}]);return{fireIfReady:function(t,n){return Nn(u,n).bind(function(n){return n(t)})}}}function xb(t,n){var e=ot("Getting GUI events settings",Ob,n),o=yb(e),r=S(["touchstart","touchmove","touchend","touchcancel","gesturestart","mousedown","mouseup","mouseover","mousemove","mouseout","click"].concat(["selectstart","input","contextmenu","change","transitionend","drag","dragstart","dragend","dragenter","dragleave","dragover","drop","keyup"]),function(n){return fb(t,n,function(t){o.fireIfReady(t,n).each(function(n){n&&t.kill()}),e.triggerEvent(n,t)&&t.kill()})}),i=ye(on.none()),u=fb(t,"paste",function(t){o.fireIfReady(t,"paste").each(function(n){n&&t.kill()}),e.triggerEvent("paste",t)&&t.kill(),i.set(on.some(v.setTimeout(function(){e.triggerEvent(li(),t)},0)))}),a=fb(t,"keydown",function(n){e.triggerEvent("keydown",n)?n.kill():!0===e.stopBackspace&&function(n){return 8===n.raw().which&&!vn(["input","textarea"],Je(n.target()))&&!db(n.target(),'[contenteditable="true"]')}(n)&&n.prevent()}),c=function(n,t){return kb?lb(n,"focus",t):fb(n,"focusin",t)}(t,function(n){e.triggerEvent("focusin",n)&&n.kill()}),s=ye(on.none()),f=function(n,t){return kb?lb(n,"blur",t):fb(n,"focusout",t)}(t,function(n){e.triggerEvent("focusout",n)&&n.kill(),s.set(on.some(v.setTimeout(function(){e.triggerEvent(fi(),n)},0)))});return{unbind:function(){bn(r,function(n){n.unbind()}),a.unbind(),c.unbind(),f.unbind(),u.unbind(),i.get().each(v.clearTimeout),s.get().each(v.clearTimeout)}}}function wb(n,t){var e=Nn(n,"target").map(function(n){return n()}).getOr(t);return ye(e)}function Sb(n,o,t,e,r,i){var u=n(o,e),a=function(n,t){var e=ye(!1),o=ye(!1);return{stop:function(){e.set(!0)},cut:function(){o.set(!0)},isStopped:e.get,isCut:o.get,event:nn(n),setSource:t.set,getSource:t.get}}(t,r);return u.fold(function(){return i.logEventNoHandlers(o,e),Eb.complete()},function(t){var e=t.descHandler();return tr(e)(a),a.isStopped()?(i.logEventStopped(o,t.element(),e.purpose()),Eb.stopped()):a.isCut()?(i.logEventCut(o,t.element(),e.purpose()),Eb.complete()):To(t.element()).fold(function(){return i.logNoParent(o,t.element(),e.purpose()),Eb.complete()},function(n){return i.logEventResponse(o,t.element(),e.purpose()),Eb.resume(n)})})}function Cb(n,t,e){var o=function(n){var t=ye(!1);return{stop:function(){t.set(!0)},cut:Z,isStopped:t.get,isCut:nn(!1),event:nn(n),setSource:r("Cannot set source of a broadcasted event"),getSource:r("Cannot get source of a broadcasted event")}}(t);return bn(n,function(n){var t=n.descHandler();tr(t)(o)}),o.isStopped()}var kb=Ht().browser.isFirefox(),Ob=qn([dt("triggerEvent"),St("stopBackspace",!0)]),Eb=Tn([{stopped:[]},{resume:["element"]},{complete:[]}]),Tb=function(t,e,o,n,r,i){return Sb(t,e,o,n,r,i).fold(function(){return!0},function(n){return Tb(t,e,o,n,r,i)},function(){return!1})},Bb=function(n,t,e,o,r){var i=wb(e,o);return Tb(n,t,e,o,i,r)},Db=xo("element","descHandler"),Ab=function(n,t){return{id:nn(n),descHandler:nn(t)}};function _b(){var i={};return{registerId:function(o,r,n){Cn(n,function(n,t){var e=i[t]!==undefined?i[t]:{};e[r]=eu(n,o),i[t]=e})},unregisterId:function(e){Cn(i,function(n,t){n.hasOwnProperty(e)&&delete n[e]})},filterByType:function(n){return Nn(i,n).map(function(n){return On(n,function(n,t){return Ab(t,n)})}).getOr([])},find:function(n,t,e){var o=Vn(t)(i);return Ur(e,function(n){return function(e,o){return Ki(o).fold(function(){return on.none()},function(n){var t=Vn(n);return e.bind(t).map(function(n){return Db(o,n)})})}(o,n)},n)}}}function Mb(){function o(n){var t=n.element();return Ki(t).fold(function(){return function(n,t){var e=Yo(Xi+n);return qi(t,e),e}("uid-",n.element())},function(n){return n})}var r=_b(),i={},u=function(n){Ki(n.element()).each(function(n){delete i[n],r.unregisterId(n)})};return{find:function(n,t,e){return r.find(n,t,e)},filter:function(n){return r.filterByType(n)},register:function(n){var t=o(n);$(i,t)&&function(n,t){var e=i[t];if(e!==n)throw new Error('The tagId "'+t+'" is already used by: '+Xo(e.element())+"\nCannot use it for: "+Xo(n.element())+"\nThe conflicting element is"+(Qe(e.element())?" ":" not ")+"already in the DOM");u(n)}(n,t);var e=[n];r.registerId(e,t,n.events()),i[t]=n},unregister:u,getById:function(n){return Vn(n)(i)}}}function Fb(e){function o(t){return To(e.element()).fold(function(){return!0},function(n){return jt(t,n)})}function r(n,t){return u.find(o,n,t)}function i(e){var n=u.filter(di());bn(n,function(n){var t=n.descHandler();tr(t)(e)})}var u=Mb(),n=xb(e.element(),{triggerEvent:function(t,e){return Gu(t,e.target(),function(n){return function(n,t,e,o){var r=e.target();return Bb(n,t,e,r,o)}(r,t,e,n)})}}),a={debugInfo:nn("real"),triggerEvent:function(t,e,o){Gu(t,e,function(n){Bb(r,t,o,e,n)})},triggerFocus:function(t,e){Ki(t).fold(function(){Sa(t)},function(n){Gu(si(),t,function(n){!function(n,t,e,o,r){var i=wb(e,o);Sb(n,t,e,o,i,r)}(r,si(),{originator:nn(e),kill:Z,prevent:Z,target:nn(t)},t,n)})})},triggerEscape:function(n,t){a.triggerEvent("keydown",n.element(),t.event())},getByUid:function(n){return g(n)},getByDom:function(n){return p(n)},build:au,addToGui:function(n){s(n)},removeFromGui:function(n){f(n)},addToWorld:function(n){t(n)},removeFromWorld:function(n){c(n)},broadcast:function(n){l(n)},broadcastOn:function(n,t){d(n,t)},broadcastEvent:function(n,t){m(n,t)},isConnected:nn(!0)},t=function(n){n.connect(a),zr(n.element())||(u.register(n),bn(n.components(),t),a.triggerEvent(yi(),n.element(),{target:nn(n.element())}))},c=function(n){zr(n.element())||(bn(n.components(),c),u.unregister(n)),n.disconnect()},s=function(n){vs(e,n)},f=function(n){ys(n)},l=function(n){i({universal:nn(!0),data:nn(n)})},d=function(n,t){i({universal:nn(!1),channels:nn(n),data:nn(t)})},m=function(n,t){var e=u.filter(n);return Cb(e,t)},g=function(n){return u.getById(n).fold(function(){return an.error(new Error('Could not find component with uid: "'+n+'" in system.'))},an.value)},p=function(n){var t=Ki(n).getOr("not found");return g(t)};return t(e),{root:nn(e),element:e.element,destroy:function(){n.unbind(),zi(e.element())},add:s,remove:f,getByUid:g,getByDom:p,addToWorld:t,removeFromWorld:c,broadcast:l,broadcastOn:d,broadcastEvent:m}}function Ib(n){return n.getParam("height",Math.max(n.getElement().offsetHeight,200))}function Rb(n){return n.getParam("width",Vh.DOM.getStyle(n.getElement(),"width"))}function Vb(n){return on.from(n.settings.min_width).filter(mn)}function Nb(n){return on.from(n.settings.min_height).filter(mn)}function Hb(n){return on.from(n.getParam("max_width")).filter(mn)}function Pb(n){return on.from(n.getParam("max_height")).filter(mn)}function zb(n){return!1!==n.getParam("menubar",!0,"boolean")}function Lb(n){var t=n.getParam("toolbar",!0),e=!0===t,o=cn(t),r=fn(t)&&0<t.length;return!Yb(n)&&(r||o||e)}function jb(t){var n=wn(t.settings),e=C(n,function(n){return/^toolbar([1-9])$/.test(n)}),o=S(e,function(n){return t.getParam(n,!1,"string")}),r=C(o,function(n){return"string"==typeof n});return 0<r.length?on.some(r):on.none()}var Ub,Wb,Gb=Al({name:"Container",factory:function(n){var t=n.dom,e=t.attributes,o=c(t,["attributes"]);return{uid:n.uid,dom:N({tag:"div",attributes:N({role:"presentation"},e)},o),components:n.components,behaviours:Rs(n.containerBehaviours),events:n.events,domModification:n.domModification,eventOrder:n.eventOrder}},configFields:[St("components",[]),Is("containerBehaviours",[]),St("events",{}),St("domModification",{}),St("eventOrder",{})]}),Xb=tinymce.util.Tools.resolve("tinymce.EditorManager"),Yb=function(n){return jb(n).fold(function(){return 0<n.getParam("toolbar",[],"string[]").length},function(){return!0})};(Wb=Ub=Ub||{})["default"]="",Wb.floating="floating",Wb.sliding="sliding",Wb.scrolling="scrolling";function qb(n){return n.getParam("toolbar_drawer","","string")}function Kb(n){var t=function(n){return n.getParam("fixed_toolbar_container","","string")}(n);return 0<t.length&&n.inline?Ou(Lr(),t):on.none()}function Jb(n){return n.inline&&Kb(n).isSome()}function $b(n){return n.inline&&!zb(n)&&!Lb(n)&&!Yb(n)}function Qb(n){return(n.getParam("toolbar_sticky",!1,"boolean")||n.inline)&&!Jb(n)&&!$b(n)}function Zb(n){return n.touches===undefined||1!==n.touches.length?on.none():on.some(n.touches[0])}function ny(n){return ya([bg.config({onFocus:!1===n.selectOnFocus?Z:function(n){var t=n.element(),e=Tr(t);t.dom().setSelectionRange(0,e.length)}})])}function ty(n){return{tag:n.tag,attributes:N({type:"text"},n.inputAttributes),styles:n.inputStyles,classes:n.inputClasses}}var ey,oy,ry,iy,uy=function(e){var o=ye(on.none()),r=ye(!1),i=$g(function(n){e.fire("longpress",N(N({},n),{type:"longpress"})),r.set(!0)},400);e.on("touchstart",function(e){Zb(e).each(function(n){i.cancel();var t={x:nn(n.clientX),y:nn(n.clientY),target:nn(e.target)};i.throttle(e),r.set(!1),o.set(on.some(t))})},!0),e.on("touchmove",function(n){i.cancel(),Zb(n).each(function(t){o.get().each(function(n){!function(n,t){var e=Math.abs(n.clientX-t.x()),o=Math.abs(n.clientY-t.y());return 5<e||5<o}(t,n)||(o.set(on.none()),r.set(!1),e.fire("longpresscancel"))})})},!0),e.on("touchend touchcancel",function(t){i.cancel(),"touchcancel"!==t.type&&o.get().filter(function(n){return n.target().isEqualNode(t.target)}).each(function(){r.get()?t.preventDefault():e.fire("tap",{touches:t.touches}).isDefaultPrevented()&&t.preventDefault()})},!0)},ay=Yo("form-component-change"),cy=Yo("form-close"),sy=Yo("form-cancel"),fy=Yo("form-action"),ly=Yo("form-submit"),dy=Yo("form-block"),my=Yo("form-unblock"),gy=Yo("form-tabchange"),py=Yo("form-resize"),hy=nn([St("prefix","form-field"),Is("fieldBehaviours",[nd,nl])]),vy=nn([Cl({schema:[ct("dom")],name:"label"}),Cl({factory:{sketch:function(n){return{uid:n.uid,dom:{tag:"span",styles:{display:"none"},attributes:{"aria-hidden":"true"},innerHtml:n.text}}}},schema:[ct("text")],name:"aria-descriptor"}),wl({factory:{sketch:function(n){var t=X(n,["factory"]);return n.factory.sketch(t)}},schema:[ct("factory")],name:"field"})]),by=_l({name:"FormField",configFields:hy(),partFields:vy(),factory:function(r,n,t,e){var o=Vs(r.fieldBehaviours,[nd.config({find:function(n){return Ks(n,r,"field")}}),nl.config({store:{mode:"manual",getValue:function(n){return nd.getCurrent(n).bind(nl.getValue)},setValue:function(n,t){nd.getCurrent(n).each(function(n){nl.setValue(n,t)})}}})]),i=so([Ri(function(n,t){var o=$s(n,r,["label","field","aria-descriptor"]);o.field().each(function(e){var t=Yo(r.prefix);o.label().each(function(n){zo(n.element(),"for",t),zo(e.element(),"id",t)}),o["aria-descriptor"]().each(function(n){var t=Yo(r.prefix);zo(n.element(),"id",t),zo(e.element(),"aria-describedby",t)})})})]),u={getField:function(n){return Ks(n,r,"field")},getLabel:function(n){return Ks(n,r,"label")}};return{uid:r.uid,dom:r.dom,components:n,behaviours:o,events:i,apis:u}},apis:{getField:function(n,t){return n.getField(t)},getLabel:function(n,t){return n.getLabel(t)}}}),yy=nn([ht("data"),St("inputAttributes",{}),St("inputStyles",{}),St("tag","input"),St("inputClasses",[]),Ku("onSetValue"),St("styles",{}),St("eventOrder",{}),Is("inputBehaviours",[nl,bg]),St("selectOnFocus",!0)]),xy=Al({name:"Input",configFields:yy(),factory:function(n,t){return{uid:n.uid,dom:ty(n),components:[],behaviours:function(n){return N(N({},ny(n)),Vs(n.inputBehaviours,[nl.config({store:{mode:"manual",initialValue:n.data.getOr(undefined),getValue:function(n){return Tr(n.element())},setValue:function(n,t){Tr(n.element())!==t&&Br(n.element(),t)}},onSetValue:n.onSetValue})]))}(n),eventOrder:n.eventOrder}}}),wy={},Sy={exports:wy};ey=undefined,oy=wy,ry=Sy,iy=undefined,function(n){"object"==typeof oy&&void 0!==ry?ry.exports=n():"function"==typeof ey&&ey.amd?ey([],n):("undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).EphoxContactWrapper=n()}(function(){return function f(i,u,a){function c(t,n){if(!u[t]){if(!i[t]){var e="function"==typeof iy&&iy;if(!n&&e)return e(t,!0);if(s)return s(t,!0);var o=new Error("Cannot find module '"+t+"'");throw o.code="MODULE_NOT_FOUND",o}var r=u[t]={exports:{}};i[t][0].call(r.exports,function(n){return c(i[t][1][n]||n)},r,r.exports,f,i,u,a)}return u[t].exports}for(var s="function"==typeof iy&&iy,n=0;n<a.length;n++)c(a[n]);return c}({1:[function(n,t,e){var o,r,i=t.exports={};function u(){throw new Error("setTimeout has not been defined")}function a(){throw new Error("clearTimeout has not been defined")}function c(n){if(o===setTimeout)return setTimeout(n,0);if((o===u||!o)&&setTimeout)return o=setTimeout,setTimeout(n,0);try{return o(n,0)}catch(t){try{return o.call(null,n,0)}catch(t){return o.call(this,n,0)}}}!function(){try{o="function"==typeof setTimeout?setTimeout:u}catch(n){o=u}try{r="function"==typeof clearTimeout?clearTimeout:a}catch(n){r=a}}();var s,f=[],l=!1,d=-1;function m(){l&&s&&(l=!1,s.length?f=s.concat(f):d=-1,f.length&&g())}function g(){if(!l){var n=c(m);l=!0;for(var t=f.length;t;){for(s=f,f=[];++d<t;)s&&s[d].run();d=-1,t=f.length}s=null,l=!1,function e(n){if(r===clearTimeout)return clearTimeout(n);if((r===a||!r)&&clearTimeout)return r=clearTimeout,clearTimeout(n);try{return r(n)}catch(t){try{return r.call(null,n)}catch(t){return r.call(this,n)}}}(n)}}function p(n,t){this.fun=n,this.array=t}function h(){}i.nextTick=function(n){var t=new Array(arguments.length-1);if(1<arguments.length)for(var e=1;e<arguments.length;e++)t[e-1]=arguments[e];f.push(new p(n,t)),1!==f.length||l||c(g)},p.prototype.run=function(){this.fun.apply(null,this.array)},i.title="browser",i.browser=!0,i.env={},i.argv=[],i.version="",i.versions={},i.on=h,i.addListener=h,i.once=h,i.off=h,i.removeListener=h,i.removeAllListeners=h,i.emit=h,i.prependListener=h,i.prependOnceListener=h,i.listeners=function(n){return[]},i.binding=function(n){throw new Error("process.binding is not supported")},i.cwd=function(){return"/"},i.chdir=function(n){throw new Error("process.chdir is not supported")},i.umask=function(){return 0}},{}],2:[function(n,l,t){(function(t){function o(){}function i(n){if("object"!=typeof this)throw new TypeError("Promises must be constructed via new");if("function"!=typeof n)throw new TypeError("not a function");this._state=0,this._handled=!1,this._value=undefined,this._deferreds=[],f(n,this)}function r(o,r){for(;3===o._state;)o=o._value;0!==o._state?(o._handled=!0,i._immediateFn(function(){var n=1===o._state?r.onFulfilled:r.onRejected;if(null!==n){var t;try{t=n(o._value)}catch(e){return void a(r.promise,e)}u(r.promise,t)}else(1===o._state?u:a)(r.promise,o._value)})):o._deferreds.push(r)}function u(n,t){try{if(t===n)throw new TypeError("A promise cannot be resolved with itself.");if(t&&("object"==typeof t||"function"==typeof t)){var e=t.then;if(t instanceof i)return n._state=3,n._value=t,void c(n);if("function"==typeof e)return void f(function o(n,t){return function(){n.apply(t,arguments)}}(e,t),n)}n._state=1,n._value=t,c(n)}catch(r){a(n,r)}}function a(n,t){n._state=2,n._value=t,c(n)}function c(n){2===n._state&&0===n._deferreds.length&&i._immediateFn(function(){n._handled||i._unhandledRejectionFn(n._value)});for(var t=0,e=n._deferreds.length;t<e;t++)r(n,n._deferreds[t]);n._deferreds=null}function s(n,t,e){this.onFulfilled="function"==typeof n?n:null,this.onRejected="function"==typeof t?t:null,this.promise=e}function f(n,t){var e=!1;try{n(function(n){e||(e=!0,u(t,n))},function(n){e||(e=!0,a(t,n))})}catch(o){if(e)return;e=!0,a(t,o)}}var n,e;n=this,e=setTimeout,i.prototype["catch"]=function(n){return this.then(null,n)},i.prototype.then=function(n,t){var e=new this.constructor(o);return r(this,new s(n,t,e)),e},i.all=function(n){var c=Array.prototype.slice.call(n);return new i(function(r,i){if(0===c.length)return r([]);var u=c.length;function a(t,n){try{if(n&&("object"==typeof n||"function"==typeof n)){var e=n.then;if("function"==typeof e)return void e.call(n,function(n){a(t,n)},i)}c[t]=n,0==--u&&r(c)}catch(o){i(o)}}for(var n=0;n<c.length;n++)a(n,c[n])})},i.resolve=function(t){return t&&"object"==typeof t&&t.constructor===i?t:new i(function(n){n(t)})},i.reject=function(e){return new i(function(n,t){t(e)})},i.race=function(r){return new i(function(n,t){for(var e=0,o=r.length;e<o;e++)r[e].then(n,t)})},i._immediateFn="function"==typeof t?function(n){t(n)}:function(n){e(n,0)},i._unhandledRejectionFn=function(n){"undefined"!=typeof console&&console&&console.warn("Possible Unhandled Promise Rejection:",n)},i._setImmediateFn=function(n){i._immediateFn=n},i._setUnhandledRejectionFn=function(n){i._unhandledRejectionFn=n},void 0!==l&&l.exports?l.exports=i:n.Promise||(n.Promise=i)}).call(this,n("timers").setImmediate)},{timers:3}],3:[function(c,n,s){(function(n,t){var o=c("process/browser.js").nextTick,e=Function.prototype.apply,r=Array.prototype.slice,i={},u=0;function a(n,t){this._id=n,this._clearFn=t}s.setTimeout=function(){return new a(e.call(setTimeout,window,arguments),clearTimeout)},s.setInterval=function(){return new a(e.call(setInterval,window,arguments),clearInterval)},s.clearTimeout=s.clearInterval=function(n){n.close()},a.prototype.unref=a.prototype.ref=function(){},a.prototype.close=function(){this._clearFn.call(window,this._id)},s.enroll=function(n,t){clearTimeout(n._idleTimeoutId),n._idleTimeout=t},s.unenroll=function(n){clearTimeout(n._idleTimeoutId),n._idleTimeout=-1},s._unrefActive=s.active=function(n){clearTimeout(n._idleTimeoutId);var t=n._idleTimeout;0<=t&&(n._idleTimeoutId=setTimeout(function(){n._onTimeout&&n._onTimeout()},t))},s.setImmediate="function"==typeof n?n:function(n){var t=u++,e=!(arguments.length<2)&&r.call(arguments,1);return i[t]=!0,o(function(){i[t]&&(e?n.apply(null,e):n.call(null),s.clearImmediate(t))}),t},s.clearImmediate="function"==typeof t?t:function(n){delete i[n]}}).call(this,c("timers").setImmediate,c("timers").clearImmediate)},{"process/browser.js":1,timers:3}],4:[function(n,t,e){var o=n("promise-polyfill"),r="undefined"!=typeof window?window:Function("return this;")();t.exports={boltExport:r.Promise||o}},{"promise-polyfill":2}]},{},[4])(4)});function Cy(n){v.setTimeout(function(){throw n},0)}function ky(n){var t=Je(n);return vn(Py,t)}function Oy(n,t){var e=t.getRoot(n).getOr(n.element());mr(e,t.invalidClass),t.notify.each(function(t){ky(n.element())&&zo(n.element(),"aria-invalid",!1),t.getContainer(n).each(function(n){Ho(n,t.validHtml)}),t.onValid(n)})}function Ey(t,n,e,o){var r=n.getRoot(t).getOr(t.element());lr(r,n.invalidClass),n.notify.each(function(n){ky(t.element())&&zo(t.element(),"aria-invalid",!0),n.getContainer(t).each(function(n){Ho(n,o)}),n.onInvalid(t,o)})}function Ty(t,n,e){return n.validator.fold(function(){return Hy(an.value(!0))},function(n){return n.validate(t)})}function By(t,e,n){return e.notify.each(function(n){n.onValidate(t)}),Ty(t,e).map(function(n){return t.getSystem().isConnected()?n.fold(function(n){return Ey(t,e,0,n),an.error(n)},function(n){return Oy(t,e),an.value(n)}):an.error("No longer in system")})}function Dy(n,t,e,o){var r=Yy(n,t,e,o);return by.sketch(r)}function Ay(n,t){return by.parts().label({dom:{tag:"label",classes:["tox-label"],innerHtml:t.translate(n)}})}var _y,My,Fy=Sy.exports.boltExport,Iy=function(n){var e=on.none(),t=[],o=function(n){r()?u(n):t.push(n)},r=function(){return e.isSome()},i=function(n){bn(n,u)},u=function(t){e.each(function(n){v.setTimeout(function(){t(n)},0)})};return n(function(n){e=on.some(n),i(t),t=[]}),{get:o,map:function(e){return Iy(function(t){o(function(n){t(e(n))})})},isReady:r}},Ry={nu:Iy,pure:function(t){return Iy(function(n){n(t)})}},Vy=function(e){function n(n){e().then(n,Cy)}return{map:function(n){return Vy(function(){return e().then(n)})},bind:function(t){return Vy(function(){return e().then(function(n){return t(n).toPromise()})})},anonBind:function(n){return Vy(function(){return e().then(function(){return n.toPromise()})})},toLazy:function(){return Ry.nu(n)},toCached:function(){var n=null;return Vy(function(){return null===n&&(n=e()),n})},toPromise:e,get:n}},Ny=function(n){return Vy(function(){return new Fy(n)})},Hy=function(n){return Vy(function(){return Fy.resolve(n)})},Py=["input","textarea"],zy=/* */Object.freeze({markValid:Oy,markInvalid:Ey,query:Ty,run:By,isInvalid:function(n,t){var e=t.getRoot(n).getOr(n.element());return gr(e,t.invalidClass)}}),Ly=/* */Object.freeze({events:function(t,n){return t.validator.map(function(n){return so([mo(n.onEvent,function(n){By(n,t).get(l)})].concat(n.validateOnLoad?[Ri(function(n){By(n,t).get(Z)})]:[]))}).getOr({})}}),jy=[ct("invalidClass"),St("getRoot",on.none),wt("notify",[St("aria","alert"),St("getContainer",on.none),St("validHtml",""),Ku("onValid"),Ku("onInvalid"),Ku("onValidate")]),wt("validator",[ct("validate"),St("onEvent","input"),St("validateOnLoad",!0)])],Uy=xa({fields:jy,name:"invalidating",active:Ly,apis:zy,extra:{validation:function(e){return function(n){var t=nl.getValue(n);return Hy(e(t))}}}}),Wy=/* */Object.freeze({exhibit:function(n,t){return nr({attributes:K([{key:t.tabAttr,value:"true"}])})}}),Gy=[St("tabAttr","data-alloy-tabstop")],Xy=xa({fields:Gy,name:"tabstopping",active:Wy}),Yy=function(n,t,e,o){return{dom:qy(e),components:n.toArray().concat([t]),fieldBehaviours:ya(o)}},qy=function(n){return{tag:"div",classes:["tox-form__group"].concat(n)}},Ky=/* */Object.freeze({getCoupled:function(n,t,e,o){return e.getOrCreate(n,t,o)}}),Jy=[st("others",nt(an.value,de()))],$y=xa({fields:Jy,name:"coupling",apis:Ky,state:/* */Object.freeze({init:function(n){var i={},t=nn({});return tu({readState:t,getOrCreate:function(e,o,r){var n=wn(o.others);if(n)return Nn(i,r).getOrThunk(function(){var n=Nn(o.others,r).getOrDie("No information found for coupled component: "+r)(e),t=e.getSystem().build(n);return i[r]=t});throw new Error("Cannot find coupled component: "+r+". Known coupled components: "+JSON.stringify(n,null,2))}})}})}),Qy=nn("sink"),Zy=nn(Cl({name:Qy(),overrides:nn({dom:{tag:"div"},behaviours:ya([_f.config({useFixed:a})]),events:so([bo(ti()),bo(qr()),bo(ii())])})}));(My=_y=_y||{})[My.HighlightFirst=0]="HighlightFirst",My[My.HighlightNone=1]="HighlightNone";function nx(n,t){var e=n.getHotspot(t).getOr(t),o=n.getAnchorOverrides();return n.layouts.fold(function(){return{anchor:"hotspot",hotspot:e,overrides:o}},function(n){return{anchor:"hotspot",hotspot:e,overrides:o,layouts:n}})}function tx(n,t,e,o,r,i,u){return function(n,t,r,e,i,o,u){var a=function(n,t,e){return(0,n.fetch)(e).map(t)}(n,t,e),c=Cw(e,n);return a.map(function(n){return n.bind(function(n){return on.from(jg.sketch(N(N({},o.menu()),{uid:qo(""),data:n,highlightImmediately:u===_y.HighlightFirst,onOpenMenu:function(n,t){var e=c().getOrDie();_f.position(e,r,t),Lf.decloak(i)},onOpenSubmenu:function(n,t,e){var o=c().getOrDie();_f.position(o,{anchor:"submenu",item:t},e),Lf.decloak(i)},onRepositionMenu:function(n,t,e){var o=c().getOrDie();_f.position(o,r,t),bn(e,function(n){_f.position(o,{anchor:"submenu",item:n.triggeringItem},n.triggeredMenu)})},onEscape:function(){return bg.focus(e),Lf.close(i),on.some(!0)}})))})})}(n,t,nx(n,e),e,o,r,u).map(function(n){return n.fold(function(){Lf.isOpen(o)&&Lf.close(o)},function(n){Lf.cloak(o),Lf.open(o,n),i(o)}),o})}function ex(n,t,e,o,r,i,u){return Lf.close(o),Hy(o)}function ox(n,t,e,o,r,i){var u=$y.getCoupled(e,"sandbox");return(Lf.isOpen(u)?ex:tx)(n,t,e,u,o,r,i)}function rx(n,t,e){var o=nd.getCurrent(t).getOr(t),r=gu(n.element());e?xr(o.element(),"min-width",r+"px"):function(n,t){Nu.set(n,t)}(o.element(),r)}function ix(n){Lf.getState(n).each(function(n){jg.repositionMenus(n)})}function ux(o,r,i){var u=Tu(),n=Cw(r,o);return{dom:{tag:"div",classes:o.sandboxClasses,attributes:{id:u.id(),role:"listbox"}},behaviours:el(o.sandboxBehaviours,[nl.config({store:{mode:"memory",initialValue:r}}),Lf.config({onOpen:function(n,t){var e=nx(o,r);u.link(r.element()),o.matchWidth&&rx(e.hotspot,t,o.useMinWidth),o.onOpen(e,n,t),i!==undefined&&i.onOpen!==undefined&&i.onOpen(n,t)},onClose:function(n,t){u.unlink(r.element()),i!==undefined&&i.onClose!==undefined&&i.onClose(n,t)},isPartOf:function(n,t,e){return ju(t,e)||ju(r,e)},getAttachPoint:function(){return n().getOrDie()}}),nd.config({find:function(n){return Lf.getState(n).bind(function(n){return nd.getCurrent(n)})}}),dc.config({channels:N(N({},Ts({isExtraPart:nn(!1)})),Bs({isExtraPart:nn(!1),doReposition:ix}))})])}}function ax(n){var t=$y.getCoupled(n,"sandbox");ix(t)}function cx(){return[St("sandboxClasses",[]),tl("sandboxBehaviours",[nd,dc,Lf,nl])]}function sx(e,t,o){function r(n,t){io(n,Dw,{value:t})}var n=by.parts().field({factory:xy,inputClasses:["tox-textfield"],onSetValue:function(n){return Uy.run(n).get(function(){})},inputBehaviours:ya([Xy.config({}),Uy.config({invalidClass:"tox-textbox-field-invalid",getRoot:function(n){return To(n.element())},notify:{onValid:function(n){var t=nl.getValue(n);io(n,Bw,{color:t})}},validator:{validateOnLoad:!1,validate:function(n){var t=nl.getValue(n);if(0===t.length)return Hy(an.value(!0));var e=we.fromTag("span");xr(e,"background-color",t);var o=Cr(e,"background-color").fold(function(){return an.error("blah")},function(n){return an.value(t)});return Hy(o)}}})]),selectOnFocus:!1}),i=e.label.map(function(n){return Ay(n,t.providers)}),u=bm(function(e,o){return Ew.sketch({dom:e.dom,components:e.components,toggleClass:"mce-active",dropdownBehaviours:ya([Tw.config({}),Xy.config({})]),layouts:e.layouts,sandboxClasses:["tox-dialog__popups"],lazySink:o.getSink,fetch:function(t){return Ny(function(n){return e.fetch(n)}).map(function(n){return on.from(ab(Dn(Bv(Yo("menu-value"),n,function(n){e.onItemAction(t,n)},e.columns,e.presets,Ih.CLOSE_ON_EXECUTE,function(){return!1},o.providers),{movement:Dv(e.columns,e.presets)})))})},parts:{menu:Cv(0,0,e.presets)}})}({dom:{tag:"span",attributes:{"aria-label":t.providers.translate("Color swatch")}},layouts:on.some({onRtl:function(){return[aa]},onLtr:function(){return[ca]}}),components:[],fetch:Wv.getFetch(o.getColors(),o.hasCustomColors()),columns:o.getColorCols(),presets:"color",onItemAction:function(n,e){u.getOpt(n).each(function(t){"custom"===e?o.colorPicker(function(n){n.fold(function(){return ro(t,Aw)},function(n){r(t,n),Nv(n)})},"#ffffff"):r(t,"remove"===e?"":e)})}},t));return by.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:i.toArray().concat([{dom:{tag:"div",classes:["tox-color-input"]},components:[n,u.asSpec()]}]),fieldBehaviours:ya([Jd("form-field-events",[mo(Bw,function(n,t){u.getOpt(n).each(function(n){xr(n.element(),"background-color",t.event().color())}),io(n,ay,{name:e.name})}),mo(Dw,function(t,e){by.getField(t).each(function(n){nl.setValue(n,e.event().value()),nd.getCurrent(t).each(bg.focus)})}),mo(Aw,function(t,n){by.getField(t).each(function(n){nd.getCurrent(t).each(bg.focus)})})])])})}function fx(n,t,e){return{hue:nn(n),saturation:nn(t),value:nn(e)}}function lx(t){return Cl({name:t+"-edge",overrides:function(n){return n.model.manager.edgeActions[t].fold(function(){return{}},function(o){return{events:so([go(Wr(),o,[n]),go(qr(),o,[n]),go(Kr(),function(n,t,e){e.mouseIsDown.get()&&o(n,e)},[n])])}})}})}function dx(n){var t=n.event().raw();if(function(n){return-1!==n.type.indexOf("touch")}(t)){var e=t;return e.touches!==undefined&&1===e.touches.length?on.some(e.touches[0]).map(function(n){return Ru(n.clientX,n.clientY)}):on.none()}var o=t;return o.clientX!==undefined?on.some(o).map(function(n){return Ru(n.clientX,n.clientY)}):on.none()}function mx(n){return n.model.minX}function gx(n){return n.model.minY}function px(n){return n.model.minX-1}function hx(n){return n.model.minY-1}function vx(n){return n.model.maxX}function bx(n){return n.model.maxY}function yx(n){return n.model.maxX+1}function xx(n){return n.model.maxY+1}function wx(n,t,e){return t(n)-e(n)}function Sx(n){return wx(n,vx,mx)}function Cx(n){return wx(n,bx,gx)}function kx(n){return Sx(n)/2}function Ox(n){return Cx(n)/2}function Ex(n){return n.stepSize}function Tx(n){return n.snapToGrid}function Bx(n){return n.snapStart}function Dx(n){return n.rounded}function Ax(n,t){return n[t+"-edge"]!==undefined}function _x(n){return Ax(n,"left")}function Mx(n){return Ax(n,"right")}function Fx(n){return Ax(n,"top")}function Ix(n){return Ax(n,"bottom")}function Rx(n){return n.model.value.get()}function Vx(n){return{x:nn(n)}}function Nx(n){return{y:nn(n)}}function Hx(n,t){return{x:nn(n),y:nn(t)}}function Px(n,t){io(n,Uw(),{value:t})}function zx(n,t,e,o){return n<t?n:e<n?e:n===t?t-1:Math.max(t,n-o)}function Lx(n,t,e,o){return e<n?n:n<t?t:n===e?e+1:Math.min(e,n+o)}function jx(n,t,e){return Math.max(t,Math.min(e,n))}function Ux(n){var t=n.min,e=n.max,o=n.range,r=n.value,i=n.step,u=n.snap,a=n.snapStart,c=n.rounded,s=n.hasMinEdge,f=n.hasMaxEdge,l=n.minBound,d=n.maxBound,m=n.screenRange,g=s?t-1:t,p=f?e+1:e;if(r<l)return g;if(d<r)return p;var h=function(n,t,e){return Math.min(e,Math.max(n,t))-t}(r,l,d),v=jx(h/m*o+t,g,p);return u&&t<=v&&v<=e?function(u,e,a,c,n){return n.fold(function(){var n=u-e,t=Math.round(n/c)*c;return jx(e+t,e-1,a+1)},function(n){var t=(u-n)%c,e=Math.round(t/c),o=Math.floor((u-n)/c),r=Math.floor((a-n)/c),i=n+Math.min(r,o+e)*c;return Math.max(n,i)})}(v,t,e,i,a):c?Math.round(v):v}function Wx(n){var t=n.min,e=n.max,o=n.range,r=n.value,i=n.hasMinEdge,u=n.hasMaxEdge,a=n.maxBound,c=n.maxOffset,s=n.centerMinEdge,f=n.centerMaxEdge;return r<t?i?0:s:e<r?u?a:f:(r-t)/o*c}function Gx(n){return n.element().dom().getBoundingClientRect()}function Xx(n,t){return n[t]}function Yx(n){var t=Gx(n);return Xx(t,Ww)}function qx(n){var t=Gx(n);return Xx(t,"right")}function Kx(n){var t=Gx(n);return Xx(t,"top")}function Jx(n){var t=Gx(n);return Xx(t,"bottom")}function $x(n){var t=Gx(n);return Xx(t,"width")}function Qx(n){var t=Gx(n);return Xx(t,"height")}function Zx(n,t,e){return(n+t)/2-e}function nw(n,t){var e=Gx(n),o=Gx(t),r=Xx(e,Ww),i=Xx(e,"right"),u=Xx(o,Ww);return Zx(r,i,u)}function tw(n,t){var e=Gx(n),o=Gx(t),r=Xx(e,"top"),i=Xx(e,"bottom"),u=Xx(o,"top");return Zx(r,i,u)}function ew(n,t){io(n,Uw(),{value:t})}function ow(n){return{x:nn(n)}}function rw(n,t,e){var o={min:mx(t),max:vx(t),range:Sx(t),value:e,step:Ex(t),snap:Tx(t),snapStart:Bx(t),rounded:Dx(t),hasMinEdge:_x(t),hasMaxEdge:Mx(t),minBound:Yx(n),maxBound:qx(n),screenRange:$x(n)};return Ux(o)}function iw(e){return function(n,t){return function(n,t,e){var o=(0<n?Lx:zx)(Rx(e).x(),mx(e),vx(e),Ex(e));return ew(t,ow(o)),on.some(o)}(e,n,t).map(function(){return!0})}}function uw(n,t,e,o,r,i){var u=function(t,n,e,o,r){var i=$x(t),u=o.bind(function(n){return on.some(nw(n,t))}).getOr(0),a=r.bind(function(n){return on.some(nw(n,t))}).getOr(i),c={min:mx(n),max:vx(n),range:Sx(n),value:e,hasMinEdge:_x(n),hasMaxEdge:Mx(n),minBound:Yx(t),minOffset:0,maxBound:qx(t),maxOffset:i,centerMinEdge:u,centerMaxEdge:a};return Wx(c)}(t,i,e,o,r);return Yx(t)-Yx(n)+u}function aw(n,t){io(n,Uw(),{value:t})}function cw(n){return{y:nn(n)}}function sw(n,t,e){var o={min:gx(t),max:bx(t),range:Cx(t),value:e,step:Ex(t),snap:Tx(t),snapStart:Bx(t),rounded:Dx(t),hasMinEdge:Fx(t),hasMaxEdge:Ix(t),minBound:Kx(n),maxBound:Jx(n),screenRange:Qx(n)};return Ux(o)}function fw(e){return function(n,t){return function(n,t,e){var o=(0<n?Lx:zx)(Rx(e).y(),gx(e),bx(e),Ex(e));return aw(t,cw(o)),on.some(o)}(e,n,t).map(function(){return!0})}}function lw(n,t,e,o,r,i){var u=function(t,n,e,o,r){var i=Qx(t),u=o.bind(function(n){return on.some(tw(n,t))}).getOr(0),a=r.bind(function(n){return on.some(tw(n,t))}).getOr(i),c={min:gx(n),max:bx(n),range:Cx(n),value:e,hasMinEdge:Fx(n),hasMaxEdge:Ix(n),minBound:Kx(t),minOffset:0,maxBound:Jx(t),maxOffset:i,centerMinEdge:u,centerMaxEdge:a};return Wx(c)}(t,i,e,o,r);return Kx(t)-Kx(n)+u}function dw(n,t){io(n,Uw(),{value:t})}function mw(n,t){return{x:nn(n),y:nn(t)}}function gw(e,o){return function(n,t){return function(n,t,e,o){var r=0<n?Lx:zx,i=t?Rx(o).x():r(Rx(o).x(),mx(o),vx(o),Ex(o)),u=t?r(Rx(o).y(),gx(o),bx(o),Ex(o)):Rx(o).y();return dw(e,mw(i,u)),on.some(i)}(e,o,n,t).map(function(){return!0})}}function pw(n){return"<alloy.field."+n+">"}function hw(n){return function(n){return kS[n]}(n)}function vw(n,t,e){return nl.config(Dn({store:{mode:"manual",getValue:t,setValue:e}},n.map(function(n){return{store:{initialValue:n}}}).getOr({})))}function bw(n,t,e){return vw(n,function(n){return t(n.element())},function(n,t){return e(n.element(),t)})}function yw(e,t){function o(n,t){t.stop()}function r(n){return function(t,e){bn(n,function(n){n(t,e)})}}function i(n,t){if(!kh.isDisabled(n)){var e=t.event().raw();a(n,e.dataTransfer.files)}}function u(n,t){var e=t.event().raw().target.files;a(n,e)}var a=function(n,t){nl.setValue(n,function(n){var t=new RegExp("("+".jpg,.jpeg,.png,.gif".split(/\s*,\s*/).join("|")+")$","i");return C(xn(n),function(n){return t.test(n.name)})}(t)),io(n,ay,{name:e.name})},c=bm({dom:{tag:"input",attributes:{type:"file",accept:"image/*"},styles:{display:"none"}},behaviours:ya([Jd("input-file-events",[bo(ii()),bo(pi())])])}),n=e.label.map(function(n){return Ay(n,t)}),s=by.parts().field({factory:{sketch:function(n){return{uid:n.uid,dom:{tag:"div",classes:["tox-dropzone-container"]},behaviours:ya([AS([]),wS(),kh.config({}),kg.config({toggleClass:"dragenter",toggleOnExecute:!1}),Jd("dropzone-events",[mo("dragenter",r([o,kg.toggle])),mo("dragleave",r([o,kg.toggle])),mo("dragover",o),mo("drop",r([o,i])),mo(ri(),u)])]),components:[{dom:{tag:"div",classes:["tox-dropzone"],styles:{}},components:[{dom:{tag:"p",innerHtml:t.translate("Drop an image here")}},Xg.sketch({dom:{tag:"button",innerHtml:t.translate("Browse for an image"),styles:{position:"relative"},classes:["tox-button","tox-button--secondary"]},components:[c.asSpec()],action:function(n){c.get(n).element().dom().click()},buttonBehaviours:ya([Xy.config({})])})]}]}}}});return Dy(n,s,["tox-form__group--stretched"],[])}function xw(n){return{dom:{tag:"div",styles:{width:"1px",height:"1px",outline:"none"},attributes:{tabindex:"0"},classes:n},behaviours:ya([bg.config({ignore:!0}),Xy.config({})])}}function ww(n,t){io(n,ti(),{raw:{which:9,shiftKey:t}})}function Sw(n,t){var e=VS&&n.sandboxed,o=N(N({},n.label.map(function(n){return{title:n}}).getOr({})),e?{sandbox:"allow-scripts allow-same-origin"}:{}),r=function(o){var r=ye("");return{getValue:function(n){return r.get()},setValue:function(n,t){if(o)zo(n.element(),"srcdoc",t);else{zo(n.element(),"src","javascript:''");var e=n.element().dom().contentWindow.document;e.open(),e.write(t),e.close()}r.set(t)}}}(e),i=n.label.map(function(n){return Ay(n,t)}),u=by.parts().field({factory:{sketch:function(n){return RS({uid:n.uid,dom:{tag:"iframe",attributes:o},behaviours:ya([Xy.config({}),bg.config({}),BS(on.none(),r.getValue,r.setValue)])})}}});return Dy(i,u,["tox-form__group--stretched"],[])}var Cw=function(t,n){return t.getSystem().getByUid(n.uid+"-"+Qy()).map(function(n){return function(){return an.value(n)}}).getOrThunk(function(){return n.lazySink.fold(function(){return function(){return an.error(new Error("No internal sink is specified, nor could an external sink be found"))}},function(n){return function(){return n(t)}})})},kw=nn([ct("dom"),ct("fetch"),Ku("onOpen"),Ju("onExecute"),St("getHotspot",on.some),St("getAnchorOverrides",nn({})),St("layouts",on.none()),Is("dropdownBehaviours",[kg,$y,dg,bg]),ct("toggleClass"),St("eventOrder",{}),ht("lazySink"),St("matchWidth",!1),St("useMinWidth",!1),ht("role")].concat(cx())),Ow=nn([Sl({schema:[Xu()],name:"menu",defaults:function(n){return{onExecute:n.onExecute}}}),Zy()]),Ew=_l({name:"Dropdown",configFields:kw(),partFields:Ow(),factory:function(t,n,e,o){function r(n){Lf.getState(n).each(function(n){jg.highlightPrimary(n)})}function i(n,t){return uo(n),on.some(!0)}var u,a,c={expand:function(n){kg.isOn(n)||ox(t,function(n){return n},n,o,Z,_y.HighlightNone).get(Z)},open:function(n){kg.isOn(n)||ox(t,function(n){return n},n,o,Z,_y.HighlightFirst).get(Z)},isOpen:kg.isOn,close:function(n){kg.isOn(n)&&ox(t,function(n){return n},n,o,Z,_y.HighlightFirst).get(Z)},repositionMenus:function(n){kg.isOn(n)&&ax(n)}};return{uid:t.uid,dom:t.dom,components:n,behaviours:Vs(t.dropdownBehaviours,[kg.config({toggleClass:t.toggleClass,aria:{mode:"expanded"}}),$y.config({others:{sandbox:function(n){return ux(t,n,{onOpen:function(){kg.on(n)},onClose:function(){kg.off(n)}})}}}),dg.config({mode:"special",onSpace:i,onEnter:i,onDown:function(n,t){if(Ew.isOpen(n)){var e=$y.getCoupled(n,"sandbox");r(e)}else Ew.open(n);return on.some(!0)},onEscape:function(n,t){return Ew.isOpen(n)?(Ew.close(n),on.some(!0)):on.none()}}),bg.config({})]),events:im(on.some(function(n){ox(t,function(n){return n},n,o,r,_y.HighlightFirst).get(Z)})),eventOrder:N(N({},t.eventOrder),(u={},u[mi()]=["disabling","toggling","alloy.base.behaviour"],u)),apis:c,domModification:{attributes:N(N({"aria-haspopup":"true"},t.role.fold(function(){return{}},function(n){return{role:n}})),"button"===t.dom.tag?{type:(a="type",Nn(t.dom,"attributes").bind(function(n){return Nn(n,a)})).getOr("button")}:{})}}},apis:{open:function(n,t){return n.open(t)},expand:function(n,t){return n.expand(t)},close:function(n,t){return n.close(t)},isOpen:function(n,t){return n.isOpen(t)},repositionMenus:function(n,t){return n.repositionMenus(t)}}}),Tw=xa({fields:[],name:"unselecting",active:/* */Object.freeze({events:function(n){return so([fo(ai(),nn(!0))])},exhibit:function(n,t){return nr({styles:{"-webkit-user-select":"none","user-select":"none","-ms-user-select":"none","-moz-user-select":"-moz-none"},attributes:{unselectable:"on"}})}})}),Bw=Yo("color-input-change"),Dw=Yo("color-swatch-change"),Aw=Yo("color-picker-cancel"),_w=nn(Yo("rgb-hex-update")),Mw=nn(Yo("slider-update")),Fw=nn(Yo("palette-update")),Iw=Cl({schema:[ct("dom")],name:"label"}),Rw=lx("top-left"),Vw=lx("top"),Nw=lx("top-right"),Hw=lx("right"),Pw=lx("bottom-right"),zw=lx("bottom"),Lw=lx("bottom-left"),jw=[Iw,lx("left"),Hw,Vw,zw,Rw,Nw,Lw,Pw,wl({name:"thumb",defaults:nn({dom:{styles:{position:"absolute"}}}),overrides:function(n){return{events:so([ho(Wr(),n,"spectrum"),ho(Gr(),n,"spectrum"),ho(Xr(),n,"spectrum"),ho(qr(),n,"spectrum"),ho(Kr(),n,"spectrum"),ho($r(),n,"spectrum")])}}}),wl({schema:[At("mouseIsDown",function(){return ye(!1)})],name:"spectrum",overrides:function(e){function o(t,n){return r.getValueFromEvent(n).map(function(n){return r.setValueFrom(t,e,n)})}var r=e.model.manager;return{behaviours:ya([dg.config({mode:"special",onLeft:function(n){return r.onLeft(n,e)},onRight:function(n){return r.onRight(n,e)},onUp:function(n){return r.onUp(n,e)},onDown:function(n){return r.onDown(n,e)}}),bg.config({})]),events:so([mo(Wr(),o),mo(Gr(),o),mo(qr(),o),mo(Kr(),function(n,t){e.mouseIsDown.get()&&o(n,t)})])}}})],Uw=nn("slider.change.value"),Ww="left",Gw=iw(-1),Xw=iw(1),Yw=on.none,qw=on.none,Kw={"top-left":on.none(),top:on.none(),"top-right":on.none(),right:on.some(function(n,t){Px(n,Vx(yx(t)))}),"bottom-right":on.none(),bottom:on.none(),"bottom-left":on.none(),left:on.some(function(n,t){Px(n,Vx(px(t)))})},Jw=/* */Object.freeze({setValueFrom:function(n,t,e){var o=rw(n,t,e),r=ow(o);return ew(n,r),o},setToMin:function(n,t){var e=mx(t);ew(n,ow(e))},setToMax:function(n,t){var e=vx(t);ew(n,ow(e))},findValueOfOffset:rw,getValueFromEvent:function(n){return dx(n).map(function(n){return n.left()})},findPositionOfValue:uw,setPositionFromValue:function(n,t,e,o){var r=Rx(e),i=uw(n,o.getSpectrum(n),r.x(),o.getLeftEdge(n),o.getRightEdge(n),e),u=gu(t.element())/2;xr(t.element(),"left",i-u+"px")},onLeft:Gw,onRight:Xw,onUp:Yw,onDown:qw,edgeActions:Kw}),$w=on.none,Qw=on.none,Zw=fw(-1),nS=fw(1),tS={"top-left":on.none(),top:on.some(function(n,t){Px(n,Nx(hx(t)))}),"top-right":on.none(),right:on.none(),"bottom-right":on.none(),bottom:on.some(function(n,t){Px(n,Nx(xx(t)))}),"bottom-left":on.none(),left:on.none()},eS=/* */Object.freeze({setValueFrom:function(n,t,e){var o=sw(n,t,e),r=cw(o);return aw(n,r),o},setToMin:function(n,t){var e=gx(t);aw(n,cw(e))},setToMax:function(n,t){var e=bx(t);aw(n,cw(e))},findValueOfOffset:sw,getValueFromEvent:function(n){return dx(n).map(function(n){return n.top()})},findPositionOfValue:lw,setPositionFromValue:function(n,t,e,o){var r=Rx(e),i=lw(n,o.getSpectrum(n),r.y(),o.getTopEdge(n),o.getBottomEdge(n),e),u=fu(t.element())/2;xr(t.element(),"top",i-u+"px")},onLeft:$w,onRight:Qw,onUp:Zw,onDown:nS,edgeActions:tS}),oS=gw(-1,!1),rS=gw(1,!1),iS=gw(-1,!0),uS=gw(1,!0),aS={"top-left":on.some(function(n,t){Px(n,Hx(px(t),hx(t)))}),top:on.some(function(n,t){Px(n,Hx(kx(t),hx(t)))}),"top-right":on.some(function(n,t){Px(n,Hx(yx(t),hx(t)))}),right:on.some(function(n,t){Px(n,Hx(yx(t),Ox(t)))}),"bottom-right":on.some(function(n,t){Px(n,Hx(yx(t),xx(t)))}),bottom:on.some(function(n,t){Px(n,Hx(kx(t),xx(t)))}),"bottom-left":on.some(function(n,t){Px(n,Hx(px(t),xx(t)))}),left:on.some(function(n,t){Px(n,Hx(px(t),Ox(t)))})},cS=/* */Object.freeze({setValueFrom:function(n,t,e){var o=rw(n,t,e.left()),r=sw(n,t,e.top()),i=mw(o,r);return dw(n,i),i},setToMin:function(n,t){var e=mx(t),o=gx(t);dw(n,mw(e,o))},setToMax:function(n,t){var e=vx(t),o=bx(t);dw(n,mw(e,o))},getValueFromEvent:function(n){return dx(n)},setPositionFromValue:function(n,t,e,o){var r=Rx(e),i=uw(n,o.getSpectrum(n),r.x(),o.getLeftEdge(n),o.getRightEdge(n),e),u=lw(n,o.getSpectrum(n),r.y(),o.getTopEdge(n),o.getBottomEdge(n),e),a=gu(t.element())/2,c=fu(t.element())/2;xr(t.element(),"left",i-a+"px"),xr(t.element(),"top",u-c+"px")},onLeft:oS,onRight:rS,onUp:iS,onDown:uS,edgeActions:aS}),sS=_l({name:"Slider",configFields:[St("stepSize",1),St("onChange",Z),St("onChoose",Z),St("onInit",Z),St("onDragStart",Z),St("onDragEnd",Z),St("snapToGrid",!1),St("rounded",!0),ht("snapStart"),st("model",it("mode",{x:[St("minX",0),St("maxX",100),At("value",function(n){return ye(n.mode.minX)}),ct("getInitialValue"),Zu("manager",Jw)],y:[St("minY",0),St("maxY",100),At("value",function(n){return ye(n.mode.minY)}),ct("getInitialValue"),Zu("manager",eS)],xy:[St("minX",0),St("maxX",100),St("minY",0),St("maxY",100),At("value",function(n){return ye({x:nn(n.mode.minX),y:nn(n.mode.minY)})}),ct("getInitialValue"),Zu("manager",cS)]})),Is("sliderBehaviours",[dg,nl]),At("mouseIsDown",function(){return ye(!1)})],partFields:jw,factory:function(i,n,t,e){function u(n){return Js(n,i,"thumb")}function a(n){return Js(n,i,"spectrum")}function o(n){return Ks(n,i,"left-edge")}function r(n){return Ks(n,i,"right-edge")}function c(n){return Ks(n,i,"top-edge")}function s(n){return Ks(n,i,"bottom-edge")}function f(n,t){v.setPositionFromValue(n,t,i,{getLeftEdge:o,getRightEdge:r,getTopEdge:c,getBottomEdge:s,getSpectrum:a})}function l(n,t){h.value.set(t);var e=u(n);return f(n,e),i.onChange(n,e,t),on.some(!0)}function d(e){var n=i.mouseIsDown.get();i.mouseIsDown.set(!1),n&&Ks(e,i,"thumb").each(function(n){var t=h.value.get();i.onChoose(e,n,t)})}function m(n,t){t.stop(),i.mouseIsDown.set(!0),i.onDragStart(n,u(n))}function g(n,t){t.stop(),i.onDragEnd(n,u(n)),d(n)}var p,h=i.model,v=h.manager;return{uid:i.uid,dom:i.dom,components:n,behaviours:Vs(i.sliderBehaviours,[dg.config({mode:"special",focusIn:function(n){return Ks(n,i,"spectrum").map(dg.focusIn).map(nn(!0))}}),nl.config({store:{mode:"manual",getValue:function(n){return h.value.get()}}}),dc.config({channels:(p={},p[Wf()]={onReceive:d},p)})]),events:so([mo(Uw(),function(n,t){l(n,t.event().value())}),Ri(function(n,t){var e=h.getInitialValue();h.value.set(e);var o=u(n);f(n,o);var r=a(n);i.onInit(n,o,r,h.value.get())}),mo(Wr(),m),mo(Xr(),g),mo(qr(),m),mo($r(),g)]),apis:{resetToMin:function(n){v.setToMin(n,i)},resetToMax:function(n){v.setToMax(n,i)},changeValue:l,refresh:f},domModification:{styles:{position:"relative"}}}},apis:{resetToMin:function(n,t){n.resetToMin(t)},resetToMax:function(n,t){n.resetToMax(t)},refresh:function(n,t){n.refresh(t)}}}),fS=function(n,t){var e=sS.parts().spectrum({dom:{tag:"div",classes:[t("hue-slider-spectrum")],attributes:{role:"presentation"}}}),o=sS.parts().thumb({dom:{tag:"div",classes:[t("hue-slider-thumb")],attributes:{role:"presentation"}}});return sS.sketch({dom:{tag:"div",classes:[t("hue-slider")],attributes:{role:"presentation"}},rounded:!1,model:{mode:"y",getInitialValue:nn({y:nn(0)})},components:[e,o],sliderBehaviours:ya([bg.config({})]),onChange:function(n,t,e){io(n,Mw(),{value:e})}})},lS=[Is("formBehaviours",[nl])],dS=function(o,n,t){return{uid:o.uid,dom:o.dom,components:n,behaviours:Vs(o.formBehaviours,[nl.config({store:{mode:"manual",getValue:function(n){var t=Qs(n,o);return P(t,function(n,t){return n().bind(function(n){return function(n,t){return n.fold(function(){return an.error(t)},an.value)}(nd.getCurrent(n),"missing current")}).map(nl.getValue)})},setValue:function(e,n){Cn(n,function(t,n){Ks(e,o,n).each(function(n){nd.getCurrent(n).each(function(n){nl.setValue(n,t)})})})}}})]),apis:{getField:function(n,t){return Ks(n,o,t).bind(nd.getCurrent)}}}},mS={getField:Qo(function(n,t,e){return n.getField(t,e)}),sketch:function(n){var e,t=(e=[],{field:function(n,t){return e.push(n),Ws("form",pw(n),t)},record:function(){return e}}),o=n(t),r=t.record(),i=S(r,function(n){return wl({name:n,pname:pw(n)})});return rf("form",lS,i,dS,o)}},gS=Yo("valid-input"),pS=Yo("invalid-input"),hS=Yo("validating-input"),vS="colorcustom.rgb.",bS=function(d,m,g,p){function h(n,t,e,o,r){var i=d(vS+"range"),u=[by.parts().label({dom:{tag:"label",innerHtml:e,attributes:{"aria-label":o}}}),by.parts().field({data:r,factory:xy,inputAttributes:N({type:"text"},"hex"===t?{"aria-live":"polite"}:{}),inputClasses:[m("textfield")],inputBehaviours:ya([function(t,o){return Uy.config({invalidClass:m("invalid"),notify:{onValidate:function(n){io(n,hS,{type:t})},onValid:function(n){io(n,gS,{type:t,value:nl.getValue(n)})},onInvalid:function(n){io(n,pS,{type:t,value:nl.getValue(n)})}},validator:{validate:function(n){var t=nl.getValue(n),e=o(t)?an.value(!0):an.error(d("aria.input.invalid"));return Hy(e)},validateOnLoad:!1}})}(t,n),Xy.config({})]),onSetValue:function(n){Uy.isInvalid(n)&&Uy.run(n).get(Z)}})],a="hex"!==t?[by.parts()["aria-descriptor"]({text:i})]:[];return{dom:{tag:"div",attributes:{role:"presentation"}},components:u.concat(a)}}function v(n,t){var e=t.red(),o=t.green(),r=t.blue();nl.setValue(n,{red:e,green:o,blue:r})}function b(n,t){y.getOpt(n).each(function(n){xr(n.element(),"background-color","#"+t.value())})}var y=bm({dom:{tag:"div",classes:[m("rgba-preview")],styles:{"background-color":"white"},attributes:{role:"presentation"}}});return Al({factory:function(){function r(n){return u[n]().get()}function i(n,t){u[n]().set(t)}function t(n,t){var e=t.event();"hex"!==e.type()?i(e.type(),on.none()):p(n)}function o(e,n,t){var o=parseInt(t,10);i(n,on.some(o)),r("red").bind(function(e){return r("green").bind(function(t){return r("blue").map(function(n){return Gh(e,t,n,1)})})}).each(function(n){var t=function(t,n){var e=Wh(n);return mS.getField(t,"hex").each(function(n){bg.isFocused(n)||nl.setValue(t,{hex:e.value()})}),e}(e,n);b(e,t)})}function e(n,t){var e=t.event();!function(n){return"hex"===n.type()}(e)?o(n,e.type(),e.value()):function(n,t){g(n);var e=zh(t);i("hex",on.some(t));var o=qh(e);v(n,o),a(o),io(n,_w(),{hex:e}),b(n,e)}(n,e.value())}function n(n){return{label:d(vS+n+".label"),description:d(vS+n+".description")}}var u={red:nn(ye(on.some(255))),green:nn(ye(on.some(255))),blue:nn(ye(on.some(255))),hex:nn(ye(on.some("ffffff")))},a=function(n){var t=n.red(),e=n.green(),o=n.blue();i("red",on.some(t)),i("green",on.some(e)),i("blue",on.some(o))},c=n("red"),s=n("green"),f=n("blue"),l=n("hex");return Dn(mS.sketch(function(n){return{dom:{tag:"form",classes:[m("rgb-form")],attributes:{"aria-label":d("aria.color.picker")}},components:[n.field("red",by.sketch(h(Xh,"red",c.label,c.description,255))),n.field("green",by.sketch(h(Xh,"green",s.label,s.description,255))),n.field("blue",by.sketch(h(Xh,"blue",f.label,f.description,255))),n.field("hex",by.sketch(h(Lh,"hex",l.label,l.description,"ffffff"))),y.asSpec()],formBehaviours:ya([Uy.config({invalidClass:m("form-invalid")}),Jd("rgb-form-events",[mo(gS,e),mo(pS,t),mo(hS,t)])])}}),{apis:{updateHex:function(n,t){nl.setValue(n,{hex:t.value()}),function(n,t){var e=qh(t);v(n,e),a(e)}(n,t),b(n,t)}}})},name:"RgbForm",configFields:[],apis:{updateHex:function(n,t,e){n.updateHex(t,e)}},extraApis:{}})},yS=function(n,o){function r(n,t){var e=n.width,o=n.height,r=n.getContext("2d");if(null!==r){r.fillStyle=t,r.fillRect(0,0,e,o);var i=r.createLinearGradient(0,0,e,0);i.addColorStop(0,"rgba(255,255,255,1)"),i.addColorStop(1,"rgba(255,255,255,0)"),r.fillStyle=i,r.fillRect(0,0,e,o);var u=r.createLinearGradient(0,0,0,o);u.addColorStop(0,"rgba(0,0,0,0)"),u.addColorStop(1,"rgba(0,0,0,1)"),r.fillStyle=u,r.fillRect(0,0,e,o)}}var i=sS.parts().spectrum({dom:{tag:"canvas",attributes:{role:"presentation"},classes:[o("sv-palette-spectrum")]}}),u=sS.parts().thumb({dom:{tag:"div",attributes:{role:"presentation"},classes:[o("sv-palette-thumb")],innerHtml:"<div class="+o("sv-palette-inner-thumb")+' role="presentation"></div>'}});return Al({factory:function(n){var t=nn({x:nn(0),y:nn(0)}),e=ya([nd.config({find:on.some}),bg.config({})]);return sS.sketch({dom:{tag:"div",attributes:{role:"presentation"},classes:[o("sv-palette")]},model:{mode:"xy",getInitialValue:t},rounded:!1,components:[i,u],onChange:function(n,t,e){io(n,Fw(),{value:e})},onInit:function(n,t,e,o){r(e.element().dom(),Jh(rv()))},sliderBehaviours:e})},name:"SaturationBrightnessPalette",configFields:[],apis:{setRgba:function(n,t,e){!function(n,t){var e=n.components()[0].element().dom();r(e,Jh(t))}(t,e)}},extraApis:{}})},xS=function(l,d){return Al({name:"ColourPicker",configFields:[ct("dom"),St("onValidHex",Z),St("onInvalidHex",Z)],factory:function(n){function t(n,e){u.getOpt(n).each(function(n){var t=qh(e);s.paletteRgba().set(t),i.setRgba(n,t)})}function e(n,t){f.getOpt(n).each(function(n){r.updateHex(n,t)})}function a(t,e,n){bn(n,function(n){n(t,e)})}var o,c,r=bS(l,d,n.onValidHex,n.onInvalidHex),i=yS(l,d),s={paletteRgba:nn(ye(rv()))},u=bm(i.sketch({})),f=bm(r.sketch({}));return{uid:n.uid,dom:n.dom,components:[u.asSpec(),fS(l,d),f.asSpec()],behaviours:ya([Jd("colour-picker-events",[mo(Fw(),(c=[e],function(n,t){var e=t.event().value(),o=function(n){var t,e=0,o=0,r=n.red()/255,i=n.green()/255,u=n.blue()/255,a=Math.min(r,Math.min(i,u)),c=Math.max(r,Math.max(i,u));return a===c?fx(0,0,100*(o=a)):(e=60*((e=r===a?3:u===a?1:5)-(r===a?i-u:u===a?r-i:u-r)/(c-a)),t=(c-a)/c,o=c,fx(Math.round(e),Math.round(100*t),Math.round(100*o)))}(s.paletteRgba().get()),r=fx(o.hue(),e.x(),100-e.y()),i=Yh(r),u=Wh(i);a(n,u,c)})),mo(Mw(),(o=[t,e],function(n,t){var e=function(n){var t=fx((100-n)/100*360,100,100),e=Yh(t);return Wh(e)}(t.event().value().y());a(n,e,o)}))]),nd.config({find:function(n){return f.getOpt(n)}}),dg.config({mode:"acyclic"})])}}})},wS=function(){return nd.config({find:on.some})},SS=function(n){return nd.config({find:n.getOpt})},CS=function(n){return nd.config({find:function(t){return Ao(t.element(),n).bind(function(n){return t.getSystem().getByDom(n).toOption()})}})},kS={"colorcustom.rgb.red.label":"R","colorcustom.rgb.red.description":"Red component","colorcustom.rgb.green.label":"G","colorcustom.rgb.green.description":"Green component","colorcustom.rgb.blue.label":"B","colorcustom.rgb.blue.description":"Blue component","colorcustom.rgb.hex.label":"#","colorcustom.rgb.hex.description":"Hex color code","colorcustom.rgb.range":"Range 0 to 255","colorcustom.sb.saturation":"Saturation","colorcustom.sb.brightness":"Brightness","colorcustom.sb.picker":"Saturation and Brightness Picker","colorcustom.sb.palette":"Saturation and Brightness Palette","colorcustom.sb.instructions":"Use arrow keys to select saturation and brightness, on x and y axes","colorcustom.hue.hue":"Hue","colorcustom.hue.slider":"Hue Slider","colorcustom.hue.palette":"Hue Palette","colorcustom.hue.instructions":"Use arrow keys to select a hue","aria.color.picker":"Color Picker","aria.input.invalid":"Invalid input"},OS=tinymce.util.Tools.resolve("tinymce.Resource"),ES=re([St("preprocess",l),St("postprocess",l)]),TS=function(r,n){var i=ot("RepresentingConfigs.memento processors",ES,n);return nl.config({store:{mode:"manual",getValue:function(n){var t=r.get(n),e=nl.getValue(t);return i.postprocess(e)},setValue:function(n,t){var e=i.preprocess(t),o=r.get(n);nl.setValue(o,e)}}})},BS=vw,DS=function(n){return bw(n,No,Ho)},AS=function(n){return nl.config({store:{mode:"memory",initialValue:n}})},_S=Yo("alloy-fake-before-tabstop"),MS=Yo("alloy-fake-after-tabstop"),FS=function(n){return db(n,["."+_S,"."+MS].join(","),nn(!1))},IS=function(n,t){var e=t.element();gr(e,_S)?ww(n,!0):gr(e,MS)&&ww(n,!1)},RS=function(n){return{dom:{tag:"div",classes:["tox-navobj"]},components:[xw([_S]),n,xw([MS])],behaviours:ya([CS(1)])}},VS=!(Ht().browser.isIE()||Ht().browser.isEdge());function NS(n,t){return zS(v.document.createElement("canvas"),n,t)}function HS(n){var t=NS(n.width,n.height);return PS(t).drawImage(n,0,0),t}function PS(n){return n.getContext("2d")}function zS(n,t,e){return n.width=t,n.height=e,n}function LS(n){return n.naturalWidth||n.width}function jS(n){return n.naturalHeight||n.height}var US,WS,GS=window.Promise?window.Promise:(US=XS.immediateFn||"function"==typeof window.setImmediate&&window.setImmediate||function(n){v.setTimeout(n,1)},WS=Array.isArray||function(n){return"[object Array]"===Object.prototype.toString.call(n)},XS.prototype["catch"]=function(n){return this.then(null,n)},XS.prototype.then=function(e,o){var r=this;return new XS(function(n,t){qS.call(r,new QS(e,o,n,t))})},XS.all=function(){for(var n=[],t=0;t<arguments.length;t++)n[t]=arguments[t];var c=Array.prototype.slice.call(1===n.length&&WS(n[0])?n[0]:n);return new XS(function(r,i){if(0===c.length)return r([]);var u=c.length;function a(t,n){try{if(n&&("object"==typeof n||"function"==typeof n)){var e=n.then;if("function"==typeof e)return void e.call(n,function(n){a(t,n)},i)}c[t]=n,0==--u&&r(c)}catch(o){i(o)}}for(var n=0;n<c.length;n++)a(n,c[n])})},XS.resolve=function(t){return t&&"object"==typeof t&&t.constructor===XS?t:new XS(function(n){n(t)})},XS.reject=function(e){return new XS(function(n,t){t(e)})},XS.race=function(r){return new XS(function(n,t){for(var e=0,o=r;e<o.length;e++)o[e].then(n,t)})},XS);function XS(n){if("object"!=typeof this)throw new TypeError("Promises must be constructed via new");if("function"!=typeof n)throw new TypeError("not a function");this._state=null,this._value=null,this._deferreds=[],ZS(n,YS(KS,this),YS(JS,this))}function YS(n,t){return function(){return n.apply(t,arguments)}}function qS(o){var r=this;null!==this._state?US(function(){var n=r._state?o.onFulfilled:o.onRejected;if(null!==n){var t;try{t=n(r._value)}catch(e){return void o.reject(e)}o.resolve(t)}else(r._state?o.resolve:o.reject)(r._value)}):this._deferreds.push(o)}function KS(n){try{if(n===this)throw new TypeError("A promise cannot be resolved with itself.");if(n&&("object"==typeof n||"function"==typeof n)){var t=n.then;if("function"==typeof t)return void ZS(YS(t,n),YS(KS,this),YS(JS,this))}this._state=!0,this._value=n,$S.call(this)}catch(e){JS.call(this,e)}}function JS(n){this._state=!1,this._value=n,$S.call(this)}function $S(){for(var n=0,t=this._deferreds;n<t.length;n++){var e=t[n];qS.call(this,e)}this._deferreds=[]}function QS(n,t,e,o){this.onFulfilled="function"==typeof n?n:null,this.onRejected="function"==typeof t?t:null,this.resolve=e,this.reject=o}function ZS(n,t,e){var o=!1;try{n(function(n){o||(o=!0,t(n))},function(n){o||(o=!0,e(n))})}catch(r){if(o)return;o=!0,e(r)}}function nC(e){return new GS(function(n,t){(function p(n){var t=n.split(","),e=/data:([^;]+)/.exec(t[0]);if(!e)return on.none();for(var o=e[1],r=t[1],i=v.atob(r),u=i.length,a=Math.ceil(u/1024),c=new Array(a),s=0;s<a;++s){for(var f=1024*s,l=Math.min(1024+f,u),d=new Array(l-f),m=f,g=0;m<l;++g,++m)d[g]=i[m].charCodeAt(0);c[s]=new Uint8Array(d)}return on.some(new v.Blob(c,{type:o}))})(e).fold(function(){t("uri is not base64: "+e)},n)})}function tC(n,o,r){return o=o||"image/png",v.HTMLCanvasElement.prototype.toBlob?new GS(function(t,e){n.toBlob(function(n){n?t(n):e()},o,r)}):nC(n.toDataURL(o,r))}function eC(n){return function t(a){return new GS(function(n,t){var e=v.URL.createObjectURL(a),o=new v.Image,r=function(){o.removeEventListener("load",i),o.removeEventListener("error",u)};function i(){r(),n(o)}function u(){r(),t("Unable to load data of type "+a.type+": "+e)}o.addEventListener("load",i),o.addEventListener("error",u),o.src=e,o.complete&&i()})}(n).then(function(n){!function e(n){v.URL.revokeObjectURL(n.src)}(n);var t=NS(LS(n),jS(n));return PS(t).drawImage(n,0,0),t})}function oC(n,t,e){var o=t.type;function r(t,e){return n.then(function(n){return function o(n,t,e){return t=t||"image/png",n.toDataURL(t,e)}(n,t,e)})}return{getType:nn(o),toBlob:function i(){return GS.resolve(t)},toDataURL:function u(){return e},toBase64:function a(){return e.split(",")[1]},toAdjustedBlob:function c(t,e){return n.then(function(n){return tC(n,t,e)})},toAdjustedDataURL:r,toAdjustedBase64:function s(n,t){return r(n,t).then(function(n){return n.split(",")[1]})},toCanvas:function f(){return n.then(HS)}}}function rC(t){return function n(e){return new GS(function(n){var t=new v.FileReader;t.onloadend=function(){n(t.result)},t.readAsDataURL(e)})}(t).then(function(n){return oC(eC(t),t,n)})}function iC(t,n){return tC(t,n).then(function(n){return oC(GS.resolve(t),n,t.toDataURL())})}function uC(n,t,e){var o="string"==typeof n?parseFloat(n):n;return e<o?o=e:o<t&&(o=t),o}var aC=[0,.01,.02,.04,.05,.06,.07,.08,.1,.11,.12,.14,.15,.16,.17,.18,.2,.21,.22,.24,.25,.27,.28,.3,.32,.34,.36,.38,.4,.42,.44,.46,.48,.5,.53,.56,.59,.62,.65,.68,.71,.74,.77,.8,.83,.86,.89,.92,.95,.98,1,1.06,1.12,1.18,1.24,1.3,1.36,1.42,1.48,1.54,1.6,1.66,1.72,1.78,1.84,1.9,1.96,2,2.12,2.25,2.37,2.5,2.62,2.75,2.87,3,3.2,3.4,3.6,3.8,4,4.3,4.7,4.9,5,5.5,6,6.5,6.8,7,7.3,7.5,7.8,8,8.4,8.7,9,9.4,9.6,9.8,10];function cC(n,t){for(var e,o=[],r=new Array(25),i=0;i<5;i++){for(var u=0;u<5;u++)o[u]=t[u+5*i];for(u=0;u<5;u++){for(var a=e=0;a<5;a++)e+=n[u+5*a]*o[a];r[u+5*i]=e}}return r}function sC(t,e){return t.toCanvas().then(function(n){return function i(n,t,e){var o=PS(n);var r=function B(n,t){for(var e,o,r,i,u=n.data,a=t[0],c=t[1],s=t[2],f=t[3],l=t[4],d=t[5],m=t[6],g=t[7],p=t[8],h=t[9],v=t[10],b=t[11],y=t[12],x=t[13],w=t[14],S=t[15],C=t[16],k=t[17],O=t[18],E=t[19],T=0;T<u.length;T+=4)e=u[T],o=u[T+1],r=u[T+2],i=u[T+3],u[T]=e*a+o*c+r*s+i*f+l,u[T+1]=e*d+o*m+r*g+i*p+h,u[T+2]=e*v+o*b+r*y+i*x+w,u[T+3]=e*S+o*C+r*k+i*O+E;return n}(o.getImageData(0,0,n.width,n.height),e);return o.putImageData(r,0,0),iC(n,t)}(n,t.getType(),e)})}function fC(t,e){return t.toCanvas().then(function(n){return function u(n,t,e){var o=PS(n);var r=o.getImageData(0,0,n.width,n.height),i=o.getImageData(0,0,n.width,n.height);return i=function w(n,t,e){function o(n,t,e){return e<n?n=e:n<t&&(n=t),n}for(var r=Math.round(Math.sqrt(e.length)),i=Math.floor(r/2),u=n.data,a=t.data,c=n.width,s=n.height,f=0;f<s;f++)for(var l=0;l<c;l++){for(var d=0,m=0,g=0,p=0;p<r;p++)for(var h=0;h<r;h++){var v=o(l+h-i,0,c-1),b=4*(o(f+p-i,0,s-1)*c+v),y=e[p*r+h];d+=u[b]*y,m+=u[1+b]*y,g+=u[2+b]*y}var x=4*(f*c+l);a[x]=o(d,0,255),a[1+x]=o(m,0,255),a[2+x]=o(g,0,255)}return t}(r,i,e),o.putImageData(i,0,0),iC(n,t)}(n,t.getType(),e)})}function lC(e){return function(n,t){return sC(n,e([1,0,0,0,0,0,1,0,0,0,0,0,1,0,0,0,0,0,1,0,0,0,0,0,1],t))}}function dC(n,t,e,o){return sC(n,function r(n,t,e,o){return cC(n,[t=uC(t,0,2),0,0,0,0,0,e=uC(e,0,2),0,0,0,0,0,o=uC(o,0,2),0,0,0,0,0,1,0,0,0,0,0,1])}([1,0,0,0,0,0,1,0,0,0,0,0,1,0,0,0,0,0,1,0,0,0,0,0,1],t,e,o))}var mC=function sI(t){return function(n){return sC(n,t)}}([-1,0,0,0,255,0,-1,0,0,255,0,0,-1,0,255,0,0,0,1,0,0,0,0,0,1]),gC=lC(function fI(n,t){return cC(n,[1,0,0,0,t=uC(255*t,-255,255),0,1,0,0,t,0,0,1,0,t,0,0,0,1,0,0,0,0,0,1])}),pC=lC(function lI(n,t){var e;return t=uC(t,-1,1),cC(n,[(e=(t*=100)<0?127+t/100*127:127*(e=0===(e=t%1)?aC[t]:aC[Math.floor(t)]*(1-e)+aC[Math.floor(t)+1]*e)+127)/127,0,0,0,.5*(127-e),0,e/127,0,0,.5*(127-e),0,0,e/127,0,.5*(127-e),0,0,0,1,0,0,0,0,0,1])}),hC=function dI(t){return function(n){return fC(n,t)}}([0,-1,0,-1,5,-1,0,-1,0]),vC=function mI(c){return function(t,e){return t.toCanvas().then(function(n){return function(n,t,e){var o=PS(n),r=new Array(256);for(var i=0;i<r.length;i++)r[i]=c(i,e);var u=function a(n,t){for(var e=n.data,o=0;o<e.length;o+=4)e[o]=t[e[o]],e[o+1]=t[e[o+1]],e[o+2]=t[e[o+2]];return n}(o.getImageData(0,0,n.width,n.height),r);return o.putImageData(u,0,0),iC(n,t)}(n,t.getType(),e)})}}(function(n,t){return 255*Math.pow(n/255,1-t)});function bC(n,t,e){var o=LS(n),r=jS(n),i=t/o,u=e/r,a=!1;(i<.5||2<i)&&(i=i<.5?.5:2,a=!0),(u<.5||2<u)&&(u=u<.5?.5:2,a=!0);var c=function s(u,a,c){return new GS(function(n){var t=LS(u),e=jS(u),o=Math.floor(t*a),r=Math.floor(e*c),i=NS(o,r);PS(i).drawImage(u,0,0,t,e,0,0,o,r),n(i)})}(n,i,u);return a?c.then(function(n){return bC(n,t,e)}):c}function yC(t,e){return t.toCanvas().then(function(n){return function a(n,t,e){var o=NS(n.width,n.height),r=PS(o),i=0,u=0;90!==(e=e<0?360+e:e)&&270!==e||zS(o,o.height,o.width);90!==e&&180!==e||(i=o.width);270!==e&&180!==e||(u=o.height);return r.translate(i,u),r.rotate(e*Math.PI/180),r.drawImage(n,0,0),iC(o,t)}(n,t.getType(),e)})}function xC(t,e){return t.toCanvas().then(function(n){return function i(n,t,e){var o=NS(n.width,n.height),r=PS(o);"v"===e?(r.scale(1,-1),r.drawImage(n,0,-o.height)):(r.scale(-1,1),r.drawImage(n,-o.width,0));return iC(o,t)}(n,t.getType(),e)})}function wC(t,e,o,r,i){return t.toCanvas().then(function(n){return function a(n,t,e,o,r,i){var u=NS(r,i);return PS(u).drawImage(n,-e,-o),iC(u,t)}(n,t.getType(),e,o,r,i)})}function SC(n){return mC(n)}function CC(n){return hC(n)}function kC(n,t){return vC(n,t)}function OC(n,t){return gC(n,t)}function EC(n,t){return pC(n,t)}function TC(n,t){return xC(n,t)}function BC(n,t,e){return function r(t,e,o){return t.toCanvas().then(function(n){return bC(n,e,o).then(function(n){return iC(n,t.getType())})})}(n,t,e)}function DC(n,t){return yC(n,t)}function AC(n,t){return N({dom:{tag:"span",innerHtml:n,classes:["tox-icon","tox-tbtn__icon-wrap"]}},t)}function _C(n,t){return AC(xm(n,t),{})}function MC(n,t){return AC(xm(n,t),{behaviours:ya([gg.config({})])})}function FC(n,t,e){return{dom:{tag:"span",innerHtml:e.translate(n),classes:[t+"__select-label"]},behaviours:ya([gg.config({})])}}function IC(n,t,o){function e(n,t){var e=nl.getValue(n);return bg.focus(e),io(e,"keydown",{raw:t.event().raw()}),Ew.close(e),on.some(!0)}var r=ye(Z),i=n.text.map(function(n){return bm(FC(n,t,o.providers))}),u=n.icon.map(function(n){return bm(MC(n,o.providers.icons))}),a=n.role.fold(function(){return{}},function(n){return{role:n}}),c=n.tooltip.fold(function(){return{}},function(n){var t=o.providers.translate(n);return{title:t,"aria-label":t}});return bm(Ew.sketch(N(N({},a),{dom:{tag:"button",classes:[t,t+"--select"].concat(S(n.classes,function(n){return t+"--"+n})),attributes:N({},c)},components:Dh([u.map(function(n){return n.asSpec()}),i.map(function(n){return n.asSpec()}),on.some({dom:{tag:"div",classes:[t+"__select-chevron"],innerHtml:xm("chevron-down",o.providers.icons)}})]),matchWidth:!0,useMinWidth:!0,dropdownBehaviours:ya(g(n.dropdownBehaviours,[Eh(n.disabled),Tw.config({}),gg.config({}),Jd("dropdown-events",[Tp(n,r),Bp(n,r)]),Jd("menubutton-update-display-text",[mo(ik,function(t,e){i.bind(function(n){return n.getOpt(t)}).each(function(n){gg.set(n,[Rr(o.providers.translate(e.event().text()))])})}),mo(uk,function(t,e){u.bind(function(n){return n.getOpt(t)}).each(function(n){gg.set(n,[MC(e.event().icon(),o.providers.icons)])})})])])),eventOrder:Dn(rk,{mousedown:["focusing","alloy.base.behaviour","item-type-events","normal-dropdown-events"]}),sandboxBehaviours:ya([dg.config({mode:"special",onLeft:e,onRight:e})]),lazySink:o.getSink,toggleClass:t+"--active",parts:{menu:Cv(0,n.columns,n.presets)},fetch:function(){return Ny(n.fetch)}}))).asSpec()}function RC(n){return"separator"===n.type}function VC(n,e){var t=O(n,function(n,t){return function(n){return cn(n)}(t)?""===t?n:"|"===t?0<n.length&&!RC(n[n.length-1])?n.concat([ak]):n:En(e,t.toLowerCase())?n.concat([e[t.toLowerCase()]]):n:n.concat([t])},[]);return 0<t.length&&RC(t[t.length-1])&&t.pop(),t}function NC(n,t){return function(n){return En(n,"getSubmenuItems")}(n)?function(n,t){var e=n.getSubmenuItems(),o=ck(e,t);return{item:n,menus:Dn(o.menus,q(n.value,o.items)),expansions:Dn(o.expansions,q(n.value,n.value))}}(n,t):{item:n,menus:{},expansions:{}}}function HC(n,e,o,t){var r=Yo("primary-menu"),i=ck(n,o.shared.providers.menuItems());if(0===i.items.length)return on.none();var u=ub(r,i.items,e,o,t),a=P(i.menus,function(n,t){return ub(t,n,e,o,!1)}),c=Dn(a,q(r,u));return on.from(jg.tieredData(r,c,i.expansions))}function PC(e){return{isDisabled:function(){return kh.isDisabled(e)},setDisabled:function(n){return kh.set(e,n)},setActive:function(n){var t=e.element();n?(lr(t,"tox-tbtn--enabled"),zo(t,"aria-pressed",!0)):(mr(t,"tox-tbtn--enabled"),Uo(t,"aria-pressed"))},isActive:function(){return gr(e.element(),"tox-tbtn--enabled")}}}function zC(n,t,e,o){return IC({text:n.text,icon:n.icon,tooltip:n.tooltip,role:o,fetch:function(t){n.fetch(function(n){t(HC(n,Ih.CLOSE_ON_EXECUTE,e,!1))})},onSetup:n.onSetup,getApi:PC,columns:1,presets:"normal",classes:[],dropdownBehaviours:[Xy.config({})]},t,e.shared)}function LC(t,o,r){return function(n){n(S(t,function(n){var t=n.text.fold(function(){return{}},function(n){return{text:n}});return N(N({type:n.type},t),{onAction:function(e){return function(n){var t=!n.isActive();n.setActive(t),e.storage.set(t),r.shared.getSink().each(function(n){o().getOpt(n).each(function(n){Sa(n.element()),io(n,fy,{name:e.name,value:e.storage.get()})})})}}(n),onSetup:function(t){return function(n){n.setActive(t.storage.get())}}(n)})}))}}function jC(n,t,e,o,r){void 0===e&&(e=[]);var i=t.fold(function(){return{}},function(n){return{action:n}}),u=N({buttonBehaviours:ya([Eh(n.disabled),Xy.config({}),Jd("button press",[lo("click"),lo("mousedown")])].concat(e)),eventOrder:{click:["button press","alloy.base.behaviour"],mousedown:["button press","alloy.base.behaviour"]}},i),a=Dn(u,{dom:o});return Dn(a,{components:r})}function UC(n,t,e,o){void 0===o&&(o=[]);var r={tag:"button",classes:["tox-tbtn"],attributes:n.tooltip.map(function(n){return{"aria-label":e.translate(n),title:e.translate(n)}}).getOr({})},i=n.icon.map(function(n){return _C(n,e.icons)}),u=Dh([i]);return jC(n,t,o,r,u)}function WC(n,t,e,o){void 0===o&&(o=[]);var r=UC(n,on.some(t),e,o);return Xg.sketch(r)}function GC(n,t,e,o,r){void 0===o&&(o=[]),void 0===r&&(r=[]);var i=e.translate(n.text),u=n.icon?n.icon.map(function(n){return _C(n,e.icons)}):on.none(),a=u.isSome()?Dh([u]):[],c=u.isSome()?{}:{innerHtml:i},s=g(n.primary||n.borderless?["tox-button"]:["tox-button","tox-button--secondary"],u.isSome()?["tox-button--icon"]:[],n.borderless?["tox-button--naked"]:[],r),f=N(N({tag:"button",classes:s},c),{attributes:{title:i}});return jC(n,t,o,f,a)}function XC(n,t,e,o,r){void 0===o&&(o=[]),void 0===r&&(r=[]);var i=GC(n,on.some(t),e,o,r);return Xg.sketch(i)}function YC(t,e){return function(n){"custom"===e?io(n,fy,{name:t,value:{}}):"submit"===e?ro(n,ly):"cancel"===e?ro(n,sy):v.console.error("Unknown button type: ",e)}}function qC(n,t,e){if(function(n,t){return"menu"===t}(0,t)){var o=n,r=N(N({},n),{fetch:LC(o.items,function(){return i},e)}),i=bm(zC(r,"tox-tbtn",e,on.none()));return i.asSpec()}if(function(n,t){return"custom"===t||"cancel"===t||"submit"===t}(0,t)){var u=YC(n.name,t),a=N(N({},n),{borderless:!1});return XC(a,u,e.shared.providers,[])}v.console.error("Unknown footer button type: ",t)}function KC(n,t){var e=YC(n.name,"custom");return function(n,t){return Dy(n,t,[],[])}(on.none(),by.parts().field(N({factory:Xg},GC(n,on.some(e),t,[AS(""),wS()]))))}function JC(n,t){return wl({factory:by,name:n,overrides:function(o){return{fieldBehaviours:ya([Jd("coupled-input-behaviour",[mo(oi(),function(e){(function(n,t,e){return Ks(n,t,e).bind(nd.getCurrent)})(e,o,t).each(function(t){Ks(e,o,"lock").each(function(n){kg.isOn(n)&&o.onLockedChange(e,t,n)})})})])])}}})}function $C(n){var t=/^\s*(\d+(?:\.\d+)?)\s*(|cm|mm|in|px|pt|pc|em|ex|ch|rem|vw|vh|vmin|vmax|%)\s*$/.exec(n);if(null===t)return an.error(n);var e=parseFloat(t[1]),o=t[2];return an.value({value:e,unit:o})}function QC(n,t){function e(n){return Object.prototype.hasOwnProperty.call(o,n)}var o={"":96,px:96,pt:72,cm:2.54,pc:12,mm:25.4,"in":1};return n.unit===t?on.some(n.value):e(n.unit)&&e(t)?o[n.unit]===o[t]?on.some(n.value):on.some(n.value/o[n.unit]*o[t]):on.none()}function ZC(n){return on.none()}function nk(n,t){return function(n,t,e){return n.isSome()&&t.isSome()?on.some(e(n.getOrDie(),t.getOrDie())):on.none()}($C(n).toOption(),$C(t).toOption(),function(n,t){return QC(n,t.unit).map(function(n){return t.value/n}).map(function(n){return function(t,e){return function(n){return QC(n,e).map(function(n){return{value:n*t,unit:e}})}}(n,t.unit)}).getOr(ZC)}).getOr(ZC)}function tk(o,t){function n(n){return{dom:{tag:"div",classes:["tox-form__group"]},components:n}}function e(e){return by.parts().field({factory:xy,inputClasses:["tox-textfield"],inputBehaviours:ya([kh.config({disabled:o.disabled}),Xy.config({}),Jd("size-input-events",[mo(Zr(),function(n,t){io(n,i,{isField1:e})}),mo(ri(),function(n,t){io(n,ay,{name:o.name})})])]),selectOnFocus:!1})}function r(n){return{dom:{tag:"label",classes:["tox-label"],innerHtml:t.translate(n)}}}var a=ZC,i=Yo("ratio-event"),u=lk.parts().lock({dom:{tag:"button",classes:["tox-lock","tox-button","tox-button--naked","tox-button--icon"],attributes:{title:t.translate(o.label.getOr("Constrain proportions"))}},components:[{dom:{tag:"span",classes:["tox-icon","tox-lock-icon__lock"],innerHtml:xm("lock",t.icons)}},{dom:{tag:"span",classes:["tox-icon","tox-lock-icon__unlock"],innerHtml:xm("unlock",t.icons)}}],buttonBehaviours:ya([Eh(o.disabled),Xy.config({})])}),c=lk.parts().field1(n([by.parts().label(r("Width")),e(!0)])),s=lk.parts().field2(n([by.parts().label(r("Height")),e(!1)]));return lk.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:[{dom:{tag:"div",classes:["tox-form__controls-h-stack"]},components:[c,s,n([r("&nbsp;"),u])]}],field1Name:"width",field2Name:"height",locked:!0,markers:{lockClass:"tox-locked"},onLockedChange:function(n,t,e){$C(nl.getValue(n)).each(function(n){a(n).each(function(n){nl.setValue(t,function(n){var t,e={"":0,px:0,pt:1,mm:1,pc:2,ex:2,em:2,ch:2,rem:2,cm:3,"in":4,"%":4},o=n.value.toFixed((t=n.unit)in e?e[t]:1);return-1!==o.indexOf(".")&&(o=o.replace(/\.?0*$/,"")),o+n.unit}(n))})})},coupledFieldBehaviours:ya([kh.config({disabled:o.disabled,onDisabled:function(n){lk.getField1(n).bind(by.getField).each(kh.disable),lk.getField2(n).bind(by.getField).each(kh.disable),lk.getLock(n).each(kh.disable)},onEnabled:function(n){lk.getField1(n).bind(by.getField).each(kh.enable),lk.getField2(n).bind(by.getField).each(kh.enable),lk.getLock(n).each(kh.enable)}}),Jd("size-input-events2",[mo(i,function(n,t){var e=t.event().isField1(),o=e?lk.getField1(n):lk.getField2(n),r=e?lk.getField2(n):lk.getField1(n),i=o.map(nl.getValue).getOr(""),u=r.map(nl.getValue).getOr("");a=nk(i,u)})])])})}function ek(r,c){function n(n,t,e,o){return bm(XC({name:n,text:n,disabled:e,primary:o,icon:on.none(),borderless:!1},t,c))}function t(n,t,e,o){return bm(WC({name:n,icon:on.some(n),tooltip:on.some(t),disabled:o,primary:!1,borderless:!1},e,c))}function u(n,e){n.map(function(n){var t=n.get(e);t.hasConfigured(kh)&&kh.disable(t)})}function a(n,e){n.map(function(n){var t=n.get(e);t.hasConfigured(kh)&&kh.enable(t)})}function i(n,t,e){io(n,t,e)}function e(n){return ro(n,hk.disable())}function o(n){return ro(n,hk.enable())}function s(n,t){e(n),i(n,dk.transform(),{transform:t}),o(n)}function f(n){return function(){Q.getOpt(n).each(function(n){gg.set(n,[J])})}}function l(n,t){e(n),i(n,dk.transformApply(),{transform:t,swap:f(n)}),o(n)}function d(){return n("Back",function(n){return i(n,dk.back(),{swap:f(n)})},!1,!1)}function m(){return bm({dom:{tag:"div",classes:["tox-spacer"]},behaviours:ya([kh.config({})])})}function g(){return n("Apply",function(n){return i(n,dk.apply(),{swap:f(n)})},!0,!0)}function p(){return function(n){var t=r.getRect();return function(n,t,e,o,r){return wC(n,t,e,o,r)}(n,t.x,t.y,t.w,t.h)}}function h(t,e){return function(n){return t(n,e)}}function v(n,t){!function(n,t){e(n),i(n,dk.tempTransform(),{transform:t}),o(n)}(n,t)}function b(n,t,e,o,r){var i=sS.parts().label({dom:{tag:"label",classes:["tox-label"],innerHtml:c.translate(n)}}),u=sS.parts().spectrum({dom:{tag:"div",classes:["tox-slider__rail"],attributes:{role:"presentation"}}}),a=sS.parts().thumb({dom:{tag:"div",classes:["tox-slider__handle"],attributes:{role:"presentation"}}});return bm(sS.sketch({dom:{tag:"div",classes:["tox-slider"],attributes:{role:"presentation"}},model:{mode:"x",minX:e,maxX:r,getInitialValue:nn({x:nn(o)})},components:[i,u,a],sliderBehaviours:ya([bg.config({})]),onChoose:t}))}function y(n,t,e,o,r){return[d(),function(n,r,t,e,o){return b(n,function(n,t,e){var o=h(r,e.x()/100);s(n,o)},t,e,o)}(n,t,e,o,r),g()]}function x(n,t,e,o,r){var i=y(n,t,e,o,r);return Gb.sketch({dom:k,components:i.map(function(n){return n.asSpec()}),containerBehaviours:ya([Jd("image-tools-filter-panel-buttons-events",[mo(hk.disable(),function(n,t){u(i,n)}),mo(hk.enable(),function(n,t){a(i,n)})])])})}function w(t,e,o){return function(n){return function(n,t,e,o){return dC(n,t,e,o)}(n,t,e,o)}}function S(n){return b(n,function(a,n,t){var e=j.getOpt(a),o=W.getOpt(a),r=U.getOpt(a);e.each(function(u){o.each(function(i){r.each(function(n){var t=nl.getValue(u).x()/100,e=nl.getValue(n).x()/100,o=nl.getValue(i).x()/100,r=w(t,e,o);s(a,r)})})})},0,100,200)}function C(t,e,o){return function(n){i(n,dk.swap(),{transform:e,swap:function(){Q.getOpt(n).each(function(n){gg.set(n,[t]),o(n)})}})}}var k={tag:"div",classes:["tox-image-tools__toolbar","tox-image-tools-edit-panel"]},O=Z,E=[d(),m(),n("Apply",function(n){var t=p();l(n,t),r.hideCrop()},!1,!0)],T=Gb.sketch({dom:k,components:E.map(function(n){return n.asSpec()}),containerBehaviours:ya([Jd("image-tools-crop-buttons-events",[mo(hk.disable(),function(n,t){u(E,n)}),mo(hk.enable(),function(n,t){a(E,n)})])])}),B=bm(tk({name:"size",label:on.none(),constrain:!0,disabled:!1},c)),D=[d(),m(),B,m(),n("Apply",function(o){B.getOpt(o).each(function(n){var t=nl.getValue(n),e=function(t,e){return function(n){return BC(n,t,e)}}(parseInt(t.width,10),parseInt(t.height,10));l(o,e)})},!1,!0)],A=Gb.sketch({dom:k,components:D.map(function(n){return n.asSpec()}),containerBehaviours:ya([Jd("image-tools-resize-buttons-events",[mo(hk.disable(),function(n,t){u(D,n)}),mo(hk.enable(),function(n,t){a(D,n)})])])}),_=h(TC,"h"),M=h(TC,"v"),F=h(DC,-90),I=h(DC,90),R=[d(),m(),t("flip-horizontally","Flip horizontally",function(n){v(n,_)},!1),t("flip-vertically","Flip vertically",function(n){v(n,M)},!1),t("rotate-left","Rotate counterclockwise",function(n){v(n,F)},!1),t("rotate-right","Rotate clockwise",function(n){v(n,I)},!1),m(),g()],V=Gb.sketch({dom:k,components:R.map(function(n){return n.asSpec()}),containerBehaviours:ya([Jd("image-tools-fliprotate-buttons-events",[mo(hk.disable(),function(n,t){u(R,n)}),mo(hk.enable(),function(n,t){a(R,n)})])])}),N=[d(),m(),g()],H=Gb.sketch({dom:k,components:N.map(function(n){return n.asSpec()})}),P=x("Brightness",OC,-100,0,100),z=x("Contrast",EC,-100,0,100),L=x("Gamma",kC,-100,0,100),j=S("R"),U=S("G"),W=S("B"),G=[d(),j,U,W,g()],X=Gb.sketch({dom:k,components:G.map(function(n){return n.asSpec()})}),Y=on.some(CC),q=on.some(SC),K=[t("crop","Crop",C(T,on.none(),function(n){r.showCrop()}),!1),t("resize","Resize",C(A,on.none(),function(n){B.getOpt(n).each(function(n){var t=r.getMeasurements(),e=t.width,o=t.height;nl.setValue(n,{width:e,height:o})})}),!1),t("orientation","Orientation",C(V,on.none(),O),!1),t("brightness","Brightness",C(P,on.none(),O),!1),t("sharpen","Sharpen",C(H,Y,O),!1),t("contrast","Contrast",C(z,on.none(),O),!1),t("color-levels","Color levels",C(X,on.none(),O),!1),t("gamma","Gamma",C(L,on.none(),O),!1),t("invert","Invert",C(H,q,O),!1)],J=Gb.sketch({dom:k,components:K.map(function(n){return n.asSpec()})}),$=Gb.sketch({dom:{tag:"div"},components:[J],containerBehaviours:ya([gg.config({})])}),Q=bm($);return{memContainer:Q,getApplyButton:function(n){return Q.getOpt(n).map(function(n){var t=n.components()[0];return t.components()[t.components().length-1]})}}}var ok=Yo("toolbar.button.execute"),rk={"alloy.execute":["disabling","alloy.base.behaviour","toggling","toolbar-button-events"]},ik=Yo("update-menu-text"),uk=Yo("update-menu-icon"),ak={type:"separator"},ck=function(n,r){var t=VC(cn(n)?n.split(" "):n,r);return k(t,function(n,t){var e=function(n){if(RC(n))return n;var t=Nn(n,"value").getOrThunk(function(){return Yo("generated-menu-item")});return Dn({value:t},n)}(t),o=NC(e,r);return{menus:Dn(n.menus,o.menus),items:[o.item].concat(n.items),expansions:Dn(n.expansions,o.expansions)}},{menus:{},expansions:{},items:[]})},sk=nn([St("field1Name","field1"),St("field2Name","field2"),$u("onLockedChange"),Yu(["lockClass"]),St("locked",!1),tl("coupledFieldBehaviours",[nd,nl])]),fk=nn([JC("field1","field2"),JC("field2","field1"),wl({factory:Xg,schema:[ct("dom")],name:"lock",overrides:function(n){return{buttonBehaviours:ya([kg.config({selected:n.locked,toggleClass:n.markers.lockClass,aria:{mode:"pressed"}})])}}})]),lk=_l({name:"FormCoupledInputs",configFields:sk(),partFields:fk(),factory:function(o,n,t,e){return{uid:o.uid,dom:o.dom,components:n,behaviours:el(o.coupledFieldBehaviours,[nd.config({find:on.some}),nl.config({store:{mode:"manual",getValue:function(n){var t,e=nf(n,o,["field1","field2"]);return(t={})[o.field1Name]=nl.getValue(e.field1()),t[o.field2Name]=nl.getValue(e.field2()),t},setValue:function(n,t){var e=nf(n,o,["field1","field2"]);$(t,o.field1Name)&&nl.setValue(e.field1(),t[o.field1Name]),$(t,o.field2Name)&&nl.setValue(e.field2(),t[o.field2Name])}}})]),apis:{getField1:function(n){return Ks(n,o,"field1")},getField2:function(n){return Ks(n,o,"field2")},getLock:function(n){return Ks(n,o,"lock")}}}},apis:{getField1:function(n,t){return n.getField1(t)},getField2:function(n,t){return n.getField2(t)},getLock:function(n,t){return n.getLock(t)}}}),dk={undo:nn(Yo("undo")),redo:nn(Yo("redo")),zoom:nn(Yo("zoom")),back:nn(Yo("back")),apply:nn(Yo("apply")),swap:nn(Yo("swap")),transform:nn(Yo("transform")),tempTransform:nn(Yo("temp-transform")),transformApply:nn(Yo("transform-apply"))},mk=nn("save-state"),gk=nn("disable"),pk=nn("enable"),hk={formActionEvent:fy,saveState:mk,disable:gk,enable:pk},vk=tinymce.util.Tools.resolve("tinymce.dom.DomQuery"),bk=tinymce.util.Tools.resolve("tinymce.geom.Rect"),yk=tinymce.util.Tools.resolve("tinymce.util.Observable"),xk=tinymce.util.Tools.resolve("tinymce.util.Tools"),wk=tinymce.util.Tools.resolve("tinymce.util.VK");function Sk(n){var t,e;if(n.changedTouches)for(t="screenX screenY pageX pageY clientX clientY".split(" "),e=0;e<t.length;e++)n[t[e]]=n.changedTouches[0][t[e]]}function Ck(n,r){var i,u,t,a,c,f,l,d=r.document||v.document;r=r||{};var m=d.getElementById(r.handle||n);t=function(n){var t,e,o=function s(n){var t,e,o,r,i,u,a,c=Math.max;return t=n.documentElement,e=n.body,o=c(t.scrollWidth,e.scrollWidth),r=c(t.clientWidth,e.clientWidth),i=c(t.offsetWidth,e.offsetWidth),u=c(t.scrollHeight,e.scrollHeight),a=c(t.clientHeight,e.clientHeight),{width:o<i?r:o,height:u<c(t.offsetHeight,e.offsetHeight)?a:u}}(d);Sk(n),n.preventDefault(),u=n.button,t=m,f=n.screenX,l=n.screenY,e=v.window.getComputedStyle?v.window.getComputedStyle(t,null).getPropertyValue("cursor"):t.runtimeStyle.cursor,i=vk("<div></div>").css({position:"absolute",top:0,left:0,width:o.width,height:o.height,zIndex:2147483647,opacity:1e-4,cursor:e}).appendTo(d.body),vk(d).on("mousemove touchmove",c).on("mouseup touchend",a),r.start(n)},c=function(n){if(Sk(n),n.button!==u)return a(n);n.deltaX=n.screenX-f,n.deltaY=n.screenY-l,n.preventDefault(),r.drag(n)},a=function(n){Sk(n),vk(d).off("mousemove touchmove",c).off("mouseup touchend",a),i.remove(),r.stop&&r.stop(n)},this.destroy=function(){vk(m).off()},vk(m).on("mousedown touchstart",t)}function kk(t){function u(n,s){c.getOpt(n).each(function(n){var e=l.get(),o=gu(n.element()),r=fu(n.element()),i=s.dom().naturalWidth*e,u=s.dom().naturalHeight*e,a=Math.max(0,o/2-i/2),c=Math.max(0,r/2-u/2),t={left:a.toString()+"px",top:c.toString()+"px",width:i.toString()+"px",height:u.toString()+"px",position:"absolute"};wr(s,t),f.getOpt(n).each(function(n){wr(n.element(),t)}),d.get().each(function(n){var t=m.get();n.setRect({x:t.x*e+a,y:t.y*e+c,w:t.w*e,h:t.h*e}),n.setClampRect({x:a,y:c,w:i,h:u}),n.setViewPortRect({x:0,y:0,w:o,h:r})})})}function e(n,t){var i=we.fromTag("img");return zo(i,"src",t),function(e){return new Hp(function(n){var t=function(){e.removeEventListener("load",t),n(e)};e.complete?n(e):e.addEventListener("load",t)})}(i.dom()).then(function(){return c.getOpt(n).map(function(n){var t=iu({element:i});gg.replaceAt(n,1,on.some(t));var e=a.get(),o={x:0,y:0,w:i.dom().naturalWidth,h:i.dom().naturalHeight};a.set(o);var r=bk.inflate(o,-20,-20);return m.set(r),e.w===o.w&&e.h===o.h||function(n,u){c.getOpt(n).each(function(n){var t=gu(n.element()),e=fu(n.element()),o=u.dom().naturalWidth,r=u.dom().naturalHeight,i=Math.min(t/o,e/r);1<=i?l.set(1):l.set(i)})}(n,i),u(n,i),i})})}var f=bm({dom:{tag:"div",classes:["tox-image-tools__image-bg"],attributes:{role:"presentation"}}}),l=ye(1),d=ye(on.none()),m=ye({x:0,y:0,w:1,h:1}),a=ye({x:0,y:0,w:1,h:1}),n=Gb.sketch({dom:{tag:"div",classes:["tox-image-tools__image"]},components:[f.asSpec(),{dom:{tag:"img",attributes:{src:t}}},{dom:{tag:"div"},behaviours:ya([Jd("image-panel-crop-events",[Ri(function(n){c.getOpt(n).each(function(n){var t=n.element().dom(),e=Bk({x:10,y:10,w:100,h:100},{x:0,y:0,w:200,h:200},{x:0,y:0,w:200,h:200},t,function(){});e.toggleVisibility(!1),e.on("updateRect",function(n){var t=n.rect,e=l.get(),o={x:Math.round(t.x/e),y:Math.round(t.y/e),w:Math.round(t.w/e),h:Math.round(t.h/e)};m.set(o)}),d.set(on.some(e))})})])])}],containerBehaviours:ya([gg.config({}),Jd("image-panel-events",[Ri(function(n){e(n,t)})])])}),c=bm(n);return{memContainer:c,updateSrc:e,zoom:function(n,t){var e=l.get(),o=0<t?Math.min(2,e+.1):Math.max(.1,e-.1);l.set(o),c.getOpt(n).each(function(n){var t=n.components()[1].element();u(n,t)})},showCrop:function(){d.get().each(function(n){n.toggleVisibility(!0)})},hideCrop:function(){d.get().each(function(n){n.toggleVisibility(!1)})},getRect:function(){return m.get()},getMeasurements:function(){var n=a.get();return{width:n.w,height:n.h}}}}function Ok(n,t,e,o,r){return WC({name:n,icon:on.some(t),disabled:e,tooltip:on.some(n),primary:!1,borderless:!1},o,r)}function Ek(n,t){t?kh.enable(n):kh.disable(n)}var Tk=0,Bk=function(s,e,f,o,r){var l,t,i,u="tox-",a="tox-crid-"+Tk++,c=[{name:"move",xMul:0,yMul:0,deltaX:1,deltaY:1,deltaW:0,deltaH:0,label:"Crop Mask"},{name:"nw",xMul:0,yMul:0,deltaX:1,deltaY:1,deltaW:-1,deltaH:-1,label:"Top Left Crop Handle"},{name:"ne",xMul:1,yMul:0,deltaX:0,deltaY:1,deltaW:1,deltaH:-1,label:"Top Right Crop Handle"},{name:"sw",xMul:0,yMul:1,deltaX:1,deltaY:0,deltaW:-1,deltaH:1,label:"Bottom Left Crop Handle"},{name:"se",xMul:1,yMul:1,deltaX:0,deltaY:0,deltaW:1,deltaH:1,label:"Bottom Right Crop Handle"}];i=["top","right","bottom","left"];var d=function(n,t){return{x:t.x+n.x,y:t.y+n.y,w:t.w,h:t.h}},m=function(n,t){return{x:t.x-n.x,y:t.y-n.y,w:t.w,h:t.h}};function g(n,t,e,o){var r,i,u,a,c;r=t.x,i=t.y,u=t.w,a=t.h,r+=e*n.deltaX,i+=o*n.deltaY,(u+=e*n.deltaW)<20&&(u=20),(a+=o*n.deltaH)<20&&(a=20),c=s=bk.clamp({x:r,y:i,w:u,h:a},f,"move"===n.name),c=m(f,c),l.fire("updateRect",{rect:c}),v(c)}function p(t){function n(n,t){t.h<0&&(t.h=0),t.w<0&&(t.w=0),vk("#"+a+"-"+n,o).css({left:t.x,top:t.y,width:t.w,height:t.h})}xk.each(c,function(n){vk("#"+a+"-"+n.name,o).css({left:t.w*n.xMul+t.x,top:t.h*n.yMul+t.y})}),n("top",{x:e.x,y:e.y,w:e.w,h:t.y-e.y}),n("right",{x:t.x+t.w,y:t.y,w:e.w-t.x-t.w+e.x,h:t.h}),n("bottom",{x:e.x,y:t.y+t.h,w:e.w,h:e.h-t.y-t.h+e.y}),n("left",{x:e.x,y:t.y,w:t.x-e.x,h:t.h}),n("move",t)}function h(n){p(s=n)}function v(n){h(d(f,n))}return function b(){vk('<div id="'+a+'" class="'+u+'croprect-container" role="grid" aria-dropeffect="execute">').appendTo(o),xk.each(i,function(n){vk("#"+a,o).append('<div id="'+a+"-"+n+'"class="'+u+'croprect-block" style="display: none" data-mce-bogus="all">')}),xk.each(c,function(n){vk("#"+a,o).append('<div id="'+a+"-"+n.name+'" class="'+u+"croprect-handle "+u+"croprect-handle-"+n.name+'"style="display: none" data-mce-bogus="all" role="gridcell" tabindex="-1" aria-label="'+n.label+'" aria-grabbed="false" title="'+n.label+'">')}),t=xk.map(c,function n(t){var e;return new Ck(a,{document:o.ownerDocument,handle:a+"-"+t.name,start:function(){e=s},drag:function(n){g(t,e,n.deltaX,n.deltaY)}})}),p(s),vk(o).on("focusin focusout",function(n){vk(n.target).attr("aria-grabbed","focus"===n.type?"true":"false")}),vk(o).on("keydown",function(t){var i;function n(n,t,e,o,r){n.stopPropagation(),n.preventDefault(),g(i,e,o,r)}switch(xk.each(c,function(n){if(t.target.id===a+"-"+n.name)return i=n,!1}),t.keyCode){case wk.LEFT:n(t,0,s,-10,0);break;case wk.RIGHT:n(t,0,s,10,0);break;case wk.UP:n(t,0,s,0,-10);break;case wk.DOWN:n(t,0,s,0,10);break;case wk.ENTER:case wk.SPACEBAR:t.preventDefault(),r()}})}(),l=xk.extend({toggleVisibility:function y(n){var t;t=xk.map(c,function(n){return"#"+a+"-"+n.name}).concat(xk.map(i,function(n){return"#"+a+"-"+n})).join(","),n?vk(t,o).show():vk(t,o).hide()},setClampRect:function x(n){f=n,p(s)},setRect:h,getInnerRect:function(){return m(f,s)},setInnerRect:v,setViewPortRect:function w(n){e=n,p(s)},destroy:function n(){xk.each(t,function(n){n.destroy()}),t=[]}},yk)};function Dk(n){var t=ye(n),e=ye(on.none()),o=function s(){var e=[],o=-1;function n(){return 0<o}function t(){return-1!==o&&o<e.length-1}return{data:e,add:function r(n){var t;return t=e.splice(++o),e.push(n),{state:n,removed:t}},undo:function i(){if(n())return e[--o]},redo:function u(){if(t())return e[++o]},canUndo:n,canRedo:t}}();function r(n){t.set(n)}function i(n){v.URL.revokeObjectURL(n.url)}function u(n){var t=a(n);return r(t),function(n){xk.each(n,i)}(o.add(t).removed),t.url}o.add(n);var a=function(n){return{blob:n,url:v.URL.createObjectURL(n)}},c=function(){e.get().each(i),e.set(on.none())};return{getBlobState:function(){return t.get()},setBlobState:r,addBlobState:u,getTempState:function(){return e.get().fold(function(){return t.get()},function(n){return n})},updateTempState:function(n){var t=a(n);return c(),e.set(on.some(t)),t.url},addTempState:function(n){var t=a(n);return e.set(on.some(t)),t.url},applyTempState:function(t){return e.get().fold(function(){},function(n){u(n.blob),t()})},destroyTempState:c,undo:function(){var n=o.undo();return r(n),n.url},redo:function(){var n=o.redo();return r(n),n.url},getHistoryStates:function(){return{undoEnabled:o.canUndo(),redoEnabled:o.canRedo()}}}}function Ak(n,t){function i(n){var t=s.getHistoryStates();m.updateButtonUndoStates(n,t.undoEnabled,t.redoEnabled),io(n,hk.formActionEvent,{name:hk.saveState(),value:t.undoEnabled})}function u(n){return n.toBlob()}function a(n){io(n,hk.formActionEvent,{name:hk.disable(),value:{}})}function r(t,n,e,o,r){return a(t),function(n){return rC(n)}(n).then(e).then(u).then(o).then(function(n){return l(t,n).then(function(n){return i(t),r(),f(t),n})})["catch"](function(n){return v.console.log(n),f(t),n})}function c(n,t,e){var o=s.getBlobState().blob;r(n,o,t,function(n){return s.updateTempState(n)},e)}var s=Dk(n.currentState),f=function(n){e.getApplyButton(n).each(function(n){kh.enable(n)}),io(n,hk.formActionEvent,{name:hk.enable(),value:{}})},l=function(n,t){return a(n),o.updateSrc(n,t)},d=function(n){var t=s.getBlobState().url;return s.destroyTempState(),i(n),t},o=kk(n.currentState.url),m=function(n){var o=bm(Ok("Undo","undo",!0,function(n){io(n,dk.undo(),{direction:1})},n)),r=bm(Ok("Redo","redo",!0,function(n){io(n,dk.redo(),{direction:1})},n));return{container:Gb.sketch({dom:{tag:"div",classes:["tox-image-tools__toolbar","tox-image-tools__sidebar"]},components:[o.asSpec(),r.asSpec(),Ok("Zoom in","zoom-in",!1,function(n){io(n,dk.zoom(),{direction:1})},n),Ok("Zoom out","zoom-out",!1,function(n){io(n,dk.zoom(),{direction:-1})},n)]}),updateButtonUndoStates:function(n,t,e){o.getOpt(n).each(function(n){Ek(n,t)}),r.getOpt(n).each(function(n){Ek(n,e)})}}}(t),e=ek(o,t);return{dom:{tag:"div",attributes:{role:"presentation"}},components:[e.memContainer.asSpec(),o.memContainer.asSpec(),m.container],behaviours:ya([nl.config({store:{mode:"manual",getValue:function(){return s.getBlobState()}}}),Jd("image-tools-events",[mo(dk.undo(),function(t,n){var e=s.undo();l(t,e).then(function(n){f(t),i(t)})}),mo(dk.redo(),function(t,n){var e=s.redo();l(t,e).then(function(n){f(t),i(t)})}),mo(dk.zoom(),function(n,t){var e=t.event().direction();o.zoom(n,e)}),mo(dk.back(),function(n,t){!function(t){var n=d(t);l(t,n).then(function(n){f(t)})}(n),t.event().swap()(),o.hideCrop()}),mo(dk.apply(),function(n,t){s.applyTempState(function(){d(n),t.event().swap()()})}),mo(dk.transform(),function(n,t){return c(n,t.event().transform(),Z)}),mo(dk.tempTransform(),function(n,t){return function(n,t){var e=s.getTempState().blob;r(n,e,t,function(n){return s.addTempState(n)},Z)}(n,t.event().transform())}),mo(dk.transformApply(),function(n,t){return function(e,n,t){var o=s.getBlobState().blob;r(e,o,n,function(n){var t=s.addBlobState(n);return d(e),t},t)}(n,t.event().transform(),t.event().swap())}),mo(dk.swap(),function(t,n){!function(n){m.updateButtonUndoStates(n,!1,!1)}(t);var e=n.event().transform(),o=n.event().swap();e.fold(function(){o()},function(n){c(t,n,o)})})]),wS()])}}function _k(e,t){var n=e.label.map(function(n){return Ay(n,t)}),o=[kh.config({disabled:e.disabled}),dg.config({mode:"execution",useEnter:!0!==e.multiline,useControlEnter:!0===e.multiline,execute:function(n){return ro(n,ly),on.some(!0)}}),Jd("textfield-change",[mo(oi(),function(n,t){io(n,ay,{name:e.name})}),mo(li(),function(n,t){io(n,ay,{name:e.name})})]),Xy.config({})],r=e.validation.map(function(o){return Uy.config({getRoot:function(n){return To(n.element())},invalidClass:"tox-invalid",validator:{validate:function(n){var t=nl.getValue(n),e=o.validator(t);return Hy(!0===e?an.value(t):an.error(e))},validateOnLoad:o.validateOnLoad}})}).toArray(),i=e.placeholder.fold(nn({}),function(n){return{placeholder:t.translate(n)}}),u=e.inputMode.fold(nn({}),function(n){return{inputmode:n}}),a=N(N({},i),u),c=by.parts().field({tag:!0===e.multiline?"textarea":"input",inputAttributes:a,inputClasses:[e.classname],inputBehaviours:ya(H([o,r])),selectOnFocus:!1,factory:xy}),s=(e.flex?["tox-form__group--stretched"]:[]).concat(e.maximized?["tox-form-group--maximize"]:[]),f=[kh.config({disabled:e.disabled,onDisabled:function(n){by.getField(n).each(kh.disable)},onEnabled:function(n){by.getField(n).each(kh.enable)}})];return Dy(n,c,s,f)}function Mk(n){var t=ye(null);return tu({readState:function(){return{timer:null!==t.get()?"set":"unset"}},setTimer:function(n){t.set(n)},cancel:function(){var n=t.get();null!==n&&n.cancel()}})}function Fk(n,t,e){var o=nl.getValue(e);nl.setValue(t,o),kE(t)}function Ik(n,t){var e=n.element(),o=Tr(e),r=e.dom();"number"!==Lo(e,"type")&&t(r,o)}function Rk(n,t,e){if(n.selectsOver){var o=nl.getValue(t),r=n.getDisplayText(o),i=nl.getValue(e);return 0===n.getDisplayText(i).indexOf(r)?on.some(function(){Fk(0,t,e),function(n,e){Ik(n,function(n,t){return n.setSelectionRange(e,t.length)})}(t,r.length)}):on.none()}return on.none()}function Vk(n){return DE(Ny(n))}function Nk(n){return{type:"menuitem",value:n.url,text:n.title,meta:{attach:n.attach},onAction:function(){}}}function Hk(n,t){return{type:"menuitem",value:t,text:n,meta:{attach:undefined},onAction:function(){}}}function Pk(n,t){return function(n){return S(n,Nk)}(function(t,n){return C(n,function(n){return n.type===t})}(n,t))}function zk(n,t){var e=n.toLowerCase();return C(t,function(n){var t=n.meta!==undefined&&n.meta.text!==undefined?n.meta.text:n.text;return Vt(t.toLowerCase(),e)||Vt(n.value.toLowerCase(),e)})}function Lk(e,n,o){var t=nl.getValue(n),r=t.meta.text!==undefined?t.meta.text:t.value;return o.getLinkInformation().fold(function(){return[]},function(n){var t=zk(r,function(n){return S(n,function(n){return Hk(n,n)})}(o.getHistory(e)));return"file"===e?function(n){return O(n,function(n,t){return 0===n.length||0===t.length?n.concat(t):n.concat(_E,t)},[])}([t,zk(r,function(n){return Pk("header",n.targets)}(n)),zk(r,H([function(n){return on.from(n.anchorTop).map(function(n){return Hk("<top>",n)}).toArray()}(n),function(n){return Pk("anchor",n.targets)}(n),function(n){return on.from(n.anchorBottom).map(function(n){return Hk("<bottom>",n)}).toArray()}(n)]))]):t})}function jk(r,o,i){function u(n){var t=nl.getValue(n);i.addToHistory(t.value,r.filetype)}var n,t,e,a,c,s=o.shared.providers,f=by.parts().field({factory:BE,dismissOnBlur:!0,inputClasses:["tox-textfield"],sandboxClasses:["tox-dialog__popups"],inputAttributes:{"aria-errormessage":ME,type:"url"},minChars:0,responseTime:0,fetch:function(n){var t=Lk(r.filetype,n,i),e=HC(t,Ih.BUBBLE_TO_SANDBOX,o,!1);return Hy(e)},getHotspot:function(n){return h.getOpt(n)},onSetValue:function(n,t){n.hasConfigured(Uy)&&Uy.run(n).get(Z)},typeaheadBehaviours:ya(H([i.getValidationHandler().map(function(e){return Uy.config({getRoot:function(n){return To(n.element())},invalidClass:"tox-control-wrap--status-invalid",notify:{onInvalid:function(n,t){d.getOpt(n).each(function(n){zo(n.element(),"title",s.translate(t))})}},validator:{validate:function(n){var t=nl.getValue(n);return AE(function(o){e({type:r.filetype,url:t.value},function(n){if("invalid"===n.status){var t=an.error(n.message);o(t)}else{var e=an.value(n.message);o(e)}})})},validateOnLoad:!1}})}).toArray(),[kh.config({disabled:r.disabled}),Xy.config({}),Jd("urlinput-events",H(["file"===r.filetype?[mo(oi(),function(n){io(n,ay,{name:r.name})})]:[],[mo(ri(),function(n){io(n,ay,{name:r.name}),u(n)}),mo(li(),function(n){io(n,ay,{name:r.name}),u(n)})]]))]])),eventOrder:(n={},n[oi()]=["streaming","urlinput-events","invalidating"],n),model:{getDisplayText:function(n){return n.value},selectsOver:!1,populateFromBrowse:!1},markers:{openClass:"tox-textfield--popup-open"},lazySink:o.shared.getSink,parts:{menu:Cv(0,0,"normal")},onExecute:function(n,t,e){io(t,ly,{})},onItemExecute:function(n,t,e,o){u(n),io(n,ay,{name:r.name})}}),l=r.label.map(function(n){return Ay(n,s)}),d=bm((t="invalid",e=on.some(ME),void 0===(a="warning")&&(a=t),void 0===c&&(c=t),{dom:{tag:"div",classes:["tox-icon","tox-control-wrap__status-icon-"+t],innerHtml:xm(a,s.icons),attributes:N({title:s.translate(c),"aria-live":"polite"},e.fold(function(){return{}},function(n){return{id:n}}))}})),m=bm({dom:{tag:"div",classes:["tox-control-wrap__status-icon-wrap"]},components:[d.asSpec()]}),g=i.getUrlPicker(r.filetype),p=Yo("browser.url.event"),h=bm({dom:{tag:"div",classes:["tox-control-wrap"]},components:[f,m.asSpec()],behaviours:ya([kh.config({disabled:r.disabled})])}),v=bm(XC({name:r.name,icon:on.some("browse"),text:r.label.getOr(""),disabled:r.disabled,primary:!1,borderless:!0},function(n){return ro(n,p)},s,[],["tox-browse-url"]));return by.sketch({dom:qy([]),components:l.toArray().concat([{dom:{tag:"div",classes:["tox-form__controls-h-stack"]},components:H([[h.asSpec()],g.map(function(){return v.asSpec()}).toArray()])}]),fieldBehaviours:ya([kh.config({disabled:r.disabled,onDisabled:function(n){by.getField(n).each(kh.disable),v.getOpt(n).each(kh.disable)},onEnabled:function(n){by.getField(n).each(kh.enable),v.getOpt(n).each(kh.enable)}}),Jd("url-input-events",[mo(p,function(o){nd.getCurrent(o).each(function(t){var e=nl.getValue(t);g.each(function(n){n(e).get(function(n){nl.setValue(t,n),io(o,ay,{name:r.name})})})})})])])})}function Uk(u,t){function n(o){return function(t,e){Eu(e.event().target(),"[data-collection-item-value]").each(function(n){o(t,e,n,Lo(n,"data-collection-item-value"))})}}var e=u.label.map(function(n){return Ay(n,t)}),o=n(function(n,t,e,o){t.stop(),io(n,fy,{name:u.name,value:o})}),r=[mo(Qr(),n(function(n,t,e){Sa(e)})),mo(ii(),o),mo(pi(),o),mo(Zr(),n(function(n,t,e){Ou(n.element(),"."+gh).each(function(n){mr(n,gh)}),lr(e,gh)})),mo(ni(),n(function(n){Ou(n.element(),"."+gh).each(function(n){mr(n,gh)})})),Hi(n(function(n,t,e,o){io(n,fy,{name:u.name,value:o})}))],i=by.parts().field({dom:{tag:"div",classes:["tox-collection"].concat(1!==u.columns?["tox-collection--grid"]:["tox-collection--list"])},components:[],factory:{sketch:l},behaviours:ya([gg.config({}),nl.config({store:{mode:"memory",initialValue:[]},onSetValue:function(o,n){!function(n,t){var e=S(t,function(n){var t=ih.translate(n.text),e=1===u.columns?'<div class="tox-collection__item-label">'+t+"</div>":"",o='<div class="tox-collection__item-icon">'+n.icon+"</div>",r={_:" "," - ":" ","-":" "},i=t.replace(/\_| \- |\-/g,function(n){return r[n]});return'<div class="tox-collection__item" tabindex="-1" data-collection-item-value="'+function(n){return'"'===n?"&quot;":n}(n.value)+'" title="'+i+'" aria-label="'+i+'">'+o+e+"</div>"}),o=1<u.columns&&"auto"!==u.columns?w(e,u.columns):[e],r=S(o,function(n){return'<div class="tox-collection__group">'+n.join("")+"</div>"});Ho(n.element(),r.join(""))}(o,n),"auto"===u.columns&&up(o,5,"tox-collection__item").each(function(n){var t=n.numRows,e=n.numColumns;dg.setGridSize(o,t,e)}),ro(o,py)}}),Xy.config({}),dg.config(function(n,t){return 1===n?{mode:"menu",moveOnTab:!1,selector:".tox-collection__item"}:"auto"===n?{mode:"flatgrid",selector:".tox-collection__item",initSize:{numColumns:1,numRows:1}}:{mode:"matrix",selectors:{row:"color"===t?".tox-swatches__row":".tox-collection__group",cell:"color"===t?"."+ch:"."+ah}}}(u.columns,"normal")),Jd("collection-events",r)])});return Dy(e,i,["tox-form__group--collection"],[])}function Wk(r){return function(t,e,o){return Nn(e,"name").fold(function(){return r(e,o)},function(n){return t.field(n,r(e,o))})}}function Gk(t,n,e){var o=Dn(e,{shared:{interpreter:function(n){return RE(t,n,o)}}});return RE(t,n,o)}function Xk(n){return{colorPicker:function(e){return function(n,t){Wv.colorPickerDialog(e)(n,t)}}(n),hasCustomColors:function(n){return function(){return Iv(n)}}(n),getColors:function(n){return function(){return Rv(n)}}(n),getColorCols:function(n){return function(){return Wv.getColorCols(n)}}(n)}}function Yk(e){return function(n){return on.from(n.getParam("style_formats")).filter(fn)}(e).map(function(n){var t=function(t,n){function e(n){bn(n,function(n){t.formatter.has(n.name)||t.formatter.register(n.name,n.format)})}var o=zE(n);return t.formatter?e(o.customFormats):t.on("init",function(){e(o.customFormats)}),o.formats}(e,n);return function(n){return n.getParam("style_formats_merge",!1,"boolean")}(e)?PE.concat(t):t}).getOr(PE)}function qk(n,t,e){var o={type:"formatter",isSelected:t(n.format),getStylePreview:e(n.format)};return Dn(n,o)}function Kk(r,n,i,u){var o=function(n){return S(n,function(n){var t=wn(n);if($(n,"items")){var e=o(n.items);return Dn(function(n){var t={type:"submenu",isSelected:nn(!1),getStylePreview:function(){return on.none()}};return Dn(n,t)}(n),{getStyleItems:function(){return e}})}return $(n,"format")?function(n){return qk(n,i,u)}(n):1===t.length&&vn(t,"title")?Dn(n,{type:"separator"}):function(n){var t=Yo(n.title),e={type:"formatter",format:t,isSelected:i(t),getStylePreview:u(t)},o=Dn(n,e);return r.formatter.register(t,o),o}(n)})};return o(n)}function Jk(t){return function(n){if(n&&1===n.nodeType){if(n.contentEditable===t)return!0;if(n.getAttribute("data-mce-contenteditable")===t)return!0}return!1}}function $k(n,t,e,o,r){return{type:n,title:t,url:e,level:o,attach:r}}function Qk(n){return n.innerText||n.textContent}function Zk(n){return function(n){return n&&"A"===n.nodeName&&(n.id||n.name)!==undefined}(n)&&WE(n)}function nO(n){return n&&/^(H[1-6])$/.test(n.nodeName)}function tO(n){return nO(n)&&WE(n)}function eO(n){var t=function(n){return n.id?n.id:Yo("h")}(n);return $k("header",Qk(n),"#"+t,function(n){return nO(n)?parseInt(n.nodeName.substr(1),10):0}(n),function(){n.id=t})}function oO(n){var t=n.id||n.name,e=Qk(n);return $k("anchor",e||"#"+t,"#"+t,0,Z)}function rO(n){return function(n,t){return S(Lc(we.fromDom(t),n),function(n){return n.dom()})}("h1,h2,h3,h4,h5,h6,a:not([href])",n)}function iO(n){return 0<LE(n.title).length}function uO(n){return cn(n)&&/^https?/.test(n)}function aO(n){return sn(n)&&I(n,function(n){return!function(n){return fn(n)&&n.length<=5&&D(n,uO)}(n)}).isNone()}function cO(){var n,t=v.localStorage.getItem(XE);if(null===t)return{};try{n=JSON.parse(t)}catch(e){if(e instanceof SyntaxError)return v.console.log("Local storage "+XE+" was not valid JSON",e),{};throw e}return aO(n)?n:(v.console.log("Local storage "+XE+" was not valid format",n),{})}function sO(n){var t=cO();return Object.prototype.hasOwnProperty.call(t,n)?t[n]:[]}function fO(t,n){if(uO(t)){var e=cO(),o=Object.prototype.hasOwnProperty.call(e,n)?e[n]:[],r=C(o,function(n){return n!==t});e[n]=[t].concat(r).slice(0,5),function(n){if(!aO(n))throw new Error("Bad format for history:\n"+JSON.stringify(n));v.localStorage.setItem(XE,JSON.stringify(n))}(e)}}function lO(n){return!!n}function dO(n){return P(xk.makeMap(n,/[, ]/),lO)}function mO(n,t,e){var o=function(n,t){return YE.call(n,t)?on.some(n[t]):on.none()}(n,t).getOr(e);return cn(o)?on.some(o):on.none()}function gO(n){return on.some(n.file_picker_callback).filter(dn)}function pO(n,t){var e=function(n){var t=on.some(n.file_picker_types).filter(lO),e=on.some(n.file_browser_callback_types).filter(lO),o=t.or(e).map(dO);return gO(n).fold(function(){return!1},function(n){return o.fold(function(){return!0},function(n){return 0<wn(n).length&&n})})}(n);return ln(e)?e?gO(n):on.none():e[t]?gO(n):on.none()}function hO(t){return{getHistory:sO,addToHistory:fO,getLinkInformation:function(){return function(n){return!1===n.settings.typeahead_urls?on.none():on.some({targets:GE(n.getBody()),anchorTop:mO(n.settings,"anchor_top","#top").getOrUndefined(),anchorBottom:mO(n.settings,"anchor_bottom","#bottom").getOrUndefined()})}(t)},getValidationHandler:function(){return function(n){return on.from(n.settings.file_picker_validator_handler).filter(dn).orThunk(function(){return on.from(n.settings.filepicker_validator_handler).filter(dn)})}(t)},getUrlPicker:function(n){return function(r,i){return pO(r.settings,i).map(function(o){return function(t){return Ny(function(e){var n=xk.extend({filetype:i},on.from(t.meta).getOr({}));o.call(r,function(n,t){if(!cn(n))throw new Error("Expected value to be string");if(t!==undefined&&!sn(t))throw new Error("Expected meta to be a object");e({value:n,meta:t})},t.value,n)})}})}(t,n)}}}function vO(n,t,e,o){var r=ye(!1),i={shared:{providers:{icons:function(){return t.ui.registry.getAll().icons},menuItems:function(){return t.ui.registry.getAll().menuItems},translate:ih.translate},interpreter:function(n){return function(n,t){return RE(IE,n,t)}(n,i)},anchors:HE(t,e,o),getSink:function(){return an.value(n)}},urlinput:hO(t),styleselect:function(e){function o(n){return function(){return e.formatter.match(n)}}function r(t){return function(){var n=e.formatter.get(t);return n!==undefined?on.some({tag:0<n.length&&(n[0].inline||n[0].block)||"div",styleAttr:e.formatter.getCssText(t)}):on.none()}}var i=function(n){var t=n.items;return t!==undefined&&0<t.length?B(t,i):[n.format]},u=ye([]),a=ye([]),c=ye([]),s=ye([]),f=ye(!1);e.on("init",function(){var n=Yk(e),t=Kk(e,n,o,r);u.set(t),a.set(B(t,i))}),e.on("addStyleModifications",function(n){var t=Kk(e,n.items,o,r);c.set(t),f.set(n.replace),s.set(B(t,i))});return{getData:function(){var n=f.get()?[]:u.get(),t=c.get();return n.concat(t)},getFlattenedKeys:function(){var n=f.get()?[]:a.get(),t=s.get();return n.concat(t)}}}(t),colorinput:Xk(t),dialog:function(n){return{isDraggableModal:function(n){return function(){return function(n){return n.getParam("draggable_modal",!1,"boolean")}(n)}}(n)}}(t),isContextMenuOpen:function(){return r.get()},setContextMenuState:function(n){return r.set(n)}};return i}function bO(n,t,o){var e=function(n,e){return O(n,function(t,n){return e(n,t.len).fold(nn(t),function(n){return{len:n.finish(),list:t.list.concat([n])}})},{len:0,list:[]}).list}(n,function(n,t){var e=o(n);return on.some({element:nn(n),start:nn(t),finish:nn(t+e),width:nn(e)})}),r=C(e,function(n){return n.finish()<=t}),i=k(r,function(n,t){return n+t.width()},0),u=e.slice(r.length);return{within:nn(r),extra:nn(u),withinWidth:nn(i)}}function yO(n){return S(n,function(n){return n.element()})}function xO(n,t,e,o){var r=function(n,t,e){var o=bO(t,n,e);return 0===o.extra().length?on.some(o):on.none()}(n,t,e).getOrThunk(function(){return bO(t,n-e(o),e)}),i=r.within(),u=r.extra(),a=r.withinWidth();return 1===u.length&&u[0].width()<=e(o)?function(n,t,e){var o=yO(n.concat(t));return QE(o,[],e)}(i,u,a):1<=u.length?function(n,t,e,o){var r=yO(n).concat([e]);return QE(r,yO(t),o)}(i,u,o,a):function(n,t,e){return QE(yO(n),[],e)}(i,0,a)}function wO(n,t){var e=S(t,function(n){return cu(n)});$E.setGroups(n,e)}function SO(n,t,e,o){var r=Js(n,t,"primary"),i=Ks(n,t,"overflow-button"),u=$y.getCoupled(n,"overflowGroup");xr(r.element(),"visibility","hidden");var a=function(n,t){return n.bind(function(t){return ka(t.element()).bind(function(n){return t.getSystem().getByDom(n).toOption()})}).orThunk(function(){return t.filter(bg.isFocused)})}(e,i);e.each(function(n){$E.setGroups(n,[])});var c=t.builtGroups.get();wO(r,c.concat([u]));var s=gu(r.element()),f=xO(s,c,function(n){return gu(n.element())},u);0===f.extra().length?(gg.remove(r,u),e.each(function(n){$E.setGroups(n,[])})):(wO(r,f.within()),e.each(function(n){wO(n,f.extra())})),Or(r.element(),"visibility"),Er(r.element()),e.each(function(t){i.each(function(n){return kg.set(n,o(t))}),a.each(bg.focus)})}function CO(o,n,t,e,r){var i="alloy.toolbar.toggle";return{uid:o.uid,dom:o.dom,components:n,behaviours:Vs(o.splitToolbarBehaviours,[$y.config({others:N(N({},r.coupling),{overflowGroup:function(t){return tT.sketch(N(N({},e["overflow-group"]()),{items:[Xg.sketch(N(N({},e["overflow-button"]()),{action:function(n){ro(t,i)}}))]}))}})}),Jd("toolbar-toggle-events",[mo(i,function(n){r.apis.toggle(n)})])]),apis:N({setGroups:function(n,t){!function(n,t){var e=S(t,n.getSystem().build);o.builtGroups.set(e)}(n,t),r.apis.refresh(n)},getMoreButton:function(n){return function(n){return Ks(n,o,"overflow-button")}(n)}},r.apis),domModification:{attributes:{role:"group"}}}}function kO(n){return n.getSystem().isConnected()}function OO(n,t,e){var o=t.lazySink(n).getOrDie(),r=t.getAnchor(n),i=t.getOverflowBounds.map(function(n){return n()});_f.positionWithinBounds(o,r,e,i)}function EO(t,e){var n=Lf.getState($y.getCoupled(t,"sandbox"));SO(t,e,n,kO),n.each(function(n){return OO(t,e,n)})}function TO(t,e){Lf.getState($y.getCoupled(t,"sandbox")).each(function(n){return OO(t,e,n)})}function BO(t,n){return n.getAnimationRoot.fold(function(){return t.element()},function(n){return n(t)})}function DO(n){return n.dimension.property}function AO(n,t){return n.dimension.getDimension(t)}function _O(n,t){var e=BO(n,t);hr(e,[t.shrinkingClass,t.growingClass])}function MO(n,t){mr(n.element(),t.openClass),lr(n.element(),t.closedClass),xr(n.element(),DO(t),"0px"),Er(n.element())}function FO(n,t){mr(n.element(),t.closedClass),lr(n.element(),t.openClass),Or(n.element(),DO(t))}function IO(n,t,e,o){e.setCollapsed(),xr(n.element(),DO(t),AO(t,n.element())),Er(n.element()),_O(n,t),MO(n,t),t.onStartShrink(n),t.onShrunk(n)}function RO(n,t,e,o){var r=o.getOrThunk(function(){return AO(t,n.element())});e.setCollapsed(),xr(n.element(),DO(t),r),Er(n.element());var i=BO(n,t);mr(i,t.growingClass),lr(i,t.shrinkingClass),MO(n,t),t.onStartShrink(n)}function VO(n,t,e){var o=AO(t,n.element());("0px"===o?IO:RO)(n,t,e,on.some(o))}function NO(n,t,e){var o=BO(n,t),r=gr(o,t.shrinkingClass),i=AO(t,n.element());FO(n,t);var u=AO(t,n.element());(r?function(){xr(n.element(),DO(t),i),Er(n.element())}:function(){MO(n,t)})(),mr(o,t.shrinkingClass),lr(o,t.growingClass),FO(n,t),xr(n.element(),DO(t),u),e.setExpanded(),t.onStartGrow(n)}function HO(n,t,e){var o=BO(n,t);return!0===gr(o,t.growingClass)}function PO(n,t,e){var o=BO(n,t);return!0===gr(o,t.shrinkingClass)}function zO(n){return sT.hasGrown(n)}function LO(n,t){var e=n.outerContainer;!function(n,t){var e=n.outerContainer.element();t&&(n.mothership.broadcastOn([jf()],{target:e}),n.uiMothership.broadcastOn([jf()],{target:e})),n.mothership.broadcastOn([pT],{readonly:t}),n.uiMothership.broadcastOn([pT],{readonly:t})}(n,t),Lt("*",e.element()).forEach(function(n){e.getSystem().getByDom(n).each(function(n){n.hasConfigured(kh)&&kh.set(n,t)})})}function jO(n,t){n.on("init",function(){n.readonly&&LO(t,!0)}),n.on("SwitchMode",function(){return LO(t,n.readonly)}),function(n){return n.getParam("readonly",!1,"boolean")}(n)&&n.setMode("readonly")}function UO(e){var n;return dc.config({channels:(n={},n[pT]={schema:hT,onReceive:function(n,t){e(n).each(function(n){!function(t,e){Lt("*",t.element()).forEach(function(n){t.getSystem().getByDom(n).each(function(n){n.hasConfigured(kh)&&kh.set(n,e)})})}(n,t.readonly)})}},n)})}function WO(n){var t=n.title.fold(function(){return{}},function(n){return{attributes:{title:n}}});return{dom:N({tag:"div",classes:["tox-toolbar__group"]},t),components:[tT.parts().items({})],items:n.items,markers:{itemSelector:"*:not(.tox-split-button) > .tox-tbtn:not([disabled]), .tox-split-button:not([disabled]), .tox-toolbar-nav-js:not([disabled])"},tgroupBehaviours:ya([Xy.config({}),bg.config({})])}}function GO(n){return tT.sketch(WO(n))}function XO(e,n,t){var o=Ri(function(n){var t=S(e.initGroups,GO);$E.setGroups(n,t)});return ya([dg.config({mode:n,onEscape:e.onEscape,selector:".tox-toolbar__group"}),Jd("toolbar-events",[o]),UO(t)])}function YO(n,t){var e=n.cyclicKeying?"cyclic":"acyclic";return{uid:n.uid,dom:{tag:"div",classes:["tox-toolbar-overlord"]},parts:{"overflow-group":WO({title:on.none(),items:[]}),"overflow-button":UC({name:"more",icon:on.some("more-drawer"),disabled:!1,tooltip:on.some("More..."),primary:!1,borderless:!1},on.none(),n.backstage.shared.providers)},splitToolbarBehaviours:XO(n,e,t)}}function qO(r){var n=YO(r,iT.getOverflow),t=iT.parts().primary({dom:{tag:"div",classes:["tox-toolbar__primary"]}});return iT.sketch(N(N({},n),{lazySink:r.getSink,getAnchor:function(){return r.backstage.shared.anchors.toolbarOverflow()},getOverflowBounds:function(){var n=r.moreDrawerData.lazyHeader().element(),t=Su(n),e=Oo(n),o=Su(e);return xu(t.x()+4,o.y(),t.width()-8,o.height())},parts:N(N({},n.parts),{overflow:{dom:{tag:"div",classes:["tox-toolbar__overflow"]}}}),components:[t],markers:{overflowToggledClass:"tox-tbtn--enabled"}}))}function KO(n){var t=mT.parts().primary({dom:{tag:"div",classes:["tox-toolbar__primary"]}}),e=mT.parts().overflow({dom:{tag:"div",classes:["tox-toolbar__overflow"]}}),o=YO(n,mT.getOverflow);return mT.sketch(N(N({},o),{components:[t,e],markers:{openClass:"tox-toolbar__overflow--open",closedClass:"tox-toolbar__overflow--closed",growingClass:"tox-toolbar__overflow--growing",shrinkingClass:"tox-toolbar__overflow--shrinking",overflowToggledClass:"tox-tbtn--enabled"},onOpened:function(n){n.getSystem().broadcastOn([gT()],{type:"opened"})},onClosed:function(n){n.getSystem().broadcastOn([gT()],{type:"closed"})}}))}function JO(n){var t=n.cyclicKeying?"cyclic":"acyclic";return $E.sketch({uid:n.uid,dom:{tag:"div",classes:["tox-toolbar"].concat(n.type===Ub.scrolling?["tox-toolbar--scrolling"]:[])},components:[$E.parts().groups({})],toolbarBehaviours:XO(n,t,nn(on.none()))})}function $O(n){return tt("toolbarbutton",bT,n)}function QO(n){return tt("menubutton",xT,n)}function ZO(n){return tt("ToggleButton",CT,n)}function nE(t){return{isDisabled:function(){return kh.isDisabled(t)},setDisabled:function(n){return kh.set(t,n)}}}function tE(t){return{setActive:function(n){kg.set(t,n)},isActive:function(){return kg.isOn(t)},isDisabled:function(){return kh.isDisabled(t)},setDisabled:function(n){return kh.set(t,n)}}}function eE(n,t){return n.map(function(n){return{"aria-label":t.translate(n),title:t.translate(n)}}).getOr({})}function oE(t,e,n,o,r,i){function u(n){return ih.isRtl()&&vn(UT,n)?n+"-rtl":n}var a,c=ih.isRtl()&&t.exists(function(n){return vn(WT,n)});return{dom:{tag:"button",classes:["tox-tbtn"].concat(e.isSome()?["tox-tbtn--select"]:[]).concat(c?["tox-tbtn__icon-rtl"]:[]),attributes:eE(n,i)},components:Dh([t.map(function(n){return _C(u(n),i.icons)}),e.map(function(n){return FC(n,"tox-tbtn",i)})]),eventOrder:(a={},a[qr()]=["focusing","alloy.base.behaviour","common-button-display-events"],a),buttonBehaviours:ya([Jd("common-button-display-events",[mo(qr(),function(n,t){t.event().prevent(),ro(n,jT)})])].concat(o.map(function(n){return VT.config({channel:n,initialData:{icon:t,text:e},renderComponents:function(n,t){return Dh([n.icon.map(function(n){return _C(u(n),i.icons)}),n.text.map(function(n){return FC(n,"tox-tbtn",i)})])}})}).toArray()).concat(r.getOr([])))}}function rE(n,t,e){var o=ye(Z),r=oE(n.icon,n.text,n.tooltip,on.none(),on.none(),e);return Xg.sketch({dom:r.dom,components:r.components,eventOrder:rk,buttonBehaviours:ya([Jd("toolbar-button-events",[function(e){return Hi(function(t,n){Ep(e,t)(function(n){io(t,ok,{buttonApi:n}),e.onAction(n)})})}({onAction:n.onAction,getApi:t.getApi}),Tp(t,o),Bp(t,o)]),Bh(n.disabled)].concat(t.toolbarButtonBehaviours))})}function iE(t,n){function e(e){return{isDisabled:function(){return kh.isDisabled(e)},setDisabled:function(n){return kh.set(e,n)},setIconFill:function(n,t){Ou(e.element(),'svg path[id="'+n+'"], rect[id="'+n+'"]').each(function(n){zo(n,"fill",t)})},setIconStroke:function(n,t){Ou(e.element(),'svg path[id="'+n+'"], rect[id="'+n+'"]').each(function(n){zo(n,"stroke",t)})},setActive:function(t){zo(e.element(),"aria-pressed",t),Ou(e.element(),"span").each(function(n){e.getSystem().getByDom(n).each(function(n){return kg.set(n,t)})})},isActive:function(){return Ou(e.element(),"span").exists(function(n){return e.getSystem().getByDom(n).exists(kg.isOn)})}}}var o,r=Yo("channel-update-split-dropdown-display"),i=ye(Z),u={getApi:e,onSetup:t.onSetup};return LT.sketch({dom:{tag:"div",classes:["tox-split-button"],attributes:An({"aria-pressed":!1},eE(t.tooltip,n.providers))},onExecute:function(n){t.onAction(e(n))},onItemExecute:function(n,t,e){},splitDropdownBehaviours:ya([Th(!1),Jd("split-dropdown-events",[mo(jT,bg.focus),Tp(u,i),Bp(u,i)]),Tw.config({})]),eventOrder:(o={},o[ki()]=["alloy.base.behaviour","split-dropdown-events"],o),toggleClass:"tox-tbtn--enabled",lazySink:n.getSink,fetch:function(e,r,o){return function(t){return Ny(function(n){return r.fetch(n)}).map(function(n){return on.from(ab(Dn(Bv(Yo("menu-value"),n,function(n){r.onItemAction(e(t),n)},r.columns,r.presets,Ih.CLOSE_ON_EXECUTE,r.select.getOr(function(){return!1}),o),{movement:Dv(r.columns,r.presets),menuBehaviours:$p("auto"!==r.columns?[]:[Ri(function(o,n){up(o,4,fp(r.presets)).each(function(n){var t=n.numRows,e=n.numColumns;dg.setGridSize(o,t,e)})})])})))})}}(e,t,n.providers),parts:{menu:Cv(0,t.columns,t.presets)},components:[LT.parts().button(oE(t.icon,t.text,on.none(),on.some(r),on.some([kg.config({toggleClass:"tox-tbtn--enabled",toggleOnExecute:!1})]),n.providers)),LT.parts().arrow({dom:{tag:"button",classes:["tox-tbtn","tox-split-button__chevron"],innerHtml:xm("chevron-down",n.providers.icons)}}),LT.parts()["aria-descriptor"]({text:n.providers.translate("To open the popup, press Shift+Enter")})]})}function uE(o,r){return mo(ok,function(n,t){var e=function(n){return{hide:function(){return ro(n,vi())},getValue:function(){return nl.getValue(n)}}}(o.get(n));r.onAction(e,t.event().buttonApi())})}function aE(n,t,e){var o={backstage:{shared:{providers:e}}};return"contextformtogglebutton"===t.type?function(n,t,e){var o=t.original,r=(o.primary,c(o,["primary"])),i=et(ZO(N(N({},r),{type:"togglebutton",onAction:function(){}})));return XT(i,e.backstage.shared.providers,[uE(n,t)])}(n,t,o):function(n,t,e){var o=t.original,r=(o.primary,c(o,["primary"])),i=et($O(N(N({},r),{type:"button",onAction:function(){}})));return GT(i,e.backstage.shared.providers,[uE(n,t)])}(n,t,o)}function cE(n,t){var e=Math.max(t.x(),n.x()),o=n.right()-e,r=t.width()-(e-t.x());return{x:e,width:Math.min(o,r)}}function sE(n){var t=zb(n)||Lb(n)||Yb(n),e=bu(v.window),o=wu(we.fromDom(n.getContentAreaContainer()));return n.inline&&!t?function(n,t,e){var o=cE(t,e),r=o.x,i=o.width;return xu(r,e.y(),i,e.height())}(0,o,e):n.inline?function(n,t,e){var o=cE(t,e),r=o.x,i=o.width,u=we.fromDom(n.getContainer()),a=Ou(u,".tox-editor-header").getOr(u),c=wu(a),s=e.height(),f=e.y();if(c.y()>=t.bottom()){var l=Math.min(s+f,c.y());return xu(r,f,i,l-f)}var d=Math.max(f,c.bottom());return xu(r,d,i,s-(d-f))}(n,o,e):function(n,t,e){var o=cE(t,e),r=o.x,i=o.width,u=we.fromDom(n.getContainer()),a=Ou(u,".tox-editor-header").getOr(u),c=wu(u),s=wu(a),f=Math.max(e.y(),t.y(),s.bottom()),l=c.bottom()-f,d=e.height()-(f-e.y()),m=Math.min(l,d);return xu(r,f,i,m)}(n,o,e)}function fE(t,n){return Au(n,function(n){return n.predicate(t.dom())?on.some({toolbarApi:n,elem:t}):on.none()})}function lE(o,r){return function(t){function n(){t.setActive(o.formatter.match(r));var n=o.formatter.formatChanged(r,t.setActive).unbind;e.set(on.some(n))}var e=ye(on.none());return o.initialized?n():o.on("init",n),function(){return e.get().each(function(n){return n()})}}}function dE(t){return function(n){return function(){t.undoManager.transact(function(){t.focus(),t.execCommand("mceToggleFormat",!1,n.format)})}}}function mE(n,t,e){var o=e.dataset,r="basic"===o.type?function(){return S(o.data,function(n){return qk(n,e.isSelectedFor,e.getPreviewFor)})}:o.getData;return{items:function(n,u,a){function r(n,t,e,o){var r=u.shared.providers.translate(n.title);if("separator"===n.type)return on.some({type:"separator",text:r});if("submenu"!==n.type)return on.some(N({type:"togglemenuitem",text:r,active:n.isSelected(o),disabled:e,onAction:a.onAction(n)},n.getStylePreview().fold(function(){return{}},function(n){return{meta:{style:n}}})));var i=B(n.getStyleItems(),function(n){return c(n,t,o)});return 0===t&&i.length<=0?on.none():on.some({type:"nestedmenuitem",text:r,disabled:i.length<=0,getSubmenuItems:function(){return B(n.getStyleItems(),function(n){return c(n,t,o)})}})}function i(n){var t=a.getCurrentValue(),e=a.shouldHide?0:1;return B(n,function(n){return c(n,e,t)})}var c=function(n,t,e){var o="formatter"===n.type&&a.isInvalid(n);return 0===t?o?[]:r(n,t,!1,e).toArray():r(n,t,o,e).toArray()};return{validateItems:i,getFetch:function(o,r){return function(n){var t=r(),e=i(t);n(HC(e,Ih.CLOSE_ON_EXECUTE,o,!1))}}}}(0,t,e),getStyleItems:r}}function gE(o,n,t){var e=mE(0,n,t),r=e.items,i=e.getStyleItems;return IC({text:t.icon.isSome()?on.none():on.some(""),icon:t.icon,tooltip:on.from(t.tooltip),role:on.none(),fetch:r.getFetch(n,i),onSetup:function(e){return t.setInitialValue.each(function(n){return n(e.getComponent())}),t.nodeChangeHandler.map(function(n){var t=n(e.getComponent());return o.on("NodeChange",t),function(){o.off("NodeChange",t)}}).getOr(Z)},getApi:function(n){return{getComponent:function(){return n}}},columns:1,presets:"normal",classes:t.icon.isSome()?[]:["bespoke"],dropdownBehaviours:[]},"tox-tbtn",n.shared)}var pE,hE,vE,bE,yE=Al({name:"HtmlSelect",configFields:[ct("options"),Is("selectBehaviours",[bg,nl]),St("selectClasses",[]),St("selectAttributes",{}),ht("data")],factory:function(e,n){var t=S(e.options,function(n){return{dom:{tag:"option",value:n.value,innerHtml:n.text}}}),o=e.data.map(function(n){return q("initialValue",n)}).getOr({});return{uid:e.uid,dom:{tag:"select",classes:e.selectClasses,attributes:e.selectAttributes},components:t,behaviours:Vs(e.selectBehaviours,[bg.config({}),nl.config({store:N({mode:"manual",getValue:function(n){return Tr(n.element())},setValue:function(n,t){E(e.options,function(n){return n.value===t}).isSome()&&Br(n.element(),t)}},o)})])}}}),xE=/* */Object.freeze({events:function(n,t){var e=n.stream.streams.setup(n,t);return so([mo(n.event,e),Vi(function(){return t.cancel()})].concat(n.cancelEvent.map(function(n){return[mo(n,function(){return t.cancel()})]}).getOr([])))}}),wE=/* */Object.freeze({throttle:Mk,init:function(n){return n.stream.streams.state(n)}}),SE=[st("stream",it("mode",{throttle:[ct("delay"),St("stopEvent",!0),Zu("streams",{setup:function(n,t){var e=n.stream,o=$g(n.onStream,e.delay);return t.setTimer(o),function(n,t){o.throttle(n,t),e.stopEvent&&t.stop()}},state:Mk})]})),St("event","input"),ht("cancelEvent"),$u("onStream")],CE=xa({fields:SE,name:"streaming",active:xE,state:wE}),kE=function(n){Ik(n,function(n,t){return n.setSelectionRange(t.length,t.length)})},OE=nn("alloy.typeahead.itemexecute"),EE=nn([ht("lazySink"),ct("fetch"),St("minChars",5),St("responseTime",1e3),Ku("onOpen"),St("getHotspot",on.some),St("getAnchorOverrides",nn({})),St("layouts",on.none()),St("eventOrder",{}),Dt("model",{},[St("getDisplayText",function(n){return n.meta!==undefined&&n.meta.text!==undefined?n.meta.text:n.value}),St("selectsOver",!0),St("populateFromBrowse",!0)]),Ku("onSetValue"),Ju("onExecute"),Ku("onItemExecute"),St("inputClasses",[]),St("inputAttributes",{}),St("inputStyles",{}),St("matchWidth",!0),St("useMinWidth",!1),St("dismissOnBlur",!0),Yu(["openClass"]),ht("initialData"),Is("typeaheadBehaviours",[bg,nl,CE,dg,kg,$y]),At("previewing",function(){return ye(!0)})].concat(yy()).concat(cx())),TE=nn([Sl({schema:[Xu()],name:"menu",overrides:function(o){return{fakeFocus:!0,onHighlight:function(t,e){o.previewing.get()?t.getSystem().getByUid(o.uid).each(function(n){Rk(o.model,n,e).fold(function(){return cd.dehighlight(t,e)},function(n){return n()})}):t.getSystem().getByUid(o.uid).each(function(n){o.model.populateFromBrowse&&Fk(o.model,n,e)}),o.previewing.set(!1)},onExecute:function(n,t){return n.getSystem().getByUid(o.uid).toOption().map(function(n){return io(n,OE(),{item:t}),!0})},onHover:function(n,t){o.previewing.set(!1),n.getSystem().getByUid(o.uid).each(function(n){o.model.populateFromBrowse&&Fk(o.model,n,t)})}}}})]),BE=_l({name:"Typeahead",configFields:EE(),partFields:TE(),factory:function(r,n,t,i){function e(n,t,e){r.previewing.set(!1);var o=$y.getCoupled(n,"sandbox");if(Lf.isOpen(o))nd.getCurrent(o).each(function(n){cd.getHighlighted(n).fold(function(){e(n)},function(){co(o,n.element(),"keydown",t)})});else{tx(r,u(n),n,o,i,function(n){nd.getCurrent(n).each(e)},_y.HighlightFirst).get(Z)}}var o=ny(r),u=function(o){return function(n){return n.map(function(n){var t=R(n.menus),e=B(t,function(n){return C(n.items,function(n){return"item"===n.type})});return nl.getState(o).update(S(e,function(n){return n.data})),n})}},a=[bg.config({}),nl.config({onSetValue:r.onSetValue,store:N({mode:"dataset",getDataKey:function(n){return Tr(n.element())},getFallbackEntry:function(n){return{value:n,meta:{}}},setValue:function(n,t){Br(n.element(),r.model.getDisplayText(t))}},r.initialData.map(function(n){return q("initialValue",n)}).getOr({}))}),CE.config({stream:{mode:"throttle",delay:r.responseTime,stopEvent:!1},onStream:function(n,t){var e=$y.getCoupled(n,"sandbox");if(bg.isFocused(n)&&Tr(n.element()).length>=r.minChars){var o=nd.getCurrent(e).bind(function(n){return cd.getHighlighted(n).map(nl.getValue)});r.previewing.set(!0);tx(r,u(n),n,e,i,function(n){nd.getCurrent(e).each(function(n){o.fold(function(){r.model.selectsOver&&cd.highlightFirst(n)},function(t){cd.highlightBy(n,function(n){return nl.getValue(n).value===t.value}),cd.getHighlighted(n).orThunk(function(){return cd.highlightFirst(n),on.none()})})})},_y.HighlightFirst).get(Z)}},cancelEvent:bi()}),dg.config({mode:"special",onDown:function(n,t){return e(n,t,cd.highlightFirst),on.some(!0)},onEscape:function(n){var t=$y.getCoupled(n,"sandbox");return Lf.isOpen(t)?(Lf.close(t),on.some(!0)):on.none()},onUp:function(n,t){return e(n,t,cd.highlightLast),on.some(!0)},onEnter:function(t){var n=$y.getCoupled(t,"sandbox"),e=Lf.isOpen(n);if(e&&!r.previewing.get())return nd.getCurrent(n).bind(function(n){return cd.getHighlighted(n)}).map(function(n){return io(t,OE(),{item:n}),!0});var o=nl.getValue(t);return ro(t,bi()),r.onExecute(n,t,o),e&&Lf.close(n),on.some(!0)}}),kg.config({toggleClass:r.markers.openClass,aria:{mode:"expanded"}}),$y.config({others:{sandbox:function(n){return ux(r,n,{onOpen:function(){return kg.on(n)},onClose:function(){return kg.off(n)}})}}}),Jd("typeaheadevents",[Hi(function(n){var t=Z;ox(r,u(n),n,i,t,_y.HighlightFirst).get(Z)}),mo(OE(),function(n,t){var e=$y.getCoupled(n,"sandbox");Fk(r.model,n,t.event().item()),ro(n,bi()),r.onItemExecute(n,e,t.event().item(),nl.getValue(n)),Lf.close(e),kE(n)})].concat(r.dismissOnBlur?[mo(fi(),function(n){var t=$y.getCoupled(n,"sandbox");ka(t.element()).isNone()&&Lf.close(t)})]:[]))];return{uid:r.uid,dom:ty(Dn(r,{inputAttributes:{role:"combobox","aria-autocomplete":"list","aria-haspopup":"true"}})),behaviours:N(N({},o),Vs(r.typeaheadBehaviours,a)),eventOrder:r.eventOrder}}}),DE=function(i){return N(N({},i),{toCached:function(){return DE(i.toCached())},bindFuture:function(t){return DE(i.bind(function(n){return n.fold(function(n){return Hy(an.error(n))},function(n){return t(n)})}))},bindResult:function(t){return DE(i.map(function(n){return n.bind(t)}))},mapResult:function(t){return DE(i.map(function(n){return n.map(t)}))},mapError:function(t){return DE(i.map(function(n){return n.mapError(t)}))},foldResult:function(t,e){return i.map(function(n){return n.fold(t,e)})},withTimeout:function(n,r){return DE(Ny(function(t){var e=!1,o=v.setTimeout(function(){e=!0,t(an.error(r()))},n);i.get(function(n){e||(v.clearTimeout(o),t(n))})}))}})},AE=Vk,_E={type:"separator"},ME=Yo("aria-invalid"),FE={bar:Wk(function(n,t){return function(n,t){return{dom:{tag:"div",classes:["tox-bar","tox-form__controls-h-stack"]},components:S(n.items,t.interpreter)}}(n,t.shared)}),collection:Wk(function(n,t){return Uk(n,t.shared.providers)}),alertbanner:Wk(function(n,t){return function(t,n){return Gb.sketch({dom:{tag:"div",attributes:{role:"alert"},classes:["tox-notification","tox-notification--in","tox-notification--"+t.level]},components:[{dom:{tag:"div",classes:["tox-notification__icon"]},components:[Xg.sketch({dom:{tag:"button",classes:["tox-button","tox-button--naked","tox-button--icon"],innerHtml:xm(t.icon,n.icons),attributes:{title:n.translate(t.iconTooltip)}},action:function(n){io(n,fy,{name:"alert-banner",value:t.url})}})]},{dom:{tag:"div",classes:["tox-notification__body"],innerHtml:n.translate(t.text)}}]})}(n,t.shared.providers)}),input:Wk(function(n,t){return function(n,t){return _k({name:n.name,multiline:!1,label:n.label,inputMode:n.inputMode,placeholder:n.placeholder,flex:!1,disabled:n.disabled,classname:"tox-textfield",validation:on.none(),maximized:n.maximized},t)}(n,t.shared.providers)}),textarea:Wk(function(n,t){return function(n,t){return _k({name:n.name,multiline:!0,label:n.label,inputMode:on.none(),placeholder:n.placeholder,flex:!0,disabled:n.disabled,classname:"tox-textarea",validation:on.none(),maximized:n.maximized},t)}(n,t.shared.providers)}),label:Wk(function(n,t){return function(n,t){var e={dom:{tag:"label",innerHtml:t.providers.translate(n.label),classes:["tox-label"]}},o=S(n.items,t.interpreter);return{dom:{tag:"div",classes:["tox-form__group"]},components:[e].concat(o),behaviours:ya([wS(),gg.config({}),DS(on.none()),dg.config({mode:"acyclic"})])}}(n,t.shared)}),iframe:(pE=function(n,t){return Sw(n,t.shared.providers)},function(n,t,e){var o=Dn(t,{source:"dynamic"});return Wk(pE)(n,o,e)}),button:Wk(function(n,t){return KC(n,t.shared.providers)}),checkbox:Wk(function(n,t){return function(e,t){function n(n){return n.element().dom().click(),on.some(!0)}function o(n){return{dom:{tag:"span",classes:["tox-icon","tox-checkbox-icon__"+n],innerHtml:xm("checked"===n?"selected":"unselected",t.icons)}}}var r=nl.config({store:{mode:"manual",getValue:function(n){return n.element().dom().checked},setValue:function(n,t){n.element().dom().checked=t}}}),i=by.parts().field({factory:{sketch:l},dom:{tag:"input",classes:["tox-checkbox__input"],attributes:{type:"checkbox"}},behaviours:ya([wS(),kh.config({disabled:e.disabled}),Xy.config({}),bg.config({}),r,dg.config({mode:"special",onEnter:n,onSpace:n,stopSpaceKeyup:!0}),Jd("checkbox-events",[mo(ri(),function(n,t){io(n,ay,{name:e.name})})])])}),u=by.parts().label({dom:{tag:"span",classes:["tox-checkbox__label"],innerHtml:t.translate(e.label)},behaviours:ya([Tw.config({})])}),a=bm({dom:{tag:"div",classes:["tox-checkbox__icons"]},components:[o("checked"),o("unchecked")]});return by.sketch({dom:{tag:"label",classes:["tox-checkbox"]},components:[i,a.asSpec(),u],fieldBehaviours:ya([kh.config({disabled:e.disabled,disableClass:"tox-checkbox--disabled",onDisabled:function(n){by.getField(n).each(kh.disable)},onEnabled:function(n){by.getField(n).each(kh.enable)}})])})}(n,t.shared.providers)}),colorinput:Wk(function(n,t){return sx(n,t.shared,t.colorinput)}),colorpicker:Wk(function(n){function t(n){return"tox-"+n}var e=xS(hw,t),r=bm(e.sketch({dom:{tag:"div",classes:[t("color-picker-container")],attributes:{role:"presentation"}},onValidHex:function(n){io(n,fy,{name:"hex-valid",value:!0})},onInvalidHex:function(n){io(n,fy,{name:"hex-valid",value:!1})}}));return{dom:{tag:"div"},components:[r.asSpec()],behaviours:ya([nl.config({store:{mode:"manual",getValue:function(n){var t=r.get(n);return nd.getCurrent(t).bind(function(n){return nl.getValue(n).hex}).map(function(n){return"#"+n}).getOr("")},setValue:function(n,t){var e=/^#([a-fA-F0-9]{3}(?:[a-fA-F0-9]{3})?)/.exec(t),o=r.get(n);nd.getCurrent(o).fold(function(){v.console.log("Can not find form")},function(n){nl.setValue(n,{hex:on.from(e[1]).getOr("")}),mS.getField(n,"hex").each(function(n){ro(n,oi())})})}}}),wS()])}}),dropzone:Wk(function(n,t){return yw(n,t.shared.providers)}),grid:Wk(function(n,t){return function(n,t){return{dom:{tag:"div",classes:["tox-form__grid","tox-form__grid--"+n.columns+"col"]},components:S(n.items,t.interpreter)}}(n,t.shared)}),selectbox:Wk(function(n,t){return function(e,t){var n=S(e.items,function(n){return{text:t.translate(n.text),value:n.value}}),o=e.label.map(function(n){return Ay(n,t)}),r=by.parts().field({dom:{},selectAttributes:{size:e.size},options:n,factory:yE,selectBehaviours:ya([kh.config({disabled:e.disabled}),Xy.config({}),Jd("selectbox-change",[mo(ri(),function(n,t){io(n,ay,{name:e.name})})])])}),i=1<e.size?on.none():on.some({dom:{tag:"div",classes:["tox-selectfield__icon-js"],innerHtml:xm("chevron-down",t.icons)}}),u={dom:{tag:"div",classes:["tox-selectfield"]},components:H([[r],i.toArray()])};return by.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:H([o.toArray(),[u]]),fieldBehaviours:ya([kh.config({disabled:e.disabled,onDisabled:function(n){by.getField(n).each(kh.disable)},onEnabled:function(n){by.getField(n).each(kh.enable)}})])})}(n,t.shared.providers)}),sizeinput:Wk(function(n,t){return tk(n,t.shared.providers)}),urlinput:Wk(function(n,t){return jk(n,t,t.urlinput)}),customeditor:Wk(function(e){var o=ye(on.none()),t=bm({dom:{tag:e.tag}}),r=ye(on.none());return{dom:{tag:"div",classes:["tox-custom-editor"]},behaviours:ya([Jd("editor-foo-events",[Ri(function(n){t.getOpt(n).each(function(t){(!function(n){return Object.prototype.hasOwnProperty.call(n,"init")}(e)?OS.load(e.scriptId,e.scriptUrl).then(function(n){return n(t.element().dom(),e.settings)}):e.init(t.element().dom())).then(function(t){r.get().each(function(n){t.setValue(n)}),r.set(on.none()),o.set(on.some(t))})})})]),nl.config({store:{mode:"manual",getValue:function(){return o.get().fold(function(){return r.get().getOr("")},function(n){return n.getValue()})},setValue:function(n,t){o.get().fold(function(){r.set(on.some(t))},function(n){return n.setValue(t)})}}}),wS()]),components:[t.asSpec()]}}),htmlpanel:Wk(function(n){return"presentation"===n.presets?Gb.sketch({dom:{tag:"div",classes:["tox-form__group"],innerHtml:n.html}}):Gb.sketch({dom:{tag:"div",classes:["tox-form__group"],innerHtml:n.html,attributes:{role:"document"}},containerBehaviours:ya([Xy.config({}),bg.config({})])})}),imagetools:Wk(function(n,t){return Ak(n,t.shared.providers)}),table:Wk(function(n,t){return function(n,t){function e(n){return{dom:{tag:"th",innerHtml:t.translate(n)}}}function o(n){return{dom:{tag:"td",innerHtml:t.translate(n)}}}function r(n){return{dom:{tag:"tr"},components:S(n,o)}}var i,u;return{dom:{tag:"table",classes:["tox-dialog__table"]},components:[(u=n.header,{dom:{tag:"thead"},components:[{dom:{tag:"tr"},components:S(u,e)}]}),(i=n.cells,{dom:{tag:"tbody"},components:S(i,r)})],behaviours:ya([Xy.config({}),bg.config({})])}}(n,t.shared.providers)}),panel:Wk(function(n,t){return function(n,t){return{dom:{tag:"div",classes:n.classes},components:S(n.items,t.shared.interpreter)}}(n,t)})},IE={field:function(n,t){return t}},RE=function(t,e,o){return Nn(FE,e.type).fold(function(){return v.console.error('Unknown factory type "'+e.type+'", defaulting to container: ',e),e},function(n){return n(t,e,o)})},VE=nn(function(n,t){!function(n,t){var e=Nu.max(n,t,["margin-left","border-left-width","padding-left","padding-right","border-right-width","margin-right"]);xr(n,"max-width",e+"px")}(n,Math.floor(t))}),NE={valignCentre:[],alignCentre:[],alignLeft:[],alignRight:[],right:[],left:[],bottom:[],top:[]},HE=function(n,t,e){function o(){return we.fromDom(n.getBody())}var r=Jb(n);return{toolbar:function(n,t,e){return e?function(){return{anchor:"node",root:n(),node:on.from(n()),bubble:Ea(-12,-12,NE),layouts:{onRtl:function(){return[hm]},onLtr:function(){return[vm]}},overrides:{maxHeightFunction:kf()}}}:function(){return{anchor:"hotspot",hotspot:t(),bubble:Ea(-12,12,NE),layouts:{onRtl:function(){return[aa]},onLtr:function(){return[ca]}},overrides:{maxHeightFunction:kf()}}}}(o,t,r),toolbarOverflow:function(n){return function(){return{anchor:"hotspot",hotspot:n(),overrides:{maxWidthFunction:VE()},layouts:{onRtl:function(){return[aa,ca]},onLtr:function(){return[ca,aa]}}}}}(e),banner:function(n,t,e){return e?function(){return{anchor:"node",root:n(),node:on.from(n()),layouts:{onRtl:function(){return[Wg]},onLtr:function(){return[Wg]}}}}:function(){return{anchor:"hotspot",hotspot:t(),layouts:{onRtl:function(){return[ic]},onLtr:function(){return[ic]}}}}}(o,t,r),cursor:function(t,n){return function(){return{anchor:"selection",root:n(),getSelection:function(){var n=t.selection.getRng();return on.some(Bc.range(we.fromDom(n.startContainer),n.startOffset,we.fromDom(n.endContainer),n.endOffset))}}}}(n,o),node:function(t){return function(n){return{anchor:"node",root:t(),node:n}}}(o)}},PE=[{title:"Headings",items:[{title:"Heading 1",format:"h1"},{title:"Heading 2",format:"h2"},{title:"Heading 3",format:"h3"},{title:"Heading 4",format:"h4"},{title:"Heading 5",format:"h5"},{title:"Heading 6",format:"h6"}]},{title:"Inline",items:[{title:"Bold",icon:"bold",format:"bold"},{title:"Italic",icon:"italic",format:"italic"},{title:"Underline",icon:"underline",format:"underline"},{title:"Strikethrough",icon:"strike-through",format:"strikethrough"},{title:"Superscript",icon:"superscript",format:"superscript"},{title:"Subscript",icon:"subscript",format:"subscript"},{title:"Code",icon:"code",format:"code"}]},{title:"Blocks",items:[{title:"Paragraph",format:"p"},{title:"Blockquote",format:"blockquote"},{title:"Div",format:"div"},{title:"Pre",format:"pre"}]},{title:"Align",items:[{title:"Left",icon:"align-left",format:"alignleft"},{title:"Center",icon:"align-center",format:"aligncenter"},{title:"Right",icon:"align-right",format:"alignright"},{title:"Justify",icon:"align-justify",format:"alignjustify"}]}],zE=function(n){return O(n,function(n,t){if(function(n){return En(n,"items")}(t)){var e=zE(t.items);return{customFormats:n.customFormats.concat(e.customFormats),formats:n.formats.concat([{title:t.title,items:e.formats}])}}if(function(n){return En(n,"inline")}(t)||function(n){return En(n,"block")}(t)||function(n){return En(n,"selector")}(t)){var o="custom-"+t.title.toLowerCase();return{customFormats:n.customFormats.concat([{name:o,format:t}]),formats:n.formats.concat([{title:t.title,format:o,icon:t.icon}])}}return N(N({},n),{formats:n.formats.concat(t)})},{customFormats:[],formats:[]})},LE=xk.trim,jE=Jk("true"),UE=Jk("false"),WE=function(n){return function(n){for(;n=n.parentNode;){var t=n.contentEditable;if(t&&"inherit"!==t)return jE(n)}return!1}(n)&&!UE(n)},GE=function(n){var t=rO(n);return C(function(n){return S(C(n,tO),eO)}(t).concat(function(n){return S(C(n,Zk),oO)}(t)),iO)},XE="tinymce-url-history",YE=Object.prototype.hasOwnProperty,qE="contexttoolbar-hide",KE=nn([ct("dom"),St("shell",!0),Is("toolbarBehaviours",[gg])]),JE=nn([Cl({name:"groups",overrides:function(n){return{behaviours:ya([gg.config({})])}}})]),$E=_l({name:"Toolbar",configFields:KE(),partFields:JE(),factory:function(t,n,e,o){var r=function(n){return t.shell?on.some(n):Ks(n,t,"groups")},i=t.shell?{behaviours:[gg.config({})],components:[]}:{behaviours:[],components:n};return{uid:t.uid,dom:t.dom,components:i.components,behaviours:Vs(t.toolbarBehaviours,i.behaviours),apis:{setGroups:function(n,t){r(n).fold(function(){throw v.console.error("Toolbar was defined to not be a shell, but no groups container was specified in components"),new Error("Toolbar was defined to not be a shell, but no groups container was specified in components")},function(n){gg.set(n,t)})}},domModification:{attributes:{role:"group"}}}},apis:{setGroups:function(n,t,e){n.setGroups(t,e)}}}),QE=xo("within","extra","withinWidth"),ZE=nn([ct("items"),Yu(["itemSelector"]),Is("tgroupBehaviours",[dg])]),nT=nn([kl({name:"items",unit:"item"})]),tT=_l({name:"ToolbarGroup",configFields:ZE(),partFields:nT(),factory:function(n,t,e,o){return{uid:n.uid,dom:n.dom,components:t,behaviours:Vs(n.tgroupBehaviours,[dg.config({mode:"flow",selector:n.markers.itemSelector})]),domModification:{attributes:{role:"toolbar"}}}}}),eT=nn([Is("splitToolbarBehaviours",[$y]),At("builtGroups",function(){return ye([])})]),oT=nn([Yu(["overflowToggledClass"]),ct("getAnchor"),xt("getOverflowBounds"),ct("lazySink")].concat(eT())),rT=nn([wl({factory:$E,schema:KE(),name:"primary"}),Sl({factory:$E,schema:KE(),name:"overflow",overrides:function(t){return{toolbarBehaviours:ya([dg.config({mode:"cyclic",onEscape:function(n){return Ks(n,t,"overflow-button").each(bg.focus),on.none()}})])}}}),Sl({name:"overflow-button",overrides:function(n){return{dom:{attributes:{"aria-haspopup":"true"}},buttonBehaviours:ya([kg.config({toggleClass:n.markers.overflowToggledClass,aria:{mode:"expanded"},toggleOnExecute:!1})])}}}),Sl({name:"overflow-group"})]),iT=_l({name:"SplitFloatingToolbar",configFields:oT(),partFields:rT(),factory:function(t,n,e,o){return CO(t,n,0,o,{coupling:{sandbox:function(n){return function(o,e){var r=Tu();return{dom:{tag:"div",attributes:{id:r.id()}},behaviours:ya([dg.config({mode:"special",onEscape:function(n){return Lf.close(n),on.some(!0)}}),Lf.config({onOpen:function(n,t){EO(o,e),Ks(o,e,"overflow-button").each(function(n){kg.on(n),r.link(n.element())}),dg.focusIn(t)},onClose:function(){Ks(o,e,"overflow-button").each(function(n){kg.off(n),bg.focus(n),r.unlink(n.element())})},isPartOf:function(n,t,e){return ju(t,e)||ju(o,e)},getAttachPoint:function(){return e.lazySink(o).getOrDie()}}),dc.config({channels:N({},Bs({isExtraPart:nn(!1),doReposition:function(){return TO(o,e)}}))})])}}(n,t)}},apis:{refresh:function(n){return EO(n,t)},toggle:function(n){return function(n,t,e){var o=$y.getCoupled(n,"sandbox");Lf.isOpen(o)?Lf.close(o):Lf.open(o,e.overflow())}(n,0,o)},getOverflow:function(n){return Lf.getState($y.getCoupled(n,"sandbox"))},reposition:function(n){return TO(n,t)}}})},apis:{setGroups:function(n,t,e){n.setGroups(t,e)},refresh:function(n,t){n.refresh(t)},reposition:function(n,t){n.reposition(t)},getMoreButton:function(n,t){return n.getMoreButton(t)},getOverflow:function(n,t){return n.getOverflow(t)},toggle:function(n,t){n.toggle(t)}}}),uT=/* */Object.freeze({refresh:function(n,t,e){if(e.isExpanded()){Or(n.element(),DO(t));var o=AO(t,n.element());xr(n.element(),DO(t),o)}},grow:function(n,t,e){e.isExpanded()||NO(n,t,e)},shrink:function(n,t,e){e.isExpanded()&&VO(n,t,e)},immediateShrink:function(n,t,e){e.isExpanded()&&IO(n,t,e)},hasGrown:function(n,t,e){return e.isExpanded()},hasShrunk:function(n,t,e){return e.isCollapsed()},isGrowing:HO,isShrinking:PO,isTransitioning:function(n,t,e){return!0===HO(n,t)||!0===PO(n,t)},toggleGrow:function(n,t,e){(e.isExpanded()?VO:NO)(n,t,e)},disableTransitions:_O}),aT=/* */Object.freeze({exhibit:function(n,t){var e=t.expanded;return nr(e?{classes:[t.openClass],styles:{}}:{classes:[t.closedClass],styles:q(t.dimension.property,"0px")})},events:function(e,o){return so([yo(ui(),function(n,t){t.event().raw().propertyName===e.dimension.property&&(_O(n,e),o.isExpanded()&&Or(n.element(),e.dimension.property),(o.isExpanded()?e.onGrown:e.onShrunk)(n))})])}}),cT=[ct("closedClass"),ct("openClass"),ct("shrinkingClass"),ct("growingClass"),ht("getAnimationRoot"),Ku("onShrunk"),Ku("onStartShrink"),Ku("onGrown"),Ku("onStartGrow"),St("expanded",!1),st("dimension",it("property",{width:[Zu("property","width"),Zu("getDimension",function(n){return gu(n)+"px"})],height:[Zu("property","height"),Zu("getDimension",function(n){return fu(n)+"px"})]}))],sT=xa({fields:cT,name:"sliding",active:aT,apis:uT,state:/* */Object.freeze({init:function(n){var t=ye(n.expanded);return tu({isExpanded:function(){return!0===t.get()},isCollapsed:function(){return!1===t.get()},setCollapsed:d(t.set,!1),setExpanded:d(t.set,!0),readState:function(){return"expanded: "+t.get()}})}})}),fT=nn([Yu(["closedClass","openClass","shrinkingClass","growingClass","overflowToggledClass"]),Ku("onOpened"),Ku("onClosed")].concat(eT())),lT=nn([wl({factory:$E,schema:KE(),name:"primary"}),wl({factory:$E,schema:KE(),name:"overflow",overrides:function(t){return{toolbarBehaviours:ya([sT.config({dimension:{property:"height"},closedClass:t.markers.closedClass,openClass:t.markers.openClass,shrinkingClass:t.markers.shrinkingClass,growingClass:t.markers.growingClass,onShrunk:function(n){Ks(n,t,"overflow-button").each(function(n){kg.off(n),bg.focus(n)}),t.onClosed(n)},onGrown:function(n){dg.focusIn(n),t.onOpened(n)},onStartGrow:function(n){Ks(n,t,"overflow-button").each(kg.on)}}),dg.config({mode:"acyclic",onEscape:function(n){return Ks(n,t,"overflow-button").each(bg.focus),on.some(!0)}})])}}}),Sl({name:"overflow-button",overrides:function(n){return{buttonBehaviours:ya([kg.config({toggleClass:n.markers.overflowToggledClass,aria:{mode:"pressed"},toggleOnExecute:!1})])}}}),Sl({name:"overflow-group"})]),dT=function(n,t){var e=Ks(n,t,"overflow");SO(n,t,e,zO),e.each(sT.refresh)},mT=_l({name:"SplitSlidingToolbar",configFields:fT(),partFields:lT(),factory:function(t,n,e,o){return CO(t,n,0,o,{coupling:{},apis:{refresh:function(n){return dT(n,t)},toggle:function(n){return function(t,e){Ks(t,e,"overflow").each(function(n){dT(t,e),sT.toggleGrow(n)})}(n,t)},getOverflow:function(n){return Ks(n,t,"overflow")}}})},apis:{setGroups:function(n,t,e){n.setGroups(t,e)},refresh:function(n,t){n.refresh(t)},getMoreButton:function(n,t){return n.getMoreButton(t)},getOverflow:function(n,t){return n.getOverflow(t)},toggle:function(n,t){n.toggle(t)}}}),gT=nn(Yo("toolbar-height-change")),pT="silver.readonly",hT=re([(hE="readonly",st(hE,pe))]),vT=[Tt("disabled",!1),yt("tooltip"),yt("icon"),yt("text"),Bt("onSetup",function(){return Z})],bT=re([ft("type"),dt("onAction")].concat(vT)),yT=[yt("text"),yt("tooltip"),yt("icon"),dt("fetch"),Bt("onSetup",function(){return Z})],xT=re(g([ft("type")],yT)),wT=re([ft("type"),yt("tooltip"),yt("icon"),yt("text"),xt("select"),dt("fetch"),Bt("onSetup",function(){return Z}),Et("presets","normal",["normal","color","listpreview"]),St("columns",1),dt("onAction"),dt("onItemAction")]),ST=[Tt("active",!1)].concat(vT),CT=re(ST.concat([ft("type"),dt("onAction")])),kT=[Bt("predicate",function(){return!1}),Et("scope","node",["node","editor"]),Et("position","selection",["node","selection","line"])],OT=vT.concat([St("type","contextformbutton"),St("primary",!1),dt("onAction"),At("original",l)]),ET=ST.concat([St("type","contextformbutton"),St("primary",!1),dt("onAction"),At("original",l)]),TT=vT.concat([St("type","contextformbutton")]),BT=ST.concat([St("type","contextformtogglebutton")]),DT=it("type",{contextformbutton:OT,contextformtogglebutton:ET}),AT=re([St("type","contextform"),Bt("initValue",function(){return""}),yt("label"),pt("commands",DT),vt("launch",it("type",{contextformbutton:TT,contextformtogglebutton:BT}))].concat(kT)),_T=re([St("type","contexttoolbar"),ft("items")].concat(kT)),MT=/* */Object.freeze({getState:function(n,t,e){return e}}),FT=/* */Object.freeze({events:function(i,u){function o(o,r){i.updateState.each(function(n){var t=n(o,r);u.set(t)}),i.renderComponents.each(function(n){var t=n(r,u.get()),e=S(t,o.getSystem().build);hs(o,e)})}return so([mo(di(),function(n,t){var e=i.channel;vn(t.channels(),e)&&o(n,t.data())}),Ri(function(t,n){i.initialData.each(function(n){o(t,n)})})])}}),IT=/* */Object.freeze({init:function(n){var t=ye(on.none());return{readState:function(){return t.get().fold(function(){return"none"},function(n){return n})},get:function(){return t.get()},set:function(n){return t.set(n)},clear:function(){return t.set(on.none())}}}}),RT=[ct("channel"),ht("renderComponents"),ht("updateState"),ht("initialData")],VT=xa({fields:RT,name:"reflecting",active:FT,apis:MT,state:IT}),NT=nn([ct("toggleClass"),ct("fetch"),$u("onExecute"),St("getHotspot",on.some),St("getAnchorOverrides",nn({})),St("layouts",on.none()),$u("onItemExecute"),ht("lazySink"),ct("dom"),Ku("onOpen"),Is("splitDropdownBehaviours",[$y,dg,bg]),St("matchWidth",!1),St("useMinWidth",!1),St("eventOrder",{}),ht("role")].concat(cx())),HT=wl({factory:Xg,schema:[ct("dom")],name:"arrow",defaults:function(n){return{buttonBehaviours:ya([bg.revoke()])}},overrides:function(t){return{dom:{tag:"span",attributes:{role:"presentation"}},action:function(n){n.getSystem().getByUid(t.uid).each(uo)},buttonBehaviours:ya([kg.config({toggleOnExecute:!1,toggleClass:t.toggleClass})])}}}),PT=wl({factory:Xg,schema:[ct("dom")],name:"button",defaults:function(n){return{buttonBehaviours:ya([bg.revoke()])}},overrides:function(e){return{dom:{tag:"span",attributes:{role:"presentation"}},action:function(t){t.getSystem().getByUid(e.uid).each(function(n){e.onExecute(n,t)})}}}}),zT=nn([HT,PT,Cl({factory:{sketch:function(n){return{uid:n.uid,dom:{tag:"span",styles:{display:"none"},attributes:{"aria-hidden":"true"},innerHtml:n.text}}}},schema:[ct("text")],name:"aria-descriptor"}),Sl({schema:[Xu()],name:"menu",defaults:function(o){return{onExecute:function(t,e){t.getSystem().getByUid(o.uid).each(function(n){o.onItemExecute(n,t,e)})}}}}),Zy()]),LT=_l({name:"SplitDropdown",configFields:NT(),partFields:zT(),factory:function(o,n,t,e){function r(n){nd.getCurrent(n).each(function(n){cd.highlightFirst(n),dg.focusIn(n)})}function i(n){ox(o,function(n){return n},n,e,r,_y.HighlightFirst).get(Z)}function u(n){var t=Js(n,o,"button");return uo(t),on.some(!0)}var a=An(so([Ri(function(e,n){Ks(e,o,"aria-descriptor").each(function(n){var t=Yo("aria");zo(n.element(),"id",t),zo(e.element(),"aria-describedby",t)})})]),im(on.some(i))),c={repositionMenus:function(n){kg.isOn(n)&&ax(n)}};return{uid:o.uid,dom:o.dom,components:n,apis:c,eventOrder:N(N({},o.eventOrder),{"alloy.execute":["disabling","toggling","alloy.base.behaviour"]}),events:a,behaviours:Vs(o.splitDropdownBehaviours,[$y.config({others:{sandbox:function(n){var t=Js(n,o,"arrow");return ux(o,n,{onOpen:function(){kg.on(t),kg.on(n)},onClose:function(){kg.off(t),kg.off(n)}})}}}),dg.config({mode:"special",onSpace:u,onEnter:u,onDown:function(n){return i(n),on.some(!0)}}),bg.config({}),kg.config({toggleOnExecute:!1,aria:{mode:"expanded"}})]),domModification:{attributes:{role:o.role.getOr("button"),"aria-haspopup":!0}}}},apis:{repositionMenus:function(n,t){return n.repositionMenus(t)}}}),jT=Yo("focus-button"),UT=["checklist","ordered-list"],WT=["indent","outdent","table-insert-column-after","table-insert-column-before","unordered-list"],GT=function(n,t,e){return rE(n,{toolbarButtonBehaviours:[].concat(0<e.length?[Jd("toolbarButtonWith",e)]:[]),getApi:nE,onSetup:n.onSetup},t)},XT=function(n,t,e){return Dn(rE(n,{toolbarButtonBehaviours:[gg.config({}),kg.config({toggleClass:"tox-tbtn--enabled",aria:{mode:"pressed"},toggleOnExecute:!1})].concat(0<e.length?[Jd("toolbarToggleButtonWith",e)]:[]),getApi:tE,onSetup:n.onSetup},t))},YT=function(n,t,e){var o=t.label.fold(function(){return{}},function(n){return{"aria-label":n}}),r=bm(xy.sketch({inputClasses:["tox-toolbar-textfield","tox-toolbar-nav-js"],data:t.initValue(),inputAttributes:o,selectOnFocus:!0,inputBehaviours:ya([dg.config({mode:"special",onEnter:function(n){return i.findPrimary(n).map(function(n){return uo(n),!0})},onLeft:function(n,t){return t.cut(),on.none()},onRight:function(n,t){return t.cut(),on.none()}})])})),i=function(t,n,e){var o=S(n,function(n){return bm(aE(t,n,e))});return{asSpecs:function(){return S(o,function(n){return n.asSpec()})},findPrimary:function(e){return Au(n,function(n,t){return n.primary?on.from(o[t]).bind(function(n){return n.getOpt(e)}).filter(b(kh.isDisabled)):on.none()})}}}(r,t.commands,e.shared.providers);return JO({type:n,uid:Yo("context-toolbar"),initGroups:[{title:on.none(),items:[r.asSpec()]},{title:on.none(),items:i.asSpecs()}],onEscape:on.none,cyclicKeying:!0,backstage:e,getSink:function(){return an.error("")}})},qT=function(t,e){function n(n){return n.dom()===e.getBody()}var o=we.fromDom(e.selection.getNode());return fE(o,t.inNodeScope).orThunk(function(){return fE(o,t.inEditorScope).orThunk(function(){return function(n,t,e){for(var o=n.dom(),r=dn(e)?e:nn(!1);o.parentNode;){o=o.parentNode;var i=we.fromDom(o),u=t(i);if(u.isSome())return u;if(r(i))break}return on.none()}(o,function(n){return fE(n,t.inNodeScope)},n)})})},KT=function(e,r){function o(t,e){var o=et(function(n){return tt("ContextForm",AT,n)}(e));(n[t]=o).launch.map(function(n){c["form:"+t]=N(N({},e.launch),{type:"contextformtogglebutton"===n.type?"togglebutton":"button",onAction:function(){r(o)}})}),"editor"===o.scope?a.push(o):u.push(o),s[t]=o}function i(t,e){(function(n){return tt("ContextToolbar",_T,n)})(e).each(function(n){"editor"===e.scope?a.push(n):u.push(n),s[t]=n})}var n={},u=[],a=[],c={},s={},t=wn(e);return bn(t,function(n){var t=e[n];"contextform"===t.type?o(n,t):"contexttoolbar"===t.type&&i(n,t)}),{forms:n,inNodeScope:u,inEditorScope:a,lookupTable:s,formNavigators:c}},JT=Yo("forward-slide"),$T=Yo("backward-slide"),QT=Yo("change-slide-event"),ZT="tox-pop--resizing";(bE=vE=vE||{})[bE.SemiColon=0]="SemiColon",bE[bE.Space=1]="Space";function nB(n,t,e,o){return{type:"basic",data:function(n){return S(n,function(n){var t=n,e=n,o=n.split("=");return 1<o.length&&(t=o[0],e=o[1]),{title:t,format:e}})}(function(n,t){return t===vE.SemiColon?n.replace(/;$/,"").split(";"):n.split(" ")}(Nn(n.settings,t).getOr(e),o))}}function tB(e){function t(n){var t=E(VD,function(n){return e.formatter.match(n.format)}).fold(function(){return"left"},function(n){return n.title.toLowerCase()});io(n,uk,{icon:"align-"+t})}var n=on.some(function(n){return function(){return t(n)}}),o=on.some(function(n){return t(n)}),r=function(n){return{type:"basic",data:n}}(VD);return{tooltip:"Align",icon:on.some("align-left"),isSelectedFor:function(n){return function(){return e.formatter.match(n)}},getCurrentValue:nn(on.none()),getPreviewFor:function(n){return function(){return on.none()}},onAction:dE(e),setInitialValue:o,nodeChangeHandler:n,dataset:r,shouldHide:!1,isInvalid:function(n){return!e.formatter.canApply(n.format)}}}function eB(n){var t=n.split(/\s*,\s*/);return S(t,function(n){return n.replace(/^['"]+|['"]+$/g,"")})}function oB(r){function i(){function e(n){return n?eB(n)[0]:""}var n=r.queryCommandValue("FontName"),t=u.data,o=n?n.toLowerCase():"";return{matchOpt:E(t,function(n){var t=n.format;return t.toLowerCase()===o||e(t).toLowerCase()===e(o).toLowerCase()}).orThunk(function(){return function(n){var t;return 0===n.indexOf("-apple-system")&&(t=eB(n.toLowerCase()),D(ND,function(n){return-1<t.indexOf(n.toLowerCase())}))}(o)?on.from({title:"System Font",format:o}):on.none()}),font:n}}function t(n){var t=i(),e=t.matchOpt,o=t.font,r=e.fold(function(){return o},function(n){return n.title});io(n,ik,{text:r})}var n=on.some(function(n){return function(){return t(n)}}),e=on.some(function(n){return t(n)}),u=nB(r,"font_formats","Andale Mono=andale mono,monospace;Arial=arial,helvetica,sans-serif;Arial Black=arial black,sans-serif;Book Antiqua=book antiqua,palatino,serif;Comic Sans MS=comic sans ms,sans-serif;Courier New=courier new,courier,monospace;Georgia=georgia,palatino,serif;Helvetica=helvetica,arial,sans-serif;Impact=impact,sans-serif;Symbol=symbol;Tahoma=tahoma,arial,helvetica,sans-serif;Terminal=terminal,monaco,monospace;Times New Roman=times new roman,times,serif;Trebuchet MS=trebuchet ms,geneva,sans-serif;Verdana=verdana,geneva,sans-serif;Webdings=webdings;Wingdings=wingdings,zapf dingbats",vE.SemiColon);return{tooltip:"Fonts",icon:on.none(),isSelectedFor:function(t){return function(n){return n.exists(function(n){return n.format===t})}},getCurrentValue:function(){return i().matchOpt},getPreviewFor:function(n){return function(){return on.some({tag:"div",styleAttr:-1===n.indexOf("dings")?"font-family:"+n:""})}},onAction:function(n){return function(){r.undoManager.transact(function(){r.focus(),r.execCommand("FontName",!1,n.format)})}},setInitialValue:e,nodeChangeHandler:n,dataset:u,shouldHide:!1,isInvalid:function(){return!1}}}function rB(n,t){return/[0-9.]+px$/.test(n)?function(n,t){var e=Math.pow(10,t);return Math.round(n*e)/e}(72*parseInt(n,10)/96,t||0)+"pt":n}function iB(e){function i(){var o=on.none(),r=u.data,i=e.queryCommandValue("FontSize");if(i)for(var n=function(n){var t=rB(i,n),e=function(n){return V(HD,n).getOr("")}(t);o=E(r,function(n){return n.format===i||n.format===t||n.format===e})},t=3;o.isNone()&&0<=t;t--)n(t);return{matchOpt:o,size:i}}function t(n){var t=i(),e=t.matchOpt,o=t.size,r=e.fold(function(){return o},function(n){return n.title});io(n,ik,{text:r})}var n=nn(nn(on.none())),o=on.some(function(n){return function(){return t(n)}}),r=on.some(function(n){return t(n)}),u=nB(e,"fontsize_formats","8pt 10pt 12pt 14pt 18pt 24pt 36pt",vE.Space);return{tooltip:"Font sizes",icon:on.none(),isSelectedFor:function(t){return function(n){return n.exists(function(n){return n.format===t})}},getPreviewFor:n,getCurrentValue:function(){return i().matchOpt},onAction:function(n){return function(){e.undoManager.transact(function(){e.focus(),e.execCommand("FontSize",!1,n.format)})}},setInitialValue:r,nodeChangeHandler:o,dataset:u,shouldHide:!1,isInvalid:function(){return!1}}}function uB(e,n,t){var o=n();return Au(t,function(t){return E(o,function(n){return e.formatter.matchNode(t,n.format)})}).orThunk(function(){return e.formatter.match("p")?on.some({title:"Paragraph",format:"p"}):on.none()})}function aB(n){var t=n.selection.getStart(!0)||n.getBody();return n.dom.getParents(t,function(){return!0},n.getBody())}function cB(o){function e(n,t){var e=function(n){return uB(o,function(){return r.data},n)}(n).fold(function(){return"Paragraph"},function(n){return n.title});io(t,ik,{text:e})}var n=on.some(function(t){return function(n){return e(n.parents,t)}}),t=on.some(function(n){var t=aB(o);e(t,n)}),r=nB(o,"block_formats","Paragraph=p;Heading 1=h1;Heading 2=h2;Heading 3=h3;Heading 4=h4;Heading 5=h5;Heading 6=h6;Preformatted=pre",vE.SemiColon);return{tooltip:"Blocks",icon:on.none(),isSelectedFor:function(n){return function(){return o.formatter.match(n)}},getCurrentValue:nn(on.none()),getPreviewFor:function(t){return function(){var n=o.formatter.get(t);return on.some({tag:0<n.length&&(n[0].inline||n[0].block)||"div",styleAttr:o.formatter.getCssText(t)})}},onAction:dE(o),setInitialValue:t,nodeChangeHandler:n,dataset:r,shouldHide:!1,isInvalid:function(n){return!o.formatter.canApply(n.format)}}}function sB(i,n){function e(n,t){var e=function(n){var t=n.items;return t!==undefined&&0<t.length?B(t,e):[{title:n.title,format:n.format}]},o=B(Yk(i),e),r=uB(i,function(){return o},n).fold(function(){return"Paragraph"},function(n){return n.title});io(t,ik,{text:r})}var t=on.some(function(t){return function(n){return e(n.parents,t)}}),o=on.some(function(n){var t=aB(i);e(t,n)});return{tooltip:"Formats",icon:on.none(),isSelectedFor:function(n){return function(){return i.formatter.match(n)}},getCurrentValue:nn(on.none()),getPreviewFor:function(t){return function(){var n=i.formatter.get(t);return n!==undefined?on.some({tag:0<n.length&&(n[0].inline||n[0].block)||"div",styleAttr:i.formatter.getCssText(t)}):on.none()}},onAction:dE(i),setInitialValue:o,nodeChangeHandler:t,shouldHide:i.getParam("style_formats_autohide",!1,"boolean"),isInvalid:function(n){return!i.formatter.canApply(n.format)},dataset:n}}function fB(o,r){return function(n,t){var e=o(n).mapError(function(n){return le(n)}).getOrDie();return r(e,t)}}function lB(n){var t=n.toolbar,e=n.buttons;return!1===t?[]:t===undefined||!0===t?function(e){var n=S(PD,function(n){var t=C(n.items,function(n){return En(e,n)||En(LD,n)});return{name:n.name,items:t}});return C(n,function(n){return 0<n.items.length})}(e):cn(t)?function(n){var t=n.split("|");return S(t,function(n){return{items:n.trim().split(" ")}})}(t):function(n){return h(n,function(n){return En(n,"name")&&En(n,"items")})}(t)?t:(v.console.error("Toolbar type should be string, string[], boolean or ToolbarGroup[]"),[])}function dB(t,e,o,r,n){return V(e,o.toLowerCase()).orThunk(function(){return n.bind(function(n){return Au(n,function(n){return V(e,n+o.toLowerCase())})})}).fold(function(){return V(LD,o.toLowerCase()).map(function(n){return n(t,r)}).orThunk(function(){return on.none()})},function(n){return function(t,e){return V(zD,t.type).fold(function(){return v.console.error("skipping button defined by",t),on.none()},function(n){return on.some(n(t,e))})}(n,r)})}function mB(e,o,r,i){var n=lB(o),t=S(n,function(n){var t=B(n.items,function(n){return 0===n.trim().length?[]:dB(e,o.buttons,n,r,i).toArray()});return{title:on.from(e.translate(n.name)),items:t}});return C(t,function(n){return 0<n.items.length})}function gB(e){return(Cr(e,"position").is("fixed")?on.none():Bo(e)).orThunk(function(){var n=we.fromTag("span");_o(e,n);var t=Bo(n);return zi(n),t}).map(mu).getOrThunk(function(){return Ru(0,0)})}function pB(t){return function(n){return n.translate(-t.left(),-t.top())}}function hB(t){return function(n){return n.translate(t.left(),t.top())}}function vB(e){return function(n,t){return O(e,function(n,t){return t(n)},Ru(n,t))}}function bB(n,t,e){return n.fold(vB([hB(e),pB(t)]),vB([pB(t)]),vB([]))}function yB(n,t,e){return n.fold(vB([hB(e)]),vB([]),vB([hB(t)]))}function xB(n,t,e){return n.fold(vB([]),vB([pB(e)]),vB([hB(t),pB(e)]))}function wB(n,t,e){return n.fold(function(n,t){return{position:"absolute",left:n+"px",top:t+"px"}},function(n,t){return{position:"absolute",left:n-e.left()+"px",top:t-e.top()+"px"}},function(n,t){return{position:"fixed",left:n+"px",top:t+"px"}})}function SB(n,i,u,a){function t(o,r){return function(n,t){var e=o(i,u,a);return r(n.getOr(e.left()),t.getOr(e.top()))}}return n.fold(t(xB,nA.offset),t(yB,nA.absolute),t(bB,nA.fixed))}function CB(n,t){var e=n.element();lr(e,t.transitionClass),mr(e,t.fadeOutClass),lr(e,t.fadeInClass),t.onShow(n)}function kB(n,t){var e=n.element();lr(e,t.transitionClass),mr(e,t.fadeInClass),lr(e,t.fadeOutClass),t.onHide(n)}function OB(n,t,e){return D(n,function(n){switch(n){case"bottom":return function(n,t){return n.bottom()<=t.bottom()}(t,e);case"top":return function(n,t){return n.y()>=t.y()}(t,e)}})}function EB(n,t){return jo(n,t)?on.some(parseInt(Lo(n,t),10)):on.none()}function TB(r,n){return EB(r,n.leftAttr).bind(function(o){return EB(r,n.topAttr).map(function(n){var t=gu(r),e=fu(r);return xu(o,n,t,e)})})}function BB(n,t,e){var o=Lo(n,t.positionAttr);switch(function(n,t){Uo(n,t.leftAttr),Uo(n,t.topAttr),Uo(n,t.positionAttr)}(n,t),o){case"static":return on.some(rA["static"]());case"absolute":return on.some(rA.absolute(e.x(),e.y()));default:return on.none()}}function DB(n,t,e,o,r){var i=wu(n);if(OB(t.modes,i,e))return on.none();var u=r(),a=Sr(n,"position");!function(n,t,e,o,r){zo(n,t.leftAttr,e),zo(n,t.topAttr,o),zo(n,t.positionAttr,r)}(n,t,i.x(),i.y(),a);var c=eA(i.x(),i.y()),s=bB(c,o,u),f=eA(e.x(),e.y()),l=bB(f,o,u),d=i.y()<=e.y()?l.top():l.top()+e.height()-i.height();return on.some(rA.fixed(s.left(),d))}function AB(n,t,e,o,r){var i=n.element();return Cr(i,"position").is("fixed")?function(t,e,o){return TB(t,e).filter(function(n){return OB(e.modes,n,o)}).bind(function(n){return BB(t,e,n)})}(i,t,e):DB(i,t,e,o,r)}function _B(t,n){bn(["left","top","position"],function(n){return Or(t.element(),n)}),n.onUndocked(t)}function MB(n,t,e,o,r){var i=wB(r,0,o);wr(n.element(),i),("fixed"===i.position?t.onDocked:t.onUndocked)(n)}function FB(o,n,r,i,u){void 0===u&&(u=!1),n.contextual.each(function(e){e.lazyContext(o).each(function(n){var t=function(n,t){return n.y()<t.bottom()&&n.bottom()>t.y()}(n,i);t!==r.isVisible()&&(r.setVisible(t),u&&!t?(pr(o.element(),[e.fadeOutClass]),e.onHide(o)):(t?CB:kB)(o,e))})})}function IB(r,i,n){var u=r.element();n.setDocked(!1),function(n,t){var e=n.element();return TB(e,t).bind(function(n){return BB(e,t,n)})}(r,i).each(function(n){n.fold(function(){return _B(r,i)},function(n,t){var e=ko(u),o=(hu(e),gB(u));MB(r,i,0,o,eA(n,t))},Z)}),n.setVisible(!0),i.contextual.each(function(n){hr(u,[n.fadeInClass,n.fadeOutClass,n.transitionClass]),n.onShow(r)}),iA(r,i,n)}function RB(n,t,e){e.isDocked()&&IB(n,t,e)}function VB(o){var r=o.element();To(r).each(function(n){if(sA.isDocked(o)){var t=gu(n);xr(r,"width",t+"px");var e=lu(r);xr(n,"padding-top",e+"px")}else Or(r,"width"),Or(n,"padding-top")})}function NB(n,t){t?(mr(n,fA.fadeOutClass),pr(n,[fA.transitionClass,fA.fadeInClass])):(mr(n,fA.fadeInClass),pr(n,[fA.fadeOutClass,fA.transitionClass]))}function HB(n,t){var e=we.fromDom(n.getContainer());t?(lr(e,lA),mr(e,dA)):(lr(e,dA),mr(e,lA))}function PB(i,e){function o(t){e().each(function(n){return t(n.element())})}function n(n){i.inline||VB(n),HB(i,sA.isDocked(n)),n.getSystem().broadcastOn([Uf()],{}),e().each(function(n){return n.getSystem().broadcastOn([Uf()],{})})}var r=ye(on.none()),t=i.inline?[]:function(){var n;return[dc.config({channels:(n={},n[gT()]={onReceive:VB},n)})]}();return g([bg.config({}),sA.config({leftAttr:"data-dock-left",topAttr:"data-dock-top",positionAttr:"data-dock-pos",contextual:N({lazyContext:function(n){var t=lu(n.element()),e=i.inline?i.getContentAreaContainer():i.getContainer(),o=wu(we.fromDom(e)),r=o.height()-t;return on.some(xu(o.x(),o.y(),o.width(),r))},onShow:function(){o(function(n){return NB(n,!0)})},onShown:function(t){o(function(n){return hr(n,[fA.transitionClass,fA.fadeInClass])}),r.get().each(function(n){!function(t,e){var o=ko(e);Ca(o).filter(function(n){return!jt(e,n)}).filter(function(n){return jt(n,we.fromDom(o.dom().body))||qe(t,n)}).each(function(){return Sa(e)})}(t.element(),n),r.set(on.none())})},onHide:function(n){r.set(function(n,t){return ka(n).orThunk(function(){return t().toOption().bind(function(n){return ka(n.element())})})}(n.element(),e)),o(function(n){return NB(n,!1)})},onHidden:function(){o(function(n){return hr(n,[fA.transitionClass])})}},fA),modes:["top"],onDocked:n,onUndocked:n})],t)}function zB(n){return"<alloy.field."+n+">"}function LB(n){return{element:function(){return n.element().dom()}}}function jB(e,o){var r=S(wn(o),function(n){var t=o[n],e=et(function(n){return tt("sidebar",kA,n)}(t));return{name:n,getApi:LB,onSetup:e.onSetup,onShow:e.onShow,onHide:e.onHide}});return S(r,function(n){var t=ye(Z);return e.slot(n.name,{dom:{tag:"div",classes:["tox-sidebar__pane"]},behaviours:$p([Tp(n,t),Bp(n,t),mo(Di(),function(t,n){var e=n.event();E(r,function(n){return n.name===e.name()}).each(function(n){(e.visible()?n.onShow:n.onHide)(n.getApi(t))})})])})})}function UB(n,t){nd.getCurrent(n).each(function(n){return gg.set(n,[function(t){return CA.sketch(function(n){return{dom:{tag:"div",classes:["tox-sidebar__pane-container"]},components:jB(n,t),slotBehaviours:$p([Ri(function(n){return CA.hideAllSlots(n)})])}})}(t)])})}function WB(n){return nd.getCurrent(n).bind(function(n){return sT.isGrowing(n)||sT.hasGrown(n)?nd.getCurrent(n).bind(function(t){return E(CA.getSlotNames(t),function(n){return CA.isShowing(t,n)})}):on.none()})}function GB(n,t,e){var o=n.element();!0===t?(gg.set(n,[function(n){return{dom:{tag:"div",attributes:{"aria-label":n.translate("Loading...")},classes:["tox-throbber__busy-spinner"]},components:[{dom:sp('<div class="tox-spinner"><div></div><div></div><div></div></div>')}],behaviours:ya([dg.config({mode:"special",onTab:function(){return on.some(!0)},onShiftTab:function(){return on.some(!0)}}),bg.config({})])}}(e)]),Or(o,"display"),Uo(o,"aria-hidden")):(gg.set(n,[]),xr(o,"display","none"),zo(o,"aria-hidden","true"))}function XB(n){return"string"==typeof n?n.split(" "):n}function YB(e,o){var r=An(RA,o.menus),t=0<wn(o.menus).length,n=o.menubar===undefined||!0===o.menubar?XB("file edit view insert format tools table help"):XB(!1===o.menubar?"":o.menubar),i=C(n,function(n){return t&&o.menus.hasOwnProperty(n)&&o.menus[n].hasOwnProperty("items")||RA.hasOwnProperty(n)}),u=S(i,function(n){var t=r[n];return function(n,e,t){var o=function(n){return n.getParam("removed_menuitems","")}(t).split(/[ ,]/);return{text:n.title,getItems:function(){return B(n.items,function(n){var t=n.toLowerCase();return 0===t.trim().length?[]:x(o,function(n){return n===t})?[]:"separator"===t||"|"===t?[{type:"separator"}]:e.menuItems[t]?[e.menuItems[t]]:[]})}}}({title:t.title,items:XB(t.items)},o,e)});return C(u,function(n){return 0<n.getItems().length&&x(n.getItems(),function(n){return"separator"!==n.type})})}function qB(n,t){var e,o=function(n){var t=n.settings,e=t.skin,o=t.skin_url;if(!1!==e){var r=e||"oxide";o=o?n.documentBaseURI.toAbsolute(o):Xb.baseURL+"/skins/ui/"+r}return o}(t);o&&(e=o+"/skin.min.css",t.contentCSS.push(o+(n?"/content.inline":"/content")+".min.css")),!1===function(n){return!1===n.getParam("skin")}(t)&&e?Vh.DOM.styleSheetLoader.load(e,VA(t)):VA(t)()}function KB(t,n,e,o){var r=n.outerContainer,i=e.toolbar,u=e.buttons;if(h(i,cn)){var a=i.map(function(n){return mB(t,{toolbar:n,buttons:u},{backstage:o},on.none())});IA.setToolbars(r,a)}else IA.setToolbar(r,mB(t,e,{backstage:o},on.none()))}function JB(n){return function(n){var t=Ib(n),e=Nb(n),o=Pb(n);return WA(t).map(function(n){return UA(n,e,o)})}(n).getOr(Ib(n))}function $B(n){var t=Rb(n),e=Vb(n),o=Hb(n);return WA(t).map(function(n){return UA(n,e,o)})}function QB(n,t){return function(){n.execCommand("mceToggleFormat",!1,t)}}function ZB(n){!function(e){xk.each([{name:"bold",text:"Bold",icon:"bold"},{name:"italic",text:"Italic",icon:"italic"},{name:"underline",text:"Underline",icon:"underline"},{name:"strikethrough",text:"Strikethrough",icon:"strike-through"},{name:"subscript",text:"Subscript",icon:"subscript"},{name:"superscript",text:"Superscript",icon:"superscript"}],function(n,t){e.ui.registry.addToggleButton(n.name,{tooltip:n.text,icon:n.icon,onSetup:lE(e,n.name),onAction:QB(e,n.name)})});for(var n=1;n<=6;n++){var t="h"+n;e.ui.registry.addToggleButton(t,{text:t.toUpperCase(),tooltip:"Heading "+n,onSetup:lE(e,t),onAction:QB(e,t)})}}(n),function(t){xk.each([{name:"cut",text:"Cut",action:"Cut",icon:"cut"},{name:"copy",text:"Copy",action:"Copy",icon:"copy"},{name:"paste",text:"Paste",action:"Paste",icon:"paste"},{name:"help",text:"Help",action:"mceHelp",icon:"help"},{name:"selectall",text:"Select all",action:"SelectAll",icon:"select-all"},{name:"newdocument",text:"New document",action:"mceNewDocument",icon:"new-document"},{name:"removeformat",text:"Clear formatting",action:"RemoveFormat",icon:"remove-formatting"},{name:"remove",text:"Remove",action:"Delete",icon:"remove"}],function(n){t.ui.registry.addButton(n.name,{tooltip:n.text,icon:n.icon,onAction:function(){return t.execCommand(n.action)}})})}(n),function(t){xk.each([{name:"blockquote",text:"Blockquote",action:"mceBlockQuote",icon:"quote"}],function(n){t.ui.registry.addToggleButton(n.name,{tooltip:n.text,icon:n.icon,onAction:function(){return t.execCommand(n.action)},onSetup:lE(t,n.name)})})}(n)}function nD(n,t,e){function o(){return!!t.undoManager&&t.undoManager[e]()}function r(){n.setDisabled(t.readonly||!o())}return n.setDisabled(!o()),t.on("Undo Redo AddUndo TypingUndo ClearUndos SwitchMode",r),function(){return t.off("Undo Redo AddUndo TypingUndo ClearUndos SwitchMode",r)}}function tD(n,t){return{anchor:"makeshift",x:n,y:t}}function eD(n){return"longpress"===n.type||0===n.type.indexOf("touch")}function oD(n,t){var e=Vh.DOM.getPos(n);return function(n,t,e){return tD(n.x+t,n.y+e)}(t,e.x,e.y)}function rD(n,t){return"contextmenu"===t.type?n.inline?function(n){if(eD(n)){var t=n.touches[0];return tD(t.pageX,t.pageY)}return tD(n.pageX,n.pageY)}(t):oD(n.getContentAreaContainer(),function(n){if(eD(n)){var t=n.touches[0];return tD(t.clientX,t.clientY)}return tD(n.clientX,n.clientY)}(t)):n_(n)}function iD(n){return{anchor:"node",node:on.some(we.fromDom(n.selection.getNode())),root:we.fromDom(n.getBody())}}function uD(n,t,e,o,r,i){var u=e(),a=function(n,t,e){return e?iD(n):rD(n,t)}(n,t,i);HC(u,Ih.CLOSE_ON_EXECUTE,o,!1).map(function(n){t.preventDefault(),Ug.showMenuAt(r,a,{menu:{markers:Sv("normal")},data:n})})}function aD(t,e,n,o,r,i){var u=function(n,t){var e=t?iD(n):n_(n);return N({bubble:Ea(0,12,e_),layouts:t_,overrides:{maxWidthFunction:VE(),maxHeightFunction:kf()}},e)}(t,i);HC(n,Ih.CLOSE_ON_EXECUTE,o,!0).map(function(n){e.preventDefault(),Ug.showMenuWithinBounds(r,u,{menu:{markers:Sv("normal")},data:n,type:"horizontal"},function(){return on.some(sE(t))}),t.fire(qE)})}function cD(t,e,o,r,i,u){function n(){var n=o();aD(t,e,n,r,i,u)}var a=Ht(),c=a.os.isiOS(),s=a.os.isOSX(),f=a.os.isAndroid();if(!s&&!c||u)f&&!u&&t.selection.setCursorLocation(e.target,0),n();else{var l=function(){!function(n){function t(){Kg.setEditorTimeout(n,function(){n.selection.setRng(e)},10),i()}var e=n.selection.getRng();n.once("touchend",t);function o(n){n.preventDefault(),n.stopImmediatePropagation()}n.on("mousedown",o,!0);function r(){return i()}n.once("longpresscancel",r);var i=function(){n.off("touchend",t),n.off("longpresscancel",r),n.off("mousedown",o)}}(t),n()};!function(n,t){var e=n.selection;if(e.isCollapsed()||t.touches.length<1)return!1;var o=t.touches[0],r=e.getRng();return Wc(n.getWin(),Bc.domRange(r)).exists(function(n){return n.left()<=o.clientX&&n.right()>=o.clientX&&n.top()<=o.clientY&&n.bottom()>=o.clientY})}(t,e)?(t.once("selectionchange",l),t.once("touchend",function(){return t.off("selectionchange",l)})):l()}}function sD(n){return"string"==typeof n?n.split(/[ ,]/):n}function fD(n){return cn(n)?"|"===n:"separator"===n.type}function lD(n,t){if(0===t.length)return n;var e=F(n).filter(function(n){return!fD(n)}).fold(function(){return[]},function(n){return[u_]});return n.concat(e).concat(t).concat([u_])}function dD(i,n,t){function e(n){return Ug.hide(a)}function o(o){if(o_(i)&&o.preventDefault(),!function(n,t){return t.ctrlKey&&!o_(n)}(i,o)&&!i_(i)){var r=function(n,t){return"longpress"!==t.type&&(2!==t.button||t.target===n.getBody()&&""===t.pointerType)}(i,o);(u()?cD:uD)(i,o,function(){var n=r?i.selection.getStart(!0):o.target,t=i.ui.registry.getAll(),e=r_(i);return function(r,n,i){var t=O(n,function(n,t){if(En(r,t)){var e=r[t].update(i);if(cn(e))return lD(n,e.split(" "));if(0<e.length){var o=S(e,a_);return lD(n,o)}return n}return n.concat([t])},[]);return 0<t.length&&fD(t[t.length-1])&&t.pop(),t}(t.contextMenus,e,n)},t,a,r)}}var u=Ht().deviceType.isTouch,a=au(Ug.sketch({dom:{tag:"div"},lazySink:n,onEscape:function(){return i.focus()},onShow:function(){return t.setContextMenuState(!0)},onHide:function(){return t.setContextMenuState(!1)},fireDismissalEventInstead:{},inlineBehaviours:ya([Jd("dismissContextMenu",[mo(Ei(),function(n,t){Lf.close(n),i.focus()})])])}));i.on("init",function(){var n="ResizeEditor ScrollContent ScrollWindow longpresscancel"+(u()?"":" ResizeWindow");i.on(n,e),i.on("longpress contextmenu",o)})}function mD(n,t){n.getSystem().addToGui(t),function(n){To(n.element()).filter(Pr).each(function(t){Cr(t,"z-index").each(function(n){zo(t,c_,n)}),xr(t,"z-index",Sr(n.element(),"z-index"))})}(t)}function gD(n){!function(n){To(n.element()).filter(Pr).each(function(n){var t=Lo(n,c_);jo(n,c_)?xr(n,"z-index",t):Or(n,"z-index"),Uo(n,c_)})}(n),n.getSystem().removeFromGui(n)}function pD(n,t,e){return n.getSystem().build(Gb.sketch({dom:{styles:{left:"0px",top:"0px",width:"100%",height:"100%",position:"fixed","z-index":"1000000000000000"},classes:[t]},events:e}))}function hD(n,t,e,o){return function(n,t){var e=n.element(),o=parseInt(Lo(e,t.leftAttr),10),r=parseInt(Lo(e,t.topAttr),10);return isNaN(o)||isNaN(r)?on.none():on.some(Ru(o,r))}(n,t).fold(function(){return e},function(n){return oA(n.left()+o.left(),n.top()+o.top())})}function vD(n,t,e,o,r,i){var u=hD(n,t,e,o),a=t.mustSnap?l_(n,t,u,r,i):d_(n,t,u,r,i),c=bB(u,r,i);return function(n,t,e){var o=n.element();zo(o,t.leftAttr,e.left()+"px"),zo(o,t.topAttr,e.top()+"px")}(n,t,c),a.fold(function(){return{coord:oA(c.left(),c.top()),extra:on.none()}},function(n){return{coord:n.output(),extra:n.extra()}})}function bD(n,t){!function(n,t){var e=n.element();Uo(e,t.leftAttr),Uo(e,t.topAttr)}(n,t)}function yD(n,e,o,r){return Au(n,function(n){var t=n.sensor();return function(n,t,e,o,r,i){var u=yB(n,r,i),a=yB(t,r,i);return Math.abs(u.left()-a.left())<=e&&Math.abs(u.top()-a.top())<=o}(e,t,n.range().left(),n.range().top(),o,r)?on.some({output:nn(SB(n.output(),e,o,r)),extra:n.extra}):on.none()})}function xD(t){return function(n,t,e,o){return n.isSome()&&t.isSome()&&e.isSome()?on.some(o(n.getOrDie(),t.getOrDie(),e.getOrDie())):on.none()}(Cr(t,"left"),Cr(t,"top"),Cr(t,"position"),function(n,t,e){return("fixed"===e?oA:tA)(parseInt(n,10),parseInt(t,10))}).getOrThunk(function(){var n=mu(t);return eA(n.left(),n.top())})}function wD(e,n,o,r,i,u,t){return function(n,t,e,o,r){var i=r.bounds,u=yB(t,e,o),a=as(u.left(),i.x(),i.x()+i.width()-r.width),c=as(u.top(),i.y(),i.y()+i.height()-r.height),s=eA(a,c);return t.fold(function(){var n=xB(s,e,o);return tA(n.left(),n.top())},function(){return s},function(){var n=bB(s,e,o);return oA(n.left(),n.top())})}(0,n.fold(function(){var n=function(n,e,o){return n.fold(function(n,t){return nA.offset(n+e,t+o)},function(n,t){return nA.absolute(n+e,t+o)},function(n,t){return nA.fixed(n+e,t+o)})}(o,u.left(),u.top()),t=bB(n,r,i);return oA(t.left(),t.top())},function(t){var n=vD(e,t,o,u,r,i);return n.extra.each(function(n){t.onSensor(e,n)}),n.coord}),r,i,t)}function SD(n,t){return{bounds:n.getBounds(),height:lu(t.element()),width:pu(t.element())}}function CD(t,e,n,o,r){var i=n.update(o,r),u=n.getStartData().getOrThunk(function(){return SD(e,t)});i.each(function(n){!function(n,t,e,o){var r=t.getTarget(n.element());if(t.repositionTarget){var i=ko(n.element()),u=hu(i),a=gB(r),c=xD(r),s=wD(n,t.snaps,c,u,a,o,e),f=wB(s,0,a);wr(r,f)}t.onDrag(n,r,o)}(t,e,u,n)})}function kD(t,n,e,o){n.each(gD),e.snaps.each(function(n){bD(t,n)});var r=e.getTarget(t.element());o.reset(),e.onDrop(t,r)}function OD(n){return function(t,e){function o(n){e.setStartData(SD(t,n))}return so(g([mo(Si(),function(n){e.getStartData().each(function(){return o(n)})})],n(t,e,o)))}}function ED(u,a,c){return[mo(qr(),function(t,n){if(0===n.event().raw().button){n.stop();var e=function(){return kD(t,on.some(i),u,a)},o=vb(e,200),r={drop:e,delayDrop:o.schedule,forceDrop:e,move:function(n){o.cancel(),CD(t,u,a,m_,n)}},i=pD(t,u.blockerClass,function(e){return so([mo(qr(),e.forceDrop),mo($r(),e.drop),mo(Kr(),function(n,t){e.move(t.event())}),mo(Jr(),e.delayDrop)])}(r));c(t),mD(t,i)}})]}function TD(i,u,a){var c=ye(on.none());return[mo(Wr(),function(t,n){n.stop();function e(){kD(t,c.get(),i,u),c.set(on.none())}var o={drop:e,delayDrop:function(){},forceDrop:e,move:function(n){CD(t,i,u,p_,n)}},r=pD(t,i.blockerClass,function(e){return so([mo(Wr(),e.forceDrop),mo(Xr(),e.drop),mo(Yr(),e.drop),mo(Gr(),function(n,t){e.move(t.event())})])}(o));c.set(on.some(r));a(t),mD(t,r)}),mo(Gr(),function(n,t){t.stop(),CD(n,i,u,p_,t.event())}),mo(Xr(),function(n,t){t.stop(),kD(n,c.get(),i,u),c.set(on.none())}),mo(Yr(),function(n){kD(n,c.get(),i,u),c.set(on.none())})]}function BD(n,r,i,u,t,e){return n.fold(function(){return S_.snap({sensor:eA(i-20,u-20),range:Ru(t,e),output:eA(on.some(i),on.some(u)),extra:{td:r}})},function(n){var t=i-20,e=u-20,o=n.element().dom().getBoundingClientRect();return S_.snap({sensor:eA(t,e),range:Ru(40,40),output:eA(on.some(i-o.width/2),on.some(u-o.height/2)),extra:{td:r}})})}function DD(n,o,r){return{getSnapPoints:n,leftAttr:"data-drag-left",topAttr:"data-drag-top",onSensor:function(n,t){var e=t.td;!function(n,t){return n.exists(function(n){return jt(n,t)})}(o.get(),e)&&(o.set(on.some(e)),r(e))},mustSnap:!0}}function AD(n){return bm(Xg.sketch({dom:{tag:"div",classes:["tox-selector"]},buttonBehaviours:ya([S_.config({mode:"mouseOrTouch",blockerClass:"blocker",snaps:n}),Tw.config({})]),eventOrder:{mousedown:["dragging","alloy.base.behaviour"],touchstart:["dragging","alloy.base.behaviour"]}}))}var _D,MD,FD,ID,RD,VD=[{title:"Left",icon:"align-left",format:"alignleft"},{title:"Center",icon:"align-center",format:"aligncenter"},{title:"Right",icon:"align-right",format:"alignright"},{title:"Justify",icon:"align-justify",format:"alignjustify"}],ND=["-apple-system","Segoe UI","Roboto","Helvetica Neue","sans-serif"],HD={"8pt":"1","10pt":"2","12pt":"3","14pt":"4","18pt":"5","24pt":"6","36pt":"7"},PD=[{name:"history",items:["undo","redo"]},{name:"styles",items:["styleselect"]},{name:"formatting",items:["bold","italic"]},{name:"alignment",items:["alignleft","aligncenter","alignright","alignjustify"]},{name:"indentation",items:["outdent","indent"]},{name:"permanent pen",items:["permanentpen"]},{name:"comments",items:["addcomment"]}],zD={button:fB($O,function(n,t){return function(n,t){return GT(n,t,[])}(n,t.backstage.shared.providers)}),togglebutton:fB(ZO,function(n,t){return function(n,t){return XT(n,t,[])}(n,t.backstage.shared.providers)}),menubutton:fB(QO,function(n,t){return zC(n,"tox-tbtn",t.backstage,on.none())}),splitbutton:fB(function(n){return tt("SplitButton",wT,n)},function(n,t){return iE(n,t.backstage.shared)}),styleSelectButton:function(n,t){return function(n,t){var e=N({type:"advanced"},t.styleselect);return gE(n,t,sB(n,e))}(n,t.backstage)},fontsizeSelectButton:function(n,t){return function(n,t){return gE(n,t,iB(n))}(n,t.backstage)},fontSelectButton:function(n,t){return function(n,t){return gE(n,t,oB(n))}(n,t.backstage)},formatButton:function(n,t){return function(n,t){return gE(n,t,cB(n))}(n,t.backstage)},alignMenuButton:function(n,t){return function(n,t){return gE(n,t,tB(n))}(n,t.backstage)}},LD={styleselect:zD.styleSelectButton,fontsizeselect:zD.fontsizeSelectButton,fontselect:zD.fontSelectButton,formatselect:zD.formatButton,align:zD.alignMenuButton},jD={valignCentre:[],alignCentre:[],alignLeft:["tox-pop--align-left"],alignRight:["tox-pop--align-right"],right:["tox-pop--right"],left:["tox-pop--left"],bottom:["tox-pop--bottom"],top:["tox-pop--top"]},UD={maxHeightFunction:kf(),maxWidthFunction:VE()},WD={onLtr:function(){return[rc,ic,sa,aa,fa,ca,Wg,Gg,hm,gm,vm,pm]},onRtl:function(){return[rc,ic,fa,ca,sa,aa,Wg,Gg,vm,pm,hm,gm]}},GD={onLtr:function(){return[ic,aa,ca,sa,fa,rc,Wg,Gg,hm,gm,vm,pm]},onRtl:function(){return[ic,ca,aa,fa,sa,rc,Wg,Gg,vm,pm,hm,gm]}},XD=function(u,n,e,a){function c(){return sE(u)}function s(){if(l()&&a.backstage.isContextMenuOpen())return!0;var n=function(){var n=g.get().map(function(n){return n.getBoundingClientRect()}).getOrThunk(function(){return u.selection.getRng().getBoundingClientRect()}),t=u.inline?hu().top():Su(we.fromDom(u.getBody())).y();return{y:n.top+t,bottom:n.bottom+t}}(),t=c();return!function(n,t,e,o){return Math.max(n,e)<=Math.min(t,o)}(n.y,n.bottom,t.y(),t.bottom())}function t(){Ug.hide(d)}function o(){m.get().each(function(n){var t=d.element();Or(t,"display"),s()?xr(t,"display","none"):_f.positionWithinBounds(e,n,d,on.some(c()))})}function f(n){return{dom:{tag:"div",classes:["tox-pop__dialog"]},components:[n],behaviours:ya([dg.config({mode:"acyclic"}),Jd("pop-dialog-wrap-events",[Ri(function(n){u.shortcuts.add("ctrl+F9","focus statusbar",function(){return dg.focusIn(n)})}),Vi(function(n){u.shortcuts.remove("ctrl+F9")})])])}}var l=Ht().deviceType.isTouch,d=au(function(n){var e=ye([]);return Ug.sketch({dom:{tag:"div",classes:["tox-pop"]},fireDismissalEventInstead:{event:"doNotDismissYet"},onShow:function(n){e.set([]),Ug.getContent(n).each(function(n){Or(n.element(),"visibility")}),mr(n.element(),ZT),Or(n.element(),"width")},inlineBehaviours:ya([Jd("context-toolbar-events",[yo(ui(),function(n,t){Ug.getContent(n).each(function(n){}),mr(n.element(),ZT),Or(n.element(),"width")}),mo(QT,function(t,e){Or(t.element(),"width");var n=gu(t.element());Ug.setContent(t,e.event().contents()),lr(t.element(),ZT);var o=gu(t.element());xr(t.element(),"width",n+"px"),Ug.getContent(t).each(function(n){e.event().focus().bind(function(n){return Sa(n),ka(t.element())}).orThunk(function(){return dg.focusIn(n),Ca()})}),Kg.setTimeout(function(){xr(t.element(),"width",o+"px")},0)}),mo(JT,function(n,t){Ug.getContent(n).each(function(n){e.set(e.get().concat([{bar:n,focus:Ca()}]))}),io(n,QT,{contents:t.event().forwardContents(),focus:on.none()})}),mo($T,function(t,n){F(e.get()).each(function(n){e.set(e.get().slice(0,e.get().length-1)),io(t,QT,{contents:cu(n.bar),focus:n.focus})})})]),dg.config({mode:"special",onEscape:function(t){return F(e.get()).fold(function(){return n.onEscape()},function(n){return ro(t,$T),on.some(!0)})}})]),lazySink:function(){return an.value(n.sink)}})}({sink:e,onEscape:function(){return u.focus(),on.some(!0)}})),m=ye(on.none()),g=ye(on.none()),r=ye(null),p=L(function(){return KT(n,function(n){var t=h(n);io(d,JT,{forwardContents:f(t)})})}),h=function(n){var t,e,o=u.ui.registry.getAll().buttons,r=qb(u)===Ub.scrolling?Ub.scrolling:Ub["default"],i=p();return"contexttoolbar"===n.type?(t=An(o,i.formNavigators),e=mB(u,{buttons:t,toolbar:n.items},a,on.some(["form:"])),JO({type:r,uid:Yo("context-toolbar"),initGroups:e,onEscape:on.none,cyclicKeying:!0,backstage:a.backstage,getSink:function(){return an.error("")}})):YT(r,n,a.backstage)};u.on("contexttoolbar-show",function(t){var n=p();Nn(n.lookupTable,t.toolbarKey).each(function(n){y(n,t.target===u?on.none():on.some(t)),Ug.getContent(d).each(dg.focusIn)})});function v(n,t){var e="node"===n?a.backstage.shared.anchors.node(t):a.backstage.shared.anchors.cursor();return Dn(e,function(n,t){return"line"===n?{bubble:Ea(12,0,jD),layouts:{onLtr:function(){return[la]},onRtl:function(){return[da]}},overrides:UD}:{bubble:Ea(0,12,jD),layouts:t?GD:WD,overrides:UD}}(n,l()))}function i(){var n=p();qT(n,u).fold(function(){m.set(on.none()),Ug.hide(d)},function(n){y(n.toolbarApi,on.some(n.elem.dom()))})}function b(n){x(),r.set(n)}var y=function(n,t){if(x(),!l()||!a.backstage.isContextMenuOpen()){var e=h(n),o=t.map(we.fromDom),r=v(n.position,o);m.set(on.some(r)),g.set(t);var i=d.element();Or(i,"display"),Ug.showWithinBounds(d,r,f(e),function(){return on.some(c())}),s()&&xr(i,"display","none")}},x=function(){var n=r.get();null!==n&&(Kg.clearTimeout(n),r.set(null))};u.on("init",function(){u.on(qE,t),u.on("ScrollContent ScrollWindow longpress",o),u.on("click keyup SetContent ObjectResized ResizeEditor",function(n){b(Kg.setEditorTimeout(u,i,0))}),u.on("focusout",function(n){Kg.setEditorTimeout(u,function(){ka(e.element()).isNone()&&ka(d.element()).isNone()&&(m.set(on.none()),Ug.hide(d))},0)}),u.on("SwitchMode",function(){u.readonly&&(m.set(on.none()),Ug.hide(d))}),u.on("NodeChange",function(n){ka(d.element()).fold(function(){b(Kg.setEditorTimeout(u,i,0))},function(n){})})})},YD=function(n,o,r){function t(t,e){bn([o,r],function(n){n.broadcastEvent(t,e)})}function e(t,e){bn([o,r],function(n){n.broadcastOn([t],e)})}function i(n){return e(jf(),{target:n.target()})}function u(n){return e(jf(),{target:we.fromDom(n.target)})}function a(n){0===n.button&&e(Wf(),{target:we.fromDom(n.target)})}function c(n){return t(Si(),hb(n))}function s(n){e(Uf(),{}),t(Ci(),hb(n))}function f(){return e(Uf(),{})}var l=fb(we.fromDom(v.document),"touchstart",i),d=fb(we.fromDom(v.document),"touchmove",function(n){return t(xi(),n)}),m=fb(we.fromDom(v.document),"touchend",function(n){return t(wi(),n)}),g=fb(we.fromDom(v.document),"mousedown",i),p=fb(we.fromDom(v.document),"mouseup",function(n){0===n.raw().button&&e(Wf(),{target:n.target()})});n.on("PostRender",function(){n.on("click",u),n.on("tap",u),n.on("mouseup",a),n.on("ScrollWindow",c),n.on("ResizeWindow",s),n.on("ResizeEditor",f)}),n.on("remove",function(){n.off("click",u),n.off("tap",u),n.off("mouseup",a),n.off("ScrollWindow",c),n.off("ResizeWindow",s),n.off("ResizeEditor",f),g.unbind(),l.unbind(),d.unbind(),m.unbind(),p.unbind()}),n.on("detach",function(){Ss(o),Ss(r),o.destroy(),r.destroy()})},qD=Bl,KD=El,JD=nn([St("shell",!1),ct("makeItem"),St("setupItem",Z),tl("listBehaviours",[gg])]),$D=Cl({name:"items",overrides:function(n){return{behaviours:ya([gg.config({})])}}}),QD=nn([$D]),ZD=_l({name:nn("CustomList")(),configFields:JD(),partFields:QD(),factory:function(s,n,t,e){var o=s.shell?{behaviours:[gg.config({})],components:[]}:{behaviours:[],components:n},r=function(n){return s.shell?on.some(n):Ks(n,s,"items")};return{uid:s.uid,dom:s.dom,components:o.components,behaviours:Vs(s.listBehaviours,o.behaviours),apis:{setItems:function(a,c){r(a).fold(function(){throw v.console.error("Custom List was defined to not be a shell, but no item container was specified in components"),new Error("Custom List was defined to not be a shell, but no item container was specified in components")},function(t){var n=gg.contents(t),e=c.length,o=e-n.length,r=0<o?function(n,t){for(var e=[],o=0;o<n;o++)e.push(t(o));return e}(o,function(){return s.makeItem()}):[],i=n.slice(e);bn(i,function(n){return gg.remove(t,n)}),bn(r,function(n){return gg.append(t,n)});var u=gg.contents(t);bn(u,function(n,t){s.setupItem(a,n,c[t],t)})})}}}},apis:{setItems:function(n,t,e){n.setItems(t,e)}}}),nA=Tn([{offset:["x","y"]},{absolute:["x","y"]},{fixed:["x","y"]}]),tA=nA.offset,eA=nA.absolute,oA=nA.fixed,rA=Tn([{"static":[]},{absolute:["x","y"]},{fixed:["x","y"]}]),iA=function(n,t,e){n.getSystem().isConnected()&&function(e,o,r){var i=o.lazyViewport(e),n=e.element(),t=ko(n),u=hu(t),a=L(function(){return gB(n)}),c=r.isDocked();c&&FB(e,o,r,i),AB(e,o,i,u,a).each(function(n){r.setDocked(!c),n.fold(function(){return _B(e,o)},function(n,t){return MB(e,o,0,a(),eA(n,t))},function(n,t){FB(e,o,r,i,!0),MB(e,o,0,a(),oA(n,t))})})}(n,t,e)},uA=/* */Object.freeze({refresh:iA,reset:RB,isDocked:function(n,t,e){return e.isDocked()}}),aA=/* */Object.freeze({events:function(o,r){return so([yo(ui(),function(t,e){o.contextual.each(function(n){gr(t.element(),n.transitionClass)&&(hr(t.element(),[n.transitionClass,n.fadeInClass]),(r.isVisible()?n.onShown:n.onHidden)(t));e.stop()})}),mo(Si(),function(n,t){iA(n,o,r)}),mo(Ci(),function(n,t){RB(n,o,r)})])}}),cA=[wt("contextual",[ft("fadeInClass"),ft("fadeOutClass"),ft("transitionClass"),dt("lazyContext"),Ku("onShow"),Ku("onShown"),Ku("onHide"),Ku("onHidden")]),Bt("lazyViewport",Cu),ft("leftAttr"),ft("topAttr"),ft("positionAttr"),(_D="modes",MD=["top","bottom"],FD=ge,Ct(_D,MD,Kn(FD))),Ku("onDocked"),Ku("onUndocked")],sA=xa({fields:cA,name:"docking",active:aA,apis:uA,state:/* */Object.freeze({init:function(){var t=ye(!1),e=ye(!0);return tu({isDocked:function(){return t.get()},setDocked:function(n){return t.set(n)},isVisible:function(){return e.get()},setVisible:function(n){return e.set(n)},readState:function(){return"docked:  "+t.get()+", visible: "+e.get()}})}})}),fA={fadeInClass:"tox-editor-dock-fadein",fadeOutClass:"tox-editor-dock-fadeout",transitionClass:"tox-editor-dock-transition"},lA="tox-tinymce--toolbar-sticky-on",dA="tox-tinymce--toolbar-sticky-off",mA=/* */Object.freeze({setup:function(n,t){n.inline||(n.on("ResizeWindow ResizeEditor ResizeContent",function(){t().each(VB)}),n.on("SkinLoaded",function(){t().each(sA.reset)}),n.on("FullscreenStateChanged",function(){t().each(sA.refresh)})),n.on("PostRender",function(){HB(n,!1)})},isDocked:function(n){return n().map(sA.isDocked).getOr(!1)},getBehaviours:PB}),gA=Z,pA=u,hA=nn([]),vA=/* */Object.freeze({setup:gA,isDocked:pA,getBehaviours:hA}),bA=Al({factory:function(t,o){var n={focus:dg.focusIn,setMenus:function(n,t){var e=S(t,function(t){var n={type:"menubutton",text:t.text,fetch:function(n){n(t.getItems())}},e=QO(n).mapError(function(n){return le(n)}).getOrDie();return zC(e,"tox-mbtn",o.backstage,on.some("menuitem"))});gg.set(n,e)}};return{uid:t.uid,dom:t.dom,components:[],behaviours:ya([gg.config({}),Jd("menubar-events",[Ri(function(n){t.onSetup(n)}),mo(Qr(),function(e,n){Ou(e.element(),".tox-mbtn--active").each(function(t){Eu(n.event().target(),".tox-mbtn").each(function(n){jt(t,n)||e.getSystem().getByDom(t).each(function(t){e.getSystem().getByDom(n).each(function(n){Ew.expand(n),Ew.close(t),bg.focus(n)})})})})}),mo(Bi(),function(e,n){n.event().prevFocus().bind(function(n){return e.getSystem().getByDom(n).toOption()}).each(function(t){n.event().newFocus().bind(function(n){return e.getSystem().getByDom(n).toOption()}).each(function(n){Ew.isOpen(t)&&(Ew.expand(n),Ew.close(t))})})})]),dg.config({mode:"flow",selector:".tox-mbtn",onEscape:function(n){return t.onEscape(n),on.some(!0)}}),Xy.config({})]),apis:n,domModification:{attributes:{role:"menubar"}}}},name:"silver.Menubar",configFields:[ct("dom"),ct("uid"),ct("onEscape"),ct("backstage"),St("onSetup",Z)],apis:{focus:function(n,t){n.focus(t)},setMenus:function(n,t,e){n.setMenus(t,e)}}}),yA="container",xA=[Is("slotBehaviours",[])],wA=function(r,n,t){function e(n){return Zs(r)}function o(e,o){return void 0===o&&(o=undefined),function(n,t){return Ks(n,r,t).map(function(n){return e(n,t)}).getOr(o)}}function i(n,t){return"true"!==Lo(n.element(),"aria-hidden")}var u,a=o(i,!1),c=o(function(n,t){if(i(n)){var e=n.element();xr(e,"display","none"),zo(e,"aria-hidden","true"),io(n,Di(),{name:t,visible:!1})}}),s=(u=c,function(t,n){bn(n,function(n){return u(t,n)})}),f=o(function(n,t){if(!i(n)){var e=n.element();Or(e,"display"),Uo(e,"aria-hidden"),io(n,Di(),{name:t,visible:!0})}}),l={getSlotNames:e,getSlot:function(n,t){return Ks(n,r,t)},isShowing:a,hideSlot:c,hideAllSlots:function(n){return s(n,e())},showSlot:f};return{uid:r.uid,dom:r.dom,components:n,behaviours:Rs(r.slotBehaviours),apis:l}},SA=P({getSlotNames:function(n,t){return n.getSlotNames(t)},getSlot:function(n,t,e){return n.getSlot(t,e)},isShowing:function(n,t,e){return n.isShowing(t,e)},hideSlot:function(n,t,e){return n.hideSlot(t,e)},hideAllSlots:function(n,t){return n.hideAllSlots(t)},showSlot:function(n,t,e){return n.showSlot(t,e)}},Qo),CA=N(N({},SA),{sketch:function(n){var e,t=(e=[],{slot:function(n,t){return e.push(n),Ws(yA,zB(n),t)},record:function(){return e}}),o=n(t),r=t.record(),i=S(r,function(n){return wl({name:n,pname:zB(n)})});return rf(yA,xA,i,wA,o)}}),kA=re([yt("icon"),yt("tooltip"),Bt("onShow",Z),Bt("onHide",Z),Bt("onSetup",function(){return Z})]),OA=Yo("FixSizeEvent"),EA=Yo("AutoSizeEvent"),TA=KD.optional({factory:bA,name:"menubar",schema:[ct("backstage")]}),BA=KD.optional({factory:{sketch:function(n){return ZD.sketch({uid:n.uid,dom:n.dom,listBehaviours:ya([dg.config({mode:"acyclic",selector:".tox-toolbar"})]),makeItem:function(){return JO({type:n.split,uid:Yo("multiple-toolbar-item"),backstage:n.backstage,cyclicKeying:!1,getSink:n.getSink,initGroups:[],onEscape:function(){return on.none()}})},setupItem:function(n,t,e,o){$E.setGroups(t,e)},shell:!0})}},name:"multiple-toolbar",schema:[ct("dom"),ct("onEscape")]}),DA=KD.optional({factory:{sketch:function(n){return function(n){return n.split===Ub.sliding?KO:n.split===Ub.floating?qO:JO}(n)({type:n.split,uid:n.uid,onEscape:function(){return n.onEscape(),on.some(!0)},cyclicKeying:!1,initGroups:[],getSink:n.getSink,backstage:n.backstage,moreDrawerData:{lazyToolbar:n.lazyToolbar,lazyMoreButton:n.lazyMoreButton,lazyHeader:n.lazyHeader}})}},name:"toolbar",schema:[ct("dom"),ct("onEscape"),ct("getSink")]}),AA=KD.optional({factory:{sketch:function(n){var t=n.editor,e=n.sticky?PB:hA;return{uid:n.uid,dom:n.dom,components:n.components,behaviours:ya(e(t,n.getSink))}}},name:"header",schema:[ct("dom")]}),_A=KD.optional({name:"socket",schema:[ct("dom")]}),MA=KD.optional({factory:{sketch:function(n){return{uid:n.uid,dom:{tag:"div",classes:["tox-sidebar"],attributes:{role:"complementary"}},components:[{dom:{tag:"div",classes:["tox-sidebar__slider"]},components:[],behaviours:ya([Xy.config({}),bg.config({}),sT.config({dimension:{property:"width"},closedClass:"tox-sidebar--sliding-closed",openClass:"tox-sidebar--sliding-open",shrinkingClass:"tox-sidebar--sliding-shrinking",growingClass:"tox-sidebar--sliding-growing",onShrunk:function(n){nd.getCurrent(n).each(CA.hideAllSlots),ro(n,EA)},onGrown:function(n){ro(n,EA)},onStartGrow:function(n){io(n,OA,{width:Cr(n.element(),"width").getOr("")})},onStartShrink:function(n){io(n,OA,{width:gu(n.element())+"px"})}}),gg.config({}),nd.config({find:function(n){var t=gg.contents(n);return yn(t)}})])}],behaviours:ya([CS(0),Jd("sidebar-sliding-events",[mo(OA,function(n,t){xr(n.element(),"width",t.event().width())}),mo(EA,function(n,t){Or(n.element(),"width")})])])}}},name:"sidebar",schema:[ct("dom")]}),FA=KD.optional({factory:{sketch:function(n){return{uid:n.uid,dom:{tag:"div",attributes:{"aria-hidden":"true"},classes:["tox-throbber"],styles:{display:"none"}},behaviours:ya([gg.config({})]),components:[]}}},name:"throbber",schema:[ct("dom")]}),IA=_l({name:"OuterContainer",factory:function(e,n,t){var o={getSocket:function(n){return qD.getPart(n,e,"socket")},setSidebar:function(n,t){qD.getPart(n,e,"sidebar").each(function(n){return UB(n,t)})},toggleSidebar:function(n,t){qD.getPart(n,e,"sidebar").each(function(n){return function(n,e){nd.getCurrent(n).each(function(t){nd.getCurrent(t).each(function(n){sT.hasGrown(t)?CA.isShowing(n,e)?sT.shrink(t):(CA.hideAllSlots(n),CA.showSlot(n,e)):(CA.hideAllSlots(n),CA.showSlot(n,e),sT.grow(t))})})}(n,t)})},whichSidebar:function(n){return qD.getPart(n,e,"sidebar").bind(WB).getOrNull()},getHeader:function(n){return qD.getPart(n,e,"header")},getToolbar:function(n){return qD.getPart(n,e,"toolbar")},setToolbar:function(n,t){qD.getPart(n,e,"toolbar").each(function(n){n.getApis().setGroups(n,t)})},setToolbars:function(n,t){qD.getPart(n,e,"multiple-toolbar").each(function(n){ZD.setItems(n,t)})},refreshToolbar:function(n){qD.getPart(n,e,"toolbar").each(function(n){return n.getApis().refresh(n)})},getMoreButton:function(n){return qD.getPart(n,e,"toolbar").bind(function(n){return n.getApis().getMoreButton(n)})},getThrobber:function(n){return qD.getPart(n,e,"throbber")},focusToolbar:function(n){qD.getPart(n,e,"toolbar").orThunk(function(){return qD.getPart(n,e,"multiple-toolbar")}).each(function(n){dg.focusIn(n)})},setMenubar:function(n,t){qD.getPart(n,e,"menubar").each(function(n){bA.setMenus(n,t)})},focusMenubar:function(n){qD.getPart(n,e,"menubar").each(function(n){bA.focus(n)})}};return{uid:e.uid,dom:e.dom,components:n,apis:o,behaviours:e.behaviours}},configFields:[ct("dom"),ct("behaviours")],partFields:[AA,TA,DA,BA,_A,MA,FA],apis:{getSocket:function(n,t){return n.getSocket(t)},setSidebar:function(n,t,e){n.setSidebar(t,e)},toggleSidebar:function(n,t,e){n.toggleSidebar(t,e)},whichSidebar:function(n,t){return n.whichSidebar(t)},getHeader:function(n,t){return n.getHeader(t)},getToolbar:function(n,t){return n.getToolbar(t)},setToolbar:function(n,t,e){var o=S(e,function(n){return GO(n)});n.setToolbar(t,o)},setToolbars:function(n,t,e){var o=S(e,function(n){return S(n,GO)});n.setToolbars(t,o)},getMoreButton:function(n,t){return n.getMoreButton(t)},refreshToolbar:function(n,t){return n.refreshToolbar(t)},getThrobber:function(n,t){return n.getThrobber(t)},setMenubar:function(n,t,e){n.setMenubar(t,e)},focusMenubar:function(n,t){n.focusMenubar(t)},focusToolbar:function(n,t){n.focusToolbar(t)}}}),RA={file:{title:"File",items:"newdocument restoredraft | preview | print | deleteallconversations"},edit:{title:"Edit",items:"undo redo | cut copy paste pastetext | selectall | searchreplace"},view:{title:"View",items:"code | visualaid visualchars visualblocks | spellchecker | preview fullscreen | showcomments"},insert:{title:"Insert",items:"image link media addcomment pageembed template codesample inserttable | charmap emoticons hr | pagebreak nonbreaking anchor toc | insertdatetime"},format:{title:"Format",items:"bold italic underline strikethrough superscript subscript codeformat | formats blockformats fontformats fontsizes align | forecolor backcolor | removeformat"},tools:{title:"Tools",items:"spellchecker spellcheckerlanguage | a11ycheck code wordcount"},table:{title:"Table",items:"inserttable | cell row column | advtablesort | tableprops deletetable"},help:{title:"Help",items:"help"}},VA=function(n){function t(){n._skinLoaded=!0,Hv(n)}return function(){n.initialized?t():n.on("init",t)}},NA=d(qB,!1),HA=d(qB,!0),PA=Vh.DOM,zA=Ht(),LA=zA.os.isiOS()&&zA.os.version.major<=12,jA={render:function(e,o,n,t,r){var i=ye(0);NA(e),function(n,t){Rf(n,t,Mo)}(we.fromDom(r.targetNode),o.mothership),ws(Lr(),o.uiMothership),e.on("PostRender",function(){KB(e,o,n,t),i.set(e.getWin().innerWidth),IA.setMenubar(o.outerContainer,YB(e,n)),IA.setSidebar(o.outerContainer,n.sidebar),function(r){function n(n){var t=r.getDoc().documentElement,e=u.get(),o=a.get();e.left()!==i.innerWidth||e.top()!==i.innerHeight?(u.set(Ru(i.innerWidth,i.innerHeight)),Lv(r,n)):o.left()===t.offsetWidth&&o.top()===t.offsetHeight||(a.set(Ru(t.offsetWidth,t.offsetHeight)),Lv(r,n))}function t(n){return zv(r,n)}var i=r.getWin(),e=r.getDoc().documentElement,u=ye(Ru(i.innerWidth,i.innerHeight)),a=ye(Ru(e.offsetWidth,e.offsetHeight));PA.bind(i,"resize",n),PA.bind(i,"scroll",t);var o=lb(we.fromDom(r.getBody()),"load",n);r.on("remove",function(){o.unbind(),PA.unbind(i,"resize",n),PA.unbind(i,"scroll",t)})}(e)});var u=IA.getSocket(o.outerContainer).getOrDie("Could not find expected socket element");if(!0===LA){wr(u.element(),{overflow:"scroll","-webkit-overflow-scrolling":"touch"});var a=function(e,o){var r=null;return{cancel:function(){null!==r&&(v.clearTimeout(r),r=null)},throttle:function(){for(var n=[],t=0;t<arguments.length;t++)n[t]=arguments[t];null===r&&(r=v.setTimeout(function(){e.apply(null,n),r=null},o))}}}(function(){e.fire("ScrollContent")},20);fb(u.element(),"scroll",a.throttle)}jO(e,o),e.addCommand("ToggleSidebar",function(n,t){IA.toggleSidebar(o.outerContainer,t),e.fire("ToggleSidebar")}),e.addQueryValueHandler("ToggleSidebar",function(){return IA.whichSidebar(o.outerContainer)});var c=qb(e);return c!==Ub.sliding&&c!==Ub.floating||e.on("ResizeWindow ResizeEditor ResizeContent",function(){var n=e.getWin().innerWidth;n!==i.get()&&(IA.refreshToolbar(o.outerContainer),i.set(n))}),{iframeContainer:u.element().dom(),editorContainer:o.outerContainer.element().dom()}}},UA=function(t,n,e){var o=n.filter(function(n){return t<n}),r=e.filter(function(n){return n<t});return o.or(r).getOr(t)},WA=function(n){return/^[0-9\.]+(|px)$/i.test(""+n)?on.some(parseInt(""+n,10)):on.none()},GA=function(n){return mn(n)?n+"px":n},XA={render:function(t,i,e,o,n){var u,r=Vh.DOM,a=Jb(t),c=Qb(t),s=we.fromDom(n.targetNode),f=Hb(t).or($B(t)),l=qb(t),d=l===Ub.sliding||l===Ub.floating;HA(t);function m(n){void 0===n&&(n=!1),d&&IA.refreshToolbar(i.outerContainer),a||function(n){var t=d?n.fold(function(){return 0},function(n){return 1<n.components().length?fu(n.components()[1].element()):0}):0,e=mu(s),o=e.top()-fu(u.element())+t;wr(i.outerContainer.element(),{position:"absolute",top:Math.round(o)+"px",left:Math.round(e.left())+"px"});var r=f.getOrThunk(function(){var n=WA(Sr(Lr(),"margin-left")).getOr(0);return gu(Lr())-e.left()+n});xr(u.element(),"max-width",r+"px")}(IA.getToolbar(i.outerContainer)),c&&(n?sA.reset(u):sA.refresh(u))}function g(){xr(i.outerContainer.element(),"display","flex"),r.addClass(t.getBody(),"mce-edit-focus"),Or(i.uiMothership.element(),"display"),m()}function p(){i.outerContainer&&(xr(i.outerContainer.element(),"display","none"),r.removeClass(t.getBody(),"mce-edit-focus")),xr(i.uiMothership.element(),"display","none")}function h(){if(u)g();else{u=IA.getHeader(i.outerContainer).getOrDie();var n=function(n){return Kb(n).getOr(Lr())}(t);ws(n,i.mothership),ws(n,i.uiMothership),KB(t,i,e,o),IA.setMenubar(i.outerContainer,YB(t,e)),g(),t.on("activate",g),t.on("deactivate",p),t.on("NodeChange SkinLoaded ResizeWindow",function(){t.hidden||m(!0)}),t.nodeChanged()}}return t.on("focus",h),t.on("blur hide",p),t.on("init",function(){t.hasFocus()&&h()}),jO(t,i),{editorContainer:i.outerContainer.element().dom()}}},YA=function(t){xk.each([{name:"alignleft",text:"Align left",cmd:"JustifyLeft",icon:"align-left"},{name:"aligncenter",text:"Align center",cmd:"JustifyCenter",icon:"align-center"},{name:"alignright",text:"Align right",cmd:"JustifyRight",icon:"align-right"},{name:"alignjustify",text:"Justify",cmd:"JustifyFull",icon:"align-justify"}],function(n){t.ui.registry.addToggleButton(n.name,{tooltip:n.text,onAction:function(){return t.execCommand(n.cmd)},icon:n.icon,onSetup:lE(t,n.name)})});var n="alignnone",e="No alignment",o="JustifyNone",r="align-none";t.ui.registry.addButton(n,{tooltip:e,onAction:function(){return t.execCommand(o)},icon:r})},qA=function(n){ZB(n),function(t){xk.each([{name:"bold",text:"Bold",action:"Bold",icon:"bold",shortcut:"Meta+B"},{name:"italic",text:"Italic",action:"Italic",icon:"italic",shortcut:"Meta+I"},{name:"underline",text:"Underline",action:"Underline",icon:"underline",shortcut:"Meta+U"},{name:"strikethrough",text:"Strikethrough",action:"Strikethrough",icon:"strike-through",shortcut:""},{name:"subscript",text:"Subscript",action:"Subscript",icon:"subscript",shortcut:""},{name:"superscript",text:"Superscript",action:"Superscript",icon:"superscript",shortcut:""},{name:"removeformat",text:"Clear formatting",action:"RemoveFormat",icon:"remove-formatting",shortcut:""},{name:"newdocument",text:"New document",action:"mceNewDocument",icon:"new-document",shortcut:""},{name:"cut",text:"Cut",action:"Cut",icon:"cut",shortcut:"Meta+X"},{name:"copy",text:"Copy",action:"Copy",icon:"copy",shortcut:"Meta+C"},{name:"paste",text:"Paste",action:"Paste",icon:"paste",shortcut:"Meta+V"},{name:"selectall",text:"Select all",action:"SelectAll",icon:"select-all",shortcut:"Meta+A"}],function(n){t.ui.registry.addMenuItem(n.name,{text:n.text,icon:n.icon,shortcut:n.shortcut,onAction:function(){return t.execCommand(n.action)}})}),t.ui.registry.addMenuItem("codeformat",{text:"Code",icon:"sourcecode",onAction:QB(t,"code")})}(n)},KA=function(n){!function(t){t.ui.registry.addMenuItem("undo",{text:"Undo",icon:"undo",shortcut:"Meta+Z",onSetup:function(n){return nD(n,t,"hasUndo")},onAction:function(){return t.execCommand("undo")}}),t.ui.registry.addMenuItem("redo",{text:"Redo",icon:"redo",shortcut:"Meta+Y",onSetup:function(n){return nD(n,t,"hasRedo")},onAction:function(){return t.execCommand("redo")}})}(n),function(t){t.ui.registry.addButton("undo",{tooltip:"Undo",icon:"undo",onSetup:function(n){return nD(n,t,"hasUndo")},onAction:function(){return t.execCommand("undo")}}),t.ui.registry.addButton("redo",{tooltip:"Redo",icon:"redo",onSetup:function(n){return nD(n,t,"hasRedo")},onAction:function(){return t.execCommand("redo")}})}(n)},JA=function(n){!function(n){n.ui.registry.addButton("visualaid",{tooltip:"Visual aids",text:"Visual aids",onAction:function(){return n.execCommand("mceToggleVisualAid")}})}(n),function(t){t.ui.registry.addToggleMenuItem("visualaid",{text:"Visual aids",onSetup:function(n){return function(t,n){t.setActive(n.hasVisual);function e(n){t.setActive(n.hasVisual)}return n.on("VisualAid",e),function(){return n.off("VisualAid",e)}}(n,t)},onAction:function(){t.execCommand("mceToggleVisualAid")}})}(n)},$A=function(n){!function(t){t.ui.registry.addButton("outdent",{tooltip:"Decrease indent",icon:"outdent",onSetup:function(n){return function(n,t){n.setDisabled(!t.queryCommandState("outdent"));function e(){n.setDisabled(!t.queryCommandState("outdent"))}return t.on("NodeChange",e),function(){return t.off("NodeChange",e)}}(n,t)},onAction:function(){return t.execCommand("outdent")}}),t.ui.registry.addButton("indent",{tooltip:"Increase indent",icon:"indent",onAction:function(){return t.execCommand("indent")}})}(n)},QA=function(n,t){!function(n,t){var e=mE(0,t,tB(n));n.ui.registry.addNestedMenuItem("align",{text:t.shared.providers.translate("Align"),getSubmenuItems:function(){return e.items.validateItems(e.getStyleItems())}})}(n,t),function(n,t){var e=mE(0,t,oB(n));n.ui.registry.addNestedMenuItem("fontformats",{text:t.shared.providers.translate("Fonts"),getSubmenuItems:function(){return e.items.validateItems(e.getStyleItems())}})}(n,t),function(n,t){var e=N({type:"advanced"},t.styleselect),o=mE(0,t,sB(n,e));n.ui.registry.addNestedMenuItem("formats",{text:"Formats",getSubmenuItems:function(){return o.items.validateItems(o.getStyleItems())}})}(n,t),function(n,t){var e=mE(0,t,cB(n));n.ui.registry.addNestedMenuItem("blockformats",{text:"Blocks",getSubmenuItems:function(){return e.items.validateItems(e.getStyleItems())}})}(n,t),function(n,t){var e=mE(0,t,iB(n));n.ui.registry.addNestedMenuItem("fontsizes",{text:"Font sizes",getSubmenuItems:function(){return e.items.validateItems(e.getStyleItems())}})}(n,t)},ZA=function(n,t){YA(n),qA(n),QA(n,t),KA(n),Wv.register(n),JA(n),$A(n)},n_=function(n){return{anchor:"selection",root:we.fromDom(n.selection.getNode())}},t_={onLtr:function(){return[ic,aa,ca,sa,fa,rc,Wg,Gg,hm,gm,vm,pm]},onRtl:function(){return[ic,ca,aa,fa,sa,rc,Wg,Gg,vm,pm,hm,gm]}},e_={valignCentre:[],alignCentre:[],alignLeft:["tox-pop--align-left"],alignRight:["tox-pop--align-right"],right:["tox-pop--right"],left:["tox-pop--left"],bottom:["tox-pop--bottom"],top:["tox-pop--top"]},o_=function(n){return n.settings.contextmenu_never_use_native||!1},r_=function(n){return function(n,t,e){var o=n.ui.registry.getAll().contextMenus;return V(n.settings,t).map(sD).getOrThunk(function(){return C(sD(e),function(n){return En(o,n)})})}(n,"contextmenu","link linkchecker image imagetools table spellchecker configurepermanentpen")},i_=function(n){return!1===n.getParam("contextmenu")},u_={type:"separator"},a_=function(t){if(cn(t))return t;switch(t.type){case"separator":return u_;case"submenu":return{type:"nestedmenuitem",text:t.text,icon:t.icon,getSubmenuItems:function(){var n=t.getSubmenuItems();return cn(n)?n:S(n,a_)}};default:return{type:"menuitem",text:t.text,icon:t.icon,onAction:function(n){return function(){return n()}}(t.onAction)}}},c_="data-initial-z-index",s_=wt("snaps",[ct("getSnapPoints"),Ku("onSensor"),ct("leftAttr"),ct("topAttr"),St("lazyViewport",Cu),St("mustSnap",!1)]),f_=[St("useFixed",u),ct("blockerClass"),St("getTarget",l),St("onDrag",Z),St("repositionTarget",!0),St("onDrop",Z),Bt("getBounds",Cu),s_],l_=function(n,t,r,i,u){var e=t.getSnapPoints(n);return yD(e,r,i,u).orThunk(function(){return O(e,function(t,e){var n=e.sensor(),o=function(n,t,e,o,r,i){var u=yB(n,r,i),a=yB(t,r,i),c=Math.abs(u.left()-a.left()),s=Math.abs(u.top()-a.top());return Ru(c,s)}(r,n,e.range().left(),e.range().top(),i,u);return t.deltas.fold(function(){return{deltas:on.some(o),snap:on.some(e)}},function(n){return(o.left()+o.top())/2<=(n.left()+n.top())/2?{deltas:on.some(o),snap:on.some(e)}:t})},{deltas:on.none(),snap:on.none()}).snap.map(function(n){return{output:nn(SB(n.output(),r,i,u)),extra:n.extra}})})},d_=function(n,t,e,o,r){var i=t.getSnapPoints(n);return yD(i,e,o,r)},m_=/* */Object.freeze({getData:function(n){return on.from(Ru(n.x(),n.y()))},getDelta:function(n,t){return Ru(t.left()-n.left(),t.top()-n.top())}}),g_=g(f_,[Zu("dragger",{handlers:OD(ED)})]),p_=/* */Object.freeze({getData:function(n){var t=n.raw().touches;return 1===t.length?function(n){var t=n[0];return on.some(Ru(t.clientX,t.clientY))}(t):on.none()},getDelta:function(n,t){return Ru(t.left()-n.left(),t.top()-n.top())}}),h_=g_,v_=g(f_,[Zu("dragger",{handlers:OD(TD)})]),b_=g(f_,[Zu("dragger",{handlers:OD(function(n,t,e){return g(ED(n,t,e),TD(n,t,e))})})]),y_=/* */Object.freeze({mouse:h_,touch:v_,mouseOrTouch:b_}),x_=/* */Object.freeze({init:function(){var o=on.none(),t=on.none(),n=nn({});return tu({readState:n,reset:function(){o=on.none(),t=on.none()},update:function(t,n){return t.getData(n).bind(function(n){return function(t,e){var n=o.map(function(n){return t.getDelta(n,e)});return o=on.some(e),n}(t,n)})},getStartData:function(){return t},setStartData:function(n){t=on.some(n)}})}}),w_=/* */Object.freeze({snapTo:function(n,t,e,o){var r=t.getTarget(n.element());if(t.repositionTarget){var i=ko(n.element()),u=hu(i),a=gB(r),c=function(n,t,e){return{coord:SB(n.output(),n.output(),t,e),extra:n.extra()}}(o,u,a),s=wB(c.coord,0,a);wr(r,s)}}}),S_=wa({branchKey:"mode",branches:y_,name:"dragging",active:{events:function(n,t){return n.dragger.handlers(n,t)}},extra:{snap:Co(["sensor","range","output"],["extra"])},state:x_,apis:w_}),C_=Ht(),k_=function(c,e){function t(n){var t=Su(n);return BD(g.getOpt(e),n,t.x(),t.y(),t.width(),t.height())}function o(n){var t=Su(n);return BD(p.getOpt(e),n,t.right(),t.bottom(),t.width(),t.height())}function r(n,t,e,o){var r=e(t);S_.snapTo(n,r),function(n,t,e,o){var r=t.dom().getBoundingClientRect();Or(n.element(),"display");var i=Eo(we.fromDom(c.getBody())).dom().innerHeight,u=e(r),a=o(r,i);(u||a)&&xr(n.element(),"display","none")}(n,t,function(n){return n[o]<0},function(n,t){return n[o]>t})}function i(n){return r(h,n,t,"top")}function u(n){return r(v,n,o,"bottom")}var a=ye([]),s=ye([]),n=ye(!1),f=ye(on.none()),l=ye(on.none()),d=DD(function(){return S(a.get(),function(n){return t(n)})},f,function(t){l.get().each(function(n){c.fire("TableSelectorChange",{start:t,finish:n})})}),m=DD(function(){return S(s.get(),function(n){return o(n)})},l,function(t){f.get().each(function(n){c.fire("TableSelectorChange",{start:n,finish:t})})}),g=AD(d),p=AD(m),h=au(g.asSpec()),v=au(p.asSpec());C_.deviceType.isTouch()&&(c.on("TableSelectionChange",function(t){n.get()||(vs(e,h),vs(e,v),n.set(!0)),f.set(on.some(t.start)),l.set(on.some(t.finish)),t.otherCells.each(function(n){a.set(n.upOrLeftCells),s.set(n.downOrRightCells),i(t.start),u(t.finish)})}),c.on("ResizeEditor ResizeWindow ScrollContent",function(){f.get().each(i),l.get().each(u)}),c.on("TableSelectionClear",function(){n.get()&&(ys(h),ys(v),n.set(!1)),f.set(on.none()),l.set(on.none())}))};(RD=ID=ID||{})[RD.None=0]="None",RD[RD.Both=1]="Both",RD[RD.Vertical=2]="Vertical";function O_(n,t,e){var o=we.fromDom(n.getContainer()),r=function(n,t,e,o,r){var i={};return i.height=UA(o+t.top(),Nb(n),Pb(n)),e===ID.Both&&(i.width=UA(r+t.left(),Vb(n),Hb(n))),i}(n,t,e,fu(o),gu(o));Cn(r,function(n,t){return xr(o,t,GA(n))}),Pv(n)}function E_(n){if(1===n.nodeType){if("BR"===n.nodeName||n.getAttribute("data-mce-bogus"))return!0;if("bookmark"===n.getAttribute("data-mce-type"))return!0}return!1}function T_(o,t){var r,n,e;return{dom:{tag:"div",classes:["tox-statusbar"]},components:(n=function(){var n=[];return o.getParam("elementpath",!0,"boolean")&&n.push(hM(o,{})),Vt(o.settings.plugins,"wordcount")&&n.push(function(n,o){function r(n,t,e){return gg.set(n,[Rr(o.translate(["{0} "+e,t[e]]))])}return Xg.sketch({dom:{tag:"button",classes:["tox-statusbar__wordcount"]},components:[],buttonBehaviours:ya([Xy.config({}),gg.config({}),nl.config({store:{mode:"memory",initialValue:{mode:"words",count:{words:0,characters:0}}}}),Jd("wordcount-events",[Hi(function(n){var t=nl.getValue(n),e="words"===t.mode?"characters":"words";nl.setValue(n,{mode:e,count:t.count}),r(n,t.count,e)}),Ri(function(e){n.on("wordCountUpdate",function(n){var t=nl.getValue(e).mode;nl.setValue(e,{mode:t,count:n.wordCount}),r(e,n.wordCount,t)})})])])})}(o,t)),o.getParam("branding",!0,"boolean")&&n.push(function(){var n=ih.translate(["Powered by {0}","Tiny"]);return{dom:{tag:"span",classes:["tox-statusbar__branding"],innerHtml:'<a href="https://www.tiny.cloud/?utm_campaign=editor_referral&amp;utm_medium=poweredby&amp;utm_source=tinymce&amp;utm_content=v5" rel="noopener" target="_blank" tabindex="-1" aria-label="'+n+'">'+n+"</a>"}}}()),0<n.length?[{dom:{tag:"div",classes:["tox-statusbar__text-container"]},components:n}]:[]}(),e=function(n){var t=!Vt(n.settings.plugins,"autoresize"),e=n.getParam("resize",t);return!1===e?ID.None:"both"===e?ID.Both:ID.Vertical}(o),e!==ID.None&&n.push((r=e,{dom:{tag:"div",classes:["tox-statusbar__resize-handle"],attributes:{title:t.translate("Resize")},innerHtml:xm("resize-handle",t.icons)},behaviours:ya([S_.config({mode:"mouse",repositionTarget:!1,onDrag:function(n,t,e){O_(o,e,r)},blockerClass:"tox-blocker"})])})),n)}}function B_(n){return[ft("type"),function(n){return st(n,me)}("columns"),n]}function D_(t){return ce("items","items",Mn(),Kn(Zn(function(n){return tt("Checking item of "+t,pF,n).fold(function(n){return an.error(le(n))},function(n){return an.value(n)})})))}function A_(n){return cn(n.type)&&cn(n.name)}function __(n){var t=function(n){return C(AF(n),A_)}(n),e=B(t,function(t){return function(n){return on.from(_F[n.type])}(t).fold(function(){return[]},function(n){return[st(t.name,n)]})});return re(e)}function M_(n){return{internalDialog:et(function(n){return tt("dialog",DF,n)}(n)),dataValidator:__(n),initialData:n.initialData}}function F_(n){var e=[],o={};return Cn(n,function(n,t){n.fold(function(){e.push(t)},function(n){o[t]=n})}),0<e.length?an.error(e):an.value(o)}function I_(n){return yn(function(n,t){var e=gn.call(n,0);return e.sort(t),e}(n,function(n,t){return t<n?-1:n<t?1:0}))}function R_(n,t){xr(n,"height",t+"px"),Ht().browser.isIE()?Or(n,"flex-basis"):xr(n,"flex-basis",t+"px")}function V_(n,o,r){ku(n,'[role="dialog"]').each(function(e){Ou(e,'[role="tablist"]').each(function(t){r.get().map(function(n){return xr(o,"height","0"),xr(o,"flex-basis","0"),Math.min(n,function(n,t,e){var o,r=Oo(n).dom(),i=ku(n,".tox-dialog-wrap").getOr(n);o="fixed"===Sr(i,"position")?Math.max(r.clientHeight,v.window.innerHeight):Math.max(r.offsetHeight,r.scrollHeight);var u=fu(t),a=t.dom().offsetLeft>=e.dom().offsetLeft+gu(e)?Math.max(fu(e),u):u,c=parseInt(Sr(n,"margin-top"),10)||0,s=parseInt(Sr(n,"margin-bottom"),10)||0;return o-(fu(n)+c+s-a)}(e,o,t))}).each(function(n){R_(o,n)})})})}function N_(n){return Ou(n,'[role="tabpanel"]')}function H_(r){var i;return{smartTabHeight:(i=ye(on.none()),{extraEvents:[Ri(function(n){var t=n.element();N_(t).each(function(o){xr(o,"visibility","hidden"),n.getSystem().getByDom(o).toOption().each(function(n){var t=function(o,r,i){return S(o,function(n,t){gg.set(i,o[t].view());var e=r.dom().getBoundingClientRect();return gg.set(i,[]),e.height})}(r,o,n),e=I_(t);i.set(e)}),V_(t,o,i),Or(o,"visibility"),function(n,t){yn(n).each(function(n){return GF.showTab(t,n.value)})}(r,n),Kg.requestAnimationFrame(function(){V_(t,o,i)})})}),mo(Ci(),function(n){var t=n.element();N_(t).each(function(n){V_(t,n,i)})}),mo(py,function(n,t){var r=n.element();N_(r).each(function(t){var n=Ca();xr(t,"visibility","hidden");var e=Cr(t,"height").map(function(n){return parseInt(n,10)});Or(t,"height"),Or(t,"flex-basis");var o=t.dom().getBoundingClientRect().height;e.forall(function(n){return n<o})?(i.set(on.from(o)),V_(r,t,i)):e.each(function(n){R_(t,n)}),Or(t,"visibility"),n.each(Sa)})})],selectFirst:!1}),naiveTabHeight:{extraEvents:[],selectFirst:!0}}}function P_(n,t,e,o){return{dom:{tag:"div",classes:["tox-dialog__content-js"],attributes:N(N({},t.map(function(n){return{id:n}}).getOr({})),o?{"aria-live":"polite"}:{})},components:[],behaviours:ya([CS(0),VT.config({channel:JF,updateState:function(n,t){return on.some({isTabPanel:function(){return"tabpanel"===t.body.type}})},renderComponents:function(n){switch(n.body.type){case"tabpanel":return[function(n,e){function o(n){var t=nl.getValue(n),e=F_(t).getOr({}),o=i.get(),r=Dn(o,e);i.set(r)}function r(n){var t=i.get();nl.setValue(n,t)}var i=ye({}),u=ye(null),t=S(n.tabs,function(n){return{value:n.name,dom:{tag:"div",classes:["tox-dialog__body-nav-item"],innerHtml:e.shared.providers.translate(n.title)},view:function(){return[mS.sketch(function(t){return{dom:{tag:"div",classes:["tox-form"]},components:S(n.items,function(n){return Gk(t,n,e)}),formBehaviours:ya([dg.config({mode:"acyclic",useTabstopAt:b(FS)}),Jd("TabView.form.events",[Ri(r),Vi(o)]),dc.config({channels:K([{key:XF,value:{onReceive:o}},{key:YF,value:{onReceive:r}}])})])}})]}}}),a=H_(t).smartTabHeight;return GF.sketch({dom:{tag:"div",classes:["tox-dialog__body"]},onChangeTab:function(n,t,e){var o=nl.getValue(t);io(n,gy,{name:o,oldName:u.get()}),u.set(o)},tabs:t,components:[GF.parts().tabbar({dom:{tag:"div",classes:["tox-dialog__body-nav"]},components:[PF.parts().tabs({})],markers:{tabClass:"tox-tab",selectedClass:"tox-dialog__body-nav-item--active"},tabbarBehaviours:ya([Xy.config({})])}),GF.parts().tabview({dom:{tag:"div",classes:["tox-dialog__body-content"]}})],selectFirst:a.selectFirst,tabSectionBehaviours:ya([Jd("tabpanel",a.extraEvents),dg.config({mode:"acyclic"}),nd.config({find:function(n){return yn(GF.getViewItems(n))}}),nl.config({store:{mode:"manual",getValue:function(n){return n.getSystem().broadcastOn([XF],{}),i.get()},setValue:function(n,t){i.set(t),n.getSystem().broadcastOn([YF],{})}}})])})}(n.body,e)];default:return[function(n,e){var t=bm(mS.sketch(function(t){return{dom:{tag:"div",classes:["tox-form"].concat(n.classes)},components:S(n.items,function(n){return Gk(t,n,e)})}}));return{dom:{tag:"div",classes:["tox-dialog__body"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-content"]},components:[t.asSpec()]}],behaviours:ya([dg.config({mode:"acyclic",useTabstopAt:b(FS)}),SS(t),TS(t,{postprocess:function(n){return F_(n).fold(function(n){return v.console.error(n),{}},function(n){return n})}})])}}(n.body,e)]}},initialData:n})])}}function z_(n,e){return[vo(Zr(),IS),n(cy,function(n,t){e.onClose(),t.onClose()}),n(sy,function(n,t,e,o){t.onCancel(n),ro(o,cy)}),mo(my,function(n,t){return e.onUnblock()}),mo(dy,function(n,t){return e.onBlock(t.event())})]}function L_(n,t){function e(n,t){return Gb.sketch({dom:{tag:"div",classes:["tox-dialog__footer-"+n]},components:S(t,function(n){return n.memento.asSpec()})})}var o=function(n,t){for(var e=[],o=[],r=0,i=n.length;r<i;r++){var u=n[r];(t(u,r)?e:o).push(u)}return{pass:e,fail:o}}(t.map(function(n){return n.footerButtons}).getOr([]),function(n){return"start"===n.align});return[e("start",o.pass),e("end",o.fail)]}function j_(n,o){return{dom:sp('<div class="tox-dialog__footer"></div>'),components:[],behaviours:ya([VT.config({channel:$F,initialData:n,updateState:function(n,t){var e=S(t.buttons,function(n){var t=bm(function(n,t){return qC(n,n.type,t)}(n,o));return{name:n.name,align:n.align,memento:t}});return on.some({lookupByName:function(n,t){return function(t,n,e){return E(n,function(n){return n.name===e}).bind(function(n){return n.memento.getOpt(t)})}(n,e,t)},footerButtons:e})},renderComponents:L_})])}}function U_(n,t){return SM.parts().footer(j_(n,t))}function W_(t,e){if(t.getRoot().getSystem().isConnected()){var o=nd.getCurrent(t.getFormWrapper()).getOr(t.getFormWrapper());return mS.getField(o,e).fold(function(){var n=t.getFooter();return VT.getState(n).get().bind(function(n){return n.lookupByName(o,e)})},function(n){return on.some(n)})}return on.none()}function G_(u,o,a){function n(n){var t=u.getRoot();t.getSystem().isConnected()&&n(t)}var c={getData:function(){var n=u.getRoot(),t=n.getSystem().isConnected()?u.getFormWrapper():n,e=nl.getValue(t),o=P(a,function(n){return n.get()});return N(N({},e),o)},setData:function(i){n(function(n){var t=c.getData(),e=An(t,i),o=function(n,t){var e=n.getRoot();return VT.getState(e).get().map(function(n){return et(tt("data",n.dataValidator,t))}).getOr(t)}(u,e),r=u.getFormWrapper();nl.setValue(r,o),Cn(a,function(n,t){En(e,t)&&n.set(e[t])})})},disable:function(n){W_(u,n).each(kh.disable)},enable:function(n){W_(u,n).each(kh.enable)},focus:function(n){W_(u,n).each(bg.focus)},block:function(t){if(!cn(t))throw new Error("The dialogInstanceAPI.block function should be passed a blocking message of type string as an argument");n(function(n){io(n,dy,{message:t})})},unblock:function(){n(function(n){ro(n,my)})},showTab:function(e){n(function(n){var t=u.getBody();VT.getState(t).get().exists(function(n){return n.isTabPanel()})&&nd.getCurrent(t).each(function(n){GF.showTab(n,e)})})},redial:function(e){n(function(n){var t=o(e);n.getSystem().broadcastOn([qF],t),n.getSystem().broadcastOn([KF],t.internalDialog),n.getSystem().broadcastOn([JF],t.internalDialog),n.getSystem().broadcastOn([$F],t.internalDialog),c.setData(t.initialData)})},close:function(){n(function(n){ro(n,cy)})}};return c}function X_(n,t){return{dom:{tag:"div",styles:{display:"none"},classes:["tox-dialog__header"]},components:[n,t]}}function Y_(n,t){return SM.parts().close(Xg.sketch({dom:{tag:"button",classes:["tox-button","tox-button--icon","tox-button--naked"],attributes:{type:"button","aria-label":t.translate("Close")}},action:n,buttonBehaviours:ya([Xy.config({})])}))}function q_(){return SM.parts().title({dom:{tag:"div",classes:["tox-dialog__title"],innerHtml:"",styles:{display:"none"}}})}function K_(n,t){return SM.parts().body({dom:{tag:"div",classes:["tox-dialog__body"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-content"]},components:[{dom:sp("<p>"+t.translate(n)+"</p>")}]}]})}function J_(n){return SM.parts().footer({dom:{tag:"div",classes:["tox-dialog__footer"]},components:n})}function $_(n,t){return[Gb.sketch({dom:{tag:"div",classes:["tox-dialog__footer-start"]},components:n}),Gb.sketch({dom:{tag:"div",classes:["tox-dialog__footer-end"]},components:t})]}function Q_(t){var n,e="tox-dialog",o=e+"-wrap",r=o+"__backdrop",i=e+"__disable-scroll";return SM.sketch({lazySink:t.lazySink,onEscape:function(n){return t.onEscape(n),on.some(!0)},useTabstopAt:function(n){return!FS(n)},dom:{tag:"div",classes:[e].concat(t.extraClasses),styles:N({position:"relative"},t.extraStyles)},components:g([t.header,t.body],t.footer.toArray()),parts:{blocker:{dom:sp('<div class="'+o+'"></div>'),components:[{dom:{tag:"div",classes:tI?[r,r+"--opaque"]:[r]}}]}},dragBlockClass:o,modalBehaviours:ya(g([bg.config({}),Jd("dialog-events",t.dialogEvents.concat([yo(Zr(),function(n,t){dg.focusIn(n)})])),Jd("scroll-lock",[Ri(function(){lr(Lr(),i)}),Vi(function(){mr(Lr(),i)})])],t.extraBehaviours)),eventOrder:N((n={},n[mi()]=["dialog-events"],n[ki()]=["scroll-lock","dialog-events","alloy.base.behaviour"],n[Oi()]=["alloy.base.behaviour","dialog-events","scroll-lock"],n),t.eventOrder)})}function Z_(n){return Xg.sketch({dom:{tag:"button",classes:["tox-button","tox-button--icon","tox-button--naked"],attributes:{type:"button","aria-label":n.translate("Close"),title:n.translate("Close")}},components:[{dom:{tag:"div",classes:["tox-icon"],innerHtml:'<svg width="24" height="24" xmlns="http://www.w3.org/2000/svg"><path d="M17.953 7.453L13.422 12l4.531 4.547-1.406 1.406L12 13.422l-4.547 4.531-1.406-1.406L10.578 12 6.047 7.453l1.406-1.406L12 10.578l4.547-4.531z" fill-rule="evenodd"></path></svg>'}}],action:function(n){ro(n,sy)}})}function nM(n,t,e){function o(n){return[Rr(e.translate(n.title))]}return{dom:{tag:"div",classes:["tox-dialog__title"],attributes:N({},t.map(function(n){return{id:n}}).getOr({}))},components:o(n),behaviours:ya([VT.config({channel:KF,renderComponents:o})])}}function tM(){return{dom:sp('<div class="tox-dialog__draghandle"></div>')}}function eM(n,t){return function(n,t){var e=SM.parts().title(nM(n,on.none(),t)),o=SM.parts().draghandle(tM()),r=SM.parts().close(Z_(t)),i=[e].concat(n.draggable?[o]:[]).concat([r]);return Gb.sketch({dom:sp('<div class="tox-dialog__header"></div>'),components:i})}({title:t.shared.providers.translate(n),draggable:t.dialog.isDraggableModal()},t.shared.providers)}function oM(n,t){return{onClose:function(){return t.closeWindow()},onBlock:function(e){SM.setBusy(n(),function(n,t){return{dom:{tag:"div",classes:["tox-dialog__busy-spinner"],attributes:{"aria-label":e.message()},styles:{left:"0px",right:"0px",bottom:"0px",top:"0px",position:"absolute"}},behaviours:t,components:[{dom:sp('<div class="tox-spinner"><div></div><div></div><div></div></div>')}]}})},onUnblock:function(){SM.setIdle(n())}}}function rM(n,t,e,o){var r;return au(Q_(N(N({},n),{lazySink:o.shared.getSink,extraBehaviours:g([VT.config({channel:qF,updateState:function(n,t){return on.some(t)},initialData:t}),AS({})],n.extraBehaviours),onEscape:function(n){ro(n,sy)},dialogEvents:e,eventOrder:(r={},r[di()]=["reflecting","receiving"],r[ki()]=["scroll-lock","reflecting","messages","dialog-events","alloy.base.behaviour"],r[Oi()]=["alloy.base.behaviour","dialog-events","messages","reflecting","scroll-lock"],r)})))}function iM(n){return S(n,function(n){return"menu"===n.type?function(n){var t=S(n.items,function(n){var t=ye(!1);return N(N({},n),{storage:t})});return N(N({},n),{items:t})}(n):n})}function uM(n){return O(n,function(n,t){return"menu"!==t.type?n:O(t.items,function(n,t){return n[t.name]=t.storage,n},n)},{})}function aM(n,t,e){var o=eM(n.internalDialog.title,e),r=function(n,t){var e=P_(n,on.none(),t,!1);return SM.parts().body(e)}({body:n.internalDialog.body},e),i=iM(n.internalDialog.buttons),u=uM(i),a=U_({buttons:i},e),c=nI(function(){return d},oM(function(){return l},t)),s="normal"!==n.internalDialog.size?"large"===n.internalDialog.size?["tox-dialog--width-lg"]:["tox-dialog--width-md"]:[],f={header:o,body:r,footer:on.some(a),extraClasses:s,extraBehaviours:[],extraStyles:{}},l=rM(f,n,c,e),d=G_({getRoot:function(){return l},getBody:function(){return SM.getBody(l)},getFooter:function(){return SM.getFooter(l)},getFormWrapper:function(){var n=SM.getBody(l);return nd.getCurrent(n).getOr(n)}},t.redial,u);return{dialog:l,instanceApi:d}}function cM(n,t,e,o){var r,i,u=Yo("dialog-label"),a=Yo("dialog-content"),c=bm(function(n,t,e){return Gb.sketch({dom:sp('<div class="tox-dialog__header"></div>'),components:[nM(n,on.some(t),e),tM(),Z_(e)],containerBehaviours:ya([S_.config({mode:"mouse",blockerClass:"blocker",getTarget:function(n){return Eu(n,'[role="dialog"]').getOrDie()},snaps:{getSnapPoints:function(){return[]},leftAttr:"data-drag-left",topAttr:"data-drag-top"}})])})}({title:n.internalDialog.title,draggable:!0},u,e.shared.providers)),s=bm(function(n,t,e,o){return P_(n,on.some(t),e,o)}({body:n.internalDialog.body},a,e,o)),f=iM(n.internalDialog.buttons),l=uM(f),d=bm(function(n,t){return j_(n,t)}({buttons:f},e)),m=nI(function(){return p},{onBlock:function(){},onUnblock:function(){},onClose:function(){return t.closeWindow()}}),g=au({dom:{tag:"div",classes:["tox-dialog","tox-dialog-inline"],attributes:(r={role:"dialog"},r["aria-labelledby"]=u,r["aria-describedby"]=""+a,r)},eventOrder:(i={},i[di()]=[VT.name(),dc.name()],i[mi()]=["execute-on-form"],i[ki()]=["reflecting","execute-on-form"],i),behaviours:ya([dg.config({mode:"cyclic",onEscape:function(n){return ro(n,cy),on.some(!0)},useTabstopAt:function(n){return!FS(n)&&("button"!==Je(n)||"disabled"!==Lo(n,"disabled"))}}),VT.config({channel:qF,updateState:function(n,t){return on.some(t)},initialData:n}),bg.config({}),Jd("execute-on-form",m.concat([yo(Zr(),function(n,t){dg.focusIn(n)})])),AS({})]),components:[c.asSpec(),s.asSpec(),d.asSpec()]}),p=G_({getRoot:function(){return g},getFooter:function(){return d.get(g)},getBody:function(){return s.get(g)},getFormWrapper:function(){var n=s.get(g);return nd.getCurrent(n).getOr(n)}},t.redial,l);return{dialog:g,instanceApi:p}}function sM(n){return sn(n)&&-1!==oI.indexOf(n.mceAction)}function fM(e,n,o,t){var r,i=eM(e.title,t),u=function(n){var t={dom:{tag:"div",classes:["tox-dialog__content-js"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-iframe"]},components:[RS({dom:{tag:"iframe",attributes:{src:n.url}},behaviours:ya([Xy.config({}),bg.config({})])})]}],behaviours:ya([dg.config({mode:"acyclic",useTabstopAt:b(FS)})])};return SM.parts().body(t)}(e),a=e.buttons.bind(function(n){return 0===n.length?on.none():on.some(U_({buttons:n},t))}),c=ZF(function(){return h},oM(function(){return p},n)),s=N(N({},e.height.fold(function(){return{}},function(n){return{height:n+"px","max-height":n+"px"}})),e.width.fold(function(){return{}},function(n){return{width:n+"px","max-width":n+"px"}})),f=e.width.isNone()&&e.height.isNone()?["tox-dialog--width-lg"]:[],l=new eI(e.url,{base_uri:new eI(v.window.location.href)}),d=l.protocol+"://"+l.host+(l.port?":"+l.port:""),m=ye(on.none()),g=[Jd("messages",[Ri(function(){var n=fb(we.fromDom(v.window),"message",function(n){if(l.isSameOrigin(new eI(n.raw().origin))){var t=n.raw().data;sM(t)?function(n,t,e){switch(e.mceAction){case"insertContent":n.insertContent(e.content);break;case"setContent":n.setContent(e.content);break;case"execCommand":var o=!!ln(e.ui)&&e.ui;n.execCommand(e.cmd,o,e.value);break;case"close":t.close();break;case"block":t.block(e.message);break;case"unblock":t.unblock()}}(o,h,t):function(n){return!sM(n)&&sn(n)&&En(n,"mceAction")}(t)&&e.onMessage(h,t)}});m.set(on.some(n))}),Vi(function(){m.get().each(function(n){return n.unbind()})})]),dc.config({channels:(r={},r[QF]={onReceive:function(n,t){Ou(n.element(),"iframe").each(function(n){n.dom().contentWindow.postMessage(t,d)})}},r)})],p=rM({header:i,body:u,footer:a,extraClasses:f,extraBehaviours:g,extraStyles:s},e,c,t),h=function(t){function n(n){t.getSystem().isConnected()&&n(t)}return{block:function(t){if(!cn(t))throw new Error("The urlDialogInstanceAPI.block function should be passed a blocking message of type string as an argument");n(function(n){io(n,dy,{message:t})})},unblock:function(){n(function(n){ro(n,my)})},close:function(){n(function(n){ro(n,cy)})},sendMessage:function(t){n(function(n){n.getSystem().broadcastOn([QF],t)})}}}(p);return{dialog:p,instanceApi:h}}var lM,dM,mM,gM,pM,hM=function(i,r){r.delimiter||(r.delimiter="\xbb");return{dom:{tag:"div",classes:["tox-statusbar__path"],attributes:{role:"navigation"}},behaviours:ya([dg.config({mode:"flow",selector:"div[role=button]"}),Xy.config({}),gg.config({}),Jd("elementPathEvents",[Ri(function(e,n){i.shortcuts.add("alt+F11","focus statusbar elementpath",function(){return dg.focusIn(e)}),i.on("NodeChange",function(n){var t=function(n){for(var t=[],e=n.length;0<e--;){var o=n[e];if(1===o.nodeType&&!E_(o)){var r=i.fire("ResolveName",{name:o.nodeName.toLowerCase(),target:o});if(r.isDefaultPrevented()||t.push({name:r.name,element:o}),r.isPropagationStopped())break}}return t}(n.parents);0<t.length&&gg.set(e,function(n){var t=S(n||[],function(t,n){return Xg.sketch({dom:{tag:"div",classes:["tox-statusbar__path-item"],attributes:{role:"button","data-index":n,"tab-index":-1,"aria-level":n+1},innerHtml:t.name},action:function(n){i.focus(),i.selection.select(t.element),i.nodeChanged()}})}),o={dom:{tag:"div",classes:["tox-statusbar__path-divider"],attributes:{"aria-hidden":!0},innerHtml:" "+r.delimiter+" "}};return O(t.slice(1),function(n,t){var e=n;return e.push(o),e.push(t),e},[t[0]])}(t))})})])]),components:[]}},vM=function(l){function d(){return e.bind(IA.getHeader)}function m(){return an.value(v)}function n(){return e.bind(function(n){return IA.getMoreButton(n)}).getOrDie("Could not find more button element")}function g(){return e.bind(function(n){return IA.getThrobber(n)}).getOrDie("Could not find throbber element")}var t=l.inline,p=t?XA:jA,h=Qb(l)?mA:vA,e=on.none(),o=Ht(),r=o.browser.isIE()?["tox-platform-ie"]:[],i=o.deviceType.isTouch()?["tox-platform-touch"]:[],u=ih.isRtl()?{attributes:{dir:"rtl"}}:{},v=au({dom:N({tag:"div",classes:["tox","tox-silver-sink","tox-tinymce-aux"].concat(r).concat(i)},u),behaviours:ya([_f.config({useFixed:function(){return h.isDocked(d)}})])}),a=bm({dom:{tag:"div",classes:["tox-anchorbar"]}}),b=vO(v,l,function(){return e.bind(function(n){return a.getOpt(n)}).getOrDie("Could not find a anchor bar element")},n),c=IA.parts().menubar({dom:{tag:"div",classes:["tox-menubar"]},backstage:b,onEscape:function(){l.focus()}}),s=qb(l),f=IA.parts().toolbar({dom:{tag:"div",classes:["tox-toolbar"]},getSink:m,backstage:b,onEscape:function(){l.focus()},split:s,lazyToolbar:function(){return e.bind(function(n){return IA.getToolbar(n)}).getOrDie("Could not find more toolbar element")},lazyMoreButton:n,lazyHeader:function(){return d().getOrDie("Could not find header element")}}),y=IA.parts()["multiple-toolbar"]({dom:{tag:"div",classes:["tox-toolbar-overlord"]},onEscape:function(){},split:s}),x=IA.parts().socket({dom:{tag:"div",classes:["tox-edit-area"]}}),w=IA.parts().sidebar({dom:{tag:"div",classes:["tox-sidebar"]}}),S=IA.parts().throbber({dom:{tag:"div",classes:["tox-throbber"]},backstage:b}),C=l.getParam("statusbar",!0,"boolean")&&!t?on.some(T_(l,b.shared.providers)):on.none(),k={dom:{tag:"div",classes:["tox-sidebar-wrap"]},components:[x,w]},O=Yb(l),E=Lb(l),T=zb(l),B=IA.parts().header({dom:{tag:"div",classes:["tox-editor-header"]},components:H([T?[c]:[],O?[y]:E?[f]:[],Jb(l)?[]:[a.asSpec()]]),sticky:Qb(l),editor:l,getSink:m}),D=H([[B],t?[]:[k]]),A=H([[{dom:{tag:"div",classes:["tox-editor-container"]},components:D}],t?[]:C.toArray(),[S]]),_=$b(l),M=N(N({role:"application"},ih.isRtl()?{dir:"rtl"}:{}),_?{"aria-hidden":"true"}:{}),F=au(IA.sketch({dom:{tag:"div",classes:["tox","tox-tinymce"].concat(t?["tox-tinymce-inline"]:[]).concat(i).concat(r),styles:N({visibility:"hidden"},_?{opacity:"0",border:"0"}:{}),attributes:M},components:A,behaviours:ya([dg.config({mode:"cyclic",selector:".tox-menubar, .tox-toolbar, .tox-toolbar__primary, .tox-toolbar__overflow--open, .tox-sidebar__overflow--open, .tox-statusbar__path, .tox-statusbar__wordcount, .tox-statusbar__branding a"})])}));e=on.some(F),l.shortcuts.add("alt+F9","focus menubar",function(){IA.focusMenubar(F)}),l.shortcuts.add("alt+F10","focus toolbar",function(){IA.focusToolbar(F)});var I=Fb(F),R=Fb(v);YD(l,I,R),uy(l);function V(){var n=GA(JB(l)),t=GA(function(n){return $B(n).getOr(Rb(n))}(l));return l.inline||(kr("div","width",t)&&xr(F.element(),"width",t),kr("div","height",n)?xr(F.element(),"height",n):xr(F.element(),"height","200px")),n}return{mothership:I,uiMothership:R,backstage:b,renderUI:function(){h.setup(l,d),ZA(l,b),dD(l,m,b),function(o){var r=o.ui.registry.getAll().sidebars;bn(wn(r),function(t){function e(){return on.from(o.queryCommandValue("ToggleSidebar")).is(t)}var n=r[t];o.ui.registry.addToggleButton(t,{icon:n.icon,tooltip:n.tooltip,onAction:function(n){o.execCommand("ToggleSidebar",!1,t),n.setActive(e())},onSetup:function(n){function t(){return n.setActive(e())}return o.on("ToggleSidebar",t),function(){o.off("ToggleSidebar",t)}}})})}(l),function(e,t,o){function r(n){n!==i.get()&&(GB(t(),n,o.providers),i.set(n))}var i=ye(!1),u=ye(on.none());e.on("ProgressState",function(n){if(u.get().each(Kg.clearTimeout),mn(n.time)){var t=Kg.setEditorTimeout(e,function(){return r(n.state)},n.time);u.set(on.some(t))}else r(n.state),u.set(on.none())})}(l,g,b.shared);var n=l.ui.registry.getAll(),t=n.buttons,e=n.menuItems,o=n.contextToolbars,r=n.sidebars,i=jb(l),u={menuItems:e,menus:l.settings.menu?P(l.settings.menu,function(n){return An(n,{items:n.items})}):{},menubar:l.settings.menubar,toolbar:i.getOrThunk(function(){return l.getParam("toolbar",!0)}),buttons:t,sidebar:r};XD(l,o,v,{backstage:b}),k_(l,v);var a=l.getElement(),c=V(),s={mothership:I,uiMothership:R,outerContainer:F},f={targetNode:a,height:c};return p.render(l,s,u,b,f)},getUi:function(){return{channels:{broadcastAll:R.broadcast,broadcastOn:R.broadcastOn,register:function(){}}}}}},bM=function(n,t){var e=on.from(Lo(n,"id")).fold(function(){var n=Yo("dialog-label");return zo(t,"id",n),n},l);zo(n,"aria-labelledby",e)},yM=nn([ct("lazySink"),ht("dragBlockClass"),Bt("getBounds",Cu),St("useTabstopAt",nn(!0)),St("eventOrder",{}),Is("modalBehaviours",[dg]),Ju("onExecute"),Qu("onEscape")]),xM={sketch:l},wM=nn([Cl({name:"draghandle",overrides:function(n,t){return{behaviours:ya([S_.config({mode:"mouse",getTarget:function(n){return ku(n,'[role="dialog"]').getOr(n)},blockerClass:n.dragBlockClass.getOrDie(new Error("The drag blocker class was not specified for a dialog with a drag handle: \n"+JSON.stringify(t,null,2)).message),getBounds:n.getDragBounds})])}}}),wl({schema:[ct("dom")],name:"title"}),wl({factory:xM,schema:[ct("dom")],name:"close"}),wl({factory:xM,schema:[ct("dom")],name:"body"}),Cl({factory:xM,schema:[ct("dom")],name:"footer"}),Sl({factory:{sketch:function(n,t){return N(N({},n),{dom:t.dom,components:t.components})}},schema:[St("dom",{tag:"div",styles:{position:"fixed",left:"0px",top:"0px",right:"0px",bottom:"0px"}}),St("components",[])],name:"blocker"})]),SM=_l({name:"ModalDialog",configFields:yM(),partFields:wM(),factory:function(o,n,t,r){var a=Yo("alloy.dialog.busy"),c=Yo("alloy.dialog.idle"),s=ya([dg.config({mode:"special",onTab:function(){return on.some(!0)},onShiftTab:function(){return on.some(!0)}}),bg.config({})]),e=Yo("modal-events"),i=N(N({},o.eventOrder),{"alloy.system.attached":[e].concat(o.eventOrder["alloy.system.attached"]||[])});return{uid:o.uid,dom:o.dom,components:n,apis:{show:function(i){var n=o.lazySink(i).getOrDie(),u=ye(on.none()),t=r.blocker(),e=n.getSystem().build(N(N({},t),{components:t.components.concat([cu(i)]),behaviours:ya([bg.config({}),Jd("dialog-blocker-events",[yo(Zr(),function(){dg.focusIn(i)}),mo(c,function(n,t){jo(i.element(),"aria-busy")&&(Uo(i.element(),"aria-busy"),u.get().each(function(n){return gg.remove(i,n)}))}),mo(a,function(n,t){zo(i.element(),"aria-busy","true");var e=t.event().getBusySpec();u.get().each(function(n){gg.remove(i,n)});var o=e(i,s),r=n.getSystem().build(o);u.set(on.some(r)),gg.append(i,cu(r)),r.hasConfigured(dg)&&dg.focusIn(r)})])])}));vs(n,e),dg.focusIn(i)},hide:function(t){To(t.element()).each(function(n){t.getSystem().getByDom(n).each(function(n){ys(n)})})},getBody:function(n){return Js(n,o,"body")},getFooter:function(n){return Js(n,o,"footer")},setIdle:function(n){ro(n,c)},setBusy:function(n,t){io(n,a,{getBusySpec:t})}},eventOrder:i,domModification:{attributes:{role:"dialog","aria-modal":"true"}},behaviours:Vs(o.modalBehaviours,[gg.config({}),dg.config({mode:"cyclic",onEnter:o.onExecute,onEscape:o.onEscape,useTabstopAt:o.useTabstopAt}),Jd(e,[Ri(function(n){bM(n.element(),Js(n,o,"title").element()),function(n,t){var e=on.from(Lo(n,"id")).fold(function(){var n=Yo("dialog-describe");return zo(t,"id",n),n},l);zo(n,"aria-describedby",e)}(n.element(),Js(n,o,"body").element())})])])}},apis:{show:function(n,t){n.show(t)},hide:function(n,t){n.hide(t)},getBody:function(n,t){return n.getBody(t)},getFooter:function(n,t){return n.getFooter(t)},setBusy:function(n,t,e){n.setBusy(t,e)},setIdle:function(n,t){n.setIdle(t)}}}),CM=[ft("type"),ft("text"),lt("level",["info","warn","error","success"]),ft("icon"),St("url","")],kM=re(CM),OM=[ft("type"),ft("text"),Tt("disabled",!1),Tt("primary",!1),ce("name","name",In(function(){return Yo("button-name")}),ge),yt("icon"),Tt("borderless",!1)],EM=re(OM),TM=[ft("type"),ft("name"),ft("label"),Tt("disabled",!1)],BM=re(TM),DM=pe,AM=[ft("type"),ft("name")],_M=AM.concat([yt("label")]),MM=re(_M),FM=ge,IM=re(_M),RM=ge,VM=re(_M),NM=Kn(se),HM=_M.concat([Tt("sandboxed",!0)]),PM=re(HM),zM=ge,LM=_M.concat([yt("inputMode"),yt("placeholder"),Tt("maximized",!1),Tt("disabled",!1)]),jM=re(LM),UM=ge,WM=_M.concat([gt("items",[ft("text"),ft("value")]),kt("size",1),Tt("disabled",!1)]),GM=re(WM),XM=ge,YM=_M.concat([Tt("constrain",!0),Tt("disabled",!1)]),qM=re(YM),KM=re([ft("width"),ft("height")]),JM=_M.concat([yt("placeholder"),Tt("maximized",!1),Tt("disabled",!1)]),$M=re(JM),QM=ge,ZM=_M.concat([Et("filetype","file",["image","media","file"]),St("disabled",!1)]),nF=re(ZM),tF=re([ft("value"),St("meta",{})]),eF=AM.concat([Ot("tag","textarea"),ft("scriptId"),ft("scriptUrl"),(lM="settings",dM=undefined,Ct(lM,dM,be))]),oF=AM.concat([Ot("tag","textarea"),dt("init")]),rF=Zn(function(n){return tt("customeditor.old",qn(oF),n).orThunk(function(){return tt("customeditor.new",qn(eF),n)})}),iF=ge,uF=[ft("type"),ft("html"),Et("presets","presentation",["presentation","document"])],aF=re(uF),cF=_M.concat([st("currentState",re([ct("blob"),ft("url")]))]),sF=re(cF),fF=_M.concat([St("columns","auto")]),lF=re(fF),dF=(mM=[ft("value"),ft("text"),ft("icon")],ue(mM)),mF=[ft("type"),pt("header",ge),pt("cells",Kn(ge))],gF=re(mF),pF=fe(function(){return rt("type",{alertbanner:kM,bar:re(function(n){return[ft("type"),n]}(D_("bar"))),button:EM,checkbox:BM,colorinput:MM,colorpicker:IM,dropzone:VM,grid:re(B_(D_("grid"))),iframe:PM,input:jM,selectbox:GM,sizeinput:qM,textarea:$M,urlinput:nF,customeditor:rF,htmlpanel:aF,imagetools:sF,collection:lF,label:re(function(n){return[ft("type"),ft("label"),n]}(D_("label"))),table:gF,panel:vF})}),hF=[ft("type"),St("classes",[]),pt("items",pF)],vF=re(hF),bF=[ce("name","name",In(function(){return Yo("tab-name")}),ge),ft("title"),pt("items",pF)],yF=[ft("type"),gt("tabs",bF)],xF=re(yF),wF=re([ft("type"),ft("name"),Tt("active",!1)].concat(Gp)),SF=pe,CF=[ce("name","name",In(function(){return Yo("button-name")}),ge),yt("icon"),Et("align","end",["start","end"]),Tt("primary",!1),Tt("disabled",!1)],kF=g(CF,[ft("text")]),OF=g([lt("type",["submit","cancel","custom"])],kF),EF=g([lt("type",["menu"]),yt("text"),yt("tooltip"),yt("icon"),pt("items",wF),Bt("onSetup",function(){return Z})],CF),TF=kF,BF=it("type",{submit:OF,cancel:OF,custom:OF,menu:EF}),DF=re([ft("title"),st("body",rt("type",{panel:vF,tabpanel:xF})),Ot("size","normal"),pt("buttons",BF),St("initialData",{}),Bt("onAction",Z),Bt("onChange",Z),Bt("onSubmit",Z),Bt("onClose",Z),Bt("onCancel",Z),St("onTabChange",Z)]),AF=function(n){return sn(n)?[n].concat(B(R(n),AF)):fn(n)?B(n,AF):[]},_F={checkbox:DM,colorinput:FM,colorpicker:RM,dropzone:NM,input:UM,iframe:zM,sizeinput:KM,selectbox:XM,size:KM,textarea:QM,urlinput:tF,customeditor:iF,collection:dF,togglemenuitem:SF},MF=re(g([lt("type",["cancel","custom"])],TF)),FF=re([ft("title"),ft("url"),bt("height"),bt("width"),(gM="buttons",pM=MF,vt(gM,Kn(pM))),Bt("onAction",Z),Bt("onCancel",Z),Bt("onClose",Z),Bt("onMessage",Z)]),IF={open:function(n,t){var e=M_(t);return n(e.internalDialog,e.initialData,e.dataValidator)},openUrl:function(n,t){return n(et(function(n){return tt("dialog",FF,n)}(t)))},redial:function(n){return M_(n)}},RF=Al({name:"TabButton",configFields:[St("uid",undefined),ct("value"),ce("dom","dom",Rn(function(n){return{attributes:{role:"tab",id:Yo("aria"),"aria-selected":"false"}}}),de()),ht("action"),St("domModification",{}),Is("tabButtonBehaviours",[bg,dg,nl]),ct("view")],factory:function(n,t){return{uid:n.uid,dom:n.dom,components:n.components,events:im(n.action),behaviours:Vs(n.tabButtonBehaviours,[bg.config({}),dg.config({mode:"execution",useSpace:!0,useEnter:!0}),nl.config({store:{mode:"memory",initialValue:n.value}})]),domModification:n.domModification}}}),VF=nn([ct("tabs"),ct("dom"),St("clickToDismiss",!1),Is("tabbarBehaviours",[cd,dg]),Yu(["tabClass","selectedClass"])]),NF=kl({factory:RF,name:"tabs",unit:"tab",overrides:function(o,n){function r(n,t){cd.dehighlight(n,t),io(n,_i(),{tabbar:n,button:t})}function i(n,t){cd.highlight(n,t),io(n,Ai(),{tabbar:n,button:t})}return{action:function(n){var t=n.getSystem().getByUid(o.uid).getOrDie(),e=cd.isHighlighted(t,n);(e&&o.clickToDismiss?r:e?Z:i)(t,n)},domModification:{classes:[o.markers.tabClass]}}}}),HF=nn([NF]),PF=_l({name:"Tabbar",configFields:VF(),partFields:HF(),factory:function(n,t,e,o){return{uid:n.uid,dom:n.dom,components:t,"debug.sketcher":"Tabbar",domModification:{attributes:{role:"tablist"}},behaviours:Vs(n.tabbarBehaviours,[cd.config({highlightClass:n.markers.selectedClass,itemClass:n.markers.tabClass,onHighlight:function(n,t){zo(t.element(),"aria-selected","true")},onDehighlight:function(n,t){zo(t.element(),"aria-selected","false")}}),dg.config({mode:"flow",getInitial:function(n){return cd.getHighlighted(n).map(function(n){return n.element()})},selector:"."+n.markers.tabClass,executeOnMove:!0})])}}}),zF=Al({name:"Tabview",configFields:[Is("tabviewBehaviours",[gg])],factory:function(n,t){return{uid:n.uid,dom:n.dom,behaviours:Vs(n.tabviewBehaviours,[gg.config({})]),domModification:{attributes:{role:"tabpanel"}}}}}),LF=nn([St("selectFirst",!0),Ku("onChangeTab"),Ku("onDismissTab"),St("tabs",[]),Is("tabSectionBehaviours",[])]),jF=wl({factory:PF,schema:[ct("dom"),mt("markers",[ct("tabClass"),ct("selectedClass")])],name:"tabbar",defaults:function(n){return{tabs:n.tabs}}}),UF=wl({factory:zF,name:"tabview"}),WF=nn([jF,UF]),GF=_l({name:"TabSection",configFields:LF(),partFields:WF(),factory:function(r,n,t,e){function o(n,t){Ks(n,r,"tabbar").each(function(n){t(n).each(uo)})}return{uid:r.uid,dom:r.dom,components:n,behaviours:Rs(r.tabSectionBehaviours),events:so(H([r.selectFirst?[Ri(function(n,t){o(n,cd.getFirst)})]:[],[mo(Ai(),function(n,t){!function(o){var t=nl.getValue(o);Ks(o,r,"tabview").each(function(e){E(r.tabs,function(n){return n.value===t}).each(function(n){var t=n.view();zo(e.element(),"aria-labelledby",Lo(o.element(),"id")),gg.set(e,t),r.onChangeTab(e,o,t)})})}(t.event().button())}),mo(_i(),function(n,t){var e=t.event().button();r.onDismissTab(n,e)})]])),apis:{getViewItems:function(n){return Ks(n,r,"tabview").map(function(n){return gg.contents(n)}).getOr([])},showTab:function(n,e){o(n,function(t){var n=cd.getCandidates(t);return E(n,function(n){return nl.getValue(n)===e}).filter(function(n){return!cd.isHighlighted(t,n)})})}}}},apis:{getViewItems:function(n,t){return n.getViewItems(t)},showTab:function(n,t,e){n.showTab(t,e)}}}),XF="send-data-to-section",YF="send-data-to-view",qF=Yo("update-dialog"),KF=Yo("update-title"),JF=Yo("update-body"),$F=Yo("update-footer"),QF=Yo("body-send-message"),ZF=function(i,n){function t(n,r){return mo(n,function(e,o){u(e,function(n,t){r(i(),n,o.event(),e)})})}var u=function(t,e){VT.getState(t).get().each(function(n){e(n,t)})};return g(z_(t,n),[t(fy,function(n,t,e){t.onAction(n,{name:e.name()})})])},nI=function(i,n){function t(n,r){return mo(n,function(e,o){u(e,function(n,t){r(i(),n,o.event(),e)})})}var u=function(t,e){VT.getState(t).get().each(function(n){e(n.internalDialog,t)})};return g(z_(t,n),[t(ly,function(n,t){return t.onSubmit(n)}),t(ay,function(n,t,e){t.onChange(n,{name:e.name()})}),t(fy,function(n,t,e,o){function r(){return dg.focusIn(o)}var i=Ca();t.onAction(n,{name:e.name(),value:e.value()}),Ca().fold(function(){r()},function(n){!qe(o.element(),n)||jo(n,"disabled")?r():qe(n,i.getOrNull())&&jo(i.getOrDie(),"disabled")&&r()})}),t(gy,function(n,t,e){t.onTabChange(n,{newTabName:e.name(),oldTabName:e.oldName()})}),Vi(function(n){var t=i();nl.setValue(n,t.getData())})])},tI=ph.deviceType.isTouch(),eI=tinymce.util.Tools.resolve("tinymce.util.URI"),oI=["insertContent","setContent","execCommand","close","block","unblock"],rI=function(n){var l=n.backstage,d=n.editor,m=Qb(d),e=function(c){var s=c.backstage.shared;return{open:function(n,t){function e(){SM.hide(u),t()}var o=bm(qC({name:"close-alert",text:"OK",primary:!0,align:"end",disabled:!1,icon:on.none()},"cancel",c.backstage)),r=q_(),i=Y_(e,s.providers),u=au(Q_({lazySink:function(){return s.getSink()},header:X_(r,i),body:K_(n,s.providers),footer:on.some(J_($_([],[o.asSpec()]))),onEscape:e,extraClasses:["tox-alert-dialog"],extraBehaviours:[],extraStyles:{},dialogEvents:[mo(sy,e)],eventOrder:{}}));SM.show(u);var a=o.get(u);bg.focus(a)}}}(n),o=function(s){var f=s.backstage.shared;return{open:function(n,t){function e(n){SM.hide(a),t(n)}var o=bm(qC({name:"yes",text:"Yes",primary:!0,align:"end",disabled:!1,icon:on.none()},"submit",s.backstage)),r=qC({name:"no",text:"No",primary:!0,align:"end",disabled:!1,icon:on.none()},"cancel",s.backstage),i=q_(),u=Y_(function(){return e(!1)},f.providers),a=au(Q_({lazySink:function(){return f.getSink()},header:X_(i,u),body:K_(n,f.providers),footer:on.some(J_($_([],[r,o.asSpec()]))),onEscape:function(){return e(!1)},extraClasses:["tox-confirm-dialog"],extraBehaviours:[],extraStyles:{},dialogEvents:[mo(sy,function(){return e(!1)}),mo(ly,function(){return e(!0)})],eventOrder:{}}));SM.show(a);var c=o.get(a);bg.focus(c)}}}(n),r=function(n,e){return IF.openUrl(function(n){var t=fM(n,{closeWindow:function(){SM.hide(t.dialog),e(t.instanceApi)}},d,l);return SM.show(t.dialog),t.instanceApi},n)},i=function(n,i){return IF.open(function(n,t,e){var o=t,r=aM({dataValidator:e,initialData:o,internalDialog:n},{redial:IF.redial,closeWindow:function(){SM.hide(r.dialog),i(r.instanceApi)}},l);return SM.show(r.dialog),r.instanceApi.setData(o),r.instanceApi},n)},u=function(n,c,s,f){return IF.open(function(n,t,e){function o(){return i.on(function(n){sA.refresh(n)})}var r=function(n,t){return et(tt("data",t,n))}(t,e),i=function(){var t=ye(on.none());return{clear:function(){t.set(on.none())},set:function(n){t.set(on.some(n))},isSet:function(){return t.get().isSome()},on:function(n){t.get().each(n)}}}(),u=cM({dataValidator:e,initialData:r,internalDialog:n},{redial:IF.redial,closeWindow:function(){i.on(Ug.hide),d.off("ResizeEditor",o),i.clear(),s(u.instanceApi)}},l,f),a=au(Ug.sketch({lazySink:l.shared.getSink,dom:{tag:"div",classes:[]},fireDismissalEventInstead:{},inlineBehaviours:ya(g([Jd("window-manager-inline-events",[mo(Ei(),function(n,t){ro(u.dialog,sy)})])],function(n,t){return t?[]:[sA.config({contextual:{lazyContext:function(){return on.some(wu(we.fromDom(n.getContentAreaContainer())))},fadeInClass:"tox-dialog-dock-fadein",fadeOutClass:"tox-dialog-dock-fadeout",transitionClass:"tox-dialog-dock-transition"},leftAttr:"data-dock-left",topAttr:"data-dock-top",positionAttr:"data-dock-pos",modes:["top"]})]}(d,m)))}));return i.set(a),Ug.showWithin(a,c,cu(u.dialog),on.some(Lr())),m||(sA.refresh(a),d.on("ResizeEditor",o)),u.instanceApi.setData(r),dg.focusIn(u.dialog),u.instanceApi},n)};return{open:function(n,t,e){return t!==undefined&&"toolbar"===t.inline?u(n,l.shared.anchors.toolbar(),e,t.ariaAttrs):t!==undefined&&"cursor"===t.inline?u(n,l.shared.anchors.cursor(),e,t.ariaAttrs):i(n,e)},openUrl:function(n,t){return r(n,t)},alert:function(n,t){e.open(n,function(){t()})},close:function(n){n.close()},confirm:function(n,t){o.open(n,function(n){t(n)})}}};!function gI(){n.add("silver",function(n){var t=vM(n),e=t.uiMothership,o=t.backstage,r=t.renderUI,i=t.getUi;mb(n,o.shared);var u=rI({editor:n,backstage:o});return{renderUI:r,getWindowManagerImpl:nn(u),getNotificationManagerImpl:function(){return Jg(0,{backstage:o},e)},ui:i()}})}()}(window);