<?php
$form=$this->beginWidget('CActiveForm',array(
        'id'=>'inf1m',
        'htmlOptions'=>array('enctype'=>'multipar1a')
));
$state= Yii::app()->request->getParam('state', '');
?>
<?php if($state == "ds"){ ?>
<style>
    .weui_label{
        width: 9em;
    }
    .warningContent{
        color: #8a6d3b;
        background-color: #fcf8e3;
        border-color: #faebcc;
        padding: 15px;
        font-size: 12px;
        margin-bottom: 20px;
        border: 1px solid transparent;
        border-radius: 4px;
    }
</style>
<?php } ?>
<div class="weui_cells_title"><?php echo Yii::t('userinfo', 'Child Profile'); ?></div>
    <div class="weui_cells weui_cells_form">
        <div class="weui_cell">
            <div class="weui_cell_bd weui_cell_primary weui_cell_primary">
                <div class="weui_uploader">
                    <div class="weui_uploader_bd">
                        <ul class="weui_uploader_files">
                            <li id="ChildProfileBasic_photo"class="weui_uploader_file" style="background-image:url(<?php echo CommonUtils::childPhotoUrl($childObj->photo, 'small');?>)">
                                <div class="weui_uploader_status_content">
                                    <!-- <i class="weui_icon_warn" style="display:none"></i> -->
                                </div>
                            </li>
                        </ul>
                        <?php if($state == "ivy"){ ?>
                            <div class="weui_uploader_input_wrp">
                                <?php echo $form->fileField($childObj, 'uploadPhoto', array(
                                    'class'=>'weui_uploader_input',
//                                'accept'=>'accept="image/jpg,image/jpeg,image/png,image/gif"',
                                )); ?>
                            </div>
                        <?php }?>

                    </div>
                </div>
            </div>
        </div>
        <div class="weui_cells weui_cells_form">
            <?php if($state == "ivy"){ ?>
                <div class="weui_cell">
                    <div class="weui_cell_hd"><label class="weui_label"><?php echo $form->label($childObj, 'name_cn', array('class'=>'weui_label'));?></label></div>
                    <div class="weui_cell_bd weui_cell_primary">
                        <?php echo $form->textField($childObj, 'name_cn', array(
                            'class'=>'weui_input',
                        )); ?>
                    </div>
                    <div class="weui_cell_ft">
                        <i class="weui_icon_warn"></i>
                    </div>
                </div>
                <div class="weui_cell">
                    <div class="weui_cell_hd"><label class="weui_label"><?php echo $form->label($childObj, 'first_name_en', array('class'=>'weui_label'));?></label></div>
                    <div class="weui_cell_bd weui_cell_primary">
                        <?php echo $form->textField($childObj, 'first_name_en', array(
                            'class'=>'weui_input',
                        )); ?>
                    </div>
                    <div class="weui_cell_ft">
                        <i class="weui_icon_warn"></i>
                    </div>
                </div>
                <div class="weui_cell">
                    <div class="weui_cell_hd"><label class="weui_label"><?php echo $form->label($childObj, 'last_name_en', array('class'=>'weui_label'));?></label></div>
                    <div class="weui_cell_bd weui_cell_primary">
                        <?php echo $form->textField($childObj, 'last_name_en', array(
                            'class'=>'weui_input',
                        )); ?>
                    </div>
                    <div class="weui_cell_ft">
                        <i class="weui_icon_warn"></i>
                    </div>
                </div>
                <div class="weui_cell">
                    <div class="weui_cell_hd"><label class="weui_label"><?php echo $form->label($childObj, 'nick', array('class'=>'weui_label'));?></label></div>
                    <div class="weui_cell_bd weui_cell_primary">
                        <?php echo $form->textField($childObj, 'nick', array(
                            'class'=>'weui_input',
                        )); ?>
                    </div>
                </div>
                <div class="weui_cell weui_cell_select weui_select_after">
                    <div class="weui_cell_hd">
                        <?php echo $form->label($childObj, 'gender', array('class'=>'weui_label'));?>
                    </div>
                    <div class="weui_cell_bd weui_cell_primary">
                        <?php echo $form->dropDownList($childObj, 'gender',$cfgs['gender'], array(
                            'class'=>'weui_select',
                        )); ?>
                    </div>
                </div>
                <div class="weui_cell weui_cell_select weui_select_after">
                    <div class="weui_cell_hd">
                        <?php echo $form->label($childObj, 'country', array('class'=>'weui_label'));?>
                    </div>
                    <div class="weui_cell_bd weui_cell_primary">
                        <?php echo $form->dropDownList($childObj, 'country',$country, array(
                            'class'=>'weui_select',
                        )); ?>
                    </div>
                </div>
                <div class="weui_cell weui_cell_select weui_select_after">
                    <div class="weui_cell_hd">
                        <?php echo $form->label($childObj, 'lang', array('class'=>'weui_label'));?>
                    </div>
                    <div class="weui_cell_bd weui_cell_primary">
                        <?php echo $form->dropDownList($childObj, 'lang',$lang, array(
                            'class'=>'weui_select',
                        )); ?>
                    </div>
                </div>

                <div class="weui_btn_area">
                    <?php echo CHtml::link(Yii::t("global","Save"), 'javascript:void(0)', array('class'=>'weui_btn weui_btn_primary ajax_submit', 'data-prefix'=>get_class($childObj))); ?>
                </div>
            <?php }else{ ?>
                <?php if($childObj->is_legel_cn_name == 1){ ?>
                    <div class="weui_cell">
                        <div class="weui_cell_hd">
                            <label class="weui_label">
                                <label for="ChildProfileBasic_name_cn" class="weui_label">法定中文名字</label>
                            </label>
                        </div>
                        <div class="weui_cell_bd weui_cell_primary">
                            <?php echo $form->textField($childObj, 'name_cn', array(
                                'class'=>'weui_input','disabled'=>'disabled'
                            )); ?>
                        </div>
                    </div>
                    <div class="weui_cell">
                        <div class="weui_cell_hd">
                            <label class="weui_label">
                                <label for="ChildProfileBasic_last_name_en" class="weui_label">法定姓（拼音）</label>
                            </label>
                        </div>
                        <div class="weui_cell_bd weui_cell_primary">
                            <?php echo $form->textField($childObj, 'last_name_en', array(
                                'class'=>'weui_input','disabled'=>'disabled'
                            )); ?>
                        </div>
                    </div>
                    <div class="weui_cell">
                        <div class="weui_cell_hd">
                            <label class="weui_label">
                                <label for="ChildProfileBasic_first_name_en" class="weui_label">法定名（拼音）</label>
                            </label>
                        </div>
                        <div class="weui_cell_bd weui_cell_primary">
                            <?php echo $form->textField($childObj, 'first_name_en', array(
                                'class'=>'weui_input','disabled'=>'disabled'
                            )); ?>
                        </div>
                    </div>
                    <div class="weui_cell">
                        <div class="weui_cell_hd">
                            <label class="weui_label">
                                <label for="ChildProfileBasic_nick" class="weui_label">昵称/英文名</label>
                            </label>
                        </div>
                        <div class="weui_cell_bd weui_cell_primary">
                            <?php echo $form->textField($childObj, 'nick', array(
                                'class'=>'weui_input','disabled'=>'disabled'
                            )); ?>
                        </div>
                    </div>
                <?php }else{ ?>
                    <div class="weui_cell">
                        <div class="weui_cell_hd">
                            <label class="weui_label">
                                <label for="ChildProfileBasic_last_name_en_en">Legal Last Name</label>
                            </label>
                        </div>
                        <div class="weui_cell_bd weui_cell_primary">
                            <?php echo $form->textField($childObj, 'last_name_en', array(
                                'class'=>'weui_input','disabled'=>'disabled', 'id' => 'ChildProfileBasic_last_name_en_en'
                            )); ?>
                        </div>
                    </div>
                    <div class="weui_cell">
                        <div class="weui_cell_hd">
                            <label class="weui_label">
                                <label for="ChildProfileBasic_first_name_en_en" style="word-wrap:break-word;word-break:break-all;">Legal First Name</label>
                            </label>
                        </div>
                        <div class="weui_cell_bd weui_cell_primary">
                            <?php echo $form->textField($childObj, 'first_name_en', array(
                                'class'=>'weui_input','disabled'=>'disabled', 'id' => 'ChildProfileBasic_first_name_en_en'
                            )); ?>
                        </div>
                    </div>
                    <div class="weui_cell">
                        <div class="weui_cell_hd">
                            <label class="weui_label">
                                <label for="ChildProfileBasic_middle_name_en_en" style="word-wrap:break-word;word-break:break-all;">Legal Middle Name</label>
                            </label>
                        </div>
                        <div class="weui_cell_bd weui_cell_primary">
                            <?php echo $form->textField($childObj, 'middle_name_en', array(
                                'class'=>'weui_input','disabled'=>'disabled', 'id' => 'ChildProfileBasic_middle_name_en_en'
                            )); ?>
                        </div>
                    </div>
                    <div class="weui_cell">
                        <div class="weui_cell_hd">
                            <label class="weui_label">
                                <label for="ChildProfileBasic_nick_en">Preferred Name</label>
                            </label>
                        </div>
                        <div class="weui_cell_bd weui_cell_primary">
                            <?php echo $form->textField($childObj, 'nick', array(
                                'class'=>'weui_input','disabled'=>'disabled', 'id' => 'ChildProfileBasic_nick_en'
                            )); ?>
                        </div>
                    </div>
                    <div class="weui_cell">
                        <div class="weui_cell_hd">
                            <label class="weui_label">
                                <label for="ChildProfileBasic_last_name_cn_en" style="word-wrap:break-word;word-break:keep-all;">Chinese Name</label>
                            </label>
                        </div>
                        <div class="weui_cell_bd weui_cell_primary">
                            <?php echo $form->textField($childObj, 'name_cn', array(
                                'class'=>'weui_input','disabled'=>'disabled', 'id' => 'ChildProfileBasic_last_name_cn_en'
                            )); ?>
                        </div>
                    </div>
                <?php } ?>
                <div class="weui_cell weui_cell_select weui_select_after">
                    <div class="weui_cell_hd">
                        <?php echo $form->label($childObj, 'gender', array('class'=>'weui_label'));?>
                    </div>
                    <div class="weui_cell_bd weui_cell_primary">
                        <?php echo $form->dropDownList($childObj, 'gender',$cfgs['gender'], array(
                            'class'=>'weui_select','disabled'=>'disabled',
                        )); ?>
                    </div>
                </div>
                <div class="weui_cell weui_cell_select weui_select_after">
                    <div class="weui_cell_hd">
                        <?php echo $form->label($childObj, 'country', array('class'=>'weui_label'));?>
                    </div>
                    <div class="weui_cell_bd weui_cell_primary">
                        <?php echo $form->dropDownList($childObj, 'country',$country, array(
                            'class'=>'weui_select','disabled'=>'disabled',
                        )); ?>
                    </div>
                </div>
                <div class="weui_cell weui_cell_select weui_select_after">
                    <div class="weui_cell_hd">
                        <?php echo $form->label($childObj, 'lang', array('class'=>'weui_label'));?>
                    </div>
                    <div class="weui_cell_bd weui_cell_primary">
                        <?php echo $form->dropDownList($childObj, 'lang',$lang, array(
                            'class'=>'weui_select','disabled'=>'disabled',
                        )); ?>
                    </div>
                </div>
                <div class="warningContent"><?php
                    echo Yii::t("labels", 'If any of the information above is inaccurate, <NAME_EMAIL> to update them.');?></div>
            <?php } ?>
        </div>
    </div>
<?php
$this->endWidget();
?>

<!-- JS -->
<script src="<?php echo Yii::app()->theme->baseUrl;?>/js/lrz/lrz.bundle.js?v1<?php echo Yii::app()->params['refreshAssets'];?>"></script>
<script>
    $('#ChildProfileBasic_uploadPhoto').change(function () {
        if(this.files[0].type != 'image/png' && this.files[0].type != 'image/jpg' && this.files[0].type != 'image/jpeg' && this.files[0].type != 'image/gif' )return;
        if (this.files.length === 0) return;
        $('#loadingToast').show();
        $('.weui_uploader_status_content').show();
        $('#ChildProfileBasic_photo').addClass('weui_uploader_status');
        // lrz(file, [options]);
        // file 通过 input:file 得到的文件，或者直接传入图片路径
        // [options] 这个参数允许忽略
        // width {Number} 图片最大不超过的宽度，默认为原图宽度，高度不设时会适应宽度。
        // height {Number} 同上
        // quality {Number} 图片压缩质量，取值 0 - 1，默认为0.7
        lrz(this.files[0], {width:200})
            .then(function (rst) {
                // 处理成功会执行
                $('#ChildProfileBasic_photo').css('background-image', 'url(' +rst.base64+ ')');
                upload(rst.file);
            })
            .catch(function (err) {
                // 处理失败会执行
                $('#loadingToast').hide();
            })
            .always(function () {
                // 不管是成功失败，都会执行
            });
    });
    //上传图片
    function upload(file) {
        formData = new FormData();
        formData.append('ChildProfileBasic[uploadPhoto]', file);
        $('#loadingToast').show();
        $.ajax({
            type:'POST',
            url:'<?php echo $this->createUrl('uploadPhoto', array('childid'=>$childObj->childid)); ?>',
            dataType: 'json',
            data: formData,
            contentType: false,
            processData: false,
            xhr: function() {
                var xhr = $.ajaxSettings.xhr();
                //绑定上传进度的回调函数
            　　xhr.upload.onprogress = function (e) {
            　　　　if (e.lengthComputable) {
            　　　　　　 var complete = (e.loaded / e.total * 100 | 0);
                        progress = $('.weui_uploader_status_content');
                        pecent = ~~(100 * e.loaded / e.total);
                        progress.html(pecent-1 + "%");
                    }
                };
                return xhr;
            },
            success:function (data) {
                if(data && data.state == 'success'){
                    $('#ChildProfileBasic_photo').removeClass('weui_uploader_status');
                    $('.weui_uploader_status_content').hide();
                    showMessage();
                } else{
                    $('.weui_uploader_status_content').html('<i class="weui_icon_warn"></i>');
                }
            },
            error:function () {
                $('.weui_uploader_status_content').html('<i class="weui_icon_warn"></i>');
            },
            complete:function () {
                $('#loadingToast').hide();
            }
        });
    }
</script>