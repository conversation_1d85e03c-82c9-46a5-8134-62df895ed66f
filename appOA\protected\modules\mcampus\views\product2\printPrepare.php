<style>
    .container-fluid {
        width: 860px;
        margin: 0 auto
    }
    body {
        font-family: 'SimHei', 'Microsoft JhengHei', 'Arial Unicode MS', sans-serif;
    }
    @media print {
        table {
            orphans: 0; /* 避免表格的第一行出现在新页 */
            widows: 0; /* 避免表格的最后一行出现在前一页 */
        }
    }
    table {
        width: 100%;
    }

    th, td {
        border: 1px solid #ddd;
        padding: 5px;
    }

    th {
        background-color: #f2f2f2;
    }
    .li-num {
        list-style-type: decimal;
        font-size: 14px;
    }
    .th-none {
        background-color: transparent; /* 去掉背景颜色 */
        border-top: none; /* 移除顶部边框 */
        border-bottom: none; /* 移除底部边框 */
    }
    .td-none {
        background-color: transparent; /* 去掉背景颜色 */
        border-top: none; /* 移除顶部边框 */
        border-bottom: none; /* 移除底部边框 */
    }
    .td-border-none{
        border: none;
    }
    .font14{
        font-size: 14px;
    }
    .title{
        display:block;
        text-align: center;
        font-size: 20px;
        font-weight: bold;
    }
    .order-id{
        display:block;
        text-align: right;
        font-size: 18px;
        font-weight: bold;
    }
    .party{
        display:block;
        font-size: 16px;
        font-weight: bold;
    }
</style>
<div class="container-fluid">
    <table class="second-table">
        <thead>
        <tr>
            <td colspan="7" style="border:none"><span class="title">采购&验收单</span></td>
        </tr>
        <tr>
            <td colspan="7" style="border:none"><span class="order-id">采购订单号：<?php echo $order_id;?></span></td>
        </tr>
        <tr>
            <td class="td-border-none" colspan="7"><span class="party">订购校园（甲方）：<u><?php echo $party_a;?></u></span></td>
        </tr>
        <tr>
            <td style="border: none;"  colspan="7"><span class="party">供应方（乙方）：&nbsp;&nbsp;<u><?php echo $party_b;?></u></span></td>
        </tr>
        <tr>
            <td class="font14 td-border-none" style="border-right: none" colspan="5">一 品名、规格、数量</td>
        </tr>
        <tr>
            <th style="text-align: center;">#</th>
            <th style="text-align: center">商品</th>
            <th style="text-align: center">颜色</th>
            <th style="text-align: center">尺寸</th>
            <th style="text-align: center">数量</th>
            <th style="text-align: center;border-right-style: dashed;" class="th-none"></th>
            <th style="text-align: center;width: 150px; border-style: dashed;">终端号（校园使用）</th>
        </tr>
        </thead>
        <tbody>
        <?php
        foreach ($product_info as $k=>$item) { ?>
                <tr>
                    <td style="text-align: center"><?php echo $k+1;?></td>
                    <td style="text-align: center"><?php echo $item['title'] ?></td>
                    <td style="text-align: center"><?php echo $item['color'] ?></td>
                    <td style="text-align: center"><?php echo $item['size'] ?></td>
                    <td style="text-align: center"><?php echo $item['num'] ?></td>
                    <td style="text-align: center;border-right-style: dashed;" class="td-none"></td>
                    <?php if($k == 0){?>
                        <td style="vertical-align: top; text-align: center;border-style: dashed;" rowspan="<?php echo count($product_info)+1?>">
                            <?php foreach ($order_list as $order_id){?>
                                <?php echo $order_id;?> <br>
                            <?php }?>
                        </td>
                    <?php }?>
                </tr>
        <?php } ?>
        <tr>
            <td colspan="4" style="text-align: right">总计</td>
            <!-- 总金额需要计算得出 -->
            <td style="text-align: center">
                <?php $num=0;?>
                <?php foreach ($product_info as $item){?>
                    <?php $num+=$item['num'];?>
                <?php }?>
                <?php echo $num;?>
            </td>
        </tr>
        </tbody>
    </table>

    <h4>二 交货信息、交货地点：</h4>
    <ul>
        <li class="li-num"><h4>交货日期：<?php echo $delivery_detail['time'];?></h4></li>
        <li class="li-num"><h4>交货地点：<?php echo $delivery_detail['address'];?></h4></li>
    </ul>
    <h4>三 <span class="font14">本订单所属的采购框架合同按约定的相关条款约束和保护</span>
    <h4>四 校园验收人联系电话：<span class="font14"><?php echo $acceptor_detail;?></span></h4>
    <table style="page-break-inside: avoid;">
        <tr>
            <td colspan="2">（无差异时无需填写此栏）</td>
        </tr>
        <tr>
            <td style="width: 200px;padding: 10px">差异具体说明</td>
            <td></td>
        </tr>
        <tr>
            <td style="width: 200px;padding: 10px">验收人/日期</td>
            <td></td>
        </tr>
    </table>
</div>
