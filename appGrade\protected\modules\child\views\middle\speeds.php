
<div class="span-18 last">
    <!--place holder-->
    <div id="pfolio-sub-nav">
        <ul class="pf-sub-nav">
            <?php if($inforObj): ?>
                <?php foreach($inforObj as $item): ?>
                    <li class="<?php echo($pid == $item->id)? 'active' : '' ?>"><?php echo CHtml::link($item->getName(), array('//child/middle/speed', 'id' => $id ,'pid' =>$item->id ))?></li>
                <?php endforeach; ?>
            <?php endif; ?>
        </ul>
    </div>

    <!--place holder-->
</div>
<?php

$this->widget('zii.widgets.grid.CGridView', array(
    'cssFile'=>Yii::app()->theme->baseUrl.'/widgets/gridview/styles.css',
    'dataProvider'=> $depositList,
    'enableSorting'=>false,
    'pager'=>array(
        'class'=>'CLinkPager',
        'cssFile'=>Yii::app()->theme->baseUrl.'/css/pager.css',
    ),
    'columns'=>array(
        array(
            'name' => Yii::t('payment','Title'),
            'type' => 'raw',
            'htmlOptions' => array("class" => "alignLeft"),
            'value' => 'CHtml::link($data->getName("title"), $data->getName("file"), array("target"=>"_black"))',
        ),
        array(
            'name' => 'times',
            'htmlOptions' => array("style" => "width:100px;"),
            'value'  => 'date("Y-m-d" , $data->times)',
        ),
    ),
    'ajaxUpdate'=>false,
    'template'=>"{summary}{pager}{items}{summary}{pager}"
));
?>