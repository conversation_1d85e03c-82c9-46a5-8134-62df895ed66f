<div class="modal-header">
	<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
	<h4 class="modal-title"><?php echo $model->id?'更新标配：':'新建标配：'; ?></h4>
</div>
<?php $form=$this->beginWidget('CActiveForm', array(
	'id'=>'purchase-products-form',
	'enableAjaxValidation'=>false,
	'htmlOptions'=>array('class'=>'J_ajaxForm form-horizontal', 'role'=>'form'),
)); ?>

	<div class="modal-body">
		<div class="form-group">
		    <label class="col-xs-2 control-label"><?php echo $form->labelEx($model,'cn_title'); ?></label>
		    <div class="col-xs-8">
		        <?php echo $form->textField($model,'cn_title',array('maxlength'=>255,'class'=>'form-control')); ?>
		    </div>
		</div>

		<div class="form-group">
		    <label class="col-xs-2 control-label"><?php echo $form->labelEx($model,'school_type'); ?></label>
		    <div class="col-xs-8">
		        <?php echo $form->textField($model,'school_type',array('maxlength'=>255,'class'=>'form-control')); ?>
		    </div>
		</div>

		<div class="form-group">
		    <label class="col-xs-2 control-label"><?php echo $form->labelEx($model,'pro_type'); ?></label>
		    <div class="col-xs-8">
		        <?php echo $form->textField($model,'pro_type',array('maxlength'=>255,'class'=>'form-control')); ?>
		    </div>
		</div>
		
		<div class="form-group">
			<label class="col-xs-2 control-label"><?php echo $form->labelEx($model,'status'); ?></label>
			<div class="col-xs-8">
				<?php echo $form->dropDownList($model,'status', array('不可用','可用') , array('class'=>'form-control', 'empty'=>Yii::t("global", 'Please Select'))); ?>
			</div>
		</div>

		<div class="form-group">
		    <label class="col-xs-2 control-label"><?php echo $form->labelEx($model,'tip'); ?></label>
		    <div class="col-xs-8">
		        <?php echo $form->textArea($model,'tip',array('maxlength'=>255,'class'=>'form-control')); ?>
		    </div>
		</div>
	</div>
	<div class="modal-footer">
		<label class="col-xs-2 control-label"></label>
		<button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global','Submit');?></button>
		<button type="button" class="btn btn-default"  data-dismiss="modal"><?php echo Yii::t('global','Cancel');?></button>
	</div>

<?php $this->endWidget(); ?>

<script>
	//监听模态框
	$(function () { $('#modal').on('hide.bs.modal', function () {
	    $('.modal-body').empty();})
	  });
</script>
