<?php echo $this->renderPartial('_nouser_header'); ?>
<div class="page__bd page__bd_spacing">
    <div class="weui-cells__title">
        <?php echo Yii::t("bind","Please use your Ivyonline/Daystar registered phone number to verify");?>
    </div>
    <?php $form = $this->beginWidget('CActiveForm', array(
        'id' => 'login-form',
    )); ?>
    <div class="weui-cells weui-cells_form">
        <div class="weui-cell weui-cell_vcode">
            <div class="weui-cell__hd"><?php echo CHtml::activeLabel($model, 'phone', array('class' => 'weui-label'))?></div>
            <div class="weui-cell__bd">
                <?php echo CHtml::activeNumberField($model, 'phone', array(
                    'class' => 'weui-input',
                    'placeholder' => Yii::t("bind","Your phone number"),
                    'required' => true))?>
            </div>
            <div class="weui-cell__ft">
                <button type="button" class="weui-vcode-btn" data-link="<?php echo $this->createUrl('/wechat/bind/vcode', array('state'=>$this->account));?>">
                    <span class="vcodeText"><?php echo Yii::t("bind","Send");?></span> <span class="countDown"></span></button>
            </div>
        </div>
        <div class="weui-cell">
            <div class="weui-cell__hd"><?php echo CHtml::activeLabel($model, 'code', array('class' => 'weui-label'))?></div>
            <div class="weui-cell__bd">
                <?php echo CHtml::activeNumberField($model, 'code', array('class' => 'weui-input', 'placeholder' => yii::t('bind', 'The verification code'), 'required' => true))?>
            </div>
        </div>
    </div>
    <div class="weui-btn-area">
        <button type="submit" class="weui-btn weui-btn_primary"><?php echo Yii::t("bind","Submit");?></button>
    </div>

    <article class="weui-article">
        <p>
            <?php echo CHtml::link(
                Yii::t("bind","Log in via Ivyonline/Daystar account"),
                array('/wechat/bind/account'))?>
        </p>
    </article>

    <?php $this->endWidget(); ?>
</div>

<?php echo $this->renderPartial('_nouser_footer'); ?>
