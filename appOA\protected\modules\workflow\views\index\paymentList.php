<style>
    .print-only {
        display: none;
        /* 在屏幕上隐藏 */
    }

    @media print {
        .print-only {
            display: block;
            /* 在打印时显示 */
        }
    }
</style>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Campus Workspace'), array('//mcampus/default/index')) ?></li>
        <li><?php echo CHtml::link(Yii::t('site', 'Workflow Workspace'), array('//workflow/index/index')) ?></li>
        <li class="active">申请列表</li>
    </ol>

    <div class="row">
        <!-- 左侧菜单 -->
        <div class="col-md-2 col-sm-2 mb10">
            <ul class="nav nav-pills nav-stacked background-gray" id="pageCategory">
                <?php foreach ($wordDlowObj as $item) { ?>
                    <li class="<?php echo ($item->defination_id == $definationId) ? "active" : ""; ?>"><?php echo CHtml::link((Yii::app()->language == 'zh_cn' ? $item->defination_name_cn : $item->defination_name_en), array('//workflow/index/workflowCount', 'definationId' => $item->defination_id)) ?></a></li>
                <?php } ?>
            </ul>

        </div>
        <?php if ($dataProvider) { ?>

            <div class="col-md-10 col-sm-12">
                <div class="mb10">
                    <!-- 搜索框 -->
                    <form id="searchForm" action="<?php echo $this->createUrl('workflowCount'); ?>" method="get" class="form-inline">
                        <?php echo Chtml::hiddenField('definationId', $definationId); ?>
                        <?php echo Chtml::hiddenField('branchId', $branchId); ?>

                        <div class="form-group">
                            <?php echo CHtml::dropDownList('currentWorkflowId', $currentWorkflowId, $paymentWorkflowList, array('class' => 'form-control', 'empty' => '全部类型')); ?>
                        </div>

                        <div class="form-group">
                            <?php echo CHtml::dropDownList('currentState', $currentState, $stateList, array('class' => 'form-control', 'empty' => '全部状态')); ?>
                        </div>

                        <div class="form-group">
                            <?php echo CHtml::dropDownList('currentDate', $currentDate, $dateList, array('class' => 'form-control', 'empty' => '快速筛选')); ?>
                        </div>

                        <div class="form-group">
                            <?php echo Chtml::textField('title', $_GET['title'], array('class' => 'form-control', 'placeholder' => '申请标题')); ?>
                        </div>

                        <!-- <div class="form-group">
                            <input type="text" class="form-control datepicker start_time" autocomplete="off" placeholder="申请日期开始" name="start_time" value="<?php echo $start_time ? $start_time : ''; ?>">
                        </div>

                        <div class="form-group">
                            <input type="text" class="form-control datepicker end_time" autocomplete="off" placeholder="申请日期结束" name="end_time" value="<?php echo $end_time ? $end_time : ''; ?>">
                        </div> -->

                        <div class="form-group">
                            <button class="btn btn-default ml5" type="button" id='buttonsa'><span class="glyphicon glyphicon-search"> </span> </button>
                        </div>
                    </form>
                </div>

                <div class="mb10">
                    <?php if ($this->applyPermission()) : ?><button id="applyModelBtn" class="btn btn-primary">申请付款</button> <?php endif; ?>
                    <?php if ($this->confirmPermission()) : ?><button id="confirmModelBtn" class="btn btn-primary">确认付款</button> <?php endif; ?>
                </div>
                <div class="col-md-12 mb5"></div>
                <div class="panel panel-default">
                    <div class="panel-body">
                        <?php
                        $this->widget('ext.ivyCGridView.BsCGridView', array(
                            'id' => 'paymentList',
                            'afterAjaxUpdate' => 'js:function(){head.Util.modal;head.Util.ajax($("#paymentList"))}',
                            'dataProvider' => $dataProvider,
                            'template' => "{items}{pager}{summary}",
                            'colgroups' => array(
                                array(
                                    "colwidth" => array(10, 80, 10, 10, 10, 10, 10, 30, 10, 10, 30, 10),
                                )
                            ),
                            'columns' => array(
                                array(
                                    'class' => 'CCheckBoxColumn', // 指定这一列使用复选框
                                    'selectableRows' => 2, // 允许选择多行
                                    'disabled' => array($this, 'disabled'),
                                    'headerHtmlOptions' => array(
                                        'class' => 'items', // 指定全选复选框的 CSS 类
                                    ),
                                    'checkBoxHtmlOptions' => array(
                                        'name' => 'itemsSelected', // 可以指定复选框的名字
                                        'class' => 'items',
                                    ),
                                ),
                                array(
                                    'name' => '标题',
                                    'type' => 'raw',
                                    'value' => array($this, 'getTitle'),
                                ),
                                array(
                                    'name' => '状态',
                                    'type' => 'raw',
                                    'value' => array($this, 'getState'),
                                ),
                                // array(
                                //     'name' => '预算',
                                //     'value' => '$data->payment->budget == 1 ? "预算中" : "预算外";'
                                // ),
                                array(
                                    'name' => Yii::t('asa', '工作流提交'),
                                    'value' => array($this, 'getWorkflowUser'),
                                ),
                                array(
                                    'name' => '金额',
                                    'value' => 'number_format($data->payment->price_total / 100, 2)'
                                ),
                                array(
                                    'name' => Yii::t('asa', '付款申请'),
                                    'value' => array($this, 'getApplyUser'),
                                ),
                                array(
                                    'name' => '申请时间',
                                    'value' => array($this, 'getApplyDate'),
                                ),
                                array(
                                    'name' => Yii::t('asa', '申请备注'),
                                    'value' => '$data->payment->apply_note',
                                ),
                                array(
                                    'name' => Yii::t('asa', '付款确认'),
                                    'value' => array($this, 'getConfirmUser'),
                                ),
                                array(
                                    'name' => '付款时间',
                                    'value' => array($this, 'getPaymentDate'),
                                ),
                                array(
                                    'name' => '付款备注',
                                    'value' => '$data->payment->payment_note;'
                                ),
                                array(
                                    'name' => Yii::t('asa', '操作'),
                                    'value' => array($this, 'getButton'),
                                ),
                            ),
                        ));
                        ?>
                    </div>
                </div>
            <?php } ?>
            </div>
    </div>
    <div id="workflow-modal-template"></div>
    <div id="workflow-modal-template2"></div>


    <!-- Modal -->
    <div class="modal fade" id="applyModal" tabindex="-1" role="dialog" aria-labelledby="applyModalLabel" data-backdrop="static">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="applyModalLabel">申请付款</h4>
                </div>
                <div class="modal-body">
                    <h4>申请付款数量：<span id="applyTotalNum"></span></h4>
                    <h4>申请付款总金额：<span id="applyTotalAmount"></span></h4>
                    <hr>
                    <div class="form-group">
                        <label for="applyTime">申请付款时间</label>
                        <input type="text" class="form-control datepicker" id="applyTime" placeholder="申请付款时间">
                    </div>

                    <div class="form-group">
                        <label for="applyTime">申请付款备注</label>
                        <textarea class="form-control" id="applyNote" placeholder="申请付款备注"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="applyBtn">确认</button>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="confirmModal" tabindex="-1" role="dialog" aria-labelledby="confirmModalLabel" data-backdrop="static">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="confirmModalLabel">确认付款</h4>
                </div>
                <div class="modal-body">
                    <h4>申请付款时间：<span id="applyDate"></span></h4>
                    <h4>申请付款数量：<span id="totalNum"></span></h4>
                    <h4>申请付款总金额：<span id="totalAmount"></span></h4>
                    <hr>
                    <div id="confirmForm">
                        <div class="form-group">
                            <label for="paymentTime">确认付款时间</label>
                            <input type="text" class="form-control datepicker" id="paymentTime" placeholder="付款时间">
                        </div>

                        <div class="form-group">
                            <label for="paymentTime">确认付款备注</label>
                            <textarea class="form-control" id="paymentNote" placeholder="付款备注"></textarea>
                        </div>
                        <button type="button" class="btn btn-primary" id="confirmBtn">确认</button>
                        <button type="button" class="btn btn-primary" id="downloadBtn" onclick="downloadBatch(this)" style="display: none;">下载</button>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                    <!-- <button type="button" class="btn btn-primary" id="confirmBtn">确认</button> -->
                </div>
            </div>
        </div>
    </div>

    <!-- 追加备注 Modal -->
    <div class="modal fade" id="noteModal" tabindex="-1" role="dialog" aria-labelledby="noteModalLabel" data-backdrop="static">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="noteModalLabel">追加备注</h4>
                </div>
                <div class="modal-body">
                    <!-- Log Table -->
                    <table class="table">
                        <thead>
                            <tr>
                                <th scope="col">操作人</th>
                                <th scope="col">操作时间</th>
                                <th scope="col">备注</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Log entries will go here -->
                        </tbody>
                    </table>

                    <!-- Input for new remark -->
                    <div class="form-group">
                        <label for="newRemark">追加备注：</label>
                        <textarea class="form-control" id="newNote" placeholder="备注"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="noteBtn">确认</button>
                </div>
            </div>
        </div>
    </div>


    <!-- 同批次付款查看 -->
    <div class="modal fade" id="batchModel" tabindex="-1" role="dialog" aria-labelledby="batchModelLabel">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="batchModelLabel">同批次付款 <a href="#" onclick="print('batchModel')">[打印]</a></h4>
                </div>
                <div class="modal-body">
                    <div class="printContent">
                        <!-- Log Table -->
                        <div class="form-group">
                            <h4 class="print-only">同批次付款</h4>
                            <p>付款时间：<span id="paymentDate"></span></p>
                            <p>付款金额：<span id="paymentAmount"></span></p>
                            <p>付款人：<span id="paymentUser"></span></p>
                        </div>
                        <table class="table" id="batchTable">
                            <thead>
                                <tr>
                                    <th scope="col">申请标题</th>
                                    <th scope="col">开户行</th>
                                    <th scope="col">银行账号</th>
                                    <th scope="col">收款人</th>
                                    <th scope="col">金额</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Log entries will go here -->
                            </tbody>
                        </table>
                    </div>

                    <!-- Input for new remark -->
                    <div class="form-group">
                        <button class="btn btn-primary" onclick="downloadBatch(this)">下载详情</button>
                        <button id="cancelConfirmBtn" class="btn btn-danger" onclick="cancelConfirm(this)">取消付款</button>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 同批次申请查看 -->
    <div class="modal fade" id="batchApplyModel" tabindex="-1" role="dialog" aria-labelledby="batchApplyModelLabel">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="batchApplyModelLabel">同批次申请<a href="#" onclick="print('batchApplyModel')">[打印]</a></h4>
                </div>
                <div class="modal-body">
                    <div class="printContent">
                        <di class="form-group">
                            <h4 class="print-only">同批次申请</h4>
                            <p>付款申请时间：<span id="applyDate2"></span></p>
                            <p>付款申请金额：<span id="applyAmount"></span></p>
                            <p>付款申请人：<span id="applyUser"></span></p>
                        </div>
                        <!-- Log Table -->
                        <table class="table" id="batchApplyTable">
                            <thead>
                                <tr>
                                    <th scope="col">申请标题</th>
                                    <th scope="col">开户行</th>
                                    <th scope="col">银行账号</th>
                                    <th scope="col">收款人</th>
                                    <th scope="col">金额</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Log entries will go here -->
                            </tbody>
                        </table>
                    </div>

                    <!-- Input for new remark -->
                    <div class="form-group">
                        <button id="cancelBatchApplyBtn" class="btn btn-danger" onclick="cancelBatchApply(this)">取消全部申请</button>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        var payment_id;
        var apply_batch_id = 0;
        var confirm_batch_id = 0;
        var excelData = [];
        $('.datepicker').datepicker({
            changeMonth: true,
            changeYear: true,
            dateFormat: 'yy-mm-dd'
        });

        downloadBatch = function(e) {
            if (confirm_batch_id == 0) {
                resultTip({
                    msg: "请选择要下载的付款申请",
                    error: 'warning'
                });
                return;
            }
            // Button disabled for three seconds
            $(e).prop('disabled', true);
            setTimeout(function() {
                $(e).prop('disabled', false);
            }, 3000);

            // 生成下载表格
            const workbook = generateSheet();

        }
        $("#confirmModelBtn").click(function() {
            confirm_batch_id = 0;
            $(this).prop('disabled', true);
            $("#downloadBtn").hide();

            var totalNum = 0; // 初始化总数量0
            var totalAmount = 0; // 初始化总金额为0
            var applyDate = [];
            var operationIdList = $('input[name="itemsSelected"]:checked').map(function() {
                totalNum++;
                // 获取当前复选框所在行的金额列的文本
                var amountText = $(this).closest('tr').find('td').eq(4).text(); // 假设金额总是在第四列
                // 移除金额中的逗号（如果有的话）并转换为浮点数
                var amount = parseFloat(amountText.replace(/,/g, ''));
                // 检查转换是否成功（不是NaN），然后累加到总金额
                if (!isNaN(amount)) {
                    totalAmount += amount;
                }
                let date = $(this).closest('tr').find('td').eq(6).text();
                if (!applyDate.includes(date)) {
                    applyDate.push(date);
                }
                return this.value;
            }).get();
            if (operationIdList.length == 0) {
                return resultTip({
                    msg: '至少选择一条数据',
                    error: 'warning'
                });
            }
            const totalAmountStr = formatAmount(totalAmount);
            $('#applyDate').text(applyDate.join(', '));
            $("#totalNum").text(totalNum);
            $("#totalAmount").text(totalAmountStr);
            $("#confirmModal").modal("show")
        });

        $("#applyModelBtn").click(function() {
            var totalNum = 0; // 初始化总数量0
            var totalAmount = 0; // 初始化总金额为0
            var operationIdList = $('input[name="itemsSelected"]:checked').map(function() {
                totalNum++;
                // 获取当前复选框所在行的金额列的文本
                var amountText = $(this).closest('tr').find('td').eq(4).text(); // 假设金额总是在第四列
                // 移除金额中的逗号（如果有的话）并转换为浮点数
                var amount = parseFloat(amountText.replace(/,/g, ''));
                // 检查转换是否成功（不是NaN），然后累加到总金额
                if (!isNaN(amount)) {
                    totalAmount += amount;
                }
                return this.value;
            }).get();
            if (operationIdList.length == 0) {
                return resultTip({
                    msg: '至少选择一条数据',
                    error: 'warning'
                });
            }
            const totalAmountStr = formatAmount(totalAmount);
            $("#applyTotalNum").text(totalNum);
            $("#applyTotalAmount").text(totalAmountStr);
            $("#applyModal").modal("show")
        });

        $("#buttonsa").click(function() {
            var startTime = $(".start_time").val();
            var endTime = $(".end_time").val()
            if (startTime && endTime) {
                var startTimeData = DateToUnix(startTime);
                var startEndData = DateToUnix(endTime);
                if (startTimeData > startEndData) {
                    resultTip({
                        msg: '结束时间不能大于开始时间',
                        error: 'warning'
                    });
                } else if ((startEndData > startTimeData + 86400 * 180)) {
                    resultTip({
                        msg: '开始时间和结束时间最多相差180天',
                        error: 'warning'
                    });
                } else {
                    $("#searchForm").submit();
                }
            } else {
                $("#searchForm").submit();
            }
        });

        $("#confirmBtn").click(function() {
            // 获取所有选中的复选框的值
            var operationIdList = $('input[name="itemsSelected"]:checked').map(function() {
                return this.value;
            }).get();
            var paymentTime = $("#paymentTime").val();
            var paymentNote = $("#paymentNote").val();
            // operationIdList
            if (operationIdList.length == 0) {
                return resultTip({
                    msg: '至少选择一条数据',
                    error: 'warning'
                });
            }
            if (!paymentTime) {
                return resultTip({
                    msg: '请选择付款时间',
                    error: 'warning'
                });
            }
            $(this).prop('disabled', true);
            // 使用ajax发送POST请求
            $.ajax({
                url: '<?php echo $this->createUrl('paymentConfirm'); ?>',
                type: 'POST',
                dataType: 'json',
                data: {
                    operationIdList,
                    paymentTime,
                    paymentNote
                },
                success: function(response) {
                    if (response.state == 'success') {
                        $("#downloadBtn").show();
                        confirm_batch_id = response.data
                        resultTip({
                            msg: response.message,
                            error: 'success'
                        });
                        refreshTable();
                    } else {
                        resultTip({
                            msg: response.message,
                            error: 'warning'
                        });
                    }

                },
                error: function(xhr, status, error) {
                    // 请求失败的回调函数
                    resultTip({
                        msg: error,
                        error: 'warning'
                    });
                },
                complete: function(jqXHR, textStatus) {
                    // 请求成功后的回调函数
                }
            });
        });
        $("#applyBtn").click(function() {
            // 获取所有选中的复选框的值
            var operationIdList = $('input[name="itemsSelected"]:checked').map(function() {
                return this.value;
            }).get();
            var applyTime = $("#applyTime").val();
            var applyNote = $("#applyNote").val();
            // operationIdList
            if (operationIdList.length == 0) {
                return resultTip({
                    msg: '至少选择一条数据',
                    error: 'warning'
                });
            }
            if (!applyTime) {
                return resultTip({
                    msg: '请选择付款时间',
                    error: 'warning'
                });
            }
            $(this).prop('disabled', true);
            // 使用ajax发送POST请求
            $.ajax({
                url: '<?php echo $this->createUrl('paymentApply'); ?>',
                type: 'POST',
                dataType: 'json',
                data: {
                    operationIdList,
                    applyTime,
                    applyNote
                },
                success: function(response) {
                    $(this).prop('disabled', false);
                    if (response.state == 'success') {
                        resultTip({
                            msg: response.message,
                            error: 'success'
                        });
                        $("#applyModal").modal("hide")
                    } else {
                        resultTip({
                            msg: response.message,
                            error: 'warning'
                        });
                    }

                },
                error: function(xhr, status, error) {
                    // 请求失败的回调函数
                    resultTip({
                        msg: error,
                        error: 'warning'
                    });
                },
                complete: function(jqXHR, textStatus) {
                    // 请求成功后的回调函数
                    refreshTable();
                }
            });
            $(this).prop('disabled', false);
        });

        $("#noteBtn").click(function() {
            // 获取所有选中的复选框的值
            var paymentIdList = [payment_id]
            var addNote = $("#newNote").val();
            // paymentIdList
            if (paymentIdList.length == 0) {
                return resultTip({
                    msg: '至少选择一条数据',
                    error: 'warning'
                });
            }
            if (!addNote) {
                return resultTip({
                    msg: '请输入备注',
                    error: 'warning'
                });
            }
            $(this).prop('disabled', true);
            // 使用ajax发送POST请求
            $.ajax({
                url: '<?php echo $this->createUrl('paymentAdd'); ?>',
                type: 'POST',
                dataType: 'json',
                data: {
                    paymentIdList,
                    addNote
                },
                success: function(response) {
                    $(this).prop('disabled', false);
                    if (response.state == 'success') {
                        resultTip({
                            msg: response.message,
                            error: 'success'
                        });
                        $("#noteModal").modal("hide")
                    } else {
                        resultTip({
                            msg: response.message,
                            error: 'warning'
                        });
                    }

                },
                error: function(xhr, status, error) {
                    // 请求失败的回调函数
                    resultTip({
                        msg: error,
                        error: 'warning'
                    });
                },
                complete: function(jqXHR, textStatus) {
                    // 请求成功后的回调函数
                    refreshTable();
                }
            });
            $(this).prop('disabled', false);
        });

        function showNote(id) {
            payment_id = id;
            $("#noteModal").modal("show")
            $.ajax({
                url: '<?php echo $this->createUrl('getPaymentNote'); ?>',
                type: 'POST',
                dataType: 'json',
                data: {
                    id
                },
                success: function(response) {
                    if (response.state == 'success') {
                        // Clear the table body
                        $('#noteModal .table tbody').empty();

                        // Iterate over each log entry
                        $.each(response.data, function(i, log) {
                            // Create a new table row
                            var newRow = $('<tr>');

                            // Append the operator, time and remark to the row
                            newRow.append('<td>' + log.name + '</td>');
                            newRow.append('<td>' + log.time + '</td>');
                            newRow.append('<td>' + log.note + '</td>');

                            // Append the row to the table body
                            $('#noteModal .table tbody').append(newRow);
                        });
                    }

                },
            });
        }

        function cancelApply(id) {
            var userConfirmed = confirm("确认取消？");

            if (userConfirmed) {
                var paymentIdList = [id]
                $.ajax({
                    url: '<?php echo $this->createUrl('cancelApply'); ?>',
                    type: 'POST',
                    dataType: 'json',
                    data: {
                        paymentIdList
                    },
                    success: function(response) {
                        // 请求成功后的回调函数
                        if (response.state == 'success') {
                            resultTip({
                                msg: response.message,
                                error: 'success'
                            });
                            refreshTable();
                        } else {
                            resultTip({
                                msg: response.message,
                                error: 'warning'
                            });
                        }
                    },
                    error: function(xhr, status, error) {
                        // 请求失败的回调函数
                        resultTip({
                            msg: error,
                            error: 'warning'
                        });
                    },
                    complete: function(jqXHR, textStatus) {
                        // 请求成功后的回调函数
                        // refreshTable();
                    }
                });
            } else {
                // User clicked "Cancel"
            }
        }

        function cancelConfirm(e) {
            if (confirm_batch_id == 0) {
                resultTip({
                    msg: "数据错误",
                    error: 'warning'
                });
            }
            var userConfirmed = confirm("确认取消？");

            if (userConfirmed) {
                $(e).prop('disabled', true);
                $.ajax({
                    url: '<?php echo $this->createUrl('cancelConfirm'); ?>',
                    type: 'POST',
                    dataType: 'json',
                    data: {
                        confirm_batch_id
                    },
                    success: function(response) {
                        if (response.state == 'success') {
                            resultTip({
                                msg: response.message,
                                error: 'success'
                            });

                            $(e).prop('disabled', false);
                            $("#batchModel").modal('hide');
                            // 请求成功后的回调函数
                            refreshTable();
                        } else {
                            resultTip({
                                msg: response.message,
                                error: 'warning'
                            });
                        }
                    },
                    error: function(xhr, status, error) {},
                    complete: function(jqXHR, textStatus) {}
                });
            } else {
                // User clicked "Cancel"
            }
        }

        function cancelBatchApply(e) {
            if (apply_batch_id == 0) {
                resultTip({
                    msg: "数据错误",
                    error: 'warning'
                });
            }
            var userConfirmed = confirm("确认取消？");

            if (userConfirmed) {
                $(e).prop('disabled', true);
                $.ajax({
                    url: '<?php echo $this->createUrl('cancelApplyBatch'); ?>',
                    type: 'POST',
                    dataType: 'json',
                    data: {
                        apply_batch_id
                    },
                    success: function(response) {
                        if (response.state == 'success') {
                            if (response.state == 'success') {

                            }
                            $(e).prop('disabled', false);
                            $("#batchApplyModel").modal('hide');
                            // 请求成功后的回调函数
                            refreshTable();
                        } else {
                            resultTip({
                                msg: response.message,
                                error: 'warning'
                            });
                        }
                    },
                    error: function(xhr, status, error) {},
                    complete: function(jqXHR, textStatus) {}

                });
            } else {
                // User clicked "Cancel"
            }
        }

        function viewApply(batchId) {
            apply_batch_id = batchId;
            $('#batchApplyModel').modal('show');

            // 使用ajax发送POST请求
            $.ajax({
                url: '<?php echo $this->createUrl('applyBatchData'); ?>',
                type: 'POST',
                dataType: 'json',
                data: {
                    apply_batch_id,
                },
                success: function(response) {
                    if (response.state == 'success') {
                        $("#batchApplyTable tbody").empty(); // 清空表格内容
                        excelData = response.data;
                        $('#applyDate2').text(response.data.apply_date);
                        $('#applyUser').text(response.data.apply_user);
                        $('#applyAmount').text(formatAmount(response.data.apply_total));
                        $.each(response.data.items, function(index, row) {
                            // 创建新行并插入单元格
                            var newRow = $("<tr>");
                            newRow.append($("<td>").text(row.title));
                            newRow.append($("<td>").text(row.bank_name));
                            newRow.append($("<td>").text(row.bank_account));
                            newRow.append($("<td>").text(row.bank_user));
                            newRow.append($("<td>").text(formatAmount(row.amount)));

                            // 将新行添加到表格中
                            $("#batchApplyTable tbody").append(newRow);
                        });
                    } else {
                        resultTip({
                            msg: response.message,
                            error: 'warning'
                        });
                    }

                },
                error: function(xhr, status, error) {
                    // 请求失败的回调函数
                    resultTip({
                        msg: error,
                        error: 'warning'
                    });
                },
                complete: function(jqXHR, textStatus) {
                    // 请求成功后的回调函数
                }
            });
        }

        function viewBatch(batchId) {
            confirm_batch_id = batchId;
            $('#batchModel').modal('show');
            $('#cancelConfirmBtn').prop('disabled', false);

            // 使用ajax发送POST请求
            $.ajax({
                url: '<?php echo $this->createUrl('confirmBatchData'); ?>',
                type: 'POST',
                dataType: 'json',
                data: {
                    confirm_batch_id,
                },
                success: function(response) {
                    if (response.state == 'success') {
                        $("#batchTable tbody").empty(); // 清空表格内容
                        excelData = response.data;
                        $('#paymentDate').text(response.data.payment_date);
                        $('#paymentUser').text(response.data.payment_user);
                        $('#paymentAmount').text(formatAmount(response.data.payment_total));
                        $.each(response.data.items, function(index, row) {
                            // 创建新行并插入单元格
                            var newRow = $("<tr>");
                            newRow.append($("<td>").text(row.title));
                            newRow.append($("<td>").text(row.bank_name));
                            newRow.append($("<td>").text(row.bank_account));
                            newRow.append($("<td>").text(row.bank_user));
                            newRow.append($("<td>").text(formatAmount(row.amount)));

                            // 将新行添加到表格中
                            $("#batchTable tbody").append(newRow);
                        });
                    } else {
                        resultTip({
                            msg: response.message,
                            error: 'warning'
                        });
                    }

                },
                error: function(xhr, status, error) {
                    // 请求失败的回调函数
                    resultTip({
                        msg: error,
                        error: 'warning'
                    });
                },
                complete: function(jqXHR, textStatus) {
                    // 请求成功后的回调函数
                }
            });
        }

        function generateSheet() {
            // fetch('<?php echo Yii::app()->themeManager->baseUrl; ?>/base/js/payment_template.xls')
            //     .then(response => response.arrayBuffer())
            //     .then(data => {
            //         var workbook = XLSX.read(new Uint8Array(data), {
            //             type: 'array',
            //             bookVBA: true,
            //             cellStyles: true,
            //             cellNF: true,
            //             cellFormula: true
            //         });
            //         var encoded_vba_blob = workbook.vbaraw;
            //         console.log(encoded_vba_blob);
            //         // 获取第一个工作表
            //         var firstSheetName = workbook.SheetNames[0];
            //         var worksheet = workbook.Sheets[firstSheetName];

            //         // 将工作表转换为JSON
            //         // var json = XLSX.utils.sheet_to_json(worksheet);

            //         // 在这里处理您的模板数据
            //         // XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1');

            //         XLSX.writeFile(workbook, getExcelName() + '.xls', {
            //             bookType: 'xls',
            //             bookVBA: true,
            //             cellStyles: true,
            //             cellNF: true,
            //             cellFormula: true
            //         });
            //     })
            //     .catch(error => console.error('Error:', error));

            let newData = [];
            if (excelData.length == 0) {
                // 使用ajax发送POST请求
                $.ajax({
                    url: '<?php echo $this->createUrl('confirmBatchData'); ?>',
                    type: 'POST',
                    dataType: 'json',
                    data: {
                        confirm_batch_id,
                    },
                    success: function(response) {
                        if (response.state == 'success') {
                            excelData = response.data;
                            $.each(excelData.items, function(index, item) {
                                newData.push({
                                    "币种": "RMB",
                                    "日期": excelData.payment_date,
                                    "明细标志": "",
                                    "顺序号": index + 1,
                                    "付款账号开户行": "                                                          ",
                                    "付款账号/卡号": "",
                                    "付款账号名称/卡名称": "",
                                    "收款账号开户行": item.bank_name,
                                    "收款账号省份": "",
                                    "收款账号地市": "",
                                    "收款账号地区码": "",
                                    "收款账号": item.bank_account,
                                    "收款账号名称": item.bank_user,
                                    "金额": item.amount,
                                    "汇款用途": "",
                                    "备注信息": "",
                                    "汇款方式": "1",
                                    "收款账户短信通知手机号码": "",
                                    "自定义序号": "",
                                    "预先审批编号": "",
                                });
                            })
                            const worksheet = XLSX.utils.json_to_sheet(newData);
                            const workbook = XLSX.utils.book_new();
                            XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1');

                            downloadExcel(workbook);
                        } else {
                            resultTip({
                                msg: response.message,
                                error: 'warning'
                            });
                        }

                    },
                    error: function(xhr, status, error) {
                        // 请求失败的回调函数
                        resultTip({
                            msg: error,
                            error: 'warning'
                        });
                    },
                    complete: function(jqXHR, textStatus) {
                        // 请求成功后的回调函数
                    }
                });
            } else {
                $.each(excelData.items, function(index, item) {
                    newData.push({
                        "币种": "RMB",
                        "日期": excelData.payment_date,
                        "明细标志": "",
                        "顺序号": index + 1,
                        "付款账号开户行": "工行                                                          ",
                        "付款账号/卡号": "0200020409031495769",
                        "付款账号名称/卡名称": "",
                        "收款账号开户行": item.bank_name,
                        "收款账号省份": "",
                        "收款账号地市": "",
                        "收款账号地区码": "",
                        "收款账号": item.bank_account,
                        "收款账号名称": item.bank_user,
                        "金额": item.amount,
                        "汇款用途": "",
                        "备注信息": "",
                        "汇款方式": "1",
                        "收款账户短信通知手机号码": "",
                        "自定义序号": "",
                        "预先审批编号": "",
                    });
                })
                const worksheet = XLSX.utils.json_to_sheet(newData);
                const workbook = XLSX.utils.book_new();
                XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1');

                downloadExcel(workbook);
            }
        }

        function downloadExcel(workbook) {
            XLSX.writeFile(workbook, getExcelName() + '.xls', {
                bookType: 'xls',
                bookVBA: true
            });
        }

        // $('.items').change(function() {
        $(document).on('change', '.items', function() {
            let diabledApply = false;
            let diabledConfirm = false;
            $("#applyModelBtn").prop('disabled', diabledApply);
            $("#confirmModelBtn").prop('disabled', diabledConfirm);

            const checkedItems = $('input[name="itemsSelected"]:checked');
            checkedItems.each(function() {
                const statusText = $(this).closest('tr').find('td').eq(2).text();
                if (statusText === '已付款') {
                    diabledApply = true;
                    diabledConfirm = true;
                    return false; // Exit the loop
                }
                if (statusText !== '待申请') {
                    diabledApply = true;
                }
                if (statusText !== '待付款' && statusText !== '已锁定') {
                    diabledConfirm = true;
                }
            });

            $("#applyModelBtn").prop('disabled', diabledApply);
            $("#confirmModelBtn").prop('disabled', diabledConfirm);
        });

        function refreshTable() {
            $.fn.yiiGridView.update('paymentList', {
                complete: function(jqXHR, status) {
                    // head.Util.ajax();
                    // head.Util.ajaxDel();
                    // head.Util.ajaxForm();
                }
            });
            console.log('刷新表格');
        }

        function getExcelName() {
            // 获取当前日期
            const today = new Date();
            const year = today.getFullYear(); // 获取年份
            const month = String(today.getMonth() + 1).padStart(2, '0'); // 获取月份（注意：月份从0开始，所以要加1）
            const day = String(today.getDate()).padStart(2, '0'); // 获取日期

            // 生成一个随机数（例如：0到9999之间）
            const randomNum = Math.floor(Math.random() * 10000);

            // 组合成字符串
            return `${year}-${month}-${day}-${randomNum}`;
        }

        function formatAmount(amount) {
            if (typeof amount !== 'number' || isNaN(amount)) {
                return '';
            }
            try {
                return amount.toLocaleString('zh-CN', {
                    style: 'currency',
                    currency: 'CNY'
                });
            } catch (error) {
                return '';
            }
        }

        function DateToUnix(string) {
            var f = string.split(' ', 2);
            var d = (f[0] ? f[0] : '').split('-', 3);
            var t = (f[1] ? f[1] : '').split(':', 3);
            return (new Date(
                parseInt(d[0], 10) || null,
                (parseInt(d[1], 10) || 1) - 1,
                parseInt(d[2], 10) || null,
                parseInt(t[0], 10) || null,
                parseInt(t[1], 10) || null,
                parseInt(t[2], 10) || null
            )).getTime() / 1000;
        }

        function print(elementName) {
            $(`#${elementName} .printContent`).printThis({
                popTitle: '付款列表'
            });
        }
    </script>
    <?php
    $this->renderPartial('//layouts/common/branchSelectBottom');
    ?>