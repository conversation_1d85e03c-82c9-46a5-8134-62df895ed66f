<?php

class MteachingModule extends CWebModule
{
    public function init()
    {
        // this method is called when the module is being created
        // you may place code here to customize the module or the application

        // import the module-level models and components
        $this->setImport(array(
            'mteaching.models.*',
            'mteaching.components.*',
        ));
    }

    public function beforeControllerAction($controller, $action)
    {
        if(parent::beforeControllerAction($controller, $action))
        {
            // this method is called before any module controller action is performed
            // you may place customized code here
            return true;
        }
        else
            return false;
    }

    public function getMenu()
    {
        $action = Yii::app()->urlManager->parseUrl(Yii::app()->request);

        $mainMenu = array(
            array('label'=>Yii::t('user','Roster by Reporting Period'), 'url'=>array("/mteaching/reportanalytic/index", 'branchId' => 'BJ_DS', 'type' => 'roster1')),
            array('label'=>Yii::t('user','Roster by Criterion'), 'url'=>array("/mteaching/reportanalytic/index", 'branchId' => 'BJ_DS', 'type' => 'roster2')),
            array('label'=>Yii::t('user','Campus Summary Reports'), 'url'=>array("/mteaching/reportanalytic/campusSummary", 'branchId' => 'BJ_DS')),
        );
        return $mainMenu;
    }
}
