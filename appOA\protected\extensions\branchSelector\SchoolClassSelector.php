<?php
class SchoolClassSelector extends CWidget
{
	public $linkRoute = '';
	
	public function run()
	{
        $schoolid = $this->controller->currentSchoolId;
        $classid = $this->controller->currentClassId;
        $weeknum = $this->controller->currentWeeknum;
		$showHistory = $this->controller->showHistory;
		$historyStartYear = $this->controller->historyStartYear;
		$task = $this->controller->task;
		
        $_rout = array(
//            'all' =>array('disable' => 'li_disabled'),
            'school' => array('areaid' =>'', 'name' => '', 'disable' => 'li_disabled', 'display' => 'display:none;'),
            'class' => array('areaid' =>'', 'name' => '', 'disable' => 'li_disabled', 'display' => 'display:none;'),
            'week' => array('areaid' =>'', 'name' => '', 'display' => 'display:none;'),
        );
        
        foreach($this->controller->schoolYears as $yid=>$yeArr){
            $calendarse[$yid] = array(
                's'=>  date('m/d/Y', $yeArr['starttime']),
                'e'=>  date('m/d/Y', $yeArr['endtime']),
            );
        }
        
        $schoolclasses = $this->controller->schoolClasses;
        $myclasses = $this->controller->myClasses;
        $myCC = count($myclasses);
        $mySC = count($schoolclasses);
        $sID = array_keys($schoolclasses);
        $sMain = current($schoolclasses);
        $cMain = current($myclasses);
        $hasLevel = false;
        
		//当前用户只有一个校园一个班
        if ($myCC == 1 && $mySC == 1){
//            $_rout['all'] = array();
            $_rout['school'] = array('areaid' =>$sID[0], 'name' => $sMain['title']);
            $_rout['class'] = array('areaid' =>$cMain, 'name' => $sMain['items'][$cMain]);
            $_rout['week'] = array('areaid' =>$weeknum, 'name' => 'Week '.$weeknum, 'disable' => 'li_disabled');
            $hasLevel = true;
        }
		
		//一个学校 分配到多个班级
        elseif ($mySC == 1 && $myCC > 1){
//            $_rout['all'] = array();
            $_rout['school'] = array('areaid' =>$sID[0], 'name' => $sMain['title'], 'disable' => 'li_disabled');
            $hasLevel = true;
        }
		
		//一个学校 没有分配到班级
        elseif ($mySC == 1 && $myCC == 0){
//            $_rout['all'] = array();
            $_rout['school'] = array('areaid' =>$sID[0], 'name' => $sMain['title'], 'disable' => 'li_disabled');
            $hasLevel = true;
        }
        
        
        if (in_array($schoolid, array_keys($schoolclasses))){
//            $_rout['all'] = array();
            $_rout['school'] = array('areaid' =>$schoolid, 'name' => $schoolclasses[$schoolid]['title'], 'disable' => 'li_disabled');
            $hasLevel = true;
        }
        if ((!$myclasses && $classid) || ($myclasses && in_array($classid, array_keys($myclasses)))){
//            $_rout['all'] = array();
            $_rout['school'] = array('areaid' =>$schoolid, 'name' => $schoolclasses[$schoolid]['title']);
            $_rout['class'] = array('areaid' =>$classid, 'name' => $schoolclasses[$schoolid]['items'][$classid], 'disable' => 'li_disabled');
            $hasLevel = true;
        }
        if ($schoolid && $myclasses && !in_array($classid, array_keys($myclasses))){
//            $_rout['all'] = array();
            $_rout['school'] = array('areaid' =>$schoolid, 'name' => $schoolclasses[$schoolid]['title']);
            $_rout['class'] = array('areaid' =>$classid, 'name' => $schoolclasses[$schoolid]['items'][$classid], 'disable' => 'li_disabled');
            $hasLevel = true;
        }
        
        if ($schoolid && $classid && $weeknum){
//            $_rout['all'] = array();
            $_rout['school'] = array('areaid' =>$schoolid, 'name' => $schoolclasses[$schoolid]['title']);
            $_rout['class'] = array('areaid' =>$classid, 'name' => $schoolclasses[$schoolid]['items'][$classid]);
            $_rout['week'] = array('areaid' =>$weeknum, 'name' => 'Week '.$weeknum, 'disable' => 'li_disabled');
            $hasLevel = true;
        }
        
		$this->render('schoolclass', array(
            'schoolclass'=>$schoolclasses,
            'myClasses'=>$myclasses,
            'route'=>$_rout,
            'hasLevel'=>$hasLevel,
            'calendarIds'=>$this->controller->calendarIds,
            'calendarse'=>$calendarse,
			'schoolid'=>$schoolid,
			'classid'=>$classid,
			'weeknum'=>$weeknum,
			'task'=>$task,
			'showHistory'=>$showHistory,
			'historyStartYear'=>$historyStartYear
        ));
	}
}