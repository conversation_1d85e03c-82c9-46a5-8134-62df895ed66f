<div id='printBox' class='report-wrapper page-break-after'>
    <div class='p24'>
        <div class='flex logoFlex'>
            <div class=''><img src="https://m2.files.ivykids.cn/cloud01-file-8025768FpUhKfisfbTkjXJTpyoMa0OpCrw5.png" alt="" class='logo'></div>
            <div class='flex1 text-right'>
                <div class='titleFlex'>
                    <img class='imgbg' src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/printBg.png' ?>" alt="">
                    <span class='title ml16'>退学确认单</span> 
                    <img class='imgbg ml16'  src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/printBg.png' ?>" alt="">
                </div>
                <div class='color3 clearfix'>序列号：{{printData.unique_id}}</div>
            </div>
        </div>
        <div class='flex bank'>
            <div class='flex1'>
                <div class='childname'>{{printData.childInfo.bilingual_name}}</div>
                <div class='className'>ID：{{printData.childInfo.id}} <span class='ml16'>{{printData.childInfo.className}}</span></div>
                <div class='mt10'>
                    <span class='color6'>父亲：</span>
                    <span class='el-icon-user-solid color3'></span>
                    <span class='color3 ml4'>{{printData.parentInfo.fName}}</span>
                    <span class='el-icon-phone color3 ml16'></span>
                    <span class='color3 ml4' v-if='printData.parentInfo.fPhone!=""'>{{printData.parentInfo.fPhone}}</span>
                    <span class='color9 ml4' v-else>—</span>
                </div>
                <div class='mt4'>
                    <span class='color6'>母亲：</span>
                    <span class='el-icon-user-solid color3'></span>
                    <span class='color3 ml4'>{{printData.parentInfo.mName}}</span>
                    <span class='el-icon-phone color3 ml16'></span>
                    <span class='color3 ml4' v-if='printData.parentInfo.mPhone!=""'>{{printData.parentInfo.mPhone}}</span>
                    <span class='color9 ml4' v-else>—</span>
                </div>
            </div>
            <div class='bankInfo' v-if='printData.financeInfo.bank_city!="" && printData.status==1'>
                <div class=''><span class='bankTitle'>银行所在城市</span><span>{{printData.financeInfo.bank_city}}</span></div>
                <div class='mt4'><span class='bankTitle'>银行名称含支行</span><span>{{printData.financeInfo.bank_name}}</span></div>
                <div class='mt4'><span class='bankTitle'>帐户名</span><span>{{printData.financeInfo.bank_user}}</span></div>
                <div class='mt4'><span class='bankTitle'>帐户号码</span><span>{{printData.financeInfo.bank_account}}</span></div>
            </div>
        </div>
    <div id="printDom">
        <div style=" margin-bottom:16px;">
            <table class="table table-bordered tableCss m0" id="totalTable">
                <tbody v-for='(list,key,index) in amountTable'>
                    <template v-if='list.goods.length==0'>
                        <tr v-if='key=="it_equ"'>
                            <td width="140" class="relative">
                                <div class="fontBold font14 color3 text-center">{{printData.nodesTitle[key]}}</div>
                            </td>
                            <td colspan="2"  class='fafa'>
                                <div class="flex align-items">
                                    <div class="font14 flex1" v-if='list.balance==0'>未归还 </div>
                                    <div class="font14 flex1" v-if='list.balance==1'>已归还—状态完好 </div>
                                    <div class="font14 flex1" v-if='list.balance==2'>已归还—存在设备损坏 </div>
                                    <div class="totalAmount color3 font16">
                                        <div class="amount" :class='list.balance_amount!=0?"redColor":""'>小计：{{list.balance_amount!=0?"-":""}}{{formatAmount(list.balance_amount)}}</div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        <tr v-else-if='list.balance=="1"'>
                            <td width="140" class="relative">
                                <div class="fontBold font14 color3 text-center">{{printData.nodesTitle[key]}}</div>
                            </td>
                            <td colspan="2" class='fafa'>
                                <div class="flex align-items">
                                    <div class="greenColor font14 flex1">已全部归还 </div>
                                    <div class="totalAmount color3 font16">
                                        <div class="amount ">小计：{{formatAmount(list.balance_amount)}}</div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                    </template>
                    <template v-if='list.goods.length!=0'>
                        <tr  v-for='(item,idx) in list.goods'>
                            <td width="140" class="relative" :rowspan='list.goods.length+1' v-if='idx==0'>
                                <div class="fontBold font14 color3 text-center" >{{printData.nodesTitle[key]}}</div>
                            </td>
                            <td colspan="2">
                                <div class="flex align-items">
                                    <div class="font14 flex1">{{item.name}} </div>
                                    <div class="totalAmount color3 font16">
                                        <div class="amount" v-if='item.balance==1'>-{{formatAmount(item.amount)}}</div>
                                        <div class="amount " v-if='item.balance==0'>{{formatAmount(item.amount)}}</div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="2"  class='fafa'>
                                <div class="flex align-items">
                                    <div class="font14 flex1"></div>
                                    <div class="totalAmount color3 font16">
                                        <div class="amount " :class='list.totalAmount<0?"redColor":""'>小计：{{formatAmount(list.totalAmount)}}</div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                    </template>
                    
                </tbody>
                <tbody>
                    <tr>
                        <td class="fontBold font14 color3 text-center">合计</td>
                        <td colspan="2" class='trBg'>
                            <div class="flex">
                                <div class="font14 flex1">
                                    <div class="color3 font14">正数为应退家长</div>
                                    <div class="color3 font14 mt4">负数为家长应支付校园</div>
                                </div>
                                <div class="totalAmount ">
                                    <div class="color3 font22 amount" style="font-size:20px">{{formatAmount(printData.financeInfo.total_amount)}}</div>
                                </div>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        <div >
            <table class="table table-bordered tableCss m0" id="viewTable">
                <tbody v-for='(list,key,index) in viewTable'>
                    <tr>
                        <td width="140" class="relative">
                            <div class="fontBold font14 color3 text-center">{{printData.nodesTitle[key]}}</div>
                        </td>
                        <td colspan="2">
                            <div class="flex " v-if='key=="insurance"'>
                                <div class="font14 flex1">
                                    <div class="color3 font14">
                                        <span v-if='list.balance=="1"'><?php echo Yii::t('withdrawal', 'Insured through Daystar, transferred out.'); ?></span>
                                        <span v-if='list.balance=="2"'><?php echo Yii::t('withdrawal', 'Insured through Daystar, not transferred out yet.'); ?></span>
                                        <span v-if='list.balance=="0"'><?php echo Yii::t('withdrawal', 'Not insured through Daystar.'); ?></span>
                                    </div>
                                    <div class="color6 font12 mt4" v-if='list.balance=="1" || list.balance=="2"'><?php echo Yii::t('withdrawal','说明');?>：{{explainList[list.balance]}}</div>
                                    <!-- <div class="color6 font12 mt4" v-if='printData.remarkData[key].remark!=""'><?php echo Yii::t('withdrawal','Comment');?>：{{printData.remarkData[key].remark}}</div> -->
                                </div>
                            </div>
                            <div class="flex " v-if='key=="enrollment"'>
                                <div class="font14 flex1" v-if='list.balance=="1"'>
                                    <div class="color3 font14">
                                        <span>有学籍</span>
                                        <span class="xuejiCss">{{showState(list.enrollment_state)}}</span>
                                    </div>
                                </div>
                                <div class="font14 flex1" v-else>
                                    <div class="color3 font14">
                                        <span>无学籍</span>
                                    </div>
                                </div>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
    <div><img class='sign_url' :src="printData.financeInfo.sign_url" alt=""></div>
    <div class='clearfix'></div>
    </div>
</div>

<script>
    let appId = '<?= $appId ?>'
    let printData = eval(<?= json_encode($printData); ?>)
    var container=new Vue({
        el: "#printBox",
        data: {
            printData:printData,
            amountTable:{},
            viewTable:{},
            explainList:{
            1:'亲爱的家长您好，根据北京市社会医疗保险（一老一小）的要求，在校学生的社会医疗保险需要跟随学生的就读学校进行迁移。由于学生需要从启明星学校转出，我校将不再为其办理下一年度社会医疗保险的续保事宜。您孩子的社会医疗保险关系即日起将从本校转出，建议您尽快联系孩子的新学校或户口所在社区进行续保，以确保不会断保。',
            2:'亲爱的家长您好，根据北京市社会医疗保险（一老一小）的要求，学生的保险需要跟随学生的所在学校/学籍进行迁移。对于已经离校的学生，启明星学校原则上不再为学生办理一老一小保险的参保及续保手续。对于未到转出学籍窗口期，学生虽已离校，但学籍仍需暂时由我校代为保管的情况，需要家长知晓：1. 请家长在转出学籍窗口期尽快完成学籍转移，并同时联系校医室办理一老一小保险的转出。2. 由于学籍暂未转出原因，需要代管一老一小保险的最长代管期为一年，逾期将默认进行减员。3. 请家长主动联系校医室申请一老一小保险代管。未在学生转走一个月内联系校医室，学生的一老一小保险将默认进行减员。'}
            },
        created: function() {
            this.viewTable = {}
            this.amountTable = {}
            for(var key in this.printData.financeInfo.items){
                if(key === 'enrollment' || key === 'insurance'){
                    this.viewTable[key]=this.printData.financeInfo.items[key]
                }else{
                    this.amountTable[key]=this.printData.financeInfo.items[key]
                } 
            }
        },
        methods: {
            showState(id){
                const value = this.printData.config.enrollmentStateList.filter(item => item.key ==id);
                return value.length?value[0].value:''
            },
            formatAmount(o_amount) {
                let amount = Number(o_amount);
                if (typeof amount !== 'number' || isNaN(amount)) {
                    return '';
                }
                try {
                    return amount.toLocaleString('zh-CN', {
                        style: 'currency',
                        currency: 'CNY'
                    });
                } catch (error) {
                    return '';
                }
            },
        },
    })

</script>
<style>
    .flex{
        display: flex
    }
    .flex1{
        flex:1
    }
    .color6{
        color:#666
    }
    .color3{
        color:#333 !important
    }
    .color9{
        color:#999
    }
    .ml16{
        margin-left:16px
    }
    .ml4{
        margin-left:4px
    }
    .mt4{
        margin-top:4px
    }
    .mt10{
        margin-top:10px
    }
    .fontBold{
        font-weight:bold
    }
    body {
        font-size: 10px;
        /*line-height: 1.42857143;*/
        color: #333333;
        margin: 0;
        padding: 0;
        background-color: #efefef;
    }
    #printBox{
        min-height: 877px;
        position: relative;
        background-color: #fff;
        width: 640px;
        margin: 0 auto;
        padding:0;
    }
    .page-break-after {
        page-break-after: always;
        /*margin-bottom: 13px;*/
    }
    .logoFlex{
        padding-bottom:18px
    }
    .logo{
        width:185px
    }
    .imgbg{
        width:80px;
    }
    .titleFlex{
        display: flex;
        align-items: center;
        float: right;
        margin:4px 0 6px
    }
    .clearfix{
        clear: both;
    }
    .table{
        margin:0
    }
    .table td {
        vertical-align: middle !important;
        color: #666;
    }
    .p24{
        padding:16px 30px 0 ;
    }
    .childname{
        font-size:17px;
        color:#333;
        font-weight:bold;
    }
    .className{
        font-size:11px;
        color:#666;
        margin-top:4px
    }
    .sign_url{
        width: 300px;
        float: right;
        clear:both;
        margin-top:5px;
    }
    .bank{
        border-top: 1px solid #ccc;
        border-bottom: 1px solid #ccc;
        padding:20px 0
    }
    .bankInfo{
        background: #F7F7F8;
        border-radius: 4px;
        border: 1px dashed #979797;
        padding:10px 12px 8px
    }
    .bankTitle{
        color:#666;
        width:86px;
        display:inline-block
    }
    #printDom{
        padding-top:15px
    }
    .greenColor{
        color:#5CB85C
    }
    .redColor{
        color:#D9534F;
    }
    .table > tbody + tbody{
        border-top:1px solid #dddddd;
    }
    .xuejiCss{
        color: #ED6A0C;
        background: rgba(237, 106, 12, 0.10);
        padding: 2px 6px;
        border-radius: 10px;
        margin-left: 5px;
    }
    .title{
        font-size:22px;
        font-weight:bold
    }
    .trBg{
        background:#F2F3F5 
    }
    .fafa{
        background:#FAFAFA 
    }
    @media print {
        #printBox {
            width: 100%;
        }
        .xuejiCss{
            color: #ED6A0C !important;
            background: rgba(237, 106, 12, 0.10) !important;
        }
        .greenColor{
            color:#5CB85C !important;
        }
        .redColor{
            color:#D9534F !important;
        }
        .p24{
            /* padding:0 !important; */
        }
        .trBg{
            background-color:#F2F3F5 !important;
        }
        .fafa{
            background-color:#FAFAFA !important;
        }
        .bankInfo{
            background-color: #F7F7F8 !important;
        }
    }
</style>