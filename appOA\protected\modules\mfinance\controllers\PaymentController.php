<?php

class PaymentController extends BranchBasedController
{
    public $actionAccessAuths = array(
        'index'            => 'o_F_Access',
        'query'            => 'o_F_Access',
    );

    public function init(){
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'main';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site','Finance Mgt');

        $this->branchSelectParams['hideOffice'] = true;
        $this->branchSelectParams['urlArray'] = array('//mfinance/payment/index');
    }

    public function createUrl($route, $params = array(), $ampersand = '&')
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        return parent::createUrl($route, $params, $ampersand);
    }

	public function actionIndex()
	{
        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/underscore-min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/backbone-min.js');

        Yii::import('application.components.policy.*');
        $policyApi = new PolicyApi($this->branchId);
        $invoiceTypes = $policyApi->getInvoiceType();

        $this->render('index', array('invoiceTypes'=>$invoiceTypes));
	}

    public function actionQuery()
    {
        Yii::import('application.components.policy.PolicyApi');
        $startdate  = Yii::app()->request->getParam('startdate', '');
        $enddate    = Yii::app()->request->getParam('enddate', '');
        $type       = Yii::app()->request->getParam('invoicetype', '');

        if($startdate && $enddate){
            $policyApi = new PolicyApi($this->branchId);
            $invoiceTypes = $policyApi->getInvoiceType();

            $startdate = strtotime($startdate);
            $enddate = strtotime($enddate.'23:59:59');
            if($enddate - $startdate < 15811200){
                Yii::import('common.models.invoice.*');
                $criteria = new CDbCriteria();
                $criteria->compare('`inout`', 'in');
                $criteria->compare('timestampe', '>='.$startdate);
                $criteria->compare('timestampe', '<='.$enddate);
                $criteria->compare('schoolid', $this->branchId);
                if($type)
                    $criteria->compare('payment_type', $type);
                $criteria->order='timestampe asc';
                $items = InvoiceTransaction::model()->findAll($criteria);
                $data = array();
                $admissionid = array();
                $newChilren = array();
                foreach($items as $item){
                    $policyApi = new PolicyApi($item->schoolid);
                    $data[$item->id] = array(
                        'childid' => $item->childid,
                        'childname' => $item->childid ? $item->childInfo->getChildName() : '',
                        'classname' => $item->childid && $item->childInfo->est_enter_date ? date("Y-m-d", $item->childInfo->est_enter_date) : '',
                        'dataname' => $item->childid ? $item->childInfo->ivyclass->title : '',
                        'invoiceClass' => $item->classInfo ? $item->classInfo->title : "",
                        'childbirthday' => $item->childid ? date("Y-m-d",$item->childInfo->birthday) : '',
                        'amount' => $item->amount,
                        'paytime' => date('Y-m-d H:i:s', $item->timestampe),
                        'admission_id' => $item->invoiceInfo->admission_id,
                        'invocieid' => $item->invoice_id,
                        'transactiontype' => $policyApi->renderPaymentType($item->transactiontype),
                        'invoicetype' => $invoiceTypes[$item->payment_type],
                    );

                    $admissionid[] = $data[$item->id]['admission_id'];
                }

                if($admissionid){
                    Yii::import('common.models.visit.AdmissionsDs');
                    $criteria = new CDbCriteria();
                    $criteria->compare('id', $admissionid);
                    $criteria->index='id';
                    $admissions = AdmissionsDs::model()->findAll($criteria);

                    $class = AdmissionsDs::getConfig();

                    $class_name_list = $class['grade'] +  $class['grade_ayalt'] + $class['grade_slt'];
                }

                foreach($data as $did=>$datum){
                    $data[$did]['childname'] = $datum['admission_id'] ? $admissions[$datum['admission_id']]->getName() : $datum['childname'];
                    $data[$did]['classname'] = $datum['admission_id'] ? date("Y-m-d",$admissions[$datum['admission_id']]->start_date) : $datum['classname'];
                    $data[$did]['dataname'] = $datum['admission_id'] ? $class_name_list[$admissions[$datum['admission_id']]->start_grade] : $datum['dataname'];
                    $data[$did]['childbirthday'] = $datum['admission_id'] ? date("Y-m-d" ,$admissions[$datum['admission_id']]->birthday) : $datum['childbirthday'];
                }


                $this->addMessage('state', 'success');
                $this->addMessage('message', '查询成功');
                $this->addMessage('data', $data);
                $this->addMessage('callback', 'callback');
            }
            else{
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '开始日期和结束日期不能跨越183天');
            }
        }
        else{
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '请先选择开始日期、结束日期');
        }
        $this->showMessage();
    }

    public function actionExpotData()
    {
        $data = Yii::app()->request->getParam('data',array());
        $startdate = Yii::app()->request->getParam('startdate',"");
        $enddate = Yii::app()->request->getParam('enddate',"");
        $data = json_decode($data);
       if($data){
            $filename = $this->branchId ." - ". 'Payment record-'. $startdate ."--". $enddate;
            header("Content-type:application/vnd.ms-excel");
            header("Content-Disposition:attachment;filename=$filename.xls");
            ob_end_clean();
            echo $this->_t("学生ID")."\t";
            echo $this->_t("学生姓名")."\t";
            echo $this->_t("出生日期")."\t";
            echo $this->_t("入学时间")."\t";
            echo $this->_t("入学班级/年级")."\t";
            echo $this->_t("账单班级")."\t";
            echo $this->_t("金额")."\t";
            echo $this->_t("支付类型")."\t";
            echo $this->_t("付款时间")."\t";
            echo $this->_t("费用类型")."\n";

            foreach($data as $_data){
                echo $_data->childid."\t";
                echo $this->_t($_data->childname)."\t";
                echo $this->_t($_data->classname)."\t";
                echo $this->_t($_data->childbirthday)."\t";
                echo $this->_t($_data->dataname)."\t";
                echo $this->_t($_data->invoiceClass)."\t";
                echo $this->_t($_data->amount)."\t";
                echo $this->_t($_data->transactiontype)."\t";
                echo $this->_t($_data->paytime)."\t";
                echo $this->_t($_data->invoicetype)."\n";
            }
        }
    }

    public function _t($str='')
    {
        return iconv('utf-8', 'gbk', $str);
    }
}