<div class="container-fluid">
	<!-- 面包屑导航 -->
	<ol class="breadcrumb">
	    <li><?php echo CHtml::link(Yii::t('site','Campus Operations'), array('//mcampus/default/index'))?></li>
	    <li><?php echo CHtml::link(Yii::t('site','Routines'), array('//mcampus/default/index'))?></li>
	    <li><?php echo CHtml::link(Yii::t('site','艾毅课程'), array('//mcampus/curriculum/project'))?></li>
	    <li><?php echo CHtml::link(Yii::t('site','活动列表'), array('//mcampus/curriculum/activity'))?></li>
	    <li class="active"><?php echo Yii::t('site',$activity->cn_title);?></li>
	</ol>
	<div class="row">
		<!-- 左侧菜单栏 -->
		<div class="col-md-2">
			<div class="list-group" id="classroom-status-list">
			    <?php foreach ($this->leftMenu as $key => $value) { ?>
			    <a href="<?php echo $this->createUrl($value[0]);?>" class="list-group-item status-filter <?php echo $this->getAction()->getId()==$value[0]?'active':''; ?>"><?php echo $value[1]; ?></a></li>
			    <?php } ?>
			</div>
		</div>
		<div class="col-md-10">
			<div class="panel panel-primary">
			  <div class="panel-heading"><h4>推荐项目</h4></div>
				<div class="panel-body">
					<?php 
						foreach ($activeProject as $key => $value) {
							var_dump(unserialize($value->projects));
							echo '<hr />';
						}
						// var_dump($activeProject);
					?>
				</div>
			</div>
		</div>
	</div>
</div>


