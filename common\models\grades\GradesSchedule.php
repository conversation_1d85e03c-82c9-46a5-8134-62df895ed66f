<?php

/**
 * This is the model class for table "ivy_grades_schedule".
 *
 * The followings are the available columns in table 'ivy_grades_schedule':
 * @property integer $id
 * @property string $schoolid
 * @property integer $yid
 * @property integer $startyear
 * @property integer $active
 * @property integer $updated
 * @property integer $valid_from
 * @property integer $valid_to
 */
class GradesSchedule extends SubDBActiveRecord
{
    public $classid = '';
    /**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_grades_schedule';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('schoolid,schedule_code,classid', 'required'),
			array('yid, startyear, active, updated, valid_from, valid_to', 'numerical', 'integerOnly'=>true),
			array('schoolid, label', 'length', 'max'=>32),
			array('schedule_code', 'length', 'max'=>8),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, schoolid, yid, startyear, active, updated, valid_from, valid_to, schedule_code', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'gradesScheduleLink'=>array(self::HAS_MANY,'GradesScheduleLink','schedule_id'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
            'label' => 'Label',
			'schoolid' => 'Schoolid',
			'yid' => 'Yid',
			'startyear' => 'Startyear',
			'active' => 'Active',
			'updated' => 'Updated',
			'valid_from' => 'Valid From',
			'valid_to' => 'Valid To',
			'schedule_code' => 'Schedule Template',
			'classid' => 'Class',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('yid',$this->yid);
		$criteria->compare('startyear',$this->startyear);
		$criteria->compare('active',$this->active);
		$criteria->compare('updated',$this->updated);
		$criteria->compare('valid_from',$this->valid_from);
		$criteria->compare('valid_to',$this->valid_to);
		$criteria->compare('schedule_code',$this->schedule_code);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return GradesSchedule the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}
