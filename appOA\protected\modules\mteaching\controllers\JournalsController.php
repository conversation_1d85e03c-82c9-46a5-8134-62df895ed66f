<?php

class JournalsController extends TeachBasedController
{
    // 访问action的初级权限
    public $actionAccessAuths = array(
        'index'                 => 'o_T_Access'
    );

    public $toReplyNum;
    public $type;
    public $managetypeList;

    public function init()
    {
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'teaching';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site', 'Teaching Tasks');

        //初始化选择校园页面
        $this->branchSelectParams['hideKG'] = true;
        $this->branchSelectParams['urlArray'] = array('//mteaching/journals/index', 'type' => $_GET['type']);
        // 自动重定向未选择学校前的 action
        if ($redirectAction = $_GET['redirectAction']) {
            $this->branchSelectParams['urlArray'] = array('//mteaching/journals/' . $redirectAction, 'type' => $_GET['type']);
        }
        $cs = Yii::app()->clientScript;
        $cs->registerCoreScript('jquery.ui');
        $cs->registerCssFile($cs->getCoreScriptUrl() . '/jui/css/base/jquery-ui.css');
        $cs->registerCssFile(Yii::app()->themeManager->baseUrl . '/base/js/viewer/viewer.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/element/vue.global.js');
        // 七牛上传所需文件
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/plupload/plupload.full.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/qiniu.min.js');
        // 引入图表所需文件
        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/echarts.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/mark/mark.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/viewer/viewer.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/hls/hls.min.js');

        // 初始化权限
        $this->initType();

        Yii::import('common.models.portfolio.*');
    }

    public function actionIndex()
    {
        // 查找待回复journal数量
        $this->toReplyNum = $this->getToReplyNum();

        $calendarYids = $this->getCalendars();
        $startyear = $this->calendarStartYear[$calendarYids['currentYid']];

        $data = array(
            "managetypeList" => $this->managetypeList,
            "startyear" => $startyear,
            "subjectList" => $this->getSubjectList(),
            "journalTypeList" => $this->getJournalTypeList(),
            "journalCategory" => $this->getJournalCategory(),
            "leaderList" => $this->getLeaderList(),
            "classList" => $this->getClassList($calendarYids['currentYid']),
            "gradeGroupList" => IvyClass::getGradeGroupListBySchoolId($this->branchId),
        );

        $this->render('index', array("data" => $data));
    }

    public function actionEdit()
    {
        // 查找待回复journal数量
        $this->toReplyNum = $this->getToReplyNum();

        $this->branchSelectParams['urlArray'] = array('//mteaching/journals/edit');
        $calendarYids = $this->getCalendars();
        $startyear = $this->calendarStartYear[$calendarYids['currentYid']];

        $data = array(
            "managetypeList" => $this->managetypeList,
            "startyear" => $startyear,
            "subjectList" => $this->getSubjectList(),
            "journalTypeList" => $this->getJournalTypeList(),
            "journalCategory" => $this->getJournalCategory(),
            "leaderList" => $this->getLeaderList(),
            "classList" => $this->getClassList($calendarYids['currentYid']),
            "gradeGroupList" => IvyClass::getGradeGroupListBySchoolId($this->branchId),
        );

        $this->render('edit', array("data" => $data));
    }

    public function actionGetList()
    {
        $type = Yii::app()->request->getParam('type');#teacher  leader(校长)
        $isMine = Yii::app()->request->getParam('isMine', 0);#只显示自己发布的 1
        $messages = Yii::app()->request->getParam('messages', 0);#特殊沟通 1
        $journals = Yii::app()->request->getParam('journals', 0);#常规日志 1
        $noAssigned = Yii::app()->request->getParam('noAssigned', 0);#未设置发布对象 1
        $myClass = Yii::app()->request->getParam('myClass');
        $gradeGroup = Yii::app()->request->getParam('gradeGroup');#type=leader字段内会有数据
        $searchText = Yii::app()->request->getParam('searchText', '');#按照内容搜素
        $staff = Yii::app()->request->getParam('staff', 0);
        $childId = Yii::app()->request->getParam('child_id');#学生id

        $uid = Yii::app()->user->getId();
        if ($staff > 0 && $this->checkViewTeachersAccess()) {
            $uid = $staff;
        }
        // 当前页码
        $pageNum = Yii::app()->request->getParam('pageNum', 1);
        $pageSize = Yii::app()->request->getParam('pageSize', 20);

        // 筛选条件
        $filter = array(
            'school_id' => $this->branchId,
            'type' => $type
        );
        if ($type == 'teacher') {
            if ($isMine) {
                $filter['created_by'] = (int)$uid;
            } else {
                $filter['status'] = 1;
            }
        }
        if ($type == 'leader') {
            if (!$isMine) {
                $filter['status'] = 1;
            }
        }
        if ($myClass) {
            $calendarYids = $this->getCalendars();
            $filter['class_id'] = $this->getManageClassList($calendarYids['currentYid']);
        }
        if ($gradeGroup) {
            $filter['grade_group_id'] = $gradeGroup;
        }
        if ($childId){
            //获取老师的信息条件
            $filter['targets'] = (int)$childId;
        }
        $data = $this->getList($pageNum, $pageSize, $filter, $isMine, $messages, $journals, $noAssigned, $searchText);
        $signIdList = array();
        foreach ($data['list'] as $item) {
            $signIdList[] = $item['sign_as_uid'];
        }
        $signIdList = array_unique($signIdList);
        // 获取所有写过 journal 的老师
        $teacherIds = array();
        if ($type == 'teacher' && $this->checkViewTeachersAccess()) {
            // $res = CommonUtils::requestDsOnline('journal/teachers', array('school_id' => $this->branchId));
            // if ($res['code'] == 0) {
            //     $teacherIds = $res['data'];
            // }
        }
        $signIdList = array_merge($signIdList, $teacherIds);
        $signIdList = array_unique($signIdList);
        $userList = $this->getUserInfo($signIdList);
        // 计算分页
        $total = $data['count'];
        $pages = ceil($total / $pageSize);
        $pagination  = array(
            'pages' => $pages,
            'pageNum' => $pageNum,
            'pageSize' => $pageSize,
            'total' => $total
        );
        $this->addMessage('state', 'success');
        $this->addMessage('data', array(
            'list' => $data['list'],
            'pagination' => $pagination,
            'userList' => $userList,
            'teacherIds' => $teacherIds,
            'school_title'=>$this->branchObj['title'],
        ));
        $this->showMessage();
    }



    public function actionGetOne()
    {
        $id = Yii::app()->request->getParam('id');

        $this->addMessage("state", "fail");
        if (!$id) {
            $this->addMessage("message", "ID 不能为空");
            $this->showMessage();
        }
        $journal = $this->getJournal($id);
        if (!$journal) {
            $this->addMessage("message", "Jornal 不存在");
            $this->showMessage();
        }
        $userInfo = $this->getUserInfo($journal['sign_as_uid']);
        $journal['user_info'] = current($userInfo);

        $this->addMessage("state", "success");
        $this->addMessage('data', $journal);
        $this->showMessage();
    }

    /**
     * 获取journal的发布对象孩子及已读孩子信息
     *
     * @return void
     */
    public function actionGetReviewed()
    {
        $id = Yii::app()->request->getParam('id');

        if (!$id) {
            $this->addMessage("state", "fail");
            $this->addMessage("message", "ID 不能为空");
            $this->showMessage();
        }
        $res = CommonUtils::requestDsOnline('journal/getReviewed/' . $id);
        if ($res['code'] == 0) {
            $targets = $res['data']['targets'];
            $reviewed = $res['data']['reviewed'];
            $reviewedChild = $res['data']['reviewedChild'];
            $sendNum = $res['data']['sendNum'];
            $childIdList = array_merge($targets, $reviewed);
            $childs = array();
            // 查找分配的孩子信息
            if ($childIdList) {
                $childModels = ChildProfileBasic::model()->findAllByPk($childIdList);
                foreach ($childModels as $childModel) {
                    $childs[$childModel->childid] = array(
                        'id' => $childModel->childid,
                        'name' => $childModel->getChildName(),
                        'avatar' => CommonUtils::childPhotoUrl($childModel->photo),
                        'class_name' => $childModel->ivyclass->title,
                        'label' => $childModel->getLabel()
                    );
                }
            }
            $data = array(
                'targets' => $targets,
                'reviewed' => $reviewed,
                'reviewedChild' => $reviewedChild,
                'childs' => $childs,
                'sendNum' => $sendNum,
            );

            $this->addMessage("state", "success");
            $this->addMessage('data', $data);
            $this->showMessage();
        } else {
            $this->addMessage("state", "fail");
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }

    public function actionGetParents()
    {
        $childId = Yii::app()->request->getParam('child_id');

        if (!$childId) {
            $this->addMessage("state", "fail");
            $this->addMessage("message", "Child ID 不能为空");
            $this->showMessage();
        }
        $childModel = ChildProfileBasic::model()->findByPk($childId);
        if (!$childModel) {
            $this->addMessage("state", "fail");
            $this->addMessage("message", "孩子不存在");
            $this->showMessage();
        }
        // 获取最近发送微信消息详情
        $res = CommonUtils::requestDsOnline('child/wx/receivers?child_id='.$childId);
        $wxReceivers = ($res['code'] == 0 && $res['data']) ? $res['data'] : array();

        $parentList = $childModel->getParents();
        $parentData = array();
        if ($parentList['father']) {
            $parentData[] = array(
                'parent_name' => $parentList['father']->getName(),
                'parent_relationship' => 1,
                'parent_relationship_name' => Yii::t('global', 'Father'),
                'parent_tel' => $parentList['father']->parent->mphone,
                'parent_email' => $parentList['father']->email,
                'parent_flag'=>$parentList['father']->parent->getParentFlag(),
            );
        }
        if ($parentList['mother']) {
            $parentData[] = array(
                'parent_name' => $parentList['mother']->getName(),
                'parent_relationship' => 2,
                'parent_relationship_name' => Yii::t('global', 'Mother'),
                'parent_tel' => $parentList['mother']->parent->mphone,
                'parent_email' => $parentList['mother']->email,
                'parent_flag'=>$parentList['mother']->parent->getParentFlag(),
            );
        }
        $this->addMessage("state", "success");
        $this->addMessage("data", array('parentData' => $parentData, 'wxReceivers' => $wxReceivers));
        $this->showMessage();
    }

    /**
     * 显示，ID 为0时为新增
     *
     * @param integer $id
     * @return void
     */
    public function actionShow($id = 0)
    {
        $cs = Yii::app()->clientScript;
        // 在线编辑器
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/tinymce/tinymce.min.js');

        $calendarYids = $this->getCalendars();
        $startyear = $this->calendarStartYear[$calendarYids['currentYid']];
        $yid = $calendarYids['currentYid'];
        $calendar = Calendar::model()->findByPk($yid);
        $timepointArray = explode(',', $calendar->timepoints);
        $startDate = date("Y-m-d", $timepointArray[0]);
        $endDate =  date("Y-m-d", $timepointArray[1]);
        $journal = array();
        if ($id != 0) {
            // 编辑时，检查权限
            $journal = $this->getJournal($id);
            if (!$journal) {
                return false;
            }
            // 检查添加类型的权限
            $this->checkType($journal['type']);
            // 检查添加班级的权限
            if ($journal['type'] == 'teacher') {
                $this->checkClass($journal['class_id']);
            }
        } else {
            // 检查添加类型的权限
            $this->checkType($this->type);
        }

        $staffPhoto = empty($this->staff->staffInfo->staff_photo) ? "blank.jpg" : $this->staff->staffInfo->staff_photo;
        $teacherInfo = array(
            "uid" => Yii::app()->user->getId(),
            "name" => $this->staff->getName(),
            "photo" => OA::CreateOAUploadUrl('infopub/staff', $staffPhoto),
            'title' =>  CommonUtils::autoLang($this->staff->profile->occupation->cn_name, $this->staff->profile->occupation->en_name),
            'title_cn' => $this->staff->profile->occupation->cn_name,
            'title_en' => $this->staff->profile->occupation->en_name,
        );

        $this->render('show', array("data" => array(
            "startyear" => $startyear,
            "yid" => $calendarYids['currentYid'],
            "teacherSubject" => $this->getTeacherSubject(Yii::app()->user->getId()),
            "managetypeList" => $this->managetypeList,
            "subjectList" => $this->getSubjectList(),
            'journal' => $journal,
            "journalTypeList" => $this->getJournalTypeList(),
            "journalCategory" => $this->getJournalCategory(),
            'teacherInfo' => $teacherInfo,
            "leaderList" => $this->getLeaderList(),
            "classList" => $this->getManageClassList($calendarYids['currentYid']),
            "gradeGroupList" => IvyClass::getGradeGroupListBySchoolId($this->branchId),
            "startDate" => $startDate,
            "endDate" => $endDate,
        )));
    }

    public function actionAdd()
    {
        $data = Yii::app()->request->getPost("data");
        // 检查添加类型的权限
        $this->checkType($data['type']);
        // 检查添加班级的权限
        if ($data['type'] == 'teacher') {
            $this->checkClass($data['class_id']);
        }

        $data['school_id'] = $this->branchId;
        $data['created_by'] = Yii::app()->user->getId();
        $data['updated_by'] = Yii::app()->user->getId();
        if (isset($data['publish_at']) && $data['publish_at']) {
            $data['week_num'] = 1;
            // 根据发布时间，判断 journal 所在的周跟月
            $yid = $data['yid'];
            $datetime = strtotime($data['publish_at']);
            $crit = new CDbCriteria();
            $crit->compare('yid', $yid);
            $crit->compare('monday_timestamp', '<=' . $datetime);
            $crit->order = 'monday_timestamp DESC';
            $weekModel = CalendarWeek::model()->find($crit);
            if ($weekModel) {
                $data['week_num'] = (int)$weekModel->weeknumber;
            }
        }

        $res = CommonUtils::requestDsOnline("journal/add", $data);
        $this->addMessage("state", "success");
        $this->addMessage("data", $res);
        $this->showMessage();
    }

    public function actionGetQrCode()
    {
        $id = Yii::app()->request->getPost('id');
        if (!$id) {
            $this->addMessage("message", "ID 不能为空");
            $this->showMessage();
        }
        $journal = $this->getJournal($id);
        if (!$journal) {
            $this->addMessage("message", "journal 不存在");
            $this->showMessage();
        }
        $datapreview = array("_id" => $id, "type" => 'journalPreview', 'schoolid' => $this->branchId);
        $respreview = CommonUtils::requestDsOnline("notice/qrCode", $datapreview);

        $datashare = array("_id" => $id, "type" => 'journalShare', 'schoolid' => $this->branchId);
        $resshare = CommonUtils::requestDsOnline("notice/qrCode", $datashare);

        $data = array("preview" => $respreview["data"], "share" => $resshare["data"]);

        $this->addMessage("state", "success");
        $this->addMessage("message", "success");
        $this->addMessage("data", $data);
        $this->showMessage();
    }


    public function actionMedia()
    {
        $this->layout = "//layouts/column3";
        $yid = Yii::app()->request->getParam("yid", '');
        $criteria = new CDbCriteria();
        $criteria->compare('branchid', $this->branchId);
        $criteria->order = "startyear DESC";
        $calendarModel = CalendarSchool::model()->findAll($criteria);

        $calendarInfo = array();
        if ($calendarModel) {
            foreach ($calendarModel as $val) {
                $nextYaer = $val->startyear + 1;
                $calendarInfo[] = array(
                    'yid' => $val->yid,
                    "title" => $val->startyear . '-' . $nextYaer . " " . Yii::t("labels", "Yr."),
                );
                if ($val->is_selected && !$yid) {
                    $yid = $val->yid;
                }
            }
        }
        $this->render('media', array("calendarInfo" => $calendarInfo, 'yid' => $yid));
    }

    public function actionGetClass()
    {
        $yid = Yii::app()->request->getParam("yid", '');
        $classInfo = array();
        $classAllModel = array();
        if (Yii::app()->user->checkAccess('ivystaff_it') || Yii::app()->user->checkAccess('ivystaff_opschool')) {
            $classAllModel = IvyClass::model()->getClassList($this->branchId, $yid);
        } elseif (Yii::app()->user->checkAccess('ivystaff_teacher')) {
            $criteria = new CDbCriteria();
            $criteria->compare('t.schoolid', $this->branchId);
            $criteria->compare('t.teacherid', Yii::app()->user->id);
            $criteria->compare('t.yid', $yid);
            $criteria->index = "classid";
            $classTeacherModel = ClassTeacher::model()->findAll($criteria);
            $crit = new CDbCriteria();
            $crit->compare('t.classid', array_keys($classTeacherModel));
            $crit->compare('schoolid', $this->branchId);
            $crit->compare('yid', $yid);
            $crit->order = 'child_age ASC, title ASC';
            $classAllModel = IvyClass::model()->findAll($crit);
        }
        if ($classAllModel) {
            foreach ($classAllModel as $val) {
                $classInfo[] = array(
                    'classid' => $val->classid,
                    'title' => $val->title
                );
            }
        }
        $this->addMessage("state", "success");
        $this->addMessage("data", $classInfo);
        $this->showMessage();
    }

    public function actionFetchWeekly()
    {
        $yid = Yii::app()->request->getParam("yid", '');
        $classId = Yii::app()->request->getParam("classId", '');
        $weekNum = Yii::app()->request->getParam("weekNum", '');
        $pageSize = Yii::app()->request->getParam("pageSize", 1);

        if (!$yid || !$classId) {
            $this->addMessage("state", "fail");
            $this->addMessage("message", "fail");
            $this->showMessage();
        }
        $criteria = new CDbCriteria();
        $criteria->compare('yid', $yid);
        $criteria->compare('branchid', $this->branchId);
        $calendarModel = CalendarSchool::model()->find($criteria);
        ChildMedia::setStartYear($calendarModel->startyear);
        $mediaData = array();
        $this->addMessage("state", "success");
        if ($weekNum) {
            $criteria = new CDbCriteria();
            $criteria->compare('schoolid', $this->branchId);
            $criteria->compare('classid', $classId);
            $criteria->compare('yid', $yid);
            $criteria->compare('weeknum', $weekNum);
            $total = ChildMedia::model()->count($criteria);

            $batchNum = 20;
            $cycle = ceil($total / $batchNum);
            $criteria->limit = $batchNum;
            $criteria->offset = ($pageSize - 1) * $batchNum;
            $criteria->order = 'timestamp DESC';
            $medias = ChildMedia::model()->findAll($criteria);

            $schoolType = in_array($this->branchId, CommonUtils::dsSchoolList()) ? 'ds' : 'ivy';
            foreach ($medias as $media) {
                $mediaData["data"][$media['id']]['id'] = $media->id;
                $mediaData["data"][$media['id']]['schoolid'] = $media->schoolid;
                $mediaData["data"][$media['id']]['classid'] = $media->classid;
                $mediaData["data"][$media['id']]['yid'] = $media->yid;
                $mediaData["data"][$media['id']]['weeknum'] = $media->weeknum;
                $mediaData["data"][$media['id']]['tag'] = $media->tag;
                $mediaData["data"][$media['id']]['type'] = $media->type;
                $mediaData["data"][$media['id']]['filename'] = $media->filename;
                $mediaData["data"][$media['id']]['server'] = $media->server;
                $mediaData["data"][$media['id']]['url'] = CommonUtils::getMediaUrl2(true, $media['type'], $media['filename'], $schoolType);
                $mediaData["data"][$media['id']]['_url'] = CommonUtils::getMediaUrl2(false, $media['type'], $media['filename'], $schoolType);
            }
            $mediaData["pageData"] = array(
                'pageCount' => $cycle,
                'pageSize' => $pageSize,
            );
            $this->addMessage("data", $mediaData);
        } else {
            $criteria = new CDbCriteria();
            $criteria->select = "weeknum";
            $criteria->compare('schoolid', $this->branchId);
            $criteria->compare('classid', $classId);
            $criteria->compare('yid', $yid);
            $criteria->index = 'weeknum';
            $criteria->distinct = true;
            $medias = ChildMedia::model()->findAll($criteria);

            if ($medias) {
                $cwCrit = new CDbCriteria();
                $cwCrit->compare('weeknumber', array_keys($medias));
                $cwCrit->compare('yid', $yid);
                $cwCrit->order = 'weeknumber DESC';
                $calendarWeekModel = CalendarWeek::model()->findAll($cwCrit);
                foreach ($calendarWeekModel as $val) {
                    $mediaData[] = array(
                        'weeknumber' => $val->weeknumber,
                        'title' => sprintf(
                            'W%02d %s ~ %s',
                            $val->weeknumber,
                            Yii::app()->dateFormatter->format('yyyy.MM.dd', $val->monday_timestamp),
                            Yii::app()->dateFormatter->format('yyyy.MM.dd', $val->monday_timestamp + 4 * 24 * 3600)
                        )
                    );
                }
            }
            $this->addMessage("data", $mediaData);
        }
        $this->showMessage();
    }

    public function actionUpdate()
    {
        $data = Yii::app()->request->getPost("data");
        // 检查添加类型的权限
        $this->checkType($data['type']);
        // 检查添加班级的权限
        if ($data['type'] == 'teacher') {
            $this->checkClass($data['class_id']);
        }

        $id = $data['_id'];
        if (!$id) {
            $this->addMessage("message", "ID 不能为空");
            $this->showMessage();
        }

        if (isset($data['publish_at']) && $data['publish_at']) {
            $data['week_num'] = 1;
            // 根据发布时间，判断 journal 所在的周跟月
            $yid = $data['yid'];
            $datetime = strtotime($data['publish_at']);
            $crit = new CDbCriteria();
            $crit->compare('yid', $yid);
            $crit->compare('monday_timestamp', '<=' . $datetime);
            $crit->order = 'monday_timestamp DESC';
            $weekModel = CalendarWeek::model()->find($crit);
            if ($weekModel) {
                $data['week_num'] = (int)$weekModel->weeknumber;
            }
        }
        $data['updated_by'] = Yii::app()->user->getId();

        $res = CommonUtils::requestDsOnline("journal/edit", $data);
        $this->addMessage("state", "success");
        $this->addMessage("data", $res);
        $this->showMessage();
    }

    public function actionDelete()
    {
        if (!in_array($this->type, $this->managetypeList)) {
            return false;
        }
        $this->addMessage("state", "fail");
        $id = Yii::app()->request->getPost('id');
        if (!$id) {
            $this->addMessage("message", "ID 不能为空");
            $this->showMessage();
        }
        $journal = $this->getJournal($id);
        if (!$journal) {
            $this->addMessage("message", "journal 不存在");
            $this->showMessage();
        }
        $this->checkType($journal['type']);
        if ($journal['type'] == 'teacher') {
            $this->checkClass($journal['class_id']);
        }

        $data = array("_id" => $id);
        $res = CommonUtils::requestDsOnline("journal/delete", $data);
        $this->addMessage("state", "success");
        $this->addMessage("message", $res["msg"]);
        $this->addMessage("data", $res['data']);
        $this->showMessage();
    }

    public function actionOffline()
    {
        $id = Yii::app()->request->getPost('id');
        if (!$id) {
            $this->addMessage("message", "ID 不能为空");
            $this->showMessage();
        }
        $journal = $this->getJournal($id);
        if (!$journal) {
            $this->addMessage("message", "journal 不存在");
            $this->showMessage();
        }
        $data = array("_id" => $id);
        $res = CommonUtils::requestDsOnline("journal/offline", $data);
        $this->addMessage("state", "success");
        $this->addMessage("message", $res['msg']);
        $this->showMessage();
    }

    // 根据用户ID 获取用户自定义的分组
    public function actionAddGroupLink()
    {
        $title = Yii::app()->request->getParam('title');
        $childIds = Yii::app()->request->getParam('childIds');
        if (!$title) {
            $this->addMessage("state", "success");
            $this->addMessage("message", "标题不能为空");
            $this->showMessage();
        }
        if (!is_array($childIds)) {
            $this->addMessage("state", "success");
            $this->addMessage("message", "参数类型错误");
            $this->showMessage();
        }
        $data = array("staff_id" => Yii::app()->user->getId(), 'title' => $title, 'child_id' => $childIds);
        $res = CommonUtils::requestDsOnline("childGroupLink/add", $data);
        $this->addMessage("state", "success");
        $this->addMessage("message", $res['msg']);
        $this->showMessage();
    }

    // 根据用户ID 获取用户自定义的分组
    public function actionEditGroupLink()
    {
        $id = Yii::app()->request->getParam('id');
        $title = Yii::app()->request->getParam('title');
        $childIds = Yii::app()->request->getParam('childIds');
        if (!$title) {
            $this->addMessage("state", "success");
            $this->addMessage("message", "标题不能为空");
            $this->showMessage();
        }
        if (!is_array($childIds)) {
            $this->addMessage("state", "success");
            $this->addMessage("message", "参数类型错误");
            $this->showMessage();
        }
        $data = array("_id" => $id, "staff_id" => Yii::app()->user->getId(), 'title' => $title, 'child_id' => $childIds);
        $res = CommonUtils::requestDsOnline("childGroupLink/edit", $data);
        $this->addMessage("state", "success");
        $this->addMessage("message", $res['msg']);
        $this->showMessage();
    }

    // 根据用户ID 获取用户自定义的分组
    public function actionDelGroupLink()
    {
        $id = Yii::app()->request->getParam('id');
        $data = array("_id" => $id);
        $res = CommonUtils::requestDsOnline("childGroupLink/delete", $data);
        $this->addMessage("state", "success");
        $this->addMessage("message", $res['msg']);
        $this->showMessage();
    }

    // 根据用户ID 获取用户自定义的分组
    public function actionGetGroupLink()
    {
        $res = CommonUtils::requestDsOnline("childGroupLink/list/" . Yii::app()->user->getId());
        $this->addMessage("state", "success");
        $this->addMessage("message", $res['msg']);
        $this->addMessage("data", $res['data']);
        $this->showMessage();
    }

    // 根据用户ID 获取用户自定义的分组
    public function actionGetGroupLinkChild()
    {
        $id = Yii::app()->request->getParam('id');
        if (!$id) {
            $this->addMessage("state", "fail");
            $this->addMessage("message", "ID 不能为空");
            $this->showMessage();
        }
        $res = CommonUtils::requestDsOnline("childGroupLink/childList/" . $id);
        $this->addMessage("state", "success");
        $this->addMessage("message", $res['msg']);
        $this->addMessage("data", $res['data']);
        $this->showMessage();
    }

    // 根据班级ID 获取孩子
    public function actionChildList()
    {
        $classId = Yii::app()->request->getPost('classId');
        if (!$classId) {
            $this->addMessage("state", "fail");
            $this->addMessage('message', "班级ID不能为空");
            $this->showMessage();
        }
        $this->addMessage("state", "success");
        $this->addMessage("data", $this->getChildData($classId));
        $this->showMessage();
    }

    // 获取七牛上传的token
    public function actionGetQiniuToken()
    {
        $linkId = Yii::app()->request->getParam('linkId');
        $linkType = Yii::app()->request->getParam('linkType');
        $isPrivate = Yii::app()->request->getParam('isPrivate', 1);
        $isVideo = Yii::app()->request->getParam('isVideo', 0);
        if (!$linkId || !$linkType) {
            $this->addMessage("state", "fail");
            $this->addMessage('message', "参数错误");
            $this->showMessage();
        }
        $data = array(
            'linkType' => $linkType,
            'linkId' => $linkId,
            'isPrivate' => $isPrivate,
        );
        $path = $isVideo ? 'getQiniuVideoToken' : 'getQiniuToken';
        $res = CommonUtils::requestDsOnline($path, $data);
        $this->addMessage("state", "success");
        $this->addMessage('data', $res);
        $this->showMessage();
    }

    // 获取七牛上传的token
    public function actionGetCommentQiniuToken()
    {
        $linkId = Yii::app()->request->getParam('linkId');
        $isVideo = Yii::app()->request->getParam('isVideo', 0);
        if (!$linkId) {
            $this->addMessage("state", "fail");
            $this->addMessage('message', "参数错误");
            $this->showMessage();
        }
        $data = array(
            'linkId' => $linkId,
            'linkType' => 'comment',
            'isPrivate' => 1,
        );
        $path = $isVideo ? 'getQiniuVideoToken' : 'getQiniuToken';
        $res = CommonUtils::requestDsOnline($path, $data);
        $this->addMessage("state", "success");
        $this->addMessage('data', $res);
        $this->showMessage();
    }

    // 获取七牛上传的token
    public function actionGetQiniuTokenSimple()
    {
        $isPrivate = Yii::app()->request->getParam('isPrivate', 1);
        $prefix = Yii::app()->request->getParam('prefix');

        if (!$prefix) {
            $this->addMessage("state", "fail");
            $this->addMessage('message', "文件前缀不能为空");
            $this->showMessage();
        }
        $data = array(
            'schoolId' => $this->branchId,
            'prefix' => $prefix,
            'isPrivate' => $isPrivate,
        );
        $res = CommonUtils::requestDsOnline('getQiniuTokenSimple', $data);
        if ($res['code'] == '1') {
            $this->addMessage("state", "fail");
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
        $this->addMessage("state", "success");
        $this->addMessage('data', $res['data']);
        $this->showMessage();
    }

    // 根据业务类型及ID获取附件的详细信息
    public function actionGetAttachments()
    {
        $linkId = Yii::app()->request->getParam('linkId');
        $linkType = Yii::app()->request->getParam('linkType');
        if (!$linkId || !$linkType) {
            $this->addMessage("state", "fail");
            $this->addMessage('message', "参数错误");
            $this->showMessage();
        }
        $res = CommonUtils::requestDsOnline('getQiniuFileList/' . $linkId . "/" . $linkType);
        $this->addMessage("state", "success");
        $this->addMessage('data', $res);
        $this->showMessage();
    }

    // 更新附件显示的名称
    public function actionUpdateAttachmentName()
    {
        $attachmentId = Yii::app()->request->getParam('attachmentId');
        $attachmentName = Yii::app()->request->getParam('attachmentName');
        $data = array(
            'id' => $attachmentId,
            'title' => $attachmentName
        );
        $res = CommonUtils::requestDsOnline('updateFileTitle', $data);
        $this->addMessage("state", "success");
        $this->addMessage('data', $res['data']);
        $this->showMessage();
    }

    public function actionSortAttachment()
    {
        $fileSortList = Yii::app()->request->getParam('fileSortList');
        $res = CommonUtils::requestDsOnline('sortFile', array("fileSortList" => $fileSortList));
        $this->addMessage("state", "success");
        $this->addMessage('data', $res);
        $this->showMessage();
    }

    /**
     * 获取当前用户待回复journal数量
     *
     * @return void
     */
    public function getToReplyNum()
    {
        $targetId = Yii::app()->user->getId();
        $res = CommonUtils::requestDsOnline('journal/toReplyNum/' . $targetId, array('school_id' => $this->branchId));

        $num = 0;
        if ($res['code'] == 0) {
            $num = $res['data'];
        }
        return $num;
    }

    // 删除附件
    public function actionDeleteAttachment()
    {
        $attachmentId = Yii::app()->request->getParam('attachmentId');
        $res = CommonUtils::requestDsOnline('deleteFile/' . $attachmentId);
        $this->addMessage("state", "success");
        $this->addMessage('data', $res);
        $this->showMessage();
    }

    // 待回复列表
    public function actionUnreply()
    {
        $targetId = $this->getUid();
        $res = CommonUtils::requestDsOnline('journal/unReplayList', array('targetId' => (int)$targetId, 'school_id' => $this->branchId));
        if ($res['code'] == 0) {
            $data = $res['data'];
            $childIdList = array();
            foreach ($data['items'] as $v) {
                $childIdList = array_merge($childIdList, $v['childids']);
            }

            $childIdList = array_unique($childIdList);
            $childs = array();
            if ($childIdList) {
                $childModels = ChildProfileBasic::model()->findAllByPk($childIdList);
                foreach ($childModels as $childModel) {
                    $childs[$childModel->childid] = array(
                        'name' => $childModel->getChildName(),
                        'avatar' => CommonUtils::childPhotoUrl($childModel->photo),
                        'class_name' => $childModel->ivyclass->title
                    );
                    if ($childModel->fid) {
                        $childs[$childModel->childid]["p_" . $childModel->fid] = 'f';
                    }
                    if ($childModel->mid) {
                        $childs[$childModel->childid]["p_" . $childModel->mid] = 'm';
                    }
                }
            }
            $data['childs'] = $childs;

            $this->addMessage('state', 'success');
            $this->addMessage('data', $data);
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
        }
        $this->showMessage();
    }

    // 所有待回复列表
    public function actionAllUnreply()
    {
        $this->checkType('leader');
        $res = CommonUtils::requestDsOnline('journal/allUnReplayList', array('school_id' => $this->branchId));
        if ($res['code'] == 0) {
            $data = $res['data'];
            $childIdList = array();
            foreach ($data['items'] as $v) {
                $childIdList = array_merge($childIdList, $v['childids']);
            }

            $childIdList = array_unique($childIdList);
            $childs = array();
            if ($childIdList) {
                $childModels = ChildProfileBasic::model()->findAllByPk($childIdList);
                foreach ($childModels as $childModel) {
                    $childs[$childModel->childid] = array(
                        'name' => $childModel->getChildName(),
                        'avatar' => CommonUtils::childPhotoUrl($childModel->photo),
                        'class_name' => $childModel->ivyclass->title
                    );
                    if ($childModel->fid) {
                        $childs[$childModel->childid]["p_" . $childModel->fid] = 'f';
                    }
                    if ($childModel->mid) {
                        $childs[$childModel->childid]["p_" . $childModel->mid] = 'm';
                    }
                }
            }
            $data['childs'] = $childs;

            $this->addMessage('state', 'success');
            $this->addMessage('data', $data);
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
        }
        $this->showMessage();
    }

    // 已回复列表
    public function actionReplied()
    {
        $targetId = $this->getUid();
        $page = Yii::app()->request->getParam("page", 1);

        $take = 20;
        $skip = ($page - 1) * $take;
        $res = CommonUtils::requestDsOnline('journal/repliedList', array(
            'targetId' => (int)$targetId,
            'skip' => (int)$skip,
            'take' => $take,
        ));
        if ($res['code'] == 0) {
            $data = $res['data'];
            $childIdList = array();
            foreach ($data['items'] as $v) {
                $childIdList = array_merge($childIdList, $v['childids']);
            }

            $childIdList = array_unique($childIdList);
            $childs = array();
            if ($childIdList) {
                $childModels = ChildProfileBasic::model()->findAllByPk($childIdList);
                foreach ($childModels as $childModel) {
                    $childs[$childModel->childid] = array(
                        'name' => $childModel->getChildName(),
                        'avatar' => CommonUtils::childPhotoUrl($childModel->photo),
                        'class_name' => $childModel->ivyclass->title
                    );
                    if ($childModel->fid) {
                        $childs[$childModel->childid]["p_" . $childModel->fid] = 'f';
                    }
                    if ($childModel->mid) {
                        $childs[$childModel->childid]["p_" . $childModel->mid] = 'm';
                    }
                }
            }
            $data['childs'] = $childs;

            $this->addMessage('state', 'success');
            $this->addMessage('data', $data);
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
        }
        $this->showMessage();
    }

    // 保存老师评论
    public function actionSaveComment()
    {
        $id = Yii::app()->request->getParam("id");
        $childId = Yii::app()->request->getParam("child_id");
        $content = Yii::app()->request->getParam("content");
        $mark = Yii::app()->request->getParam("mark_as_staff", 0);
        $attachments = Yii::app()->request->getParam("attachments", array());
        $uid = Yii::app()->user->getId();

        $childModel = ChildProfileBasic::model()->findByPk($childId);
        if (!$childModel) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', 'child id error');
            $this->showMessage();
        }
        $data = array(
            'link_id' => $id,
            'link_type' => 'journal',
            'child_id' => $childId,
            'class_id' => $childModel->classid,
            'school_id' => $this->branchId,
            'content' => $mark == 1 ? 'No Reply Needed' : $content,
            'creator_type' => 'staff',
            'mark_as_staff' => $mark,
            'created_by' => (int)$uid,
            'attachments' => $attachments,
        );
        $res = CommonUtils::requestDsOnline('journal/saveComment', $data);
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res['data']);
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
        }
        $this->showMessage();
    }

    // 所有评论列表
    public function actionAll()
    {
        $this->checkType('leader');
        $page = Yii::app()->request->getParam("page", 1);
        $childId = Yii::app()->request->getParam("child_id", 0);

        $take = 20;
        $skip = ($page - 1) * $take;
        $res = CommonUtils::requestDsOnline('journal/allRepliedList', array(
            'skip' => (int)$skip,
            'take' => $take,
            'school_id' => $this->branchId,
            'child_id' => $childId,
        ));
        if ($res['code'] == 0) {
            $data = $res['data'];
            $childIdList = array_unique($res['data']['childIdList']);
            $res['data']['staffIdList'][] = Yii::app()->user->getId();
            $staffIdList = array_unique($res['data']['staffIdList']);

            $childs = array();
            $staff = array();
            if ($childIdList) {
                $childModels = ChildProfileBasic::model()->findAllByPk($childIdList);
                foreach ($childModels as $childModel) {
                    $childs[$childModel->childid] = array(
                        'name' => $childModel->getChildName(),
                        'avatar' => CommonUtils::childPhotoUrl($childModel->photo),
                        'class_name' => $childModel->ivyclass->title
                    );
                    if ($childModel->fid) {
                        $childs[$childModel->childid]["p_" . $childModel->fid] = 'f';
                    }
                    if ($childModel->mid) {
                        $childs[$childModel->childid]["p_" . $childModel->mid] = 'm';
                    }
                }
            }
            if ($staffIdList) {
                $staffInfoList = $this->getUserInfo($data['staffIdList']);
                foreach ($staffInfoList as $staffInfo) {
                    $staff[$staffInfo['uid']] = array(
                        'name' => $staffInfo['name'],
                        'avatar' => $staffInfo['photo'],
                    );
                }
            }
            $data['childs'] = $childs;
            $data['staff'] = $staff;
            unset($data['childIdList']);
            unset($data['staffIdList']);

            $this->addMessage('state', 'success');
            $this->addMessage('data', $data);
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
        }
        $this->showMessage();
    }

    // 查看反馈
    public function actionViewComment()
    {
        $id = Yii::app()->request->getParam("id");
        if (!$id) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', 'id error');
            $this->showMessage();
        }

        $res = CommonUtils::requestDsOnline('journal/viewComment', array(
            'id' => $id,
        ));
        if ($res['code'] == 0) {
            $data = $res['data'];
            $childIdList = array_unique($res['data']['childIdList']);
            $res['data']['staffIdList'][] = Yii::app()->user->getId();
            $staffIdList = array_unique($res['data']['staffIdList']);

            $childs = array();
            $staff = array();
            if ($childIdList) {
                $childModels = ChildProfileBasic::model()->findAllByPk($childIdList);
                foreach ($childModels as $childModel) {
                    $childs[$childModel->childid] = array(
                        'name' => $childModel->getChildName(),
                        'avatar' => CommonUtils::childPhotoUrl($childModel->photo),
                        'class_name' => $childModel->ivyclass->title
                    );
                    if ($childModel->fid) {
                        $childs[$childModel->childid]["p_" . $childModel->fid] = 'f';
                    }
                    if ($childModel->mid) {
                        $childs[$childModel->childid]["p_" . $childModel->mid] = 'm';
                    }
                }
            }
            if ($staffIdList) {
                $staffInfoList = $this->getUserInfo($data['staffIdList']);
                foreach ($staffInfoList as $staffInfo) {
                    $staff[$staffInfo['uid']] = array(
                        'name' => $staffInfo['name'],
                        'avatar' => $staffInfo['photo'],
                    );
                }
            }
            $data['childs'] = $childs;
            $data['staff'] = $staff;
            unset($data['childIdList']);
            unset($data['staffIdList']);

            $this->addMessage('state', 'success');
            $this->addMessage('data', $data);
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
        }
        $this->showMessage();
    }

    /**
     * 获取评论列表
     *
     * @return void
     */
    public function actionItem()
    {
        $id = Yii::app()->request->getParam('id');
        $childId = Yii::app()->request->getParam('child_id');
        if (!$id || !$childId) {
            $this->addMessage('state', 'fail');
            $this->addMessage("message", "参数错误");
            $this->showMessage();
        }

        $res = CommonUtils::requestDsOnline("journal/comments", array('id' => $id, 'childId' => (int)$childId));
        if ($res['code'] == 0) {
            $data = $res['data'];
            $staff = array();
            $data['staffIdList'][] = Yii::app()->user->getId();
            if ($data['staffIdList']) {
                $staffInfoList = $this->getUserInfo($data['staffIdList']);
                foreach ($staffInfoList as $staffInfo) {
                    $staff[$staffInfo['uid']] = array(
                        'name' => $staffInfo['name'],
                        'avatar' => $staffInfo['photo'],
                    );
                }
                unset($data['staffIdList']);
            }
            $data['staff'] = $staff;

            $this->addMessage("state", "success");
            $this->addMessage("data", $data);
        } else {
            $this->addMessage("state", "fail");
            $this->addMessage("message", $res['msg']);
        }
        $this->showMessage();
    }


    // 分配孩子到 journal
    public function actionAssignTargets()
    {
        $id = Yii::app()->request->getParam('id');
        $targets = Yii::app()->request->getParam('targets', array());
        if (!$id || !$targets) {
            $this->addMessage('state', 'fail');
            $this->addMessage("message", "参数错误");
            $this->showMessage();
        }
        $res = CommonUtils::requestDsOnline("journal/assignTargets/" . $id, array('targets' => $targets));
        if ($res['code'] == 0) {
            $this->addMessage("state", "success");
            $this->addMessage("data", $res['data']);
        } else {
            $this->addMessage("state", "fail");
            $this->addMessage("message", $res['msg']);
        }
        $this->showMessage();
    }

    // 移除孩子到 journal
    public function actionRemoveTargets()
    {
        $id = Yii::app()->request->getParam('id');
        $targets = Yii::app()->request->getParam('targets');
        if (!$id || !$targets) {
            $this->addMessage('state', 'fail');
            $this->addMessage("message", "参数错误");
            $this->showMessage();
        }
        $res = CommonUtils::requestDsOnline("journal/removeTargets/" . $id . '/' . $targets);
        if ($res['code'] == 0) {
            $this->addMessage("state", "success");
            $this->addMessage("data", $res['data']);
        } else {
            $this->addMessage("state", "fail");
            $this->addMessage("message", $res['msg']);
        }
        $this->showMessage();
    }

    // 移除 journal 下所有孩子
    public function actionRemoveAllTargets()
    {
        $id = Yii::app()->request->getParam('id');
        $targets = Yii::app()->request->getParam('targets');
        if (!$id || !$targets) {
            $this->addMessage('state', 'fail');
            $this->addMessage("message", "参数错误");
            $this->showMessage();
        }
        $res = CommonUtils::requestDsOnline("journal/removeAllTargets/" . $id);
        if ($res['code'] == 0) {
            $this->addMessage("state", "success");
            $this->addMessage("data", $res['data']);
        } else {
            $this->addMessage("state", "fail");
            $this->addMessage("message", $res['msg']);
        }
        $this->showMessage();
    }

    // 获取当前用户教授的课程或班级列表
    public function actionGetCategoryList()
    {
        $staff = Yii::app()->request->getParam('staff', 0);
        $calendarYids = $this->getCalendars();
        $startyear = $this->calendarStartYear[$calendarYids['currentYid']];
        $yid = $this->branchObj->schcalendar;
        $schoolId = $this->branchId;
        $uid = Yii::app()->user->getId();
        if ($staff > 0 && Yii::app()->user->checkAccess('ivystaff_it')) {
            $uid = $staff;
        }
        // 获取当前用户教授的中学课程
        if ($uid > 0) {
            $staff = User::model()->findByPk($uid);
        } else {
            $staff = $this->staff;
        }
        $staffPhoto = empty($staff->staffInfo->staff_photo) ? "blank.jpg" : $staff->staffInfo->staff_photo;
        $teacherInfo = array(
            "uid" => $uid,
            "name" => $staff->getName(),
            "photo" => OA::CreateOAUploadUrl('infopub/staff', $staffPhoto),
            'title' =>  CommonUtils::autoLang($staff->profile->occupation->cn_name, $staff->profile->occupation->en_name),
            'title_cn' => $staff->profile->occupation->cn_name,
            'title_en' => $staff->profile->occupation->en_name,
        );
        // 查找是否有评论
        $res = CommonUtils::requestDsOnline("journal/hasComment?staff={$uid}");
        if ($res['code'] != 0) {
            $this->addMessage("state", "fail");
            $this->addMessage("message", $res['msg']);
        }
        if ($res['data'] != true) {
            $this->addMessage("state", "success");
            $this->addMessage('data', array('list' => array(), 'type' => 'class', 'teacherInfo' => $teacherInfo));
            $this->showMessage();
        }
        Yii::import('common.models.timetable.*');
        $categoryList = TimetableCourseTeacher::getCourseList($yid, $schoolId, $startyear, $uid);
        if ($categoryList) {
            $this->addMessage("state", "success");
            $this->addMessage('data', array('list' => $categoryList, 'type' => 'course', 'teacherInfo' => $teacherInfo));
            $this->showMessage();
        } else {
            // 查找班级列表
            $classList = $this->getManageClassList($yid);
            foreach ($classList as $class) {
                $categoryList[] = array(
                    'id' => $class['classid'],
                    'title' => $class['title'],
                );
            }
            $this->addMessage("state", "success");
            $this->addMessage('data', array('list' => $categoryList, 'type' => 'class', 'teacherInfo' => $teacherInfo));
            $this->showMessage();
        }
    }

    // 获取当前用户教授的课程列表
    public function actionGetCourseList()
    {
        // 如果指定了类型为 course 则只返回 course
        $calendarYids = $this->getCalendars();
        $startyear = $this->calendarStartYear[$calendarYids['currentYid']];
        $yid = $this->branchObj->schcalendar;
        $schoolId = $this->branchId;
        $uid = Yii::app()->user->getId();

        Yii::import('common.models.timetable.*');
        $categoryList = TimetableCourseTeacher::getCourseList($yid, $schoolId, $startyear, $uid);
        $this->addMessage("state", "success");
        $this->addMessage('data', array('list' => $categoryList));
        $this->showMessage();

    }

    // 根据课程号获取下面的学生
    public function actionGetCourseChild()
    {
        $courseId = Yii::app()->request->getParam('courseId');
        if (!$courseId) {
            $this->addMessage('state', 'fail');
            $this->addMessage("message", "参数错误");
            $this->showMessage();
        }
        Yii::import('common.models.timetable.*');
        $yid = $this->branchObj->schcalendar;
        $crit = new CDbCriteria();
        $crit->compare('yid', $yid);
        $Timetable = Timetable::model()->find($crit);
        $crit = new CDbCriteria();
        $crit->compare('course_id', $courseId);
        $crit->compare('status', 1);
        $crit->compare('tid', $Timetable->id);
        $courseStudentList = TimetableStudentData::model()->findAll($crit);
        $childIdList = array();
        foreach ($courseStudentList as $item) {
            $childIdList[] = $item['child_id'];
        }
        $childModelList = ChildProfileBasic::model()->findAllByPk($childIdList);
        $childList = array();
        foreach ($childModelList as $child) {
            $childList[] = array(
                'id' => $child->childid,
                'birth' => $child->birthday_search,
                'name' => CHtml::encode($child->getChildName()),
                'classid' => intval($child->classid),
                'status' => $child->status,
                'gender' => $child->gender,
                'photo' => CommonUtils::childPhotoUrl($child->photo),
                'nick' => CHtml::encode($child->nick),
                'age' => CommonUtils::getAge($child->birthday),
                'country' => $child->country,
            );
        }
        $this->addMessage("state", "success");
        $this->addMessage('data', $childList);
        $this->showMessage();
    }

    // 根据课程或班级ID获取孩子信息
    public function actionGetChildList() {
        $id = Yii::app()->request->getParam('id');
        $type = Yii::app()->request->getParam('type');
        $yid = $this->branchObj->schcalendar;
        $schoolId = $this->branchId;
        $staff = Yii::app()->request->getParam('staff', 0);
        if (!$id || !$type) {
            $this->addMessage('state', 'fail');
            $this->addMessage("message", "参数错误");
            $this->showMessage();
        }
        $uid = 0;
        if ($staff > 0 && Yii::app()->user->checkAccess('ivystaff_it')) {
            $uid = $staff;
        }
        $res = CommonUtils::requestDsOnline("journal/getStudentByCategory?category={$type}&id={$id}&yid={$yid}&branchId={$schoolId}&staff={$uid}");
        if ($res['code'] == 0) {
            $this->addMessage("state", "success");
            $this->addMessage("data", $res['data']);
        } else {
            $this->addMessage("state", "fail");
            $this->addMessage("message", $res['msg']);
        }
        $this->showMessage();
    }

    // 根据孩子ID获取相关的反馈列表
    public function actionGetFeedbackByChild() {
        $childId = Yii::app()->request->getParam('childId');
        $pageNum = Yii::app()->request->getParam('pageNum');
        if (!$childId) {
            $this->addMessage('state', 'fail');
            $this->addMessage("message", "参数错误");
            $this->showMessage();
        }
        $staff = Yii::app()->request->getParam('staff', 0);
        if ($staff > 0 && Yii::app()->user->checkAccess('ivystaff_it')) {
            $uid = $staff;
        }
        $res = CommonUtils::requestDsOnline("journal/getCommentByChildId?childId={$childId}&pageNum={$pageNum}&staff={$uid}");
        if ($res['code'] == 0) {
            $this->addMessage("state", "success");
            $this->addMessage("data", $res['data']);
        } else {
            $this->addMessage("state", "fail");
            $this->addMessage("message", $res['msg']);
        }
        $this->showMessage();
    }


    public function actionClassList()
    {
        $yid = $this->branchObj->schcalendar;
        $this->addMessage('state', 'success');
        $this->addMessage('data', $this->getManageClassList($yid));
        $this->showMessage();

    }

    public function actionAsaCourseGroupList(){
        $input_data = array(
            'school_id'=>$this->branchId,
            'teacher_id'=>Yii::app()->user->getId()
        );
        $res = CommonUtils::requestDsOnline("asa/courseGroupList",$input_data);
        if ($res['code'] == 0) {
            $this->addMessage("state", "success");
            $this->addMessage("data", $res['data']);
        } else {
            $this->addMessage("state", "fail");
            $this->addMessage("message", $res['msg']);
        }
        $this->showMessage();
    }

    public function actionAsaCourseList(){
        $group_id = Yii::app()->request->getParam('group_id');
        $input_data = array(
            'school_id'=>$this->branchId,
            'group_id'=>$group_id,
            'teacher_id'=>Yii::app()->user->getId()
        );
        $res = CommonUtils::requestDsOnline("asa/courseList",$input_data);
        if ($res['code'] == 0) {
            $this->addMessage("state", "success");
            $this->addMessage("data", $res['data']);
        } else {
            $this->addMessage("state", "fail");
            $this->addMessage("message", $res['msg']);
        }
        $this->showMessage();
    }

    public function actionAsaCourseStudentList(){
        $id = Yii::app()->request->getParam('id');
        $input_data = array(
            'school_id'=>$this->branchId,
            'id'=>$id,
        );
        $res = CommonUtils::requestDsOnline("asa/courseStudentList",$input_data);
        if ($res['code'] == 0) {

            $this->addMessage("state", "success");
            $this->addMessage("data", $res['data']);
        } else {
            $this->addMessage("state", "fail");
            $this->addMessage("message", $res['msg']);
        }
        $this->showMessage();
    }

    public function actionSearchStudent()
    {
        $name = Yii::app()->request->getParam('child_name','');
        $group = Yii::app()->request->getParam('group','');
        if (empty($name)){
            $this->addMessage("state", "fail");
            $this->addMessage("message", 'child name error');
            $this->showMessage();
        }
        $input_data = array(
            'school_id'=>$this->branchId,
            'child_name'=>$name,
            'group'=>$group,
        );
        $res = CommonUtils::requestDsOnline("student/studentSearch",$input_data);
        if ($res['code'] == 0) {

            $this->addMessage("state", "success");
            $this->addMessage("data", $res['data']);
        } else {
            $this->addMessage("state", "fail");
            $this->addMessage("message", $res['msg']);
        }
        $this->showMessage();
    }
    // ******************************************************** 常用方法 ********************************************************

    // 获取 Journa 类型
    public function getJournalTypeList()
    {
        return array(
            "leader" => Yii::t('newDS', 'School Leader'),
            "teacher" => Yii::t('newDS', 'Teacher'),
        );
    }

    // 获取 Journa 分类
    public function getJournalCategory()
    {
        return array(
            "1" => Yii::t('newDS', 'Normal Journal'),
            "2" => Yii::t('newDS', 'Direct Message'),
        );
    }


    // 获取校长列表
    public function getLeaderList()
    {
        $cdids = array(54, 138, 147, 207, 182, 199, 151, 152, 154, 226, 251, 252, 340, 457, 551, 223, 552, 538,223); # 园长 副园长 行政园长 教学园长 启明星园长、小学副校长 校长 小学副校长&PYP协调员 中学设计老师兼学生事务主管 
        $criteria = new CDbCriteria();
        $criteria->compare('t.level', 1);
        $criteria->compare('profile.occupation_en', $cdids);
        $criteria->compare('profile.branch', $this->branchId);
        $criteria->order = 'profile.occupation_en DESC';
        $cdItems = User::model()->with('profile')->findAll($criteria);
        $leaderIdList = array();

        // 查找管理多校园的用户
        $criteria = new CDbCriteria();
        $criteria->compare('type', AdmBranchLink::ADM_TYPE_CD);
        $criteria->compare('schoolid', $this->branchId);
        $admUsers = AdmBranchLink::model()->findAll($criteria);
        $admIds = array();
        foreach ($admUsers as $admuser) {
            $admIds[] = $admuser->uid;
        }

        foreach ($cdItems as $cd) {
            if ($cd->profile->branch == $this->branchId || in_array($cd->uid, $admIds)) {
                $staffPhoto = empty($cd->staffInfo->staff_photo) ? "blank.jpg" : $cd->staffInfo->staff_photo;
                $leaderIdList[] = array(
                    'uid' => $cd->uid,
                    'name' => $cd->getName(),
                    'photo' => OA::CreateOAUploadUrl('infopub/staff', $staffPhoto),
                    'title' => CommonUtils::autoLang($cd->profile->occupation->cn_name, $cd->profile->occupation->en_name),
                    'title_cn' => $cd->profile->occupation->cn_name,
                    'title_en' => $cd->profile->occupation->en_name,
                );
            }
        }

        return $leaderIdList;
    }

    // 获取班级列表
    public function getClassList($yid)
    {
        $classList = array();
        $models = IvyClass::getClassList($this->branchId, $yid);
        foreach ($models as $class) {
            $classList[] = array(
                'classid' => $class->classid,
                'title' => $class->title,
                'classtype'=>$class->classtype,
            );
        }
        return $classList;
    }

    // 获取用户管理的班级列表
    public function getManageClassList($yid)
    {
        $classList = array();
        if (Yii::app()->user->checkAccess('t_A_CampusAdm')) {
            $classList = $this->getClassList($yid);
        } else {
            Yii::import("common.models.classTeacher.ClassTeacher");
            $crit = new CDbCriteria();
            $crit->compare("t.schoolid", $this->branchId);
            $crit->compare("t.yid", $yid);
            $crit->compare("t.teacherid",  Yii::app()->user->getId());
            $crit->with = "classInfo";
            $models = ClassTeacher::model()->findAll($crit);
            foreach ($models as $item) {
                $classList[] = array(
                    'classid' => $item->classInfo->classid,
                    'title' => $item->classInfo->title,
                    'class_type' => $item->classInfo->classtype,
                );
            }
        }
        return $classList;
    }

    public function getUid()
    {
        $staff = Yii::app()->request->getParam('staff', 0);

        $uid = Yii::app()->user->getId();
        if ($staff > 0 && $this->checkViewTeachersAccess()) {
            $uid = $staff;
        }
        return $uid;
    }

    // 检查是否具有查看其他老师 journal 的权限
    public function checkViewTeachersAccess()
    {
        if (Yii::app()->user->checkAccess('ivystaff_it')) {
            return true;
        }
        if (Yii::app()->user->checkAccess('ivystaff_cd')) {
            return true;
        }
        if (Yii::app()->user->checkAccess('ivystaff_teacher')) {
            return true;
        }
        if (Yii::app()->user->checkAccess('ivystaff_opschool')) {
            return true;
        }
        return false;
    }

    /**
     * 根据分页跟筛选条件，获取列表
     *
     * @param int $pageNum 当前页码
     * @param int $pageSize 每页数量
     * @param array $filter 筛选条件
     * @return int
     */
    public function getListCount($filter)
    {
        $res = CommonUtils::requestDsOnline('journal/count', array(
            'filter' => $filter,
        ));

        if ($res['code'] == 0) {
            return $res['data'];
        } else {
            return 0;
        }
    }

    /**
     * 根据分页跟筛选条件，获取列表
     *
     * @param int $pageNum 当前页码
     * @param int $pageSize 每页数量
     * @param array $filter 筛选条件
     * @return array
     */
    public function getList($pageNum, $pageSize, $filter, $isMine, $messages, $journals, $noAssigned, $searchText = '')
    {
        $skip = ($pageNum - 1) * $pageSize;
        $take = $pageSize;

        $res = CommonUtils::requestDsOnline('journal/list', array(
            'skip' => (int)$skip,
            'take' => (int)$take,
            'filter' => $filter,
            'isMine' => (int)$isMine,
            'messages' => (int)$messages,
            'journals' => (int)$journals,
            'noAssigned' => (int)$noAssigned,
            'searchText' => (string)$searchText,
            'orderBy' => array('publish_at', 'DESC'),
        ));

        if ($res['code'] == 0) {
            return $res['data'];
        } else {
            return array();
        }
    }

    /**
     * 根据ID获取单个 journal 的数据
     *
     * @param [type] $id journal ID
     * @return void
     */
    public function getJournal($id)
    {
        $res = CommonUtils::requestDsOnline('journal', array('_id' => $id));
        if ($res['code'] == 0) {
            $journal = $res['data'];
            $childs = array();
            // 查找分配的孩子信息
            if (isset($journal['targets']) || isset($journal['comment_child_ids'])) {
                $childIds = array_merge($journal['targets'], $journal['comment_child_ids']);
                if ($childIds) {
                    $childModels = ChildProfileBasic::model()->findAllByPk($childIds);
                    $i = 0;
                    foreach ($childModels as $childModel) {
                        $childs[$i] = array(
                            'id' => $childModel->childid,
                            'name' => $childModel->getChildName(),
                            'avatar' => CommonUtils::childPhotoUrl($childModel->photo),
                            'class_name' => $childModel->ivyclass->title
                        );
                        if ($childModel->fid) {
                            $childs[$i]["p_" . $childModel->fid] = 'f';
                        }
                        if ($childModel->mid) {
                            $childs[$i]["p_" . $childModel->mid] = 'm';
                        }
                        $i++;
                    }
                }
            }
            $journal['targets_info'] = $childs;
            return $journal;
        } else {
            return array();
        }
    }

    /**
     * 获取学科列表
     *
     * @return void
     */
    public function getSubjectList()
    {
        $config = CommonUtils::LoadConfig('CfgCampusProgram');
        return $config[Branch::TYPE_CAMPUS_DAYSTAR]["programs"];
    }

    /**
     * 获取某个老师分配的学科
     *
     * @param [type] $teacherId
     * @return void
     */
    public function getTeacherSubject($teacherId)
    {
        Yii::import("common.models.grades.TeacherSubjectLink");
        $list = array();
        $models = TeacherSubjectLink::model()->findAllByAttributes(array("schoolid" => $this->branchId, "teacher_uid" => $teacherId, "status" => 1));
        foreach ($models as $model) {
            $list[] = $model->subject_flag;
        }
        return $list;
    }

    public function initType()
    {
        $this->type = Yii::app()->request->getParam('type', 'teacer');
        if (Yii::app()->user->checkAccess('t_T_task')) {
            $this->managetypeList = array('teacher');
        }
        if (Yii::app()->user->checkAccess('ivystaff_it') || Yii::app()->user->checkAccess('ivystaff_cd')) {
            $this->managetypeList = array('teacher', 'leader');
        }
        // 查找待回复journal数量
        $this->toReplyNum = $this->getToReplyNum();
    }

    public function checkType($type)
    {
        if (!in_array($type, $this->managetypeList)) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '无权限');
            $this->showMessage();
        }
    }

    public function checkClass($classList)
    {
        $calendarYids = $this->getCalendars();
        $manageClassList = $this->getManageClassList($calendarYids['currentYid']);
        foreach ($classList as $item) {
            $flag = false;
            foreach ($manageClassList as $class) {
                if ($class['classid'] == $item) {
                    $flag = true;
                }
            }
            if (!$flag) {
                $this->addMessage("state", "fail");
                $this->addMessage("message", "未拥有操作班级ID的权限：" . $item);
                $this->showMessage();
            }
        }
    }

    /**
     * 根据用户ID获取名称，照片，职位等信息
     *
     * @param array $userIdList 用户ID
     * @return array
     */
    public function getUserInfo($userIdList)
    {
        $userModels = User::model()->findAllByPk($userIdList);
        $userInfoList = array();
        foreach ($userModels as $userModel) {
            $staffPhoto = empty($userModel->staffInfo->staff_photo) ? "blank.jpg" : $userModel->staffInfo->staff_photo;
            $userInfoList[] = array(
                'uid' => $userModel->uid,
                'name' => $userModel->getName(),
                'photo' => OA::CreateOAUploadUrl('infopub/staff', $staffPhoto),
                'title' => CommonUtils::autoLang($userModel->profile->occupation->cn_name, $userModel->profile->occupation->en_name),
                'title_cn' => $userModel->profile->occupation->cn_name,
                'title_en' => $userModel->profile->occupation->en_name,
                'level' => $userModel->level,
            );
        }
        return $userInfoList;
    }

    public function getChildData($classId)
    {
        $crit = new CDbCriteria();
        $crit->compare('schoolid', $this->branchId);
        $crit->compare('classid', $classId);
        $crit->compare('status', "<100");
        $crit->order = 'birthday ASC';
        $childrenObjs = ChildProfileBasic::model()->findAll($crit);
        $childData = array();
        foreach ($childrenObjs as $child) {
            $childData[] = array(
                'id' => $child->childid,
                'birth' => $child->birthday_search,
                'name' => CHtml::encode($child->getChildName(false, true)),
                'classid' => intval($child->classid),
                'status' => $child->status,
                'gender' => $child->gender,
                'photo' => CommonUtils::childPhotoUrl($child->photo),
                'nick' => CHtml::encode($child->nick),
                'age' => CommonUtils::getAge($child->birthday),
                'country' => $child->country,
            );
        }
        return  $childData;
    }

    public function actionFeedback()
    {
        $cs = Yii::app()->clientScript;
        $cs->registerCssFile(Yii::app()->themeManager->baseUrl . '/base/js/element/index.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/element/index.js');

        $this->branchSelectParams['urlArray'] = array('//mteaching/journals/feedback');
        $calendarYids = $this->getCalendars();
        $startyear = $this->calendarStartYear[$calendarYids['currentYid']];

        $data = array(
            "managetypeList" => $this->managetypeList,
            "startyear" => $startyear,
            "subjectList" => $this->getSubjectList(),
            "journalTypeList" => $this->getJournalTypeList(),
            "journalCategory" => $this->getJournalCategory(),
            "leaderList" => $this->getLeaderList(),
            "classList" => $this->getClassList($calendarYids['currentYid']),
            "gradeGroupList" => IvyClass::getGradeGroupListBySchoolId($this->branchId),
        );

        $this->render('feedback', array("data" => $data));
    }
}
