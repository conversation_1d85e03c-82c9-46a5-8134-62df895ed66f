<?php
    if($withToolBar){
        $widthHeight=array(
            'pageWidth' => 820,
            'pageHeight' => 1148,
            'bodyfontSize' => 16,
            'tableTheadSzie'=>14,
            'tableTBodySzie'=>10,
            'coverTextSize'=>60,
            'coverLogoSize'=>240,
            'coverFrontendImg' => 343,
            'avatarWdith' => 350,
            'avatarHeight' => 350,
            'avatarLeft' => 94,
            'avatarTop' => 178,
            'creationTop' => 580,
            'creationHeight' => 490,
            'teacherList'=>81,
            'chineseteacherList'=>81,
            'artImgPt'=>168,
            'artHeight'=>483,
            'artLeftTextWidth'=>360,
            'artTextP'=>50,
            'artTextPCn'=>105,
            'artRightTextWidth'=>310,
            'publicImgHeight'=>520,
            'publicTablePadding'=>5,
            'languageLeftWidth'=>400,
            'languageLeftWidthCn'=>320,
            'languageBtoPadding2Cn'=>70,
            'languageBtoPadding1'=>10,
            'languageBtoPadding1Cn'=>70,
            'languageBtoPadding2'=>50,
            'languageRightWidth'=>270,
            'languageImgHeight'=>547,
            'languageImgPt'=>125,
            'mathematicsTextWidth'=>434,
            'mathematicsTextRight'=>360,
            'mathematicsTextTop'=>105,
            'mathematicsTextTopCn'=>200,
            'mathematicsTextHieght'=>614,
            'mathematicsTextHieghtCn'=>520,
            'mathematicsImgHeight'=>423,
            'socialityImgHeiht'=>476,
            'socialityBtoPadding'=>40,
            'socialityBtoPaddingCn'=>80,
            'socialityImgPt'=>130,
            'socialityImgMtop'=>158,
            'socialityLogoTop'=>70,
            'bodyDeveImgTop'=>340,
            'bodyDeveImgHeight'=>635,
            'bodyDeveBtoWidth'=>670,
            'bodyDeveBtoHeight'=>113,
            'bodyDeveBtoPadding1'=>20,
            'bodyDeveBtoPadding1Cn'=>34,
            'bodyDeveBtoPadding2'=>30,
            'chinesebodyDeveTextWidth'=>430,
            'chinesebodyDeveTextTop'=>215,
            'chinesebodyDeveTextRight'=>80,
            'bodyDeveTextWidth'=>480,
            'bodyDeveTextTop'=>195,
            'bodyDeveTextRight'=>78,
            'teacherMesImgTop'=>170,
            'teacherMesImgWidth'=>440,
            'stuInfoSize'=>24,
            'toSumUpPostionLeft'=>54,
            'toSumUpPostionTop'=>250,
            'toSumUpPostionWidth'=>350,
            'toSumUpTitleTop'=>112,
            'toSumUpTitleSize'=>40,
            'lineHeight'=>26,
            'teacherMess'=>20,
            'teacherListBto'=>90,
            'teacherListLeft'=>70,
            'teacherNameEnSize'=>24,
            'enTeacherNameEnSize'=>18,
            'chinesedirectorName'=>32,
            'important-infoOneTop'=>240,
            'chineseimportant-infoOneTop'=>290,
            'importantOnepWidth'=>545,
            'importantTwoWidth'=>505,
            'importantTwoWTop'=>33,
            'chineseimportantTwoWTop'=>50,
            'importantTwoWLeft'=>25,
            'importantThreeWidth'=>479,
            'importantThreeTop'=>75,
            'chineseimportantThreeTop'=>115,
            'importantThreeLeft'=>197,
            'importantNoteWidth'=>372,
            'chineseimportantNoteWidth'=>325,
            'text6cDiv'=>670,
            'cLeftWidth'=>311,
            'sixImgDivWidth'=>470,
            'sixImgDivMargin'=>-235,
            'sixImgDivTop'=>180,
            'sixImgDivLeft'=>155,
            'profileBoxTop'=>55,
            'profileBoxRight'=>50,
        );
    }else{
        $widthHeight=array(
            'pageWidth' => 620,
            'pageHeight' => 877,
            'bodyfontSize' =>12,
            'tableTheadSzie'=>12,
            'tableTBodySzie'=>8,
            'coverTextSize'=>50,
            'coverLogoSize'=>184,
            'coverFrontendImg' => 259,
            'avatarWdith' => 250,
            'avatarHeight' => 250,
            'avatarLeft' => 77,
            'avatarTop' => 139,
            'creationTop' => 440,
            'creationHeight' => 350,
            'teacherList'=>61,
            'chineseteacherList'=>61,
            'artImgPt'=>135,
            'artHeight'=>365,
            'artLeftTextWidth'=>265,
            'artTextP'=>30,
            'artTextPCn'=>88,
            'artRightTextWidth'=>265,
            'publicImgHeight'=>393,
            'publicTablePadding'=>3,
            'languageLeftWidth'=>310,
            'languageLeftWidthCn'=>250,
            'languageBtoPadding1'=>10,
            'languageBtoPadding1Cn'=>50,
            'languageBtoPadding2'=>30,
            'languageBtoPadding2Cn'=>50,
            'languageRightWidth'=>220,
            'languageImgHeight'=>413,
            'languageImgPt'=>96,
            'mathematicsTextWidth'=>327,
            'mathematicsTextRight'=>270,
            'mathematicsTextTop'=>80,
            'mathematicsTextTopCn'=>145,
            'mathematicsTextHieght'=>472,
            'mathematicsTextHieghtCn'=>409,
            'mathematicsImgHeight'=>320,
            'socialityImgHeiht'=>360,
            'socialityBtoPadding'=>15,
            'socialityBtoPaddingCn'=>55,
            'socialityImgPt'=>100,
            'socialityImgMtop'=>124,
            'socialityLogoTop'=>55,
            'bodyDeveImgTop'=>258,
            'bodyDeveImgHeight'=>480,
            'bodyDeveBtoWidth'=>507,
            'bodyDeveBtoHeight'=>92,
            'bodyDeveBtoPadding1'=>18,
            'bodyDeveBtoPadding1Cn'=>27,
            'bodyDeveBtoPadding2'=>20,
            'chinesebodyDeveTextWidth'=>320,
            'chinesebodyDeveTextTop'=>160,
            'chinesebodyDeveTextRight'=>60,
            'bodyDeveTextWidth'=>355,
            'bodyDeveTextTop'=>150,
            'bodyDeveTextRight'=>65,
            'teacherMesImgTop'=>130,
            'teacherMesImgWidth'=>350,
            'stuInfoSize'=>18,
            'toSumUpPostionLeft'=>40,
            'toSumUpPostionTop'=>215,
            'toSumUpPostionWidth'=>300,
            'toSumUpTitleTop'=>85,
            'toSumUpTitleSize'=>30,
            'lineHeight'=>20,
            'teacherMess'=>14,
            'teacherListBto'=>80,
            'teacherListLeft'=>50,
            'teacherNameEnSize'=>18,
            'enTeacherNameEnSize'=>15,
            'chinesedirectorName'=>26,
            'important-infoOneTop'=>181,
            'chineseimportant-infoOneTop'=>218,
            'importantOnepWidth'=>395,
            'importantTwoWidth'=>390,
            'importantTwoWTop'=>8,
            'chineseimportantTwoWTop'=>35,
            'importantTwoWLeft'=>1,
            'importantThreeWidth'=>330,
            'importantThreeTop'=>54,
            'chineseimportantThreeTop'=>82,
            'importantThreeLeft'=>133,
            'importantNoteWidth'=>315,
            'chineseimportantNoteWidth'=>250,
            'text6cDiv'=>510,
            'cLeftWidth'=>230,
            'sixImgDivWidth'=>350,
            'sixImgDivMargin'=>-175,
            'sixImgDivTop'=>145,
            'sixImgDivLeft'=>125,
            'profileBoxTop'=>40,
            'profileBoxRight'=>30,
        );
    }
    if ($data['report']['startyear']>=2020 && $data['report']['semester'] == 1) {
        $tdNum = 5;
    } else {
        $tdNum = 4;
    }
?>
<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="language" content="en" />
    <title><?php echo CHtml::encode($this->pageTitle); ?></title>
</head>
<body>

    <?php if($withToolBar):?>
        <div class="navbar navbar-fixed-top navbar-inverse" role="navigation">
            <?php
            echo CHtml::form( $downloadActionUrl );
            echo CHtml::hiddenField('schoolid',     $params['schoolid']);
            echo CHtml::hiddenField('childid',      $params['childid']);
            echo CHtml::hiddenField('id',           $params['id']);
            echo CHtml::submitButton('Download PDF / 下载 PDF', array('style'=>'font-family: Microsoft Yahei','id'=>'submit'));
            echo CHtml::endForm();
            ?>
        </div>
    <?php endif;?>
    <!-- chinese english-->
    <div class='bgBox' style='position: relative;'>
        <?php if($withToolBar):?>
            <div class='bgck' id='bgck'></div>
            <div id="loading">
                <i></i>
            </div>
        <?php endif;?>
        <div  class="report-wrapper web-view <?php echo Yii::app()->language == 'zh_cn' ? 'chinese' : 'english' ?>">

            <div class="report-page-blank page-break-after cover-frontend">
                <div class='logo'><img src="https://m2.files.ivykids.cn/cloud01-file-8025768Fr0BYU9_dGhBYSSfGLPvBGibQTjJ.png" alt=""></div>
                <div class="bg"></div>
                <div class='img'></div>
                <div class='report'>
                    <div><?php echo Yii::t('principal', 'Semester<br>Report') ?></div>
                </div>
                <div class='coverName'>
                    <div><?php echo Yii::t('principal', 'Name:') ?>
                    <?php
                            $childName = $data['child']->getChildName();
                            $hasCNChars = CommonUtils::hasCNChars( $childName );
                            if( $hasCNChars > 0){
                                echo '<span>';
                            }else{
                                echo '<span >';
                            }
                            echo $childName . '</span>';
                        ?>
                    </div>
                    <div><?php echo Yii::t('principal', 'Semester:') ?>
                    <?php echo IvyClass::formatSchoolYear($data['report']['startyear'])?>
                    <?php echo ($data['report']['semester'] == 1) ? Yii::t("principal", "Autumn"): Yii::t("principal", "Spring")
                        ?>
                    </div>
                    <div><?php echo Yii::t('principal', 'Campus:') ?>
                        <?php
                        $title = $data['campusType']. ' ' .$data['campusTitle'];
                        echo $title;
                        ?>
                    </div>
                </div>
            </div>
            <div class="report-page-blank page-break-after important-info">
                <div class='one'>
                <p ><?php echo Yii::t('principal', 'The Ivy Schools Semester Report is based on observations made by the entire teaching team throughout each school day. When children are busy at work and play in our learning centers and engaged in activities that they are interested in, teachers are busy observing and assessing each child’s growth and learning; reflecting on how to support and extend learning. We assess each child’s skills and capabilities, and also identify each child’s individual learning style, special interests, strengths and challenges, so we can ensure that they develop toward their full potential.') ?></p>
                </div>
                <div style='clear:both'></div>
                <div  class='two'>
                <?php echo Yii::t('principal', 'The Ivy Courageous Curriculum encourages teachers to view children through the lens of Multiple Intelligences theory, accepting that we all learn in different ways and at varying paces. The semester report is a snapshot along the journey of your child’s development and education. It should not be used as a basis for comparison with other children. Additionally the report includes descriptions of learning domains. The descriptions of the learning domains are presented for general reference only. Each child develops their own unique way. No child develops equally across all domains at the same time. If you have not seen your child exhibiting the described behaviours this in no way an indication that your child is ‘behind’. Remember: childhood is a journey, not a race.') ?>
                </div>
                <div  class='three'>
                <?php echo Yii::t('principal', 'We are happy to share the growth and development that your child has made and help you to have a better understanding of your child. It is our pleasure to be your partner in your child’s education as we all strive to have the courage to change and inspire.') ?>
                </div>
                <div  class='note'>
                <?php if($data['report']['startyear'] < 2020) echo Yii::t('principal', "<p>Please Note:</p>If there is a field in your child's report that has been left unmarked, that simply means that this learning goal has not been addressed this semester.") ?>
                </div>
                </div>
                <div class="report-page-blank page-break-after text-6c">
                <div class='sixImgDiv'>
                    <img src="http://m1.files.ivykids.cn/images/sreport2/img/6c4.png" alt="">
                    <img class='sixImg' src="http://m1.files.ivykids.cn/images/sreport2/img/6c3.png" alt="">

                </div>
                <div class='sixText'>
                <p class='cLeft'><?php echo Yii::t('principal', "The Six C’s: Curiosity, Creativity, Competence, Conﬁdence, Character and aCCountability were formulated from a close reading of Howard Gardner, Jean Piaget, Jean Jaques Rousseau, Patty Hill Smith and other thinkers from the naturalist and progressive kindergarten movements combined with a synthesis of our combined decades of research and experience into the most important traits to cultivate in young children. We understand that children will need to be COURAGEOUS in order to be successful in their lives and live the mission of driving positive change for their communities and the world.") ?>
                </p>
                <p  class='cRight'><?php echo Yii::t('principal', "The development of these six traits is instrumental in allowing them to be curious about the world, apply their creativity to solutions to challenging problems and have the competence to be able to apply their skills in many contexts. With a foundation of character they will understand the importance of virtue and be accountable to themselves and others. The consistent practice of making choices will lead to the conﬁdence that comes from the experience of both success and failure.") ?></p>
                </div>

            </div>
            <div class="report-page-blank page-break-after studentInfo">
                <div class="profile-box">
                    <div>
                        <label><?php echo Yii::t('principal', 'Name') ?></label>
                        <?php
                            $childName = $data['child']->getChildName();
                            $hasCNChars = CommonUtils::hasCNChars( $childName );
                            if( $hasCNChars > 0){
                                echo '<span class="cn">';
                            }else{
                                echo '<span class="en">';
                            }
                            echo $childName . '</span>';
                        ?>
                    </div>
                    <div>
                    <label><?php echo Yii::t('principal', 'DOB') ?></label>
                    <?php echo $data['child']->birthday_search?>
                    </div>
                    <div>
                        <label><?php echo Yii::t('principal', 'Campus') ?></label>
                        <?php
                            $title = $data['campusT'];
                            $title = str_replace('(', '(<span class="cn">', $title);
                            $title = str_replace(')', '</span>)', $title);
                            echo $title;
                        ?>
                    </div>
                    <div>
                    <label><?php echo Yii::t('principal', 'Semester') ?></label>
                        <?php echo IvyClass::formatSchoolYear($data['report']['startyear'])?>
                        <?php echo ($data['report']['semester'] == 1) ? Yii::t("principal", "Autumn"): Yii::t("principal", "Spring")
                        ?>
                    </div>
                    <div>
                    <label><?php echo Yii::t('principal', 'Class') ?></label>
                        <?php echo $data['classTitle'];?>
                    </div>
                    <div>
                    <label class='classTeacher'><?php echo Yii::t('principal', 'Teachers') ?></label>
                        <?php
                            $listClass = (count($data['teachers']) < 9 ) ? 'a' :(
                            (count($data['teachers']) < 16 ) ? 'b' : 'c'
                            );
                        ?>
                        <div class="teacher-list teacher-list-<?php echo $listClass;?>">
                            <?php
                            if ($data['teachers']):?>
                                <?php foreach ($data['teachers'] as $tName):?>
                                    <span class="item"><?php echo $tName;?></span>
                                <?php endforeach;?>
                            <?php endif;?>
                        </div>
                        <div style="clear: both"></div>
                    </div>
                </div>
                <div class='profile-photo shadow-img'>
                <?php
                if(isset($data['photos'][$data['items']['Avatar']['media_id']]) && $data['photos'][$data['items']['Avatar']['media_id']]){
                    if ($data['items']['Avatar']['img_cropper']) {
                        echo CHtml::image($data['photos'][$data['items']['Avatar']['media_id']].$data['items']['Avatar']['img_cropper'].'/thumbnail/1000x1000');
                    } else {
                        echo CHtml::image($data['photos'][$data['items']['Avatar']['media_id']].'?imageView2/1/w/1000/h/1000');
                    }
                }
                elseif($data['child']->photo){
                    echo CHtml::image(CommonUtils::childPhotoUrl($data['child']->photo));
                }
                ?>
                </div>
                <div class="cover-bottom">
                    <?php
                    if($data['items']['FrontCoverWork']['media_id']){
                        echo CHtml::image($data['photos'][
                            $data['items']['FrontCoverWork']['media_id']
                            ] . "?imageMogr/auto-orient", "", array('class'=>'middle-size shadow-img')
                        );
                    }
                    ?>
                    <p class="tac">
                        <?php
                        if($data['items']['FrontCoverWork']['content']){
                            echo $data['items']['FrontCoverWork']['content'];
                        }
                        ?>
                    </p>
                </div>
            </div>
            <div class="report-page-blank page-break-after bodyDeve">
                <?php
                $learnItem = $data['learningDomains'][0];
                $childReport = isset($data['childLearningDomains'][$learnItem->id])?$data['childLearningDomains'][$learnItem->id]:null;
                $learnText = array_values(array_filter(explode("\n", $learnItem->getContent('intro'))));
                ?>
                <div class='bodyDeveTop'>
                    <?php echo $learnText[0];?>
                </div>
                <div class='bodyDeveImg'>
                    <?php
                    if($childReport->intro_img){
                        echo CHtml::image($data['photos'][
                        $childReport->intro_img
                        ] . $childReport->intro_img_cropper . '/thumbnail/2000x2000>', "", array()
                        );
                    }
                    ?>
                </div>
                <div class='bodyDeveBtoOpacity'>
                    <?php echo $learnText[1];?>
                </div>
            </div>
            <div class="report-page-blank page-break-after tableCom">
                <div class='img'>
                    <?php
                    if($childReport->items_img){
                        echo CHtml::image($data['photos'][
                        $childReport->items_img
                        ] . $childReport->items_img_cropper . '/thumbnail/2000x2000>', "", array()
                        );
                    }
                    ?>
                </div>
                <div class='TabPding'>    
                    <table class="arttable">
                        <thead>
                        <tr>
                            <th class='key'><?php echo  Yii::t("principal", "Key :subject Milestones", array(":subject"=>$learnItem->getContent()) )?></th>
                            <?php if( $tdNum == 5):?>
                                <th class='observed'><?php echo Yii::t('teaching', 'Not Observed');?></th>
                            <?php endif;?>
                            <th class='marking'><?php echo Yii::t('teaching', 'Making Progress');?></th>
                            <th class='meething'><?php echo Yii::t('teaching', 'Meeting Expectations');?></th>
                            <th class='exceeding'><?php echo Yii::t('teaching', 'Exceeding Expectations');?></th>
                        </tr>
                        </thead>
                        <tbody>
                        <?php foreach ($learnItem->items as $_item):?>
                            <tr>
                                <td><?php echo $_item->getContent();?></td>
                                <?php if( $tdNum == 5):?>
                                    <td>
                                    <?php if( isset($data['childLDItems'][$_item->id]) && $data['childLDItems'][$_item->id]->option == 4):?>
                                        <img src="http://m1.files.ivykids.cn//images/sreport2/img/not_observed.png" alt="">
                                    <?php endif;?>
                                    </td>
                                <?php endif;?>
                                <td>
                                    <?php if( isset($data['childLDItems'][$_item->id]) && $data['childLDItems'][$_item->id]->option == 1):?>
                                        <img src="http://m1.files.ivykids.cn/images/sreport2/img/marking.png" alt="">
                                    <?php endif;?>
                                </td>
                                <td>
                                    <?php if( isset($data['childLDItems'][$_item->id]) && $data['childLDItems'][$_item->id]->option == 2):?>
                                        <img src="http://m1.files.ivykids.cn/images/sreport2/img/meeting.png" alt="">
                                    <?php endif;?>
                                </td>
                                <td>
                                    <?php if( isset($data['childLDItems'][$_item->id]) && $data['childLDItems'][$_item->id]->option == 3):?>
                                        <img src="http://m1.files.ivykids.cn/images/sreport2/img/exceeding.png" alt="">
                                    <?php endif;?>
                                </td>
                                
                            </tr>
                        <?php endforeach;?>
                        <?php if( $childReport->proof_text):?>
                        <tr>
                            <td colspan='<?php echo $tdNum; ?>' class='lastTable'>
                                <div class='problem'>
                                    <strong><?php echo Yii::t('principal', 'Evidence from Teachers’ Observation:') ?></strong>
                                </div>
                                <div class='answer'>
                                    <i><?php echo $childReport->proof_text;?></i>
                                </div>
                            </td>
                        </tr>
                        <?php endif;?>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="report-page-blank page-break-after sociality">
                <?php
                $learnItem = $data['learningDomains'][1];
                $childReport = isset($data['childLearningDomains'][$learnItem->id])?$data['childLearningDomains'][$learnItem->id]:null;
                $learnText = array_values(array_filter(explode("\n", $learnItem->getContent('intro'))));
                ?>
                <div class='img'>
                    <?php
                    if($childReport->intro_img){
                        echo CHtml::image($data['photos'][
                        $childReport->intro_img
                        ] . $childReport->intro_img_cropper . '/thumbnail/2000x2000>', "", array('style'=>'width:100%;')
                        );
                    }
                    ?>
                </div>
                <div class='socialityBto'>
                    <div>
                        <?php echo $learnText[0];?>
                    </div>
                    <p>
                        <?php echo $learnText[1];?>
                    </p>
                    <p>
                        <?php echo $learnText[2];?>
                    </p>
                </div>
            </div>
            <div class="report-page-blank page-break-after tableCom">
                <div class='img'>
                    <?php
                    if($childReport->items_img){
                        echo CHtml::image($data['photos'][
                        $childReport->items_img
                        ] . $childReport->items_img_cropper . '/thumbnail/2000x2000>', "", array()
                        );
                    }
                    ?>
                </div>
                <div class='TabPding'>    
                    <table class="arttable">
                        <thead>
                        <tr>
                            <th class='key'><?php echo  Yii::t("principal", "Key :subject Milestones", array(":subject"=>$learnItem->getContent()) )?></th>
                            <?php if( $tdNum == 5):?>
                                <th class='observed'><?php echo Yii::t('teaching', 'Not Observed');?></th>
                            <?php endif;?>
                            <th class='marking'><?php echo Yii::t('teaching', 'Making Progress');?></th>
                            <th class='meething'><?php echo Yii::t('teaching', 'Meeting Expectations');?></th>
                            <th class='exceeding'><?php echo Yii::t('teaching', 'Exceeding Expectations');?></th>
                        </tr>
                        </thead>
                        <tbody>
                        <?php foreach ($learnItem->items as $_item):?>
                            <tr>
                                <td><?php echo $_item->getContent();?></td>
                                <?php if( $tdNum == 5):?>
                                    <td>
                                    <?php if( isset($data['childLDItems'][$_item->id]) && $data['childLDItems'][$_item->id]->option == 4):?>
                                        <img src="http://m1.files.ivykids.cn//images/sreport2/img/not_observed.png" alt="">
                                    <?php endif;?>
                                    </td>
                                <?php endif;?>
                                <td>
                                    <?php if( isset($data['childLDItems'][$_item->id]) && $data['childLDItems'][$_item->id]->option == 1):?>
                                        <img src="http://m1.files.ivykids.cn/images/sreport2/img/marking.png" alt="">
                                    <?php endif;?>
                                </td>
                                <td>
                                    <?php if( isset($data['childLDItems'][$_item->id]) && $data['childLDItems'][$_item->id]->option == 2):?>
                                        <img src="http://m1.files.ivykids.cn/images/sreport2/img/meeting.png" alt="">
                                    <?php endif;?>
                                </td>
                                <td>
                                    <?php if( isset($data['childLDItems'][$_item->id]) && $data['childLDItems'][$_item->id]->option == 3):?>
                                        <img src="http://m1.files.ivykids.cn/images/sreport2/img/exceeding.png" alt="">
                                    <?php endif;?>
                                </td>
                                
                            </tr>
                        <?php endforeach;?>
                        <?php if( $childReport->proof_text):?>
                        <tr>
                            <td colspan='<?php echo $tdNum; ?>' class='lastTable'>
                                <div class='problem'>
                                    <strong><?php echo Yii::t('principal', 'Evidence from Teachers’ Observation:') ?></strong>
                                </div>
                                <div class='answer'>
                                    <i><?php echo $childReport->proof_text;?></i>
                                </div>
                            </td>
                        </tr>
                        <?php endif;?>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="report-page-blank page-break-after mathematics">
                <?php
                $learnItem = $data['learningDomains'][2];
                $childReport = isset($data['childLearningDomains'][$learnItem->id])?$data['childLearningDomains'][$learnItem->id]:null;
                $learnText = array_values(array_filter(explode("\n", $learnItem->getContent('intro'))));
                ?>
                <div class='mathematicsText'>
                    <div>
                    <?php echo $learnText[0];?>
                    </div>
                    <p>
                        <?php echo $learnText[1];?>
                    </p>
                    <div>
                        <?php echo $learnText[2];?>
                    </div>
                </div>
                <div class='imgs'>
                    <?php
                    if($childReport->intro_img){
                        echo CHtml::image($data['photos'][
                        $childReport->intro_img
                        ] . $childReport->intro_img_cropper . '/thumbnail/2000x2000>', "", array('style'=>'')
                        );
                    }
                    ?>
                </div>
            </div>
            <div class="report-page-blank page-break-after tableCom">
                <div class='img'>
                    <?php
                    if($childReport->items_img){
                        echo CHtml::image($data['photos'][
                        $childReport->items_img
                        ] . $childReport->items_img_cropper . '/thumbnail/2000x2000>', "", array()
                        );
                    }
                    ?>
                </div>
                <div class='TabPding'>    
                    <table class="arttable">
                        <thead>
                        <tr>
                            <th class='key'><?php echo  Yii::t("principal", "Key :subject Milestones", array(":subject"=>$learnItem->getContent()) )?></th>
                            <?php if( $tdNum == 5):?>
                                <th class='observed'><?php echo Yii::t('teaching', 'Not Observed');?></th>
                            <?php endif;?>
                            <th class='marking'><?php echo Yii::t('teaching', 'Making Progress');?></th>
                            <th class='meething'><?php echo Yii::t('teaching', 'Meeting Expectations');?></th>
                            <th class='exceeding'><?php echo Yii::t('teaching', 'Exceeding Expectations');?></th>
                        </tr>
                        </thead>
                        <tbody>
                        <?php foreach ($learnItem->items as $_item):?>
                            <tr>
                                <td><?php echo $_item->getContent();?></td>
                                <?php if( $tdNum == 5):?>
                                    <td>
                                    <?php if( isset($data['childLDItems'][$_item->id]) && $data['childLDItems'][$_item->id]->option == 4):?>
                                        <img src="http://m1.files.ivykids.cn//images/sreport2/img/not_observed.png" alt="">
                                    <?php endif;?>
                                    </td>
                                <?php endif;?>
                                <td>
                                    <?php if( isset($data['childLDItems'][$_item->id]) && $data['childLDItems'][$_item->id]->option == 1):?>
                                        <img src="http://m1.files.ivykids.cn/images/sreport2/img/marking.png" alt="">
                                    <?php endif;?>
                                </td>
                                <td>
                                    <?php if( isset($data['childLDItems'][$_item->id]) && $data['childLDItems'][$_item->id]->option == 2):?>
                                        <img src="http://m1.files.ivykids.cn/images/sreport2/img/meeting.png" alt="">
                                    <?php endif;?>
                                </td>
                                <td>
                                    <?php if( isset($data['childLDItems'][$_item->id]) && $data['childLDItems'][$_item->id]->option == 3):?>
                                        <img src="http://m1.files.ivykids.cn/images/sreport2/img/exceeding.png" alt="">
                                    <?php endif;?>
                                </td>
                                
                            </tr>
                        <?php endforeach;?>
                        <?php if( $childReport->proof_text):?>
                        <tr>
                            <td colspan='<?php echo $tdNum; ?>' class='lastTable'>
                                <div class='problem'>
                                    <strong><?php echo Yii::t('principal', 'Evidence from Teachers’ Observation:') ?></strong>
                                </div>
                                <div class='answer'>
                                    <i><?php echo $childReport->proof_text;?></i>
                                </div>
                            </td>
                        </tr>
                        <?php endif;?>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="report-page-blank page-break-after arts">
                <div class='artImg1'>
                    <?php
                    $learnItem = $data['learningDomains'][3];
                    $childReport = isset($data['childLearningDomains'][$learnItem->id])?$data['childLearningDomains'][$learnItem->id]:null;
                    $learnText = array_values(array_filter(explode("\n", $learnItem->getContent('intro'))));

                    if($childReport->intro_img){
                        echo CHtml::image($data['photos'][
                        $childReport->intro_img
                        ] . $childReport->intro_img_cropper . '/thumbnail/2000x2000>', "", array('class'=>'')
                        );
                    }
                    ?>
                </div>
                <div class='artsLeft'>
                <?php echo $learnText[0];?>
                </div>
                <div class='artsRight'>
                    <?php echo $learnText[1];?>
                </div>
            </div>
            <div class="report-page-blank page-break-after arts2">
                <div class='arts2Img'>
                    <?php
                    if($childReport->items_img){
                        echo CHtml::image($data['photos'][
                        $childReport->items_img
                        ] . $childReport->items_img_cropper . '/thumbnail/2000x2000>', "", array()
                        );
                    }
                    ?>
                </div>
                <div class='TabPding'>    
                    <table class="arttable">
                        <thead>
                            <tr>
                                <th class='key'><?php echo  Yii::t("principal", "Key :subject Milestones", array(":subject"=>$learnItem->getContent()) )?></th>
                                <?php if( $tdNum == 5):?>
                                    <th class='observed'><?php echo Yii::t('teaching', 'Not Observed');?></th>
                                <?php endif;?>
                                <th class='marking'><?php echo Yii::t('teaching', 'Making Progress');?></th>
                                <th class='meething'><?php echo Yii::t('teaching', 'Meeting Expectations');?></th>
                                <th class='exceeding'><?php echo Yii::t('teaching', 'Exceeding Expectations');?></th>
                            </tr>
                        </thead>
                        <tbody>
                        <?php foreach ($learnItem->items as $_item):?>
                            <tr>
                                <td><?php echo $_item->getContent();?></td>
                                <?php if( $tdNum == 5):?>
                                    <td>
                                    <?php if( isset($data['childLDItems'][$_item->id]) && $data['childLDItems'][$_item->id]->option == 4):?>
                                        <img src="http://m1.files.ivykids.cn//images/sreport2/img/not_observed.png" alt="">
                                    <?php endif;?>
                                    </td>
                                <?php endif;?>
                                <td>
                                    <?php if( isset($data['childLDItems'][$_item->id]) && $data['childLDItems'][$_item->id]->option == 1):?>
                                    <img src="http://m1.files.ivykids.cn/images/sreport2/img/marking.png" alt="">
                                    <?php endif;?>
                                </td>
                                <td>
                                    <?php if( isset($data['childLDItems'][$_item->id]) && $data['childLDItems'][$_item->id]->option == 2):?>
                                        <img src="http://m1.files.ivykids.cn/images/sreport2/img/meeting.png" alt="">
                                    <?php endif;?>
                                </td>
                                <td>
                                    <?php if( isset($data['childLDItems'][$_item->id]) && $data['childLDItems'][$_item->id]->option == 3):?>
                                        <img src="http://m1.files.ivykids.cn/images/sreport2/img/exceeding.png" alt="">
                                    <?php endif;?>
                                </td>
                                
                            </tr>
                        <?php endforeach;?>
                            <?php if( $childReport->proof_text):?>
                                <tr>
                                    <td colspan='<?php echo $tdNum; ?>' class='lastTable'>
                                    <div class='problem'>
                                    <strong><?php echo Yii::t('principal', 'Evidence from Teachers’ Observation:') ?></strong>
                                    </div>
                                    <div class='answer'>
                                        <i><?php echo $childReport->proof_text;?></i>
                                    </div>
                                    </td>
                                </tr>
                            <?php endif;?>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="report-page-blank page-break-after language">
                <?php
                $learnItem = $data['learningDomains'][4];
                $childReport = isset($data['childLearningDomains'][$learnItem->id])?$data['childLearningDomains'][$learnItem->id]:null;
                $learnText = array_values(array_filter(explode("\n", $learnItem->getContent('intro'))));

                ?>
                <div class='lanImg'>
                    <?php
                    if($childReport->intro_img){
                        echo CHtml::image($data['photos'][
                        $childReport->intro_img
                        ] . $childReport->intro_img_cropper . '/thumbnail/2000x2000>', "", array('style'=>'width:100%;')
                        );
                    }
                    ?>
                </div>
                <div>
                    <div class='lanLeft'>
                        <p><?php echo $learnText[0];?></p>
                        <p><?php echo $learnText[1];?></p>
                    </div>
                    <p class='lanRight'>
                        <?php echo $learnText[2];?>
                    </p>
                </div>
            </div>
            <div class="report-page-blank page-break-after tableCom">
                <div class='img'>
                    <?php
                    if($childReport->items_img){
                        echo CHtml::image($data['photos'][
                        $childReport->items_img
                        ] . $childReport->items_img_cropper . '/thumbnail/2000x2000>', "", array()
                        );
                    }
                    ?>
                </div>
                <div class='TabPding'>                
                    <table class="arttable">
                        <thead>
                        <tr>
                            <th class='key'><?php echo  Yii::t("principal", "Key :subject Milestones", array(":subject"=>$learnItem->getContent()) )?></th>
                            <?php if( $tdNum == 5):?>
                                <th class='observed'><?php echo Yii::t('teaching', 'Not Observed');?></th>
                            <?php endif;?>
                            <th class='marking'><?php echo Yii::t('teaching', 'Making Progress');?></th>
                            <th class='meething'><?php echo Yii::t('teaching', 'Meeting Expectations');?></th>
                            <th class='exceeding'><?php echo Yii::t('teaching', 'Exceeding Expectations');?></th>
                        </tr>
                        </thead>
                        <tbody>
                        <?php foreach ($learnItem->items as $_item):?>
                            <tr>
                                <td><?php echo $_item->getContent();?></td>
                                <?php if( $tdNum == 5):?>
                                    <td>
                                    <?php if( isset($data['childLDItems'][$_item->id]) && $data['childLDItems'][$_item->id]->option == 4):?>
                                        <img src="http://m1.files.ivykids.cn//images/sreport2/img/not_observed.png" alt="">
                                    <?php endif;?>
                                    </td>
                                <?php endif;?>
                                <td>
                                    <?php if( isset($data['childLDItems'][$_item->id]) && $data['childLDItems'][$_item->id]->option == 1):?>
                                        <img src="http://m1.files.ivykids.cn/images/sreport2/img/marking.png" alt="">
                                    <?php endif;?>
                                </td>
                                <td>
                                    <?php if( isset($data['childLDItems'][$_item->id]) && $data['childLDItems'][$_item->id]->option == 2):?>
                                        <img src="http://m1.files.ivykids.cn/images/sreport2/img/meeting.png" alt="">
                                    <?php endif;?>
                                </td>
                                <td>
                                    <?php if( isset($data['childLDItems'][$_item->id]) && $data['childLDItems'][$_item->id]->option == 3):?>
                                        <img src="http://m1.files.ivykids.cn/images/sreport2/img/exceeding.png" alt="">
                                    <?php endif;?>
                                </td>
                            </tr>
                        <?php endforeach;?>
                        <?php if( $childReport->proof_text):?>
                        <tr>
                            <td colspan='<?php echo $tdNum; ?>' class='lastTable'>
                                <div class='problem'>
                                    <strong><?php echo Yii::t('principal', 'Evidence from Teachers’ Observation:') ?></strong>
                                </div>
                                <div class='answer'>
                                    <i><?php echo $childReport->proof_text;?></i>
                                </div>
                            </td>
                        </tr>
                        <?php endif;?>
                        </tbody>
                    </table>
                </div>
            </div>
            <?php
            $childReportZongJie = isset($data['childLearningDomains'][0])?$data['childLearningDomains'][0]:null;
            if($data['campusGroup'] == 30 && $childReportZongJie->proof_text){
                ?>
                <div class="report-page-blank page-break-after toSumUp">
                    <div class='toSumUpTitle'><strong><?php echo Yii::t('principal', 'Summary') ?></strong></div>
                    <div class='toSumUpPostion'>
                        <?php
                        $learnTexts = array_values(array_filter(explode("\n", $childReportZongJie->proof_text)));
                        foreach ($learnTexts as $val){
                        ?>
                            <p>
                                <?php echo $val;?>
                            </p>
                        <?php }?>
                    </div>
                </div>
            <?php } ?>
            <div class="report-page-blank page-break-after teacherMes">
                <p class="teacherMesImg">
                    <?php
                    if($data['items']['TeacherMessage']['media_id']){
                        echo CHtml::image($data['photos'] [
                        $data['items']['TeacherMessage']['media_id']
                        ] . "?imageMogr/auto-orient", "", array('class'=>'')
                        );
                    }
                    ?>
                </p>
                <div class='teacherMesText'>
                    <?php
                    if($data['items']['TeacherMessage']['content']){
                        echo nl2br( $data['items']['TeacherMessage']['content']);
                    }
                    ?>
                </div>
                <div style='clear:both'></div>
                <?php
                    $listClass = (count($data['teachers']) < 9 ) ? 'a' :(
                    (count($data['teachers']) < 16 ) ? 'b' : 'c'
                    );
                ?>
                <div class="teacher-list teacher-list-<?php echo $listClass;?> teaBtoList">
                    <?php
                    if ($data['teachersName']):?>
                    <?php foreach ($data['teachersName'] as $tName):?>
                    <span class="teacherName"><?php echo $tName;?></span>
                    <?php endforeach;?>
                    <?php endif;?>
                </div>
                <div class="teacherMesBto">
                <?php if(!$data['teachersName']): ?>
                        <div>
                        ___________________
                        </div>
                    <?php endif;?>
                    <div><strong class="en"><i><?php echo Yii::t('principal', 'Your Teachers') ?></i></strong></div>
                </div>
            </div>
            <div class="report-page-blank page-break-after groupPhoto">
                <div class="groupClass"><strong><i><?php echo $data['classTitle'];?></i></strong></div>
                <p class="groupPhotoImg">
                <?php
                if($data['items']['BackCoverPhoto']['media_id']){
                    echo CHtml::image($data['photos'] [
                    $data['items']['BackCoverPhoto']['media_id']
                    ] . "?imageMogr/auto-orient", "", array('class'=>'')
                    );
                }
                ?>
                </p>

                <div class="teacherMesBto">
                    ___________________<br>

                    <strong class="en"><i><?php echo Yii::t('principal', 'Your Campus Director') ?></i></strong>
                </div>
            </div>
            <div class='report-page-blank page-break-after socialInnovation'>
            </div>
            <div class="report-page-blank page-break-after end">
            </div>
        </div>
    </div>
    <script>
        var submit = document.querySelector('#submit');
        submit.onclick = ()=>{
            document.getElementById("loading").style.display = "block"
            document.getElementById("bgck").style.display = "block"
        }
    </script>
    <style>
        @font-face {
            font-family: 'teacherFont';
            src: url('https://m1.files.ivykids.cn/images/sreport2/img/FZHJJW.TTF')
        }
        @font-face {
            font-family: 'cnFontName';
            src: url('https://m1.files.ivykids.cn/images/sreport2/img/FZHCJW.TTF')
        }
        @font-face {
            font-family: 'enFontName';
            src: url('https://m1.files.ivykids.cn/images/sreport2/img/SEGOEPR.TTF')
        }
        @font-face {
            font-family: 'simhei';
            src: url('https://m1.files.ivykids.cn/images/sreport2/img/simhei.ttf')
        }
        @font-face {
            font-family: 'Trebuchet MS';
            src: url('https://m1.files.ivykids.cn/images/sreport2/img/trebuc.ttf')
        }
        body {
            font-family:'Trebuchet MS','simhei',Arial,Helvetica, sans-serif;
            font-size: 16px;
            line-height: 1.42857143;
            color: #333333;
            margin: 0;
            padding: 0;
            background-color: #efefef;
            <?php if($withToolBar):?>
                padding-top: 70px;
                padding-bottom: 20px;
            <?php endif;?>
        }
        .enFont{
            font-family: 'enFontName';
        }
        .cnFont{
            font-family: 'cnFontName';
        }
        .bgck{
            width:100%;
            height:100%;
            position: absolute;
            left:0;
            top:0;
            background:#000;
            opacity:0.3;
            z-index:9;
            display:none
        }
        #loading {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 999;
        }
        #loading i {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            width: 50px;
            height: 50px;
            border: 4px solid #fff;
            border-right-color: transparent;
            border-radius: 50%;
            animation: loadingAnimation 0.8s infinite Linear;
            margin: auto;
        }
        @keyframes loadingAnimation {
            from {
                transform: rotate(0deg);
            }
            to {
                transform: rotate(360deg);
            }
        }
        /* .report-page{
            padding:0;
            height: 877px;
            position: relative;
            background: url('http://m1.files.ivykids.cn/images/sreport2/header-01.jpg') no-repeat center top, url('http://m1.files.ivykids.cn/images/sreport2/footer-01.jpg') no-repeat center bottom;
            background-size: 100%;
            background-color: #ffffff;
        } */
        .page-opposite{
            background: url('http://m1.files.ivykids.cn/images/sreport2/header-02.jpg') no-repeat center top, url('http://m1.files.ivykids.cn/images/sreport2/footer-02.jpg') no-repeat center bottom;
            background-size: 100%;
            background-color: #ffffff;
        }
        .report-page-blank{
            padding:0;
            height: <?php echo $widthHeight['pageHeight']?>px;
            position: relative;
            background-size: 100%;
            background-color: #ffffff;
            margin-top:10px;
            /* display: inline-block;
            text-align: justify; */
        }
        .socialInnovation{
            background-image: url('http://m2.files.ivykids.cn/cloud01-file-8025768Fm_OQ2YUUeQhiIu1oVk2IHT3nsND.jpg');
            background-repeat:no-repeat;
            background-size: cover;
        }
        .chinese .socialInnovation{
            background-image: url('http://m2.files.ivykids.cn/cloud01-file-8025768Fg01RjrlruNTiKqR5B_I-0BvU3bF.jpg');
            background-repeat:no-repeat;
            background-size: cover;
        }
        .shadow-img{
            box-shadow: 0 .5rem 1rem rgba(0,0,0,.15)!important;
        }
        .cover-frontend{
            background-image: url('<?php echo $data['photos'][$data['items']['CoverWork']['media_id']] . "?imageMogr/auto-orient"?>');
            background-repeat:no-repeat;
            background-position: center;
            background-size: cover;
        }
        .cover-frontend .logo{
            width:<?php echo $widthHeight['coverLogoSize']?>px;
            position:absolute;
            right:0;
            top:0
        }
        .cover-frontend .logo img{
            width:100%
        }
        .cover-frontend .report{
            position:absolute;
            right:50px;
            bottom:30%;
            color:#fff;
            font-weight:700;
            font-size:<?php echo $widthHeight['coverTextSize']?>px;
            text-shadow:1px 1px 0px #999;
        }
        .cover-frontend .img{
            position: absolute;
            left: 0;
            bottom: 0;
            height: <?php echo $widthHeight['coverFrontendImg']?>px;
            width: 100%;
            background-image: url('http://m1.files.ivykids.cn/images/sreport2/img/cover.png');
            background-size: 100%;
        }
        .cover-frontend .coverName{
            position:absolute;
            left:30px;
            bottom:55px;
            font-size:<?php echo $widthHeight['stuInfoSize']?>px;
            color:#fff;
            width:80%
        }
        .cover-backend{
            background-image: url('http://m1.files.ivykids.cn/images/sreport2/cover-backend.jpg');
        }
        .important-info{
            background-image: url('http://m1.files.ivykids.cn/images/sreport2/img/importantEn1.jpg');
            color:#fff;
            padding: 0 70px;
        }
        .chinese .important-info{
            background-image: url('http://m1.files.ivykids.cn/images/sreport2/img/importantCn1.jpg');
            color:#fff;
            line-height: <?php echo $widthHeight['lineHeight']?>px

        }
        .important-info .one {
            width: 100%;
            padding-top: <?php echo $widthHeight['important-infoOneTop']?>px
            
        }
        .chinese .important-info .one {
            width: 100%;
            /* chineseimportant-infoOneTop */
            padding-top:<?php echo $widthHeight['chineseimportant-infoOneTop']?>px
        }
        .important-info .one p{
            width:<?php echo $widthHeight['importantOnepWidth']?>px;
            float:right
        }
        .chinese .important-info .one p{
            width: <?php echo $widthHeight['importantOnepWidth']?>px;
            float:right
        }
        .important-info .two{
            width:<?php echo $widthHeight['importantTwoWidth']?>px;
            padding-top:<?php echo $widthHeight['importantTwoWTop']?>px;
            padding-left:<?php echo $widthHeight['importantTwoWLeft']?>px;
        }
        .chinese .important-info .two{
            width:<?php echo $widthHeight['importantTwoWidth']?>px;
            padding-top:<?php echo $widthHeight['chineseimportantTwoWTop']?>px;
            padding-left:<?php echo $widthHeight['importantTwoWLeft']?>px;
        }
        .important-info .three{
            width:<?php echo $widthHeight['importantThreeWidth']?>px;
            padding-top:<?php echo $widthHeight['importantThreeTop']?>px;
            padding-left: <?php echo $widthHeight['importantThreeLeft']?>px;
        }
        .chinese .important-info .three{
            width:<?php echo $widthHeight['importantThreeWidth']?>px;
            padding-top:<?php echo $widthHeight['chineseimportantThreeTop']?>px;
            padding-left: <?php echo $widthHeight['importantThreeLeft']?>px;
        }
        .important-info .note{
            width:<?php echo $widthHeight['importantNoteWidth']?>px;
            padding-top:40px;
        }
        .chinese .important-info .note{
            width:<?php echo $widthHeight['chineseimportantNoteWidth']?>px;
            padding-top: 75px;
        }
        .text-6c{
            background-image: url('http://m1.files.ivykids.cn/images/sreport2/img/6c1.jpg');
        }
        .chinese .text-6c{
            background-image: url('http://m1.files.ivykids.cn/images/sreport2/img/6c2.jpg');
        }
        .sixImgDiv{
            width:<?php echo $widthHeight['sixImgDivWidth']?>px;
            height:<?php echo $widthHeight['sixImgDivWidth']?>px;
            position: absolute;
            left:50%;
            top: <?php echo $widthHeight['sixImgDivTop']?>px;
            /* background:#fff;
            border-radius:50%; */
            margin-left:<?php echo $widthHeight['sixImgDivMargin']?>px
        }
        .sixImgDiv img{
            position: absolute;
            left:0;
            top:0;
            width:100%;
            height:100%;
        }
        .sixImg{
            width:100%;
            height:100%;
        }

        .text-6c .sixText{
            padding:<?php echo $widthHeight['text6cDiv']?>px 50px 0 50px;
        }
        .text-6c .cLeft{
            float: left;
            width:<?php echo $widthHeight['cLeftWidth']?>px;
            padding-right: 50px;
            line-height: <?php echo $widthHeight['lineHeight']?>px;
            color:#14283D
        }
        .text-6c .cRight{
            float: right;
            width:<?php echo $widthHeight['cLeftWidth']?>px;
            color:#fff;
            line-height: <?php echo $widthHeight['lineHeight']?>px

        }
        .studentInfo{
            background-image: url('http://m1.files.ivykids.cn/images/sreport2/img/stuInfo.jpg');
            background-repeat:no-repeat;
            background-position:top left;
        }
        .studentInfo .profile-photo{
            position: absolute;
            left:<?php echo $widthHeight['avatarLeft']?>px;
            top: <?php echo $widthHeight['avatarTop']?>px;
            width: <?php echo $widthHeight['avatarWdith']?>px;
            height:<?php echo $widthHeight['avatarHeight']?>px;
            border-radius: 50%;
            -moz-border-radius: 50%;
            -webkit-border-radius:50%;
            overflow: hidden;
            background:#fff
        }
        .studentInfo .profile-photo img{
            width:100%;
            border-radius: 50%;
            -moz-border-radius: 50%;
            -webkit-border-radius:50%;
        }
        .studentInfo .profile-box{
            position: absolute;
            right:<?php echo $widthHeight['profileBoxRight']?>px;
            top:<?php echo $widthHeight['profileBoxTop']?>px;
            color:#fff;
            /* max-width:250px */
        }

        .studentInfo .profile-box div{line-height:<?php echo $widthHeight['lineHeight']?>px}
        .studentInfo .profile-box label:after{content: ' : '}
        .studentInfo .profile-box .classTeacher{
            float: left;
        }
        .studentInfo .profile-box .teacher-list{
            margin-left:<?php echo $widthHeight['teacherList']?>px;
        }
        .chinese .studentInfo .profile-box .teacher-list{
            margin-left:<?php echo $widthHeight['chineseteacherList']?>px;
        }
        .teacher-list span.item{display: inline-block; text-align: left;color: #fff;}
        .teacher-list-a span.item{display: block; }
        .teacher-list-b span.item{width: 110px;float: left; height: 25px;}

        .studentInfo .cover-bottom{
            position: absolute;
            width: 80%;
            top: <?php echo $widthHeight['creationTop']?>px;
            left: 10%;
        }
        .studentInfo .cover-bottom img{
           width:100%;
           max-height:<?php echo $widthHeight['creationHeight']?>px
        }
        .studentInfo .cover-bottom .tac{
            text-align: center;
            font-size:<?php echo $widthHeight['bodyfontSize'] ?>px;
            /* font-family:'EnFontName' */
        }
        .arts{
            background-image: url('http://m2.files.ivykids.cn/cloud01-file-8025768FqIE08GDPYzrq_KaOsL82vfF8gmz.jpg');
            background-repeat:no-repeat;
            background-size: cover;
        }
        .chinese  .arts{
            background-image: url('http://m2.files.ivykids.cn/cloud01-file-8025768Fi61i2tw2J4UH_8RblhF8SlTDsFD.jpg');
        }
        .arts .artImg1{
            width:100%;
            padding-top: <?php echo $widthHeight['artImgPt']?>px;
            height:<?php echo $widthHeight['artHeight']?>px
        }
        .arts .artImg1 img{
            width:100%;
            height:100%;
        }
        .chinese .arts .artsLeft,.chinese .arts .artsRight{
            padding-top: <?php echo $widthHeight['artTextPCn']?>px;
        }
        .arts .artsLeft{
            padding: <?php echo $widthHeight['artTextP']?>px;
            width: <?php echo $widthHeight['artLeftTextWidth']?>px;
            line-height: <?php echo $widthHeight['lineHeight']?>px;
            float:left
        }
        .arts .artsRight{
            padding-right: <?php echo $widthHeight['artTextP']?>px;
            padding-top: <?php echo $widthHeight['artTextP']?>px;
            width: <?php echo $widthHeight['artRightTextWidth']?>px;
            line-height:<?php echo $widthHeight['lineHeight']?>px;
            float:left
        }
        .arts2{
            /* background-image: url('http://m1.files.ivykids.cn/images/sreport2/img/art2Bg.jpg'); */
            /* padding: 0 50px; */
            background-repeat:no-repeat;
            background-size: cover;
        }
        .arts2Img{
            padding-bottom:15px
        }
        .arts2 .arts2Img img{
            width:100%;
            height:<?php echo $widthHeight['publicImgHeight'] ?>px
        }
        .TabPding{
            padding: 0 15px
        }
        table.arttable {
            border-collapse: collapse;
            width:100%;
        }
         table.arttable th {
            border-width:2px;
            border-style: solid;
            border-color: #fff;
            text-align:center;
            color:#fff;
            font-size:<?php echo $widthHeight['tableTBodySzie'] ?>px;
            font-weight:400;
        }
         table.arttable tr .key{
            background:#13ae67;
            font-size:<?php echo $widthHeight['tableTheadSzie'] ?>px;
            text-align:left;
            padding:10px 8px;
            font-weight:700
        }
        table.arttable tr .marking{
            background:#00a29a;
            width:50px
        }
        table.arttable  tr .meething{
            background:#2ea7e0;
            width:50px
        }
        table.arttable  tr .exceeding{
            background:#036eb8;
            width:50px
        }
        table.arttable  tr .observed{
            background:#449867;
            width:50px
        }
        table.arttable tbody tr td:first-child{
            background:#d9f1ef;
            padding:2px 8px;
            text-align:left;

        }
        table.arttable tbody  tr td:first-child+td{
            background:#E3F0E9;
        }
        table.arttable tbody tr td:first-child+td+td{
            background:#d9f1ef;
        }
        table.arttable tbody tr td:first-child+td+td+td{
            background:#dff1fa;
        }
        table.arttable tbody tr td:last-child{
            background:#d9e9f4;
        }
        table.arttable tbody tr td img{
            width:25px;
        }
        table.arttable tbody tr td {
            border-width:2px;
            font-size:<?php echo $widthHeight['tableTBodySzie'] ?>px;
            border-style: solid;
            border-color: #fff;
            text-align:center;
        }
        table.arttable tbody tr .lastTable{
            background:#c0e8e6 !important;
            padding:8px !important;
            border-width: 8px 2px 0 0;
        }
        table.arttable tbody tr .lastTable .problem{
           color:#20b378;
           font-size:11px
        }
        table.arttable tbody tr .lastTable .answer{
            padding:2px 0px
        }
        .language{
            background-image: url('http://m2.files.ivykids.cn/cloud01-file-8025768FjLaavRXMmlAJTlnf3CXeXlE2ohy.jpg');
            background-repeat:no-repeat;
            background-size: cover;
        }
        .chinese .language{
            background-image: url('http://m2.files.ivykids.cn/cloud01-file-8025768Frejh3eAPUARpelj1Y7TVFhsmq-z.jpg');
        }
        .language .lanLeft{
            width:<?php echo $widthHeight['languageLeftWidth'] ?>px;
            color:#fff;
            padding:<?php echo $widthHeight['languageBtoPadding1'] ?>px <?php echo $widthHeight['languageBtoPadding2'] ?>px;
            line-height:<?php echo $widthHeight['lineHeight']?>px;
            float:left
        }
        .chinese .language .lanLeft{
            width:<?php echo $widthHeight['languageLeftWidthCn'] ?>px;
            padding:<?php echo $widthHeight['languageBtoPadding1Cn'] ?>px <?php echo $widthHeight['languageBtoPadding2Cn'] ?>px;
        }
        .chinese .language .lanRight{
            padding:<?php echo $widthHeight['languageBtoPadding1Cn'] ?>px 0;
        }
        .language .lanRight{
            width:<?php echo $widthHeight['languageRightWidth'] ?>px;
            color:#fff;
            float:left;
            padding:<?php echo $widthHeight['languageBtoPadding1'] ?>px <?php echo $widthHeight['languageBtoPadding2'] ?>px 0 0;
            line-height:<?php echo $widthHeight['lineHeight']?>px
        }
        .language .lanImg{
            padding-top:<?php echo $widthHeight['languageImgPt'] ?>px;
            width: 100%;
            height:<?php echo $widthHeight['languageImgHeight'] ?>px;
        }
        .language .lanImg img{
            height:100%
        }
        .tableCom{
            /* padding:0 25px */
        }
        .tableCom .img{
            width:100%;
            padding-bottom:15px;
            /* padding-top:30px; */
            height:<?php echo $widthHeight['publicImgHeight'] ?>px
        }
        .tableCom .img img{
            width:100%;
            height:100%
        }
        .mathematics{
            background-image: url('http://m2.files.ivykids.cn/cloud01-file-8025768FhGmAoLnnExhuaSpZUwVoP05OZgR.jpg');
            background-repeat:no-repeat;
            background-size: cover;
        }
        .chinese .mathematics{
            background-image: url('http://m2.files.ivykids.cn/cloud01-file-8025768FgxqeG_f1wtP5npcKsHBHbqAP3ag.jpg');
        }
        .chinese .mathematics .mathematicsText{
            padding-top:<?php echo $widthHeight['mathematicsTextTopCn'] ?>px;
            height:<?php echo $widthHeight['mathematicsTextHieghtCn'] ?>px;
        }
        .mathematics .mathematicsText{
            padding-left:<?php echo $widthHeight['mathematicsTextRight'] ?>px;
            padding-top:<?php echo $widthHeight['mathematicsTextTop'] ?>px;
            width:<?php echo $widthHeight['mathematicsTextWidth'] ?>px;
            height:<?php echo $widthHeight['mathematicsTextHieght'] ?>px;
            overflow:hidden
        }
        .mathematics .imgs{
            height:<?php echo $widthHeight['mathematicsImgHeight'] ?>px;
            width:100%;
        }
        .mathematics .imgs img{
            height:100%;
            width:100%;
        }
        .sociality .img{
            width:100%;
            height:<?php echo $widthHeight['socialityImgHeiht'] ?>px;
            padding-top:<?php echo $widthHeight['socialityImgPt'] ?>px
        }
        .sociality .img img{
            width:100%;
            height:100%
        }
        .sociality {
            background-image: url('http://m2.files.ivykids.cn/cloud01-file-8025768FjpKMiyYaA2mrATeti7xCIiIeGSI.jpg');
        }
        .chinese .sociality {
            background-image: url('http://m2.files.ivykids.cn/cloud01-file-8025768Fh7ibar0dhzY1uVhDxZAbpnn9YgQ.jpg');
        }
        .sociality .socialityBto{
            padding:<?php echo $widthHeight['socialityBtoPadding']?>px 50px 0;
            color:#fff;
            line-height:<?php echo $widthHeight['lineHeight']?>px
        }
        .chinese .sociality .socialityBto{
            padding:<?php echo $widthHeight['socialityBtoPaddingCn']?>px 50px 0;
            color:#fff;
            line-height:<?php echo $widthHeight['lineHeight']?>px
        }
        .bodyDeve{
            background-image: url('http://m2.files.ivykids.cn/cloud01-file-8025768FgYxXj6RsTErhfugHIeP7PwwezBw.jpg');
            background-repeat:no-repeat;
            background-size: cover;
        }
        .chinese .bodyDeve{
            background-image: url('http://m2.files.ivykids.cn/cloud01-file-8025768FiVZobiAMXNRBsRFl_dKTI2LfT7B.jpg');
        }
        .chinese .bodyDeve .bodyDeveTop{
            color: #fff;
            width:<?php echo $widthHeight['chinesebodyDeveTextWidth'] ?>px;
            float: right;
            margin-top:<?php echo $widthHeight['chinesebodyDeveTextTop'] ?>px;
            margin-right:<?php echo $widthHeight['chinesebodyDeveTextRight'] ?>px;
            line-height:<?php echo $widthHeight['lineHeight']?>px
        }
        .bodyDeve .bodyDeveTop{
            color: #fff;
            width:<?php echo $widthHeight['bodyDeveTextWidth'] ?>px;
            float: right;
            margin-top:<?php echo $widthHeight['bodyDeveTextTop'] ?>px;
            margin-right:<?php echo $widthHeight['bodyDeveTextRight'] ?>px;
            line-height:<?php echo $widthHeight['lineHeight']?>px
        }
        .bodyDeve .bodyDeveImg{
            padding-top:<?php echo $widthHeight['bodyDeveImgTop'] ?>px;
            height:<?php echo $widthHeight['bodyDeveImgHeight'] ?>px;
            width:100%
        }
        .bodyDeve .bodyDeveImg img{
            width:100%;
            position: relative;
            height:100%
        }
        .bodyDeve .bodyDeveBtoOpacity{
            padding:<?php echo $widthHeight['bodyDeveBtoPadding1'] ?>px <?php echo $widthHeight['bodyDeveBtoPadding2'] ?>px;
            color:#fff;
            line-height:<?php echo $widthHeight['lineHeight']?>px
        }
        .chinese .bodyDeve .bodyDeveBtoOpacity{
            padding:<?php echo $widthHeight['bodyDeveBtoPadding1Cn'] ?>px <?php echo $widthHeight['bodyDeveBtoPadding2'] ?>px;
            color:#fff;
            line-height:<?php echo $widthHeight['lineHeight']?>px
        }
        .teacherMes{
            background-image: url('http://m1.files.ivykids.cn/images/sreport2/img/teaMessageEn.jpg');
            background-repeat:no-repeat;
            background-size: cover;
        }
        .chinese .teacherMes{
            background-image: url('http://m1.files.ivykids.cn/images/sreport2/img/teaMessageCn.jpg');
        }
        .teacherMes .teacherMesImg{
            width:100%;
            margin:auto;
            text-align:center;
            padding-top:<?php echo $widthHeight['teacherMesImgTop'] ?>px;
        }
        .teacherMes .teacherMesImg img{
            border-radius:10%;
            margin:auto;
            height:<?php echo $widthHeight['teacherMesImgWidth'] ?>px;
        }
        .teacherMes .teacherMesText{
            width:80%;
            margin-left:10%;
            margin-top:20px;
            font-size:<?php echo $widthHeight['teacherMess'] ?>px;
            font-family: 'teacherFont';
            display: inline-block;
            text-align: justify;
        }
        .teacherSign{
            clear: both;
            position: absolute;
            bottom: 55px;
            left: 0;
        }
        .teacherSign img{
            width: 120px;
        }
        .DirectorSign{
            width: 150px;
            position: absolute;
            right: 50px;
            bottom: 50px;
        }
        .DirectorSign img{
            width:100%;
        }
        .teacherMesBto{
            position: absolute;
            right:50px;
            bottom:50px;
            text-align:right;
            font-size:<?php echo $widthHeight['teacherMess'] ?>px;
        }
        .teaBtoList{
            position: absolute;
            right:50px;
            bottom:<?php echo $widthHeight['teacherListBto'] ?>px;
            margin-left:50px;
            text-align:right;
            /* font-size:<?php echo $widthHeight['teacherMess'] ?>px; */
            border-bottom:2px solid #000;
            max-width:620px
        }
        .teacherName{
            font-size:<?php echo $widthHeight['enTeacherNameEnSize'] ?>px;
            font-family: 'enFontName'
        }
        .teaBtoList .teacherName{
            padding-left:10px;
            color:#000 !important
        }
        .DirectorName{
            position: absolute;
            right:50px;
            bottom:<?php echo $widthHeight['teacherListBto'] ?>px;
            width:84%;
            text-align:right;
            font-size:<?php echo $widthHeight['teacherMess'] ?>px;
            color:#000
        }
        .director{
            font-size:<?php echo $widthHeight['directorName'] ?>px;
            font-family: 'enFontName'
        }
        .chinese  .director{
            font-size:<?php echo $widthHeight['chinesedirectorName'] ?>px;
            font-family: 'cnFontName'
        }
        .chinese .teacherName{
            font-size:<?php echo $widthHeight['teacherNameEnSize'] ?>px;
            font-family:'cnFontName'
        }
        .groupPhoto{
            background-image: url('http://m2.files.ivykids.cn/cloud01-file-8025768FhPxxM5kFRgwiwrJRvaOK5MpHTjB.jpg');
            background-repeat:no-repeat;
            background-size: cover;
        }
        .groupPhoto .groupClass{
            font-size:26px;
            padding:50px 50px 20px;
            color:#142A43
        }
        .groupPhoto .groupPhotoImg{
            width:100%;
        }
        .groupPhoto .groupPhotoImg img{
            width:100%;
        }
        .end{
            background-image: url('http://m1.files.ivykids.cn/images/sreport2/img/end.jpg');
            background-repeat:no-repeat;
            background-size: cover;
        }
        .toSumUp{
            background-image: url('http://m1.files.ivykids.cn/images/sreport2/img/mik.jpg');
            background-repeat:no-repeat;
            background-size: cover;
        }
        .toSumUpTitle{
            position: absolute;
            left:<?php echo $widthHeight['toSumUpPostionLeft'] ?>px;
            top:<?php echo $widthHeight['toSumUpTitleTop'] ?>px;
            color:#fff;
            font-size:<?php echo $widthHeight['toSumUpTitleSize'] ?>px
        }
        .toSumUpPostion{
            color:#fff;
            position: absolute;
            left:<?php echo $widthHeight['toSumUpPostionLeft'] ?>px;
            top:<?php echo $widthHeight['toSumUpPostionTop'] ?>px;
            width:<?php echo $widthHeight['toSumUpPostionWidth'] ?>px;
            line-height:<?php echo $widthHeight['lineHeight']?>px
        }
        .physical-growth-dev{
            background: url('http://m1.files.ivykids.cn/images/sreport2/physical-growth-dev.jpg') no-repeat center 20px, url('http://m1.files.ivykids.cn/images/sreport2/footer-02.jpg') no-repeat center bottom;
            background-size: 100%;
            background-color: #ffffff;
        }
        .fitness-test{
            background: url('http://m1.files.ivykids.cn/images/sreport2/header-01.jpg') no-repeat center top, url('http://m1.files.ivykids.cn/images/sreport2/footer-fitness.jpg') no-repeat center bottom;
            background-size: 100%;
            background-color: #ffffff;
        }
        .p2-title{
            font-size: 18px;
            color: #fff;
            padding-top: 65px;
            padding-left: 20px;
            padding-right: 100px;
            font-weight: bold;
        }
        .p2-text-en{
            padding: 0 144px 40px 60px;
        }
        .p2-text-en p{
            color: #fff;
        }
        .p2-text-cn{
            padding: 0 25px;
        }
        .p3-title{
            font-size: 22px;
            padding: 30px 0;
            text-align: center;
        }
        .p3-text{
            font-size: 15px;
            padding: 20px 25px 0 25px;
        }
        .ld-box1{
            padding: 60px 25px 0 25px;
        }
        .ld-box1 .box1-title{
            font-size: 15px;
            text-align: center;
            font-weight: bold;
        }
        .ld-box1 .box1-image{
            padding-bottom: 10px;
        }
        .ld-box2{
            padding: 60px 25px 0 25px;
        }
        .ld-box2 .box2-image{
            padding-bottom: 2px;
        }
        .ld-box2 table.box2-table{
            border: 1px;
            background-color: #000;
        }
        .ld-box2 table.box2-table td{
            background-color: #fff;
            padding: 5px;
        }
        .cd-signature{
            position: absolute;
            right: 60px;
            bottom: 120px;
            text-align: right;
            font-size: 12px;
        }
        .report-wrapper{
            width:<?php echo $widthHeight['pageWidth'] ?>px;
            margin: 0px auto;
            padding: 0;
            font-size:<?php echo $widthHeight['bodyfontSize'] ?>px;
        }
        .logo-contact-left .phone{font-size:18px;display:block;}
        .logo-contact-left .url{font-size:12px;}
        span.ld{
            color: #000;
            font-size: 110%;
            padding: 1px 3px;
            margin-right: 4px;
            background: #efefef;
        }
        span.ld:after{
        }
        hr.sep{
            margin: 5em 0 3em;
            border: 0;
            height: 1px;
            background: #333;
            background-image: -webkit-linear-gradient(left, #efefef, #ccc, #efefef);
            background-image:    -moz-linear-gradient(left, #efefef, #999, #ccc);
            background-image:     -ms-linear-gradient(left, #efefef, #999, #ccc);
            background-image:      -o-linear-gradient(left, #efefef, #999, #ccc);
        }
        .navbar-inverse {
            background-color: #333333;
            border-color: #efefef;
        }
        .navbar-inverse form {
            line-height: 50px;
            height: 50px;
            text-align: center;
        }
        .navbar-inverse form input {
            margin-top: 13px;
        }
        .navbar-fixed-top {
            top: 0;
            position: fixed;
            right: 0;
            left: 0;
            z-index: 1030;
            -webkit-transform: translate3d(0, 0, 0);
            transform: translate3d(0, 0, 0);
        }
        .navbar-inverse a{
            color: #f2f2f2;
        }
        .important-info .one , .important-info .two,.important-info .three,.text-6c .cLeft,.text-6c .cRight,.bodyDeve .bodyDeveTop,.bodyDeve .bodyDeveBtoOpacity,.sociality .socialityBto,.mathematics .mathematicsText,.arts .artsLeft,.arts .artsRight,.language .lanLeft,.language .lanRight{
            display: inline-block;
            text-align: justify;
        }
    }
    </style>
</body>
</html>
