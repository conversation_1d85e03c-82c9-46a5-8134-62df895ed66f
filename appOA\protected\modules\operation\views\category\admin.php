<?php
$this->breadcrumbs=array(
	'Ivy Pcategories'=>array('admin'),
	'Manage',
);

$this->menu=array(
	array('label'=>'Create IvyPCategory', 'url'=>array('create')),
);
?>
<h1>Manage Ivy Pcategories</h1>
<?php $this->widget('zii.widgets.grid.CGridView', array(
	'id'=>'ivy-pcategory-grid',
	'dataProvider'=>$model->search(),
	'filter'=>$model,
	'columns'=>array(
		'id',
		'title',
		array(
			'name' => 'update_timestamp',
            'sortable' => true,
            'value' => 'OA::formatDateTime($data->update_timestamp)',
            //'filter'=>CHtml::activeDropDownList($model, 'issuer_id',array('0'=>Yii::t("bview", '全部'))+$model->getAwardsIssuer()),
            'filter' => false,
		),
		array(
			'class'=>'CButtonColumn',
            'template' => '{update} {delete}',
		 	'updateButtonUrl' => 'Yii::app()->createUrl("/operations/category/create", array("id" => $data->id))',
		),
	),
)); ?>
