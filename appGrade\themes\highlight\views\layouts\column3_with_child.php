<?php $this->beginContent('//layouts/main'); ?>
    <div id="pfolio-nav-box">
        <div class="span-6">
            <h2><?php echo Yii::t("navigations", 'Information'); ?><em></em></h2>
        </div>
        <div class="span-18 last">
            <!--place holder-->
            <div id="pfolio-sub-nav">
                <ul class="pf-sub-nav">
                </ul>
            </div>
            <script language="javascript">
                $('#pfolio-sub-nav').html('<ul class="pf-sub-nav">' + $('ul.sf-menu .active ul').html() + '</ul>');
            </script>
            <!--place holder-->


        </div>
        <div class="clear"></div>
    </div>
    <div id="left-col" class="span-6 no-bg">

        <!--place holder-->
        <div id="current-sub-nav">
        </div>
        <script language="javascript">
            if ($('ul.sf-menu .active ul').html() != null)
                $('#current-sub-nav').html('<ul class="sub-nav">' + $('ul.sf-menu .active ul').html() + '</ul>');
        </script>
        <!--place holder-->

        <?php if ((!Yii::app()->params['hideShortCuts']) && !Yii::app()->user->getIsGuest()) : ?>
            <?php $this->widget('ext.quickLinks.QuickLinks', array()); ?>
        <?php endif; ?>
    </div>

    <div id="main-col" class="span-18 last">
        <?php if (isset($this->breadcrumbs)): ?>
            <?php
            $this->widget('zii.widgets.CBreadcrumbs', array(
                'links' => $this->breadcrumbs,
            ));
            $this->setSubPageTitle($this->breadcrumbs);
            ?><!-- breadcrumbs -->
        <?php endif ?>

        <?php echo $content; ?>

    </div>

    <div class="clear"></div>

<?php $this->endContent(); ?>