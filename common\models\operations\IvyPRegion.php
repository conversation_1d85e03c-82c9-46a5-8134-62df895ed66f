<?php

/**
 * This is the model class for table "ivy_p_region".
 *
 * The followings are the available columns in table 'ivy_p_region':
 * @property integer $id
 * @property string $en_title
 * @property string $cn_title
 * @property string $en_memo
 * @property string $cn_memo
 * @property integer $status
 * @property integer $create_userid
 * @property integer $update_timestamp
 */
class IvyPRegion extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @return IvyPRegion the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_p_region';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('en_title, cn_title, status, create_userid, update_timestamp', 'required'),
			array('status, create_userid, update_timestamp', 'numerical', 'integerOnly'=>true),
			array('en_title, cn_title', 'length', 'max'=>255),
			array('en_memo, cn_memo', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, en_title, cn_title, en_memo, cn_memo, status, create_userid, update_timestamp', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => Yii::t('operations', '标识'),
			'en_title' => Yii::t('operations', '英文标题'),
			'cn_title' => Yii::t('operations', '中文标题'),
			'en_memo' => Yii::t('operations', '英文备注'),
			'cn_memo' => Yii::t('operations', '中文备注'),
			'status' => Yii::t('operations', '状态'),
			'create_userid' => 'Create Userid',
			'update_timestamp' => Yii::t('operations', '更新日期'),
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('en_title',$this->en_title,true);
		$criteria->compare('cn_title',$this->cn_title,true);
		$criteria->compare('en_memo',$this->en_memo,true);
		$criteria->compare('cn_memo',$this->cn_memo,true);
		$criteria->compare('status',$this->status);
		$criteria->compare('create_userid',$this->create_userid);
		$criteria->compare('update_timestamp',$this->update_timestamp);

		return new CActiveDataProvider(get_class($this), array(
			'criteria'=>$criteria,
		));
	}
	
	/**
	 * 显示区域的状态
	 * @return string
	 * <AUTHOR>
	 * @copyright IvyGroup
	 */
	public function showStatus()
	{
		return ($this->status) ? Yii::t('operations', '隐藏') : Yii::t('operations', '显示');
	}
	
	/**
	 * 取得区域的标题
	 * @param int $status
	 * @return multitype:
	 */
	public function getPegionTitle($status = 0)
	{
		$regionList = array();
		$regionObj = $this->model()->findAll('status=:status',array(':status'=>$status));
		if (!empty($regionObj))
		{
			foreach ($regionObj as $value)
			{
				$regionList[$value->id] = (Yii::app()->language == 'zh_cn') ? $value->cn_title : $value->en_title;
			}
		}
		return $regionList;
	}
}