<style media="print">
    .view:nth-child(even) {
        page-break-after: always
    }
    body{
        font-family:"宋体";
    }
</style>
<style>
    .font{
        font-family:"宋体";
    }
    .table{
        margin-bottom: 0
    }
    .center{
        text-align: center;
        margin-top: 10px
    }
    h2{
        width: 100%;
        font-weight:800;
        margin-top:0px;
        border-bottom: 2px solid #000;
        margin-bottom:0;
    }
    h2 span{
     font-size:20px;
     font-weight:800
    }
   .head{
       margin-top:2.5px;
       margin-bottom: 0
   }
   .right{
    float: right;
   }
    .center{
        position: relative;
        margin-bottom:0
    }
    .center span{
        font-size:13px;
        position:absolute;
        right:5px;
        bottom:0;
    }
    .table > thead > tr > th, .table > tbody > tr > th, .table > tfoot > tr > th, .table > thead > tr > td, .table > tbody > tr > td, .table > tfoot > tr > td{
        padding:5.7px
    }
</style>
<?php
if($data){
$data2 = array_chunk($data,10);
foreach ($data2 as $item){
    $monty = 0;
    ?>
    <div class="bs-example view" data-example-id="bordered-table">
        <div >
            <h2 class="font"><strong>T<span>HE</span> I<span>VY</span> G<span>ROUP</span></strong></h2>
            <h4 class="head"><strong>Global Education For Children</strong> <strong class="right">www.ivygroup.org</strong></h4> 
        
        
        <h4 class="center"><strong>市内交通费报销明细* </strong><span>File No FINOO4-1</span></h4>
        <h4 class="center"><strong>Transportation Details (Within City)</strong></h4>
        </div>
        <table class="table table-bordered">
            <thead>
            <tr>
                <th style="width: 8%">序号<br>NO.</th>
                <th style="width: 15%">日期<br>Date</th>
                <th style="width: 15%">原因<br>Reason</th>
                <th style="width: 10%">起始地点<br>From:Location</th>
                <th style="width: 15%">起始时间<br>From:Location</th>
                <th style="width: 10%">到达地点<br>TO:Location</th>
                <th style="width: 15%">到达时间<br>TO:Time</th>
                <th style="width: 10%">金额<br>Amount</th>
            </tr>
            </thead>
            <tbody>
            <?php foreach ($item as $k=>$val){
                $monty += $val['money'];
                ?>
                <tr>
                    <td scope="row"><?php echo $k+1; ?></td>
                    <td><?php echo $val['time']; ?></td>
                    <td><?php echo $val['title']; ?></td>
                    <td></td>
                    <td></td>
                    <td><?php echo $val['schoolid']; ?></td>
                    <td></td>
                    <td><?php echo sprintf("%.2f", $val['money']);  ?></td>
                </tr>
            <?php }
            ?>
            </tbody>
            <?php
            $count = count($item);
            if($count < 10){
                for ($i = $count; $i < 10; $i++){
                    ?>
                    <tbody>
                    <tr>
                        <th><?php echo $i+1; ?></th>
                        <th></th>
                        <th></th>
                        <th></th>
                        <th></th>
                        <th></th>
                        <th></th>
                        <th></th>
                    </tr>
                    </tbody>
                <?php }
            } ?>
            <tbody>
                <tr>
                    <th colspan="2" class="text-center">申请人<br>Claimant</th>
                    <th><?php echo $this->staff->getName(); ?></th>
                    <th colspan="4" class="text-center">合计<br>Total Amount</th>
                    <th><?php echo sprintf("%.2f", $monty); ?></th>
                </tr>
            </tbody>
        </table>
        <div><i>* 本表作为 “FIN003 报销单” 的附件使用</i></div>
        <div style='margin-bottom: 10px'><i>* This form is an attachment to the "FIN003 Reimbursement Form"</i></div>
    </div>
<?php } }?>