<?php

class WeChatBasedController extends Controller
{
	public $appId = 'wx903fba9d4709cf10';
	public $appSecret = '388486edf736f1c078c808f291ce0075';
	public $openid;

	public $token = 'I34vyOn92L456in13e';
	public $securityKey = 'A23sW343eL8934ov2332E';

	public $layout = '//layouts/main';
	public $wechatObj = null;
	public $userMsg = null;
	public $wechatUser = null;
	public $isBound = 0;
	public $myChildObjs = array();
	public $activeChildIds = array();
	public $campusId = '';
	public $branchList = null;

	const STAT_BOUND = 10; //已经绑定
	const STAT_BOUND_EXPIRED = 2; //绑定过，当已取消绑定
	const STAT_BOUND_NONE = 0; //没有绑定

	public function init(){
		$this->siteTitle = Yii::t("global","IvyOnline");
		$this->wechatObj = new WXCallBack($this->token);
		$this->wechatObj->parseMsg();
		$this->userMsg = $this->wechatObj->getUserMsg();
		$this->wechatUser = WechatUser::model()->findByPk($this->userMsg->FromUserName);
		if(is_null($this->wechatUser)){
			$this->isBound = self::STAT_BOUND_NONE;
		}else{
			if($this->wechatUser->valid && $this->wechatUser->userid)
				$this->isBound = self::STAT_BOUND;
			else
				$this->isBound = self::STAT_BOUND_EXPIRED;
		}

		if( $this->isBound == self::STAT_BOUND ){
			$parent = IvyParent::model()->findByPk($this->wechatUser->userid);
			if (!empty($parent) && count(@unserialize($parent->childs))) {
				$cids = @unserialize($parent->childs);
				$this->myChildObjs = ChildProfileBasic::model()->findAllByPk($cids, array("index" => "childid","order"=>"childid DESC"));

				foreach($this->myChildObjs as $child){
					if($child->status < 100){
						$this->activeChildIds[] = $child->childid;
						if(!empty($child->schoolid)){
							$this->campusId = $child->schoolid;
						}
					}
				}
				//孩子id从大到小排列
				$this->branchList = Branch::model()->getBranchList(null, true);
				rsort($this->activeChildIds);
			}
		}
	}

	public function text($str="Testing", $endApp=true){
		$textTpl = "<xml>
					<ToUserName><![CDATA[%s]]></ToUserName>
					<FromUserName><![CDATA[%s]]></FromUserName>
					<CreateTime>%s</CreateTime>
					<MsgType><![CDATA[%s]]></MsgType>
					<Content><![CDATA[%s]]></Content>
					<FuncFlag>0</FuncFlag>
					</xml>";
		$msgType = "text";
		$resultStr = sprintf($textTpl, $this->userMsg->FromUserName, $this->userMsg->ToUserName, time(), $msgType, $str);
		Yii::app()->cache->set("weixinreply", $resultStr);
		echo $resultStr;
		//if($endApp){
		//	Yii::app()->end();
		//}
	}

    public function image($mediaId=""){
        $textTpl = "<xml>
					<ToUserName><![CDATA[%s]]></ToUserName>
					<FromUserName><![CDATA[%s]]></FromUserName>
					<CreateTime>%s</CreateTime>
					<MsgType><![CDATA[%s]]></MsgType>
                    <Image>
                        <MediaId><![CDATA[%s]]></MediaId>
                    </Image>
					</xml>";
        $msgType = "image";
        $resultStr = sprintf($textTpl, $this->userMsg->FromUserName, $this->userMsg->ToUserName, time(), $msgType, $mediaId);
        echo $resultStr;
    }

	public function link($title, $desc, $picUrl, $url)
	{
		$textTpl = "<xml>
					<ToUserName><![CDATA[%s]]></ToUserName>
					<FromUserName><![CDATA[%s]]></FromUserName>
					<CreateTime>%s</CreateTime>
					<MsgType><![CDATA[%s]]></MsgType>
					<ArticleCount>1</ArticleCount>
					<Articles>
					<item>
					<Title><![CDATA[%s]]></Title>
					<Description><![CDATA[%s]]></Description>
					<PicUrl><![CDATA[%s]]></PicUrl>
					<Url><![CDATA[%s]]></Url>
					</item>
					</Articles>
					</xml>";
		$msgType = "news";
		$resultStr = sprintf($textTpl, $this->userMsg->FromUserName, $this->userMsg->ToUserName, time(), $msgType, $title, $desc, $picUrl, $url);
		echo $resultStr;
	}
}
