<style>
[v-cloak] {
    display: none;
}
.yearData{
    padding:8px 10px;
    margin-right:10px;
    cursor:pointer;
    display:inline-block
}
.action{
    background:#337ab7;
    color:#fff;
    border-radius:3px
}
table tr td{
    vertical-align:middle !important
}
.add{
    display:inline-block;
    width:22px;
    height:22px;
    border:1px solid #fff;
    text-align:center;
    line-height:100%;
    font-size:20px;
    cursor:pointer;
    background:green;
    color:#fff
}
.files{
    display:inline-block !important
}
</style>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('//mcampus/default/index')) ?></li>
        <li><?php echo CHtml::link(Yii::t('site', 'Advanced'), array('//mcampus/default/index')) ?></li>
        <li class="active"><?php echo Yii::t('site', '奖学金申请记录'); ?></li>
    </ol>
    <div  id='container' v-cloak>
        <p class='mb20 list'>
            <template v-for='(list,index) in yearList'>
                <span :class="activeClass ==index?'action yearData':' yearData'"  @click='yearActive(index,list)'>{{list.title}}</span>
            </template>
        </p>
        <table class='table table-hover mt20 table-bordered' v-if='tableData.length!=0'>
            <tr>
                <th>姓名</th>
                <!-- <th>生日</th> -->
                <th>班级</th>
                <th>父亲姓名</th>
                <th>父亲电话/邮件</th>
                <!-- <th>父亲邮件</th> -->
                <th>母亲姓名</th>
                <th>母亲电话/邮件</th>
                <!-- <th>母亲邮件</th> -->
                <th>原因</th>
                <th>文件</th>
                <th>操作</th>
            </tr>
            <template v-for='(list,index) in tableData'>
                <template v-if='list.childInfo.length>1'>
                    <template v-for='(child,index) in list.childInfo'>
                        <tr>
                            <td>{{child.name}}</td>
                            <!-- <td width='100'>{{child.birthdate}}</td> -->
                            <td>{{child.grade}}</td>
                            <template v-if='index==0'>
                                <td :rowspan="list.childInfo.length">{{list.fName}}</td>
                                <td width='100' :rowspan="list.childInfo.length">
                                <p>{{list.fPhone}}</p><p>{{list.fEmail}}</p></td>
                                <!-- <td  :rowspan="list.childInfo.length">{{list.fEmail}}</td> -->
                                <td  :rowspan="list.childInfo.length">{{list.mName}}</td>
                                <td  :rowspan="list.childInfo.length" width='100'><p>{{list.mPhone}}</p><p>{{list.mEmail}}</p></td>
                                <!-- <td  :rowspan="list.childInfo.length">{{list.mEmail}}</td> -->
                                <td  :rowspan="list.childInfo.length">{{list.Reason}}</td>
                                <td  :rowspan="list.childInfo.length">
                                    <template v-for='(files,idx) in list.attach'>
                                        <a :href="'<?php echo $this->createUrl('ossfileRedit')?>&filePath=' + files" target="_blank" class='mr10'>附件{{idx+1}}</a>
                                    </template>
                                </td>

                                <td   :rowspan="list.childInfo.length" width='150'>
                                <a href="javascript:;" class="btn btn-xs btn-primary mr5" @click='modify(list)'><span class="glyphicon glyphicon-pencil"></span></a>
                                <a  class="btn btn-xs btn-info  mr5" 
                                
                                :href="'<?php echo $this->createUrl("print") ?>&id='+list.id"    target="_blank"><span class="glyphicon glyphicon-print"></span></a>

                                <a href="javascript:;" class="btn btn-xs btn-danger  mr5" @click='ListDel(list.id,index)'><span class="glyphicon glyphicon-trash"></span></a>
                                </td>
                            </template>      
                        </tr>
                    </template>   
                </template>
                <template v-else>
                    <tr>
                        <template v-for='(child,index) in list.childInfo'>
                            <td>{{child.name}}</td>
                            <!-- <td width='100'>{{child.birthdate}}</td> -->
                            <td>{{child.grade}}</td>
                        </template>   
                        <td>{{list.fName}}</td>
                        <td><p>{{list.fPhone}}</p><p>{{list.fEmail}}</p></td>
                        <!-- <td>{{list.fEmail}}</td> -->
                        <td>{{list.mName}}</td>
                        <td><p>{{list.mPhone}}</p><p>{{list.mEmail}}</p></td>
                        <!-- <td>{{list.mEmail}}</td> -->
                        <td>{{list.Reason}}</td>
                        <td  :rowspan="list.childInfo.length">
                                <template v-for='(files,idx) in list.attach'>
                                    <a :href="'<?php echo $this->createUrl('ossfileRedit')?>&filePath=' + files" target="_blank" class='mr10'>附件{{idx+1}}</a>
                                </template>
                                
                            </td>
                        <td width='150'>
                            <a href="javascript:;" class="btn btn-xs btn-primary mr5" @click='modify(list)'><span class="glyphicon glyphicon-pencil"></span></a>
                            <a class="btn btn-xs btn-info  mr5" :href="'<?php echo $this->createUrl("print") ?>&id='+list.id"  target="_blank"><span class="glyphicon glyphicon-print"></span></a>
                            <a href="javascript:;" class="btn btn-xs btn-danger  mr5" @click='ListDel(list.id,index)'><span class="glyphicon glyphicon-trash"></span></a>
                        </td>
                    </tr>
                </template>
            </template> 
        </table>
        <div class="alert alert-info" role="alert"   v-if='pages==0'>暂无数据</div>
        <nav aria-label="Page navigation " class='pull-right'>
            <ul class="pagination"  v-if='pages>20'>
                <li>
                <a href="#" aria-label="Previous">
                    <span aria-hidden="true">&laquo;</span>
                </a>
                </li>
                <li v-for="index of pages" :class="currentPage==index?'active':''" @click='dataList(currentSchool,index)'><a href="#" onclick>{{index}}</a></li>
                <li>
                <a href="#" aria-label="Next">
                    <span aria-hidden="true">&raquo;</span>
                </a>
                </li>
            </ul>
        </nav>
        <div class="modal fade bs-example-modal-lg" id="editModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true" data-backdrop="static">
			<div class="modal-dialog modal-lg">
				<?php $form = $this->beginWidget('CActiveForm', array(
				        'id' => 'expense-forms',
				        'enableAjaxValidation' => false,
				        'action' => ($model->id) ? $this->createUrl('expenesAdd', array('expenesId' => $model->id)) : $this->createUrl('update'),
				        'htmlOptions' => array('class' => 'J_ajaxForm form-horizontal', 'role' => 'form', 'enctype' => "multipart/form-data"),
				    )); ?>
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
						<h4 class="modal-title" id="myModalLabel">编辑</h4>
					</div>
					<div class="modal-body">
                        <form class="form-horizontal">
                            <input type="text" class="hidden" name="id" :value='editData.id'>
                            <div class="form-group">
                                <label for="inputEmail3" class="col-sm-2 control-label">原因</label>
                                <div class="col-sm-10">
                                    <textarea class="form-control" rows="3" :value='editData.Reason' name='reason'></textarea>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="inputPassword3" class="col-sm-2 control-label">已上传文件</label>
                                <div class="col-sm-10">
                                  <template v-for='(data,index) in cloneData.attach'>
                                  <p>
                                    <a :href="'<?php echo $this->createUrl('ossfileRedit')?>&filePath=' + data" target="_blank" class='mr10'>{{data}}</a>
                                    <a href="javascript:;" class="btn btn-xs btn-danger  mr5" @click='editDel(cloneData,index)'><span class="glyphicon glyphicon-trash"></span></a>
                                  </p>
                                  <input type="text" class="hidden" name="attach[]" :value='data'>
                                  </template>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-2 control-label">上传文件</label>
                                <div class='col-sm-10'>
                                   <p><input type="file" class="files" name="file[]">
                                   <button type="button"  @click='addFile()' class="btn btn-default btn-xs" aria-label="Left Align">
                                        <span class="glyphicon glyphicon-plus" aria-hidden="true"></span>
                                        </button></p> 
                                    <template v-for='(data,index) in files'>
                                        <p><input type="file" class="files" name="file[]">
                                         <button type="button"  @click='delFile(index)' class="btn btn-default btn-xs" aria-label="Left Align">
                                        <span class="glyphicon glyphicon-minus" aria-hidden="true"></span>
                                        </button></p>
                                    </template>
                                </div>
                            </div>
                        </form>
					</div>
					<div class="modal-footer">
						<button type="button" class="btn btn-primary J_ajax_submit_btn">确定</button>
						<button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
					</div>
				</div>
				<!-- /.modal-content -->
				<?php $this->endWidget(); ?>
			</div>
			<!-- /.modal -->
		</div>
    </div>
</div>
<script>
    var calendarInfo = <?php echo json_encode($calendarInfo) ?>;
    var nowYear = <?php echo json_encode($nowYear) ?>;
    console.log(calendarInfo)
    var container = new Vue({
        el: "#container",
        data: {
            yearList:calendarInfo,
            nowYear:nowYear,
            activeClass:-1,
            tableData:[],
            editData:'',
            cloneData:'',
            pages:'-1',
            currentPage:'',
            currentSchool:'',
            files:[],
            add:''
        },
        created:function(){
            for(var i=0;i<calendarInfo.length;i++){
                if(calendarInfo[i].is_current==1){
                    this.activeClass=i
                    this.currentSchool=calendarInfo[i]
                    this.dataList(calendarInfo[i],1)
                }else{
                    this.activeClass=0
                    this.currentSchool=calendarInfo[0]
                    this.dataList(calendarInfo[0],1)
                }
            }
        },
        methods: {
            yearActive(index,data){
                this.currentSchool=data
                this.activeClass=index
                this.dataList(data,1)
            },
            modify(data){
                this.cloneData = JSON.parse(JSON.stringify(data));
                this.editData=data
                $('#editModal').modal('show')
            },
            dataList(data,num){
                this.currentSchool=data
                console.log(data)
                var that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("findScholarShip") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        schoolYear:data.startyear,
                        yid:data.yid,
                        page:num,
                        pageNum:20
                    },
                    success: function(res) {
                        if (res.state == "success") {
                            that.pages=res.data.total
                            console.log(res.data.total)
                            that.currentPage=res.data.currentPage
                            console.log(that.currentPage)
                            that.tableData=res.data.info
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: res.message
                            });
                        }
                    },
                    error: function() {
                        resultTip({
                            error: 'warning',
                            msg: '请求错误'
                        });
                    }
                });
            },
            ListDel(data,index){
                var that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("delScholarship") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        id:data,
                    },
                    success: function(res) {
                        if (res.state == "success") {
                            for(var i=0;i<that.tableData.length;i++){
                                if(that.tableData[i].id==data){
                                    that.tableData.splice(i,1);
                                }
                            }
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: res.message
                            });
                        }
                    },
                    error: function() {
                        resultTip({
                            error: 'warning',
                            msg: '请求错误'
                        });
                    }
                });
            },
            editDel(data,index){
                console.log(data)
                data.attach.splice(index, 1);
            },
            addFile(){
                this.add ++;
                this.files.push(this.add)
                console.log(this.files)
            },
            delFile(index){
                this.files.splice(index, 1);
            },
            print(){
                window.location.href='<?php echo $this->createUrl("print") ?>'
            }
        }
    })
    function cbSuccess(data){
        $('#editModal').modal('hide')
        $('.files').val('')
        container.add=''
        container.files=[]
        container.dataList(container.currentSchool,container.currentPage)
    }
</script>
<!-- 显示底部学校选择栏 -->
<?php $this->renderPartial('//layouts/common/branchSelectBottom'); ?>
