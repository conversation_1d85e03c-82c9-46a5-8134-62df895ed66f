<?php

/*
 * 通知类API
 */
class ClubApiController extends Controller{
    
    public function beforeAction($action){
        if(!OA::isProduction()){
            return true;
        }
        if(Yii::app()->request->isPostRequest && isset($_POST['postTime']) && isset($_POST['flagid']))
        {
            $myCode = md5(sprintf("%s&%s&%s", $_POST['postTime'], $_POST['flagid'], OA::SECURITY_KEY));
			$time = time();
			if($myCode == $_POST['postKey'] && abs($time - $_POST['postTime'] ) < 300 ){
                return TRUE;
            }
        }
        return false;
    }
    

    public function actionProcessArticle()
    {
        $id = Yii::app()->request->getParam('flagid', 0);

        if ($id){
            Yii::import('common.models.club.ClubArticle');
            Yii::import('common.models.club.ClubArticleContent');

            ClubArticle::model()->genArticleCache($id);
        }
    }

    public function actionGetArticle()
    {
        $cacheKey = Yii::app()->request->getParam('cacheKey');

        $article = Yii::app()->cache->get($cacheKey);
        echo $article;
    }
}
?>
