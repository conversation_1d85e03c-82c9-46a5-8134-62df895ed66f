<div class="container-fluid" id='box'>
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('//mcampus/default/index')) ?></li>
        <li class="active"><?php echo Yii::t('schoolTeams','Phoenix Athletic Program');?></li>
    </ol>
    <div >
        <el-tabs v-model="activeName" @tab-click="handleClick">
            <el-tab-pane label="<?php echo Yii::t("schoolTeams", "Homepage"); ?>" name="home"></el-tab-pane>
            <el-tab-pane label="<?php echo Yii::t("schoolTeams", "Teams"); ?>" name="list"></el-tab-pane>
        </el-tabs>
        <div class='flex' v-if='configData.configList'>
            <div class='flex1 mt16 mr24'>
                <div style='margin-top:9px' class='flex align-items mb24'>
                    <button type="button" class="btn btn-primary" @click='addTeam()'><span class='el-icon-circle-plus-outline'></span> <?php echo Yii::t("schoolTeams", "New Team"); ?></button>
                    <span class='flex1 ml24'>
                        <label class="checkbox-inline" v-for='(list,index) in configData.configList.grade_group'>
                            <input type="checkbox" v-model='grade_group' @change='filterGrade()'  :value="list.key"> {{list.value}}
                        </label>
                    </span>
                    <el-select v-model="startYear" placeholder="<?php echo Yii::t("global", "Please Select"); ?>" size='small'>
                        <el-option
                        v-for="item in configData.startYearList"
                        :key="item.key"
                        :label="item.value"
                        :value="item.key">
                        </el-option>
                    </el-select>
                </div>
                <div ref='content' v-if='Object.keys(listData).length' class='overflow-y scroll-box'  @scroll="handleScroll" :style="'max-height:'+(height-310)+'px;overflow-x: hidden;'">
                    <div class='mt10 ' :id='"list"+key' :ref="setSectionRef" v-for='(list,key,indexs) in listData'>   
                        <div class='mt24'>
                            <span class='color3 font16 fontBold'> {{getTitle(key,configData.categoryList,'id','title')}} </span>
                            <span class='ml16 blueColor cur-p font14' @click='addTeam(key)' ><span class='el-icon-plus'></span> <?php echo Yii::t('global','Add');?></span>
                        </div>
                        <div class='row'>
                            <template v-for='(item,index) in list'>
                                <div class='col-md-3' v-if='grade_group.indexOf(item.grade_group)!=-1'>
                                    <div class='detailList' >
                                        <div class='flex'>
                                            <span class='font14 color3 flex1'>{{item.title}}</span>
                                            <el-popover
                                                :ref="`popover-${item.id}`"
                                                placement="bottom"
                                                width="200"
                                                v-model="tipVisibles[item.id]"
                                                trigger="click">
                                                <div class='text-center p12'>
                                                    <div class='color3 font14 mb16 fontBold'><?php echo Yii::t("schoolTeams", "QRcode for Preview");?></div>
                                                    <div id='wechatQrcode' class='wechatQrcode'></div>
                                                </div>
                                                <button type="button" class="btn btn-default btn-xs mr8" slot="reference" style='line-height: 1;padding:3px 4px;' @click="popoverHide(item.id)"> <span class='glyphicon glyphicon-qrcode font14' ></span></button>
                                            </el-popover>
                                            <el-dropdown>
                                                <span class="el-dropdown-link">
                                                <button type="button" class="btn btn-default btn-xs" style='line-height: 18px;padding:0px 5px 4px'>...</button>
                                                </span>
                                                <el-dropdown-menu slot="dropdown">
                                                    <el-dropdown-item @click.native='editDetail(item)'><?php echo Yii::t("withdrawal", "Edit");?></el-dropdown-item>
                                                    <el-dropdown-item @click.native='editAdmin(item)'><?php echo Yii::t("schoolTeams", "Members");?></el-dropdown-item>
                                                    <el-dropdown-item @click.native='delTeam(item)'><?php echo Yii::t("newDS", "Delete");?></el-dropdown-item>
                                                </el-dropdown-menu>
                                            </el-dropdown>
                                        </div>
                                        <div class='flex align-items mt10'>
                                            <span class='flex1'>
                                                <span class='tagLabel'>{{getTitle(item.grade_group,configData.configList.grade_group,'key','value')}}</span>
                                                <span class='tagLabel' :class='item.type==1?"blueBg":item.type==2?"redBg":"yellowBg"'>{{getTitle(item.type,configData.configList.team_type,'key','value')}}</span>
                                            </span>
                                            <span class=' blueColor cur-p'  @click='editAdmin(item)'>
                                                <span class='el-icon-user font14 '></span>
                                                <span class='font14  ml4'>{{item.member_num}}</span>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </template>
                        </div>
                        <hr>
                    </div>
                </div>
                <div v-else-if='dataLoading'>
                    <el-empty description="<?php echo Yii::t("ptc", "No Data");?>"></el-empty>
                </div>
            </div>
            <div class='' style='background:#F2F3F5;width:180px'>
                <div class='overflow-y scroll-box' :style="'height:'+(height-229)+'px;overflow-x: hidden;'">
                    <div v-for='(list,index) in categoryListFilter' class='filterList' :class="{ active: isActiveSection(list.id) }"  @click='scrollList(list.id)'>
                        <div class='flex1'> {{list.title}}</div>
                        <span class="badge font12"> {{listDataCopy[list.id].length}}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- 成员 -->
    <div class="modal fade" id="editStaffModal" tabindex="-1" role="dialog" data-backdrop="static" data-backdrop="static">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentCommentsModalLabel"><?php echo Yii::t("schoolTeams", "Members");?></h4>
                </div>
                <div class="modal-body p24 overflow-y scroll-box" :style="'max-height:'+(height-180)+'px;overflow-x: hidden;'">
                    <div class='mb24'><button type="button" class="btn btn-primary" @click='addStaff'><span class='el-icon-circle-plus-outline'></span> <?php echo Yii::t("teaching", "Add members");?></button></div>
                    <el-table
                    v-if='configData.configList'
                        :data="membersList"
                        style="width: 100%"
                        :header-cell-style="{background:'#fafafa',color:'#333'}"
                        >
                        <el-table-column
                        prop="date"
                        label="<?php echo Yii::t("labels", "Student"); ?>"
                        width="">
                            <template  slot-scope="scope">
                                <div class="flex align-items">
                                    <img :src="tableData.child_info[scope.row.child_id].avatar" alt="" class="img42 img-circle"> 
                                    <div class="flex1 ml10">
                                        <div class="font14 color3">{{tableData.child_info[scope.row.child_id].name}}</div> 
                                        <div class="font12 color6">{{scope.row.class_title}}</div>
                                    </div>
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column
                        prop="name"
                        label="<?php echo Yii::t("schoolTeams", "Uniform NO."); ?>"
                        width="180">
                            <template  slot-scope="scope">
                                <el-input v-model='scope.row.uniform_number' size='small'  placeholder="<?php echo Yii::t("leave", "Input"); ?>"></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column
                            prop="address"
                            label="<?php echo Yii::t("schoolTeams", "Title"); ?>"
                            width="180">
                            <template  slot-scope="scope">
                                <el-select  v-model='scope.row.position' size='small' placeholder="<?php echo Yii::t("global", "Please Select"); ?>">
                                    <el-option
                                    v-for="(list,index) in configData.configList.position"
                                    :key="list.key"
                                    :label="list.value"
                                    :value="list.key+''">
                                    </el-option>
                                </el-select>
                            </template>
                        </el-table-column>
                        <el-table-column
                        prop="address"
                        label="<?php echo Yii::t("newDS", "Actions"); ?>"
                        width="100">
                            <template  slot-scope="scope">
                                <button type="button" class="btn btn-link" @click='delTeamStaff(scope.row.id)'><?php echo Yii::t("newDS", "Delete");?></button>
                            </template>
                        </el-table-column>
                    </el-table>
                </div> 
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Cancel");?></button>
                    <button type="button" class="btn btn-primary" :disabled='btnDisanled'  @click='confirmSatff()'><?php echo Yii::t("global", "OK");?></button>
                </div>
            </div>
        </div>
    </div>
    <!-- 选择学生 -->
    <div class="modal fade" id="addClassModal" tabindex="-1" role="dialog" data-backdrop="static"  >
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentCommentsModalLabel"><?php echo Yii::t("referral", "Student"); ?></h4>
                </div>
                <div class="modal-body p24 relative">
                    <span class='borderLeftpos'></span>
                    <div style='max-height:600px;' class='row'>
                        <div class='col-md-6 col-sm-6'>
                            <div>
                                <el-input
                                placeholder="<?php echo Yii::t("global", "Search"); ?>"
                                v-model='searchText' 
                                size='small'
                                clearable>
                                </el-input>
                            </div>
                            <div  class="tab-pane active mt15 scroll-box" id="class" v-if='searchText==""'  style='max-height:500px;overflow-y:auto'>
                                <div v-for='(list,index) in classList' class='relative mb16'>
                                    <p  @click='getChild(list)'>
                                        <span  class='font14 color606 cur-p'>{{list.title}} </span>
                                        <span class='el-icon-arrow-down ml5' v-if='classId!=list.classid'></span>
                                        <span class='el-icon-arrow-up ml5' v-else></span>
                                    </p>
                                    <p  class='allCheck' v-if='classId==list.classid'><button class="btn btn-default pull-right btn-xs" type="button" @click='selectAll(list,index)'  v-if='list.childData && list.childData.length!=0'><?php echo Yii::t("global", "Select All");?></button></p>
                                    <div  class='border scroll-box mr10 childList' v-if='classId==list.classid'>
                                        <div v-if='!childLoading'>
                                            <div class='' v-if='list.childData && list.childData.length!=0'>
                                                <div class="media mb10 listMedia" v-for='(item,idx) in list.childData'>
                                                    <div class="media-left pull-left media-middle">
                                                        <a href="javascript:void(0)">
                                                            <img :src="item.avatar" data-holder-rendered="true" class="media-object img-circle img42">
                                                        </a>
                                                    </div>
                                                    <div v-if='item.stuLoading'>
                                                        <div v-if='item.disabled' class="media-right pull-right text-muted lineHeight">
                                                            <span class='cur-p mt10 color9'><?php echo Yii::t("newDS", "Subscribed");?></span>
                                                        </div>
                                                        <div v-else class="media-right pull-right text-muted" @click='assignChildren(item,index,idx)'>
                                                            <span class='cur-p font16 bluebg mt15 el-icon-circle-plus-outline'></span>
                                                        </div>
                                                    </div>
                                                    <div class='childLoading' v-else>
                                                        <span></span>
                                                    </div>
                                                    <div class="media-body media-middle">
                                                        <h4 class="media-heading font14 lineHeight">{{item.name}}</h4>
                                                    </div>
                                                </div>
                                            </div>
                                            <div v-else>
                                                <div class='font12 text-muted text-center'><?php echo Yii::t("newDS", "no student in this class");?></div>
                                            </div>
                                        </div>
                                        <div class='loading' v-else>
                                            <span></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div v-else>
                                <div v-if='searchChildList.length!=0'  class='scroll-box'   style='max-height:500px;overflow-y:auto'>                               
                                    <div class="media mt10 listMedia" v-for='(item,idx) in searchChildList'>
                                        <div class="media-left pull-left media-middle">
                                            <a href="javascript:void(0)">
                                                <img :src="item.avatar" data-holder-rendered="true" class="media-object img-circle img42">
                                            </a>
                                        </div>
                                        <div v-if='item.disabled' class="media-right pull-right text-muted lineHeight">
                                            <span class='cur-p mt10'><?php echo Yii::t("newDS", "Subscribed");?></span>
                                        </div>
                                        <div v-else class="media-right pull-right text-muted" @click='assignChildren(item,idx,"search")'>
                                            <span class='cur-p font16 bluebg mt15 el-icon-circle-plus-outline'></span>
                                        </div>
                                        <div class="media-body media-middle">
                                            <h4 class="media-heading font14 color3 mt5">{{item.name}}</h4>
                                            <div class="text-muted color6">{{item.className}}</div>
                                        </div>
                                    </div>
                                </div>
                                <div v-else-if='searchText!=""'>
                                        <div class='font14 color6 text-center mt20'><?php echo Yii::t("ptc", "No Data"); ?></div>    
                                </div>
                            </div>
                        </div>
                        <div class='col-md-6 col-sm-6 borderLeft'>
                            <p class='mt10 font14 color606'>
                            <?php echo Yii::t("newDS", " ");?>{{childSelected.length}}<?php echo Yii::t("newDS", " student(s) selected");?>
                                <button class="btn btn-link pull-right btn-xs font14" v-if='childSelected.length!=0' type="button" @click='batchDel("modal")'><?php echo Yii::t("directMessage", "Clear All");?></button>
                            </p>
                            <div class='scroll-box p10 overflow-y' style='height:500px'>
                                <div class="media m0 listMedia" v-for='(list,index) in childSelected'>
                                    <div class="media-left pull-left media-middle">
                                        <a href="javascript:void(0)">
                                            <img :src="list.avatar" data-holder-rendered="true" class="media-object img-circle img42">
                                        </a>
                                    </div>
                                    <div class="media-right pull-right text-muted" @click='Unassign(list,index)'>
                                        <span class='closeChild cur-p mt15 font16 el-icon-circle-close'></span>
                                    </div>
                                    <div class="media-body media-middle">
                                        <h4 class="media-heading font14 color3 mt5">{{list.name}} 
                                           
                                        </h4>
                                        <div class="text-muted color6">{{list.className}}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class='clearfix'></div>
                    </div>
                </div> 
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Cancel"); ?></button>
                    <button type="button" class="btn btn-primary" @click='saveChild()' :disabled='btnDisanled'><?php echo Yii::t("global", "OK"); ?></button>
                </div>
            </div>
        </div>
    </div>
    <!-- 编辑 -->
    <div class="modal fade" id="editInfoModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel" data-backdrop="static" >
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel">{{detailData.url=='add'?"<?php echo Yii::t("schoolTeams", "New Team"); ?>":"<?php echo Yii::t("withdrawal", "Edit");?>"}}</h4>
                </div>
                <div class="modal-body p24 overflow-y scroll-box" :style="'max-height:'+(height-180)+'px;overflow-x: hidden;'">
                    <div class="form-horizontal" v-if='configData.configList'>
                        <div class="form-group">
                            <span class="col-sm-2 font14 color6"><?php echo Yii::t("curriculum", "Projects"); ?></span>
                            <div class="col-sm-10">
                                <el-select  v-model='detailData.category_id' size='small' placeholder="<?php echo Yii::t("global", "Please Select"); ?>">
                                    <el-option
                                    v-for="(list,index) in configData.categoryList"
                                    :key="list.id"
                                    :label="list.title"
                                    :value="list.id">
                                    </el-option>
                                </el-select>
                            </div>
                        </div>
                        <div class="form-group">
                            <span class="col-sm-2 font14 color6"><?php echo Yii::t("curriculum", "Cn Title"); ?></span>
                            <div class="col-sm-10">
                                <el-input v-model='detailData.title_cn' size='small'  placeholder="<?php echo Yii::t("leave", "Input"); ?>"></el-input>
                            </div>
                        </div>
                        <div class="form-group">
                            <span class="col-sm-2 font14 color6"><?php echo Yii::t("curriculum", "En Title"); ?></span>
                            <div class="col-sm-10">
                                <el-input v-model='detailData.title_en' size='small'  placeholder="<?php echo Yii::t("leave", "Input"); ?>"></el-input>
                            </div>
                        </div>
                        <div class="form-group">
                            <span class="col-sm-2 font14 color6"><?php echo Yii::t("schoolTeams", "Division"); ?></span>
                            <div class="col-sm-10">
                                <label class="radio-inline mr12" v-for='(list,index) in configData.configList.grade_group'>
                                    <input type="radio" v-model='detailData.grade_group' :value="list.key"> {{list.value}}
                                </label>
                            </div>
                        </div>
                        <div class="form-group">
                            <span class="col-sm-2 font14 color6"><?php echo Yii::t("newDS", "Type"); ?></span>
                            <div class="col-sm-10">
                                <label class="radio-inline mr12" v-for='(list,index) in configData.configList.team_type'>
                                    <input type="radio" v-model='detailData.type' :value="list.key"> {{list.value}}
                                </label>
                            </div>
                        </div>
                        <div class="form-group">
                            <span class="col-sm-2 font14 color6"><?php echo Yii::t("teaching", "Cover"); ?></span>
                            <div class="col-sm-10">
                                <div class='font14 color6 mb16 mt8'><span class='el-icon-info'></span> <?php echo Yii::t("schoolTeams", "Image with a 5:3 aspect ratio preferred."); ?></div>
                                <el-upload
                                    class="avatar-uploader"
                                    action="https://up-z0.qiniup.com"
                                    :show-file-list="false"
                                    :on-success="handleAvatarSuccess"
                                    :data="uploadToken"
                                    :before-upload="beforeAvatarUpload">
                                    <div v-if="imageUrl" class='relative'>
                                        <span class='errorClose' @click.stop='delImg()'>
                                            <span class='el-icon-close'></span>
                                        </span>
                                        <img  :src="imageUrl" class="avatar">
                                    </div>
                                    <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                                </el-upload>
                            </div>
                        </div>
                        <div class="form-group">
                            <span class="col-sm-2 font14 color6"><?php echo Yii::t("labels", "Status"); ?></span>
                            <div class="col-sm-10">
                                <label class="checkbox-inline">
                                <input type="checkbox" id="inlineCheckbox1" v-model="detailData.status"> <?php echo Yii::t("schoolTeams", "Open to parents"); ?>
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class='font14 color6 mb16 mt24'><?php echo Yii::t("event", "Introduction Cn"); ?></div>
                    <div class='relative'>
                        <el-popover
                            placement="top"
                            width="270"
                            class='uploadImg'
                            trigger="hover">
                                <div >
                                    <?php echo sprintf(Yii::t("newDS", "From Media Repository: "), $this->createUrl('/mteaching/weekly/index'));?>
                                    <br>
                                    <?php echo sprintf(Yii::t("newDS", "Click <a target='_blank'  href='%s'>here</a> to upload photos or videos, then insert files by clicking the gallery icon."), $this->createUrl('/mteaching/weekly/index'));?>
                                    <div class='mt16'>
                                        <?php echo Yii::t("newDS", "Direct Upload: ");?>
                                    </div>
                                    <?php echo Yii::t("newDS", "Just drag images to editor area (Images Only).");?>
                                </div>
                                <span slot="reference" class='yellow font14'><span class='el-icon-info mr8'></span> <?php echo Yii::t("newDS", "How to insert a photo or video?");?> </span>
                        </el-popover>
                        <textarea id="tinymceCn" class='tinymceText' v-model='detailData.intro_cn'></textarea>

                    </div>
                    <div class='font14 color6 mb16 mt24'><?php echo Yii::t("event", "Introduction En"); ?></div>
                    <div class='relative'>
                        <el-popover
                            placement="top"
                            width="270"
                            class='uploadImg'
                            trigger="hover">
                                <div >
                                    <?php echo sprintf(Yii::t("newDS", "From Media Repository: "), $this->createUrl('/mteaching/weekly/index'));?>
                                    <br>
                                    <?php echo sprintf(Yii::t("newDS", "Click <a target='_blank'  href='%s'>here</a> to upload photos or videos, then insert files by clicking the gallery icon."), $this->createUrl('/mteaching/weekly/index'));?>
                                    <div class='mt16'>
                                        <?php echo Yii::t("newDS", "Direct Upload: ");?>
                                    </div>
                                    <?php echo Yii::t("newDS", "Just drag images to editor area (Images Only).");?>
                                </div>
                                <span slot="reference" class='yellow font14'><span class='el-icon-info mr8'></span> <?php echo Yii::t("newDS", "How to insert a photo or video?");?> </span>
                        </el-popover>
                        <textarea id="tinymceEn" class='tinymceText' v-model='detailData.intro_en'></textarea>

                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Cancel"); ?></button>
                    <button type="button" class="btn btn-primary" :disabled='btnDisanled' @click='savelList()'><?php echo Yii::t("global", "OK"); ?></button>
                </div>
            </div>
        </div>
    </div>
    <!-- 删除 -->
    <div class="modal fade" id="delModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
        <div class="modal-dialog modal-sm" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t('newDS', 'Warning') ?></h4>
                </div>
                <div class="modal-body p24" >
                    <?php echo Yii::t('directMessage', 'Proceed to remove?') ?>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Cancel"); ?></button>
                    <button type="button" class="btn btn-primary" v-if='delType=="list"' :disabled='btnDisanled' @click='delTeam()'><?php echo Yii::t("global", "OK"); ?></button>
                    <button type="button" class="btn btn-primary" v-if='delType=="child"' :disabled='btnDisanled' @click='delTeamStaff()'><?php echo Yii::t("global", "OK"); ?></button>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    var height=document.documentElement.clientHeight;
    $(document).ready(function () {
        // 通过该方法来为每次弹出的模态框设置最新的zIndex值，从而使最新的modal显示在最前面
        $(document).on('show.bs.modal', '.modal', function (event) {
            var zIndex = 1040 + (10 * $('.modal:visible').length);
            $(this).css('z-index', zIndex);
            setTimeout(function() {
                $('.modal-backdrop').not('.modal-stack').css('z-index', zIndex - 1).addClass('modal-stack');
            }, 0);
        });
    });
    var container=new Vue({
        el: "#box",
        data: {
            height:height,
            activeSection:'',
            configData:{},
            startYear:'',
            listData:{},
            listDataCopy:{},
            grade_group:[],
            uploadToken:{},
            WatermarkImg:'<?php echo (isset($this->schoolType) && $this->schoolType == 'ds') ? '!v1000' : '!i1000'; ?>',
            imageUrl: '',
            domain:'',
            token:'',
            detailData:{},
            tableData:{},
            membersList:[],
            classList:[],
            searchText:'',
            childSelected:[],
            btnDisanled:false,
            childSelectedCopy:[],
            delId:'',
            delType:'',
            membersItem:{},
            tipVisibles:[],
            dataLoading:false,
            categoryListFilter:[],
            activeName:'list',
            sectionRefs:{}
        },
        created: function() {
            this.getInit()
        },
        beforeUnmount() {
            this.$refs.content.removeEventListener('scroll', this.handleScroll);
        },
        watch:{
            searchText(old,newValue){
                if(old!=''){
                    this.searchChild()
                }
            },
        },
        methods: {
            popoverHide(index){
                let that=this
                Vue.set(that.tipVisibles, index, false);
                $('.wechatQrcode').html('')
                $.ajax({
                    url: '<?php echo $this->createUrl("api") ?>',
                    type: "post",
                    dataType: 'json',
                    data:{
                        url:'previewUrl',
                        teamId:index,
                        startYear:this.startYear
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.$nextTick(() => {
                                $('.wechatQrcode').qrcode({
                                    width:120,
                                    height:120,
                                    text:data.data,
                                });
                            })
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            scrollList(id){
                this.activeSection='list'+id
                var targetElement = document.querySelector('#list'+id);
                window.location.hash = '#list'+id
            },
            setSectionRef(el) {
                if (el) {
                    this.sectionRefs[el.id] = el;
                }
            },
            isActiveSection(id) {
                return this.activeSection === 'list'+id;
            },
            handleScroll() {
                const contentRect = this.$refs.content.getBoundingClientRect();
                for (let key in this.sectionRefs) {
                    const element = this.sectionRefs[key];
                    const rect = element.getBoundingClientRect();
                    if (rect.top <= contentRect.height * 0.4 && rect.bottom >= contentRect.height * 0.6) {
                        this.activeSection = key;
                        break;
                    }
                }
            },
            getInit(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("api") ?>',
                    type: "post",
                    dataType: 'json',
                    data:{
                        url:'indexData',
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                          that.configData=data.data
                          let year=data.data.startYearList.filter((i) => i.current ==1)
                          that.startYear=year[0].key
                          that.configData.configList.grade_group.forEach(item => {
                            that.grade_group.push(item.key)
                          });
                          that.getList()
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            getTitle(id,list,key,value){
                let item=list.filter((i) => i[key] ==id)
                return item[0][value]
            },
            filterGrade(){
                filteredData={}
                for (let key in this.listDataCopy) {
                    const filteredItems = this.listDataCopy[key].filter(item => this.grade_group.includes(item.grade_group));
                    console.log(filteredItems)
                    if (filteredItems.length != 0) {
                        filteredData[key] = filteredItems;
                    }
                }
                console.log(filteredData)
                this.listData=filteredData
            },
            getList(){
                let that=this
                that.dataLoading=false
                $.ajax({
                    url: '<?php echo $this->createUrl("api") ?>',
                    type: "post",
                    dataType: 'json',
                    data:{
                        url:'list',
                        startYear:this.startYear
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.categoryListFilter=data.data.categoryDataList
                            that.listData=data.data.teamList
                            that.dataLoading=true
                            that.listDataCopy= JSON.parse(JSON.stringify(data.data.teamList));
                            if(Object.keys( data.data.teamList).length!=0){
                                that.activeSection ='list'+Object.keys( data.data.teamList)[0];
                            }
                            that.$nextTick(()=>{
                                if(that.$refs.content){
                                    that.$refs.content.addEventListener('scroll', that.handleScroll);
                                }
                            })
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            addTeam(key){
                this.imageUrl=''
                this.detailData={
                    category_id:key?parseInt(key):'',
                    grade_group:'',
                    intro_cn:'',
                    intro_en:'',
                    poster:'',
                    title_cn:'',
                    title_en:'',
                    type:'',
                    status:false,
                    url:'add',
                    startYear:this.startYear
                }
                tinymce.remove('#tinymceCn');
                tinymce.remove('#tinymceEn');
                if(this.configData.configList.grade_group.length==1){
                    this.detailData.grade_group=this.configData.configList.grade_group[0].key
                }
                $("#editInfoModal").modal('show')
                this.$nextTick(() => {
                    newTinymce()
                    this.getQiniu()
                })
            },
            editDetail(item){
                let that=this
                tinymce.remove('#tinymceCn');
                tinymce.remove('#tinymceEn');
                $.ajax({
                    url: '<?php echo $this->createUrl("api") ?>',
                    type: "post",
                    dataType: 'json',
                    data:{
                        url:'detail',
                        startYear:this.startYear,
                        id:item.id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.detailData={
                                id:data.data.id,
                                category_id:data.data.category_id,
                                grade_group:data.data.grade_group,
                                intro_cn:data.data.intro_cn,
                                intro_en:data.data.intro_en,
                                poster:data.data.poster,
                                title_cn:data.data.title_cn,
                                title_en:data.data.title_en,
                                type:data.data.type,
                                status:data.data.status==1?true:false,
                                url:'update',
                                startYear:that.startYear
                            }
                            that.imageUrl=data.data.poster_url
                            $("#editInfoModal").modal('show')
                            that.$nextTick(() => {
                                newTinymce()
                                that.getQiniu()
                            })
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
               
            },
            getQiniu(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("api") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        url: 'token',
                        startYear:this.startYear
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.token = data.data.token;
                            that.domain = data.data.domain;
                            that.uploadToken = {'token':data.data.token};
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            savelList(){
                this.detailData.intro_cn= tinymce.get('tinymceCn').getContent()
                this.detailData.intro_en= tinymce.get('tinymceEn').getContent()
                this.detailData.status= this.detailData.status?1:2
                if(this.detailData.category_id==''){
                    resultTip({
                        error: 'warning',
                        msg:'<?php echo Yii::t("global", "Please Select"); ?> <?php echo Yii::t("curriculum", "Projects"); ?>'
                    });
                    return
                }
                if(this.detailData.title_cn==''){
                    resultTip({
                        error: 'warning',
                        msg:'<?php echo Yii::t("leave", "Input"); ?> <?php echo Yii::t("curriculum", "Cn Title"); ?>'
                    });
                    return
                }
                if(this.detailData.title_en==''){
                    resultTip({
                        error: 'warning',
                        msg:'<?php echo Yii::t("leave", "Input"); ?> <?php echo Yii::t("curriculum", "En Title"); ?>'
                    });
                    return
                }
                if(this.detailData.grade_group==''){
                    resultTip({
                        error: 'warning',
                        msg:'<?php echo Yii::t("global", "Please Select"); ?> <?php echo Yii::t("schoolTeams", "Division"); ?>'
                    });
                    return
                }
                if(this.detailData.type==''){
                    resultTip({
                        error: 'warning',
                        msg:'<?php echo Yii::t("global", "Please Select"); ?> <?php echo Yii::t("newDS", "Type"); ?>'
                    });
                    return
                }
                if(this.detailData.intro_cn==''){
                    resultTip({
                        error: 'warning',
                        msg:'<?php echo Yii::t("leave", "Input"); ?> <?php echo Yii::t("event", "Introduction Cn"); ?>'
                    });
                    return
                }
                if(this.imageUrl==''){
                    this.detailData.poster=''
                }
                let that=this
                this.btnDisanled=true
                $.ajax({
                    url: '<?php echo $this->createUrl("api") ?>',
                    type: "post",
                    dataType: 'json',
                    data:this.detailData,
                    success: function(data) {
                        if (data.state == 'success') {
                            $("#editInfoModal").modal('hide')
                            that.getList()
                            resultTip({
                                msg: data.message
                            });
                            that.btnDisanled=false
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            that.btnDisanled=false
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                        that.btnDisanled=false
                    },
                })
            },
            handleAvatarSuccess(res, file) {
                this.detailData.poster=res.name
                this.imageUrl=this.domain+ "/" +res.name
            },
            beforeAvatarUpload(file) {
                let fileType=file.type.split('/')
                const isJPG = true;
                const isLt2M = file.size / 1024 / 1024 < 2;
                if(fileType[0]=="image"){
                    if (!isLt2M) {
                        resultTip({
                            error: 'warning',
                            msg:'上传图片大小不能超过 2MB!'
                        });
                    }
                    return isJPG && isLt2M;
                }
            },
            delImg(){
                this.imageUrl=''
            },
            delTeam(item){
                if(item){
                    this.delId=item.id
                    this.delType='list'
                    $("#delModal").modal('show')
                    return
                }
                let that=this
                that.btnDisanled=true
                $.ajax({
                    url: '<?php echo $this->createUrl("api") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        url:'delete',
                        id:this.delId
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.getList()
                            resultTip({
                                msg: data.message
                            });
                            $("#delModal").modal('hide')
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                        that.btnDisanled=false
                    },
                    error: function(data) {
                        that.btnDisanled=false
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            editAdmin(item,type){
                let that=this
                this.membersItem=item
                $.ajax({
                    url: '<?php echo $this->createUrl("api") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        url:'members',
                        teamId:item.id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.tableData=data.data
                            that.childSelectedCopy=[]
                            data.data.members.forEach(item => {
                                that.childSelectedCopy.push(item.child_id)
                            });
                            that.membersList=data.data.members
                            if(!type){
                                $("#editStaffModal").modal('show')
                            }
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                        that.btnDisanled=false
                    },
                    error: function(data) {
                        that.btnDisanled=false
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })                
            },
            addStaff(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("/mteaching/journals/classList") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            data.data.forEach(item => {
                                item.childData=[]
                            })
                            that.classList=data.data
                            that.searchChildList=[]
                            that.childSelected=[]
                            that.searchText=''
                            that.classId=''
                            $('#addClassModal').modal('show')
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            getChild(list){
                let that=this
                if(that.classId==list.classid){
                    that.classId=''
                    return
                }
                that.classId=list.classid
                var childId=[]
                if(this.childSelected!=null){
                    for(var i=0;i<this.childSelected.length;i++){
                        childId.push(this.childSelected[i].id)
                    }
                }
                for(var i=0;i<this.childSelectedCopy.length;i++){
                    childId.push(this.childSelectedCopy[i])
                }
                for(var i=0;i<that.classList.length;i++){
                    if(that.classList[i].classid==list.classid){
                        if(that.classList[i].childData.length!=0){
                            for(var j=0;j<that.classList[i].childData.length;j++){
                                if (childId.indexOf(that.classList[i].childData[j].id)!=-1) {
                                    that.classList[i].childData[j].disabled=true
                                }else{
                                    that.classList[i].childData[j].disabled=false
                                }
                            }
                            that.$forceUpdate()
                            return
                        }
                    }
                }
                that.childLoading=true
                $.ajax({
                    url: '<?php echo $this->createUrl("/mteaching/student/childList") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        classId:list.classid
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            for(var i=0;i<data.data.length;i++){
                                data.data[i].classTitle=list.title
                                data.data[i].stuLoading=true
                                data.data[i].loading=true
                                if (childId.indexOf(data.data[i].id)!=-1) {
                                    data.data[i].disabled=true
                                }else{
                                    data.data[i].disabled=false
                                }
                            }
                            that.sortData(data.data)
                            for(var i=0;i<that.classList.length;i++){
                                if(that.classList[i].classid==list.classid){
                                    that.classList[i].childData=data.data
                                }
                            }
                            that.childLoading=false
                            that.$forceUpdate()
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            searchChild(){
                var childId=[]
                if(this.childSelected!=null){
                    for(var i=0;i<this.childSelected.length;i++){
                        childId.push(this.childSelected[i].id+'')
                    }
                }
                for(var i=0;i<this.childSelectedCopy.length;i++){
                    childId.push(this.childSelectedCopy[i])
                }
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("/mteaching/student/studentSearch") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        child_name:this.searchText
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            for(var i=0;i<data.data.length;i++){
                                data.data[i].loading=true
                                if (childId.indexOf(data.data[i].id+'')!=-1) {
                                    data.data[i].disabled=true
                                }else{
                                    data.data[i].disabled=false
                                }
                            }
                            that.searchChildList=data.data
                            that.$forceUpdate()
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            sortData(list){
                <?php if (Yii::app()->language == 'zh_cn') { ?>
                    list.sort((a, b)=> a.name.localeCompare(b.name, 'zh'));
                <?php } else { ?>
                    list.sort((a, b) => a.name.charCodeAt(0) - b.name.charCodeAt(0));
                <?php } ?>
            },
            selectAll(list,index){
                for(var i=0;i<list.childData.length;i++){
                    if (!list.childData[i].disabled) {
                        this.childSelected.push(list.childData[i])
                        Vue.set(this.classList[index].childData[i], 'disabled', true);
                    }
                }
                this.$forceUpdate()
            },
            assignChildren(list,index,idx){
                if(idx=='search'){
                    this.searchChildList[index].disabled=true
                }else{
                    Vue.set(this.classList[index].childData[idx], 'disabled', true);
                }
                this.$forceUpdate()
                this.childSelected.push(list)
            },
            Unassign(data,index,type){
                for(var i=0;i<this.classList.length;i++){
                    for(var j=0;j<this.classList[i].childData.length;j++){
                        if(data.id==this.classList[i].childData[j].id){
                            Vue.set(this.classList[i].childData[j], 'disabled', false);
                        }
                    }
                }
                for(var i=0;i<this.searchChildList.length;i++){
                    if(data.id==this.searchChildList[i].id){
                        Vue.set(this.searchChildList[i], 'disabled', false);
                    }
                }
                this.$forceUpdate()
                this.childSelected.splice(index,1)
            },
            batchDel(){
                this.childSelected=[]
                for(var i=0;i<this.classList.length;i++){
                    for(var j=0;j<this.classList[i].childData.length;j++){
                        this.classList[i].childData[j].disabled=false
                    }                        
                }                
                this.$forceUpdate() 
            },
            saveChild(){
                let that=this
                if(this.childSelected.length==0){
                    resultTip({
                        error: 'warning',
                        msg: '请选择学生'
                    });
                    return
                }
                let childIds=[]
                for(var i=0;i<this.childSelected.length;i++){
                    childIds.push(this.childSelected[i].id)
                }
                this.btnDisanled=true
                $.ajax({
                    url: '<?php echo $this->createUrl("api") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        url:'addMember',
                        teamId:this.membersItem.id,
                        childIds:childIds,
                        startYear:that.startYear
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.tableData=data.data
                            that.membersList=data.data.members
                            for (let key in that.listData) {
                                for (let item of that.listData[key]) {
                                    if (item.id === that.membersItem.id) {
                                        item.member_num =data.data.members.length;
                                        break;  
                                    }
                                }
                            }
                            $("#addClassModal").modal('hide')
                            that.btnDisanled=false
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            that.btnDisanled=false
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                        that.btnDisanled=false
                    },
                })             
            },
            confirmSatff(){
                let that=this
                let data=[]
                for(var i=0;i<this.tableData.members.length;i++){
                    data.push({
                        "id": this.tableData.members[i].id,
                        "child_id": this.tableData.members[i].child_id,
                        "class_id": this.tableData.members[i].class_id,
                        "uniform_number":  this.tableData.members[i].uniform_number,
                        "position":  this.tableData.members[i].position
                    })
                }
                that.btnDisanled=true
                $.ajax({
                    url: '<?php echo $this->createUrl("api") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        url:'updateMember',
                        teamId:this.membersItem.id,
                        startYear:that.startYear,
                        data:data
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            $("#editStaffModal").modal('hide')
                            that.btnDisanled=false
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            that.btnDisanled=false
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                        that.btnDisanled=false
                    },
                })            
            },
            delTeamStaff(item){
                if(item){
                    this.delId=item
                    this.delType='child'
                    $("#delModal").modal('show')
                    return
                }
                let that=this
                that.btnDisanled=true
                $.ajax({
                    url: '<?php echo $this->createUrl("api") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        startYear:that.startYear,
                        url:'deleteMember',
                        memberId:this.delId
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.editAdmin(that.membersItem,'init')
                            resultTip({
                                msg: data.message
                            });
                            for (let key in that.listData) {
                                for (let item of that.listData[key]) {
                                    if (item.id === that.membersItem.id) {
                                        item.member_num =item.member_num-1;
                                        break;  
                                    }
                                }
                            }
                            $("#delModal").modal('hide')
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                        that.btnDisanled=false
                    },
                    error: function(data) {
                        that.btnDisanled=false
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            handleClick(){
                if(this.activeName=='home'){
                    window.location.href="<?php echo $this->createUrl('info', array('branchId' => $this->branchId)); ?>"
                }
            }
        }
    })
    function newTinymce(){
        const example_image_upload_handler = (blobInfo, progress) => new Promise((resolve, reject) => {
            const xhr = new XMLHttpRequest();
            xhr.withCredentials = false;
            xhr.open("post", "https://up-z0.qiniup.com");
            xhr.upload.onprogress = (e) => {
                progress(e.loaded / e.total * 100);
            };

            xhr.onload = () => {
                if (xhr.status === 403) {
                reject({ message: 'HTTP Error: ' + xhr.status, remove: true });
                return;
                }

                if (xhr.status < 200 || xhr.status >= 300) {
                reject('HTTP Error: ' + xhr.status);
                return;
                }

                const json = JSON.parse(xhr.responseText);
            
                resolve( container.domain + "/" + json.name);
            };

            xhr.onerror = () => {
                reject('Image upload failed due to a XHR Transport error. Code: ' + xhr.status);
            };

            var formData = new FormData();
            var file = blobInfo.blob();
            formData.append('file', file, file.name);
            formData.append('token', container.token);
            xhr.send(formData);
        });
        tinymce.PluginManager.add('my-example-plugin', function (editor) {
            editor.ui.registry.addMenuItem('image', {
                icon: 'image',
                text: '<?php echo Yii::t("newDS", "Add/Remove Watermark");?>',
                onAction: function () {
                    editor.insertContent('<div><img  style="max-width:100%"  src='+container.Watermark+'></div>')
                }
            });
            editor.ui.registry.addContextMenu('image', {
                update: function (element) {
                    container.Watermark=element.src
                    return !container.Watermark ? '' : 'image';
                }
            });
        });
        tinymce.init({
            selector: 'textarea.tinymceText',
            width: "100%",
            content_style: "img{max-width:100%}",
            height:800,
            resize: false,
            language:'<?php echo Yii::app()->language;?>'=='zh_cn'?'zh_CN':'en',
            plugins: [
                'fullscreen', 'image', 'link', 'media', 'preview', 'table', 'code', 'my-example-plugin', 'lists', 'advlist','my-example-plugin',
            ],
            contextmenu: ['image'],
            noneditable_class: 'non-editable',
            toolbar:'mediaDialog shareDialog bullist numlist',
            images_upload_handler:example_image_upload_handler,
            setup: function(editor) {
                // 实时同步编辑器内容到 selector
                editor.on('change', function() {
                    tinymce.triggerSave();
                });
                editor.ui.registry.addIcon('mediaDialog', '<svg width="24" height="24"><path fill-rule="nonzero" d="M5 15.7l2.3-2.2c.3-.3.7-.3 1 0L11 16l5.1-5c.3-.4.8-.4 1 0l2 1.9V8H5v7.7zM5 18V19h3l1.8-1.9-2-2L5 17.9zm14-3l-2.5-2.4-6.4 6.5H19v-4zM4 6h16c.6 0 1 .4 1 1v13c0 .6-.4 1-1 1H4a1 1 0 0 1-1-1V7c0-.6.4-1 1-1zm6 7a2 2 0 1 1 0-4 2 2 0 0 1 0 4zM4.5 4h15a.5.5 0 1 1 0 1h-15a.5.5 0 0 1 0-1zm2-2h11a.5.5 0 1 1 0 1h-11a.5.5 0 0 1 0-1z"></path></svg>');
                editor.ui.registry.addButton('mediaDialog', {
                    icon:'mediaDialog',
                    tooltip: '<?php echo Yii::t("newDS", "Media Gallery");?>',
                    onAction: function () {
                        container.tinymceList=[]
                        var instanceApi = editor.windowManager.openUrl({
                            onMessage: function (dialogApi, details) {
                                switch (details.mceAction) {
                                    case 'loading':
                                        dialogApi.unblock()
                                        break;
                                    case 'addItem':
                                        container.tinymceList.push(details.content)
                                        break;
                                    case 'removeItem':
                                        for(var i=0;i<container.tinymceList.length;i++){
                                            if(container.tinymceList[i].id==details.content.id){
                                                container.tinymceList.splice(i,1)
                                            }
                                        }
                                    break;
                                }
                            },
                            onAction: function (dialogApi, details) {
                                dialogApi.close()
                                for(var i=0;i<container.tinymceList.length;i++){
                                    if(container.tinymceList[i].type=='photo'){
                                        editor.insertContent('<div><img style="max-width:100%" src='+container.tinymceList[i]._url+'></div>')
                                    }else{
                                        let url=container.tinymceList[i].url.split('!vh120')[0]
                                        editor.insertContent(
                                        '<p><video controls  style="max-width:100%;object-fit:cover"  width="600" height="auto" poster='+url+' >'+
                                            '<source src='+container.tinymceList[i]._url+'  type="video/mp4">'+
                                        '</video></p>')
                                    }
                                }
                            },
                            title: '<?php echo Yii::t("newDS", "Media Gallery"); ?>',
                            url: '<?php echo $this->createUrl('/mteaching/journals/media'); ?>',
                            height: 500,
                            width: 730,
                            buttons: [{
                                type:'custom',
                                text:'Insert',
                                name:'btn-insert',
                                primary: true,
                                align: 'end'
                            },
                                {
                                type:'cancel',
                                text:'Close',
                                name:'btn-close',
                                primary: false,
                                align: 'end'
                            }],

                        });
                        instanceApi.block('loading')
                    }
                })
            },
        })
    }
</script>
<?php
    $this->renderPartial('//layouts/common/branchSelectBottom');
?>
<style> 
    .scroll-box::-webkit-scrollbar-thumb {
        /*滚动条里面小方块*/
        border-radius   : 10px;
        background-color: skyblue;
        background-color: #D8D8D8;
        background-image: none
    }
    .scroll-box::-webkit-scrollbar-track {
        /*滚动条里面轨道*/
        box-shadow   : inset 0 0 5px rgba(0, 0, 0, 0);
        background   : #fff;
        border-radius: 10px;
    }
    .filterList{
        padding: 1px 10px;
        color: #333333;
        display: flex;
        border-left: 4px solid #f2f3f5;
        margin: 20px 0;
        height: 20px;
        line-height: 20px;
        align-items: center
    }
    .filterList .flex1{
        font-size:14px;
    }
    .filterList:hover{
        color: #4D88D2;
        cursor: pointer;
    }
    .filterList.active{
        color: #4D88D2;
        border-left: 4px solid #4D88D2;
    }
    .detailList{
        padding:16px;
        border-radius: 4px;
        border: 1px solid #E5E6EB;
        margin-top:20px
    }
    .tagLabel{
        font-size: 12px;
        color: #333333;
        line-height: 12px;
        background: #F2F3F5;
        border-radius: 2px;
        padding:2px 4px;
        margin-right:6px
    }
    .redBg{
        color: #D9534F;
        background: #FCF1F1;
    }
    .blueBg{
        color: #4D88D2;
        background: #F0F5FB;
    }
    .yellowBg{
        color: #F0AD4E;
        background: #FDF8F1;
    }
    .yellow{
        color: #F0AD4E;
    }
    .tox .tox-tbtn{
        overflow: inherit !important;
    }
    .tox .borderInsert{
        max-width:100px !important;
    }
    .tox-editor-container .tox-promotion,.tox-statusbar .tox-statusbar__branding ,.tox .tox-toolbar-nav-js{
    display: none !important;
    }
    .uploadImg{
        position: absolute;
        right: 10px;
        z-index: 998;
        top: 15px;
    }
    .tox-tinymce{
        height:500px !important
    }
    .avatar-uploader .el-upload {
    border: 1px dashed #4D88D2;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
  }
  .blueColor{
    color: #4D88D2;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #4D88D2;
    width:250px;
    height: 150px;
    line-height: 150px;
    text-align: center;
  }
  .errorClose{
    position: absolute;
    right: 10px;
    top: 5px;
    font-size: 15px;
    color: #fff;
    border: 1px solid #fff;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: rgba(0, 0, 0, 0.9);
    line-height: 20px;
  }
  .avatar {
    width: 250px;
    height: 150px;
    display: block;
    /* object-fit: contain; */
  }
  .el-upload__input{
    display:none !important
  }
  .img42{
    width: 42px;
    height: 42px;
    object-fit: cover;
    border-radius: 50%;
  }
  .listMedia{
    padding:8px;
    border: 1px solid #fff;
    margin:0
    }
    .listMedia:hover{
        background: rgba(77,136,210,0.1);
        border-radius: 4px;
        border: 1px solid #428bca;
        cursor: pointer;
    }
    .lineHeight {
        line-height:42px;
    }
    .childList {
        max-height: 250px;
        min-height: 40px;
        padding: 14px;
        overflow-y: auto;
    }
    .border {
        border: 1px solid #E8EAED;
        border-radius: 4px;
    }
    .allCheck {
        position: absolute;
        right: 10px;
        top: 4px;
    }
    .borderLeft{
        border-left: 1px solid #EBEDF0;
    }
    #wechatQrcode{
        width: 120px;
        height: 120px;
        margin: 0 auto;
    }
    .p12{
        padding:12px
    }
    span.col-sm-2 {
        padding-top:7px
    }
    .el-tabs__item.is-active{
        color:#4D88D2
    }
    .el-tabs__active-bar{
        background-color:#4D88D2
    }
    .el-tabs__item:hover{
        color:#4D88D2
    }
    .el-tabs__header{
        margin:0
    }
    .blueColor:hover{
        color: #044985
    }
</style>