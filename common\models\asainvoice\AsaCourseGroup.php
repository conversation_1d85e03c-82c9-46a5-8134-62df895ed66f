<?php

/**
 * This is the model class for table "ivy_asa_course_group".
 *
 * The followings are the available columns in table 'ivy_asa_course_group':
 * @property integer $id
 * @property integer $startyear
 * @property string $title_cn
 * @property string $title_en
 * @property integer $open_date_start
 * @property integer $open_date_end
 * @property string $schoolid
 * @property integer $status
 * @property integer $buy_from
 * @property integer $buy_end
 * @property integer $show_from
 * @property integer $show_end
 * @property integer $updated
 * @property integer $updated_userid
 * @property integer $program_type
 * @property integer $progress
 * @property integer $follower
 * @property integer $season
 */
class AsaCourseGroup extends CActiveRecord
{
    public $refundTime;
    public $refundStatus;
	const STATUS_ACTIVE = 1;
	const STATUS_FAIL = 0;

	const PROGRAM_ACTIVE = 1;
	const PROGRAM_WEEKEND = 2;
	const PROGRAM_DELAYCARE = 10;
	const PROGRAM_CAMP = 20;



	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_asa_course_group';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('program_type, title_cn, title_en, open_date_start, open_date_end, status, buy_from, buy_end, show_from, show_end, updated, updated_userid, startyear, season', 'required'),
			array('open_date_start, progress, follower, open_date_end, status, buy_from, buy_end, show_from, show_end, updated, updated_userid, datecheck, startyear, season', 'numerical', 'integerOnly'=>true),
			array('title_cn, title_en, schoolid', 'length', 'max'=>255),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, title_cn, program_type, progress, follower, title_en, open_date_start, open_date_end, schoolid, status, buy_from, buy_end, show_from, show_end, updated, updated_userid, datecheck, startyear', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
			'course' => array(self::HAS_MANY, 'AsaCourse', array('gid'=>'id'), 'order' => 'weight asc'),
			'sharegroup' => array(self::HAS_MANY, 'AsaCourseGroupShare', array('group_id'=>'id')),
			'discount' => array(self::HAS_MANY, 'AsaDiscount', array('group_id'=>'id')),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'title_cn' => Yii::t('asainvoice', 'Title Cn'),
			'title_en' => Yii::t('asainvoice', 'Title En'),
			'open_date_start' => Yii::t('asainvoice', 'Start Date'),
			'open_date_end' => Yii::t('asainvoice', 'End Date'),
			'schoolid' => 'Schoolid',
			'status' => Yii::t('asainvoice', 'Status'),
			'buy_from' => Yii::t('asainvoice', '购买开始时间'),
			'buy_end' => Yii::t('asainvoice', '购买结束时间'),
			'show_from' => Yii::t('asainvoice', '展示开始时间'),
			'show_end' => Yii::t('asainvoice', '展示结束时间'),
			'updated' => Yii::t('asainvoice', 'Updated'),
			'updated_userid' => Yii::t('asainvoice', 'Updated Userid'),
			'program_type' => Yii::t('asainvoice', '课程组类型'),
			'progress' => Yii::t('asainvoice', '显示购买进度'),
			'follower' => Yii::t('asainvoice', '开启心愿单'),
			'datecheck' => Yii::t('asainvoice', '开启时间检查'),
			'startyear' => Yii::t('asainvoice', '课程组开始年'),
			'season' => Yii::t('asainvoice', '课程组开设季'),
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('title_cn',$this->title_cn,true);
		$criteria->compare('title_en',$this->title_en,true);
		$criteria->compare('open_date_start',$this->open_date_start);
		$criteria->compare('open_date_end',$this->open_date_end);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('status',$this->status);
		$criteria->compare('buy_from',$this->buy_from);
		$criteria->compare('buy_end',$this->buy_end);
		$criteria->compare('show_from',$this->show_from);
		$criteria->compare('show_end',$this->show_end);
		$criteria->compare('updated',$this->updated);
		$criteria->compare('updated_userid',$this->updated_userid);
		$criteria->compare('program_type',$this->program_type);
		$criteria->compare('progress',$this->progress);
		$criteria->compare('follower',$this->follower);
		$criteria->compare('datecheck',$this->datecheck);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return AsaCourseGroup the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	static public function getGroup($schoolid, $time = false, $show = false, $status = AsaCourseGroup::STATUS_ACTIVE )
	{
		$criteria = new CDbCriteria;
		if ($status) {
			$criteria->compare('status', $status);
		}
		$criteria->compare('schoolid', $schoolid);
		$criteria->order = 'open_date_start DESC, id ASC';
		if ($time) {
			$criteria->compare('open_date_start', '<=' . $time);
			$criteria->compare('open_date_end', '>=' . $time);
		}

		if ($show) {
			$criteria->compare('show_from', '<=' . $show);
			$criteria->compare('show_end', '>=' . $show);
		}

		return AsaCourseGroup::model()->findAll($criteria);
	}

	public function getName()
	{
		switch (Yii::app()->language) {
			case "zh_cn":
				return  !empty( $this->title_cn ) ?  $this->title_cn : $this->title_en ;
				break;
			case "en_us":
				return  !empty( $this->title_en ) ? $this->title_en : $this->title_cn;
				break;
		}
	}

	public function getShareGroup($schoolid)
	{
		$criteria = new CDbCriteria;
		$criteria->compare('t.status', AsaCourseGroup::STATUS_ACTIVE);
		$criteria->compare('sharegroup.site_id', $schoolid);
		$criteria->with = 'sharegroup';
		$group = AsaCourseGroup::model()->findAll($criteria);
		return $group;
	}

	static public function getRefund(){
         return array(
             0 => "不可退费",
             1 => "第一节课之前",
             2 => "第二节课之前",
             3 => "第三节课之前"
         );
    }

	// 特殊处理课后课学费，限制退费
	public static function getNoRefundGroupIds()
	{
		return array(429, 441, 405, 426, 475, 476, 422, 406, 484, 473, 588, 596, 598, 640);
	}
}
