<?php

class ReportingLineController extends BranchBasedController
{
    public static $type;

    public function init()
    {
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";
        $this->modernMenuFlag = 'campusOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site', 'Campus Workspace');
        $this->branchSelectParams['hideOffice'] = false;
        $this->branchSelectParams['urlArray'] = array('//mcampus/reportingLine/index', 'type' => 'leave');
        $hideSchool = array('BJ_SS','BJ_BU');//只保留HQ
        foreach ($this->accessBranch as $key => $item) {
            if(in_array($item,$hideSchool)){
                unset($this->accessBranch[$key]);
            }
        }
        $this->accessBranch = array_values($this->accessBranch);
        //初始化选择校园页面
        $cs = Yii::app()->clientScript;
        $cs->registerCoreScript('jquery.ui');
        $cs->registerCssFile($cs->getCoreScriptUrl().'/jui/css/base/jquery-ui.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/vue-tree/vue.min.js');
        $cs->registerCssFile(Yii::app()->themeManager->baseUrl.'/base/js/element/index.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/element/index.js');
        $cs->registerCssFile(Yii::app()->themeManager->baseUrl.'/base/js/vue-tree/style.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/vue-tree/index.js');

        self::$type = Yii::app()->request->getParam('type', '');
        if (empty(self::$type)) {
            self::$type = 'leave';
        }
    }

    public function createUrl($route, $params = array(), $ampersand = '&')
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        $params['type'] = self::$type;
        return parent::createUrl($route, $params, $ampersand);
    }
    public function getOptions(){
        return array(
            'reportingLine' => array(
                array(
                    'id'      => 1,
                    'name_cn' => '请假汇报关系',
                    'name_en' => 'Leave approval',
                    'name'    => CommonUtils::autoLang('请假汇报关系', 'Leave approval'),
                    'type'    => 'leave'
                ),
//                array(
//                    'id'      => 2,
//                    'name_cn' => '报销审批',
//                    'name_en' => 'Reimbursement approval',
//                    'name'    => CommonUtils::autoLang('报销审批', 'Reimbursement approval'),
//                    'type'    => 'reimburse'
//                ),
            ),
            'type' => self::$type,
        );
    }

    public function checkRole() {
        $roleList = array('ivystaff_it', 'ivystaff_hr');
        foreach ($roleList as $role) {
            if (Yii::app()->user->checkAccess($role)) {
                return true;
                break;
            }
        }
        return false;
    }

    public function actionIndex()
    {
        $data = $this->getOptions();
        $this->render('index', $data);
    }

    public function actionApprovalGroup()
    {
        $this->branchSelectParams['urlArray'] = array('//mcampus/reportingLine/approvalGroup', 'type' => 'leave');
        $data = $this->getOptions();
        $this->render('approvalGroup',$data);
    }

    //汇报组首页的关系数据
    public function actionGroupIndex()
    {
        $requestUrl = "reportingLine/index/".self::$type."/".$this->branchId;
        $this->remote($requestUrl, 'get');
    }

    public function actionDelAllLine()
    {
        //权限验证
        if (!$this->checkRole()) {
            //返回json格式的错误
            $this->returnFailMessage('You do not have permission to perform this action.');
        }
        $requestUrl = "reportingLine/tier/".self::$type.'/'.$this->branchId;
        $this->remote($requestUrl, 'delete');
    }

    //未分配关系的员工
    public function actionUngroupedDetail()
    {
        $school_id = Yii::app()->request->getParam('branchId', '');
        $requestUrl = "reportingLine/ungrouped/".self::$type."/".$this->branchId;
        $this->remote($requestUrl, 'get');
    }

    //员工的上下级关系
    public function actionTierLine()
    {
        $staff_id = Yii::app()->request->getParam('staff_id', '');
        $requestUrl = "reportingLine/tier/".self::$type."/".$staff_id;
        $this->remote($requestUrl, 'get',array(
            'line_school_id' => $this->branchId,
        ));
    }

    //删除汇报关系
    public function actionDelLine()
    {
        //权限验证
        if (!$this->checkRole()) {
            $this->returnFailMessage('You do not have permission to perform this action.');
        }
        $staff_ids = Yii::app()->request->getParam('staff_ids', '');
        $requestUrl = "reportingLine/tier/".self::$type;
        $data = array(
            'staff_ids' => $staff_ids,
            'school_id' => $this->branchId
        );
        $this->remote($requestUrl, 'delete', $data);
    }

    public function actionSearchFirst()
    {
        $staff_name = Yii::app()->request->getParam('staff_name', '');
        $requestUrl = 'reportingLine/searchFirst/'.self::$type;
        $data = array(
            'staff_name' => $staff_name,
            'school_id'  => $this->branchId
        );
        $this->remote($requestUrl, 'get', $data);
    }

    //转移或者添加汇报关系
    public function actionEditLine()
    {
        //权限验证
        if (!$this->checkRole()) {
            $this->returnFailMessage('You do not have permission to perform this action.');
        }
        $staff_ids = Yii::app()->request->getParam('staff_ids', array());//需要操作的的员工id
        $top_uid = Yii::app()->request->getParam('top_uid', 0);//接收员工的用户id
        $requestUrl = "reportingLine/tier/".self::$type;
        $data = array(
            'staff_ids'      => $staff_ids,
            'top_uid'        => $top_uid,
            'line_school_id' => $this->branchId,
        );
        $this->remote($requestUrl, 'post', $data);
    }

    //初始状态添加员工关系
    public function actionAddLine()
    {
        //权限验证
        if (!$this->checkRole()) {
            $this->returnFailMessage('You do not have permission to perform this action.');
        }
        $staff_ids = Yii::app()->request->getParam('staff_ids', array());//下级员工id
        $top_uid = Yii::app()->request->getParam('top_uid', 0);//上级用户id
        $current_uid = Yii::app()->request->getParam('current_uid', 0);//当前节点用户id
        //校园首次添加
        if (!empty($current_uid) && empty($staff_ids) && empty($top_uid)) {
            $requestUrl = "reportingLine/tierIni/".self::$type;
        } else {
            $requestUrl = "reportingLine/tier/".self::$type;
        }
        //弹窗中修改关系
        $data = array(
            'staff_ids'      => $staff_ids,
            'top_uid'        => $top_uid,
            'current_uid'    => $current_uid,
            'line_school_id' => $this->branchId,
        );
        $this->remote($requestUrl, 'put', $data);
    }

    //可选择的上下级
    public function actionOptionalStaff()
    {
        $current_uid = Yii::app()->request->getParam('current_uid', 0);//当前节点用户id
        $status = Yii::app()->request->getParam('status', 0);// 1上级  2下级
        $school_id = Yii::app()->request->getParam('school_id', 0);// 学校id
        $requestUrl = "reportingLine/optionalStaff/".self::$type;
        $data = array(
            'status'      => $status,
            'current_uid' => $current_uid,
            'school_id'   => $school_id,
        );
        $this->remote($requestUrl, 'get', $data);
    }

    //可选学校列表
    public function actionSchoolList()
    {
        $requestUrl = "reportingLine/schoolList";
        $school_id = Yii::app()->request->getParam('school_id', 0);// 学校id
        $data = array(
            'school_id' => empty($school_id) ? $this->branchId : $school_id,
        );
        $this->remote($requestUrl, 'get', $data);
    }

    //审批组信息
    public function actionApprovalGroupList()
    {
        $requestUrl = "reportingLine/approvalGroup/".self::$type;;
        $school_id = Yii::app()->request->getParam('school_id', 0);// 学校id
        $data = array(
            'school_id' => empty($school_id) ? $this->branchId : $school_id,
        );
        $this->remote($requestUrl, 'get', $data);
    }

    //审批组添加修改
    public function actionSaveApprovalGroup()
    {
        //权限验证
        if (!$this->checkRole()) {
            $this->returnFailMessage('You do not have permission to perform this action.');
        }
        $requestUrl = "reportingLine/approvalGroup/".self::$type;;
        $school_id = Yii::app()->request->getParam('school_id', 0);// 学校id
        $inputData = Yii::app()->request->getParam('data', 0);// 学校id
        $data = array(
            'school_id' => empty($school_id) ? $this->branchId : $school_id,
            'data' => $inputData
        );
        $this->remote($requestUrl, 'post', $data);
    }

    //删除审批组
    public function actionDeleteApprovalGroup()
    {
        //权限验证
        if (!$this->checkRole()) {
            $this->returnFailMessage('You do not have permission to perform this action.');
        }
        $id = Yii::app()->request->getParam('id', 0);
        $requestUrl = "reportingLine/approvalGroup/".$id;
        $this->remote($requestUrl, 'delete');
    }

    //设置首选审批人和抄送人
    public function actionSaveApprovalFirstConfig()
    {
        //权限验证
        if (!$this->checkRole()) {
            $this->returnFailMessage('You do not have permission to perform this action.');
        }
        $staff_id = Yii::app()->request->getParam('current_uid', '');
        $setType = Yii::app()->request->getParam('setType', '');
        $cc_user_list = Yii::app()->request->getParam('cc_user_list', array());
        $backup_user_list = Yii::app()->request->getParam('backup_user_list', array());
        $requestUrl = "reportingLine/approvalFirstConfig/".self::$type."/".$staff_id;
        $this->remote($requestUrl, 'put',array(
            'cc_user_list' => $cc_user_list,
            'backup_user_list' => $backup_user_list,
            'setType' => $setType,
        ));
    }

    //审批组添加挂载人
    public function actionSaveApprovalGroupMount()
    {
        //权限验证
        if (!$this->checkRole()) {
            $this->returnFailMessage('You do not have permission to perform this action.');
        }
        $id = Yii::app()->request->getParam('id', 0);
        $old_id = Yii::app()->request->getParam('old_id', 0);
        $user_id = Yii::app()->request->getParam('user_id', 0);
        $requestUrl = "reportingLine/approvalGroupMount/".$id;
        $this->remote($requestUrl, 'put',array(
            'old_id' => $old_id,
            'user_id' => $user_id,
        ));
    }

    //清除审核组的挂载人
    public function actionDeleteApprovalGroupMount(){
        //权限验证
        if (!$this->checkRole()) {
            $this->returnFailMessage('You do not have permission to perform this action.');
        }
        $id = Yii::app()->request->getParam('id', 0);
        $user_id = Yii::app()->request->getParam('user_id', 0);
        $requestUrl = "reportingLine/approvalGroupMount/".$id;
        $this->remote($requestUrl, 'delete',array(
            'user_id' => $user_id
        ));
    }

    public function remote($requestUrl, $method = 'post', $requestData = array())
    {
        $res = CommonUtils::requestDsOnline2($requestUrl, $requestData, $method);
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('message', $res['msg']);
            $this->addMessage('data', $res['data']);
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->addMessage('data', $res['data']);
        }
        $this->showMessage();
    }

    public function returnFailMessage($message)
    {
        $this->addMessage('state', 'fail');
        $this->addMessage('message',$message);
        $this->addMessage('data', array());
        $this->showMessage();
    }

}