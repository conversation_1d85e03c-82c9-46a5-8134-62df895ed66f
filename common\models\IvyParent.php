<?php

/**
 * This is the model class for table "ivy_parent".
 *
 * The followings are the available columns in table 'ivy_parent':
 * @property integer $pid
 * @property string $password_text
 * @property string $cn_name
 * @property string $relationship
 * @property string $en_firstname
 * @property string $en_lastname
 * @property integer $gender
 * @property integer $language
 * @property string $country
 * @property string $province
 * @property string $company
 * @property string $job
 * @property string $address
 * @property string $post
 * @property string $tel
 * @property string $mphone
 * @property string $fax
 * @property string $childs
 * @property string $family_id
 * @property integer flag
 */
class IvyParent extends CActiveRecord {

    public $email = null;
    public $relationEmail = null;
    public $primary_contact_desc = null;

    /**
     * Returns the static model of the specified AR class.
     * @return IvyParent the static model class
     */
    public static function model($className = __CLASS__) {
        return parent::model($className);
    }

    /**
     * @return string the associated database table name
     */
    public function tableName() {
        return 'ivy_parent';
    }

    /**
     * @return array validation rules for model attributes.
     */
    public function rules() {
        // NOTE: you should only define rules for those attributes that
        // will receive user inputs.
        return array(
            //array('password_text, en_firstname, en_lastname', 'required'),
            array('password_text, en_firstname, relationship, en_lastname', 'safe'),
            array('pid, gender, language', 'numerical', 'integerOnly' => true),
            array('password_text, company, childs', 'length', 'max' => 255),
            array('family_id', 'length', 'max' => 32),
            array('cn_name, en_firstname, relationship, en_lastname, country, province, job, address, post, tel, mphone, fax', 'length', 'max' => 150),
            // The following rule is used by search().
            // Please remove those attributes that should not be searched.
            array('pid, relationship, password_text, cn_name, en_firstname, en_lastname, gender, language, country, province, company, job, address, post, tel, mphone, fax, childs', 'safe', 'on' => 'search'),
            array('en_firstname', 'allEmpty', 'on'=>'update, create'), //en_firstname 和 cn_name 不能同时为空
            array('relationEmail', 'email'),
        );
    }

    /**
     * @return array relational rules.
     */
    public function relations() {
        // NOTE: you may need to adjust the relation name and the related
        // class name for the relations automatically generated below.
        return array(
            'user' => array(self::HAS_ONE, 'User', 'uid'),
            'ivyUser' => array(self::BELONGS_TO, 'User', 'pid'),
            'countryInfo' => array(self::BELONGS_TO, 'Country', 'country'),
        );
    }

    public function allEmpty($attribute, $params) {
        if (!$this->hasErrors()) {
            if (trim($this->cn_name) == "" && trim($this->en_firstname) == "") {
                $this->addError("en_firstname", Yii::t("userinfo", "Please input either Chinese Name or First Name"));
            }
        }
    }

    public function getGenderList($pleaseChoose = true) {
        if (true == $pleaseChoose) {
            $arr = array('0' => Yii::t("labels", '请选择'));
        } else {
            $arr = null;
        }
        $arr[1] = Yii::t("labels", '男');
        $arr[2] = Yii::t("labels", '女');
        return $arr;
    }

    public function generateDefaultMail($childid, $father = true) {
        if (true === $father) {
            return 'father_' . $childid . '@defaultmail.com';
        } else {
            return 'mother_' . $childid . '@defaultmail.com';
        }
    }

    /**
     * @return array customized attribute labels (name=>label)
     */
    public function attributeLabels() {       
        return array(
            'pid' => 'Pid',
            'password_text' => 'Password Text',
            'cn_name' => Yii::t("labels", 'Chinese Name'),
            'relationship' => Yii::t("labels", 'Relationship'),
            'en_firstname' => Yii::t("labels", 'First Name'),
            'en_lastname' => Yii::t("labels", 'Last Name'),
            'gender' => Yii::t("labels", 'Gender'),
            'language' => Yii::t("labels", 'Language'),//母语
            'Language' => Yii::t("child", 'Language'),//沟通语言
            'flag' => Yii::t("labels", 'Flag'),
            'country' => Yii::t("labels", 'Nationality'),
            'province' => Yii::t("labels", 'Province'),
            'company' => Yii::t("labels", 'Company'),
            'job' => Yii::t("labels", 'Job'),
            'address' => 'Address',
            'post' => 'Post',
            'tel' => Yii::t("labels", 'Telephone'),
            'mphone' => Yii::t("labels", 'Mobile Phone'),
            'fax' => Yii::t("labels", 'Fax'),
            'ID_card' => Yii::t("labels", 'ID_card'),
            'childs' => 'Childs',
        );        
    }

    /**
     * Retrieves a list of models based on the current search/filter conditions.
     * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
     */
    public function search() {
        // Warning: Please modify the following code to remove attributes that
        // should not be searched.

        $criteria = new CDbCriteria;

        $criteria->compare('pid', $this->pid);
        $criteria->compare('password_text', $this->password_text, true);
        $criteria->compare('cn_name', $this->cn_name, true);
        $criteria->compare('relationship', $this->relationship, true);
        $criteria->compare('en_firstname', $this->en_firstname, true);
        $criteria->compare('en_lastname', $this->en_lastname, true);
        $criteria->compare('gender', $this->gender);
        $criteria->compare('language', $this->language);
        $criteria->compare('country', $this->country, true);
        $criteria->compare('province', $this->province, true);
        $criteria->compare('company', $this->company, true);
        $criteria->compare('job', $this->job, true);
        $criteria->compare('address', $this->address, true);
        $criteria->compare('post', $this->post, true);
        $criteria->compare('tel', $this->tel, true);
        $criteria->compare('mphone', $this->mphone, true);
        $criteria->compare('fax', $this->fax, true);
        $criteria->compare('childs', $this->childs, true);

        return new CActiveDataProvider($this, array(
            'criteria' => $criteria,
        ));
    }
    
    public function getName() {
        if (trim($this->en_lastname) != "")
            $eName = trim(sprintf("%s %s", trim($this->en_firstname), trim($this->en_lastname)));
        else
            $eName = trim($this->en_firstname);
        $cName = trim($this->cn_name);
        if (in_array($this->country, array(36, 175))) {
            $fullName = ($cName != "") ? $cName : $eName;
        } else {
            $fullName = ($eName != "") ? $eName : $cName;
        }
        return $fullName;
    }

    /**给制定位置设置 0/1
     * @param $n int 要设置的位置 从1开始
     * @param $set int 设置的数 0 / 1
     */
    public function setFlag($n,$set)
    {
        if($n>64){
            return  false;
        }
        $flagConfig = $this->flagConfig();
        $flags = array();
        foreach ($flagConfig as $item){
            $flags[]=$item['flag'];
        }
        if(!in_array($n,$flags)){
            return false;
        }
        if($set === 1){
            return $this->flag | (1 << $n-1);
        }else{
            return $this->flag & (~(1 << $n-1));
        }
    }

    /**
     * 家长所有标签
     * @return array
     */
    public function getParentFlag(){
        $binaryFlag = $this->getBinaryFlag();
        $flagConfig = $this->flagConfig();
        //获取 从右到左 1的位置
        $BinaryFlagArr = str_split($binaryFlag);
        krsort($BinaryFlagArr);
        $BinaryFlagArr = array_values($BinaryFlagArr);
        //0的位置
//        $flag0 = array_keys(array_filter($BinaryFlagArr,function ($bit){
//            return !$bit;
//        }));
        //1的位置
        $flag1 = array_keys(array_filter($BinaryFlagArr));
        $prentFlag = array();
        foreach ($flag1 as $k=>$value){
            if(!empty($flagConfig[$value+1])){
                $prentFlag[$k] = $flagConfig[$value+1];
            }
        }

        return $prentFlag;
    }

    /**获取指定位置的值 0/1
     * @param $n
     * @return int
     */
    public function getPositionFlag($n)
    {
        if($n>64){
            return  false;
        }
        $num = $this->flag;
        $digit = $n-1;
        return ($num & (1<<$digit))>>$digit;
    }

    /**十进制转二进制
     * @return string
     */
    public function getBinaryFlag()
    {
        return decbin($this->flag);
    }

    /**
     * 不同位置的1代表的标签 最多可设置64个标签
     * @return array
     */
    public function flagConfig()
    {
        return array(
            1=>array(
                'flag'=>1,
                'desc'=>Yii::t('labels','Primary Contact'),
                'name'=>Yii::t('labels','Primary Contact'),//主要联络人
                'editable_by_staff'=>true,//行政是否可以修改
                'color'=>'#d9534f',
            ),
            2=>array(
                'flag'=>2,
                'desc'=>Yii::t('global','Do NOT contact'),
                'name'=>Yii::t('global','Do NOT contact'),
                'editable_by_staff'=>true,
                'color'=>'#337ab7',
            )
        );
    }

    /**
     * 沟通语言 暂时只设置 英文 中文 中英双语
     */
    public function getLanguage(){
        $list = Term::model()->getLangList();
        $need = array(1,2,156);
        foreach ($list as $k=>$value){
            if(!in_array($k,$need)){
                unset($list[$k]);
            }
        }
        return $list;
    }

    public static function config()
    {
        return array(
            99 => Yii::t("labels", 'Default'),
            1 => Yii::t("labels", 'Father'),
            2 => Yii::t("labels", 'Mother'),
            3 => Yii::t("labels", 'Grandfather'),
            4 => Yii::t("labels", 'Grandmother'),
            5 => Yii::t("labels", 'Other'),
        );
    }
}