<div>
    <div class='flex relative'>
        <div class='dot'></div>
        <span class='stepBorderLeft'></span>
        <div class='ml8 pb24 flex1 width90'>
            <div class='font14 color3 mb20'>
                <span class='fontBold'><?php echo Yii::t('withdrawal','Send WeChat request form');?></span> 
                <span class='textHide'>
                    <span class='bluebg cur-p ml16' onclick='expand(1)'><?php echo Yii::t('newDS','Collapse');?> <span class='el-icon-arrow-up'></span></span> 
                </span> 
            </div>
            <div class='overscroll'>
                <div id='wechatBox'></div>
            </div>
        </div>
    </div>
    <div class='flex relative'>
        <div class='dot'></div>
        <div class='ml8  flex1 width90'>
            <div class='flex align-items' id='status'>
                <span class='font14 color3 fontBold'><?php echo Yii::t('withdrawal','Form submission');?></span> 
            </div>
            <div class='mt20 form-horizontal font14 applyData'>
                <div class='flex flexStart mb20'>
                    <div class='color6 flexWidth'><?php echo Yii::t('withdrawal','Applicant');?></div>
                    <div class='flex1 color3' id='applyUser'> </div>
                </div>
                <div class='flex mb20 flexStart'>
                    <div class='color6 flexWidth'><?php echo Yii::t('withdrawal','Withdrawal Date');?></div>
                    <div class='flex1 color3' id='withdrawal_date'></div>
                </div>
                <div class='flex mb20 flexStart'>
                    <div class='color6 flexWidth'><?php echo Yii::t('withdrawal','Where are you transferring to?');?></div>
                    <div class='flex1'>
                        <div class='color3' id='withdrawal_where'></div>
                        <div class='color6 mt4' id='withdrawal_where_memo'></div>
                    </div>
                    
                </div>
                <div class='flex mb20 flexStart'>
                    <div class='color6 flexWidth'><?php echo Yii::t('withdrawal','Reason for withdrawal');?></div>
                    <div class='flex1'>
                        <div class='color3' id='withdrawal_reason'></div>
                        <div class='color6 mt4' id='withdrawal_reason_memo'></div>
                    </div>
                </div>
                <div class='flex mb20 flexStart'>
                    <div class='color6 flexWidth'><?php echo Yii::t('withdrawal','Are there any aspects of school you are unsatisfied with?');?></div>
                    <div class='flex1'>
                        <div class='color3' id='withdrawal_unsatisfied'></div>
                    </div>
                </div>
                <div class='flex mb20 flexStart'>
                    <div class='color6 flexWidth'><?php echo Yii::t('withdrawal','Do you have any advice for us?');?></div>
                    <div class='flex1'>
                        <div class='color3' id='withdrawal_recommendations'></div>
                    </div>
                </div>
                <div class='flex mb20 flexStart' id='contact'>
                    <div class='color6 flexWidth'><?php echo Yii::t('withdrawal','Who do you wish to contact about the withdrawal process?');?></div>
                    <div class='flex1'>
                        <div class='color3' id='contact_user'></div>
                    </div>
                </div>
                
            </div>
        </div>
    </div>
</div>
<script>
    function expand(type){
        if(type==1){
            $('.overscroll').hide()
            $('.textHide').html("<span class='bluebg cur-p ml16' onclick='expand()'>展开 <span class='el-icon-arrow-down'></span></span>")
        }else{
            $('.overscroll').show()
            $('.textHide').html("<span class='bluebg cur-p ml16' onclick='expand(1)'>收起 <span class='el-icon-arrow-up'></span></span>") 
        }
    }
    init()
    function init(){    
        let send_list=childDetails.forms[0].send_list
        if(send_list.length==0){
            return
        }
        var send_log={}
        var latest_send={}
        if(childDetails.forms[0].data.send_log){
            send_log=childDetails.forms[0].data.send_log
        }
        if(childDetails.forms[0].data.latest_send){
            latest_send=childDetails.forms[0].data.latest_send
        }
        let openids = send_list.map(user => user.openid);
        $.ajax({
            url: '<?php echo $this->createUrl("getParentsFreq") ?>',
            type: "post",
            dataType: 'json',
            data: {
                childId: container.child_id,
                openIds:openids,
            },
            success: function(data) {
                if (data.state == 'success') {
                    var list=''
                    for(let i=0;i<send_list.length;i++){
                        let parentType=send_list[i].parentType=='father'?"<?php echo Yii::t('global','Father');?>":"<?php echo Yii::t('global','Mother');?>"
                        let total=Object.values(data.data[send_list[i].openid]).reduce((acc, curr) => acc + curr, 0)
                        var commentClass = total === 0 ? 'sendTag' : 'sendTagNum';
                        var interactionList = `
                            <div style='width:200px;padding:7px 6px 3px'>
                                <div class='font12 color3 fontBold mb12'>互动次数记录</div>
                                <div class='flex mb8 font12'><span class='flex1 color6'>沟通</span><span class='color3'>${data.data[send_list[i].openid].comment}</span></div>
                                <div class='flex mb8 font12'><span class='flex1 color6'>参与资料收集</span><span class='color3'>${data.data[send_list[i].openid].coll}</span></div>
                                <div class='flex mb8 font12'><span class='flex1 color6'>参与问卷</span><span class='color3'>${data.data[send_list[i].openid].survey}</span></div>
                                <div class='flex  font12'><span class='flex1 color6'>参与调查</span><span class='color3'>${data.data[send_list[i].openid].research}</span></div>
                            </div>
                        `;
                        if(commentClass=='sendTagNum'){
                            var question='<div class="mt12 interaction"><span class="mr8 interactionPopover '+commentClass+'" data-html="true" data-content="'+interactionList+'" tabindex="0" data-placement="bottom"  role="button"  data-trigger="focus" data-container="body"  data-toggle="popover" >互动 '+total+' 次</span></div>'
                        }else{
                            var question='<div class="mt12 interaction"><span class="mr8 '+commentClass+'" >互动 '+total+' 次</span></div>'
                        }
                        if(latest_send[send_list[i].openid]){
                            list="<div class='wechatList'>"
                            if(latest_send[send_list[i].openid].can_send){
                                list+="<div class='flex1'><div class='flex align-items'><img src="+send_list[i].headimgurl+" alt='' class='img28'><div class='font14 color3 ml8'>"+send_list[i].nickname+"</div><div class='font14 color6'>｜"+parentType+"</div></div>"+question+"<div class='font12 color6 mt10 cur-p' onclick='sendLog(this)' data-id="+send_list[i].openid+">最近一次推送："+latest_send[send_list[i].openid].date+" <span class='el-icon-arrow-right'></span></div>"
                            }else{
                                list+="<div class='flex1'><div class='flex align-items'><img src="+send_list[i].headimgurl+" alt='' class='img28'><div class='font14 color3 ml8'>"+send_list[i].nickname+"</div><div class='font14 color6'>｜"+parentType+"</div></div>"+question+"<div class='font12 color6 mt10 cur-p' onclick='sendLog(this)' data-id="+send_list[i].openid+">最近一次推送："+latest_send[send_list[i].openid].date+" <span class='el-icon-arrow-right'></span></div>"
                            }
                        }else{
                            list="<div class='wechatList'><div class='flex1'><div class='flex align-items'><img src="+send_list[i].headimgurl+" alt='' class='img28'><div class='font14 color3 ml8'>"+send_list[i].nickname+"</div><div class='font14 color6'>｜"+parentType+"</div></div>"+question+"<div class='font12 color9 mt10'>无发送记录</div></div></div>"
                        }
                        $('#wechatBox').append(list)
                        $('.interactionPopover').popover();
                        $('.interactionPopover').on('shown.bs.popover', function () {
                            $(this).addClass('clicked');
                        }).on('hidden.bs.popover', function () {
                            $(this).removeClass('clicked');
                        });
                    }
                }
            }
        })
        let data=childDetails.forms[0].data
        if(data.type_status){
            if(data.type_status==3){
                $('#contact').hide()
                $('#status').append("<span class='labelTag greenColor greenBg'>已代填写完成</span>")
            }
            if(data.type_status==2){
                $('#contact').show()
                $('#status').append("<span class='labelTag greenColor greenBg'>家长已填写完成</span>")
            }
            let indexData=container.indexDataList
            $('#withdrawal_where').text(indexData.whereList[data.withdrawal_where])
            $('#withdrawal_reason').text(indexData.reasonList[data.withdrawal_reason])
            if(childDetails.info.appInfo.applyUser){
                // console.log(send_list)
                // for(let j=0;j<send_list.length;j++){
                //     console.log(send_list[j].openid)
                //     if(send_list[j].openid==data.openid){
                //         let parentType=send_list[j].parentType=='father'?"父亲":"母亲"
                //         console.log(parentType)
                //         $('#applyUser').html("<div class='flex align-items'>"+
                //             "<img src="+send_list[j].headimgurl+" alt='' class='img28'>"+
                //             "<div class='font14 color3 ml8'>"+send_list[j].nickname+"</div>"+
                //             "<div class='tagList'>"+parentType+"</div>"+
                //             "</div>")
                //     }
                // }
                var parentType=''
                if(childDetails.info.appInfo.applyUser.is_staff==1){
                    parentType="<?php echo Yii::t('withdrawal','Submission on behalf of parent');?>"
                }else if(childDetails.info.appInfo.applyUser.pid==childDetails.info.baseInfo.fid){
                    parentType="<?php echo Yii::t('global','Father');?>"
                }else if(childDetails.info.appInfo.applyUser.pid==childDetails.info.baseInfo.mid){
                    parentType="<?php echo Yii::t('global','Mother');?>"
                }
                $('#applyUser').html("<div class='flex align-items'>"+
                "<img src="+childDetails.info.appInfo.applyUser.avatar+" alt='' class='img28'>"+
                "<div class='font14 color3 ml8'>"+childDetails.info.appInfo.applyUser.name+"</div>"+
                "<div class='tagList ml8'>"+parentType+"</div>"+
                "</div>")
            }
            $('#withdrawal_date').text(data.withdrawal_date)
            $('#withdrawal_where_memo').text(data.withdrawal_where_memo)
            $('#withdrawal_reason_memo').text(data.withdrawal_reason_memo)
            $('#withdrawal_unsatisfied').text(data.withdrawal_unsatisfied)
            $('#withdrawal_recommendations').text(data.withdrawal_recommendations)
            var text=''
            if(data.withdrawal_contact_user==2){
                $('#contact_user').html('<span class="el-icon-user mr5"></span>'+data.withdrawal_contact_name+'<span class="tagList ml8">其他</span><span class="glyphicon glyphicon-earphone ml16 mr8 color3"></span>'+data.withdrawal_contact_phone)
            }else{
                text+='<span class="el-icon-user mr5"></span>'+data.withdrawal_contact_name
                if(data.withdrawal_contact_user==childDetails.info.baseInfo.mid){
                    text+= '<span class="tagList ml8"><?php echo Yii::t('global','Mother');?></span>'
                }else{
                    text+= '<span class="tagList ml8"><?php echo Yii::t('global','Father');?></span>'
                }
                text+='<span class="glyphicon glyphicon-earphone ml16 mr8 color3"></span>'+data.withdrawal_contact_phone
                console.log(text)
                $('#contact_user').html(text)
            }
                
        }else{
            // $('.applyData').hide()
        }
    }
    function allChecked(){
        var objs = window.document.getElementsByName("sendList");
        for(var   i=0;i<objs.length;i++){
            if (objs[i].type == "checkbox" && objs[i].disabled==false){
                objs[i].checked = true;     
            }          
        }
    }
    function sendLog(that){
        let list=$(that).attr('data-id')
        let log=childDetails.forms[0].data.send_log
        var html=''
        for(let i=0;i<log[list].length;i++){
            html+="<div class='flex relative'>"+
                "<div class='dot'></div>"
                if(log[list].length>i+1){
                    html+="<span class='stepBorderLeft'></span>"
                }
                html+="<div class='ml8 pb24 flex1 width90' >"+
                    "<div>"+
                        "<span class='font14 color3 fontBold'>已推送</span>"+
                    "</div>"+
                    "<div class='color3 font14 mt5'>推送时间："+log[list][i].date+"</div>"+
                "</div>"+
            "</div>"
        }
        $('#dateList').html(html) 
        $('#dateListModal').modal('show')
    }
</script>
<style>
    .wechatList{
        height: 100px;
        background: #FAFAFA;
        border-radius: 4px;
        display:inline-block;
        padding:6px 16px;
        margin-right:16px
    }
    .img28{
        width: 28px;
        height: 28px;
        border-radius: 50%;
        object-fit: cover;
    }
    .yellow{
        color: #F0AD4E;
    }
    .overscroll{
    }
    #wechatBox{
        overflow-x: auto;        
        white-space: nowrap;
    }
    .tagList{
        background: #F0F5FB;
        border-radius: 2px;
        font-size: 12px;
        color: #4D88D2;
        padding:4px 6px ;
        display:inline-block;
        line-height:1
    }
    .sendTagNum{
        padding:4px 6px;
        font-size:12px;
        border-radius: 2px;
        background:#F0F5FB;
        color: #4D88D2;
    }
    .sendTag{
        padding:4px 6px;
        font-size:12px;
        border-radius: 2px;
        background:#F2F3F5;
        color: #333;
    }
    .interactionPopover{
        cursor: pointer;
    }
    .interactionPopover:hover{
        background: #4D88D2;
        color: #fff;
    }
    .clicked {
        background: #4D88D2;
        color: #fff;
    }
    .popover{
        border:none !important;
        z-index:1999 !important
    }
    .popover.bottom > .arrow{
        border-bottom-color: #fff;
        top: -10px;
    }
</style>