<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span>
    </button>
    <h4 class="modal-title" id="exampleModalLabel">编辑</h4>
</div>
<?php $form = $this->beginWidget('CActiveForm', array(
    'id' => 'course-desc',
    'enableAjaxValidation' => false,
    'htmlOptions' => array('class' => 'J_ajaxForm form-horizontal', 'role' => 'form'),
)); ?>
<div class="modal-body">
    <div class="form-group">
        <label class="col-xs-3"><?php echo Yii::t('label','学年') ?></label>
        <div class="col-xs-8">
            <?php echo $form->hiddenField($model, 'id');?>
            <?php echo $form->dropDownList($model, 'startyear', $startyear, array('class' => 'form-control')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3"><?php echo Yii::t('label','奖学金类型') ?></label>
        <div class="col-xs-8">
            <?php echo $form->dropDownList($model, 'apply_type', $applyType, array('class' => 'form-control')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3"><?php echo Yii::t('label','学生名') ?></label>
        <div class="col-xs-8">
            <?php echo $form->textField($model, 'first_name', array('maxlength' => 255, 'class' => 'form-control')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3"><?php echo Yii::t('label','学生姓') ?></label>
        <div class="col-xs-8">
            <?php echo $form->textField($model, 'last_name', array('maxlength' => 255, 'class' => 'form-control')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3"><?php echo Yii::t('label','出生日期') ?></label>
        <div class="col-xs-8">
            <?php echo $form->textField($model, 'birthday', array('maxlength' => 255, 'class' => 'datepicker form-control')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3"><?php echo Yii::t('label','当前学校') ?></label>
        <div class="col-xs-8">
            <?php echo $form->textField($model, 'current_school', array('maxlength' => 255, 'class' => 'form-control')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3"><?php echo Yii::t('label','就读时间') ?></label>
        <div class="col-xs-8">
            <?php echo $form->textField($model, 'study_time', array('maxlength' => 255, 'class' => 'form-control')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3"><?php echo Yii::t('label','申请年级1') ?></label>
        <div class="col-xs-8">
            <?php echo $form->textField($model, 'apply_grade', array('maxlength' => 255, 'class' => 'form-control')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3"><?php echo Yii::t('label','申请年级2') ?></label>
        <div class="col-xs-8">
            <?php echo $form->textField($model, 'apply_grade2', array('maxlength' => 255, 'class' => 'form-control')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3"><?php echo Yii::t('label','申请理由') ?></label>
        <div class="col-xs-8">
            <?php echo $form->textField($model, 'apply_reason', array('maxlength' => 255, 'class' => 'form-control')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3"><?php echo Yii::t('label','家长邮箱') ?></label>
        <div class="col-xs-8">
            <?php echo $form->textField($model, 'parent_email', array('maxlength' => 255, 'class' => 'form-control')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3"><?php echo Yii::t('label','家长电话') ?></label>
        <div class="col-xs-8">
            <?php echo $form->textField($model, 'parent_phone', array('maxlength' => 255, 'class' => 'form-control')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3"><?php echo Yii::t('label','班主任姓名') ?></label>
        <div class="col-xs-8">
            <?php echo $form->textField($model, 'head_teacher_name', array('maxlength' => 255, 'class' => 'form-control')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3"><?php echo Yii::t('label','班主任邮箱') ?></label>
        <div class="col-xs-8">
            <?php echo $form->textField($model, 'head_teacher_email', array('maxlength' => 255, 'class' => 'form-control')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3"><?php echo Yii::t('label','科任老师姓名') ?></label>
        <div class="col-xs-8">
            <?php echo $form->textField($model, 'class_teacher_name', array('maxlength' => 255, 'class' => 'form-control')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3"><?php echo Yii::t('label','科任老师邮箱') ?></label>
        <div class="col-xs-8">
            <?php echo $form->textField($model, 'class_teacher_email', array('maxlength' => 255, 'class' => 'form-control')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3"><?php echo Yii::t('label','已传文件') ?></label>
        <div class="col-xs-8" id="attachment">
            <?php
            if($model->attach) {
                $attach = json_decode($model->attach);
                foreach ($attach as $k => $file) {
                    ?>
                    <span class="mt delFund_<?php echo $file ?>">
                    <?php echo CHtml::hiddenField('files[]', $file); ?>
                    <a href="<?php echo $this->createUrl('ossfileRedit', array('filePath' => $file, 'space' => 'admissions/')) ?>"
                       target="_blank" class="btn btn-info btn-xs"><span class="glyphicon glyphicon-file"></span> 点击查看图片</a>
                    <span class="btn btn-danger btn-xs delfile"
                          onclick="delrefund(this)"
                          value="<?php echo $file ?>"><i class="glyphicon glyphicon-remove"></i></span>
                </span>
                <?php }
            }
            ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3"><?php echo Yii::t('label','文件') ?></label>
        <div>
            <input id="inputfile" onchange="uploadata()" type="file" >
        </div>
    </div>
</div>
<div class="modal-footer">
    <button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global', 'Submit'); ?></button>
    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('global', 'Cancel'); ?></button>
</div>
<?php $this->endWidget(); ?>
<script>
    $('.datepicker').datepicker({
        changeMonth: true,
        changeYear: true,
        dateFormat: 'yy-mm-dd'
    });
    function uploadata () {
        var data = new FormData();
        $.each($('#inputfile')[0].files, function(i, file) {
            data.append('upload_file', file);
        });
        $.ajax({
            url:'<?php echo $this->createUrl('refundFiles'); ?>',
            data:data,
            type:'post',
            dataType:'json',
            contentType: false,    //不可缺
            processData: false,    //不可缺
            success : function (data) {
                console.log(data)
                if (data.msg == 'success') {
                    //清空文件域
                    $("#inputfile").after($("#inputfile").clone().val(""));
                    $("#inputfile").remove();
                    //显示附件
                    var atta = '<span class="mt delFund_'+data.saveName+'"><a class="btn btn-info btn-xs" target="_blank" href="'+data.url+'">'+data.saveName+' <span></span></a><input type="hidden" name="files[]" value="'+data.saveName+'" > <span class="btn btn-danger btn-xs delfile" onclick="delrefund(this)" _value="' + data.saveName + '"><i class="glyphicon glyphicon-remove"></i></span></span> ';
                    $("#attachment").append(atta);
                }else{
                    resultTip({
                        error: 'warning',
                        msg:data.msg
                    });
                    $("#inputfile").after($("#inputfile").clone().val(""));
                    $("#inputfile").remove();
                }
            },
            error : function (data) {
                resultTip({
                    error: 'warning',
                    msg: '请求错误'
                });
            }
        });
    }

    function delrefund(obj) {
        $(obj).parent().remove();
    }
</script>
