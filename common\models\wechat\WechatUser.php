<?php

/**
 * This is the model class for table "wechat_users".
 *
 * The followings are the available columns in table 'wechat_users':
 * @property string $openid
 * @property integer $userid
 * @property integer $valid
 */
class WechatUser extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return WechatUser the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'wechat_users';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('openid, userid, valid', 'required'),
			array('userid, valid, updated, last_contact, child_active, group_sync', 'numerical', 'integerOnly'=>true),
			array('disabled, campus','safe'),
			array('openid, unionid', 'length', 'max'=>128),
			array('account', 'length', 'max'=>255),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('openid, userid, valid', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'info' => array(self::HAS_ONE, 'WechatUsersInfo', 'openid'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'openid' => 'Openid',
			'userid' => 'Userid',
			'valid' => 'Valid',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('openid',$this->openid,true);
		$criteria->compare('userid',$this->userid);
		$criteria->compare('valid',$this->valid);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	public function updateInfo()
	{
		if ($this->account) {
			$baseUrl = 'https://api.weixin.qq.com/cgi-bin/user/info';
			$accessToken = CommonUtils::getAccessToken($this->account);

			$url = $baseUrl . '?access_token=' . $accessToken .'&openid='. $this->openid;
			$responseData = file_get_contents($url);
			$res = CJSON::decode($responseData);
			if (isset($res['errcode'])) {
			    return false;
			}
			if (isset($res['subscribe']) && $res['subscribe'] == 0) {
			    return false;
			}
			if (isset($res['unionid'])) {
				$this->unionid = $res['unionid'];
				$this->save();
			}
			$infoModel = WechatUsersInfo::model()->findByPk($this->openid);
			if (!$infoModel) {
			    $infoModel = new WechatUsersInfo();
			}
			$infoModel->openid = $this->openid;
			$infoModel->info = json_encode($res);
			$infoModel->updated = time();
			$infoModel->save();
		}
	}

    public function updateInfov2()
    {
        if ($_GET['access_token'] && $_GET['openid']) {
            $url = 'https://api.weixin.qq.com/sns/userinfo?access_token=%s&openid=%s&lang=zh_CN';
            $rawRet = file_get_contents(sprintf($url, $_GET['access_token'], $_GET['openid']));
            $ret = json_decode($rawRet);
            if(isset($ret->openid) && $ret->openid){
                $uModel = WechatUsersInfo::model()->findByPk($_GET['openid']);
                if (!$uModel) {
                    $uModel = new WechatUsersInfo();
                }
                $uModel->openid = $_GET['openid'];
                $uModel->info = $rawRet;
                $uModel->updated = time();
                if (!$uModel->save()) {
                    Yii::log('wechatSaveUser', 'info', $_GET['openid'].'-'.json_encode($uModel->getErrors()));
                }
            }
            else {
                Yii::log('wechatGetUser', 'info', $_GET['openid'].'-'.$rawRet);
            }
        }
    }

	/**
	 * 给微信用户打语言标签
	 *
	 * @param [type] $lang cn中文，en英文
	 * @return void
	 */
	public function updateTag($lang)
	{
		$accessToken = CommonUtils::getAccessToken($this->account);
		if (!$accessToken) {
			return false;
		}
		$openId = $this->openid;
        if ($this->account == 'mmx') {
            $cnTagId = 127; // 中文
            $enTagId = 128; // 英文
        }
		else if ($this->account == 'ivy') {
			// 标签ID
			$cnTagId = 132; // 中文
			$enTagId = 133; // 英文
		} else {
			// 标签ID
			$cnTagId = 129; // 中文
			$enTagId = 130; // 英文
		}

		// 取消标签
		$this->batchUnTag(array($openId), $cnTagId, $accessToken);
		$this->batchUnTag(array($openId), $enTagId, $accessToken);

		// 打标签
		if ($lang == 'en') {
			$res = $this->batchTag(array($openId), $enTagId, $accessToken);
		} else {
			$res = $this->batchTag(array($openId), $cnTagId, $accessToken);
		}
		if ($res) {
			// 保存表
			$this->lang = $lang;
			$this->save();
		}

		return $res;
	}

	// 批量打标签
	public static function batchTag($openidList, $tagId, $token)
	{
		$ch = curl_init();
		$postData = array(
			"tagid" => $tagId,
			"openid_list" => $openidList
		);
		
		curl_setopt($ch, CURLOPT_URL,"https://api.weixin.qq.com/cgi-bin/tags/members/batchtagging?access_token=" . $token);
		curl_setopt($ch, CURLOPT_POSTFIELDS, CJSON::encode($postData));
		curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

		$res = curl_exec($ch);
		curl_close($ch);
		$resArr = CJSON::decode($res);
		if (isset($resArr['errcode']) && $resArr['errcode'] == 0) {
			return true;
		} else {
			return false;
		}
	}

	// 批量取消标签
	public static function batchUnTag($openidList, $tagId, $token)
	{
		$ch = curl_init();
		$postData = array(
			"tagid" => $tagId,
			"openid_list" => $openidList
		);
		
		curl_setopt($ch, CURLOPT_URL,"https://api.weixin.qq.com/cgi-bin/tags/members/batchuntagging?access_token=" . $token);
		curl_setopt($ch, CURLOPT_POSTFIELDS, CJSON::encode($postData));
		curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

		$res = curl_exec($ch);
		curl_close($ch);
		$resArr = CJSON::decode($res);
		Yii::log('wechatLangTag', 'info', $resArr);
		if (isset($resArr['errcode']) && $resArr['errcode'] == 0) {
			return true;
		} else {
			return false;
		}
	}
}
