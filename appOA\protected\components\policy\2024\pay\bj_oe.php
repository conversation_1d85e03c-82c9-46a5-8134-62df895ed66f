<?php
if(!defined("IVY_POLICY"))
throw new CException('DO NOT USE THIS FILE DIRECTLY');

return array( 'policy'=>array(

	//是否老生老办法
	ENABLE_CONSTANT_TUITION => true,
    // 按天收费
    FENTAN_FACTOR => DAILY,

	//年保育费
	//ANNUAL_CHILDCARE_AMOUNT => 8000,

	//是否开放课外活动账单
	ENABLE_AFTERSCHOOLS => true,

	//图书馆工本费
	FEETYPE_LIBCARD => 10,

	//学期四个时间点；取前后两端
	TIME_POINTS   => array(202408,202501,202502,202507),

	//年付开通哪些缴费类型
//	PAYGROUP_ANNUAL => array(
//		ITEMS => array(
//			FEETYPE_TUITION,
//			FEETYPE_LUNCH,
//			FEETYPE_SCHOOLBUS,
//		),
//		TUITION_CALC_BASE => BY_MONTH,
//	),
//
//	学期付开通哪些缴费类型
	 PAYGROUP_SEMESTER => array(
	 	ITEMS => array(
	 		FEETYPE_TUITION,
	 		FEETYPE_LUNCH,
            FEETYPE_SCHOOLBUS,
	 	),
	 	TUITION_CALC_BASE => BY_SEMESTER,
	 ),
//
//    PAYGROUP_MONTH => array(
//        ITEMS => array(
//            FEETYPE_TUITION,
//            FEETYPE_LUNCH,
//            FEETYPE_SCHOOLBUS,
//        ),
//        TUITION_CALC_BASE => BY_MONTH,
//    ),

	//计算基准：BY_MONTH, 或者 BY_SETTING, 前者表示所有的学费都基于月费用计算，后者表示所有的学费都基于学期或学年
	TUITION_CALCULATION_BASE => array(


        BY_MONTH => array(

            //月收费金额
            // AMOUNT => array(
            //    FULL5D => 10500,
            //    HALF5D => 7560,
                // FULL3D => 6840,
                // HALF3D => 5050,
                // FULL2D => 4750,
                // HALF2D => 4321,
            // ),

            //收费月份，及其权重
            MONTH_FACTOR => array(
                202408=>.167,
                202409=>1,
                202410=>1,
                202411=>1,
                202412=>1,
                202501=>1,
                202502=>0.75,
                202503=>1,
                202504=>1,
                202505=>1,
                202506=>1,
                202507=>0.2
            ),

            //折扣
            GLOBAL_DISCOUNT => array(
                DISCOUNT_ANNUAL => '1:1:2025-06-01:round',
                DISCOUNT_SEMESTER1 => '1:1:2025-07-02:round',
                DISCOUNT_SEMESTER2 => '1:1:2025-07-31:round'
            )
        ),

        BY_SEMESTER1 => array(
            AMOUNT => array(
                FULL5D => 87687,
                PROGRAM_IBS => 41144,
//                FULL5D2 => 40143,
                // HALF2D => 28500,
                // HALF3D => 41500,
                HALF5D => 61378,
            ),
        ),

        BY_SEMESTER2 => array(
            AMOUNT => array(
                FULL5D => 97323,
                PROGRAM_IBS => 54256,
//                FULL5D2 => 55256,
                // HALF2D => 28500,
                // HALF3D => 41500,
                // HALF5D => 55322,
            ),
            ENDDATE => array(
                FULL5D => 1751558400,
                RS_FULL5D => 1751558400,
                PROGRAM_IBS => 1753891200,
                LUNCHA => 1751558400,
                LUNCHB => 1753891200,

                // 国际班校车费
                41870 => 1751558400,
                41871 => 1751558400,
                41872 => 1751558400,
                41889 => 1751558400,
                41890 => 1751558400,
                41891 => 1751558400,

                // 双语班校车费
                41864 => 1753891200,
                41865 => 1753891200,
                41866 => 1753891200,
                41867 => 1753891200,
                41868 => 1753891200,
                41869 => 1753891200,

            )
        ),
	),

	//每餐费用
    LUNCH_PERDAY => array(
        LUNCHA => 45,
        LUNCHB => 45,
    ),

	//账单到期日和账单开始日之差
	DUEDAYS_OFFSET => 1,

));