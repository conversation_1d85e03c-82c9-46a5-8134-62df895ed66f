<div class="container-fluid">
	<!-- 面包屑导航 -->
	<ol class="breadcrumb">
	    <li><?php echo CHtml::link(Yii::t('site','Campus Operations'), array('//mcampus/default/index'))?></li>
	    <li><?php echo CHtml::link(Yii::t('site','Routines'), array('//mcampus/default/index'))?></li>
        <li><?php echo CHtml::link(Yii::t('curriculum','Curriculum'), array('//mcampus/curriculum/project'))?></li>	    
        <li class="active"><?php echo Yii::t('curriculum','My Projects');?></li>
	</ol>
	<div class="row">
		<!-- 左侧菜单栏 -->
		<div class="col-md-2">
			<div class="list-group" id="classroom-status-list">
			    <?php foreach ($this->leftMenu as $key => $value) { ?>
			    <a href="<?php echo $this->createUrl($value[0]);?>" class="list-group-item status-filter <?php echo $this->getAction()->getId()==$value[0]?'active':''; ?>"><?php echo $value[1]; ?></a></li>
			    <?php } ?>
			</div>
		</div>
		<div class="col-md-10">
			<div class="panel panel-primary">
			    <div class="panel-heading"><h4><?php echo Yii::t('curriculum','My Projects');?>：</h4></div>
				 	<div class="panel-body">
						<?php 
							echo "<table valign='middle' class='table table-bordered'>";
							echo '<colgroup><col width="100"><col width="50"></colgroup>';
							if (!empty($projects)) {
								echo '<tr><th colspan=2>'.Yii::t('curriculum','My Projects').'：</th></tr>';
								foreach ($projects as $key => $value) {
									echo '<tr><td><a target="_blank" href="'.$this->createUrl('showproject',array('pid'=>$value->pid)).'">'.CHtml::encode($value->cn_title).' '.CHtml::encode($value->en_title).'</a></td>';
									echo "<td><button onclick='deletePro(this,{$value->pid})' class='btn btn-danger btn-xs'>".Yii::t('curriculum','DeletePro')."</button></td></tr>";
								}
							}

							if (!empty($favorites)) {
								echo '<tr><th colspan=2>'.Yii::t('curriculum','My Favorite').'：</th></tr>';
								foreach ($favorites as $key => $value) {
									$url = $this->createUrl('deleteFavortes',array('fid'=>$value->fid));
									echo '<tr><td><a target="_blank" href="'.$this->createUrl('showproject',array('pid'=>$value->projects->pid)).'">'.$value->projects->cn_title.' '.$value->projects->en_title.'</a></td>'."<td><button class='btn btn-danger btn-xs' onclick = 'removeCollect(this,{$value->pid})'>".Yii::t('curriculum','Unsubscribe')."</button></td></tr>";
								}
							}
							echo "</table>";
						?>
				 	</div>
			    </div>
		</div>
	</div>
</div>


<script>
	//取消收藏项目
	function removeCollect (remBtn,pid) {
		$.ajax({
			url:'<?php echo $this->createUrl('removeCollect'); ?>',
			data:{pid : pid},
			type:'post',
			dataType:'json',
			success : function (data) {
				if (data.msg == 'success') {
					$(remBtn).parent().parent().remove();
				}
			},
			error : function (data) {
				// body...
			}
		});
	}
	//删除项目
	function deletePro (deletebtn,pid) {
	    if (confirm('确定要删除吗？')) {
	        $.ajax( {    
	            url:'<?php echo $this->createUrl('deleteProject'); ?>',    
	            data:{pid : pid},    
	            type:'post',    
	            dataType:'json',    
	            success:function(data) {    
	                if(data.msg == 'success'){    
	                    $(deletebtn).parent().parent().remove();
	                    // window.location.reload();    
	                }else{    
	                    alert(data.msg);
	                    console.log(data);    
	                }    
	             },    
	            error : function() {    
	                console.log("异常！");    
	            }    
	        });  
	    }
	}
</script>
