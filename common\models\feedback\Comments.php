<?php

/**
 * This is the model class for table "ivy_comments".
 *
 * The followings are the available columns in table 'ivy_comments':
 * @property integer $id
 * @property integer $com_post_id
 * @property integer $com_root_id
 * @property string $com_mod_name
 * @property string $com_type
 * @property string $com_title
 * @property string $com_content
 * @property integer $com_child_id
 * @property integer $com_uid
 * @property integer $com_class_id
 * @property integer $com_yid
 * @property integer $com_week_num
 * @property string $com_school_id
 * @property integer $com_status
 * @property integer $com_ifshow
 * @property integer $com_ifread
 * @property integer $com_ifreply
 * @property integer $com_user_type
 * @property integer $com_created_time
 */
class Comments extends CActiveRecord {

    // 在显示所有反馈的页面 决定是否显示 周信息(如果本周有多条反馈 只有第一条反馈才显示周信息)
    public $showWeekInfo = true;
    public $userName = null;
    public $userPhoto = null;

    /**
     * Returns the static model of the specified AR class.
     * @param string $className active record class name.
     * @return Comments the static model class
     */
    public static function model($className = __CLASS__) {
        return parent::model($className);
    }

    /**
     * @return string the associated database table name
     */
    public function tableName() {
        return 'ivy_comments';
    }

    /**
     * @return array validation rules for model attributes.
     */
    public function rules() {
        // NOTE: you should only define rules for those attributes that
        // will receive user inputs.
        return array(
            array('com_content, com_class_id, com_week_num', 'required'),
            array('com_post_id, com_root_id, com_child_id, com_uid, com_class_id, com_yid, com_week_num, com_status, com_ifshow, com_ifread, com_ifreply, com_user_type, com_created_time', 'numerical', 'integerOnly' => true),
            array('com_mod_name, com_type, com_school_id', 'length', 'max' => 25),
            array('com_title', 'length', 'max' => 255),
            array('com_content', 'safe'),
            // The following rule is used by search().
            // Please remove those attributes that should not be searched.
            array('id, com_post_id, com_root_id, com_mod_name, com_type, com_title, com_content, com_child_id, com_uid, com_class_id, com_yid, com_week_num, com_school_id, com_status, com_ifshow, com_ifread, com_ifreply, com_user_type, com_created_time', 'safe', 'on' => 'search'),
        );
    }

    /**
     * @return array relational rules.
     */
    public function relations() {
        // NOTE: you may need to adjust the relation name and the related
        // class name for the relations automatically generated below.
        return array(
            'userWithProfile' => array(self::BELONGS_TO, 'User', 'com_uid', 'together' => false, 'with' => 'profile'),
            'staffInfo' => array(self::BELONGS_TO, 'InfopubStaffExtend', 'com_uid'),
            // howard 添加 (/portfolio/portfolio/journal)
            'replies' => array(self::HAS_MANY, 'Comments', 'com_root_id'),
            'yInfo' => array(self::BELONGS_TO, 'Calendar', 'com_yid'),
            'childInfo' => array(self::BELONGS_TO, 'ChildProfileBasic', 'com_child_id'),
            'classInfo' => array(self::BELONGS_TO, 'IvyClass', 'com_class_id', 'select'=>'title'),
        );
    }

    /**
     * @return array customized attribute labels (name=>label)
     */
    public function attributeLabels() {
        return array(
            'id' => 'ID',
            'com_post_id' => 'Com Post',
            'com_root_id' => 'Com Root',
            'com_mod_name' => 'Com Mod Name',
            'com_type' => 'Com Type',
            'com_title' => 'Com Title',
            'com_content' => 'Com Content',
            'com_child_id' => 'Com Child',
            'com_uid' => 'Com Uid',
            'com_class_id' => 'Com Class',
            'com_yid' => 'Com Yid',
            'com_week_num' => 'Com Week Num',
            'com_school_id' => 'Com School',
            'com_status' => 'Com Status',
            'com_ifshow' => 'Com Ifshow',
            'com_ifread' => 'Com Ifread',
            'com_ifreply' => 'Com Ifreply',
            'com_user_type' => 'Com User Type',
            'com_created_time' => 'Com Created Time',
        );
    }

    /**
     * Retrieves a list of models based on the current search/filter conditions.
     * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
     */
    public function search() {
        // Warning: Please modify the following code to remove attributes that
        // should not be searched.

        $criteria = new CDbCriteria;

        $criteria->compare('id', $this->id);
        $criteria->compare('com_post_id', $this->com_post_id);
        $criteria->compare('com_root_id', $this->com_root_id);
        $criteria->compare('com_mod_name', $this->com_mod_name, true);
        $criteria->compare('com_type', $this->com_type, true);
        $criteria->compare('com_title', $this->com_title, true);
        $criteria->compare('com_content', $this->com_content, true);
        $criteria->compare('com_child_id', $this->com_child_id);
        $criteria->compare('com_uid', $this->com_uid);
        $criteria->compare('com_class_id', $this->com_class_id);
        $criteria->compare('com_yid', $this->com_yid);
        $criteria->compare('com_week_num', $this->com_week_num);
        $criteria->compare('com_school_id', $this->com_school_id, true);
        $criteria->compare('com_status', $this->com_status);
        $criteria->compare('com_ifshow', $this->com_ifshow);
        $criteria->compare('com_ifread', $this->com_ifread);
        $criteria->compare('com_ifreply', $this->com_ifreply);
        $criteria->compare('com_user_type', $this->com_user_type);
        $criteria->compare('com_created_time', $this->com_created_time);

        return new CActiveDataProvider($this, array(
                    'criteria' => $criteria,
                ));
    }

    public function beforeSave() {
        if (parent::beforeSave()) {
            // 同一用户插入间隔不能大于2秒
            $criteria = new CDbCriteria();
            $criteria->compare('com_uid', $this->com_uid);
            $criteria->select = 'com_created_time';
            $criteria->order = 'com_created_time desc';
            $commentsModel = Comments::model()->find($criteria);
            if(!$commentsModel){
                return true;
            }
            $timeStamp = time();
            $createTime = $commentsModel->com_created_time;
            if ($timeStamp - $createTime >= 2) {
                return true;
            }
            $this->addError('com_created_time','操作过快');
        }
        return false;
    }

}