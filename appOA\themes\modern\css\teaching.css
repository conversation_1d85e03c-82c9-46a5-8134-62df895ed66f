.icon-delete, .icon-rotate, .icon-tag, .icon-avatar{
	background: url(../images/media_icons.png) no-repeat top left transparent;
	display: block;
	width: 16px;
	height: 16px;
}
.icon-delete{
	background-position: -32px top;
}
.icon-tag{
	background-position: -16px top;
}
.icon-avatar{
	background-position: -48px top;
}
#media-list li div.itemOp a{
	display: block;
	float: left;
	width: 16px;
	height: 16px;
	text-indent: -9999px;
	margin-right: 4px;
}

li{
	-webkit-text-size-adjust:none;
	text-align: left;
}
.child-list li {
	padding: 2px;
	height: 30px;
	line-height: 30px;
	margin-bottom: 2px;
	border-bottom: 2px solid #f3f3f3;
	/*overflow: hidden;*/
	cursor: pointer;
	position: relative;
	padding-left: 16px;
}
.child-list li span{
	display: inline-block;
	padding: 2px;
	overflow: hidden;
	width: 120px;
	height: 30px;
}
.child-list li.current {
	border-bottom: 2px solid #DD1E2F;
	color:#DD1E2F;
}
.child-list li em.stats{
	position: absolute;
	left: 0;
	top: 0;
	display: block;
	font-size: 10px;
	padding: 1px 6px;
	border: 1px solid #fff;
	line-height: 14px;
}
.child-list li em.stats-1{
	background:#ff6600;
	color:#000;
}
.child-list li em.stats-2{
	background:#ffff66;
	color:#333;
}
.child-list li em.stats-3{
	background:#009933;
	color:#333;
}
.child-list li:hover {
	background:#fff;
}
.child-list li img.face{
	width: 26px;
	vertical-align: top;
	padding-right: 4px;
}
.child-list li div.s-rpt{
	padding: 2px;
	position: absolute;
	display: none;
	height: 30px;
	width: 180px;
	left: 176px;
	top:-2px;
	height: 30px;
	line-height: 30px;
	background:#fff;
}
.child-list li div.s-rpt a{
	margin-right: 10px;
}
.child-list li:hover div.s-rpt{
	display: block;
}
#media-list li {
	float: left;
	width: 120px;
	height: 126px;
	padding: 4px;
	position: relative;
}
#media-list li em.tagged, #media-list li div.info{
	position: absolute;
	top: 0;
	left: 0;
	background-color: #333;
	color:#fff;
	padding: 1px 6px;
	font-size: 10px;
	border: 1px solid #fff;
}
#media-list li em.loading{
	position: absolute;
	right: 12px;
	top: 4px;
	width: 16px;
	height: 16px;
	display: block;
	z-index: 2;
	background-color: #fffde3;
	background-position: 0 0;
	border: 0;
	padding:0;
}
#media-list li:hover em.tagged{
	background:#ff0000;
}
#media-list li img.preview{
	width: 100px;
	border: 2px solid #fff;
}
#media-list li.selected em.tagged{
	background:#ff0000;
}
#media-list li.selected img.preview{
	border-color:#ff6600;
}
#media-list li div.itemOp {
	visibility: hidden;
}
#media-list li:hover div.itemOp {
	visibility: visible;
}
#child-checkBoxTemp ul li{
	float: left;
	width: 220px;
	display: block;
}
.mediaByChild{
	position: relative;
}
ul#current-child{
	position: absolute;
	width: 180px;
	right: 40px;
	top: 2px;
	border: 0 !important;
}
img.full-preview{
	max-height: 120px;
}
h2.tag-title{
	font-size: 12px;
	background:#dedede;
	padding: 2px 4px;
}
div.tag-wrapper{
	padding: 10px;
}
.height300{
	height:300px;
	overflow-y:auto;
}
.child-checklist li em.devflag{
	left: 3px;
	position: absolute;
	display: block;
	width: 10px;
	height: 10px;
	background: url(../images/stats.png?t=3) no-repeat left top transparent;
}
.child-checklist li em.finished{
	background-position: 0 -10px;
}
.child-checklist li em.flag-1{
	top: 2px;
}
.child-checklist li em.flag-2{
	top: 16px;
}
.checklist td.level0{
	background: #e6e6e6;
	font-weight: bold;
}
.checklist td.level1,.checklist td.level2{
	color:#333 !important;
	font-weight: bold;
}
.checklist td.item div{
	padding-left: 4em;
	background-image: url(../images/level.png) ;
	background-repeat: no-repeat ;
	background-position: 32px -10px ;
}
#checklist-zone{
	padding-left: 1em;
}