<script>
var monthItemList = <?php echo $monthItemList;?>; //按月统一数据
var yearTtemList = <?php echo $yearTtemList;?>; //按年统一数据
var codeFinanceActual = <?php echo IvyStatsItem::CODE_FINANCE_ACTUAL;?>;//实际
var codeFinanceBudget = <?php echo IvyStatsItem::CODE_FINANCE_BUDGET;?>;//预算
var subcodeEnrollment = <?php echo IvyStatsItem::SUBCODE_FINANCE_ENROLLMENT;?>;//招生人数
var subcodeRevenue = <?php echo IvyStatsItem::SUBCODE_FINANCE_REVENUE;?>;//收入
var subcodePersonnelCost = <?php echo IvyStatsItem::SUBCODE_FINANCE_PERSONNEL_COST;?>;//人事费用
var subcodeFacilitiesCost = <?php echo IvyStatsItem::SUBCODE_FINANCE_FACILITIES_COST;?>;//设备费用
var subcodeOverhend = <?php echo IvyStatsItem::SUBCODE_FINANCE_OVERHEND;?>;//校园日常动作成本
var subcodeOtherCost = <?php echo IvyStatsItem::SUBCODE_FINANCE_OTHER_COSTS;?>;//其它(折旧、市场管理费用等)
var subcodeCashFlow = <?php echo IvyStatsItem::SUBCODE_FINANCE_CASH_FLOW;?>;//现金流
var subcodeTeachers = <?php echo IvyStatsItem::SUBCODE_FINANCE_TEACHERS;?>;//教师（全职、包括保育教师）
var subcodeAdmin = <?php echo IvyStatsItem::SUBCODE_FINANCE_ADMIN;?>;//行政人员（园长、副园长、行政经理、助理、保健医）
(function($){
    addData(null,null);
})(jQuery);

function addData(monthData,yearData){
    if (!isset(monthData) && !isset(yearData)){
        monthData = monthItemList;
        yearData = yearTtemList;
    }
    $('table#one tbody').html();
}

 </script>
