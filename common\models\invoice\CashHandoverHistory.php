<?php

/**
 * This is the model class for table "ivy_cash_handover_history".
 *
 * The followings are the available columns in table 'ivy_cash_handover_history':
 * @property integer $id
 * @property integer $handover_date
 * @property integer $count
 * @property integer $userid
 * @property string $schoolid
 * @property double $amount
 * @property string $transaction_id
 * @property string $invoice_id
 * @property integer $timestmpe
 * @property integer $status
 */
class CashHandoverHistory extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CashHandoverHistory the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_cash_handover_history';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('handover_date, count, userid, schoolid, amount, timestmpe, transaction_id, invoice_id', 'required'),
			array('handover_date, count, userid, timestmpe, status', 'numerical', 'integerOnly'=>true),
			array('amount', 'numerical'),
			array('schoolid', 'length', 'max'=>100),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, handover_date, count, userid, schoolid, amount, transaction_id, invoice_id, timestmpe, status', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'userInfo' => array(self::BELONGS_TO, 'User', 'userid'),
            'handoverLink'=>array(self::HAS_MANY,'CashHandoverLink','handover_id','with'=>'transaction'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'handover_date' => 'Handover Date',
			'count' => 'Count',
			'userid' => 'Userid',
			'schoolid' => 'Schoolid',
			'amount' => 'Amount',
			'transaction_id' => 'Transaction',
			'invoice_id' => 'Invoice',
			'timestmpe' => 'Timestmpe',
			'status' => 'Status',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('handover_date',$this->handover_date);
		$criteria->compare('count',$this->count);
		$criteria->compare('userid',$this->userid);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('amount',$this->amount);
		$criteria->compare('transaction_id',$this->transaction_id,true);
		$criteria->compare('invoice_id',$this->invoice_id,true);
		$criteria->compare('timestmpe',$this->timestmpe);
		$criteria->compare('status',$this->status);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}