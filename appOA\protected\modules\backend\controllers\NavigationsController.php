<?php

class NavigationsController extends ProtectedController
{
	public function actionList()
	{
		$this->layout = "//layouts/blank";
		Yii::app()->clientScript->registerCoreScript('jquery');
//		Yii::app()->clientScript->registerCoreScript('jquery.ui');
		//echo urlencode("http://apps.ivynew.cn");
		Yii::app()->clientScript->registerCssFile(Yii::app()->theme->baseUrl.'/css/navigation.css');
//		Yii::app()->clientScript->registerCssFile( 'http://code.jquery.com/ui/1.10.3/themes/smoothness/jquery-ui.css' );
//		Yii::app()->clientScript->registerScriptFile( 'http://code.jquery.com/ui/1.10.3/jquery-ui.js' );
        
        $alias = 'application.components.menu';
        $menuCategory = require Yii::getPathOfAlias($alias).'/menuCategory.php';
        $menuItem = require Yii::getPathOfAlias($alias).'/menuItem.php';
        
		$auth = Yii::app()->authManager;
		$role = $auth->getAuthAssignments(Yii::app()->user->id);
        $hasTask = $auth->getItemChildren(array_keys($role));
        
        $menus = array();
        foreach ($menuItem as $mKey=>$mItem){
            if (in_array($mKey, array_keys($hasTask))){
                foreach ($mItem as $menuKey=>$menu){
                    $menus[$menuKey] = $menu;
                }
            }
        }
        
		$this->render('list', array(
            'menuCategory'=>$menuCategory,
            'menus'=>$menus,
        ));
	}
}
