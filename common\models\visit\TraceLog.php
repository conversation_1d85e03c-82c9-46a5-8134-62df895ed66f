<?php

/**
 * This is the model class for table "ivy_visits_trace_log".
 *
 * The followings are the available columns in table 'ivy_visits_trace_log':
 * @property integer $id
 * @property integer $basic_id
 * @property integer $visit_id
 * @property integer $type
 * @property string $content
 * @property string $schoolid
 * @property integer $status
 * @property integer $update_timestamp
 * @property integer $update_user
 */
class TraceLog extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_visits_trace_log';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('type', 'required'),
			array('basic_id, visit_id, type, status, update_timestamp, update_user', 'numerical', 'integerOnly'=>true),
			array('schoolid', 'length', 'max'=>25),
			array('content', 'safe'),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, basic_id, visit_id, type, content, schoolid, status, update_timestamp, update_user', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'basic_id' => 'Basic',
			'visit_id' => 'Visit',
			'type' => 'Type',
			'content' => 'Content',
			'schoolid' => 'Schoolid',
			'status' => 'Status',
			'update_timestamp' => 'Update Timestamp',
			'update_user' => 'Update User',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('basic_id',$this->basic_id);
		$criteria->compare('visit_id',$this->visit_id);
		$criteria->compare('type',$this->type);
		$criteria->compare('content',$this->content,true);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('status',$this->status);
		$criteria->compare('update_timestamp',$this->update_timestamp);
		$criteria->compare('update_user',$this->update_user);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return TraceLog the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}
