<div>
    <div class='bgGrey m24' style='padding:10px'>
        <div class='flex align-items'>
            <div class='flex1'>
                <div class='color3 fontBold mb10 font14'>No.{{contentList.behavior.id}}</div>
                <div class='flex align-items'>
                <img :src="contentList.staff_info_list[contentList.behavior.created_by].photoUrl" data-holder-rendered="true" class="avatar32">
                <span class='color3 font14 ml8'>{{contentList.staff_info_list[contentList.behavior.created_by].name}} <el-divider direction="vertical"></el-divider> <span class=''>{{contentList.behavior.created_date}}</span></span>
                </div>
            </div>
            <button class="btn btn-primary"  @click='allSendMessage()'>
                <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/share.png' ?>" alt="" width='16' height='16'>
                <?php echo Yii::t("referral", "Share To"); ?>
            </button>
        </div>
    </div>  
    <div class='relative row p24'>
        <span class='borderLeftpos' ></span>
        <div class='overflow-y scroll-box col-md-6 col-sm-6  pl24 pb24 pr24' :style="'max-height:'+(height-100)+'px;overflow-x: hidden;'" >
            <div class='font14'>      
                <div class='color3 fontBold'><?php echo Yii::t("referral", "Description"); ?></div>    
                <div class='mt16'>
                    <div class='flex'>
                        <div class='iconWidth'><img class='imgIcon' src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/student_icon.png' ?>" alt=""></div>
                        <div class='flex1'>
                            <div class="media mb12" v-for='(list,index) in contentList.behavior.child'>
                                <div class="media-left pull-left media-middle relative" v-if='contentList.student_info_list[list.id]'>
                                    <img :src="contentList.student_info_list[list.id].avatar" data-holder-rendered="true" class="avatar">
                                </div> 
                                <div class="pull-left media-middle">
                                    <div class="mt4 color3">{{contentList.student_info_list[list.id].name}} 
                                        <span v-if='list.tag==2 || list.tag==3' class='glyphicon glyphicon-bookmark font12' :class='list.tag==2?"waitingColor":list.tag==3?"colorRed":""'></span>
                                    </div> 
                                    <div class="color6 font12">{{contentList.student_info_list[list.id].className}}</div>
                                    <div class='bluebg font12 cur-p' @click='viewAffirm(list)' v-if='list.affirm_num>0'><?php echo Yii::t("reg", "Total "); ?>{{list.affirm_num}}<?php echo Yii::t("reg", " "); ?> <span class='el-icon-arrow-right'></span></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>  
                <div class='flex mt12 mb16'>
                    <div class='iconWidth'><img class='imgIcon' src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/time_icon.png' ?>" alt=""></div>
                    <div class='color3  flex1'>{{detail.date}} {{detail.time}}</div>
                </div>
                <div class='flex mt12 mb16' v-if='detail.location.length!=0 || (detail.location_other!="" && detail.location_other!=null)'>
                    <div class='iconWidth'><img class='imgIcon' src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/location_icon.png' ?>" alt=""></div>
                    <div class='color3  flex1'>
                        <div v-if='detail.location.length!=0 || (detail.location_other!="" && detail.location_other!=null)'>
                            <span v-for='(list,index) in detail.location'>{{showTitle(list,'location')}}<span v-if='index+1<detail.location.length'>; </span></span> 
                            <span v-if='detail.location_other!="" && detail.location_other!=null'>{{detail.location_other}} （<?php echo Yii::t("report", "Other"); ?>）</span>
                        </div>
                        <div v-else>
                            <span class='font14 color6'><?php echo Yii::t("global", "N/A"); ?></span>
                        </div>
                    </div>
                </div>
                <div class='flex mt12 mb16'>
                    <div class='iconWidth'><img class='imgIcon' src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/desc_icon.png' ?>" alt=""></div>
                    <div class='color3  flex1' v-if='contentList.behavior.detail.subtype=="POSITIVEBEHAVIOR"'>
                        <div class='bluebg font12 relative mb24'>
                            <img  class='praise1Png'  src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/praise1.png' ?>" alt="">
                            <span class='PositiveBehavior'><?php echo Yii::t("behavior", "Positive Behavior"); ?>  </span>
                        </div>
                        <div class='clearfix'></div>
                        <div class='mt12' v-html='htmlBr(contentList.behavior.detail.desc)'></div>
                    </div>
                    <div class='color3  flex1' v-else>
                        <span v-html='htmlBr(contentList.behavior.detail.desc)'></span>
                    </div>
                </div>
                <div class='flex mt12' v-if='contentList.behavior.detail.attachments.other || contentList.behavior.detail.attachments.img'>
                    <div class='iconWidth'><img class='imgIcon' src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/file_icon.png' ?>" alt=""></div>
                    
                    <div class='color3 flex1'>
                        <div class=''   v-if='contentList.behavior.detail.attachments.img'>
                            <ul class='mb12 imgLi'  id='imgList' v-if='Object.values(contentList.attachmentList).length>0'>
                                <li v-for='(list,i) in contentList.behavior.detail.attachments.img'>
                                    <img :src="contentList.attachmentList[list].file_key" v-if='contentList.attachmentList[list]'  class='imgList mr8' @click='showImg("imgList",contentList.behavior.detail.attachments.img)' alt=""  >
                                </li>
                            </ul>
                            <div v-else><?php echo Yii::t("referral", "Attachments Error"); ?></div>
                        </div>
                        <div class='mt12 color3' v-if='contentList.behavior.detail.attachments.other'>
                            <div v-for='(list,j) in contentList.behavior.detail.attachments.other'>
                                <div class='flex fileLink' v-if='contentList.attachmentList[list]' >
                                    <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/PDF1.png' ?>" alt="" class="pdfImg" v-if='contentList.attachmentList[list].mime_type=="application/pdf"' />
                                    <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/excel.png' ?>" alt="" class="pdfImg" v-else-if='contentList.attachmentList[list].mime_type=="application/vnd.ms-excel" || contentList.attachmentList[list].mime_type=="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"' />
                                    <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/word1.png' ?>" alt="" class="pdfImg" v-else-if='contentList.attachmentList[list].mime_type=="application/msword" || contentList.attachmentList[list].mime_type=="application/vnd.openxmlformats-officedocument.wordprocessingml.document"' />
                                    <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/zip.png' ?>" alt="" class="pdfImg" v-else-if='contentList.attachmentList[list].mime_type=="application/x-zip-compressed"' />
                                    <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/link.png' ?>" alt="" class="pdfImg" v-else /> 
                                    <a class='flex1 ml5 flieEllipsis' target= "_blank" :href="contentList.attachmentList[list].file_key">{{contentList.attachmentList[list].title}}
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div v-if='detail.assist && detail.assist.length>0'>
                    <div class='color3 fontBold mt24'><?php echo Yii::t("referral", "Collaborators"); ?></div>
                    <div  class='color3 '>
                        <div v-for='(list,index) in detail.assist'>
                            <div class='color3 mt12'>{{list.class_name}}</div>
                            <div class='bgGrey mt8'><span v-for='(item,id) in list.teacher_ids' class='mr24 color6 line20'>{{contentList.staff_info_list[item].name}}</span></div>
                        </div>
                    </div>
                </div>
                <div class='color3 fontBold mt24' v-if='detail.subtype && detail.subtype!=""'><?php echo Yii::t("referral", "Type"); ?></div>
                <div v-if='detail.subtype && detail.subtype!=""'>
                    <div class='color3 mt12'><span class='color6'>{{showTitle(detail.subtype,'subtype')}}</span> </div>
                </div>
                <div  v-if='contentList.behavior.detail.subtype!="POSITIVEBEHAVIOR"'>
                <div v-if='detail.subject && detail.subject!=""'>
                    <div class='color3 fontBold mt24' ><?php echo Yii::t("application", "Subject"); ?></div>
                    <div >
                        <div class='color3 mt12'><span class='color6'>{{showTitle(detail.subject,'subject')}}</span> </div>
                    </div>
                </div>
                <div v-if='detail.behavioral_sub && detail.behavioral_sub!=""'>
                    <div class='color3 fontBold mt24' ><?php echo Yii::t("referral", "Special Tracking"); ?></div>
                    <div >
                        <div class='color3 mt12'><span class='color6'>{{showTitle(detail.behavioral_sub,'behavioral_sub')}}</span> </div>
                    </div>
                </div>
                <div class='color3 fontBold mt24'><?php echo Yii::t("referral", "Core value that was not met or violated"); ?></div>
                <div v-if='detail.violated.length>0'>
                    <div class='color3 mt8'><span  v-for='(list,index) in detail.violated' class='color6'>{{showTitle(list,'violated')}}<span v-if='index+1<detail.violated.length'>; </span></span> </div>
                </div>
                <div v-else class='mt8 font14 color6'><?php echo Yii::t("global", "N/A"); ?></div>
                <div class='color3 fontBold mt24'><?php echo Yii::t("referral", "Problem Behavior"); ?></div>
                <div  class='color3 mb16'>
                    <div v-if='detail.problem_behavior.length>0 || (detail.problem_behavior_other!="" && detail.problem_behavior_other!=null)'>
                        <div class='mt8 color6' v-for='(list,index) in detail.problem_behavior'><span v-if='detail.problem_behavior.length>1'>{{index+1}}.</span> {{showTitle(list,'problem_behavior')}}</div>
                        <div class='mt8 color6' v-if='detail.problem_behavior_other!="" && detail.problem_behavior_other!=null'>{{detail.problem_behavior.length+1}}. {{detail.problem_behavior_other}}（<?php echo Yii::t("report", "Other"); ?>）</div>
                    </div>
                    <div v-else class='font14 color6'><?php echo Yii::t("global", "N/A"); ?></div>
                </div>
                <div class='color3 fontBold mt24'><?php echo Yii::t("referral", "What previous steps have you taken to address this behavior"); ?></div>
                <div  class='color3 mb16'>
                    <div v-if='detail.intervene.length>0 || (detail.intervene_other!="" && detail.intervene_other!=null)'>
                        <div class='mt8 color6' v-for='(list,index) in detail.intervene'><span v-if='detail.intervene.length>1'>{{index+1}}.</span> {{showTitle(list,'intervene')}}</div>
                        <div class='mt8 color6' v-if='detail.intervene_other!="" && detail.intervene_other!=null'>{{detail.intervene.length+1}}. {{detail.intervene_other}}（<?php echo Yii::t("report", "Other"); ?>）</div>
                    </div>
                    <div v-else class='font14 color6'><?php echo Yii::t("global", "N/A"); ?></div>
                </div>
                <div class='color3 fontBold mt24'><?php echo Yii::t("referral", "Possible Motivation"); ?></div>
                <div  class='color3 mb16'>
                    <div v-if='detail.motivation.length>0 || (detail.motivation_other!="" && detail.motivation_other!=null)'>
                        <div class='mt8 color6' v-for='(list,index) in detail.motivation'><span v-if='detail.motivation.length>1'>{{index+1}}.</span> {{showTitle(list,'motivation')}}</div>
                        <div class='mt8 color6' v-if='detail.motivation_other!="" && detail.motivation_other!=null'>{{detail.motivation.length+1}}. {{detail.motivation_other}}（<?php echo Yii::t("report", "Other"); ?>）</div>
                    </div>
                    <div v-else class='font14 color6'><?php echo Yii::t("global", "N/A"); ?></div>
                </div>      
                </div>                      
            </div>
        </div>
        <div class='overflow-y scroll-box col-md-6 col-sm-6 font14 p0  pl24' :style="'max-height:'+(height-100)+'px'">
            <div class='font14'>
                <div><i class='el-icon-s-check bluebg font18'></i><span class='font16 color3 ml8 fontBold'><?php echo Yii::t("referral", "Followup Log"); ?></span></div>
                <div v-for='(list,index) in contentList.behavior.processed_list'>
                    <div v-if='list.node=="ISP"'>
                        <div class='flex mt24 '  v-if='list.result.finally==0'>
                            <div class='relative'>
                                <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/ISP.png' ?>" alt="" class='avatar24'>
                                <div class='stepLeft'></div>
                            </div>
                            <div class='flex1 ml8 pr24'> 
                                <div class='color3 fontBold font14 mt4'><?php echo Yii::t("referral", "Support Group"); ?></div>
                                <div class='flex align-items mt12'>
                                    <div class='flex1'>
                                        <img :src="contentList.staff_info_list[list.updated_by].photoUrl" data-holder-rendered="true" class="avatar32">
                                        <span class='font14 fontBold ml10'>{{contentList.staff_info_list[list.updated_by].name}}</span>
                                    </div>
                                    <div class='color6'>{{list.updated_at}}</div>
                                </div>
                                <div class='flex mt12' v-if='list.result.remark!=""'>
                                    <span class='color6 font14'><?php echo Yii::t("referral", "Comment"); ?>：</span>
                                    <div class='flex1 color3' v-html='htmlBr(list.result.remark)'></div>
                                </div>
                                <div class='flex mt12' v-if="list.result.to_admin == 1">
                                    <span class='color6 font14'><?php echo Yii::t("referral", "Followup"); ?>：</span>
                                    <div class="color3 flex1">
                                        <?php echo Yii::t("referral", "Transfer to Principal Office"); ?>
                                    </div>
                                </div>
                                <div class='flex mt12' v-if="list.result.to_gradelead == 1">
                                    <span class='color6 font14'><?php echo Yii::t("referral", "Followup"); ?>：</span>
                                    <div class="color3 flex1">
                                        <?php echo Yii::t("referral", "Transfer to Head of Grade"); ?>
                                    </div>
                                </div>
                                <div v-if='list.result.to_subjectlead==1'>
                                        <div class='flex mt12'>
                                            <span class='color6 font14'><?php echo Yii::t("referral", "Followup"); ?>：</span>
                                            <div class='flex1 color3'><?php echo Yii::t("referral", "Transfer to Head of Department"); ?></div>
                                        </div>
                                        <div class='flex mt12' v-if='list.result.subject'>
                                            <span class='color6 font14'><?php echo Yii::t("application", "Subject"); ?>：</span>
                                            <div class='flex1 color3'>{{showTitle(list.result.subject,'subject')}}</div>
                                        </div>
                                    </div>
                                <div class='flex mt12' v-if='list.result.attachments.img || list.result.attachments.other'>
                                    <span class='color6 font14'><?php echo Yii::t("curriculum", "Attachments"); ?>：</span>
                                    <div class='flex1 color3'>
                                        <div class='' v-if='list.result.attachments.img'>
                                            <ul class='mb12 imgLi'  id='isp' v-if='Object.values(contentList.attachmentList).length>0'>
                                                <li v-for='(item,i) in list.result.attachments.img'>
                                                    <img :src="contentList.attachmentList[item].file_key" v-if='contentList.attachmentList[item]' class='imgList mr8 mb8' @click='showImg("isp",list.result.attachments.img)' alt=""  >
                                                </li>
                                            </ul>
                                            <div v-else><?php echo Yii::t("referral", "Attachments Error"); ?></div>
                                        </div>
                                        <div class='color3'>
                                            <div v-for='(item,j) in list.result.attachments.other'>
                                                <div class='flex fileLink' v-if='contentList.attachmentList[item]'>
                                                    <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/PDF1.png' ?>" alt="" class="pdfImg" v-if='contentList.attachmentList[item].mime_type=="application/pdf"' />
                                                    <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/excel.png' ?>" alt="" class="pdfImg" v-else-if='contentList.attachmentList[item].mime_type=="application/vnd.ms-excel" || contentList.attachmentList[item].mime_type=="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"' />
                                                    <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/word1.png' ?>" alt="" class="pdfImg" v-else-if='contentList.attachmentList[item].mime_type=="application/msword" || contentList.attachmentList[item].mime_type=="application/vnd.openxmlformats-officedocument.wordprocessingml.document"' />
                                                    <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/zip.png' ?>" alt="" class="pdfImg" v-else-if='contentList.attachmentList[item].mime_type=="application/x-zip-compressed"' />
                                                    <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/link.png' ?>" alt="" class="pdfImg" v-else /> 
                                                    <a class='flex1 ml5 flieEllipsis' target= "_blank" :href="contentList.attachmentList[item].file_key">{{contentList.attachmentList[item].title}}
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div v-if='list.result.finally==1'>   
                            <div class='flex mt24 '>
                                <div class='relative'>
                                    <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/result.png' ?>" alt="" class='avatar24'>
                                    <div class='stepLeft'></div>
                                </div>
                                <div class='flex1 ml8 font14 pr24'>                                                    
                                    <div class='flex mt4'>
                                        <span class='fontBold color3 flex1'><?php echo Yii::t("referral", "Final Decision"); ?></span>
                                        <?php if($type != 'myList'):?>
                                        <el-button type="text" icon="el-icon-circle-plus-outline" v-if='contentList.afresh_affirm'  @click='addReaff()'class='font14 bluebg p0'><?php echo Yii::t("referral", "Add other student"); ?></el-button>

                                        <!-- <span class='bluebg cur-p' @click='addReaff()'>添加学生</span> -->
                                        <?php endif;?>
                                    </div>
                                    <div v-for='(item,idx) in contentList.finally_data' class='bgGrey presenter mt12 relative'>
                                        <span class='NOID'>No.{{item.id}}</span>
                                        <div class='flex align-items mt15'>
                                            <span class='color6 font14'><?php echo Yii::t("ptc", "Student"); ?>：</span>
                                            <div class='flex1 color3'>
                                                <div class="media">
                                                    <div class="media-left pull-left media-middle relative" v-if='contentList.student_info_list[item.child.id]'>
                                                        <img :src="contentList.student_info_list[item.child.id].avatar" data-holder-rendered="true" class="avatar">
                                                    </div> 
                                                    <div class="pull-left media-middle">
                                                        <div class="mt4 color3">{{contentList.student_info_list[item.child.id].name}} 
                                                            <span v-if='item.child.tag==2 || item.child.tag==3' class='glyphicon glyphicon-bookmark font12' :class='item.child.tag==2?"waitingColor":item.child.tag==3?"colorRed":""'></span>
                                                            <!-- <span :class='item.child.tag==2?"tagLabelYellow":item.child.tag==3?"tagLabel":""'>{{showTitle(item.child.tag,'ISP_type')}}</span> -->
                                                        </div> 
                                                        <div class="color6 font12">{{contentList.student_info_list[item.child.id].className}}</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class='flex mt12'>
                                            <span class='color6 font14'><?php echo Yii::t("referral", "Decision"); ?>：</span>
                                            <div class='flex1 color3'><span class="label fontWeightNormal" :class='item.result.identified==1?"label-danger":"label-success"'>{{showTitle(item.result.identified,'fact')}}</span></div>
                                        </div>
                                        <div class='flex mt12'>
                                            <span class='color6 font14'><?php echo Yii::t("referral", "Time in Office"); ?>：</span>
                                            <div class='flex1 color3'>{{item.result.duration}}</div>
                                        </div>
                                        <div class='flex mt12'>
                                            <span class='color6 font14'><?php echo Yii::t("referral", "Final Result"); ?>：</span>
                                            <div class='flex1 color3' >
                                                <span class='mr10' v-for='(list,index) in item.result.actions'>{{showTitle(list,'actions')}}</span>
                                                <?php if($type != 'myList'):?>
                                                    <span v-if='showFollow(item.result.actions)'>
                                                        <span class='btn-link' @click='follow(item,"show")' v-if='item.followup.date'>{{item.followup.date[0]}} ~ {{item.followup.date[1]}} <span class='el-icon-arrow-right'></span></span>
                                                        <span class='btn-link' @click='follow(item,"edit")' v-else><?php echo Yii::t("referral", "Detail"); ?></span>
                                                    </span>
                                                <?php endif;?>
                                                <?php if($type == 'myList'):?>
                                                    <span class='btn-link' @click='follow(item,"show")' v-if='item.followup.date'>{{item.followup.date[0]}} ~ {{item.followup.date[1]}} <span class='el-icon-arrow-right'></span></span>
                                                    </span>
                                                <?php endif;?>
                                            </div>
                                        </div>
                                        <div class='flex mt12'>
                                            <span class='color6 font14'><?php echo Yii::t("referral", "Contact parents"); ?>：</span>
                                            <div class='flex1 color3'><span class='mr10' v-for='(list,index) in item.result.contact_parents'>{{showTitle(list,'contact_parents')}}</span></div>
                                        </div> 
                                        <div class='flex mt12' v-if='item.result.remark!=""'>
                                            <span class='color6 font14'><?php echo Yii::t("referral", "Comment"); ?>：</span>
                                            <div class='flex1 color3' v-html='htmlBr(item.result.remark)'></div>
                                        </div>
                                        <div class='flex mt12' v-if='item.result.attachments.img || item.result.attachments.other'>
                                            <span class='color6 font14'><?php echo Yii::t("curriculum", "Attachments"); ?>：</span>
                                            <div class='flex1 color3'>
                                                <div class=''  v-if='item.result.attachments.img'>
                                                    <ul class='mb12 imgLi'  id='to_admin'  v-if='Object.values(contentList.attachmentList).length>0'>
                                                        <li v-for='(list,i) in item.result.attachments.img'>
                                                            <img :src="contentList.attachmentList[list].file_key" v-if='contentList.attachmentList[list]' class='imgList mb8 mr8' @click='showImg("to_admin",item.result.attachments.img)' alt=""  >
                                                        </li>
                                                    </ul>
                                                    <div v-else><?php echo Yii::t("referral", "Attachments Error"); ?></div>
                                                </div>
                                                <div class='color3' v-if='item.result.attachments.other'>
                                                    <div v-for='(list,j) in item.result.attachments.other'>
                                                        <div class='flex fileLink' v-if='contentList.attachmentList[list]'>
                                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/PDF1.png' ?>" alt="" class="pdfImg" v-if='contentList.attachmentList[list].mime_type=="application/pdf"' />
                                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/excel.png' ?>" alt="" class="pdfImg" v-else-if='contentList.attachmentList[list].mime_type=="application/vnd.ms-excel" || contentList.attachmentList[list].mime_type=="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"' />
                                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/word1.png' ?>" alt="" class="pdfImg" v-else-if='contentList.attachmentList[list].mime_type=="application/msword" || contentList.attachmentList[list].mime_type=="application/vnd.openxmlformats-officedocument.wordprocessingml.document"' />
                                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/zip.png' ?>" alt="" class="pdfImg" v-else-if='contentList.attachmentList[list].mime_type=="application/x-zip-compressed"' />
                                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/link.png' ?>" alt="" class="pdfImg" v-else /> 
                                                            <a class='flex1 ml5 flieEllipsis' target= "_blank" :href="contentList.attachmentList[list].file_key">{{contentList.attachmentList[list].title}}
                                                            </a>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class='flex mt12 align-items '>
                                            <span class='color6 font14'><?php echo Yii::t("referral", "By"); ?>：</span>
                                            <div class='flex1 color3'>
                                                <div class='flex align-items '>
                                                    <img :src="contentList.staff_info_list[item.updated_by].photoUrl" data-holder-rendered="true" class="avatar32">
                                                    <span class='ml10'>{{contentList.staff_info_list[item.updated_by].name}}</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class='flex mt12'>
                                            <span class='color6 font14'><?php echo Yii::t("ptc", "Time"); ?>：</span>
                                            <div class='flex1 color3'>{{item.updated_at}}</div>
                                        </div>
                                        <?php if($type != 'myList'):?>
                                        <div class='flex mt16'>
                                            <div class='flex1'>
                                            </div>
                                            <button class="btn btn-primary"  @click='reaffirmation(item,"isp")' v-if='contentList.afresh_affirm' ><?php echo Yii::t("referral", "Reset"); ?></button>
                                            <!-- <div class="btn-group" v-if='item.send_log && item.send_log.length>0'>
                                                <button type="button" class="btn btn-primary"><?php echo Yii::t("referral", "Notify To"); ?></button>
                                                <button type="button" class="btn btn-primary dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                    <span class="caret"></span>
                                                    <span class="sr-only">Toggle Dropdown</span>
                                                </button>
                                                <ul class="dropdown-menu dropdownLeft">
                                                    <li><a href="javascript:;" @click='sendMessage(item)'><?php echo Yii::t("referral", "Send Notify"); ?></a></li>
                                                    <li v-if='item.send_log && item.send_log.length>0'><a href="javascript:;" @click='viewSendMessage(item)'><?php echo Yii::t("referral", "All notification logs"); ?></a></li>
                                                </ul>
                                            </div>
                                            <div v-else>
                                                <button class="btn btn-primary"  @click='sendMessage(item)'><?php echo Yii::t("referral", "Send Notify"); ?></button>
                                            </div> -->
                                            <!-- <button class="btn btn-primary"  @click='allSendMessage(item)'><?php echo Yii::t("referral", "Notify To"); ?></button> -->
                                        </div>
                                        <?php endif;?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div v-if='list.node=="ADMIN"'>
                        <div v-if='list.status==1'>
                            <div v-if='list.result.to_isp==1'>
                                <div class='flex mt24 '>
                                    <div class='relative'>
                                        <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/office.png' ?>" alt="" class='avatar24'>
                                        <div class='stepLeft'></div>
                                    </div>
                                    <div class='flex1 ml8 font14 pr24'>                                                    
                                        <div class='fontBold color3 mt4'><?php echo Yii::t("referral", "Principal Office"); ?></div>
                                        <div class='flex align-items mt12'>
                                            <div class='flex1'>
                                                <img :src="contentList.staff_info_list[list.updated_by].photoUrl" data-holder-rendered="true" class="avatar32">
                                                <span  class='font14 fontBold ml10'>{{contentList.staff_info_list[list.updated_by].name}}</span>
                                            </div>
                                            <div class='color6'>{{list.updated_at}}</div>
                                        </div>
                                        <div class='flex mt12' v-if='list.result.remark!=""'>
                                            <span class='color6 font14'><?php echo Yii::t("referral", "Comment"); ?>：</span>
                                            <div class='flex1 color3' v-html='htmlBr(list.result.remark)'></div>
                                        </div>
                                        <div class='flex mt12'>
                                            <span class='color6 font14'><?php echo Yii::t("referral", "Followup"); ?>：</span>
                                            <div class='flex1 color3'><?php echo Yii::t("referral", "Transfer to Support Groups"); ?></div>
                                        </div>
                                        <div class='flex mt12'>
                                            <span class='color6 font14'><?php echo Yii::t("referral", "Support Type"); ?>：</span>
                                            <div class='flex1 color3'>{{showTitle(list.result.help_type,'ISP_help')}}</div>
                                        </div>
                                        <div class='flex mt12'>
                                            <span class='color6 font14'><?php echo Yii::t("referral", "Support Group"); ?>：</span>
                                            <div class='flex1 color3'>{{showTitle(list.result.support,'ISP_type')}}</div>
                                        </div>
                                        <div class='flex mt12' v-if='list.result.assign_to'>
                                            <span class='color6 font14'><?php echo Yii::t("referral", "Assign to"); ?>：</span>
                                            <div class='flex1 color3'>{{contentList.staff_info_list[list.result.assign_to].name}}</div>
                                        </div>
                                        <div class='flex mt12'  v-if='list.result.attachments.img || list.result.attachments.other'>
                                            <span class='color6 font14'><?php echo Yii::t("curriculum", "Attachments"); ?>：</span>
                                            <div class='flex1 color3'>
                                                <div class='' v-if='list.result.attachments.img'>
                                                    <ul class='mb12 imgLi'  id='admin' v-if='Object.values(contentList.attachmentList).length>0' >
                                                        <li v-for='(item,i) in list.result.attachments.img'>
                                                            <img :src="contentList.attachmentList[item].file_key" v-if='contentList.attachmentList[item]' class='imgList mr8 mb8' @click='showImg("admin",list.result.attachments.img)' alt=""  >
                                                        </li>
                                                    </ul>
                                                    <div v-else><?php echo Yii::t("referral", "Attachments Error"); ?></div>
                                                </div>
                                                <div class='color3'>
                                                    <div v-for='(item,j) in list.result.attachments.other'>
                                                        <div class='flex fileLink' v-if='contentList.attachmentList[item]'>
                                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/PDF1.png' ?>" alt="" class="pdfImg" v-if='contentList.attachmentList[item].mime_type=="application/pdf"' />
                                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/excel.png' ?>" alt="" class="pdfImg" v-else-if='contentList.attachmentList[item].mime_type=="application/vnd.ms-excel" || contentList.attachmentList[item].mime_type=="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"' />
                                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/word1.png' ?>" alt="" class="pdfImg" v-else-if='contentList.attachmentList[item].mime_type=="application/msword" || contentList.attachmentList[item].mime_type=="application/vnd.openxmlformats-officedocument.wordprocessingml.document"' />
                                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/zip.png' ?>" alt="" class="pdfImg" v-else-if='contentList.attachmentList[item].mime_type=="application/x-zip-compressed"' />
                                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/link.png' ?>" alt="" class="pdfImg" v-else /> 
                                                            <a class='flex1 ml5 flieEllipsis' target= "_blank" :href="contentList.attachmentList[item].file_key">{{contentList.attachmentList[item].title}}
                                                            </a>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>  
                                </div>
                            </div>
                            <div v-if='list.result.to_gradelead==1'>
                                <div class='flex mt24 '>
                                    <div class='relative'>
                                        <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/office.png' ?>" alt="" class='avatar24'>
                                        <div class='stepLeft'></div>
                                    </div>
                                    <div class='flex1 ml8 font14 pr24'>                                                    
                                        <div class='fontBold color3 mt4'><?php echo Yii::t("referral", "Principal Office"); ?></div>
                                        <div class='flex align-items mt12'>
                                            <div class='flex1'>
                                                <img :src="contentList.staff_info_list[list.updated_by].photoUrl" data-holder-rendered="true" class="avatar32">
                                                <span  class='font14 fontBold ml10'>{{contentList.staff_info_list[list.updated_by].name}}</span>
                                            </div>
                                            <div class='color6'>{{list.updated_at}}</div>
                                        </div>
                                        <div class='flex mt12' v-if='list.result.remark!=""'>
                                            <span class='color6 font14'><?php echo Yii::t("referral", "Comment"); ?>：</span>
                                            <div class='flex1 color3' v-html='htmlBr(list.result.remark)'></div>
                                        </div>
                                        <div class='flex mt12'>
                                            <span class='color6 font14'><?php echo Yii::t("referral", "Followup"); ?>：</span>
                                            <div class='flex1 color3'><?php echo Yii::t("referral", "Transfer to Head of Grade"); ?></div>
                                        </div>
                                        <div class='flex mt12' v-if='list.result.assign_to'>
                                            <span class='color6 font14'><?php echo Yii::t("referral", "Assign to"); ?>：</span>
                                            <div class='flex1 color3'>{{contentList.staff_info_list[list.result.assign_to].name}}</div>
                                        </div>
                                        <div class='flex mt12'  v-if='list.result.attachments.img || list.result.attachments.other'>
                                            <span class='color6 font14'><?php echo Yii::t("curriculum", "Attachments"); ?>：</span>
                                            <div class='flex1 color3'>
                                                <div class='' v-if='list.result.attachments.img'>
                                                    <ul class='mb12 imgLi'  id='admin' v-if='Object.values(contentList.attachmentList).length>0' >
                                                        <li v-for='(item,i) in list.result.attachments.img'>
                                                            <img :src="contentList.attachmentList[item].file_key" v-if='contentList.attachmentList[item]' class='imgList mr8 mb8' @click='showImg("admin",list.result.attachments.img)' alt=""  >
                                                        </li>
                                                    </ul>
                                                    <div v-else><?php echo Yii::t("referral", "Attachments Error"); ?></div>
                                                </div>
                                                <div class='color3'>
                                                    <div v-for='(item,j) in list.result.attachments.other'>
                                                        <div class='flex fileLink'  v-if='contentList.attachmentList[item]' >
                                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/PDF1.png' ?>" alt="" class="pdfImg" v-if='contentList.attachmentList[item].mime_type=="application/pdf"' />
                                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/excel.png' ?>" alt="" class="pdfImg" v-else-if='contentList.attachmentList[item].mime_type=="application/vnd.ms-excel" || contentList.attachmentList[item].mime_type=="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"' />
                                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/word1.png' ?>" alt="" class="pdfImg" v-else-if='contentList.attachmentList[item].mime_type=="application/msword" || contentList.attachmentList[item].mime_type=="application/vnd.openxmlformats-officedocument.wordprocessingml.document"' />
                                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/zip.png' ?>" alt="" class="pdfImg" v-else-if='contentList.attachmentList[item].mime_type=="application/x-zip-compressed"' />
                                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/link.png' ?>" alt="" class="pdfImg" v-else /> 
                                                            <a class='flex1 ml5 flieEllipsis' target= "_blank" :href="contentList.attachmentList[item].file_key">{{contentList.attachmentList[item].title}}
                                                            </a>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>  
                                </div>
                            </div>
                            <div v-if='list.result.to_subjectlead==1'>
                                <div class='flex mt24 '>
                                    <div class='relative'>
                                        <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/office.png' ?>" alt="" class='avatar24'>
                                        <div class='stepLeft'></div>
                                    </div>
                                    <div class='flex1 ml8 font14 pr24'>                                                    
                                        <div class='fontBold color3 mt4'><?php echo Yii::t("referral", "Principal Office"); ?></div>
                                        <div class='flex align-items mt12'>
                                            <div class='flex1'>
                                                <img :src="contentList.staff_info_list[list.updated_by].photoUrl" data-holder-rendered="true" class="avatar32">
                                                <span  class='font14 fontBold ml10'>{{contentList.staff_info_list[list.updated_by].name}}</span>
                                            </div>
                                            <div class='color6'>{{list.updated_at}}</div>
                                        </div>
                                        <div class='flex mt12' v-if='list.result.remark!=""'>
                                            <span class='color6 font14'><?php echo Yii::t("referral", "Comment"); ?>：</span>
                                            <div class='flex1 color3' v-html='htmlBr(list.result.remark)'></div>
                                        </div>
                                        <div class='flex mt12'>
                                            <span class='color6 font14'><?php echo Yii::t("referral", "Followup"); ?>：</span>
                                            <div class='flex1 color3'><?php echo Yii::t("referral", "Transfer to Head of Department"); ?></div>
                                        </div>
                                        <div class='flex mt12' v-if='list.result.subject'>
                                            <span class='color6 font14'><?php echo Yii::t("application", "Subject"); ?>：</span>
                                            <div class='flex1 color3'>{{showTitle(list.result.subject,'subject')}}</div>
                                        </div>
                                        <div class='flex mt12'  v-if='list.result.attachments.img || list.result.attachments.other'>
                                            <span class='color6 font14'><?php echo Yii::t("curriculum", "Attachments"); ?>：</span>
                                            <div class='flex1 color3'>
                                                <div class=''  v-if='list.result.attachments.img'>
                                                    <ul class='mb12 imgLi'  id='admin'  v-if='Object.values(contentList.attachmentList).length>0'>
                                                        <li v-for='(item,i) in list.result.attachments.img'>
                                                            <img :src="contentList.attachmentList[item].file_key"  v-if='contentList.attachmentList[item]' class='imgList mr8 mb8' @click='showImg("admin",list.result.attachments.img)' alt=""  >
                                                        </li>
                                                    </ul>
                                                    <div v-else><?php echo Yii::t("referral", "Attachments Error"); ?></div>
                                                </div>
                                                <div class='color3'>
                                                    <div v-for='(item,j) in list.result.attachments.other'>
                                                        <div class='flex fileLink' v-if='contentList.attachmentList[item]'  >
                                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/PDF1.png' ?>" alt="" class="pdfImg" v-if='contentList.attachmentList[item].mime_type=="application/pdf"' />
                                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/excel.png' ?>" alt="" class="pdfImg" v-else-if='contentList.attachmentList[item].mime_type=="application/vnd.ms-excel" || contentList.attachmentList[item].mime_type=="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"' />
                                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/word1.png' ?>" alt="" class="pdfImg" v-else-if='contentList.attachmentList[item].mime_type=="application/msword" || contentList.attachmentList[item].mime_type=="application/vnd.openxmlformats-officedocument.wordprocessingml.document"' />
                                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/zip.png' ?>" alt="" class="pdfImg" v-else-if='contentList.attachmentList[item].mime_type=="application/x-zip-compressed"' />
                                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/link.png' ?>" alt="" class="pdfImg" v-else /> 
                                                            <a class='flex1 ml5 flieEllipsis' target= "_blank" :href="contentList.attachmentList[item].file_key">{{contentList.attachmentList[item].title}}
                                                            </a>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>  
                                </div>
                            </div>
                            <div v-if='list.result.finally==1'>   
                                <div class='flex mt24 '>
                                    <div class='relative'>
                                        <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/result.png' ?>" alt="" class='avatar24'>
                                        <div class='stepLeft'></div>
                                    </div>
                                    <div class='flex1 ml8 font14 pr24'>                                                    
                                        <div class='flex mt4'>
                                            <span class='fontBold color3 flex1'><?php echo Yii::t("referral", "Final Decision"); ?></span>
                                            <?php if($type != 'myList'):?>
                                        <el-button type="text" icon="el-icon-circle-plus-outline" v-if='contentList.afresh_affirm'   @click='addReaff()'class='font14 bluebg p0'><?php echo Yii::t("referral", "Add other student"); ?></el-button>

                                            <!-- <span class='bluebg cur-p' @click='addReaff()'>添加学生</span> -->
                                            <?php endif;?>
                                        </div>
                                        <div v-for='(item,idx) in contentList.finally_data' class='bgGrey presenter mt12 relative'>
                                            <span class='NOID'>No.{{item.id}}</span>
                                            <div class='flex align-items mt15'>
                                                <span class='color6 font14'><?php echo Yii::t("ptc", "Student"); ?>：</span>
                                                <div class='flex1 color3'>
                                                    <div class="media">
                                                        <div class="media-left pull-left media-middle relative" v-if='contentList.student_info_list[item.child.id]'>
                                                            <img :src="contentList.student_info_list[item.child.id].avatar" data-holder-rendered="true" class="avatar">
                                                        </div> 
                                                        <div class="pull-left media-middle">
                                                            <div class="mt4 color3">{{contentList.student_info_list[item.child.id].name}} 
                                                                <!-- <span :class='item.child.tag==2?"tagLabelYellow":item.child.tag==3?"tagLabel":""'>{{showTitle(item.child.tag,'ISP_type')}}</span> -->
                                                                <span v-if='item.child.tag==2 || item.child.tag==3' class='glyphicon glyphicon-bookmark font12' :class='item.child.tag==2?"waitingColor":item.child.tag==3?"colorRed":""'></span>
                                                            </div> 
                                                            <div class="color6 font12">{{contentList.student_info_list[item.child.id].className}}</div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class='flex mt12'>
                                                <span class='color6 font14'><?php echo Yii::t("referral", "Decision"); ?>：</span>
                                                <div class='flex1 color3'><span class="label fontWeightNormal" :class='item.result.identified==1?"label-danger":"label-success"'>{{showTitle(item.result.identified,'fact')}}</span></div>
                                            </div>
                                            <div class='flex mt12'>
                                                <span class='color6 font14'><?php echo Yii::t("referral", "Time in Office"); ?>：</span>
                                                <div class='flex1 color3'>{{item.result.duration}}</div>
                                            </div>
                                            <div class='flex mt12'>
                                                <span class='color6 font14'><?php echo Yii::t("referral", "Final Result"); ?>：</span>
                                                <div class='flex1 color3' >
                                                    <span class='mr10' v-for='(list,index) in item.result.actions'>{{showTitle(list,'actions')}}</span>
                                                    <?php if($type != 'myList'):?>
                                                        <span v-if='showFollow(item.result.actions)'>
                                                            <span class='btn-link' @click='follow(item,"show")' v-if='item.followup.date'>{{item.followup.date[0]}} ~ {{item.followup.date[1]}} <span class='el-icon-arrow-right'></span></span>
                                                            <span class='btn-link' @click='follow(item,"edit")' v-else><?php echo Yii::t("referral", "Detail"); ?></span>
                                                        </span>
                                                    <?php endif;?>
                                                    <?php if($type == 'myList'):?>
                                                        <span class='btn-link' @click='follow(item,"show")' v-if='item.followup.date'>{{item.followup.date[0]}} ~ {{item.followup.date[1]}} <span class='el-icon-arrow-right'></span></span>
                                                        </span>
                                                    <?php endif;?>
                                                </div>
                                            </div>
                                            <div class='flex mt12'>
                                                <span class='color6 font14'><?php echo Yii::t("referral", "Contact parents"); ?>：</span>
                                                <div class='flex1 color3'><span class='mr10' v-for='(list,index) in item.result.contact_parents'>{{showTitle(list,'contact_parents')}}</span></div>
                                            </div>
                                            <div class='flex mt12' v-if='item.result.remark!=""'>
                                                <span class='color6 font14'><?php echo Yii::t("referral", "Comment"); ?>：</span>
                                                <div class='flex1 color3' v-html='htmlBr(item.result.remark)'></div>
                                            </div>
                                            <div class='flex mt12' v-if='item.result.attachments.img || item.result.attachments.other'>
                                                <span class='color6 font14'><?php echo Yii::t("curriculum", "Attachments"); ?>：</span>
                                                <div class='flex1 color3'>
                                                    <div class='' v-if='item.result.attachments.img'>
                                                        <ul class='mb12 imgLi'  id='finally'   v-if='Object.values(contentList.attachmentList).length>0'>
                                                            <li v-for='(list,i) in item.result.attachments.img'>
                                                                <img :src="contentList.attachmentList[list].file_key"  v-if='contentList.attachmentList[list]'  class='imgList mb8 mr8' @click='showImg("finally",item.result.attachments.img)' alt=""  >
                                                            </li>
                                                        </ul>
                                                        <div v-else><?php echo Yii::t("referral", "Attachments Error"); ?></div>
                                                    </div>
                                                    <div class='color3' v-if='item.result.attachments.other'>
                                                        <div v-for='(list,j) in item.result.attachments.other'>
                                                            <div class='flex fileLink' v-if='contentList.attachmentList[list]'>
                                                                <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/PDF1.png' ?>" alt="" class="pdfImg" v-if='contentList.attachmentList[list].mime_type=="application/pdf"' />
                                                                <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/excel.png' ?>" alt="" class="pdfImg" v-else-if='contentList.attachmentList[list].mime_type=="application/vnd.ms-excel" || contentList.attachmentList[list].mime_type=="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"' />
                                                                <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/word1.png' ?>" alt="" class="pdfImg" v-else-if='contentList.attachmentList[list].mime_type=="application/msword" || contentList.attachmentList[list].mime_type=="application/vnd.openxmlformats-officedocument.wordprocessingml.document"' />
                                                                <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/zip.png' ?>" alt="" class="pdfImg" v-else-if='contentList.attachmentList[list].mime_type=="application/x-zip-compressed"' />
                                                                <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/link.png' ?>" alt="" class="pdfImg" v-else />  
                                                                <a class='flex1 ml5 flieEllipsis' target= "_blank" :href="contentList.attachmentList[list].file_key">{{contentList.attachmentList[list].title}}
                                                                </a>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class='flex mt12 align-items '>
                                                <span class='color6 font14'><?php echo Yii::t("referral", "By"); ?>：</span>
                                                <div class='flex1 color3'>
                                                    <div class='flex align-items '>
                                                        <img :src="contentList.staff_info_list[item.updated_by].photoUrl" data-holder-rendered="true" class="avatar32">
                                                        <span class='ml10'>{{contentList.staff_info_list[item.updated_by].name}}</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class='flex mt12'>
                                                <span class='color6 font14'><?php echo Yii::t("ptc", "Time"); ?>：</span>
                                                <div class='flex1 color3'>{{item.updated_at}}</div>
                                            </div>
                                            <?php if($type != 'myList'):?>
                                            <div class='flex mt16'>
                                                <div class='flex1'>
                                                </div>
                                                <button class="btn btn-primary" type="submit" @click='reaffirmation(item,"admin")' v-if='contentList.afresh_affirm' ><?php echo Yii::t("referral", "Reset"); ?></button>
                                                <!-- <div class="btn-group" v-if='item.send_log && item.send_log.length>0'>
                                                    <button type="button" class="btn btn-primary"><?php echo Yii::t("referral", "Notify To"); ?></button>
                                                    <button type="button" class="btn btn-primary dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                        <span class="caret"></span>
                                                        <span class="sr-only">Toggle Dropdown</span>
                                                    </button>
                                                    <ul class="dropdown-menu dropdownLeft">
                                                        <li><a href="javascript:;" @click='sendMessage(item)'><?php echo Yii::t("referral", "Send Notify"); ?></a></li>
                                                        <li v-if='item.send_log && item.send_log.length>0'><a href="javascript:;" @click='viewSendMessage(item)'><?php echo Yii::t("referral", "All notification logs"); ?></a></li>
                                                    </ul>
                                                </div>
                                                <div v-else>
                                                    <button class="btn btn-primary"  @click='sendMessage(item)'><?php echo Yii::t("referral", "Send Notify"); ?></button>
                                                </div> -->
                                                <!-- <button class="btn btn-primary"  @click='allSendMessage(item)'><?php echo Yii::t("referral", "Notify To"); ?></button> -->
                                            </div>
                                            <?php endif;?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div v-if='list.node=="GRADELEAD"'>
                        <div class='flex mt24 '  v-if='list.result.finally==0'>
                            <div class='relative'>
                                <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/Gradelead.png' ?>"  alt="" class='avatar24'>
                                
                                <div class='stepLeft'></div>
                            </div>
                            <div class='flex1 ml8 pr24'> 
                                <div class='color3 fontBold font14 mt4'><?php echo Yii::t("referral", "Lead of Grade"); ?></div>
                                <div class='flex align-items mt12'>
                                    <div class='flex1'>
                                        <img :src="contentList.staff_info_list[list.updated_by].photoUrl" data-holder-rendered="true" class="avatar32">
                                        <span class='font14 fontBold ml10'>{{contentList.staff_info_list[list.updated_by].name}}</span>
                                    </div>
                                    <div class='color6'>{{list.updated_at}}</div>
                                </div>
                                <div class='flex mt12' v-if='list.result.remark!=""'>
                                    <span class='color6 font14'><?php echo Yii::t("referral", "Comment"); ?>：</span>
                                    <div class='flex1 color3' v-html='htmlBr(list.result.remark)'></div>
                                </div>
                                <div v-if='list.result.to_admin==1'>
                                    <div class='flex mt12'>
                                        <span class='color6 font14'><?php echo Yii::t("referral", "Followup"); ?>：</span>
                                        <div class='flex1 color3'><?php echo Yii::t("referral", "Transfer to Principal Office"); ?></div>
                                    </div>
                                </div>
                                <div v-if='list.result.to_subjectlead==1'>
                                    <div class='flex mt12'>
                                        <span class='color6 font14'><?php echo Yii::t("referral", "Followup"); ?>：</span>
                                        <div class='flex1 color3'><?php echo Yii::t("referral", "Transfer to Head of Department"); ?></div>
                                    </div>
                                    <div class='flex mt12' v-if='list.result.subject'>
                                        <span class='color6 font14'><?php echo Yii::t("application", "Subject"); ?>：</span>
                                        <div class='flex1 color3'>{{showTitle(list.result.subject,'subject')}}</div>
                                    </div>
                                </div>
                                <div v-if='list.result.to_isp==1'>
                                    <div class='flex mt12'>
                                        <span class='color6 font14'><?php echo Yii::t("referral", "Followup"); ?>：</span>
                                        <div class='flex1 color3'><?php echo Yii::t("referral", "Transfer to Support Groups"); ?></div>
                                    </div>
                                    <div class='flex mt12'>
                                        <span class='color6 font14'><?php echo Yii::t("referral", "Support Type"); ?>：</span>
                                        <div class='flex1 color3'>{{showTitle(list.result.help_type,'ISP_help')}}</div>
                                    </div>
                                    <div class='flex mt12'>
                                        <span class='color6 font14'><?php echo Yii::t("referral", "Support Group"); ?>：</span>
                                        <div class='flex1 color3'>{{showTitle(list.result.support,'ISP_type')}}</div>
                                    </div>
                                </div>
                                <div class='flex mt12' v-if='list.result.attachments.img || list.result.attachments.other'>
                                    <span class='color6 font14'><?php echo Yii::t("curriculum", "Attachments"); ?>：</span>
                                    <div class='flex1 color3'>
                                        <div class='' v-if='list.result.attachments.img'>
                                            <ul class='mb12 imgLi'  id='grade'    v-if='Object.values(contentList.attachmentList).length>0'>
                                                <li v-for='(item,i) in list.result.attachments.img'>
                                                    <img :src="contentList.attachmentList[item].file_key" v-if='contentList.attachmentList[item]' class='imgList mr8 mb8' @click='showImg("grade",list.result.attachments.img)' alt=""  >
                                                </li>
                                            </ul>
                                            <div v-else><?php echo Yii::t("referral", "Attachments Error"); ?></div>
                                        </div>
                                        <div class='color3'>
                                            <div v-for='(item,j) in list.result.attachments.other'>
                                                <div class='flex fileLink' v-if='contentList.attachmentList[item]'>
                                                    <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/PDF1.png' ?>" alt="" class="pdfImg" v-if='contentList.attachmentList[item].mime_type=="application/pdf"' />
                                                    <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/excel.png' ?>" alt="" class="pdfImg" v-else-if='contentList.attachmentList[item].mime_type=="application/vnd.ms-excel" || contentList.attachmentList[item].mime_type=="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"' />
                                                    <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/word1.png' ?>" alt="" class="pdfImg" v-else-if='contentList.attachmentList[item].mime_type=="application/msword" || contentList.attachmentList[item].mime_type=="application/vnd.openxmlformats-officedocument.wordprocessingml.document"' />
                                                    <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/zip.png' ?>" alt="" class="pdfImg" v-else-if='contentList.attachmentList[item].mime_type=="application/x-zip-compressed"' />
                                                    <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/link.png' ?>" alt="" class="pdfImg" v-else /> 
                                                    <a class='flex1 ml5 flieEllipsis' target= "_blank" :href="contentList.attachmentList[item].file_key">{{contentList.attachmentList[item].title}}
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div v-if='list.result.finally==1'>   
                            <div class='flex mt24 '>
                                <div class='relative'>
                                    <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/result.png' ?>" alt="" class='avatar24'>
                                    <div class='stepLeft'></div>
                                </div>
                                <div class='flex1 ml8 font14 pr24'>                                                    
                                    <div class='flex mt4'>
                                        <span class='fontBold color3 flex1'><?php echo Yii::t("referral", "Final Decision"); ?></span>
                                        <?php if($type != 'myList'):?>
                                        <el-button type="text" icon="el-icon-circle-plus-outline" v-if='contentList.afresh_affirm' @click='addReaff()'class='font14 bluebg p0'><?php echo Yii::t("referral", "Add other student"); ?></el-button>

                                        <!-- <span class='bluebg cur-p' @click='addReaff()'>添加学生</span> -->
                                        <?php endif;?>
                                    </div>
                                    <div v-for='(item,idx) in contentList.finally_data' class='bgGrey presenter mt12 relative'>
                                        <span class='NOID'>No.{{item.id}}</span>
                                        <div class='flex align-items mt15'>
                                            <span class='color6 font14'><?php echo Yii::t("ptc", "Student"); ?>：</span>
                                            <div class='flex1 color3'>
                                                <div class="media">
                                                    <div class="media-left pull-left media-middle relative" v-if='contentList.student_info_list[item.child.id]'>
                                                        <img :src="contentList.student_info_list[item.child.id].avatar" data-holder-rendered="true" class="avatar">
                                                    </div> 
                                                    <div class="pull-left media-middle">
                                                        <div class="mt4 color3">{{contentList.student_info_list[item.child.id].name}} 
                                                            <!-- <span :class='item.child.tag==2?"tagLabelYellow":item.child.tag==3?"tagLabel":""'>{{showTitle(item.child.tag,'ISP_type')}}</span> -->
                                                            <span v-if='item.child.tag==2 || item.child.tag==3' class='glyphicon glyphicon-bookmark font12' :class='item.child.tag==2?"waitingColor":item.child.tag==3?"colorRed":""'></span>
                                                        </div> 
                                                        <div class="color6 font12">{{contentList.student_info_list[item.child.id].className}}</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class='flex mt12'>
                                            <span class='color6 font14'><?php echo Yii::t("referral", "Decision"); ?>：</span>
                                            <div class='flex1 color3'><span class="label fontWeightNormal" :class='item.result.identified==1?"label-danger":"label-success"'>{{showTitle(item.result.identified,'fact')}}</span></div>
                                        </div>
                                        <div class='flex mt12'>
                                            <span class='color6 font14'><?php echo Yii::t("referral", "Time in Office"); ?>：</span>
                                            <div class='flex1 color3'>{{item.result.duration}}</div>
                                        </div>
                                        <div class='flex mt12'>
                                            <span class='color6 font14'><?php echo Yii::t("referral", "Final Result"); ?>：</span>
                                            <div class='flex1 color3' >
                                                <span class='mr10' v-for='(list,index) in item.result.actions'>{{showTitle(list,'actions')}}</span>
                                                <?php if($type != 'myList'):?>
                                                    <span v-if='showFollow(item.result.actions)'>
                                                        <span class='btn-link' @click='follow(item,"show")' v-if='item.followup.date'>{{item.followup.date[0]}} ~ {{item.followup.date[1]}} <span class='el-icon-arrow-right'></span></span>
                                                        <span class='btn-link' @click='follow(item,"edit")' v-else><?php echo Yii::t("referral", "Detail"); ?></span>
                                                    </span>
                                                <?php endif;?>
                                                <?php if($type == 'myList'):?>
                                                    <span class='btn-link' @click='follow(item,"show")' v-if='item.followup.date'>{{item.followup.date[0]}} ~ {{item.followup.date[1]}} <span class='el-icon-arrow-right'></span></span>
                                                    </span>
                                                <?php endif;?>
                                            </div>
                                        </div>
                                        <div class='flex mt12'>
                                            <span class='color6 font14'><?php echo Yii::t("referral", "Contact parents"); ?>：</span>
                                            <div class='flex1 color3'><span class='mr10' v-for='(list,index) in item.result.contact_parents'>{{showTitle(list,'contact_parents')}}</span></div>
                                        </div> 
                                        <div class='flex mt12' v-if='item.result.remark!=""'>
                                            <span class='color6 font14'><?php echo Yii::t("referral", "Comment"); ?>：</span>
                                            <div class='flex1 color3' v-html='htmlBr(item.result.remark)'></div>
                                        </div>
                                        <div class='flex mt12' v-if='item.result.attachments.img || item.result.attachments.other'>
                                            <span class='color6 font14'><?php echo Yii::t("curriculum", "Attachments"); ?>：</span>
                                            <div class='flex1 color3'>
                                                <div class='' v-if='item.result.attachments.img'>
                                                    <ul class='mb12 imgLi'  id='to_admin'   v-if='Object.values(contentList.attachmentList).length>0'>
                                                        <li v-for='(list,i) in item.result.attachments.img'>
                                                            <img :src="contentList.attachmentList[list].file_key" v-if='contentList.attachmentList[list]' class='imgList mb8 mr8' @click='showImg("to_admin",item.result.attachments.img)' alt=""  >
                                                        </li>
                                                    </ul>
                                                    <div v-else><?php echo Yii::t("referral", "Attachments Error"); ?></div>
                                                </div>
                                                <div class='color3' v-if='item.result.attachments.other'>
                                                    <div v-for='(list,j) in item.result.attachments.other'>
                                                        <div class='flex fileLink' v-if='contentList.attachmentList[list]'>
                                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/PDF1.png' ?>" alt="" class="pdfImg" v-if='contentList.attachmentList[list].mime_type=="application/pdf"' />
                                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/excel.png' ?>" alt="" class="pdfImg" v-else-if='contentList.attachmentList[list].mime_type=="application/vnd.ms-excel" || contentList.attachmentList[list].mime_type=="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"' />
                                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/word1.png' ?>" alt="" class="pdfImg" v-else-if='contentList.attachmentList[list].mime_type=="application/msword" || contentList.attachmentList[list].mime_type=="application/vnd.openxmlformats-officedocument.wordprocessingml.document"' />
                                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/zip.png' ?>" alt="" class="pdfImg" v-else-if='contentList.attachmentList[list].mime_type=="application/x-zip-compressed"' />
                                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/link.png' ?>" alt="" class="pdfImg" v-else /> 
                                                            <a class='flex1 ml5 flieEllipsis' target= "_blank" :href="contentList.attachmentList[list].file_key">{{contentList.attachmentList[list].title}}
                                                            </a>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class='flex mt12 align-items '>
                                            <span class='color6 font14'><?php echo Yii::t("referral", "By"); ?>：</span>
                                            <div class='flex1 color3'>
                                                <div class='flex align-items '>
                                                    <img :src="contentList.staff_info_list[item.updated_by].photoUrl" data-holder-rendered="true" class="avatar32">
                                                    <span class='ml10'>{{contentList.staff_info_list[item.updated_by].name}}</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class='flex mt12'>
                                            <span class='color6 font14'><?php echo Yii::t("ptc", "Time"); ?>：</span>
                                            <div class='flex1 color3'>{{item.updated_at}}</div>
                                        </div>
                                        <?php if($type != 'myList'):?>
                                        <div class='flex mt16'>
                                            <div class='flex1'>
                                            </div>
                                            <button class="btn btn-primary"  @click='reaffirmation(item,"isp")' v-if='contentList.afresh_affirm' ><?php echo Yii::t("referral", "Reset"); ?></button>
                                            <!-- <div class="btn-group" v-if='item.send_log && item.send_log.length>0'>
                                                <button type="button" class="btn btn-primary"><?php echo Yii::t("referral", "Notify To"); ?></button>
                                                <button type="button" class="btn btn-primary dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                    <span class="caret"></span>
                                                    <span class="sr-only">Toggle Dropdown</span>
                                                </button>
                                                <ul class="dropdown-menu dropdownLeft">
                                                    <li><a href="javascript:;" @click='sendMessage(item)'><?php echo Yii::t("referral", "Send Notify"); ?></a></li>
                                                    <li v-if='item.send_log && item.send_log.length>0'><a href="javascript:;" @click='viewSendMessage(item)'><?php echo Yii::t("referral", "All notification logs"); ?></a></li>
                                                </ul>
                                            </div>
                                            <div v-else>
                                                <button class="btn btn-primary"  @click='sendMessage(item)'><?php echo Yii::t("referral", "Send Notify"); ?></button>
                                            </div> -->
                                            <!-- <button class="btn btn-primary"  @click='allSendMessage(item)'><?php echo Yii::t("referral", "Notify To"); ?></button> -->
                                        </div>
                                        <?php endif;?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div v-if='list.node=="SUBJECTLEAD"'>
                        <div class='flex mt24 '  v-if='list.result.finally==0'>
                            <div class='relative'>
                                <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/subject.png' ?>"  alt="" class='avatar24'>
                                
                                <div class='stepLeft'></div>
                            </div>
                            <div class='flex1 ml8 pr24'> 
                                <div class='color3 fontBold font14 mt4'><?php echo Yii::t("referral", "Head of Department"); ?></div>
                                <div class='flex align-items mt12'>
                                    <div class='flex1'>
                                        <img :src="contentList.staff_info_list[list.updated_by].photoUrl" data-holder-rendered="true" class="avatar32">
                                        <span class='font14 fontBold ml10'>{{contentList.staff_info_list[list.updated_by].name}}</span>
                                    </div>
                                    <div class='color6'>{{list.updated_at}}</div>
                                </div>
                                <div class='flex mt12' v-if='list.result.remark!=""'>
                                    <span class='color6 font14'><?php echo Yii::t("referral", "Comment"); ?>：</span>
                                    <div class='flex1 color3' v-html='htmlBr(list.result.remark)'></div>
                                </div>
                                <div v-if='list.result.to_admin==1'>
                                    <div class='flex mt12'>
                                        <span class='color6 font14'><?php echo Yii::t("referral", "Followup"); ?>：</span>
                                        <div class='flex1 color3'><?php echo Yii::t("referral", "Transfer to Principal Office"); ?></div>
                                    </div>
                                </div>
                                <div class='flex mt12' v-if="list.result.to_gradelead == 1">
                                    <span class='color6 font14'><?php echo Yii::t("referral", "Followup"); ?>：</span>
                                    <div class="color3 flex1">
                                        <?php echo Yii::t("referral", "Transfer to Head of Grade"); ?>
                                    </div>
                                </div>
                                <div v-if='list.result.to_isp==1'>
                                    <div class='flex mt12'>
                                        <span class='color6 font14'><?php echo Yii::t("referral", "Followup"); ?>：</span>
                                        <div class='flex1 color3'><?php echo Yii::t("referral", "Transfer to Support Groups"); ?></div>
                                    </div>
                                    <div class='flex mt12'>
                                        <span class='color6 font14'><?php echo Yii::t("referral", "Support Type"); ?>：</span>
                                        <div class='flex1 color3'>{{showTitle(list.result.help_type,'ISP_help')}}</div>
                                    </div>
                                    <div class='flex mt12'>
                                        <span class='color6 font14'><?php echo Yii::t("referral", "Support Group"); ?>：</span>
                                        <div class='flex1 color3'>{{showTitle(list.result.support,'ISP_type')}}</div>
                                    </div>
                                </div>
                                <div class='flex mt12' v-if='list.result.attachments.img || list.result.attachments.other'>
                                    <span class='color6 font14'><?php echo Yii::t("curriculum", "Attachments"); ?>：</span>
                                    <div class='flex1 color3'>
                                        <div class='' v-if='list.result.attachments.img'>
                                            <ul class='mb12 imgLi'  id='grade'   v-if='Object.values(contentList.attachmentList).length>0'>
                                                <li v-for='(item,i) in list.result.attachments.img'>
                                                    <img :src="contentList.attachmentList[item].file_key" v-if='contentList.attachmentList[item]' class='imgList mr8 mb8' @click='showImg("grade",list.result.attachments.img)' alt=""  >
                                                </li>
                                            </ul>
                                            <div v-else><?php echo Yii::t("referral", "Attachments Error"); ?></div>
                                        </div>
                                        <div class='color3'>
                                            <div v-for='(item,j) in list.result.attachments.other'>
                                                <div class='flex fileLink' v-if='contentList.attachmentList[item]' >
                                                    <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/PDF1.png' ?>" alt="" class="pdfImg" v-if='contentList.attachmentList[item].mime_type=="application/pdf"' />
                                                    <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/excel.png' ?>" alt="" class="pdfImg" v-else-if='contentList.attachmentList[item].mime_type=="application/vnd.ms-excel" || contentList.attachmentList[item].mime_type=="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"' />
                                                    <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/word1.png' ?>" alt="" class="pdfImg" v-else-if='contentList.attachmentList[item].mime_type=="application/msword" || contentList.attachmentList[item].mime_type=="application/vnd.openxmlformats-officedocument.wordprocessingml.document"' />
                                                    <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/zip.png' ?>" alt="" class="pdfImg" v-else-if='contentList.attachmentList[item].mime_type=="application/x-zip-compressed"' />
                                                    <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/link.png' ?>" alt="" class="pdfImg" v-else /> 
                                                    <a class='flex1 ml5 flieEllipsis' target= "_blank" :href="contentList.attachmentList[item].file_key">{{contentList.attachmentList[item].title}}
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div v-if='list.result.finally==1'>   
                            <div class='flex mt24 '>
                                <div class='relative'>
                                    <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/result.png' ?>" alt="" class='avatar24'>
                                    <div class='stepLeft'></div>
                                </div>
                                <div class='flex1 ml8 font14 pr24'>                                                    
                                    <div class='flex mt4'>
                                        <span class='fontBold color3 flex1'><?php echo Yii::t("referral", "Final Decision"); ?></span>
                                        <?php if($type != 'myList'):?>
                                        <el-button type="text" icon="el-icon-circle-plus-outline" v-if='contentList.afresh_affirm' @click='addReaff()'class='font14 bluebg p0'><?php echo Yii::t("referral", "Add other student"); ?></el-button>

                                        <!-- <span class='bluebg cur-p' @click='addReaff()'>添加学生</span> -->
                                        <?php endif;?>
                                    </div>
                                    <div v-for='(item,idx) in contentList.finally_data' class='bgGrey presenter mt12 relative'>
                                        <span class='NOID'>No.{{item.id}}</span>
                                        <div class='flex align-items mt15'>
                                            <span class='color6 font14'><?php echo Yii::t("ptc", "Student"); ?>：</span>
                                            <div class='flex1 color3'>
                                                <div class="media">
                                                    <div class="media-left pull-left media-middle relative" v-if='contentList.student_info_list[item.child.id]'>
                                                        <img :src="contentList.student_info_list[item.child.id].avatar" data-holder-rendered="true" class="avatar">
                                                    </div> 
                                                    <div class="pull-left media-middle">
                                                        <div class="mt4 color3">{{contentList.student_info_list[item.child.id].name}} 
                                                            <!-- <span :class='item.child.tag==2?"tagLabelYellow":item.child.tag==3?"tagLabel":""'>{{showTitle(item.child.tag,'ISP_type')}}</span> -->
                                                            <span v-if='item.child.tag==2 || item.child.tag==3' class='glyphicon glyphicon-bookmark font12' :class='item.child.tag==2?"waitingColor":item.child.tag==3?"colorRed":""'></span>
                                                        </div> 
                                                        <div class="color6 font12">{{contentList.student_info_list[item.child.id].className}}</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class='flex mt12'>
                                            <span class='color6 font14'><?php echo Yii::t("referral", "Decision"); ?>：</span>
                                            <div class='flex1 color3'><span class="label fontWeightNormal" :class='item.result.identified==1?"label-danger":"label-success"'>{{showTitle(item.result.identified,'fact')}}</span></div>
                                        </div>
                                        <div class='flex mt12'>
                                            <span class='color6 font14'><?php echo Yii::t("referral", "Time in Office"); ?>：</span>
                                            <div class='flex1 color3'>{{item.result.duration}}</div>
                                        </div>
                                        <div class='flex mt12'>
                                            <span class='color6 font14'><?php echo Yii::t("referral", "Final Result"); ?>：</span>
                                            <div class='flex1 color3' >
                                                <span class='mr10' v-for='(list,index) in item.result.actions'>{{showTitle(list,'actions')}}</span>
                                                <?php if($type != 'myList'):?>
                                                    <span v-if='showFollow(item.result.actions)'>
                                                        <span class='btn-link' @click='follow(item,"show")' v-if='item.followup.date'>{{item.followup.date[0]}} ~ {{item.followup.date[1]}} <span class='el-icon-arrow-right'></span></span>
                                                        <span class='btn-link' @click='follow(item,"edit")' v-else><?php echo Yii::t("referral", "Detail"); ?></span>
                                                    </span>
                                                <?php endif;?>
                                                <?php if($type == 'myList'):?>
                                                    <span class='btn-link' @click='follow(item,"show")' v-if='item.followup.date'>{{item.followup.date[0]}} ~ {{item.followup.date[1]}} <span class='el-icon-arrow-right'></span></span>
                                                    </span>
                                                <?php endif;?>
                                            </div>
                                        </div>
                                        <div class='flex mt12'>
                                            <span class='color6 font14'><?php echo Yii::t("referral", "Contact parents"); ?>：</span>
                                            <div class='flex1 color3'><span class='mr10' v-for='(list,index) in item.result.contact_parents'>{{showTitle(list,'contact_parents')}}</span></div>
                                        </div> 
                                        <div class='flex mt12' v-if='item.result.remark!=""'>
                                            <span class='color6 font14'><?php echo Yii::t("referral", "Comment"); ?>：</span>
                                            <div class='flex1 color3' v-html='htmlBr(item.result.remark)'></div>
                                        </div>
                                        <div class='flex mt12' v-if='item.result.attachments.img || item.result.attachments.other'>
                                            <span class='color6 font14'><?php echo Yii::t("curriculum", "Attachments"); ?>：</span>
                                            <div class='flex1 color3'>
                                                <div class='' v-if='item.result.attachments.img'>
                                                    <ul class='mb12 imgLi'  id='to_admin'   v-if='Object.values(contentList.attachmentList).length>0'>
                                                        <li v-for='(list,i) in item.result.attachments.img'>
                                                            <img :src="contentList.attachmentList[list].file_key"  v-if='contentList.attachmentList[list]' class='imgList mb8 mr8' @click='showImg("to_admin",item.result.attachments.img)' alt=""  >
                                                        </li>
                                                    </ul>
                                                    <div v-else><?php echo Yii::t("referral", "Attachments Error"); ?></div>
                                                </div>
                                                <div class='color3' v-if='item.result.attachments.other'>
                                                    <div v-for='(list,j) in item.result.attachments.other'>
                                                        <div class='flex fileLink' v-if='contentList.attachmentList[list]'>
                                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/PDF1.png' ?>" alt="" class="pdfImg" v-if='contentList.attachmentList[list].mime_type=="application/pdf"' />
                                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/excel.png' ?>" alt="" class="pdfImg" v-else-if='contentList.attachmentList[list].mime_type=="application/vnd.ms-excel" || contentList.attachmentList[list].mime_type=="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"' />
                                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/word1.png' ?>" alt="" class="pdfImg" v-else-if='contentList.attachmentList[list].mime_type=="application/msword" || contentList.attachmentList[list].mime_type=="application/vnd.openxmlformats-officedocument.wordprocessingml.document"' />
                                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/zip.png' ?>" alt="" class="pdfImg" v-else-if='contentList.attachmentList[list].mime_type=="application/x-zip-compressed"' />
                                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/link.png' ?>" alt="" class="pdfImg" v-else /> 
                                                            <a class='flex1 ml5 flieEllipsis' target= "_blank" :href="contentList.attachmentList[list].file_key">{{contentList.attachmentList[list].title}}
                                                            </a>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class='flex mt12 align-items '>
                                            <span class='color6 font14'><?php echo Yii::t("referral", "By"); ?>：</span>
                                            <div class='flex1 color3'>
                                                <div class='flex align-items '>
                                                    <img :src="contentList.staff_info_list[item.updated_by].photoUrl" data-holder-rendered="true" class="avatar32">
                                                    <span class='ml10'>{{contentList.staff_info_list[item.updated_by].name}}</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class='flex mt12'>
                                            <span class='color6 font14'><?php echo Yii::t("ptc", "Time"); ?>：</span>
                                            <div class='flex1 color3'>{{item.updated_at}}</div>
                                        </div>
                                        <?php if($type != 'myList'):?>
                                        <div class='flex mt16'>
                                            <div class='flex1'>
                                            </div>
                                            <button class="btn btn-primary"  @click='reaffirmation(item,"isp")' v-if='contentList.afresh_affirm' ><?php echo Yii::t("referral", "Reset"); ?></button>
                                            <!-- <div class="btn-group" v-if='item.send_log && item.send_log.length>0'>
                                                <button type="button" class="btn btn-primary"><?php echo Yii::t("referral", "Notify To"); ?></button>
                                                <button type="button" class="btn btn-primary dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                    <span class="caret"></span>
                                                    <span class="sr-only">Toggle Dropdown</span>
                                                </button>
                                                <ul class="dropdown-menu dropdownLeft">
                                                    <li><a href="javascript:;" @click='sendMessage(item)'><?php echo Yii::t("referral", "Send Notify"); ?></a></li>
                                                    <li v-if='item.send_log && item.send_log.length>0'><a href="javascript:;" @click='viewSendMessage(item)'><?php echo Yii::t("referral", "All notification logs"); ?></a></li>
                                                </ul>
                                            </div>
                                            <div v-else>
                                                <button class="btn btn-primary"  @click='sendMessage(item)'><?php echo Yii::t("referral", "Send Notify"); ?></button>
                                            </div> -->
                                            <!-- <button class="btn btn-primary"  @click='allSendMessage(item)'><?php echo Yii::t("referral", "Notify To"); ?></button> -->
                                        </div>
                                        <?php endif;?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div v-if='list.node=="FEEDBACK"'>
                        <div class='flex mt24 '>
                            <div class='relative'>
                                <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/other.png' ?>" alt="" class='avatar24'>
                                <div class='stepLeft'></div>
                            </div>
                            <div class='flex1 ml8 pr24'>
                                <div class='color4 fontBold font14 mt4'><?php echo Yii::t("ptc", "Comment"); ?></div>
                                <div class="flex mt20" v-if='contentList.comment[list.result.comment_id]'>
                                    <div class="media-left pull-left media-middle relative">
                                        <img :src="contentList.staff_info_list[contentList.comment[list.result.comment_id].created_by].photoUrl" data-holder-rendered="true" class="avatar32">
                                    </div> 
                                    <div class="flex1 ml10">
                                        <div class='flex align-items'>
                                            <div class="mt5 pb8 color3 font14 fontBold flex1">{{contentList.staff_info_list[contentList.comment[list.result.comment_id].created_by].name}}</div> 
                                            <span class='color6'>{{list.updated_at}}</span>
                                        </div>
                                        <div v-html='htmlBr(contentList.comment[list.result.comment_id].comment)' class='color6 font14 mt8'></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div v-if='list.node=="OVER"'>
                        <div class='flex mt24 '>
                            <div class='relative'>
                                <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/end.png' ?>" alt="" class='avatar24'>
                                <!-- <div class='stepLeft'></div> -->
                            </div>
                            <div class='flex1 ml8 pr24'>
                                <div class='color4 fontBold font14 mt4'><?php echo Yii::t("referral", "Mark as completed"); ?></div>
                                <div class="flex mt20">
                                    <div class="media-left pull-left media-middle relative">
                                        <img :src="contentList.staff_info_list[list.updated_by].photoUrl" data-holder-rendered="true" class="avatar32">
                                    </div>
                                    <div class="flex1 ml10">
                                        <div class='flex align-items'>
                                            <div class="mt5 pb8 color3 font14 fontBold flex1">{{contentList.staff_info_list[list.updated_by].name}}</div>
                                            <span class='color6'>{{list.updated_at}}</span>
                                        </div>
                                        <p class='bg-success text-success p10 mt8' style="border-radius: 4px">
                                            <span class="glyphicon glyphicon-ok-sign"></span>
                                            <?php echo Yii::t("referral", "Handling Completed");?>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div>
                    <?php if($type == 'myList'):?>
                        <div v-if='contentList.behavior.current_node=="ADMIN"'>
                            <div class='flex mt24 '>
                                <div class='relative'>
                                    <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/office.png' ?>" alt="" class='avatar24'>
                                    <div class='stepLeft'></div>
                                </div>
                                <div class='flex1 ml8 pr24'>
                                    <div class='flex mt4 relative'><span class='flex1 color3 fontBold font14'><?php echo Yii::t("referral", "Principal Office"); ?></span><span class='handle'><?php echo Yii::t("referral", "In progress"); ?></span></div>
                                    <div class="alert alert-warning mt16" role="alert" v-if='contentList.behavior.detail.notify_ids==0'><?php echo Yii::t("referral", "Waiting for "); ?><?php echo Yii::t("referral", "Principal Office"); ?><?php echo Yii::t("referral", " to handle"); ?></div>
                                    <div class="alert alert-warning mt16" role="alert" v-else><?php echo Yii::t("referral", "Waiting for "); ?>{{contentList.staff_info_list[contentList.behavior.detail.notify_ids].name}}<?php echo Yii::t("referral", " to handle"); ?></div>
                                </div>
                            </div>
                            
                        </div>
                        <div v-if='contentList.behavior.current_node=="ISP"'>
                            <div class='flex mt24 '>
                                <div class='relative'>
                                    <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/ISP.png' ?>" alt="" class='avatar24'>
                                    <div class='stepLeft'></div>
                                </div>
                                <div class='flex1 ml8 pr24'>
                                    <div class='flex mt4 relative'><span class='flex1 color3 fontBold font14'><?php echo Yii::t("referral", "Support Group"); ?></span><span class='handle'><?php echo Yii::t("referral", "In progress"); ?></span></div>
                                    <div class="alert alert-warning mt16" role="alert" v-if='contentList.behavior.detail.notify_ids==0'><?php echo Yii::t("referral", "Waiting for "); ?><?php echo Yii::t("referral", "Support Group"); ?><?php echo Yii::t("referral", " to handle"); ?></div>
                                    <div class="alert alert-warning mt16" role="alert" v-else><?php echo Yii::t("referral", "Waiting for "); ?>{{contentList.staff_info_list[contentList.behavior.detail.notify_ids].name}}<?php echo Yii::t("referral", " to handle"); ?></div>
                                </div>
                            </div>                               
                        </div>
                        <div v-if='contentList.behavior.current_node=="GRADELEAD"'>
                            <div class='flex mt24 '>
                                <div class='relative'>
                                    <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/Gradelead.png' ?>" alt="" class='avatar24'>
                                    <div class='stepLeft'></div>
                                </div>
                                <div class='flex1 ml8 pr24'>
                                    <div class='flex mt4 relative'><span class='flex1 color3 fontBold font14'><?php echo Yii::t("referral", "Head of Grade"); ?></span><span class='handle'><?php echo Yii::t("referral", "In progress"); ?></span></div>
                                    <div class="alert alert-warning mt16" role="alert" v-if='contentList.behavior.detail.notify_ids==0'><?php echo Yii::t("referral", "Waiting for "); ?><?php echo Yii::t("referral", "Head of Grade"); ?><?php echo Yii::t("referral", " to handle"); ?></div>
                                    <div class="alert alert-warning mt16" role="alert" v-else><?php echo Yii::t("referral", "Waiting for "); ?>{{contentList.staff_info_list[contentList.behavior.detail.notify_ids].name}}<?php echo Yii::t("referral", " to handle"); ?></div>
                                </div>
                            </div>                               
                        </div>
                    <?php endif;?>
                    <?php if($type == 'processing'):?>
                        <div v-if='contentList.finally_data.length>0 && contentList.behavior.current_node!=null'>
                            <div class='flex mt24' v-if='contentList.can_end_mark'>
                                <div class='relative'>
                                    <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/end.png' ?>" alt="" class='avatar24'>
                                    <!-- <div class='stepLeft'></div> -->
                                </div>
                                <div class='flex1 ml8 pr24'>
                                    <div class='flex mt4 relative'><span class='flex1 color3 fontBold font14'><?php echo Yii::t("referral", "Mark as completed"); ?></span></div>
                                    <button type="button" class="btn btn-primary mt16" @click='markEnd()'><?php echo Yii::t("referral", "Complete"); ?></button>
                                </div>
                            </div>      
                        </div>
                        <div v-else>
                            <div v-if='contentList.behavior.current_node=="ADMIN"'>
                                <div class='flex mt24'>
                                    <div class='relative'>
                                        <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/office.png' ?>" alt="" class='avatar24'>
                                        <!-- <div class='stepLeft'></div> -->
                                    </div>
                                    <div class='flex1 ml8 pr24' >
                                        <div class='flex mt4 relative'><span class='flex1 color3 fontBold font14'><?php echo Yii::t("referral", "Principal Office"); ?></span><span class='handle'><?php echo Yii::t("referral", "In progress"); ?></span></div>
                                        <div v-if='contentList.is_office'>
                                            <div class="btn-group mt16" >
                                                <button type="button" class="btn btn-primary"><?php echo Yii::t("referral", "Process"); ?></button>
                                                <button type="button" class="btn btn-primary dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                    <span class="caret"></span>
                                                    <span class="sr-only">Toggle Dropdown</span>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li><a href="javascript:;" @click='forwardOffice("office")'><?php echo Yii::t("referral", "Assign to Principal Office"); ?></a></li>
                                                    <li  v-if='contentList.can_grade'><a href="javascript:;" @click='forwardGrade("office")'><?php echo Yii::t("referral", "Transfer to Head of Grade"); ?></a></li>
                                                    <li><a href="javascript:;" @click='forwardISP("office")'><?php echo Yii::t("referral", "Transfer to Support Groups"); ?></a></li>
                                                    <li v-if='contentList.can_subject'><a href="javascript:;" @click='forwardSubject("office")'><?php echo Yii::t("referral", "Transfer to Head of Department"); ?></a></li>
                                                    <li><a href="javascript:;" @click='finalResult("office")'><?php echo Yii::t("referral", "Complete Final Decision"); ?></a></li>
                                                </ul>
                                            </div>
                                        </div>
                                        <div v-else>
                                            <div class="alert alert-warning mt16" role="alert"><?php echo Yii::t("referral", "Waiting"); ?></div>
                                        </div>
                                    </div>
                                </div>
                                
                            </div>
                            <div v-if='contentList.behavior.current_node=="ISP"'>
                                <div class='flex mt24'>
                                    <div class='relative'>
                                        <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/ISP.png' ?>" alt="" class='avatar24'>
                                        <!-- <div class='stepLeft'></div> -->
                                    </div>
                                    <div class='flex1 ml8 pr24'>
                                        <div class='flex mt4 relative'><span class='flex1 color3 fontBold font14'><?php echo Yii::t("referral", "Support Group"); ?></span><span class='handle'><?php echo Yii::t("referral", "In progress"); ?></span></div>
                                        <div class="btn-group mt16" v-if='contentList.is_isp'>
                                            <button type="button" class="btn btn-primary"><?php echo Yii::t("referral", "Process"); ?></button>
                                            <button type="button" class="btn btn-primary dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                <span class="caret"></span>
                                                <span class="sr-only">Toggle Dropdown</span>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li v-if='contentList.can_finally'><a href="javascript:;" @click='finalResult("isp","office")'><?php echo Yii::t("referral", "Complete Final Decision"); ?></a></li>
                                                <li v-else><a href="javascript:;" @click='finalResult("isp")'><?php echo Yii::t("referral", "It is not a behavior issue"); ?></a></li>
                                                <li><a href="javascript:;" @click='forwardOffice("isp")'><?php echo Yii::t("referral", "Transfer to Principal Office"); ?></a></li>
                                                <li v-if='contentList.can_subject'><a href="javascript:;" @click='forwardSubject("isp")'><?php echo Yii::t("referral", "Transfer to Head of Department"); ?></a></li>
                                                <li v-if='contentList.can_grade'><a href="javascript:;" @click='forwardGrade("isp")'><?php echo Yii::t("referral", "Transfer to Head of Grade"); ?></a></li>
                                            </ul>
                                        </div>
                                        <div v-else>
                                            <div class="alert alert-warning mt16" role="alert" v-if='contentList.behavior.detail.notify_ids==0'>
                                                <?php echo Yii::t("referral", "Waiting for "); ?><?php echo Yii::t("referral", "Support Group"); ?><?php echo Yii::t("referral", " to handle"); ?>
                                            </div>
                                            <div class="alert alert-warning mt16" role="alert" v-else>
                                                <?php echo Yii::t("referral", "Waiting for "); ?>{{contentList.staff_info_list[contentList.behavior.detail.notify_ids].name}}<?php echo Yii::t("referral", " to handle"); ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>                               
                            </div>
                            <div v-if='contentList.behavior.current_node=="GRADELEAD"'>
                                <div class='flex mt24'>
                                    <div class='relative'>
                                        <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/Gradelead.png' ?>" alt="" class='avatar24'>
                                        <!-- <div class='stepLeft'></div> -->
                                    </div>
                                    <div class='flex1 ml8 pr24'>
                                        <div class='flex mt4 relative'><span class='flex1 color3 fontBold font14'><?php echo Yii::t("referral", "Lead of Grade"); ?></span><span class='handle'><?php echo Yii::t("referral", "In progress"); ?></span></div>
                                        <div class="btn-group mt16"  v-if='contentList.is_grade' >
                                            <button type="button" class="btn btn-primary"><?php echo Yii::t("referral", "Process"); ?></button>
                                            <button type="button" class="btn btn-primary dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                <span class="caret"></span>
                                                <span class="sr-only">Toggle Dropdown</span>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li v-if='contentList.can_finally'><a href="javascript:;" @click='finalResult("grade","office")'><?php echo Yii::t("referral", "Complete Final Decision"); ?></a></li>
                                                <li v-else><a href="javascript:;" @click='finalResult("grade")'><?php echo Yii::t("referral", "It is not a behavior issue"); ?></a></li>
                                                <li><a href="javascript:;" @click='forwardOffice("grade")'><?php echo Yii::t("referral", "Transfer to Principal Office"); ?></a></li>
                                                <li><a href="javascript:;" @click='forwardISP("grade")'><?php echo Yii::t("referral", "Transfer to Support Groups"); ?></a></li>
                                                <li v-if='contentList.can_subject'><a href="javascript:;" @click='forwardSubject("grade")'><?php echo Yii::t("referral", "Transfer to Head of Department"); ?></a></li>
                                            </ul>
                                        </div>
                                        <div v-else>
                                            <div class="alert alert-warning mt16" role="alert" v-if='contentList.behavior.detail.notify_ids==0'>
                                                <?php echo Yii::t("referral", "Waiting for "); ?><?php echo Yii::t("referral", "Head of Grade"); ?><?php echo Yii::t("referral", " to handle"); ?>
                                            </div>
                                            <div class="alert alert-warning mt16" role="alert" v-else>
                                                <?php echo Yii::t("referral", "Waiting for "); ?>{{contentList.staff_info_list[contentList.behavior.detail.notify_ids].name}}<?php echo Yii::t("referral", " to handle"); ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>                               
                            </div>
                            <div v-if='contentList.behavior.current_node=="SUBJECTLEAD"'>
                                <div class='flex mt24'>
                                    <div class='relative'>
                                        <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/subject.png' ?>" alt="" class='avatar24'>
                                        <!-- <div class='stepLeft'></div> -->
                                    </div>
                                    <div class='flex1 ml8 pr24'>
                                        <div class='flex mt4 relative'><span class='flex1 color3 fontBold font14'><?php echo Yii::t("referral", "Head of Department"); ?></span><span class='handle'><?php echo Yii::t("referral", "In progress"); ?></span></div>
                                        <div class="btn-group mt16"  v-if='contentList.is_subject' >
                                            <button type="button" class="btn btn-primary"><?php echo Yii::t("referral", "Process"); ?></button>
                                            <button type="button" class="btn btn-primary dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                <span class="caret"></span>
                                                <span class="sr-only">Toggle Dropdown</span>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li><a href="javascript:;" @click='forwardOffice("subject")'><?php echo Yii::t("referral", "Transfer to Principal Office"); ?></a></li>
                                                <li  v-if='contentList.can_grade'><a href="javascript:;" @click='forwardGrade("subject")'><?php echo Yii::t("referral", "Transfer to Head of Grade"); ?></a></li>
                                                <li><a href="javascript:;" @click='forwardISP("subject")'><?php echo Yii::t("referral", "Transfer to Support Groups"); ?></a></li>
                                                <li v-if='contentList.can_finally'><a href="javascript:;" @click='finalResult("subject","office")'><?php echo Yii::t("referral", "Complete Final Decision"); ?></a></li>
                                                <li v-else><a href="javascript:;" @click='finalResult("subject")'><?php echo Yii::t("referral", "It is not a behavior issue"); ?></a></li>
                                            </ul>
                                        </div>
                                        <div v-else>
                                            <div class="alert alert-warning mt16" role="alert" v-if='contentList.behavior.detail.notify_ids==0'>
                                                <?php echo Yii::t("referral", "Waiting for "); ?><?php echo Yii::t("referral", "Head of Department"); ?><?php echo Yii::t("referral", " to handle"); ?>
                                            </div>
                                            <div class="alert alert-warning mt16" role="alert" v-else>
                                                <?php echo Yii::t("referral", "Waiting for "); ?>{{contentList.staff_info_list[contentList.behavior.detail.notify_ids].name}}<?php echo Yii::t("referral", " to handle"); ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>                               
                            </div>
                        </div>
                    <?php endif;?>
                </div> 
            </div>
            <div class='flex overflow mt24'>
                <div class='relative'>
                    <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/other.png' ?>" alt="" class='avatar24'>
                    <!-- <div class='stepLeft'></div> -->
                </div>
                <div class='flex1 ml8 pr24 mb24'>
                    <div class='color4 fontBold font14 mt4'><?php echo Yii::t("ptc", "Comment"); ?></div>
                    <div class="flex pb10 mt16">
                        <div class="">
                            <img :src="config.user_data.photoUrl" data-holder-rendered="true" class="avatar32">
                        </div> 
                        <div class="flex1 ml10">
                            <div class="mt5 pb8 color3 font14 fontBold">{{config.user_data.name}}</div> 
                            <div  class='mt8'>
                                <textarea class="form-control" v-model='appendComment' rows="3"></textarea>
                            </div>
                            <div class='mt8'>
                                <button type="button" class="btn btn-primary pull-right ml10" @click='saveComment'><?php echo Yii::t("global", "Submit"); ?></button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>