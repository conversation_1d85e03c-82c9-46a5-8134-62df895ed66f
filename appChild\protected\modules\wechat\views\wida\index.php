<div class="page">
    <div class="weui-cells__title"><?php echo Yii::t('navigations', 'Scheduling Platform'); ?></div>
    <?php if(count($data) > 0): ?>
    <div class="page__bd">
        <?php foreach ($data as $childid => $plans): ?>
            <?php
                $childObj = $this->myChildObjs[$childid];
                $name = $childObj->getChildName();;
                $birthday = date('Y-m-d', $childObj->birthday);
                $photo = $childObj->photo ? CommonUtils::childPhotoUrl($childObj->photo, 'small') : '';
            ?>
            <div class="weui-cells">
                <div class="weui-cell">
                    <div class="weui-cell__hd" style="position: relative;margin-right: 10px;">
                        <img src="<?php echo $photo; ?>" style="width: 50px;display: block">
                        <!-- <span class="weui-badge __web-inspector-hide-shortcut__" style="position: absolute;top: -.4em;right: -.4em;">8</span> -->
                    </div>
                    <div class="weui-cell__bd">
                        <p><?php echo $name; ?></p>
                        <p style="font-size: 13px;color: #888888;"><?php echo $birthday; ?></p>
                    </div>
                </div>
                <?php foreach ($plans as $plan): ?>
                <a class="weui-cell weui-cell_access" href="<?php echo $this->createUrl('detail', array('planid'=>$plan['planid'], 'childid'=>$plan['childid'])); ?>">
                    <div class="weui-cell__bd">
                        <span style="vertical-align: middle"><?php echo CommonUtils::autoLang($plan['title_cn'], $plan['title_en']); ?></span>
                        <?php if(isset($itemData[$childid][$plan['planid']])): ?>
                        <?php
                            $k = $itemData[$childid][$plan['planid']];
                            $datetime = strtotime($k['date']);
                            $slot = $k['timeslot'];
                            $slot = str_replace(',', '-', $slot);
                            $dateText = date('Y-m-d', $datetime) .' '. $slot;
                        ?>
                        <br><i class="weui-icon-success"></i><span style="font-size:12px"> <?php echo Yii::t('support', 'Reserved'); ?> </span>
                        <span class="weui-badge bg-success" style="background-color:#07c160 ; margin-left: 5px; font-size:12px"><?php echo $dateText; ?></span>
                        <?php else: ?>
                        <br><i class="weui-icon-waiting"></i><span style="font-size:12px"> <?php echo Yii::t('support', 'Not Reserved'); ?> </span>
                        <?php endif; ?>
                    </div>
                    <div class="weui-cell__ft"></div>
                </a>
                <?php endforeach; ?>
            </div>
        <?php endforeach; ?>
    </div>    
    <?php else: ?>
        <div class="page msg_success">
            <div class="weui-msg">
                <div class="weui-msg__icon-area"><i class="weui-icon-success weui-icon_msg"></i></div>
                <div class="weui-msg__text-area">
                    <h2 class="weui-msg__title"><?php echo Yii::t("survey", "No Data"); ?></h2>
                    <p class="weui-msg__desc"><?php  echo Yii::t("support","Currently there is no open scheduling."); ?></p>
                </div>
            </div>
        </div>
    <?php endif; ?>
    </div>
</div>