<?php

/**
 * 退费工作流
 */
class RefundFee implements WorkflowTemplate{
    private $template;
    const REFUND_FEE = 10; //一般退费
    const REFUND_LEAVE_SCHOOL = 20;  //退学退费
    const REFUND_GRADUATE = 50;  //毕业退费
    const REFUND_CHANGE = 30;  //服务修改
    const REFUND_SPECIAL = 40;  //特殊退费 退费期间不出勤
    const REFUND_SPECIALT = 41;  //特殊退费 退费期间正常出勤
    public $step = 'one';
    public $refundType;
    public $refundTypeDesc;
    public $controller;
    public $workflow;
    public function __construct() {
        Yii::import('application.components.policy.refund.*');
        Yii::import('common.models.invoice.Invoice');
        Yii::import('common.models.invoice.ChildServiceInfo');
        Yii::import('common.models.invoice.InvoiceChildRefund');
        Yii::import('common.models.calendar.Calendar');
        Yii::import('common.models.invoice.DepositHistory');
        $this->refundType = array(
            self::REFUND_FEE=>'一般退费 （ 退费期间<spen style="color: red">不出勤</spen> ）',
            self::REFUND_CHANGE=>'服务修改',
            self::REFUND_LEAVE_SCHOOL=>'中途退学',
            self::REFUND_GRADUATE=>'提前毕业',
            self::REFUND_SPECIAL=>'特殊退费（ 退费期间<spen style="color: red">不出勤</spen> ）',
            self::REFUND_SPECIALT=>'特殊退费（ 退费期间<spen style="color: #0BB20C">正常出勤</spen> ）',
        );
        $this->refundTypeDesc = array(
            self::REFUND_FEE=>'符合收退费政策的退费，退费期间孩子不出勤',
            self::REFUND_CHANGE=>'账单开错或半天转全天等',
            self::REFUND_LEAVE_SCHOOL=>'退费后孩子不再上学',
            self::REFUND_GRADUATE=>'本学年3月1日至8月31日大班孩子离校',
            self::REFUND_SPECIAL=>'收退费政策之外的退费，退费期间孩子不出勤',
            self::REFUND_SPECIALT=>'收退费政策之外的退费，退费期间孩子正常出勤',
        );
    }

    public function initi(){
        $this->setTemplate('initi');
        $data['jsFunction'] = 'WorkflowModalShow';
        $data['modalNameId'] = 'workflow-refund-modal';
        $data['workflow'] = $this->workflow;
        $data['type'] = $this->refundType;
        $data['typeDesc'] = $this->refundTypeDesc;
        $data['refundFee'] = self::REFUND_LEAVE_SCHOOL;
        return $data;
    }
    
    /**
     * 公共保存跳转函数
     */
    public function publicSave(){
        $this->step = Yii::app()->request->getParam('step');
        $buttonCategory = Yii::app()->request->getParam('buttonCategory');
        switch ($this->step){
            case 'one':
                return $this->firstSave();
                break;
            case 'two':
                if ($buttonCategory === 'next'){
                    return $this->twoSave();
                }else{
                    $this->step = 'one';
                    return $this->show();
                }
                break;
            case 'three':
                if ($buttonCategory === 'next'){
                    return $this->threeSave();
                }else{
                    $this->step = 'two';
                    return $this->show();
                }
                break;
        }
    }
    
    /**
     * 第一节点（第一步保存）函数
     */
    public function firstSave(){
        if (isset($_POST['RefundFee']) && Yii::app()->request->isPostRequest){
            $refund = new RefundFeeFirstForm();
            $refund->attributes = $_POST['RefundFee'];
            if (in_array($refund->refund_type, array(self::REFUND_LEAVE_SCHOOL, self::REFUND_GRADUATE))){
                $refund->leave_date = ($_POST['RefundFee']['leave_date']) ? strtotime($_POST['RefundFee']['leave_date']) : 0;
                $exte1 = $refund->leave_date;
                $exte2 = '';
                $refund->setScenario('fristLeave');
            }else{
                $refund->setScenario('fristRefund');
                $refund->refund_startdate = ($_POST['RefundFee']['refund_startdate']) ? strtotime($_POST['RefundFee']['refund_startdate']) : 0;
                $refund->refund_enddate = ($_POST['RefundFee']['refund_enddate']) ? strtotime($_POST['RefundFee']['refund_enddate']) : 0;
                $exte1 = $refund->refund_startdate;
                $exte2 = $refund->refund_enddate;
            }
            $refund->memo = isset($_POST['RefundFee']['memo']) ? $_POST['RefundFee']['memo'] : null;
            if (empty($refund->memo)){
                $this->controller->addMessage('state', 'fail');
                $this->controller->addMessage('message', '意见不能为空！');
                $this->controller->showMessage();
            }
            //判断同一个工作流是否正在进行中
            if (!$this->workflow->getOperateId()){
                $count = WorkflowOperation::model()->count('operation_object_id=:operation_object_id and operation_type=:operation_type and state=:state',
                        array(':operation_object_id'=>$refund->childid,':operation_type'=>  WorkflowConfig::WORKFLOW_TYPE_REFUND,':state'=>WorkflowOperation::WORKFLOW_STATS_STEP));
                if ($count){
                    $this->controller->addMessage('state', 'fail');
                    $this->controller->addMessage('message', '此孩子退费流程正在审批中，不能重复提交！');
                    $this->controller->showMessage();
                }
            }
            //判断孩子的状态
            if ($refund->childid){
                // $basic = ChildProfileBasic::model()->findByPk($refund->childid);
                // $statusList = ChildProfileBasic::model()->getStatusList();
                // if ($basic->status >= ChildProfileBasic::STATS_GRADUATED){
                //     $status = $statusList[$basic->status];
                //     $this->controller->addMessage('state', 'fail');
                //     $this->controller->addMessage('message', "孩子已{$status}中，不能操作！");
                //     $this->controller->showMessage();
                // }
                // 检查孩子是否在当前学校就读过。
                // if ($basic->schoolid != $this->workflow->schoolId) {
                //     if (!$this->workflow->hadStudyInSchool($refund->childid)) {
                //         $this->controller->addMessage('state', 'fail');
                //         $this->controller->addMessage('message', "孩子未在当前校园就读过！");
                //         $this->controller->showMessage();
                //     }
                // }
            }
            if ($refund->validate()){
                $transaction = Yii::app()->db->beginTransaction();
                try{
                    //保存工作流业务流程
                    $operationData = array('id'=>$this->workflow->getOperateId(),'operation_type'=>  WorkflowConfig::WORKFLOW_TYPE_REFUND,'operation_object_id'=>$refund->childid,'defination_id'=>$this->workflow->getWorkflowId(),
                        'branchid'=>$this->workflow->schoolId,'state'=>WorkflowOperation::WORKFLOW_STATS_STEP,'current_node_index'=>$this->workflow->getCurrentNodeId(),
                        'exte1'=>$exte1,'exte2'=>$exte2,'exte3'=>$refund->refund_type,
                    );
                    $operationRet = $this->workflow->saveFirstNodeForWorkflow($operationData,null,$refund->memo,$transaction);
                    if ($operationRet === false){
                        $this->controller->addMessage('state', 'fail');
                        $this->controller->addMessage('message', '保存失败');
                        $this->controller->showMessage();
                    }
                    $transaction->commit();
                    $this->setTemplate('initi_two');
                    $data['jsFunction'] = 'WorkflowModalShow';
                    $data['modalNameId'] = 'workflow-refund-modal';
                    $data['workflow'] = $this->workflow;
                    //查询账单
                    $criter = new CDbCriteria;
                    // $criter->compare('payment_type', array('tuition','bus','lunch'));
                    $criter->compare('schoolid', $this->workflow->schoolId);
                    $criter->compare('childid',$refund->childid);
                    $criter->compare('status', Invoice::STATS_PAID);
                    $criter->compare('`inout`', 'in');
                    $criter->addCondition('enddate>='.$exte1);
                    $invoice = Invoice::model()->findAll($criter);
                    $data['invoices'] = $invoice;
                    return $data;
                } catch (Exception $ex) {
                    $transaction->rollBack();
                    $this->controller->addMessage('state', 'fail');
                    $this->controller->addMessage('message', $ex->getMessage());
                    $this->controller->showMessage();
                }
            }else{
                $this->controller->addMessage('state', 'fail');
                $errs = current($refund->getErrors());
                if ($errs)
                    $this->controller->addMessage('message', $errs[0]);
            }
            $this->controller->showMessage();
        }
    }
    
    /**
     * 第一节点（第二步保存）函数
     */
    public function twoSave(){
        $invoiceIds = Yii::app()->request->getPost('invoiceId');
        if (is_array($invoiceIds) && count($invoiceIds)){
            $invoiceObj = Invoice::model()->findAllByPk($invoiceIds);
            $refund = new Refund();
            $refundConfig = array();
            foreach ($invoiceObj as $invoice){
                //已使用的预缴学费不可退费
                if ($invoice->payment_type == 'deposit') {
                    $balance = DepositHistory::model()->getChildDepositBalance($invoice->childid, $invoice->calendar_id);
                    if ($balance <= 0) {
                        $this->controller->addMessage('state', 'fail');
                        $this->controller->addMessage('message',Yii::t('invoice', '预缴学费已用完.'));
                        $this->controller->showMessage();
                    }
                }
                //start Year
                $calendar = Calendar::model()->getCalendarInfo($invoice->calendar_id, array('startyear'));
                if (!is_object($refundConfig[$calendar['startyear']])){
                    $refundConfig[$calendar['startyear']] = OA::getPolicy('refund', $calendar['startyear'],$invoice->schoolid);
                }
                $refundSignConfig = $refundConfig[$calendar['startyear']];
                if (isset($refundSignConfig->configs['REFUND_TYPE'][$invoice->payment_type]['path'])){
                    Yii::import(sprintf($refundSignConfig->configs['REFUND_TYPE'][$invoice->payment_type]['path'],$invoice->payment_type));
                    if (class_exists($refundSignConfig->configs['REFUND_TYPE'][$invoice->payment_type]['name'])){
                        $_class = $refundSignConfig->configs['REFUND_TYPE'][$invoice->payment_type]['name'];
                        $refund->addObserver(new $_class($invoice,$refundSignConfig,$this->workflow));
                    }
                }
            }
            $refundList = $refund->trigger();
            $this->setTemplate('initi_three');
            $data['jsFunction'] = 'WorkflowModalShow';
            $data['modalNameId'] = 'workflow-refund-modal';
            $data['workflow'] = $this->workflow;
            $data['invoices'] = $invoiceObj;
            $data['refundList'] = $refundList;
            return $data;
        }else{
            $this->controller->addMessage('state', 'fail');
            $this->controller->addMessage('message',Yii::t('invoice', '请勾选 [ 帐单 ]'));
            $this->controller->showMessage();
        }
    }
    
    /**
     * 最终保存
     */
    public function threeSave(){
        if (isset($_POST['InvoiceChildRefund'])){
            //查询账单
            $invoices = Invoice::model()->findAllByPk(array_keys($_POST['InvoiceChildRefund']));
            $invoiceList = array();
            foreach ($invoices as $invoice){
                $invoiceList[$invoice->invoice_id] = $invoice->getAttributes();
            }
            $objs = array();
            //验证
            foreach ($_POST['InvoiceChildRefund'] as $invoicKey=>$invoiceVal){
                $refundFee = new RefundFeeThreeForm();
                $refundFee->invoice_id = $invoicKey;
                $refundFee->title = $invoiceVal['title'];
                $refundFee->startdate = isset($invoiceVal['startdate']) ? strtotime($invoiceVal['startdate']) : '';
                $refundFee->enddate = isset($invoiceVal['enddate']) ? strtotime($invoiceVal['enddate']) : '';
                $refundFee->amount = $invoiceVal['amount'];
                if ($refundFee->validate()){
                    $objs[] = $refundFee;
                }else{
                    $this->controller->addMessage('state', 'fail');
                    $errs = current($refundFee->getErrors());
                    if ($errs)
                        $this->controller->addMessage('message', $errs[0]);
                    $this->controller->showMessage();
                }
                // 验证退费区间的正确性
                $criter = new CDbCriteria;
                $criter->compare('invoice_id', $invoicKey);
                $serviceInfo = ChildServiceInfo::model()->findAll($criter);
                if (in_array($invoiceList[$invoicKey]['payment_type'], array('tuition', 'lunch', 'bus')) && empty($serviceInfo)) {
                    $this->controller->addMessage('state', 'fail');
                    $this->controller->addMessage('message', $invoiceVal['title'] . '退费区间出错了');
                    $this->controller->showMessage();
                }
                if ($serviceInfo) {
                    $flag = 0;
                    foreach ($serviceInfo as $v) {
                        if ($refundFee->startdate<=$refundFee->enddate&&$v->startdate<=$refundFee->startdate&&$v->enddate>=$refundFee->enddate) {
                            $flag = 1;
                            break;
                        }
                    }
                    unset($serviceInfo);
                    if ($flag != 1) {
                        $this->controller->addMessage('state', 'fail');
                        $this->controller->addMessage('message', '退费区间出错了');
                        $this->controller->showMessage();
                    }
                }
            }

            $processModel = $this->workflow->getWorkflowProcess(true);
            //保存
            $transaction = Yii::app()->db->beginTransaction();
            try{
                $refundChildIds = array();
                foreach ($objs as $invoice){
                    $childRefund = new InvoiceChildRefund();
                    $childRefund->on_invoice_id = $invoice->invoice_id;
                    $childRefund->childid = $invoiceList[$invoice->invoice_id]['childid'];
                    $childRefund->yid = $invoiceList[$invoice->invoice_id]['calendar_id'];
                    $childRefund->title = $invoice->title;
                    $childRefund->memo = $processModel->process_desc;
                    $childRefund->classid = $invoiceList[$invoice->invoice_id]['classid'];
                    $childRefund->schoolid = $invoiceList[$invoice->invoice_id]['schoolid'];
                    $childRefund->payment_type = $invoiceList[$invoice->invoice_id]['payment_type'];
                    $childRefund->startdate = $invoice->startdate;
                    $childRefund->enddate = $invoice->enddate;
                    $childRefund->amount = $invoice->amount;
                    $childRefund->userid = Yii::app()->user->id;
                    $childRefund->timestamp = time();
                    $childRefund->refund_type = $this->workflow->opertationObj->exte3;
                    $childRefund->status = InvoiceChildRefund::STATS_AWAITING;
                    if (!$childRefund->save()){
                        $errs = current($childRefund->getErrors());
                        $transaction->rollback();
                        $this->controller->addMessage('state', 'fail');
                        $this->controller->addMessage('message', "保存失败，原因：{$errs[0]}！");
                        $this->controller->showMessage();
                    }else{
                        $refundChildIds[] = $childRefund->rid;
                    }
                }
                 //保存工作流业务流程
                $operationData = array('id'=>$this->workflow->getOperateId(),'operation_type'=>WorkflowConfig::WORKFLOW_TYPE_REFUND,'operation_object_id'=>$this->workflow->getOperateObjId(),'defination_id'=>$this->workflow->getWorkflowId(),
                            'branchid'=>$this->workflow->schoolId,'state'=>WorkflowOperation::WORKFLOW_STATS_UNOP,'current_node_index'=>$this->workflow->getCurrentNodeId(),
                            'exte1'=>$this->workflow->opertationObj->exte1,'exte2'=>$this->workflow->opertationObj->exte2,'exte3'=>$this->workflow->opertationObj->exte3,
                        );
                $operationRet = $this->workflow->saveFirstNodeForWorkflow($operationData,$refundChildIds,$processModel->process_desc,$transaction);
                if ($operationRet === false){
                    //$transaction->rollback();
                    $this->controller->addMessage('state', 'fail');
                    $this->controller->addMessage('message', '保存失败,原因：工作流底层更新错误！');
                    $this->controller->showMessage();
                }
                $transaction->commit();
            }catch(Exception $e){
                $transaction->rollBack();
                $this->controller->addMessage('state', 'fail');
                $this->controller->addMessage('message', $e->getMessage());
                $this->controller->showMessage();
            }
            //发邮件
            $this->jumpMail();
            $this->controller->addMessage('state', 'success');
            $this->controller->addMessage('message', '成功');
            $this->controller->addMessage('data',array('modalNameId'=>'workflow-refund-modal','operatorId'=>$this->workflow->getOperateId(),'html'=>$this->workflow->getNodeListUseHtml()));
            $this->controller->addMessage('callback', 'saveWorkflowOperator');
            $this->controller->showMessage();
        }
    }
    

    public function show(){
        $data['jsFunction'] = 'WorkflowModalShow';
        $data['modalNameId'] = 'workflow-refund-modal';
        $data['workflow'] = $this->workflow;
        $data['type'] = $this->refundType;
        $data['refundFee'] = self::REFUND_LEAVE_SCHOOL;
        if (($this->workflow->isFristNode() === true) && $this->workflow->getOperateStatus() == WorkflowOperation::WORKFLOW_STATS_STEP){
            if ($this->step === 'one'){
                $this->setTemplate('initi');
                //业务
                $process = $this->workflow->getWorkflowProcess(TRUE);
                //查询孩子
                $ftsChild = FtsChild::model()->findByPk($this->workflow->getOperateObjId());
                $data['processDesc'] = $process->process_desc;
                preg_match('/{(.*)}/', $ftsChild->tdata, $matches);
                $data['ftsChild'] = array($ftsChild->id =>$matches[1]);
                $data['select'] = $ftsChild->id;
            }elseif($this->step === 'two'){
                $this->setTemplate('initi_two');
                //查询账单
                Yii::import('common.models.invoice.Invoice');
                $criter = new CDbCriteria;
                $criter->compare('payment_type', array('tuition','bus','lunch'));
                $criter->compare('schoolid', $this->workflow->schoolId);
                $criter->compare('childid',$this->workflow->getOperateObjId());
                $criter->compare('status', Invoice::STATS_PAID);
                $criter->compare('`inout`', 'in');
                $criter->addCondition('enddate>='.$this->workflow->opertationObj->exte1);
                $invoice = Invoice::model()->findAll($criter);
                $data['invoices'] = $invoice;
            }
            return $data;
        }else{
            //show
            $this->setTemplate('show');
            Yii::import('common.models.workflow.WorkflowOperationDetail');
            $criter = new CDbCriteria;
            $criter->compare('operation_id', $this->workflow->getOperateId());
            $criter->index = 'operation_object_id';
            $operationDetail = WorkflowOperationDetail::model()->findAll($criter);
            $refundList = InvoiceChildRefund::model()->with('Invoice')->findAllByPk(array_keys($operationDetail));
            $profile = ChildProfileBasic::model()->findByPk($this->workflow->getOperateObjId());
            // 计算退费总金额
            $total = 0;
            foreach ($refundList as $refundInvoice) {
                $total += $refundInvoice->amount;
            }
            //处理POST请求
            if (Yii::app()->request->isPostRequest){
                Yii::import('common.models.invoice.InvoiceTransaction');
                Yii::import('common.models.invoice.ChildCredit');
                $buttonCategory = Yii::app()->request->getPost('buttonCategory',0);
                $memo = Yii::app()->request->getPost('memo','');
                $workflowStatus = WorkflowOperation::WORKFLOW_STATS_UNOP;
                if (empty($memo)){
                    $this->controller->addMessage('state', 'fail');
                    $this->controller->addMessage('message', '提交失败，原因：意见必填！');
                    $this->controller->showMessage();
                }
                $transaction = Yii::app()->db->beginTransaction();
                try{
                     //被拒绝
                    if ($buttonCategory == WorkflowOperation::WORKFLOW_STATS_OPDENY){
                        foreach ($refundList as $invoice){
                            $invoice->status = InvoiceChildRefund::STATS_CANCELLED;
                            if (!$invoice->save()){
                                $transaction->rollback();
                                $this->controller->addMessage('state', 'fail');
                                $this->controller->addMessage('message', '保存失败，原因：更新退费中间表失败！');
                                $this->controller->showMessage();
                            }
                        }
                        $workflowStatus = WorkflowOperation::WORKFLOW_STATS_OPDENY;
                    }else{
                        $end = false;
                        $exte3 = $this->workflow->getOperateValue('exte3');
                        // 根据退费类型判断是否需要根据退费金额跳节点
                        if ($exte3 != self::REFUND_CHANGE) {
                            // 计算退费总金额
                            $total = 0;
                            foreach ($refundList as $refundInvoice) {
                                $total += $refundInvoice->amount;
                            }
                            if ($total < $this->workflow->nodeObj->field) {
                                $end = true;
                            }
                        }
                        // 如果是服务修改则永不需要后面节点审核
                        if ($exte3 == self::REFUND_CHANGE && $this->workflow->nodeObj->field > 10000) {
                            $end = true;
                        }
                        
                        // 判断当前节点是否有权限终结流程
                        // 判断是不是最后节点
                        if ($end === true || $this->workflow->isEndNode()){
                            $profileCreditAmount = $profile->credit;
                            foreach ($refundList as $invoice){
                                //invoice
                                $newInvoice = new Invoice();
                                $newInvoice->setAttributes($invoice->Invoice->getAttributes());
                                $newInvoice->title = $invoice->title;
                                $newInvoice->memo = $invoice->memo;
                                $newInvoice->amount = $invoice->amount;
                                $newInvoice->original_amount = $invoice->amount;
                                $newInvoice->nodiscount_amount = 0.00;
                                $newInvoice->inout = Invoice::INVOICE_INOUT_OUT;
                                $newInvoice->startdate = $invoice->startdate;
                                $newInvoice->enddate = $invoice->enddate;
                                $newInvoice->timestamp = time();
                                $newInvoice->userid = Yii::app()->user->id;
                                $newInvoice->apportion_status = 0;
                                $newInvoice->receivable_status = 0;
                                if (!$newInvoice->save()){
                                    $errs = current($newInvoice->getErrors());
                                    $transaction->rollback();
                                    $this->controller->addMessage('state', 'fail');
                                    $this->controller->addMessage('message', "新创建账单失败，原因：{$errs[0]}");
                                    $this->controller->showMessage();
                                }
                                //transaction
                                $tran = new InvoiceTransaction();
                                $tran->setAttributes($newInvoice->getAttributes());
                                $tran->timestampe = time();
                                $tran->transactiontype = InvoiceTransaction::TYPE_TOCREDIT;
                                $tran->operator_uid = $newInvoice->userid;
                                $tran->ufida_income = 0;
                                if (!$tran->save()){
                                    $errs = current($tran->getErrors());
                                    $transaction->rollback();
                                    $this->controller->addMessage('state', 'fail');
                                    $this->controller->addMessage('message', "新创建账单流水失败，原因：{$errs[0]}");
                                    $this->controller->showMessage();
                                }
                                $invoice->status = InvoiceChildRefund::STATS_COMPLETED;
                                $invoice->invoice_id = $newInvoice->invoice_id;
                                $invoice->transaction_id = $tran->id;
                                if (!$invoice->save()){
                                    $errs = current($invoice->getErrors());
                                    $transaction->rollback();
                                    $this->controller->addMessage('state', 'fail');
                                    $this->controller->addMessage('message', "保存失败，原因：{$errs[0]}！");
                                    $this->controller->showMessage();
                                }
                                $credit = new ChildCredit();
                                $profileCreditAmount = $profileCreditAmount+$invoice->amount;
                                $credit->classid = $newInvoice->classid;
                                $credit->schoolid = $newInvoice->schoolid;
                                $credit->yid = $newInvoice->calendar_id;
                                $credit->childid = $newInvoice->childid;
                                $credit->amount = $newInvoice->amount;
                                $credit->inout = $invoice->Invoice->inout;
                                $credit->itemname = $newInvoice->payment_type;
                                $credit->invoice_id = $newInvoice->invoice_id;
                                $credit->transaction_id = $tran->id;
                                $credit->userid = $newInvoice->userid;
                                $credit->transactiontype = 0;
                                $credit->balance = $profileCreditAmount;
                                $credit->cash_status = 0;
                                $credit->updated_timestamp = time();
                                if (!$credit->save()){
                                    $errs = current($credit->getErrors());
                                    $transaction->rollback();
                                    $this->controller->addMessage('state', 'fail');
                                    $this->controller->addMessage('message', "个人账户操作失败，原因：{$errs[0]}！");
                                    $this->controller->showMessage();
                                }
                                //更新Servers_info
                                if (in_array($invoice->payment_type, array('lunch','bus','tuition'))){
                                    $criter = new CDbCriteria();
                                    $criter->compare('invoice_id', $invoice->on_invoice_id);
                                    $criter->addCondition("startdate<={$invoice->startdate} and enddate>={$invoice->enddate}");
                                    $serviceInfo = ChildServiceInfo::model()->find($criter);
                                    if (empty($serviceInfo)){
                                        $transaction->rollback();
                                        $this->controller->addMessage('state', 'fail');
                                        $this->controller->addMessage('message',"账单：{$invoice->title}({$invoice->on_invoice_id})退费区间出错！");
                                        $this->controller->showMessage();
                                    }
                                    if($invoice->refund_type != $this::REFUND_SPECIALT){

                                        //全退情况,删除serviceInfo表此帐单的信息
                                        if ($serviceInfo->startdate == $invoice->startdate && $serviceInfo->enddate == $invoice->enddate){
                                            if (!$serviceInfo->delete()){
                                                $transaction->rollback();
                                                $this->controller->addMessage('state', 'fail');
                                                $this->controller->addMessage('message', "删除失败！");
                                                $this->controller->showMessage();
                                            }
                                        //退上半期情况
                                        }elseif($serviceInfo->startdate==$invoice->startdate && $invoice->enddate<$serviceInfo->enddate){
                                            $serviceInfo->startdate = strtotime("+1 day",$invoice->enddate);
                                            if (!$serviceInfo->save()){
                                                $errs = current($serviceInfo->getErrors());
                                                $transaction->rollback();
                                                $this->controller->addMessage('state', 'fail');
                                                $this->controller->addMessage('message', "更新账单开始日期失败，原因：{$errs[0]}！");
                                                $this->controller->showMessage();
                                            }
                                        //退下半期情况
                                        }elseif ($invoice->startdate > $serviceInfo->startdate && $invoice->enddate==$serviceInfo->enddate){
                                            $serviceInfo->enddate = strtotime("-1 day",$invoice->startdate);
                                            if (!$serviceInfo->save()){
                                                $errs = current($serviceInfo->getErrors());
                                                $transaction->rollback();
                                                $this->controller->addMessage('state', 'fail');
                                                $this->controller->addMessage('message', "更新账单结束日期失败，原因：{$errs[0]}！");
                                                $this->controller->showMessage();
                                            }
                                        //退中间的情况
                                        }elseif ($invoice->startdate > $serviceInfo->startdate && $invoice->enddate<$serviceInfo->enddate){
                                             //新建帐单
                                            $newServiceInfo = new ChildServiceInfo();
                                            $newServiceInfo->setAttributes($serviceInfo->getAttributes());
                                            $newServiceInfo->startdate = strtotime("+1 day",$invoice->enddate);
                                            if (!$newServiceInfo->save()){
                                                $errs = current($newServiceInfo->getErrors());
                                                $transaction->rollback();
                                                $this->controller->addMessage('state', 'fail');
                                                $this->controller->addMessage('message', "新创建帐单记录失败，原因：{$errs[0]}！");
                                                $this->controller->showMessage();
                                            }
                                            //更新原帐单的结束日期
                                            $serviceInfo->enddate = strtotime("-1 day",$invoice->startdate);
                                            if (!$serviceInfo->save()){
                                                $errs = current($serviceInfo->getErrors());
                                                $transaction->rollback();
                                                $this->controller->addMessage('state', 'fail');
                                                $this->controller->addMessage('message', "更新账单结束日期失败，原因：{$errs[0]}！");
                                                $this->controller->showMessage();
                                            }
                                        }else{
                                            $transaction->rollback();
                                            $this->controller->addMessage('state', 'fail');
                                            $this->controller->addMessage('message', "操作错误！");
                                            $this->controller->showMessage();
                                        }
                                    }
                                }
                                //预交学费更新表ivy_child_deposit_history
                                if ($tran->payment_type == 'deposit') {
                                    $depositHistory = new DepositHistory();
                                    $depositHistory->childid = $tran->childid;
                                    $depositHistory->yid = $tran->calendar_id;
                                    $depositHistory->amount = $tran->amount;
                                    $depositHistory->balance = $depositHistory->getChildDepositBalance($tran->childid, $tran->calendar_id) - $tran->amount;
                                    $depositHistory->inout = $tran->inout;
                                    $depositHistory->tran_id = $tran->id;
                                    $depositHistory->timestamp = $tran->timestampe;
                                    $depositHistory->create_uid = $tran->operator_uid;
                                    $depositHistory->ufida_status = 0;
                                    if (!$depositHistory->save()){
                                        $errs = current($depositHistory->getErrors());
                                        $transaction->rollback();
                                        $this->controller->addMessage('state', 'fail');
                                        $this->controller->addMessage('message', "预缴学费历史表添加失败，原因：{$errs[0]}");
                                        $this->controller->showMessage();
                                    }
                                }
                            }
                            // 如果退学或毕业，更改孩子状态
                            if (in_array($this->workflow->getOperateValue('exte3'), array(self::REFUND_LEAVE_SCHOOL, self::REFUND_GRADUATE))){
                                Yii::import('common.models.child.ChildLeaveSchool');
                                $leaveSchool = ChildLeaveSchool::model()->findByPk($this->workflow->getOperateObjId());
                                if (empty($leaveSchool)){
                                    $leaveSchool = new ChildLeaveSchool();
                                }
                                $leaveSchool->childid = $this->workflow->getOperateObjId();
                                $leaveSchool->leave_school_time = $this->workflow->getOperateValue('exte1') ? $this->workflow->getOperateValue('exte1') : $invoice->startdate;
                                $leaveSchool->status = $this->workflow->getOperateValue('exte3');
                                if (!$leaveSchool->save()){
                                    $errs = current($leaveSchool->getErrors());
                                    $transaction->rollback();
                                    $this->controller->addMessage('state', 'fail');
                                    $this->controller->addMessage('message', "保存失败，原因：{$errs[0]}！");
                                    $this->controller->showMessage();
                                }
                                // $profile->status = ChildProfileBasic::STATS_DROPPINGOUT;
                            }
                            $profile->credit = $profileCreditAmount;
                            if (!$profile->save()){
                                $errs = current($profile->getErrors());
                                $transaction->rollback();
                                $this->controller->addMessage('state', 'fail');
                                $this->controller->addMessage('message', "保存失败，原因：{$errs[0]}！");
                                $this->controller->showMessage();
                            }
                            $workflowStatus = WorkflowOperation::WORKFLOW_STATS_OPED;
                            $this->workflow->endNode = true;
                        }
                    }
                    if ($this->workflow->saveWorkflow($memo,$workflowStatus,$transaction) === false){
                        $this->controller->addMessage('state', 'fail');
                        $this->controller->addMessage('message', '保存失败');
                        $this->controller->showMessage();
                    }
                    //发邮件
                    $transaction->commit();
                    $this->jumpMail();
                    $this->controller->addMessage('state', 'success');
                    $this->controller->addMessage('message', '保存成功');
                    $this->controller->addMessage('data',array('modalNameId'=>'workflow-refund-modal','operatorId'=>$this->workflow->getOperateId(),'html'=>$this->workflow->getNodeListUseHtml()));
                    $this->controller->addMessage('callback', 'saveWorkflowOperator');
                    $this->controller->showMessage();
                } catch (Exception $ex) {
                    $transaction->rollBack();
                    $this->controller->addMessage('state', 'fail');
                    $this->controller->addMessage('message', $ex->getMessage());
                    $this->controller->showMessage();
                }
            }
            $data['refundList'] = $refundList;
            $data['profile'] = $profile;
            $data['type'] = $this->refundType;
            return $data;
        }
    }
    
    public function jumpMail() {
        $branchList = Branch::model()->getBranchList();
        $branchTitle = $branchList[$this->workflow->getOperateValue('branchid')];
        $childName = WorkflowConfig::getChildName($this->workflow->getOperateValue('operation_object_id'));
        $workflowTitle = CommonUtils::autoLang($this->workflow->definationObj->defination_name_cn,$this->workflow->definationObj->defination_name_en);
        $nodesTitle = CommonUtils::autoLang($this->workflow->nodesList[$this->workflow->getNextNodeId()]->node_name_cn,$this->workflow->nodesList[$this->workflow->getNextNodeId()]->node_name_en);
        if ($this->workflow->getOperateStatus() == WorkflowOperation::WORKFLOW_STATS_OPDENY){
            $subject = sprintf('%s 校园 %s 提出 %s申请被拒绝',$branchTitle,$childName,$workflowTitle);
        }else{
            if ($this->workflow->isEndNode() || $this->workflow->endNode){
                $subject = sprintf('%s 校园 %s 提出 %s申请已完成',$branchTitle,$childName,$workflowTitle);
            }else{
                $subject = sprintf('%s 校园 %s 提出 %s申请需要您处理',$branchTitle,$childName,$workflowTitle);
            }
        }
        // 微信推送
        $this->workflow->wechatPush();
        $link = $this->controller->createUrl('/workflow/index', array('branchId'=>$this->workflow->schoolId));
        $body = $this->controller->renderPartial('/mail/templater',array('branchTitle'=>$branchTitle,'childName'=>$childName,'workflowTitle'=>$workflowTitle,'nodes'=>$nodesTitle,'link'=>$link,'workflow'=>$this->workflow),true);
        return $this->workflow->sendEmail($subject,$body);
    }
    
    /**
     * 删除未完成工作流
     */
    public function delete() {
        $request = Yii::app()->request;
        if ($this->workflow->getOperateStatus() == WorkflowOperation::WORKFLOW_STATS_STEP){
            Yii::import('common.models.workflow.WorkflowOperationDetail');
            $criter = new CDbCriteria;
            $criter->compare('operation_id', $this->workflow->getOperateId());
            $criter->index = 'operation_object_id';
            $operationDetail = WorkflowOperationDetail::model()->findAll($criter);
            $transaction = Yii::app()->db->beginTransaction();
            try{
                if (!empty($operationDetail)){
                    $criter = new CDbCriteria;
                    $criter->compare('rid', array_keys($operationDetail));
                    if (!InvoiceChildRefund::model()->deleteAll($criter)){
                        $transaction->rollBack();
                        $this->controller->addMessage('state', 'fail');
                        $this->controller->addMessage('message', "删除失败!");
                        $this->controller->showMessage();
                    }
                }
                if (!$this->workflow->deleteOperationNode($transaction)){
                    $this->controller->addMessage('state', 'fail');
                    $this->controller->addMessage('message', "工作流底层删除失败!");
                    $this->controller->showMessage();
                }
                $transaction->commit();
                $this->controller->addMessage('state', 'success');
                $this->controller->addMessage('message', '工作流删除成功');
                $this->controller->addMessage('data',$this->workflow->getOperateId());
                $this->controller->addMessage('callback', 'deleteWorkflowOperator');
                $this->controller->showMessage();
            } catch (Exception $ex) {
                $transaction->rollBack();
                $this->controller->addMessage('state', 'fail');
                $this->controller->addMessage('message', $ex->getMessage());
                $this->controller->showMessage();
            }
        }
    }
    
    public function setTemplate($name){
        $this->template = $name;
    }
    
    public function getTemplate(){
        return $this->template;
    }
}