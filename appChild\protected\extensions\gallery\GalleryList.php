<?php
 
class GalleryList extends CWidget {
 
    public $pics;
    public $startyear;
    public $nurl;
    public $albumid;

    public function init() {
        parent::init();
    }
 
    /**
     * Run the widget, including the js files.
     */
    public function run() {
 
//        $dir = dirname(__FILE__) . DIRECTORY_SEPARATOR;
//        $baseUrl = Yii::app()->getAssetManager()->publish($dir . 'assets', false, -1, YII_DEBUG);
        $baseUrl = Yii::app()->theme->baseUrl.'/widgets/gallery';
 
        $clientScript = Yii::app()->getClientScript();
        
//        $clientScript->registerCssFile($baseUrl . '/gallery.css');
        $clientScript->registerCssFile($baseUrl . '/gallery.css?t='.Yii::app()->params['refreshAssets']);
 
        $clientScript->registerScriptFile(Yii::app()->theme->baseUrl.'/js/i18n-1.0.js?t='.Yii::app()->params['refreshAssets']);
        
        $clientScript->registerScriptFile($baseUrl . '/gallery.js?t='.Yii::app()->params['refreshAssets']);
        $clientScript->registerScriptFile($baseUrl . '/udctrack.js');
        $clientScript->registerScriptFile($baseUrl . '/a1.js');
        
        ChildMedia::setStartYear($this->startyear);
        $criteria = new CDbCriteria();
        $criteria->compare('id', array_keys($this->pics));
        $criteria->compare('type', 'photo');
        $medias = ChildMedia::model()->findAll($criteria);
 
        $this->render('gallerylist', array('baseUrl'=>$baseUrl, 'medias'=>$medias, 'pics'=>$this->pics, 'nurl'=>  $this->nurl, 'startyear'=>  $this->startyear, 'albumid'=>  $this->albumid));
    }
 
}
?>