body{
    background-color:#fcfcfc;
    /*font-family: Tahoma, Helvetica, Arial, "Microsoft Yahei", "微软雅黑", <PERSON><PERSON><PERSON><PERSON>, "华文细黑", sans-serif;*/
    /*font-size: 12px;*/
    /*line-height: 1.42857143;*/
    /*color: #333333;*/
}

a, a:hover, a:active{
    text-decoration: none;
}

.settings, .settings:focus{
    display: block;
    color: #fff;
    font-size: 18px;
    float: right;
    margin-right: 10px;
    line-height: 50px;
    text-decoration: none;
}

.navbar-toggle{
    border: 0;
    float: left;
    display: inline;
	margin-right: 0;
}

.spmenu-left{
    position: fixed;
    background-color: #fff;
    width: 240px;
    height: 100%;
    z-index: 2000;
    left: -240px;
    -webkit-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    transition: all 0.3s ease;
    overflow: hidden;
}

.spmenu-left-open{
    left: 0;
}

.spmenu-header{
    height: 120px;
    background-color: #00a651;
    padding: 18px 0;
}

.spmenu-header a{
    color: #fff;
}

.spmenu-body{
    height: 100%;
    overflow-y: auto;
    padding-top: 10px;
}

.spmenu-body>h6{
    padding: 0 15px;
}

.spmenu-body .list-group-item{
    border: 0;
    border-radius: 0;
    padding: 15px;
}

.spmenu-body .list-group-item span.glyphicon{
    margin-right: 10px;
}

.container-fluid{
    padding-top: 70px;
}

#shade{
    position: fixed;
    width: 100%;
    height: 100%;
    z-index: 1999;
    opacity: .7;
    background-color: #000;
}

.form-signin {
    max-width: 330px;
    padding: 15px;
    margin: 100px auto 0 auto;
}
.form-signin .form-signin-heading,
.form-signin .checkbox {
    margin-bottom: 10px;
}
.form-signin .checkbox {
    font-weight: normal;
}
.form-signin .form-control {
    position: relative;
    height: auto;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    padding: 10px;
    font-size: 16px;
}
.form-signin .form-control:focus {
    z-index: 2;
}
.form-signin input[type="text"] {
    margin-bottom: -1px;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;
}
.form-signin input[type="password"] {
    margin-bottom: 10px;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
}