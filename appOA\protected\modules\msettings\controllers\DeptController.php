<?php

class DeptController extends ProtectedController
{
    public $dialogWidth = 600;

    public function init(){
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'sysConfig';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site','System Management');

        Yii::import('common.models.hr.*');
    }

    public function actionIndex($category='')
    {
        $criteria = new CDbCriteria();
        $criteria->compare('t.category', $category);
        $criteria->order='t.status desc, t.weight';
        $items = HrDepartment::model()->with('title')->findAll($criteria);
        $this->render('index', array('category'=>$category, 'items'=>$items));
    }

    public function actionDeptEdit($id=0, $category='')
    {
        $this->layout='//layouts/dialog';

        $model = HrDepartment::model()->findByPk($id);
        if($model == null){
            $model = new HrDepartment;
            $model->category=$category;
        }

        if(isset($_POST['HrDepartment'])){
            $model->attributes = $_POST['HrDepartment'];
            $model->userid = Yii::app()->user->id;
            $model->timestamp = time();
            if($model->save()){
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message','success'));
                $this->addMessage('refresh', true);
            }
            else{
                $this->addMessage('state', 'fail');
                $errs = current($model->getErrors());
                $this->addMessage('message', $errs?$errs[0]:Yii::t('message','Failed!'));
            }
            $this->showMessage();
        }
        $this->render('deptedit', array('model'=>$model));
    }

    public function actionDeptDel()
    {
        if(Yii::app()->request->isPostRequest){
            $id = Yii::app()->request->getParam('id', 0);
            if($id){
                $count = DepPosLink::model()->countByAttributes(array('department_id'=>$id));
                if($count){
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', '请先解除部门下面的职位再删除！');
                }
                else{
                    $model = HrDepartment::model()->findByPk($id);
                    if($model->delete()){
                        $this->addMessage('state', 'success');
                        $this->addMessage('message', '删除成功！');
                        $this->addMessage('refresh', true);
                    }
                    else{
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', '删除失败！');
                    }
                }
            }
            else{
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '参数错误！');
            }
            $this->showMessage();
        }
    }

    public function actionTitleEdit($id=0, $category='')
    {
        $this->layout='//layouts/dialog';

        $model = HrPosition::model()->with(array('link', 'role'))->findByPk($id);
        if($model == null)
            $model = new HrPosition;
        if($model->link == null)
            $model->link = new DepPosLink;
        if($model->role == null)
            $model->role = new HrTitleRole;

        if(isset($_POST['HrPosition']) && isset($_POST['DepPosLink'])){
            $model->attributes = $_POST['HrPosition'];
            if($model->save()){
                $model->link->position_id = $model->id;
                $model->link->attributes = $_POST['DepPosLink'];
                if($model->link->save()){
                    if($model->id){
                        HrTitleRole::model()->deleteAllByAttributes(array('title_id'=>$model->id));
                        $roles = Yii::app()->request->getParam('HrPosition', array());
                        foreach($roles['_role'] as $role){
                            $modelRole = new HrTitleRole;
                            $modelRole->title_id=$model->id;
                            $modelRole->role=$role;
                            $modelRole->save();
                        }
                        $this->addMessage('state', 'success');
                        $this->addMessage('message', Yii::t('message','success'));
                        $this->addMessage('refresh', true);
                    }
                }
                else{
                    $this->addMessage('state', 'fail');
                    $errs = current($model->link->getErrors());
                    $this->addMessage('message', $errs?$errs[0]:Yii::t('message','Failed!'));
                }
            }
            else{
                $this->addMessage('state', 'fail');
                $errs = current($model->getErrors());
                $this->addMessage('message', $errs?$errs[0]:Yii::t('message','Failed!'));
            }
            $this->showMessage();
        }
        $criteria = new CDbCriteria();
        if($category)
            $criteria->compare('category', $category);
        $criteria->compare('status', 1);
        $criteria->order='weight';
        $depts = HrDepartment::model()->findAll($criteria);

        Yii::import('application.modules.srbac.models.*');
        $criteria = new CDbCriteria();
        $criteria->compare('type', 2);
        $roles = AuthItem::model()->findAll($criteria);
        if(is_array($model->role)){
            foreach($model->role as $r){
                $model->_role[] = $r->role;
            }
        }

        $this->render('titleedit', array('model'=>$model, 'depts'=>$depts, 'roles'=>$roles));
    }

    public function actionTitleDel()
    {
        if(Yii::app()->request->isPostRequest){
            $id = Yii::app()->request->getParam('id', 0);
            if($id){
                $criteria = new CDbCriteria();
                $criteria->compare('profile.occupation_en', $id);
                // $criteria->compare('t.level', 1);
                $users = User::model()->with('profile')->findAll($criteria);

                if (count($users) > 0) {
                    $msg = '';
                    foreach ($users as $user) {
                        $msg .= $user->getName(). '<br>';
                    }
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', '职位有人占用！<br>'.$msg);
                    $this->showMessage();
                }

                HrPosition::model()->findByPk($id)->delete();
                DepPosLink::model()->findByPk($id)->delete();
                HrTitleRole::model()->deleteAllByAttributes(array('title_id'=>$id));
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message','success'));
                $this->addMessage('refresh', true);
            }
            else{
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '参数错误！');
            }
            $this->showMessage();
        }
    }
}