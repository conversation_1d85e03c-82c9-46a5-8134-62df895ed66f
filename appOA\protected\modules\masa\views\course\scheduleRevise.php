<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span>
    </button>
    <h4 class="modal-title" id="exampleModalLabel">调整课程时间--<?php echo $course->title_cn; ?></h4>
</div>

<div class="modal-body">
    <div class="row">
        <div class="col-md-12">
            <?php foreach ($course->schedule->asaScheduleItem as $item): ?>
                <?php
                $sid = $item->id;
                $revised = false;
                if (isset($revises[$item->id])) {
                    $revised = true;
                    $timeStart = explode(':', $revises[$item->id]->time_start);
                    $timeEnd = explode(':', $revises[$item->id]->time_end);
                    $revise_date = $revises[$item->id]->schedule_date;
                    $timestart_hour = $timeStart[0];
                    $timestart_minute = $timeStart[1];
                    $timeend_hour = $timeEnd[0];
                    $timeend_minute = $timeEnd[1];
                } else {
                    $timeStart = explode(':', $item->time_start);
                    $timeEnd = explode(':', $item->time_end);
                    $revise_date = $item->schedule_date;
                    $timestart_hour = $timeStart[0];
                    $timestart_minute = $timeStart[1];
                    $timeend_hour = $timeEnd[0];
                    $timeend_minute = $timeEnd[1];
                }
                $item->schedule_date = date('Y-m-d', strtotime($item->schedule_date));
                $revise_date = date('Y-m-d', strtotime($revise_date));
                $weekArray = array('周日', '周一', '周二', '周三', '周四', '周五', '周六');
                $week = $weekArray[date('w', strtotime($item->schedule_date))];
                ?>
                <div class="form-group">
                    <div class="col-md-4 form-inline">
                        <span class="label label-info"><?php echo $week; ?></span>
                        <?php if ($revised) echo '<s>'; ?>
                        <?php echo $item->schedule_date . ' ' . $item->time_start . '-' . $item->time_end; ?>
                        <?php if ($revised) echo '</s>'; ?>
                        <div class="form-control input-sm" style="visibility: hidden"></div>
                    </div>
                    <div class="col-md-8 form-inline">
                        <input id="revise_date_<?php echo $sid; ?>" class="form-control input-sm datepicker" type="text"
                               placeholder="调整日期" value="<?php echo $revise_date; ?>">
                        <select class="form-control input-sm" id="timestart_hour_<?php echo $sid; ?>">
                            <?php for ($i = 0; $i < 24; $i++): ?>
                                <?php if ($i == $timestart_hour) {
                                    $selected = 'selected=selected';
                                } else {
                                    $selected = '';
                                } ?>
                                <option
                                    value="<?php echo sprintf("%'.02d", $i) ?>" <?php echo $selected; ?>><?php echo sprintf("%'.02d", $i) ?></option>
                            <?php endfor; ?>
                        </select>
                        <select class="form-control input-sm" id="timestart_minute_<?php echo $sid; ?>">
                            <?php
                            $j = 0;
                            while ($j <= 60) {
                                if ($j == $timestart_minute) {
                                    $selected = 'selected=selected';
                                } else {
                                    $selected = '';
                                }
                                echo '<option ' . $selected . ' value="' . sprintf("%'.02d", $j) . '">';
                                echo sprintf("%'.02d", $j);
                                echo '</option>';
                                $j = $j + 5;
                            }
                            ?>
                        </select>
                        <select class="form-control input-sm" id="timeend_hour_<?php echo $sid; ?>">
                            <?php for ($i = 0; $i < 24; $i++): ?>
                                <?php if ($i == $timeend_hour) {
                                    $selected = 'selected=selected';
                                } else {
                                    $selected = '';
                                } ?>
                                <option
                                    value="<?php echo sprintf("%'.02d", $i); ?>" <?php echo $selected; ?>><?php echo sprintf("%'.02d", $i) ?></option>
                            <?php endfor; ?>
                        </select>
                        <select class="form-control input-sm" id="timeend_minute_<?php echo $sid; ?>">
                            <?php
                            $j = 0;
                            while ($j <= 60) {
                                if ($j == $timeend_minute) {
                                    $selected = 'selected=selected';
                                } else {
                                    $selected = '';
                                }
                                echo '<option ' . $selected . ' value="' . sprintf("%'.02d", $j) . '">';
                                echo sprintf("%'.02d", $j);
                                echo '</option>';
                                $j = $j + 5;
                            }
                            ?>
                        </select>
                        <button class="btn btn-primary btn-sm" onclick="revise(this, <?php echo $sid; ?>)">调整</button>
                    </div>
                    <div class="clearfix"></div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</div>
<div class="modal-footer">
    <button type="button" class="btn btn-default"
            data-dismiss="modal"><?php echo Yii::t('global', 'Cancel'); ?></button>
</div>
<script>
    $('.datepicker').datepicker({
        changeMonth: true,
        changeYear: true,
        dateFormat: 'yy-mm-dd'
    });
    function revise(btn, sid) {
        var cid = <?php echo $course->id; ?>;
        var gid = <?php echo $course->gid; ?>;
        var revise_date = $('#revise_date_' + sid).val();
        var timestart_hour = $('#timestart_hour_' + sid).val();
        var timestart_minute = $('#timestart_minute_' + sid).val();
        var timeend_hour = $('#timeend_hour_' + sid).val();
        var timeend_minute = $('#timeend_minute_' + sid).val();
        $(btn).addClass('disabled');
        $.ajax({
            'type': 'post',
            'url': '<?php echo $this->createUrl('scheduleRevise'); ?>',
            'data': {
                sid: sid,
                cid: cid,
                gid: gid,
                revise_date: revise_date,
                timestart_hour: timestart_hour,
                timestart_minute: timestart_minute,
                timeend_hour: timeend_hour,
                timeend_minute: timeend_minute
            },
            'dataType': 'json',
            'success': function (data) {
                if (data.state == 'success') {
                    alert('调整成功');
                } else {
                    alert('调整失败，' + data.message);
                }
                $(btn).removeClass('disabled');
            }
        });
    }
</script>