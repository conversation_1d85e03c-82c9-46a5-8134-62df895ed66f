<?php
class Handbook extends CWidget{
	public $childid;
	public $version;
    	
	public function run(){
		Yii::import('common.models.feedback.HandbookReport');

		$count = HandbookReport::model()->countByAttributes(
			array(
				'version' => $this->version,
				'childid' => $this->childid,
				'status' => HandbookReport::STATS_AGREE
			)
		);
		$childinfo = $this->controller->getChildInfo();

		if(!$count && !in_array($childinfo['stat'], array(ChildProfileBasic::STATS_GRADUATED, ChildProfileBasic::STATS_DROPPINGOUT, ChildProfileBasic::STATS_DROPOUT))){
			Yii::app()->clientScript->registerCssFile(Yii::app()->theme->baseUrl . "/css/notice.css");

			$this->render('handbook');
		}
	}
}