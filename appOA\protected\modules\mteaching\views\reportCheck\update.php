<?php $form=$this->beginWidget('CActiveForm', array(
    'id'=>'points-product-form',
    'enableAjaxValidation'=>false,
    'htmlOptions'=>array('class'=>'J_ajaxForm form-horizontal', 'role'=>'form'),
    'action' => $this->createUrl('addTeacher', array('branchId' => $branchId)),
)); ?>
<div class="pop_cont">
    <div class="form-group">
        <label for="inputEmail" class="col-xs-2 control-label"><?php echo $form->labelEx($model,'teacher_id'); ?></label>
        <div class="col-xs-10">
            <?php $this->widget('ext.search.StaffSearchBox', array(
                'acInputCSS' => 'form-control',
                'htmlOptions' => array('class'=>'form-control'),
                'data' => array(),
                'useModel' => true,
                'model' => $model,
                'attribute' => 'teacher_id',
                'allowMultiple' => false,
                'withAlumni' => false,
            )) ?>
        </div>
    </div>
</div>
<div class="pop_bottom">
    <button id="J_dialog_close" type="button" class="btn btn-default pull-right"><?php echo Yii::t('global','Cancel');?></button>
    <button type="submit" class="btn btn-primary J_ajax_submit_btn pull-right mr10"><?php echo Yii::t('global','Submit');?></button>
</div>
<?php $this->endWidget(); ?>

