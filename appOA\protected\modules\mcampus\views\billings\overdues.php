<?php
$unpayInvoice = array();
$overdueInvoice = array();
$unpayClass = array();
$uAmount = 0;
$oAmount = 0;
$today = strtotime('today');
if (!empty($modelList)) {
    foreach ($modelList as $val) {
        //去除部分付清的
//        $tamount = 0;
//        if (isset($val->invoiceTransaction)){
//            foreach ($val->invoiceTransaction as $tval){
//                $tamount += $tval->amount;
//            }
//        }
        $val->amount = $val->amount-$val->paidSum;

        $tmpData2 = array(
            'fduetime'=>OA::formatDateTime($val->duetime),
            'status'=>Yii::t('payment',  Invoice::getStatusTxt($val->status)),
            'amount'=> number_format($val->amount,2),
            'title'=> $val->title,
            'invoice_id'=> $val->invoice_id,
            'fsend_timestamp'=> ($val->send_timestamp) ? OA::formatDateTime($val->send_timestamp) : '' ,
            'send_timestamp'=> $val->send_timestamp,
            'classid'=> $val->classid,
            'childid'=> $val->childid
        );

        if ($val->duetime < $today) {
            $overdueInvoice[$val->childid]['name'] = $val->childprofile->getChildName();
            $overdueInvoice[$val->childid]['classid'] = $val->classid;
            $overdueInvoice[$val->childid]['childid'] = $val->childid;
            $overdueInvoice[$val->childid]['amount'] = isset($overdueInvoice[$val->childid]['amount']) ?
                $overdueInvoice[$val->childid]['amount'] + $val->amount :
                $val->amount;
            $overdueInvoice[$val->childid]['fAmount'] = number_format($overdueInvoice[$val->childid]['amount'],2);
            $overdueInvoice[$val->childid]['ins'][] = $tmpData2;
            $oAmount += $val->amount;
            $overdueClass[$val->classid] = array('classid'=>$val->classid,'name'=>$val->classInfo->title);
        }
        $unpayInvoice[$val->childid]['name'] = $val->childprofile->getChildName();
        $unpayInvoice[$val->childid]['classid'] = $val->classid;
        $unpayInvoice[$val->childid]['childid'] = $val->childid;
        $unpayInvoice[$val->childid]['amount'] = isset($unpayInvoice[$val->childid]['amount']) ?
            $unpayInvoice[$val->childid]['amount'] + $val->amount :
            $val->amount;
        $unpayInvoice[$val->childid]['fAmount'] = number_format($unpayInvoice[$val->childid]['amount'],2);
        $unpayInvoice[$val->childid]['ins'][] = $tmpData2;
        $uAmount += $val->amount;
        $unpayClass[$val->classid] = array('classid'=>$val->classid,'name'=>$val->classInfo->title);
        }
}
?>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site','Campus Operations'), array('//mcampus/default/index'))?></li>
        <li><?php echo CHtml::link(Yii::t('site','Campus Support'), array('//mcampus/default/index'))?></li>
        <li class="active"><?php echo Yii::t('site','Overdue Reminder');?></li>
    </ol>
    <div class="row">
        <div class="col-md-2">
            <div class="list-group" id="invoice-type-list">
                <a data-vfilter="overdue" href="javascript:void(0)" class="list-group-item action-view-by">
                    <?php echo Yii::t('invoice','Overdue Invoices');?>
                        <span class="badge"><?php echo number_format($oAmount,2);?></span>
                </a>
                <a data-vfilter="unpad" href="javascript:void(0)" class="list-group-item action-view-by">
                    <?php echo Yii::t('invoice','Unpaid');?>
                    <span class="badge"><?php echo number_format($uAmount,2);?></span>
                </a>
            </div>

        </div>
        <div class="col-md-7 J_check_wrap" id="invoice-list">

        </div>
    </div>
</div>

<?php $iLabels = Invoice::model()->attributeLabels();?>

<script type="text/template" id="invoice-list-template">
    <div class="panel panel-default">
        <div class="panel-heading">
            <h4>
                <a href="<?php echo $this->createUrl('//child/invoice/finance', array('t'=>'invoice'));?>&childid=<%=list.childid%>" target="_blank">
                    <span class="glyphicon glyphicon-user"></span>
                    <%=list.name%></a>
                <span class="badge ml10"><%=list.fAmount%></span>
            </h4>
        </div>
        <div class="panel-body">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th width="80">
                            <label>
                                <?php echo CHtml::checkBox('childAll', false, array('data-checklist'=>'J_check_c<%=list.childid%>','data-yid'=>'J_check_c<%=list.classid%>','class'=>'J_check_all J_check', 'data-direction'=>'x','encode'=>false));?> <?php echo Yii::t('global','All');?></label>
                        </th>
                        <th width="100"><label><?php echo $iLabels['duetime'];?></label></th>
                        <th width="150"><label><?php echo Yii::t("payment", "Last Reminder Date");?></label></th>
                        <th width="200"><label><?php echo $iLabels['title'];?></label></th>
                        <th width="100"><label><?php echo $iLabels['status'];?></label></th>
                        <th width="200"><label><?php echo $iLabels['amount'];?></label></th>
                    </tr>
                </thead>
                <tbody>
                    <%_.each(list.ins,function(v,k){%>
                    <tr>
                        <th width="50">
                            <label><input data-yid="J_check_c<%=v.classid%>" data-xid="J_check_c<%=v.childid%>" class="J_check" value="<%=v.invoice_id%>"  type="checkbox" name="invoiceIds[]"></label>
                        </th>
                        <td>
                            <%=v.fduetime%>
                            <span class="text-success" id="invoice_id_<%=v.invoice_id%>"></span>
                        </td>
                        <td>
                            <% if (v.send_timestamp>0){%>
                                <span class="label label-danger">
                                    <%=v.fsend_timestamp%>
                                </span>
                            <%}%>
                        </td>
                        <td><%=v.title%></td>
                        <td><%=v.status%></td>
                        <td><%=v.amount%></td>
                    </tr>
                    <%})%>
                </tbody>
            </table>
        </div>
    </div>
</script>

<script type="text/template" id="class-list-template">
    <?php echo CHtml::form($this->createUrl('//mcampus/billings/overduesSendMail'), 'post', array('class'=>'J_ajaxForm'));?>
    <div class="panel panel-primary">
        <div class="panel-heading">
            <label class="mr20">
            <?php echo CHtml::checkBox('classAll', false, array('data-checklist'=>'J_check_c<%=list.classid%>', 'class'=>'J_check_all', 'data-direction'=>'y','encode'=>false));?>
                <%if (list.name){%><%=list.name%><%}else{%><?php echo Yii::t('campus', 'no class'); ?><%}%>
            </label>
            <button type="button" class="btn btn-default btn-sm J_ajax_submit_btn pull-right"
                    data-subcheck="1"
                    data-msg="<?php echo Yii::t("message", "Send reminder email to parents?");?>">
            <?php echo Yii::t('payment', 'Send Reminder Email');?></button>
            <input id='emailsubject' name='emailsubject' type='text' class='form-control pull-right length_5 mr10' placeholder='<?php echo Yii::t('campus', 'Input email title'); ?>'>
            <div class="clearfix"></div>
        </div>
        <div class="panel-body" id="classid-<%=list.classid%>">

        </div>
    </div>
    <input type="hidden" name="type" id="typetype" class="J_typetype">
    <?php echo CHtml::endForm();?>
</script>

<script>
var unpayInvoice = <?php echo CJSON::encode($unpayInvoice);?>;
var overdueInvoice = <?php echo CJSON::encode($overdueInvoice);?>;
var overdueClass = <?php echo CJSON::encode($overdueClass);?>;
var unpayClass = <?php echo CJSON::encode($unpayClass);?>;
var subject = <?php echo CJSON::encode($subject);?>;
var invoiceListTemplate = _.template($('#invoice-list-template').html());
var classListTemplate = _.template($('#class-list-template').html());
var viewby;
$(function(){

    var _container = $('#invoice-list');

    $('.action-view-by').click(function(){
        viewby = $(this).data('vfilter');
        _container.empty();
        if (viewby == 'overdue'){
            showClass(overdueClass);
            showData(overdueInvoice,viewby);
        }else if (viewby == 'unpad'){
            showClass(unpayClass);
            showData(unpayInvoice);
        }
        $('#invoice-type-list a').removeClass('active');
        $('#invoice-type-list a[data-vfilter|="'+viewby+'"]').addClass('active');
        head.Util.checkAll();
        head.Util.ajaxForm();
        $('.J_typetype').val(viewby);
        $('input[name="emailsubject"]').val(subject[viewby]);
    });

    //渲染孩子及帐单
    showData = function(data){
        _.each(data, function(val,key){
            $("#classid-"+val.classid).append(invoiceListTemplate({list:val}));
        });
    }

    //渲染班级
    showClass = function(data){
        _.each(data, function(val,key){
            _container.append(classListTemplate({list:val}));
        });
    }

    //发送邮件成功回调函数
    callbackInvoice = function(data){
        subject[viewby] = $('#emailsubject').val();
        _.each(data, function(val,key){
            $("#invoice_id_"+key).html(val);
        })
    }

})

</script>
<?php $this->renderPartial('//layouts/common/branchSelectBottom');?>