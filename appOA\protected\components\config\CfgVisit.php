<?php
define(VISIT_READY_INFO,1);
define(VISIT_UNREADY_INFO,2);
define(VISIT_USELESS_INFO,3);
$cfgVisit = array(
	'knowus'=>array(
        "3" => array("cn" => "互联网", "en" => "Internet"),
        "4" => array("cn" => "朋友介绍", "en" => "Friends"),
        "2" => array("cn" => "杂志", "en" => "Magazine"),
        "5" => array("cn" => "迁居服务", "en" => "Relocation Service"),
        "10" => array("cn" => "员工介绍", "en" => "Introduced by Staff"),
        "12" => array("cn" => "路过进来看看", "en" => "Passing By"),
        "13" => array("cn" => "艾毅市场活动", "en" => "Ivy Marketing Events"),
        "14" => array("cn" => "校车", "en" => "School Bus"),
        "15" => array("cn" => "广告", "en" => "Advertising"),
        "16" => array("cn" => "校园活动", "en" => "School Event"),
        "31" => array("cn" => "微信订阅号", "en" => "WeChat Subscription"),
        "32" => array("cn" => "微信朋友圈", "en" => "WeChat Moments"),
        "33" => array("cn" => "朋友推荐", "en" => "Friends"),
        "34" => array("cn" => "微博", "en" => "Weibo"),
        "35" => array("cn" => "浏览器搜索", "en" => "Browser Search"),
        "36" => array("cn" => "互联网广告", "en" => "Internet Ad"),
        "37" => array("cn" => "展会", "en" => "Education Fair"),
        "99" => array("cn" => "其它", "en" => "Other")
	),
    'knowu'=>array(
        "31" => array("cn" => "微信订阅号", "en" => "WeChat Subscription"),
        "32" => array("cn" => "微信朋友圈", "en" => "WeChat Moments"),
        "33" => array("cn" => "朋友推荐", "en" => "Friends"),
        "34" => array("cn" => "微博", "en" => "Weibo"),
        "35" => array("cn" => "浏览器搜索", "en" => "Browser Search"),
        "36" => array("cn" => "互联网广告", "en" => "Internet Ad"),
        "37" => array("cn" => "展会", "en" => "Education Fair"),
        "2" => array("cn" => "杂志", "en" => "Magazine"),
        "14" => array("cn" => "校车", "en" => "School Bus"),
    ),
	'language'=>array(
		'1'=>array(
			'cn'=>'中文',
			'en'=>'Chinese'
		),
		'2'=>array(
			'cn'=>'英文',
			'en'=>'English'
		)
	),
	'time'=>array(
		'14:00-16:00'=>'14:00-16:00',
		'16:00-18:00'=>'16:00-18:00'
	),
	'reservation'=>array(
		'date'=>array(1,3,5,6,0),
		'span'=>30
	),
    'concerns'=>array(
        "a" => array(
            "k"=>"a",
            "en" => "Tuition",
            "cn" => "学费",
        ),
        "b" => array(
            "k"=>"b",
            "en" => "Curriculum and Teachers' qualification",
            "cn" => "教学的内容及师资",
        ),
        "c" => array(
            "k"=>"c",
            "en" => " Campus environment",
            "cn" => "校园环境",
        ),
        "d" => array(
            "k"=>"d",
            "en" => "Teacher and students' ratio",
            "cn" => "师生比例",
        ),
        "e" => array(
            "k"=>"e",
            "en" => "Children' s food and school bus arrangement",
            "cn" => "孩子的餐饮及校车安排",
        ),
        "f" => array(
            "k"=>"f",
            "en" => "Elementary School Entrance Preparation Program",
            "cn" => "幼小衔接课程安排",
        ),
        "g" => array(
            "k"=>"g",
            "en" => "Others",
            "cn" => "其他",
        ),
        "h" => array(
            "k"=>"h",
            "en" => "Class Size",
            "cn" => "班级规模",
        ),
        "i" => array(
            "k"=>"i",
            "en" => "Parental Involvement",
            "cn" => "家长参与",
        ),
        "j" => array(
            "k"=>"j",
            "en" > "School Bus",
            "cn" => "校车服务",
        ),
        "k" => array(
            "k"=>"k",
            "en" => "School Facilities",
            "cn" => "校园设施",
        ),
    ),
    'category'=>array(
        'visit'=>'来访记录',
        'appointment'=>'预约参观',
    ),
    'status'=>array(
        10 =>'有效',
        30 =>'已注册',
        20 =>'无效',
    ),
    'ages'=>array(
        '1' => '不满一岁',
        '2' => '1岁',
        '3' => '2岁',
        '4' => '3岁',
        '5' => '4岁',
        '6' => '5岁',
        '7' => '6岁',
        '8' => '7岁及以上',
        '9' => '未知',
    ),
    'purpose'=>array(
        10 =>'高',
        20 =>'一般',
        30 =>'低',
    ),
    'eventstat'=>array(
        10 =>'开放',
        30 =>'仅关闭艾毅家长报名',
        40 =>'仅关闭非艾毅家长报名',
        20 =>'关闭',
    ),
);
return $cfgVisit;