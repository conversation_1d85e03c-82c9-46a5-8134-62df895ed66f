<?php

class StaffController extends ProtectedController
{
    public $actionAccessAuths = array(
        'MultiManage'       => 'o_X_Access',
        'AddMultBranch'     => array('access'=>'o_X_Access', 'admin'=>'o_X_Adm_Common'),
        'Schoolslist'       => array('access'=>'o_X_Access', 'admin'=>'o_X_Adm_Common'),
        'DeleteMult'        => 'o_X_Adm_Common',
    );

    public $dialogWidth = 400;

    public function init(){
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'sysConfig';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site','System Management');
    }

    /**
     * 管理多校园主页面
     * @param string $type
     */
    public function actionMultiManage($type='pd')
    {
        $mainMenu = array();
        foreach (OA::getMultiAdmType() as $mKey=>$mult){
            $mainMenu[] = array(
                'label' => $mult,
                'url' => array('//msettings/staff/multiManage', 'type'=>$mKey),
            );
        }

        $criteria = new CDbCriteria;
        if ($type != 'edu')
            $criteria->compare('t.admtype', 1);
        $criteria->compare('t.isstaff', 1);
        // $criteria->addCondition('t.level>0 and t.rank<>7');
        $criteria->order='uname';
        $userList = User::model()->with(array('profile',
                'multBranchWithParam'=>array('params'=>array(':type'=>$type))
            )
        )->findAll($criteria);
        $allBranch = $this->getAllBranch();
        foreach($allBranch as $bid => $b){
            $branchList[$bid] = $b['title'];
        }
        $this->render('multbranch/admin',array('userList'=>$userList,'branchList'=>$branchList, 'type'=>$type, 'mainMenu'=>$mainMenu));
    }

    /**
     * 添加管理多校园页面及提交处理
     * @param $type
     */
    public function actionAddMultBranch($type)
    {
        $this->layout='//layouts/dialog';
        $this->dialogWidth = 600;
        $model = new AdmBranchLink;
        $model->type = $type;

        if (isset($_POST['AdmBranchLink'])){
            if($_POST['AdmBranchLink']['schoolid']){
                if($_POST['AdmBranchLink']['uid']){
                    AdmBranchLink::model()->deleteAll('uid=:useid and type=:type',array(':useid'=>$_POST['AdmBranchLink']['uid'], ':type'=>$_POST['AdmBranchLink']['type']));
                }
                foreach ($_POST['AdmBranchLink']['schoolid'] as $schoolid){
                    $model = new AdmBranchLink;
                    $model->attributes = $_POST['AdmBranchLink'];
                    $model->userid = Yii::app()->user->id;
                    $model->schoolid = $schoolid;
                    if(!$model->save()){
                        $this->addMessage('state', 'fail');
                        $err = current($model->getErrors());
                        $this->addMessage('message', $err[0]);
                        $this->showMessage();
                    }
                }
                // if ($type == 'edu')
                User::model()->updateByPk($model->uid, array('admtype'=>1));
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message','Data saved!'));
                $this->addMessage('refresh', true);
                $this->addMessage('referer', $this->createUrl('multiManage', array('type'=>$model->type)));
            }
            else{
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('message','请选择学校'));
            }
            $this->showMessage();
        }

        $branch=$this->getAllBranch();

        $this->render('multbranch/addbranch', array('model'=>$model, 'branch'=>$branch));
    }

    /**
     * 修改管理多校园页面及提交处理
     * @param int $id
     * @param string $type
     */
    public function actionSchoolslist($id=0, $type='')
    {
        if (Yii::app()->request->isPostRequest){
            $school   = Yii::app()->request->getPost('school', array());

            AdmBranchLink::model()->deleteAll('uid=:useid and type=:type',array(':useid'=>$id, ':type'=>$type));

            $_data = '';
            foreach($school as $sid=>$_tv){
                $admBranch = new AdmBranchLink;
                $admBranch->uid = $id;
                $admBranch->type = $type;
                $admBranch->schoolid = $sid;
                $admBranch->save();
//                $_data .= '<span>'.$_tv.'</span>&nbsp;&nbsp;&nbsp;&nbsp;';
            }

            $this->addMessage('refresh', true);
            $this->addMessage('state', 'success');
            $this->addMessage('message', Yii::t('message','Data saved!'));
//            $this->addMessage('data', CJSON::encode(array('uid'=>$id, '_data'=>$_data)));
//            $this->addMessage('callback', 'callback');
            $this->showMessage();
        }

        $this->layout='//layouts/dialog';
        $allBranch = $this->getAllBranch();

        $criteria = new CDbCriteria;
        $criteria->compare('uid', $id);
        $criteria->compare('type', $type);
        $criteria->index='schoolid';
        $adbs = AdmBranchLink::model()->findAll($criteria);

        foreach($allBranch as $bid => $b){
            $branchList[$bid] = array(
                'title' => $b['title'],
                'selected' => isset( $adbs[$bid] ) ? 1 : 0,
            );
        }

        $this->render('multbranch/schoolslist', array('branchList'=>$branchList));
    }

    /**
     * 删除管理多校园
     */
    public function actionDeleteMult()
    {
        $type   = Yii::app()->request->getParam('type', '');
        $uid    = Yii::app()->request->getParam('uid', 0);

        if (Yii::app()->request->isPostRequest && $uid){
            if (AdmBranchLink::model()->deleteAll('uid=:useid and type=:type',array(':useid'=>$uid, ':type'=>$type))) {
                $criteria = new CDbCriteria;
                $criteria->compare('uid', $uid);
                $count = AdmBranchLink::model()->count($criteria);
                if (!$count){
                    User::model()->updateByPk($uid, array('admtype'=>0));
                }
                $this->addMessage('state', 'success');
                $this->addMessage('message', '删除成功！');
            }
            else {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '删除失败！');
            }
            $this->showMessage();
        }
    }
}