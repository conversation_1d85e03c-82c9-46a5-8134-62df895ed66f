<?php $form=$this->beginWidget('CActiveForm', array(
	'id'=>'event-form',
	'enableAjaxValidation'=>false,
    'htmlOptions'=>array('class'=>'J_ajaxForm form-horizontal', 'role'=>'form'),
));
?>
<div class="pop_cont" style="overflow-y: auto;height: 400px;">
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'en_title'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->textField($model,'en_title',array('maxlength'=>255,'class'=>'form-control')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'cn_title'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->textField($model,'cn_title',array('maxlength'=>255,'class'=>'form-control')); ?>
        </div>
    </div>
    <div class="form-group" id="e_show" style="<?php if ($model->school_id != 'BJ_TYG'):?>display:none;<?php endif;?>">
        <label class="col-xs-3 control-label"></label>
        <div class="col-xs-9">
            <?php echo CHtml::checkBoxList('SchoolEventsShow[schoolid][]', $sArray, $this->branchArr, array('template'=>'<div class="checkbox">{input} {label}</div>', 'separator'=>''));?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'event_date'); ?></label>
        <div class="col-xs-9">
            <?php
            $this->widget('zii.widgets.jui.CJuiDatePicker', array(
                "model"=>$model,
                "attribute"=>"event_date",
                "options"=>array(
                    'changeMonth'=>true,
                    'changeYear'=>true,
                    'dateFormat'=>'yy-mm-dd',
                ),
                'htmlOptions'=>array(
                    'class'=>'form-control'
                ),
            ));
            ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'apply_duedate'); ?></label>
        <div class="col-xs-9">
            <?php
            $this->widget('zii.widgets.jui.CJuiDatePicker', array(
                "model"=>$model,
                "attribute"=>"apply_duedate",
                "options"=>array(
                    'changeMonth'=>true,
                    'changeYear'=>true,
                    'dateFormat'=>'yy-mm-dd',
                ),
                'htmlOptions'=>array(
                    'class'=>'form-control'
                ),
            ));
            ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'introduction_en'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->textArea($model,'introduction_en',array('maxlength'=>255,'class'=>'form-control')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'introduction_cn'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->textArea($model,'introduction_cn',array('maxlength'=>255,'class'=>'form-control')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'stat'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->radioButtonList($model, 'stat', $this->cfg['eventstat'], array('template'=>'<div class="radio">{input} {label}</div>', 'separator'=>'')); ?>
        </div>
    </div>
</div>

<div class="pop_bottom">
    <button id="J_dialog_close" type="button" class="btn btn-default pull-right"><?php echo Yii::t('global','Cancel');?></button>
    <button type="submit" class="btn btn-primary J_ajax_submit_btn pull-right mr10"><?php echo Yii::t('global','Submit');?></button>
</div>
<?php $this->endWidget(); ?>

<script type="text/javascript">
function show1(_this)
{
    if ( $(_this).val() == 'BJ_TYG') {
        $('#e_show').show();
    }
    else {
        $('#e_show').hide();
    }
}
</script>