<?php
$invoiceModel = new Invoice;
$criteria = new CDbCriteria;
$criteria->compare('admission_id', $child->id);
$criteria->compare('status', Invoice::STATS_UNPAID);

$form = $this->beginWidget('CActiveForm', array(
    'id' => 'invoice-form',
    'enableAjaxValidation' => false,
    'action' => $this->createUrl('admissionsInvoice', array('id' => $child->id)),
    'htmlOptions' => array('class' => 'J_ajaxForm form-horizontal', 'role' => 'form'),
));
?>
    <div class="modal-body">
        <?php if (Invoice::model()->exists($criteria)): ?>
            <div class="form-group">
                <h3 class="text-center"><?php echo Yii::t('management','Unpaid bills')?></h3>
            </div>
        <?php else: ?>
            <div class="form-group">
                <label class="col-xs-3 control-label"><?php echo $form->labelEx($invoiceModel, 'title'); ?></label>

                <div class="col-xs-9">
                    <?php echo $form->textField($invoiceModel, 'title', array('maxlength' => 255, 'class' => 'form-control', 'value' => '申请费/Application Fee')); ?>
                </div>
            </div>

            <div class="form-group">
                <label class="col-xs-3 control-label"><?php echo $form->labelEx($invoiceModel, 'amount'); ?></label>

                <div class="col-xs-9">
                    <?php echo $form->textField($invoiceModel, 'amount', array('maxlength' => 255, 'class' => 'form-control', 'value' => 2000.00, 'type' => 'number')); ?>
                </div>
            </div>

            <div class="form-group">
                <label class="col-xs-3 control-label"><?php echo $form->labelEx($invoiceModel, 'duetime'); ?></label>

                <div class="col-xs-9">
                    <?php $this->widget('zii.widgets.jui.CJuiDatePicker', array(
                        'model' => $invoiceModel,
                        'attribute' => 'duetime',
                        'options' => array(
                            'dateFormat' => 'yy-mm-dd',
                        ),
                        'htmlOptions' => array(
                            'class' => 'form-control',
                            'placeholder' => Yii::t('management', 'Date of received payment'),
                            'value' => date('Y-m-d', time() + 2*24*3600),
                        ),
                    )); ?>
                </div>
            </div>

            <div class="form-group">
                <label class="col-xs-3 control-label"><?php echo $form->labelEx($invoiceModel, 'memo'); ?></label>

                <div class="col-xs-9">
                    <?php echo $form->textField($invoiceModel, 'memo', array('maxlength' => 255, 'class' => 'form-control')); ?>
                </div>
            </div>
        <?php endif; ?>
    </div>
<?php if (!Invoice::model()->exists($criteria)): ?>
    <div class="modal-footer">
        <button type="submit"
                class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global', 'Submit'); ?></button>
        <button type="button" class="btn btn-default"
                data-dismiss="modal"><?php echo Yii::t('global', 'Cancel'); ?></button>
    </div>
<?php endif; ?>
<?php $this->endWidget(); ?>