<div class="row">
    <div class="col-md-9">
        <div class="panel panel-default">
            <div class="panel-heading"><?php echo Yii::t('teaching','Class Report');?></div>
            <div class="panel-body">
                <?php echo CHtml::form('', 'post', array('class'=>'form-horizontal J_ajaxForm', 'role'=>'form'));?>
                <div class="form-group">
                    <label class="col-sm-2 control-label"><?php echo Yii::t('labels','Title');?></label>
                    <div class="col-sm-10">
                        <?php echo CHtml::activeTextField($taskData['model'], 'en_title',array('class'=>'form-control','style'=>'width:80%'));?>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-2 control-label"><?php echo Yii::t('labels','Content');?></label>
                    <div class="col-sm-10">
                        <?php
                        $this->widget('common.extensions.ueditor.ueditor_1_5_0',array(
                            'model' => $taskData['model'],
                            'attribute' => 'en_content',
                            'configFile' => 'ueditor.teacher.config',
                            'language' =>Yii::app()->language == 'en_us' ? 'en' : 'zh-cn',
                            'editorOptions' => array('initialFrameWidth'=>'80%'),
                            'plugFile'=>'add.customize.image.button',
                            'toolbars'=>array('fullscreen', 'source', '|', 'undo', 'redo', '|','bold', 'italic', 'underline', 'strikethrough', 'superscript', 'subscript', '|','insertorderedlist', 'insertunorderedlist', '|','link', 'unlink', 'attachment'),
                            'classId' => $this->selectedClassId,
                        ));
                        ?>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-2 control-label"><?php echo Yii::t('teaching','Make Online');?></label>
                    <div class="col-sm-10">
                        <div class="checkbox">
                            <label>
                                <?php echo CHtml::activeCheckBox($taskData['model'], 'stat', array('value'=>20));?>
                                &nbsp;
                            </label>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-2"></label>
                    <div class="col-sm-10">
                        <button class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global','Save');?></button>
                    </div>
                </div>
                <?php echo CHtml::endForm();?>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="panel panel-default">
            <div class="panel-heading"><?php echo Yii::t('teaching','Class Report');?></div>
            <div class="panel-body">
                    <?php 
                    if (!empty($taskData['classNewsList'])):
                        foreach ($taskData['classNewsList'] as $val):
                        $color = ($val->stat == 10 ) ? '#f2f2f2;' : '#398439;';
                        $active = ($val->id == Yii::app()->request->getParam('notesId',0)) ? 'text-danger' : '';
                    ?>
                    <div>
                        <span class="glyphicon glyphicon-ok-sign mr5" style="color:<?php echo $color;?>"></span>
                        <span class="mr15 <?php echo $active;?>"><?php echo $val->en_title;?></span>
                        <?php echo  CHtml::link('<span class="glyphicon glyphicon-pencil mr5"></span>', $this->createUrl('/mteaching/weekly/index',array('classid'=>$this->selectedClassId,'weeknum'=>$this->selectedWeeknum,'task'=>$this->selectedTask,'notesId'=>$val->id)));?>
                        <?php echo  CHtml::link('<span class="glyphicon glyphicon-remove"></span>', $this->createUrl('/mteaching/weekly/delGradeClassNews',array('classid'=>$this->selectedClassId,'weeknum'=>$this->selectedWeeknum,'notesId'=>$val->id,'task'=>$this->selectedTask)),array('class'=>'J_ajax_del'));?>
                    </div>
                    <?php 
                        endforeach;
                    endif;
                    ?>
            </div>
        </div>
    </div>
</div>

<style>
    .customWH{width: 460px;height: 85px !important;}
</style>
<?php 
$this->widget('ext.selectMedia.SelectMedia', array(
    'weeknum'=>$this->selectedWeeknum,
    'classid'=>$this->selectedClassId,
    'branchid'=>$this->branchId,
    'startyear'=>$this->calendarModel->startyear,
    'yid'=>$this->calendarId,
    'multiple'=>true,
    'callback'=>'callback',
    'buttonLabel' => false,
));
?>
<script type="text/javascript">
    function callback(data){
        var pids = [];
        $('#items-class input[name="pid[]"]').each(function(){
            pids.push( $(this).val() );
        });
        $.each(data, function(index, value){
            if(_.indexOf(pids, index) == -1){
                value = value.replace('h120','ww600');
                ueNotesSchCla_en_content.setContent('<img class="img-thumbnail" src="'+value+'"/>', true);
            }
        });
    }
</script>