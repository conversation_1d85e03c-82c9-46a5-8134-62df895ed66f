<!--[if lt IE 9]>
<script>'section header aside article nav footer'.replace(/\w+/g, function (n) { document.createElement(n) })</script>
<![endif]--><!--[if IE 6]><script type="text/javascript">try { document.execCommand('BackgroundImageCache', false, true); } catch(e) {}</script><![endif]-->

<section class="tools cf hide">
    <div class="description"><?php echo Yii::t('navigations', 'Photo Gallery')?></div>
    <div class="num"><span class="curr"></span>/<span class="all"></span></div>
    <div class="tool"><ul class="cf"><li class="list"></li><li class="prev"></li><li class="play stop"><span class="stoptip"><?php echo Yii::t("portfolio", "Paused...")?></span></li><li class="next"></li></ul></div>
</section>
<article style="margin-top:0">
    <div id="photolist" class="hide" style="margin-top: 3px; display: none !important;">
        <div class="mask">
            <ul class="cf">
                <?php $i = 0;
                foreach ($medias as $m): ?>
                <li <?php if ($i == 0): ?>class="on"<?php endif; ?>>
                    <a target="_blank" href="#"><img src="<?php echo $m->getMediaUrl(true) ?>" bigpic="<?php echo $m->getMediaUrl() ?>" desc="<?php echo $pics[$m->id]?>" /></a>
                </li>
                <?php $i++;
            endforeach; ?>
            </ul>
        </div>
        <div class="btn_l"></div>
        <div class="btn_r"></div>
        <div class="listnav"></div>
    </div>
    <div id="picbox" class="span-18 last" style="position: absolute;background-color: #333;">
        <table>
            <tr>
                <td>
                    <div><img id="mainimg" src="" /></div><?php //echo $medias[0]->getMediaUrl()?>
                </td>
            </tr>
        </table>
        <div id="imgLoading">
            <img src="<?php echo $baseUrl?>/loading.gif" alt="Loading..." />
            <div>
                <em><?php echo Yii::t("portfolio", "Loading...")?></em>
            </div>
        </div>
    </div>
    <div class="summary" style="position: absolute;bottom: 0;font-size: 16px;background-color: #333333;text-align: center;">
        <div class="description"><p /></div>
        <div class="bg"></div>
    </div>
    <div id="more">
        <h5><?php echo Yii::t("portfolio", "The End...")?></h5>
        <?php if ($nurl):?>
        <p>
            <?php echo Yii::t("portfolio", "Next gallery will be played in :seconds seconds.", array(':seconds'=>'<span class="timecount">15</span>'));?>
        </p>
        <?php endif;?>
        <div class="tj ">
            <div class="replay">&nbsp;<span><s> </s><?php echo Yii::t("portfolio", "Replay")?></span></div>
            <?php if ($nurl):?>
            <div class="nextplay">&nbsp;<span><s> </s><a href="<?php echo $nurl?>"><?php echo Yii::t('portfolio', 'Next Gallery')?></a></span></div>
            <?php endif;?>
        </div>
    </div>
</article>
<script type="text/javascript">
    $(function () {
        IVY.Page.slide.settings.resize = true;
        IVY.Page.slide.init();
        IVY.Page.slide.goto(0);
    });
    var cURL = "<?php echo $baseUrl . '/c.gif?' ?>";
    $.track({trackInfoOpts:{sitePage:{lang:'zn-cn',siteDI:'11703',sitePI:'33235',sitePS:'70635', pagename:'mainpage',dept:'',sdept:'',pgGrpId:'',cntType:'',srchQ:''},
            userStatic: { signedIn:'false', age:'', gender: ''}
        },spinTimeout: 150}).register(new $.track.genericTracking({ base: cURL,linkTrack:1,samplingRate:99,commonMap:{event: {evt:'type'}, userDynamic:{rid:'requestId',cts:'timeStamp'},client:{clid:'clientId'}},
        impr:{param:{evt:'impr'},paramMap:{client:{rf:'referrer',cu:'pageUrl',bh:'height',bw: 'width',sl:'silverlightEnabled',slv:'silverlightVersion',scr:'screenResolution',sd: 'colorDepth'},userDynamic:{hp:'isHomePage'},userStatic:{pp:'signedIn',bd:'age',gnd: 'gender'},sitePage:{mk:'lang',di:'siteDI',pi:'sitePI',ps:'sitePS',pn:'pagename',pid: 'pageId','st.dpt':'dept','st.sdpt':'sdept','dv.pgGrpId':'pgGrpId','dv.contnTp':'cntType', mv:'pgVer', q:'srchQ'}}},
        click:{paramMap:{report:{hl:'headline',ce:'contentElement',cm:'contentModule',du: 'destinationUrl'}} },unload: {/**/}}), 
    new $.track.genericTracking({base:cURL,linkTrack:0,impr:{param:{udc:"true"},paramMap:{ client:{rf:"referrer",tp:'pageUrl'},sitePage:{di:"siteDI",pi:"sitePI",ps:"sitePS"}, userDynamic:{rid:"requestId",cts:"timeStamp"}}}}));jQuery.track.trackPage();
</script>

<?php
//cur 需要设置绝对路径，否则IE下不显示
?>
<style>
    #picbox .p_left,#picbox .p_right{
        cursor: url(<?php echo Yii::app()->themeManager->baseUrl . '/base/images/cur/'?>left.cur), auto;
    }
    #picbox .p_right{ cursor: url(<?php echo Yii::app()->themeManager->baseUrl . '/base/images/cur/'?>right.cur), auto;}
</style>