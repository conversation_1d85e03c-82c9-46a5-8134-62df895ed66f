@CHARSET "UTF-8";

.votebox {
    background: url("../images/votebt.png") no-repeat scroll 0 bottom;
    clear: both;
    margin-bottom: 10px;
    padding-bottom: 14px;
}
.votebox h4 {
    background: url("../images/vote_bar.png") no-repeat scroll 0 bottom transparent;
    color: #FFFFFF;
    font-size: 14px;
    height: 32px;
    line-height: 32px;
    overflow: hidden;
    padding-top: 14px;
    text-indent: 60px;
    margin-bottom: 0;
    font-weight:bold;
}
.votebox h4.survey {
    background-image: url("../images/survey_bar.png");
}
.votebox .alignRight {
    float: right !important;
    padding-right: 30px;
}
.votebox .voteinfo {
    background: url("../images/votebox_bg.gif") repeat-y scroll 0 0 transparent;
    clear: both;
    line-height: 20px;
    padding: 1px 15px 0;
}
.votebox .voteinfo .prebg {
    background: none repeat scroll 0 0 #FEF6EB;
    border: 1px solid #FFFFFF;
}
.votebox .voteinfo .poll {
    margin-top: 12px;
    /*width: 610px;*/
}
.votebox .voteinfo .poll .vtitle a em {
    color: green !important;
}
.votebox .pd10 {
    padding: 10px;
}
.votebox .voteinfo .poll .vtitle a {
    background-image: url("../images/pollitem.gif");
    background-position: 0 2px;
    background-repeat: no-repeat;
    border-bottom: 1px dotted #FFC882;
    color: orange !important;
    display: block;
    font-size: 14px;
    font-weight: bold;
    height: 20px;
    line-height: 20px;
    padding-bottom: 4px;
    padding-left: 20px;
}
.votebox .polldesc {
    /*text-indent: 2em;*/
    margin-top: 6px;
}
.votebox a:link, .votebox a:visited {
    color: #009900;
    text-decoration: none;
}
.votebox .poll .optpreview {
    width: 400px;
}
.votebox .fl {
    float: left;
}
.votebox .voteinfo .poll .vopt {
    color: #BB6800;
    padding: 2px 0 2px 22px;
}
.votebox .pollact {
    margin: 20px 10px;
}
.votebox .fr {
    float: right;
}

.votebox .pollother .txt {
    background: none repeat scroll 0 0 #FFFFFF;
    border: 1px solid orange;
    height: 22px;
    margin-left: 10px;
    margin-top: 4px;
}
.votebox .pollother .txt em {
    background: none repeat scroll 0 0 orange;
    color: #FFFFFF;
    display: inline-block;
    height: 22px;
    line-height: 22px;
    margin: 0;
    padding: 0 6px;
    font-weight: 400;
    font-style: normal;
}
.votebox .pollother .txt span {
    color: #BB6800;
    padding: 0 6px;
}
.poll .polledit {
    margin-left: 20px;
    margin-top: 10px;
    text-align: right;
}
.poll .polledit a {
    display: inline-block;
    padding: 2px 6px;
    background: #EA5506;
    color: #ffffff;
}
.poll .polledit a:hover {
    background: #CF4700;
}
.poll .polledit .txt {
    color: #EA5506;
    padding: 2px 6px;
    background: #FFF1E0;
}
#operate .hint {
    background-color: #FFFFCC;
    border: 1px solid #FFCC66;
    margin: 10px 0;
    padding: 10px 20px;
}
#operate .btn {
    padding: 20px 60px 20px 400px;
}