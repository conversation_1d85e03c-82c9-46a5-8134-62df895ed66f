<div class="h_a">积分兑换设置</div>
<div class="prompt_text">
	<ul>
		<li>未开通在线支付的校园将不会显示积分兑换信息</li>
	</ul>
</div>

<?php $form=$this->beginWidget('CActiveForm', array(
    'id'=>'pointconf-form',
    'enableAjaxValidation'=>false,
    'htmlOptions'=>array('class'=>'J_ajaxForm'),
)); ?>
<div class="table_list">
	<table width="100%" id="J_table_list" style="table-layout:fixed;">
		<colgroup>
			<col width="30">
			<col width="200">
			<col width="80">
			<col>
		</colgroup>
		<thead>
			<tr>
				<td></td>
				<td>校园</td>
				<td class="tac">启用/关闭</td>
				<td>礼品上限值（只显示该数值以下的礼品，0为无限制）</td>
			</tr>
		</thead>
		<tbody>
			<?php foreach ($schools as $school):?>
			<tr>
				<td><span class="zero_icon" data-id="<?php echo $school->branchid?>"></span></td>
				<td height=26><?php echo $school->title?></td>
				<td class="tac">
                    <?php echo CHtml::hiddenField('id['.$school->branchid.']', $school->branchid);?>
                    <?php echo CHtml::checkBox('toggle['.$school->branchid.']', $school->points->status ? true : false)?>
				</td>
				<td>
                    <input class="input" type="text" name="maxn[<?php echo $school->branchid?>]" id="max<?php echo $school->branchid?>" value="<?php echo $school->points->maximum===null ? 0 : $school->points->maximum?>" />
				</td>
			</tr>
			<?php endforeach; ?>
		</tbody>

		</table>
</div>
<div class="btn_wrap">
    <div class="btn_wrap_pd">
        <button class="btn btn_submit mr10 J_ajax_submit_btn"><?php echo Yii::t('global','Submit');?></button>
    </div>
</div>
<?php $this->endWidget(); ?>