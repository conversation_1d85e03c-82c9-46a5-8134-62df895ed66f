<?php

/**
 * This is the model class for table "ivy_banktransfer_detail".
 *
 * The followings are the available columns in table 'ivy_banktransfer_detail':
 * @property integer $id
 * @property string $transferid
 * @property integer $invoiceid
 * @property integer $tranid
 * @property integer $childid
 */
class BanktransferDetail extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_banktransfer_detail';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('transferid, invoiceid, tranid, childid', 'required'),
			array('invoiceid, tranid, childid', 'numerical', 'integerOnly'=>true),
			array('transferid', 'length', 'max'=>64),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, transferid, invoiceid, tranid, childid', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'invoice'=>array(self::BELONGS_TO,'Invoice','invoiceid'),
            'invoiceTransaction'=>array(self::BELONGS_TO,'InvoiceTransaction','tranid'),
            'childProfile' => array(self::BELONGS_TO, 'ChildProfileBasic', 'childid'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'transferid' => 'Transferid',
			'invoiceid' => Yii::t('invoice', '关联账单'),
			'tranid' => 'Tranid',
			'childid' => 'Childid',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('transferid',$this->transferid,true);
		$criteria->compare('invoiceid',$this->invoiceid);
		$criteria->compare('tranid',$this->tranid);
		$criteria->compare('childid',$this->childid);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return BanktransferDetail the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
    
    /*
     * 根据InvoiceId查询对应的tranid
     */
    public function getTrantionIdByInvoice($invoiceId){
        $ret = array();
        $data = $this->model()->findAll('invoiceid=:invoiceid',array(':invoiceid'=>$invoiceId));
        if (!empty($data)){
            foreach ($data as $val){
                $ret[$val->tranid] = $val->tranid;
            }
        }
        return $ret;
    }
}
