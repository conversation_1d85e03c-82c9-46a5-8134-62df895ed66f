<style>
    .course_code {-webkit-text-size-adjust:none;font-size: 10px;color:#777}
</style>
<!-- 加载底部导航栏 -->
<?php
//$this->branchSelectParams['extraUrlArray'] = array('//mgrades/attendance/index');
//$this->renderPartial('//layouts/common/branchSelectBottom');

?>
<style>
    .weekNum{
        width:72px;
        height:50px;
        background: #FAFAFA;
        border-radius: 4px;
        border: 1px solid #EBEEF5;
        color:#666666;
    }
    .weekNum:hover{
        color:#fff !important;
        background: #4D88D2;
        border-radius: 4px;
        border: 1px solid #4D88D2;
        cursor:pointer 
    }
    .borderBto{
        border-bottom: 1px solid #EBEEF5;
    }
    .lineHeight{
        line-height:25px
    }
    .weekActive{
        color:#fff !important;
        background: #4D88D2;
        border-radius: 4px;
        border: 1px solid #4D88D2;
    }
    .weekActive .weekColor{
        color:#fff !important;
    }
    .border{
        width: 0;
        height: 0;
        border-top: 50px solid #5CB85C;
        border-right: 50px solid transparent;
        position: absolute;
        left: 0px;
        top: 0px;
    }
    .holiday{
        border-top: 50px solid #5CB85C;
    }
    .replace{
        border-top: 50px solid #F0AD4E;
    }
    .holidayText{
        position: absolute;
        left: 10px; 
        top: -48px;
        color: #fff;
        font-size: 18px;
        font-weight: 200;
    }
    .table{
        color:#606266
    }
    .table tr th {
        vertical-align:middle !important;
        text-align:center;
        color:#606266
    }
    .replaceCourse{
        color:#F0AD4E;
        position: absolute;
        top:20px;
        right: 20px;
        width: 42px;
    }
    .yellow{
        background:#FDEBC0 !important;
    }
    .blue{
        background:#EFF5FF;
    }
    .gray{
        background:#FAFAFA;
    }
    .font16{
        font-size:16px
    }
    .course{
        padding:8px; 
        border-radius:4px;
        color:#666666
    }
    .prev{
        position: absolute;
        left: 30px;
        top: 0;
        font-size: 18px;
        height: 52px;
        background: #fff;
        line-height: 50px;
        width:30px;
        z-index: 99;
        top:-1px;
    }
    .prevLeft{
        left:0;
    }
    .next{
        position: absolute;
        right: 30px;
        top: 0;
        font-size: 18px;
        height: 52px;
        background: #fff;
        line-height: 50px;
        width:30px;
        z-index: 99;
        top:-1px;
    }
    .nextRight{
        right:0;
    }
    .dis{
        color:#C0C4CC
    }
    .loading{
        width:98%;
        height:400px;
        background:#fff;
        position: absolute; 
        opacity: 0.5;
        z-index: 99
    }
    .loading span{
        width:100%;
        height:60%;
        display:block;
        background: url("<?php echo Yii::app()->theme->baseUrl?>/images/loading.gif")no-repeat center center ;
    }
    [v-cloak] {
        display: none;
    }
    .animation{
        animation:margin-left: 92px; 0.1s infinite;
        -webkit-animation:margin-left: 92px; 0.5s infinite;
    }
    @keyframes mymove
    {
        100% {margin-left: 92px;}
    }

    @-webkit-keyframes mymove /*Safari and Chrome*/
    {
        100% {margin-left: 92px;}
    }
    .actOpacity{
        opacity: .5;
        cursor: not-allowed !important; 
    }
    .bgYellow{
        background:#FFF7E3;
    }
    .borderBto{
        border-bottom:1px solid #EBEEF5
    }
    .pink{
        background:#FFECEE
    }
    .qrcode{
        position: absolute;
        right: 0；;
        right: 100px;
        top: 0px;
        width: 400px;
        height: 350px;
        z-index: 99;
        background: #fff;
        border-radius: 5px;
        box-shadow: 0px 2px 8px 0px rgb(0 0 0 / 20%);
        text-align:center;
        margin:0 auto;
        /* display:none */
    }
    .qrcode .orange{
        color:#E89C32
    }
    /* .viewQrcode:hover .qrcode{
        display: block;
    } */
</style>
<div class="container-fluid">
    <!-- 面包屑导航 -->
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('//mcampus/default/index')) ?></li>
        <li class="active"><?php echo Yii::t('site', 'G6-12 Schedule Management'); ?></li>
    </ol>
    <div class="row">
        <div class="col-md-2">
            <?php
                $this->widget('zii.widgets.CMenu',array(
                    'items'=> $this->siderMenu,
                    'htmlOptions'=>array('class'=>'nav nav-pills nav-stacked text-right background-gray mb10'),
                    'activeCssClass'=>'active',
                    'itemCssClass'=>''
                ));
            ?>
        </div>
        <div class="col-md-10" id='container'  v-cloak >
            <div class="row" @click='qrcodeShow=false'>
                <p class='color3 pb20 col-md-12 borderBto' ><strong style='font-size:18px'><?php echo Yii::t('attends','Student Schedule') ?></strong> <span class='ml20 font14'>{{startYear}}</span> </p>
                <div class="col-md-2 mt20">
                    <div class="list-group" >
                    <select class="form-control input-sm mb5"  v-model="classId">
                        <option value="-1" selected><?php echo Yii::t('global','Please Select') ?></option>
                        <option v-for="(list,key,index) in classArr" :value="list.id">{{list.name}}</option>
                    </select>
                    </div>
                    <div class="list-group">
                    <a class="list-group-item" href="javascript:void(0);"  v-for='(list,key,idx) in childArr[classId]' :class='key==studentId?"active":""' @click='getData(key)'>{{list.childName}}</a>
                    </div>
                </div>
                <div class="col-md-10 mt20 relative" v-if='studentId!=""'>
                    <div class='loading'  v-if='showLoading'>
                        <span></span>
                    </div>
                    <div  v-if='schedule.length!=0 && !showLoading'>
                        <div class=''>
                            <a  class="pull-right ml20" href='javascript:;' @click="exportChildList()"><span class="glyphicon glyphicon-print mr5" aria-hidden="true"> </span> <?php echo Yii::t('global', 'Print') ?></a>
                            <a  class="pull-right mr20 color3 relative viewQrcode" href='javascript:;' @click="courseQrcode()"><span class="glyphicon glyphicon-qrcode mr5" aria-hidden="true"> </span> <?php echo Yii::t('global', '二维码查看') ?>
                                <div class='qrcode font14 color3 p20' v-if='qrcodeShow'>
                                    <div class='col-md-6'>
                                        <p>学生课程表</p>
                                        <p>{{childArr[classId][studentId].childName}}</p>
                                        <img class=" img-face mb10" style='width:150px;height:150px' :src="qrcodeImg">
                                        <p class='orange'>一人一码</p>
                                        <p>无需家长权限</p>
                                        <p>扫码即可查看</p>
                                    </div>
                                    <div class='col-md-6'>
                                        <p>学生课程表</p>
                                        <?php if (Yii::app()->language == 'en_us'): ?>
                                            <img class=" img-face mb10" style='width:150px;height:150px;margin-top:28px' src="http://m2.files.ivykids.cn/cloud01-file-8025768Fu-dQ4XY7qjy5_ME8xiX9bsEWHlv.jpg">
                                        <?php else:?>
                                            <img class=" img-face mb10" style='width:150px;height:150px;margin-top:28px' src="http://m2.files.ivykids.cn/cloud01-file-8025768FueKTM9BPtazdge8AHkqSALQD_ej.jpg">
                                            <?php endif;?>
                                        <p class='orange'>统一码</p>
                                        <p>需要家长权限</p>
                                        <p>可分享给家长</p>
                                    </div>
                                </div>
                            </a>
                        </div>
                        <div class='clearfix'></div>
                        <div class='relative mt20' v-if='week.item'>
                            <div class='glyphicon glyphicon-step-backward prev prevLeft color6' v-if='offsetLeft<0'  @click='headWeek()'></div>
                            <div class='glyphicon glyphicon-step-backward prev prevLeft dis color6' v-else></div>
                            <div class='glyphicon glyphicon-chevron-left prev color6' v-if='offsetLeft<0'  @click='nextWeek()'></div>
                            <div class='glyphicon glyphicon-chevron-left prev dis color6' v-else></div>
                            <div style='width:100% ;padding:0 60px;overflow:hidden;' ref='week'>
                                <div :style="'margin-left:'+ animateLeft +'px;width:'+weekWidth"  class='animation'>
                                    <div class='pull-left mr20 weekNum text-center font12'  v-for='(list,index) in week.item' :class='list.number==currentWeek.number?"weekActive":list.category==null?"actOpacity":""' @click='eventWeek(list)'  aria-hidden="true" onMouseOver="$(this).tooltip('show')"  data-toggle="tooltip" data-html='true' :title='`<div>week ${list.number}</div>${formatTime(list.days[0])} — ${formatTime(list.days[4])}`'  data-placement="top">
                                        <div class='borderBto lineHeight'>week {{list.number}}</div>   
                                        <div class='weekColor lineHeight'><strong>{{list.category==null?'-':list.category}}</strong> </div>
                                    </div>
                                </div>
                                <div class='clearfix'></div>
                            </div>
                            <div class='glyphicon glyphicon-chevron-right next color6' v-if='offsetLeft>=Difference' @click='prevWeek()'></div>
                            <div class='glyphicon glyphicon-chevron-right next color6 dis' v-else></div>
                            <div class='glyphicon glyphicon-step-forward next nextRight color6' v-if='offsetLeft>=Difference' @click='footWeek()'></div>
                            <div class='glyphicon glyphicon-step-forward next nextRight color6 dis' v-else></div>
                            
                        </div>
                        <table class='table table-bordered mt20' style='table-layout: fixed' ref='table' v-if='currentWeek!=null'>
                            <thead >
                                <tr class='text-center'>
                                    <th width='100' >
                                    <?php echo Yii::t('labels','Time') ?>
                                    </th>
                                    <th v-for='(list,id) in currentWeek.days' class='relative font14'>
                                        <div class='border holiday' v-if='holidaysData(list)' onMouseOver="$(this).tooltip('show')"  data-toggle="tooltip" data-html='true' :title='holidays[list]'  data-placement="top">
                                            <div class='holidayText' >H</div>  
                                        </div>
                                        <div class='border replace' v-if='replaceCourse(list)' onMouseOver="$(this).tooltip('show')"  data-toggle="tooltip" data-html='true' :title='`${replaceData[list].replace_tip}`'  data-placement="top">
                                            <div class='holidayText' >E</div>  
                                        </div>
                                        <div>{{currentWeek.weekDayData[id]}} <span v-if='list==nowDay'  class="label label-info">Today</span> </div>
                                        <div>{{formatTime(list)}}</div>
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for='(data,index) in schedule[currentWeek.category]'>
                                    <th class='text-center'>
                                        <div class='font14'>{{timetableTimes[index].label}}</div>
                                        <div style='font-weight:200'>{{timetableTimes[index].timeslot}}</div>
                                    </th>
                                    <template v-if='data!=null && data.length!=0'>
                                        
                                        <template  v-for='(item,idx) in currentWeek.days' >
                                            <td class='text-center' style='vertical-align:middle !important;' v-if='data[idx]==null && !replaceCourse(item) && !holidaysData(item)'>
                                                <span class='glyphicon glyphicon-plus-sign cur-p ' style='font-size:18px;color:#428BCA' @click='addCourse(idx,index)'></span>
                                            </td>
                                            <td v-else :class='holidaysData(item)?"gray":replaceCourse(item)?"bgYellow":""'>
                                                <template v-for='(_item,i) in data[idx]' >                                        
                                                    <div v-if='_item!=null && !replaceCourse(item) && !holidaysData(item)' :class='data[idx].length!=1?"pink mb5":"blue"'  class='course color6 ' >
                                                        <p class='color3 flex'><strong class='flex1'>{{courses[_item[1]]}}</strong> <span class='glyphicon glyphicon-trash pull-right cur-p pull-right' style='width:20px' @click='delCourse(_item)'></span></p>
                                                        <p>{{_item[1]}}</p>
                                                        <p>{{_item[3]}}</p>
                                                        <p>{{_item[2]}}</p>
                                                        </div>
                                                    </div>
                                                </template>
                                                <template v-for='(_item,i) in course(item,index)' >  
                                                    <div v-if='replaceCourse(item) && course(item,index)!=null'  :class='course(item,index).length!=1?"pink mb5":"yellow"'  class='course color6 ' >
                                                        <p class='color3'><strong>{{courses[_item[1]]}}</strong> </p>
                                                        <p>{{_item[1]}}</p>
                                                        <p>{{_item[3]}}</p>
                                                        <p>{{_item[2]}}</p>
                                                    </div>
                                                </template>
                                            </td>
                                        </template>
                                    </template>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="alert alert-warning" v-else-if='!showLoading' role="alert"><?php echo Yii::t('attends', 'No course has been assigned to you.')?></div>
                </div>
            </div>
            <div class="modal fade" id="editModel" tabindex="-1" role="dialog" aria-labelledby="modal" aria-hidden="false">
                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                            <h4 class="modal-title" ><?php echo Yii::t('attends','Add a Course') ?><span id="remarks_t"></span></h4>
                        </div>
                        <div class="modal-body">
                            <p class='color3 font14'><strong>{{currentWeek.category}} <?php echo Yii::t('global','Weekly schedule') ?></strong></p>
                            <p class='color3 font14' v-if='timetableTimes[times]'><span class='glyphicon glyphicon-time'></span> <span class='ml10'>{{currentWeek.weekDayData[weekId]}}</span>  <span class='ml10'> 第{{timetableTimes[times].period}}节课 ({{timetableTimes[times].timeslot}})</span></p>
                            <table class="table">
                                <thead>
                                    <tr>
                                        <td width='50'>#</td>
                                        <td><?php echo Yii::t('attends','Course Code') ?></td>
                                        <td><?php echo Yii::t('attends','Course Title') ?></td>
                                        <td><?php echo Yii::t('teaching','Teacher') ?></td>
                                        <td><?php echo Yii::t('labels','Room NO.') ?></td>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr v-for='(list,index) in courseList'>
                                        <td ><input type="radio" v-model='addCourse_id' :value='list.course_id'></td>
                                        <td>{{list.course_code}}</td>
                                        <td>{{list.title}}</td>
                                        <td>{{list.teacherName}}</td>
                                        <td>{{list.class_room_name}}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-primary" :disabled='disabled' @click='saveCourse()'><?php echo Yii::t("global", 'Save');?></button>
                            <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", 'Close');?></button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal fade" id="delModel" tabindex="-1" role="dialog" aria-labelledby="modal" aria-hidden="false">
                <div class="modal-dialog modal-sm" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                            <h4 class="modal-title" ><?php echo Yii::t('newDS','Delete') ?> <span id="remarks_t"></span></h4>
                        </div>
                        <div class="modal-body">
                            <?php echo Yii::t("newDS", 'Confirm to delete this item?');?>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-primary" :disabled='disabled' @click='romoveCourse()'><?php echo Yii::t("global", 'Save');?></button>
                            <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", 'Close');?></button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="modal fade" id="exportChild" tabindex="-1" role="dialog" aria-labelledby="modal" aria-hidden="false">
    <div class="modal-dialog" role="document"><div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" onclick="quxiao(this)" aria-label="Close"><span aria-hidden="true">×</span>
                </button>
                <h4 class="modal-title" ><?php echo Yii::t('attends','Check students to print schedules') ?> <span id="remarks_t"></span></h4>
            </div>
            <?php $form=$this->beginWidget('CActiveForm', array(
//                'method'=>'get',
                'id'=>'courseGroup-form',
                'enableAjaxValidation'=>false,
                'action'=> $this->createUrl('exportChild', array('tid' => $tid)),
                'htmlOptions'=>array(
//                   'class'=>'form-horizontal',
                    'role'=>'form',
                    "target" => "_blank",
                ),
            )); ?>
                <div class="form-horizontal">
                    <div class="modal-body">
                        <div class="form-group">
                            <label class="col-xs-2 control-label">
                                <label><?php echo Yii::t('attends','Schedules Type') ?></label>
                            </label>
                            <div class="col-xs-9 " >
                                <div id="scheduleTypeList" class="J_check_wrap"></div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-xs-2 control-label">
                                <label><?php echo Yii::t('attends','Student List') ?></label>
                            </label>
                            <div class="col-xs-9 " >
                                 <div  id="childList" class="J_check_wrap"></div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="submit" class="btn btn-primary" onclick="quxiao()"><?php echo Yii::t('global','Submit') ?></button>
                        <button type="button" class="btn btn-default" onclick="quxiao()"><?php echo Yii::t('global','Cancel') ?></button>
                    </div>
                </div>
            <?php $this->endWidget(); ?>
        </div>
    </div>
</div>

<script>
    var childArr = <?php echo CJSON::encode($childArr); ?>;
    var classArr = <?php echo CJSON::encode($classArr); ?>;
    var courseArr = <?php echo CJSON::encode($courseArr); ?>;
    // $('#editModel').modal('show'); 

    
    var tid = '<?php echo $tid; ?>';
    var course = [];
    var course_data = [];
    var container = new Vue({
        el: "#container",
        data: {
            childArr:childArr,
            classArr:classArr,
            week: {},
            schedule:[],
            replaceData: {},
            courses: {},
            holidays: {},
            currentWeek:{},
            replaceDay:100,
            holidayIndex:100,
            teacherName:name,
            weekWidth:'',
            offsetLeft:0,
            Difference:0,
            width:276,
            weekLang:['<?php echo Yii::t('labels','Mon') ?>','<?php echo Yii::t('labels','Tue') ?>','<?php echo Yii::t('labels','Wed') ?>','<?php echo Yii::t('labels','Thu') ?>','<?php echo Yii::t('labels','Fri') ?>'],
            teacherList:[],
            showLoading:false,
            timetableTimes:{},
            animateLeft:0,
            studentId:'',
            replaceList:{},
            disabled:false,
            startYear:'',
            classId:'-1',
            nowDay:'',
            delId:'',
            weekId:'',
            times:'',
            courseList:[],
            addCourse_id:'',
            qrcodeImg:'',
            qrcodeShow:false
        },
        methods: {
            course(list,index){
                var len=[]
                if(this.replaceData[list]){
                    let replaceList=this.replaceData[list]
                    if(this.schedule[replaceList.category]){
                        for(var i=0;i<this.schedule[replaceList.category].length;i++){
                            len.push(this.schedule[replaceList.category][i][replaceList.day-1])
                        }
                    }
                } 
                return len[index]
            },
            formatTime(date){
                let data=date+''
                let year=data.substring(0,4)
                let mon=data.substring(4,6)
                let day=data.substring(6,8)
                return year+'/'+mon+'/'+day
            },
            getData(id){
                let that=this
                this.showLoading=true
                if(id!=''){
                    this.teacherName=this.teacherList[id]
                }
                this.studentId=id
                $.ajax({
                    url: '<?php echo $this->createUrl("studentInfo") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        studentId:this.studentId,
                        weeknumber:this.currentWeek.number, //可选,
                    },
                    success: function(data) {
                        if(data.state=='success'){  
                            that.week=data.data.week
                            that.schedule=data.data.schedule
                            that.holidays=data.data.holidays
                            that.courses=data.data.courses
                            that.replaceData=data.data.replace
                            that.weekWidth=that.week.item.length*92+'px'
                            that.nowDay=data.data.nowDay
                            that.timetableTimes=data.data.timetableTimes
                            if(!that.currentWeek.days){
                                for(var i=0;i<that.week.item.length;i++){
                                    if(that.week.item[i].number==that.week.current){
                                            that.currentWeek=that.week.item[i]
                                    }
                                }
                            }
                            let start=data.data.week.item[0].monday+''
                            let end=data.data.week.item[data.data.week.item.length-1].monday+''
                            that.startYear=start.substring(0,4)+"-"+end.substring(0,4)+' <?php echo Yii::t('labels','School Year') ?>'
                            if(id!=''){
                                $("#remarks").modal('hide');
                            }
                            that.showLoading=false
                            if(that.schedule.length!=0){
                                that.$nextTick(()=>{ 
                                    that.Difference=that.$refs.week.offsetWidth-that.week.item.length*92
                                    var len=parseInt(that.$refs.week.offsetWidth/92)
                                    if(that.week.current>len/2){
                                        if(-that.week.current*92>that.Difference && that.week.current<=that.week.item.length-len){
                                            that.offsetLeft=-(that.week.current-len/2)*92
                                            that.animateLeft=-(that.week.current-len/2)*92
                                        }else{
                                            that.offsetLeft=that.Difference
                                            that.animateLeft=that.Difference
                                        }
                                    }else{
                                        that.offsetLeft=0
                                        that.animateLeft=0
                                    }
                                });
                            }
                        }else{
                            that.showLoading=false 
                        }
                    }
                })
            },
            eventWeek(data){
                if(data.category==null){
                    return
                }
                this.currentWeek=data
                this.holidayIndex=100
                this.replaceDay=100
            },
            replaceCourse(list){
                if(this.replaceData[list]){
                    // this.replaceDay=index
                    // this.replaceList=this.replaceData[list]
                    // console.log(this.replaceList)
                    return true
                } 
            },
            holidaysData(list){
                if(this.holidays[list]){
                    // this.holidayIndex=index
                    return true
                }
            },
            prevWeek(){
                if(this.offsetLeft-this.Difference<this.width){
                    this.offsetLeft=this.Difference-120
                    $(".animation").animate({marginLeft:this.offsetLeft+'px'});
                }else{
                    this.offsetLeft=this.offsetLeft-this.width
                    $(".animation").animate({marginLeft:this.offsetLeft+'px'});

                }
            },
            nextWeek(){
                if(this.offsetLeft>-this.width){
                    this.offsetLeft=0
                    $(".animation").animate({marginLeft:'0px'});

                }else{
                    this.offsetLeft=this.offsetLeft+this.width
                    $(".animation").animate({marginLeft:this.offsetLeft+'px'});

                }
            },
            headWeek(){
                this.offsetLeft=0
                $(".animation").animate({marginLeft:'0px'});
            },
            footWeek(){
                this.offsetLeft=this.Difference-120
                $(".animation").animate({marginLeft:this.offsetLeft+'px'});
            },
            exportChildList(){
                var scheduleArr = JSON.parse(JSON.stringify(this.schedule))
                var scheduleTypeList = '<label class="checkbox-inline"><input type="checkbox" checked="checked" class="J_check_all" data-checklist="J_check_c2" data-direction="y"> <?php echo Yii::t('global','Select All');?></label>';
                $.each(scheduleArr,function (key,val){
                    console.log(key)
                    scheduleTypeList += "<label class='checkbox-inline'><input type='checkbox' data-yid='J_check_c2' class='J_check' checked='checked'  value='"+key+"' name='scheduleTypeList[]'/> "+key+ "</label>";
                });
                $('#scheduleTypeList').html(scheduleTypeList);

                $("#exportChild").modal('show');
                var list = '<label><input type="checkbox" checked="checked" class="J_check_all" data-checklist="J_check_c1" data-direction="y"> <?php echo Yii::t('global','Select All');?></label>';
                $.each(childArr[this.classId],function(key,val){
                    list += "<div><label><input type='checkbox' data-yid='J_check_c1' class='J_check' checked='checked'  value='"+val.childId+"' name='childList[]'/> "+val.childName+ "</label></div>";
                });
                $("#childList").html(list);
                head.Util.checkAll();
            },
            delCourse(data){
                this.delId=data[0]
                $('#delModel').modal('show'); 
            },
            romoveCourse(){
                let that=this
                that.disabled=true
                $.ajax({
                    type: "POST",
                    url: "<?php echo $this->createUrl('removeStudentCourse')?>",
                    data: {id:this.delId},
                    dataType: "json",
                    success: function(data){
                       if(data.state=='success'){  
                           if(data.data!=null){
                            that.getData(that.studentId)
                                that.isShow=true
                                setTimeout(function () {
                                    $('#delModel').modal('hide');
                                }, 1000)
                                
                                resultTip({
                                    msg:data.state
                                });
                           }else{
                                resultTip({
                                    error: 'warning',
                                    msg:data.message
                                }); 
                           }
                       }
                       that.disabled=false 
                    },
                    error:function(){
                        that.disabled=false
                    }
                });
            },
            addCourse(week,time){
                this.addCourse_id=''
                this.weekId=week;
                this.times=time
                let that=this
                $.ajax({
                    type: "POST",
                    url: "<?php echo $this->createUrl('updateChildCourseList')?>",
                    data: {
                        "category":this.currentWeek.category,
                        "weekday": week+1,
                        "period": this.timetableTimes[this.times].period
                    },
                    dataType: "json",
                    success: function(data){
                       if(data.state=='success'){  
                           if(data.data.length!=0){
                               that.courseList=data.data
                               $('#editModel').modal('show'); 
                           }else{
                            resultTip({
                                error: 'warning',
                                msg:'<?php echo Yii::t('attends','No courses arranged in this period') ?>'
                            }); 
                        }
                       }else{
                            resultTip({
                                error: 'warning',
                                msg:data.message
                            }); 
                        }
                    },
                    error:function(){
                    }
                });
                
            },
            saveCourse(){
                let that=this
                this.disabled=true
                $.ajax({
                    type: "POST",
                    url: "<?php echo $this->createUrl('UpdateChildCourse')?>",
                    data:{
                        "course_id": this.addCourse_id,
                        "student_id": this.studentId,
                        "category":this.currentWeek.category,
                    },
                    dataType: "json",
                    success: function(data){
                       if(data.state=='success'){  
                           if(data.data!=null){
                                that.getData(that.studentId)
                                setTimeout(function () {
                                    $('#editModel').modal('hide');
                                }, 1000)
                                
                                resultTip({
                                    msg:data.state
                                });
                           }else{
                                resultTip({
                                    error: 'warning',
                                    msg:data.message
                                });
                           }
                       }
                       that.disabled=false
                    },
                    error:function(){
                        that.disabled=false
                    }
                });
            },
            courseQrcode(){
                let that=this
                $.ajax({
                    type: "POST",
                    url: "<?php echo $this->createUrl('attendance/getCourseQRCode')?>",
                    data:{
                        "child_id": this.studentId,
                    },
                    dataType: "json",
                    success: function(data){
                       if(data.state=='success'){  
                         that.qrcodeImg=data.data
                         that.qrcodeShow=true
                       }
                       that.disabled=false
                    },
                    error:function(){
                        that.disabled=false
                    }
                });
            }
        }
    })
    function quxiao() {
        $("#remarks").modal('hide');
        $("#exportChild").modal('hide');
        $('#content').val("")
    }
</script>

