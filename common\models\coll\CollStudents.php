<?php

/**
 * This is the model class for table "ivy_reg_students".
 *
 * The followings are the available columns in table 'ivy_reg_students':
 * @property integer $id
 * @property integer $coll_id
 * @property integer $classid
 * @property integer $childid
 * @property integer $status
 * @property integer $is_selected
 * @property integer $updated_by
 * @property integer $updated_at
 * @property integer $schoolid
 * @property integer $start_time
 * @property integer $end_time
 */
class CollStudents extends CActiveRecord
{
    const STATUS_DEFAULT = 0;
    const STATUS_CONDUCT = 1;
    const STATUS_COMPLETE = 2;
    const STATUS_AUDITED = 3;
    const STATUS_OVERRULE = 4;

    /**
     * @return string the associated database table name
     */
    public function tableName()
    {
        return 'ivy_coll_students';
    }

    /**
     * @return array validation rules for model attributes.
     */
    public function rules()
    {
        // NOTE: you should only define rules for those attributes that
        // will receive user inputs.
//        return array(
//            array('reg_id, classid, schoolid, is_selected, start_time, end_time, childid, status, updated_by, updated_at', 'required'),
//            array('reg_id, classid, childid, is_selected, start_time, end_time, status, updated_by, updated_at', 'numerical', 'integerOnly'=>true),
//            // The following rule is used by search().
//            array('schoolid', 'length', 'max'=>255),
//            // @todo Please remove those attributes that should not be searched.
//            array('id, reg_id, classid, childid, status, updated_by, updated_at', 'safe', 'on'=>'search'),
//        );
    }

    /**
     * @return array relational rules.
     */
    public function relations()
    {
        // NOTE: you may need to adjust the relation name and the related
        // class name for the relations automatically generated below.
        return array(
//            'childProfile'=>array(self::HAS_ONE, 'ChildProfileBasic', array('childid'=>'childid')),
        );
    }

    /**
     * @return array customized attribute labels (name=>label)
     */
    public function attributeLabels()
    {
        return array(
            'id' => 'ID',
            'coll_id' => 'Coll',
            'classid' => 'Classid',
            'childid' => 'Childid',
            'status' => 'Status',
            'is_selected' => 'IS Selected',
            'updated_by' => 'Updated By',
            'updated_at' => 'Updated At',
            'schoolid' => 'schoolid',
            'start_time' => 'start_time',
            'end_time' => 'end_time',
        );
    }

    /**
     * Retrieves a list of models based on the current search/filter conditions.
     *
     * Typical usecase:
     * - Initialize the model fields with values from filter form.
     * - Execute this method to get CActiveDataProvider instance which will filter
     * models according to data in model fields.
     * - Pass data provider to CGridView, CListView or any similar widget.
     *
     * @return CActiveDataProvider the data provider that can return the models
     * based on the search/filter conditions.
     */
    public function search()
    {
        // @todo Please modify the following code to remove attributes that should not be searched.

        $criteria=new CDbCriteria;

        $criteria->compare('id',$this->id);
        $criteria->compare('coll_id',$this->coll_id);
        $criteria->compare('classid',$this->classid);
        $criteria->compare('childid',$this->childid);
        $criteria->compare('status',$this->status);
        $criteria->compare('is_selected',$this->is_selected);
        $criteria->compare('updated_by',$this->updated_by);
        $criteria->compare('updated_at',$this->updated_at);
        $criteria->compare('start_time',$this->start_time);
        $criteria->compare('end_time',$this->end_time);

        return new CActiveDataProvider($this, array(
            'criteria'=>$criteria,
        ));
    }

    /**
     * @return CDbConnection the database connection used for this class
     */
    public function getDbConnection()
    {
        return Yii::app()->subdb;
    }

    /**
     * Returns the static model of the specified AR class.
     * Please note that you should have this exact method in all your CActiveRecord descendants!
     * @param string $className active record class name.
     * @return RegStudents the static model class
     */
    public static function model($className=__CLASS__)
    {
        return parent::model($className);
    }




}
