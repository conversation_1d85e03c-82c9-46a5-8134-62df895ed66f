<?php

/**
 * This is the model class for table "ivy_asa_vendor_info".
 *
 * The followings are the available columns in table 'ivy_asa_vendor_info':
 * @property integer $vendor_id
 * @property string $bank
 * @property string $bank_address
 * @property string $bank_account
 * @property string $bank_name
 * @property string $identify
 */
class AsaVendorInfo extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_asa_vendor_info';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('vendor_id,bank,bank_account,bank_name,identify', 'required'),
			array('vendor_id', 'numerical', 'integerOnly'=>true),
			array('bank, bank_account, bank_name, bank_address, identify', 'length', 'max'=>255),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('vendor_id, bank, bank_account, bank_address, bank_name, identify', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
			'vendorName' => array(self::BELONGS_TO, 'AsaVendor', "vendor_id"),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'vendor_id' => 'vendor_id',
			'bank' => Yii::t('asa', 'Bank Name'),
			'bank_account' => Yii::t('asa', 'Bank Account'),
			'bank_name' => Yii::t('asa', 'Beneficiary'),
			'bank_address' => Yii::t('asa', '开户行支行'),
			'identify' => Yii::t('asa', 'Identity Number'),
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('vendor_id',$this->vendor_id);
		$criteria->compare('bank',$this->bank,true);
		$criteria->compare('bank_account',$this->bank_account,true);
		$criteria->compare('bank_name',$this->bank_name,true);
		$criteria->compare('bank_address',$this->bank_address,true);
		$criteria->compare('identify',$this->identify,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return AsaVendorInfo the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}
