<?php

/**
 * This is the model class for table "ivy_yp_invoice_transaction".
 *
 * The followings are the available columns in table 'ivy_yp_invoice_transaction':
 * @property string $orderId
 * @property double $fact_amount
 * @property double $payable_amount
 * @property string $r2_TrxId
 * @property string $r7_Uid
 * @property string $rb_BankId
 * @property string $ro_BankOrderId
 * @property string $rp_PayDate
 * @property string $schoolId
 * @property integer $childId
 * @property integer $operatorId
 * @property integer $payment_method
 * @property integer $status
 * @property integer $orderTime
 * @property integer $updateTime
 * @property string $payed_status
 */
class YeepayOrder extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return YeepayOrder the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_yp_invoice_transaction';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('orderId, payable_amount', 'required'),
			array('childId, operatorId, payment_method, status, orderTime, updateTime', 'numerical', 'integerOnly'=>true),
			array('fact_amount, payable_amount', 'numerical'),
			array('orderId, r2_TrxId, r7_Uid, rb_BankId', 'length', 'max'=>50),
			array('ro_BankOrderId', 'length', 'max'=>100),
			array('rp_PayDate', 'length', 'max'=>20),
			array('schoolId', 'length', 'max'=>15),
			array('payed_status', 'length', 'max'=>2),
			array('flag', 'length', 'max'=>1),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('orderId, fact_amount, payable_amount, r2_TrxId, r7_Uid, rb_BankId, ro_BankOrderId, rp_PayDate, schoolId, childId, operatorId, payment_method, status, orderTime, updateTime, payed_status', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'ChildProfile' => array(self::BELONGS_TO, 'ChildProfileBasic', 'childId'),
            'school' => array(self::BELONGS_TO, 'Branch', 'schoolId'),
//            'items' => array(self::HAS_MANY, 'YeepayOrderItem', 'it_orderId', 'with'=>'invoice'),
            'items' => array(self::HAS_MANY, 'YeepayOrderItem', 'it_orderId'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
				'orderId' => 'Order',
				'fact_amount' => 'Actual Amount',
				'payable_amount' => 'Payable Amount',
				'r2_TrxId' => 'R2 Trx',
				'r7_Uid' => 'R7 Uid',
				'rb_BankId' => 'Rb Bank',
				'ro_BankOrderId' => 'Ro Bank Order',
				'rp_PayDate' => 'Rp Pay Date',
				'schoolId' => 'Campus',
				'childId' => 'Child',
				'operatorId' => 'Operator',
				'payment_method' => 'Payment Method',
				'status' => 'Status',
				'orderTime' => 'Order Time',
				'updateTime' => 'Update Time',
				'payed_status' => 'Payment Status',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('orderId',$this->orderId,true);
		$criteria->compare('fact_amount',$this->fact_amount);
		$criteria->compare('payable_amount',$this->payable_amount);
		$criteria->compare('r2_TrxId',$this->r2_TrxId,true);
		$criteria->compare('r7_Uid',$this->r7_Uid,true);
		$criteria->compare('rb_BankId',$this->rb_BankId,true);
		$criteria->compare('ro_BankOrderId',$this->ro_BankOrderId,true);
		$criteria->compare('rp_PayDate',$this->rp_PayDate,true);
		$criteria->compare('schoolId',$this->schoolId,true);
		$criteria->compare('childId',$this->childId);
		$criteria->compare('operatorId',$this->operatorId);
		$criteria->compare('payment_method',$this->payment_method);
		$criteria->compare('status',$this->status);
		$criteria->compare('orderTime',$this->orderTime);
		$criteria->compare('updateTime',$this->updateTime);
		$criteria->compare('payed_status',$this->payed_status,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
	
	public function genOrderID($prefix){
        $prefix = OA::isProduction() ? $prefix : '99'.$prefix;
		$maxId = Yii::app()->db->createCommand()
			->select('max(orderId)')
			->from($this->tableName())
			->where('orderId LIKE :prefix', array(':prefix'=>$prefix.'%'))
			->queryRow();
//		print_r($maxId);
		if(empty($maxId)) return $prefix . '001';
		
		$maxId = array_shift($maxId);
		$postfix = sprintf('%03s', intval(substr($maxId, -3)) + 1);
		return $prefix . $postfix;
	}
}