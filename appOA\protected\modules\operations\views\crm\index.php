<div class="h_a">搜索</div>
<div class="search_type cc mb10">
<?php $form=$this->beginWidget('CActiveForm', array(
	'id'=>'s-form',
	'enableAjaxValidation'=>false,
    'method'=>'get',
)); ?>
    <div class="ul_wrap">
        <ul class="cc">
            <li>
                <label for="parent_name">家长姓名：</label>
                <input type="text" id="parent_name" name="parent_name" value="<?php echo $parent_name?>" class="input length_3" />
            </li>
            <li>
                <label for="child_name">孩子姓名：</label>
                <input type="text" id="child_name" name="child_name" value="<?php echo $child_name?>" class="input length_3" />
            </li>
            <li>
                <label for="email">邮箱：</label>
                <input type="text" id="email" name="email" value="<?php echo $email?>" class="input length_3" />
            </li>
            <li>
                <label for="tel">电话：</label>
                <input type="text" id="tel" name="tel" value="<?php echo $tel?>" class="input length_3" />
            </li>
            <li>
                <label for="parent_name">孩子年龄：</label>
                <?php echo CHtml::dropDownList('age', $age, $this->cfg['ages'], array('empty'=>'全部'));?>
            </li>
            <li>
                <label for="school">学校：</label>
                <?php echo CHtml::dropDownList('school', $branchId, $this->branchArr, array('empty'=>'全部'));?>
            </li>
            <li>
                <label for="purpose">意向客户：</label>
                <?php echo CHtml::dropDownList('purpose', $purpose, $this->cfg['purpose'], array('empty'=>'全部'));?>
            </li>
            <li></li>
        </ul>
    </div>
    <div class="btn_side"><button class="btn" type="submit">搜索</button></div>
<?php $this->endWidget(); ?>
</div>

<div class="mb10">
    <a href="<?php echo $this->createUrl("/operations/crm/edit");?>" class="btn"><span class="add"></span>添加来访记录</a>
</div>

<?php
$this->widget('ext.ivyCGridView.IvyCGridView', array(
    'id'=>'crm-grid',
    'ajaxUpdate'=>false,
    'dataProvider'=>$dataProvider,
    'colgroups'=>array(
        array(
            "colwidth"=>array(150,150,150,150,200,80),
        )
    ),
	'columns'=>array(
		'child_name',
		'parent_name',
        array(
            'name'=>'年龄',
            'value'=>'OA::getAge($data->birth_timestamp)',
        ),
        array(
            'name'=>'学校',
            'value'=>'$data->renderSchool()',
        ),
        array(
            'name'=>'更新信息',
            'value'=>'$data->newlog->content',
        ),
        array(
			'class'=>'CButtonColumn',
            'template' => '{update} {addlog}',
            'updateButtonUrl'=>'Yii::app()->createUrl("/operations/crm/edit", array("id" => $data->id))',
            'updateButtonImageUrl'=>false,
            'buttons'=>array(
                'addlog'=>array(
                    'label'=>'跟踪记录',
                    'url'=>'Yii::app()->createUrl("/operations/crm/log", array("id" => $data->id))',
                    'options'=>array('class'=>'J_dialog'),
                ),
            ),
		)
    ),
));
?>