<?php
$disabled = true;
if ($data['workflow']->useRole == 1 && $data['workflow']->getOperateStatus() == WorkflowOperation::WORKFLOW_STATS_UNOP && $data['isFinance']) {
    $disabled = false;
}
$isPaid = $data['paymentApply']->payment_user > 0;
$payDate = date('Y.m.d', $data['paymentApply']->payment_time);
?>
<!-- Modal -->
<?php echo CHtml::form('/workflow/entrance/index', 'post', array('class' => 'J_ajaxForm')); ?>
<div class="modal fade" id="<?php echo $data['modalNameId'] ?>" tabindex="-1" role="dialog" aria-labelledby="workflowModalLabel">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel"><?php echo CommonUtils::autoLang($data['workflow']->definationObj->defination_name_cn, $data['workflow']->definationObj->defination_name_en); ?> - <?php echo CommonUtils::autoLang($data['workflow']->nodeObj->node_name_cn, $data['workflow']->nodeObj->node_name_en); ?><a href="#" id="print"> [<?php echo Yii::t('workflow', 'print'); ?>]</a></h4>
            </div>
            <div class="modal-body" id="workflow-modal-body">
                <div class="row">
                    <div class="panel panel-default" style="margin: 10px;">
                        <div class="panel-body">
                            <!-- 左侧Logo -->
                            <div class='flex'>
                                <div>
                                    <img class="centered-img" style="width: 180px;display: block;margin: 0 auto;" src="https://m2.files.ivykids.cn/cloud01-file-8014547FjYiFdWDDZo4YK_rBXjNuY1Snut9.png" alt="Logo">
                                </div>
                                <div class="flex1" style="margin-left: 180px;">
                                    <h4><?php echo CommonUtils::autoLang($data['workflow']->definationObj->defination_name_cn, $data['workflow']->definationObj->defination_name_en); ?></h4>
                                    <h4>唯一编号：<?php echo $data['workflow']->opertationObj->id; ?></h4>
                                </div>
                                <?php if ($isPaid) : ?>
                                    <div class='text-center mr10'>
                                        <img style='width:60px;height:60px' src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/payment.png' ?>" alt="Logo">
                                        <div style='color:#5CB85C' class='mt5'><?php echo $payDate; ?> </div>
                                    </div>
                                <?php endif; ?>
                            </div>

                        </div>
                    </div>
                    <div class="col-md-12">
                        <table class="table table-bordered table-contract">
                            <?php if (!$disabled && $data['paymentFlowList']) : ?>
                                <tr>
                                    <th><label>关联付款工作流 *</label></th>
                                    <td>
                                        <select name="paymentFlowId" class="form-control">
                                            <option value="">请选择</option>
                                            <?php foreach ($data['paymentFlowList'] as $flow) : ?>
                                                <option value="<?php echo $flow->defination_id; ?>"><?php echo CommonUtils::autoLang($flow->defination_name_cn, $flow->defination_name_en); ?></option>
                                            <?php endforeach; ?>
                                        </select>
                                    </td>
                                </tr>
                            <?php endif; ?>
                            <tr>
                                <th><label>合同标题：</label></th>
                                <td><?php echo $data['contractApplyModel']->title; ?></td>
                            </tr>
                            <tr>
                                <th><label>合同甲方：</label></th>
                                <td><?php echo $data['contractApplyModel']->party_a; ?></td>
                            </tr>
                            <tr>
                                <th><label>合同乙方：</label></th>
                                <td><?php echo $data['contractApplyModel']->party_b; ?></td>
                            </tr>
                            <tr>
                                <th><label>合同开始：</label></th>
                                <td><?php echo date('Y-m-d', $data['contractApplyModel']->start_date); ?></td>
                            </tr>
                            <tr>
                                <th><label>合同结束：</label></th>
                                <td><?php echo date('Y-m-d', $data['contractApplyModel']->end_date); ?></td>
                            </tr>
                            <tr>
                                <th><label>合同类型：</label></th>
                                <td><?php echo $data['categoryList'][$data['contractApplyModel']->category]; ?></td>
                            </tr>
                            <?php if($data['contractApplyModel']->category == 2 ): ?>
                                <tr>
                                    <th><label>预估总额：</label></th>
                                    <td>
                                        <?php echo number_format($data['contractApplyModel']->price_total / 100, 2); ?>
                                        <span class="mt5" style="color: #B8860B;">（框架合同的金额为预估金额，仅作为内部参考，不作为结算依据。）</span>
                                    </td>
                                </tr>
                            <?php else: ?>
                                <tr>
                                    <th><label>合同总额（含税）：</label></th>
                                    <td><?php echo number_format($data['contractApplyModel']->price_total / 100, 2); ?></td>
                                </tr>
                            <?php endif; ?>
                            <tr>
                                <th><label>付款周期：</label></th>
                                <td><?php echo $data['cycleList'][$data['contractApplyModel']->cycle]; ?></td>
                            </tr>
                            <tr>
                                <th><label>收款方：</label></th>
                                <td><?php echo $data['contractApplyModel']->bank_user; ?></td>
                            </tr>
                            <tr>
                                <th><label>银行账号：</label></th>
                                <td><?php echo $data['contractApplyModel']->bank_account; ?></td>
                            </tr>
                            <tr>
                                <th><label>开户银行：</label></th>
                                <td><?php echo $data['contractApplyModel']->bank_name; ?></td>
                            </tr>
                            <tr>
                                <th><label>预算标准：</label></th>
                                <td>
                                    <?php if ($disabled): echo $data['contractApplyModel']->budget == 1 ? '预算内' : '预算外'; ?>
                                    <?php else: ?>
                                        <label>
                                            <input <?php if ($data['contractApplyModel']->budget == 1) echo 'checked'; ?> type="radio" name="budget" value="1"> 预算内
                                        </label>
                                        <label>
                                            <input <?php if ($data['contractApplyModel']->budget == 0) echo 'checked'; ?> type="radio" name="budget" value="0"> 预算外
                                        </label>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        </table>
                    </div>

                    <?php if ($data['paidData']['items']) : ?>
                        <div class="col-xs-12">
                            <hr>
                            <table class="table table-bordered">
                                <caption>
                                    <h4>付款信息</h4>
                                </caption>
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>标题</th>
                                        <th>状态</th>
                                        <th>申请日期</th>
                                        <th>付款日期</th>
                                        <th>金额</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($data['paidData']['items'] as $key => $item) : ?>
                                        <tr>
                                            <td><?php echo $key + 1; ?></td>
                                            <td><?php echo $item['title']; ?></td>
                                            <td><?php echo $data['paidData']['stateList'][$item['state']]; ?></td>
                                            <td><?php echo $item['apply_date']; ?></td>
                                            <td><?php echo $item['date']; ?></td>
                                            <td class="text-right"><?php echo number_format($item['amount'], 2); ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                    <tr>
                                        <td colspan="6" class="text-right">总计：<?php echo number_format($data['paidData']['total'], 2); ?>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
                <!-- 显示附件 -->
                <br>
                <div id="fileList">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>相关资料</th>
                            </tr>
                        </thead>
                        <?php foreach ($data['workflow']->getAttachmentList() as $uploadFile): ?>
                            <tr>
                                <td><a target="_blank" href="<?php echo $uploadFile['bigimages']; ?>"><?php echo $uploadFile['notes']; ?></a></td>
                            </tr>
                        <?php endforeach; ?>
                    </table>
                </div>
                <?php $this->renderPartial('workflow.views.entrance.plug.progressDetail', array('data' => $data)); ?>
            </div>
            <?php if ($data['workflow']->useRole == 1 && $data['workflow']->getOperateStatus() == WorkflowOperation::WORKFLOW_STATS_UNOP && !$data['workflow']->isFristNode()) : ?>
                <div class="modal-footer">
                    <?php echo CHtml::hiddenField('definationId', $data['workflow']->definationObj->defination_id); ?>
                    <?php echo CHtml::hiddenField('nodeId', $data['workflow']->nodeObj->node_id); ?>
                    <?php echo CHtml::hiddenField('operationId', $data['workflow']->opertationObj->id); ?>
                    <?php echo CHtml::hiddenField('branchId', $data['workflow']->opertationObj->branchid); ?>
                    <?php echo CHtml::hiddenField('action', 'show'); ?>
                    <?php echo CHtml::hiddenField('buttonCategory', ''); ?>
                    <button type="button" class="btn btn-primary J_ajax_submit_btn" data-category="<?php echo WorkflowOperation::WORKFLOW_STATS_OPED; ?>"><?php echo Yii::t('workflow', 'Yes'); ?></button>
                    <button type="button" class="btn btn-info J_ajax_submit_btn" data-category="<?php echo WorkflowOperation::WORKFLOW_STATS_RESET; ?>"><?php echo Yii::t('workflow', '退回修改'); ?></button>
                    <button type="button" class="btn btn-danger J_ajax_submit_btn" data-category="<?php echo WorkflowOperation::WORKFLOW_STATS_OPDENY; ?>"><?php echo Yii::t('workflow', '拒绝并作废'); ?></button>
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('global', 'Cancel'); ?></button>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php CHtml::endForm(); ?>

<script>
    //打印
    $(function() {
        const searchUrl = '<?php echo Yii::app()->createUrl("/workflow/index/searchVendor", array('branchId' => $data['workflow']->schoolId)); ?>';

        $("#bank_user").autocomplete({
            source: searchUrl,
            // minLength: 2,
            select: function(event, ui) {
                if (ui.item.value <= 0) {
                    return false;
                }
                $("#vendor_id").val(ui.item.vendor_id);
                $("#bank_user").val(ui.item.bank_user);
                $("#bank_name").val(ui.item.bank_name);
                $("#bank_account").val(ui.item.bank_account);
                // return false;
            },
        });

        $("#print").click(function() {
            $("#workflow-modal-body a").removeAttr('href');
            $("#workflow-modal-body").printThis({
                popTitle: '采购申请单'
            });
        })

        $('#workflow-contract-modal').on('hidden.bs.modal', function () {
            // 检查是否还有其他模态框打开
            if($('.modal:visible').length) {
                // 添加 modal-open class
                $('body').addClass('modal-open');
            }
        });
    });
</script>