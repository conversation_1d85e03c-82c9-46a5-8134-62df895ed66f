<?php
Yii::import('common.models.visit.*');
$class_list = $child->getConfig();
if (!$childinterview) {
    $childinterview = new ChildInterview();
}
$options = array(
    'a' => 'A',
    'b' => 'B',
    'c' => 'C',
    'd' => 'D',
);

$form = $this->beginWidget('CActiveForm', array(
    'id' => 'child_interivew',
    'enableAjaxValidation' => false,
    'action' => $this->createUrl('interviewResults', array('childid' => $child->id)),
    'htmlOptions' => array('class' => 'J_ajaxForm', 'role' => 'form'),
));

$select_interview_results = array(
    AdmissionsDs::STATS_HAS_BEEN_PAID,
    AdmissionsDs::STATS__INTERVIEW_PASS,
    AdmissionsDs::STATS__INTERVIEW_OUT,
    AdmissionsDs::STATS__INTERVIEW_ALTERNATE,
);
$results = array();
if ($childinterview->discipline_stat) {
    $results = CJSON::decode($childinterview->discipline_stat);
}

?>

<div class="modal-body">
    <table class="table table-bordered">
        <h3 class="text-center"><?php echo Yii::t('interview', 'Daystar Academy Kindergarten Interview'); ?></h3><br/>
    </table>
    <div class="table-responsive">
        <table class="table table-bordered">
            <tr>
                <td width="280"><?php echo Yii::t('user', 'Child Name') ?>：<?php echo $child->getName() ?></td>
                <td width="280"><?php echo Yii::t('user', 'Date Of Birth') ?>
                    ：<?php echo date("Y-m-d", $child->birthday) ?></td>
                <td width="280" colspan="3"><?php echo Yii::t('interview', 'Gender') ?>
                    ：<?php echo ($child->gender == 1) ? '男' : '女'; ?>
                </td>
            </tr>
            <!--<tr>
                <!-- <td width="200">当前所在学校：</td>
                 <td width="200">当前所在年级：</td>
                <td width="200" colspan="3"><php /*echo Yii::t('interview','Application Grade')*/?>： <php /*echo $class_list['grade'][$child->start_grade] */?> </td>
            </tr>-->
        </table>
    </div>
    <div class="">

        <div class="table-responsive">
            <table class="table table-bordered">
                <tbody>
                <tr bgcolor="#E2EFD9">
                    <th class="text-center" style="width: 500px"><h4
                            style="display: inline-block"><?php echo Yii::t('interview', 'content of interview') ?>
                            ：</h4>
                    </th>
                    <th style="width: 45px">
                        <h4 class="pull-right">C</h4>
                    </th>
                    <th style="width: 45px">
                        <h4 class="pull-right">P</h4>
                    </th>
                    <th style="width: 45px">
                        <h4 class="pull-right">M</h4>
                    </th>
                </tr>
                <tr>
                    <td>
                        <h4 style="display: inline-block"><?php echo Yii::t('interview', 'Separation Anxiety') ?></h4>
                    </td>
                    <td>
                        <div class="radio-inline pull-right">
                            <?php echo CHtml::textField("interview[separationC]", ($results) ? $results['separationC'] : "", array('class' => "form-control", 'style' => 'width:100%')) ?>
                        </div>
                    </td>
                    <td>
                        <div class="radio-inline pull-right">
                            <?php echo CHtml::textField("interview[separationP]", ($results) ? $results['separationP'] : "", array('class' => "form-control", 'style' => 'width:100%')) ?>
                        </div>
                    </td>
                    <td>
                        <div class="radio-inline pull-right">
                            <?php echo CHtml::textField("interview[separationM]", ($results) ? $results['separationM'] : "", array('class' => "form-control", 'style' => 'width:100%')) ?>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>
                        <h4 style="display: inline-block"><?php echo Yii::t('interview', 'Can be	interviewed	separately	from parents?') ?></h4>
                    </td>
                    <td>
                        <div class="radio-inline pull-right">
                            <?php echo CHtml::textField("interview[interviewedC]", ($results) ? $results['interviewedC'] : "", array('class' => "form-control", 'style' => 'width:100%')) ?>
                        </div>
                    </td>
                    <td>
                        <div class="radio-inline pull-right">
                            <?php echo CHtml::textField("interview[interviewedP]", ($results) ? $results['interviewedP'] : "", array('class' => "form-control", 'style' => 'width:100%')) ?>
                        </div>
                    </td>
                    <td>
                        <div class="radio-inline pull-right">
                            <?php echo CHtml::textField("interview[interviewedM]", ($results) ? $results['interviewedM'] : "", array('class' => "form-control", 'style' => 'width:100%')) ?>
                        </div>
                    </td>
                </tr>

                <tr>
                    <td>
                        <h4 style="display: inline-block"><?php echo Yii::t('interview', 'Eye contact?') ?></h4>
                    </td>
                    <td>
                        <div class="radio-inline pull-right">
                            <?php echo CHtml::textField("interview[eyeC]", ($results) ? $results['eyeC'] : "", array('class' => "form-control", 'style' => 'width:100%')) ?>
                        </div>
                    </td>
                    <td>
                        <div class="radio-inline pull-right">
                            <?php echo CHtml::textField("interview[eyeP]", ($results) ? $results['eyeP'] : "", array('class' => "form-control", 'style' => 'width:100%')) ?>
                        </div>
                    </td>
                    <td>
                        <div class="radio-inline pull-right">
                            <?php echo CHtml::textField("interview[eyeM]", ($results) ? $results['eyeM'] : "", array('class' => "form-control", 'style' => 'width:100%')) ?>
                        </div>
                    </td>
                </tr>

                <tr>
                    <td>
                        <h4 style="display: inline-block"><?php echo Yii::t('interview', 'Can answer questions?') ?></h4>
                    </td>
                    <td>
                        <div class="radio-inline pull-right">
                            <?php echo CHtml::textField("interview[questionsC]", ($results) ? $results['questionsC'] : "", array('class' => "form-control", 'style' => 'width:100%')) ?>
                        </div>
                    </td>
                    <td>
                        <div class="radio-inline pull-right">
                            <?php echo CHtml::textField("interview[questionsP]", ($results) ? $results['questionsP'] : "", array('class' => "form-control", 'style' => 'width:100%')) ?>
                        </div>
                    </td>
                    <td>
                        <div class="radio-inline pull-right">
                            <?php echo CHtml::textField("interview[questionsM]", ($results) ? $results['questionsM'] : "", array('class' => "form-control", 'style' => 'width:100%')) ?>
                        </div>
                    </td>
                </tr>

                <tr>
                    <td>
                        <h4 style="display: inline-block"><?php echo Yii::t('interview', 'Eye-hand cooperation') ?></h4>
                    </td>
                    <td>
                        <div class="radio-inline pull-right">
                            <?php echo CHtml::textField("interview[eyeHandC]", ($results) ? $results['eyeHandC'] : "", array('class' => "form-control", 'style' => 'width:100%')) ?>
                        </div>
                    </td>
                    <td>
                        <div class="radio-inline pull-right">
                            <?php echo CHtml::textField("interview[eyeHandP]", ($results) ? $results['eyeHandP'] : "", array('class' => "form-control", 'style' => 'width:100%')) ?>
                        </div>
                    </td>
                    <td>
                        <div class="radio-inline pull-right">
                            <?php echo CHtml::textField("interview[eyeHandM]", ($results) ? $results['eyeHandM'] : "", array('class' => "form-control", 'style' => 'width:100%')) ?>
                        </div>
                    </td>
                </tr>

                <tr>
                    <td>
                        <h4 style="display: inline-block"><?php echo Yii::t('interview', 'Strong	hand') ?></h4>
                    </td>
                    <td>
                        <div class="radio-inline pull-right">
                            <?php echo CHtml::textField("interview[handC]", ($results) ? $results['handC'] : "", array('class' => "form-control", 'style' => 'width:100%')) ?>
                        </div>
                    </td>
                    <td>
                        <div class="radio-inline pull-right">
                            <?php echo CHtml::textField("interview[handP]", ($results) ? $results['handP'] : "", array('class' => "form-control", 'style' => 'width:100%')) ?>
                        </div>
                    </td>
                    <td>
                        <div class="radio-inline pull-right">
                            <?php echo CHtml::textField("interview[handM]", ($results) ? $results['handM'] : "", array('class' => "form-control", 'style' => 'width:100%')) ?>
                        </div>
                    </td>
                </tr>

                <tr>
                    <td>
                        <h4 style="display: inline-block"><?php echo Yii::t('interview', 'Pencil	grasps') ?></h4>
                    </td>
                    <td>
                        <div class="radio-inline pull-right">
                            <?php echo CHtml::textField("interview[graspsC]", ($results) ? $results['graspsC'] : "", array('class' => "form-control", 'style' => 'width:100%')) ?>
                        </div>
                    </td>
                    <td>
                        <div class="radio-inline pull-right">
                            <?php echo CHtml::textField("interview[graspsP]", ($results) ? $results['graspsP'] : "", array('class' => "form-control", 'style' => 'width:100%')) ?>
                        </div>
                    </td>
                    <td>
                        <div class="radio-inline pull-right">
                            <?php echo CHtml::textField("interview[graspsM]", ($results) ? $results['graspsM'] : "", array('class' => "form-control", 'style' => 'width:100%')) ?>
                        </div>
                    </td>
                </tr>

                <tr>
                    <td>
                        <h4 style="display: inline-block"><?php echo Yii::t('interview', 'Length of concentration?') ?></h4>
                    </td>
                    <td>
                        <div class="radio-inline pull-right">
                            <?php echo CHtml::textField("interview[concentrationC]", ($results) ? $results['concentrationC'] : "", array('class' => "form-control", 'style' => 'width:100%')) ?>
                        </div>
                    </td>
                    <td>
                        <div class="radio-inline pull-right">
                            <?php echo CHtml::textField("interview[concentrationP]", ($results) ? $results['concentrationP'] : "", array('class' => "form-control", 'style' => 'width:100%')) ?>
                        </div>
                    </td>
                    <td>
                        <div class="radio-inline pull-right">
                            <?php echo CHtml::textField("interview[concentrationM]", ($results) ? $results['concentrationM'] : "", array('class' => "form-control", 'style' => 'width:100%')) ?>
                        </div>
                    </td>
                </tr>

                <tr>
                    <td>
                        <h4 style="display: inline-block"><?php echo Yii::t('interview', 'pack away works?') ?></h4>
                    </td>
                    <td>
                        <div class="radio-inline pull-right">
                            <?php echo CHtml::textField("interview[worksC]", ($results) ? $results['worksC'] : "", array('class' => "form-control", 'style' => 'width:100%')) ?>
                        </div>
                    </td>
                    <td>
                        <div class="radio-inline pull-right">
                            <?php echo CHtml::textField("interview[worksP]", ($results) ? $results['worksP'] : "", array('class' => "form-control", 'style' => 'width:100%')) ?>
                        </div>
                    </td>
                    <td>
                        <div class="radio-inline pull-right">
                            <?php echo CHtml::textField("interview[worksM]", ($results) ? $results['worksM'] : "", array('class' => "form-control", 'style' => 'width:100%')) ?>
                        </div>
                    </td>
                </tr>
                <tr bgcolor="#E2EFD9">
                    <td colspan="4"><?php echo Yii::t('interview', 'For children above 5') ?></td>
                </tr>

                <tr>
                    <td>
                        <h4 style="display: inline-block"><?php echo Yii::t('interview', 'Verbal	ability') ?></h4>
                    </td>
                    <td>
                        <div class="radio-inline pull-right">
                            <?php echo CHtml::textField("interview[aboveC]", ($results) ? $results['aboveC'] : "", array('class' => "form-control", 'style' => 'width:100%')) ?>
                        </div>
                    </td>
                    <td>
                        <div class="radio-inline pull-right">
                            <?php echo CHtml::textField("interview[aboveP]", ($results) ? $results['aboveP'] : "", array('class' => "form-control", 'style' => 'width:100%')) ?>
                        </div>
                    </td>
                    <td>
                        <div class="radio-inline pull-right">
                            <?php echo CHtml::textField("interview[aboveM]", ($results) ? $results['aboveM'] : "", array('class' => "form-control", 'style' => 'width:100%')) ?>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>
                        <h4 style="display: inline-block"><?php echo Yii::t('interview', 'Reading ability') ?></h4>
                    </td>
                    <td>
                        <div class="radio-inline pull-right">
                            <?php echo CHtml::textField("interview[readingC]", ($results) ? $results['readingC'] : "", array('class' => "form-control", 'style' => 'width:100%')) ?>
                        </div>
                    </td>
                    <td>
                        <div class="radio-inline pull-right">
                            <?php echo CHtml::textField("interview[readingP]", ($results) ? $results['readingP'] : "", array('class' => "form-control", 'style' => 'width:100%')) ?>
                        </div>
                    </td>
                    <td>
                        <div class="radio-inline pull-right">
                            <?php echo CHtml::textField("interview[readingM]", ($results) ? $results['readingM'] : "", array('class' => "form-control", 'style' => 'width:100%')) ?>
                        </div>
                    </td>
                </tr>

                <tr>
                    <td>
                        <h4 style="display: inline-block"><?php echo Yii::t('interview', 'Math') ?></h4>
                    </td>
                    <td>
                        <div class="radio-inline pull-right">
                            <?php echo CHtml::textField("interview[mathC]", ($results) ? $results['mathC'] : "", array('class' => "form-control", 'style' => 'width:100%')) ?>
                        </div>
                    </td>
                    <td>
                        <div class="radio-inline pull-right">
                            <?php echo CHtml::textField("interview[mathP]", ($results) ? $results['mathP'] : "", array('class' => "form-control", 'style' => 'width:100%')) ?>
                        </div>
                    </td>
                    <td>
                        <div class="radio-inline pull-right">
                            <?php echo CHtml::textField("interview[mathM]", ($results) ? $results['mathM'] : "", array('class' => "form-control", 'style' => 'width:100%')) ?>
                        </div>
                    </td>
                </tr>

                <tr>
                    <td>
                        <h4 style="display: inline-block"><?php echo Yii::t('interview', 'Behavior') ?></h4>
                    </td>
                    <td>
                        <div class="radio-inline pull-right">
                            <?php echo CHtml::textField("interview[behaviorC]", ($results) ? $results['behaviorC'] : "", array('class' => "form-control", 'style' => 'width:100%')) ?>
                        </div>
                    </td>
                    <td>
                        <div class="radio-inline pull-right">
                            <?php echo CHtml::textField("interview[behaviorP]", ($results) ? $results['behaviorP'] : "", array('class' => "form-control", 'style' => 'width:100%')) ?>
                        </div>
                    </td>
                    <td>
                        <div class="radio-inline pull-right">
                            <?php echo CHtml::textField("interview[behaviorM]", ($results) ? $results['behaviorM'] : "", array('class' => "form-control", 'style' => 'width:100%')) ?>
                        </div>
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
    </div>



    <div class="table-responsive">
        <table class="table table-bordered"><div style="float: right"></div>
            <tbody>
            <tr bgcolor="#E2EFD9">
                <td colspan="4"><?php echo Yii::t('interview', 'Feedback') ?></td>
            </tr>
            <tr>
                <td colspan="5">
                    <div class="form-group">
                        <?php echo CHtml::textArea("interview[feedback]", ($results) ? $results['feedback'] : "", array('class' => 'form-control')); ?>
                    </div>
                </td>
            </tr>
            </tbody>
        </table>
    </div>


    <?php
    if (Yii::app()->user->checkAccess('o_A_Adm_Visits') && $child->status != AdmissionsDs::STATS__TRANSFER_CHILD): ?>
        <div class="form-group"><?php echo Yii::t('interview', 'Whether to pass the interview') ?>:
            <label for="exampleInputEmail1">
                <?php echo CHtml::radioButtonList('stat', $child->status, array('41' => Yii::t('user', 'No'), '40' => Yii::t('user', 'Yes'), '42' => Yii::t('user', '候补')), array('separator' => '&nbsp;&nbsp;')); ?>
        </div>
    <?php endif; ?>
</div>

<div class="modal-footer">
    <button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global', 'Submit') ?></button>
    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('global', 'Cancel') ?></button>
</div>
<?php $this->endWidget(); ?>
