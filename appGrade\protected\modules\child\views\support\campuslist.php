<?php
$this->breadcrumbs=array(
		Yii::t("navigations", 'Support')=>array('/child/support'),
		Yii::t("navigations", 'Campus List'),
);?>

<?php

$cfg = Mims::LoadConfig('CfgBranch');
$programs = $cfg["programs"];
?>
<?php foreach($cities as $city):?>

<?php if($city->campuslist):?>
<h1>
	<label>
	<?php echo $city->getContent();?>
	</label>
</h1>

<div class="city-box">
	<?php foreach($city->campuslist as $campus):?>
		<div class="campus type-<?php echo $campus->group;?>">
			<h3><?php echo $campus->title;?>
				<span>
					<?php
						echo CHtml::link($programs[$campus->group]["sign"], $programs[$campus->group]["url"], array("target"=>"_blank", "title"=>$programs[$campus->group]["title"]));
					?>
				</span>
			</h3>
			<dl>
				<dt><?php echo addColon($campus->info->getAttributeLabel('tel'));?></dt>
				<dd><?php echo $campus->info->tel;;?></dd>
			</dl>
			<dl>
				<dt><?php echo addColon($campus->info->getAttributeLabel('fax'));?></dt>
				<dd><?php echo $campus->info->fax;;?></dd>
			</dl>
			<dl>
				<dt><?php echo addColon($campus->info->getAttributeLabel('support_email'));?></dt>
				<dd><?php echo $campus->info->support_email;;?></dd>
			</dl>
			<dl>
				<dt><?php echo addColon($campus->info->getAttributeLabel('address'));?></dt>
				<dd><?php echo $campus->info->getLangContent("address");;?></dd>
			</dl>
		</div>
		
	<?php endforeach;?>
</div>	
	
<?php endif;?>
<?php endforeach;?>


<div class="clearfix"></div>

<style>
.city-box{
text-align:right;
}
.city-box .campus{
	text-align:left;
	padding: 10px;
	display:inline-block;
	width: 300px;
	border: 2px dotted #E8E7DA;
	margin: 0 0 10px 10px;
	color:#666;
}
.campus h3{
	margin-bottom: 0.5em;
	font-weight:bold;
	position: relative;
	text-shadow: 2px 1px 1px rgba(255, 255, 255, 0.8);
}
.campus h3 span{
	position: absolute;
	right: 10px;
	top: 0;
	text-shadow: none;
}
.campus h3 span a{
	color:#fff;
}
.campus dl{
	clear:both;
	margin-bottom:0.5em;
}
.campus dt{
	float: left;
	width: 80px;
	text-align:right;
	padding-right: 10px;
}
.campus dd{
	margin-left:0;
	padding-left:90px;
}
.type-10{
	background-color:#90B82F;
}
.type-20{
	background-color:#F36601;
	color:#333 !important;
}
.type-30{
	background-color:#01C2F4;
}
</style>