
<style>
	 [v-cloak] {
        display: none;
    }
    #ui-datepicker-div{
    	z-index: 99 !important 
    }
</style>
<div class="container-fluid">
    <ol class="breadcrumb">
	    <li><?php echo CHtml::link(Yii::t('site','Campus Operations'), array('//mcampus/default/index'))?></li>
	    <li><?php echo CHtml::link(Yii::t('site','Advanced'), array('//mcampus/default/index'))?></li>
	    <li class="active"><?php echo Yii::t('site','Discount Report');?></li>
	</ol>
  <div class="form-inline mb15">
	
	  <input type="text" class="form-control form-group" id="datepicker" placeholder="请选择日期"> 
	  <button type="button" class="btn btn-primary btn-sm ml15" id="search" onclick='search()'>确认</button>
	  <?php if (date('m') <= 8): ?>
		  <a href="javascript:void(0)"  id="nextYear" onclick='nextYear()'>下学年（09.01）</a>
	  <?php endif; ?>
  </div>
  <div class="row"  id='datalist' v-cloak v-if='data !=""'>
  <div class="col-md-12">
  	<div class="panel panel-default">
	  <div class="panel-body">
	    <p>{{monDate}} 共 {{data.total_invoice_num_str}} 学费账单，总金额为 {{data.total_invoice_amount_str}}，其中 {{data.discount_invoice_num_str}} 为折扣账单，折扣账单比例 {{data.invoice_percent }}%</p>
        <p>所有折扣账单原价金额为 {{data.nodiscount_invoice_amount_str}}，折扣后实际金额为 {{data.discount_invoice_amount_str}}，折扣总额为
        {{data.invoice_discount_amount_str}}，折扣率平均为 {{ data.invoice_discount_percent }} 折</p>
	  </div>
	</div>
  </div>
	 <div class="col-md-2">
        <ul class="list-group">
          <a  href="javascript:void(0)" class="list-group-item all active" @click="all($event)"><span class="badge">{{len}}</span>全部折扣</a>
          <a v-for='(list,id,index) in list' @click="addClassFun(id,index,list.discount_discount,list.discount_name,list.invoice_amount)" href="javascript:void(0)" class="list-group-item" :class='{active:index==qwerqwre}'><span class="badge">{{list.invoice_num}}</span>{{parseInt(list.discount_discount)}}% {{list.discount_name}}</a>
        </ul>
	</div>
  	<div class="col-md-10">
  	  <table id="tabledata" class="table table-bordered mb15" v-if='table!=""'>
  	  <thead>
  	  	  <colgroup>
  	  	      <col style="width:5%">
  	  	      <col style="width:5%">
	          <col style="width:10%">
	          <col style="width:15%">
	          <col style="width:15%">
	          <col style="width:5%">
	          <col style="width:15%">
	          <col style="width:8%">
	          <col style="width:8%">
	          <col style="width:8%">
	          <col style="width:8%">
	          <col style="width:8%">
	      </colgroup>
  	  </thead>
  	    <tbody>
		   <tr>
		    <td>#</td>
		   	<td @click="sort('child_name')">学生ID</td>
		   	<td @click="sort('child_name')">学生姓名</td>
		   	<td @click="sort('class_title')">班级</td>
		   	<td>学费账单</td>
		   	<td @click="sort('status')">账单状态</td>
		   	<td @click="sort('discount_id')">折扣名称</td>
		   	<td @click="sort('discount_id')">折扣</td>
		   	<td @click="sort('nodiscount_amount')">原价</td>  
		   	<td @click="sort('amount')">账单金额</td>
		   	<td @click="sort('amount')">折扣金额</td>
		   	<td @click="sort('monthly')">每月折扣金额</td>
		   </tr>
		   <tr v-for='(data,index) in sortedTable' :class="{ 'danger': data.status < 3 }">
		     <td>{{index+1}}</td>
			 <td>{{data.childid}}</td>
		   	 <td><a target="_blank" :href="'<?php echo $this->createUrl("/child/index/index"); ?>&childid='+data.childid">{{data.child_name}}<span v-if='data.flag' class="ml5 label label-warning" title="<?= Yii::t('invoice', 'Multiple discounted invoices found, please correct.'); ?>">!</span></a></td>
		   	 <td>{{data.class_title}}</td>
		   	 <td><a class="mr5 J_dialog" non-resize="1" role="button" :href="'<?php echo $this->createUrl("/child/invoice/viewInvoice"); ?>&childid='+data.childid+'&invoiceid='+data.invoice_id">{{data.invoive_title}}</a></td>
		   	 <td>{{data.status == 1 ? '未付' : (data.status == 2 ? '部分付' : '已付')}}</td>
		   	 <td>{{list[data.discount_id].discount_name}}</td>
		   	 <td class="text-right">{{list[data.discount_id].discount_discount}}</td>  	 
		   	 <td class="text-right">{{data.nodiscount_amount}}</td>
		   	 <td class="text-right">{{data.amount}}</td>
		   	 <td class="text-right">{{(data.nodiscount_amount-data.amount).toFixed(2)}}</td>
		   	 <td class="text-right">{{(data.monthly).toFixed(2)}}</td>
		   </tr>
		   <tr>
		     <td>总计</td>
		     <td></td>
		     <td></td>
			 <td></td>
		     <td></td>
		     <td></td>
		     <td></td>
		     <td></td>
		     <td class="text-right">{{add(table).toFixed(2)}}</td>
		     <td class="text-right">
                 <template v-if='total==""'>
                 	{{totals(table).toFixed(2)}}
                 </template>
                 <template v-else>
                 	{{total.toFixed(2)}}
                 </template>
		     </td>
		     <td class="text-right">
                 <template v-if='total==""'>
                 	{{(add(table)-totals(table)).toFixed(2)}}
                 </template>
                 <template v-else>
                 	{{(add(table)-total).toFixed(2)}}
                 </template>
		     </td>
		     <td class="text-right">{{last(table).toFixed(2)}}</td>		     
		   </tr>
  	    </tbody>
	  </table>
	  <button v-if='table!=""' type="button" class="btn btn-primary btn-xsm" onclick="exportXsl()">导出表格</button>
	  <br>
  	</div>
  </div>
  </div>
<script>
	$( function() {
		var date = new Date();
        var year = date.getFullYear();
        var month = date.getMonth() + 1;
        var strDate = date.getDate();
        if (month >= 1 && month <= 9) {
            month = "0" + month;
        }
        if (strDate >= 0 && strDate <= 9) {
            strDate = "0" + strDate;
        }
        var currentdate = year + "-" + month + "-" + strDate;
        $('#datepicker').val(currentdate)
		$( "#datepicker" ).datepicker({
		  dateFormat: "yy-mm-dd",
		});
		search()
    });

	function nextYear(){
		$('#nextYear').addClass('disabled').attr('disabled', 'disabled')
		var date = new Date();
        var year = date.getFullYear();
        var month = date.getMonth() + 1;
        var strDate = date.getDate();
        if (month >= 1 && month <= 9) {
            month = "0" + month;
        }
        if (strDate >= 0 && strDate <= 9) {
            strDate = "0" + strDate;
        }
        var currentdate = year + "-" + month + "-" + strDate;
        var nextYear = month >= 9 ? year + 1 : year;
        var nextDate = nextYear + "-09-01";
        $('#datepicker').val(nextDate)
		search()
	}

	function search(){
		if($('#datepicker').val()==''){
			resultTip({
	                    error: 'warning',
	                    msg: '请选择日期'
	                });
			return
		}
		$('#search').addClass('disabled').attr('disabled', 'disabled')
		$('#search').html('查询中...')
		$.ajax({
	        url: '<?php echo $this->createUrl("getData")?>',
	        type: "get",
	        async: true,
	        dataType: 'json',
	        data: {
	            date:$('#datepicker').val(),
	        },
	        success: function(data) {
	            console.log(data)
	            $('#search').removeClass('disabled').removeAttr('disabled');
	            $('#nextYear').removeClass('disabled').removeAttr('disabled');
		        $('#search').html('确定')
	            if(data.state == 'success') {
	            	datalist.data=data.data
	            	datalist.list=data.data.discount_info
	            	datalist.monDate=$('#datepicker').val()
	            	// 显示渲染所有数据
        			var datas=[]
        			for(key in datalist.data.items){
        				for(data in datalist.data.items[key]){
        					datas.push(datalist.data.items[key][data])
        				}
        				
        			}
        	        datalist.table =datas
        	        datalist.qwerqwre = '-1';
	            } else {
	                resultTip({
	                    error: 'warning',
	                    msg: data.message
	                });
	            }

	        },
	        error: function() {
	            alert("请求错误")
	        }
	    });
	}

	function exportXsl(){
		var elt = document.getElementById('tabledata');
		var wb = XLSX.utils.table_to_book(elt, {sheet:"<?php echo Yii::t('asa','折扣统计'); ?>"});
		return XLSX.writeFile(wb, '<?php echo Yii::t('asa','折扣统计'); ?>.xlsx');
	}

	var datalist = new Vue({
        el: "#datalist",
        data: {
          data:'',
          list:'',
          table:'',  
          qwerqwre: "-1",
          total:'',
          monDate:'',
          len:'',
          currentSort:'status',
          currentSortDir:'asc'
        },
        updated: function () {
            head.Util.aDialog();
            var datas=[]
            for(key in this.data.items){
    			for(data in this.data.items[key]){
    				datas.push(this.data.items[key][data])
    			}
    		}
    		this.len=datas.length
        },
        methods: {
        	add(obj){
        		var num=0
        		for(key in obj){
        			num=num+parseInt(obj[key].nodiscount_amount)
        		}
        		return num
        	},
        	totals(obj){
        		var num=0
        		for(key in obj){
        			num=num+parseInt(obj[key].amount)
        		}
        		return num
        	},
        	last(obj){
        		var num=0
        		for(key in obj){
        			num=num+parseInt(obj[key].monthly)
        		}
        		return num
        	},
        	all(e){
                var thisDom = e.currentTarget;
                $(thisDom).addClass('active')
        		var datas=[]
        		for(key in this.data.items){
        			for(data in this.data.items[key]){
        				datas.push(this.data.items[key][data])
        			}
        			
        		}
                 this.table =datas
                 this.qwerqwre = '-1';
        	},
        	addClassFun(id,index,discount,discountname,total) {
        		$('.all').removeClass('active')
                this.total=total
                this.qwerqwre = index;
                var arr=[]
                for(data in this.data.items[id]){
        				arr.push(this.data.items[id][data])
        			}
                this.table =arr
            },
          sort:function(s) {
            if(s === this.currentSort) {
              this.currentSortDir = this.currentSortDir==='asc'?'desc':'asc';
            }
            this.currentSort = s;
          }
        },
        computed: {
          sortedTable:function() {
            return this.table.sort((a,b) => {
              let modifier = 1;
              if(this.currentSortDir === 'desc') modifier = -1;
              if(a[this.currentSort] < b[this.currentSort]) return -1 * modifier;
              if(a[this.currentSort] > b[this.currentSort]) return 1 * modifier;
              return 0;
            });
          }
        }
    })
</script>
<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>
