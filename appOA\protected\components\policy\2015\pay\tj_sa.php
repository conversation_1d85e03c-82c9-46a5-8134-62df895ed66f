<?php
if(!defined("IVY_POLICY"))
throw new CException('DO NOT USE THIS FILE DIRECTLY');

return array( 'policy'=>array(

	//是否老生老办法
	ENABLE_CONSTANT_TUITION => true,
	
	//年保育费
	//ANNUAL_CHILDCARE_AMOUNT => 8000,
	
	//是否开放课外活动账单
	ENABLE_AFTERSCHOOLS => true,
	
	//图书馆工本费
	FEETYPE_LIBCARD => 10,
		
	//学期四个时间点；取前后两端
	TIME_POINTS   => array(201509,201512,201601,201607),
		
	//寒暑假月份，及其每月计费天数
	//HOLIDAY_MONTH => array(201402=>20,201408=>20),
	
	//年付开通哪些缴费类型
	PAYGROUP_ANNUAL => array(
		ITEMS => array(
			FEETYPE_TUITION,
			FEETYPE_LUNCH,
			FEETYPE_SCHOOLBUS,		
		),
		TUITION_CALC_BASE => BY_MONTH,
	),
	
	//学期付开通哪些缴费类型
	PAYGROUP_SEMESTER => array(
		ITEMS => array(
			FEETYPE_TUITION,
			FEETYPE_LUNCH,
			FEETYPE_SCHOOLBUS,		
		),
		TUITION_CALC_BASE => BY_MONTH,
	),
	
	//月份开通哪些缴费类型
	PAYGROUP_MONTH => array(
		ITEMS => array(
			FEETYPE_TUITION,
			FEETYPE_LUNCH,
			FEETYPE_SCHOOLBUS,		
		),
		TUITION_CALC_BASE => BY_MONTH,
	),
	
	//计算基准：BY_MONTH, 或者 BY_SETTING, 前者表示所有的学费都基于月费用计算，后者表示所有的学费都基于学期或学年
	TUITION_CALCULATION_BASE => array(
	
		BY_MONTH => array(
			
			//月收费金额
			AMOUNT => array(
				FULL5D => 5040,
				PROGRAM_MIK => 3150,
                OTHER_PROGRAM_CHILDCARE => 3000,
			),
			
			//收费月份，及其权重
			MONTH_FACTOR => array(
				201509=>1,
				201510=>1,
				201511=>1,
				201512=>1,
				201601=>1,
				201602=>.6,
				201603=>1,
				201604=>1,
				201605=>1,
				201606=>1,
				201607=>1
			),
			
			//折扣
			GLOBAL_DISCOUNT => array(
				DISCOUNT_ANNUAL => '0.95:10:2015-12-01',
                DISCOUNT_SEMESTER1 => '0.97:1:2015-11-02',
                DISCOUNT_SEMESTER2 => '0.97:1:2016-06-02'
			)
		),

        BY_ANNUAL => array(
            AMOUNT => array(
                FULL5D => 50750,
            ),
        ),

        BY_SEMESTER1 => array(
            AMOUNT => array(
                FULL5D => 19560,
            ),
        ),

        BY_SEMESTER2 => array(
            AMOUNT => array(
                FULL5D => 32270,
            ),
        ),
	),
	
	//每餐费用
	LUNCH_PERDAY => 28,
	
	//账单到期日和账单开始日之差
	DUEDAYS_OFFSET => 1,

    //代金卷金额
//    VOUCHER_AMOUNT => 500,

));

?>