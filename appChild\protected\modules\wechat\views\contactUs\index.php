<style>
    #failInfo {
        text-indent: 10px;
        color: red;
    }

    #showChildern {
        border-bottom: none !important;
    }

    .weui_label {
        word-break: normal;
    }
    .menu a{
       color: #3070FF
    }
</style>
<div id="contactForm">
    <div class="weui_cells_title"><?php echo Yii::t('navigations', 'Contact Campus') ?> <a href="javascript:"
                                                                                           class="leaveListBtn"
                                                                                           style="color: #586C94;float: right"><i
                class="weui_icon_info_circle"></i></a></div>
    <div class="weui_cells">
        <?php
        //选择孩子
        $this->widget('ext.wechat.ChildSelector', array(
            'childObj' => $this->childObj,
            'myChildObjs' => $this->myChildObjs,
            'jumpUrl' => 'index',
        ));
        ?>
        <div class="weui_cell weui_cell_select">
            <div class="weui_cell_bd weui_cell_primary">
                <select class="weui_select" id="typeSelect" name="leaveType" onchange="select(this)">
                    <option selected value=""><?php echo Yii::t('wechat', 'Please select a category') ?></option>
                    <?php if ($this->childObj->schoolid != 'BJ_IASLT'): ?>
                    <option value="1"><?php echo Yii::t('wechat', 'General Contact') ?></option>
                    <?php endif;?>
                    <option value="2"><?php echo Yii::t('wechat', 'On Leave') ?></option>
                    <option value="3"><?php echo Yii::t('wechat', 'Late Arrival') ?></option>
                    <option value="4"><?php echo Yii::t('wechat', 'Early Departure') ?></option>
                    <option value="5"><?php echo Yii::t('wechat', 'Change Pick Up Method') ?></option>
                </select>
            </div>
        </div>

        <div class="weui_cell takeBusText" style="display: none">
            <div class="weui_cell_bd weui_cell_primary" style="font-size: 14px;color: #888;">
                <p><?php echo Yii::t('wechat', 'This function applies to bus riders only. Change can only be applied to one day.') ?></p>
            </div>
        </div>
    </div>
    <div class="weui_cells weui_cells_form" id="selectDateForm" style="display: none">
        <div class="weui_cell">
            <div class="weui_cell_hd"><label for=""
                                             class="weui_label selectDateTextUp disabled"><?php echo Yii::t('wechat', 'Date') ?></label>
            </div>
            <div class="weui_cell_bd weui_cell_primary">
                <input class="weui_input selectDate selectDateUp disabled" name="startDate"
                       type="date" value="">
            </div>
        </div>
        <div class="weui_cell estimated">
            <div class="weui_cell_hd"><label for="" class="weui_label"><?php echo Yii::t('wechat', 'Expected Arrival Time') ?></label>
            </div>
            <div class="weui_cell_bd weui_cell_primary">
                <input class="weui_input estimatedTime disabled" type="time" value="09:00" name="estBeginTime">
            </div>
        </div>
        <div class="weui_cell second" style="display: none">
            <div class="weui_cell_hd"><label for=""
                                             class="weui_label selectDateTextDown disabled"><?php echo Yii::t('navigations', 'End Date') ?></label>
            </div>
            <div class="weui_cell_bd weui_cell_primary">
                <input class="weui_input selectDate selectDateDown disabled" name="endDate"
                       type="date" value="">
            </div>
        </div>
    </div>
    <div class="weui_cells weui_cells_radio" id="radio" style="display: none">
        <label class="weui_cell weui_check_label" for="x12">
            <div class="weui_cell_bd weui_cell_primary">
                <p><?php echo Yii::t('navigations', 'Personal Leave') ?></p>
            </div>
            <div class="weui_cell_ft">
                <input type="radio" name='type' class="weui_check leaveType disabled" value="20"
                       id="x12"
                       checked="checked">
                <span class="weui_icon_checked"></span>
            </div>
        </label>
        <label class="weui_cell weui_check_label" for="x11">
            <div class="weui_cell_bd weui_cell_primary">
                <p><?php echo Yii::t('navigations', 'Sick Leave') ?></p>
            </div>
            <div class="weui_cell_ft">
                <input type="radio" class="weui_check leaveType disabled" name='type' value="10"
                       id="x11">
                <span class="weui_icon_checked"></span>
            </div>
        </label>
    </div>
    <div class="weui_cells weui_cells_form" id="cancelLunch" style="display: none">
        <div class="weui_cell weui_cell_switch">
            <div class="weui_cell_hd weui_cell_primary"><?php echo Yii::t('wechat', 'Cancel Lunch?') ?></div>
            <div class="weui_cell_ft">
                <input class="weui_switch lunch disabled" name='lunchStatus' type="checkbox" checked>
            </div>
        </div>
        <div class="weui_cell">
            <div class="weui_cell_bd weui_cell_primary" style="font-size: 14px;color: #888">
                
                <div  style="padding-bottom: 5px">
                    <p style="padding-bottom: 5px"> <?php echo Yii::t('wechat', ' 1. Please cancel lunch before 9am everyday.') ?></p>
                    <p class="menu"> <?php echo Yii::t("wechat", " 2. Click <a href=':url'>here</a> to cancel lunch without leave request.", array(':url' => $this->createUrl('user/cateringMenu')));?></p>
                </div>
            </div>
        </div>
    </div>
    <div class="weui_cells weui_cells_checkbox takeBusCheckBox" style="display: none">
        <label class="weui_cell weui_check_label" for="s11">
            <div class="weui_cell_hd">
                <input type="checkbox" class="weui_check" name="intoSchool" value="1" id="s11">
                <i class="weui_icon_checked"></i>
            </div>
            <div class="weui_cell_bd weui_cell_primary">
                <p><?php echo Yii::t('wechat', 'Cancel School Bus Pick Up') ?></p>
            </div>
        </label>
        <label class="weui_cell weui_check_label" for="s12">
            <div class="weui_cell_hd">
                <input type="checkbox" name="leaveSchool" value="1" class="weui_check" id="s12">
                <i class="weui_icon_checked"></i>
            </div>
            <div class="weui_cell_bd weui_cell_primary">
                <p><?php echo Yii::t('wechat', 'Leave School Without Taking School Bus') ?></p>
            </div>
        </label>
    </div>
    <div class="weui_cells weui_cells_form" id="textArea" style="display: none">
        <div class="weui_cell">
            <div class="weui_cell_bd weui_cell_primary">
                <textarea class="weui_textarea" id="textAreaContent" maxlength="500" name="contact"
                          placeholder="<?php echo Yii::t('wechat', 'Remark') ?>"
                          rows="3"></textarea>

                <div class="weui_textarea_counter"><span id="textAreaLength">0</span>/500</div>
            </div>
        </div>
    </div>
    <div id="failInfo"></div>
    <div class="weui_btn_area" style="display: none" id="submitOuter">
        <a class="weui_btn weui_btn_primary" style="width: 100%" href="javascript:"
           id="submit"><?php echo Yii::t('reg', 'Submit') ?></a>
    </div>
    <div id="loadingToast" class="weui_loading_toast" style="display: none;">
        <div class="weui_mask_transparent"></div>
        <div class="weui_toast">
            <div class="weui_loading">
                <div class="weui_loading_leaf weui_loading_leaf_0"></div>
                <div class="weui_loading_leaf weui_loading_leaf_1"></div>
                <div class="weui_loading_leaf weui_loading_leaf_2"></div>
                <div class="weui_loading_leaf weui_loading_leaf_3"></div>
                <div class="weui_loading_leaf weui_loading_leaf_4"></div>
                <div class="weui_loading_leaf weui_loading_leaf_5"></div>
                <div class="weui_loading_leaf weui_loading_leaf_6"></div>
                <div class="weui_loading_leaf weui_loading_leaf_7"></div>
                <div class="weui_loading_leaf weui_loading_leaf_8"></div>
                <div class="weui_loading_leaf weui_loading_leaf_9"></div>
                <div class="weui_loading_leaf weui_loading_leaf_10"></div>
                <div class="weui_loading_leaf weui_loading_leaf_11"></div>
            </div>
            <p class="weui_toast_content"><?php echo Yii::t("wechat", "Submission is in process"); ?></p>
        </div>
    </div>
    <div id="toast" style="display: none;">
        <div class="weui_mask_transparent"></div>
        <div class="weui_toast">
            <i class="weui_icon_toast"></i>

            <p class="weui_toast_content"><?php echo Yii::t("wechat", "Submitted Successfully"); ?></p>
        </div>
    </div>

    <div class="weui_mask_transition leaveListBtn" id="mask" style="display: none;"></div>
    <div class="weui_actionsheet" id="actionsheet">
        <div class="weui_actionsheet_menu">
            <div class="weui_actionsheet_cell"><a href="<?php echo $this->createUrl('leaveList'); ?>"
                                                  style="color:#333;"><?php echo Yii::t("wechat", "Leave Record"); ?></a></div>
        </div>
        <div class="weui_actionsheet_action">
            <div class="weui_actionsheet_cell leaveListBtn"><?php echo Yii::t("wechat", "Cancel"); ?></div>
        </div>
    </div>
</div>
<script>
    var selectvalue = '<?php echo Yii::app()->request->getParam('selectvalue', ''); ?>';
    var schoolStatus = <?php echo json_encode($schoolStatus); ?>;
    var nowDate = new Date();
    var year = nowDate.getFullYear();
    var month = nowDate.getMonth() + 1 < 10 ? "0" + (nowDate.getMonth() + 1)
        : nowDate.getMonth() + 1;
    var day = nowDate.getDate() < 10 ? "0" + nowDate.getDate() : nowDate
        .getDate();
    var dateStr = year + "-" + month + "-" + day; //获取当日yyyy-mm-dd格式
    $('#typeSelect').val('');
    if(selectvalue==2){
        var val=$("#typeSelect").val(2);
        select(val)
    }
    function select(obj) {
        if ($(obj).val() == 0) {
            $('#selectDateForm').hide();
            $('#cancelLunch').hide();
            $('#textArea').hide();
            $('#radio').hide();
            $('.second').hide();
            $('.selectDateUp').addClass('disabled');
            $('.selectDateDown').addClass('disabled');
            $('.lunch').addClass('disabled');
            $('.estimatedTime').addClass('disabled');
            $('.estimated').hide();
            $('.leaveType').addClass('disabled');
            $('#submitOuter').hide();
            $('.takeBusText').hide();
            $('#textArea').hide();
            $('.takeBusCheckBox').hide();
            $('.takeBusCheckBox').find('.weui_check').addClass('disabled');
        } else if ($(obj).val() == 1) {
            $('#selectDateForm').hide();
            $('#cancelLunch').hide();
            $('#textArea').show();
            $('#radio').hide();
            $('.second').hide();
            $('.estimatedTime').addClass('disabled');
            $('.estimated').hide();
            $('.selectDateUp').addClass('disabled');
            $('.selectDateDown').addClass('disabled');
            $('.lunch').addClass('disabled');
            $('.leaveType').addClass('disabled');
            $('#submitOuter').show();
            $('.takeBusText').hide();
            $('#textArea').show();
            $('.takeBusCheckBox').hide();
            $('.takeBusCheckBox').find('.weui_check').addClass('disabled');
        } else {
            $('#textArea').show();
            $('#selectDateForm').show();
            $('#submitOuter').show();
            $('.takeBusText').hide();
            $('.takeBusCheckBox').hide();
            $('.takeBusCheckBox').find('.weui_check').addClass('disabled');
            if ($(obj).val() == 2) {
                $('#radio').show();
                $('.selectDateTextUp').text('<?php echo Yii::t("navigations","Start Date") ?>');
                $('.selectDateTextDown').text('<?php echo Yii::t("navigations","End Date") ?>');
                $('.selectDateUp').removeClass('disabled');
                $('.selectDateDown').removeClass('disabled');
                if (schoolStatus == 0) {
                    $('.lunch').removeClass('disabled');
                    $('#cancelLunch').show();
                }
                $('.second').show();
                $('.selectDate').val(dateStr).attr('min', dateStr);
                $('.leaveType').removeClass('disabled');
                $('.estimatedTime').addClass('disabled');
                $('.estimated').hide();
                $('#textArea').show();
            } else {
                $('#radio').hide();
                $('.second').hide();
                $('#cancelLunch').hide();
                $('.selectDateUp').removeClass('disabled');
                $('.selectDateDown').addClass('disabled');
                $('.lunch').addClass('disabled');
                $('.selectDate').val(dateStr).attr('min', dateStr);
                $('.leaveType').addClass('disabled');
                $('.estimatedTime').removeClass('disabled');
                $('.estimated').show();
                $('#textArea').show();
                $('#takeBusTime').hide();
                if ($(obj).val() == 3) {
                    $('.selectDateTextUp').text('<?php echo Yii::t('wechat', 'Date') ?>');
                    $('.estimatedTime').prop('value', '09:00');
                    $('.estimated').find('.weui_label').text('<?php echo Yii::t('wechat', 'Expected Arrival Time') ?>');
                } else if ($(obj).val() == 4) {
                    $('.selectDateTextUp').text('<?php echo Yii::t('wechat', 'Date') ?>');
                    $('.estimatedTime').prop('value', '14:00');
                    $('.estimated').find('.weui_label').text('<?php echo Yii::t('wechat', 'Expected Departure Time') ?>');
                } else if ($(obj).val() == 5) {
                    $('#takeBusTime').show();
                    $('#cancelLunch').hide();
                    $('#radio').hide();
                    $('.second').hide();
                    $('.selectDateTextUp').text('<?php echo Yii::t('wechat', 'Date') ?>');
                    $('.estimatedTime').addClass('disabled');
                    $('.estimated').hide();
                    $('.selectDateDown').addClass('disabled');
                    $('.lunch').addClass('disabled');
                    $('.leaveType').addClass('disabled');
                    $('#submitOuter').show();
                    $('.takeBusCheckBox').show();
                    $('.takeBusCheckBox').find('.weui_check').removeClass('disabled');
                    $('.takeBusText').show();
                }
            }
        }
    };
    var _txt = document.getElementById("textAreaContent");
    var textAreaLength = document.getElementById("textAreaLength");
    _txt.addEventListener("keyup",function(){
        textAreaLength.textContent = _txt.value.length;
    });

    $('.leaveListBtn').on('click', function () {
        if ($('#actionsheet').hasClass('weui_actionsheet_toggle')) {
            $('#actionsheet').removeClass('weui_actionsheet_toggle');
            $('#mask').removeClass('weui_fade_toggle').hide()
        } else {
            $('#actionsheet').addClass('weui_actionsheet_toggle');
            $('#mask').addClass('weui_fade_toggle').show()
        }
    });

    $('#submit').click(function () {
        var _flag = true;
        var submitArr = {};
        $.each($('#contactForm').find('input'), function (i, input) {
            if (!$(input).hasClass('disabled')) {
                if ($(input).attr("type") == 'checkbox') {
                    if ($(input).is(":checked")) {
                        if ($(input).val() == '') {
                            _flag = false;
                            return false;
                        }
                        submitArr[$(input).prop("name")] = $(input).val();
                    }
                } else if ($(input).attr("type") == 'radio') {
                    if ($(input).is(":checked")) {
                        submitArr[$(input).prop("name")] = $(input).val();
                    }
                } else {
                    submitArr[$(input).prop("name")] = $(input).val();
                }
            }
        });
        $.each($('#contactForm').find('select'), function (i, select) {
            if (!$(select).hasClass('disabled')) {
                if ($(select).val() == '') {
                    _flag = false;
                    $('#failInfo').show().text('<?php echo Yii::t('global', 'Please Select') ?>' + $(select).prop('name'));
                    return false;
                }
                submitArr[$(select).prop("name")] = $(select).val()
            }
        });
        if (!_flag) {
            return false;
        }
        var _textArea = $('#contactForm').find('textarea').val();
        if (_textArea == '') {
            _flag = false;
            $('#failInfo').show().text('<?php echo Yii::t('wechat', 'Provide Details') ?>');
            return false;
        }
        submitArr['vacation_reason'] = _textArea;
        if (!_flag) {
            return false;
        }
        if ($('#textAreaContent').val().length > 500) {
            _flag = false;
            $('#failInfo').show().text('<?php echo Yii::t('wechat', 'Characters Exceed Limit') ?>');
            return false;
        }
        if (!_flag) {
            return false;
        }
        if ($('#submit').hasClass('disabled')) {
            return false;
        }
        $('#submit').addClass('disabled');
        $('#failInfo').hide();
        $('#loadingToast').show();
        $.ajax({
            type: "POST",
            url: "<?php echo $this->createUrl('updateContact');?>",
            data: submitArr,
            dataType: 'json',
            success: function (data) {
                if (data.state == 'success') {
                    $('#loadingToast').hide();
                    $('#toast').show();

                    setTimeout(function () {
                        $('#toast').hide();
                        if(data.data == 0 && $('#typeSelect').val() == 2 && schoolStatus != 1){
                            alert('<?php echo Yii::t('wechat', 'Lunch Not Served On Date Chosen. Try Again') ?>')
                        }
                        location.reload();
                    }, 2000);
                } else {
                    $('#failInfo').show().text(data.message);
                    $('#loadingToast').hide();
                    $('#submit').removeClass('disabled')
                }
            }
        });
    });
</script>