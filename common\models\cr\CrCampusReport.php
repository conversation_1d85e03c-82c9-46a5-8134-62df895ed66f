<?php

/**
 * This is the model class for table "ivy_cr_campus_report".
 *
 * The followings are the available columns in table 'ivy_cr_campus_report':
 * @property integer $id
 * @property integer $yid
 * @property string $schoolid
 * @property integer $weeknumber
 * @property integer $monday_timestamp
 * @property integer $classid
 * @property integer $spring_target
 * @property integer $fall_target
 * @property string $capacity
 * @property integer $mon_person
 * @property integer $tue_person
 * @property integer $wed_person
 * @property integer $thu_person
 * @property integer $fri_person
 * @property double $mon_fte_ratio
 * @property double $tue_fte_ratio
 * @property double $wed_fte_ratio
 * @property double $thu_fte_ratio
 * @property double $fri_fte_ratio
 * @property string $full_ratio
 * @property string $payed_person
 * @property integer $uid
 * @property integer $status
 * @property integer $time
 */
class CrCampusReport extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_cr_campus_report';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('yid, schoolid, weeknumber, classid', 'required'),
			array('yid, weeknumber, monday_timestamp, classid, spring_target, fall_target, mon_person, tue_person, wed_person, thu_person, fri_person, uid, status, time', 'numerical', 'integerOnly'=>true),
			array('mon_fte_ratio, tue_fte_ratio, wed_fte_ratio, thu_fte_ratio, fri_fte_ratio', 'numerical'),
			array('schoolid, capacity', 'length', 'max'=>255),
			array('full_ratio, payed_person', 'safe'),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, yid, schoolid, weeknumber, monday_timestamp, classid, spring_target, fall_target, capacity, mon_person, tue_person, wed_person, thu_person, fri_person, mon_fte_ratio, tue_fte_ratio, wed_fte_ratio, thu_fte_ratio, fri_fte_ratio, full_ratio, payed_person, uid, status, time', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'yid' => 'Yid',
			'schoolid' => 'Schoolid',
			'weeknumber' => 'Weeknumber',
			'monday_timestamp' => 'Monday Timestamp',
			'classid' => 'Classid',
			'spring_target' => 'Spring Target',
			'fall_target' => 'Fall Target',
			'capacity' => 'Capacity',
			'mon_person' => 'Mon Person',
			'tue_person' => 'Tue Person',
			'wed_person' => 'Wed Person',
			'thu_person' => 'Thu Person',
			'fri_person' => 'Fri Person',
			'mon_fte_ratio' => 'Mon Fte Ratio',
			'tue_fte_ratio' => 'Tue Fte Ratio',
			'wed_fte_ratio' => 'Wed Fte Ratio',
			'thu_fte_ratio' => 'Thu Fte Ratio',
			'fri_fte_ratio' => 'Fri Fte Ratio',
			'full_ratio' => 'Full Ratio',
			'payed_person' => 'Payed Person',
			'uid' => 'Uid',
			'status' => 'Status',
			'time' => 'Time',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('yid',$this->yid);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('weeknumber',$this->weeknumber);
		$criteria->compare('monday_timestamp',$this->monday_timestamp);
		$criteria->compare('classid',$this->classid);
		$criteria->compare('spring_target',$this->spring_target);
		$criteria->compare('fall_target',$this->fall_target);
		$criteria->compare('capacity',$this->capacity,true);
		$criteria->compare('mon_person',$this->mon_person);
		$criteria->compare('tue_person',$this->tue_person);
		$criteria->compare('wed_person',$this->wed_person);
		$criteria->compare('thu_person',$this->thu_person);
		$criteria->compare('fri_person',$this->fri_person);
		$criteria->compare('mon_fte_ratio',$this->mon_fte_ratio);
		$criteria->compare('tue_fte_ratio',$this->tue_fte_ratio);
		$criteria->compare('wed_fte_ratio',$this->wed_fte_ratio);
		$criteria->compare('thu_fte_ratio',$this->thu_fte_ratio);
		$criteria->compare('fri_fte_ratio',$this->fri_fte_ratio);
		$criteria->compare('full_ratio',$this->full_ratio,true);
		$criteria->compare('payed_person',$this->payed_person,true);
		$criteria->compare('uid',$this->uid);
		$criteria->compare('status',$this->status);
		$criteria->compare('time',$this->time);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return CrCampusReport the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}
