<?php

/**
 * This is the model class for table "ea_child".
 *
 * The followings are the available columns in table 'ea_child':
 * @property integer $id
 * @property integer $family_id
 * @property string $first_name_cn
 * @property string $last_name_cn
 * @property string $first_name_en
 * @property string $middle_name_en
 * @property string $last_name_en
 * @property integer $birthday
 * @property string $child_photo
 * @property integer $gender
 * @property integer $country
 * @property string $identity
 * @property integer $mother_tongue
 * @property string $now_school
 * @property integer $now_grade
 * @property integer $status
 * @property integer $created_by
 * @property integer $created_at
 * @property integer $updated_by
 * @property integer $updated_at
 */
class Child extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ea_child';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('family_id', 'required'),
			array('family_id, birthday, gender, country, mother_tongue, now_grade, status, created_by, created_at, updated_by, updated_at', 'numerical', 'integerOnly'=>true),
			array('first_name_cn, last_name_cn, first_name_en, middle_name_en, last_name_en, child_photo, identity, now_school', 'length', 'max'=>255),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, family_id, first_name_cn, last_name_cn, first_name_en, middle_name_en, last_name_en, birthday, child_photo, gender, country, identity, mother_tongue, now_school, now_grade, status, created_by, created_at, updated_by, updated_at', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'family_id' => 'Family',
			'first_name_cn' => 'First Name Cn',
			'last_name_cn' => 'Last Name Cn',
			'first_name_en' => 'First Name En',
			'middle_name_en' => 'Middle Name En',
			'last_name_en' => 'Last Name En',
			'birthday' => 'Birthday',
			'child_photo' => 'Child Photo',
			'gender' => 'Gender',
			'country' => 'Country',
			'identity' => 'Identity',
			'mother_tongue' => 'Mother Tongue',
			'now_school' => 'Now School',
			'now_grade' => 'Now Grade',
			'status' => 'Status',
			'created_by' => 'Created By',
			'created_at' => 'Created At',
			'updated_by' => 'Updated By',
			'updated_at' => 'Updated At',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('family_id',$this->family_id);
		$criteria->compare('first_name_cn',$this->first_name_cn,true);
		$criteria->compare('last_name_cn',$this->last_name_cn,true);
		$criteria->compare('first_name_en',$this->first_name_en,true);
		$criteria->compare('middle_name_en',$this->middle_name_en,true);
		$criteria->compare('last_name_en',$this->last_name_en,true);
		$criteria->compare('birthday',$this->birthday);
		$criteria->compare('child_photo',$this->child_photo,true);
		$criteria->compare('gender',$this->gender);
		$criteria->compare('country',$this->country);
		$criteria->compare('identity',$this->identity,true);
		$criteria->compare('mother_tongue',$this->mother_tongue);
		$criteria->compare('now_school',$this->now_school,true);
		$criteria->compare('now_grade',$this->now_grade);
		$criteria->compare('status',$this->status);
		$criteria->compare('created_by',$this->created_by);
		$criteria->compare('created_at',$this->created_at);
		$criteria->compare('updated_by',$this->updated_by);
		$criteria->compare('updated_at',$this->updated_at);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->mmxxcxdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return Child the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}
