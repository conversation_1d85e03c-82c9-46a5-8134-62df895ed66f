<?php
return array(
	'OK' => '确定',
	'My Children' => '我的孩子',
	'Birthday: ' => '生日：',
	'Amount: ' => '金额：',
	'Date: ' => '日期：',
	'Account' => '帐号',
	'Password' => '密码',
	'Completed' => '已完成',

	'My Friends' => '我的小伙伴',
	'My Class' => '我的同学',
	'Finance' => '财务信息',
	'Child Info' => '孩子信息',
	'School Life' => '学校生活',
	'Unpaid Bills' => '未付账单',
	'Balance' => '余额信息',
	"Please login with your DaystarOnline account info to check your child's weekly journal, bills, and much more!" => '输入系统帐号登录信息与微信绑定，您可以使用微信查看周报告，账单，孩子信息等功能。',
	"Please login with your IvyOnline account info to check your child's weekly journal, bills, and much more!" => '输入系统帐号登录信息与微信绑定，您可以使用微信查看周报告，账单，孩子信息等功能。',
	'Please enter account name  (email)' => '请输入账号 (邮箱)',
	'Please enter your password' => '请输入密码',
	'Login' => '绑定微信帐号',
	'Login Success' => '帐号已绑定',
	'You can now begin to access DaystarOnline on wechat!' => '帐号已绑定成功，您可以使用微信菜单功能了。',
	'You can now begin to access IvyOnline on wechat!' => '帐号已绑定成功，您可以使用微信菜单功能了。',
	'Logout' => '取消绑定',
	'Are you sure you want to logout?' => '您确定要解除绑定吗？',
	'Logged Out' => '已解除',
	'Login Failed' => '绑定失败',
	'Login Again' => '重新绑定',
	'You have been logged out.' => '连接已失效',
	"We haven't heard from you in a while.  Please close the page and try again." => '由于您长时间未操作，请关闭页面后再试',
	'Close' => '关闭页面',
	'%s photos' => '%s张照片',
	'Menu' => '餐品',
	'Special Menu' => '特殊餐',
	'We do not support wechat payment yet.' => '暂不支持微信支付',
	'Payment Record' => '支付通知',
	'Invoice' => '支付账单',
	'Press the QR code for 2 seconds and Extract QR code to pay' => '长按识别二维码支付',
	'Long-press the QR code to make payment, or scan this on another device, or take a photo of this QR code on another device and scan it with your current device.' => '请长按识别二维码支付订单，如果支付受阻请将此二维码截屏发送到其他设备上再进行扫码支付。',
	'Error in payment process. Please try again.' => '二维码生成失败，请重新发起支付',
	'Continue' => '继续支付',
	'Payment Failed' => '支付失败',
	'Loading...Please Wait' => '正在加载...请稍后',
	'Your child is not in a state of reading.' => '您的孩子不是在读状态。',
	'Daystar Academy' => '启明星学校',
	'New registration is closed, please contact the campus if you have any questions.' => '新生注册已关闭，如有疑问请联系校园。',
	'Switch' => '点击切换',
	'Cards' => '接送卡',

	'Pick-up card is not available. Please contact the campus to get the card. ' => '尚未办理接送卡，请联系校园办理。',
	'Pick-up Card' => '接送卡',
	'Pick-up Card Manage' => '接送卡管理',
	'Add a New Card' => '增加新卡',
	'Persons Authorized to Pick Up' => '授权接送人信息',
	'Parent:' => '家长：',
	'Phone:' => '手机：',
	'Relationship:' => '关系：',
	"Valid:" => "有效：",

	'Parent can not be null' => '家长姓名不能为空',
	'Phone can not be null' => '家长电话不能为空',
	'Wrong phone format' => '家长电话格式错误',
	'Relationship can not be null' => '家长关系不能为空',
	'Avatar can not be null' => '家长头像不能为空',
	'The function is not open' => '功能暂未开放',

	'ID QRcode' => '身份卡二维码',
	'Refresh' => '立即刷新',
	'Expire in' => '到期于',
	'Get ID QRcode' => '获取二维码',
	'No card? click to get ID QRcode for authentication.' => '忘记带卡？点击按钮获取二维码进行身份验证。',

    'Departments' => '商品分类',
    'Purchasing Notice: ' => '购买须知：',
    'The uniform bill must be paid to complete the purchasing procedure.' => '下单后请支付校服费账单才算购买完成。',
    'The school is in charge of uniform distribution when the bill is paid.' => '购买完成后校服由校园统一发放。',
    'Please contact the school when it comes to the uniform return or change.' => '退换货请联系校园。',
    'My orders' => '我的订单',
    'Size chart' => '尺码对照',
    'Size of uniform' => '校服尺寸',
    'Height chart for reference' => '参考身高',

    'Product name' => '商品名称',
    'Purchase' => '购买',
    'Select product' => '请选择商品',
    'Please select a category' => '请选择类型',
    'Buy it now' => '下单',
    'Product Description' => '商品描述',
    'Back' => '返回',
    'This item has been added in your Cart' => '已加入',
    'My Cart' => '我的购物车',
    'Your cart is empty' => '您的购物车是空的',
    'Please select product specifications' => '请选择商品规格',
    'Yes' => '确定',
    'Are you sure you want to delete this item ?' => '确定要删除该商品吗？',
    'Submission is in process' => '提交中',
    'This product is out of stock' => '该规格下暂无商品',
    'Please fill in the correct quantity' => '请填写正确的商品数量',
    'Cancel' => '取消',
    'Quantity' => '数量',
    'No data found!' => '暂无数据！',
    'Tardy' => '晚到',
    'No more content' => '没有更多内容了',
    'Subtotal ({{ allNum }} items) : ¥ {{ allPrice }}' => '{{ allNum }} 件商品，共 ¥{{ allPrice }}',
    'General Contact' => '一般咨询',
    'Late Arrival' => '晚到',
    'Early Departure' => '早退',
    'Change Pick Up Method' => '临时修改接送方式',
    'This function applies to bus riders only. Change can only be applied to one day.' => '本功能仅针对乘坐校车的学生临时改变当天校车接送为自行接送，仅修改当日有效。',
    'Expected Arrival Time' => '预计到达',
    'Expected Departure Time' => '预计离校',
    'Cancel Lunch?' => '是否取消午餐？',
    'Cancel School Bus Pick Up' => '入校不乘坐班车',
    'Leave School Without Taking School Bus' => '离校不乘坐班车',
    'Remark' => '备注',
    'Submitted Successfully' => '提交成功！',
    'Leave Record' => '请假记录',
    'Provide Details' => '请填写内容',
    'Characters Exceed Limit' => '文本过长',
    'Lunch Not Served On Date Chosen. Try Again' => '所选日期没有午餐',
    'Cancel Leave or Late Arrival?' => '是否取消该请假/迟到？',
    'For cancellations of leave or late arrival before 7:00 am, otherwise please contact the campus.' => '取消请假和晚到记录请于当天7点前操作，否则请联系校园取消。',
    'Two hours earlier is needed if you want to cancel the application, otherwise please contact with campus staff.' => '当前时间距离早退时间小于2个小时不可取消，否则请联系校园取消。',
    'Cancellation Notice:' => '取消记录须知：',
    'leave list' => '已请假列表',
    'Date' => '日期',
    'Confirm' => '确定',
    'On Leave' => '请假',
    'Click a song title to play'=>'点击歌曲进行播放',
    'The end'=>'数据加载完毕',
    'Loading data...'=>'正在加载...请稍后',
    'S'=>'六',
    'T'=>'四',
    ' 1. Please cancel lunch before 9am everyday.' => '1. 当日午餐在九点之后不能取消',
	" 2. Click <a href=':url'>here</a> to cancel lunch without leave request."=>"2. 学生出勤但需要取消午餐请点击 <a href=':url'>这里</a>",
	'Registration: '=>'开放时间：',
	'Size:' => '尺寸：',
	'Color:' => '颜色：',
	'Out of stock intent'=>'缺货意向登记',
	'Not Available'=>'无规格',
	'Stock Quantity:'=>'库存数量：',
	'Are you sure to submit "Intent to Buy" registration?'=>'您确定意向登记该商品吗？' ,
	'Out of stock'=>'缺货登记',
	'This product has been registered'=>'已经登记过该商品', 
  'Registration Successful'=>'意向登记成功',
	'%s journals' => '%s篇日志'
);
