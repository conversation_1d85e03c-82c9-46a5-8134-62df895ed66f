/**
 * Created by XINRAN on 14-4-16.
 */

var currentMonthIndex = null;
var maxMonthIndex =0;
var monthIndex = new Array();
var monthListEle;
var monthDataEle;
var currentDataKey;
var yearlyData;

var cycleMain = ['c11','c12'];
var cycleCost = ['s1003','s1004','s1005','s1006','s1010'];
var cycleMonthlyGap = ['s1001', 's1002', 'cost', 's1003', 's1004', 's1005', 's1006', 's1010', 'revenuePerChild','profit', 'profitMargin', 'profitPerChild', 'costPerChild', 'hrCostPerChild','eqCostPerChild','opCostPerChild','rtCostPerChild','otCostPerChild'];
var cycleCursive = ['s1001','s1002', 's1003', 's1004', 's1005', 's1006', 's1010'];
var standardTuitionFee;

function getCampusFinanceData(branchId){
    var progress = 0;
    var step = ( 100 *( 1 / getCampusDataRequest.finance.length) ).toFixed(2);
    var complete = 0;
    var container;
    $('#overall-progress .progress-bar').css('width','0').html('0%');
    $('#overall-progress').modal({keyboard: false,backdrop:'static'});

    for(var _m=0; _m<getCampusDataRequest.finance.length;_m++){
        if(_m == 0 && $('#monthly-finance-data-template').length > 0) {
            progress = parseInt(progress) + parseInt(step);
            complete +=1;
            continue;
        }
        var varName = getCampusDataRequest.finance[_m].varName;
        $.ajax({
            type: "POST",
            url: getCampusDataRequest.finance[_m].url,
            data: getCampusDataRequest.finance[_m].data,
            dataType: 'json',
            async: true,
            success: function(data) {
                progress = parseInt(progress) + parseInt(step);
                complete +=1;
                if(progress > 100) progress = 100;
                if(complete == getCampusDataRequest.finance.length) progress = 100;

                if(data.type == 'append'){
                    $('body').append(data.data);
                }else{
                    eval(data.cmd);
                }

                $('#overall-progress .progress-bar').css('width',progress+'%').html(progress+'%');
                if(progress == 100){
                    $('#overall-progress .hint').html('Preparing Data...');
                    showFinanceData(branchId);
                }
            }
        });
    }
    currentMonthIndex = null;
    setTimeout("js:$('#overall-progress').modal('hide')", 500);
}

function showFinanceData(branchId){
    currentDataKey = branchId + financeSelectedYear[branchId];

    yearlyData = financeData[currentDataKey]['data'];
    standardTuitionFee = financeData[currentDataKey]['extra']['tuition'];

    var _container = $('#container-campus-'+branchId+' .campus-main');
    _container.html('').html($('#monthly-finance-structure-template').html());

    monthListEle = _container.find('.month-list');
    monthDataEle = _container.find('.month-data');
    monthListEle.append($('<ul class="nav nav-pills"></ul>'));
    monthIndex = [];
    maxMonthIndex = 0;
    for(var _mkey in yearlyData){
        yearlyData[_mkey]['updated'] = financeData[currentDataKey]['extra']['timestamp'];
        monthListEle.find('ul.nav').prepend($('<li></li>').html('<a href="javascript:void(0);" onClick="renderData(this)" period="'+_mkey+'" monthindex="'+maxMonthIndex+'">'+_mkey+'</a>'));
        monthIndex.push(_mkey);
        maxMonthIndex++;
    }

    if(_.isNull(currentMonthIndex)){
        var _i = parseInt(monthIndex.length)-1;
        monthListEle.find('a[monthindex|='+_i+']').click();
    }
}

function renderData(obj){
    if(currentMonthIndex == $(obj).attr('monthindex')){
        return true;
    }
    monthDataEle.hide();
    monthListEle.find('li').removeClass('active');
    $(obj).parents('li').addClass('active');

    currentMonthIndex = $(obj).attr('monthindex');
    var period = $(obj).attr('period');
    var _tmpData = prepareMonthlyData(currentMonthIndex, true);
    if(currentMonthIndex>0){
        var _tmpMonthIndex = parseInt(currentMonthIndex) - 1;
        var _tmpDataPast = prepareMonthlyData(_tmpMonthIndex, false);
        _tmpData = setMonthGapData(_tmpData, _tmpDataPast);
    }
    var _finance_template = _.template($('#monthly-finance-data-template').html(), _tmpData);
    monthDataEle.html(_finance_template);
    $('.chart-container').hide();
    monthDataEle.fadeIn(500);

}

function prepareMonthlyData(_monthindex, _addCursive){
    var tmpData = {};
    var _monthperiod = monthIndex[_monthindex];
    $.extend(tmpData, yearlyData[_monthperiod]); //复制一个对象

    for(var m=0; m<cycleMain.length; m++){
        var cost = 0.00;
        for(var n=0; n<cycleCost.length; n++){
            cost += parseFloat(tmpData[cycleMain[m]][cycleCost[n]]['data']);
        }
        tmpData[cycleMain[m]]['cost'] = {data:cost};
        tmpData[cycleMain[m]]['revenuePerChild'] = {data: parseFloat(tmpData[cycleMain[m]]['s1002']['data'])/ parseFloat(tmpData[cycleMain[m]]['s1001']['data'])};

        tmpData[cycleMain[m]]['profit'] = {data: parseFloat(tmpData[cycleMain[m]]['s1002']['data']) - cost};
        tmpData[cycleMain[m]]['profitMargin'] = {data: parseFloat(tmpData[cycleMain[m]]['profit']['data']) / parseFloat(tmpData[cycleMain[m]]['s1002']['data'])};
        tmpData[cycleMain[m]]['profitPerChild'] = {data: parseFloat(tmpData[cycleMain[m]]['profit']['data']) / parseFloat(tmpData[cycleMain[m]]['s1001']['data'])};
        tmpData[cycleMain[m]]['costPerChild'] = {data: parseFloat(tmpData[cycleMain[m]]['cost']['data']) / parseFloat(tmpData[cycleMain[m]]['s1001']['data'])};
        tmpData[cycleMain[m]]['hrCostPerChild'] = {data: parseFloat(tmpData[cycleMain[m]]['s1003']['data']) / parseFloat(tmpData[cycleMain[m]]['s1001']['data'])};
        tmpData[cycleMain[m]]['eqCostPerChild'] = {data: parseFloat(tmpData[cycleMain[m]]['s1004']['data']) / parseFloat(tmpData[cycleMain[m]]['s1001']['data'])};
        tmpData[cycleMain[m]]['opCostPerChild'] = {data: parseFloat(tmpData[cycleMain[m]]['s1005']['data']) / parseFloat(tmpData[cycleMain[m]]['s1001']['data'])};
        tmpData[cycleMain[m]]['rtCostPerChild'] = {data: parseFloat(tmpData[cycleMain[m]]['s1010']['data']) / parseFloat(tmpData[cycleMain[m]]['s1001']['data'])};
        tmpData[cycleMain[m]]['otCostPerChild'] = {data: parseFloat(tmpData[cycleMain[m]]['s1006']['data']) / parseFloat(tmpData[cycleMain[m]]['s1001']['data'])};


        if(_addCursive){ //计算累计数据
            for(var n=0; n<cycleCursive.length;n++){
                var total=0.00;
                for(var i=0; i<=_monthindex; i++){
                    total += parseFloat(yearlyData[monthIndex[i]][cycleMain[m]][cycleCursive[n]]['data']);
                }
                tmpData[cycleMain[m]][cycleCursive[n]]['cursive'] = total;
            }

            var cost2 = 0.00;
            for(var n=0; n<cycleCost.length; n++){
                cost2 += parseFloat(tmpData[cycleMain[m]][cycleCost[n]]['cursive']);
            }
            tmpData[cycleMain[m]]['cost']['cursive'] = cost2;
            tmpData[cycleMain[m]]['profit']['cursive'] = parseFloat(tmpData[cycleMain[m]]['s1002']['cursive']) - cost2;
            tmpData[cycleMain[m]]['profitMargin']['cursive'] = parseFloat(tmpData[cycleMain[m]]['profit']['cursive']) / parseFloat(tmpData[cycleMain[m]]['s1002']['cursive']);
            tmpData[cycleMain[m]]['profitPerChild']['cursive'] = parseFloat(tmpData[cycleMain[m]]['profit']['cursive']) / parseFloat(tmpData[cycleMain[m]]['s1001']['cursive']);
            tmpData[cycleMain[m]]['costPerChild']['cursive'] = parseFloat(tmpData[cycleMain[m]]['cost']['cursive']) / parseFloat(tmpData[cycleMain[m]]['s1001']['cursive']);
            tmpData[cycleMain[m]]['revenuePerChild']['cursive'] = parseFloat(tmpData[cycleMain[m]]['s1002']['cursive']) / parseFloat(tmpData[cycleMain[m]]['s1001']['cursive']);
            tmpData[cycleMain[m]]['hrCostPerChild']['cursive'] = parseFloat(tmpData[cycleMain[m]]['s1003']['cursive']) / parseFloat(tmpData[cycleMain[m]]['s1001']['cursive']);
            tmpData[cycleMain[m]]['eqCostPerChild']['cursive'] = parseFloat(tmpData[cycleMain[m]]['s1004']['cursive']) / parseFloat(tmpData[cycleMain[m]]['s1001']['cursive']);
            tmpData[cycleMain[m]]['opCostPerChild']['cursive'] = parseFloat(tmpData[cycleMain[m]]['s1005']['cursive']) / parseFloat(tmpData[cycleMain[m]]['s1001']['cursive']);
            tmpData[cycleMain[m]]['rtCostPerChild']['cursive'] = parseFloat(tmpData[cycleMain[m]]['s1010']['cursive']) / parseFloat(tmpData[cycleMain[m]]['s1001']['cursive']);
            tmpData[cycleMain[m]]['otCostPerChild']['cursive'] = parseFloat(tmpData[cycleMain[m]]['s1006']['cursive']) / parseFloat(tmpData[cycleMain[m]]['s1001']['cursive']);
        }
    }
    return tmpData;
}

function setMonthGapData(_currentMonthData, _pastMonthData){
    for(var m=0; m<cycleMain.length; m++){
        for(var n=0; n<cycleMonthlyGap.length; n++){
            _currentMonthData[cycleMain[m]][cycleMonthlyGap[n]]['gap'] = 100 * (( parseFloat(_currentMonthData[cycleMain[m]][cycleMonthlyGap[n]]['data']) - parseFloat(_pastMonthData[cycleMain[m]][cycleMonthlyGap[n]]['data'])) / Math.abs(parseFloat(_pastMonthData[cycleMain[m]][cycleMonthlyGap[n]]['data'])));
        }
    }
    return _currentMonthData;
}

function popChart(dataKey,obj){
    head.load(baseThemeUrl + "/js/highcharts.js", function() {
        $('span[popover|=1]').not(obj).popover('hide');
        $('#simple-chart-container').html('eeeeee');
        if($(obj).attr('popovered')!=1){
            $(obj).popover({trigger:'hover',placement:'right',html:true,content:function(){return drawSimpleCharts();}});
            $(obj).popover('show');
            $(obj).attr('popovered', 1);
        }
    });
}

function drawChart(obj){
    $(obj).parent().siblings().removeClass('active');
    $(obj).parent().addClass('active');
    var type = $(obj).attr("dataType");
    head.load(baseThemeUrl + "/js/highcharts.js", function() {
        if(type == 'enroll'){
            var _chartOpts = {
                title: {
                    text:  branchObjs[currentBranchId]['title'] + ' Enrollments and Targets'
                },
                subtitle: {
                    text: financeSelectedYear[currentBranchId]+ '-' + (parseInt(financeSelectedYear[currentBranchId]) + 1)
                },
                xAxis: {
                    categories: monthIndex
                },
                yAxis: {
                    min: 0,
                    title: {
                        text: 'Children'
                    }
                },
                tooltip: {
                    headerFormat: '<h3>{point.key}</h3><table>',
                    pointFormat: '<tr><td style="color:{series.color};padding:0">{series.name}: </td>' +
                        '<td style="padding:0"><b>{point.y}</b></td></tr>',
                    footerFormat: '</table>',
                    shared: true,
                    useHTML: true
                },
                plotOptions: {
                    column: {
                        pointPadding: .2,
                        borderWidth: 1
                    }
                },
                series: [{
                    type: 'spline',
                    name: 'Target',
                    data: []
                },{
                    type: 'column',
                    name: 'Enrollment',
                    data: []
                }]
            };

            for(var _i=0; _i<monthIndex.length;_i++){
                _chartOpts['series'][0]['data'][_i] = parseInt(yearlyData[monthIndex[_i]]['c12']['s1001']['data']);
                _chartOpts['series'][1]['data'][_i] = parseInt(yearlyData[monthIndex[_i]]['c11']['s1001']['data']);
            }

        }else if(type == 'finance'){
            var _chartOpts = {
                title: {
                    text:  branchObjs[currentBranchId]['title'] + ' Finance Data'
                },
                subtitle: {
                    text: financeSelectedYear[currentBranchId]+ '-' + (parseInt(financeSelectedYear[currentBranchId]) + 1)
                },
                xAxis: {
                    categories: monthIndex
                },
                yAxis: {
                    title: {
                        text: 'Amount (RMB)'
                    }
                },
                series: [{
                    type: 'column',
                    name: 'Revenu',
                    data: []
                }, {
                    type: 'column',
                    name: 'Cost',
                    data: []
                }, {
                    type: 'column',
                    name: 'Profit',
                    data: []
                }, {
                    type: 'spline',
                    name: 'Target Revenu',
                    data: [],
                    marker: {
                        lineWidth: 2,
                        lineColor: Highcharts.getOptions().colors[3],
                        fillColor: 'white'
                    }
                }, {
                    type: 'spline',
                    name: 'Target Cost',
                    data: [],
                    marker: {
                        lineWidth: 2,
                        lineColor: Highcharts.getOptions().colors[4],
                        fillColor: 'white'
                    }
                }, {
                    type: 'spline',
                    name: 'Target Profit',
                    data: [],
                    marker: {
                        lineWidth: 2,
                        lineColor: Highcharts.getOptions().colors[5],
                        fillColor: 'white'
                    }
                }]
            };
        
            for(var _i=0; _i<monthIndex.length;_i++){
                _chartOpts['series'][0]['data'][_i] = parseFloat(yearlyData[monthIndex[_i]]['c11']['s1002']['data']);
                _chartOpts['series'][1]['data'][_i] = 0.00;
                for(var _n=0; _n<cycleCost.length; _n++){
                    _chartOpts['series'][1]['data'][_i] += parseFloat(yearlyData[monthIndex[_i]]['c11'][cycleCost[_n]]['data']);
                }
                _chartOpts['series'][2]['data'][_i] = _chartOpts['series'][0]['data'][_i] - _chartOpts['series'][1]['data'][_i];

                var _tmp3 = parseFloat(yearlyData[monthIndex[_i]]['c12']['s1002']['data']);
                var _tmp4 = 0.00;
                for(var _n=0; _n<cycleCost.length; _n++){
                    _tmp4 += parseFloat(yearlyData[monthIndex[_i]]['c12'][cycleCost[_n]]['data']);
                }
                var _tmp5 = _tmp3 - _tmp4;

                _chartOpts['series'][3]['data'][_i] = [ -0.2 + parseFloat(_i) , _tmp3]; //[ -0.2 + parseFloat(_i) , _tmp3];
                _chartOpts['series'][4]['data'][_i] = [ 0.0 + parseFloat(_i) ,_tmp4];//[ parseFloat(_i) , _tmp4];
                _chartOpts['series'][5]['data'][_i] = [ 0.2 + parseFloat(_i) ,_tmp5];//[ 0.2 + parseFloat(_i) , _tmp5];

            }

    }else if(type == 'cost'){
            var _chartOpts = {
                chart: {
                    type: 'column'
                },
                title: {
                    text:  branchObjs[currentBranchId]['title'] + ' Cost Percentages'
                },
                subtitle: {
                    text: financeSelectedYear[currentBranchId]+ '-' + (parseInt(financeSelectedYear[currentBranchId]) + 1)
                },
                xAxis: {
                    categories: monthIndex
                },
                yAxis: {
                    title: {
                        text: 'Percentge'
                    }
                },
                plotOptions: {
                    column: {
                        stacking: 'percent'
                    }
                },
                tooltip: {
                    headerFormat: '<h3>{point.key}</h3><table>',
                    pointFormat: '<span style="color:{series.color}">{series.name}</span>: <b>{point.y}</b> ({point.percentage:.0f}%)<br/>',
                    footerFormat: '</table>',
                    shared: true
                },
                series: [{
                    name: '人事费用',
                    data: []
                }, {
                    name: '设备费用',
                    data: []
                }, {
                    name: '日常运作',
                    data: []
                }, {
                    name: '房租',
                    data: []
                }, {
                    name: '其他',
                    data: []
                }]
            };

            for(var _i=0; _i<monthIndex.length;_i++){
                _chartOpts['series'][0]['data'][_i] = parseFloat(yearlyData[monthIndex[_i]]['c11']['s1003']['data']);
                _chartOpts['series'][1]['data'][_i] = parseFloat(yearlyData[monthIndex[_i]]['c11']['s1004']['data']);
                _chartOpts['series'][2]['data'][_i] = parseFloat(yearlyData[monthIndex[_i]]['c11']['s1005']['data']);
                _chartOpts['series'][3]['data'][_i] = parseFloat(yearlyData[monthIndex[_i]]['c11']['s1010']['data']);
                _chartOpts['series'][4]['data'][_i] = parseFloat(yearlyData[monthIndex[_i]]['c11']['s1006']['data']);
            }

    }

    var _chartContainer = $('#container-campus-'+currentBranchId+' .chart-container');

        _chartContainer.show();
        $("html, body").animate({ scrollTop: $(document).height() },10);
        _chartContainer.highcharts(_chartOpts);
        if(type == 'finance'){
            var _chart = _chartContainer.highcharts();
            _chart.series[3].hide();
            _chart.series[4].hide();
        }
    });

}