<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('//mcampus/default/index')) ?></li>
        <li><?php echo CHtml::link(Yii::t('site', 'Routines'), array('//mcampus/default/index')) ?></li>
        <li class="active"><?php echo Yii::t('site', '课后课管理') ?></li>
        <li class="active"><?php echo Yii::t('site', '退费列表') ?></li>
    </ol>

    <div class="row">
        <div class="col-md-2 col-sm-2 mb10">
            <?php
            $this->widget('zii.widgets.CMenu', array(
                'items' => $this->module->getMenu(),
                'id' => 'pageCategory',
                'htmlOptions' => array('class' => 'nav nav-pills nav-stacked background-gray'),
                'activeCssClass' => 'active',
            ));
            ?>
        </div>
        <div class="col-md-10 col-sm-12">
            <!--分类菜单-->
            <?php
            $this->widget('zii.widgets.CMenu', array(
                'items' => $this->AttendMenu(),
                'id' => 'AttendMenu',
                'htmlOptions' => array('class' => 'nav nav-tabs'),
                'activeCssClass' => 'active',
                'itemCssClass' => ''
            ));
            ?>

            <div class="col-md-12">

                <?php
                $this->widget('ext.ivyCGridView.BsCGridView', array(
                    'id' => 'refund',
                    'afterAjaxUpdate' => 'js:head.Util.modal',
                    'dataProvider' => $courseDemand,
                    'template' => "{items}{pager}",
                    //状态为无效时标红
                    //'rowCssClassExpression' => '( $data->status == 0 ? "active" : "" )',
                    'colgroups' => array(
                        array(
                            "colwidth" => array(100, 100, 100, 100, 100, 100, 100),
                        )
                    ),
                    'columns' => array(
                        array(
                            'name' => 'childid',
                            'value' => array($this, "getChildName"),
                        ),
                        array(
                            'name' => 'course_title',
                            'value' => '$data->course_title',
                        ),
                        array(
                            'name' => 'teacher_name',
                            'value' => '$data->teacher_name',
                        ),
                        array(
                            'name' => 'teacher_contact',
                            'value' => '$data->teacher_contact',
                        ),
                        array(
                            'name' => 'updated',
                            'value'=>'date("Y-m-d", $data->updated)',
                        ),
                        array(
                            'name' => 'parent_id',
                            'value' => array($this, "getParnetNameDemand"),
                        ),
                        array(
                            'name' => 'parent_id',
                            'value' => array($this, "getButton"),
                        ),
                    ),
                ));
                ?>
            </div>
        </div>
    </div>
</div>
<script>
    var modal = '<div class="modal fade" id="modal" data-backdrop = "boolean" class="1123" tabindex="-1" role="dialog" aria-labelledby="modal"><div class="modal-dialog" role="document"><div class="modal-content"></div></div></div>';
    $('body').append(modal);
    $('.J_modal_s').on('click', function (e) {
        e.preventDefault();
        $('#modal .modal-content').load($(this).attr('href'), function () {
            $('#modal').modal({
                show: true,
            });
        });
    });
</script>
<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>