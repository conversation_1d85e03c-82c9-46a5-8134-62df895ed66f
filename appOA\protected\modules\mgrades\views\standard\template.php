<style>
    .blueColor{
       color:#4D88D2
    }
    .fixedTop{
        padding:15px;  
        box-shadow: 4px 4px 6px 0px rgba(0,0,0,0.1);
        margin-left: -30px;
        margin-right: -15px;
        right: 0;
        z-index: 99;
        background: #fff;
        margin-bottom:24px
    }
    .bg<PERSON>rey{
        padding:20px;
        background: #F7F7F8;
    }
    .isActive {
        background: #EDF3FB;
    }
    .editSubject:hover{
        background: #EDF3FB;
        cursor: pointer;
    }
    .tabLabel{
        background: #F7F7F8;
        border-radius: 2px;
        border: 1px solid #D9D9D9;
        padding:1px 5px;
        font-size:12px;
        color:#333
    }
    .blueBlock{
        width: 6px;
        height: 16px;
        background: #4D88D2;
        border-radius: 1px;
        display:block
    }
    .number{
        width: 24px;
        height: 18px;
        background: #FBEDED;
        border-radius: 9px;
        border: 1px solid #FFFFFF;
        text-align: center;
        line-height: 18px;
        
        font-size: 12px;
    }
    .red{
        color: #D9534F;
    }
    .green{
        color:#5CB85C
    }
    .bgGreen{
        background:#E7F5E7;
    }
    .colorOrg{
        color:#F0AD4E

    }
    .colorc{
        color:#ccc
    }
    .subject:hover{
        background: #F7F7F8;
        color: #4D88D2;
    }
    .subject:hover .subjectText{
        color: #4D88D2 !important;
    }
    .bgGrey{
        border-radius:5px;
        padding:24px 16px;
        background: #FAFAFA;
    }
    .semesterCheck[type="checkbox"]:checked::before {
        content: "✔";
        background-color: #5CB85C !important;
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        border-radius: 2px;
        color: #fff;
        text-align: center;
        height: 13px;
        line-height: 14px;
    }
    table tbody td{
        vertical-align: middle !important;
        padding:24px !important;
        border: none !important;
        border-right: 1px solid #ddd !important;
        border-bottom: 1px solid #ddd !important;
    }
    table tbody {
        display: block;
        overflow-y: scroll;
    }
    table thead, tbody tr {
        display: table;
        width: 100%;
        table-layout: fixed;
    }
    .radio-inline, .checkbox-inline{
        position: relative;
    }
    .scroll-box::-webkit-scrollbar-thumb {
        /*滚动条里面小方块*/
        border-radius   : 10px;
        background-color: #ccc;
        background-image: none
    }
    .scroll-box::-webkit-scrollbar-track {
        /*滚动条里面轨道*/
        box-shadow   : inset 0 0 5px rgba(0, 0, 0, 0);
        background   : transparent;
        border-radius: 10px;
        border:none
    }
    .mt7{
        margin-top:7px
    }
    .text-right{
        text-align:left !important
    }
    .bgList{
        background: #FAFAFA;
        padding:0px 15px;
        margin-bottom:10px
    }
    .tdHover :hover{
        cursor: pointer;
        background: #FAFAFA;
    }
    .fontFamily{
        /* font-family: PingFangSC-Medium, PingFang SC; */
    }
    .el-divider--vertical{
        height: 16px;
        margin-top: -4px;
    }
    label{
        line-height:1.5 !important
    }
    .el-switch__core{
        height:16px;
        width:32px !important;
    }
    .el-switch__core:after{
        width: 14px;
        height: 14px;
        top: 0px
    }
    .el-switch.is-checked .el-switch__core::after{
        margin-left: -14px
    }
    .width200{
        width:200px
    }
    .form-control{
        box-shadow: none;
        border: 1px solid #dcdfe6;
        box-sizing: border-box;
    }
    .form-control::-webkit-input-placeholder { /* Chrome/Opera/Safari */
        color: #ccc;
    }
    .mb0{
        margin-bottom:0px
    }
    .el-input__inner,.form-control{
        height:34px;
        box-shadow:none
    }
    .el-input__prefix, .el-input__suffix{
        top:4px
    }
    .form-control:focus {
        box-shadow: none;
    }
    .loading{
        width:98%
    }
    .maxWidth{
        max-width:270px
    }
    .tdBg{
        background:#FAFAFA
    }
    .m0{
        margin:0
    }
</style>
<div class="container-fluid fontFamily" id='container'>
    <ol class="breadcrumb">
        <li><?php echo Yii::t('site','Campus Workspace');?></li>
        <li><?php echo Yii::t('site','Reports Templates');?></li>
        <li class="active">评估报告模板</li>
    </ol>

    <div class="row " v-if='!isEdit'>
        <div class="col-md-2 col-sm-12">
        <div class="list-group" id="classroom-status-list">
                <a href="<?php echo $this->createUrl('index'); ?>" class="list-group-item status-filter"><?php echo Yii::t("newDS", "标准库管理");?></a>
                <a href="<?php echo $this->createUrl('option'); ?>" class="list-group-item status-filter"><?php echo Yii::t("newDS", "评定标准管理");?></a>
                <a href="<?php echo $this->createUrl('template'); ?>" class="list-group-item status-filter active"><?php echo Yii::t("newDS", "评估报告模板");?></a>
            </div>
        </div>
        <div class="col-md-10 col-sm-12 " v-if='configData.startYearList'>
            <div v-for='(list,index) in configYearList'>
                <div class='flex align-items mb24'>
                    <span class='blueBlock'></span>
                    <span  class='font16 color3 ml10 flex1 fontBold'>{{list.value}}</span>
                </div>
                <div class='row'>
                    <div class='col-md-3' v-for='(grade,gradeId,id) in configData.gradeList'>
                        <div class="panel panel-default mb24">
                            <div class="panel-heading font14 color3 flex">
                                <span class='flex1 fontBold'>{{grade}}</span>
                                <el-tooltip class="item" effect="dark" content="导入标准" placement="top" v-if='list.id>=configData.startYearList.current'>
                                    <span class='glyphicon glyphicon-log-in blueColor' @click='copyTemplate(list.id,gradeId)'></span>
                                </el-tooltip>
                            </div>
                            <div class="panel-body p16">
                                <div class='flex subject p8 cur-p' v-for='(item,sub,i) in configData.programConfig' @click='showEdit(list.id,gradeId,sub)'>
                                    <span class='number green bgGreen' v-if='templateList.list[list.id] && templateList.list[list.id][gradeId] && templateList.list[list.id][gradeId][sub]'>{{templateList.list[list.id][gradeId][sub] }}</span>
                                    
                                    <span class='number red' v-else>0</span>
                                    <span class='font14 color3 ml8 subjectText'>{{item}}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class='' v-if='isEdit'>
        <div class='row'>
            <div class='col-md-12 col-sm-12 mb24'>
                <span class='font16 blueColor cur-p' @click='isEdit=false'>
                    <span class='el-icon-arrow-left'></span>
                    <span class='ml4'>评估报告模版</span>
                </span> 
                <el-divider direction="vertical"></el-divider>
                <span class=' font16 color6'>编辑</span>
            </div>
        </div>
       
        <div class='flex row'>
            <div class='col-md-4 col-sm-4 maxWidth'>
                <div class='bgGrey' >
                    <div class='font14 mb12'><span class='color6'>学年：</span><span class='fontBold'>{{configData.startYearList.list[editYear]}}</span> </div>
                    <div class='font14 mb12'><span class='color6'>班级：</span><span class='fontBold'>{{configData.gradeList[editGrade]}}</span> </div>
                    <div class='overflow-y scroll-box' :style="'max-height:'+(height-350)+'px;overflow-x: hidden;'">
                        <div class='mt10 p10 relative editSubject' :class="{'isActive': key===editSub}"   v-for='(list,key,index) in templateDetail.config.programConfig' @click="handleLeft(index,key)" >
                            <div class='font14 color3' :class="{'fontBold': key===editSub}" >{{list}}</div>
                            <div class='color9 mt4'>已选择
                                <span class='green' v-if='templateList.list[editYear] &&  templateList.list[editYear][editGrade] && templateList.list[editYear][editGrade][key]'>{{templateList.list[editYear][editGrade][key]}}</span>
                                <span class='red' v-else>0</span>
                                 项</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class='col-md-8  col-sm-8 flex1'>
                <div class='loading'  v-if='showData'>
                    <span></span>
                </div>
                <div v-if='templateDetail.categoryList[editSub]'>
                    <div class='flex align-items fixedTop' >
                        <span class='flex1 font16 color3'>
                        <span class='el-icon-s-management blueColor mr5 font18'></span><span class='fontBold'>{{configData.programConfig[editSub]}}</span>
                        </span> 
                        <button type="button" class="btn btn-primary" @click='saveData()'>保存</button>
                    </div>
                    <table class="table table-bordered m0"  >
                        <thead>
                            <tr>
                                <th class='tdBg' style='width:200px'>分类</th>
                                <th class='text-center tdBg'  style='width:100px'><input type="checkbox" v-model='all'  @change='allChecked()'></th>
                                <th>
                                    选择项
                                    <span class='pull-right width200 mr24'>适用学期</span>
                                </th>
                            </tr>
                        </thead>
                        <tbody class='overflow-y scroll-box' :style="'max-height:'+(height-365)+'px;overflow-x: hidden;'">
                            <tr v-for='(list,index) in templateDetail.categoryList[editSub]'>
                                <td style='width:200px' class='tdBg'>
                                    <div class='color3 font14'>{{list.title_cn}}</div>
                                    <div class='color6 font12' v-if='templateDetail.config.optionGroupList[list.option_groupid]'>{{templateDetail.config.optionGroupList[list.option_groupid].title}}</div>
                                </td>
                                <td  class='text-center tdBg' style='width:100px'>
                                    <input type="checkbox"  v-model='list.checkAll'  :disabled='list.disabled' @change='checkAll(list,index)'>
                                </td>
                                <td>
                                    <div class='tdHover' v-for='(item,idx) in list.standard'>
                                        <div class='flex align-items  font14 color3' v-if='item.startyear<=editYear'>
                                            <div class="checkbox flex1" >
                                                <label>
                                                    <input type="checkbox" :value='item.id' v-model='list.check' @change='checked(list,index)' >{{item.title}} <span class="tabLabel color3" >{{item.standard_code}}</span>
                                                </label>
                                            </div>
                                            <div class='ml24 width200'>
                                                <!-- <span>适用学期：</span> -->
                                                <el-switch
                                                    v-model="item.semester1"
                                                    active-color="#428bca"
                                                    active-value=1
                                                    inactive-value=0>
                                                </el-switch>
                                                <span class='color6'>Term 1</span>
                                                <el-switch
                                                    class='ml20'
                                                    v-model="item.semester2"
                                                    active-color="#428bca"
                                                    active-value=1
                                                    inactive-value=0>
                                                </el-switch>
                                                <span class='color6'>Term 2</span>
                                            </div>
                                        </div>
                                        <div class='flex align-items font14 color3' v-else>
                                            <div class="checkbox flex1 colorc" >
                                                <label>
                                                    <input type="checkbox" disabled>
                                                    <div class=''>{{item.title}} <span class="tabLabel colorc" >{{item.standard_code}}</span></div> 
                                                    <div class='font12 colorOrg'> 启用学年：{{configData.startYearList.list[item.startyear]}}</div>
                                                </label>
                                            </div>
                                            <div class='colorc  ml24 width200'>
                                                <!-- <span>适用学期：</span> -->
                                                <el-switch
                                                    disabled
                                                    v-model="item.semester1"
                                                    active-color="#428bca"
                                                    active-value=1
                                                    active-text="Term 1"
                                                    inactive-value=0>
                                                </el-switch>
                                                <span class='color6'>Term 1</span>
                                                <el-switch
                                                    disabled
                                                    class='ml20'
                                                    v-model="item.semester2"
                                                    active-color="#428bca"
                                                    active-value=1
                                                    inactive-value=0>
                                                </el-switch>
                                                <span class='color6'>Term 2</span>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div v-else>
                    <el-empty description="暂无数据"></el-empty>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="copyModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("newDS", "导入标准"); ?></h4>
                </div>
                <div class="modal-body p24" v-if='configData.gradeList'>
                    <div class="alert alert-warning" role="alert">将源学年的标准复制到目标学年</div>
                    <div class="form-horizontal">
                        <div class="form-group">
                            <span class="col-sm-2 control-label text-right font14 color6">年级</span>
                            <div class="col-sm-10">
                            <div class='font14 mt7'>{{configData.gradeList[copyGrade]}}</div>
                            </div>
                        </div>
                        <div class="form-group">
                            <span class="col-sm-2 control-label text-right font14 color6">目标学年</span>
                            <div class="col-sm-10">
                                <div class='font14 mt7'>{{configData.startYearList.list[copyStartyear]}}</div>
                            </div>
                        </div>
                        <div class="form-group">
                            <span class="col-sm-2 control-label text-right font14 color6">选择学校</span>
                            <div class="col-sm-10">
                                <el-select v-model="currentSchool"  placeholder="请选择"  @change='selectSchool()'>
                                    <el-option
                                    v-for='(list,key,index) in schoolList'
                                    :key="key"
                                    :label="list"
                                    :value="key">
                                    </el-option>
                                </el-select>
                                <!-- <select class="form-control select_3" v-model='currentSchool' @change='selectSchool()'>
                                    <option value=''>请选择</option>
                                    <option v-for='(list,key,index) in schoolList' :value='key'>{{list}}</option>
                                </select> -->
                            </div>
                        </div>
                        <div class="form-group" v-if='currentSchool!=""'>
                            <span class="col-sm-2 control-label text-right font14 color6">源数据</span>
                            <div class="col-sm-10">
                                <el-select v-model="newYear"  placeholder="请选择">
                                    <el-option
                                    v-for='(list,key,index) in configData.startYearList.list'
                                    :key="key"
                                    :label="list"
                                    :value="key">
                                    </el-option>
                                </el-select>
                                <!-- <select class="form-control select_3" v-model='newYear'>
                                    <option value=''>请选择</option>
                                    <option v-for='(list,key,index) in configData.startYearList.list' :value='key'>{{list}}</option>
                                </select> -->
                            </div>
                        </div>
                    </div>
                    <div v-if='newYear!=""'>
                        <div class='font14 mt24 fontBold'>选择源学年中的标准，并复制到目标学年</div>
                        <div class="checkbox mt12">
                            <label>
                                <input type="checkbox" v-model='copyCheck' @change='copyChecked'><div class='color6 font14'>全选</div> 
                            </label>
                        </div>
                        <div>
                            <div class='bgList' v-for='(list,key,index) in configData.programConfig'>
                                <div class='flex'  v-if='schoolTemplateList.list[newYear] && schoolTemplateList.list[newYear][copyGrade] && schoolTemplateList.list[newYear][copyGrade][key]'>
                                    <div class="checkbox flex1">
                                        <label>
                                            <input type="checkbox" :value="key" v-model='copyprograms' @change='copyCheckList'>
                                            <div class='color6 font14'>{{list}}</div> 
                                            <div  class='red font12' v-if='templateList.list[copyStartyear] && templateList.list[copyStartyear][copyGrade] && templateList.list[copyStartyear][copyGrade][key]'>目标学年中，该科目已有标准项，仍要导入将覆盖</div>
                                        </label>
                                    </div>
                                    <div class='mt12 color6 font12'  v-if='schoolTemplateList.list[newYear] && schoolTemplateList.list[newYear][copyGrade] && schoolTemplateList.list[newYear][copyGrade][key]'>标准项：{{schoolTemplateList.list[newYear][copyGrade][key] }}</div>
                                    <div class='mt12 color6 font12' v-else>标准项：0</div>

                                </div>
                                <div v-else  class='flex' >
                                    <div class="checkbox flex1">
                                        <label>
                                            <input type="checkbox" disabled>
                                            <div class='colorc font14'>{{list}}</div> 
                                            <!-- <div  class='red font12' v-if='templateList.list[copyStartyear] && templateList.list[copyStartyear][copyGrade] && templateList.list[copyStartyear][copyGrade][key]'>目标学年中，该科目已有标准项，仍要导入将覆盖</div> -->
                                        </label>
                                    </div>
                                    <div class='mt12 colorc font12'>标准项：0</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" @click='copyList()'>确定</button>
                </div>
            </div>
        </div>
    </div>
</div>
<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>
<script>
    var height=document.documentElement.clientHeight;   
    // $('#copyModal').modal('show')

     var container = new Vue({
        el: "#container",
        data: {
            height:height,
            config:{},
            configData:{},
            configYearList:[],
            templateList:{},
            templateDetail:'',
            currentYear:'',
            isEdit:false,
            editYear:'',
            editGrade:'',
            editSub:'',
            all:false,
            newYear:'',
            copyStartyear:'',
            copyGrade:'',
            copyprograms:[],
            copyCheck:false,
            schoolList:{},
            currentSchool:'',
            schoolTemplateList:{},
            showData:false
        },
        watch:{
        },
        created: function() {
            this.getList()
            this.getConfig()
        },
        computed: {},
        methods: {
            getConfig(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("config") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.schoolList=data.data.schoolYearList
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            getList(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("templateList") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.templateList=data.data
                            that.schoolTemplateList=data.data
                            that.currentYear=data.data.config.current
                            that.configData=data.data.config
                            that.configYearList=[]
                            for(var key in data.data.config.startYearList.list){
                                that.configYearList.push({
                                    value:data.data.config.startYearList.list[key],
                                    id:key
                                })
                            }
                            that.configYearList=that.configYearList.reverse()
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            handleLeft(index,sub) {
                this.editSub = sub;
                this.showEdit(this.editYear,this.editGrade,this.editSub)
            },
            showEdit(year,grade,sub){
                if(year<this.currentYear){
                    return
                }
                this.editYear=year
                this.editGrade=grade
                this.editSub=sub
                this.showData=true
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("templateDetail") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        startyear:this.editYear,
                        grade:this.editGrade,
                        program:this.editSub
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.isEdit=true
                            that.templateDetail=data.data                          
                            for(var key in that.templateDetail.categoryList){
                                if( that.templateDetail.categoryList[key]){
                                    that.templateDetail.categoryList[key].forEach(item => {
                                        var len=0
                                        var allLen=[]
                                        
                                        if(data.data.list && data.data.list[item.id]){
                                            len=Object.keys(data.data.list[item.id]).map(Number)
                                            Vue.set(item, 'check',len);
                                            
                                            item.standard.forEach(_item => {
                                                if(data.data.list[item.id][_item.id]){
                                                    Vue.set(_item, 'semester1',data.data.list[item.id][_item.id].semester1+'');
                                                    Vue.set(_item, 'semester2',data.data.list[item.id][_item.id].semester2+'');
                                                }else{
                                                    if(_item.startyear<=that.editYear){
                                                        Vue.set(_item, 'semester1','1');
                                                        Vue.set(_item, 'semester2','1');
                                                    }else{
                                                        Vue.set(_item, 'semester1','0');
                                                        Vue.set(_item, 'semester2','0');
                                                    }
                                                }
                                                if(_item.startyear<=that.editYear){
                                                    allLen.push(_item)
                                                }
                                            });
                                            if(len.length==allLen.length){
                                                Vue.set(item, 'checkAll',true);
                                            }else{
                                                Vue.set(item, 'checkAll',false);
                                            }
                                        }else{
                                            Vue.set(item, 'check',[]);
                                            Vue.set(item, 'checkAll',false);
                                            item.standard.forEach(_item => {
                                                if(_item.startyear<=that.editYear){
                                                    allLen.push(_item)
                                                    Vue.set(_item, 'semester2','1');
                                                    Vue.set(_item, 'semester1','1');
                                                }else{
                                                    Vue.set(_item, 'semester2','0');
                                                    Vue.set(_item, 'semester1','0');
                                                }
                                            });
                                            if(allLen.length==0){
                                                Vue.set(item, 'disabled',true);   
                                            }
                                        }
                                    });
                                }
                            }
                            that.allData()
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                        that.showData=false
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                        that.showData=false
                    },
                })
            },
            allData(){
                var disabled=[]
                var checked=[]
                if(this.templateDetail.categoryList[this.editSub]){                
                    this.templateDetail.categoryList[this.editSub].forEach(item => {
                        if(item.disabled){
                            disabled.push(item)
                        }else{
                            if(item.checkAll){
                                checked.push(item.checkAll)
                            }
                        }
                    })
                    if(this.templateDetail.categoryList[this.editSub].length-disabled.length==checked.length){
                        this.all=true
                    }else{
                        this.all=false

                    }
                }
            },
            allChecked(){
                if(this.all){
                    this.templateDetail.categoryList[this.editSub].forEach(item => {  
                        Vue.set(item, 'checkAll',true);
                        var allLen=[]
                        item.standard.forEach(_item => {
                            if(_item.startyear<=this.editYear){
                                allLen.push(_item.id)
                            }
                        });
                        Vue.set(item, 'check',allLen);
                    })
                }else{
                    this.templateDetail.categoryList[this.editSub].forEach(item => {  
                        Vue.set(item, 'checkAll',false);
                        Vue.set(item, 'check',[]);
                    })
                }
            },
            checkAll(item,index){
                var check=[]
                if(item.checkAll){
                    item.standard.forEach(_item => {
                        if(_item.startyear<=this.editYear){
                            check.push(_item.id)
                        }
                    });
                }
                Vue.set(this.templateDetail.categoryList[this.editSub][index], 'check',check);
                this.allData()
            },
            checked(item,index){
                var check=[]
                item.standard.forEach(_item => {
                    if(_item.startyear<=this.editYear){
                        check.push(_item)
                    }
                });
                if(check.length==item.check.length){
                    Vue.set(this.templateDetail.categoryList[this.editSub][index], 'checkAll',true);
                }else{
                    Vue.set(this.templateDetail.categoryList[this.editSub][index], 'checkAll',false);
                }
                this.allData()
            },
            saveData(){
                let that=this
                var categoryList={}
                this.templateDetail.categoryList[this.editSub].forEach(item => {
                    item.standard.forEach(_item => {
                        if(item.check.indexOf(_item.id)!=-1){
                            categoryList[_item.id]={
                                semester1:_item.semester1,
                                semester2:_item.semester2
                            }
                        }
                    });
                });
                // if(Object.values(categoryList).length==0){
                //     resultTip({
                //         error: 'warning',
                //         msg:'请选择'
                //     });
                //     return
                // }
                $.ajax({
                    url: '<?php echo $this->createUrl("templateUpdate") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        startyear:this.editYear,
                        grade:this.editGrade,
                        program:this.editSub,
                        updateList:categoryList,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.getList()
                            resultTip({
                                msg: data.message
                            });
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            copyTemplate(id,gradeId){
                this.copyStartyear=id
                this.copyGrade=gradeId
                this.newYear=''
                this.currentSchool='<?php echo $this->branchId; ?>'
                this.copyprograms=[]
                this.copyCheck=false
                $('#copyModal').modal('show')
            },
            copyChecked(){
                this.copyprograms=[]
                if(this.copyCheck){
                    for(var key in this.configData.programConfig){
                        if(this.schoolTemplateList.list[this.newYear] && this.schoolTemplateList.list[this.newYear][this.copyGrade] && this.schoolTemplateList.list[this.newYear][this.copyGrade][key]){
                            this.copyprograms.push(key)
                        }
                    }
                }
            },
            copyCheckList(){
                var check=[]
                for(var key in this.configData.programConfig){
                    if(this.schoolTemplateList.list[this.newYear] && this.schoolTemplateList.list[this.newYear][this.copyGrade] && this.schoolTemplateList.list[this.newYear][this.copyGrade][key]){
                        check.push(key)
                    }
                }
                if(check.length==this.copyprograms.length){
                    this.copyCheck=true
                }else{
                    this.copyCheck=false
                }
            },
            copyList(){
                let that=this
                if(this.copyprograms.length==0){
                    return
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("templateCopy")?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        startyear:this.copyStartyear,
                        grade:this.copyGrade,
                        copyyear:this.newYear,
                        programs:this.copyprograms,
                        copySchoolId:this.currentSchool
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                          resultTip({
                                msg: data.message
                            });
                          that.getList()
                          $('#copyModal').modal('hide')
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            selectSchool(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("templateList") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        branchId: this.currentSchool
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                          that.schoolTemplateList=data.data
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            }
        }
    })
</script>