
.bankinfo {
    /*
     *
     border-bottom: 1px solid #CCCCCC;
     border-left: 1px solid #CCCCCC;
     border-right: 1px solid #CCCCCC;
     width: 650px;
    */
    padding-top: 5px;
    padding-bottom: 5px;
}
.bankinfo .ui-list-icons input {
    vertical-align: middle;
}
.bankinfo img {
    vertical-align: middle;
}
.bankinfo .ui-list-icons li {
    float: left;
    margin: 3px;
    position: relative;
    width: 182px;
    height: 33px;
}
.bankinfo .icon-box {
    background-color: #EEEEEE;
    border: 1px solid #DDDDDD;
    display: inline-block;
    position: relative;
    vertical-align: middle;
    width: 154px;
}
.bankinfo .current {
    border-color: #FF6600 !important;
}
.bankinfo li .shuhu {
    position: absolute;
    display: block;
    width: 155px;
    height: 34px;
    top: 0;
    left: 0;
    filter: alpha(opacity = 0);
    background-color: #fff;
}
.bankinfo li span {
    margin: 20px;
    width: 155px;
}
.bankinfo h3{
    margin: 0.8em 0 0.5em;
}
.bank-list:before{
    clear:both;
}
.bank-list ul li.bank{
    float: left;
    width: 200px;
    margin-bottom: 2px;
    position: relative;
    height: 38px;
}
li.bank input{
    position: absolute;
    left: 0;
    top: 6px;
}
li.bank label{
    position: absolute;
    left: 22px;
    top: 0;
    box-shadow: 2px 2px 3px #666;
}
span.faq{
    display: inline-block;
    height:64px;
    margin: 0 0 10px 10px;
}
span.faq em{
    display: inline-block;
    width:64px;
    height:64px;
    float: left;
    background: url(../images/icons/faq_bg.png) no-repeat left -64px transparent;
    padding-right: 10px;
}
a span.faq:hover em{
    background-position: left top;
}

span.faq span{
    line-height: 64px;
    font-size:18px;
}
a span.faq span:hover{
    color:#FF6200;
    text-decoration: underline;
}
.operateCol_div{
    margin: 1em 0;
}

.onlinenotice{
    padding: 20px;
}
.onlinenotice .partners{
    border-bottom: 2px solid #078D00;
    margin-bottom: 1em;
    text-align: left;
}
.onlinenotice .banklist li{
    display: inline;
    margin: 2px;
}

.bank label{
    background-image: url("../../base/images/bankLogo4Alipay/bankicon.png");
    display:inline-block;
    height: 30px;
    width:120px;
    background-repeat: no-repeat;
    border: 1px solid #E2E2E2;
}
.ICBCB2C{
    background-position: 0 -40px;
}
.CMB {
    background-position: 0 -80px;
}
.CCB{
    background-position: 0 -320px;
}
.BOCB2C{
    background-position: 0 -520px;
}
.ABC{
    background-position: 0 -480px;
}
.COMM{
    background-position: 0 -600px;
}
.POSTGC {
    background-position: 0 -400px;
}
.CEBBANK {
    background-position: 0 -440px;
}
.SPDB {
    background-position: 0 -360px;
}
.GDB {
    background-position: 0 -280px;
}
.CITIC {
    background-position: 0 -200px;
}
.CIB {
    background-position: 0 -3365px;
}
.SDB {
    background-position: 0 -240px;
}
.CMBC {
    background-position: 0 -120px;
}
.HZCBB2C {
    background-position: 0 -760px;
}
.SHBANK {
    background-position: 0 -840px;
}
.BJRCB {
    background-position: 0 -2640px;
}
.SPABANK {
    background-position: 0 -1880px;
}
.FDB {
    background-position: 0 -1320px;
}
.NBBANK {
    background-position: 0 -1240px;
}
.BJBANK {
    background-position: 0 -3240px;
}
.WZCB {
    background-position: 0 -1720px;
}
.abc1003{
    background-position: 0 -716px;
}
.abc1004{
    background-position: 0 -3080px;
}
label.ICBCBTB{
    background-image: url('../../base/images/bankLogo4Alipay/ENV_ICBC_OUT.gif');
}
label.CCBBTB{
    background-image: url('../../base/images/bankLogo4Alipay/ENV_CCB_OUT.gif');
}
label.ABCBTB{
    background-image: url('../../base/images/bankLogo4Alipay/ENV_ABC_OUT.gif');
}
label.SPDBB2B{
    background-image: url('../../base/images/bankLogo4Alipay/ENV_SPDB_OUT.gif');
}
.bank-quota{
    color:#666;
    background:#f2f2f2;
    padding: 10px;
}
.bank-quota table th, .bank-quota table td{
    border-bottom: 1px solid #dedede;
    border-right: 1px solid #dedede;
    line-height: 1.8em !important;
    padding: 2px 4px;
}
.bank-quota table th{
    background:#efefef;
}
.bank-quota tr.head th{
    border-top: 1px solid #dedede;
}
.bank-quota .orange{
    color: #FF6200;
}