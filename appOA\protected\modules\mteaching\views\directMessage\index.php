<style>
    .avatar{
        width: 54px;
        height: 54px;
        border-radius: 8px;
        object-fit:cover;
    }
    .postObject{
        width: 24px;
        height: 24px;
        border-radius: 50%;
        object-fit:cover;
    }
    .font18{
        font-size:18px
    }
    .lineHeight20{
        line-height:20px
    }
    .border{
        border-radius:8px;
        border: 1px solid #EBEDF0;
    }
    .borderBto{
        border-bottom:1px solid #EBEDF0;
    }
    .more{
        width: 24px;
        height: 24px;
        color: #666;
        font-size: 12px;
        background: #EBEDF0;
        border-radius: 50%;
        display: inline-block;
        text-align: center;
        vertical-align: middle;
        line-height: 24px;
    }
    .title:hover{
        color:#4D88D2 !important;
        cursor:pointer;
        text-decoration: none;
    }
    .menuRight{
        right:0;
        left:auto;
        min-width:100px
    }
    .new {
        left:0 !important;
        right:auto !important;
        min-width:100px
    }
    .keep{
        background: #FCF8E3;
        border-radius: 4px;
    }
    .otherTeacher{
        background: #F2DEDE;
        border-radius: 2px;
        font-size: 12px;
        font-weight: 400;
        color: #D9534F;
        padding:0px 8px;
        height:36px;
        line-height:36px;
    }
    .postStatus{
        border-radius: 0px 100px 100px 0px;
        color: #FFFFFF;
        position: absolute;
        left: 0;
        top: 0;
        padding: 0 12px;
        display: inline-block;
        line-height: 20px;
        font-size: 12px;
    }
    .green{
        background:#5CB85C
    }
    .yellow{
        background:#F0AD4E
    }
    .grey{
        background:#999999
    }
    .blue{
        background:#5BC0DE
    }
    .contentAvatar{
        width: 44px;
        height: 44px;
        border-radius: 50%;
        object-fit:cover;
    }
    .contentPostObject{
        width: 36px;
        height: 36px;
        border-radius: 50%;
        object-fit:cover;
    }
    .postMore{
        font-size: 14px;
        display: inline-block;
        height: 36px;
        line-height: 36px;
        background: #4D88D2;
        color: #fff;
        padding: 0 10px;
        border-radius: 18px;
    }
    .iconImg{
        width:25px
    }
    .line20{
        line-height:20px
    }
    .readIcon{
        width:20px
    }
    .moreBtn{
        height: 25px;
        line-height: 25px;
        width: 25px;
        padding: 0 0 20px 0;
        color: #666;
        border: 1px solid #E8EAED;
    }
    .badgeNum{
        display: inline-block;
        width: 6px;
        height: 6px;
        background: #d9534f;
        border-radius: 50%;
        margin-top: 6px;
    }
    .bgGrey{
        background: #FAFAFA;
        border-radius: 4px;
        padding:8px;
        border:1px solid #FAFAFA;
    }
    .commentBorder{
        border: 2px solid #D9534F;
        display: inline-block;
        border-radius: 50%;
        box-shadow: 0px 2px 8px 0px rgba(0,0,0,0.4);
    }
    .commentBorder img{
       width:44px;
       height:44px
    }
    .modal-body{
        overflow:hidden
    }
    .readBorder{
        border: 2px solid #D9534F;
        display: inline-block;
        border-radius: 50%;
        box-shadow: 0px 2px 8px 0px rgba(0,0,0,0.4);
    }
    .readBorder img{
       width:36px;
       height:36px
    }
    .childImage {
        width: 32px;
        height: 32px;
        object-fit: cover;
    }
    .wechat {
        justify-content: center;
        align-items: center;
    }
    .commentImage {
        width: 36px;
        height: 36px;
        object-fit: cover;
    }
    .commentBorder {
        border: 2px solid #D9534F;
        display: inline-block;
        border-radius: 50%;
        box-shadow: 0px 2px 8px 0px rgb(0 0 0 / 40%);
    }
    .commentBorder img {
        width: 44px;
        height: 44px;
    }
    .borderbto {
        border-bottom: 1px solid #E4E7EDFF;

    }
    .closeDate {
        position: absolute;
        right: 10px;
        top: 9px;
        line-height: 16px;
        cursor: pointer;
        transition: color .2s cubic-bezier(.645,.045,.355,1);
        width: 14px;
        height: 14px;
        border-radius: 50%;
        color: #999;
        text-align: center;
        line-height: 14px;
    }
    .borderRight{
        border-right:1px solid #E4E7EDFF
    }
    .borderLeft{
        border-left:1px solid #E4E7EDFF
    }
    .cloneBg{
        background: #FAFAFA;
    }
    .titleList{
        padding:10px 16px
    }
    .titleList:hover{
        background: #EBEDF0;
        color:#4D88D2 !important;
        cursor:pointer;
    }
    .titleListHover{
        background: #EBEDF0;
        color:#4D88D2 !important;
        cursor:pointer;
    }
    .el-tabsBto{
        border-bottom:2px solid #409EFF;
        color:#409EFF
    }
    .shareContent{
        width:375px;
        /* border-radius: 18px; */
        margin:0 auto
    }
    .shareStatus{
        position: absolute;
        left:0;
        top:0
    }
    .bgPto{
        background-repeat: no-repeat;
        background-size: contain;
    }
    .sharePto .logo{
        width:150px;
        margin-top:32px;
    }
    .sharePto .titleHover{
        font-size: 26px;
        font-family: PingFangSC-Semibold, PingFang SC;
        font-weight: 600;
        color: #FFFFFF;
        line-height: 38px;
        margin-top:24px
    }
    .sharePto .view{
        width: 100%;
        margin-top:8px;
        background: #E69119;
        font-size: 12px;
        color:#fff;
        padding:6px
    }
    .shareContent .contentText{
        margin:16px 16px 0;
        border-radius: 16px;
    }
    .contentText .titleHover{
        font-size:16px;
        line-height: 20px;
        margin-bottom:4px
    }
    .contentText .summary{
        font-size: 13px;
        line-height: 19px;
        margin-bottom:4px
    }
    .shareContent .bottom{
        margin:0 16px
    }
    .shareContent .bottom img{
        width: 100%;
        margin-bottom:16px
    }
    .contentText .wechat{
        border-top: 1px dashed #D0D0D0;
        margin-top: 20px;
        text-align: center;
    }
    .wechat{
        justify-content: center;
        align-items: center;
    }
    .fontWight{
        font-weight:600
    }
    .white{
        color:#fff
    }
    .Thumbnail{
        position: absolute;
        right: 0;
        top: 0;
        width:100px;
        text-align:center
    }
    .Thumbnail img{
        width:90px;
        padding:10px
    }
    .Thumbnail div{
        background:#F7F7F8
    }
    .Thumbnail .checkImg{
        border: 2px solid #4D88D2;
        border-radius: 8px;
    }
    .wechatImg{
        width: 125px;
        height: 125px;
        padding: 5px;
        background: #fff
    }
    .shareAvatar{
        width:40px;
        height:40px;
        object-fit:cover;
        border:1px solid #fff
    }
    .white08{
        opacity: 0.8;
    }
    .pt5{
        padding-top:5px
    }
    .p24{
        padding:24px !important
    }
    .p8{
        padding:8px
    }
    .Options{
        position: absolute;
        left: 0;
        top: 0;
        width:200px
    }
    .pt2{
        padding-top:2px
    }
    .modal-body{
        overflow:hidden
    }
    .readBorder{
        border: 2px solid #D9534F;
        display: inline-block;
        border-radius: 50%;
        box-shadow: 0px 2px 8px 0px rgba(0,0,0,0.4);
    }
    .readBorder img{
       width:36px;
       height:36px
    }
    .point{
        width: 8px;
        height: 8px;
        background: #F0AD4E;
        border-radius:50%;
        display:inline-block
    }
    .F0AD4E{
        color:#F0AD4E
    }
    .el-dropdown-link {
        cursor: pointer;
        color: #409EFF;
    }
    .el-icon-arrow-down {
        font-size: 12px;
    }
    .lineHeight{
        line-height:28px
    }
    .readMedia{
        padding:10px;
        background: #FAFAFA;
        border-radius:4px
    }
    .bgBlue{
        background: #4D88D2;
        color:#fff
    }
    .mt0{
        margin-top:0px !important
    }
    .fontWeight6{
        font-weight:600
    }
    .box-fixed{
        position:fixed;
        right:0;
        top:60px
    }
    .left-fixed{
        position:sticky;
        left:0;
        top:60px
    }
    .p0{
        padding:0 !important
    }
    .tagLabel {
        background:#EBEDF0;
        font-size:100%
    }
    .bRight{
        border-right:1px solid #D9D9D9;
    }
    .authorizedMedia:hover{
        background: rgba(77,136,210,0.1);
        border-radius: 4px;
        border: 1px solid #4D88D2;
        cursor: pointer;
    }
    [v-cloak] {
        display: none;
    }
    .optionSearch{
        height:auto
    }
    .imageSearch{
        width: 44px;
        height: 44px;
        object-fit: cover;
    }
    .text_overflow {
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        line-height: 14px;
    }
    .colorD8{
        color:#D8D8D8
    }
    .colorBlue{
        color:#2e6da4
    }
    .glyphiconList{
        border: 1px solid #E8EAED;
        padding:4px 7px;
        cursor:pointer
    }
    .inputSelect{
        width:300px;
    }
    .el-input__inner:focus{
        border-color: #428bca;
        box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.075) inset, 0px 0px 8px rgba(64, 158, 255, 0.5)
    }
    .el-input__inner{
        height:34px;
        line-height:34px;
        padding: 6px 12px;
        font-size: 14px;
        color: #555;
        background-color: #fff;
        background-image: none;
        border: 1px solid #ccc;
        border-radius: 4px;
        transition: border-color ease-in-out .15s,box-shadow ease-in-out .15s,-webkit-box-shadow ease-in-out .15s;
    }
    .wechatQrcode{
        display: inline-block;
        padding: 6px 10px;
        color: #50B674;
        background: #FFFFFF;
        border-radius: 4px;
        border: 1px solid #50B674;
        cursor: pointer;
    }
    .wechatQrcode img{
        width:16px;
        float:left;
        border-radius:50%
    }
    .qrcodeBox{
        position: absolute;
        left: 0;
        /* top: 15px; */
        width: 290px;
        /* height: 274px; */
        border: 1px solid #ddd;
        background: #fff;
        border-radius: 4px;
        text-align:center;
        font-size:14px;
        color:#333;
        box-shadow: 0px 2px 8px 0px rgb(0 0 0 / 20%);
        z-index:9;
        padding:20px 10px
    }
    .qrcodeBox .qrcodeWechat{
        width:124px;
        height:124px;
        margin:0 auto
    }
    .wechatIcon{
        height:16px;
        position: absolute;
        bottom: 0;
        right: 0;
        width: 16px;
    }
    .avatarWechat{
        width:60px;
        height:60px;
        border-radius:50%
    }
    .selectTeacher{
        width:300px;
        max-height: 300px;
        position: absolute;
        background: #fff;
        border: 1px solid #E8EAED;
        border-radius: 3px;
        top:35px;
        overflow-y: auto;
        z-index:1;
        left:0px;
        padding:8px 8px 16px 8px ;
        box-shadow: 0px 0px 6px 0px rgba(0,0,0,0.04), 0px 2px 4px 0px rgba(0,0,0,0.12);
    }
    .selectName{
        height:30px;
        line-height:30px;
        padding-left:10px
    }
    .selectName:hover{
        background:#EEF4FB
    }
    .activeShare{
        background:rgba(77, 136, 210, 0.10);
        color:#4D88D2
    }
    .managerBorder{
        border: 1px solid #E8EAED;
        border-radius: 4px 4px 0px 0px;
    }
    .managerBorderRight{
        border-right: 1px solid #E8EAED;
    }
    .p16{
        padding:16px
    }
    .height200{
        min-height:200px
    }
    .translate{
        width:16px;
        padding-top:5px
    }
    .schoolTitle,.schoolTitle1{
        color: #fff;
        font-size: 16px;
        margin-top:8px;
        position: relative;
        display: inline-block;
    }
    .schoolTitle:before, .schoolTitle:after {
        content: '';
        position: absolute;
        top: 50%;
        background: #fff;
        width: 25px;
        height: 1px;
    }
    .schoolTitle:before,.schoolTitle1:before{
        left: -35px;
    }
    .schoolTitle:after,.schoolTitle1:after {
        right: -35px;
    }
    .schoolTitle1:before, .schoolTitle1:after {
        content: '';
        position: absolute;
        top: 50%;
        background: #061F9D;
        width: 25px;
        height: 1px;
    }
    .delSearchText{
        position: absolute;
        left: -25px;
        top: 9px;
        z-index: 8;
        color: #999;
    }
    mark {
        background: yellow;
        padding: 0;
    }
    .fl{
        float:left
    }
    .fr{
        float:right
    }
    .forwarded{
        background:#F7F7F8;
        border: 1px solid #EBEDF0;
        padding:12px 16px;
        border-radius: 4px;
    }
    .el-input__suffix{
        top:3px
    }
    .breakAll{
        word-break: break-all;
    }
    .forwardOption{
        width:100%;
        height:auto
    }
    .forwardText{
        background: #FAFAFA;
        border-radius:4px;
        padding:16px
    }
    .label_flag{
        font-size: 11px;
        font-weight: 400;
        padding-left: 4px;
        padding-right: 4px;
    }
    .noReplyLists{
        position: absolute;
        right: 0;
        background: #FFFFFF;
        box-shadow: 0px 0px 6px 0px rgba(0,0,0,0.04), 0px 2px 4px 0px rgba(0,0,0,0.12);
        border-radius: 4px;
        border: 1px solid #E8EAED;
        max-height: 250px;
        z-index: 9;
        margin-bottom: 10px;
        min-width:200px
    }
    .noReplyLists .childImg{
        width:30px;
        height:30px;
        border-radius:50%
    }
    .noReplyLists .noList{
        align-items: center;
        padding: 6px 12px;
        font-size: 14px;
    }
    .noReplyLists .noList:hover{
        cursor: pointer;
        background: #F7F7F8;
    }
    .newMessage{
        width: 12px;
        height: 12px;
        background: #F0AD4E;
        border: 1px solid #FFFFFF;
        display: inline-block;
        position: absolute;
        border-radius: 50%;
        top: 0;
        right: 0;
    }
    .textareaCss{
        border: none;
        border-radius: 0;
        box-shadow: none;
    }
    .uploadIcon{
        background: #fff;
        font-size: 16px;
        
        color: #555555;
        margin-top:10px
    }
    .uploadImg{
        padding-top:20px;
        overflow-x: auto;
        white-space: nowrap;
        margin-top:10px
    }
    .fileImgs{
        width:72px;
        height:72px;
        object-fit: cover;
        display: inline-block;
    }
    .fileImg{
        width:72px;
        height:72px;
        object-fit: cover;
        display: inline-block;
        margin-bottom:10px;
        cursor: pointer;
    }
    .imgData{
        position: relative;
        display: inline-block;
    }
    .imgData span{    
        position: absolute;
        right: 3px;
        top: 3px;
        background: #333333;
        opacity: 1;
        color: #fff;
        width: 17px;
        height: 17px;
        border-radius: 50%;
        font-weight: 200;
        text-align: center;
        line-height: 15px;
        font-size: 19px;
    }
    .uploadFile {
        font-size:14px;
        padding:6px 12px;
        align-items: center;
    }
    .uploadFile .icon{
        display:none
    }
    .uploadFile:hover{
        background: #F7F7F8;

    }
    .uploadFile:hover .icon{
        display:block
    }
    .uploadFile a{
        color:#4D88D2
    }
    .fileLink{
        background: #F7F7F8;
        display: inline-block;
        border-radius: 4px;
        border: 1px solid #EBEDF0;
        padding: 6px 12px;
        font-size: 14px;
        margin-right: 16px;
        margin-bottom:8px;
        line-height:20px
    }
    .fileLink img{
        width:20px
    }
    .uploadLoading{
        width: 72px;
        height: 72px;
        background: #D9D9D9;
        border-radius: 4px;
        line-height: 72px;
        text-align: center;
    }
    .inputStyle{
        border: none;
        border-bottom: 1px solid #ccc;
        padding: 0;
        height: 25px;
        line-height: 25px;
        width: 100%;
    }
    .imgLi{
        list-style: none;
        padding-left: 0;
        margin-bottom:0px
    }
    .imgLi li{
        display:inline-block
    }
    .breakAll{
        word-break: break-word;
        text-align: justify
    }
    .breakAll img{
        max-width:100%
    }
    video{
        max-height: 300px;
        max-width: 100%;
        width: auto !important;
        margin-bottom:8px
    }
</style>
<div class="container-fluid" id='container' v-cloak >
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Teaching Tasks'), array('//mteaching/default/index')) ?></li>
        <li><?php echo Yii::t('directMessage', 'Direct Message') ?></li>
        <li><?php echo Yii::t("newDs", "Mine"); ?></li>
    </ol>
    <div class="row">
        <div class="col-md-3 col-lg-2 col-sm-12 leftBox ">
            <div class="list-group " id="classroom-status-list">
                <a href="<?php echo $this->createUrl('index'); ?>" class="list-group-item status-filter active"><?php echo Yii::t("newDS", "Mine"); ?></a>
                <a href="<?php echo $this->createUrl('feedback'); ?>" class="list-group-item status-filter"><span  v-if='toReplyNum>0' class='badgeNum pull-right'></span><?php echo Yii::t("newDS", "Parent Feedback"); ?></a>
            </div>
            <div v-if='otherTeacherId!=0 && otherTeacherId!=indexList.uid'>
                <div v-if='isDev==1' class='relative'>
                    <div class='wechatQrcode mb15' @click.stop='itdevShowUnWechatQrcode' >
                        <div>
                            <img src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/wechatDM.png' ?>" alt="">
                            <span class='ml4'><?php echo Yii::t("directMessage", "Wechat Notification"); ?></span>
                        </div>
                    </div>
                    <div class="qrcodeBox"  v-if='itdevScanQrcodeBox'>
                        <div class='font14 color3'><strong><?php echo Yii::t("directMessage", "Wechat Notification"); ?></strong></div>
                        <div class='mt8 font12 color6'><?php echo Yii::t("directMessage", "Response parent feedback on wechat!"); ?></div>
                        <div>
                            <div id='itdevwechatQrcodeBind' alt="" class='qrcodeWechat mt24'></div>
                            <div class='font12 mt16 color6 '><?php echo Yii::t("directMessage", "Wechat scan to bind your account."); ?></div>
                        </div>
                    </div>
                </div>
            </div>
            <div v-else class='relative'>            
                <div class='wechatQrcode mb15' @click.stop='showUnWechatQrcode' v-if='wechatData.state==0'>
                    <div>
                        <img src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/wechatDM.png' ?>" alt="">
                        <span class='ml4'><?php echo Yii::t("directMessage", "Wechat Notification"); ?></span>
                    </div>
                </div>
                <div class="qrcodeBox"  v-if='scanQrcodeBox'>
                    <div class='font14 color3'><strong><?php echo Yii::t("directMessage", "Wechat Notification"); ?></strong></div>
                    <div class='mt8 font12 color6'><?php echo Yii::t("directMessage", "Response parent feedback on wechat!"); ?></div>
                    <div>
                        <div id='wechatQrcodeBind' alt="" class='qrcodeWechat mt24'></div>
                        <div class='font12 mt16 color6 '><?php echo Yii::t("directMessage", "Wechat scan to bind your account."); ?></div>
                    </div>
                </div>
                <div class='wechatQrcode mb15' @click.stop='qrcodeBox=true'  v-if='wechatData.state==1'>
                    <div>
                        <img src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/wechatDM.png' ?>" alt="" v-if='wechatData.headimgurl=="" || wechatData.headimgurl==null'>
                        <img :src="wechatData.headimgurl" alt="" v-if='wechatData.headimgurl!=""'>
                        <span class='ml4'><?php echo Yii::t("directMessage", "Wechat Enabled"); ?></span>
                    </div>
                </div>
                <div  class="qrcodeBox"  v-if='qrcodeBox'>
                    <div class='font14 color3'><strong><?php echo Yii::t("directMessage", "Wechat Enabled"); ?></strong></div>
                    <div class='mt8 font12 color6'><?php echo Yii::t("directMessage", "Response parent feedback on wechat!"); ?></div>
                    <div class='relative inline-block' v-if='wechatData.headimgurl!=""'>
                        <img :src="wechatData.headimgurl" alt="" class='avatarWechat mt16'>
                        <img src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/wechatDM.png' ?>" alt="" class='wechatIcon'>
                    </div>
                    <div class='font12 mt16 color6 '  v-if='wechatData.nickname!=""'>{{wechatData.nickname}}</div>
                    <div class='font12 mt24 mt16 color6 '><button class="btn btn-default" type="button" @click.stop='unbind()'><?php echo Yii::t("directMessage", "Unbind your account"); ?></button></div>
                </div>
            </div>
            
        </div>
        <div class="col-md-6 col-lg-7 col-sm-12" >
            <div class='userInfo'  ref="element">
                <div class=' '> 
                    <div class=' mb24  addFl'>
                        <div class="btn-group">
                            <button type="button" class="btn btn-primary dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <?php echo Yii::t("directMessage", "New"); ?> <span class="caret"></span>
                            </button>
                            <ul class="dropdown-menu new">
                                <li><a href="<?php echo $this->createUrl('editOne'); ?>" ><?php echo Yii::t("directMessage", "Create a DM"); ?></a></li>
                                <li><a href="javascript:;" @click='cloneAll'><?php echo Yii::t("directMessage", "Clone a DM"); ?></a></li>
                            </ul>
                        </div>
                        <div class="btn-group ml16" v-if='groupList.length!=0'>
                            <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            <?php echo Yii::t("directMessage", "Division Privacy Management"); ?> <span class="caret"></span>
                            </button>
                            <ul class="dropdown-menu">
                                <li v-for='(list,index) in groupList'><a href="javascript:;" @click='facultyAuthority(list.value,list.text)'>{{list.text}}</a></li>
                            </ul>
                        </div>
                    </div>
                    <div class="form-inline addFr ">
                        <div class="form-group relative">
                            <v-date-picker name="calendar"  @dayclick="showHoliday"  :masks="masks"  v-model='clickDays' ref='calendar' :available-dates='availableDates' @update:from-page="pageChange" :disabled-dates='disabledDates' >
                            <template v-slot="{ inputValue, togglePopover,hidePopover  }">
                                <input
                                    class="form-control"
                                    :value="inputValue"
                                    style='width:160px'
                                    @click="togglePopover"
                                    placeholder="<?php echo Yii::t("directMessage", "By publish date"); ?>"
                                />
                                <span class='closeDate glyphicon glyphicon-remove-circle' v-if='clickDays!=null' @click.stop="removeDate(hidePopover)"></span>
                            </template>
                            </v-date-picker>
                        </div>
                        <div class="form-group has-feedback ml16">
                            <div class="input-group">
                                <input type="text" class="form-control" v-model='searchText' placeholder='<?php echo Yii::t("newDS", "Input text to search title or content");?>'  @keyup.enter='getIndex()'>
                                <a href='javascript:;' class="input-group-addon relative" @click='getIndex()'><span class='glyphicon glyphicon-search'></span> <?php echo Yii::t("global", "Search");?>
                                    <span class="glyphicon glyphicon-remove-circle font12 delSearchText"  v-if='searchText!="" && isSearch'  @click='searchText="";getIndex()'></span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <div class='clearfix'></div>
                <div class='loading'  v-if='indexLoading'>
                    <span></span>
                </div>
                <div class="media mb24" v-if='indexList.userInfo'>
                    <div class="media-left pull-left media-middle">
                        <a href="javascript:void(0)"><img :src="indexList.otherTeacherId!=0?indexList.userInfo[indexList.otherTeacherId].photoUrl:indexList.userInfo[indexList.uid].photoUrl" data-holder-rendered="true" class="avatar"></a>
                    </div>
                    <div class="media-right pull-right pt10 text-right" v-if='otherTeacherId!=0 && otherTeacherId!=indexList.uid'>
                        <div class="otherTeacher pull-right"  >
                            <span><?php echo Yii::t("directMessage", "You are viewing DMs of other staffs"); ?></span>
                            <span class='color9 font18'>|</span>
                            <span class='cur-p' @click='tabTeacher(staffSelf.uid)'>
                                <span class='ml5 text-primary'><?php echo Yii::t("directMessage", "Exit"); ?></span>
                            </span>
                        </div>
                    </div>
                    <div class="media-body mt5 media-middle">
                        <div class="media-heading lineHeight20  text-primary">
                            <span class='cur-p' @click='toggleTeacher("modal")'  v-if='Object.keys(teacherList).length>1'>
                                <span class='font18 fontWeight6'>{{indexList.otherTeacherId!=0?indexList.userInfo[indexList.otherTeacherId].name:indexList.userInfo[indexList.uid].name}}</span>
                                <span class='glyphicon glyphicon-chevron-down ml10'></span>
                            </span>
                            <span class='cur-p' v-else >
                                <span class='font18 fontWeight6'>{{indexList.otherTeacherId!=0?indexList.userInfo[indexList.otherTeacherId].name:indexList.userInfo[indexList.uid].name}}</span>
                            </span>
                            <span class='ml10' v-if='otherTeacherId==0 || otherTeacherId==indexList.uid'>
                                <span class='color9 font18'>|</span>
                                <span class='cur-p' @click='setAuthorize("modal")'>
                                    <span class='glyphicon glyphicon-user ml10'></span>
                                    <span class='ml5 '><?php echo Yii::t("directMessage", "Authorization & Share"); ?></span>
                                </span>
                            </span>
                        </div>
                        <div class="font14 color6">{{indexList.otherTeacherId!=0?indexList.userInfo[indexList.otherTeacherId].hrPosition:indexList.userInfo[indexList.uid].hrPosition}}</div>
                    </div>
                </div>
            </div>

            <div v-if='itemList.items && itemList.items.length!=0'>
                <p v-if='clickDays!=null || isSearch' class='color6 mb16 font14'>{{itemList.total}} <?php echo Yii::t("directMessage", "items filtered"); ?></p>
                <div class='' v-for='(list,index) in itemList.items'>
                    <div class='border p24 mb15 relative' v-if='list.category==3 || list.category==4'>
                        <span class='postStatus blue'><?php echo Yii::t("directMessage", "Parent Enquiry"); ?></span>
                        <div class='flex'>
                            <div class='flex1 font14 color3 title fontWeight6 listTitle' @click='showContent(list)' v-html='list.title'></div>
                            <span class='cur-p' @click='showContent(list)'  v-if='otherTeacherId==0 || otherTeacherId==indexList.uid'>
                                <img src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/comment.png' ?>" class='pull-left readIcon mr4' >
                                <span  class='line20 pull-left'>{{list.comment_num}} <?php echo Yii::t("directMessage", "comments"); ?></span>
                            </span>
                        </div>
                    </div>
                    <div class='border p24 mb15 relative' v-else>
                        <span class='postStatus' v-if='(otherTeacherId==0 || otherTeacherId==indexList.uid) && list.publish_type!=1' :class='list.publish_type==2?"yellow":list.publish_type==3?"green":""'>{{indexList.publishTypeList[list.publish_type]}}</span>
                        <div class='flex'>
                            <div class='flex1 font14 color3 title fontWeight6 listTitle' @click='showContent(list)' v-html='list.title'></div>
                            <div>
                                <img src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/qrcode.png' ?>" class='iconImg mr8' alt="" @click='showQrcode(list)'>
                                <el-tooltip v-if='list.is_favorite' class="item" effect="dark" content="<?php echo Yii::t("directMessage", "Remove from Favorites"); ?>" placement="top">
                                    <img src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/collected.png' ?>"  @click='delFavorite(list)' class='iconImg'>
                                </el-tooltip>
                                <el-tooltip  class="item" v-else effect="dark" content="<?php echo Yii::t("directMessage", "Add to Favorites"); ?>" placement="top">
                                    <img src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/coll.png' ?>"  @click='addFavorites(list)'  class='iconImg'>
                                </el-tooltip>
                                <div class="btn-group ml8">
                                    <button type="button" class="btn btn-default  btn-xs moreBtn dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">···</span>
                                    </button>
                                    <ul class="dropdown-menu menuRight">
                                        <li v-if='(otherTeacherId==0 || otherTeacherId==indexList.uid) && list.publish_type!=3'><a :href="'<?php echo $this->createUrl('editOne'); ?>&journal_id='+list.journal_id" ><?php echo Yii::t("asa", "Edit"); ?></a></li>
                                        <li><a href="javascript:;" @click='cloneContent(list)'><?php echo Yii::t("directMessage", "Clone"); ?></a></li>
                                        <li v-if='(otherTeacherId==0 || otherTeacherId==indexList.uid) && list.publish_type!=3'><a href="javascript:;" @click='unpublish(list)'><?php echo Yii::t("directMessage", "Not online, save as draft"); ?></a></li>
                                        <li v-if='(otherTeacherId==0 || otherTeacherId==indexList.uid) && list.publish_type!=3'><a href="javascript:;" @click='delJournal(list,"index")'><?php echo Yii::t("newDS", "Delete"); ?></a></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="media mt16" v-if='list.publish_type==2'>
                            <div class="media-left pull-left media-middle">
                                <a href="javascript:void(0)">
                                    <img class="contentAvatar" :src="indexList.userInfo[list.sign_as_uid].photoUrl" data-holder-rendered="true" >
                                </a>
                            </div>
                            <div class="media-body pt8 media-middle">
                                <h4  class="media-heading font12">{{indexList.userInfo[list.sign_as_uid].name}}<span class="label label-default color6 tagLabel  ml5"><?php echo Yii::t("directMessage", "Signature"); ?></span></h4>
                                <div class='text-muted'>{{indexList.userInfo[list.sign_as_uid].hrPosition}}</div>
                            </div>
                        </div>
                        <div class='color6 font12 mt12 flex' v-if='list.publish_type==2'>
                            <div class='flex1'>
                                <span><?php echo Yii::t("newDS", "Created:"); ?>{{indexList.userInfo[list.created_by].name}}</span>
                            </div>
                        </div>
                        <div class='color6 font12 mt12 pb20 borderBto flex' >
                            <div class='flex1' >
                                <span class='bRight pr8' ><?php echo Yii::t("directMessage", "Last edit: "); ?>{{indexList.userInfo[list.updated_by].name}}</span>
                                <span class='pl8'>{{list.format_updated_at}}</span>
                            </div>
                            <div>
                                <!-- <span class='F0AD4E ml12' v-if='!list.published'><span class='point'></span> <span>未到指定时间</span></span> -->
                                <span class='ml20' ><?php echo Yii::t("message", "Published"); ?>：{{list.format_publish_at}}</span>
                            </div>
                        </div>
                        <div class='flex mt20'>
                            <div class='flex1 color6' >
                                <span><span class='glyphicon glyphicon-user'></span><span class='ml5 mr10' ><?php echo Yii::t("newDS", "Subscribers"); ?> {{list.targets_num}} </span></span>
                                <el-tooltip placement="top" v-for='(item,i) in list.targets' >
                                    <div slot="content">{{indexList.childData[item].name}}</div>
                                    <img :src="indexList.childData[item].avatar" class='postObject mr8' alt="">
                                </el-tooltip>
                                <span class='more cur-p' @click='showMore(list)' v-if='list.targets_num>6'>+{{list.targets_num-6}}</span>
                            </div>
                            <div class='color6'>
                                <span class='cur-p' @click='showRead(list)' >
                                    <img src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/read.png' ?>" class='pull-left readIcon mr4'>
                                    <span class='line20 pull-left pr16 ' >{{list.reviewed_num}} <?php echo Yii::t("directMessage", "read"); ?></span>
                                </span>
                                <span class='cur-p' @click='showContent(list)'  v-if='otherTeacherId==0 || otherTeacherId==indexList.uid'>
                                    <img src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/comment.png' ?>" class='pull-left readIcon mr4' >
                                    <span  class='line20 pull-left'>{{list.comment_num}} <?php echo Yii::t("directMessage", "comments"); ?></span>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
                <nav aria-label="Page navigation" v-if='CopyPages.last_page>1'  class="text-left ml10">
                    <ul class="pagination">
                        <li v-if='pageNum >1'>
                            <a href="javascript:void(0)" @click="plus(1)" aria-label="Previous">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>
                        <li v-else class="disabled">
                            <a aria-label="Previous">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>
                        <li class="previous" v-if='pageNum >1'>
                            <a href="javascript:void(0)" @click="prev(pageNum)">‹</a>
                        </li>
                        <li class="disabled" v-else>
                            <a href="javascript:void(0)">‹</a>
                        </li>
                        <li v-for='(data,index) in pages.pages' :class="{ active:data==pageNum }">
                            <a href="javascript:void(0)" @click="plus(data)">{{data}}</a>
                        </li>
                        <li class="previous" v-if='pageNum <CopyPages.last_page'>
                            <a href="javascript:void(0)" @click="next(pageNum)">›</a>
                        </li>
                        <li class="previous disabled" v-else>
                            <a href="javascript:void(0)">›</a>
                        </li>
                        <li v-if='pageNum <CopyPages.last_page'>
                            <a href="javascript:void(0)" @click="plus(CopyPages.last_page)" aria-label="Next">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                        <li v-else class="disabled">
                            <a aria-label="Next">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                    </ul>
                </nav>
            </div>
            <div v-else-if='itemList.items'>
                <el-empty description="<?php echo Yii::t("ptc", "No Data"); ?>"></el-empty>
            </div>
        </div>
        <div class="col-md-3 col-sm-12 rightBox" >
            <div :style="'height:'+rightHeight+'px'" class='border mb20 pl24 pt24'>
                <div class='mb5'><span class='color3 font16'><?php echo Yii::t("directMessage", "Drafts"); ?></span>  <span class="badge">{{draftList.length}}</span></div>
                <div class='overflow-y  scroll-box pr24' :style="'height:'+(rightHeight-55)+'px'" >
                    <div v-if='draftList.length!=0'>
                        <div class='mt8 mb8' v-for='(list,index) in draftList'>
                            <div class='flex'>
                                <div class='flex1'>
                                    <div class='font14  pt8 title'>
                                        <a class='color3 title' v-if='list.title!=""' :href="'<?php echo $this->createUrl('editOne'); ?>&journal_id='+list.journal_id" >{{list.title}}</a>
                                        <a class='color3' v-else :href="'<?php echo $this->createUrl('editOne'); ?>&journal_id='+list.journal_id" ><?php echo Yii::t("directMessage", "NO TITLE"); ?></a>
                                    </div>
                                    <div class='color9 pt8'><?php echo Yii::t("directMessage", "Last saved:"); ?>{{list.format_updated_at}}</div>
                                </div>
                                <div class='ml20'>
                                    <div class="btn-group">
                                        <button type="button" class="btn btn-link  btn-sm dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                            ...</span>
                                        </button>
                                        <ul class="dropdown-menu menuRight">
                                            <li><a href="javascript:;" @click='delJournal(list,"draft")'><?php echo Yii::t("newDS", "Delete"); ?></a></li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div v-else>
                    <el-empty description="<?php echo Yii::t("ptc", "No Data"); ?>" :image-size="80"></el-empty>
                    </div>
                </div>
            </div>
            <div :style="'height:'+rightHeight+'px'"  class='border mt20 pt24'>
                <div class='mb5 pl24'><span class='color3 font16'><?php echo Yii::t("directMessage", "Favorites"); ?></span>  <span class="badge">{{favoriteList.length}}</span></div>
                <div class='overflow-y  scroll-box pl8 pr8' :style="'height:'+(rightHeight-55)+'px'" >
                    <div v-if='favoriteList.length!=0'>
                        <div class='mt8 mb8 keep pl16 pr16 pt12 pb12' v-for='(list,index) in favoriteList'>
                            <div class='flex'>
                                <div class='flex1'>
                                    <div class='font14 color3 pt8 title' @click='contentView(list,"#contentModal","coll")'>{{list.journal_title}}</div>
                                    <div class='color9 pt8'>{{list.favorite_title}}</div>
                                </div>
                                <div class='ml20'>
                                    <span v-if='list.on_top==0' >
                                        <el-tooltip  class="item" effect="dark" content="<?php echo Yii::t("newDS", "Stick on Top"); ?>" placement="top">
                                            <img src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/top.png' ?>"  @click='setTop(list)'  class='pull-left readIcon pt8'>
                                        </el-tooltip>
                                    </span>
                                    <span v-if='list.on_top==1'>
                                    <el-tooltip  class="item" effect="dark" content="<?php echo Yii::t("directMessage", "Undo pin"); ?>" placement="top">
                                            <img src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/pinned.png' ?>"  @click='delTop(list)'  class='pull-left readIcon pt8'>
                                        </el-tooltip>
                                    </span>

                                    <div class="btn-group pull-right ml5">
                                        <button type="button" class="btn btn-link  btn-sm dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                            ...</span>
                                        </button>
                                        <ul class="dropdown-menu menuRight">
                                            <li><a href="javascript:;" @click='cloneContent(list)'><?php echo Yii::t("directMessage", "Clone"); ?></a></li>
                                            <li><a href="javascript:;" @click='delFavorite(list)'><?php echo Yii::t("directMessage", "Remove from Favorites"); ?></a></li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="media pt8 mt0">
                                <div class="media-left pull-left media-middle">
                                    <a href="javascript:void(0)"><img :src="list.journal_user_avatar" data-holder-rendered="true" class="postObject"></a>
                                </div>
                                <div class="media-body pt4 media-middle">
                                    <div class="color6">{{list.journal_user_name}}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div v-else>
                    <el-empty description="<?php echo Yii::t("ptc", "No Data"); ?>" :image-size="80"></el-empty>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- 详情 -->
    <div class="modal fade" id="contentModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("newDS", "Detail"); ?></h4>
                </div>
                <div class="modal-body" v-if='contentData.journalData' @click='showNoReplyList=false' >
                    <div
                    class=' overflow-y scroll-box' 
                    :class="{'borderRight':!borderShow,'col-md-6 col-sm-6':(otherTeacherId==0 || otherTeacherId==indexList.uid) && !isColl,'col-md-12 col-sm-12':(otherTeacherId!=0 && otherTeacherId!=indexList.uid) && isColl}"  :style="'max-height:'+(height)+'px'" ref='contentShow'  >
                        <div><span class="label label-default tagLabel  color6"><?php echo Yii::t("newDS", "School Year"); ?>：{{contentData.journalData.start_year}}-{{contentData.journalData.start_year+1}}</span></div>
                        <div class='font20 color3 mt24 contentTitle'><label v-html='contentData.journalData.title'></label> </div>
                        
                        <div v-if='contentJournal.category==3 || contentJournal.category==4'>
                            <div v-if='contentJournal.category==3'>
                            <div class="media mt24">
                                    <div class="media-left pull-left media-middle">
                                        <a href="javascript:void(0)">
                                        <img :src="indexList.otherTeacherId!=0?indexList.userInfo[indexList.otherTeacherId].photoUrl:indexList.userInfo[indexList.uid].photoUrl" data-holder-rendered="true" class="contentAvatar">
                                        </a>
                                    </div>
                                    <div class="media-body pt8 media-middle">
                                        <h4  class="media-heading font12">{{indexList.otherTeacherId!=0?indexList.userInfo[indexList.otherTeacherId].name:indexList.userInfo[indexList.uid].name}}</h4>
                                        <div class='text-muted'>{{indexList.otherTeacherId!=0?indexList.userInfo[indexList.otherTeacherId].hrPosition:indexList.userInfo[indexList.uid].hrPosition}}</div>
                                    </div>
                                </div>
                            </div>
                            <div class='mt24' v-if='contentJournal.category==4'>
                                <div class='mt24 mb24 breakAll' v-html='contentData.deptData.deptDesc'></div>
                                <div v-if='contentData.deptData.staffs.length!=0'>                                
                                    <p class='color3 font14'><strong><?php echo Yii::t("directMessage", "Team members"); ?></strong> <span class="badge ml5">{{contentData.deptData.staffs.length}}</span></p>
                                    <div v-for='(list,index) in contentData.deptData.staffs' :class='indexList.otherTeacherId!=0?"col-md-6 col-sm-6":"col-md-12 col-sm-12"' class='mt8 mb8' >
                                        <div class="media">
                                            <div class="media-left pull-left media-middle">
                                                <a href="javascript:void(0)">
                                                    <img class="contentAvatar" :src="contentData.userInfo[list].photoUrl" data-holder-rendered="true" >
                                                </a>
                                            </div>
                                            <div class="media-body pt8 media-middle">
                                                <h4  class="media-heading font12">{{contentData.userInfo[list].name}}</h4>
                                                <div class='text-muted'>{{contentData.userInfo[list].hrPosition}}</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div v-else>
                            <div class="media mt24">
                                <div class="media-left pull-left media-middle">
                                    <a href="javascript:void(0)">
                                        <img class="contentAvatar" :src="contentData.userInfo[contentData.journalData.sign_as_uid].photoUrl" data-holder-rendered="true" >
                                    </a>
                                </div>
                                <div class="media-body pt8 media-middle">
                                    <h4  class="media-heading font12">{{contentData.userInfo[contentData.journalData.sign_as_uid].name}}<span class="label label-default color6 tagLabel  ml5"><?php echo Yii::t("directMessage", "Signature"); ?></span></h4>
                                    <div class='text-muted'>{{contentData.journalData.sign_as_title}}</div>
                                </div>
                            </div>
                            <div class='mt24 contentList breakAll' v-html='contentData.journalData.content'></div>
                            <div class='mt24'  v-if='contentData.journalData.attachments.length!=0'>
                                <p class='color3 font14'><strong><?php echo Yii::t("curriculum", "Attachments"); ?></strong> <span class="badge ml5">{{contentData.journalData.attachments.length}}</span></p>
                                <div v-for='(list,index) in contentData.journalData.attachments' class='mt16' style='word-break:break-all;'>
                                    <a :href="list.file_key" target='_blank'><span class='glyphicon glyphicon-paperclip mr4'></span>{{list.title}}</a>
                                </div>
                            </div>                   
                            <div class='mt24'>
                                <p class='color3 font14'><strong><?php echo Yii::t("newDS", "Subscribers"); ?></strong> <span class="badge ml5">{{contentData.journalData.targets_num}}</span></p>
                                <div class='mt16' v-if='contentData.journalData.targets.length>6'>
                                    <el-tooltip placement="top" v-for='(list,index) in contentData.journalData.targets' >
                                        <div slot="content">
                                            <div>{{contentData.childData[list].name}}</div>
                                            <div>{{contentData.childData[list].className}}</div>
                                        </div>
                                        <img class="contentPostObject mr16 mb8" :src="contentData.childData[list].avatar" data-holder-rendered="true" >
                                    </el-tooltip>
                                    <span class='postMore cur-p' v-if='contentData.journalData.targets_num>10' @click='showMore("show")'>+ {{contentData.journalData.targets_num-10}} ></span>
                                </div>
                                <div class='mt16' v-else>
                                    <div class="media mt16"  v-for='(list,index) in contentData.journalData.targets'>
                                        <div class="media-left pull-left media-middle">
                                            <a href="javascript:void(0)">
                                                <img :src="contentData.childData[list].avatar" data-holder-rendered="true" class="contentPostObject">
                                            </a>
                                        </div>
                                        <div class="media-body pt4 media-middle">
                                            <h4 class="media-heading font12">{{contentData.childData[list].name}}</h4>
                                            <div class="text-muted">{{contentData.childData[list].className}}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class='mt24' v-if='contentData.journalData.joint_admins.length!=0'>
                                <p class='color3 font14'><strong><?php echo Yii::t("directMessage", "Collaboration"); ?></strong> <span class="badge ml5">{{contentData.journalData.joint_admins.length}}</span></p>
                                <div class='row'>
                                    <div v-for='(list,index) in contentData.journalData.joint_admins' :class='indexList.otherTeacherId!=0?"col-md-6 col-sm-6":"col-md-12 col-sm-12"' class='mt8 mb8' >
                                        <div class="media">
                                            <div class="media-left pull-left media-middle">
                                                <a href="javascript:void(0)">
                                                    <img class="contentPostObject" :src="contentData.userInfo[list].photoUrl" data-holder-rendered="true" >
                                                </a>
                                            </div>
                                            <div class="media-body pt4 media-middle">
                                                <h4  class="media-heading font12">{{contentData.userInfo[list].name}}</h4>
                                                <div class='text-muted'>{{contentData.userInfo[list].hrPosition}}</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class='col-md-6 col-sm-6 overflow-y scroll-box relative' v-if='(otherTeacherId==0 || otherTeacherId==indexList.uid) && !isColl' :class='borderShow?"borderLeft":""' ref='contentComment' :style="'height:'+(height)+'px'">
                        <div class='font14 color3 flex'>
                            <span class='flex1'><?php echo Yii::t("global", "Comment"); ?></span> 
                            
                            <span class="el-dropdown-link" @click.stop='noReplyList()' v-if='contentJournal.category==2'>
                            <?php echo Yii::t("directMessage", "Add Message");?><i class="el-icon-arrow-down el-icon--right"></i>
                            </span>                            
                        </div>
                        <div class='noReplyLists overflow-y scroll-box' v-if='showNoReplyList && Object.keys(noReplyChild).length!=0'>
                            <div class='p10 '> <span class='F0AD4E'><?php echo Yii::t("directMessage", "To parents who have not responded.");?></span> </div>
                            <div class='noList flex' v-for='(list,index) in noReplyChild' @click='addReplyChild(list,list.id,index)'>
                                <img class='childImg' :src='list.avatar' />
                                <span class='color6 flex1 ml10'>{{list.name}}</span> 
                            </div>
                        </div>
                        <div  v-if='commentChild.length!=0'>
                            <div class='pt10 pb10 borderbto'>
                                <div v-for='(childList,index) in commentChild' class='pull-left flex wechat'  style='width:60px;height:55px;text-align:center;' >
                                    <span class='inline-block relative' :class='childList.id==commentChildId?"commentBorder":""' >
                                        <el-tooltip placement="top" >
                                            <div slot="content">
                                                <div class='text-center'>{{childList.name}}</div>
                                                <div class='text-center'>{{childList.class_name}}</div>
                                            </div>
                                            <img class='img-circle commentImage cur-p'  :title="`<div>${childList.name}</div>${childList.class_name}`"  @click='commentList(childList.id)'  :src="childList.avatar" alt="">
                                        </el-tooltip>
                                        <span class='newMessage' v-if='childList.new'></span>
                                    </span>
                                </div>
                                <div class='clearfix'></div>
                            </div>
                            <div class="alert alert-info mt20" role="alert" v-if='commentChildId==""'><?php echo Yii::t("newDS", "Click a child to view comments."); ?></div>
                        </div>
                        <div class="alert alert-warning mt16" role="alert" v-else><?php echo Yii::t("newDS", "No Comment"); ?></div>
                        <div class=' scroll-box messageChild relative mb20' v-if='commentData.items && commentData.items.length!=0'>
                            <div class='loading'  v-if='commentLoading'>
                                <span></span>
                            </div>
                            <div class="media mt20 mb10" v-for='(fItem,i) in commentData.items'>
                                <div v-if='fItem.mark_as_staff==0'>
                                    <div class='flex' v-if="fItem.creator_type == 'parent'">
                                        <div class="">
                                            <input type="checkbox" class='mr15' v-if='isForward' :value='i' v-model='inputForward'>
                                            <img :src="commentChildData.avatar" data-holder-rendered="true" class="img-circle commentImage">
                                        </div>
                                        <div class="flex1">
                                            <div class="flex1 ml8" v-if='fItem.repost==0 || fItem.repost==1'>
                                                <h4 class="media-heading color3 font14 flex1" style='line-height:37px'><strong>{{commentChildData.name}}</strong> </h4>
                                                <div class="content-2 mb10" v-html='html(fItem.content)'></div>
                                                <div>
                                                    <ul class='mb12 imgLi' :id='fItem.id' v-if='fItem.imgUrl.length!=0'>
                                                        <li v-for='(list,j) in fItem.imgUrl'>
                                                            <img :src="list.url" class='fileImg mr8' @click='showImg(fItem)' alt="" >
                                                        </li>
                                                    </ul>
                                                    <div  v-if='fItem.videoUrl.length!=0'>
                                                        <div v-for="(list, j) in fItem.videoUrl" :key="j">
                                                            <div v-if="list.video_convert_status == 0" class="flex fileLink">
                                                                <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/link.png' ?>" alt="" class="pdfImg" /> 
                                                                <a class='flex1 ml5' target= "_blank" :href="list.url"><?php echo Yii::t('message', "Convert") ?></a>
                                                            </div>
                                                            <video v-else :src="list.url" :poster="list.video_cover"  controls muted preload="auto" style="width: 100%; height: 100%; fit: fill"></video>
                                                        </div>
                                                    </div>
                                                    <div >
                                                        <div class='flex fileLink' v-for='(list,j) in fItem.pdfUrl'>
                                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/PDF1.png' ?>" alt="" class="pdfImg" v-if='list.mimetype=="application/pdf"' />
                                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/excel.png' ?>" alt="" class="pdfImg" v-else-if='list.mimetype=="application/vnd.ms-excel" || list.mimetype=="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"' />
                                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/word1.png' ?>" alt="" class="pdfImg" v-else-if='list.mimetype=="application/msword" || list.mimetype=="application/vnd.openxmlformats-officedocument.wordprocessingml.document"' />
                                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/zip.png' ?>" alt="" class="pdfImg" v-else-if='list.mimetype=="application/x-zip-compressed"' />
                                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/link.png' ?>" alt="" class="pdfImg" v-else /> 
                                                            <a class='flex1 ml5' target= "_blank" :href="list.url">{{list.title}}
                                                            </a>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="text-muted" v-if="commentChildData[`p_${fItem.created_by}`] == 'f'"><?php echo Yii::t("newDS", "From student's Dad");?><span class='ml16'>{{ fItem.updated_at }}</span></div>
                                                <div class="text-muted font12 mt10" v-else><?php echo Yii::t("newDS", "From student's Mom");?> <span class='ml16'>{{ fItem.updated_at }}</span> </div>
                                                <div class='forwarded color6 mt15' v-if='fItem.repost==1'>
                                                    <div v-if='fItem.repost_to_type==3'><?php echo Yii::t("directMessage", "Handover to: ");?>{{commentData.staff[fItem.repost_to_target].name}}</div>  
                                                    <div v-if='fItem.repost_to_type==4'><?php echo Yii::t("directMessage", "Handover to: ");?>{{commentData.deptInfo[fItem.repost_to_target]}}</div>  
                                                    <div  class='mt5 flex' v-if='fItem.repost_to_mark!=""'><span><?php echo Yii::t("labels", "Memo");?>：</span><div class='flex1' v-html='html(fItem.repost_to_mark)'></div></div>
                                                </div>
                                            </div>
                                            <div class="flex1 ml8"  v-if='fItem.repost==2'>
                                                <div class="align-items flex">
                                                    <h4 class="media-heading color3 font14 flex1" style='line-height:37px'><strong>{{commentChildData.name}}</strong> </h4>
                                                    <div class='font14 text-primary cur-p' @click='showOriginalInfo(fItem.id)'><img width='16' src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/more.png' ?>" alt=""> <?php echo Yii::t("directMessage", "Original Information");?></div>
                                                </div>
                                                <div class="content-2 mb10" v-html='html(fItem.content)'></div>
                                                <div>
                                                    <ul class='mb12 imgLi' :id='fItem.id' v-if='fItem.imgUrl.length!=0'>
                                                        <li v-for='(list,j) in fItem.imgUrl'>
                                                            <img :src="list.url" class='fileImg mr8' @click='showImg(fItem)' alt=""  >
                                                        </li>
                                                    </ul>
                                                    <div  v-if='fItem.videoUrl.length!=0'>
                                                        <div v-for="(list, j) in fItem.videoUrl" :key="j">
                                                            <div v-if="list.video_convert_status == 0" class="flex fileLink">
                                                                <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/link.png' ?>" alt="" class="pdfImg" /> 
                                                                <a class='flex1 ml5' target= "_blank" :href="list.url"><?php echo Yii::t('message', "Convert") ?></a>
                                                            </div>
                                                            <video v-else :src="list.url" :poster="list.video_cover"  controls muted preload="auto" style="width: 100%; height: 100%; fit: fill"></video>
                                                        </div>
                                                    </div>
                                                    <div >
                                                        <div class='flex fileLink' v-for='(list,j) in fItem.pdfUrl'>
                                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/PDF1.png' ?>" alt="" class="pdfImg" v-if='list.mimetype=="application/pdf"' />
                                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/excel.png' ?>" alt="" class="pdfImg" v-else-if='list.mimetype=="application/vnd.ms-excel" || list.mimetype=="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"' />
                                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/word1.png' ?>" alt="" class="pdfImg" v-else-if='list.mimetype=="application/msword" || list.mimetype=="application/vnd.openxmlformats-officedocument.wordprocessingml.document"' />
                                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/zip.png' ?>" alt="" class="pdfImg" v-else-if='list.mimetype=="application/x-zip-compressed"' />
                                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/link.png' ?>" alt="" class="pdfImg" v-else /> 
                                                            <a class='flex1 ml5' target= "_blank" :href="list.url">{{list.title}}
                                                            </a>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="text-muted font12" ><?php echo Yii::t("directMessage", "Handover at: ");?>{{ fItem.updated_at }}</div>
                                                <div class='forwarded color6 mt15' v-if='fItem.repost==2'>
                                                    <div><?php echo Yii::t("directMessage", "Handover from: ");?>{{commentData.staff[fItem.repost_by].name}}</div>  
                                                    <div  class='mt5 flex'  v-if='fItem.repost_mark!=""'><span><?php echo Yii::t("labels", "Memo");?>：</span><div class='flex1' v-html='html(fItem.repost_mark)'></div></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class='flex' v-else>
                                        <div class="">
                                            <input type="checkbox" class='mr15' disabled  v-if='isForward'>
                                            <img :src="commentData.staff[fItem.created_by].avatar" data-holder-rendered="true" class="img-circle commentImage ">
                                        </div>
                                        <div class="flex1 pl12">
                                            <h4 class="media-heading color3 font14 "  style='line-height:37px'><strong>{{commentData.staff[fItem.created_by].name}}</strong> </h4>
                                            <div class='color3 font14 mb10' v-html='fItem.content'></div>
                                            <div>
                                                <ul class='imgLi' :id='fItem.id' v-if='fItem.imgUrl.length!=0'>
                                                    <li v-for='(list,j) in fItem.imgUrl'>
                                                        <img :src="list.url" class='fileImg mr8' @click='showImg(fItem)' alt=""  >
                                                    </li>
                                                </ul>
                                                <div  v-if='fItem.videoUrl.length!=0'>
                                                    <div v-for="(list, j) in fItem.videoUrl" :key="j">
                                                        <div v-if="list.video_convert_status == 0" class="flex fileLink">
                                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/link.png' ?>" alt="" class="pdfImg" /> 
                                                            <a class='flex1 ml5' target= "_blank" :href="list.url"><?php echo Yii::t('message', "Convert") ?></a>
                                                        </div>
                                                        <video v-else :src="list.url" :poster="list.video_cover"  controls muted preload="auto" style="width: 100%; height: 100%; fit: fill"></video>
                                                    </div>
                                                </div>
                                                <div >
                                                    <div class='flex fileLink' v-for='(list,j) in fItem.pdfUrl'>
                                                        <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/PDF1.png' ?>" alt="" class="pdfImg" v-if='list.mimetype=="application/pdf"' />
                                                        <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/excel.png' ?>" alt="" class="pdfImg" v-else-if='list.mimetype=="application/vnd.ms-excel" || list.mimetype=="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"' />
                                                        <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/word1.png' ?>" alt="" class="pdfImg" v-else-if='list.mimetype=="application/msword" || list.mimetype=="application/vnd.openxmlformats-officedocument.wordprocessingml.document"' />
                                                        <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/zip.png' ?>" alt="" class="pdfImg" v-else-if='list.mimetype=="application/x-zip-compressed"' />
                                                        <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/link.png' ?>" alt="" class="pdfImg" v-else /> 
                                                        <a class='flex1 ml5' target= "_blank" :href="list.url">{{list.title}}
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class='text-muted'>{{ fItem.updated_at }}</div>
                                        </div>
                                    </div>
                                </div>
                                <div v-else class='pl20 mt20 text-center borderTop' :class='i!=commentData.items.length-1?"borderB":""'>
                                    <p class='font14 color3'>
                                        <span class='glyphicon glyphicon-pushpin mr10' style='color:#EC971FFF'></span>{{commentData.staff[fItem.created_by].name}} <?php echo Yii::t("newDS", "marked No Reply Needed"); ?> <span class='font14 color6 ml10'>{{fItem.updated_at}}</span>
                                    </p>
                                    <div  class='font12 color9'>
                                        <?php echo Yii::t("newDS", "(This message is invisible to parents)"); ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div v-if='isForward' class='mt20'>
                            <div class="F0AD4E mb16 font14 flex"><span aria-hidden="true" class="glyphicon glyphicon-info-sign"  style='margin-top:2px'></span> <span class='ml4'><?php echo Yii::t("directMessage", "Please select items and click the “Handover” button, then select target staff or deptartment.");?></span></div>
                            <div class='text-right'>
                                <span class='color6 mr16'><?php echo Yii::t("directMessage", "selected");?> {{inputForward.length}}</span>
                                <button type="button" class="btn btn-link mr20"  @click.stop="forwardCancel"><?php echo Yii::t("global", "Cancel");?></button>
                                <button class="btn btn-primary"  @click.stop="forwardTeacher()" :disabled="loading"  ><?php echo Yii::t("directMessage", "Handover");?></button>
                            </div>
                        </div>
                        <div  v-else-if='commentData.items'>
                            <div>
                                <div class='uploadImg' v-if='uploadImgList.length>0'>
                                    <div class='imgData mr8'  v-for='(list,i) in uploadImgList'>
                                        <div v-if="list.types=='1'">
                                            <div class='uploadLoading'> <?php echo Yii::t("directMessage", "Uploading");?></div>
                                        </div>
                                        <div v-else>
                                            <img class='fileImgs' @click='uploadShowImg(list.file_key)'  :src="list.file_key" alt="">
                                            <span aria-hidden="true"  @click.stop='delImg("img",list,i)'>×</span>
                                        </div>
                                    </div>
                                    <template v-if='loadingType==1'>
                                        <div class='imgData mr8'  v-for='(list,i) in loadingList'>
                                                <div class='uploadLoading'> <?php echo Yii::t("directMessage", "Upload queue");?></div>
                                        </div>
                                    </template>
                                </div>
                                <div class='mt16' v-if='uploadLinkList.length>0'>
                                    <div class='flex uploadFile' v-for='(list,index) in uploadLinkList'>
                                        <span class='glyphicon glyphicon-paperclip mr8'></span>
                                        <a target="_blank" class='flex1'  style='line-height:26px' :href='list.file_key' v-if='!list.isEdit'>{{list.title}}</a>
                                        <span class='flex1' v-else><input type="input" class='inputStyle'  @keyup.enter="saveFile(list,index)"  v-model='attachmentName' ></span>
                                        <template v-if='!list.isEdit'>
                                            <span class='glyphicon glyphicon-edit icon mr16' v-if='list.file_key!=""' @click.stop='list.isEdit=true,attachmentName=list.title'></span>
                                            <span class='glyphicon glyphicon-trash icon' v-if='list.file_key!=""' @click.stop='delImg("link",list,index)'></span>
                                        </template>
                                        <span style='width:90px'  class='text-right inline-block' v-else>
                                            <button type="button" class="btn btn-primary btn-xs" @click='saveFile(list,index)'><?php echo Yii::t("global", "Save");?></button>
                                            <button type="button" class="btn btn-default btn-xs" @click='list.isEdit=false'><?php echo Yii::t("global", "Cancel");?></button>
                                        </span>
                                    </div>
                                </div>
                                <template v-if='loadingType==2'>
                                    <div class='flex uploadFile'  v-for='(list,i) in loadingList'>
                                        <span class='glyphicon glyphicon-paperclip mr8'></span>
                                        <a  class='flex1'  style='line-height:26px' > <?php echo Yii::t("directMessage", "Upload queue");?></a>
                                    </div>
                                </template>
                                <div class='uploadIcon'>
                                    <div class='cur-p inline-block' id='pickfilesPhoto'>
                                        <span class='glyphicon glyphicon-picture pull-left' style='margin-top:2px'></span>
                                        <span class='ml5 font14 color6'><?php echo Yii::t("directMessage", "Photo");?></span> 
                                    </div>
                                    <div class='cur-p inline-block ml10' id='pickfilesVideo'>
                                        <span class='glyphicon glyphicon-picture pull-left' style='margin-top:2px'></span>
                                        <span class='ml5 font14 color6'><?php echo Yii::t("directMessage", "Video");?></span> 
                                    </div>
                                    <div class='cur-p inline-block ml10' id='pickfilesPDF'>
                                        <span class='glyphicon glyphicon-paperclip pull-left' style='margin-top:2px'></span>
                                        <span class='ml5 font14 color6'><?php echo Yii::t("curriculum", "Attachments");?></span> 
                                    </div>
                                </div>
                                <textarea class="form-control" rows="5" v-model='commentContent' placeholder='<?php echo Yii::t("newDS", "Please input your reply"); ?>'></textarea>
                                <div  class='flex align-items'>
                                    <div>
                                        <!--  v-if='contentData.journalData.category==3 || contentData.journalData.category==4' -->
                                        <div>                                        
                                            <span class='text-primary pull-left cur-p font14' @click.stop='forwardEvent()' ><?php echo Yii::t("directMessage", "Handover");?></span>
                                            <span class="glyphicon glyphicon-info-sign color6 ml5 p2"   onMouseOver="$(this).tooltip('show')"  data-toggle="tooltip" data-html='true' title="<?php echo Yii::t("directMessage", "If enquiries should not be answered by you, you can handover to others.");?>"  data-placement="right"></span>
                                        </div>
                                    </div>
                                    <p class='text-right pull-right mt10 flex1'>
                                        <button type="button" class="btn btn-link mr20" v-if='commentData.items.length>0' @click.stop="markNoReply"><?php echo Yii::t("newDS", "No Reply Needed"); ?></button>
                                        <button class="btn btn-primary" @click.stop="reply('0')" :disabled="loadingBtn"><?php echo Yii::t("newDS", "Reply"); ?></button>
                                    </p>
                                </div>
                            </div>
                            
                            <div class='clearfix'></div>
                        </div>

                    </div>
                    <div class='clearfix'></div>
                </div>
            </div>
        </div>
    </div>
    <!-- 标记为未回复 -->
    <div class="modal fade" id="noReplyModal" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-sm" role="document">
            <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("newDS", "Warning"); ?></h4>
            </div>
            <div class="modal-body">
                <div v-if='contentData.journalData && contentData.journalData.joint_admins.length!=0'>
                    <div><?php echo Yii::t("directMessage", 'This DM involves multi collaborators. This operation will cause everyone to no longer receive unreplied reminders about this feedback. Are you sure to continue?');?> </div>
                    <div class="checkbox">
                        <label>
                            <input type="checkbox" v-model='noReplySure'><?php echo Yii::t("directMessage", 'Yes, pretty sure');?>
                        </label>
                    </div>
                </div>
                <div v-else><?php echo Yii::t("newDS", 'Proceed to mark it as "No Reply Needed"?');?></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Cancel"); ?></button>
                <button type="button" class="btn btn-primary" @click='reply("noReply")'><?php echo Yii::t("message", "OK"); ?></button>
            </div>
            </div>
        </div>
    </div>
    <!-- 查看全部发布对象 -->
    <div class="modal fade" id="postObjectModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content"  >
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("directMessage", "All subscribers"); ?></h4>
                </div>
                <div class="modal-body" >
                    <div class="form-group">
                        <div class="input-group">
                        <div class="input-group-addon"><span class='glyphicon glyphicon-search'></span></div>
                        <input type="text" class="form-control" v-model='search' placeholder="<?php echo Yii::t("attends", "Filter by name"); ?>">
                        </div>
                    </div>
                    <div class='font14'>
                        <span class='glyphicon glyphicon-user color6'> </span><span class='color3 pl4'><?php echo Yii::t("newDS", "Total"); ?> {{Object.keys(searchData).length}} <?php echo Yii::t("directMessage", "subscribers"); ?></span>
                    </div>
                    <div class='overflow-y  scroll-box' :style="'max-height:'+(height-100)+'px'">
                        <div class="media mt10 mb10 col-md-6 col-sm-6"  v-for='(list,key,index) in searchData' >
                            <div class="media-left pull-left media-middle">
                                <a href="javascript:void(0)">
                                    <img class="contentAvatar" :src="list.avatar" data-holder-rendered="true" >
                                </a>
                            </div>
                            <div class="media-body pt8 media-middle">
                                <h4  class="media-heading font12">{{list.name}}</h4>
                                <div class='text-muted'>{{list.class_name}}</div>
                            </div>
                        </div>
                        <div class='clearfix'></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- 阅读统计 -->
    <div class="modal fade" id="readModal" tabindex="-1" role="dialog"  >
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="parentCommentsModalLabel"><?php echo Yii::t("newDS", "Read Report"); ?></h4>
            </div>
            <div class="modal-body">
                <div class='loading'  v-if='readLoading'>
                    <span></span>
                </div>
                <div v-if='targetsChildId!=""'>
                    <div class='col-md-8 pb20'  style='border-right:1px solid #E4E7EDFF'>
                        <div class='col-md-12'>
                            <div class='flex mt10' >
                                <div style='width:85px'>
                                    <img :src="reviewChilds[targetsChildId].avatar" style='width:70px;height:70px' class='media-object img-circle image' alt="">
                                </div>
                                <div class='flex1 mt20'>
                                    <p class='font14 color3 mb5' style="display: flex;">
                                        <strong >{{reviewChilds[targetsChildId].name}}</strong>
                                        <label style="display: flex;flex-wrap:wrap;width: 90%">
                                            <span
                                                 v-for="(item,index) in reviewChilds[targetsChildId].label"
                                                 :key="index"
                                                 :title="item.desc"
                                                 :style="'color:rgb('+(item.color)+')'"
                                                 class="label"
                                                 v-html='item.name'
                                            >
                                            </span>
                                        </label>
                                    </p>
                                    <div class='font12 color6'>{{reviewChilds[targetsChildId].class_name}}</div>
                                </div>
                            </div>
                        </div>
                        <div class='col-md-6' v-for="(list,index) in targetsParent">
                            <p class='font14 color3 relative mt15' style="margin-bottom: 4px">
                                <span class='readPoint'></span>
                                <span class='ml15'>{{list.parent_relationship_name}}</span>
                            </p>
                            <div class='ml15' style="display: flex;flex-wrap: wrap">
                                <span v-for="(item,index1) in list.parent_flag"
                                      :key="index1"
                                      :title="item.desc"
                                      :style="{backgroundColor:item.color}"
                                      class="label label-info mr8 mb8 label_flag"
                                >
                                    {{item.name}}
                                </span>
                            </div>
                            <div class='ml15'>
                                <p class='mb10 font14 flex'><span style='width:15px;padding-top: 3px;' class='glyphicon glyphicon-user color6'></span><span class='ml10 flex1'>{{list.parent_name}}</span></p>
                                <p class='mb10 font14 color6 flex' ><span style='width:15px;padding-top: 3px;' class='glyphicon glyphicon-earphone'></span> <span class='ml10 flex1'>{{list.parent_tel}}</span></p>
                                <p class='mb10 font14 color6 flex'><span style='width:15px;padding-top: 3px;' class='glyphicon glyphicon-envelope'></span> <span class='ml10 flex1' style='word-break:break-all;'>{{list.parent_email}}</span></p>
                            </div>
                        </div>
                    </div>
                    <div class='col-md-4 pb20'>
                        <p><strong><?php echo Yii::t("newDS", "Wechat message receivers"); ?></strong>  </p>
                        <div class=' scroll-box' style='max-height:185px;overflow-y:auto' v-if='wxReceivers && wxReceivers.length!=0'>
                            <div class='flex mt10'  v-for='(list,index) in wxReceivers'>
                                <div style='width:50px'>
                                        <img v-if='list.headimgurl!=""' :src="list.headimgurl" style='width:40px;height:40px' class='media-object img-circle image' alt="">
                                        <img v-else src="http://m2.files.ivykids.cn/cloud01-file-8025768Fn6dkbMWLydi9RSVfmuPHU2CITQ6.png" style='width:40px;height:40px' class='media-object img-circle image' alt="">
                                </div>
                                <div class='flex1'>
                                    <p><span class="label label-info" v-if='list.isDev'>IT DEV</span> <strong> {{list.nickname}}</strong></p>
                                    <div><?php echo Yii::t("newDS", "Recent received:"); ?> {{list.recent}}</div>
                                </div>
                            </div>
                        </div>
                        <div v-else class="alert alert-warning" role="alert"><?php echo Yii::t("newDS", "No messsage sent successfully in recent 60 days."); ?></div>
                    </div>
                </div>
                <div class='pt10 col-md-12'  :style='targetsChildId!=""?"border-top:1px solid #E4E7EDFF":""'>
                    <div class='col-md-6 col-sm-6 text-center '  :class='clientWidth>"750"?"borderRight":""'>
                        <div id='echart' style='height:400px;width:100%'></div>
                    </div>
                    <div class='col-md-6 col-sm-6 '>
                        <div class='font14 color3 mb12 flex'>
                           <span class='flex1'><?php echo Yii::t("newDS", "Subscribers List"); ?><span class="badge ml5">{{reviewTargets.length}}</span> </span>
                            <div class='font12' >
                                <span class='glyphicon glyphicon-th-large pull-left glyphiconList' style='border-radius:4px 0 0 4px'   :class='showChild==1?"colorBlue":"colorD8"'  @click='showChild=1'></span>
                                <span class='glyphicon glyphicon-align-justify  pull-left glyphiconList'  style='border-left: none;border-radius:0 4px 4px 0'  :class='showChild==2?"colorBlue":"colorD8"'  @click='showChild=2'></span>
                            </div>
                        </div>
                        <p class='color6 mb12'><?php echo Yii::t("directMessage", "Notifications sent:"); ?>{{sendNumFamily}} <?php echo Yii::t("directMessage", "students. (Notifications normally go out in the next 10 mins)"); ?></p>
                        <div class='pt10 scroll-box' v-if='reviewTargets.length!=0'  style='max-height:400px;overflow-y:auto;padding:5px 18px'>
                            <div v-if='showChild==1'>
                                <div class='pull-left flex wechat' v-for='(list,index) in reviewed'  style='width:50px;height:50px;text-align:center;' >
                                    <span class='inline-block' :class='list==targetsChildId?"readBorder":""'  style='margin:8px 10px'>
                                        <img class='img-circle childImage cur-p'  onMouseOver="$(this).tooltip('show')"  data-toggle="tooltip" data-html='true' :title="`<div>${reviewChilds[list].name}</div>${reviewChilds[list].class_name}`"  data-placement="top" @click='childInfo(list)'  :src="reviewChilds[list].avatar" alt="">
                                    </span>
                                </div>
                                <div class='pull-left flex wechat' v-for='(list,index) in unreadList'  style='width:50px;height:50px;text-align:center;' >
                                    <span class='inline-block' :class='list==targetsChildId?"readBorder":""'  style='margin:8px 10px;' onMouseOver="$(this).tooltip('show')"  data-toggle="tooltip" data-html='true' :title="`<div>${reviewChilds[list].name}</div>${reviewChilds[list].class_name}`"  data-placement="top">
                                        <img class='img-circle childImage cur-p' style='opacity:0.4;'    @click='childInfo(list)' :src="reviewChilds[list].avatar" alt="">
                                    </span>
                                </div>
                            </div>
                            <div v-if='showChild==2'>
                                <div class='col-md-6 col-sm-6 p0' v-for='(list,index) in reviewed'>
                                    <div class="media mt10 readMedia mr15"  @click='childInfo(list)' :class='list==targetsChildId?"bgBlue":""' >
                                        <div class="media-left pull-left media-middle">
                                            <a href="javascript:void(0)">
                                                <img :src="reviewChilds[list].avatar" data-holder-rendered="true" class="childImage img-circle">
                                            </a>
                                        </div>
                                        <div class="media-body media-middle">
                                            <h4 class="media-heading font12 lineHeight">{{reviewChilds[list].name}}</h4>
                                        </div>
                                    </div>
                                </div>
                                <div class='col-md-6 col-sm-6 p0' v-for='(list,index) in unreadList' >
                                    <div class="media mt10 readMedia mr15"  @click='childInfo(list)'  :class='list==targetsChildId?"bgBlue":""'>
                                        <div class="media-left pull-left media-middle">
                                            <a href="javascript:void(0)">
                                                <img :src="reviewChilds[list].avatar" data-holder-rendered="true"  style='opacity:0.4;'  class="childImage img-circle">
                                            </a>
                                        </div>
                                        <div class="media-body media-middle">
                                            <h4 class="media-heading font12 lineHeight">{{reviewChilds[list].name}}</h4>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class='clearfix'></div>
            </div>
            </div>
        </div>
    </div>
    <!-- 添加收藏 -->
    <div class="modal fade" id="favoritesModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("directMessage", "Add to favorites"); ?></h4>
                </div>
                <div class="modal-body" >
                    <div class='color6 font14'><?php echo Yii::t("newDS", "Title"); ?></div>
                    <div class='color3 mt12 font14'>{{favoritesData.title}}</div>
                    <div class='color6 font14 mt24'><?php echo Yii::t("directMessage", "Favorite Memo (optional)"); ?></div>
                    <input class="form-control mt12" v-model='favoriteRemark'></input>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Cancel"); ?></button>
                    <button type="button" class="btn btn-primary" @click='saveFavorites'><?php echo Yii::t("global", "OK"); ?></button>
                </div>
            </div>
        </div>
    </div>
    <!-- 删除确认框 -->
    <div class="modal fade" id="delModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
        <div class="modal-dialog modal-sm" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel">
                        <span v-if='delType=="index"'><?php echo Yii::t("newDS", "Delete"); ?></span>
                        <span v-else><?php echo Yii::t("global", "Cancel"); ?></span>
                    </h4>
                </div>
                <div class="modal-body" >
                    <div v-if='delType=="favorites"'><?php echo Yii::t("directMessage", "Remove from favorites?"); ?></div>
                    <div v-if='delType=="top"'><?php echo Yii::t("directMessage", "Remove pin?"); ?></div>
                    <div v-if='delType=="draft"'><?php echo Yii::t("directMessage", "Proceed to remove this draft?"); ?></div>
                    <div v-if='delType=="index"'>
                        <div v-if='delStatus==10'><?php echo Yii::t("directMessage", "DM with comments from parents cannot be deleted, make it offline instead?"); ?></div>
                        <div v-else><?php echo Yii::t("directMessage", "This DM is already online, Proceed to delete?"); ?></div>
                    </div>
                    <div v-if='delType=="publish"'><?php echo Yii::t("directMessage", "Proceed to make it offline to Draft box?"); ?></div>
                    <div v-if='delType=="shared"'><?php echo Yii::t("directMessage", "确认删除吗？"); ?></div>

                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close"); ?></button>
                    <button type="button" v-if='delStatus==10' class="btn btn-primary" @click='journalOffline()'><?php echo Yii::t("workflow", "Offline"); ?></button>
                    <button type="button" v-else class="btn btn-primary" @click='delConfirm'><?php echo Yii::t("global", "OK"); ?></button>
                </div>
            </div>
        </div>
    </div>
    <!-- 授权列表 -->
    <div class="modal fade" id="authorizedModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel"  aria-labelledby="wantSayModalLabel"  data-backdrop="static">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("directMessage", "Authorization & Share"); ?></h4>
                </div>
                <div class="modal-body">
                    <ul class="nav nav-tabs mb20" role="tablist">
                        <li role="presentation"  :class="tabActive=='authorize'?'active':''" ><a href="javascript:;"  @click='tabActive="authorize";setAuthorize()'><?php echo Yii::t("directMessage", "Authorization"); ?></a></li>
                        <li role="presentation" :class="tabActive=='shared'?'active':''" ><a href="javascript:;" @click='tabActive="shared";setShare()'><?php echo Yii::t("directMessage", "Share"); ?></a></li>
                    </ul>
                    <div  class="tab-content overflow-y height200 scroll-box" :style="'max-height:'+(height-100)+'px'">
                        <div id='authorize'  role="tabpanel" v-if='Object.keys(authorizedList).length!=0 && tabActive=="authorize"'>
                            <div class='col-md-7 col-sm-12 borderRight'>
                                <p><?php echo Yii::t("directMessage", "Authorize to"); ?></p>
                                <el-alert
                                    title="<?php echo Yii::t("directMessage", "Who can use my signature"); ?>"
                                    type="info"
                                    :closable="false"
                                    show-icon>
                                </el-alert>
                                <div class='color6 font14 mt24'><?php echo Yii::t("global", "Search"); ?></div>
                                <div  class='flex mt16'>
                                    <el-select
                                        v-model="teacherUid"
                                        filterable
                                        remote
                                        clearable
                                        class='inline-input flex1 formControl'
                                        reserve-keyword
                                        placeholder="<?php echo Yii::t("directMessage", "Key words"); ?>"
                                        :remote-method="remoteMethod"
                                        prefix-icon="el-icon-search"
                                        :loading="loading">
                                        <el-option
                                            v-for="item in options"
                                            :key="item.uid"
                                            :label="item.name"
                                            class='optionSearch mb8'
                                            :value="item.uid">
                                            <div class="media">
                                                <div class="media-left pull-left media-middle">
                                                    <a href="javascript:void(0)">
                                                        <img :src="item.photoUrl" data-holder-rendered="true" class="media-object img-circle imageSearch">
                                                    </a>
                                                </div>
                                                <div class="media-body mt5 media-middle">
                                                    <h4 class="media-heading font14 color3 text_overflow">{{item.name}}</h4>
                                                    <div class="text-muted text_overflow font12">{{ item.hrPosition }}</div>
                                                </div>
                                            </div>
                                        </el-option>
                                    </el-select>
                                    <button type="button" class="btn btn-primary ml16"  :disabled='teacherUid==""?true:false' @click='confirmAuthorization'><?php echo Yii::t("directMessage", "Proceed the authorization"); ?></button>
                                </div>

                                <div class='color6 font14 mt24 mb16'><span><?php echo Yii::t("directMessage", "Authorized staffs:"); ?></span>  <span class="badge">{{Object.keys(authorizedList.authorizationList).length}}</span></div>
                                <div class='mb16' v-for='(list,key,index) in authorizedList.authorizationList'>
                                    <div class="media bgGrey authorizedMedia" >
                                        <div class="media-left pull-left media-middle">
                                            <a href="javascript:void(0)"><img :src="list.photoUrl" data-holder-rendered="true" class="contentAvatar"></a>
                                        </div>
                                        <div class="media-right pull-right pt12 text-right">
                                            <span class='el-icon-circle-close font16' @click='delAuthorized(key)'></span>
                                        </div>
                                        <div class="media-body pt4 media-middle">
                                            <div class="lineHeight20  text-primary">
                                                <span class='font14 color3 nowrap'>{{list.name}}</span>
                                            </div>
                                            <div class="font12 color6 nowrap">{{list.hrPosition}}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class='col-md-5 col-sm-12'>
                                <p><?php echo Yii::t("directMessage", "Authrorized to me:"); ?> <span class="badge">{{Object.keys(authorizedList.authorizedToList).length}}</span></p>
                                <el-alert
                                    title="<?php echo Yii::t("directMessage", "Signatures I can use"); ?>"
                                    type="info"
                                    :closable="false"
                                    show-icon>
                                </el-alert>
                                <div class='mt16' v-for='(list,key,index) in authorizedList.authorizedToList'>
                                    <div class="media" >
                                        <div class="media-left pull-left media-middle">
                                            <a href="javascript:void(0)"><img :src="list.photoUrl" data-holder-rendered="true" class="contentAvatar"></a>
                                        </div>
                                        <div class="media-body pt4 media-middle">
                                            <div class="lineHeight20  text-primary">
                                                <span class='font14 color3 nowrap'>{{list.name}}</span>
                                            </div>
                                            <div class="font12 color6 nowrap">{{list.hrPosition}}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class='clearfix'></div>
                        </div>
                        <div id='shared' role="tabpanel"  v-if='Object.keys(sharedList).length!=0 && tabActive=="shared"'>
                            <div class='col-md-7 col-sm-12 borderRight'>
                                <p><?php echo Yii::t("directMessage", "Share to"); ?></p>
                                <el-alert
                                        title="<?php echo Yii::t("directMessage", "Those who can view all my DMs"); ?>"
                                        type="info"
                                        :closable="false"
                                        show-icon>
                                </el-alert>
                                <div class='color6 font14 mt24'><?php echo Yii::t("global", "Search"); ?></div>
                                <div  class='flex mt16'>
                                    <el-select
                                            v-model="teacherUid"
                                            filterable
                                            remote
                                            clearable
                                            class='inline-input flex1 formControl'
                                            reserve-keyword
                                            placeholder="<?php echo Yii::t("directMessage", "Key words"); ?>"
                                            :remote-method="remoteMethod"
                                            prefix-icon="el-icon-search"
                                            :loading="loading">
                                        <el-option
                                                v-for="item in options"
                                                :key="item.uid"
                                                :label="item.name"
                                                class='optionSearch mb8'
                                                :value="item.uid">
                                            <div class="media">
                                                <div class="media-left pull-left media-middle">
                                                    <a href="javascript:void(0)">
                                                        <img :src="item.photoUrl" data-holder-rendered="true" class="media-object img-circle imageSearch">
                                                    </a>
                                                </div>
                                                <div class="media-body mt5 media-middle">
                                                    <h4 class="media-heading font14 color3 text_overflow">{{item.name}}</h4>
                                                    <div class="text-muted text_overflow font12">{{ item.hrPosition }}</div>
                                                </div>
                                            </div>
                                        </el-option>
                                    </el-select>
                                    <button type="button" class="btn btn-primary ml16"  :disabled='teacherUid==""?true:false' @click='confirmShare'><?php echo Yii::t("directMessage", "Proceed to share"); ?></button>
                                </div>

                                <div class='color6 font14 mt24 mb16'><span><?php echo Yii::t("directMessage", "Shared staffs:"); ?></span>  <span class="badge">{{Object.keys(sharedList.shareList).length}}</span></div>
                                <div class='mb16' v-for='(list,key,index) in sharedList.shareList'>
                                    <div class="media bgGrey authorizedMedia" >
                                        <div class="media-left pull-left media-middle">
                                            <a href="javascript:void(0)"><img :src="list.photoUrl" data-holder-rendered="true" class="contentAvatar"></a>
                                        </div>
                                        <div class="media-right pull-right pt12 text-right">
                                            <span class='el-icon-circle-close font16' @click='delShared(key)'></span>
                                        </div>
                                        <div class="media-body pt4 media-middle">
                                            <div class="lineHeight20  text-primary">
                                                <span class='font14 color3 nowrap'>{{list.name}}</span>
                                            </div>
                                            <div class="font12 color6 nowrap">{{list.hrPosition}}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class='col-md-5 col-sm-12'>
                                <p><?php echo Yii::t("directMessage", "Be shared:"); ?> <span class="badge">{{Object.keys(sharedList.sharedToList).length}}</span></p>
                                <el-alert
                                        title="<?php echo Yii::t("directMessage", "I can view all DMs of selected staffs"); ?>"
                                        type="info"
                                        :closable="false"
                                        show-icon>
                                </el-alert>
                                <div class='mt16' v-for='(list,key,index) in sharedList.sharedToList'>
                                    <div class="media" >
                                        <div class="media-left pull-left media-middle">
                                            <a href="javascript:void(0)"><img :src="list.photoUrl" data-holder-rendered="true" class="contentAvatar"></a>
                                        </div>
                                        <div class="media-body pt4 media-middle">
                                            <div class="lineHeight20  text-primary">
                                                <span class='font14 color3 nowrap'>{{list.name}}</span>
                                            </div>
                                            <div class="font12 color6 nowrap">{{list.hrPosition}}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class='clearfix'></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- 权限管理 -->
    <div class="modal fade" id="authorityManagementModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel"  aria-labelledby="wantSayModalLabel"  data-backdrop="static">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("directMessage", "Division Privacy Management"); ?></h4>
                </div>
                <div class="modal-body">
                    <ul class="nav nav-tabs mb20 col-sm-12" role="tablist" v-if='groupId!="OTHER"'>
                        <li role="presentation"  :class="DrmActive=='permissions'?'active':''" ><a href="javascript:;"  @click='DrmActive="permissions";setAuthorize()'><?php echo Yii::t("directMessage", "View All"); ?></a></li>
                        <li role="presentation" :class="DrmActive=='user'?'active':''" ><a href="javascript:;" @click='DrmActive="user";managerShare()'><?php echo Yii::t("directMessage", "Peer to peer share"); ?></a></li>
                    </ul>
                    <div class="tab-content overflow-y  scroll-box col-sm-12" :style="'max-height:'+(height-100)+'px'">
                        <div v-if='DrmActive=="permissions" && groupId!="OTHER"'>
                            <p class='color3 font14 mb10'><strong><?php echo Yii::t("directMessage", "Staffs with view-all privilege"); ?><?php echo Yii::t("directMessage", " ("); ?>{{groupTitle}}<?php echo Yii::t("directMessage", ")"); ?></strong></p>
                            <el-alert
                                    title="<?php echo Yii::t("directMessage", "With view-all privilege, staff can view all staff DMs of whole division"); ?>"
                                    type="info"
                                    :closable="false"
                                    show-icon>
                            </el-alert>
                            <div class='color6 font14 mt24'><?php echo Yii::t("global", "Search"); ?></div>
                            <div  class='flex mt16 mb16'>
                                <el-select
                                        v-model="teacherUid"
                                        filterable
                                        remote
                                        clearable
                                        class='inline-input flex1 formControl'
                                        reserve-keyword
                                        placeholder="<?php echo Yii::t("directMessage", "Staff Name"); ?>"
                                        :remote-method="remoteMethod"
                                        prefix-icon="el-icon-search"
                                        :loading="loading">
                                    <el-option
                                            v-for="item in options"
                                            :key="item.uid"
                                            :label="item.name"
                                            class='optionSearch mb8'
                                            :value="item.uid">
                                        <div class="media">
                                            <div class="media-left pull-left media-middle">
                                                <a href="javascript:void(0)">
                                                    <img :src="item.photoUrl" data-holder-rendered="true" class="media-object img-circle imageSearch">
                                                </a>
                                            </div>
                                            <div class="media-body mt5 media-middle">
                                                <h4 class="media-heading font14 color3 text_overflow">{{item.name}}</h4>
                                                <div class="text-muted text_overflow font12">{{ item.hrPosition }}</div>
                                            </div>
                                        </div>
                                    </el-option>
                                </el-select>
                                <button type="button" class="btn btn-primary ml16"  :disabled='teacherUid==""?true:false' @click='confirmManager'><?php echo Yii::t("directMessage", "Add"); ?></button>
                            </div>
                            <div class="row row-no-gutters">
                                <div class='col-xs-6 pb16'   v-for='(list,index) in managerList.adminIds'>
                                    <div class="media bgGrey authorizedMedia" >
                                        <div class="media-left pull-left media-middle">
                                            <a href="javascript:void(0)">
                                                <img :src="managerList.staffInfo[list].photoUrl" data-holder-rendered="true" class="contentAvatar">
                                            </a>
                                        </div>
                                        <div class="media-body pt4 media-middle">
                                            <div class="lineHeight20  text-primary">
                                                <span class='font14 color3 nowrap'>{{managerList.staffInfo[list].name}}</span>
                                            </div>
                                            <div class="font12 color6 nowrap">{{managerList.staffInfo[list].hrPosition}}</div>
                                        </div>
                                    </div>
                                </div>
                                <div class='col-xs-6 pb16'   v-for='(list,index) in managerList.managerStaffIds'>
                                    <div class="media bgGrey authorizedMedia" >
                                        <div class="media-left pull-left media-middle">
                                            <a href="javascript:void(0)">
                                                <img :src="managerList.staffInfo[list].photoUrl" data-holder-rendered="true" class="contentAvatar">
                                            </a>
                                        </div>
                                        <div class="media-right pull-right pt12 text-right">
                                            <span class='el-icon-circle-close font16' @click='delManager(list)'></span>
                                        </div>
                                        <div class="media-body pt4 media-middle">
                                            <div class="lineHeight20  text-primary">
                                                <span class='font14 color3 nowrap'>{{managerList.staffInfo[list].name}}</span>
                                            </div>
                                            <div class="font12 color6 nowrap">{{managerList.staffInfo[list].hrPosition}}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div v-if='DrmActive=="user"'>
                            <div class="row" v-if='managerShareData.staffInfo' style='min-height:200px'>
                                <div>
                                    <div class=' col-xs-4'>
                                        <input type="text" class="form-control" v-model='managerSearch' placeholder="<?php echo Yii::t("directMessage", "Select a staff"); ?>" >
                                    </div>
                                    <div class='clearfix'></div>
                                </div>
                                <div class="col-xs-4 mt10" >
                                    <div class="checkbox color3 font14 mb10" v-if='managerSearch==""'>
                                        <label>
                                            <input type="checkbox" v-model='showShareData'> <?php echo Yii::t("directMessage", "Only display shared staffs"); ?>
                                        </label>
                                    </div>
                                    <div class=" overflow-y  scroll-box" :style="'max-height:'+(height-200)+'px'" v-if='managerStaffInfo.length!=0'>
                                        <div class="bgGrey" v-for='(list,index) in managerStaffInfo' :class='index==showManagerIndex?"activeShare":""'>
                                            <div class='font14 nowrap cur-p' @click='showManager(list,index)'>{{list.name}}</div>
                                        </div>
                                    </div>
                                    <div v-else>
                                        <div class="alert alert-warning" role="alert"><?php echo Yii::t('ptc', 'No Data'); ?></div>
                                    </div>
                                </div>
                                <div class="col-xs-8 mt10" v-if='showManagerStaff!=""'>
                                    <div class="row" style="margin-bottom: 15px"  >
                                        <div class="col-xs-6 media">
                                            <div class="media-left pull-left media-middle">
                                                <a href="javascript:void(0)">
                                                    <img :src="managerShareData.staffInfo[showManagerStaff.uid].photoUrl" data-holder-rendered="true" class="contentAvatar">
                                                </a>
                                            </div>
                                            <div class="media-body pt4 media-middle">
                                                <div class="lineHeight20  text-primary">
                                                    <span class='font14 color3 nowrap'>{{managerShareData.staffInfo[showManagerStaff.uid].name}}</span>
                                                </div>
                                                <div class="font12 color6 nowrap">{{managerShareData.staffInfo[showManagerStaff.uid].hrPosition}}</div>
                                            </div>
                                        </div>
                                        <div class="col-xs-3">
                                            <div class="lineHeight20  text-primary text-center">
                                                <span class='font14 color3 nowrap'>{{managerShareData.shareData[showManagerStaff.uid]?managerShareData.shareData[showManagerStaff.uid].shareTo.length:0}}</span>
                                            </div>
                                            <div class="font12 color6 nowrap text-center"><?php echo Yii::t("directMessage", "Shared to"); ?></div>
                                        </div>
                                        <div class="col-xs-3">
                                            <div class="lineHeight20  text-primary text-center">
                                                <span class='font14 color3 nowrap'>{{managerShareData.shareData[showManagerStaff.uid]?managerShareData.shareData[showManagerStaff.uid].shared.length:0}}</span>
                                            </div>
                                            <div class="font12 color6 nowrap text-center"><?php echo Yii::t("directMessage", "Be shared"); ?></div>
                                        </div>
                                    </div>
                                    <div class='flex managerBorder'>
                                        <div class='flex1 managerBorderRight p16'>
                                            <div class='mb15 flex'>
                                                <div class='flex1 color3 font14'> <?php echo Yii::t("directMessage", "Shared to"); ?></div>
                                                <span class='text-primary cur-p'  @click='addSharedList("shareTo")'><span class='glyphicon glyphicon-pencil'></span> <?php echo Yii::t("global", "Add"); ?> </span>
                                            </div>
                                            <div v-if='managerShareData.shareData[showManagerStaff.uid]'>
                                                <div class="media pt8 mt0" v-for='(list,index) in managerShareData.shareData[showManagerStaff.uid].shareTo'>
                                                    <div class="media-left pull-left media-middle">
                                                        <a href="javascript:void(0)">
                                                            <img :src="managerShareData.staffInfo[list].photoUrl" data-holder-rendered="true" class="postObject">
                                                        </a>
                                                    </div>
                                                    <div class="media-right pull-right">
                                                        <span class='el-icon-circle-close color9 font16 pt5 cur-p' @click='delSharedList("shareTo",managerShareData.staffInfo[list].uid,index)'></span>
                                                    </div>
                                                    <div class="media-body pt4 media-middle">
                                                        <div class="color6">{{managerShareData.staffInfo[list].name}}</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class='flex1 p16'>
                                            <div class='mb15 flex'>
                                                <div class='flex1 color3 font14'> <?php echo Yii::t("directMessage", "Be shared"); ?></div>
                                                <span class='text-primary cur-p' @click='addSharedList("shared")'><span class='glyphicon glyphicon-pencil'></span> <?php echo Yii::t("global", "Add"); ?> </span>
                                            </div>
                                            <div v-if='managerShareData.shareData[showManagerStaff.uid]'>
                                                <div class="media pt8 mt0" v-for='(list,index) in managerShareData.shareData[showManagerStaff.uid].shared'>
                                                    <div class="media-left pull-left media-middle">
                                                        <a href="javascript:void(0)">
                                                            <img :src="managerShareData.staffInfo[list].photoUrl" data-holder-rendered="true" class="postObject">
                                                        </a>
                                                    </div>
                                                    <div class="media-right pull-right">
                                                        <span class='el-icon-circle-close font16 color9 pt5 cur-p'@click='delSharedList("shared",managerShareData.staffInfo[list].uid,index)'></span>
                                                    </div>
                                                    <div class="media-body pt4 media-middle">
                                                        <div class="color6">{{managerShareData.staffInfo[list].name}}</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- 添加共享 -->
    <div class="modal fade" id="addSharedModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content" @click='showSelect=false' >
                <div class="modal-header"  >
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("teaching", "添加共享"); ?></h4>
                </div>
                <div class="modal-body" style='min-height:200px' v-if='managerShareData.shareData' >
                    <div class="media" >
                        <div class="media-left pull-left media-middle">
                            <a href="javascript:void(0)">
                                <img :src="showManagerStaff.photoUrl" data-holder-rendered="true" class="contentAvatar">
                            </a>
                        </div>
                        <div class="media-body pt4 media-middle">
                            <div class="lineHeight20  text-primary">
                                <span class='font14 color3 nowrap'>{{showManagerStaff.name}}</span>
                            </div>
                            <div class="font12 color6 nowrap">{{showManagerStaff.hrPosition}}</div>
                        </div>
                    </div>
                    <div class='color6 font14 mt24'><?php echo Yii::t("global", "Search"); ?></div>
                    <div  class='flex mt16 mb16'>
                        <el-select
                                v-model="teacherUid"
                                filterable
                                remote
                                clearable
                                class='inline-input flex1 formControl'
                                reserve-keyword
                                placeholder="<?php echo Yii::t("directMessage", "Key words"); ?>"
                                :remote-method="remoteMethod"
                                prefix-icon="el-icon-search"
                                :loading="loading">
                            <el-option
                                    v-for="item in options"
                                    :key="item.uid"
                                    :label="item.name"
                                    class='optionSearch mb8'
                                    :value="item.uid">
                                <div class="media">
                                    <div class="media-left pull-left media-middle">
                                        <a href="javascript:void(0)">
                                            <img :src="item.photoUrl" data-holder-rendered="true" class="media-object img-circle imageSearch">
                                        </a>
                                    </div>
                                    <div class="media-body mt5 media-middle">
                                        <h4 class="media-heading font14 color3 text_overflow">{{item.name}}</h4>
                                        <div class="text-muted text_overflow font12">{{ item.hrPosition }}</div>
                                    </div>
                                </div>
                            </el-option>
                        </el-select>
                        <button type="button" class="btn btn-primary ml16"  :disabled='teacherUid==""?true:false' @click='confirmSharedList'><?php echo Yii::t("directMessage", "Proceed to share"); ?></button>
                    </div>
                    <div class="row row-no-gutters" v-if='managerShareData.shareData[showManagerStaff.uid] && sharedType!=""'>
                        <div class='col-xs-6 pb16'   v-for='(list,index) in managerShareData.shareData[showManagerStaff.uid][sharedType]'>
                            <div class="media bgGrey authorizedMedia" >
                                <div class="media-left pull-left media-middle">
                                    <a href="javascript:void(0)">
                                        <img :src="managerShareData.staffInfo[list].photoUrl" data-holder-rendered="true" class="contentAvatar">
                                    </a>
                                </div>
                                <div class="media-right pull-right pt12 text-right">
                                    <span class='el-icon-circle-close font16' @click='delSharedList(sharedType,list,index)'></span>
                                </div>
                                <div class="media-body pt4 media-middle">
                                    <div class="lineHeight20  text-primary">
                                        <span class='font14 color3 nowrap'>{{managerShareData.staffInfo[list].name}}</span>
                                    </div>
                                    <div class="font12 color6 nowrap">{{managerShareData.staffInfo[list].hrPosition}}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- 切换老师 -->
    <div class="modal fade" id="teacherTabModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content" @click='showSelect=false' >
                <div class="modal-header"  >
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("teaching", "Teacher"); ?></h4>
                </div>
                <div class="modal-body" style='min-height:200px'>
                    <div class='flex'>
                        <div class="relative ">
                            <div class="inputSelect" >
                                <input type="text" class="form-control pl30" id="exampleInputAmount" placeholder="<?php echo Yii::t("attends", "Input name to filter"); ?>" v-model="searchTeacher" @click.stop="showFocus">
                            </div>
                            <div class='selectTeacher' v-if='showSelect'>
                                <div v-if='teacherSearchData.length==0'>
                                <p class='font14 color3 text-center mt20 color9'><?php echo Yii::t('ptc', 'No Data'); ?></p>
                                </div>
                                <div v-else>
                                    <div  v-for='(list,index) in teacherSearchData'>
                                        <div class="selectName font14 color3 cur-p" @click.stop="handleSelect(list)">
                                            {{list.name}} <span v-if='list.level==0' class="label label-default color6 font12 tagLabel ml5"><?php echo Yii::t('newDS', 'Resigned'); ?></span></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <span  class='flex1'></span>
                    </div>
                    <hr >
                    <div name='teacherId'>
                        <a class='mb5 btn btn-default mr10'  href='javascript:;' @click.stop='tabTeacher(staffSelf.uid)'>
                            {{staffSelf.name}} <span class="label label-primary ml4" style='top:0'>&nbsp;<?php echo Yii::t("directMessage", "Me"); ?>&nbsp;</span>
                        </a>
                        <a v-for='(list,index) in normalList'  class='mb5 btn btn-default mr10'  href='javascript:;' @click.stop='tabTeacher(list.uid)'>
                            {{list.name}}
                        </a>
                    </div>
                    <div v-if='resignList.length!=0'>
                        <h4><?php echo Yii::t('newDS', 'Resigned'); ?></h4>
                        <div name='teacherId'>
                            <a v-for='(list,index) in resignList'  class='mb5 btn btn-default mr10' href='javascript:;' @click.stop='tabTeacher(list.uid)'>
                                {{list.name}}
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
     <!-- 直接克隆 -->
    <div class="modal fade" id="cloneContentModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("directMessage", "Clone from other DM"); ?></h4>
                </div>
                <div class="modal-body "  v-if='contentData.journalData' >
                    <div class='col-md-8 col-sm-8 borderRight overflow-y'  style='min-height:400px'  ref='content' :style="'height:'+height+'px'">
                        <div class='pt12'><span class="label label-default color6 tagLabel "><?php echo Yii::t("newDS", "School Year"); ?>：{{contentData.journalData.start_year}}-{{contentData.journalData.start_year+1}}</span></div>
                        <div class='font20 color3 mt24'><label>{{contentData.journalData.title}}</label></div>
                        <div class="media mt24">
                            <div class="media-left pull-left media-middle">
                                <a href="javascript:void(0)">
                                    <img class="contentAvatar" :src="contentData.userInfo[contentData.journalData.sign_as_uid].photoUrl" data-holder-rendered="true" >
                                </a>
                            </div>
                            <div class="media-body pt8 media-middle">
                                <h4  class="media-heading font12">{{contentData.userInfo[contentData.journalData.sign_as_uid].name}}<span class="label label-default color6 tagLabel  ml5"><?php echo Yii::t("directMessage", "Signature"); ?></span></h4>
                                <div class='text-muted'>{{contentData.journalData.sign_as_title}}</div>
                            </div>
                        </div>
                        <div class='mt24 breakAll' v-html='contentData.journalData.content'></div>
                        <div class='mt24'  v-if='contentData.journalData.attachments.length!=0'>
                            <p class='color3 font14'><strong><?php echo Yii::t("curriculum", "Attachments"); ?></strong> <span class="badge ml5">{{contentData.journalData.attachments.length}}</span></p>
                            <div v-for='(list,index) in contentData.journalData.attachments' class='mt16' style='word-break:break-all;'>
                                <a :href="list.file_key" target='_blank'><span class='glyphicon glyphicon-paperclip mr4'></span>{{list.title}}</a>
                            </div>
                        </div>
                        <div class='mt24'>
                            <p class='color3 font14'><strong><?php echo Yii::t("newDS", "Subscribers"); ?></strong> <span class="badge ml5">{{contentData.journalData.targets_num}}</span></p>
                            <div class='mt16' v-if='contentData.journalData.targets.length>6'>
                                <el-tooltip placement="top" v-for='(list,index) in contentData.journalData.targets' >
                                    <div slot="content">{{contentData.childData[list].name}}
                                        <div>{{contentData.childData[list].className}}</div>
                                    </div>
                                    <img class="contentPostObject mr16 mb8" :src="contentData.childData[list].avatar" data-holder-rendered="true" >
                                </el-tooltip>
                                <span class='postMore cur-p' v-if='contentData.journalData.targets_num>10' @click='showMore(contentData.journalData)'>+ {{contentData.journalData.targets_num-10}} ></span>
                            </div>
                            <div class='mt16' v-else>
                                <div class="media mt24"  v-for='(list,index) in contentData.journalData.targets'>
                                    <div class="media-left pull-left media-middle">
                                        <a href="javascript:void(0)">
                                            <img :src="contentData.childData[list].avatar" data-holder-rendered="true" class="contentPostObject">
                                        </a>
                                    </div>
                                    <div class="media-body pt4 media-middle">
                                        <h4 class="media-heading font12">{{contentData.childData[list].name}}</h4>
                                        <div class="text-muted">{{contentData.childData[list].className}}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class='mt24' v-if='contentData.journalData.joint_admins.length!=0'>
                            <p class='color3 font14'><strong><?php echo Yii::t("directMessage", "Collaboration"); ?></strong> <span class="badge ml5">{{contentData.journalData.joint_admins.length}}</span></p>
                                <div v-for='(list,index) in contentData.journalData.joint_admins' :class='indexList.otherTeacherId!=0?"col-md-6 col-sm-6":"col-md-12 col-sm-12"' class='mt8 mb8' >
                                    <div class="media">
                                        <div class="media-left pull-left media-middle">
                                            <a href="javascript:void(0)">
                                                <img class="contentAvatar" :src="contentData.userInfo[list].photoUrl" data-holder-rendered="true" >
                                            </a>
                                        </div>
                                        <div class="media-body pt8 media-middle">
                                            <h4  class="media-heading font12">{{contentData.userInfo[list].name}}</h4>
                                            <div class='text-muted'>{{contentData.userInfo[list].hrPosition}}</div>
                                        </div>
                                    </div>
                                </div>
                        </div>

                    </div>
                    <div class='col-md-4 col-sm-4 cloneBg' ref='comment' :style="'height:'+cloneContentHeight+'px'"  v-if='contentData.journalData'>
                        <div class='color9  pt12 mb12'><?php echo Yii::t("directMessage", "Clone options"); ?></div>
                        <div class="checkbox mt8">
                            <label>
                                <input type="checkbox"  v-model='keepTargets'><?php echo Yii::t("directMessage", "sync subscribers"); ?>
                            </label>
                        </div>
                        <div class="checkbox mt8">
                            <label>
                                <input type="checkbox" :disabled='contentData.journalData.joint_admins.length==0?true:false'  v-model='keepJointAdmins'><?php echo Yii::t("directMessage", "sync reply collaborators"); ?>
                            </label>
                        </div>
                        <div class="checkbox mt8">
                            <label>
                                <input type="checkbox" :disabled='contentData.journalData.attachments.length==0?true:false'   v-model='keepAttachments'><?php echo Yii::t("directMessage", "sync attachements"); ?>
                            </label>
                        </div>
                        <button type="button" class="btn btn-primary btn-lg btn-block mt8" @click='cloneCopy'><?php echo Yii::t("directMessage", "Clone"); ?></button>
                    </div>
                    <div class='clearfix'></div>
                </div>
            </div>
        </div>
    </div>
    <!-- 已有内容克隆 -->
    <div class="modal fade" id="cloneAllModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("directMessage", "Clone from other DM"); ?></h4>
                </div>
                <div class="modal-body p0 ">
                    <div class='col-md-8 col-sm-8 borderRight  pb20' :class='cloneLoading?"overflowHidden":"overflow-y"'  :style="'height:'+height+'px'"   ref='content' >
                        <div class='loading'  v-if='cloneLoading'>
                            <span></span>
                        </div>
                        <div v-if='contentData.journalData'>
                            <div class='pt12'><span class="label label-default color6 tagLabel "><?php echo Yii::t("newDS", "School Year"); ?>：{{contentData.journalData.start_year}}-{{contentData.journalData.start_year+1}}</span></div>
                            <div class='font20 color3 mt24'><label>{{contentData.journalData.title}}</label></div>
                            <div class="media mt24">
                                <div class="media-left pull-left media-middle">
                                    <a href="javascript:void(0)">
                                        <img class="contentAvatar" :src="contentData.userInfo[contentData.journalData.sign_as_uid].photoUrl" data-holder-rendered="true" >
                                    </a>
                                </div>
                                <div class="media-body pt8 media-middle">
                                    <h4  class="media-heading font12">{{contentData.userInfo[contentData.journalData.sign_as_uid].name}}<span class="label label-default color6 tagLabel  ml5"><?php echo Yii::t("directMessage", "Signature"); ?></span></h4>
                                    <div class='text-muted'>{{contentData.journalData.sign_as_title}}</div>
                                </div>
                            </div>
                            <div class='mt24 breakAll' v-html='contentData.journalData.content'></div>
                            <div class='mt24'  v-if='contentData.journalData.attachments.length!=0'>
                                <p class='color3 font14'><strong><?php echo Yii::t("curriculum", "Attachments"); ?></strong> <span class="badge ml5">{{contentData.journalData.attachments.length}}</span></p>
                                <div v-for='(list,index) in contentData.journalData.attachments' class='mt16' style='word-break:break-all;'>
                                    <a :href="list.file_key" target='_blank'><span class='glyphicon glyphicon-paperclip mr4'></span>{{list.title}}</a>
                                </div>
                            </div>
                            <div class='mt24'>
                                <p class='color3 font14'><strong><?php echo Yii::t("newDS", "Subscribers"); ?></strong> <span class="badge ml5">{{contentData.journalData.targets_num}}</span></p>
                                <div class='mt16' v-if='contentData.journalData.targets.length>6'>
                                    <el-tooltip placement="top" v-for='(list,index) in contentData.journalData.targets' >
                                        <div slot="content">{{contentData.childData[list].name}}
                                            <div>{{contentData.childData[list].className}}</div>
                                        </div>
                                        <img class="contentPostObject mr16 mb8" :src="contentData.childData[list].avatar" data-holder-rendered="true" >
                                    </el-tooltip>
                                    <span class='postMore cur-p' v-if='contentData.journalData.targets_num>10' @click='showMore(contentData.journalData)'>+ {{contentData.journalData.targets_num-10}} ></span>
                                </div>
                                <div class='mt16' v-else>
                                    <div class="media mt24"  v-for='(list,index) in contentData.journalData.targets'>
                                        <div class="media-left pull-left media-middle">
                                            <a href="javascript:void(0)">
                                                <img :src="contentData.childData[list].avatar" data-holder-rendered="true" class="contentPostObject">
                                            </a>
                                        </div>
                                        <div class="media-body pt4 media-middle">
                                            <h4 class="media-heading font12">{{contentData.childData[list].name}}</h4>
                                            <div class="text-muted">{{contentData.childData[list].className}}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class='mt24'  v-if='contentData.journalData.joint_admins.length!=0'>
                                <p class='color3 font14'><strong><?php echo Yii::t("directMessage", "Collaboration"); ?></strong> <span class="badge ml5">{{contentData.journalData.joint_admins.length}}</span></p>
                                <div v-for='(list,index) in contentData.journalData.joint_admins' :class='indexList.otherTeacherId!=0?"col-md-6 col-sm-6":"col-md-12 col-sm-12"' class='mt8 mb8' >
                                    <div class="media">
                                        <div class="media-left pull-left media-middle">
                                            <a href="javascript:void(0)">
                                                <img class="contentAvatar" :src="contentData.userInfo[list].photoUrl" data-holder-rendered="true" >
                                            </a>
                                        </div>
                                        <div class="media-body pt8 media-middle">
                                            <h4  class="media-heading font12">{{contentData.userInfo[list].name}}</h4>
                                            <div class='text-muted'>{{contentData.userInfo[list].hrPosition}}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                    <div class='col-md-4 col-sm-4 cloneBg pb20' ref='comment'>
                        <div >
                            <div class='tab_List mb8'>
                                <div class='el-tabs__item' :class='activeName=="first"?"el-tabsBto":""' @click="handleClick('first')">
                                    <span><?php echo Yii::t("directMessage", "Favorites"); ?></span>
                                </div>
                                <span class='el-tabs__item' :class='activeName=="second"?"el-tabsBto":""' @click="handleClick('second')"><?php echo Yii::t("directMessage", "Recent Published"); ?></span>
                            </div>
                            <div class='overflow-y'  :style="'height:'+(height-208)+'px'" >
                                <div v-show='activeName=="first"'>
                                    <div v-if='favoriteList.length!=0'>
                                        <div v-for='(list,index) in favoriteList' @click='showCloneData(list,index,"first")' class='titleList title mb10' :class='activeIndex=="first"+index?"titleListHover":""'>
                                            <div class='font14 nowrap'>{{list.journal_title}}</div>
                                            <div class='mt4 font12 nowrap'>{{list.favorite_title}}</div>
                                        </div>
                                    </div>
                                    <div v-else>
                                        <el-empty description="<?php echo Yii::t("ptc", "No Data"); ?>" :image-size="100"></el-empty>
                                    </div>
                                </div>
                                <div v-show='activeName=="second"'>
                                    <div v-if='latestList.length!=0'>
                                        <p v-for='(list,index) in latestList'  @click='showCloneData(list,index,"second")' :class='activeIndex=="second"+index?"titleListHover":""' class='titleList'>{{list.title}}</p>
                                    </div>
                                    <div v-else>
                                        <el-empty description="<?php echo Yii::t("ptc", "No Data"); ?>"  :image-size="100"></el-empty>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div v-if='contentData.journalData'>
                            <div class='color9  pt12 mb12'><?php echo Yii::t("directMessage", "Clone options"); ?></div>
                            <div class="checkbox mt8">
                                <label>
                                    <input type="checkbox"  v-model='keepTargets' :disabled='activeIndex==null?true:false'><?php echo Yii::t("directMessage", "sync subscribers"); ?>
                                </label>
                            </div>
                            <div class="checkbox mt8">
                                <label>
                                    <input type="checkbox"   v-model='keepJointAdmins'  :disabled='activeIndex==null || contentData.journalData.joint_admins.length==0?true:false'><?php echo Yii::t("directMessage", "sync reply collaborators"); ?>
                                </label>
                            </div>
                            <div class="checkbox mt8">
                                <label>
                                    <input type="checkbox"   v-model='keepAttachments'  :disabled='activeIndex==null || contentData.journalData.attachments.length==0?true:false'><?php echo Yii::t("directMessage", "sync attachements"); ?>
                                </label>
                            </div>
                            <button type="button" class="btn btn-primary btn-lg btn-block mt8" @click='cloneCopy'  :disabled='activeIndex==null?true:false'><?php echo Yii::t("directMessage", "Clone"); ?></button>
                        </div>
                    </div>
                    <div class='clearfix'></div>
                </div>
            </div>
        </div>
    </div>
    <!-- 二维码 -->
    <div class="modal fade" id="qrcodeModal" tabindex="-1" role="dialog"  >
        <div class="modal-dialog" role="document">
            <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="parentCommentsModalLabel"><?php echo Yii::t("newDS", "Qr-codes list"); ?></h4>
            </div>
            <div class="modal-body" style='min-height:200px'>
                <div class='loading'  v-if='qrcodeLoading'>
                    <span></span>
                </div>
                <div v-if='previewQrcode!=""'>
                    <div class='col-md-6 col-sm-6 text-center'>
                        <p><strong><?php echo Yii::t("newDS", "For Preview Only"); ?></strong> </p>
                        <div><?php echo Yii::t("newDS", "Don't Share"); ?></div>
                        <div class='mt20'>
                            <img class='qrcodeImg' :src="previewQrcode" alt="">
                        </div>
                        <div class='mt15 mb20'>
                            <button type="button" class="btn btn-primary"  @click='shareImg("view")'><?php echo Yii::t("reg", "Beautify this QrCode"); ?></button>
                        </div>
                    </div>
                    <div class='col-md-6 col-sm-6 text-center'>
                        <p><strong><?php echo Yii::t("newDS", "For Parent Sharing"); ?></strong></p>
                        <div><?php echo Yii::t("newDS", "Need Parent Identity"); ?></div>
                        <div class='mt20'>
                            <img class='qrcodeImg'  :src="shareQrcode" alt="">
                        </div>
                        <div class='mt15 mb20'>
                            <button type="button" class="btn btn-primary" @click='shareImg("parent")'><?php echo Yii::t("reg", "Beautify this QrCode"); ?></button>
                        </div>
                    </div>
                    <div class='clearfix'></div>
                </div>

            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close"); ?></button>
            </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="shareModal" tabindex="-1" role="dialog" aria-labelledby="modal" aria-hidden="false">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="parentCommentsModalLabel"><?php echo Yii::t("reg", "QrCode template"); ?></h4>
            </div>
            <div class="modal-body">
                <?php $this->renderPartial("share");?>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close"); ?></button>
            </div>
            </div>
        </div>
    </div>
    <!-- //移交 -->
    <div class="modal fade" id="forwardModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel" data-backdrop="static">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("directMessage", "Handover");?></h4>
            </div>
            <div class="modal-body p24" v-if='commentChildId!=""'>
                <div class='row'>
                    <div class='col-md-6 borderRight' style='min-height:480px' v-if='inputForward.length!=0'>
                        <div><label class='font14'><?php echo Yii::t("directMessage", "Handover Content");?></label></div>
                        <div class='color6'><?php echo Yii::t("directMessage", "Multiple messages will be merged.");?></div>
                        <div class='flex mt15'>
                            <div>
                                <img :src="oldCommentChild[commentChildId].avatar" height="50" width="50" class="img-circle" style="object-fit: cover;">
                            </div>
                            <div class='flex1 ml15'>
                                <div class='color3 font14 mt5'>{{oldCommentChild[commentChildId].name}}</div>
                                <div class='color6 mt5'>{{oldCommentChild[commentChildId].class_name}}</div>
                                <div class='forwardText mt20'>
                                    <div v-for='(item,index) in inputForward' class=''>
                                        <div class='color3 font14 mb12' v-html='html(commentData.items[item].content)'></div>
                                        <div>
                                            <ul class='mb12 imgLi' :id='commentData.items[item].id' v-if='commentData.items[item].imgUrl.length!=0'>
                                                <li v-for='(list,j) in commentData.items[item].imgUrl'>
                                                    <img :src="list.url" class='fileImg mr8' @click='showImg(commentData.items[item])' alt=""  >
                                                </li>
                                            </ul>
                                            <div  v-if='commentData.items[item].videoUrl.length!=0'>
                                                <div v-for="(list, j) in commentData.items[item].videoUrl" :key="j">
                                                    <div v-if="list.video_convert_status == 0" class="flex fileLink">
                                                        <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/link.png' ?>" alt="" class="pdfImg" /> 
                                                        <a class='flex1 ml5' target= "_blank" :href="list.url"><?php echo Yii::t('message', "Convert") ?></a>
                                                    </div>
                                                    <video v-else :src="list.url" :poster="list.video_cover"  controls muted preload="auto" style="width: 100%; height: 100%; fit: fill"></video>
                                                </div>
                                            </div>
                                            <div >
                                                <div class='flex fileLink' v-for='(list,j) in commentData.items[item].pdfUrl'>
                                                    <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/PDF1.png' ?>" alt="" class="pdfImg" v-if='list.mimetype=="application/pdf"' />
                                                    <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/excel.png' ?>" alt="" class="pdfImg" v-else-if='list.mimetype=="application/vnd.ms-excel" || list.mimetype=="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"' />
                                                    <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/word1.png' ?>" alt="" class="pdfImg" v-else-if='list.mimetype=="application/msword" || list.mimetype=="application/vnd.openxmlformats-officedocument.wordprocessingml.document"' />
                                                    <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/zip.png' ?>" alt="" class="pdfImg" v-else-if='list.mimetype=="application/x-zip-compressed"' />
                                                    <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/link.png' ?>" alt="" class="pdfImg" v-else /> 
                                                    <a class='flex1 ml5' target= "_blank" :href="list.url">{{list.title}}
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class='col-md-6'>
                        <div class='mt16 mb16'>
                            <label class="radio-inline">
                                <input type="radio" v-model="forwardType" value="3"> <?php echo Yii::t("directMessage", "Handover to staff");?> 
                            </label>
                            <label class="radio-inline">
                                <input type="radio"  v-model="forwardType" value="4"> <?php echo Yii::t("directMessage", "Handover to a team");?>
                            </label>
                        </div>
                        <div class='mb20' v-if='forwardType==3'>
                            <el-select v-model="forwardTo" placeholder="<?php echo Yii::t("directMessage", "Handover to staff");?> " class='forwardOption mb8'>
                                <el-option
                                    v-for="item in teacherList"
                                    :key="item.uid"
                                    class='forwardOption mb8'
                                    :label="item.name"
                                    :value="item.uid">
                                    <div class="media">
                                        <div class="media-left pull-left media-middle">
                                            <a href="javascript:void(0)">
                                                <img :src="item.photoUrl" data-holder-rendered="true" class="media-object img-circle contentAvatar">
                                            </a>
                                        </div>
                                        <div class="media-body mt5 media-middle">
                                            <h4 class="media-heading font14 color3 text_overflow">{{item.name}}</h4>
                                            <div class="color6  text_overflow font12">{{ item.hrPosition }}</div>
                                        </div>
                                    </div>
                                </el-option>
                            </el-select>
                        </div>
                        <div class='mb20' v-if='forwardType==4'>
                            <el-select v-model="forwardTo" placeholder="<?php echo Yii::t("directMessage", "Handover to a team");?>" class='forwardOption mb8'>
                                <el-option
                                    class='forwardOption mb8'
                                    v-for="item in deptList"
                                    :key="item.id"
                                    :label="item.name"
                                    :value="item.id">
                                    <h4 class="media-heading font14 color3 text_overflow">{{item.name}}</h4>
                                </el-option>
                            </el-select>
                        </div>
                        <div>
                            <div class='mb16'><span class='font14 color3'><?php echo Yii::t("labels", "Memo");?></span> <span class='color6 font12 ml10'><?php echo Yii::t("directMessage", "Only handover target can see the memo");?></span></div>
                            <textarea class="form-control" rows="3" v-model='forwardMark'></textarea>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                <button type="button" class="btn btn-primary" :disabled='btnDis' @click='repost'><?php echo Yii::t("directMessage", "Confirm to handover");?></button>
            </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="originalInfoModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("directMessage", "Original Information");?></h4>
            </div>
            <div class="modal-body p24" >
                <div class='flex mt15' v-if='commentChildId!=""'>
                    <div>
                        <img :src="oldCommentChild[commentChildId].avatar" height="50" width="50" class="img-circle" style="object-fit: cover;">
                    </div>
                    <div class='flex1 ml15'>
                        <div class='color3 font14 mt5'>{{oldCommentChild[commentChildId].name}}</div>
                        <div class='color6 mt5'>{{oldCommentChild[commentChildId].class_name}}</div>
                        <div class='forwardText mt20'>
                            <div v-for='(list,index) in repostDetailList.commentList' class=' pb16 pt16' :class='repostDetailList.commentList.length!=(index+1)?"borderBto":""'>
                                <div class='color3 font14 mb12' v-html='html(list.content)'></div>
                                <div>
                                    <ul class='mb12 imgLi' :id='list.link_id' v-if='list.imgUrl.length!=0'>
                                        <li v-for='(item,j) in list.imgUrl'>
                                            <img :src="item.url" class='fileImg mr8' @click='showImg(list)' alt=""  >
                                        </li>
                                    </ul>
                                    <div  v-if='list.videoUrl.length!=0'>
                                        <div v-for="(item, j) in list.videoUrl" :key="j">
                                            <div v-if="item.video_convert_status == 0" class="flex fileLink">
                                                <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/link.png' ?>" alt="" class="pdfImg" /> 
                                                <a class='flex1 ml5' target= "_blank" :href="item.url"><?php echo Yii::t('message', "Convert") ?></a>
                                            </div>
                                            <video v-else :src="item.url" :poster="item.video_cover"  controls muted preload="auto" style="width: 100%; height: 100%; fit: fill"></video>
                                        </div>
                                    </div>
                                    <div >
                                        <div class='flex fileLink' v-for='(item,j) in list.pdfUrl'>
                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/PDF1.png' ?>" alt="" class="pdfImg" v-if='item.mimetype=="application/pdf"' />
                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/excel.png' ?>" alt="" class="pdfImg" v-else-if='item.mimetype=="application/vnd.ms-excel" || item.mimetype=="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"' />
                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/word1.png' ?>" alt="" class="pdfImg" v-else-if='item.mimetype=="application/msword" || item.mimetype=="application/vnd.openxmlformats-officedocument.wordprocessingml.document"' />
                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/zip.png' ?>" alt="" class="pdfImg" v-else-if='item.mimetype=="application/x-zip-compressed"' />
                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/link.png' ?>" alt="" class="pdfImg" v-else /> 
                                            <a class='flex1 ml5' target= "_blank" :href="item.url">{{item.title}}
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                <div class='color6 font12'>
                                    <div class='mb8'><?php echo Yii::t("directMessage", "Originally sent to: ");?>{{repostDetailList.contactInfo[list.link_id]}}</div>
                                    <div class='mb8'><?php echo Yii::t("directMessage", "Submitted at: ");?>{{list.updated_at}}</div>
                                    <div>
                                        <span v-if="oldCommentChild[commentChildId][`p_${list.created_by}`] == 'f'"><?php echo Yii::t("newDS", "From student's Dad");?></span>
                                        <span v-else><?php echo Yii::t("newDS", "From student's Mom");?> </span> 
                                    </div>
                                    
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
            </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="addMessageModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("directMessage", "Add Message");?></h4>
            </div>
            <div class="modal-body p24" >
                <div><span class='F0AD4E'><?php echo Yii::t("directMessage", "To parents who have not responded.");?></span></div>
                <div class="media mt10 mb10">
                    <div class="media-left pull-left media-middle">
                        <a href="javascript:void(0)">
                            <img class="contentAvatar" :src="currentAddChild.avatar" data-holder-rendered="true" >
                        </a>
                    </div>
                    <div class="media-body pt8 media-middle">
                        <h4  class="media-heading font12">{{currentAddChild.name}}</h4>
                        <div class='text-muted'>{{currentAddChild.class_name}}</div>
                    </div>
                </div>
                <div>
                    <div class='uploadImg' v-if='adduploadImgList.length>0'>
                        <div class='imgData mr8'  v-for='(list,i) in adduploadImgList'>
                            <div v-if="list.types=='1'">
                                <div class='uploadLoading'> <?php echo Yii::t("directMessage", "Uploading");?></div>
                            </div>
                            <div v-else>
                                <img class='fileImgs' @click='uploadShowImg(list.file_key)'  :src="list.file_key" alt="">
                                <span aria-hidden="true"  @click.stop='delImg("img",list,i)'>×</span>
                            </div>
                        </div>
                        <template v-if='addloadingType==1'>
                            <div class='imgData mr8'  v-for='(list,i) in addloadingList'>
                                    <div class='uploadLoading'> <?php echo Yii::t("directMessage", "Upload queue");?></div>
                            </div>
                        </template>
                    </div>
                    <div class='mt16' v-if='adduploadLinkList.length>0'>
                        <div class='flex uploadFile' v-for='(list,index) in adduploadLinkList'>
                            <span class='glyphicon glyphicon-paperclip mr8'></span>
                            <a target="_blank" class='flex1'  style='line-height:26px' :href='list.file_key' v-if='!list.isEdit'>{{list.title}}</a>
                            <span class='flex1' v-else><input type="input" class='inputStyle'  @keyup.enter="saveFile(list,index)"  v-model='attachmentName' ></span>
                            <template v-if='!list.isEdit'>
                                <span class='glyphicon glyphicon-edit icon mr16' v-if='list.file_key!=""' @click.stop='list.isEdit=true,attachmentName=list.title'></span>
                                <span class='glyphicon glyphicon-trash icon' v-if='list.file_key!=""' @click.stop='delImg("link",list,index)'></span>
                            </template>
                            <span style='width:90px'  class='text-right inline-block' v-else>
                                <button type="button" class="btn btn-primary btn-xs" @click='saveFile(list,index)'><?php echo Yii::t("global", "Save");?></button>
                                <button type="button" class="btn btn-default btn-xs" @click='list.isEdit=false'><?php echo Yii::t("global", "Cancel");?></button>
                            </span>
                        </div>
                    </div>
                    <template v-if='addloadingType==2'>
                        <div class='flex uploadFile'  v-for='(list,i) in addloadingList'>
                            <span class='glyphicon glyphicon-paperclip mr8'></span>
                            <a  class='flex1'  style='line-height:26px' > <?php echo Yii::t("directMessage", "Upload queue");?></a>
                        </div>
                    </template>
                    <div class='uploadIcon'>
                        <div class='cur-p inline-block' id='pickfilesPhoto1'>
                            <span class='glyphicon glyphicon-picture pull-left' style='margin-top:2px'></span>
                            <span class='ml5 font14 color6'><?php echo Yii::t("directMessage", "Photo");?></span> 
                        </div>
                        <div class='cur-p inline-block ml10' id='pickfilesVideo1'>
                            <span class='glyphicon glyphicon-paperclip pull-left' style='margin-top:2px'></span>
                            <span class='ml5 font14 color6'><?php echo Yii::t("directMessage", "Video");?></span> 
                        </div>
                        <div class='cur-p inline-block ml10' id='pickfilesPDF1'>
                            <span class='glyphicon glyphicon-paperclip pull-left' style='margin-top:2px'></span>
                            <span class='ml5 font14 color6'><?php echo Yii::t("curriculum", "Attachments");?></span> 
                        </div>
                    </div>
                    <textarea class="form-control mt20" rows="5" v-model='commentContent' placeholder='<?php echo Yii::t("newDS", "Please input your reply"); ?>'></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                <button type="button" class="btn btn-primary" @click="reply('new')"><?php echo Yii::t("newDS", "Reply");?></button>
            </div>
            </div>
        </div>
    </div>
</div>

<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>
<script>
    if($('.navbar-fixed-bottom').length>0){
        var height=document.documentElement.clientHeight-$('.breadcrumb').outerHeight(true)-140
    }else{
        var height=document.documentElement.clientHeight-$('.breadcrumb').outerHeight(true)-89
    }
    $(document).ready(function () {
    // 通过该方法来为每次弹出的模态框设置最新的zIndex值，从而使最新的modal显示在最前面
        $(document).on('show.bs.modal', '.modal', function (event) {
            var zIndex = 1040 + (10 * $('.modal:visible').length);
            $(this).css('z-index', zIndex);
            setTimeout(function() {
                $('.modal-backdrop').not('.modal-stack').css('z-index', zIndex - 1).addClass('modal-stack');
            }, 0);
        });
    });
    window.addEventListener('resize', function() {
        addClass()
    })
    addClass()
    function addClass() {
        if(document.body.clientWidth>1200){
            $('.addFr').addClass('fr')
            $('.addFl').addClass('fl')
        }else{
            $('.addFr').removeClass('fr') 
            $('.addFl').removeClass('fl')

        }
    }
    
    window.addEventListener('scroll', function(){
        let t = $('body, html').scrollTop();  
        if(t>60 && document.body.clientWidth>990){
            $('.rightBox').addClass('box-fixed')
            $('.leftBox').addClass('left-fixed')

        }else{
            $('.rightBox').removeClass('box-fixed')
            $('.leftBox').removeClass('left-fixed')
        }
    })
    var branchId = '<?php echo $_GET['branchId']?>';
    var container = new Vue({
        el: "#container",
        data: {
            branchId:branchId,
            admin:'<?php echo $this->isAdmin(); ?>',
            isDev:'<?php echo $this->isDev(); ?>',
            height:height,
            rightHeight:(height-22)/2,
            commentData:{},
            borderShow:false,
            commentLoading:false,
            readLoading:false,
            targetsChildId:'',
            indexLoading:false, //首页laoding
            clientWidth:document.body.clientWidth,
            reviewTargets:[],
            otherTeacherId:'', //其他老师id
            indexList:{}, //首页数据
            itemList:{}, //首页列表
            draftList:[], //草稿箱列表
            favoritesData:{}, //收藏数据
            favoriteRemark:"", //收藏备注
            toReplyNum:0,  //反馈红点
            pageNum:1, //当前页数
            favoriteList:[],  //收藏列表
            delFavoriteList:{}, //取消收藏
            delType:'', //删除类型
            delTopList:{}, //取消置顶
            authorizedList:{}, //授权列表
            teacherList:{}, //切换老师列表
            teacherUid:'', //授权老师id
            contentData:{}, //查看详情
            contentJournal:{}, //Journal 数据
            commentChild:{}, //详情已回复学生列表
            commentChildId:'', //详情学生
            commentContent:'', //回复
            loadingBtn:false,
            commentData:{},
            clickDays:null,
            availableDates:[],
            disabledDates:[],
            masks: {
                input: 'YYYY-MM-DD',
            },
            postObjectList:{}, //全部发布对象
            search: "", //搜索名字发布对象
            cloneContentHeight:'', //克隆高度
            cloneList:'', //克隆list
            keepTargets:false, //是否保留发布对象
            keepJointAdmins:false, //是否保留协作者
            keepAttachments: false, //是否保留附件
            activeName:'first',
            latestList:[] , //最新发布列表
            activeIndex:null,
            cloneLoading:false,
            delJournalList:{}, //删除delJournal
            publishList:{} , //取消发布
            previewQrcode:'',
            shareQrcode:'',
            shareList:{},
            shareType:'',
            showSummary:true,
            showPublisher:true,
            htmlContent:1,
            sharetUser:'',
            showChild:1, //已读显示学生状态
            sendNumFamily:null,
            delStatus:1,
            showData:[], //可选日期
            qrcodeLoading:false,
            isColl:false,
            options: [],
            loading: false,
            qrcodeBox:false,
            showSelect:false,
            searchTeacher:'',
            resignList:[],
            normalList:[],
            qrcode:'',
            wechatData:{},
            scanQrcodeBox:false,
            itdevScanQrcodeBox:false,
            staffSelf:{},
            sharedList:[],
            tabActive:"authorize",
            DrmActive:'permissions',
            groupList:[],
            managerList:{},
            groupId:'',
            groupTitle:'',
            managerShareData:{},
            managerSearch:'',
            showManagerStaff:'',
            showManagerIndex:-1,
            showShareData:true,
            sharedType:'',
            delSharedId:'',
            delSharedIndex:'',
            CopyshowManager:-1,
            searchText:'',
            copySearchText:'',
            isSearch:false,
            isForward:false,
            inputForward:[],
            forwardTo:'',
            options:[],
            forwardType:'',
            forwardMark:'',
            teacherList:[],
            deptList:[],
            repostDetailList:[],
            timer:'',
            noReplyChild:{},
            showNoReplyList:false,
            classList:[],
            oldCommentChild:{},
            oldPostObjectList:{},
            currentAddChild:{},
            currentAddIndex:'',
            uploadImgList:[],
            uploadLinkList:[],
            attachmentName:'',
            uploadShow:false,
            loadingList:[],
            loadingType:0,
            token:'',
            addToken:true,
            adduploadImgList:[],
            adduploadLinkList:[],
            addloadingList:[],
            addloadingType:0,
            noReplySure:false,
            CopyPages:{},
            btnDis:false,
            videoToken:''
        },
        watch:{
            forwardType(old,newValue){
                if(old!=newValue){
                    this.forwardTo=''
                    this.teacherName=''
                    this.deptName=''
                }
            }
        },
        created: function() {

            $.ajax({
                url: '<?php echo $this->createUrl("unRepliedNum") ?>',
                type: "post",
                dataType: 'json',
                data: {
                },
                success: function(data) {
                    if (data.state == 'success') {
                        container.toReplyNum=parseInt(data.data)
                    }
                },
                error: function(data) {

                },
            })
            $.ajax({
                url: '<?php echo $this->createUrl("managerInfo") ?>',
                type: "post",
                dataType: 'json',
                data: {
                },
                success: function(data) {
                    if (data.state == 'success') {
                        container.groupList=data.data
                    }
                },
                error: function(data) {

                },
            })
            this.getCLassList()
            this.getIndex('one')
            this.getDraft()
            this.getCollect()
            this.showWechatQrcode()
            
        },
        computed: {
            searchData: function() {
                var search = this.search;
                var searchVal = ''; //搜索后的数据
                if(search) {
                    searchVal =this.postObjectList.filter(function(product) {
                        return Object.keys(product).some(function(key) {
                            return String(product['name'].toLowerCase()).indexOf(search.toLowerCase()) !== -1;
                        })
                    })
                    return searchVal;
                }
                return this.postObjectList;
            },
            teacherSearchData: function() {
                var search = this.searchTeacher;
                var searchVal = ''; //搜索后的数据
                if(search) {
                    searchVal =Object.values(this.teacherList).filter(function(product) {
                        return Object.keys(product).some(function(key) {
                            return String(product['name'].toLowerCase()).indexOf(search.toLowerCase()) !== -1;
                        })
                    })
                    return searchVal;
                }
                return this.teacherList;
            },
            managerStaffInfo: function() {
                var search = this.managerSearch;
                let that=this
                this.showManagerStaff=''
                this.showManagerIndex=-1
                if(search) {
                    searchVal =Object.values(that.managerShareData.staffInfo).filter(function(product) {
                        return Object.keys(product).some(function(key) {
                            return String(product['name'].toLowerCase()).indexOf(search.toLowerCase()) !== -1;
                        })
                    })
                    return searchVal;
                }else{
                    if(this.showShareData){
                        var shareList=[]
                        if(that.managerShareData.shareStaffIds){
                            for(var i=0;i<that.managerShareData.shareStaffIds.length;i++){
                                shareList.push(that.managerShareData.staffInfo[that.managerShareData.shareStaffIds[i]])
                            }
                        }
                        return shareList;
                    }else{
                        var shareList=[]
                        if(that.managerShareData.shareStaffIds){
                            for(var i=0;i<that.managerShareData.shareStaffIds.length;i++){
                                shareList.push(that.managerShareData.staffInfo[that.managerShareData.shareStaffIds[i]])
                            }
                        }
                        if(that.managerShareData.unShareStaffIds){
                            for(var i=0;i<that.managerShareData.unShareStaffIds.length;i++){
                                shareList.push(that.managerShareData.staffInfo[that.managerShareData.unShareStaffIds[i]])
                            }
                        }
                        return shareList;
                    }
                }


            },
        },
        methods: {
            getCLassList(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("journals/classList") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.classList=data.data
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            getSortData(dataList){
                var list=[]
                this.classList.forEach((item,index) => {
                    list.push(item.title)
                })
                var sortData = Object.values(dataList).sort((a, b) => {
                    return list.indexOf(a.class_name) - list.indexOf(b.class_name)
                })
                return sortData
            },
            showFocus(){
                this.showSelect=!this.showSelect
            },
            itdevShowUnWechatQrcode(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("teacherBindQrcode") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        staff_id:this.otherTeacherId==0?this.indexList.uid:this.otherTeacherId
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.itdevScanQrcodeBox=!that.itdevScanQrcodeBox
                            that.$nextTick(() => {
                                $('#itdevwechatQrcodeBind').qrcode({width: 124,height: 124,text:data.data});
                            })
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            showUnWechatQrcode(){
                let that=this
                if(this.scanQrcodeBox){
                    clearInterval(this.timer);
                    that.scanQrcodeBox=!that.scanQrcodeBox
                    return
                }else{
                    this.timer =setInterval(this.showWechatQrcode, 5000);   
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("teacherBindQrcode") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        staff_id:this.otherTeacherId==0?this.indexList.uid:this.otherTeacherId
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.scanQrcodeBox=!that.scanQrcodeBox
                            that.$nextTick(() => {
                                $('#wechatQrcodeBind').qrcode({width: 124,height: 124,text:data.data});
                            })
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            showWechatQrcode(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("teacherBindInfo") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {},
                    success: function(data) {
                        if (data.state == 'success') {
                            that.wechatData=data.data
                            if(data.data.state==1){
                                if(that.scanQrcodeBox){
                                    that.scanQrcodeBox=false
                                    that.qrcodeBox=true
                                }
                                clearInterval(that.timer);
                            }
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            unbind(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("teacherUnbind") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {},
                    success: function(data) {
                        if (data.state == 'success') {
                            resultTip({
                                msg:data.message
                            })
                            that.qrcodeBox=false
                            that.showWechatQrcode()
                        }else{
                            resultTip({
                                error: 'warning',
                                msg:data.message
                            })
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            initDay(year,mon,end){
                let that=this
                that.disabledDates=[{
                    start: new Date(year, mon-1, 1),
                    end:  new Date(year, mon-1,end),
                }]
                $.ajax({
                    url: '<?php echo $this->createUrl("overviewDay") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        "startDate": year+'-'+mon+'-'+1,
                        "endDate":year+'-'+mon+'-'+end,
                        otherTeacherId:this.otherTeacherId
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                           if(data.data.length!=0){
                                that.availableDates=[]
                                that.showData=data.data
                                that.disabledDates=[]
                                for(var i=0;i<data.data.length;i++){
                                    that.availableDates.push({
                                        start: new Date(data.data[i].substr(0,4), parseInt(data.data[i].substr(5,2))-1, data.data[i].substr(8,2)),
                                        end:  new Date(data.data[i].substr(0,4), parseInt(data.data[i].substr(5,2))-1, data.data[i].substr(8,2)),
                                    })
                                }
                            }else{
                                that.availableDates=[]
                                that.disabledDates=[{
                                    start: new Date(year, mon-1, 1),
                                    end:  new Date(year, mon-1,end),
                                }]
                            }

                        }
                    },
                    error: function(data) {

                    },
                })
            },
            removeDate(hide) {
                this.clickDays=null
                this.getIndex()
                hide();
            },
            showHoliday(day){
                if(this.showData.indexOf(day.id)!=-1){
                    this.clickDays=day.id
                    this.getIndex()
                }
            },
            pageChange(page){
                var nextMonthFirstDay=new Date(page.year,page.month,1);
                var oneDay=1000*60*60*24;
                var lastTime = new Date(nextMonthFirstDay-oneDay);
                var day = lastTime.getDate();
                this.initDay(page.year,page.month,day)

            },
            initPage(){
                var _this = this;
                _this.CopyPages=JSON.parse(JSON.stringify(_this.pages))
                if(_this.CopyPages.last_page>=10){
                    _this.pages.pages=[1,2,3,4,5,6,7,8,9,10]
               }else{
                    var numPage=[]
                    for(var i=1;i<=_this.CopyPages.last_page;i++){
                        numPage.push(i)
                    }
                    _this.pages.pages=numPage
               }
            },
            plus(index) { 
                var _this = this;
                _this.pageNum = Number(index)
                this.pagesSize()
                this.getIndex()
            },
            pagesSize(){
                var _this = this;
                if(_this.pageNum<10){
                    if(_this.CopyPages.last_page>=10){
                        _this.pages.pages=[1,2,3,4,5,6,7,8,9,10]
                    }else{
                        var numPage=[]
                        for(var i=1;i<=_this.CopyPages.last_page;i++){
                            numPage.push(i)
                        }
                        _this.pages.pages=numPage
                    }
                }else if(_this.pageNum<=_this.CopyPages.last_page){
                    if(_this.CopyPages.last_page-_this.pageNum>=4){
                        var minPage=_this.pageNum-5
                        var maxPage=_this.pageNum+4
                    }else{
                        var minPage=_this.pageNum-(9-((_this.CopyPages.last_page-_this.pageNum)))
                        var maxPage=_this.pageNum+(_this.CopyPages.last_page-_this.pageNum)
                    }
                    var numPage=[]
                    for(var i=minPage;i<=maxPage;i++){
                        numPage.push(i)
                    }
                    _this.pages.pages=numPage
                }
            },
            next(index){
                var _this = this;
                _this.pageNum = Number(index) + 1
                this.pagesSize()
                this.getIndex()
            },
            prev(index) {
                var _this = this;
                _this.pageNum = Number(index) - 1
                this.pagesSize()
                this.getIndex()
            },
            toggleTeacher(type){
                let that=this
                this.showSelect=false
                this.searchTeacher=''
                if(type && Object.keys(that.teacherList).length!=0){
                    $('#teacherTabModal').modal('show')
                    return
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("teacherList") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.teacherList=that.sortTea(Object.values(data.data))
                            var resignList=[]
                            var normalList=[]
                            if(that.indexList.uid){
                                that.staffSelf=data.data[that.indexList.uid]
                            }
                            let dataList=data.data
                            delete dataList[that.indexList.uid]
                            Object.values(dataList).forEach(item => {
                                if(item.level==0){
                                    resignList.push(item)
                                }else{
                                    normalList.push(item)
                                }
                            })
                            that.normalList=that.sortTea(normalList)
                            that.resignList=that.sortTea(resignList)
                            if(type){
                                $('#teacherTabModal').modal('show')
                            }
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            handleSelect(item) {
                this.tabTeacher(item.uid)
            },
            sortTea(list){
                list.sort((x,y)=>{
                    return x['name'].localeCompare(y['name'])
                })
                return list
            },
            tabTeacher(id){
                this.pageNum=1
                this.otherTeacherId=parseInt(id)
                $('#teacherTabModal').modal('hide')
                this.clickDays=null
                this.getIndex()
                this.searchText=''
            },
            getIndex(one){
                let that=this
                this.indexLoading=true
                var dataList={}
                if(this.otherTeacherId!=''){
                    dataList={
                         otherTeacherId:parseInt(this.otherTeacherId),
                         page:this.pageNum,
                         date:this.clickDays,
                         searchString:this.searchText
                    }
                }else{
                    dataList={
                         page:this.pageNum,
                         date:this.clickDays,
                         searchString:this.searchText
                    }
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("list") ?>',
                    type: "post",
                    dataType: 'json',
                    data:dataList,
                    success: function(data) {
                        if (data.state == 'success') {
                            if(one){
                                that.toggleTeacher()
                            }
                            that.indexLoading=false
                           that.indexList=data.data
                           that.itemList=data.data.pageData
                           that.$forceUpdate()
                           
                           that.$nextTick(() => {    
                                var context = document.querySelectorAll(".listTitle");
                                var instance = new Mark(context);
                                instance.mark(that.copySearchText ,{separateWordSearch: false});
                            })
                            if(that.searchText!=''){
                                that.isSearch=true
                                that.copySearchText= JSON.parse(JSON.stringify(that.searchText))
                                that.$nextTick(() => {    
                                    var context = document.querySelectorAll(".listTitle");
                                    var instance = new Mark(context);
                                    instance.mark(that.searchText ,{separateWordSearch: false});
                                })
                            }else{
                                that.copySearchText=''
                                that.isSearch=false
                            }
                            that.CopyPages=data.data.pageData
                            if(that.pageNum==1){
                                that.pages = data.data.pageData
                                that.pageNum='1'
                                that.initPage()
                            }
                        }else{
                            that.indexLoading=false
                        }
                    },
                    error: function(data) {
                        that.indexLoading=false
                    },
                })
            },
            getDraft(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("draft") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                           that.draftList=data.data
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            delJournal(list,type){
                if(list){
                    this.delType=type
                    this.delStatus=1;
                    this.delJournalList=list
                    this.delJournalList.type=type
                    $('#delModal').modal('show')
                    return
                }

                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("delete") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        journal_id:this.delJournalList.journal_id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.delStatus=data.data
                            if(data.data==1){
                                if(that.delJournalList.type=='index'){
                                    that.getIndex()
                                }else{
                                    that.getDraft()
                                }
                                $('#delModal').modal('hide')
                                resultTip({
                                    msg:data.state
                                });
                            }else if(data.data==10){
                                that.delType='index'
                               $('#delModal').modal('show')
                            }else{
                                resultTip({
                                    error: 'warning',
                                    msg:'<?php echo Yii::t("newDS", "Cannot delete due to existence of parent comments."); ?>'
                                })
                            }
                          
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            journalOffline(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("offline") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        journal_id:this.delJournalList.journal_id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            if(that.delJournalList.type=='index'){
                                that.getIndex()
                            }else{
                                that.getDraft()
                            }
                            $('#delModal').modal('hide')
                            resultTip({
                                msg:data.message
                            });
                        } else {
                            resultTip({
                                error: 'warning',
                                msg:data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg:data.message
                        });
                    },
                })
            },
            getCollect(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("favorite") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        "otherTeacherId": 8034220

                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.favoriteList=data.data
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            addFavorites(data){
                this.favoritesData=data
                $('#favoritesModal').modal('show')
            },
            saveFavorites(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("favoriteAdd") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        favorite_title:this.favoriteRemark,
                        journal_id:this.favoritesData.journal_id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                           that.getCollect()
                           that.getIndex()
                           $('#favoritesModal').modal('hide')
                            resultTip({
                                msg:data.state
                            });

                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            delConfirm(){
                if(this.delType=='favorites'){
                    this.delFavorite()
                }else if(this.delType=='top'){
                    this.delTop()
                }else if(this.delType=='index' || this.delType=='draft'){
                    this.delJournal()
                }
                else if(this.delType=='publish'){
                    this.unpublish()
                }else if(this.delType=='shared'){
                    this.delSharedList('modal')
                }
            },
            delFavorite(list){
                // if(list){
                //     this.delType='favorites'
                //     this.delFavoriteList=list
                //     $('#delModal').modal('show')
                //     return
                // }
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("favoriteDel") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        journal_id:list.journal_id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                           that.getIndex()
                           that.getCollect()
                           $('#delModal').modal('hide')
                            resultTip({
                                msg:data.state
                            });
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            setTop(list){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("favoriteSetTop") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        favorite_id:list.favorite_id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                           that.getCollect()
                            resultTip({
                                msg:data.state
                            });
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            delTop(list){
                // if(list){
                //     this.delType='top'
                //     this.delTopList=list
                //     $('#delModal').modal('show')
                //     return
                // }
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("favoriteCancelTop") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        favorite_id:list.favorite_id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                           that.getCollect()
                           $('#delModal').modal('hide')
                            resultTip({
                                msg:data.state
                            });
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            setAuthorize(type){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("authorizer") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                           that.authorizedList=data.data
                           if(type){
                                that.teacherUid=''
                                that.options=[]
                               $('#authorizedModal').modal('show')
                           }
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            setShare(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("shareList") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.sharedList=data.data
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            remoteMethod(query) {
                let that=this
                if (query !== '') {
                    this.loading = true;
                    $.ajax({
                        url: '<?php echo $this->createUrl("teacherSearch") ?>',
                        type: "post",
                        dataType: 'json',
                        data: {
                            searchString:query
                        },
                        success: function(data) {
                            if (data.state == 'success') {
                                that.options = Object.values(data.data) ;
                            }else{
                                resultTip({
                                    error: 'warning',
                                    msg: data.message
                                });
                            }
                            that.loading = false;
                        },
                        error: function(data) {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            that.loading = false;
                        },
                    })
                } else {
                    this.options = [];
                }
            },
            confirmAuthorization(){
                if(this.authorizedList.authorizationList[this.teacherUid]){
                    resultTip({
                        error: 'warning',
                        msg: '<?php echo Yii::t("directMessage", "Already added"); ?>'
                    });
                    return
                }
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("authorizerAdd") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        staff_id:this.teacherUid
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                           that.teacherUid=''
                           that.options = []
                           that.setAuthorize()
                           resultTip({
                                msg:data.state
                            });
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            confirmShare(){
                if(this.sharedList.shareList[this.teacherUid]){
                    resultTip({
                        error: 'warning',
                        msg: '<?php echo Yii::t("directMessage", "Already added"); ?>'
                    });
                    return
                }
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("shareAdd") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        staff_id:this.teacherUid
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.teacherUid=''
                            that.options = []
                            that.setShare()
                            resultTip({
                                msg:data.state
                            });
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            delAuthorized(id){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("authorizerDel") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        staff_id:id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.setAuthorize()
                            resultTip({
                                msg:data.state
                            });

                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            delShared(id){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("shareDel") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        staff_id:id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.setShare()
                            resultTip({
                                msg:data.state
                            });

                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            
            showContent(list){
                let that=this
                this.forwardType=''
                this.forwardTo=''
                this.forwardMark=''
                this.commentChildId=''
                this.isForward=false
                this.inputForward=[]
                this.contentJournal=list
                this.contentView(list,'#contentModal')
                if(this.indexList.otherTeacherId==0){
                     $.ajax({
                        url: '<?php echo $this->createUrl("commentChildIdlist") ?>',
                        type: "get",
                        dataType: 'json',
                        data: {
                            journal_id:list.journal_id
                        },
                        success: function(data) {
                            if (data.state == 'success') {
                                that.commentChild=that.getSortData(data.data)
                                that.oldCommentChild=data.data
                            }else{
                                resultTip({
                                    error: 'warning',
                                    msg: data.message
                                });
                            }
                        },
                        error: function(data) {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        },
                    })
                }

            },
            contentView(list,type,coll){
                let that=this
                if(coll){
                    this.isColl=true
                }else{
                    this.isColl=false
                }
                if(!type){
                    that.cloneLoading=true
                }
                if(type){
                    this.initQiniu()
                }
                this.isToken=true
                that.commentData={}
                that.commentChildId=''
                that.showMore(list,'hide')
                $.ajax({
                    url: '<?php echo $this->createUrl("view") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        journal_id:list.journal_id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.contentData=data.data
                            that.shareList=data.data
                            that.borderShow=false
                            if(type){
                                $(type).modal('show')
                            }else{
                                that.cloneLoading=false
                            }
                            that.$nextTick(() => {
                                if(that.copySearchText!='' && that.isSearch){
                                    var context = document.querySelector(".contentTitle");
                                    var instance = new Mark(context);
                                    instance.mark(that.copySearchText ,{separateWordSearch: false});

                                    var title = document.querySelector(".contentList");
                                    var instance = new Mark(title);
                                    instance.mark(that.copySearchText ,{separateWordSearch: false});
                                }
                            })
                        }else{
                            that.cloneLoading=false
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        that.cloneLoading=false
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            commentList(list){
                let that=this
                this.isForward=false
                this.inputForward=[]
                this.commentChildId=list
                this.commentLoading=true
                this.commentChildData=this.oldCommentChild[list]
                this.uploadImgList=[]
                this.uploadLinkList=[]
                this.loadingList=[]
                $.ajax({
                    url: '<?php echo $this->createUrl("journals/item") ?>',
                    type: 'get',
                    dataType: 'json',
                    data: {
                        id: that.contentJournal.journal_id,
                        child_id:list
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            
                            data.data.items.forEach((list) => {
                                list.imgUrl=[]
                                list.pdfUrl=[]
                                list.videoUrl=[]
                                if(list.attachments.length!=0){
                                    list.attachments.forEach((item) => {
                                        if(data.data.attachmentList[item]){
                                            let type=data.data.attachmentList[item].mimetype.split('/')[0]
                                            if(type=="image"){
                                            list.imgUrl.push(data.data.attachmentList[item])
                                            }else if(type=="video"){
                                            list.videoUrl.push(data.data.attachmentList[item])
                                            }else{
                                            list.pdfUrl.push(data.data.attachmentList[item])
                                            }
                                        }
                                    })
                                }
                            })
                            that.getQiniu('child')
                            that.commentData = data.data
                            that.commentLoading=false
                            that.$nextTick(() => {
                               if(that.$refs.contentShow.offsetHeight<that.$refs.contentComment.offsetHeight){
                                    that.borderShow=true
                               }else{
                                    that.borderShow=false
                               }
                            })
                        }
                        else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            that.commentLoading=false
                        }
                    }
                });
            },
            markNoReply(){
                this.noReplySure=false
                $('#noReplyModal').modal()
            },
            reply(typeData) {
                var _this = this;
                if(this.loadingList.length!=0 || this.addloadingList.length!=0){
                    resultTip({
                        error: 'warning',
                        msg: '<?php echo Yii::t("directMessage", "Uploading");?>'
                    });
                    return
                }
                var imgIds=[]
                var pdfIds=[]
                var ids=[]
                if(typeData=='new'){
                    this.adduploadImgList.forEach((item) => {
                        ids.push(item._id)
                        imgIds.push({
                            url:item.file_key,
                            id:item._id,
                            mimetype:item.mimetype,
                            title:item.title
                        })
                    })
                    this.adduploadLinkList.forEach((item) => {
                        ids.push(item._id)
                        pdfIds.push({
                            url:item.file_key,
                            id:item._id,
                            mimetype:item.mimetype,
                            title:item.title,
                        })
                    })
                }else{
                    this.uploadImgList.forEach((item) => {
                        ids.push(item._id)
                        imgIds.push({
                            url:item.file_key,
                            id:item._id,
                            mimetype:item.mimetype,
                            title:item.title
                        })
                    })
                    this.uploadLinkList.forEach((item) => {
                        ids.push(item._id)
                        pdfIds.push({
                            url:item.file_key,
                            id:item._id,
                            mimetype:item.mimetype,
                            title:item.title,
                        })
                    })
                }
                if(typeData=='noReply' && this.joint_admins_count!=0){
                    if(!this.noReplySure){
                        resultTip({
                            error: 'warning',
                            msg: '<?php echo Yii::t("newDS", "Please confirm");?>'
                        });
                        return
                    }
                }
                if(typeData!='noReply'){
                    if(ids.length==0 && _this.content==''){
                        resultTip({
                            error: 'warning',
                            msg: '<?php echo Yii::t("directMessage", "Content cannot be empty.");?>'
                        });
                        return
                    }
                }
                this.loadingBtn = true
                $.ajax({
                    url: '<?php echo $this->createUrl("journals/saveComment") ?>',
                    type: 'post',
                    dataType: 'json',
                    data: {
                        id: _this.contentJournal.journal_id,
                        child_id:typeData=='new'?_this.currentAddChild.id : _this.commentChildId,
                        content: _this.commentContent,
                        mark_as_staff:typeData=='noReply'?1:0,
                        attachments:ids
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            _this.loadingBtn = false
                            if(typeData=='noReply'){
                                $('#noReplyModal').modal('hide')
                            }
                            _this.commentContent=''
                            if(typeData=='new'){
                                $('#addMessageModal').modal('hide')
                                _this.oldCommentChild[_this.currentAddChild.id]=_this.currentAddChild
                                _this.commentChildId=_this.currentAddChild.id
                                _this.commentChild.push(_this.currentAddChild)
                                _this.noReplyChild.splice(_this.currentAddIndex, 1)
                            }
                            _this.commentList(_this.commentChildId)
                            _this.uploadImgList=[]
                            _this.uploadLinkList=[]
                            _this.loadingList=[]
                            resultTip({
                                msg: data.state
                            });
                        }
                        else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            _this.loadingBtn = false
                        }
                    }
                });
            },
            showMore(list,type){
                if(list=='show'){
                    $('#postObjectModal').modal('show')
                    return
                }
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("subscribers") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        journal_id:list.journal_id,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.postObjectList=that.getSortData(data.data)
                            that.oldPostObjectList=data.data

                            if(!type){
                                $('#postObjectModal').modal('show')
                            }
                        }
                    },
                    error: function(data) {

                    },
                })

            },
            optionData(myCharts, destData){
                var option = {
                    tooltip: {
                        trigger: 'item',
                        formatter: '{a} <br/>{b} : {c} ({d}%)'
                    },
                    legend: {
                        // orient: 'vertical',
                        bottom: 10,
                        left: 'center',
                    },
                    color:['#F8C42DFF','#5B8FF9FF'],
                    series: [
                        {
                            name: '<?php echo Yii::t("newDS", "Read Report"); ?>',
                            type: 'pie',
                            radius: '55%',
                            center: ['50%', '45%'],
                            data:destData,
                            emphasis: {
                                itemStyle: {
                                    shadowBlur: 10,
                                    shadowOffsetX: 0,
                                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                                }
                            }
                        }
                    ]
                };
                return option
            },
            showRead(list){
                this.clientWidth=document.body.clientWidth
                let that=this
                that.targetsChildId=''
                that.reviewChilds={}
                that.reviewed=[]
                that.reviewTargets=[]
                that.unreadList=[]
                $.ajax({
                    url: '<?php echo $this->createUrl("journals/getReviewed") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        id:list.journal_id,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            $('#echart').removeAttr('_echarts_instance_');
                            that.reviewChilds=data.data.childs
                            that.reviewed=[...new Set(data.data.reviewed.map(Number))]
                            for(var i=0;i<data.data.targets.length;i++){
                                if(that.reviewed.indexOf(data.data.targets[i])==-1){
                                    that.unreadList.push(data.data.targets[i])
                                }
                            }
                            that.reviewTargets=data.data.targets
                            that.sendNumFamily=data.data.sendNum
                            let noRead=data.data.targets.length-data.data.reviewed.length
                            var data=[{value: data.data.reviewed.length, name: '<?php echo Yii::t("newDS", "Read"); ?>'},
                                {value: noRead, name: '<?php echo Yii::t("newDS", "Unread"); ?>'},]
                            $('#readModal').modal('show')
                            $('#readModal').on('shown.bs.modal', function (e) {
                                var myCharts = echarts.init(document.getElementById('echart'))
                                var option = that.optionData(myCharts,data)
                                myCharts.setOption(option,true);
                                myCharts.resize()
                            })
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            childInfo(list){
                this.readLoading=true
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("journals/getParents") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        child_id:list,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.targetsParent=data.data.parentData
                            that.wxReceivers=data.data.wxReceivers
                            that.targetsChildId=list
                            that.readLoading=false
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            cloneContent(list){
                let that=this
                this.cloneList=list
                $.ajax({
                    url: '<?php echo $this->createUrl("view") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        journal_id:list.journal_id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.contentData=data.data
                            $('#cloneContentModal').modal('show')
                            that.$nextTick(() => {
                                setTimeout(function() {
                                    that.cloneContentHeight=that.$refs.content.offsetHeight
                                }, 500);

                            })
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            cloneCopy(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("copy") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        journal_id:this.cloneList.journal_id,
                        keepTargets: this.keepTargets?1:0,
                        keepJointAdmins:this.keepJointAdmins?1:0,
                        keepAttachments: this.keepAttachments?1:0,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            window.location.href='<?php echo $this->createUrl('editOne'); ?>&journal_id='+data.data+'&clone=1'
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            cloneAll(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("latest") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.latestList=data.data
                            that.contentData={}
                            $('#cloneAllModal').modal('show')
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })

            },
            handleClick(type) {
                this.activeName=type
            },
            showCloneData(list,index,type){
                this.cloneList=list
                this.contentView(list)
                this.activeIndex=type+index
            },
            unpublish(list){
                if(list){
                    this.delType='publish'
                    this.publishList=list
                    $('#delModal').modal('show')
                    return
                }
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("offline") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        journal_id:this.publishList.journal_id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.getDraft()
                            that.getIndex()
                            $('#delModal').modal('hide')
                           resultTip({
                                msg:data.state
                            });
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            showQrcode(list){
                let that=this
                $('#qrcodeModal').modal('show')
                this.qrcodeLoading=true
                $.ajax({
                    url: '<?php echo $this->createUrl("Journals/getQrCode") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        id:list.journal_id,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.previewQrcode=data.data.preview
                            that.shareQrcode=data.data.share
                            that.qrcodeLoading=false
                        }
                    },
                    error: function(data) {
                        that.qrcodeLoading=false

                    },
                })
                this.contentView(list)
            },
            shareImg(type){
                this.htmlContent=1
                this.showPublisher=true
                this.shareType=type
                $('#shareModal').modal('show')
            },
            facultyAuthority(id,title){
                let that=this
                this.groupId=id
                this.groupTitle=title
                if(id=='OTHER'){
                    this.DrmActive='user'
                    this.managerShare('init')
                }else{
                    this.DrmActive='permissions'
                    $.ajax({
                        url: '<?php echo $this->createUrl("managerList") ?>',
                        type: "post",
                        dataType: 'json',
                        data: {
                            grade_group:id
                        },
                        success: function(data) {
                            if (data.state == 'success') {
                                that.managerList=data.data
                                that.teacherUid=''
                                that.options = []
                                $("#authorityManagementModal").modal('show')
                            }
                        },
                        error: function(data) {

                        },
                    })
                }
            },
            confirmManager(){
                if(this.managerList.staffInfo[this.teacherUid]){
                    resultTip({
                        error: 'warning',
                        msg: '<?php echo Yii::t("directMessage", "Already added"); ?>'
                    });
                    return
                }
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("managerAdd") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        staff_id:this.teacherUid,
                        grade_group:this.groupId
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.teacherUid=''
                            that.options = []
                            that.facultyAuthority(that.groupId,that.groupTitle)
                            resultTip({
                                msg:data.state
                            });
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            delManager(id){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("managerDel") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        staff_id:id,
                        grade_group:this.groupId
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.facultyAuthority(that.groupId,that.groupTitle)
                            resultTip({
                                msg:data.state
                            });

                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            managerShare(init){
                let that=this
                if(init!='show'){
                    this.showManagerStaff=''
                    this.showManagerIndex=-1
                    this.showShareData=true
                }

                $.ajax({
                    url: '<?php echo $this->createUrl("managerShareList") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        grade_group:this.groupId
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.managerShareData=data.data
                            if(init=='init'){
                                that.teacherUid=''
                                that.options = []
                                $("#authorityManagementModal").modal('show')
                            }else if(init=='show'){
                                that.managerStaffInfo.forEach((item,index) => {
                                    if(that.CopyshowManager.uid==item.uid){
                                        that.showManagerStaff=that.managerStaffInfo[index]
                                        that.showManagerIndex=index
                                    }
                                })

                            }else{
                                that.showManagerStaff=''
                                that.showManagerIndex=-1
                            }
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            showManager(list,index){
                this.showManagerStaff=list
                this.showManagerIndex=index
                this.CopyshowManager=list
            },
            addSharedList(type){
                this.sharedType=type
                this.teacherUid=''
                this.options=[]
                $("#addSharedModal").modal('show')
            },
            confirmSharedList(){

                let that=this
                var dataList={}
                if(that.sharedType=='shareTo'){
                    dataList={
                        "uid":this.showManagerStaff.uid,
                        "sharedToUid":this.teacherUid,
                    }
                }else{
                    dataList={
                        "uid":this.teacherUid,
                        "sharedToUid":this.showManagerStaff.uid,
                    }
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("managerShareAdd") ?>',
                    type: "post",
                    dataType: 'json',
                    data: dataList,
                    success: function(data) {
                        if (data.state == 'success') {
                            that.teacherUid=''
                            that.options = []
                            that.managerShare('show')
                            resultTip({
                                msg:data.state
                            });
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            delSharedList(type,id,index){
                if(type!='modal'){
                    this.delType='shared'
                    this.sharedType=type
                    this.delSharedId=id
                    this.delSharedIndex=index
                    $('#delModal').modal('show')
                    return
                }
                let that=this
                var dataList={}
                if(that.sharedType=='shareTo'){
                    dataList={
                        "uid":this.showManagerStaff.uid,
                        "sharedToUid":that.delSharedId,
                    }
                }else{
                    dataList={
                        "uid":that.delSharedId,
                        "sharedToUid":this.showManagerStaff.uid,
                    }
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("managerShareDel") ?>',
                    type: "post",
                    dataType: 'json',
                    data: dataList,
                    success: function(data) {
                        if (data.state == 'success') {
                            that.managerShare('show')
                            $('#delModal').modal('hide')
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            html(data){
              return  data.replace(/\n/g, '<br/>')
            },
            forwardEvent(){
                this.isForward=true
            },
            forwardCancel(){
                this.isForward=false
                this.inputForward=[]
            },
            forwardTeacher(){
                if(this.inputForward.length==0){
                    resultTip({
                        error: 'warning',
                        msg:'<?php echo Yii::t("global", "Please Select");?><?php echo Yii::t("directMessage", "Handover Content");?>'
                    });
                    return
                }
                this.inputForward=this.inputForward.sort()
                this.forwardType=''
                this.forwardTo=''
                this.forwardMark=''
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("childTeacherList") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        childId:this.commentChildId
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.teacherList = Object.values(data.data) ;
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                        that.loading = false;
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                        that.loading = false;
                    },
                })
                $.ajax({
                    url: '<?php echo $this->createUrl("childDeptList") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {},
                    success: function(data) {
                        if (data.state == 'success') {
                            that.deptList = Object.values(data.data) ;
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                        that.loading = false;
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                        that.loading = false;
                    },
                })
                $('#forwardModal').modal('show')
            },
            repost(){
                var comment_id_list=[]
                this.inputForward.forEach(item => {
                    comment_id_list.push(this.commentData.items[item].id)
                });
                let that=this
                if(this.forwardType==''){
                    resultTip({
                        error: 'warning',
                        msg:'<?php echo Yii::t("global", "Please Select");?>移交类型'
                    });
                    return
                }
                if(this.forwardTo==''){
                    resultTip({
                        error: 'warning',
                        msg:'<?php echo Yii::t("global", "Please Select");?>移交人或者部门'
                    });
                    return
                }
                this.btnDis=true
                $.ajax({
                    url: '<?php echo $this->createUrl("repost") ?>',
                    type: 'post',
                    dataType: 'json',
                    data:{
                        comment_id_list:comment_id_list ,
                        repost_type: this.forwardType, 
                        repost_to: this.forwardTo,
                        repost_mark:this.forwardMark
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            $('#forwardModal').modal('hide')
                            that.initLoading = false;
                            that.commentContent=''
                            that.commentList(that.commentChildId)
                            resultTip({
                                msg: data.message
                            });
                            that.btnDis=true
                        }
                        else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            that.btnDis=false
                        }
                    },
                    error: function(data) {
                        that.classLoading=false
                        that.btnDis=false
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                }); 
            },
            showOriginalInfo(id){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("repostDetail") ?>',
                    type: 'post',
                    dataType: 'json',
                    data:{
                        commentId:id,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            $('#originalInfoModal').modal('show')
                            data.data.commentList.forEach((list) => {
                                list.imgUrl=[]
                                list.pdfUrl=[]
                                list.videoUrl=[]
                                if(list.attachments.length!=0){
                                    list.attachments.forEach((item) => {
                                        if(data.data.attachmentsList[item]){
                                            let type=data.data.attachmentsList[item].mimetype.split('/')[0]
                                            if(type=="image"){
                                            list.imgUrl.push(data.data.attachmentsList[item])
                                            }else if(type=="video"){
                                            list.videoUrl.push(data.data.attachmentsList[item])
                                            }else{
                                            list.pdfUrl.push(data.data.attachmentsList[item])
                                            }
                                        }
                                    })
                                }
                            })
                           that.repostDetailList=data.data
                        }
                        else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        that.classLoading=false
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                }); 
            },
            noReplyList(){
                var noList=JSON.parse(JSON.stringify(this.oldPostObjectList))
                for(var key in this.oldCommentChild){
                    delete noList[key]
                }
                this.noReplyChild=this.getSortData(noList)
                this.showNoReplyList=true
            },
            addReplyChild(list,id,index){
                this.currentAddChild=list
                this.currentAddChild.new=true
                this.currentAddIndex=index
                this.adduploadImgList=[]
                this.adduploadLinkList=[]
                this.addloadingList=[]
                let that=this
                $('#addMessageModal').modal('show')
                if(this.addToken){
                    that.$nextTick(()=>{
                        that.addToken=false
                        var btn=[{id:'pickfilesPhoto1',type:'photo'},{id:'pickfilesVideo1',type:'video'},{id:'pickfilesPDF1',type:'pdf'}]
                        for(var i in btn){
                            addconfig['token'] =btn[i].type=="video"?that.videoToken:that.token;
                            addconfig['browse_button'] =btn[i].id;
                            addconfig['filters']={
                                mime_types:btn[i].type=='photo'?[{title : "<?php echo Yii::t('teaching', 'Image files');?>", extensions : "jpg,gif,png,jpeg"}]:[ {title : "<?php echo Yii::t('teaching', 'pdf files');?>", extensions : "pdf,doc,xls,zip,xlsx,docx,mp4,avi,mov,wmv"}]
                            };
                            var adduploader = new plupload.Uploader(addconfig);
                            adduploader.init();
                        }
                    })
                }
            },
            // initQiniu(){
            //     let that=this
            //     $.ajax({
            //         url: '<?php echo $this->createUrl("journals/getCommentQiniuToken") ?>',
            //         type: "post",
            //         dataType: 'json',
            //         data: {
            //             linkId: that.contentJournal.journal_id,
            //             linkType:'journal'
            //         },
            //         success: function(data) {
            //             if (data.state == 'success') {
            //                 that.token=data.data.data                            
            //             } else {
            //                 resultTip({
            //                     error: 'warning',
            //                     msg: data.message
            //                 });
            //             }
            //         },
            //         error: function(data) {

            //         },
            //     }) 
            //     $.ajax({
            //         url: '<?php echo $this->createUrl("journals/getCommentQiniuToken") ?>',
            //         type: "post",
            //         dataType: 'json',
            //         data: {
            //             linkId: that.contentJournal.journal_id,
            //             linkType:'journal',
            //             isVideo:1
            //         },
            //         success: function(data) {
            //             if (data.state == 'success') {
            //                 that.videoToken=data.data.data                            
            //             } else {
            //                 resultTip({
            //                     error: 'warning',
            //                     msg: data.message
            //                 });
            //             }
            //         },
            //         error: function(data) {

            //         },
            //     }) 
            // },
            initQiniu() {
                const that = this;
                const url = '<?php echo $this->createUrl("journals/getCommentQiniuToken") ?>';

                function fetchToken(isVideo = 0, callback) {
                    const data = {
                    linkId: that.contentJournal.journal_id,
                    linkType: 'journal'
                    };
                    if (isVideo) data.isVideo = 1;

                    $.ajax({
                        url,
                        type: 'POST',
                        dataType: 'json',
                        data,
                        success(res) {
                            if (res.state === 'success') {
                            callback && callback(res.data.data);
                            } else {
                            resultTip({ error: 'warning', msg: res.message });
                            }
                        },
                        error() {
                            resultTip({ error: 'warning', msg: '请求 Token 失败' });
                        }
                    });
                }
                fetchToken(0, token => {
                    that.token = token;
                });
                fetchToken(1, videoToken => {
                    that.videoToken = videoToken;
                });
            },
            getQiniu(type){
                let that=this
                if(!that.isToken){
                    return
                }
                setTimeout(() => {               
                    that.$nextTick(()=>{
                        var btn=[{id:'pickfilesPhoto',type:'photo'},{id:'pickfilesVideo',type:'video'},{id:'pickfilesPDF',type:'pdf'}]
                        that.isToken=false
                        for(var i in btn){
                            config['token'] =btn[i].type=="video"?that.videoToken:that.token;
                            config['browse_button'] =btn[i].id;
                            const mimeTypes = that.getMimeTypes(btn[i].type);
                            config['filters']=mimeTypes;
                            var uploader = new plupload.Uploader(config);
                            uploader.init();
                        }
                    })
                }, 100);
            },
            getMimeTypes(type) {
                let mimeTypes;
                if (type === 'photo') {
                    mimeTypes = [{
                        title: "<?php echo Yii::t('teaching', 'Image files'); ?>",
                        extensions: "jpg,gif,png,jpeg"
                    }];
                } else if (type === 'video') {
                    mimeTypes = [{
                        title: "<?php echo Yii::t('teaching', 'Video files'); ?>",
                        extensions: "mp4,avi,mov,wmv"
                    }];
                } else {
                    mimeTypes = [{
                        title: "<?php echo Yii::t('teaching', 'PDF and document files'); ?>",
                        extensions: "pdf,doc,xls,zip,xlsx,docx"
                    }];
                }
                return mimeTypes;
            },
            delImg(type,list,index){
                var that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("journals/deleteAttachment") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        attachmentId:list._id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            if(type=='img'){
                                that.uploadImgList.forEach((item,index) => {
                                    if (item._id==list._id) {
                                        that.uploadImgList.splice(index, 1)
                                    } 
                                })
                            }else{
                                that.uploadLinkList.forEach((item,index) => {
                                    if (item._id==list._id) {
                                        that.uploadLinkList.splice(index, 1)
                                    } 
                                })
                            }
                            resultTip({
                                msg:data.state
                            });
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            saveFile(list,index) {
                var that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("journals/updateAttachmentName") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        attachmentId:list._id,
                        attachmentName:this.attachmentName

                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.uploadLinkList.forEach((item,index) => {
                                if (item._id==list._id) {
                                    Vue.set(that.uploadLinkList[index], 'title',data.data.title);
                                    Vue.set(that.uploadLinkList[index], 'file_key', data.data.url);
                                    Vue.set(that.uploadLinkList[index], 'isEdit', false);
                                } 
                            })
                            resultTip({
                                msg:'<?php echo Yii::t("newDS", "Data Saved!");?>'
                            });
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            showImg(list){
                var viewer = new Viewer(document.getElementById(list.id),{
                    fullscreen: false,
                    title:false,
                    show:function(){ 
                        if(list.imgUrl.length==1){
                            $('.viewer-prev').hide()
                            $('.viewer-next').hide()
                        }
                    },
                    hide:function(){ 
                        viewer.destroy()
                    }
                });
                $("#"+list.id).click();
            }
        }
    })
    var config = {
        runtimes: 'html5,flash,html4', //上传模式,依次退化
        container: 'container', //上传区域DOM ID，默认是browser_button的父元素，
        browse_button: '', //上传选择的点选按钮，**必需**
        token: '',
        flash_swf_url: '/js/plupload/Moxie.swf', // flash的相对地址
        auto_start: true, //选择文件后自动上传，若关闭需要自己绑定事件触发上传
        // multi_selection: false, //不允许多选
        url:'https://upload-z1.qiniup.com',

        init: {
            'FilesAdded': function(up, files) {
                container.disabledUpload=true
                let fileType=files[0].type.split('/')
                if(fileType[0]=="image"){
                  container.loadingType=1
                }else{
                  container.loadingType=2
                }
                container.loadingList=files
                up.start();
            },
            BeforeUpload: function(up, file) {
                //设置参数
                let fileType=file.type.split('/')
                let token=fileType[0]=="video"?container.videoToken:container.token
                up.setOption({
                    multipart_params:{token:token}
                })
                if(fileType[0]=="image"){
                    container.uploadImgList.push({types:'1'})
                }else{
                    container.uploadLinkList.push({title: '<?php echo Yii::t("directMessage", "Uploading");?>'})  
                }
                container.loadingList.splice(0,1)
            },
            UploadProgress: function(up, file) {
                $('#progress').css('width', file.percent+'%').html(file.percent+'%');
            },
            'FileUploaded': function(up, file, info) {
                var response = eval('('+info.response+')');
                if(info.status == 200){
                    let fileType=response.data.mimetype.split('/')
                    if(fileType[0]=="image"){
                        container.uploadImgList.splice(container.uploadImgList.length-1,1)
                        container.uploadImgList.push(response.data)
                    }else{
                        response.data.isEdit=false
                        container.uploadLinkList.splice(container.uploadLinkList.length-1,1)
                        container.uploadLinkList.push(response.data)
                    }
                }
            },
            'Error': function(up, err, errTip) {
                if(err.response){
                    var response = eval('('+err.response+')');
                }
                else{
                    var response = {message: err.message};
                }
                if(response.error == 'token not specified'){
                    up.stop();
                    resultTip({
                        error: 'warning',
                        msg: err.message
                    });
                }
            },
            'UploadComplete': function(up, file) {
                //队列文件处理完毕后,处理相关的事情
            }
        }
    };
    var addconfig = {
        runtimes: 'html5,flash,html4', //上传模式,依次退化
        container: 'container', //上传区域DOM ID，默认是browser_button的父元素，
        browse_button: '', //上传选择的点选按钮，**必需**
        token: '',
        flash_swf_url: '/js/plupload/Moxie.swf', // flash的相对地址
        auto_start: true, //选择文件后自动上传，若关闭需要自己绑定事件触发上传
        // multi_selection: false, //不允许多选
        url:'https://upload-z1.qiniup.com',

        init: {
            'FilesAdded': function(up, files) {
                container.disabledUpload=true
                let fileType=files[0].type.split('/')
                if(fileType[0]=="image"){
                  container.addloadingType=1
                }else{
                  container.addloadingType=2
                }
                container.addloadingList=files
                up.start();
            },
            BeforeUpload: function(up, file) {
                let fileType=file.type.split('/')
                let token=fileType[0]=="video"?container.videoToken:container.token
                up.setOption({
                    multipart_params:{token:token}
                })
                if(fileType[0]=="image"){
                    container.adduploadImgList.push({types:'1'})
                }else{
                    container.adduploadLinkList.push({title: '<?php echo Yii::t("directMessage", "Uploading");?>'})  
                }
                container.addloadingList.splice(0,1)
            },
            UploadProgress: function(up, file) {
                $('#progress').css('width', file.percent+'%').html(file.percent+'%');
            },
            'FileUploaded': function(up, file, info) {
                var response = eval('('+info.response+')');
                if(info.status == 200){
                    let fileType=response.data.mimetype.split('/')
                    if(fileType[0]=="image"){
                        container.adduploadImgList.splice(container.adduploadImgList.length-1,1)
                        container.adduploadImgList.push(response.data)
                    }else{
                        response.data.isEdit=false
                        container.adduploadLinkList.splice(container.adduploadLinkList.length-1,1)
                        container.adduploadLinkList.push(response.data)
                    }
                }
            },
            'Error': function(up, err, errTip) {
                if(err.response){
                    var response = eval('('+err.response+')');
                }
                else{
                    var response = {message: err.message};
                }
                if(response.error == 'token not specified'){
                    up.stop();
                    resultTip({
                        error: 'warning',
                        msg: err.message
                    });
                }
            },
            'UploadComplete': function(up, file) {
                //队列文件处理完毕后,处理相关的事情
            }
        }
    };
    $(document).click(function(event) {
        container.qrcodeBox=false;
        container.scanQrcodeBox=false;
        container.itdevScanQrcodeBox=false;
        clearInterval(container.timer);
    });
</script>