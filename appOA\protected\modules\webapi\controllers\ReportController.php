<?php
class ReportController extends Controller
{
    //调用远程学期报告需要使用
    protected $securityKey = 'I3v4y7Onl8ine6Mi433ms';

    public function __construct($id, $module = null)
    {
        parent::__construct($id, $module);
        Yii::import('common.models.calendar.*');
        $childId = Yii::app()->request->getParam('childId');
        $token = Yii::app()->request->getParam('token');
        if (!$childId || !$token) {
            $this->module->_sendResponse(403);
        }
        if ($token != md5($childId . CommonUtils::SECURITY_KEY)) {
            $this->module->_sendResponse(403);
        }
    }

    // 生成幼儿园报告
    public function actionDownloadKG()
    {
        $id = Yii::app()->request->getParam('id');
        $childId = Yii::app()->request->getParam('childId');

        if (!$id || !$childId) {
            return false;
        }
        Yii::import('common.models.portfolio.*');
        $report = SReport::model()->findByAttributes(array('id' => $id, 'childid' => $childId));
        if ($report && $report->stat == 20) {
            if (!$report->pdf_file || !$report->custom) {
                Yii::import('common.components.teaching.*');
                $params = array(
                    'schoolid' => $report->schoolid,
                    'childid' => $report->childid,
                    'id' => $id,
                );
                if ($report->lang == 'en') {
                    Yii::app()->language = 'en_us';
                } else {
                    Yii::app()->language = 'zh_cn';
                }
                $version = 1;
                if (strtolower($report->template_id) == 'ivy03') {
                    $version = 2;
                }
                // IBS 学校特殊处理
                if ($report->branch->group == 20 && ($report->startyear + $report->semester) > 2020) {
                    $version = 3;
                }
                if ($version == 1) {
                    $html = ContentFetcher::getSemesterReport($params);
                } elseif ($version == 2) {
                    $html = ContentFetcher::getSemesterReport2($params);
                } elseif ($version == 3) {
                    $html = ContentFetcher::getSemesterReport3($params);
                }
                $html = base64_encode($html);

                try {
                    $postParams = array(
                        'id' => $id,
                        'classid' => $report->classid,
                        'str' => $html,
                        'zoom' => 1.28,
                        'mac' => md5($id . $report->classid . $html . $this->securityKey)
                    );
                    $url = 'https://transfer.api.ivykids.cn/api/html2pdf';
                    $ch = curl_init();
                    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
                    curl_setopt($ch, CURLOPT_POST, 1);
                    curl_setopt($ch, CURLOPT_URL, $url);
                    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                        'Content-Type: application/json',
                    ));
                    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($postParams));
                    $result = curl_exec($ch);
                    curl_close($ch);
                    if ($result !== false) {
                        $this->addMessage('state', 'success');
                        $this->addMessage('data', $result);
                    } else {
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', "Curl 失败" . curl_error($ch));
                    }
                    curl_close($ch);
                    $this->showMessage();
                } catch (Exception $e) {
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', $e->getMessage());
                    $this->showMessage();
                }
            }
        }
    }

    // 生成小学报告
    public function actionDownloadES()
    {
        Yii::import('common.models.reportCards.*');
        Yii::import('common.components.teaching.*');

        $id = Yii::app()->request->getParam('id');
        $childId = Yii::app()->request->getParam('childId');
        $startyear = Yii::app()->getRequest()->getParam('startyear');

        if (!$id || !$childId || !$startyear) {
            return false;
        }
        $currentReport = ReportsData::model()->findByPk($id);

        $startyear = $currentReport->startyear;
        $semester = $currentReport->semester;
        $flag = $startyear + $semester;

        $html = '';
        if ($currentReport) {

            $classid = $currentReport->class_id;
            $classModel = IvyClass::model()->findByPk($classid);

            $params = array(
                'schoolid' => $classModel->schoolid,
                'childid' => $childId,
                'classid' => $classid,
                'id' => $currentReport->id,
                'flag' => '',
            );
            // 生成中文报告
            Yii::app()->language = 'zh_cn';
            $html = ContentFetcher::getSemesterReportOfDs($params);
            $html = base64_encode($html);

            if ($flag < (2024+2)) {
                $postParams = array(
                    'id' => $id,
                    'classid' => $classid,
                    'lang' => 'cn',
                    'str' => $html,
                    'zoom' => 1.28,
                    'mac' => md5($id . $classid . $html . $this->securityKey)
                );
            } else {
                $coverUrl = $this->getCoverUrl($id, $childId, 'es', Yii::app()->language);
                $headerUrl = $this->getHeaderUrl($id, $childId, 'es', Yii::app()->language);
                $footerUrl = $this->getFooterUrl(Yii::app()->language);
                $postParams = array(
                    'str' => $html,
                    'headerHtml' => $headerUrl,
                    'footerHtml' => $footerUrl,
                    'coverHtml' => $coverUrl,
                    'marginTop' => '30mm',
                    'marginBottom' => '30mm',
                    'id' => $id,
                    'classid' => $classid,
                    'lang' => 'cn',
                    'mac' => md5($id . $classid . $html . $this->securityKey)
                );
            }

            $message = "";
            $data = '';
            $resCn = $this->remote($postParams);
            if ($resCn !== false) {
                $data = $resCn;
                $message .= "cn success;";
            } else {
                $message .= "cn fail;";
            }
            // 生成英文报告
            Yii::app()->language = 'en_us';
            $html = ContentFetcher::getSemesterReportOfDs($params);
            $html = base64_encode($html);
            $postParams['lang'] = 'en';
            $postParams['str'] = $html;
            $postParams['mac'] = md5($id . $classid . $html . $this->securityKey);
            if (isset($postParams['coverHtml'])) {
                $postParams['coverHtml'] = $this->getCoverUrl($id, $childId, 'es', Yii::app()->language);
                $postParams['headerHtml'] = $this->getHeaderUrl($id, $childId, 'es', Yii::app()->language);
                $postParams['footerHtml'] = $this->getFooterUrl(Yii::app()->language);
            }
            $resEn = $this->remote($postParams);
            if ($resEn !== false) {
                $data = $resEn;
                $message .= "en success;";
            } else {
                $message .= "en fail;";
            }
            if ($resCn || $resEn) {
                $state = 'success';
            } else {
                $state = 'fail';
            }
            $this->addMessage('state', $state);
            $this->addMessage('data', $data);
            $this->addMessage("message", $message);
            $this->showMessage();
        }
    }

    // 生成中学报告
    public function actionDownloadMS()
    {
        Yii::import('common.models.secondary.*');
        $reportChildId = Yii::app()->request->getParam('reportChildId');

        $schoolId = Yii::app()->request->getParam('schoolId');
        $report = AchievementReportChild::model()->findByPk($reportChildId);
        Yii::import('common.models.reportCards.*');
        if ($report) {
            $flag = $report->report->startyear + $report->report->cycle;

            $uniqueId = $report->id;
            $params = array(
                'id' => $report->report->id,
                'yid' => $report->calender,
                'childid' => $report->childid,
                'classid' => $report->classid,
                'courseid' => $report->id,
                'runJs' => 1,
            );
            Yii::import('common.components.teaching.*');
            // 设置用户ID为 1，用于去 API 拿数据过授权
            Yii::app()->user->setId(1);
            // 生成中文报告
            Yii::app()->language = 'zh_cn';
            $html = ContentFetcher::getReportOfDs($params, $schoolId);
            $html = base64_encode($html);
            if (in_array($report->report->cycle, array(1, 3)) || $flag < 2024 + 4) {
                $postParams = array(
                    'id' => $uniqueId,
                    'classid' => $params['classid'],
                    'lang' => 'cn',
                    'str' => $html,
                    'zoom' => 1.28,
                    'mac' => md5($uniqueId . $params['classid'] . $html . $this->securityKey)
                );
            } else {
                $coverUrl = $this->getCoverUrl($params['id'], $params['childid'], 'ms', Yii::app()->language);
                $headerUrl = $this->getHeaderUrl($params['id'], $params['childid'], 'ms', Yii::app()->language);
                $footerUrl = $this->getFooterUrl(Yii::app()->language);
                $postParams = array(
                    'str' => $html,
                    'headerHtml' => $headerUrl,
                    'footerHtml' => $footerUrl,
                    'coverHtml' => $coverUrl,
                    'marginTop' => '30mm',
                    'marginBottom' => '30mm',
                    'id' => $uniqueId,
                    'classid' => $params['classid'],
                    'lang' => 'cn',
                    'mac' => md5($uniqueId . $params['classid'] . $html . $this->securityKey)
                );
            }
            $message = "";
            $data = '';
            $resCn = $this->remote($postParams);
            if ($resCn !== false) {
                $data = $resCn;
                $message .= "cn success;";
            } else {
                $message .= "cn fail;";
            }
            // 生成英文报告
            Yii::app()->language = 'en_us';
            $html = ContentFetcher::getReportOfDs($params, $schoolId);
            $html = base64_encode($html);
            
            $postParams['lang'] = 'en';
            $postParams['str'] = $html;
            $postParams['mac'] = md5($uniqueId . $params['classid'] . $html . $this->securityKey);
            if (isset($postParams['coverHtml'])) {
                $postParams['coverHtml'] = $this->getCoverUrl($params['id'], $params['childid'], 'ms', Yii::app()->language);
                $postParams['headerHtml'] = $this->getHeaderUrl($params['id'], $params['childid'], 'ms', Yii::app()->language);
                $postParams['footerHtml'] = $this->getFooterUrl(Yii::app()->language);
            }
            $resEn = $this->remote($postParams);
            if ($resEn !== false) {
                $data = $resEn;
                $message .= "en success;";
            } else {
                $message .= "en fail;";
            }
            if ($resCn || $resEn) {
                $state = 'success';
            } else {
                $state = 'fail';
            }
            $this->addMessage('state', $state);
            $this->addMessage('data', $data);
            $this->addMessage("message", $message);
            $this->showMessage();
        }
    }

    public function remote($params)
    {
        try {
            $url = 'https://transfer.api.ivykids.cn/api/html2pdf';
            if (!CommonUtils::isProduction()) {
                $url = 'http://192.168.149.137:3000/html2pdf';
            }
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                'Content-Type: application/json',
            ));
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($params));
            $result = curl_exec($ch);
            curl_close($ch);
            return $result;
        } catch (Exception $e) {
            return false;
        }
    }

    function getCoverUrl($reportId, $childId, $type, $lang)
    {
        return Yii::app()->controller->createUrl('//reportHtml/cover', array(
            'rid' => $reportId,
            'childId' => $childId,
            'type' => $type,
            'lang' => $lang,
        ));
    }

    function getHeaderUrl($reportId, $childId, $type, $lang)
    {
        return Yii::app()->controller->createUrl('//reportHtml/header', array(
            'rid' => $reportId,
            'childId' => $childId,
            'type' => $type,
            'lang' => $lang,
        ));
    }

    function getFooterUrl($lang)
    {
        return Yii::app()->controller->createUrl('//reportHtml/footer', array('lang' => $lang));
    }

}
