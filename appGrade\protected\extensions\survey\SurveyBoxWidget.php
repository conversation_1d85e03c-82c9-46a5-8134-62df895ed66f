<?php
/*
 * 结构参见 http://www.cnblogs.com/davidhhuan/archive/2012/01/09/2316851.html
*/
class SurveyBoxWidget extends CWidget {

	/*
	 * 孩子ID
	*/
	public $childId = 0;
	/*
	 * 学校ID
	*/
	public $schoolId = 0;
	/*
	 * 问卷ID
	*/
	public $surveyId = 0;
	/*
	 * 资源(images css js)的主目录
	*/
	public $assetsHomeUrl = null;
	/*
	 * 脚本文件 true 为默认 null 为不加载脚本文件
	*/
	public $scriptFile = true;
	/*
	 * css 文件 true 为默认 null 为不加载css文件
	*/
	public $cssFile = true;
	/*
	 * 视图文件名称
	*/
	public $view = 'surveyBox';
	/*
	 * 初始化 设定属性的默认值
	* 此方法会被 CController::beginWidget() 调用
	*/
	public function init() {

		Yii::import('common.models.survey.*');
		if(empty($this->childId)){
			$this->childId  = $this->getController()->getChildId();
		}
		if(empty($this->schoolId)){
			$this->schoolId  = $this->getController()->getSchoolId();
		}
		$cs = Yii::app()->getClientScript();
		if(empty($this->assetsHomeUrl)){
			$this->assetsHomeUrl = Yii::app()->getAssetManager()->publish(dirname(__FILE__).'/assets',false, -1, YII_DEBUG);
		}
		if($this->scriptFile == true){
			$this->scriptFile = 'survey.js';
		}
		if($this->cssFile == true){
			$this->cssFile = 'survey.css';
		}
		if(!empty($this->scriptFile)){
			$cs->registerScriptFile($this->assetsHomeUrl.'/js/'.$this->scriptFile,CClientScript::POS_HEAD);
		}
		if(!empty($this->cssFile)){
			//$cs->registerCssFile($this->assetsHomeUrl.'/css/'.$this->cssFile);
//			$cs->registerCssFile(Yii::app()->theme->baseUrl.'/css/vote_survey.css');
			$cs->registerCssFile(Yii::app()->theme->baseUrl.'/css/vote_survey.css?t='.Yii::app()->params['refreshAssets']);
		}

	}

	/*
	 * 此方法会被 CController::endWidget() 调用
	*/
	public function run() {

            Mims::LoadHelper('HtoolKits');

            $today_timestamp = strtotime('today');
            $yesterday_timestamp = strtotime('yesterday');
            $sd_cri = new CDbCriteria();
            $sd_cri->compare('t.is_published', 1);
            $sd_cri->compare('t.schoolid', $this->schoolId);
            $sd_cri->addCondition('t.start_time <= ' . $today_timestamp);
            $sd_cri->addCondition('t.end_time > ' . $yesterday_timestamp);
            $sd_cri->compare('fbInfo.status', 0);
            $sd_cri->compare('fbInfo.is_fb', 0);
            $sd_cri->compare('fbInfo.childid', $this->childId);
            $sd_cri->compare('survey.respondents', 'parent');
            $sd_info = WSurveyDetail::model()->with('fbInfo')->findAll($sd_cri);

            $sd_has_fb_cri = new CDbCriteria();
            $sd_has_fb_cri->compare('t.is_published', 1);
            $sd_has_fb_cri->compare('t.schoolid', $this->schoolId);
            $sd_has_fb_cri->addCondition('t.start_time <= ' . $today_timestamp);
//            $sd_has_fb_cri->addCondition('t.end_time > ' . strtotime("+10days", $yesterday_timestamp));
            //临时解决办法 
            $sd_has_fb_cri->addCondition("TO_DAYS(curdate()) - TO_DAYS(FROM_UNIXTIME(t.end_time,'%Y-%m-%d')) <= 10");
            $sd_has_fb_cri->compare('fbInfo.status', 0);
            $sd_has_fb_cri->compare('fbInfo.is_fb', 1);
            $sd_has_fb_cri->compare('fbInfo.childid', $this->childId);
            $fb_info = WSurveyDetail::model()->with('fbInfo')->findAll($sd_has_fb_cri);
            $show = 1;
            if (empty($sd_info) && !empty($fb_info)){
                foreach ($fb_info as $val){
                    if ($val->survey->survey_type == Survey::SURVEY_TYPE_ONE && ($val->end_time - $yesterday_timestamp) / 86400 < 10) {
                        $show = 0;
                        break;
                    }
                }
            }
        /*
        if (!empty($sd_info)) {
            $has_fb_survey = true;
            $sd_info->end_time = Mims::formatDateTime($sd_info->end_time);
            $sd_info->survey->title = HtoolKits::getContentByLang($sd_info->survey->title_cn, $sd_info->survey->title_en);
        }
         * 
         */
            // 输出 变量到 视图文件
            $langKey = Yii::app()->language == 'en_us' ? 'en' : 'cn';
            $view_data = array(
                'fb_info' => $fb_info,
                'sd_info' => $sd_info,
                'langKey' => $langKey,
                'yesterday_timestamp' => $yesterday_timestamp,
                'show' => $show,
            );

            $this->render($this->view, $view_data);
        }

}