<?php

class WxpayController extends ProtectedController {

    public function init() {
        parent::init();
        Yii::import('common.models.invoice.*');
        Yii::import('common.models.alipay.*');
        Yii::import('common.models.wxpay.*');
    }

    public function actionOnlineBankInfo() {
        
        $this->renderPartial('/wxpay/onlineBankInfo', array(
                )
        );
    }

    public function actionCheckState()
    {
        if(Yii::app()->request->isPostRequest){
            $id = Yii::app()->request->getPost('id');
            if(!$id)Yii::app()->end();
            set_time_limit(360);
            $i=0;
            while ($i<300){
                sleep(1);
                $model = WechatPayOrder::model()->findByPk($id);
                if($model->status == 1){
                    echo CJSON::encode(array('state'=>'success'));
                    break;
                }
                $i++;
            }
        }
    }

    public function actionOnlineNotice() {

        $alipayBankInfo = Mims::LoadConfig('CfgAlipayBankInfo');

//        Yii::app()->clientScript->registerCssFile(Yii::app()->theme->baseUrl . '/css/notice.css?t=' . Yii::app()->params['refreshAssets']);
        // 输出 变量到 视图文件
        $view_data = array(
            'childId' => $this->getChildId(),
            'langKey' => Yii::app()->language == 'en_us' ? 'en' : 'cn',
            'alipayBankInfo' => $alipayBankInfo,
        );
        $this->renderPartial('/alipay/onlineNotice', $view_data);
    }

}
