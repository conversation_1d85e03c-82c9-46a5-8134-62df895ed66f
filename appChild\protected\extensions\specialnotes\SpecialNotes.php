<?php
class SpecialNotes extends CWidget{
	public $childId;
    public $yId;
    public $classId;
    public $weekNum;
    public $cssFile;
    public $assetsUrl;
	
	public function init(){
		Yii::import('ext.schoolnews.models.*');

//        if($this->assetsUrl===null)
//			$this->assetsUrl=Yii::app()->getAssetManager()->publish(dirname(__FILE__) . '/assets', false, -1, YII_DEBUG);

        $cs = Yii::app()->clientScript;
        if (false !== $this->cssFile)
        {
            if (null === $this->cssFile)
                //$this->cssFile = $this->assetsUrl . '/css.css';
                $this->cssFile = Yii::app()->theme->baseUrl.'/css/portfolio.css';
            $cs->registerCssFile($this->cssFile);
        }
	}
	
	public function run(){

        Mims::LoadHelper('HtoolKits');
        
        $criteria = new CDbCriteria();
        $criteria->compare('classid', $this->classId);
        $criteria->compare('yid', $this->yId);
        $criteria->compare('weeknum', $this->weekNum);
        $criteria->compare('stat', 20);
        $criteria->compare('childid', $this->childId);
        $CsNotesModel = NotesSpecial::model()->find($criteria);

        $sCMedia = array();
        if ($CsNotesModel){
            $criteria = new CDbCriteria();
            $criteria->compare('t.classid', $this->classId);
            $criteria->compare('t.yid', $this->yId);
            $criteria->compare('t.weeknum', $this->weekNum);
            $criteria->compare('t.childid', array(0, $this->childId));
            $criteria->compare('t.category', 'snotes');
            $sCMedia=ChildMediaLinks::model()->with('photoInfo')->findAll($criteria);
        }

        $this->render('specialnotes', array('sCMedia'=>$sCMedia));
	}
	
}