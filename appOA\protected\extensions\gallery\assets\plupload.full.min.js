(function(){var d=0,i=[],k={},g={},a={"<":"lt",">":"gt","&":"amp",'"':"quot","'":"#39"},j=/[<>&\"\']/g,b,c=window.setTimeout;function f(){this.returnValue=false}function h(){this.cancelBubble=true}(function(l){var m=l.split(/,/),n,p,o;for(n=0;n<m.length;n+=2){o=m[n+1].split(/ /);for(p=0;p<o.length;p++){g[o[p]]=m[n]}}})("application/msword,doc dot,application/pdf,pdf,application/pgp-signature,pgp,application/postscript,ps ai eps,application/rtf,rtf,application/vnd.ms-excel,xls xlb,application/vnd.ms-powerpoint,ppt pps pot,application/zip,zip,application/x-shockwave-flash,swf swfl,application/vnd.openxmlformats,docx pptx xlsx,audio/mpeg,mpga mpega mp2 mp3,audio/x-wav,wav,image/bmp,bmp,image/gif,gif,image/jpeg,jpeg jpg jpe,image/png,png,image/svg+xml,svg svgz,image/tiff,tiff tif,text/html,htm html xhtml,text/rtf,rtf,video/mpeg,mpeg mpg mpe,video/quicktime,qt mov,video/x-flv,flv,video/vnd.rn-realvideo,rv,text/plain,asc txt text diff log,application/octet-stream,exe");var e={STOPPED:1,STARTED:2,QUEUED:1,UPLOADING:2,FAILED:4,DONE:5,GENERIC_ERROR:-100,HTTP_ERROR:-200,IO_ERROR:-300,SECURITY_ERROR:-400,INIT_ERROR:-500,FILE_SIZE_ERROR:-600,FILE_EXTENSION_ERROR:-700,mimeTypes:g,extend:function(l){e.each(arguments,function(m,n){if(n>0){e.each(m,function(p,o){l[o]=p})}});return l},cleanName:function(l){var m,n;n=[/[\300-\306]/g,"A",/[\340-\346]/g,"a",/\307/g,"C",/\347/g,"c",/[\310-\313]/g,"E",/[\350-\353]/g,"e",/[\314-\317]/g,"I",/[\354-\357]/g,"i",/\321/g,"N",/\361/g,"n",/[\322-\330]/g,"O",/[\362-\370]/g,"o",/[\331-\334]/g,"U",/[\371-\374]/g,"u"];for(m=0;m<n.length;m+=2){l=l.replace(n[m],n[m+1])}l=l.replace(/\s+/g,"_");l=l.replace(/[^a-z0-9_\-\.]+/gi,"");return l},addRuntime:function(l,m){m.name=l;i[l]=m;i.push(m);return m},guid:function(){var l=new Date().getTime().toString(32),m;for(m=0;m<5;m++){l+=Math.floor(Math.random()*65535).toString(32)}return(e.guidPrefix||"p")+l+(d++).toString(32)},buildUrl:function(m,l){var n="";e.each(l,function(p,o){n+=(n?"&":"")+encodeURIComponent(o)+"="+encodeURIComponent(p)});if(n){m+=(m.indexOf("?")>0?"&":"?")+n}return m},each:function(o,p){var n,m,l;if(o){n=o.length;if(n===b){for(m in o){if(o.hasOwnProperty(m)){if(p(o[m],m)===false){return}}}}else{for(l=0;l<n;l++){if(p(o[l],l)===false){return}}}}},formatSize:function(l){if(l===b){return e.translate("N/A")}if(l>1048576){return Math.round(l/1048576,1)+" MB"}if(l>1024){return Math.round(l/1024,1)+" KB"}return l+" b"},getPos:function(m,q){var r=0,p=0,t,s=document,n,o;m=m;q=q||s.body;function l(z){var v,w,u=0,A=0;if(z){w=z.getBoundingClientRect();v=s.compatMode==="CSS1Compat"?s.documentElement:s.body;u=w.left+v.scrollLeft;A=w.top+v.scrollTop}return{x:u,y:A}}if(m.getBoundingClientRect&&(navigator.userAgent.indexOf("MSIE")>0&&s.documentMode!==8)){n=l(m);o=l(q);return{x:n.x-o.x,y:n.y-o.y}}t=m;while(t&&t!=q&&t.nodeType){r+=t.offsetLeft||0;p+=t.offsetTop||0;t=t.offsetParent}t=m.parentNode;while(t&&t!=q&&t.nodeType){r-=t.scrollLeft||0;p-=t.scrollTop||0;t=t.parentNode}return{x:r,y:p}},getSize:function(l){return{w:l.clientWidth||l.offsetWidth,h:l.clientHeight||l.offsetHeight}},parseSize:function(l){var m;if(typeof(l)=="string"){l=/^([0-9]+)([mgk]+)$/.exec(l.toLowerCase().replace(/[^0-9mkg]/g,""));m=l[2];l=+l[1];if(m=="g"){l*=1073741824}if(m=="m"){l*=1048576}if(m=="k"){l*=1024}}return l},xmlEncode:function(l){return l?(""+l).replace(j,function(m){return a[m]?"&"+a[m]+";":m}):l},toArray:function(n){var m,l=[];for(m=0;m<n.length;m++){l[m]=n[m]}return l},addI18n:function(l){return e.extend(k,l)},translate:function(l){return k[l]||l},addEvent:function(m,l,n){if(m.attachEvent){m.attachEvent("on"+l,function(){var o=window.event;if(!o.target){o.target=o.srcElement}o.preventDefault=f;o.stopPropagation=h;n(o)})}else{if(m.addEventListener){m.addEventListener(l,n,false)}}}};e.Uploader=function(o){var m={},r,q=[],s,n;r=new e.QueueProgress();o=e.extend({chunk_size:0,multipart:true,multi_selection:true,file_data_name:"file",filters:[]},o);function p(){var t;if(this.state==e.STARTED&&s<q.length){t=q[s++];if(t.status==e.QUEUED){t.status=e.UPLOADING;this.trigger("BeforeUpload",t);this.trigger("UploadFile",t)}else{p.call(this)}}else{this.stop()}}function l(){var u,t;r.reset();for(u=0;u<q.length;u++){t=q[u];if(t.size!==b){r.size+=t.size;r.loaded+=t.loaded}else{r.size=b}if(t.status==e.DONE){r.uploaded++}else{if(t.status==e.FAILED){r.failed++}else{r.queued++}}}if(r.size===b){r.percent=q.length>0?Math.ceil(r.uploaded/q.length*100):0}else{r.bytesPerSec=Math.ceil(r.loaded/((+new Date()-n||1)/1000));r.percent=r.size>0?Math.ceil(r.loaded/r.size*100):0}}e.extend(this,{state:e.STOPPED,features:{},files:q,settings:o,total:r,id:e.guid(),init:function(){var y=this,z,v,u,x=0,w;if(typeof(o.preinit)=="function"){o.preinit(y)}else{e.each(o.preinit,function(B,A){y.bind(A,B)})}o.page_url=o.page_url||document.location.pathname.replace(/\/[^\/]+$/g,"/");if(!/^(\w+:\/\/|\/)/.test(o.url)){o.url=o.page_url+o.url}o.chunk_size=e.parseSize(o.chunk_size);o.max_file_size=e.parseSize(o.max_file_size);y.bind("FilesAdded",function(A,D){var C,B,F=0,G,E=o.filters;if(E&&E.length){G=[];e.each(E,function(H){e.each(H.extensions.split(/,/),function(I){G.push("\\."+I.replace(new RegExp("["+("/^$.*+?|()[]{}\\".replace(/./g,"\\$&"))+"]","g"),"\\$&"))})});G=new RegExp(G.join("|")+"$","i")}for(C=0;C<D.length;C++){B=D[C];B.loaded=0;B.percent=0;B.status=e.QUEUED;if(G&&!G.test(B.name)){A.trigger("Error",{code:e.FILE_EXTENSION_ERROR,message:"File extension error.",file:B});continue}if(B.size!==b&&B.size>o.max_file_size){A.trigger("Error",{code:e.FILE_SIZE_ERROR,message:"File size error.",file:B});continue}q.push(B);F++}if(F){c(function(){y.trigger("QueueChanged");y.refresh()})}else{return false}});if(o.unique_names){y.bind("UploadFile",function(A,B){var D=B.name.match(/\.([^.]+)$/),C="tmp";if(D){C=D[1]}B.target_name=B.id+"."+C})}y.bind("UploadProgress",function(A,B){B.percent=B.size>0?Math.ceil(B.loaded/B.size*100):100;l()});y.bind("StateChanged",function(A){if(A.state==e.STARTED){n=(+new Date())}});y.bind("QueueChanged",l);y.bind("Error",function(A,B){if(B.file){B.file.status=e.FAILED;l();c(function(){p.call(y)})}});y.bind("FileUploaded",function(A,B){B.status=e.DONE;B.loaded=B.size;A.trigger("UploadProgress",B);c(function(){p.call(y)})});if(o.runtimes){v=[];w=o.runtimes.split(/\s?,\s?/);for(z=0;z<w.length;z++){if(i[w[z]]){v.push(i[w[z]])}}}else{v=i}function t(){var D=v[x++],C,A,B;if(D){C=D.getFeatures();A=y.settings.required_features;if(A){A=A.split(",");for(B=0;B<A.length;B++){if(!C[A[B]]){t();return}}}D.init(y,function(E){if(E&&E.success){y.features=C;y.trigger("Init",{runtime:D.name});y.trigger("PostInit");y.refresh()}else{t()}})}else{y.trigger("Error",{code:e.INIT_ERROR,message:"Init error."})}}t();if(typeof(o.init)=="function"){o.init(y)}else{e.each(o.init,function(B,A){y.bind(A,B)})}},refresh:function(){this.trigger("Refresh")},start:function(){if(this.state!=e.STARTED){s=0;this.state=e.STARTED;this.trigger("StateChanged");p.call(this)}},stop:function(){if(this.state!=e.STOPPED){this.state=e.STOPPED;this.trigger("StateChanged")}},getFile:function(u){var t;for(t=q.length-1;t>=0;t--){if(q[t].id===u){return q[t]}}},removeFile:function(u){var t;for(t=q.length-1;t>=0;t--){if(q[t].id===u.id){return this.splice(t,1)[0]}}},splice:function(v,t){var u;u=q.splice(v===b?0:v,t===b?q.length:t);this.trigger("FilesRemoved",u);this.trigger("QueueChanged");return u},trigger:function(u){var w=m[u.toLowerCase()],v,t;if(w){t=Array.prototype.slice.call(arguments);t[0]=this;for(v=0;v<w.length;v++){if(w[v].func.apply(w[v].scope,t)===false){return false}}}return true},bind:function(t,v,u){var w;t=t.toLowerCase();w=m[t]||[];w.push({func:v,scope:u||this});m[t]=w},unbind:function(t,v){var w=m[t.toLowerCase()],u;if(w){for(u=w.length-1;u>=0;u--){if(w[u].func===v){w.splice(u,1)}}}}})};e.File=function(o,m,n){var l=this;l.id=o;l.name=m;l.size=n;l.loaded=0;l.percent=0;l.status=0};e.Runtime=function(){this.getFeatures=function(){};this.init=function(l,m){}};e.QueueProgress=function(){var l=this;l.size=0;l.loaded=0;l.uploaded=0;l.failed=0;l.queued=0;l.percent=0;l.bytesPerSec=0;l.reset=function(){l.size=l.loaded=l.uploaded=l.failed=l.queued=l.percent=l.bytesPerSec=0}};e.runtimes={};window.plupload=e})();(function(b){var c={};function a(j,f,m,l,d){var n,h,g,i;h=google.gears.factory.create("beta.canvas");try{h.decode(j);i=Math.min(f/h.width,m/h.height);if(i<1){h.resize(Math.round(h.width*i),Math.round(h.height*i));return h.encode(d,{quality:l/100})}}catch(k){}return j}b.runtimes.Gears=b.addRuntime("gears",{getFeatures:function(){return{dragdrop:true,jpgresize:true,pngresize:true,chunks:true,progress:true,multipart:true}},init:function(g,i){var h;if(!window.google||!google.gears){return i({success:false})}try{h=google.gears.factory.create("beta.desktop")}catch(f){return i({success:false})}function d(k){var j,e,l=[],m;for(e=0;e<k.length;e++){j=k[e];m=b.guid();c[m]=j.blob;l.push(new b.File(m,j.name,j.blob.length))}g.trigger("FilesAdded",l)}g.bind("PostInit",function(){var j=g.settings,e=document.getElementById(j.drop_element);if(e){b.addEvent(e,"dragover",function(k){h.setDropEffect(k,"copy");k.preventDefault()});b.addEvent(e,"drop",function(l){var k=h.getDragData(l,"application/x-gears-files");if(k){d(k.files)}l.preventDefault()});e=0}b.addEvent(document.getElementById(j.browse_button),"click",function(o){var n=[],l,k,m;o.preventDefault();for(l=0;l<j.filters.length;l++){m=j.filters[l].extensions.split(",");for(k=0;k<m.length;k++){n.push("."+m[k])}}h.openFiles(d,{singleFile:!j.multi_selection,filter:n})})});g.bind("UploadFile",function(o,l){var q=0,p,m,n=0,k=o.settings.resize,e;if(k&&/\.(png|jpg|jpeg)$/i.test(l.name)){c[l.id]=a(c[l.id],k.width,k.height,k.quality||90,/\.png$/i.test(l.name)?"image/png":"image/jpeg")}l.size=c[l.id].length;m=o.settings.chunk_size;e=m>0;p=Math.ceil(l.size/m);if(!e){m=l.size;p=1}function j(){var v,x,s=o.settings.multipart,r=0,w={name:l.target_name||l.name},t=o.settings.url;function u(z){var y,E="----pluploadboundary"+b.guid(),B="--",D="\r\n",A,C;if(s){v.setRequestHeader("Content-Type","multipart/form-data; boundary="+E);y=google.gears.factory.create("beta.blobbuilder");b.each(b.extend(w,o.settings.multipart_params),function(G,F){y.append(B+E+D+'Content-Disposition: form-data; name="'+F+'"'+D+D);y.append(G+D)});C=b.mimeTypes[l.name.replace(/^.+\.([^.]+)/,"$1")]||"application/octet-stream";y.append(B+E+D+'Content-Disposition: form-data; name="'+o.settings.file_data_name+'"; filename="'+l.name+'"'+D+"Content-Type: "+C+D+D);y.append(z);y.append(D+B+E+B+D);A=y.getAsBlob();r=A.length-z.length;z=A}v.send(z)}if(l.status==b.DONE||l.status==b.FAILED||o.state==b.STOPPED){return}if(e){w.chunk=q;w.chunks=p}x=Math.min(m,l.size-(q*m));if(!s){t=b.buildUrl(o.settings.url,w)}v=google.gears.factory.create("beta.httprequest");v.open("POST",t);if(!s){v.setRequestHeader("Content-Disposition",'attachment; filename="'+l.name+'"');v.setRequestHeader("Content-Type","application/octet-stream")}b.each(o.settings.headers,function(z,y){v.setRequestHeader(y,z)});v.upload.onprogress=function(y){l.loaded=n+y.loaded-r;o.trigger("UploadProgress",l)};v.onreadystatechange=function(){var y;if(v.readyState==4){if(v.status==200){y={chunk:q,chunks:p,response:v.responseText,status:v.status};o.trigger("ChunkUploaded",l,y);if(y.cancelled){l.status=b.FAILED;return}n+=x;if(++q>=p){l.status=b.DONE;o.trigger("FileUploaded",l,{response:v.responseText,status:v.status})}else{j()}}else{o.trigger("Error",{code:b.HTTP_ERROR,message:"HTTP Error.",file:l,chunk:q,chunks:p,status:v.status})}}};if(q<p){u(c[l.id].slice(q*m,x))}}j()});i({success:true})}})})(plupload);(function(c){var a={};function b(l){var k,j=typeof l,h,e,g,f;if(j==="string"){k="\bb\tt\nn\ff\rr\"\"''\\\\";return'"'+l.replace(/([\u0080-\uFFFF\x00-\x1f\"])/g,function(n,m){var i=k.indexOf(m);if(i+1){return"\\"+k.charAt(i+1)}n=m.charCodeAt().toString(16);return"\\u"+"0000".substring(n.length)+n})+'"'}if(j=="object"){e=l.length!==h;k="";if(e){for(g=0;g<l.length;g++){if(k){k+=","}k+=b(l[g])}k="["+k+"]"}else{for(f in l){if(l.hasOwnProperty(f)){if(k){k+=","}k+=b(f)+":"+b(l[f])}}k="{"+k+"}"}return k}if(l===h){return"null"}return""+l}function d(o){var r=false,f=null,k=null,g,h,i,q,j,m=0;try{try{k=new ActiveXObject("AgControl.AgControl");if(k.IsVersionSupported(o)){r=true}k=null}catch(n){var l=navigator.plugins["Silverlight Plug-In"];if(l){g=l.description;if(g==="1.0.30226.2"){g="2.0.30226.2"}h=g.split(".");while(h.length>3){h.pop()}while(h.length<4){h.push(0)}i=o.split(".");while(i.length>4){i.pop()}do{q=parseInt(i[m],10);j=parseInt(h[m],10);m++}while(m<i.length&&q===j);if(q<=j&&!isNaN(q)){r=true}}}}catch(p){r=false}return r}c.silverlight={trigger:function(j,f){var h=a[j],g,e;if(h){e=c.toArray(arguments).slice(1);e[0]="Silverlight:"+f;setTimeout(function(){h.trigger.apply(h,e)},0)}}};c.runtimes.Silverlight=c.addRuntime("silverlight",{getFeatures:function(){return{jpgresize:true,pngresize:true,chunks:true,progress:true,multipart:true}},init:function(l,m){var k,h="",j=l.settings.filters,g,f=document.body;if(!d("2.0.31005.0")||(window.opera&&window.opera.buildNumber)){m({success:false});return}a[l.id]=l;k=document.createElement("div");k.id=l.id+"_silverlight_container";c.extend(k.style,{position:"absolute",top:"0px",background:l.settings.shim_bgcolor||"transparent",zIndex:99999,width:"100px",height:"100px",overflow:"hidden",opacity:l.settings.shim_bgcolor||document.documentMode>8?"":0.01});k.className="plupload silverlight";if(l.settings.container){f=document.getElementById(l.settings.container);f.style.position="relative"}f.appendChild(k);for(g=0;g<j.length;g++){h+=(h!=""?"|":"")+j[g].title+" | *."+j[g].extensions.replace(/,/g,";*.")}k.innerHTML='<object id="'+l.id+'_silverlight" data="data:application/x-silverlight," type="application/x-silverlight-2" style="outline:none;" width="1024" height="1024"><param name="source" value="'+l.settings.silverlight_xap_url+'"/><param name="background" value="Transparent"/><param name="windowless" value="true"/><param name="enablehtmlaccess" value="true"/><param name="initParams" value="id='+l.id+",filter="+h+'"/></object>';function e(){return document.getElementById(l.id+"_silverlight").content.Upload}l.bind("Silverlight:Init",function(){var i,n={};l.bind("Silverlight:StartSelectFiles",function(o){i=[]});l.bind("Silverlight:SelectFile",function(o,r,p,q){var s;s=c.guid();n[s]=r;n[r]=s;i.push(new c.File(s,p,q))});l.bind("Silverlight:SelectSuccessful",function(){if(i.length){l.trigger("FilesAdded",i)}});l.bind("Silverlight:UploadChunkError",function(o,r,p,s,q){l.trigger("Error",{code:c.IO_ERROR,message:"IO Error.",details:q,file:o.getFile(n[r])})});l.bind("Silverlight:UploadFileProgress",function(o,s,p,r){var q=o.getFile(n[s]);if(q.status!=c.FAILED){q.size=r;q.loaded=p;o.trigger("UploadProgress",q)}});l.bind("Refresh",function(o){var p,q,r;p=document.getElementById(o.settings.browse_button);q=c.getPos(p,document.getElementById(o.settings.container));r=c.getSize(p);c.extend(document.getElementById(o.id+"_silverlight_container").style,{top:q.y+"px",left:q.x+"px",width:r.w+"px",height:r.h+"px"})});l.bind("Silverlight:UploadChunkSuccessful",function(o,r,p,u,t){var s,q=o.getFile(n[r]);s={chunk:p,chunks:u,response:t};o.trigger("ChunkUploaded",q,s);if(q.status!=c.FAILED){e().UploadNextChunk()}if(p==u-1){q.status=c.DONE;o.trigger("FileUploaded",q,{response:t})}});l.bind("Silverlight:UploadSuccessful",function(o,r,p){var q=o.getFile(n[r]);q.status=c.DONE;o.trigger("FileUploaded",q,{response:p})});l.bind("FilesRemoved",function(o,q){var p;for(p=0;p<q.length;p++){e().RemoveFile(n[q[p].id])}});l.bind("UploadFile",function(o,q){var r=o.settings,p=r.resize||{};e().UploadFile(n[q.id],o.settings.url,b({name:q.target_name||q.name,mime:c.mimeTypes[q.name.replace(/^.+\.([^.]+)/,"$1")]||"application/octet-stream",chunk_size:r.chunk_size,image_width:p.width,image_height:p.height,image_quality:p.quality||90,multipart:!!r.multipart,multipart_params:r.multipart_params||{},headers:r.headers}))});m({success:true})})}})})(plupload);(function(c){var a={};function b(){var d;try{d=navigator.plugins["Shockwave Flash"];d=d.description}catch(f){try{d=new ActiveXObject("ShockwaveFlash.ShockwaveFlash").GetVariable("$version")}catch(e){d="0.0"}}d=d.match(/\d+/g);return parseFloat(d[0]+"."+d[1])}c.flash={trigger:function(f,d,e){setTimeout(function(){var j=a[f],h,g;if(j){j.trigger("Flash:"+d,e)}},0)}};c.runtimes.Flash=c.addRuntime("flash",{getFeatures:function(){return{jpgresize:true,pngresize:true,chunks:true,progress:true,multipart:true}},init:function(g,l){var k,f,h,e,m=0,d=document.body;if(b()<10){l({success:false});return}a[g.id]=g;k=document.getElementById(g.settings.browse_button);f=document.createElement("div");f.id=g.id+"_flash_container";c.extend(f.style,{position:"absolute",top:"0px",background:g.settings.shim_bgcolor||"transparent",zIndex:99999,width:"100%",height:"100%"});f.className="plupload flash";if(g.settings.container){d=document.getElementById(g.settings.container);d.style.position="relative"}d.appendChild(f);h="id="+escape(g.id);f.innerHTML='<object id="'+g.id+'_flash" width="100%" height="100%" style="outline:0" type="application/x-shockwave-flash" data="'+g.settings.flash_swf_url+'"><param name="movie" value="'+g.settings.flash_swf_url+'" /><param name="flashvars" value="'+h+'" /><param name="wmode" value="transparent" /><param name="allowscriptaccess" value="always" /></object>';function j(){return document.getElementById(g.id+"_flash")}function i(){if(m++>5000){l({success:false});return}if(!e){setTimeout(i,1)}}i();k=f=null;g.bind("Flash:Init",function(){var p={},o,n=g.settings.resize||{};e=true;j().setFileFilters(g.settings.filters,g.settings.multi_selection);g.bind("UploadFile",function(q,r){var s=q.settings;j().uploadFile(p[r.id],s.url,{name:r.target_name||r.name,mime:c.mimeTypes[r.name.replace(/^.+\.([^.]+)/,"$1")]||"application/octet-stream",chunk_size:s.chunk_size,width:n.width,height:n.height,quality:n.quality||90,multipart:s.multipart,multipart_params:s.multipart_params||{},file_data_name:s.file_data_name,format:/\.(jpg|jpeg)$/i.test(r.name)?"jpg":"png",headers:s.headers,urlstream_upload:s.urlstream_upload})});g.bind("Flash:UploadProcess",function(r,q){var s=r.getFile(p[q.id]);if(s.status!=c.FAILED){s.loaded=q.loaded;s.size=q.size;r.trigger("UploadProgress",s)}});g.bind("Flash:UploadChunkComplete",function(q,s){var t,r=q.getFile(p[s.id]);t={chunk:s.chunk,chunks:s.chunks,response:s.text};q.trigger("ChunkUploaded",r,t);if(r.status!=c.FAILED){j().uploadNextChunk()}if(s.chunk==s.chunks-1){r.status=c.DONE;q.trigger("FileUploaded",r,{response:s.text})}});g.bind("Flash:SelectFiles",function(q,t){var s,r,u=[],v;for(r=0;r<t.length;r++){s=t[r];v=c.guid();p[v]=s.id;p[s.id]=v;u.push(new c.File(v,s.name,s.size))}if(u.length){g.trigger("FilesAdded",u)}});g.bind("Flash:SecurityError",function(q,r){g.trigger("Error",{code:c.SECURITY_ERROR,message:"Security error.",details:r.message,file:g.getFile(p[r.id])})});g.bind("Flash:GenericError",function(q,r){g.trigger("Error",{code:c.GENERIC_ERROR,message:"Generic error.",details:r.message,file:g.getFile(p[r.id])})});g.bind("Flash:IOError",function(q,r){g.trigger("Error",{code:c.IO_ERROR,message:"IO error.",details:r.message,file:g.getFile(p[r.id])})});g.bind("QueueChanged",function(q){g.refresh()});g.bind("FilesRemoved",function(q,s){var r;for(r=0;r<s.length;r++){j().removeFile(p[s[r].id])}});g.bind("StateChanged",function(q){g.refresh()});g.bind("Refresh",function(q){var r,s,t;j().setFileFilters(g.settings.filters,g.settings.multi_selection);r=document.getElementById(q.settings.browse_button);s=c.getPos(r,document.getElementById(q.settings.container));t=c.getSize(r);c.extend(document.getElementById(q.id+"_flash_container").style,{top:s.y+"px",left:s.x+"px",width:t.w+"px",height:t.h+"px"})});l({success:true})})}})})(plupload);(function(a){a.runtimes.BrowserPlus=a.addRuntime("browserplus",{getFeatures:function(){return{dragdrop:true,jpgresize:true,pngresize:true,chunks:true,progress:true,multipart:true}},init:function(g,i){var e=window.BrowserPlus,h={},d=g.settings,c=d.resize;function f(n){var m,l,j=[],k,o;for(l=0;l<n.length;l++){k=n[l];o=a.guid();h[o]=k;j.push(new a.File(o,k.name,k.size))}if(l){g.trigger("FilesAdded",j)}}function b(){g.bind("PostInit",function(){var m,k=d.drop_element,o=g.id+"_droptarget",j=document.getElementById(k),l;function p(r,q){e.DragAndDrop.AddDropTarget({id:r},function(s){e.DragAndDrop.AttachCallbacks({id:r,hover:function(t){if(!t&&q){q()}},drop:function(t){if(q){q()}f(t)}},function(){})})}function n(){document.getElementById(o).style.top="-1000px"}if(j){if(document.attachEvent&&(/MSIE/gi).test(navigator.userAgent)){m=document.createElement("div");m.setAttribute("id",o);a.extend(m.style,{position:"absolute",top:"-1000px",background:"red",filter:"alpha(opacity=0)",opacity:0});document.body.appendChild(m);a.addEvent(j,"dragenter",function(r){var q,s;q=document.getElementById(k);s=a.getPos(q);a.extend(document.getElementById(o).style,{top:s.y+"px",left:s.x+"px",width:q.offsetWidth+"px",height:q.offsetHeight+"px"})});p(o,n)}else{p(k)}}a.addEvent(document.getElementById(d.browse_button),"click",function(v){var t=[],r,q,u=d.filters,s;v.preventDefault();for(r=0;r<u.length;r++){s=u[r].extensions.split(",");for(q=0;q<s.length;q++){t.push(a.mimeTypes[s[q]])}}e.FileBrowse.OpenBrowseDialog({mimeTypes:t},function(w){if(w.success){f(w.value)}})});j=m=null});g.bind("UploadFile",function(m,j){var l=h[j.id],r={},k=m.settings.chunk_size,n,o=[];function q(s,u){var t;if(j.status==a.FAILED){return}r.name=j.target_name||j.name;if(k){r.chunk=""+s;r.chunks=""+u}t=o.shift();e.Uploader.upload({url:m.settings.url,files:{file:t},cookies:document.cookies,postvars:a.extend(r,m.settings.multipart_params),progressCallback:function(x){var w,v=0;n[s]=parseInt(x.filePercent*t.size/100,10);for(w=0;w<n.length;w++){v+=n[w]}j.loaded=v;m.trigger("UploadProgress",j)}},function(w){var v,x;if(w.success){v=w.value.statusCode;if(k){m.trigger("ChunkUploaded",j,{chunk:s,chunks:u,response:w.value.body,status:v})}if(o.length>0){q(++s,u)}else{j.status=a.DONE;m.trigger("FileUploaded",j,{response:w.value.body,status:v});if(v>=400){m.trigger("Error",{code:a.HTTP_ERROR,message:"HTTP Error.",file:j,status:v})}}}else{m.trigger("Error",{code:a.GENERIC_ERROR,message:"Generic Error.",file:j,details:w.error})}})}function p(s){j.size=s.size;if(k){e.FileAccess.chunk({file:s,chunkSize:k},function(v){if(v.success){var w=v.value,t=w.length;n=Array(t);for(var u=0;u<t;u++){n[u]=0;o.push(w[u])}q(0,t)}})}else{n=Array(1);o.push(s);q(0,1)}}if(c&&/\.(png|jpg|jpeg)$/i.test(j.name)){BrowserPlus.ImageAlter.transform({file:l,quality:c.quality||90,actions:[{scale:{maxwidth:c.width,maxheight:c.height}}]},function(s){if(s.success){p(s.value.file)}})}else{p(l)}});i({success:true})}if(e){e.init(function(k){var j=[{service:"Uploader",version:"3"},{service:"DragAndDrop",version:"1"},{service:"FileBrowse",version:"1"},{service:"FileAccess",version:"2"}];if(c){j.push({service:"ImageAlter",version:"4"})}if(k.success){e.require({services:j},function(l){if(l.success){b()}else{i()}})}else{i()}})}else{i()}}})})(plupload);(function(d){var e,c;function b(g,h){var f;if("FileReader" in window){f=new FileReader();f.readAsDataURL(g);f.onload=function(){h(f.result)}}else{return h(g.getAsDataURL())}}function a(l,n,k,f,m){var h,g,j,i;b(l,function(o){h=document.createElement("canvas");h.style.display="none";document.body.appendChild(h);g=h.getContext("2d");j=new Image();j.onload=function(){var s,p,q,r,t;i=Math.min(n/j.width,k/j.height);if(i<1){s=Math.round(j.width*i);p=Math.round(j.height*i);h.width=s;h.height=p;g.drawImage(j,0,0,s,p);t=new c();t.init(atob(o.substring(o.indexOf("base64,")+7)));r=t.APP1({width:s,height:p});o=h.toDataURL(f);o=o.substring(o.indexOf("base64,")+7);o=atob(o);if(r){t.init(o);t.setAPP1(r);o=t.getBinary()}h.parentNode.removeChild(h);m({success:true,data:o})}else{m({success:false})}};j.src=o})}d.runtimes.Html5=d.addRuntime("html5",{getFeatures:function(){var k,g,j,h,f,i=window;g=j=h=f=false;if(i.XMLHttpRequest){k=new XMLHttpRequest();j=!!k.upload;g=!!(k.sendAsBinary||k.upload)}if(g){h=!!(File&&(File.prototype.getAsDataURL||i.FileReader)&&k.sendAsBinary);f=!!(File&&File.prototype.slice)}e=navigator.userAgent.indexOf("Safari")>0;return{html5:g,dragdrop:i.mozInnerScreenX!==undefined||f||e,jpgresize:h,pngresize:h,multipart:h||!!i.FileReader||!!i.FormData,progress:j,chunking:f||h}},init:function(i,j){var f={},g;function h(n){var l,k,m=[],o;for(k=0;k<n.length;k++){l=n[k];o=d.guid();f[o]=l;m.push(new d.File(o,l.fileName,l.fileSize))}if(m.length){i.trigger("FilesAdded",m)}}g=this.getFeatures();if(!g.html5){j({success:false});return}i.bind("Init",function(o){var s,q=[],n,r,l=o.settings.filters,m,p,k=document.body;s=document.createElement("div");s.id=o.id+"_html5_container";for(n=0;n<l.length;n++){m=l[n].extensions.split(/,/);for(r=0;r<m.length;r++){p=d.mimeTypes[m[r]];if(p){q.push(p)}}}d.extend(s.style,{position:"absolute",background:i.settings.shim_bgcolor||"transparent",width:"100px",height:"100px",overflow:"hidden",zIndex:99999,opacity:i.settings.shim_bgcolor?"":0});s.className="plupload html5";if(i.settings.container){k=document.getElementById(i.settings.container);k.style.position="relative"}k.appendChild(s);s.innerHTML='<input id="'+i.id+'_html5" style="width:100%;" type="file" accept="'+q.join(",")+'" '+(i.settings.multi_selection?'multiple="multiple"':"")+" />";document.getElementById(i.id+"_html5").onchange=function(){h(this.files);this.value=""}});i.bind("PostInit",function(){var k=document.getElementById(i.settings.drop_element);if(k){if(e){d.addEvent(k,"dragenter",function(o){var n,l,m;n=document.getElementById(i.id+"_drop");if(!n){n=document.createElement("input");n.setAttribute("type","file");n.setAttribute("id",i.id+"_drop");n.setAttribute("multiple","multiple");n.onchange=function(){h(this.files);this.value=""}}l=d.getPos(k,document.getElementById(i.settings.container));m=d.getSize(k);d.extend(n.style,{position:"absolute",display:"block",top:l.y+"px",left:l.x+"px",width:m.w+"px",height:m.h+"px",opacity:0});k.appendChild(n)});return}d.addEvent(k,"dragover",function(l){l.preventDefault()});d.addEvent(k,"drop",function(m){var l=m.dataTransfer;if(l&&l.files){h(l.files)}m.preventDefault()})}});i.bind("Refresh",function(k){var l,m,n;l=document.getElementById(i.settings.browse_button);m=d.getPos(l,document.getElementById(k.settings.container));n=d.getSize(l);d.extend(document.getElementById(i.id+"_html5_container").style,{top:m.y+"px",left:m.x+"px",width:n.w+"px",height:n.h+"px"})});i.bind("UploadFile",function(k,m){var n=k.settings,p,l;function o(q){var t=0,s=0;function r(){var A=q,H,I,D,E,F=0,w="----pluploadboundary"+d.guid(),z,B,x,y="--",G="\r\n",C="",v,u=k.settings.url;if(m.status==d.DONE||m.status==d.FAILED||k.state==d.STOPPED){return}E={name:m.target_name||m.name};if(n.chunk_size&&g.chunking){z=n.chunk_size;D=Math.ceil(m.size/z);B=Math.min(z,m.size-(t*z));if(typeof(q)=="string"){A=q.substring(t*z,t*z+B)}else{A=q.slice(t*z,B)}E.chunk=t;E.chunks=D}else{B=m.size}H=new XMLHttpRequest();I=H.upload;if(I){I.onprogress=function(J){m.loaded=Math.min(m.size,s+J.loaded-F);k.trigger("UploadProgress",m)}}if(!k.settings.multipart||!g.multipart){u=d.buildUrl(k.settings.url,E)}else{E.name=m.target_name||m.name}H.open("post",u,true);H.onreadystatechange=function(){var J,L;if(H.readyState==4){try{J=H.status}catch(K){J=0}if(J>=400){k.trigger("Error",{code:d.HTTP_ERROR,message:"HTTP Error.",file:m,status:J})}else{if(D){L={chunk:t,chunks:D,response:H.responseText,status:J};k.trigger("ChunkUploaded",m,L);s+=B;if(L.cancelled){m.status=d.FAILED;return}m.loaded=Math.min(m.size,(t+1)*z)}else{m.loaded=m.size}k.trigger("UploadProgress",m);if(!D||++t>=D){m.status=d.DONE;k.trigger("FileUploaded",m,{response:H.responseText,status:J});p=q=f[m.id]=null}else{r()}}H=A=x=C=null}};d.each(k.settings.headers,function(K,J){H.setRequestHeader(J,K)});if(k.settings.multipart&&g.multipart){if(!H.sendAsBinary){x=new FormData();d.each(d.extend(E,k.settings.multipart_params),function(K,J){x.append(J,K)});x.append(k.settings.file_data_name,A);H.send(x);return}H.setRequestHeader("Content-Type","multipart/form-data; boundary="+w);d.each(d.extend(E,k.settings.multipart_params),function(K,J){C+=y+w+G+'Content-Disposition: form-data; name="'+J+'"'+G+G;C+=unescape(encodeURIComponent(K))+G});v=d.mimeTypes[m.name.replace(/^.+\.([^.]+)/,"$1")]||"application/octet-stream";C+=y+w+G+'Content-Disposition: form-data; name="'+k.settings.file_data_name+'"; filename="'+unescape(encodeURIComponent(m.name))+'"'+G+"Content-Type: "+v+G+G+A+G+y+w+y+G;F=C.length-A.length;A=C}else{H.setRequestHeader("Content-Type","application/octet-stream")}if(H.sendAsBinary){H.sendAsBinary(A)}else{H.send(A)}}r()}p=f[m.id];l=k.settings.resize;if(g.jpgresize){if(l&&/\.(png|jpg|jpeg)$/i.test(m.name)){a(p,l.width,l.height,/\.png$/i.test(m.name)?"image/png":"image/jpeg",function(q){if(q.success){m.size=q.data.length;o(q.data)}else{o(p.getAsBinary())}})}else{o(p.getAsBinary())}}else{o(p)}});j({success:true})}});c=function(){var h,i,x,r,s,m,q,t,C,o,z,v,j,B,f,A,y,k,g;function n(){var G=false,E;function H(J,L){var I=G?0:-8*(L-1),M=0,K;for(K=0;K<L;K++){M|=(E.charCodeAt(J+K)<<Math.abs(I+K*8))}return M}function D(I,K,J){E=E.substr(0,I)+K+E.substr((J===true?K.length:0)+I)}function F(J,K,M){var N="",I=G?0:-8*(M-1),L;for(L=0;L<M;L++){N+=String.fromCharCode((K>>Math.abs(I+L*8))&255)}D(J,N,true)}return{II:function(I){if(I===f){return G}else{G=I}},init:function(I){E=I},SEGMENT:function(I,K,J){if(!arguments.length){return E}if(typeof K=="number"){return E.substr(parseInt(I,10),K)}D(I,K,J)},BYTE:function(I){return H(I,1)},SHORT:function(I){return H(I,2)},LONG:function(I,J){if(J===f){return H(I,4)}else{F(I,J,4)}},SLONG:function(I){var J=H(I,4);return(J>2147483647?J-4294967296:J)},STRING:function(I,J){var K="";for(J+=I;I<J;I++){K+=String.fromCharCode(H(I,1))}return K}}}C=new n();A={274:"Orientation",34665:"ExifIFDPointer",34853:"GPSInfoIFDPointer"};y={36864:"ExifVersion",40961:"ColorSpace",40962:"PixelXDimension",40963:"PixelYDimension",36867:"DateTimeOriginal",33434:"ExposureTime",33437:"FNumber",34855:"ISOSpeedRatings",37377:"ShutterSpeedValue",37378:"ApertureValue",37383:"MeteringMode",37384:"LightSource",37385:"Flash",41986:"ExposureMode",41987:"WhiteBalance",41990:"SceneCaptureType",41988:"DigitalZoomRatio",41992:"Contrast",41993:"Saturation",41994:"Sharpness"};k={0:"GPSVersionID",1:"GPSLatitudeRef",2:"GPSLatitude",3:"GPSLongitudeRef",4:"GPSLongitude"};g={ColorSpace:{1:"sRGB",0:"Uncalibrated"},MeteringMode:{0:"Unknown",1:"Average",2:"CenterWeightedAverage",3:"Spot",4:"MultiSpot",5:"Pattern",6:"Partial",255:"Other"},LightSource:{1:"Daylight",2:"Fliorescent",3:"Tungsten",4:"Flash",9:"Fine weather",10:"Cloudy weather",11:"Shade",12:"Daylight fluorescent (D 5700 - 7100K)",13:"Day white fluorescent (N 4600 -5400K)",14:"Cool white fluorescent (W 3900 - 4500K)",15:"White fluorescent (WW 3200 - 3700K)",17:"Standard light A",18:"Standard light B",19:"Standard light C",20:"D55",21:"D65",22:"D75",23:"D50",24:"ISO studio tungsten",255:"Other"},Flash:{0:"Flash did not fire.",1:"Flash fired.",5:"Strobe return light not detected.",7:"Strobe return light detected.",9:"Flash fired, compulsory flash mode",13:"Flash fired, compulsory flash mode, return light not detected",15:"Flash fired, compulsory flash mode, return light detected",16:"Flash did not fire, compulsory flash mode",24:"Flash did not fire, auto mode",25:"Flash fired, auto mode",29:"Flash fired, auto mode, return light not detected",31:"Flash fired, auto mode, return light detected",32:"No flash function",65:"Flash fired, red-eye reduction mode",69:"Flash fired, red-eye reduction mode, return light not detected",71:"Flash fired, red-eye reduction mode, return light detected",73:"Flash fired, compulsory flash mode, red-eye reduction mode",77:"Flash fired, compulsory flash mode, red-eye reduction mode, return light not detected",79:"Flash fired, compulsory flash mode, red-eye reduction mode, return light detected",89:"Flash fired, auto mode, red-eye reduction mode",93:"Flash fired, auto mode, return light not detected, red-eye reduction mode",95:"Flash fired, auto mode, return light detected, red-eye reduction mode"},ExposureMode:{0:"Auto exposure",1:"Manual exposure",2:"Auto bracket"},WhiteBalance:{0:"Auto white balance",1:"Manual white balance"},SceneCaptureType:{0:"Standard",1:"Landscape",2:"Portrait",3:"Night scene"},Contrast:{0:"Normal",1:"Soft",2:"Hard"},Saturation:{0:"Normal",1:"Low saturation",2:"High saturation"},Sharpness:{0:"Normal",1:"Soft",2:"Hard"},GPSLatitudeRef:{N:"North latitude",S:"South latitude"},GPSLongitudeRef:{E:"East longitude",W:"West longitude"}};function p(D,K){var F=C.SHORT(D),H,N,P,J,I,E,G,L,M=[],O={};for(H=0;H<F;H++){G=E=D+12*H+2;P=K[C.SHORT(G)];if(P===f){continue}J=C.SHORT(G+=2);I=C.LONG(G+=2);G+=4;M=[];switch(J){case 1:case 7:if(I>4){G=C.LONG(G)+B}for(N=0;N<I;N++){M[N]=C.BYTE(G+N)}break;case 2:if(I>4){G=C.LONG(G)+B}O[P]=C.STRING(G,I-1);continue;case 3:if(I>2){G=C.LONG(G)+B}for(N=0;N<I;N++){M[N]=C.SHORT(G+N*2)}break;case 4:if(I>1){G=C.LONG(G)+B}for(N=0;N<I;N++){M[N]=C.LONG(G+N*4)}break;case 5:G=C.LONG(G)+B;for(N=0;N<I;N++){M[N]=C.LONG(G+N*4)/C.LONG(G+N*4+4)}break;case 9:G=C.LONG(G)+B;for(N=0;N<I;N++){M[N]=C.SLONG(G+N*4)}break;case 10:G=C.LONG(G)+B;for(N=0;N<I;N++){M[N]=C.SLONG(G+N*4)/C.SLONG(G+N*4+4)}break;default:continue}L=(I==1?M[0]:M);if(g.hasOwnProperty(P)&&typeof L!="object"){O[P]=g[P][L]}else{O[P]=L}}return O}function w(){var D=t+4;B+=t;if(C.STRING(D,4).toUpperCase()!=="EXIF"||C.SHORT(D+=4)!==0){return}C.II(C.SHORT(D+=2)==18761);if(C.SHORT(D+=2)!==42){return}j=B+C.LONG(D+=2);h=p(j,A);z=("ExifIFDPointer" in h?B+h.ExifIFDPointer:f);v=("GPSInfoIFDPointer" in h?B+h.GPSInfoIFDPointer:f);return true}function l(D,I,H){var G=D.SHORT(H),F,E;for(E=0;E<G;E++){F=H+12*E+2;if(D.SHORT(F)==I){return F+8}}}function u(G,E){var F,H,I=z!=f?z-t:f,D=new n();D.init(q);D.II(C.II());if(I===f){return}F=l(D,40962,I);if(F!==f){D.LONG(F,G)}H=l(D,40963,I);if(H!==f){D.LONG(H,E)}q=D.SEGMENT()}return{init:function(D){B=10;h=i=x=r=s=m=q=t=o=f;C.init(D);if(C.SHORT(0)!==65496){return false}switch(C.SHORT(2)){case 65504:s=2;m=C.SHORT(4)+2;if(C.SHORT(m)==65505){t=m;o=C.SHORT(m+2)+2}break;case 65505:t=2;o=C.SHORT(4)+2;break;default:return false}if(o!==f){w()}},APP1:function(D){if(t===f&&o===f){return}q=q||(q=C.SEGMENT(t,o));if(D!==f&&"width" in D&&"height" in D){u(D.width,D.height)}return q},EXIF:function(){i=p(z,y);i.ExifVersion=String.fromCharCode(i.ExifVersion[0],i.ExifVersion[1],i.ExifVersion[2],i.ExifVersion[3]);return i},GPS:function(){x=p(v,k);x.GPSVersionID=x.GPSVersionID.join(".");return x},setAPP1:function(D){if(t!==f){return false}C.SEGMENT((s?s+m:2),D)},getBinary:function(){return C.SEGMENT()}}}})(plupload);(function(a){function b(c){return document.getElementById(c)}a.runtimes.Html4=a.addRuntime("html4",{getFeatures:function(){return{multipart:true}},init:function(c,d){c.bind("Init",function(m){var f=document.body,k,e="javascript",g,u,n,o=/MSIE/.test(navigator.userAgent),q=[],j=m.settings.filters,l,h,p,t;for(l=0;l<j.length;l++){h=j[l].extensions.split(/,/);for(t=0;t<h.length;t++){p=a.mimeTypes[h[t]];if(p){q.push(p)}}}q=q.join(",");function s(){var w,v,i;n=a.guid();w=document.createElement("form");w.setAttribute("id","form_"+n);w.setAttribute("method","post");w.setAttribute("enctype","multipart/form-data");w.setAttribute("encoding","multipart/form-data");w.setAttribute("target",m.id+"_iframe");w.style.position="absolute";v=document.createElement("input");v.setAttribute("id","input_"+n);v.setAttribute("type","file");v.setAttribute("accept",q);v.setAttribute("size",1);a.extend(v.style,{width:"100%",height:"100%",opacity:0});i=m.settings.shim_bgcolor;if(i){w.style.background=i}if(o){a.extend(v.style,{filter:"alpha(opacity=0)"})}a.addEvent(v,"change",function(A){var y=A.target,x,z=[];if(y.value){b("form_"+n).style.top=-1048575+"px";x=y.value.replace(/\\/g,"/");x=x.substring(x.length,x.lastIndexOf("/")+1);z.push(new a.File(n,x));s();if(z.length){c.trigger("FilesAdded",z)}}});w.appendChild(v);f.appendChild(w);m.refresh()}function r(){k=document.createElement("iframe");k.setAttribute("src",e+':""');k.setAttribute("id",m.id+"_iframe");k.setAttribute("name",m.id+"_iframe");k.style.display="none";f.appendChild(k);a.addEvent(k,"load",function(x){var y=x.target,w,i;if(!g){return}try{w=y.contentWindow.document||y.contentDocument||window.frames[y.id].document}catch(v){m.trigger("Error",{code:a.SECURITY_ERROR,message:"Security error.",file:g});return}i=w.documentElement.innerText||w.documentElement.textContent;if(i){g.status=a.DONE;g.loaded=1025;g.percent=100;m.trigger("UploadProgress",g);m.trigger("FileUploaded",g,{response:i})}});m.bind("UploadFile",function(i,w){var x,v;if(w.status==a.DONE||w.status==a.FAILED||i.state==a.STOPPED){return}x=b("form_"+w.id);v=b("input_"+w.id);v.setAttribute("name",i.settings.file_data_name);x.setAttribute("action",i.settings.url);a.each(a.extend({name:w.target_name||w.name},i.settings.multipart_params),function(A,z){var y=document.createElement("input");a.extend(y,{type:"hidden",name:z,value:A});x.insertBefore(y,x.firstChild)});g=w;b("form_"+n).style.top=-1048575+"px";x.submit();x.parentNode.removeChild(x)})}if(m.settings.container){f=b(m.settings.container);f.style.position="relative"}m.bind("StateChanged",function(i){if(i.state==a.STARTED){r()}if(i.state==a.STOPPED){window.setTimeout(function(){k.parentNode.removeChild(k)},0)}});m.bind("Refresh",function(i){var v,w,x;v=b(i.settings.browse_button);w=a.getPos(v,b(i.settings.container));x=a.getSize(v);a.extend(b("form_"+n).style,{top:w.y+"px",left:w.x+"px",width:x.w+"px",height:x.h+"px"})});c.bind("FilesRemoved",function(v,x){var w,y;for(w=0;w<x.length;w++){y=b("form_"+x[w].id);y.parentNode.removeChild(y)}});s()});d({success:true})}})})(plupload);