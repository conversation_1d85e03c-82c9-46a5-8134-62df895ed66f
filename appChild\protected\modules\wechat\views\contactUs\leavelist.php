<style>

    .weui-loadmore_line {
        border-top: 1px solid #E5E5E5;
        margin-top: 2.4em;
    }

    .weui-loadmore {
        width: 65%;
        margin: 1.5em auto;
        line-height: 1.6em;
        font-size: 14px;
        text-align: center;
    }

    .weui-loadmore_line .weui-loadmore__tips {
        position: relative;
        top: -0.9em;
        padding: 0 .55em;
        color: #999999;
    }

    .weui-loadmore__tips {
        display: inline-block;
        vertical-align: middle;
        background: #fbf9fe;
    }
</style>
<div class="weui_cells_title"><?php echo Yii::t("wechat", "leave list"); ?></div>
<?php if ($model) { ?>
    <div class="weui_cells weui_cells_access leaveListContainer">
        <div class="weui_cell">
            <div class="weui_cell_bd weui_cell_primary" style="font-size: 14px;color: #888;">
                <p style="list-style: none;padding-bottom: 5px"><?php echo Yii::t("wechat", "Cancellation Notice:"); ?></p>
                <ol style="margin:0;padding: 0; list-style-position:inside;">
                    <li style="padding-bottom: 5px"><?php echo Yii::t("wechat", "For cancellations of leave or late arrival before 7:00 am, otherwise please contact the campus."); ?></li>
                    <li style="padding-bottom: 5px"><?php echo Yii::t("wechat", "Two hours earlier is needed if you want to cancel the application, otherwise please contact with campus staff."); ?></li>
                </ol>
            </div>
        </div>
        <?php
        foreach ($model as $item): ?>
            <a class="weui_cell" href="javascript:;">
                <div class="weui_cell_bd weui_cell_primary">
                    <p>
                        <?php $config = ChildVacation::getConfig();
                        $config[40] = Yii::t('campus', 'Tardy');
                        if ($item->vacation_time_start == $item->vacation_time_end) {
                            echo date("Y/m/d", $item->vacation_time_start);
                            echo '</br> <div style="color:gray;font-size: 15px">' . $config[$item->type] . '</div>';

                        } else {
                            echo date("Y/m/d", $item->vacation_time_start) . " - " . date("Y/m/d", $item->vacation_time_end);
                            echo '</br> <div style="color:gray;font-size: 15px">' . $config[$item->type] . '</div>';
                        }
                        ?>
                    </p>
                </div>
                <?php
                $stats = 0;
                if (empty($childDailModel) || $childDailModel->sign_timestamp != $item->vacation_time_start) {
                    $data = strtotime(date("Y-m-d", $item->vacation_time_start) . " 7:00");
                    $typeList = array(ChildVacation::VACATION_SICK_LEAVE, ChildVacation::VACATION_AFFAIR_LEAVE, ChildVacation::VACATION_LATE);

                    if ($data > time() && in_array($item->type, $typeList)) {
                        $stats = 1;
                    }
                }
                $estData = ($item->est_begin_time) ? $item->est_begin_time - 7200 : 0;
                if ($estData > time() && ChildVacation::VACATION_LEAVE == $item->type) {
                    $stats = 1;
                }
                if ($stats) { ?>
                    <div class="cancelLeave weui_btn weui_btn_mini weui_btn_default" _value="<?php echo $item->id ?>">
                        <?php echo Yii::t("wechat", "Cancel"); ?>
                    </div>
                <?php } ?>
            </a>
        <?php endforeach;
        ?>
    </div>
<?php } else { ?>
    <div class="weui-loadmore weui-loadmore_line"><span
            class="weui-loadmore__tips"><?php echo Yii::t("wechat", "No data found!"); ?></span></div>
<?php } ?>

<div class="weui-loadmore weui-loadmore_line" id="noMoreData" style="display: none"><span
        class="weui-loadmore__tips"><?php echo Yii::t("wechat", "No more content"); ?></span></div>
<div class="weui_dialog_confirm" id="cancelLeaveDialog" style="display: none;">
    <div class="weui_mask"></div>
    <div class="weui_dialog">
        <div class="weui_dialog_hd"><strong class="weui_dialog_title"><?php echo Yii::t("wechat", "Cancel Leave or Late Arrival?"); ?></strong></div>
        <!--        <div class="weui_dialog_bd">自定义弹窗内容，居左对齐显示，告知需要确认的信息等</div>-->
        <div class="weui_dialog_ft">
            <a href="javascript:;" class="weui_btn_dialog default cancelLeaveDialogCancel"><?php echo Yii::t("wechat", "Cancel"); ?></a>
            <a href="javascript:;" class="weui_btn_dialog primary confirmBtn"><?php echo Yii::t("wechat", "Confirm"); ?></a>
        </div>
    </div>
</div>

<div id="loadingToast" class="weui_loading_toast" style="display: none;">
    <div class="weui_mask_transparent"></div>
    <div class="weui_toast">
        <div class="weui_loading">
            <div class="weui_loading_leaf weui_loading_leaf_0"></div>
            <div class="weui_loading_leaf weui_loading_leaf_1"></div>
            <div class="weui_loading_leaf weui_loading_leaf_2"></div>
            <div class="weui_loading_leaf weui_loading_leaf_3"></div>
            <div class="weui_loading_leaf weui_loading_leaf_4"></div>
            <div class="weui_loading_leaf weui_loading_leaf_5"></div>
            <div class="weui_loading_leaf weui_loading_leaf_6"></div>
            <div class="weui_loading_leaf weui_loading_leaf_7"></div>
            <div class="weui_loading_leaf weui_loading_leaf_8"></div>
            <div class="weui_loading_leaf weui_loading_leaf_9"></div>
            <div class="weui_loading_leaf weui_loading_leaf_10"></div>
            <div class="weui_loading_leaf weui_loading_leaf_11"></div>
        </div>
        <p class="weui_toast_content"><?php echo Yii::t("wechat", "Loading...Please Wait"); ?></p>
    </div>
</div>

<script>
    var _value,counter = 1,flag = true;
    var _model = <?php echo json_encode($model); ?>;
    $(document).on('click','.cancelLeave', function () {
        _value = $(this).attr('_value');
        $('#cancelLeaveDialog').show();
    });
    $('.cancelLeaveDialogCancel').on('click', function () {
        $('#cancelLeaveDialog').hide();
    });
    $(document).on('click','.confirmBtn', function () {
        var _this = this;
        if ($(_this).hasClass('disabled')) {
            return false;
        }
        $(_this).addClass('disabled');
        $.ajax({
            type: "POST",
            url: '<?php echo $this->createUrl('vacation'); ?>',
            data: {id: _value},
            dataType: 'json',
            success: function (data) {
                if (data.state == 'success') {
                    location.reload();
                } else {
                    alert(data.message);
                    $(_this).removeClass('disabled');
                    $('#cancelLeaveDialog').hide();
                }
            }
        });
    })

    var getMore = function () {
        if (0) return false;
        if ($('#loadingToast').css('display') != 'none') return false;
        if (_model.length < 20) return false;

        if (flag) {
            $('#loadingToast').show();
            $.ajax({
                type: "POST",
                url: "<?php echo $this->createUrl('pagination'); ?>",
                data: {offset: counter},
                dataType: 'json',
                success: function (data) {
                    if (!isEmptyArr(data)) {
                        var _content = '';
                        $.each(data, function (i, list) {
                            var _listFlag = true;
                            if(list.type == 10 ||  list.type == 20 || list.type == 40 ){
                                if(list.time > list.data){
                                    _listFlag = false;
                                }
                            }
                            if(list.type == 50){
                                if(parseInt(list.estData) < parseInt(list.time) || list.est_begin_time == 0){
                                    _listFlag = false;
                                }
                            }
                            if(_listFlag){
                                _content += '<a class="weui_cell" href="javascript:;"><div class="weui_cell_bd weui_cell_primary"><p>' + list.vacation_time_start + '<br></p><div style="color:gray;font-size: 15px">'+ list.typeName +'</div><p></p></div><div class="cancelLeave weui_btn weui_btn_mini weui_btn_default" _value="'+ list.id + '">取消</div></a>'
                            }else {
                                _content += '<a class="weui_cell" href="javascript:;"><div class="weui_cell_bd weui_cell_primary"><p>' + list.vacation_time_start + '<br></p><div style="color:gray;font-size: 15px">'+ list.typeName +'</div><p></p></div></a>'
                            }
                        });
                        $('.leaveListContainer').append(_content);
                    } else {
                        $('#noMoreData').show();
                        flag = false;
                    }
                    $('#loadingToast').hide();
                }
            });
            counter++;
        }
    };
    var isEmptyArr = function (arr) {
        if (arr.length == 0) {
            return true;
        } else {
            return false;
        }
    };
    $('.container').scroll(function () {
        if ($('.container').scrollTop() >= ($('.leaveListContainer').height() - $('.container').height())) {
            getMore();
        }
    });
</script>