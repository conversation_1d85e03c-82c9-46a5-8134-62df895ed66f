<?php

/**
 * UserIdentity represents the data needed to identity a user.
 * It contains the authentication method that checks if the provided
 * data can identity the user.
 */
class VisitorIdentity extends CUserIdentity
{
	private $_id;
	private $_childid;
	const ERROR_ACCESSKEY=10;
	const ERROR_WRONG_PASSCODE=11;
	const ERROR_WRONG_DATA=12;
	/**
	 * Authenticates a user.
	 * The example implementation makes sure if the username and password
	 * are both 'demo'.
	 * In practical applications, this should be changed to authenticate
	 * against some persistent user identity storage (e.g. database).
	 * @return boolean whether authentication succeeds.
	 */
	public function authenticate()
	{
		$childAccess=ChildAccess::model()->findByAttributes(array('accesskey'=>$this->username));
		if($childAccess===null){
		    $this->errorCode=self::ERROR_ACCESSKEY;
		}elseif($childAccess->childid){
		    $childBasic = ChildProfileBasic::model()->auth()->findByPk($childAccess->childid);
			if($childBasic->access_pass==$this->password){
				$this->_childid = $childAccess->childid;
				$this->errorCode=self::ERROR_NONE;
			}else{
				$this->errorCode=self::ERROR_WRONG_PASSCODE;
			}
		}else{
		    $this->errorCode=self::ERROR_WRONG_DATA;
		}
		return !$this->errorCode;
	}
	
	public function getChildId()
	{
		return $this->_childid;
	}
    
    /**
    * @return integer the ID of the user record
    */
	public function getId()
	{
		return $this->_id;
	}
}