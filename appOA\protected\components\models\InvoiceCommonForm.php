<?php

class InvoiceCommonForm extends CFormModel
{
	public $language;
    public $startdate;
    public $enddate;
	public $availableFees;
	public $policy;
    
    public $fData = array();
	
	
	public $invoiceType = '';
	public $itemModels = array();
	public $itemConfig = array();
	

	/**
	 * Declares the validation rules.
	 */
	public function rules()
	{
		return array(
			array('language', 'required'),
            array('startdate', 'required', 'on'=>'PAYGROUP_ANNUAL'),
            array('startdate', 'required', 'on'=>'PAYGROUP_SEMESTER'),
            array('startdate', 'required', 'on'=>'PAYGROUP_MONTH'),
            array('startdate', 'required', 'on'=>'FREE_LUNCH'),
            array('startdate, enddate', 'required', 'on'=>'HOLIDAY_MONTH'),
            array('startdate, enddate', 'required', 'on'=>'ENABLE_AFTERSCHOOLS'),
            array('startdate, enddate', 'required', 'on'=>'FREE_LUNCH'),
            array('startdate, enddate', 'safe'),
            array('availableFees', 'notEmpty'),
            array('startdate, enddate', 'schoolYearRange', 'on'=>array('ANNUAL_CHILDCARE_AMOUNT', 'PAYGROUP_ANNUAL', 'PAYGROUP_SEMESTER', 'HOLIDAY_MONTH')),
            array('startdate, enddate', 'dateCompare'),
		);
	}

	public function validate($attributes=null, $clearErrors=true){
		$result = parent::validate($attributes,$clearErrors);
		foreach( array_keys($this->itemModels) as $fkey){
			$result = $this->itemModels[$fkey]->validate() && $result;
		}
		return $result;
	}
	
	public function schoolYearRange($attribute, $params){
		if(!empty($this->$attribute)){
			$configs = $this->policy->configs;
			$minMonth = $configs['TIME_POINTS'][0];
			$maxMonth = $configs['TIME_POINTS'][3];
			$selectedMonth = intval(date('Ym', strtotime($this->$attribute)));
			if($selectedMonth < $minMonth || $selectedMonth > $maxMonth){
				$this->addError($attribute, Yii::t("payment", "Please select at date within this schoolyear"));
			}
		}
	}
    
	public function dateCompare($attribute, $params){
		if(!empty($this->startdate) && !empty($this->enddate)){
			if(strtotime($this->enddate) < strtotime($this->startdate)){
				$this->addError($attribute, Yii::t("payment", "End date should bigger than starting date"));
			}
		}
	}
	
    public function notEmpty($attribute, $params) {
        //if (!$this->hasErrors()) {
			$total = 0;
			if(count($this->itemModels)){
				foreach($this->itemModels as $m){
					if($m->feetype && $this->invoiceType == 'ENABLE_AFTERSCHOOLS'){
                        if ($m->feename != FEETYPE_BREAKFAST){
                            if( intval($m->project_num)!= $m->project_num || intval($m->project_num) < 1 ){
                                $this->addError("availableFees", Yii::t("payment", "Please input the correct number of afterschool class"));
                            }
                        }
					}
					$total += $m->feetype;
				}
			}
			if($total < 1){
				$this->addError("availableFees", Yii::t("payment", "Please select at least one invoice"));
			}
        //}
    }		
	/**
	 * Declares customized attribute labels.
	 * If not declared here, an attribute would have a label that is
	 * the same as its name with the first letter in upper case.
	 */
	public function attributeLabels()
	{
		return array(
			'language'=>Yii::t('invoice', 'Language'),
			'startdate'=>Yii::t('invoice', 'Start Date'),
			'enddate'=>Yii::t('invoice', 'End Date'),
			'availableFees'=>Yii::t('invoice', 'Fee Types'),
		);
	}
	
	public function renderExtraDates($targetEleId){
		if($this->invoiceType == "HOLIDAY_MONTH") return;
		$configs = $this->policy->configs;
		$thisMonth = date('Ym', time());
		$ret = "";
		if($thisMonth < $configs['TIME_POINTS'][0]){
			$newSchoolYear = date('Y-m-d', strtotime($configs['TIME_POINTS'][0] * 100 + 1));
			$ret .= CHtml::link(
				Yii::t("payment", "Beginning of school year (:date)", array(':date'=>$newSchoolYear)), "javascript:void(0);",
				array("class"=>"mr10 assitDate", "tId"=>$targetEleId, "a-date"=>$newSchoolYear)
			);
			$secondMonth = date('Y-m-d', strtotime((1 + $configs['TIME_POINTS'][0]) * 100 + 1));
			$ret .= CHtml::link(
				Yii::t("payment", "2nd Month (:date)", array(':date'=>$secondMonth)), "javascript:void(0);",
				array("class"=>"mr10 assitDate", "tId"=>$targetEleId, "a-date"=>$secondMonth)
			);
		}else{
			$today = date('Y-m-d', time());
			$nextMonday = date('Y-m-d', mktime(0,0,0,date('m'),date('d') + ( 8 - date('N')), date('Y')) );
			$nextMonth  = date('Y-m-d', mktime(0,0,0,date('m') + 1, 1, date('Y')) );
			$tMonth = date('Y-m-d', mktime(0,0,0,date('m'), 1, date('Y')) );
			$ret .= CHtml::link(
				Yii::t("payment", "本月初 (:date)", array(':date'=>$tMonth)), "javascript:void(0);",
				array("class"=>"mr10 assitDate", "tId"=>$targetEleId, "a-date"=>$tMonth)
			);
			$ret .= CHtml::link(
				Yii::t("payment", "Today (:date)", array(':date'=>$today)), "javascript:void(0);",
				array("class"=>"mr10 assitDate", "tId"=>$targetEleId, "a-date"=>$today)
			);
			$ret .= CHtml::link(
				Yii::t("payment", "Next Monday (:date)", array(':date'=>$nextMonday)), "javascript:void(0);",
				array("class"=>"mr10 assitDate", "tId"=>$targetEleId, "a-date"=>$nextMonday)
			);
			$ret .= CHtml::link(
				Yii::t("payment", "Next Month (:date)", array(':date'=>$nextMonth)), "javascript:void(0);",
				array("class"=>"mr10 assitDate", "tId"=>$targetEleId, "a-date"=>$nextMonth)
			);
			if (Yii::app()->params['siteFlag'] == 'daystar') {
                $ret .= CHtml::link(
                    Yii::t("payment", "2025.01.27"), "javascript:void(0);",
                    array("class"=>"mr10 assitDate", "tId"=>$targetEleId, "a-date"=>'2025-01-27')
                );
                $ret .= CHtml::link(
                    Yii::t("payment", "2025.02.10"), "javascript:void(0);",
                    array("class"=>"mr10 assitDate", "tId"=>$targetEleId, "a-date"=>'2025-02-10')
                );
            }
			
		}
		return $ret;
	}
	
	public function renderExtraDatesEnd($targetEleId){
			$today = date('Y-m-d', time());
			//$nextMonday = date('Y-m-d', mktime(0,0,0,date('m'),date('d') + ( 8 - date('N')), date('Y')) );
			$weekEnd = date('Y-m-d', mktime(0,0,0,date('m'),date('d') + ( 5 - date('N')) , date('Y')) );
			//$nextMonth  = date('Y-m-d', mktime(0,0,0,date('m') + 1, 1, date('Y')) );
			$monthEnd = date('Y-m-d', mktime(0,0,0,date('m') + 1, 0, date('Y')) );
			$tMonth = date('Y-m-d', mktime(0,0,0,date('m'), 1, date('Y')) );
			
			$ret .= CHtml::link(
				Yii::t("payment", "Today (:date)", array(':date'=>$today)), "javascript:void(0);",
				array("class"=>"mr10 assitDate", "tId"=>$targetEleId, "a-date"=>$today)
			);
			$ret .= CHtml::link(
				Yii::t("payment", "This Week End (:date)", array(':date'=>$weekEnd)), "javascript:void(0);",
				array("class"=>"mr10 assitDate", "tId"=>$targetEleId, "a-date"=>$weekEnd)
			);
			$ret .= CHtml::link(
				Yii::t("payment", "This Month End (:date)", array(':date'=>$monthEnd)), "javascript:void(0);",
				array("class"=>"mr10 assitDate", "tId"=>$targetEleId, "a-date"=>$monthEnd)
			);
			
			return $ret;
	}
    
    public function renderField($key='language', $fee='')
    {
		switch($key){
			case 'language':
				$ret = CHtml::activeRadioButtonList($this, 'language', $this->fData['title'], array('template'=>'<li>{input}{label}</li>', 'separator'=>''));
			break;
			case 'startdate':
				$ret = Yii::app()->getController()->widget('zii.widgets.jui.CJuiDatePicker', array(
							"model"=>$this,
							"attribute"=>"startdate",
							"options"=>array(
								'changeMonth'=>true,
								'changeYear'=>true,
								'dateFormat'=>'yy-mm-dd',
							),
							'htmlOptions'=>array('class'=>'mr10 input length_2 specialDatePicker'),
						), true);
				$ret .= $this->renderExtraDates(CHtml::getIdByName(CHtml::activeName($this, 'startdate')));
			break;
			case 'enddate':
				$ret = Yii::app()->getController()->widget('zii.widgets.jui.CJuiDatePicker', array(
							"model"=>$this,
							"attribute"=>"enddate",
							"options"=>array(
								'changeMonth'=>true,
								'changeYear'=>true,
								'dateFormat'=>'yy-mm-dd',
							),
							'htmlOptions'=>array('class'=>'mr10 input length_2 specialDatePicker'),
						), true);
				$ret .= $this->renderExtraDatesEnd(CHtml::getIdByName(CHtml::activeName($this, 'enddate')));
			break;
		}
		return $ret;
    }
	
	public function initialItem($invoiceType, $policy){
		$this->invoiceType = $invoiceType;
		$this->policy = $policy;
		$this->itemConfig = $this->getConfigs($this->invoiceType);
		
		foreach ( array_keys($this->itemConfig['INVOICE']) as $_k){
			$this->itemModels[$_k] = new InvoiceItemForm;
		}
	}
	
    public function getConfigs($key='PAYGROUP_ANNUAL'){
        $normalInvoice = array(
            'COMMON' => array(
                'language','startdate'
            ),
            'INVOICE' => array(
                FEETYPE_TUITION => array('feetype', 'feeamount', 'discount'),
                FEETYPE_LUNCH => array('feetype', 'feeamount' ),
                FEETYPE_SCHOOLBUS => array('feetype', 'feeamount'),
            ),
			'flag' => true
        );
        
        $ret = array(
            REG_DEPOSIT => array(
                'COMMON' => array(
                    'language',
                ),
                'INVOICE' => array(
                    FEETYPE_MATERIAL => array('feetype', 'feeamount'),
                    FEETYPE_DEPOSIT => array('feetype', 'feeamount'),
                )
            ),
            ANNUAL_CHILDCARE_AMOUNT => array(
                'COMMON' => array(
                    'language','startdate'
                ),
                'INVOICE' => array(
                    FEETYPE_CHILDCARE => array('feetype', 'feeamount'),
                )
            ),
            PAYGROUP_ANNUAL => $normalInvoice,
            PAYGROUP_SEMESTER => $normalInvoice,
            PAYGROUP_MONTH => $normalInvoice,
            HOLIDAY_MONTH => array(
                'COMMON' => array(
                    'language','startdate','enddate'
                ),
                'INVOICE' => array(
                    FEETYPE_TUITION => array('feetype', 'feeamount', 'discount'),
                    FEETYPE_LUNCH => array('feetype', 'feeamount'),
                    FEETYPE_SCHOOLBUS => array('feetype', 'feeamount'),
                ),
				'flag' => true
            ),
            ENABLE_AFTERSCHOOLS => array(
                'COMMON' => array(
                    'language','startdate','enddate'
                ),
                'INVOICE' => array(
                    FEETYPE_CAROUSEL => array('feetype', 'feeamount', 'project_num'),
                    FEETYPE_AFTERSCHOOL => array('feetype', 'feeamount', 'project_num'),
                )
            ),
            FREE_LUNCH => array(
                'COMMON' => array(
                    'language','startdate','enddate'
                ),
                'INVOICE' => array(
                    FEETYPE_LUNCH => array('feetype', 'service_info_show','feeamount'),
                )
            ),
            FEETYPE_LIBCARD => array(
                'COMMON' => array(
                    'language',
                ),
                'INVOICE' => array(
                    FEETYPE_LIBCARD => array('feetype', 'feeamount'),
                )
            ),
        );
        $configs = $this->policy->configs;
        //根据配置添加早餐费用
        if ($key == ENABLE_AFTERSCHOOLS && $configs['FEETYPE_BREAKFAST'] == TRUE){
            $ret[ENABLE_AFTERSCHOOLS]['INVOICE'][FEETYPE_BREAKFAST] = array('feetype', 'feeamount');
        }
		$result = isset($ret[$key]) ? $ret[$key] : $normalInvoice;
		if(isset($result['flag'])){
			foreach(array_keys($result['INVOICE']) as $_k){
				if(!in_array($_k, $configs[$key]['ITEMS']))
				unset($result['INVOICE'][$_k]);
			}
		}
		return $result;
    }	
	
	
}