<?php

/**
 * IVY MIK/IBS的学期报告结构模版
 */


$tree = array(

    //第一页；基本信息；需要挑选一张照片
    array(
        'sign' => 'FrontCover',
        'title' => Yii::t('teaching', 'Cover'),
        'items' => array(
            array(
                'sign' => 'FrontCoverWork',
                'title' => Yii::t('teaching', 'Creation'),
                'desc' => array(Yii::t('teaching', 'Please attach a photo of a creation completed by the child')),
                'content' => 'photo'
            )
        )
    ),

    //第二页：列举幼儿的两个优势智能；图文配对
    array(
        'sign' => 'Strength',
        'title' => Yii::t('teaching', 'Strengths'),
        'items' => array(
            array(
                'sign' => 'Strength1',
                'title' => Yii::t('teaching', 'Strength 1'),
                'desc' => array(
                    Yii::t('teaching', 'Please attach a photo displaying a child\'s strength like block building, drawing etc.'),
                    Yii::t('teaching', 'Write two - three sentences about the child\'s strength')
                ),
                'content' => 'learningDomain+photo+text'
            ),
            array(
                'sign' => 'Strength2',
                'title' => Yii::t('teaching', 'Strength 2'),
                'desc' => array(
                    Yii::t('teaching', 'Please attach a photo displaying a child\'s strength like block building, drawing etc.'),
                    Yii::t('teaching', 'Write two - three sentences about the child\'s strength')
                ),
                'content' => 'learningDomain+photo+text'
            )
        )
    ),

    //第三页：1. 列举幼儿的兴趣爱好；图文配对
    //       2. 概述
    array(
        'sign' => 'InterestComment',
        'title' => Yii::t('teaching', 'Interest & General Comment'),
        'items' => array(
            array(
                'title' => Yii::t('teaching', 'Interest'),
                'desc' => array(
                    Yii::t('teaching', 'Please attach a photo of the child engaged in his/her interest'),
                    Yii::t('teaching', 'Write two-three sentences about the child\'s interest')
                ),
                'sign' => 'Interest',
                'content' => 'photo+text'
            ),
            array(
                'title' => Yii::t('teaching', 'General Comment'),
                'desc' =>
                    array(Yii::t('teaching',
                        'Provide a 100 - 150 word description about the child\'s general growth over the course of the semester')),
                'sign' => 'Comment',
                'content' => 'text'
            )
        )
    ),

    //第四页：封底 封底图片，班级合影为佳
    array(
        'sign' => 'BackCover',
        'title' => Yii::t('teaching', 'Back Cover'),
        'items' => array(
            array(
                'title' => Yii::t('teaching', 'Class Photo'),
                'desc' =>  array(Yii::t('teaching', 'Please attach a class photo')),
                'sign' => 'BackCoverPhoto',
                'content' => 'photo+batchPhoto',
            )
        )
    ),
);


$learningDomains = array(
    1 => 'Physical & Health',
    2 => '身体发展与健康',
    3 => 'Personal',
    4 => '个性',
    5 => 'Language and Literacy',
    6 => '语言文字',
    7 => 'Art',
    8 => '艺术',
    9 => 'Social',
    10 => '社会性',
    11 => 'Social Studies',
    12 => '社会学习',
    13 => 'Mathematical Thinking',
    14 => '数学思维',
    15 => 'Scientific Thinking',
    16 => '科学思维',
);

return array(
    'tree' => $tree,
    'learningDomains' => $learningDomains
);
