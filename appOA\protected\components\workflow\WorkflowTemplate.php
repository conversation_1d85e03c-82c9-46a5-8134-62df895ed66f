<?php

/**
 * Description of WorkflowParam
 *
 * <AUTHOR>
 */
interface WorkflowTemplate{
    /**
     * 
    实现接口方法如下：
    private $template;
    public function setTemplate($name){
        $this->template = $name;
    }
    
    public function getTemplate(){
        return $this->template;
    }
    * 
    */
    
    public function setTemplate($name);
    public function getTemplate();
    public function initi();
    public function show();
    public function jumpMail();
    public function delete();
}
