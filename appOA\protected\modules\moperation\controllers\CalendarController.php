<?php

class CalendarController extends ProtectedController
{
    public $actionAccessAuths = array(
        'Index'             => 'o_X_Access',
        'SaveCalendar'      => 'o_X_Adm_Common',
        'TemplateSaveAs'    => 'o_X_Adm_Common',
        'SaveCalendarDays'  => 'o_X_Adm_Common',
//        'GetCalendarDays'   => 'o_X_Access',
        'DelCalendar'       => 'o_X_Adm_Common',
        'DelDay'            => 'o_X_Adm_Common',
        'AssignCalendar'    => 'o_X_Adm_Common',
        'UnassignCalendar'  => 'o_X_Adm_Common',
        'SetCurrentCalendar'=> 'o_X_Adm_Common',
    );

    public function init(){
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'hqOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site','HQ Operations');

        Yii::import('common.models.calendar.*');
    }

    /**
     * 校历主页面
     */
    public function actionIndex()
	{
        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/underscore-min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/backbone-min.js');
        $cs->registerCoreScript('jquery.ui');
        $cs->registerCssFile($cs->getCoreScriptUrl().'/jui/css/base/jquery-ui.css');
        $cs->registerScriptFile($cs->getCoreScriptUrl().'/jui/js/jquery-ui-i18n.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/phpfunc.js');
        $cs->registerCssFile(Yii::app()->theme->baseUrl.'/css/calendar.css');

        $this->render('index');
	}

    //新建校历模版或编辑校历模版
    public function actionSaveCalendar()
    {
        if(isset($_POST['Calendar'])){
            $yid = Yii::app()->request->getParam('yid', 0);
            $semester1 = strtotime($_POST['Calendar']['firstSemester'][0]);
            $semester2 = strtotime($_POST['Calendar']['firstSemester'][1]);
            $semester3 = strtotime($_POST['Calendar']['secondSemester'][0]);
            $semester4 = strtotime($_POST['Calendar']['secondSemester'][1]);
            $semesterStatus = 0;
            if($yid){
                $model = Calendar::model()->findByPk($yid);
                $items=CalendarSemester::model()->findAllByAttributes(array('yid'=>$yid));

                foreach($items as $item){
                    if($item->semester_flag == 10){
                        $item->school_start_timestamp = $semester1;
                        $item->school_end_timestamp = $semester2;
                    }
                    elseif($item->semester_flag == 20){
                        $item->school_start_timestamp = $semester3;
                        $item->school_end_timestamp = $semester4;

                    }
                    $item->save();
                }

                $timepoints = explode("," ,$model->timepoints);
                if(!in_array($semester1, $timepoints) || !in_array($semester2, $timepoints) || !in_array($semester3, $timepoints) || !in_array($semester4, $timepoints)){
                    $semesterStatus = 1;
                }
            } else{
                $model = new Calendar();
                $semesterStatus = 1;
            }

            $newStatus = 1;
            if($model->yid){
                $newStatus = 0;
            }

            $model->setScenario('nCalendar');

            $model->attributes = $_POST['Calendar'];
            if($model->isNewRecord)
                $model->created_timestamp = time();
            $model->userid = Yii::app()->user->id;
            $model->updated_timestamp = time();
            $model->stat = ($_POST['Calendar']['stat']) ? 10 : 20;
            if($semester1 && $semester2 && $semester3 && $semester4){
                $model->timepoints = implode(',', array($semester1, $semester2, $semester3, $semester4));
            }

            if($model->save()){
                $retData['yid'] = $model->yid;
                $retData['startyear'] = $model->startyear;
                $retData['title'] = $model->title;
                $retData['firstSemester'] = $model->firstSemester;
                $retData['secondSemester'] = $model->secondSemester;
                $retData['isNew'] = $yid ? false : true;
                $retData['isStatus'] = $model->stat;
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message','Data saved!'));

                if($semesterStatus) {
                    $schoolCalendar = new SchoolCalendar();
                    $schoolCalendar->generateCalendarWeek($model->yid, $semester1, $semester4);
                    if($newStatus){
                        $schoolCalendar->generateSchoolDayT($model->yid, array($semester1, $semester2, $semester3, $semester4), $newStatus);
                    }else{
                        $schoolCalendar->generateSchoolDay($model->yid, array($semester1, $semester2, $semester3, $semester4), $newStatus);
                    }
                }

                $crit = new CDbCriteria();
                $crit->compare('yid',$yid);
                $schoolDaysModel = CalendarSchoolDays::model()->findAll($crit);
                if($schoolDaysModel){
                    foreach ($schoolDaysModel as $val){
                        $retData['schoolDay'][$val->yid]['month'][$val->month] = $val->schoolday;
                        //$retData['schoolDay'][$val->yid]['semester'][$val->semester_flag] += $val->schoolday;
                        $firstSemester = date("Y-m", strtotime($retData['firstSemester'][1]));
                        $secondSemester = date("Y-m", strtotime($retData['secondSemester'][0]));
                        if($firstSemester == $secondSemester && $firstSemester == $val->month_label){
                            $autumn = 0;
                            $spring = 0;
                            foreach (explode(',', $val->schoolday_array) as $item){
                                $dataDay = $val->month_label . '-' . $item;
                                if($dataDay <= $retData['firstSemester'][1]){
                                    $autumn++;
                                }else{
                                    $spring++;
                                }
                            }
                            $retData['schoolDay'][$val->yid]['semester'][10] += $autumn;
                            $retData['schoolDay'][$val->yid]['semester'][20] += $spring;
                        }else{
                            $retData['schoolDay'][$val->yid]['semester'][$val->semester_flag] += $val->schoolday;
                        }
                        $retData['schoolDay'][$val->yid]['year'] += $val->schoolday;
                    }
                }

                if($yid){
                    $this->addMessage('data', $retData);
                    $this->addMessage('callback', 'cb');
                }
                else{
                    $this->addMessage('refresh', true);
                }

            }
            else{
                $this->addMessage('state', 'fail');
                $this->addMessage('error', $model->getErrors());
                $errs = current($model->getErrors());
                $this->addMessage('message', $errs?$errs[0]:Yii::t('message','Saving Failed!'));
            }

            $semesterid = array();
            if(!$yid){
                foreach(array(10, 20) as $stime){
                    $item = new CalendarSemester();
                    $item->yid=$model->yid;
                    $item->semester_flag=$stime;
                    $item->school_start_timestamp=$stime==10?$semester1:$semester3;
                    $item->school_end_timestamp=$stime==10?$semester2:$semester4;
                    $item->save();
                    $semesterid[$stime]=intval($item->sid);
                }
                $model->semester = serialize($semesterid);
                $model->save();
            }

            $this->showMessage();
        }
    }

    //校历模版另存
    public function actionTemplateSaveAs(){
        if(Yii::app()->request->isPostRequest){
            $yid = Yii::app()->request->getParam('fromyid', 0);
            $title = Yii::app()->request->getParam('title', '');
            $stat = Yii::app()->request->getParam('stat', '');
            $schoolCalendar = new SchoolCalendar();
            $model = $schoolCalendar->SaveCalendarAs($yid, $title,$stat);

            if($model){
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message','Data saved!'));
                $this->addMessage('callback', 'reshowC');
                $this->addMessage('data', array(
                    'startyear'=>$model->startyear,
                    'yid'=>$model->yid,
                    'title'=>$model->title,
                    'fromyid'=>$yid,
                    'isStatus'=>$model->stat,
                ));
            }
            else{
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('message','Saving Failed!'));
            }
            $this->showMessage();
        }
    }

    //保存校历日
    public function actionSaveCalendarDays(){
        $editingDays = Yii::app()->request->getParam('editingDays', '');
        if($editingDays && isset($_POST['CalendarDay']) && $_POST['CalendarDay']['yid']){
            $editingDaysArr = explode(',', $editingDays);
            $editingDayTime = array();
            foreach($editingDaysArr as $_time){
                $editingDayTime[] = strtotime($_time);
            }
            $criteria = new CDbCriteria();
            $criteria->compare('yid', $_POST['CalendarDay']['yid']);
            $criteria->compare('date_timestamp', $editingDayTime);
            $criteria->compare('schoolid', 0);
            $criteria->index = 'date_timestamp';
            $models = CalendarDay::model()->findAll($criteria);

            foreach($editingDayTime as $daytime){
                if($models[$daytime] === null){
                    $model = new CalendarDay();
                }else{
                    $model = $models[$daytime];
                }

                $model->attributes = $_POST['CalendarDay'];
                $model->date = date('Ymd', $daytime);
                $model->sid = 0;
                $model->date_timestamp = $daytime;
                $model->updated_timestamp = time();
                $model->userid = Yii::app()->user->id;
                if(!$model->save()){
                    $this->addMessage('state', 'fail');
                    $this->addMessage('error', $model->getErrors());
                    $errs = current($model->getErrors());
                    $this->addMessage('message', $errs?$errs[0]:Yii::t('message','Saving Failed!'));
                    break;
                }
                else{
                    $this->addMessage('state', 'success');
                    $this->addMessage('message', Yii::t('message','Data saved!'));
                    $data['data'][$model->date] = $model->attributes;
                    $data['data'][$model->date]['datestr'] = date('Y-m-d', $model->date_timestamp);
                    $data['data'][$model->date]['title_cn'] = CHtml::encode($model->title_cn);
                    $data['data'][$model->date]['memo_cn'] = CHtml::encode($model->memo_cn);
                    $data['data'][$model->date]['title_en'] = CHtml::encode($model->title_en);
                    $data['data'][$model->date]['memo_en'] = CHtml::encode($model->memo_en);
                    $data['data'][$model->date]['url'] = isset($branchid) ? $this->createUrl('//moperation/calendar/delDay', array('branchId' => $branchid, 'did' => $model->did, 'yid' => $model->yid)) : $this->createUrl('//moperation/calendar/delDay', array('did' => $model->did, 'yid' => $model->yid));

                    $this->addMessage('callback', 'cbc');

                    $schoolCalendar = new SchoolCalendar();
                    $schoolCalendar->updateSchoolDayForMonth($model->yid, date('Ym', $daytime));
                    $data['calendar'] = $schoolCalendar->showSchoolDayForMonth($model->yid);
                }
            }
            $this->addMessage('data', $data);
            $this->showMessage();
        }
    }

    //取某calendarId下calendar Days的数据;作权限时需注意，可能别的地方会调用
    public function actionGetCalendarDays(){
        $return = array();
        $yid = intval(Yii::app()->request->getParam('calendarId', 0));
        $branchid = Yii::app()->request->getParam('branchId', '');
        $startdate='';
        $enddate='';
        if($yid){
            Yii::import('common.models.calendar.*');
            $crit = new CDbCriteria();
            $crit->compare('yid',$yid);
            if ($branchid) {
                $crit->compare('schoolid', array('0', $branchid));
            }
            else {
                $crit->compare('schoolid', '0'); //剔除校园自定义的内容
            }
            $crit->order = 'date ASC';
            //$crit->index = 'date';
            $days = CalendarDay::model()->findAll($crit);
            foreach($days as $key=>$data){
                if($return[$data->date]){
                    if($branchid && $data->schoolid == $branchid){
                        $return[$data->date] = $data->getAttributes();
                    }
                }else{
                    $return[$data->date] = $data->getAttributes();
                    $return[$data->date]['datestr'] = date('Y-m-d', $data->date_timestamp);
                    $return[$data->date]['title_cn'] = CHtml::encode($data->title_cn);
                    $return[$data->date]['memo_cn'] = CHtml::encode($data->memo_cn);
                    $return[$data->date]['title_en'] = CHtml::encode($data->title_en);
                    $return[$data->date]['memo_en'] = CHtml::encode($data->memo_en);
                    $return[$data->date]['url'] = isset($branchid) ? $this->createUrl('//moperation/calendar/delDay', array('branchId' => $branchid, 'did' => $data->did, 'yid' => $data->yid)) : $this->createUrl('//moperation/calendar/delDay', array('did' => $data->did, 'yid' => $data->yid));
                }
            }

            $model = Calendar::model()->findByPk($yid);
            $timepoints = explode(',', $model->timepoints);
            $startdate1 = date("Y-m-d", $timepoints[0]);
            $startdate = $timepoints[0];
            $enddate1 = date("Y-m-d", $timepoints[3]);
            $enddate = $timepoints[3];

            $days = Yii::app()->db->createCommand()
                ->select('sum(schoolday) as s')
                ->from('ivy_calendar_schooldays')
                ->where('yid=:yid', array(':yid'=>$yid))
                ->queryRow();


        }

        $this->addMessage('state','success');
        $this->addMessage('data',array('cdata'=>$return, 'startdate'=>$startdate, 'startdate1' => $startdate1, 'enddate'=>$enddate, 'enddate1'=>$enddate1, 'schoolday'=>$days['s']));
        $this->showMessage();
    }

    /**
     * 删除校历
     */
    public function actionDelCalendar()
    {
        if(Yii::app()->request->isPostRequest){
            $yid = Yii::app()->request->getParam('yid', 0);
            if($yid){
                $count = CalendarSchool::model()->countByAttributes(array('yid'=>$yid));
                if($count){
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', '此校历已经分配到校园不能删除！');
                }
                else{
                    $count = IvyClass::model()->countByAttributes(array('yid'=>$yid));
                    if($count){
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', '此校历已经有班级不能删除！');
                    }
                    else{
                        Yii::import('common.models.invoice.Invoice');
                        $count = Invoice::model()->countByAttributes(array('calendar_id'=>$yid));
                        if($count){
                            $this->addMessage('state', 'fail');
                            $this->addMessage('message', '此校历已经有账单不能删除！');
                        }
                        else{
                            $model = Calendar::model()->findByPk($yid);
                            $startyear = $model->startyear;
                            if($model->delete()){
                                CalendarWeek::model()->deleteAllByAttributes(array('yid'=>$yid));
                                CalendarDay::model()->deleteAllByAttributes(array('yid'=>$yid));
                                CalendarSchoolDays::model()->deleteAllByAttributes(array('yid'=>$yid));
                                $this->addMessage('state', 'success');
                                $this->addMessage('data', array('startyear'=>$startyear));
                                $this->addMessage('message', '删除成功！');
                            }
                            else{
                                $this->addMessage('state', 'fail');
                                $this->addMessage('message', '删除失败！');
                            }
                        }
                    }
                }
                $this->showMessage();
            }
        }
    }

    /**
     * 删除校历中一天的内容
     */
    public function actionDelDay()
    {
        if(Yii::app()->request->isPostRequest){
            $did = Yii::app()->request->getParam('did', 0);
            $yid = Yii::app()->request->getParam('yid', 0);
            $branchId = Yii::app()->request->getParam('branchId', 0);

            if($did && $yid){
                $model = CalendarDay::model()->findByAttributes(array('did'=>$did, 'yid'=>$yid, 'schoolid' => $branchId));

                $event = $model->event;
                $day = $model->date;
                $mm = date('Ym', $model->date_timestamp);
                if($model->delete()){
                    $data = array();
                    if($event != 20){
                        $schoolCalendar = new SchoolCalendar();
                        $schoolCalendar->updateSchoolDayForMonth($yid, $mm);
                        $data['calendar'] = $schoolCalendar->showSchoolDayForMonth($model->yid);
                    }

                    $this->addMessage('state', 'success');
                    $this->addMessage('message', '删除成功！');
                    $this->addMessage('callback', 'deldayCallback');
                    $this->addMessage('data', array('day'=>$day, 'data' => $data));
                }
                else{
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', '删除失败！');
                }
                $this->showMessage();
            }
        }
    }

    /**
     * 指定校历给学校
     */
    public function actionAssignCalendar()
    {
        if(Yii::app()->request->isPostRequest){
            $yid        = Yii::app()->request->getParam('yid', 0);
            $schoolid   = Yii::app()->request->getParam('schoolid', '');

            if($yid && $schoolid){
                $yModel = Calendar::model()->findByPk($yid);
                $criteria = new CDbCriteria();
                $criteria->compare('yid', $yid);
                $criteria->compare('branchid', $schoolid);
                $count = CalendarSchool::model()->count($criteria);
                if(!$count){
                    $model = new CalendarSchool();
                    $model->yid = $yid;
                    $model->startyear = $yModel->startyear;
                    $model->branchid = $schoolid;
                    $model->is_selected = 0;
                    if($model->save()){
                        $this->addMessage('state', 'success');
                        $this->addMessage('message', '分配成功！');
                    }
                    else{
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', '分配失败！');
                    }
                }
                else{
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', '已经分配过了，请不要重复分配。');
                }
                $this->showMessage();
            }
        }
    }

    /**
     * 取消已分配的校历
     */
    public function actionUnassignCalendar()
    {
        if(Yii::app()->request->isPostRequest){
            $yid        = Yii::app()->request->getParam('yid', 0);
            $schoolid   = Yii::app()->request->getParam('schoolid', '');

            $count = IvyClass::model()->countByAttributes(array('yid'=>$yid, 'schoolid'=>$schoolid));
            if($count){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '此校历已经有班级不能删除！');
            }
            else{
                Yii::import('common.models.invoice.Invoice');
                $count = Invoice::model()->countByAttributes(array('calendar_id'=>$yid, 'schoolid'=>$schoolid));
                if($count){
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', '此校历已经有账单不能删除！');
                }
                else{
                    if(CalendarSchool::model()->deleteAllByAttributes(array('yid'=>$yid, 'branchid'=>$schoolid))){
                        $this->addMessage('state', 'success');
                        $this->addMessage('message', '删除成功！');
                    }
                    else{
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', '删除失败！');
                    }
                }
            }
            $this->showMessage();
        }
    }

    /**
     * 学校设为当前校历
     */
    public function actionSetCurrentCalendar()
    {
        if(Yii::app()->request->isPostRequest){
            $yid        = Yii::app()->request->getParam('yid', 0);
            $schoolid   = Yii::app()->request->getParam('schoolid', '');

            if($yid && $schoolid){
                $count = CalendarSchool::model()->countByAttributes(array('branchid'=>$schoolid, 'is_selected'=>1));
                if($count){
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', '此校园已经有默认校历，所以不能在此手动更换！');
                }
                else{
                    $model = CalendarSchool::model()->findByAttributes(array('yid'=>$yid, 'branchid'=>$schoolid));
                    $model->is_selected = 1;
                    if($model->save()){
                        $bModel = Branch::model()->findByPk($schoolid);
                        $bModel->schcalendar = $yid;
                        $bModel->save(false);
                        $this->addMessage('state', 'success');
                        $this->addMessage('message', '设置成功');
                    }
                    else{
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', '设置失败！');
                    }
                }
            }
            else{
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '参数失败！');
            }
            $this->showMessage();
        }
    }
}
