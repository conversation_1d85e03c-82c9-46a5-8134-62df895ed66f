<script>
    var canSubmit=true;
    function submitLogin(){
        if(canSubmit){
            canSubmit = !canSubmit;
            return true;
        }else{
            return false;
        }
    }
</script>
<div class="container">
    <div class="row vertical-offset-100">
        <div class="col-md-6 col-md-offset-3">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h3 class="panel-title"><span class="glyphicon glyphicon-user"></span> <?php echo Yii::t('user','Please sign in');?></h3>
                </div>
                <div class="panel-body">
                    <div class="mb10 logo-wrapper">
                        <img src="<?php echo Yii::app()->theme->baseUrl.'/images/logo.png'; ?>" class="img-responsive" alt="Responsive image">
                    </div>

                    <?php if ( Yii::app()->user->hasFlash('loginMessage')): ?>

                        <div class="well bg-warning">
                            <span class="text-danger">
                            <?php echo Yii::app()->user->getFlash('loginMessage');?>
                            </span>
                        </div>

                    <?php endif; ?>

                    <div class="form">
                        <?php echo CHtml::beginForm('', 'post', array('onsubmit'=>'return submitLogin()')); ?>
                            <fieldset>
                                <div class="form-group">
                                    <?php echo CHtml::activeTextField($model, 'username', array('class' => 'form-control', "placeholder"=>$model->getAttributeLabel('username'))) ?>
                                </div>
                                <div class="form-group">
                                    <?php echo CHtml::activePasswordField($model, 'password', array('class' => 'form-control', "placeholder"=>$model->getAttributeLabel('password'))) ?>
                                </div>
								<div class="form-group row">
									<div class='col-sm-6'>
										<div class="checkbox">
											<label>
												<?php echo CHtml::activeCheckBox($model, 'rememberMe'); ?>
												<?php echo $model->getAttributeLabel('rememberMe'); ?>
											</label>
										</div>
									</div>
									<div class='col-sm-6 text-right p10'>
										<?php echo CHtml::link(Yii::t('user', 'Lost Password?'), array('auth/lostpass'))?>
									</div>
									<div class='clearfix'></div>
								</div>
                                <?php echo CHtml::submitButton(Yii::t("user", "Login"), array('class' => 'btn btn-lg btn-success btn-block')); ?>
                            </fieldset>
                        <?php echo CHtml::endForm(); ?>
                    </div><!-- form -->
                </div>
                <div class="panel-footer">
                    <div class="text-right">
                        <img src="<?php echo Yii::app()->theme->baseUrl.'/images/logo_ig_small.png'?>">
                        an <a href="http://www.ivygroup.cn" target="_blank">IvyGroup</a> site
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    body{
        background: url('<?php echo Yii::app()->theme->baseUrl;?>/images/ds00<?php echo rand(1,4);?>.jpg');
        background-size: cover;
    }

</style>

<script>
    $(document).ready(function(){
        $('input.error').parents('div.form-group').addClass('has-error');

    });
</script>
