<?php
/**
 * Created by PhpStorm.
 * User: XINRAN
 * Date: 15-4-24
 * Time: 上午9:33
 */

class OSS {
    public $oss;
    public $debugMode=true;
    public $bucket = 'ivy-www-uploads';

    public function __construct($bucket=null) {
        if(!is_null($bucket)){
            $this->bucket = $bucket;
        }
        require(dirname(__FILE__).'/OSS/sdk.class.php');
        $this->oss = new ALIOSS();
        $this->oss->set_debug_mode($this->debugMode);
    }

    function processBatch($files, $rootUploadPath, $newPrefix="") {
        $rootUploadPath = rtrim($rootUploadPath, '/') . '/';
        if(isset($files['new']) && is_array($files['new']) ){
            foreach($files['new'] as $newFile) {
                $newFile = str_replace('//', '/', $newFile);
                $this->upload_by_file($newFile, $rootUploadPath, $newPrefix);
            }
        }
        if(isset($files['del']) && is_array($files['del']) ){
            foreach($files['del'] as $delFile) {
                $delFile = str_replace('//', '/', $delFile);
                $this->delete_object($delFile, $newPrefix);
            }
        }
    }

    //通过路径上传文件
    function upload_by_file($ossObjectPath, $uploadRootPath, $newPrefix=""){
        $file_path = $uploadRootPath . $ossObjectPath;
        $ossObjectPath = trim($newPrefix, '/') . '/' . trim($ossObjectPath);
        $ossObjectPath = trim($ossObjectPath, '/');
        if(file_exists($file_path)) {
            $response = $this->oss->upload_file_by_file($this->bucket,$ossObjectPath,$file_path);
            return $response;
        }
        return false;
    }

    //删除object
    function delete_object($ossObjectPath, $newPrefix=""){
        $ossObjectPath = trim($newPrefix, '/') . '/' . trim($ossObjectPath);
        $ossObjectPath = trim($ossObjectPath, '/');
        $response = $this->oss->delete_object($this->bucket,$ossObjectPath);
        return $response;
    }

    //检测object是否存在
    function is_object_exist($ossObjectPath){
        $response = $this->oss->is_object_exist($this->bucket,$ossObjectPath);
        return $response;
    }

    //拷贝object
    function copy_object($from_bucket,$from_object,$to_bucket,$to_object,$options=null){
        $response = $this->oss->copy_object($from_bucket,$from_object,$to_bucket,$to_object,$options);
        return $response;
    }

    //获取object
    function get_object($ossObjectPath){
        $options = array(
//            ALIOSS::OSS_FILE_DOWNLOAD => "d:\\cccccccccc.sh",
            //ALIOSS::OSS_CONTENT_TYPE => 'txt/html',
        );

        $response = $this->oss->get_object($this->bucket,$ossObjectPath,$options);
        return $response;
//        _format($response);
    }

    //获取bucket cors
    function  get_bucket_cors(){
        $response = $this->oss->get_bucket_cors($this->bucket);
        return $response;
    }

    function get_sign_url($ossObjectPath, $seconds = 3600){
        $response = $this->oss->get_sign_url($this->bucket,$ossObjectPath,$seconds);
        return $response;
    }

    //格式化返回结果
    function _format($response) {
        echo '|-----------------------Start---------------------------------------------------------------------------------------------------'."\n";
        echo '|-Status:' . $response->status . "\n";
        echo '|-Body:' ."\n";
        echo $response->body . "\n";
        echo "|-Header:\n";
        print_r ( $response->header );
        echo '-----------------------End-----------------------------------------------------------------------------------------------------'."\n\n";
    }


    /**
     * 上传文件
     * @param  [type] $fileName       [oss 存储路径]
     * @param  [type] $uploadRootPath [本地文件路径]
     * @return [type]                 [description]
     */
    function uploadFile($fileName, $uploadRootPath){
        $response = $this->oss->upload_file_by_file($this->bucket, $fileName, $uploadRootPath);
        if ($response->status == 200) {
            return true;
        }
        return false;
    }
} 
