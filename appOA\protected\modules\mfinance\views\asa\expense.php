<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo Yii::t('site','Finance Management');?></li>
        <li class="active">课后课财务</li>
    </ol>

    <div class="row">
        <!-- 左侧菜单 -->
        <div class="col-md-2 col-sm-2 mb10">
            <?php
            $this->widget('zii.widgets.CMenu', array(
                'items' => $this->getMenu(),
                'id' => 'pageCategory',
                'htmlOptions' => array('class' => 'nav nav-pills nav-stacked background-gray'),
                'activeCssClass' => 'active',
                'itemCssClass' => ''
            ));
            ?>
        </div>

        <div class="col-md-10 col-sm-12">
            <?php
            $this->widget('zii.widgets.CMenu', array(
                'items' => $this->ExpenseMen(),
                'id' => 'AttendMenu',
                'htmlOptions' => array('class' => 'nav nav-tabs'),
                'activeCssClass' => 'active',
                'itemCssClass' => ''
            ));?>
            <?php
            $this->widget('ext.ivyCGridView.BsCGridView', array(
                'id' => 'expense',
                'afterAjaxUpdate' => 'js:head.Util.modal',
                'dataProvider' => $expenseModel,
                'template' => "{items}{pager}",
                //状态为无效时标红
                //'rowCssClassExpression' => '( $data->status == 0 ? "active" : "" )',
                'colgroups' => array(
                    array(
                        "colwidth" => array(100, 100, 100, 100, 100, 100, 100, 100),
                    )
                ),
                'columns' => array(
                    array(
                        'name' => 'voucherid',
                        'value' => 'sprintf("%08d",$data->id)',
                    ),
                    array(
                        'name' => 'title',
                        'value' => '$data->title',
                    ),
                    array(
                        'name' => 'type',
                        'value' => '$data->type == 1 ? "报销" : "付款"',
                    ),
                    array(
                        'name' => 'branch',
                        'value' => array($this, "getBranch"),
                    ),
                    array(
                        'name' => 'amount',
                        'value' => '$data->amount',
                    ),
                    array(
                        'name' => 'expense_uid',
                        'value' => array($this, "getUserName"),
                    ),
                    array(
                        'name' => 'created',
                        'value' => 'date("Y-m-d", $data->created)',
                    ),
                    array(
                        'name' => Yii::t('asa','操作'),
                        'value' => array($this, "getExpenseButton"),
                    ),
                ),
            ));
            ?>
        </div>
    </div>
</div>
<script>
    var modal = '<div class="modal fade" id="modal" class="1123" tabindex="-1" role="dialog" aria-labelledby="modal"><div class="modal-dialog modal-lg" role="document"><div class="modal-content"></div></div></div>';
    $('body').append(modal);

    function cbUpdateExpense(){
        $('#modal').modal('hide');
        $.fn.yiiGridView.update('expense');
    }
</script>