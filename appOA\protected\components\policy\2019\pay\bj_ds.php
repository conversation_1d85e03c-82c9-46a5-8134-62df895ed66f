<?php
if(!defined("IVY_POLICY"))
throw new CException('DO NOT USE THIS FILE DIRECTLY');

return array( 'policy'=>array(

	//是否老生老办法
	ENABLE_CONSTANT_TUITION => true,
    //FENTAN_FACTOR => DAILY,
    //校园多收费摸式
    MORE_FEE_BY_SCHOOL => true,

	//是否开放课外活动账单
	ENABLE_AFTERSCHOOLS => false,

    NEWFUNCTION => true,

	//图书馆工本费
	FEETYPE_LIBCARD => 10,

	//学期四个时间点；取前后两端
    TIME_POINTS   => array(201908,202001,202002,202006),

	//年付开通哪些缴费类型
	 PAYGROUP_ANNUAL => array(
	 	ITEMS => array(
	 		FEETYPE_TUITION,
	 		FEETYPE_LUNCH,
	 		FEETYPE_SCHOOLBUS,
	 	),
	 	TUITION_CALC_BASE => BY_MONTH,
	 ),
//
//	//学期付开通哪些缴费类型
	 PAYGROUP_SEMESTER => array(
	 	ITEMS => array(
	 		FEETYPE_TUITION,
	 		FEETYPE_LUNCH,
	 		FEETYPE_SCHOOLBUS,
	 	),
	 	TUITION_CALC_BASE => BY_MONTH,
	 ),

	//月份开通哪些缴费类型
    /*
	PAYGROUP_MONTH => array(
		ITEMS => array(
			FEETYPE_TUITION,
			FEETYPE_LUNCH,
			FEETYPE_SCHOOLBUS,
		),
		TUITION_CALC_BASE => BY_MONTH,
	),
     *
     */

	//计算基准：BY_MONTH, 或者 BY_SETTING, 前者表示所有的学费都基于月费用计算，后者表示所有的学费都基于学期或学年
	TUITION_CALCULATION_BASE => array(
        'BY_TODDLER' =>array(
            BY_MONTH => array(
                //月收费金额
                AMOUNT => array(
                    FULL5D => 17000,
                ),
                //收费月份，及其权重
                MONTH_FACTOR => array(
                    201908=>0,
                    201909=>1,
                    201910=>1,
                    201911=>1,
                    201912=>1,
                    202001=>1,
                    202002=>1,
                    202003=>1,
                    202004=>1,
                    202005=>1,
                    202006=>1,
                ),
                //折扣
                GLOBAL_DISCOUNT => array(
                    DISCOUNT_ANNUAL => '0.94117:10:2019-10-01:round'
                ),
                LUNCH_PERDAY=> 800.8
            ),
        ),
        'BY_KINDERGARTEN' =>array(
            BY_MONTH => array(
                //月收费金额
                AMOUNT => array(
                    FULL5D => 20000,
                ),
                //收费月份，及其权重
                MONTH_FACTOR => array(
                    201908=>0,
                    201909=>1,
                    201910=>1,
                    201911=>1,
                    201912=>1,
                    202001=>1,
                    202002=>1,
                    202003=>1,
                    202004=>1,
                    202005=>1,
                    202006=>1,
                ),
                //折扣
                GLOBAL_DISCOUNT => array(
                    DISCOUNT_ANNUAL => '0.93:10:2019-10-01:round'
                ),
                LUNCH_PERDAY=> 800.8
            ),
        ),
        'BY_GRADE' =>array(
            BY_MONTH => array(
                //月收费金额
                AMOUNT => array(
                    FULL5D => 20000,
                ),
                //收费月份，及其权重
                MONTH_FACTOR => array(
                    201908=>0,
                    201909=>1,
                    201910=>1,
                    201911=>1,
                    201912=>1,
                    202001=>1,
                    202002=>1,
                    202003=>1,
                    202004=>1,
                    202005=>1,
                    202006=>1,
                ),
                //折扣
                GLOBAL_DISCOUNT => array(
                    DISCOUNT_ANNUAL => '0.93:10:2019-10-01:round'
                ),
                LUNCH_PERDAY=> 910
            ),
        ),
        'BY_MIDDLE' =>array(
            BY_MONTH => array(
                //月收费金额
                AMOUNT => array(
                    FULL5D => 21000,
//                    NEWSTUDENT => 15000,
                ),
                //收费月份，及其权重
                MONTH_FACTOR => array(
                    201908=>0,
                    201909=>1,
                    201910=>1,
                    201911=>1,
                    201912=>1,
                    202001=>1,
                    202002=>1,
                    202003=>1,
                    202004=>1,
                    202005=>1,
                    202006=>1,
                ),
                //折扣
                GLOBAL_DISCOUNT => array(
                    DISCOUNT_ANNUAL => '0.90476:10:2019-10-01:round'
                ),
                LUNCH_PERDAY=> 910
            ),
        ),
        'BY_HIGH' =>array(
            BY_MONTH => array(
                //月收费金额
                AMOUNT => array(
                    FULL5D => 21000,
//                    NEWSTUDENT => 15000,
                ),
                //收费月份，及其权重
                MONTH_FACTOR => array(
                    201908=>0,
                    201909=>1,
                    201910=>1,
                    201911=>1,
                    201912=>1,
                    202001=>1,
                    202002=>1,
                    202003=>1,
                    202004=>1,
                    202005=>1,
                    202006=>1,
                ),
                //折扣
                GLOBAL_DISCOUNT => array(
                    DISCOUNT_ANNUAL => '0.90476:10:2019-10-01:round'
                ),
                LUNCH_PERDAY=> 910
            ),
        ),
	),

	//每餐费用
	//LUNCH_PERDAY => 30,

	//账单到期日和账单开始日之差
	DUEDAYS_OFFSET => 0,

));