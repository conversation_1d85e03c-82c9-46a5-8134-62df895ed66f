<?php

/**
 * This is the model class for table "ivy_child_daily_sign".
 *
 * The followings are the available columns in table 'ivy_child_daily_sign':
 * @property integer $id
 * @property integer $childid
 * @property integer $classid
 * @property string $schoolid
 * @property integer $sign_timestamp
 * @property string $img
 * @property integer $uid
 * @property integer $update_timestamp
 */
class ChildDailySign extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_child_daily_sign';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('childid, classid, schoolid, sign_timestamp, uid, update_timestamp', 'required'),
			array('childid, classid, sign_timestamp, uid, update_timestamp', 'numerical', 'integerOnly'=>true),
			array('schoolid', 'length', 'max'=>10),
			array('img', 'length', 'max'=>100),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, childid, classid, schoolid, sign_timestamp, img, uid, update_timestamp', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
			'child'=>array(self::HAS_ONE, 'ChildProfileBasic', array('childid'=>'childid')),
			'actionStaff' => array(self::BELONGS_TO, 'User', 'uid'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'childid' => 'Childid',
			'classid' => 'Classid',
			'schoolid' => 'Schoolid',
			'sign_timestamp' => 'Sign Timestamp',
			'img' => 'Img',
			'uid' => 'Uid',
			'update_timestamp' => 'Update Timestamp',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('childid',$this->childid);
		$criteria->compare('classid',$this->classid);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('sign_timestamp',$this->sign_timestamp);
		$criteria->compare('img',$this->img,true);
		$criteria->compare('uid',$this->uid);
		$criteria->compare('update_timestamp',$this->update_timestamp);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return ChildDailySign the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	public static function getRulersByTime()
	{
		return array(
			'BJ_DS' => array(
				'23:58' => array('ivystaff_teacher'),
				'23:59' => array('ivystaff_doctor'),
			),
			'BJ_SLT' => array(
				'23:58' => array('ivystaff_teacher'),
				'23:59' => array('ivystaff_doctor'),
			),
		);
	}

	public function afterSave()
	{
		parent::afterSave();
		// 保存日志
		$status = 60;
		$start = $this->sign_timestamp;
		$end = $this->sign_timestamp;
		$res = ChildDailySignLog::signLog($this->schoolid, $this->classid, $this->childid, $status, $start, $end, $this->uid);
	}

	public function afterDelete()
	{
		parent::afterDelete();
		// 保存日志
		$status = 69;
		$start = $this->sign_timestamp;
		$end = $this->sign_timestamp;
		$res = ChildDailySignLog::signLog($this->schoolid, $this->classid, $this->childid, $status, $start, $end, $this->uid);
	}

    //未签到的孩子数
    public static function getAbsent($schoolid)
    {
        $today = strtotime('today');
        $classids = CHtml::listData(IvyClass::model()->findAllByAttributes(array('stat'=>IvyClass::STATS_OPEN, 'schoolid'=>$schoolid)), 'classid', 'classid');
        if($classids){
            Yii::import('common.models.attendance.ChildVacation');
            $tenClock = strtotime('10:00');
            $elevenClock = strtotime('11:00');
            $style = 'btn-primary';
            $t = time();
            if($t > $tenClock){
                $style = 'btn-warning';
            }
            if($t > $elevenClock){
                $style = 'btn-danger';
            }
            $classids = implode(',', $classids);
            $time = strtotime('today');
            $total = ChildProfileBasic::model()->countBySql("SELECT count(*) FROM `ivy_child_profile_basic` WHERE `classid` in ($classids) AND `status`<100 AND if(`enter_date`=0, `est_enter_date`, `enter_date`) <= $time");
            $criteria = new CDbCriteria();
            $criteria->compare('schoolid', $schoolid);
            $criteria->compare('sign_timestamp', $today);
            $countPresent = ChildDailySign::model()->count($criteria);
            $criteria = new CDbCriteria();
            $criteria->compare('school_id', $schoolid);
            $criteria->compare('type', array(ChildVacation::VACATION_SICK_LEAVE, ChildVacation::VACATION_AFFAIR_LEAVE));
            $criteria->compare('vacation_time_start', '<='.$today);
            $criteria->compare('vacation_time_end', '>='.$today);
            $countVacation = ChildVacation::model()->count($criteria);
            return array(
                'count' => ($total - $countPresent - $countVacation),
                'style' => $style,
            );
        }
        return array();
    }
}
