<?php
$crit = new CDbCriteria();
$crit->order = 't.startyear DESC';
$crit->compare('branchid',$this->branchId);
$returnModels = CalendarSchool::model()->with('cTemplate')->findAll($crit);

$calendarData = array();
$calendarBigMap = array();
$activeYears = array();
foreach($returnModels as $_campusCal){
    $timePoints = explode(',', $_campusCal->cTemplate->timepoints);
    $calendarBigMap[$_campusCal->cTemplate->startyear] = $_campusCal->cTemplate->yid;
    $calendarData[$_campusCal->cTemplate->yid] = array(
        'yid' => $_campusCal->cTemplate->yid,
        'title' => $_campusCal->cTemplate->title,
        'startyear' => $_campusCal->cTemplate->startyear,
        'firstSemester' => array(date('Y-m-d',$timePoints[0]),date('Y-m-d',$timePoints[1])),
        'secondSemester' => array(date('Y-m-d',$timePoints[2]),date('Y-m-d',$timePoints[3])),
        'isNew' => false,
        'isStatus' => $_campusCal->cTemplate->stat,
    );
}

$crit = new CDbCriteria();
$crit->compare('yid',$calendarBigMap);
$schoolDaysModel = CalendarSchoolDays::model()->findAll($crit);
$schoolDayList = array();
$schoolyear = array();
if($schoolDaysModel){
    foreach ($schoolDaysModel as $val){
        /*$schoolDayList[$val->yid]['month'][$val->month] = $val->schoolday;
        $schoolDayList[$val->yid]['semester'][$val->semester_flag] += $val->schoolday;
        $schoolDayList[$val->yid]['year'] += $val->schoolday;*/
        $schoolDayList[$val->yid]['month'][$val->month] = $val->schoolday;
        $firstSemester = date("Y-m", strtotime($calendarData[$val->yid]['firstSemester'][1]));
        $secondSemester = date("Y-m", strtotime($calendarData[$val->yid]['secondSemester'][0]));
        if($firstSemester == $secondSemester && $firstSemester == $val->month_label){
            $autumn = 0;
            $spring = 0;
            foreach (explode(',', $val->schoolday_array) as $item){
                $dataDay = $val->month_label . '-' . $item;
                if($dataDay <= $calendarData[$val->yid]['firstSemester'][1]){
                    $autumn++;
                }else{
                    $spring++;
                }
            }
            $schoolDayList[$val->yid]['semester'][10] += $autumn;
            $schoolDayList[$val->yid]['semester'][20] += $spring;
        }else{
            $schoolDayList[$val->yid]['semester'][$val->semester_flag] += $val->schoolday;
        }
        $schoolDayList[$val->yid]['year'] += $val->schoolday;
    }
}

$branches = $this->getAllBranch();
$clabels = Calendar::attributeLabels();
$cdLabels = CalendarDay::attributeLabels();

$crit = new CDbCriteria();
$crit->compare('category_sign', array('calendar_day_10','calendar_day_20','calendar_day_30'));
$crit->index = 'category_sign';
$terms = DiglossiaCategory::model()->with('terms')->findAll($crit);

foreach($terms as $key=>$_termCat){
    foreach($_termCat->terms as $_term){
        $termsData[$key][] = array(
            'entitle' => $_term->entitle,
            'cntitle' => $_term->cntitle
        );
    }
}

$newCalendarDay = new CalendarDay();
$newCalendarData = $newCalendarDay->getAttributes();
$newCalendarData['datestr'] = '';

$activeYears['min'] = empty($calendarBigMap) ? date('Y')-5 : min(array_keys($calendarBigMap));
$activeYears['max'] = empty($calendarBigMap) ? date('Y')+5 : max(array_keys($calendarBigMap)) + 2;
for($i=$activeYears['max'];$i>=$activeYears['min'];$i--){
    $optionYears[$i] = sprintf('%d-%d',$i,$i+1);
}

?>

<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site','Campus Operations'), array('//mcampus/default/index'))?></li>
        <li><?php echo CHtml::link(Yii::t('site','Basic'), array('//mcampus/default/index'))?></li>
        <li class="active"><?php echo Yii::t('site','School Calendar');?></li>
    </ol>

    <div class="row">
        <div class="col-md-1 col-sm-2">
            <ul class="nav nav-pills nav-stacked background-gray" id="calendar-years-list">
                <?php
                foreach($calendarBigMap as $_year => $_v){
                    echo CHtml::openTag('li');
                    echo CHtml::link(sprintf('%s - %s', $_year, $_year + 1), 'javascript:void(0);', array('yid'=>$_v,'startyear'=>$_year,'displayItems'=>''));
                    echo CHtml::closeTag('li');
                }
                ?>
            </ul>
        </div>

        <div class="col-md-11 col-sm-10" id="calendar-detail-box" style="display: none;">
            <div class="row">
                <div class="col-md-12">
                    <div class="panel panel-default">
                        <!-- Default panel contents -->
                        <div class="panel-heading calendar-basic-edit">
                            <span></span>
                        </div>
                        <div class="panel-body" id="calendar-edit">
                            <div class="form-horizontal">
                                <div id="calendar-edit-basic">
                                </div>
                                <div id='schoolnum'></div>
                                <div class="form-group">
                                    <div class="col-sm-2"></div>
                                    <div class="col-sm-10">
                                        <?php echo CHtml::hiddenField('yid'); ?>
                                        <button type="button" class="btn btn-primary" onclick="editCalendarDetail()">
                                            <?php echo Yii::t('site','Edit Calendar');?></button>
                                    </div>
                                </div>
                            </div>

                            <div id="calendar-detail">

                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>


<?php

$this->branchSelectParams['extraUrlArray'] = array('//mcampus/calendar/index');
$this->renderPartial('//layouts/common/branchSelectBottom');
?>

<?php //$this->renderPartial('templates/backboneTemplate',array('clabels'=>$clabels,'cdLabels'=>$cdLabels,'optionYears'=>$optionYears));?>
<?php $this->renderPartial('moperation.views.calendar.templates.backboneTemplate',array('clabels'=>$clabels,'cdLabels'=>$cdLabels,'optionYears'=>$optionYears, 'campus'=>1));?>

<script>
var calendarBigMap = <?php echo CJSON::encode($calendarBigMap);?>;
var calendarData = <?php echo CJSON::encode($calendarData);?>;
var branchData = <?php echo CJSON::encode($branches);?>;
var schoolDayList = <?php echo CJSON::encode($schoolDayList);?>;
var thisSchoolDayList;
var calendarTemplate = _.template($('#calendar-item-template').html());
var campusYesTemplate = _.template($('#calendar-campusYes-template').html());
var campusNoTemplate = _.template($('#calendar-campusNo-template').html());
var basicEditCalendarTemplate = _.template($('#calendar-basicEdit-template').html());
var totalCount;
 var schoolday = _.template($('#schooldays').html());
var currentCalendarId;
var currentStartYear;

var displayCalendarEditForm; //显示校历模版基本信息
var editCalendarDetail; //编辑日历详细信息
var dateSelect; //点击日历某天的编辑框
var beforeShowDay;
var startYearChange; //开始年下拉框onChange事件
var closeEditingDayWindow; //关闭编辑校历日界面

var calendarDayData = {};
var displayCalendarDays;
var calendarDayDisplayTemplate = _.template($('#day-item-template').html()); //日历天显示模版
var calendarDayReadonlyDisplayTemplate = _.template($('#day-item-readonly-template').html()); //日历天显示模版

var calendarDayEditTemplate = _.template($('#day-item-edit-template').html()); //日历天编辑模版
var setCalendarDate;
var editingDays=[];

var globalDefaultDays = <?php echo CJSON::encode($termsData);?>; //系统定义的假期、时间、工作日调休等

var changeEventType; //选择日历日类型时自动弹出预定义的假期、事件类型
var setDefaultEvent;

var showUnassignedCampus; //显示未分配校历模版的校园列表

$(function(){

        $.datepicker.setDefaults($.datepicker.regional['<?php echo (Yii::app()->language == 'en_us')? '': 'zh-CN'?>']);

    //显示所选学年的校历模版信息
    $('#calendar-years-list a[displayItems]').click(function(){
        $('#calendar-years-list li').removeClass('active');
        $(this).parents('li').addClass('active');

        var startYear = $(this).attr('startyear');
        var _calendarId = parseInt(calendarBigMap[startYear]);
        displayCalendarEditForm(_calendarId, '#calendar-edit-basic');
        $( "#calendar-detail").empty();
    });

    displayCalendarEditForm = function(calendarId, domWrapperId){
        thisSchoolDayList=schoolDayList[calendarId]
        calendarData[calendarId].year=thisSchoolDayList.year
        calendarData[calendarId].semester=thisSchoolDayList.semester
        calendarData[calendarId].isshow='1'
        var view = schoolday(calendarData[calendarId])
        $('#schoolnum').html(view);
        var _view = basicEditCalendarTemplate(calendarData[calendarId]);
        $(domWrapperId).html(_view);
        $('#yid').val(calendarId);
        $(domWrapperId + ' ' + '#Calendar_startyear').attr('disabled','disabled');
        $(domWrapperId + ' ' + 'input[name|="Calendar[title]"]').attr('readonly','readonly');
        $(domWrapperId + ' ' + 'input[name|="Calendar[firstSemester][0]"]').attr('readonly','readonly');
        $(domWrapperId + ' ' + 'input[name|="Calendar[firstSemester][1]"]').attr('readonly','readonly');
        $(domWrapperId + ' ' + 'input[name|="Calendar[secondSemester][0]"]').attr('readonly','readonly');
        $(domWrapperId + ' ' + 'input[name|="Calendar[secondSemester][1]"]').attr('readonly','readonly');
        $(domWrapperId + ' ' + '#Calendar_startyear option[value="'+calendarData[calendarId].startyear+'"]').attr('selected', true).attr('disabled','disabled');
        $(domWrapperId + ' ' + 'a[enableEdit]').hide();
        $('#calendar-detail-box').show();
        $('#calendar-detail-box .calendar-basic-edit span').html(branchData[currentBranchId].title + ' ' + calendarData[calendarId].title);
        currentCalendarId = calendarId;

    }

    editCalendarDetail = function(){
        $.ajax({
            async: false,
            type: 'post',
            url: '<?php echo $this->createUrl('//moperation/calendar/getCalendarDays');?>',
            dataType: 'json',
            data: {calendarId: currentCalendarId}
        }).done(function(data){
            if(data.state == 'success'){
                calendarDayData = data.data.cdata;
                calendarDayData[0] = <?php echo CJSON::encode($newCalendarData);?>;
                var minDate=new Date();
                var maxDate=new Date();
                var _date1 = explode('-',$('#calendar-edit-basic #Calendar_firstSemester_0').val());
                var _date2 = explode('-',$('#calendar-edit-basic #Calendar_secondSemester_1').val());

                minDate.setFullYear(parseInt(_date1[0]),parseInt(_date1[1]) - 1, parseInt(_date1[2]));
                maxDate.setFullYear(parseInt(_date2[0]),parseInt(_date2[1]) - 1, parseInt(_date2[2]));

                $( "#calendar-detail").empty();
                $('<div id="calendar-no-1"></div>').appendTo($( "#calendar-detail" ));
                if( $("#calendar-detail #calendar-no-1").html() != "" ){
                    $( "#calendar-detail #calendar-no-1").datepicker('refresh');
                }else{
                    $( "#calendar-detail #calendar-no-1").datepicker({
                        minDate: minDate,
                        maxDate: maxDate,
                        numberOfMonths: 3,
                        dateFormat: 'yy-mm-dd',
                        onSelect: dateSelect,
                        selectOtherMonths: false,
                        showOtherMonths: false,
                        changeMonth: true,
                        changeYear: true,
                        firstDay: 0,
                        beforeShowDay: beforeShowDay,
                        onChangeMonthYear:onChangeMonthYear,
                    });
                }

                $('<div id="edit-zone"></div>').appendTo($( "#calendar-detail #calendar-no-1 .ui-datepicker-inline" ));
                $('#edit-zone').html('<div class="mt20 alert alert-info">' +
                    '<?php echo Yii::t('message', 'Click calendar date to edit');?>' +
                    '</div>');

                displayCalendarDays();
                var year = $('.ui-datepicker-year option:selected').val();
                var month = parseInt($('.ui-datepicker-month option:selected').val())+1;
                onChangeMonthYear(year,month)
            }
        });
    }
    refreshCalendarDays = function(){
        $('.days-list[daytype]').empty();
        $.each(_.keys(calendarDayData), function(_k, day){
            if (calendarDayData[day].schoolid == currentBranchId){
                var _display = calendarDayDisplayTemplate(calendarDayData[day]);
            }else{
                var _display = calendarDayReadonlyDisplayTemplate(calendarDayData[day]);
            }

            $('#days-list .days-list[daytype|="'+calendarDayData[day].event+'"]').append(_display);
            $('#days-list .days-list[daytype|="'+calendarDayData[day].event+'"]').parents('.col-md-4').show();
        });
        _.each($('#days-list .days-list[daytype]'), function(_box){
            var _heading = $(_box).siblings('div.panel-heading');
            _heading.find('small').html($(_box).children('dl[theday]').length);
        });

        head.Util.ajaxDel($('#days-list'));
    }

    displayCalendarDays = function(){
        $( "#calendar-detail").append($('<div id="days-list" class="row mt20"></div>'));
        $( '#days-list').html($('#hidden-days-list-category').html());
        refreshCalendarDays();
    }

    dateSelect = function(date){
        selectedDate = new Date(date);
        var theDate = $.datepicker.formatDate('yymmdd',selectedDate);
        if($('#calendar-day-edit').length == 0){
            $('#calendar-no-1').after($('<div id="calendar-day-edit" class="mt20"></div> '));
            $('#calendar-day-edit').html($('#day-edit-form-wrapper').html());
        }

        if(_.indexOf(editingDays, date) < 0){

            if(editingDays.length < 2 && _.isUndefined( $("#selectManyDays").attr("checked")) ){
                if(_.isUndefined(calendarDayData[theDate])){
                    var _model = calendarDayData[0];
                }else{
                    var _model = calendarDayData[theDate];
                }
                var _edit_form = calendarDayEditTemplate(_model);
                $('#calendar-day-edit form').html(_edit_form);
                $('#CalendarDay_privacy').attr('checked', _model.privacy == 1 ? true : false);
                $('#CalendarDay_day_type').val(_model.day_type);
                $('#CalendarDay_event').val(_model.event);
                if(_model.event > 0){
                    changeEventType(document.getElementById('CalendarDay_event'));
                }
                head.Util.ajaxForm( $('#calendar-day-edit') );
            }

            if(_.isUndefined( $("#selectManyDays").attr("checked"))) {
                editingDays = [];
            }
            editingDays.push(date);
            renderEditingDays();
            $('#editingDays').val(editingDays.join(','));
            $('#CalendarDay_yid').val(currentCalendarId);
        }
    }

    //编辑校历日时，显示在左边的日期列表
    function renderEditingDays(){
        $('#editing-days-list').empty();
        $.each(editingDays, function(_k, _d){
            var _item = ' <li mydate="'+_d+'" class="list-group-item"><span class="glyphicon glyphicon-calendar"></span> ' +_d + ' <span class="pull-right"><a href="javascript:;" onclick="removeEditingDay(this)"><span class="glyphicon glyphicon-remove"></span></a> </span> </li> ';
            $('#editing-days-list').append(_item);
        });
        if(editingDays.length>1){
            $("#selectManyDays").attr("disabled","disabled");
        }else{
            $("#selectManyDays").removeAttr("disabled");
        }
    }

    beforeShowDay = function(date){
        var theDate = $.datepicker.formatDate('yymmdd',date);
        var _css = [];
        _css.push(theDate);
        if(!_.isUndefined(calendarDayData[theDate])){
            var _tip = [calendarDayData[theDate].title_cn, calendarDayData[theDate].title_en, calendarDayData[theDate].memo_cn, calendarDayData[theDate].memo_en];
            _css.push('dayflag');
            _css.push('dayflag-' + calendarDayData[theDate].day_type + calendarDayData[theDate].event);
            return [true, _css.join(' '), _tip.join("\n")];
        }else{
            return [true, _css.join(' '), ''];
        }
    }
    onChangeMonthYear=function(date,mon){
        if (mon >= 1 && mon <= 9) {
            mon = '0' + mon;
        }else{
            mon=mon
        }
        var datapush=[]
        var year2 = date;
        var month2 = parseInt(mon);
         for(var i=1;i<3;i++){
            if (parseInt(month2)+i >12) {
                year3 = parseInt(year2) + parseInt((parseInt(month2)+1 - 12 == 0 ? 1 : parseInt(month2)+1 - 12));
                month4 = parseInt(month2)+i - 12;
                if (month4 < 10) {
                    month4 = '0' + month4;
                }
                datapush.push( year3.toString()+ month4.toString())
            }else{
               month3=parseInt(month2)+i
                if (month3 < 10) {
                    month3 = '0' + month3;
                }
                 datapush.push( year2.toString() + month3.toString())
            }
        }
         if($('#daynum').length == 0){
            $('#calendar-no-1').append($('<div class="panel panel-default mt15" style="width:56em"> <div class="panel-body"  id="daynum"></div></div>'));
         }

           $('#daynum').html('<div class="col-md-4">'+date.toString()+mon.toString()+'：'+'<?php echo Yii::t('global', 'School Days num'); ?>'.replace(/num/g,thisSchoolDayList.month[date.toString()+mon.toString()])+'</div><div class="col-md-4">'+datapush[0]+'：'+'<?php echo Yii::t('global', 'School Days num'); ?>'.replace(/num/g,thisSchoolDayList.month[datapush[0]])+'</div><div class="col-md-4">'+datapush[1]+'：'+'<?php echo Yii::t('global', 'School Days num'); ?>'.replace(/num/g,thisSchoolDayList.month[datapush[1]])+'</div>');
    }
    setCalendarDate = function(obj){
        var theDay = $.datepicker.parseDate( 'yymmdd', $(obj).attr("theday"));
        $( "#calendar-detail #calendar-no-1").datepicker("setDate", theDay );
        var year = $('.ui-datepicker-year option:selected').val();
        var month = parseInt($('.ui-datepicker-month option:selected').val())+1;
        onChangeMonthYear(year,month)
    }

    removeEditingDay = function(obj){
        var _mydate = $(obj).parents('li[mydate]').attr('mydate');
        editingDays = _.without(editingDays, _mydate);
        if(editingDays.length == 0){
            closeEditingDayWindow();
        }else{
            renderEditingDays();
        }
    }

    closeEditingDayWindow = function(){
        editingDays = [];
        $('#calendar-day-edit').remove();
    }

    var daysEventOptions = {};
    changeEventType = function(obj){
        $('#event-default-list').hide();
        var _type = $(obj).find('option:selected').val();
        if(!_.isEmpty(_type)){
            var _key = 'calendar_day_' + _type;
            if(_.isUndefined(daysEventOptions[_type])){
                daysEventOptions[_type] = '';
                var _itemTemplate = _.template('<li><a onclick="setDefaultEvent(this)" data-index=<%= index %> data-event=<%= key %> href="javascript:;" title="<%= othertitle %>"><%= title %></a></li>');
                if(!_.isUndefined(globalDefaultDays) && globalDefaultDays && globalDefaultDays[_key]){
                    for(var _i=0; _i<globalDefaultDays[_key].length; _i++){
                        var _title = (LANG == 'en_us') ? globalDefaultDays[_key][_i].entitle: globalDefaultDays[_key][_i].cntitle;
                        var _othertitle = (LANG == 'en_us') ? globalDefaultDays[_key][_i].cntitle: globalDefaultDays[_key][_i].entitle;
                        daysEventOptions[_type] += _itemTemplate({index:_i,key:_type,title:_title, othertitle: _othertitle});
                    }
                }
            }

            $('#event-default-list').empty().html(daysEventOptions[_type]);
            $('#event-default-list').show();
        }
    }

    setDefaultEvent = function(obj){
        var bterm = globalDefaultDays['calendar_day_' + $(obj).attr('data-event')][$(obj).attr('data-index')];
        $('#CalendarDay_title_cn').val(bterm.cntitle);
        $('#CalendarDay_title_en').val(bterm.entitle);
    }

});

// 编辑天（假期、事件等）回调函数
function cbc(data)
{
    setTimeout(function(){closeEditingDayWindow();}, 1000);
    $.each(data, function(key, value){
        calendarDayData[key] = value;
    });
    refreshCalendarDays();
    $( "#calendar-detail #calendar-no-1").datepicker('refresh');
}

// 删除天回调函数
function deldayCallback(data)
{
    delete calendarDayData[data.day];
    refreshCalendarDays();
    $( "#calendar-detail #calendar-no-1").datepicker('refresh');
}
</script>

<div id="hidden-days-list-category" style="display: none;">
    <div class="col-md-4" style="display: none;">
        <div class="panel panel-default">
            <div class="panel-heading"><?php echo Yii::t('site','Holiday List');?> <small class="text-primary"></small></div>
            <div class="panel-body days-list" daytype="10">
            </div>
        </div>
    </div>
    <div class="col-md-4" style="display: none;">
        <div class="panel panel-default">
            <div class="panel-heading"><?php echo Yii::t('site','Event List');?> <small class="text-primary"></small></div>
            <div class="panel-body days-list" daytype="20">
            </div>
        </div>
    </div>
    <div class="col-md-4" style="display: none;">
        <div class="panel panel-default">
            <div class="panel-heading"><?php echo Yii::t('site','Schooldays Make-up');?>  <small class="text-primary"></small></div>
            <div class="panel-body days-list" daytype="30">
            </div>
        </div>
    </div>
</div>

<div id="day-edit-form-wrapper" style="display: none;">
    <div class="panel panel-primary">
        <div class="panel-heading"><?php echo Yii::t('message','Edit Calendar Day');?> <span></span></div>
        <div class="panel-body">
            <form class="day-edit-form J_ajaxForm" method="post" action="<?php echo $this->createUrl('saveCalendarDays');?>"></form>
        </div>
    </div>
</div>
