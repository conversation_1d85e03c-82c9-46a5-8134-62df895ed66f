<?php
/**
 * 修改账单工作流
 */
class InvoiceChangeIvy implements WorkflowTemplate{
    private $template;
    public $controller;
    public $workflow;
    private $step = 'one';
    public function __construct() {
        Yii::import('common.models.invoice.Invoice');
        Yii::import('common.models.invoice.InvoiceChangeHistory');
    }
    public function initi(){
        $invoiceId = Yii::app()->request->getParam('invoiceId',0);
        $childId = Yii::app()->request->getParam('childid',0);
        if ($childId && $invoiceId){
            $this->setTemplate('initi');
            return $this->signChild($invoiceId, $childId);
        }else{
            $data['jsFunction'] = 'WorkflowModalShow';
            $data['modalNameId'] = 'workflow-change-invoice-modal';
            $data['workflow'] = $this->workflow;
            $this->setTemplate('newiniti');
            return $data;
        }
    }
    
    /**
     * 公共保存跳转函数
     */
    public function publicSave(){
        $this->step = Yii::app()->request->getParam('step');
        $buttonCategory = Yii::app()->request->getParam('buttonCategory');
        switch ($this->step){
            case 'one':
                return $this->firstSave();
                break;
            case 'two':
                if ($buttonCategory === 'next'){
                    return $this->twoSave();
                }else{
                    $this->step = 'one';
                    return $this->show();
                }
                break;
            case 'three':
                if ($buttonCategory === 'next'){
                    return $this->threeSave();
                }else{
                    $this->step = 'two';
                    return $this->show();
                }
                break;
        }
    }
    
    /**
     * 第一节点（第一步保存）函数
     */
    public function firstSave(){
        if (isset($_POST['ChangeInvoice']) && Yii::app()->request->isPostRequest){
            $childid = isset($_POST['ChangeInvoice']['childid']) ? $_POST['ChangeInvoice']['childid'] : 0;
            $memo = isset($_POST['ChangeInvoice']['memo']) ? $_POST['ChangeInvoice']['memo'] : null;
            if ($childid === 0){
                $this->controller->addMessage('state', 'fail');
                $this->controller->addMessage('message', '请先选择孩子！');
                $this->controller->showMessage();
            }
            if (empty($memo)){
                $this->controller->addMessage('state', 'fail');
                $this->controller->addMessage('message', '意见不能为空！');
                $this->controller->showMessage();
            }
            
            //判断孩子的状态
            if ($childid){
                $basic = ChildProfileBasic::model()->findByPk($childid);
                $statusList = ChildProfileBasic::model()->getStatusList();
                if ($basic->status >= ChildProfileBasic::STATS_GRADUATED){
                    $status = $statusList[$basic->status];
                    $this->controller->addMessage('state', 'fail');
                    $this->controller->addMessage('message', "孩子已{$status}中，不能操作！");
                    $this->controller->showMessage();
                }
            }
            // 检查孩子是否在当前学校就读过。
            if ($basic->schoolid != $this->workflow->schoolId) {
                if (!$this->workflow->hadStudyInSchool($childid)) {
                    $this->controller->addMessage('state', 'fail');
                    $this->controller->addMessage('message', "孩子未在当前校园就读过！");
                    $this->controller->showMessage();
                }
            }
            $transaction = Yii::app()->db->beginTransaction();
            try{
                //保存工作流业务流程
                $operationData = array('id'=>$this->workflow->getOperateId(),'operation_type'=>  WorkflowConfig::WORKFLOW_TYPE_CHANGE,'operation_object_id'=>$childid,'defination_id'=>$this->workflow->getWorkflowId(),
                    'branchid'=>$this->workflow->schoolId,'state'=>WorkflowOperation::WORKFLOW_STATS_STEP,'current_node_index'=>$this->workflow->getCurrentNodeId(),
                );
                $operationRet = $this->workflow->saveFirstNodeForWorkflow($operationData,null,$memo,$transaction);
                if ($operationRet === false){
                    $this->controller->addMessage('state', 'fail');
                    $this->controller->addMessage('message', '保存失败');
                    $this->controller->showMessage();
                }
                $transaction->commit();
                $this->setTemplate('initi_two');
                $data['jsFunction'] = 'WorkflowModalShow';
                $data['modalNameId'] = 'workflow-change-invoice-modal';
                $data['workflow'] = $this->workflow;
                //查询账单
                $criter = new CDbCriteria;
                // $criter->compare('payment_type', array('tuition','bus','lunch','afterschool'));
                $criter->compare('schoolid', $this->workflow->schoolId);
                $criter->compare('childid',$childid);
                $criter->compare('status', Invoice::STATS_UNPAID);
                $criter->compare('`inout`', 'in');
                $invoice = Invoice::model()->findAll($criter);
                $data['invoices'] = $invoice;
                return $data;
            } catch (Exception $ex) {
                $transaction->rollBack();
                $this->controller->addMessage('state', 'fail');
                $this->controller->addMessage('message', $ex->getMessage());
                $this->controller->showMessage();
            }
            $this->controller->showMessage();
        }
    }
    
    /**
     * 第一节点（第二步保存）函数
     */
    public function twoSave(){
        $invoiceIds = Yii::app()->request->getPost('invoiceId');
        if (is_array($invoiceIds) && count($invoiceIds)){
            $invoiceObj = Invoice::model()->findAllByPk($invoiceIds);
            $this->setTemplate('initi_three');
            $data['jsFunction'] = 'WorkflowModalShow';
            $data['modalNameId'] = 'workflow-change-invoice-modal';
            $data['workflow'] = $this->workflow;
            $data['invoices'] = $invoiceObj;
            foreach ($invoiceObj as $invoice) {
                if ($invoice->discount_id != 0) {
                    $this->controller->addMessage('state', 'fail');
                    $this->controller->addMessage('message',Yii::t('invoice', '使用折扣的账单不能修改'));
                    $this->controller->showMessage();
                }
            }
            return $data;
        }else{
            $this->controller->addMessage('state', 'fail');
            $this->controller->addMessage('message',Yii::t('invoice', '请勾选 [ 帐单 ]'));
            $this->controller->showMessage();
        }
    }
    
    /**
     * 第一节点最终保存函数
     */
    public function threeSave(){
        if (isset($_POST['InvoiceChange'])){
            $objs = array();
            //验证
            foreach ($_POST['InvoiceChange'] as $invoicKey => $invoiceVal){
                $invoiceChange = new InvoiceChangeThreeForm();
                $invoiceChange->invoice_id = $invoicKey;
                $invoiceChange->title = $invoiceVal['title'];
                $invoiceChange->startdate = isset($invoiceVal['startdate']) ? strtotime($invoiceVal['startdate']) : '';
                $invoiceChange->enddate = isset($invoiceVal['enddate']) ? strtotime($invoiceVal['enddate']) : '';
                $invoiceChange->amount = $invoiceVal['amount'];
                if ($invoiceChange->validate()){
                    $objs[] = $invoiceChange;
                }else{
                    $this->controller->addMessage('state', 'fail');
                    $errs = current($invoiceChange->getErrors());
                    if ($errs)
                        $this->controller->addMessage('message', $errs[0]);
                    $this->controller->showMessage();
                }
            }
            $processModel = $this->workflow->getWorkflowProcess(true);
            //保存
            $transaction = Yii::app()->db->beginTransaction();
            try{
                //查询账单
                $criter = new CDbCriteria();
                $criter->compare('invoice_id', array_keys($_POST['InvoiceChange']));
                $criter->compare('childid', $this->workflow->getOperateObjId());
                $criter->compare('status', Invoice::STATS_UNPAID);
                $invoices = Invoice::model()->findAll($criter);
                if (empty($invoices)){
                    $this->controller->addMessage('state', 'fail');
                    $this->controller->addMessage('message',Yii::t('invoice', '请勾选 [ 帐单 ]'));
                    $this->controller->showMessage();
                }
                foreach ($invoices as $invoiceValue){
                    $newInovice = new Invoice();
                    $newInovice->setAttributes($invoiceValue->getAttributes());
                    $newInovice->attributes = $_POST['InvoiceChange'][$invoiceValue->invoice_id];
                    $newInovice->receivable_status = 0;
                    $newInovice->apportion_status = 0;
                    $newInovice->original_amount = $newInovice->amount;
                    $newInovice->status = Invoice::STATS_AWAITING_CHANGE;
                    $newInovice->userid = Yii::app()->user->getId();
                    $newInovice->timestamp = time();
                    $newInovice->startdate = strtotime($newInovice->startdate);
                    $newInovice->enddate = strtotime($newInovice->enddate);
                    if (!$newInovice->save()){
                        $transaction->rollback();
                        $errs = current($newInovice->getErrors());
                        $this->controller->addMessage('state', 'fail');
                        $this->controller->addMessage('message', "创建新账单失败，原因：{$errs[0]}");
                        $this->controller->showMessage();
                    }
                    $invoiceValue->status = Invoice::STATS_AWAITING_CHANGE;
                    if (!$invoiceValue->save()){
                        $transaction->rollback();
                        $this->controller->addMessage('state', 'fail');
                        $this->controller->addMessage('message', '保存失败，原因：原账单状态更新失败。');
                        $this->controller->showMessage();
                    }
                    //关联表
                    $history = new InvoiceChangeHistory();
                    $history->invoice_id = $invoiceValue->invoice_id;
                    $history->new_invoiceid = $newInovice->invoice_id;
                    $history->invoice_type = InvoiceChangeHistory::INVOICE_TYPE_APPLICATION_CHANGE;
                    $history->operator_uid = Yii::app()->user->getId();
                    $history->operate_timestamp = time();
                    if (!$history->save()){
                        $transaction->rollback();
                        $this->controller->addMessage('state', 'fail');
                        $this->controller->addMessage('message', '保存失败，原因：账单关系中间表保存失败。');
                        $this->controller->showMessage();
                    }else{
                        $historyIds[] = $history->hid;
                    }
                }
                 //保存工作流业务流程
                $operationData = array('id'=>$this->workflow->getOperateId(),'operation_type'=>WorkflowConfig::WORKFLOW_TYPE_CHANGE,'operation_object_id'=>$this->workflow->getOperateObjId(),'defination_id'=>$this->workflow->getWorkflowId(),
                            'branchid'=>$this->workflow->schoolId,'state'=>WorkflowOperation::WORKFLOW_STATS_UNOP,'current_node_index'=>$this->workflow->getCurrentNodeId(),
                        );
                $operationRet = $this->workflow->saveFirstNodeForWorkflow($operationData,$historyIds,$processModel->process_desc,$transaction);
                if ($operationRet === false){
                    $this->controller->addMessage('state', 'fail');
                    $this->controller->addMessage('message', '保存失败,原因：工作流底层更新错误！');
                    $this->controller->showMessage();
                }
            }catch(Exception $e){
                $transaction->rollBack();
                $this->controller->addMessage('state', 'fail');
                $this->controller->addMessage('message', $e->getMessage());
                $this->controller->showMessage();
            }
            $transaction->commit();
            $this->jumpMail();
            $this->controller->addMessage('state', 'success');
            $this->controller->addMessage('message', '成功');
            $this->controller->addMessage('data',array('modalNameId'=>'workflow-change-invoice-modal','operatorId'=>$this->workflow->getOperateId(),'html'=>$this->workflow->getNodeListUseHtml()));
            $this->controller->addMessage('callback', 'saveWorkflowOperator');
            $this->controller->showMessage();
        }
    }

    public function signChild($invoiceId,$childId){
        $invocie = Invoice::model()->findByPk($invoiceId);
        if ($invocie->status != Invoice::STATS_UNPAID){
            throw new Exception('未付款账单才能更改！');
        }
        if ((int)$invocie->childid !== (int)$childId){
            throw new Exception('错误孩子！');
        }
        if (isset($_POST['Invoice'])){
            $newInovice = new Invoice();
            $newInovice->setAttributes($invocie->getAttributes());
            $newInovice->attributes = $_POST['Invoice'];
            $newInovice->status = Invoice::STATS_AWAITING_CHANGE;
            $newInovice->receivable_status = 0;
            $newInovice->apportion_status = 0;
            $newInovice->userid = Yii::app()->user->getId();
            $newInovice->timestamp = time();
            $newInovice->startdate = strtotime($newInovice->startdate);
            $newInovice->enddate = strtotime($newInovice->enddate);
            $transaction = Yii::app()->db->beginTransaction();
            if (!$newInovice->save()){
                $transaction->rollback();
                $this->controller->addMessage('state', 'fail');
                $this->controller->addMessage('message', '保存失败，原因：创建新账单失败。');
                $this->controller->showMessage();
            }
            $invocie->status = Invoice::STATS_AWAITING_CHANGE;
            if (!$invocie->save()){
                $transaction->rollback();
                $this->controller->addMessage('state', 'fail');
                $this->controller->addMessage('message', '保存失败，原因：原账单状态更新失败。');
                $this->controller->showMessage();
            }
            //关联表
            $history = new InvoiceChangeHistory();
            $history->invoice_id = $invocie->invoice_id;
            $history->new_invoiceid = $newInovice->invoice_id;
            $history->invoice_type = InvoiceChangeHistory::INVOICE_TYPE_APPLICATION_CHANGE;
            $history->operator_uid = Yii::app()->user->getId();
            $history->operate_timestamp = time();
            if (!$history->save()){
                $transaction->rollback();
                $this->controller->addMessage('state', 'fail');
                $this->controller->addMessage('message', '保存失败，原因：账单关系中间表保存失败。');
                $this->controller->showMessage();
            }
            //保存工作流业务流程
            $operationData = array('operation_type'=>WorkflowConfig::WORKFLOW_TYPE_CHANGE,'operation_object_id'=>$invocie->childid,'defination_id'=>$this->workflow->getWorkflowId(),'branchid'=>$invocie->schoolid,'state'=>WorkflowOperation::WORKFLOW_STATS_UNOP,'current_node_index'=>$this->workflow->getNextNodeId(),'exte1'=>'');
            $operationRet = $this->workflow->saveFirstNodeForWorkflow($operationData,array($history->hid),$newInovice->memo,$transaction);
            if ($operationRet === false){
                $this->controller->addMessage('state', 'fail');
                $this->controller->addMessage('message', '保存失败, 原因：工作流底层更新失败。');
                $this->controller->showMessage();
            }
            $transaction->commit();
            $this->jumpMail();
            $this->controller->addMessage('state', 'success');
            $this->controller->addMessage('message', '成功');
            $this->controller->addMessage('refresh', true);
        }
        return $invocie;
    }
    
    public function show(){
        //Definition modal's id and create js function
        $data['jsFunction'] = 'WorkflowModalShow';
        $data['modalNameId'] = 'workflow-change-invoice-modal';
        $data['workflow'] = $this->workflow;
        if (($this->workflow->isFristNode() === true) && $this->workflow->getOperateStatus() == WorkflowOperation::WORKFLOW_STATS_STEP){
            if ($this->step === 'one'){
                $this->setTemplate('newiniti');
                //业务
                $process = $this->workflow->getWorkflowProcess(TRUE);
                //查询孩子
                $ftsChild = FtsChild::model()->findByPk($this->workflow->getOperateObjId());
                $data['processDesc'] = $process->process_desc;
                preg_match('/{(.*)}/', $ftsChild->tdata, $matches);
                $data['ftsChild'] = array($ftsChild->id =>$matches[1]);
                $data['select'] = $ftsChild->id;
            }elseif($this->step === 'two'){
                $this->setTemplate('initi_two');
                //查询账单
                Yii::import('common.models.invoice.Invoice');
                $criter = new CDbCriteria;
                // $criter->compare('payment_type', array('tuition','bus','lunch'));
                $criter->compare('schoolid', $this->workflow->schoolId);
                $criter->compare('childid',$this->workflow->getOperateObjId());
                $criter->compare('status', Invoice::STATS_UNPAID);
                $criter->compare('`inout`', 'in');
                $invoice = Invoice::model()->findAll($criter);
                $data['invoices'] = $invoice;
            }
            return $data;
        }else{
            $this->setTemplate('show');
            //业务
            $oDetails = WorkflowOperationDetail::model()->findAll('operation_id=:operation_id',array(':operation_id'=>$this->workflow->getOperateId()));
            $changeInvoiceId = array();
            foreach ($oDetails as $oDetail) {
                $changeInvoiceId[] = $oDetail->operation_object_id;
            }
            $changeInvoices = InvoiceChangeHistory::model()->with('invoice','newInvoice')->findAllByPk($changeInvoiceId);
            if (empty($changeInvoices)){
                throw new Exception('工作流程业务参数错误！',500);
            }
            //处理POST请求
            if (Yii::app()->request->isPostRequest){
                $buttonCategory = Yii::app()->request->getPost('buttonCategory',0);
                $memo = Yii::app()->request->getPost('memo','');
                $workflowStatus = WorkflowOperation::WORKFLOW_STATS_UNOP;
                if (empty($memo)){
                    $this->controller->addMessage('state', 'fail');
                    $this->controller->addMessage('message', '保存失败，原因：意见必填！');
                    $this->controller->showMessage();
                }
                $transaction = Yii::app()->db->beginTransaction();
                try{
                    foreach ($changeInvoices as $changeInvoice) {
                        $changeInvoice->auditor_uid = Yii::app()->user->id;
                        $changeInvoice->auditor_timestamp = time();
                        if (!$changeInvoice->save()){
                            $transaction->rollback();
                            $this->controller->addMessage('state', 'fail');
                            $this->controller->addMessage('message', '保存失败，原因：账单关系中间表更新失败。');
                            $this->controller->showMessage();
                        }
                        //被拒绝
                        if ($buttonCategory == WorkflowOperation::WORKFLOW_STATS_OPDENY){
                            $changeInvoice->invoice->status = Invoice::STATS_UNPAID;
                            if (!$changeInvoice->invoice->save()){
                                $transaction->rollback();
                                $this->controller->addMessage('state', 'fail');
                                $this->controller->addMessage('message', '保存失败，原因：原账单状态更新失败。');
                                $this->controller->showMessage();
                            }
                            $changeInvoice->newInvoice->status = Invoice::STATS_CANCELLED;
                            $changeInvoice->newInvoice->send_timestamp = time();
                            if (!$changeInvoice->newInvoice->save()){
                                $transaction->rollback();
                                $this->controller->addMessage('state', 'fail');
                                $this->controller->addMessage('message', '保存失败，原因：新账单状态更新失败。');
                                $this->controller->showMessage();
                            }
                            $workflowStatus = WorkflowOperation::WORKFLOW_STATS_OPDENY;
                        }else{
                            //判断是不是最后节点
                            if ($this->workflow->isEndNode()){
                                if ($changeInvoice->invoice->payment_type == 'tuition'){
                                    $tuitionAmount = 0;
                                    //查询账单是否使用预交学费
                                    Yii::import('common.models.invoice.TemporaryDeposit');
                                    $depositTemp = TemporaryDeposit::model()->findByPk($changeInvoice->invoice->invoice_id);
                                    if (!empty($depositTemp)){
                                        if ((int)$changeInvoice->newInvoice->amount === 0){
                                            //删除使用预交学费
                                            if (!$depositTemp->delete()){
                                                $transaction->rollback();
                                                $this->controller->addMessage('state', 'fail');
                                                $this->controller->addMessage('message', '保存失败，原因：预缴学费临时表删除失败！');
                                                $this->controller->showMessage();
                                            }
                                        } elseif(($changeInvoice->newInvoice->amount - $depositTemp->amount)>=0){
                                            $tuitionAmount = $changeInvoice->newInvoice->amount - $depositTemp->amount;
                                            $depositTemp->invoice_id = $changeInvoice->newInvoice->invoice_id;
                                            if (!$depositTemp->save()){
                                                $transaction->rollback();
                                                $this->controller->addMessage('state', 'fail');
                                                $this->controller->addMessage('message', '保存失败，原因：预缴学费临时表更新invoiceID失败！');
                                                $this->controller->showMessage();
                                            }
                                        } elseif(($changeInvoice->newInvoice->amount - $depositTemp->amount)<0){
                                            $tuitionAmount = 0;
                                            $depositTemp->invoice_id = $changeInvoice->newInvoice->invoice_id;
                                            $depositTemp->amount = $changeInvoice->newInvoice->amount;
                                            if (!$depositTemp->save()){
                                                $transaction->rollback();
                                                $this->controller->addMessage('state', 'fail');
                                                $this->controller->addMessage('message', '保存失败，原因：预缴学费临时表更新invoiceID失败！');
                                                $this->controller->showMessage();
                                            }
                                        }
                                        $changeInvoice->newInvoice->amount = $tuitionAmount;
                                    }
                                }
                                $changeInvoice->invoice->status = Invoice::STATS_CHANGE_ARCHIVED;
                                $changeInvoice->invoice->send_timestamp = time();
                                if (!$changeInvoice->invoice->save()){
                                    $transaction->rollback();
                                    $this->controller->addMessage('state', 'fail');
                                    $this->controller->addMessage('message', '保存失败，原因：原账单状态更新失败。');
                                    $this->controller->showMessage();
                                }
                                $changeInvoice->newInvoice->status = Invoice::STATS_UNPAID;
                                if (!$changeInvoice->newInvoice->save()){
                                    $transaction->rollback();
                                    $this->controller->addMessage('state', 'fail');
                                    $this->controller->addMessage('message', '保存失败，原因：新账单状态更新失败。');
                                    $this->controller->showMessage();
                                }
                                // 处理餐费的收退费金额
                                if ($changeInvoice->invoice->payment_type == 'lunch') {
                                    Yii::import('common.models.invoice.InvoiceLunchUnit');
                                    Yii::import('common.models.calendar.CalendarSchoolDays');
                                    $lunchUnitModel = InvoiceLunchUnit::model()->findByPk($changeInvoice->invoice->invoice_id);
                                    if ($lunchUnitModel) {
                                        $calendarId = $changeInvoice->newInvoice->calendar_id;
                                        $startDate = $changeInvoice->newInvoice->startdate;
                                        $endDate = $changeInvoice->newInvoice->enddate;
                                        $schooldayList = CalendarSchoolDays::model()->countCalendarSchoolday($calendarId, $startDate, $endDate);
                                        $day = $schooldayList['totalActualSchoolday'];
                                        if ($day <= 0) {
                                            $transaction->rollback();
                                            $this->controller->addMessage('state', 'fail');
                                            $this->controller->addMessage('message', '计算餐费价格出差');
                                            $this->controller->showMessage();
                                        }
                                        $newPrice = round($changeInvoice->newInvoice->amount / $day, 2);
                                        $newRefund = round($newPrice * ($lunchUnitModel->refund / $lunchUnitModel->price), 2);
                                        $newLunchUnitModel = new InvoiceLunchUnit();
                                        $newLunchUnitModel->invoice_id = $changeInvoice->newInvoice->invoice_id;
                                        $newLunchUnitModel->price = $newPrice;
                                        $newLunchUnitModel->refund = $newRefund;
                                        if(!$newLunchUnitModel->save()) {
                                            $error = current($newLunchUnitModel->getErrors());
                                            $transaction->rollback();
                                            $this->controller->addMessage('state', 'fail');
                                            $this->controller->addMessage('message', $error[0]);
                                            $this->controller->showMessage();
                                        }
                                    }
                                }
                                $workflowStatus = WorkflowOperation::WORKFLOW_STATS_OPED;
                            }
                        }
                    }
                    if ($this->workflow->saveWorkflow($memo,$workflowStatus,$transaction) === false){
                        $this->controller->addMessage('state', 'fail');
                        $this->controller->addMessage('message', '保存失败');
                        $this->controller->showMessage();
                    }
                    $transaction->commit();
                    $this->jumpMail();
                    $this->controller->addMessage('state', 'success');
                    $this->controller->addMessage('message', '保存成功');
                    $this->controller->addMessage('data',array('modalNameId'=>'workflow-change-invoice-modal','operatorId'=>$this->workflow->getOperateId(),'html'=>$this->workflow->getNodeListUseHtml()));
                    $this->controller->addMessage('callback', 'saveWorkflowOperator');
                    $this->controller->showMessage();
                }catch(Exception $e){
                    $transaction->rollBack();
                    $this->controller->addMessage('state', 'fail');
                    $this->controller->addMessage('message', $e->getMessage());
                    $this->controller->showMessage();
                }
            }
            $data['changeInvoices'] = $changeInvoices;
            $data['workflow'] = $this->workflow;
            return $data;
        }
    }
    
    public function jumpMail() {
        $branchList = Branch::model()->getBranchList();
        $branchTitle = $branchList[$this->workflow->getOperateValue('branchid')];
        $childName = WorkflowConfig::getChildName($this->workflow->getOperateValue('operation_object_id'));
        $workflowTitle = CommonUtils::autoLang($this->workflow->definationObj->defination_name_cn,$this->workflow->definationObj->defination_name_en);
        $nodesTitle = CommonUtils::autoLang($this->workflow->nodesList[$this->workflow->getNextNodeId()]->node_name_cn,$this->workflow->nodesList[$this->workflow->getNextNodeId()]->node_name_en);
        if ($this->workflow->getOperateStatus() == WorkflowOperation::WORKFLOW_STATS_OPDENY){
            $subject = sprintf('%s 校园 %s 提出 %s申请被拒绝',$branchTitle,$childName,$workflowTitle);
        }else{
            if ($this->workflow->isEndNode()){
                $subject = sprintf('%s 校园 %s 提出 %s申请已完成',$branchTitle,$childName,$workflowTitle);
            }else{
                $subject = sprintf('%s 校园 %s 提出 %s申请需要您处理',$branchTitle,$childName,$workflowTitle);
            }
        }
        // 微信推送
        $this->workflow->wechatPush();
        $link = $this->controller->createUrl('/workflow/index', array('branchId'=>$this->workflow->schoolId));
        $body = $this->controller->renderPartial('/mail/templater',array('branchTitle'=>$branchTitle,'childName'=>$childName,'workflowTitle'=>$workflowTitle,'nodes'=>$nodesTitle,'link'=>$link,'workflow'=>$this->workflow),true);
        return $this->workflow->sendEmail($subject,$body);
    }
    
    public function delete() {
        $request = Yii::app()->request;
        if ($this->workflow->getOperateStatus() == WorkflowOperation::WORKFLOW_STATS_STEP){
            $transaction = Yii::app()->db->beginTransaction();
            try{
                if (!$this->workflow->deleteOperationNode($transaction)){
                    $this->controller->addMessage('state', 'fail');
                    $this->controller->addMessage('message', "工作流底层删除失败!");
                    $this->controller->showMessage();
                }
                $transaction->commit();
                $this->controller->addMessage('state', 'success');
                $this->controller->addMessage('message', '工作流删除成功');
                $this->controller->addMessage('data',$this->workflow->getOperateId());
                $this->controller->addMessage('callback', 'deleteWorkflowOperator');
                $this->controller->showMessage();
            } catch (Exception $ex) {
                $transaction->rollBack();
                $this->controller->addMessage('state', 'fail');
                $this->controller->addMessage('message', $ex->getMessage());
                $this->controller->showMessage();
            }
        }
    }

    //实现接口方法
    public function setTemplate($name){
        $this->template = $name;
    }
    //实现接口方法
    public function getTemplate(){
        return $this->template;
    }
}