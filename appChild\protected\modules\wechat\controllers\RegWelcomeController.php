<?php

class RegWelcomeController extends Controller
{
	public $layout = '//layouts/bootstrap4';

	public function init()
	{
		parent::init();
        if(isset($_GET['lang'])){
            $lang = strtolower($_GET['lang']);
            if(!in_array($lang, array("en_us","zh_cn"))){
                $lang = "en_us";
            }
            Mims::setLangCookie($lang);
        }
        else{
            $langcookie = Yii::app()->request->cookies['child_mims_lang'];
            $lang = isset($langcookie->value) ? $langcookie->value : '';
        }
        Yii::app()->language = $lang;
		Yii::app()->theme = 'app';
	}

    public function createUrl($route, $params = array(), $ampersand = '&', $parentOnly = false)
    {
        if (!$parentOnly) {
            if (empty($params['state'])) {
                $params['state'] = $_GET['state'];
            }
        }
        return parent::createUrl($route, $params, $ampersand);
    }

	public function actionIndex($schoolid='')
	{
	    Yii::import('common.models.regextrainfo.*');

	    $schoolid = Yii::app()->request->getParam('schoolid');
	    $content = '';
	    $lang = Yii::app()->language;
	    $redirect_uri = urlencode('http://www.ivyonline.cn/wechat/regExtraInfo/index/lang/'.$lang.'?to_uri=https://www.ivyonline.cn/wechat/regExtraInfo/index');
	    $url = 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=wx903fba9d4709cf10&redirect_uri='.$redirect_uri.'&response_type=code&scope=snsapi_base&state=ivy#wechat_redirect';
	    if ($schoolid) {
	    	$ctime = time();
	    	$criteria = new CDbCriteria();
	    	$criteria->compare('startdate', '<=' . $ctime);
	    	$criteria->compare('enddate', '>=' . $ctime);
	    	$criteria->compare('schoolid', $schoolid);
	    	$reg = Reg::model()->find($criteria);
	    	if ($reg) {
			    $config = RegConfig::getContent($reg->id, 0);
			    if ($config) {
			        $content_cn = base64_decode($config['cn']['welcome']);
			        $content_en = base64_decode($config['en']['welcome']);
			        $content = CommonUtils::autoLang($content_cn, $content_en);
			    }
	    	}

	    	$branchObj = Branch::model()->findByPk($schoolid);
	    	if ($branchObj->type == 50 || $schoolid == "BJ_QFF"){
                $url = 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=wxb1a42b81111e29f3&redirect_uri='.$redirect_uri.'&response_type=code&scope=snsapi_base&state=ds#wechat_redirect';
            }
	    }

	    //todo 查询欢迎页面文字
		$this->siteTitle =Yii::t("reg", 'New Student Online Registration');
		$this->render('index', array('content' => $content, 'url'=>$url));
	}

	public function actionReEnrollment()
    {
		$sid = Yii::app()->request->getParam('sid');
		$state = Yii::app()->request->getParam('state');
		if (!$sid || !$state) {
			return false;
		}
		Yii::import('common.models.surveyReturn.*');
		$model = SurveyReturn::model()->findByPk($sid);
		if (!$model) {
			return false;
		}
		$tModel = SurveyReturnTemplate::model()->findByPk($model->tid);
		if (!$tModel) {
			return false;
		}
		$lang = Yii::app()->language;
		$url =  $this->createUrl('/wechat/school/returnIndex', array('tid' => $model->tid, 'lang' =>$lang));
		if ($state == 'ds') {
			$this->siteTitle = Yii::t("global", 'Daystar Online');
			$loginUrl = 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=wxb1a42b81111e29f3&redirect_uri='.$url.'&response_type=code&scope=snsapi_base&state=ds#wechat_redirect';
		} else {
			$this->siteTitle = Yii::t("global", 'IvyOnline');
			$loginUrl = 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=wx903fba9d4709cf10&redirect_uri='.$url.'&response_type=code&scope=snsapi_base&state=ivy#wechat_redirect';
		}
		if ($lang == 'en_us') {
			$startYear = date('Y', $model->start_timestamp);
			$startMon = date('M', $model->start_timestamp);
			$startDay = date('d', $model->start_timestamp);
			$startDate =  sprintf("%s %02d, %04d", $startMon, $startDay, $startYear);

			$endYear = date('Y', $model->end_timestamp);
			$endMon = date('M', $model->end_timestamp);
			$endDay = date('d', $model->end_timestamp);
			$endDate =  sprintf("%s %02d, %04d", $endMon, $endDay, $endYear);
		} else {
			$startYear = date('Y', $model->start_timestamp);
			$startMon = date('m', $model->start_timestamp);
			$startDay = date('d', $model->start_timestamp);
			$startDate =  sprintf("%04d年%02d月%02d日", $startYear, $startMon, $startDay);

			$endYear = date('Y', $model->end_timestamp);
			$endMon = date('m', $model->end_timestamp);
			$endDay = date('d', $model->end_timestamp);
			$endDate =  sprintf("%04d年%0s月%02d日", $endYear, $endMon, $endDay);
		}
		$bgimg = 'http://img.admissions.daystaracademy.cn/img/ea/home-102.jpg';
		if (in_array($tModel->id, array(4,7))) {
			$bgimg = 'http://img.admissions.daystaracademy.cn/img/ea/bg-ivy-01.jpg';
		}
        if (in_array($tModel->id, array(5,6))) {
            $bgimg = 'http://img.admissions.daystaracademy.cn/img/ea/wh03.jpg';
        }
		$data = array(
			'sid' => $sid,
			'title' => CommonUtils::autoLang($tModel->cn_title, $tModel->en_title),
			'intro' => CommonUtils::autoLang($tModel->cn_intro, $tModel->en_intro),
			'intro2' => CommonUtils::autoLang($tModel->cn_intro2, $tModel->en_intro2),
			'start' => $startDate,
			'end' => $endDate,
			'url' => $loginUrl,
			'state' => $state,
			'bgimg' => $bgimg,
		);
        $this->subPageTitle = Yii::t("navigations", "Survey");

        $this->render('reenrollment', array('data' => $data));
    }
}
