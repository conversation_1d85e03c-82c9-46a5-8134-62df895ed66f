<!-- Modal -->
<?php 
    $labels = Invoice::model()->attributeLabels();
?>
<?php echo CHtml::form('/workflow/entrance/index', 'post',array('class'=>'J_ajaxForm'));?>
<div class="modal fade" id="<?php echo $data['modalNameId']?>" tabindex="-1" role="dialog" aria-labelledby="workflowModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel"><?php echo CommonUtils::autoLang($data['workflow']->definationObj->defination_name_cn,$data['workflow']->definationObj->defination_name_en);?> - <?php echo CommonUtils::autoLang($data['workflow']->nodeObj->node_name_cn,$data['workflow']->nodeObj->node_name_en);?></h4>
            </div>
            <div class="modal-body" id="workflow-modal-body">
                <div class="alert alert-warning" role="alert">学费账单更改会遇到账单使用预缴学费情况，不需要考虑预缴学费，按照实际情况填写金额，系统会自动冲抵预缴学费。</div>
                <ul class="nav nav-wizard mb20">
                    <li style="width:33%;"><a>第一步：选择孩子</a></li>
                    <li style="width:33%;"><a>第二步：选择账单</a></li>
                    <li style="width:33%;" class="active"><a>第三步：更改账单</a></li>
                </ul>
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <td><?php echo $labels['title'];?></td>
                                <td><?php echo $labels['payment_type'];?></td>
                                <td><?php echo $labels['amount'];?></td>
                                <td><?php echo $labels['startdate'];?></td>
                                <td><?php echo $labels['enddate'];?></td>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($data['invoices'] as $invoice):?>
                            <tr>
                                <td><?php echo $invoice->title;?></td>
                                <td><?php echo $invoice->payment_type;?></td>
                                <td><?php echo number_format($invoice->amount,2);?></td>
                                <td><?php echo OA::formatDateTime($invoice->startdate);?></td>
                                <td><?php echo OA::formatDateTime($invoice->enddate);?></td>
                            </tr>
                            <tr class="active">
                                <td colspan="2">
                                    <div class="form-group" style="width: 250px;">
                                        <?php echo CHtml::textField("InvoiceChange[".$invoice->invoice_id."][title]", $invoice->title, array('placeholder'=>'账单标题','class'=>'form-control'));?>
                                    </div>
                                    <div class="form-group" style="width: 250px;">
                                        <?php echo CHtml::textField("InvoiceChange[".$invoice->invoice_id."][amount]",$invoice->amount,array('placeholder'=>'账单金额','class'=>'form-control'));?>
                                    </div>
                                </td>
                                <td colspan="3">
                                    <div class="form-group" style="width: 250px;">
                                        <?php echo CHtml::textField("InvoiceChange[".$invoice->invoice_id."][startdate]",OA::formatDateTime($invoice->startdate),array('placeholder'=>'开始日期','class'=>'form-control'));?>
                                    </div>
                                    <div class="form-group" style="width: 250px;">
                                        <?php echo CHtml::textField("InvoiceChange[".$invoice->invoice_id."][enddate]",OA::formatDateTime($invoice->enddate),array('placeholder'=>'结束日期','class'=>'form-control'));?>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach;?>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <?php echo CHtml::hiddenField('definationId', $data['workflow']->getWorkflowId());?>
                <?php echo CHtml::hiddenField('nodeId', $data['workflow']->getCurrentNodeId());?>
                <?php echo CHtml::hiddenField('branchId', $data['workflow']->schoolId);?>
                <?php echo CHtml::hiddenField('operationId', $data['workflow']->getOperateId());?>
                <?php echo CHtml::hiddenField('step', 'three');?>
                <?php echo CHtml::hiddenField('buttonCategory', '');?>
                <?php echo CHtml::hiddenField('action', 'publicSave');?>
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('global','Cancel');?></button>
                <button type="button" class="btn btn-info J_ajax_submit_btn" data-category="previous"><?php echo Yii::t('workflow','Prev');?></button>
                <button type="button" class="btn btn-primary J_ajax_submit_btn" data-category="next"><?php echo Yii::t('global','Submit');?></button>
            </div>
        </div>
    </div>
</div>
<?php echo CHtml::endForm();?>
<script>
    var invoices = <?php echo CJSON::encode($data['invoices']);?>;
    _.each(invoices,function(val,key){
        $("#InvoiceChange_"+val.invoice_id+"_startdate").datepicker({'dateFormat':'yy-mm-dd'});
        $("#InvoiceChange_"+val.invoice_id+"_enddate").datepicker({'dateFormat':'yy-mm-dd'});
    })
</script>