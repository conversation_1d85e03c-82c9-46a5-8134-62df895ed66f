<?xml version="1.0" encoding="utf-8" ?>

<phpunit backupGlobals="true"
         backupStaticAttributes="false"
         bootstrap="/path/to/bootstrap.php"
         cacheTokens="false"
         columns="80"
         colors="false"
         stderr="false"
         convertErrorsToExceptions="true"
         convertNoticesToExceptions="true"
         convertWarningsToExceptions="true"
         forceCoversAnnotation="false"
         mapTestClassNameToCoveredClassName="false"
         printerClass="PHPUnit_TextUI_ResultPrinter"
         stopOnFailure="false"
         testSuiteLoaderClass="PHPUnit_Runner_StandardTestSuiteLoader"
         timeoutForSmallTests="1"
         timeoutForMediumTests="10"
         timeoutForLargeTests="60"
         beStrictAboutTestsThatDoNotTestAnything="false"
         beStrictAboutOutputDuringTests="false"
         beStrictAboutTestSize="false"
         beStrictAboutTodoAnnotatedTests="false"
         checkForUnintentionallyCoveredCode="false"
         beStrictAboutChangesToGlobalState="false"
         verbose="false">
  <testsuites>
    <testsuite name="My Test Suite">
      <directory suffix="Test.php" phpVersion="5.3.0" phpVersionOperator=">=">/path/to/files</directory>
      <file phpVersion="5.3.0" phpVersionOperator=">=">/path/to/MyTest.php</file>
    </testsuite>
  </testsuites>

  <groups>
    <include>
      <group>name</group>
    </include>
    <exclude>
      <group>name</group>
    </exclude>
  </groups>

  <filter>
    <blacklist>
      <directory suffix=".php">/path/to/files</directory>
      <file>/path/to/file</file>
      <file>
          /path/to/file
      </file>
      <exclude>
        <directory suffix=".php">/path/to/files</directory>
        <file>/path/to/file</file>
      </exclude>
    </blacklist>
    <whitelist addUncoveredFilesFromWhitelist="true"
               processUncoveredFilesFromWhitelist="false">
      <directory suffix=".php">/path/to/files</directory>
      <file>/path/to/file</file>
      <exclude>
        <directory suffix=".php">/path/to/files</directory>
        <file>/path/to/file</file>
      </exclude>
    </whitelist>
  </filter>

  <listeners>
    <listener class="MyListener" file="/optional/path/to/MyListener.php">
      <arguments>
        <array>
          <element key="0">
            <string>Sebastian</string>
          </element>
        </array>
        <integer>22</integer>
        <string>April</string>
        <double>19.78</double>
        <null/>
        <object class="stdClass"/>
        <file>MyTestFile.php</file>
        <directory>MyRelativePath</directory>
      </arguments>
    </listener>
    <listener class="IncludePathListener" file="ConfigurationTest.php" />
    <listener class="CompactArgumentsListener" file="/CompactArgumentsListener.php"><arguments><integer>42</integer></arguments></listener>
  </listeners>

  <logging>
    <log type="coverage-html" target="/tmp/report" lowUpperBound="50" highLowerBound="90"/>
    <log type="coverage-clover" target="/tmp/clover.xml"/>
    <log type="json" target="/tmp/logfile.json"/>
    <log type="plain" target="/tmp/logfile.txt"/>
    <log type="tap" target="/tmp/logfile.tap"/>
    <log type="junit" target="/tmp/logfile.xml" logIncompleteSkipped="false"/>
    <log type="testdox-html" target="/tmp/testdox.html"/>
    <log type="testdox-text" target="/tmp/testdox.txt"/>
  </logging>

  <php>
    <includePath>.</includePath>
    <includePath>/path/to/lib</includePath>
    <ini name="foo" value="bar"/>
    <const name="FOO" value="false"/>
    <const name="BAR" value="true"/>
    <var name="foo" value="false"/>
    <env name="foo" value="true"/>
    <post name="foo" value="bar"/>
    <get name="foo" value="bar"/>
    <cookie name="foo" value="bar"/>
    <server name="foo" value="bar"/>
    <files name="foo" value="bar"/>
    <request name="foo" value="bar"/>
  </php>

  <selenium>
    <browser name="Firefox on Linux"
             browser="*firefox /usr/lib/firefox/firefox-bin"
             host="my.linux.box"
             port="4444"
             timeout="30000"/>
  </selenium>
</phpunit>

