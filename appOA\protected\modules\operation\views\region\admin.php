<?php
$this->breadcrumbs=array(
	'Ivy Pregions'=>array('admin'),
	'Manage',
);

$this->menu=array(
	array('label'=>'Create IvyPRegion', 'url'=>array('create')),
);
?>

<h1>Manage Ivy Pregions</h1>

<?php $this->widget('zii.widgets.grid.CGridView', array(
	'id'=>'ivy-pregion-grid',
	'dataProvider'=>$model->search(),
	'filter'=>$model,
	'columns'=>array(
		'id',
		'en_title',
		'cn_title',
		array(
			'name' => 'status',
            'value' => '$data->showStatus()',
            'filter'=>CHtml::activeDropDownList($model, 'status',array(''=>Yii::t("operations", Yii::t('global','Please Select')))+array(Yii::t('operations', '显示'),Yii::t('operations', '隐藏'))),
		),
		array(
			'name' => 'update_timestamp',
            'sortable' => true,
            'value' => 'OA::formatDateTime($data->update_timestamp)',
            //'filter'=>CHtml::activeDropDownList($model, 'issuer_id',array('0'=>Yii::t("bview", '全部'))+$model->getAwardsIssuer()),
            'filter' => false,
		),
		array(
			'class'=>'CButtonColumn',
		 	'template' => '{update} {delete}',
		 	'updateButtonUrl' => 'Yii::app()->createUrl("/operations/region/create", array("id" => $data->id))',
		),
	),
)); ?>
