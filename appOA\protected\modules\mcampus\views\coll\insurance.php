<div id='homeAddress'>
    <div class="progress">
        <div class="progress-bar progress-bar-success" :style="'width:'+ parsefloat(filled/total) +'%;'">
            <span v-if='filled!=0' :title="filled+'/'+total">{{parsefloat(filled/total)}}% ({{filled}}/{{total}})</span>
            <span v-else>0% ({{filled}}/{{total}})</span>
        </div>
    </div>
    <div class='pull-left' id="nameSearchComp"></div>
    <p class='pull-right'>
        <button type="button" class="btn btn-default btn-sm"  v-if='type=="filled"' :disabled='btnCon' @click='batchPass("modal")'>批量通过</button>
        <button type="button" class="btn btn-default btn-sm ml10"  @click='exportTab()'>导出表格</button>
        <button type="button" class="btn btn-primary btn-sm ml10" v-if='type=="filled"' :disabled='btnCon'  @click='print()'>打印</button>
    </p>
    <div class='clearfix'></div>
    <div class='relative'>
        <div class='loading' v-show='loading'>
            <span></span>
        </div>
        <ul class="nav nav-tabs" role="tablist">
            <li role="presentation" class="active"><a href="#home" aria-controls="home" role="tab" data-toggle="tab"  @click='getInfo("filled")'>已填写 <span class="badge">{{filled}}</span></a></li>
            <li role="presentation"><a href="#unstart" aria-controls="unstart" role="tab" data-toggle="tab" @click='getInfo("unfilled")'>未填写 <span class="badge badge-error">{{unfilled}}</span></a></li>
        </ul>
        <div class="tab-content">
            <div role="tabpanel" class="tab-pane active" id="home">
                <table class="table table-bordered mt20" id='filled' v-show='list.length!=0'>
                    <thead>
                    <tr>
                        <th width='30' class="nosort">
                            <input type="checkbox" id="inlineCheckbox1" value="option1"  v-model='all'  @change='checkAll($event)'>
                        </th>
                        <th width='150'><?php echo Yii::t('global', 'Name') ?></th>
                        <th width='200'><?php echo Yii::t('labels', 'Class') ?></th>
                        <th width='150'><?php echo Yii::t('labels', 'Date of Birth') ?></th>
                        <th width='100'><?php echo Yii::t('labels', 'Status') ?></th>
                        <th width='150'>提交时间</th>
                        <th width='100' class="nosort"><?php echo Yii::t('global', 'Action') ?></th>
                    </tr>
                    </thead>
                    <tbody v-if='isShow'>
                        <tr v-for='(item,index) in list'>
                            <th>
                                <input type="checkbox" id="inlineCheckbox1" :value="item.childid" :disabled='item.step_status==4?true:false' v-model='child_ids' @change='childCheck($event)'>
                            </th>
                            <td >
                                <a href="javascript:;"  @click="overalls(item.childid)">
                                    {{item.name}}
                                </a>
                            </td>
                            <td > {{item.class_name}}</td>
                            <td >{{item.birthday}}</td>
                            <td >
                                <i class='glyphicon glyphicon-remove-sign' v-if='item.step_status==3' style='color:#D5514E'></i>
                                <i class='glyphicon glyphicon-ok-sign'  v-if='item.step_status==4' style='color:#5cb85c'></i>
                                <i class='glyphicon glyphicon-registration-mark'  v-if='item.step_status==1' style='color:#f0ad4e'></i>
                                <i class='glyphicon glyphicon-question-sign'  v-if='item.step_status==0' style='color:#f0ad4e'></i>    
                            {{statusList[item.step_status]}}</td>
                            
                            <td >{{item.step_data.submitted_at}} </td>
                            <td >
                                <a href='javascript:;' class="btn btn-primary btn-xs" @click='auditList(index,"no")'><?php echo Yii::t('global', 'View Detail') ?></a>
                                <a  class="btn btn-primary btn-xs" :href="'<?php echo $this->createUrlReg('PrintDetail', array('branchId' => $this->branchId,'coll_id'=>$coll_id)); ?>&childid='+item.childid+'&step_id=insurance'" target="_blank">
                                    <?php echo Yii::t('global', 'Print') ?>
                                </a>
                            </td>
                        </tr>
                    </tbody>
                </table>
                
                <div class="mt15" v-show='list.length==0'>
                    <div class="alert alert-warning" role="alert"><?php echo Yii::t('ptc', 'No Data') ?></div>
                </div>
            </div>
            <!-- 未填写的数据 -->
            <div role="tabpanel" class="tab-pane " id="unstart">    
                <table class="table table-bordered mt20" id='unfilled' v-show='unList.length!=0'>
                    <thead>
                    <tr>
                        <th><?php echo Yii::t('global', 'Name') ?></th>
                        <th><?php echo Yii::t('labels', 'Class') ?></th>
                        <th><?php echo Yii::t('labels', 'Date of Birth') ?></th>
                        <th><?php echo Yii::t('labels', 'Status') ?></th>
                    </tr>
                    </thead>
                    <tbody  v-if='isShow'>
                    <tr v-for='(item,index) in unList'>
                            <td >
                                <a href="javascript:;"  @click="overalls(item.childid)">
                                    {{item.name}}
                                </a>
                            </td>
                            <td > {{item.class_name}}</td>
                            <td >{{item.birthday}}</td>
                            <td >{{statusList[item.step_status]}}</td>
                        </tr>
                    </tbody>
                </table>
                
                <div class="mt15" v-show='unList.length==0'>
                    <div class="alert alert-warning" role="alert"><?php echo Yii::t('ptc', 'No Data') ?></div>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" tabindex="-1" role="dialog" id='auditDialog' data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            <h4 class="modal-title"><?php echo Yii::t('global', 'View Detail') ?></h4>
            </div>
            <div class="modal-body" v-if='Object.keys(rejectionList).length != 0'>
                <div class="media col-md-12 ">
                    <div class="media-left pull-left media-middle">
                        <a href="javascript:void(0)">
                            <img :src="rejectionList.avatar" data-holder-rendered="true" class="media-object img-circle avatarImage">
                        </a>
                    </div> 
                    <div class="media-body pt10 media-middle">
                        <h4 class="media-heading font14">{{rejectionList.name}}</h4> 
                        <div class="text-muted">{{rejectionList.class_name}}</div>
                    </div>
                </div>  
                <div class='clearfix'></div>
                <div class='mt20 mb20'>
                    <div class='col-md-4 mt10'><?php echo Yii::t('reg', 'Has your child ever signed up for Beijing Social Medical Insurance') ?>：
                        <span :style="{color:setColor(rejectionList.step_data.is_insured,rejectionList.step_data.last_submitted_log.is_insured??rejectionList.step_data.is_insured)}">{{rejectionList.step_data.is_insured==1?'是':'否'}}</span>
                    </div>
                    <template v-if='rejectionList.step_data.is_insured==1'>
                        <div class='col-md-4 mt10'><?php echo Yii::t('reg', 'Do you agree to continue Beijing Social Medical Insurance through Daystar Academy?') ?>：
                            <span :style="{color:setColor(rejectionList.step_data.is_continued,rejectionList.step_data.last_submitted_log.is_continued??rejectionList.step_data.is_continued)}">{{rejectionList.step_data.is_continued==1?'是':'否'}}</span>
                        </div>
                    </template>
                    <template v-if='rejectionList.step_data.is_continued==1 && rejectionList.step_data.is_insured==1'>
                        <div class='col-md-4 mt10'><?php echo Yii::t('reg', "Student's ID Number") ?>：
                            <span :style="{color:setColor(rejectionList.step_data.student_id,rejectionList.step_data.last_submitted_log.student_id??rejectionList.step_data.student_id)}">{{rejectionList.step_data.student_id}}</span>
                        </div>
                        <div class='col-md-4 mt10'><?php echo Yii::t('reg', "Phone number registered while applying for Social Medical insurance") ?>：
                            <span :style="{color:setColor(rejectionList.step_data.insured_phone,rejectionList.step_data.last_submitted_log.insured_phone??rejectionList.step_data.insured_phone)}">{{rejectionList.step_data.insured_phone}}</span>
                        </div>
                    </template> 
                </div>
                <div class='clearfix'></div>
                <div class='col-md-12  mt10'  v-if='rejectionList.step_data.is_insured==2'>参保类型：
                    <span :style="{color:setColor(rejectionList.step_data.insurance_type,rejectionList.step_data.last_submitted_log.insurance_type ?? rejectionList.step_data.insurance_type)}">{{specialList[rejectionList.step_data.insurance_type]}}</span>
                </div>
                <div class='clearfix'></div>
                <template v-if='rejectionList.step_data.is_insured==2'>
                    <div class='col-md-12 mt20 mb20' v-if='rejectionList.step_data.insurance_type==3'>
                        <p><?php echo Yii::t('reg', "1:1 ratio duplicate or scan of Student's Household Paper - front page and the page containing student's information or the front and back of the Residence Permit for Hong Kong, Macaoand Taiwan residents issued by the Beijing Municipal Public Security Bureau") ?></p> <img v-for='(list,index) in rejectionList.step_data.insurance_file_url.student_account' @click='bigImg(list)' :src="list" alt="" class='stepUpImg'>
                    </div>
                    <div class='col-md-12 mt20 mb20' v-if='rejectionList.step_data.insurance_type==4 || rejectionList.step_data.insurance_type==5 || rejectionList.step_data.insurance_type==6'>
                        <p><?php echo Yii::t('reg', "1:1 ratio duplicate or scan of Student's Household Paper - front page and thepage containing student's information.") ?></p> <img v-for='(list,index) in rejectionList.step_data.insurance_file_url.student_account' @click='bigImg(list)' :src="list" alt="" class='stepUpImg'>
                    </div>
                    <div class='col-md-12 mt20 mb20' v-if='rejectionList.step_data.insurance_type==3'>
                        <p><?php echo Yii::t('reg', '1:1 ratio duplicate or scan the front and back of Student’s 2nd Generation ID.  No need to submit if student has not applied for 2nd Generation ID.') ?></p> <img v-for='(list,index) in rejectionList.step_data.insurance_file_url.student_id_card' :src="list" @click='bigImg(list)' alt="" class='stepUpImg'>
                    </div>
                    <div class='col-md-12 mt20 mb20' v-if='rejectionList.step_data.insurance_type==3 || rejectionList.step_data.insurance_type==4 || rejectionList.step_data.insurance_type==5 || rejectionList.step_data.insurance_type==6 || rejectionList.step_data.insurance_type==7'>
                        <p><?php echo Yii::t('reg', 'Please upload recent 1 inch color ID photo with white background.  Photo needs to meet the requirement of 2nd Generation ID card.') ?></p> <img v-for='(list,index) in rejectionList.step_data.insurance_file_url.student_photo' :src="list" @click='bigImg(list)' alt="" class='stepUpImg'>
                    </div>
                    
                    <div class='col-md-12 mt20 mb20' v-if='rejectionList.step_data.insurance_type==4'>
                        <p><?php echo Yii::t('reg', 'par1:1 ratio duplicate or scan of either Father or Mother’s Household Paper  and the page  containing the student information.ent_account') ?></p> <img v-for='(list,index) in rejectionList.step_data.insurance_file_url.parent_account' @click='bigImg(list)' :src="list" alt="" class='stepUpImg'>
                    </div>
                    <div class='col-md-12 mt20 mb20' v-if='rejectionList.step_data.insurance_type==4 || rejectionList.step_data.insurance_type==5 || rejectionList.step_data.insurance_type==6'>
                        <p><?php echo Yii::t('reg', '1:1 ratio duplicate or scan of student’s birth certificate.  If document is not in Chinese, then must be translated into Chinese and bear official chops from either translation or notary company.') ?></p> <img v-for='(list,index) in rejectionList.step_data.insurance_file_url.student_birth' :src="list" alt="" @click='bigImg(list)' class='stepUpImg'>
                    </div>
                    <div class='col-md-12 mt20 mb20' v-if='rejectionList.step_data.insurance_type==4 || rejectionList.step_data.insurance_type==5 || rejectionList.step_data.insurance_type==6'>
                        <p><?php echo Yii::t('reg', '1:1 ratio duplicate or scan of parents’ marriage license. Step-child must submit divorce paper from birth parents and re-marriage license from step parents. Adopted child must supplement with adoption paper issued by Bureau of Civil Affairs or relevant government agencies.') ?></p> <img v-for='(list,index) in rejectionList.step_data.insurance_file_url.parent_marry' :src="list" alt="" @click='bigImg(list)' class='stepUpImg'>
                    </div>
                    <div class='col-md-12 mt20 mb20' v-if='rejectionList.step_data.insurance_type==4 || rejectionList.step_data.insurance_type==5 || rejectionList.step_data.insurance_type==6'>
                        <p><?php echo Yii::t('reg', '1:1 ratio duplicate or scan of student’s 2nd Generation ID.  No need to submit if not yet apply for.') ?></p> <img v-for='(list,index) in rejectionList.step_data.insurance_file_url.student_id_card1' :src="list" alt="" @click='bigImg(list)' class='stepUpImg'>
                    </div>
                    <div class='col-md-12 mt20 mb20' v-if='rejectionList.step_data.insurance_type==5'>
                        <p><?php echo Yii::t('reg', '1:1 ratio of duplicate or scan of either Father or Mother’s valid Beijing Working and Residing Permit. (Said permit must have student’s name listed as dependent or will be deemed unqualified to apply for Social Medical Insurance) ') ?></p> <img v-for='(list,index) in rejectionList.step_data.insurance_file_url.work_permit' :src="list" @click='bigImg(list)' alt="" class='stepUpImg'>
                    </div>
                    <div class='col-md-12 mt20 mb20' v-if='rejectionList.step_data.insurance_type==6'>
                        <p><?php echo Yii::t('reg', 'Original proof document issued by the unit at or above the regiment level. ') ?></p> <img v-for='(list,index) in rejectionList.step_data.insurance_file_url.political_organ' :src="list" alt="" @click='bigImg(list)' class='stepUpImg'>
                    </div>
                    <div class='col-md-12 mt20 mb20' v-if='rejectionList.step_data.insurance_type==7'>
                        <p><?php echo Yii::t('reg', '1:1 ratio of duplicate or scan of student’s passport.') ?></p> <img v-for='(list,index) in rejectionList.step_data.insurance_file_url.student_passport' :src="list" @click='bigImg(list)' alt="" class='stepUpImg'>
                    </div>
                    <div class='col-md-12 mt20 mb20' v-if='rejectionList.step_data.insurance_type==7'>
                        <p><?php echo Yii::t('reg', '1:1 ratio of duplicate or scan of student’s Alien Permanent Residence ID card.') ?></p> <img v-for='(list,index) in rejectionList.step_data.insurance_file_url.foreigner_id_card' :src="list" @click='bigImg(list)' alt="" class='stepUpImg'>
                    </div>
                    <div class='col-md-12 mt20 mb20' v-if='rejectionList.step_data.insurance_type==7'>
                        <p><?php echo Yii::t('reg', 'Affidavit declaring no medical insurance coverage.') ?></p> <img v-for='(list,index) in rejectionList.step_data.insurance_file_url.commitment' :src="list" @click='bigImg(list)' alt="" class='stepUpImg'>
                    </div>
                    <div class='col-md-12 mt20 mb20' v-if='rejectionList.step_data.insurance_type==3 || rejectionList.step_data.insurance_type==4 || rejectionList.step_data.insurance_type==5 || rejectionList.step_data.insurance_type==6 || rejectionList.step_data.insurance_type==7'>
                        <p><?php echo Yii::t('reg', 'Phone number to apply for the Social Medical Insurance (only Beijing phone number can be used, otherwise part of medical insurance will not be available.)') ?></p>
                        <p >{{rejectionList.step_data.insured_phone_bj}}</p>
                    </div>
                    <div class='col-md-12 mt20 mb20' v-if='rejectionList.step_data.insurance_type==3 || rejectionList.step_data.insurance_type==4 || rejectionList.step_data.insurance_type==5 || rejectionList.step_data.insurance_type==6 || rejectionList.step_data.insurance_type==7'>
                        <p><?php echo Yii::t('reg', 'Choose 3 from National Medical Insurance Designated Hospitals within BJ city as student’s preferred hospital choices') ?></p>
                        <p v-for='(list,index) in rejectionList.step_data.insurance_hospitals'>{{list}}</p>
                    </div>
                </template> 
                <div class='col-md-12 mt20 mb20' v-if='rejectionList.step_data.is_continued==1 && rejectionList.step_data.is_insured==1'>
                    <p>学生医保卡：</p> <img v-for='(list,index) in rejectionList.step_data.insurance_card_url' :src="list" alt="" @click='bigImg(list)' class='stepUpImg'>
                </div>
                <div class='col-md-12 mt20 mb20' v-if='rejectionList.sign_url!=""'>
                    <span class='pull-left'><?php echo Yii::t('event', 'Signature') ?>：</span> <img :src="rejectionList.sign_url" alt="" class='signImgDetails'>
                </div>
                <div class='clearfix'></div>          
                <div class="panel panel-default mt20">
                    <div class="panel-heading">
                        <h3 class="panel-title"><?php echo Yii::t('reg', '审核') ?></h3>
                    </div>
                    <div class="panel-body">
                        <div class='col-md-12 col-sm-12 font14'>
                            <div class='col-md-6 col-sm-6 borderRight scroll-box' style='max-height:250px;overflow-y:auto'>
                                <p><strong>当前审核状态</strong></p>
                                
                                <div>
                                    {{statusList[rejectionList.step_status]}}
                                    <button type="button" class="btn btn-primary  btn-xs pull-right" @click='resetStatus()'  v-if='rejectionList.step_status==3 || rejectionList.step_status==4'><?php echo Yii::t('global', 'Reset') ?></button>
                                </div>
                                <p class='mt10'><strong>审核记录</strong></p>
                                <div>
                                    <div v-for='(list,index) in rejectionList.audit_log' class='ml10' style='border-left:1px dashed #ccc'>
                                    <p style='margin-left:-7px;background: #fff;' >
                                        <span v-if='list.status==4'>
                                        <span class='glyphicon glyphicon-ok-sign greenIcon'></span><span> <?php echo Yii::t('global', 'Confirmed') ?></span> 
                                        </span>
                                        <span v-if='list.status==3'>
                                        <span class='glyphicon glyphicon-info-sign redIcon'></span> <span> <?php echo Yii::t('reg', 'Rejected') ?></span>
                                        </span>
                                        <span v-if='list.status==0'>
                                        <span class='glyphicon glyphicon-question-sign yellowIcon'></span> <span> <?php echo Yii::t('global', 'Reset') ?></span>
                                        </span>
                                        </p> 
                                    <p class='ml10' style='background:#F7F7F8;line-height:22px'  v-if='list.comment!="" && list.comment!=null'><?php echo Yii::t('reg', '备注：') ?>{{list.comment}}</p>
                                    <p class='pl10 font12'>{{list.date}} {{list.user}}</p>
                                    </div>
                                </div>
                            </div>
                            <div class='col-md-6 col-sm-6' >
                                <p><strong>审核</strong></p>
                                <div class='p10 grey'>
                                    <label class="radio ml20">
                                        <input type="radio" id="inlineradio1" v-model='audit_status' value="4"> <?php echo Yii::t('labels', 'Yes') ?>
                                    </label>
                                    <label class="radio ml20">
                                        <input type="radio" id="inlineradio2" v-model='audit_status' value="3"> 未通过
                                    </label>
                                    <textarea class="form-control" rows="3" placeholder='请输入备注' v-model='comment'></textarea>
                                    <div v-if='audit_status==3 || type_status==2'>
                                        <p class='mt10'>修改建议将直接显示在家长端，请准确措辞。</p>   
                                        <div class="checkbox">
                                            <label>
                                                <input type="checkbox"  v-model='send_wechat'>将审核结果发送至家长微信
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class='mt20'>
                                    <button type="button"  class="btn btn-primary pull-right" :disabled='btnCon' @click='saveInfo()'><?php echo Yii::t("newDS", "确认");?></button>
                                    <button type="button" class="btn btn-default  pull-right mr10" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class='clearfix'></div>
                <div class="panel panel-default mt20">
                    <div class="panel-heading">
                        <h3 class="panel-title"><?php echo Yii::t('reg', 'Summary of Nurse Review Status') ?></h3>
                    </div>
                    <div class="panel-body">
                        <div class=''>
                            <div><label for=""><?php echo Yii::t('reg', 'Social Medical Insurance') ?></label></div>
                            <label class="radio-inline" v-for='(list,key,i) in medicalInsurance'>
                                <input type="radio" name="medicalHistory" v-model='remarks.socialInsurance' :value="key"> {{list}}
                            </label>
                            <input type="text" class="form-control mt10"  placeholder="输入详细信息"  v-model='remarks.socialInsuranceDetail'>
                        </div>
                        <div class='mt20'>
                            <button type="button"  class="btn btn-primary pull-right ml10" :disabled='btnCon' @click='saveRemarks()'><?php echo Yii::t("newDS", "确认");?></button>
                            <button type="button" class="btn btn-default  pull-right" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        </div>
    </div>
    <div class="modal fade" id="stuDetails" tabindex="-1" role="dialog" >
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" ><?php echo Yii::t('attends', '整体情况') ?> <span id="remarks_t"></span></h4>
                </div>
                <div class="modal-body color6" v-if='childData.child'>
                    <p><strong class='font14 color3'>学生资料</strong></p>
                    <div class='flex'>
                        <div style='width:80px'><img :src="childData.child.avatar" class='stuImg'></div>
                        <div class='flex1'>
                            <p class='mt10'><strong class='font14 color3 studentName'>{{childData.child.name}}</strong> </p>
                            <p class='mb20'>{{childData.child.class_name}}</p>
                            <div  v-if='childData.parents.finfo!=null'>
                                <div class='col-md-4'>
                                <strong class='color3'><?php echo Yii::t('global', 'Father') ?>：</strong>  
                                    <span class='glyphicon glyphicon-user'></span> {{childData.parents.finfo.name}}
                                </div>
                                <div class='col-md-4'>
                                    <span class='glyphicon glyphicon-phone'></span> {{childData.parents.finfo.mphone}}
                                </div>
                                <div class='col-md-4'>
                                    <span class='glyphicon glyphicon-envelope'></span> {{childData.parents.finfo.email}}
                                </div>
                            </div>
                            <div class='clearfix'></div>
                            <div class='mt10' v-if='childData.parents.minfo!=null'>
                                <div class='col-md-4'>
                                <strong class='color3'><?php echo Yii::t('global', 'Mother') ?>：</strong> 
                                    <span class='glyphicon glyphicon-user'></span> {{childData.parents.minfo.name}}
                                </div>
                                <div class='col-md-4'>
                                    <span class='glyphicon glyphicon-phone'></span> {{childData.parents.minfo.mphone}}
                                </div>
                                <div class='col-md-4'>
                                    <span class='glyphicon glyphicon-envelope'></span> {{childData.parents.minfo.email}}
                                </div>
                            </div>
                        </div>
                    </div>
                    <ul class="nav nav-wizard pb20 mt20">
                        <li v-for='(list,index) in childData.step_status'>
                            <a href="javascript::" class='color6' :data-tab="list.step_id" :data-status="list.status" :data-name="childData.child.name"><span class="number">{{index+1}}</span>{{list.title}}</a>
                            <p class='mt10 color6' >
                                <span :class="list.status==4?'greenStep':list.status==3?'redStep':list.status==1 || list.status==2?'orgStep':''">{{ statusList[list.status]}}</span></p>
                        </li>
                    </ul>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('global', 'Cancel') ?></button>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="tipsModal" tabindex="-1" role="dialog" >
        <div class="modal-dialog modal-sm" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" ><?php echo Yii::t('attends', '批量通过') ?> <span id="remarks_t"></span></h4>
                </div>
                <div class="modal-body color6" >
                    <div class='color3 font14'>确认批量通过审核吗？</div> 
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('global', 'Cancel') ?></button>
                    <button type="button" class="btn btn-primary" :disabled='btnTips' @click='batchPass()'><?php echo Yii::t('global', '确定') ?></button>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    
    var step_id = '<?php echo $step_id?>';
    var coll_id = '<?php echo $coll_id?>';
    var type = '<?php echo $type?>';
    var type_status = '<?php echo $type_status?>';

    var  registrations = new Vue({
        el: "#homeAddress",
        data: {
            type_status:type_status,
            list:[],
            unfilled:0,
            coll_id:coll_id,
            filled:0,
            total:0,
            type:type,
            logList:[],
            statusList:[],
            audit_status:'',
            comment:'',
            child_id:'',
            childData:{},
            child_ids:[],
            rejectionList:{},
            unList:{},
            btnCon:false,
            send_wechat:true,
            isShow:true,
            specialList: {
                1:"<?php echo Yii::t('reg', 'Students DO NOT meet any of the criteria listed above.') ?>",
                2:"<?php echo Yii::t('reg', 'Qualified but willingly forgo the opportunity to participate the Social Medical Insurance with the school.') ?>",
                3:"<?php echo Yii::t('reg', 'Student with a Beijing Household Resident Status or Holders of Residence Permit for Hong Kong, Macao and Taiwan residents issued by the Beijing Municipal Public Security Bureau') ?>",
                4:"<?php echo Yii::t('reg', 'Student without Beijing Household Resident Status  – one of the parents is BJ Household Resident') ?>",
                5:"<?php echo Yii::t('reg', 'Student without Beijing Household Resident Status – one of the parents has official Beijing Working and Residing Permit.') ?>",
                6:"<?php echo Yii::t('reg', 'Minor children of Military Personnel') ?>",
                7:"<?php echo Yii::t('reg', 'Students of foreign nationals have Alien Permanent Residence ID Card and do not have any medical insurance.') ?>",
            },
            medicalInsurance:{
                1:'<?php echo Yii::t('reg', 'Applied through school') ?>',
                2:'<?php echo Yii::t('reg', 'Ineligible to apply') ?>',
                3:'<?php echo Yii::t('reg', 'Waiver') ?>',
            },
            remarks:{},
            resetStatusIndex:'',
            loading:false,
            all:false,
            btnTips:false,
        },
        created: function() {
            this.getInfo(this.type)
            eventBus.$on('listChanged', list => {
                if(sessionStorage.getItem('tabType')  === 'filled'){
                    this.list = list;
                }else{
                    this.unList = list
                }
            });
        },
        watch: {
        },
        methods: {
            setColor(newData,oldData){
                return newData != oldData ? 'red' : 'initial';
            },
            overalls(id){
                let that=this
                $.ajax({
                    url:'<?php echo $this->createUrlReg('getStudentOverall'); ?>',
                    type: 'get',
                    dataType:'json',
                    data:{
                        child_id:id,
                        coll_id:coll_id,
                    },
                    success:function(data){
                        if(data.state=="success"){
                            that.childData=data.data
                            $('#stuDetails').modal('show')
                        }else{
                            resultTip({error: 'warning', msg: data.message});
                        }
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            parsefloat(num) {
                return Number(num * 100).toFixed(2);
            },
            getInfo(type){
                let that = this
                this.type=type
                this.isShow=false
                $.ajax({
                    url:'<?php echo $this->createUrlReg('stepTemplateInfo'); ?>',
                    type: 'get',
                    dataType:'json',
                    data:{
                        coll_id:coll_id,
                        step_id:step_id,
                        type:type
                    },
                    success:function(data){
                        if(data.state=="success"){
                            if(type=='unfilled'){
                                that.unList = data.data.list
                            }else{
                                that.list = data.data.list
                            }
                            that.unfilled = data.data.unfilled
                            that.filled = data.data.filled
                            that.total = data.data.count
                            that.statusList=data.data.statusList
                            that.logList=data.data.logList
                            that.isShow=true
                            if(that.resetStatusIndex!==''){
                                that.auditList(that.resetStatusIndex)
                            }
                            sessionStorage.setItem('tabType', type);
                            sessionStorage.setItem('listUpdated',  JSON.stringify(data.data.list));
                            that.$forceUpdate();
                            that.$nextTick(()=>{
                                that.DataTable(data)
                            })
                        }else{
                            resultTip({error: 'warning', msg: data.message});
                        }
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            DataTable(data){
                let that=this
                    that.$nextTick(()=>{
                        $('#stuDetails').modal('hide');
                        $('#nameSearchComp').html(data.data.nameSearchComp)
                        if(that.type=='filled'){
                            var table = $('#filled').DataTable({
                                aaSorting: [5, 'desc'], // 默认排序
                                paging: false,
                                info: false,
                                searching: false,
                                "destroy": true,
                                columnDefs: [ {
                                    "targets": 'nosort',
                                    "orderable": false
                                } ],
                            });
                        }else{
                            var table = $('#unfilled').DataTable({
                                aaSorting: [1, 'desc'], // 默认排序
                                paging: false,
                                info: false,
                                "destroy": true,
                                searching: false,
                            });
                        }                    
                    })  
               
            },
            auditList(index,type){
                this.resetStatusIndex=index
                this.rejectionList=this.list[index]
                if(this.list[index].step_data.remarks && this.list[index].step_data.remarks.length!=0){
                    this.remarks=this.list[index].step_data.remarks
                }else{
                    this.remarks={
                        socialInsurance:'',
                        socialInsuranceDetail:'',
                    }
                }
                this.child_id=this.list[index].childid
                this.audit_status=''
                this.comment=''
                if(type){
                    $('#auditDialog').modal('show')
                }
            },
            exportTab() {
                if(this.type=='filled'){
                    const filename ='学生社保已填写.xlsx';
                    const ws_name = "SheetJS";
                    var exportDatas = [];
                    for(var i=0;i<this.list.length;i++){
                        var data={
                            'ID':this.list[i].childid,
                            '<?php echo Yii::t('global', 'Name') ?>':this.list[i].name,
                            "<?php echo Yii::t('labels', 'Class') ?>":this.list[i].className,
                            "<?php echo Yii::t('labels', 'Date of Birth') ?>":this.list[i].birthday,
                            "<?php echo Yii::t('labels', 'Status') ?>":this.statusList[this.list[i].step_status],

                            "<?php echo Yii::t('labels', 'Barcode') ?>":this.list[i].barcode,
                            "<?php echo Yii::t('labels', 'Educational Student ID') ?>":this.list[i].educational_id,
                            "<?php echo Yii::t("labels",'中文法定名')?>": this.list[i].is_legel_cn_name==1 ? '是':'否',
                            "<?php echo Yii::t("labels",'Chinese Name')?>":this.list[i].cn_name,
                            "<?php echo Yii::t("labels",'Legal First Name')?>":this.list[i].first_name_en,
                            "<?php echo Yii::t("labels",'Legal Middle Name')?>":this.list[i].middle_name_en,
                            "<?php echo Yii::t("labels",'Legal Last Name')?>":this.list[i].last_name_en,
                            "<?php echo Yii::t("labels",'Preferred Name')?>":this.list[i].nick,
                            "<?php echo Yii::t("labels",'Gender')?>":this.list[i].gender == 1 ? '男' : '女',
                            "<?php echo Yii::t("labels",'Nationality')?>":this.list[i].country,
                            "<?php echo Yii::t("labels",'Language')?>":this.list[i].lang,
                            "<?php echo Yii::t("labels",'Father Name')?>":this.list[i].father_name,
                            "<?php echo Yii::t("labels",'Father Email')?>":this.list[i].father_email,
                            "<?php echo Yii::t("labels",'Father Mobile')?>":this.list[i].father_phone,
                            "<?php echo Yii::t("labels",'Mother Name')?>":this.list[i].mather_name,
                            "<?php echo Yii::t("labels",'Mother Email')?>":this.list[i].mather_email,
                            "<?php echo Yii::t("labels",'Mother Mobile')?>":this.list[i].mather_phone,
                            "入学日期":this.list[i].attend_school_time,
                            "<?php echo Yii::t("labels",'Dwelling Place')?>":this.list[i].address,
                            "<?php echo Yii::t("labels",'ID NO./Passport NO.')?>":this.list[i].identity,
                            "入学学年":this.list[i].first_startyear,

                            "是否参保":this.list[i].step_data.is_insured==1?'是':'否',
                            "是否续保":this.list[i].step_data.is_continued==1?'是':'否',
                            "身份证号":this.list[i].step_data.is_continued==1?this.list[i].step_data.student_id:'',
                            "手机号":this.list[i].step_data.is_continued==1?this.list[i].step_data.insured_phone:'',
                            "提交时间":this.list[i].step_data.submitted_at,
                            "护士审核":this.list[i].step_data.remarks!=null?'是':'否',
                        }
                        if(this.list[i].step_data.remarks!=null){
                            data=Object.assign(data,{
                                "学生社会医疗保险":this.medicalInsurance[this.list[i].step_data.remarks.socialInsurance],
                                "学生社会医疗保险备注":this.list[i].step_data.remarks.socialInsuranceDetail,
                            })
                        }
                    exportDatas.push(data)
                    }
                    var wb=XLSX.utils.json_to_sheet(exportDatas,{
                        origin:'A1',// 从A1开始增加内容
                        header: ['ID',
                            '<?php echo Yii::t('global', 'Name') ?>',
                            '<?php echo Yii::t('labels', 'Class') ?>',
                            '<?php echo Yii::t('labels', 'Date of Birth') ?>',
                            '<?php echo Yii::t('labels', 'Status') ?>',
                            '<?php echo Yii::t('labels', 'Barcode') ?>',
                            '<?php echo Yii::t('labels', 'Educational Student ID') ?>',
                            '<?php echo Yii::t('labels','中文法定名')?>',
                            '<?php echo Yii::t('labels','Chinese Name')?>',
                            '<?php echo Yii::t('labels','Legal First Name')?>',
                            '<?php echo Yii::t('labels','Legal Middle Name')?>',
                            '<?php echo Yii::t('labels','Legal Last Name')?>',
                            '<?php echo Yii::t('labels','Preferred Name')?>',
                            '<?php echo Yii::t('labels','Gender')?>',
                            '<?php echo Yii::t('labels','Nationality')?>',
                            '<?php echo Yii::t('labels','Language')?>',
                            '<?php echo Yii::t('labels','Father Name')?>',
                            '<?php echo Yii::t('labels','Father Email')?>',
                            '<?php echo Yii::t('labels','Father Mobile')?>',
                            '<?php echo Yii::t('labels','Mother Name')?>',
                            '<?php echo Yii::t('labels','Mother Email')?>',
                            '<?php echo Yii::t('labels','Mother Mobile')?>',
                            '入学日期',
                            '<?php echo Yii::t('labels','Dwelling Place')?>',
                            '<?php echo Yii::t('labels','ID NO./Passport NO.')?>',
                            '入学学年',
                            '是否参保',
                            '是否续保',
                            '身份证号',
                            '手机号',
                            '提交时间',
                            '护士审核',
                            '学生社会医疗保险',
                            '学生社会医疗保险备注'],
                    });
                    const workbook = XLSX.utils.book_new();
                    XLSX.utils.book_append_sheet(workbook, wb, ws_name);
                    const wbout = XLSX.write(workbook, {bookType: 'xlsx', type: 'array'});
                    const blob = new Blob([wbout], {type: 'application/octet-stream'});
                    let link = document.createElement('a');
                    link.href = URL.createObjectURL(blob);
                    link.download = filename;
                    link.click();
                    setTimeout(function() {
                        // 延时释放掉obj
                        URL.revokeObjectURL(link.href);
                        link.remove();
                    }, 500);
                }else{  
                    var title='学生社保未填写.xlsx'
                    const ws_name = "SheetJS";
                    var exportDatas = [];
                    for(var i=0;i<this.unList.length;i++){
                        exportDatas.push(
                            {
                            'ID':this.unList[i].childid,
                            '<?php echo Yii::t('global', 'Name') ?>':this.unList[i].name,
                            "<?php echo Yii::t('labels', 'Class') ?>":this.unList[i].className,
                            "<?php echo Yii::t('labels', 'Date of Birth') ?>":this.unList[i].birthday,
                            "<?php echo Yii::t('labels', 'Status') ?>":this.statusList[this.unList[i].step_status],

                            "<?php echo Yii::t('labels', 'Barcode') ?>":this.unList[i].barcode,
                            "<?php echo Yii::t('labels', 'Educational Student ID') ?>":this.unList[i].educational_id,
                            "<?php echo Yii::t("labels",'中文法定名')?>": this.unList[i].is_legel_cn_name==1 ? '是':'否',
                            "<?php echo Yii::t("labels",'Chinese Name')?>":this.unList[i].cn_name,
                            "<?php echo Yii::t("labels",'Legal First Name')?>":this.unList[i].first_name_en,
                            "<?php echo Yii::t("labels",'Legal Middle Name')?>":this.unList[i].middle_name_en,
                            "<?php echo Yii::t("labels",'Legal Last Name')?>":this.unList[i].last_name_en,
                            "<?php echo Yii::t("labels",'Preferred Name')?>":this.unList[i].nick,
                            "<?php echo Yii::t("labels",'Gender')?>":this.unList[i].gender == 1 ? '男' : '女',
                            "<?php echo Yii::t("labels",'Nationality')?>":this.unList[i].country,
                            "<?php echo Yii::t("labels",'Language')?>":this.unList[i].lang,
                            "<?php echo Yii::t("labels",'Father Name')?>":this.unList[i].father_name,
                            "<?php echo Yii::t("labels",'Father Email')?>":this.unList[i].father_email,
                            "<?php echo Yii::t("labels",'Father Mobile')?>":this.unList[i].father_phone,
                            "<?php echo Yii::t("labels",'Mother Name')?>":this.unList[i].mather_name,
                            "<?php echo Yii::t("labels",'Mother Email')?>":this.unList[i].mather_email,
                            "<?php echo Yii::t("labels",'Mother Mobile')?>":this.unList[i].mather_phone,
                            "入学日期":this.unList[i].attend_school_time,
                            "<?php echo Yii::t("labels",'Dwelling Place')?>":this.unList[i].address,
                            "<?php echo Yii::t("labels",'ID NO./Passport NO.')?>":this.unList[i].identity,
                            "入学学年":this.unList[i].first_startyear,

                            });
                    }
                    var wb=XLSX.utils.json_to_sheet(exportDatas,{
                        origin:'A1',// 从A1开始增加内容
                        header: ['ID',
                            '<?php echo Yii::t('global', 'Name') ?>',
                            '<?php echo Yii::t('labels', 'Class') ?>',
                            '<?php echo Yii::t('labels', 'Date of Birth') ?>',
                            '<?php echo Yii::t('labels', 'Status') ?>'],
                    });
                    const workbook = XLSX.utils.book_new();
                    XLSX.utils.book_append_sheet(workbook, wb, ws_name);
                    const wbout = XLSX.write(workbook, {bookType: 'xlsx', type: 'array'});
                    const blob = new Blob([wbout], {type: 'application/octet-stream'});
                    let link = document.createElement('a');
                    link.href = URL.createObjectURL(blob);
                    link.download = title;
                    link.click();
                    setTimeout(function() {
                        // 延时释放掉obj
                        URL.revokeObjectURL(link.href);
                        link.remove();
                    }, 500);
                }
            },
            checkAll(e){
                this.child_ids=[]
                if(e.target.checked){
                    for(var i=0;i<this.list.length;i++){
                        if(this.list[i].step_status!=4){
                            this.child_ids.push(this.list[i].childid)
                        }
                    }
                    this.all=true
                }else{
                    this.all=false
                }
            },
            childCheck(e){
                let checkListLen=this.list.filter((item)=>{return item.step_status!=4});
                if(e.target.checked){
                    if(this.child_ids.length==checkListLen.length){
                        this.all=true
                    }else{
                        this.all=false
                    }
                }else{
                    this.all=false
                }
            },
            batchPass(modal){
                let that = this
                if(this.child_ids.length==0){
                    resultTip({error: 'warning', msg: '请选择学生'});
                    return
                }
                if(modal){
                    $('#tipsModal').modal('show')
                    return
                }
                this.loading=true
                this.btnCon=true
                this.btnTips=true
                $.ajax({
                    url:'<?php echo $this->createUrlReg('batchPass'); ?>',
                    type: 'post',
                    dataType:'json',
                    data:{
                        child_ids:this.child_ids,
                        step_id:step_id,
                        coll_id:this.coll_id
                    },
                    success:function(data){
                        if(data.state=="success"){
                            resultTip({
								msg: data.state
							});
                            that.getInfo(that.type)
                            $('#auditDialog').modal('hide')
                            $('#tipsModal').modal('hide')
                        }else{
                            resultTip({error: 'warning', msg: data.message});
                        }
                        that.btnCon=false
                        that.loading=false
                        that.btnTips=false
                    },
                    error:function(data){
                        that.btnCon=false
                        that.loading=false
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            print(){
                let that = this
                if(this.child_ids.length==0){
                    resultTip({error: 'warning', msg: '请选择学生'});
                    return
                }
                let  child_ids = this.child_ids.toString()
                window.open('<?php echo $this->createUrlReg('batchPrint'); ?>&childid='+child_ids+'&step_id='+step_id+'&coll_id='+this.coll_id,'_blank');
            },
            resetStatus(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrlReg("stepAudit") ?>',
                    type: "get",
                    dataType: 'json',
                    data:{
                        child_id:this.child_id,
                        step_id:step_id,
                        audit_status:0, 
                        coll_id:this.coll_id,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            resultTip({
								msg: data.state
							});
                            that.getInfo(that.type)
                            // $('#auditDialog').modal('hide')
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                    },
                })                
            },
            saveInfo(){
                let that = this
                if(this.audit_status==''){
                    resultTip({
                        error: 'warning',
                        msg: '请选择审核状态'
                    });
                    return
                }
                that.btnCon=true
                var dataList={
                    child_id:this.child_id,
                    step_id:step_id,
                    audit_status:this.audit_status, 
                    coll_id:this.coll_id,
                    comment: this.comment,
                }
                if(this.audit_status==3 || this.type_status==2){
                   var dataList=Object.assign({send_wechat:that.send_wechat?1:0},dataList)
                }
                $.ajax({
                    url:'<?php echo $this->createUrlReg('stepAudit'); ?>',
                    type: 'get',
                    dataType:'json',
                    data:dataList,
                    success:function(data){
                        if(data.state=="success"){
                            resultTip({
								msg: data.state
							});
                            that.getInfo(that.type)
                            // $('#auditDialog').modal('hide')
                        }else{
                            resultTip({error: 'warning', msg: data.message});
                        }
                        that.btnCon=false
                    },
                    error:function(data){
                        that.btnCon=false
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            bigImg(list){
                var opacityBottom = '<div class="opacityBottom" style = "display:none"><img class="bigImg" src="' + list + '"></div>';
                $(document.body).append(opacityBottom);
                this.toBigImg();//变大函数
            },
            toBigImg() {
                $(".opacityBottom").addClass("opacityBottom");//添加遮罩层
                $(".opacityBottom").show();
                $("html,body").addClass("none-scroll");//下层不可滑动
                $(".bigImg").addClass("bigImg");//添加图片样式
                $(".opacityBottom").click(function () {//点击关闭
                    $("html,body").removeClass("none-scroll");
                    $(".opacityBottom").remove();
                });
            },
            saveRemarks(){
                let that = this
                $.ajax({
                    url:'<?php echo $this->createUrlReg('saveRemarks'); ?>',
                    type: 'get',
                    dataType:'json',
                    data:{
                        child_id:this.child_id,
                        step_id:step_id,
                        remarks:this.remarks, 
                        coll_id:this.coll_id,
                        
                    },
                    success:function(data){
                        if(data.state=="success"){
                            resultTip({
								msg: data.state
							});
                            that.getInfo(that.type)
                            // $('#auditDialog').modal('hide')
                        }else{
                            resultTip({error: 'warning', msg: data.message});
                        }
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
        }
    })
</script>
<style>
    a[data-status="0"] {
        cursor: default;
        text-decoration: none;
    }
</style>
