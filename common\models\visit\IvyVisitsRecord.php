<?php

/**
 * This is the model class for table "ivy_visits_record".
 *
 * The followings are the available columns in table 'ivy_visits_record':
 * @property integer $id
 * @property integer $basic_id
 * @property integer $event_id
 * @property string $category
 * @property string $schoolid
 * @property integer $visit_timestamp
 * @property integer $parent_num
 * @property integer $child_num
 * @property integer $is_ivyparent
 * @property string $from_campus
 * @property string $ad_channel
 * @property integer $status
 * @property integer $update_timestamp
 * @property integer $update_user
 * @property string $ip
 */
class IvyVisitsRecord extends CActiveRecord
{


	const VISITS_STATUS = 10; //最新的数据 有效状态
	const VISIT_STATUS = 20; // 之前的数据 无效状态 在前台不显示
	/**
	 * Returns the static model of the specified AR class.
	 * @return IvyVisitsRecord the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_visits_record';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('basic_id, event_id, visit_timestamp, parent_num, child_num, is_ivyparent, status, update_timestamp, update_user, appointment_date', 'numerical', 'integerOnly'=>true),
			array('category', 'length', 'max'=>25),
			array('schoolid', 'length', 'max'=>25),
			array('from_campus', 'length', 'max'=>30),
			array('appointment_time', 'length', 'max'=>255),
			array('ad_channel', 'length', 'max'=>255),
			array('ip', 'length', 'max'=>50),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, basic_id, event_id, category, schoolid, visit_timestamp, parent_num, child_num, is_ivyparent, from_campus, ad_channel, status, update_timestamp, update_user, ip, appointment_time, appointment_date', 'safe', 'on'=>'search'),
            array('appointment_date, appointment_time', 'required', 'on'=>'changedate'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'basic'=>array(self::BELONGS_TO, 'IvyVisitsBasicInfo', 'basic_id'),
            'newlog'=>array(self::HAS_ONE, 'VisitsTraceLog', 'visit_id', 'order'=>'newlog.update_timestamp desc'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'basic_id' => 'Basic',
			'event_id' => 'Event',
			'category' => 'Category',
			'schoolid' => 'Schoolid',
			'visit_timestamp' => '来访时间',
			'parent_num' => 'Parent Num',
			'child_num' => 'Child Num',
			'is_ivyparent' => 'Is Ivyparent',
			'from_campus' => 'From Campus',
			'ad_channel' => 'Ad Channel',
			'status' => 'Status',
			'update_timestamp' => 'Update Timestamp',
			'update_user' => 'Update User',
			'ip' => 'Ip',
			'appointment_date' => '预约日期',
			'appointment_time' => '预约时间',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('basic_id',$this->basic_id);
		$criteria->compare('event_id',$this->event_id);
		$criteria->compare('category',$this->category,true);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('visit_timestamp',$this->visit_timestamp);
		$criteria->compare('appointment_date',$this->appointment_date);
		$criteria->compare('appointment_time',$this->appointment_time);
		$criteria->compare('parent_num',$this->parent_num);
		$criteria->compare('child_num',$this->child_num);
		$criteria->compare('is_ivyparent',$this->is_ivyparent);
		$criteria->compare('from_campus',$this->from_campus,true);
		$criteria->compare('ad_channel',$this->ad_channel,true);
		$criteria->compare('status',$this->status);
		$criteria->compare('update_timestamp',$this->update_timestamp);
		$criteria->compare('update_user',$this->update_user);
		$criteria->compare('ip',$this->ip,true);

		return new CActiveDataProvider(get_class($this), array(
			'criteria'=>$criteria,
		));
	}
    
    public function searchDate()
	{

		$criteria=new CDbCriteria;
        $criteria->compare('schoolid',$this->schoolid);
        $criteria->compare('t.status',array(0, IvyVisitsRecord::VISITS_STATUS));
        if ($this->appointment_date)
            $criteria->compare('appointment_date', strtotime($this->appointment_date));
        else
            $criteria->compare('appointment_date', '>='.strtotime('today'));
		if ($this->appointment_time){
			$criteria->compare('appointment_time', $this->appointment_time);
		}

		$criteria->with = 'basic';

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
            'sort' => array(
                'defaultOrder' => 't.appointment_date asc',
            ),
            'pagination'=>array(
                'pageSize'=>200,
            ),
		));
	}
}