<?php

/**
 * This is the model class for table "ivy_admissions_ds_temporary".
 *
 * The followings are the available columns in table 'ivy_admissions_ds_temporary':
 * @property integer $id
 * @property integer $section_id
 * @property string $en_name
 * @property string $cn_name
 * @property string $school_id
 * @property string $gender
 * @property integer $birthday
 * @property integer $nationality
 * @property string $passportid
 * @property integer $valid_date
 * @property integer $resident_date
 * @property integer $native_lang
 * @property integer $other_lang
 * @property integer $start_date
 * @property string $start_grade
 * @property integer $has_xueji
 * @property integer $is_staff
 * @property integer $require_bus
 * @property string $home_address
 * @property string $school_history
 * @property string $sibling_info
 * @property string $father_name
 * @property string $father_nation
 * @property string $father_lang
 * @property string $father_home_lang
 * @property string $father_employer
 * @property string $father_position
 * @property string $father_education
 * @property string $father_phone
 * @property string $father_email
 * @property string $father_residence
 * @property string $mother_name
 * @property string $mother_nation
 * @property string $mother_lang
 * @property string $mother_home_lang
 * @property string $mother_employer
 * @property string $mother_position
 * @property string $mother_education
 * @property string $mother_phone
 * @property string $mother_email
 * @property string $mother_residence
 * @property string $emergency_contact_name
 * @property string $emergency_contact_phone
 * @property string $emergency_contact_email
 * @property string $learn_diffculties
 * @property string $learn_support
 * @property string $special_needs
 * @property string $psychological_evaluation
 * @property string $been_expelled
 * @property string $medical_conditions
 * @property string $physical_disabilities
 * @property string $academic_report
 * @property string $persion_copy
 * @property string $child_avatar
 * @property string $recommendation_form
 * @property integer $add_timestamp
 * @property string $talent
 * @property integer $status
 * @property string $remark
 * @property string $en_name_last
 * @property string $cn_name_last
 * @property string $pinyin_first
 * @property string $pinyin_last
 * @property string $start_year
 * @property string $en_name_middle
 * @property string $account_location
 * @property string $school_roll
 * @property string $is_visit
 */
class AdmissionsDsTemporary extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_admissions_ds_temporary';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('section_id, birthday, nationality, valid_date, resident_date, native_lang, other_lang, start_date, has_xueji, is_staff, require_bus, add_timestamp, status', 'numerical', 'integerOnly'=>true),
			array('en_name, cn_name, school_id, gender, passportid, start_grade, father_name, father_nation, father_lang, father_home_lang, father_employer, father_position, father_education, father_phone, father_email, father_residence, mother_name, mother_nation, mother_lang, mother_home_lang, mother_employer, mother_position, mother_education, mother_phone, mother_email, mother_residence, emergency_contact_name, emergency_contact_phone, emergency_contact_email, recommendation_form, remark, en_name_last, cn_name_last, pinyin_first, pinyin_last, start_year, en_name_middle, account_location, is_visit', 'length', 'max'=>255),
			array('home_address, school_history, sibling_info, learn_diffculties, learn_support, special_needs, psychological_evaluation, been_expelled, medical_conditions, physical_disabilities, academic_report, persion_copy, child_avatar, talent, school_roll', 'safe'),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, section_id, en_name, cn_name, school_id, gender, birthday, nationality, passportid, valid_date, resident_date, native_lang, other_lang, start_date, start_grade, has_xueji, is_staff, require_bus, home_address, school_history, sibling_info, father_name, father_nation, father_lang, father_home_lang, father_employer, father_position, father_education, father_phone, father_email, father_residence, mother_name, mother_nation, mother_lang, mother_home_lang, mother_employer, mother_position, mother_education, mother_phone, mother_email, mother_residence, emergency_contact_name, emergency_contact_phone, emergency_contact_email, learn_diffculties, learn_support, special_needs, psychological_evaluation, been_expelled, medical_conditions, physical_disabilities, academic_report, persion_copy, child_avatar, recommendation_form, add_timestamp, talent, status, remark, en_name_last, cn_name_last, pinyin_first, pinyin_last, start_year, en_name_middle, account_location, school_roll, is_visit', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'section_id' => 'Section',
			'en_name' => 'En Name',
			'cn_name' => 'Cn Name',
			'school_id' => 'School',
			'gender' => 'Gender',
			'birthday' => 'Birthday',
			'nationality' => 'Nationality',
			'passportid' => 'Passportid',
			'valid_date' => 'Valid Date',
			'resident_date' => 'Resident Date',
			'native_lang' => 'Native Lang',
			'other_lang' => 'Other Lang',
			'start_date' => 'Start Date',
			'start_grade' => 'Start Grade',
			'has_xueji' => 'Has Xueji',
			'is_staff' => 'Is Staff',
			'require_bus' => 'Require Bus',
			'home_address' => 'Home Address',
			'school_history' => 'School History',
			'sibling_info' => 'Sibling Info',
			'father_name' => 'Father Name',
			'father_nation' => 'Father Nation',
			'father_lang' => 'Father Lang',
			'father_home_lang' => 'Father Home Lang',
			'father_employer' => 'Father Employer',
			'father_position' => 'Father Position',
			'father_education' => 'Father Education',
			'father_phone' => 'Father Phone',
			'father_email' => 'Father Email',
			'father_residence' => 'Father Residence',
			'mother_name' => 'Mother Name',
			'mother_nation' => 'Mother Nation',
			'mother_lang' => 'Mother Lang',
			'mother_home_lang' => 'Mother Home Lang',
			'mother_employer' => 'Mother Employer',
			'mother_position' => 'Mother Position',
			'mother_education' => 'Mother Education',
			'mother_phone' => 'Mother Phone',
			'mother_email' => 'Mother Email',
			'mother_residence' => 'Mother Residence',
			'emergency_contact_name' => 'Emergency Contact Name',
			'emergency_contact_phone' => 'Emergency Contact Phone',
			'emergency_contact_email' => 'Emergency Contact Email',
			'learn_diffculties' => 'Learn Diffculties',
			'learn_support' => 'Learn Support',
			'special_needs' => 'Special Needs',
			'psychological_evaluation' => 'Psychological Evaluation',
			'been_expelled' => 'Been Expelled',
			'medical_conditions' => 'Medical Conditions',
			'physical_disabilities' => 'Physical Disabilities',
			'academic_report' => 'Academic Report',
			'persion_copy' => 'Persion Copy',
			'child_avatar' => 'Child Avatar',
			'recommendation_form' => 'Recommendation Form',
			'add_timestamp' => 'Add Timestamp',
			'talent' => 'Talent',
			'status' => 'Status',
			'remark' => 'Remark',
			'en_name_last' => 'En Name Last',
			'cn_name_last' => 'Cn Name Last',
			'pinyin_first' => 'Pinyin First',
			'pinyin_last' => 'Pinyin Last',
			'start_year' => 'Start Year',
			'en_name_middle' => 'En Name Middle',
			'account_location' => 'Account Location',
			'school_roll' => 'School Roll',
			'is_visit' => 'Is Visit',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('section_id',$this->section_id);
		$criteria->compare('en_name',$this->en_name,true);
		$criteria->compare('cn_name',$this->cn_name,true);
		$criteria->compare('school_id',$this->school_id,true);
		$criteria->compare('gender',$this->gender,true);
		$criteria->compare('birthday',$this->birthday);
		$criteria->compare('nationality',$this->nationality);
		$criteria->compare('passportid',$this->passportid,true);
		$criteria->compare('valid_date',$this->valid_date);
		$criteria->compare('resident_date',$this->resident_date);
		$criteria->compare('native_lang',$this->native_lang);
		$criteria->compare('other_lang',$this->other_lang);
		$criteria->compare('start_date',$this->start_date);
		$criteria->compare('start_grade',$this->start_grade,true);
		$criteria->compare('has_xueji',$this->has_xueji);
		$criteria->compare('is_staff',$this->is_staff);
		$criteria->compare('require_bus',$this->require_bus);
		$criteria->compare('home_address',$this->home_address,true);
		$criteria->compare('school_history',$this->school_history,true);
		$criteria->compare('sibling_info',$this->sibling_info,true);
		$criteria->compare('father_name',$this->father_name,true);
		$criteria->compare('father_nation',$this->father_nation,true);
		$criteria->compare('father_lang',$this->father_lang,true);
		$criteria->compare('father_home_lang',$this->father_home_lang,true);
		$criteria->compare('father_employer',$this->father_employer,true);
		$criteria->compare('father_position',$this->father_position,true);
		$criteria->compare('father_education',$this->father_education,true);
		$criteria->compare('father_phone',$this->father_phone,true);
		$criteria->compare('father_email',$this->father_email,true);
		$criteria->compare('father_residence',$this->father_residence,true);
		$criteria->compare('mother_name',$this->mother_name,true);
		$criteria->compare('mother_nation',$this->mother_nation,true);
		$criteria->compare('mother_lang',$this->mother_lang,true);
		$criteria->compare('mother_home_lang',$this->mother_home_lang,true);
		$criteria->compare('mother_employer',$this->mother_employer,true);
		$criteria->compare('mother_position',$this->mother_position,true);
		$criteria->compare('mother_education',$this->mother_education,true);
		$criteria->compare('mother_phone',$this->mother_phone,true);
		$criteria->compare('mother_email',$this->mother_email,true);
		$criteria->compare('mother_residence',$this->mother_residence,true);
		$criteria->compare('emergency_contact_name',$this->emergency_contact_name,true);
		$criteria->compare('emergency_contact_phone',$this->emergency_contact_phone,true);
		$criteria->compare('emergency_contact_email',$this->emergency_contact_email,true);
		$criteria->compare('learn_diffculties',$this->learn_diffculties,true);
		$criteria->compare('learn_support',$this->learn_support,true);
		$criteria->compare('special_needs',$this->special_needs,true);
		$criteria->compare('psychological_evaluation',$this->psychological_evaluation,true);
		$criteria->compare('been_expelled',$this->been_expelled,true);
		$criteria->compare('medical_conditions',$this->medical_conditions,true);
		$criteria->compare('physical_disabilities',$this->physical_disabilities,true);
		$criteria->compare('academic_report',$this->academic_report,true);
		$criteria->compare('persion_copy',$this->persion_copy,true);
		$criteria->compare('child_avatar',$this->child_avatar,true);
		$criteria->compare('recommendation_form',$this->recommendation_form,true);
		$criteria->compare('add_timestamp',$this->add_timestamp);
		$criteria->compare('talent',$this->talent,true);
		$criteria->compare('status',$this->status);
		$criteria->compare('remark',$this->remark,true);
		$criteria->compare('en_name_last',$this->en_name_last,true);
		$criteria->compare('cn_name_last',$this->cn_name_last,true);
		$criteria->compare('pinyin_first',$this->pinyin_first,true);
		$criteria->compare('pinyin_last',$this->pinyin_last,true);
		$criteria->compare('start_year',$this->start_year,true);
		$criteria->compare('en_name_middle',$this->en_name_middle,true);
		$criteria->compare('account_location',$this->account_location,true);
		$criteria->compare('school_roll',$this->school_roll,true);
		$criteria->compare('is_visit',$this->is_visit,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return AdmissionsDsTemporary the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

    public function getCatestates($cate)
    {
        $cfg = AdmissionsDs::getConfig();
        return $cfg[$cate][$this->status];
    }

    public function getCatename($cate)
    {
        $cfg = AdmissionsDs::getConfig();
        return $cfg[$cate][$this->start_grade];
    }

    public function getName()
    {
        switch (Yii::app()->language) {
            case "zh_cn":
                return ($this->cn_name) ? $this->cn_name . $this->cn_name_last : $this->en_name . " " . $this->en_name_middle  . " " . $this->en_name_last ;
                break;
            case "en_us":
                return ($this->en_name) ? $this->en_name . " " . $this->en_name_middle  . " " . $this->en_name_last : $this->pinyin_first . $this->pinyin_last;
                break;
        }
    }
}
