@echo off
echo ====== DOWNLOADING PHOTOS =======
echo *********************************
ren "%cd%\lib\*.list" "data.list"
for /f "tokens=1-4 delims=\" %%A in (lib\data.list) do call :processImg0 %%A %%B %%C %%D
echo *********************************

echo ====== DOWNLOADING COMPLETED =======
start %~sdp0\photos
pause
goto:eof

:processImg0
echo downloading %4 ...
set rootFolder=photos
set relFolder=photos\%1\%2
if not exist "%~dp0%relFolder%" md "%~dp0%relFolder%"
if not exist "%~dp0%relFolder%\%3.jpg" "%cd%\lib\wget" -q -N %4 -O "%~dp0%relFolder%\%3.jpg"
goto:eof