<?php
class AuthController extends Controller{
    
    public function init(){
        parent::init();
        Yii::app()->theme = Yii::app()->params['siteFlag'] != 'ivygroup' ? Yii::app()->params['siteFlag'] : 'blue';
    }

    public function actionLogin(){
        if (Yii::app()->user->isGuest) {
            $model = new UserLogin;
            // collect user input data
            if (isset($_POST['UserLogin'])) {
                $model->attributes = $_POST['UserLogin'];
                if(isset( $_POST['UserLogin']['lang']) ){
                    $lang = $_POST['UserLogin']['lang'];
                    $lang = Mims::setLangCookie($lang);
                    Yii::app()->language = $lang;
                }
                // validate user input and redirect to previous page if valid
                if ($model->validate()) {
                    $this->lastLogin();
                    $this->redirect(Yii::app()->user->returnUrl);
                }
            }
//            $cs = Yii::app()->clientScript;
//            $cs->registerCssFile(Yii::app()->theme->baseUrl.'/css/auth.css');
//        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/TweenLite.min.js');

            $this->render('login', array('model' => $model ));
            Yii::app()->end();
        }

        $this->redirect(Yii::app()->defaultController);
    }

    private function lastLogin() {
        $lastLogin = User::model()->findByPk(Yii::app()->user->id);
        $lastLogin->last_login = time();
        $lastLogin->save();
    }

    public function actionLogout(){
        Yii::app()->user->logout();
        $this->redirect(Yii::app()->user->loginUrl);
    }
	
	public function actionLostpass(){
		$model = new UserLostpass;
		if (isset($_POST['UserLostpass'])) {
			$model->attributes = $_POST['UserLostpass'];
			if($model->validate()){
				$criter = new CDbCriteria();
				$criter->compare('level','>0');
				$criter->compare('email',$model->username);
				$item = User::model()->find($criter);
				if($item != null){
					$authcode = substr($item->pass, 3, 6);
					$emailUrl = $this->createUrl('repass', array("activkey" => $authcode, "email" => $item->email));
					$mailer = Yii::createComponent('common.extensions.mailer.EMailer');
                    
                    $layout = 'main';
                    $mailer->Subject = sprintf('【Ivygroup%s】', OA::isProduction() ? '' : ' 测试' );
                    if (Yii::app()->params['siteFlag'] == 'daystar') {
                        $layout = 'todsparent';
    					$mailer->Subject = sprintf('【Daystar%s】', OA::isProduction() ? '' : ' 测试' );
                    }
                    $mailer->Subject .= '找回密码  Recover your password';
					$mailer->AddAddress($model->username);
					$mailer->getView('daystarlostpass', array('username' => $item->getName(), 'emailUrl' => $emailUrl), $layout);
					$mailer->iniMail( OA::isProduction() ); // 此行代码要放到AddAddress, AddCC方法下面
                    if ($mailer->Send()) {
                        $this->addMessage('state', 'success');
						$this->addMessage('message', Yii::t('message', 'Verification sent successfully!'));
                    } else {
						$this->addMessage('state', 'fail');
						$this->addMessage('message', Yii::t('message', 'Verification sent failed!'));
                    }
				}
				else{
					$this->addMessage('state', 'fail');
					$this->addMessage('message', Yii::t('message', 'Email does not exist or expired.'));
				}
			}
			else{
				$this->addMessage('state', 'fail');
                $err = current($model->getErrors());
                $this->addMessage('message', $err[0]);
			}
			$this->showMessage();
		}
		$this->render('lostpass', array('model' => $model ));
	
	}
	public function actionRepass(){
        $email = Yii::app()->request->getParam('email', '');
        $activkey = Yii::app()->request->getParam('activkey', '');
        $model = new UserRepass;
        $validLink = false;

        $criter = new CDbCriteria();
        $criter->compare('email', $email);
        $criter->compare('level', '>0');
        $item = User::model()->find($criter);

        if(!empty($item) && substr($item->pass, 3, 6) == $activkey ){
            $validLink = true;
            if (isset($_POST['UserRepass'])) {
                $model->attributes = $_POST['UserRepass'];
                if($model->validate()){
                    $item->pass = md5($model->pass);
                    if($item->save()){
                        $this->addMessage('state', 'success');
                        $this->addMessage('message', Yii::t('message', 'Updated password successfully!'));
                        $this->addMessage('referer', Yii::app()->urlManager->baseUrl);
                        $this->addMessage('refresh', true);
                    }
                    else{
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', Yii::t('message', 'Updated password failed!'));
                    }
                }
                else{
                    $this->addMessage('state', 'fail');
                    $err = current($model->getErrors());
                    $this->addMessage('message', $err[0]);
                }
                $this->showMessage();
            }
        }
        $this->render('repass', array('model' => $model, 'email' => $email, 'validLink' => $validLink ));
	}
}

?>
