
<div  id='container' v-cloak>
    <div class=''>
<!--        <el-select v-model="startYear" size="small" placeholder="请选择" @change='calendar()'>-->
<!--            <el-option-->
<!--            v-for="(item,index) in yearList"-->
<!--            :key="item.key"-->
<!--            :label="item.title"-->
<!--            :value="item.key">-->
<!--            </el-option>-->
<!--        </el-select>-->
        <!-- <el-select v-model="schoolId" size="small" placeholder="请选择" @change='getCalendar()'>
            <el-option
            v-for="(item,index) in schoolList"
            :key="item.branchid"
            :label="item.title"
            :value="item.branchid">
            </el-option>
        </el-select> -->
    </div>
    <div class='row ' v-if='leaveData.list'>
        <div class='col-md-8 relative'>
            <!-- <div class='groupData'>
                <div class="btn-group" role="group" aria-label="...">
                    <button type="button" class="btn " :class='type=="qingjia"?"btn-primary":"btn-default"' @click='tabType("qingjia")'>显示请假</button>
                    <button type="button" class="btn btn-default" :class='type=="jiaban"?"btn-primary":"btn-default"'  @click='tabType("jiaban")'>显示加班</button>
                </div>
            </div> -->
            <div class='currentMon'>
                <button type="button" class="btn btn-default" @click='currentMon'><?php echo Yii::t('leave', 'Current month') ?></button>
            </div>
            <div class="text-center section">
                <v-date-picker
                ref='calendar'
                class="custom-calendar max-w-full"
                :masks="masks"
                :attributes="attributes"
                disable-page-swipe
                is-expanded
                v-model='clickDays' 
                @transition-end='PrevMonth' 
                :first-day-of-week="2" :masks="{ title: 'MMM YYYY' }"
                >
                <template v-slot:day-content="{ day, attributes, dayEvents }">
                    <el-popover
                        placement="top"
                        width="420"
                        trigger="click"
                        :ref="`popover-${day.id}`">
                        <div v-if='modalList.day'>
                            <div class='mb12 mt5'><span class='color3 font14 font600'>{{modalList.day.id}}</span><span class='font12 color6 ml10'>{{translate("<?php echo Yii::t('leave', '%s item(s)'); ?>", modalList.attributes.length)}}</span></div>
                            <div style='max-height:450px; overflow-y: auto;padding-right:12px' class='scroll-box'>
                                <div class='flex align-items' :class='index==0?"":"mt12"' v-for='(list,index) in modalList.attributes'>
                                    <img class="img42" :src="calendarData.user_info[list.customData.leave_staff].photoUrl" />
                                    <div class='flex1 flexText ml8'>
                                        <div class='color3 font14'>{{ calendarData.user_info[list.customData.leave_staff].name }}</div>
                                        <div class='color6 font12'>{{ calendarData.user_info[list.customData.leave_staff].hrPosition }}</div>
                                    </div>
                                    <div>
                                        <el-tooltip class="item" effect="dark" placement="top" v-if='list.customData.dateEnd!=null'>
                                            <div slot="content">{{formatDate(list.customData.date)}} - {{formatDate(list.customData.dateEnd)}}</div>
                                            <div>
                                                <el-tag size="mini" type='success' v-if='list.customData.type=="waichu"'>{{list.customData.type_title}}</el-tag>
                                                <el-tag size="mini" v-else>{{list.customData.type_title}}</el-tag>
                                                <span class='color6 ml5'>{{list.customData.duration_format}} <span v-if='list.customData.duration_start!=null && list.customData.duration_start!=""'>（<?php echo Yii::t('leave', 'From ') ?>{{list.customData.duration_start}}）</span></span>
                                            </div>
                                        </el-tooltip>
                                        <div v-else>
                                            <el-tag size="mini" type='success' v-if='list.customData.type=="waichu"'>{{list.customData.type_title}}</el-tag>
                                            <el-tag size="mini" v-else>{{list.customData.type_title}}</el-tag>
                                            <span class='color6 ml5'>{{list.customData.duration_format}} <span v-if='list.customData.duration_start!=null && list.customData.duration_start!=""'>（<?php echo Yii::t('leave', 'From ') ?>{{list.customData.duration_start}}）</span></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div slot="reference" class="overflow-hidden" @click="showHoliday(day,attributes)"  >
                            <div > 
                                <div class="color3 font14 mb8 font600">{{ day.day }}<el-tag size="mini" class='ml10' v-if='day.isToday'><?php echo Yii::t('leave', 'Today') ?></el-tag></div>
                                <div class="flex-grow overflow-y-auto overflow-x-auto" v-if='attributes'>
                                    <div
                                        v-for="attr in attributes.slice(0,3)"
                                        :key="attr.key"
                                        class="mb4 align-items"
                                    >
                                        <div class='flex' v-if='calendarData.user_info[attr.customData.leave_staff]'>
                                            <img class="calendarImg" :src="calendarData.user_info[attr.customData.leave_staff].photoUrl" />
                                            <div class='flex1 word-break ml8 color3 font12'>{{ calendarData.user_info[attr.customData.leave_staff].name }}</div>
                                        </div>
                                    </div>
                                    <div v-if='attributes.length>3' class='text-primary text-center font12 mt8'>
                                        <span>+ {{attributes.length-3}}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </el-popover>  
                </template>
                </v-date-picker>
            </div>
        </div>
        <div class='col-md-4'>
            <div class='borderList' v-if='leaveData.list'> 
                <div class='color3 font16 font600'><span class='el-icon-date mr8 iconCalendar'></span><span>{{dayTitle}}</span></div> 
                <div v-if='leaveData.list.length==0' class='text-center font14 color3 mt24'><?php echo Yii::t('leave', 'No absentees today') ?></div>
                <div v-else>
                    <div v-if='leaveData.list.qingjia'>
                        <div class='font14 color3 mt20 font600'><?php echo Yii::t('leave', 'Staff on leave /Out of office today') ?></div>   
                        <div>
                            <div class='mt12' v-for='(list,index) in qingjiaToday'>
                                <div class='flex align-items '>
                                    <img class="img42" :src="leaveData.user_info[list.leave_staff].photoUrl" />
                                    <div class='flex1 flexText ml8'>
                                        <div class='color3 font14'>{{ leaveData.user_info[list.leave_staff].name }}</div>
                                        <div class='color6 font12'>{{ leaveData.user_info[list.leave_staff].hrPosition }}</div>
                                    </div>
                                    <div  v-if='clientWidth<990 || clientWidth>1200'>
                                        <el-tooltip class="item" effect="dark" placement="top" v-if='list.date_end!=null'>
                                            <div slot="content">{{formatDate(list.date)}} - {{formatDate(list.date_end)}}</div>
                                            <div>
                                            <el-tag size="mini" type='success' v-if='list.type=="waichu"'>{{list.type_title}}</el-tag>
                                            <el-tag size="mini" v-else>{{list.type_title}}</el-tag>
                                                <span class='color6 ml5'>{{list.duration_format}} <span v-if='list.duration_start!=null && list.duration_start!=""'>（<?php echo Yii::t('leave', 'From ') ?>{{list.duration_start}}）</span></span>
                                            </div>
                                        </el-tooltip>
                                        <div v-else>
                                            <el-tag size="mini" type='success' v-if='list.type=="waichu"'>{{list.type_title}}</el-tag>
                                            <el-tag size="mini" v-else>{{list.type_title}}</el-tag>
                                            <span class='color6 ml5'>{{list.duration_format}} <span v-if='list.duration_start!=null && list.duration_start!=""'>（<?php echo Yii::t('leave', 'From ') ?>{{list.duration_start}}）</span></span>
                                        </div>
                                    </div>
                                </div>
                                <div class='ml50 mt5'  v-if='clientWidth>990 && clientWidth<1200'>
                                    <el-tooltip class="item" effect="dark" placement="top" v-if='list.date_end!=null'>
                                        <div slot="content">{{formatDate(list.date)}} - {{formatDate(list.date_end)}}</div>
                                        <div>
                                            <el-tag size="mini" type='success' v-if='list.type=="waichu"'>{{list.type_title}}</el-tag>
                                            <el-tag size="mini" v-else>{{list.type_title}}</el-tag>
                                            <span class='color6 ml5'>{{list.duration_format}} <span v-if='list.duration_start!=null && list.duration_start!=""'>（<?php echo Yii::t('leave', 'From ') ?>{{list.duration_start}}）</span></span>
                                        </div>
                                    </el-tooltip>
                                    <div v-else>
                                        <el-tag size="mini" type='success' v-if='list.type=="waichu"'>{{list.type_title}}</el-tag>
                                        <el-tag size="mini" v-else>{{list.type_title}}</el-tag>
                                        <span class='color6 ml5'>{{list.duration_format}} <span v-if='list.duration_start!=null && list.duration_start!=""'>（<?php echo Yii::t('leave', 'From ') ?>{{list.duration_start}}）</span></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div> 
                    <div v-if='leaveData.list.jiaban'>
                        <div class='font14 color3 mt20 font600'><?php echo Yii::t('leave', 'Staff working overtime today') ?></div>   
                        <div>
                            <div class='flex align-items mt12' v-for='(list,index) in leaveData.list.jiaban'>
                                <img class="img42" :src="leaveData.user_info[list.leave_staff].photoUrl" />
                                <div class='flex1 word-break ml8'>
                                    <div class='color3 font14'>{{ leaveData.user_info[list.leave_staff].name }}</div>
                                    <div class='color6 font12'>{{ leaveData.user_info[list.leave_staff].hrPosition }}</div>
                                </div>
                                <div>
                                    <el-tag size="mini">{{list.type_title}}</el-tag>
                                    <span class='color6 ml5'>{{list.duration_format}} <span v-if='list.duration_start!=null && list.duration_start!=""'>（<?php echo Yii::t('leave', 'From ') ?>{{list.duration_start}}）</span></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
const year = new Date().getFullYear();
const month = new Date().getMonth()+1;
const day = new Date().getDate();
var yearData = <?php echo CJSON::encode($yearList) ?>;
var yearList = yearData.list.map((val,index,arr)=>{
	let json = {}
	json.title= val.value
	json.key = val.key+''
	return json
})
var container=new Vue({
    el: "#container",
    data: {
        masks: {
            // weekdays: 'WWW',
        },
        clientWidth:document.body.clientWidth,
        attributes: [],
        clickDays:null,
        type:'qingjia',
        currentDays:'',
        currentMonth:month,
        currentYear:year,
        dayTitle:'',
        schoolList:[],
        schoolId:'',
        yearList:yearList,
        startYear:yearData.current+'',
        calendarData:{},
        leaveData:{},
        modalList:{},
        visible: false,
        qingjiaToday:[]
    },
    created: function() {
        let mon=month
        if(mon<10){
            mon='0'+mon
        }else{
            mon=mon+''   
        }
        let days=day
        if(days<10){
            days='0'+days
        }else{
            days=days+''   
        }
        this.currentDays=year+mon+days
        this.dayTitle=year+'-'+mon+'-'+days
        this.todayLeave()
        this.calendar()
    },
    methods: {
        translate(text, ...args) {
            return text.replace(/%s/g, () => args.shift());
        },
        showHoliday(day,attributes){
            if(attributes.length!=0){
                this.modalList={
                    day:day,
                    attributes:attributes
                }
                this.$refs['popover-'+day.id].doClose();
            }else{
                this.$refs['popover-'+day.id].doShow();
            }     
        },
        formatDate(dateString){
            const date=dateString+''
            const year = date.slice(0, 4);
            const month = date.slice(4, 6);
            const day = date.slice(6, 8);
            return year+'.'+month+'.'+ day
        },
        currentMon(){
            const calendar = this.$refs.calendar
            calendar.move({ month: month, year:year })
        },
        PrevMonth(day){
            let month=this.$refs.calendar.$children[0].pages[0].month
            let year=this.$refs.calendar.$children[0].pages[0].year
            if(month<10){
                month='0'+month
            }else{
                month=month+''   
            }
            this.currentYear=year
            this.currentMonth=month
            this.calendar()
        },
        sortTea(list){
            list.sort((x,y)=>{
                return x['user_name'].localeCompare(y['user_name'])
            })
            return list
        },
        calendar(){
            let that=this
            this.modalList={}
            $.ajax({
                url: '<?php echo $this->createUrl("getCalendar") ?>',
                type: "post",
                dataType: 'json',
                data: {
                    year:this.currentYear,
                    month:this.currentMonth,
                },
                success: function(data) {
                    if (data.state == 'success') {
                        that.attributes=[]
                        that.calendarData=data.data
                        if(data.data.list.qingjia){
                            data.data.list.qingjia=that.sortTea(data.data.list.qingjia)                        
                            data.data.list.qingjia.forEach((item,index) => {
                                let date = new Date(item.date_timestamp);
                                var y = date.getFullYear();
                                var m = date.getMonth();
                                var d = date.getDate();   
                                that.attributes.push({
                                    key:index,
                                    dates: new Date(y, parseInt(m), parseInt(d)),
                                    customData:{
                                        date_type:item.date_type,
                                        leave_staff :item.leave_staff,
                                        duration_format:item.duration_format,
                                        duration_start:item.duration_start,
                                        type_title:item.type_title,
                                        dateEnd:item.date_end,
                                        date:item.date,
                                        type:item.type
                                    }
                                }) 
                            });
                        }
                    }else{
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    }
                },
                error: function(data) {
                    resultTip({
                        error: 'warning',
                        msg: data.message
                    });
                },
            })
        },
        todayLeave(){
            let that=this
            $.ajax({
                url: '<?php echo $this->createUrl("getTodayLeave") ?>',
                type: "post",
                dataType: 'json',
                data: {
                    date:this.currentDays,
                },
                success: function(data) {
                    if (data.state == 'success') {
                        if(data.data.list.qingjia){
                            that.qingjiaToday=that.sortTea(data.data.list.qingjia)            
                        }
                        that.leaveData=data.data
                    }else{
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    }
                },
                error: function(data) {
                    resultTip({
                        error: 'warning',
                        msg: data.message
                    });
                },
            })
        }
    }
})
   
</script>

<style>
    .scroll-box::-webkit-scrollbar-thumb {
        /*滚动条里面小方块*/
        border-radius   : 10px;
        background-color: #ccc;
        background-image: none
    }
    .scroll-box::-webkit-scrollbar-track {
        /*滚动条里面轨道*/
        box-shadow   : inset 0 0 5px rgba(0, 0, 0, 0);
        background   : #fff;
        border-radius: 10px;
        border:none
    }
    .vc-container {
        border:none !important;
    }
    .vc-header {
        background-color: #fff;
        padding: 18px !important;
    }
    .vc-arrows-container{
        width:200px;
        left:50%;
        margin-left:-100px;
        top:8px
    }
    .vc-weeks {
        padding: 0 !important;
        border: 1px solid #E8EAED;
    }
    
   .vc-weekday {
        border-bottom:1px solid #E8EAED;
        border-right: 1px solid #E8EAED;
        padding:0 !important;
        color: #333333 !important;
        height:50px;
        line-height:50px !important
    }
    .vc-weekday:last-child{
        border-right:none !important
    }
   .vc-day {
        padding:10px;
        text-align: left;
        height:150px;
        /* width:150px; */
        background-color: white; 
    }
    .vc-day:not(.on-bottom) {
        border-bottom:1px solid #E8EAED;
    }
    .vc-day:not(.on-right) {
        border-right:1px solid #E8EAED;
    }
    .vc-day-dots {
        margin-bottom: 5px;
    }
    .word-break{
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        width: 0;
    }
    .flexText{
        width: 0;  
    }
    .flexText div{
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    .groupData{
        position: absolute;
        top: 18px;
        z-index: 99;
    }
    .currentMon{
        position: absolute;
        top: 18px;
        z-index: 99;
        right:18px
    }
    .borderList{
        border: 1px solid #E8EAED;
        padding:20px;
        margin-top:18px;
        border-radius: 6px;
    }
    .overflow-hidden{
        width:100%;
        height:100%;
        cursor: pointer;
    }
    .calendarImg{
        width:20px;
        height:20px;
        object-fit: cover;
        border-radius:50%
    }
    .font600{
        font-weight:600
    }
    .vc-container.vc-is-expanded{
        width:100%
    }
    .iconCalendar{
        background: #4D88D2;
        color: #fff;
        display: inline-block;
        width: 22px;
        height: 22px;
        border-radius: 50%;
        text-align: center;
        line-height: 22px;
        font-size: 12px;
    }
    .ml50{
        margin-left:50px
    }
</style>