<div class="col-md-11 col-sm-10">
    <div id="school-bus-list">
        <div class="well"><?php echo Yii::t('message', 'Student List (with school-bus invoice issued)');?></div>
        <?php echo CHtml::form($this->createUrl('/mcampus/schoolbus/assignChildForSchoolbus'),'post',
           array('class'=>'J_ajaxForm form-inline','id'=>'unassigned-child'));?>
        <ul class="nav nav-tabs nav-justified" role="tablist">
            <li role="presentation" id="notassign"><a href="javascript:setChildNotAssign();"><?php echo Yii::t('message','Not assigned to schoolbus');?></a></li>
            <li role="presentation" id="hasbug"><a href="javascript:setAllChild();"><?php echo Yii::t('message','Assigned to schoolbus');?></a></li>
        </ul>
        <div class="panel panel-default" style="border-top: none;border-radius: 0 0 4px 4px;">

            <div class="panel-body">
                <div class="unassigned-child-list">

                </div>
            </div>
            <div class="panel-footer">
                <?php
                echo CHtml::hiddenField('SchoolBusChild[startYear]', $startYear);
                echo CHtml::dropDownList('SchoolBusChild[busId]',null,array(),
                    array('disabled'=>'disabled','empty'=> Yii::t('message','Assign to School Bus'), 'class'=>'form-control mb5'));
                ?>
                <button type="button" class="btn btn-default J_ajax_submit_btn mb5">
                    <?php echo Yii::t('global','Submit');?>
                </button>
            </div>
        </div>
        <?php echo CHtml::endForm();?>


        <?php
        foreach($schoolbusList as $_key => $schoolbus):
            $_title = $schoolbus['bus_title'] . ' - ' . $schoolbus['bus_code'];
        ?>
            <div class="panel panel-default" id='schoolbus-list-item-<?php echo $schoolbus['bid'];?>'>
                <div class="panel-heading">
                    <span class="dropdown">
                        <a class-dropdown=flag data-toggle=dropdown href="#"><span class="glyphicon glyphicon-list"></span></a>
                        <ul class="dropdown-menu">
                            <li><a href="<?php echo $this->createUrl('/mcampus/schoolbus/exportChildBySchoolbus',
                                    array('startYear'=>$startYear,'busId'=>$schoolbus['bid']));?>">
                                    <span class="glyphicon glyphicon-floppy-save"></span> <?php echo Yii::t('campus','Export Name list');?></a>
                            </li>
                            <li><a onclick="createSchoolbus(<?php echo $schoolbus['bid'];?>)" href="javascript:void(0)">
                                    <span class="glyphicon glyphicon-pencil"></span> <?php echo Yii::t('campus','Edit School Bus');?></a>
                            </li>
                            <li class="divider"></li>
                            <li><a href="<?php echo $this->createUrl('/mcampus/schoolbus/deleteSchoolbus',array('busId'=>$schoolbus['bid']));?>"
                                   class="J_ajax_del" title="<?php echo Yii::t('global','Delete');?>"
                                   id="delete-schoolbus-<?php echo $schoolbus['bid'];?>">
                                    <span class="glyphicon glyphicon-remove"></span> <?php echo Yii::t('campus','Delete School Bus');?></a></li>
                        </ul>
                    </span>
                    <?php echo $_title  ; ?>
                </div>
                <div class="panel-body schoolbus-child-list table-responsive" style="overflow-y: hidden;overflow-x: auto;" busid="<?php echo $schoolbus['bid'];?>">
                    <?php echo CHtml::form($this->createUrl('/mcampus/schoolbus/updateChildBySchoolbus'),'post', array('class'=>'J_ajaxForm form-inline'));?>
                    <table class="table table-hover">
                        <thead>
                        <tr>
                            <th width="120"><?php echo Yii::t('labels','Name');?></th>
                            <th width="360"><?php echo Yii::t('campus','Pick up time/place');?></th>
                            <th width="460"><?php echo Yii::t('campus','Drop off time/place');?></th>
                            <th><?php echo Yii::t('invoice','Memo');?></th>
                            <th width="60"><?php echo Yii::t('global','Action');?></th>
                        </tr>
                        </thead>
                        <tbody>

                        </tbody>
                    </table>
                        <button type="button"
                                class="btn btn-primary J_ajax_submit_btn"
                                id="schoolbus-save-btn-<?php echo $schoolbus['bid'];?>">
                            <?php echo Yii::t('global','Save');?>
                        </button>
                    <?php echo CHtml::endForm();?>
                </div>
            </div>

        <?php
        endforeach;
        ?>
    </div>
</div>

<script type="text/template" id="child-item-template">
    <% if (!_.isUndefined(childInfo[childid])){ %>
    <tr dbid=<%= id %> childid=<%= childid %>>
        <th>
            <a href="<?php echo $this->createUrl('//child/index/index');?>&childid=<%=childid%>" target="_blank"><h4><%= childInfo[childid].childName %></h4></a>
            <input type="hidden" value="<%= id %>" name="SchoolBusChild[id][]">
            <input type="hidden" value="<%= childid %>" name="SchoolBusChild[childid][]">
        </th>
        <td class="form-inline">
            <input type="text" class="form-control mr5 mb5" style="width: 60px" value="<%= time1 %>" name="SchoolBusChild[time1][]">
            <input type="text" class="form-control" style="width: 270px" value="<%= addr1 %>" name="SchoolBusChild[addr1][]"></td>
        <td class="form-inline">
            <input type="text" class="form-control mr5 mb5" style="width: 100px" value="<%= time2 %>" name="SchoolBusChild[time2][]">
            <input type="text" class="form-control" style="width: 335px" value="<%= addr2 %>" name="SchoolBusChild[addr2][]"></td>
        <td>
            <input type="text" class="form-control mr5 mb5 length_2" value="<%= remark %>" name="SchoolBusChild[remark][]"></td>
        <td>
            <a class="J_ajax_del btn btn-danger btn-xs"
               title="<?php echo Yii::t('global','Delete')?>"
               href="<?php echo $this->createUrl('/mcampus/schoolbus/deteteChildBySchoolbus');?>&dbId=<%=id%>&childId=<%=childid%>">
            <i class="glyphicon glyphicon-remove"></i></a></td>
    </tr>
    <%}%>
</script>

<script>
//校车信息
var schoolbusList = <?php echo CJSON::encode($schoolbusList);?>;
//已购买校车服务的孩子（有校车帐单已付，未付，部分付）
var childInfo = <?php echo CJSON::encode($childInfo);?>;
//未分配的孩子
var childNotAssign = _.clone(childInfo);
// 已分配
var hasBus={};
//已被分配校车的孩子
var childBySchoolbusList = <?php echo CJSON::encode($childBySchoolbusList)?>;
var childTpl = _.template($('#child-item-template').html());
$(function(){
    //显示所有校车及乘坐校车的孩子信息
    setChildBySchoolbusList = function(childBySchoolbusList){
        if (!_.isEmpty(childBySchoolbusList)){
            $.each(childBySchoolbusList, function(i,items){
                var _container = $('div.schoolbus-child-list[busid|='+i+'] table tbody');
                $.each(items, function(m, item){
                    _container.append(childTpl(item));
                    hasBus[item.childid] = childInfo[item.childid];
                    if (!_.isEmpty(childNotAssign)){
                        delete childNotAssign[item.childid];
                    }
                })
            })
        }
    }

    //显示单个校车及乘坐校车的孩子信息
    setChildBySingleSchoolbus = function(childBySchoolbus,busId){
        if (!_.isEmpty(childBySchoolbus)){
            var _container = $('div.schoolbus-child-list[busid|='+busId+'] table tbody');
            _container.empty();
            $.each(childBySchoolbus[busId], function(m, item){
                _container.append(childTpl(item));
                if (!_.isEmpty(childNotAssign)){
                    delete childNotAssign[item.childid];
                }
            })
        }
    }

    //设置未分配的孩子
    setChildNotAssign = function (){
        $('#hasbug').removeClass('active');
        $('#notassign').addClass('active');
        var _container = $('div.unassigned-child-list');
        _container.html('');
        if (!_.isEmpty(childNotAssign)){
            $.each(childNotAssign, function(i, item){
                var _unassignChildTpl = _.template('<div class="col-md-3"><div class="checkbox">' +
                    '<label><input type="checkbox" value="<%=childid%>" name="SchoolBusChild[childIds][]"> '+
                    '<% print(childInfo[childid].childName);%></label></div></div>');
                var _checkbox = _unassignChildTpl({childid:i,name:item.i});
                _container.append(_checkbox);
            })
        }else{
            _container.append();
        }
    }

    setAllChild = function (){
        $('#notassign').removeClass('active');
        $('#hasbug').addClass('active');
        if (!_.isEmpty(hasBus)){
            var _container = $('div.unassigned-child-list');
            _container.html('');
            $.each(hasBus, function(i, item){
                if(!_.isEmpty(item)) {
                    var _unassignChildTpl = _.template('<div class="col-md-3"><div class="checkbox">' +
                        '<label><input type="checkbox" value="<%=childid%>" name="SchoolBusChild[childIds][]"> ' +
                        '<% print(childInfo[childid].childName);%></label></div></div>');
                    var _checkbox = _unassignChildTpl({childid: i, name: item.i});
                    _container.append(_checkbox);
                }
            })
        }
    }

    //设置校车列表
    setSchoolbusList = function(schoolbusList){
        $.each(schoolbusList, function(i,item){
            $("<option />").attr("value", item['bid'])
                .text(item['bus_title'] + ' - ' + item['bus_code'])
                .appendTo("#SchoolBusChild_busId");
        })
    }

    //删除已分配的校车孩子的回调函数
    deleteCallback = function(data){
        $.each(childBySchoolbusList[data.busId], function(i, item){
            if (item.childid == data.childId){
                childBySchoolbusList[data.busId].splice(i,1);
                return false;
            }
        })
        childNotAssign[data.childId] = childInfo[data.childId];
        $("div.unassigned-child-list").empty();
        setChildNotAssign(childNotAssign);
        setChildBySingleSchoolbus(childBySchoolbusList,data.busId);
        showSaveBtn();
        head.Util.ajaxDel();
    }

    //分配孩子到校车回调函数
    addCallback = function(data){
        $.each(data.childInfo, function(m, item){
            item['addr1'] = childNotAssign[item.childid].compound;
            item['addr2'] = childNotAssign[item.childid].compound;
            if (_.isUndefined(childBySchoolbusList[data.busId])){
                childBySchoolbusList[data.busId] = [];
                childBySchoolbusList[data.busId][m] = item;
            }else{
                childBySchoolbusList[data.busId].unshift(item);
            }
            if (!_.isEmpty(childNotAssign)){
                delete childNotAssign[item.childid];
            }
        })
        console.log(childBySchoolbusList);
        $("div.unassigned-child-list").empty();
        setChildNotAssign(childNotAssign);
        setChildBySingleSchoolbus(childBySchoolbusList,data.busId);
        showSaveBtn();
        head.Util.ajaxDel();
    }

    //更新已分配校车孩子的信息回调函数
    updateCallback = function(data){
        delete childBySchoolbusList[data.busId];
        data.childInfo = _.sortBy(data.childInfo,'time1');
        childBySchoolbusList[data.busId] = data.childInfo;
        console.log(childBySchoolbusList);
        setChildBySingleSchoolbus(childBySchoolbusList,data.busId);
        head.Util.ajaxDel();
    }

    //删除校车的回调函数
    deleteSchoolbusCallback = function(data){
        $.each(schoolbusList, function(i,item){
            console.log(item);
            if (item.bid == data){
                schoolbusList.splice(i,1);
                return false;
            }
        })
        $("#SchoolBusChild_busId").empty();
        $("<option />").attr("value", '')
            .text('<?php echo Yii::t('message','Assign to School Bus');?>')
            .appendTo("#SchoolBusChild_busId");
        setSchoolbusList(schoolbusList);
        $("#schoolbus-list-item-"+data).remove();
    }

    //控制保存与删除按钮显示
    showSaveBtn = function(){
        $.each(schoolbusList, function(m, item){
            if (_.isEmpty(childBySchoolbusList) || _.isUndefined(childBySchoolbusList[item.bid])){
//                if (_.isEmpty(childBySchoolbusList)){
//                    $('#unassigned-child').hide();
//                }
                $("#schoolbus-save-btn-"+item.bid).hide();
                $("#delete-schoolbus-"+item.bid).parent().removeClass('disabled');
            }else{
                $('#unassigned-child').show();
                if (childBySchoolbusList[item.bid].length == 0){
                    $("#schoolbus-save-btn-"+item.bid).hide();
                    $("#delete-schoolbus-"+item.bid).parent().removeClass('disabled');
                }else{
                    $("#schoolbus-save-btn-"+item.bid).show();
                    $("#delete-schoolbus-"+item.bid).parent().addClass('disabled');
                }
            }
        })
    }

    //初始化页面
    $("#SchoolBusChild_busId").removeAttr('disabled');
    setChildBySchoolbusList(childBySchoolbusList);
    setChildNotAssign(childNotAssign);
    setSchoolbusList(schoolbusList);
    showSaveBtn();
    head.Util.ajaxDel();
})
</script>
