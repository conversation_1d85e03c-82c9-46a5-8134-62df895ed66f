<?php
$this->breadcrumbs=array(
	'Portfolio'=>array('/portfolio/portfolio/classes'),
	Yii::t("navigations",'Photo Gallery'),
);?>

<div id="left-col" class="no-bg span-6">
    <ul class="sub-nav" id="subnav">
        <?php foreach ($allcalendar as $startyear=>$cla):?>
        <li <?php if ($_startyear == $startyear):?>class="active"<?php endif;?>><a href="<?php echo Yii::app()->getController()->createUrl('//portfolio/portfolio/gallerylist', array('startyear'=>$startyear))?>"><?php echo IvyClass::formatSchoolYear($startyear)?> <?php echo Yii::t("portfolio", 'School Year')?></a><em></em>
            <?php if ($_startyear == $startyear):?>
            <ul class="sub-group">
                <?php foreach ($albums['weelyjournal'] as $albumidt=>$album):?>
                <li <?php if ($startyear && $type == 'journal' && $albumid==$albumidt):?>class="active"<?php endif;?>>
                    <?php echo CHtml::link(Yii::t("navigations", 'Weekly Journal').' '.$album, Yii::app()->getController()->createUrl('//portfolio/portfolio/gallerylist', array('startyear'=>$startyear,'albumid'=>$albumidt)))?>
                </li>
                <?php endforeach;?>
                <?php if (isset($sems[$startyear])):?>
                <li <?php if ($_startyear == $startyear && $type == 'semester'):?>class="active"<?php endif;?>>
                    <?php echo CHtml::link(Yii::t("navigations", 'Semester Report'), Yii::app()->getController()->createUrl('//portfolio/portfolio/gallerylist', array('startyear'=>$startyear, 'type'=>'semester')))?>
                </li>
                <?php endif;?>
            </ul>
            <?php endif;?>
        </li>
        <?php endforeach;?>
    </ul>
</div>

<div id="main-col" class="span-18 last">
    <?php
    if ($pics && $_startyear){
        $this->widget('application.extensions.gallery.GalleryList', array(
                'pics'=>$pics,
                'startyear'=>$_startyear,
                'nurl'=>$nurl,
                'albumid'=>$albumid,
            )
        );
    }
    else {
        echo Yii::t("portfolio","No photos found.");
//        if ($nurl){
//            echo "<script>setTimeout('window.location=\"".$nurl."\"', 3000)</script>";
//        }
    }
    ?>
</div>

<div class="clear"></div>

<?php
$this->widget('application.extensions.dit.Dit', array(
        'bId'=>'subnav',
        'cId'=>'mainbox',
    )
);
?>