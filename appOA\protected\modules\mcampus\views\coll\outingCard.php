<div id='homeAddress'>
    <div class="progress">
        <div class="progress-bar progress-bar-success" :style="'width:'+ parsefloat(filled/total) +'%;'">
            <span v-if='filled!=0' :title="filled+'/'+total">{{parsefloat(filled/total)}}% ({{filled}}/{{total}})</span>
            <span v-else>0% ({{filled}}/{{total}})</span>
        </div>
<!--        <div class="progress-bar progress-bar-warning progress-bar-striped" style="'width:0%">-->
<!--            <span >0% (0/--><?php //echo $data['count']?><!--)</span>-->
<!--        </div>-->
<!--        <div class="progress-bar progress-bar-danger" :style="'width:'+ parsefloat(unfilled/total) +'%;'">-->
<!--            <span v-if='unfilled!=0' :title="unfilled+'/'+total">{{parsefloat(unfilled/total)}}% ({{unfilled}}/{{total}})</span>-->
<!--            <span v-else>0% ({{unfilled}}/{{total}})</span>-->
<!--        </div>-->
    </div>
    <div class='pull-left' id="nameSearchComp"></div>
    <p class='pull-right'>
        <button type="button" class="btn btn-default btn-sm"  v-if='type=="filled"'  :disabled='btnCon' @click='batchPass("modal")'>批量通过</button>
        <button type="button" class="btn btn-default btn-sm ml10"  @click='exportTab()'>导出表格</button>
        <!-- <button type="button" class="btn btn-primary btn-sm ml10" v-if='type=="filled"' :disabled='btnCon'  @click='print()'>打印</button> -->
        <button type="button" class="btn btn-primary btn-sm ml10" v-if='type=="filled"' :disabled='btnCon'  @click='printAll()'>打印</button>
    </p>
    <div class='clearfix'></div>
    <div class='relative'>
        <div class='loading' v-show='loading'>
            <span></span>
        </div>
        <ul class="nav nav-tabs" role="tablist">
            <li role="presentation" class="active"><a href="#home" aria-controls="home" role="tab" data-toggle="tab"  @click='getInfo("filled")'>已填写 <span class="badge">{{filled}}</span></a></li>
            <li role="presentation"><a href="#unstart" aria-controls="unstart" role="tab" data-toggle="tab" @click='getInfo("unfilled")'>未填写 <span class="badge badge-error">{{unfilled}}</span></a></li>
        </ul>
        <div class="tab-content">
            <div role="tabpanel" class="tab-pane active" id="home">
                <table class="table table-bordered mt20" id='filled' v-show='list.length!=0'>
                    <thead>
                    <tr>
                        <th width='30' class="nosort">
                            <input type="checkbox" id="inlineCheckbox1" value="option1"  v-model='all'  @change='checkAll($event)'>
                        </th>
                        <th width='150'><?php echo Yii::t('global', 'Name') ?></th>
                        <th width='200'><?php echo Yii::t('labels', 'Class') ?></th>
                        <th width='150'><?php echo Yii::t('labels', 'Date of Birth') ?></th>
                        <th width='150'><?php echo Yii::t('labels', 'Status') ?></th>
                        <th width='150'>提交时间</th>
                        <th width='80' class="nosort"><?php echo Yii::t('global', 'Action') ?></th>
                    </tr>
                    </thead>
                    <tbody v-if='isShow'>
                        <tr v-for='(item,index) in list'>
                            <th>
                                <input type="checkbox" id="inlineCheckbox1" :value="item.childid" :disabled='item.step_status==4?true:false' v-model='child_ids' @change='childCheck($event)'>
                            </th>
                            <td >
                                <a href="javascript:;"  @click="overalls(item.childid)">
                                    {{item.name}}
                                </a>
                            </td>
                            <td > {{item.class_name}}</td>
                            <td >{{item.birthday}}</td>
                            <td >
                                <i class='glyphicon glyphicon-remove-sign' v-if='item.step_status==3' style='color:#D5514E'></i>
                                <i class='glyphicon glyphicon-ok-sign'  v-if='item.step_status==4' style='color:#5cb85c'></i>
                                <i class='glyphicon glyphicon-registration-mark'  v-if='item.step_status==1' style='color:#f0ad4e'></i>
                                <i class='glyphicon glyphicon-question-sign'  v-if='item.step_status==0' style='color:#f0ad4e'></i>
                            {{statusList[item.step_status]}}</td>

                            <td >{{item.step_data.submitted_at}} </td>
                            <td >
                                <a  class="btn btn-primary btn-xs" href='javascript:;' @click='auditList(item)'><?php echo Yii::t('global', 'View Detail') ?></a>
                                <a class="btn btn-primary btn-xs" :href="'<?php echo $this->createUrlReg('batchPrint', array('branchId' => $this->branchId,'coll_id'=>$coll_id)); ?>&childid='+item.childid+'&step_id=outingCard'" target="_blank">
                                    <?php echo Yii::t('global', 'Print') ?>
                                </a>
                            </td>
                        </tr>
                    </tbody>
                </table>

                <div class="mt15" v-show='list.length==0'>
                    <div class="alert alert-warning" role="alert"><?php echo Yii::t('ptc', 'No Data') ?></div>
                </div>
            </div>
            <!-- 未填写的数据 -->
            <div role="tabpanel" class="tab-pane " id="unstart">
                <table class="table table-bordered mt20" id='unfilled' v-show='unList.length!=0'>
                    <thead>
                    <tr>
                        <th><?php echo Yii::t('global', 'Name') ?></th>
                        <th><?php echo Yii::t('labels', 'Class') ?></th>
                        <th><?php echo Yii::t('labels', 'Date of Birth') ?></th>
                        <th><?php echo Yii::t('labels', 'Status') ?></th>
                    </tr>
                    </thead>
                    <tbody  v-if='isShow'>
                    <tr v-for='(item,index) in unList'>
                            <td >
                                <a href="javascript:;"  @click="overalls(item.childid)">
                                    {{item.name}}
                                </a>
                            </td>
                            <td > {{item.class_name}}</td>
                            <td >{{item.birthday}}</td>
                            <td >{{statusList[item.step_status]}}</td>
                        </tr>
                    </tbody>
                </table>

                <div class="mt15" v-show='unList.length==0'>
                    <div class="alert alert-warning" role="alert"><?php echo Yii::t('ptc', 'No Data') ?></div>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" tabindex="-1" role="dialog" id='auditDialog' data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            <h4 class="modal-title"><?php echo Yii::t('global', 'View Detail') ?></h4>
            </div>
            <div class="modal-body" v-if='Object.keys(rejectionList).length != 0'>
                <div class="media col-md-12 ">
                    <div class="media-left pull-left media-middle">
                        <a href="javascript:void(0)">
                            <img :src="rejectionList.avatar" data-holder-rendered="true" class="media-object img-circle avatarImage">
                        </a>
                    </div> 
                    <div class="media-body pt10 media-middle">
                        <h4 class="media-heading font14">{{rejectionList.name}}</h4> 
                        <div class="text-muted">{{rejectionList.class_name}}</div>
                    </div>
                </div>  
                <div class='col-md-12 mt20'>
                    <span class='color3 mr10'><?php echo Yii::t('coll', 'What identification documents are being used by the student for this trip?') ?> ：
                    {{rejectionList.step_data.identity_type=='ID_card'?'<?php echo Yii::t('coll', 'Chinese ID card') ?>':rejectionList.step_data.identity_type=='passport'?'<?php echo Yii::t('coll', 'Passport') ?>':'<?php echo Yii::t('coll', 'Other documents (Hong Kong, Macau, Taiwan)') ?>'}}
                </div>
                <div v-if='rejectionList.step_data.identity_type=="ID_card"'>
                    <div class='col-md-12 mt20'>
                        <span class='color3 mr10'><?php echo Yii::t('reg', '中国身份证号') ?> ：
                        {{rejectionList.step_data.identity_number}}
                    </div>
                    <div class='col-md-12 mt20'>
                        <span class='color3 mr10'><?php echo Yii::t('coll', 'ID card expiry date') ?> ：
                        {{rejectionList.step_data.identity_validity}}
                    </div>
                    <div class='col-md-12 mt20'>
                        <p><span class='color3 mr10'><?php echo Yii::t('coll', 'All pages of the documents') ?> ：</p>
                        <img v-for='(list,index) in rejectionList.step_data.identity_photo'  @click='bigImg(list)'  :src="list" alt="" class='stepUpImg'>
                    </div>
                </div>
                <div v-if='rejectionList.step_data.identity_type=="passport"'>
                    <div class='col-md-12 mt20'>
                        <span class='color3 mr10'><?php echo Yii::t('reg', '护照号') ?> ：
                        {{rejectionList.step_data.identity_number}}
                    </div>
                    <div class='col-md-12 mt20'>
                        <span class='color3 mr10'><?php echo Yii::t('coll', 'ID card expiry date') ?> ：
                        {{rejectionList.step_data.identity_validity}}
                    </div>
                    <div class='col-md-12 mt20'>
                        <span class='color3 mr10'><?php echo Yii::t('coll', 'Chinese visa number on your passport/or foreign permanent resident ID card number') ?> ：
                        {{rejectionList.step_data.visa_number }}
                    </div>
                    <div class='col-md-12 mt20'>
                        <span class='color3 mr10'><?php echo Yii::t('coll', 'Students passport\'s personal information page and valid visa page/ or foreign permanent resident ID card page') ?> ：
                        <img v-for='(list,index) in rejectionList.step_data.identity_photo'  @click='bigImg(list)'  :src="list" alt="" class='stepUpImg'>
                    </div>
                </div>
                <div v-if='rejectionList.step_data.identity_type=="other"'>
                <div class='col-md-12 mt20'>
                        <span class='color3 mr10'><?php echo Yii::t('reg', '证件号') ?> ：
                        {{rejectionList.step_data.identity_number}}
                    </div>
                    <div class='col-md-12 mt20'>
                        <span class='color3 mr10'><?php echo Yii::t('coll', 'ID card expiry date') ?> ：
                        {{rejectionList.step_data.identity_validity}}
                    </div>
                    <div class='col-md-12 mt20'>
                       <p> <span class='color3 mr10'><?php echo Yii::t('coll', 'All pages of the documents') ?> ：</p>
                        <img v-for='(list,index) in rejectionList.step_data.identity_photo'  @click='bigImg(list)'  :src="list" alt="" class='stepUpImg'>
                    </div>
                </div>
                <div class='col-md-12 mt20'>
                    <span class='color3 mr10'><?php echo Yii::t('coll', 'Full name as on ID card') ?> ：
                    {{rejectionList.step_data.identity_name}}
                </div>
                <div class='col-md-12 mt20'>
                    <span class='color3 mr10'><?php echo Yii::t('coll', 'Emergency contact person for this trip') ?> ：
                    {{rejectionList.step_data.emergency_contact}}
                </div>
                <div class='col-md-12 mt20'>
                    <span class='color3 mr10'><?php echo Yii::t('coll', 'Relationship to student') ?> ：
                    {{rejectionList.step_data.emergency_relation}}
                </div>
                <div class='col-md-12 mt20'>
                    <span class='color3 mr10'><?php echo Yii::t('coll', 'Emergency contact phone number') ?> ：
                    {{rejectionList.step_data.emergency_tel}}
                </div>
               
                <div class='col-md-12 mt20' v-if='rejectionList.sign_url!=""'>
                    <p><?php echo Yii::t('event', 'Signature') ?>：</p>
                    <img :src="rejectionList.sign_url" alt="" class='signImgDetails'>
                </div>
                <div class='clearfix'></div>
                <hr>
                <div class='col-md-12 col-sm-12 font14'>
                    <div class='col-md-6 col-sm-6 borderRight scroll-box' style='max-height:250px;overflow-y:auto'>
                        <p><strong>当前审核状态</strong></p>

                        <div>
                            {{statusList[rejectionList.step_status]}}
                            <button type="button" class="btn btn-primary  btn-xs pull-right" @click='resetStatus()'  v-if='rejectionList.step_status==3 || rejectionList.step_status==4'><?php echo Yii::t('global', 'Reset') ?></button>
                        </div>
                        <p class='mt10'><strong>审核记录</strong></p>
                        <div>
                            <div v-for='(list,index) in rejectionList.audit_log' class='ml10' style='border-left:1px dashed #ccc'>
                            <p style='margin-left:-7px;background: #fff;' >
                                <span v-if='list.status==4'>
                                <span class='glyphicon glyphicon-ok-sign greenIcon'></span><span> <?php echo Yii::t('global', 'Confirmed') ?></span>
                                </span>
                                <span v-if='list.status==3'>
                                <span class='glyphicon glyphicon-info-sign redIcon'></span> <span> <?php echo Yii::t('reg', 'Rejected') ?></span>
                                </span>
                                <span v-if='list.status==0'>
                                <span class='glyphicon glyphicon-question-sign yellowIcon'></span> <span> <?php echo Yii::t('global', 'Reset') ?></span>
                                </span>
                                </p>
                            <p class='ml10' style='background:#F7F7F8;line-height:22px'  v-if='list.comment!="" && list.comment!=null'><?php echo Yii::t('reg', '备注：') ?>{{list.comment}}</p>
                            <p class='pl10 font12'>{{list.date}} {{list.user}}</p>
                            </div>
                        </div>
                    </div>
                    <div class='col-md-6 col-sm-6' >
                        <p><strong>审核</strong></p>
                        <div class='p10 grey'>
                            <label class="radio ml20">
                                <input type="radio" id="inlineradio1" v-model='audit_status' value="4"> <?php echo Yii::t('labels', 'Yes') ?>
                            </label>
                            <label class="radio ml20">
                                <input type="radio" id="inlineradio2" v-model='audit_status' value="3"> 未通过
                            </label>
                            <textarea class="form-control" rows="3" placeholder='请输入备注' v-model='comment'></textarea>
                            <div v-if='audit_status==3 || type_status==2'>
                                <p class='mt10'>修改建议将直接显示在家长端，请准确措辞。</p>
                                <div class="checkbox">
                                    <label>
                                        <input type="checkbox"  v-model='send_wechat'>将审核结果发送至家长微信
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class='mt20'>
                            <button type="button"  class="btn btn-primary pull-right" :disabled='btnCon' @click='saveInfo()'><?php echo Yii::t("newDS", "确认"); ?></button>
                            <button type="button" class="btn btn-default  pull-right mr10" data-dismiss="modal"><?php echo Yii::t("global", "Close"); ?></button>
                        </div>
                    </div>
                </div>
                <div class='clearfix'></div>
            </div>
        </div>
        </div>
    </div>
    <div class="modal fade" id="stuDetails" tabindex="-1" role="dialog" >
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" ><?php echo Yii::t('attends', '整体情况') ?> <span id="remarks_t"></span></h4>
                </div>
                <div class="modal-body color6" v-if='childData.child'>
                    <p><strong class='font14 color3'>学生资料</strong></p>
                    <div class='flex'>
                        <div style='width:80px'><img :src="childData.child.avatar" class='stuImg'></div>
                        <div class='flex1'>
                            <p class='mt10'><strong class='font14 color3 studentName'>{{childData.child.name}}</strong> </p>
                            <p class='mb20'>{{childData.child.class_name}}</p>
                            <div  v-if='childData.parents.finfo!=null'>
                                <div class='col-md-4'>
                                <strong class='color3'><?php echo Yii::t('global', 'Father') ?>：</strong>
                                    <span class='glyphicon glyphicon-user'></span> {{childData.parents.finfo.name}}
                                </div>
                                <div class='col-md-4'>
                                    <span class='glyphicon glyphicon-phone'></span> {{childData.parents.finfo.mphone}}
                                </div>
                                <div class='col-md-4'>
                                    <span class='glyphicon glyphicon-envelope'></span> {{childData.parents.finfo.email}}
                                </div>
                            </div>
                            <div class='clearfix'></div>
                            <div class='mt10' v-if='childData.parents.minfo!=null'>
                                <div class='col-md-4'>
                                <strong class='color3'><?php echo Yii::t('global', 'Mother') ?>：</strong>
                                    <span class='glyphicon glyphicon-user'></span> {{childData.parents.minfo.name}}
                                </div>
                                <div class='col-md-4'>
                                    <span class='glyphicon glyphicon-phone'></span> {{childData.parents.minfo.mphone}}
                                </div>
                                <div class='col-md-4'>
                                    <span class='glyphicon glyphicon-envelope'></span> {{childData.parents.minfo.email}}
                                </div>
                            </div>
                        </div>
                    </div>
                    <ul class="nav nav-wizard pb20 mt20">
                        <li v-for='(list,index) in childData.step_status'>
                            <a href="javascript::" class='color6' :data-tab="list.step_id" :data-status="list.status" :data-name="childData.child.name"><span class="number">{{index+1}}</span>{{list.title}}</a>
                            <p class='mt10 color6' >
                                <span :class="list.status==4?'greenStep':list.status==3?'redStep':list.status==1 || list.status==2?'orgStep':''">{{ statusList[list.status]}}</span></p>
                        </li>
                    </ul>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('global', 'Cancel') ?></button>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="tipsModal" tabindex="-1" role="dialog" >
        <div class="modal-dialog modal-sm" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" ><?php echo Yii::t('attends', '批量通过') ?> <span id="remarks_t"></span></h4>
                </div>
                <div class="modal-body color6" >
                    <div class='color3 font14'>确认批量通过审核吗？</div> 
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('global', 'Cancel') ?></button>
                    <button type="button" class="btn btn-primary" :disabled='btnTips' @click='batchPass()'><?php echo Yii::t('global', '确定') ?></button>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="printDialog" tabindex="-1" role="dialog" data-backdrop="static" data-keyboard="false" >
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentCommentsModalLabel"><?php echo Yii::t("referral", "打印"); ?></h4>
                </div>
                <div class="modal-body p0 relative">
                    <span class='borderLeftpos'></span>
                    <div style='max-height:600px;' class='p24 row'>
                        <div class='col-md-6 col-sm-6'>
                            <div  class="tab-pane active scroll-box" id="class" style='max-height:525px;overflow-y:auto'>
                                <div v-for='(list,index) in sortClassData' class='relative mb16'>
                                    <p  @click='getChild(list)'>
                                        <span  class='font14 color606 cur-p'>{{list.class_name}} </span>
                                        <span class='el-icon-arrow-down ml5' v-if='classId!=list.class_id'></span>
                                        <span class='el-icon-arrow-up ml5' v-else></span>
                                    </p>
                                    <p  class='allCheck' v-if='classId==list.class_id'><button class="btn btn-default pull-right btn-xs" type="button" @click='selectAll(list,index)'  v-if='list.childData && list.childData.length!=0'><?php echo Yii::t("global", "Select All");?></button></p>
                                    <div  class='border scroll-box mr10 childList' v-if='classId==list.class_id'>
                                        <div class='' v-if='list.childData && list.childData.length!=0'>
                                            <div class="media mt10 flex align-items listMedia" v-for='(item,idx) in list.childData'>
                                                <div class="media-left pull-left media-middle">
                                                    <a href="javascript:void(0)">
                                                        <img :src="item.avatar" data-holder-rendered="true" class="media-object img-circle avatar32">
                                                    </a>
                                                </div>
                                                <div class="media-body media-middle flex1">
                                                    <div class="font14 lineHeight">{{item.name}}</div>
                                                </div>
                                                <div v-if='item.disabled' class="media-right pull-right text-muted lineHeight">
                                                    <span class='cur-p mt10 color9'><?php echo Yii::t("newDS", "Subscribed");?></span>
                                                </div>
                                                <div v-else class="media-right pull-right text-muted" @click='assignChildren(item,index,idx)'>
                                                    <span class='cur-p font16 bluebg  el-icon-circle-plus-outline'></span>
                                                </div>
                                                
                                            </div>
                                        </div>
                                        <div v-else>
                                            <div class='font12 text-muted text-center'><?php echo Yii::t("newDS", "no student in this class");?></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class='col-md-6 col-sm-6 borderLeft'>
                            <p class='font14 color606'>
                            <?php echo Yii::t("newDS", " ");?>{{childSelected.length}}<?php echo Yii::t("newDS", " student(s) selected");?>
                                <button class="btn btn-link pull-right btn-xs font14" v-if='childSelected.length!=0' type="button" @click='batchDel("modal")'><?php echo Yii::t("directMessage", "Clear All");?></button>
                            </p>
                            <div class='scroll-box p10 overflow-y' style='height:500px'>
                                <div class="media m0 flex align-items listMedia" v-for='(list,index) in childSelected'>
                                    <div class="media-left pull-left media-middle">
                                        <a href="javascript:void(0)">
                                            <img :src="list.avatar" data-holder-rendered="true" class="media-object img-circle avatar32">
                                        </a>
                                    </div>
                                    
                                    <div class="media-body media-middle flex1">
                                        <div class=" font12 color3">{{list.name}}</div>
                                    </div>
                                    <div class="media-right pull-right text-muted" @click='Unassign(list,index)'>
                                        <span class='closeChild cur-p  font16 el-icon-circle-close'></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class='clearfix'></div>
                    </div>
                </div> 
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Cancel"); ?></button>
                    <button type="button" class="btn btn-primary" @click='printChild()'><?php echo Yii::t("global", "打印"); ?></button>
                </div>
            </div>
        </div>
    </div>
</div>
<script>

    var step_id = '<?php echo $step_id ?>';
    var coll_id = '<?php echo $coll_id ?>';
    var type = '<?php echo $type ?>';
    var type_status = '<?php echo $type_status ?>';

    var  registrations = new Vue({
        el: "#homeAddress",
        data: {
            type_status:type_status,
            list:[],
            unfilled:0,
            coll_id:coll_id,
            filled:0,
            total:0,
            type:type,
            logList:[],
            statusList:[],
            audit_status:'',
            comment:'',
            child_id:'',
            childData:{},
            child_ids:[],
            rejectionList:{},
            unList:{},
            btnCon:false,
            send_wechat:true,
            isShow:true,
            loading:false,
            all:false,
            btnTips:false,
            sortClassData:[],
            classId:'',
            childSelected:[]
        },
        created: function() {
            this.getInfo(this.type)
            eventBus.$on('listChanged', list => {
                if(sessionStorage.getItem('tabType')  === 'filled'){
                    this.list = list;
                }else{
                    this.unList = list
                }
            });
        },
        watch: {
        },
        methods: {
            overalls(id){
                let that=this
                $.ajax({
                    url:'<?php echo $this->createUrlReg('getStudentOverall'); ?>',
                    type: 'get',
                    dataType:'json',
                    data:{
                        child_id:id,
                        coll_id:coll_id,
                    },
                    success:function(data){
                        if(data.state=="success"){
                            that.childData=data.data
                            $('#stuDetails').modal('show')
                        }else{
                            resultTip({error: 'warning', msg: data.message});
                        }
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            parsefloat(num) {
                return Number(num * 100).toFixed(2);
            },
            getInfo(type){
                let that = this
                this.type=type
                this.isShow=false
                $.ajax({
                    url:'<?php echo $this->createUrlReg('stepTemplateInfo'); ?>',
                    type: 'get',
                    dataType:'json',
                    data:{
                        coll_id:coll_id,
                        step_id:step_id,
                        type:type
                    },
                    success:function(data){
                        if(data.state=="success"){
                            if(type=='unfilled'){
                                that.unList = data.data.list
                            }else{
                                that.list = data.data.list
                            }
                            that.unfilled = data.data.unfilled
                            that.filled = data.data.filled
                            that.total = data.data.count
                            that.statusList=data.data.statusList
                            that.logList=data.data.logList
                            that.isShow=true
                            sessionStorage.setItem('tabType', type);
                            sessionStorage.setItem('listUpdated',  JSON.stringify(data.data.list));
                            that.$forceUpdate();
                            that.$nextTick(()=>{
                                that.DataTable(data)
                            })
                        }else{
                            resultTip({error: 'warning', msg: data.message});
                        }
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            DataTable(data){
                let that=this
                    that.$nextTick(()=>{
                        $('#stuDetails').modal('hide');
                        $('#nameSearchComp').html(data.data.nameSearchComp)
                        if(that.type=='filled'){
                            var table = $('#filled').DataTable({
                                aaSorting: [5, 'desc'], // 默认排序
                                paging: false,
                                info: false,
                                searching: false,
                                "destroy": true,
                                columnDefs: [ {
                                    "targets": 'nosort',
                                    "orderable": false
                                } ],
                            });
                        }else{
                            var table = $('#unfilled').DataTable({
                                aaSorting: [1, 'desc'], // 默认排序
                                paging: false,
                                info: false,
                                "destroy": true,
                                searching: false,
                            });
                        }
                    })

            },
            auditList(list){
                this.rejectionList=list
                this.child_id=list.childid
                this.audit_status=''
                this.comment=''
                $('#auditDialog').modal('show')

            },
            exportTab() {
                if(this.type=='filled'){
                    const filename ='学生旅行证件收集.xlsx';
                    const ws_name = "SheetJS";
                    var exportDatas = [];
                    for(var i=0;i<this.list.length;i++){
                        var data={
                            'ID':this.list[i].childid,
                            'ID':this.list[i].childid,
                            '<?php echo Yii::t('global', 'Name') ?>':this.list[i].name,
                            "<?php echo Yii::t('labels', 'Class') ?>":this.list[i].className,
                            "<?php echo Yii::t('labels', 'Date of Birth') ?>":this.list[i].birthday,
                            "<?php echo Yii::t('labels', 'Status') ?>":this.statusList[this.list[i].step_status],
                            "<?php echo Yii::t('labels', 'Barcode') ?>":this.list[i].barcode,
                            "<?php echo Yii::t('labels', 'Educational Student ID') ?>":this.list[i].educational_id,
                            "<?php echo Yii::t("labels",'中文法定名')?>": this.list[i].is_legel_cn_name==1 ? '是':'否',
                            "<?php echo Yii::t("labels",'Chinese Name')?>":this.list[i].cn_name,
                            "<?php echo Yii::t("labels",'Legal First Name')?>":this.list[i].first_name_en,
                            "<?php echo Yii::t("labels",'Legal Middle Name')?>":this.list[i].middle_name_en,
                            "<?php echo Yii::t("labels",'Legal Last Name')?>":this.list[i].last_name_en,
                            "<?php echo Yii::t("labels",'Preferred Name')?>":this.list[i].nick,
                            "<?php echo Yii::t("labels",'Gender')?>":this.list[i].gender == 1 ? '男' : '女',
                            "<?php echo Yii::t("labels",'Nationality')?>":this.list[i].country,
                            "<?php echo Yii::t("labels",'Language')?>":this.list[i].lang,
                            "<?php echo Yii::t("labels",'Father Name')?>":this.list[i].father_name,
                            "<?php echo Yii::t("labels",'Father Email')?>":this.list[i].father_email,
                            "<?php echo Yii::t("labels",'Father Mobile')?>":this.list[i].father_phone,
                            "<?php echo Yii::t("labels",'Mother Name')?>":this.list[i].mather_name,
                            "<?php echo Yii::t("labels",'Mother Email')?>":this.list[i].mather_email,
                            "<?php echo Yii::t("labels",'Mother Mobile')?>":this.list[i].mather_phone,
                            "<?php echo Yii::t("labels",'ID NO./Passport NO.')?>":this.list[i].identity,
                            "<?php echo Yii::t('coll', 'What identification documents are being used by the student for this trip?') ?>" :this.list[i].step_data.identity_type=='ID_card'?'<?php echo Yii::t('coll', 'Chinese ID card') ?>':this.list[i].step_data.identity_type=='passport'?'<?php echo Yii::t('coll', 'Passport') ?>':'<?php echo Yii::t('coll', 'Other documents (Hong Kong, Macau, Taiwan)') ?>',
                            "本次出行证件号" :this.list[i].step_data.identity_number,
                            "<?php echo Yii::t('coll', 'ID card expiry date') ?>" :this.list[i].step_data.identity_validity,
                            "<?php echo Yii::t('coll', 'ID card expiry date') ?>" :this.list[i].step_data.visa_number,
                            "<?php echo Yii::t('coll', 'Full name as on ID card') ?>" :this.list[i].step_data.identity_name,
                            "<?php echo Yii::t('coll', 'Emergency contact person for this trip') ?>" :this.list[i].step_data.emergency_contact,
                            "<?php echo Yii::t('coll', 'Relationship to student') ?>" :this.list[i].step_data.emergency_relation,
                            "<?php echo Yii::t('coll', 'Emergency contact phone number') ?>" :this.list[i].step_data.emergency_tel,
                            "提交时间":this.list[i].step_data.submitted_at,
                            }
                            if(this.list[i].step_data.identity_type=='ID_card'){}

                            exportDatas.push(data)
                    }
                    var wb=XLSX.utils.json_to_sheet(exportDatas,{
                        origin:'A1',// 从A1开始增加内容
                        header: ['ID',
                        '<?php echo Yii::t('global', 'Name') ?>',
                            '<?php echo Yii::t('labels', 'Class') ?>',
                            '<?php echo Yii::t('labels', 'Date of Birth') ?>',
                            '<?php echo Yii::t('labels', 'Status') ?>',
                            '<?php echo Yii::t('labels', 'Barcode') ?>',
                            '<?php echo Yii::t('labels', 'Educational Student ID') ?>',
                            '<?php echo Yii::t('labels','中文法定名')?>',
                            '<?php echo Yii::t('labels','Chinese Name')?>',
                            '<?php echo Yii::t('labels','Legal First Name')?>',
                            '<?php echo Yii::t('labels','Legal Middle Name')?>',
                            '<?php echo Yii::t('labels','Legal Last Name')?>',
                            '<?php echo Yii::t('labels','Preferred Name')?>',
                            '<?php echo Yii::t('labels','Gender')?>',
                            '<?php echo Yii::t('labels','Nationality')?>',
                            '<?php echo Yii::t('labels','Language')?>',
                            '<?php echo Yii::t('labels','Father Name')?>',
                            '<?php echo Yii::t('labels','Father Email')?>',
                            '<?php echo Yii::t('labels','Father Mobile')?>',
                            '<?php echo Yii::t('labels','Mother Name')?>',
                            '<?php echo Yii::t('labels','Mother Email')?>',
                            '<?php echo Yii::t('labels','Mother Mobile')?>',
                            '<?php echo Yii::t('labels','ID NO./Passport NO.')?>',
                            '提交时间',
                            '<?php echo Yii::t('coll', 'What identification documents are being used by the student for this trip?') ?>',
                            '本次出行证件号',
                            '<?php echo Yii::t('coll', 'ID card expiry date') ?>',
                            '<?php echo Yii::t('coll', 'ID card expiry date') ?>',
                            '<?php echo Yii::t('coll', 'Full name as on ID card') ?>', 
                            '<?php echo Yii::t('coll', 'Emergency contact person for this trip') ?>',
                            '<?php echo Yii::t('coll', 'Relationship to student') ?>',
                            '<?php echo Yii::t('coll', 'Emergency contact phone number') ?>',
                            ],
                    });
                    const workbook = XLSX.utils.book_new();
                    XLSX.utils.book_append_sheet(workbook, wb, ws_name);
                    const wbout = XLSX.write(workbook, {bookType: 'xlsx', type: 'array'});
                    const blob = new Blob([wbout], {type: 'application/octet-stream'});
                    let link = document.createElement('a');
                    link.href = URL.createObjectURL(blob);
                    link.download = filename;
                    link.click();
                    setTimeout(function() {
                        // 延时释放掉obj
                        URL.revokeObjectURL(link.href);
                        link.remove();
                    }, 500);
                }else{
                    var title='学生旅行证件收集.xlsx'
                    const ws_name = "SheetJS";
                    var exportDatas = [];
                    for(var i=0;i<this.unList.length;i++){
                        exportDatas.push(
                            {
                            'ID':this.unList[i].childid,
                            '<?php echo Yii::t('global', 'Name') ?>':this.unList[i].name,
                            "<?php echo Yii::t('labels', 'Class') ?>":this.unList[i].className,
                            "<?php echo Yii::t('labels', 'Date of Birth') ?>":this.unList[i].birthday,
                            "<?php echo Yii::t('labels', 'Status') ?>":this.statusList[this.unList[i].step_status],
                            });
                    }
                    var wb=XLSX.utils.json_to_sheet(exportDatas,{
                        origin:'A1',// 从A1开始增加内容
                        header: ['ID','<?php echo Yii::t('global', 'Name') ?>', '<?php echo Yii::t('labels', 'Class') ?>','<?php echo Yii::t('labels', 'Date of Birth') ?>','<?php echo Yii::t('labels', 'Status') ?>'],
                    });
                    const workbook = XLSX.utils.book_new();
                    XLSX.utils.book_append_sheet(workbook, wb, ws_name);
                    const wbout = XLSX.write(workbook, {bookType: 'xlsx', type: 'array'});
                    const blob = new Blob([wbout], {type: 'application/octet-stream'});
                    let link = document.createElement('a');
                    link.href = URL.createObjectURL(blob);
                    link.download = title;
                    link.click();
                    setTimeout(function() {
                        // 延时释放掉obj
                        URL.revokeObjectURL(link.href);
                        link.remove();
                    }, 500);
                }
            },
            checkAll(e){
                this.child_ids=[]
                if(e.target.checked){
                    for(var i=0;i<this.list.length;i++){
                        if(this.list[i].step_status!=4){
                            this.child_ids.push(this.list[i].childid)
                        }
                    }
                    this.all=true
                }else{
                    this.all=false
                }
            },
            childCheck(e){
                let checkListLen=this.list.filter((item)=>{return item.step_status!=4});
                if(e.target.checked){
                    if(this.child_ids.length==checkListLen.length){
                        this.all=true
                    }else{
                        this.all=false
                    }
                }else{
                    this.all=false
                }
            },
            batchPass(modal){
                let that = this
                if(this.child_ids.length==0){
                    resultTip({error: 'warning', msg: '请选择学生'});
                    return
                }
                if(modal){
                    $('#tipsModal').modal('show')
                    return
                }
                this.loading=true
                this.btnCon=true
                this.btnTips=true
                $.ajax({
                    url:'<?php echo $this->createUrlReg('batchPass'); ?>',
                    type: 'post',
                    dataType:'json',
                    data:{
                        child_ids:this.child_ids,
                        step_id:step_id,
                        coll_id:this.coll_id
                    },
                    success:function(data){
                        if(data.state=="success"){
                            resultTip({
								msg: data.state
							});
                            that.getInfo(that.type)
                            $('#auditDialog').modal('hide')
                            $('#tipsModal').modal('hide')
                        }else{
                            resultTip({error: 'warning', msg: data.message});
                        }
                        that.btnCon=false
                        that.loading=false
                        that.btnTips=false
                    },
                    error:function(data){
                        that.btnCon=false
                        that.loading=false
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            resetStatus(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrlReg("stepAudit") ?>',
                    type: "get",
                    dataType: 'json',
                    data:{
                        child_id:this.child_id,
                        step_id:step_id,
                        audit_status:0,
                        coll_id:this.coll_id,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            resultTip({
								msg: data.state
							});
                            that.getInfo(that.type)
                            $('#auditDialog').modal('hide')
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                    },
                })
            },
            saveInfo(){
                let that = this
                if(this.audit_status==''){
                    resultTip({
                        error: 'warning',
                        msg: '请选择审核状态'
                    });
                    return
                }
                that.btnCon=true
                var dataList={
                    child_id:this.child_id,
                    step_id:step_id,
                    audit_status:this.audit_status,
                    coll_id:this.coll_id,
                    comment: this.comment,
                }
                if(this.audit_status==3 || this.type_status==2){
                   var dataList=Object.assign({send_wechat:that.send_wechat?1:0},dataList)
                }
                $.ajax({
                    url:'<?php echo $this->createUrlReg('stepAudit'); ?>',
                    type: 'get',
                    dataType:'json',
                    data:dataList,
                    success:function(data){
                        if(data.state=="success"){
                            resultTip({
								msg: data.state
							});
                            that.getInfo(that.type)
                            $('#auditDialog').modal('hide')
                        }else{
                            resultTip({error: 'warning', msg: data.message});
                        }
                        that.btnCon=false
                    },
                    error:function(data){
                        that.btnCon=false
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            bigImg(list){
                var opacityBottom = '<div class="opacityBottom" style = "display:none"><img class="bigImg" src="' + list + '"></div>';
                $(document.body).append(opacityBottom);
                this.toBigImg();//变大函数
            },
            toBigImg() {
                $(".opacityBottom").addClass("opacityBottom");//添加遮罩层
                $(".opacityBottom").show();
                $("html,body").addClass("none-scroll");//下层不可滑动
                $(".bigImg").addClass("bigImg");//添加图片样式
                $(".opacityBottom").click(function () {//点击关闭
                    $("html,body").removeClass("none-scroll");
                    $(".opacityBottom").remove();
                });
            },
            printAll(){
                let that = this
                let childs=[]
                const groupedByClassId = this.list.reduce((acc, item) => {
                    if (!acc[item.class_id]) {
                        acc[item.class_id] = {
                        class_name: item.class_name, // 设置 class_name
                        childData: []                // 初始化学生数组
                        };
                    }
                    if (item.step_status === 4) {
                        acc[item.class_id].childData.push({
                            name: item.bilingual_name,
                            id:item.childid,
                            disabled:false,
                            avatar:item.avatar
                        });
                    }
                    return acc;
                }, {});
                this.sortClassData = Object.entries(groupedByClassId)
                .map(([class_id, value]) => ({ class_id, ...value }))
                .sort((a, b) => {
                    const getGradeNumber = (className) => parseInt(className.match(/Grade (\d+)/)[1], 10);
                    return getGradeNumber(a.class_name) - getGradeNumber(b.class_name);
                });
                if(this.sortClassData.length==0){
                    resultTip({error: 'warning', msg: '没有审核通过的学生'});
                    return
                }
                this.classId=''
                this.childSelected=[]
                $('#printDialog').modal('show')
            },
            getChild(list){
                let that=this
                if(that.classId==list.class_id){
                    that.classId=''
                    return
                }
                that.classId=list.class_id
                if (this.childSelected.length) {
                    const childId = this.childSelected.map(item => item.id);
                    const targetClass = that.sortClassData.find(item => item.class_id === list.class_id);
                    if (targetClass && targetClass.childData.length) {
                        targetClass.childData.forEach(child => {
                            child.disabled = childId.includes(child.id);
                        });
                        that.$forceUpdate();
                    }
                }
            },
            selectAll(list,index){
                for(var i=0;i<list.childData.length;i++){
                    if (!list.childData[i].disabled) {
                        this.childSelected.push(list.childData[i])
                        Vue.set(this.sortClassData[index].childData[i], 'disabled', true);
                    }
                }
                this.$forceUpdate()
            },
            assignChildren(list,index,idx){
                Vue.set(this.sortClassData[index].childData[idx], 'disabled', true);
                this.$forceUpdate()
                this.childSelected.push(list)
            },
            Unassign(data,index){
                for(var i=0;i<this.sortClassData.length;i++){
                    for(var j=0;j<this.sortClassData[i].childData.length;j++){
                        if(data.id==this.sortClassData[i].childData[j].id){
                            Vue.set(this.sortClassData[i].childData[j], 'disabled', false);
                        }
                    }
                }
                this.$forceUpdate()
                this.childSelected.splice(index,1)
            },
            batchDel(){
                this.childSelected=[]
                for(var i=0;i<this.sortClassData.length;i++){
                    for(var j=0;j<this.sortClassData[i].childData.length;j++){
                        this.sortClassData[i].childData[j].disabled=false
                    }                        
                }                
                this.$forceUpdate() 
            },
            printChild(){
                if(this.childSelected.length==0){
                    resultTip({error: 'warning', msg: '请选择学生'});
                    return
                }
                let child=[]
                this.childSelected.forEach(item => {
                    child.push(item.id)
                });
                let  child_ids =child.toString()
                window.open('<?php echo $this->createUrlReg('batchPrint'); ?>&childid='+child_ids+'&step_id='+step_id+'&coll_id='+this.coll_id,'_blank');
            }
        }
    })



</script>
<style>
    a[data-status="0"] {
        cursor: default;
        text-decoration: none;
    }
    .listMedia{
        padding:8px;
        border: 1px solid #fff;
        max-height:62px
    }
    .listMedia:hover{
        background: rgba(77,136,210,0.1);
        border-radius: 4px;
        border: 1px solid #4D88D2;
        cursor: pointer;
    }
    .listMedia:hover .closeIcon{
        display:block
    }
    .avatar32{
        width:32px;
        height: 32px;
        border-radius: 50%;
        object-fit: cover;
    }
    .allCheck {
        position: absolute;
        right: 10px;
        top: 4px;
    }
    .scroll-box::-webkit-scrollbar-thumb {
        /*滚动条里面小方块*/
        border-radius   : 10px;
        background-color: #ccc;
        background-image: none
    }
    .scroll-box::-webkit-scrollbar-track {
        /*滚动条里面轨道*/
        box-shadow   : inset 0 0 5px rgba(0, 0, 0, 0);
        background   : #fff;
        border-radius: 10px;
        border:none
    }
    .borderLeftpos {
        display: inline-block;
        border-left: 1px solid #E4E7ED;
        height: 100%;
        width: 1px;
        position: absolute;
        left: 50%;
        top:0px
    }
</style>
