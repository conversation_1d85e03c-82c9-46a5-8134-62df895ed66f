<div  id='container'  v-cloak>
    <div class='container-fluid'>
        <ol class="breadcrumb">
            <li><?php echo CHtml::link(Yii::t('site','Campus Operations'), array('//mcampus/default/index'))?></li>
            <li><?php echo CHtml::link(Yii::t('site','Routines'), array('//mcampus/default/index'))?></li>
            <li class="active"><?php echo Yii::t('site','Withdrawal Process');?></li>
        </ol>
        <div class='mt24'>
            <div class='flex list' v-if='indexDataList.length'>
                <div v-for='(item,index) in indexDataList' class='flex1 listData'  >
                    <div class='stepTitle'>
                        <div><span class='step'>{{index+1}}</span> </div>
                        <div class='font14 fontBold'>{{item.name}}</div>
                    </div>
                    <div class='overflow-y scroll-box' :style="'height:'+(height-275)+'px'">
                        <div  v-if='item.tasks.length>1'>
                            <div v-for='(_item,idx) in item.tasks' class='userList'>
                                <div class='font14 color3 text-center mb16 fontBold'>{{_item.name}} </div>
                                <div>
                                    <div class='flex userData' v-for='(owner,i) in _item.owner'>
                                        <img class='img24' :src="userInfo[owner].photoUrl" alt="">
                                        <div class='flex1'><div class='ellipsis'>{{userInfo[owner].name}}</div></div>
                                        <span class='el-icon-close font12 color6 cur-p' @click='delUser(item.node,_item.task,owner)'></span>
                                    </div>
                                </div>
                                <div class='flex addUser' @click='getSchoolList(item.node,_item.task)'>
                                    <img class='img24' src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/default1.png' ?>" alt="">
                                    <div class='flex1 text-center add'><span class='el-icon-plus font14 fontBold'></span></div>
                                </div>
                            </div>
                        </div>
                        <div v-else  class='userList'>
                            <div>
                                <div class='flex userData' v-for='(owner,i) in item.tasks[0].owner'>
                                    <img class='img24' :src="userInfo[owner].photoUrl" alt="">
                                    <div class='flex1'><div class='ellipsis'>{{userInfo[owner].name}}</div></div>
                                    <span class='el-icon-close font12 color6 cur-p' @click='delUser(item.node,item.tasks[0].task,owner)'></span>
                                </div>
                            </div>
                            <div class='flex addUser' @click='getSchoolList(item.node,item.tasks[0].task)'>
                                <img class='img24' src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/default1.png' ?>" alt="">
                                <div class='flex1 text-center add'><span class='el-icon-plus font14 fontBold'></span></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- 选择成员 -->
    <div class="modal fade" id="addStaffModal" tabindex="-1" role="dialog" data-backdrop="static" data-keyboard="false" data-backdrop="static">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentCommentsModalLabel"><?php echo Yii::t('leave', '添加成员') ?></h4>
                </div>
                <div class="modal-body relative" style='padding:0'>
                    <span class='borderLeftpos'></span>
                    <div style='max-height:700px;' class='p24 row'>
                        <div class='col-md-6 col-sm-6'>
                          <div>
                            <el-select v-model="schoolId" style='width:100%' size='small' placeholder="<?php echo Yii::t('teaching', 'Please select') ?>" @change='getSchoolData()'>
                                <el-option
                                    v-for="(item,key,index) in schoolList"
                                    :key="key"
                                    :label="item.title"
                                    :value="key">
                                </el-option>
                            </el-select>
                          </div>
                          <div class='mt10'>
                              <el-input
                              size='small' 
                              placeholder="<?php echo Yii::t('global', 'Search') ?>"
                              v-model='searchText' 
                              clearable>
                              </el-input>
                          </div>
                          <div  class="tab-pane active mt15 scroll-box" id="class" v-if='searchText==""'  style='max-height:460px;overflow-y:auto'>
                            <div v-if='currentDept.list && currentDept.list.length>0'>
                              <div v-for='(list,index) in currentDept.list' class='relative mb16'>
                                  <p  @click='showDepName(list)'>
                                      <span  class='font14 color606 cur-p'>{{list.dep_name}} </span>
                                      <span class='el-icon-arrow-down ml5' v-if='dep_name!=list.dep_name'></span>
                                      <span class='el-icon-arrow-up ml5' v-else></span>
                                  </p>
                                  <!-- <p  class='allCheck' v-if='dep_name==list.dep_name'>
                                    <button class="btn btn-default pull-right btn-xs" type="button" @click='selectAll(list,index)' ><?php echo Yii::t("global", "Select All");?></button>
                                    </p> -->
                                  <div  class='border scroll-box mr10 childList' v-if='dep_name==list.dep_name'>
                                      <div class="flex align-items listMedia" v-for='(item,key,idx) in list.user'>
                                          <div class='flex flex1' v-if='currentDept.user_info[item.uid]'>
                                              <img :src="currentDept.user_info[item.uid].photoUrl" data-holder-rendered="true" class="media-object img-circle img42">
                                              <div class="flex1 ml10 flex1Text">
                                                  <div class=" font14 mt8 color3 text_overflow">{{currentDept.user_info[item.uid].name}}</div>
                                                  <div class="font12 mt5 color6 text_overflow">{{currentDept.user_info[item.uid].hrPosition}}</div>
                                              </div>
                                          </div>
                                          <div >
                                              <span class='cur-p font16 text-primary  el-icon-circle-plus-outline' v-if='!currentDept.user_info[item.uid].disabled' @click='assignStaff(item,index,idx)'></span>
                                              <span v-else><?php echo Yii::t('directMessage', 'selected') ?></span>
                                          </div>
                                      </div>
                                  </div>
                              </div>
                            </div>
                            <div v-else>
                              <el-empty description="<?php echo Yii::t('ptc', '无可选择成员') ?>"></el-empty>
                            </div>
                          </div>
                          <div v-else>
                              <div v-if='searchStaffList.length!=0' class='mt24 scroll-box'  style='max-height:460px;overflow-y:auto'>  
                                  <div class="flex align-items listMedia" v-for='(item,idx) in searchStaffList'>
                                      <div class='flex flex1' >
                                          <img :src="item.photoUrl" data-holder-rendered="true" class="media-object img-circle img42">
                                          <div class="flex1 ml10 flex1Text">
                                              <div class=" font14 mt8 color3 text_overflow">{{item.name}}</div>
                                              <div class="font12 mt5 color6 text_overflow">{{item.hrPosition}}</div>
                                          </div>
                                      </div>
                                      <div >
                                          <span class='cur-p font16 text-primary  el-icon-circle-plus-outline' v-if='!currentDept.user_info[item.uid].disabled' @click='assignStaff(item,idx,"search")'></span>
                                          <span v-else><?php echo Yii::t('directMessage', 'selected') ?></span>
                                      </div>
                                  </div> 
                              </div>
                              <div v-else-if='searchText!=""'>
                                      <div class='font14 color6 text-center mt20'><?php echo Yii::t('ptc', 'No Data') ?></div>    
                              </div>
                          </div>
                        </div>
                        <div class='col-md-6 col-sm-6'>
                            <p class='font14 color6'>
                            <?php echo Yii::t("newDS", " ");?>{{staffSelected.length}} <?php echo Yii::t('leave', '成员') ?>
                            </p>
                            <div class='scroll-box p10 overflow-y' style='height:500px'>
                                <div class="flex align-items listMedia" v-for='(item,idx) in staffSelected'>
                                    <div class='flex flex1' v-if='allDept[item]'>
                                        <img :src="allDept[item].photoUrl" data-holder-rendered="true" class="media-object img-circle img42">
                                        <div class="flex1 ml10 flex1Text">
                                            <div class=" font14 mt8 color3 text_overflow">{{allDept[item].name}}</div>
                                            <div class="font12  mt5 color6 text_overflow">{{allDept[item].hrPosition}}</div>
                                        </div>
                                    </div>
                                    <div @click='Unassign(item,idx)'>
                                        <span class='closeChild cur-p mt5 font16 el-icon-circle-close'></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class='clearfix'></div>
                    </div>
                </div> 
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Cancel");?></button>
                    <el-button type="button" class="btn btn-primary"  @click='confirmSatff()' ><?php echo Yii::t("global", "OK");?></el-button>
                </div>
            </div>
        </div>
    </div>
     <!-- 删除 -->
     <div class="modal fade" id='delModal'  tabindex="-1" role="dialog" aria-labelledby="mySmallModalLabel">
        <div class="modal-dialog modal-sm" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title">
                        <span><?php echo Yii::t("global", "Delete"); ?></span> 
                    </h4>
                </div>
                <div class="modal-body" >
                    <div>
                        <?php echo Yii::t("directMessage", "Proceed to remove?");?> 
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                    <button type="button" class="btn btn-primary" @click='delUser()'><?php echo Yii::t("message", "OK");?></button>
                </div>
            </div>
        </div>
    </div>
</div>
<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>
<script>
    var height=document.documentElement.clientHeight;
    var container=new Vue({
        el: "#container",
        data: {
            height:height,
            indexDataList:[],
            userInfo:{},
            schoolId:'',
            schoolList:[],
            staffSelected:[],
            searchText:'',
            dep_name:'',
            currentDept:{},
            allDept:{},
            addNode:'',
            addTask:'',
            delData:{},
        },
        created: function() {
         this.indexData()
        },
        computed: {
            searchStaffList: function() {
                var search = this.searchText;
                var searchVal = ''; //搜索后的数据
                if(search) {
                    searchVal =Object.values(this.currentDept.user_info).filter(function(product) {
                        return Object.keys(product).some(function(key) {
                            return String(product['name'].toLowerCase()).indexOf(search.toLowerCase()) !== -1;
                        })
                    })
                    return searchVal;
                }
                return this.searchStaffList;
            },
        },
        methods: {
            indexData(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("ownersList") ?>',
                    type: "post",
                    dataType: 'json',
                    data:{
                        startYear: this.startYear, 
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.indexDataList=data.data.list
                            that.userInfo=data.data.user_info
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                        that.tableLoading=false
                    },
                    error: function(data) {
                        that.tableLoading=false
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            getSchoolList(node,task){
                this.addNode=node
                this.addTask=task
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("leave/schoolList") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {},
                    success: function(data) {
                        if (data.state == 'success') {
                            that.schoolList=data.data.list
                            that.schoolId=data.data.current_school
                            that.getSchoolData()
                            that.dep_name=''
                            that.searchText=''
                            that.staffSelected=[]
                            $("#addStaffModal").modal('show')
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            getSchoolData(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("leave/getAllDepartment") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        school_id:this.schoolId
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                           that.currentDept=data.data
                           that.allDept=Object.assign(that.allDept, data.data.user_info)
                           that.dep_name=''
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            showDepName(list){
                if(this.dep_name==list.dep_name){
                    this.dep_name=''
                    return
                }
                this.dep_name=list.dep_name
                for(let i=0;i<list.user.length;i++){
                    if(this.staffSelected.indexOf(list.user[i].uid)!=-1){
                        console.log(list.user[i].uid)
                        Vue.set(this.currentDept.user_info[list.user[i].uid], 'disabled', true);
                    }
                }
            },
            assignStaff(list,index,idx){
                if(idx=='search'){
                    this.searchStaffList[index].disabled=true
                }else{
                    Vue.set(this.currentDept.user_info[list.uid], 'disabled', true);
                }
                this.staffSelected.push(list.uid)
            },
            Unassign(list,index){
                if(this.currentDept.user_info[list]){
                    Vue.set(this.currentDept.user_info[list], 'disabled', false);
                }
                this.staffSelected.splice(index,1)
            },
            confirmSatff(){
                let ids=JSON.parse(JSON.stringify(this.staffSelected))
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("ownersAdd") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        node:this.addNode,
                        task:this.addTask,
                        user_id:ids
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            $("#addStaffModal").modal('hide')
                            that.indexData()
                            resultTip({
                                msg: data.message
                            });
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            delUser(node,task,id){
                if(node){
                    this.delData={ 
                        node:node,
                        task:task,
                        user_id:id
                    }
                    $('#delModal').modal('show')
                    return
                }
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("ownersDel") ?>',
                    type: "get",
                    dataType: 'json',
                    data:this.delData,
                    success: function(data) {
                        if (data.state == 'success') {
                            $("#delModal").modal('hide')
                            that.indexData()
                            resultTip({
                                msg: data.message
                            });
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            }
        },
    })

</script>
<style>
    .scroll-box::-webkit-scrollbar-thumb {
        /*滚动条里面小方块*/
        border-radius   : 10px;
        background-color: #ccc;
        background-image: none
    }
    .scroll-box::-webkit-scrollbar-track {
        /*滚动条里面轨道*/
        box-shadow   : inset 0 0 5px rgba(0, 0, 0, 0);
        background   : #fff;
        border-radius: 10px;
        border:none
    }
    .listData{
        background: #F7F7F8;
        border-radius: 4px;
        margin:0 8px
    }
   .list{
        margin: 0 -8px;
    }
    .stepTitle{
        padding:16px 0;
        background: #ECEEF3;
        border-radius: 4px 4px 0px 0px;
        text-align:center
    }
    .step{
        display:inline-block;
        width: 20px;
        height: 20px;
        background: #4D88D2;
        border-radius:50%;
        color:#fff;
        font-size:12px;
        margin-bottom:5px;
        line-height:20px;
    }
    .userList{
        padding:16px 12px;
        background: #FFFFFF;
        margin:16px 10px
    }
    .addUser{
        padding:4px 8px;
        background: #E9F3FF;
        border-radius: 4px;
        border: 1px solid #E9F3FF;

    }
    .img24{
        width: 24px;
        height: 24px;
        border-radius:50%;
        object-fit: cover;
    }
    .add{
        line-height:24px;
        color:#4D88D2;
        margin-left:-24px
    }
    .addUser:hover{
        border: 1px solid #4D88D2;
        cursor: pointer;
    }
    .userData{
        padding:4px 8px;
        background: #F2F3F5;
        border-radius: 4px;
        margin-bottom: 8px;
        align-items:center
    }
    .userData .flex1{
        flex: 1;
        width: 0;
        margin:0 10px 0 4px
    }
    .ellipsis{
        overflow: hidden; 
        text-overflow: ellipsis; 
        white-space: nowrap;
        font-size:12px;
        color:#333
    }
    .listMedia{
        padding:8px;
        border: 1px solid #fff;
    }
    .listMedia:hover{
        background: rgba(77,136,210,0.1);
        border-radius: 4px;
        border: 1px solid #4D88D2;
        cursor: pointer;
    }
    .text_overflow {
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        line-height: 14px;
    }
    .img42{
      width: 42px;
      height: 42px;
      object-fit: cover;
      border-radius:50%;
    }
    .allCheck{
        position: absolute;
        right: 10px;
        top: 0;
    }
    .border{
        border: 1px solid #DCDFE6;
        padding: 16px;
        border-radius: 4px;
    }
    .borderLeftpos {
        display: inline-block;
        border-left: 1px solid #E4E7ED;
        height: 100%;
        width: 1px;
        position: absolute;
        left: 50%;
    }
    .optionSearch{
        height:auto;
        padding:5px 20px
    }
    .flex1Text{
        overflow: hidden;
        width: 0;
        margin-right:10px
    }
</style>