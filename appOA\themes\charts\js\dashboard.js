function printGap(gap){
    var str='';
    if(!_.isUndefined(gap)){
        if(Math.abs(gap)>1){
            str +=(gap >0)?'<span class="icon-direction icon-direction-up">':'<span class="icon-direction icon-direction-down">';
            str +=$.number(gap,0);
            str +='</span>';
        }
    }
    return str;
}
function formatMoney(data){
    return $.number(data,2);
}
function getPercent(a,b){
    var _a = parseFloat(a);
    var _b = parseFloat(b);
    if(( _a - _b) > 0 )
        return $.number( ( (_a - _b )/Math.abs(_b) + 1 ) * 100 , 2) + '%'
    else if(_b < 0.0001)
        return ''
    else
        return $.number(100*(parseFloat(a)/parseFloat(b)),2) + '%';
}