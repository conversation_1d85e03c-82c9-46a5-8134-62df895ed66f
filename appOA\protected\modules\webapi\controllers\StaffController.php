<?php

class StaffController extends Controller
{
    public function filters()
    {
        return array(
            'accessControl'
        );
    }

    public function accessRules()
    {
        return array(
//             array('allow',
//                   'actions'=>array('receiveApInfo'),
//                   'ips' => array('127.0.0.1','***************','***************
// '),
//             ),            
//             array('deny',
//                   'actions'=>array('receiveApInfo'),
//                   'ips' => array('*'),
//             ),
        );
    }

	public function actionAuthUser($email='', $pass='')
    {
        $staff = array('<EMAIL>', '<EMAIL>', '<EMAIL>');
        if(in_array($email, $staff)){
            $criter = new CDbCriteria();
            $criter->compare('email', $email);
            $criter->compare('level', '>0');
            $item = User::model()->find($criter);

            if($item){
                if($item->pass == md5($pass)){
                    $this->module->_sendResponse(200, CJSON::encode($item));
                }
                else{
                    $this->module->_sendResponse(404);
                }
            }
            else{
                $this->module->_sendResponse(404);
            }
        }
        else{
            $this->module->_sendResponse(404);
        }
    }

    public function actionReceiveApInfo()
    {
        $data = file_get_contents("php://input");
        $macObj = json_decode($data);
        if(!$macObj){
            echo json_encode(array('state'=>'fail','msg'=>'mac 为空'));
            Yii::app()->end();
        }
        // 对象转化为数组
        $macArr = array();
        foreach ($macObj as $mac) {
            $macArr[] = $mac;
        }

        Yii::import('common.models.staff.*');
        // 将当天所有记录设置为未在线
        $today = strtotime(date('Y/m/d'), time());
        ApconnectRecord::model()->updateAll(
            array('state'=>0), 
            'first_timestamp > :first_timestamp', 
            array(':first_timestamp'=>$today)
        );
        // 查找当前在线员工的 mac 地址资料
        $criteria = new CDbCriteria();
        $criteria->compare('mac_address', $macArr);
        // $criteria->index = 'mac_address';
        $staffMacs = PhoneMac::model()->findAll($criteria);
        foreach ($staffMacs as $staffMac) {
            $criteria = new CDbCriteria();
            $criteria->compare('sid', $staffMac->staff_id);
            $criteria->compare('first_timestamp', '>' . $today);
            if(!$model = ApconnectRecord::model()->find($criteria)){
                $model = new ApconnectRecord();
                $model->first_timestamp = time();
            }
            $model->sid = $staffMac->staff_id;
            $model->last_timestamp = time();
            $model->state = 1;
            if(!$model->save()){
                print_r($model->getErrors());
            } else {
                echo 'staff:' . $model->sid . ",success\r\n";
            }
        }
    }

}
