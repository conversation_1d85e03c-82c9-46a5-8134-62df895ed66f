<?php

class FinanceController extends Controller
{
    public function init()
    {
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";
    }

	public function actionIndex($childid=0)
	{
        $data = array();
        if($childid){
            Yii::import('common.models.invoice.ChildCredit');
            Yii::import('common.models.invoice.InvoiceTransaction');
            $model = ChildProfileBasic::model()->with(array('creditList'=>array('order'=>'creditList.updated_timestamp', 'with'=>'transaction')))->findByPk($childid);
            $data['name'] = $model->getChildName();
            $data['credit'] = $model->credit;
            $data['creditList'] = $model->creditList;
        }
		$this->render('index', array('data'=>$data, 'childid'=>$childid));
	}

    public function actionUpdateCredit()
    {
        if(Yii::app()->request->isPostRequest && Yii::app()->request->isAjaxRequest){
            Yii::import('common.models.invoice.ChildCredit');
            $childid = Yii::app()->request->getParam('childid', 0);
            $amount = ChildCredit::model()->getChildCredit($childid);
            $model = ChildProfileBasic::model()->findByPk($childid);
            if($amount == $model->credit){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '余额相等不必更新！');
            }
            else{
                $model->credit = $amount;
                if($model->save()){
                    $this->addMessage('state', 'success');
                    $this->addMessage('message', '更新成功！');
                }
                else{
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', '更新失败！');
                }
            }
            $this->showMessage();
        }
    }

    public function actionDel()
    {
        if(Yii::app()->request->isPostRequest && Yii::app()->request->isAjaxRequest){
            Yii::import('common.models.invoice.ChildCredit');
            Yii::import('common.models.invoice.InvoiceTransaction');
            $id = Yii::app()->request->getParam('id', 0);
            $model = ChildCredit::model()->findByPk($id);
            if($model){
                $childid = $model->childid;
                if(!$model->transaction){
                    if($model->delete()){
                        $criteria = new CDbCriteria();
                        $criteria->compare('childid', $childid);
                        $criteria->order = 'updated_timestamp desc';
                        $criteria->limit = 1;
                        $lastItem = ChildCredit::model()->find($criteria);
                        $lastItem->balance = ChildCredit::model()->getChildCredit($childid);
                        $lastItem->save();
                        $this->addMessage('state', 'success');
                        $this->addMessage('message', '删除成功');
                    }
                    else{
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', '删除失败');
                    }
                }
                else{
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', '无记录');
                }
            }
            else{
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '无记录');
            }
            $this->showMessage();
        }
    }

    public function actionService()
    {
        $cs = Yii::app()->clientScript;
        $cs->registerCoreScript('jquery.ui');
        $cs->registerCssFile($cs->getCoreScriptUrl().'/jui/css/base/jquery-ui.css');
        $cs->registerScriptFile($cs->getCoreScriptUrl().'/jui/js/jquery-ui.min.js');

        $invoiceid = Yii::app()->request->getParam('invoiceid', 0);
        Yii::app()->language = 'zh_cn';
        if($invoiceid) {
            Yii::import('common.models.invoice.*');
            $criteria = new CDbCriteria();
            $criteria->compare('invoice_id', $invoiceid);
            $serviceInfoModel = ChildServiceInfo::model()->findAll($criteria);
            $invoiceModel = Invoice::model()->findByPk($invoiceid);
        }
        $this->render('service', array(
            'invoiceid' => $invoiceid,
            'invoiceModel' => $invoiceModel,
            'serviceInfoModel' => $serviceInfoModel,

        ));
    }

    public function actionUpdateServiceInfo()
    {
        Yii::import('common.models.invoice.*');
        $invoiceid = Yii::app()->request->getParam('invoiceid', 0);
        $startdate = Yii::app()->request->getParam('startdate', 0);
        $enddate = Yii::app()->request->getParam('enddate', 0);

        if(Yii::app()->request->isPostRequest) {
            if (!$invoiceid || !$startdate || !$enddate) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '缺少参数');
                $this->showMessage();
            }
            $str_startdate = strtotime($startdate);
            $str_enddate = strtotime($enddate);
            if ($str_startdate > $str_enddate) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '开始时间不能大于结束时间');
                $this->showMessage();
            }

            $invoiceModel = Invoice::model()->findByPk($invoiceid);
            if(!$invoiceModel){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '没有找到账单');
                $this->showMessage();
            }
            if ($str_startdate < $invoiceModel->startdate || $str_enddate > $invoiceModel->enddate) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '开始时间和结束时间必须要账单区间');
                $this->showMessage();
            }

            $info = ($invoiceModel->child_service_info) ? unserialize($invoiceModel->child_service_info) : array();

            $criteria = new CDbCriteria();
            $criteria->compare('invoice_id', $invoiceid);
            $criteria->order = 'timestampe DESC';
            $transactionId = InvoiceTransaction::model()->find($criteria);

            $serviceInfo = new ChildServiceInfo();
            $serviceInfo->invoice_id = $invoiceModel->invoice_id;
            $serviceInfo->transaction_id = $transactionId->id;
            $serviceInfo->schoolid = $invoiceModel->schoolid;
            $serviceInfo->classid = $invoiceModel->classid;
            $serviceInfo->childid = $invoiceModel->childid;
            $serviceInfo->yid = $invoiceModel->calendar_id;
            $serviceInfo->payment_type = $invoiceModel->payment_type;
            $serviceInfo->startdate = $str_startdate;
            $serviceInfo->enddate = $str_enddate;
            $serviceInfo->mon = $info && $info['mon'] ? $info['mon'] : '';
            $serviceInfo->tue = $info && $info['tue'] ? $info['tue'] : '';
            $serviceInfo->wed = $info && $info['wed'] ? $info['wed'] : '';
            $serviceInfo->thu = $info && $info['thu'] ? $info['thu'] : '';
            $serviceInfo->fri = $info && $info['fri'] ? $info['fri'] : '';
            if($serviceInfo->save()){
                $this->addMessage('data', array('state' => 'success', 'message' => '增加成功'));
                $this->addMessage('callback', 'cbSuccess');
            }
            else{
                $errs = current($serviceInfo->getErrors());
                $this->addMessage('data', array('state' => 'fail', 'message' => $errs[0]));
            }
            $this->showMessage();
        }
        $model = new ChildServiceInfo();
        $model->invoice_id = $invoiceid;
        $this->renderPartial('info', array(
            'model' => $model,
        ));
    }
}
