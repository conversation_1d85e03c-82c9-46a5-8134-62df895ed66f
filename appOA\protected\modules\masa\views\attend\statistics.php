<style>
    [v-cloak] {
        display: none
    }

    .invoiceLoading {
        background: url(/themes/blue/images/loading.gif) no-repeat scroll center center transparent;
        height: 50px;
    }

    .invertRow {
        display: inline-block;
        border-right: none !important;
        border-top: 1px solid #ddd;
        max-width: 30%;
    }

    .invertRow td, .invertRow th {
        display: block;
        border-top: none !important;
    }

    .innerTableBorderRightNone td {
        border-right: none !important;
    }

    .innerTableBorderLeftNone td {
        border-left: none !important;
    }

    .innerTableBorderLRNone td {
        border-left: none !important;
        border-right: none !important;
    }
</style>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('//mcampus/default/index')) ?></li>
        <li><?php echo CHtml::link(Yii::t('site', 'Routines'), array('//mcampus/default/index')) ?></li>
        <li class="active"><?php echo Yii::t('site', 'ASA Management') ?></li>
    </ol>

    <div class="row">
        <div class="col-md-2 mb10">
            <?php
            $this->widget('zii.widgets.CMenu', array(
                'items' => $this->module->getMenu(),
                'id' => 'pageCategory',
                'htmlOptions' => array('class' => 'nav nav-pills nav-stacked background-gray'),
                'activeCssClass' => 'active',
            ));
            ?>
        </div>
        <div class="col-md-2">
            <?php
            $this->widget('zii.widgets.CMenu', array(
                'items' => $this->module->getAttendSubMenu(),
                'id' => 'pageCategory',
                'htmlOptions' => array('class' => 'nav nav-pills nav-stacked background-gray'),
                'activeCssClass' => 'active',
            ));
            ?>
        </div>
        <div class="col-md-8">
            <?php
            $this->widget('zii.widgets.CMenu', array(
                'items' => $this->module->getAttendMenu(),
                'id' => 'AttendMenu',
                'htmlOptions' => array('class' => 'nav nav-tabs'),
                'activeCssClass' => 'active',
            ));
            ?>
            <!-- Tab panes -->
            <div class="tab-content">

                <div class="row">
                    <div class="col-md-12" id="statisticsVue" v-cloak>
                        <!-- <div class="page-header">
                            <h3>{{year}}年</h3>
                        </div> -->
                        <div class="tab-content pt10">
                            <div class="panel panel-default">
                                <div class="panel-heading">
                                    <h3 class="panel-title" style="display: unset">选择年:</h3>
                                    <select autocomplete="off" class="form-control" style="width: auto;display: unset"
                                            id="selectstartyear" v-model='checkedYear'>
                                        <option value="">请选择年</option>
                                        <option v-for='(mon,idx) in datahead' :value='Object.keys(mon)[0]'>
                                            {{Object.keys(mon)[0]}}
                                        </option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="tab-content pt10" v-if="checkedYear!=''">
                            <!--   <label>请选择月份：</label> -->
                            <div class="panel panel-default">
                                <div class="panel-heading">
                                    <h3 class="panel-title">请选择月份：</h3>
                                </div>
                                <div class="panel-body">
                                    <div class="panel panel-default" v-for='(mon,idx) in datahead'
                                         v-if="checkedYear ==  Object.keys(mon)[0]">
                                        <div class="panel-heading">
                                            <template v-for='(datas,id,key) in mon'>
                                                <h4 class="panel-title">
                                                    <a data-toggle="collapse" data-parent="#accordion"
                                                       :href="'#collapseOne' + id">
                                                        {{id}}年
                                                    </a>
                                                </h4>
                                            </template>
                                        </div>
                                        <div :id="'collapseOne'+Object.keys(mon)[0]" class="panel-collapse collapse in">
                                            <div class="panel-body">
                                                <template v-for='(datas,id,key) in mon'>
                                                    <template v-for='(list,mons) in datas'>
                                                        <button type="button" @click="selectMonth(list)"
                                                                class="btn btn-default mr15 mb15"
                                                                :class="{ 'btn-primary' : month == list}">
                                                            {{list}} <span
                                                                    v-if="moneySum[list]">（¥ {{moneySum[list]}}）</span>
                                                        </button>
                                                    </template>
                                                </template>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- <div class="panel panel-default" v-for='(mon,idx) in datahead'>
                              <div class="panel-heading">
                                  <template v-for='(datas,id,key) in mon'>
                                      {{id}}
                                  </template>
                              </div>
                              <div class="panel-body">
                                 <template v-for='(datas,id,key) in mon'>
                                 <template  v-for='(list,mons) in datas'>
                                    <button type="button" @click="selectMonth(list)" class="btn btn-default mr15"  :class="{ 'btn-primary' : month == list}">
                                       {{list}} <span v-if="moneySum[list]">（¥ {{moneySum[list]}}）</span>
                                    </button>
                                 </template>
                                  </template>
                              </div>
                            </div> -->
                        </div>
                        <h4 v-if="statisticsList == ''">暂无数据</h4>

                        <div class="invoiceLoading" id="invoiceLoadingTop" style="display: none"></div>
                        <div class="panel panel-default" v-for="(slist,cid) in statisticsList"
                             v-if="statisticsList != 1">
                            <div class="panel-heading">{{slist.courseTitle}}</div>
                            <div class="panel-body">
                                <h5 class="">付费人数：
                                    <small>（当前人数：{{slist.number}}）</small>
                                </h5>
                                <h5>已签到教师/机构：<span v-for="attend in teacherAttendance[slist.id]"> {{attend.teacherName}} &nbsp;&nbsp;&nbsp;</span>
                                </h5>

                                <div class="form-inline">
                                    <div class="input-group mb5 mr5" style="width: 180px"
                                         v-for="schedule in slist.schedule"
                                         v-if="slist.schedule">
                                        <span class="input-group-addon">{{dataAddBar(schedule.time)}}</span>
                                        <input type="number" class="form-control" placeholder="上课人数"
                                               v-model="schedule.peopleNum">
                                    </div>
                                </div>
                                <hr/>
                                <div class="btn-group mb5 mr5">
                                    <button class="btn btn-primary" :id="'savePayers' + slist.id"
                                            @click="savePayers(slist.id)">
                                        确认人数
                                    </button>
                                </div>
                                如果在确认人数后老师考勤有变动，系统会以最新考勤数据为准，再次点击确认人数同步页面数据。
                            </div>
                            <div class="invoiceLoading" :id="'invoiceLoading' + slist.id" style="display: none"></div>
                            <div class="">
                                <div class="" v-if="slist.teacherList">
                                    <div class="" style="padding:0 15px;">
                                        <table class="table table-bordered">
                                            <tr>
                                                <td width="100">&nbsp;</td>
                                                <td width="200" v-for="teacher in slist.teacherList">
                                                    <h4>{{teacher.teacher}}
                                                        <small><em style="font-style: normal">
                                                                {{teacher.unit_price}}/人/节</em>
                                                        </small>
                                                    </h4>
                                                </td>
                                            </tr>
                                            <tr v-for="schedule in slist.schedule">
                                                <td>{{dataAddBar(schedule.time)}}</td>
                                                <td v-for="teacher in slist.teacherList">
                                                    <div v-if="teacher.num != null">
                                                        <span v-if="teacher.num.status[schedule.time] == 1 && schedule.staticPeopleNum">出勤 （ ¥ {{teacher.unit_price}} * {{schedule.staticPeopleNum}}人 = ¥ {{parsefloat(schedule.staticPeopleNum) * parsefloat(teacher.unit_price) }} ）</span>
                                                        <span v-else-if="teacher.num.status[schedule.time] == 1">
                                                            出勤
                                                        </span>
                                                        <span
                                                                v-else-if="teacher.num.status[schedule.time] == 2">缺勤</span>
                                                        <span v-else-if="teacher.num.status[schedule.time] == 3 && schedule.staticPeopleNum">替补（ ¥ {{teacher.unit_price}} * {{schedule.staticPeopleNum}}人 = ¥ {{parsefloat(schedule.staticPeopleNum) * parsefloat(teacher.unit_price) }} ）</span>
                                                        <span v-else-if="teacher.num.status[schedule.time] == 3">替补</span>
                                                        <span v-else></span>
                                                    </div>
                                                    <!--                                                    <div v-else>-->
                                                    <!--                                                        <span>缺勤</span>-->
                                                    <!--                                                    </div>-->
                                                </td>
                                            </tr>
                                            <tr v-if="slist.assistant_fee == 10">
                                                <td>助教薪资</td>
                                                <td v-for="teacher in slist.teacherList">
                                                    <div>
                                                        ¥ -{{teacher.assistant_pay}}
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>总计金额</td>
                                                <td v-for="teacher in slist.teacherList">
                                                    <div v-if="teacher.num != null">
                                                        <div v-if="teacher.num.moneyNum != ''">
                                                            ¥ {{teacher.total_amount_x}}
                                                            <span
                                                                    v-if="( parsefloat(teacher.num.unit_salary) * parsefloat(teacher.num.moneyNum) - parsefloat(teacher.assistant_pay) ) != parsefloat(teacher.total_amount)">（当前价格：¥ {{parsefloat(teacher.num.unit_salary) * parsefloat(teacher.num.moneyNum)}}）</span>
                                                        </div>
                                                        <div v-else>
                                                            ¥ {{teacher.total_amount_x}}
                                                        </div>
                                                    </div>
                                                    <div v-else>
                                                        ¥ {{teacher.total_amount_x}}（当前价格：¥ 0）
                                                    </div>
                                                </td>
                                            </tr>
                                            <!--<tr>
                                                <td>调整金额</td>
                                                <td v-for="teacher in slist.teacherList">
                                                    <div class="input-group">
                                                                <span class="input-group-addon"
                                                                      style="padding: 6px;">¥</span>
                                                        <input type="number" placeholder="可输入负数"
                                                               v-model="teacher.repatch_amount"
                                                               :disabled="teacher.status == 1"
                                                               class="form-control input-sm">
                                                    </div>
                                                </td>
                                            </tr>-->
                                            <template v-for="teacher in slist.teacherList">
                                                <tr v-if="teacher.repatch_amount">
                                                    <td>合计金额</td>
                                                    <td>{{parsefloat(teacher.total_amount_x)+
                                                        parsefloat(teacher.repatch_amount) }}
                                                    </td>
                                                </tr>
                                            </template>
                                            <!-- <tr v-for="teacher in slist.teacherList">
                                                <td>合计金额</td>
                                                <td v-for="teacher in slist.teacherList">
                                                    如果有moneyNum就计算moneyNum的值 如果没有就计算total_amount的值--
                                                    <div v-if="teacher.repatch_amount">
                                                        <div v-if="teacher.num != null">
                                                            <div v-if="teacher.num.moneyNum != '' && ( parsefloat(teacher.num.unit_salary) * parsefloat(teacher.num.moneyNum) - parsefloat(teacher.assistant_pay) ) != parsefloat(teacher.total_amount)"> ¥ {{parsefloat(teacher.total_amount) + parsefloat(teacher.repatch_amount)}}
                                                                    <span v-if="( parsefloat(teacher.num.unit_salary) * parsefloat(teacher.num.moneyNum) - parsefloat(teacher.assistant_pay) ) != parsefloat(teacher.total_amount)">（当前价1格：¥ {{parsefloat(teacher.num.unit_salary) * parsefloat(teacher.num.moneyNum) + parsefloat(teacher.repatch_amount)}}）</span>
                                                                <div v-else>
                                                                    ¥ {{parsefloat(teacher.total_amount) + parsefloat(teacher.repatch_amount)}}
                                                                </div>
                                                            </div>
                                                            <div v-else>
                                                                ¥ {{parsefloat(teacher.total_amount) + parsefloat(teacher.repatch_amount)}}
                                                            </div>
                                                        </div>
                                                        <div v-else>
                                                            ¥ {{teacher.total_amount}}（ 当前价格：¥ {{0 + parsefloat(teacher.repatch_amount) }}）
                                                        </div>
                                                    </div>
                                                    <div v-else>
                                                        <div v-if="teacher.num != null">
                                                            <div v-if="teacher.num.moneyNum != ''">
                                                                ¥ {{teacher.total_amount}}
                                                            <span
                                                                v-if="( parsefloat(teacher.num.unit_salary) * parsefloat(teacher.num.moneyNum) - parsefloat(teacher.assistant_pay) ) != parsefloat(teacher.total_amount)">（当前价格：¥ {{parsefloat(teacher.num.unit_salary) * parsefloat(teacher.num.moneyNum) }}）</span>
                                                            </div>
                                                            <div v-else>
                                                                ¥ {{teacher.total_amount}}
                                                            </div>
                                                        </div>
                                                        <div v-else>
                                                            ¥ {{ toFixed( parsefloat(teacher.total_amount) ) }}（ 当前价格：¥ 0）
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr> -->
                                            <!--<tr>
                                                <td>备注</td>
                                                <td v-for="teacher in slist.teacherList">
                                                    <textarea placeholder="备注" v-model="teacher.repatch_reason"
                                                              :disabled="teacher.status == 1"
                                                              class="form-control">
                                                            </textarea>
                                                </td>
                                            </tr>-->
                                            <!--<tr>
                                                <td></td>
                                                <td v-for="teacher in slist.teacherList">
                                                    <div class="row" v-if="slist.status == 1">
                                                        <div class="col-lg-6 col-md-12" v-if="teacher.status == 0">
                                                            <!--<label style="font-weight: normal;"><input type="checkbox"
                                                                                                       v-model="teacher.confirm">
                                                                确认该列表无误,提交后不可修改
                                                            </label>--
                                                            <br/>
                                                            <button
                                                                v-if="teacher.num != null && teacher.num.moneyNum != ''"
                                                                class="btn btn-primary btn-xs btn-block mb5 mr5"
                                                                :id="'submitTeacher'+teacher.id"
                                                                @click="submitTeacher(slist.id,teacher.id,parsefloat(teacher.num.unit_salary) * parsefloat(teacher.num.moneyNum))"/>
                                                            保存</button>
                                                            <button v-else-if="teacher.num != null"
                                                                    class="btn btn-primary btn-xs btn-block mb5 mr5"
                                                                    :id="'submitTeacher'+teacher.id"
                                                                    @click="submitTeacher(slist.id,teacher.id, '')"/>
                                                            保存</button>
                                                            <button v-else
                                                                    class="btn btn-primary btn-xs btn-block mb5 mr5"
                                                                    :id="'submitTeacher'+teacher.id"
                                                                    @click="submitTeacher(slist.id,teacher.id, 99)"/>
                                                            保存</button>
                                                        </div>
                                                        <div class="col-lg-6 col-md-12" v-else-if="teacher.status == 1">
                                                            <h5>已提交</h5>
                                                        </div>
                                                        <div class="col-lg-6 col-md-12" v-else>
                                                            <h5>已发放</h5>
                                                        </div>
                                                    </div>
                                                    <div v-else>上课人数填写完整才可以保存</div>
                                                </td>
                                            </tr>-->
                                        </table>
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script type="text/x-template" id="m-Template">
        <select class="form-control input-sm mb5" :disabled="teacher.status == 1" v-model="teacher.settle_month">
            <option value="-1" selected>请选择</option>
            <option v-for="(mon,val) in settleMonths" :value="val">{{mon}}</option>
        </select>
    </script>
    <script>
        var monthList = <?php echo CJSON::encode($month); ?>;
        var moneySum = <?php echo CJSON::encode($moneySum); ?>;
        //        var monthComponent = {
        //            template: '#m-Template',
        //            data: function () {
        //                return {
        //                    teacher: {
        //                        status: 0
        //                    },
        //                    settleMonths: <?php //echo CJSON::encode($settleMonths); ?>
        //                }
        //            }
        //        };


        var date = new Date;
        var statisticsVueModel = new Vue({
            el: "#statisticsVue",
            data: {
                statisticsList: 1,//分成统计数据
                monthList: monthList,
                moneySum: moneySum,
                teacherAttendance:<?php echo CJSON::encode($teacherAttendance); ?>,
                settleMonths: <?php echo CJSON::encode($settleMonths); ?>,
                month: "",
                year: date.getFullYear(),
                datahead: '',
                checkedYear: '',
            },
            created: function () {
                var data = []
                for (key in monthList) {
                    data.push(monthList[key].substr(0, 4))
                }
                var r = data.filter(function (element, index, self) {
                    return self.indexOf(element) === index;
                });

                var mondata = []
                for (var i = 0; i < r.length; i++) {
                    var keys = []
                    var monlist = {}
                    for (key in monthList) {
                        if (monthList[key].indexOf(r[i]) != -1) {
                            keys.push(monthList[key])
                            monlist[r[i]] = keys
                        }
                    }
                    mondata.push(monlist)

                }
                this.datahead = mondata
                console.log(r)
                this.checkedYear = r[0]
            },
            updated: function () {

            },
            methods: {
                isEmptyObj: function (obj) {
                    if (Object.keys(obj).length > 0) {
                        return true;
                    }
                    return false;
                },
                addedNum: function (num) {
                    var _num;
                    if (num < 10) {
                        _num = '0' + num;
                    } else {
                        _num = num
                    }
                    return _num
                },
                arrLength: function (arr) {
                    return Object.keys(arr).length;
                },
                parsefloat: function (num) {
                    return parseFloat(num);
                },
                toFixed: function (num) {
                    return num.toFixed(2);
                },
                dataAddBar: function (data) {
                    var _data = '';
                    $.each(data, function (i, d) {
                        if (i == 4 || i == 6) {
                            _data += '-' + d;
                        } else {
                            _data += d;
                        }
                    });
                    return _data;
                },
                selectMonth: function (month) {
                    this.month = month;
                    var _this = this;
                    $('#invoiceLoadingTop').fadeIn();
                    $.ajax({
                        url: '<?php echo $this->createUrl('statistics');?>',
                        type: 'POST',
                        timeout: 5000,
                        data: {timeData: month},
                        dataType: 'json'
                    }).done(function (data) {
                        console.log(data)
                        $('#invoiceLoadingTop').hide();
                        _this.statisticsList = data.course;
                        if (_this.isEmptyObj(data.courseNumber)) {  //保存过的天数存入模型
                            for (key in data.course) {
                                $.each(data.courseNumber, function (i, courseNumber) {
                                    if (i == key) {
                                        $.each(courseNumber, function (_i, schedule) {
                                            for (datakey in data.course[key].schedule) {
                                                if (_i == datakey) {
                                                    _this.statisticsList[key].schedule[_i]['peopleNum'] = schedule['peopleNum'];           //更改人数影响该数组
                                                    _this.statisticsList[key].schedule[_i]['staticPeopleNum'] = schedule['peopleNum'];    //更改人数不会影响该数组
                                                }
                                            }
                                        });
                                    }
                                });
                            }
                        }
                        if (_this.isEmptyObj(data.teacher)) {           //已保存过的老师存入模型
                            $.each(data.teacher, function (i, teachers) {
                                $.each(teachers, function (_i, teacher) {
                                    for (key in data.course) {
                                        if (i == key) {
                                            if (_this.statisticsList[i]['teacherList']) {
                                                Vue.set(_this.statisticsList[i]['teacherList'], _i, teacher)
                                            } else {
                                                Vue.set(_this.statisticsList[i], 'teacherList', {});
                                                Vue.set(_this.statisticsList[i]['teacherList'], _i, teacher)
                                            }
                                        }
                                    }

                                });
                            });
                        }
                        _this.teacherAttendance = data.teacherAttendance
                    }).fail(function (data) {
                        $('#invoiceLoadingTop').hide();
                        alert(data.message)
                    });
                }, savePayers: function (cid) {
                    $('#invoiceLoading' + cid).fadeIn();
                    $('#savePayers' + cid).attr('disabled', 'disabled');
                    var _this = this;
                    var _date = {};
                    $.each(_this.statisticsList[cid].schedule, function (i, schedule) {
                        if (schedule.peopleNum) {
                            _date[schedule.time] = schedule.peopleNum;
                            schedule.staticPeopleNum = schedule.peopleNum;
                        } else {
                            _date[schedule.time] = 0;
                        }
                    });
                    $.ajax({
                        url: '<?php echo $this->createUrl('updateThirdpart');?>',
                        type: 'POST',
                        timeout: 5000,
                        data: {timeNumberArr: _date, courseId: cid},
                        dataType: 'json'
                    }).done(function (data) {
                        if (data.state == 'success') {
                            $('#invoiceLoading' + cid).hide();
                            Vue.set(_this.statisticsList[cid], 'status', data.data.status);
                            $.each(data.data.moneySum, function (i, price) {
                                Vue.set(_this.moneySum, i, price)
                            });
                            Vue.set(_this.statisticsList[cid], 'teacherList', data.data['teacher'][cid])
                        } else {
                            resultTip({error: 'warning', "msg": data.message});
                            $('#invoiceLoading' + cid).hide();
                        }
                        $('#savePayers' + cid).removeAttr('disabled', 'disabled');
                    }).fail(function (data) {
                        $('#invoiceLoading' + cid).hide();
                        alert(data.message);
                        $('#savePayers' + cid).removeAttr('disabled', 'disabled');
                    });
                },
                submitTeacher: function (cid, _teacherId, newTotalAmount) {
                    var _this = this, _flag = true;
                    var data = {};
                    $('#submitTeacher' + _teacherId).attr('disabled', 'disabled');
                    $.each(_this.statisticsList[cid]['teacherList'], function (i, teacher) {
                        if (teacher.id == _teacherId) {
                            if (teacher.repatch_reason) {
                                data.repatch_reason = teacher.repatch_reason;
                            }
                            if (teacher.repatch_amount > 0) {
                                data.repatch_amount = teacher.repatch_amount;
                            }
                            /*if (teacher.settle_month != -1) {
                                Vue.set(_this.statisticsList[cid]['teacherList'][i], 'failInfo', '');
                                data.settle_month = teacher.settle_month;
                            } else {
                                Vue.set(_this.statisticsList[cid]['teacherList'][i], 'failInfo', '请选择要保存至几月');
                                _flag = false;
                            }*/
                            if (teacher.confirm) {
                                data.status = teacher.confirm;
                            }
                            data.thirdpartReportId = _teacherId;
                            if (newTotalAmount == 99) {
                                data.newTotalAmount = 0;
                            } else if (newTotalAmount != '') {
                                data.newTotalAmount = newTotalAmount;
                            } else {
                                data.newTotalAmount = teacher.total_amount_x;
                            }
                        }
                    });
                    if (!_flag) {
                        $('#submitTeacher' + _teacherId).removeAttr('disabled');
                        return false
                    }
                    $.ajax({
                        url: '<?php echo $this->createUrl('revisionThirdpart');?>',
                        type: 'POST',
                        timeout: 5000,
                        data: data,
                        dataType: 'json'
                    }).done(function (_data) {
                        if (_data.state == 'success') {
                            Vue.set(_this.statisticsList[cid]['teacherList'][_data.data.status.id], 'total_amount_x', data.newTotalAmount);
                            if (newTotalAmount != 99) {
                                Vue.set(_this.statisticsList[cid]['teacherList'][_data.data.status.id]['num'], 'moneyNum', '');
                            }
                            Vue.set(_this.statisticsList[cid]['teacherList'][_data.data.status.id], 'status', _data.data.status.status);
                            $.each(_data.data.money, function (i, price) {
                                Vue.set(_this.moneySum, i, price)
                            });
                            resultTip({"msg": "保存成功"});
                        } else {
                            resultTip({error: 'warning', "msg": _data.message})
                        }
                        $('#submitTeacher' + _teacherId).removeAttr('disabled');
                    }).fail(function (data) {
                        resultTip({error: 'warning', "msg": "保存失败"});
                        $('#submitTeacher' + _teacherId).removeAttr('disabled');
                    });
                },
//                deleteTeacher: function (cid, _teacherId) {
//                    var _this = this;
//                    $.ajax({
//                        url: '<?php //echo $this->createUrl('deleteReport');?>//',
//                        type: 'POST',
//                        timeout: 5000,
//                        data: {thirdpartReportId: _teacherId},
//                        dataType: 'json'
//                    }).done(function (data) {
//                        if (data.state == 'success') {
//                            Vue.delete(_this.statisticsList[cid]['teacherList'], _teacherId);
//                            resultTip({"msg": "删除成功"})
//                        } else {
//
//                        }
//                    }).fail(function (data) {
//                        resultTip({error: 'warning',"msg": "删除失败"})
//                    });
//                }
            }
//            , components: {
//                'my-component': monthComponent
//            }
        });

    </script>
<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>