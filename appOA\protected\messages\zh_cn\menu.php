<?php
/**
 * Message translations.
 *
 * This file is automatically generated by 'yiic message' command.
 * It contains the localizable messages extracted from source code.
 * You may modify this file by translating the extracted messages.
 *
 * Each array element represents the translation (value) of a message (key).
 * If the value is empty, the message is considered as not translated.
 * Messages that no longer need translation will have their translations
 * enclosed between a pair of '@@' marks.
 *
 * Message string can be used with plural forms format. Check i18n section
 * of the guide for details.
 *
 * NOTE, this file must be saved in UTF-8 encoding.
 *
 * @version $Id: $
 */
return array (
  '孩子前台预览' => '',
  '孩子管理' => '',
  '家长会议记录' => '',
  '积分兑换' => '',
  '积分兑换配置' => '',
  '管理多校园' => '',
  '预约参观管理' => '',
  'Configurations' => '配置',
  'Operations' => '运营管理',
  'Not Yet - child has not yet demonstrated indicator' => '未显现 – 幼儿未有此行为',
  'In Process - child demonstrates indicator intermittently' => '发展中 – 幼儿偶有此行为',
  'Proficient - child can reliably demonstrate indicator' => '熟练 – 幼儿确有此行为',
);
