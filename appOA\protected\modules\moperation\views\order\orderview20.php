<h4><?php echo $v;?></h4>
<?php $form=$this->beginWidget('CActiveForm', array(
    'id'=>'form-'.$s,
    'action' => $this->createUrl('pack', array('branchId'=>$branchId)),
    'enableAjaxValidation'=>false,
    'htmlOptions'=>array('class'=>'J_ajaxForm'),
)); ?>
<div class="J_check_wrap table-responsive">
	<table width="100%" id="J_table_list" class="table table-hover">
		<colgroup>
			<col width="50">
			<col width="50">
			<col width="300">
			<col width="150">
			<col width="150">
			<col width="150">
			<col>
		</colgroup>
		<thead>
			<tr>
				<th><?php echo CHtml::checkBox('chall', false, array('data-checklist'=>'J_check_c1', 'class'=>'J_check_all', 'data-direction'=>'y'));?></th>
                <th>数量</th>
				<th>商品</th>
				<th>孩子</th>
				<th>学校</th>
				<th><?php echo Yii::t('labels', 'Created Time');?></th>
				<th>创建人</th>
			</tr>
		</thead>
		<tbody>
			<?php foreach ($sData as $order):?>
			<tr>
				<td><?php echo CHtml::checkBox('orderid[]', false, array('value'=>$order->id,'data-yid'=>'J_check_c1', 'class'=>'J_check'));?></td>
                <td><?php echo $order->quantity?></td>
				<td><?php echo $order->Product->getContent()?></td>
				<td><?php echo $order->ChildProfile->getChildName()?></td>
				<td><?php echo $order->school->title?></td>
				<td><?php echo OA::formatDateTime($order->created_timestamp, "medium", "short")?></td>
				<td><?php echo $order->orderUser->getName()?></td>
			</tr>
			<?php endforeach; ?>
		</tbody>

		</table>
</div>
<div class="">
    <button class="btn btn-primary J_ajax_submit_btn <?php if (!$this->oAdmin):?>disabled<?php endif;?>" <?php if (!$this->oAdmin):?>disabled<?php endif;?>>打包</button>
</div>
<?php $this->endWidget(); ?>
