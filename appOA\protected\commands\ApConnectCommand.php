<?php
class ApConnectCommand extends CConsoleCommand{
    // 所有AP的信息
    public $apInfo = array(
        'groupAp2' => array(
            'loginType' => 'basic',
            'username' => 'admin',
            'pwd' => 'barney',
            'loginUrl' => 'http://192.168.0.246',
            'url' => 'http://192.168.0.246/html/sClientInfo_table.htm',
        ),
        'groupAp1' => array(
            'loginType' => 'basic',
            'username' => 'admin',
            'pwd' => 'barney',
            'loginUrl' => 'http://192.168.0.247',
            'url' => 'http://192.168.0.247/cgi-bin/stalist.html',
        ),
        '3f' => array(
            'loginType' => 'cookie',
            'username' => 'admin',
            'pwd' => 'barney',
            'loginUrl' => 'http://192.168.0.51/login.php',
            'url' => 'http://192.168.0.51/st_info.php',
        ),
        '2005' => array(
            'loginType' => 'cookie',
            'username' => 'admin',
            'pwd' => 'barney',
            'loginUrl' => 'http://192.168.0.248/login.php',
            'url' => 'http://192.168.0.248/st_info.php',
        ),
        'ivygroup' => array(
            'loginType' => 'cookie',
            'username' => 'admin',
            'pwd' => 'barney',
            'loginUrl' => 'http://192.168.0.1/Web/login',
            'url' => 'http://192.168.0.1/wcn/device/get.x?uid=',
        ),
    );

    public function init(){
        Yii::import('common.staff.*');
	}
    
    public function run(){
        Yii::import('common.models.staff.*');
        
        // 获取当前所有的 mac 地址
        $macArr = array();

        // foreach ($this->apInfo as $apname => $ap) {
        //     if ($ap['loginType'] == 'basic'){
        //         $result = $this->basicAuth($ap['url'], $ap['username'], $ap['pwd']);
        //         $result = $this->basicAuth($ap['url'], $ap['username'], $ap['pwd']);
        //     }      
        //     if ($ap['loginType'] == 'cookie'){
        //         $result = $this->cookieAuth($ap['loginUrl'] ,$ap['url'], $ap['username'], $ap['pwd']);
        //         $result = $this->cookieAuth($ap['loginUrl'] ,$ap['url'], $ap['username'], $ap['pwd']);
        //     }
        //     $macArr = array_merge($macArr, current($result));
        // }

        // ivy group 
        $macArr = $this->ivygroupAuth($this->apInfo['ivygroup']['loginUrl'], $this->apInfo['ivygroup']['url'], $this->apInfo['ivygroup']['username'], $this->apInfo['ivygroup']['pwd']);
        $macArr = array_unique($macArr);
        foreach ($macArr as $v=>$mac) {
            $mac = strtoupper(str_replace('-', '', $mac));
            $macArr[$v] = implode(':', str_split($mac, 2));
        }
        // 查找当前在线的员工
        if ($macArr) {
            // post 到线上
            $this->posToOnline($macArr);
            Yii::app()->end();
            $today = strtotime(date('Y/m/d'), time());
            $criteria = new CDbCriteria();
            $criteria->compare('mac_address', $macArr);
            $criteria->index = 'mac_address';
            $staffMacs = PhoneMac::model()->findAll($criteria);
            foreach ($macArr as $mac) {
                if (isset($staffMacs[$mac])) {
                    $criteria = new CDbCriteria();
                    $criteria->compare('sid', $staffMacs[$mac]->id);
                    $criteria->compare('first_timestamp', '>' . $today);
                    if(!$model = ApconnectRecord::model()->find($criteria)){
                        $model = new ApconnectRecord();
                        $model->first_timestamp = time();
                    }
                    $model->sid = $staffMacs[$mac]->id;
                    $model->last_timestamp =  time();
                    $model->state = 1;
                    if(!$model->save()){
                        print_r($model->getErrors());
                    } else {
                        echo $model->id;
                    }
                }
            }
        }
    }   

    // basicAuth
    public function basicAuth($url, $username, $pwd)
    {
        // 设置cookie
        $cookie = 'Cookie:RpWebID=3c9d1234';
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
        curl_setopt($ch, CURLOPT_USERPWD, "$username:$pwd");
        curl_setopt($ch, CURLOPT_COOKIE, $cookie);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        // 获取 MAC
        $curl = curl_init($url);
        curl_setopt($curl, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
        curl_setopt($curl, CURLOPT_USERPWD, "$username:$pwd");
        curl_setopt($curl, CURLOPT_COOKIE, $cookie);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);

        $content = curl_exec($curl);
        curl_close($ch);
        curl_close($curl);

        // 匹配出MAC地址
        $matches = array();
        preg_match_all('/\w\w:\w\w:\w\w:\w\w:\w\w:\w\w/', $content, $matches);

        return $matches;
    }    

    // cookieAuth
    public function cookieAuth($loginUrl, $url, $username, $pwd)
    {
        // 获取 cookie 权限
        $post = "ACTION_POST=LOGIN&LOGIN_USER=$username&LOGIN_PASSWD=$pwd";
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $loginUrl);
        curl_setopt($ch, CURLOPT_HEADER, 1);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        //
        curl_setopt($ch, CURLOPT_COOKIEJAR, 'Cookie:dlink_uid=abcd');
        curl_setopt($ch, CURLOPT_POST,1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $post);
        $content = curl_exec($ch);
        curl_close($ch);
        // 获取 mac 信息
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch,CURLOPT_COOKIE,'Cookie:dlink_uid=abcd');
        $content = curl_exec($ch);
        curl_close($ch);

        // 匹配出MAC地址
        $matches = array();
        preg_match_all('/\w\w:\w\w:\w\w:\w\w:\w\w:\w\w/', $content, $matches);

        return $matches;
    }

    // ivygroup auth
    public function ivygroupAuth($loginUrl, $url, $username, $pwd)
    {
        // 获取 cookie 权限
        $post = "user_name=$username&password=$pwd";
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $loginUrl);
        curl_setopt($ch, CURLOPT_HEADER, 1);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        //
        curl_setopt($ch, CURLOPT_COOKIEJAR, 'Cookie:dlink_uid=abcd');
        curl_setopt($ch, CURLOPT_POST,1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $post);
        $content = curl_exec($ch);
        curl_close($ch);
        $matches = array();
        preg_match_all('/<uid>(\w+)<\/uid>/', $content, $matches);
        $uid = $matches[1][0];

        $url = $url . $uid . '&xml=3c726571756573743e3c636f6d6d616e6420616374696f6e3d226765742d6e6578742220636f756e743d2230222f3e3c646174613e3c636c69656e74696e666f3e3c636c69656e74696e666f3e3c636c69656e74646973702d7461626c653e3c6d6163616464722f3e3c61706e616d652f3e3c737469642f3e3c737369642f3e3c766c616e69642f3e3c6970616464722f3e3c716f737175616c6974792f3e3c726f616d666c61672f3e3c61757468666c61672f3e3c2f636c69656e74646973702d7461626c653e3c2f636c69656e74696e666f3e3c2f636c69656e74696e666f3e3c2f646174613e3c2f726571756573743e&info=';
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_HEADER, 1);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST,1);
        $content = curl_exec($ch);
        curl_close($ch);
        $matches = array();
        preg_match_all('/\w{4}-\w{4}-\w{4}/', $content, $matches);
        return $matches[0];
    }

    // 发送 mac 信息到线上
    public function posToOnline($data)
    {
        if($data) {
            $post = json_encode($data);
            $url = 'http://apps.ivyonline.cn/webapi/staff/receiveApInfo';
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $post);
            // curl_setopt($ch, CURLOPT_HEADER, 1);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER , 1);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_POST, 1);
            $result = curl_exec($ch);
            curl_close($ch);
            print_r($result);
        }
    }
}
?>
