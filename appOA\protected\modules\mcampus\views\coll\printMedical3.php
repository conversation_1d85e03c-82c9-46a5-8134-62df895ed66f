<?php
$medicalCn = '';
$medicalEn = '';
$filedata = '';
$data = '';
if($model){
    $data = json_decode($model->data);
    $filedata = ($data->filedata) ? $data->filedata : '' ;
}
?>
<style>
.printBox{
    width:860px;
    margin:0 auto
}
.page{
    margin-top:20px
}
img{
    margin-top:10px;
    max-width:100%;
    height:auto
}
.sign{
    float: right;
    margin-right:20px;
    margin-bottom:20px;
    max-height: 15mm;
}
.sign img{
    width:200px;
    height: 110px;
}
@media print {
    .print_img{
        max-height: 290mm;
        display: block;
        margin: 0 auto;
        page-break-after:always;
    }
    .print_protocol_img
    {
        max-height: 290mm;
        display: block;
        margin: 0 auto;
    }
}
.always{
    page-break-after: always;
}
.always:last-child{
    page-break-after: auto;
}
</style>
<div class="printBox">
    <div class ="always">
        <table class="table">
            <tbody>
            <tr>
                <th  width="25%"><?php echo Yii::t('reg', 'Name') ?></th>
                <td colspan="3"><?php echo $child_info->getChildName() ?></td>
            </tr>
            <tr>
                <th><?php echo Yii::t('labels', 'Date of Birth') ?>期</th>
                <td colspan="3"><?php echo $child_info->birthday_search ?></td>
            </tr>
            <tr>
                <th><?php echo Yii::t('labels', 'Class') ?></th>
                <td colspan="3"><?php echo $child_info->ivyclass->title?></td>
            </tr>
            <?php foreach ($step_data['intended_hospitals'] as $k=>$v){?>
                <tr>
                    <th><?php echo Yii::t('reg', 'Preferred Hospital') ?> <?php echo $k+1?></th>
                    <td colspan="3"><?php echo $v?></td>
                </tr>
            <?php }?>
            <tr>
                <th><?php echo Yii::t('reg', 'Personal Medical Insurance Information') ?></th>
                <td colspan="3"><?php echo $step_data['insurance_companies']?></td>
            </tr>
            <?php foreach ($step_data['emergency_contact'] as $k=>$v){?>
                <tr>
                    <th><?php echo Yii::t('site', 'Emergency Contact') ?> <?php echo $k+1?></th>
                    <td colspan="3"><?php echo $v['name']?></td>
                </tr>
                <?php if(!empty($v['mobile'])){?>
                    <tr>
                        <th><?php echo Yii::t('user', 'Emergency Contact Phone') ?> <?php echo $k+1?></th>
                        <td colspan="3"><?php echo $v['mobile']?></td>
                    </tr>
                <?php }?>

                <?php if(!empty($v['email'])){?>
                    <tr>
                        <th><?php echo Yii::t('user', 'Emergency Contact Email') ?> <?php echo $k+1?></th>
                        <td colspan="3"><?php echo $v['email']?></td>
                    </tr>
                <?php }?>

            <?php }?>
            <tr>
                <th><?php echo Yii::t('reg', 'ADD/ADHD') ?></th>
                <td><?php echo $step_data['is_adhd']==1 ?  "是":  "否" ?></td>
                <th width="20%"><?php echo Yii::t('reg', 'Heart Disorder') ?></th>
                <td><?php echo $step_data['is_heart_disease']==1 ?  "是":  "否" ?></td>
            </tr>
            <tr>
                <th><?php echo Yii::t('reg', 'Tuberculosis') ?></th>
                <td><?php echo $step_data['is_tuberculosis']==1 ?  "是":  "否" ?></td>
                <th width="20%"></th>
                <td></td>
            </tr>
            <tr>
                <th><?php echo Yii::t('reg', 'Allergies(food,medications,etc.)') ?></th>
                <td><?php echo $step_data['allergies']==1 ? "是":  "否"?></td>
                <th width="20%"><?php echo Yii::t('reg', 'Allergy') ?></th>
                <?php if($step_data['allergies']==1){?>
                    <td><?php echo $step_data['allergies_food'] .PHP_EOL. $step_data['allergies_medication'].PHP_EOL.$step_data['allergies_other']?></td>
                <?php }else{?>
                    <td>无</td>
                <?php }?>
            </tr>
            <tr>
                <th><?php echo Yii::t('reg', 'Fracture Dislocate') ?></th>
                <td><?php echo $step_data['fracture_dislocate'] == 1 ? "是": "否"?></td>
                <th width="20%"></th>
                <td></td>
            </tr>
            <tr>
                <th> <?php echo Yii::t('reg', 'Hyper pyretic convulsion') ?> </th>
                <td colspan="3">
                    <?php echo $step_data['pyretic_convulsion'] == 1 ? "是": "否"?>
                </td>
            </tr>
            <tr>
                <th> <?php echo Yii::t('reg', 'Other Illnesses (please specify)') ?> </th>
                <td colspan="3"><?php echo $step_data['other']?></td>
            </tr>
            </tbody>
        </table>
    </div>
    <?php if(isset($step_data['extraInfo']['protocol']) && !empty($step_data['extraInfo']['protocol'])){?>
    <div class='always'></div>
    <!--    协议-->
    <div class="">
        <?php $protocol_num = count($step_data['extraInfo']['protocol'])?>
        <?php foreach ($step_data['extraInfo']['protocol'] as $k=>$v){?>
            <div>
                <?php echo $child_info->getChildName() ?>
                <?php echo $child_info->ivyclass->title?>
            </div>
            <p>
                <img  class="img-responsive print_protocol_img" src="<?php echo $v; ?>">
            </p>
            <?php if(($k+1)<$protocol_num){?>
                <div class='always'></div>
            <?php }?>
            <?php if(($k+1)==$protocol_num){?>
                <?php if(!empty($step_data['childStatus']['sign_url'])){?>
                <div class="sign">
                    <img src="<?php echo $step_data['childStatus']['sign_url']?>" alt="" >
                </div>
                <?php }?>
                <div class='always'></div>
            <?php }?>
        <?php }?>

    </div>
    <?php }?>
    
        <!--        体检报告-->
        <?php if($step_data['examination_reports']){?>
            <div class="">
            <?php foreach ($step_data['examination_reports'] as $val){?>
                <div>
                    <?php echo $child_info->getChildName() ?>
                    <?php echo $child_info->ivyclass->title?>
                </div>
                <p class="pageBreak">
                    <img  class="img-responsive print_img" src="<?php echo $val; ?>">
                </p>
            <?php }?>
            </div>
        <?php }?>
        <!--        疫苗-->
        <?php if($step_data['vaccines_reports']){?>
            <div class="">
            <?php foreach ($step_data['vaccines_reports'] as $val){?>
                <div>
                    <?php echo $child_info->getChildName() ?>
                    <?php echo $child_info->ivyclass->title?>
                </div>
                <p class="pageBreak">
                    <img class="img-responsive print_img" src="<?php echo $val; ?>">
                </p>
            <?php   } ?>
            </div>
        <?php }?>
        <!--        保险-->
        <?php if($step_data['insurance_card_photos']){?>
            <div class="">
            <?php foreach ($step_data['insurance_card_photos'] as $k=>$val){?>
                <div>
                    <?php echo $child_info->getChildName() ?>
                    <?php echo $child_info->ivyclass->title?>
                </div>
                <p class="pageBreak">
                    <img  style="width: 90%" class="img-responsive print_img" src="<?php echo $val; ?>">
                </p>
            <?php   }?>
            </div>
        <?php }?>
</div>