<?php

/**
 * This is the model class for table "ivy_testimonials".
 *
 * The followings are the available columns in table 'ivy_testimonials':
 * @property integer $id
 * @property string $schoolid
 * @property integer $classid
 * @property integer $childid
 * @property string $title_cn
 * @property string $title_en
 * @property string $summary_cn
 * @property string $summary_en
 * @property string $content_cn
 * @property string $content_en
 * @property string $avatar
 * @property string $thumb_avatar
 * @property integer $is_graduated
 * @property integer $place
 * @property integer $status
 * @property integer $update_user
 * @property integer $create_timestamp
 * @property integer $update_timestamp
 */
class Testimonials extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return Testimonials the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_testimonials';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('schoolid', 'required'),
			array('classid, childid, is_graduated, place, status, update_user, create_timestamp, update_timestamp', 'numerical', 'integerOnly'=>true),
			array('schoolid', 'length', 'max'=>25),
			array('title_cn, title_en, avatar, thumb_avatar', 'length', 'max'=>100),
			array('summary_cn, summary_en', 'length', 'max'=>1000),
			array('content_cn, content_en', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, schoolid, classid, childid, title_cn, title_en, summary_cn, summary_en, content_cn, content_en, avatar, thumb_avatar, is_graduated, place, status, update_user, create_timestamp, update_timestamp', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'schoolid' => 'Schoolid',
			'classid' => 'Classid',
			'childid' => 'Childid',
			'title_cn' => 'Title Cn',
			'title_en' => 'Title En',
			'summary_cn' => 'Summary Cn',
			'summary_en' => 'Summary En',
			'content_cn' => 'Content Cn',
			'content_en' => 'Content En',
			'avatar' => 'Avatar',
			'thumb_avatar' => 'Thumb Avatar',
			'is_graduated' => 'Is Graduated',
			'place' => 'Place',
			'status' => 'Status',
			'update_user' => 'Update User',
			'create_timestamp' => 'Create Timestamp',
			'update_timestamp' => 'Update Timestamp',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('classid',$this->classid);
		$criteria->compare('childid',$this->childid);
		$criteria->compare('title_cn',$this->title_cn,true);
		$criteria->compare('title_en',$this->title_en,true);
		$criteria->compare('summary_cn',$this->summary_cn,true);
		$criteria->compare('summary_en',$this->summary_en,true);
		$criteria->compare('content_cn',$this->content_cn,true);
		$criteria->compare('content_en',$this->content_en,true);
		$criteria->compare('avatar',$this->avatar,true);
		$criteria->compare('thumb_avatar',$this->thumb_avatar,true);
		$criteria->compare('is_graduated',$this->is_graduated);
		$criteria->compare('place',$this->place);
		$criteria->compare('status',$this->status);
		$criteria->compare('update_user',$this->update_user);
		$criteria->compare('create_timestamp',$this->create_timestamp);
		$criteria->compare('update_timestamp',$this->update_timestamp);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}