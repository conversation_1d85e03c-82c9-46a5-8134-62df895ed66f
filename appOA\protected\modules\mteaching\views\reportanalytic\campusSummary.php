<style>
    .size {
        font-size: 18px
    }
    .checkbox-custom {
        position: relative;
        margin-bottom: 7px;
        margin-top: 0;
        display: inline-block;
        border: 1px solid #428BCA;
        border-radius: 3px;
        margin-right: 10px
    }
    /*
将初始的checkbox的样式改变
*/
    
    .checkbox-custom input[type="checkbox"] {
        opacity: 0;
        /*将初始的checkbox隐藏起来*/
        position: absolute;
        cursor: pointer;
        z-index: 2;
        margin: -6px 0 0 0;
        top: 50%;
        left: 3px;
        margin-left: 10px;
    }
    /*
设计新的checkbox，位置
*/
    
    .checkbox-custom label:before {
        content: '';
        position: absolute;
        top: 50%;
        left: 0;
        margin-top: -8px;
        width: 19px;
        height: 17px;
        display: inline-block;
        border-radius: 2px;
        border: 1px solid #bbb;
        background: #fff;
        margin-left: 10px;
    }
    /*
点击初始的checkbox，将新的checkbox关联起来
*/
    
    .checkbox-custom input[type="checkbox"]:checked+label:after {
        position: absolute;
        display: inline-block;
        font-family: 'Glyphicons Halflings';
        content: "\e013";
        top: 42%;
        left: 3px;
        margin-top: -6px;
        font-size: 11px;
        width: 19px;
        height: 18px;
        color: #428BCA;
        border: 1px solid #428BCA;
        text-align: center;
        line-height: 14px;
        border-radius: 2px;
        background: #fff;
        margin-left: 7px;
    }
    
    .checkbox-custom input[type="checkbox"]:checked+label {
        color: #428BCA;
    }
    
    .checkbox-custom label {
        cursor: pointer;
        line-height: 1.2;
        font-weight: normal;
        /*改变了rememberme的字体*/
        margin-bottom: 0;
        text-align: left;
        padding: 10px 10px 10px 40px;
    }
    .table>tbody+tbody {
        border-top: none;
    }
    
    [v-cloak] {
        display: none;
    }
    
    .dataheight {
        overflow: auto;
    }
    
    .panel-heading+.list-group .list-group-item:first-child {
        border-top-width: 1px;
    }
    
    .panel {
        margin-bottom: 0
    }
    .checkbox{
        margin: 0
    }
</style>

<div class="container-fluid">
    <ol class="breadcrumb">
        <li>
            <?php echo Yii::t('user','Report Card Analytic Reports');?>
        </li>
        <li>
            <?php echo Yii::t('user','Campus Summary Reports') ?>
        </li>
    </ol>
    <div class="row">
        <div class="col-md-2 mb15">
            <?php
            $this->widget('zii.widgets.CMenu', array(
                'items'  => $this->module->getMenu(),
                'id'   => 'pageCategory',
                'htmlOptions'  => array('class'   => 'nav nav-pills nav-stacked background-gray'),
                'activeCssClass'  => 'active',
            ));
            ?>
        </div>
        <div class="col-md-10" id="datalist" v-cloak>

            <div class="">
                <div class="mb15">

                    <div class="btn-group wid">
                        <button type="button" class="btn btn-default dropdown-toggle  wid" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            {{calenderList[yid]}}<span class="caret"></span>
                        </button>
                        <ul class="dropdown-menu wid">
                            <li v-for="(val, key, index) in calenderList">
                                <a :href="'<?php echo $this->createUrl('campusSummary', array('branchId' => $branchId, 'type'=> $type)); ?>&yid='+key+''">{{val}}</a>
                            </li>
                        </ul>
                    </div>
                    <div class="radio-inline" v-if='permissionStatus==1'>
                        <button type="button" class="btn btn-primary btn-sm" @click='tea()'><?php echo Yii::t('user','View Report') ?></button>
                    </div>
                    <div class="radio-inline"  v-else>
                        <div v-if='status == 1 || status == 2'>
                        <select id="group-name" class="form-control ol-xs-5" @change="getname()">
                            <option>
                                <?php echo Yii::t('user','Please select a filter'); ?>
                            </option>
                            <option value="class">
                                <?php echo Yii::t('user','By Class'); ?>
                            </option>
                            <option value="course">
                                <?php echo Yii::t('user','By Course'); ?>
                            </option>
                            <option value="teacher">
                                <?php echo Yii::t('user','By Teacher'); ?>
                            </option>
                        </select>
                    </div> 
                    </div>  
                   
                    <template v-if='status == 0'>
                        <button type="button" class="btn btn-primary ml15 btn-sm disabled"><?php echo Yii::t('user','Generate Report'); ?>...</button>
                        <span><?php echo Yii::t('user','Generating reports, please wait'); ?></span>
                        <span></span>
                    </template>
                    <template v-if='status == 3'>
                        <button type="button" class="btn btn-primary ml15 btn-sm" @click='cache()'><?php echo Yii::t('user','Generate Report'); ?></button>
                        <span><?php echo Yii::t('user','Report is outdated, click to update'); ?></span>
                    </template>
                    <template v-if='status ==2'>
                        <button type="button" class="btn btn-primary ml15 btn-sm" @click='cache()'><?php echo Yii::t('user','Generate Report'); ?></button>
                        <span><?php echo Yii::t('user','Report is outdated, click to update'); ?></span>
                        <span class="text-danger"><?php echo ($status == 2) ? $time : "" ?></span>
                    </template>

                    <div class="clearfix">

                    </div>
                </div>
                <div class="row">
                <div class="col-md-2">
                <button type="button" class="btn btn-primary btn-xs col-md-12" id='listcontrol' onclick="showhide(this)"> &#x25B2;</button>
                </div>
                </div>
                <div class="mt15 row" id='listplay'>
                    <div v-if='selectname == "teacher"'>
                    <div class="col-md-12">
                        <div class="checkbox-custom" v-for='(list,id,index) in selectlist'>
                            <input type="checkbox" :id="id" :value="id" name='item' @click="tabeldata($event,id)">
                            <label :for="id">{{list}}</label>
                        </div>
                        </div>
                    </div>
                    <template v-if='selectname=="course"|| selectname=="class"'>
                        <div class="listheight col-md-2 col-xs-12">
                            <span class="list-group-item" v-for='(list,id,index) in classification' @click="addClassFun(index,id,list)" :class='{active:index==qwerqwre}'>{{list}}</span>
                        </div>
                        <div class="col-md-10 col-xs-12 rightover" v-if='Object.keys(classificationSub).length!=0'>
                            <div class="panel panel-default">
                                <div class="panel-heading">
                                <div class="checkbox">
                                <label>
                                  <input type="checkbox" @click='all($event)' :id='checkcoursename' :value="checkcoursename" v-model='checked'> {{checkcoursename}}
                                </label>
                                </div>
                                </div>
                                <ul class="list-group dataheight">
                                    <li class="list-group-item col-xs-12 col-sm-12 col-md-12 col-lg-6" v-for='(list,id,index) in classificationSub'>
                                    <div class="checkbox">
                                    <label>
                                      <input type="checkbox" :id="id" :value="id" name='item' @click="tabeldata($event,id)" v-model="check"> {{list}}
                                    </label>
                                    </div>
                                      <!--   <input type="checkbox" :id="id" :value="id" name='item' @click="tabeldata($event,id)" v-model="check">
                                        <label :for="id">{{list}}</label> -->
                                    </li>
                                    <div class="clearfix"></div>
                                </ul>
                            </div>
                        </div>
                        <div class="clearfix"> </div>
                    </template>
                </div>
            </div>

            <div class="clearfix"> </div>

            <div class="tab" v-if='tabledata.length!=0'>
                <template v-if='selectname=="class"'>
                    <p class="pull-right">
                        <button type="button" class="btn btn-primary" class="pull-right" @click='clear("class")'><?php echo Yii::t('user','Clear Selection'); ?></button>
                    </p>
                    <div class="clearfix"></div>
                    <p><button class="btn btn-primary"  onclick="print('class')"><?php echo Yii::t('user','Excel Export'); ?></button></p>
                    <table class=" table  table-bordered" :id="'average' + selectname">
                        <thead>
                            <tr>
                                <th colspan="19">
                                    <?php echo Yii::t('user','Class Average Report') ?>
                                </th>
                            </tr>
                            <tr>
                                <th rowspan="2">
                                    <?php echo Yii::t('user','Class') ?>
                                </th>
                                <th rowspan="2">
                                    <?php echo Yii::t('user','Students Number') ?>
                                </th>
                                <th colspan="4">
                                    <?php echo Yii::t('user','Reporting Period 1') ?>
                                </th>
                                <th colspan="4">
                                    <?php echo Yii::t('user','Reporting Period 2') ?>
                                </th>
                                <th colspan="4">
                                    <?php echo Yii::t('user','Reporting Period 3') ?>
                                </th>
                                <th colspan="4">
                                    <?php echo Yii::t('user','Reporting Period 4') ?>
                                </th>
                                <th rowspan="2">
                                    <?php echo Yii::t('user','Year-End Score') ?>
                                </th>
                            </tr>
                            <tr>

                                <th>A</th>
                                <th>B</th>
                                <th>C</th>
                                <th>D</th>
                                <th>A</th>
                                <th>B</th>
                                <th>C</th>
                                <th>D</th>
                                <th>A</th>
                                <th>B</th>
                                <th>C</th>
                                <th>D</th>
                                <th>A</th>
                                <th>B</th>
                                <th>C</th>
                                <th>D</th>

                            </tr>
                        </thead>
                        <tbody>
                            <template v-for='(datalist,id,index) in list'>
                                <tr v-for='(data,id) in datalist'>
                                    <td>
                                        <template v-if='data.teacherNameCn!=""'>
                                            {{data.name_cn}}<br>{{data.teacherNameCn}}</td>
                                        </template>
                                        <template v-else>
                                           {{data.name_cn}}<br><?php echo Yii::t("user","No teacher assigned") ?> 
                                        </template>
                                    </td>
                                    <td>{{data.total_students}}</td>
                                    <template v-for='datas in data.peroid1'>
                                        <td>{{datas}}</td>
                                    </template>
                                    <template v-for='datas in data.peroid2'>
                                        <td>{{datas}}</td>
                                    </template>

                                    <template v-for='datas in data.peroid3'>
                                        <td>{{datas}}</td>
                                    </template>
                                    <template v-for='datas in data.peroid4'>
                                        <td>{{datas}}</td>
                                    </template>
                                    <td>{{data.yearend}}</td>
                                </tr>
                            </template>
                        </tbody>
                    </table>
                    <p><button class="btn btn-primary"  onclick="printtwo('class')"><?php echo Yii::t('user','Excel Export'); ?></button></p>
                    <table class=" table  table-bordered" :id="'frequency' + selectname">
                        <thead>
                            <tr>
                                <th colspan="20">
                                    <?php echo Yii::t('user','Class Report') ?>
                                </th>
                            </tr>
                            <tr>
                                <th rowspan="2">
                                    <?php echo Yii::t('user','Class') ?>
                                </th>
                                <th rowspan="2">
                                    <?php echo Yii::t('user','Students Number') ?>
                                </th>
                                <th rowspan="2">
                                    <?php echo Yii::t('user','Score Level') ?>
                                </th>
                                <th colspan="4">
                                    <?php echo Yii::t('user','Reporting Period 1') ?>
                                </th>
                                <th colspan="4">
                                    <?php echo Yii::t('user','Reporting Period 2') ?>
                                </th>
                                <th colspan="4">
                                    <?php echo Yii::t('user','Reporting Period 3') ?>
                                </th>
                                <th colspan="4">
                                    <?php echo Yii::t('user','Reporting Period 4') ?>
                                </th>
                                <th rowspan="2">
                                    <?php echo Yii::t('user','Year-End Score') ?>
                                </th>
                            </tr>
                            <tr>
                                <th>A</th>
                                <th>B</th>
                                <th>C</th>
                                <th>D</th>
                                <th>A</th>
                                <th>B</th>
                                <th>C</th>
                                <th>D</th>
                                <th>A</th>
                                <th>B</th>
                                <th>C</th>
                                <th>D</th>
                                <th>A</th>
                                <th>B</th>
                                <th>C</th>
                                <th>D</th>
                            </tr>
                        </thead>
                        <tbody v-for='(datas,id,index) in classlist'>
                            <template v-for='(data,id) in datas'>
                                <tr>
                                    <td rowspan="5">
                                    <template v-if='data.teacherNameCn!=""'>
                                        {{data.name_cn}}<br>{{data.teacherNameCn}}</td>
                                    </template>
                                    <template v-else>
                                       {{data.name_cn}}<br><?php echo Yii::t("user","No teacher assigned") ?> 
                                    </template>
                                    </td>
                                    <td rowspan="5">{{data.total_students}}</td>
                                    <td>0</td>
                                    <td>{{data.score1.peroid1.A}}</td>
                                    <td>{{data.score1.peroid1.B}}</td>
                                    <td>{{data.score1.peroid1.C}}</td>
                                    <td>{{data.score1.peroid1.D}}</td>
                                    <td>{{data.score1.peroid2.A}}</td>
                                    <td>{{data.score1.peroid2.B}}</td>
                                    <td>{{data.score1.peroid2.C}}</td>
                                    <td>{{data.score1.peroid2.D}}</td>
                                    <td>{{data.score1.peroid3.A}}</td>
                                    <td>{{data.score1.peroid3.B}}</td>
                                    <td>{{data.score1.peroid3.C}}</td>
                                    <td>{{data.score1.peroid3.D}}</td>
                                    <td>{{data.score1.peroid4.A}}</td>
                                    <td>{{data.score1.peroid4.B}}</td>
                                    <td>{{data.score1.peroid4.C}}</td>
                                    <td>{{data.score1.peroid4.D}}</td>
                                    <td>{{data.score1.yearend}}</td>
                                </tr>
                                <tr>
                                    <td>1 - 2</td>
                                    <td>{{data.score2.peroid1.A}}</td>
                                    <td>{{data.score2.peroid1.B}}</td>
                                    <td>{{data.score2.peroid1.C}}</td>
                                    <td>{{data.score2.peroid1.D}}</td>
                                    <td>{{data.score2.peroid2.A}}</td>
                                    <td>{{data.score2.peroid2.B}}</td>
                                    <td>{{data.score2.peroid2.C}}</td>
                                    <td>{{data.score2.peroid2.D}}</td>
                                    <td>{{data.score2.peroid3.A}}</td>
                                    <td>{{data.score2.peroid3.B}}</td>
                                    <td>{{data.score2.peroid3.C}}</td>
                                    <td>{{data.score2.peroid3.D}}</td>
                                    <td>{{data.score2.peroid4.A}}</td>
                                    <td>{{data.score2.peroid4.B}}</td>
                                    <td>{{data.score2.peroid4.C}}</td>
                                    <td>{{data.score2.peroid4.D}}</td>
                                    <td>{{data.score2.yearend}}</td>
                                </tr>
                                <tr>
                                    <td>3 - 4</td>
                                    <td>{{data.score3.peroid1.A}}</td>
                                    <td>{{data.score3.peroid1.B}}</td>
                                    <td>{{data.score3.peroid1.C}}</td>
                                    <td>{{data.score3.peroid1.D}}</td>
                                    <td>{{data.score3.peroid2.A}}</td>
                                    <td>{{data.score3.peroid2.B}}</td>
                                    <td>{{data.score3.peroid2.C}}</td>
                                    <td>{{data.score3.peroid2.D}}</td>
                                    <td>{{data.score3.peroid3.A}}</td>
                                    <td>{{data.score3.peroid3.B}}</td>
                                    <td>{{data.score3.peroid3.C}}</td>
                                    <td>{{data.score3.peroid3.D}}</td>
                                    <td>{{data.score3.peroid4.A}}</td>
                                    <td>{{data.score3.peroid4.B}}</td>
                                    <td>{{data.score3.peroid4.C}}</td>
                                    <td>{{data.score3.peroid4.D}}</td>
                                    <td>{{data.score3.yearend}}</td>
                                </tr>
                                <tr>
                                    <td>5 - 6</td>
                                    <td>{{data.score4.peroid1.A}}</td>
                                    <td>{{data.score4.peroid1.B}}</td>
                                    <td>{{data.score4.peroid1.C}}</td>
                                    <td>{{data.score4.peroid1.D}}</td>
                                    <td>{{data.score4.peroid2.A}}</td>
                                    <td>{{data.score4.peroid2.B}}</td>
                                    <td>{{data.score4.peroid2.C}}</td>
                                    <td>{{data.score4.peroid2.D}}</td>
                                    <td>{{data.score4.peroid3.A}}</td>
                                    <td>{{data.score4.peroid3.B}}</td>
                                    <td>{{data.score4.peroid3.C}}</td>
                                    <td>{{data.score4.peroid3.D}}</td>
                                    <td>{{data.score4.peroid4.A}}</td>
                                    <td>{{data.score4.peroid4.B}}</td>
                                    <td>{{data.score4.peroid4.C}}</td>
                                    <td>{{data.score4.peroid4.D}}</td>
                                    <td>{{data.score4.yearend}}</td>
                                </tr>
                                <tr>
                                    <td>7 - 8</td>
                                    <td>{{data.score5.peroid1.A}}</td>
                                    <td>{{data.score5.peroid1.B}}</td>
                                    <td>{{data.score5.peroid1.C}}</td>
                                    <td>{{data.score5.peroid1.D}}</td>
                                    <td>{{data.score5.peroid2.A}}</td>
                                    <td>{{data.score5.peroid2.B}}</td>
                                    <td>{{data.score5.peroid2.C}}</td>
                                    <td>{{data.score5.peroid2.D}}</td>
                                    <td>{{data.score5.peroid3.A}}</td>
                                    <td>{{data.score5.peroid3.B}}</td>
                                    <td>{{data.score5.peroid3.C}}</td>
                                    <td>{{data.score5.peroid3.D}}</td>
                                    <td>{{data.score5.peroid4.A}}</td>
                                    <td>{{data.score5.peroid4.B}}</td>
                                    <td>{{data.score5.peroid4.C}}</td>
                                    <td>{{data.score5.peroid4.D}}</td>
                                    <td>{{data.score5.yearend}}</td>
                                </tr>
                            </template>
                        </tbody>
                    </table>
                </template>
                <template v-if='selectname=="course"'>
                    <p class="pull-right">
                        <button type="button" class="btn btn-primary" class="pull-right" @click='clear("course")'><?php echo Yii::t('user','Clear Selection'); ?></button>
                    </p>
                    <div class="clearfix"></div>
                    <p><button class="btn btn-primary"  onclick="print('course')"><?php echo Yii::t('user','Excel Export'); ?></button></p>
                    <table class=" table  table-bordered" :id="'average' + selectname">
                        <thead>
                            <tr>
                                <th colspan="19">
                                    <?php echo Yii::t('user','Class Average Report') ?>
                                </th>
                            </tr>
                            <tr>
                                <th rowspan="2">
                                    <?php echo Yii::t('user','Class') ?>
                                </th>
                                <th rowspan="2">
                                    <?php echo Yii::t('user','Students Number') ?>
                                </th>
                                <th colspan="4">
                                    <?php echo Yii::t('user','Reporting Period 1') ?>
                                </th>
                                <th colspan="4">
                                    <?php echo Yii::t('user','Reporting Period 2') ?>
                                </th>
                                <th colspan="4">
                                    <?php echo Yii::t('user','Reporting Period 3') ?>
                                </th>
                                <th colspan="4">
                                    <?php echo Yii::t('user','Reporting Period 4') ?>
                                </th>
                                <th rowspan="2">
                                    <?php echo Yii::t('user','Year-End Score') ?>
                                </th>
                            </tr>
                            <tr>

                                <th>A</th>
                                <th>B</th>
                                <th>C</th>
                                <th>D</th>
                                <th>A</th>
                                <th>B</th>
                                <th>C</th>
                                <th>D</th>
                                <th>A</th>
                                <th>B</th>
                                <th>C</th>
                                <th>D</th>
                                <th>A</th>
                                <th>B</th>
                                <th>C</th>
                                <th>D</th>

                            </tr>
                        </thead>
                        <tbody>
                            <template v-for='(list,id,index) in course_listaverage'>
                                <tr v-for='(data,id) in list'>
                                    <td>
                                        <template v-if='data.teacherNameCn!=""'>
                                            {{data.name_cn}}<br>{{data.teacherNameCn}}</td>
                                        </template>
                                        <template v-else>
                                           {{data.name_cn}}<br><?php echo Yii::t("user","No teacher assigned") ?> 
                                        </template>
                                    </td>
                                    <td>{{data.total_students}}</td>
                                    <template v-for='datas in data.peroid1'>
                                        <td>{{datas}}</td>
                                    </template>

                                    <template v-for='datas in data.peroid2'>
                                        <td>{{datas}}</td>
                                    </template>

                                    <template v-for='datas in data.peroid3'>
                                        <td>{{datas}}</td>
                                    </template>

                                    <template v-for='datas in data.peroid4'>
                                        <td>{{datas}}</td>
                                    </template>
                                    <td>{{data.yearend}}</td>
                                </tr>
                            </template>
                        </tbody>
                    </table>
                    <p><button class="btn btn-primary"  onclick="printtwo('course')"><?php echo Yii::t('user','Excel Export'); ?></button></p>
                    <table class=" table  table-bordered" :id="'frequency' + selectname">
                        <thead>
                            <tr>
                                <th colspan="20">
                                    <?php echo Yii::t('user','Class Frequency Report') ?>
                                </th>
                            </tr>
                            <tr>
                                <th rowspan="2">
                                    <?php echo Yii::t('user','Class') ?>
                                </th>
                                <th rowspan="2">
                                    <?php echo Yii::t('user','Students Number') ?>
                                </th>
                                <th rowspan="2">
                                    <?php echo Yii::t('user','Score Level') ?>
                                </th>
                                <th colspan="4">
                                    <?php echo Yii::t('user','Reporting Period 1') ?>
                                </th>
                                <th colspan="4">
                                    <?php echo Yii::t('user','Reporting Period 2') ?>
                                </th>
                                <th colspan="4">
                                    <?php echo Yii::t('user','Reporting Period 3') ?>
                                </th>
                                <th colspan="4">
                                    <?php echo Yii::t('user','Reporting Period 4') ?>
                                </th>
                                <th rowspan="2">
                                    <?php echo Yii::t('user','Year-End Score') ?>
                                </th>
                            </tr>
                            <tr>
                                <th>A</th>
                                <th>B</th>
                                <th>C</th>
                                <th>D</th>
                                <th>A</th>
                                <th>B</th>
                                <th>C</th>
                                <th>D</th>
                                <th>A</th>
                                <th>B</th>
                                <th>C</th>
                                <th>D</th>
                                <th>A</th>
                                <th>B</th>
                                <th>C</th>
                                <th>D</th>
                            </tr>
                        </thead>
                        <tbody v-for='(list,id,index) in course_listfrequency'>
                            <template v-for='(data,id) in list'>
                                <tr>
                                    <td rowspan="5">
                                        <template v-if='data.teacherNameCn!=""'>
                                            {{data.name_cn}}<br>{{data.teacherNameCn}}</td>
                                        </template>
                                        <template v-else>
                                           {{data.name_cn}}<br><?php echo Yii::t("user","No teacher assigned") ?>
                                        </template>
                                    </td>
                                    <td rowspan="5">{{data.total_students}}</td>
                                    <td>0</td>
                                    <td>{{data.score1.peroid1.A}}</td>
                                    <td>{{data.score1.peroid1.B}}</td>
                                    <td>{{data.score1.peroid1.C}}</td>
                                    <td>{{data.score1.peroid1.D}}</td>
                                    <td>{{data.score1.peroid2.A}}</td>
                                    <td>{{data.score1.peroid2.B}}</td>
                                    <td>{{data.score1.peroid2.C}}</td>
                                    <td>{{data.score1.peroid2.D}}</td>
                                    <td>{{data.score1.peroid3.A}}</td>
                                    <td>{{data.score1.peroid3.B}}</td>
                                    <td>{{data.score1.peroid3.C}}</td>
                                    <td>{{data.score1.peroid3.D}}</td>
                                    <td>{{data.score1.peroid4.A}}</td>
                                    <td>{{data.score1.peroid4.B}}</td>
                                    <td>{{data.score1.peroid4.C}}</td>
                                    <td>{{data.score1.peroid4.D}}</td>
                                    <td>{{data.score1.yearend}}</td>
                                </tr>
                                <tr>
                                    <td>1 - 2</td>
                                    <td>{{data.score2.peroid1.A}}</td>
                                    <td>{{data.score2.peroid1.B}}</td>
                                    <td>{{data.score2.peroid1.C}}</td>
                                    <td>{{data.score2.peroid1.D}}</td>
                                    <td>{{data.score2.peroid2.A}}</td>
                                    <td>{{data.score2.peroid2.B}}</td>
                                    <td>{{data.score2.peroid2.C}}</td>
                                    <td>{{data.score2.peroid2.D}}</td>
                                    <td>{{data.score2.peroid3.A}}</td>
                                    <td>{{data.score2.peroid3.B}}</td>
                                    <td>{{data.score2.peroid3.C}}</td>
                                    <td>{{data.score2.peroid3.D}}</td>
                                    <td>{{data.score2.peroid4.A}}</td>
                                    <td>{{data.score2.peroid4.B}}</td>
                                    <td>{{data.score2.peroid4.C}}</td>
                                    <td>{{data.score2.peroid4.D}}</td>
                                    <td>{{data.score2.yearend}}</td>
                                </tr>
                                <tr>
                                    <td>3 - 4</td>
                                    <td>{{data.score3.peroid1.A}}</td>
                                    <td>{{data.score3.peroid1.B}}</td>
                                    <td>{{data.score3.peroid1.C}}</td>
                                    <td>{{data.score3.peroid1.D}}</td>
                                    <td>{{data.score3.peroid2.A}}</td>
                                    <td>{{data.score3.peroid2.B}}</td>
                                    <td>{{data.score3.peroid2.C}}</td>
                                    <td>{{data.score3.peroid2.D}}</td>
                                    <td>{{data.score3.peroid3.A}}</td>
                                    <td>{{data.score3.peroid3.B}}</td>
                                    <td>{{data.score3.peroid3.C}}</td>
                                    <td>{{data.score3.peroid3.D}}</td>
                                    <td>{{data.score3.peroid4.A}}</td>
                                    <td>{{data.score3.peroid4.B}}</td>
                                    <td>{{data.score3.peroid4.C}}</td>
                                    <td>{{data.score3.peroid4.D}}</td>
                                    <td>{{data.score3.yearend}}</td>
                                </tr>
                                <tr>
                                    <td>5 - 6</td>
                                    <td>{{data.score4.peroid1.A}}</td>
                                    <td>{{data.score4.peroid1.B}}</td>
                                    <td>{{data.score4.peroid1.C}}</td>
                                    <td>{{data.score4.peroid1.D}}</td>
                                    <td>{{data.score4.peroid2.A}}</td>
                                    <td>{{data.score4.peroid2.B}}</td>
                                    <td>{{data.score4.peroid2.C}}</td>
                                    <td>{{data.score4.peroid2.D}}</td>
                                    <td>{{data.score4.peroid3.A}}</td>
                                    <td>{{data.score4.peroid3.B}}</td>
                                    <td>{{data.score4.peroid3.C}}</td>
                                    <td>{{data.score4.peroid3.D}}</td>
                                    <td>{{data.score4.peroid4.A}}</td>
                                    <td>{{data.score4.peroid4.B}}</td>
                                    <td>{{data.score4.peroid4.C}}</td>
                                    <td>{{data.score4.peroid4.D}}</td>
                                    <td>{{data.score4.yearend}}</td>
                                </tr>
                                <tr>
                                    <td>7 - 8</td>
                                    <td>{{data.score5.peroid1.A}}</td>
                                    <td>{{data.score5.peroid1.B}}</td>
                                    <td>{{data.score5.peroid1.C}}</td>
                                    <td>{{data.score5.peroid1.D}}</td>
                                    <td>{{data.score5.peroid2.A}}</td>
                                    <td>{{data.score5.peroid2.B}}</td>
                                    <td>{{data.score5.peroid2.C}}</td>
                                    <td>{{data.score5.peroid2.D}}</td>
                                    <td>{{data.score5.peroid3.A}}</td>
                                    <td>{{data.score5.peroid3.B}}</td>
                                    <td>{{data.score5.peroid3.C}}</td>
                                    <td>{{data.score5.peroid3.D}}</td>
                                    <td>{{data.score5.peroid4.A}}</td>
                                    <td>{{data.score5.peroid4.B}}</td>
                                    <td>{{data.score5.peroid4.C}}</td>
                                    <td>{{data.score5.peroid4.D}}</td>
                                    <td>{{data.score5.yearend}}</td>
                                </tr>
                            </template>
                        </tbody>
                    </table>
                </template>
                <template v-if='selectname=="teacher"'>
                    <p class="pull-right">
                        <button type="button" class="btn btn-primary" class="pull-right" @click='clear("teacher")'><?php echo Yii::t('user','Clear Selection'); ?></button>
                    </p>
                    <div class="clearfix"></div>
                    <p><button class="btn btn-primary"  onclick="print('teacher')"><?php echo Yii::t('user','Excel Export'); ?></button></p>
                    <table class=" table  table-bordered" :id="'average' + selectname">
                        <thead>
                            <tr>
                                <th colspan="19">
                                    <?php echo Yii::t('user','Class Average Report'); ?>
                                </th>
                            </tr>
                            <tr>
                                <th rowspan="2">
                                    <?php echo Yii::t('user','Class'); ?>
                                </th>
                                <th rowspan="2">
                                    <?php echo Yii::t('user','Students Number'); ?>
                                </th>
                                <th colspan="4">
                                    <?php echo Yii::t('user','Reporting Period 1'); ?>
                                </th>
                                <th colspan="4">
                                    <?php echo Yii::t('user','Reporting Period 2'); ?>
                                </th>
                                <th colspan="4">
                                    <?php echo Yii::t('user','Reporting Period 3'); ?>x</th>
                                <th colspan="4">
                                    <?php echo Yii::t('user','Reporting Period 4'); ?>
                                </th>
                                <th rowspan="2">
                                    <?php echo Yii::t('user','Year-End Score'); ?>
                                </th>
                            </tr>
                            <tr>

                                <th>A</th>
                                <th>B</th>
                                <th>C</th>
                                <th>D</th>
                                <th>A</th>
                                <th>B</th>
                                <th>C</th>
                                <th>D</th>
                                <th>A</th>
                                <th>B</th>
                                <th>C</th>
                                <th>D</th>
                                <th>A</th>
                                <th>B</th>
                                <th>C</th>
                                <th>D</th>

                            </tr>
                        </thead>
                        <tbody>
                            <template v-for='(datas,id,index) in teacher_listaverage'>
                                <tr v-for='(data,id) in datas'>
                                    <td>
                                        <template v-if='data.teacherNameCn!=""'>
                                            {{data.name_cn}}<br>{{data.teacherNameCn}}</td>
                                        </template>
                                        <template v-else>
                                           {{data.name_cn}}<br><?php echo Yii::t("user","No teacher assigned") ?> 
                                        </template>
                                    </td>
                                    <td>{{data.total_students}}</td>
                                    <template v-for='datas in data.peroid1'>
                                        <td>{{datas}}</td>
                                    </template>
                                    <template v-for='datas in data.peroid2'>
                                        <td>{{datas}}</td>
                                    </template>
                                    <template v-for='datas in data.peroid3'>
                                        <td>{{datas}}</td>
                                    </template>
                                    <template v-for='datas in data.peroid4'>
                                        <td>{{datas}}</td>
                                    </template>
                                    <td>{{data.yearend}}</td>
                                </tr>
                            </template>
                        </tbody>
                    </table>
                    <p><button class="btn btn-primary"  onclick="printtwo('teacher')"><?php echo Yii::t('user','Excel Export'); ?></button></p>
                    <table class=" table  table-bordered" :id="'frequency' + selectname">
                        <thead>
                            <tr>
                                <th colspan="20">
                                    <?php echo Yii::t('user','Class Frequency Report'); ?>
                                </th>
                            </tr>
                            <tr>
                                <th rowspan="2">
                                    <?php echo Yii::t('user','Class'); ?>
                                </th>
                                <th rowspan="2">
                                    <?php echo Yii::t('user','Students Number'); ?>
                                </th>
                                <th rowspan="2">
                                    <?php echo Yii::t('user','Score Level'); ?>
                                </th>
                                <th colspan="4">
                                    <?php echo Yii::t('user','Reporting Period 1'); ?>
                                </th>
                                <th colspan="4">
                                    <?php echo Yii::t('user','Reporting Period 2'); ?>
                                </th>
                                <th colspan="4">
                                    <?php echo Yii::t('user','Reporting Period 3'); ?>
                                </th>
                                <th colspan="4">
                                    <?php echo Yii::t('user','Reporting Period 4'); ?>
                                </th>
                                <th rowspan="2">
                                    <?php echo Yii::t('user','Year-End Score'); ?>
                                </th>
                            </tr>
                            <tr>
                                <th>A</th>
                                <th>B</th>
                                <th>C</th>
                                <th>D</th>
                                <th>A</th>
                                <th>B</th>
                                <th>C</th>
                                <th>D</th>
                                <th>A</th>
                                <th>B</th>
                                <th>C</th>
                                <th>D</th>
                                <th>A</th>
                                <th>B</th>
                                <th>C</th>
                                <th>D</th>
                            </tr>
                        </thead>
                        <tbody v-for='(datas,id,index) in teacher_listfrequency'>
                            <template v-for='(data,id) in datas'>
                                <tr>
                                    <td rowspan="5">
                                        <template v-if='data.teacherNameCn!=""'>
                                            {{data.name_cn}}<br>{{data.teacherNameCn}}</td>
                                        </template>
                                        <template v-else>
                                           {{data.name_cn}}<br><?php echo Yii::t("user","No teacher assigned") ?> 
                                        </template>
                                    </td>
                                    <td rowspan="5">{{data.total_students}}</td>
                                    <td>0</td>
                                    <td>{{data.score1.peroid1.A}}</td>
                                    <td>{{data.score1.peroid1.B}}</td>
                                    <td>{{data.score1.peroid1.C}}</td>
                                    <td>{{data.score1.peroid1.D}}</td>
                                    <td>{{data.score1.peroid2.A}}</td>
                                    <td>{{data.score1.peroid2.B}}</td>
                                    <td>{{data.score1.peroid2.C}}</td>
                                    <td>{{data.score1.peroid2.D}}</td>
                                    <td>{{data.score1.peroid3.A}}</td>
                                    <td>{{data.score1.peroid3.B}}</td>
                                    <td>{{data.score1.peroid3.C}}</td>
                                    <td>{{data.score1.peroid3.D}}</td>
                                    <td>{{data.score1.peroid4.A}}</td>
                                    <td>{{data.score1.peroid4.B}}</td>
                                    <td>{{data.score1.peroid4.C}}</td>
                                    <td>{{data.score1.peroid4.D}}</td>
                                    <td>{{data.score1.yearend}}</td>
                                </tr>
                                <tr>
                                    <td>1 - 2</td>
                                    <td>{{data.score2.peroid1.A}}</td>
                                    <td>{{data.score2.peroid1.B}}</td>
                                    <td>{{data.score2.peroid1.C}}</td>
                                    <td>{{data.score2.peroid1.D}}</td>
                                    <td>{{data.score2.peroid2.A}}</td>
                                    <td>{{data.score2.peroid2.B}}</td>
                                    <td>{{data.score2.peroid2.C}}</td>
                                    <td>{{data.score2.peroid2.D}}</td>
                                    <td>{{data.score2.peroid3.A}}</td>
                                    <td>{{data.score2.peroid3.B}}</td>
                                    <td>{{data.score2.peroid3.C}}</td>
                                    <td>{{data.score2.peroid3.D}}</td>
                                    <td>{{data.score2.peroid4.A}}</td>
                                    <td>{{data.score2.peroid4.B}}</td>
                                    <td>{{data.score2.peroid4.C}}</td>
                                    <td>{{data.score2.peroid4.D}}</td>
                                    <td>{{data.score2.yearend}}</td>
                                </tr>
                                <tr>
                                    <td>3 - 4</td>
                                    <td>{{data.score3.peroid1.A}}</td>
                                    <td>{{data.score3.peroid1.B}}</td>
                                    <td>{{data.score3.peroid1.C}}</td>
                                    <td>{{data.score3.peroid1.D}}</td>
                                    <td>{{data.score3.peroid2.A}}</td>
                                    <td>{{data.score3.peroid2.B}}</td>
                                    <td>{{data.score3.peroid2.C}}</td>
                                    <td>{{data.score3.peroid2.D}}</td>
                                    <td>{{data.score3.peroid3.A}}</td>
                                    <td>{{data.score3.peroid3.B}}</td>
                                    <td>{{data.score3.peroid3.C}}</td>
                                    <td>{{data.score3.peroid3.D}}</td>
                                    <td>{{data.score3.peroid4.A}}</td>
                                    <td>{{data.score3.peroid4.B}}</td>
                                    <td>{{data.score3.peroid4.C}}</td>
                                    <td>{{data.score3.peroid4.D}}</td>
                                    <td>{{data.score3.yearend}}</td>
                                </tr>
                                <tr>
                                    <td>5 - 6</td>
                                    <td>{{data.score4.peroid1.A}}</td>
                                    <td>{{data.score4.peroid1.B}}</td>
                                    <td>{{data.score4.peroid1.C}}</td>
                                    <td>{{data.score4.peroid1.D}}</td>
                                    <td>{{data.score4.peroid2.A}}</td>
                                    <td>{{data.score4.peroid2.B}}</td>
                                    <td>{{data.score4.peroid2.C}}</td>
                                    <td>{{data.score4.peroid2.D}}</td>
                                    <td>{{data.score4.peroid3.A}}</td>
                                    <td>{{data.score4.peroid3.B}}</td>
                                    <td>{{data.score4.peroid3.C}}</td>
                                    <td>{{data.score4.peroid3.D}}</td>
                                    <td>{{data.score4.peroid4.A}}</td>
                                    <td>{{data.score4.peroid4.B}}</td>
                                    <td>{{data.score4.peroid4.C}}</td>
                                    <td>{{data.score4.peroid4.D}}</td>
                                    <td>{{data.score4.yearend}}</td>
                                </tr>
                                <tr>
                                    <td>7 - 8</td>
                                    <td>{{data.score5.peroid1.A}}</td>
                                    <td>{{data.score5.peroid1.B}}</td>
                                    <td>{{data.score5.peroid1.C}}</td>
                                    <td>{{data.score5.peroid1.D}}</td>
                                    <td>{{data.score5.peroid2.A}}</td>
                                    <td>{{data.score5.peroid2.B}}</td>
                                    <td>{{data.score5.peroid2.C}}</td>
                                    <td>{{data.score5.peroid2.D}}</td>
                                    <td>{{data.score5.peroid3.A}}</td>
                                    <td>{{data.score5.peroid3.B}}</td>
                                    <td>{{data.score5.peroid3.C}}</td>
                                    <td>{{data.score5.peroid3.D}}</td>
                                    <td>{{data.score5.peroid4.A}}</td>
                                    <td>{{data.score5.peroid4.B}}</td>
                                    <td>{{data.score5.peroid4.C}}</td>
                                    <td>{{data.score5.peroid4.D}}</td>
                                    <td>{{data.score5.yearend}}</td>
                                </tr>
                            </template>
                        </tbody>
                    </table>
                </template>
            </div>
            <div v-if='Object.keys(teacherdata).length!=0'>
                <p><button class="btn btn-primary"  onclick="print('teacher')"><?php echo Yii::t('user','Excel Export'); ?></button></p>
                <table class=" table  table-bordered" id="averageteacher">
                        <thead>
                            <tr>
                                <th colspan="19">
                                    <?php echo Yii::t('user','Class Average Report') ?>
                                   
                                </th>
                            </tr>
                            <tr>
                                <th rowspan="2">
                                    <?php echo Yii::t('user','Class') ?>
                                </th>
                                <th rowspan="2">
                                    <?php echo Yii::t('user','Students Number') ?>
                                </th>
                                <th colspan="4">
                                    <?php echo Yii::t('user','Reporting Period 1') ?>
                                </th>
                                <th colspan="4">
                                    <?php echo Yii::t('user','Reporting Period 2') ?>
                                </th>
                                <th colspan="4">
                                    <?php echo Yii::t('user','Reporting Period 3') ?>
                                </th>
                                <th colspan="4">
                                    <?php echo Yii::t('user','Reporting Period 4') ?>
                                </th>
                                <th rowspan="2">
                                    <?php echo Yii::t('user','Year-End Score') ?>
                                </th>
                            </tr>
                            <tr>

                                <th>A</th>
                                <th>B</th>
                                <th>C</th>
                                <th>D</th>
                                <th>A</th>
                                <th>B</th>
                                <th>C</th>
                                <th>D</th>
                                <th>A</th>
                                <th>B</th>
                                <th>C</th>
                                <th>D</th>
                                <th>A</th>
                                <th>B</th>
                                <th>C</th>
                                <th>D</th>

                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for='(data,ind) in teacherdata'>
                           
                               
                                    <td>
                                        <template v-if='data.teacherNameCn!=""'>
                                            {{data.name_cn}}<br>{{data.teacherNameCn}}</td>
                                        </template>
                                        <template v-else>
                                           {{data.name_cn}}<br><?php echo Yii::t("user","No teacher assigned") ?> 
                                        </template>
                                    </td>
                                    <td>{{data.total_students}}</td>
                                    <template v-for='datas in data.peroid1'>
                                        <td>{{datas}}</td>
                                    </template>
                                    <template v-for='datas in data.peroid2'>
                                        <td>{{datas}}</td>
                                    </template>

                                    <template v-for='datas in data.peroid3'>
                                        <td>{{datas}}</td>
                                    </template>
                                    <template v-for='datas in data.peroid4'>
                                        <td>{{datas}}</td>
                                    </template>
                                    <td>{{data.yearend}}</td>
                              
                            </tr>
                        </tbody>
                </table>
                <p><button class="btn btn-primary"  onclick="printtwo('teacher')"><?php echo Yii::t('user','Excel Export'); ?></button></p>
                <table class=" table  table-bordered" id="frequencyteacher">
                    <thead>
                        <tr>
                            <th colspan="20">
                                <?php echo Yii::t('user','Class Report') ?>
                            </th>
                        </tr>
                        <tr>
                            <th rowspan="2">
                                <?php echo Yii::t('user','Class') ?>
                            </th>
                            <th rowspan="2">
                                <?php echo Yii::t('user','Students Number') ?>
                            </th>
                            <th rowspan="2">
                                <?php echo Yii::t('user','Score Level') ?>
                            </th>
                            <th colspan="4">
                                <?php echo Yii::t('user','Reporting Period 1') ?>
                            </th>
                            <th colspan="4">
                                <?php echo Yii::t('user','Reporting Period 2') ?>
                            </th>
                            <th colspan="4">
                                <?php echo Yii::t('user','Reporting Period 3') ?>
                            </th>
                            <th colspan="4">
                                <?php echo Yii::t('user','Reporting Period 4') ?>
                            </th>
                            <th rowspan="2">
                                <?php echo Yii::t('user','Year-End Score') ?>
                            </th>
                        </tr>
                        <tr>
                            <th>A</th>
                            <th>B</th>
                            <th>C</th>
                            <th>D</th>
                            <th>A</th>
                            <th>B</th>
                            <th>C</th>
                            <th>D</th>
                            <th>A</th>
                            <th>B</th>
                            <th>C</th>
                            <th>D</th>
                            <th>A</th>
                            <th>B</th>
                            <th>C</th>
                            <th>D</th>
                        </tr>
                    </thead>
                    <tbody>
                        <template v-for='(data,id) in teacherdata2'>
                            <tr>
                                <td rowspan="5">
                                    <template v-if='data.teacherNameCn!=""'>
                                        {{data.name_cn}}<br>{{data.teacherNameCn}}</td>
                                    </template>
                                    <template v-else>
                                       {{data.name_cn}}<br><?php echo Yii::t("user","No teacher assigned") ?> 
                                    </template>
                                </td>
                                <td rowspan="5">{{data.total_students}}</td>
                                <td>0</td>
                                <td>{{data.score1.peroid1.A}}</td>
                                <td>{{data.score1.peroid1.B}}</td>
                                <td>{{data.score1.peroid1.C}}</td>
                                <td>{{data.score1.peroid1.D}}</td>
                                <td>{{data.score1.peroid2.A}}</td>
                                <td>{{data.score1.peroid2.B}}</td>
                                <td>{{data.score1.peroid2.C}}</td>
                                <td>{{data.score1.peroid2.D}}</td>
                                <td>{{data.score1.peroid3.A}}</td>
                                <td>{{data.score1.peroid3.B}}</td>
                                <td>{{data.score1.peroid3.C}}</td>
                                <td>{{data.score1.peroid3.D}}</td>
                                <td>{{data.score1.peroid4.A}}</td>
                                <td>{{data.score1.peroid4.B}}</td>
                                <td>{{data.score1.peroid4.C}}</td>
                                <td>{{data.score1.peroid4.D}}</td>
                                <td>{{data.score1.yearend}}</td>
                            </tr>
                            <tr>
                                <td>1 - 2</td>
                                <td>{{data.score2.peroid1.A}}</td>
                                <td>{{data.score2.peroid1.B}}</td>
                                <td>{{data.score2.peroid1.C}}</td>
                                <td>{{data.score2.peroid1.D}}</td>
                                <td>{{data.score2.peroid2.A}}</td>
                                <td>{{data.score2.peroid2.B}}</td>
                                <td>{{data.score2.peroid2.C}}</td>
                                <td>{{data.score2.peroid2.D}}</td>
                                <td>{{data.score2.peroid3.A}}</td>
                                <td>{{data.score2.peroid3.B}}</td>
                                <td>{{data.score2.peroid3.C}}</td>
                                <td>{{data.score2.peroid3.D}}</td>
                                <td>{{data.score2.peroid4.A}}</td>
                                <td>{{data.score2.peroid4.B}}</td>
                                <td>{{data.score2.peroid4.C}}</td>
                                <td>{{data.score2.peroid4.D}}</td>
                                <td>{{data.score2.yearend}}</td>
                            </tr>
                            <tr>
                                <td>3 - 4</td>
                                <td>{{data.score3.peroid1.A}}</td>
                                <td>{{data.score3.peroid1.B}}</td>
                                <td>{{data.score3.peroid1.C}}</td>
                                <td>{{data.score3.peroid1.D}}</td>
                                <td>{{data.score3.peroid2.A}}</td>
                                <td>{{data.score3.peroid2.B}}</td>
                                <td>{{data.score3.peroid2.C}}</td>
                                <td>{{data.score3.peroid2.D}}</td>
                                <td>{{data.score3.peroid3.A}}</td>
                                <td>{{data.score3.peroid3.B}}</td>
                                <td>{{data.score3.peroid3.C}}</td>
                                <td>{{data.score3.peroid3.D}}</td>
                                <td>{{data.score3.peroid4.A}}</td>
                                <td>{{data.score3.peroid4.B}}</td>
                                <td>{{data.score3.peroid4.C}}</td>
                                <td>{{data.score3.peroid4.D}}</td>
                                <td>{{data.score3.yearend}}</td>
                            </tr>
                            <tr>
                                <td>5 - 6</td>
                                <td>{{data.score4.peroid1.A}}</td>
                                <td>{{data.score4.peroid1.B}}</td>
                                <td>{{data.score4.peroid1.C}}</td>
                                <td>{{data.score4.peroid1.D}}</td>
                                <td>{{data.score4.peroid2.A}}</td>
                                <td>{{data.score4.peroid2.B}}</td>
                                <td>{{data.score4.peroid2.C}}</td>
                                <td>{{data.score4.peroid2.D}}</td>
                                <td>{{data.score4.peroid3.A}}</td>
                                <td>{{data.score4.peroid3.B}}</td>
                                <td>{{data.score4.peroid3.C}}</td>
                                <td>{{data.score4.peroid3.D}}</td>
                                <td>{{data.score4.peroid4.A}}</td>
                                <td>{{data.score4.peroid4.B}}</td>
                                <td>{{data.score4.peroid4.C}}</td>
                                <td>{{data.score4.peroid4.D}}</td>
                                <td>{{data.score4.yearend}}</td>
                            </tr>
                            <tr>
                                <td>7 - 8</td>
                                <td>{{data.score5.peroid1.A}}</td>
                                <td>{{data.score5.peroid1.B}}</td>
                                <td>{{data.score5.peroid1.C}}</td>
                                <td>{{data.score5.peroid1.D}}</td>
                                <td>{{data.score5.peroid2.A}}</td>
                                <td>{{data.score5.peroid2.B}}</td>
                                <td>{{data.score5.peroid2.C}}</td>
                                <td>{{data.score5.peroid2.D}}</td>
                                <td>{{data.score5.peroid3.A}}</td>
                                <td>{{data.score5.peroid3.B}}</td>
                                <td>{{data.score5.peroid3.C}}</td>
                                <td>{{data.score5.peroid3.D}}</td>
                                <td>{{data.score5.peroid4.A}}</td>
                                <td>{{data.score5.peroid4.B}}</td>
                                <td>{{data.score5.peroid4.C}}</td>
                                <td>{{data.score5.peroid4.D}}</td>
                                <td>{{data.score5.yearend}}</td>
                            </tr>
                        </template>
                    </tbody>
                </table>
            </div>
            <div class="modal fade speed" tabindex="-1" id="progressbars" data-backdrop="static" data-keyboard="false">
                <!--窗口声明：-->
                <div class="modal-dialog modal-lg">
                    <!-- 内容声明 -->
                    <div class="modal-content">
                        <!-- 主体 -->
                        <div class="modal-body">
                            <div class="progress progress-striped active">
                                <div id="test" class="progress-bar progress-bar-info " role="progressbar" :style="width">
                                    {{length}}%
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>
</div>

<script>
    var yid = <?php echo $yid ?>;
    var status = <?php echo $status ?>;
    var calenderList = <?php echo json_encode($calenderList) ?>;
    var permissionStatus = <?php echo $permissionStatus ?>;
    var permissionStatusId = <?php echo $id ?>;
    $('#listcontrol').hide()

    function print(ids) {
  
        var wb = XLSX.utils.book_new();
        var ws = XLSX.utils.table_to_sheet(document.getElementById('average' + ids));
        XLSX.utils.book_append_sheet(wb, ws, "table");
        // generate Blob
        var wbout = XLSX.write(wb, {
            bookType: 'xlsx',
            type: 'array'
        });
        var blob = new Blob([wbout], {
            type: 'application/octet-stream'
        });
        // save file
        var link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = 'average' + ids + '.xlsx';
        link.click();

    }

    function printtwo(ids) {
        var wb = XLSX.utils.book_new();
        var ws = XLSX.utils.table_to_sheet(document.getElementById('frequency' + ids));
        XLSX.utils.book_append_sheet(wb, ws, "table");
        // generate Blob
        var wbout = XLSX.write(wb, {
            bookType: 'xlsx',
            type: 'array'
        });
        var blob = new Blob([wbout], {
            type: 'application/octet-stream'
        });
        // save file
        var link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = 'frequency' + ids + '.xlsx';
        link.click();

    }

    function showhide(obj) {
        $(obj).html($("#listplay").is(":hidden") ? ' &#x25B2;' : '&#x25BC;');
        
        $("#listplay").slideToggle();
    }
    var datalist = new Vue({
        el: "#datalist",
        data: {
            data: '',
            calenderList: calenderList,
            selectlist: '', //班级课程或者老师
            selectname: '', //选中的名字
            yid: yid,
            status: status,
            tabledata: [],
            list: {}, //班级展示一
            ajaxdata: {}, //班级获取一
            classlist: {}, //班级展示二
            ajaxclass: {}, //班级获取二  
            course_listaverage: {}, //课程展示表格一
            course_ajaxaverage: {}, //课程获取表格一
            course_listfrequency: {}, //课程展示表格二
            course_ajaxfrequency: {}, //课程获取表格一
            teacher_listaverage: {}, //老师展示表格一
            teacher_ajaxaverage: {}, //老师获取表格一
            teacher_listfrequency: {}, //老师展示表格二
            teacher_ajaxfrequency: {}, //老师获取表格一
            classification: {}, //课程区分type
            qwerqwre: "-1",
            classificationSub: {}, //根据typeid查找对应数据
            check: [], //model
            checkcoursename: '', //选中的课程title显示
            length: '', //进度条
            width: {
                width: ''
            },
            checked: [], //全选model
            permissionStatus:permissionStatus,
            teacherdata:{},
            teacherdata2:{}
        },
        created: function() {
            if(this.status == '0') {
                this.$options.methods.set();
            }

        },
        methods: {
            tea(){
              
                 $.ajax({
                    url: '<?php echo $this->createUrl("campusSummaryData")?>',
                    type: "POST",
                    async: true,
                    dataType: 'json',
                    data: {
                        type:'teacher',
                        id: permissionStatusId,
                        yid: yid
                    },
                    success: function(data) {
                       
                        if(data.state == 'success') {
                           datalist.teacherdata=data.data.average
                           datalist.teacherdata2=data.data.frequency
                           for(var i=0;i<data.data.average.length;i++){
                            console.log(data.data.average[i])
                           }
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }

                    },
                    error: function() {
                        alert("请求错误")
                    }
                });
            },
            addClassFun(index, id, list) {
                this.checkcoursename = list
                this.classificationSub = {}
                this.qwerqwre = index;
                this.datalist = this.data[this.qwerqwre]
                for(key in this.data[this.selectname]) {
                    if(key.substring(0, 2) == id) {
                        this.classificationSub[key.toString()] = this.data[this.selectname][key]
                    }
                }
                this.$nextTick(function() {
                    var headheight = $('.listheight').height();
                    $(".rightover").css("max-height", headheight + "px")
                    $('.dataheight').css("max-height", headheight - $('.panel-heading').outerHeight() + "px")
                });
            },
            set() {
                var t1 = setInterval(function() {
                    $.ajax({
                        url: '<?php echo $this->createUrl("StateManageStatus")?>',
                        type: 'post',
                        dataType: 'json',
                        data: {
                            yid: yid
                        },
                        success: function(data) {
                            if(data.data.status != '0') {
                                datalist.status = data.data.status
                                window.clearInterval(t1);
                            }

                        },
                        error: function(data) {
                            resultTip({
                                error: 'warning',
                                msg: '请求错误'
                            });
                        }
                    })
                }, 10000);
            },
            cache() {
                $.ajax({
                    url: '<?php echo $this->createUrl("generateData")?>',
                    type: 'post',
                    dataType: 'json',
                    data: {
                        yid: yid
                    },
                    success: function(data) {
                      
                        if(data.state == 'success') {
                            datalist.status = data.data.status
                            datalist.$options.methods.set();
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }

                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: '请求错误'
                        });
                    }
                })
            },
            all(e) {
                var checked = e.target.checked
                var valArr = []
                if(checked) {
                    $("input[name='item']").attr('checked', true)
                    $("input[name='item']:checkbox:checked").each(function(i) {
                        valArr[i] = $(this).val();
                    });
                    datalist.$options.methods.tabeldata(e, valArr)
                    for(var i = 0; i < valArr.length; ++i) {
                        if(datalist.check.indexOf(valArr[i]) == -1) {
                            datalist.check.push(valArr[i]);
                        }
                    }
                } else {
                    $(":checkbox").removeAttr("checked");
                    $("input[name='item']:checkbox").each(function(i) {
                        valArr[i] = $(this).val();
                    });
                    for(var i = 0; i < valArr.length; ++i) {
                        datalist.$options.methods.tabeldata(e, valArr[i])
                        Vue.delete(datalist.check, datalist.check.indexOf(valArr[i]));
                       
                    }
                }
            },
            getname() {
                var name = $("option:selected").val()
                if(this.data == '') {
                    $.ajax({
                        url: '<?php echo $this->createUrl("campusSummaryList")?>',
                        type: 'post',
                        dataType: 'json',
                        data: {
                            yid: yid
                        },
                        success: function(data) {
                            
                            if(data.state == 'success') {
                                datalist.data = data.data
                                datalist.selectname = name
                                datalist.selectlist = data.data[name]
                                datalist.classification = data.data.type
                            } else {

                                resultTip({
                                    error: 'warning',
                                    msg: data.message
                                });
                            }
                        },
                        error: function(data) {
                            resultTip({
                                error: 'warning',
                                msg: '请求错误'
                            });
                        }
                    })
                }

                this.selectname = name
                this.selectlist = this.data[name]
                this.classification = this.data.type
                this.tabledata = []
                this.list = {}
                this.check = []
                this.checked = []
                this.qwerqwre = '-1'
                this.classificationSub = {}
                this.classlist = {}
                this.course_listaverage = {}
                this.course_listfrequency = {}
                this.teacher_listaverage = {}
                this.teacher_listfrequency = {}
                $('input[type=checkbox]').attr('checked', false);
                $('#listcontrol').show()
                $('#listcontrol').html('&#x25B2;')
                $('#listplay').show()
            },
            clear(name) {
                if(name == 'class') {
                    this.list = {}
                    this.classlist = {}
                }
                if(name == 'course') {
                    this.course_listaverage = {}
                    this.course_listfrequency = {}
                }
                if(name == 'teacher') {
                    this.teacher_listaverage = {}
                    this.teacher_listfrequency = {}
                    $('input[name=item]').attr('checked', false);
                }
                this.check = []
                this.checked = []
                this.tabledata = []
            },
            tabeldata(e, id) {
           
                var checked = e.target.checked
                if(checked) {
                    if(datalist.selectname == 'class') {
                        if(id instanceof Array) {

                            function sendAjaxS(id) {
                                var index = 0;
                                var result = [];
                                sendAjax();

                                function sendAjax() {
                                    if(index >= id.length) {
                                        doSomething(result);
                                        return;
                                    }
                                    if(datalist.ajaxdata[id[index]] == undefined) {
                                        $.ajax({
                                            url: '<?php echo $this->createUrl("campusSummaryData")?>',
                                            type: "POST",
                                            async: true,
                                            dataType: 'json',
                                            data: {
                                                type: datalist.selectname,
                                                id: id[index],
                                                yid: yid
                                            },
                                            success: function(data) {
                                                if(data.state == 'success') {
                                                    result.push(data);
                                                    $('.speed').modal('show')
                                                    var s = index + 1
                                                    datalist.length = Math.round(s / id.length * 10000) / 100.00
                                                    datalist.width.width = Math.round(s / id.length * 10000) / 100.00 + "%"
                                                    datalist.tabledata.push(data.data.average)
                                                    datalist.list[id[index].toString()] = data.data.average
                                                    datalist.ajaxdata[id[index].toString()] = data.data.average
                                                    datalist.classlist[id[index].toString()] = data.data.frequency
                                                    datalist.ajaxclass[id[index].toString()] = data.data.frequency
                                                    index++;
                                                    sendAjax();
                                                } else {
                                                    resultTip({
                                                        error: 'warning',
                                                        msg: data.message
                                                    });
                                                }

                                            },
                                            error: function() {
                                                alert("请求错误")
                                            }
                                        });
                                    } else {
                                        datalist.tabledata.push(datalist.ajaxdata[id])
                                        for(var s = 0; s < id.length; s++) {
                                            Vue.set(datalist.list, id[s], datalist.ajaxdata[id[s]]);
                                            Vue.set(datalist.classlist, id[s], datalist.ajaxclass[id[s]])
                                        }
                                        index++;
                                        sendAjax();

                                    }
                                }
                            }

                            function doSomething(data) {
                                $('.speed').modal('hide')
                            }
                            sendAjaxS(id);
                        } else {
                            if(datalist.ajaxdata[id] == undefined) {
                                $.ajax({
                                    url: '<?php echo $this->createUrl("campusSummaryData")?>',
                                    type: 'post',
                                    dataType: 'json',
                                    data: {
                                        type: datalist.selectname,
                                        id: id,
                                        yid: yid
                                    },
                                    success: function(data) {
                                      
                                        if(data.state == 'success') {

                                            datalist.tabledata.push(data.data.average)
                                            datalist.list[id.toString()] = data.data.average
                                            datalist.ajaxdata[id.toString()] = data.data.average
                                            datalist.classlist[id.toString()] = data.data.frequency
                                            datalist.ajaxclass[id.toString()] = data.data.frequency

                                        } else {

                                            resultTip({
                                                error: 'warning',
                                                msg: data.message
                                            });
                                        }

                                    },
                                    error: function(data) {
                                      

                                        $('.yes').removeClass('disabled').removeAttr('disabled');
                                        $('.yes').html('<?php echo Yii::t('user','确认'); ?>')
                                        resultTip({
                                            error: 'warning',
                                            msg: '请求错误'
                                        });
                                    }
                                })
                            } else {
                                datalist.tabledata.push(datalist.ajaxdata[id])
                                Vue.set(datalist.list, id, datalist.ajaxdata[id]);
                                Vue.set(datalist.classlist, id, datalist.ajaxclass[id])
                            }
                        }
                    }
                    if(datalist.selectname == 'course') {
                        if(id instanceof Array) {
                            function sendAjaxS(id) {
                                var index = 0;
                                var result = [];
                                sendAjax();

                                function sendAjax() {
                                    if(index >= id.length) {
                                   
                                        doSomething(result);
                                        return;
                                    }
                                    if(datalist.ajaxdata[id[index]] == undefined) {
                                        $.ajax({
                                            url: '<?php echo $this->createUrl("campusSummaryData")?>',
                                            type: "POST",
                                            async: true,
                                            dataType: 'json',
                                            data: {
                                                type: datalist.selectname,
                                                id: id[index],
                                                yid: yid
                                            },
                                            success: function(data) {
                                               
                                                if(data.state == 'success') {
                                                    result.push(data);
                                                    $('.speed').modal('show')
                                                    var s = index + 1
                                                    datalist.length = Math.round(s / id.length * 10000) / 100.00
                                                    datalist.width.width = Math.round(s / id.length * 10000) / 100.00 + "%"
                                                    datalist.tabledata.push(data.data.average)
                                                    datalist.course_listaverage[id[index].toString()] = data.data.average
                                                    datalist.course_ajaxaverage[id[index].toString()] = data.data.average
                                                    datalist.course_listfrequency[id[index].toString()] = data.data.frequency
                                                    datalist.course_ajaxfrequency[id[index].toString()] = data.data.frequency

                                                    index++;
                                                    sendAjax();
                                                } else {
                                                    resultTip({
                                                        error: 'warning',
                                                        msg: data.message
                                                    });
                                                }

                                            },
                                            error: function() {
                                                alert("请求错误")
                                            }
                                        });
                                    } else {
                                        for(var s = 0; s < id.length; s++) {
                                            datalist.tabledata.push(datalist.course_ajaxaverage[id[s]])
                                            Vue.set(datalist.course_listaverage, id[s], datalist.course_ajaxaverage[id[s]]);
                                            Vue.set(datalist.course_listfrequency, id[s], datalist.course_ajaxfrequency[id[s]])
                                        }

                                    }
                                }
                            }

                            function doSomething(data) {
                                $('.speed').modal('hide')
                            }
                            sendAjaxS(id);
                        } else {
                            if(datalist.course_ajaxaverage[id] == undefined) {

                                $.ajax({
                                    url: '<?php echo $this->createUrl("campusSummaryData")?>',
                                    type: 'post',
                                    dataType: 'json',
                                    data: {
                                        type: datalist.selectname,
                                        id: id,
                                        yid: yid

                                    },
                                    success: function(data) {
                                     
                                        if(data.state == 'success') {

                                            datalist.tabledata.push(data.data.average)
                                            datalist.course_listaverage[id.toString()] = data.data.average
                                            datalist.course_ajaxaverage[id.toString()] = data.data.average
                                            datalist.course_listfrequency[id.toString()] = data.data.frequency
                                            datalist.course_ajaxfrequency[id.toString()] = data.data.frequency

                                        } else {

                                            resultTip({
                                                error: 'warning',
                                                msg: data.message
                                            });
                                        }

                                    },
                                    error: function(data) {
                                       

                                        $('.yes').removeClass('disabled').removeAttr('disabled');
                                        $('.yes').html('<?php echo Yii::t('user','确认'); ?>')
                                        resultTip({
                                            error: 'warning',
                                            msg: '请求错误'
                                        });
                                    }
                                })
                            } else {
                                datalist.tabledata.push(datalist.course_ajaxaverage[id])
                                Vue.set(datalist.course_listaverage, id, datalist.course_ajaxaverage[id]);
                                Vue.set(datalist.course_listfrequency, id, datalist.course_ajaxfrequency[id])
                            }
                        }
                    }
                    if(datalist.selectname == 'teacher') {
                        if(datalist.teacher_ajaxaverage[id] == undefined) {

                            $.ajax({
                                url: '<?php echo $this->createUrl("campusSummaryData")?>',
                                type: 'post',
                                dataType: 'json',
                                data: {
                                    type: datalist.selectname,
                                    id: id,
                                    yid: yid
                                },
                                success: function(data) {
                                  
                                    if(data.state == 'success') {

                                        datalist.tabledata.push(data.data.average)
                                        datalist.teacher_listaverage[id.toString()] = data.data.average
                                        datalist.teacher_ajaxaverage[id.toString()] = data.data.average
                                        datalist.teacher_listfrequency[id.toString()] = data.data.frequency
                                        datalist.teacher_ajaxfrequency[id.toString()] = data.data.frequency

                                    } else {

                                        resultTip({
                                            error: 'warning',
                                            msg: data.message
                                        });
                                    }

                                },
                                error: function(data) {
                                   
                                    $('.yes').removeClass('disabled').removeAttr('disabled');
                                    $('.yes').html('<?php echo Yii::t('user','确认'); ?>')
                                    resultTip({
                                        error: 'warning',
                                        msg: '请求错误'
                                    });
                                }
                            })
                        } else {
                            datalist.tabledata.push(datalist.ajaxdata[id])
                            Vue.set(datalist.teacher_listaverage, id, datalist.teacher_ajaxaverage[id]);
                            Vue.set(datalist.teacher_listfrequency, id, datalist.teacher_ajaxfrequency[id])
                        }
                    }
                } else {
                    if(datalist.selectname == 'class') {
           
                        for(var j = 0; j < datalist.checked.length; j++) {
                            if(datalist.checkcoursename == datalist.checked[j]) {
                                Vue.delete(datalist.checked, datalist.checked.indexOf(datalist.checked[j]));
                            }
                        }
                        datalist.tabledata.splice(0, 1)

                        for(key in datalist.list) {
                            if(id == key) {
                                Vue.delete(datalist.list, key);
                                Vue.delete(datalist.classlist, key);
                            }
                        }
                    }
                    if(datalist.selectname == 'course') {
                        for(var j = 0; j < datalist.checked.length; j++) {
                            if(datalist.checkcoursename == datalist.checked[j]) {
                                Vue.delete(datalist.checked, datalist.checked.indexOf(datalist.checked[j]));
                            }
                        }
                        datalist.tabledata.splice(0, 1)
                        for(key in datalist.course_listaverage) {
                            if(id == key) {
                                Vue.delete(datalist.course_listaverage, key);
                                Vue.delete(datalist.course_listfrequency, key);
                            }
                        }
                    }
                    if(datalist.selectname == 'teacher') {
                        datalist.tabledata.splice(0, 1)
                        for(key in datalist.teacher_listaverage) {
                            if(id == key) {
                                Vue.delete(datalist.teacher_listaverage, key);
                                Vue.delete(datalist.teacher_listfrequency, key);
                            }
                        }
                    }
                }
            }
        }
    })
</script>
<?php
$this->branchSelectParams['extraUrlArray'] = array('//mteaching/reportanalytic/index');
$this->renderPartial('//layouts/common/branchSelectBottom');
?>