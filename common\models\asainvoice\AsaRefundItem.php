<?php

/**
 * This is the model class for table "ivy_asa_refund_item".
 *
 * The followings are the available columns in table 'ivy_asa_refund_item':
 * @property integer $id
 * @property integer $refund_id
 * @property string $site_id
 * @property integer $course_group_id
 * @property integer $course_id
 * @property integer $child_id
 * @property integer $schedule_id
 * @property integer $schedule_item_id
 */
class AsaRefundItem extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_asa_refund_item';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('refund_id, site_id, course_group_id, course_id, child_id, schedule_id, schedule_item_id', 'required'),
			array('refund_id, course_group_id, course_id, child_id, schedule_id, schedule_item_id', 'numerical', 'integerOnly'=>true),
			array('site_id', 'length', 'max'=>255),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, refund_id, site_id, course_group_id, course_id, child_id, schedule_id, schedule_item_id', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'refund_id' => 'Refund',
			'site_id' => 'Site',
			'course_group_id' => 'Course Group',
			'course_id' => 'Course',
			'child_id' => 'Child',
			'schedule_id' => 'Schedule',
			'schedule_item_id' => 'Schedule Item',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('refund_id',$this->refund_id);
		$criteria->compare('site_id',$this->site_id,true);
		$criteria->compare('course_group_id',$this->course_group_id);
		$criteria->compare('course_id',$this->course_id);
		$criteria->compare('child_id',$this->child_id);
		$criteria->compare('schedule_id',$this->schedule_id);
		$criteria->compare('schedule_item_id',$this->schedule_item_id);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return AsaRefundItem the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}
