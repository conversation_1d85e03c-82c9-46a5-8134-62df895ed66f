<?php

use phpDocumentor\Reflection\PseudoTypes\False_;

class DeptController extends BranchBasedController
{
    // 访问action的初级权限
    public $actionAccessAuths = array(
//        'index'                 => 'o_T_Access'
    );

    public $toReplyNum;
    public $type;
    public $managetypeList;
    public $schoolType;
    public $schoolList;
    
    public function init()
    {
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'campusOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site', 'Teaching Tasks');

        //初始化选择校园页面
        $schoolList = CommonUtils::allSchoolList();
        foreach ($this->accessBranch as $key => $item) {
            if (!in_array($item, $schoolList)) {
                unset($this->accessBranch[$key]);
            }
        }
        $this->branchSelectParams['urlArray'] = array('//mcampus/dept/index');
        // 自动重定向未选择学校前的 action
        if ($redirectAction = $_GET['redirectAction']) {
            $this->branchSelectParams['urlArray'] = array('//mcampus/dept/' . $redirectAction);
        }
        $cs = Yii::app()->clientScript;
        $cs->registerCoreScript('jquery.ui');
        $cs->registerCssFile($cs->getCoreScriptUrl() . '/jui/css/base/jquery-ui.css');
        // elementui
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/element/vue.global.js');
        $cs->registerCssFile(Yii::app()->themeManager->baseUrl . '/base/js/element/index.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/element/index.js');
        Yii::import('common.models.portfolio.*');
    }

    public function beforeAction($action) {
        $this->schoolType = 'ivy';
        $this->schoolList = CommonUtils::ivySchoolList();
        if (in_array($_GET['branchId'], CommonUtils::dsSchoolList())) {
            $this->schoolType = 'ds';
            $this->schoolList = CommonUtils::dsSchoolList();
        }
        return parent::beforeAction($action);
    }

    public function createUrl($route, $params = array(), $ampersand = '&', $parentOnly = false)
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        return parent::createUrl($route, $params, $ampersand);
    }

    public function actionIndex()
    {
        $this->render('index');
    }


    // *************************************部门管理****************************************

    /**
     * 部门可选列表
     */
    public function actionDeptChoseList()
    {
        $requestUrl = 'directMessage/dept/choseList';
        $this->remote($requestUrl);
    }

    /**
     * 部门列表
     */
    public function actionDeptList()
    {
        $requestUrl = 'directMessage/dept/list';
        $this->remote($requestUrl);
    }

    /**
     * 部门详情
     */
    public function actionDeptView()
    {
        $deptId = Yii::app()->request->getParam('deptId');
        $requestUrl = 'directMessage/dept/view/' . $deptId;
        $this->remote($requestUrl);
    }

    /**
     * 部门保存
     */
    public function actionDeptSave()
    {
        $requestUrl = 'directMessage/dept/save';
        $requestData = array(
            'deptId' => Yii::app()->request->getParam('deptId'),
            'data' => Yii::app()->request->getParam('data', array()),
        );
        $this->remote($requestUrl, $requestData);
    }

    /**
     * 部门删除
     */
    public function actionDeptDel()
    {
        $deptId = Yii::app()->request->getParam('deptId');
        $requestUrl = 'directMessage/dept/del/' . $deptId;
        $this->remote($requestUrl);
    }

    /**
     * 部门添加员工
     */
    public function actionDeptStaffAdd()
    {
        $deptId = Yii::app()->request->getParam('deptId');
        $staffId = Yii::app()->request->getParam('staffId');
        $requestUrl = "directMessage/dept/addStaff/$deptId/$staffId";
        $this->remote($requestUrl);
    }

    /**
     * 部门删除员工
     */
    public function actionDeptStaffDel()
    {
        $deptId = Yii::app()->request->getParam('deptId');
        $staffId = Yii::app()->request->getParam('staffId');
        $requestUrl = "directMessage/dept/delStaff/$deptId/$staffId";
        $this->remote($requestUrl);
    }

    /**
     * 部门排序
     */
    public function actionDeptSort()
    {
        $requestUrl = "directMessage/dept/sort";
        $requestData = array(
            'deptIdList' => Yii::app()->request->getParam('deptIdList'),
        );
        $this->remote($requestUrl, $requestData);
    }

    public function actionContactList()
    {
        $requestUrl = "directMessage/contact/list";
        $this->remote($requestUrl);
    }

    /**
     * 部门二维码
     */
    public function actionDeptQrcode()
    {
        $deptId = Yii::app()->request->getParam('deptId');
        $requestUrl = "directMessage/dept/qrcode";
        $requestData = array(
            'deptId' => $deptId,
        );
        $this->remote($requestUrl, $requestData);
    }

    /**
     * 可选老师搜索
     *
     * @return void
     */
    public function actionTeacherSearch()
    {
        $requestData = array(
            'searchString' => Yii::app()->request->getParam('searchString'),
        );
        $requestUrl = 'directMessage/teacherSearch';
        $this->remote($requestUrl, $requestData);
    }


    // *************************************方法********************************************

    public function remote($requestUrl, $requestData = array())
    {
        $requestData['schoolId'] = $this->branchId;
        $res = CommonUtils::requestDsOnline($requestUrl, $requestData);
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('message', $res['msg']);
            $this->addMessage('data', $res['data']);
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->addMessage('data', $res['data']);
        }
        $this->showMessage();
    }
}
