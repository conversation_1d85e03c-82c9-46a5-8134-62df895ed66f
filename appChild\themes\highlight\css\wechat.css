#Qr-return{padding: 0 20px;font-size:11px;}
/*#bound-box h3#summary{*/
.wc-btn, .wc-btn-disabled{
  font-family: "Microsoft Yahei";
  border: 0;
  cursor: pointer;
  text-align: center;
  padding: 10px 0;
  background: #F36601;
  margin-bottom: 20px;
  font-size: 12px;
  font-weight: normal;
  color: #f2f2f2;
}
.wc-btn-disabled{
  color: #ddd;
  background: #acacac;
}
#bound-box h3#summary em{
  display: block;
  font-size: 24px;
  font-style: normal;
  padding: 10px;
}
.wc-btn:hover{
  color:#fff;
}
#bound-list .loading, #Qr-return .loading{
  text-align: center;
  background: url("../images/loading.gif") no-repeat left top transparent !important;
}
.pl-28{
  padding-left: 28px;
}
#bound-list li{
  color: #666;
  margin-bottom: 10px;
  border-bottom: 1px dotted #999;
  padding-bottom: 6px;
  padding-left: 6px;
  position: relative;
  -webkit-text-size-adjust:none; 
}
#bound-list li:hover{
  color:#333;
}
#bound-list li h3{
  font-size: 14px;
  margin-bottom: 4px;
  color:#F36601;
}
#bound-list li .small{
  margin-bottom: 0;
  color:#999;
}
#bound-list li .hover{
  display: none;
}
#bound-list li:hover .hover{
  display: block;
}
#bound-list li em.unbind{
  position: absolute;
  left: 170px;
  bottom: 4px;
  cursor: pointer;
  width:24px;
  height: 24px;
  overflow: hidden;
  text-indent: -9999px;
  background: url("../images/icons/refunded.png") no-repeat left top transparent;
}
.location span{
  margin-right: 4px;
}