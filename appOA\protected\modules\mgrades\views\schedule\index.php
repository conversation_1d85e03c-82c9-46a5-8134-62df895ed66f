<?php
$timePoints = explode(',', $calendar->timepoints);
$labels = GradesSchedule::model()->attributeLabels();
foreach ($programCfg['schedules'] as $key=>$val){
    $programList[$key] = $key;
}
$classListLabels = array();
$classList = IvyClass::getClassList($this->branchId, $calendar->yid, true);
if (!empty($classList)){
    foreach ($classList as $val){
        $allClass[$val->classid] = $val->title;
    }
}
?>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo Yii::t('site','Campus Workspace');?></li>
        <li class="active"><?php echo Yii::t('campus','Course Schedule');?></li>
    </ol>

    <div class="row">
        <div class="col-md-4">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <?php echo sprintf("%s - %s", $calendar->startyear, $calendar->startyear + 1); ?>
                </div>
                <div class="panel-body">

                    <ul class="list-unstyled">
                        <li>
                            <h4>
                                <span class="glyphicon glyphicon-calendar"></span> Semester 1#
                                <small>
                                    <?php echo sprintf('%s - %s',
                                        OA::formatDateTime($timePoints[0], 'medium'),
                                        OA::formatDateTime($timePoints[1], 'medium')); ?>
                                </small>
                            </h4>
                        </li>
                        <li>
                            <h4>
                                <span class="glyphicon glyphicon-calendar"></span> Semester 2#
                                <small>
                                    <?php echo sprintf('%s - %s',
                                        OA::formatDateTime($timePoints[2], 'medium'),
                                        OA::formatDateTime($timePoints[3], 'medium')); ?>
                                </small>
                            </h4>
                        </li>
                    </ul>

                    <div id="schedule-list-wrapper">
                        <!--place holder-->
                    </div>

                    <div>
                        <div class="text-right">
                            <button type="button" class="btn btn-primary" onclick="openEditScheduleModal(0);">
                                <span class="glyphicon glyphicon-plus"></span>
                                New Schedule Plan</button>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


<!-- Modal -->
<div class="modal fade" id="editScheduleModal" aria-labelledby="editScheduleModalLabel">
    <div class="modal-dialog">
        <?php echo CHtml::form($this->createUrl('saveSchedulePlan'), 'post', array('class'=>'J_ajaxForm form-horizontal'));?>
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                <h4 class="modal-title">Schedule Plan</h4>
            </div>

            <div class="panel-body form-horizontal" id="splan-add-form">
                <!-- place holder -->
            </div>

            <div class="modal-footer pop_bottom">
                <button type="button" class="btn btn-default" onclick="$('#editScheduleModal').modal('hide')">Cancel</button>
                <button type="button" class="btn btn-primary J_ajax_submit_btn">Submit</button>
            </div>
        </div>
        <?php echo CHtml::endForm();?>
    </div>
</div>

<!--schedule form template-->
<script type="text/template" id="schedule-plan-form-template">
    <?php echo CHtml::hiddenField('GradesSchedule[schoolid]', $this->branchId); ?>
    <?php echo CHtml::hiddenField('GradesSchedule[yid]', $calendar->yid); ?>
    <?php echo CHtml::hiddenField('GradesSchedule[startyear]', $calendar->startyear); ?>
    <?php echo CHtml::hiddenField('GradesSchedule[id]', '<%- id %>', array('encode'=>false)); ?>
    <div class="form-group">
        <?php echo CHtml::label($labels['label'], CHtml::getIdByName('GradesSchedule[label]'),array('class'=>"control-label col-sm-3")) ?>
        <div class="col-sm-9">
            <?php echo CHtml::textField('GradesSchedule[label]', '<%- label %>', array('class'=>'form-control', 'encode'=>false)) ?>
        </div>
    </div>
    <div class="form-group">
        <?php echo CHtml::label($labels['valid_from'], CHtml::getIdByName('GradesSchedule[valid_from]'),array('class'=>"control-label col-sm-3")) ?>
        <div class="col-sm-9">
            <?php echo CHtml::textField('GradesSchedule[valid_from]', '<%- valid_from_label %>', array('class'=>'form-control','encode'=>false)) ?>
        </div>
    </div>
    <div class="form-group">
        <?php echo CHtml::label($labels['valid_to'], CHtml::getIdByName('GradesSchedule[valid_to]'),array('class'=>"control-label col-sm-3")) ?>
        <div class="col-sm-9">
            <?php echo CHtml::textField('GradesSchedule[valid_to]', '<%- valid_to_label %>', array('class'=>'form-control','encode'=>false)) ?>
        </div>
    </div>
    <div class="form-group">
        <?php echo CHtml::label($labels['schedule_code'], CHtml::getIdByName('GradesSchedule[schedule_code]'),array('class'=>"control-label col-sm-3")) ?>
        <div class="col-sm-9">
            <?php echo CHtml::dropDownList('GradesSchedule[schedule_code]', '',$programList ,array('class'=>'form-control','empty'=>Yii::t('global','Please Select')));?>
        </div>
    </div>
    <div class="form-group">
        <?php echo CHtml::label($labels['classid'], CHtml::getIdByName('GradesSchedule[classid]'),array('class'=>"control-label col-sm-3")) ?>
        <div class="col-sm-9">
            <?php //echo CHtml::dropDownList('GradesScheduleItem[classid]', '',array(),array('class'=>'form-control','multiple'=>'multiple','size'=>20));?>
            <?php echo CHtml::checkBoxList('GradesScheduleItem[classid]','',$allClass,array('class'=>'checkbox-inline','encode'=>false, 'separator'=>'<br>'));?>
        </div>
    </div>
</script>

<script type="text/template" id="schedule-plan-item">
    <li class="list-group-item">
        <span class="glyphicon glyphicon-chevron-right"></span>
        <a href="<?php echo $this->createUrl('//mgrades/schedule/index')?>&scheduleid=<%- id %>"><%- label %> <%- valid_from_label %> - <%- valid_to_label %></a>
    <span class="pull-right"><a href="<?php echo $this->createUrl('delSchedule')?>&scheduleid=<%- id %>" class="J_ajax_del"> <span class="glyphicon glyphicon-remove"></span></a> </span>
    <span class="pull-right mr10"><a href="javascript:;" onclick="openEditScheduleModal(<%- id %>)"> <span class="glyphicon glyphicon-pencil"></span></a> </span>
    </li>
</script>

<script>
    var openEditScheduleModal = null;
    var renderPlanList = null;
    var cbSchedulePlanSave = null; //保存成功的回调
    var scheduleFormTemplate = _.template($('#schedule-plan-form-template').html());
    var scheduleItemTemplate = _.template($('#schedule-plan-item').html());
    var scheduleData = <?php echo CJSON::encode($scheduleData);?>;
    var allClass = <?php echo CJSON::encode($allClass);?>;
    $(function(){
        renderPlanList = function(){
            if(_.isEmpty(scheduleData)){
                scheduleData = {};
                $('#schedule-list-wrapper').html('<div class="alert alert-warning" role="alert">尚未建立课表计划</div>');
            }else{
                $('#schedule-list-wrapper').empty().append($('<div class="list-group"></div>'));
                var _sortedScheduleData = _.sortBy(scheduleData, 'valid_from');
                _.each(_sortedScheduleData, function(_s, _key){
                    $('#schedule-list-wrapper .list-group').append(scheduleItemTemplate(_s));
                });
            }
            head.Util.ajaxDel($('#schedule-list-wrapper'));
        }

        renderPlanList();

        cbSchedulePlanSave = function(data){
            scheduleData[data.item.id] = data.item;
            renderPlanList();
            $('#editScheduleModal').modal('hide');
        }

        openEditScheduleModal = function(id){
            if(id){
                var item = scheduleData[id];
            }else{
                var item = {id: 0, label:'',valid_from:'',valid_to:'',valid_from_label:'',valid_to_label:''};
            }
            $('#splan-add-form').html(scheduleFormTemplate(item));
           // $('#splan-add-form #GradesScheduleItem_classid').empty();
            var assignClass = getAssignClass(id);
            if (!_.isEmpty(allClass)){
                if(assignClass.byBranch){
                    _.each(assignClass.byBranch,function(val,key){
                        $('#GradesScheduleItem_classid input[value="'+key+'"]').attr('disabled', true);
                    });
                }
                if (assignClass.bySchedule){
                    _.each(assignClass.bySchedule,function(val,key){
                        $('#GradesScheduleItem_classid input[value="'+key+'"]').removeAttr('disabled').attr('checked', true);
                    });
                }
            }
            $('#splan-add-form #GradesSchedule_schedule_code').val(item.schedule_code);
            $('#splan-add-form #GradesSchedule_valid_from').datepicker({'changeMonth':true,'changeYear':true,'dateFormat':'yy-mm-dd'});
            $('#splan-add-form #GradesSchedule_valid_to').datepicker({'changeMonth':true,'changeYear':true,'dateFormat':'yy-mm-dd'});

            $('#editScheduleModal').modal();
        };

        cbDelPlan = function(data){
            delete scheduleData[data.scheduleid];
            renderPlanList();
        }
        
        getAssignClass = function(id){
            var assignClass = '';
            $.ajax({
                type: "POST",
                url: '<?php echo $this->createUrl('//mgrades/schedule/getAssignClass');?>',
                data: "id=" +id,
                dataType: 'json',
                async: false,
                success: function(data) {
                    if(data.state=='success'){
                        assignClass = data.data
                    }
                    else{
                        alert('Error!');
                    }
                }
            });
            return assignClass;
        }
    })
</script>