<?php

class GovController extends ProtectedController
{
    public $actionAccessAuths = array(
        'index'            => 'o_A_Access',
        'query'            => 'o_A_Access',
        'viewItem'         => 'o_A_Access',
        'export'           => 'o_A_Access',
        'update'           => 'o_A_Access',
        'pay'              => 'ivystaff_finance',
    );

    public function init(){
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'main';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site','Finance Mgt');
    }

	public function actionIndex()
	{
        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/underscore-min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/backbone-min.js');

        $months = array();
        $i=(date('m')-6);
        while(($mTime = mktime(0, 0, 0, $i, 1, date('Y'))) < mktime(0, 0, 0, date('m'), 1, date('Y'))){
            $months[date('Ym', $mTime)] = date('Y-m', $mTime);
            $i++;
        }

        $branchs = Branch::model()->getBranchList();

        $this->render('index', array('months'=>$months, 'branchs'=>$branchs));
	}

    public function actionQuery()
    {
        if(Yii::app()->request->isPostRequest && Yii::app()->request->isAjaxRequest){
            Yii::import('common.models.subsidy.*');
            $month = Yii::app()->request->getPost('month', 0);

            if($month){
                $criteira = new CDbCriteria();
                $criteira->compare('status', 0);
                $criteira->compare('startmonth', $month);
                $criteira->compare('endmonth', $month);
                $items = BatchInvoices::model()->findAll($criteira);
                $data = array();
                foreach($items as $item){
                    $data[$item->schoolid][$item->payment_type] = array(
                        'id' => $item->id,
                        'month' => $month,
                        'type' => $item->payment_type,
                        'amount' => $item->amount,
                        'title' => $item->title,
                        'schoolid' => $item->schoolid,
                        'create_timestamp' => date('Y-m-d H:i', $item->create_timestamp),
                    );
                }
                $this->addMessage('state', 'success');
                $this->addMessage('message', '查询成功');
                $this->addMessage('data', $data);
                $this->addMessage('callback', 'callback');
            }
            else{
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '请选择月份');
            }
            $this->showMessage();
        }
    }

    public function actionViewItem($id=0)
    {
        if($id){
            Yii::import('common.models.subsidy.*');
            Yii::import('common.models.invoice.*');
            $ret = array();
            $items = BatchInvoicesItem::model()->with(array('invoice', 'child'))->findAllByAttributes(array('batch_id'=>$id));
            foreach($items as $item){
                $ret[] = array(
                    'id' => $item->invoice->invoice_id,
                    'name' => $item->child->getChildName(),
                    'title' => $item->invoice->title,
                    'amount' => $item->invoice->amount,
                    'childid' => $item->childid,
                );
            }

            echo CJSON::encode($ret);
        }
    }

    public function actionExport($id=0)
    {
        if($id){
            Yii::import('common.models.subsidy.*');
            Yii::import('common.models.invoice.*');
            $model = BatchInvoices::model()->findByPk($id);
            $items = BatchInvoicesItem::model()->with(array('invoice', 'child'))->findAllByAttributes(array('batch_id'=>$id));
            header("Content-type:application/vnd.ms-excel");
            header("Content-Disposition:attachment;filename=".$model->school->abb.' '.$model->title.".xls");
            $total = 0;
            foreach($items as $item){
                echo $item->childid."\t";
                echo $this->_t($item->child->getChildName())."\t";
                echo $item->invoice->amount."\n";
                $total += $item->invoice->amount;
            }
            echo "\t";
            echo "\t";
            echo $total."\n";
        }
    }

    public function _t($str='')
    {
        return iconv('utf-8', 'gbk', $str);
    }

    public function actionUpdate()
    {
        $id = Yii::app()->request->getPost('id', 0);
        if($id){
            Yii::import('common.models.subsidy.*');
            Yii::import('common.models.invoice.*');

            $model = BatchInvoices::model()->findByPk($id);
            if($model->status == 0){
                $sql = "delete from ".BatchInvoicesItem::model()->tableName()." where batch_id=".$id;
                Yii::app()->db->createCommand($sql)->execute();
                if($model->delete()){
                    $newModel = new BatchInvoices();
                    $newModel->attributes = $model->attributes;
                    $newModel->create_timestamp = time();
                    $newModel->create_userid = Yii::app()->user->id;
                    $newModel->save();

                    $items = Invoice::model()->getBatchInvoicesData($model->schoolid, $model->startmonth, $model->payment_type);
                    if($items){
                        $amount = 0;
                        foreach($items as $item){
                            if ($item->inout == 'in')
                            {
                                $amount += $item->amount;
                                $itemModel = new BatchInvoicesItem();
                                $itemModel->batch_id = $newModel->id;
                                $itemModel->schoolid = $item->schoolid;
                                $itemModel->childid = $item->childid;
                                $itemModel->invoice_id = $item->invoice_id;
                                $itemModel->save();
                            }
                            else
                            {
                                $amount -= $item->amount;
                            }
                        }
                        $newModel->amount = $amount;
                        $newModel->save();
                    }
                    $this->addMessage('state', 'success');
                    $this->addMessage('callback', 'cbUpdate');
                    $this->addMessage('message', '更新成功!');
                }
            }
            else{
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '更新失败!');
            }
            $this->showMessage();
        }
    }

    public function actionPay()
    {
        $id = Yii::app()->request->getPost('id', 0);
        if($id){
            Yii::import('common.models.subsidy.*');
            Yii::import('common.models.invoice.*');
            Yii::import('application.components.policy.*');

            $model = BatchInvoices::model()->findByPk($id);
            if($model->status == 0){
                $ret = 0;
                if($model->payment_type == 'preschool_subsidy'){
                    $items = BatchInvoicesItem::model()->findAllByAttributes(array('batch_id'=>$id));
                    $invoiceids = array();
                    foreach($items as $item){
                        $invoiceids[$item->invoice_id] = $item->invoice_id;
                    }
                    $criteria = new CDbCriteria();
                    $criteria->compare('invoice_id', $invoiceids);
                    $invoiceModels = Invoice::model()->findAll($criteria);
                    $total = 0;
                    foreach($invoiceModels as $invoice){
                        $total += $invoice->amount;
                    }
                    if($total != $model->amount){
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', '金额不对请检查数据!');
                        $this->showMessage();
                    }
                    $policyApi = new PolicyApi($model->schoolid);
                    $ret = $policyApi->pay($invoiceModels, InvoiceTransaction::TYPE_TRANSFER, $model->amount);
                    if(!$ret){
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', $ret);
                    }
                }
                if(!$ret){
                    $model->status = 1;
                    $model->update_timestamp = time();
                    $model->create_userid = Yii::app()->user->id;
                    if($model->save()){
                        $this->addMessage('state', 'success');
                        $this->addMessage('callback', 'cbUpdate');
                        $this->addMessage('message', '成功!');
                    }
                    else{
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', '失败!');
                    }
                }
            }
            $this->showMessage();
        }
    }
}