<style>
    .font16{
        font-size:16px
    }
    .icon{
        width:16px;
        height:16px;
        margin-top:2px
    }
    .fee{
        width: 16px;
        height: 16px;
        
        border-radius: 1px;
        color:#fff;
        display:inline-block;
        text-align:center;
        line-height:16px;
        font-size:12px
    }
    .blue{
        background: #4D88D2;
    }
    .green{
        background: #5CB85C;
    }
    .childList{      
        border: 1px solid #E8EAED;
        border-radius: 4px;
        padding:16px 0 16px 16px;
        height:100px
    }
    .flexStart{
        align-items: flex-start;
    }
    .classText{
        border-radius: 4px;
        border: 1px solid #E8EAED;
        display:inline-block;
        background: #EBEDF0;
        border-radius: 100px 0px 0px 100px;
        padding: 2px 5px;
        float: right;
    }
    .border{
        border: 1px solid #cccccc;
        border-radius: 4px;
        width: 20px;
        height: 22px;
        font-size: 12px;
        text-align: center;
        line-height: 21px;
    }
    .expected{
        border: 1px solid #D9534F;
        position: relative;
    }
    .expectedText{
        position: absolute;
        left: -1px;
        top: -10px;
        background: #D9534F;
        border-radius: 2px;
        color: #fff;
        padding: 2px 5px;
    }
    .historyNum{
        display:inline-block;
        width: 16px;
        height: 16px;
        background: #999999;
        color:#fff;
        border-radius:50%;
        text-align: center;
        line-height: 16px;
        margin-top:2px
    }
    .historyBorder{
        position: absolute;
        border-left: 1px dashed #ccc;
        height: 100%;
        left: 8px;
        top: 20px;
    }
    .w90{
        width:90px
    }
    .width24{
        width:24px
    }
    .PayFee{
        background: #F6FCF4;
    }
    .orange{
        color:#F0AD4E
    }
    [v-cloak] {
		display: none;
	}
    .borderLeft{
        border-left: 1px solid #D9D9D9;
    }
    .plr20{
        padding-right: 20px;
        padding-left: 20px;
    }
    .font20{
        font-size:24px
    }
    .font_bold{
        font-weight: bold
    }
    .border10{
        border-radius:10px
    }
</style>
<?php

$_yids = $this->getCalendars();
$yid = $_yids['nextYid'];
?>
<?php if (is_null($yid)):?>

    <div class="col-md-5">
        <div class="alert alert-danger p20">尚未建立下学年校历或班级，请完成后再尝试。</div>
    </div>

<?php else: ?>
<div id='container' v-cloak>
    <div class="col-md-10" v-if='Object.keys(dataList).length!=0'>
        <div class="row mb20">
            <div class="flex mb20 mt20 align-items font20">
                <span class="font20 fontBold color3"><?php echo Yii::t('site','Total Enrolled');?> </span>
                <span class="label label-default ml5" >{{dataList.student_total}}</span>
            </div>
        </div>
        <div class="">
            <div class="pull-left mr20">
                <div class='flex'>
                    <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/child.png' ?>" class='icon' alt="">
                    <div class='flex1 ml5'>
                        <div class='color6 font14'>下学年已分班</div>
                        <div class='color3 font16 mt5'>{{dataList.next_student_total}}人</div>
                    </div>
                </div>
            </div>
            <div class="pull-left borderLeft plr20">
                <div class='flex'>
                    <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/tuition_fee.png' ?>" class='icon' alt="">
                    <div class='flex1 ml10'>
                        <div class='color6 font14'>下学年已缴费</div>
                        <div class='color3 font16 mt5'>{{dataList.pay_student_total}}人 <span class='font12 color6 ml5'>定位金：{{dataList.pay_deposit_student_total}}人，学费：{{dataList.pay_tuition_student_total}}人</span></div>
                    </div>
                </div>
            </div>
            <div class="pull-left borderLeft plr20">
                <div class='flex'>
                    <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/lost_staff.png' ?>" class='icon' alt=""> 
                    <div class='flex1 ml10'>
                        <div class='color6 font14'>预计流失</div>
                        <div class='color3 font16 mt5'>{{dataList.aropping_out_student_total}}人 <span class='font12 color6 ml5'>其中新生：{{dataList.dropping_out_new_student_total}}人</span></div>
                    </div>
                </div>
            </div>
        </div>
        <div class='clearfix'></div>
        <div>
            <div class="panel panel-default mt20" v-for='(data,index) in dataList.have_students_class_list'>
                <div class="panel-heading">
                    <label class="checkbox-inline">
                        <input type="checkbox" id="inlineCheckbox1" value="option1" v-model='data.checked' @change='allChecked(data)'><span class='color3 font14 font_bold'> {{data.title}}</span>  <span class="label label-default ml10 mr10 border10">{{dataList.student_list[data.classid].length}}</span>
                        <span class='ml20 font14'>流失：{{data.dropping_out_total}}</span> 
                        <span class='ml20 font14'>已支付：{{data.pay_total}}</span> 
                    </label>
                    <button type="button" class="btn btn-primary pull-right ml10" @click='saveAssign(data)'>保存</button>
                    <div class="form-inline pull-right">                   
                    <select class="form-control " v-model='data.assignClass'>
                        <option value=''>分配班级或设置状态</option>
                        <optgroup label="分配到未来学年">
                            <option v-for='(list,id) in dataList.next_class_list' :value='list.select_class_value'>{{list.title}}</option>
                        </optgroup>
                        <optgroup label="设置状态">
                            <option value="setGraduated">设置为已毕业</option>
                            <option value="setDroppedout">设置为已退学</option>
                        </optgroup>
                    </select>
                    </div>
                    <div class='clearfix'></div>
                </div>
                <div class="panel-body">
                    <div class="col-md-4 " v-for='(list,idx) in dataList.student_list[data.classid]'>
                        <div :class='list.depositStatus==1 || list.tuitionStatus==1?"PayFee":""'>
                            <div class='childList mb20 ' :class='list.aroppingOutStatus==10?"expected":""'>  
                                <span class='expectedText' v-if='list.aroppingOutStatus==10'>预计流失</span>                      
                                <div class='flex flexStart '>
                                    <input type="checkbox" id="inlineCheckbox1" :value="list.id" v-model='data.checkList' @change='Checked(data)'>
                                    <div class='flex1 ml10'>
                                        <div class='color3 '>
                                            <span class='font14 mr15'>{{list.name}}</span>     
                                            <span class=' fee blue' v-if='list.depositStatus==1'>定</span> 
                                            <span class='fee green'  v-if='list.tuitionStatus==1'>学</span>
                                            <div class='pull-right mr20'>
                                                <a class="btn btn-default btn-xs mr10" target="_blank" :href="'<?php echo $this->createUrl('//child/index/index');?>&childid='+list.id" role="button">
                                                    <span class="glyphicon glyphicon-link"></span>
                                                </a>
                                                <div class="dropdown pull-right">
                                                    <button class="btn btn-default dropdown-toggle btn-xs width24" type="button" id="dropdownMenu1" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true">
                                                        <span class="caret"></span>
                                                    </button>
                                                    <ul class="dropdown-menu" aria-labelledby="dropdownMenu1">
                                                        <li><a  href="javascript:;" @click='leaveState("20",list)' v-if='list.aroppingOutStatus==10'>取消预计流失状态</a></li>
                                                        <li><a  href="javascript:;" @click='leaveState("10",list)' v-if='list.aroppingOutStatus==20'>设置为预计流失状态</a></li>

                                                        <li><a href="javascript:;" @click='showHistory(list)'>操作记录</a></li>
                                                    </ul>
                                                </div>
                                            </div>
                                            
                                        </div>
                                        <div class='color6 font12 mt20'>
                                            <span>{{list.gender==1?'男':'女'}} | {{list.age}}</span>
                                            <span class='classText' v-if='list.nextClassTitle!=""'>{{list.nextClassTitle}}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div> 
    <div v-else-if='!showLoading'  class="col-md-10">
        <div class="alert alert-warning" role="alert">暂无数据</div>
    </div>
    <div class="modal fade" id="contentModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("newDS", "设置为预计流失状态");?></h4>
                </div>
                <div class="modal-body">
                    <div v-if='type=="10"'>                   
                        <div class='font14 color3 mb10'>预计流失时间：</div>
                        <div class="form-inline mb20"  >
                            <div class="form-group">
                                <select class="form-control mr20" v-model='effect_time_y'>
                                    <option  value=''>请选择学年</option>
                                    <option v-for='(list,index) in dataList.school_year_value' :value='list.value'>{{list.label}}</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <select class="form-control " v-model='effect_time_s'>
                                    <option value=''>请选择学期</option>
                                    <option value='1'>第一学期结束</option>
                                    <option value='2'> 第二学期结束</option>
                                </select>
                            </div>
                            
                        </div>
                        <div class="checkbox">
                            <label>
                            <input type="checkbox" v-model='child_type'> 是否新生
                            </label>
                        </div>
                    </div>
                    <div v-else class='orange mb10 font14'>
                       <span class='glyphicon glyphicon-info-sign mr5'></span> 此操作将取消预计流失状态
                    </div>
                    <div class='font14 color3 mb5 '>备注说明：</div>
                    <textarea class="form-control" rows="3" v-model='memo'></textarea>
                    <div v-if='historyData.length!=0'>
                        <div class='color6 font14 mt20'>历史记录</div>
                        <div class='flex mt20 flexStart relative' v-for='(list,index) in historyData'>
                            <div><span class='historyNum'>{{index+1}}</span>  </div>
                            <div class='flex1 ml15'>
                                <div class='historyBorder' v-if='index!=historyData.length-1'></div>
                                <div class='font14 color3 mb5'>{{list.state==20?'取消预计流失状态':'设置为预计流失状态'}}</div>
                                <div>
                                    <div class='color6 mt5 flex' v-if='list.state==10'>
                                        <span class='w90 inline-block'>预计流失时间：</span>
                                        <span class='flex1'>{{list.effect_time_desc}}</span>
                                    </div>
                                    <div class='color6 mt5 flex' v-if='list.state==10'>
                                        <span class='w90 inline-block'>是否新生：</span>
                                        <span class='flex1'>{{list.child_type==10?"是":"否"}}</span>
                                    </div>
                                    <div class='color6 mt5 flex'>
                                        <span class='w90 inline-block'>操作时间：</span>
                                        <span class='flex1'>{{list.created_at}}</span>
                                    </div>
                                    <div class='color6 mt5 flex'>
                                        <span class='w90 inline-block'>操作人：</span>
                                        <span class='flex1'>{{list.created_by_name}}</span>
                                    </div>
                                    <div class='color6 mt5 flex'>
                                        <span class='w90 inline-block'>备注说明：</span>
                                        <span class='flex1' v-html='list.memo'></span>
                                    </div>
                                </div>
                            
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                    <button type="button" class="btn btn-primary" @click='saveMemo()'><?php echo Yii::t("newDS", "确认");?></button>
                </div>
                <div class='clearfix'></div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="historyModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("newDS", "操作历史");?></h4>
                </div>
                <div class="modal-body">
                    <div v-if='historyData.length!=0'>
                        <div class='color6 font14'>历史记录</div>
                        <div class='flex mt20 flexStart relative' v-for='(list,index) in historyData'>
                            <div><span class='historyNum'>{{index+1}}</span>  </div>
                            <div class='flex1 ml15'>
                                <div class='historyBorder' v-if='index!=historyData.length-1'></div>
                                <div class='font14 color3 mb5'>{{list.state==20?'取消预计流失状态':'设置为预计流失状态'}}</div>
                                <div>
                                    <div class='color6 mt5 flex' v-if='list.state==10'>
                                        <span class='w90 inline-block'>预计流失时间：</span>
                                        <span class='flex1'>{{list.effect_time_desc}}</span>
                                    </div>
                                    <div class='color6 mt5 flex' v-if='list.state==10'>
                                        <span class='w90 inline-block'>是否新生：</span>
                                        <span class='flex1'>{{list.child_type==10?"是":"否"}}</span>
                                    </div>
                                    <div class='color6 mt5 flex'>
                                        <span class='w90 inline-block'>操作时间：</span>
                                        <span class='flex1'>{{list.created_at}}</span>
                                    </div>
                                    <div class='color6 mt5 flex'>
                                        <span class='w90 inline-block'>操作人：</span>
                                        <span class='flex1'>{{list.created_by_name}}</span>
                                    </div>
                                    <div class='color6 mt5 flex'>
                                        <span class='w90 inline-block'>备注说明：</span>
                                        <span class='flex1' v-html='list.memo'></span>
                                    </div>
                                </div>
                            
                            </div>
                        </div>
                    </div>
                    <div v-else>
                        <div class="alert alert-danger"   role="alert"><?php echo Yii::t('asa','No data found.') ?></div>
                    </div>
                </div>
                <div class='clearfix'></div>
            </div>
        </div>
    </div>
</div> 
    <script>
        var childBaseUrl = '<?php echo $this->createUrl('//child/index/index')."&childid=";?>';
        // $('#historyModal').modal('show')
         var container = new Vue({
        el: "#container",
        data: {
            dataList:{},
            assignClass:'',
            type:'',
            historyData:[],
            effect_time_y:'',
            effect_time_s:'',
            memo:'',
            currentChild:'',
            child_type:false,
            showLoading:false
        },
        watch:{},
        created:function(){
            this.init()
        },
        methods:{
            init(){
                this.showLoading=true
                var that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("getStudentsWithSwitchInfo") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            console.log(data)
                            if(data.data.length==0){
                                that.dataList={}
                               
                            }else{                           
                                data.data.have_students_class_list.forEach((item,index) => {
                                    item.checked=false
                                    item.assignClass=''
                                    item.checkList=[]
                                })
                                that.effect_time_y=data.data.current_year
                                that.dataList=data.data
                            }
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                        that.showLoading=false
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                        that.showLoading=false
                    }
                })
            },
            allChecked(list){
                console.log(list)
                list.checkList=[]
                if(list.checked){
                    this.dataList.student_list[list.classid].forEach((item,index) => {
                        list.checkList.push(item.id)
                    })
                }
            },
            Checked(list){
                if(list.checkList.length==this.dataList.student_list[list.classid].length){
                    list.checked=true
                }else{
                    list.checked=false
                }
            },
            saveAssign(data){
                var that=this
                if(data.checkList.length==0){
                    resultTip({
                        error: 'warning',
                        msg: '请勾选学生'
                    });
                    return
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("assignClasses") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        'assignData[classid]':data.assignClass,
                        'assignData[childid]':data.checkList,

                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            console.log(data)
                            that.init()
                            resultTip({
                                msg: data.message
                            });
                            
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                        that.showLoading=false
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                        that.showLoading=false
                    }
                })
            },
            leaveState(type,list){
                this.type=type
                let that=this
                this.currentChild=list.id
                this.effect_time_s=''
                this.memo=''
                $.ajax({
                    url: '<?php echo $this->createUrl("getOutflowLog") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        'child_id':list.id,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            console.log(data)
                            that.historyData=data.data
                            $('#contentModal').modal('show')
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                        that.showLoading=false
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                        that.showLoading=false
                    }
                })
            },
            saveMemo(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("saveOutflow") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        'child_id':this.currentChild,
                        state: this.type,
                        'submit_data[effect_time_s]':this.effect_time_s,
                        'submit_data[memo]':this.memo,
                        'submit_data[effect_time_y]':this.effect_time_y,
                        'submit_data[child_type]':this.child_type?10:20,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            console.log(data)
                            that.init()
                            resultTip({
                                msg: data.state
                            });
                            $('#contentModal').modal('hide')
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                        that.showLoading=false
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                        that.showLoading=false
                    }
                })
            },
            showHistory(list){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("getOutflowLog") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        'child_id':list.id,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            console.log(data)
                            that.historyData=data.data
                            $('#historyModal').modal('show')
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                        that.showLoading=false
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                        that.showLoading=false
                    }
                })
            }
        }
    })
    </script>

<?php endif; ?>