<?php

/**
 * This is the model class for table "ivy_easyapply_invoice".
 *
 * The followings are the available columns in table 'ivy_easyapply_invoice':
 * @property integer $id
 * @property integer $invoiceid
 * @property integer $invoiceid_oa
 * @property string $trade_ids
 * @property string $apply_id
 * @property string $apply_year
 * @property integer $child_id
 * @property integer $class_id
 * @property integer $updated_by
 * @property integer $updated_at
 */
class EasyapplyInvoice extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_easyapply_invoice';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('invoiceid, invoiceid_oa, apply_id, apply_year, child_id, class_id, updated_by, updated_at', 'required'),
			array('invoiceid, invoiceid_oa, child_id, class_id, updated_by, updated_at', 'numerical', 'integerOnly' => true),
			array('trade_ids, apply_id', 'length', 'max' => 255),
			array('apply_year', 'length', 'max' => 10),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, invoiceid, invoiceid_oa, trade_ids, apply_id, apply_year, child_id, class_id, updated_by, updated_at', 'safe', 'on' => 'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array();
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'invoiceid' => 'Invoiceid',
			'invoiceid_oa' => 'Invoiceid Oa',
			'trade_ids' => 'Trade Ids',
			'apply_id' => 'Apply',
			'apply_year' => 'Apply Year',
			'child_id' => 'Child',
			'class_id' => 'Class',
			'updated_by' => 'Updated By',
			'updated_at' => 'Updated At',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria = new CDbCriteria;

		$criteria->compare('id', $this->id);
		$criteria->compare('invoiceid', $this->invoiceid);
		$criteria->compare('invoiceid_oa', $this->invoiceid_oa);
		$criteria->compare('trade_ids', $this->trade_ids, true);
		$criteria->compare('apply_id', $this->apply_id, true);
		$criteria->compare('apply_year', $this->apply_year, true);
		$criteria->compare('child_id', $this->child_id);
		$criteria->compare('class_id', $this->class_id);
		$criteria->compare('updated_by', $this->updated_by);
		$criteria->compare('updated_at', $this->updated_at);

		return new CActiveDataProvider($this, array(
			'criteria' => $criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return EasyapplyInvoice the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

}
