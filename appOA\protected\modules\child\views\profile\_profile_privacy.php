<div class="form">
<?php $form=$this->beginWidget('CActiveForm', array(
	'id'=>'child-form',
	'enableClientValidation'=>true,
	'clientOptions'=>array(
		'validateOnSubmit'=>true,
	),
)); ?>
	
	<div class="table_full">
		<table width="100%">
			<colgroup>
				<col width="200">
			</colgroup>
			<tbody>
				<tr>
					<th>
						<?php echo Yii::t("userinfo","Family Directory");?>
					</th>
					<td>
						<div class="mb5">
						<?php echo Yii::t("userinfo","Put a mark in the box for item(s) to be published internally distributed Family Directory.");?>
						</div>
						
						<div class="mb5">
						<?php echo CHtml::activeCheckBox($model,'mother_mobile'); ?>
						<?php echo CHtml::activeLabelEx($model,'mother_mobile') ?>
						</div>
						
						<div class="mb5">
						<?php echo CHtml::activeCheckBox($model,'father_mobile'); ?>
						<?php echo CHtml::activeLabelEx($model,'father_mobile') ?>
						</div>
		
						<div class="mb5">
						<?php echo CHtml::activeCheckBox($model,'mother_email'); ?>
						<?php echo CHtml::activeLabelEx($model,'mother_email') ?>
						</div>
						
						<div class="mb5">
						<?php echo CHtml::activeCheckBox($model,'father_email'); ?>
						<?php echo CHtml::activeLabelEx($model,'father_email') ?>
						</div>
		
						<div class="mb5">
						<?php echo CHtml::activeCheckBox($model,'home_phone'); ?>
						<?php echo CHtml::activeLabelEx($model,'home_phone') ?>
						</div>
					</td>
				</tr>

			</tbody>
		</table>
	</div>
		<div class="btn_wrap_pd">
			<?php
				echo CHtml::submitButton(Yii::t("global", "Save"), array("class"=>"btn btn_submit"));
                foreach(Yii::app()->user->getFlashes() as $key => $message) {
                    echo '<div class="tips_' . $key . '">' . $message . "</div>\n";
                }
			?>
		</div>


	
	
<?php $this->endWidget(); ?>
</div><!-- form -->