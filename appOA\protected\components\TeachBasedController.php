<?php
/**
 * 新版本的 TeachBasedController; 完成之后可以删除 TeachingBasedController
 */
class TeachBasedController extends BranchBasedController
{
    public $weeklyTasks; //周任务
    public $monthlyTasks; //月任务
    public $semesterTasks; //学期任务

	public $optionalData = array();

	public $classSelection = array();


	public $schoolClasses = array();
	public $myClasses = array();
	public $calendarId = array();

	public $showHistory = 0;
	public $historyStartYear = null;
	public $historyYid = null;

	public $teachingTasks = array();

    public function createUrl($route, $params = array(), $ampersand = '&', $parentOnly = false)
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        return parent::createUrl($route, $params, $ampersand);
    }

    public function createUrlwithHistory($route, $params = array(), $ampersand = '&')
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        if ($this->showHistory){
            $params['showHistory'] = $this->showHistory;
        }
        if ($this->historyYid){
            $params['historyYid'] = $this->historyYid;
        }
        return parent::createUrl($route, $params, $ampersand);
    }

	public function init(){
		parent::init();
        $this->showHistory = intval(Yii::app()->request->getParam('showHistory', 0));
        $this->historyYid = intval(Yii::app()->request->getParam('historyYid', 0));

		Yii::import('common.models.classTeacher.*');
		Yii::import('common.models.calendar.*');

        $this->weeklyTasks = array(
            'mi' => array(
                "mediaupload" => Yii::t("teaching", "Photo Upload"),
                "videoupload" => Yii::t("teaching", "Video Upload"),
                "mediamgt" => Yii::t("teaching", "Media Management"),
                "schoolnews" => Yii::t("teaching", "School News"),
                "classnews" => Yii::t("teaching", "Class Notes"),
                "childreport" => Yii::t("teaching", "Special Notes"),
                "specialnotes" => Yii::t("teaching", "Special Notes"),
                "schedule" => Yii::t("teaching", "Weekly Schedule"),
            )
        );

        $this->monthlyTasks = array(
            'mi' => array(
                "learntargets" => Yii::t("teaching", "Unit Plan"),
            )
        );

        $this->semesterTasks = array(
            'mi' => array(
//                "schecklist" => Yii::t("teaching", "Development Checklist"),
                "learning" => Yii::t("curriculum", "Learning Domains"),
                "sreport" => Yii::t("teaching", "Semester Report"),
                "ptc" => Yii::t("teaching", "Parent-Teacher Conference"),
            ),
            'ds' => array(
                "dsreport01" => Yii::t("teaching", "Semester Report"),
                "ptc" => Yii::t("teaching", "Parent-Teacher Conference"),
            )
        );
	}
	
	public function initExt(){
        if($this->branchObj->group == 10){
            unset($this->weeklyTasks['mi']['childreport']);
        }
        else{
            unset($this->weeklyTasks['mi']['specialnotes']);
        }
        $this->schoolClasses['title'] = $this->branchObj->title;
        //历史信息
		if($this->showHistory && $this->historyYid){
            $crit = new CDbCriteria;
            $crit->compare('schoolid', $this->branchId);
            $crit->compare('yid', $this->historyYid);
            $crit->order='child_age,title';

            $this->calendarId = $this->historyYid;

            $historyClasses = IvyClass::model()->findAll($crit);

            foreach($historyClasses as $hclass){
                $this->schoolClasses['items'][$hclass->classid] = $hclass->title;
            }
		}else{ // 当前学年
			$this->calendarId = $this->branchObj->schcalendar;
            $this->schoolClasses['items'] = CHtml::listData($this->branchObj->currentClasses, 'classid', 'title');
            $this->schoolClasses['ages']  = CHtml::listData($this->branchObj->currentClasses, 'classid', 'child_age');
		}

		//针对校园人员
		if(!$this->multipleBranch){
            $this->myClasses = CHtml::listData(ClassTeacher::model()->findAllByAttributes(
                array('yid'=>$this->calendarId, 'teacherid'=>Yii::app()->user->id, 'schoolid'=>$this->staff->profile->branch)
            ), 'classid','classid');
        }
	}
	
	public function getNameList($classid=0, $activeOnly=true, $return='array', $type = 'type'){
		$nameList = array();
        $crit = new CDbCriteria();
		if($this->showHistory && $this->historyYid){
            Yii::import('common.components.teaching.*');
            $childIds = ContentFetcher::getHistoricalStudentsByClass($classid, $this->branchId);
            $crit->compare('childid', $childIds);
		}else{
			$crit->compare('classid', $classid);
			if($activeOnly){
//				$crit->compare('status','<'.ChildProfileBasic::STATS_GRADUATED);
				$crit->compare('status','<='.ChildProfileBasic::STATS_DROPPINGOUT);#2022-7-12退学中的学生也要展示出来
				$crit->compare('status','>'.ChildProfileBasic::STATS_REGISTERED);
			}
		}
        $childObjs = ChildProfileBasic::model()->findAll($crit);
        $parents = array();
		foreach ($childObjs as $item){
            $parents[$item->fid] = $item->fid;
            $parents[$item->mid] = $item->mid;
        }

        $parentOpenid = array();
        $parentinfo = array();
		if($type == 'ptc') {
            Yii::import('common.models.wechat.*');
            $crit = new CDbCriteria();
            $crit->compare('userid', $parents);
            $crit->compare('valid', 1);
            if ($this->branchObj->type == 50) {
                $crit->compare('account', 'ds');
            } else {
                $crit->compare('account', 'ivy');
            }
            $crit->with = 'info';
            $wechatModel = WechatUser::model()->findAll($crit);

            foreach ($wechatModel as $val) {
                $parentOpenid[$val->userid] = 1;
                if($val->info){
                    $information = json_decode($val->info->info);
                    $parentinfo[$val->userid][] = array(
                        'name' => $information->nickname,
                        'headimgurl' => $information->headimgurl,
                    );
                }
            }
        }

        foreach($childObjs as $child){
            $bindingStatus = 0;
            $parnet = array();
            $fidArray = array();
            $midArray = array();
            if($type == 'ptc') {
                if (isset($parentOpenid)) {
                    if ($parentOpenid[$child->fid] || $parentOpenid[$child->mid]) {
                        $bindingStatus = 1;
                    }
                }
                if ($child->fid && $parentinfo[$child->fid]) {
                    $fidArray = $parentinfo[$child->fid];
                }
                if ($child->mid && $parentinfo[$child->mid]) {
                    $midArray = $parentinfo[$child->mid];
                }
            }

            $ret = array(
                'id' => $child->childid,
                'name' => ( Yii::app()->language == 'en_us' )?
                        $child->getChildName(null,null, true):
                        $child->getChildName(true, true),
                'photo' => $child->photo,
                'ccUrl' => OA::genCCUrlHome($child->childid),
                'bindingStatus' => $bindingStatus,
                'parent' => array_merge($fidArray,$midArray),
            );

            if($return == 'array')
                $nameList[$child->childid] = $ret;
            elseif($return == 'json')
                $nameList[] = CJSON::encode($ret);
        }

		return $nameList;
	}

    public function getServerConf($classid=0, $video=false)
    {
        $serverKey = $video ? 'videoServers' : 'servers';
        $serverConfs = CommonUtils::LoadConfig('CfgMediaServer');

        if( isset($serverConfs['byUser'][Yii::app()->user->id]) ){
            $sConfs = $serverConfs[$serverKey][$serverConfs['byUser'][Yii::app()->user->id]];
        }
        elseif( isset($serverConfs['byClass'][$this->selectedClassId]) ){
            $sConfs = $serverConfs[$serverKey][$serverConfs['byClass'][$classid]];
        }
        elseif( isset($serverConfs['byCampus'][$this->branchId]) ){
            $sConfs = $serverConfs[$serverKey][$serverConfs['byCampus'][$this->branchId]];
        }
        else{
            $sConfs = $serverConfs[$serverKey][$serverConfs['defaultServer']];
        }

        return $sConfs;
    }

    public function checkTeachers()
    {
        if($this->selectedClassId){
            if(!$this->calendarId)
                $this->initExt();
            if($this->myClasses){
                if(in_array($this->selectedClassId, $this->myClasses)){
                    return true;
                }
                else{
                    return false;
                }
            }
            return true;
        }
        return false;
    }
}