<?php 
    // 由于微信支付安全目录问题，特殊处理支付页面的跳转URL
    $payMentUrl = Yii::app()->createUrl('wechat/user/unpaidInvoices')
        . '/?openid=' . $_GET['openid']
        . '&access_token=' . $_GET['access_token']
        . '&state=' . $_GET['state'];
 ?>
<link href="<?php echo Yii::app()->theme->baseUrl; ?>/css/swiper.min.css" type="text/css" rel="stylesheet"
      media="screen">

<script src="<?php echo Yii::app()->theme->baseUrl; ?>/js/vue.js"></script>

<script src="<?php echo Yii::app()->theme->baseUrl; ?>/js/swiper.min.js"></script>

<style>
    #fixed-bottom {
        display: block;
        line-height: 50px;
        background-color: #5c5c5c;
        color: #ffffff;
        z-index: 9999;
    }

    .bottom-text {
        padding-left: 10px;
    }

    .buyButton {
        float: right;
    }

    #actionsheet {
        bottom: 50px;
    }

    .shoppingThumbnail {
        width: 80px;
        /*padding-top: 10px;*/
    }

    .clearfix {
        clear: both;
    }

    .weui_media_desc {
        color: #999999;
        font-size: 13px;
        line-height: 1.2;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
    }
    .weui-btn_disabled{
        color:#ccc;
        
    }
    .sup{
        position: relative;
        float: left;
        margin: 1.2em 2.2em .5em .5em;
    }
    .sup a{
        min-width:50px
    }
    .weui-badge{
        position: absolute;
        right: -40px;
        top: -10px;
        padding: 0px 2px;
        background: #ccc;
        font-size: 12px;
        height: 15px;
        border-radius:5px 0;
        color:#fff ;
        line-height:16px;
        min-width:40px;
        text-align:center
    }
    .red-badge{
        background:#E64340
    }
    .toast_content{
        position: fixed;
        z-index:10002;
        top: 50%;
        left: 50%;
        margin-left: -3.8em;
        background: rgba(40, 40, 40, 0.75);
        text-align: center;
        border-radius: 5px;
        color: #FFFFFF;
        padding:5px 10px
    }
</style>

<div class="tabbar" style="height: 100%;" id="shoppingVueModel">

    <div class="bd" style="height: 100%;">
        <div class="weui_tab">
            <div class="weui_tab_bd">
                <div class="tabbar">
                    <div class="bd">
                        <div class="weui_cells_title">{{catTitle}}</div>
                        <div class="weui_panel weui_panel_access" style="margin: 0">
                            <div class="weui_panel_bd">
                                <a href="javascript:void(0);" class="weui_media_box weui_media_appmsg"
                                   v-for="(item,pid) in products">
                                    <div class="weui_media_hd" v-if="(typeof item.imgthumb.all) != 'string'">
                                        <img class="weui_media_appmsg_thumb" @click="detailsBtn(pid)"
                                             :src="item.imgthumb.all[0]">
                                    </div>
                                    <div class="weui_media_bd">
                                        <h4 class="weui_media_title" style="line-height: 1.2;white-space:normal;word-break: normal">
                                            <span
                                                @click="detailsBtn(pid)">{{item.title}}</span>
                                        </h4>
                                        <p class="weui_media_desc">{{item.intro}}</p>
                                    </div>
                                    <div class="weui_media_bd">
                                        <button class="weui_btn weui_btn_mini buyButton weui_btn_primary"
                                                @click="toggleShppingCart(pid,item.pid)"><?php echo Yii::t("wechat", "Purchase"); ?>
                                        </button>
                                    </div>
                                </a>
                            </div>
                        </div>
                        <!---->
                    </div>
                </div>
            </div>

            <div id="fixed-bottom" class="weui_tabbar">
                <!--                <div class="bottom-text" id="" v-if="!isEmpty(shoppingCart)" v-cloak>-->
                <div class="bottom-text" id="" v-if="!isEmpty(shoppingCart)" v-cloak>
                    <p v-if="1"><?php echo Yii::t("wechat", "Select product"); ?></p>

                </div>
                <div class="shopping-cart" id="" v-else v-cloak>
                    <div class="shopping-count">
                        <div class="bottom-text">
                            <p class="weui-footer__text" @click="actionSheet">
                                <?php echo Yii::t("wechat", "Subtotal ({{ allNum }} items) : ¥ {{ allPrice }}"); ?>
                                <span class="caret"></span>
                            </p>
                        </div>
                    </div>
                    <div @click="cartSubmit"
                         class="weui-btn shopping-btn"/><?php echo Yii::t("wechat", "Buy it now"); ?></div>
            </div>
        </div>
    </div>
</div>

<div class="weui_dialog_alert" id="detailsDialog" style="display: none;">
    <div class="weui_dialog" style="width: 100%; height: 100%;z-index: 10000;">
        <div class="weui_dialog_bd" style="padding:0;overflow: scroll;max-height: 93%">
            <div class="swiper-container">
                <div class="swiper-wrapper">

                </div>
                <!-- 如果需要分页器 -->
                <div class="swiper-pagination">

                </div>
            </div>
            <div class="product-info">
                <div class="info-name detailsTitle"></div>
                <div class="info-price">
                    <!--                        <span class="price-shop">￥10.00</span>-->
                    <!--                        <span class="price-market">￥10.00</span>-->
                </div>
            </div>
            <div class="product-intro">
                <div class="intro-header">
                    <?php echo Yii::t("wechat", "Product Description"); ?>
                </div>
                <div class="intro-content">
                    <p style="text-align: center" class="detailsIntro">
                    </p>
                </div>
            </div>
        </div>
        <div class="weui_dialog_ft"
             style="position: fixed; bottom: 0; width: 100%; background:#fff; z-index: 10001; text-align: center;">
            <a href="javascript:" class="weui_btn_dialog primary"
               @click="cancelDetailsDialog"><?php echo Yii::t("wechat", "Back"); ?></a>
        </div>
    </div>
</div>
<div class="weui_mask_transition" id="shoppingCartMask" @click="toggleShppingCart" style="z-index: 10000"></div>
<div class="weui_mask_transition" id="mask" @click="actionSheet"></div>
<div id="toast" style="display: none;">
    <div class="weui_mask_transparent"></div>
    <div class="weui_toast">
        <i class="weui_icon_toast"></i>

        <p class="weui_toast_content"><?php echo Yii::t("wechat", "This item has been added in your Cart"); ?></p>
    </div>
</div>


<div class="weui_actionsheet" id="actionsheet">
    <div class="weui-actionsheet__title"><p
            class="weui-actionsheet__title-text"><?php echo Yii::t("wechat", "My Cart"); ?></p></div>
    <div class="weui_actionsheet_menu" v-if="allNum">

        <div v-for="(cart,productName) in shoppingCart" class="">
            <div class="weui_cell" style="display: block">
                <div class="weui_cell_hd" style="float: left">
                    <img :src="cart.selectImg" alt=""
                         style="width:50px;margin-right:5px;display:block">
                </div>
                <p style="overflow: hidden;text-overflow:ellipsis;white-space: nowrap;" >{{cart.name}}</p>
                
                <div class="weui_cell_bd weui_cell_primary">
             
                    <p class="weui_media_desc" style="display: inline-block;vertical-align: middle;margin-right:5px ">
                            <span v-for="(o,oKey,optionIndex) in cart.options">
                                <span v-for="_o in o">
                                    <span v-if="optionIndex < isEmpty(cart.options) - 1">{{ _o }}, </span>
                                    <span v-else>{{ _o }}</span>
                                </span>
                            </span>

                    </p>

                    <p class="tb-promo-price" style="font-size: 13px;line-height: 1.2;vertical-align: middle" >
                        ¥ {{toFixed(cart.num * cart.price)}}
                    </p>
                    <p class="tb-promo-price kucun" style="font-size: 13px;line-height: 1.2;vertical-align: middle;color: #E64340;" :id='cart.paid' >
                         
                    </p>
                    <div style="float: right;vertical-align: middle">
                        <a href="javascript:" class="weui_btn weui_btn_mini weui_btn_default"
                           @click="cartMinusNum(productName)"
                           style="vertical-align: bottom;"> - </a>
                        <input type="number" :value="cart.num" readonly class="weui_btn weui_btn_mini weui_btn_default"
                               style="width: 40px;margin-top: 0;vertical-align: bottom;"/>
                        <a href="javascript:" class="weui_btn weui_btn_mini weui_btn_default"
                           @click="cartAddNum(productName)"
                           style="margin-top: 0;vertical-align: bottom;"> + </a>
                    </div>
                    <div class="clearfix"></div>
                </div>
            </div>
        </div>
    </div>
    <div class="weui_actionsheet_menu" v-else>

        <div class="weui_cell">
            <?php echo Yii::t("wechat", "Your cart is empty"); ?>
        </div>
    </div>
</div>

<div class="weui_actionsheet weui_dialog_confirm" id="buyDialog">
    <div class="weui-actionsheet__title"><p
            class="weui-actionsheet__title-text"><?php echo Yii::t("wechat", "Please select product specifications"); ?></p>
    </div>
    <div class="weui_actionsheet_menu">
        <div class="weui_dialog_bd" v-if="beforeAddedObj.productId !== '' ">
            <div style="float: left;padding-right: 15px"
                 v-if="typeof products[beforeAddedObj.productId].img.all != 'string'">
                <img :src="products[beforeAddedObj.productId].img.all[0]" class="shoppingThumbnail"
                     v-if="!thumbnailUrl"
                     alt=""/>
                <img :src="thumbnailUrl" class="shoppingThumbnail" :class="thumbnailUrl" v-else
                     alt=""/>
            </div>
            <div>
                <div class="tb-promo-price" v-if="beforeCartStock[1]">¥ {{beforeCartStock[1]}}</div>
                <div class="tb-promo-price" v-else>¥ {{products[beforeAddedObj.productId].unit_price}}</div>
            </div>
            <div style="color:#010101;font-size: 17px">{{beforeAddedObj.name}}</div>
            <div style="color:;">{{catTitle}}</div>
            <div class="clearfix"></div>
            <div v-for="(product,type) in products[beforeAddedObj.productId]['attr']">
                <div class="weui_dialog_hd" style="width: 18%;display: inline-block">{{product.title}}</div>
                <div style="float: right;width:82%;">
                <span  v-for="(option,key) in product.item">
                <div v-if='option.disabled== true' class='sup'>
                    <a href="javascript:"  class="weui_btn weui_btn_mini weui-btn_disabled" >{{option.text}}</a>
                    <span class="weui-badge"><?php echo Yii::t("wechat", "Not Available"); ?></span>
                </div>
                <div v-else class='sup' >
                    <a href="javascript:" class="weui_btn weui_btn_mini"   @click="selTypeBtn(type,option.key,option.text)" :class="{ 'weui_btn_primary' : beforeAddedObj['options'][type]['optionKey'] == option.key , 'weui_btn_default':beforeAddedObj['options'][type]['optionKey'] != option.key}">{{option.text}}</a>
<!--                    <span class="weui-badge red-badge"  v-if='option.stock'>--><?php //echo Yii::t("wechat", "Out of stock"); ?><!--</span>-->
                </div>
                
                </span>
                    
                </div>
                <div class="clearfix"></div>
                <hr size=1px style="border:0;background-color:ghostwhite;height:1px;margin: 6px 0">
            </div>
            <div class="weui_dialog_hd"
                 style="width: 18%;display: inline-block"><?php echo Yii::t("wechat", "Quantity"); ?>
                <!--                    <span id="stock" v-if="beforeCartStock != false">(库存<span id="J_SpanStock">{{beforeCartStock[0]}}</span>件)</span>-->
            </div>
            <div style="padding: 1.2em 0 .5em;width:82%;float: right">
                <div style="float: left">
                    <a href="javascript:" class="weui_btn weui_btn_mini weui_btn_default" @click="minusNum"
                       style="vertical-align: bottom;margin-left: .5em"> - </a>
                    <input type="number" :value="beforeAddedObj.num" readonly
                           class="weui_btn weui_btn_mini weui_btn_default"
                           style="width: 40px;margin-top: 0;vertical-align: bottom;"/>
                    <a href="javascript:" class="weui_btn weui_btn_mini weui_btn_default" @click="addNum"
                       style="margin-top: 0;vertical-align: bottom;"> + </a>
                </div>
                <span style='margin-left:10px' v-if='(size && color && checkInfo)  && checkInfo.stock!=0'><?php echo Yii::t("wechat", "Stock Quantity:"); ?>{{checkInfo.stock}}</span>
            </div>
            <div class="clearfix"></div>
            <div id="failInfo" class="weui_dialog_hd" style="color:red;"></div>
        </div>
        <div class="weui_dialog_ft">

            <a href="javascript:" class="weui_btn_dialog default" style="text-align: center"
               @click="toggleShppingCart"><?php echo Yii::t("wechat", "Cancel"); ?>
            </a>

            <a href="#failInfo"  v-if="(size && color && checkInfo) && checkInfo.stock!=0" class="weui_btn_dialog primary" style="text-align: center"
               @click="putIn()"><?php echo Yii::t("wechat", "Yes"); ?></a>

        </div>

    </div>
</div>

<div class="weui_dialog_confirm" id="removeDialog" style="display: none">
    <div class="weui_mask" style="z-index: 2"></div>
    <div class="weui_dialog">
        <div class="weui_dialog_hd"><strong
                class="weui_dialog_title"><?php echo Yii::t("wechat", "Are you sure you want to delete this item ?"); ?></strong>
        </div>
        <!--            <div class="weui_dialog_bd">自定义弹窗内容</div>-->
        <div class="weui_dialog_ft">
            <a href="javascript:" @click="cancelRemoveDialog"
               class="weui_btn_dialog default"><?php echo Yii::t("wechat", "Cancel"); ?></a>
            <a href="javascript:" @click="removeProduct(removeDialogPid)"
               class="weui_btn_dialog primary"><?php echo Yii::t("wechat", "Yes"); ?></a>
        </div>
    </div>
</div>

<div class="weui_dialog_confirm" v-if="checkInDialog">
    <div class="weui_mask" style="z-index: 10002"></div>
    <div class="weui_dialog"  style="z-index: 10002">
        <div class="weui_dialog_hd">
            <p>{{beforeAddedObj.name}}</p>
            <div style="color:#888;font-size:15px">
                <p> <?php echo Yii::t("wechat", "Color:"); ?> {{checkInColor}} </p>
                <p><?php echo Yii::t("wechat", "Size:"); ?> {{checkInSize}}</p>
                <p> <?php echo Yii::t("wechat", "Amount: "); ?>  ¥ {{checkInNum}}</p>
            </div>
            <strong class="weui_dialog_title"><?php echo Yii::t("wechat", 'Are you sure to submit "Intent to Buy" registration?'); ?></strong>
        </div>
        <div class="weui_dialog_ft">
            <a href="javascript:" @click="checkInDialog=false"
               class="weui_btn_dialog default"><?php echo Yii::t("wechat", "Cancel"); ?></a>
            <a href="javascript:" @click="checkInStock"
               class="weui_btn_dialog primary"><?php echo Yii::t("wechat", "Yes"); ?></a>
        </div>
    </div>
</div>
<div class="weui_dialog_confirm" v-if="checkToast">
    <div  class='toast_content' style="z-index: 10002">
        <p class="">{{checkMessage}}</p>
    </div>
</div>
<div id="loadingToast" class="weui_loading_toast" style="display: none;">
    <div class="weui_mask_transparent"></div>
    <div class="weui_toast">
        <div class="weui_loading">
            <div class="weui_loading_leaf weui_loading_leaf_0"></div>
            <div class="weui_loading_leaf weui_loading_leaf_1"></div>
            <div class="weui_loading_leaf weui_loading_leaf_2"></div>
            <div class="weui_loading_leaf weui_loading_leaf_3"></div>
            <div class="weui_loading_leaf weui_loading_leaf_4"></div>
            <div class="weui_loading_leaf weui_loading_leaf_5"></div>
            <div class="weui_loading_leaf weui_loading_leaf_6"></div>
            <div class="weui_loading_leaf weui_loading_leaf_7"></div>
            <div class="weui_loading_leaf weui_loading_leaf_8"></div>
            <div class="weui_loading_leaf weui_loading_leaf_9"></div>
            <div class="weui_loading_leaf weui_loading_leaf_10"></div>
            <div class="weui_loading_leaf weui_loading_leaf_11"></div>
        </div>
        <p class="weui_toast_content"><?php echo Yii::t("wechat", "Submission is in process"); ?></p>
    </div>
</div>
</div>
<script>
    //swiper插件
    $(document).ready(function () {

        var mySwiper = new Swiper('.swiper-container', {
//            autoplay: 2000,//可选选项，自动滑动
            // 如果需要分页器
            pagination: '.swiper-pagination',
            observer: true,//修改swiper自己或子元素时，自动初始化swiper
            observeParents: true//修改swiper的父元素时，自动初始化swiper
        });

    });

    function arrayIntersection(a, b) {  //两个数组取交集
        var ai = 0, bi = 0;
        var result = [];
        while (ai < a.length && bi < b.length) {
            if (a[ai] < b[bi]) {
                ai++;
            }
            else if (a[ai] > b[bi]) {
                bi++;
            }
            else /* they're equal */
            {
                result.push(a[ai]);
                ai++;
                bi++;
            }
        }
        return result;
    }
    //end
    var products = <?php echo json_encode($products); ?>;
    //    var _products = [];
    //    $.each(pproducts, function (i, option) {
    //        var _i;
    //        if(1){
    //            _i =  option.sort
    //        }
    //        _products.push(option)
    //    });
    //    var sortedObjKeys = Object.keys(products).sort(function(a, b) {
    //        return b.sort - a.sort;
    //    });
    //    console.log(sortedObjKeys)
    var productsVue = new Vue({
        el: "#shoppingVueModel",
        data: {
            products: products,
            catTitle: <?php echo json_encode($catTitle); ?>,
            shoppingCart: { //购物车
            },
            beforeAddedObj: {
                productId: '',
                options: {}
            },
            optionArr: {},
            detailsObj: {},
            removeDialogPid: '',
            size:false,
            color:false,
            checkInDialog:false,
            checkInNum:'',
            checkInColor:'',
            checkInSize:'',
            checkInSizeId:'',
            checkInColorId:'',
            checkInfo:{},
            checkToast:false,
            checkMessage:''
        }, beforeUpdate: function () {
        },
        created: function () {
            var maxHeight = this.getDialogMaxHeight();
            $('#buyDialog').find('.weui_dialog_bd').css({'max-height': maxHeight + 'px', 'overflow': 'auto'});
        },
        update: function () {
        },
        methods: {
            toFixed: function (_price) {
                return _price.toFixed(2);
            },
            parseFloat: function (num) {
                return parseFloat(num);
            },
            inverse: function (num) {
                return -num;
            },
            isEmpty: function (obj) {
                return Object.keys(obj).length;
            },
            changeNewCourse: function (num) {
                this.newPriceNum = '';
            },
            detailsBtn: function (productId) {
                this.detailsObj.productId = productId;
                $('.swiper-wrapper').empty();
                if (typeof this.products[productId]['img']['all'] == 'string') {
                    return false;
                }
                $.each(this.products[productId]['img']['all'], function (i, url) {
                    $('.swiper-wrapper').append('<div class="swiper-slide" style="background:url(\'' + url + '\') center top / contain no-repeat;"></div>')//swiper插件只能识别dom改变
                });;
                $('.detailsTitle').html(this.products[productId]['title']);
                if (this.products[productId]['intro'] != '') {
                    $('.detailsIntro').html(this.products[productId]['intro']);
                    $('.product-intro').show();
                } else {
                    $('.product-intro').hide();
                }

                $('#detailsDialog').show();
            },
            toggleShppingCart: function (productId, _pid) {
                var _this = this;
                this.color=false
                this.size=false

                if ($('#buyDialog').hasClass('weui_actionsheet_toggle')) {
                   
                    $('#buyDialog').removeClass('weui_actionsheet_toggle').css('z-index', 0);
                    $('#shoppingCartMask').removeClass('weui_fade_toggle').hide()
                } else {
                    let datas=this.products[productId].attr
                    var colorList=[]
                    var sizeList=[]
                    if(datas.color.item.constructor === Array){
                        for(var i=0;i<datas.color.item.length;i++){
                            colorList.push({
                                key :datas.color.item[i].key,
                                disabled:false,
                                text:datas.color.item[i].text,
                                stock:false
                            })
                        }
                        for(var i=0;i<datas.size.item.length;i++){
                            sizeList.push({
                                key :datas.size.item[i].key,
                                disabled:false,
                                text:datas.size.item[i].text,
                                stock:false
                            })
                        }
                    }else{
                        for(var key in datas.color.item){
                            colorList.push({
                                key :key,
                                disabled:false,
                                text:datas.color.item[key],
                                stock:false
                            })
                        }
                        for(var key in datas.size.item){
                            sizeList.push({
                                key :key,
                                disabled:false,
                                text:datas.size.item[key],
                                stock:false
                            })
                        }
                    }
                    this.products[productId].attr.size.item=sizeList
                    this.products[productId].attr.color.item=colorList
                    $('#failInfo').text('');
                    Vue.set(_this.beforeAddedObj, 'options', {});
                    $.each(_this.products[productId]['attr'], function (i, option) {
                        Vue.set(_this.beforeAddedObj['options'], i, {
                            name: option.title,
                            optionName: '',
                            optionKey: ''
                        });
                    });
                    Vue.set(_this.beforeAddedObj, 'productId', productId);
                    Vue.set(_this.beforeAddedObj, '_pid', _pid);
                    Vue.set(_this.beforeAddedObj, 'num', 1);
                    Vue.set(_this.beforeAddedObj, 'name', _this.products[productId]['title']);
                    $('#buyDialog').addClass('weui_actionsheet_toggle').css('z-index', 10001);
                    $('#shoppingCartMask').addClass('weui_fade_toggle').show()
                }
            },
            getDialogMaxHeight: function () {
                var titleHeight = $('#buyDialog').find('.weui-actionsheet__title').height();
                var dialogftHeight = $('#buyDialog').find('.weui_dialog_ft').height();
                var bodyHeight = $('body').height();
                return (bodyHeight - titleHeight - dialogftHeight - 20);
            },
            cancelDetailsDialog: function () {
                $('#detailsDialog').hide()
            },
            addNum: function () {
                if(this.beforeAddedObj.num<this.checkInfo.stock){
                    this.beforeAddedObj.num++
                }
            },
            minusNum: function () {
                if (this.beforeAddedObj.num > 1) {
                    this.beforeAddedObj.num--
                }
            },
            actionSheet: function () {
                if ($('#actionsheet').hasClass('weui_actionsheet_toggle')) {
                    $('#actionsheet').removeClass('weui_actionsheet_toggle');
                    $('.weui-footer__text > .caret').css('transform', 'rotate(0deg)');
                    $('#mask').removeClass('weui_fade_toggle').hide()
                } else {
                    $('#actionsheet').addClass('weui_actionsheet_toggle');
                    $('.weui-footer__text > .caret').css('transform', 'rotate(180deg)');
                    $('#mask').addClass('weui_fade_toggle').show()
                }
            }, selTypeBtn: function (type, typeName, optionName) {
                Vue.set(this.beforeAddedObj['options'][type], 'optionKey', typeName);
                Vue.set(this.beforeAddedObj['options'][type], 'optionName', optionName);
                if(type=='size'){
                    this.size=true
                    this.checkInSize=optionName
                    this.checkInSizeId=typeName
                    let datas=this.products[this.beforeAddedObj.productId]        
                    var colorList=[]
                    for(var i=0;i<datas.attr.color.item.length;i++){
                        colorList.push({
                            key:datas.attr.color.item[i].key,
                            find:datas.attr.color.item[i].key+'_'+typeName
                        })
                    }
                    let notConfig=[]
                    for(var i=0;i<colorList.length;i++){
                        if(!datas.info[colorList[i].find]){
                            notConfig.push(colorList[i].key)
                        }
                    }
                    datas.attr.color.item.forEach(item => { 
                        if (notConfig.indexOf(item.key)!=-1){
                            item.disabled=true
                        }else{
                            item.disabled=false
                        }
                    })
                }
                if(type=='color'){
                    this.color=true
                    this.checkInColorId=typeName
                    this.checkInColor=optionName
                    var sizeList=[]
                    let dataList=this.products[this.beforeAddedObj.productId]
                    for(var i=0;i<dataList.attr.size.item.length;i++){
                        sizeList.push({
                            key:dataList.attr.size.item[i].key,
                            find:typeName+'_'+dataList.attr.size.item[i].key
                        })
                    }
                    let notConfig=[]
                    for(var i=0;i<sizeList.length;i++){
                        if(!dataList.info[sizeList[i].find]){
                            notConfig.push(sizeList[i].key)
                        }
                    }
                    dataList.attr.size.item.forEach(item => { 
                        if (notConfig.indexOf(item.key)!=-1){
                            item.disabled=true
                        }else{
                            item.disabled=false
                        }
                    })
                }
                if(this.size && this.color){
                    let list=this.products[this.beforeAddedObj.productId]
                    this.checkInNum=list.unit_price
                    this.checkInfo=list.info[this.checkInColorId+'_'+this.checkInSizeId]
                    if(this.checkInfo.stock==0){
                        list.attr.size.item.forEach(item => { 
                            if (this.checkInSizeId.indexOf(item.key)!=-1){
                                item.stock=true
                            }else{
                                item.stock=false
                            }
                        })
                    }else{
                        list.attr.size.item.forEach(item => { 
                            item.stock=false
                        })
                    }
                    if (!this.beforeCartStock) {
                        $('#failInfo').show().text('<?php echo Yii::t("wechat", "This product is out of stock"); ?>');
                    }
                    else{
                        $('#failInfo').hide()
                    }
                }
            },
            putIn: function () { //加入购物车
                var _this = this;
                var putInFlag = true;
                _this.optionArr = {};
                var optionKeys = '';
                $.each(this.beforeAddedObj['options'], function (i, obj) { //验证是否有未选
                    if (obj.optionKey == '') {
//                        $('#failInfo').show().text(obj.name + '<?php //echo Yii::t("wechat", "不能为空"); ?>//');
                        $('#failInfo').show().text('<?php echo Yii::t("wechat", "Please select product specifications"); ?>');
                        putInFlag = false;
                        return false;
                    } else {
                        Vue.set(_this.optionArr, i, {});
                        Vue.set(_this.optionArr[i], obj['optionKey'], obj['optionName']);
                        optionKeys += obj['optionKey']; //组合键
                    }
                });

                if (!putInFlag) {
                    return false;
                }
                if (!_this.beforeCartStock) {
                    $('#failInfo').show().text('<?php echo Yii::t("wechat", "This product is out of stock"); ?>');
                    putInFlag = false;
                }
                if (!parseInt(_this.beforeAddedObj['num']) > 0) {
//                    else if (parseInt(_this.beforeAddedObj['num']) > parseInt(_this.beforeCartStock[0]) && parseInt(_this.beforeAddedObj['num']) > 0) {
                    $('#failInfo').show().text('<?php echo Yii::t("wechat", "Please fill in the correct quantity"); ?>！');
                    putInFlag = false;
                }
                if (!putInFlag) {
                    return false;
                }

                if (!_this.shoppingCart[_this.beforeAddedObj.productId + optionKeys]) {
                    Vue.set(_this.shoppingCart, _this.beforeAddedObj.productId + optionKeys, {options: {}});
                }
                Vue.set(_this.shoppingCart[_this.beforeAddedObj.productId + optionKeys], 'options', _this.optionArr);
                Vue.set(_this.shoppingCart[_this.beforeAddedObj.productId + optionKeys], 'num', _this.beforeAddedObj['num']);
                Vue.set(_this.shoppingCart[_this.beforeAddedObj.productId + optionKeys], 'name', _this.products[_this.beforeAddedObj.productId]['title']);
                Vue.set(_this.shoppingCart[_this.beforeAddedObj.productId + optionKeys], '_pid', _this.beforeAddedObj._pid);
                Vue.set(_this.shoppingCart[_this.beforeAddedObj.productId + optionKeys], 'max', _this.beforeCartStock[0]);
                Vue.set(_this.shoppingCart[_this.beforeAddedObj.productId + optionKeys], 'price', _this.beforeCartStock[1]);
                Vue.set(_this.shoppingCart[_this.beforeAddedObj.productId + optionKeys], 'paid', _this.beforeCartStock[2]);
                if (_this.thumbnailUrl) {
                    Vue.set(_this.shoppingCart[_this.beforeAddedObj.productId + optionKeys], 'selectImg', _this.thumbnailUrl);
                } else {
                    if (typeof _this.products[_this.beforeAddedObj.productId]['imgthumb']['all'] != 'string') {
                        Vue.set(_this.shoppingCart[_this.beforeAddedObj.productId + optionKeys], 'selectImg', _this.products[_this.beforeAddedObj.productId]['imgthumb']['all'][0]);
                    }
                }
                $('#buyDialog').removeClass('weui_actionsheet_toggle').css('z-index', 0);
                $('#shoppingCartMask').removeClass('weui_fade_toggle').hide();
                $('#toast').show();
                setTimeout(function () {
                    $('#toast').hide()
                }, 2000);
                $('#failInfo').hide();
            },
            cartMinusNum: function (pid) {
                var _this = this;
                if (this.shoppingCart[pid].num > 1) {
                    this.shoppingCart[pid].num--;
                } else {
                    _this.removeDialogPid = pid;
                    $('#removeDialog').show();
                }
            },
            cartAddNum: function (pid) {
//                if (this.shoppingCart[pid].num < this.shoppingCart[pid].max) {
                this.shoppingCart[pid].num++;
//                }

            },
            arrayIntersection: function (a, b) {  //两个数组取交集
                var ai = 0, bi = 0;
                var result = [];
                while (ai < a.length && bi < b.length) {
                    if (a[ai] < b[bi]) {
                        ai++;
                    }
                    else if (a[ai] > b[bi]) {
                        bi++;
                    }
                    else /* they're equal */
                    {
                        result.push(a[ai]);
                        ai++;
                        bi++;
                    }
                }
                return result;
            },
            cartSubmit: function () {
            	$(".kucun").html('')
                $('#loadingToast').show();
                if ($('.shopping-btn ').hasClass('disabled')) {
                    return false;
                }
                $('.shopping-btn').addClass('disabled');
                var _data = {};
                $.each(this.shoppingCart, function (i, cart) {
                    _data[cart['paid']] = {num: cart.num, pid: cart._pid}
                });
                $.ajax({
                    type: "POST",
                    url: "<?php echo $this->createUrl('order');?>",
                    data: {data: _data},
                    dataType: 'json',
                    success: function (data) {
                        if (data.state == 'success') {
                            location.href = '<?php echo $payMentUrl;?>'
                        } else {
                        	if(data.data.length>0){
                        			var dataid=[]
							  	$(".kucun").each(function(i) {
							  		dataid.push($(this).attr('id'))
							  		for(var i=0;i<data.data.length;i++){
								  	for(var j=0;j<dataid.length;j++){
								  	    if(dataid[j]==data.data[i]){
					                       $('#'+data.data[i]).html(data.message)					                       									
								    	}
								  	}
								  }      
		                       })
		                    $('#actionsheet').addClass('weui_actionsheet_toggle');
		                    $('.weui-footer__text > .caret').css('transform', 'rotate(180deg)');
		                    $('#mask').addClass('weui_fade_toggle').show()
                        	}else{
                        		alert(data.message)
                        	}                        
                            $('#loadingToast').hide();
                            $('.shopping-btn ').removeClass('disabled')
                        }
                    }
                });
            },
            cancelRemoveDialog: function () {
                $('#removeDialog').hide();
            }, removeProduct: function (pid) {
                Vue.delete(this.shoppingCart, pid);
                $('#removeDialog').hide();
            },
            checkIn(){
                this.checkInDialog=true
            },
            checkInStock(){
                let that=this
                $.ajax({
                    type: "POST",
                    url: "<?php echo $this->createUrl('register');?>",
                    data: {aid: this.checkInfo.paid},
                    dataType: 'json',
                    success: function (data) {
                        that.checkInDialog=false   
                        that.checkMessage=data.message 
                        that.checkToast=true
                        setTimeout(() => {
                            that.checkToast=false
                        }, 1000)
                    }
                })
            }
        },
        computed: {
            allPrice: function () {
                var _price = 0;
                $.each(this.shoppingCart, function (i, cart) {
                    _price += parseFloat(cart.price * cart.num);
                });
                return _price.toFixed(2);
            }, allNum: function () {
                var _num = 0;
                $.each(this.shoppingCart, function (i, cart) {
                    _num += parseInt(cart.num);
                });
                return _num;
            },
             beforeCartStock: function () {    //库存和价格
                var _this = this;
                var _key = '';
                var objLength = this.isEmpty(this.beforeAddedObj.options);
                var _index = 1;
                var _num;
                var _paid;
                var _price;
                $.each(this.beforeAddedObj.options, function (i, options) {
                    if (_index < objLength && options.optionKey != '') {
                        _key += options.optionKey + '_';
                    } else {
                        _key += options.optionKey;
                    }
                    _index++;
                });
                if (typeof _this.products[_this.beforeAddedObj.productId]['info'][_key] != 'undefined') {
                    _num = _this.products[_this.beforeAddedObj.productId]['info'][_key]['stock'];
                    _price = _this.products[_this.beforeAddedObj.productId]['info'][_key]['price'];
                    _paid = _this.products[_this.beforeAddedObj.productId]['info'][_key]['paid'];
                    return [_num, _price, _paid];
                } else {
                    return false;
                }
                
            }, 
             thumbnailUrl: function () {
                var _this = this;
                var optionNameArr = [];
                $.each(this.beforeAddedObj.options, function (i, options) {
                    optionNameArr.push(options.optionName)
                });
                if (optionNameArr[0] == '' && optionNameArr[1] == '') {
                    return false;
                } else if (optionNameArr[0] == '' || optionNameArr[1] == '') {
                    if (typeof _this.products[_this.beforeAddedObj.productId]['imgthumb'][optionNameArr[0]] != 'undefined') {
                        return _this.products[_this.beforeAddedObj.productId]['imgthumb'][optionNameArr[0]][0]
                    } else if (typeof _this.products[_this.beforeAddedObj.productId]['imgthumb'][optionNameArr[1]] != 'undefined') {
                        return _this.products[_this.beforeAddedObj.productId]['imgthumb'][optionNameArr[1]][0]
                    }
                    return false;
                }
                if (typeof _this.products[_this.beforeAddedObj.productId]['imgthumb'][optionNameArr[1]] == 'undefined') { //判断该img数组下是否有有该属性
                    if (typeof _this.products[_this.beforeAddedObj.productId]['imgthumb'][optionNameArr[0]] != 'undefined') {
                        return _this.products[_this.beforeAddedObj.productId]['imgthumb'][optionNameArr[0]][0]
                    }
                } else if (typeof _this.products[_this.beforeAddedObj.productId]['imgthumb'][optionNameArr[0]] == 'undefined') {
                    if (typeof _this.products[_this.beforeAddedObj.productId]['imgthumb'][optionNameArr[1]] != 'undefined') {
                        return _this.products[_this.beforeAddedObj.productId]['imgthumb'][optionNameArr[1]][0]
                    }
                } else {
                    var filteredThumbnailArr = this.arrayIntersection(_this.products[_this.beforeAddedObj.productId]['imgthumb'][optionNameArr[0]], _this.products[_this.beforeAddedObj.productId]['imgthumb'][optionNameArr[1]]);//如果都有就取交集
                    if (filteredThumbnailArr[0] == undefined) {
                        return _this.products[_this.beforeAddedObj.productId]['imgthumb'][optionNameArr[0]][0]
                    } else {
                        return filteredThumbnailArr[0];
                    }
                }

            }
        }
    });

</script>