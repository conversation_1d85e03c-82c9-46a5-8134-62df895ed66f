<?php
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of Mims
 *
 * <AUTHOR>
 */
class OA
{
    const SECURITY_KEY = CommonUtils::SECURITY_KEY;//safe code
    /**
     * 生成securityCode,应该跟Xoops中方法一致
     * @return	Object		Description
     */
    static function genSecurityCode(){
        //$checkCode = $_SERVER['REMOTE_ADDR'];
        //
        //    $checkCode = substr( $checkCode, 0, strrpos( $checkCode, '.', 1 ) );
        //    $checkCode = substr( $checkCode, 0, strrpos( $checkCode, '.', 1 ) );

		$checkCode = 'MySecolithkey%&*';
        $checkCode .= @$_SERVER['HTTP_USER_AGENT'];
        $checkCode = md5( $checkCode );
        return $checkCode;
    }

    /**
     * 检查Yii生成的SecurityCode和XOOPS中写入Session的SecurityCode是否一致
     * @return	Object		Description
     */
    static function checkSecurity(){
        $checkCode = OA::genSecurityCode();
        if ( !isset( $_SESSION['xos_http_SessionService']['securityCode'] ) || $_SESSION['xos_http_SessionService']['securityCode'] != $checkCode ){
            Yii::app()->user->logout();
            $absUrl = Yii::app()->createAbsoluteUrl(Yii::app()->getRequest()->getUrl());
            Yii::app()->user->loginUrl = Yii::app()->user->loginUrl . "?xoops_requesturi=" . urlencode($absUrl);
            Yii::app()->user->loginRequired();
        }else{
            return true;
        }
    }

    //再通过权限验证之后写入；由孩子前台负责验证
    static function genPreviewCCToken(){
        $ccTokenFlag = "@!pre%sVi%sew%dCC!"; //需与员工查看孩子前台时的FLAG一致；
        $str = sprintf($ccTokenFlag, OA::genSecurityCode(), Yii::app()->session->sessionId,Yii::app()->user->getId());
        return md5($str);
    }

    /**
    ** 只能在 ProtectedController 的派生类 中调用
    **/
    static function Nav($childs=null){
        return array();
    }

    static function sendMail($emails, $subject,$message){
        $mailer = Yii::createComponent('application.extensions.mailer.EMailer');
        //$mailer->IsSendmail();
		/*
        $mailer->IsSMTP();
        $mailer->SMTPAuth=true;
        $mailer->SMTPSecure = 'ssl';
        $mailer->Host = 'smtp.gmail.com';
        $mailer->Port = 465;
        $mailer->Username = '****@gmail.com';
        $mailer->Password = '****';
		*/

        /*
        $mailer->IsSMTP();
        $mailer->SMTPAuth=true;
        $mailer->Host = 'mail.ivygroup.cn';
        $mailer->Username = 'no-reply';
        $mailer->Password = 'barney';

		$mailer->Sender = '<EMAIL>';
        $mailer->From = '<EMAIL>';
        $mailer->FromName = 'IvyOnline';

        if(is_array($emails)){
            foreach($emails as $email)
                $mailer->AddAddress($email);
        }else{
            $mailer->AddAddress($emails);
        }
        $mailer->CharSet = 'UTF-8';
        */
        $mailer->iniAsTest();
        $mailer->Subject = $subject;
        $mailer->MsgHTML($message);
        $mailer->IsHTML(true);
        return $mailer->Send();
    }

    static function CreateUploadUrl($uploadSubPath){
        return Yii::app()->params['uploadBaseUrl'] . $uploadSubPath;
    }

    static function CreateOAUploadUrl($subPath='', $file=''){
        return Yii::app()->params['OAUploadBaseUrl'] . '/' . $subPath . '/' . $file;
    }

    /**
     * 格式化日期
     * @param	Object	$timestamp	Description
     * @param	String	$dateWidth	'medium', 'short', 'long', null
     * @param	String	$timeWidth	'medium', 'short', 'long', null
     * @return	Object				Description
     */
    static function formatDateTime($timestamp, $dateWidth='medium', $timeWidth=null){
        if($timestamp)
        return Yii::app()->dateFormatter->formatDateTime($timestamp,$dateWidth, $timeWidth);
    }

    /**
     * Summary
     * @param	Object	$category	配置文件名 目录在app/comp/config下
     * @return	Object				返回
     * 使用方法 若文件名为 CfgTest.php
     * 	$ret = Mims::LoadConfig('CfgTest');
     */
    static function LoadConfig($category){
        $alias = 'application.components.config.'.$category;
        $config = require(Yii::getPathOfAlias($alias).'.php');
        return $config;
    }

    /**
     * Summary
     * @param	Object	$category	配置文件名 目录在app/comp/helper下
     * @return	Object				返回
     * 使用方法 若类名为 HTest
     * 		$helper = Mims::LoadHelper('HTest');
     *		$helper->display();
     */
    static function LoadHelper($func){
        $alias = 'application.components.helper.'.$func;
        $helper = Yii::createComponent($alias);
        return $helper;
    }

    static function processPicUpload($uploadedInst, $ckey, $oldFile = "", $deleteOld = true) {
        $cfgs = OA::LoadConfig('CfgPhoto');

        $params = $cfgs[$ckey];
        $subPath = rtrim($params['subDir'], '/') . '/';
        $subThumbPath = rtrim($params['subDir'], '/') . '/thumbs/';
        $psize = isset($params['sizes']) ? $params['sizes'] : $cfgs['defaultSizes'];

        //$normalDir = Yii::app()->params['OAUploadBasePath'] . $subPath;
       // $thumbDir = Yii::app()->params['OAUploadBasePath'] . $subThumbPath;
        self::autoMkDirs($subThumbPath);
        $fileName = strtolower( $params['filePrefix'] . uniqid() . '.' . $uploadedInst->getExtensionName() );

        $filePath = $subPath . $fileName;
        $fileThumbPath = $subThumbPath . $fileName;

        $uploadedInst->saveAs($filePath);

        Yii::import('application.extensions.image.Image');
        $image = new Image($filePath);

        //裁剪至定宽高；多于部分裁掉
        if(isset($psize['normalW']) && isset($psize['normalH'])){
            $width = $psize['normalW'];
            $height = $psize['normalH'];

            $master = ($image->width / $image->height >= $psize['normalW'] / $psize['normalH']) ? Image::HEIGHT : Image::WIDTH;
            $image->resize($psize['normalW'], $psize['normalH'], $master);
            $image->crop($psize['normalW'], $psize['normalH']);
        }

        //维持原比例裁剪至固定宽度
        elseif(isset($psize['normalW']) && !isset($psize['normalH'])){
            if($psize['normalW'] < $image->width ){
                $width = $psize['normalW'];
                $height = intval( ( $image->height / $image->width ) * $psize['normalW']);
                $image->resize($psize['normalW'], $height);
            }else{
                $width = $image->width;
                $height = $image->height;
            }
        }

        //维持原比例裁剪至固定高度
        elseif(!isset($psize['normalW']) && isset($psize['normalH'])){
            if($psize['normalH'] < $image->height ){
                $width = intval( ( $image->width / $image->height ) * $psize['normalH']);
                $height = $psize['normalH'];
                $image->resize($width, $psize['normalH']);
            }else{
                $width = $image->width;
                $height = $image->height;
            }
        }

        $uploadRootPath = rtrim(Yii::app()->params['OAUploadBasePath'],'/').'/';
        $image->save();
        $aliYunOss['new'][] = str_replace($uploadRootPath, '', $filePath);


        if(isset($psize['thumbW'])){
            if($psize['thumbW'] < $image->width){
                $image = new Image($filePath);
                $image->resize($psize['thumbW'], null)->save($fileThumbPath);
            }else{
                $image->save($fileThumbPath);
            }
            $aliYunOss['new'][] = ltrim(str_replace($uploadRootPath, '', $fileThumbPath), '/');
        }

        if ($deleteOld && !empty($oldFile)) {
            @unlink($subPath . $oldFile);
            @unlink($subThumbPath . $oldFile);
            $aliYunOss['del'][] = ltrim(str_replace($uploadRootPath, '', $subPath . $oldFile), '/');
            $aliYunOss['del'][] = ltrim(str_replace($uploadRootPath, '', $subThumbPath . $oldFile), '/');
        }

        CommonUtils::processAliYunOSS($aliYunOss, $uploadRootPath);
        return array(
            'filename' => $fileName,
            'width' => $width,
            'height' => $height
        );
    }

	static function processPic($uploadedInst, $ckey) {
        $cfgs = OA::LoadConfig('CfgPhoto');

        $params = $cfgs[$ckey];
        $subPath = rtrim($params['subDir'], '/') . '/';
        $subThumbPath = rtrim($params['subDir'], '/') . '/thumbs/';
        $psize = isset($params['sizes']) ? $params['sizes'] : $cfgs['defaultSizes'];

        self::autoMkDirs($subThumbPath);
        $fileName = strtolower( $params['filePrefix'] . uniqid() . '.' . $uploadedInst->getExtensionName() );

        $filePath = $subPath . $fileName;

        $uploadedInst->saveAs($filePath);

        Yii::import('application.extensions.image.Image');
        $image = new Image($filePath);

        //裁剪至定宽高；多于部分裁掉
        if(isset($psize['normalW']) && isset($psize['normalH'])){
            $width = $psize['normalW'];
            $height = $psize['normalH'];

            $master = ($image->width / $image->height >= $psize['normalW'] / $psize['normalH']) ? Image::HEIGHT : Image::WIDTH;
            $image->resize($psize['normalW'], $psize['normalH'], $master);
            $image->crop($psize['normalW'], $psize['normalH']);
        }

        //维持原比例裁剪至固定宽度
        elseif(isset($psize['normalW']) && !isset($psize['normalH'])){
            if($psize['normalW'] < $image->width ){
                $width = $psize['normalW'];
                $height = intval( ( $image->height / $image->width ) * $psize['normalW']);
                $image->resize($psize['normalW'], $height);
            }else{
                $width = $image->width;
                $height = $image->height;
            }
        }

        //维持原比例裁剪至固定高度
        elseif(!isset($psize['normalW']) && isset($psize['normalH'])){
            if($psize['normalH'] < $image->height ){
                $width = intval( ( $image->width / $image->height ) * $psize['normalH']);
                $height = $psize['normalH'];
                $image->resize($width, $psize['normalH']);
            }else{
                $width = $image->width;
                $height = $image->height;
            }
        }

        $image->save();

        return $fileName;
    }

 	/**
     * 判断目录是否存在  不存在则自动循环创建目录
     * @param string $path
     * @param string $mode
     * <AUTHOR>
     */
    static function autoMkDirs($path, $mode = 0777, $return = false) {
        if (!is_dir($path)) {
            if (!self::autoMkDirs(dirname($path))) {
                return false;
            }
            if (!mkdir($path, $mode)) {
                return false;
            }
        }

        if (true == $return) {
            return $path;
        }
        return true;
    }

    const RC_LUNCH_CANCEL_EXCEED_TIME = 20011;
    const RC_LUNCH_CANCEL_NONE_SCHOOLDAY = 20012;
    const RC_LUNCH_CANCEL_TARGET_UNPAID = 20013;
    const RC_LUNCH_CANCEL_ALREADY_FUNDED = 20014;

    static function genMenu($uid=0)
    {
        $uid = $uid ? $uid : Yii::app()->user->id;
        $alias = 'application.components.menu';
        $menuCategory = require Yii::getPathOfAlias($alias).'/menuCategory.php';
        $menuItem = require Yii::getPathOfAlias($alias).'/menuItem.php';

		$auth = Yii::app()->authManager;
		$role = $auth->getAuthAssignments($uid);
        $hasTask = $auth->getItemChildren(array_keys($role));

        $menus = array();
        $_menus = array();

        foreach ($menuItem as $mKey=>$mItem){
            if (in_array($mKey, array_keys($hasTask))){
                foreach ($mItem as $menuKey=>$menu){
                    $_menus[$menuKey] = $menu;
                }
            }
        }
        if ($_menus){
            foreach ($menuCategory as $mLabel){
                $menus[$mLabel['id']]['id'] = $mLabel['id'];
                $menus[$mLabel['id']]['name'] = $mLabel['label'];
                $menus[$mLabel['id']]['tip'] = $mLabel['label'];
                $menus[$mLabel['id']]['parent'] = 'root';
                $menus[$mLabel['id']]['top'] = '';

                foreach ($mLabel['items'] as $key){
                    if (in_array($key, array_keys($_menus))){
                        $value = $_menus[$key];
                        $menus[$mLabel['id']]['items'][$key]['id'] = $key;
                        $menus[$mLabel['id']]['items'][$key]['name'] = $value['title'];
                        $menus[$mLabel['id']]['items'][$key]['tip'] = $value['title'];
                        $menus[$mLabel['id']]['items'][$key]['parent'] = $mLabel['id'];
                        $menus[$mLabel['id']]['items'][$key]['top'] = '';
                        $menus[$mLabel['id']]['items'][$key]['url'] = CHtml::normalizeUrl($value['link']);
                    }
                }
            }
        }

        return $menus;
    }

    static function genUserMenu($category){
        $userMenu = OAMenu::getMenu($category);
//        $userRoles = array_keys(Yii::app()->authManager->getRoles(Yii::app()->user->id));
        $userRoles = Yii::app()->user->getRoles();

        foreach($userMenu as $_catkey=>$menus){
            foreach($menus as $_mk=>$_mv){
                if(isset($_mv['items'])){
                    foreach($_mv['items'] as $_nk=>$_nv){
                        $_intersect = array_intersect($userRoles, $_nv['roles']);
                        if(empty($_intersect)){
                            unset($userMenu[$_catkey][$_mk]['items'][$_nk]);
                        }
                    }
                    if(empty($userMenu[$_catkey][$_mk]['items'])){
                        unset($userMenu[$_catkey][$_mk]);
                    }
                }
            }
        }
        // 龙腾去掉艾毅大家庭菜单
        if (Yii::app()->params['siteFlag'] == 'longteng') {
            unset($userMenu['main']['pub']);
        }
        return $userMenu;
    }

	CONST POLICY_PAY = 'pay'; //收费政策
	CONST POLICY_REFUND = 'refund'; //退费政策

	static function getPolicy($type=self::POLICY_PAY, $startYear=0, $schoolId=''){
		Yii::import('application.components.policy.IvyPolicy');
		$configs = new IvyPolicy($type, $startYear, $schoolId);
		return $configs;
		//$defaultConfigs = new CConfiguration()
	}

	static function renderChildGender($flag){
		return ($flag == 1)? Yii::t("child", "Male") : Yii::t("child", "Female");
	}

	static function getChildGenderList(){
		return 	array(
			"1"=>Yii::t("child","Male"),
			"2"=>Yii::t("child","Female")
		);
	}

	static function getClassListByYid($yid=0, $branchId=""){
		$ret = array();
		$crit = new CDbCriteria;
		if($yid) $crit->compare("yid", $yid);
		if($branchId != "") $crit->compare("schoolid", $branchId);
		$crit->order = "child_age ASC";
		$classes = IvyClass::model()->findAll($crit);

		foreach($classes as $class){
			$ret[$class->classid] = $class->title;
		}

		return $ret;
	}

    static function t($category, $message, $lang=null, $params = array(), $source = null)
    {
        return Yii::t($category, $message, $params, $source, $lang);
    }

	static function getChildStatus($val=null){
		$status = array(
			ChildProfileBasic::STATS_REGISTERED => Yii::t("child", "Newly Registered"),
//			ChildProfileBasic::STATS_ACTIVE_WAITING => Yii::t("child", "Active"),
			ChildProfileBasic::STATS_ACTIVE => Yii::t("child", "Active"),
			ChildProfileBasic::STATS_GRADUATED => Yii::t("child", "Graduated"),
			ChildProfileBasic::STATS_DROPPINGOUT => Yii::t("child", "Dropping Out"),
			ChildProfileBasic::STATS_DROPOUT => Yii::t("child", "Dropped Out"),
		);
		if(is_null($val))
			return $status;
		else
			return isset($status[$val]) ? $status[$val] : '';
	}

    static function getChildClassStatus($val=null){
        $status = OA::getChildStatus();
        $status[50] = Yii::t('child','取消分班');
        return isset($status[$val]) ? $status[$val] : '';
    }

    static function isProduction()
    {
        if ( strtolower(Yii::app()->params['productionDomain']) == strtolower($_SERVER['HTTP_HOST']) ) {
            return true;
        }
        elseif (Yii::app()->params['isProduct']) {
            return true;
        }
       else {
           return false;
       }
    }

	static function getAllBranch($displayColumn='title', $order='branchid ASC', $type='<>10', $program=null, $active=Branch::STATUS_ACTIVE){
		$crit = new CDbCriteria;
		if(!empty($type))
			$crit->compare('type',$type);

		if(!empty($program))
			$crit->compare('program',$program);

		if(!empty($active))
			$crit->compare('status',$active);

		$crit->compare('city',">0");
		$crit->order = $order;

		$branches = Branch::model()->findAll($crit);
		return CHtml::listData($branches, 'branchid', $displayColumn);
	}

	//action = 'add', 'remove', 'equal' 添加标记、去掉标记、判断是否是
	public static function constantFeeFlag($action="", $tuition_type)
	{
		$flag = 'RS_';
		if (in_array($action, array('add','remove','equal')) && $tuition_type)
		{
			switch (strtolower($action))
			{
				case 'add':
					return $flag.$tuition_type;
					break;
				case 'remove':
					if (stristr($tuition_type,$flag))
					{
						return str_replace($flag,"",$tuition_type);
					}
					else
					{
						return $tuition_type;
					}
					break;
				case 'equal':
					if (stristr($tuition_type,$flag))
					{
						return true;
					}
					else
					{
						return false;
					}
					break;
			}
		}
	}

    static function getRequestLang($force=true){
        if($force){
            $lang = strtolower(Yii::app()->request->preferredLanguage);
            if(!in_array($lang, array("schinese_utf8","english"))){
                $lang = "english";
            }
        }else{
            $lang = Yii::app()->request->preferredLanguage;
        }
        return $lang;
    }

    static function setLangCookie($lang){
        $lang = strtolower(trim($lang));
		$cookiename = "lang";
		if(!in_array($lang, array("schinese_utf8","english"))){
            $lang = 'english';
		}

        unset(Yii::app()->request->cookies[$cookiename]);
        $cookie = new CHttpCookie($cookiename, $lang);
        $cookie->expire = time()+31104000;
        $cookie->domain = Yii::app()->session->cookieParams['domain'];
        Yii::app()->request->cookies[$cookiename]=$cookie;
        return $lang;
    }

	static function formatMoney($amount){
		return number_format($amount, 2);
	}

    static function genCCUrlHome($childId=0, $journal=false){
        $key1 = "!ivyOnline";
        $key2 = "journal";

        if (!$childId) return null;
        $str = $childId;
        $str .= $key1;
        if($journal != false){
           $str .= "+" . $key2 . $journal;
        }
        $str = urlencode(base64_encode($str));
        return Yii::app()->baseUrl . "/previewCC/" . $str;
     }

	 //为 AdmBranchLink Model 提供
	 static function getMultiAdmType(){
		return array(
            'pd' => 'PD',
            'rpd' => 'RPD',
			'cd' => Yii::t("cadmin", "Campus Director"),
			'om' => Yii::t("cadmin", "Office Management"),
			'fn' => Yii::t("cadmin", "Finance Team"),
			'fnmgr' => Yii::t("cadmin", "Finance Senior"),
			'am' => Yii::t("cadmin", "招生审核"),
            'it_mgt' => Yii::t("cadmin", "IT审核组"),
            'edu' => Yii::t("cadmin", "EDU"),
            'asa' => Yii::t("cadmin", "ASA"),
            'staffsurvey' => Yii::t("cadmin", "View Staff Survey"),
            'parentsurvey' => Yii::t("cadmin", "View Parent Survey"),
			'adm_school' => Yii::t("cadmin", "Depreciated Data"),
		);
	 }

     static function timediff($begin_time, $end_time)
    {
        $start = new DateTime("@$begin_time");
        $end = new DateTime("@$end_time");
        if ($start > $end) {
            list($start, $end) = array($end, $start);
        }
        $interval = $start->diff($end);
        return array(
            'year' => (int)$interval->format('%y'),
            'month' => (int)$interval->format('%m'),
            'day' => (int)$interval->format('%d'),
            'hour' => (int)$interval->format('%h'),
            'min' => (int)$interval->format('%i'),
            'sec' => (int)$interval->format('%s'),
        );
    }

    static function getAge($birthday_time,$current_time=0,$isDate = false)
    {
		$current_time =($current_time == 0)? strtotime('now'):$current_time;
        if(empty($birthday_time)){
            $re_str = "N/A";
        }else{
            if(true == $isDate){
                $birthday_time = strtotime($birthday_time);
            }
            $result = self::timediff($birthday_time,$current_time);
            $re_str = "";
            if($result['year']>0){
                $re_str.= $result['year']." ".Yii::t("global", "Year(s)");
            }
            if($result['month']>0){
                $re_str.= " ".$result['month']." ".Yii::t("global", "Month");
            }
            if(empty($re_str)){
                $re_str = Yii::t("global", "Year(s)");
            }
        }
        return $re_str;
    }

	static function getMediaServerConfig(){
		$serverConfig = CommonUtils::getConfig('common.config.CfgMediaServer', '.local.php');
		if(is_null($serverConfig)){
			$serverConfig = CommonUtils::getConfig('common.config.CfgMediaServer', '.php');
		}

		return $serverConfig;

	}

	static function getMediaServerUrl(){
		$servers = OA::getMediaServerConfig();
		foreach($servers['servers'] as $serverId=>$server){
			$result[$serverId] = $server['url'];
		}
		return $result;
	}

	/*
	 * 判断是否显示下学年信息
	 */
	static function isShowNextYear($timestamp=0, $startM=3, $endM=8){
		$timestamp = ($timestamp) ? $timestamp :time();
		return in_array(date('n',$timestamp),  range($startM, $endM));
	}

    static function isShowNextYear_t($timestamp = 0, $startM = 1, $endM = 12, $other = 12){
        $timestamp = ($timestamp) ? $timestamp :time();
        $data = date('n',$timestamp);
        if(in_array($data,  range($startM, $endM)) || $other == $data){
            return true;
        }else{
            return false;
        }
    }


	static function getMediaServer($classId, $campusId, $configObj){


		if(!empty($configObj)){
			$userId = Yii::app()->user->getId();

			if(isset( $configObj['byUser'][$userId]) ){
				return $configObj['servers'][ $configObj['byUser'][$userId] ];
			}
			if(isset( $configObj['byClass'][$classId]) ){
				return $configObj['servers'][ $configObj['byClass'][$classId] ];
			}
			if(isset( $configObj['byCampus'][$campusId]) ){
				return $configObj['servers'][ $configObj['byCampus'][$campusId] ];
			}

			return $configObj['servers'][ $configObj['defaultServer'] ];

		}else{
			echo 'Media Servers Configuration missing.';
			Yii::app()->end();
		}
		$serverCfg = new CConfiguration(  );
	}

    static function getProgram($id=null){
        $programs = array(
            Branch::PROGRAM_NONE => Yii::t('global', 'Office'),
            Branch::PROGRAM_IA => Yii::t('global', 'IA'),
            Branch::PROGRAM_IBS => Yii::t('global', 'IBS'),
            Branch::PROGRAM_MIK => Yii::t('global', 'MIK'),
            Branch::PROGRAM_DAYSTAR => Yii::t('global', 'Daystar')
        );
        if( !is_null($id) && isset($programs[$id]) ){
            return $programs[$id];
        }else{
            return $programs;
        }
    }

    static function getCampusTeachers($schoolid='')
    {
        $items = array();
        if($schoolid){
            Yii::import('common.models.classTeacher.*');
            $sql = "select u.uid from ivy_users u join ivy_user_profile p on u.uid=p.uid join auth_oa_assignments a on p.uid=a.userid where u.level=1 and p.branch=:schoolid and a.itemname in ('ivystaff_teacher','ivystaff_caregiver')";
            $command = Yii::app()->db->createCommand($sql);
            $command->bindParam(':schoolid', $schoolid, PDO::PARAM_STR);
            $rows = $command->queryAll();
            foreach($rows as $row){
                $items[]=$row['uid'];
            }
        }
        return $items;
    }

    static function isYZQ($schoolid = '')
    {
        if (in_array($schoolid, array('BJ_TT'))) {
            return true;
        }else{
            return false;
        }
    }
    static function getLastClassTitle($child_id)
    {
       Yii::import('common.models.portfolio.*');
        $sql = "SELECT
	l.title
FROM
	ivy_child_study_history AS h
	LEFT JOIN ivy_class_list AS l ON h.classid = l.classid
WHERE  h.childid=:childid

ORDER BY
	h.hid DESC
	LIMIT 1";
        $command = Yii::app()->db->createCommand($sql);
        $command->bindParam(':childid', $child_id, PDO::PARAM_STR);
        $rows = $command->queryAll();
        return $rows[0]['title'];
    }
}

function addColon($str){
	return $str . Yii::t("global",": ");
}
function addComma($str, $beforeStr=true){
	if($beforeStr){
		return Yii::t("global", ", ") . $str;
	}else{
		return $str . Yii::t("global", ", ");
	}
}
function addBrackets($str, $withSpace=true, $spacePos='pre'){
	if($withSpace){
		if($spacePos == "pre")
			return(Yii::t("global", " (:str)", array(':str'=>$str)) );
		else
			return(Yii::t("global", "(:str) ", array(':str'=>$str)) );
	}else{
		return(Yii::t("global", "(:str)", array(':str'=>$str)) );
	}
}
