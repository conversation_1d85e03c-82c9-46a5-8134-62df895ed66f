<?php if($status==1):?>
    <div class="weui_cells_title">
        <?php 
            echo $this->state == 'ds' ? Yii::t('wechat', "Please login with your DaystarOnline account info to check your child's weekly journal, bills, and much more!") : Yii::t('wechat', "Please login with your IvyOnline account info to check your child's weekly journal, bills, and much more!");
        ?>
    </div>
    <?php $form=$this->beginWidget('CActiveForm',array(
        'id'=>'bind-form',
        'action'=>$this->createUrl('bind'),
    )); ?>
    <div class="weui_cells weui_cells_form">
        <div class="weui_cell">
            <div class="weui_cell_hd">
                <?php echo $form->label($model, 'username', array('class'=>'weui_label')); ?>
            </div>
            <div class="weui_cell_bd weui_cell_primary">
                <?php echo $form->textField($model,'username',array('class'=>'weui_input', 'placeholder'=>Yii::t('wechat', 'Please enter account name  (email)'))) ?>
            </div>
        </div>
        <div class="weui_cell">
            <div class="weui_cell_hd">
                <?php echo $form->label($model, 'password', array('class'=>'weui_label')); ?>
            </div>
            <div class="weui_cell_bd weui_cell_primary">
                <?php echo $form->passwordField($model, 'password', array('class'=>'weui_input','autocomplete'=>'off', 'placeholder'=>Yii::t('wechat', 'Please enter your password'))); ?>
            </div>
        </div>
    </div>
    <?php echo $form->hiddenField($model, 'wechatUname');?>
    <div class="weui_btn_area">
        <?php echo CHtml::submitButton(Yii::t("wechat", "Login"),array('class'=>'weui_btn weui_btn_primary')); ?>
    </div>
    <?php $this->endWidget(); ?>
    <script>
        // 删除孩子ID的 cookie
        document.cookie = "childid=;expires=-1;path=/";
    </script>
<?php elseif($status==2): ?>
    <div class="weui_msg">
        <div class="weui_icon_area">
            <i class="weui_icon_success weui_icon_msg"></i>
        </div>
        <div class="weui_text_area">
            <h2 class="weui_msg_title"> <?php echo Yii::t('wechat', 'Login Success'); ?> </h2>
            <p class="weui_msg_desc"> 
                <?php 
                    echo $this->state == 'ds' ? Yii::t('wechat', "You can now begin to access DaystarOnline on wechat!") : Yii::t('wechat', "You can now begin to access IvyOnline on wechat!"); 
                ?> 
            </p>
        </div>
        <div class="weui_opr_area">
            <p class="weui_btn_area">
                <?php echo CHtml::link(Yii::t('wechat', 'OK'), $startUrl, array('class'=>'weui_btn weui_btn_primary'))?>
                <?php echo CHtml::link(Yii::t('wechat', 'Logout'), 'javascript:;', array('class'=>'weui_btn weui_btn_default', 'id'=>'showDialog1'))?>
            </p>
        </div>
    </div>
    <div class="weui_dialog_confirm" id="dialog1" style="display: none;">
        <div class="weui_mask"></div>
        <div class="weui_dialog">
            <div class="weui_dialog_hd"><strong class="weui_dialog_title"><?php echo Yii::t('wechat', 'Logout') ?></strong></div>
            <div class="weui_dialog_bd"><?php echo Yii::t('wechat', 'Are you sure you want to logout?'); ?></div>
            <div class="weui_dialog_ft">
                <a href="javascript:;" class="weui_btn_dialog default"><?php echo Yii::t('global', 'Cancel'); ?></a>
                <?php echo CHtml::link(Yii::t('wechat', 'OK'), array('unbind'), array('class'=>'weui_btn_dialog primary'));?>
            </div>
        </div>
    </div>
    <div id="toast" style="display: none;">
        <div class="weui_mask_transparent"></div>
        <div class="weui_toast">
            <i class="weui_icon_toast"></i>
            <p class="weui_toast_content"><?php echo Yii::t('wechat', 'Logged Out'); ?></p>
        </div>
    </div>
<?php elseif($status==3): ?>
    <div class="weui_msg">
        <div class="weui_icon_area">
            <i class="weui_icon_msg weui_icon_warn"></i>
        </div>
        <div class="weui_text_area">
            <h2 class="weui_msg_title"><?php echo Yii::t('wechat', 'Login Failed'); ?></h2>
            <p class="weui_msg_desc"><?php echo $errors[0]; ?></p>
        </div>
        <div class="weui_opr_area">
            <p class="weui_btn_area">
                <?php echo CHtml::link(Yii::t('wechat', 'Login Again'), $this->createUrl('bind'), array('class'=>'weui_btn weui_btn_default'))?>
            </p>
        </div>
    </div>
<?php else:?>
    <div class="weui_msg">
        <div class="weui_icon_area">
            <i class="weui_icon_safe weui_icon_safe_warn"></i>
        </div>
        <div class="weui_text_area">
            <h2 class="weui_msg_title"><?php echo Yii::t('wechat', 'You have been logged out.'); ?></h2>
            <p class="weui_msg_desc"><?php echo Yii::t('wechat', "We haven't heard from you in a while.  Please close the page and try again."); ?></p>
        </div>
        <div class="weui_opr_area">
            <p class="weui_btn_area">
                <?php echo CHtml::link(Yii::t('wechat', 'Close'), 'javascript:wx.closeWindow();', array('class'=>'weui_btn weui_btn_default'))?>
            </p>
        </div>
    </div>

<?php endif;?>