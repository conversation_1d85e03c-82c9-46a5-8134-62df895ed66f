<style>
    [v-cloak] {
		display: none;
	}
    .addData{
        border:1px solid #E5E7EB;
        border-radius:4px;
        height:180px
    }
    .progress{
        height:10px
    }
    .addImg{
        width: 31px;
        height: 31px;
        border-radius: 50%;
        object-fit: cover;
        border:1px solid #fff
    }
    .more{
        width: 31px;
        height: 31px;
        border-radius: 50%;
        border:1px solid #fff;
        display:inline-block;
        background:#E5E7EB;
        vertical-align: middle;
        text-align: center;
        color: #fff;
    }
    .ml-10{
        margin-left:-10px
    }
    .blue{
        color:#4D88D2
    }
    .text-overflow{
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    .border{
        border:1px solid #E5E7EB;
        border-radius:4px;
    }
    .borderBto{
        border-bottom:1px solid #E5E7EB;

    }
    .height2{
        height:200px;
        overflow-y:auto
    }
    .p0{
        padding:0
    }
    .pl0{
        padding-left:0
    }
    .font16{
        font-size:16px
    }
    .display{
        display:none
    }
    .addData:hover .display{
        display:inline-block
    }
</style>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('//mcampus/default/index')) ?></li>
        <li><?php echo CHtml::link(Yii::t('site','Campus Support'), array('//mcampus/default/index'))?></li>
        <li class="active">学生信息收集</li>
    </ol>
    <div class="row"  id='registration' v-cloak>
        <div class="col-md-2 col-sm-2">
            <ul class="nav nav-pills nav-stacked text-left background-gray" id="pageCategory">
                <li class="active" ><a href="<?php echo $this->createUrl('index', array('branchId' => $this->branchId)); ?>">学生信息收集</a></li>
                <li class="" ><a href="<?php echo $this->createUrl('schoolBus2/index', array('branchId' => $this->branchId)); ?>">班车站点</a></li>
            </ul>
        </div>
        <div  class='col-md-10 col-sm-10 p0'>
            <div class='col-md-12 col-sm-12 p0' v-if='yearList["0"]'>
                <p  class='col-md-12 col-sm-12'><strong class='font16'>常规收集</strong> </p>
                <div>
                    <!-- <div class='col-md-4 mb20'>
                        <div class='addData text-center blue cur-p' @click='addData("")'>
                            <div class='font14' style='padding-top:60px'>
                                <span class='glyphicon glyphicon-plus-sign'  style='font-size:20px'></span>
                                <p class='mt5'>创建新的资料</p>
                            </div>
                        </div>
                    </div> -->
                    <div class='col-md-4  mb20' v-for='(list,index) in yearList["0"]'>
                        <div class='addData p15 font14' @click='href(list)'>
                            <p class='color3 flex title'><strong class='flex1 text-overflow'><a href='javascript:;'>{{list.title_cn}}</a> </strong><span v-if='list.commit==0' style='width:40px' class='glyphicon glyphicon-trash text-right blue display' @click.stop='delColl(list)'></span></p>
                            <p><span class="label label-default">本次收集共{{list.step_num}}步</span></p>
                            <p class='mb10 color6' v-if="list.startdate!=0">{{list.startdate}} - {{list.enddate}}</p>
                            <div class='flex'>
                                <div class="progress flex1" aria-hidden="true"  onMouseOver="$(this).tooltip('show')"  data-toggle="tooltip" data-html='true'  :title="`<div>已审核 ${list.audited}</div><?php echo Yii::t('asa', 'submitted') ?> ${list.commit}`" data-placement="top">
                                    <div class="progress-bar" role="progressbar" aria-valuenow="60" aria-valuemin="0" aria-valuemax="100" :style="numberPeople(list.commit,list.total)" >
                                        <div class="progress-bar progress-bar-success" role="progressbar" aria-valuenow="60" aria-valuemin="0" aria-valuemax="100" :style="numberPeople(list.audited,list.commit)"  >
                                        </div>
                                    </div>
                                </div>
                                <div style='width:40px;margin-top:-4px' class='text-center'>{{list.total}}</div>
                            </div>
                            <div>
                                <img class='addImg' v-for='(stu,idx) in list.audit_student.slice(0,5)' :class='idx!=0?"ml-10":""' :src="stu.student_avatar" alt="">
                                <span class='ml-10 more' v-if='list.pending>5'>...</span>
                                <span class='color6'>还有{{list.pending}}人待审核</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class='col-md-12 col-sm-12 p0' v-for='(item,index) in yearData.yearList'>
                <p  class='col-md-12 col-sm-12'><strong class='font16'>{{item.year}}</strong> </p>
                <div>
                    <div class='col-md-4 mb20' v-if='yearData.startYearList[item.yid]'>
                        <div class='addData text-center blue cur-p' @click='addData(item.yid)'>
                            <div class='font14' style='padding-top:60px'>
                                <span class='glyphicon glyphicon-plus-sign'  style='font-size:20px'></span>
                                <p class='mt5'>创建新的收集</p>
                            </div>
                        </div>
                    </div>
                    <div class='col-md-4  mb20' v-for='(list,index) in yearList[item.year]'>
                        <div class='addData p15 font14 cur-p' @click='href(list)'>
                            <p class='color3 flex title'><strong class='flex1 text-overflow'><a href='javascript:;'>{{list.title_cn}}</a> </strong><span v-if='list.commit==0' style='width:40px' class='glyphicon glyphicon-trash text-right blue display' @click.stop='delColl(list)'></span></p>
                            <p><span class="label label-default">本次收集共{{list.step_num}}步</span></p>
                            <p class='mb10 color6'>{{list.startdate}} - {{list.enddate}}</p>
                            <div class='flex'>
                                <div class="progress flex1" aria-hidden="true"  onMouseOver="$(this).tooltip('show')"  data-toggle="tooltip" data-html='true'  :title="`<div>已审核 ${list.audited}</div><?php echo Yii::t('asa', 'submitted') ?> ${list.commit}`" data-placement="top">
                                    <div class="progress-bar" role="progressbar" aria-valuenow="60" aria-valuemin="0" aria-valuemax="100" :style="numberPeople(list.commit,list.total)" >
                                        <div class="progress-bar progress-bar-success" role="progressbar" aria-valuenow="60" aria-valuemin="0" aria-valuemax="100" :style="numberPeople(list.audited,list.commit)"  >
                                        </div>
                                    </div>
                                </div>
                                <div style='width:40px;margin-top:-4px' class='text-center'>{{list.total}}</div>
                            </div>
                            <div>
                                <img class='addImg' v-for='(stu,idx) in list.audit_student.slice(0,5)' :class='idx!=0?"ml-10":""' :src="stu.student_avatar" alt="">
                                <span class='ml-10 more' v-if='list.pending>5'>...</span>
                                <span class='color6'>还有{{list.pending}}人待审核</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal fade" id="addDataModel" tabindex="-1" role="dialog" aria-labelledby="modal" aria-hidden="false" data-backdrop="static" data-keyboard="false">
            <div class="modal-dialog  modal-lg" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title" ><?php echo Yii::t('global', 'Add') ?> <span id="remarks_t"></span></h4>
                    </div>
                    <div class="modal-body">
                        <div class="form-horizontal">
                            <div class="form-group">
                                <label for="inputEmail3" class="col-sm-2 control-label"><?php echo Yii::t('labels', 'School Year') ?></label>
                                <div class="col-sm-10">
                                    <select class="form-control" v-model='startYear'disabled='true'  >
                                        <option value=''><?php echo Yii::t('global', 'Please Select') ?> </option>
                                        <option v-for='(list,index) in yearData.yearList' :value='list.yid'>{{list.year}}</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="inputPassword3" class="col-sm-2 control-label"><?php echo Yii::t('event', 'Title Cn') ?></label>
                                <div class="col-sm-10">
                                <input type="text" class="form-control" id="inputPassword3" :value='title_cn' v-model='title_cn'>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="inputPassword3" class="col-sm-2 control-label"><?php echo Yii::t('event', 'Title En') ?></label>
                                <div class="col-sm-10">
                                <input type="text" class="form-control" id="inputPassword3" :value='title_en' v-model='title_en'>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="inputPassword3" class="col-sm-2 control-label">简介中文</label>
                                <div class="col-sm-10">
                                    <textarea class="form-control" rows="3" :value='desc_cn' v-model='desc_cn' ></textarea>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="inputPassword3" class="col-sm-2 control-label">简介英文</label>
                                <div class="col-sm-10">
                                    <textarea class="form-control" rows="3" :value='desc_en' v-model='desc_en'></textarea>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="inputPassword3" class="col-sm-2 control-label">填写完成的展示文字（中文）</label>
                                <div class="col-sm-10">
                                    <textarea class="form-control" rows="3" :value='sub_success_cn' v-model='sub_success_cn'></textarea>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="inputPassword3" class="col-sm-2 control-label">填写完成的展示文字（英文）</label>
                                <div class="col-sm-10">
                                    <textarea class="form-control" rows="3" :value='sub_success_en' v-model='sub_success_en'></textarea>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="inputPassword3" class="col-sm-2 control-label">审核完成的展示文字（中文）</label>
                                <div class="col-sm-10">
                                    <textarea class="form-control" rows="3" :value='audit_success_cn' v-model='audit_success_cn'></textarea>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="inputPassword3" class="col-sm-2 control-label">审核完成的展示文字（英文）</label>
                                <div class="col-sm-10">
                                    <textarea class="form-control" rows="3" :value='audit_success_en' v-model='audit_success_en'></textarea>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="inputPassword3" class="col-sm-2 control-label"><?php echo Yii::t('labels', 'Date') ?></label>
                                <div class="col-sm-10">
                                <input type="text" class="form-control select_2 pull-left mr10 ml0" @blur='publish_atVal' id="startDate" v-model='startDate'  placeholder="<?php echo Yii::t("newDS", "Select a date"); ?>"  :value='startDate'>
                                <span class='pull-left' style='line-height:30px'>-</span>
                                <input type="text" class="form-control select_2 pull-left ml10"  @blur='expired_atVal' id="endDate" v-model='endDate'  placeholder="<?php echo Yii::t("newDS", "Select a date"); ?>"  :value='endDate'>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="inputPassword3" class="col-sm-2 control-label">步骤</label>
                                <div class="col-sm-10">
                                    <div class='col-sm-5 p0'>
                                        <div class='border'>
                                            <p class='text-center borderBto p10'>全部步骤</p>
                                            <div class='p10 height2'>
                                                <div class="checkbox" v-for='(item, index) in configStepAll'>
                                                    <label  :for=`checkbox${item.flag}`>
                                                        <input type="checkbox" value=""  :id=`checkbox${item.flag}` name="checkbox" :checked="item.select" @click="item.select=!item.select">{{ item.title }}
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class='col-sm-2 text-center'>
                                        <div style='margin-top:80px'>
                                            <button type="button" class="btn btn-primary" @click='del'><</button>
                                            <button type="button" class="btn btn-primary ml10" @click='push'>></button>
                                        </div>
                                    </div>
                                    <div class='col-sm-5 p0'>
                                        <div class='border '>
                                            <p class='text-center borderBto p10'>已选步骤</p>
                                            <div class='p10 height2'>
                                                <div class="checkbox flex" v-for='(item, index) in new_info'  :class='item.dragStatus?"blue":""'>
                                                    <label :for=`newcheckbox${item.flag}` class='flex1'>
                                                        <input type="checkbox" value="" :id=`newcheckbox${item.flag}` name="newcheckbox" :checked="item.select" @click="item.select=!item.select">{{ item.title }}
                                                    </label>
                                                    <span style='width:35px' class='text-right inline-block mt5 relative' v-if='!item.isEdit'>
                                                        <span  class='dragStatus'
                                                            draggable="true"
                                                            @dragstart="handleDragStart($event, item, new_info)" 
                                                            @dragover.prevent="handleDragOver($event, item, new_info)" 
                                                            @dragenter="handleDragEnter($event, item, new_info)" 
                                                            @dragend="handleDragEnd($event, item, new_info, index)" >
                                                            <span 
                                                            >
                                                                <span class="glyphicon glyphicon-move"></span>
                                                            </span>
                                                        </span>
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('global', 'Cancel') ?></button>
                        <button type="button" class="btn btn-primary" :disabled='disBtn' @click='saveList()'><?php echo Yii::t('global', 'Save') ?></button>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal fade" id="delModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
            <div class="modal-dialog modal-sm" role="document">
                <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("newDS", "Delete");?></h4>
                </div>
                <div class="modal-body">      
                    <div><?php echo Yii::t("newDS", "Confirm to delete this item?");?></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                    <button type="button" class="btn btn-primary" @click='delColl("hide")'><?php echo Yii::t("newDS", "Delete");?></button>
                </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>
<script>
    // $('#addDataModel').modal('show')

    var  container = new Vue({
        el: "#registration",
        data: {
            yearList:{},
            configStepAll:{},
            yearData:{},
            new_info: [],// 新数据，右框数据，
            startYear:'',
            endDate:'',
            startDate:'',
            title_cn:'',
            title_en:'',
            desc_cn:'',
            desc_en:'',
            delList:{},
            disBtn:false,
            sub_success_cn:'',
            sub_success_en:'',
            audit_success_cn:'',
            audit_success_en:''
        },
        created: function() {
            let that=this
            $.ajax({
                url: '<?php echo $this->createUrlReg('addCollShow'); ?>',
                type: 'post',
                dataType: 'json',
                data: {
                },
                success: function(data) {
                    if(data.state == 'success') {
                        that.configStepAll=data.data.configStepAll
                        that.configStepAll.map((val,key)=>{ that.$set(val,'select',false) });
                    } else {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    }
                },
                error: function(data) {
                    resultTip({
                        error: 'warning',
                        msg: '请求错误'
                    });
                }
            })
            $.ajax({
                url: '<?php echo $this->createUrlReg('getYearList'); ?>',
                type: 'post',
                dataType: 'json',
                data: {
                },
                success: function(data) {
                    if(data.state == 'success') {
                        that.yearData=data.data
                    } else {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    }
                },
                error: function(data) {
                    resultTip({
                        error: 'warning',
                        msg: '请求错误'
                    });
                }
            })
           this.showData()
        },
        mounted(){

        },
        methods: {
            numberPeople(num,total){
                var progress=num <= 0? "0%" : Math.round((num / total) * 10000) / 100.0 + "%";
                return 'width:'+progress
            },
            href(list){
                window.location.href='<?php echo $this->createUrl('details', array('branchId' => $this->branchId)); ?>&coll_id='+list.id
            },
            showData(){
                let that=this
                $.ajax({
					url: '<?php echo $this->createUrlReg('list'); ?>',
					type: 'post',
					dataType: 'json',
					data: {
					},
					success: function(data) {
						if(data.state == 'success') {
							that.yearList=data.data
						} else {
							resultTip({
								error: 'warning',
								msg: data.message
							});
						}
					},
					error: function(data) {
						resultTip({
							error: 'warning',
							msg: '请求错误'
						});
					}
				})
            },
            addData(year){
                let that=this
                this.startYear=year
                $('#addDataModel').modal('show')
                setTimeout(function() {
                    that.$nextTick(()=>{
                        $("#startDate").datepicker({
                            dateFormat: "yy-mm-dd ",
                        });
                        $("#endDate").datepicker({
                            dateFormat: "yy-mm-dd ",
                        });
                    })
                }, 500);
            },
            publish_atVal(){
                let that=this
                setTimeout(function() {
                    that.$nextTick(()=>{
                        container.startDate = $('#startDate').val();
                    })
                }, 500);
            },
            expired_atVal(){
                let that=this
                setTimeout(function() {
                    that.$nextTick(()=>{
                        container.endDate = $('#endDate').val();
                    })
                }, 500);
            },
            push(){
                let that = this;
                let configStepAll = JSON.parse(JSON.stringify(that.configStepAll));
                configStepAll.forEach((item, index )=>{
                    if (item.select){
                        that.new_info = that.new_info.concat(item).sort((a,b)=>{ return a.flag - b.flag });
                        delete configStepAll[index];
                        item.select = false;
                    }
                })
                configStepAll = configStepAll.filter(function (val) { return val });
                that.configStepAll = configStepAll;
            },
            // 移除数据
            del(){
                let that = this;
                let info = JSON.parse(JSON.stringify(that.new_info)); // 拷贝原数据, 深拷贝
                    info.forEach((item, index )=>{
                        if (item.select){
                            that.configStepAll = that.configStepAll.concat(item).sort((a,b)=>{ return a.id - b.id }); // 添加到新数据框, 排序
                            delete info[index];    // 删除数据
                            item.select = false;
                        }
                    })
                    info = info.filter(function (val) { return val }); // 过滤 undefined 
                    that.new_info = info; // 更新原数据
            },
            saveList(){
                let that=this
                let coll_step=[]
                that.new_info.forEach(function(value,key){
                    coll_step.push(value.flag)
　　　　　　　　　});   
                that.disBtn=true
                $.ajax({
					url: '<?php echo $this->createUrlReg('addColl'); ?>',
					type: 'post',
					dataType: 'json',
					data: {
                        'coll[yid]':this.startYear,
                        "coll[startyear]":this.yearData.startYearList[this.startYear],
                        "coll[startdate]": this.startDate,
                        "coll[enddate]": this.endDate,
                        "coll[title_cn]": this.title_cn,
                        "coll[title_en]":this.title_en,
                        "coll[desc_cn]":this.desc_cn,
                        "coll[desc_en]":this.desc_en,
                        "coll_step": coll_step,
                        "coll[sub_success_cn]": this.sub_success_cn,
                        "coll[sub_success_en]": this.sub_success_en,
                        "coll[audit_success_cn]": this.audit_success_cn,
                        "coll[audit_success_en]": this.audit_success_en,
                        "coll[type]":1,
					},
					success: function(data) {
						if(data.state == 'success') {
                            resultTip({
								msg: data.state
							});
                            that.disBtn=false
                            that.showData()
                            $('#addDataModel').modal('hide')
						} else {
                            that.disBtn=false
							resultTip({
								error: 'warning',
								msg: data.message
							});
						}
					},
					error: function(data) {
                        that.disBtn=false
						resultTip({
							error: 'warning',
							msg: '请求错误'
						});
					}
				})
            },
            delColl(list){
                if(list!='hide'){
                    this.delList=list
                    $('#delModal').modal('show')
                    return
                }
                let that=this
                $.ajax({
					url: '<?php echo $this->createUrlReg('delColl'); ?>',
					type: 'post',
					dataType: 'json',
					data: {
                        'coll_id':this.delList.id,
					},
					success: function(data) {
						if(data.state == 'success') {
                            resultTip({
								msg: data.state
							});
                            $('#delModal').modal('hide')
                            that.showData()
						} else {
							resultTip({
								error: 'warning',
								msg: data.message
							});
						}
					},
					error: function(data) {
						resultTip({
							error: 'warning',
							msg: '请求错误'
						});
					}
				})
            },
            handleDragStart(e, item) {
                this.dragging = item;
            },
            handleDragEnd(e, item) {
                this.dragging = null
                var fileList=[];
                this.new_info.forEach((item,index) => {
                    fileList.push(item)
                    Vue.set(item, 'dragStatus', false);
                })
                this.new_info = fileList
            },
            handleDragOver(e) {
                e.dataTransfer.dropEffect = 'move' // e.dataTransfer.dropEffect="move";//在dragenter中针对放置目标来设置!
            },
            handleDragEnter(e, item) {
                e.dataTransfer.effectAllowed = "move" //为需要移动的元素设置dragstart事件
                if(item === this.dragging) {
                    return
                }
                const newItems = [...this.new_info]
                const src = newItems.indexOf(this.dragging)
                const dst = newItems.indexOf(item)
                newItems.splice(dst, 0, ...newItems.splice(src, 1))
                this.new_info = newItems
                this.new_info.forEach((item,index) => {
                    Vue.set(item, 'dragStatus', false);
                })
                Vue.set(this.new_info[dst], 'dragStatus', true);
            },
        },
    })
</script>
