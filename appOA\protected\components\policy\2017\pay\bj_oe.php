<?php
if(!defined("IVY_POLICY"))
throw new CException('DO NOT USE THIS FILE DIRECTLY');

return array( 'policy'=>array(

	//是否老生老办法
	ENABLE_CONSTANT_TUITION => true,
    // 按天收费
    FENTAN_FACTOR => DAILY,

	//年保育费
	//ANNUAL_CHILDCARE_AMOUNT => 8000,

	//是否开放课外活动账单
	ENABLE_AFTERSCHOOLS => true,

	//图书馆工本费
	FEETYPE_LIBCARD => 10,

	//学期四个时间点；取前后两端
	TIME_POINTS   => array(201708,201712,201801,201807),

	//年付开通哪些缴费类型
	PAYGROUP_ANNUAL => array(
		ITEMS => array(
			FEETYPE_TUITION,
			FEETYPE_LUNCH,
			FEETYPE_SCHOOLBUS,
		),
		TUITION_CALC_BASE => BY_MONTH,
	),

	//学期付开通哪些缴费类型
	PAYGROUP_SEMESTER => array(
		ITEMS => array(
			FEETYPE_TUITION,
			FEETYPE_LUNCH,
			FEETYPE_SCHOOLBUS,
		),
		TUITION_CALC_BASE => BY_SEMESTER,
	),

    PAYGROUP_MONTH => array(
        ITEMS => array(
            FEETYPE_TUITION,
            FEETYPE_LUNCH,
            FEETYPE_SCHOOLBUS,
        ),
        TUITION_CALC_BASE => BY_MONTH,
    ),

	//计算基准：BY_MONTH, 或者 BY_SETTING, 前者表示所有的学费都基于月费用计算，后者表示所有的学费都基于学期或学年
	TUITION_CALCULATION_BASE => array(


        BY_MONTH => array(

            //月收费金额
            AMOUNT => array(
                FULL5D => 10500,
                HALF5D => 7560,
                // FULL3D => 6840,
                // HALF3D => 5050,
                // FULL2D => 4750,
                // HALF2D => 4321,
            ),

            //收费月份，及其权重
            MONTH_FACTOR => array(
                201709=>1,
                201710=>1,
                201711=>1,
                201712=>.8,
                201801=>1,
                201802=>.8,
                201803=>1,
                201804=>1,
                201805=>1,
                201806=>1,
                201807=>1
            ),

            //折扣
            GLOBAL_DISCOUNT => array(
                DISCOUNT_ANNUAL => '1:1:2017-12-01:round',
                DISCOUNT_SEMESTER1 => '1:1:2017-12-31:round',
                DISCOUNT_SEMESTER2 => '1:1:2018-07-31:round'
            )
        ),
        BY_SEMESTER1 => array(
            AMOUNT => array(
               // FULL5D => 57336,
                FULL5D => 50642,//2019
                // HALF2D => 28500,
                // HALF3D => 41500,
                // HALF5D => 69664,
            ),
        ),

        BY_SEMESTER2 => array(
            AMOUNT => array(
               // FULL5D => 53964,
                FULL5D => 60658,//2019
                // HALF2D => 28500,
                // HALF3D => 41500,
                // HALF5D => 55322,
            ),
        ),
	),

	//每餐费用
	LUNCH_PERDAY => 35,

	//账单到期日和账单开始日之差
	DUEDAYS_OFFSET => 1,

));