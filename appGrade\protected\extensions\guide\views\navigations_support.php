<h1>
<?php echo Yii::t("navigations","Support"); ?>
</h1>
<?php if (Yii::app()->language == 'en_us'): ?>
<p>
	You can get all campuses’ contact information here and send email to our support team online for assistance.
</p>
<p>
	Click "Next" to see sub navigations of this category.
</p>
<p>
	Or jump to guide of other categories by clicking the links below:
</p>
<?php else: ?>
<p>
	您可以在此获取所有学校的联系方式，并且通过在线发送邮件到校园支持团队以获取帮助。
</p>
<p>
	请点击“下一步”查看该目录的具体项目。
</p>
<p>
	或者点击以下链接跳转到其他页面的向导：
</p>
<?php endif ?>
<ul class="list">
<?php foreach(Mims::Nav() as $key=>$nav):?>
	<li>
		<?php
		if($key == "portfolio"){
			$nav['url'] = array('//portfolio/portfolio/classes');
		}
		$url = array_merge($nav['url'], array("demo"=>"guide"));
		echo CHtml::link($nav['label'], $url);
		?>
	</li>
<?php endforeach;?>
</ul>