<?php

/**
 * This is the model class for table "ivy_content".
 *
 * The followings are the available columns in table 'ivy_content':
 * @property integer $id
 * @property string $schoolid
 * @property string $linkitem
 * @property string $en_title
 * @property string $cn_title
 * @property string $category
 * @property string $en_intro
 * @property string $cn_intro
 * @property string $en_content
 * @property string $cn_content
 * @property integer $updated_timestamp
 * @property integer $userid
 */
class Content extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return content the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_content';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('schoolid, linkitem, en_title, cn_title, category, en_intro, cn_intro, en_content, cn_content, updated_timestamp, userid', 'required'),
			array('updated_timestamp, userid', 'numerical', 'integerOnly'=>true),
			array('schoolid, en_title, cn_title, category', 'length', 'max'=>255),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, schoolid, linkitem, en_title, cn_title, category, en_intro, cn_intro, en_content, cn_content, updated_timestamp, userid', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'schoolid' => 'Schoolid',
			'linkitem' => 'Linkitem',
			'en_title' => 'En Title',
			'cn_title' => 'Cn Title',
			'category' => 'Category',
			'en_intro' => 'En Intro',
			'cn_intro' => 'Cn Intro',
			'en_content' => 'En Content',
			'cn_content' => 'Cn Content',
			'updated_timestamp' => 'Updated Timestamp',
			'userid' => 'Userid',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('linkitem',$this->linkitem,true);
		$criteria->compare('en_title',$this->en_title,true);
		$criteria->compare('cn_title',$this->cn_title,true);
		$criteria->compare('category',$this->category,true);
		$criteria->compare('en_intro',$this->en_intro,true);
		$criteria->compare('cn_intro',$this->cn_intro,true);
		$criteria->compare('en_content',$this->en_content,true);
		$criteria->compare('cn_content',$this->cn_content,true);
		$criteria->compare('updated_timestamp',$this->updated_timestamp);
		$criteria->compare('userid',$this->userid);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
	
	public function getTitleList($items, $page=0){
		$ret = array();
		foreach($items as $item){
			$ret[] = array(
				'label'=>$item->getLangContent("title"),
				'url'=>array('//child/resource/content','category'=>$item->category, 'id'=>$item->id, 'page'=>$page),
			);
		}
		return $ret;
	}
	
	public function getLangContent($cat){
		$col = (Yii::app()->language == "zh_cn") ? "cn_".$cat : "en_".$cat;
		return $this->getAttribute($col);
	}
}