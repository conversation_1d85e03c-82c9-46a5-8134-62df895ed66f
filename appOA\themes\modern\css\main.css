.table_c1{
	margin-bottom: 10px;
}

.table_c1 th{
	background-color: #eef3f6;
	border-right: 1px solid #e5e3e3;
	vertical-align: top;
}
.table_c1 td{
	vertical-align: top;
}
#invoice-type li.level0 span{
	display: block;
	line-height: 24px;
	color:#666;
	padding: 4px 9px;
}
.level0 .subm li a{
	display: block;
    padding: 0 10px 0 30px;
    height: 25px;
    line-height: 25px;
    background: url(../images/admin/layout/bg.png);
    background-position: -265px 9px;
    background-repeat: no-repeat;    
}
.level0 .subm li a:hover{
	text-decoration:none;
    background-color:#e9e9e9;
}
.level0 .subm li.current a{
    background-color: #dbdada;
    background-position: -265px -11px;
}
.service-day{
    margin-top: 4px;
}
.service-day i{
    width: 20px;
    height: 20px;
    background-image: url('../images/sicon.gif?t=2');
    display: block;
    float: left;
    margin-right: 5px;
	cursor: pointer;
}
.service-day i.day-20{
    background-position: 0 -20px;
}
.service-day i.day-10{
    background-position: 0 0;
}
.service-day i.day-40{
    background-position: 0 -40px;
}
.cl{
    clear: left;
}
.user_group dd label.short-action {
    width: 240px !important;
}
.user_group dd label.long-action {
    width: 370px !important;
    text-align: left;
}
.user_group span i{
    font-style: normal;
    padding-left: 4px;
    -webkit-text-size-adjust: none !important;
}
.user_group span i.stats{
    color: #EA740C;
}
.user_group span i.class{
	color: #277CCD;
    font-size: 10px;
}
.user_group dd a.icon{
    background: url('../images/link.png') no-repeat left top transparent;
    display: inline-block;
    width: 16px;
    height: 16px;
    margin-left: 10px;
}
.disabled{
	background:#dedede;
	color:#999;
}
.errorMessage{
	color:red;
}
.tipRed{
    border-bottom: 2px solid red;
}
div.classInvoice_box{
	border: 2px solid #999;
	padding: 4px;
	width: 1140px;
	height: 500px;
	overflow: auto;
}
ul.classInvoice-column, ul.classInvoice-list{
	clear: both;
}
ul.classInvoice-column li, ul.classInvoice-list li{
	display: inline-block;
	height: 22px;
	float: left;
	width: 71px;
	overflow: hidden;
	border-right: 1px dotted #999;
	padding: 2px;
	text-align: center;
}
a.invoice-status{
	display: inline-block;
	width: 16px;
	height: 16px;
	background: url('../images/icons/status_bg.png') no-repeat left top transparent;
	text-indent: -99999px;
	margin-right: 2px;
}
a.status-20{
	background-position: left -18px;
}
a.status-10, a.status-30{
	background-position: left top;
}
.classInvoice_box table tr.head th,.classInvoice_box table tr.head td{
	background:#999;
	color:#fff;
}
.classInvoice_box i.stats-20, .classInvoice_box i.stats-100, .classInvoice_box i.stats-888, .classInvoice_box i.stats-999{
	font-style: normal;
	padding-left: 2px;
}
.classInvoice_box i.stats-888, .classInvoice_box i.stats-999{
	color:#EA740C;
}
.classInvoice_box tr.row-status-888 td, .classInvoice_box tr.row-status-888 tr,.classInvoice_box tr.row-status-999 td, .classInvoice_box tr.row-status-999 tr{
	background: #dedede;
}
.constant-list li{
	line-height: 30px;
	display: block;
	border-bottom: 1px dotted #999;
}
.constant-list li:hover{
	background:#fff;	
}
.constant-list li span.name{
	width: 180px;
	display: inline-block;
	text-align: right;
	
}
.constant-list li span.constant{
	color: #EA740C;
	font-size: 14px;
	width: 80px;
	display: inline-block;
}
.constant-list li span.discount{
	padding-left: 20px;
	display: inline-block;
	margin-left: 2px;
}
.constant-list li span.final{
	color:red;
	padding-left: 20px;
}
.pageSubMenu {
	margin-bottom: 10px;
}
.pageSubMenu li{
	float: left;
}
.pageSubMenu li a{
	padding: 4px 10px;
	float: left;
	padding: 0 15px;
	line-height: 32px;
}
.pageSubMenu li.current a{
	background: #dedede;
}
div.h_a span{
	font-weight: normal !important;
}
.enabled_icon, .disabled_icon{
	background: url(../images/icons/stats.png) no-repeat;
	display: inline-block;
	vertical-align: middle;
	width: 20px;
	height: 20px;
	overflow: hidden;
	cursor: pointer;	
}
.disabled_icon{
	background-position: -20px top;
}

/*** ESSENTIAL STYLES ***/
.sf-menu, .sf-menu * {
    margin:			0;
    padding:		0;
    list-style:		none;
}
.sf-menu {
    line-height:	1.0;
}
.sf-menu ul {
    position:		absolute;
    top:			-999em;
    width:			10em; /* left offset of submenus need to match (see below) */
    width: 			100%;
    display: 		block;
}
.sf-menu ul li {
    width:			100%;
}
.sf-menu li:hover {
    visibility:		inherit; /* fixes IE7 'sticky bug' */
}
.sf-menu li {
    float:			left;
    position:		relative;
    z-index:	9;
}
.sf-menu a {
    display:		block;
    position:		relative;
}
.sf-menu li:hover ul,
.sf-menu li.sfHover ul {
    /**left:			0;**/
    right: 0px;
    /**top:			2.5em; /* match top ul list item height */
    top: 34px;
    z-index:		99;
}
ul.sf-menu li:hover li ul,
ul.sf-menu li.sfHover li ul {
    top:			-999em;
}
ul.sf-menu li li:hover ul,
ul.sf-menu li li.sfHover ul {
    left:			10em; /* match ul width */
    top:			0;
}
ul.sf-menu li li:hover li ul,
ul.sf-menu li li.sfHover li ul {
    top:			-999em;
}
ul.sf-menu li li li:hover ul,
ul.sf-menu li li li.sfHover ul {
    left:			10em; /* match ul width */
    top:			0;
}

/*** DEMO SKIN ***/
a.sf-with-ul{
}
.sf-menu li{
    /*border-right: 1px solid #fff;*/
}
.sf-menu li.last{
    border-right: none;
}
.sf-menu li li{
    border-right: 0px;
}
.sf-menu li li em{
    border-top: 0;
    height: 0px;
    display: none !important;
}
.sf-menu ul li{
    font-size: 12px;
    height: 28px;
    line-height: 28px;
}
ul.subitem li{
	background:#fff !important;
}
ul.subitem li a{
	display: block;
	color:#333;
	float: none !important;
	text-align: left;
}
ul.subitem li.current a{
	background:#266aae;
	color:#fff !important;
}
.sf-menu{
    /*background:#078D00;*/
}
.sf-menu ul.length_2 {
	width: 110px !important;
	left: 0;
	right: auto;
}
/*** arrows **/
.sf-menu a.sf-with-ul {
    min-width:		1px; /* trigger IE7 hasLayout so spans position accurately */
}
.sf-sub-indicator {
    position:		absolute;
    display:		block;
    right:			2px;
    top:			1.05em; /* IE6 only */
    width:			10px;
    height:			10px;
    text-indent: 	-999em;
    overflow:		hidden;
    background:		url('../images/icons/dropdown_arrow.png') no-repeat transparent; /* 8-bit indexed alpha png. IE6 gets solid image only */
}
a > .sf-sub-indicator {  /* give all except IE6 the correct values */
    top:			13px;
    /*background-position: 0 -100px; /* use translucent arrow for modern browsers*/
}
/* apply hovers to modern browsers */
a:focus > .sf-sub-indicator,
a:hover > .sf-sub-indicator,
a:active > .sf-sub-indicator,
li:hover > a > .sf-sub-indicator,
/*li.sfHover > a > .sf-sub-indicator {*/
/*    background-position: -10px -100px; /* arrow hovers for modern browsers*/
/*}*/

.sf-menu .subitem li a{
    text-align: right;
    font-size: 12px;
}
.sf-menu ul.subitem li a{
	border-bottom: none !important;
	font-weight: normal;
	color: #666;	
}
.sf-menu ul.subitem li:hover a{
    text-decoration: underline;
}
.child-search1{
	float: right;
}
.ui-autocomplete-loading{
	color:blue;
	background:#fffde3 url(../images/admin/content/loading.gif) right center no-repeat;
}
/*菜单结束*/

.pay-summary{
	background:#fff;
	padding: 20px;
	line-height: 30px;
}
.pay-summary .num, .pay-summary .amount{
	font-size:16px;
	line-height: 30px;
}
.clear{
	clear:both;
}
.date-active a{
    background: url("../images/icons/circle.png") no-repeat right -2px !important;
}
a.dfilter{
    position: relative;
    float: none;
    white-space: nowrap;
    margin-top: 0;
    background: none;
    text-decoration: none !important;
    color: #333;
    border-radius: 2px;
    padding: 0 4px;
    padding-right: 15px;
    background: red;
}
a.dfilter strong{
    color: #fff;
    font-weight: normal;
}
a.dfilter b{
    display: block;
    position: absolute;
    right: 3px;
    top: 2px;
    overflow: hidden;
    width: 7px;
    height: 7px;
    background: url("../images/admin/layout/bg.png") no-repeat -144px 0;
    cursor: pointer;
}