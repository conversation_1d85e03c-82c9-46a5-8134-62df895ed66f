<script>
    tinymce.init({
        selector: '.tinymce',
        language: 'zh_CN',
        height: 300,
        plugins: 'fullscreen image link media imagetools preview tools table code',
        imagetools_cors_hosts: ['picsum.photos'],
        menubar: 'file edit view insert format tools help',
        toolbar: 'undo redo bold italic underline strikethrough fontselect fontsizeselect formatselect fullscreen image',
        toolbar_sticky: true,
        image_uploadtab: true,
        image_dimensions: false,
        /* enable title field in the Image dialog*/
        // image_title: true,
        /* enable automatic uploads of images represented by blob or data URIs*/
        automatic_uploads: false,
        /*
          URL of our upload handler (for more details check: https://www.tiny.cloud/docs/configure/file-image-upload/#images_upload_url)
          images_upload_url: 'postAcceptor.php',
          here we add custom filepicker only to Image dialog
        */
        file_picker_types: 'image',
        /* and here's our custom image picker*/
        file_picker_callback: function(cb, value, meta) {
            var input = document.createElement('input');
            input.setAttribute('type', 'file');
            input.setAttribute('accept', 'image/*');

            /*
              Note: In modern browsers input[type="file"] is functional without
              even adding it to the DOM, but that might not be the case in some older
              or quirky browsers like IE, so you might want to add it to the DOM
              just in case, and visually hide it. And do not forget do remove it
              once you do not need it anymore.
            */

            input.onchange = function() {
                var file = this.files[0];
                var upurl = '';

                var xhr, formData;
                xhr = new XMLHttpRequest();
                xhr.withCredentials = false;
                xhr.open('POST', upurl);
                xhr.onload = function() {
                    var json;
                    if (xhr.status != 200) {
                        failure('HTTP Error: ' + xhr.status);
                        return;
                    }
                    json = JSON.parse(xhr.responseText);
                    console.log(json)
                    if (!json || typeof json.location != 'string') {
                        failure('Invalid JSON: ' + xhr.responseText);
                        return;
                    }
                    cb(json.location);
                };
                formData = new FormData();
                formData.append('file', file, file.name);
                xhr.send(formData);
            };

            input.click();
        },
        setup: function(editor) {
            // 实时同步编辑器内容到 selector
            editor.on('change', function() {
                tinymce.triggerSave();
            });
        }
    });
</script>
<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span>
    </button>
    <h4 class="modal-title" id="exampleModalLabel"><?php echo $model->course_id ? '更新：' : '添加：'; ?></h4>
</div>
<?php $form = $this->beginWidget('CActiveForm', array(
    'id' => 'course-desc',
    'enableAjaxValidation' => false,
    'htmlOptions' => array('class' => 'J_ajaxForm form-horizontal', 'role' => 'form'),
)); ?>
<div class="modal-body">
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'brief_cn'); ?></label>

        <div class="col-xs-9">
            <?php echo $form->textArea($model, 'brief_cn', array('maxlength' => 255, 'class' => 'form-control')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'brief_en'); ?></label>

        <div class="col-xs-9">
            <?php echo $form->textArea($model, 'brief_en', array('maxlength' => 255, 'class' => 'form-control')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'desc_cn'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->textArea($model, 'desc_cn', array('maxlength' => 255, 'class' => 'form-control tinymce')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'desc_en'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->textArea($model, 'desc_en', array('maxlength' => 255, 'class' => 'form-control tinymce')); ?>
        </div>
    </div>
</div>
<div class="modal-footer">
    <button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global', 'Submit'); ?></button>
    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('global', 'Cancel'); ?></button>
</div>
<?php $this->endWidget(); ?>

<script>
    var ueAsaCourseDesc_desc_cn;
    var ueAsaCourseDesc_desc_en;

    function cbCourseDesc() {
        var courseId = '<?php echo $model->course_id; ?>';
        $('#modal1').modal('hide');
        window.location.reload(true);
    }

    $('#modal1').on('hidden.bs.modal', function(e) {
        if (tinyMCE.editors['AsaCourseDesc_desc_cn']) {
            tinyMCE.editors['AsaCourseDesc_desc_cn'].destroy();
        }
        if (tinyMCE.editors['AsaCourseDesc_desc_en']) {
            tinyMCE.editors['AsaCourseDesc_desc_en'].destroy();
        }
    })
</script>