<?php $weekday = array(
    'mon'=>'星期一 Monday',
    'tue'=>'星期二 Tuesday',
    'wed'=>'星期三 Wednesday',
    'thu'=>'星期四 Thursday',
    'fri'=>'星期五 Friday',
);?>
<h3 class="text-center"><?php echo $model->title_cn.' '.$model->title_en?></h3>
<?php if($week):?>
<div class="text-right">Week <?php echo $week.' ('.date('Y/m/d', $monday).' - '.date('Y/m/d', ($monday+345600)).')';?></div>
<?php endif;?>
<table class="t1" cellpadding="1" cellspacing="0" border="1" bordercolor="#000000" width="100%";>
    <tr>
        <th width="10%" class="text-center">日期 Date</th>
        <?php foreach($weekdayData as $wd):?>
            <th width="10%" class="text-center">
                <?php echo $wd;?>
            </th>
        <?php endforeach;?>
    </tr>
    <?php foreach($model->menu_cate as $cate):?>
        <tr>
            <th class="text-center" valign="middle"><?php echo $lunchs[$cate][0].'<br>'.$lunchs[$cate][1];?></th>
            <?php foreach(array_keys($weekdayData) as $week):?>
                <td valign="top"><?php echo nl2br(CHtml::encode(trim($details[$cate][$week])))?></td>
            <?php endforeach;?>
        </tr>
    <?php endforeach;?>
</table>
<div class="pageEnd"></div>
<style>
    .t1 tr th, .t1 tr td{
        padding: 8px;
    }
    @media print
    {
        table {page-break-after:always;}
    }
</style>