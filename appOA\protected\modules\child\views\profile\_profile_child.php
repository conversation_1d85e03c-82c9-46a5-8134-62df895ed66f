<?php
if($this->multipleBranch):
?>
<script>
	function transferSchool(){
		$('#transferCampus').toggle();
	}
</script>
<?php
endif;

?>

<div class="form">
<?php $form=$this->beginWidget('CActiveForm', array(
	'id'=>'child-form',
	'enableClientValidation'=>true,
	'clientOptions'=>array(
		'validateOnSubmit'=>true,
	),
	'htmlOptions' => array('enctype' => 'multipart/form-data'),
)); ?>
	
	<div class="table_full">
		<table width="100%">
			<colgroup>
				<col width="200">
			</colgroup>
			<tbody>
				<tr>
					<th>
						<?php echo CHtml::activeLabelEx($model,'schoolid'); ?>
					</th>
					<td>
						<?php
							$branchList = OA::getAllBranch();

							echo CHtml::openTag('span',array('class'=>'mr15'));
							echo $branchList[$model->schoolid];
							echo CHtml::closeTag('span');
							
							if( $this->multipleBranch ){
                                if (Yii::app()->user->checkAccess('ivystaff_it')) {
                                    echo CHtml::link(Yii::t('child', '转校'), 'javascript:void(0);', array('onClick' => 'transferSchool()', 'class' => 'btn mr15'));
                                }
								echo CHtml::openTag('span',array('id'=>'transferCampus','style'=>'display:none;'));
								echo CHtml::activeDropDownList($model, 'schoolid', OA::getAllBranch());
								echo CHtml::closeTag('span');
							}
							
						?>
					</td>
				</tr>
				<tr>
					<th>
						<?php echo CHtml::activeLabelEx($model,'photo'); ?>
					</th>
					<td>
						<?php
                        if($model->photo):
							echo CHtml::openTag('p');
                            echo CHtml::image(CommonUtils::childPhotoUrl($model->photo), '', array("width"=>120));
							echo CHtml::closeTag('p');
                        endif;
						?>
						<?php echo CHtml::activeFileField($model,'uploadPhoto') ?>
						<?php echo CHtml::error($model,'uploadPhoto'); ?>								
					</td>
				</tr>
				<tr>
					<th>
						<?php echo CHtml::activeLabelEx($model,'birthday_search'); ?>
					</th>
					<td>
						<?php
							$this->widget('zii.widgets.jui.CJuiDatePicker', array(
								"model"=>$model,
								"attribute"=>"birthday_search",
								"options"=>array(
									'changeMonth'=>true,
									'changeYear'=>true,
									'dateFormat'=>'yy-mm-dd', //很重要，不要随便修改 (RAN)
								),
								"htmlOptions"=>array(
									'class'=>'input'
								)
							));
						?>
						<?php echo CHtml::error($model,'birthday_search'); ?>						
					</td>
				</tr>
				<?php if($this->childObj->school->type == 50){ ?>
				<tr>
					<th>
						<?php echo CHtml::activeLabelEx($model,'barcode'); ?>
					</th>
					<td>
						<?php echo CHtml::activeTextField($model,'barcode',array('class'=>'input')) ?>
						<?php echo CHtml::error($model,'barcode'); ?>						
					</td>
				</tr>
				<tr>
					<th>
						<?php echo CHtml::activeLabelEx($model,'educational_id'); ?>
					</th>
					<td>
						<?php echo CHtml::activeTextField($model,'educational_id',array('class'=>'input')) ?>
						<?php echo CHtml::error($model,'educational_id'); ?>						
					</td>
				</tr>
				<?php } ?>
                <?php if(in_array($model->schoolid, array("BJ_DS","BJ_SLT","BJ_IASLT")) || ($model->nextYear && in_array($model->nextYear->schoolid, array("BJ_DS", "BJ_SLT","BJ_IASLT")))){ ?>
                    <tr>
                        <th>
                        </th>
                        <td>
                            <?php echo CHTml::activeRadioButtonList($model, 'is_legel_cn_name', array(1=> Yii::t("labels","Chinese Student"),2=>Yii::t("labels","Non Chinese Student")), array("name" => "is_legel_cn_name", 'class'=> 'input', 'onchange'=>'legelName()','template'=>'<div class="radio">{input} {label}</div>')) ?>
                            <?php echo CHtml::error($model,'is_legel_cn_name'); ?>
                        </td>
                    </tr>
                    <tbody id="is_legel_cn_name_en" style="display:none">
                        <tr>
                            <th>
                                <label for="ChildProfileBasic_last_name_en_en">Legal Last Name</label>
                            </th>
                            <td>
                                <?php echo CHtml::activeTextField($model,'last_name_en',array('class'=>'input attrEn', "id" => "ChildProfileBasic_last_name_en_en")) ?>
                                <?php echo CHtml::error($model,'last_name_en'); ?>
                            </td>
                        </tr>
                        <tr>
                            <th>
                                <label for="ChildProfileBasic_first_name_en_en">Legal First Name</label>
                            </th>
                            <td>
                                <?php echo CHtml::activeTextField($model,'first_name_en',array('class'=>'input attrEn', "id" => "ChildProfileBasic_first_name_en_en")) ?>
                                <?php echo CHtml::error($model,'first_name_en'); ?>
                            </td>
                        </tr>
                        <tr>
                            <th>
                                <label for="ChildProfileBasic_middle_name_en_en">Legal Middle Name</label>
                            </th>
                            <td>
                                <?php echo CHtml::activeTextField($model,'middle_name_en',array('class'=>'input attrEn', "id" => "ChildProfileBasic_middle_name_en_en")) ?>
                                <?php echo CHtml::error($model,'middle_name_en'); ?>&nbsp; <span class="btn-default"></span>
                            </td>
                        </tr>
                        <tr>
                            <th>
                                <label for="ChildProfileBasic_nick_en">Preferred Name</label>
                            </th>
                            <td>
                                <?php echo CHtml::activeTextField($model,'nick',array('class'=>'input attrEn', "id" => "ChildProfileBasic_nick_en")) ?>
                                <?php echo CHtml::error($model,'nick'); ?>
                            </td>
                        </tr>
                        <tr>
                            <th>
                                <label for="ChildProfileBasic_last_name_cn_en">Chinese Name</label>
                            </th>
                            <td>
                                <?php echo CHtml::activeTextField($model,'name_cn',array('class'=>'input attrEn', "id" => "ChildProfileBasic_last_name_cn_en")) ?>
                                <?php echo CHtml::error($model,'name_cn'); ?>
                            </td>
                        </tr>
                    </tbody>
                    <tbody  id="is_legel_cn_name_cn" style="display:none">
                        <tr>
                            <th>
                                <label for="ChildProfileBasic_name_cn">法定中文名字<br>Legal Name (Chinese)</label>
                            </th>
                            <td>
                                <?php echo CHtml::activeTextField($model,'name_cn',array('class'=>'input pinyinInfo attrCn')) ?> <span onclick="savePinyin()" class="btn btn-primary"><?php echo Yii::t("labels","Auto complete Pinyin") ?></span> <span id="chindNamePinyin"></span>
                                <?php echo CHtml::error($model,'name_cn'); ?>
                            </td>
                        </tr>
                        <tr>
                            <th>
                                <label for="ChildProfileBasic_last_name_en">法定姓（拼音）<br>Legal Surname (Chinese)</label>
                            </th>
                            <td>
                                <?php echo CHtml::activeTextField($model,'last_name_en',array('class'=>'input attrCn childProfileBasic_last_name_en_pinyin')) ?>
                                <?php echo CHtml::error($model,'last_name_en'); ?>
                            </td>
                        </tr>
                        <tr>
                            <th>
                                <label for="ChildProfileBasic_first_name_en">法定名（拼音）<br>Legal First Name (Chinese)</label>
                            </th>
                            <td>
                                <?php echo CHtml::activeTextField($model,'first_name_en',array('class'=>'input attrCn childProfileBasic_first_name_en_pinyin')) ?>
                                <?php echo CHtml::error($model,'first_name_en'); ?>
                            </td>
                        </tr>
                        <tr>
                            <th>
                                <label for="ChildProfileBasic_nick">昵称/英文名<br>Nickname/English Name</label>
                            </th>
                            <td>
                                <?php echo CHtml::activeTextField($model,'nick',array('class'=>'input attrCn')) ?>
                                <?php echo CHtml::error($model,'nick'); ?>
                            </td>
                        </tr>
                    </tbody>
                <?php }else{?>
                    <tr>
                        <th>
                            <?php echo CHtml::activeLabelEx($model,'name_cn'); ?>
                        </th>
                        <td>
                            <?php echo CHtml::activeTextField($model,'name_cn',array('class'=>'input')) ?>
                            <?php echo CHtml::error($model,'name_cn'); ?>
                        </td>
                    </tr>
                    <tr>
                        <th>
                            <?php echo CHtml::activeLabelEx($model,'first_name_en'); ?>
                        </th>
                        <td>
                            <?php echo CHtml::activeTextField($model,'first_name_en',array('class'=>'input')) ?>
                            <?php echo CHtml::error($model,'first_name_en'); ?>
                        </td>
                    </tr>
                    <tr>
                        <th>
                            <?php echo CHtml::activeLabelEx($model,'last_name_en'); ?>
                        </th>
                        <td>
                            <?php echo CHtml::activeTextField($model,'last_name_en',array('class'=>'input')) ?>
                            <?php echo CHtml::error($model,'last_name_en'); ?>
                        </td>
                    </tr>
                    <tr>
                        <th>
                            <?php echo CHtml::activeLabelEx($model,'middle_name_en'); ?>
                        </th>
                        <td>
                            <?php echo CHtml::activeTextField($model,'middle_name_en',array('class'=>'input')) ?>
                            <?php echo CHtml::error($model,'middle_name_en'); ?>&nbsp; <span class="btn-default"></span>
                        </td>
                    </tr>
                    <tr>
                        <th>
                            <?php echo CHtml::activeLabelEx($model,'nick'); ?>
                        </th>
                        <td>
                            <?php echo CHtml::activeTextField($model,'nick',array('class'=>'input')) ?>
                            <?php echo CHtml::error($model,'nick'); ?>
                        </td>
                    </tr>
                <?php } ?>
				<tr>
					<th>
						<?php echo CHtml::activeLabelEx($model,'gender'); ?>
					</th>
					<td>
						<?php echo CHtml::activeDropDownList($model,'gender',OA::getChildGenderList(),array('class'=>'select_2')); ?>
						<?php echo CHtml::error($model,'gender'); ?>
					</td>
				</tr>
				<tr>
					<th>
						<?php echo CHtml::activeLabelEx($model,'country'); ?>
					</th>
					<td>
						<?php echo CHtml::activeDropDownList($model,'country', Country::model()->getData(), array('empty'=>Yii::t("global", 'Please Select'))) ?>
						<?php echo CHtml::error($model,'country'); ?>
					</td>
				</tr>
				<tr>
					<th>
						<?php echo CHtml::activeLabelEx($model,'lang'); ?>
					</th>
					<td>
						<?php echo CHtml::activeDropDownList($model,'lang', Term::model()->getLangList(), array('empty'=>Yii::t("global", 'Please Select'))) ?>
						<?php echo CHtml::error($model,'lang'); ?>
					</td>
				</tr>
				<tr>
					<th>
						<?php echo CHtml::activeLabelEx($model,'est_enter_date'); ?>
					</th>
					<td>
						<?php
							$this->widget('zii.widgets.jui.CJuiDatePicker', array(
								"model"=>$model,
								"attribute"=>"est_enter_date",
								"options"=>array(
									'changeMonth'=>true,
									'changeYear'=>true,
									'dateFormat'=>'yy-mm-dd', //很重要，不要随便修改 (RAN)
								),
								"htmlOptions"=>array(
									'class'=>'input'
								)
							));
						?>
						<?php echo CHtml::error($model,'est_enter_date'); ?>						
					</td>
				</tr>
				<tr>
					<th>
						<?php echo CHtml::activeLabelEx($model,'enter_date'); ?>
					</th>
					<td>
						<?php
							$this->widget('zii.widgets.jui.CJuiDatePicker', array(
								"model"=>$model,
								"attribute"=>"enter_date",
								"options"=>array(
									'changeMonth'=>true,
									'changeYear'=>true,
									'dateFormat'=>'yy-mm-dd', //很重要，不要随便修改 (RAN)
								),
								"htmlOptions"=>array(
									'class'=>'input'
								)
							));
						?>
						<?php echo CHtml::error($model,'enter_date'); ?>						
					</td>
				</tr>
                <tr>
                    <th>
                        <?php echo CHtml::activeLabelEx($model, 'identity'); ?>
                    </th>
                    <td>
                        <?php echo CHtml::activeTextField($model,'identity',array('class'=>'input length_5')) ?>
                        <?php echo CHtml::error($model,'identity'); ?>
                    </td>
                </tr><tr>
                    <th>
                        <?php echo CHtml::activeLabelEx($model, 'invoice_title'); ?>
                    </th>
                    <td>
                        <?php echo CHtml::activeTextField($model,'invoice_title',array('class'=>'input length_5')) ?>
                        <?php echo CHtml::error($model,'invoice_title'); ?>
                    </td>
                </tr>
			</tbody>
		</table>
	</div>
		<div class="btn_wrap_pd">
			<?php
				echo CHtml::submitButton(Yii::t("global", "Save"), array("class"=>"btn btn_submit"));
                foreach(Yii::app()->user->getFlashes() as $key => $message) {
                    echo '<div class="tips_' . $key . '">' . $message . "</div>\n";
                }
			?>
		</div>


	
	
<?php $this->endWidget(); ?>
</div><!-- form -->
<script>
    function savePinyin(){
        $.ajax({
            type: "GET",
            url: "<?php echo $this->createUrl('//child/profile/childNamePinyin');?>",
            data: {childNamePinyin:$('.pinyinInfo').val()},
            dataType: "json",
            success: function(data){
                $("#chindNamePinyin").empty();
                childNamePinyinLastName = data.data[0];
                childNamePinyinName = data.data[1];
                $('.childProfileBasic_first_name_en_pinyin').val(data.data[1]);
                $('.childProfileBasic_last_name_en_pinyin').val(data.data[0]);
            }
        });
    }
    var status = "<?php echo $model->is_legel_cn_name; ?>"

    function legelName(){
        var is_legel_cn_name = $('input[name="is_legel_cn_name"]:checked').val();
        if(is_legel_cn_name == 1){
            $("#is_legel_cn_name_cn").show();
            $(".attrCn").attr('disabled', false);
            $("#is_legel_cn_name_en").hide();
            $(".attrEn").attr('disabled', true);
        }else{
            $("#is_legel_cn_name_en").show();
            $(".attrEn").attr('disabled', false);
            $("#is_legel_cn_name_cn").hide();
            $(".attrCn").attr('disabled', true);
        }
    }

    if(status == 1){
        $("#is_legel_cn_name_cn").show();
        $(".attrEn").attr('disabled', 'disabled');
    }else{
        $("#is_legel_cn_name_en").show();
        $(".attrCn").attr('disabled', 'disabled');
    }
</script>