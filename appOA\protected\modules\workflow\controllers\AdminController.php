<?php

class AdminController extends ProtectedController{
    
    public $actionAccessAuths = array(
        'addNode' => 'oSuperAdmin',
        'deleteNode' => 'oSuperAdmin',
        'deteteWorkflow' => 'oSuperAdmin',
        'getNodesList' => 'oSuperAdmin',
        'index' => 'oSuperAdmin',
        'saveDefination' => 'oSuperAdmin',
        'saveNode' => 'oSuperAdmin',
        'saveNodeSort' => 'oSuperAdmin',
    );

    public $dialogWidth = 400;
    
    
    public function init(){
        parent::init();
        Yii::import('common.models.workflow.*');
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";
        $this->modernMenuFlag = 'hqOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site','HQ Operations');
    }
	public function actionIndex()
	{
        $definationModel = new WorkflowDefination();
        $roles = Yii::app()->authManager->getAuthItems(2);
        //隐藏超级用户
        unset($roles[Yii::app()->getModule('srbac')->superUser]);
        unset($roles['ivystaff']);
        $rolesArray = array();
        foreach($roles as $_k=>$_d){
            $rolesArray[$_k] = $_k . ( empty($_d->description)? '':' '.$_d->description );
        }
        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/underscore-min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/backbone-min.js');
		$this->render('index',array('definationModel'=>$definationModel,'rolesArray'=>$rolesArray));
	}
    
    /**
     * 保存工作流程
     */
    public function actionSaveDefination(){
        $request = Yii::app()->request;
        $id = $request->getPost('id',0);
        $model = WorkflowDefination::model()->findByPk($id);
        if (empty($model)){
            $model = new WorkflowDefination();
        }
        if ($request->isAjaxRequest && isset($_POST['WorkflowDefination'])){
            $model->attributes = $_POST['WorkflowDefination'];
            if ($model->save()){
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message','success'));
                $this->addMessage('data',$model->getAttributes());
                $this->addMessage('callback', 'updateCallback');
                $this->showMessage();
            }else{
                $errs = current($model->getErrors());
                $this->addMessage('state', 'fail');
                $this->addMessage('error', $model->getErrors());
                $this->addMessage('message',  $errs ? $errs[0] : '请填写必填项');
                $this->showMessage();
            }
        }
    }
    
    /**
     * 删除工作流
     */
    public function actionDeteteWorkflow(){
        $request = Yii::app()->request;
        $id = Yii::app()->request->getPost('id',0);
        if ($request->isAjaxRequest && $id){
            $count = WorkflowOperation::model()->count('defination_id=:defination_id',array(':defination_id'=>$id));
            if ($count){
                $this->addMessage('state', 'fail');
                $this->addMessage('message','已有工流业务流程存在，不能删除！');
            }else{
                $model = WorkflowDefination::model()->findByPk($id);
                if ($model->delete()){
                    //删除节点缓存
                    Yii::app()->cache->delete(WorkflowNode::WORKFLOW_NODES_CACHE_ID);
                    WorkflowNode::model()->deleteAllByAttributes(array('defination_id'=>$id));
                    $this->addMessage('state', 'success');
                    $this->addMessage('data',$model->getAttributes());
                    $this->addMessage('message', Yii::t('message','success'));
                    $this->addMessage('callback', 'deleteCallback');
                }
            }
            $this->showMessage();
        }
    }
    
    /*
     * 工作流节点
     */
    public function actionGetNodesList(){
        $id = Yii::app()->request->getParam('id',0);
        $definationModel = WorkflowDefination::model()->with('workflowNode')->findByPk($id);
        //系统职位权限
        $branchType = Branch::branchTypes();
        //管理多校园
        $multSchool = AdmBranchLink::getMultSchoolWorkflowCheck();
        $criteria = new CDbCriteria();
        $criteria->order='t.status desc, t.weight';
        $items = HrDepartment::model()->with('title')->findAll($criteria);
        $itemList = array();
        if (!empty($items)){
            foreach ($items as $val){
                foreach ($val->title as $position=>$postionVal){
                    $itemList[$val->category][$postionVal->position_id] = $postionVal->position->cn_name;
                }
            }
        }
        $cs = Yii::app()->clientScript;
        $cs->registerCoreScript('jquery.ui');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/underscore-min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/backbone-min.js');
        $this->render('node', array('definationModel'=>$definationModel,'multSchool'=>$multSchool,'branchType'=>$branchType,'positionList'=>$itemList));
    }
    
    /*
     * 初始化工作流节点
     */
    public function actionAddNode(){
        $request = Yii::app()->request;
        $definationId = $request->getPost('definationId',0);
        $nodeId = $request->getPost('nodeId',0);
        if ($request->isAjaxRequest && $definationId){
            $node = WorkflowNode::model()->findByPk($nodeId);
            if (empty($node)){
                $node = new WorkflowNode();
                $node->setScenario('add');
            }else{
                if ($node->defination_id != $definationId){
                    return;
                }
                $node->setScenario('update');
            }
            $node->defination_id = $definationId;
            if (isset($_POST['WorkflowNode'])){
                $node->attributes = $_POST['WorkflowNode'];
            }else{
                $node->executor = '';
            }
            if ($node->save()){
                $defindObj = WorkflowDefination::model()->findByPk($definationId);
                $oldOrder = $defindObj->node_order;
                $oldOrder = ($defindObj->node_order) ? $oldOrder.','.$node->node_id : $node->node_id;
                $defindObj->node_order = $oldOrder;
                $defindObj->save();
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message','success'));
                $this->addMessage('refresh',true);
            }else{
                $errs = current($model->getErrors());
                $this->addMessage('state', 'fail');
                $this->addMessage('error', $node->getErrors());
                $this->addMessage('message',  $errs ? $errs[0] : '请填写必填项');
            }
            $this->showMessage();
        }
    }
    
    /**
     * 删除工作流节点
     */
    public function actionDeleteNode(){
        $request = Yii::app()->request;
        $nodeId = $request->getPost('nodeId',0);
        $definationId = $request->getPost('definationId',0);
        if ($request->isAjaxRequest && $definationId && $nodeId){
            $count = WorkflowOperation::model()->count('current_node_index=:current_node_index',array(':current_node_index'=>$nodeId));
            if ($count){
                $this->addMessage('state', 'fail');
                $this->addMessage('message','已有工流业务流程存在，不能删除！');
            }else{
                $model = WorkflowNode::model()->findByPk($nodeId);
                if ($model->defination_id ==$definationId && $model->delete()){
                    $nodeorder = $model->defination->node_order;
                    $nodeArr = explode(',', $nodeorder);
                    if($key = array_search($nodeId, $nodeArr)){
                        unset($nodeArr[$key]);
                    }
                    $model->defination->node_order = implode(',', $nodeArr);
                    if($model->defination->save()){
                        WorkflowNode::model()->delete();
                    }
                    $this->addMessage('state', 'success');
                    $this->addMessage('data',$model->getAttributes());
                    $this->addMessage('message', Yii::t('message','success'));
                    $this->addMessage('callback', 'deleteNodeCallback');
                }
            }
            $this->showMessage();
        }
    }
    
    /**
     * 更新节点信息
     */
    public function actionSaveNode(){
        $request = Yii::app()->request;
        $nodeId = $request->getPost('nodeId',0);
        $definationId = $request->getPost('definationId',0);
        $this->addMessage('state', 'fail');
        $this->addMessage('message', '保存错误!');
        if ($request->isAjaxRequest && $definationId && $nodeId){
            $model = WorkflowNode::model()->findByPk($nodeId);
            if ($model->defination_id == $definationId){
                $model->attributes = $_POST['WorkflowNode'];
                if (is_array($_POST['WorkflowNode']['executeId'])){
                    $model->executeId = implode(',', $model->executeId);
                }elseif(isset($_POST['WorkflowNode']['executeId'])){
                    $model->executeId = $_POST['WorkflowNode']['executeId'];
                }else{
                    $model->executeId = 0;
                }
                if ($model->executor && isset($_POST['WorkflowNode']['branchType'])){
                    $model->executor = serialize(array($model->executor=>$model->executeId,'branchType'=>$_POST['WorkflowNode']['branchType']));
                }elseif ($model->executor) {
                    $model->executor = serialize(array($model->executor=>$model->executeId));
                }
                $model->setScenario('update');
                if ($model->save()){
                    $data = $model->getAttributes();
                    if ($model->executor){
                        $executor = unserialize($model->executor);
                        $arrKey = key($executor);
                        $arrVal = current($executor);
                        $branchTypeSelect = isset($executor['branchType']) ? $executor['branchType'] : 0;
                    }else{
                        $arrKey = 0;
                        $arrVal = '';
                        $branchTypeSelect = 0;
                    }
                    if ($arrKey === WorkflowDefination::WORKFLOW_AUTH_SYSTEM_POSITION){
                        $arrTag = 1;
                    }else{
                        $arrTag = (empty($arrVal)) ? 2 : 3;
                    }
                    $data['executor'] = $arrKey;
                    $data['executeId'] = $arrVal;
                    $data['branchType'] = $branchTypeSelect;
                    $data['executeTag'] = $arrTag;
                    $this->addMessage('state', 'success');
                    $this->addMessage('data',$data);
                    $this->addMessage('message', Yii::t('message','success'));
                    $this->addMessage('callback', 'saveNodeCallback');
                }else{
                    $errs = current($model->getErrors());
                    $this->addMessage('state', 'fail');
                    $this->addMessage('error', $model->getErrors());
                    $this->addMessage('message',  $errs ? $errs[0] : '请填写必填项');
                }
            }
        }
        $this->showMessage();
    }
    
    public function actionSaveNodeSort(){
        $request = Yii::app()->request;
        $definationId = $request->getPost('defination_id',0);
        $nodeOrder = $request->getPost('node_order',0);
        $this->addMessage('state', 'fail');
        $this->addMessage('message', '保存错误!');
        if ($request->isAjaxRequest && $definationId && $nodeOrder){
            $model = WorkflowDefination::model()->findByPk($definationId);
            if (!empty($model)){
                $model->node_order = $nodeOrder;
                if ($model->save()){
                    $this->addMessage('state', 'success');
                    $this->addMessage('message', Yii::t('message','success'));
                    $this->addMessage('data',$model->getAttributes());
                }else{
                    $errs = current($model->getErrors());
                    $this->addMessage('state', 'fail');
                    $this->addMessage('error', $model->getErrors());
                    $this->addMessage('message',  $errs ? $errs[0] : '保存失败');
                }
            }
        }
        $this->showMessage();
    }
    
}