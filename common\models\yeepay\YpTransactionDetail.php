<?php

/**
 * This is the model class for table "ivy_yp_transaction_detail".
 *
 * The followings are the available columns in table 'ivy_yp_transaction_detail':
 * @property integer $detailId
 * @property integer $invocieId
 * @property string $it_orderId
 * @property double $payable_amount
 * @property double $fact_amount
 * @property integer $status
 * @property integer $orderTime
 * @property integer $updateTime
 */
class YpTransactionDetail extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return WYpTransactionDetail the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_yp_transaction_detail';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('invocieId, it_orderId', 'required'),
			array('invocieId, status, orderTime, updateTime', 'numerical', 'integerOnly'=>true),
			array('payable_amount, fact_amount', 'numerical'),
			array('it_orderId', 'length', 'max'=>50),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('detailId, invocieId, it_orderId, payable_amount, fact_amount, status, orderTime, updateTime', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'detailId' => 'Detail',
			'invocieId' => 'Invocie',
			'it_orderId' => 'It Order',
			'payable_amount' => 'Payable Amount',
			'fact_amount' => 'Fact Amount',
			'status' => 'Status',
			'orderTime' => 'Order Time',
			'updateTime' => 'Update Time',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('detailId',$this->detailId);
		$criteria->compare('invocieId',$this->invocieId);
		$criteria->compare('it_orderId',$this->it_orderId,true);
		$criteria->compare('payable_amount',$this->payable_amount);
		$criteria->compare('fact_amount',$this->fact_amount);
		$criteria->compare('status',$this->status);
		$criteria->compare('orderTime',$this->orderTime);
		$criteria->compare('updateTime',$this->updateTime);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}