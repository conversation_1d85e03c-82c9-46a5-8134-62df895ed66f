<?php
$labels = User::model()->attributeLabels();
$profileLabels = UserProfile::model()->attributeLabels();
$allBranch = $this->getAllBranch();
foreach ($allBranch as $val) {
    $branchList[$val['id']] = $val['title'];
}
?>
<?php echo CHtml::form($this->createUrl('//user/profile/saveBasic'), 'post', array('class'=>'form-horizontal J_ajaxForm','role'=>'form'))?>
<div class="panel panel-default">
    <div class="panel-heading">
        <h3 class="panel-title edit-title">
            <!--基本资料-->
        </h3>
    </div>
    <div class="panel-body">
        <div class="form-group">
            <label class='control-label col-sm-3'>ID</label>
            <div class="col-lg-3 col-sm-9">
                <div class='pt8'><?php echo $this->staff->uid ?></div>
            </div>
        </div>
        <div class="form-group">
            <?php echo CHtml::label($labels['email'], CHtml::getIdByName('User[email]'), array('class'=>'control-label col-sm-3')); ?>
            <div class="col-lg-3 col-sm-9">
                <?php echo CHtml::textField('User[email]', $this->staff->email,array('class'=>'form-control','disabled'=>'disabled'));?>
            </div>
        </div>
        <div class="form-group">
            <?php echo CHtml::label($labels['user_sig'], CHtml::getIdByName('labels[user_sig]'), array('class'=>'control-label col-sm-3')); ?>
            <div class="col-lg-3 col-sm-9">
                <?php echo CHtml::dropDownList('labels[user_sig]', $this->staff->user_sig,User::getSalutation(),array('class'=>'form-control','placeholder'=>$labels['user_sig'],'disabled'=>'disabled','empty'=>Yii::t('global', 'Please Select')));?>
            </div>
        </div>
        <div class="form-group" model-attribute="first_name">
            <?php echo CHtml::label($profileLabels['first_name'], CHtml::getIdByName('UserProfile[first_name]'), array('class'=>'control-label col-sm-3')); ?>
            <div class="col-lg-3 col-sm-9">
                <?php echo CHtml::textField('UserProfile[first_name]', $this->staff->profile->first_name,array('class'=>'form-control','placeholder'=>$profileLabels['first_name']));?>
            </div>
        </div>
        <div class="form-group" model-attribute="last_name">
            <?php echo CHtml::label($profileLabels['last_name'], CHtml::getIdByName('UserProfile[last_name]'), array('class'=>'control-label col-sm-3')); ?>
            <div class="col-lg-3 col-sm-9">
                <?php echo CHtml::textField('UserProfile[last_name]', $this->staff->profile->last_name,array('class'=>'form-control','placeholder'=>$profileLabels['last_name']));?>
            </div>
        </div>
        <div class="form-group">
            <?php echo CHtml::label($labels['name'], CHtml::getIdByName('User[name]'), array('class'=>'control-label col-sm-3')); ?>
            <div class="col-lg-3 col-sm-9">
                <?php echo CHtml::textField('User[name]', $this->staff->name,array('class'=>'form-control'));?>
            </div>
        </div>
        <div class="form-group" model-attribute="user_gender">
            <?php echo CHtml::label($profileLabels['user_gender'], CHtml::getIdByName('UserProfile[user_gender]'), array('class'=>'control-label col-sm-3')); ?>
            <div class="col-lg-3 col-sm-9">
                <?php echo CHtml::dropDownList('UserProfile[user_gender]', $this->staff->profile->user_gender,OA::getChildGenderList(),array('class'=>'form-control','placeholder'=>$profileLabels['user_gender'],'empty'=>Yii::t('global', 'Please Select')));?>
            </div>
        </div>
        <div class="form-group" model-attribute="nationality">
            <?php echo CHtml::label($profileLabels['nationality'], CHtml::getIdByName('UserProfile[nationality]'), array('class'=>'control-label col-sm-3')); ?>
            <div class="col-lg-3 col-sm-9">
                <?php echo CHtml::dropDownList('UserProfile[nationality]', $this->staff->profile->nationality,Country::model()->getData(),array('class'=>'form-control','placeholder'=>$profileLabels['nationality'],'empty'=>Yii::t('global', 'Please Select')));?>
            </div>
        </div>
        <div class="form-group">
            <?php echo CHtml::label($profileLabels['branch'], CHtml::getIdByName('UserProfile[branch]'), array('class'=>'control-label col-sm-3')); ?>
            <div class="col-lg-3 col-sm-9">
                <?php echo CHtml::dropDownList('UserProfile[branch]', $this->staff->profile->branch,$branchList,array('class'=>'form-control','placeholder'=>$profileLabels['branch'],'disabled'=>'disabled'));?>
            </div>
        </div>
        <div class="form-group">
            <?php echo CHtml::label($profileLabels['occupation_en'], CHtml::getIdByName('UserProfile[occupation_en]'), array('class'=>'control-label col-sm-3')); ?>
            <div class="col-lg-3 col-sm-9">
                <?php echo CHtml::dropDownList('UserProfile[occupation_en]', $this->staff->profile->occupation_en,DepPosLink::model()->getDepPos(),array('class'=>'form-control','placeholder'=>$profileLabels['occupation_en'],'disabled'=>'disabled'));?>
            </div>
        </div>
<!--        <div class="form-group">-->
<!--            --><?php //echo CHtml::label($labels['user_icq'], CHtml::getIdByName('User[user_icq]'), array('class'=>'control-label col-sm-2')); ?>
<!--            <div class="col-lg-3 col-sm-10">-->
<!--                --><?php //echo CHtml::textField('User[user_icq]', $this->staff->user_icq,array('class'=>'form-control','placeholder'=>$labels['user_icq']));?>
<!--            </div>-->
<!--        </div>-->
<!--        <div class="form-group">-->
<!--            --><?php //echo CHtml::label($labels['bio'], CHtml::getIdByName('User[bio]'), array('class'=>'control-label col-sm-2')); ?>
<!--            <div class="col-lg-3 col-sm-10">-->
<!--                --><?php //echo CHtml::textArea('User[bio]', $this->staff->bio,array('class'=>'form-control','rows'=>10, 'placeholder'=>$labels['bio']));?>
<!--            </div>-->
<!--        </div>-->
    </div>
    <div class="panel-footer">
        <button type="button" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global','Save');?></button>
    </div>
</div>
<?php echo CHtml::endForm()?>