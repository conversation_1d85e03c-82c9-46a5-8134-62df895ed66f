	<div class="h_a"><?php echo Yii::t("campus", "Class List");?></div>
	<div class="prompt_text">
		<ul>
			<li><?php echo Yii::t("child","请逐班处理");?></li>
		</ul>
	</div>
	<div class="user_group J_check_wrap">
			<?php foreach ($calendar->currentClasses as $class):?>
				<dl id="classid-<?php echo $class->classid;?>">
					<dt>
						<?php
						echo CHtml::openTag("label");
						echo CHtml::checkBox("class-".$class->classid, false, array('data-checklist'=>'J_check_c'.$class->classid, 'class'=>'J_check_all', 'data-direction'=>'y'));
						echo $class->title . ' ('.count($class->children).')';
						echo CHtml::closeTag("label");
						?></dt>
						<dd>
						<?php if($class->children):?>
							<?php foreach ($class->children as $child):
								$sts = $child->getSwitchStatus();
								$_htmlopt = array();
								if(isset($sts['txt'])){
									$_htmlopt['disabled']='disabled';
								}
                                else {
                                    $_htmlopt['data-yid'] = 'J_check_c'.$class->classid;
                                }
                                $_htmlopt['class'] = 'J_check';
                                $_htmlopt['value'] = $child->childid;
								echo CHtml::openTag("label");
									echo CHtml::checkBox("child[".$class->classid."][".$child->childid."]", false, $_htmlopt);
									$childName = $child->getChildName();
									echo CHtml::openTag("span", array("title"=>$childName, "id"=>'c_'.$child->childid));
										
										echo strlen($childName)>16? substr($child->getChildName(), 0, 16).'...':$childName;
										if(isset($sts['txt'])){
											echo CHtml::openTag("i", array("class"=>"stats"));
											echo $sts['txt'];
											echo CHtml::closeTag("i");		
										}
										if(isset($sts['classid'])){
											echo CHtml::openTag("i", array("class"=>"class"));
											echo $nextClassList[$sts['classid']];
											echo CHtml::closeTag("i");				
										}
									echo CHtml::closeTag("span");
									echo CHtml::link('', array("//child/index/index", "childid"=>$child->childid), array('class'=>'icon', 'target'=>'_blank'));
								echo CHtml::closeTag("label");
							endforeach;?>
							
							<?php
								echo CHtml::openTag("label", array("class"=>"short-action cl"));
								echo CHtml::link(Yii::t("campus", "Set Droppout"),'javascript:;', array("class"=>"btn mr15", 'onclick'=>'batHandle(this, "f");', 'classid'=>$class->classid));
								echo CHtml::link(Yii::t("campus", "Set Graduated"),'javascript:;', array("class"=>"btn", 'onclick'=>'batHandle(this, "o");', 'classid'=>$class->classid));
								echo CHtml::closeTag("label");
								
								echo CHtml::openTag("label", array("class"=>"long-action"));
								echo CHtml::openTag("span");
								echo Yii::t("campus", "Assign To: ");
                                $nextClassHtmlOpt['classid'] = $class->classid;
								echo CHtml::dropDownList("toClass",0, $nextClassList, $nextClassHtmlOpt);
								echo CHtml::closeTag("span");
								echo CHtml::closeTag("label");
							?>
						<?php endif;?>
						</dd>
				</dl>
			<?php endforeach; ?>
	</div>