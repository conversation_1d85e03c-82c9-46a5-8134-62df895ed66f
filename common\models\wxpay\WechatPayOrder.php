<?php

/**
 * This is the model class for table "wechat_pay_order".
 *
 * The followings are the available columns in table 'wechat_pay_order':
 * @property string $orderid
 * @property double $fact_amount
 * @property double $payable_amount
 * @property string $schoolid
 * @property integer $childid
 * @property string $type
 * @property integer $status
 * @property double $handling_amount 手续费金额
 * @property integer $settlement_status
 * @property string $ext
 * @property integer $order_time
 * @property integer $update_timestamp
 * @property integer $uid
 */
class WechatPayOrder extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'wechat_pay_order';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('orderid, payable_amount, schoolid, childid, type, order_time, update_timestamp, uid', 'required'),
			array('childid, status, settlement_status, order_time, update_timestamp, uid', 'numerical', 'integerOnly'=>true),
			array('fact_amount, payable_amount, handling_amount', 'numerical'),
			array('orderid', 'length', 'max'=>50),
			array('schoolid, type', 'length', 'max'=>32),
			array('ext', 'safe'),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('orderid, fact_amount, payable_amount, schoolid, childid, type, status, settlement_status, ext, order_time, update_timestamp, uid', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'ChildProfile' => array(self::BELONGS_TO, 'ChildProfileBasic', 'childid'),
            'school' => array(self::BELONGS_TO, 'Branch', 'schoolid'),
            'items' => array(self::HAS_MANY, 'WechatPayOrderItem', 'orderid'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'orderid' => 'Orderid',
			'fact_amount' => 'Fact Amount',
			'payable_amount' => 'Payable Amount',
			'schoolid' => 'Schoolid',
			'childid' => 'Childid',
			'type' => 'Type',
			'status' => 'Status',
			'settlement_status' => 'Settlement Status',
			'ext' => 'Ext',
			'order_time' => 'Order Time',
			'update_timestamp' => 'Update Timestamp',
			'uid' => 'Uid',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('orderid',$this->orderid,true);
		$criteria->compare('fact_amount',$this->fact_amount);
		$criteria->compare('payable_amount',$this->payable_amount);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('childid',$this->childid);
		$criteria->compare('type',$this->type,true);
		$criteria->compare('status',$this->status);
		$criteria->compare('settlement_status',$this->settlement_status);
		$criteria->compare('ext',$this->ext,true);
		$criteria->compare('order_time',$this->order_time);
		$criteria->compare('update_timestamp',$this->update_timestamp);
		$criteria->compare('uid',$this->uid);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return WechatPayOrder the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

    public function genOrderID($prefix){
//		$prefix = CommonUtils::isProduction() ? $prefix : '99'.$prefix;
		$prefix = (isset(Yii::app()->params['isProduct']) && Yii::app()->params['isProduct']) ? $prefix : '99'.$prefix;
        $maxId = Yii::app()->db->createCommand()
            ->select('max(orderid)')
            ->from($this->tableName())
            ->where('orderid LIKE :prefix', array(':prefix'=>$prefix.'%'))
            ->queryRow();
        if(empty($maxId)) return $prefix . '001';

        $maxId = array_shift($maxId);
        $postfix = sprintf('%03s', intval(substr($maxId, -3)) + 1);
        return $prefix . $postfix;
    }

    public function payInvoice()
    {
        if($this->status == 0){
			Yii::import('application.components.policy.*');
            $invoice = array();
            foreach($this->items as $item){
                $invoice[] = $item->invoice;
            }
            $policyApi = new PolicyApi($this->schoolid);
            $ret = $policyApi->pay($invoice, InvoiceTransaction::TYPE_WX_MICROPAY, $this->payable_amount);
            if($ret != 0){
                return $ret;
            }
            $this->fact_amount = $this->payable_amount;
            $this->status = 1;
            $this->update_timestamp = time();
            if($this->save()){
            	if (count($invoice)==1 && current($invoice)->payment_type=='registration' && current($invoice)->childid==0) {
            		PolicyApi::admissionsToEmail(current($invoice)->admission_id);
            	} else{
	            	PolicyApi::wxpayToEmail(array($this->orderid));
            	}
            }
        }
        return 0;
    }
}
