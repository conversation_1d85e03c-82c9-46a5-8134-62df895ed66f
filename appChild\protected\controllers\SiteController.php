<?php

class SiteController extends Controller
{	
	//public function filters()
	//{
	//	return array(
	//		'accessControl',
	//	);
	//}
	//
	//public function accessRules()
	//{
	//	return array(
	//		array(	'allow',
	//			//'actions'=>array('index','page','contact','captcha'),
	//			//'expression'=>'Yii::app()->user->getId()==2',
	//			//'ips'=>array('127.0.0.1'),
	//			//'verbs'=>array('GET'),
	//			//'users'=>array('@'),
	//			//'roles'=>array(),
	//		),
	//		array(	'deny',
	//			//'actions'=>array('index'),
	//			'users'=>array('*')
	//		)
	//	);
	//}
	/**
	 * Declares class-based actions.
	 */
	public function actions()
	{
		return array(
			// captcha action renders the CAPTCHA image displayed on the contact page
			'captcha'=>array(
				'class'=>'CCaptchaAction',
				//'backColor'=>0xFFFFFF,
			),
			// page action renders "static" pages stored under 'protected/views/site/pages'
			// They can be accessed via: index.php?r=site/page&view=FileName
			'page'=>array(
				'class'=>'CViewAction',
			),
		);
	}

	public function actionLang($lang)
	{
		$lang = Mims::setLangCookie($lang);

        echo CJSON::encode(array("result"=>"success","ret"=>$lang));
        Yii::app()->end();
		
	}

    public function actionLogout(){
        Yii::app()->user->logout();
        echo CJSON::encode(array("result"=>"success"));
        Yii::app()->end();
    }
	/**
	 * This is the action to handle external exceptions.
	 */
	public function actionError()
	{
		
	    if($error=Yii::app()->errorHandler->error)
	    {
	    	if(Yii::app()->request->isAjaxRequest)
	    		echo $error['message'];
	    	else{
				$clientScript = Yii::app()->getClientScript();
				$clientScript->registerCoreScript('jquery');
				$this->setSubPageTitle(Yii::t("message","Error! :code" ,array(":code"=> $error['code'])) );
				$this->layout = '//layouts/site.maintain';			
	        	$this->render('error', array('error'=>$error));
	    	}
	    }
		//$this->render('error');
	}
	
	public function actionMaintain(){
		$this->setSubPageTitle(Yii::t("message","System Maintainence"));
		$this->layout = '//layouts/site.maintain';
		$this->render('maintain');
	}
    
    public function actionCheckLocale()
    {
        echo '<p>Language: '.Yii::app()->language.'</p>';
        echo '<p>Preferred Language: '.Yii::app()->request->preferredLanguage.'</p>';
		echo '<p>Forced Language: ' . Mims::getRequestLang() . '</p>';
        echo '<p>Locale ID: '.Yii::app()->locale->id.'</p>';
        echo 'Cookie: ';
        echo '<pre>';
        print_r($_COOKIE);
        echo '</pre>';
    }
	
}