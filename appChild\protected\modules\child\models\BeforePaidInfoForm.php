<?php

/**
 * <AUTHOR>
 * @date 2012-7-23
 *
 */
class BeforePaidInfoForm extends CFormModel
{

	public $childId = 0;
	public $schoolId = '';
	public $bankId = '';
	public $autoOrderId = 0;
	public $totalDueAmount = 0;
	public $reqURL_onLine = '';
	public $customHmac = '';
	public $invoiceId = '';

	public function rules()
	{
		return array(
				array('childId, schoolId,bankId,autoOrderId,totalDueAmount,reqURL_onLine,customHmac,invoiceId', 'required'),
				array('childId,autoOrderId,totalDueAmount', 'numerical'),
		);
	}
}