<?php

class CollController extends BranchBasedController
{
    public $yid;
    public $printFW;

    public function createUrlReg($route, $params = array(), $ampersand = '&')
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        if (empty($params['yid'])) {
//            $params['yid'] = $this->yid;
        }
        return parent::createUrl($route, $params, $ampersand);
    }

    public function init()
    {
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'campusOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site', 'Campus Workspace');
        Yii::import('common.models.regextrainfo.*');
        Yii::import('common.models.calendar.*');
        Yii::import('common.models.child.HomeAddress');
        //初始化选择校园页面
        $this->branchSelectParams['hideOffice'] = true;
        $this->branchSelectParams['urlArray'] = array('//mcampus/coll/index');
        $cs = Yii::app()->clientScript;
        $cs->registerCoreScript('jquery.ui');
        $cs->registerCssFile(Yii::app()->themeManager->baseUrl . '/modern/css/wizard/bootstrap-nav-wizard.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/vue2.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/excellentexport.js');
        $cs->registerCssFile($cs->getCoreScriptUrl() . '/jui/css/base/jquery-ui.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/jquery.jPrintArea.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/xlsx.full.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/tinymce/tinymce.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/vue-tree/vue.min.js');
        $cs->registerCssFile(Yii::app()->themeManager->baseUrl . '/base/js/element/index.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/element/index.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/coll/eventBus.js');

    }


    public function beforeAction($action)
    {
        parent::beforeAction($action);
        // 初始化通用数据
        $this->yid = Yii::app()->request->getParam('yid');

        if (!$this->yid) {
            $this->getCalendars();
            // 默认下学年的yid
            if (isset($this->calendarYids['nextYid'])) {
                $this->yid = $this->calendarYids['nextYid'];
            } else {
                $this->yid = $this->calendarYids['currentYid'];
            }
        }
        return true;
    }

//    public function createUrl($route, $params = array(), $ampersand = '&')
//    {
//        if (empty($params['branchId'])) {
//            $params['branchId'] = $this->branchId;
//        }
//        return parent::createUrl($route, $params, $ampersand);
//    }

    public function actionIndex()
    {
        $this->render('index');
    }

    // 获取侧边栏菜单
    public function getSubMenu()
    {
        $subMenu = array(
            array('label' => Yii::t('user', '资料收集'), 'url' => "index"),
            array('label' => Yii::t('user', '班车站点'), 'url' => "carsitelist"),
        );

        return $subMenu;
    }

    //班车站点
    public function actionBusStation()
    {
        $this->render('busStation');
    }

    //资料详情
    public function actionDetails()
    {

        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/datatables/jquery.dataTables.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/datatables/dataTables.bootstrap.min.js');
        $cs->registerCssFile(Yii::app()->themeManager->baseUrl.'/base/js/datatables/dataTables.bootstrap.min.css');

        $this->render('details');
    }

    //打印接送卡
    public function actionIvyPrintPickupCard()
    {
        $view = 'ivyPrintPickupCard';
        $this->render($view);
    }

    //打印接送卡
    public function actionPrintPickupCard()
    {
        $view = 'printPickupCard';
        $this->render($view);
    }

    #资料收集
    public function actionList()
    {
//        $school_id = Yii::app()->request->getPost("school_id");
        $res = CommonUtils::requestDsOnline('coll/list', array(
            'school_id' => $this->branchId,
        ));
        if ($res['code'] == 0) {
            $data = $res['data'];
            $this->addMessage('state', 'success');
            $this->addMessage('data', $data);
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
        }
        $this->showMessage();
    }

    public function actionAddColl()
    {
        $coll = Yii::app()->request->getPost("coll");
        $coll_step = Yii::app()->request->getPost("coll_step");
        $data['coll'] = $coll;
        $data['coll']['schoolid'] = $this->branchId;
        $data['coll_step'] = $coll_step;
        $res = CommonUtils::requestDsOnline('coll/addColl', $data);
        if ($res['code'] == 0) {
            $data = $res['data'];
            $this->addMessage('state', 'success');
            $this->addMessage('data', $data);
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
        }
        $this->showMessage();
    }

    public function actionEditColl()
    {
        $coll_id = Yii::app()->request->getPost("coll_id");
        $colldata = Yii::app()->request->getPost("coll");
        $coll_step = Yii::app()->request->getPost("coll_step");
        $colldata['schoolid'] = $data['schoolid'] = $this->branchId;
        $res = CommonUtils::requestDsOnline('coll/editColl', array(
            'coll_id' => $coll_id,
            'coll' => $colldata,
            'coll_step' => $coll_step
        ));
        if ($res['code'] == 0) {
            $data = $res['data'];
            $this->addMessage('state', 'success');
            $this->addMessage('data', $data);
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
        }
        $this->showMessage();
    }

    public function actionDelColl()
    {
        $coll_id = Yii::app()->request->getPost("coll_id");
        $res = CommonUtils::requestDsOnline('coll/delColl', array(
            'coll_id' => $coll_id
        ));
        if ($res['code'] == 0) {
            $data = $res['data'];
            $this->addMessage('state', 'success');
            $this->addMessage('data', $data);
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
        }
        $this->showMessage();
    }

    public function actionAddCollShow()
    {
        $res = CommonUtils::requestDsOnline('coll/addCollShow', '', 'POST');
        if ($res['code'] == 0) {
            $data = $res['data'];
            $this->addMessage('state', 'success');
            $this->addMessage('data', $data);
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
        }
        $this->showMessage();
    }

    public function actionDelStudent()
    {
        $id = Yii::app()->request->getPost("id");
        $res = CommonUtils::requestDsOnline('coll/delStudent', array(
            'id' => $id
        ));
        if ($res['code'] == 0) {
            $data = $res['data'];
            $this->addMessage('state', 'success');
            $this->addMessage('data', $data);
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
        }
        $this->showMessage();
    }

    public function actionGetYearList()
    {
        $res = CommonUtils::requestDsOnline('coll/getYearList', array(
            'school_id' => $this->branchId
        ));
        if ($res['code'] == 0) {
            $data = $res['data'];
            $this->addMessage('state', 'success');
            $this->addMessage('data', $data);
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
        }
        $this->showMessage();
    }

    //复制站点
    public function actionPutCopyCartSite()
    {
        $from_yid = Yii::app()->request->getParam("from_yid");
        $to_yid = Yii::app()->request->getParam("to_yid");
        $res = CommonUtils::requestDsOnline('schoolBus/putCopyCartSite', array(
            'school_id' => $this->branchId,
            'from_yid' => $from_yid,
            'to_yid' => $to_yid,
        ));
        if ($res['code'] == 0) {
            $data = $res['data'];
            $this->addMessage('state', 'success');
            $this->addMessage('data', $data);
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
        }
        $this->showMessage();
    }

    public function actionDetailColl()
    {
        $coll_id = Yii::app()->request->getParam("coll_id");
        $res = CommonUtils::requestDsOnline('coll/detailColl', array(
            'coll_id' => $coll_id,
        ));
        if ($res['code'] == 0) {
            $data = $res['data'];
            $this->addMessage('state', 'success');
            $this->addMessage('data', $data);
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
        }
        $this->showMessage();
    }

    public function actionGetStudentOverall()
    {
        $coll_id = Yii::app()->request->getParam("coll_id");
        $child_id = Yii::app()->request->getParam("child_id");
//        $class_id = Yii::app()->request->getParam("class_id");
        $res = CommonUtils::requestDsOnline('coll/getStudentOverall', array(
            'coll_id' => $coll_id,
            'child_id' => $child_id,
//            'class_id' => $class_id,
        ));
        if ($res['code'] == 0) {
            $data = $res['data'];
            $this->addMessage('state', 'success');
            $this->addMessage('data', $data);
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
        }
        $this->showMessage();
    }

    public function actionGetQRCode()
    {
        $coll_id = Yii::app()->request->getParam("coll_id");
        $res = CommonUtils::requestDsOnline('coll/getQRCode', array(
            'coll_id' => $coll_id,
        ));
        if ($res['code'] == 0) {
            $data = $res['data'];
            $this->addMessage('state', 'success');
            $this->addMessage('data', $data);
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
        }
        $this->showMessage();
    }

    public function actionAddStudentShow()
    {
        $coll_id = Yii::app()->request->getParam("coll_id");
        $yid = Yii::app()->request->getParam("yid");
        $student_type = Yii::app()->request->getParam("student_type");
        $res = CommonUtils::requestDsOnline('coll/addStudentShow', array(
            'coll_id' => $coll_id,
            'school_id' => $this->branchId,
            'yid' => $yid,
            'student_type' => $student_type,
        ));
        if ($res['code'] == 0) {
            $data = $res['data'];
            $this->addMessage('state', 'success');
            $this->addMessage('data', $data);
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
        }
        $this->showMessage();
    }

    public function actionAddStudentOpe()
    {
        $coll_id = Yii::app()->request->getParam("coll_id");
        $data = Yii::app()->request->getParam("data");
        $res = CommonUtils::requestDsOnline('coll/addStudentOpe', array(
            'coll_id' => $coll_id,
            'school_id' => $this->branchId,
            'data' => $data
        ));
        if ($res['code'] == 0) {
            $data = $res['data'];
            $this->addMessage('state', 'success');
            $this->addMessage('data', $data);
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
        }
        $this->showMessage();
    }

    //编辑步骤的模板
    public function actionEditCollConfigShow()
    {
        $coll_id = Yii::app()->request->getParam("coll_id");
        $step_flag = Yii::app()->request->getParam("step_flag");
        $style = Yii::app()->request->getParam("style",'collStepConfigDefault');

        $data = $this->renderPartial('/collStepConfig/'.$style,array('step_flag'=>$step_flag,'coll_id'=>$coll_id),true);
        $this->addMessage('state', 'success');
        $this->addMessage('data', $data);
        $this->showMessage();
    }

    public function actionEditCollConfigShowInfo()
    {
        $coll_id = Yii::app()->request->getParam("coll_id");
        $step_flag = Yii::app()->request->getParam("step_flag");
        $res = CommonUtils::requestDsOnline('coll/editCollConfigShow', array(
            'coll_id' => $coll_id,
            'step_flag' => $step_flag,
        ));
        if ($res['code'] == 0) {
            $data = $res['data'];
            $this->addMessage('state', 'success');
            $this->addMessage('data', $data);
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
        }
        $this->showMessage();
    }

    public function actionEditCollConfigOpe()
    {
        $coll_id = Yii::app()->request->getParam("coll_id");
        $step_flag = Yii::app()->request->getParam("flag");
        $data = Yii::app()->request->getParam('data', array());

        $res = CommonUtils::requestDsOnline('coll/editCollConfigOpe', array(
            'coll_id' => $coll_id,
            'flag' => $step_flag,
            'data' => $data
        ));
        if ($res['code'] == 0) {
            $data = $res['data'];
            $this->addMessage('state', 'success');
            $this->addMessage('data', $data);
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
        }
        $this->showMessage();
    }

    // 导入站点名称  CSV
    public function actionImport()
    {
        Yii::import('common.models.visit.*');
        $model = new EventImportForm;
        $valArray = array();
        if (isset($_POST['EventImportForm'])) {
            $model->attributes = $_POST['EventImportForm'];
            if ($model->validate()) {
                $model->csv = CUploadedFile::getInstance($model, 'csv');
                if ($model->csv) {
                    $fileName = uniqid('', false) . '.' . $model->csv->getExtensionName();
                    $model->csv->saveAs(Yii::app()->params['xoopsVarPath'] . '/' . $fileName);
                    if (is_file(Yii::app()->params['xoopsVarPath'] . '/' . $fileName)) {
                        $fileArray = file(Yii::app()->params['xoopsVarPath'] . '/' . $fileName);
                        @unlink(Yii::app()->params['xoopsVarPath'] . '/' . $fileName);
                        foreach ($fileArray as $val) {
                            $val = iconv('GB2312', 'UTF-8', $val);
                            $val = str_replace(';', ',', $val);
                            $valArray[] = explode(',', $val);
                        }
                        array_shift($valArray);
                        $this->addMessage('state', 'success');
                        $this->addMessage('message', '成功');
                        $this->addMessage('data', $valArray);
//                        $this->addMessage('callback', 'cbSite');
                        $this->showMessage();
                    }
                }
            } else {
                $this->addMessage('state', 'fail');
                $errs = current($model->getErrors());
                $this->addMessage('message', $errs ? $errs[0] : Yii::t('message', 'Failed!'));
            }
        }
        $this->showMessage();
    }

    //站点列表
    public function actionCarSiteList()
    {
        $yid = Yii::app()->request->getParam("yid");
        $res = CommonUtils::requestDsOnline('schoolBus/list', array(
            'school_id' => $this->branchId,
            'yid' => $yid,
        ));
        if ($res['code'] == 0) {
            $data = $res['data'];
            $this->addMessage('state', 'success');
            $this->addMessage('data', $data);
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
        }
        $this->showMessage();
    }

    //修改站点
    public function actionEditCarsite()
    {
        $id = Yii::app()->request->getParam("id");
        $data = Yii::app()->request->getParam("data");
        $res = CommonUtils::requestDsOnline('schoolBus/edit', array(
            'id' => $id,
            'data' => $data,
        ));
        if ($res['code'] == 0) {
            $data = $res['data'];
            $this->addMessage('state', 'success');
            $this->addMessage('data', $data);
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
        }
        $this->showMessage();
    }


    // 增加站点录入
    public function actionAddCarsite()
    {
        $data = Yii::app()->request->getParam("data");
        $data['schoolid'] = $this->branchId;
        $res = CommonUtils::requestDsOnline('schoolBus/add', array(
            'data' => $data,
        ));
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res['data']);
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
        }
        $this->showMessage();
    }

    // 删除站点
    public function actionDelCarsite()
    {
        $id = Yii::app()->request->getParam("id");
        $res = CommonUtils::requestDsOnline('schoolBus/del', array(
            'id' => $id,
        ));
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res['data']);
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
        }
        $this->showMessage();
    }

    public function actionGetChildsParentEmail()
    {
        $id = Yii::app()->request->getParam("id");
        $type = Yii::app()->request->getParam("type",1);
        $res = CommonUtils::requestDsOnline('coll/getChildsParentEmail', array(
            'id' => $id,
            'type' => $type,
            'account' => $this->branchObj->type,
        ));
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res['data']);
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
        }
        $this->showMessage();
    }


    // 获取学生列表准备发微信通知
    public function actionTaskData()
    {
        $id = Yii::app()->request->getParam("id");
        $res = CommonUtils::requestDsOnline('coll/getTaskData', array(
            'coll_id' => $id,
            'school_id' => $this->branchId,
            'school_type' => $this->branchObj->type,
        ));
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res['data']);
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
        }
        $this->showMessage();
    }
    public function actionSendCollWxMsg()
    {
        $child_id = Yii::app()->request->getParam("child_id");
        $coll_id = Yii::app()->request->getParam("coll_id");
        $res = CommonUtils::requestDsOnline('coll/sendCollWxMsg',array(
            'coll_id' => $coll_id,
            'child_id' => $child_id,
        ));
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }

    //获取模板
    public function actionStepTemplate()
    {
        $step_id = Yii::app()->request->getParam("step_id");
        $coll_id = Yii::app()->request->getParam("coll_id");
        $type = Yii::app()->request->getParam("type",'unfilled');
        $type_status = Yii::app()->request->getParam("type_status");
        $data = $this->renderPartial($step_id,array('step_id'=>$step_id,'coll_id'=>$coll_id,'type'=>$type,'type_status'=>$type_status),true);
        $this->addMessage('state', 'success');
        $this->addMessage('data', $data);
        $this->showMessage();
    }

    //获取数据
    public function actionStepTemplateInfo()
    {
        $step_id = Yii::app()->request->getParam("step_id");
        $coll_id = Yii::app()->request->getParam("coll_id");
        $type = Yii::app()->request->getParam("type",'unfilled');
        if($type === 'unfilled'){
            $res = CommonUtils::requestDsOnline('coll/unfilledStepChildren/' . $coll_id.'/'.$step_id);
        }elseif($type === 'filled'){
            $res = CommonUtils::requestDsOnline('coll/filledStepChildren/' . $coll_id.'/'.$step_id);
        }elseif($type === 'audited'){
            //已审核
            $res = CommonUtils::requestDsOnline('coll/auditedStepChildren/' . $coll_id.'/'.$step_id);
        }elseif ($type === 'waitAudited'){
            //待审核
            $res = CommonUtils::requestDsOnline('coll/waitAuditedStepChildren/' . $coll_id.'/'.$step_id);
        }else{
            $this->addMessage('state', 'fail');
            $this->addMessage('message', 'type error!!');
            $this->showMessage();
        }
        if ($res['code'] == 0) {
            $res['data']['nameSearchComp'] = $this->renderPartial('nameSearch',array(),true);
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }

    public function actionGetStepOneChildrenData()
    {
        $step_id = Yii::app()->request->getParam("step_id");
        $coll_id = Yii::app()->request->getParam("coll_id");
        $childid = Yii::app()->request->getParam("childid");
        $res = CommonUtils::requestDsOnline2('coll/filledStepChildrenOne/' . $coll_id.'/'.$step_id.'/'.$childid,'','get');
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }

    // 审核数据
    public function actionStepAudit()
    {
        $coll_id = Yii::app()->request->getParam("coll_id");
        $child_id = Yii::app()->request->getParam("child_id");
        $step_id = Yii::app()->request->getParam("step_id");
        $audit_status = Yii::app()->request->getParam("audit_status");
        $comment = Yii::app()->request->getParam("comment");
        $send_wechat = Yii::app()->request->getParam("send_wechat", 0);
        $bus_routes_id = Yii::app()->request->getParam("bus_routes_id", 0);#额外审核数据

        if (!$coll_id || !$child_id || !$step_id) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '参数错误');
            $this->showMessage();
        }
        $staffName = $this->staff->getName();
        $data = array(
            'child_id' => $child_id,
            'audit_status' => $audit_status,
            'comment' => $comment,
            'staff_name' => $staffName,
            'send_wechat' => $send_wechat,
        );
        if(!empty($bus_routes_id)){
            $data['bus_routes_id'] = $bus_routes_id;
        }
        $res = CommonUtils::requestDsOnline('coll/auditStep/' . $coll_id.'/'.$step_id, $data);
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res['msg']);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }

    // 批量通过
    public function actionBatchPass()
    {
        $coll_id = Yii::app()->request->getParam("coll_id");
        $child_ids = Yii::app()->request->getParam("child_ids");
        $step_id = Yii::app()->request->getParam("step_id");
        if (!$coll_id || !$child_ids || !$step_id) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '参数错误');
            $this->showMessage();
        }
        $staffName = $this->staff->getName();
        $data = array(
            'child_ids' => $child_ids,
            'staff_name' => $staffName,
        );
        $res = CommonUtils::requestDsOnline('coll/batchPass/' . $coll_id.'/'.$step_id, $data);
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('message', $res['msg']);
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        }
    }

    // 保存额外数据
    public function actionSaveRemarks()
    {
        $coll_id = Yii::app()->request->getParam("coll_id");
        $child_id = Yii::app()->request->getParam("child_id");
        $step_id = Yii::app()->request->getParam("step_id");
        $remarks = Yii::app()->request->getParam("remarks");
        $type = Yii::app()->request->getParam("type");//send保存并发送邮件提示 save仅保存
        $remarks['type'] = $type;
        if (!$coll_id || !$child_id || !$step_id || !$remarks) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '参数错误');
            $this->showMessage();
        }
        $staffName = $this->staff->getName();
        $data = array(
            'child_id' => $child_id,
            'remarks' => $remarks,
            'staff_name' => $staffName
        );
        $res = CommonUtils::requestDsOnline2('coll/saveRemarks/' . $coll_id.'/'.$step_id, $data);
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res['msg']);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }

    public function actionStaffSaveStep()
    {
        $coll_id = Yii::app()->request->getParam("coll_id");
        $child_id = Yii::app()->request->getParam("child_id");
        $step_id = Yii::app()->request->getParam("step_id");
        $data = Yii::app()->request->getParam("data");
        if (!$coll_id || !$child_id || !$step_id) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '参数错误');
            $this->showMessage();
        }
        $data['child_id'] = $child_id;
        $data2 = array(
            'data' => $data,
        );
        $res = CommonUtils::requestDsOnline2('coll/staffSaveStep/' . $coll_id.'/'.$step_id, $data2);
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res['msg']);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }

    public function actionPickupCardList()
    {
        $coll_id = Yii::app()->request->getParam("coll_id");
        $step_id = Yii::app()->request->getParam("step_id");
        $url = 'pickupCardList';
        $res = CommonUtils::requestDsOnline('coll/'.$url.'/', array(
            'coll_id' => $coll_id,
            'school_id' => $this->branchId,
            'step_id' => $step_id,
        ));
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }

    public function actionIvyPickupCardList()
    {
        $coll_id = Yii::app()->request->getParam("coll_id");
        $step_id = Yii::app()->request->getParam("step_id");
        $url = 'ivyPickupCardList';
        $res = CommonUtils::requestDsOnline('coll/'.$url.'/', array(
            'coll_id' => $coll_id,
            'school_id' => $this->branchId,
            'step_id' => $step_id,
        ));
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }

    // 打印接具体送卡
    public function actionPrintCard()
    {
//        $this->layout = "//layouts/print";
        $this->layout = "//layouts/column3";
        $this->printFW = $this->branchObj->getPrintHeader();
        $childid = Yii::app()->request->getParam('childid', array());
        $school_id = $this->branchId;
        $coll_id = Yii::app()->request->getParam("coll_id");
        $data = array();
        $view = 'printcard';
        if($childid) {
            $childIdToPickupIndex = array();
            foreach ($childid as $item) {
                $arr = explode('-', $item);
                $childId = $arr[0];
                $index = $arr[1];
                $childIdToPickupIndex[$childId][] = $index;
            }
            $url = 'printPickupCardByChildId';
            $data = CommonUtils::requestDsOnline('coll/'.$url.'/', array(
                'childIds' => $childIdToPickupIndex,
                'school_id' => $school_id,
                'coll_id' => $coll_id,
            ));
        }
        $logo = "http://m2.files.ivykids.cn/cloud01-file-8025768FpN4sDubssQGgIeKSWowx2bdhdsT.png";
        if($this->branchObj->type == 50){
            $logo = "http://mega.ivymik.cn/reg/student/ds-hoz-logo.png";
        }
        if($this->branchId == 'BJ_QFF'){
            $logo ="https://m2.files.ivykids.cn/cloud01-file-8025768Fmub-A20IL1BnzYBRpT4CxWH-Eba.png";
        }
        $branchTitle = $this->branchObj->info->getTitle();

        if (isset($_POST['for_vendor'])) {
            $view = 'printCardVendor';
        }

        $this->render($view, array(
            'data' => $data['data'],
            'logo' => $logo,
            'branchTitle' => $branchTitle,
        ));
    }

    // 打印接具体送卡
    public function actionIvyPrintCard()
    {
        $this->layout = "//layouts/print";
        // $this->layout = "//layouts/column3";
        $this->printFW = $this->branchObj->getPrintHeader();
        $childid = Yii::app()->request->getParam('childid', array());
        $school_id = $this->branchId;
        $coll_id = Yii::app()->request->getParam("coll_id");
        $data = array();
        $view = 'ivyPrintcard';
        if($childid) {
            $url = 'ivyPrintPickupCardByChildId';
            $data = CommonUtils::requestDsOnline('coll/'.$url.'/', array(
                'childIds' => $childid,
                'school_id' => $school_id,
                'coll_id' => $coll_id,
            ));
        }
        $logo = "http://m2.files.ivykids.cn/cloud01-file-8025768FpN4sDubssQGgIeKSWowx2bdhdsT.png";
        if($this->branchObj->type == 50){
            $logo = "http://m2.files.ivykids.cn/cloud01-file-8025768Fmj6uC47xLVbcWqSYsHyG7Vuu5qJ.png";
        }
        if($this->branchId == 'BJ_QFF'){
            $logo ="https://m2.files.ivykids.cn/cloud01-file-8025768Fmub-A20IL1BnzYBRpT4CxWH-Eba.png";
        }
        $branchTitle = $this->branchObj->info->getTitle();
        $this->render($view, array(
            'data' => $data['data'],
            'logo' => $logo,
            'branchTitle' => $branchTitle,
        ));
    }

    /**
     * 打印收集信息的详情
     */
    public function actionPrintDetail()
    {
        Yii::import('common.models.ChildProfileBasic.*');
        $this->layout = "//layouts/print";
        $this->printFW = $this->branchObj->getPrintHeader();
        $child_id = Yii::app()->request->getParam('childid', array());
        $school_id = $this->branchId;
        $step_id = Yii::app()->request->getParam("step_id");
        $coll_id = Yii::app()->request->getParam("coll_id");
        $data = array();
        if($child_id) {
            $data = CommonUtils::requestDsOnline('coll/printDetail/', array(
                'childId' => $child_id,
                'school_id' => $school_id,
                'coll_id' => $coll_id,
                'step_flag' => $step_id,
            ));
            //孩子的信息
            $child_info = ChildProfileBasic::model()->findByPk($child_id);
        }
//        header("Content-Type:text/html;charset=utf-8");
//        echo "<pre>";
//        var_dump($data['data']);die;
        $view = 'print'.ucwords($step_id);
        $this->render($view, array(
            'step_data' => $data['data'],
            'child_info' => $child_info,
        ));

    }
    public function actionBatchPrint()
    {
        Yii::import('common.models.ChildProfileBasic.*');
        $this->layout = "//layouts/print";
        $this->printFW = $this->branchObj->getPrintHeader();
        $child_ids = Yii::app()->request->getParam('childid');
        $school_id = $this->branchId;
        $step_id = Yii::app()->request->getParam("step_id");
        $coll_id = Yii::app()->request->getParam("coll_id");
        $data = array();
        $child_infos = array();
        if($child_ids) {
            $data = CommonUtils::requestDsOnline('coll/batchPrintDetail/', array(
                'childIds' => $child_ids,
                'school_id' => $school_id,
                'coll_id' => $coll_id,
                'step_flag' => $step_id,
            ));
            //孩子的信息
            $child_ids = explode(',',$child_ids);
            $child_infos = ChildProfileBasic::model()->findAllByPk($child_ids,array("index" => "childid","order"=>"childid DESC"));
        }
//        echo "<pre>";
//        var_dump($data['data']);die;
////        var_dump($child_infos);die;
        $view = 'batchPrint'.ucwords($step_id);
        $this->render($view, array(
            'step_data' => $data['data'],
            'child_infos' => $child_infos,
        ));
    }

    public function actionGetAllDepartment()
    {
        $step_id = Yii::app()->request->getParam("step_id");
        $school_id = Yii::app()->request->getParam("school_id",'');
        $url = 'coll/getAllDepartment/'.$step_id;
        $res = CommonUtils::requestDsOnline2($url,array(
            'school_id'=> empty($school_id) ? $this->branchId : $school_id,
        ),'get','');

        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }

    public function actionSchoolList()
    {
        $step_id = Yii::app()->request->getParam("step_id");
        $school_id = Yii::app()->request->getParam("school_id",'');
        $url = 'coll/schoolList/'.$step_id;
        $res = CommonUtils::requestDsOnline2($url,array(
            'school_id'=> empty($school_id) ? $this->branchId : $school_id,
        ),'get','');

        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }



}
