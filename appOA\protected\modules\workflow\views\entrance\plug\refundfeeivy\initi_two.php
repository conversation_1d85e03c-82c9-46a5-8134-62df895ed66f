<!-- Modal -->
<?php 
    $labels = Invoice::model()->attributeLabels();
?>
<?php echo CHtml::form('/workflow/entrance/index', 'post',array('class'=>'J_ajaxForm'));?>
<div class="modal fade" id="<?php echo $data['modalNameId']?>" tabindex="-1" role="dialog" aria-labelledby="workflowModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel"><?php echo CommonUtils::autoLang($data['workflow']->definationObj->defination_name_cn,$data['workflow']->definationObj->defination_name_en);?> - <?php echo CommonUtils::autoLang($data['workflow']->nodeObj->node_name_cn,$data['workflow']->nodeObj->node_name_en);?></h4>
            </div>
            <div class="modal-body" id="workflow-modal-body">
                <ul class="nav nav-wizard mb20">
                    <li style="width:33%;"><a>第一步：选择退费孩子</a></li>
                    <li class="active" style="width:33%;"><a>第二步：选择退费账单</a></li>
                    <li style="width:33%;"><a>第三步：确认退费金额</a></li>
                </ul>
                <?php
                    $childid = current($data['invoices'])->childid;
                    if($childid){
                        $refundfeeNum = ChildProfileBasic::model()->findByPk($childid)->refundfeeNum();
                        echo '<p class="text-danger">本学年退过' .$refundfeeNum. '次学费</p>';
                    }
                 ?>
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <td></td>
                                <td><?php echo $labels['title'];?></td>
                                <td><?php echo $labels['payment_type'];?></td>
                                <td><?php echo $labels['amount'];?></td>
                                <td><?php echo $labels['startdate'];?></td>
                                <td><?php echo $labels['enddate'];?></td>
                                <td><?php echo Yii::t('workflow','Tips');?></td>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($data['invoices'] as $invoice): ?>
                            <tr>
                                <td><?php echo CHtml::checkBox('invoiceId[]',false,array('value'=>$invoice->invoice_id));?></td>
                                <td><?php echo $invoice->title;?></td>
                                <td><?php echo $invoice->payment_type;?></td>
                                <td><?php echo $invoice->amount;?></td>
                                <td><?php echo OA::formatDateTime($invoice->startdate);?></td>
                                <td><?php echo OA::formatDateTime($invoice->enddate);?></td>
                                <td width="50" class="text-danger">
                                <?php
                                    $exte1 = $data['workflow']->opertationObj->exte1;
                                    $exte2 = ($data['workflow']->getOperateValue('exte2')) ? $data['workflow']->getOperateValue('exte2') : $invoice->enddate;
                                    foreach ($invoice->workflowRefund as $workflowRefund) {
                                        if ($invoice->payment_type == 'lunch' && RefundLunch::model()->getRefundLunch($invoice->childid,$exte1,$exte2)) {
                                            echo "单独取消过午餐";
                                            break;
                                        } elseif ($workflowRefund->status == InvoiceChildRefund::STATS_AWAITING) {
                                            echo "退费中";
                                            break;
                                        } 
                                    }  
                                ?>
                                </td>
                            </tr>
                            <?php endforeach;?>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <?php echo CHtml::hiddenField('definationId', $data['workflow']->getWorkflowId());?>
                <?php echo CHtml::hiddenField('nodeId', $data['workflow']->getCurrentNodeId());?>
                <?php echo CHtml::hiddenField('branchId', $data['workflow']->schoolId);?>
                <?php echo CHtml::hiddenField('operationId', $data['workflow']->getOperateId());?>
                <?php echo CHtml::hiddenField('step', 'two');?>
                <?php echo CHtml::hiddenField('buttonCategory', '');?>
                <?php echo CHtml::hiddenField('action', 'publicSave');?>
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('global','Cancel');?></button>
                <button type="button" class="btn btn-info J_ajax_submit_btn" data-category="previous"><?php echo Yii::t('workflow','Prev');?></button>
                <button type="button" class="btn btn-primary J_ajax_submit_btn" data-category="next"><?php echo Yii::t('workflow','Next');?></button>
            </div>
        </div>
    </div>
</div>
<?php echo CHtml::endForm();?>
<script type=" text/javascript">
    var nodeListHtml = '<?php echo $data['workflow']->getNodeListUseHtml();?>';
    var currentOperatorId = <?php echo $data['workflow']->getOperateId();?>;
    if (!$('#operation_id_'+currentOperatorId).html()){
        $('#wait-check-list').prepend(nodeListHtml);
        head.Util.ajaxDel();
        head.Util.ajaxForm();
        head.Util.ajax();
    }
</script>