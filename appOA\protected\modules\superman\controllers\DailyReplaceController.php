<?php
//课表按天特殊调整
class DailyReplaceController extends TeachBasedController
{
    public function createUrlReg($route, $params = array(), $ampersand = '&')
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        return parent::createUrl($route, $params, $ampersand);
    }

    public function init()
    {
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $schoolList = array(
            'BJ_DS',);
        foreach ($this->accessBranch as $key => $item) {
            if (!in_array($item, $schoolList)) {
                unset($this->accessBranch[$key]);
            }
        }
        //初始化选择校园页面
        $this->branchSelectParams['urlArray'] = array('//superman/dailyReplace/index',);
        $cs = Yii::app()->clientScript;
        $cs->registerCoreScript('jquery.ui');
        $cs->registerCssFile($cs->getCoreScriptUrl() . '/jui/css/base/jquery-ui.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/vue2.js');
    }

    //展示页面
    public function actionIndex()
    {
        $res = CommonUtils::requestDsOnline('timetable/dailyReplace','', 'get');
        $this->render('/DailyReplace/index', array(
            'data' => $res['data'],
        ));
    }

    public function actionDelDailyReplace()
    {
        $id = Yii::app()->request->getParam("id");
        $res = CommonUtils::requestDsOnline('timetable/delDailyReplace',array(
            'id'=>$id,
        ));
        if($res){
            $this->addMessage('state', 'success');
            $this->addMessage('message', '删除成功');
            $this->showMessage();
        }
    }

    public function actionAddData()
    {
        $time1 = Yii::app()->request->getParam("time1");
        $time2 = Yii::app()->request->getParam("time2");
        $school_year = Yii::app()->request->getParam("school_year");
        $ex_school_year = explode('-',$school_year);
        $res = CommonUtils::requestDsOnline('timetable/addDailyReplace',array(
            'time1'=>$time1,
            'time2'=>$time2,
            'yid'=>$ex_school_year[0],
            'start_year'=>$ex_school_year[1],
        ));
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('message', '添加成功');
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
        }
        $this->showMessage();
    }
}
