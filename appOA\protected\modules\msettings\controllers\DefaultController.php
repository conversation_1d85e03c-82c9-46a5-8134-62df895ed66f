<?php

class DefaultController extends ProtectedController
{
    public function init(){
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'sysConfig';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site','System Management');
    }

    public function actionIndex()
    {
        $this->render('application.modules.mcampus.views.default.index', array('key'=>'sysConfig'));
    }
}
