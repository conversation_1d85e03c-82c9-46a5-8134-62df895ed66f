<?php
$form=$this->beginWidget('CActiveForm', array(
    'id'=>'visits-form',
    'enableAjaxValidation'=>false,
    'action' => $this->createUrl('updatedRule',array("srid"=> $model->id)),
    'htmlOptions'=>array('class'=>'J_ajaxForm form-horizontal', 'role'=>'form'),
));

$month = array();
for ($m = 1; $m < 13; $m++) {
    $month[sprintf("%02d", $m)] = sprintf("%02d", $m);
}

$year = array(date("Y", time()) => date("Y", time()),  date("Y", time())+1 => date("Y", time())+1);
$yearDate = ($model->start_time) ? substr($model->start_time, 0,4) : "";
$monthDate = ($model->start_time) ? substr($model->start_time, 4) : "";

?>
<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">×</span></button>
    <h4 class="modal-title"><?php echo Yii::t('site','增加详细选项')?></h4>
</div>
<div class="modal-body">
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'title_cn'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->textField($model,'title_cn',array('maxlength'=>255,'class'=>'form-control')); ?>
        </div>
    </div>
    <!-- 英文名称 -->
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'title_en'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->textField($model,'title_en',array('maxlength'=>255,'class'=>'form-control')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'document_cn'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->textField($model,'document_cn',array('maxlength'=>255,'class'=>'form-control')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'document_en'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->textField($model,'document_en',array('maxlength'=>255,'class'=>'form-control')); ?>
        </div>
    </div>

    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'start_time'); ?></label>
        <div class="col-xs-3">
            <?php echo CHtml::dropDownList('year', $yearDate, $year, array('class'=> 'form-control', 'empty' => Yii::t('teaching', '年份'))) ?>
        </div>
        <div class="col-xs-3">
            <?php echo CHtml::dropDownList('month',$monthDate, $month, array('class'=> 'form-control', 'empty' => Yii::t('teaching', '年份'))) ?>
        </div>
    </div>
    <!-- 状态 -->
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'status'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->checkbox($model,'status', array('1'=>"开启"),array('template'=>'<label class="radio-inline">{input}{label}</label>','separator'=>'')); ?>
        </div>
    </div>
</div>
<div class="modal-footer">
    <button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global','Submit');?></button>
    <button type="button" class="btn btn-default"  data-dismiss="modal"><?php echo Yii::t('global','Cancel');?></button>
</div>

<?php $this->endWidget(); ?>
<script>
    // 回调：添加成功
    function cbSecurityRule() {
        $('#modal').modal('hide');
        location.reload()
    }
</script>
