<?php

/**
 * This is the model class for table "ivy_stats_child_count".
 *
 * The followings are the available columns in table 'ivy_stats_child_count':
 * @property integer $id
 * @property string $schoolid
 * @property integer $classid
 * @property integer $yid
 * @property integer $num1
 * @property integer $num2
 * @property integer $num3
 * @property integer $num4
 * @property integer $num5
 * @property integer $num6
 * @property integer $num7
 * @property integer $num8
 * @property integer $num9
 * @property integer $num10
 * @property integer $num11
 * @property integer $num12
 * @property integer $num13
 * @property integer $num14
 * @property integer $num15
 * @property integer $num16
 * @property string $fte_ratio
 * @property string $childids1
 * @property string $childids2
 * @property string $childids3
 * @property string $childids4
 * @property string $childids5
 * @property string $childids6
 * @property string $childids7
 * @property string $childids8
 * @property string $childids9
 * @property string $childids10
 * @property string $childids11
 * @property string $childids12
 * @property string $childids13
 * @property string $childids14
 * @property string $childids15
 * @property string $childids16
 * @property integer $period_timestamp
 * @property integer $update_timestamp
 */
class StatsChildCount extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @return StatsChildCount the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_stats_child_count';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
//			array('childids1, childids2, childids3, childids4, childids5, childids6, childids7, childids8, childids9, childids10, childids11, childids12, childids13, childids14, childids15, childids16', 'required'),
			array('classid, yid, num1, num2, num3, num4, num5, num6, num7, num8, num9, num10, num11, num12, num13, num14, num15, num16, period_timestamp, update_timestamp', 'numerical', 'integerOnly'=>true),
			array('schoolid', 'length', 'max'=>20),
			array('fte_ratio', 'length', 'max'=>50),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, schoolid, classid, yid, num1, num2, num3, num4, num5, num6, num7, num8, num9, num10, num11, num12, num13, num14, num15, num16, fte_ratio, childids1, childids2, childids3, childids4, childids5, childids6, childids7, childids8, childids9, childids10, childids11, childids12, childids13, childids14, childids15, childids16, period_timestamp, update_timestamp', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'classTitle'=>array(self::BELONGS_TO, 'IvyClass', 'classid'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'schoolid' => 'Schoolid',
			'classid' => 'Classid',
			'yid' => 'Yid',
			'num1' => 'Num1',
			'num2' => 'Num2',
			'num3' => 'Num3',
			'num4' => 'Num4',
			'num5' => 'Num5',
			'num6' => 'Num6',
			'num7' => 'Num7',
			'num8' => 'Num8',
			'num9' => 'Num9',
			'num10' => 'Num10',
			'num11' => 'Num11',
			'num12' => 'Num12',
			'num13' => 'Num13',
			'num14' => 'Num14',
			'num15' => 'Num15',
			'num16' => 'Num16',
			'fte_ratio' => 'Fte Ratio',
			'childids1' => 'Childids1',
			'childids2' => 'Childids2',
			'childids3' => 'Childids3',
			'childids4' => 'Childids4',
			'childids5' => 'Childids5',
			'childids6' => 'Childids6',
			'childids7' => 'Childids7',
			'childids8' => 'Childids8',
			'childids9' => 'Childids9',
			'childids10' => 'Childids10',
			'childids11' => 'Childids11',
			'childids12' => 'Childids12',
			'childids13' => 'Childids13',
			'childids14' => 'Childids14',
			'childids15' => 'Childids15',
			'childids16' => 'Childids16',
			'period_timestamp' => 'Period Timestamp',
			'update_timestamp' => 'Update Timestamp',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('classid',$this->classid);
		$criteria->compare('yid',$this->yid);
		$criteria->compare('num1',$this->num1);
		$criteria->compare('num2',$this->num2);
		$criteria->compare('num3',$this->num3);
		$criteria->compare('num4',$this->num4);
		$criteria->compare('num5',$this->num5);
		$criteria->compare('num6',$this->num6);
		$criteria->compare('num7',$this->num7);
		$criteria->compare('num8',$this->num8);
		$criteria->compare('num9',$this->num9);
		$criteria->compare('num10',$this->num10);
		$criteria->compare('num11',$this->num11);
		$criteria->compare('num12',$this->num12);
		$criteria->compare('num13',$this->num13);
		$criteria->compare('num14',$this->num14);
		$criteria->compare('num15',$this->num15);
		$criteria->compare('num16',$this->num16);
		$criteria->compare('fte_ratio',$this->fte_ratio,true);
		$criteria->compare('childids1',$this->childids1,true);
		$criteria->compare('childids2',$this->childids2,true);
		$criteria->compare('childids3',$this->childids3,true);
		$criteria->compare('childids4',$this->childids4,true);
		$criteria->compare('childids5',$this->childids5,true);
		$criteria->compare('childids6',$this->childids6,true);
		$criteria->compare('childids7',$this->childids7,true);
		$criteria->compare('childids8',$this->childids8,true);
		$criteria->compare('childids9',$this->childids9,true);
		$criteria->compare('childids10',$this->childids10,true);
		$criteria->compare('childids11',$this->childids11,true);
		$criteria->compare('childids12',$this->childids12,true);
		$criteria->compare('childids13',$this->childids13,true);
		$criteria->compare('childids14',$this->childids14,true);
		$criteria->compare('childids15',$this->childids15,true);
		$criteria->compare('childids16',$this->childids16,true);
		$criteria->compare('period_timestamp',$this->period_timestamp);
		$criteria->compare('update_timestamp',$this->update_timestamp);

		return new CActiveDataProvider(get_class($this), array(
			'criteria'=>$criteria,
		));
	}
	
	public function getChild($schoolid, $time=0)
	{
		$childids = array();
		if ($schoolid && $time){
			$time = mktime(0,0,0,date('m',$time),date('d',$time),date('Y',$time));
			$criteria = new CDbCriteria;
			$criteria->compare('period_timestamp', $time);
			$criteria->compare('schoolid', $schoolid);
			
			$items = $this->model()->findAll($criteria);
			foreach($items as $item){
				if ($item->classid){
					$chids = $item->childids1.','.$item->childids2;
					foreach(explode(',',$chids) as $cid){
						if ($cid){
							$childids[$cid] = $cid;
						}
					}
				}
			}
		}
		return $childids;
	}
    
    /*
     * 查询一段日期内每周周五在读人数的平均数
     * @param string $schoolId 
     * @param array $days       每周周五日期时间戳
     * @return int $num         学校在读的总人数
     */
    static public function getEnrollmentAverageNumber($schoolId,$days){
        $num = 0;
        $numData = Yii::app()->db->createCommand()
                    ->select('sum(num1) as num1,sum(num2) as num2')
                    ->from('ivy_stats_child_count')
                    ->where('schoolid=:schoolid and classid=:classid',array(':schoolid'=>$schoolId,':classid'=>0))
                    ->andWhere(array('in','period_timestamp',$days))
                    ->queryAll();
         if (is_array($numData) && count($numData)){
             $num = array_sum(current($numData));
             return ceil($num/count($days));
         }
         return $num;
    }
}