<?php

class WSurveyFeedbackForm extends CFormModel
{
	public $survey_id = 0;
	public $child_id = 0;
	
	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('survey_id, child_id', 'numerical', 'integerOnly'=>true),
		);
	}


	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'survey_id' => 'Survey',
			'child_id' => 'Childid',
		);
	}

}