<div class="row">
    <div class="col-md-12">
        <div class="panel panel-default">
            <div class="panel-heading"><?php echo Yii::t('teaching','School News');?></div>
            <div class="panel-body">
                <?php echo CHtml::form('', 'post', array('class'=>'form-horizontal J_ajaxForm', 'role'=>'form'));?>
                <div class="form-group">
                    <label class="col-sm-2 control-label"><?php echo Yii::t('labels','Content');?></label>
                    <div class="col-sm-10">
                        <?php
                        $toolbars = array('fullscreen', 'source', '|', 'undo', 'redo', '|','bold', 'italic', 'underline', 'strikethrough', 'superscript', 'subscript', '|','insertorderedlist', 'insertunorderedlist', '|','link', 'unlink', 'attachment');
                        if (Yii::app()->params['siteFlag'] == 'daystar') {
                            $toolbars = array('fullscreen', 'source', '|', 'undo', 'redo', '|','bold', 'italic', 'underline', 'strikethrough', 'superscript', 'subscript', '|','insertorderedlist', 'insertunorderedlist', '|','link', 'unlink', 'attachment', 'insertimage');
                        }
                        $this->widget('common.extensions.ueditor.ueditor_1_5_0',array(
                            'model' => $taskData['model'],
                            'attribute' => 'en_content',
                            'configFile' => 'ueditor.teacher.config',
                            'language' =>Yii::app()->language == 'en_us' ? 'en' : 'zh-cn',
                            'editorOptions' => array('initialFrameWidth'=>'80%'),
                            'toolbars'=>$toolbars,
                            'classId' => $this->selectedClassId,
                        ));
                        ?>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-2 control-label"><?php echo Yii::t('teaching','Key Reminders');?></label>
                    <div class="col-sm-10">
                        <?php
                        $toolbars = array('fullscreen', 'source', '|', 'undo', 'redo', '|','bold', 'italic', 'underline', 'strikethrough', 'superscript', 'subscript', '|','insertorderedlist', 'insertunorderedlist', '|','link', 'unlink', 'attachment');
                        if (Yii::app()->params['siteFlag'] == 'daystar') {
                            $toolbars = array('fullscreen', 'source', '|', 'undo', 'redo', '|','bold', 'italic', 'underline', 'strikethrough', 'superscript', 'subscript', '|','insertorderedlist', 'insertunorderedlist', '|','link', 'unlink', 'attachment', 'insertimage');
                        }
                        $this->widget('common.extensions.ueditor.ueditor_1_5_0',array(
                            'model' => $taskData['model'],
                            'attribute' => 'en_important',
                            'configFile' => 'ueditor.teacher.config',
                            'language' =>Yii::app()->language == 'en_us' ? 'en' : 'zh-cn',
                            'editorOptions' => array('initialFrameWidth'=>'80%'),
                            'toolbars'=>$toolbars,
                            'classId' => $this->selectedClassId,
                        ));
                        ?>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-2 control-label"><?php echo Yii::t('teaching','Campus Director');?></label>
                    <div class="col-sm-10">
                        <div class="checkbox">
                            <?php foreach($taskData['cds'] as $uid=>$cds):?>
                                <label class="mr15">
                                    <?php
                                    if(in_array($uid, $taskData['model']->cd_uids)){
                                        $checked = true;
                                    }
                                    else{
                                        $checked = false;
                                    }
                                    echo CHtml::checkBox('NotesSchCla[cd_uids][]', $checked, array('value'=>$uid));
                                    ?>
                                    <?php echo $cds;?>
                                </label>
                            <?php endforeach;?>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-2 control-label"><?php echo Yii::t('teaching','Make Online');?></label>
                    <div class="col-sm-10">
                        <div class="checkbox">
                            <label>
                                <?php echo CHtml::activeCheckBox($taskData['model'], 'stat', array('value'=>20));?>
                                &nbsp;
                            </label>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-2"></label>
                    <div class="col-sm-10">
                        <button class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global','Save');?></button>
                    </div>
                </div>
                <?php echo CHtml::endForm();?>
            </div>
        </div>
    </div>

</div>
