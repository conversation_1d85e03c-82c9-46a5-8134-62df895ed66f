<?php $form=$this->beginWidget('CActiveForm', array(
	'id'=>'event-memberivy-form',
	'enableAjaxValidation'=>false,
    'htmlOptions'=>array('class'=>'J_ajaxForm form-horizontal', 'role'=>'form'),
));
?>
<div class="pop_cont pop_table" style="height:auto;">
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'parent_name'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->textField($model,'parent_name',array('maxlength'=>255,'class'=>'form-control')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'telphone'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->textField($model,'telphone',array('maxlength'=>255,'class'=>'form-control')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'schoolid'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->dropDownList($model,'schoolid',$this->branchArr,array('empty'=>Yii::t('global','Please Select'),'class'=>'form-control')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'ivyclass'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->textField($model,'ivyclass',array('maxlength'=>255,'class'=>'form-control')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'child_name'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->textField($model,'child_name',array('maxlength'=>255,'class'=>'form-control')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'adult_number'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->numberField($model,'adult_number',array('maxlength'=>255,'class'=>'form-control length_1')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'kid_number'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->numberField($model,'kid_number',array('maxlength'=>255,'class'=>'form-control length_1')); ?>
        </div>
    </div>
</div>
<div class="pop_bottom">
    <button id="J_dialog_close" type="button" class="btn btn-default pull-right"><?php echo Yii::t('global','Cancel');?></button>
    <button type="submit" class="btn btn-primary J_ajax_submit_btn pull-right mr10"><?php echo Yii::t('global','Submit');?></button>
</div>
<?php $this->endWidget(); ?>