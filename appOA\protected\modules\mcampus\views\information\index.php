<?php
$mailIds = CommonUtils::LoadConfig('CfgInformation');
?>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('//mcampus/default/index')) ?></li>
        <li><?php echo CHtml::link(Yii::t('site', 'Campus Support'), array('//mcampus/default/index')) ?></li>
        <li class="active"><?php echo Yii::t('site', 'Parent Information') ?></li>
        <li class="active"><?php echo Yii::t('site', 'Content Management') ?></li>
    </ol>

    <div class="row">
        <!-- ���˵� -->
        <div class="col-md-2 col-sm-2">
            <?php
            $this->widget('zii.widgets.CMenu', array(
                'items' => $this->getMenu(),
                'id' => 'visitConfiguration',
                'htmlOptions' => array('class' => 'nav nav-pills nav-stacked background-gray mb10'),
                'activeCssClass' => 'active',
                'itemCssClass' => ''
            ));
            ?>
        </div>

        <div class="col-md-2 col-sm-2">
            <ul class="nav nav-pills nav-stacked background-gray">
                <?php
                foreach ($structureObj as $v) {
                    $href = $this->createUrl('index', array('type' => $type, 'id' => $v->id));
                    $active = '';

                    $title = Yii::app()->language == "zh_cn" ?
                        empty($v->title_cn) ? $v->title_cn : $v->title_cn
                        :
                        empty($v->title_en) ? $v->title_cn : $v->title_en;

                    if ($v->id == $id) {
                        $active = 'active';
                        $selected_title = $title;
                    }

                    echo "<li class='{$active}'><a href='{$href}'>";
                    echo $title;
                    echo '</a></li>';
                }
                ?>
            </ul>
        </div>

        <div class="col-md-2 col-sm-2">
            <ul class="nav nav-pills nav-stacked background-gray">
                <?php
                foreach ($structureObjT as $v) {
                    $href = $this->createUrl('index', array('type' => $type, 'id' => $id, 'pid' => $v->id));
                    $active = '';

                    $title = Yii::app()->language == "zh_cn" ?
                        empty($v->title_cn) ? $v->title_cn : $v->title_cn
                        :
                        empty($v->title_en) ? $v->title_cn : $v->title_en;

                    if ($v->id == $pid) {
                        $active = 'active';
                        $selected_title = $title;
                    }

                    echo "<li class='{$active}'><a href='{$href}'>";
                    echo $title;
                    echo '</a></li>';
                }
                ?>
            </ul>
        </div>

        <div class="col-md-6 col-sm-12">
            <?php if ($pid): ?>
                <div class="mb10 text-right">
                    <a class="J_modal btn btn-primary" href="" onclick="informAdd()"><span
                            class="glyphicon glyphicon-plus"></span> <?php echo Yii::t('campus', 'Add record') ?></a>
                </div>
                <div class="panel panel-default">
                    <div class="panel-body">
                        <?php
                        $this->widget('ext.ivyCGridView.BsCGridView', array(
                            'id' => 'informaction',
                            'dataProvider' => $dataProvider,
                            'afterAjaxUpdate' => 'js:function(){head.Util.ajaxDel()}',
                            'colgroups' => array(
                                array(
                                    "colwidth" => array(100, 100, 100, 100, 100, 100, 100),
                                )
                            ),
                            'columns' => array(
                                /*array(
                                    'name' => Yii::t('ivyer', 'cagegory'),
                                    'value' => array($this, 'getCagegory'),
                                ),*/
                                array(
                                    'name' => Yii::t('ivyer', 'title_en'),
                                    'value' => '$data->title_en',
                                ),
                                array(
                                    'name' => Yii::t('ivyer', 'title_cn'),
                                    'value' => '$data->title_cn',
                                ),
                                array(
                                    'name' => Yii::t('ivyer', 'published'),
                                    'value' => '($data->published == 1) ?  Yii::t("user","Yes") :  Yii::t("user","No") ',
                                ),
                                array(
                                    'name' => Yii::t('site', 'Display on Frontpage'),
                                    'value' => '($data->is_home == 1) ?  Yii::t("user","Yes") :  Yii::t("user","No") ',
                                ),
                                array(
                                    'name' => Yii::t('curriculum', 'Public Time'),
                                    'value' => 'date("Y-m-d", $data->times)',
                                ),
                                array(
                                    'name' => Yii::t('ivyer', 'updated'),
                                    'value' => 'date("Y-m-d", $data->updated)',
                                ),
                                array(
                                    'name' => Yii::t('global', 'Action'),
                                    'type' => 'raw',
                                    'value' => array($this, 'getButtons'),
                                ),
                            ),
                        ));
                        ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<div class="modal fade" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <?php $form = $this->beginWidget('CActiveForm', array(
                'id' => 'visits-form',
                'enableAjaxValidation' => false,
                'htmlOptions' => array('class' => 'J_ajaxForm form-horizontal', 'role' => 'form'),
                'action' => $this->createUrl('inform'),
            ));


            ?>
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                        aria-hidden="true">×</span></button>
                <h4 class="modal-title"><?php echo $modalTitle; ?></h4>
            </div>
            <div class="modal-body">
                <!-- 类型 -->
                <div class="form-group">
                    <label class="col-xs-3 control-label"><?php echo Yii::t('labels', 'Type') ?></label>

                    <div class="col-xs-9">
                        <select id="InformationDs_cagegory" name="InformationDs[cagegory]" class="form-control">
                            <option value=""><?php echo Yii::t('site', 'Please Select') ?></option>
                            <?php
                            if ($structureList) {
                                foreach ($structureList as $k => $item) { ?>
                                    <optgroup label="<?php echo $k ?>">
                                        <?php foreach ($item as $key => $ite) { ?>
                                            <option value="<?php echo $key ?>"><?php echo $ite ?></option>
                                        <?php } ?>
                                    </optgroup>
                                <?php } ?>
                            <?php } ?>
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-xs-3 control-label"><?php echo Yii::t('curriculum', 'Cn Title') ?></label>

                    <div class="col-xs-9">
                        <?php echo $form->textField($model, 'title_cn', array('maxlength' => 255, 'class' => 'form-control')); ?>

                        <?php echo CHtml::hiddenField('id', "", array('id' => 'inform_id')); ?>
                    </div>
                </div>

                <!-- 英文标题 -->
                <div class="form-group">
                    <label class="col-xs-3 control-label"><?php echo Yii::t('curriculum', 'En Title') ?></label>

                    <div class="col-xs-9">
                        <?php echo $form->textField($model, 'title_en', array('maxlength' => 255, 'class' => 'form-control')); ?>
                    </div>
                </div>
                <!-- 中文标题 -->


                <div class="form-group">
                    <label class="col-xs-3 control-label"><?php echo $form->labelEx($model, 'file_cn'); ?></label>

                    <div class="col-xs-9">
                        <div id="inffile_cn"></div>
                        <input name="InformationDs[inform_file_cn]" class="new_Employee_Input" id="Staff_bankcard_cn"
                               type="file"/>
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-xs-3 control-label"><?php echo $form->labelEx($model, 'file_en'); ?></label>

                    <div class="col-xs-9">
                        <div id="inffile_en"></div>
                        <input name="InformationDs[inform_file_en]" class="new_Employee_Input" id="Staff_bankcard_en"
                               type="file"/>
                    </div>
                </div>


                <div class="form-group">
                    <label class="col-xs-3 control-label"><?php echo Yii::t('curriculum', 'published'); ?></label>

                    <div class="col-xs-9">
                        <?php echo $form->CheckBox($model, 'published', array()); ?>
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-xs-3 control-label"><?php echo Yii::t('site', 'Display on Frontpage'); ?></label>

                    <div class="col-xs-9">
                        <?php echo $form->CheckBox($model, 'is_home', array()); ?>
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-xs-3 control-label"><?php echo Yii::t('curriculum', 'Public Time') ?></label>

                    <div class="col-xs-9">
                        <?php
                        $this->widget('zii.widgets.jui.CJuiDatePicker', array(
                            "model" => $model,
                            "name" => "InformationDs[times]",
                            "options" => array(
                                'changeMonth' => true,
                                'changeYear' => true,
                                'dateFormat' => 'yy-mm-dd',
                            ),
                            'htmlOptions' => array(
                                'class' => 'form-control',
                                'placeholder' => "",
                                'style' => 'width:100%'
                            ),
                        ));
                        ?>
                    </div>
                </div>

            </div>
            <div class="modal-footer">
                <button type="submit"
                        class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global', 'Submit'); ?></button>
                <button type="button" class="btn btn-default"
                        data-dismiss="modal"><?php echo Yii::t('global', 'Cancel'); ?></button>
            </div>

            <?php $this->endWidget(); ?>

        </div>
    </div>
</div>

<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>
<script>

    function informAdd() {
        emptyContent();
        /* if(childid){
         var childParents = ;
         $('<option></option>').val(childid).text(childParents[childid]['name']).appendTo( $('#ChildVacation_child_id') );
         }*/
        $('#myModal').modal({backdrop: 'static'});
    }
    // 模态框
    /*var modal = '<div class="modal fade" id="modal" tabindex="-1" role="dialog" aria-labelledby="modal"><div class="modal-dialog" role="document"><div class="modal-content"></div></div></div>';
     $('body').append(modal);*/

    function informEdit(id) {
        $.ajax({
            url: '<?php echo $this->createUrl('informEied');?>',
            data: {id: id},
            type: 'post',
            dataType: 'json',
            success: function (data) {
                emptyContent();
                $('#myModal').modal({backdrop: 'static', keyboard: false});
                    $("#InformationDs_cagegory option[value='" + data.cagegory + "']").attr("selected", true);

                $('#InformationDs_times').val(data.times);
                $('#InformationDs_title_en').val(data.title_en);
                $('#InformationDs_title_cn').val(data.title_cn);
//                $("#InformationDs_cagegory  option[value=data.cagegory] ").attr("selected",true)

                $('#inform_id').val(data.id);
                $('#inffile_en').html('<a href="<?php echo Yii::app()->params['OAUploadBaseUrl']  .'/information/' ?>' + data.file_en + '" target="_black" class="btn btn-info btn-xs">有文件</a>');
                $('#inffile_cn').html('<a href="<?php echo Yii::app()->params['OAUploadBaseUrl']  .'/information/' ?>' + data.file_cn + '" target="_black" class="btn btn-info btn-xs">有文件</a>');
                if (data.published) {
                    $('#InformationDs_published').attr('checked', true);
                }
                if (data.is_home) {
                    $('#InformationDs_is_home').attr('checked', true);
                }

                /* $('#Staff_bankcard_cn').val("有文件");
                 $('#Staff_bankcard_en').val("有文件");*/
            }
        });
    }


    function cbInformation() {
        $('#myModal').modal('hide');
        location.reload();
        // $.fn.yiiGridView.update('informaction');
    }

    function emptyContent() {
        $('#InformationDs_times').empty().attr("value", "");
        $('#InformationDs_cagegory').val("");
        $('#InformationDs_title_cn').attr("value", "");
        $('#InformationDs_title_en').attr("value", "");
        $('#Staff_bankcard_cn').attr("value", "");
        $('#Staff_bankcard_en').attr("value", "");
        $('#inffile_cn').empty();
        $('#inffile_en').empty();
        $('#InformationDs_published').attr('checked', false);
    }

</script>


