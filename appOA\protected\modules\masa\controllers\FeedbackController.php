<?php

class FeedbackController extends BranchBasedController
{
    public $childsName = array();
    public $parentsName = array();
    public function createUrl($route, $params = array(), $ampersand = '&')
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        return parent::createUrl($route, $params, $ampersand);
    }

    public function init(){
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'campusOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site','Campus Workspace');

        $this->branchSelectParams['urlArray'] = array('//masa/feedback/index');

        // jquery ui
        $cs = Yii::app()->clientScript;
        $cs->registerCoreScript('jquery.ui');
        $cs->registerCssFile($cs->getCoreScriptUrl().'/jui/css/base/jquery-ui.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/vue2.js');
    }

	public function actionIndex()
    {
        $critwith = new CDbCriteria;
        $critwith->compare('schoolid', $this->branchId);

        $feedback = new CActiveDataProvider('AsaFeedback', array(
            'criteria' => $critwith,
            'sort' => array(
                'defaultOrder' => 'updated DESC',
            ),
            'pagination'=>array(
                'pageSize'=>20,
            ),
        ));

        $childId = array();
        $parent = array();
        foreach($feedback->getData() as $child){
            $childId[$child->childid] = $child->childid;
            $parent[$child->updated_by] = $child->updated_by;
        }

        if($childId){
            $childModel = ChildProfileBasic::model()->findAllByPk($childId);

            foreach($childModel as $child){
                $this->childsName[$child->childid] = $child->getChildName();
            }
        }

        if($parent){
            $parentModel = User::model()->findAllByPk($parent);
            foreach($parentModel as $parent){
                $this->parentsName[$parent->uid] = $parent->getName();
            }
        }

        $this->render('index',array("feedback" => $feedback));
	}

    public function actionDemand()
    {
        $crits = new CDbCriteria;
        $crits->compare('site_id', $this->branchId);
        $courseDemand = new CActiveDataProvider('AsaCourseDemand', array(
            'criteria' => $crits,
            'sort' => array(
                'defaultOrder' => 'updated DESC',
            ),
            'pagination'=>array(
                'pageSize'=>20,
            ),
        ));

        $childId = array();
        $parent = array();
        foreach($courseDemand->getData() as $child){
            $childId[$child->childid] = $child->childid;
            $parent[$child->parent_id] = $child->parent_id;
        }

        if($childId){
            $childModel = ChildProfileBasic::model()->findAllByPk($childId);

            foreach($childModel as $child){
                $this->childsName[$child->childid] = $child->getChildName();
            }
        }

        if($parent){
            $parentModel = User::model()->findAllByPk($parent);
            foreach($parentModel as $parent){
                $this->parentsName[$parent->uid] = $parent->getName();
            }
        }

        $this->render('demand',array("courseDemand" => $courseDemand));
    }

    public function AttendMenu()
    {
        $mainMenu = array(
            array('label'=>Yii::t('','反馈与建议'), 'url'=>array("/masa/feedback/index")),
            array('label'=>Yii::t('','开课需求'), 'url'=>array("/masa/feedback/demand")),
        );
        return $mainMenu;
    }

    public function actionDemandMemo()
    {
        $cashHandoverId = Yii::app()->request->getParam('id','');
        if($cashHandoverId){
            $model = AsaCourseDemand::model()->findByPk($cashHandoverId);
            $childModel = ChildProfileBasic::model()->findByPk($model->childid);
            $this->renderPartial('memo', array(
                'model' => $model,
                'childName' => $childModel->getChildName(),
            ));
        }
    }


    public function getButton($data)
    {
        echo CHtml::link('查看推荐原因', array('demandMemo', 'id' => $data->id, "branchId"=>Yii::app()->controller->branchId), array('class' => 'J_modal_s btn btn-xs btn-info')) . ' ';

    }

    public function getChildName($data)
    {
        return $this->childsName[$data->childid];
    }

    public function getParentName($data)
    {
        return $this->parentsName[$data->updated_by];
    }

    public function getParnetNameDemand($data)
    {
        return $this->parentsName[$data->parent_id];
    }

    public function getType($data)
    {
        $type = array(
            '1' => '课程种类',
            '2' => '服务态度',
            '3' => '教师质量',
            '4' => '管理水平',
            '5' => '微信端功能',
            '6' => '其他',
        );
        return $type[$data->type];
    }

}