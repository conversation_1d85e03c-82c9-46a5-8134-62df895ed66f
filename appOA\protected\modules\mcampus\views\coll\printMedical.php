<?php
$medicalCn = '';
$medicalEn = '';
$filedata = '';
$data = '';
if($model){
    $data = json_decode($model->data);
    $filedata = ($data->filedata) ? $data->filedata : '' ;
}
?>
<style>
.container-fluid{
    width:860px;
    margin:0 auto
}
.page{
    margin-top:20px
}
img{
    margin-top:10px;
    max-width:100%;
    height:auto
}
.sign{
    float: right;
    margin-right:20px;
    margin-bottom:20px
}
.sign img{
    width:200px;
    height: 110px;
}
@media print {
    .print_img{
        max-height: 1223px;
        display: block;
        margin: 0 auto;
    }
    .print_protocol_img
    {
        max-height: 1130px;
        display: block;
        margin: 0 auto;
    }
}
</style>
<div class="container-fluid">
    <div class ="row">
        <table class="table">
            <tbody>
            <tr>
                <th  width="25%"><?php echo Yii::t('reg', 'Name') ?></th>
                <td colspan="3"><?php echo $child_info->getChildName() ?></td>
            </tr>
            <tr>
                <th><?php echo Yii::t('labels', 'Date of Birth') ?></th>
                <td colspan="3"><?php echo $child_info->birthday_search ?></td>
            </tr>
            <tr>
                <th><?php echo Yii::t('labels', 'Class') ?></th>
                <td colspan="3"><?php echo $child_info->ivyclass->title?></td>
            </tr>
            <?php foreach ($step_data['intended_hospitals'] as $k=>$v){?>
                <tr>
                    <th><?php echo Yii::t('reg', 'Preferred Hospital') ?> <?php echo $k+1?></th>
                    <td colspan="3"><?php echo $v?></td>
                </tr>
            <?php }?>
            <tr>
                <th><?php echo Yii::t('reg', 'Personal Medical Insurance Information') ?></th>
                <td colspan="3"><?php echo $step_data['insurance_companies']?></td>
            </tr>
            <?php foreach ($step_data['emergency_contact'] as $k=>$v){?>
                <tr>
                    <th><?php echo Yii::t('site', 'Emergency Contact') ?> <?php echo $k+1?></th>
                    <td colspan="3"><?php echo $v['name']?></td>
                </tr>
                <?php if(!empty($v['mobile'])){?>
                    <tr>
                        <th><?php echo Yii::t('user', 'Emergency Contact Phone') ?> <?php echo $k+1?></th>
                        <td colspan="3"><?php echo $v['mobile']?></td>
                    </tr>
                <?php }?>

                <?php if(!empty($v['email'])){?>
                    <tr>
                        <th><?php echo Yii::t('user', 'Emergency Contact Email') ?> <?php echo $k+1?></th>
                        <td colspan="3"><?php echo $v['email']?></td>
                    </tr>
                <?php }?>

            <?php }?>
            <tr>
                <th><?php echo Yii::t('reg', 'ADD/ADHD') ?></th>
                <td><?php echo $step_data['is_adhd']==1 ?  "是":  "否" ?></td>
                <th width="20%"><?php echo Yii::t('reg', 'Heart Disorder') ?></th>
                <td><?php echo $step_data['is_heart_disease']==1 ?  "是":  "否" ?></td>
            </tr>
            <tr>
                <th><?php echo Yii::t('reg', 'Are there tuberculosis patients among the family members, relatives and friends who are often in contact with the immediate family over the last 2 years') ?></th>
                <td><?php echo $step_data['tuberculosisFour']==1 ? "是":  "否"?></td>
                <th width="20%"></th>
                <td></td>
            </tr>
            <tr>
                <th><?php echo Yii::t('reg', 'Allergies(food,medications,etc.)') ?> </th>
                <td><?php echo $step_data['allergies']==1 ? "是":  "否"?></td>
<!--                allergies_other  allergies_food allergies_medication-->
                <th width="20%"><?php echo Yii::t('reg', 'Allergy') ?></th>
                <?php if($step_data['allergies']==1){?>
                    <td><?php echo $step_data['allergies_food'] .PHP_EOL. $step_data['allergies_medication'].PHP_EOL.$step_data['allergies_other']?></td>
                <?php }else{?>
                    <td>无</td>
                <?php }?>
            </tr>
            <tr>
                <th> <?php echo Yii::t('reg', 'Other Illnesses (please specify)') ?></th>
                <td colspan="3"><?php echo $step_data['other']?></td>
            </tr>
            </tbody>
        </table>
    </div>
    <div style="page-break-after:always;"></div>
    <!--    协议-->
    <div class="row" style="">
        <?php if(isset($step_data['extraInfo']['protocol']) && !empty($step_data['extraInfo']['protocol'])){?>
            <?php foreach ($step_data['extraInfo']['protocol'] as $k=>$v){?>
                <img class="img-responsive print_protocol_img" src="<?php echo $v; ?>">
            <?php }?>
        <?php }?>
        <div class="sign">
            <img src="<?php echo $step_data['childStatus']['sign_url']?>" alt="">
        </div>
    </div>
    <div class="row">
            <!--        体检报告-->
            <?php if($step_data['examination_reports']){?>
                <?php foreach ($step_data['examination_reports'] as $val){?>
                    <p class="pageBreak"><img style="width: 90%" class="img-responsive print_img" src="<?php echo $val; ?>"></p>
                <?php }?>
            <?php }?>
            <!--        疫苗-->
            <?php
            if($step_data['vaccines_reports']){
                foreach ($step_data['vaccines_reports'] as $val){
                    ?>
                    <p class="pageBreak"><img style="width: 90%" class="img-responsive print_img" src="<?php echo $val; ?>"></p>
                <?php   }
            }
            ?>
            <!--        保险-->
            <?php
            if($step_data['insurance_card_photos']){
                foreach ($step_data['insurance_card_photos'] as $val){
                    ?>
                    <p class="pageBreak"><img style="width: 90%" class="img-responsive print_img" src="<?php echo $val; ?>"></p>
                <?php   }
            }
            ?>
        </table>
    </div>
</div>