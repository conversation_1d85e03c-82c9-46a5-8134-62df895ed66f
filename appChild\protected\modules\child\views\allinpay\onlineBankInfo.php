<?php
if (true === $allinpayAvailable) {

    $usedBefore = count($usedBank) ? true : false;
    ?>
<link rel="stylesheet" type="text/css"
      href="<?php echo Yii::app()->theme->baseUrl . '/css/allinpay.css?t=' . Yii::app()->params['refreshAssets']; ?>"/>
<div id="usedbankinfo" class="bankinfo">
    <?php
    //常用帮助按钮
    // $faq = sprintf('<span class="faq"><em></em><span>%s</span></span>', Yii::t('payment', 'Online Banking FAQ'));
    echo CHtml::openTag("div", array("class" => "alignRight fr"));
    echo CHtml::link($faq, array("//child/allinpay/onlineNotice"), array("class" => "colorbox cboxElement"));
    echo CHtml::closeTag("div");


    Yii::import('application.components.CGroupRadioButtonList');

    echo CHtml::openTag("div", array("class"=>"flash-info2"));
    echo Yii::t("payment", "Any invoice paid through System's Banking will automatically earn Reward Points (1 RMB=1 point).");
    echo CHtml::closeTag("div");
    
    echo CHtml::openTag("h2");
    echo Yii::t("payment", "Bank List");
    echo CHtml::closeTag("h2");
    if ($usedBefore) {
        $currentBank = each($usedBank);
        $selected = $currentBank['key'];
        echo CHtml::openTag("h3");
        echo Yii::t("payment", 'Favorite');
        echo CHtml::closeTag("h3");
        echo CHtml::openTag("div", array("class" => "bank-list"));
        echo MCHtml::groupRadioButtonList(
            "allinBankId", '', $usedBank, 0, array(
                "container" => "ul",
                "separator" => "",
                "template" => "<li class='bank'>{input}{label}</li>",
                'labelOptions' => array(
                    'class' => '{value}'
                ),
            )
        );
        echo CHtml::openTag("div", array("class" => "clear"));
        echo CHtml::closeTag("div");
        echo CHtml::closeTag("div");
    } else {
        $currentBank = each($otherBankList);
        $selected = $currentBank['key'];
    }

    if ($usedBefore) {
        echo CHtml::openTag("h3");
        echo Yii::t("payment", 'Other Banks');
        echo CHtml::closeTag("h3");
    } else {
        echo CHtml::openTag("p", array("class" => "flash-info"));
        echo Yii::t("payment", 'Please make the online payment via a bank where the online banking has been enabled.');
        echo CHtml::closeTag("p");
    }

    echo CHtml::openTag("div", array("class" => "bank-list"));

    echo MCHtml::groupRadioButtonList(
        "allinBankId", '', $otherBankList, count($usedBank), array(
            "container" => "ul",
            "separator" => "",
            "template" => "<li class='bank'>{input}{label}</li>",
            'labelOptions' => array(
                'class' => '{value}'
            ),
        )
    );
    echo CHtml::openTag("div", array("class" => "clear"));
    echo CHtml::closeTag("div");
    echo CHtml::closeTag("div");
    $payNotice1 = Yii::t('payment', 'Please select at least one invoice.');
    $payNotice2 = Yii::t('payment', 'Please select a bank');
    $payNotice3 = Yii::t("payment", 'Please select up to 6 invoices each time to proceed the payment.');
    $payNotice4 = Yii::t("payment", '请选择储蓄卡或是信用卡');
    ?>
    <hr>
    <div class="clearfix"></div>
    <div></div>
    <div id="operateCol" class="operateCol_div" style="display: none">
        <ul>
            <li><span class="btn001"><a id="confirmPay" href="#"
                                        onclick="confirmInvoiceInfo('allinpay','<?php echo $payNotice1; ?>','<?php echo $payNotice2; ?>','<?php echo $payNotice3; ?>','<?php echo $payNotice4; ?>');return false"
                                        class="operatecol"><span><?php echo Yii::t("payment", 'Make Payment'); ?>
                            </span> </a></span>
            </li>
        </ul>
    </div>
    <div class="none-ie-warning flash-warning" style="display: none">
        <p>
            <?php echo Yii::t("payment", 'Most online banking sites do not support this browser. Please use IE explorer.'); ?>
        </p>

        <p>
            <?php echo CHtml::link(Yii::t("payment", 'Thank you for the reminder. I wish to proceed to make the payment using this browser.'), 'javascript:;', array('onclick' => "confirmInvoiceInfo('allinpay','" . $payNotice1 . "','" . $payNotice2 . "','" . $payNotice3 . "','" . $payNotice4 . "');return false")); ?>
        </p>
    </div>


</div>


<script type="text/javascript">
    function isIE() {
        if (!!window.ActiveXObject || "ActiveXObject" in window)
            return true;
        else
            return false;
    }
    $(document).ready(function () {

        if (!isIE()){
            $("#tabAllinpay .none-ie-warning").show();
            $("#tabAllinpay .operateCol_div").remove();
        } else {
            $("#tabAllinpay .operateCol_div").show();
        }

        //各个银行的点击事件
        $(".bankinfo input[type=radio]").click(function(){
            $(".bankinfo label").removeClass("current");
            $(".bankinfo label[for=" + $(this).attr('id') + "]").addClass("current");
            var bankId = $(this).val();

            $.get("<?php echo $this->createUrl('payment/getBankInfo'); ?>",
            {
              bankId:bankId
            },
            function(data,status){
                if (status == 'success') {
                    $('.clearfix').next().remove();
                    $('.clearfix').after(data);
                }
            });
        });
    });

</script>

<?php
} else {

    echo '<div class="flash-info">';
    echo Yii::t("payment", 'Sorry, online payment is not available for this campus.');
    echo '</div>';
}
?>