<?php
$busCn = '';
$busEn = '';
$needBus = 0;
if($configModel){
    $baseContentCn = ($configModel->content_cn) ? json_decode($configModel->content_cn) : '';
    $busCn = ($baseContentCn) ? nl2br(base64_decode($baseContentCn->bus)) : '';

    $baseContentCn = ($configModel->content_en) ? json_decode($configModel->content_en) : '';
    $busEn = ($baseContentCn) ? nl2br(base64_decode($baseContentCn->bus)) : '';
}

if($model){
    $data = json_decode($model->data);
    $needBus = ($data->needBus) ? $data->needBus : 0 ;
}



?>
<style>
.bus{
	width:950px;
	margin:0 auto;
}
.sign{
    float: right;
    margin-right:20px;
    margin-bottom:20px
}
.sign img{
    width:100px;
}
</style>
<div class="container-fluid">
	<div class="row">
        <?php if($needBus){ ?>
       		<div class="bus">
	            <div><?php echo $busCn; ?></div>
	            <div><?php echo $busEn; ?></div>
	            <div class="sign"><img src="<?php echo RegExtraInfo::getOssImageUrl($data->filedata) ?>" alt=""></div>
            </div>
        <?php } ?>
    </div>
</div>