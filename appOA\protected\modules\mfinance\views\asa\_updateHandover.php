<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span>
    </button>
    <h4 class="modal-title" id="exampleModalLabel"><?php echo $model->id ? '更新：' : '添加：'; ?></h4>
</div>
<?php $form = $this->beginWidget('CActiveForm', array(
    'id' => 'course-forms',
    'enableAjaxValidation' => false,
    'action' => $this->createUrl('updateHandover', array('id' => $model->id)),
    'htmlOptions' => array('class' => 'J_ajaxForm form-horizontal', 'role' => 'form'),
)); ?>
<div class="modal-body">

        <div class="page-header">
            <h4>
            <label class="col-xs-2 text-info text-right">现金交接信息：</label>
                <div style="clear: both"></div></h4>
        </div>
    <div class="form-group">
        <label class="col-xs-2 text-right"><?php echo $form->labelEx($model, 'amount'); ?></label>

        <div class="col-xs-8">
            <div><?php echo $model->amount ?></div>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 text-right"><?php echo $form->labelEx($model, 'bank_ref'); ?></label>

        <div class="col-xs-8">
            <div><?php echo $model->bank_ref ?></div>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 text-right"><?php echo $form->labelEx($model, 'bank_deposit_time'); ?></label>

        <div class="col-xs-8">
            <div><?php echo date("Y-m-d", $model->bank_deposit_time) ?></div>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 text-right"><?php echo $form->labelEx($model, 'bank_ref_url'); ?></label>

        <div class="col-xs-8">
            <?php if($model->bank_ref_url){ ?>
                <a href="<?php echo $this->createUrl('downloadsHand',array('id' => $model->id));?>" target="_blank" class="btn btn-info btn-xs"><span class="glyphicon glyphicon-file"></span> 点击查看附件</a>
            <?php }?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 text-right"><?php echo $form->labelEx($model, 'creater_memo'); ?></label>

        <div class="col-xs-8">
            <?php echo $model->creater_memo ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 text-right"><?php echo $form->labelEx($model, 'creater'); ?></label>

        <div class="col-xs-8">
            <div><?php echo $uidName->getName() ?></div>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 text-right"><?php echo Yii::t('asa','账单明细'); ?></label>
        <div class="col-xs-8">
            <table class="table table-bordered">
                <tr>
                    <td><?php echo Yii::t('asa','账单名字'); ?></td>
                    <td><?php echo Yii::t('asa','账单金额'); ?></td>
                    <td><?php echo Yii::t('asa','付款时间'); ?></td>
                </tr>
                <?php foreach($invoices as $_invoice): ?>
                    <tr>
                        <td><?php echo $_invoice->title ?></td>
                        <td><?php echo $_invoice->amount_actual ?></td>
                        <td><?php echo date("Y-m-d", $_invoice->updated) ?></td>
                    </tr>
                <?php endforeach; ?>
            </table>
        </div>
    </div>

    <div class="page-header">
        <h4>
            <label class="col-xs-2 text-info text-right">审核结果：</label>
            <div style="clear: both"></div></h4>
    </div>
    <div class="form-group">
        <label class="col-xs-2 text-right"><?php echo $form->labelEx($model, 'status'); ?></label>

        <div class="col-xs-8">
            <?php echo $form->radioButtonList($model, 'status', array('2' => '确定款项到账,完成交接', '99' => '信息不正确,返回校园处理'), array('labelOptions' => array('class' => 'labelForRadio'))); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 text-right"><?php echo $form->labelEx($model, 'received_memo'); ?></label>

        <div class="col-xs-8">
            <?php echo $form->textArea($model, 'receiver_memo', array('class' => 'form-control')); ?>
        </div>
    </div>


</div>
<div class="modal-footer">
    <button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global', 'Submit'); ?></button>
    <button type="button" class="btn btn-default"
    data-dismiss="modal"><?php echo Yii::t('global', 'Cancel'); ?></button>
</div>
<?php $this->endWidget(); ?>
