<?php
 
class Dit extends CWidget {
 
    public $bId; // 需要固定的元素ID
    public $cId; // 固定在这个元素ID之内

    public function init() {
        parent::init();
    }
 
    /**
     * Run the widget, including the js files.
     */
    public function run() {
        $cs = Yii::app()->clientScript;
 
        $dir = dirname(__FILE__) . DIRECTORY_SEPARATOR;
        $baseUrl = Yii::app()->getAssetManager()->publish($dir . 'assets', false, -1, YII_DEBUG);
 
        $clientScript = Yii::app()->getClientScript();
 
        $clientScript->registerScriptFile($baseUrl . '/google.dit.js?t='.Yii::app()->params['refreshAssets']);
 
        $js =  "<script>DIT_floatMetadata('".$this->bId."', '".$this->cId."');</script>";
 
        echo $js;
    }
 
}
?>