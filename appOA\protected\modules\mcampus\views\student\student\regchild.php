<?php
$clabels = ChildProfileBasic::model()->attributeLabels();
foreach($branches as $_key=>$_branch){
    if($_branch['type']!=10)
        $branchData[$_key] = $_branch['title'];
}
$langModels = Term::model()->lang()->findAll();
?>
<div id="regform-new-template" style="display: none;">
    <div class="register">
    <div class="row">
        <form action="<?php echo $this->createUrl('register')?>" method="post" class="form-horizontal J_ajaxForm" role="form">
            <?php echo CHtml::hiddenField('ChildProfileBasic[schoolid]',$this->branchId) ?>
            <div class="col-md-4">
                <div class="jumbotron">
                    <h1>新家庭首次注册</h1>
                    <p>请与家长确认该家庭以前没有注册过学生（包括其他的校园）</p>
                </div>
            </div>
            <div class="col-md-8" id="fill-form">
                <div class="col-md-6"><?php $this->renderPartial('student/_childform', array('clabels'=>$clabels,'branches'=>$branches, 'langModels'=>$langModels));?></div>
                <div class="col-md-6">

                    <div class="form-group" model-attribute="fatherName">
                        <?php echo CHtml::label($clabels['fatherName'], CHtml::getIdByName('fatherName'), array('class'=>'col-sm-3 control-label'));?>
                        <div class="col-sm-9">
                            <?php echo CHtml::textField('fatherName','',array('placeholder'=>$clabels['fatherName'], 'class'=>'form-control'));?>
                        </div>
                    </div>

                    <div class="form-group" model-attribute="fatherEmail">
                        <?php echo CHtml::label($clabels['fatherEmail'], CHtml::getIdByName('fatherEmail'), array('class'=>'col-sm-3 control-label'));?>
                        <div class="col-sm-9">
                            <?php echo CHtml::textField('fatherEmail','',array('placeholder'=>$clabels['fatherEmail'], 'class'=>'form-control'));?>
                        </div>
                    </div>

                    <div class="form-group" model-attribute="fatherMobile">
                        <?php echo CHtml::label($clabels['fatherMobile'], CHtml::getIdByName('fatherMobile'), array('class'=>'col-sm-3 control-label'));?>
                        <div class="col-sm-9">
                            <?php echo CHtml::textField('fatherMobile','',array('placeholder'=>$clabels['fatherMobile'], 'class'=>'form-control length_3'));?>
                        </div>
                    </div>

                    <div class="form-group" model-attribute="motherName">
                        <?php echo CHtml::label($clabels['motherName'], CHtml::getIdByName('motherName'), array('class'=>'col-sm-3 control-label'));?>
                        <div class="col-sm-9">
                            <?php echo CHtml::textField('motherName','',array('placeholder'=>$clabels['motherName'], 'class'=>'form-control', 'onchange'=>'eChange(this)'));?>
                        </div>
                    </div>

                    <div class="form-group" model-attribute="motherEmail">
                        <?php echo CHtml::label($clabels['motherEmail'], CHtml::getIdByName('motherEmail'), array('class'=>'col-sm-3 control-label'));?>
                        <div class="col-sm-9">
                            <?php echo CHtml::textField('motherEmail','',array('placeholder'=>$clabels['motherEmail'], 'class'=>'form-control', 'onchange'=>'eChange(this)'));?>
                        </div>
                    </div>

                    <div class="form-group" model-attribute="motherMobile">
                        <?php echo CHtml::label($clabels['motherMobile'], CHtml::getIdByName('motherMobile'), array('class'=>'col-sm-3 control-label'));?>
                        <div class="col-sm-9">
                            <?php echo CHtml::textField('motherMobile','',array('placeholder'=>$clabels['motherMobile'], 'class'=>'form-control length_3'));?>
                        </div>
                    </div>

                    <?php if($this->branchObj->type != 50):?>
                    <div class="form-group">
                        <div class="col-sm-3"></div>
                        <div class="col-sm-9">
                            <?php echo CHtml::checkBox('sendmail',true,array('value'=>1));?>
                            是否发送家长帐号信息（如果只是课外活动、夏令营、亲子班等请取消发送）
                        </div>
                    </div>
                    <?php endif;?>

                    <div class="form-group" model-attribute="motherMobile">
                        <div class="col-sm-3"></div>
                        <div class="col-sm-9">
                            <button type="button" class="btn btn-primary btn-lg J_ajax_submit_btn"><?php echo Yii::t('global','Save');?></button>
                        </div>
                    </div>

                </div>
            </div>

        </form>
    </div>
    </div>
</div>


<div id="regform-sibling-template" style="display: none;">
    <div class="register">
        <div class="row">
            <form action="<?php echo $this->createUrl('register')?>" method="post" class="form-horizontal J_ajaxForm" role="form">
                <?php echo CHtml::hiddenField('ChildProfileBasic[schoolid]',$this->branchId) ?>
                <div class="col-md-4">
                    <div class="jumbotron">
                        <h1>注册兄弟姐妹</h1>
                        <p>该家庭曾经注册过孩子，包括其他的校园。父母信息不用重复录入。</p>
                    </div>
                </div>
                <div class="col-md-8" id="fill-form">
                    <div class="col-md-6">
                        <div class="form-group" model-attribute="name_cn">
                            <div class="col-sm-12 mb5">
                                <?php echo CHtml::label('查询注册过的信息，可通过孩子姓名，家长手机号，电子邮件查询', 'siblingSearch', array('class'=>'control-label'));?>
                            </div>
                            <div class="col-sm-12">
                                <div class="form-inline">
                                    <?php
                                    echo CHtml::dropDownList('SiblingSearch[targetBranchId]', $this->branchId, $branchData,
                                        array('class'=>"col-sm-4 form-control",'onchange'=>'SSCampusChange(this)'));
                                    ?>
                                    <div class="input-group">
                                        <?php echo CHtml::textField('siblingSearch[queryText]', '', array('class'=>'form-control'))?>
                                        <span class="input-group-addon"><a href="#" onclick="showSearch()"><span class="glyphicon glyphicon-search"></span></a></span>
                                    </div>
                                </div>
                                <div id="searchAlert" class="alert alert-warning" role="alert" style="display: none;">
                                    为保护用户隐私，<strong>跨校</strong>查询时请尽量精确条件，最多返回5条结果
                                </div>
                                <div id="search-box" class="panel panel-default" style="display: none">
                                    <div class="panel-heading">查询结果</div>
                                    <div class="panel-body">
                                        <!--place holder-->
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div id="regform-content" style="display: none;">
                            <?php $this->renderPartial('student/_childform', array('clabels'=>$clabels,'branches'=>$branches, 'langModels'=>$langModels));?>

                            <div class="form-group" model-attribute="motherMobile">
                                <div class="col-sm-3"> </div>
                                <div class="col-sm-9">
                                    <button type="button" class="btn btn-primary btn-lg J_ajax_submit_btn"><?php echo Yii::t('global','Save');?></button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">

                </div>
            </form>
        </div>
    </div>
</div>

<style>
    #sibling-list{max-height: 600px; overflow-y: auto}
</style>


<script type="text/template" id="sibling-item-template">
    <div class="row">
        <div class="col-sm-2">
            <a href="<?php echo Yii::app()->createUrl('/child/index/index');?>?childid=<%= childid%>" target="_blank"><img width="60" class="thumbnail" src="<% print(childPhotoBaseUrl); %><%= photo %>"/></a>
        </div>
        <div class="col-sm-10">
            <h3><%= name %> <small><%= gender %> <%= dob %></small></h3>
        </div>
    </div>
</script>

<script type="text/template" id="success-template">
    <div class="jumbotron">
        <h1><%= name%> 添加成功！</h1>
        <p>
            <a href="/child/index/index?childid=<%= childid%>" class="btn btn-default btn-xs" target="_blank">孩子基本信息</a>
            <a href="javascript:;" class="btn btn-default btn-xs" onclick="regristerNew();">继续添加新孩子</a>
        </p>
    </div>
</script>