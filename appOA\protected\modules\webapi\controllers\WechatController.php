<?php
class WechatController extends Controller{
    public function __construct($id, $module = null) {
        parent::__construct($id, $module);
        $token = Yii::app()->request->getParam('token','');
        if ($token != md5(CommonUtils::SECURITY_KEY)){
            $this->module->_sendResponse(403);
        }
    }
    
    /**
     * 获取微信服务号AccessToken
     */
    public function actionGetAccessToken()
    {
        $wechatKey = Yii::app()->request->getParam('wechatKey','');
        $authKey = Yii::app()->request->getParam('authKey','');

        if($authKey != md5($wechatKey.CommonUtils::SECURITY_KEY)){
            $this->module->_sendResponse(403);
        }

        $config = $this->getConfig();
        if($wechatKey && in_array($wechatKey, array_keys($config))){
            $config = $config[$wechatKey];
            $cacheKey = 'wechat-token-'.$wechatKey;
            $accessToken = Yii::app()->cache->get($cacheKey);

            if(!$accessToken){
                $appId = $config['appId'];
                $appSecret = $config['appSecret'];
                if (isset($config['corp']) && $config['corp']) {
                    $url = "https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid=$appId&corpsecret=$appSecret";
                }
                else {
                    $url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=$appId&secret=$appSecret";
                }
                $res = json_decode($this->module->_sendHttp($url));
                if($res->access_token) {
                    $accessToken = $res->access_token;
//                    $expire = $res->expires_in - 200;
                    $expire = 7000;
                    Yii::app()->cache->set($cacheKey, $accessToken, $expire);
                }
                Yii::import('common.models.wechat.WechatToken');
                $model = new WechatToken();
                $model->wechat_key = $wechatKey;
                $model->appid = $appId;
                $model->token = isset($res->access_token) ? $res->access_token : '';
                $model->expires_in = isset($res->expires_in) ? $res->expires_in : 0;
                $model->response = isset($res->access_token) ? '' : json_encode($res);
                $model->updated_at = time();
                $model->save();
                $returnExpire = isset($res->expires_in) ? $res->expires_in : 0;
                if ($returnExpire != 7200) {
                    $address = '<EMAIL>';
                    $subject = $wechatKey . ': token expires is ' . $returnExpire;
                    $httpBody = json_encode($res);
                    $data = array(
                        'setTo' => $address,
                        'subject' => $subject,
                        'body' => $httpBody,
                        'template' => '',
                    );
                    Yii::import('common.components.AliYun.MQ.MQProducer');
                    CommonUtils::addProducer(MQProducer::TAG_WX_WORK, "Mail.sendTask",  CJSON::encode($data));
                }
            }

            $this->module->_sendResponse(200, CJSON::encode(array('accessToken' => $accessToken)));
        }
        $this->module->_sendResponse(500);
    }

    public function actionDelToken()
    {
        $wechatKey = Yii::app()->request->getParam('wechatKey','');
        $authKey = Yii::app()->request->getParam('authKey','');

        if($authKey != md5($wechatKey.CommonUtils::SECURITY_KEY)){
            $this->module->_sendResponse(403);
        }

        $config = $this->getConfig();
        if($wechatKey && in_array($wechatKey, array_keys($config))){
            $cacheKey = 'wechat-token-'.$wechatKey;
            $isDeleted = 0;
            if (Yii::app()->cache->delete($cacheKey)) {
                $isDeleted = 1;
            }
            $this->module->_sendResponse(200, CJSON::encode(array('isDeleted' => $isDeleted)));
        }
        $this->module->_sendResponse(500);
    }

    private function getConfig()
    {
        $config = array(
            'ivy' => array(
                'appId' => 'wx903fba9d4709cf10',
                'appSecret' => '388486edf736f1c078c808f291ce0075',
            ),
            'ds' => array(
                'appId' => 'wxb1a42b81111e29f3',
                'appSecret' => '7fec437b1b5cbbb64b32e170e6857a7f',
            ),
            'ivy_messenger' => array(
                'appId' => 'wxe929eea87aaf012f',
                'appSecret' => 'a3e576445712e57f9a7d7eb70f3b9140',
            ),
            'mmx' => array(
                'appId' => 'wxb9c6c538ebf7efa2',
                'appSecret' => 'e2c9baeb99cb91bc14033f45bf4b244c',
            ),
            'mmx_app' => array(
                'appId' => 'wx3690b00fdbbb3d53',
                'appSecret' => '86826a5e17fb4f91adccad73ad1c91e8',
            ),
            'ivyqiye' => array(
                'appId' => 'wx4d022dc5e7d095ac',
                'appSecret' => 'nSi033WEhE4frq83zf-G2L8mkGybhsHUaXKnJiHVb21-ITO2z57RWOXa9sQ7W6TK',
                'corp' => true,
            ),
            'ivy_namelist' => array(
                'appId' => 'ww11b11e149511490a',
                'appSecret' => 'wCd_19ZJX3g8GwZeQqqjxes06AykdRLIO7FuTt4jhfY',
                'corp' => true,
            ),
            'mmxqiye' => array(
                'appId' => 'ww669e012cc918be3f',
//                'appSecret' => 'Dz7Ldee3Fo1Sh5kciNluq231-tkrHh3rtxE-AiwNMKA',
                'appSecret' => 'GjvVMU6-UJggV-VBBi0MgWKfqS-LgBQItHFztqQ5Q9w',
                'corp' => true,
            ),
            'mmx_namelist' => array(
                'appId' => 'ww669e012cc918be3f',
                'appSecret' => '_xetdpn6tWA5BBQaSm8GTD8m6z3Ze5grw8_OQBxTCT8',
                'corp' => true,
            ),
            'ds_admissions' => array(
                'appId' => 'wx2c9067a8e72a79c4',
                'appSecret' => '91e3542d1927a4e01571dccb8569e99d',
            ),
            'ivy_admissions' => array(
                'appId' => 'wx60ad1b4f4a779ea6',
                'appSecret' => 'e407fff511112c227df6238cdf19acdc',
            ),
            'workwx_approval' => array(
                'appId' => 'ww11b11e149511490a',
                'appSecret' => 'h5HfNZSiET8FFyZACPB3rqW6aqsC51oVENln_ll7nHg',
                'corp' => true,
            ),
            'workflow_backend' => array(
                'appId' => 'ww11b11e149511490a',
                'appSecret' => 'TX3_ac-Q9zMudjWNL-4UXMeuZZcEHJI1J2Gqy1lS800',
                'corp' => true,
            ),
            'workwx_esign' => array(
                'appId' => 'ww11b11e149511490a',
                'appSecret' => 'jC_XQmnz3Zw34S73OwifqfZH8-oj1CqRkDj5e-3_lQU',
                'corp' => true,
            ),
            'workwx_upload' => array(
                'appId' => 'ww11b11e149511490a',
                'appSecret' => 'xRX4-OAObgdXbYd3qLHoYlhJDpjx7uQ4hTtGSo93ke0',
                'corp' => true,
            ),
            'workwx_notice' => array(
                'appId' => 'ww11b11e149511490a',
                'appSecret' => 'WIYpuK7yArzKJAoHnd25Qa30KbT4FAyz61Pg-hS036o',
                'corp' => true,
            ),
        );

        return $config;
    }

    public function actionGetSchoolID($school='')
    {
        $payIDs = CommonUtils::LoadConfig('CfgYeePayGlobal');
        $prefix = '33';

        if ($school) {
            $ret = intval($prefix.$payIDs[$school]['number_code']);
        }
        else {
            foreach ($payIDs as $skey=>$sval) {
                $ret[$skey] = intval($prefix.$sval['number_code']);
            }
        }
        $ret['BJ_BU'] = 33990;
        $ret['BJ_SS'] = 33991;

        $this->module->_sendResponse(200, CJSON::encode($ret));
    }

    public function actionTest()
    {
        $res = $this->module->_sendHttp('https://www.baidu.com');
        var_dump($res);
    }

    public function actionGetSession()
    {
        $sess_id = Yii::app()->request->getParam('sess_id','');
        $authKey = Yii::app()->request->getParam('authKey','');

        if($authKey != md5($sess_id.CommonUtils::SECURITY_KEY)){
            $this->module->_sendResponse(403);
        }

        $result = array();
        if ($sess_id){
            $sql = "select * from ivy_session where sess_id='".$sess_id."'";
            $result = Yii::app()->db->createCommand($sql)->queryRow();
        }
        $this->module->_sendResponse(200, CJSON::encode($result));
    }

    public function actionGetUsers()
    {
        $userids = Yii::app()->request->getParam('userids','');
        $authKey = Yii::app()->request->getParam('authKey','');

        if($authKey != md5($userids.CommonUtils::SECURITY_KEY)){
            $this->module->_sendResponse(403);
        }

        $result = array();
        if ($userids){
            $ids = explode(',', $userids);
            $items = User::model()->findAllByPk($ids);
            foreach ($items as $item) {
                $result[$item->uid] = array(
                    'name' => $item->getName(),
                    'photo' => $item->getPhotoSubUrlforOA(false),
                );
            }
        }
        $this->module->_sendResponse(200, CJSON::encode($result));
    }
}
