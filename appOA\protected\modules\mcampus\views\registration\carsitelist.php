<style>
	[v-cloak] {
		display: none;
	}
	#mapExample{
		height: 500px
	}
	#log-form{
		display: inline-block;
	}
	.text-left{
		text-align:left !important;
	}
	.maxHeight{
		max-height:500px;
		overflow-y: scroll;
	}
</style>
<div class="container-fluid">
	<ol class="breadcrumb">
		<li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('//mcampus/default/index')) ?></li>
		<li><?php echo CHtml::link(Yii::t('site', '新生注册'), array('//mcampus/registration/index')) ?></li>
		<li class="active"><?php echo Yii::t('site','站点录入') ?></li>
	</ol>
	<div class="row">
		<div class="col-md-2 col-sm-2">
			<ul class="nav nav-pills nav-stacked text-left background-gray" id="pageCategory">
				<?php foreach ($this->getSubMenu() as $val){ ?>
				<li class="<?php echo ($this->getAction()->getId() == $val['url']) ? " active " : " "; ?>">
					<a href="<?php echo $this->createUrlReg($val['url']) ?>">
						<?php echo $val['label'] ?>
					</a>
				</li>
				<?php } ?>
			</ul>
		</div>
		<div class="col-md-10 col-sm-10" id='map' v-cloak>
			<div>
				<div class="btn-group wid pull-left">
					<button type="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" class="btn btn-default dropdown-toggle  wid">{{startYearList[yid]}} <span class="caret"></span></button>
					<ul class="dropdown-menu wid">
						<li v-for='(data,index) in YearList'>
							<a :href="'<?php echo $this->createUrl('/mcampus/registration/carsitelist', array('branchId' => $this->branchId)); ?>&yid='+data.id+''">{{data.title}} </a>
						</li>
					</ul>
				</div>
				<a class="btn btn-primary ml10" href="http://mega.ivymik.cn/bus_stop_win.csv"> <?php echo Yii::t('user','下载Windows模板'); ?></a>
				<a class="btn btn-primary ml10" href="http://mega.ivymik.cn/bus_stop_mac.csv"> <?php echo Yii::t('user','下载Mac模板'); ?></a>
				<button type="button" class="btn btn-primary ml10" @click='modify(0)'><?php echo Yii::t('global','手动添加');?></button>
				<?php $form=$this->beginWidget('CActiveForm', array(
	                    'id'=>'log-form',
	                    'enableAjaxValidation'=>false,
	                    'htmlOptions'=>array('class'=>'J_ajaxForm'),
	                    'action' => $this->createUrlReg('import'),
	                ));
	                ?>
				<label class="ml15">
	                <?php echo $form->fileField($model,'csv'); ?>
	            </label>
				<button type="submit" class="btn btn-primary J_ajax_submit_btn mr10"><?php echo Yii::t('global','批量添加');?></button>
				<?php $this->endWidget(); ?>
			</div>
			<div class="clearfix"></div>
			<div class="row mt15">
				<div class="col-md-6">
					<div class="panel panel-default maxHeight" v-if='city!=""'>
						<div class="panel-heading">
							<span>新增站点</span>
						</div>
						<table class="table">
							<thead>
								<tr>
									<th>小区中文</th>
									<th>小区英文</th>
									<th>停车点中文</th>
									<th>停车点英文</th>
								</tr>
							</thead>
							<tbody>
								<template v-for='(list,index) in city'>
									<template  v-if='list.update==1'>
										<tr class="info">
											<td width="25%">{{list[0]}}</td>
											<td width="25%">{{list[1]}}</td>
											<td width="25%">{{list[2]}}</td>
											<td width="25%">{{list[3]}}</td>
										</tr>
									</template>
									<template  v-else>
										<tr @click="markLocation(list,index)" :class="{warning:index==importIndex}">
											<td width="25%">{{list[0]}}</td>
											<td width="25%">{{list[1]}}</td>
											<td width="25%">{{list[2]}}</td>
											<td width="25%">{{list[3]}}</td>
										</tr>
									</template>
								</template>
							</tbody>
						</table>
					</div>
					<div class="panel panel-default">
						<div class="panel-heading">
							<span>已有站点</span><span>（{{carSiteList.length}}个）</span>
						</div>
						<table class="table" v-if='carSiteList.length!=0'>
							<thead>
								<tr>
								    <th>小区中文</th>
									<th>小区英文</th>
									<th>停车点中文</th>
									<th>停车点英文</th>
									<th>操作</th>
								</tr>
							</thead>
							<tbody>
								<template v-for='(list,idx) in carSiteList'>
									<tr class=""  @click="showlocation(list,idx)" :class="{warning:idx==current}">
										<td width="22%">{{list.name_cn}}</td>
										<td width="22%">{{list.name_en}}</td>
										<td width="22%">{{list.park_address_cn}}</td>
										<td width="22%">{{list.park_address_en}}</td>
										<td width="22%"  @click.stop>
											<div>
												<a href="javascript:;" class="btn btn-xs btn-primary" @click='modify(list)'><span class="glyphicon glyphicon-pencil"></span></a>
												<a href="javascript:;" class="btn btn-xs btn-danger" @click='remove(list.id,idx)'><span class="glyphicon glyphicon-trash"></span></a>
											</div>
										</td>
									</tr>
								</template>
							</tbody>
						</table>
						<p v-else class="panel-body">暂无数据</p>
					</div>
				</div>
				<div id='site' class="col-md-6">
					<div class="panel panel-default">
						<div class="panel-heading">
						<div class="form-horizontal">
						    <label for="inputEmail3" class="col-sm-2 text-left control-label">地图</label>
						    <div class="col-sm-10">
						    	<input name="lon" class="hidden">
								<input name="lat" class="hidden">
						        <input type="text" name="resourceAddress" class="form-control" id="address" placeholder="站点名称">
						    </div>
						</div>
						<div class="clearfix"></div>
						</div>
						<div class="panel-body" id='mapExample'>

						</div>
						<p class=" pull-right mt15">
							<a href="javascript:;" class="btn btn-primary" id='update' @click='subcarlist()'>提交</a>
						</p>
					</div>
				</div>
			</div>
			<div class="modal fade bs-example-modal-lg" id="modify" tabindex="-1" role="dialog" data-backdrop="static">
                <div class="modal-dialog" >
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-hidden="true">
                            &times;
                        </button>
                            <h4 class="modal-title" id="myModalLabel">
                            更新
                        </h4>
                        </div>
                        <div class="modal-body">
                            <div class="form-horizontal">
                                <div class="form-group">
                                    <label for="inputEmail3" class="col-sm-2 control-label">小区中文</label>
                                    <div class="col-sm-10">
                                      <input type="text" class="form-control" id='name_cn' :value='modifyAddress.name_cn'>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="inputEmail3" class="col-sm-2 control-label">小区英文</label>
                                    <div class="col-sm-10">
                                      <input type="text" class="form-control" id='name_en' :value='modifyAddress.name_en'>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="inputEmail3" class="col-sm-2 control-label">停车点中文</label>
                                    <div class="col-sm-10">
                                      <input type="text" class="form-control" id='park_address_cn' :value='modifyAddress.park_address_cn'>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="inputEmail3" class="col-sm-2 control-label">停车点英文</label>
                                    <div class="col-sm-10">
                                      <input type="text" class="form-control" id='park_address_en' :value='modifyAddress.park_address_en'>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
                            <button type="button" class="btn btn-primary" @click='updateAddress()'>更新</button>
                        </div>
                    </div>
                </div>
            </div>
		</div>
	</div>
</div>
<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>
<script type="text/javascript">
  window._AMapSecurityConfig = {
    serviceHost: "https://apps.ivyonline.cn/_AMapService",
  };
</script>
<script type="text/javascript" src="https://webapi.amap.com/maps?v=1.4.12&key=166c9d81f042ef0ae55c7a2c85e19a7e"></script>
<script>
	var oldCarSite = <?php echo json_encode($oldCarSite) ?> //历史站点
	var carSiteList = <?php echo json_encode($carSiteList) ?>;
	var startYearList = <?php echo json_encode($startYearList) ?>;
	var yid = <?php echo $yid ?>;
	var map;
	var addMarker;
	var geocoder;
	var placeSearch;
	var lnglatXY;
	function sortKey(array, key) {
		return array.sort(function(a, b) {
			var x = parseInt(a[key])
			var y = parseInt(b[key]);
			return((x > y) ? -1 : (x < y) ? 1 : 0)
		})
	}
	var mapdata = new Vue({
		el: "#map",
		data: {
			carSiteList: carSiteList,
			startYearList: startYearList,
			yid:yid,
			YearList: '',
			oldCarSite: oldCarSite,
			city: [],
			current: '-1',
			carSiteId: '',
			addLocation:'',
			modifyAddress:'',
			role:'',
			markers:[],
			addMarkers:[],
			importIndex:'-1'
		},
		created: function() {
			var newcalendarArr = []
			for(key in startYearList) {
				var calendarArrkey = {}
				calendarArrkey.id = parseInt(key)
				calendarArrkey.title = startYearList[key]
				newcalendarArr.push(calendarArrkey)
			}
			this.YearList = sortKey(newcalendarArr, 'id')
		},
		computed: {},
		mounted: function() {
			this.init()
			this.defaultPot()
		},
		methods: {
			init() {
				map = new AMap.Map('mapExample', {
					resizeEnable: true,
					zoom: 12
					/*center: [116.397428, 39.90923] */
				});
				AMap.plugin(['AMap.ToolBar','AMap.Scale'],
			    function(){
			            map.addControl(new AMap.Scale());
			    });
				AMap.service('AMap.Geocoder', function() { //回调函数
					geocoder = new AMap.Geocoder({
						city: "北京", //城市，默认：“全国”
					});
				});
			},
			//显示已添加的站点
			defaultPot(){
				map.remove(this.markers);
				map.remove(this.addMarkers);
				for(var i = 0; i < this.carSiteList.length; i++) {
					var marker = new AMap.Marker({
						icon: 'https://webapi.amap.com/theme/v1.3/markers/n/mark_b.png',
						offset: new AMap.Pixel(-13, -30),
					});
					this.markers.push(marker)
					marker.setPosition(new AMap.LngLat(this.carSiteList[i].longitude, this.carSiteList[i].latitude));
					marker.setMap(map);
					if(GV.lang=="zh_cn"){
				        marker.content ="<p class='mt15'><b>小区名：</b><span>"+this.carSiteList[i].name_cn+"</span></p>"+
            				"<p><b>停车地点：</b><span>"+this.carSiteList[i].park_address_cn+"</span></p>";
					}else{
						marker.content ="<p class='mt15'><b>小区名：</b><span>"+this.carSiteList[i].name_en+"</span></p>"+
            				"<p><b>停车地点：</b><span>"+this.carSiteList[i].park_address_en+"</span></p>";
					}
                    marker.on('click', markerClick);
				}
				map.setFitView()
			},
			//点击新增定位
			markLocation(list,index) {
				infoWindow.close();//关闭点击提示信息
				for(var i=0;i<this.carSiteList.length;i++){
					if(list[0]+list[2]==this.carSiteList[i].name_cn+this.carSiteList[i].park_address_cn){
						resultTip({
							error: 'warning',
							msg: '定位已标注'
						});
						this.addLocation=''
						this.importIndex=-1
						this.defaultPot()
						this.showlocation(this.carSiteList[i],-2)
						map.off('click', initMapClick);
						return
					}
				}
				this.importIndex=index
				this.current = -1
				this.addLocation=list
				locationname(list[0],list[2])
				
			},
			//查看已有定位
			showlocation(list,index){
				map.remove(this.addMarkers);
				if(index!=-2){
				this.current = index
				this.importIndex=-1
				}
				$("input[name=resourceAddress]").val(list.site_name);
				var marker = new AMap.Marker({
						icon: "http://webapi.amap.com/theme/v1.3/markers/n/mark_b.png",
						position: [list.longitude,list.latitude],
					});
				infoWindow.close();
				map.setFitView(marker); // 执行定位
			},
			//修改新增
			modify(list){
				if(list!=0){
					this.role=1
					this.modifyAddress=list

				}else{
					this.role=0
					this.modifyAddress=''
				}
				 $('#modify').modal('show')
			},
			//导出模板
			updateAddress(){
				if(this.role==0){
					this.importIndex=-1
					this.city.unshift([$('#name_cn').val(),$('#name_en').val(),$('#park_address_cn').val(),$('#park_address_en').val()])
					$('#modify').modal('hide')
					return
				}
				$.ajax({
					url: '<?php echo $this->createUrlReg('Carsite'); ?>',
					type: 'post',
					dataType: 'json',
					data: {
						'name_cn' : $("#name_cn").val(),
				        'name_en' : $("#name_en").val(),
				        'park_address_cn' : $("#park_address_cn").val(),
				        'park_address_en': $("#park_address_en").val(),
						'carSiteId':this.modifyAddress.id,
						'siteName':this.modifyAddress.site_name,
						'longitude':this.modifyAddress.longitude,
						'latitude': this.modifyAddress.latitude,
					},
					success: function(data) {
						if(data.state == 'success') {
								Vue.set(mapdata.modifyAddress,'name_cn', $('#name_cn').val())
                                Vue.set(mapdata.modifyAddress,'name_en', $('#name_en').val())
                                Vue.set(mapdata.modifyAddress,'park_address_cn',$("#park_address_cn").val())
                                Vue.set(mapdata.modifyAddress,'park_address_en',$("#park_address_en").val())
							resultTip({
								msg: data.message
							});
							infoWindow.close();
							mapdata.defaultPot()
							$('#modify').modal('hide')
						} else {
							resultTip({
								error: 'warning',
								msg: data.message
							});
						}
					},
					error: function(data) {
						resultTip({
							error: 'warning',
							msg: '请求错误'
						});
					}
				})
			},
			//删除
			remove(id,index){
				$.ajax({
					url: '<?php echo $this->createUrlReg('delCarsite'); ?>',
					type: 'post',
					dataType: 'json',
					data: {
						'regCarSiteId':id,
					},
					success: function(data) {
						if(data.state == 'success') {
							mapdata.carSiteList.splice(index,1);
							mapdata.defaultPot()
							resultTip({
								msg: data.message
							});
						} else {
							resultTip({
								error: 'warning',
								msg: data.message
							});
						}
					},
					error: function(data) {
						resultTip({
							error: 'warning',
							msg: '请求错误'
						});
					}
				})
			},
			//提交
			subcarlist(){
				$("#update").attr('disabled', true);
				if(this.importIndex==-1){
					resultTip({
							error: 'warning',
							msg:'请先选择新增站点'
						});
					$("#update").attr('disabled', false);
					return
				}
				lon=$("input[name=lon]").val().toString()
                lat=$("input[name=lat]").val().toString()
                for(var i=0;i<this.carSiteList.length;i++){
                	if(lon.substring(0,lon.length-2)+lat.substring(0,lat.length-2)==this.carSiteList[i].longitude.substring(0,this.carSiteList[i].longitude.length-2)+this.carSiteList[i].latitude.substring(0,this.carSiteList[i].latitude.length-2)){
                		resultTip({
							error: 'warning',
							msg: '定位已标注'
						});
						this.addLocation=''
						this.importIndex=-1
						this.defaultPot()
						this.showlocation(this.carSiteList[i],-2)
						map.off('click', initMapClick);
                		return
                	}
				}
				$.ajax({
					url: '<?php echo $this->createUrlReg('Carsite'); ?>',
					type: 'post',
					dataType: 'json',
					data: {
						'carSiteId': this.carSiteId,
						'siteName': $("input[name=resourceAddress]").val(),
						'longitude': $("input[name=lon]").val(),
						'latitude': $("input[name=lat]").val(),
						'park_address_cn' :this.addLocation[2],
				        'park_address_en': this.addLocation[3],
						'name_cn' :this.addLocation[0],
				        'name_en' : this.addLocation[1],
					},
					success: function(data) {
						$("#update").attr('disabled', false);
						if(data.state == 'success') {
							Vue.set(mapdata.addLocation,'update','1')
							mapdata.carSiteList.push(data.data)
							mapdata.defaultPot()
							lnglatXY=''
							map.off('click', initMapClick);
							resultTip({
								msg: data.message
							});
						} else {
							resultTip({
								error: 'warning',
								msg: data.message
							});
						}
					},
					error: function(data) {
						$("#update").attr('disabled', false);
						resultTip({
							error: 'warning',
							msg: '请求错误'
						});
					}
				})
			},
		},
	})
	// 回调：添加成功
	function cbSite(data) {
		for(var i=0;i<data.length;i++){
			mapdata.city.push(data[i])
		}
		
	}
	//回显
	myMapViewLocation()
	function myMapViewLocation(mlon, mlat){
	    if(mlon&&mlat){
	        lnglatXY = [mlon,mlat];
	        $("input[name=lon]").val(mlon);
	       	$("input[name=lat]").val(mlat);
	        addMarker(lnglatXY);
	    }
	}
	// 填写地址
	function writeAddress(lnglatXY){
		var geocoder = new AMap.Geocoder({
	        city : "北京", //城市，默认：“全国”
	    });
	    geocoder.getAddress(lnglatXY, function(status, result) {
	        if (status === 'complete' && result.info === 'OK') {
	        	geocoder_CallBack(result);
	        }
	    });
	}
	//地址回调
	function geocoder_CallBack(data) {
	    var address = data.regeocode.formattedAddress; //返回地址描述

	    $("input[name=resourceAddress]").val(address);
	}
	//打点
	var t=1;
	function addMarker(lnglatXY) {
		map.remove(mapdata.addMarkers);
		mapdata.addMarkers=[]
	    if(t == 1) {
	        marker = new AMap.Marker({
	        	icon: "http://webapi.amap.com/theme/v1.3/markers/n/mark_r.png",
	            position: lnglatXY,
	            offset: new AMap.Pixel(-13, -30),
	            // 设置是否可以拖拽
	            draggable: true,
	            cursor: 'move',
	            // 设置拖拽效果
	            raiseOnDrag: true
	        });
	        marker.setMap(map);
	        map.setFitView();// 执行定位
	        t++;
	    }
	    //修改标点位置
	    if(t != 1){
	        marker.on('dragend', showInfoM);
	        marker.setPosition(lnglatXY);
	        mapdata.addMarkers.push(marker)
	        map.setCenter(lnglatXY);
	        marker.setMap(map);
	        map.on('click',initMapClick)
	        map.setFitView(marker);// 执行定位
	    }
	}
	var infoWindow = new AMap.InfoWindow({offset: new AMap.Pixel(0, -30)});//信息窗口
	//点击点出现详情
	function markerClick(e){
		infoWindow.setContent(e.target.content);
        infoWindow.open(map, e.target.getPosition());
	}
	//拖拽重新定位获取地址
    function showInfoM(e){
        writeAddress([e.lnglat.lng,e.lnglat.lat]);
        $("input[name=lon]").val(e.lnglat.lng);
        $("input[name=lat]").val(e.lnglat.lat);
    }
    //地图注册click事件获取鼠标点击出的经纬度坐标
	function initMapClick(e){
		writeAddress([e.lnglat.lng, e.lnglat.lat]);
		addMarker([e.lnglat.lng, e.lnglat.lat]);
	}
	//根据地址获取经纬度
    function locationname(address,redirectAdd) {
    	var geocoder = new AMap.Geocoder({
	        city : "北京", //城市，默认：“全国”
	    });
   		AMap.plugin('AMap.Geocoder', function() {       
	        geocoder.getLocation(address+redirectAdd, function(status, result) {
	            if (status === 'complete' && result.info === 'OK') {
	     
	                var lon = result.geocodes[0].location.lng;
	                var lat = result.geocodes[0].location.lat;
	                $("input[name=lon]").val(lon);
	                $("input[name=lat]").val(lat);
	                $("input[name=resourceAddress]").val(address+redirectAdd);
	                lnglatXY = [lon, lat];
	                lon=lon.toString()
	                lat=lat.toString()
	                for(var i=0;i<mapdata.carSiteList.length;i++){
	                	if(lon.substring(0,lon.length-2)+lat.substring(0,lat.length-2)==mapdata.carSiteList[i].longitude.substring(0,mapdata.carSiteList[i].longitude.length-2)+mapdata.carSiteList[i].latitude.substring(0,mapdata.carSiteList[i].latitude.length-2)){
	                		resultTip({
								error: 'warning',
								msg: '定位已标注'
							});
							mapdata.addLocation=''
							mapdata.importIndex=-1
							mapdata.defaultPot()
							mapdata.showlocation(mapdata.carSiteList[i],-2)
							map.off('click', initMapClick);
	                		return
	                	}
					}
	                addMarker(lnglatXY);
	            } else {
	            	redirect(address)
	            }
	        });
	    });
	}
	//重定向
	function redirect(redirectAdd){
		AMap.plugin('AMap.Geocoder', function() {
	        var geocoder = new AMap.Geocoder({
		        city : "北京", //城市，默认：“全国”
		    });           
	        geocoder.getLocation(redirectAdd, function(status, result) {
	            if (status === 'complete' && result.info === 'OK') {
	                var lon = result.geocodes[0].location.lng;
	                var lat = result.geocodes[0].location.lat;
	                $("input[name=lon]").val(lon);
	                $("input[name=lat]").val(lat);
	                $("input[name=resourceAddress]").val(redirectAdd);
	                lnglatXY = [lon, lat];
	                addMarker(lnglatXY);
	            } else {
	            	resultTip({
						error: 'warning',
						msg: '定位失败！'
					});
					mapdata.defaultPot()
	            }
	        });
	    });
	}
</script>