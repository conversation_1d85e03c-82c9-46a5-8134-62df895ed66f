<?php
$this->breadcrumbs=array(
	'Portfolio'=>array('/portfolio/portfolio/classes'),
	Yii::t("portfolio", 'Project Report View'),
);
$colorbox = $this->widget('application.extensions.colorbox.JColorBox');
$colorbox->addInstance('.colorbox', array('iframe'=>false));
$colorbox->addInstance('.colorbox_m', array('iframe'=>true, 'width'=>'725', 'height'=>'80%'));
?>
<h3><?php
echo HtoolKits::autoLang($ProjectModel->cn_title, $ProjectModel->en_title);
?>
</h3>

<?php
$desc = HtoolKits::autoLang($ProjectModel->cn_desc, $ProjectModel->en_desc);
if($desc){
?>
<p>
<?php echo $desc;?>
</p>
<?php }?>

<?php if ($ClassMii->en_content){?>
<p><?php echo $ClassMii->en_content;?></p>
<?php }?>

<?php if ($ChildMii->en_content){?>
<p><?php echo $ChildMii->en_content;?></p>
<?php }?>

<?php if ($lid_aid_arr){?>
<h3><?php echo Yii::t("portfolio", 'Development Focus');?></h3>
<div>
    <ul>
        <?php foreach($lid_aid_arr as $lid=>$lid_aid){?>
        <li>
            <dl>
                <dt><?php echo $learning_arr[$lid];?></dt>
                <?php foreach($lid_aid_arr[$lid] as $key=>$aid){?>
                <dd><?php echo CHtml::link($activitytit[$aid], Yii::app()->getController()->createUrl("//portfolio/portfolio/activityview", array('id'=>$aid)), array("class"=>"colorbox_m"));?></dd>
                <?php }?>
            </dl>
        </li>
        <?php }?>
    </ul>
</div>
<?php }?>
<?php if ($iid_aid_arr){?>
<h3><?php echo Yii::t("portfolio", 'MI Connections');?></h3>
<div>
    <ul>
        <?php foreach($iid_aid_arr as $lid=>$lid_aid){?>
        <li>
            <dl>
                <dt><?php echo $intelligence_arr[$lid];?></dt>
                <?php foreach($iid_aid_arr[$lid] as $key=>$aid){?>
                <dd><?php echo CHtml::link($activitytit[$aid], Yii::app()->getController()->createUrl("//portfolio/portfolio/activityview", array('id'=>$aid)), array("class"=>"colorbox_m"));?></dd>
                <?php }?>
            </dl>
        </li>
        <?php }?>
    </ul>
</div>
<?php }?>

<?php if ($_savedImgs_class){?>
<h3><?php echo Yii::t("portfolio", 'Class Media');?></h3>
<?php foreach($_savedImgs_class as $img){?>
<div><?php echo CHtml::link(CHtml::image($img['thumb']), $img['url'], array('class'=>'colorbox group1', 'title'=>$img['txt']));?></div>
<?php }?>
<script>$(".group1").colorbox({rel:'group1'});</script>
<?php }?>

<?php if ($_savedImgs_child){?>
<h3><?php echo Yii::t("portfolio", 'Child Media');?></h3>
<?php foreach($_savedImgs_child as $img){?>
<div><?php echo CHtml::link(CHtml::image($img['thumb']), $img['url'], array('class'=>'colorbox group1', 'title'=>$img['txt']));?></div>
<?php }?>
<script>$(".group2").colorbox({rel:'group2'});</script>
<?php }?>