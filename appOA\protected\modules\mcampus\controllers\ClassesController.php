<?php

class ClassesController extends BranchBasedController
{
    public $actionAccessAuths = array(
        'index'                 => 'o_A_Access',
        'SetAssignMany'         => 'o_A_Adm_Class',
        'AssignStaff'           => 'o_A_Adm_Class',
        'SaveDefaultClassTime'  => 'o_A_Adm_Class',
        'SaveClass'             => 'o_A_Adm_Class',
        'DropClass'             => 'o_A_Adm_Class',
    );

    public function createUrl($route, $params = array(), $ampersand = '&')
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        return parent::createUrl($route, $params, $ampersand);
    }
    public function init(){
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'campusOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site','Campus Workspace');

        //初始化选择校园页面
        $this->branchSelectParams['hideOffice'] = true;
        $this->branchSelectParams['urlArray'] = array('//mcampus/classes/index','category'=>'current');
    }

    /**
     * 班级列表页面
     * @param string $category
     */
    public function actionIndex($category='')
    {
        Yii::import('common.models.classTeacher.*');
        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/underscore-min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/backbone-min.js');

        if($category=='other'){
            $yids = $this->getCalendars();
            $criteria = new CDbCriteria();
            $criteria->compare('t.schoolid', $this->branchId);
            $criteria->compare('t.yid', '<>'.$yids['currentYid']);
            $clases = IvyClass::model()->with('teacherInfo')->findAll($criteria);
            $historyYid = array();
            $classData = array();
            $yearData = array();
            $classTeachers = array();
            $teacherids = array();
            $teachers = array();

            foreach($clases as $class){
                $historyYid[$class->yid]=$class->yid;
                $classData[$class->yid][$class->classtype][$class->classid] = array(
                    'classid' => $class->classid,
                    'schoolid' => $class->schoolid,
                    'yid' => $class->yid,
                    'title' => $class->title,
                    'capacity' => $class->capacity,
                    'classtype' => $class->classtype,
                    'periodtime' => IvyClass::formatPeriodtime($class->periodtime),
                    'child_inclass' => $class->child_inclass,
                    'child_total' => $class->child_total,
                    'introduction' => $class->introduction,
                );

                foreach($class->teacherInfo as $tv){
                    $teacherids[$tv->teacherid]=$tv->teacherid;
                    $classTeachers[$class->classid][] = array(
                        'teacherId' => $tv->teacherid,
                        'isHead' => $tv->isheadteacher,
                        'isHelp' => $tv->ishelpteacher,
                        'weight' => $tv->weight
                    );
                }
            }

            if($teacherids){
                $criteria = new CDbCriteria();
                $criteria->compare('t.uid', $teacherids);
                $teachers = User::model()->with(array('profile','staffInfo'))->findAll($criteria);
            }

            if($historyYid){
                $criteria = new CDbCriteria();
                $criteria->compare('yid', $historyYid);
                $yearobjs = Calendar::model()->findAll($criteria);
                foreach($yearobjs as $year){
                    $yearData[$year->yid]=$year->startyear;
                }
                krsort($yearData);
            }

            foreach($teachers as $teacher){
                $teacherData[$teacher->uid] = array(
                    'id'=>$teacher->uid,
                    'name'=>$teacher->getName(),
                    'photo'=>empty($teacher->staffInfo->staff_photo)?'blank.jpg':$teacher->staffInfo->staff_photo,
                    'active'=>intval($teacher->level),
                    'assignMany'=>intval($teacher->profile->multiple_class)
                );
            }

            $typeList = IvyClass::getClassTypes();

            $this->render('other', array('category'=>$category, 'classData'=>$classData, 'yearData'=>$yearData, 'teachers'=>$teachers, 'classTeachers'=>$classTeachers, 'typeList'=>$typeList, 'teacherData'=>$teacherData));
        }
        else{
            $this->render('index');
        }
    }

    /**
     * 设置老师是否可以被分配到多个班级
     */
    public function actionSetAssignMany(){
        $doType = Yii::app()->request->getParam('doType',null);
        $teacherId = Yii::app()->request->getParam('teacherId',null);
        if($doType && $teacherId && Yii::app()->request->isAjaxRequest ){
            $profile = UserProfile::model()->findByAttributes(array('branch'=>$this->branchId, 'uid'=>$teacherId));
            if($profile){
                $setTo = ($doType == 'invoke') ? 1 : 0;
                if($profile->multiple_class != $setTo){
                    $profile->multiple_class = $setTo;
                    $profile->save();
                }
            }
            $this->addMessage('state','success');
            $this->addMessage('data', array("doType"=>$doType,'teacherId'=>$teacherId));
        }else{
            $this->addMessage('state','fail');
            $this->addMessage('message','校园不匹配');
        }
        $this->showMessage();
    }

    /**
     * 处理教师分配到班级
     */
    public function actionAssignStaff(){
        Yii::import('common.models.classTeacher.*');

        $postData = $_POST['postData'];
        $teacherPost = $_POST['teacherPost'];

        if($postData['classid'] && $postData['calendarid']){
            ClassTeacher::model()->deleteAllByAttributes(
                array(
                    'classid' => $postData['classid'],
                    'schoolid' => $this->branchId,
                    'yid' => $postData['calendarid']
                )
            );

            $result[] = array();
            foreach( array_keys($teacherPost) as $teacherId){
                $classTeacher = new ClassTeacher;
                $classTeacher->setAttributes(
                    array(
                        'classid' => $postData['classid'],
                        'schoolid' => $this->branchId,
                        'yid' => $postData['calendarid'],
                        'teacherid' => $teacherId,
                        'isheadteacher' => isset($teacherPost[$teacherId]['isHead'])?1:0,
                        'ishelpteacher' => isset($teacherPost[$teacherId]['isHelp'])?1:0,
                        'weight' => intval($teacherPost[$teacherId]['weight']),
                        'updated_timestamp' => time(),
                        'userid' => Yii::app()->user->getId()
                    )
                );
                $classTeacher->save();
            }
            $theClass = $this->getClassList($this->branchId, $postData['calendarid'], $postData['classid']);
            if($theClass){
                $i=1;
                foreach($theClass->teacherInfo as $id=>$_t){
                    $result[] = array(
                        'teacherId' => $id,
                        'isHead' => $_t->isheadteacher,
                        'isHelp' => $_t->ishelpteacher,
                        'weight' => $i++
                    );
                }
                $this->addMessage('state','success');
                $this->addMessage('callback','assignedCallBack');
                $this->addMessage('message','保存成功');
                $this->addMessage('data',array('classid'=>$theClass->classid,'data'=>$result));
                $this->showMessage();
            }
        }

        $this->addMessage('state','fail');
        $this->addMessage('message','非法提交');
//        $this->addMessage('callback','reload');
        $this->showMessage();

    }

    public function actionSelect(){
        $this->render('//layouts/common/branchSelect');
    }

    public function getCampusTeachers(){
        $teacherids = OA::getCampusTeachers($this->branchId);

        $crit = new CDbCriteria();
        $crit->select = "uid";
        $crit->compare('schoolid',$this->branchId);
        $crit->compare('type','edu');
        $teachers_edu = AdmBranchLink::model()->findAll($crit);
        $teacher_edu = array();
        if($teachers_edu){
            foreach($teachers_edu as $_teache){
                $teacher_edu[] = $_teache->uid;
            }
            $teacherids = array_merge($teacherids,$teacher_edu);
        }
        
        $teachers = array();
        if($teacherids){
            $crit = new CDbCriteria();
            $crit->compare('t.uid', $teacherids);
            $crit->index = 'uid';
            $teachers = User::model()->with(array('profile','staffInfo'))->findAll($crit);
        }

        return $teachers;
    }

    public function getClassList($schoolid, $yid, $classid=0){
        $crit = new CDbCriteria();
        $crit->compare('t.schoolid',$schoolid);
        $crit->compare('t.yid',$yid);
        if($classid){
            $crit->compare('t.classid',$classid);
        }
        $crit->order='t.child_age ASC, t.stat ASC, t.title ASC';
        if($classid){
            return IvyClass::model()->with(array('teacherInfo'=>array('index'=>'teacherid','order'=>'weight ASC')))->find($crit);
        }else{
            return IvyClass::model()->with(array('teacherInfo'=>array('index'=>'teacherid','order'=>'weight ASC')))->findAll($crit);
        }

    }

    //保存校园默认班级时间
    public function actionSaveDefaultClassTime(){
        $classTime = Yii::app()->request->getParam('classTime',null);
        if($classTime && Yii::app()->request->isAjaxRequest ){
            $branchVar = BranchVar::model()->findByAttributes(array('branchid'=>$this->branchId, 'category'=>'classtime'));
            $data = implode('|',$classTime);
            if(preg_match('/^[0-9|]+$/',$data)){
                if(is_null($branchVar)){
                    $branchVar = new BranchVar();
                    $branchVar->branchid = $this->branchId;
                    $branchVar->category = 'classtime';
                    $branchVar->flag = 0;
                }
                $branchVar->setAttributes(array(
                    'data' => $data,
                    'user' => Yii::app()->user->getId(),
                    'updated' => time()
                ));
                if($branchVar->save()){
                    $this->addMessage('state','success');
                    $this->showMessage();
                }
            }

        }
        $this->addMessage('state','fail');
        $this->addMessage('message','System Error');
        $this->showMessage();
    }

    //保存班级信息
    public function actionSaveClass(){
        if(Yii::app()->request->isAjaxRequest){
            if($_POST['IvyClass']['classid']){
                $classModel = IvyClass::model()->findByPk($_POST['IvyClass']['classid']);//array('schoolid'=>$this->branchId,'classid'=>$_POST['IvyClass']['classid']));
            }else{
                $classModel = new IvyClass();
                $classModel->setAttribute('schoolid', $this->branchId);
                $classModel->setAttribute('created_timestamp', time());
            }
            if($classModel->schoolid == $this->branchId){
                $classModel->attributes = $_POST['IvyClass'];
                $calendars = $this->getCalendars();
                if(in_array($classModel->yid, $calendars)){
                    $ageMapping = IvyClass::getAgeMapping();
                    if($classModel->isNewRecord)
                        $classModel->stat = ($classModel->yid == $calendars['currentYid']) ? 10 : 0;
                    $reg = '/^e\d{1,}$/';
                    if(!preg_match($reg, $classModel->classtype)){
                        $classModel->periodtime = implode('|', $classModel->periodData);
                    }
                    $classModel->updated_timestamp = time();
                    $classModel->userid = Yii::app()->user->getId();
                    $classModel->child_age = $ageMapping[$classModel->classtype];

                    if( $classModel->save() ){
                        $this->addMessage('state','success');
                        $this->addMessage('message', 'Data saved.');
                        $this->addMessage('refresh', true);
                        $this->showMessage();
                    }else{
                        $this->addMessage('state', 'fail');
                        $err = current($classModel->getErrors());
                        $this->addMessage('message', $err[0]);
//                        $this->addMessage('callback', 'postFeedback');
                        $this->showMessage();
                    }

                }

            }
        }
    }

    /**
     * 删除班级信息
     */
    public function actionDropClass(){
        if(Yii::app()->request->isAjaxRequest && Yii::app()->request->isPostRequest && $_POST['classId']){
            $classId = intval($_POST['classId']);
            $classModel = IvyClass::model()->findByPk($classId);
            if($classModel && $classModel->schoolid == $this->branchId){
                $sql = 'select count(*) as num from ivy_class_teacher where classid = ' . $classId;
                $result = Yii::app()->db->createCommand($sql)->queryRow();
                if($result['num']==0){
                    $sql = 'select count(*) as num from ivy_child_profile_basic where classid = ' . $classId;
                    $result = Yii::app()->db->createCommand($sql)->queryRow();
                    if($result['num']==0){
                        //判断未来学年
                        $sql = 'select count(*) as num from ivy_child_reserve where classid='.$classId;
                        $result = Yii::app()->db->createCommand($sql)->queryRow();
                        if ($result['num'] ==0){
                            $sql = 'select count(*) as num from ivy_invoice_invoice where classid = ' . $classId;
                            $result = Yii::app()->db->createCommand($sql)->queryRow();
                            if($result['num']==0){
                                $classModel->delete();
                                $this->addMessage('state','success');
                                $this->addMessage('message','成功删除');
                                $this->addMessage('data',array('classId'=>$classId));
                                $this->showMessage();
                            }
                        }
                    }
                }
            }
        }
        $this->addMessage('state','fail');
        $this->addMessage('message','无权删除');
        $this->showMessage();
    }
    
    public function actionGetChildrenList(){
        Yii::import('common.models.invoice.ChildReserve');
        Yii::import('common.models.invoice.ChildDiscountLink');
        Yii::import('common.models.invoice.DiscountSchool');
        Yii::import('common.models.invoice.DiscountCategory');
        $classId = Yii::app()->request->getPost('classid',0);
        $childList = array();
        if ($classId){
            $basic = ChildProfileBasic::model()->with('bindDiscount')->findAll('t.classid=:classid and t.schoolid=:schoodid',array(':classid'=>$classId,':schoodid'=>$this->branchId));
            $next = ChildReserve::model()->findAll('t.classid=:classid and t.schoolid=:schoodid',array(':classid'=>$classId,':schoodid'=>$this->branchId));
            if (!empty($basic)){
                foreach ($basic as $child){
                    $childList[] = array(
                        'childid'=>$child->childid,
                        'name'=>$child->getChildName(),
                        'active'=> (!empty($child->bindDiscount)) ? 1 : 0,
                    );
                }
            } elseif (!empty($next)) {
                foreach ($next as $child){
                    $childList[] = array(
                        'childid'=>$child->childid,
                        'name'=>$child->childProfile->getChildName(),
                        'active'=> (!empty($child->childProfile->bindDiscount)) ? 1 : 0,
                    );
                }
            }
        }
        echo CJSON::encode($childList);
    }
}
