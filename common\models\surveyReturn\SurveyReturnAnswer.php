<?php

/**
 * This is the model class for table "ivy_survey_return_answer".
 *
 * The followings are the available columns in table 'ivy_survey_return_answer':
 * @property integer $id
 * @property string $school_id
 * @property integer $yid
 * @property integer $childid
 * @property integer $cid
 * @property integer $survey_id
 * @property integer $template_id
 * @property integer $pre_id
 * @property integer $q_id
 * @property string $q_val
 * @property string $q_memo
 * @property string $q_signature
 * @property integer $created_at
 * @property integer $created_by
 * @property integer $updated_at
 * @property integer $updated_by
 * @property integer $answer_no
 * @property integer $del_flag
 */
class SurveyReturnAnswer extends CActiveRecord
{
    public $filedata;
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_survey_return_answer';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('school_id, yid, childid, survey_id, template_id, q_id, q_val, created_at, created_by, updated_at, updated_by, answer_no, del_flag', 'required'),
			array('yid, childid, cid, survey_id, template_id, q_id, created_at, created_by, updated_at, updated_by, answer_no, del_flag, pre_id', 'numerical', 'integerOnly'=>true),
			array('school_id, q_val, q_memo, q_signature', 'length', 'max'=>255),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, school_id, yid, childid, cid, survey_id, template_id, q_id, q_val, q_memo, q_signature, created_at, created_by, updated_at, updated_by', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
			'returnchild' => array(self::BELONGS_TO, "SurveyReturnChild", array("cid" => "id")),
			'question' => array(self::BELONGS_TO, "SurveyReturnQuestions", array("q_id" => "id")),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'school_id' => 'School',
			'yid' => 'Yid',
			'childid' => 'Childid',
			'cid' => 'Cid',
			'survey_id' => 'Survey',
			'template_id' => 'Template',
			'q_id' => 'Q',
			'q_val' => 'Q Val',
			'q_memo' => 'Q Memo',
			'q_signature' => 'Q signature',
			'created_at' => 'Created At',
			'created_by' => 'Created By',
			'updated_at' => 'Updated At',
			'updated_by' => 'Updated By',
			'answer_no' => 'answer no',
			'del_flag' => 'del flag',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('school_id',$this->school_id,true);
		$criteria->compare('yid',$this->yid);
		$criteria->compare('childid',$this->childid);
		$criteria->compare('cid',$this->cid);
		$criteria->compare('survey_id',$this->survey_id);
		$criteria->compare('template_id',$this->template_id);
		$criteria->compare('q_id',$this->q_id);
		$criteria->compare('q_val',$this->q_val,true);
		$criteria->compare('q_memo',$this->q_memo,true);
		$criteria->compare('q_signature',$this->q_signature,true);
		$criteria->compare('created_at',$this->created_at);
		$criteria->compare('created_by',$this->created_by);
		$criteria->compare('updated_at',$this->updated_at);
		$criteria->compare('updated_by',$this->updated_by);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return SurveyReturnAnswer the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

    public function uploadFile()
    {
        // 判断是否为 base64 图片
        if (strpos($this->filedata, 'base64') === false) {
            return true;
        }
        $data = base64_decode(preg_replace('#^data:image/\w+;base64,#i', '', $this->filedata));

        $normalDir = rtrim(Yii::app()->params['xoopsVarPath'], '/') .'/';

        $fileName = uniqid() . '.png';

        $filePath = $normalDir . $fileName;
        if (file_put_contents($filePath, $data)) {
            $this->q_signature = $fileName;
            if ($this->q_signature) {
                // 上传到 OSS
                $objectPath = 'survey/';
                $oss = CommonUtils::initOSS('private');
                if ($oss->uploadFile($objectPath . $this->q_signature, $filePath)) {
                    unlink($filePath);
                    return true;
                }
            }
        }
        return false;
    }
}
