<?php

/**
 * This is the model class for table "ivy_cur_projects".
 *
 * The followings are the available columns in table 'ivy_cur_projects':
 * @property integer $pid
 * @property string $en_title
 * @property string $cn_title
 * @property integer $appeared
 * @property integer $state
 * @property string $age
 * @property integer $season
 * @property integer $weight
 * @property string $en_desc
 * @property string $cn_desc
 * @property string $en_memo
 * @property string $cn_memo
 * @property integer $official
 * @property integer $userid
 * @property integer $update_timestamp
 */
class CurProjects extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CurProjects the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_cur_projects';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('en_title, cn_title', 'required'),
			array('appeared, state, season, weight, official, userid, update_timestamp', 'numerical', 'integerOnly'=>true),
			array('en_title, cn_title, age', 'length', 'max'=>255),
			array('state, age, season, en_desc, cn_desc, cn_memo, userid, update_timestamp,en_memo', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('pid, en_title, cn_title, appeared, state, age, season, weight, en_desc, cn_desc, en_memo, cn_memo, official, userid, update_timestamp', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'activity'=>array(self::HAS_MANY, 'CurActivityInfo', 'pid'),
            'user'=>array(self:: BELONGS_TO,'User','userid'),
            'tagid'=>array(self:: BELONGS_TO,'User','userid'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'pid' => 'Pid',
			'en_title' => Yii::t('curriculum','En Title'),
			'cn_title' => Yii::t('curriculum','Cn Title'),
			'appeared' => Yii::t('curriculum','Appeared'),
			'state' => Yii::t('curriculum','State'),
			'age' => Yii::t('curriculum','Age'),
			'season' => Yii::t('curriculum','Season'),
			'weight' => Yii::t('curriculum','Weight'),
			'en_desc' => Yii::t('curriculum','En Desc'),
			'cn_desc' => Yii::t('curriculum','Cn Desc'),
			'en_memo' => Yii::t('curriculum','En Memo'),
			'cn_memo' => Yii::t('curriculum','Cn Memo'),
			'official' => Yii::t('curriculum','Education Team'),
			'attachments' => Yii::t('curriculum','Attachments'),
			'userid' => 'Userid',
			'update_timestamp' => 'Update Timestamp',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('pid',$this->pid);
		$criteria->compare('en_title',$this->en_title,true);
		$criteria->compare('cn_title',$this->cn_title,true);
		$criteria->compare('appeared',$this->appeared);
		$criteria->compare('state',$this->state);
		$criteria->compare('age',$this->age,true);
		$criteria->compare('season',$this->season);
		$criteria->compare('weight',$this->weight);
		$criteria->compare('en_desc',$this->en_desc,true);
		$criteria->compare('cn_desc',$this->cn_desc,true);
		$criteria->compare('en_memo',$this->en_memo,true);
		$criteria->compare('cn_memo',$this->cn_memo,true);
		$criteria->compare('official',$this->official);
		$criteria->compare('userid',$this->userid);
		$criteria->compare('update_timestamp',$this->update_timestamp);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}