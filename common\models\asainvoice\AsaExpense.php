<?php

/**
 * This is the model class for table "ivy_asa_expense".
 *
 * The followings are the available columns in table 'ivy_asa_expense':
 * @property integer $id
 * @property string $voucherid
 * @property string $title
 * @property string $branch
 * @property integer $uid
 * @property integer $expense_uid
 * @property double $amount
 * @property string $payee_user
 * @property string $payee_bank
 * @property string $payee_branch
 * @property string $payee_account
 * @property string $memo
 * @property integer $status
 * @property integer $type
 * @property integer $created
 * @property integer $updated
 */
class AsaExpense extends CActiveRecord
{
	const STATS_DRAFT = 0;     //草稿
	const STATS_REFER = 10;    //提交
	const STATS_PAST = 20;     //通过
	const STATS_REFUSAL = 30;  //拒绝
	const STATS_INVALID = 99;  //作废
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_asa_expense';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('title, branch, uid, expense_uid, amount, status, created, updated', 'required'),
			array('uid, expense_uid, status, created, updated, type', 'numerical', 'integerOnly'=>true),
			array('amount', 'numerical'),
			array('voucherid, title, branch, payee_user, payee_bank, payee_branch, payee_account, memo', 'length', 'max'=>255),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, voucherid, title, branch, uid, expense_uid, amount, payee_user, payee_bank, payee_branch, payee_account, memo, status, created, updated, type', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
			'asaExpenseItem' => array(self::HAS_MANY, 'AsaExpenseItem', array('eid'=>'id')),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'voucherid' => Yii::t('asa','凭证唯一编号'),
			'title' => Yii::t('asa','标题'),
			'branch' => Yii::t('asa','学校'),
			'uid' => Yii::t('asa','增加人'),
			'expense_uid' => Yii::t('asa','申请人'),
			'amount' => Yii::t('asa','金额'),
			'payee_user' => Yii::t('asa','收款人户名'),
			'payee_bank' => Yii::t('asa','开户行'),
			'payee_branch' => Yii::t('asa','开户地址'),
			'payee_account' => Yii::t('asa','收款人账号'),
			'memo' => Yii::t('asa','备注'),
			'status' => Yii::t('asa','状态'),
			'created' => Yii::t('asa','申请时间'),
			'updated' => Yii::t('asa','更新时间'),
			'type' => Yii::t('asa','申请类型'),
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('voucherid',$this->voucherid,true);
		$criteria->compare('title',$this->title,true);
		$criteria->compare('branch',$this->branch,true);
		$criteria->compare('uid',$this->uid);
		$criteria->compare('expense_uid',$this->uid);
		$criteria->compare('amount',$this->amount);
		$criteria->compare('payee_user',$this->payee_user,true);
		$criteria->compare('payee_bank',$this->payee_bank,true);
		$criteria->compare('payee_branch',$this->payee_branch,true);
		$criteria->compare('payee_account',$this->payee_account,true);
		$criteria->compare('memo',$this->memo,true);
		$criteria->compare('status',$this->status);
		$criteria->compare('created',$this->created);
		$criteria->compare('updated',$this->updated);
		$criteria->compare('type',$this->type);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return AsaExpense the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	public static function getConfig($lang=null)
	{
		return  array(
			AsaExpense::STATS_DRAFT => '草稿',     //草稿
			AsaExpense::STATS_REFER => '提交',    //提交
			AsaExpense::STATS_PAST => '通过',     //通过
			AsaExpense::STATS_REFUSAL => '拒绝',
		);
	}
}
