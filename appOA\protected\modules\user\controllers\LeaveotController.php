<?php

/**
 * Class LeaveotController
 */

class LeaveotController extends ProtectedController
{
	public $userInfo = null;

	public function createUrl($route, $params = array(), $ampersand = '&')
    {
        if (empty($params['userid'])) {
            $params['userid'] = $this->userInfo->uid;
        }
        return parent::createUrl($route, $params, $ampersand);
    }

	public function init(){
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";
        $this->modernMenuFlag = 'main';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        Yii::import('common.models.hr.StaffApprover');
    }

	public function beforeAction($action) {
		parent::beforeAction($action);
        $currentUserId =  Yii::app()->user->getId();
		$userid = Yii::app()->request->getParam('userid', $currentUserId);
		if ($userid == Yii::app()->user->getId()){
			$this->userInfo = $this->staff;
		}else{
			//判断是否有判断访问其他人主页
            $approverUserId = StaffApprover::getApproverUser($userid);
            if ($approverUserId == $currentUserId){
                $this->userInfo = User::model()->with('profile')->findByPk($userid);
            }else{
                return false;
            }
		}
		return true;
	}

	public function actionIndex()
	{
		$appLabel = UserLeave::getApplicationLabel();
		$leaveType = UserLeave::getLeaveType();
		//当前用户假期汇总
		$leaveList = UserLeave::model()->getCountUserLeave($this->userInfo->uid);
        //当前用户请假记录
        $crit = new CDbCriteria();
        $crit->compare('t.staff_uid', $this->userInfo->uid);
        $crit->compare('t.status', LeaveItem::STATUS_WAITING);
        $crit->with = 'creatorUser';
        $crit->order = 't.startdate DESC,t.type DESC';
        $historyData = new CActiveDataProvider('LeaveItem', array(
            'criteria' => $crit,
            'pagination' => false,
        ));
        //最近30天内被拒绝请假
        $time = time();
        $crit = new CDbCriteria();
        $crit->compare('t.staff_uid', $this->userInfo->uid);
        $crit->compare('t.status', LeaveItem::STATUS_REJECT);
        $crit->addCondition('t.approvedate>='. mktime(0, 0, 0, date('m',  $time), date('d',$time)-30, date('Y',$time)));
        $crit->with = 'checkUser';
        $crit->order = 't.startdate DESC,t.type DESC';
        $rejectData = new CActiveDataProvider('LeaveItem', array(
            'criteria' => $crit,
            'pagination' => false,
        ));
		//本月累计加班、休假
        $command = Yii::app()->db->createCommand();
        $command->from(LeaveItem::model()->tableName())->where('staff_uid=:staff_uid and month=:month and status=:status', array(":staff_uid"=> $this->userInfo->uid,"month"=>date('Ym',  time()),"status"=>LeaveItem::STATUS_AGREE));
        $command->andWhere(array('or','type<='.UserLeave::TYPE_NO_PAY_LEAVE,'type>='.UserLeave::OT_WEEK_DAY));
        $command->order('startdate DESC');
        $infoArr = $command->queryAll();
        $leaveCountList = array();
        if (is_array($infoArr) && count($infoArr)){
            foreach ($infoArr as $val){
                $leaveCountList[$val['category']][$val['type']]['sum'] = isset($leaveCountList[$val['category']][$val['type']]['sum']) ? $leaveCountList[$val['category']][$val['type']]['sum']+$val['hours'] : $val['hours'];
                $leaveCountList[$val['category']]['list'][] = array(
                    'id' => $val['id'],
                    'typeTxt' => $leaveType[$val['type']],
                    'dateTxt' => UserLeave::showLeavePeriod($val),
                    'hoursTxt' => UserLeave::getLeaveFormat($val['hours']),
                    'created' => OA::formatDateTime($val['created']),
                );
                $leaveCountList[$val['category']][$val['type']]['sumTxt'] = UserLeave::getLeaveFormat($leaveCountList[$val['category']][$val['type']]['sum']);
            }
            unset($infoArr);
        }
        //register script
		$cs = Yii::app()->clientScript;
		$cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/underscore-min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/backbone-min.js');
		$cs->registerCoreScript('jquery.ui');
		$cs->registerScriptFile($cs->getCoreScriptUrl() . '/jui/js/jquery-ui-i18n.min.js');
        $cs->registerCssFile($cs->getCoreScriptUrl().'/jui/css/base/jquery-ui.css');
		$this->render('index', array(
                'appLabel'=>$appLabel,
                'leaveList'=>$leaveList,
                'historyData'=>$historyData,
                'leaveCountList'=>$leaveCountList,
                'leaveType'=>$leaveType,
                'rejectData'=>$rejectData,
            )
        );
	}

    //请假或加班保存
    public function actionSaveLeave(){
		$type = Yii::app()->request->getPost('type');
		$methods = Yii::app()->request->getPost('methods');
        $appLabel = UserLeave::getApplicationLabel();
        $this->addMessage('state', 'fail');
        $this->addMessage('message','保存信息失败！');
        if (Yii::app()->request->isAjaxRequest && in_array($methods, array('hour','day','more')) && isset($appLabel[$type])){
            $model = new LeaveItem();
            //当前用户假期汇总
            $model->setScenario('annualLeave');
            if (isset($_POST['LeaveItem'])){
                $dStartDate = '';
                $model->attributes = $_POST['LeaveItem'];
                if (!isset($appLabel[$type]['subtitle'])){
                    $model->type = $type;
                }
                $type = ($model->type) ? $model->type : $type;
                $model->staff_uid = $this->userInfo->uid;
                $model->category = ($type<20) ? UserLeave::USER_TYPE_LEAVE : UserLeave::USER_TYPE_OVERTIME;
                if ($methods== 'hour'){
                    $model->startdate = strtotime($_POST['LeaveItem']['startdate']);
                    $model->enddate = strtotime($_POST['LeaveItem']['startdate']);
                }elseif($methods == 'day'){
                    $dStartDate = isset($_POST['LeaveItem']['startdate'])? explode(',', $_POST['LeaveItem']['startdate']) : '';
                    $model->startdate = is_array($dStartDate) ? strtotime($dStartDate[0]) :'';
                    $model->enddate = 0;
                }elseif($methods== 'more'){
                    $model->startdate = strtotime($_POST['LeaveItem']['startdate']);
                    $model->enddate = strtotime($_POST['LeaveItem']['enddate']);
                }
                $model->startTime = isset($_POST['LeaveItem']['startTime']) ? $_POST['LeaveItem']['startTime'] : 0;
                $model->endTime = isset($_POST['LeaveItem']['endTime']) ? $_POST['LeaveItem']['endTime'] : 0;
                $model->hours = UserLeave::getHours($_POST['LeaveItem']['startdate'], $_POST['LeaveItem']['enddate'], $methods,$model);
                $model->month = 0;
                $model->branchid = $this->userInfo->profile->branch;
                $model->applydate = time();
                //查员工的部门（天元港的员工）或所在班级（学校员工）
                $dep = DepPosLink::model()->findByPk($this->userInfo->profile->occupation_en);
                $model->classid = $dep->department_id;
                $model->status = LeaveItem::STATUS_WAITING;
                $model->approver = StaffApprover::getApproverUser($model->staff_uid);
                $model->creator = Yii::app()->user->getId();
                $model->created = time();
                if ($model->validate()){
                    if ($methods == 'hour'){
                        if (!empty($model->startTime) && !empty($model->endTime)){
                            $model->startdate = strtotime($_POST['LeaveItem']['startdate']." ".$model->startTime);
                            $model->enddate = strtotime($_POST['LeaveItem']['startdate']." ".$model->endTime);
                            $model->month = date('Ym',$model->startdate);
                        }
                        if ($model->save()){
                            $this->addMessage('state', 'success');
                            $this->addMessage('refresh', true);
                            $this->addMessage('message', '保存成功');
                        }
                    }elseif ($methods == 'day') {
                        $ret = array();
                        foreach ($dStartDate as $val){
                            $m = new LeaveItem();
                            $m->setAttributes($model->getAttributes());
                            $m->startdate = strtotime($val);
                            $m->enddate = strtotime($val);
                            $m->month = date('Ym',$m->startdate);
                            $m->hours = UserLeave::setLeaveFormat(1, 0);
                            if (!$ret[]=$m->save()){
                                break;
                            }
                        }
                        if (count($ret) == count($dStartDate))
                        {
                            $this->addMessage('state', 'success');
                            $this->addMessage('refresh', true);
                            $this->addMessage('message', '保存成功');
                        }
                    }elseif ($methods == 'more') {
                        $sumMonth = array();
                        $startMonth = date('Y-m',$model->startdate);
                        $endMonth = date('Y-m',$model->enddate);
                        for($i=$startMonth;$i<=$endMonth;$i=date('Y-m',strtotime($i.'next month'))){
                            $m = new LeaveItem();
                            $m->setAttributes($model->getAttributes());
                            $firstDay = strtotime($i.'-01');
                            $lastDay = strtotime($i.'-'.date('t',$firstDay));
                            if (($i == $startMonth) && ($i != $endMonth)){
                                $m->startdate = $model->startdate;
                                $m->enddate = $lastDay;
                                $m->month = date('Ym',$m->startdate);
                                $m->hours = UserLeave::setLeaveFormat(1+(($m->enddate-$m->startdate)/86400), 0);
                            }elseif(($i == $endMonth) && ($i != $startMonth)){
                                $m->startdate = $firstDay;
                                $m->enddate = $model->enddate;
                                $m->month = date('Ym',$m->startdate);
                                $m->hours = UserLeave::setLeaveFormat(1+(($m->enddate-$m->startdate)/86400), 0);
                            }elseif (($i == $startMonth) && ($i == $endMonth)) {
                                $m->startdate = $model->startdate;
                                $m->enddate = $model->enddate;
                                $m->month = date('Ym',$m->startdate);
                                $m->hours = UserLeave::setLeaveFormat(1+(($m->enddate-$m->startdate)/86400), 0);
                            }else{
                                $m->startdate = $firstDay;
                                $m->enddate = $lastDay;
                                $m->month = date('Ym',$m->startdate);
                                $m->hours = UserLeave::setLeaveFormat(date('t',$firstDay), 0);
                            }
                            $sumMonth[] = $i;
                            if (!$ret[]=$m->save()){
                                break;
                            }
                        }
                        if (count($ret) == count($sumMonth))
                        {
                            $this->addMessage('state', 'success');
                            $this->addMessage('refresh', true);
                            $this->addMessage('message', '保存成功');
                        }
                    }
                }else{
                    $this->addMessage('state', 'fail');
                    $errs = current($model->getErrors());
                    $this->addMessage('message', $errs ? $errs[0] : '保存信息失败！');
                }
            }
        }
        $this->showMessage();
	}

    //根据用户ID取申请数据
    public function actionGetBehalfApplicationData(){
        $uid = $this->userInfo->uid;
        if (Yii::app()->request->isAjaxRequest && $uid){
            $crite = new CDbCriteria;
            $crite->compare('t.approver', $uid);
            $staffApprover = StaffApprover::model()->with('user')->findAll('approver=:uid', array(':uid'=>$uid));
            $approverList = array();
            if (!empty($staffApprover)){
                foreach ($staffApprover as $val){
                    $approverList[] = array(
                        'uid' => $val->staff_uid,
                        'name' => $val->user->getName()
                    );
                }
            }
        }
        $this->addMessage('data',$approverList);
        $this->showMessage();
    }

    //删除未被审核的请假或加班
    public function actionDelete(){
        $uid = $this->userInfo->uid;
        $id = Yii::app()->request->getParam('id');
        $command = Yii::app()->request->getParam('command');
        $this->addMessage('state', 'fail');
        $this->addMessage('message','撤销失败！');
        if (Yii::app()->request->isAjaxRequest && $uid && $id){
            $model = LeaveItem::model()->findByPk($id);
            if (($model->status == LeaveItem::STATUS_WAITING)){
                if (isset($command) && $command == 'invalid' && $model->approver == Yii::app()->user->getId()){
                    $model->status = LeaveItem::STATUS_INVALID;
                    $model->approvedate = time();
                    $model->check_desc = Yii::app()->user->getId()."代申请（系统生成）";
                    if ($model->save()){
                        $this->addMessage('state', 'success');
                        $this->addMessage('message', '保存成功');
                        $this->addMessage('refresh', true);
                        $this->addMessage('referer', "/user/leaveot?userid=".$model->staff_uid);
                    }
                }else{
                    if (in_array(Yii::app()->user->getId(), array($model->staff_uid, $model->creator)) && $model->delete()) {
                        $this->addMessage('state', 'success');
                        $this->addMessage('message', '保存成功');
                        $this->addMessage('refresh', true);
                    }
                }
            }
        }
        $this->showMessage();
    }

    //休假加班审核列表
    public function actionCheckList(){
        $leaveType = UserLeave::getLeaveType();
        $crit = new CDbCriteria();
        $crit->compare('t.approver', Yii::app()->user->getId());
        $crit->compare('t.status', 0);
        $crit->order = 't.startdate DESC,t.type DESC';
        $model = LeaveItem::model()->with('appUser')->findAll($crit);
        $checkList = array();
        if (!empty($model)){
            foreach ($model as $val){
                $checkList[$val->staff_uid]['sum'] = isset($checkList[$val->staff_uid]['sum']) ? $checkList[$val->staff_uid]['sum']+1 : 1;
                $checkList[$val->staff_uid]['staff_uid'] = $val->staff_uid;
                $checkList[$val->staff_uid]['name'] = $val->appUser->getName();
                $checkList[$val->staff_uid]['img'] = $val->appUser->user_avatar;
                $checkList[$val->staff_uid]['list'][] = array(
                    'id'=>$val->id,
                    'typeTxt' => $leaveType[$val['type']],
                    'dateTxt' => UserLeave::showLeavePeriod($val->getAttributes()),
                    'hoursTxt' => UserLeave::getLeaveFormat($val['hours']),
                    'desc' => CHtml::encode($val['apply_desc']),
                    'created' => OA::formatDateTime($val['created']),
                );
            }
        }
        $cs = Yii::app()->clientScript;
		$cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/underscore-min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/backbone-min.js');
		$cs->registerCoreScript('jquery.ui');
        $this->render('checkList',array('checkList'=>$checkList));
    }

    //休假加班审核
    public function actionCheck(){
        $id = Yii::app()->request->getParam('id', 0);
        $agree = Yii::app()->request->getParam('agree');
        $desc = Yii::app()->request->getParam('desc');
        $this->addMessage('state', 'fail');
        $this->addMessage('message','审核失败！');
        if (Yii::app()->request->isAjaxRequest && $id && in_array($agree, array('no','yes'))) {
            $ids = is_array($id) ? $id : array($id);
            foreach ($ids as $id){
                $model = LeaveItem::model()->findByPk($id);
                if (($model->approver == $this->userInfo->uid) && ($model->status == LeaveItem::STATUS_WAITING)) {
                    $model->approvedate = time();
                    $model->check_desc = $desc[$id];
                    $model->status = (strtolower($agree) == 'yes') ? LeaveItem::STATUS_AGREE : LeaveItem::STATUS_REJECT;
                    //处理请假材料
                    $_type = array(UserLeave::TYPE_MARRIAGE_LEAVE, UserLeave::TYPE_MATERNITY_LEAVE, UserLeave::TYPE_BEREAVEMENT_LEAVE,UserLeave::TYPE_SICK_LEAVE);
                    if (($model->status == LeaveItem::STATUS_AGREE)) {
                        if (in_array($model->type, $_type) && ($model->hours >= UserLeave::DAY * UserLeave::FLAG)) {
                            $model->doc_flag = LeaveItem::DOC_FLAG_HAVE;
                        }
                        //更新假期余额
                        if (in_array($model->type, array(UserLeave::TYPE_ANNUAL_LEAVE, UserLeave::TYPE_DAYS_OFF))) {
                            //计算剩余假期balance
                            $leaveModel = UserLeave::model()->findByPk($model->leave_id);
                            $leaveModel->balance = $leaveModel->balance - $model->hours;
                            $leaveModel->update();
                        }elseif ($model->type == UserLeave::OT_CONVER_HOLIDAY) {
                            $startYear = date('m', $model->enddate) < 9 ? date('Y', $model->enddate) : date('Y', $model->enddate) + 1;
                            $leaveModel = new UserLeave();
                            $leaveModel->type = UserLeave::TYPE_DAYS_OFF;
                            $leaveModel->staff_uid = $model->staff_uid;
                            $leaveModel->hours = $model->hours;
                            $leaveModel->startdate = $model->enddate;
                            $leaveModel->enddate = strtotime(sprintf(LeaveItem::OT_DURATION, $startYear));
                            $leaveModel->startyear = $startYear - 1;
                            $leaveModel->balance = $model->hours;
                            $leaveModel->flag = $model->id;
                            $leaveModel->created = time();
                            $leaveModel->creator = $this->userInfo->uid;
                            $leaveModel->save();
                        }
                    }
                    if ($model->save()){
                        $data[$id] = $id;
                    }
                }
            }
            $this->addMessage('state', 'success');
            $this->addMessage('message','审核成功！');
            $this->addMessage('callback','checkCallback');
            $this->addMessage('data',$data);
        }
        $this->showMessage();
    }
}