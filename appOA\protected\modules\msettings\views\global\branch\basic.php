<?php
$branches = Branch::model()->with('info')->findAll();
?>
<div class="col-md-1">
    <div class="list-group" id="branch-list">
        <?php
        foreach($branches as $branch):
            echo CHtml::link($branch->abb,'javascript:void(0)', array('branchid'=>$branch->branchid,'class'=>'list-group-item'));
            $branchData[$branch->branchid]['basic'] = $branch->attributes;
            $branchData[$branch->branchid]['basic']['opentime'] = OA::formatDateTime($branch->opentime);
            $branchData[$branch->branchid]['info'] = $branch->info->attributes;
            $branchData[$branch->branchid]['id'] = $branch->branchid;
        endforeach;
        $model = new Branch();
        $model->info = new BranchInfo();

        $branchData['new']['basic'] = $model->attributes;
        $branchData['new']['basic']['opentime'] = '';
        $branchData['new']['info'] = $model->info->attributes;
        $branchData['new']['id'] = '';

        ?>
    </div>
    <div class="text-center">
        <a class="btn btn-primary" id="J_add">
            <span class="glyphicon glyphicon-plus"></span>
            添加
        </a>
    </div>
</div>

<script type="text/template" id="branch-form-template">
    <div class="col-md-6">
        <!--1-->
        <div class="form-group">
                    <?php echo CHtml::label($model->getAttributeLabel('branchid'), CHtml::getIdByName('Branch[branchid]'),array('class'=>"col-sm-2 control-label")) ?>
                    <div class="col-sm-10">
                        <?php echo CHtml::textField('Branch[branchid]', '<%= id %>', array('class'=>"form-control length_4",'encode'=>false,'readonly'=>'readonly')) ?>
                    </div>
        </div>
        <!--2-->
        <div class="form-group">
                    <?php echo CHtml::label($model->getAttributeLabel('title'), CHtml::getIdByName('Branch[title]'),array('class'=>"col-sm-2 control-label")) ?>
                    <div class="col-sm-10">
                        <?php echo CHtml::textField('Branch[title]', '<%= basic.title %>', array('class'=>"form-control length_4",'encode'=>false)) ?>
                    </div>
        </div>
        <!--3-->
        <div class="form-group">
                    <?php echo CHtml::label($model->getAttributeLabel('type'), CHtml::getIdByName('Branch[type]'),array('class'=>"col-sm-2 control-label")) ?>
                    <div class="col-sm-10">
                        <?php
                            echo CHtml::dropDownList('Branch[type]', '',
                                Branch::branchTypes(),
                                array('class'=>'form-control length_4 mr10', 'encode'=>false, 'empty'=>'please select'));
                        ?>
                        <div class="radio" <% if(basic.type != 20){ %>style="display: none;"<%}%>>
                        <?php
                            echo CHtml::radioButtonList('Branch[group]', '',
                            array(10=>'IA', 20=>'IBS', 30=>'MIK'),
                            array('template'=>'<label class="radio-inline">{input}{labelTitle}</label>','separator'=> ' ', 'encode'=>false));
                        ?>
                        </div>
                    </div>
        </div>
        <!--4-->
        <div class="form-group">
                    <?php echo CHtml::activeLabel($model,'city', array('class'=>"col-sm-2 control-label")) ?>
                    <div class="col-sm-10">
                        <?php echo CHtml::dropDownList('Branch[city]','',
                            CHtml::listData(Term::model()->city()->findAll(), 'diglossia_id', function($city){return sprintf('%s (%s)', $city->entitle, $city->cntitle);}),
                            array('class'=>"form-control length_4",'empty'=>'please select')) ?>
                    </div>
        </div>
        <!--5-->
        <div class="form-group">
                    <?php echo CHtml::label($model->info->getAttributeLabel('email'), CHtml::getIdByName('BranchInfo[email]'),array('class'=>"col-sm-2 control-label")) ?>
                    <div class="col-sm-10">
                        <?php echo CHtml::textField('BranchInfo[email]', '<%= info.email %>', array('class'=>"form-control length_4",'encode'=>false)) ?>
                    </div>
        </div>
        <div class="form-group">
                    <?php echo CHtml::label($model->info->getAttributeLabel('tel'), CHtml::getIdByName('BranchInfo[tel]'),array('class'=>"col-sm-2 control-label")) ?>
                    <div class="col-sm-10">
                        <?php echo CHtml::textField('BranchInfo[tel]', '<%= info.tel %>', array('class'=>"form-control length_4",'encode'=>false)) ?>
                    </div>
        </div>
        <!--6-->
        <div class="form-group">
                    <?php echo CHtml::label($model->info->getAttributeLabel('title_cn'), CHtml::getIdByName('BranchInfo[title_cn]'),array('class'=>"col-sm-2 control-label")) ?>
                    <div class="col-sm-10">
                        <?php echo CHtml::textField('BranchInfo[title_cn]', '<%= info.title_cn %>', array('class'=>"form-control length_4",'encode'=>false)) ?>
                    </div>
        </div>

        <div class="form-group">
            <?php echo CHtml::label($model->info->getAttributeLabel('admissions_tel'), CHtml::getIdByName('BranchInfo[admissions_tel]'),array('class'=>"col-sm-2 control-label")) ?>
            <div class="col-sm-10">
                <?php echo CHtml::textField('BranchInfo[admissions_tel]', '<%= info.admissions_tel %>', array('class'=>"form-control length_4",'encode'=>false)) ?>
            </div>
        </div>
        <!--7-->
        <div class="form-group">
                    <?php echo CHtml::label($model->info->getAttributeLabel('address_cn'), CHtml::getIdByName('BranchInfo[address_cn]'),array('class'=>"col-sm-2 control-label")) ?>
                    <div class="col-sm-10">
                        <?php echo CHtml::textArea('BranchInfo[address_cn]', '<%= info.address_cn %>',array('class'=>"form-control","rows"=>5,"encode"=>false)) ?>
                    </div>
        </div>
        <div class="form-group">
                    <?php echo CHtml::label($model->info->getAttributeLabel('interview_address_cn'), CHtml::getIdByName('BranchInfo[interview_address_cn]'),array('class'=>"col-sm-2 control-label")) ?>
                    <div class="col-sm-10">
                        <?php echo CHtml::textArea('BranchInfo[interview_address_cn]', '<%= info.interview_address_cn %>',array('class'=>"form-control","rows"=>5,"encode"=>false)) ?>
                    </div>
        </div>
        <!--8-->
        <div class="form-group">
                    <?php echo CHtml::label($model->info->getAttributeLabel('desc_cn'), CHtml::getIdByName('BranchInfo[desc_cn]'),array('class'=>"col-sm-2 control-label")) ?>
                    <div class="col-sm-10">
                        <?php echo CHtml::textArea('BranchInfo[desc_cn]', '<%= info.desc_cn %>',array('class'=>"form-control","rows"=>5,"encode"=>false)) ?>
                    </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="form-group">
                    <?php echo CHtml::label($model->getAttributeLabel('abb'), CHtml::getIdByName('Branch[abb]'),array('class'=>"col-sm-2 control-label")) ?>
                    <div class="col-sm-10">
                        <?php echo CHtml::textField('Branch[abb]', '<%= basic.abb %>', array('class'=>"form-control length_4",'encode'=>false)) ?>
                    </div>
        </div>
        <div class="form-group">
                    <?php echo CHtml::label($model->info->getAttributeLabel('official_name'), CHtml::getIdByName('BranchInfo[official_name]'),array('class'=>"col-sm-2 control-label")) ?>
                    <div class="col-sm-10">
                        <?php echo CHtml::textField('BranchInfo[official_name]', '<%= info.official_name %>', array('class'=>"form-control length_4",'encode'=>false)) ?>
                    </div>
        </div>
        <div class="form-group">
                    <?php echo CHtml::label($model->getAttributeLabel('opentime'), CHtml::getIdByName('Branch[opentime]'),array('class'=>"col-sm-2 control-label")) ?>
                    <div class="col-sm-10">
                        <?php echo CHtml::textField('Branch[opentime]', '<%= basic.opentime %>', array('class'=>"form-control length_4",'encode'=>false)) ?>
                    </div>
        </div>
        <div class="form-group">
                    <?php echo CHtml::label($model->getAttributeLabel('status'), CHtml::getIdByName('Branch[status]'),array('class'=>"col-sm-2 control-label")) ?>
                    <div class="col-sm-10">
                        <?php
                        echo CHtml::radioButtonList('Branch[status]','',
                            array('10'=>"有效", '99'=>'已关闭'),
                            array('template'=>'<label class="radio-inline">{input}{labelTitle}</label>','separator'=> ' ')
                        );?>
                    </div>
        </div>
        <div class="form-group">
                    <?php echo CHtml::label($model->info->getAttributeLabel('support_email'), CHtml::getIdByName('BranchInfo[support_email]'),array('class'=>"col-sm-2 control-label")) ?>
                    <div class="col-sm-10">
                        <?php echo CHtml::textField('BranchInfo[support_email]', '<%= info.support_email %>', array('class'=>"form-control length_4",'encode'=>false)) ?>
                    </div>
        </div>
        <div class="form-group">
                    <?php echo CHtml::label($model->info->getAttributeLabel('fax'), CHtml::getIdByName('BranchInfo[fax]'),array('class'=>"col-sm-2 control-label")) ?>
                    <div class="col-sm-10">
                        <?php echo CHtml::textField('BranchInfo[fax]', '<%= info.fax %>', array('class'=>"form-control length_4",'encode'=>false)) ?>
                    </div>
        </div>
        <div class="form-group">
                    <?php echo CHtml::label($model->info->getAttributeLabel('title_en'), CHtml::getIdByName('BranchInfo[title_en]'),array('class'=>"col-sm-2 control-label")) ?>
                    <div class="col-sm-10">
                        <?php echo CHtml::textField('BranchInfo[title_en]', '<%= info.title_en %>', array('class'=>"form-control length_4",'encode'=>false)) ?>
                    </div>
        </div>
        <div class="form-group">
            <?php echo CHtml::label($model->info->getAttributeLabel('admissions_email'), CHtml::getIdByName('BranchInfo[admissions_email]'),array('class'=>"col-sm-2 control-label")) ?>
            <div class="col-sm-10">
                <?php echo CHtml::textField('BranchInfo[admissions_email]', '<%= info.admissions_email %>', array('class'=>"form-control length_4",'encode'=>false)) ?>
            </div>
        </div>
        <div class="form-group">
                    <?php echo CHtml::label($model->info->getAttributeLabel('address_en'), CHtml::getIdByName('BranchInfo[address_en]'),array('class'=>"col-sm-2 control-label")) ?>
                    <div class="col-sm-10">
                        <?php echo CHtml::textArea('BranchInfo[address_en]', '<%= info.address_en %>',array('class'=>"form-control","rows"=>5,"encode"=>false)) ?>
                    </div>
        </div>
        <div class="form-group">
                    <?php echo CHtml::label($model->info->getAttributeLabel('interview_address_en'), CHtml::getIdByName('BranchInfo[interview_address_en]'),array('class'=>"col-sm-2 control-label")) ?>
                    <div class="col-sm-10">
                        <?php echo CHtml::textArea('BranchInfo[interview_address_en]', '<%= info.interview_address_en %>',array('class'=>"form-control","rows"=>5,"encode"=>false)) ?>
                    </div>
        </div>
        <div class="form-group">
                    <?php echo CHtml::label($model->info->getAttributeLabel('desc_en'), CHtml::getIdByName('BranchInfo[desc_en]'),array('class'=>"col-sm-2 control-label")) ?>
                    <div class="col-sm-10">
                        <?php echo CHtml::textArea('BranchInfo[desc_en]', '<%= info.desc_en %>',array('class'=>"form-control","rows"=>5,"encode"=>false)) ?>
                    </div>
        </div>
    </div>
</script>


<div class="col-md-10">
    <?php echo CHtml::beginForm('', 'post', array('class'=>'J_ajaxForm form-horizontal')); ?>

    <div class="row" id="form-content">
        <!--place holder-->
    </div>

    <div class="row hidden" id="submit-box">
        <div class="col-md-12">
            <div class="form-group">
                <div class="col-sm-1"></div>
                <div class="col-sm-11">
                    <button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global','Submit');?></button>
                    <a href="javascript:;" onclick="delBranch(this);" class="btn btn-danger" id="del-branch">删除</a>
                    <input type="hidden" id="new" name="new" value="0">
                </div>
            </div>
        </div>
    </div>

    <?php echo CHtml::endForm(); ?>
</div>

<script>
    var branchData=<?php echo CJSON::encode($branchData)?>;
    var currentBranchData;
    var template = _.template($('#branch-form-template').html());
    $(function(){
        showBranchForm=function(obj){
            var _id = $(obj).attr('branchid');
            var _tempData = branchData[_id];
            var view = template(_tempData);
            $("#form-content").html(view);
            if(_tempData.basic.type == 20){
                $('#form-content #Branch_group input[name="Branch[group]"][value="'+_tempData.basic.group+'"]').attr('checked', true);
            }
            $('#form-content #Branch_type option[value="'+_tempData.basic.type+'"]').attr('selected', true);
            $('#form-content #Branch_city option[value="'+_tempData.basic.city+'"]').attr('selected', true);
            $('#form-content #Branch_status input[name="Branch[status]"][value="'+_tempData.basic.status+'"]').attr('checked', true);
            $('#form-content #Branch_opentime').datepicker({'changeMonth':true,'changeYear':true,'dateFormat':'yy-mm-dd'});
            $('#submit-box').removeClass('hidden');
            $('#submit-box #del-branch').data('branchid', _id);
        };
        $('#branch-list').on('click', 'a', function(){
            $('#branch-list a').each(function(){
                $(this).removeClass('background-gray');
            });
            $(this).addClass('background-gray');
            $('#submit-box #new').val(0);
            showBranchForm(this);
        });
        $('#J_add').click(function(){
            $('#branch-list a').each(function(){
                $(this).removeClass('background-gray');
            });
            var _tempData = branchData['new'];
            var view = template(_tempData);
            $("#form-content").html(view);
            $('#form-content #Branch_opentime').datepicker({'changeMonth':true,'changeYear':true,'dateFormat':'yy-mm-dd'});
            $('#Branch_branchid').removeAttr('readonly');
            $('#submit-box').removeClass('hidden');
            $('#submit-box #new').val(1);
        });

        $('#form-content').on('change', '#Branch_type', function(){
            var v = $(this).val();
            if(v == 20){
                $('#Branch_group').parent().show();
            }
            else{
                $('#Branch_group').parent().hide();
            }
        });
    });
    function callback(data)
    {
        var bid = data.data.basic.branchid;
        branchData[bid]=data.data;
        if(data.isNew){
            $('#branch-list').append('<a branchid="'+bid+'" class="list-group-item background-gray" href="javascript:void(0);">'+data.data.basic.abb+'</a>');
            $('#form-content #Branch_branchid').attr('readonly', true);
            $('#del-branch').data('branchid', bid);
        }
        else{
            $('#branch-list a[branchid="'+bid+'"]').html(data.data.basic.abb);
        }
    }
    function delBranch(_this)
    {
        head.load('<?php echo Yii::app()->themeManager->baseUrl;?>/base/js/dialog/dialog.js',function() {
            var $this = $(_this), href = '<?php echo $this->createUrl('delBranch');?>?id='+$this.data('branchid'), msg = $this.data('msg');
            var params = {
                message    : msg ? msg : '确定要删除吗？',
                type    : 'confirm',
                isMask    : false,
                follow    : $this,//跟随触发事件的元素显示
                onOk    : function() {
                    var urlArr = href.split('?');
                    $.post(urlArr[0], urlArr[1], function(data){
                        if(data.state === 'success') {
                            resultTip({msg: data.message, callback: function(){
                                if(data.refresh === true){
                                    if(data.referer) {
                                        location.href = data.referer;
                                    }else {
                                        reloadPage(window);
                                    }
                                }
                            }});
                        }else if( data.state === 'fail' ) {
                            head.dialog.alert(data.message);
                        }
                        if (typeof(data.callback) !== 'undefined'){
                            eval(data.callback+'(data.data)');
                        }
                    }, 'json');
                }
            };
            head.dialog(params);
        });
    }
    function delCB(data)
    {
        $('#branch-list a[branchid="'+data.id+'"]').remove();
        $('#form-content').html('');
        $('#submit-box').addClass('hidden');
    }
</script>