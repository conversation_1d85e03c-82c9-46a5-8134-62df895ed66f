<?php echo $this->renderPartial('steps_header', array('currentStep'=>0)); ?>
<?php if (in_array($this->regStudent->status, array(2, 3))) { ?>
    <div class="container pt100">
        <div class="step_icon step_success"><i class="glyphicon glyphicon-ok-circle"></i></div>
        <div class="alert alert-success text-center mt-3">
           <a href="#" class="alert-link"><?php echo Yii::t('reg', 'Congratulation! You have successfully submitted your information.'); ?></a>
        </div>
    </div>
<?php } else { ?>
    <div class="container pt100">
        <div class="alert alert-warning text-center mt-3">
            <?php //echo Yii::t('reg', 'Error! Please fill in the information as needed.'); ?>
            <?php echo Yii::t('reg', 'Please complete highlited steps'); ?>
        </div>
        <div class="mt-3">
            <ul class="list-group step" style="font-size: 12px;">
        <?php 
            foreach ($this->steps as $k=>$step):
                $class = 'error_span';
                if (isset($this->studentSteps[$step])) {
                    if ($this->studentSteps[$step] >= 0) {
                        $class = 'success_span';
                    }
                }

                $config = RegExtraInfo::getStepConfig();
        ?>
            <a href="<?php echo $this->createUrl('step'.$step);?>"  class="list-group-item">
                <span class="shadow-sm step-no align-middle rounded-circle <?php echo $class; ?>"><?php echo $k+1; ?></span><?php echo $config[$step]; ?>
            </a>
        <?php endforeach; ?>
            </ul>
        </div>
    </div>
<?php  } ?>

<style>
    .step .list-group-item {
        border: 0;
        padding: 0.5rem 1rem;
        background-color:#eef3f6
    }
    .success_span {
        display: inline-block;
        width: 30px;
        height: 30px;
        color: #fff;
        background: linear-gradient(to right, #61ba6d, #83c331);
        margin-right: 0.5em;
        text-align: center;
        line-height: 30px;
    }

    .error_span {
        display: inline-block;
        width: 30px;
        height: 30px;
        color: #fff;
        background: linear-gradient(to right, #ff0000, #ff0000);
        margin-right: 0.5em;
        text-align: center;
        line-height: 30px;
    }
</style>



