<?php
class VacationMailCommand extends CConsoleCommand{
    public function actionIndex()
    {
        echo $this->getHelp();
	}

    public function actionVacationMail($school_id = '')
    {
        Yii::import('common.models.attendance.*');
        Yii::app()->language = 'zh_cn';

        $vacationTimeData = date("Y-m-d", time());
        $vacationTimestart = strtotime($vacationTimeData);
        if($vacationTimestart) {
            $criteria = new CDbCriteria();
            $criteria->compare('t.vacation_time_start', "<=$vacationTimestart");
            $criteria->compare('t.vacation_time_end', ">=$vacationTimestart");
            $criteria->compare('t.type', ChildVacation::VACATION_SICK_LEAVE);
            $criteria->compare('t.stat', ChildVacation::STATUS_CHECKED);
            if($school_id){
                $criteria->compare('t.school_id', $school_id);
            }
            $vacation = ChildVacation::model()->findAll($criteria);

            $info = array();
            if (isset($vacation)) {
                foreach ($vacation as $val) {
                    $classids[$val->class_id] = $val->class_id;
                    $track = array();
                    if (isset($val->tracks)) {
                        foreach ($val->tracks as $item) {
                            $track[$item->track_type][] = array(
                                'memo' => $item->track_memo,
                                'userName' => $item->user->getName(),
                                'time' => date("H:i", $item->track_at)
                            );
                        }
                    }
                    $info{$val->school_id}[$val->class_id][$val->child_id] = array(
                        'childName' => $val->childProfile->getChildName(),
                        'track' => $track,
                    );
                }

                $crit = new CDbCriteria();
                $crit->compare('classid', $classids);
                $crit->order = 'child_age ASC, title ASC';
                $classInfo = IvyClass::model()->findAll($crit);
                $schoolClass = array();
                foreach ($classInfo as $class) {
                    $schoolClass[$class->schoolid][] = array(
                        'classid' => $class->classid,
                        'title' => $class->title,
                    );
                }
                $criter = new CDbCriteria();
                $criter->compare('branchid', array_keys($info));
                $sModel = Branch::model()->findAll($criter);
                $bInfo = array();
                foreach ($sModel as $model) {
                    $bInfo[$model->branchid] = $model->title;
                }

                foreach ($info as $school_id => $item) {
                    $mailer = Yii::createComponent('common.extensions.mailer.EMailer');
                    $mailer->Subject = $bInfo[$school_id] . ' ' . $vacationTimeData . " 病假记录";
                    $mailer->pushTo('vacation', $school_id);
                    $mailer->getCommandView('sickTrack', array(
                        'item' => $item,
                        'classInfo' => $schoolClass[$school_id],
                    ), 'main');
                    $isProduction = defined("IS_PRODUCTION") ? IS_PRODUCTION : false;
                    $mailer->iniMail($isProduction);
                    if ($mailer->Send()) {
                        echo $school_id . " - success \n";
                    } else {
                        echo $school_id . " - error \n";
                    }
                }
            } else {
                echo "No sick leave";
            }
        }else {
            echo "No sick leave";
        }
    }
}
