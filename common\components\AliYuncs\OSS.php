<?php
require(dirname(__FILE__).'/aliyuncs/autoload.php');
require_once __DIR__ . '/Config.php';
use OSS\OssClient;
use OSS\Core\OssException;

class OSS {
    public $oss;
    public $bucket = 'ivy-www-uploads';
    public $timeout = 3600;

    const endpoint = Config::OSS_ENDPOINT;
    const accessKeyId = Config::OSS_ACCESS_ID;
    const accessKeySecret = Config::OSS_ACCESS_KEY;
    const bucket = Config::OSS_TEST_BUCKET;

    public function __construct($bucket=null, $timeout=null) {
        if(!is_null($bucket)){
            $this->bucket = $bucket;
        }        
        if(!is_null($timeout)){
            $this->timeout = $timeout;
        }
        $this->oss = $this->getOssClient();
    }

    /**
     * 根据Config配置，得到一个OssClient实例
     *
     * @return OssClient 一个OssClient实例
     */
    public static function getOssClient()
    {
        try {
            $ossClient = new OssClient(self::accessKeyId, self::accessKeySecret, self::endpoint, false);
        } catch (OssException $e) {
            printf(__FUNCTION__ . "creating OssClient instance: FAILED\n");
            printf($e->getMessage() . "\n");
            return null;
        }
        return $ossClient;
    }

    // 获取带样式的图片
    public function getImageUrl($object, $style="", $timeout=3600)
    {
        if (!$object) {
            return false;
        }
        $options = array();
        if ($style) {
            $options = array(
                OssClient::OSS_PROCESS => $style,
                );
        }

        // try {
        //     $ossClient = new OssClient(self::accessKeyId, self::accessKeySecret, self::endpoint, false);
        // } catch (OssException $e) {
        //     printf(__FUNCTION__ . "creating OssClient instance: FAILED\n");
        //     printf($e->getMessage() . "\n");
        //     return null;
        // }
        $signedUrl = $this->oss->signUrl($this->bucket, $object, $timeout, "GET", $options);
        return $signedUrl;
    }

    /**
     * 上传文件
     * @param  [type] $fileName       [oss 存储路径]
     * @param  [type] $uploadRootPath [本地文件路径]
     * @return [type]                 [description]
     */
    public function uploadFile($fileName, $uploadRootPath)
    {
        try {
            $res = $this->oss->uploadFile($this->bucket, $fileName, $uploadRootPath);
        } catch (OssException $e) {
            $error = $e->getMessage();
            return false;
        }
        return true;
    }
} 
