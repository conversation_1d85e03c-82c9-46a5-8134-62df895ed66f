<?php
/**
 * 收费正常相关
 */


class PolicyApi{

    public $constantFlag;
    private $schoolId;

    function __construct( $schoolId = null) {
        $this->schoolId = $schoolId;
        $constantFiles = require('constants/default.php');
        $this->constantFlag = ( in_array($schoolId, array_keys($constantFiles)) ) ? $constantFiles[$schoolId] : $constantFiles['default'];
        require('constants/'.$this->constantFlag.'.php');
    }

	/**
	 * Summary
	 * @param	String	$branchId	e.g. BJ_OE, BJ_OG
	 * @param	Number	$startYear	e.g. 2013, 2014
	 * @return	返回工作日			array('201309'=>array(2,3,4...), '201310'=>(1,2,3...)...)
	 * to do : 加入缓存
	 */
	public static function getSchoolMonth($branchId='', $startYear=0){
		if(empty($branchId) || empty($startYear)){
			return null;
		}

		Yii::import('common.models.calendar.*');
		$crit = new CDbCriteria;
		$crit->compare('branchid', $branchId);
		$crit->compare('startyear', $startYear);
		$schoolCalendar = CalendarSchool::model()->with('months')->find($crit);
		$result = array();
		foreach($schoolCalendar->months as $month){
			$key = str_replace("-", "", $month->month_label);
			$result[$key] = explode(",", $month->schoolday_array);
		}
		ksort($result);
		return $result;
	}

	public static function getAvailableYears($branchId='',$schoolyear =0)
	{
		$configs = array();
		$criteria = new CDbCriteria();
		$criteria->select = 'startyear,is_selected';
		$criteria->compare('branchid', $branchId);
		$criteria->order = 'startyear ASC';
		$calendarList = CalendarSchool::model()->findAll($criteria);
		if (!empty($calendarList))
		{
			foreach ($calendarList as $val)
			{
                if ($schoolyear){
                    if ($val->startyear>=$schoolyear){
                        $configs[] = $val->startyear;
                    }
                }else{
                    if ($val->is_selected == 1)
                    {
                        $startYear = $val->startyear;
                        $configs[] = $startYear;
                    }
                    if (isset($startYear) && ($val->startyear > $startYear))
                    {
                        $configs[] = $val->startyear;
                    }
                }

			}
		}
		return $configs;
	}

	/**
	 * Summary
	 * @param	String	$schoolId	Description
	 * @param	Number	$startYear	Description
	 * @param	string	$byType		BY_MONTH, BY_ANNUAL, BY_SEMESTER1, BY_SEMESTER2
	 * @param	Object	$types		array(FEETYPE_MATERIAL, FEETYPE_DEPOSIT, FEETYPE_SCHOOLBUS, FEETYPE_CAROUSEL, FEETYPE_AFTERSCHOOL)
	 * @return	Object				Description
	 */
	public function getPaymentAmounts($startYear=0, $types=array(), $byType, $childId=0){
        $schoolId = $this->schoolId;
		if(empty($schoolId) || empty($startYear) || !count($types)){
			return null;
		}

		$dbFeeAllTypes = array(FEETYPE_MATERIAL, FEETYPE_DEPOSIT, FEETYPE_SCHOOLBUS, FEETYPE_CAROUSEL, FEETYPE_AFTERSCHOOL, FEETYPE_BREAKFAST);
		$configTypes = array_diff($types, $dbFeeAllTypes);

		$result = null;
		if(!empty($configTypes)){
			$feeConfig = new IvyPolicy('pay', $startYear, $schoolId);
			// 获取配置中的缴费金额
			$result = $feeConfig->getConfigedFeeAmounts($configTypes,$startYear,$byType,$childId);
		}

		$types = array_intersect($types, $dbFeeAllTypes);
		if(!empty($types)){
			global $paymentMappings;

			$dbFeeType = array_flip($paymentMappings['dbFeeType']);
			Yii::import('common.models.calendar.CalendarSchool');
			Yii::import('common.models.FeemgtFeeOther');
			//获取某一学年校历的主键
			$calendarId = CalendarSchool::model()->getCalendarId($schoolId,$startYear);
			$types = array_intersect($dbFeeType,$types);
			if (in_array(FEETYPE_AFTERSCHOOL, $types))
			{
				$calendarId = array(0,$calendarId);
			}
			$criteria=new CDbCriteria;
			$criteria->compare('type', array_keys($types));
			$criteria->compare('yid', $calendarId);
			$criteria->compare('branchid', $schoolId);
			$criteria->compare('status', 0);
			$feeOther = FeemgtFeeOther::model()->findAll($criteria);
            // 优惠政策
			if (!empty($feeOther))
			{
				foreach ($feeOther as $value)
				{
					$result[$dbFeeType[$value->type]][$value->id] = array('cn_title'=>$value->cn_title,'en_title'=>$value->en_title,'amount'=>$value->amount);
				}
			}
		}
		return $result;
	}



	/**
	 * 查询某学校在使用的折扣信息
	 * @param	String	$schoolId	Description
	 * @return	array				Description
	 */
	static function getDiscountList($schoolId=''){
		$result = array();
		if (empty($schoolId))
		{
			return $result;
		}
		Yii::import('common.models.invoice.DiscountSchool');
		Yii::import('common.models.invoice.DiscountCategory');
		$criteria = new CDbCriteria;
		$criteria->select='t.id';
		$criteria->compare('t.schoolid', $schoolId);
		$criteria->compare('t.stat', 10);
		$criteria->compare('t.flag', 0);
		$criteria->compare('t.mailto', 0);
		$criteria->order = "t.discount DESC";
		$criteria->addCondition('expire_date>'.strtotime('today'));
		$model = DiscountSchool::model()->with('discountTitle')->findAll($criteria);
		if (!empty($model))
		{
			foreach ($model as $value)
			{
				$result[$value->id] = $value->discountTitle->title_cn;
			}
		}
		return $result;
	}


	/**
	* 该方法只能由本机的 http api中调用
	* @param	Object	$childId	孩子ID
	* @param	Object	$targetDate	目标日期
	* @return	Object				result;
	*/

	public function cancelLunch($childId, $classId, $schoolId, $startYear, $targetDate,$operate ='DO',$userId=0)
	{
		//加载退费配置
		$IvyPolicy = new IvyPolicy('refund', $startYear, $schoolId);
		$calendarId = $IvyPolicy->getYid();
        if (!$calendarId){
             return RC::getTip(RC::E_LUNCH_CANCEL_DATE);
        }

        //判断不同权限所取消餐的范围
        $dayStamp = strtotime($targetDate);
        if (Yii::app()->user->getId() && $userId === 0){
            $roles = array_keys(Yii::app()->authManager->getRoles(Yii::app()->user->id));
            if (Yii::app()->user->checkAccess('oSuperAdmin') || in_array('ivystaff_opschool', $roles)){
                if (in_array('ivystaff_opschool', $roles)){
                    $prevMonth = mktime(0,0,0,date('m')-6,1,date('Y'));
                    if ($dayStamp < $prevMonth){
                        return RC::getTip(RC::E_NO_PERMISSION);
                    }
                }
            }
            else{
                return RC::getTip(RC::E_NO_PERMISSION);
            }
        }
        else{
            //没登录 家长取消
            $currentDateTime = mktime(0,0,0,date('m',time()),  date('d',time()),date('Y',time()));
            if ($dayStamp<$currentDateTime){
                return RC::getTip(RC::E_LUNCH_CANCEL_EXPIRED);
            }elseif($dayStamp == $currentDateTime){
                if (time()>$currentDateTime+9*3600){
                    return RC::getTip(RC::E_LUNCH_CANCEL_EXPIRED);
                }
            }
        }
		//判断是否为教学天数
		$schoolDay = array();
		$schoolDayArr = CalendarSchoolDays::model()->getCalendarSchoolDay($calendarId, $dayStamp, $dayStamp);
		foreach ($schoolDayArr as $item) {
			$dayList = ($item->schoolday_array) ? explode(',', $item->schoolday_array) : array();
			if ($dayList) {
				$schoolDay = array_merge($schoolDay, $dayList);
			}
		}
		if (!in_array(date('d', $dayStamp), $schoolDay))
		{
			return RC::getTip(RC::E_LUNCH_CANCEL_NOT_SCHOOLDAY);
		}
		//判断当天孩子是否吃饭
		$childServArr = ChildServiceInfo::model()->getChildFeeInfo($childId, 'lunch', $calendarId, $dayStamp, true);
		if (is_array($childServArr) && count($childServArr))
		{
			$d = (date('D', $dayStamp));
			foreach ($childServArr as $ca)
			{
				$weekArr = array('Mon' => $ca->mon, 'Tue' => $ca->tue, 'Wed' => $ca->wed, 'Thu' => $ca->thu, 'Fri' => $ca->fri, 'Sat' => 10, 'Sun' => 10);
				if (!in_array(intval($weekArr[$d]), array(10, 30)))
				{
					return RC::getTip(RC::E_LUNCH_CANCEL_NOT_PAID);
				}
				$invoice_id = $ca->invoice_id;
			}
		}
		else
		{
			return RC::getTip(RC::E_LUNCH_CANCEL_NOT_PAID);
		}
		//判断取消的午餐天是否在工作流退费审批中
		$refundCount = InvoiceChildRefund::model()->getCancleLunchExist($childId, $dayStamp);
		if ($refundCount)
		{
			return RC::getTip(RC::E_LUNCH_CANCEL_IN_WORKFLOW);
		}

		//判断（取消OR恢复）
		$refundObj = RefundLunch::model()->getChildLunch($childId, $dayStamp);
		if (strtoupper($operate) == 'DO')
		{
			if ($refundObj == null)
			{
				//get school lunch config
				unset($weekArr['Sat']);
				unset($weekArr['Sun']);
				$amount = 0;

				Yii::import('common.models.invoice.InvoiceLunchUnit');
				$lunchUnitModel = InvoiceLunchUnit::model()->findByAttributes(array('invoice_id' => $invoice_id));

				if ($lunchUnitModel) {
					$amount = $lunchUnitModel->refund;
				} elseif (isset($IvyPolicy->configs[LUNCH_PERDAY])) {
					if (is_array($IvyPolicy->configs[LUNCH_PERDAY])){
						$invoiceObj = Invoice::model()->findByPk($invoice_id);
						if (!$invoiceObj) {
							return RC::getTip(RC::E_LUNCH_CANCEL_NO_REFUND_CONFIG);
						}
						$lunchType = $invoiceObj->fee_program;
						if (isset($IvyPolicy->configs[LUNCH_PERDAY][$lunchType])) {
							$amount = $IvyPolicy->configs[LUNCH_PERDAY][$lunchType];
						}elseif(isset($IvyPolicy->configs[LUNCH_PERDAY_OLD])){
                            $amount = $IvyPolicy->configs[LUNCH_PERDAY_OLD];
                        }else{
                            return RC::getTip(RC::E_LUNCH_CANCEL_NO_REFUND_CONFIG);
                        }
					}else{
					   $amount = $IvyPolicy->configs[LUNCH_PERDAY];
					}
				} else {
					return RC::getTip(RC::E_LUNCH_CANCEL_NO_REFUND_CONFIG);
				}

                // if (isset($IvyPolicy->configs[LUNCH_PERDAY])){
                //     if (is_array($IvyPolicy->configs[LUNCH_PERDAY])){
                //         $key = current(array_keys($IvyPolicy->configs[LUNCH_PERDAY]));
                //         if ($targetDate >= $key){
                //             $amount = $IvyPolicy->configs[LUNCH_PERDAY][$key][1];
                //         }else{
                //             $amount = $IvyPolicy->configs[LUNCH_PERDAY][$key][0];
                //         }
                //     }else{
                //        $amount = $IvyPolicy->configs[LUNCH_PERDAY];
                //     }
                // }else{
                //     return RC::getTip(RC::E_LUNCH_CANCEL_NO_REFUND_CONFIG);
                // }

				$refundModel = new RefundLunch();
				$refundModel->yid = $calendarId;
				$refundModel->schoolid = $schoolId;
				$refundModel->classid = $classId;
				$refundModel->childid = $childId;
				$refundModel->target_date = date('Ymd',$dayStamp);
				$refundModel->target_timestamp = $dayStamp;
				$refundModel->amount = $amount;
				$refundModel->userid = $userId;
				$refundModel->updated_timestamp = time();
				$refundModel->child_credit_id = 0;
				$refundModel->operator_uid = 0;
				$refundModel->operate_timestamp = 0;
				if ($refundModel->save())
				{
					return RC::E_NO_ERROR;
				}
				else
				{
					return RC::getTip(RC::E_LUNCH_CANCEL_OTHER);
				}
			}
			else
			{
				return RC::getTip(RC::E_LUNCH_CANCEL_ALREADY_DONE);
			}
		}
		elseif (strtoupper($operate) == 'UNDO')
		{
			if (is_object($refundObj) && count($refundObj))
			{
				if ($refundObj->child_credit_id || $refundObj->operator_uid)
				{
					return RC::getTip(RC::E_LUNCH_CANCEL_REFUNDED);
				}
				else
				{
					if (RefundLunch::model()->deleteByPk($refundObj->id))
					{
						return RC::E_NO_ERROR;
					}
					else
					{
						return RC::getTip(RC::E_LUNCH_CANCEL_OTHER);
					}
				}
			}
			else
			{
				return RC::getTip(RC::E_LUNCH_CANCEL_UNCANCELLED);
			}
		}
		else
		{
			return RC::getTip(RC::E_LUNCH_CANCEL_OTHER);
		}
	}

    /**
     * 把数据库中的Invoice的费用类型渲染为可读文字
     * @param	Object	$dbValue	Description
     * @param	Object	$lang		Description
     * @return	Object				Description
     */
    public function renderFeeType($dbValue, $lang=null){
        global $paymentMappings;
//        var_dump($paymentMappings);
        $tmp = array_flip($paymentMappings['dbFeeType']);
        return Yii::t("payment", $paymentMappings['titleFeeType'][$tmp[$dbValue]], array(), null, $lang);
    }

    public function renderInvoiceStatus($dbValue, $lang=null, $withWrap=false){
        global $paymentMappings;
        $status = Yii::t("payment", $paymentMappings['invoiceStatus'][$dbValue], array(), null, $lang);
        if($withWrap){
            if( in_array($dbValue, array(Invoice::STATS_UNPAID, Invoice::STATS_PARTIALLY_PAID)) ){
                $status = CHtml::openTag('span', array('class'=>'red')) . $status . CHtml::closeTag('span');

            }
        }
        return $status;
    }

    public function renderRefundStatus($dbValue, $lang=null){
        global $paymentMappings;
        return Yii::t("invoice", $paymentMappings['refundStatus'][$dbValue], array(), null, $lang);
    }

	/**
	 * 把账单里的服务类型，比如3半天、2半天之类的信息显示出来
	 * @param	Object	$dbValue	Description
	 * @param	Object	$feetype	Description
	 * @return	Object				Description
	 */
	public function renderServiceInfo($dbValue, $feetype){
		$week = unserialize($dbValue);
		$html = CHtml::openTag("div", array("class"=>"service-day"));
		foreach($week as $day){
			switch($feetype){
				case 'tuition':
					$classflag = ($day == 30)? 20: $day;
					break;
				case 'lunch':
					$classflag = ($day == 20) ? 40: $day;
					break;
			}
			$html .= CHtml::openTag("i", array("class"=>"day-".$classflag));
			$html .= CHtml::closeTag("i");
		}
		$html .= CHtml::closeTag("div");
		return $html;
	}

	public function renderPaymentType($dbValue, $lang=null){
		global $paymentMappings;
		return Yii::t("invoice", $paymentMappings['transactionType'][$dbValue], array(), null, $lang);
	}


	const E_CANCEL_INVOICE_SUCCESS = 0;
	const E_CANCEL_INVOICE_OTHER = 1;

	static function cancelInvoice($invoiceId=0)
	{
		if ($invoiceId)
		{
//			global $paymentMappings;
//			$dbFeeType = array_flip($paymentMappings['dbFeeType']);
			$model = Invoice::model()->findByPk($invoiceId);
			if ($model->status==Invoice::STATS_PAID || $model->status==Invoice::STATS_PARTIALLY_PAID || $model->status==Invoice::STATS_CANCELLED  || $model->status==Invoice::STATS_AWAITING_DISCOUNT)
			{
				return self::E_CANCEL_INVOICE_OTHER;
			}
			$model->status=Invoice::STATS_CANCELLED;
			$model->send_timestamp = time();
			if ($model->save())
			{
			    Yii::log($invoiceId.': '.Yii::app()->user->id, CLogger::LEVEL_INFO, 'invoice.cancel');
				//预缴学费
				if ($model->amount != $model->original_amount)
				{
					TemporaryDeposit::model()->deleteByPk($model->invoice_id);
				}
				//代收代缴
				if ($model->flag)
				{
					$flagModel = Invoice::model()->findByPk($model->flag);
					if (!empty($flagModel) && $flagModel->status == Invoice::STATS_UNPAID)
					{
						$flagModel->status = Invoice::STATS_CANCELLED;
						$flagModel->send_timestamp = time();
						$flagModel->save();
					}
				}
				return self::E_CANCEL_INVOICE_SUCCESS;
			}
		}
	}

	static function getPlandate($startDate)
	{
		if (!$startDate){
			return false;
		}
		$plantime = date('Ymd', mktime(0, 0, 0, date('m', $startDate)-1, MonthlyFeeTask::model()->planDay, date('Y', $startDate) ));
		$today = date('Ymd');
		return $plantime < $today ? $today : $plantime;
	}

	/**
	 * 批量分班、操作孩子状态
	 * @param array $childId	孩子主键		e.g. array(1392)
	 * @param int $classId		班级主键		e.g. 100
	 * @param string $flag		操作			e.g. current,next
	 * @param int $status		状态			e.g. ChildProfileBasic::STATS_ACTIVE,ChildProfileBasic::STATS_GRADUATED,ChildProfileBasic::STATS_DROPOUT
	 */
	public function changeChildStatus($childId=array(),$flag=null,$classId=0,$status=0)
	{
		if (count($childId) && in_array($flag, array('current','newreg')) && in_array($status, array(ChildProfileBasic::STATS_REGISTERED,ChildProfileBasic::STATS_GRADUATED,ChildProfileBasic::STATS_DROPOUT)))
		{
			//当前学年
			Yii::import('common.models.portfolio.ChildStudyHistory');
			Yii::import('common.models.invoice.ChildConstantTuition');
            Yii::import('common.models.invoice.ChildInactiveUnpaid');
			switch ($status)
			{
				case ChildProfileBasic::STATS_REGISTERED:
					$classModel = IvyClass::model()->findByPk($classId);
					$values = array
					(
    						'schoolid'=>$classModel->schoolid,
    						'classid'=>$classId,
    						'calendar'=>$classModel->yid,
    						'semester'=>0,
    						'stat'=>ChildProfileBasic::STATS_ACTIVE,
    						'userid'=>Yii::app()->user->id,
					);

                    ChildReserve::model()->deleteAllByAttributes(array('childid'=>$childId, 'schoolid'=>$this->schoolId));
					foreach ($childId as $val)
					{
						$historyModel = new ChildStudyHistory;
						$historyModel->setAttributes($values);
						$historyModel->childid = $val;
						$historyModel->timestamp = time();
						$historyModel->from_classid =0;
						if ($historyModel->save())
						{

							$reserveModel = new ChildReserve;
							$reserveModel->setAttributes($values);
							$reserveModel->childid = $val;
							$reserveModel->hid = $historyModel->hid;
							$reserveModel->updated_timestamp = time();
							$reserveModel->save();
						}
					}
					return array('status'=>true);
					break;
				case ChildProfileBasic::STATS_GRADUATED:
				case ChildProfileBasic::STATS_DROPOUT:
					//老生老办法表删除
    				ChildConstantTuition::model()->deleteAllByAttributes(array('childid'=>$childId));
					$unInvoice = $this->getChildUnInvoices($childId,0,array(FEETYPE_TUITION,FEETYPE_LUNCH,FEETYPE_CHILDCARE,FEETYPE_SCHOOLBUS,FEETYPE_COLLECTION));
					if (!empty($unInvoice))
					{
						//return array('status'=>false,'childIds'=>array_keys($unInvoice));
                         foreach ($unInvoice as $key => $val) {
                            $unpaidModel = ChildInactiveUnpaid::model()->findByPk($key);
                            if (empty($unpaidModel)){
                                $unpaidModel = new ChildInactiveUnpaid();
                                $unpaidModel->childid = $key;
                            }
                            $unpaidModel->schoolid = $val->schoolid;
                            $unpaidModel->created = time();
                            $unpaidModel->updated = time();
                            $unpaidModel->save();
                        }
					}
					$branchList = Branch::model()->getBranchList(null,true);
					ChildReserve::model()->deleteAllByAttributes(array('childid'=>$childId, 'schoolid'=>$this->schoolId));
					foreach ($childId as $val)
					{
						$profileModel = ChildProfileBasic::model()->findByPk($val);
						$profileModel->status = $status;
						if ($profileModel->save())
						{
							if ($flag == 'current')
							{
								$historyModel = new ChildStudyHistory;
								$historyModel->childid = $val;
								$historyModel->schoolid = $profileModel->schoolid;
								$historyModel->classid = $profileModel->classid;
								$historyModel->calendar = $branchList[$profileModel->schoolid]['yid'];
								$historyModel->semester = 0;
								$historyModel->stat = $status;
								$historyModel->timestamp = time();
								$historyModel->userid = Yii::app()->user->id;
								$historyModel->save();
								$linkModel = ChildClassLink::model()->findByPk($val);
								if (!empty($linkModel))
								{
									$linkModel->stat = $status;
									$linkModel->updated_timestamp = time();
									$linkModel->userid = Yii::app()->user->id;
									$linkModel->hid = $historyModel->hid;
									$linkModel->save();
								}
							}
						}

					}
					return array('status'=>true);
					break;
			}
		}
	}

	/**
	 * 分配班级
	 * @param	array	$classes		班级主键		e.g.110当前学年班级 210未来学年班级  array(110,210)
	 * @param	int		$childId		孩子主键
	 * @return	boolean
	 */
	public static function assignClass($classes,$childId)
	{
		$childObj = self::getChildObj($childId);
		$schoolId = $childObj->schoolid;
		if (count($classes))
		{
			$branchObj = Branch::model()->findByPk($schoolId);
			foreach ($classes as $val)
			{
				$classObj = IvyClass::model()->findByPk($val);
				if (!empty($classObj))
				{
					$values = array
					(
    						'schoolid'=>$classObj->schoolid,
    						'classid'=>$val,
    						'calendar'=>$classObj->yid,
    						'semester'=>0,
    						'stat'=>ChildProfileBasic::STATS_ACTIVE,
    						'userid'=>Yii::app()->user->id,
					);
					//判断是当前学年
					$studyHistory = new ChildStudyHistory;
					$studyHistory->setAttributes($values);
					$studyHistory->childid = $childId;
					$studyHistory->timestamp = time();
					$studyHistory->from_classid =0;
					$studyHistory->save();
					if ($classObj->yid == $branchObj->schcalendar)
					{
//						ChildProfileBasic::model()->updateByPk($childId,array('status'=>ChildProfileBasic::STATS_ACTIVE,'classid'=>$val));

						$classLink = ChildClassLink::model()->findByPk($childId);
						if (empty($classLink))
						{
							$classLink = new ChildClassLink;
						}else{
                            ChildStudyHistory::model()->updateByPk($studyHistory->hid, array('from_classid'=>$childObj->classid));
                            //更新已付帐单、已经生成计划表数据（时间包括转班日期及以后帐单）
                            self::updateTransferedClassData($childId, $val, $childObj->classid);
                        }
						$classLink->setAttributes($values);
						$classLink->childid = $childId;
						$classLink->hid = $studyHistory->hid;
						$classLink->updated_timestamp = time();
						$classLink->save();
                        //ChildProfileBasic
                        $childObj->status=ChildProfileBasic::STATS_ACTIVE;
                        $childObj->classid=$val;
                        $childObj->save(false);
					}
					else
					{
						$childReserve = ChildReserve::model()->find('childid=:childId',array(':childId'=>$childId));
						if (empty($childReserve))
						{
							$childReserve = new ChildReserve;
						}
                        else
                        {
                            ChildStudyHistory::model()->updateByPk($studyHistory->hid, array('from_classid'=>$childReserve->classid));
                            //更新已付帐单、已经生成计划表数据（时间包括转班日期及以后帐单）
                            self::updateTransferedClassData($childId, $val, $childReserve->classid);
                        }
						$childReserve->setAttributes($values);
						$childReserve->childid = $childId;
						$childReserve->hid = $studyHistory->hid;
						$childReserve->updated_timestamp = time();
						$childReserve->save();
					}
				}
			}
			return true;
		}
		return false;
	}


	/**
	 * 转班
	 * @param int $childId
	 * @param int $classId
	 * @param int $fromClassId
	 * @return boolean
	 */
	public static function transferClass($childId=0,$classId=0,$fromClassId=0)
	{
		if ($childId && $classId && $fromClassId)
		{
			$class = IvyClass::model()->findByPk($classId);
			$fromClass = IvyClass::model()->findByPk($fromClassId);
			if (!empty($class) && !empty($fromClass) && $class->yid == $fromClass->yid && $class->schoolid == $fromClass->schoolid)
			{
				if (!self::getChildUnInvoices($childId,$class->yid,array(FEETYPE_TUITION,FEETYPE_LUNCH,FEETYPE_CHILDCARE,FEETYPE_SCHOOLBUS)))
				{
					$values = array
					(
    						'schoolid'=>$class->schoolid,
    						'classid'=>$classId,
    						'calendar'=>$class->yid,
    						'semester'=>0,
    						'stat'=>ChildProfileBasic::STATS_ACTIVE,
    						'userid'=>Yii::app()->user->id,
					);
					$studyHistory = new ChildStudyHistory;
					$studyHistory->setAttributes($values);
					$studyHistory->childid = $childId;
					$studyHistory->timestamp = time();
					$studyHistory->from_classid =$fromClassId;
					if ($studyHistory->save())
					{
						ChildProfileBasic::model()->updateByPk($childId,array('classid'=>$classId));
						$classLink = ChildClassLink::model()->findByPk($childId);
						if (empty($classLink))
						{
							$classLink = new ChildClassLink;
						}
						$classLink->setAttributes($values);
						$classLink->childid = $childId;
						$classLink->hid = $studyHistory->hid;
						$classLink->updated_timestamp = time();
						$classLink->save();
						return true;
					}
				}
			}
		}
		return false;
	}

    /**
	 * 更新已转班孩子遗留数据班级不正确函数
	 * @param int $childId		孩子主键
	 * @param int $classId      班级主键
	 * @return boolean
	 */
    public static function updateTransferedClassData($childId,$classId,$fromClassId){
         Yii::import('common.models.invoice.*');
        $today = strtotime(date('Ymd'));
        $invoiceList = Invoice::model()->findAll('childid=:childid and classid=:classid and ((startdate<=:currenttime and enddate>=:currenttime) or enddate<:currenttime)',
                                                    array(':childid' => $childId, ':classid' => $fromClassId, ':currenttime' =>$today));
        if (!empty($invoiceList)) {
            $invoiceIds = array();
            foreach ($invoiceList as $val){
                Invoice::model()->updateByPk($val->invoice_id, array('classid'=>$classId));
                if ($val->status == Invoice::STATS_PAID){
                    InvoiceTransaction::model()->updateAll(array('classid'=>$classId), 'childid=:childid and invoice_id=:invoice_id', array(':invoice_id'=>$val->invoice_id,':childid'=>$childId));
                    ChildServiceInfo::model()->updateAll(array('classid'=>$classId),'childid=:childid and invoice_id=:invoice_id', array(':invoice_id'=>$val->invoice_id,':childid'=>$childId));
                }
            }
        }
        //更新计划表
        $today = date('Ym');
        $taskList = MonthlyFeeTask::model()->findAll('childid=:childid and classid=:classid and month>=:currenttime',
                                                    array(':childid' => $childId, ':classid' => $fromClassId, ':currenttime' => $today));
        if (!empty($taskList)){
            foreach ($taskList as $val){
                MonthlyFeeTask::model()->updateByPk($val->id, array('classid'=>$classId));
            }
        }
        return true;
    }

    /**
	 * 判断孩子是否有未付账单
	 * @param int $childId		孩子主键
	 * @param int $calanderId	校历主键
	 * @param array $feeType	费用类型		e.g. array(FEETYPE_TUITION,FEETYPE_LUNCH,FEETYPE_CHILDCARE,FEETYPE_SCHOOLBUS)
	 * @return boolean
	 */
	public function getChildUnInvoices($childId=0,$calanderId=0,$feeType=array())
	{
		if ($childId)
		{
			global $paymentMappings;
			$dbfeeType = $paymentMappings['dbFeeType'];
			$paymentList = array();
			if (count($feeType))
			{
				foreach ($feeType as $val)
				{
					$paymentList[] = $dbfeeType[$val];
				}
			}
			$criteria = new CDbCriteria();
			$criteria->compare('childid', $childId);
			if ($calanderId)
			{
				$criteria->compare('calendar_id', $calanderId);
			}
			if (count($paymentList))
			{
				$criteria->compare('payment_type', $paymentList);
			}
			$criteria->addNotInCondition('status', array(Invoice::STATS_PAID,Invoice::STATS_CHANGE_ARCHIVED,Invoice::STATS_CANCELLED));
			if (is_array($childId) && count($childId))
			{
				$criteria->index = 'childid';
				return Invoice::model()->findAll($criteria);
			}
			else
			{
				return Invoice::model()->exists($criteria);
			}
		}
		return false;
	}

	/**
	 * 设置孩子状态
	 * @param int $childId	孩子主键
	 * @param int $status	孩子状态		e.g. ChildProfileBasic(STATS_GRADUATED:设置毕业,STATS_DROPPINGOUT:设置退学处理中,STATS_DROPOUT:设置退学,STATS_REGISTERED:设置重新入学)
	 * @return boolean
	 */
	public function setChildStatus($childId=0,$status=ChildProfileBasic::STATS_REGISTERED,$userId=0)
	{
		if ($childId && in_array($status, array(ChildProfileBasic::STATS_DROPOUT,ChildProfileBasic::STATS_DROPPINGOUT,ChildProfileBasic::STATS_GRADUATED,ChildProfileBasic::STATS_REGISTERED)))
		{
            $userId = ($userId) ? $userId :Yii::app()->user->id;
			Yii::import('common.models.portfolio.ChildStudyHistory');
			Yii::import('common.models.invoice.ChildConstantTuition');
			Yii::import('common.models.invoice.ChildInactiveUnpaid');
			$model = ChildProfileBasic::model()->findByPk($childId);
			// 如果要设置的状态与孩子当前状态相同，则直接返回 true
			if ($model->status == $status) {
				return true;
			}
            $model->status = $status;
            $model->updated_timestamp = time();
            if($status == ChildProfileBasic::STATS_DROPOUT){
                $model->quit_date = time();
            }
            $model->create_uid = $userId;
            $model->save(false);
			switch ($status)
			{
				case ChildProfileBasic::STATS_REGISTERED:
					ChildClassLink::model()->deleteByPk($childId);
					ChildReserve::model()->deleteAllByAttributes(array('childid'=>$childId, 'schoolid'=>$model->schoolid));
                    $model->classid = ChildProfileBasic::STATS_REGISTERED;
                    $model->status = ChildProfileBasic::STATS_REGISTERED;
                    $model->est_quit_date = null;
                    $model->save();
					return true;
					break;
				case ChildProfileBasic::STATS_DROPOUT:
				case ChildProfileBasic::STATS_DROPPINGOUT:
				case ChildProfileBasic::STATS_GRADUATED:
					$classLink = ChildClassLink::model()->findByPk($childId);
					$historyModel = new ChildStudyHistory;
                    if(!empty($classLink)){
                        $historyModel->schoolid = $classLink->schoolid;
                        $historyModel->classid = $classLink->classid;
                        $historyModel->calendar = $classLink->calendar;
                    }else{
                        Yii::import('common.models.calendar.*');
                        $yid = CalendarSchool::model()->getCalendarId($model->schoolid, $model->first_startyear);
                        $historyModel->schoolid = $model->schoolid;
                        $historyModel->classid = $model->classid;
                        $historyModel->calendar = $yid;
                    }
                    $historyModel->childid = $childId;
					$historyModel->semester = 0;
					$historyModel->stat = $status;
					$historyModel->timestamp = time();
					$historyModel->userid = $userId;
					$historyModel->save();
					if (!empty($classLink))
					{
						$classLink->stat = $status;
						$classLink->hid = $historyModel->hid;
						$classLink->updated_timestamp = time();
						$classLink->userid = $userId;
						$classLink->save();
					}
					ChildReserve::model()->deleteAllByAttributes(array('childid'=>$childId, 'schoolid'=>$model->schoolid));
					//老生老办法表删除
    				ChildConstantTuition::model()->deleteByPk($childId);
                    //如果有未付帐单，添加未付帐单历史表
                    $unInvoice = $this->getChildUnInvoices($childId,0,array(FEETYPE_TUITION,FEETYPE_LUNCH,FEETYPE_CHILDCARE,FEETYPE_SCHOOLBUS,FEETYPE_COLLECTION,FEETYPE_AFTERSCHOOL));
                    if ($unInvoice) {
                        $unpaidModel = ChildInactiveUnpaid::model()->findByPk($childId);
                        if (empty($unpaidModel)){
                            $unpaidModel = new ChildInactiveUnpaid();
                            $unpaidModel->childid = $childId;
                        }
                        $unpaidModel->schoolid = $model->schoolid;
                        $unpaidModel->created = time();
                        $unpaidModel->updated = time();
                        $unpaidModel->save();
                    }
					return true;
					break;
			}
		}
		return false;
	}

    /**后台选择设置退学中和已退学
     * @param int $childId
     * @param int $status
     * @param int $userId
     * @return bool
     * @throws CException
     */
    public function setChildDroppingoutStatus($childId=0,$status=ChildProfileBasic::STATS_REGISTERED,$userId=0,$input)
    {
        $userId = ($userId) ? $userId :Yii::app()->user->id;
        Yii::import('common.models.portfolio.ChildStudyHistory');
        Yii::import('common.models.invoice.ChildConstantTuition');
        Yii::import('common.models.invoice.ChildInactiveUnpaid');
        $model = ChildProfileBasic::model()->findByPk($childId);
        $model->status = $status;
        $model->updated_timestamp = time();
        $model->create_uid = $userId;
        if(!empty($input['est_quit_date'])){
            $model->est_quit_date = $input['est_quit_date'];#预计退学时间
        }
        if(!empty($input['quit_date'])){
            $model->quit_date = $input['quit_date'];#实际退学时间
        }
        $model->save(false);
        $classLink = ChildClassLink::model()->findByPk($childId);
        $historyModel = new ChildStudyHistory;
        if(!empty($classLink)){
            $historyModel->schoolid = $classLink->schoolid;
            $historyModel->classid = $classLink->classid;
            $historyModel->calendar = $classLink->calendar;
        }else{
            Yii::import('common.models.calendar.*');
            $yid = CalendarSchool::model()->getCalendarId($model->schoolid, $model->first_startyear);
            $historyModel->schoolid = $model->schoolid;
            $historyModel->classid = $model->classid;
            $historyModel->calendar = $yid;
        }
        $historyModel->childid = $childId;
        $historyModel->semester = 0;
        $historyModel->stat = $status;
        $historyModel->timestamp = time();
        $historyModel->userid = $userId;
        $historyModel->save();
        if (!empty($classLink))
        {
            $classLink->stat = $status;
            $classLink->hid = $historyModel->hid;
            $classLink->updated_timestamp = time();
            $classLink->userid = $userId;
            $classLink->save();
        }
        ChildReserve::model()->deleteAllByAttributes(array('childid'=>$childId, 'schoolid'=>$model->schoolid));
        //老生老办法表删除
        ChildConstantTuition::model()->deleteByPk($childId);
        //如果有未付帐单，添加未付帐单历史表
        $unInvoice = $this->getChildUnInvoices($childId,0,array(FEETYPE_TUITION,FEETYPE_LUNCH,FEETYPE_CHILDCARE,FEETYPE_SCHOOLBUS,FEETYPE_COLLECTION,FEETYPE_AFTERSCHOOL));
        if ($unInvoice) {
            $unpaidModel = ChildInactiveUnpaid::model()->findByPk($childId);
            if (empty($unpaidModel)){
                $unpaidModel = new ChildInactiveUnpaid();
                $unpaidModel->childid = $childId;
            }
            $unpaidModel->schoolid = $model->schoolid;
            $unpaidModel->created = time();
            $unpaidModel->updated = time();
            $unpaidModel->save();
        }
        return true;
    }

	/**
	 * 孩子提现
	 * @param int $childId
	 * @param number $amount
	 * @return boolean
	 */
	public static function childOutCash($childId=0,$amount=0)
	{
		if ($childId && $amount>0.01)
		{
			$childObj = self::getChildObj($childId);
			//取孩子当前账户余额
			$historyAmount = PolicyApi::getCreditAmount($childId);
			$balanceAmount = $historyAmount-$amount;
			if ( abs($historyAmount - $childObj->credit)<0.01 && ( ($balanceAmount >=0 ) ||( abs($balanceAmount)<0.01) ) )
			{
				$branch = Branch::model()->getBranchInfo($childObj->schoolid, array('schcalendar'));
				$childCredit = new ChildCredit;
				$childCredit->classid = $childObj->classid;
				$childCredit->schoolid = $childObj->schoolid;
				$childCredit->yid = $branch['schcalendar'];
				$childCredit->childid = $childId;
				$childCredit->amount = $amount;
				$childCredit->inout = 'out';
				$childCredit->itemname = ChildCredit::TYPE_CASHOUT;
				$childCredit->userid = isset(Yii::app()->user) ? Yii::app()->user->id : 1;
				$childCredit->updated_timestamp = time();
				$childCredit->balance = $balanceAmount;
				if ($childCredit->save())
				{
					ChildProfileBasic::model()->updateByPk($childId, array('credit'=>$balanceAmount));
				}
				return $childCredit->cid;
			}
		}
		return false;
	}

	/**
	 * 处置孩子个人账户余额
	 * @param 	int $childId
	 * @return	boolean
	 */
	public function childBalanceDisposal($childId=0)
	{
		if ($childId)
		{
			global $paymentMappings;
			$childObj = self::getChildObj($childId);
			$branch = Branch::model()->getBranchInfo($childObj->schoolid, array('schcalendar'));
			//取孩子当前账户余额
			$historyAmount =  PolicyApi::getCreditAmount($childId);
			if (abs($historyAmount - $childObj->credit) < 0.01 && $historyAmount)
			{
				$values = array(
    				'calendar_id'=>$branch['schcalendar'],
    				'amount'=>$historyAmount,
    				'schoolid'=>$childObj->schoolid,
    				'classid'=>$childObj->classid,
    				'childid'=>$childId,
    				'payment_type'=>$paymentMappings['dbFeeType'][FEETYPE_ACCOUNTBALANCE],
    				'inout'=>'in',
    				'fee_type'=>0,
    				'startdate'=>0,
    				'enddate'=>0,
    				'title'=>'个人账户余额清零帐单',
				);
				$invoice = new Invoice;
				$invoice->setAttributes($values);
				$invoice->original_amount = $historyAmount;
				$invoice->userid = Yii::app()->user->id;
				$invoice->timestamp = time();
				$invoice->status = Invoice::STATS_PAID;
				if ($invoice->save())
				{
					//流水表
					$transaction = new InvoiceTransaction;
                    $transaction->setAttributes($values);
					$transaction->invoice_id = $invoice->invoice_id;
					$transaction->timestampe = time();
					$transaction->transactiontype = InvoiceTransaction::TYPE_USERCREDIT;
                    $transaction->operator_uid = Yii::app()->user->id;
					$transaction->save();
					//个人账户表
					$childCredit = new ChildCredit;
					$childCredit->classid = $childObj->classid;
					$childCredit->schoolid = $childObj->schoolid;
					$childCredit->yid = $branch['schcalendar'];
					$childCredit->childid = $childId;
					$childCredit->amount = $historyAmount;
					$childCredit->invoice_id = $invoice->invoice_id;
					$childCredit->transaction_id = $transaction->id;
					$childCredit->inout = 'out';
					$childCredit->itemname = $paymentMappings['dbFeeType'][FEETYPE_ACCOUNTBALANCE];
					$childCredit->userid = Yii::app()->user->id;
					$childCredit->updated_timestamp = time();
					$childCredit->balance = 0;
					$childCredit->save();
					//孩子基本表
					ChildProfileBasic::model()->updateByPk($childId, array('credit'=>0));
					return true;
				}
			}
		}
		return false;
	}

	/**
	 * 兄弟姐妹相互转移个人账户金额
	 * @param object $fromChild
	 * @param object $toChild
	 * @param number $amount
	 * @return boolean
	 */
	public static function transferChildBalance($fromChild=0,$toChild=0,$amount=0)
	{
		if ($fromChild && $toChild && $amount>0.01)
		{
			if (($fromChild->fid && $fromChild->fid == $toChild->fid) || ($fromChild->mid && $fromChild->mid == $toChild->mid))
			{
				$branchList = Branch::model()->getBranchList(null,true);
				$fromBalanceAmount =PolicyApi::getCreditAmount($fromChild->childid);
				$_fromBalanceAmount = $fromBalanceAmount-$amount;
				if ($_fromBalanceAmount>=0.00 || abs($_fromBalanceAmount) == 0)
				{
                    $transaction = Yii::app()->db->beginTransaction();
                    try{
                        //转出孩子个人账户表操作
                        $fromChildCredit = new ChildCredit;
                        $fromChildCredit->classid = $fromChild->classid;
                        $fromChildCredit->schoolid = $fromChild->schoolid;
                        $fromChildCredit->yid = $branchList[$fromChild->schoolid]['yid'];
                        $fromChildCredit->childid = $fromChild->childid;
                        $fromChildCredit->amount = $amount;
                        $fromChildCredit->inout = 'out';
                        $fromChildCredit->itemname = ChildCredit::TYPE_TRANSFER;
                        $fromChildCredit->userid = Yii::app()->user->id;
                        $fromChildCredit->updated_timestamp = time();
                        $fromChildCredit->balance = $_fromBalanceAmount;
                        if (!$fromChildCredit->save())
                        {
                            $transaction->rollback();
                            return false;
                        }
                        //转出孩子基本表操作
                        if(!ChildProfileBasic::model()->updateByPk($fromChild->childid, array('credit'=>$_fromBalanceAmount))){
                            $transaction->rollback();
                            return false;
                        }
                        //转入孩子个人账户表操作
                        $toBalanceAmount = ChildCredit::model()->getChildCredit($toChild->childid);
                        $_toBalanceAmount = $toBalanceAmount+$amount;
                        $toChildCredit = new ChildCredit;
                        $toChildCredit->classid = $toChild->classid;
                        $toChildCredit->schoolid = $toChild->schoolid;
                        $toChildCredit->yid = $branchList[$toChild->schoolid]['yid'];
                        $toChildCredit->childid = $toChild->childid;
                        $toChildCredit->amount = $amount;
                        $toChildCredit->inout = 'in';
                        $toChildCredit->itemname = ChildCredit::TYPE_TRANSFER;
                        $toChildCredit->userid = Yii::app()->user->id;
                        $toChildCredit->updated_timestamp = time();
                        $toChildCredit->balance = $_toBalanceAmount;
                        if (!$toChildCredit->save())
                        {
                            $transaction->rollback();
                            return false;
                        }
                        //转入孩子基本表操作
                        if (!ChildProfileBasic::model()->updateByPk($toChild->childid, array('credit'=>$_toBalanceAmount))){
                            $transaction->rollback();
                            return false;
                        }
                        //转出入流水表操作
                        $transfer = new CreditTransfer;
                        $transfer->fromchildid = $fromChild->childid;
                        $transfer->tochildid = $toChild->childid;
                        $transfer->fromcalendarid = $branchList[$fromChild->schoolid]['yid'];
                        $transfer->tocalendarid = $branchList[$toChild->schoolid]['yid'];
                        $transfer->fromschoolid = $fromChild->schoolid;
                        $transfer->toschoolsid = $toChild->schoolid;
                        $transfer->type = ChildCredit::TYPE_TRANSFER;
                        $transfer->amount = $amount;
                        $transfer->fromcid = $fromChildCredit->cid;
                        $transfer->tocid = $toChildCredit->cid;
                        $transfer->updated_timestamp = time();
                        $transfer->userid = Yii::app()->user->id;
                        if (!$transfer->save()){
                            $transaction->rollback();
                            return false;
                        }
                        $transaction->commit();
                        return true;
                    }catch(Exception $e){
                        $transaction->rollBack();
                        return false;
                    }
				}
			}
		}
		return false;
	}

    /**
     * 转校
     * @param string $fromSchoolId
     * @param string $toSchoolId
     * @param int $childId
     * @deprecated since version number
     * @return boolean
     */
    public static function transferSchool($fromSchoolId=null,$toSchoolId=null,$childId=0)
    {
    	if (!empty($fromSchoolId) && !empty($toSchoolId) && $childId)
    	{
    		$childObj = self::getChildObj($childId);
    		if (ChildProfileBasic::model()->updateByPk($childId,array('classid'=>0,'status'=>0, 'ufida_update'=>0,'updated_timestamp'=>time(),'create_uid'=>Yii::app()->user->id)))
    		{
    			ChildClassLink::model()->deleteByPk($childId);
    			ChildReserve::model()->deleteAllByAttributes(array('childid'=>$childId));
    			if ($childObj->credit)
    			{
    				$transferSchool = new TransferSchoolHistory();
    				$transferSchool->childid = $childId;
    				$transferSchool->from_schoolid = $fromSchoolId;
    				$transferSchool->to_schoolid = $toSchoolId;
    				$transferSchool->balance = $childObj->credit;
    				$transferSchool->transfer_time = time();
    				$transferSchool->save();
    			}
    			$branchList = Branch::model()->getBranchList(null,true);
    			Yii::import('common.models.portfolio.ChildStudyHistory');
    			Yii::import('common.models.invoice.ChildConstantTuition');
    			$historyModel = new ChildStudyHistory;
    			$historyModel->childid = $childId;
    			$historyModel->schoolid = $childObj->schoolid;
    			$historyModel->classid = $childObj->classid;
    			$historyModel->calendar = $branchList[$childObj->schoolid]['yid'];
    			$historyModel->semester = 0;
    			$historyModel->stat = ChildProfileBasic::STATS_DROPOUT;
    			$historyModel->timestamp = time();
    			$historyModel->userid = Yii::app()->user->id;
    			$historyModel->save();
    			//老生老办法表删除
    			ChildConstantTuition::model()->deleteByPk($childId);
    			return true;
    		}
    	}
    	return false;
    }

	public static function getCreditAmount($childId=0){
		$result = 0;
		$rows = Yii::app()->db->createCommand()->select('SUM(amount) as amount, `inout` as T')->from('ivy_child_credit')->where('childid=:childid', array(':childid'=>$childId))->group('inout')->queryAll();
		if($rows){
			foreach($rows as $row){
				if($row['T']=='in')
					$result += $row['amount'];
				else
					$result -= $row['amount'];
			}
		}
		return round($result, 2);
	}

    /**
     * 获取孩子每个学年余额，若指定yid，则返回 array("amount"=>xxx,"frozen"=>yyy,"startyear"=>2012)
     * 若不指定yid, 则返回 array( '2012' => array("amount"=>xxx,"frozen"=>yyy,"startyear"=>2012))
     * @param $childId
     * @return Array
     */
    public static function getDepositAmount($childId=0, $calendarId=0, $withFrozenInfo=false)
    {
        $result = null;
        if($childId){
            if($calendarId){
                $cond = 'childid=:childid AND H.yid=:yid';
                $params = array(':childid'=>$childId, ':yid'=>$calendarId);
            }else{
                $cond = 'childid=:childid';
                $params = array(':childid'=>$childId);
            }
            $rows = Yii::app()->db->createCommand()
                ->select('SUM(amount) as amount, H.yid, `inout`, C.startyear')
                ->from('ivy_child_deposit_history H')
                ->leftjoin('ivy_calendar_yearly C', 'H.yid=C.yid')
                ->where($cond, $params)
                ->group('yid,inout')
                ->queryAll();

            if($rows){
                $yids = array();
                foreach ($rows as $row){
                    $result[$row['startyear']]['amount'] = isset($result[$row['startyear']]['amount']) ? $result[$row['startyear']]['amount'] : 0;
                    $result[$row['startyear']]['startyear'] = $row['startyear'];
                    if($row['inout'] == 'in'){
                        $result[$row["startyear"]]["amount"] += $row["amount"];
                    }else{
                        $result[$row["startyear"]]["amount"] -= $row["amount"];
                    }
                    $result[$row["startyear"]]["yid"] = $row["yid"];
                    $yids[] = $row['yid'];
                }
                if($yids && $withFrozenInfo){
                    $crit = new CDbCriteria;
                    if($calendarId){
                        $crit->condition = 'childid=:childid AND yid=:yid';
                        $crit->params = array(':childid'=>$childId, ':yid'=>$calendarId);
                    }else{
                        $crit->condition = 'childid=:childid';
                        $crit->params = array(':childid'=>$childId);
                    }

                    $tmp = TemporaryDeposit::model()->findAll($crit);
                    $tmpData = CHtml::listData($tmp,'yid','amount');

                    foreach($result as $k=>$r){
                        if( isset($tmpData[$r['yid']]) )
                        $result[$k]['frozen'] = $tmpData[$r['yid']];
                    }
                }

//                if(count($result) ==1){
//                    return array_shift($result);
//                }
            }
            return $result;
        }
    }

    /**
     * 得到兄弟姐妹
     * @param int $childId
     * @param boolean $strict
     * @return array
     */
    public static function getSiblings($childId,$strict=false)
    {
    	$parentIds = array();
    	$childs = array();
    	$isRelation = false;
    	$model = ChildProfileBasic::model()->findByPk($childId);
    	$criter = new CDbCriteria();
    	if ($model->fid)
    	{
    		$criter->addCondition('fid='.$model->fid);
    	}
    	if ($model->mid)
    	{
    		$criter->addCondition('mid='.$model->mid,'OR');
    	}
    	$parentList = ChildProfileBasic::model()->findAll($criter);
    	if (!empty($parentList) && count($parentList)>=2)
    	{
    		foreach ($parentList as $val)
    		{
                $isRelation = true;
                $childs['info'][$val->childid] = $val;
    		}
    	}
    	if ($strict === true)
    	{
    		$isRelation = false;
    		$criter = new CDbCriteria();
    		$criter->compare('childid', array_keys($childs['info']));
    		$classModel = ChildClassLink::model()->findAll($criter);
    		if (!empty($classModel))
    		{
    			foreach ($classModel as $val)
    			{
    				if ($val->stat == ChildClassLink::STATS_ONGOING)
    				{
    					$isRelation = true;
    				}
    			}
    		}
    		if ($isRelation === false)
    		{
    			$childs['info'] = array();
    		}
    	}
        unset($childs['info'][$childId]);
    	$childs['isRelation'] = $isRelation;
    	return $childs;
    }

    /**
     * 付账单
     * @param type $invoiceModels
     * @param type $tType
     * @param type $amount
     * @return boolean
     */
    public function Pay($invoiceModels, $tType, $amount=0, $opUid=null)
    {
        Yii::import('common.models.invoice.*');

        if (!$opUid){
            $opUid = (Yii::app()->user->isGuest) ? 1 : Yii::app()->user->id;
        }

        $childObj = self::getChildObj($invoiceModels[0]->childid);
        $credit = new ChildCredit;
        if ($tType == InvoiceTransaction::TYPE_USERCREDIT && ( $childObj->credit <= 0.001 || (abs($childObj->credit - self::getCreditAmount($invoiceModels[0]->childid))>0.001))){
            return RC::ERR_TC_PAYMENT_310001;
        }
        $invoiceAmount = 0;
        $transaction = Yii::app()->db->beginTransaction();
        try{
            foreach ($invoiceModels as $invoiceModel){
                if (!in_array($invoiceModel->status, array(Invoice::STATS_UNPAID, Invoice::STATS_PARTIALLY_PAID))){
                    $transaction->rollBack();
                    return RC::ERR_TC_PAYMENT_310002;
                }

                $dealWith = $invoiceModel->amount - $invoiceModel->paidSum; #应付款

				if ($tType == InvoiceTransaction::TYPE_USERCREDIT) {
					// 判断金额正确性
					if ($amount - $childObj->credit > 0.001) {
						return RC::ERR_TC_PAYMENT_310001;
					}
					if ($amount - $dealWith > 0.001) {
						return RC::ERR_TC_PAYMENT_310001;
					}
                    $amount = min($amount, $dealWith);
				}
                $model = new InvoiceTransaction;
                foreach(array("invoice_id", "calendar_id", "inout", "childid", "classid", "schoolid", "payment_type", "afterschool_id", "title", "startdate", "enddate", "installment", "fee_type", "memo", "fee_other_id") as $tag) {
                    $model->$tag = $invoiceModel->$tag;
                }
                $model->amount = $amount > $dealWith ? $dealWith : $amount;

                $model->transactiontype = $tType;
                $model->operator_uid = $opUid;
                $model->timestampe = $invoiceModel->payDate ? $invoiceModel->payDate : time();
                if (!$model->save()){
                    $transaction->rollBack();
                    return RC::ERR_TC_PAYMENT_310003;
                }

                if ($tType == InvoiceTransaction::TYPE_USERCREDIT){

                    $childObj->credit -= $amount;
                    if (!$childObj->save()){
                        $transaction->rollBack();
                        return RC::ERR_TC_PAYMENT_310004;
                    }

                    $credit->classid = $invoiceModel->classid;
                    $credit->schoolid = $invoiceModel->schoolid;
                    $credit->yid = $invoiceModel->calendar_id;
                    $credit->childid = $invoiceModel->childid;
                    $credit->amount = $amount;
                    $credit->inout = 'out';
                    $credit->itemname = $invoiceModel->payment_type;
                    $credit->invoice_id = $invoiceModel->invoice_id;
                    $credit->transaction_id = $model->id;
                    $credit->userid = $opUid;
                    $credit->updated_timestamp = time();
                    $credit->balance = $childObj->credit;
                    if (!$credit->save()){
                        $transaction->rollBack();
                        return RC::ERR_TC_PAYMENT_310005;
                    }
                }

                $depAmount = self::getDepositAmount($invoiceModel->childid, $invoiceModel->calendar_id);
                if (!empty($depAmount))
                    $depAmount = current($depAmount);
                else
                    $depAmount = array('amount'=>0);

                if ( ( abs($amount - $dealWith) < 0.001 ) || ( $amount - $dealWith > 0.001 ) ){ // 金额充裕，账单将变成款付清
                    switch ($invoiceModel->payment_type){
                        case $GLOBALS['paymentMappings']['dbFeeType'][FEETYPE_TUITION]:
                        case $GLOBALS['paymentMappings']['dbFeeType'][FEETYPE_LUNCH]:
                        case $GLOBALS['paymentMappings']['dbFeeType'][FEETYPE_SCHOOLBUS]:
                            //款付清的状态才处理使用预交学费的情况
                            if ($invoiceModel->payment_type === $GLOBALS['paymentMappings']['dbFeeType'][FEETYPE_TUITION]){
                                $tModel = TemporaryDeposit::model()->findByPk($invoiceModel->invoice_id);
                                if ($tModel !== null){

                                    $depModel = new DepositHistory;
                                    $depModel->childid = $invoiceModel->childid;
                                    $depModel->yid = $invoiceModel->calendar_id;
                                    $depModel->amount = $tModel->amount;
                                    $depModel->balance = $depAmount['amount']-$tModel->amount;
                                    $depModel->inout = 'out';
                                    $depModel->tran_id = $model->id;
                                    $depModel->timestamp = time();
                                    $depModel->create_uid = $opUid;
                                    if (!$depModel->save()){
                                        $transaction->rollBack();
                                        return RC::ERR_TC_PAYMENT_310006;
                                    }

                                    if (!$tModel->delete()){
                                        $transaction->rollBack();
                                        return RC::ERR_TC_PAYMENT_310007;
                                    }
                                }
                            }
                            $serviceModel = new ChildServiceInfo;
                            $serviceModel->invoice_id = $invoiceModel->invoice_id;
                            $serviceModel->transaction_id = $model->id;
                            $serviceModel->schoolid = $invoiceModel->schoolid;
                            $serviceModel->classid = $invoiceModel->classid;
                            $serviceModel->childid = $invoiceModel->childid;
                            $serviceModel->yid = $invoiceModel->calendar_id;
                            $serviceModel->payment_type = $invoiceModel->payment_type;
                            $serviceModel->startdate = $invoiceModel->startdate;
                            $serviceModel->enddate = $invoiceModel->enddate;

                            $_child_service_info = unserialize(stripcslashes(trim($invoiceModel->child_service_info)));

                            foreach (array('mon', 'tue', 'wed', 'thu', 'fri') as $w){
                                $serviceModel->$w = $_child_service_info[$w];
                            }
                            if (!$serviceModel->save()){
                                $transaction->rollBack();
                                return RC::ERR_TC_PAYMENT_310009;
                            }

                            $criteria = new CDbCriteria();
                            $criteria->compare('childid', $invoiceModel->childid);
                            $criteria->compare('yid', $invoiceModel->calendar_id);
                            $criteria->compare('category', 10);
                            $criteria->compare('itemname', $invoiceModel->payment_type);
                            $criteria->compare('branchid', $invoiceModel->schoolid);
                            $criteria->order= 'itemvalue desc';
                            $criteria->limit= 1;
                            $actModel = ChildActiveStatus::model()->find($criteria);
                            if ($actModel === null)
                                $actModel = new ChildActiveStatus;
                            if ($actModel->itemvalue < $invoiceModel->enddate){
                                $actModel->childid = $invoiceModel->childid;
                                $actModel->yid = $invoiceModel->calendar_id;
                                $actModel->category = 10;
                                $actModel->itemname = $invoiceModel->payment_type;
                                $actModel->transaction_id = $model->id;
                                $actModel->classid = $invoiceModel->classid;
                                $actModel->itemvalue = $invoiceModel->enddate;
                                $actModel->branchid = $invoiceModel->schoolid;
                                $actModel->updatetime = time();
                                if(!$actModel->save()){
                                    $transaction->rollBack();
                                    return RC::ERR_TC_PAYMENT_310010;
                                }
                            }
                            break;
                    }
                    $invoiceModel->status = Invoice::STATS_PAID;

                    # 预交学费付款 交多少进多少流水
                    if ($invoiceModel->payment_type === $GLOBALS['paymentMappings']['dbFeeType'][FEETYPE_DEPOSIT]){
                        $depModel = new DepositHistory;
                        $depModel->childid = $invoiceModel->childid;
                        $depModel->yid = $invoiceModel->calendar_id;
                        $depModel->amount = $dealWith;
                        $depModel->balance = $depAmount['amount']+$dealWith;
                        $depModel->inout = 'in';
                        $depModel->tran_id = $model->id;
                        $depModel->timestamp = time();
                        $depModel->create_uid = $opUid;
                        if ( !$depModel->save() ) {
                            $transaction->rollBack();
                            return RC::ERR_TC_PAYMENT_310008;
                        }
                    }
                }
                elseif ( ($amount - $dealWith) < 0.001 ) { // 金额不足，账单将变成部分付清
                    $invoiceModel->status = Invoice::STATS_PARTIALLY_PAID;

                    # 预交学费付款 交多少进多少流水
                    if ($invoiceModel->payment_type === $GLOBALS['paymentMappings']['dbFeeType'][FEETYPE_DEPOSIT]){
                        $depModel = new DepositHistory;
                        $depModel->childid = $invoiceModel->childid;
                        $depModel->yid = $invoiceModel->calendar_id;
                        $depModel->amount = $amount;
                        $depModel->balance = $depAmount['amount']+$amount;
                        $depModel->inout = 'in';
                        $depModel->tran_id = $model->id;
                        $depModel->timestamp = time();
                        $depModel->create_uid = $opUid;
                        if ( !$depModel->save() ) {
                            $transaction->rollBack();
                            return RC::ERR_TC_PAYMENT_310008;
                        }
                    }
                }

                $invoiceModel->last_paid_timestamp = time();
                if (!$invoiceModel->save()){
                    $transaction->rollBack();
                    return RC::ERR_TC_PAYMENT_310011;
                }

                $invoiceAmount += $dealWith;
            }

            if ($tType != InvoiceTransaction::TYPE_USERCREDIT){
                $amount -= $invoiceAmount;
                if ($amount > 0.001){ // 多余的钱进个人账户
                    $childObj->credit += $amount;
                    if(!$childObj->save()){
                        $transaction->rollBack();
                        return RC::ERR_TC_PAYMENT_310004;
                    }

                    $credit->classid = $invoiceModel->classid;
                    $credit->schoolid = $invoiceModel->schoolid;
                    $credit->yid = $invoiceModel->calendar_id;
                    $credit->childid = $invoiceModel->childid;
                    $credit->amount = $amount;
                    $credit->inout = 'in';
                    $credit->itemname = $invoiceModel->payment_type;
                    $credit->invoice_id = $invoiceModel->invoice_id;
                    $credit->transaction_id = $model->id;
                    $credit->userid = $opUid;
                    $credit->updated_timestamp = time();
                    $credit->balance = $childObj->credit;
                    $credit->transactiontype = $tType;
                    if (!$credit->save()){
                        $transaction->rollBack();
                        return RC::ERR_TC_PAYMENT_310012;
                    }
                }
            }

            $transaction->commit();
            return 0;
        }
        catch(Exception $e){
             $transaction->rollBack();
             return RC::ERR_TC_PAYMENT_310013;
        }
    }

    private static function getChildObj($childId)
    {
//        if(isset(Yii::app()->getController()->childObj))
//		{
//			$childObj = Yii::app()->getController()->childObj;
//		}
//		else
//		{
			$childObj = ChildProfileBasic::model()->findByPk($childId);
//		}
        return $childObj;
    }

    /**
     * POS支付成功发送邮件
     * @param  [type]  $orderIds     [description]
     * @param  string  $addCC        [description]
     * @param  boolean $controller   [description]
     * @param  integer $isProduction [description]
     * @return [type]                [description]
     */
    public static function posToEmail($orderIds,$addCC='parent',$controller=true,$isProduction=0){
        Yii::import('common.models.*');
        Yii::import('common.models.invoice.Invoice');
        Yii::import('common.models.yeepay.YeepayOrder');
        Yii::import('common.models.yeepay.YeepayOrderItem');
        Yii::import('application.components.OA');
        $mailer = Yii::createComponent('common.extensions.mailer.EMailer');
        $addCC = strtolower($addCC);
        if (is_array($orderIds) && count($orderIds))
        {
            foreach ($orderIds as $val){
                $orderModel = YeepayOrder::model()->with('items', 'ChildProfile')->findByPk($val);
    	    	if ( OA::isYZQ($orderModel->schoolId) ) {
    	    		$addCC='support';
				}
                if (!empty($orderModel)) {
                    //查询孩子的学校信息
                    $branchList = Branch::model()->with('info')->findByPk($orderModel->schoolId);
                    //班级信息
                    $classModel = null;
                    if ($orderModel->ChildProfile->classid) {
                        $classModel = IvyClass::model()->findByPk($orderModel->ChildProfile->classid);
                    }
                    $xContentCn = '';
                    $xContentEn = '';
                    $totalAmount = 0;
                    $i = 1;
                    foreach ($orderModel->items as $oVal){
                        if ($oVal->payable_amount == $oVal->invoice->amount){
                            $xContentEn .= "<u>" . $i . "、" . $oVal->invoice->title . " ( " . $oVal->payable_amount . " RMB )</u><br>";
                            $xContentCn .= "<u>" . $i . "、" . $oVal->invoice->title . " ( " . $oVal->payable_amount . " 元 )</u><br>";
                        }else{
                            $xContentEn .= "<u>" . $i . "、" . $oVal->invoice->title . " ( Invoice Total: " . $oVal->invoice->amount . " RMB, Debit/Credit Card Payment: " . $oVal->payable_amount . " RMB )</u><br>";
                            $xContentCn .= "<u>" . $i . "、" . $oVal->invoice->title . " ( 帐单金额：" . $oVal->invoice->amount . " 元，本次刷卡：" . $oVal->payable_amount . " 元 )</u><br>";
                        }
                        $totalAmount += $oVal->payable_amount;
                        $i++;
                    }
                    if ($addCC == 'parent'){
                        //查询家长邮件信息
                        $criteria = new CDbCriteria();
                        $criteria->compare('uid', array($orderModel->ChildProfile->fid, $orderModel->ChildProfile->mid));
                        $criteria->compare('isstaff', 0);
                        $criteria->addCondition('level>0');
                        $useModel = User::model()->findAll($criteria);
                        foreach ($useModel as $uVal) {
                            $mailer->AddAddress($uVal->email);
                        }
//                        $mailer->AddCC($branchList->info->support_email);
//                        $mailer->AddReplyTo($branchList->info->support_email);
                        $mailer->pushTo('invoice_paid', $orderModel->schoolId);
                    }else{
                         $mailer->AddAddress($branchList->info->support_email);
                    }
                   // $mailer->Subject = "【艾毅在线】付款确认（POS机支付）订单号：" . $val .": [IvyOnline] Invoice Payment Confirmation (POS) Order NO. " . $val;
                    $mailer->Subject = 'POS#'.$val.' 付款确认 Invoice Payment Confirmation';
                    $mailer->iniMail( OA::isProduction() ); // 此行代码要放到AddAddress, AddCC方法下面
                    if ($isProduction===0){
                        $mailer->iniAsTest();
                    }
                    if ($controller === true){
                    	if($orderModel->school->type == 50){
                        	$mailer->getView('pos_ds', array('orderModel' => $orderModel,'xContentCn'=>$xContentCn,'xContentEn'=>$xContentEn,'branchList'=>$branchList,'classModel'=>$classModel,'totalAmount'=>number_format($totalAmount,2)), 'todsparent');
                    	}else{
                        	$mailer->getView('pos', array('orderModel' => $orderModel,'xContentCn'=>$xContentCn,'xContentEn'=>$xContentEn,'branchList'=>$branchList,'classModel'=>$classModel,'totalAmount'=>number_format($totalAmount,2)), 'toparent');
                    	}
                    }else{
                		if($orderModel->school->type == 50){
                        	$mailer->getCommandView('pos_ds', array('orderModel' => $orderModel,'xContentCn'=>$xContentCn,'xContentEn'=>$xContentEn,'branchList'=>$branchList,'classModel'=>$classModel,'totalAmount'=>number_format($totalAmount,2)), 'todsparent');
                		}else{
                        	$mailer->getCommandView('pos', array('orderModel' => $orderModel,'xContentCn'=>$xContentCn,'xContentEn'=>$xContentEn,'branchList'=>$branchList,'classModel'=>$classModel,'totalAmount'=>number_format($totalAmount,2)), 'toparent');
                		}
                    }
                    if ($mailer->Send()) {
                        echo "ok..\r\n";
                    } else {
                        return false;
                    }
                }
             }
        }
    }

    /**
     * 微信支付成功发送邮件
     * @param  array   $orderIds     [订单号]
     * @param  string  $addCC        [收件人]
     * @param  boolean $controller   [description]
     * @param  integer $isProduction [是否为生产环境]
     * @param  boolean $isAllinpay 	 [是否为通联在线支付]
     * @return [type]                [description]
     */
    public static function wxpayToEmail($orderIds,$addCC='parent',$controller=true,$isProduction=0,$isAllinpay=false)
    {
    	// 注释掉下面这一行代码则开启发信
    	// return false;
    	Yii::import('common.models.*');
    	Yii::import('common.models.invoice.Invoice');
    	Yii::import('common.models.wxpay.*');
    	Yii::import('common.models.alipay.*');
    	Yii::import('application.components.OA');
    	$mailer = Yii::createComponent('common.extensions.mailer.EMailer');
    	$addCC = strtolower($addCC);
    	if (is_array($orderIds) && count($orderIds))
    	{
    	    foreach ($orderIds as $val){
    	        $orderModel = WechatPayOrder::model()->findByPk($val);
    	    	if ( OA::isYZQ($orderModel->schoolid) ) {
    	    		$addCC='support';
				}
    	        $loginUrl = CommonUtils::isGradeSchool($orderModel->ChildProfile->classid) ? 'http://dse.ivyonline.cn' : 'http://dsk.ivyonline.cn';
    	        if($isAllinpay){$orderModel = AlipayOrder::model()->findByPk($val);}
    	        if (!empty($orderModel)) {
    	            //查询孩子的学校信息
    	            $branchList = Branch::model()->with('info')->findByPk($orderModel->schoolid);
    	            //班级信息
    	            $classModel = null;
    	            if ($orderModel->ChildProfile->classid) {
    	                $classModel = IvyClass::model()->findByPk($orderModel->ChildProfile->classid);
    	            }
    	            $xContentCn = '';
    	            $xContentEn = '';
	                $totalAmount = $orderModel->fact_amount;
    	            $i = 1;
    	            if (count($orderModel->items) == 1 ) {
	                    $xContentEn .= "<u>" . $i . "、" . $orderModel->items[0]->invoice->title . " ( Invoice Total: " . $orderModel->items[0]->invoice->amount . " RMB, The Payment: " . $orderModel->fact_amount . " RMB )</u><br>";
	                    $xContentCn .= "<u>" . $i . "、" . $orderModel->items[0]->invoice->title . " ( 帐单金额：" . $orderModel->items[0]->invoice->amount . " 元，本次支付：" . $orderModel->fact_amount . " 元 )</u><br>";
    	            }else{
	    	            foreach ($orderModel->items as $oVal){
	    	                    $xContentEn .= "<u>" . $i . "、" . $oVal->invoice->title . " ( Invoice Total: " . $oVal->invoice->amount . " RMB, The Payment: " . $oVal->amount . " RMB </u><br>";
	    	                    $xContentCn .= "<u>" . $i . "、" . $oVal->invoice->title . " ( 帐单金额：" . $oVal->invoice->amount . " 元，本次支付：" . $oVal->amount . " 元 )</u><br>";
		    	                $i++;
	    	            }
    	            }
    	            if ($addCC == 'parent'){
    	                //查询家长邮件信息
    	                $criteria = new CDbCriteria();
    	                $criteria->compare('uid', array($orderModel->ChildProfile->fid, $orderModel->ChildProfile->mid));
    	                $criteria->compare('isstaff', 0);
    	                $criteria->addCondition('level>0');
    	                $useModel = User::model()->findAll($criteria);
    	                // 暂时关闭发给家长邮件
    	                foreach ($useModel as $uVal) {
    	                    $mailer->AddAddress($uVal->email);
    	                }
//    	                $mailer->AddCC($branchList->info->support_email);
//    	                $mailer->AddReplyTo($branchList->info->support_email);
    	                $mailer->pushTo('invoice_paid', $orderModel->schoolid);
    	            }else{
    	                 $mailer->AddAddress($branchList->info->support_email);
    	            }
    	            $mailer->Subject = '微信支付#'.$val.' 付款确认 Invoice Payment Confirmation';
    	            if($isAllinpay){$mailer->Subject = '[IvyOnline] Online Banking#'.$val.' 付款确认 Invoice Payment Confirmation';}
    	            $mailer->iniMail( OA::isProduction() ); // 此行代码要放到AddAddress, AddCC方法下面

					if($orderModel->school->type == 50){
						$view = 'wxpay_ds';$vmain = 'todsparent';
					}else{
						$view = 'wxpay';$vmain = 'toparent';
					}

					$mailer->getView($view, array('orderModel' => $orderModel,'loginUrl'=>$loginUrl,'xContentCn'=>$xContentCn,'xContentEn'=>$xContentEn,'branchList'=>$branchList,'classModel'=>$classModel,'totalAmount'=>number_format($totalAmount,2),'isAllinpay'=>$isAllinpay), $vmain);
    	            if ($mailer->Send()) {
    	                return true;
    	            } else {
    	                return false;
    	            }
    	        }
    	     }
    	}
    }

    /**
     * 银行转账确认发送邮件
     * @param  [type]  $orderIds     [description]
     * @param  string  $addCC        [description]
     * @param  boolean $controller   [description]
     * @param  integer $isProduction [description]
     * @return [type]                [description]
     */
    public static function transferToEmail($orderIds,$addCC='parent',$controller=true,$isProduction=0){
        Yii::import('common.models.*');
        Yii::import('common.models.invoice.Invoice');
        Yii::import('common.models.invoice.Banktransfer');
        Yii::import('common.models.invoice.BanktransferDetail');
        Yii::import('application.components.OA');
        $mailer = Yii::createComponent('common.extensions.mailer.EMailer');
        $addCC = strtolower($addCC);
        if (is_array($orderIds) && count($orderIds))
        {
            foreach ($orderIds as $val){
            	$orderModel = Banktransfer::model()->findByPk($val);
            	$childs = explode(',', $orderModel->childid);
            	$childModels = ChildProfileBasic::model()->findAllByPk($childs);
            	foreach ($childModels as $childModel) {
            		//孩子学校信息
            		$branchList = $childModel->school;
            		$classModel = $childModel->ivyclass;
                    $xContentCn = '';
                    $xContentEn = '';
                    $totalAmount = 0;
                    $i = 1;
                    $bDetilModels = BanktransferDetail::model()->findAll('transferid=:transferid AND childid=:childid',
	                	array(
							':transferid'=>$orderModel->id,
							':childid'=>$childModel->childid,
						));
                   	if (!$bDetilModels) {
                   		continue;
                   	}
                   	$branchId = current($bDetilModels)->invoice->schoolid;
                    foreach ($bDetilModels as $dVal){
	                    $xContentEn .= "<u>" . $i . "、" . $dVal->invoice->title . " ( Invoice Total: " . $dVal->invoice->amount . " RMB,Payment: " . $dVal->invoiceTransaction->amount . " RMB )</u><br>";
	                    $xContentCn .= "<u>" . $i . "、" . $dVal->invoice->title . " ( 帐单金额：" . $dVal->invoice->amount . " 元，本次付款：" . $dVal->invoiceTransaction->amount . " 元 )</u><br>";
	                    $totalAmount += $dVal->invoiceTransaction->amount;
                        $i++;
                    }
                    if ($addCC == 'parent'){
                        //查询家长邮件信息
                        $criteria = new CDbCriteria();
                        $criteria->compare('uid', array($childModel->fid,$childModel->mid));
                        $criteria->compare('isstaff', 0);
                        $criteria->addCondition('level>0');
                        $useModel = User::model()->findAll($criteria);
                        foreach ($useModel as $uVal) {
                        	if (strpos($uVal->email, '@')) {
	                            $mailer->AddAddress($uVal->email);
                        	}
                        }
//                        $mailer->AddCC($branchList->info->support_email);
//                        $mailer->AddReplyTo($branchList->info->support_email);
                        $mailer->pushTo('invoice_paid', $branchId);
                    }else{
                         $mailer->AddAddress($branchList->info->support_email);
                    }
                    $mailer->Subject = 'BankTransfer#'.$val.' 付款确认 Invoice Payment Confirmation';
                    if ($isProduction===0){
                        $mailer->iniAsTest();
                    }
                    if ($controller === true){
                    	if($orderModel->school->type == 50){
                        	$mailer->getView('bankTransfer_ds', array('orderModel' => $orderModel,'xContentCn'=>$xContentCn,'xContentEn'=>$xContentEn,'branchList'=>$branchList,'classModel'=>$classModel,'totalAmount'=>number_format($totalAmount,2),'childModel'=>$childModel), 'todsparent');
                    	}else{
                        	$mailer->getView('bankTransfer', array('orderModel' => $orderModel,'xContentCn'=>$xContentCn,'xContentEn'=>$xContentEn,'branchList'=>$branchList,'classModel'=>$classModel,'totalAmount'=>number_format($totalAmount,2),'childModel'=>$childModel), 'toparent');
                    	}
                    }else{
                		if($orderModel->school->type == 50){
                        	$mailer->getCommandView('bankTransfer_ds', array('orderModel' => $orderModel,'xContentCn'=>$xContentCn,'xContentEn'=>$xContentEn,'branchList'=>$branchList,'classModel'=>$classModel,'totalAmount'=>number_format($totalAmount,2),'childModel'=>$childModel), 'todsparent');
                		}else{
                        	$mailer->getCommandView('bankTransfer', array('orderModel' => $orderModel,'xContentCn'=>$xContentCn,'xContentEn'=>$xContentEn,'branchList'=>$branchList,'classModel'=>$classModel,'totalAmount'=>number_format($totalAmount,2),'childModel'=>$childModel), 'toparent');
                		}
                    }
                    $mailer->iniMail( OA::isProduction() ); // 此行代码要放到AddAddress, AddCC方法下面
                    if (!$mailer->Send()){
                    	echo $mailer->getMessage();
                    	Yii::app()->end();
                    }
            	}
             }
        }
    }

    // 付款成功改变入学申请孩子状态并发送邮件
    public static function admissionsToEmail($admissionsId)
    {
    	Yii::import('common.models.visit.AdmissionsDs');

    	$child = AdmissionsDs::model()->findByPk($admissionsId);
    	if (!$child) {
    		return false;
    	}
    	$child->changeStatus(AdmissionsDs::STATS_HAS_BEEN_PAID);
    	// 关闭发送邮件
    	return true;
    	// 发送邮件
    	$to = array($child->father_email, $child->mother_email);
    	$sub = 'Daystar Academy 启明星学校 ｜Application Fee Confirmation 申请费确认函';
    	$tem = 'dsadmission_confirm';
    	$mailer = Yii::createComponent('common.extensions.mailer.EMailer');
    	$mailer->Subject = $sub;

    	$flag = false;
    	foreach ($to as $v) {
    	    if (strpos($v, '@')) {
    	        $flag = true;
    	        $mailer->AddAddress($v);
    	    }
    	}
    	if (!$flag) {
    	    return false;
    	}
		$mailer->AddCC('<EMAIL>');

    	$mailer->getView($tem, array(), 'todsparent');

    	$mailer->iniMail( OA::isProduction()); // 此行代码要放到AddAddress, AddCC方法下面
    	$mailer->Send();
    }

    /*
     * 取消孩子午餐
     * @param int $childid
     * @param string $targetDate  e.g. 2013-01-01
     */
    public static function cancelChildLunchByDate($childId,$targetDate,$userId=0){
        if ($childId && $targetDate){
            //根据目标日期查询帐单信息
            Yii::import('common.models.invoice.ChildServiceInfo');
            $model = ChildServiceInfo::model()->getCurrentDateFeeInfo($childId,'lunch',$targetDate);
            if (!empty($model)){
                return self::cancelLunch($childId, $model->classid, $model->schoolid, $model->calendar->startyear, $targetDate, 'DO',$userId);
            }else{
                return RC::getTip(RC::E_LUNCH_CANCEL_NOT_PAID);
            }
        }
    }

    /*
     * 恢复孩子午餐
     */
    public static function recoverChildLunchByDate($childId,$targetDate,$userId=0){
        if ($childId && $targetDate){
            //根据目标日期查询帐单信息
            Yii::import('common.models.invoice.ChildServiceInfo');
            $model = ChildServiceInfo::model()->getCurrentDateFeeInfo($childId,'lunch',$targetDate);
            if (!empty($model)){
                return self::cancelLunch($childId, $model->classid, $model->schoolid, $model->calendar->startyear, $targetDate, 'UNDO',$userId);
            }else{
                return RC::getTip(RC::E_LUNCH_CANCEL_NOT_PAID);
            }
        }
    }

    public function getInvoiceType()
    {
        global $paymentMappings;
        $langFile = 'payment_'.$this->constantFlag;
        $ret = array();
        foreach (array_flip($paymentMappings['dbFeeType']) as $dbk=>$key){
            $ret[$dbk] = Yii::t($langFile, $paymentMappings['titleFeeType'][$key]);
        }
        return $ret;
    }

    /*
     * 代金卷处理
     * @param object $invoiceModel 账单对象
     */
    public function processVoucher($invoiceModel){
        //获取学校某学年代金卷设置金额
        Yii::import('common.models.calendar.Calendar');
        $startYear = Calendar::model()->getCalendarInfo($invoiceModel->calendar_id,'startyear');
        $feeConfig = new IvyPolicy(OA::POLICY_PAY, $startYear, $invoiceModel->schoolid);
        $voucherAmount = isset($feeConfig->configs[VOUCHER_AMOUNT]) ? $feeConfig->configs[VOUCHER_AMOUNT]:0;
        if ($invoiceModel->status == Invoice::STATS_PARTIALLY_PAID){
            $dealWith = $invoiceModel->amount - $invoiceModel->paidSum; #应付款
        }else{
            $dealWith = $invoiceModel->amount;
        }
        $dealWith = ($dealWith<$invoiceModel->amount) ? $dealWith : $invoiceModel->amount;
        return $this->Pay(array($invoiceModel), InvoiceTransaction::TYPE_EDUFEE, ($dealWith<$voucherAmount) ? $dealWith : $voucherAmount);
    }

    /**
     * 部分付 退费功能
     */
    public function unPartPay($model=null)
    {
        if($model){
            Yii::import('common.models.invoice.*');
            if($model->status == Invoice::STATS_PARTIALLY_PAID){
                $transaction = Yii::app()->db->beginTransaction();
                try{
                    $payAmount = $model->paidSum;
                    if($payAmount > 0 ){
                        # 处理 ivy_invoice_transaction
                        $tModel = new InvoiceTransaction;
                        $tModel->invoice_id = $model->invoice_id;
                        foreach(array('calendar_id', 'childid', 'classid', 'schoolid', 'payment_type', 'afterschool_id', 'startdate', 'enddate', 'installment', 'fee_type') as $tag){
                            $tModel->$tag = $model->$tag;
                        }
                        $tModel->timestampe = time();
                        $tModel->amount = $payAmount;
                        $tModel->inout = 'out';
                        $tModel->title = '退 '.$model->title;
                        $tModel->operator_uid = Yii::app()->user->id;
                        $tModel->transactiontype = InvoiceTransaction::TYPE_TOCREDIT;

                        if(!$tModel->save()){
                            $transaction->rollback();
                            return RC::ERR_TC_PAYMENT_310001;
                        }

                        # 个人账户处理 ivy_child_credit
                        $cModel = new ChildCredit;
                        foreach(array('childid', 'classid', 'schoolid') as $tag){
                            $cModel->$tag = $model->$tag;
                        }
                        $cModel->yid = $model->calendar_id;
                        $cModel->amount = $payAmount;
                        $cModel->inout = 'in';
                        $cModel->itemname = $model->payment_type;
                        $cModel->invoice_id = $model->invoice_id;
                        $cModel->transaction_id = $tModel->id;
                        $cModel->userid = Yii::app()->user->id;
                        $cModel->updated_timestamp =  time();
                        $cModel->balance =  $cModel->getChildCredit($model->childid);
                        if(!$cModel->save()){
                            $transaction->rollback();
                            return RC::ERR_TC_PAYMENT_310005;
                        }

                        # 个人账户处理 ivy_child_profile_basic
                        $childModel = ChildProfileBasic::model()->findByPk($model->childid);
                        $childModel->credit += $payAmount;
                        if(!$childModel->save()){
                            $transaction->rollback();
                            return RC::ERR_TC_PAYMENT_310006;
                        }

                        # 处理 ivy_invoice_invoice
                        $model->status = Invoice::STATS_UNPAID;
                        if(!$model->save()){
                            $transaction->rollback();
                            return RC::ERR_TC_PAYMENT_310003;
						}
						// 处理预缴学费余额
						if ($model->payment_type == 'deposit') {

							$depAmount = self::getDepositAmount($tModel->childid, $tModel->calendar_id);
							if (!empty($depAmount)) {
								$depAmount = current($depAmount);
							}
							else {
								$depAmount = array('amount'=>0);
							}

							$depModel = new DepositHistory;
							$depModel->childid = $tModel->childid;
							$depModel->yid = $tModel->calendar_id;
							$depModel->amount = $tModel->amount;
							$depModel->balance = $depAmount['amount']-$tModel->amount;
							$depModel->inout = 'out';
							$depModel->tran_id = $tModel->id;
							$depModel->timestamp = time();
							$depModel->create_uid = Yii::app()->user->id;
							if (!$depModel->save()){
								$transaction->rollBack();
								return RC::ERR_TC_PAYMENT_310006;
							}

						}
                        $transaction->commit();
                        return RC::E_NO_ERROR;
                    }
                }
                catch(Exception $e){
                    $transaction->rollback();
                    return RC::ERR_TC_PAYMENT_310004;
                }
            }
        }
    }
    
    /**
     * 转校(新写方法)
     * @param object $transferModel 转校历史对象
     * @return boolean
     */
    public static function newTransferSchool($transferModel,$nextSchoolYear=false,$system=false){
        Yii::import('common.models.portfolio.ChildStudyHistory');
        Yii::import('common.models.invoice.ChildConstantTuition');
        Yii::import('common.models.invoice.ChildReserve');
        Yii::import('common.models.invoice.ChildCredit');
        $uid = ($system===true) ? 1 : Yii::app()->user->id;
        $classModel = ChildClassLink::model()->findByPk($transferModel->childid);
        ChildReserve::model()->deleteAllByAttributes(array('childid'=>$transferModel->childid));
        $basicModel = ChildProfileBasic::model()->findByPk($transferModel->childid);
        $modelValue = array(
            'childid'=>$transferModel->childid,
            'schoolid'=>$transferModel->to_schoolid,
            'classid'=>$transferModel->classid,
            'calendar'=>$transferModel->yid,
            'semester'=>0,
            'stat'=>ChildProfileBasic::STATS_ACTIVE,
            'timestamp'=>time(),
            'userid'=>$uid,
            'from_classid'=>0,
        );
        if ($nextSchoolYear === true){
            //历史表
            $historyModel = new ChildStudyHistory;
            $historyModel->setAttributes($modelValue);
            $historyModel->save();
            //未来学年表
            $childReserve = new ChildReserve();
            $childReserve->setAttributes($modelValue);
            $childReserve->hid = $historyModel->hid;
            $childReserve->updated_timestamp = time();
            $childReserve->save();
            //清空用友状态
            $basicModel->ufida_update = 0;
            $basicModel->updated_timestamp = time();
            $basicModel->create_uid = $uid;
            $basicModel->save();
        }else{
            //中间表数据
            //1、先退学
            if (!empty($classModel)){
                $historyModel = new ChildStudyHistory;
                $historyModel->setAttributes($modelValue);
                $historyModel->schoolid = $classModel->schoolid;
                $historyModel->classid = $classModel->classid;
                $historyModel->calendar = $classModel->calendar;
                $historyModel->stat = ChildProfileBasic::STATS_DROPOUT;
                $historyModel->timestamp = time();
                $historyModel->save();
            }
            //2、再入学
            $historyModel = new ChildStudyHistory;
            $historyModel->setAttributes($modelValue);
            $historyModel->save();
            //更新当前表
            if (empty($classModel)){
                $classModel = new ChildClassLink();
            }
            $classModel->setAttributes($modelValue);
            $classModel->hid = $historyModel->hid;
            $classModel->updated_timestamp = time();
            $classModel->save();
            //更新孩子基本表学校、班级、用友导入状态
            $basicModel->classid = $transferModel->classid;
            $basicModel->status = ChildProfileBasic::STATS_ACTIVE;
            $basicModel->ufida_update = 0;
            $basicModel->updated_timestamp = time();
            $basicModel->create_uid = $uid;
            $basicModel->schoolid = $transferModel->to_schoolid;
            // 更新入学学年字段
            $basicModel->first_startyear = null;
            $basicModel->save();
            //删除老生老办法
            ChildConstantTuition::model()->deleteByPk($transferModel->childid);
            //如果孩子个人帐户有钱，先处理个人帐户金额
            if ($basicModel->credit){
                $branchinfo = Branch::model()->findByPk($transferModel->from_schoolid);
                $credit = new ChildCredit();
                $credit->classid = $basicModel->classid;
                $credit->schoolid = $basicModel->schoolid;
                $credit->yid = $branchinfo->schcalendar;
                $credit->childid = $transferModel->childid;
                $credit->amount = $basicModel->credit;
                $credit->inout = "out";
                $credit->itemname = ChildCredit::TYPE_CASHOUT;
                $credit->invoice_id = 0;
                $credit->transaction_id = 0;
                $credit->userid = 1;
                $credit->updated_timestamp = time();
                $credit->transfer_to_uid = 1;
                $credit->transfer_timestamp = time();
                $credit->transactiontype = 0;
                $credit->balance = 0.00;
                $credit->cash_status = 0;
                if ($credit->save()){
                    $ncredit = new ChildCredit();
                    $ncredit->setAttributes($credit->getAttributes());
                    $ncredit->classid = $transferModel->classid;
                    $ncredit->schoolid = $transferModel->to_schoolid;
                    $ncredit->yid = $transferModel->yid;
                    $ncredit->inout = "in";
                    $ncredit->itemname = "transfer_cash";
                    $ncredit->balance = $basicModel->credit;
                    $ncredit->updated_timestamp = time()+1;
                    $ncredit->transfer_timestamp = time()+1;
                    $ncredit->save();
                }
            }
        }
        //结束
        $transferModel->balance = $basicModel->credit ? $basicModel->credit : 0;
        $transferModel->save();
        return true;
    }
    
    /**
	 * 孩子提现
	 * @param int $childId
	 * @param number $amount
     * @param object $transaction //开启事务对象
	 * @return boolean
	 */
	public static function newChildOutCash($childId=0,$amount=0,$transaction)
	{
		if ($childId && $amount>=0.01)
		{
            Yii::import('common.models.invoice.ChildCredit');
			$childObj = self::getChildObj($childId);
			//取孩子当前账户余额
			$historyAmount = PolicyApi::getCreditAmount($childId);
			$balanceAmount = $historyAmount-$amount;
			if ( abs($historyAmount - $childObj->credit)<0.01 && ( ($balanceAmount >=0 ) ||( abs($balanceAmount)<0.01) ) )
			{
				$branch = Branch::model()->getBranchInfo($childObj->schoolid, array('schcalendar'));
				$childCredit = new ChildCredit;
				$childCredit->classid = $childObj->classid;
				$childCredit->schoolid = $childObj->schoolid;
				$childCredit->yid = $branch['schcalendar'];
				$childCredit->childid = $childId;
				$childCredit->amount = $amount;
				$childCredit->inout = 'out';
				$childCredit->itemname = ChildCredit::TYPE_CASHOUT;
				$childCredit->userid = Yii::app()->user->id;
				$childCredit->updated_timestamp = time();
				$childCredit->balance = $balanceAmount;
				if (!$childCredit->save())
				{
                    $transaction->rollBack();
                    return false;
				}
                if (!ChildProfileBasic::model()->updateByPk($childId, array('credit'=>$balanceAmount))){
                    $transaction->rollBack();
                    return false;
                }
				return $childCredit->cid;
			}
		}
		return false;
	}
}