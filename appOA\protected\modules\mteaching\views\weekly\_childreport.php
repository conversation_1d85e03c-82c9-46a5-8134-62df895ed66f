<script>
    var childDataRaw = <?php echo CJSON::encode($this->getNameList($this->selectedClassId)); ?>;
    var childData = _.sortBy(childDataRaw, 'name');
    var onlines = <?php echo CJSON::encode($taskData['onlines'])?>;

    $(function(){
        var childItemTemplate = _.template( $('#child-li-item-template').html() );
        renderChildList = function(){
            $('#weekly-media-child-list').html('');
            _.each(childData, function(_data,_id){
                var _item = childItemTemplate(_data);
                var _child = _.first($(_item));
                if(_.indexOf(onlines, _data.id) != -1) $(_child).addClass('online');

                $('#weekly-media-child-list').append( $(_child) );
            });
        }

        renderChildList();
    });

    function childNote(id)
    {
        if(id){
            $('#info-guide-box').html('<?php echo Yii::t("global", "Loading Data...");?>').show();
            $('#childnote').hide();
            $('#NotesChild_childid').val(id);
            $('#photo-child').html('<img src="<?php echo Yii::app()->params['OAUploadBaseUrl']?>/childmgt/'+childDataRaw[id].photo+'" class="img-thumbnail">');
            $('#name-child').html( childDataRaw[id].name );
            $.getJSON('<?php echo $this->createUrl('childNote');?>', {id: id, classid: classid, weeknum: weeknum}, function(data){
                var ct = data.content ? data.content : '';
                ueNotesChild_en_content.setContent(ct);
                if(data.stat == 20){
                    $('#NotesChild_stat').attr('checked', true);
                }
                else{
                    $('#NotesChild_stat').attr('checked', false);
                }
                $('#info-guide-box').hide();
                $('#childnote').show();
            });
        }
    }

    function callback(data)
    {
        var index = _.indexOf(onlines, data.childid);
        if(data.stat == 20){
            if(index == -1){
                onlines.push(data.childid);
            }
        }
        else{
            if(index != -1){
                delete onlines[index];
            }
        }
        renderChildList();
    }
    
    function setAll(type)
    {
        $.post(
            '<?php echo $this->createUrl('setAllDs', array('classid'=>$this->selectedClassId, 'weeknum'=>$this->selectedWeeknum))?>',
            {type: type},
            function(data){
                if(data.state == 'success'){
                    resultTip({msg: data.message});
                    onlines = data.data;
                    renderChildList();
                }
                else{
                    resultTip({msg: data.message, error: 1});
                }
            },
            'json'
        );
    }
</script>

<div class="col-md-9" id="child-note">
    <div class="panel panel-default">
        <div class="panel-heading"><?php echo Yii::t('teaching','Child Report');?></div>
        <div class="panel-body">
			<div id="info-guide-box"><?php echo Yii::t('teaching','Click child list right side to start');?></div>
            <div id="childnote" style="display: none;">
                <?php echo CHtml::form('', 'post', array('class'=>'form-horizontal J_ajaxForm', 'role'=>'form'));?>
                <div class="form-group">
                    <label class="col-sm-2 control-label" id="name-child"></label>
                    <div class="col-sm-10">
                        <div id="photo-child" style="max-width: 200px;"></div>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-2 control-label"><?php echo Yii::t('labels','Content');?></label>
                    <div class="col-sm-10">
                        <?php
                        $this->widget('common.extensions.ueditor.ueditor_1_5_0',array(
                            'model' => $taskData['model'],
                            'attribute' => 'en_content',
                            'configFile' => 'ueditor.teacher.config',
                            'language' =>Yii::app()->language == 'en_us' ? 'en' : 'zh-cn',
                            'editorOptions' => array('initialFrameWidth'=>'95%'),
                            'toolbars'=>array('fullscreen', 'source', '|', 'undo', 'redo', '|','bold', 'italic', 'underline', 'strikethrough', 'superscript', 'subscript', '|','insertorderedlist', 'insertunorderedlist', '|','link', 'unlink', 'attachment'),
                            'classId' => $this->selectedClassId,
                        ));
                        ?>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-2 control-label"><?php echo Yii::t('teaching','Make Online');?></label>
                    <div class="col-sm-10">
                        <div class="checkbox">
                            <label>
                                <?php echo CHtml::activeCheckBox($taskData['model'], 'stat', array('value'=>20));?>
                                &nbsp;
                            </label>
                        </div>
                        <?php echo CHtml::activeHiddenField($taskData['model'], 'childid');?>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-2"></label>
                    <div class="col-sm-10">
                        <button class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global','Save');?></button>
                    </div>
                </div>
                <?php echo CHtml::endForm();?>
            </div>
        </div>
    </div>

</div>

<div class="col-md-3">
    <div class="panel panel-default">
        <div class="panel-heading">
            <button class="btn btn-primary"><?php echo Yii::t('global','Refresh');?></button>
            <?php if (CommonUtils::isGradeSchool($this->selectedClassId)):?>
            <div class="pull-right dropdown">
                <button class="btn btn-default" data-toggle="dropdown"><?php echo Yii::t('teaching',
                        'Batch');?> <span class="caret"></span></button>
                <ul class="dropdown-menu">
                    <li><a href="javascript:;" onclick="setAll('online');"><?php echo Yii::t('teaching','Make All Online');?></a></li>
                    <li><a href="javascript:;" onclick="setAll('offline');"><?php echo Yii::t('teaching','Make All Offline');?></a></li>
                </ul>
            </div>
            <?php endif;?>
        </div>
        <div class="panel-body">
            <ul class="media-list" id="weekly-media-child-list"></ul>
        </div>
    </div>
</div>

<style>
    .img-item{background: #f2f2f2;border: 1px solid #d5d5d5}
    .child-face{max-width: 40px;}
    #weekly-media-child-list .media{padding-left: 22px; position: relative}
    #weekly-media-child-list h5.flag{position: absolute;top: 2px; left: 0px; font-size:16px;color: #f2f2f2}
    #weekly-media-child-list .online h5.flag{color: #008000}
</style>


<script type="text/template" id="child-li-item-template">
    <li class="media class-child" childid=<%- id %>>
        <a class="pull-left" href="javascript:childNote(<%= id%>);">
            <img class="media-object child-face img-thumbnail" src="<?php echo Yii::app()->params['OAUploadBaseUrl']?>/childmgt/<%= photo %>" title="<%- name %>">
        </a>
        <div class="media-body">
            <h5 class="media-heading">
                <a href="<%= ccUrl%>" target="_blank" title="<?php echo Yii::t('teaching','Frontend Preview');?>">
                    <span class="glyphicon glyphicon-play"></span>
                </a>
                <a href="javascript:childNote(<%= id%>);"><%- name %></a>
            </h5>
        </div>
        <h5 class="flag"><span class="glyphicon glyphicon-ok-sign"></span></h5>
    </li>
</script>
