<?php
$nav = $this->nav['resource']['items'][$category]['label'];
$this->breadcrumbs = array(
    Yii::t("navigations", "Resources") => array('/child/resource/index'),
    $nav,
);
?>

<style>
    #catalog{
        float: right;
        border: 1px solid #999;
        background: #fff;
        margin-left: 20px;
        background: #fff;
        padding-bottom: 10px;
        width: 300px;
    }
    #catalog h3{
        padding: 4px 20px;
        background: #E8E7DA;
        color: #666;
        border-bottom: 1px solid #EFEEE5;
        font-size: 14px;
        margin-bottom: 0;
        line-height: 24px;
    }
    #catalog-list{
        margin: 4px 20px 20px;
    }
    #catalog-list li a{
        line-height: 22px;
        text-decoration: none;
        border-bottom: 1px dotted #999;
    }
    #catalog-list li.active a{
        color: #333;
    }
</style>

<div id="inner-main">

    <?php if ($contentAll):?>
    <div id="catalog">
        <h3><?php echo Yii::t("resource", 'Index'); ?></h3>
        <?php
        $this->widget('zii.widgets.CMenu', array(
            'items' => Content::model()->getTitleList($contentAll, Yii::app()->request->getParam('page', 0)),
            'id' => 'catalog-list',
        ));
        ?>
        <div style="text-align:center;">
            <?php
            $this->widget('CLinkPager',array(
                'header'=>'',
                'pages'=>$pager,
                'maxButtonCount'=>5,
                'cssFile' => Yii::app()->theme->baseUrl . '/css/pager.css',
                )
            );
            ?>
        </div>
    </div>
    <?php endif;?>

    <?php
    if ($id && isset($contentAll[$id])) {
        $item = $contentAll[$id];
    } else {
        $item = array_shift($contentAll);
    }

    echo CHtml::openTag('h2');
    echo $contentAll ? $item->getLangContent("title") : '';
    echo CHtml::closeTag('h2');
    if ($contentAll && $category == 'newsongs'):
        $links = unserialize($item->linkitem);
        foreach ($links as $lnk) {
            echo CHtml::openTag('p');
            echo MCHtml::mediaPlayer($lnk);
            echo CHtml::openTag('p');
        }
    endif;
    echo $contentAll ? $item->getLangContent("content") : '';
    ?>


</div>

<div class="clear"></div>
