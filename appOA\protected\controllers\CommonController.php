<?php

class CommonController extends ProtectedController
{
    private $privateOSS = null;

    public function actionGetHistoryYid(){
        $branchId = Yii::app()->request->getPost('branchid', 0);
        Yii::import('common.components.teaching.*');
        $data = ContentFetcher::getHistoricalStartYear($branchId);
        $this->addMessage('state','success');
        $this->addMessage('data', $data);
        $this->showMessage();
    }

    public function actionGetOSSSignedUrl($subpaths) {
        if ( $this->privateOSS == null ) {
            $this->privateOSS = CommonUtils::initOSS('private');
        }
        if(is_array($subpaths)) {
            foreach($subpaths as $subpath) {
                $result[$subpath] = $this->privateOSS->get_sign_url($subpath);
            }
        } else {
            $result = $this->privateOSS->get_sign_url($subpaths);
        }
        echo $result;
    }
	
	public function actionLearning()
	{
		$this->layout = '//layouts/blank';
		$this->setPageTitle('2016 Ivy Annual Learning Conference Feedback Survey');
		Yii::import('common.models.learning.*');
		
		if(Yii::app()->request->isPostRequest){
			$branches = $this->getAllBranch();
			$satisfaction = Yii::app()->request->getParam('satisfaction', '');
			$valuable = Yii::app()->request->getParam('valuable', '');
			$worthless = Yii::app()->request->getParam('worthless', '');
			$grouping_impression = Yii::app()->request->getParam('grouping_impression', '');
			$improve = Yii::app()->request->getParam('improve', '');
			$Grouping_quality = Yii::app()->request->getParam('Grouping_quality', '');
			$correlation = Yii::app()->request->getParam('correlation', '');
			$food = Yii::app()->request->getParam('food', '');
			$group_sessions = Yii::app()->request->getParam('group_sessions', '');
			$keynote = Yii::app()->request->getParam('keynote', '');
			$venue = Yii::app()->request->getParam('venue', '');
			$uid = Yii::app()->user->id;
			
			$criteria =new CDbCriteria; 
			$criteria->compare('uid',$uid);
			$users = LearningSurvey::model()->count($criteria);
			if($users){
				$this->addMessage('state', 'faila');
				$this->addMessage('message', "You have done the questionnaire!\n您已经填写过问卷!");
				$this->showMessage();
			}
			$model = new LearningSurvey();
			$model->satisfaction = $satisfaction;
			$model->valuable = $valuable;
			$model->worthless = $worthless;
			$model->grouping_impression = $grouping_impression;
			$model->improve = $improve;
			$model->Grouping_quality = $Grouping_quality;
			$model->correlation = $correlation;
			$model->food = $food;
			$model->group_sessions = $group_sessions;
			$model->keynote = $keynote;
			$model->venue = $venue;
			$model->schoolid = $branches[$this->staff->profile->branch]['id'];
			$model->uid = $uid;
			$model->times = time();
			$model->status = 'v1';
			if($model->save()){
                $this->addMessage('state', 'success');
                $this->addMessage('message', "Your questionnaire has been submitted successfully. Thank you for your honest feedback!\n提交成功,诚挚感谢您的反馈!");
            } else {
                $this->addMessage('state', 'fail');
                //$err = current($model->getErrors());
                //$this->addMessage('message', $err ? $err[0] : '失败');
                $this->addMessage('message', "Please submit the questionnaire after finishing all questions!\n请填写所有的问题在提交！");
            }
			$this->showMessage();
		}

		$cs = Yii::app()->clientScript;
		$cs->registerCoreScript('jquery');
		$cs->registerCssFile('http://libs.useso.com/js/bootstrap/3.2.0/css/bootstrap.min.css');
		$cs->registerScriptFile('http://libs.useso.com/js/bootstrap/3.2.0/js/bootstrap.min.js', CClientScript::POS_END);

		$this->render('learning');
	}
}