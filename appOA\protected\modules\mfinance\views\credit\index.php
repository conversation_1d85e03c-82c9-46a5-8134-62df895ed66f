<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo Yii::t('site','Finance Management');?></li>
        <li class="active">个人账户查询</li>
    </ol>
    <div>
    	<div class="form-inline">
            <label class="control-label" for="inputError2">&nbsp;&nbsp;截止日期：</label>
    	<input type="text" class="form-control form-group" id="datepicker" >
    	<a href="<?php echo $this->createUrl('credit/export',array('branchId'=>Yii::app()->request->getParam('branchId'),'time'=>Yii::app()->request->getParam('time'))); ?>" class="btn btn-success">导出</a><b>（例：查询08-31的数据则选择09-01）</b>
    	</div>
        <br />
    	<table class="table table-hover" >
    		<tr>
	    		<th width="50" class="text-center">序号</th>
                <th width="100" class="text-center">班级ID</th>
                <th width="200">班级</th>
                <th width="100" class="text-center">孩子ID</th>
                <th width="300">姓名</th>
                <th width="150">年龄</th>
                <th width="150">状态</th>
                <th width="100" class="text-right">余额</th>
    		</tr>
    		<?php $i=1;$sum=''; foreach ($credit as $k=>$v) { $sum+=$v['surplus']; ?>
    		<tr>
                <td class="text-center"><?php echo $i++; ?></td>
                <td class="text-center"><?php echo $v['classid']; ?></td>
                <td><?php echo $v['classname']; ?></td>
                <td class="text-center"><?php echo $v['childid']; ?></td>
                <td><?php echo '<a href='.$this->createUrl('//child/index/',array('branchId'=>$v['schoolid'],'childid'=>$v['childid']))." target='_blank' >". $v->child->getChildName().'</a>'; ?></td>
    			<td><?php echo CommonUtils::getAge($v->child['birthday'])?></td>
    			<td>
                    <?php echo $v->child->getStatus()?>
                </td>
                <td class="text-right"><?php echo $v['surplus']; ?></td>
    		</tr>
    		<?php  }?>
            <tr><td class="text-right" colspan="8">总余额: <?php echo number_format($sum, 2); ?></td></tr>
    	</table>
    </div>
</div>
<script>
$(function() {
  $("#datepicker").datepicker({
    dateFormat : "yy-mm-dd",
    minDate: new Date(2000,1,1),
    maxDate: new Date(<?php echo date('Y,m-1,d',time()) ?>),
    });
  $("#datepicker").val('<?php echo $_GET['time']?$_GET['time']:date('Y-m-d',time()); ?>');
});
$("#datepicker").change(function () {
	var time = $("#datepicker").val();
	window.location="?branchId=<?php echo $_GET['branchId'] ?>&time="+time;
});

</script>
<?php $this->renderPartial('//layouts/common/branchSelectBottom');?>