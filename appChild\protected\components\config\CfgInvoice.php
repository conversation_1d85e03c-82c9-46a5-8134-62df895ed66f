<?php
/*
 * <PERSON>.<PERSON>
* 2012-7-16
*
*/
return array(
		// 账单状态
		'invoice_state'=>array(
				'10' => array(
						"cn" => "等待付款",
						"en" => "Wait Pay"
				),
				'20' =>array(
						"cn" => "款付清",
						"en" => "Paid"
				),
				'30' => array(
						"cn" => "款部分付清",
						"en" => "Partially Pay"
				),
				'40' => array(
						"cn" => "等待折扣审核",
						"en" => "Wait Discount Review"
				),
				'77' => array(
						"cn" => "帐单更改待审核",
						"en" => "Wait Audit"
				),
				'88' => array(
						"cn" => "帐单更改历史",
						"en" => "Audit History"
				),
				'99' => array(
						"cn" => "付款通知单作废",
						"en" => "Invalid"
				),
		),
		// 支付方式
		'payment_type' => array(
				// 也是退费方式
				'10' => array(
						"cn" => "现金",
						"en" => "Cash"
				),
				'20' =>array(
						"cn" => "转帐",
						"en" => "Bank Transfer"
				),
				'30' => array(
						"cn" => "支票",
						"en" => "Check"
				),
				'40' => array(
						"cn" => "使用个人帐户",
						"en" => "Use Credit"
				),
				//退费方式
				'50' =>array(
						"cn" => "转到个人帐户",
						"en" => "To Credit"
				),
				'60' => array(
						"cn" => "银行卡-非艾毅POS机",
						"en" => "Bank Card"
				),
				'70' => array(
						"cn" => "POS",
						"en" => "POS"
				),
				'71' => array(
						"cn" => "POS",
						"en" => "POS"
				),
				'80' => array(
						"cn" => "网银支付",
						"en" => "Online Payment"
				),
				'81' => array(
						"cn" => "网银支付",
						"en" => "Online Payment"
				),
				'85' => array(
						"cn" => "微信支付",
						"en" => "Wechat Pay"
				),
				'86' => array(
						"cn" => "微信支付",
						"en" => "Wechat Pay"
				),				
				'90' => array(
						"cn" => "代金券",
						"en" => "Voucher Pay"
				),
		),
		// 费用类型
		'fee_type' => array(
				'registration' => array(
						"sign" => 'registration',
						"cn" => "入园材料费",
						"en" => "Registration",
						"unique" => false, // 每学年是否只有唯一值
				),
				'deposit' => array(
						"sign" => 'deposit',
						"cn" => "预缴学费",
						"en" => "Tuition Deposit",
						"unique" => false,
				),
				'tuition' => array(
						"sign" => 'tuition',
						"cn" => "学费",
						"en" => "Tuition",
				),
				'bus' => array(
						"sign" => 'bus',
						"cn" => "校车费",
						"en" => "School Bus",
						"unique" => false,
				),
				'lunch' => array(
						"sign" => 'lunch',
						"cn" => "餐费",
						"en" => "Lunch",
						"unique" => true,

				),
				'afterschool' => array(
						"sign" => 'afterschool',
						"cn" => "课外活动",
						"en" => "After School",
						"unique" => false,

				),
				'carousel' => array(
						"sign" => 'carousel',
						"cn" => "亲子班",
						"en" => "CaRousel",
						"unique" => false,

				),
				'library_card' => array(
						"sign" => 'library_card',
						"cn" => "借书卡",
						"en" => "Library Card",
						"unique" => false,

				),
				'library_card_cost' => array(
						"sign" => 'library_card_cost',
						"cn" => "借书卡工本费",
						"en" => "Library Card Cost",
						"unique" => false,

				),
				'latepayment' => array(
						"sign" => 'latepayment',
						"cn" => "滞纳金",
						"en" => "Late Payment",
						"unique" => false,

				),
				'account_balance_disposal' => array(
						"sign" => 'account_balance_disposal',
						"cn" => "个人账户余额处置",
						"en" => "Account Balance Disposal",
						"unique" => false,

				),
				'margin' => array(
						"sign" => 'margin',
						"cn" => "保证金",
						"en" => "Margin",
						"unique" => false,

				),
				'childcare' => array(
						"sign" => 'childcare',
						"cn" => "保育费",
						"en" => "保育费",
						"unique" => false,

				),
				'preschool_subsidy' => array(
						"sign" => 'preschool_subsidy',
						//学前教育资助金
						"cn" => "学前教育津贴",
						"en" => "Pre-school Education Funding",
						"unique" => false,
				
				),
				'sponsorship_fee' => array(
						"sign" => 'sponsorship_fee',
						"cn" => "赞助费",
						"en" => "Sponsorship Fee",
						"unique" => false,
				
				),
		)
);