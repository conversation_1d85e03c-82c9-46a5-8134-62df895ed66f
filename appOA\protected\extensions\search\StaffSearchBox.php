<?php
class StaffSearchBox extends CWidget
{
	//ac: autoComplete
	public $acInputCSS = 'length_5';
	public $acName = 'searchStaff';
	public $acPopPosition = array('my'=>"left top", 'at'=>"left bottom", 'collision'=>"none");
	public $withAlumni = false; //是否需要搜索离职的员工
	public $allowMultiple = false; //是否可以多选
	
	public $useModel = false; //如果使用model，请配置下面两个变量
	public $model;
	public $attribute;
	
	public $name; //如果不使用model，请忽略上面两个变量，配置这两个变量
	public $select;
	
	public $data;
	public $htmlOptions;
	
	public function run(){
		$this->render('staffSearchBox');
	}
}