<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site','Campus Operations'), array('//mcampus/default/index'))?></li>
        <li><?php echo Yii::t('site','Quotes')?></li>
    </ol>
    <div class="row">
        <div class="col-md-2">
            <?php
            $mainMenu = array(
                array('label'=>Yii::t('quote','Weekly Quote'), 'url'=>array("//mcampus/quote/index")),
                array('label'=>Yii::t('quote','Quotes Tank'), 'url'=>array("//mcampus/quote/lib")),
            );
            $this->widget('zii.widgets.CMenu',array(
                'id' => 'user-profile-item',
                'items'=> $mainMenu,
                'htmlOptions'=>array('class'=>'nav nav-pills nav-stacked text-right background-gray'),
                'activeCssClass'=>'active',
                'itemCssClass'=>''
            ));
            ?>
        </div>
        <div class="col-md-10">
            <div class="container-fluid">
                <div id="app" class="row" >
                    <div class="col-md-2" style="padding-left: 0px;">
                        <div class="btn-group mb10">
                            <button type="button" class="btn btn-primary float-end mt-2" data-bs-toggle="modal" @click="addMotto">
                                <?php echo Yii::t('quote', 'Add Quotes')?>
                            </button>
                        </div>
                    </div>
                    <div v-cloak class=""  v-loading="loading" v-if="list.length" >
                        <!-- 名人名言列表 -->
                        <template>
                            <el-table
                                    @sort-change="handleSort"
                                    :data="list"
                                    stripe
                                    style="width: 100%">
                                <el-table-column
                                        prop="content"
                                        label="<?php echo Yii::t('quote', 'Quote Content')?>"
                                        width="auto">
                                    <template v-slot="{ row }">
                                        <span class="keepAll">{{ row.content }}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column
                                        prop="author"
                                        label="<?php echo Yii::t('quote', 'Quote Author')?>"
                                        width="200">
                                    <template v-slot="{ row }">
                                        <span class="keepAll">{{ row.author }}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column
                                        label="<?php echo Yii::t('quote', 'Latest Used')?>"
                                        width="200">
                                    <template slot-scope="{ row }">
                                        {{ row.latest_use.length==0 ? '' : row.latest_use[0] }}
                                    </template>
                                </el-table-column>

                                <el-table-column
                                        label="<?php echo Yii::t('quote', 'Like')?>"
                                        width="150">
                                    <template slot-scope="scope">
                                        <el-button v-if="scope.row.like_count > 0" @click="showLikeList(scope.row)" type="text" size="small">{{scope.row.like_count}}</el-button>
                                        <span v-else>{{ scope.row.like_count}}</span>
                                    </template>

                                </el-table-column>
                                <el-table-column
                                        label=""
                                        width="100">
                                    <template slot-scope="scope">
                                        <el-button @click="editMotto(scope.row)" type="text" size="small">
                                            <?php echo Yii::t('quote', 'Edit')?>
                                        </el-button>
                                        <el-button @click="showDelQuotesTank(scope.row)" type="text" size="small">
                                            <?php echo Yii::t('quote', 'Delete')?>
                                        </el-button>
                                    </template>
                                </el-table-column>
                            </el-table>
                            <div class="page_block" style="display: flex;align-items: center;">
                                <span class="el-pagination__total2">
                                     <?php echo Yii::t('quote', 'Total :count results', array(':count' => '{{ total }}'))?>
                                </span>
                                <el-pagination
                                        background
                                        hide-on-single-page
                                        @current-change="handleCurrentChange"
                                        :current-page.sync="currentPage"
                                        layout="prev, pager, next"
                                        :total="total"
                                        :page-size="pageSize"
                                >
                                </el-pagination>
                            </div>
                        </template>
                    </div>
                    <div class="col-md-10" v-else>
                        <div><el-empty description="<?php echo Yii::t('ptc', 'No Data') ?>"></el-empty></div>
                    </div>
                    <!-- 添加名言弹窗 -->
                    <div class="modal fade" id="addMottoModal" tabindex="-1"  data-backdrop="static" data-keyboard="true" >
                        <div class="modal-dialog modal-md">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h4 class="modal-title" v-if="listData.id">
                                        <?php echo Yii::t('quote', 'Edit')?>
                                    </h4>
                                    <h4 class="modal-title" v-if="!listData.id">
                                        <?php echo Yii::t('quote', 'Add')?>
                                    </h4>
                                </div>
                                <div class="modal-body" style="margin-left: 24px;margin-right: 24px;">
                                    <!-- 添加名言表单内容 -->
                                    <el-row :gutter="14" style="margin-bottom: 24px; display: flex; align-items: center; ">
                                        <el-col :span="3" style="width: auto;padding-left:0">
                                            <label class="form-label" style="font-size:14px;color: #333333;font-weight: 500">
                                                <?php echo Yii::t('quote', 'Creator')?>
                                            </label>
                                        </el-col>
                                        <el-col :span="3" style="width: auto;">
                                            <div class="block">
                                                <el-avatar :size="32" :src="listData.user_photoUrl"></el-avatar>
                                            </div>
                                        </el-col>
                                        <el-col :span="15" style="padding-left: 0">
                                            <label class="form-label" style="color: #333333;font-weight: 400">{{listData.user_name}}</label>
                                        </el-col>
                                    </el-row>


                                    <el-row :gutter="14" style="margin-bottom:16px;">
                                        <div><label class="form-label" style="font-size:14px;color: #666666;font-weight: 400">
                                                <?php echo Yii::t('quote', 'Quote Content')?>
                                            </label></div>
                                        <el-col :span="20" style="padding-left:0;width:100%">
                                            <el-input
                                                    type="textarea"
                                                    :rows="3"
                                                    placeholder="<?php echo Yii::t('quote', 'Please input quote content')?>"
                                                    v-model="listData.content">
                                            </el-input>
                                        </el-col>
                                    </el-row>

                                    <el-row :gutter="20" style="">
                                        <div><label class="form-label" style="font-size:14px;color: #666666;font-weight: 400"><?php echo Yii::t('quote', 'Quote Author')?></label></div>
                                        <el-col :span="20" style="padding-left:0;width:100%">
                                            <el-input v-model="listData.author" placeholder="<?php echo Yii::t('quote', 'Please input quote author')?>"></el-input>
                                        </el-col>
                                    </el-row>
                                    <!--                        <el-row :gutter="20">-->
                                    <!--                            <el-col :span="3">-->
                                    <!--                                <label class="form-check-label" for="isActiveCheckbox">语言选择</label>-->
                                    <!--                            </el-col>-->
                                    <!--                            <el-col :span="20">-->
                                    <!--                                <el-radio-group v-model="listData.lang">-->
                                    <!--                                    <el-radio label="1">中文</el-radio>-->
                                    <!--                                    <el-radio label="2">英文</el-radio>-->
                                    <!--                                </el-radio-group>-->
                                    <!--                            </el-col>-->
                                    <!--                        </el-row>-->
                                    <!--                        <el-row :gutter="20">-->
                                    <!--                            <el-col :span="3">-->
                                    <!--                                <label class="form-check-label" for="isActiveCheckbox">是否可用</label>-->
                                    <!--                            </el-col>-->
                                    <!--                            <el-col :span="20">-->
                                    <!--                                <el-radio-group v-model="listData.status">-->
                                    <!--                                    <el-radio label="1">正常</el-radio>-->
                                    <!--                                    <el-radio label="2">禁用</el-radio>-->
                                    <!--                                </el-radio-group>-->
                                    <!--                            </el-col>-->
                                    <!--                        </el-row>-->
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close"); ?></button>
                                    <button type="button" class="btn btn-primary" @click="saveQuotesTank()"><?php echo Yii::t("global", "Save"); ?></button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- 确认删除模态对话框 -->
                    <div class="modal fade" id="confirmDeleteModal" tabindex="-1"  data-backdrop="static" data-keyboard="true" >
                        <div class="modal-dialog modal-sm">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="confirmDeleteModalLabel"> <?php echo Yii::t('quote', 'Delete')?></h5>
                                </div>
                                <div class="modal-body">
                                    <?php echo Yii::t('quote', 'Proceed to remove?')?>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close"); ?></button>
                                    <button type="button" class="btn btn-primary" id="confirmDeleteButton" @click="delQuotesTank(delData)">
                                        <?php echo Yii::t('quote', 'Confirm')?>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!--点赞列表弹窗-->
                    <?php echo $this->renderPartial('like');?>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    var status = "<?php echo !empty($_GET['status']) ? $_GET['status'] : 1;?>"
    var container = new Vue({
        el: '#app',
        data: {
            list: [],
            total: 0,
            pageSize: 20,
            currentPage:1,
            sort:'',
            listData: '',
            delData:'',
            loading:true,
            oa_user_info: '',
            likeObject: null
        },
        created() {
            this.getIndex()
        },
        mounted() {
        },
        methods: {
            handleCurrentChange(){
                this.getIndex()
            },
            getIndex() {
                let that=this
                that.loading=true
                $.ajax({
                    url: '<?php echo $this->createUrl("quotesTankList") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        page: this.currentPage,
                        pageSize: this.pageSize,
                        status: status,
                        sort: this.sort,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.list = data.data.list
                            that.total = data.data.total
                            that.oa_user_info = data.data.oa_user
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                        that.loading=false
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                        that.loading=false
                    },
                })
            },
            addMotto() {
                this.listData = {
                    id:0,
                    user_photoUrl:this.oa_user_info.photoUrl,
                    user_name:this.oa_user_info.name,
                    content: '',
                    author: '',
                    // show_time:[],
                    // lang:[],
                    status: '1',
                }
                $("#addMottoModal").modal('show')
            },
            editMotto(data) {
                this.listData = JSON.parse(JSON.stringify(data));
                $("#addMottoModal").modal('show')
            },
            saveQuotesTank() {
                let that=this
                that.listData.content = that.listData.content.trim();
                that.listData.author = that.listData.author.trim();
                if(!that.listData.content){
                    resultTip({
                        error: 'warning',
                        msg: '请填写内容'
                    });
                    return;
                }
                if(!that.listData.author){
                    resultTip({
                        error: 'warning',
                        msg: '请填写作者'
                    });
                    return;
                }
                // if(!that.listData.lang){
                //     resultTip({
                //         error: 'warning',
                //         msg: '请选择语言'
                //     });
                //     return;
                // }
                if(!that.listData.status){
                    resultTip({
                        error: 'warning',
                        msg: '请选择可用状态'
                    });
                    return;
                }
                // if(!that.listData.show_time ){
                //     resultTip({
                //         error: 'warning',
                //         msg: '请填写展示时间'
                //     });
                // }
                $.ajax({
                    url: '<?php echo $this->createUrl("saveQuotesTank") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        listData: this.listData,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            $("#addMottoModal").modal('hide')
                            that.getIndex()
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            showDelQuotesTank(item){
                this.delData = item
                $("#confirmDeleteModal").modal('show')
            },
            showLikeList(item){
                $.getJSON('<?php echo $this->createUrl('quote/likeUserList') ?>', {
                    id: item.id
                }, (data) => {
                    this.likeObject = data.data

                    this.$nextTick(() => {
                        $('#quote_like').modal()
                    })
                })
            },
            delQuotesTank() {
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("delQuotesTank") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        id: this.delData.id,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            $("#confirmDeleteModal").modal('hide')
                            that.getIndex()
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            handleSort({ column, prop, order }) {
                let fieldname = prop;
                let sortType = order;
                if (sortType === "ascending") {
                    this.sort = "asc";
                } else if (sortType === "descending") {
                    this.sort = "desc";
                }else{
                    this.sort = '';
                }
                this.getIndex()
            }
        }
    })
</script>

<style>
    [v-cloak] {
        display: none;
    }
    .el-row {
        margin-bottom: 20px;
    &:last-child {
         margin-bottom: 0;
     }
    }
    .el-col {
        border-radius: 4px;
    }
    .bg-purple-dark {
        background: #99a9bf;
    }
    .bg-purple {
        background: #d3dce6;
    }
    .bg-purple-light {
        background: #e5e9f2;
    }
    .grid-content {
        border-radius: 4px;
        min-height: 36px;
    }
    .row-bg {
        padding: 10px 0;
        background-color: #f9fafc;
    }
    .el-pagination.is-background .el-pager li:not(.disabled).active {
        background-color: #428bca;
        color: #FFF;
    }
    .el-button--text {
        color: #428bca;
    }
    .el-radio__input.is-checked .el-radio__inner{
        border-color: #428bca;
        background: #428bca;
    }
    .page_block{
        margin-bottom: 20px;
        margin-top: 10px;
    }
    .title-02 {
        font-weight: 500;
        font-size: 14px;
        color: #333333;
        font-style: normal;
    }
    .item-01-right {
        width: 100%;
    }
    .text-01 {
        font-weight: 400;
        font-size: 14px;
        color: #333333;
        line-height: 20px;
        text-align: left;
        font-style: normal;
    }
    .text-02 {
        font-weight: 400;
        font-size: 12px;
        color: #999999;
        line-height: 16px;
        text-align: left;
        font-style: normal;
    }
    .text-03 {
        font-weight: 400;
        font-size: 12px;
        color: #666666;
        line-height: 16px;
        font-style: normal;
    }
    .list-02 {
        display: flex;
        width: 200px;
        float: left;
        align-items: center;
        overflow: hidden;
    }
    .list-02 img{
        width: 32px;
        height: 32px;
        object-fit: cover;
    }
    .keepAll{
        word-wrap:break-word;
        word-break:keep-all;
    }
    .el-pagination__total2{
        display: inline-block;
        font-size: 13px;
        min-width: 35.5px;
        height: 28px;
        line-height: 28px;
        vertical-align: top;
        -webkit-box-sizing: border-box;
        box-sizing: border-box;
        margin-right: 10px;
        font-weight: 400;
        color: #606266;
    }
</style>

