<div class="container-fluid">
    <!-- 面包屑导航 -->
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site','Teaching Tasks'), array('//mteaching/default/index'))?></li>
        <li><?php echo CHtml::link(Yii::t('site','Curriculum'), array('//mteaching/curriculum/project', 'official'=>'1'))?></li>
        <li class="active"><?php echo Yii::t('curriculum','Tags');?></li>
    </ol>
    <div class="row">
        <!-- 左侧菜单栏 -->
        <div class="col-md-2">
            <div class="list-group" id="classroom-status-list">
                <?php foreach ($this->leftMenu as $key => $value) { ?>
                <a href="<?php echo ($value[0] == 'project') ? $this->createUrl($value[0], array('official' =>1 ))  : $this->createUrl($value[0]);?>" class="list-group-item status-filter <?php echo $this->getAction()->getId()==$value[0]?'active':''; ?>"><?php echo $value[1]; ?></a></li>
                <?php } ?>
            </div>
        </div>
        <div class="col-md-10">
            <div class="panel panel-primary">
                <div class="panel-heading">
                    <h4><?php echo Yii::t('curriculum','Tags');?>：</h4>
                </div>
                <div class="panel-body">
                    <?php 
                        $proTag = '';
                        $actTag = '';
                        foreach ($tags as $tag) {
                            $class = 'btn btn-success btn-xs';
                            //选中标签高亮
                            // if ($tag->tag_id == $tag_id) {
                            //     $class = 'btn btn-primary btn-xs';
                            // }
                            if ($tag->tag_catid == 1) {
                                $proTag .= "<div class='col-sm-2 form-group'><a href='{$this->createUrl('tags',array('tag_id'=>$tag->tag_id,'tag_catid'=>1))}' class='{$class}'>{$tag->tag->tag_term}({$tag->num})</a></div>";
                            }else{
                                $actTag .= "<div class='col-sm-2 form-group'><a href='{$this->createUrl('tags',array('tag_id'=>$tag->tag_id,'tag_catid'=>2))}' class='{$class}'>{$tag->tag->tag_term}({$tag->num})</a></div>";
                            }
                        }
                    ?>
                    <div class="col-sm-12 form-horizontal">
                        <h4><?php echo Yii::t('curriculum','Projects');?>：</h4>
                        <?php echo $proTag; ?>
                    </div>
                    <div class="col-sm-12 form-horizontal">
                        <h4><?php echo Yii::t('curriculum','Activity');?>：</h4>
                        <?php echo $actTag; ?>
                    </div>
                    <table class="table table-bordered table-hover">
                        <?php 
                            if ($tag_catid == 1) {
                                echo "<caption><h4>".Yii::t('curriculum','Projects List')."</h4></caption>";
                                foreach ($tagObj as $key => $value) {
                                    $str = "<tr><td><a target='_blank' href='{$this->createUrl('showproject',array('pid'=>$value->projects->pid))}'>{$value->projects->cn_title}  {$value->projects->en_title}</a></td></tr>";
                                    echo $str;
                                }
                            }else{
                                echo "<caption><h4>".Yii::t('curriculum','Activity List')."</h4></caption>";
                                foreach ($tagObj as $key => $value) {
                                    $str = "<tr><td><a target='_blank' href='{$this->createUrl('showactivity',array('aid'=>$value->activity->aid))}'>{$value->activity->cn_title}  {$value->activity->en_title}</a></td></tr>";
                                    echo $str;
                                }
                            }
                        ?>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
</script>
