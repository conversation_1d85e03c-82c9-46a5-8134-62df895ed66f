<?php

/**
 * This is the model class for table "ivy_ufida_income".
 *
 * The followings are the available columns in table 'ivy_ufida_income':
 * @property integer $id
 * @property integer $invoice_id
 * @property integer $transaction_id
 * @property string $payment_type
 * @property integer $transactiontype
 * @property string $borrow
 * @property string $loan
 * @property string $borrow_accounts_code
 * @property string $loan_accounts_code
 * @property double $amount
 * @property integer $childid
 * @property integer $classid
 * @property string $schoolid
 * @property string $child_name
 * @property string $class_name
 * @property string $afterschool_name
 * @property string $ufida_number
 * @property string $school_name
 * @property string $type
 * @property integer $fee_type
 * @property integer $payment_timestamp
 * @property string $memo
 * @property integer $status
 * @property integer $timestmp
 * @property string $transferid
 */
class UfidaIncome extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_ufida_income';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('transactiontype,borrow_accounts_code, loan_accounts_code, type,schoolid,timestmp,status,payment_timestamp', 'required'),
			array('invoice_id, transaction_id, transactiontype, childid, classid, fee_type, payment_timestamp, status, timestmp', 'numerical', 'integerOnly'=>true),
			array('amount', 'numerical'),
			array('payment_type, child_name, class_name, afterschool_name, ufida_number, school_name', 'length', 'max'=>255),
			array('borrow, loan', 'length', 'max'=>500),
			array('borrow_accounts_code, loan_accounts_code, schoolid, type', 'length', 'max'=>100),
			array('memo, transferid', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, invoice_id, transaction_id, payment_type, transactiontype, borrow, loan, borrow_accounts_code, loan_accounts_code, amount, childid, classid, schoolid, child_name, class_name, afterschool_name, ufida_number, school_name, type, fee_type, payment_timestamp, memo, status, timestmp,transferid', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'invoice_id' => 'Invoice',
			'transaction_id' => 'Transaction',
			'payment_type' => 'Payment Type',
			'transactiontype' => 'Transactiontype',
			'borrow' => 'Borrow',
			'loan' => 'Loan',
			'borrow_accounts_code' => 'Borrow Accounts Code',
			'loan_accounts_code' => 'Loan Accounts Code',
			'amount' => 'Amount',
			'childid' => 'Childid',
			'classid' => 'Classid',
			'schoolid' => 'Schoolid',
			'child_name' => 'Child Name',
			'class_name' => 'Class Name',
			'afterschool_name' => 'Afterschool Name',
			'ufida_number' => 'Ufida Number',
			'school_name' => 'School Name',
			'type' => 'Type',
			'fee_type' => 'Fee Type',
			'payment_timestamp' => 'Payment Timestamp',
			'memo' => 'Memo',
			'status' => 'Status',
			'timestmp' => 'Timestmp',
			'transferid' => 'Transferid',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('invoice_id',$this->invoice_id);
		$criteria->compare('transaction_id',$this->transaction_id);
		$criteria->compare('payment_type',$this->payment_type,true);
		$criteria->compare('transactiontype',$this->transactiontype);
		$criteria->compare('borrow',$this->borrow,true);
		$criteria->compare('loan',$this->loan,true);
		$criteria->compare('borrow_accounts_code',$this->borrow_accounts_code,true);
		$criteria->compare('loan_accounts_code',$this->loan_accounts_code,true);
		$criteria->compare('amount',$this->amount);
		$criteria->compare('childid',$this->childid);
		$criteria->compare('classid',$this->classid);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('child_name',$this->child_name,true);
		$criteria->compare('class_name',$this->class_name,true);
		$criteria->compare('afterschool_name',$this->afterschool_name,true);
		$criteria->compare('ufida_number',$this->ufida_number,true);
		$criteria->compare('school_name',$this->school_name,true);
		$criteria->compare('type',$this->type,true);
		$criteria->compare('fee_type',$this->fee_type);
		$criteria->compare('payment_timestamp',$this->payment_timestamp);
		$criteria->compare('memo',$this->memo,true);
		$criteria->compare('status',$this->status);
		$criteria->compare('timestmp',$this->timestmp);
		$criteria->compare('transferid',$this->transferid,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return UfidaIncome the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}
