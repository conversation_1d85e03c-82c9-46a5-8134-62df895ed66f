<div class="col-md-11 col-sm-10">
    <div id="school-bus-list">
        <?php
        foreach($schoolbusList as $_key => $schoolbus):
            $_title = $schoolbus['bus_title'] . ' - ' . $schoolbus['bus_code'];
        ?>
            <div class="panel panel-primary">
                <div class="panel-heading"><?php echo $_title  ; ?></div>
                <div class="panel-body schoolbus-child-list" busid="<?php echo $schoolbus['bid'];?>">
                    <table class="table">
                        <thead>
                        <tr>
                            <th width="140"><?php echo Yii::t('labels','Name');?></th>
                            <th width="360"><?php echo Yii::t('campus','Pick up time/place');?></th>
                            <th width="360"><?php echo Yii::t('campus','Drop off time/place');?></th>
                            <th><?php echo Yii::t('invoice','Memo');?></th>
                        </tr>
                        </thead>
                        <tbody>

                        </tbody>
                    </table>
                </div>
            </div>

        <?php
        endforeach;
        ?>
    </div>
</div>

<script type="text/template" id="child-item-template">
    <tr dbid=<%= id %> childid=<%= childid %>>
        <th><%= childName %></th>
        <td class="form-inline"><%= time1 %><%= addr1 %></td>
        <td class="form-inline"><%= time2 %><%= addr2 %></td>
        <td><%= remark %></td>
    </tr>
</script>

<script>
//校车信息
var schoolbusList = <?php echo CJSON::encode($schoolbusList);?>;
//已被分配校车的孩子
var childBySchoolbusList = <?php echo CJSON::encode($childBySchoolbusList)?>;
var childTpl = _.template($('#child-item-template').html());
//console.log(schoolbusList);
//console.log(childInfo);
console.log(childBySchoolbusList);
$(function(){
    if (!_.isEmpty(childBySchoolbusList)){
        $.each(childBySchoolbusList, function(i,items){
            var _container = $('div.schoolbus-child-list[busid|='+i+'] table tbody');
            $.each(items, function(m, item){
                _container.append(childTpl(item));
            })
        })
    }
})
</script>