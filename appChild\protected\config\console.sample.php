<?php

// This is the configuration for yiic console application.
// Any writable CConsoleApplication properties can be configured here.

$dbname = "mims";
$dbuser = "root";
$dbpass = "";

return array(
	'basePath'=>dirname(__FILE__).DIRECTORY_SEPARATOR.'..',
	'name'=>'My Console Application',
	// application components
	'import'=>array(
		'application.models.*',
		'application.components.*',
		'application.components.sql.*',
	),	
	'components'=>array(
		'db'=>array(
			'connectionString' => 'mysql:host=localhost;dbname='.$dbname,
			'emulatePrepare' => true,
			'username' => $dbuser,
			'password' => $dbpass,
			'charset' => 'utf8',
		),
        'authManager'=>array(
            'class'=>'CDbAuthManager',
            'connectionID' => 'db',
            'defaultRoles'=>array('visitor')
        ),
		'CURL' =>array(
				'class' => 'application.extensions.curl.Curl',
				//you can setup timeout,http_login,proxy,proxylogin,cookie, and setOPTIONS
		),
		'cache'=>array(
				'class'=>'CFileCache',
		),
	),
);