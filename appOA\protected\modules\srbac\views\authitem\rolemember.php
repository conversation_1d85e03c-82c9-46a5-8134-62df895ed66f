<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site','System Management'), array('/msettings'))?></li>
        <li class="active">系统权限</li>
    </ol>
    <div class="row panel-group">
        <div class="col-md-12">
            <?php
            $this->renderPartial('_menu');
            ?>
        </div>
        <?php if($this->module->getMessage() != ""){ ?>
            <div id="srbacError">
                <?php echo $this->module->getMessage();?>
            </div>
        <?php } ?>

    </div>

    <div class="row">
        <div class="col-md-2">
            <div class="">

                <?php
                $roles = Yii::app()->authManager->getAuthItems(2, $userid);
                //隐藏超级用户
                unset($roles[$this->getModule()->superUser]);

                $rolesArray = array();

                foreach($roles as $_k=>$_d){
                    $rolesArray[$_k] = $_k . ( empty($_d->description)? '':' '.$_d->description );
                }
                ?>

                <?php
                $userRows = Yii::app()->db->createCommand()->select('id,schoolid,tdata')->from('fts_staff')->where('active=1')->order('tdata asc')->queryAll();
                foreach($userRows as $_row){
                    $userList[$_row['id']] = array('branch'=>$_row['schoolid'],'tdata'=>$_row['tdata']);
                }
                $branchList = $this->getAllBranch();

                echo SHtml::beginForm();
                echo CHtml::dropDownList( 'role',
                    null,$rolesArray,
                    array('size'=>$this->module->listBoxNumberOfLines,'class'=>'form-control mb5','ajax' => array(
                        'type'=>'POST',
                        'url'=>array('showRolemembers'),
                        'update'=>'#members',
                        'dataType'=>'json',
                        'beforeSend' => 'function(){
                            $("#members").addClass("srbacLoading");
                        }',
                        'success' => 'function(data){
                            fetchUsers = data.userids;
                            $("#members").removeClass("srbacLoading");
                            showUsers();
                        }'
                    ),
                    ));
                echo SHtml::endForm();
                ?>
                <div id="branch-filter"></div>
            </div>
        </div>
        <div class="col-md-10">
            <div id="members">
                <?php foreach($branchList as $_branch):?>
                <div total=0 class="col-md-3 user-wrapper" id="wrapper-<?php echo $_branch['id'];?>">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <?php echo $_branch['title']; ?> <span class="label label-primary">4</span>
                        </div>
                        <div class="panel-body user-list">
                        </div>
                    </div>
                </div>
                <?php endforeach;?>
            </div>
        </div>
    </div>

</div>

<script>
    var staffList;
    var branchTotal =0;
    var branchList = <?php echo CJSON::encode($branchList);?>;
    var fetchUsers = [];
    var showUser;
    $(function() {
        $('.user-wrapper').hide();
        staffList = <?php echo CJSON::encode($userList);?>;
        showUsers=function(){
            $('.user-wrapper').attr('total',0).hide();
            $('div.user-list').html("");
            for(var i=0;i<fetchUsers.length;i++){
                if(staffList[fetchUsers[i]]!=undefined){
                    var wrapper = $('#wrapper-'+staffList[fetchUsers[i]]['branch']);
                    var ulist = wrapper.find('div.user-list');
                    var _total = parseInt(wrapper.attr("total")) + 1;
                    wrapper.show();
                    var tdata = staffList[fetchUsers[i]]['tdata'];
                    var _data = tdata.substr(tdata.indexOf('| ')+2, tdata.indexOf('}') - tdata.indexOf('| ') - 2);
                    $('<span></span>').attr('uid', fetchUsers[i]).addClass("badge mb5 mr5").html(_data).appendTo(ulist);
                    wrapper.append(' ');
                    wrapper.attr("total",_total);
                    wrapper.find("span.label-primary").html(_total);
                }
            }
        };
    });
</script>

<style>
    .badge{font-weight: normal}
    .badge:hover{background: #428bca;}
</style>